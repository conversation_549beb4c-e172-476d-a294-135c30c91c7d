/*     */ package weaver.hrm.schedule.dao;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.common.StringUtil;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.framework.BaseConnection;
/*     */ import weaver.framework.BaseDao;
/*     */ import weaver.hrm.schedule.domain.HrmSchduleResttime;
/*     */ import weaver.hrm.schedule.domain.HrmScheduleWorktime;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HrmScheduleWorktimeDao
/*     */   extends BaseConnection
/*     */   implements BaseDao<HrmScheduleWorktime>
/*     */ {
/*     */   public Comparable insert(HrmScheduleWorktime paramHrmScheduleWorktime) {
/*  25 */     if (paramHrmScheduleWorktime == null) return Integer.valueOf(-1);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  31 */     StringBuffer stringBuffer = (new StringBuffer()).append(" insert into hrm_schedule_worktime (delflag,creater,create_time,last_modifier,last_modification_time,field001,").append(" field002,field003,field004,field005,field006,field007 )").append(" values(" + paramHrmScheduleWorktime.getDelflag() + "," + paramHrmScheduleWorktime.getCreater() + ",'" + paramHrmScheduleWorktime.getCreateTime() + "'," + paramHrmScheduleWorktime.getLastModifier() + ",").append(" '" + paramHrmScheduleWorktime.getLastModificationTime() + "','" + paramHrmScheduleWorktime.getField001() + "','" + paramHrmScheduleWorktime.getField002() + "','" + paramHrmScheduleWorktime.getField003() + "',").append(" " + paramHrmScheduleWorktime.getField004() + "," + paramHrmScheduleWorktime.getField005() + ",'" + paramHrmScheduleWorktime.getField006() + "'," + paramHrmScheduleWorktime.getField007() + " )");
/*  32 */     boolean bool = this.rs.executeSql(stringBuffer.toString());
/*  33 */     if (bool) {
/*  34 */       this.rs.executeSql("select id from hrm_schedule_worktime where field001='" + paramHrmScheduleWorktime.getField001() + "' and field002='" + paramHrmScheduleWorktime.getField002() + "' and field003='" + paramHrmScheduleWorktime
/*  35 */           .getField003() + "' and field004='" + paramHrmScheduleWorktime.getField004() + "'");
/*  36 */       String str = this.rs.next() ? this.rs.getString("id") : "";
/*  37 */       if (StringUtil.isNotNull(str)) {
/*  38 */         List list = paramHrmScheduleWorktime.getResttimeList();
/*  39 */         for (HrmSchduleResttime hrmSchduleResttime : list) {
/*  40 */           stringBuffer = new StringBuffer();
/*  41 */           stringBuffer.append("insert into hrm_schedule_resttime(worktimeid, delflag, reststarttime, restendtime,reststarttime1, restendtime1,reststarttime2, restendtime2) values(" + str + "," + hrmSchduleResttime
/*  42 */               .getDelflag() + ", '" + StringUtil.vString(hrmSchduleResttime.getRestStartTime()) + "', '" + 
/*  43 */               StringUtil.vString(hrmSchduleResttime.getRestEndTime()) + "', '" + 
/*  44 */               StringUtil.vString(hrmSchduleResttime.getRestStartTime1()) + "', '" + 
/*  45 */               StringUtil.vString(hrmSchduleResttime.getRestEndTime1()) + "', '" + 
/*  46 */               StringUtil.vString(hrmSchduleResttime.getRestStartTime2()) + "', '" + 
/*  47 */               StringUtil.vString(hrmSchduleResttime.getRestEndTime2()) + "')");
/*     */           
/*  49 */           this.rs.executeSql(stringBuffer.toString());
/*     */         } 
/*     */       } 
/*     */     } 
/*  53 */     return Integer.valueOf(0);
/*     */   }
/*     */   
/*     */   public void update(HrmScheduleWorktime paramHrmScheduleWorktime) {
/*  57 */     if (paramHrmScheduleWorktime == null) {
/*     */       return;
/*     */     }
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  64 */     StringBuffer stringBuffer = (new StringBuffer()).append(" update hrm_schedule_worktime set").append(" delflag = " + paramHrmScheduleWorktime.getDelflag() + ",creater = " + paramHrmScheduleWorktime.getCreater() + ",create_time = '" + paramHrmScheduleWorktime.getCreateTime() + "',").append(" last_modifier = " + paramHrmScheduleWorktime.getLastModifier() + ",last_modification_time = '" + paramHrmScheduleWorktime.getLastModificationTime() + "',field001 = '" + paramHrmScheduleWorktime.getField001() + "',").append(" field002 = '" + paramHrmScheduleWorktime.getField002() + "',field003 = '" + paramHrmScheduleWorktime.getField003() + "',field004 = " + paramHrmScheduleWorktime.getField004() + ",").append(" field005 = " + paramHrmScheduleWorktime.getField005() + ",field006 = '" + paramHrmScheduleWorktime.getField006() + "',field007 = " + paramHrmScheduleWorktime.getField007() + "").append(" where id = " + paramHrmScheduleWorktime.getId() + "");
/*  65 */     this.rs.executeSql(stringBuffer.toString());
/*  66 */     List list = paramHrmScheduleWorktime.getResttimeList();
/*     */     
/*  68 */     for (HrmSchduleResttime hrmSchduleResttime : list) {
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/*  76 */       stringBuffer = (new StringBuffer()).append("update hrm_schedule_resttime set reststarttime='").append(StringUtil.vString(hrmSchduleResttime.getRestStartTime())).append("', restendtime='").append(StringUtil.vString(hrmSchduleResttime.getRestEndTime())).append("', reststarttime1='").append(StringUtil.vString(hrmSchduleResttime.getRestStartTime1())).append("', restendtime1='").append(StringUtil.vString(hrmSchduleResttime.getRestEndTime1())).append("', reststarttime2='").append(StringUtil.vString(hrmSchduleResttime.getRestStartTime2())).append("', restendtime2='").append(StringUtil.vString(hrmSchduleResttime.getRestEndTime2())).append("'").append(" where id=").append(hrmSchduleResttime.getRestTimeId());
/*  77 */       this.rs.executeSql(stringBuffer.toString());
/*     */     } 
/*     */   }
/*     */   
/*     */   public List<HrmScheduleWorktime> find(Map<String, Comparable> paramMap) {
/*  82 */     ArrayList<HrmScheduleWorktime> arrayList = new ArrayList();
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  87 */     StringBuffer stringBuffer = (new StringBuffer()).append(" select t.id,t.delflag,t.creater,t.create_time,t.last_modifier,t.last_modification_time,t.field001,").append(" t.field002,t.field003,t.field004,t.field005,t.field006,t.field007").append(" from hrm_schedule_worktime t").append(" where t.delflag = 0");
/*  88 */     if (paramMap != null) {
/*  89 */       if (paramMap.containsKey("id")) {
/*  90 */         stringBuffer.append(" and t.id = ").append(StringUtil.vString(paramMap.get("id")));
/*     */       }
/*  92 */       if (paramMap.containsKey("begin_id")) {
/*  93 */         stringBuffer.append(" and t.id >= ").append(StringUtil.vString(paramMap.get("begin_id")));
/*     */       }
/*  95 */       if (paramMap.containsKey("end_id")) {
/*  96 */         stringBuffer.append(" and t.id < ").append(StringUtil.vString(paramMap.get("end_id")));
/*     */       }
/*  98 */       if (paramMap.containsKey("sql_id")) {
/*  99 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_id")));
/*     */       }
/* 101 */       if (paramMap.containsKey("delflag")) {
/* 102 */         stringBuffer.append(" and t.delflag = ").append(StringUtil.vString(paramMap.get("delflag")));
/*     */       }
/* 104 */       if (paramMap.containsKey("begin_delflag")) {
/* 105 */         stringBuffer.append(" and t.delflag >= ").append(StringUtil.vString(paramMap.get("begin_delflag")));
/*     */       }
/* 107 */       if (paramMap.containsKey("end_delflag")) {
/* 108 */         stringBuffer.append(" and t.delflag < ").append(StringUtil.vString(paramMap.get("end_delflag")));
/*     */       }
/* 110 */       if (paramMap.containsKey("sql_delflag")) {
/* 111 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_delflag")));
/*     */       }
/* 113 */       if (paramMap.containsKey("creater")) {
/* 114 */         stringBuffer.append(" and t.creater = ").append(StringUtil.vString(paramMap.get("creater")));
/*     */       }
/* 116 */       if (paramMap.containsKey("begin_creater")) {
/* 117 */         stringBuffer.append(" and t.creater >= ").append(StringUtil.vString(paramMap.get("begin_creater")));
/*     */       }
/* 119 */       if (paramMap.containsKey("end_creater")) {
/* 120 */         stringBuffer.append(" and t.creater < ").append(StringUtil.vString(paramMap.get("end_creater")));
/*     */       }
/* 122 */       if (paramMap.containsKey("sql_creater")) {
/* 123 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_creater")));
/*     */       }
/* 125 */       if (paramMap.containsKey("lastModifier")) {
/* 126 */         stringBuffer.append(" and t.last_modifier = ").append(StringUtil.vString(paramMap.get("lastModifier")));
/*     */       }
/* 128 */       if (paramMap.containsKey("begin_lastModifier")) {
/* 129 */         stringBuffer.append(" and t.last_modifier >= ").append(StringUtil.vString(paramMap.get("begin_lastModifier")));
/*     */       }
/* 131 */       if (paramMap.containsKey("end_lastModifier")) {
/* 132 */         stringBuffer.append(" and t.last_modifier < ").append(StringUtil.vString(paramMap.get("end_lastModifier")));
/*     */       }
/* 134 */       if (paramMap.containsKey("sql_lastModifier")) {
/* 135 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_lastModifier")));
/*     */       }
/* 137 */       if (paramMap.containsKey("field004")) {
/* 138 */         stringBuffer.append(" and t.field004 = ").append(StringUtil.vString(paramMap.get("field004")));
/*     */       }
/* 140 */       if (paramMap.containsKey("begin_field004")) {
/* 141 */         stringBuffer.append(" and t.field004 >= ").append(StringUtil.vString(paramMap.get("begin_field004")));
/*     */       }
/* 143 */       if (paramMap.containsKey("end_field004")) {
/* 144 */         stringBuffer.append(" and t.field004 < ").append(StringUtil.vString(paramMap.get("end_field004")));
/*     */       }
/* 146 */       if (paramMap.containsKey("sql_field004")) {
/* 147 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_field004")));
/*     */       }
/* 149 */       if (paramMap.containsKey("field005")) {
/* 150 */         stringBuffer.append(" and t.field005 = ").append(StringUtil.vString(paramMap.get("field005")));
/*     */       }
/* 152 */       if (paramMap.containsKey("begin_field005")) {
/* 153 */         stringBuffer.append(" and t.field005 >= ").append(StringUtil.vString(paramMap.get("begin_field005")));
/*     */       }
/* 155 */       if (paramMap.containsKey("end_field005")) {
/* 156 */         stringBuffer.append(" and t.field005 < ").append(StringUtil.vString(paramMap.get("end_field005")));
/*     */       }
/* 158 */       if (paramMap.containsKey("sql_field005")) {
/* 159 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_field005")));
/*     */       }
/* 161 */       if (paramMap.containsKey("field007")) {
/* 162 */         stringBuffer.append(" and t.field007 = ").append(StringUtil.vString(paramMap.get("field007")));
/*     */       }
/* 164 */       if (paramMap.containsKey("begin_field007")) {
/* 165 */         stringBuffer.append(" and t.field007 >= ").append(StringUtil.vString(paramMap.get("begin_field007")));
/*     */       }
/* 167 */       if (paramMap.containsKey("end_field007")) {
/* 168 */         stringBuffer.append(" and t.field007 < ").append(StringUtil.vString(paramMap.get("end_field007")));
/*     */       }
/* 170 */       if (paramMap.containsKey("sql_field007")) {
/* 171 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_field007")));
/*     */       }
/* 173 */       if (paramMap.containsKey("createTime")) {
/* 174 */         stringBuffer.append(" and t.create_time = '").append(StringUtil.vString(paramMap.get("createTime"))).append("'");
/*     */       }
/* 176 */       if (paramMap.containsKey("like_createTime")) {
/* 177 */         stringBuffer.append(" and t.create_time like '%").append(StringUtil.vString(paramMap.get("like_createTime"))).append("%'");
/*     */       }
/* 179 */       if (paramMap.containsKey("sql_createTime")) {
/* 180 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_createTime")));
/*     */       }
/* 182 */       if (paramMap.containsKey("lastModificationTime")) {
/* 183 */         stringBuffer.append(" and t.last_modification_time = '").append(StringUtil.vString(paramMap.get("lastModificationTime"))).append("'");
/*     */       }
/* 185 */       if (paramMap.containsKey("like_lastModificationTime")) {
/* 186 */         stringBuffer.append(" and t.last_modification_time like '%").append(StringUtil.vString(paramMap.get("like_lastModificationTime"))).append("%'");
/*     */       }
/* 188 */       if (paramMap.containsKey("sql_lastModificationTime")) {
/* 189 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_lastModificationTime")));
/*     */       }
/* 191 */       if (paramMap.containsKey("field001")) {
/* 192 */         stringBuffer.append(" and t.field001 = '").append(StringUtil.vString(paramMap.get("field001"))).append("'");
/*     */       }
/* 194 */       if (paramMap.containsKey("like_field001")) {
/* 195 */         stringBuffer.append(" and t.field001 like '%").append(StringUtil.vString(paramMap.get("like_field001"))).append("%'");
/*     */       }
/* 197 */       if (paramMap.containsKey("sql_field001")) {
/* 198 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_field001")));
/*     */       }
/* 200 */       if (paramMap.containsKey("field002")) {
/* 201 */         stringBuffer.append(" and t.field002 = '").append(StringUtil.vString(paramMap.get("field002"))).append("'");
/*     */       }
/* 203 */       if (paramMap.containsKey("like_field002")) {
/* 204 */         stringBuffer.append(" and t.field002 like '%").append(StringUtil.vString(paramMap.get("like_field002"))).append("%'");
/*     */       }
/* 206 */       if (paramMap.containsKey("sql_field002")) {
/* 207 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_field002")));
/*     */       }
/* 209 */       if (paramMap.containsKey("field003")) {
/* 210 */         stringBuffer.append(" and t.field003 = '").append(StringUtil.vString(paramMap.get("field003"))).append("'");
/*     */       }
/* 212 */       if (paramMap.containsKey("like_field003")) {
/* 213 */         stringBuffer.append(" and t.field003 like '%").append(StringUtil.vString(paramMap.get("like_field003"))).append("%'");
/*     */       }
/* 215 */       if (paramMap.containsKey("sql_field003")) {
/* 216 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_field003")));
/*     */       }
/* 218 */       if (paramMap.containsKey("field006")) {
/* 219 */         stringBuffer.append(" and t.field006 = '").append(StringUtil.vString(paramMap.get("field006"))).append("'");
/*     */       }
/* 221 */       if (paramMap.containsKey("like_field006")) {
/* 222 */         stringBuffer.append(" and t.field006 like '%").append(StringUtil.vString(paramMap.get("like_field006"))).append("%'");
/*     */       }
/* 224 */       if (paramMap.containsKey("sql_field006")) {
/* 225 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_field006")));
/*     */       }
/* 227 */       if (paramMap.containsKey("mfsql")) {
/* 228 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("mfsql")));
/*     */       }
/* 230 */       if (paramMap.containsKey("like_qCondition")) {
/* 231 */         stringBuffer.append(" and t.field001 like '%" + StringUtil.vString(paramMap.get("like_qCondition")) + "%'");
/*     */       }
/* 233 */       if (paramMap.containsKey("sqlorderby")) {
/* 234 */         stringBuffer.append(" order by " + StringUtil.vString(paramMap.get("sqlorderby")));
/*     */       } else {
/* 236 */         stringBuffer.append(" order by t.id ").append((StringUtil.vString(paramMap.get("sqlsortway")).length() > 0) ? StringUtil.vString(paramMap.get("sqlsortway")) : "desc");
/*     */       } 
/*     */     } 
/* 239 */     this.rs.executeSql(stringBuffer.toString());
/* 240 */     HrmScheduleWorktime hrmScheduleWorktime = null;
/* 241 */     RecordSet recordSet = new RecordSet();
/* 242 */     while (this.rs.next()) {
/* 243 */       hrmScheduleWorktime = new HrmScheduleWorktime();
/* 244 */       hrmScheduleWorktime.setId(Long.valueOf(StringUtil.parseToLong(this.rs.getString("id"))));
/* 245 */       hrmScheduleWorktime.setDelflag(Integer.valueOf(StringUtil.parseToInt(this.rs.getString("delflag"))));
/* 246 */       hrmScheduleWorktime.setCreater(Long.valueOf(StringUtil.parseToLong(this.rs.getString("creater"))));
/* 247 */       hrmScheduleWorktime.setCreateTime(StringUtil.vString(this.rs.getString("create_time")));
/* 248 */       hrmScheduleWorktime.setLastModifier(Long.valueOf(StringUtil.parseToLong(this.rs.getString("last_modifier"))));
/* 249 */       hrmScheduleWorktime.setLastModificationTime(StringUtil.vString(this.rs.getString("last_modification_time")));
/* 250 */       hrmScheduleWorktime.setField001(StringUtil.vString(this.rs.getString("field001")));
/* 251 */       hrmScheduleWorktime.setField002(StringUtil.vString(this.rs.getString("field002")));
/* 252 */       hrmScheduleWorktime.setField003(StringUtil.vString(this.rs.getString("field003")));
/* 253 */       hrmScheduleWorktime.setField004(Integer.valueOf(StringUtil.parseToInt(this.rs.getString("field004"))));
/* 254 */       hrmScheduleWorktime.setField005(Integer.valueOf(StringUtil.parseToInt(this.rs.getString("field005"))));
/* 255 */       hrmScheduleWorktime.setField006(StringUtil.vString(this.rs.getString("field006")));
/* 256 */       hrmScheduleWorktime.setField007(Double.valueOf(StringUtil.round(StringUtil.parseToDouble(this.rs.getString("field007")), 1)));
/* 257 */       String str = "";
/*     */ 
/*     */ 
/*     */       
/* 261 */       str = " order by reststarttime asc";
/*     */       
/* 263 */       recordSet.executeSql("select id, worktimeid, delflag, reststarttime, restendtime,reststarttime1, restendtime1,reststarttime2, restendtime2 from hrm_schedule_resttime where delflag=0 and worktimeid=" + hrmScheduleWorktime.getId() + str);
/* 264 */       ArrayList<HrmSchduleResttime> arrayList1 = new ArrayList();
/* 265 */       HrmSchduleResttime hrmSchduleResttime = null;
/* 266 */       while (recordSet.next()) {
/* 267 */         hrmSchduleResttime = new HrmSchduleResttime();
/* 268 */         hrmSchduleResttime.setRestTimeId(StringUtil.vString(recordSet.getString("id")));
/* 269 */         hrmSchduleResttime.setWorktimeId(StringUtil.vString(recordSet.getString("worktimeid")));
/* 270 */         hrmSchduleResttime.setDelflag(StringUtil.vString(recordSet.getString("delflag")));
/* 271 */         hrmSchduleResttime.setRestStartTime(StringUtil.vString(recordSet.getString("reststarttime")));
/* 272 */         hrmSchduleResttime.setRestEndTime(StringUtil.vString(recordSet.getString("restendtime")));
/* 273 */         hrmSchduleResttime.setRestStartTime1(StringUtil.vString(recordSet.getString("reststarttime1")));
/* 274 */         hrmSchduleResttime.setRestEndTime1(StringUtil.vString(recordSet.getString("restendtime1")));
/* 275 */         hrmSchduleResttime.setRestStartTime2(StringUtil.vString(recordSet.getString("reststarttime2")));
/* 276 */         hrmSchduleResttime.setRestEndTime2(StringUtil.vString(recordSet.getString("restendtime2")));
/* 277 */         arrayList1.add(hrmSchduleResttime);
/*     */       } 
/* 279 */       hrmScheduleWorktime.setResttimeList(arrayList1);
/* 280 */       arrayList.add(hrmScheduleWorktime);
/*     */     } 
/* 282 */     return arrayList;
/*     */   }
/*     */   
/*     */   public HrmScheduleWorktime get(Comparable paramComparable) {
/* 286 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 287 */     hashMap.put("id", paramComparable);
/* 288 */     List<HrmScheduleWorktime> list = find((Map)hashMap);
/* 289 */     return (list != null && list.size() > 0) ? list.get(0) : null;
/*     */   }
/*     */   
/*     */   public int count(Map<String, Comparable> paramMap) {
/* 293 */     StringBuffer stringBuffer = new StringBuffer("select count(id) as result from hrm_schedule_worktime where 1 = 1");
/* 294 */     if (paramMap != null) {
/* 295 */       if (paramMap.containsKey("id")) {
/* 296 */         stringBuffer.append(" and id = ").append(StringUtil.vString(paramMap.get("id")));
/*     */       }
/* 298 */       if (paramMap.containsKey("sql_id")) {
/* 299 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_id")));
/*     */       }
/* 301 */       if (paramMap.containsKey("delflag")) {
/* 302 */         stringBuffer.append(" and delflag = ").append(StringUtil.vString(paramMap.get("delflag")));
/*     */       }
/* 304 */       if (paramMap.containsKey("sql_delflag")) {
/* 305 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_delflag")));
/*     */       }
/* 307 */       if (paramMap.containsKey("creater")) {
/* 308 */         stringBuffer.append(" and creater = ").append(StringUtil.vString(paramMap.get("creater")));
/*     */       }
/* 310 */       if (paramMap.containsKey("sql_creater")) {
/* 311 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_creater")));
/*     */       }
/* 313 */       if (paramMap.containsKey("lastModifier")) {
/* 314 */         stringBuffer.append(" and last_modifier = ").append(StringUtil.vString(paramMap.get("lastModifier")));
/*     */       }
/* 316 */       if (paramMap.containsKey("sql_lastModifier")) {
/* 317 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_lastModifier")));
/*     */       }
/* 319 */       if (paramMap.containsKey("field004")) {
/* 320 */         stringBuffer.append(" and field004 = ").append(StringUtil.vString(paramMap.get("field004")));
/*     */       }
/* 322 */       if (paramMap.containsKey("sql_field004")) {
/* 323 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_field004")));
/*     */       }
/* 325 */       if (paramMap.containsKey("field005")) {
/* 326 */         stringBuffer.append(" and field005 = ").append(StringUtil.vString(paramMap.get("field005")));
/*     */       }
/* 328 */       if (paramMap.containsKey("sql_field005")) {
/* 329 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_field005")));
/*     */       }
/* 331 */       if (paramMap.containsKey("field007")) {
/* 332 */         stringBuffer.append(" and field007 = ").append(StringUtil.vString(paramMap.get("field007")));
/*     */       }
/* 334 */       if (paramMap.containsKey("sql_field007")) {
/* 335 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_field007")));
/*     */       }
/* 337 */       if (paramMap.containsKey("createTime")) {
/* 338 */         stringBuffer.append(" and create_time = '").append(StringUtil.vString(paramMap.get("createTime"))).append("'");
/*     */       }
/* 340 */       if (paramMap.containsKey("sql_createTime")) {
/* 341 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_createTime")));
/*     */       }
/* 343 */       if (paramMap.containsKey("lastModificationTime")) {
/* 344 */         stringBuffer.append(" and last_modification_time = '").append(StringUtil.vString(paramMap.get("lastModificationTime"))).append("'");
/*     */       }
/* 346 */       if (paramMap.containsKey("sql_lastModificationTime")) {
/* 347 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_lastModificationTime")));
/*     */       }
/* 349 */       if (paramMap.containsKey("field001")) {
/* 350 */         stringBuffer.append(" and field001 = '").append(StringUtil.vString(paramMap.get("field001"))).append("'");
/*     */       }
/* 352 */       if (paramMap.containsKey("sql_field001")) {
/* 353 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_field001")));
/*     */       }
/* 355 */       if (paramMap.containsKey("field002")) {
/* 356 */         stringBuffer.append(" and field002 = '").append(StringUtil.vString(paramMap.get("field002"))).append("'");
/*     */       }
/* 358 */       if (paramMap.containsKey("sql_field002")) {
/* 359 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_field002")));
/*     */       }
/* 361 */       if (paramMap.containsKey("field003")) {
/* 362 */         stringBuffer.append(" and field003 = '").append(StringUtil.vString(paramMap.get("field003"))).append("'");
/*     */       }
/* 364 */       if (paramMap.containsKey("sql_field003")) {
/* 365 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_field003")));
/*     */       }
/* 367 */       if (paramMap.containsKey("field006")) {
/* 368 */         stringBuffer.append(" and field006 = '").append(StringUtil.vString(paramMap.get("field006"))).append("'");
/*     */       }
/* 370 */       if (paramMap.containsKey("sql_field006")) {
/* 371 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_field006")));
/*     */       }
/*     */     } 
/* 374 */     this.rs.executeSql(stringBuffer.toString());
/* 375 */     return this.rs.next() ? this.rs.getInt(1) : 0;
/*     */   }
/*     */   
/*     */   public void delete(Comparable paramComparable) {
/* 379 */     this.rs.executeSql("update hrm_schedule_worktime set delflag = 1 where id in ( " + paramComparable + " ) ");
/* 380 */     this.rs.executeSql("update hrm_schedule_resttime set delflag = 1 where worktimeid in(" + paramComparable + ")");
/*     */   }
/*     */   
/*     */   public void delete(Map<String, Comparable> paramMap) {
/* 384 */     if (paramMap == null || paramMap.isEmpty())
/*     */       return; 
/* 386 */     List<HrmScheduleWorktime> list = find(paramMap);
/*     */     
/* 388 */     StringBuffer stringBuffer = new StringBuffer("update hrm_schedule_worktime set delflag = 1  where 1 = 1");
/* 389 */     if (paramMap.containsKey("id")) {
/* 390 */       stringBuffer.append(" and id = ").append(StringUtil.vString(paramMap.get("id")));
/*     */     }
/* 392 */     if (paramMap.containsKey("sql_id")) {
/* 393 */       stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_id")));
/*     */     }
/* 395 */     if (paramMap.containsKey("delflag")) {
/* 396 */       stringBuffer.append(" and delflag = ").append(StringUtil.vString(paramMap.get("delflag")));
/*     */     }
/* 398 */     if (paramMap.containsKey("sql_delflag")) {
/* 399 */       stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_delflag")));
/*     */     }
/* 401 */     if (paramMap.containsKey("creater")) {
/* 402 */       stringBuffer.append(" and creater = ").append(StringUtil.vString(paramMap.get("creater")));
/*     */     }
/* 404 */     if (paramMap.containsKey("sql_creater")) {
/* 405 */       stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_creater")));
/*     */     }
/* 407 */     if (paramMap.containsKey("lastModifier")) {
/* 408 */       stringBuffer.append(" and last_modifier = ").append(StringUtil.vString(paramMap.get("lastModifier")));
/*     */     }
/* 410 */     if (paramMap.containsKey("sql_lastModifier")) {
/* 411 */       stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_lastModifier")));
/*     */     }
/* 413 */     if (paramMap.containsKey("field004")) {
/* 414 */       stringBuffer.append(" and field004 = ").append(StringUtil.vString(paramMap.get("field004")));
/*     */     }
/* 416 */     if (paramMap.containsKey("sql_field004")) {
/* 417 */       stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_field004")));
/*     */     }
/* 419 */     if (paramMap.containsKey("field005")) {
/* 420 */       stringBuffer.append(" and field005 = ").append(StringUtil.vString(paramMap.get("field005")));
/*     */     }
/* 422 */     if (paramMap.containsKey("sql_field005")) {
/* 423 */       stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_field005")));
/*     */     }
/* 425 */     if (paramMap.containsKey("field007")) {
/* 426 */       stringBuffer.append(" and field007 = ").append(StringUtil.vString(paramMap.get("field007")));
/*     */     }
/* 428 */     if (paramMap.containsKey("sql_field007")) {
/* 429 */       stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_field007")));
/*     */     }
/* 431 */     if (paramMap.containsKey("createTime")) {
/* 432 */       stringBuffer.append(" and create_time = '").append(StringUtil.vString(paramMap.get("createTime"))).append("'");
/*     */     }
/* 434 */     if (paramMap.containsKey("sql_createTime")) {
/* 435 */       stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_createTime")));
/*     */     }
/* 437 */     if (paramMap.containsKey("lastModificationTime")) {
/* 438 */       stringBuffer.append(" and last_modification_time = '").append(StringUtil.vString(paramMap.get("lastModificationTime"))).append("'");
/*     */     }
/* 440 */     if (paramMap.containsKey("sql_lastModificationTime")) {
/* 441 */       stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_lastModificationTime")));
/*     */     }
/* 443 */     if (paramMap.containsKey("field001")) {
/* 444 */       stringBuffer.append(" and field001 = '").append(StringUtil.vString(paramMap.get("field001"))).append("'");
/*     */     }
/* 446 */     if (paramMap.containsKey("sql_field001")) {
/* 447 */       stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_field001")));
/*     */     }
/* 449 */     if (paramMap.containsKey("field002")) {
/* 450 */       stringBuffer.append(" and field002 = '").append(StringUtil.vString(paramMap.get("field002"))).append("'");
/*     */     }
/* 452 */     if (paramMap.containsKey("sql_field002")) {
/* 453 */       stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_field002")));
/*     */     }
/* 455 */     if (paramMap.containsKey("field003")) {
/* 456 */       stringBuffer.append(" and field003 = '").append(StringUtil.vString(paramMap.get("field003"))).append("'");
/*     */     }
/* 458 */     if (paramMap.containsKey("sql_field003")) {
/* 459 */       stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_field003")));
/*     */     }
/* 461 */     if (paramMap.containsKey("field006")) {
/* 462 */       stringBuffer.append(" and field006 = '").append(StringUtil.vString(paramMap.get("field006"))).append("'");
/*     */     }
/* 464 */     if (paramMap.containsKey("sql_field006")) {
/* 465 */       stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_field006")));
/*     */     }
/* 467 */     this.rs.executeSql(stringBuffer.toString());
/*     */     
/* 469 */     for (byte b = 0; b < list.size(); b++) {
/* 470 */       HrmScheduleWorktime hrmScheduleWorktime = list.get(b);
/* 471 */       stringBuffer = new StringBuffer("update hrm_schedule_resttime set delflag = 1  where worktimeid=" + hrmScheduleWorktime.getId());
/* 472 */       this.rs.executeSql(stringBuffer.toString());
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/schedule/dao/HrmScheduleWorktimeDao.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */