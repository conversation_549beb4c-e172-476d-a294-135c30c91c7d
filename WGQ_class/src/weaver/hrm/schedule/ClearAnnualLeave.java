/*    */ package weaver.hrm.schedule;
/*    */ 
/*    */ import java.util.ArrayList;
/*    */ import java.util.Calendar;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.Util;
/*    */ import weaver.system.ThreadWork;
/*    */ 
/*    */ public class ClearAnnualLeave
/*    */   implements ThreadWork {
/* 11 */   private String sql = "";
/*    */ 
/*    */   
/*    */   public void doThreadWork() {
/* 15 */     checkAnnualMaturity();
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public void checkAnnualMaturity() {
/* 23 */     Calendar calendar = Calendar.getInstance();
/* 24 */     String str1 = Util.add0(calendar.get(1), 4) + "-" + Util.add0(calendar.get(2) + 1, 2) + "-" + Util.add0(calendar.get(5), 2);
/*    */ 
/*    */     
/* 27 */     ArrayList<String> arrayList1 = new ArrayList();
/* 28 */     ArrayList<String> arrayList2 = new ArrayList();
/* 29 */     RecordSet recordSet = new RecordSet();
/*    */     
/* 31 */     this.sql = "select id from hrmsubcompany order by id asc";
/* 32 */     recordSet.executeSql(this.sql);
/* 33 */     while (recordSet.next()) {
/* 34 */       arrayList1.add(recordSet.getString("id"));
/*    */     }
/*    */     
/* 37 */     this.sql = "select distinct annualyear from hrmannualmanagement where annualdays > 0";
/* 38 */     recordSet.executeSql(this.sql);
/* 39 */     while (recordSet.next()) {
/* 40 */       arrayList2.add(recordSet.getString("annualyear"));
/*    */     }
/*    */     
/* 43 */     String str2 = "";
/* 44 */     HrmAnnualManagement hrmAnnualManagement = new HrmAnnualManagement();
/*    */     try {
/* 46 */       if (arrayList1 != null && arrayList2 != null) {
/* 47 */         for (byte b = 0; b < arrayList1.size(); b++) {
/* 48 */           for (byte b1 = 0; b1 < arrayList2.size(); b1++) {
/* 49 */             String str3 = arrayList2.get(b1);
/* 50 */             String str4 = arrayList1.get(b);
/* 51 */             str2 = HrmAnnualManagement.getAnnualPeriod(str4, str3);
/*    */             
/* 53 */             String str5 = Util.TokenizerString2(str2, "#")[1];
/*    */             
/* 55 */             if (str5.compareTo(str1) < 0) {
/* 56 */               clearAnnualDays(str4, str3);
/*    */             }
/*    */           } 
/*    */         } 
/*    */       }
/* 61 */     } catch (Exception exception) {}
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public void clearAnnualDays(String paramString1, String paramString2) {
/* 71 */     RecordSet recordSet = new RecordSet();
/* 72 */     this.sql = "update hrmannualmanagement set status = 0 where resourceid in (select id from hrmresource where subcompanyid1 = " + paramString1 + ") and annualyear = " + paramString2;
/*    */     
/* 74 */     recordSet.executeSql(this.sql);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/schedule/ClearAnnualLeave.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */