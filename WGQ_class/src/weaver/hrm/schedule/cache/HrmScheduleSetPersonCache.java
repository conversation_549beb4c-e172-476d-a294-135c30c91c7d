/*    */ package weaver.hrm.schedule.cache;
/*    */ 
/*    */ import java.util.List;
/*    */ import weaver.framework.BaseCache;
/*    */ import weaver.hrm.schedule.domain.HrmScheduleSetPerson;
/*    */ import weaver.hrm.schedule.manager.HrmScheduleSetPersonManager;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class HrmScheduleSetPersonCache
/*    */   extends BaseCache<HrmScheduleSetPerson>
/*    */ {
/*    */   public HrmScheduleSetPersonCache() {
/* 19 */     super("HrmScheduleSetPersonCache");
/*    */   }
/*    */   
/*    */   protected List<HrmScheduleSetPerson> findResults() {
/* 23 */     return (new HrmScheduleSetPersonManager()).find();
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/schedule/cache/HrmScheduleSetPersonCache.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */