/*    */ package weaver.hrm.schedule.cache;
/*    */ 
/*    */ import java.util.List;
/*    */ import weaver.framework.BaseCache;
/*    */ import weaver.hrm.schedule.domain.HrmSchedulePerson;
/*    */ import weaver.hrm.schedule.manager.HrmSchedulePersonnelManager;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class HrmSchedulePersonCache
/*    */   extends BaseCache<HrmSchedulePerson>
/*    */ {
/*    */   public HrmSchedulePersonCache() {
/* 19 */     super("HrmSchedulePerson");
/*    */   }
/*    */   
/*    */   protected List<HrmSchedulePerson> findResults() {
/* 23 */     return (new HrmSchedulePersonnelManager()).getSchedulePersons(null);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/schedule/cache/HrmSchedulePersonCache.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */