/*    */ package weaver.hrm.schedule.cache;
/*    */ 
/*    */ import java.util.List;
/*    */ import weaver.framework.BaseCache;
/*    */ import weaver.hrm.schedule.domain.HrmScheduleShiftsWt;
/*    */ import weaver.hrm.schedule.manager.HrmScheduleShiftsWtManager;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class HrmScheduleShiftsWtCache
/*    */   extends BaseCache<HrmScheduleShiftsWt>
/*    */ {
/*    */   public HrmScheduleShiftsWtCache() {
/* 19 */     super("HrmScheduleShiftsWtCache");
/*    */   }
/*    */   
/*    */   protected List<HrmScheduleShiftsWt> findResults() {
/* 23 */     return (new HrmScheduleShiftsWtManager()).find();
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/schedule/cache/HrmScheduleShiftsWtCache.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */