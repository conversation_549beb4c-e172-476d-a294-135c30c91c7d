/*    */ package weaver.hrm.schedule.cache;
/*    */ 
/*    */ import java.util.List;
/*    */ import weaver.framework.BaseCache;
/*    */ import weaver.hrm.schedule.domain.HrmScheduleWorktime;
/*    */ import weaver.hrm.schedule.manager.HrmScheduleWorktimeManager;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class HrmScheduleWorktimeCache
/*    */   extends BaseCache<HrmScheduleWorktime>
/*    */ {
/*    */   public HrmScheduleWorktimeCache() {
/* 19 */     super("HrmScheduleWorktimeCache");
/*    */   }
/*    */   
/*    */   protected List<HrmScheduleWorktime> findResults() {
/* 23 */     return (new HrmScheduleWorktimeManager()).find();
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/schedule/cache/HrmScheduleWorktimeCache.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */