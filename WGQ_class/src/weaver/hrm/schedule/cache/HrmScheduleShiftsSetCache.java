/*    */ package weaver.hrm.schedule.cache;
/*    */ 
/*    */ import java.util.List;
/*    */ import weaver.framework.BaseCache;
/*    */ import weaver.hrm.schedule.domain.HrmScheduleShiftsSet;
/*    */ import weaver.hrm.schedule.manager.HrmScheduleShiftsSetManager;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class HrmScheduleShiftsSetCache
/*    */   extends BaseCache<HrmScheduleShiftsSet>
/*    */ {
/*    */   public HrmScheduleShiftsSetCache() {
/* 19 */     super("HrmScheduleShiftsSetCache");
/*    */   }
/*    */   
/*    */   protected List<HrmScheduleShiftsSet> findResults() {
/* 23 */     return (new HrmScheduleShiftsSetManager()).find();
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/schedule/cache/HrmScheduleShiftsSetCache.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */