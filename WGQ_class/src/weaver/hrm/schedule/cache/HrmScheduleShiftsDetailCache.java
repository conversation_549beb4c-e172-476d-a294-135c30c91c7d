/*    */ package weaver.hrm.schedule.cache;
/*    */ 
/*    */ import java.util.List;
/*    */ import weaver.framework.BaseCache;
/*    */ import weaver.hrm.schedule.domain.HrmScheduleShiftsDetail;
/*    */ import weaver.hrm.schedule.manager.HrmScheduleShiftsDetailManager;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class HrmScheduleShiftsDetailCache
/*    */   extends BaseCache<HrmScheduleShiftsDetail>
/*    */ {
/*    */   public HrmScheduleShiftsDetailCache() {
/* 19 */     super("HrmScheduleShiftsDetailCache");
/*    */   }
/*    */   
/*    */   protected List<HrmScheduleShiftsDetail> findResults() {
/* 23 */     return (new HrmScheduleShiftsDetailManager()).find();
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/schedule/cache/HrmScheduleShiftsDetailCache.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */