/*    */ package weaver.hrm.schedule.cache;
/*    */ 
/*    */ import java.util.List;
/*    */ import weaver.framework.BaseCache;
/*    */ import weaver.hrm.schedule.domain.HrmScheduleSet;
/*    */ import weaver.hrm.schedule.manager.HrmScheduleSetManager;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class HrmScheduleSetCache
/*    */   extends BaseCache<HrmScheduleSet>
/*    */ {
/*    */   public HrmScheduleSetCache() {
/* 19 */     super("HrmScheduleSetCache");
/*    */   }
/*    */   
/*    */   protected List<HrmScheduleSet> findResults() {
/* 23 */     return (new HrmScheduleSetManager()).find();
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/schedule/cache/HrmScheduleSetCache.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */