/*    */ package weaver.hrm.schedule.cache;
/*    */ 
/*    */ import java.util.List;
/*    */ import weaver.framework.BaseCache;
/*    */ import weaver.hrm.schedule.domain.HrmScheduleSetDetail;
/*    */ import weaver.hrm.schedule.manager.HrmScheduleSetDetailManager;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class HrmScheduleSetDetailCache
/*    */   extends BaseCache<HrmScheduleSetDetail>
/*    */ {
/*    */   public HrmScheduleSetDetailCache() {
/* 19 */     super("HrmScheduleSetDetailCache");
/*    */   }
/*    */   
/*    */   protected List<HrmScheduleSetDetail> findResults() {
/* 23 */     return (new HrmScheduleSetDetailManager()).find();
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/schedule/cache/HrmScheduleSetDetailCache.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */