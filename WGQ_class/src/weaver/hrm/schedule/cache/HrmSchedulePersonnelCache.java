/*    */ package weaver.hrm.schedule.cache;
/*    */ 
/*    */ import java.util.List;
/*    */ import weaver.framework.BaseCache;
/*    */ import weaver.hrm.schedule.domain.HrmSchedulePersonnel;
/*    */ import weaver.hrm.schedule.manager.HrmSchedulePersonnelManager;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class HrmSchedulePersonnelCache
/*    */   extends BaseCache<HrmSchedulePersonnel>
/*    */ {
/*    */   public HrmSchedulePersonnelCache() {
/* 19 */     super("HrmSchedulePersonnelCache");
/*    */   }
/*    */   
/*    */   protected List<HrmSchedulePersonnel> findResults() {
/* 23 */     return (new HrmSchedulePersonnelManager()).find();
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/schedule/cache/HrmSchedulePersonnelCache.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */