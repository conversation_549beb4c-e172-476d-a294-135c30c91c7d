/*     */ package weaver.hrm.schedule;
/*     */ 
/*     */ import java.util.Calendar;
/*     */ import weaver.common.DateUtil;
/*     */ import weaver.common.StringUtil;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.ThreadVarLanguage;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ public class HrmScheduleSign extends BaseBean {
/*     */   private int id;
/*  13 */   private int usertype = 1; private int userid;
/*     */   private int signtype;
/*     */   private String signdate;
/*     */   private String signtime;
/*     */   private String clientaddress;
/*     */   private String signfrom;
/*     */   private String longitude;
/*     */   private String latitude;
/*     */   private String addr;
/*  22 */   private int isincom = 1;
/*  23 */   private int isimport = 1;
/*     */   private String uuid;
/*     */   private String suuid;
/*     */   private String importsql;
/*     */   
/*     */   public void save() throws Exception {
/*  29 */     String str = "";
/*  30 */     RecordSet recordSet = new RecordSet();
/*     */     
/*  32 */     boolean bool = true;
/*     */     
/*  34 */     str = " select count(*) from HrmScheduleSign where userid =" + this.userid + " and signdate='" + this.signdate + "' and signtime='" + this.signtime + "' and isincom='1'";
/*  35 */     recordSet.execute(str);
/*  36 */     if (recordSet.next() && 
/*  37 */       recordSet.getInt(1) > 0) bool = false;
/*     */     
/*  39 */     if (this.clientaddress == null) {
/*  40 */       this.clientaddress = "";
/*     */     }
/*  42 */     if (bool) {
/*     */       
/*  44 */       str = " insert into HrmScheduleSign (userid, usertype, signtype, signdate, signtime, clientaddress, isincom, isimport, suuid, signfrom, longitude, latitude, addr)  values(" + this.userid + ",'" + this.usertype + "','" + this.signtype + "','" + this.signdate + "','" + this.signtime + "','" + this.clientaddress + "','" + this.isincom + "', " + this.isimport + ",'" + StringUtil.vString(this.suuid) + "','" + StringUtil.vString(this.signfrom) + "','" + StringUtil.vString(this.longitude) + "','" + StringUtil.vString(this.latitude) + "','" + StringUtil.vString(this.addr) + "')";
/*  45 */       recordSet.execute(str);
/*     */     } 
/*     */   }
/*     */   
/*     */   public void saveImp() throws Exception {
/*  50 */     String str = "";
/*  51 */     RecordSet recordSet = new RecordSet();
/*     */ 
/*     */     
/*  54 */     str = " insert into HRMSCHEDULESIGNIMP (userid, usertype, signtype, signdate, signtime, clientaddress, isincom,impdatetime,suuid,uuid,delflag)  values(" + this.userid + ",'" + this.usertype + "','0','" + this.signdate + "','" + this.signtime + "','" + this.clientaddress + "','" + this.isincom + "','" + DateUtil.getCalendarDateTime(Calendar.getInstance()) + "','" + this.suuid + "','" + this.uuid + "',0)";
/*  55 */     recordSet.execute(str);
/*     */   }
/*     */   
/*     */   public static void updateSignType(String paramString1, int paramInt, String paramString2) {
/*     */     try {
/*  60 */       String str1 = "";
/*     */       
/*  62 */       if (1 == paramInt) {
/*  63 */         str1 = "" + SystemEnv.getHtmlLabelName(10003675, ThreadVarLanguage.getLang()) + "";
/*  64 */       } else if (2 == paramInt) {
/*  65 */         str1 = "" + SystemEnv.getHtmlLabelName(10003676, ThreadVarLanguage.getLang()) + "";
/*     */       } else {
/*  67 */         str1 = "" + SystemEnv.getHtmlLabelName(10003677, ThreadVarLanguage.getLang()) + "";
/*     */       } 
/*  69 */       String str2 = "";
/*  70 */       RecordSet recordSet = new RecordSet();
/*  71 */       str2 = " update HRMSCHEDULESIGNIMP set signtype=" + paramInt + ",importsql='" + paramString2 + "',signFrom='" + str1 + "' where uuid='" + paramString1 + "' ";
/*  72 */       recordSet.execute(str2);
/*  73 */     } catch (Exception exception) {
/*  74 */       exception.printStackTrace();
/*     */     } 
/*     */   }
/*     */   public static void deleteSignImp() throws Exception {
/*  78 */     String str1 = "";
/*  79 */     String str2 = DateUtil.getCurrentDate();
/*  80 */     String str3 = DateUtil.addDate(str2, -7);
/*  81 */     RecordSet recordSet = new RecordSet();
/*     */     
/*  83 */     boolean bool = false;
/*     */     
/*  85 */     str1 = " select count(*) from HRMSCHEDULESIGNIMP where signdate <='" + str3 + "' ";
/*     */     
/*  87 */     recordSet.execute(str1);
/*  88 */     if (recordSet.next() && 
/*  89 */       recordSet.getInt(1) > 0) bool = true;
/*     */     
/*  91 */     if (bool) {
/*  92 */       str1 = "  delete from HRMSCHEDULESIGNIMP where signdate <='" + str3 + "' ";
/*  93 */       recordSet.execute(str1);
/*     */     } 
/*     */   }
/*     */   
/*     */   public static void delete(String paramString1, String paramString2) throws Exception {
/*  98 */     String str = "";
/*  99 */     RecordSet recordSet = new RecordSet();
/* 100 */     str = " delete from HrmScheduleSign where isimport=1 and signdate >='" + paramString1 + "' and signdate < '" + paramString2 + "'";
/* 101 */     recordSet.execute(str);
/*     */   }
/*     */   public static void delete(String paramString1, String paramString2, int paramInt) throws Exception {
/* 104 */     String str = "";
/* 105 */     RecordSet recordSet = new RecordSet();
/* 106 */     str = " delete from HrmScheduleSign where isimport=1 and signdate >='" + paramString1 + "' and signdate <= '" + paramString2 + "' and userId=" + paramInt;
/* 107 */     recordSet.execute(str);
/*     */   }
/*     */   
/*     */   public void deleteByUser() throws Exception {
/* 111 */     String str = "";
/* 112 */     RecordSet recordSet = new RecordSet();
/* 113 */     str = " delete from HrmScheduleSign where signdate ='" + this.signdate + "' and signtype = '" + this.signtype + "' and userId=" + this.userid;
/* 114 */     recordSet.execute(str);
/*     */   }
/*     */   
/*     */   public int getId() {
/* 118 */     return this.id;
/*     */   }
/*     */   public void setId(int paramInt) {
/* 121 */     this.id = paramInt;
/*     */   }
/*     */   public int getUserid() {
/* 124 */     return this.userid;
/*     */   }
/*     */   public void setUserid(int paramInt) {
/* 127 */     this.userid = paramInt;
/*     */   }
/*     */   public int getUsertype() {
/* 130 */     return this.usertype;
/*     */   }
/*     */   public void setUsertype(int paramInt) {
/* 133 */     this.usertype = paramInt;
/*     */   }
/*     */   public int getSigntype() {
/* 136 */     return this.signtype;
/*     */   }
/*     */   public void setSigntype(int paramInt) {
/* 139 */     this.signtype = paramInt;
/*     */   }
/*     */   public String getSigndate() {
/* 142 */     return this.signdate;
/*     */   }
/*     */   public void setSigndate(String paramString) {
/* 145 */     this.signdate = paramString;
/*     */   }
/*     */   public String getSigntime() {
/* 148 */     return this.signtime;
/*     */   }
/*     */   public void setSigntime(String paramString) {
/* 151 */     this.signtime = paramString;
/*     */   }
/*     */   public String getClientaddress() {
/* 154 */     return this.clientaddress;
/*     */   }
/*     */   public void setClientaddress(String paramString) {
/* 157 */     this.clientaddress = paramString;
/*     */   }
/*     */   public int getIsincom() {
/* 160 */     return this.isincom;
/*     */   }
/*     */   public void setIsincom(int paramInt) {
/* 163 */     this.isincom = paramInt;
/*     */   }
/*     */   
/*     */   public String getSignfrom() {
/* 167 */     return this.signfrom;
/*     */   }
/*     */   
/*     */   public void setSignfrom(String paramString) {
/* 171 */     this.signfrom = paramString;
/*     */   }
/*     */   
/*     */   public String getLongitude() {
/* 175 */     return this.longitude;
/*     */   }
/*     */   
/*     */   public void setLongitude(String paramString) {
/* 179 */     this.longitude = paramString;
/*     */   }
/*     */   
/*     */   public String getLatitude() {
/* 183 */     return this.latitude;
/*     */   }
/*     */   
/*     */   public void setLatitude(String paramString) {
/* 187 */     this.latitude = paramString;
/*     */   }
/*     */   
/*     */   public String getAddr() {
/* 191 */     return this.addr;
/*     */   }
/*     */   
/*     */   public void setAddr(String paramString) {
/* 195 */     this.addr = paramString;
/*     */   }
/*     */   
/*     */   public int getIsimport() {
/* 199 */     return this.isimport;
/*     */   }
/*     */   
/*     */   public void setIsimport(int paramInt) {
/* 203 */     this.isimport = paramInt;
/*     */   }
/*     */   public String getUuid() {
/* 206 */     return this.uuid;
/*     */   }
/*     */   
/*     */   public void setUuid(String paramString) {
/* 210 */     this.uuid = paramString;
/*     */   }
/*     */   
/*     */   public String getSuuid() {
/* 214 */     return this.suuid;
/*     */   }
/*     */   
/*     */   public void setSuuid(String paramString) {
/* 218 */     this.suuid = paramString;
/*     */   }
/*     */   
/*     */   public String getImportsql() {
/* 222 */     return this.importsql;
/*     */   }
/*     */   
/*     */   public void setImportsql(String paramString) {
/* 226 */     this.importsql = paramString;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/schedule/HrmScheduleSign.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */