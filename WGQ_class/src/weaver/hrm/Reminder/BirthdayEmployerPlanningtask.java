/*    */ package weaver.hrm.Reminder;
/*    */ 
/*    */ import com.engine.hrm.biz.BirthdayRemindBiz;
/*    */ import weaver.integration.logging.Logger;
/*    */ import weaver.integration.logging.LoggerFactory;
/*    */ import weaver.interfaces.schedule.BaseCronJob;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class BirthdayEmployerPlanningtask
/*    */   extends BaseCronJob
/*    */ {
/* 24 */   private Logger newlog = LoggerFactory.getLogger(BirthdayEmployerPlanningtask.class);
/*    */   
/*    */   public void execute() {
/* 27 */     long l = System.currentTimeMillis();
/* 28 */     this.newlog.info("计划任务【BirthdayEmployerPlanningtask】开始执行");
/* 29 */     start();
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */     
/* 35 */     this.newlog.info("计划任务【BirthdayEmployerPlanningtask】执行结束，用时：" + (System.currentTimeMillis() - l));
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   private void start() {
/*    */     try {
/* 44 */       BirthdayRemindBiz.getInstance().dispatchBirthRemindMsg(null);
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */     
/*    */     }
/* 55 */     catch (Exception exception) {
/* 56 */       this.newlog.info("计划任务【BirthdayEmployerPlanningtask】执行异常：" + exception);
/* 57 */       exception.printStackTrace();
/*    */     } 
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/Reminder/BirthdayEmployerPlanningtask.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */