/*    */ package weaver.hrm.Reminder;
/*    */ 
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.settings.ChgPasswdReminder;
/*    */ import weaver.hrm.settings.RemindSettings;
/*    */ import weaver.integration.logging.Logger;
/*    */ import weaver.integration.logging.LoggerFactory;
/*    */ import weaver.interfaces.schedule.BaseCronJob;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class ChgPasswdPlanningtask
/*    */   extends BaseCronJob
/*    */ {
/* 23 */   private Logger newlog = LoggerFactory.getLogger(ChgPasswdPlanningtask.class);
/*    */   
/*    */   public void execute() {
/* 26 */     long l = System.currentTimeMillis();
/* 27 */     this.newlog.info("计划任务【ChgPasswdPlanningtask】开始执行");
/* 28 */     start();
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */     
/* 34 */     this.newlog.info("计划任务【ChgPasswdPlanningtask】执行结束，用时：" + (System.currentTimeMillis() - l));
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   private void start() {
/*    */     try {
/* 43 */       ChgPasswdReminder chgPasswdReminder = new ChgPasswdReminder();
/* 44 */       RemindSettings remindSettings = chgPasswdReminder.getRemindSettings();
/* 45 */       String str1 = remindSettings.getRemindperiod();
/* 46 */       String str2 = remindSettings.getValid();
/* 47 */       if (str2 != null && str2.equals("1")) {
/* 48 */         chgPasswdReminder.remind(Util.getIntValue(str1));
/*    */       }
/* 50 */     } catch (Exception exception) {
/* 51 */       this.newlog.info("计划任务【ChgPasswdPlanningtask】执行异常：" + exception);
/* 52 */       exception.printStackTrace();
/*    */     } 
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/Reminder/ChgPasswdPlanningtask.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */