/*     */ package weaver.hrm.Reminder;
/*     */ 
/*     */ import com.alibaba.fastjson.JSONArray;
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import com.engine.kq.biz.KQAutoCheckBiz;
/*     */ import com.engine.kq.biz.KQAutoTimeRangeComInfo;
/*     */ import com.engine.kq.biz.KQGroupBiz;
/*     */ import com.engine.kq.log.KQLog;
/*     */ import com.google.common.collect.Lists;
/*     */ import com.google.common.collect.Maps;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.common.DateUtil;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.integration.logging.Logger;
/*     */ import weaver.integration.logging.LoggerFactory;
/*     */ import weaver.interfaces.schedule.BaseCronJob;
/*     */ 
/*     */ 
/*     */ public class KQAutoCardTask
/*     */   extends BaseCronJob
/*     */ {
/*  25 */   private Logger newlog = LoggerFactory.getLogger(KQAutoCardTask.class);
/*     */   
/*     */   public void execute() {
/*  28 */     long l = System.currentTimeMillis();
/*  29 */     this.newlog.info("计划任务【KQAutoCardTask】开始执行");
/*  30 */     start();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  36 */     this.newlog.info("计划任务【KQAutoCardTask】执行结束，用时：" + (System.currentTimeMillis() - l));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void start() {
/*     */     try {
/*  44 */       initAutoCardTaskByDate("", false, "");
/*  45 */     } catch (Exception exception) {
/*  46 */       this.newlog.info("计划任务【KQAutoCardTask】执行异常：" + exception);
/*  47 */       exception.printStackTrace();
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public void initAutoCardTask(String paramString) throws Exception {}
/*     */ 
/*     */   
/*     */   public void initAutoCardTaskByDate(String paramString1, boolean paramBoolean, String paramString2) throws Exception {
/*  57 */     KQLog kQLog = new KQLog();
/*     */     
/*  59 */     KQAutoCheckBiz kQAutoCheckBiz = new KQAutoCheckBiz();
/*  60 */     KQGroupBiz kQGroupBiz = new KQGroupBiz();
/*     */     
/*  62 */     String str1 = DateUtil.getCurrentDate();
/*  63 */     if (paramString2.length() > 0) {
/*  64 */       str1 = paramString2;
/*     */     }
/*  66 */     String str2 = DateUtil.addDate(str1, -1);
/*  67 */     String str3 = DateUtil.addDate(str1, 1);
/*     */     
/*  69 */     ArrayList<String> arrayList1 = Lists.newArrayList();
/*  70 */     RecordSet recordSet1 = new RecordSet();
/*     */ 
/*     */     
/*  73 */     String str4 = "delete from kq_auto_timerange where kq_date < ? ";
/*  74 */     recordSet1.executeUpdate(str4, new Object[] { str2 });
/*     */     
/*  76 */     RecordSet recordSet2 = new RecordSet();
/*     */     
/*  78 */     String str5 = "select * from kq_group where auto_checkout=1 or auto_checkin=1 ";
/*  79 */     if (paramString1.length() > 0) {
/*  80 */       str5 = str5 + " and id = ? ";
/*  81 */       recordSet1.executeQuery(str5, new Object[] { paramString1 });
/*     */     } else {
/*  83 */       recordSet1.executeQuery(str5, new Object[0]);
/*     */     } 
/*  85 */     while (recordSet1.next()) {
/*  86 */       String str = recordSet1.getString("id");
/*  87 */       List list = kQGroupBiz.getGroupMembers(str);
/*  88 */       kQLog.info("initAutoCardTask:groupId:" + str + ":lsGroupMembers:" + list);
/*  89 */       for (String str6 : list) {
/*  90 */         if (!arrayList1.contains(str6)) {
/*  91 */           arrayList1.add(str6);
/*     */         }
/*     */       } 
/*     */     } 
/*  95 */     ArrayList<String> arrayList2 = new ArrayList();
/*  96 */     ArrayList<String> arrayList3 = new ArrayList();
/*     */     
/*  98 */     for (String str : arrayList1) {
/*  99 */       ArrayList arrayList4 = Lists.newArrayList();
/* 100 */       ArrayList arrayList5 = Lists.newArrayList();
/* 101 */       if (paramBoolean) {
/* 102 */         kQAutoCheckBiz.initAutoInfo(str, str1, arrayList5);
/* 103 */         if (!arrayList5.isEmpty()) {
/* 104 */           arrayList4.addAll(arrayList5);
/*     */         }
/*     */       } else {
/* 107 */         arrayList5 = Lists.newArrayList();
/* 108 */         kQAutoCheckBiz.initAutoInfo(str, str1, arrayList5);
/* 109 */         if (!arrayList5.isEmpty()) {
/* 110 */           arrayList4.addAll(arrayList5);
/*     */         }
/* 112 */         arrayList5 = Lists.newArrayList();
/* 113 */         kQAutoCheckBiz.initAutoInfo(str, str2, arrayList5);
/* 114 */         if (!arrayList5.isEmpty()) {
/* 115 */           arrayList4.addAll(arrayList5);
/*     */         }
/* 117 */         arrayList5 = Lists.newArrayList();
/* 118 */         kQAutoCheckBiz.initAutoInfo(str, str3, arrayList5);
/* 119 */         if (!arrayList5.isEmpty()) {
/* 120 */           arrayList4.addAll(arrayList5);
/*     */         }
/*     */       } 
/* 123 */       kQLog.info("initAutoCardTask:members:" + str + ":auto_list_all:" + arrayList4);
/* 124 */       if (!arrayList4.isEmpty()) {
/* 125 */         HashMap<String, JSONArray> hashMap = Maps.newHashMap();
/* 126 */         JSONArray jSONArray = new JSONArray();
/* 127 */         for (Map map : arrayList4) {
/* 128 */           for (Map.Entry entry : map.entrySet()) {
/* 129 */             String str6 = (String)entry.getKey();
/* 130 */             if (str6.indexOf("task_starttime") > -1) {
/* 131 */               String[] arrayOfString = str6.split("_", 2);
/* 132 */               if (arrayOfString.length == 2) {
/* 133 */                 String str7 = arrayOfString[0];
/* 134 */                 String str8 = str7 + "_task_starttime";
/* 135 */                 String str9 = str7 + "_task_endtime";
/* 136 */                 String str10 = (String)map.get(str8);
/* 137 */                 String str11 = (String)map.get(str9);
/*     */                 
/* 139 */                 String str12 = (String)map.get("beginSignScope");
/* 140 */                 String str13 = (String)map.get("endSignScope");
/* 141 */                 String str14 = (String)map.get("hasSetSignScope");
/* 142 */                 String str15 = (String)map.get("card_type");
/* 143 */                 String str16 = (String)map.get("locationcheck");
/* 144 */                 String str17 = (String)map.get("wificheck");
/* 145 */                 String str18 = (String)map.get("locationfacecheck");
/* 146 */                 String str19 = (String)map.get("wififacecheck");
/* 147 */                 String str20 = (String)map.get("belongdate");
/*     */                 
/* 149 */                 JSONObject jSONObject = new JSONObject();
/* 150 */                 jSONObject.put("endSignScope", str13);
/* 151 */                 jSONObject.put("beginSignScope", str12);
/* 152 */                 jSONObject.put("hasSetSignScope", str14);
/* 153 */                 jSONObject.put("card_type", str15);
/* 154 */                 jSONObject.put("task_starttime", str10);
/* 155 */                 jSONObject.put("task_endtime", str11);
/* 156 */                 jSONObject.put("belongdate", str20);
/* 157 */                 String str21 = str + "_" + str7;
/* 158 */                 String str22 = str7 + "#" + str21;
/* 159 */                 if (hashMap.containsKey(str22)) {
/* 160 */                   JSONArray jSONArray1 = (JSONArray)hashMap.get(str22);
/* 161 */                   jSONArray1.add(jSONObject); continue;
/*     */                 } 
/* 163 */                 jSONArray = new JSONArray();
/* 164 */                 jSONArray.add(jSONObject);
/* 165 */                 hashMap.put(str22, jSONArray);
/*     */               } 
/*     */             } 
/*     */           } 
/*     */         } 
/*     */         
/* 171 */         if (!hashMap.isEmpty()) {
/* 172 */           for (Map.Entry<String, JSONArray> entry : hashMap.entrySet()) {
/* 173 */             String str6 = (String)entry.getKey();
/* 174 */             JSONArray jSONArray1 = (JSONArray)entry.getValue();
/* 175 */             String str7 = jSONArray1.toJSONString();
/* 176 */             String[] arrayOfString = str6.split("#", 2);
/* 177 */             if (arrayOfString.length == 2) {
/* 178 */               String str8 = arrayOfString[1];
/* 179 */               String str9 = arrayOfString[0];
/* 180 */               String str10 = "delete from kq_auto_timerange where user_kq_date='" + str8 + "'";
/* 181 */               arrayList3.add(str10);
/* 182 */               String str11 = "insert into kq_auto_timerange(user_kq_date,kq_date,kq_param) values('" + str8 + "','" + str9 + "','" + str7 + "')";
/* 183 */               arrayList2.add(str11);
/*     */             } 
/*     */           } 
/*     */         }
/*     */       } 
/*     */     } 
/* 189 */     if (!arrayList2.isEmpty()) {
/* 190 */       for (String str : arrayList3) {
/* 191 */         recordSet2.executeUpdate(str, new Object[0]);
/*     */       }
/* 193 */       for (String str : arrayList2) {
/* 194 */         kQLog.info("initAutoCardTask:sql:" + str);
/* 195 */         recordSet2.executeUpdate(str, new Object[0]);
/*     */       } 
/*     */     } 
/* 198 */     KQAutoTimeRangeComInfo kQAutoTimeRangeComInfo = new KQAutoTimeRangeComInfo();
/* 199 */     kQAutoTimeRangeComInfo.removeCache();
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/Reminder/KQAutoCardTask.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */