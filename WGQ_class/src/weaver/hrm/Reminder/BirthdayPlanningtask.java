/*    */ package weaver.hrm.Reminder;
/*    */ 
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.settings.BirthdayReminder;
/*    */ import weaver.hrm.settings.ChgPasswdReminder;
/*    */ import weaver.hrm.settings.RemindSettings;
/*    */ import weaver.integration.logging.Logger;
/*    */ import weaver.integration.logging.LoggerFactory;
/*    */ import weaver.interfaces.schedule.BaseCronJob;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class BirthdayPlanningtask
/*    */   extends BaseCronJob
/*    */ {
/* 23 */   private Logger newlog = LoggerFactory.getLogger(BirthdayPlanningtask.class);
/*    */   
/*    */   public void execute() {
/* 26 */     long l = System.currentTimeMillis();
/* 27 */     this.newlog.info("计划任务【BirthdayPlanningtask】开始执行");
/* 28 */     start();
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */     
/* 34 */     this.newlog.info("计划任务【BirthdayPlanningtask】执行结束，用时：" + (System.currentTimeMillis() - l));
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   private void start() {
/*    */     try {
/* 43 */       ChgPasswdReminder chgPasswdReminder = new ChgPasswdReminder();
/* 44 */       BirthdayReminder birthdayReminder = new BirthdayReminder();
/* 45 */       RemindSettings remindSettings = chgPasswdReminder.getRemindSettings();
/* 46 */       String str1 = remindSettings.getBirthremindperiod();
/* 47 */       String str2 = remindSettings.getCongratulation1();
/* 48 */       String str3 = remindSettings.getBirthvalid();
/* 49 */       String str4 = remindSettings.getBirthvalidadmin();
/* 50 */       String str5 = remindSettings.getBirthremindmode();
/* 51 */       if (str4 != null && str4.equals("1")) {
/* 52 */         birthdayReminder.remindAdministrator(Util.getIntValue(str1));
/*    */       }
/* 54 */     } catch (Exception exception) {
/* 55 */       this.newlog.info("计划任务【BirthdayPlanningtask】执行异常：" + exception);
/* 56 */       exception.printStackTrace();
/*    */     } 
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/Reminder/BirthdayPlanningtask.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */