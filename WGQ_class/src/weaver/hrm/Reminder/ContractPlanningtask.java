/*    */ package weaver.hrm.Reminder;
/*    */ 
/*    */ import weaver.general.BaseBean;
/*    */ import weaver.hrm.tools.HrmDateCheck;
/*    */ import weaver.integration.logging.Logger;
/*    */ import weaver.integration.logging.LoggerFactory;
/*    */ import weaver.interfaces.schedule.BaseCronJob;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class ContractPlanningtask
/*    */   extends BaseCronJob
/*    */ {
/* 35 */   private Logger newlog = LoggerFactory.getLogger(ContractPlanningtask.class);
/*    */   
/*    */   public void execute() {
/* 38 */     long l = System.currentTimeMillis();
/* 39 */     this.newlog.info("计划任务【ContractPlanningtask】开始执行");
/* 40 */     start();
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */     
/* 46 */     this.newlog.info("计划任务【ContractPlanningtask】执行结束，用时：" + (System.currentTimeMillis() - l));
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   private void start() {
/*    */     try {
/* 55 */       (new BaseBean()).writeLog("ContractPlanningtask>>>start");
/* 56 */       HrmDateCheck hrmDateCheck = new HrmDateCheck();
/* 57 */       hrmDateCheck.setResourceComInfo();
/* 58 */       hrmDateCheck.contractRemind();
/* 59 */       hrmDateCheck.contractCheck();
/* 60 */       hrmDateCheck.changeStatus();
/* 61 */       (new BaseBean()).writeLog("ContractPlanningtask>>>over");
/* 62 */     } catch (Exception exception) {
/* 63 */       this.newlog.info("计划任务【ContractPlanningtask】执行异常：" + exception);
/* 64 */       exception.printStackTrace();
/*    */     } 
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/Reminder/ContractPlanningtask.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */