/*      */ package weaver.hrm.search;
/*      */ 
/*      */ import com.engine.common.service.impl.HrmCommonServiceImpl;
/*      */ import java.io.Serializable;
/*      */ import java.util.ArrayList;
/*      */ import java.util.Calendar;
/*      */ import java.util.HashMap;
/*      */ import java.util.Map;
/*      */ import java.util.Set;
/*      */ import javax.servlet.http.HttpServletRequest;
/*      */ import weaver.common.StringUtil;
/*      */ import weaver.conn.RecordSet;
/*      */ import weaver.docs.docs.CustomFieldManager;
/*      */ import weaver.general.BaseBean;
/*      */ import weaver.general.GCONST;
/*      */ import weaver.general.Util;
/*      */ import weaver.hrm.common.database.dialect.DbDialectFactory;
/*      */ import weaver.hrm.common.database.dialect.DialectUtil;
/*      */ import weaver.hrm.common.database.dialect.IDbDialectSql;
/*      */ import weaver.hrm.company.DepartmentComInfo;
/*      */ 
/*      */ 
/*      */ 
/*      */ public class HrmSearchComInfo
/*      */   extends BaseBean
/*      */   implements Serializable
/*      */ {
/*      */   private static final long serialVersionUID = 2834684841735192912L;
/*      */   private static boolean isinit = true;
/*   30 */   private String resourceid = "";
/*   31 */   private String resourcename = "";
/*   32 */   private String belongto = "";
/*   33 */   private String jobtitle = "";
/*   34 */   private String activitydesc = "";
/*   35 */   private String jobgroup = "";
/*   36 */   private String jobactivity = "";
/*   37 */   private String costcenter = "";
/*   38 */   private String competency = "";
/*   39 */   private String resourcetype = "";
/*   40 */   private String status = "";
/*   41 */   private String subcompany1 = "";
/*   42 */   private String subcompany2 = "";
/*   43 */   private String subcompany3 = "";
/*   44 */   private String subcompany4 = "";
/*   45 */   private String department = "";
/*   46 */   private String location = "";
/*   47 */   private String manager = "";
/*   48 */   private String assistant = "";
/*   49 */   private String roles = "";
/*   50 */   private String seclevel = "";
/*   51 */   private String seclevelTo = "";
/*   52 */   private String joblevel = "";
/*   53 */   private String joblevelTo = "";
/*   54 */   private String workroom = "";
/*   55 */   private String telephone = "";
/*   56 */   private String startdate = "";
/*   57 */   private String startdateTo = "";
/*   58 */   private String enddate = "";
/*   59 */   private String enddateTo = "";
/*   60 */   private String contractdate = "";
/*   61 */   private String contractdateTo = "";
/*   62 */   private String birthdaydate = "";
/*   63 */   private String birthdaydateTo = "";
/*      */ 
/*      */ 
/*      */   
/*   67 */   private String age = "";
/*   68 */   private String ageTo = "";
/*   69 */   private String sex = "";
/*   70 */   private int accounttype = -1;
/*      */ 
/*      */ 
/*      */   
/*   74 */   private String resourceidfrom = "";
/*   75 */   private String resourceidto = "";
/*   76 */   private String workcode = "";
/*   77 */   private String jobcall = "";
/*   78 */   private String mobile = "";
/*   79 */   private String mobilecall = "";
/*   80 */   private String fax = "";
/*   81 */   private String email = "";
/*   82 */   private String folk = "";
/*   83 */   private String nativeplace = "";
/*   84 */   private String regresidentplace = "";
/*   85 */   private String maritalstatus = "";
/*   86 */   private String certificatenum = "";
/*   87 */   private String tempresidentnumber = "";
/*   88 */   private String residentplace = "";
/*   89 */   private String homeaddress = "";
/*   90 */   private String healthinfo = "";
/*   91 */   private String heightfrom = "";
/*   92 */   private String heightto = "";
/*   93 */   private String weightfrom = "";
/*   94 */   private String weightto = "";
/*   95 */   private String educationlevel = "";
/*   96 */   private String educationlevelTo = "";
/*   97 */   private String degree = "";
/*   98 */   private String usekind = "";
/*   99 */   private String policy = "";
/*  100 */   private String bememberdatefrom = "";
/*  101 */   private String bememberdateto = "";
/*  102 */   private String bepartydatefrom = "";
/*  103 */   private String bepartydateto = "";
/*  104 */   private String islabouunion = "";
/*  105 */   private String bankid1 = "";
/*  106 */   private String accountid1 = "";
/*  107 */   private String accumfundaccount = "";
/*  108 */   private String loginid = "";
/*  109 */   private String systemlanguage = "";
/*      */   
/*  111 */   private String groupid = "";
/*  112 */   private String groupVaild = "";
/*      */   
/*  114 */   private Map customFieldBase = new HashMap<>();
/*  115 */   private Map customFieldPersonal = new HashMap<>();
/*  116 */   private Map customFieldWork = new HashMap<>();
/*      */ 
/*      */   
/*      */   private boolean isoracle = false;
/*      */ 
/*      */   
/*  122 */   private String dff01name = "";
/*  123 */   private String dff02name = "";
/*  124 */   private String dff03name = "";
/*  125 */   private String dff04name = "";
/*  126 */   private String dff05name = "";
/*  127 */   private String dff01nameto = "";
/*  128 */   private String dff02nameto = "";
/*  129 */   private String dff03nameto = "";
/*  130 */   private String dff04nameto = "";
/*  131 */   private String dff05nameto = "";
/*      */ 
/*      */   
/*  134 */   private String nff01name = "";
/*  135 */   private String nff02name = "";
/*  136 */   private String nff03name = "";
/*  137 */   private String nff04name = "";
/*  138 */   private String nff05name = "";
/*  139 */   private String nff01nameto = "";
/*  140 */   private String nff02nameto = "";
/*  141 */   private String nff03nameto = "";
/*  142 */   private String nff04nameto = "";
/*  143 */   private String nff05nameto = "";
/*      */ 
/*      */   
/*  146 */   private String tff01name = "";
/*  147 */   private String tff02name = "";
/*  148 */   private String tff03name = "";
/*  149 */   private String tff04name = "";
/*  150 */   private String tff05name = "";
/*      */ 
/*      */   
/*  153 */   private String bff01name = "";
/*  154 */   private String bff02name = "";
/*  155 */   private String bff03name = "";
/*  156 */   private String bff04name = "";
/*  157 */   private String bff05name = "";
/*      */ 
/*      */ 
/*      */   
/*  161 */   private String virtualtype = "";
/*  162 */   private String resourcesql = "";
/*  163 */   private String orderby = "";
/*  164 */   private String searchFrom = "";
/*      */ 
/*      */   
/*      */   public void resetSearchInfo() {
/*  168 */     this.resourceid = "";
/*  169 */     this.resourcename = "";
/*  170 */     this.belongto = "";
/*  171 */     this.jobtitle = "";
/*  172 */     this.activitydesc = "";
/*  173 */     this.jobgroup = "";
/*  174 */     this.jobactivity = "";
/*  175 */     this.costcenter = "";
/*  176 */     this.competency = "";
/*  177 */     this.resourcetype = "";
/*  178 */     this.status = "";
/*  179 */     this.subcompany1 = "";
/*  180 */     this.subcompany2 = "";
/*  181 */     this.subcompany3 = "";
/*  182 */     this.subcompany4 = "";
/*  183 */     this.department = "";
/*  184 */     this.location = "";
/*  185 */     this.manager = "";
/*  186 */     this.assistant = "";
/*  187 */     this.roles = "";
/*  188 */     this.seclevel = "";
/*  189 */     this.seclevelTo = "";
/*  190 */     this.joblevel = "";
/*  191 */     this.joblevelTo = "";
/*  192 */     this.workroom = "";
/*  193 */     this.telephone = "";
/*  194 */     this.startdate = "";
/*  195 */     this.startdateTo = "";
/*  196 */     this.enddate = "";
/*  197 */     this.enddateTo = "";
/*  198 */     this.contractdate = "";
/*  199 */     this.contractdateTo = "";
/*  200 */     this.birthdaydate = "";
/*  201 */     this.birthdaydateTo = "";
/*      */ 
/*      */ 
/*      */     
/*  205 */     this.age = "";
/*  206 */     this.ageTo = "";
/*  207 */     this.sex = "";
/*  208 */     this.accounttype = -1;
/*      */     
/*  210 */     this.resourceidfrom = "";
/*  211 */     this.resourceidto = "";
/*  212 */     this.workcode = "";
/*  213 */     this.jobcall = "";
/*  214 */     this.mobile = "";
/*  215 */     this.mobilecall = "";
/*  216 */     this.fax = "";
/*  217 */     this.email = "";
/*  218 */     this.folk = "";
/*  219 */     this.nativeplace = "";
/*  220 */     this.regresidentplace = "";
/*  221 */     this.maritalstatus = "";
/*  222 */     this.certificatenum = "";
/*  223 */     this.tempresidentnumber = "";
/*  224 */     this.residentplace = "";
/*  225 */     this.homeaddress = "";
/*  226 */     this.healthinfo = "";
/*  227 */     this.heightfrom = "";
/*  228 */     this.heightto = "";
/*  229 */     this.weightfrom = "";
/*  230 */     this.weightto = "";
/*  231 */     this.educationlevel = "";
/*  232 */     this.educationlevelTo = "";
/*  233 */     this.degree = "";
/*  234 */     this.usekind = "";
/*  235 */     this.policy = "";
/*  236 */     this.bememberdatefrom = "";
/*  237 */     this.bememberdateto = "";
/*  238 */     this.bepartydatefrom = "";
/*  239 */     this.bepartydateto = "";
/*  240 */     this.islabouunion = "";
/*  241 */     this.bankid1 = "";
/*  242 */     this.accountid1 = "";
/*  243 */     this.accumfundaccount = "";
/*  244 */     this.loginid = "";
/*  245 */     this.systemlanguage = "";
/*      */     
/*  247 */     this.dff01name = "";
/*  248 */     this.dff02name = "";
/*  249 */     this.dff03name = "";
/*  250 */     this.dff04name = "";
/*  251 */     this.dff05name = "";
/*  252 */     this.dff01nameto = "";
/*  253 */     this.dff02nameto = "";
/*  254 */     this.dff03nameto = "";
/*  255 */     this.dff04nameto = "";
/*  256 */     this.dff05nameto = "";
/*  257 */     this.nff01name = "";
/*  258 */     this.nff02name = "";
/*  259 */     this.nff03name = "";
/*  260 */     this.nff04name = "";
/*  261 */     this.nff05name = "";
/*  262 */     this.nff01nameto = "";
/*  263 */     this.nff02nameto = "";
/*  264 */     this.nff03nameto = "";
/*  265 */     this.nff04nameto = "";
/*  266 */     this.nff05nameto = "";
/*  267 */     this.tff01name = "";
/*  268 */     this.tff02name = "";
/*  269 */     this.tff03name = "";
/*  270 */     this.tff04name = "";
/*  271 */     this.tff05name = "";
/*  272 */     this.bff01name = "";
/*  273 */     this.bff02name = "";
/*  274 */     this.bff03name = "";
/*  275 */     this.bff04name = "";
/*  276 */     this.bff05name = "";
/*      */     
/*  278 */     this.resourcesql = "";
/*  279 */     this.orderby = "";
/*  280 */     this.isoracle = false;
/*  281 */     this.customFieldBase = new HashMap<>();
/*  282 */     this.customFieldPersonal = new HashMap<>();
/*  283 */     this.searchFrom = "";
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setResourcesql(String paramString) {
/*  292 */     this.resourcesql = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getResourcesql() {
/*  300 */     return this.resourcesql;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setOrderby(String paramString) {
/*  308 */     this.orderby = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getOrderby() {
/*  316 */     return this.orderby;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setResourceid(String paramString) {
/*  324 */     this.resourceid = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getResourceid() {
/*  332 */     return this.resourceid;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setResourcename(String paramString) {
/*  340 */     this.resourcename = paramString.trim();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getResourcename() {
/*  348 */     return this.resourcename;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setBelongto(String paramString) {
/*  356 */     this.belongto = paramString.trim();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getBelongto() {
/*  364 */     return this.belongto;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setJobtitle(String paramString) {
/*  372 */     this.jobtitle = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getJobtitle() {
/*  380 */     return this.jobtitle;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setActivitydesc(String paramString) {
/*  388 */     this.activitydesc = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getActivitydesc() {
/*  396 */     return this.activitydesc;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setJobgroup(String paramString) {
/*  404 */     this.jobgroup = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getJobgroup() {
/*  412 */     return this.jobgroup;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setJobactivity(String paramString) {
/*  420 */     this.jobactivity = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getJobactivity() {
/*  428 */     return this.jobactivity;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setCostcenter(String paramString) {
/*  436 */     this.costcenter = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getCostcenter() {
/*  444 */     return this.costcenter;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setCompetency(String paramString) {
/*  452 */     this.competency = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getCompetency() {
/*  460 */     return this.competency;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setResourcetype(String paramString) {
/*  468 */     this.resourcetype = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getResourcetype() {
/*  476 */     return this.resourcetype;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setStatus(String paramString) {
/*  484 */     this.status = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getStatus() {
/*  492 */     return this.status;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setSubcompany1(String paramString) {
/*  500 */     this.subcompany1 = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getSubcompany1() {
/*  508 */     return this.subcompany1;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setSubcompany2(String paramString) {
/*  516 */     this.subcompany2 = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getSubcompany2() {
/*  524 */     return this.subcompany2;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setSubcompany3(String paramString) {
/*  532 */     this.subcompany3 = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getSubcompany3() {
/*  540 */     return this.subcompany3;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setSubcompany4(String paramString) {
/*  548 */     this.subcompany4 = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getSubcompany4() {
/*  556 */     return this.subcompany4;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setDepartment(String paramString) {
/*  564 */     this.department = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getDepartment() {
/*  572 */     return this.department;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setLocation(String paramString) {
/*  580 */     this.location = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getLocation() {
/*  588 */     return this.location;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setManager(String paramString) {
/*  596 */     this.manager = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getManager() {
/*  604 */     return this.manager;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setAssistant(String paramString) {
/*  612 */     this.assistant = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getAssistant() {
/*  620 */     return this.assistant;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setRoles(String paramString) {
/*  628 */     this.roles = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getRoles() {
/*  636 */     return this.roles;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setSeclevel(String paramString) {
/*  644 */     this.seclevel = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getSeclevel() {
/*  652 */     return this.seclevel;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setSeclevelTo(String paramString) {
/*  660 */     this.seclevelTo = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getSeclevelTo() {
/*  668 */     return this.seclevelTo;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setJoblevel(String paramString) {
/*  676 */     this.joblevel = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getJoblevel() {
/*  684 */     return this.joblevel;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setJoblevelTo(String paramString) {
/*  692 */     this.joblevelTo = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getJoblevelTo() {
/*  700 */     return this.joblevelTo;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setWorkroom(String paramString) {
/*  708 */     this.workroom = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getWorkroom() {
/*  716 */     return this.workroom;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setTelephone(String paramString) {
/*  724 */     this.telephone = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getTelephone() {
/*  732 */     return this.telephone;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setStartdate(String paramString) {
/*  740 */     this.startdate = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getStartdate() {
/*  748 */     return this.startdate;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setStartdateTo(String paramString) {
/*  766 */     this.startdateTo = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getStartdateTo() {
/*  774 */     return this.startdateTo;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setEnddate(String paramString) {
/*  782 */     this.enddate = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getEnddate() {
/*  790 */     return this.enddate;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setEnddateTo(String paramString) {
/*  798 */     this.enddateTo = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getEnddateTo() {
/*  806 */     return this.enddateTo;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setContractdate(String paramString) {
/*  814 */     this.contractdate = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getContractdate() {
/*  822 */     return this.contractdate;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setContractdateTo(String paramString) {
/*  830 */     this.contractdateTo = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getContractdateTo() {
/*  838 */     return this.contractdateTo;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setBirthdaydate(String paramString) {
/*  846 */     this.birthdaydate = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getBirthdaydate() {
/*  878 */     return this.birthdaydate;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setBirthdaydateTo(String paramString) {
/*  910 */     this.birthdaydateTo = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getBirthdaydateTo() {
/*  918 */     return this.birthdaydateTo;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setAge(String paramString) {
/*  926 */     this.age = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getAge() {
/*  934 */     return this.age;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setAgeTo(String paramString) {
/*  942 */     this.ageTo = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getAgeTo() {
/*  950 */     return this.ageTo;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setSex(String paramString) {
/*  958 */     this.sex = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getSex() {
/*  966 */     return this.sex;
/*      */   }
/*      */   
/*      */   public int getAccounttype() {
/*  970 */     return this.accounttype;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setAccounttype(int paramInt) {
/*  977 */     this.accounttype = paramInt;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setResourceidfrom(String paramString) {
/*  985 */     this.resourceidfrom = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getResourceidfrom() {
/*  993 */     return this.resourceidfrom;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setResourceidto(String paramString) {
/* 1001 */     this.resourceidto = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getResourceidto() {
/* 1009 */     return this.resourceidto;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setWorkcode(String paramString) {
/* 1017 */     this.workcode = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getWorkcode() {
/* 1025 */     return this.workcode;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setJobcall(String paramString) {
/* 1033 */     this.jobcall = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getJobcall() {
/* 1041 */     return this.jobcall;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setMobile(String paramString) {
/* 1049 */     this.mobile = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getMobile() {
/* 1057 */     return this.mobile;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setMobilecall(String paramString) {
/* 1065 */     this.mobilecall = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getMobilecall() {
/* 1073 */     return this.mobilecall;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setFax(String paramString) {
/* 1081 */     this.fax = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getFax() {
/* 1089 */     return this.fax;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setEmail(String paramString) {
/* 1097 */     this.email = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getEmail() {
/* 1105 */     return this.email;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setFolk(String paramString) {
/* 1113 */     this.folk = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getFolk() {
/* 1121 */     return this.folk;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setNativeplace(String paramString) {
/* 1129 */     this.nativeplace = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getNativeplace() {
/* 1137 */     return this.nativeplace;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setRegresidentplace(String paramString) {
/* 1145 */     this.regresidentplace = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getRegresidentplace() {
/* 1153 */     return this.regresidentplace;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setMaritalstatus(String paramString) {
/* 1161 */     this.maritalstatus = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getMaritalstatus() {
/* 1169 */     return this.maritalstatus;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setCertificatenum(String paramString) {
/* 1177 */     this.certificatenum = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getCertificatenum() {
/* 1185 */     return this.certificatenum;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setTempresidentnumber(String paramString) {
/* 1193 */     this.tempresidentnumber = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getTempresidentnumber() {
/* 1201 */     return this.tempresidentnumber;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setResidentplace(String paramString) {
/* 1209 */     this.residentplace = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getResidentplace() {
/* 1217 */     return this.residentplace;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setHomeaddress(String paramString) {
/* 1225 */     this.homeaddress = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getHomeaddress() {
/* 1233 */     return this.homeaddress;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setHealthinfo(String paramString) {
/* 1241 */     this.healthinfo = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getHealthinfo() {
/* 1249 */     return this.healthinfo;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setHeightfrom(String paramString) {
/* 1257 */     this.heightfrom = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getHeightfrom() {
/* 1265 */     return this.heightfrom;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setHeightto(String paramString) {
/* 1273 */     this.heightto = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getHeightto() {
/* 1281 */     return this.heightto;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setWeightfrom(String paramString) {
/* 1289 */     this.weightfrom = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getWeightfrom() {
/* 1297 */     return this.weightfrom;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setWeightto(String paramString) {
/* 1305 */     this.weightto = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getWeightto() {
/* 1313 */     return this.weightto;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setEducationlevel(String paramString) {
/* 1321 */     this.educationlevel = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setEducationlevelTo(String paramString) {
/* 1329 */     this.educationlevelTo = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getEducationlevel() {
/* 1337 */     return this.educationlevel;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getEducationlevelTo() {
/* 1345 */     return this.educationlevelTo;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setDegree(String paramString) {
/* 1353 */     this.degree = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getDegree() {
/* 1361 */     return this.degree;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setUsekind(String paramString) {
/* 1369 */     this.usekind = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getUsekind() {
/* 1377 */     return this.usekind;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setPolicy(String paramString) {
/* 1385 */     this.policy = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getPolicy() {
/* 1393 */     return this.policy;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setBememberdatefrom(String paramString) {
/* 1401 */     this.bememberdatefrom = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getBememberdatefrom() {
/* 1409 */     return this.bememberdatefrom;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setBememberdateto(String paramString) {
/* 1417 */     this.bememberdateto = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getBememberdateto() {
/* 1425 */     return this.bememberdateto;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setBepartydatefrom(String paramString) {
/* 1433 */     this.bepartydatefrom = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getBepartydatefrom() {
/* 1441 */     return this.bepartydatefrom;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setBepartydateto(String paramString) {
/* 1449 */     this.bepartydateto = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getBepartydateto() {
/* 1457 */     return this.bepartydateto;
/*      */   }
/*      */   
/*      */   public void setIslabouunion(String paramString) {
/* 1461 */     this.islabouunion = paramString;
/*      */   }
/*      */   
/*      */   public String getIslabouunion() {
/* 1465 */     return this.islabouunion;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setBankid1(String paramString) {
/* 1473 */     this.bankid1 = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getBankid1() {
/* 1481 */     return this.bankid1;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setAccountid1(String paramString) {
/* 1489 */     this.accountid1 = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getAccountid1() {
/* 1497 */     return this.accountid1;
/*      */   }
/*      */   
/*      */   public void setAccumfundaccount(String paramString) {
/* 1501 */     this.accumfundaccount = paramString;
/*      */   }
/*      */   
/*      */   public String getAccumfundaccount() {
/* 1505 */     return this.accumfundaccount;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setLoginid(String paramString) {
/* 1513 */     this.loginid = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getLoginid() {
/* 1521 */     return this.loginid;
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   public String getBff01name() {
/* 1527 */     return this.bff01name;
/*      */   }
/*      */   
/*      */   public void setBff01name(String paramString) {
/* 1531 */     this.bff01name = paramString;
/*      */   }
/*      */   
/*      */   public String getBff02name() {
/* 1535 */     return this.bff02name;
/*      */   }
/*      */   
/*      */   public void setBff02name(String paramString) {
/* 1539 */     this.bff02name = paramString;
/*      */   }
/*      */   
/*      */   public String getBff03name() {
/* 1543 */     return this.bff03name;
/*      */   }
/*      */   
/*      */   public void setBff03name(String paramString) {
/* 1547 */     this.bff03name = paramString;
/*      */   }
/*      */   
/*      */   public String getBff04name() {
/* 1551 */     return this.bff04name;
/*      */   }
/*      */   
/*      */   public void setBff04name(String paramString) {
/* 1555 */     this.bff04name = paramString;
/*      */   }
/*      */   
/*      */   public String getBff05name() {
/* 1559 */     return this.bff05name;
/*      */   }
/*      */   
/*      */   public void setBff05name(String paramString) {
/* 1563 */     this.bff05name = paramString;
/*      */   }
/*      */   
/*      */   public String getDff01name() {
/* 1567 */     return this.dff01name;
/*      */   }
/*      */   
/*      */   public void setDff01name(String paramString) {
/* 1571 */     this.dff01name = paramString;
/*      */   }
/*      */   
/*      */   public String getDff01nameto() {
/* 1575 */     return this.dff01nameto;
/*      */   }
/*      */   
/*      */   public void setDff01nameto(String paramString) {
/* 1579 */     this.dff01nameto = paramString;
/*      */   }
/*      */   
/*      */   public String getDff02name() {
/* 1583 */     return this.dff02name;
/*      */   }
/*      */   
/*      */   public void setDff02name(String paramString) {
/* 1587 */     this.dff02name = paramString;
/*      */   }
/*      */   
/*      */   public String getDff02nameto() {
/* 1591 */     return this.dff02nameto;
/*      */   }
/*      */   
/*      */   public void setDff02nameto(String paramString) {
/* 1595 */     this.dff02nameto = paramString;
/*      */   }
/*      */   
/*      */   public String getDff03name() {
/* 1599 */     return this.dff03name;
/*      */   }
/*      */   
/*      */   public void setDff03name(String paramString) {
/* 1603 */     this.dff03name = paramString;
/*      */   }
/*      */   
/*      */   public String getDff03nameto() {
/* 1607 */     return this.dff03nameto;
/*      */   }
/*      */   
/*      */   public void setDff03nameto(String paramString) {
/* 1611 */     this.dff03nameto = paramString;
/*      */   }
/*      */   
/*      */   public String getDff04name() {
/* 1615 */     return this.dff04name;
/*      */   }
/*      */   
/*      */   public void setDff04name(String paramString) {
/* 1619 */     this.dff04name = paramString;
/*      */   }
/*      */   
/*      */   public String getDff04nameto() {
/* 1623 */     return this.dff04nameto;
/*      */   }
/*      */   
/*      */   public void setDff04nameto(String paramString) {
/* 1627 */     this.dff04nameto = paramString;
/*      */   }
/*      */   
/*      */   public String getDff05name() {
/* 1631 */     return this.dff05name;
/*      */   }
/*      */   
/*      */   public void setDff05name(String paramString) {
/* 1635 */     this.dff05name = paramString;
/*      */   }
/*      */   
/*      */   public String getDff05nameto() {
/* 1639 */     return this.dff05nameto;
/*      */   }
/*      */   
/*      */   public void setDff05nameto(String paramString) {
/* 1643 */     this.dff05nameto = paramString;
/*      */   }
/*      */   
/*      */   public String getNff01name() {
/* 1647 */     return this.nff01name;
/*      */   }
/*      */   
/*      */   public void setNff01name(String paramString) {
/* 1651 */     this.nff01name = paramString;
/*      */   }
/*      */   
/*      */   public String getNff01nameto() {
/* 1655 */     return this.nff01nameto;
/*      */   }
/*      */   
/*      */   public void setNff01nameto(String paramString) {
/* 1659 */     this.nff01nameto = paramString;
/*      */   }
/*      */   
/*      */   public String getNff02name() {
/* 1663 */     return this.nff02name;
/*      */   }
/*      */   
/*      */   public void setNff02name(String paramString) {
/* 1667 */     this.nff02name = paramString;
/*      */   }
/*      */   
/*      */   public String getNff02nameto() {
/* 1671 */     return this.nff02nameto;
/*      */   }
/*      */   
/*      */   public void setNff02nameto(String paramString) {
/* 1675 */     this.nff02nameto = paramString;
/*      */   }
/*      */   
/*      */   public String getNff03name() {
/* 1679 */     return this.nff03name;
/*      */   }
/*      */   
/*      */   public void setNff03name(String paramString) {
/* 1683 */     this.nff03name = paramString;
/*      */   }
/*      */   
/*      */   public String getNff03nameto() {
/* 1687 */     return this.nff03nameto;
/*      */   }
/*      */   
/*      */   public void setNff03nameto(String paramString) {
/* 1691 */     this.nff03nameto = paramString;
/*      */   }
/*      */   
/*      */   public String getNff04name() {
/* 1695 */     return this.nff04name;
/*      */   }
/*      */   
/*      */   public void setNff04name(String paramString) {
/* 1699 */     this.nff04name = paramString;
/*      */   }
/*      */   
/*      */   public String getNff04nameto() {
/* 1703 */     return this.nff04nameto;
/*      */   }
/*      */   
/*      */   public void setNff04nameto(String paramString) {
/* 1707 */     this.nff04nameto = paramString;
/*      */   }
/*      */   
/*      */   public String getNff05name() {
/* 1711 */     return this.nff05name;
/*      */   }
/*      */   
/*      */   public void setNff05name(String paramString) {
/* 1715 */     this.nff05name = paramString;
/*      */   }
/*      */   
/*      */   public String getNff05nameto() {
/* 1719 */     return this.nff05nameto;
/*      */   }
/*      */   
/*      */   public void setNff05nameto(String paramString) {
/* 1723 */     this.nff05nameto = paramString;
/*      */   }
/*      */   
/*      */   public String getTff01name() {
/* 1727 */     return this.tff01name;
/*      */   }
/*      */   
/*      */   public void setTff01name(String paramString) {
/* 1731 */     this.tff01name = paramString;
/*      */   }
/*      */   
/*      */   public String getTff02name() {
/* 1735 */     return this.tff02name;
/*      */   }
/*      */   
/*      */   public void setTff02name(String paramString) {
/* 1739 */     this.tff02name = paramString;
/*      */   }
/*      */   
/*      */   public String getTff03name() {
/* 1743 */     return this.tff03name;
/*      */   }
/*      */   
/*      */   public void setTff03name(String paramString) {
/* 1747 */     this.tff03name = paramString;
/*      */   }
/*      */   
/*      */   public String getTff04name() {
/* 1751 */     return this.tff04name;
/*      */   }
/*      */   
/*      */   public void setTff04name(String paramString) {
/* 1755 */     this.tff04name = paramString;
/*      */   }
/*      */   
/*      */   public String getTff05name() {
/* 1759 */     return this.tff05name;
/*      */   }
/*      */   
/*      */   public void setTff05name(String paramString) {
/* 1763 */     this.tff05name = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setSystemlanguage(String paramString) {
/* 1771 */     this.systemlanguage = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getVirtualType() {
/* 1779 */     return this.virtualtype;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setVirtualType(String paramString) {
/* 1787 */     this.virtualtype = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getSystemlanguage() {
/* 1795 */     return this.systemlanguage;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setIsoracle(boolean paramBoolean) {
/* 1803 */     this.isoracle = paramBoolean;
/*      */   }
/*      */   
/*      */   public Map getCustomFieldBase() {
/* 1807 */     return this.customFieldBase;
/*      */   }
/*      */   
/*      */   public void setCustomFieldBase(Map paramMap) {
/* 1811 */     this.customFieldBase = paramMap;
/*      */   }
/*      */   
/*      */   public Map getCustomFieldPersonal() {
/* 1815 */     return this.customFieldPersonal;
/*      */   }
/*      */   
/*      */   public void setCustomFieldPersonal(Map paramMap) {
/* 1819 */     this.customFieldPersonal = paramMap;
/*      */   }
/*      */   
/*      */   public Map getCustomFieldWork() {
/* 1823 */     return this.customFieldWork;
/*      */   }
/*      */   
/*      */   public void setCustomFieldWork(Map paramMap) {
/* 1827 */     this.customFieldWork = paramMap;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String FormatSQLSearch() {
/* 1837 */     String str1 = "";
/* 1838 */     boolean bool = false;
/*      */     
/* 1840 */     Calendar calendar = Calendar.getInstance();
/*      */ 
/*      */     
/* 1843 */     String str2 = Util.add0(calendar.get(1), 4) + "-" + Util.add0(calendar.get(2) + 1, 2) + "-" + Util.add0(calendar.get(5), 2);
/*      */     
/* 1845 */     String str3 = "";
/* 1846 */     String str4 = "";
/*      */     
/* 1848 */     int i = Util.getIntValue(Util.add0(calendar.get(1), 4));
/*      */     
/* 1850 */     if (!this.age.equals(""))
/*      */     {
/*      */       
/* 1853 */       str3 = (i - Util.getIntValue(this.age)) + "-" + Util.add0(calendar.get(2) + 1, 2) + "-" + Util.add0(calendar.get(5), 2);
/*      */     }
/*      */     
/* 1856 */     if (!this.ageTo.equals(""))
/*      */     {
/*      */       
/* 1859 */       str4 = (i - Util.getIntValue(this.ageTo)) + "-" + Util.add0(calendar.get(2) + 1, 2) + "-" + Util.add0(calendar.get(5), 2);
/*      */     }
/*      */     
/* 1862 */     if (!this.resourcename.equals("")) {
/* 1863 */       String str6 = StringUtil.string2Unicode(this.resourcename);
/* 1864 */       if (!bool) {
/* 1865 */         bool = true;
/* 1866 */         str1 = " where";
/*      */       } else {
/* 1868 */         str1 = str1 + " and";
/*      */       } 
/* 1870 */       String str7 = "/";
/* 1871 */       if ("hrmResource".equals(this.searchFrom)) {
/* 1872 */         if (DialectUtil.isMySql()) {
/* 1873 */           str1 = str1 + " (lastname like '%" + Util.StringReplace(this.resourcename, "_", str7 + "_") + "%' escape '" + str7 + "' or pinyinlastname like '%" + Util.StringReplace(this.resourcename.toLowerCase(), "_", str7 + "_") + "%' escape '" + str7 + "'  or lastname like '%" + Util.StringReplace(str6, "_", str7 + "_") + "%' or pinyinlastname like '%" + Util.StringReplace(str6.toLowerCase(), "_", str7 + "_") + "%')";
/*      */         } else {
/* 1875 */           str1 = str1 + " (lastname like '%" + Util.StringReplace(this.resourcename, "_", "\\_") + "%' escape '\\' or pinyinlastname like '%" + Util.StringReplace(this.resourcename.toLowerCase(), "_", "\\_") + "%' escape '\\'  or lastname like '%" + Util.StringReplace(str6, "_", "\\_") + "%' or pinyinlastname like '%" + Util.StringReplace(str6.toLowerCase(), "_", "\\_") + "%')";
/*      */         }
/*      */       
/*      */       }
/* 1879 */       else if (DialectUtil.isMySql()) {
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */         
/* 1887 */         str1 = str1 + " (lastname like '%" + Util.StringReplace(this.resourcename, "_", str7 + "_") + "%' escape '" + str7 + "' or pinyinlastname like '%" + Util.StringReplace(this.resourcename.toLowerCase(), "_", str7 + "_") + "%' escape '" + str7 + "'  or mobile like '%" + Util.StringReplace(this.resourcename, "_", str7 + "_") + "%' escape '" + str7 + "' or workcode like '%" + Util.StringReplace(this.resourcename, "_", str7 + "_") + "%' escape '" + str7 + "' or lastname like '%" + Util.StringReplace(str6, "_", "_") + "%' or pinyinlastname like '%" + Util.StringReplace(str6.toLowerCase(), "_", str7 + "_") + "%' or workcode like '%" + Util.StringReplace(str6.toLowerCase(), "_", str7 + "_") + "%' or mobile like '%" + Util.StringReplace(str6, "_", str7 + "_") + "%')";
/*      */       } else {
/* 1889 */         str1 = str1 + " (lastname like '%" + Util.StringReplace(this.resourcename, "_", "\\_") + "%' escape '\\' or pinyinlastname like '%" + Util.StringReplace(this.resourcename.toLowerCase(), "_", "\\_") + "%' escape '\\'  or mobile like '%" + Util.StringReplace(this.resourcename, "_", "\\_") + "%' escape '\\' or workcode like '%" + Util.StringReplace(this.resourcename.toLowerCase(), "_", "\\_") + "%' escape '\\' or lastname like '%" + Util.StringReplace(str6, "_", "\\_") + "%' or pinyinlastname like '%" + Util.StringReplace(str6.toLowerCase(), "_", "\\_") + "%' or mobile like '%" + Util.StringReplace(str6, "_", "\\_") + "%' or workcode like '%" + Util.StringReplace(str6.toLowerCase(), "_", "\\_") + "%')";
/*      */       } 
/*      */     } 
/*      */ 
/*      */     
/* 1894 */     if (!this.belongto.equals("") && !this.belongto.equals("0")) {
/* 1895 */       if (!bool) {
/* 1896 */         bool = true;
/* 1897 */         str1 = " where belongto = " + this.belongto + " ";
/*      */       } else {
/* 1899 */         str1 = str1 + " and belongto =" + this.belongto + " ";
/*      */       } 
/*      */     }
/*      */     
/* 1903 */     if (!this.jobtitle.equals("") && !this.jobtitle.equals("0")) {
/* 1904 */       if (!bool) {
/* 1905 */         bool = true;
/* 1906 */         str1 = " where jobtitle in(select id from HrmJobTitles where jobtitlename like '%" + this.jobtitle + "%')";
/*      */       }
/*      */       else {
/*      */         
/* 1910 */         str1 = str1 + " and jobtitle in(select id from HrmJobTitles where jobtitlename like '%" + this.jobtitle + "%')";
/*      */       } 
/*      */     }
/*      */     
/* 1914 */     if (!this.activitydesc.equals("")) {
/* 1915 */       if (!bool) {
/* 1916 */         bool = true;
/* 1917 */         str1 = " where jobactivitydesc like '%" + this.activitydesc + "%' ";
/*      */       } else {
/* 1919 */         str1 = str1 + " and jobactivitydesc like '%" + this.activitydesc + "%' ";
/*      */       } 
/*      */     }
/*      */     
/* 1923 */     if (!this.jobgroup.equals("") && !this.jobgroup.equals("0")) {
/* 1924 */       if (!bool) {
/* 1925 */         bool = true;
/* 1926 */         str1 = " where jobtitle in( select id from HrmJobTitles where jobactivityid in( select id from HrmJobActivities where jobgroupid = " + this.jobgroup + ")) ";
/*      */       } else {
/* 1928 */         str1 = str1 + " and jobtitle in( select id from HrmJobTitles where jobactivityid in( select id from HrmJobActivities where jobgroupid = " + this.jobgroup + ")) ";
/*      */       } 
/*      */     }
/*      */     
/* 1932 */     if (!this.groupid.equals("") && !this.groupid.equals("0")) {
/* 1933 */       String[] arrayOfString = Util.TokenizerString2(this.groupid, ",");
/* 1934 */       int j = arrayOfString.length;
/* 1935 */       if (!bool) {
/* 1936 */         bool = true;
/* 1937 */         if (this.groupVaild.equals("1")) {
/* 1938 */           str1 = " where id in ( select  userid from HrmGroupMembers where groupid in(" + this.groupid + ") group by userid having COUNT(userid)=" + j + "  ) ";
/*      */         } else {
/* 1940 */           str1 = " where id in( select userid from HrmGroupMembers where groupid in ( " + this.groupid + " ) ) ";
/*      */         }
/*      */       
/* 1943 */       } else if (this.groupVaild.equals("1")) {
/* 1944 */         str1 = str1 + " and id in( select userid from HrmGroupMembers where groupid in(" + this.groupid + ") group by userid having COUNT(userid)=" + j + " ) ";
/*      */       } else {
/* 1946 */         str1 = str1 + " and id in( select userid from HrmGroupMembers where groupid in ( " + this.groupid + " ) ) ";
/*      */       } 
/*      */     } 
/*      */ 
/*      */     
/* 1951 */     if (!this.jobactivity.equals("") && !this.jobactivity.equals("0")) {
/* 1952 */       if (!bool) {
/* 1953 */         bool = true;
/* 1954 */         str1 = " where jobtitle  in(select id from HrmJobTitles where jobactivityid = " + this.jobactivity + ") ";
/*      */       } else {
/* 1956 */         str1 = str1 + " and jobtitle  in(select id from HrmJobTitles where jobactivityid = " + this.jobactivity + ") ";
/*      */       } 
/*      */     }
/*      */     
/* 1960 */     if (!this.costcenter.equals("") && !this.costcenter.equals("0")) {
/* 1961 */       if (!bool) {
/* 1962 */         bool = true;
/* 1963 */         str1 = " where costcenterid = " + this.costcenter;
/*      */       } else {
/* 1965 */         str1 = str1 + " and costcenterid = " + this.costcenter;
/*      */       } 
/*      */     }
/*      */     
/* 1969 */     if (!this.resourcetype.equals("")) {
/* 1970 */       if (!bool) {
/* 1971 */         bool = true;
/* 1972 */         str1 = " where resourcetype = '" + this.resourcetype + "' ";
/*      */       } else {
/* 1974 */         str1 = str1 + " and resourcetype  = '" + this.resourcetype + "' ";
/*      */       } 
/*      */     }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/* 2006 */     if (!this.subcompany1.equals("0") && !this.subcompany1.equals("")) {
/* 2007 */       if (this.subcompany1.startsWith("-")) {
/* 2008 */         if (!bool) {
/* 2009 */           bool = true;
/* 2010 */           str1 = " where EXISTS (SELECT * FROM hrmresourcevirtual WHERE hrmresource.id = resourceid AND hrmresourcevirtual.subcompanyid IN ( " + this.subcompany1 + " ) )";
/*      */         } else {
/* 2012 */           str1 = str1 + " and EXISTS (SELECT * FROM hrmresourcevirtual WHERE hrmresource.id = resourceid AND hrmresourcevirtual.subcompanyid IN ( " + this.subcompany1 + " ) )";
/*      */         }
/*      */       
/* 2015 */       } else if (!bool) {
/* 2016 */         bool = true;
/* 2017 */         if ("hrmResource".equals(this.searchFrom)) {
/*      */           
/* 2019 */           str1 = " where subcompanyid1 in (" + this.subcompany1 + ")";
/*      */         } else {
/* 2021 */           str1 = " where subcompanyid1 in (" + this.subcompany1 + ")";
/*      */         }
/*      */       
/* 2024 */       } else if ("hrmResource".equals(this.searchFrom)) {
/*      */         
/* 2026 */         str1 = str1 + " and subcompanyid1 in (" + this.subcompany1 + ")";
/*      */       } else {
/* 2028 */         str1 = str1 + " and subcompanyid1 in (" + this.subcompany1 + ")";
/*      */       } 
/*      */     }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/* 2063 */     if (!this.department.equals("") && !this.department.equals("0")) {
/* 2064 */       if (this.department.startsWith("-")) {
/* 2065 */         if (!bool) {
/* 2066 */           bool = true;
/* 2067 */           str1 = " where EXISTS (SELECT * FROM hrmresourcevirtual WHERE hrmresource.id = resourceid AND hrmresourcevirtual.departmentid IN ( " + this.department + " ) )";
/*      */         } else {
/* 2069 */           str1 = str1 + " and EXISTS (SELECT * FROM hrmresourcevirtual WHERE hrmresource.id = resourceid AND hrmresourcevirtual.departmentid IN ( " + this.department + " ) )";
/*      */         }
/*      */       
/* 2072 */       } else if (!bool) {
/* 2073 */         bool = true;
/* 2074 */         if (this.department.indexOf(",") > -1) {
/* 2075 */           str1 = " where departmentid in(" + this.department + ")";
/*      */         } else {
/* 2077 */           str1 = " where departmentid = " + this.department;
/*      */         } 
/* 2079 */       } else if (this.department.indexOf(",") > -1) {
/* 2080 */         str1 = str1 + " and departmentid in(" + this.department + ")";
/*      */       } else {
/* 2082 */         str1 = str1 + " and departmentid = " + this.department;
/*      */       } 
/*      */     }
/*      */ 
/*      */     
/* 2087 */     if (!this.location.equals("") && !this.location.equals("0")) {
/* 2088 */       if (!bool) {
/* 2089 */         bool = true;
/* 2090 */         str1 = " where locationid = " + this.location;
/*      */       } else {
/* 2092 */         str1 = str1 + " and locationid = " + this.location;
/*      */       } 
/*      */     }
/*      */     
/* 2096 */     if (!this.manager.equals("") && !this.manager.equals("0")) {
/* 2097 */       if (!bool) {
/* 2098 */         bool = true;
/* 2099 */         str1 = " where managerid = " + this.manager;
/*      */       } else {
/* 2101 */         str1 = str1 + " and managerid = " + this.manager;
/*      */       } 
/*      */     }
/*      */     
/* 2105 */     if (!this.assistant.equals("") && !this.assistant.equals("0")) {
/* 2106 */       if (!bool) {
/* 2107 */         bool = true;
/* 2108 */         str1 = " where assistantid = " + this.assistant;
/*      */       } else {
/* 2110 */         str1 = str1 + " and assistantid = " + this.assistant;
/*      */       } 
/*      */     }
/*      */     
/* 2114 */     if (!this.roles.equals("") && !this.roles.equals("0")) {
/* 2115 */       HrmCommonServiceImpl hrmCommonServiceImpl = new HrmCommonServiceImpl();
/* 2116 */       String str = hrmCommonServiceImpl.getHrmRoleMemeberSqlByRoleIds(this.roles);
/* 2117 */       str = " select resourceid from (" + str + ") ";
/* 2118 */       if (!bool) {
/* 2119 */         bool = true;
/* 2120 */         str1 = " where HrmResource.id in( " + str + ")";
/*      */       } else {
/* 2122 */         str1 = str1 + " and HrmResource.id in( " + str + ")";
/*      */       } 
/*      */     } 
/*      */     
/* 2126 */     if (!this.seclevel.equals("")) {
/* 2127 */       if (!bool) {
/* 2128 */         bool = true;
/* 2129 */         str1 = " where seclevel >= " + this.seclevel;
/*      */       } else {
/* 2131 */         str1 = str1 + " and seclevel >= " + this.seclevel;
/*      */       } 
/*      */     }
/*      */     
/* 2135 */     if (!this.seclevelTo.equals("")) {
/* 2136 */       if (!bool) {
/* 2137 */         bool = true;
/* 2138 */         str1 = " where seclevel <= " + this.seclevelTo;
/*      */       } else {
/* 2140 */         str1 = str1 + " and seclevel <= " + this.seclevelTo;
/*      */       } 
/*      */     }
/*      */     
/* 2144 */     if (!this.joblevel.equals("")) {
/* 2145 */       if (!bool) {
/* 2146 */         bool = true;
/* 2147 */         str1 = " where joblevel >= " + this.joblevel;
/*      */       } else {
/* 2149 */         str1 = str1 + " and joblevel >= " + this.joblevel;
/*      */       } 
/*      */     }
/*      */     
/* 2153 */     if (!this.joblevelTo.equals("")) {
/* 2154 */       if (!bool) {
/* 2155 */         bool = true;
/* 2156 */         str1 = " where joblevel <= " + this.joblevelTo;
/*      */       } else {
/* 2158 */         str1 = str1 + " and joblevel <= " + this.joblevelTo;
/*      */       } 
/*      */     }
/*      */     
/* 2162 */     if (!this.workroom.equals("")) {
/* 2163 */       if (!bool) {
/* 2164 */         bool = true;
/* 2165 */         str1 = " where workroom like '%" + this.workroom + "%'";
/*      */       } else {
/* 2167 */         str1 = str1 + " and workroom like '%" + this.workroom + "%'";
/*      */       } 
/*      */     }
/*      */     
/* 2171 */     if (!this.telephone.equals("")) {
/* 2172 */       if (!bool) {
/* 2173 */         bool = true;
/* 2174 */         str1 = " where telephone like '%" + this.telephone + "%'";
/*      */       } else {
/* 2176 */         str1 = str1 + " and telephone like '%" + this.telephone + "%'";
/*      */       } 
/*      */     }
/*      */     
/* 2180 */     if (!this.startdate.equals("")) {
/* 2181 */       if (!bool) {
/* 2182 */         bool = true;
/* 2183 */         str1 = " where startdate >= '" + this.startdate + "'";
/*      */       } else {
/* 2185 */         str1 = str1 + " and startdate >= '" + this.startdate + "'";
/*      */       } 
/*      */     }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/* 2198 */     if (!this.startdateTo.equals("")) {
/* 2199 */       if (!this.isoracle) {
/* 2200 */         if (!bool) {
/* 2201 */           bool = true;
/* 2202 */           str1 = " where startdate <= '" + this.startdateTo + "' and startdate<>''";
/*      */         } else {
/* 2204 */           str1 = str1 + " and startdate <= '" + this.startdateTo + "' and startdate<>''";
/*      */         }
/*      */       
/*      */       }
/* 2208 */       else if (!bool) {
/* 2209 */         bool = true;
/* 2210 */         str1 = " where startdate <= '" + this.startdateTo + "' and startdate is not null";
/*      */       } else {
/* 2212 */         str1 = str1 + " and startdate <= '" + this.startdateTo + "' and startdate is not null";
/*      */       } 
/*      */     }
/*      */ 
/*      */ 
/*      */     
/* 2218 */     if (!this.enddate.equals("")) {
/* 2219 */       if (!bool) {
/* 2220 */         bool = true;
/* 2221 */         str1 = " where enddate >= '" + this.enddate + "'";
/*      */       } else {
/* 2223 */         str1 = str1 + " and enddate >= '" + this.enddate + "'";
/*      */       } 
/*      */     }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/* 2236 */     if (!this.enddateTo.equals("")) {
/* 2237 */       if (!this.isoracle) {
/* 2238 */         if (!bool) {
/* 2239 */           bool = true;
/* 2240 */           str1 = " where enddate <= '" + this.enddateTo + "' and enddate<>''";
/*      */         } else {
/* 2242 */           str1 = str1 + " and enddate <= '" + this.enddateTo + "' and enddate<>''";
/*      */         }
/*      */       
/*      */       }
/* 2246 */       else if (!bool) {
/* 2247 */         bool = true;
/* 2248 */         str1 = " where enddate <= '" + this.enddateTo + "' and enddate is not null";
/*      */       } else {
/* 2250 */         str1 = str1 + " and enddate <= '" + this.enddateTo + "' and enddate is not null";
/*      */       } 
/*      */     }
/*      */ 
/*      */ 
/*      */     
/* 2256 */     if (!this.contractdate.equals("")) {
/* 2257 */       if (!bool) {
/* 2258 */         bool = true;
/* 2259 */         str1 = " where probationenddate >= '" + this.contractdate + "'";
/*      */       } else {
/* 2261 */         str1 = str1 + " and probationenddate >= '" + this.contractdate + "'";
/*      */       } 
/*      */     }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/* 2274 */     if (!this.contractdateTo.equals("")) {
/* 2275 */       if (!this.isoracle) {
/* 2276 */         if (!bool) {
/* 2277 */           bool = true;
/* 2278 */           str1 = " where probationenddate <= '" + this.contractdateTo + "' and probationenddate<>''";
/*      */         } else {
/* 2280 */           str1 = str1 + " and probationenddate <= '" + this.contractdateTo + "' and probationenddate<>''";
/*      */         }
/*      */       
/*      */       }
/* 2284 */       else if (!bool) {
/* 2285 */         bool = true;
/* 2286 */         str1 = " where probationenddate <= '" + this.contractdateTo + "' and probationenddate is not null";
/*      */       } else {
/* 2288 */         str1 = str1 + " and probationenddate <= '" + this.contractdateTo + "' and probationenddate is not null";
/*      */       } 
/*      */     }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/* 2338 */     if (!this.birthdaydate.equals("")) {
/* 2339 */       if (!this.isoracle) {
/* 2340 */         if (!bool) {
/* 2341 */           bool = true;
/* 2342 */           str1 = " where birthday >= '" + this.birthdaydate + "' and birthday<>''";
/*      */         } else {
/* 2344 */           str1 = str1 + " and birthday >= '" + this.birthdaydate + "' and birthday<>''";
/*      */         }
/*      */       
/*      */       }
/* 2348 */       else if (!bool) {
/* 2349 */         bool = true;
/* 2350 */         str1 = " where birthday >= '" + this.birthdaydate + "' and birthday is not null";
/*      */       } else {
/* 2352 */         str1 = str1 + " and birthday >= '" + this.birthdaydate + "' and birthday is not null";
/*      */       } 
/*      */     }
/*      */ 
/*      */     
/* 2357 */     if (!this.birthdaydateTo.equals("")) {
/* 2358 */       if (!this.isoracle) {
/* 2359 */         if (!bool) {
/* 2360 */           bool = true;
/* 2361 */           str1 = " where birthday <= '" + this.birthdaydateTo + "' and birthday<>''";
/*      */         } else {
/* 2363 */           str1 = str1 + " and birthday <= '" + this.birthdaydateTo + "' and birthday<>''";
/*      */         }
/*      */       
/*      */       }
/* 2367 */       else if (!bool) {
/* 2368 */         bool = true;
/* 2369 */         str1 = " where birthday <= '" + this.birthdaydateTo + "' and birthday is not null";
/*      */       } else {
/* 2371 */         str1 = str1 + " and birthday <= '" + this.birthdaydateTo + "' and birthday is not null";
/*      */       } 
/*      */     }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/* 2385 */     if (!str3.equals("")) {
/* 2386 */       if (!this.isoracle) {
/* 2387 */         if (!bool) {
/* 2388 */           bool = true;
/* 2389 */           str1 = " where birthday <= '" + str3 + "' and birthday<>''";
/*      */         } else {
/* 2391 */           str1 = str1 + " and birthday <= '" + str3 + "' and birthday<>''";
/*      */         }
/*      */       
/*      */       }
/* 2395 */       else if (!bool) {
/* 2396 */         bool = true;
/* 2397 */         str1 = " where birthday <= '" + str3 + "' and birthday is not null";
/*      */       } else {
/* 2399 */         str1 = str1 + " and birthday <= '" + str3 + "' and birthday is not null";
/*      */       } 
/*      */     }
/*      */ 
/*      */     
/* 2404 */     if (!str4.equals("")) {
/* 2405 */       if (!bool) {
/* 2406 */         bool = true;
/* 2407 */         str1 = " where birthday >= '" + str4 + "' ";
/*      */       } else {
/* 2409 */         str1 = str1 + " and birthday >= '" + str4 + "' ";
/*      */       } 
/*      */     }
/*      */     
/* 2413 */     if (!this.sex.equals("")) {
/* 2414 */       if (!bool) {
/* 2415 */         bool = true;
/* 2416 */         str1 = " where sex = '" + this.sex + "'";
/*      */       } else {
/* 2418 */         str1 = str1 + " and sex = '" + this.sex + "'";
/*      */       } 
/*      */     }
/*      */     
/* 2422 */     if (this.accounttype != -1) {
/* 2423 */       if (!bool) {
/* 2424 */         bool = true;
/* 2425 */         if (this.accounttype == 0) {
/* 2426 */           str1 = " where (accounttype=0 OR accounttype IS NULL)";
/*      */         } else {
/* 2428 */           str1 = " where accounttype=1";
/*      */         } 
/* 2430 */       } else if (this.accounttype == 0) {
/* 2431 */         str1 = str1 + " and (accounttype=0 OR accounttype IS NULL)";
/*      */       } else {
/* 2433 */         str1 = str1 + " and accounttype=1";
/*      */       } 
/*      */     }
/*      */     
/* 2437 */     if (!this.resourceidfrom.equals("") && !this.resourceidfrom.equals("0")) {
/* 2438 */       if (!bool) {
/* 2439 */         bool = true;
/* 2440 */         str1 = " where id >= " + this.resourceidfrom;
/*      */       } else {
/* 2442 */         str1 = str1 + " and id >= " + this.resourceidfrom;
/*      */       } 
/*      */     }
/*      */     
/* 2446 */     if (!this.resourceidto.equals("") && !this.resourceidto.equals("0")) {
/* 2447 */       if (!bool) {
/* 2448 */         bool = true;
/* 2449 */         str1 = " where id <= " + this.resourceidto;
/*      */       } else {
/* 2451 */         str1 = str1 + " and id <= " + this.resourceidto;
/*      */       } 
/*      */     }
/*      */     
/* 2455 */     if (!this.workcode.equals("")) {
/* 2456 */       if (!bool) {
/* 2457 */         bool = true;
/* 2458 */         str1 = " where workcode like '%" + this.workcode + "%' ";
/*      */       } else {
/* 2460 */         str1 = str1 + " and workcode like '%" + this.workcode + "%' ";
/*      */       } 
/*      */     }
/*      */     
/* 2464 */     if (!this.jobcall.equals("")) {
/* 2465 */       if (!bool) {
/* 2466 */         bool = true;
/* 2467 */         str1 = " where jobcall = " + this.jobcall + " ";
/*      */       } else {
/* 2469 */         str1 = str1 + " and jobcall = " + this.jobcall + " ";
/*      */       } 
/*      */     }
/*      */     
/* 2473 */     if (!this.mobile.equals("")) {
/* 2474 */       if (!bool) {
/* 2475 */         bool = true;
/* 2476 */         str1 = " where mobile like '%" + this.mobile + "%' ";
/*      */       } else {
/* 2478 */         str1 = str1 + " and mobile like '%" + this.mobile + "%' ";
/*      */       } 
/*      */     }
/*      */     
/* 2482 */     if (!this.mobilecall.equals("")) {
/* 2483 */       if (!bool) {
/* 2484 */         bool = true;
/* 2485 */         str1 = " where mobilecall like '%" + this.mobilecall + "%' ";
/*      */       } else {
/* 2487 */         str1 = str1 + " and mobilecall like '%" + this.mobilecall + "%' ";
/*      */       } 
/*      */     }
/*      */     
/* 2491 */     if (!this.fax.equals("")) {
/* 2492 */       if (!bool) {
/* 2493 */         bool = true;
/* 2494 */         str1 = " where fax like '%" + this.fax + "%' ";
/*      */       } else {
/* 2496 */         str1 = str1 + " and fax like '%" + this.fax + "%' ";
/*      */       } 
/*      */     }
/*      */     
/* 2500 */     if (!this.email.equals("")) {
/* 2501 */       if (!bool) {
/* 2502 */         bool = true;
/* 2503 */         str1 = " where email like '%" + this.email + "%' ";
/*      */       } else {
/* 2505 */         str1 = str1 + " and email like '%" + this.email + "%' ";
/*      */       } 
/*      */     }
/*      */     
/* 2509 */     if (!this.folk.equals("")) {
/* 2510 */       if (!bool) {
/* 2511 */         bool = true;
/* 2512 */         str1 = " where folk like '%" + this.folk + "%' ";
/*      */       } else {
/* 2514 */         str1 = str1 + " and folk like '%" + this.folk + "%' ";
/*      */       } 
/*      */     }
/*      */     
/* 2518 */     if (!this.nativeplace.equals("")) {
/* 2519 */       if (!bool) {
/* 2520 */         bool = true;
/* 2521 */         str1 = " where nativeplace like '%" + this.nativeplace + "%' ";
/*      */       } else {
/* 2523 */         str1 = str1 + " and nativeplace like '%" + this.nativeplace + "%' ";
/*      */       } 
/*      */     }
/*      */     
/* 2527 */     if (!this.regresidentplace.equals("")) {
/* 2528 */       if (!bool) {
/* 2529 */         bool = true;
/* 2530 */         str1 = " where regresidentplace like '%" + this.regresidentplace + "%' ";
/*      */       } else {
/* 2532 */         str1 = str1 + " and regresidentplace like '%" + this.regresidentplace + "%' ";
/*      */       } 
/*      */     }
/*      */     
/* 2536 */     if (!this.maritalstatus.equals("")) {
/* 2537 */       if (!bool) {
/* 2538 */         bool = true;
/* 2539 */         str1 = " where maritalstatus = '" + this.maritalstatus + "' ";
/*      */       } else {
/* 2541 */         str1 = str1 + " and maritalstatus = '" + this.maritalstatus + "' ";
/*      */       } 
/*      */     }
/*      */     
/* 2545 */     if (!this.certificatenum.equals("")) {
/* 2546 */       if (!bool) {
/* 2547 */         bool = true;
/* 2548 */         str1 = " where certificatenum like '%" + this.certificatenum + "%' ";
/*      */       } else {
/* 2550 */         str1 = str1 + " and certificatenum like '%" + this.certificatenum + "%' ";
/*      */       } 
/*      */     }
/*      */     
/* 2554 */     if (!this.tempresidentnumber.equals("")) {
/* 2555 */       if (!bool) {
/* 2556 */         bool = true;
/* 2557 */         str1 = " where tempresidentnumber like '%" + this.tempresidentnumber + "%' ";
/*      */       } else {
/* 2559 */         str1 = str1 + " and tempresidentnumber like '%" + this.tempresidentnumber + "%' ";
/*      */       } 
/*      */     }
/*      */     
/* 2563 */     if (!this.residentplace.equals("")) {
/* 2564 */       if (!bool) {
/* 2565 */         bool = true;
/* 2566 */         str1 = " where residentplace like '%" + this.residentplace + "%' ";
/*      */       } else {
/* 2568 */         str1 = str1 + " and residentplace like '%" + this.residentplace + "%' ";
/*      */       } 
/*      */     }
/*      */     
/* 2572 */     if (!this.homeaddress.equals("")) {
/* 2573 */       if (!bool) {
/* 2574 */         bool = true;
/* 2575 */         str1 = " where homeaddress like '%" + this.homeaddress + "%' ";
/*      */       } else {
/* 2577 */         str1 = str1 + " and homeaddress like '%" + this.homeaddress + "%' ";
/*      */       } 
/*      */     }
/*      */     
/* 2581 */     if (!this.healthinfo.equals("")) {
/* 2582 */       if (!bool) {
/* 2583 */         bool = true;
/* 2584 */         str1 = " where healthinfo = '" + this.healthinfo + "' ";
/*      */       } else {
/* 2586 */         str1 = str1 + " and healthinfo = '" + this.healthinfo + "' ";
/*      */       } 
/*      */     }
/*      */     
/* 2590 */     if (!this.heightfrom.equals("") && !this.heightfrom.equals("0")) {
/* 2591 */       if (!bool) {
/* 2592 */         bool = true;
/* 2593 */         str1 = " where height >= " + this.heightfrom;
/*      */       } else {
/* 2595 */         str1 = str1 + " and height >= " + this.heightfrom;
/*      */       } 
/*      */     }
/*      */     
/* 2599 */     if (!this.heightto.equals("") && !this.heightto.equals("0")) {
/* 2600 */       if (!bool) {
/* 2601 */         bool = true;
/* 2602 */         str1 = " where height <= " + this.heightto;
/*      */       } else {
/* 2604 */         str1 = str1 + " and height <= " + this.heightto;
/*      */       } 
/*      */     }
/*      */     
/* 2608 */     if (!this.weightfrom.equals("") && !this.weightfrom.equals("0")) {
/* 2609 */       if (!bool) {
/* 2610 */         bool = true;
/* 2611 */         str1 = " where weight >= " + this.weightfrom;
/*      */       } else {
/* 2613 */         str1 = str1 + " and weight >= " + this.weightfrom;
/*      */       } 
/*      */     }
/*      */     
/* 2617 */     if (!this.weightto.equals("") && !this.weightto.equals("0")) {
/* 2618 */       if (!bool) {
/* 2619 */         bool = true;
/* 2620 */         str1 = " where weight <= " + this.weightto;
/*      */       } else {
/* 2622 */         str1 = str1 + " and weight <= " + this.weightto;
/*      */       } 
/*      */     }
/*      */     
/* 2626 */     if (!this.educationlevel.equals("")) {
/* 2627 */       if (!bool) {
/* 2628 */         bool = true;
/* 2629 */         str1 = " where educationlevel >= " + this.educationlevel;
/*      */       } else {
/* 2631 */         str1 = str1 + " and educationlevel >= " + this.educationlevel;
/*      */       } 
/*      */     }
/*      */     
/* 2635 */     if (!this.educationlevelTo.equals("")) {
/* 2636 */       if (!bool) {
/* 2637 */         bool = true;
/* 2638 */         str1 = " where educationlevel <= " + this.educationlevelTo;
/*      */       } else {
/* 2640 */         str1 = str1 + " and educationlevel <= " + this.educationlevelTo;
/*      */       } 
/*      */     }
/*      */     
/* 2644 */     if (!this.degree.equals("")) {
/* 2645 */       if (!bool) {
/* 2646 */         bool = true;
/* 2647 */         str1 = " where degree like '%" + this.degree + "%' ";
/*      */       } else {
/* 2649 */         str1 = str1 + " and degree like '%" + this.degree + "%' ";
/*      */       } 
/*      */     }
/*      */     
/* 2653 */     if (!this.usekind.equals("")) {
/* 2654 */       if (!bool) {
/* 2655 */         bool = true;
/* 2656 */         str1 = " where usekind = " + this.usekind + " ";
/*      */       } else {
/* 2658 */         str1 = str1 + " and usekind = " + this.usekind + " ";
/*      */       } 
/*      */     }
/*      */     
/* 2662 */     if (!this.policy.equals("")) {
/* 2663 */       if (!bool) {
/* 2664 */         bool = true;
/* 2665 */         str1 = " where policy like '%" + this.policy + "%' ";
/*      */       } else {
/* 2667 */         str1 = str1 + " and policy like '%" + this.policy + "%' ";
/*      */       } 
/*      */     }
/*      */ 
/*      */     
/* 2672 */     if (!this.bememberdatefrom.equals("")) {
/* 2673 */       if (!bool) {
/* 2674 */         bool = true;
/* 2675 */         str1 = " where bememberdate >= '" + this.bememberdatefrom + "'";
/*      */       } else {
/* 2677 */         str1 = str1 + " and bememberdate >= '" + this.bememberdatefrom + "'";
/*      */       } 
/*      */     }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/* 2690 */     if (!this.bememberdateto.equals("")) {
/* 2691 */       if (!this.isoracle) {
/* 2692 */         if (!bool) {
/* 2693 */           bool = true;
/* 2694 */           str1 = " where bememberdate <= '" + this.bememberdateto + "' and bememberdate<>''";
/*      */         } else {
/* 2696 */           str1 = str1 + " and bememberdate <= '" + this.bememberdateto + "' and bememberdate<>''";
/*      */         }
/*      */       
/*      */       }
/* 2700 */       else if (!bool) {
/* 2701 */         bool = true;
/* 2702 */         str1 = " where bememberdate <= '" + this.bememberdateto + "' and bememberdate is not null";
/*      */       } else {
/* 2704 */         str1 = str1 + " and bememberdate <= '" + this.bememberdateto + "' and bememberdate is not null";
/*      */       } 
/*      */     }
/*      */     
/* 2708 */     if (!this.dff01name.equals("")) {
/* 2709 */       if (!bool) {
/* 2710 */         bool = true;
/* 2711 */         str1 = " where datefield1 >= '" + this.dff01name + "'";
/*      */       } else {
/* 2713 */         str1 = str1 + " and datefield1 >= '" + this.dff01name + "'";
/*      */       } 
/*      */     }
/* 2716 */     if (!this.dff02name.equals("")) {
/* 2717 */       if (!bool) {
/* 2718 */         bool = true;
/* 2719 */         str1 = " where datefield2 >= '" + this.dff02name + "'";
/*      */       } else {
/* 2721 */         str1 = str1 + " and datefield2 >= '" + this.dff02name + "'";
/*      */       } 
/*      */     }
/* 2724 */     if (!this.dff03name.equals("")) {
/* 2725 */       if (!bool) {
/* 2726 */         bool = true;
/* 2727 */         str1 = " where datefield3 >= '" + this.dff03name + "'";
/*      */       } else {
/* 2729 */         str1 = str1 + " and datefield3 >= '" + this.dff03name + "'";
/*      */       } 
/*      */     }
/* 2732 */     if (!this.dff04name.equals("")) {
/* 2733 */       if (!bool) {
/* 2734 */         bool = true;
/* 2735 */         str1 = " where datefield4 >= '" + this.dff04name + "'";
/*      */       } else {
/* 2737 */         str1 = str1 + " and datefield4 >= '" + this.dff04name + "'";
/*      */       } 
/*      */     }
/* 2740 */     if (!this.dff05name.equals("")) {
/* 2741 */       if (!bool) {
/* 2742 */         bool = true;
/* 2743 */         str1 = " where datefield5 >= '" + this.dff05name + "'";
/*      */       } else {
/* 2745 */         str1 = str1 + " and datefield5 >= '" + this.dff05name + "'";
/*      */       } 
/*      */     }
/* 2748 */     if (!this.dff01nameto.equals("")) {
/* 2749 */       if (!bool) {
/* 2750 */         bool = true;
/* 2751 */         str1 = " where datefield1 <= '" + this.dff01nameto + "'";
/*      */       } else {
/* 2753 */         str1 = str1 + " and datefield1 <= '" + this.dff01nameto + "'";
/*      */       } 
/*      */     }
/* 2756 */     if (!this.dff02nameto.equals("")) {
/* 2757 */       if (!bool) {
/* 2758 */         bool = true;
/* 2759 */         str1 = " where datefield2 <= '" + this.dff02nameto + "'";
/*      */       } else {
/* 2761 */         str1 = str1 + " and datefield2 <= '" + this.dff02nameto + "'";
/*      */       } 
/*      */     }
/* 2764 */     if (!this.dff03nameto.equals("")) {
/* 2765 */       if (!bool) {
/* 2766 */         bool = true;
/* 2767 */         str1 = " where datefield3 <= '" + this.dff03nameto + "'";
/*      */       } else {
/* 2769 */         str1 = str1 + " and datefield3 <= '" + this.dff03nameto + "'";
/*      */       } 
/*      */     }
/* 2772 */     if (!this.dff04nameto.equals("")) {
/* 2773 */       if (!bool) {
/* 2774 */         bool = true;
/* 2775 */         str1 = " where datefield4 <= '" + this.dff04nameto + "'";
/*      */       } else {
/* 2777 */         str1 = str1 + " and datefield4 <= '" + this.dff04nameto + "'";
/*      */       } 
/*      */     }
/* 2780 */     if (!this.dff05nameto.equals("")) {
/* 2781 */       if (!bool) {
/* 2782 */         bool = true;
/* 2783 */         str1 = " where datefield5 <= '" + this.dff05nameto + "'";
/*      */       } else {
/* 2785 */         str1 = str1 + " and datefield5 <= '" + this.dff05nameto + "'";
/*      */       } 
/*      */     }
/* 2788 */     if (!this.nff01name.equals("")) {
/* 2789 */       if (!bool) {
/* 2790 */         bool = true;
/* 2791 */         str1 = " where numberfield1 >= " + this.nff01name;
/*      */       } else {
/* 2793 */         str1 = str1 + " and numberfield1 >= " + this.nff01name;
/*      */       } 
/*      */     }
/*      */     
/* 2797 */     if (!this.nff01nameto.equals("")) {
/* 2798 */       if (!bool) {
/* 2799 */         bool = true;
/* 2800 */         str1 = " where numberfield1 <= " + this.nff01nameto;
/*      */       } else {
/* 2802 */         str1 = str1 + " and numberfield1 <= " + this.nff01nameto;
/*      */       } 
/*      */     }
/* 2805 */     if (!this.nff02name.equals("")) {
/* 2806 */       if (!bool) {
/* 2807 */         bool = true;
/* 2808 */         str1 = " where numberfield2 >= " + this.nff02name;
/*      */       } else {
/* 2810 */         str1 = str1 + " and numberfield2 >= " + this.nff02name;
/*      */       } 
/*      */     }
/*      */     
/* 2814 */     if (!this.nff02nameto.equals("")) {
/* 2815 */       if (!bool) {
/* 2816 */         bool = true;
/* 2817 */         str1 = " where numberfield2 <= " + this.nff02nameto;
/*      */       } else {
/* 2819 */         str1 = str1 + " and numberfield2 <= " + this.nff02nameto;
/*      */       } 
/*      */     }
/* 2822 */     if (!this.nff03name.equals("")) {
/* 2823 */       if (!bool) {
/* 2824 */         bool = true;
/* 2825 */         str1 = " where numberfield3 >= " + this.nff03name;
/*      */       } else {
/* 2827 */         str1 = str1 + " and numberfield3 >= " + this.nff03name;
/*      */       } 
/*      */     }
/*      */     
/* 2831 */     if (!this.nff03nameto.equals("")) {
/* 2832 */       if (!bool) {
/* 2833 */         bool = true;
/* 2834 */         str1 = " where numberfield3 <= " + this.nff03nameto;
/*      */       } else {
/* 2836 */         str1 = str1 + " and numberfield3 <= " + this.nff03nameto;
/*      */       } 
/*      */     }
/* 2839 */     if (!this.nff04name.equals("")) {
/* 2840 */       if (!bool) {
/* 2841 */         bool = true;
/* 2842 */         str1 = " where numberfield4 >= " + this.nff04name;
/*      */       } else {
/* 2844 */         str1 = str1 + " and numberfield4 >= " + this.nff04name;
/*      */       } 
/*      */     }
/*      */     
/* 2848 */     if (!this.nff04nameto.equals("")) {
/* 2849 */       if (!bool) {
/* 2850 */         bool = true;
/* 2851 */         str1 = " where numberfield4 <= " + this.nff04nameto;
/*      */       } else {
/* 2853 */         str1 = str1 + " and numberfield4 <= " + this.nff04nameto;
/*      */       } 
/*      */     }
/* 2856 */     if (!this.nff05name.equals("")) {
/* 2857 */       if (!bool) {
/* 2858 */         bool = true;
/* 2859 */         str1 = " where numberfield5 >= " + this.nff05name;
/*      */       } else {
/* 2861 */         str1 = str1 + " and numberfield5 >= " + this.nff05name;
/*      */       } 
/*      */     }
/*      */     
/* 2865 */     if (!this.nff05nameto.equals("")) {
/* 2866 */       if (!bool) {
/* 2867 */         bool = true;
/* 2868 */         str1 = " where numberfield5 <= " + this.nff05nameto;
/*      */       } else {
/* 2870 */         str1 = str1 + " and numberfield5 <= " + this.nff05nameto;
/*      */       } 
/*      */     }
/* 2873 */     if (!this.tff01name.equals("")) {
/* 2874 */       if (!bool) {
/* 2875 */         bool = true;
/* 2876 */         str1 = " where textfield1 like '%" + this.tff01name + "%' ";
/*      */       } else {
/* 2878 */         str1 = str1 + " and textfield1 like '%" + this.tff01name + "%' ";
/*      */       } 
/*      */     }
/* 2881 */     if (!this.tff02name.equals("")) {
/* 2882 */       if (!bool) {
/* 2883 */         bool = true;
/* 2884 */         str1 = " where textfield2 like '%" + this.tff02name + "%' ";
/*      */       } else {
/* 2886 */         str1 = str1 + " and textfield2 like '%" + this.tff02name + "%' ";
/*      */       } 
/*      */     }
/* 2889 */     if (!this.tff03name.equals("")) {
/* 2890 */       if (!bool) {
/* 2891 */         bool = true;
/* 2892 */         str1 = " where textfield3 like '%" + this.tff03name + "%' ";
/*      */       } else {
/* 2894 */         str1 = str1 + " and textfield3 like '%" + this.tff03name + "%' ";
/*      */       } 
/*      */     }
/* 2897 */     if (!this.tff04name.equals("")) {
/* 2898 */       if (!bool) {
/* 2899 */         bool = true;
/* 2900 */         str1 = " where textfield4 like '%" + this.tff04name + "%' ";
/*      */       } else {
/* 2902 */         str1 = str1 + " and textfield4 like '%" + this.tff04name + "%' ";
/*      */       } 
/*      */     }
/* 2905 */     if (!this.tff05name.equals("")) {
/* 2906 */       if (!bool) {
/* 2907 */         bool = true;
/* 2908 */         str1 = " where textfield5 like '%" + this.tff05name + "%' ";
/*      */       } else {
/* 2910 */         str1 = str1 + " and textfield5 like '%" + this.tff05name + "%' ";
/*      */       } 
/*      */     }
/* 2913 */     if (!this.bff01name.equals("")) {
/* 2914 */       if (!bool) {
/* 2915 */         bool = true;
/* 2916 */         str1 = " where tinyintfield1 = " + this.bff01name + " ";
/*      */       } else {
/* 2918 */         str1 = str1 + " and tinyintfield1 = " + this.bff01name + " ";
/*      */       } 
/*      */     }
/* 2921 */     if (!this.bff02name.equals("")) {
/* 2922 */       if (!bool) {
/* 2923 */         bool = true;
/* 2924 */         str1 = " where tinyintfield2 = " + this.bff02name + "  ";
/*      */       } else {
/* 2926 */         str1 = str1 + " and tinyintfield2 = " + this.bff02name + "  ";
/*      */       } 
/*      */     }
/* 2929 */     if (!this.bff03name.equals("")) {
/* 2930 */       if (!bool) {
/* 2931 */         bool = true;
/* 2932 */         str1 = " where tinyintfield3 = " + this.bff03name + "  ";
/*      */       } else {
/* 2934 */         str1 = str1 + " and tinyintfield3 = " + this.bff03name + "  ";
/*      */       } 
/*      */     }
/* 2937 */     if (!this.bff04name.equals("")) {
/* 2938 */       if (!bool) {
/* 2939 */         bool = true;
/* 2940 */         str1 = " where tinyintfield4 = " + this.bff04name + "  ";
/*      */       } else {
/* 2942 */         str1 = str1 + " and tinyintfield4 = " + this.bff04name + "  ";
/*      */       } 
/*      */     }
/* 2945 */     if (!this.bff05name.equals("")) {
/* 2946 */       if (!bool) {
/* 2947 */         bool = true;
/* 2948 */         str1 = " where tinyintfield5 = " + this.bff05name + "  ";
/*      */       } else {
/* 2950 */         str1 = str1 + " and tinyintfield5 = " + this.bff05name + "  ";
/*      */       } 
/*      */     }
/*      */     
/* 2954 */     if (!this.bepartydatefrom.equals("")) {
/* 2955 */       if (!bool) {
/* 2956 */         bool = true;
/* 2957 */         str1 = " where bepartydate >= '" + this.bepartydatefrom + "'";
/*      */       } else {
/* 2959 */         str1 = str1 + " and bepartydate >= '" + this.bepartydatefrom + "'";
/*      */       } 
/*      */     }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/* 2972 */     if (!this.bepartydateto.equals("")) {
/* 2973 */       if (!this.isoracle) {
/* 2974 */         if (!bool) {
/* 2975 */           bool = true;
/* 2976 */           str1 = " where bepartydate <= '" + this.bepartydateto + "' and bepartydate <>''";
/*      */         } else {
/* 2978 */           str1 = str1 + " and bepartydate <= '" + this.bepartydateto + "' and bepartydate <>''";
/*      */         }
/*      */       
/*      */       }
/* 2982 */       else if (!bool) {
/* 2983 */         bool = true;
/* 2984 */         str1 = " where bepartydate <= '" + this.bepartydateto + "' and bepartydate is not null";
/*      */       } else {
/* 2986 */         str1 = str1 + " and bepartydate <= '" + this.bepartydateto + "' and bepartydate is not null";
/*      */       } 
/*      */     }
/*      */ 
/*      */     
/* 2991 */     if (!this.islabouunion.equals("")) {
/* 2992 */       if (!bool) {
/* 2993 */         bool = true;
/* 2994 */         str1 = " where islabouunion = '" + this.islabouunion + "' ";
/*      */       } else {
/* 2996 */         str1 = str1 + " and islabouunion = '" + this.islabouunion + "' ";
/*      */       } 
/*      */     }
/*      */     
/* 3000 */     if (!this.bankid1.equals("")) {
/* 3001 */       if (!bool) {
/* 3002 */         bool = true;
/* 3003 */         str1 = " where bankid1 = " + this.bankid1 + " ";
/*      */       } else {
/* 3005 */         str1 = str1 + " and bankid1 = " + this.bankid1 + " ";
/*      */       } 
/*      */     }
/*      */     
/* 3009 */     if (!this.accountid1.equals("")) {
/* 3010 */       if (!bool) {
/* 3011 */         bool = true;
/* 3012 */         str1 = " where accountid1 like '%" + this.accountid1 + "%' ";
/*      */       } else {
/* 3014 */         str1 = str1 + " and accountid1 like '%" + this.accountid1 + "%' ";
/*      */       } 
/*      */     }
/*      */     
/* 3018 */     if (!this.accumfundaccount.equals("")) {
/* 3019 */       if (!bool) {
/* 3020 */         bool = true;
/* 3021 */         str1 = " where accumfundaccount like '%" + this.accumfundaccount + "%' ";
/*      */       } else {
/* 3023 */         str1 = str1 + " and accumfundaccount like '%" + this.accumfundaccount + "%' ";
/*      */       } 
/*      */     }
/*      */     
/* 3027 */     if (!this.loginid.equals("")) {
/* 3028 */       if (!bool) {
/* 3029 */         bool = true;
/* 3030 */         str1 = " where loginid like '%" + this.loginid + "%' ";
/*      */       } else {
/* 3032 */         str1 = str1 + " and loginid like '%" + this.loginid + "%' ";
/*      */       } 
/*      */     }
/*      */     
/* 3036 */     if (!this.systemlanguage.equals("") && !this.systemlanguage.equals("0")) {
/* 3037 */       if (!bool) {
/* 3038 */         bool = true;
/* 3039 */         str1 = " where systemlanguage = " + this.systemlanguage + " ";
/*      */       } else {
/* 3041 */         str1 = str1 + " and systemlanguage = " + this.systemlanguage + " ";
/*      */       } 
/*      */     }
/*      */     
/* 3045 */     if (!this.status.equals("") && !this.status.equals("8") && !this.status.equals("9")) {
/* 3046 */       if (!bool) {
/* 3047 */         bool = true;
/* 3048 */         str1 = " where status = " + this.status + " ";
/*      */       } else {
/* 3050 */         str1 = str1 + " and status = " + this.status + " ";
/*      */       } 
/*      */     }
/*      */     
/* 3054 */     if (this.status.equals("8") || this.status.equals("")) {
/* 3055 */       if (!bool) {
/* 3056 */         bool = true;
/* 3057 */         str1 = " where (status = 0 or status = 1 or status = 2 or status = 3) ";
/*      */       } else {
/* 3059 */         str1 = str1 + " and (status = 0 or status = 1 or status = 2 or status = 3) ";
/*      */       } 
/*      */     }
/*      */ 
/*      */     
/* 3064 */     if (!this.resourcesql.equals("")) {
/* 3065 */       if (!bool) {
/* 3066 */         bool = true;
/* 3067 */         str1 = " where " + this.resourcesql + "";
/*      */       } else {
/* 3069 */         str1 = str1 + " and " + this.resourcesql + "";
/*      */       } 
/*      */     }
/*      */     
/* 3073 */     if (Util.getIntValue(this.virtualtype) < -1) {
/* 3074 */       if (!bool) {
/* 3075 */         bool = true;
/* 3076 */         str1 = " where EXISTS ( SELECT * FROM   hrmresourcevirtual WHERE  hrmresource.id = hrmresourcevirtual.resourceid AND hrmresourcevirtual.virtualtype = " + this.virtualtype + " )";
/*      */       } else {
/* 3078 */         str1 = str1 + " and EXISTS ( SELECT * FROM   hrmresourcevirtual WHERE  hrmresource.id = hrmresourcevirtual.resourceid AND hrmresourcevirtual.virtualtype = " + this.virtualtype + " )";
/*      */       } 
/*      */     }
/*      */     
/* 3082 */     if (this.customFieldBase.size() > 0) {
/* 3083 */       Set set = this.customFieldBase.keySet();
/* 3084 */       for (String str6 : set) {
/*      */         
/* 3086 */         String str7 = (String)this.customFieldBase.get(str6);
/* 3087 */         String str8 = "";
/* 3088 */         String str9 = "";
/* 3089 */         String str10 = "";
/* 3090 */         if (str7.equals(""))
/* 3091 */           continue;  if (!bool) {
/* 3092 */           bool = true;
/* 3093 */           if (str6.indexOf("start") > 0) {
/* 3094 */             String str = str6.substring(0, str6.indexOf("start"));
/* 3095 */             str1 = " where t0_field" + str + ">='" + str7 + "'"; continue;
/* 3096 */           }  if (str6.indexOf("end") > 0) {
/* 3097 */             String str = str6.substring(0, str6.indexOf("end"));
/* 3098 */             str1 = " where t0_field" + str + "<='" + str7 + "'"; continue;
/*      */           } 
/* 3100 */           RecordSet recordSet1 = new RecordSet();
/* 3101 */           recordSet1.executeSql("select fieldhtmltype, fielddbtype, type from cus_formdict where id = " + str6);
/* 3102 */           if (recordSet1.next()) {
/* 3103 */             str8 = recordSet1.getString("fieldhtmltype");
/* 3104 */             str9 = recordSet1.getString("fielddbtype");
/* 3105 */             str10 = recordSet1.getString("type");
/*      */           } 
/* 3107 */           if ("1".equals(str8) || "2".equals(str8) || "text".equals(str9)) {
/* 3108 */             str1 = " where t0_field" + str6 + " like '%" + str7 + "%'"; continue;
/* 3109 */           }  if (("3".equals(str8) && str9.toLowerCase().equals("text")) || "161".equals(str10) || "162".equals(str10)) {
/* 3110 */             str1 = " where 1=1 ";
/* 3111 */             str1 = str1 + " and ( 1=2 ";
/*      */             
/* 3113 */             String[] arrayOfString = str7.split(",");
/* 3114 */             for (String str : arrayOfString) {
/* 3115 */               if (str.length() != 0)
/* 3116 */                 if (this.isoracle) {
/* 3117 */                   str1 = str1 + " or ','||cast(t0_field" + str6 + " as VARCHAR2(4000))||',' like '%," + str + ",%'";
/* 3118 */                 } else if (DialectUtil.isMySql()) {
/* 3119 */                   IDbDialectSql iDbDialectSql = DbDialectFactory.get(recordSet1.getDBType());
/* 3120 */                   String str11 = iDbDialectSql.castToChar("t0_field" + str6, Integer.valueOf(4000));
/* 3121 */                   String str12 = "','";
/* 3122 */                   String str13 = iDbDialectSql.concatStr(str12, new String[] { str11, str12 });
/* 3123 */                   str1 = str1 + " or " + str13 + " like '%," + str + ",%'";
/*      */                 } else {
/* 3125 */                   str1 = str1 + " or ','+cast(t0_field" + str6 + " as VARCHAR(4000))+',' like '%," + str + ",%'";
/*      */                 }  
/* 3127 */             }  str1 = str1 + " ) "; continue;
/*      */           } 
/* 3129 */           str1 = " where t0_field" + str6 + "='" + str7 + "'";
/*      */           
/*      */           continue;
/*      */         } 
/* 3133 */         if (str6.indexOf("start") > 0) {
/* 3134 */           String str = str6.substring(0, str6.indexOf("start"));
/* 3135 */           str1 = str1 + " and t0_field" + str + ">='" + str7 + "'"; continue;
/* 3136 */         }  if (str6.indexOf("end") > 0) {
/* 3137 */           String str = str6.substring(0, str6.indexOf("end"));
/* 3138 */           str1 = str1 + " and t0_field" + str + "<='" + str7 + "'"; continue;
/*      */         } 
/* 3140 */         RecordSet recordSet = new RecordSet();
/* 3141 */         recordSet.executeSql("select fieldhtmltype, fielddbtype, type from cus_formdict where id = " + str6);
/* 3142 */         if (recordSet.next()) {
/* 3143 */           str8 = recordSet.getString("fieldhtmltype");
/* 3144 */           str9 = recordSet.getString("fielddbtype");
/* 3145 */           str10 = recordSet.getString("type");
/*      */         } 
/* 3147 */         if ("1".equals(str8) || "2".equals(str8) || "text".equals(str9)) {
/* 3148 */           str1 = str1 + " and t0_field" + str6 + " like '%" + str7 + "%'"; continue;
/* 3149 */         }  if (("3".equals(str8) && str9.toLowerCase().equals("text")) || "161".equals(str10) || "162".equals(str10)) {
/* 3150 */           str1 = str1 + " and ( 1=2 ";
/*      */           
/* 3152 */           String[] arrayOfString = str7.split(",");
/* 3153 */           for (String str : arrayOfString) {
/* 3154 */             if (str.length() != 0)
/* 3155 */               if (this.isoracle) {
/* 3156 */                 str1 = str1 + " or ','||cast(t0_field" + str6 + " as VARCHAR2(4000))||',' like '%," + str + ",%'";
/*      */               } else {
/* 3158 */                 str1 = str1 + " or ','+cast(t0_field" + str6 + " as VARCHAR(4000))+',' like '%," + str + ",%'";
/*      */               }  
/* 3160 */           }  str1 = str1 + " ) "; continue;
/*      */         } 
/* 3162 */         str1 = str1 + " and t0_field" + str6 + "='" + str7 + "'";
/*      */       } 
/*      */     } 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/* 3170 */     if (this.customFieldPersonal.size() > 0) {
/* 3171 */       Set set = this.customFieldPersonal.keySet();
/* 3172 */       for (String str6 : set) {
/*      */         
/* 3174 */         String str7 = (String)this.customFieldPersonal.get(str6);
/* 3175 */         String str8 = "";
/* 3176 */         String str9 = "";
/* 3177 */         String str10 = "";
/* 3178 */         if (str7.equals(""))
/* 3179 */           continue;  if (!bool) {
/* 3180 */           bool = true;
/* 3181 */           if (str6.indexOf("start") > 0) {
/* 3182 */             String str = str6.substring(0, str6.indexOf("start"));
/* 3183 */             str1 = " where t1_field" + str + ">='" + str7 + "'"; continue;
/* 3184 */           }  if (str6.indexOf("end") > 0) {
/* 3185 */             String str = str6.substring(0, str6.indexOf("end"));
/* 3186 */             str1 = " where t1_field" + str + "<='" + str7 + "'"; continue;
/*      */           } 
/* 3188 */           RecordSet recordSet1 = new RecordSet();
/* 3189 */           recordSet1.executeSql("select fieldhtmltype, fielddbtype, type from cus_formdict where id = " + str6);
/* 3190 */           if (recordSet1.next()) {
/* 3191 */             str8 = recordSet1.getString("fieldhtmltype");
/* 3192 */             str9 = recordSet1.getString("fielddbtype");
/* 3193 */             str10 = recordSet1.getString("type");
/*      */           } 
/* 3195 */           if ("1".equals(str8) || "2".equals(str8) || "text".equals(str9)) {
/* 3196 */             str1 = " where t1_field" + str6 + " like '%" + str7 + "%'"; continue;
/* 3197 */           }  if (("3".equals(str8) && str9.toLowerCase().equals("text")) || "161".equals(str10) || "162".equals(str10)) {
/* 3198 */             str1 = " where 1=1 ";
/* 3199 */             str1 = str1 + " and ( 1=2 ";
/*      */             
/* 3201 */             String[] arrayOfString = str7.split(",");
/* 3202 */             for (String str : arrayOfString) {
/* 3203 */               if (str.length() != 0)
/* 3204 */                 if (this.isoracle) {
/* 3205 */                   str1 = str1 + " or ','||cast(t1_field" + str6 + " as VARCHAR2(4000))||',' like '%," + str + ",%'";
/*      */                 } else {
/* 3207 */                   str1 = str1 + " or ','+cast(t1_field" + str6 + " as VARCHAR(4000))+',' like '%," + str + ",%'";
/*      */                 }  
/* 3209 */             }  str1 = str1 + " ) "; continue;
/*      */           } 
/* 3211 */           str1 = " where t1_field" + str6 + "='" + str7 + "'";
/*      */           
/*      */           continue;
/*      */         } 
/* 3215 */         if (str6.indexOf("start") > 0) {
/* 3216 */           String str = str6.substring(0, str6.indexOf("start"));
/* 3217 */           str1 = str1 + " and t1_field" + str + ">='" + str7 + "'"; continue;
/* 3218 */         }  if (str6.indexOf("end") > 0) {
/* 3219 */           String str = str6.substring(0, str6.indexOf("end"));
/* 3220 */           str1 = str1 + " and t1_field" + str + "<='" + str7 + "'"; continue;
/*      */         } 
/* 3222 */         RecordSet recordSet = new RecordSet();
/* 3223 */         recordSet.executeSql("select fieldhtmltype, fielddbtype, type from cus_formdict where id = " + str6);
/* 3224 */         if (recordSet.next()) {
/* 3225 */           str8 = recordSet.getString("fieldhtmltype");
/* 3226 */           str9 = recordSet.getString("fielddbtype");
/* 3227 */           str10 = recordSet.getString("type");
/*      */         } 
/* 3229 */         if ("1".equals(str8) || "2".equals(str8) || "text".equals(str9)) {
/* 3230 */           str1 = str1 + " and t1_field" + str6 + " like '%" + str7 + "%'"; continue;
/* 3231 */         }  if (("3".equals(str8) && str9.toLowerCase().equals("text")) || "161".equals(str10) || "162".equals(str10)) {
/* 3232 */           str1 = str1 + " and ( 1=2 ";
/*      */           
/* 3234 */           String[] arrayOfString = str7.split(",");
/* 3235 */           for (String str : arrayOfString) {
/* 3236 */             if (str.length() != 0)
/* 3237 */               if (this.isoracle) {
/* 3238 */                 str1 = str1 + " or ','||cast(t1_field" + str6 + " as VARCHAR2(4000))||',' like '%," + str + ",%'";
/*      */               } else {
/* 3240 */                 str1 = str1 + " or ','+cast(t1_field" + str6 + " as VARCHAR(4000))+',' like '%," + str + ",%'";
/*      */               }  
/* 3242 */           }  str1 = str1 + " ) "; continue;
/*      */         } 
/* 3244 */         str1 = str1 + " and t1_field" + str6 + "='" + str7 + "'";
/*      */       } 
/*      */     } 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/* 3252 */     if (this.customFieldWork.size() > 0) {
/* 3253 */       Set set = this.customFieldWork.keySet();
/* 3254 */       for (String str6 : set) {
/*      */         
/* 3256 */         String str7 = (String)this.customFieldWork.get(str6);
/* 3257 */         String str8 = "";
/* 3258 */         String str9 = "";
/* 3259 */         String str10 = "";
/* 3260 */         if (str7.equals(""))
/* 3261 */           continue;  if (!bool) {
/* 3262 */           bool = true;
/* 3263 */           if (str6.indexOf("start") > 0) {
/* 3264 */             String str = str6.substring(0, str6.indexOf("start"));
/* 3265 */             str1 = " where t3_field" + str + ">='" + str7 + "'"; continue;
/* 3266 */           }  if (str6.indexOf("end") > 0) {
/* 3267 */             String str = str6.substring(0, str6.indexOf("end"));
/* 3268 */             str1 = " where t3_field" + str + "<='" + str7 + "'"; continue;
/*      */           } 
/* 3270 */           RecordSet recordSet1 = new RecordSet();
/* 3271 */           recordSet1.executeSql("select fieldhtmltype, fielddbtype, type from cus_formdict where id = " + str6);
/* 3272 */           if (recordSet1.next()) {
/* 3273 */             str8 = recordSet1.getString("fieldhtmltype");
/* 3274 */             str9 = recordSet1.getString("fielddbtype");
/* 3275 */             str10 = recordSet1.getString("type");
/*      */           } 
/* 3277 */           if ("1".equals(str8) || "2".equals(str8) || "text".equals(str9)) {
/* 3278 */             str1 = " where t3_field" + str6 + " like '%" + str7 + "%'"; continue;
/* 3279 */           }  if (("3".equals(str8) && str9.toLowerCase().equals("text")) || "161".equals(str10) || "162".equals(str10)) {
/* 3280 */             str1 = " where 1=1 ";
/* 3281 */             str1 = str1 + " and ( 1=2 ";
/*      */             
/* 3283 */             String[] arrayOfString = str7.split(",");
/* 3284 */             for (String str : arrayOfString) {
/* 3285 */               if (str.length() != 0)
/* 3286 */                 if (this.isoracle) {
/* 3287 */                   str1 = str1 + " or ','||cast(t3_field" + str6 + " as VARCHAR2(4000))||',' like '%," + str + ",%'";
/*      */                 } else {
/* 3289 */                   str1 = str1 + " or ','+cast(t3_field" + str6 + " as VARCHAR(4000))+',' like '%," + str + ",%'";
/*      */                 }  
/* 3291 */             }  str1 = str1 + " ) "; continue;
/*      */           } 
/* 3293 */           str1 = " where t3_field" + str6 + "='" + str7 + "'";
/*      */           
/*      */           continue;
/*      */         } 
/* 3297 */         if (str6.indexOf("start") > 0) {
/* 3298 */           String str = str6.substring(0, str6.indexOf("start"));
/* 3299 */           str1 = str1 + " and t3_field" + str + ">='" + str7 + "'"; continue;
/* 3300 */         }  if (str6.indexOf("end") > 0) {
/* 3301 */           String str = str6.substring(0, str6.indexOf("end"));
/* 3302 */           str1 = str1 + " and t3_field" + str + "<='" + str7 + "'"; continue;
/*      */         } 
/* 3304 */         RecordSet recordSet = new RecordSet();
/* 3305 */         recordSet.executeSql("select fieldhtmltype, fielddbtype, type from cus_formdict where id = " + str6);
/* 3306 */         if (recordSet.next()) {
/* 3307 */           str8 = recordSet.getString("fieldhtmltype");
/* 3308 */           str9 = recordSet.getString("fielddbtype");
/* 3309 */           str10 = recordSet.getString("type");
/*      */         } 
/* 3311 */         if ("1".equals(str8) || "2".equals(str8) || "text".equals(str9)) {
/* 3312 */           str1 = str1 + " and t3_field" + str6 + " like '%" + str7 + "%'"; continue;
/* 3313 */         }  if (("3".equals(str8) && str9.toLowerCase().equals("text")) || "161".equals(str10) || "162".equals(str10)) {
/* 3314 */           str1 = str1 + " and ( 1=2 ";
/*      */           
/* 3316 */           String[] arrayOfString = str7.split(",");
/* 3317 */           for (String str : arrayOfString) {
/* 3318 */             if (str.length() != 0)
/* 3319 */               if (this.isoracle) {
/* 3320 */                 str1 = str1 + " or ','||cast(t3_field" + str6 + " as VARCHAR2(4000))||',' like '%," + str + ",%'";
/*      */               } else {
/* 3322 */                 str1 = str1 + " or ','+cast(t3_field" + str6 + " as VARCHAR(4000))+',' like '%," + str + ",%'";
/*      */               }  
/* 3324 */           }  str1 = str1 + " ) "; continue;
/*      */         } 
/* 3326 */         str1 = str1 + " and t3_field" + str6 + "='" + str7 + "'";
/*      */       } 
/*      */     } 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/* 3334 */     if (!bool) {
/* 3335 */       bool = true;
/* 3336 */       str1 = " where status != 10";
/*      */     } else {
/* 3338 */       str1 = str1 + " and status != 10";
/*      */     } 
/*      */ 
/*      */ 
/*      */     
/* 3343 */     String str5 = "";
/* 3344 */     if (!this.orderby.equals("")) { str5 = " order by " + this.orderby; }
/* 3345 */     else { str5 = " order by departmentid,dsporder "; }
/* 3346 */      return str1 + str5;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getSearchFrom() {
/* 3408 */     return this.searchFrom;
/*      */   }
/*      */   
/*      */   public void setSearchFrom(String paramString) {
/* 3412 */     this.searchFrom = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void initSearchParam(int paramInt1, int paramInt2, HttpServletRequest paramHttpServletRequest) throws Exception {
/* 3420 */     RecordSet recordSet = new RecordSet();
/* 3421 */     DepartmentComInfo departmentComInfo = new DepartmentComInfo();
/* 3422 */     String str1 = Util.null2String(paramHttpServletRequest.getParameter("from"));
/* 3423 */     String str2 = Util.null2String(paramHttpServletRequest.getParameter("flowTitle"));
/* 3424 */     if (str2.length() == 0 && Util.null2String(getResourcename()).length() > 0)
/*      */     {
/* 3426 */       str2 = getResourcename();
/*      */     }
/* 3428 */     String str3 = "";
/* 3429 */     String str4 = "";
/* 3430 */     String str5 = "";
/* 3431 */     String str6 = "";
/* 3432 */     String str7 = "";
/* 3433 */     String str8 = "";
/* 3434 */     String str9 = "";
/* 3435 */     String str10 = "";
/* 3436 */     String str11 = "";
/* 3437 */     String str12 = "";
/* 3438 */     String str13 = "";
/* 3439 */     String str14 = "";
/* 3440 */     String str15 = "";
/* 3441 */     String str16 = "";
/* 3442 */     String str17 = "";
/* 3443 */     String str18 = "";
/* 3444 */     String str19 = "";
/* 3445 */     String str20 = "";
/* 3446 */     String str21 = "";
/* 3447 */     String str22 = "";
/* 3448 */     String str23 = "";
/* 3449 */     String str24 = "";
/* 3450 */     String str25 = "";
/* 3451 */     String str26 = "";
/* 3452 */     String str27 = "";
/* 3453 */     String str28 = "";
/* 3454 */     String str29 = "";
/* 3455 */     String str30 = "";
/* 3456 */     String str31 = "";
/* 3457 */     String str32 = "";
/* 3458 */     String str33 = "";
/* 3459 */     String str34 = "";
/* 3460 */     String str35 = "";
/* 3461 */     String str36 = "";
/* 3462 */     String str37 = "";
/* 3463 */     String str38 = "";
/* 3464 */     String str39 = "";
/* 3465 */     String str40 = "";
/* 3466 */     String str41 = "";
/* 3467 */     String str42 = "";
/* 3468 */     String str43 = "";
/* 3469 */     String str44 = "";
/* 3470 */     String str45 = "";
/* 3471 */     String str46 = "";
/* 3472 */     String str47 = "";
/* 3473 */     String str48 = "";
/* 3474 */     String str49 = "";
/* 3475 */     String str50 = "";
/* 3476 */     String str51 = "";
/* 3477 */     String str52 = "";
/* 3478 */     String str53 = "";
/* 3479 */     String str54 = "";
/* 3480 */     String str55 = "";
/* 3481 */     String str56 = "";
/* 3482 */     String str57 = "";
/* 3483 */     String str58 = "";
/* 3484 */     String str59 = "";
/* 3485 */     String str60 = "";
/* 3486 */     String str61 = "";
/* 3487 */     String str62 = "";
/* 3488 */     String str63 = "";
/* 3489 */     String str64 = "";
/* 3490 */     String str65 = "";
/* 3491 */     String str66 = "";
/* 3492 */     String str67 = "";
/* 3493 */     String str68 = "";
/* 3494 */     String str69 = "";
/* 3495 */     recordSet.executeProc("HrmUserDefine_SelectByID", "" + paramInt1);
/* 3496 */     if (recordSet.next()) {
/* 3497 */       str3 = recordSet.getString(2);
/* 3498 */       str4 = Util.fromScreen(recordSet.getString(3), paramInt2);
/* 3499 */       str5 = Util.fromScreen(recordSet.getString(4), paramInt2);
/* 3500 */       str6 = Util.fromScreen(recordSet.getString(5), paramInt2);
/* 3501 */       str7 = Util.fromScreen(recordSet.getString(6), paramInt2);
/* 3502 */       str8 = Util.fromScreen(recordSet.getString(7), paramInt2);
/* 3503 */       str9 = Util.fromScreen(recordSet.getString(8), paramInt2);
/* 3504 */       str10 = Util.fromScreen(recordSet.getString(9), paramInt2);
/* 3505 */       str11 = Util.fromScreen(recordSet.getString(10), paramInt2);
/* 3506 */       str12 = Util.fromScreen(recordSet.getString(11), paramInt2);
/* 3507 */       str13 = Util.fromScreen(recordSet.getString(12), paramInt2);
/* 3508 */       str14 = Util.fromScreen(recordSet.getString(13), paramInt2);
/* 3509 */       str15 = Util.fromScreen(recordSet.getString(14), paramInt2);
/* 3510 */       str16 = Util.fromScreen(recordSet.getString(15), paramInt2);
/* 3511 */       str17 = Util.fromScreen(recordSet.getString(16), paramInt2);
/* 3512 */       str18 = Util.fromScreen(recordSet.getString(17), paramInt2);
/* 3513 */       str19 = Util.fromScreen(recordSet.getString(18), paramInt2);
/* 3514 */       str20 = Util.fromScreen(recordSet.getString(19), paramInt2);
/* 3515 */       str21 = Util.fromScreen(recordSet.getString(20), paramInt2);
/* 3516 */       str22 = Util.fromScreen(recordSet.getString(21), paramInt2);
/* 3517 */       str23 = Util.fromScreen(recordSet.getString(22), paramInt2);
/* 3518 */       str24 = Util.fromScreen(recordSet.getString(23), paramInt2);
/* 3519 */       str25 = Util.fromScreen(recordSet.getString(24), paramInt2);
/* 3520 */       str26 = Util.fromScreen(recordSet.getString(25), paramInt2);
/* 3521 */       str29 = Util.fromScreen(recordSet.getString("hasage"), paramInt2);
/* 3522 */       str27 = Util.fromScreen(recordSet.getString("hassex"), paramInt2);
/* 3523 */       str28 = Util.fromScreen(recordSet.getString("hasaccounttype"), paramInt2);
/* 3524 */       str40 = Util.fromScreen(recordSet.getString("hasworkcode"), paramInt2);
/* 3525 */       str41 = Util.fromScreen(recordSet.getString("hasjobcall"), paramInt2);
/* 3526 */       str42 = Util.fromScreen(recordSet.getString("hasmobile"), paramInt2);
/* 3527 */       str43 = Util.fromScreen(recordSet.getString("hasmobilecall"), paramInt2);
/* 3528 */       str44 = Util.fromScreen(recordSet.getString("hasfax"), paramInt2);
/* 3529 */       str45 = Util.fromScreen(recordSet.getString("hasemail"), paramInt2);
/* 3530 */       str46 = Util.fromScreen(recordSet.getString("hasfolk"), paramInt2);
/* 3531 */       str47 = Util.fromScreen(recordSet.getString("hasnativeplace"), paramInt2);
/* 3532 */       str48 = Util.fromScreen(recordSet.getString("hasregresidentplace"), paramInt2);
/* 3533 */       str49 = Util.fromScreen(recordSet.getString("hasmaritalstatus"), paramInt2);
/* 3534 */       str50 = Util.fromScreen(recordSet.getString("hascertificatenum"), paramInt2);
/* 3535 */       str51 = Util.fromScreen(recordSet.getString("hastempresidentnumber"), paramInt2);
/* 3536 */       str52 = Util.fromScreen(recordSet.getString("hasresidentplace"), paramInt2);
/* 3537 */       str53 = Util.fromScreen(recordSet.getString("hashomeaddress"), paramInt2);
/* 3538 */       str54 = Util.fromScreen(recordSet.getString("hashealthinfo"), paramInt2);
/* 3539 */       str55 = Util.fromScreen(recordSet.getString("hasheight"), paramInt2);
/* 3540 */       str56 = Util.fromScreen(recordSet.getString("hasweight"), paramInt2);
/* 3541 */       str57 = Util.fromScreen(recordSet.getString("haseducationlevel"), paramInt2);
/* 3542 */       str58 = Util.fromScreen(recordSet.getString("hasdegree"), paramInt2);
/* 3543 */       str59 = Util.fromScreen(recordSet.getString("hasusekind"), paramInt2);
/* 3544 */       str60 = Util.fromScreen(recordSet.getString("haspolicy"), paramInt2);
/* 3545 */       str61 = Util.fromScreen(recordSet.getString("hasbememberdate"), paramInt2);
/* 3546 */       str62 = Util.fromScreen(recordSet.getString("hasbepartydate"), paramInt2);
/* 3547 */       str63 = Util.fromScreen(recordSet.getString("hasislabouunion"), paramInt2);
/* 3548 */       str64 = Util.fromScreen(recordSet.getString("hasbankid1"), paramInt2);
/* 3549 */       str65 = Util.fromScreen(recordSet.getString("hasaccountid1"), paramInt2);
/* 3550 */       str66 = Util.fromScreen(recordSet.getString("hasaccumfundaccount"), paramInt2);
/* 3551 */       str67 = Util.fromScreen(recordSet.getString("hasloginid"), paramInt2);
/* 3552 */       str68 = Util.fromScreen(recordSet.getString("hassystemlanguage"), paramInt2);
/*      */     } 
/* 3554 */     String str70 = "";
/* 3555 */     String str71 = "";
/* 3556 */     String str72 = "";
/* 3557 */     String str73 = "";
/* 3558 */     String str74 = "";
/* 3559 */     String str75 = "";
/* 3560 */     String str76 = "";
/* 3561 */     String str77 = "";
/* 3562 */     String str78 = "";
/* 3563 */     String str79 = "";
/* 3564 */     String str80 = "";
/* 3565 */     String str81 = "";
/* 3566 */     String str82 = "";
/* 3567 */     String str83 = "";
/* 3568 */     String str84 = "";
/* 3569 */     String str85 = "";
/* 3570 */     String str86 = "";
/* 3571 */     String str87 = "";
/* 3572 */     String str88 = "";
/* 3573 */     String str89 = "";
/*      */     
/* 3575 */     boolean bool = true;
/* 3576 */     recordSet.executeProc("Base_FreeField_Select", "hr");
/* 3577 */     if (recordSet.getCounts() <= 0) {
/* 3578 */       bool = false;
/*      */     } else {
/* 3580 */       recordSet.first();
/* 3581 */       str70 = recordSet.getString("dff01use");
/* 3582 */       str71 = recordSet.getString("dff02use");
/* 3583 */       str72 = recordSet.getString("dff03use");
/* 3584 */       str73 = recordSet.getString("dff04use");
/* 3585 */       str74 = recordSet.getString("dff05use");
/* 3586 */       str75 = recordSet.getString("tff01use");
/* 3587 */       str76 = recordSet.getString("tff02use");
/* 3588 */       str77 = recordSet.getString("tff03use");
/* 3589 */       str78 = recordSet.getString("tff04use");
/* 3590 */       str79 = recordSet.getString("tff05use");
/* 3591 */       str85 = recordSet.getString("bff01use");
/* 3592 */       str86 = recordSet.getString("bff02use");
/* 3593 */       str87 = recordSet.getString("bff03use");
/* 3594 */       str88 = recordSet.getString("bff04use");
/* 3595 */       str89 = recordSet.getString("bff05use");
/* 3596 */       str80 = recordSet.getString("nff01use");
/* 3597 */       str81 = recordSet.getString("nff02use");
/* 3598 */       str82 = recordSet.getString("nff03use");
/* 3599 */       str83 = recordSet.getString("nff04use");
/* 3600 */       str84 = recordSet.getString("nff05use");
/*      */     } 
/*      */     
/* 3603 */     String str90 = "";
/* 3604 */     String str91 = "";
/* 3605 */     String str92 = "";
/* 3606 */     String str93 = "";
/* 3607 */     String str94 = "";
/* 3608 */     String str95 = "";
/* 3609 */     String str96 = "";
/* 3610 */     String str97 = "";
/* 3611 */     String str98 = "";
/* 3612 */     String str99 = "";
/* 3613 */     String str100 = Util.null2String(paramHttpServletRequest.getParameter("companyid"));
/* 3614 */     String str101 = Util.null2String(paramHttpServletRequest.getParameter("subcompany1"));
/* 3615 */     String str102 = "";
/* 3616 */     String str103 = "";
/* 3617 */     String str104 = "";
/* 3618 */     String str105 = Util.null2String(paramHttpServletRequest.getParameter("department"));
/* 3619 */     String str106 = "";
/* 3620 */     String str107 = "";
/* 3621 */     String str108 = "";
/* 3622 */     String str109 = "";
/* 3623 */     String str110 = "";
/* 3624 */     String str111 = "";
/* 3625 */     String str112 = "";
/* 3626 */     String str113 = "";
/* 3627 */     String str114 = "";
/* 3628 */     String str115 = "";
/* 3629 */     String str116 = "";
/* 3630 */     String str117 = "";
/* 3631 */     String str118 = "";
/* 3632 */     String str119 = "";
/* 3633 */     String str120 = "";
/* 3634 */     String str121 = "";
/* 3635 */     String str122 = "";
/* 3636 */     String str123 = "";
/* 3637 */     String str124 = "";
/* 3638 */     String str125 = "";
/* 3639 */     String str126 = "";
/* 3640 */     String str127 = "";
/* 3641 */     int i = -1;
/* 3642 */     String str128 = "";
/* 3643 */     String str129 = "";
/* 3644 */     String str130 = "";
/* 3645 */     String str131 = "";
/* 3646 */     String str132 = "";
/* 3647 */     String str133 = "";
/* 3648 */     String str134 = "";
/* 3649 */     String str135 = "";
/* 3650 */     String str136 = "";
/* 3651 */     String str137 = "";
/* 3652 */     String str138 = "";
/* 3653 */     String str139 = "";
/* 3654 */     String str140 = "";
/* 3655 */     String str141 = "";
/* 3656 */     String str142 = "";
/* 3657 */     String str143 = "";
/* 3658 */     String str144 = "";
/* 3659 */     String str145 = "";
/* 3660 */     String str146 = "";
/* 3661 */     String str147 = "";
/* 3662 */     String str148 = "";
/* 3663 */     String str149 = "";
/* 3664 */     String str150 = "";
/* 3665 */     String str151 = "";
/* 3666 */     String str152 = "";
/* 3667 */     String str153 = "";
/* 3668 */     String str154 = "";
/* 3669 */     String str155 = "";
/* 3670 */     String str156 = "";
/* 3671 */     String str157 = "";
/* 3672 */     String str158 = "";
/* 3673 */     String str159 = "";
/* 3674 */     String str160 = "";
/* 3675 */     String str161 = "";
/* 3676 */     String str162 = "";
/* 3677 */     String str163 = "";
/*      */ 
/*      */     
/* 3680 */     String str164 = "";
/* 3681 */     String str165 = "";
/* 3682 */     String str166 = "";
/* 3683 */     String str167 = "";
/* 3684 */     String str168 = "";
/* 3685 */     String str169 = "";
/* 3686 */     String str170 = "";
/* 3687 */     String str171 = "";
/* 3688 */     String str172 = "";
/* 3689 */     String str173 = "";
/*      */ 
/*      */     
/* 3692 */     String str174 = "";
/* 3693 */     String str175 = "";
/* 3694 */     String str176 = "";
/* 3695 */     String str177 = "";
/* 3696 */     String str178 = "";
/* 3697 */     String str179 = "";
/* 3698 */     String str180 = "";
/* 3699 */     String str181 = "";
/* 3700 */     String str182 = "";
/* 3701 */     String str183 = "";
/*      */ 
/*      */     
/* 3704 */     String str184 = "";
/* 3705 */     String str185 = "";
/* 3706 */     String str186 = "";
/* 3707 */     String str187 = "";
/* 3708 */     String str188 = "";
/*      */ 
/*      */     
/* 3711 */     String str189 = "";
/* 3712 */     String str190 = "";
/* 3713 */     String str191 = "";
/* 3714 */     String str192 = "";
/* 3715 */     String str193 = "";
/*      */ 
/*      */ 
/*      */     
/* 3719 */     recordSet.executeSql("select * from HrmSearchMould where 1=2");
/* 3720 */     String[] arrayOfString = recordSet.getColumnName();
/* 3721 */     ArrayList<String> arrayList1 = new ArrayList(); byte b;
/* 3722 */     for (b = 0; b < arrayOfString.length; b++) {
/* 3723 */       arrayList1.add(arrayOfString[b]);
/*      */     }
/*      */     
/* 3726 */     b = -1;
/* 3727 */     CustomFieldManager customFieldManager = new CustomFieldManager("HrmCustomFieldByInfoType", b);
/* 3728 */     b = 0;
/* 3729 */     customFieldManager.getCustomFields();
/* 3730 */     String str194 = "";
/* 3731 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 3732 */     ArrayList<String> arrayList2 = new ArrayList();
/* 3733 */     while (customFieldManager.next()) {
/* 3734 */       str194 = "column_" + b + "_" + customFieldManager.getId();
/* 3735 */       arrayList2.add(str194);
/* 3736 */       if (arrayList1.contains(str194) || arrayList1.contains(str194.toUpperCase())) {
/*      */         continue;
/*      */       }
/* 3739 */       if ("oracle".equals(recordSet.getDBType())) {
/* 3740 */         recordSet.executeSql("ALTER TABLE HrmSearchMould ADD " + str194 + " varchar2(200) NULL"); continue;
/*      */       } 
/* 3742 */       recordSet.executeSql("ALTER TABLE HrmSearchMould ADD " + str194 + " varchar(200) NULL");
/*      */     } 
/*      */ 
/*      */ 
/*      */     
/* 3747 */     for (byte b1 = 0; b1 < arrayList1.size(); b1++) {
/* 3748 */       String str = arrayList1.get(b1);
/* 3749 */       if (str.startsWith("column_" + b + "_"))
/*      */       {
/* 3751 */         if (!arrayList2.contains(str)) {
/* 3752 */           recordSet.executeSql("ALTER TABLE HrmSearchMould DROP COLUMN  " + str);
/*      */         }
/*      */       }
/*      */     } 
/*      */     
/* 3757 */     b = 1;
/* 3758 */     customFieldManager = new CustomFieldManager("HrmCustomFieldByInfoType", b);
/* 3759 */     customFieldManager.getCustomFields();
/* 3760 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 3761 */     ArrayList<String> arrayList3 = new ArrayList();
/* 3762 */     while (customFieldManager.next()) {
/* 3763 */       str194 = "column_" + b + "_" + customFieldManager.getId();
/* 3764 */       arrayList3.add(str194);
/* 3765 */       if (arrayList1.contains(str194) || arrayList1.contains(str194.toUpperCase())) {
/*      */         continue;
/*      */       }
/* 3768 */       if ("oracle".equals(recordSet.getDBType())) {
/* 3769 */         recordSet.executeSql("ALTER TABLE HrmSearchMould ADD " + str194 + " varchar2(200) NULL"); continue;
/*      */       } 
/* 3771 */       recordSet.executeSql("ALTER TABLE HrmSearchMould ADD " + str194 + " varchar(200) NULL");
/*      */     } 
/*      */     
/*      */     int j;
/*      */     
/* 3776 */     for (j = 0; j < arrayList1.size(); j++) {
/* 3777 */       String str = arrayList1.get(j);
/* 3778 */       if (str.startsWith("column_" + b + "_"))
/*      */       {
/* 3780 */         if (!arrayList3.contains(str)) {
/* 3781 */           recordSet.executeSql("ALTER TABLE HrmSearchMould DROP COLUMN  " + str);
/*      */         }
/*      */       }
/*      */     } 
/*      */     
/* 3786 */     j = Util.getIntValue(paramHttpServletRequest.getParameter("mouldid"), 0);
/*      */     
/* 3788 */     recordSet.executeProc("HrmSearchMould_SelectByID", "" + j);
/* 3789 */     if (recordSet.next()) {
/* 3790 */       str90 = Util.toScreenToEdit(recordSet.getString("resourceid"), paramInt2);
/* 3791 */       str91 = Util.toScreenToEdit(recordSet.getString("resourcename"), paramInt2);
/* 3792 */       str92 = Util.toScreenToEdit(recordSet.getString("jobtitle"), paramInt2);
/* 3793 */       str93 = Util.toScreenToEdit(recordSet.getString("activitydesc"), paramInt2);
/* 3794 */       str94 = Util.toScreenToEdit(recordSet.getString("jobgroup"), paramInt2);
/* 3795 */       str95 = Util.toScreenToEdit(recordSet.getString("jobactivity"), paramInt2);
/* 3796 */       str96 = Util.toScreenToEdit(recordSet.getString("costcenter"), paramInt2);
/* 3797 */       str97 = Util.toScreenToEdit(recordSet.getString("competency"), paramInt2);
/* 3798 */       str98 = Util.toScreenToEdit(recordSet.getString("resourcetype"), paramInt2);
/* 3799 */       str99 = Util.toScreenToEdit(recordSet.getString("status"), paramInt2);
/* 3800 */       str101 = Util.toScreenToEdit(recordSet.getString("subcompany1"), paramInt2);
/* 3801 */       str106 = Util.toScreenToEdit(recordSet.getString("department"), paramInt2);
/* 3802 */       ArrayList<String> arrayList = Util.TokenizerString(str106, ",");
/* 3803 */       for (byte b2 = 0; b2 < arrayList.size(); b2++) {
/* 3804 */         str105 = str105 + (String)arrayList.get(b2) + ",";
/* 3805 */         str69 = str69 + departmentComInfo.getDepartmentname(arrayList.get(b2)) + "&nbsp;,";
/*      */       } 
/* 3807 */       if (!"".equals(str105)) {
/* 3808 */         str105 = str105.substring(0, str105.length() - 1);
/* 3809 */         str69 = str69.substring(0, str69.length() - 1);
/*      */       } 
/* 3811 */       str107 = Util.toScreenToEdit(recordSet.getString("location"), paramInt2);
/* 3812 */       str108 = Util.toScreenToEdit(recordSet.getString("manager"), paramInt2);
/* 3813 */       str109 = Util.toScreenToEdit(recordSet.getString("assistant"), paramInt2);
/* 3814 */       str110 = Util.toScreenToEdit(recordSet.getString("roles"), paramInt2);
/* 3815 */       str111 = Util.toScreenToEdit(recordSet.getString("seclevel"), paramInt2);
/* 3816 */       str112 = Util.toScreenToEdit(recordSet.getString("seclevelTo"), paramInt2);
/* 3817 */       str113 = Util.toScreenToEdit(recordSet.getString("joblevel"), paramInt2);
/* 3818 */       str114 = Util.toScreenToEdit(recordSet.getString("joblevelTo"), paramInt2);
/* 3819 */       str115 = Util.toScreenToEdit(recordSet.getString("workroom"), paramInt2);
/* 3820 */       str116 = Util.toScreenToEdit(recordSet.getString("telephone"), paramInt2);
/* 3821 */       str117 = Util.toScreenToEdit(recordSet.getString("startdate"), paramInt2);
/* 3822 */       str118 = Util.toScreenToEdit(recordSet.getString("startdateTo"), paramInt2);
/* 3823 */       str119 = Util.toScreenToEdit(recordSet.getString("enddate"), paramInt2);
/* 3824 */       str120 = Util.toScreenToEdit(recordSet.getString("enddateTo"), paramInt2);
/* 3825 */       str121 = Util.toScreenToEdit(recordSet.getString("contractdate"), paramInt2);
/* 3826 */       str122 = Util.toScreenToEdit(recordSet.getString("contractdateTo"), paramInt2);
/* 3827 */       str123 = Util.toScreenToEdit(recordSet.getString("birthdaydate"), paramInt2);
/* 3828 */       str124 = Util.toScreenToEdit(recordSet.getString("birthdaydateTo"), paramInt2);
/* 3829 */       str125 = Util.toScreenToEdit(recordSet.getString("age"), paramInt2);
/* 3830 */       str126 = Util.toScreenToEdit(recordSet.getString("ageTo"), paramInt2);
/* 3831 */       str127 = Util.toScreenToEdit(recordSet.getString("sex"), paramInt2);
/* 3832 */       i = recordSet.getInt("accounttype");
/* 3833 */       str128 = Util.toScreenToEdit(recordSet.getString("resourceidfrom"), paramInt2);
/* 3834 */       str129 = Util.toScreenToEdit(recordSet.getString("resourceidto"), paramInt2);
/* 3835 */       str130 = Util.toScreenToEdit(recordSet.getString("workcode"), paramInt2);
/* 3836 */       str131 = Util.toScreenToEdit(recordSet.getString("jobcall"), paramInt2);
/* 3837 */       str132 = Util.toScreenToEdit(recordSet.getString("mobile"), paramInt2);
/* 3838 */       str133 = Util.toScreenToEdit(recordSet.getString("mobilecall"), paramInt2);
/* 3839 */       str134 = Util.toScreenToEdit(recordSet.getString("fax"), paramInt2);
/* 3840 */       str135 = Util.toScreenToEdit(recordSet.getString("email"), paramInt2);
/* 3841 */       str136 = Util.toScreenToEdit(recordSet.getString("folk"), paramInt2);
/* 3842 */       str137 = Util.toScreenToEdit(recordSet.getString("nativeplace"), paramInt2);
/* 3843 */       str138 = Util.toScreenToEdit(recordSet.getString("regresidentplace"), paramInt2);
/* 3844 */       str139 = Util.toScreenToEdit(recordSet.getString("maritalstatus"), paramInt2);
/* 3845 */       str140 = Util.toScreenToEdit(recordSet.getString("certificatenum"), paramInt2);
/* 3846 */       str141 = Util.toScreenToEdit(recordSet.getString("tempresidentnumber"), paramInt2);
/* 3847 */       str142 = Util.toScreenToEdit(recordSet.getString("residentplace"), paramInt2);
/* 3848 */       str143 = Util.toScreenToEdit(recordSet.getString("homeaddress"), paramInt2);
/* 3849 */       str144 = Util.toScreenToEdit(recordSet.getString("healthinfo"), paramInt2);
/* 3850 */       str145 = Util.toScreenToEdit(recordSet.getString("heightfrom"), paramInt2);
/* 3851 */       str146 = Util.toScreenToEdit(recordSet.getString("heightto"), paramInt2);
/* 3852 */       str147 = Util.toScreenToEdit(recordSet.getString("weightfrom"), paramInt2);
/* 3853 */       str148 = Util.toScreenToEdit(recordSet.getString("weightto"), paramInt2);
/* 3854 */       str149 = Util.toScreenToEdit(recordSet.getString("educationlevel"), paramInt2);
/* 3855 */       str150 = Util.toScreenToEdit(recordSet.getString("educationlevelto"), paramInt2);
/* 3856 */       str151 = Util.toScreenToEdit(recordSet.getString("degree"), paramInt2);
/* 3857 */       str152 = Util.toScreenToEdit(recordSet.getString("usekind"), paramInt2);
/* 3858 */       str153 = Util.toScreenToEdit(recordSet.getString("policy"), paramInt2);
/* 3859 */       str154 = Util.toScreenToEdit(recordSet.getString("bememberdatefrom"), paramInt2);
/* 3860 */       str155 = Util.toScreenToEdit(recordSet.getString("bememberdateto"), paramInt2);
/* 3861 */       str156 = Util.toScreenToEdit(recordSet.getString("bepartydatefrom"), paramInt2);
/* 3862 */       str157 = Util.toScreenToEdit(recordSet.getString("bepartydateto"), paramInt2);
/* 3863 */       str158 = Util.toScreenToEdit(recordSet.getString("islabouunion"), paramInt2);
/* 3864 */       str159 = Util.toScreenToEdit(recordSet.getString("bankid1"), paramInt2);
/* 3865 */       str160 = Util.toScreenToEdit(recordSet.getString("accountid1"), paramInt2);
/* 3866 */       str161 = Util.toScreenToEdit(recordSet.getString("accumfundaccount"), paramInt2);
/* 3867 */       str162 = Util.toScreenToEdit(recordSet.getString("loginid"), paramInt2);
/* 3868 */       str163 = Util.toScreenToEdit(recordSet.getString("systemlanguage"), paramInt2);
/* 3869 */       str164 = Util.toScreenToEdit(recordSet.getString("datefield1"), paramInt2);
/* 3870 */       str169 = Util.toScreenToEdit(recordSet.getString("datefieldto1"), paramInt2);
/* 3871 */       str165 = Util.toScreenToEdit(recordSet.getString("datefield2"), paramInt2);
/* 3872 */       str170 = Util.toScreenToEdit(recordSet.getString("datefieldto2"), paramInt2);
/* 3873 */       str166 = Util.toScreenToEdit(recordSet.getString("datefield3"), paramInt2);
/* 3874 */       str171 = Util.toScreenToEdit(recordSet.getString("datefieldto3"), paramInt2);
/* 3875 */       str167 = Util.toScreenToEdit(recordSet.getString("datefield4"), paramInt2);
/* 3876 */       str172 = Util.toScreenToEdit(recordSet.getString("datefieldto4"), paramInt2);
/* 3877 */       str168 = Util.toScreenToEdit(recordSet.getString("datefield5"), paramInt2);
/* 3878 */       str173 = Util.toScreenToEdit(recordSet.getString("datefieldto5"), paramInt2);
/* 3879 */       str174 = Util.toScreenToEdit(recordSet.getString("numberfield1"), paramInt2);
/* 3880 */       str179 = Util.toScreenToEdit(recordSet.getString("numberfieldto1"), paramInt2);
/* 3881 */       str175 = Util.toScreenToEdit(recordSet.getString("numberfield2"), paramInt2);
/* 3882 */       str180 = Util.toScreenToEdit(recordSet.getString("numberfieldto2"), paramInt2);
/* 3883 */       str176 = Util.toScreenToEdit(recordSet.getString("numberfield3"), paramInt2);
/* 3884 */       str181 = Util.toScreenToEdit(recordSet.getString("numberfieldto3"), paramInt2);
/* 3885 */       str177 = Util.toScreenToEdit(recordSet.getString("numberfield4"), paramInt2);
/* 3886 */       str182 = Util.toScreenToEdit(recordSet.getString("numberfieldto4"), paramInt2);
/* 3887 */       str178 = Util.toScreenToEdit(recordSet.getString("numberfield5"), paramInt2);
/* 3888 */       str183 = Util.toScreenToEdit(recordSet.getString("numberfieldto5"), paramInt2);
/* 3889 */       str184 = Util.toScreenToEdit(recordSet.getString("textfield1"), paramInt2);
/* 3890 */       str185 = Util.toScreenToEdit(recordSet.getString("textfield2"), paramInt2);
/* 3891 */       str186 = Util.toScreenToEdit(recordSet.getString("textfield3"), paramInt2);
/* 3892 */       str187 = Util.toScreenToEdit(recordSet.getString("textfield4"), paramInt2);
/* 3893 */       str188 = Util.toScreenToEdit(recordSet.getString("textfield5"), paramInt2);
/* 3894 */       str189 = Util.toScreenToEdit(recordSet.getString("tinyintfield1"), paramInt2);
/* 3895 */       str190 = Util.toScreenToEdit(recordSet.getString("tinyintfield2"), paramInt2);
/* 3896 */       str191 = Util.toScreenToEdit(recordSet.getString("tinyintfield3"), paramInt2);
/* 3897 */       str192 = Util.toScreenToEdit(recordSet.getString("tinyintfield4"), paramInt2);
/* 3898 */       str193 = Util.toScreenToEdit(recordSet.getString("tinyintfield5"), paramInt2);
/*      */       
/* 3900 */       String str195 = "";
/* 3901 */       String str196 = ""; byte b3;
/* 3902 */       for (b3 = 0; b3 < arrayList2.size(); b3++) {
/* 3903 */         str196 = arrayList2.get(b3).toString();
/* 3904 */         str195 = Util.toScreenToEdit(recordSet.getString(str196), paramInt2);
/* 3905 */         hashMap1.put(str196, str195);
/*      */       } 
/*      */       
/* 3908 */       str195 = "";
/* 3909 */       str196 = "";
/* 3910 */       for (b3 = 0; b3 < arrayList2.size(); b3++) {
/* 3911 */         str196 = arrayList2.get(b3).toString();
/* 3912 */         str195 = Util.toScreenToEdit(recordSet.getString(str196), paramInt2);
/* 3913 */         hashMap2.put(str196, str195);
/*      */       } 
/*      */       
/* 3916 */       String str197 = Util.fromScreen(paramHttpServletRequest.getParameter("orderby"), paramInt2);
/* 3917 */       boolean bool1 = recordSet.getDBType().equals("oracle");
/*      */       
/* 3919 */       resetSearchInfo();
/* 3920 */       setCustomFieldBase(hashMap1);
/* 3921 */       setCustomFieldPersonal(hashMap2);
/*      */ 
/*      */       
/* 3924 */       if (str70.equals("1") && (j == 0 || !str164.equals("") || !str169.equals(""))) {
/* 3925 */         setDff01name(str164);
/* 3926 */         setDff01nameto(str169);
/*      */       } 
/* 3928 */       if (str71.equals("1") && (j == 0 || !str165.equals("") || !str170.equals(""))) {
/* 3929 */         setDff02name(str165);
/* 3930 */         setDff02nameto(str170);
/*      */       } 
/* 3932 */       if (str72.equals("1") && (j == 0 || !str166.equals("") || !str171.equals(""))) {
/* 3933 */         setDff03name(str166);
/* 3934 */         setDff03nameto(str171);
/*      */       } 
/* 3936 */       if (str73.equals("1") && (j == 0 || !str167.equals("") || !str172.equals(""))) {
/* 3937 */         setDff04name(str167);
/* 3938 */         setDff04nameto(str172);
/*      */       } 
/* 3940 */       if (str74.equals("1") && (j == 0 || !str168.equals("") || !str173.equals(""))) {
/* 3941 */         setDff05name(str168);
/* 3942 */         setDff05nameto(str173);
/*      */       } 
/* 3944 */       if (str80.equals("1") && (j == 0 || !str174.equals("") || !str179.equals(""))) {
/* 3945 */         setNff01name(str174);
/* 3946 */         setNff01nameto(str179);
/*      */       } 
/* 3948 */       if (str81.equals("1") && (j == 0 || !str175.equals("") || !str180.equals(""))) {
/* 3949 */         setNff02name(str175);
/* 3950 */         setNff02nameto(str180);
/*      */       } 
/* 3952 */       if (str82.equals("1") && (j == 0 || !str176.equals("") || !str181.equals(""))) {
/* 3953 */         setNff03name(str176);
/* 3954 */         setNff03nameto(str181);
/*      */       } 
/* 3956 */       if (str83.equals("1") && (j == 0 || !str177.equals("") || !str182.equals(""))) {
/* 3957 */         setNff04name(str177);
/* 3958 */         setNff04nameto(str182);
/*      */       } 
/* 3960 */       if (str84.equals("1") && (j == 0 || !str178.equals("") || !str183.equals(""))) {
/* 3961 */         setNff05name(str178);
/* 3962 */         setNff05nameto(str183);
/*      */       } 
/* 3964 */       if (str75.equals("1") && (j == 0 || !str184.equals(""))) {
/* 3965 */         setTff01name(str184);
/*      */       }
/* 3967 */       if (str76.equals("1") && (j == 0 || !str185.equals(""))) {
/* 3968 */         setTff02name(str185);
/*      */       }
/* 3970 */       if (str77.equals("1") && (j == 0 || !str186.equals(""))) {
/* 3971 */         setTff03name(str186);
/*      */       }
/* 3973 */       if (str78.equals("1") && (j == 0 || !str187.equals(""))) {
/* 3974 */         setTff04name(str187);
/*      */       }
/* 3976 */       if (str79.equals("1") && (j == 0 || !str188.equals(""))) {
/* 3977 */         setTff05name(str188);
/*      */       }
/* 3979 */       if (str85.equals("1") && (j == 0 || !str189.equals("0"))) {
/* 3980 */         setBff01name(str189);
/*      */       }
/* 3982 */       if (str86.equals("1") && (j == 0 || !str190.equals("0"))) {
/* 3983 */         setBff02name(str190);
/*      */       }
/* 3985 */       if (str87.equals("1") && (j == 0 || !str191.equals("0"))) {
/* 3986 */         setBff03name(str191);
/*      */       }
/* 3988 */       if (str88.equals("1") && (j == 0 || !str192.equals("0"))) {
/* 3989 */         setBff04name(str192);
/*      */       }
/* 3991 */       if (str89.equals("1") && (j == 0 || !str193.equals("0"))) {
/* 3992 */         setBff05name(str193);
/*      */       }
/*      */ 
/*      */       
/* 3996 */       setResourceid(str90);
/* 3997 */       if (str4.equals("1") && (j == 0 || !str91.equals(""))) {
/* 3998 */         if (!str1.equals("QuickSearch")) setResourcename(str91); 
/* 3999 */         if (str91.length() == 0 && str2.length() > 0) setResourcename(str2); 
/*      */       } 
/* 4001 */       if (str5.equals("1") && (j == 0 || !str92.equals(""))) setJobtitle(str92); 
/* 4002 */       if (str6.equals("1") && (j == 0 || !str93.equals(""))) setActivitydesc(str93); 
/* 4003 */       if (str7.equals("1") && (j == 0 || !str94.equals(""))) setJobgroup(str94); 
/* 4004 */       if (str8.equals("1") && (j == 0 || !str95.equals(""))) setJobactivity(str95); 
/* 4005 */       if (str9.equals("1") && (j == 0 || !str96.equals(""))) setCostcenter(str96); 
/* 4006 */       if (str10.equals("1") && (j == 0 || !str97.equals(""))) setCompetency(str97); 
/* 4007 */       if (str11.equals("1") && (j == 0 || !str98.equals(""))) setResourcetype(str98); 
/* 4008 */       if (str12.equals("1") && (j == 0 || !str99.equals(""))) setStatus(str99);
/*      */       
/* 4010 */       if (str13.equals("1") && (j == 0 || !str101.equals("0"))) setSubcompany1(str101);
/*      */       
/* 4012 */       if (str14.equals("1") && (j == 0 || !str105.equals("0"))) setDepartment(str105); 
/* 4013 */       if (str15.equals("1") && (j == 0 || !str107.equals(""))) setLocation(str107); 
/* 4014 */       if (str16.equals("1") && (j == 0 || !str108.equals(""))) setManager(str108); 
/* 4015 */       if (str17.equals("1") && (j == 0 || !str109.equals(""))) setAssistant(str109); 
/* 4016 */       if (str18.equals("1") && (j == 0 || !str110.equals(""))) setRoles(str110); 
/* 4017 */       if (str19.equals("1") && (j == 0 || !str111.equals("0") || !str112.equals("0"))) {
/* 4018 */         setSeclevel(str111);
/* 4019 */         setSeclevelTo(str112);
/*      */       } 
/* 4021 */       if (str20.equals("1") && (j == 0 || !str113.equals("0") || !str114.equals("0"))) {
/* 4022 */         setJoblevel(str113);
/* 4023 */         setJoblevelTo(str114);
/*      */       } 
/* 4025 */       if (str21.equals("1") && (j == 0 || !str115.equals(""))) setWorkroom(str115); 
/* 4026 */       if (str22.equals("1") && (j == 0 || !str116.equals(""))) setTelephone(str116); 
/* 4027 */       if (str23.equals("1") && (j == 0 || !str117.equals("") || !str118.equals(""))) {
/* 4028 */         setStartdate(str117);
/* 4029 */         setStartdateTo(str118);
/*      */       } 
/* 4031 */       if (str24.equals("1") && (j == 0 || !str119.equals("") || !str120.equals(""))) {
/* 4032 */         setEnddate(str119);
/* 4033 */         setEnddateTo(str120);
/*      */       } 
/* 4035 */       if (str25.equals("1") && (j == 0 || !str121.equals("") || !str122.equals(""))) {
/* 4036 */         setContractdate(str121);
/* 4037 */         setContractdateTo(str122);
/*      */       } 
/* 4039 */       if (str26.equals("1") && (j == 0 || !str123.equals("") || !str124.equals(""))) {
/* 4040 */         setBirthdaydate(str123);
/* 4041 */         setBirthdaydateTo(str124);
/*      */       } 
/* 4043 */       if (str29.equals("1") && (j == 0 || !str125.equals("0") || !str126.equals("0"))) {
/* 4044 */         setAge(str125);
/* 4045 */         setAgeTo(str126);
/*      */       } 
/* 4047 */       if (str27.equals("1") && (j == 0 || !str127.equals(""))) setSex(str127); 
/* 4048 */       boolean bool2 = GCONST.getMOREACCOUNTLANDING();
/* 4049 */       if (bool2 && str28.equals("1") && (j == 0 || !str28.equals(""))) setAccounttype(i); 
/* 4050 */       if (str3.equals("1") && (j == 0 || !str128.equals("") || !str129.equals(""))) {
/* 4051 */         setResourceidfrom(str128);
/* 4052 */         setResourceidto(str129);
/*      */       } 
/* 4054 */       if (str40.equals("1") && (j == 0 || !str130.equals(""))) setWorkcode(str130); 
/* 4055 */       if (str41.equals("1") && (j == 0 || !str131.equals("0"))) setJobcall(str131); 
/* 4056 */       if (str42.equals("1") && (j == 0 || !str132.equals(""))) setMobile(str132); 
/* 4057 */       if (str43.equals("1") && (j == 0 || !str133.equals(""))) setMobilecall(str133); 
/* 4058 */       if (str44.equals("1") && (j == 0 || !str134.equals(""))) setFax(str134); 
/* 4059 */       if (str45.equals("1") && (j == 0 || !str135.equals(""))) setEmail(str135); 
/* 4060 */       if (str46.equals("1") && (j == 0 || !str136.equals(""))) setFolk(str136); 
/* 4061 */       if (str47.equals("1") && (j == 0 || !str137.equals(""))) setNativeplace(str137); 
/* 4062 */       if (str48.equals("1") && (j == 0 || !str138.equals(""))) setRegresidentplace(str138); 
/* 4063 */       if (str49.equals("1") && (j == 0 || !str139.equals(""))) setMaritalstatus(str139); 
/* 4064 */       if (str50.equals("1") && (j == 0 || !str140.equals(""))) setCertificatenum(str140); 
/* 4065 */       if (str51.equals("1") && (j == 0 || !str141.equals(""))) setTempresidentnumber(str141); 
/* 4066 */       if (str52.equals("1") && (j == 0 || !str142.equals(""))) setResidentplace(str142); 
/* 4067 */       if (str53.equals("1") && (j == 0 || !str143.equals(""))) setHomeaddress(str143); 
/* 4068 */       if (str54.equals("1") && (j == 0 || !str144.equals(""))) setHealthinfo(str144); 
/* 4069 */       if (str55.equals("1") && (j == 0 || !str145.equals("0") || !str146.equals("0"))) {
/* 4070 */         setHeightfrom(str145);
/* 4071 */         setHeightto(str146);
/*      */       } 
/* 4073 */       if (str56.equals("1") && (j == 0 || !str125.equals("0") || !str126.equals("0"))) {
/* 4074 */         setWeightfrom(str147);
/* 4075 */         setWeightto(str148);
/*      */       } 
/* 4077 */       if (str57.equals("1") && (j == 0 || !str149.equals("0") || !str150.equals("0"))) {
/* 4078 */         setEducationlevel(str149);
/* 4079 */         setEducationlevelTo(str150);
/*      */       } 
/* 4081 */       if (str58.equals("1") && (j == 0 || !str151.equals(""))) setDegree(str151); 
/* 4082 */       if (str59.equals("1") && (j == 0 || !str152.equals(""))) setUsekind(str152); 
/* 4083 */       if (str60.equals("1") && (j == 0 || !str153.equals(""))) setPolicy(str153); 
/* 4084 */       if (str61.equals("1") && (j == 0 || !str154.equals("0") || !str155.equals("0"))) {
/* 4085 */         setBememberdatefrom(str154);
/* 4086 */         setBememberdateto(str155);
/*      */       } 
/* 4088 */       if (str62.equals("1") && (j == 0 || !str156.equals("0") || !str157.equals("0"))) {
/* 4089 */         setBepartydatefrom(str156);
/* 4090 */         setBepartydateto(str157);
/*      */       } 
/* 4092 */       if (str63.equals("1") && (j == 0 || !str158.equals(""))) setIslabouunion(str158); 
/* 4093 */       if (str64.equals("1") && (j == 0 || !str159.equals(""))) setBankid1(str159); 
/* 4094 */       if (str65.equals("1") && (j == 0 || !str160.equals(""))) setAccountid1(str160); 
/* 4095 */       if (str66.equals("1") && (j == 0 || !str161.equals(""))) setAccumfundaccount(str161); 
/* 4096 */       if (str67.equals("1") && (j == 0 || !str162.equals(""))) setLoginid(str162); 
/* 4097 */       if (str68.equals("1") && (j == 0 || !str163.equals(""))) setSystemlanguage(str163); 
/* 4098 */       setIsoracle(bool1);
/*      */       
/* 4100 */       if (!str197.equals("")) {
/* 4101 */         setOrderby(str197);
/*      */       } else {
/*      */         
/* 4104 */         setOrderby("id");
/*      */       } 
/*      */     } 
/*      */   }
/*      */   
/*      */   public String getGroupid() {
/* 4110 */     return this.groupid;
/*      */   }
/*      */   
/*      */   public void setGroupid(String paramString) {
/* 4114 */     this.groupid = paramString;
/*      */   }
/*      */   public String getGroupVaild() {
/* 4117 */     return this.groupVaild;
/*      */   }
/*      */   public void setGroupVaild(String paramString) {
/* 4120 */     this.groupVaild = paramString;
/*      */   }
/*      */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/search/HrmSearchComInfo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */