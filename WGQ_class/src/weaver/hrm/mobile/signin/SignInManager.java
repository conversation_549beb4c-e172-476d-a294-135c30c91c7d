/*     */ package weaver.hrm.mobile.signin;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.Calendar;
/*     */ import java.util.Date;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.common.DateUtil;
/*     */ import weaver.common.StringUtil;
/*     */ import weaver.common.util.xtree.TreeNode;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.ThreadVarLanguage;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ import weaver.mobile.sign.ISignType;
/*     */ import weaver.mobile.sign.SignService;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class SignInManager
/*     */ {
/*     */   public Map getData(String paramString1, String paramString2, String paramString3) {
/*  29 */     return getData(paramString1, paramString2, paramString3, null);
/*     */   }
/*     */   
/*     */   public Map getData(String paramString1, String paramString2, String paramString3, String paramString4) {
/*  33 */     return getData(paramString1, paramString2, paramString3, 0, 0, paramString4);
/*     */   }
/*     */   
/*     */   public Map getData(String paramString1, String paramString2, String paramString3, int paramInt1, int paramInt2) {
/*  37 */     return getData(paramString1, paramString2, paramString3, paramInt1, paramInt2, null);
/*     */   }
/*     */   
/*     */   public Map getData(String paramString1, String paramString2, String paramString3, int paramInt1, int paramInt2, String paramString4) {
/*  41 */     return SignService.getSign(paramString1, paramString2, paramString3, paramInt1, paramInt2, paramString4);
/*     */   }
/*     */   
/*     */   public Map<String, String> getDate(String paramString1, String paramString2) {
/*  45 */     String str1 = "";
/*  46 */     String str2 = "";
/*  47 */     Date date = null;
/*  48 */     paramString1 = StringUtil.vString(paramString1, "tv_today");
/*  49 */     paramString2 = StringUtil.vString(paramString2, DateUtil.getCurrentDate());
/*  50 */     if (paramString1.equals("tv_today") || paramString1.equals("tv_yesterday") || paramString1.equals("tv_cDate")) {
/*  51 */       str1 = paramString2;
/*  52 */       str2 = paramString2;
/*  53 */     } else if (paramString1.equals("tv_thisYear") || paramString1.equals("tv_lastYear") || paramString1.equals("tv_beforeLastYear")) {
/*  54 */       date = DateUtil.parseToDate(paramString2 + "-01-01");
/*  55 */       str1 = DateUtil.getFirstDayOfYear(date);
/*  56 */       str2 = DateUtil.getLastDayOfYear(date);
/*  57 */     } else if (paramString1.startsWith("tv_thisMonth_") || paramString1.startsWith("tv_lastMonth_") || paramString1.startsWith("tv_beforeLastMonth_")) {
/*  58 */       String str = DateUtil.getYear();
/*  59 */       if (paramString1.startsWith("tv_lastMonth_")) {
/*  60 */         str = DateUtil.getYear(DateUtil.addYear(DateUtil.getCalendar(), -1));
/*  61 */       } else if (paramString1.startsWith("tv_beforeLastMonth_")) {
/*  62 */         str = DateUtil.getYear(DateUtil.addYear(DateUtil.getCalendar(), -2));
/*     */       } 
/*  64 */       date = DateUtil.parseToDate(str + "-" + ((paramString2.length() == 1) ? ("0" + paramString2) : paramString2) + "-01");
/*  65 */       str1 = DateUtil.getDate(DateUtil.getFirstDayOfMonth(date));
/*  66 */       str2 = DateUtil.getDate(DateUtil.getLastDayOfMonth(date));
/*     */     } 
/*  68 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  69 */     hashMap.put("beginDate", str1);
/*  70 */     hashMap.put("endDate", str2);
/*  71 */     return (Map)hashMap;
/*     */   }
/*     */   
/*     */   public String getShowName(ISignType paramISignType, User paramUser) {
/*  75 */     String str = paramISignType.getShowName();
/*     */     
/*  77 */     if (StringUtil.isNull(str)) {
/*  78 */       if ("hrm_sign".equalsIgnoreCase(paramISignType.getType())) {
/*  79 */         str = SystemEnv.getHtmlLabelName(132104, paramUser.getLanguage());
/*  80 */       } else if ("visit_customer".equalsIgnoreCase(paramISignType.getType())) {
/*  81 */         str = SystemEnv.getHtmlLabelName(502034, paramUser.getLanguage());
/*  82 */       } else if ("mobile_sign".equalsIgnoreCase(paramISignType.getType())) {
/*  83 */         str = SystemEnv.getHtmlLabelName(132105, paramUser.getLanguage());
/*  84 */       } else if ("e9_mobile_out".equalsIgnoreCase(paramISignType.getType())) {
/*  85 */         str = SystemEnv.getHtmlLabelName(518268, paramUser.getLanguage());
/*     */       } else {
/*  87 */         str = paramISignType.getType();
/*     */       } 
/*     */     }
/*  90 */     return str;
/*     */   }
/*     */   
/*     */   public String getShowName(ISignType paramISignType) {
/*  94 */     String str = paramISignType.getShowName();
/*     */     
/*  96 */     if (StringUtil.isNull(str)) {
/*  97 */       if ("hrm_sign".equalsIgnoreCase(paramISignType.getType())) {
/*  98 */         str = "" + SystemEnv.getHtmlLabelName(10003655, ThreadVarLanguage.getLang()) + "";
/*  99 */       } else if ("visit_customer".equalsIgnoreCase(paramISignType.getType())) {
/* 100 */         str = "" + SystemEnv.getHtmlLabelName(10003656, ThreadVarLanguage.getLang()) + "";
/* 101 */       } else if ("mobile_sign".equalsIgnoreCase(paramISignType.getType())) {
/* 102 */         str = "" + SystemEnv.getHtmlLabelName(10003657, ThreadVarLanguage.getLang()) + "";
/* 103 */       } else if ("e9_mobile_out".equalsIgnoreCase(paramISignType.getType())) {
/* 104 */         str = "" + SystemEnv.getHtmlLabelName(10003657, ThreadVarLanguage.getLang()) + "";
/*     */       } else {
/* 106 */         str = paramISignType.getType();
/*     */       } 
/*     */     }
/* 109 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public String getSubordinateTreeList(String paramString1, String paramString2, int paramInt) throws Exception {
/* 115 */     StringBuffer stringBuffer = new StringBuffer("[");
/* 116 */     if (paramInt == 1) {
/* 117 */       String str = StringUtil.vString(paramString1);
/* 118 */       stringBuffer.append(getNodeStr(str, paramString2, "", true));
/*     */     } else {
/* 120 */       stringBuffer.append(getChildNodes(paramString1, paramString2));
/*     */     } 
/* 122 */     stringBuffer.append("]");
/* 123 */     return stringBuffer.toString();
/*     */   }
/*     */   
/*     */   public String getSubordinateTreeList(String paramString1, String paramString2, int paramInt, boolean paramBoolean) throws Exception {
/* 127 */     String str = "";
/* 128 */     if (paramInt == 1) {
/* 129 */       StringBuffer stringBuffer = new StringBuffer("[");
/* 130 */       RecordSet recordSet = new RecordSet();
/* 131 */       String str1 = "";
/* 132 */       byte b = 0;
/* 133 */       if (recordSet.getDBType().equals("oracle")) {
/* 134 */         str1 = "select id from HrmResource where status in (0,1,2,3) and (managerid is null or managerid<=0 or managerid=id) order by dsporder,id";
/*     */       } else {
/* 136 */         str1 = "select id from HrmResource where status in (0,1,2,3) and (managerid is null or managerid='' or managerid<=0 or managerid=id) order by dsporder,id";
/*     */       } 
/* 138 */       recordSet.executeSql(str1);
/* 139 */       while (recordSet.next()) {
/* 140 */         String str2 = Util.null2String(recordSet.getString("id"));
/* 141 */         if (b) {
/* 142 */           stringBuffer.append(", ");
/*     */         }
/* 144 */         b++;
/* 145 */         stringBuffer.append(getNodeStr(str2, paramString2, "", true));
/*     */       } 
/* 147 */       stringBuffer.append("]");
/* 148 */       str = stringBuffer.toString();
/*     */     } else {
/* 150 */       StringBuffer stringBuffer = new StringBuffer("[");
/* 151 */       stringBuffer.append(getChildNodes(paramString1, paramString2));
/* 152 */       stringBuffer.append("]");
/* 153 */       str = stringBuffer.toString();
/*     */     } 
/* 155 */     return str;
/*     */   }
/*     */   
/*     */   private String getNodeStr(String paramString1, String paramString2, String paramString3, boolean paramBoolean) throws Exception {
/* 159 */     ResourceComInfo resourceComInfo = new ResourceComInfo();
/* 160 */     String str = StringUtil.vString(resourceComInfo.getResourcename(paramString1));
/* 161 */     StringBuffer stringBuffer = new StringBuffer();
/* 162 */     stringBuffer.append("{ ");
/* 163 */     stringBuffer.append("id:\"" + paramString1 + "\", ");
/* 164 */     stringBuffer.append("pId:\"" + paramString3 + "\", ");
/*     */ 
/*     */     
/* 167 */     stringBuffer.append("name:\"");
/* 168 */     stringBuffer.append(Util.replace(Util.replace(Util.replace(Util.replace(
/* 169 */                 Util.replace(str, "<", "&lt;", 0), ">", "&gt;", 0), "&", "&amp;", 0), "'", "&apos;", 0), "\"", "&quot;", 0));
/*     */     
/* 171 */     stringBuffer.append("\", ");
/*     */     
/* 173 */     if (hasChild(paramString1)) {
/* 174 */       if ("i".equals(paramString2)) {
/*     */         
/* 176 */         stringBuffer.append("iconClose:\"/images/treemaker/clsprn_wev8.png\", ");
/*     */         
/* 178 */         stringBuffer.append("iconOpen:\"/images/treemaker/openprn_wev8.png\", ");
/*     */       } 
/*     */       
/* 181 */       stringBuffer.append("ajaxParam:\"id=" + paramString1 + "&isfirst=0&slg=" + paramString2 + "\", ");
/* 182 */       stringBuffer.append("isParent:true, ");
/*     */     }
/* 184 */     else if ("i".equals(paramString2)) {
/*     */       
/* 186 */       stringBuffer.append("icon:\"/images/treemaker/linkprn_wev8.png\", ");
/*     */       
/* 188 */       stringBuffer.append("iconClose:\"/images/treemaker/clsprn_wev8.png\", ");
/*     */       
/* 190 */       stringBuffer.append("iconOpen:\"/images/treemaker/openprn_wev8.png\", ");
/*     */     } 
/*     */ 
/*     */     
/* 194 */     stringBuffer.append("target:\"_self\", ");
/* 195 */     stringBuffer.append("nocheck: ").append(String.valueOf("i".equals(paramString2)));
/* 196 */     if (paramBoolean) {
/* 197 */       stringBuffer.append(",open:true ");
/*     */     }
/*     */     
/* 200 */     stringBuffer.append(" }");
/*     */     
/* 202 */     return stringBuffer.toString();
/*     */   }
/*     */   
/*     */   private boolean hasChild(String paramString) throws Exception {
/* 206 */     ResourceComInfo resourceComInfo = new ResourceComInfo();
/* 207 */     resourceComInfo.setTofirstRow();
/* 208 */     while (resourceComInfo.next()) {
/* 209 */       if (!paramString.equals(resourceComInfo.getResourceid()) && paramString.equals(resourceComInfo.getManagerID()) && ("0".equals(resourceComInfo.getStatus()) || "1".equals(resourceComInfo.getStatus()) || "2".equals(resourceComInfo.getStatus()) || "3".equals(resourceComInfo.getStatus()))) {
/* 210 */         return true;
/*     */       }
/*     */     } 
/* 213 */     return false;
/*     */   }
/*     */   
/*     */   private String getChildNodes(String paramString1, String paramString2) throws Exception {
/* 217 */     StringBuffer stringBuffer = new StringBuffer();
/* 218 */     byte b = 0;
/* 219 */     ResourceComInfo resourceComInfo = new ResourceComInfo();
/* 220 */     resourceComInfo.setTofirstRow();
/* 221 */     while (resourceComInfo.next()) {
/* 222 */       if (!paramString1.equals(resourceComInfo.getResourceid()) && paramString1.equals(resourceComInfo.getManagerID()) && ("0".equals(resourceComInfo.getStatus()) || "1".equals(resourceComInfo.getStatus()) || "2".equals(resourceComInfo.getStatus()) || "3".equals(resourceComInfo.getStatus()))) {
/* 223 */         if (b) {
/* 224 */           stringBuffer.append(", ");
/*     */         }
/* 226 */         b++;
/* 227 */         String str = StringUtil.vString(resourceComInfo.getResourceid());
/* 228 */         stringBuffer.append(getNodeStr(str, paramString2, paramString1, false));
/*     */       } 
/*     */     } 
/* 231 */     return stringBuffer.toString();
/*     */   }
/*     */   
/*     */   private RecordSet getRecordSet(String paramString) {
/* 235 */     RecordSet recordSet = new RecordSet();
/* 236 */     recordSet.executeSql("select id from HrmResource where id <> " + paramString + " and managerid = " + paramString + " and (status =0 or status = 1 or status =2 or status =3) order by dsporder");
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 241 */     return recordSet;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public TreeNode getSubordinateTreeListByCheck(TreeNode paramTreeNode, String paramString) throws Exception {
/* 247 */     ResourceComInfo resourceComInfo = new ResourceComInfo();
/* 248 */     resourceComInfo.setTofirstRow();
/* 249 */     String str = "";
/* 250 */     TreeNode treeNode = null;
/* 251 */     while (resourceComInfo.next()) {
/* 252 */       if (!paramString.equals(resourceComInfo.getResourceid()) && paramString.equals(resourceComInfo.getManagerID()) && ("0".equals(resourceComInfo.getStatus()) || "1".equals(resourceComInfo.getStatus()) || "2".equals(resourceComInfo.getStatus()) || "3".equals(resourceComInfo.getStatus()))) {
/* 253 */         str = StringUtil.vString(resourceComInfo.getResourceid());
/* 254 */         treeNode = new TreeNode();
/* 255 */         treeNode.setTitle(StringUtil.vString(resourceComInfo.getResourcename()));
/* 256 */         treeNode.setNodeId("res_" + str);
/* 257 */         if (hasChild(str)) {
/* 258 */           treeNode.setNodeXmlSrc("/hrm/mobile/signin/subordinateXML.jsp?id=" + str + "&nodeid=" + treeNode.getNodeId() + "&appendthis=false");
/*     */         }
/* 260 */         treeNode.setCheckbox("Y");
/* 261 */         treeNode.setValue(str);
/* 262 */         treeNode.setOncheck("check(" + treeNode.getNodeId() + ")");
/*     */         
/* 264 */         paramTreeNode.addTreeNode(treeNode);
/*     */       } 
/*     */     } 
/* 267 */     return paramTreeNode;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> filtrateMonths(String paramString1, String paramString2) {
/* 278 */     StringBuffer stringBuffer = (new StringBuffer()).append(" select signDate as operate_date from HrmScheduleSign").append(" where userId = '").append(paramString1).append("' AND LONGITUDE!='' and LATITUDE!=''").append(" and substring(signDate, 0, 5) >= '").append(paramString2).append("' UNION").append(" select operate_date from mobile_sign t").append(" WHERE t.operater = '").append(paramString1).append("'").append(" and substring(operate_date, 0, 5) >= '").append(paramString2).append("'").append(" ORDER BY operate_date");
/* 279 */     return getFiltrateMonths(stringBuffer.toString(), paramString2);
/*     */   }
/*     */ 
/*     */   
/*     */   private Map<String, Object> getFiltrateMonths(String paramString1, String paramString2) {
/* 284 */     ArrayList<String> arrayList1 = new ArrayList();
/* 285 */     ArrayList<String> arrayList2 = new ArrayList();
/* 286 */     ArrayList<String> arrayList3 = new ArrayList();
/* 287 */     boolean bool = false;
/* 288 */     RecordSet recordSet = new RecordSet();
/* 289 */     recordSet.executeSql(paramString1);
/*     */     
/* 291 */     String str1 = "", str2 = "", str3 = paramString2;
/* 292 */     Calendar calendar = DateUtil.getCalendar();
/* 293 */     str1 = DateUtil.getYear(calendar);
/* 294 */     String str4 = DateUtil.getYesterday();
/* 295 */     str2 = DateUtil.getYear(DateUtil.addYear(calendar, -1));
/*     */     
/* 297 */     String[] arrayOfString = new String[0];
/* 298 */     String str5 = "";
/* 299 */     while (recordSet.next()) {
/* 300 */       str5 = StringUtil.vString(recordSet.getString(1));
/* 301 */       arrayOfString = str5.split("-");
/* 302 */       if (arrayOfString.length < 3)
/* 303 */         continue;  if (!bool && str5.equals(str4)) bool = true; 
/* 304 */       if (arrayOfString[0].equals(str1)) { arrayList1.add(arrayOfString[1]); continue; }
/* 305 */        if (arrayOfString[0].equals(str2)) { arrayList2.add(arrayOfString[1]); continue; }
/* 306 */        if (arrayOfString[0].equals(str3)) arrayList3.add(arrayOfString[1]);
/*     */     
/*     */     } 
/* 309 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 310 */     hashMap.put("showYesterday", Boolean.valueOf(bool));
/* 311 */     hashMap.put("thisMonths", arrayList1);
/* 312 */     hashMap.put("lastMonths", arrayList2);
/* 313 */     hashMap.put("beforeLastMonths", arrayList3);
/* 314 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/mobile/signin/SignInManager.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */