/*    */ package weaver.hrm.contract;
/*    */ 
/*    */ import weaver.conn.RecordSet;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class ContractTempletComInfo
/*    */ {
/* 10 */   RecordSet rs = new RecordSet();
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getContractTempletname(String paramString) {
/* 20 */     String str1 = "select templetname from HrmContracttemplet where id = '" + paramString + "'";
/* 21 */     this.rs.executeSql(str1);
/* 22 */     String str2 = "";
/* 23 */     if (this.rs.next()) {
/* 24 */       str2 = this.rs.getString("templetname");
/*    */     }
/* 26 */     return str2;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/contract/ContractTempletComInfo.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */