/*    */ package weaver.hrm.contract;
/*    */ 
/*    */ import weaver.conn.RecordSet;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class ContractTypeComInfo
/*    */ {
/* 10 */   RecordSet rs = new RecordSet();
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getContractTypename(String paramString) {
/* 20 */     String str1 = "select typename from HrmContractType where id = '" + paramString + "'";
/* 21 */     this.rs.executeSql(str1);
/* 22 */     String str2 = "";
/* 23 */     while (this.rs.next()) {
/* 24 */       str2 = this.rs.getString("typename");
/*    */     }
/* 26 */     return str2;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public boolean isHireContract(int paramInt) {
/* 35 */     String str = "select ishirecontract from HrmContractType where id='" + paramInt + "'";
/* 36 */     this.rs.executeSql(str);
/* 37 */     while (this.rs.next()) {
/* 38 */       String str1 = this.rs.getString("ishirecontract");
/* 39 */       if (str1.equals("1")) {
/* 40 */         return true;
/*    */       }
/*    */     } 
/* 43 */     return false;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getRemindMan(String paramString) {
/* 52 */     String str1 = "select remindman from HrmContractType where id = '" + paramString + "'";
/* 53 */     this.rs.executeSql(str1);
/* 54 */     String str2 = "";
/* 55 */     while (this.rs.next()) {
/* 56 */       str2 = this.rs.getString("remindman");
/*    */     }
/* 58 */     return str2;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public int getRemindAheadDate(String paramString) {
/* 68 */     String str = "select remindaheaddate from HrmContractType where id = '" + paramString + "'";
/* 69 */     this.rs.executeSql(str);
/* 70 */     int i = 3;
/* 71 */     while (this.rs.next()) {
/* 72 */       i = this.rs.getInt("remindaheaddate");
/*    */     }
/* 74 */     return i;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/contract/ContractTypeComInfo.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */