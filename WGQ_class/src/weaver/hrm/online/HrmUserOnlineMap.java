/*     */ package weaver.hrm.online;
/*     */ 
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import com.alibaba.fastjson.TypeReference;
/*     */ import com.engine.hrm.cmd.securitysetting.UnLockPasswordTimer;
/*     */ import com.engine.hrm.cmd.securitysetting.UnRegPwdLockTimer;
/*     */ import java.util.ArrayList;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import java.util.Set;
/*     */ import java.util.concurrent.ConcurrentHashMap;
/*     */ import org.apache.commons.beanutils.BeanUtils;
/*     */ import org.apache.commons.lang.StringUtils;
/*     */ import weaver.cluster.CacheManager;
/*     */ import weaver.common.StringUtil;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.StaticObj;
/*     */ import weaver.general.Util;
/*     */ import weaver.general.WArrayList;
/*     */ import weaver.general.WHashMap;
/*     */ import weaver.hrm.cachecenter.CacheLoadQueueManager;
/*     */ import weaver.hrm.online.schedule.HrmUserOnlineTimer;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ import weaver.systeminfo.SysMaintenanceLog;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ public class HrmUserOnlineMap
/*     */   extends BaseBean
/*     */ {
/*  32 */   private static Map<Integer, HrmResourceOnlineBean> CacheMap = new ConcurrentHashMap<>();
/*     */   
/*  34 */   private static Map<Integer, String> ClienIpMap = new ConcurrentHashMap<>();
/*     */ 
/*     */   
/*  37 */   private static Map<Integer, Long> OffLineMap = new ConcurrentHashMap<>();
/*     */   
/*  39 */   private static Map<Integer, HrmResourceOnlineBean> ClusterMap = new ConcurrentHashMap<>();
/*     */   
/*  41 */   private static HrmUserOnlineMap instance = new HrmUserOnlineMap();
/*     */ 
/*     */ 
/*     */   
/*     */   static {
/*  46 */     HrmUserOnlineTimer.getInstance();
/*     */     
/*  48 */     UnLockPasswordTimer.getInstance();
/*     */ 
/*     */ 
/*     */     
/*  52 */     UnRegPwdLockTimer.getInstance();
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  57 */     CacheLoadQueueManager.start();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static HrmUserOnlineMap getInstance() {
/*  65 */     return instance;
/*     */   }
/*     */   
/*     */   public String getCacheMapJSON() {
/*  69 */     return JSONObject.toJSONString(CacheMap);
/*     */   }
/*     */ 
/*     */   
/*     */   public void userOffline(String paramString) {
/*  74 */     String[] arrayOfString = CacheManager.getInstance().getSyncHosts();
/*  75 */     Set set = CacheManager.getInstance().getBadHosts();
/*  76 */     String str = "";
/*  77 */     for (byte b = 0; b < arrayOfString.length; b++) {
/*  78 */       if (!set.contains(arrayOfString[b])) {
/*     */ 
/*     */         
/*  81 */         str = arrayOfString[b];
/*  82 */         String str1 = "http://" + str + "/login/LoginOperation.jsp?method=userOffline&uid=" + paramString + "&token=" + getToken();
/*     */         try {
/*  84 */           HttpRequestUtils.httpGet(str1);
/*  85 */         } catch (Exception exception) {
/*  86 */           writeLog("集群强制下线异常----" + str + ";;;uid=" + paramString, exception);
/*     */         } 
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   public String getCluterMapJSON() {
/*  94 */     return JSONObject.toJSONString(ClusterMap);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<Integer, HrmResourceOnlineBean> getCacheMap() {
/* 103 */     return CacheMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<Integer, Long> getOffLineMap() {
/* 112 */     return OffLineMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<Integer, String> getClientIpMap() {
/* 121 */     return ClienIpMap;
/*     */   }
/*     */   
/*     */   public boolean islocalMachine(String paramString) {
/* 125 */     boolean bool = false;
/* 126 */     if (StringUtils.isNotEmpty(paramString)) {
/* 127 */       String str = IPUtil.getLocalIp();
/* 128 */       if (paramString.trim().equals(str)) {
/* 129 */         bool = true;
/*     */       }
/*     */     } 
/* 132 */     return bool;
/*     */   }
/*     */ 
/*     */   
/*     */   public List<String> getAllActiveMachine() {
/* 137 */     String[] arrayOfString = CacheManager.getInstance().getSyncHosts();
/* 138 */     Set set = CacheManager.getInstance().getBadHosts();
/* 139 */     ArrayList<String> arrayList = new ArrayList();
/* 140 */     for (byte b = 0; b < arrayOfString.length; b++) {
/* 141 */       if (!set.contains(arrayOfString[b])) {
/*     */ 
/*     */         
/* 144 */         String str1 = arrayOfString[b].split(":")[0];
/* 145 */         if (!arrayList.contains(str1.trim())) arrayList.add(str1.trim()); 
/*     */       } 
/* 147 */     }  String str = IPUtil.getLocalIp();
/* 148 */     if (!arrayList.contains(str)) arrayList.add(str); 
/* 149 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getClientIpByUid(Integer paramInteger) {
/* 159 */     return ClienIpMap.get(paramInteger);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getCurrentOnlineCount() {
/* 168 */     return CacheMap.size();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getUserOnlineCount() {
/* 177 */     return ClusterMap.size();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void updateOnlineDate(Integer paramInteger) {
/* 187 */     updateOnlineDate(paramInteger, "");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void updateOnlineDate(Integer paramInteger, String paramString) {
/*     */     try {
/* 197 */       ResourceComInfo resourceComInfo = new ResourceComInfo();
/* 198 */       String str1 = Util.null2String(resourceComInfo.getSubCompanyID("" + paramInteger));
/* 199 */       String str2 = Util.null2String(resourceComInfo.getDepartmentID("" + paramInteger));
/* 200 */       if (resourceComInfo.getAccountType("" + paramInteger).equals("1") || ((str1
/* 201 */         .length() == 0 || str1.equals("0")) && (str2.length() == 0 || str2.equals("0")))) {
/*     */         return;
/*     */       }
/* 204 */     } catch (Exception exception) {
/* 205 */       writeLog("剔除外部人员、次账号异常----" + paramInteger, exception);
/*     */     } 
/* 207 */     HrmResourceOnlineBean hrmResourceOnlineBean = new HrmResourceOnlineBean();
/* 208 */     hrmResourceOnlineBean.setServerIp(IPUtil.getLocalIp());
/* 209 */     hrmResourceOnlineBean.setUserid(paramInteger);
/* 210 */     hrmResourceOnlineBean.setTime(System.currentTimeMillis());
/* 211 */     hrmResourceOnlineBean.setClientIp(paramString);
/* 212 */     CacheMap.put(paramInteger, hrmResourceOnlineBean);
/* 213 */     ClienIpMap.put(paramInteger, paramString);
/* 214 */     ClusterMap.put(paramInteger, hrmResourceOnlineBean);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean isOnline(int paramInt) {
/* 226 */     return ClusterMap.containsKey(Integer.valueOf(paramInt));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<Integer, HrmResourceOnlineBean> getClusterMap() {
/* 236 */     return ClusterMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getClientIpByUidFromClusterMap(String paramString) {
/* 245 */     String str = "";
/* 246 */     int i = StringUtil.parseToInt(paramString);
/* 247 */     if (isOnline(i)) {
/* 248 */       HrmResourceOnlineBean hrmResourceOnlineBean = ClusterMap.get(Integer.valueOf(i));
/* 249 */       str = (hrmResourceOnlineBean == null) ? "" : StringUtil.vString(hrmResourceOnlineBean.getClientIp());
/*     */     } 
/* 251 */     return str;
/*     */   }
/*     */ 
/*     */   
/*     */   public void syncClusterMap() {
/* 256 */     getAllNodeOnlineMap();
/*     */     
/* 258 */     setOnlineUserIds();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void updateAllOnlineUser() {
/* 266 */     long l = System.currentTimeMillis();
/* 267 */     for (Map.Entry<Integer, HrmResourceOnlineBean> entry : CacheMap.entrySet()) {
/* 268 */       Integer integer = (Integer)entry.getKey();
/* 269 */       HrmResourceOnlineBean hrmResourceOnlineBean = (HrmResourceOnlineBean)entry.getValue();
/* 270 */       if (hrmResourceOnlineBean != null && 
/* 271 */         l - hrmResourceOnlineBean.getTime() > 300000L) {
/* 272 */         updateOffline(integer);
/*     */       }
/*     */     } 
/*     */ 
/*     */     
/*     */     try {
/* 278 */       if (StaticObj.getInstance().isCluster()) {
/* 279 */         getAllNodeOnlineMap();
/*     */       }
/* 281 */     } catch (Exception exception) {
/* 282 */       writeLog(exception);
/*     */     } 
/*     */     
/* 285 */     setOnlineUserIds();
/*     */   }
/*     */   
/*     */   public void updateOffline(Integer paramInteger) {
/* 289 */     updateOffline(paramInteger, true);
/*     */   }
/*     */   public void updateOffline(Integer paramInteger, boolean paramBoolean) {
/* 292 */     if (paramBoolean) {
/*     */       try {
/* 294 */         ResourceComInfo resourceComInfo = new ResourceComInfo();
/*     */         
/* 296 */         SysMaintenanceLog sysMaintenanceLog = new SysMaintenanceLog();
/* 297 */         sysMaintenanceLog.resetParameter();
/* 298 */         sysMaintenanceLog.setRelatedId(paramInteger.intValue());
/* 299 */         sysMaintenanceLog.setRelatedName(resourceComInfo.getLastname());
/* 300 */         sysMaintenanceLog.setOperateType("303");
/* 301 */         sysMaintenanceLog.setOperateDesc(SystemEnv.getHtmlLabelName(25149, 7));
/* 302 */         sysMaintenanceLog.setOperateItem("505");
/* 303 */         sysMaintenanceLog.setOperateUserid(paramInteger.intValue());
/* 304 */         sysMaintenanceLog.setClientAddress(ClienIpMap.get(paramInteger));
/* 305 */         sysMaintenanceLog.setClientType(1);
/* 306 */         sysMaintenanceLog.setSysLogInfo();
/*     */       }
/* 308 */       catch (Exception exception) {
/* 309 */         writeLog(exception);
/*     */       } 
/*     */     }
/* 312 */     CacheMap.remove(paramInteger);
/* 313 */     ClienIpMap.remove(paramInteger);
/* 314 */     ClusterMap.remove(paramInteger);
/*     */     
/* 316 */     setOnlineUserIds();
/* 317 */     OffLineMap.put(paramInteger, Long.valueOf(System.currentTimeMillis()));
/*     */   }
/*     */   
/*     */   public static String getToken() {
/* 321 */     RecordSet recordSet = new RecordSet();
/* 322 */     recordSet.executeQuery("select PASSWORD from HRMRESOURCEMANAGER where id=1", new Object[0]);
/* 323 */     String str = "weaver";
/* 324 */     if (recordSet.next()) {
/* 325 */       String str1 = Util.null2String(recordSet.getString("PASSWORD"));
/* 326 */       if (StringUtils.isNotBlank(str1)) {
/* 327 */         str = str + str1;
/*     */       }
/*     */     } 
/*     */     
/* 331 */     return Util.getEncrypt(str);
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   private void getAllNodeOnlineMap() {
/* 337 */     String[] arrayOfString = CacheManager.getInstance().getSyncHosts();
/* 338 */     Set set = CacheManager.getInstance().getBadHosts();
/* 339 */     String str = "";
/* 340 */     ConcurrentHashMap<Object, Object> concurrentHashMap = new ConcurrentHashMap<>();
/* 341 */     for (byte b = 0; b < arrayOfString.length; b++) {
/* 342 */       if (!set.contains(arrayOfString[b])) {
/*     */ 
/*     */         
/* 345 */         str = arrayOfString[b];
/*     */         try {
/* 347 */           String str1 = "http://" + str + "/login/LoginOperation.jsp?method=syncOnline&token=" + getToken();
/* 348 */           String str2 = HttpRequestUtils.httpGet(str1);
/* 349 */           Map<Integer, HrmResourceOnlineBean> map = (Map)JSONObject.parseObject(str2, new TypeReference<Map<Integer, HrmResourceOnlineBean>>() {  }, new com.alibaba.fastjson.parser.Feature[0]);
/*     */           
/* 351 */           if (map != null) {
/* 352 */             tranMapToTmpMap((Map)concurrentHashMap, map);
/*     */           }
/* 354 */         } catch (Exception exception) {
/* 355 */           writeLog("在线用户数同步异常----" + str, exception);
/*     */         } 
/*     */       } 
/* 358 */     }  tranMapToTmpMap((Map)concurrentHashMap, CacheMap);
/* 359 */     ClusterMap = (Map)concurrentHashMap;
/*     */   }
/*     */   
/*     */   private void tranMapToTmpMap(Map<Integer, HrmResourceOnlineBean> paramMap1, Map<Integer, HrmResourceOnlineBean> paramMap2) {
/* 363 */     for (Map.Entry<Integer, HrmResourceOnlineBean> entry : paramMap2.entrySet()) {
/*     */       try {
/* 365 */         Integer integer = (Integer)entry.getKey();
/* 366 */         HrmResourceOnlineBean hrmResourceOnlineBean1 = (HrmResourceOnlineBean)entry.getValue();
/* 367 */         if (paramMap1.get(integer) != null) {
/* 368 */           HrmResourceOnlineBean hrmResourceOnlineBean3 = paramMap1.get(integer);
/* 369 */           hrmResourceOnlineBean3.setServerIp(hrmResourceOnlineBean3.getServerIp() + "," + hrmResourceOnlineBean1.getServerIp());
/* 370 */           HrmResourceOnlineBean hrmResourceOnlineBean4 = new HrmResourceOnlineBean();
/* 371 */           BeanUtils.copyProperties(hrmResourceOnlineBean4, hrmResourceOnlineBean3);
/* 372 */           paramMap1.put(integer, hrmResourceOnlineBean4); continue;
/*     */         } 
/* 374 */         HrmResourceOnlineBean hrmResourceOnlineBean2 = new HrmResourceOnlineBean();
/* 375 */         BeanUtils.copyProperties(hrmResourceOnlineBean2, hrmResourceOnlineBean1);
/* 376 */         paramMap1.put(integer, hrmResourceOnlineBean2);
/*     */       }
/* 378 */       catch (Exception exception) {
/* 379 */         writeLog("tranMapToTmpMap----", exception);
/*     */       } 
/*     */     } 
/*     */   }
/*     */   
/*     */   private void setOnlineUserIds() {
/*     */     try {
/* 386 */       WArrayList<String> wArrayList = new WArrayList();
/* 387 */       for (Map.Entry<Integer, HrmResourceOnlineBean> entry : ClusterMap.entrySet()) {
/* 388 */         wArrayList.add("" + entry.getKey());
/*     */       }
/*     */       
/* 391 */       StaticObj.getInstance().putObject("onlineuserids", wArrayList);
/*     */       
/* 393 */       WHashMap wHashMap = new WHashMap();
/* 394 */       for (Map.Entry<Integer, String> entry : ClienIpMap.entrySet()) {
/* 395 */         wHashMap.put(entry.getKey(), entry.getValue());
/*     */       }
/*     */       
/* 398 */       StaticObj.getInstance().putObject("onlineuserips", wHashMap);
/* 399 */     } catch (Exception exception) {
/* 400 */       writeLog("onlineuserids init error:" + exception);
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/online/HrmUserOnlineMap.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */