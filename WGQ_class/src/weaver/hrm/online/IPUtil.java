/*    */ package weaver.hrm.online;
/*    */ 
/*    */ import java.net.InetAddress;
/*    */ import java.net.NetworkInterface;
/*    */ import java.net.SocketException;
/*    */ import java.util.ArrayList;
/*    */ import java.util.Enumeration;
/*    */ import java.util.List;
/*    */ import java.util.regex.Pattern;
/*    */ import javax.servlet.http.HttpServletRequest;
/*    */ import org.apache.commons.lang3.StringUtils;
/*    */ import weaver.general.BaseBean;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class IPUtil
/*    */ {
/*    */   public static String getIp(HttpServletRequest paramHttpServletRequest) {
/* 28 */     String str = paramHttpServletRequest.getHeader("Cdn-Src-Ip");
/* 29 */     if (StringUtils.isEmpty(str) || "unknown".equalsIgnoreCase(str)) {
/* 30 */       str = paramHttpServletRequest.getHeader("X-Forwarded-For");
/*    */     }
/* 32 */     if (StringUtils.isEmpty(str) || "unknown".equalsIgnoreCase(str)) {
/* 33 */       str = paramHttpServletRequest.getHeader("Proxy-Client-IP");
/*    */     }
/* 35 */     if (StringUtils.isEmpty(str) || "unknown".equalsIgnoreCase(str)) {
/* 36 */       str = paramHttpServletRequest.getHeader("WL-Proxy-Client-IP");
/*    */     }
/* 38 */     if (StringUtils.isEmpty(str) || "unknown".equalsIgnoreCase(str)) {
/* 39 */       str = paramHttpServletRequest.getHeader("HTTP_CLIENT_IP");
/*    */     }
/* 41 */     if (StringUtils.isEmpty(str) || "unknown".equalsIgnoreCase(str)) {
/* 42 */       str = paramHttpServletRequest.getHeader("HTTP_X_FORWARDED_FOR");
/*    */     }
/* 44 */     if (StringUtils.isEmpty(str) || "unknown".equalsIgnoreCase(str)) {
/* 45 */       str = paramHttpServletRequest.getRemoteAddr();
/*    */     }
/* 47 */     return str;
/*    */   }
/* 49 */   private static String localip = "";
/*    */   public static String getLocalIp() {
/* 51 */     if (StringUtils.isNotEmpty(localip)) {
/* 52 */       return localip;
/*    */     }
/*    */     
/*    */     try {
/* 56 */       InetAddress inetAddress = InetAddress.getLocalHost();
/* 57 */       localip = inetAddress.getHostAddress();
/* 58 */     } catch (Exception exception) {
/* 59 */       exception.printStackTrace();
/*    */     } 
/*    */     
/* 62 */     if (StringUtils.isEmpty(localip)) {
/* 63 */       List<String> list = getLocalIpList();
/* 64 */       if (list != null && list.size() > 0) {
/* 65 */         localip = list.get(0);
/*    */       }
/*    */     } 
/* 68 */     return localip;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public static List<String> getLocalIpList() {
/* 77 */     ArrayList<String> arrayList = new ArrayList();
/* 78 */     Pattern pattern = Pattern.compile("\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}");
/*    */     
/*    */     try {
/* 81 */       Enumeration<NetworkInterface> enumeration = NetworkInterface.getNetworkInterfaces();
/* 82 */       while (enumeration.hasMoreElements()) {
/* 83 */         Enumeration<InetAddress> enumeration1 = ((NetworkInterface)enumeration.nextElement()).getInetAddresses();
/* 84 */         while (enumeration1.hasMoreElements()) {
/* 85 */           String str = ((InetAddress)enumeration1.nextElement()).getHostAddress();
/* 86 */           if (pattern.matcher(str).matches()) {
/* 87 */             arrayList.add(str);
/*    */           }
/*    */         } 
/*    */       } 
/* 91 */     } catch (SocketException socketException) {
/* 92 */       (new BaseBean()).writeLog(socketException.getClass().getName(), socketException);
/*    */     } finally {
/* 94 */       for (String str : arrayList) {
/* 95 */         (new BaseBean()).writeLog("本机多网上Ip集：==========={}=======", str);
/*    */       }
/*    */     } 
/* 98 */     return arrayList;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/online/IPUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */