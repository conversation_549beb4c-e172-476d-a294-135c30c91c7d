/*     */ package weaver.hrm.online;
/*     */ 
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import java.io.IOException;
/*     */ import java.net.URLDecoder;
/*     */ import org.apache.http.HttpEntity;
/*     */ import org.apache.http.client.methods.CloseableHttpResponse;
/*     */ import org.apache.http.client.methods.HttpGet;
/*     */ import org.apache.http.client.methods.HttpPost;
/*     */ import org.apache.http.client.methods.HttpUriRequest;
/*     */ import org.apache.http.entity.StringEntity;
/*     */ import org.apache.http.impl.client.CloseableHttpClient;
/*     */ import org.apache.http.impl.client.HttpClients;
/*     */ import org.apache.http.util.EntityUtils;
/*     */ import weaver.general.BaseBean;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HttpRequestUtils
/*     */ {
/*     */   public static JSONObject httpPost(String paramString, JSONObject paramJSONObject) {
/*  27 */     return httpPost(paramString, paramJSONObject, false);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static JSONObject httpPost(String paramString, JSONObject paramJSONObject, boolean paramBoolean) {
/*  40 */     CloseableHttpClient closeableHttpClient = HttpClients.createDefault();
/*  41 */     JSONObject jSONObject = null;
/*  42 */     HttpPost httpPost = new HttpPost(paramString);
/*     */     try {
/*  44 */       if (null != paramJSONObject) {
/*     */         
/*  46 */         StringEntity stringEntity = new StringEntity(paramJSONObject.toString(), "utf-8");
/*  47 */         stringEntity.setContentEncoding("UTF-8");
/*  48 */         stringEntity.setContentType("application/json");
/*  49 */         httpPost.setEntity((HttpEntity)stringEntity);
/*     */       } 
/*  51 */       CloseableHttpResponse closeableHttpResponse = closeableHttpClient.execute((HttpUriRequest)httpPost);
/*  52 */       paramString = URLDecoder.decode(paramString, "UTF-8");
/*     */       
/*  54 */       if (closeableHttpResponse.getStatusLine().getStatusCode() == 200) {
/*  55 */         String str = "";
/*     */         
/*     */         try {
/*  58 */           str = EntityUtils.toString(closeableHttpResponse.getEntity());
/*  59 */           if (paramBoolean) {
/*  60 */             return null;
/*     */           }
/*     */           
/*  63 */           jSONObject = JSONObject.parseObject(str);
/*  64 */         } catch (Exception exception) {
/*  65 */           (new BaseBean()).writeLog("post请求提交失败:" + paramString, exception);
/*     */         } 
/*     */       } 
/*  68 */     } catch (IOException iOException) {
/*  69 */       (new BaseBean()).writeLog("post请求提交失败:" + paramString, iOException);
/*     */     } 
/*  71 */     return jSONObject;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String httpGet(String paramString) {
/*  83 */     String str = "";
/*     */     
/*     */     try {
/*  86 */       CloseableHttpClient closeableHttpClient = HttpClients.createDefault();
/*     */       
/*  88 */       HttpGet httpGet = new HttpGet(paramString);
/*  89 */       CloseableHttpResponse closeableHttpResponse = closeableHttpClient.execute((HttpUriRequest)httpGet);
/*     */ 
/*     */       
/*  92 */       if (closeableHttpResponse.getStatusLine().getStatusCode() == 200) {
/*     */         
/*  94 */         str = EntityUtils.toString(closeableHttpResponse.getEntity());
/*     */ 
/*     */         
/*  97 */         paramString = URLDecoder.decode(paramString, "UTF-8");
/*     */       } else {
/*  99 */         (new BaseBean()).writeLog("get请求提交失败:" + paramString);
/*     */       } 
/* 101 */     } catch (IOException iOException) {
/* 102 */       (new BaseBean()).writeLog("get请求提交失败:" + paramString, iOException);
/*     */     } 
/* 104 */     return str;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/online/HttpRequestUtils.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */