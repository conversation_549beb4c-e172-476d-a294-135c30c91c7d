/*    */ package weaver.hrm.online.schedule;
/*    */ 
/*    */ import java.util.Timer;
/*    */ import org.apache.log4j.Logger;
/*    */ 
/*    */ public class HrmUserOnlineTimer
/*    */ {
/*  8 */   private static final Logger log = Logger.getLogger(HrmUserOnlineTimer.class); private Timer timer;
/*  9 */   private static HrmUserOnlineTimer instance = new HrmUserOnlineTimer();
/*    */   
/*    */   private HrmUserOnlineTimer() {
/* 12 */     init();
/*    */   }
/*    */   public static HrmUserOnlineTimer getInstance() {
/* 15 */     return instance;
/*    */   }
/*    */   
/*    */   private void init() {
/* 19 */     this.timer = new Timer();
/* 20 */     this.timer.schedule(new HrmUserOnlineSchedule(), 3000L, 300000L);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/online/schedule/HrmUserOnlineTimer.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */