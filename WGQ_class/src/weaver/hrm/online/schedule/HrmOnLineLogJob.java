/*    */ package weaver.hrm.online.schedule;
/*    */ 
/*    */ import java.util.ArrayList;
/*    */ import java.util.Iterator;
/*    */ import java.util.Map;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.BaseBean;
/*    */ import weaver.hrm.online.HrmResourceOnlineBean;
/*    */ import weaver.hrm.online.HrmUserOnlineMap;
/*    */ import weaver.interfaces.schedule.BaseCronJob;
/*    */ 
/*    */ 
/*    */ public class HrmOnLineLogJob
/*    */   extends BaseCronJob
/*    */ {
/*    */   public void execute() {
/*    */     try {
/* 18 */       (new BaseBean()).writeLog("HrmOnLineLogJob>>>start");
/* 19 */       String str = "";
/* 20 */       RecordSet recordSet = new RecordSet();
/* 21 */       ArrayList<ArrayList<Integer>> arrayList = new ArrayList();
/* 22 */       ArrayList<Integer> arrayList1 = null;
/* 23 */       Map map1 = HrmUserOnlineMap.getInstance().getClusterMap();
/* 24 */       for (Map.Entry entry : map1.entrySet()) {
/* 25 */         arrayList1 = new ArrayList();
/* 26 */         int i = ((Integer)entry.getKey()).intValue();
/* 27 */         HrmResourceOnlineBean hrmResourceOnlineBean = (HrmResourceOnlineBean)entry.getValue();
/* 28 */         arrayList1.add(Integer.valueOf(i));
/* 29 */         arrayList1.add(Long.valueOf(hrmResourceOnlineBean.getTime()));
/* 30 */         arrayList1.add(Integer.valueOf(1));
/* 31 */         arrayList.add(arrayList1);
/*    */       } 
/*    */       
/* 34 */       ArrayList<Integer> arrayList2 = new ArrayList();
/* 35 */       Map map2 = HrmUserOnlineMap.getInstance().getOffLineMap();
/* 36 */       for (Map.Entry entry : map2.entrySet()) {
/* 37 */         arrayList1 = new ArrayList<>();
/* 38 */         int i = ((Integer)entry.getKey()).intValue();
/* 39 */         long l = ((Long)entry.getValue()).longValue();
/* 40 */         arrayList1.add(Integer.valueOf(i));
/* 41 */         arrayList1.add(Long.valueOf(l));
/* 42 */         arrayList1.add(Integer.valueOf(0));
/* 43 */         arrayList.add(arrayList1);
/* 44 */         arrayList2.add(Integer.valueOf(i));
/*    */       } 
/* 46 */       for (Iterator<Integer> iterator = arrayList2.iterator(); iterator.hasNext(); ) { int i = ((Integer)iterator.next()).intValue();
/* 47 */         map2.remove(Integer.valueOf(i)); }
/*    */       
/* 49 */       str = " insert into hrm_online_log(resourceId,onlineTime,status)values(?,?,?)";
/* 50 */       recordSet.executeBatchSql(str, arrayList);
/* 51 */       (new BaseBean()).writeLog("HrmOnLineLogJob>>>over");
/* 52 */     } catch (Exception exception) {
/* 53 */       (new BaseBean()).writeLog(exception);
/* 54 */       exception.printStackTrace();
/*    */     } 
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/online/schedule/HrmOnLineLogJob.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */