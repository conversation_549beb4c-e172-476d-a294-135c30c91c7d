/*     */ package weaver.hrm.orggroup;
/*     */ 
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.SplitPageParaBean;
/*     */ import weaver.general.SplitPageUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HrmOrgGroupSearchManager
/*     */   extends BaseBean
/*     */ {
/*     */   private RecordSet rs;
/*     */   private int id;
/*     */   private String orgGroupName;
/*     */   private String orgGroupDesc;
/*     */   private double showOrder;
/*  34 */   private int pagenum = 1;
/*     */   
/*  36 */   private int perpage = 0;
/*     */   
/*  38 */   private int recordersize = 0;
/*  39 */   private int start = 1;
/*     */ 
/*     */   
/*     */   SplitPageParaBean spp;
/*     */ 
/*     */   
/*     */   SplitPageUtil spu;
/*     */ 
/*     */ 
/*     */   
/*     */   public HrmOrgGroupSearchManager() {
/*  50 */     this.spp = new SplitPageParaBean();
/*  51 */     this.spu = new SplitPageUtil();
/*     */     
/*  53 */     resetParameter();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void resetParameter() {
/*  60 */     this.recordersize = 0;
/*  61 */     this.start = 1;
/*  62 */     this.perpage = Util.getPerpageLog();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean next() throws Exception {
/*  73 */     if (this.rs.next()) {
/*  74 */       setSearchInfo();
/*  75 */       return true;
/*     */     } 
/*  77 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getId() {
/*  86 */     return this.id;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getOrgGroupName() {
/*  95 */     return this.orgGroupName;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getOrgGroupDesc() {
/* 104 */     return this.orgGroupDesc;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public double getShowOrder() {
/* 113 */     return this.showOrder;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getRecordersize() {
/* 122 */     return (this.recordersize == -1) ? 0 : this.recordersize;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setId(int paramInt) {
/* 132 */     this.id = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setOrgGroupName(String paramString) {
/* 142 */     this.orgGroupName = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setOrgGroupDesc(String paramString) {
/* 152 */     this.orgGroupDesc = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setShowOrder(double paramDouble) {
/* 162 */     this.showOrder = paramDouble;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setStart(int paramInt) {
/* 172 */     this.start = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setPagenum(int paramInt) {
/* 182 */     this.pagenum = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setPerpage(int paramInt) {
/* 192 */     this.perpage = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void setSearchInfo() throws Exception {
/* 200 */     setId(Util.getIntValue(this.rs.getString("id"), 0));
/* 201 */     setOrgGroupName(Util.null2String(this.rs.getString("orgGroupName")));
/* 202 */     setOrgGroupDesc(Util.null2String(this.rs.getString("orgGroupDesc")));
/* 203 */     setShowOrder(Util.getDoubleValue(this.rs.getString("showOrder"), 0.0D));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void getSelectResult(String paramString1, String paramString2, User paramUser) throws Exception {
/* 222 */     String str = "";
/* 223 */     int i = paramString2.indexOf(" by ");
/* 224 */     if (i != -1) {
/*     */       
/* 226 */       str = paramString2.substring(i + 3);
/* 227 */       str = Util.StringReplace(str, " desc", " ");
/* 228 */       str = Util.StringReplace(str, " asc", " ");
/*     */     } 
/*     */     
/* 231 */     if (!paramString1.equals("") && 
/* 232 */       paramString1.toUpperCase().indexOf("WHERE") <= 0)
/*     */     {
/*     */       
/* 235 */       paramString1 = " where " + paramString1;
/*     */     }
/*     */ 
/*     */     
/* 239 */     this.rs = new RecordSet();
/*     */     
/* 241 */     this.rs.executeSql("select count(id) from HrmOrgGroup  t1 " + paramString1);
/*     */     
/* 243 */     if (this.rs.next()) {
/* 244 */       this.recordersize = Util.getIntValue(this.rs.getString(1), 0);
/*     */     }
/* 246 */     this.spp.setBackFields("id,orgGroupName,orgGroupDesc,showOrder");
/*     */     
/* 248 */     this.spp.setSqlFrom("HrmOrgGroup  t1 ");
/* 249 */     this.spp.setSqlWhere(paramString1);
/* 250 */     this.spp.setSqlOrderBy(str);
/* 251 */     this.spp.setPrimaryKey("id");
/* 252 */     this.spp.setDistinct(true);
/* 253 */     this.spp.getClass(); this.spp.setSortWay(0);
/*     */     
/* 255 */     this.spu.setSpp(this.spp);
/* 256 */     this.rs = this.spu.getCurrentPageRs(this.pagenum, this.perpage);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void getSelectResultCount(String paramString, User paramUser) throws Exception {
/* 272 */     if (!paramString.equals("") && 
/* 273 */       paramString.toUpperCase().indexOf("WHERE") <= 0)
/*     */     {
/*     */       
/* 276 */       paramString = " where " + paramString;
/*     */     }
/*     */ 
/*     */     
/* 280 */     this.rs = new RecordSet();
/*     */     
/* 282 */     this.rs.executeSql("select count(id) from HrmOrgGroup  t1 " + paramString);
/*     */     
/* 284 */     this.rs.next();
/* 285 */     this.recordersize = this.rs.getInt(1);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/orggroup/HrmOrgGroupSearchManager.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */