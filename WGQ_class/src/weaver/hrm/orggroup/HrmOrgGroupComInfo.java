/*    */ package weaver.hrm.orggroup;
/*    */ 
/*    */ import weaver.cache.CacheBase;
/*    */ import weaver.cache.CacheColumn;
/*    */ import weaver.cache.CacheColumnType;
/*    */ import weaver.cache.PKColumn;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class HrmOrgGroupComInfo
/*    */   extends CacheBase
/*    */ {
/* 21 */   protected static String TABLE_NAME = "HrmOrgGroup";
/*    */   
/* 23 */   protected static String TABLE_WHERE = "(isDelete is null or isDelete='0' or isDelete='' )";
/*    */   
/* 25 */   protected static String TABLE_ORDER = "showOrder";
/*    */   
/*    */   @PKColumn(type = CacheColumnType.NUMBER)
/* 28 */   protected static String PK_NAME = "id";
/*    */   
/*    */   @CacheColumn(name = "orgGroupName")
/*    */   protected static int orgGroupName;
/*    */   
/*    */   @CacheColumn(name = "orgGroupDesc")
/*    */   protected static int orgGroupDesc;
/*    */   
/*    */   @CacheColumn(name = "showOrder")
/*    */   protected static int showOrder;
/*    */   
/*    */   public int getHrmOrgGroupNum() {
/* 40 */     return size();
/*    */   }
/*    */   
/*    */   public String getId() {
/* 44 */     return (String)getRowValue(0);
/*    */   }
/*    */ 
/*    */   
/*    */   public String getOrgGroupName() {
/* 49 */     return (String)getRowValue(orgGroupName);
/*    */   }
/*    */   
/*    */   public String getOrgGroupDesc() {
/* 53 */     return (String)getRowValue(orgGroupDesc);
/*    */   }
/*    */   
/*    */   public String getShowOrder() {
/* 57 */     return (String)getRowValue(showOrder);
/*    */   }
/*    */   
/*    */   public String getOrgGroupName(String paramString) {
/* 61 */     return (String)getValue(orgGroupName, paramString);
/*    */   }
/*    */   
/*    */   public String getOrgGroupDesc(String paramString) {
/* 65 */     return (String)getValue(orgGroupDesc, paramString);
/*    */   }
/*    */   
/*    */   public String getShowOrder(String paramString) {
/* 69 */     return (String)getValue(showOrder, paramString);
/*    */   }
/*    */   
/*    */   public void removeHrmOrgGroupCache() {
/* 73 */     removeCache();
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/orggroup/HrmOrgGroupComInfo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */