/*     */ package weaver.hrm.orggroup;
/*     */ 
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.company.DepartmentComInfo;
/*     */ import weaver.hrm.company.SubCompanyComInfo;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class SptmForOrgGroup
/*     */   extends BaseBean
/*     */ {
/*     */   public String getRelatedSetting(String paramString1, String paramString2) {
/*  33 */     String str = "";
/*     */     try {
/*  35 */       int i = Util.getIntValue(paramString2, 7);
/*  36 */       str = SystemEnv.getHtmlLabelName(24662, i);
/*  37 */     } catch (Exception exception) {}
/*     */ 
/*     */     
/*  40 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getRelatedType(String paramString1, String paramString2) {
/*  52 */     String str = "";
/*     */     try {
/*  54 */       int i = Util.getIntValue(paramString2, 7);
/*  55 */       if ("1".equals(paramString1)) {
/*  56 */         str = SystemEnv.getHtmlLabelName(1867, i);
/*  57 */       } else if ("2".equals(paramString1)) {
/*  58 */         str = SystemEnv.getHtmlLabelName(141, i);
/*  59 */       } else if ("3".equals(paramString1)) {
/*  60 */         str = SystemEnv.getHtmlLabelName(124, i);
/*  61 */       } else if ("4".equals(paramString1)) {
/*  62 */         str = SystemEnv.getHtmlLabelName(122, i);
/*  63 */       } else if ("5".equals(paramString1)) {
/*  64 */         str = SystemEnv.getHtmlLabelName(1340, i);
/*     */       } 
/*  66 */     } catch (Exception exception) {}
/*     */ 
/*     */     
/*  69 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getRelatedName(String paramString1, String paramString2) {
/*  81 */     String str1 = "";
/*     */     
/*  83 */     String str2 = "";
/*  84 */     String str3 = "";
/*  85 */     String[] arrayOfString = Util.TokenizerString2(paramString2, "+");
/*  86 */     if (arrayOfString.length >= 1) {
/*  87 */       str2 = arrayOfString[0];
/*     */     }
/*  89 */     if (arrayOfString.length >= 2) {
/*  90 */       str3 = arrayOfString[1];
/*     */     }
/*     */     
/*  93 */     if ("1".equals(str2)) {
/*     */       try {
/*  95 */         ResourceComInfo resourceComInfo = new ResourceComInfo();
/*  96 */         str1 = resourceComInfo.getResourcename("" + paramString1);
/*  97 */       } catch (Exception exception) {}
/*     */     }
/*  99 */     else if ("2".equals(str2)) {
/*     */       try {
/* 101 */         SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/* 102 */         str1 = subCompanyComInfo.getSubCompanyname("" + paramString1);
/* 103 */         str1 = "<a href='javascript:this.openFullWindowForXtable(\"/hrm/company/HrmSubCompanyDsp.jsp?id=" + paramString1 + "\")'>" + str1 + "</a>";
/* 104 */       } catch (Exception exception) {}
/*     */     }
/* 106 */     else if ("3".equals(str2)) {
/*     */       try {
/* 108 */         DepartmentComInfo departmentComInfo = new DepartmentComInfo();
/* 109 */         str1 = departmentComInfo.getDepartmentname("" + paramString1);
/* 110 */         str1 = "<a href='javascript:this.openFullWindowForXtable(\"/hrm/company/HrmDepartmentDsp.jsp?id=" + paramString1 + "\")'>" + str1 + "</a>";
/* 111 */       } catch (Exception exception) {}
/*     */     } 
/*     */ 
/*     */     
/* 115 */     return str1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getRelatedSecLevel(String paramString1, String paramString2) {
/* 125 */     String str = "";
/* 126 */     str = paramString1 + " - " + paramString2;
/* 127 */     return str;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/orggroup/SptmForOrgGroup.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */