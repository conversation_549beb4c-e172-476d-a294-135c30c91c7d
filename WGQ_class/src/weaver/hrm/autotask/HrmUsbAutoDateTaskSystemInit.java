/*    */ package weaver.hrm.autotask;
/*    */ 
/*    */ import javax.servlet.ServletContext;
/*    */ import weaver.common.SystemInitialitionInterface;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class HrmUsbAutoDateTaskSystemInit
/*    */   implements SystemInitialitionInterface
/*    */ {
/* 12 */   private AutoTask task = null;
/*    */   
/*    */   public HrmUsbAutoDateTaskSystemInit() {
/* 15 */     this.task = new AutoTask();
/*    */   }
/*    */   
/*    */   public void destroyed(ServletContext paramServletContext) {
/* 19 */     this.task.close();
/*    */   }
/*    */   
/*    */   public void initialized(ServletContext paramServletContext) {
/* 23 */     this.task.start();
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/autotask/HrmUsbAutoDateTaskSystemInit.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */