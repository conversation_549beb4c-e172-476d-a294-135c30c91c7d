/*    */ package weaver.hrm.autotask.domain;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class HrmUsbAutoDate
/*    */ {
/*    */   private Long id;
/*    */   private Long userId;
/*    */   private Integer needAuto;
/*    */   private String enableDate;
/*    */   private Integer enableUsbType;
/*    */   private Integer delflag;
/*    */   private boolean isAdmin;
/*    */   
/*    */   public HrmUsbAutoDate() {
/* 19 */     this(true);
/*    */   }
/*    */   
/*    */   public HrmUsbAutoDate(boolean paramBoolean) {
/* 23 */     if (paramBoolean) {
/* 24 */       init();
/*    */     }
/*    */   }
/*    */   
/*    */   public void init() {
/* 29 */     this.id = Long.valueOf(0L);
/* 30 */     this.userId = Long.valueOf(0L);
/* 31 */     this.needAuto = Integer.valueOf(0);
/* 32 */     this.enableDate = "";
/* 33 */     this.enableUsbType = Integer.valueOf(0);
/* 34 */     this.delflag = Integer.valueOf(0);
/* 35 */     this.isAdmin = false;
/*    */   }
/*    */   
/*    */   public void setId(Long paramLong) {
/* 39 */     this.id = paramLong;
/*    */   }
/*    */   
/*    */   public Long getId() {
/* 43 */     return this.id;
/*    */   }
/*    */   
/*    */   public void setUserId(Long paramLong) {
/* 47 */     this.userId = paramLong;
/*    */   }
/*    */   
/*    */   public Long getUserId() {
/* 51 */     return this.userId;
/*    */   }
/*    */   
/*    */   public void setNeedAuto(Integer paramInteger) {
/* 55 */     this.needAuto = paramInteger;
/*    */   }
/*    */   
/*    */   public Integer getNeedAuto() {
/* 59 */     return this.needAuto;
/*    */   }
/*    */   
/*    */   public void setEnableDate(String paramString) {
/* 63 */     this.enableDate = paramString;
/*    */   }
/*    */   
/*    */   public String getEnableDate() {
/* 67 */     return this.enableDate;
/*    */   }
/*    */   
/*    */   public void setEnableUsbType(Integer paramInteger) {
/* 71 */     this.enableUsbType = paramInteger;
/*    */   }
/*    */   
/*    */   public Integer getEnableUsbType() {
/* 75 */     return this.enableUsbType;
/*    */   }
/*    */   
/*    */   public void setDelflag(Integer paramInteger) {
/* 79 */     this.delflag = paramInteger;
/*    */   }
/*    */   
/*    */   public Integer getDelflag() {
/* 83 */     return this.delflag;
/*    */   }
/*    */   
/*    */   public boolean isAdmin() {
/* 87 */     return this.isAdmin;
/*    */   }
/*    */   
/*    */   public void setAdmin(boolean paramBoolean) {
/* 91 */     this.isAdmin = paramBoolean;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/autotask/domain/HrmUsbAutoDate.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */