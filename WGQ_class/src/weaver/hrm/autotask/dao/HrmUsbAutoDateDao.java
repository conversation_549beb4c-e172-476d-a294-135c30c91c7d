/*     */ package weaver.hrm.autotask.dao;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.common.StringUtil;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.framework.BaseDao;
/*     */ import weaver.hrm.autotask.domain.HrmUsbAutoDate;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HrmUsbAutoDateDao
/*     */   implements BaseDao<HrmUsbAutoDate>
/*     */ {
/*     */   public Comparable insert(HrmUsbAutoDate paramHrmUsbAutoDate) {
/*  27 */     RecordSet recordSet = new RecordSet();
/*  28 */     if (paramHrmUsbAutoDate == null) {
/*  29 */       return Integer.valueOf(-1);
/*     */     }
/*     */ 
/*     */ 
/*     */     
/*  34 */     StringBuffer stringBuffer = (new StringBuffer()).append(" insert into hrm_usb_auto_date (user_id,need_auto,enable_date,enable_usb_type,delflag )").append(" values(" + paramHrmUsbAutoDate.getUserId() + "," + paramHrmUsbAutoDate.getNeedAuto() + ",'" + paramHrmUsbAutoDate.getEnableDate() + "'," + paramHrmUsbAutoDate.getEnableUsbType() + ",").append(" " + paramHrmUsbAutoDate.getDelflag() + " )");
/*  35 */     recordSet.executeSql(stringBuffer.toString());
/*  36 */     return Integer.valueOf(1);
/*     */   }
/*     */   
/*     */   public void update(HrmUsbAutoDate paramHrmUsbAutoDate) {
/*  40 */     RecordSet recordSet = new RecordSet();
/*  41 */     if (paramHrmUsbAutoDate == null) {
/*     */       return;
/*     */     }
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  48 */     StringBuffer stringBuffer = (new StringBuffer()).append(" update hrm_usb_auto_date set").append(" user_id = " + paramHrmUsbAutoDate.getUserId() + ",need_auto = " + paramHrmUsbAutoDate.getNeedAuto() + ",enable_date = '" + paramHrmUsbAutoDate.getEnableDate() + "',").append(" enable_usb_type = " + paramHrmUsbAutoDate.getEnableUsbType() + ",delflag = " + paramHrmUsbAutoDate.getDelflag() + "").append(" where id = " + paramHrmUsbAutoDate.getId() + "");
/*  49 */     recordSet.executeSql(stringBuffer.toString());
/*     */   }
/*     */   
/*     */   public List<HrmUsbAutoDate> find(Map<String, Comparable> paramMap) {
/*  53 */     RecordSet recordSet = new RecordSet();
/*  54 */     ArrayList<HrmUsbAutoDate> arrayList = new ArrayList();
/*     */ 
/*     */ 
/*     */     
/*  58 */     StringBuffer stringBuffer = (new StringBuffer()).append(" select t.id,t.user_id,t.need_auto,t.enable_date,t.enable_usb_type,t.delflag,(case when t2.id is null then 0 else 1 end) as isAdmin").append(" from hrm_usb_auto_date t left join HrmResource t1 on t.user_id = t1.id left join HrmResourceManager t2 on t.user_id = t2.id ").append(" where  t.delflag = 0");
/*  59 */     if (paramMap != null) {
/*  60 */       if (paramMap.containsKey("id")) {
/*  61 */         stringBuffer.append(" and t.id = ").append(StringUtil.vString(paramMap.get("id")));
/*     */       }
/*  63 */       if (paramMap.containsKey("begin_id")) {
/*  64 */         stringBuffer.append(" and t.id >= ").append(StringUtil.vString(paramMap.get("begin_id")));
/*     */       }
/*  66 */       if (paramMap.containsKey("end_id")) {
/*  67 */         stringBuffer.append(" and t.id < ").append(StringUtil.vString(paramMap.get("end_id")));
/*     */       }
/*  69 */       if (paramMap.containsKey("sql_id")) {
/*  70 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_id")));
/*     */       }
/*  72 */       if (paramMap.containsKey("userId")) {
/*  73 */         stringBuffer.append(" and t.user_id = ").append(StringUtil.vString(paramMap.get("userId")));
/*     */       }
/*  75 */       if (paramMap.containsKey("begin_userId")) {
/*  76 */         stringBuffer.append(" and t.user_id >= ").append(StringUtil.vString(paramMap.get("begin_userId")));
/*     */       }
/*  78 */       if (paramMap.containsKey("end_userId")) {
/*  79 */         stringBuffer.append(" and t.user_id < ").append(StringUtil.vString(paramMap.get("end_userId")));
/*     */       }
/*  81 */       if (paramMap.containsKey("sql_userId")) {
/*  82 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_userId")));
/*     */       }
/*  84 */       if (paramMap.containsKey("needAuto")) {
/*  85 */         stringBuffer.append(" and t.need_auto = ").append(StringUtil.vString(paramMap.get("needAuto")));
/*     */       }
/*  87 */       if (paramMap.containsKey("begin_needAuto")) {
/*  88 */         stringBuffer.append(" and t.need_auto >= ").append(StringUtil.vString(paramMap.get("begin_needAuto")));
/*     */       }
/*  90 */       if (paramMap.containsKey("end_needAuto")) {
/*  91 */         stringBuffer.append(" and t.need_auto < ").append(StringUtil.vString(paramMap.get("end_needAuto")));
/*     */       }
/*  93 */       if (paramMap.containsKey("sql_needAuto")) {
/*  94 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_needAuto")));
/*     */       }
/*  96 */       if (paramMap.containsKey("enableUsbType")) {
/*  97 */         stringBuffer.append(" and t.enable_usb_type = ").append(StringUtil.vString(paramMap.get("enableUsbType")));
/*     */       }
/*  99 */       if (paramMap.containsKey("begin_enableUsbType")) {
/* 100 */         stringBuffer.append(" and t.enable_usb_type >= ").append(StringUtil.vString(paramMap.get("begin_enableUsbType")));
/*     */       }
/* 102 */       if (paramMap.containsKey("end_enableUsbType")) {
/* 103 */         stringBuffer.append(" and t.enable_usb_type < ").append(StringUtil.vString(paramMap.get("end_enableUsbType")));
/*     */       }
/* 105 */       if (paramMap.containsKey("sql_enableUsbType")) {
/* 106 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_enableUsbType")));
/*     */       }
/* 108 */       if (paramMap.containsKey("delflag")) {
/* 109 */         stringBuffer.append(" and t.delflag = ").append(StringUtil.vString(paramMap.get("delflag")));
/*     */       }
/* 111 */       if (paramMap.containsKey("begin_delflag")) {
/* 112 */         stringBuffer.append(" and t.delflag >= ").append(StringUtil.vString(paramMap.get("begin_delflag")));
/*     */       }
/* 114 */       if (paramMap.containsKey("end_delflag")) {
/* 115 */         stringBuffer.append(" and t.delflag < ").append(StringUtil.vString(paramMap.get("end_delflag")));
/*     */       }
/* 117 */       if (paramMap.containsKey("sql_delflag")) {
/* 118 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_delflag")));
/*     */       }
/* 120 */       if (paramMap.containsKey("enableDate")) {
/* 121 */         stringBuffer.append(" and t.enable_date = '").append(StringUtil.vString(paramMap.get("enableDate"))).append("'");
/*     */       }
/* 123 */       if (paramMap.containsKey("like_enableDate")) {
/* 124 */         stringBuffer.append(" and t.enable_date like '%").append(StringUtil.vString(paramMap.get("like_enableDate"))).append("%'");
/*     */       }
/* 126 */       if (paramMap.containsKey("sql_enableDate")) {
/* 127 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("sql_enableDate")));
/*     */       }
/* 129 */       if (paramMap.containsKey("mfsql")) {
/* 130 */         stringBuffer.append(" " + StringUtil.vString(paramMap.get("mfsql")));
/*     */       }
/* 132 */       if (paramMap.containsKey("sqlorderby")) {
/* 133 */         stringBuffer.append(" order by " + StringUtil.vString(paramMap.get("sqlorderby")));
/*     */       } else {
/* 135 */         stringBuffer.append(" order by t.id ").append((StringUtil.vString(paramMap.get("sqlsortway")).length() > 0) ? StringUtil.vString(paramMap.get("sqlsortway")) : "desc");
/*     */       } 
/*     */     } 
/* 138 */     recordSet.executeSql(stringBuffer.toString());
/* 139 */     HrmUsbAutoDate hrmUsbAutoDate = null;
/* 140 */     while (recordSet.next()) {
/* 141 */       hrmUsbAutoDate = new HrmUsbAutoDate();
/* 142 */       hrmUsbAutoDate.setId(Long.valueOf(StringUtil.parseToLong(recordSet.getString("id"))));
/* 143 */       hrmUsbAutoDate.setUserId(Long.valueOf(StringUtil.parseToLong(recordSet.getString("user_id"))));
/* 144 */       hrmUsbAutoDate.setNeedAuto(Integer.valueOf(StringUtil.parseToInt(recordSet.getString("need_auto"))));
/* 145 */       hrmUsbAutoDate.setEnableDate(StringUtil.vString(recordSet.getString("enable_date")));
/* 146 */       hrmUsbAutoDate.setEnableUsbType(Integer.valueOf(StringUtil.parseToInt(recordSet.getString("enable_usb_type"))));
/* 147 */       hrmUsbAutoDate.setDelflag(Integer.valueOf(StringUtil.parseToInt(recordSet.getString("delflag"))));
/* 148 */       hrmUsbAutoDate.setAdmin((recordSet.getInt("isAdmin") == 1));
/* 149 */       arrayList.add(hrmUsbAutoDate);
/*     */     } 
/* 151 */     return arrayList;
/*     */   }
/*     */   
/*     */   public HrmUsbAutoDate get(Comparable paramComparable) {
/* 155 */     HrmUsbAutoDate hrmUsbAutoDate = null;
/* 156 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 157 */     hashMap.put("id", paramComparable);
/* 158 */     List<HrmUsbAutoDate> list = find((Map)hashMap);
/* 159 */     if (list != null && list.size() > 0) {
/* 160 */       hrmUsbAutoDate = list.get(0);
/*     */     }
/* 162 */     return hrmUsbAutoDate;
/*     */   }
/*     */   
/*     */   public void delete(Comparable paramComparable) {
/* 166 */     RecordSet recordSet = new RecordSet();
/* 167 */     recordSet.executeSql("update hrm_usb_auto_date set delflag = 1 where id in ( " + paramComparable + " ) ");
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/autotask/dao/HrmUsbAutoDateDao.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */