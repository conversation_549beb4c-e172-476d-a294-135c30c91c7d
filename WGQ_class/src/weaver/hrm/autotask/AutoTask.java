/*    */ package weaver.hrm.autotask;
/*    */ 
/*    */ import java.util.HashMap;
/*    */ import java.util.List;
/*    */ import java.util.Map;
/*    */ import org.apache.commons.logging.Log;
/*    */ import org.apache.commons.logging.LogFactory;
/*    */ import weaver.common.DateUtil;
/*    */ import weaver.common.StringUtil;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.hrm.autotask.domain.HrmUsbAutoDate;
/*    */ import weaver.hrm.autotask.manager.HrmUsbAutoDateManager;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class AutoTask
/*    */   extends Thread
/*    */ {
/* 21 */   private final Log LOG = LogFactory.getLog(AutoTask.class);
/*    */   
/* 23 */   private final byte[] LOCK = new byte[0];
/*    */   
/* 25 */   private long JOB_SLEEP = 1800000L;
/*    */   
/* 27 */   private int errorCount = 0;
/*    */   
/*    */   boolean jFlag = true;
/*    */   
/* 31 */   private HrmUsbAutoDateManager manager = null;
/*    */   
/* 33 */   private Map<String, Comparable> map = null;
/*    */ 
/*    */ 
/*    */   
/*    */   public AutoTask() {
/* 38 */     this.manager = new HrmUsbAutoDateManager();
/* 39 */     this.map = new HashMap<>();
/* 40 */     this.map.put("needAuto", Integer.valueOf(1));
/* 41 */     this.map.put("delflag", Integer.valueOf(0));
/*    */   }
/*    */ 
/*    */   
/*    */   public void run() {
/* 46 */     RecordSet recordSet = new RecordSet();
/* 47 */     synchronized (this.LOCK) {
/* 48 */       while (this.jFlag) {
/*    */         try {
/* 50 */           this.map.put("enableDate", DateUtil.getCurrentDate());
/* 51 */           List list = this.manager.find(this.map);
/* 52 */           if (list != null && list.size() > 0) {
/* 53 */             StringBuffer stringBuffer = new StringBuffer();
/* 54 */             for (HrmUsbAutoDate hrmUsbAutoDate : list) {
/*    */ 
/*    */ 
/*    */               
/* 58 */               StringBuffer stringBuffer1 = (new StringBuffer("update ")).append(hrmUsbAutoDate.isAdmin() ? "HrmResourceManager" : "HrmResource").append(" set usbstate = ").append(hrmUsbAutoDate.getEnableUsbType()).append(" where id = ").append(hrmUsbAutoDate.getUserId());
/* 59 */               recordSet.executeSql(stringBuffer1.toString());
/* 60 */               stringBuffer.append(StringUtil.isNull(stringBuffer.toString()) ? "" : ",").append(hrmUsbAutoDate.getId());
/*    */             } 
/* 62 */             if (StringUtil.isNotNull(stringBuffer.toString())) {
/* 63 */               this.manager.delete(stringBuffer.toString());
/*    */             }
/*    */           } 
/* 66 */           Thread.sleep(this.JOB_SLEEP);
/* 67 */         } catch (Exception exception) {
/* 68 */           this.LOG.error("AutoTask job error.", exception);
/* 69 */           this.JOB_SLEEP += this.JOB_SLEEP;
/* 70 */           if (++this.errorCount >= 3) {
/* 71 */             this.jFlag = false; continue;
/*    */           }  
/* 73 */           try { Thread.sleep(this.JOB_SLEEP); } catch (InterruptedException interruptedException) {}
/*    */         } 
/*    */       } 
/*    */     } 
/*    */   }
/*    */ 
/*    */   
/*    */   public void close() {
/* 81 */     this.jFlag = false;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/autotask/AutoTask.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */