/*    */ package weaver.hrm.resinsn;
/*    */ 
/*    */ import java.io.File;
/*    */ import java.io.FileWriter;
/*    */ import java.io.IOException;
/*    */ import java.net.InetAddress;
/*    */ import java.util.UUID;
/*    */ import javax.servlet.ServletException;
/*    */ import javax.servlet.http.HttpServlet;
/*    */ import javax.servlet.http.HttpServletRequest;
/*    */ import javax.servlet.http.HttpServletResponse;
/*    */ import org.jdom.Content;
/*    */ import org.jdom.Document;
/*    */ import org.jdom.Element;
/*    */ import org.jdom.input.SAXBuilder;
/*    */ import org.jdom.output.Format;
/*    */ import org.jdom.output.XMLOutputter;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.GCONST;
/*    */ import weaver.general.StaticObj;
/*    */ import weaver.security.util.SecurityMethodUtil;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class ResinSNServlet
/*    */   extends HttpServlet
/*    */ {
/*    */   private static final long serialVersionUID = 1L;
/*    */   
/*    */   public void init() {
/*    */     try {
/* 41 */       RecordSet recordSet = new RecordSet();
/* 42 */       SAXBuilder sAXBuilder = new SAXBuilder();
/* 43 */       Document document = sAXBuilder.build(new File(GCONST.getRootPath() + File.separator + "WEB-INF" + File.separator + "HrmResinSN.xml"));
/* 44 */       SecurityMethodUtil.setSaxBuilderFeature(sAXBuilder);
/* 45 */       Element element1 = document.getRootElement();
/* 46 */       Element element2 = new Element("resinSN");
/* 47 */       int i = 0;
/* 48 */       String str1 = InetAddress.getLocalHost().getHostAddress();
/* 49 */       String str2 = "";
/* 50 */       if (element1.getChild("resinSN") == null || element1.getChild("resinSN").equals("")) {
/*    */         
/* 52 */         str2 = UUID.randomUUID().toString();
/* 53 */         element2.setText(str2);
/* 54 */         element1.addContent((Content)element2);
/* 55 */         Format format = Format.getPrettyFormat();
/* 56 */         format.setEncoding("GB2312");
/* 57 */         XMLOutputter xMLOutputter = new XMLOutputter(format);
/* 58 */         xMLOutputter.output(document, new FileWriter(GCONST.getRootPath() + File.separator + "WEB-INF" + File.separator + "HrmResinSN.xml"));
/*    */         
/* 60 */         recordSet.execute("select max(id) from hrmResinSNIPList");
/* 61 */         if (recordSet.next()) {
/* 62 */           i = recordSet.getInt(1);
/* 63 */           if (i == -1) {
/* 64 */             i = 1;
/*    */           } else {
/* 66 */             i++;
/*    */           } 
/*    */         } 
/* 69 */         recordSet.executeSql("insert into hrmResinSNIPList(id,resinSn,IP)values(" + i + ",'" + str2 + "','" + str1 + "')");
/*    */       } else {
/* 71 */         str2 = element1.getChildText("resinSN");
/* 72 */         recordSet.executeSql("update hrmResinSNIPList set IP='" + str1 + "' where resinSN='" + str2 + "'");
/*    */       } 
/*    */ 
/*    */       
/* 76 */       StaticObj staticObj = StaticObj.getInstance();
/* 77 */       staticObj.putRecordToObj("HrmResinSN", "resinSN", str2);
/*    */     }
/* 79 */     catch (Exception exception) {}
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public void doGet(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws ServletException, IOException {
/* 93 */     doPost(paramHttpServletRequest, paramHttpServletResponse);
/*    */   }
/*    */   
/*    */   public void doPost(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws ServletException, IOException {}
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/resinsn/ResinSNServlet.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */