/*     */ package weaver.hrm.province;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.Iterator;
/*     */ import weaver.common.util.xtree.TreeNode;
/*     */ import weaver.hrm.common.Tools;
/*     */ import weaver.hrm.country.CountryComInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class ProvinceManager
/*     */ {
/*     */   public TreeNode getCountryTree(TreeNode paramTreeNode, ArrayList<TreeNode> paramArrayList, String paramString) throws Exception {
/*  26 */     Iterator<TreeNode> iterator = paramArrayList.iterator();
/*  27 */     while (iterator.hasNext()) {
/*  28 */       TreeNode treeNode = iterator.next();
/*     */       
/*  30 */       if ((treeNode.getTreeNode()).length > 0 && travel(paramTreeNode, treeNode, true)) {
/*  31 */         paramArrayList.remove(treeNode);
/*  32 */         iterator = paramArrayList.iterator();
/*     */         
/*     */         continue;
/*     */       } 
/*  36 */       String str1 = treeNode.getNodeId();
/*  37 */       String str2 = str1.substring(str1.lastIndexOf('_') + 1);
/*  38 */       if (str2.equals(paramString)) {
/*     */         continue;
/*     */       }
/*  41 */       if (!str2.equals("") && !str2.equals("0")) {
/*  42 */         TreeNode treeNode1 = new TreeNode();
/*     */         
/*  44 */         treeNode1.setTitle((new CountryComInfo()).getCountryname(str2));
/*  45 */         treeNode1.setNodeId("country_" + str2);
/*     */         
/*  47 */         String str = null;
/*  48 */         if ("0".equals(str2)) {
/*  49 */           str = "/images/treeimages/home16_wev8.gif";
/*     */         } else {
/*  51 */           str = "/LeftMenu/ThemeXP/page_wev8.gif";
/*     */         } 
/*     */         
/*  54 */         treeNode1.setIcon(str);
/*  55 */         treeNode1.setValue(str2);
/*     */ 
/*     */ 
/*     */ 
/*     */         
/*  60 */         getCountryTree(treeNode1, str2, paramString, treeNode, paramArrayList);
/*  61 */         paramArrayList.add(0, treeNode1);
/*     */       } else {
/*  63 */         getCountryTree(paramTreeNode, "0", paramString, treeNode, paramArrayList);
/*     */       } 
/*  65 */       iterator = paramArrayList.iterator();
/*     */     } 
/*  67 */     return paramTreeNode;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public TreeNode getCountryTree(TreeNode paramTreeNode1, String paramString1, String paramString2, TreeNode paramTreeNode2, ArrayList paramArrayList) throws Exception {
/*  80 */     CountryComInfo countryComInfo = new CountryComInfo();
/*  81 */     countryComInfo.setTofirstRow();
/*     */     
/*  83 */     String str = "";
/*  84 */     TreeNode treeNode = null;
/*  85 */     while (countryComInfo.next()) {
/*  86 */       if (Tools.vString(countryComInfo.getCountryiscanceled()).equals("1"))
/*     */         continue; 
/*  88 */       str = countryComInfo.getCountryid();
/*  89 */       treeNode = new TreeNode();
/*  90 */       treeNode.setTitle(toScreen(countryComInfo.getCountryname()));
/*  91 */       treeNode.setNodeId("country_" + str);
/*  92 */       treeNode.setTarget("_self");
/*     */       
/*  94 */       if (paramTreeNode2 != null && (paramTreeNode2.getTreeNode()).length > 0 && paramTreeNode2.equals(treeNode)) {
/*  95 */         treeNode = paramTreeNode2;
/*     */       }
/*  97 */       if (!str.equals(paramString2)) {
/*  98 */         paramTreeNode1.addTreeNode(treeNode);
/*     */       }
/* 100 */       if (paramArrayList != null) {
/* 101 */         paramArrayList.remove(treeNode);
/*     */       }
/* 103 */       getProvinceTree(treeNode, str, paramString2, paramTreeNode2, paramArrayList);
/*     */     } 
/* 105 */     return paramTreeNode1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public TreeNode getProvinceTree(TreeNode paramTreeNode1, String paramString1, String paramString2, TreeNode paramTreeNode2, ArrayList paramArrayList) throws Exception {
/* 118 */     ProvinceComInfo provinceComInfo = new ProvinceComInfo();
/* 119 */     provinceComInfo.setTofirstRow();
/*     */     
/* 121 */     String str1 = "0";
/* 122 */     String str2 = "";
/* 123 */     TreeNode treeNode = null;
/* 124 */     while (provinceComInfo.next()) {
/* 125 */       str1 = provinceComInfo.getProvincecountryid();
/* 126 */       if (!str1.equals(paramString1) || Tools.vString(provinceComInfo.getProvinceiscanceled()).equals("1")) {
/*     */         continue;
/*     */       }
/* 129 */       str2 = provinceComInfo.getProvinceid();
/* 130 */       treeNode = new TreeNode();
/* 131 */       treeNode.setTitle(toScreen(provinceComInfo.getProvincename()));
/* 132 */       treeNode.setNodeId("province_" + str2);
/* 133 */       treeNode.setHref("javascript:setProvince('" + treeNode.getNodeId() + "')");
/* 134 */       treeNode.setTarget("_self");
/*     */       
/* 136 */       if (paramTreeNode2 != null && (paramTreeNode2.getTreeNode()).length > 0 && paramTreeNode2.equals(treeNode)) {
/* 137 */         treeNode = paramTreeNode2;
/*     */       }
/* 139 */       if (!str2.equals(paramString2)) {
/* 140 */         paramTreeNode1.addTreeNode(treeNode);
/*     */       }
/* 142 */       if (paramArrayList != null) {
/* 143 */         paramArrayList.remove(treeNode);
/*     */       }
/*     */     } 
/* 146 */     return paramTreeNode1;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   private boolean travel(TreeNode paramTreeNode1, TreeNode paramTreeNode2, boolean paramBoolean) {
/* 152 */     if (paramTreeNode1.equals(paramTreeNode2)) {
/* 153 */       if (paramBoolean) {
/* 154 */         if ((paramTreeNode2.getTreeNode()).length > 0)
/* 155 */           paramTreeNode1.setNodeXmlSrc(null); 
/* 156 */         paramTreeNode1.setTreeNode(paramTreeNode2.getTreeNode());
/*     */       } 
/* 158 */       return true;
/*     */     } 
/* 160 */     TreeNode[] arrayOfTreeNode = paramTreeNode1.getTreeNode();
/* 161 */     for (byte b = 0; b < arrayOfTreeNode.length; b++) {
/* 162 */       if (travel(arrayOfTreeNode[b], paramTreeNode2, paramBoolean)) {
/* 163 */         return true;
/*     */       }
/*     */     } 
/* 166 */     return false;
/*     */   }
/*     */   
/*     */   private static String toScreen(String paramString) {
/* 170 */     String str = Tools.vString(paramString);
/* 171 */     str = Tools.replace(str, "&lt;", "<");
/* 172 */     str = Tools.replace(str, "&gt;", ">");
/* 173 */     str = Tools.replace(str, "&quot;", "\"");
/* 174 */     return str;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/province/ProvinceManager.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */