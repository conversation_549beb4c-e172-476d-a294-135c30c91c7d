/*     */ package weaver.hrm.province;
/*     */ 
/*     */ import weaver.cache.CacheBase;
/*     */ import weaver.cache.CacheColumn;
/*     */ import weaver.cache.CacheColumnType;
/*     */ import weaver.cache.PKColumn;
/*     */ 
/*     */ public class ProvinceComInfo
/*     */   extends CacheBase
/*     */ {
/*  11 */   protected static String TABLE_NAME = "HrmProvince";
/*     */   
/*  13 */   protected static String TABLE_WHERE = null;
/*     */   
/*  15 */   protected static String TABLE_ORDER = "countryid";
/*     */   
/*     */   @PKColumn(type = CacheColumnType.NUMBER)
/*  18 */   protected static String PK_NAME = "id";
/*     */ 
/*     */   
/*     */   @CacheColumn(name = "provincename")
/*     */   protected static int provincename;
/*     */ 
/*     */   
/*     */   @CacheColumn(name = "provincedesc")
/*     */   protected static int provincedesc;
/*     */ 
/*     */   
/*     */   @CacheColumn(name = "countryid")
/*     */   protected static int countryid;
/*     */ 
/*     */   
/*     */   @CacheColumn(name = "canceled")
/*     */   protected static int canceled;
/*     */   
/*     */   @CacheColumn(name = "quicksearch")
/*     */   protected static int quicksearch;
/*     */ 
/*     */   
/*     */   public int getProvinceNum() {
/*  41 */     return size();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean next(String paramString) {
/*  50 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getProvinceid() {
/*  58 */     return (String)getRowValue(0);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getProvinceiscanceled() {
/*  66 */     return (String)getRowValue(canceled);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getRegionID() {
/*  74 */     return "";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getProvincename() {
/*  82 */     return (String)getRowValue(provincename);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getProvincename(String paramString) {
/*  92 */     return (String)getValue(provincename, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getProvincedesc() {
/* 100 */     return (String)getRowValue(provincedesc);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getProvincedesc(String paramString) {
/* 110 */     return (String)getValue(provincedesc, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getRegionID(String paramString) {
/* 120 */     return "";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getProvincecountryid() {
/* 128 */     return (String)getRowValue(countryid);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getProvincecountryid(String paramString) {
/* 138 */     return (String)getValue(countryid, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getQuickSearch() {
/* 146 */     return (String)getRowValue(quicksearch);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getQuickSearch(String paramString) {
/* 156 */     return (String)getValue(quicksearch, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void removeProvinceCache() {
/* 163 */     removeCache();
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/province/ProvinceComInfo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */