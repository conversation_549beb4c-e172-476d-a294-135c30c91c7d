/*     */ package weaver.hrm.resign;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class WorkFlowDetail
/*     */ {
/*     */   String requestid;
/*     */   String createdate;
/*     */   String creatorid;
/*     */   String creator;
/*     */   String creatortype;
/*     */   String creatorname;
/*     */   String workflowid;
/*     */   String workflowname;
/*     */   String requestname;
/*     */   String status;
/*     */   String requestlevel;
/*     */   
/*     */   public String getCreatorid() {
/*  33 */     return this.creatorid;
/*     */   }
/*     */   
/*     */   public void setCreatorid(String paramString) {
/*  37 */     this.creatorid = paramString;
/*     */   }
/*     */   
/*     */   public String getRequestid() {
/*  41 */     return this.requestid;
/*     */   }
/*     */   
/*     */   public void setRequestid(String paramString) {
/*  45 */     this.requestid = paramString;
/*     */   }
/*     */   
/*     */   public String getCreatedate() {
/*  49 */     return this.createdate;
/*     */   }
/*     */   
/*     */   public void setCreatedate(String paramString) {
/*  53 */     this.createdate = paramString;
/*     */   }
/*     */   
/*     */   public String getCreator() {
/*  57 */     return this.creator;
/*     */   }
/*     */   
/*     */   public void setCreator(String paramString) {
/*  61 */     this.creator = paramString;
/*     */   }
/*     */   
/*     */   public String getCreatortype() {
/*  65 */     return this.creatortype;
/*     */   }
/*     */   
/*     */   public void setCreatortype(String paramString) {
/*  69 */     this.creatortype = paramString;
/*     */   }
/*     */   
/*     */   public String getCreatorname() {
/*  73 */     return this.creatorname;
/*     */   }
/*     */   
/*     */   public void setCreatorname(String paramString) {
/*  77 */     this.creatorname = paramString;
/*     */   }
/*     */   
/*     */   public String getWorkflowid() {
/*  81 */     return this.workflowid;
/*     */   }
/*     */   
/*     */   public void setWorkflowid(String paramString) {
/*  85 */     this.workflowid = paramString;
/*     */   }
/*     */   
/*     */   public String getWorkflowname() {
/*  89 */     return this.workflowname;
/*     */   }
/*     */   
/*     */   public void setWorkflowname(String paramString) {
/*  93 */     this.workflowname = paramString;
/*     */   }
/*     */   
/*     */   public String getRequestname() {
/*  97 */     return this.requestname;
/*     */   }
/*     */   
/*     */   public void setRequestname(String paramString) {
/* 101 */     this.requestname = paramString;
/*     */   }
/*     */   
/*     */   public String getStatus() {
/* 105 */     return this.status;
/*     */   }
/*     */   
/*     */   public void setStatus(String paramString) {
/* 109 */     this.status = paramString;
/*     */   }
/*     */   
/*     */   public String getRequestlevel() {
/* 113 */     return this.requestlevel;
/*     */   }
/*     */   
/*     */   public void setRequestlevel(String paramString) {
/* 117 */     this.requestlevel = paramString;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/resign/WorkFlowDetail.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */