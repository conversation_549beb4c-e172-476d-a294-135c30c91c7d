/*    */ package weaver.hrm.resign;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class RoleDetail
/*    */ {
/*    */   String roleid;
/*    */   String roleName;
/*    */   String rolelevel;
/*    */   
/*    */   public String getRoleid() {
/* 29 */     return this.roleid;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public void setRoleid(String paramString) {
/* 37 */     this.roleid = paramString;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getRoleName() {
/* 45 */     return this.roleName;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public void setRoleName(String paramString) {
/* 53 */     this.roleName = paramString;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getRolelevel() {
/* 61 */     return this.rolelevel;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public void setRolelevel(String paramString) {
/* 69 */     this.rolelevel = paramString;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/resign/RoleDetail.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */