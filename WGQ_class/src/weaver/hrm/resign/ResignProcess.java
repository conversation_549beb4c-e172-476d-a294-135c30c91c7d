/*     */ package weaver.hrm.resign;
/*     */ 
/*     */ import java.math.BigDecimal;
/*     */ import java.sql.Timestamp;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Date;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ import weaver.hrm.roles.RolesComInfo;
/*     */ import weaver.proj.Maint.ProjectTaskList;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.workflow.workflow.WorkflowComInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class ResignProcess
/*     */ {
/*     */   public static int countWorkFlows(String paramString) {
/*  40 */     boolean bool = false;
/*  41 */     RecordSet recordSet = new RecordSet();
/*  42 */     StringBuilder stringBuilder = new StringBuilder();
/*  43 */     stringBuilder.append("SELECT COUNT(1) FROM (SELECT DISTINCT a.workflowId, a.requestId FROM Workflow_CurrentOperator a INNER JOIN workflow_base b ON a.workflowid=b.id");
/*  44 */     stringBuilder.append(" WHERE b.isvalid IN ('1','3')");
/*  45 */     stringBuilder.append(" AND a.userId=");
/*  46 */     stringBuilder.append(paramString);
/*  47 */     stringBuilder.append(" AND a.isLastTimes=1");
/*  48 */     stringBuilder.append(" AND a.isRemark IN ('0','1','5','8','9','7')");
/*  49 */     stringBuilder.append(" AND a.userType='0'");
/*  50 */     stringBuilder.append(" ) temptab");
/*     */     
/*  52 */     recordSet.executeSql(stringBuilder.toString());
/*  53 */     bool = recordSet.next() ? recordSet.getInt(1) : false;
/*  54 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static ArrayList getWorkFlowsDetail(String paramString, int paramInt1, int paramInt2, int paramInt3) throws Exception {
/*     */     String str;
/*  68 */     RecordSet recordSet = new RecordSet();
/*     */     
/*  70 */     if (paramInt1 == 1) {
/*  71 */       if (recordSet.getDBType().equals("oracle")) {
/*  72 */         str = "select  distinct t1.requestid, createdate, createtime,lastoperatedate, lastoperatetime,creater, creatertype, t1.workflowid, requestname, status,requestlevel from workflow_requestbase t1,workflow_currentoperator t2 where t1.requestid = t2.requestid and t2.userid = " + paramString + "   and  (t1.deleted = 0 or t1.deleted is null) and t1.currentnodetype!='3' and rownum<=" + paramInt2 + " order by t1.requestid";
/*  73 */       } else if (recordSet.getDBType().equals("db2")) {
/*  74 */         str = "select  distinct t1.requestid, createdate, createtime,lastoperatedate, lastoperatetime,creater, creatertype, t1.workflowid, requestname, status,requestlevel from workflow_requestbase t1,workflow_currentoperator t2 where t1.requestid = t2.requestid and t2.userid = " + paramString + "   and  (t1.deleted = 0 or t1.deleted is null) and t1.currentnodetype!='3' order by t1.requestid fetch first " + paramInt2 + " rows only";
/*     */       } else {
/*  76 */         str = "select distinct top " + paramInt2 + "  t1.requestid, createdate, createtime,lastoperatedate, lastoperatetime,creater, creatertype, t1.workflowid, requestname, status,requestlevel from workflow_requestbase t1,workflow_currentoperator t2 where t1.requestid = t2.requestid and t2.userid = " + paramString + "   and  (t1.deleted = 0 or t1.deleted is null) and t1.currentnodetype!='3' order by t1.requestid";
/*     */       } 
/*  78 */     } else if (recordSet.getDBType().equals("oracle")) {
/*  79 */       str = "select * from (select a.* from ( select distinct t1.requestid, createdate, createtime,lastoperatedate, lastoperatetime,creater, creatertype, t1.workflowid, requestname, status,requestlevel,rownum r from workflow_requestbase t1,workflow_currentoperator t2 where t1.requestid = t2.requestid and t2.userid = " + paramString + "   and  (t1.deleted = 0 or t1.deleted is null) and t1.currentnodetype!='3' and rownum<=" + (paramInt1 * paramInt2) + " order by t1.requestid)  a ) b where r>" + ((paramInt1 - 1) * paramInt2);
/*  80 */     } else if (recordSet.getDBType().equals("db2")) {
/*  81 */       str = "select distinct   t1.requestid, createdate, createtime,lastoperatedate, lastoperatetime,creater, creatertype, t1.workflowid, requestname, status,requestlevel from workflow_requestbase t1,workflow_currentoperator t2 where t1.requestid>( select max(t.requestid) from( select distinct t1.requestid from workflow_requestbase t1,workflow_currentoperator t2 where t1.requestid = t2.requestid and t2.userid = " + paramString + "   and  (t1.deleted = 0 or t1.deleted is null) and t1.currentnodetype!='3' order by t1.requestid  fetch first " + ((paramInt1 - 1) * paramInt2) + " rows only ) as t) and t1.requestid = t2.requestid and t2.userid = " + paramString + "   and  (t1.deleted = 0 or t1.deleted is null) and t1.currentnodetype!='3' order by t1.requestid  fetch first " + paramInt2 + " rows only";
/*     */     } else {
/*  83 */       str = "select distinct top " + paramInt2 + "  t1.requestid, createdate, createtime,lastoperatedate, lastoperatetime,creater, creatertype, t1.workflowid, requestname, status,requestlevel from workflow_requestbase t1,workflow_currentoperator t2 where t1.requestid>( select max(t.requestid) from(select distinct top " + ((paramInt1 - 1) * paramInt2) + " t1.requestid from workflow_requestbase t1,workflow_currentoperator t2 where t1.requestid = t2.requestid and t2.userid = " + paramString + "   and (t1.deleted = 0 or t1.deleted is null) and t1.currentnodetype!='3' order by t1.requestid) as t) and t1.requestid = t2.requestid and t2.userid = " + paramString + "   and  (t1.deleted = 0 or t1.deleted is null) and t1.currentnodetype!='3' order by t1.requestid";
/*     */     } 
/*  85 */     recordSet.executeSql(str);
/*  86 */     ArrayList<WorkFlowDetail> arrayList = new ArrayList();
/*  87 */     ResourceComInfo resourceComInfo = new ResourceComInfo();
/*  88 */     WorkflowComInfo workflowComInfo = new WorkflowComInfo();
/*  89 */     while (recordSet.next()) {
/*  90 */       WorkFlowDetail workFlowDetail = new WorkFlowDetail();
/*  91 */       String str1 = recordSet.getString("requestid");
/*  92 */       String str2 = recordSet.getString("createdate");
/*  93 */       String str3 = recordSet.getString("creater");
/*  94 */       String str4 = recordSet.getString("creatertype");
/*  95 */       String str5 = resourceComInfo.getResourcename(str3);
/*  96 */       String str6 = recordSet.getString("workflowid");
/*  97 */       String str7 = workflowComInfo.getWorkflowname(str6);
/*  98 */       String str8 = recordSet.getString("requestname");
/*  99 */       String str9 = recordSet.getString("status");
/* 100 */       String str10 = recordSet.getString("requestlevel");
/*     */       
/* 102 */       workFlowDetail.setRequestid(str1);
/* 103 */       workFlowDetail.setCreatedate(str2);
/* 104 */       workFlowDetail.setCreator(str3);
/* 105 */       workFlowDetail.setStatus(str9);
/* 106 */       workFlowDetail.setCreatorid(str3);
/* 107 */       workFlowDetail.setCreatortype(str4);
/* 108 */       workFlowDetail.setWorkflowid(str6);
/* 109 */       workFlowDetail.setRequestname(str6);
/* 110 */       workFlowDetail.setRequestlevel(str10);
/* 111 */       workFlowDetail.setCreatorname(str5);
/* 112 */       workFlowDetail.setWorkflowname(str7);
/* 113 */       workFlowDetail.setRequestname(str8);
/* 114 */       arrayList.add(workFlowDetail);
/*     */     } 
/* 116 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static int countDocuments(String paramString) {
/* 127 */     RecordSet recordSet = new RecordSet();
/* 128 */     String str = "select count(t1.id) from DocDetail  t1 where  t1.ownerid=" + paramString + " and (ishistory is null or ishistory = 0) ";
/* 129 */     recordSet.executeSql(str);
/* 130 */     recordSet.next();
/* 131 */     return recordSet.getInt(1);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static ArrayList getDocumetsDetail(String paramString, int paramInt1, int paramInt2, int paramInt3) throws Exception {
/*     */     String str;
/* 146 */     RecordSet recordSet = new RecordSet();
/*     */     
/* 148 */     if (paramInt1 == 1) {
/* 149 */       if (recordSet.getDBType().equals("oracle")) {
/* 150 */         str = "select id,maincategory,subcategory,seccategory,doccreaterid,ownerid,doclastmoddate,doclastmodtime,docsubject,docstatus,doccreatedate,doccreatetime,replydocid,docreplyable,isreply,replaydoccount,accessorycount,t1.usertype,t1.doctype from DocDetail  t1 where rownum<=" + paramInt2 + " and doccreaterid=" + paramString + " order by id";
/* 151 */       } else if (recordSet.getDBType().equals("db2")) {
/* 152 */         str = "select id,maincategory,subcategory,seccategory,doccreaterid,ownerid,doclastmoddate,doclastmodtime,docsubject,docstatus,doccreatedate,doccreatetime,replydocid,docreplyable,isreply,replaydoccount,accessorycount,t1.usertype,t1.doctype from DocDetail  t1 where   doccreaterid=" + paramString + " order by id fetch first " + paramInt2 + " rows only";
/*     */       } else {
/* 154 */         str = "select top " + paramInt2 + " id,maincategory,subcategory,seccategory,doccreaterid,ownerid,doclastmoddate,doclastmodtime,docsubject,docstatus,doccreatedate,doccreatetime,replydocid,docreplyable,isreply,replaydoccount,accessorycount,t1.usertype,t1.doctype from DocDetail t1 where doccreaterid=" + paramString + " order by id";
/*     */       } 
/* 156 */     } else if (recordSet.getDBType().equals("oracle")) {
/* 157 */       str = "select * from (select a.* from (select id,maincategory,subcategory,seccategory,doccreaterid,ownerid,doclastmoddate,doclastmodtime,docsubject,docstatus,doccreatedate,doccreatetime,replydocid,docreplyable,isreply,replaydoccount,accessorycount,t1.usertype,t1.doctype,rownum r from DocDetail  t1 where rownum<=" + (paramInt1 * paramInt2) + " and doccreaterid=" + paramString + " order by id) a) b where r >" + ((paramInt1 - 1) * paramInt2);
/* 158 */     } else if (recordSet.getDBType().equals("db2")) {
/* 159 */       str = "select id,maincategory,subcategory,seccategory,doccreaterid,ownerid,doclastmoddate,doclastmodtime,docsubject,docstatus,doccreatedate,doccreatetime,replydocid,docreplyable,isreply,replaydoccount,accessorycount,t1.usertype,t1.doctype  from DocDetail  t1 where id>(select max(t.id) from (select id from DocDetail where doccreaterid=" + paramString + "order by id  fetch first " + ((paramInt1 - 1) * paramInt2) + " rows only ) t) and t1.doccreaterid=" + paramString + " order by id  fetch first " + paramInt2 + " rows only";
/*     */     } else {
/* 161 */       str = "select top " + paramInt2 + " id,maincategory,subcategory,seccategory,doccreaterid,ownerid,doclastmoddate,doclastmodtime,docsubject,docstatus,doccreatedate,doccreatetime,replydocid,docreplyable,isreply,replaydoccount,accessorycount,t1.usertype,t1.doctype  from DocDetail  t1 where id>(select max(t.id) from (select top " + ((paramInt1 - 1) * paramInt2) + " id from DocDetail where doccreaterid=" + paramString + "order by id) t) and t1.doccreaterid=" + paramString + " order by id";
/*     */     } 
/*     */     
/* 164 */     recordSet.executeSql(str);
/* 165 */     ArrayList<DocumentDetail> arrayList = new ArrayList();
/* 166 */     ResourceComInfo resourceComInfo = new ResourceComInfo();
/* 167 */     while (recordSet.next()) {
/* 168 */       DocumentDetail documentDetail = new DocumentDetail();
/* 169 */       documentDetail.setId(recordSet.getString("id"));
/* 170 */       documentDetail.setTitle(recordSet.getString("docsubject"));
/* 171 */       documentDetail.setCreateDate(recordSet.getString("doccreatedate") + "<br>" + recordSet.getString("doccreatetime"));
/* 172 */       documentDetail.setModifyDate(recordSet.getString("doclastmoddate") + "<br>" + recordSet.getString("doclastmodtime"));
/* 173 */       documentDetail.setOwnerid(recordSet.getString("ownerid"));
/* 174 */       documentDetail.setReply(recordSet.getString("replaydoccount"));
/* 175 */       String str1 = recordSet.getString("docstatus");
/* 176 */       if (str1.equals("0")) {
/* 177 */         str1 = SystemEnv.getHtmlLabelName(220, paramInt3);
/* 178 */       } else if (str1.equals("1") || str1.equals("2")) {
/* 179 */         str1 = SystemEnv.getHtmlLabelName(1984, paramInt3);
/* 180 */       } else if (str1.equals("3")) {
/* 181 */         str1 = SystemEnv.getHtmlLabelName(360, paramInt3);
/* 182 */       } else if (str1.equals("4")) {
/* 183 */         str1 = SystemEnv.getHtmlLabelName(236, paramInt3);
/* 184 */       } else if (str1.equals("5")) {
/* 185 */         str1 = SystemEnv.getHtmlLabelName(251, paramInt3);
/*     */       } else {
/* 187 */         str1 = "";
/* 188 */       }  documentDetail.setStatus(str1);
/* 189 */       documentDetail.setOwner(resourceComInfo.getResourcename(recordSet.getString("ownerid")));
/* 190 */       arrayList.add(documentDetail);
/*     */     } 
/*     */     
/* 193 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static int countTasks(String paramString) {
/* 204 */     RecordSet recordSet = new RecordSet();
/* 205 */     String str1 = "Select count(*) From Prj_TaskProcess Where isdelete =0 and hrmid=" + paramString + " and (begindate<='2005-01-10' or begindate='x') and( enddate>='2005-01-10' or enddate='-' ) and (Finish < 100 or (Finish = 100 and Status <>0))";
/* 206 */     String str2 = "Select count(*) From Prj_TaskProcess Where isdelete =0 and hrmid=" + paramString + " and (enddate<'2005-01-10' and enddate <>'-') and (Finish < 100 or (Finish = 100 and Status <>0))";
/* 207 */     recordSet.executeSql(str1);
/* 208 */     recordSet.next();
/* 209 */     int i = recordSet.getInt(1);
/* 210 */     recordSet.executeSql(str2);
/* 211 */     recordSet.next();
/* 212 */     int j = recordSet.getInt(1);
/* 213 */     return i + j;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static ArrayList getTasksDetail(String paramString) {
/* 224 */     Date date = new Date();
/* 225 */     long l = date.getTime();
/* 226 */     Timestamp timestamp = new Timestamp(l);
/* 227 */     String str1 = timestamp.toString().substring(0, 4) + "-" + timestamp.toString().substring(5, 7) + "-" + timestamp.toString().substring(8, 10);
/* 228 */     String str2 = timestamp.toString().substring(11, 13) + ":" + timestamp.toString().substring(14, 16) + ":" + timestamp.toString().substring(17, 19);
/*     */ 
/*     */     
/* 231 */     ProjectTaskList projectTaskList1 = new ProjectTaskList();
/*     */     
/* 233 */     String str3 = "Select * From Prj_TaskProcess Where isdelete =0 and hrmid=" + paramString + " and (begindate<='" + str1 + "' or begindate='x') and ( enddate>='" + str1 + "' or enddate='-' )";
/*     */ 
/*     */     
/* 236 */     projectTaskList1.setSqlStr(str3);
/*     */     
/* 238 */     ArrayList arrayList1 = projectTaskList1.getTaskApproveList();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 245 */     ProjectTaskList projectTaskList2 = new ProjectTaskList();
/*     */     
/* 247 */     str3 = "Select * From Prj_TaskProcess Where isdelete =0 and hrmid=" + paramString + " and (enddate<'" + str1 + "' and enddate <>'-') and (Finish < 100 or (Finish = 100 and Status <>" + Character.MIN_VALUE + "))";
/*     */ 
/*     */     
/* 250 */     projectTaskList2.setSqlStr(str3);
/*     */     
/* 252 */     ArrayList arrayList2 = projectTaskList2.getTaskApproveList();
/* 253 */     arrayList1.addAll(arrayList2);
/* 254 */     return arrayList1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static int countCustoms(String paramString) {
/* 265 */     RecordSet recordSet = new RecordSet();
/* 266 */     String str = "select  count(*) from CRM_CustomerInfo  t1  where t1.deleted = 0  and t1.manager =" + paramString + "";
/* 267 */     recordSet.executeSql(str);
/* 268 */     recordSet.next();
/* 269 */     return recordSet.getInt(1);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static ArrayList getCustomDetail(String paramString, int paramInt1, int paramInt2, int paramInt3) {
/*     */     String str;
/* 282 */     RecordSet recordSet = new RecordSet();
/*     */     
/* 284 */     if (paramInt1 == 1) {
/* 285 */       if (recordSet.getDBType().equals("oracle")) {
/* 286 */         str = "select t1.id,t1.name from CRM_CustomerInfo  t1 where rownum<=" + paramInt2 + " and t1.deleted = 0  and t1.manager =" + paramString + " order by t1.id";
/* 287 */       } else if (recordSet.getDBType().equals("db2")) {
/* 288 */         str = "select t1.id,t1.name from CRM_CustomerInfo  t1 where  t1.deleted = 0  and t1.manager =" + paramString + " order by t1.id fetch first " + paramInt2 + " rows only";
/*     */       } else {
/* 290 */         str = "select top " + paramInt2 + " t1.id,t1.name from CRM_CustomerInfo t1 where t1.deleted = 0  and t1.manager =" + paramString + " order by t1.id";
/*     */       } 
/* 292 */     } else if (recordSet.getDBType().equals("oracle")) {
/* 293 */       str = "select * from (select a.* from (select t1.id,t1.name,rownum r from CRM_CustomerInfo  t1 where rownum<=" + (paramInt1 * paramInt2) + " and t1.deleted = 0  and t1.manager =" + paramString + " order by id) a) b where r >" + ((paramInt1 - 1) * paramInt2);
/* 294 */     } else if (recordSet.getDBType().equals("db2")) {
/* 295 */       str = "select t1.id,t1.name  from CRM_CustomerInfo  t1 where id>(select max(t.id) from (select  t1.id from CRM_CustomerInfo t1 where t1.deleted = 0  and t1.manager =" + paramString + "order by t1.id  fetch first " + ((paramInt1 - 1) * paramInt2) + " rows only  ) t)  and  t1.deleted = 0  and t1.manager =" + paramString + " order by t1.id  fetch first " + paramInt2 + " rows only";
/*     */     } else {
/* 297 */       str = "select top " + paramInt2 + " t1.id,t1.name  from CRM_CustomerInfo  t1 where id>(select max(t.id) from (select top " + ((paramInt1 - 1) * paramInt2) + " t1.id from CRM_CustomerInfo t1 where t1.deleted = 0  and t1.manager =" + paramString + "order by t1.id) t)  and  t1.deleted = 0  and t1.manager =" + paramString + " order by t1.id";
/*     */     } 
/*     */     
/* 300 */     recordSet.executeSql(str);
/* 301 */     ArrayList<CustomDetail> arrayList = new ArrayList();
/*     */     
/* 303 */     while (recordSet.next()) {
/* 304 */       CustomDetail customDetail = new CustomDetail();
/*     */       
/* 306 */       customDetail.setCustomer(recordSet.getString("name"));
/* 307 */       customDetail.setId(recordSet.getString("id"));
/*     */       
/* 309 */       arrayList.add(customDetail);
/*     */     } 
/*     */     
/* 312 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static double countDebts(String paramString) {
/* 321 */     RecordSet recordSet = new RecordSet();
/* 322 */     String str = "select sum(amount) as amount from fnaloaninfo where organizationtype=3 and organizationid=" + paramString;
/* 323 */     recordSet.executeSql(str);
/* 324 */     recordSet.next();
/* 325 */     BigDecimal bigDecimal = new BigDecimal(Util.getDoubleValue(recordSet.getString(1), 0.0D));
/* 326 */     str = "select sum(amountBorrow * borrowDirection) amt from FnaBorrowInfo a where a.applicantid=" + paramString;
/* 327 */     recordSet.executeSql(str);
/* 328 */     recordSet.next();
/* 329 */     bigDecimal = bigDecimal.add(new BigDecimal(Util.getDoubleValue(recordSet.getString(1), 0.0D)));
/* 330 */     return bigDecimal.setScale(2, 4).doubleValue();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static ArrayList getDebtsDetail(String paramString, int paramInt1, int paramInt2, int paramInt3) {
/*     */     String str;
/* 342 */     RecordSet recordSet = new RecordSet();
/*     */     
/* 344 */     if (paramInt1 == 1) {
/* 345 */       if (recordSet.getDBType().equals("oracle")) {
/* 346 */         str = "select  t1.occurdate,t1.amount,t1.credenceno,t1.releatedid,t1.releatedname from FnaLoanLog t1 where t1.resourceid= " + paramString + "  and rownum<=" + paramInt2 + " order by t1.id";
/* 347 */       } else if (recordSet.getDBType().equals("db2")) {
/* 348 */         str = "select  t1.occurdate,t1.amount,t1.credenceno,t1.releatedid,t1.releatedname from FnaLoanLog t1 where t1.resourceid= " + paramString + "   order by t1.id fetch first " + paramInt2 + " rows only ";
/*     */       } else {
/* 350 */         str = "select  top " + paramInt2 + "  t1.occurdate,t1.amount,t1.credenceno,t1.releatedid,t1.releatedname from FnaLoanLog t1 where t1.resourceid= " + paramString + "    order by t1.id";
/*     */       } 
/* 352 */     } else if (recordSet.getDBType().equals("oracle")) {
/* 353 */       str = "select * from (select a.* from (select t1.occurdate,t1.amount,t1.credenceno,t1.releatedid,t1.releatedname ,rownum r from FnaLoanLog t1 where t1.resourceid= " + paramString + "   and rownum<=" + (paramInt1 * paramInt2) + " order by t1.id)  a ) b where r>" + ((paramInt1 - 1) * paramInt2);
/* 354 */     } else if (recordSet.getDBType().equals("db2")) {
/* 355 */       str = "select  t1.occurdate,t1.amount,t1.credenceno,t1.releatedid,t1.releatedname  from FnaLoanLog t1 where t1.id>( select max(t.id) from(select   t1.id from FnaLoanLog t1 where t1.resourceid= " + paramString + "   order by t1.id  fetch first " + ((paramInt1 - 1) * paramInt2) + " rows only ) as t) and t1.resourceid= " + paramString + "    order by t1.id  fetch first " + paramInt2 + " rows only";
/*     */     } else {
/* 357 */       str = "select  top " + paramInt2 + " t1.occurdate,t1.amount,t1.credenceno,t1.releatedid,t1.releatedname  from FnaLoanLog t1 where t1.id>( select max(t.id) from(select  top " + ((paramInt1 - 1) * paramInt2) + " t1.id from FnaLoanLog t1 where t1.resourceid= " + paramString + "   order by t1.id) as t) and t1.resourceid= " + paramString + "    order by t1.id";
/*     */     } 
/* 359 */     recordSet.executeSql(str);
/* 360 */     ArrayList<DebtDetail> arrayList = new ArrayList();
/*     */     
/* 362 */     while (recordSet.next()) {
/* 363 */       DebtDetail debtDetail = new DebtDetail();
/* 364 */       String str1 = recordSet.getString("occurdate");
/* 365 */       String str2 = recordSet.getString("amount");
/* 366 */       String str3 = recordSet.getString("credenceno");
/* 367 */       String str4 = recordSet.getString("releatedid");
/*     */       
/* 369 */       String str5 = recordSet.getString("releatedname");
/*     */ 
/*     */       
/* 372 */       debtDetail.setDate(str1);
/* 373 */       debtDetail.setMoney(str2);
/* 374 */       debtDetail.setReceiptNo(str3);
/* 375 */       debtDetail.setReqid(str4);
/* 376 */       debtDetail.setRelatedReq(str5);
/*     */       
/* 378 */       arrayList.add(debtDetail);
/*     */     } 
/* 380 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static int countCapitals(String paramString) {
/* 391 */     RecordSet recordSet = new RecordSet();
/* 392 */     String str = "select count(*) from CptCapital t1  where isdata = '2' and resourceid =" + paramString;
/* 393 */     recordSet.executeSql(str);
/* 394 */     recordSet.next();
/* 395 */     return recordSet.getInt(1);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static ArrayList getCapitalsDetail(String paramString, int paramInt1, int paramInt2, int paramInt3) {
/*     */     String str;
/* 408 */     RecordSet recordSet = new RecordSet();
/*     */     
/* 410 */     if (paramInt1 == 1) {
/* 411 */       if (recordSet.getDBType().equals("oracle")) {
/* 412 */         str = "select  t1.id,t1.mark,t1.name,t1.capitalspec,t1.capitalgroupid,t1.resourceid,t1.departmentid,t1.stateid,t1.remark from CptCapital t1 where isdata = '2' and resourceid = " + paramString + "  and rownum<=" + paramInt2 + " order by t1.id";
/* 413 */       } else if (recordSet.getDBType().equals("db2")) {
/* 414 */         str = "select  t1.id,t1.mark,t1.name,t1.capitalspec,t1.capitalgroupid,t1.resourceid,t1.departmentid,t1.stateid,t1.remark from CptCapital t1 where isdata = '2' and resourceid = " + paramString + "   order by t1.id  fetch first " + paramInt2 + " rows only ";
/* 415 */       } else if (recordSet.getDBType().equals("sqlserver")) {
/* 416 */         str = "select  top " + paramInt2 + "  t1.id,t1.mark,t1.name,t1.capitalspec,t1.capitalgroupid,t1.resourceid,t1.departmentid,t1.stateid,t1.remark from CptCapital t1 where isdata = '2' and resourceid = " + paramString + "    order by t1.id";
/*     */       } else {
/* 418 */         str = "select   t1.id,t1.mark,t1.name,t1.capitalspec,t1.capitalgroupid,t1.resourceid,t1.departmentid,t1.stateid,t1.remark from CptCapital t1 where isdata = '2' and resourceid = " + paramString + "    order by t1.id limit " + paramInt2;
/*     */       }
/*     */     
/* 421 */     } else if (recordSet.getDBType().equals("oracle")) {
/* 422 */       str = "select * from (select a.* from (select t1.id,t1.mark,t1.name,t1.capitalspec,t1.capitalgroupid,t1.resourceid,t1.departmentid,t1.stateid,t1.remark ,rownum r from CptCapital t1 where isdata = '2' and resourceid = " + paramString + "   and rownum<=" + (paramInt1 * paramInt2) + " order by t1.id)  a ) b where r>" + ((paramInt1 - 1) * paramInt2);
/* 423 */     } else if (recordSet.getDBType().equals("db2")) {
/* 424 */       str = "select   t1.id,t1.mark,t1.name,t1.capitalspec,t1.capitalgroupid,t1.resourceid,t1.departmentid,t1.stateid,t1.remark  from CptCapital t1 where t1.id>( select max(t.id) from(select  t1.id from CptCapital t1 where isdata = '2' and resourceid = " + paramString + "   order by t1.id  fetch first " + ((paramInt1 - 1) * paramInt2) + " rows only ) as t) and isdata = '2' and resourceid = " + paramString + "    order by t1.id fetch first " + paramInt2 + " rows only";
/* 425 */     } else if (recordSet.getDBType().equals("sqlserver")) {
/* 426 */       str = "select  top " + paramInt2 + " t1.id,t1.mark,t1.name,t1.capitalspec,t1.capitalgroupid,t1.resourceid,t1.departmentid,t1.stateid,t1.remark  from CptCapital t1 where t1.id>( select max(t.id) from(select  top " + ((paramInt1 - 1) * paramInt2) + " t1.id from CptCapital t1 where isdata = '2' and resourceid = " + paramString + "   order by t1.id) as t) and isdata = '2' and resourceid = " + paramString + "    order by t1.id";
/*     */     } else {
/* 428 */       str = "select  t1.id,t1.mark,t1.name,t1.capitalspec,t1.capitalgroupid,t1.resourceid,t1.departmentid,t1.stateid,t1.remark  from CptCapital t1 where t1.id>( select max(t.id) from(select   t1.id from CptCapital t1 where isdata = '2' and resourceid = " + paramString + "   order by t1.id limit" + ((paramInt1 - 1) * paramInt2) + ") as t) and isdata = '2' and resourceid = " + paramString + "    order by t1.id limit " + paramInt2;
/*     */     } 
/*     */     
/* 431 */     recordSet.executeSql(str);
/* 432 */     ArrayList<CapitalDetail> arrayList = new ArrayList();
/*     */     
/* 434 */     while (recordSet.next()) {
/* 435 */       CapitalDetail capitalDetail = new CapitalDetail();
/* 436 */       String str1 = recordSet.getString("id");
/* 437 */       String str2 = recordSet.getString("mark");
/* 438 */       String str3 = recordSet.getString("name");
/* 439 */       String str4 = recordSet.getString("capitalspec");
/* 440 */       String str5 = recordSet.getString("capitalgroupid");
/*     */       
/* 442 */       String str6 = recordSet.getString("resourceid");
/* 443 */       String str7 = recordSet.getString("departmentid");
/* 444 */       String str8 = recordSet.getString("stateid");
/* 445 */       String str9 = recordSet.getString("remark");
/*     */ 
/*     */       
/* 448 */       capitalDetail.setId(str1);
/* 449 */       capitalDetail.setNo(str2);
/* 450 */       capitalDetail.setName(str3);
/* 451 */       capitalDetail.setType(str4);
/* 452 */       capitalDetail.setGroup(str5);
/* 453 */       capitalDetail.setUserid(str6);
/*     */       
/* 455 */       capitalDetail.setDeptid(str7);
/*     */       
/* 457 */       capitalDetail.setStatus(str8);
/* 458 */       capitalDetail.setRemark(str9);
/* 459 */       arrayList.add(capitalDetail);
/*     */     } 
/* 461 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static int countRoles(String paramString) {
/* 471 */     RecordSet recordSet = new RecordSet();
/* 472 */     String str = "select count(*) from HrmRoleMembers WHERE resourcetype=1 and resourceid=" + paramString;
/* 473 */     recordSet.executeSql(str);
/* 474 */     recordSet.next();
/* 475 */     return recordSet.getInt(1);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static ArrayList getRolesDetail(String paramString, int paramInt1, int paramInt2, int paramInt3) throws Exception {
/* 489 */     ArrayList<RoleDetail> arrayList = new ArrayList();
/* 490 */     RecordSet recordSet = new RecordSet();
/* 491 */     String str = "select * from HrmRoleMembers WHERE resourcetype=1 resourceid=" + paramString;
/* 492 */     recordSet.executeSql(str);
/* 493 */     while (recordSet.next()) {
/* 494 */       String str1 = recordSet.getString("roleid");
/* 495 */       RoleDetail roleDetail = new RoleDetail();
/* 496 */       roleDetail.setRoleid(str1);
/* 497 */       roleDetail.setRoleName(Util.toScreen((new RolesComInfo()).getRolesRemark(str1), paramInt3));
/* 498 */       String str2 = recordSet.getString("rolelevel");
/* 499 */       String str3 = "";
/* 500 */       if (str2.equals("2")) str3 = SystemEnv.getHtmlLabelName(140, paramInt3); 
/* 501 */       if (str2.equals("1")) str3 = SystemEnv.getHtmlLabelName(141, paramInt3); 
/* 502 */       if (str2.equals("0")) str3 = SystemEnv.getHtmlLabelName(124, paramInt3); 
/* 503 */       roleDetail.setRolelevel(str3);
/* 504 */       arrayList.add(roleDetail);
/*     */     } 
/* 506 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static int countCoworks(String paramString) {
/* 516 */     RecordSet recordSet = new RecordSet();
/* 517 */     String str = "select count(*) from cowork_items where coworkmanager=" + paramString;
/* 518 */     recordSet.executeSql(str);
/* 519 */     recordSet.next();
/* 520 */     return recordSet.getInt(1);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/hrm/resign/ResignProcess.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */