/*    */ package weaver.framework;
/*    */ 
/*    */ import java.util.ArrayList;
/*    */ import java.util.List;
/*    */ import weaver.general.BaseBean;
/*    */ import weaver.general.StaticObj;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public abstract class BaseCache<T>
/*    */   extends BaseBean
/*    */ {
/* 15 */   protected StaticObj staticobj = null;
/* 16 */   protected int current_index = -1;
/* 17 */   protected List<T> list = null;
/* 18 */   protected static Object lock = new Object();
/*    */   
/*    */   protected String cacheName;
/*    */   
/*    */   public BaseCache(String paramString) {
/* 23 */     this.cacheName = paramString;
/* 24 */     this.staticobj = StaticObj.getInstance();
/*    */     
/* 26 */     init();
/*    */   }
/*    */ 
/*    */   
/*    */   private void init() {
/*    */     try {
/* 32 */       synchronized (lock) {
/* 33 */         if (this.staticobj.getObject(this.cacheName) == null) setValue(); 
/* 34 */         this.list = (List<T>)this.staticobj.getRecordFromObj(this.cacheName, "list");
/* 35 */         if (this.list == null) setValue(); 
/*    */       } 
/* 37 */     } catch (Exception exception) {
/* 38 */       writeLog(exception);
/*    */     } 
/*    */   }
/*    */   
/*    */   private void setValue() throws Exception {
/* 43 */     if (this.list != null) {
/* 44 */       this.list.clear();
/*    */     } else {
/* 46 */       this.list = new ArrayList<T>();
/*    */     } 
/* 48 */     this.list.addAll(findResults());
/*    */     
/* 50 */     this.staticobj.putRecordToObj(this.cacheName, "list", this.list);
/*    */   }
/*    */   
/*    */   protected abstract List<T> findResults();
/*    */   
/*    */   public List<T> getResult() {
/* 56 */     return (this.list == null) ? new ArrayList<T>() : this.list;
/*    */   }
/*    */   
/*    */   public int length() {
/* 60 */     return (this.list == null) ? 0 : this.list.size();
/*    */   }
/*    */   
/*    */   public void remove() {
/* 64 */     if (this.staticobj.getObject(this.cacheName) != null)
/* 65 */       this.staticobj.removeObject(this.cacheName); 
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/framework/BaseCache.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */