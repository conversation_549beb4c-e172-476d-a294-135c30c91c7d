package weaver.framework;

import java.util.List;
import java.util.Map;

public interface BaseDao<T> {
  Comparable insert(T paramT);
  
  void update(T paramT);
  
  void delete(Comparable paramComparable);
  
  T get(Comparable paramComparable);
  
  List<T> find(Map<String, Comparable> paramMap);
}


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/framework/BaseDao.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */