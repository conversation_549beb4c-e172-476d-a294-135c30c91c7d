/*     */ package weaver.car;
/*     */ 
/*     */ import com.weaver.formmodel.util.DateHelper;
/*     */ import java.text.SimpleDateFormat;
/*     */ import java.util.Calendar;
/*     */ import weaver.dateformat.DateTransformer;
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CarDateTimeUtil
/*     */ {
/*  12 */   private static DateTransformer dateTransformer = new DateTransformer();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String castTimeByLength(String paramString) {
/*  20 */     if ("".equals(paramString)) {
/*  21 */       paramString = DateHelper.getCurrentTime();
/*     */     }
/*  23 */     if (paramString.length() == 5) {
/*  24 */       paramString = paramString + ":00";
/*     */     }
/*  26 */     return paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getServerDate(String paramString1, String paramString2) {
/*  36 */     paramString2 = castTimeByLength(paramString2);
/*  37 */     return dateTransformer.getServerDate(paramString1, paramString2);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getServerTime(String paramString1, String paramString2) {
/*  48 */     paramString2 = castTimeByLength(paramString2);
/*  49 */     return dateTransformer.getServerTime(paramString1, paramString2);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getShortServerTime(String paramString1, String paramString2) {
/*  59 */     paramString2 = getServerTime(paramString1, paramString2);
/*  60 */     if (paramString2.length() == 8) {
/*  61 */       paramString2 = paramString2.substring(0, 5);
/*     */     }
/*  63 */     return paramString2;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getServerDateTime(String paramString1, String paramString2) {
/*  73 */     paramString2 = castTimeByLength(paramString2);
/*  74 */     return dateTransformer.getServerDateTime(paramString1, paramString2);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getLocaleDate(String paramString1, String paramString2) {
/*  84 */     paramString2 = castTimeByLength(paramString2);
/*  85 */     return dateTransformer.getLocaleDate(paramString1, paramString2);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getLocaleTime(String paramString1, String paramString2) {
/*  95 */     paramString2 = castTimeByLength(paramString2);
/*  96 */     return dateTransformer.getLocaleTime(paramString1, paramString2);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getShortLocaleTime(String paramString1, String paramString2) {
/* 106 */     paramString2 = getLocaleTime(paramString1, paramString2);
/*     */     
/* 108 */     if (paramString2.length() == 8) {
/* 109 */       paramString2 = paramString2.substring(0, 5);
/*     */     }
/* 111 */     return paramString2;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getLocaleDateTime(String paramString1, String paramString2) {
/* 121 */     paramString2 = castTimeByLength(paramString2);
/* 122 */     return dateTransformer.getLocaleDateTime(paramString1, paramString2);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static Calendar getLocaleCurrentCalendar() {
/* 130 */     Calendar calendar = Calendar.getInstance();
/* 131 */     String str1 = DateHelper.getCurrentDate();
/* 132 */     String str2 = DateHelper.getCurrentTime();
/* 133 */     String str3 = dateTransformer.getLocaleDateTime(str1, str2);
/* 134 */     SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
/*     */     try {
/* 136 */       calendar.setTime(simpleDateFormat.parse(str3));
/* 137 */     } catch (Exception exception) {}
/*     */     
/* 139 */     return calendar;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getLocaleCurrentDateTime() {
/* 147 */     Calendar calendar = getLocaleCurrentCalendar();
/* 148 */     SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
/* 149 */     return simpleDateFormat.format(calendar.getTime());
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/car/CarDateTimeUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */