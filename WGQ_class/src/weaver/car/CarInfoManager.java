/*    */ package weaver.car;
/*    */ 
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.BaseBean;
/*    */ import weaver.general.Util;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class CarInfoManager
/*    */   extends BaseBean
/*    */ {
/*    */   public void copyCarWrokflowSet(int paramInt1, int paramInt2) {
/* 16 */     RecordSet recordSet1 = new RecordSet();
/* 17 */     RecordSet recordSet2 = new RecordSet();
/* 18 */     RecordSet recordSet3 = new RecordSet();
/*    */     
/*    */     try {
/* 21 */       recordSet1.executeSql("select id from carbasic where workflowid=" + paramInt1);
/*    */       
/* 23 */       String str1 = "";
/* 24 */       String str2 = "";
/* 25 */       while (recordSet1.next()) {
/* 26 */         str1 = Util.null2String(recordSet1.getString("id"));
/* 27 */         recordSet2.executeSql("insert into carbasic(workflowid,workflowname,typeid,wtype,formid,isuse,modifydate) select " + paramInt2 + ",workflowname,typeid,wtype,formid,isuse,modifydate from carbasic where workflowid=" + paramInt1);
/*    */ 
/*    */ 
/*    */         
/* 31 */         recordSet2.executeSql("select id from carbasic where workflowid=" + paramInt2);
/*    */         
/* 33 */         while (recordSet2.next()) {
/* 34 */           str2 = Util.null2String(recordSet2.getString("id"));
/* 35 */           recordSet3.executeSql("insert into mode_carrelatemode(mainid,carfieldid,modefieldid) select " + str2 + ",carfieldid,modefieldid from mode_carrelatemode where mainid=" + str1);
/*    */         }
/*    */       
/*    */       }
/*    */     
/*    */     }
/* 41 */     catch (Exception exception) {
/* 42 */       writeLog("复制用车流程设置失败！原流程id:" + paramInt1 + ",新流程id:" + paramInt2);
/*    */       
/* 44 */       exception.printStackTrace();
/*    */     } 
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/car/CarInfoManager.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */