/*    */ package weaver.car;
/*    */ 
/*    */ import weaver.cache.CacheBase;
/*    */ import weaver.cache.CacheColumn;
/*    */ import weaver.cache.CacheColumnType;
/*    */ import weaver.cache.PKColumn;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class CarTypeComInfo
/*    */   extends CacheBase
/*    */ {
/* 24 */   protected static String TABLE_NAME = "cartype";
/*    */   
/* 26 */   protected static String TABLE_WHERE = null;
/*    */   
/* 28 */   protected static String TABLE_ORDER = "name";
/*    */   @PKColumn(type = CacheColumnType.NUMBER)
/* 30 */   protected static String PK_NAME = "id";
/*    */   
/*    */   @CacheColumn(name = "name")
/*    */   protected static int name;
/*    */   @CacheColumn(name = "description")
/*    */   protected static int description;
/*    */   
/*    */   public int getCarTypeNum() {
/* 38 */     return size();
/*    */   }
/*    */   
/*    */   public boolean next() {
/* 42 */     return super.next();
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public boolean next(String paramString) {
/* 50 */     return false;
/*    */   }
/*    */ 
/*    */   
/*    */   public String getCarTypeid() {
/* 55 */     return (String)getRowValue(0);
/*    */   }
/*    */   
/*    */   public String getCarTypename() {
/* 59 */     return (String)getRowValue(name);
/*    */   }
/*    */ 
/*    */   
/*    */   public String getCarTypename(String paramString) {
/* 64 */     return (String)getValue(name, paramString);
/*    */   }
/*    */   public String getCarTypedesc() {
/* 67 */     return (String)getRowValue(description);
/*    */   }
/*    */ 
/*    */   
/*    */   public String getCarTypedesc(String paramString) {
/* 72 */     return (String)getValue(description, paramString);
/*    */   }
/*    */   
/*    */   public void removeCarTypeCache() {
/* 76 */     removeCache();
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/car/CarTypeComInfo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */