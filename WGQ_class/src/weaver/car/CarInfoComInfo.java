/*     */ package weaver.car;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import weaver.cache.CacheBase;
/*     */ import weaver.cache.CacheColumn;
/*     */ import weaver.cache.CacheColumnType;
/*     */ import weaver.cache.PKColumn;
/*     */ import weaver.conn.RecordSet;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CarInfoComInfo
/*     */   extends CacheBase
/*     */ {
/*  26 */   protected static String TABLE_NAME = "carinfo";
/*     */   
/*  28 */   protected static String TABLE_WHERE = null;
/*     */   
/*  30 */   protected static String TABLE_ORDER = "carno";
/*     */   @PKColumn(type = CacheColumnType.NUMBER)
/*  32 */   protected static String PK_NAME = "id";
/*     */   
/*     */   @CacheColumn(name = "carno")
/*     */   protected static int carno;
/*     */   
/*     */   @CacheColumn(name = "cartype")
/*     */   protected static int cartype;
/*     */   
/*     */   @CacheColumn(name = "factoryno")
/*     */   protected static int factoryno;
/*     */   
/*     */   @CacheColumn(name = "usefee")
/*     */   protected static int usefee;
/*     */   
/*     */   @CacheColumn(name = "subcompanyid")
/*     */   protected static int subcompanyid;
/*     */ 
/*     */   
/*     */   public void setCarInfoComInfo() throws Exception {}
/*     */ 
/*     */   
/*     */   public boolean next() {
/*  54 */     return super.next();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public boolean next(String paramString) {
/*  62 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getCarInfoComInfoNum() {
/*  69 */     return size();
/*     */   }
/*     */   
/*     */   public String getMeetingRoomInfoid() {
/*  73 */     return (String)getRowValue(0);
/*     */   }
/*     */   
/*     */   public String getCarNo() {
/*  77 */     return (String)getRowValue(carno);
/*     */   }
/*     */   
/*     */   public String getCarNo(String paramString) {
/*  81 */     return (String)getValue(carno, paramString);
/*     */   }
/*     */   
/*     */   public String getCarType() {
/*  85 */     return (String)getRowValue(cartype);
/*     */   }
/*     */   
/*     */   public String getCarType(String paramString) {
/*  89 */     return (String)getValue(cartype, paramString);
/*     */   }
/*     */   
/*     */   public String getFactoryNo() {
/*  93 */     return (String)getRowValue(factoryno);
/*     */   }
/*     */   
/*     */   public String getFactoryNo(String paramString) {
/*  97 */     return (String)getValue(factoryno, paramString);
/*     */   }
/*     */   
/*     */   public void removeCarInfoComInfoCache() {
/* 101 */     removeCache();
/*     */   }
/*     */   public static ArrayList getCarIds() {
/* 104 */     RecordSet recordSet = new RecordSet();
/* 105 */     ArrayList<String> arrayList = new ArrayList();
/* 106 */     recordSet.executeSql("select * from CarInfo");
/* 107 */     while (recordSet.next()) {
/* 108 */       arrayList.add(recordSet.getString("id"));
/*     */     }
/* 110 */     return arrayList;
/*     */   }
/*     */   
/*     */   public String getUsefee() {
/* 114 */     return (String)getRowValue(usefee);
/*     */   }
/*     */   public String getUsefee(String paramString) {
/* 117 */     return (String)getValue(usefee, paramString);
/*     */   }
/*     */   public String getSubcompanyid() {
/* 120 */     return (String)getRowValue(subcompanyid);
/*     */   }
/*     */   public String getSubcompanyid(String paramString) {
/* 123 */     return (String)getValue(subcompanyid, paramString);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/car/CarInfoComInfo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */