/*     */ package weaver.car;
/*     */ 
/*     */ import com.api.car.util.CarUtil;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.dateformat.DateTransformer;
/*     */ import weaver.dateformat.UnifiedConversionInterface;
/*     */ import weaver.general.ThreadVarLanguage;
/*     */ import weaver.general.TimeUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.workflow.form.FormManager;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CarInfoReport
/*     */ {
/*     */   private static final int Type_BYMONTH = 2;
/*     */   private static final int Type_BYWEEK = 3;
/*     */   private static final int Type_BYDAY = 4;
/*  34 */   ResourceComInfo rc = null;
/*  35 */   ArrayList carIds = null;
/*     */   public CarInfoReport() {
/*  37 */     this.carIds = CarInfoComInfo.getCarIds();
/*     */     try {
/*  39 */       this.rc = new ResourceComInfo();
/*  40 */     } catch (Exception exception) {}
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getSql(String paramString, int paramInt1, int paramInt2) {
/*     */     byte b;
/*     */     UnifiedConversionInterface unifiedConversionInterface;
/*  53 */     String str1 = "";
/*  54 */     RecordSet recordSet1 = new RecordSet();
/*  55 */     RecordSet recordSet2 = new RecordSet();
/*  56 */     FormManager formManager = new FormManager();
/*  57 */     String str2 = "";
/*  58 */     if (recordSet1.getDBType().equals("oracle")) {
/*  59 */       str2 = str2 + "(select id,requestid,to_number(carId) as carId,to_number(driver) as driver,to_number(userid) as userid,startdate,starttime,enddate,endtime,cancel from CarUseApprove";
/*  60 */     } else if (recordSet1.getDBType().equals("mysql")) {
/*  61 */       str2 = str2 + "(select id,requestid,carId,driver,userid,startdate,starttime,enddate,endtime,cancel from CarUseApprove";
/*     */     } else {
/*  63 */       str2 = str2 + "(select id,requestid,carId,driver,userid,startdate,starttime,enddate,endtime,cancel from CarUseApprove";
/*     */     } 
/*  65 */     recordSet1.executeSql("select id,formid,workflowid from carbasic where formid!=163 and isuse = 1  and workflowid in (select id from workflow_base)");
/*  66 */     while (recordSet1.next()) {
/*  67 */       String str3 = recordSet1.getString("id");
/*  68 */       String str4 = recordSet1.getString("formid");
/*  69 */       String str5 = formManager.getTablename(str4);
/*  70 */       if (str5 == null || "".equals(str5))
/*  71 */         continue;  String str6 = recordSet1.getString("workflowid");
/*  72 */       str2 = str2 + " union all select id,requestid,";
/*  73 */       HashMap<Object, Object> hashMap = new HashMap<>();
/*  74 */       recordSet2.executeSql("select carfieldid,modefieldid,fieldname from mode_carrelatemode c,workflow_billfield b where c.modefieldid=b.id and mainid=" + str3);
/*  75 */       while (recordSet2.next()) {
/*  76 */         String str7 = recordSet2.getString("carfieldid");
/*  77 */         String str8 = recordSet2.getString("modefieldid");
/*  78 */         String str9 = recordSet2.getString("fieldname");
/*  79 */         hashMap.put(str7, str9);
/*     */       } 
/*  81 */       str2 = str2 + CarUtil.addSql(hashMap);
/*  82 */       str2 = str2 + " from " + str5;
/*  83 */       str2 = str2 + " where (select max(workflowid) from workflow_requestbase where requestid=" + str5 + ".requestid)=" + str6;
/*     */     } 
/*  85 */     str2 = str2 + ")";
/*     */     
/*  87 */     switch (paramInt1) {
/*     */       case 2:
/*  89 */         str1 = "select t1.*,t2.currentnodetype from " + str2 + " t1,workflow_requestbase t2 where ('" + paramString + "' between SUBSTRING(t1.startDate,1,7) and SUBSTRING(t1.endDate,1,7)) and t1.carId = " + paramInt2 + " and (t1.enddate is not null and t1.endtime is not null) and t1.requestid=t2.requestid and t2.workflowid not in (select workflowid from carbasic where isuse=0) and t2.currentnodetype<>0 order by t1.startDate desc";
/*     */         break;
/*     */ 
/*     */       
/*     */       case 3:
/*  94 */         str1 = "select t1.*,t2.currentnodetype from " + str2 + " t1,workflow_requestbase t2 where ( ";
/*  95 */         for (b = -1; b < 6; b++) {
/*  96 */           String str = TimeUtil.dateAdd(paramString, b);
/*  97 */           str1 = str1 + "('" + str + "' between t1.startDate and t1.endDate) or";
/*     */         } 
/*  99 */         str1 = str1.substring(0, str1.length() - 2);
/* 100 */         str1 = str1 + ") and t1.carId = " + paramInt2 + " and (t1.enddate is not null and t1.endtime is not null) and t1.requestid=t2.requestid and t2.workflowid not in (select workflowid from carbasic where isuse=0) and t2.currentnodetype<>0 order by t1.startDate desc";
/*     */         break;
/*     */       case 4:
/* 103 */         unifiedConversionInterface = new UnifiedConversionInterface();
/* 104 */         if (unifiedConversionInterface.getTimeZoneStatus()) {
/* 105 */           String str3 = (new DateTransformer()).getServerDate(paramString, "00:00:00");
/* 106 */           String str4 = (new DateTransformer()).getServerTime(paramString, "00:00:00");
/* 107 */           String str5 = (new DateTransformer()).getServerDate(paramString, "23:59:59");
/* 108 */           String str6 = (new DateTransformer()).getServerTime(paramString, "23:59:59");
/* 109 */           str1 = "select t1.*,t2.currentnodetype from " + str2 + " t1,workflow_requestbase t2 where ";
/* 110 */           if (recordSet1.getDBType().equals("oracle")) {
/* 111 */             str1 = str1 + "  ((t1.startDate ||' '||t1.startTime >= '" + str3 + " " + str4 + "' AND t1.startDate ||' '||t1.startTime <= '" + str5 + " " + str6 + "') OR (t1.startDate ||' '||t1.startTime <= '" + str3 + " " + str4 + "' AND t1.endDate||' '||t1.endTime >= '" + str5 + " " + str6 + "') OR (t1.endDate||' '||t1.endTime >= '" + str3 + " " + str4 + "' AND t1.endDate||' '||t1.endTime <= '" + str5 + " " + str6 + "') )";
/*     */           
/*     */           }
/* 114 */           else if (recordSet1.getDBType().equals("mysql")) {
/* 115 */             str1 = str1 + "  ((concat(t1.startDate,' ',t1.startTime) >= concat('" + str3 + "',' ','" + str4 + "') AND concat(t1.startDate,' ',t1.startTime) <= concat('" + str5 + "',' ','" + str6 + "')) OR (concat(t1.startDate ,' ',t1.startTime) <= concat('" + str3 + "',' ','" + str4 + "') AND concat(t1.endDate,' ',t1.endTime) >= concat('" + str5 + "',' ','" + str6 + "')) OR (concat(t1.endDate,' ',t1.endTime) >= concat('" + str3 + "',' ','" + str4 + "') AND concat(t1.endDate,' ',t1.endTime) <= concat('" + str5 + "',' ','" + str6 + "')))";
/*     */ 
/*     */           
/*     */           }
/* 119 */           else if (recordSet1.getDBType().equals("postgresql")) {
/* 120 */             str1 = str1 + "  ((t1.startDate ||' '||t1.startTime >= '" + str3 + " " + str4 + "' AND t1.startDate ||' '||t1.startTime <= '" + str5 + " " + str6 + "') OR (t1.startDate ||' '||t1.startTime <= '" + str3 + " " + str4 + "' AND t1.endDate||' '||t1.endTime >= '" + str5 + " " + str6 + "') OR (t1.endDate||' '||t1.endTime >= '" + str3 + " " + str4 + "' AND t1.endDate||' '||t1.endTime <= '" + str5 + " " + str6 + "') )";
/*     */           
/*     */           }
/*     */           else {
/*     */             
/* 125 */             str1 = str1 + "  ((t1.startDate +' '+t1.startTime >= '" + str3 + " " + str4 + "' AND t1.startDate +' '+t1.startTime <= '" + str5 + " " + str6 + "') OR (t1.startDate +' '+t1.startTime <= '" + str3 + " " + str4 + "' AND t1.endDate+' '+t1.endTime >= '" + str5 + " " + str6 + "') OR (t1.endDate+' '+t1.endTime >= '" + str3 + " " + str4 + "' AND t1.endDate+' '+t1.endTime <= '" + str5 + " " + str6 + "'))";
/*     */           }
/*     */         
/*     */         } else {
/*     */           
/* 130 */           str1 = "select t1.*,t2.currentnodetype from " + str2 + " t1,workflow_requestbase t2 where ('" + paramString + "' between t1.startDate and t1.endDate)";
/*     */         } 
/*     */ 
/*     */ 
/*     */         
/* 135 */         str1 = str1 + " and t1.carId = " + paramInt2 + " and (t1.enddate is not null and t1.endtime is not null) and t1.requestid=t2.requestid and t2.workflowid not in (select workflowid from carbasic where isuse=0) and t2.currentnodetype<>0 order by t1.startTime desc";
/*     */         break;
/*     */     } 
/*     */     
/* 139 */     if (recordSet2.getDBType().equals("oracle")) {
/* 140 */       str1 = Util.StringReplace(str1, "SUBSTRING", "substr");
/*     */     }
/* 142 */     return str1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public HashMap getMapping(String paramString1, int paramInt, String paramString2) {
/* 152 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/* 154 */     RecordSet recordSet = new RecordSet();
/* 155 */     ArrayList<String> arrayList = new ArrayList();
/* 156 */     recordSet.executeSql("select * from CarInfo where id = '" + paramString2 + "'");
/* 157 */     while (recordSet.next()) {
/* 158 */       arrayList.add(recordSet.getString("id"));
/*     */     }
/* 160 */     String str = "";
/*     */     
/* 162 */     for (byte b = 0; b < arrayList.size(); b++) {
/* 163 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/*     */       
/* 165 */       ArrayList<String> arrayList1 = new ArrayList();
/* 166 */       ArrayList<String> arrayList2 = new ArrayList();
/* 167 */       ArrayList<String> arrayList3 = new ArrayList();
/* 168 */       ArrayList<String> arrayList4 = new ArrayList();
/* 169 */       ArrayList<String> arrayList5 = new ArrayList();
/* 170 */       ArrayList<String> arrayList6 = new ArrayList();
/* 171 */       ArrayList<String> arrayList7 = new ArrayList();
/* 172 */       ArrayList<String> arrayList8 = new ArrayList();
/* 173 */       ArrayList<String> arrayList9 = new ArrayList();
/* 174 */       ArrayList<String> arrayList10 = new ArrayList();
/* 175 */       ArrayList<String> arrayList11 = new ArrayList();
/* 176 */       ArrayList<String> arrayList12 = new ArrayList();
/* 177 */       ArrayList<String> arrayList13 = new ArrayList();
/*     */ 
/*     */       
/* 180 */       if (paramInt == 2 || paramInt == 3 || paramInt == 4)
/*     */       {
/*     */         
/* 183 */         str = getSql(paramString1, paramInt, Util.getIntValue(arrayList.get(b)));
/*     */       }
/* 185 */       RecordSet recordSet1 = new RecordSet();
/* 186 */       recordSet1.executeSql(str);
/* 187 */       while (recordSet1.next()) {
/* 188 */         String str1 = Util.null2String(recordSet1.getString("id"));
/* 189 */         String str2 = Util.null2String(recordSet1.getString("driver"));
/* 190 */         String str3 = Util.null2String(recordSet1.getString("userid"));
/* 191 */         String str4 = Util.null2String(recordSet1.getString("departmentId"));
/* 192 */         String str5 = Util.null2String(recordSet1.getString("applier"));
/* 193 */         String str6 = Util.null2String(recordSet1.getString("mileage"));
/* 194 */         String str7 = Util.null2String(recordSet1.getString("startDate"));
/* 195 */         String str8 = Util.null2String(recordSet1.getString("endDate"));
/* 196 */         String str9 = Util.null2String(recordSet1.getString("startTime"));
/* 197 */         String str10 = Util.null2String(recordSet1.getString("endTime"));
/* 198 */         String str11 = Util.null2String(recordSet1.getString("requestid"));
/* 199 */         String str12 = Util.null2String(recordSet1.getString("cancel"));
/* 200 */         String str13 = Util.null2String(recordSet1.getString("currentnodetype"));
/*     */         
/* 202 */         if (str9.equals("")) str9 = "00:00"; 
/* 203 */         if (str10.equals("")) str10 = "24:00";
/*     */         
/* 205 */         arrayList1.add(str1);
/* 206 */         arrayList2.add(str2);
/* 207 */         arrayList3.add(str3);
/* 208 */         arrayList4.add(str4);
/* 209 */         arrayList5.add(str5);
/* 210 */         arrayList6.add(str6);
/* 211 */         arrayList7.add(str7);
/* 212 */         arrayList8.add(str8);
/* 213 */         arrayList9.add(str9);
/* 214 */         arrayList10.add(str10);
/* 215 */         arrayList11.add(str11);
/* 216 */         arrayList12.add(str12);
/* 217 */         arrayList13.add(str13);
/*     */       } 
/* 219 */       hashMap1.put("ids", arrayList1);
/* 220 */       hashMap1.put("drivers", arrayList2);
/* 221 */       hashMap1.put("userids", arrayList3);
/* 222 */       hashMap1.put("departmentIds", arrayList4);
/* 223 */       hashMap1.put("appliers", arrayList5);
/* 224 */       hashMap1.put("mileages", arrayList6);
/* 225 */       hashMap1.put("startDates", arrayList7);
/* 226 */       hashMap1.put("endDates", arrayList8);
/* 227 */       hashMap1.put("startTimes", arrayList9);
/* 228 */       hashMap1.put("endTimes", arrayList10);
/* 229 */       hashMap1.put("requestids", arrayList11);
/* 230 */       hashMap1.put("cancels", arrayList12);
/* 231 */       hashMap1.put("currentnodetypes", arrayList13);
/*     */       
/* 233 */       hashMap.put("" + arrayList.get(b), hashMap1);
/*     */     } 
/* 235 */     return hashMap;
/*     */   }
/*     */   public String getCarInfoUseCase(String paramString1, String paramString2, String paramString3, String paramString4, String paramString5, String paramString6, String paramString7) {
/* 238 */     String str = "";
/*     */ 
/*     */ 
/*     */     
/* 242 */     str = "" + SystemEnv.getHtmlLabelName(20319, ThreadVarLanguage.getLang()) + ":  " + paramString1 + "  " + SystemEnv.getHtmlLabelName(17649, ThreadVarLanguage.getLang()) + ":   " + this.rc.getResourcename(paramString2) + "\n" + SystemEnv.getHtmlLabelName(17670, ThreadVarLanguage.getLang()) + ":  " + this.rc.getResourcename(paramString3) + "\n" + SystemEnv.getHtmlLabelName(24978, ThreadVarLanguage.getLang()) + ":  " + paramString4 + "  " + SystemEnv.getHtmlLabelName(1323, ThreadVarLanguage.getLang()) + ":  " + paramString5 + "\n" + SystemEnv.getHtmlLabelName(83825, ThreadVarLanguage.getLang()) + ":  " + paramString6 + "  " + SystemEnv.getHtmlLabelName(83826, ThreadVarLanguage.getLang()) + ":  " + paramString7 + "\n";
/* 243 */     return str;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/car/CarInfoReport.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */