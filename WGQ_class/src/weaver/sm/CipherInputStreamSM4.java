/*     */ package weaver.sm;
/*     */ 
/*     */ import java.io.FilterInputStream;
/*     */ import java.io.IOException;
/*     */ import java.io.InputStream;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CipherInputStreamSM4
/*     */   extends FilterInputStream
/*     */ {
/*     */   private byte[] cipher;
/*     */   private InputStream input;
/*  83 */   private byte[] ibuffer = new byte[512];
/*     */ 
/*     */   
/*     */   private boolean done = false;
/*     */ 
/*     */   
/*     */   private byte[] obuffer;
/*     */ 
/*     */   
/*  92 */   private int ostart = 0;
/*     */   
/*  94 */   private int ofinish = 0;
/*     */   
/*     */   private int mode;
/*     */   
/*  98 */   private long totalGroup = 0L;
/*     */   
/* 100 */   private long currentGroup = 0L;
/*     */   
/* 102 */   private long lastGroupSize = 0L;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public CipherInputStreamSM4(InputStream paramInputStream, byte[] paramArrayOfbyte, int paramInt) {
/* 119 */     this(paramInputStream, paramArrayOfbyte, paramInt, 0L);
/*     */   }
/*     */ 
/*     */   
/*     */   public CipherInputStreamSM4(InputStream paramInputStream, byte[] paramArrayOfbyte, int paramInt, long paramLong) {
/* 124 */     super(paramInputStream);
/* 125 */     if (paramInt == 0) {
/*     */       try {
/* 127 */         this.totalGroup = paramLong / this.ibuffer.length;
/* 128 */         this.lastGroupSize = paramLong % this.ibuffer.length;
/*     */         
/* 130 */         this.totalGroup++;
/*     */       
/*     */       }
/* 133 */       catch (Exception exception) {
/* 134 */         exception.printStackTrace();
/*     */       } 
/*     */     }
/* 137 */     this.input = paramInputStream;
/* 138 */     this.cipher = paramArrayOfbyte;
/*     */     
/* 140 */     this.mode = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private int getMoreData() throws IOException {
/* 156 */     if (this.done) return -1; 
/* 157 */     int i = this.input.read(this.ibuffer);
/* 158 */     if (i == -1) {
/* 159 */       this.done = true;
/*     */       
/*     */       try {
/* 162 */         this.obuffer = null;
/*     */       
/*     */       }
/* 165 */       catch (Exception exception) {
/* 166 */         exception.printStackTrace();
/* 167 */         this.obuffer = null;
/*     */       } 
/* 169 */       if (this.obuffer == null) {
/* 170 */         return -1;
/*     */       }
/* 172 */       this.ostart = 0;
/* 173 */       this.ofinish = this.obuffer.length;
/* 174 */       return this.ofinish;
/*     */     } 
/*     */     
/*     */     try {
/* 178 */       this.currentGroup++;
/* 179 */       int j = i;
/* 180 */       if (this.currentGroup == this.totalGroup) {
/* 181 */         j = (int)this.lastGroupSize;
/*     */       }
/*     */ 
/*     */       
/* 185 */       byte[] arrayOfByte = new byte[i];
/* 186 */       System.arraycopy(this.ibuffer, 0, arrayOfByte, 0, i);
/* 187 */       update(arrayOfByte, j);
/* 188 */     } catch (Exception exception) {
/* 189 */       exception.printStackTrace();
/* 190 */       this.obuffer = null;
/*     */     } 
/* 192 */     this.ostart = 0;
/* 193 */     if (this.obuffer == null)
/* 194 */     { this.ofinish = 0; }
/* 195 */     else { this.ofinish = this.obuffer.length; }
/* 196 */      return this.ofinish;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private byte[] update(byte[] paramArrayOfbyte, int paramInt) throws Exception {
/* 206 */     if (this.mode == 1) {
/* 207 */       this.obuffer = SM4Util.encode(paramArrayOfbyte, this.cipher);
/* 208 */     } else if (this.mode == 0) {
/*     */       
/* 210 */       this.obuffer = SM4Util.decode(paramArrayOfbyte, this.cipher, paramInt);
/*     */     } else {
/* 212 */       this.obuffer = null;
/*     */     } 
/* 214 */     return this.obuffer;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int read() throws IOException {
/* 233 */     if (this.ostart >= this.ofinish) {
/*     */       
/* 235 */       int i = 0;
/* 236 */       for (; !i; i = getMoreData());
/* 237 */       if (i == -1) return -1; 
/*     */     } 
/* 239 */     return this.obuffer[this.ostart++] & 0xFF;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int read(byte[] paramArrayOfbyte) throws IOException {
/* 259 */     return read(paramArrayOfbyte, 0, paramArrayOfbyte.length);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int read(byte[] paramArrayOfbyte, int paramInt1, int paramInt2) throws IOException {
/* 281 */     if (this.ostart >= this.ofinish) {
/*     */       
/* 283 */       int j = 0;
/* 284 */       for (; !j; j = getMoreData());
/* 285 */       if (j == -1) return -1; 
/*     */     } 
/* 287 */     if (paramInt2 <= 0) {
/* 288 */       return 0;
/*     */     }
/* 290 */     int i = this.ofinish - this.ostart;
/* 291 */     if (paramInt2 < i) i = paramInt2; 
/* 292 */     if (paramArrayOfbyte != null) {
/* 293 */       System.arraycopy(this.obuffer, this.ostart, paramArrayOfbyte, paramInt1, i);
/*     */     }
/* 295 */     this.ostart += i;
/* 296 */     return i;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public long skip(long paramLong) throws IOException {
/* 319 */     int i = this.ofinish - this.ostart;
/* 320 */     if (paramLong > i) {
/* 321 */       paramLong = i;
/*     */     }
/* 323 */     if (paramLong < 0L) {
/* 324 */       return 0L;
/*     */     }
/* 326 */     this.ostart = (int)(this.ostart + paramLong);
/* 327 */     return paramLong;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int available() throws IOException {
/* 342 */     return this.ofinish - this.ostart;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void close() throws IOException {
/* 357 */     this.input.close();
/* 358 */     this.ostart = 0;
/* 359 */     this.ofinish = 0;
/*     */   }
/*     */   
/*     */   public void reset() {
/* 363 */     this.ostart = 0;
/* 364 */     this.ofinish = 0;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean markSupported() {
/* 378 */     return false;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/sm/CipherInputStreamSM4.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */