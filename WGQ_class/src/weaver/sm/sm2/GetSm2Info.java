/*    */ package weaver.sm.sm2;
/*    */ 
/*    */ import java.io.IOException;
/*    */ import java.io.Serializable;
/*    */ import javax.servlet.ServletException;
/*    */ import javax.servlet.http.HttpServlet;
/*    */ import javax.servlet.http.HttpServletRequest;
/*    */ import javax.servlet.http.HttpServletResponse;
/*    */ import org.json.JSONException;
/*    */ import org.json.JSONObject;
/*    */ import org.slf4j.Logger;
/*    */ import org.slf4j.LoggerFactory;
/*    */ import weaver.sm.sm2.util.Sm2EncDecUtils;
/*    */ import weaver.sm.sm2.vo.Sm2KeyVO;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class GetSm2Info
/*    */   extends HttpServlet
/*    */   implements Serializable
/*    */ {
/* 24 */   private static final Logger logger = LoggerFactory.getLogger(GetSm2Info.class);
/*    */ 
/*    */   
/*    */   private static final long serialVersionUID = -1444538744910431737L;
/*    */ 
/*    */   
/*    */   protected void doGet(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws ServletException, IOException {
/* 31 */     doPost(paramHttpServletRequest, paramHttpServletResponse);
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   protected void doPost(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws ServletException, IOException {
/* 37 */     new Sm2EncDecUtils();
/* 38 */     Sm2KeyVO sm2KeyVO = new Sm2KeyVO();
/* 39 */     JSONObject jSONObject = new JSONObject();
/*    */     try {
/* 41 */       jSONObject.put("sm2_pub", sm2KeyVO.getPubHexInSoft());
/* 42 */       jSONObject.put("sm2_code", Sm2EncDecUtils.generateSalt(paramHttpServletRequest, paramHttpServletResponse));
/* 43 */       jSONObject.put("sm2_flag", "``SM2``");
/* 44 */     } catch (JSONException jSONException) {
/* 45 */       logger.error("获取公钥异常", (Throwable)jSONException);
/*    */     } 
/*    */     
/* 48 */     paramHttpServletResponse.setContentType("application/json; charset=UTF-8");
/* 49 */     paramHttpServletResponse.getWriter().println(jSONObject.toString());
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/sm/sm2/GetSm2Info.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */