/*    */ package weaver.sm.sm2.vo;
/*    */ 
/*    */ import java.io.File;
/*    */ import java.io.FileInputStream;
/*    */ import java.io.FileNotFoundException;
/*    */ import java.io.IOException;
/*    */ import java.nio.charset.Charset;
/*    */ import weaver.sm.sm2.util.Sm2EncDecUtils;
/*    */ import weaver.sm.sm2.util.Sm2IOUtils;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class Sm2KeyVO
/*    */ {
/*    */   private static final String PUB_KEY_NAME = "ec_sm2_2048_public.key";
/*    */   private static final String PRI_KEY_NAME = "ec_sm2_2048_private.key";
/*    */   public static final String UTF8_ENCODING = "UTF-8";
/* 20 */   private static String rootPath = Sm2EncDecUtils.getProjectPath();
/*    */ 
/*    */ 
/*    */   
/*    */   private String privateKey;
/*    */ 
/*    */   
/*    */   private String publicKey;
/*    */ 
/*    */ 
/*    */   
/*    */   public String getPrivateKey() {
/* 32 */     return this.privateKey;
/*    */   }
/*    */   
/*    */   public void setPrivateKey(String paramString) {
/* 36 */     this.privateKey = paramString;
/*    */   }
/*    */   
/*    */   public String getPublicKey() {
/* 40 */     return this.publicKey;
/*    */   }
/*    */   
/*    */   public void setPublicKey(String paramString) {
/* 44 */     this.publicKey = paramString;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getPubHexInSoft() throws IOException {
/* 51 */     return readFileToString(new File(rootPath + "keys" + File.separator + "ec_sm2_2048_public.key"), Charset.forName("UTF-8"));
/*    */   }
/*    */ 
/*    */   
/*    */   public String getPriHexInSoft() throws IOException {
/* 56 */     return readFileToString(new File(rootPath + "keys" + File.separator + "ec_sm2_2048_private.key"), Charset.forName("UTF-8"));
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public static String readFileToString(File paramFile, Charset paramCharset) throws IOException {
/* 62 */     FileInputStream fileInputStream = null;
/*    */     try {
/* 64 */       fileInputStream = openInputStream(paramFile);
/* 65 */       return Sm2IOUtils.toString(fileInputStream, (paramCharset == null) ? Charset.defaultCharset() : paramCharset);
/*    */     } finally {
/* 67 */       Sm2IOUtils.closeQuietly(fileInputStream);
/*    */     } 
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public static FileInputStream openInputStream(File paramFile) throws IOException {
/* 74 */     if (paramFile.exists()) {
/* 75 */       if (paramFile.isDirectory()) {
/* 76 */         throw new IOException("File '" + paramFile + "' exists but is a directory");
/*    */       }
/* 78 */       if (!paramFile.canRead()) {
/* 79 */         throw new IOException("File '" + paramFile + "' cannot be read");
/*    */       }
/*    */     } else {
/* 82 */       throw new FileNotFoundException("File '" + paramFile + "' does not exist");
/*    */     } 
/* 84 */     return new FileInputStream(paramFile);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/sm/sm2/vo/Sm2KeyVO.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */