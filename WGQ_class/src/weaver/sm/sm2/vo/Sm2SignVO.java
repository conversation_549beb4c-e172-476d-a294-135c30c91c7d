/*     */ package weaver.sm.sm2.vo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class Sm2SignVO
/*     */ {
/*     */   public String sm2_userd;
/*     */   public String x_coord;
/*     */   public String y_coord;
/*     */   public String sm3_z;
/*     */   public String sign_express;
/*     */   public String sm3_digest;
/*     */   public String sign_r;
/*     */   public String sign_s;
/*     */   public String verify_r;
/*     */   public String verify_s;
/*     */   public String sm2_sign;
/*     */   public String sm2_type;
/*     */   public boolean isVerify;
/*     */   
/*     */   public String getX_coord() {
/*  38 */     return this.x_coord;
/*     */   }
/*     */   public void setX_coord(String paramString) {
/*  41 */     this.x_coord = paramString;
/*     */   }
/*     */   public String getY_coord() {
/*  44 */     return this.y_coord;
/*     */   }
/*     */   public void setY_coord(String paramString) {
/*  47 */     this.y_coord = paramString;
/*     */   }
/*     */   public String getSm3_z() {
/*  50 */     return this.sm3_z;
/*     */   }
/*     */   public void setSm3_z(String paramString) {
/*  53 */     this.sm3_z = paramString;
/*     */   }
/*     */   public String getSm3_digest() {
/*  56 */     return this.sm3_digest;
/*     */   }
/*     */   public void setSm3_digest(String paramString) {
/*  59 */     this.sm3_digest = paramString;
/*     */   }
/*     */   public String getSm2_signForSoft() {
/*  62 */     return this.sm2_sign;
/*     */   }
/*     */ 
/*     */   
/*     */   public String getSm2_signForHard() {
/*  67 */     return getSign_r() + getSign_s();
/*     */   }
/*     */   public void setSm2_sign(String paramString) {
/*  70 */     this.sm2_sign = paramString;
/*     */   }
/*     */   public String getSign_express() {
/*  73 */     return this.sign_express;
/*     */   }
/*     */   public void setSign_express(String paramString) {
/*  76 */     this.sign_express = paramString;
/*     */   }
/*     */   public String getSm2_userd() {
/*  79 */     return this.sm2_userd;
/*     */   }
/*     */   public void setSm2_userd(String paramString) {
/*  82 */     this.sm2_userd = paramString;
/*     */   }
/*     */   public String getSm2_type() {
/*  85 */     return this.sm2_type;
/*     */   }
/*     */   public void setSm2_type(String paramString) {
/*  88 */     this.sm2_type = paramString;
/*     */   }
/*     */   public boolean isVerify() {
/*  91 */     return this.isVerify;
/*     */   }
/*     */   public void setVerify(boolean paramBoolean) {
/*  94 */     this.isVerify = paramBoolean;
/*     */   }
/*     */   public String getSign_r() {
/*  97 */     return this.sign_r;
/*     */   }
/*     */   public void setSign_r(String paramString) {
/* 100 */     this.sign_r = paramString;
/*     */   }
/*     */   public String getSign_s() {
/* 103 */     return this.sign_s;
/*     */   }
/*     */   public void setSign_s(String paramString) {
/* 106 */     this.sign_s = paramString;
/*     */   }
/*     */   public String getVerify_r() {
/* 109 */     return this.verify_r;
/*     */   }
/*     */   public void setVerify_r(String paramString) {
/* 112 */     this.verify_r = paramString;
/*     */   }
/*     */   public String getVerify_s() {
/* 115 */     return this.verify_s;
/*     */   }
/*     */   public void setVerify_s(String paramString) {
/* 118 */     this.verify_s = paramString;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/sm/sm2/vo/Sm2SignVO.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */