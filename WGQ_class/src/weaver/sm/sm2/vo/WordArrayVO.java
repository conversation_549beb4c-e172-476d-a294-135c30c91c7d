/*    */ package weaver.sm.sm2.vo;
/*    */ 
/*    */ import java.util.Arrays;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class WordArrayVO
/*    */ {
/*    */   private int[] words;
/*    */   private int sigBytes;
/*    */   
/*    */   public WordArrayVO() {}
/*    */   
/*    */   public WordArrayVO(int[] paramArrayOfint, int paramInt) {
/* 20 */     this.words = paramArrayOfint;
/*    */     
/* 22 */     this.sigBytes = paramInt;
/*    */   }
/*    */   
/*    */   public int[] getWords() {
/* 26 */     return this.words;
/*    */   }
/*    */   
/*    */   public void setWords(int[] paramArrayOfint) {
/* 30 */     this.words = paramArrayOfint;
/*    */   }
/*    */   
/*    */   public int getSigBytes() {
/* 34 */     return this.sigBytes;
/*    */   }
/*    */   
/*    */   public void setSigBytes(int paramInt) {
/* 38 */     this.sigBytes = paramInt;
/*    */   }
/*    */ 
/*    */   
/*    */   public String toString() {
/* 43 */     return "WordArrayVO{words=" + 
/* 44 */       Arrays.toString(this.words) + ", sigBytes=" + this.sigBytes + '}';
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/sm/sm2/vo/WordArrayVO.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */