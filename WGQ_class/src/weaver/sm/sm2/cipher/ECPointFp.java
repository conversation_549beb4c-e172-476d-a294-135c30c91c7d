/*     */ package weaver.sm.sm2.cipher;
/*     */ 
/*     */ import weaver.sm.sm2.entity.BigIntegerJS;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class ECPointFp
/*     */ {
/*     */   private ECCurveFp curve;
/*     */   private ECFieldElementFp x;
/*     */   private ECFieldElementFp y;
/*     */   private BigIntegerJS z;
/*     */   private BigIntegerJS zinv;
/*     */   
/*     */   public ECPointFp() {}
/*     */   
/*     */   public ECPointFp(ECCurveFp paramECCurveFp, ECFieldElementFp paramECFieldElementFp1, ECFieldElementFp paramECFieldElementFp2, BigIntegerJS paramBigIntegerJS) {
/*  24 */     this.curve = paramECCurveFp;
/*  25 */     this.x = paramECFieldElementFp1;
/*  26 */     this.y = paramECFieldElementFp2;
/*     */     
/*  28 */     if (paramBigIntegerJS == null) {
/*  29 */       this.z = BigIntegerJS.getBigIntegerJS_ONE();
/*     */     } else {
/*  31 */       this.z = paramBigIntegerJS;
/*     */     } 
/*  33 */     this.zinv = null;
/*     */   }
/*     */   
/*     */   public ECPointFp multiply(BigIntegerJS paramBigIntegerJS) {
/*  37 */     if (isInfinity()) {
/*  38 */       return this;
/*     */     }
/*  40 */     if (paramBigIntegerJS.signum() == 0) {
/*  41 */       return this.curve.getInfinity();
/*     */     }
/*     */     
/*  44 */     BigIntegerJS bigIntegerJS1 = paramBigIntegerJS;
/*  45 */     BigIntegerJS bigIntegerJS2 = bigIntegerJS1.multiply(new BigIntegerJS("3", null, "undefined"));
/*  46 */     ECPointFp eCPointFp1 = pointFpNegate();
/*  47 */     ECPointFp eCPointFp2 = this;
/*     */     
/*  49 */     for (int i = bigIntegerJS2.bitLength() - 2; i > 0; i--) {
/*  50 */       eCPointFp2 = eCPointFp2.twice();
/*  51 */       boolean bool1 = bigIntegerJS2.testBit(i);
/*  52 */       boolean bool2 = bigIntegerJS1.testBit(i);
/*     */       
/*  54 */       if (bool1 != bool2) {
/*  55 */         eCPointFp2 = eCPointFp2.pointFpAdd(bool1 ? this : eCPointFp1);
/*     */       }
/*     */     } 
/*  58 */     return eCPointFp2;
/*     */   }
/*     */ 
/*     */   
/*     */   public ECPointFp twice() {
/*  63 */     if (isInfinity()) {
/*  64 */       return this;
/*     */     }
/*  66 */     if (this.y.toBigInteger().signum() == 0) {
/*  67 */       return this.curve.getInfinity();
/*     */     }
/*  69 */     BigIntegerJS bigIntegerJS1 = new BigIntegerJS("3", null, "undefined");
/*  70 */     BigIntegerJS bigIntegerJS2 = this.x.toBigInteger();
/*  71 */     BigIntegerJS bigIntegerJS3 = this.y.toBigInteger();
/*     */     
/*  73 */     BigIntegerJS bigIntegerJS4 = bigIntegerJS3.multiply(this.z);
/*  74 */     BigIntegerJS bigIntegerJS5 = bigIntegerJS4.multiply(bigIntegerJS3).mod(this.curve.getQ());
/*  75 */     BigIntegerJS bigIntegerJS6 = this.curve.getA().toBigInteger();
/*     */     
/*  77 */     BigIntegerJS bigIntegerJS7 = bigIntegerJS2.square().multiply(bigIntegerJS1);
/*  78 */     if (!BigIntegerJS.getBigIntegerJS_ZERO().equals(bigIntegerJS6)) {
/*  79 */       bigIntegerJS7 = bigIntegerJS7.add(this.z.square().multiply(bigIntegerJS6));
/*     */     }
/*  81 */     bigIntegerJS7 = bigIntegerJS7.mod(this.curve.getQ());
/*  82 */     BigIntegerJS bigIntegerJS8 = bigIntegerJS7.square().subtract(bigIntegerJS2.shiftLeft(3).multiply(bigIntegerJS5)).shiftLeft(1).multiply(bigIntegerJS4).mod(this.curve.getQ());
/*     */ 
/*     */ 
/*     */     
/*  86 */     BigIntegerJS bigIntegerJS9 = bigIntegerJS7.multiply(bigIntegerJS1).multiply(bigIntegerJS2).subtract(bigIntegerJS5.shiftLeft(1)).shiftLeft(2).multiply(bigIntegerJS5).subtract(bigIntegerJS7.square().multiply(bigIntegerJS7)).mod(this.curve.getQ());
/*  87 */     BigIntegerJS bigIntegerJS10 = bigIntegerJS4.square().multiply(bigIntegerJS4).shiftLeft(3).mod(this.curve.getQ());
/*     */     
/*  89 */     return new ECPointFp(this.curve, this.curve.fromBigInteger(bigIntegerJS8), this.curve.fromBigInteger(bigIntegerJS9), bigIntegerJS10);
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public ECPointFp pointFpNegate() {
/*  95 */     return new ECPointFp(this.curve, this.x, this.y.feFpNegate(), this.z);
/*     */   }
/*     */ 
/*     */   
/*     */   public boolean isInfinity() {
/* 100 */     if (this.x == null && this.y == null) {
/* 101 */       return true;
/*     */     }
/* 103 */     return (this.z.equals(BigIntegerJS.getBigIntegerJS_ZERO()) && this.y
/* 104 */       .getX().equals(BigIntegerJS.getBigIntegerJS_ZERO()));
/*     */   }
/*     */   
/*     */   public ECPointFp pointFpAdd(ECPointFp paramECPointFp) {
/* 108 */     if (isInfinity()) {
/* 109 */       return paramECPointFp;
/*     */     }
/* 111 */     if (paramECPointFp.isInfinity()) {
/* 112 */       return this;
/*     */     }
/* 114 */     BigIntegerJS bigIntegerJS1 = paramECPointFp.y.toBigInteger().multiply(this.z).subtract(this.y.toBigInteger().multiply(paramECPointFp.z)).mod(this.curve.getQ());
/* 115 */     BigIntegerJS bigIntegerJS2 = paramECPointFp.x.toBigInteger().multiply(this.z).subtract(this.x.toBigInteger().multiply(paramECPointFp.z)).mod(this.curve.getQ());
/*     */     
/* 117 */     if (BigIntegerJS.getBigIntegerJS_ZERO().equals(bigIntegerJS2)) {
/* 118 */       if (BigIntegerJS.getBigIntegerJS_ZERO().equals(bigIntegerJS1)) {
/* 119 */         return twice();
/*     */       }
/* 121 */       return this.curve.getInfinity();
/*     */     } 
/* 123 */     BigIntegerJS bigIntegerJS3 = new BigIntegerJS("3", null, "undefined");
/* 124 */     BigIntegerJS bigIntegerJS4 = this.x.toBigInteger();
/* 125 */     BigIntegerJS bigIntegerJS5 = this.y.toBigInteger();
/* 126 */     BigIntegerJS bigIntegerJS6 = paramECPointFp.x.toBigInteger();
/* 127 */     BigIntegerJS bigIntegerJS7 = paramECPointFp.y.toBigInteger();
/*     */     
/* 129 */     BigIntegerJS bigIntegerJS8 = bigIntegerJS2.square();
/* 130 */     BigIntegerJS bigIntegerJS9 = bigIntegerJS8.multiply(bigIntegerJS2);
/* 131 */     BigIntegerJS bigIntegerJS10 = bigIntegerJS4.multiply(bigIntegerJS8);
/* 132 */     BigIntegerJS bigIntegerJS11 = bigIntegerJS1.square().multiply(this.z);
/*     */     
/* 134 */     BigIntegerJS bigIntegerJS12 = bigIntegerJS11.subtract(bigIntegerJS10.shiftLeft(1)).multiply(paramECPointFp.z).subtract(bigIntegerJS9).multiply(bigIntegerJS2).mod(this.curve.getQ());
/*     */     
/* 136 */     BigIntegerJS bigIntegerJS13 = bigIntegerJS10.multiply(bigIntegerJS3).multiply(bigIntegerJS1).subtract(bigIntegerJS5.multiply(bigIntegerJS9)).subtract(bigIntegerJS11.multiply(bigIntegerJS1)).multiply(paramECPointFp.z).add(bigIntegerJS1.multiply(bigIntegerJS9)).mod(this.curve.getQ());
/*     */     
/* 138 */     BigIntegerJS bigIntegerJS14 = bigIntegerJS9.multiply(this.z).multiply(paramECPointFp.z).mod(this.curve.getQ());
/*     */     
/* 140 */     return new ECPointFp(this.curve, this.curve.fromBigInteger(bigIntegerJS12), this.curve.fromBigInteger(bigIntegerJS13), bigIntegerJS14);
/*     */   }
/*     */ 
/*     */   
/*     */   public ECCurveFp getCurve() {
/* 145 */     return this.curve;
/*     */   }
/*     */   
/*     */   public ECFieldElementFp getX() {
/* 149 */     if (this.zinv == null) {
/* 150 */       this.zinv = this.z.modInverse(this.curve.getQ());
/*     */     }
/* 152 */     return this.curve.fromBigInteger(this.x.toBigInteger().multiply(this.zinv).mod(this.curve.getQ()));
/*     */   }
/*     */   
/*     */   public ECFieldElementFp getY() {
/* 156 */     if (this.zinv == null) {
/* 157 */       this.zinv = this.z.modInverse(this.curve.getQ());
/*     */     }
/* 159 */     return this.curve.fromBigInteger(this.y.toBigInteger().multiply(this.zinv).mod(this.curve.getQ()));
/*     */   }
/*     */   
/*     */   public BigIntegerJS getZ() {
/* 163 */     return this.z;
/*     */   }
/*     */   
/*     */   public BigIntegerJS getZinv() {
/* 167 */     return this.zinv;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/sm/sm2/cipher/ECPointFp.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */