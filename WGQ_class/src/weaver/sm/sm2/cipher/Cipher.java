/*     */ package weaver.sm.sm2.cipher;
/*     */ 
/*     */ import java.math.BigInteger;
/*     */ import org.bouncycastle.crypto.AsymmetricCipherKeyPair;
/*     */ import org.bouncycastle.crypto.digests.SM3Digest;
/*     */ import org.bouncycastle.crypto.params.ECPrivateKeyParameters;
/*     */ import org.bouncycastle.crypto.params.ECPublicKeyParameters;
/*     */ import org.bouncycastle.math.ec.ECPoint;
/*     */ import weaver.sm.sm2.entity.BigIntegerJS;
/*     */ import weaver.sm.sm2.entity.Sm2;
/*     */ import weaver.sm.sm2.util.Sm2Util;
/*     */ import weaver.sm.sm2.vo.WordArrayVO;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class Cipher
/*     */ {
/*  34 */   private int ct = 1; private ECPoint p2; private ECPointFp p2_js;
/*  35 */   private byte[] key = new byte[32]; private SM3Digest sm3keybase; private SM3Digest sm3c3;
/*  36 */   private byte keyOff = 0;
/*     */ 
/*     */   
/*     */   private void reset() {
/*  40 */     this.sm3keybase = new SM3Digest();
/*  41 */     this.sm3c3 = new SM3Digest();
/*     */     
/*  43 */     byte[] arrayOfByte = Sm2Util.byteConvert32Bytes(this.p2.getX().toBigInteger());
/*  44 */     this.sm3keybase.update(arrayOfByte, 0, arrayOfByte.length);
/*  45 */     this.sm3c3.update(arrayOfByte, 0, arrayOfByte.length);
/*     */     
/*  47 */     arrayOfByte = Sm2Util.byteConvert32Bytes(this.p2.getY().toBigInteger());
/*  48 */     this.sm3keybase.update(arrayOfByte, 0, arrayOfByte.length);
/*  49 */     this.ct = 1;
/*  50 */     nextKey();
/*     */   }
/*     */   
/*     */   private void nextKey() {
/*  54 */     SM3Digest sM3Digest = new SM3Digest(this.sm3keybase);
/*  55 */     sM3Digest.update((byte)(this.ct >> 24 & 0xFF));
/*  56 */     sM3Digest.update((byte)(this.ct >> 16 & 0xFF));
/*  57 */     sM3Digest.update((byte)(this.ct >> 8 & 0xFF));
/*  58 */     sM3Digest.update((byte)(this.ct & 0xFF));
/*  59 */     sM3Digest.doFinal(this.key, 0);
/*  60 */     this.keyOff = 0;
/*  61 */     this.ct++;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void reset_js() {
/*  68 */     this.sm3keybase = new SM3Digest();
/*  69 */     this.sm3c3 = new SM3Digest();
/*  70 */     byte[] arrayOfByte1 = getWords(this.p2_js.getX().toBigInteger().toRadix(Integer.valueOf(16)));
/*  71 */     byte[] arrayOfByte2 = getWords(this.p2_js.getY().toBigInteger().toRadix(Integer.valueOf(16)));
/*     */     
/*  73 */     this.sm3keybase.update(arrayOfByte1, 0, arrayOfByte1.length);
/*  74 */     this.sm3c3.update(arrayOfByte1, 0, arrayOfByte1.length);
/*  75 */     this.sm3keybase.update(arrayOfByte2, 0, arrayOfByte2.length);
/*  76 */     this.ct = 1;
/*  77 */     nextKey_js();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public byte[] getWords(String paramString) {
/*  88 */     return Sm2Util.hexStringToBytes(paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void nextKey_js() {
/*  96 */     SM3Digest sM3Digest = new SM3Digest(this.sm3keybase);
/*     */     
/*  98 */     sM3Digest.update((byte)(this.ct >> 24 & 0xFF));
/*  99 */     sM3Digest.update((byte)(this.ct >> 16 & 0xFF));
/* 100 */     sM3Digest.update((byte)(this.ct >> 8 & 0xFF));
/* 101 */     sM3Digest.update((byte)(this.ct & 0xFF));
/* 102 */     sM3Digest.doFinal(this.key, 0);
/* 103 */     this.keyOff = 0;
/* 104 */     this.ct++;
/*     */   }
/*     */ 
/*     */   
/*     */   public ECPoint init_enc(Sm2 paramSm2, ECPoint paramECPoint) {
/* 109 */     AsymmetricCipherKeyPair asymmetricCipherKeyPair = paramSm2.ecc_key_pair_generator.generateKeyPair();
/* 110 */     ECPrivateKeyParameters eCPrivateKeyParameters = (ECPrivateKeyParameters)asymmetricCipherKeyPair.getPrivate();
/* 111 */     ECPublicKeyParameters eCPublicKeyParameters = (ECPublicKeyParameters)asymmetricCipherKeyPair.getPublic();
/* 112 */     BigInteger bigInteger = eCPrivateKeyParameters.getD();
/* 113 */     ECPoint eCPoint = eCPublicKeyParameters.getQ();
/* 114 */     this.p2 = paramECPoint.multiply(bigInteger);
/* 115 */     reset();
/* 116 */     return eCPoint;
/*     */   }
/*     */ 
/*     */   
/*     */   public void encrypt(byte[] paramArrayOfbyte) {
/* 121 */     this.sm3c3.update(paramArrayOfbyte, 0, paramArrayOfbyte.length);
/*     */     
/* 123 */     for (byte b = 0; b < paramArrayOfbyte.length; b++) {
/* 124 */       if (this.keyOff == this.key.length) {
/* 125 */         nextKey();
/*     */       }
/* 127 */       this.keyOff = (byte)(this.keyOff + 1); paramArrayOfbyte[b] = (byte)(paramArrayOfbyte[b] ^ this.key[this.keyOff]);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void init_dec(BigInteger paramBigInteger, ECPoint paramECPoint) {
/* 138 */     this.p2 = paramECPoint.multiply(paramBigInteger);
/* 139 */     reset();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void init_dec_js(BigIntegerJS paramBigIntegerJS, ECPointFp paramECPointFp) {
/* 149 */     this.p2_js = paramECPointFp.multiply(paramBigIntegerJS);
/* 150 */     reset_js();
/*     */   }
/*     */   
/*     */   public void decrypt(byte[] paramArrayOfbyte) {
/* 154 */     for (byte b = 0; b < paramArrayOfbyte.length; b++) {
/* 155 */       if (this.keyOff == this.key.length) {
/* 156 */         nextKey();
/*     */       }
/* 158 */       this.keyOff = (byte)(this.keyOff + 1); paramArrayOfbyte[b] = (byte)(paramArrayOfbyte[b] ^ this.key[this.keyOff]);
/*     */     } 
/*     */     
/* 161 */     this.sm3c3.update(paramArrayOfbyte, 0, paramArrayOfbyte.length);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void decrypt_js(byte[] paramArrayOfbyte) {
/* 169 */     for (byte b = 0; b < paramArrayOfbyte.length; b++) {
/* 170 */       if (this.keyOff == this.key.length) {
/* 171 */         nextKey_js();
/*     */       }
/* 173 */       this.keyOff = (byte)(this.keyOff + 1); paramArrayOfbyte[b] = (byte)(paramArrayOfbyte[b] ^ this.key[this.keyOff]);
/*     */     } 
/*     */     
/* 176 */     this.sm3c3.update(paramArrayOfbyte, 0, paramArrayOfbyte.length);
/*     */   }
/*     */   
/*     */   public void dofinal(byte[] paramArrayOfbyte) {
/* 180 */     byte[] arrayOfByte = Sm2Util.byteConvert32Bytes(this.p2.getY().toBigInteger());
/* 181 */     this.sm3c3.update(arrayOfByte, 0, arrayOfByte.length);
/* 182 */     this.sm3c3.doFinal(paramArrayOfbyte, 0);
/* 183 */     reset();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void dofinal_js(byte[] paramArrayOfbyte) {
/* 192 */     byte[] arrayOfByte = getWords(this.p2_js.getY().toBigInteger().toRadix(Integer.valueOf(16)));
/*     */ 
/*     */ 
/*     */     
/* 196 */     this.sm3c3.update(arrayOfByte, 0, arrayOfByte.length);
/*     */     
/* 198 */     this.sm3c3.doFinal(paramArrayOfbyte, 0);
/* 199 */     reset_js();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public WordArrayVO getHex_js(int[] paramArrayOfint) {
/* 209 */     int[] arrayOfInt = new int[8];
/* 210 */     byte b1 = 0;
/* 211 */     for (byte b2 = 0; b2 < paramArrayOfint.length * 2; b2 += 2) {
/* 212 */       arrayOfInt[b2 >>> 3] = arrayOfInt[b2 >>> 3] | Integer.parseInt(paramArrayOfint[b1] + "") << 24 - b2 % 8 * 4;
/* 213 */       b1++;
/*     */     } 
/* 215 */     return new WordArrayVO(arrayOfInt, paramArrayOfint.length);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/sm/sm2/cipher/Cipher.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */