/*    */ package weaver.sm.sm2.cipher;
/*    */ 
/*    */ import java.util.Map;
/*    */ import java.util.concurrent.ConcurrentHashMap;
/*    */ import weaver.sm.sm2.entity.BigIntegerJS;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class ECCCurveJS
/*    */ {
/* 16 */   static String[] ecc_param = new String[] { "FFFFFFFEFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF00000000FFFFFFFFFFFFFFFF", "FFFFFFFEFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF00000000FFFFFFFFFFFFFFFC", "28E9FA9E9D9F5E344D5A9E4BCF6509A7F39789F515AB8F92DDBCBD414D940E93", "FFFFFFFEFFFFFFFFFFFFFFFFFFFFFFFF7203DF6B21C6052B53BBF40939D54123", "1", "32C4AE2C1F1981195F9904466A39C9948FE30BBFF2660BE1715A4589334C74C7", "BC3736A2F4F6779C59BDCEE36B692153D0A9877CC62A474002DF32E52139F0A0" };
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/* 26 */   private static Map<String, Object> db = new ConcurrentHashMap<>(16);
/*    */   
/*    */   public static void instance() {
/* 29 */     regist();
/*    */   }
/*    */   
/*    */   private static void regist() {
/* 33 */     String str = "sm2";
/* 34 */     char c = 'Ā';
/*    */     
/* 36 */     BigIntegerJS bigIntegerJS1 = new BigIntegerJS(ecc_param[0], Integer.valueOf(16), "undefined");
/* 37 */     BigIntegerJS bigIntegerJS2 = new BigIntegerJS(ecc_param[1], Integer.valueOf(16), "undefined");
/* 38 */     BigIntegerJS bigIntegerJS3 = new BigIntegerJS(ecc_param[2], Integer.valueOf(16), "undefined");
/* 39 */     BigIntegerJS bigIntegerJS4 = new BigIntegerJS(ecc_param[3], Integer.valueOf(16), "undefined");
/* 40 */     BigIntegerJS bigIntegerJS5 = new BigIntegerJS(ecc_param[4], Integer.valueOf(16), "undefined");
/*    */ 
/*    */     
/* 43 */     dataFilterR(bigIntegerJS1);
/* 44 */     dataFilterR(bigIntegerJS2);
/* 45 */     dataFilterR(bigIntegerJS3);
/* 46 */     dataFilterR(bigIntegerJS4);
/* 47 */     dataFilterR(bigIntegerJS5);
/*    */     
/* 49 */     ECCurveFp eCCurveFp = new ECCurveFp(bigIntegerJS1, bigIntegerJS2, bigIntegerJS3);
/* 50 */     ECPointFp eCPointFp = eCCurveFp.decodePointHex("04" + ecc_param[5] + ecc_param[6]);
/*    */     
/* 52 */     db.put("name", str);
/* 53 */     db.put("keylen", Integer.valueOf(c));
/* 54 */     db.put("curve", eCCurveFp);
/* 55 */     db.put("G", eCPointFp);
/* 56 */     db.put("n", bigIntegerJS4);
/* 57 */     db.put("h", bigIntegerJS5);
/*    */   }
/*    */   
/*    */   public static Map getDb() {
/* 61 */     return db;
/*    */   }
/*    */ 
/*    */   
/*    */   public static void dataFilter(BigIntegerJS paramBigIntegerJS) {
/* 66 */     int[] arrayOfInt = paramBigIntegerJS.getData();
/* 67 */     for (byte b = 0; b < arrayOfInt.length; b++) {
/* 68 */       if (arrayOfInt[b] == 0) {
/* 69 */         int[] arrayOfInt1 = new int[b];
/* 70 */         System.arraycopy(arrayOfInt, 0, arrayOfInt1, 0, b);
/* 71 */         paramBigIntegerJS.setData(arrayOfInt1);
/*    */         break;
/*    */       } 
/*    */     } 
/*    */   }
/*    */   
/*    */   public static void dataFilterR(BigIntegerJS paramBigIntegerJS) {
/* 78 */     int[] arrayOfInt = new int[paramBigIntegerJS.getT()];
/* 79 */     System.arraycopy(paramBigIntegerJS.getData(), 0, arrayOfInt, 0, paramBigIntegerJS.getT());
/* 80 */     paramBigIntegerJS.setData(arrayOfInt);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/sm/sm2/cipher/ECCCurveJS.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */