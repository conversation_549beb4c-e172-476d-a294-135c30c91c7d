/*    */ package weaver.sm.sm2.cipher;
/*    */ 
/*    */ import weaver.sm.sm2.entity.BigIntegerJS;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class ECCurveFp
/*    */ {
/*    */   private BigIntegerJS q;
/*    */   private ECFieldElementFp a;
/*    */   private ECFieldElementFp b;
/*    */   private ECPointFp infinity;
/*    */   
/*    */   public ECCurveFp() {}
/*    */   
/*    */   public ECCurveFp(BigIntegerJS paramBigIntegerJS1, BigIntegerJS paramBigIntegerJS2, BigIntegerJS paramBigIntegerJS3) {
/* 23 */     this.q = paramBigIntegerJS1;
/* 24 */     this.a = fromBigInteger(paramBigIntegerJS2);
/* 25 */     this.b = fromBigInteger(paramBigIntegerJS3);
/* 26 */     this.infinity = new ECPointFp(this, null, null, null);
/*    */   }
/*    */   
/*    */   public ECFieldElementFp fromBigInteger(BigIntegerJS paramBigIntegerJS) {
/* 30 */     return new ECFieldElementFp(this.q, paramBigIntegerJS);
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public ECPointFp decodePointHex(String paramString) {
/*    */     int i;
/*    */     String str1;
/*    */     String str2;
/* 40 */     switch (Integer.toHexString(Integer.parseInt(paramString.substring(0, 2)))) {
/*    */       case "0":
/* 42 */         return this.infinity;
/*    */       case "2":
/*    */       case "3":
/* 45 */         return null;
/*    */       case "4":
/*    */       case "6":
/*    */       case "7":
/* 49 */         i = (paramString.length() - 2) / 2;
/* 50 */         str1 = paramString.substring(2, 2 + i);
/* 51 */         str2 = paramString.substring(i + 2, i + 2 + i);
/*    */         
/* 53 */         return new ECPointFp(this, fromBigInteger(new BigIntegerJS(str1, Integer.valueOf(16), "undefined")), 
/* 54 */             fromBigInteger(new BigIntegerJS(str2, Integer.valueOf(16), "undefined")), null);
/*    */     } 
/* 56 */     return null;
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public BigIntegerJS getQ() {
/* 62 */     return this.q;
/*    */   }
/*    */   
/*    */   public ECFieldElementFp getA() {
/* 66 */     return this.a;
/*    */   }
/*    */   
/*    */   public ECFieldElementFp getB() {
/* 70 */     return this.b;
/*    */   }
/*    */   
/*    */   public ECPointFp getInfinity() {
/* 74 */     return this.infinity;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/sm/sm2/cipher/ECCurveFp.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */