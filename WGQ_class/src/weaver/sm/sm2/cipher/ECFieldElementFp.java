/*    */ package weaver.sm.sm2.cipher;
/*    */ 
/*    */ import weaver.sm.sm2.entity.BigIntegerJS;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class ECFieldElementFp
/*    */ {
/*    */   private BigIntegerJS x;
/*    */   private BigIntegerJS q;
/*    */   
/*    */   public ECFieldElementFp() {}
/*    */   
/*    */   public ECFieldElementFp(BigIntegerJS paramBigIntegerJS1, BigIntegerJS paramBigIntegerJS2) {
/* 22 */     if ((paramBigIntegerJS2.getData()).length == 64 && paramBigIntegerJS2.getT() != 0 && paramBigIntegerJS2.getT() != 64) {
/* 23 */       ECCCurveJS.dataFilterR(paramBigIntegerJS2);
/*    */     }
/* 25 */     this.x = paramBigIntegerJS2;
/* 26 */     this.q = paramBigIntegerJS1;
/*    */   }
/*    */   
/*    */   public BigIntegerJS getX() {
/* 30 */     return this.x;
/*    */   }
/*    */   
/*    */   public BigIntegerJS getQ() {
/* 34 */     return this.q;
/*    */   }
/*    */   
/*    */   public ECFieldElementFp feFpNegate() {
/* 38 */     return new ECFieldElementFp(this.q, this.x.negate().mod(this.q));
/*    */   }
/*    */   
/*    */   public BigIntegerJS toBigInteger() {
/* 42 */     return this.x;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/sm/sm2/cipher/ECFieldElementFp.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */