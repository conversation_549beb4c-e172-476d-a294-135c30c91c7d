/*     */ package weaver.sm.sm2.util;
/*     */ 
/*     */ import java.math.BigInteger;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public final class Sm2Util
/*     */ {
/*     */   public static byte[] intToBytes(int paramInt) {
/*  20 */     byte[] arrayOfByte = new byte[4];
/*  21 */     for (byte b = 0; b < 4; b++) {
/*  22 */       arrayOfByte[b] = (byte)(0xFF & paramInt >> b * 8);
/*     */     }
/*  24 */     return arrayOfByte;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static int byteToInt(byte[] paramArrayOfbyte) {
/*  34 */     int i = 0;
/*     */     
/*  36 */     for (byte b = 0; b < 4; b++) {
/*  37 */       int j = (0xFF & paramArrayOfbyte[b]) << b * 8;
/*  38 */       i |= j;
/*     */     } 
/*  40 */     return i;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static byte[] longToBytes(long paramLong) {
/*  50 */     byte[] arrayOfByte = new byte[8];
/*  51 */     for (byte b = 0; b < 8; b++) {
/*  52 */       arrayOfByte[b] = (byte)(int)(0xFFL & paramLong >> b * 8);
/*     */     }
/*  54 */     return arrayOfByte;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static byte[] byteConvert32Bytes(BigInteger paramBigInteger) {
/*  64 */     byte[] arrayOfByte = (byte[])null;
/*  65 */     if (paramBigInteger == null) {
/*  66 */       return null;
/*     */     }
/*     */     
/*  69 */     if ((paramBigInteger.toByteArray()).length == 33) {
/*  70 */       arrayOfByte = new byte[32];
/*  71 */       System.arraycopy(paramBigInteger.toByteArray(), 1, arrayOfByte, 0, 32);
/*  72 */     } else if ((paramBigInteger.toByteArray()).length == 32) {
/*  73 */       arrayOfByte = paramBigInteger.toByteArray();
/*     */     } else {
/*     */       
/*  76 */       arrayOfByte = new byte[32];
/*  77 */       for (byte b = 0; b < 32 - (paramBigInteger.toByteArray()).length; b++) {
/*  78 */         arrayOfByte[b] = 0;
/*     */       }
/*  80 */       System.arraycopy(paramBigInteger.toByteArray(), 0, arrayOfByte, 32 - (paramBigInteger.toByteArray()).length, (paramBigInteger.toByteArray()).length);
/*     */     } 
/*  82 */     return arrayOfByte;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static BigInteger byteConvertInteger(byte[] paramArrayOfbyte) {
/*  92 */     if (paramArrayOfbyte[0] < 0) {
/*  93 */       byte[] arrayOfByte = new byte[paramArrayOfbyte.length + 1];
/*     */       
/*  95 */       arrayOfByte[0] = 0;
/*  96 */       System.arraycopy(paramArrayOfbyte, 0, arrayOfByte, 1, paramArrayOfbyte.length);
/*  97 */       return new BigInteger(arrayOfByte);
/*     */     } 
/*  99 */     return new BigInteger(paramArrayOfbyte);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getHexString(byte[] paramArrayOfbyte) {
/* 109 */     return getHexString(paramArrayOfbyte, true);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getHexString(byte[] paramArrayOfbyte, boolean paramBoolean) {
/* 120 */     String str = "";
/* 121 */     for (byte b = 0; b < paramArrayOfbyte.length; b++) {
/* 122 */       str = str + Integer.toString((paramArrayOfbyte[b] & 0xFF) + 256, 16).substring(1);
/*     */     }
/* 124 */     return paramBoolean ? str.toUpperCase() : str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static void printHexString(byte[] paramArrayOfbyte) {
/* 133 */     for (byte b = 0; b < paramArrayOfbyte.length; b++) {
/* 134 */       String str = Integer.toHexString(paramArrayOfbyte[b] & 0xFF);
/* 135 */       if (str.length() == 1) {
/* 136 */         str = '0' + str;
/*     */       }
/* 138 */       System.out.print("0x" + str.toUpperCase() + ",");
/*     */     } 
/* 140 */     System.out.println("");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static byte[] hexStringToBytes(String paramString) {
/* 150 */     if (paramString == null || paramString.equals("")) {
/* 151 */       return null;
/*     */     }
/*     */     
/* 154 */     paramString = paramString.toUpperCase();
/* 155 */     int i = (int)Math.floor(paramString.length() / 2.0D);
/* 156 */     char[] arrayOfChar = paramString.toCharArray();
/* 157 */     byte[] arrayOfByte = new byte[i];
/* 158 */     for (byte b = 0; b < i; b++) {
/* 159 */       int j = b * 2;
/* 160 */       arrayOfByte[b] = (byte)(charToByte(arrayOfChar[j]) << 4 | charToByte(arrayOfChar[j + 1]));
/*     */     } 
/* 162 */     return arrayOfByte;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static byte charToByte(char paramChar) {
/* 172 */     return (byte)"0123456789ABCDEF".indexOf(paramChar);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/* 178 */   private static final char[] DIGITS_LOWER = new char[] { '0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f' };
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/* 184 */   private static final char[] DIGITS_UPPER = new char[] { '0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F' };
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static char[] encodeHex(byte[] paramArrayOfbyte) {
/* 194 */     return encodeHex(paramArrayOfbyte, true);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static char[] encodeHex(byte[] paramArrayOfbyte, boolean paramBoolean) {
/* 205 */     return encodeHex(paramArrayOfbyte, paramBoolean ? DIGITS_LOWER : DIGITS_UPPER);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   protected static char[] encodeHex(byte[] paramArrayOfbyte, char[] paramArrayOfchar) {
/* 216 */     int i = paramArrayOfbyte.length;
/* 217 */     char[] arrayOfChar = new char[i << 1];
/*     */     
/* 219 */     for (byte b1 = 0, b2 = 0; b1 < i; b1++) {
/* 220 */       arrayOfChar[b2++] = paramArrayOfchar[(0xF0 & paramArrayOfbyte[b1]) >>> 4];
/* 221 */       arrayOfChar[b2++] = paramArrayOfchar[0xF & paramArrayOfbyte[b1]];
/*     */     } 
/* 223 */     return arrayOfChar;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String encodeHexString(byte[] paramArrayOfbyte) {
/* 233 */     return encodeHexString(paramArrayOfbyte, true);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String encodeHexString(byte[] paramArrayOfbyte, boolean paramBoolean) {
/* 244 */     return encodeHexString(paramArrayOfbyte, paramBoolean ? DIGITS_LOWER : DIGITS_UPPER);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   protected static String encodeHexString(byte[] paramArrayOfbyte, char[] paramArrayOfchar) {
/* 255 */     return new String(encodeHex(paramArrayOfbyte, paramArrayOfchar));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static byte[] decodeHex(char[] paramArrayOfchar) {
/* 266 */     int i = paramArrayOfchar.length;
/*     */     
/* 268 */     if ((i & 0x1) != 0) {
/* 269 */       throw new RuntimeException("Odd number of characters.");
/*     */     }
/*     */     
/* 272 */     byte[] arrayOfByte = new byte[i >> 1];
/*     */ 
/*     */     
/* 275 */     for (byte b1 = 0, b2 = 0; b2 < i; b1++) {
/* 276 */       int j = toDigit(paramArrayOfchar[b2], b2) << 4;
/* 277 */       b2++;
/* 278 */       j |= toDigit(paramArrayOfchar[b2], b2);
/* 279 */       b2++;
/* 280 */       arrayOfByte[b1] = (byte)(j & 0xFF);
/*     */     } 
/*     */     
/* 283 */     return arrayOfByte;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   protected static int toDigit(char paramChar, int paramInt) {
/* 295 */     int i = Character.digit(paramChar, 16);
/* 296 */     if (i == -1) {
/* 297 */       throw new RuntimeException("Illegal hexadecimal character " + paramChar + " at index " + paramInt);
/*     */     }
/*     */     
/* 300 */     return i;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String StringToAsciiString(String paramString) {
/* 310 */     String str = "";
/* 311 */     int i = paramString.length();
/* 312 */     for (byte b = 0; b < i; b++) {
/* 313 */       char c = paramString.charAt(b);
/* 314 */       String str1 = Integer.toHexString(c);
/* 315 */       str = str + str1;
/*     */     } 
/* 317 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String hexStringToString(String paramString, int paramInt) {
/* 328 */     String str = "";
/* 329 */     int i = paramString.length() / paramInt;
/* 330 */     for (byte b = 0; b < i; b++) {
/* 331 */       char c = (char)hexStringToAlgorism(paramString
/* 332 */           .substring(b * paramInt, (b + 1) * paramInt));
/* 333 */       str = str + c;
/*     */     } 
/* 335 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static int hexStringToAlgorism(String paramString) {
/* 345 */     paramString = paramString.toUpperCase();
/* 346 */     int i = paramString.length();
/* 347 */     int j = 0;
/* 348 */     for (int k = i; k > 0; k--) {
/* 349 */       char c = paramString.charAt(k - 1);
/* 350 */       int m = 0;
/* 351 */       if (c >= '0' && c <= '9') {
/* 352 */         m = c - 48;
/*     */       } else {
/* 354 */         m = c - 55;
/*     */       } 
/* 356 */       j = (int)(j + Math.pow(16.0D, (i - k)) * m);
/*     */     } 
/* 358 */     return j;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String hexStringToBinary(String paramString) {
/* 368 */     paramString = paramString.toUpperCase();
/* 369 */     String str = "";
/* 370 */     int i = paramString.length();
/* 371 */     for (byte b = 0; b < i; b++) {
/* 372 */       char c = paramString.charAt(b);
/* 373 */       switch (c) {
/*     */         case '0':
/* 375 */           str = str + "0000";
/*     */           break;
/*     */         case '1':
/* 378 */           str = str + "0001";
/*     */           break;
/*     */         case '2':
/* 381 */           str = str + "0010";
/*     */           break;
/*     */         case '3':
/* 384 */           str = str + "0011";
/*     */           break;
/*     */         case '4':
/* 387 */           str = str + "0100";
/*     */           break;
/*     */         case '5':
/* 390 */           str = str + "0101";
/*     */           break;
/*     */         case '6':
/* 393 */           str = str + "0110";
/*     */           break;
/*     */         case '7':
/* 396 */           str = str + "0111";
/*     */           break;
/*     */         case '8':
/* 399 */           str = str + "1000";
/*     */           break;
/*     */         case '9':
/* 402 */           str = str + "1001";
/*     */           break;
/*     */         case 'A':
/* 405 */           str = str + "1010";
/*     */           break;
/*     */         case 'B':
/* 408 */           str = str + "1011";
/*     */           break;
/*     */         case 'C':
/* 411 */           str = str + "1100";
/*     */           break;
/*     */         case 'D':
/* 414 */           str = str + "1101";
/*     */           break;
/*     */         case 'E':
/* 417 */           str = str + "1110";
/*     */           break;
/*     */         case 'F':
/* 420 */           str = str + "1111";
/*     */           break;
/*     */       } 
/*     */     } 
/* 424 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String asciiStringToString(String paramString) {
/* 434 */     String str = "";
/* 435 */     int i = paramString.length() / 2;
/* 436 */     for (byte b = 0; b < i; b++) {
/* 437 */       String str1 = paramString.substring(b * 2, b * 2 + 2);
/* 438 */       int j = hexStringToAlgorism(str1);
/* 439 */       char c = (char)j;
/* 440 */       String str2 = String.valueOf(c);
/* 441 */       str = str + str2;
/*     */     } 
/* 443 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String algorismToHexString(int paramInt1, int paramInt2) {
/* 454 */     String str = "";
/* 455 */     str = Integer.toHexString(paramInt1);
/*     */     
/* 457 */     if (str.length() % 2 == 1) {
/* 458 */       str = "0" + str;
/*     */     }
/* 460 */     return patchHexString(str.toUpperCase(), paramInt2);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String byteToString(byte[] paramArrayOfbyte) {
/* 470 */     String str = "";
/*     */ 
/*     */     
/* 473 */     int i = paramArrayOfbyte.length;
/* 474 */     for (byte b = 0; b < i; b++) {
/* 475 */       char c = (char)paramArrayOfbyte[b];
/* 476 */       str = str + c;
/*     */     } 
/* 478 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static int binaryToAlgorism(String paramString) {
/* 488 */     int i = paramString.length();
/* 489 */     int j = 0;
/* 490 */     for (int k = i; k > 0; k--) {
/* 491 */       char c = paramString.charAt(k - 1);
/* 492 */       int m = c - 48;
/* 493 */       j = (int)(j + Math.pow(2.0D, (i - k)) * m);
/*     */     } 
/* 495 */     return j;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String algorismToHEXString(int paramInt) {
/* 505 */     String str = "";
/* 506 */     str = Integer.toHexString(paramInt);
/*     */     
/* 508 */     if (str.length() % 2 == 1) {
/* 509 */       str = "0" + str;
/*     */     }
/*     */     
/* 512 */     str = str.toUpperCase();
/*     */     
/* 514 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String patchHexString(String paramString, int paramInt) {
/* 525 */     String str = "";
/* 526 */     for (byte b = 0; b < paramInt - paramString.length(); b++) {
/* 527 */       str = "0" + str;
/*     */     }
/* 529 */     paramString = (str + paramString).substring(0, paramInt);
/* 530 */     return paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static int parseToInt(String paramString, int paramInt1, int paramInt2) {
/* 542 */     int i = 0;
/*     */     try {
/* 544 */       i = Integer.parseInt(paramString, paramInt2);
/* 545 */     } catch (NumberFormatException numberFormatException) {
/* 546 */       i = paramInt1;
/*     */     } 
/* 548 */     return i;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static int parseToInt(String paramString, int paramInt) {
/* 559 */     int i = 0;
/*     */     try {
/* 561 */       i = Integer.parseInt(paramString);
/* 562 */     } catch (NumberFormatException numberFormatException) {
/* 563 */       i = paramInt;
/*     */     } 
/* 565 */     return i;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static byte[] hexToByte(String paramString) throws IllegalArgumentException {
/* 575 */     if (paramString.length() % 2 != 0) {
/* 576 */       throw new IllegalArgumentException();
/*     */     }
/* 578 */     char[] arrayOfChar = paramString.toCharArray();
/* 579 */     byte[] arrayOfByte = new byte[paramString.length() / 2]; byte b1, b2; int i;
/* 580 */     for (b1 = 0, b2 = 0, i = paramString.length(); b1 < i; b1++, b2++) {
/* 581 */       String str = "" + arrayOfChar[b1++] + arrayOfChar[b1];
/* 582 */       int j = Integer.parseInt(str, 16) & 0xFF;
/* 583 */       arrayOfByte[b2] = (new Integer(j)).byteValue();
/*     */     } 
/* 585 */     return arrayOfByte;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String byteToHex(byte[] paramArrayOfbyte) {
/* 595 */     if (paramArrayOfbyte == null) {
/* 596 */       throw new IllegalArgumentException("Argument b ( byte array ) is null! ");
/*     */     }
/*     */     
/* 599 */     String str1 = "";
/* 600 */     String str2 = "";
/* 601 */     for (byte b = 0; b < paramArrayOfbyte.length; b++) {
/* 602 */       str2 = Integer.toHexString(paramArrayOfbyte[b] & 0xFF);
/* 603 */       if (str2.length() == 1) {
/* 604 */         str1 = str1 + "0" + str2;
/*     */       } else {
/* 606 */         str1 = str1 + str2;
/*     */       } 
/*     */     } 
/* 609 */     return str1.toLowerCase();
/*     */   }
/*     */ 
/*     */   
/*     */   public static byte[] subByte(byte[] paramArrayOfbyte, int paramInt1, int paramInt2) {
/* 614 */     byte[] arrayOfByte = new byte[paramInt2];
/* 615 */     for (byte b = 0; b < paramInt2; b++) {
/* 616 */       arrayOfByte[b] = paramArrayOfbyte[b + paramInt1];
/*     */     }
/* 618 */     return arrayOfByte;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static int[] byteArrConvertintArr(byte[] paramArrayOfbyte) {
/* 625 */     int[] arrayOfInt = new int[paramArrayOfbyte.length];
/* 626 */     for (byte b = 0; b < paramArrayOfbyte.length; b++) {
/* 627 */       arrayOfInt[b] = paramArrayOfbyte[b] & 0xFF;
/*     */     }
/* 629 */     return arrayOfInt;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/sm/sm2/util/Sm2Util.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */