/*     */ package weaver.sm.sm2.util;
/*     */ 
/*     */ import java.io.BufferedOutputStream;
/*     */ import java.io.File;
/*     */ import java.io.FileOutputStream;
/*     */ import java.io.IOException;
/*     */ import java.math.BigInteger;
/*     */ import java.net.URL;
/*     */ import java.net.URLDecoder;
/*     */ import java.nio.charset.Charset;
/*     */ import java.security.InvalidAlgorithmParameterException;
/*     */ import java.security.NoSuchAlgorithmException;
/*     */ import java.security.SecureRandom;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import org.bouncycastle.asn1.gm.GMNamedCurves;
/*     */ import org.bouncycastle.asn1.x9.X9ECParameters;
/*     */ import org.bouncycastle.crypto.AsymmetricCipherKeyPair;
/*     */ import org.bouncycastle.crypto.CipherParameters;
/*     */ import org.bouncycastle.crypto.KeyGenerationParameters;
/*     */ import org.bouncycastle.crypto.engines.SM2Engine;
/*     */ import org.bouncycastle.crypto.generators.ECKeyPairGenerator;
/*     */ import org.bouncycastle.crypto.params.ECDomainParameters;
/*     */ import org.bouncycastle.crypto.params.ECKeyGenerationParameters;
/*     */ import org.bouncycastle.crypto.params.ECPrivateKeyParameters;
/*     */ import org.bouncycastle.crypto.params.ECPublicKeyParameters;
/*     */ import org.bouncycastle.math.ec.ECPoint;
/*     */ import org.bouncycastle.util.encoders.Base64;
/*     */ import org.bouncycastle.util.encoders.Hex;
/*     */ import org.slf4j.Logger;
/*     */ import org.slf4j.LoggerFactory;
/*     */ import org.springframework.util.CollectionUtils;
/*     */ import weaver.sm.sm2.vo.Sm2KeyVO;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public final class Sm2EncDecUtils
/*     */ {
/*  44 */   private static final Logger logger = LoggerFactory.getLogger(Sm2EncDecUtils.class);
/*     */ 
/*     */   
/*  47 */   private static ECDomainParameters domainParameters = null;
/*     */   private static final String PUB_KEY_NAME = "ec_sm2_2048_public.key";
/*     */   private static final String PRI_KEY_NAME = "ec_sm2_2048_private.key";
/*     */   private static final int BUFFER_SIZE = 102400;
/*     */   private static final String UTF8_ENCODING = "UTF-8";
/*  52 */   private HashMap<String, String> cacheData = new HashMap<>(1);
/*     */   
/*     */   static {
/*     */     try {
/*  56 */       init();
/*  57 */     } catch (Exception exception) {
/*  58 */       exception.printStackTrace();
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static void init() {
/*  65 */     X9ECParameters x9ECParameters = GMNamedCurves.getByName("sm2p256v1");
/*  66 */     domainParameters = new ECDomainParameters(x9ECParameters.getCurve(), x9ECParameters.getG(), x9ECParameters.getN());
/*     */     
/*  68 */     String str1 = getProjectPath();
/*  69 */     String str2 = str1 + "keys" + File.separator;
/*     */     
/*  71 */     String str3 = str2 + "ec_sm2_2048_public.key";
/*  72 */     String str4 = str2 + "ec_sm2_2048_private.key";
/*     */     
/*  74 */     File file1 = new File(str3);
/*  75 */     File file2 = new File(str4);
/*     */     
/*  77 */     if (!file1.exists() || !file2.exists()) {
/*     */       
/*  79 */       file1.delete();
/*  80 */       file2.delete();
/*     */ 
/*     */       
/*  83 */       if (!file1.exists()) {
/*  84 */         boolean bool = createFile(file1);
/*  85 */         if (!bool) {
/*  86 */           logger.error("公钥文件创建失败，请检查创建权限");
/*     */         }
/*     */       } 
/*     */       
/*  90 */       if (!file2.exists()) {
/*  91 */         boolean bool = createFile(file2);
/*  92 */         if (!bool) {
/*  93 */           logger.error("私钥文件创建失败，请检查创建权限");
/*     */         }
/*     */       } 
/*     */ 
/*     */       
/*     */       try {
/*  99 */         HashMap<Object, Object> hashMap = new HashMap<>(2);
/* 100 */         Map<String, String> map = genPUPRkey(hashMap);
/* 101 */         if (CollectionUtils.isEmpty(map)) {
/* 102 */           logger.error("创建密钥对失败");
/* 103 */           throw new ExceptionInInitializerError();
/*     */         } 
/* 105 */         String str5 = map.get("priKey");
/* 106 */         String str6 = map.get("pubKey");
/* 107 */         generateKeyPair(str6, str5);
/*     */ 
/*     */         
/* 110 */         genarateKeyFiles(str2, Charset.forName("UTF-8"), str5, str6);
/* 111 */       } catch (Throwable throwable) {
/* 112 */         logger.error("Sm2EncDecUtils============写入数据失败", throwable);
/* 113 */         file1.delete();
/* 114 */         file2.delete();
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String generateSalt(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/* 130 */     SecureRandom secureRandom = new SecureRandom();
/*     */     
/* 132 */     byte[] arrayOfByte = new byte[4];
/* 133 */     secureRandom.nextBytes(arrayOfByte);
/* 134 */     StringBuffer stringBuffer = new StringBuffer();
/* 135 */     for (byte b = 0; b < arrayOfByte.length; b++) {
/* 136 */       String str = Integer.toHexString(0xFF & arrayOfByte[b]);
/* 137 */       if (str.length() == 1)
/* 138 */         stringBuffer.append('0'); 
/* 139 */       stringBuffer.append(str);
/*     */     } 
/* 141 */     if (paramHttpServletRequest != null)
/*     */     {
/* 143 */       paramHttpServletRequest.getSession().setAttribute("sm2_code", stringBuffer.toString());
/*     */     }
/* 145 */     return stringBuffer.toString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static void generateKeyPair(String paramString1, String paramString2) {
/* 155 */     Sm2KeyVO sm2KeyVO = new Sm2KeyVO();
/* 156 */     sm2KeyVO.setPrivateKey(paramString2);
/* 157 */     sm2KeyVO.setPublicKey(paramString1);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static void genarateKeyFiles(String paramString1, Charset paramCharset, String paramString2, String paramString3) throws Throwable {
/* 168 */     writeDataToFile(new File(paramString1 + "ec_sm2_2048_public.key"), paramString3);
/* 169 */     writeDataToFile(new File(paramString1 + "ec_sm2_2048_private.key"), paramString2);
/*     */   }
/*     */ 
/*     */   
/*     */   private static void writeDataToFile(File paramFile, String paramString) throws Exception {
/* 174 */     BufferedOutputStream bufferedOutputStream = null;
/*     */     try {
/* 176 */       bufferedOutputStream = new BufferedOutputStream(new FileOutputStream(paramFile));
/* 177 */       byte[] arrayOfByte = paramString.getBytes("UTF-8");
/* 178 */       bufferedOutputStream.write(arrayOfByte, 0, arrayOfByte.length);
/* 179 */       bufferedOutputStream.flush();
/*     */     } finally {
/* 181 */       if (bufferedOutputStream != null) {
/* 182 */         bufferedOutputStream.close();
/*     */       }
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static boolean createFile(File paramFile) {
/* 197 */     if (!paramFile.getParentFile().exists()) {
/* 198 */       logger.info("目标文件所在目录不存在，准备创建");
/* 199 */       if (!paramFile.getParentFile().mkdirs()) {
/* 200 */         logger.error("创建目标文件所在目录失败，请检查权限");
/* 201 */         return false;
/*     */       } 
/*     */     } 
/*     */     
/*     */     try {
/* 206 */       if (paramFile.exists() && !paramFile.isDirectory()) {
/* 207 */         return true;
/*     */       }
/*     */       
/* 210 */       if (paramFile.createNewFile()) {
/* 211 */         logger.info("创建单个文件" + paramFile.getPath() + "成功");
/* 212 */         return true;
/*     */       } 
/* 214 */       logger.error("创建单个文件" + paramFile.getPath() + "失败");
/* 215 */       return false;
/*     */     }
/* 217 */     catch (IOException iOException) {
/* 218 */       logger.error("创建单个文件" + paramFile.getPath() + "失败");
/* 219 */       return false;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getProjectPath() {
/* 231 */     URL uRL = Sm2EncDecUtils.class.getProtectionDomain().getCodeSource().getLocation();
/* 232 */     String str = null;
/*     */     try {
/* 234 */       str = URLDecoder.decode(uRL.getPath(), "utf-8");
/* 235 */     } catch (Exception exception) {
/* 236 */       exception.printStackTrace();
/*     */     } 
/* 238 */     if (str.endsWith(".jar"))
/* 239 */       str = str.substring(0, str.lastIndexOf("/") + 1); 
/* 240 */     System.out.println("before skip real path:::::" + str);
/* 241 */     if (str.startsWith("file:")) {
/* 242 */       str = str.substring(5);
/*     */     }
/* 244 */     System.out.println("skip file:::::" + str);
/* 245 */     int i = str.indexOf("SM2");
/* 246 */     System.out.println("SM2 POS:::" + i);
/* 247 */     if (i != -1) {
/* 248 */       str = str.substring(0, i);
/*     */     }
/* 250 */     if (str.indexOf("classbean") != -1 || str.indexOf("classes") != -1) {
/* 251 */       str = str.replace("/WEB-INF", "");
/* 252 */       str = str.replace("/classes", "");
/* 253 */       str = str.replace("/classbean", "");
/* 254 */       if (!str.endsWith("/")) {
/* 255 */         str = str + "/";
/*     */       }
/* 257 */       str = str + "WEB-INF/lib/";
/*     */     } 
/* 259 */     if (!str.endsWith("/")) {
/* 260 */       str = str + "/";
/*     */     }
/* 262 */     return str;
/*     */   }
/*     */ 
/*     */   
/*     */   public static Map<String, String> genPUPRkey(Map<String, String> paramMap) throws NoSuchAlgorithmException, InvalidAlgorithmParameterException {
/* 267 */     ECKeyPairGenerator eCKeyPairGenerator = new ECKeyPairGenerator();
/* 268 */     eCKeyPairGenerator.init((KeyGenerationParameters)new ECKeyGenerationParameters(domainParameters, SecureRandom.getInstance("SHA1PRNG")));
/* 269 */     AsymmetricCipherKeyPair asymmetricCipherKeyPair = eCKeyPairGenerator.generateKeyPair();
/*     */     
/* 271 */     BigInteger bigInteger = ((ECPrivateKeyParameters)asymmetricCipherKeyPair.getPrivate()).getD();
/*     */     
/* 273 */     String str1 = bigInteger.toString(16);
/*     */     
/* 275 */     ECPoint eCPoint = ((ECPublicKeyParameters)asymmetricCipherKeyPair.getPublic()).getQ();
/*     */     
/* 277 */     String str2 = Hex.toHexString(eCPoint.getEncoded(false));
/* 278 */     paramMap.put("pubKey", str2);
/* 279 */     paramMap.put("priKey", str1);
/* 280 */     return paramMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public String decrypt_js(String paramString1, String paramString2, String paramString3, HttpServletRequest paramHttpServletRequest) {
/*     */     try {
/* 286 */       return decrypt(paramString2, paramString1);
/* 287 */     } catch (Exception exception) {
/* 288 */       exception.printStackTrace();
/* 289 */       return paramString2;
/*     */     } 
/*     */   }
/*     */   
/*     */   public static String decrypt(String paramString1, String paramString2) {
/* 294 */     return decrypt(paramString1, paramString2, true);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String decrypt(String paramString1, String paramString2, boolean paramBoolean) {
/*     */     try {
/* 308 */       String str1 = paramString1;
/* 309 */       byte[] arrayOfByte1 = null;
/* 310 */       if (paramBoolean) {
/* 311 */         arrayOfByte1 = Hex.decode(str1);
/*     */       } else {
/* 313 */         arrayOfByte1 = Base64.decode(str1);
/*     */       } 
/*     */       
/* 316 */       BigInteger bigInteger = new BigInteger(paramString2, 16);
/* 317 */       ECPrivateKeyParameters eCPrivateKeyParameters = new ECPrivateKeyParameters(bigInteger, domainParameters);
/*     */ 
/*     */       
/* 320 */       SM2Engine sM2Engine = new SM2Engine();
/* 321 */       sM2Engine.init(false, (CipherParameters)eCPrivateKeyParameters);
/*     */ 
/*     */       
/* 324 */       byte[] arrayOfByte2 = Base64.decode(sM2Engine.processBlock(arrayOfByte1, 0, arrayOfByte1.length));
/*     */       
/* 326 */       String str2 = new String(arrayOfByte2);
/*     */       
/* 328 */       int i = str2.length() - 8;
/* 329 */       str2 = str2.substring(0, i);
/*     */       
/* 331 */       return str2;
/* 332 */     } catch (Exception exception) {
/* 333 */       logger.error("解密失败：" + paramString1, exception);
/* 334 */       return null;
/*     */     } 
/*     */   }
/*     */   
/*     */   public static void main(String[] paramArrayOfString) {
/* 339 */     init();
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/sm/sm2/util/Sm2EncDecUtils.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */