/*     */ package weaver.sm.sm2.util;
/*     */ import java.io.BufferedReader;
/*     */ import java.io.CharArrayWriter;
/*     */ import java.io.EOFException;
/*     */ import java.io.IOException;
/*     */ import java.io.InputStream;
/*     */ import java.io.InputStreamReader;
/*     */ import java.io.OutputStream;
/*     */ import java.io.OutputStreamWriter;
/*     */ import java.io.PrintWriter;
/*     */ import java.io.Reader;
/*     */ import java.io.Writer;
/*     */ import java.net.URI;
/*     */ import java.net.URL;
/*     */ import java.nio.charset.Charset;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Collection;
/*     */ 
/*     */ public class Sm2IOUtils {
/*  20 */   public static final char DIR_SEPARATOR = File.separatorChar;
/*     */   
/*     */   private static final int EOF = -1;
/*     */   public static final char DIR_SEPARATOR_UNIX = '/';
/*     */   public static final char DIR_SEPARATOR_WINDOWS = '\\';
/*     */   public static final String LINE_SEPARATOR_UNIX = "\n";
/*     */   public static final String LINE_SEPARATOR_WINDOWS = "\r\n";
/*     */   public static final String LINE_SEPARATOR;
/*     */   private static final int DEFAULT_BUFFER_SIZE = 4096;
/*     */   private static final int SKIP_BUFFER_SIZE = 2048;
/*     */   private static char[] SKIP_CHAR_BUFFER;
/*     */   private static byte[] SKIP_BYTE_BUFFER;
/*     */   
/*     */   public static void closeQuietly(Closeable paramCloseable) {
/*     */     try {
/*  35 */       if (paramCloseable != null) {
/*  36 */         paramCloseable.close();
/*     */       }
/*  38 */     } catch (IOException iOException) {}
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static void closeQuietly(Socket paramSocket) {
/*  45 */     if (paramSocket != null) {
/*     */       try {
/*  47 */         paramSocket.close();
/*     */       }
/*  49 */       catch (IOException iOException) {}
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static void closeQuietly(Selector paramSelector) {
/*  56 */     if (paramSelector != null) {
/*     */       try {
/*  58 */         paramSelector.close();
/*     */       }
/*  60 */       catch (IOException iOException) {}
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static void closeQuietly(ServerSocket paramServerSocket) {
/*  67 */     if (paramServerSocket != null) {
/*     */       try {
/*  69 */         paramServerSocket.close();
/*     */       }
/*  71 */       catch (IOException iOException) {}
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static BufferedReader toBufferedReader(Reader paramReader) {
/*  79 */     return (paramReader instanceof BufferedReader) ? (BufferedReader)paramReader : new BufferedReader(paramReader);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static byte[] toByteArray(InputStream paramInputStream, long paramLong) throws IOException {
/*  86 */     if (paramLong > 2147483647L) {
/*  87 */       throw new IllegalArgumentException("Size cannot be greater than Integer max value: " + paramLong);
/*     */     }
/*     */     
/*  90 */     return toByteArray(paramInputStream, (int)paramLong);
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static byte[] toByteArray(InputStream paramInputStream, int paramInt) throws IOException {
/*  96 */     if (paramInt < 0) {
/*  97 */       throw new IllegalArgumentException("Size must be equal or greater than zero: " + paramInt);
/*     */     }
/*     */     
/* 100 */     if (paramInt == 0) {
/* 101 */       return new byte[0];
/*     */     }
/*     */     
/* 104 */     byte[] arrayOfByte = new byte[paramInt];
/* 105 */     int i = 0;
/*     */     int j;
/* 107 */     while (i < paramInt && (j = paramInputStream.read(arrayOfByte, i, paramInt - i)) != -1) {
/* 108 */       i += j;
/*     */     }
/*     */     
/* 111 */     if (i != paramInt) {
/* 112 */       throw new IOException("Unexpected readed size. current: " + i + ", excepted: " + paramInt);
/*     */     }
/*     */     
/* 115 */     return arrayOfByte;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static char[] toCharArray(InputStream paramInputStream) throws IOException {
/* 121 */     return toCharArray(paramInputStream, Charset.defaultCharset());
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static char[] toCharArray(InputStream paramInputStream, Charset paramCharset) throws IOException {
/* 127 */     CharArrayWriter charArrayWriter = new CharArrayWriter();
/* 128 */     copy(paramInputStream, charArrayWriter, paramCharset);
/* 129 */     return charArrayWriter.toCharArray();
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static char[] toCharArray(InputStream paramInputStream, String paramString) throws IOException {
/* 135 */     return toCharArray(paramInputStream, Sm2Charsets.toCharset(paramString));
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static char[] toCharArray(Reader paramReader) throws IOException {
/* 141 */     CharArrayWriter charArrayWriter = new CharArrayWriter();
/* 142 */     copy(paramReader, charArrayWriter);
/* 143 */     return charArrayWriter.toCharArray();
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static String toString(InputStream paramInputStream) throws IOException {
/* 149 */     return toString(paramInputStream, Charset.defaultCharset());
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static String toString(InputStream paramInputStream, Charset paramCharset) throws IOException {
/* 155 */     Sm2StringBuilderWriter sm2StringBuilderWriter = new Sm2StringBuilderWriter();
/* 156 */     copy(paramInputStream, sm2StringBuilderWriter, paramCharset);
/* 157 */     return sm2StringBuilderWriter.toString();
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static String toString(InputStream paramInputStream, String paramString) throws IOException {
/* 163 */     return toString(paramInputStream, Sm2Charsets.toCharset(paramString));
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static String toString(Reader paramReader) throws IOException {
/* 169 */     Sm2StringBuilderWriter sm2StringBuilderWriter = new Sm2StringBuilderWriter();
/* 170 */     copy(paramReader, sm2StringBuilderWriter);
/* 171 */     return sm2StringBuilderWriter.toString();
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static String toString(URI paramURI) throws IOException {
/* 177 */     return toString(paramURI, Charset.defaultCharset());
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static String toString(URI paramURI, Charset paramCharset) throws IOException {
/* 183 */     return toString(paramURI.toURL(), Sm2Charsets.toCharset(paramCharset));
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static String toString(URI paramURI, String paramString) throws IOException {
/* 189 */     return toString(paramURI, Sm2Charsets.toCharset(paramString));
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static String toString(URL paramURL) throws IOException {
/* 195 */     return toString(paramURL, Charset.defaultCharset());
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static String toString(URL paramURL, Charset paramCharset) throws IOException {
/* 201 */     InputStream inputStream = paramURL.openStream();
/*     */     try {
/* 203 */       return toString(inputStream, paramCharset);
/*     */     } finally {
/* 205 */       inputStream.close();
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static String toString(URL paramURL, String paramString) throws IOException {
/* 212 */     return toString(paramURL, Sm2Charsets.toCharset(paramString));
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public static String toString(byte[] paramArrayOfbyte) throws IOException {
/* 219 */     return new String(paramArrayOfbyte);
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static String toString(byte[] paramArrayOfbyte, String paramString) throws IOException {
/* 225 */     return new String(paramArrayOfbyte, Sm2Charsets.toCharset(paramString));
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static List<String> readLines(InputStream paramInputStream) throws IOException {
/* 231 */     return readLines(paramInputStream, Charset.defaultCharset());
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static List<String> readLines(InputStream paramInputStream, Charset paramCharset) throws IOException {
/* 237 */     InputStreamReader inputStreamReader = new InputStreamReader(paramInputStream, Sm2Charsets.toCharset(paramCharset));
/* 238 */     return readLines(inputStreamReader);
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static List<String> readLines(InputStream paramInputStream, String paramString) throws IOException {
/* 244 */     return readLines(paramInputStream, Sm2Charsets.toCharset(paramString));
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static List<String> readLines(Reader paramReader) throws IOException {
/* 250 */     BufferedReader bufferedReader = toBufferedReader(paramReader);
/* 251 */     ArrayList<String> arrayList = new ArrayList();
/* 252 */     String str = bufferedReader.readLine();
/* 253 */     while (str != null) {
/* 254 */       arrayList.add(str);
/* 255 */       str = bufferedReader.readLine();
/*     */     } 
/* 257 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static InputStream toInputStream(CharSequence paramCharSequence) {
/* 263 */     return toInputStream(paramCharSequence, Charset.defaultCharset());
/*     */   }
/*     */ 
/*     */   
/*     */   public static InputStream toInputStream(CharSequence paramCharSequence, Charset paramCharset) {
/* 268 */     return toInputStream(paramCharSequence.toString(), paramCharset);
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static InputStream toInputStream(CharSequence paramCharSequence, String paramString) throws IOException {
/* 274 */     return toInputStream(paramCharSequence, Sm2Charsets.toCharset(paramString));
/*     */   }
/*     */ 
/*     */   
/*     */   public static InputStream toInputStream(String paramString) {
/* 279 */     return toInputStream(paramString, Charset.defaultCharset());
/*     */   }
/*     */ 
/*     */   
/*     */   public static InputStream toInputStream(String paramString, Charset paramCharset) {
/* 284 */     return new ByteArrayInputStream(paramString.getBytes(Sm2Charsets.toCharset(paramCharset)));
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static InputStream toInputStream(String paramString1, String paramString2) throws IOException {
/* 290 */     byte[] arrayOfByte = paramString1.getBytes(Sm2Charsets.toCharset(paramString2));
/* 291 */     return new ByteArrayInputStream(arrayOfByte);
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static void write(byte[] paramArrayOfbyte, OutputStream paramOutputStream) throws IOException {
/* 297 */     if (paramArrayOfbyte != null) {
/* 298 */       paramOutputStream.write(paramArrayOfbyte);
/*     */     }
/*     */   }
/*     */ 
/*     */   
/*     */   public static void write(byte[] paramArrayOfbyte, Writer paramWriter) throws IOException {
/* 304 */     write(paramArrayOfbyte, paramWriter, Charset.defaultCharset());
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static void write(byte[] paramArrayOfbyte, Writer paramWriter, Charset paramCharset) throws IOException {
/* 310 */     if (paramArrayOfbyte != null) {
/* 311 */       paramWriter.write(new String(paramArrayOfbyte, Sm2Charsets.toCharset(paramCharset)));
/*     */     }
/*     */   }
/*     */ 
/*     */   
/*     */   public static void write(byte[] paramArrayOfbyte, Writer paramWriter, String paramString) throws IOException {
/* 317 */     write(paramArrayOfbyte, paramWriter, Sm2Charsets.toCharset(paramString));
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static void write(char[] paramArrayOfchar, Writer paramWriter) throws IOException {
/* 323 */     if (paramArrayOfchar != null) {
/* 324 */       paramWriter.write(paramArrayOfchar);
/*     */     }
/*     */   }
/*     */ 
/*     */   
/*     */   public static void write(char[] paramArrayOfchar, OutputStream paramOutputStream) throws IOException {
/* 330 */     write(paramArrayOfchar, paramOutputStream, Charset.defaultCharset());
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static void write(char[] paramArrayOfchar, OutputStream paramOutputStream, Charset paramCharset) throws IOException {
/* 336 */     if (paramArrayOfchar != null) {
/* 337 */       paramOutputStream.write((new String(paramArrayOfchar)).getBytes(Sm2Charsets.toCharset(paramCharset)));
/*     */     }
/*     */   }
/*     */ 
/*     */   
/*     */   public static void write(char[] paramArrayOfchar, OutputStream paramOutputStream, String paramString) throws IOException {
/* 343 */     write(paramArrayOfchar, paramOutputStream, Sm2Charsets.toCharset(paramString));
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static void write(CharSequence paramCharSequence, Writer paramWriter) throws IOException {
/* 349 */     if (paramCharSequence != null) {
/* 350 */       write(paramCharSequence.toString(), paramWriter);
/*     */     }
/*     */   }
/*     */ 
/*     */   
/*     */   public static void write(CharSequence paramCharSequence, OutputStream paramOutputStream) throws IOException {
/* 356 */     write(paramCharSequence, paramOutputStream, Charset.defaultCharset());
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static void write(CharSequence paramCharSequence, OutputStream paramOutputStream, Charset paramCharset) throws IOException {
/* 362 */     if (paramCharSequence != null) {
/* 363 */       write(paramCharSequence.toString(), paramOutputStream, paramCharset);
/*     */     }
/*     */   }
/*     */ 
/*     */   
/*     */   public static void write(CharSequence paramCharSequence, OutputStream paramOutputStream, String paramString) throws IOException {
/* 369 */     write(paramCharSequence, paramOutputStream, Sm2Charsets.toCharset(paramString));
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static void write(String paramString, Writer paramWriter) throws IOException {
/* 375 */     if (paramString != null) {
/* 376 */       paramWriter.write(paramString);
/*     */     }
/*     */   }
/*     */ 
/*     */   
/*     */   public static void write(String paramString, OutputStream paramOutputStream) throws IOException {
/* 382 */     write(paramString, paramOutputStream, Charset.defaultCharset());
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static void write(String paramString, OutputStream paramOutputStream, Charset paramCharset) throws IOException {
/* 388 */     if (paramString != null) {
/* 389 */       paramOutputStream.write(paramString.getBytes(Sm2Charsets.toCharset(paramCharset)));
/*     */     }
/*     */   }
/*     */ 
/*     */   
/*     */   public static void write(String paramString1, OutputStream paramOutputStream, String paramString2) throws IOException {
/* 395 */     write(paramString1, paramOutputStream, Sm2Charsets.toCharset(paramString2));
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public static void write(StringBuffer paramStringBuffer, Writer paramWriter) throws IOException {
/* 402 */     if (paramStringBuffer != null) {
/* 403 */       paramWriter.write(paramStringBuffer.toString());
/*     */     }
/*     */   }
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public static void write(StringBuffer paramStringBuffer, OutputStream paramOutputStream) throws IOException {
/* 410 */     write(paramStringBuffer, paramOutputStream, (String)null);
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public static void write(StringBuffer paramStringBuffer, OutputStream paramOutputStream, String paramString) throws IOException {
/* 417 */     if (paramStringBuffer != null) {
/* 418 */       paramOutputStream.write(paramStringBuffer.toString().getBytes(Sm2Charsets.toCharset(paramString)));
/*     */     }
/*     */   }
/*     */ 
/*     */   
/*     */   public static void writeLines(Collection<?> paramCollection, String paramString, OutputStream paramOutputStream) throws IOException {
/* 424 */     writeLines(paramCollection, paramString, paramOutputStream, Charset.defaultCharset());
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static void writeLines(Collection<?> paramCollection, String paramString, OutputStream paramOutputStream, Charset paramCharset) throws IOException {
/* 430 */     if (paramCollection == null) {
/*     */       return;
/*     */     }
/* 433 */     if (paramString == null) {
/* 434 */       paramString = LINE_SEPARATOR;
/*     */     }
/* 436 */     Charset charset = Sm2Charsets.toCharset(paramCharset);
/* 437 */     for (Object object : paramCollection) {
/* 438 */       if (object != null) {
/* 439 */         paramOutputStream.write(object.toString().getBytes(charset));
/*     */       }
/* 441 */       paramOutputStream.write(paramString.getBytes(charset));
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static void writeLines(Collection<?> paramCollection, String paramString1, OutputStream paramOutputStream, String paramString2) throws IOException {
/* 448 */     writeLines(paramCollection, paramString1, paramOutputStream, Sm2Charsets.toCharset(paramString2));
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static void writeLines(Collection<?> paramCollection, String paramString, Writer paramWriter) throws IOException {
/* 454 */     if (paramCollection == null) {
/*     */       return;
/*     */     }
/* 457 */     if (paramString == null) {
/* 458 */       paramString = LINE_SEPARATOR;
/*     */     }
/* 460 */     for (Object object : paramCollection) {
/* 461 */       if (object != null) {
/* 462 */         paramWriter.write(object.toString());
/*     */       }
/* 464 */       paramWriter.write(paramString);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static int copy(InputStream paramInputStream, OutputStream paramOutputStream) throws IOException {
/* 471 */     long l = copyLarge(paramInputStream, paramOutputStream);
/* 472 */     if (l > 2147483647L) {
/* 473 */       return -1;
/*     */     }
/* 475 */     return (int)l;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static long copyLarge(InputStream paramInputStream, OutputStream paramOutputStream) throws IOException {
/* 481 */     return copyLarge(paramInputStream, paramOutputStream, new byte[4096]);
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static long copyLarge(InputStream paramInputStream, OutputStream paramOutputStream, byte[] paramArrayOfbyte) throws IOException {
/* 487 */     long l = 0L;
/* 488 */     int i = 0;
/* 489 */     while (-1 != (i = paramInputStream.read(paramArrayOfbyte))) {
/* 490 */       paramOutputStream.write(paramArrayOfbyte, 0, i);
/* 491 */       l += i;
/*     */     } 
/* 493 */     return l;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static long copyLarge(InputStream paramInputStream, OutputStream paramOutputStream, long paramLong1, long paramLong2) throws IOException {
/* 499 */     return copyLarge(paramInputStream, paramOutputStream, paramLong1, paramLong2, new byte[4096]);
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static long copyLarge(InputStream paramInputStream, OutputStream paramOutputStream, long paramLong1, long paramLong2, byte[] paramArrayOfbyte) throws IOException {
/* 505 */     if (paramLong1 > 0L) {
/* 506 */       skipFully(paramInputStream, paramLong1);
/*     */     }
/* 508 */     if (paramLong2 == 0L) {
/* 509 */       return 0L;
/*     */     }
/* 511 */     int i = paramArrayOfbyte.length;
/* 512 */     int j = i;
/* 513 */     if (paramLong2 > 0L && paramLong2 < i) {
/* 514 */       j = (int)paramLong2;
/*     */     }
/*     */     
/* 517 */     long l = 0L;
/*     */     int k;
/* 519 */     while (j > 0 && -1 != (k = paramInputStream.read(paramArrayOfbyte, 0, j))) {
/* 520 */       paramOutputStream.write(paramArrayOfbyte, 0, k);
/* 521 */       l += k;
/* 522 */       if (paramLong2 > 0L)
/*     */       {
/* 524 */         j = (int)Math.min(paramLong2 - l, i);
/*     */       }
/*     */     } 
/* 527 */     return l;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static void copy(InputStream paramInputStream, Writer paramWriter) throws IOException {
/* 533 */     copy(paramInputStream, paramWriter, Charset.defaultCharset());
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static void copy(InputStream paramInputStream, Writer paramWriter, Charset paramCharset) throws IOException {
/* 539 */     InputStreamReader inputStreamReader = new InputStreamReader(paramInputStream, Sm2Charsets.toCharset(paramCharset));
/* 540 */     copy(inputStreamReader, paramWriter);
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static void copy(InputStream paramInputStream, Writer paramWriter, String paramString) throws IOException {
/* 546 */     copy(paramInputStream, paramWriter, Sm2Charsets.toCharset(paramString));
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static int copy(Reader paramReader, Writer paramWriter) throws IOException {
/* 552 */     long l = copyLarge(paramReader, paramWriter);
/* 553 */     if (l > 2147483647L) {
/* 554 */       return -1;
/*     */     }
/* 556 */     return (int)l;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static long copyLarge(Reader paramReader, Writer paramWriter) throws IOException {
/* 562 */     return copyLarge(paramReader, paramWriter, new char[4096]);
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static long copyLarge(Reader paramReader, Writer paramWriter, char[] paramArrayOfchar) throws IOException {
/* 568 */     long l = 0L;
/* 569 */     int i = 0;
/* 570 */     while (-1 != (i = paramReader.read(paramArrayOfchar))) {
/* 571 */       paramWriter.write(paramArrayOfchar, 0, i);
/* 572 */       l += i;
/*     */     } 
/* 574 */     return l;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static long copyLarge(Reader paramReader, Writer paramWriter, long paramLong1, long paramLong2) throws IOException {
/* 580 */     return copyLarge(paramReader, paramWriter, paramLong1, paramLong2, new char[4096]);
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static long copyLarge(Reader paramReader, Writer paramWriter, long paramLong1, long paramLong2, char[] paramArrayOfchar) throws IOException {
/* 586 */     if (paramLong1 > 0L) {
/* 587 */       skipFully(paramReader, paramLong1);
/*     */     }
/* 589 */     if (paramLong2 == 0L) {
/* 590 */       return 0L;
/*     */     }
/* 592 */     int i = paramArrayOfchar.length;
/* 593 */     if (paramLong2 > 0L && paramLong2 < paramArrayOfchar.length) {
/* 594 */       i = (int)paramLong2;
/*     */     }
/*     */     
/* 597 */     long l = 0L;
/*     */     int j;
/* 599 */     while (i > 0 && -1 != (j = paramReader.read(paramArrayOfchar, 0, i))) {
/* 600 */       paramWriter.write(paramArrayOfchar, 0, j);
/* 601 */       l += j;
/* 602 */       if (paramLong2 > 0L)
/*     */       {
/* 604 */         i = (int)Math.min(paramLong2 - l, paramArrayOfchar.length);
/*     */       }
/*     */     } 
/* 607 */     return l;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static void copy(Reader paramReader, OutputStream paramOutputStream) throws IOException {
/* 613 */     copy(paramReader, paramOutputStream, Charset.defaultCharset());
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static void copy(Reader paramReader, OutputStream paramOutputStream, Charset paramCharset) throws IOException {
/* 619 */     OutputStreamWriter outputStreamWriter = new OutputStreamWriter(paramOutputStream, Sm2Charsets.toCharset(paramCharset));
/* 620 */     copy(paramReader, outputStreamWriter);
/*     */     
/* 622 */     outputStreamWriter.flush();
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static void copy(Reader paramReader, OutputStream paramOutputStream, String paramString) throws IOException {
/* 628 */     copy(paramReader, paramOutputStream, Sm2Charsets.toCharset(paramString));
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean contentEquals(InputStream paramInputStream1, InputStream paramInputStream2) throws IOException {
/* 634 */     if (!(paramInputStream1 instanceof BufferedInputStream)) {
/* 635 */       paramInputStream1 = new BufferedInputStream(paramInputStream1);
/*     */     }
/* 637 */     if (!(paramInputStream2 instanceof BufferedInputStream)) {
/* 638 */       paramInputStream2 = new BufferedInputStream(paramInputStream2);
/*     */     }
/*     */     
/* 641 */     int i = paramInputStream1.read();
/* 642 */     while (-1 != i) {
/* 643 */       int k = paramInputStream2.read();
/* 644 */       if (i != k) {
/* 645 */         return false;
/*     */       }
/* 647 */       i = paramInputStream1.read();
/*     */     } 
/*     */     
/* 650 */     int j = paramInputStream2.read();
/* 651 */     return (j == -1);
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean contentEquals(Reader paramReader1, Reader paramReader2) throws IOException {
/* 657 */     paramReader1 = toBufferedReader(paramReader1);
/* 658 */     paramReader2 = toBufferedReader(paramReader2);
/*     */     
/* 660 */     int i = paramReader1.read();
/* 661 */     while (-1 != i) {
/* 662 */       int k = paramReader2.read();
/* 663 */       if (i != k) {
/* 664 */         return false;
/*     */       }
/* 666 */       i = paramReader1.read();
/*     */     } 
/*     */     
/* 669 */     int j = paramReader2.read();
/* 670 */     return (j == -1);
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean contentEqualsIgnoreEOL(Reader paramReader1, Reader paramReader2) throws IOException {
/* 676 */     BufferedReader bufferedReader1 = toBufferedReader(paramReader1);
/* 677 */     BufferedReader bufferedReader2 = toBufferedReader(paramReader2);
/*     */     
/* 679 */     String str1 = bufferedReader1.readLine();
/* 680 */     String str2 = bufferedReader2.readLine();
/* 681 */     while (str1 != null && str2 != null && str1.equals(str2)) {
/* 682 */       str1 = bufferedReader1.readLine();
/* 683 */       str2 = bufferedReader2.readLine();
/*     */     } 
/* 685 */     return (str1 == null) ? false : ((str2 == null) ? true : str1.equals(str2));
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static long skip(InputStream paramInputStream, long paramLong) throws IOException {
/* 691 */     if (paramLong < 0L) {
/* 692 */       throw new IllegalArgumentException("Skip count must be non-negative, actual: " + paramLong);
/*     */     }
/*     */     
/* 695 */     if (SKIP_BYTE_BUFFER == null) {
/* 696 */       SKIP_BYTE_BUFFER = new byte[2048];
/*     */     }
/* 698 */     long l = paramLong;
/* 699 */     while (l > 0L) {
/* 700 */       long l1 = paramInputStream.read(SKIP_BYTE_BUFFER, 0, (int)Math.min(l, 2048L));
/* 701 */       if (l1 < 0L) {
/*     */         break;
/*     */       }
/* 704 */       l -= l1;
/*     */     } 
/* 706 */     return paramLong - l;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static long skip(Reader paramReader, long paramLong) throws IOException {
/* 712 */     if (paramLong < 0L) {
/* 713 */       throw new IllegalArgumentException("Skip count must be non-negative, actual: " + paramLong);
/*     */     }
/*     */     
/* 716 */     if (SKIP_CHAR_BUFFER == null) {
/* 717 */       SKIP_CHAR_BUFFER = new char[2048];
/*     */     }
/* 719 */     long l = paramLong;
/* 720 */     while (l > 0L) {
/* 721 */       long l1 = paramReader.read(SKIP_CHAR_BUFFER, 0, (int)Math.min(l, 2048L));
/* 722 */       if (l1 < 0L) {
/*     */         break;
/*     */       }
/* 725 */       l -= l1;
/*     */     } 
/* 727 */     return paramLong - l;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static void skipFully(InputStream paramInputStream, long paramLong) throws IOException {
/* 733 */     if (paramLong < 0L) {
/* 734 */       throw new IllegalArgumentException("Bytes to skip must not be negative: " + paramLong);
/*     */     }
/* 736 */     long l = skip(paramInputStream, paramLong);
/* 737 */     if (l != paramLong) {
/* 738 */       throw new EOFException("Bytes to skip: " + paramLong + " actual: " + l);
/*     */     }
/*     */   }
/*     */ 
/*     */   
/*     */   public static void skipFully(Reader paramReader, long paramLong) throws IOException {
/* 744 */     long l = skip(paramReader, paramLong);
/* 745 */     if (l != paramLong) {
/* 746 */       throw new EOFException("Chars to skip: " + paramLong + " actual: " + l);
/*     */     }
/*     */   }
/*     */ 
/*     */   
/*     */   public static int read(Reader paramReader, char[] paramArrayOfchar, int paramInt1, int paramInt2) throws IOException {
/* 752 */     if (paramInt2 < 0) {
/* 753 */       throw new IllegalArgumentException("Length must not be negative: " + paramInt2);
/*     */     }
/* 755 */     int i = paramInt2;
/* 756 */     while (i > 0) {
/* 757 */       int j = paramInt2 - i;
/* 758 */       int k = paramReader.read(paramArrayOfchar, paramInt1 + j, i);
/* 759 */       if (-1 == k) {
/*     */         break;
/*     */       }
/* 762 */       i -= k;
/*     */     } 
/* 764 */     return paramInt2 - i;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static int read(Reader paramReader, char[] paramArrayOfchar) throws IOException {
/* 770 */     return read(paramReader, paramArrayOfchar, 0, paramArrayOfchar.length);
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static int read(InputStream paramInputStream, byte[] paramArrayOfbyte, int paramInt1, int paramInt2) throws IOException {
/* 776 */     if (paramInt2 < 0) {
/* 777 */       throw new IllegalArgumentException("Length must not be negative: " + paramInt2);
/*     */     }
/* 779 */     int i = paramInt2;
/* 780 */     while (i > 0) {
/* 781 */       int j = paramInt2 - i;
/* 782 */       int k = paramInputStream.read(paramArrayOfbyte, paramInt1 + j, i);
/* 783 */       if (-1 == k) {
/*     */         break;
/*     */       }
/* 786 */       i -= k;
/*     */     } 
/* 788 */     return paramInt2 - i;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static int read(InputStream paramInputStream, byte[] paramArrayOfbyte) throws IOException {
/* 794 */     return read(paramInputStream, paramArrayOfbyte, 0, paramArrayOfbyte.length);
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static void readFully(Reader paramReader, char[] paramArrayOfchar, int paramInt1, int paramInt2) throws IOException {
/* 800 */     int i = read(paramReader, paramArrayOfchar, paramInt1, paramInt2);
/* 801 */     if (i != paramInt2) {
/* 802 */       throw new EOFException("Length to read: " + paramInt2 + " actual: " + i);
/*     */     }
/*     */   }
/*     */ 
/*     */   
/*     */   public static void readFully(Reader paramReader, char[] paramArrayOfchar) throws IOException {
/* 808 */     readFully(paramReader, paramArrayOfchar, 0, paramArrayOfchar.length);
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static void readFully(InputStream paramInputStream, byte[] paramArrayOfbyte, int paramInt1, int paramInt2) throws IOException {
/* 814 */     int i = read(paramInputStream, paramArrayOfbyte, paramInt1, paramInt2);
/* 815 */     if (i != paramInt2) {
/* 816 */       throw new EOFException("Length to read: " + paramInt2 + " actual: " + i);
/*     */     }
/*     */   }
/*     */ 
/*     */   
/*     */   public static void readFully(InputStream paramInputStream, byte[] paramArrayOfbyte) throws IOException {
/* 822 */     readFully(paramInputStream, paramArrayOfbyte, 0, paramArrayOfbyte.length);
/*     */   }
/*     */ 
/*     */   
/*     */   static {
/* 827 */     Sm2StringBuilderWriter sm2StringBuilderWriter = new Sm2StringBuilderWriter(4);
/* 828 */     PrintWriter printWriter = new PrintWriter(sm2StringBuilderWriter);
/* 829 */     printWriter.println();
/* 830 */     LINE_SEPARATOR = sm2StringBuilderWriter.toString();
/* 831 */     printWriter.close();
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/sm/sm2/util/Sm2IOUtils.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */