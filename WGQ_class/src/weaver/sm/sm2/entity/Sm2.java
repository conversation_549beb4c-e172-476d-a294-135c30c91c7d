/*    */ package weaver.sm.sm2.entity;
/*    */ 
/*    */ import java.math.BigInteger;
/*    */ import java.security.SecureRandom;
/*    */ import org.bouncycastle.crypto.KeyGenerationParameters;
/*    */ import org.bouncycastle.crypto.generators.ECKeyPairGenerator;
/*    */ import org.bouncycastle.crypto.params.ECDomainParameters;
/*    */ import org.bouncycastle.crypto.params.ECKeyGenerationParameters;
/*    */ import org.bouncycastle.math.ec.ECCurve;
/*    */ import org.bouncycastle.math.ec.ECFieldElement;
/*    */ import org.bouncycastle.math.ec.ECPoint;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class Sm2
/*    */ {
/* 23 */   public static String[] ecc_param = new String[] { "FFFFFFFEFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF00000000FFFFFFFFFFFFFFFF", "FFFFFFFEFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF00000000FFFFFFFFFFFFFFFC", "28E9FA9E9D9F5E344D5A9E4BCF6509A7F39789F515AB8F92DDBCBD414D940E93", "FFFFFFFEFFFFFFFFFFFFFFFFFFFFFFFF7203DF6B21C6052B53BBF40939D54123", "32C4AE2C1F1981195F9904466A39C9948FE30BBFF2660BE1715A4589334C74C7", "BC3736A2F4F6779C59BDCEE36B692153D0A9877CC62A474002DF32E52139F0A0" };
/*    */   
/*    */   public final BigInteger ecc_p;
/*    */   
/*    */   public final BigInteger ecc_a;
/*    */   
/*    */   public final BigInteger ecc_b;
/*    */   public final BigInteger ecc_n;
/*    */   
/*    */   public static Sm2 instance() {
/* 33 */     return new Sm2();
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/* 42 */   public final BigInteger ecc_h = new BigInteger("1", 16);
/*    */   
/*    */   public final BigInteger ecc_gx;
/*    */   
/*    */   public final BigInteger ecc_gy;
/*    */   
/*    */   public final ECCurve ecc_curve;
/*    */   public final ECPoint ecc_point_g;
/*    */   public final ECDomainParameters ecc_bc_spec;
/*    */   public final ECKeyPairGenerator ecc_key_pair_generator;
/*    */   public final ECFieldElement ecc_gx_fieldelement;
/*    */   public final ECFieldElement ecc_gy_fieldelement;
/*    */   
/*    */   public Sm2() {
/* 56 */     this.ecc_p = new BigInteger(ecc_param[0], 16);
/* 57 */     this.ecc_a = new BigInteger(ecc_param[1], 16);
/* 58 */     this.ecc_b = new BigInteger(ecc_param[2], 16);
/* 59 */     this.ecc_n = new BigInteger(ecc_param[3], 16);
/* 60 */     this.ecc_gx = new BigInteger(ecc_param[4], 16);
/* 61 */     this.ecc_gy = new BigInteger(ecc_param[5], 16);
/*    */     
/* 63 */     this.ecc_gx_fieldelement = (ECFieldElement)new ECFieldElement.Fp(this.ecc_p, this.ecc_gx);
/* 64 */     this.ecc_gy_fieldelement = (ECFieldElement)new ECFieldElement.Fp(this.ecc_p, this.ecc_gy);
/*    */ 
/*    */     
/* 67 */     this.ecc_curve = (ECCurve)new ECCurve.Fp(this.ecc_p, this.ecc_a, this.ecc_b);
/*    */     
/* 69 */     this.ecc_point_g = (ECPoint)new ECPoint.Fp(this.ecc_curve, this.ecc_gx_fieldelement, this.ecc_gy_fieldelement);
/*    */     
/* 71 */     this.ecc_bc_spec = new ECDomainParameters(this.ecc_curve, this.ecc_point_g, this.ecc_n);
/*    */ 
/*    */ 
/*    */ 
/*    */     
/* 76 */     ECKeyGenerationParameters eCKeyGenerationParameters = new ECKeyGenerationParameters(this.ecc_bc_spec, new SecureRandom());
/*    */     
/* 78 */     this.ecc_key_pair_generator = new ECKeyPairGenerator();
/*    */ 
/*    */     
/* 81 */     this.ecc_key_pair_generator.init((KeyGenerationParameters)eCKeyGenerationParameters);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/sm/sm2/entity/Sm2.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */