/*    */ package weaver.sm.sm2.entity;
/*    */ 
/*    */ import java.util.Map;
/*    */ import org.apache.commons.lang.StringUtils;
/*    */ import org.springframework.util.CollectionUtils;
/*    */ import weaver.sm.sm2.cipher.ECCCurveJS;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class KjurCryptoEcdsa
/*    */ {
/*    */   private String curveName;
/*    */   private Map ecparams;
/*    */   private String prvKeyHex;
/*    */   private String pubKeyHex;
/*    */   private boolean isPrivate;
/*    */   private boolean isPublic;
/*    */   
/*    */   public static KjurCryptoEcdsa instance(Map<String, String> paramMap) {
/* 26 */     return new KjurCryptoEcdsa(paramMap);
/*    */   }
/*    */   
/*    */   private KjurCryptoEcdsa(Map<String, String> paramMap) {
/* 30 */     this.ecparams = ECCCurveJS.getDb();
/* 31 */     this.prvKeyHex = null;
/* 32 */     this.pubKeyHex = null;
/* 33 */     this.curveName = paramMap.get("curveName");
/*    */     
/* 35 */     if (!CollectionUtils.isEmpty(paramMap)) {
/* 36 */       if (!StringUtils.isEmpty(paramMap.get("prv"))) {
/* 37 */         this.isPrivate = true;
/* 38 */         this.prvKeyHex = paramMap.get("prv");
/*    */       } 
/* 40 */       if (!StringUtils.isEmpty(paramMap.get("pub"))) {
/* 41 */         this.isPublic = true;
/* 42 */         this.pubKeyHex = paramMap.get("pub");
/*    */       } 
/*    */     } 
/*    */   }
/*    */ 
/*    */   
/*    */   public String getCurveName() {
/* 49 */     return this.curveName;
/*    */   }
/*    */   
/*    */   public Map getEcparams() {
/* 53 */     return this.ecparams;
/*    */   }
/*    */   
/*    */   public String getPrvKeyHex() {
/* 57 */     return this.prvKeyHex;
/*    */   }
/*    */   
/*    */   public String getPubKeyHex() {
/* 61 */     return this.pubKeyHex;
/*    */   }
/*    */   
/*    */   public boolean isPrivate() {
/* 65 */     return this.isPrivate;
/*    */   }
/*    */   
/*    */   public boolean isPublic() {
/* 69 */     return this.isPublic;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/sm/sm2/entity/KjurCryptoEcdsa.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */