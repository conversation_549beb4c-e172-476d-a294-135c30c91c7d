/*     */ package weaver.sm;
/*     */ 
/*     */ import java.io.ByteArrayInputStream;
/*     */ import java.io.ByteArrayOutputStream;
/*     */ import java.io.IOException;
/*     */ import java.io.InputStream;
/*     */ import java.io.OutputStream;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Arrays;
/*     */ import java.util.List;
/*     */ import org.apache.commons.io.IOUtils;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class TestSM4Utils
/*     */ {
/*     */   private static final int DECRYPT = 0;
/*     */   public static final int ROUND = 32;
/*     */   private static final int BLOCK = 16;
/*     */   
/*     */   public static void main(String[] paramArrayOfString) {}
/*     */   
/*  32 */   private static byte[] Sbox = new byte[] { -42, -112, -23, -2, -52, -31, 61, -73, 22, -74, 20, -62, 40, -5, 44, 5, 43, 103, -102, 118, 42, -66, 4, -61, -86, 68, 19, 38, 73, -122, 6, -103, -100, 66, 80, -12, -111, -17, -104, 122, 51, 84, 11, 67, -19, -49, -84, 98, -28, -77, 28, -87, -55, 8, -24, -107, Byte.MIN_VALUE, -33, -108, -6, 117, -113, 63, -90, 71, 7, -89, -4, -13, 115, 23, -70, -125, 89, 60, 25, -26, -123, 79, -88, 104, 107, -127, -78, 113, 100, -38, -117, -8, -21, 15, 75, 112, 86, -99, 53, 30, 36, 14, 94, 99, 88, -47, -94, 37, 34, 124, 59, 1, 33, 120, -121, -44, 0, 70, 87, -97, -45, 39, 82, 76, 54, 2, -25, -96, -60, -56, -98, -22, -65, -118, -46, 64, -57, 56, -75, -93, -9, -14, -50, -7, 97, 21, -95, -32, -82, 93, -92, -101, 52, 26, 85, -83, -109, 50, 48, -11, -116, -79, -29, 29, -10, -30, 46, -126, 102, -54, 96, -64, 41, 35, -85, 13, 83, 78, 111, -43, -37, 55, 69, -34, -3, -114, 47, 3, -1, 106, 114, 109, 108, 91, 81, -115, 27, -81, -110, -69, -35, -68, Byte.MAX_VALUE, 17, -39, 92, 65, 31, 16, 90, -40, 10, -63, 49, -120, -91, -51, 123, -67, 45, 116, -48, 18, -72, -27, -76, -80, -119, 105, -105, 74, 12, -106, 119, 126, 101, -71, -15, 9, -59, 110, -58, -124, 24, -16, 125, -20, 58, -36, 77, 32, 121, -18, 95, 62, -41, -53, 57, 72 };
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  51 */   private static int[] CK = new int[] { 462357, 472066609, 943670861, 1415275113, 1886879365, -1936483679, -1464879427, -993275175, -521670923, -66909679, 404694573, 876298825, 1347903077, 1819507329, -2003855715, -1532251463, -1060647211, -589042959, -117504499, 337322537, 808926789, 1280531041, 1752135293, -2071227751, -1599623499, -1128019247, -656414995, -184876535, 269950501, 741554753, 1213159005, 1684763257 };
/*     */ 
/*     */ 
/*     */   
/*     */   private static int Rotl(int paramInt1, int paramInt2) {
/*  56 */     return paramInt1 << paramInt2 | paramInt1 >>> 32 - paramInt2;
/*     */   }
/*     */   
/*     */   private static int ByteSub(int paramInt) {
/*  60 */     return (Sbox[paramInt >>> 24 & 0xFF] & 0xFF) << 24 | (Sbox[paramInt >>> 16 & 0xFF] & 0xFF) << 16 | (Sbox[paramInt >>> 8 & 0xFF] & 0xFF) << 8 | Sbox[paramInt & 0xFF] & 0xFF;
/*     */   }
/*     */   
/*     */   private static int L1(int paramInt) {
/*  64 */     return paramInt ^ Rotl(paramInt, 2) ^ Rotl(paramInt, 10) ^ Rotl(paramInt, 18) ^ Rotl(paramInt, 24);
/*     */   }
/*     */   
/*     */   private static int L2(int paramInt) {
/*  68 */     return paramInt ^ Rotl(paramInt, 13) ^ Rotl(paramInt, 23);
/*     */   }
/*     */ 
/*     */   
/*     */   static void SMS4Crypt(byte[] paramArrayOfbyte1, byte[] paramArrayOfbyte2, int[] paramArrayOfint) {
/*  73 */     int[] arrayOfInt1 = new int[4];
/*  74 */     int[] arrayOfInt2 = new int[4]; byte b2;
/*  75 */     for (b2 = 0; b2 < 4; b2++) {
/*  76 */       arrayOfInt2[0] = paramArrayOfbyte1[0 + 4 * b2] & 0xFF;
/*  77 */       arrayOfInt2[1] = paramArrayOfbyte1[1 + 4 * b2] & 0xFF;
/*  78 */       arrayOfInt2[2] = paramArrayOfbyte1[2 + 4 * b2] & 0xFF;
/*  79 */       arrayOfInt2[3] = paramArrayOfbyte1[3 + 4 * b2] & 0xFF;
/*  80 */       arrayOfInt1[b2] = arrayOfInt2[0] << 24 | arrayOfInt2[1] << 16 | arrayOfInt2[2] << 8 | arrayOfInt2[3];
/*     */     } 
/*  82 */     for (byte b1 = 0; b1 < 32; b1 += 4) {
/*  83 */       int i = arrayOfInt1[1] ^ arrayOfInt1[2] ^ arrayOfInt1[3] ^ paramArrayOfint[b1 + 0];
/*  84 */       i = ByteSub(i);
/*  85 */       arrayOfInt1[0] = arrayOfInt1[0] ^ L1(i);
/*     */       
/*  87 */       i = arrayOfInt1[2] ^ arrayOfInt1[3] ^ arrayOfInt1[0] ^ paramArrayOfint[b1 + 1];
/*  88 */       i = ByteSub(i);
/*  89 */       arrayOfInt1[1] = arrayOfInt1[1] ^ L1(i);
/*     */       
/*  91 */       i = arrayOfInt1[3] ^ arrayOfInt1[0] ^ arrayOfInt1[1] ^ paramArrayOfint[b1 + 2];
/*  92 */       i = ByteSub(i);
/*  93 */       arrayOfInt1[2] = arrayOfInt1[2] ^ L1(i);
/*     */       
/*  95 */       i = arrayOfInt1[0] ^ arrayOfInt1[1] ^ arrayOfInt1[2] ^ paramArrayOfint[b1 + 3];
/*  96 */       i = ByteSub(i);
/*  97 */       arrayOfInt1[3] = arrayOfInt1[3] ^ L1(i);
/*     */     } 
/*     */     
/* 100 */     for (b2 = 0; b2 < 16; b2 += 4) {
/* 101 */       paramArrayOfbyte2[b2] = (byte)(arrayOfInt1[3 - b2 / 4] >>> 24 & 0xFF);
/* 102 */       paramArrayOfbyte2[b2 + 1] = (byte)(arrayOfInt1[3 - b2 / 4] >>> 16 & 0xFF);
/* 103 */       paramArrayOfbyte2[b2 + 2] = (byte)(arrayOfInt1[3 - b2 / 4] >>> 8 & 0xFF);
/* 104 */       paramArrayOfbyte2[b2 + 3] = (byte)(arrayOfInt1[3 - b2 / 4] & 0xFF);
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   private static void SMS4KeyExt(byte[] paramArrayOfbyte, int[] paramArrayOfint, int paramInt) {
/* 110 */     int[] arrayOfInt1 = new int[4];
/* 111 */     int[] arrayOfInt2 = new int[4];
/* 112 */     for (byte b2 = 0; b2 < 4; b2++) {
/* 113 */       arrayOfInt2[0] = paramArrayOfbyte[0 + 4 * b2] & 0xFF;
/* 114 */       arrayOfInt2[1] = paramArrayOfbyte[1 + 4 * b2] & 0xFF;
/* 115 */       arrayOfInt2[2] = paramArrayOfbyte[2 + 4 * b2] & 0xFF;
/* 116 */       arrayOfInt2[3] = paramArrayOfbyte[3 + 4 * b2] & 0xFF;
/* 117 */       arrayOfInt1[b2] = arrayOfInt2[0] << 24 | arrayOfInt2[1] << 16 | arrayOfInt2[2] << 8 | arrayOfInt2[3];
/*     */     } 
/* 119 */     arrayOfInt1[0] = arrayOfInt1[0] ^ 0xA3B1BAC6;
/* 120 */     arrayOfInt1[1] = arrayOfInt1[1] ^ 0x56AA3350;
/* 121 */     arrayOfInt1[2] = arrayOfInt1[2] ^ 0x677D9197;
/* 122 */     arrayOfInt1[3] = arrayOfInt1[3] ^ 0xB27022DC; byte b1;
/* 123 */     for (b1 = 0; b1 < 32; b1 += 4) {
/* 124 */       int i = arrayOfInt1[1] ^ arrayOfInt1[2] ^ arrayOfInt1[3] ^ CK[b1 + 0];
/* 125 */       i = ByteSub(i);
/* 126 */       arrayOfInt1[0] = arrayOfInt1[0] ^ L2(i); paramArrayOfint[b1 + 0] = arrayOfInt1[0] ^ L2(i);
/*     */       
/* 128 */       i = arrayOfInt1[2] ^ arrayOfInt1[3] ^ arrayOfInt1[0] ^ CK[b1 + 1];
/* 129 */       i = ByteSub(i);
/* 130 */       arrayOfInt1[1] = arrayOfInt1[1] ^ L2(i); paramArrayOfint[b1 + 1] = arrayOfInt1[1] ^ L2(i);
/*     */       
/* 132 */       i = arrayOfInt1[3] ^ arrayOfInt1[0] ^ arrayOfInt1[1] ^ CK[b1 + 2];
/* 133 */       i = ByteSub(i);
/* 134 */       arrayOfInt1[2] = arrayOfInt1[2] ^ L2(i); paramArrayOfint[b1 + 2] = arrayOfInt1[2] ^ L2(i);
/*     */       
/* 136 */       i = arrayOfInt1[0] ^ arrayOfInt1[1] ^ arrayOfInt1[2] ^ CK[b1 + 3];
/* 137 */       i = ByteSub(i);
/* 138 */       arrayOfInt1[3] = arrayOfInt1[3] ^ L2(i); paramArrayOfint[b1 + 3] = arrayOfInt1[3] ^ L2(i);
/*     */     } 
/*     */     
/* 141 */     if (paramInt == 0) {
/* 142 */       for (b1 = 0; b1 < 16; b1++) {
/* 143 */         int i = paramArrayOfint[b1];
/* 144 */         paramArrayOfint[b1] = paramArrayOfint[31 - b1];
/* 145 */         paramArrayOfint[31 - b1] = i;
/*     */       } 
/*     */     }
/*     */   }
/*     */   
/*     */   private static int sms4(byte[] paramArrayOfbyte1, int paramInt1, byte[] paramArrayOfbyte2, byte[] paramArrayOfbyte3, int paramInt2) {
/* 151 */     byte b = 0;
/* 152 */     int[] arrayOfInt = new int[32];
/* 153 */     SMS4KeyExt(paramArrayOfbyte2, arrayOfInt, paramInt2);
/* 154 */     byte[] arrayOfByte1 = new byte[16];
/* 155 */     byte[] arrayOfByte2 = new byte[16];
/*     */     
/* 157 */     while (paramInt1 >= 16) {
/* 158 */       arrayOfByte1 = Arrays.copyOfRange(paramArrayOfbyte1, b, b + 16);
/* 159 */       SMS4Crypt(arrayOfByte1, arrayOfByte2, arrayOfInt);
/* 160 */       System.arraycopy(arrayOfByte2, 0, paramArrayOfbyte3, b, 16);
/* 161 */       paramInt1 -= 16;
/* 162 */       b += 16;
/*     */     } 
/*     */     
/* 165 */     return 0;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static byte[] encode16(byte[] paramArrayOfbyte1, byte[] paramArrayOfbyte2) {
/* 176 */     byte[] arrayOfByte = new byte[16];
/* 177 */     sms4(paramArrayOfbyte1, 16, paramArrayOfbyte2, arrayOfByte, 1);
/* 178 */     return arrayOfByte;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static byte[] decode16(byte[] paramArrayOfbyte1, byte[] paramArrayOfbyte2) {
/* 189 */     byte[] arrayOfByte = new byte[16];
/* 190 */     sms4(paramArrayOfbyte1, 16, paramArrayOfbyte2, arrayOfByte, 0);
/* 191 */     return arrayOfByte;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void encode(String paramString1, String paramString2) throws IOException {}
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static OutputStream encode(OutputStream paramOutputStream, byte[] paramArrayOfbyte) throws IOException {
/*     */     try {
/* 208 */       ByteArrayOutputStream byteArrayOutputStream1 = new ByteArrayOutputStream();
/* 209 */       byteArrayOutputStream1 = (ByteArrayOutputStream)paramOutputStream;
/* 210 */       byte[] arrayOfByte1 = byteArrayOutputStream1.toByteArray();
/* 211 */       List<byte[]> list = splitBytes(arrayOfByte1, 16);
/* 212 */       byte[] arrayOfByte2 = new byte[list.size() * 16];
/* 213 */       int i = 0;
/* 214 */       for (byte[] arrayOfByte3 : list) {
/* 215 */         byte[] arrayOfByte4 = encode16(arrayOfByte3, paramArrayOfbyte);
/* 216 */         System.arraycopy(arrayOfByte4, 0, arrayOfByte2, i, arrayOfByte4.length);
/* 217 */         i += arrayOfByte3.length;
/*     */       } 
/* 219 */       ByteArrayOutputStream byteArrayOutputStream2 = new ByteArrayOutputStream();
/* 220 */       byteArrayOutputStream2.write(arrayOfByte2);
/* 221 */       ByteArrayOutputStream byteArrayOutputStream3 = new ByteArrayOutputStream();
/* 222 */       byteArrayOutputStream1.writeTo(byteArrayOutputStream3);
/* 223 */       return byteArrayOutputStream3;
/* 224 */     } catch (Exception exception) {
/* 225 */       exception.printStackTrace();
/*     */       
/* 227 */       return null;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static InputStream encode(InputStream paramInputStream, byte[] paramArrayOfbyte) throws IOException {
/* 238 */     byte[] arrayOfByte1 = IOUtils.toByteArray(paramInputStream);
/* 239 */     List<byte[]> list = splitBytes(arrayOfByte1, 16);
/* 240 */     byte[] arrayOfByte2 = new byte[list.size() * 16];
/* 241 */     int i = 0;
/* 242 */     for (byte[] arrayOfByte3 : list) {
/* 243 */       byte[] arrayOfByte4 = encode16(arrayOfByte3, paramArrayOfbyte);
/* 244 */       System.arraycopy(arrayOfByte4, 0, arrayOfByte2, i, arrayOfByte4.length);
/* 245 */       i += arrayOfByte3.length;
/*     */     } 
/* 247 */     paramInputStream = new ByteArrayInputStream(arrayOfByte2);
/* 248 */     return paramInputStream;
/*     */   }
/*     */   
/*     */   public static byte[] encodeBytes(byte[] paramArrayOfbyte1, byte[] paramArrayOfbyte2) throws IOException {
/* 252 */     List<byte[]> list = splitBytes(paramArrayOfbyte1, 16);
/* 253 */     byte[] arrayOfByte = new byte[list.size() * 16];
/* 254 */     int i = 0;
/* 255 */     for (byte[] arrayOfByte1 : list) {
/* 256 */       byte[] arrayOfByte2 = encode16(arrayOfByte1, paramArrayOfbyte2);
/* 257 */       System.arraycopy(arrayOfByte2, 0, arrayOfByte, i, arrayOfByte2.length);
/* 258 */       i += arrayOfByte1.length;
/*     */     } 
/* 260 */     return arrayOfByte;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void decode(String paramString1, String paramString2, int paramInt) throws IOException {}
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static OutputStream decode(OutputStream paramOutputStream, byte[] paramArrayOfbyte, int paramInt) throws IOException {
/*     */     try {
/* 277 */       ByteArrayOutputStream byteArrayOutputStream1 = new ByteArrayOutputStream();
/* 278 */       byteArrayOutputStream1 = (ByteArrayOutputStream)paramOutputStream;
/* 279 */       byte[] arrayOfByte1 = byteArrayOutputStream1.toByteArray();
/* 280 */       List<byte[]> list = splitBytes(arrayOfByte1, 16);
/* 281 */       byte[] arrayOfByte2 = new byte[list.size() * 16];
/* 282 */       int i = 0;
/* 283 */       for (byte[] arrayOfByte4 : list) {
/* 284 */         byte[] arrayOfByte5 = decode16(arrayOfByte4, paramArrayOfbyte);
/* 285 */         System.arraycopy(arrayOfByte5, 0, arrayOfByte2, i, arrayOfByte5.length);
/* 286 */         i += arrayOfByte4.length;
/*     */       } 
/* 288 */       byte[] arrayOfByte3 = new byte[paramInt];
/* 289 */       System.arraycopy(arrayOfByte2, 0, arrayOfByte3, 0, paramInt);
/* 290 */       ByteArrayOutputStream byteArrayOutputStream2 = new ByteArrayOutputStream();
/* 291 */       byteArrayOutputStream2.write(arrayOfByte3);
/* 292 */       ByteArrayOutputStream byteArrayOutputStream3 = new ByteArrayOutputStream();
/* 293 */       byteArrayOutputStream1.writeTo(byteArrayOutputStream3);
/* 294 */       return byteArrayOutputStream3;
/* 295 */     } catch (Exception exception) {
/* 296 */       exception.printStackTrace();
/*     */       
/* 298 */       return null;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static InputStream decode(InputStream paramInputStream, byte[] paramArrayOfbyte, int paramInt) throws IOException {
/* 310 */     byte[] arrayOfByte1 = IOUtils.toByteArray(paramInputStream);
/* 311 */     List<byte[]> list = splitBytes(arrayOfByte1, 16);
/* 312 */     byte[] arrayOfByte2 = new byte[list.size() * 16];
/* 313 */     int i = 0;
/* 314 */     for (byte[] arrayOfByte4 : list) {
/* 315 */       byte[] arrayOfByte5 = decode16(arrayOfByte4, paramArrayOfbyte);
/* 316 */       System.arraycopy(arrayOfByte5, 0, arrayOfByte2, i, arrayOfByte5.length);
/* 317 */       i += arrayOfByte4.length;
/*     */     } 
/* 319 */     byte[] arrayOfByte3 = new byte[paramInt];
/* 320 */     System.arraycopy(arrayOfByte2, 0, arrayOfByte3, 0, paramInt);
/* 321 */     paramInputStream = new ByteArrayInputStream(arrayOfByte3);
/* 322 */     return paramInputStream;
/*     */   }
/*     */   
/*     */   private static List<byte[]> splitBytes(byte[] paramArrayOfbyte, int paramInt) {
/* 326 */     int i = (paramArrayOfbyte.length % paramInt == 0) ? (paramArrayOfbyte.length / paramInt) : (paramArrayOfbyte.length / paramInt + 1);
/* 327 */     ArrayList<byte[]> arrayList = new ArrayList();
/* 328 */     for (byte b = 0; b < i; b++) {
/* 329 */       int j = b * paramInt;
/* 330 */       if (j + paramInt > paramArrayOfbyte.length) {
/* 331 */         paramInt = paramArrayOfbyte.length % paramInt;
/*     */       }
/* 333 */       byte[] arrayOfByte = new byte[paramInt];
/* 334 */       System.arraycopy(paramArrayOfbyte, j, arrayOfByte, 0, paramInt);
/* 335 */       arrayList.add(arrayOfByte);
/*     */     } 
/* 337 */     return (List<byte[]>)arrayList;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/sm/TestSM4Utils.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */