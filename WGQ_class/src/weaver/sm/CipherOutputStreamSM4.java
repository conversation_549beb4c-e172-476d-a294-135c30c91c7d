/*     */ package weaver.sm;
/*     */ 
/*     */ import java.io.FilterOutputStream;
/*     */ import java.io.IOException;
/*     */ import java.io.OutputStream;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CipherOutputStreamSM4
/*     */   extends FilterOutputStream
/*     */ {
/*     */   private byte[] cipher;
/*     */   private OutputStream output;
/*  74 */   private byte[] ibuffer = new byte[512];
/*     */ 
/*     */   
/*     */   private byte[] obuffer;
/*     */ 
/*     */   
/*     */   private int mode;
/*     */   
/*  82 */   private long totalGroup = 0L;
/*     */   
/*  84 */   private long currentGroup = 0L;
/*     */   
/*  86 */   private long lastGroupSize = 0L;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public CipherOutputStreamSM4(OutputStream paramOutputStream, byte[] paramArrayOfbyte, int paramInt) {
/* 102 */     this(paramOutputStream, paramArrayOfbyte, paramInt, 0L);
/*     */   }
/*     */   
/*     */   public CipherOutputStreamSM4(OutputStream paramOutputStream, byte[] paramArrayOfbyte, int paramInt, long paramLong) {
/* 106 */     super(paramOutputStream);
/* 107 */     if (paramInt == 0) {
/*     */       try {
/* 109 */         this.totalGroup = paramLong / this.ibuffer.length;
/* 110 */         this.lastGroupSize = paramLong % this.ibuffer.length;
/*     */         
/* 112 */         this.totalGroup++;
/*     */       
/*     */       }
/* 115 */       catch (Exception exception) {
/* 116 */         exception.printStackTrace();
/*     */       } 
/*     */     }
/* 119 */     this.output = paramOutputStream;
/* 120 */     this.cipher = paramArrayOfbyte;
/* 121 */     this.mode = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private byte[] update(byte[] paramArrayOfbyte, int paramInt) {
/*     */     try {
/* 132 */       if (this.mode == 1) {
/* 133 */         this.obuffer = SM4Util.encode(paramArrayOfbyte, this.cipher);
/* 134 */       } else if (this.mode == 0) {
/*     */         
/* 136 */         this.obuffer = SM4Util.decode(paramArrayOfbyte, this.cipher, paramInt);
/*     */       } else {
/* 138 */         this.obuffer = null;
/*     */       } 
/* 140 */     } catch (Exception exception) {
/* 141 */       exception.printStackTrace();
/*     */     } 
/* 143 */     return this.obuffer;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void write(int paramInt) throws IOException {
/* 154 */     this.ibuffer[0] = (byte)paramInt;
/* 155 */     update(this.ibuffer, 1);
/* 156 */     if (this.obuffer != null) {
/* 157 */       this.output.write(this.obuffer);
/* 158 */       this.obuffer = null;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void write(byte[] paramArrayOfbyte) throws IOException {
/* 178 */     write(paramArrayOfbyte, 0, paramArrayOfbyte.length);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void write(byte[] paramArrayOfbyte, int paramInt1, int paramInt2) throws IOException {
/* 192 */     byte[] arrayOfByte = new byte[paramInt2];
/* 193 */     System.arraycopy(paramArrayOfbyte, 0, arrayOfByte, 0, paramInt2);
/* 194 */     this.currentGroup++;
/* 195 */     int i = paramInt2;
/* 196 */     if (this.currentGroup == this.totalGroup) {
/* 197 */       i = (int)this.lastGroupSize;
/*     */     }
/* 199 */     update(arrayOfByte, i);
/* 200 */     if (this.obuffer != null) {
/* 201 */       this.output.write(this.obuffer);
/* 202 */       this.obuffer = null;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void flush() throws IOException {
/* 221 */     if (this.obuffer != null) {
/* 222 */       this.output.write(this.obuffer);
/* 223 */       this.obuffer = null;
/*     */     } 
/* 225 */     this.output.flush();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void close() throws IOException {
/*     */     try {
/* 246 */       flush();
/* 247 */     } catch (IOException iOException) {}
/* 248 */     this.out.close();
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/sm/CipherOutputStreamSM4.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */