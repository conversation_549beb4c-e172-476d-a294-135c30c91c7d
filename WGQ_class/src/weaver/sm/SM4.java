/*     */ package weaver.sm;
/*     */ 
/*     */ import java.io.ByteArrayInputStream;
/*     */ import java.io.ByteArrayOutputStream;
/*     */ 
/*     */ 
/*     */ 
/*     */ public class SM4
/*     */ {
/*     */   public static final int SM4_ENCRYPT = 1;
/*     */   public static final int SM4_DECRYPT = 0;
/*     */   
/*     */   private long GET_ULONG_BE(byte[] paramArrayOfbyte, int paramInt) {
/*  14 */     return (paramArrayOfbyte[paramInt] & 0xFF) << 24L | ((paramArrayOfbyte[paramInt + 1] & 0xFF) << 16) | ((paramArrayOfbyte[paramInt + 2] & 0xFF) << 8) | (paramArrayOfbyte[paramInt + 3] & 0xFF) & 0xFFFFFFFFL;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   private void PUT_ULONG_BE(long paramLong, byte[] paramArrayOfbyte, int paramInt) {
/*  20 */     paramArrayOfbyte[paramInt] = (byte)(int)(0xFFL & paramLong >> 24L);
/*  21 */     paramArrayOfbyte[paramInt + 1] = (byte)(int)(0xFFL & paramLong >> 16L);
/*  22 */     paramArrayOfbyte[paramInt + 2] = (byte)(int)(0xFFL & paramLong >> 8L);
/*  23 */     paramArrayOfbyte[paramInt + 3] = (byte)(int)(0xFFL & paramLong);
/*     */   }
/*     */ 
/*     */   
/*     */   private long SHL(long paramLong, int paramInt) {
/*  28 */     return (paramLong & 0xFFFFFFFFFFFFFFFFL) << paramInt;
/*     */   }
/*     */ 
/*     */   
/*     */   private long ROTL(long paramLong, int paramInt) {
/*  33 */     return SHL(paramLong, paramInt) | paramLong >> 32 - paramInt;
/*     */   }
/*     */ 
/*     */   
/*     */   private void SWAP(long[] paramArrayOflong, int paramInt) {
/*  38 */     long l = paramArrayOflong[paramInt];
/*  39 */     paramArrayOflong[paramInt] = paramArrayOflong[31 - paramInt];
/*  40 */     paramArrayOflong[31 - paramInt] = l;
/*     */   }
/*     */   
/*  43 */   public static final byte[] SboxTable = new byte[] { -42, -112, -23, -2, -52, -31, 61, -73, 22, -74, 20, -62, 40, -5, 44, 5, 43, 103, -102, 118, 42, -66, 4, -61, -86, 68, 19, 38, 73, -122, 6, -103, -100, 66, 80, -12, -111, -17, -104, 122, 51, 84, 11, 67, -19, -49, -84, 98, -28, -77, 28, -87, -55, 8, -24, -107, Byte.MIN_VALUE, -33, -108, -6, 117, -113, 63, -90, 71, 7, -89, -4, -13, 115, 23, -70, -125, 89, 60, 25, -26, -123, 79, -88, 104, 107, -127, -78, 113, 100, -38, -117, -8, -21, 15, 75, 112, 86, -99, 53, 30, 36, 14, 94, 99, 88, -47, -94, 37, 34, 124, 59, 1, 33, 120, -121, -44, 0, 70, 87, -97, -45, 39, 82, 76, 54, 2, -25, -96, -60, -56, -98, -22, -65, -118, -46, 64, -57, 56, -75, -93, -9, -14, -50, -7, 97, 21, -95, -32, -82, 93, -92, -101, 52, 26, 85, -83, -109, 50, 48, -11, -116, -79, -29, 29, -10, -30, 46, -126, 102, -54, 96, -64, 41, 35, -85, 13, 83, 78, 111, -43, -37, 55, 69, -34, -3, -114, 47, 3, -1, 106, 114, 109, 108, 91, 81, -115, 27, -81, -110, -69, -35, -68, Byte.MAX_VALUE, 17, -39, 92, 65, 31, 16, 90, -40, 10, -63, 49, -120, -91, -51, 123, -67, 45, 116, -48, 18, -72, -27, -76, -80, -119, 105, -105, 74, 12, -106, 119, 126, 101, -71, -15, 9, -59, 110, -58, -124, 24, -16, 125, -20, 58, -36, 77, 32, 121, -18, 95, 62, -41, -53, 57, 72 };
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  83 */   public static final int[] FK = new int[] { -1548633402, 1453994832, 1736282519, -1301273892 };
/*     */   
/*  85 */   public static final int[] CK = new int[] { 462357, 472066609, 943670861, 1415275113, 1886879365, -1936483679, -1464879427, -993275175, -521670923, -66909679, 404694573, 876298825, 1347903077, 1819507329, -2003855715, -1532251463, -1060647211, -589042959, -117504499, 337322537, 808926789, 1280531041, 1752135293, -2071227751, -1599623499, -1128019247, -656414995, -184876535, 269950501, 741554753, 1213159005, 1684763257 };
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private byte sm4Sbox(byte paramByte) {
/*  96 */     int i = paramByte & 0xFF;
/*  97 */     return SboxTable[i];
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   private long sm4Lt(long paramLong) {
/* 103 */     long l1 = 0L;
/* 104 */     long l2 = 0L;
/* 105 */     byte[] arrayOfByte1 = new byte[4];
/* 106 */     byte[] arrayOfByte2 = new byte[4];
/* 107 */     PUT_ULONG_BE(paramLong, arrayOfByte1, 0);
/* 108 */     arrayOfByte2[0] = sm4Sbox(arrayOfByte1[0]);
/* 109 */     arrayOfByte2[1] = sm4Sbox(arrayOfByte1[1]);
/* 110 */     arrayOfByte2[2] = sm4Sbox(arrayOfByte1[2]);
/* 111 */     arrayOfByte2[3] = sm4Sbox(arrayOfByte1[3]);
/* 112 */     l1 = GET_ULONG_BE(arrayOfByte2, 0);
/* 113 */     l2 = l1 ^ ROTL(l1, 2) ^ ROTL(l1, 10) ^ ROTL(l1, 18) ^ ROTL(l1, 24);
/* 114 */     return l2;
/*     */   }
/*     */ 
/*     */   
/*     */   private long sm4F(long paramLong1, long paramLong2, long paramLong3, long paramLong4, long paramLong5) {
/* 119 */     return paramLong1 ^ sm4Lt(paramLong2 ^ paramLong3 ^ paramLong4 ^ paramLong5);
/*     */   }
/*     */ 
/*     */   
/*     */   private long sm4CalciRK(long paramLong) {
/* 124 */     long l1 = 0L;
/* 125 */     long l2 = 0L;
/* 126 */     byte[] arrayOfByte1 = new byte[4];
/* 127 */     byte[] arrayOfByte2 = new byte[4];
/* 128 */     PUT_ULONG_BE(paramLong, arrayOfByte1, 0);
/* 129 */     arrayOfByte2[0] = sm4Sbox(arrayOfByte1[0]);
/* 130 */     arrayOfByte2[1] = sm4Sbox(arrayOfByte1[1]);
/* 131 */     arrayOfByte2[2] = sm4Sbox(arrayOfByte1[2]);
/* 132 */     arrayOfByte2[3] = sm4Sbox(arrayOfByte1[3]);
/* 133 */     l1 = GET_ULONG_BE(arrayOfByte2, 0);
/* 134 */     l2 = l1 ^ ROTL(l1, 13) ^ ROTL(l1, 23);
/* 135 */     return l2;
/*     */   }
/*     */ 
/*     */   
/*     */   private void sm4_setkey(long[] paramArrayOflong, byte[] paramArrayOfbyte) {
/* 140 */     long[] arrayOfLong1 = new long[4];
/* 141 */     long[] arrayOfLong2 = new long[36];
/* 142 */     byte b = 0;
/* 143 */     arrayOfLong1[0] = GET_ULONG_BE(paramArrayOfbyte, 0);
/* 144 */     arrayOfLong1[1] = GET_ULONG_BE(paramArrayOfbyte, 4);
/* 145 */     arrayOfLong1[2] = GET_ULONG_BE(paramArrayOfbyte, 8);
/* 146 */     arrayOfLong1[3] = GET_ULONG_BE(paramArrayOfbyte, 12);
/* 147 */     arrayOfLong2[0] = arrayOfLong1[0] ^ FK[0];
/* 148 */     arrayOfLong2[1] = arrayOfLong1[1] ^ FK[1];
/* 149 */     arrayOfLong2[2] = arrayOfLong1[2] ^ FK[2];
/* 150 */     arrayOfLong2[3] = arrayOfLong1[3] ^ FK[3];
/* 151 */     for (; b < 32; b++) {
/*     */       
/* 153 */       arrayOfLong2[b + 4] = arrayOfLong2[b] ^ sm4CalciRK(arrayOfLong2[b + 1] ^ arrayOfLong2[b + 2] ^ arrayOfLong2[b + 3] ^ CK[b]);
/* 154 */       paramArrayOflong[b] = arrayOfLong2[b + 4];
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   private void sm4_one_round(long[] paramArrayOflong, byte[] paramArrayOfbyte1, byte[] paramArrayOfbyte2) {
/* 160 */     byte b = 0;
/* 161 */     long[] arrayOfLong = new long[36];
/* 162 */     arrayOfLong[0] = GET_ULONG_BE(paramArrayOfbyte1, 0);
/* 163 */     arrayOfLong[1] = GET_ULONG_BE(paramArrayOfbyte1, 4);
/* 164 */     arrayOfLong[2] = GET_ULONG_BE(paramArrayOfbyte1, 8);
/* 165 */     arrayOfLong[3] = GET_ULONG_BE(paramArrayOfbyte1, 12);
/* 166 */     while (b < 32) {
/*     */       
/* 168 */       arrayOfLong[b + 4] = sm4F(arrayOfLong[b], arrayOfLong[b + 1], arrayOfLong[b + 2], arrayOfLong[b + 3], paramArrayOflong[b]);
/* 169 */       b++;
/*     */     } 
/* 171 */     PUT_ULONG_BE(arrayOfLong[35], paramArrayOfbyte2, 0);
/* 172 */     PUT_ULONG_BE(arrayOfLong[34], paramArrayOfbyte2, 4);
/* 173 */     PUT_ULONG_BE(arrayOfLong[33], paramArrayOfbyte2, 8);
/* 174 */     PUT_ULONG_BE(arrayOfLong[32], paramArrayOfbyte2, 12);
/*     */   }
/*     */ 
/*     */   
/*     */   private byte[] padding(byte[] paramArrayOfbyte, int paramInt) {
/* 179 */     if (paramArrayOfbyte == null)
/*     */     {
/* 181 */       return null;
/*     */     }
/*     */     
/* 184 */     byte[] arrayOfByte = (byte[])null;
/* 185 */     if (paramInt == 1) {
/*     */       
/* 187 */       int i = 16 - paramArrayOfbyte.length % 16;
/* 188 */       arrayOfByte = new byte[paramArrayOfbyte.length + i];
/* 189 */       System.arraycopy(paramArrayOfbyte, 0, arrayOfByte, 0, paramArrayOfbyte.length);
/* 190 */       for (byte b = 0; b < i; b++)
/*     */       {
/* 192 */         arrayOfByte[paramArrayOfbyte.length + b] = (byte)i;
/*     */       }
/*     */     }
/*     */     else {
/*     */       
/* 197 */       byte b = paramArrayOfbyte[paramArrayOfbyte.length - 1];
/* 198 */       arrayOfByte = new byte[paramArrayOfbyte.length - b];
/* 199 */       System.arraycopy(paramArrayOfbyte, 0, arrayOfByte, 0, paramArrayOfbyte.length - b);
/*     */     } 
/* 201 */     return arrayOfByte;
/*     */   }
/*     */ 
/*     */   
/*     */   public void sm4_setkey_enc(SM4_Context paramSM4_Context, byte[] paramArrayOfbyte) throws Exception {
/* 206 */     if (paramSM4_Context == null)
/*     */     {
/* 208 */       throw new Exception("ctx is null!");
/*     */     }
/*     */     
/* 211 */     if (paramArrayOfbyte == null)
/*     */     {
/* 213 */       throw new Exception("key error!");
/*     */     }
/*     */     
/* 216 */     paramSM4_Context.mode = 1;
/* 217 */     sm4_setkey(paramSM4_Context.sk, paramArrayOfbyte);
/*     */   }
/*     */ 
/*     */   
/*     */   public void sm4_setkey_dec(SM4_Context paramSM4_Context, byte[] paramArrayOfbyte) throws Exception {
/* 222 */     if (paramSM4_Context == null)
/*     */     {
/* 224 */       throw new Exception("ctx is null!");
/*     */     }
/*     */     
/* 227 */     if (paramArrayOfbyte == null)
/*     */     {
/* 229 */       throw new Exception("key error!");
/*     */     }
/*     */     
/* 232 */     byte b = 0;
/* 233 */     paramSM4_Context.mode = 0;
/* 234 */     sm4_setkey(paramSM4_Context.sk, paramArrayOfbyte);
/* 235 */     for (b = 0; b < 16; b++)
/*     */     {
/* 237 */       SWAP(paramSM4_Context.sk, b);
/*     */     }
/*     */   }
/*     */ 
/*     */   
/*     */   public byte[] sm4_crypt_ecb(SM4_Context paramSM4_Context, byte[] paramArrayOfbyte) throws Exception {
/* 243 */     if (paramArrayOfbyte == null)
/*     */     {
/* 245 */       throw new Exception("input is null!");
/*     */     }
/*     */     
/* 248 */     if (paramSM4_Context.isPadding && paramSM4_Context.mode == 1)
/*     */     {
/* 250 */       paramArrayOfbyte = padding(paramArrayOfbyte, 1);
/*     */     }
/*     */     
/* 253 */     int i = paramArrayOfbyte.length;
/* 254 */     byte[] arrayOfByte = null;
/* 255 */     ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(paramArrayOfbyte);
/* 256 */     ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
/*     */     try {
/* 258 */       for (; i > 0; i -= 16) {
/*     */         
/* 260 */         byte[] arrayOfByte1 = new byte[16];
/* 261 */         byte[] arrayOfByte2 = new byte[16];
/* 262 */         byteArrayInputStream.read(arrayOfByte1);
/* 263 */         sm4_one_round(paramSM4_Context.sk, arrayOfByte1, arrayOfByte2);
/* 264 */         byteArrayOutputStream.write(arrayOfByte2);
/*     */       } 
/*     */       
/* 267 */       arrayOfByte = byteArrayOutputStream.toByteArray();
/* 268 */       if (paramSM4_Context.isPadding && paramSM4_Context.mode == 0)
/*     */       {
/* 270 */         arrayOfByte = padding(arrayOfByte, 0);
/*     */       }
/*     */     } finally {
/* 273 */       byteArrayInputStream.close();
/* 274 */       byteArrayOutputStream.close();
/*     */     } 
/* 276 */     return arrayOfByte;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/sm/SM4.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */