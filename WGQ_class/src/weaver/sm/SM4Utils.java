/*     */ package weaver.sm;
/*     */ 
/*     */ import java.io.File;
/*     */ import java.io.FileInputStream;
/*     */ import java.io.IOException;
/*     */ import java.io.InputStream;
/*     */ import java.io.OutputStream;
/*     */ import java.security.MessageDigest;
/*     */ import java.util.UUID;
/*     */ import java.util.regex.Matcher;
/*     */ import java.util.regex.Pattern;
/*     */ import java.util.zip.CRC32;
/*     */ import sun.misc.BASE64Decoder;
/*     */ import sun.misc.BASE64Encoder;
/*     */ import weaver.encrypt.CustomAlgorithmInterface;
/*     */ import weaver.file.AESCoder;
/*     */ import weaver.general.BaseBean;
/*     */ 
/*     */ 
/*     */ public class SM4Utils
/*     */   extends BaseBean
/*     */   implements CustomAlgorithmInterface
/*     */ {
/*     */   private boolean hexString = false;
/*  25 */   private static String ENC_MODE = "AES";
/*     */   
/*     */   public static final String ENC_AES = "AES";
/*     */   
/*     */   public static final String ENC_SM4 = "SM4";
/*     */   
/*     */   static {
/*  32 */     BaseBean baseBean = new BaseBean();
/*  33 */     ENC_MODE = baseBean.getPropValue("weaver_security_type", "reversible_enc_type");
/*  34 */     if (ENC_MODE == null || !"SM4".equalsIgnoreCase(ENC_MODE)) {
/*  35 */       ENC_MODE = "AES";
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public InputStream encrypt(InputStream paramInputStream, String paramString) throws Exception {
/*  57 */     return encrypt(paramInputStream, paramString, -1L);
/*     */   }
/*     */ 
/*     */   
/*     */   public InputStream encrypt(InputStream paramInputStream, String paramString, long paramLong) throws Exception {
/*     */     byte[] arrayOfByte;
/*  63 */     if (!ENC_MODE.equalsIgnoreCase("SM4")) {
/*  64 */       return AESCoder.encrypt(paramInputStream, paramString);
/*     */     }
/*     */ 
/*     */     
/*  68 */     if (this.hexString) {
/*     */       
/*  70 */       arrayOfByte = Util.hexStringToBytes(paramString);
/*     */     }
/*     */     else {
/*     */       
/*  74 */       arrayOfByte = paramString.getBytes();
/*     */     } 
/*     */     
/*  77 */     return new CipherInputStreamSM4(paramInputStream, arrayOfByte, 1, paramLong);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public OutputStream encrypt(OutputStream paramOutputStream, String paramString) throws Exception {
/*  95 */     return encrypt(paramOutputStream, paramString, (String)null);
/*     */   }
/*     */   public OutputStream encrypt(OutputStream paramOutputStream, String paramString1, String paramString2) throws Exception {
/*     */     byte[] arrayOfByte;
/*  99 */     if (!"SM4".equalsIgnoreCase(paramString2) && (
/* 100 */       !ENC_MODE.equalsIgnoreCase("SM4") || "AES".equalsIgnoreCase(paramString2))) {
/* 101 */       return AESCoder.encrypt(paramOutputStream, paramString1);
/*     */     }
/*     */ 
/*     */ 
/*     */     
/* 106 */     if (this.hexString) {
/*     */       
/* 108 */       arrayOfByte = Util.hexStringToBytes(paramString1);
/*     */     }
/*     */     else {
/*     */       
/* 112 */       arrayOfByte = paramString1.getBytes();
/*     */     } 
/*     */ 
/*     */     
/* 116 */     return new CipherOutputStreamSM4(paramOutputStream, arrayOfByte, 1);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public InputStream decrypt(InputStream paramInputStream, String paramString, long paramLong) throws Exception {
/* 134 */     return decrypt(paramInputStream, paramString, paramLong, (String)null);
/*     */   }
/*     */ 
/*     */   
/*     */   public InputStream decrypt(InputStream paramInputStream, String paramString1, long paramLong, String paramString2) throws Exception {
/*     */     byte[] arrayOfByte;
/* 140 */     if (!"SM4".equalsIgnoreCase(paramString2) && (
/* 141 */       !ENC_MODE.equalsIgnoreCase("SM4") || "AES".equalsIgnoreCase(paramString2))) {
/* 142 */       return AESCoder.decrypt(paramInputStream, paramString1);
/*     */     }
/*     */ 
/*     */ 
/*     */     
/* 147 */     if (this.hexString) {
/*     */       
/* 149 */       arrayOfByte = Util.hexStringToBytes(paramString1);
/*     */     }
/*     */     else {
/*     */       
/* 153 */       arrayOfByte = paramString1.getBytes();
/*     */     } 
/*     */     
/* 156 */     return new CipherInputStreamSM4(paramInputStream, arrayOfByte, 0, paramLong);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public OutputStream decrypt(OutputStream paramOutputStream, String paramString, long paramLong) throws Exception {
/* 175 */     return decrypt(paramOutputStream, paramString, paramLong, (String)null);
/*     */   }
/*     */ 
/*     */   
/*     */   public OutputStream decrypt(OutputStream paramOutputStream, String paramString1, long paramLong, String paramString2) throws Exception {
/*     */     byte[] arrayOfByte;
/* 181 */     if (!"SM4".equalsIgnoreCase(paramString2) && (
/* 182 */       !ENC_MODE.equalsIgnoreCase("SM4") || "AES".equalsIgnoreCase(paramString2))) {
/* 183 */       return AESCoder.decrypt(paramOutputStream, paramString1);
/*     */     }
/*     */ 
/*     */ 
/*     */     
/* 188 */     if (this.hexString) {
/*     */       
/* 190 */       arrayOfByte = Util.hexStringToBytes(paramString1);
/*     */     }
/*     */     else {
/*     */       
/* 194 */       arrayOfByte = paramString1.getBytes();
/*     */     } 
/*     */     
/* 197 */     return new CipherOutputStreamSM4(paramOutputStream, arrayOfByte, 0, paramLong);
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public String encrypt(String paramString1, String paramString2) {
/* 203 */     return encrypt(paramString1, paramString2, (String)null);
/*     */   }
/*     */ 
/*     */   
/*     */   public String encrypt(String paramString1, String paramString2, String paramString3) {
/*     */     try {
/*     */       byte[] arrayOfByte1;
/* 210 */       if (!"SM4".equalsIgnoreCase(paramString3) && (
/* 211 */         !ENC_MODE.equalsIgnoreCase("SM4") || "AES".equalsIgnoreCase(paramString3))) {
/* 212 */         return AESCoder.encrypt(paramString1, paramString2);
/*     */       }
/*     */ 
/*     */       
/* 216 */       SM4_Context sM4_Context = new SM4_Context();
/* 217 */       sM4_Context.isPadding = true;
/* 218 */       sM4_Context.mode = 1;
/*     */ 
/*     */       
/* 221 */       if (this.hexString) {
/*     */         
/* 223 */         arrayOfByte1 = Util.hexStringToBytes(paramString2);
/*     */       }
/*     */       else {
/*     */         
/* 227 */         arrayOfByte1 = paramString2.getBytes();
/*     */       } 
/*     */       
/* 230 */       SM4 sM4 = new SM4();
/* 231 */       sM4.sm4_setkey_enc(sM4_Context, arrayOfByte1);
/* 232 */       byte[] arrayOfByte2 = sM4.sm4_crypt_ecb(sM4_Context, paramString1.getBytes("UTF-8"));
/* 233 */       String str = (new BASE64Encoder()).encode(arrayOfByte2);
/* 234 */       if (str != null && str.trim().length() > 0) {
/*     */         
/* 236 */         Pattern pattern = Pattern.compile("\\s*|\t|\r|\n");
/* 237 */         Matcher matcher = pattern.matcher(str);
/* 238 */         str = matcher.replaceAll("");
/*     */       } 
/* 240 */       return str;
/*     */     }
/* 242 */     catch (Exception exception) {
/*     */       
/* 244 */       (new BaseBean()).writeLog(exception);
/* 245 */       exception.printStackTrace();
/* 246 */       return null;
/*     */     } 
/*     */   }
/*     */   
/*     */   public String decrypt(String paramString1, String paramString2) {
/* 251 */     return decrypt(paramString1, paramString2, (String)null);
/*     */   }
/*     */ 
/*     */   
/*     */   public String decrypt(String paramString1, String paramString2, String paramString3) {
/*     */     try {
/*     */       byte[] arrayOfByte1;
/* 258 */       if (!"SM4".equalsIgnoreCase(paramString3) && (
/* 259 */         !ENC_MODE.equalsIgnoreCase("SM4") || "AES".equalsIgnoreCase(paramString3))) {
/* 260 */         return AESCoder.decrypt(paramString1, paramString2);
/*     */       }
/*     */ 
/*     */       
/* 264 */       SM4_Context sM4_Context = new SM4_Context();
/* 265 */       sM4_Context.isPadding = true;
/* 266 */       sM4_Context.mode = 0;
/*     */ 
/*     */       
/* 269 */       if (this.hexString) {
/*     */         
/* 271 */         arrayOfByte1 = Util.hexStringToBytes(paramString2);
/*     */       }
/*     */       else {
/*     */         
/* 275 */         arrayOfByte1 = paramString2.getBytes();
/*     */       } 
/*     */       
/* 278 */       SM4 sM4 = new SM4();
/* 279 */       sM4.sm4_setkey_dec(sM4_Context, arrayOfByte1);
/* 280 */       byte[] arrayOfByte2 = sM4.sm4_crypt_ecb(sM4_Context, (new BASE64Decoder()).decodeBuffer(paramString1));
/* 281 */       return new String(arrayOfByte2, "UTF-8");
/*     */     }
/* 283 */     catch (Exception exception) {
/*     */       
/* 285 */       exception.printStackTrace();
/* 286 */       return null;
/*     */     } 
/*     */   }
/*     */   
/*     */   public String createSecretKey() {
/* 291 */     return UUID.randomUUID().toString().substring(0, 16);
/*     */   }
/*     */   
/*     */   public String getENC_MODE() {
/* 295 */     return ENC_MODE;
/*     */   }
/*     */   public byte[] encodeBytes(byte[] paramArrayOfbyte1, byte[] paramArrayOfbyte2) throws IOException {
/* 298 */     return SM4Util.encodeBytes(paramArrayOfbyte1, paramArrayOfbyte2);
/*     */   }
/*     */   public byte[] getSMCode(String paramString) {
/* 301 */     return SM4Util.getSMCode(paramString);
/*     */   }
/*     */   
/*     */   public static void main(String[] paramArrayOfString) throws Exception {
/* 305 */     SM4Utils sM4Utils = new SM4Utils();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String hash(String paramString) {
/* 384 */     if (paramString == null) return null; 
/*     */     try {
/* 386 */       CRC32 cRC32 = new CRC32();
/* 387 */       cRC32.update(paramString.getBytes("UTF-8"));
/* 388 */       return Long.toHexString(cRC32.getValue());
/*     */     }
/* 390 */     catch (Exception exception) {
/* 391 */       writeLog(exception);
/* 392 */       return null;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public String hash(InputStream paramInputStream) {
/* 399 */     String str = null;
/*     */     try {
/* 401 */       MessageDigest messageDigest = MessageDigest.getInstance("SHA-256");
/* 402 */       byte[] arrayOfByte1 = new byte[1024];
/* 403 */       int i = -1;
/* 404 */       while ((i = paramInputStream.read(arrayOfByte1, 0, 1024)) != -1) {
/* 405 */         messageDigest.update(arrayOfByte1, 0, i);
/*     */       }
/* 407 */       byte[] arrayOfByte2 = messageDigest.digest();
/* 408 */       str = byte2hexLower(arrayOfByte2);
/* 409 */     } catch (Exception exception) {
/* 410 */       writeLog(exception);
/*     */     } 
/* 412 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public String hash(File paramFile) {
/* 418 */     FileInputStream fileInputStream = null;
/*     */     try {
/* 420 */       fileInputStream = new FileInputStream(paramFile);
/* 421 */       return hash(fileInputStream);
/* 422 */     } catch (Exception exception) {
/* 423 */       writeLog(exception);
/* 424 */       return null;
/*     */     } finally {
/*     */       try {
/* 427 */         if (fileInputStream != null) {
/* 428 */           fileInputStream.close();
/*     */         }
/* 430 */       } catch (IOException iOException) {
/* 431 */         writeLog(iOException);
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public String sign(String paramString) {
/* 439 */     return hash(paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean verifyHash(String paramString1, String paramString2) {
/* 445 */     if (paramString1 == null) return true; 
/* 446 */     if (paramString2 == null) return false; 
/* 447 */     String str = hash(paramString1);
/* 448 */     return paramString2.equals(str);
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean verifyHash(InputStream paramInputStream, String paramString) {
/* 454 */     if (paramString == null) return false; 
/* 455 */     String str = hash(paramInputStream);
/* 456 */     return paramString.equals(str);
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean verifyHash(File paramFile, String paramString) {
/* 462 */     if (paramString == null) return false; 
/* 463 */     String str = hash(paramFile);
/* 464 */     return paramString.equals(str);
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean verifySign(String paramString1, String paramString2) {
/* 470 */     return verifyHash(paramString1, paramString2);
/*     */   }
/*     */ 
/*     */   
/*     */   public String hashAndSign(String paramString) {
/* 475 */     return sign(hash(paramString));
/*     */   }
/*     */ 
/*     */   
/*     */   public boolean verifyHashAndSign(String paramString1, String paramString2) {
/* 480 */     return verifySign(hash(paramString1), paramString2);
/*     */   }
/*     */ 
/*     */   
/*     */   public String signFile(InputStream paramInputStream) {
/* 485 */     return sign(hash(paramInputStream));
/*     */   }
/*     */ 
/*     */   
/*     */   public String signFile(File paramFile) {
/* 490 */     return sign(hash(paramFile));
/*     */   }
/*     */ 
/*     */   
/*     */   public boolean verifySignFile(InputStream paramInputStream, String paramString) {
/* 495 */     return verifySign(hash(paramInputStream), paramString);
/*     */   }
/*     */ 
/*     */   
/*     */   public boolean verifySignFile(File paramFile, String paramString) {
/* 500 */     return verifySign(hash(paramFile), paramString);
/*     */   }
/*     */   
/*     */   private static String byte2hexLower(byte[] paramArrayOfbyte) {
/* 504 */     String str1 = "";
/* 505 */     String str2 = "";
/* 506 */     for (byte b = 0; b < paramArrayOfbyte.length; b++) {
/* 507 */       str2 = Integer.toHexString(paramArrayOfbyte[b] & 0xFF);
/* 508 */       if (str2.length() == 1) {
/* 509 */         str1 = str1 + "0" + str2;
/*     */       } else {
/* 511 */         str1 = str1 + str2;
/*     */       } 
/*     */     } 
/* 514 */     return str1;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/sm/SM4Utils.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */