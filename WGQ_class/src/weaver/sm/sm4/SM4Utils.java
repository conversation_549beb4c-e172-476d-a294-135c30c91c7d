/*     */ package weaver.sm.sm4;
/*     */ 
/*     */ import java.util.regex.Matcher;
/*     */ import java.util.regex.Pattern;
/*     */ import org.bouncycastle.util.encoders.Base64;
/*     */ import org.bouncycastle.util.encoders.Hex;
/*     */ 
/*     */ 
/*     */ 
/*     */ public class SM4Utils
/*     */ {
/*  12 */   private static String iv = "1234567890123456";
/*     */   
/*  14 */   private static String UTF_8 = "UTF-8";
/*     */ 
/*     */ 
/*     */   
/*     */   private static final boolean hexString = false;
/*     */ 
/*     */ 
/*     */   
/*     */   public static String encryptData_ECB(String paramString1, String paramString2) {
/*     */     try {
/*  24 */       SM4_Context sM4_Context = new SM4_Context();
/*  25 */       sM4_Context.isPadding = true;
/*  26 */       sM4_Context.mode = 1;
/*     */ 
/*     */       
/*  29 */       byte[] arrayOfByte1 = paramString2.getBytes();
/*  30 */       SM4 sM4 = new SM4();
/*  31 */       sM4.sm4_setkey_enc(sM4_Context, arrayOfByte1);
/*  32 */       byte[] arrayOfByte2 = sM4.sm4_crypt_ecb(sM4_Context, paramString1.getBytes(UTF_8));
/*  33 */       String str = new String(Base64.encode(arrayOfByte2));
/*  34 */       if (str != null && str.trim().length() > 0) {
/*  35 */         Pattern pattern = Pattern.compile("\\s*|\t|\r|\n");
/*  36 */         Matcher matcher = pattern.matcher(str);
/*  37 */         str = matcher.replaceAll("");
/*     */       } 
/*  39 */       return str;
/*  40 */     } catch (Exception exception) {
/*  41 */       exception.printStackTrace();
/*  42 */       return null;
/*     */     } 
/*     */   }
/*     */   
/*     */   public static String decryptData_ECB(String paramString1, String paramString2) {
/*     */     try {
/*  48 */       SM4_Context sM4_Context = new SM4_Context();
/*  49 */       sM4_Context.isPadding = true;
/*  50 */       sM4_Context.mode = 0;
/*     */ 
/*     */       
/*  53 */       byte[] arrayOfByte1 = paramString2.getBytes();
/*  54 */       SM4 sM4 = new SM4();
/*  55 */       sM4.sm4_setkey_dec(sM4_Context, arrayOfByte1);
/*  56 */       byte[] arrayOfByte2 = sM4.sm4_crypt_ecb(sM4_Context, Base64.decode(paramString1));
/*  57 */       return new String(arrayOfByte2, UTF_8);
/*  58 */     } catch (Exception exception) {
/*  59 */       exception.printStackTrace();
/*  60 */       return null;
/*     */     } 
/*     */   }
/*     */   
/*     */   public static String encryptData_CBC(String paramString1, String paramString2) {
/*     */     try {
/*  66 */       SM4_Context sM4_Context = new SM4_Context();
/*  67 */       sM4_Context.isPadding = true;
/*  68 */       sM4_Context.mode = 1;
/*     */ 
/*     */ 
/*     */ 
/*     */       
/*  73 */       byte[] arrayOfByte1 = paramString2.getBytes();
/*  74 */       byte[] arrayOfByte2 = iv.getBytes();
/*     */       
/*  76 */       SM4 sM4 = new SM4();
/*  77 */       sM4.sm4_setkey_enc(sM4_Context, arrayOfByte1);
/*  78 */       byte[] arrayOfByte3 = sM4.sm4_crypt_cbc(sM4_Context, arrayOfByte2, paramString1.getBytes(UTF_8));
/*  79 */       String str = new String(Base64.encode(arrayOfByte3));
/*  80 */       if (str != null && str.trim().length() > 0) {
/*  81 */         Pattern pattern = Pattern.compile("\\s*|\t|\r|\n");
/*  82 */         Matcher matcher = pattern.matcher(str);
/*  83 */         str = matcher.replaceAll("");
/*     */       } 
/*  85 */       return str;
/*  86 */     } catch (Exception exception) {
/*  87 */       exception.printStackTrace();
/*  88 */       return null;
/*     */     } 
/*     */   }
/*     */   
/*     */   public static String decryptData_CBC(String paramString1, String paramString2) {
/*     */     try {
/*  94 */       SM4_Context sM4_Context = new SM4_Context();
/*  95 */       sM4_Context.isPadding = true;
/*  96 */       sM4_Context.mode = 0;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 104 */       byte[] arrayOfByte1 = paramString2.getBytes();
/* 105 */       byte[] arrayOfByte2 = iv.getBytes();
/*     */ 
/*     */       
/* 108 */       SM4 sM4 = new SM4();
/* 109 */       sM4.sm4_setkey_dec(sM4_Context, arrayOfByte1);
/* 110 */       byte[] arrayOfByte3 = sM4.sm4_crypt_cbc(sM4_Context, arrayOfByte2, Base64.decode(paramString1));
/* 111 */       return new String(arrayOfByte3, UTF_8);
/* 112 */     } catch (Exception exception) {
/* 113 */       exception.printStackTrace();
/* 114 */       return null;
/*     */     } 
/*     */   }
/*     */   
/*     */   public static void main(String[] paramArrayOfString) {
/* 119 */     String str1 = "aaa";
/* 120 */     String str2 = "ab207984-bdaf-48";
/* 121 */     String str3 = encryptData_ECB(str1, str2);
/* 122 */     System.out.println("ECB模式加密密文: " + str3);
/*     */     
/* 124 */     str1 = decryptData_ECB(str3, str2);
/* 125 */     System.out.println("ECB模式解密明文: " + str1);
/*     */     
/* 127 */     str3 = encryptData_CBC(str1, str2);
/* 128 */     System.out.println("CBC模式加密密文: " + str3);
/*     */     
/* 130 */     str1 = decryptData_CBC(str3, str2);
/* 131 */     System.out.println("CBC模式解密明文: " + str1);
/*     */     
/* 133 */     String str4 = "77735a64616d64566e4c526247585a566c38753564673d3d0a";
/* 134 */     str1 = decryptData_ECB(new String(Hex.decode(str4)), str2);
/* 135 */     System.out.println("ECB mode decrypt: " + str1);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/sm/sm4/SM4Utils.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */