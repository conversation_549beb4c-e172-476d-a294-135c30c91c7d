/*     */ package weaver.sm.sm4;
/*     */ 
/*     */ import java.io.ByteArrayInputStream;
/*     */ import java.io.ByteArrayOutputStream;
/*     */ 
/*     */ 
/*     */ 
/*     */ public class SM4
/*     */ {
/*     */   public static final int SM4_ENCRYPT = 1;
/*     */   public static final int SM4_DECRYPT = 0;
/*     */   
/*     */   private int GET_ULONG_BE(byte[] paramArrayOfbyte, int paramInt) {
/*  14 */     return (paramArrayOfbyte[paramInt] & 0xFF) << 24 | (paramArrayOfbyte[paramInt + 1] & 0xFF) << 16 | (paramArrayOfbyte[paramInt + 2] & 0xFF) << 8 | paramArrayOfbyte[paramInt + 3] & 0xFF & 0xFFFFFFFF;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   private void PUT_ULONG_BE(int paramInt1, byte[] paramArrayOfbyte, int paramInt2) {
/*  20 */     paramArrayOfbyte[paramInt2] = (byte)(0xFF & paramInt1 >> 24);
/*  21 */     paramArrayOfbyte[paramInt2 + 1] = (byte)(0xFF & paramInt1 >> 16);
/*  22 */     paramArrayOfbyte[paramInt2 + 2] = (byte)(0xFF & paramInt1 >> 8);
/*  23 */     paramArrayOfbyte[paramInt2 + 3] = (byte)(0xFF & paramInt1);
/*     */   }
/*     */   
/*     */   private int SHL(int paramInt1, int paramInt2) {
/*  27 */     return (paramInt1 & 0xFFFFFFFF) << paramInt2;
/*     */   }
/*     */   
/*     */   private int ROTL(int paramInt1, int paramInt2) {
/*  31 */     return SHL(paramInt1, paramInt2) | paramInt1 >> 32 - paramInt2;
/*     */   }
/*     */   
/*  34 */   public static final byte[] SboxTable = new byte[] { -42, -112, -23, -2, -52, -31, 61, -73, 22, -74, 20, -62, 40, -5, 44, 5, 43, 103, -102, 118, 42, -66, 4, -61, -86, 68, 19, 38, 73, -122, 6, -103, -100, 66, 80, -12, -111, -17, -104, 122, 51, 84, 11, 67, -19, -49, -84, 98, -28, -77, 28, -87, -55, 8, -24, -107, Byte.MIN_VALUE, -33, -108, -6, 117, -113, 63, -90, 71, 7, -89, -4, -13, 115, 23, -70, -125, 89, 60, 25, -26, -123, 79, -88, 104, 107, -127, -78, 113, 100, -38, -117, -8, -21, 15, 75, 112, 86, -99, 53, 30, 36, 14, 94, 99, 88, -47, -94, 37, 34, 124, 59, 1, 33, 120, -121, -44, 0, 70, 87, -97, -45, 39, 82, 76, 54, 2, -25, -96, -60, -56, -98, -22, -65, -118, -46, 64, -57, 56, -75, -93, -9, -14, -50, -7, 97, 21, -95, -32, -82, 93, -92, -101, 52, 26, 85, -83, -109, 50, 48, -11, -116, -79, -29, 29, -10, -30, 46, -126, 102, -54, 96, -64, 41, 35, -85, 13, 83, 78, 111, -43, -37, 55, 69, -34, -3, -114, 47, 3, -1, 106, 114, 109, 108, 91, 81, -115, 27, -81, -110, -69, -35, -68, Byte.MAX_VALUE, 17, -39, 92, 65, 31, 16, 90, -40, 10, -63, 49, -120, -91, -51, 123, -67, 45, 116, -48, 18, -72, -27, -76, -80, -119, 105, -105, 74, 12, -106, 119, 126, 101, -71, -15, 9, -59, 110, -58, -124, 24, -16, 125, -20, 58, -36, 77, 32, 121, -18, 95, 62, -41, -53, 57, 72 };
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  59 */   public static final int[] FK = new int[] { -1548633402, 1453994832, 1736282519, -1301273892 };
/*     */   
/*  61 */   public static final int[] CK = new int[] { 462357, 472066609, 943670861, 1415275113, 1886879365, -1936483679, -1464879427, -993275175, -521670923, -66909679, 404694573, 876298825, 1347903077, 1819507329, -2003855715, -1532251463, -1060647211, -589042959, -117504499, 337322537, 808926789, 1280531041, 1752135293, -2071227751, -1599623499, -1128019247, -656414995, -184876535, 269950501, 741554753, 1213159005, 1684763257 };
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private byte sm4Sbox(byte paramByte) {
/*  67 */     int i = paramByte & 0xFF;
/*  68 */     return SboxTable[i];
/*     */   }
/*     */ 
/*     */   
/*     */   private int sm4Lt(int paramInt) {
/*  73 */     int i = 0;
/*  74 */     int j = 0;
/*  75 */     byte[] arrayOfByte1 = new byte[4];
/*  76 */     byte[] arrayOfByte2 = new byte[4];
/*  77 */     PUT_ULONG_BE(paramInt, arrayOfByte1, 0);
/*  78 */     arrayOfByte2[0] = sm4Sbox(arrayOfByte1[0]);
/*  79 */     arrayOfByte2[1] = sm4Sbox(arrayOfByte1[1]);
/*  80 */     arrayOfByte2[2] = sm4Sbox(arrayOfByte1[2]);
/*  81 */     arrayOfByte2[3] = sm4Sbox(arrayOfByte1[3]);
/*  82 */     i = GET_ULONG_BE(arrayOfByte2, 0);
/*  83 */     j = i ^ ROTL(i, 2) ^ ROTL(i, 10) ^ ROTL(i, 18) ^ ROTL(i, 24);
/*  84 */     return j;
/*     */   }
/*     */   
/*     */   private int sm4F(int paramInt1, int paramInt2, int paramInt3, int paramInt4, int paramInt5) {
/*  88 */     return paramInt1 ^ sm4Lt(paramInt2 ^ paramInt3 ^ paramInt4 ^ paramInt5);
/*     */   }
/*     */   
/*     */   private int sm4CalciRK(int paramInt) {
/*  92 */     int i = 0;
/*  93 */     int j = 0;
/*  94 */     byte[] arrayOfByte1 = new byte[4];
/*  95 */     byte[] arrayOfByte2 = new byte[4];
/*  96 */     PUT_ULONG_BE(paramInt, arrayOfByte1, 0);
/*  97 */     arrayOfByte2[0] = sm4Sbox(arrayOfByte1[0]);
/*  98 */     arrayOfByte2[1] = sm4Sbox(arrayOfByte1[1]);
/*  99 */     arrayOfByte2[2] = sm4Sbox(arrayOfByte1[2]);
/* 100 */     arrayOfByte2[3] = sm4Sbox(arrayOfByte1[3]);
/* 101 */     i = GET_ULONG_BE(arrayOfByte2, 0);
/* 102 */     j = i ^ ROTL(i, 13) ^ ROTL(i, 23);
/* 103 */     return j;
/*     */   }
/*     */   
/*     */   private void sm4_setkey(int[] paramArrayOfint, byte[] paramArrayOfbyte) {
/* 107 */     int[] arrayOfInt1 = new int[4];
/* 108 */     int[] arrayOfInt2 = new int[36];
/* 109 */     byte b = 0;
/* 110 */     arrayOfInt1[0] = GET_ULONG_BE(paramArrayOfbyte, 0);
/* 111 */     arrayOfInt1[1] = GET_ULONG_BE(paramArrayOfbyte, 4);
/* 112 */     arrayOfInt1[2] = GET_ULONG_BE(paramArrayOfbyte, 8);
/* 113 */     arrayOfInt1[3] = GET_ULONG_BE(paramArrayOfbyte, 12);
/* 114 */     arrayOfInt2[0] = arrayOfInt1[0] ^ FK[0];
/* 115 */     arrayOfInt2[1] = arrayOfInt1[1] ^ FK[1];
/* 116 */     arrayOfInt2[2] = arrayOfInt1[2] ^ FK[2];
/* 117 */     arrayOfInt2[3] = arrayOfInt1[3] ^ FK[3];
/* 118 */     for (; b < 32; b++) {
/* 119 */       arrayOfInt2[b + 4] = arrayOfInt2[b] ^ sm4CalciRK(arrayOfInt2[b + 1] ^ arrayOfInt2[b + 2] ^ arrayOfInt2[b + 3] ^ CK[b]);
/* 120 */       paramArrayOfint[b] = arrayOfInt2[b + 4];
/*     */     } 
/*     */   }
/*     */   
/*     */   private void sm4_one_round(int[] paramArrayOfint, byte[] paramArrayOfbyte1, byte[] paramArrayOfbyte2) {
/* 125 */     byte b = 0;
/* 126 */     int[] arrayOfInt = new int[36];
/* 127 */     arrayOfInt[0] = GET_ULONG_BE(paramArrayOfbyte1, 0);
/* 128 */     arrayOfInt[1] = GET_ULONG_BE(paramArrayOfbyte1, 4);
/* 129 */     arrayOfInt[2] = GET_ULONG_BE(paramArrayOfbyte1, 8);
/* 130 */     arrayOfInt[3] = GET_ULONG_BE(paramArrayOfbyte1, 12);
/* 131 */     while (b < 32) {
/* 132 */       arrayOfInt[b + 4] = sm4F(arrayOfInt[b], arrayOfInt[b + 1], arrayOfInt[b + 2], arrayOfInt[b + 3], paramArrayOfint[b]);
/* 133 */       b++;
/*     */     } 
/* 135 */     PUT_ULONG_BE(arrayOfInt[35], paramArrayOfbyte2, 0);
/* 136 */     PUT_ULONG_BE(arrayOfInt[34], paramArrayOfbyte2, 4);
/* 137 */     PUT_ULONG_BE(arrayOfInt[33], paramArrayOfbyte2, 8);
/* 138 */     PUT_ULONG_BE(arrayOfInt[32], paramArrayOfbyte2, 12);
/*     */   }
/*     */   
/*     */   private byte[] padding(byte[] paramArrayOfbyte, int paramInt) {
/* 142 */     if (paramArrayOfbyte == null) {
/* 143 */       return null;
/*     */     }
/*     */     
/* 146 */     byte[] arrayOfByte = (byte[])null;
/* 147 */     if (paramInt == 1) {
/* 148 */       int i = 16 - paramArrayOfbyte.length % 16;
/* 149 */       arrayOfByte = new byte[paramArrayOfbyte.length + i];
/* 150 */       System.arraycopy(paramArrayOfbyte, 0, arrayOfByte, 0, paramArrayOfbyte.length);
/* 151 */       for (byte b = 0; b < i; b++) {
/* 152 */         arrayOfByte[paramArrayOfbyte.length + b] = (byte)i;
/*     */       }
/*     */     } else {
/* 155 */       byte b = paramArrayOfbyte[paramArrayOfbyte.length - 1];
/* 156 */       arrayOfByte = new byte[paramArrayOfbyte.length - b];
/* 157 */       System.arraycopy(paramArrayOfbyte, 0, arrayOfByte, 0, paramArrayOfbyte.length - b);
/*     */     } 
/* 159 */     return arrayOfByte;
/*     */   }
/*     */   
/*     */   public void sm4_setkey_enc(SM4_Context paramSM4_Context, byte[] paramArrayOfbyte) throws Exception {
/* 163 */     if (paramSM4_Context == null) {
/* 164 */       throw new Exception("ctx is null!");
/*     */     }
/*     */     
/* 167 */     if (paramArrayOfbyte == null || paramArrayOfbyte.length != 16) {
/* 168 */       throw new Exception("key error!");
/*     */     }
/*     */     
/* 171 */     paramSM4_Context.mode = 1;
/* 172 */     sm4_setkey(paramSM4_Context.sk, paramArrayOfbyte);
/*     */   }
/*     */   
/*     */   public byte[] sm4_crypt_ecb(SM4_Context paramSM4_Context, byte[] paramArrayOfbyte) throws Exception {
/* 176 */     if (paramArrayOfbyte == null) {
/* 177 */       throw new Exception("input is null!");
/*     */     }
/*     */     
/* 180 */     if (paramSM4_Context.isPadding && paramSM4_Context.mode == 1) {
/* 181 */       paramArrayOfbyte = padding(paramArrayOfbyte, 1);
/*     */     }
/*     */     
/* 184 */     int i = paramArrayOfbyte.length;
/* 185 */     ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(paramArrayOfbyte);
/* 186 */     ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
/* 187 */     for (; i > 0; i -= 16) {
/* 188 */       byte[] arrayOfByte1 = new byte[16];
/* 189 */       byte[] arrayOfByte2 = new byte[16];
/* 190 */       byteArrayInputStream.read(arrayOfByte1);
/* 191 */       sm4_one_round(paramSM4_Context.sk, arrayOfByte1, arrayOfByte2);
/* 192 */       byteArrayOutputStream.write(arrayOfByte2);
/*     */     } 
/*     */     
/* 195 */     byte[] arrayOfByte = byteArrayOutputStream.toByteArray();
/* 196 */     if (paramSM4_Context.isPadding && paramSM4_Context.mode == 0) {
/* 197 */       arrayOfByte = padding(arrayOfByte, 0);
/*     */     }
/* 199 */     byteArrayInputStream.close();
/* 200 */     byteArrayOutputStream.close();
/* 201 */     return arrayOfByte;
/*     */   }
/*     */   
/*     */   public void sm4_setkey_dec(SM4_Context paramSM4_Context, byte[] paramArrayOfbyte) throws Exception {
/* 205 */     if (paramSM4_Context == null) {
/* 206 */       throw new Exception("ctx is null!");
/*     */     }
/*     */     
/* 209 */     if (paramArrayOfbyte == null || paramArrayOfbyte.length != 16) {
/* 210 */       throw new Exception("key error!");
/*     */     }
/*     */     
/* 213 */     byte b = 0;
/* 214 */     paramSM4_Context.mode = 0;
/* 215 */     sm4_setkey(paramSM4_Context.sk, paramArrayOfbyte);
/* 216 */     for (b = 0; b < 16; b++) {
/* 217 */       SWAP(paramSM4_Context.sk, b);
/*     */     }
/*     */   }
/*     */   
/*     */   private void SWAP(int[] paramArrayOfint, int paramInt) {
/* 222 */     int i = paramArrayOfint[paramInt];
/* 223 */     paramArrayOfint[paramInt] = paramArrayOfint[31 - paramInt];
/* 224 */     paramArrayOfint[31 - paramInt] = i;
/*     */   }
/*     */   
/*     */   public byte[] sm4_crypt_cbc(SM4_Context paramSM4_Context, byte[] paramArrayOfbyte1, byte[] paramArrayOfbyte2) throws Exception {
/* 228 */     if (paramArrayOfbyte1 == null || paramArrayOfbyte1.length != 16) {
/* 229 */       throw new Exception("iv error!");
/*     */     }
/*     */     
/* 232 */     if (paramArrayOfbyte2 == null) {
/* 233 */       throw new Exception("input is null!");
/*     */     }
/*     */     
/* 236 */     if (paramSM4_Context.isPadding && paramSM4_Context.mode == 1) {
/* 237 */       paramArrayOfbyte2 = padding(paramArrayOfbyte2, 1);
/*     */     }
/*     */     
/* 240 */     byte b = 0;
/* 241 */     int i = paramArrayOfbyte2.length;
/* 242 */     ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(paramArrayOfbyte2);
/* 243 */     ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
/* 244 */     if (paramSM4_Context.mode == 1) {
/* 245 */       for (; i > 0; i -= 16) {
/* 246 */         byte[] arrayOfByte1 = new byte[16];
/* 247 */         byte[] arrayOfByte2 = new byte[16];
/* 248 */         byte[] arrayOfByte3 = new byte[16];
/*     */         
/* 250 */         byteArrayInputStream.read(arrayOfByte1);
/* 251 */         for (b = 0; b < 16; b++) {
/* 252 */           arrayOfByte2[b] = (byte)(arrayOfByte1[b] ^ paramArrayOfbyte1[b]);
/*     */         }
/* 254 */         sm4_one_round(paramSM4_Context.sk, arrayOfByte2, arrayOfByte3);
/* 255 */         System.arraycopy(arrayOfByte3, 0, paramArrayOfbyte1, 0, 16);
/* 256 */         byteArrayOutputStream.write(arrayOfByte3);
/*     */       } 
/*     */     } else {
/* 259 */       byte[] arrayOfByte1 = new byte[16];
/* 260 */       for (; i > 0; i -= 16) {
/* 261 */         byte[] arrayOfByte2 = new byte[16];
/* 262 */         byte[] arrayOfByte3 = new byte[16];
/* 263 */         byte[] arrayOfByte4 = new byte[16];
/*     */         
/* 265 */         byteArrayInputStream.read(arrayOfByte2);
/* 266 */         System.arraycopy(arrayOfByte2, 0, arrayOfByte1, 0, 16);
/* 267 */         sm4_one_round(paramSM4_Context.sk, arrayOfByte2, arrayOfByte3);
/* 268 */         for (b = 0; b < 16; b++) {
/* 269 */           arrayOfByte4[b] = (byte)(arrayOfByte3[b] ^ paramArrayOfbyte1[b]);
/*     */         }
/* 271 */         System.arraycopy(arrayOfByte1, 0, paramArrayOfbyte1, 0, 16);
/* 272 */         byteArrayOutputStream.write(arrayOfByte4);
/*     */       } 
/*     */     } 
/*     */     
/* 276 */     byte[] arrayOfByte = byteArrayOutputStream.toByteArray();
/* 277 */     if (paramSM4_Context.isPadding && paramSM4_Context.mode == 0) {
/* 278 */       arrayOfByte = padding(arrayOfByte, 0);
/*     */     }
/* 280 */     byteArrayInputStream.close();
/* 281 */     byteArrayOutputStream.close();
/* 282 */     return arrayOfByte;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/sm/sm4/SM4.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */