/*     */ package weaver.sm.sm4;
/*     */ 
/*     */ import java.math.BigInteger;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class Util
/*     */ {
/*     */   public static byte[] intToBytes(int paramInt) {
/*  15 */     byte[] arrayOfByte = new byte[4];
/*  16 */     arrayOfByte[0] = (byte)(0xFF & paramInt >> 0);
/*  17 */     arrayOfByte[1] = (byte)(0xFF & paramInt >> 8);
/*  18 */     arrayOfByte[2] = (byte)(0xFF & paramInt >> 16);
/*  19 */     arrayOfByte[3] = (byte)(0xFF & paramInt >> 24);
/*  20 */     return arrayOfByte;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static int byteToInt(byte[] paramArrayOfbyte) {
/*  31 */     int i = 0;
/*     */     
/*  33 */     int j = (0xFF & paramArrayOfbyte[0]) << 0;
/*  34 */     i |= j;
/*  35 */     j = (0xFF & paramArrayOfbyte[1]) << 8;
/*  36 */     i |= j;
/*  37 */     j = (0xFF & paramArrayOfbyte[2]) << 16;
/*  38 */     i |= j;
/*  39 */     j = (0xFF & paramArrayOfbyte[3]) << 24;
/*  40 */     i |= j;
/*  41 */     return i;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static byte[] longToBytes(long paramLong) {
/*  52 */     byte[] arrayOfByte = new byte[8];
/*  53 */     for (byte b = 0; b < 8; b++) {
/*  54 */       arrayOfByte[b] = (byte)(int)(0xFFL & paramLong >> b * 8);
/*     */     }
/*     */     
/*  57 */     return arrayOfByte;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static byte[] byteConvert32Bytes(BigInteger paramBigInteger) {
/*  67 */     byte[] arrayOfByte = (byte[])null;
/*  68 */     if (paramBigInteger == null) {
/*  69 */       return null;
/*     */     }
/*     */     
/*  72 */     if ((paramBigInteger.toByteArray()).length == 33) {
/*  73 */       arrayOfByte = new byte[32];
/*  74 */       System.arraycopy(paramBigInteger.toByteArray(), 1, arrayOfByte, 0, 32);
/*  75 */     } else if ((paramBigInteger.toByteArray()).length == 32) {
/*  76 */       arrayOfByte = paramBigInteger.toByteArray();
/*     */     } else {
/*  78 */       arrayOfByte = new byte[32];
/*  79 */       for (byte b = 0; b < 32 - (paramBigInteger.toByteArray()).length; b++) {
/*  80 */         arrayOfByte[b] = 0;
/*     */       }
/*  82 */       System.arraycopy(paramBigInteger.toByteArray(), 0, arrayOfByte, 32 - (paramBigInteger.toByteArray()).length, (paramBigInteger.toByteArray()).length);
/*     */     } 
/*  84 */     return arrayOfByte;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static BigInteger byteConvertInteger(byte[] paramArrayOfbyte) {
/*  94 */     if (paramArrayOfbyte[0] < 0) {
/*  95 */       byte[] arrayOfByte = new byte[paramArrayOfbyte.length + 1];
/*  96 */       arrayOfByte[0] = 0;
/*  97 */       System.arraycopy(paramArrayOfbyte, 0, arrayOfByte, 1, paramArrayOfbyte.length);
/*  98 */       return new BigInteger(arrayOfByte);
/*     */     } 
/* 100 */     return new BigInteger(paramArrayOfbyte);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getHexString(byte[] paramArrayOfbyte) {
/* 110 */     return getHexString(paramArrayOfbyte, true);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getHexString(byte[] paramArrayOfbyte, boolean paramBoolean) {
/* 121 */     String str = "";
/* 122 */     for (byte b = 0; b < paramArrayOfbyte.length; b++) {
/* 123 */       str = str + Integer.toString((paramArrayOfbyte[b] & 0xFF) + 256, 16).substring(1);
/*     */     }
/* 125 */     return paramBoolean ? str.toUpperCase() : str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static void printHexString(byte[] paramArrayOfbyte) {
/* 134 */     for (byte b = 0; b < paramArrayOfbyte.length; b++) {
/* 135 */       String str = Integer.toHexString(paramArrayOfbyte[b] & 0xFF);
/* 136 */       if (str.length() == 1) {
/* 137 */         str = '0' + str;
/*     */       }
/* 139 */       System.out.print("0x" + str.toUpperCase() + ",");
/*     */     } 
/* 141 */     System.out.println("");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static byte[] hexStringToBytes(String paramString) {
/* 152 */     if (paramString == null || paramString.equals("")) {
/* 153 */       return null;
/*     */     }
/*     */     
/* 156 */     paramString = paramString.toUpperCase();
/* 157 */     int i = paramString.length() / 2;
/* 158 */     char[] arrayOfChar = paramString.toCharArray();
/* 159 */     byte[] arrayOfByte = new byte[i];
/* 160 */     for (byte b = 0; b < i; b++) {
/* 161 */       int j = b * 2;
/* 162 */       arrayOfByte[b] = (byte)(charToByte(arrayOfChar[j]) << 4 | charToByte(arrayOfChar[j + 1]));
/*     */     } 
/* 164 */     return arrayOfByte;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static byte charToByte(char paramChar) {
/* 175 */     return (byte)"0123456789ABCDEF".indexOf(paramChar);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/* 181 */   private static final char[] DIGITS_LOWER = new char[] { '0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f' };
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/* 187 */   private static final char[] DIGITS_UPPER = new char[] { '0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F' };
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static char[] encodeHex(byte[] paramArrayOfbyte) {
/* 198 */     return encodeHex(paramArrayOfbyte, true);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static char[] encodeHex(byte[] paramArrayOfbyte, boolean paramBoolean) {
/* 211 */     return encodeHex(paramArrayOfbyte, paramBoolean ? DIGITS_LOWER : DIGITS_UPPER);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   protected static char[] encodeHex(byte[] paramArrayOfbyte, char[] paramArrayOfchar) {
/* 224 */     int i = paramArrayOfbyte.length;
/* 225 */     char[] arrayOfChar = new char[i << 1];
/*     */     
/* 227 */     for (byte b1 = 0, b2 = 0; b1 < i; b1++) {
/* 228 */       arrayOfChar[b2++] = paramArrayOfchar[(0xF0 & paramArrayOfbyte[b1]) >>> 4];
/* 229 */       arrayOfChar[b2++] = paramArrayOfchar[0xF & paramArrayOfbyte[b1]];
/*     */     } 
/* 231 */     return arrayOfChar;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String encodeHexString(byte[] paramArrayOfbyte) {
/* 242 */     return encodeHexString(paramArrayOfbyte, true);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String encodeHexString(byte[] paramArrayOfbyte, boolean paramBoolean) {
/* 255 */     return encodeHexString(paramArrayOfbyte, paramBoolean ? DIGITS_LOWER : DIGITS_UPPER);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   protected static String encodeHexString(byte[] paramArrayOfbyte, char[] paramArrayOfchar) {
/* 268 */     return new String(encodeHex(paramArrayOfbyte, paramArrayOfchar));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static byte[] decodeHex(char[] paramArrayOfchar) {
/* 281 */     int i = paramArrayOfchar.length;
/*     */     
/* 283 */     if ((i & 0x1) != 0) {
/* 284 */       throw new RuntimeException("Odd number of characters.");
/*     */     }
/*     */     
/* 287 */     byte[] arrayOfByte = new byte[i >> 1];
/*     */ 
/*     */     
/* 290 */     for (byte b1 = 0, b2 = 0; b2 < i; b1++) {
/* 291 */       int j = toDigit(paramArrayOfchar[b2], b2) << 4;
/* 292 */       b2++;
/* 293 */       j |= toDigit(paramArrayOfchar[b2], b2);
/* 294 */       b2++;
/* 295 */       arrayOfByte[b1] = (byte)(j & 0xFF);
/*     */     } 
/*     */     
/* 298 */     return arrayOfByte;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   protected static int toDigit(char paramChar, int paramInt) {
/* 313 */     int i = Character.digit(paramChar, 16);
/* 314 */     if (i == -1) {
/* 315 */       throw new RuntimeException("Illegal hexadecimal character " + paramChar + " at index " + paramInt);
/*     */     }
/* 317 */     return i;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String StringToAsciiString(String paramString) {
/* 328 */     String str = "";
/* 329 */     int i = paramString.length();
/* 330 */     for (byte b = 0; b < i; b++) {
/* 331 */       char c = paramString.charAt(b);
/* 332 */       String str1 = Integer.toHexString(c);
/* 333 */       str = str + str1;
/*     */     } 
/* 335 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String hexStringToString(String paramString, int paramInt) {
/* 348 */     String str = "";
/* 349 */     int i = paramString.length() / paramInt;
/* 350 */     for (byte b = 0; b < i; b++) {
/* 351 */       char c = (char)hexStringToAlgorism(paramString.substring(b * paramInt, (b + 1) * paramInt));
/* 352 */       str = str + c;
/*     */     } 
/* 354 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static int hexStringToAlgorism(String paramString) {
/* 365 */     paramString = paramString.toUpperCase();
/* 366 */     int i = paramString.length();
/* 367 */     int j = 0;
/* 368 */     for (int k = i; k > 0; k--) {
/* 369 */       char c = paramString.charAt(k - 1);
/* 370 */       int m = 0;
/* 371 */       if (c >= '0' && c <= '9') {
/* 372 */         m = c - 48;
/*     */       } else {
/* 374 */         m = c - 55;
/*     */       } 
/* 376 */       j = (int)(j + Math.pow(16.0D, (i - k)) * m);
/*     */     } 
/* 378 */     return j;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String hexStringToBinary(String paramString) {
/* 389 */     paramString = paramString.toUpperCase();
/* 390 */     String str = "";
/* 391 */     int i = paramString.length();
/* 392 */     for (byte b = 0; b < i; b++) {
/* 393 */       char c = paramString.charAt(b);
/* 394 */       switch (c) {
/*     */         case '0':
/* 396 */           str = str + "0000";
/*     */           break;
/*     */         case '1':
/* 399 */           str = str + "0001";
/*     */           break;
/*     */         case '2':
/* 402 */           str = str + "0010";
/*     */           break;
/*     */         case '3':
/* 405 */           str = str + "0011";
/*     */           break;
/*     */         case '4':
/* 408 */           str = str + "0100";
/*     */           break;
/*     */         case '5':
/* 411 */           str = str + "0101";
/*     */           break;
/*     */         case '6':
/* 414 */           str = str + "0110";
/*     */           break;
/*     */         case '7':
/* 417 */           str = str + "0111";
/*     */           break;
/*     */         case '8':
/* 420 */           str = str + "1000";
/*     */           break;
/*     */         case '9':
/* 423 */           str = str + "1001";
/*     */           break;
/*     */         case 'A':
/* 426 */           str = str + "1010";
/*     */           break;
/*     */         case 'B':
/* 429 */           str = str + "1011";
/*     */           break;
/*     */         case 'C':
/* 432 */           str = str + "1100";
/*     */           break;
/*     */         case 'D':
/* 435 */           str = str + "1101";
/*     */           break;
/*     */         case 'E':
/* 438 */           str = str + "1110";
/*     */           break;
/*     */         case 'F':
/* 441 */           str = str + "1111";
/*     */           break;
/*     */       } 
/*     */     } 
/* 445 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String AsciiStringToString(String paramString) {
/* 456 */     String str = "";
/* 457 */     int i = paramString.length() / 2;
/* 458 */     for (byte b = 0; b < i; b++) {
/* 459 */       String str1 = paramString.substring(b * 2, b * 2 + 2);
/* 460 */       int j = hexStringToAlgorism(str1);
/* 461 */       char c = (char)j;
/* 462 */       String str2 = String.valueOf(c);
/* 463 */       str = str + str2;
/*     */     } 
/* 465 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String algorismToHexString(int paramInt1, int paramInt2) {
/* 478 */     String str = "";
/* 479 */     str = Integer.toHexString(paramInt1);
/*     */     
/* 481 */     if (str.length() % 2 == 1) {
/* 482 */       str = "0" + str;
/*     */     }
/* 484 */     return patchHexString(str.toUpperCase(), paramInt2);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String byteToString(byte[] paramArrayOfbyte) {
/* 495 */     String str = "";
/*     */ 
/*     */     
/* 498 */     int i = paramArrayOfbyte.length;
/* 499 */     for (byte b = 0; b < i; b++) {
/* 500 */       char c = (char)paramArrayOfbyte[b];
/* 501 */       str = str + c;
/*     */     } 
/* 503 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static int binaryToAlgorism(String paramString) {
/* 514 */     int i = paramString.length();
/* 515 */     int j = 0;
/* 516 */     for (int k = i; k > 0; k--) {
/* 517 */       char c = paramString.charAt(k - 1);
/* 518 */       int m = c - 48;
/* 519 */       j = (int)(j + Math.pow(2.0D, (i - k)) * m);
/*     */     } 
/* 521 */     return j;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String algorismToHEXString(int paramInt) {
/* 532 */     String str = "";
/* 533 */     str = Integer.toHexString(paramInt);
/*     */     
/* 535 */     if (str.length() % 2 == 1) {
/* 536 */       str = "0" + str;
/*     */     }
/*     */     
/* 539 */     str = str.toUpperCase();
/*     */     
/* 541 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String patchHexString(String paramString, int paramInt) {
/* 554 */     String str = "";
/* 555 */     for (byte b = 0; b < paramInt - paramString.length(); b++) {
/* 556 */       str = "0" + str;
/*     */     }
/* 558 */     paramString = (str + paramString).substring(0, paramInt);
/* 559 */     return paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static int parseToInt(String paramString, int paramInt1, int paramInt2) {
/* 574 */     int i = 0;
/*     */     try {
/* 576 */       i = Integer.parseInt(paramString, paramInt2);
/* 577 */     } catch (NumberFormatException numberFormatException) {
/* 578 */       i = paramInt1;
/*     */     } 
/* 580 */     return i;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static int parseToInt(String paramString, int paramInt) {
/* 593 */     int i = 0;
/*     */     try {
/* 595 */       i = Integer.parseInt(paramString);
/* 596 */     } catch (NumberFormatException numberFormatException) {
/* 597 */       i = paramInt;
/*     */     } 
/* 599 */     return i;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static byte[] hexToByte(String paramString) throws IllegalArgumentException {
/* 608 */     if (paramString.length() % 2 != 0) {
/* 609 */       throw new IllegalArgumentException();
/*     */     }
/* 611 */     char[] arrayOfChar = paramString.toCharArray();
/* 612 */     byte[] arrayOfByte = new byte[paramString.length() / 2]; byte b1, b2; int i;
/* 613 */     for (b1 = 0, b2 = 0, i = paramString.length(); b1 < i; b1++, b2++) {
/* 614 */       String str = "" + arrayOfChar[b1++] + arrayOfChar[b1];
/* 615 */       int j = Integer.parseInt(str, 16) & 0xFF;
/* 616 */       arrayOfByte[b2] = (new Integer(j)).byteValue();
/*     */     } 
/* 618 */     return arrayOfByte;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String byteToHex(byte[] paramArrayOfbyte) {
/* 629 */     if (paramArrayOfbyte == null) {
/* 630 */       throw new IllegalArgumentException("Argument b ( byte array ) is null! ");
/*     */     }
/* 632 */     String str1 = "";
/* 633 */     String str2 = "";
/* 634 */     for (byte b = 0; b < paramArrayOfbyte.length; b++) {
/* 635 */       str2 = Integer.toHexString(paramArrayOfbyte[b] & 0xFF);
/* 636 */       if (str2.length() == 1) {
/* 637 */         str1 = str1 + "0" + str2;
/*     */       } else {
/* 639 */         str1 = str1 + str2;
/*     */       } 
/*     */     } 
/* 642 */     return str1.toUpperCase();
/*     */   }
/*     */   
/*     */   public static byte[] subByte(byte[] paramArrayOfbyte, int paramInt1, int paramInt2) {
/* 646 */     byte[] arrayOfByte = new byte[paramInt2];
/* 647 */     for (byte b = 0; b < paramInt2; b++) {
/* 648 */       arrayOfByte[b] = paramArrayOfbyte[b + paramInt1];
/*     */     }
/* 650 */     return arrayOfByte;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/sm/sm4/Util.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */