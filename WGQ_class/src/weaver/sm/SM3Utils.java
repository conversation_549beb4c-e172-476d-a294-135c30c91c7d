/*     */ package weaver.sm;
/*     */ 
/*     */ import cfca.sadk.util.Base64;
/*     */ import cfca.sadk.util.HashUtil;
/*     */ import java.util.UUID;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.security.encryption_alg.AlgUtil;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class SM3Utils
/*     */ {
/*     */   public static final String ENC_TYPE_MD5 = "md5";
/*     */   public static final String ENC_TYPE_SM3 = "sm3";
/*  20 */   private static String ENC_TYPE = "md5";
/*     */   
/*     */   static {
/*  23 */     BaseBean baseBean = new BaseBean();
/*  24 */     ENC_TYPE = Util.null2String(baseBean.getPropValue("weaver_security_type", "nonreversible_enc_type"));
/*  25 */     if ("".equals(ENC_TYPE)) {
/*  26 */       ENC_TYPE = "md5";
/*     */     }
/*     */   }
/*     */ 
/*     */   
/*     */   public static void main(String[] paramArrayOfString) {
/*  32 */     String str1 = "1";
/*  33 */     String str2 = createSalt();
/*  34 */     System.out.println(str2.length());
/*  35 */     System.out.println(getEncrypt(str1));
/*  36 */     System.out.println(getEncrypt(str1, str2, "md5", null));
/*  37 */     System.out.println(getEncrypt(str1, str2, "sm3", null));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getEncrypt(String paramString) {
/*  47 */     return getEncrypt(paramString, null);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getEncrypt(String paramString1, String paramString2) {
/*  57 */     return getEncrypt(paramString1, paramString2, "UTF-8");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getEncrypt(String paramString1, String paramString2, String paramString3) {
/*  68 */     return getEncrypt(paramString1, paramString2, null, paramString3);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getEncrypt(String paramString1, String paramString2, String paramString3, String paramString4) {
/*     */     try {
/*  82 */       byte[] arrayOfByte = null;
/*  83 */       if (paramString4 == null || "".equals(paramString4)) {
/*  84 */         paramString4 = "UTF-8";
/*     */       }
/*  86 */       if (paramString2 != null && !"".equals(paramString2)) {
/*  87 */         paramString1 = paramString1 + paramString2;
/*     */       }
/*  89 */       if (paramString3 == null || "".equals(paramString3)) {
/*  90 */         paramString3 = ENC_TYPE;
/*     */       }
/*  92 */       if ("sm3".equals(paramString3)) {
/*     */         try {
/*  94 */           byte[] arrayOfByte1 = paramString1.getBytes(paramString4);
/*  95 */           byte[] arrayOfByte2 = HashUtil.SM2HashMessageByBCWithoutZValue(arrayOfByte1);
/*  96 */           arrayOfByte = Base64.encode(arrayOfByte2);
/*  97 */         } catch (Exception exception) {
/*  98 */           (new BaseBean()).writeLog(exception);
/*     */         } 
/*     */       } else {
/* 101 */         return Util.getEncrypt(paramString1);
/*     */       } 
/* 103 */       return new String(arrayOfByte, paramString4);
/* 104 */     } catch (Exception exception) {
/* 105 */       (new BaseBean()).writeLog(exception);
/*     */       
/* 107 */       return null;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String createSalt() {
/* 115 */     return UUID.randomUUID().toString();
/*     */   }
/*     */   
/*     */   public static Boolean compareSm3(String paramString1, String paramString2) {
/* 119 */     if (paramString1 == null || paramString2 == null) return Boolean.valueOf(false); 
/* 120 */     byte[] arrayOfByte = Base64.encode(AlgUtil.hexStringToByteArray(paramString1));
/* 121 */     return Boolean.valueOf(paramString2.equals(new String(arrayOfByte)));
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/sm/SM3Utils.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */