/*     */ package weaver.sm;
/*     */ 
/*     */ import java.math.BigInteger;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class Util
/*     */ {
/*     */   public static byte[] intToBytes(int paramInt) {
/*  15 */     byte[] arrayOfByte = new byte[4];
/*  16 */     arrayOfByte[0] = (byte)(0xFF & paramInt >> 0);
/*  17 */     arrayOfByte[1] = (byte)(0xFF & paramInt >> 8);
/*  18 */     arrayOfByte[2] = (byte)(0xFF & paramInt >> 16);
/*  19 */     arrayOfByte[3] = (byte)(0xFF & paramInt >> 24);
/*  20 */     return arrayOfByte;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static int byteToInt(byte[] paramArrayOfbyte) {
/*  31 */     int i = 0;
/*     */     
/*  33 */     int j = (0xFF & paramArrayOfbyte[0]) << 0;
/*  34 */     i |= j;
/*  35 */     j = (0xFF & paramArrayOfbyte[1]) << 8;
/*  36 */     i |= j;
/*  37 */     j = (0xFF & paramArrayOfbyte[2]) << 16;
/*  38 */     i |= j;
/*  39 */     j = (0xFF & paramArrayOfbyte[3]) << 24;
/*  40 */     i |= j;
/*  41 */     return i;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static byte[] longToBytes(long paramLong) {
/*  52 */     byte[] arrayOfByte = new byte[8];
/*  53 */     for (byte b = 0; b < 8; b++)
/*     */     {
/*  55 */       arrayOfByte[b] = (byte)(int)(0xFFL & paramLong >> b * 8);
/*     */     }
/*     */     
/*  58 */     return arrayOfByte;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static byte[] byteConvert32Bytes(BigInteger paramBigInteger) {
/*  69 */     byte[] arrayOfByte = (byte[])null;
/*  70 */     if (paramBigInteger == null)
/*     */     {
/*  72 */       return null;
/*     */     }
/*     */     
/*  75 */     if ((paramBigInteger.toByteArray()).length == 33) {
/*     */       
/*  77 */       arrayOfByte = new byte[32];
/*  78 */       System.arraycopy(paramBigInteger.toByteArray(), 1, arrayOfByte, 0, 32);
/*     */     }
/*  80 */     else if ((paramBigInteger.toByteArray()).length == 32) {
/*     */       
/*  82 */       arrayOfByte = paramBigInteger.toByteArray();
/*     */     }
/*     */     else {
/*     */       
/*  86 */       arrayOfByte = new byte[32];
/*  87 */       for (byte b = 0; b < 32 - (paramBigInteger.toByteArray()).length; b++)
/*     */       {
/*  89 */         arrayOfByte[b] = 0;
/*     */       }
/*  91 */       System.arraycopy(paramBigInteger.toByteArray(), 0, arrayOfByte, 32 - (paramBigInteger.toByteArray()).length, (paramBigInteger.toByteArray()).length);
/*     */     } 
/*  93 */     return arrayOfByte;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static BigInteger byteConvertInteger(byte[] paramArrayOfbyte) {
/* 104 */     if (paramArrayOfbyte[0] < 0) {
/*     */       
/* 106 */       byte[] arrayOfByte = new byte[paramArrayOfbyte.length + 1];
/* 107 */       arrayOfByte[0] = 0;
/* 108 */       System.arraycopy(paramArrayOfbyte, 0, arrayOfByte, 1, paramArrayOfbyte.length);
/* 109 */       return new BigInteger(arrayOfByte);
/*     */     } 
/* 111 */     return new BigInteger(paramArrayOfbyte);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getHexString(byte[] paramArrayOfbyte) {
/* 122 */     return getHexString(paramArrayOfbyte, true);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getHexString(byte[] paramArrayOfbyte, boolean paramBoolean) {
/* 134 */     String str = "";
/* 135 */     for (byte b = 0; b < paramArrayOfbyte.length; b++)
/*     */     {
/* 137 */       str = str + Integer.toString((paramArrayOfbyte[b] & 0xFF) + 256, 16).substring(1);
/*     */     }
/* 139 */     return paramBoolean ? str.toUpperCase() : str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static void printHexString(byte[] paramArrayOfbyte) {
/* 149 */     for (byte b = 0; b < paramArrayOfbyte.length; b++) {
/*     */       
/* 151 */       String str = Integer.toHexString(paramArrayOfbyte[b] & 0xFF);
/* 152 */       if (str.length() == 1)
/*     */       {
/* 154 */         str = '0' + str;
/*     */       }
/* 156 */       System.out.print("0x" + str.toUpperCase() + ",");
/*     */     } 
/* 158 */     System.out.println("");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static byte[] hexStringToBytes(String paramString) {
/* 170 */     if (paramString == null || paramString.equals(""))
/*     */     {
/* 172 */       return null;
/*     */     }
/*     */     
/* 175 */     paramString = paramString.toUpperCase();
/* 176 */     int i = paramString.length() / 2;
/* 177 */     char[] arrayOfChar = paramString.toCharArray();
/* 178 */     byte[] arrayOfByte = new byte[i];
/* 179 */     for (byte b = 0; b < i; b++) {
/*     */       
/* 181 */       int j = b * 2;
/* 182 */       arrayOfByte[b] = (byte)(charToByte(arrayOfChar[j]) << 4 | charToByte(arrayOfChar[j + 1]));
/*     */     } 
/* 184 */     return arrayOfByte;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static byte charToByte(char paramChar) {
/* 196 */     return (byte)"0123456789ABCDEF".indexOf(paramChar);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/* 202 */   private static final char[] DIGITS_LOWER = new char[] { '0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f' };
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/* 208 */   private static final char[] DIGITS_UPPER = new char[] { '0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F' };
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static char[] encodeHex(byte[] paramArrayOfbyte) {
/* 218 */     return encodeHex(paramArrayOfbyte, true);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static char[] encodeHex(byte[] paramArrayOfbyte, boolean paramBoolean) {
/* 229 */     return encodeHex(paramArrayOfbyte, paramBoolean ? DIGITS_LOWER : DIGITS_UPPER);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   protected static char[] encodeHex(byte[] paramArrayOfbyte, char[] paramArrayOfchar) {
/* 240 */     int i = paramArrayOfbyte.length;
/* 241 */     char[] arrayOfChar = new char[i << 1];
/*     */     
/* 243 */     for (byte b1 = 0, b2 = 0; b1 < i; b1++) {
/* 244 */       arrayOfChar[b2++] = paramArrayOfchar[(0xF0 & paramArrayOfbyte[b1]) >>> 4];
/* 245 */       arrayOfChar[b2++] = paramArrayOfchar[0xF & paramArrayOfbyte[b1]];
/*     */     } 
/* 247 */     return arrayOfChar;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String encodeHexString(byte[] paramArrayOfbyte) {
/* 257 */     return encodeHexString(paramArrayOfbyte, true);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String encodeHexString(byte[] paramArrayOfbyte, boolean paramBoolean) {
/* 268 */     return encodeHexString(paramArrayOfbyte, paramBoolean ? DIGITS_LOWER : DIGITS_UPPER);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   protected static String encodeHexString(byte[] paramArrayOfbyte, char[] paramArrayOfchar) {
/* 279 */     return new String(encodeHex(paramArrayOfbyte, paramArrayOfchar));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static byte[] decodeHex(char[] paramArrayOfchar) {
/* 290 */     int i = paramArrayOfchar.length;
/*     */     
/* 292 */     if ((i & 0x1) != 0) {
/* 293 */       throw new RuntimeException("Odd number of characters.");
/*     */     }
/*     */     
/* 296 */     byte[] arrayOfByte = new byte[i >> 1];
/*     */ 
/*     */     
/* 299 */     for (byte b1 = 0, b2 = 0; b2 < i; b1++) {
/* 300 */       int j = toDigit(paramArrayOfchar[b2], b2) << 4;
/* 301 */       b2++;
/* 302 */       j |= toDigit(paramArrayOfchar[b2], b2);
/* 303 */       b2++;
/* 304 */       arrayOfByte[b1] = (byte)(j & 0xFF);
/*     */     } 
/*     */     
/* 307 */     return arrayOfByte;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   protected static int toDigit(char paramChar, int paramInt) {
/* 319 */     int i = Character.digit(paramChar, 16);
/* 320 */     if (i == -1) {
/* 321 */       throw new RuntimeException("Illegal hexadecimal character " + paramChar + " at index " + paramInt);
/*     */     }
/*     */     
/* 324 */     return i;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String StringToAsciiString(String paramString) {
/* 335 */     String str = "";
/* 336 */     int i = paramString.length();
/* 337 */     for (byte b = 0; b < i; b++) {
/* 338 */       char c = paramString.charAt(b);
/* 339 */       String str1 = Integer.toHexString(c);
/* 340 */       str = str + str1;
/*     */     } 
/* 342 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String hexStringToString(String paramString, int paramInt) {
/* 355 */     String str = "";
/* 356 */     int i = paramString.length() / paramInt;
/* 357 */     for (byte b = 0; b < i; b++) {
/* 358 */       char c = (char)hexStringToAlgorism(paramString
/* 359 */           .substring(b * paramInt, (b + 1) * paramInt));
/* 360 */       str = str + c;
/*     */     } 
/* 362 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static int hexStringToAlgorism(String paramString) {
/* 373 */     paramString = paramString.toUpperCase();
/* 374 */     int i = paramString.length();
/* 375 */     int j = 0;
/* 376 */     for (int k = i; k > 0; k--) {
/* 377 */       char c = paramString.charAt(k - 1);
/* 378 */       int m = 0;
/* 379 */       if (c >= '0' && c <= '9') {
/* 380 */         m = c - 48;
/*     */       } else {
/* 382 */         m = c - 55;
/*     */       } 
/* 384 */       j = (int)(j + Math.pow(16.0D, (i - k)) * m);
/*     */     } 
/* 386 */     return j;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String hexStringToBinary(String paramString) {
/* 397 */     paramString = paramString.toUpperCase();
/* 398 */     String str = "";
/* 399 */     int i = paramString.length();
/* 400 */     for (byte b = 0; b < i; b++) {
/* 401 */       char c = paramString.charAt(b);
/* 402 */       switch (c) {
/*     */         case '0':
/* 404 */           str = str + "0000";
/*     */           break;
/*     */         case '1':
/* 407 */           str = str + "0001";
/*     */           break;
/*     */         case '2':
/* 410 */           str = str + "0010";
/*     */           break;
/*     */         case '3':
/* 413 */           str = str + "0011";
/*     */           break;
/*     */         case '4':
/* 416 */           str = str + "0100";
/*     */           break;
/*     */         case '5':
/* 419 */           str = str + "0101";
/*     */           break;
/*     */         case '6':
/* 422 */           str = str + "0110";
/*     */           break;
/*     */         case '7':
/* 425 */           str = str + "0111";
/*     */           break;
/*     */         case '8':
/* 428 */           str = str + "1000";
/*     */           break;
/*     */         case '9':
/* 431 */           str = str + "1001";
/*     */           break;
/*     */         case 'A':
/* 434 */           str = str + "1010";
/*     */           break;
/*     */         case 'B':
/* 437 */           str = str + "1011";
/*     */           break;
/*     */         case 'C':
/* 440 */           str = str + "1100";
/*     */           break;
/*     */         case 'D':
/* 443 */           str = str + "1101";
/*     */           break;
/*     */         case 'E':
/* 446 */           str = str + "1110";
/*     */           break;
/*     */         case 'F':
/* 449 */           str = str + "1111";
/*     */           break;
/*     */       } 
/*     */     } 
/* 453 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String AsciiStringToString(String paramString) {
/* 464 */     String str = "";
/* 465 */     int i = paramString.length() / 2;
/* 466 */     for (byte b = 0; b < i; b++) {
/* 467 */       String str1 = paramString.substring(b * 2, b * 2 + 2);
/* 468 */       int j = hexStringToAlgorism(str1);
/* 469 */       char c = (char)j;
/* 470 */       String str2 = String.valueOf(c);
/* 471 */       str = str + str2;
/*     */     } 
/* 473 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String algorismToHexString(int paramInt1, int paramInt2) {
/* 486 */     String str = "";
/* 487 */     str = Integer.toHexString(paramInt1);
/*     */     
/* 489 */     if (str.length() % 2 == 1) {
/* 490 */       str = "0" + str;
/*     */     }
/* 492 */     return patchHexString(str.toUpperCase(), paramInt2);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String byteToString(byte[] paramArrayOfbyte) {
/* 503 */     String str = "";
/*     */ 
/*     */     
/* 506 */     int i = paramArrayOfbyte.length;
/* 507 */     for (byte b = 0; b < i; b++) {
/* 508 */       char c = (char)paramArrayOfbyte[b];
/* 509 */       str = str + c;
/*     */     } 
/* 511 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static int binaryToAlgorism(String paramString) {
/* 522 */     int i = paramString.length();
/* 523 */     int j = 0;
/* 524 */     for (int k = i; k > 0; k--) {
/* 525 */       char c = paramString.charAt(k - 1);
/* 526 */       int m = c - 48;
/* 527 */       j = (int)(j + Math.pow(2.0D, (i - k)) * m);
/*     */     } 
/* 529 */     return j;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String algorismToHEXString(int paramInt) {
/* 540 */     String str = "";
/* 541 */     str = Integer.toHexString(paramInt);
/*     */     
/* 543 */     if (str.length() % 2 == 1) {
/* 544 */       str = "0" + str;
/*     */     }
/*     */     
/* 547 */     str = str.toUpperCase();
/*     */     
/* 549 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String patchHexString(String paramString, int paramInt) {
/* 562 */     String str = "";
/* 563 */     for (byte b = 0; b < paramInt - paramString.length(); b++) {
/* 564 */       str = "0" + str;
/*     */     }
/* 566 */     paramString = (str + paramString).substring(0, paramInt);
/* 567 */     return paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static int parseToInt(String paramString, int paramInt1, int paramInt2) {
/* 582 */     int i = 0;
/*     */     try {
/* 584 */       i = Integer.parseInt(paramString, paramInt2);
/* 585 */     } catch (NumberFormatException numberFormatException) {
/* 586 */       i = paramInt1;
/*     */     } 
/* 588 */     return i;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static int parseToInt(String paramString, int paramInt) {
/* 601 */     int i = 0;
/*     */     try {
/* 603 */       i = Integer.parseInt(paramString);
/* 604 */     } catch (NumberFormatException numberFormatException) {
/* 605 */       i = paramInt;
/*     */     } 
/* 607 */     return i;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static byte[] hexToByte(String paramString) throws IllegalArgumentException {
/* 617 */     if (paramString.length() % 2 != 0) {
/* 618 */       throw new IllegalArgumentException();
/*     */     }
/* 620 */     char[] arrayOfChar = paramString.toCharArray();
/* 621 */     byte[] arrayOfByte = new byte[paramString.length() / 2]; byte b1, b2; int i;
/* 622 */     for (b1 = 0, b2 = 0, i = paramString.length(); b1 < i; b1++, b2++) {
/* 623 */       String str = "" + arrayOfChar[b1++] + arrayOfChar[b1];
/* 624 */       int j = Integer.parseInt(str, 16) & 0xFF;
/* 625 */       arrayOfByte[b2] = (new Integer(j)).byteValue();
/*     */     } 
/* 627 */     return arrayOfByte;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String byteToHex(byte[] paramArrayOfbyte) {
/* 638 */     if (paramArrayOfbyte == null) {
/* 639 */       throw new IllegalArgumentException("Argument b ( byte array ) is null! ");
/*     */     }
/*     */     
/* 642 */     String str1 = "";
/* 643 */     String str2 = "";
/* 644 */     for (byte b = 0; b < paramArrayOfbyte.length; b++) {
/* 645 */       str2 = Integer.toHexString(paramArrayOfbyte[b] & 0xFF);
/* 646 */       if (str2.length() == 1) {
/* 647 */         str1 = str1 + "0" + str2;
/*     */       } else {
/* 649 */         str1 = str1 + str2;
/*     */       } 
/*     */     } 
/* 652 */     return str1.toUpperCase();
/*     */   }
/*     */   
/*     */   public static byte[] subByte(byte[] paramArrayOfbyte, int paramInt1, int paramInt2) {
/* 656 */     byte[] arrayOfByte = new byte[paramInt2];
/* 657 */     for (byte b = 0; b < paramInt2; b++) {
/* 658 */       arrayOfByte[b] = paramArrayOfbyte[b + paramInt1];
/*     */     }
/* 660 */     return arrayOfByte;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/sm/Util.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */