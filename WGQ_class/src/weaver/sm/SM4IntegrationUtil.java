/*    */ package weaver.sm;
/*    */ 
/*    */ import weaver.general.BaseBean;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class SM4IntegrationUtil
/*    */ {
/* 17 */   public static String SECRET_KEY = "60fb3ed9-3e18-4f";
/*    */   
/*    */   public static boolean USE_SM4 = false;
/*    */   
/* 21 */   private static SM4Utils sm4Utils = new SM4Utils();
/*    */   
/*    */   static {
/* 24 */     USE_SM4 = "SM4".equalsIgnoreCase((new BaseBean()).getPropValue("weaver_security_type", "reversible_enc_type"));
/* 25 */     SECRET_KEY = (new BaseBean()).getPropValue("SM4_SECRET_KEY", "secret_key");
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public static String decrypt(String paramString) {
/* 34 */     return sm4Utils.decrypt(paramString, SECRET_KEY);
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public static String encrypt(String paramString) {
/* 43 */     return sm4Utils.encrypt(paramString, SECRET_KEY);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/sm/SM4IntegrationUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */