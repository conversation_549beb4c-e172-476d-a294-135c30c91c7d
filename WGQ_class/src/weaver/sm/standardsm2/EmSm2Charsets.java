/*    */ package weaver.sm.standardsm2;
/*    */ 
/*    */ import java.nio.charset.Charset;
/*    */ 
/*    */ public class EmSm2Charsets
/*    */ {
/*  7 */   public static final Charset ISO_8859_1 = Charset.forName("ISO-8859-1");
/*    */   
/*  9 */   public static final Charset US_ASCII = Charset.forName("US-ASCII");
/*    */   
/* 11 */   public static final Charset UTF_16 = Charset.forName("UTF-16");
/*    */   
/* 13 */   public static final Charset UTF_16BE = Charset.forName("UTF-16BE");
/*    */   
/* 15 */   public static final Charset UTF_16LE = Charset.forName("UTF-16LE");
/*    */   
/* 17 */   public static final Charset UTF_8 = Charset.forName("UTF-8");
/*    */ 
/*    */   
/*    */   public static Charset toCharset(Charset paramCharset) {
/* 21 */     return (paramCharset == null) ? Charset.defaultCharset() : paramCharset;
/*    */   }
/*    */ 
/*    */   
/*    */   public static Charset toCharset(String paramString) {
/* 26 */     return (paramString == null) ? Charset.defaultCharset() : Charset.forName(paramString);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/sm/standardsm2/EmSm2Charsets.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */