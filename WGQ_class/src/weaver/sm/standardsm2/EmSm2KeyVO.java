/*    */ package weaver.sm.standardsm2;
/*    */ 
/*    */ import java.io.File;
/*    */ import java.io.FileInputStream;
/*    */ import java.io.FileNotFoundException;
/*    */ import java.io.IOException;
/*    */ import java.math.BigInteger;
/*    */ import java.nio.charset.Charset;
/*    */ import org.bouncycastle.math.ec.ECPoint;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class EmSm2KeyVO
/*    */ {
/*    */   private static final String PUB_KEY_NAME = "em_sm2_2048_public.key";
/*    */   private static final String PRI_KEY_NAME = "em_sm2_2048_private.key";
/*    */   public static final String UTF8_ENCODING = "UTF-8";
/* 20 */   private static String rootPath = EmSm2EncDecUtils.getProjectPath();
/*    */ 
/*    */ 
/*    */   
/*    */   private BigInteger privateKey;
/*    */ 
/*    */   
/*    */   private ECPoint publicKey;
/*    */ 
/*    */ 
/*    */   
/*    */   public BigInteger getPrivateKey() {
/* 32 */     return this.privateKey;
/*    */   }
/*    */   
/*    */   public void setPrivateKey(BigInteger paramBigInteger) {
/* 36 */     this.privateKey = paramBigInteger;
/*    */   }
/*    */   
/*    */   public ECPoint getPublicKey() {
/* 40 */     return this.publicKey;
/*    */   }
/*    */   
/*    */   public void setPublicKey(ECPoint paramECPoint) {
/* 44 */     this.publicKey = paramECPoint;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getPubHexInSoft() throws IOException {
/* 52 */     return readFileToString(new File(rootPath + "keys" + File.separator + "em_sm2_2048_public.key"), Charset.forName("UTF-8"));
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public String getPriHexInSoft() throws IOException {
/* 58 */     return readFileToString(new File(rootPath + "keys" + File.separator + "em_sm2_2048_private.key"), Charset.forName("UTF-8"));
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String innerGetPublicKey() {
/* 68 */     return EmSm2Util.byteToHex(this.publicKey.getEncoded(false));
/*    */   }
/*    */   
/*    */   public String innerGetPrivateKey() {
/* 72 */     return EmSm2Util.byteToHex(this.privateKey.toByteArray());
/*    */   }
/*    */ 
/*    */   
/*    */   public static String readFileToString(File paramFile, Charset paramCharset) throws IOException {
/* 77 */     FileInputStream fileInputStream = null;
/*    */     try {
/* 79 */       fileInputStream = openInputStream(paramFile);
/* 80 */       return EmSm2IOUtils.toString(fileInputStream, (paramCharset == null) ? Charset.defaultCharset() : paramCharset);
/*    */     } finally {
/* 82 */       EmSm2IOUtils.closeQuietly(fileInputStream);
/*    */     } 
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public static FileInputStream openInputStream(File paramFile) throws IOException {
/* 89 */     if (paramFile.exists()) {
/* 90 */       if (paramFile.isDirectory()) {
/* 91 */         throw new IOException("File '" + paramFile + "' exists but is a directory");
/*    */       }
/* 93 */       if (!paramFile.canRead()) {
/* 94 */         throw new IOException("File '" + paramFile + "' cannot be read");
/*    */       }
/*    */     } else {
/* 97 */       throw new FileNotFoundException("File '" + paramFile + "' does not exist");
/*    */     } 
/* 99 */     return new FileInputStream(paramFile);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/sm/standardsm2/EmSm2KeyVO.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */