/*    */ package weaver.sm.standardsm2;
/*    */ 
/*    */ import java.io.IOException;
/*    */ import java.io.Serializable;
/*    */ import java.io.Writer;
/*    */ 
/*    */ 
/*    */ 
/*    */ public class EmSm2StringBuilderWriter
/*    */   extends Writer
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 7917209502356755843L;
/*    */   private final StringBuilder builder;
/*    */   
/*    */   public EmSm2StringBuilderWriter() {
/* 17 */     this.builder = new StringBuilder();
/*    */   }
/*    */ 
/*    */   
/*    */   public EmSm2StringBuilderWriter(int paramInt) {
/* 22 */     this.builder = new StringBuilder(paramInt);
/*    */   }
/*    */ 
/*    */   
/*    */   public EmSm2StringBuilderWriter(StringBuilder paramStringBuilder) {
/* 27 */     this.builder = (paramStringBuilder != null) ? paramStringBuilder : new StringBuilder();
/*    */   }
/*    */ 
/*    */   
/*    */   public Writer append(char paramChar) {
/* 32 */     this.builder.append(paramChar);
/* 33 */     return this;
/*    */   }
/*    */ 
/*    */   
/*    */   public Writer append(CharSequence paramCharSequence) {
/* 38 */     this.builder.append(paramCharSequence);
/* 39 */     return this;
/*    */   }
/*    */ 
/*    */   
/*    */   public Writer append(CharSequence paramCharSequence, int paramInt1, int paramInt2) {
/* 44 */     this.builder.append(paramCharSequence, paramInt1, paramInt2);
/* 45 */     return this;
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public void close() {}
/*    */ 
/*    */ 
/*    */   
/*    */   public void flush() {}
/*    */ 
/*    */   
/*    */   public void write(String paramString) {
/* 58 */     if (paramString != null) {
/* 59 */       this.builder.append(paramString);
/*    */     }
/*    */   }
/*    */   
/*    */   public void write(char[] paramArrayOfchar, int paramInt1, int paramInt2) {
/* 64 */     if (paramArrayOfchar != null) {
/* 65 */       this.builder.append(paramArrayOfchar, paramInt1, paramInt2);
/*    */     }
/*    */   }
/*    */   
/*    */   public StringBuilder getBuilder() {
/* 70 */     return this.builder;
/*    */   }
/*    */ 
/*    */   
/*    */   public String toString() {
/* 75 */     return this.builder.toString();
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/sm/standardsm2/EmSm2StringBuilderWriter.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */