/*    */ package weaver.sm.standardsm2;
/*    */ 
/*    */ import cfca.sadk.algorithm.sm2.SM2PrivateKey;
/*    */ import cfca.sadk.algorithm.sm2.SM2PublicKey;
/*    */ import cfca.sadk.cgb.toolkit.SM2Toolkit;
/*    */ import java.security.KeyPair;
/*    */ import org.apache.commons.codec.binary.Base64;
/*    */ import org.slf4j.Logger;
/*    */ import org.slf4j.LoggerFactory;
/*    */ 
/*    */ 
/*    */ public class EmSm2KeyGenerator
/*    */ {
/* 14 */   private static Logger logger = LoggerFactory.getLogger(EmSm2KeyGenerator.class);
/*    */   
/*    */   private byte[] privateKey;
/*    */   
/*    */   private byte[] publicKey;
/*    */   
/*    */   public EmSm2KeyGenerator() {
/*    */     try {
/* 22 */       KeyPair keyPair = (new SM2Toolkit()).SM2GenerateKeyPair();
/* 23 */       SM2PrivateKey sM2PrivateKey = (SM2PrivateKey)keyPair.getPrivate();
/* 24 */       SM2PublicKey sM2PublicKey = (SM2PublicKey)keyPair.getPublic();
/*    */ 
/*    */       
/* 27 */       this.privateKey = Base64.encodeBase64(sM2PrivateKey.getDByBytes());
/* 28 */       this.publicKey = Base64.encodeBase64(sM2PublicKey.getQ().getEncoded(false));
/* 29 */     } catch (Throwable throwable) {
/* 30 */       logger.error(throwable.getMessage(), throwable);
/*    */     } 
/*    */   }
/*    */   
/*    */   public byte[] getPrivateKey() {
/* 35 */     return this.privateKey;
/*    */   }
/*    */   
/*    */   public byte[] getPublicKey() {
/* 39 */     return this.publicKey;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getPrivateKeyString() {
/* 47 */     return new String(this.privateKey);
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getPublicKeyString() {
/* 55 */     return new String(this.publicKey);
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public static void main(String[] paramArrayOfString) throws Exception {
/* 63 */     EmSm2KeyGenerator emSm2KeyGenerator = new EmSm2KeyGenerator();
/* 64 */     System.out.println("私钥:" + emSm2KeyGenerator.getPrivateKeyString());
/* 65 */     System.out.println("公钥:" + emSm2KeyGenerator.getPublicKeyString());
/*    */     
/* 67 */     System.out.println("---");
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/sm/standardsm2/EmSm2KeyGenerator.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */