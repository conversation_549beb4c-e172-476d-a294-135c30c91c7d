/*     */ package weaver.sm.standardsm2;
/*     */ 
/*     */ import cfca.sadk.algorithm.common.PKIException;
/*     */ import cfca.sadk.algorithm.sm2.SM2PrivateKey;
/*     */ import cfca.sadk.cgb.toolkit.BASE64Toolkit;
/*     */ import cfca.sadk.cgb.toolkit.SM2Toolkit;
/*     */ import java.io.BufferedOutputStream;
/*     */ import java.io.File;
/*     */ import java.io.FileOutputStream;
/*     */ import java.io.IOException;
/*     */ import java.net.URL;
/*     */ import java.net.URLDecoder;
/*     */ import java.nio.charset.Charset;
/*     */ import java.security.PrivateKey;
/*     */ import java.security.SecureRandom;
/*     */ import java.util.HashMap;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import org.apache.commons.codec.binary.Base64;
/*     */ import org.slf4j.Logger;
/*     */ import org.slf4j.LoggerFactory;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class EmSm2EncDecUtils
/*     */ {
/*  29 */   private static final Logger logger = LoggerFactory.getLogger(EmSm2EncDecUtils.class);
/*     */   
/*     */   private static final String PUB_KEY_NAME = "em_sm2_2048_public.key";
/*     */   private static final String PRI_KEY_NAME = "em_sm2_2048_private.key";
/*     */   private static final String UTF8_ENCODING = "UTF-8";
/*  34 */   private HashMap<String, String> cacheData = new HashMap<>(1);
/*     */ 
/*     */ 
/*     */   
/*     */   static {
/*  39 */     String str1 = getProjectPath();
/*  40 */     logger.info("Sm2EncDecUtils============rootPath:::" + str1);
/*  41 */     String str2 = str1 + "keys" + File.separator;
/*     */     
/*  43 */     String str3 = str2 + "em_sm2_2048_public.key";
/*  44 */     String str4 = str2 + "em_sm2_2048_private.key";
/*     */     
/*  46 */     File file1 = new File(str3);
/*  47 */     File file2 = new File(str4);
/*     */     
/*  49 */     if (!file1.exists() || !file2.exists()) {
/*     */       
/*  51 */       file1.delete();
/*  52 */       file2.delete();
/*     */ 
/*     */       
/*  55 */       if (!file1.exists()) {
/*  56 */         boolean bool = createFile(file1);
/*  57 */         if (!bool) {
/*  58 */           logger.error("公钥文件创建失败，请检查创建权限");
/*     */         }
/*     */       } 
/*     */       
/*  62 */       if (!file2.exists()) {
/*  63 */         boolean bool = createFile(file2);
/*  64 */         if (!bool) {
/*  65 */           logger.error("私钥文件创建失败，请检查创建权限");
/*     */         }
/*     */       } 
/*     */ 
/*     */       
/*     */       try {
/*  71 */         EmSm2KeyGenerator emSm2KeyGenerator = new EmSm2KeyGenerator();
/*  72 */         String str5 = emSm2KeyGenerator.getPrivateKeyString();
/*  73 */         String str6 = emSm2KeyGenerator.getPublicKeyString();
/*  74 */         genarateKeyFiles(str2, Charset.forName("UTF-8"), str5, str6);
/*  75 */       } catch (Throwable throwable) {
/*  76 */         logger.error("Sm2EncDecUtils============写入数据失败" + throwable.getMessage());
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String generateSalt(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/*  90 */     SecureRandom secureRandom = new SecureRandom();
/*     */     
/*  92 */     byte[] arrayOfByte = new byte[4];
/*  93 */     secureRandom.nextBytes(arrayOfByte);
/*  94 */     StringBuffer stringBuffer = new StringBuffer();
/*  95 */     for (byte b = 0; b < arrayOfByte.length; b++) {
/*  96 */       String str = Integer.toHexString(0xFF & arrayOfByte[b]);
/*  97 */       if (str.length() == 1)
/*  98 */         stringBuffer.append('0'); 
/*  99 */       stringBuffer.append(str);
/*     */     } 
/* 101 */     if (paramHttpServletRequest != null)
/*     */     {
/* 103 */       paramHttpServletRequest.getSession().setAttribute("sm2_code", stringBuffer.toString());
/*     */     }
/* 105 */     return stringBuffer.toString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static void genarateKeyFiles(String paramString1, Charset paramCharset, String paramString2, String paramString3) throws Throwable {
/* 116 */     writeDataToFile(new File(paramString1 + "em_sm2_2048_public.key"), paramString3);
/* 117 */     writeDataToFile(new File(paramString1 + "em_sm2_2048_private.key"), paramString2);
/*     */   }
/*     */ 
/*     */   
/*     */   private static void writeDataToFile(File paramFile, String paramString) throws Exception {
/* 122 */     BufferedOutputStream bufferedOutputStream = null;
/*     */     try {
/* 124 */       bufferedOutputStream = new BufferedOutputStream(new FileOutputStream(paramFile));
/* 125 */       byte[] arrayOfByte = paramString.getBytes("UTF-8");
/* 126 */       bufferedOutputStream.write(arrayOfByte, 0, arrayOfByte.length);
/* 127 */       bufferedOutputStream.flush();
/*     */     } finally {
/* 129 */       if (bufferedOutputStream != null) {
/* 130 */         bufferedOutputStream.close();
/*     */       }
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static boolean createFile(File paramFile) {
/* 145 */     if (!paramFile.getParentFile().exists()) {
/* 146 */       logger.info("目标文件所在目录不存在，准备创建");
/* 147 */       if (!paramFile.getParentFile().mkdirs()) {
/* 148 */         logger.error("创建目标文件所在目录失败，请检查权限");
/* 149 */         return false;
/*     */       } 
/*     */     } 
/*     */     
/*     */     try {
/* 154 */       if (paramFile.exists() && !paramFile.isDirectory()) {
/* 155 */         return true;
/*     */       }
/*     */       
/* 158 */       if (paramFile.createNewFile()) {
/* 159 */         logger.info("创建单个文件" + paramFile.getPath() + "成功");
/* 160 */         return true;
/*     */       } 
/* 162 */       logger.error("创建单个文件" + paramFile.getPath() + "失败");
/* 163 */       return false;
/*     */     }
/* 165 */     catch (IOException iOException) {
/* 166 */       logger.error("创建单个文件" + paramFile.getPath() + "失败");
/* 167 */       return false;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getProjectPath() {
/* 180 */     URL uRL = EmSm2EncDecUtils.class.getProtectionDomain().getCodeSource().getLocation();
/* 181 */     String str = null;
/*     */     try {
/* 183 */       str = URLDecoder.decode(uRL.getPath(), "utf-8");
/* 184 */     } catch (Exception exception) {
/* 185 */       exception.printStackTrace();
/*     */     } 
/* 187 */     if (str.endsWith(".jar"))
/* 188 */       str = str.substring(0, str.lastIndexOf("/") + 1); 
/* 189 */     System.out.println("before skip real path:::::" + str);
/* 190 */     if (str.startsWith("file:")) {
/* 191 */       str = str.substring(5);
/*     */     }
/* 193 */     System.out.println("skip file:::::" + str);
/* 194 */     int i = str.indexOf("SM2");
/* 195 */     System.out.println("SM2 POS:::" + i);
/* 196 */     if (i != -1) {
/* 197 */       str = str.substring(0, i);
/*     */     }
/* 199 */     if (str.indexOf("classbean") != -1 || str.indexOf("classes") != -1) {
/* 200 */       str = str.replace("/WEB-INF", "");
/* 201 */       str = str.replace("/classes", "");
/* 202 */       str = str.replace("/classbean", "");
/* 203 */       if (!str.endsWith("/")) {
/* 204 */         str = str + "/";
/*     */       }
/* 206 */       str = str + "WEB-INF/lib/";
/*     */     } 
/* 208 */     if (!str.endsWith("/")) {
/* 209 */       str = str + "/";
/*     */     }
/* 211 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String encrypt(byte[] paramArrayOfbyte1, byte[] paramArrayOfbyte2) {
/* 222 */     return null;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String decrypt_js(String paramString1, String paramString2, String paramString3, HttpServletRequest paramHttpServletRequest) {
/* 237 */     logger.info("-----------------------解密开始--------------------");
/* 238 */     logger.info("-----privateKey---" + paramString1 + "-----ciphertext---" + paramString2 + "-----request---" + paramHttpServletRequest);
/*     */     
/*     */     try {
/* 241 */       SM2Toolkit sM2Toolkit = new SM2Toolkit();
/* 242 */       SM2PrivateKey sM2PrivateKey = (SM2PrivateKey)sM2Toolkit.SM2BuildPrivateKey(BASE64Toolkit.encode(Base64.decodeBase64(paramString1)));
/*     */       
/* 244 */       String str1 = new String(sM2Toolkit.SM2DecryptData((PrivateKey)sM2PrivateKey, Base64.decodeBase64(paramString2)), Charset.forName("UTF-8"));
/*     */ 
/*     */       
/* 247 */       String str2 = Thread.currentThread().getName();
/* 248 */       String str3 = (String)paramHttpServletRequest.getSession().getAttribute("sm2_code");
/* 249 */       logger.info("---------sm2Code_session-------" + str3);
/* 250 */       if (str3 != null) {
/* 251 */         str1 = getDecryptData(str3, str1);
/* 252 */         if ((this.cacheData.size() == 0 || !((String)this.cacheData.get("threadName")).equals(str2)) && str1 != null) {
/* 253 */           this.cacheData.put("threadName", str2);
/*     */         }
/* 255 */         paramHttpServletRequest.getSession().setAttribute("sm2_code", null);
/* 256 */       } else if (this.cacheData.get("threadName") != null && ((String)this.cacheData.get("threadName")).equals(str2)) {
/*     */         
/* 258 */         int i = str1.length() - 8;
/* 259 */         str1 = str1.substring(0, i);
/*     */       } else {
/* 261 */         logger.info("------------session为空，可能存在恶意攻击行为----------");
/* 262 */         str1 = null;
/*     */       } 
/*     */       
/* 265 */       return str1;
/* 266 */     } catch (PKIException pKIException) {
/* 267 */       logger.error("解密失败", (Throwable)pKIException);
/* 268 */       return null;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String getDecryptData(String paramString1, String paramString2) {
/* 281 */     int i = paramString2.length() - 8;
/* 282 */     String str1 = paramString2.substring(0, i);
/* 283 */     String str2 = paramString2.substring(i);
/* 284 */     if (str2.equals(paramString1)) {
/* 285 */       return str1;
/*     */     }
/* 287 */     logger.info("------------session中的随机数与密文中的随机数不同----------");
/* 288 */     return null;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/sm/standardsm2/EmSm2EncDecUtils.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */