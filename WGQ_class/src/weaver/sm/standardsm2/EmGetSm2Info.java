/*    */ package weaver.sm.standardsm2;
/*    */ 
/*    */ import java.io.IOException;
/*    */ import java.io.Serializable;
/*    */ import javax.servlet.ServletException;
/*    */ import javax.servlet.http.HttpServlet;
/*    */ import javax.servlet.http.HttpServletRequest;
/*    */ import javax.servlet.http.HttpServletResponse;
/*    */ import org.json.JSONException;
/*    */ import org.json.JSONObject;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class EmGetSm2Info
/*    */   extends HttpServlet
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = -1444538744910431737L;
/*    */   
/*    */   protected void doGet(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws ServletException, IOException {
/* 26 */     doPost(paramHttpServletRequest, paramHttpServletResponse);
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   protected void doPost(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws ServletException, IOException {
/* 32 */     new EmSm2EncDecUtils();
/*    */     
/* 34 */     EmSm2KeyVO emSm2KeyVO = new EmSm2KeyVO();
/* 35 */     JSONObject jSONObject = new JSONObject();
/*    */     try {
/* 37 */       jSONObject.put("sm2_pub", emSm2KeyVO.getPubHexInSoft());
/* 38 */       jSONObject.put("sm2_code", EmSm2EncDecUtils.generateSalt(paramHttpServletRequest, paramHttpServletResponse));
/* 39 */       jSONObject.put("sm2_flag", "``SM2``");
/* 40 */     } catch (JSONException jSONException) {
/* 41 */       jSONException.printStackTrace();
/*    */     } 
/*    */     
/* 44 */     paramHttpServletResponse.setContentType("application/json; charset=UTF-8");
/* 45 */     paramHttpServletResponse.getWriter().println(jSONObject.toString());
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/sm/standardsm2/EmGetSm2Info.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */