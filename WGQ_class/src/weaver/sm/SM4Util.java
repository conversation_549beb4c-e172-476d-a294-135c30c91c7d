/*     */ package weaver.sm;
/*     */ 
/*     */ import java.io.File;
/*     */ import java.io.FileInputStream;
/*     */ import java.io.FileOutputStream;
/*     */ import java.io.IOException;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Arrays;
/*     */ import java.util.List;
/*     */ import org.apache.commons.io.IOUtils;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class SM4Util
/*     */ {
/*     */   private static final int DECRYPT = 0;
/*     */   public static final int ROUND = 32;
/*     */   private static final int BLOCK = 16;
/*  21 */   private static byte[] Sbox = new byte[] { -42, -112, -23, -2, -52, -31, 61, -73, 22, -74, 20, -62, 40, -5, 44, 5, 43, 103, -102, 118, 42, -66, 4, -61, -86, 68, 19, 38, 73, -122, 6, -103, -100, 66, 80, -12, -111, -17, -104, 122, 51, 84, 11, 67, -19, -49, -84, 98, -28, -77, 28, -87, -55, 8, -24, -107, Byte.MIN_VALUE, -33, -108, -6, 117, -113, 63, -90, 71, 7, -89, -4, -13, 115, 23, -70, -125, 89, 60, 25, -26, -123, 79, -88, 104, 107, -127, -78, 113, 100, -38, -117, -8, -21, 15, 75, 112, 86, -99, 53, 30, 36, 14, 94, 99, 88, -47, -94, 37, 34, 124, 59, 1, 33, 120, -121, -44, 0, 70, 87, -97, -45, 39, 82, 76, 54, 2, -25, -96, -60, -56, -98, -22, -65, -118, -46, 64, -57, 56, -75, -93, -9, -14, -50, -7, 97, 21, -95, -32, -82, 93, -92, -101, 52, 26, 85, -83, -109, 50, 48, -11, -116, -79, -29, 29, -10, -30, 46, -126, 102, -54, 96, -64, 41, 35, -85, 13, 83, 78, 111, -43, -37, 55, 69, -34, -3, -114, 47, 3, -1, 106, 114, 109, 108, 91, 81, -115, 27, -81, -110, -69, -35, -68, Byte.MAX_VALUE, 17, -39, 92, 65, 31, 16, 90, -40, 10, -63, 49, -120, -91, -51, 123, -67, 45, 116, -48, 18, -72, -27, -76, -80, -119, 105, -105, 74, 12, -106, 119, 126, 101, -71, -15, 9, -59, 110, -58, -124, 24, -16, 125, -20, 58, -36, 77, 32, 121, -18, 95, 62, -41, -53, 57, 72 };
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  40 */   private static int[] CK = new int[] { 462357, 472066609, 943670861, 1415275113, 1886879365, -1936483679, -1464879427, -993275175, -521670923, -66909679, 404694573, 876298825, 1347903077, 1819507329, -2003855715, -1532251463, -1060647211, -589042959, -117504499, 337322537, 808926789, 1280531041, 1752135293, -2071227751, -1599623499, -1128019247, -656414995, -184876535, 269950501, 741554753, 1213159005, 1684763257 };
/*     */ 
/*     */ 
/*     */   
/*     */   private static int Rotl(int paramInt1, int paramInt2) {
/*  45 */     return paramInt1 << paramInt2 | paramInt1 >>> 32 - paramInt2;
/*     */   }
/*     */   
/*     */   private static int ByteSub(int paramInt) {
/*  49 */     return (Sbox[paramInt >>> 24 & 0xFF] & 0xFF) << 24 | (Sbox[paramInt >>> 16 & 0xFF] & 0xFF) << 16 | (Sbox[paramInt >>> 8 & 0xFF] & 0xFF) << 8 | Sbox[paramInt & 0xFF] & 0xFF;
/*     */   }
/*     */   
/*     */   private static int L1(int paramInt) {
/*  53 */     return paramInt ^ Rotl(paramInt, 2) ^ Rotl(paramInt, 10) ^ Rotl(paramInt, 18) ^ Rotl(paramInt, 24);
/*     */   }
/*     */   
/*     */   private static int L2(int paramInt) {
/*  57 */     return paramInt ^ Rotl(paramInt, 13) ^ Rotl(paramInt, 23);
/*     */   }
/*     */ 
/*     */   
/*     */   static void SMS4Crypt(byte[] paramArrayOfbyte1, byte[] paramArrayOfbyte2, int[] paramArrayOfint) {
/*  62 */     int[] arrayOfInt1 = new int[4];
/*  63 */     int[] arrayOfInt2 = new int[4]; byte b2;
/*  64 */     for (b2 = 0; b2 < 4; b2++) {
/*  65 */       arrayOfInt2[0] = paramArrayOfbyte1[0 + 4 * b2] & 0xFF;
/*  66 */       arrayOfInt2[1] = paramArrayOfbyte1[1 + 4 * b2] & 0xFF;
/*  67 */       arrayOfInt2[2] = paramArrayOfbyte1[2 + 4 * b2] & 0xFF;
/*  68 */       arrayOfInt2[3] = paramArrayOfbyte1[3 + 4 * b2] & 0xFF;
/*  69 */       arrayOfInt1[b2] = arrayOfInt2[0] << 24 | arrayOfInt2[1] << 16 | arrayOfInt2[2] << 8 | arrayOfInt2[3];
/*     */     } 
/*  71 */     for (byte b1 = 0; b1 < 32; b1 += 4) {
/*  72 */       int i = arrayOfInt1[1] ^ arrayOfInt1[2] ^ arrayOfInt1[3] ^ paramArrayOfint[b1 + 0];
/*  73 */       i = ByteSub(i);
/*  74 */       arrayOfInt1[0] = arrayOfInt1[0] ^ L1(i);
/*     */       
/*  76 */       i = arrayOfInt1[2] ^ arrayOfInt1[3] ^ arrayOfInt1[0] ^ paramArrayOfint[b1 + 1];
/*  77 */       i = ByteSub(i);
/*  78 */       arrayOfInt1[1] = arrayOfInt1[1] ^ L1(i);
/*     */       
/*  80 */       i = arrayOfInt1[3] ^ arrayOfInt1[0] ^ arrayOfInt1[1] ^ paramArrayOfint[b1 + 2];
/*  81 */       i = ByteSub(i);
/*  82 */       arrayOfInt1[2] = arrayOfInt1[2] ^ L1(i);
/*     */       
/*  84 */       i = arrayOfInt1[0] ^ arrayOfInt1[1] ^ arrayOfInt1[2] ^ paramArrayOfint[b1 + 3];
/*  85 */       i = ByteSub(i);
/*  86 */       arrayOfInt1[3] = arrayOfInt1[3] ^ L1(i);
/*     */     } 
/*     */     
/*  89 */     for (b2 = 0; b2 < 16; b2 += 4) {
/*  90 */       paramArrayOfbyte2[b2] = (byte)(arrayOfInt1[3 - b2 / 4] >>> 24 & 0xFF);
/*  91 */       paramArrayOfbyte2[b2 + 1] = (byte)(arrayOfInt1[3 - b2 / 4] >>> 16 & 0xFF);
/*  92 */       paramArrayOfbyte2[b2 + 2] = (byte)(arrayOfInt1[3 - b2 / 4] >>> 8 & 0xFF);
/*  93 */       paramArrayOfbyte2[b2 + 3] = (byte)(arrayOfInt1[3 - b2 / 4] & 0xFF);
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   private static void SMS4KeyExt(byte[] paramArrayOfbyte, int[] paramArrayOfint, int paramInt) {
/*  99 */     int[] arrayOfInt1 = new int[4];
/* 100 */     int[] arrayOfInt2 = new int[4];
/* 101 */     for (byte b2 = 0; b2 < 4; b2++) {
/* 102 */       arrayOfInt2[0] = paramArrayOfbyte[0 + 4 * b2] & 0xFF;
/* 103 */       arrayOfInt2[1] = paramArrayOfbyte[1 + 4 * b2] & 0xFF;
/* 104 */       arrayOfInt2[2] = paramArrayOfbyte[2 + 4 * b2] & 0xFF;
/* 105 */       arrayOfInt2[3] = paramArrayOfbyte[3 + 4 * b2] & 0xFF;
/* 106 */       arrayOfInt1[b2] = arrayOfInt2[0] << 24 | arrayOfInt2[1] << 16 | arrayOfInt2[2] << 8 | arrayOfInt2[3];
/*     */     } 
/* 108 */     arrayOfInt1[0] = arrayOfInt1[0] ^ 0xA3B1BAC6;
/* 109 */     arrayOfInt1[1] = arrayOfInt1[1] ^ 0x56AA3350;
/* 110 */     arrayOfInt1[2] = arrayOfInt1[2] ^ 0x677D9197;
/* 111 */     arrayOfInt1[3] = arrayOfInt1[3] ^ 0xB27022DC; byte b1;
/* 112 */     for (b1 = 0; b1 < 32; b1 += 4) {
/* 113 */       int i = arrayOfInt1[1] ^ arrayOfInt1[2] ^ arrayOfInt1[3] ^ CK[b1 + 0];
/* 114 */       i = ByteSub(i);
/* 115 */       arrayOfInt1[0] = arrayOfInt1[0] ^ L2(i); paramArrayOfint[b1 + 0] = arrayOfInt1[0] ^ L2(i);
/*     */       
/* 117 */       i = arrayOfInt1[2] ^ arrayOfInt1[3] ^ arrayOfInt1[0] ^ CK[b1 + 1];
/* 118 */       i = ByteSub(i);
/* 119 */       arrayOfInt1[1] = arrayOfInt1[1] ^ L2(i); paramArrayOfint[b1 + 1] = arrayOfInt1[1] ^ L2(i);
/*     */       
/* 121 */       i = arrayOfInt1[3] ^ arrayOfInt1[0] ^ arrayOfInt1[1] ^ CK[b1 + 2];
/* 122 */       i = ByteSub(i);
/* 123 */       arrayOfInt1[2] = arrayOfInt1[2] ^ L2(i); paramArrayOfint[b1 + 2] = arrayOfInt1[2] ^ L2(i);
/*     */       
/* 125 */       i = arrayOfInt1[0] ^ arrayOfInt1[1] ^ arrayOfInt1[2] ^ CK[b1 + 3];
/* 126 */       i = ByteSub(i);
/* 127 */       arrayOfInt1[3] = arrayOfInt1[3] ^ L2(i); paramArrayOfint[b1 + 3] = arrayOfInt1[3] ^ L2(i);
/*     */     } 
/*     */     
/* 130 */     if (paramInt == 0) {
/* 131 */       for (b1 = 0; b1 < 16; b1++) {
/* 132 */         int i = paramArrayOfint[b1];
/* 133 */         paramArrayOfint[b1] = paramArrayOfint[31 - b1];
/* 134 */         paramArrayOfint[31 - b1] = i;
/*     */       } 
/*     */     }
/*     */   }
/*     */   
/*     */   private static int sms4(byte[] paramArrayOfbyte1, int paramInt1, byte[] paramArrayOfbyte2, byte[] paramArrayOfbyte3, int paramInt2) {
/* 140 */     byte b = 0;
/* 141 */     int[] arrayOfInt = new int[32];
/* 142 */     SMS4KeyExt(paramArrayOfbyte2, arrayOfInt, paramInt2);
/* 143 */     byte[] arrayOfByte1 = new byte[16];
/* 144 */     byte[] arrayOfByte2 = new byte[16];
/*     */     
/* 146 */     while (paramInt1 >= 16) {
/* 147 */       arrayOfByte1 = Arrays.copyOfRange(paramArrayOfbyte1, b, b + 16);
/* 148 */       SMS4Crypt(arrayOfByte1, arrayOfByte2, arrayOfInt);
/* 149 */       System.arraycopy(arrayOfByte2, 0, paramArrayOfbyte3, b, 16);
/* 150 */       paramInt1 -= 16;
/* 151 */       b += 16;
/*     */     } 
/*     */     
/* 154 */     return 0;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static byte[] encode16(byte[] paramArrayOfbyte1, byte[] paramArrayOfbyte2) {
/* 165 */     byte[] arrayOfByte = new byte[16];
/* 166 */     sms4(paramArrayOfbyte1, 16, paramArrayOfbyte2, arrayOfByte, 1);
/* 167 */     return arrayOfByte;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static byte[] decode16(byte[] paramArrayOfbyte1, byte[] paramArrayOfbyte2) {
/* 178 */     byte[] arrayOfByte = new byte[16];
/* 179 */     sms4(paramArrayOfbyte1, 16, paramArrayOfbyte2, arrayOfByte, 0);
/* 180 */     return arrayOfByte;
/*     */   }
/*     */   
/*     */   protected void encode(String paramString1, String paramString2) throws IOException {
/* 184 */     byte[] arrayOfByte1 = { 1, 35, 69, 103, -119, -85, -51, -17, -2, -36, -70, -104, 118, 84, 50, 16 };
/*     */     
/* 186 */     File file1 = new File(paramString1);
/* 187 */     File file2 = new File(paramString2);
/*     */     
/* 189 */     FileInputStream fileInputStream = new FileInputStream(file1);
/* 190 */     byte[] arrayOfByte2 = IOUtils.toByteArray(fileInputStream);
/* 191 */     List<byte[]> list = splitBytes(arrayOfByte2, 16);
/* 192 */     byte[] arrayOfByte3 = new byte[list.size() * 16];
/* 193 */     int i = 0;
/* 194 */     for (byte[] arrayOfByte4 : list) {
/* 195 */       byte[] arrayOfByte5 = encode16(arrayOfByte4, arrayOfByte1);
/* 196 */       System.arraycopy(arrayOfByte5, 0, arrayOfByte3, i, arrayOfByte5.length);
/* 197 */       i += arrayOfByte4.length;
/*     */     } 
/*     */     
/* 200 */     FileOutputStream fileOutputStream = new FileOutputStream(file2);
/* 201 */     IOUtils.write(arrayOfByte3, fileOutputStream);
/* 202 */     if (fileInputStream != null) {
/* 203 */       fileInputStream.close();
/*     */     }
/* 205 */     if (fileOutputStream != null) {
/* 206 */       fileOutputStream.close();
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   protected static byte[] encode(byte[] paramArrayOfbyte1, byte[] paramArrayOfbyte2) throws IOException {
/*     */     try {
/* 220 */       List<byte[]> list = splitBytes(paramArrayOfbyte1, 16);
/* 221 */       byte[] arrayOfByte = new byte[list.size() * 16];
/* 222 */       int i = 0;
/* 223 */       for (byte[] arrayOfByte1 : list) {
/* 224 */         byte[] arrayOfByte2 = encode16(arrayOfByte1, paramArrayOfbyte2);
/* 225 */         System.arraycopy(arrayOfByte2, 0, arrayOfByte, i, arrayOfByte2.length);
/* 226 */         i += arrayOfByte1.length;
/*     */       } 
/* 228 */       return arrayOfByte;
/* 229 */     } catch (Exception exception) {
/* 230 */       exception.printStackTrace();
/*     */       
/* 232 */       return null;
/*     */     } 
/*     */   }
/*     */   protected void decode(String paramString1, String paramString2, int paramInt) throws IOException {
/* 236 */     byte[] arrayOfByte1 = { 1, 35, 69, 103, -119, -85, -51, -17, -2, -36, -70, -104, 118, 84, 50, 16 };
/*     */     
/* 238 */     File file1 = new File(paramString1);
/* 239 */     File file2 = new File(paramString2);
/* 240 */     FileInputStream fileInputStream = new FileInputStream(file1);
/* 241 */     byte[] arrayOfByte2 = IOUtils.toByteArray(fileInputStream);
/* 242 */     List<byte[]> list = splitBytes(arrayOfByte2, 16);
/* 243 */     byte[] arrayOfByte3 = new byte[list.size() * 16];
/* 244 */     int i = 0;
/* 245 */     for (byte[] arrayOfByte5 : list) {
/* 246 */       byte[] arrayOfByte6 = decode16(arrayOfByte5, arrayOfByte1);
/* 247 */       System.arraycopy(arrayOfByte6, 0, arrayOfByte3, i, arrayOfByte6.length);
/* 248 */       i += arrayOfByte5.length;
/*     */     } 
/*     */     
/* 251 */     byte[] arrayOfByte4 = new byte[paramInt];
/*     */     
/* 253 */     System.arraycopy(arrayOfByte3, 0, arrayOfByte4, 0, paramInt);
/*     */     
/* 255 */     FileOutputStream fileOutputStream = new FileOutputStream(file2);
/* 256 */     IOUtils.write(arrayOfByte4, fileOutputStream);
/* 257 */     if (fileInputStream != null) {
/* 258 */       fileInputStream.close();
/*     */     }
/* 260 */     if (fileOutputStream != null) {
/* 261 */       fileOutputStream.close();
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   protected static byte[] decode(byte[] paramArrayOfbyte1, byte[] paramArrayOfbyte2, int paramInt) throws IOException {
/* 274 */     List<byte[]> list = splitBytes(paramArrayOfbyte1, 16);
/* 275 */     byte[] arrayOfByte1 = new byte[list.size() * 16];
/* 276 */     int i = 0;
/* 277 */     for (byte[] arrayOfByte3 : list) {
/* 278 */       byte[] arrayOfByte4 = decode16(arrayOfByte3, paramArrayOfbyte2);
/* 279 */       System.arraycopy(arrayOfByte4, 0, arrayOfByte1, i, arrayOfByte4.length);
/* 280 */       i += arrayOfByte3.length;
/*     */     } 
/* 282 */     byte[] arrayOfByte2 = new byte[paramInt];
/* 283 */     System.arraycopy(arrayOfByte1, 0, arrayOfByte2, 0, paramInt);
/* 284 */     return arrayOfByte2;
/*     */   }
/*     */   
/*     */   private static List<byte[]> splitBytes(byte[] paramArrayOfbyte, int paramInt) {
/* 288 */     int i = (paramArrayOfbyte.length % paramInt == 0) ? (paramArrayOfbyte.length / paramInt) : (paramArrayOfbyte.length / paramInt + 1);
/* 289 */     ArrayList<byte[]> arrayList = new ArrayList();
/* 290 */     for (byte b = 0; b < i; b++) {
/* 291 */       int j = b * paramInt;
/* 292 */       if (j + paramInt > paramArrayOfbyte.length) {
/* 293 */         paramInt = paramArrayOfbyte.length % paramInt;
/*     */       }
/* 295 */       byte[] arrayOfByte = new byte[paramInt];
/* 296 */       System.arraycopy(paramArrayOfbyte, j, arrayOfByte, 0, paramInt);
/* 297 */       arrayList.add(arrayOfByte);
/*     */     } 
/* 299 */     return (List<byte[]>)arrayList;
/*     */   }
/*     */   public static byte[] encodeBytes(byte[] paramArrayOfbyte1, byte[] paramArrayOfbyte2) throws IOException {
/* 302 */     List<byte[]> list = splitBytes(paramArrayOfbyte1, 16);
/* 303 */     byte[] arrayOfByte = new byte[list.size() * 16];
/* 304 */     int i = 0;
/* 305 */     for (byte[] arrayOfByte1 : list) {
/* 306 */       byte[] arrayOfByte2 = encode16(arrayOfByte1, paramArrayOfbyte2);
/* 307 */       System.arraycopy(arrayOfByte2, 0, arrayOfByte, i, arrayOfByte2.length);
/* 308 */       i += arrayOfByte1.length;
/*     */     } 
/* 310 */     return arrayOfByte;
/*     */   }
/*     */   
/*     */   public static byte[] getSMCode(String paramString) {
/* 314 */     byte[] arrayOfByte = new byte[16];
/*     */     
/*     */     try {
/* 317 */       while (paramString.length() < 16)
/*     */       {
/* 319 */         paramString = paramString + paramString;
/*     */       }
/* 321 */       byte[] arrayOfByte1 = paramString.getBytes();
/* 322 */       System.arraycopy(arrayOfByte1, 0, arrayOfByte, 0, 16);
/*     */     }
/* 324 */     catch (Exception exception) {
/*     */       
/* 326 */       exception.printStackTrace();
/*     */     } 
/* 328 */     return arrayOfByte;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/sm/SM4Util.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */