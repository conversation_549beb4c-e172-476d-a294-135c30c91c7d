/*    */ package weaver.ln;
/*    */ 
/*    */ import java.io.IOException;
/*    */ import javax.servlet.http.HttpServlet;
/*    */ import javax.servlet.http.HttpServletRequest;
/*    */ import javax.servlet.http.HttpServletResponse;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class LNServlet
/*    */   extends HttpServlet
/*    */ {
/*    */   private static final long serialVersionUID = -9076098031178250147L;
/*    */   
/*    */   public void doGet(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws IOException {
/* 37 */     doPost(paramHttpServletRequest, paramHttpServletResponse);
/*    */   }
/*    */ 
/*    */   
/*    */   public void doPost(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws IOException {
/* 42 */     paramHttpServletResponse.getWriter().print("not support!");
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/ln/LNServlet.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */