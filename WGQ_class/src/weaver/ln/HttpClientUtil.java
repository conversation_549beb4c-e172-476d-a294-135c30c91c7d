/*     */ package weaver.ln;
/*     */ 
/*     */ import java.util.Iterator;
/*     */ import net.sf.json.JSONObject;
/*     */ import org.apache.commons.httpclient.Header;
/*     */ import org.apache.commons.httpclient.HttpClient;
/*     */ import org.apache.commons.httpclient.HttpConnectionManager;
/*     */ import org.apache.commons.httpclient.HttpMethod;
/*     */ import org.apache.commons.httpclient.MultiThreadedHttpConnectionManager;
/*     */ import org.apache.commons.httpclient.NameValuePair;
/*     */ import org.apache.commons.httpclient.contrib.ssl.EasySSLProtocolSocketFactory;
/*     */ import org.apache.commons.httpclient.methods.GetMethod;
/*     */ import org.apache.commons.httpclient.methods.PostMethod;
/*     */ import org.apache.commons.httpclient.protocol.Protocol;
/*     */ import org.apache.commons.httpclient.protocol.ProtocolSocketFactory;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HttpClientUtil
/*     */ {
/*  24 */   private static HttpConnectionManager connectionManager = (HttpConnectionManager)new MultiThreadedHttpConnectionManager();
/*     */   
/*     */   static {
/*  27 */     ((MultiThreadedHttpConnectionManager)connectionManager).setMaxConnectionsPerHost(1);
/*  28 */     ((MultiThreadedHttpConnectionManager)connectionManager).setMaxTotalConnections(10);
/*  29 */     ((MultiThreadedHttpConnectionManager)connectionManager).setConnectionStaleCheckingEnabled(true);
/*     */     try {
/*  31 */       Protocol.registerProtocol("https", new Protocol("https", (ProtocolSocketFactory)new EasySSLProtocolSocketFactory(), 443));
/*  32 */     } catch (Exception exception) {}
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static HttpConnectionManager getInstance() {
/*  38 */     return connectionManager;
/*     */   }
/*     */   
/*     */   public static HttpClient getHttpClient() {
/*  42 */     HttpClient httpClient = new HttpClient(connectionManager);
/*  43 */     httpClient.getHttpConnectionManager().getParams().setSoTimeout(6000);
/*  44 */     httpClient.getHttpConnectionManager().getParams().setConnectionTimeout(2000);
/*  45 */     return httpClient;
/*     */   }
/*     */   
/*     */   public static String get(String paramString) {
/*  49 */     GetMethod getMethod = null;
/*     */     try {
/*  51 */       HttpClient httpClient = getHttpClient();
/*  52 */       getMethod = new GetMethod(paramString);
/*  53 */       int i = httpClient.executeMethod((HttpMethod)getMethod);
/*  54 */       if (i == 200) {
/*  55 */         String str = getMethod.getResponseCharSet();
/*  56 */         byte[] arrayOfByte = getMethod.getResponseBody();
/*  57 */         return new String(arrayOfByte, str);
/*     */       } 
/*  59 */       return null;
/*     */     
/*     */     }
/*  62 */     catch (Exception exception) {
/*  63 */       exception.printStackTrace();
/*     */     } finally {
/*  65 */       if (getMethod != null) {
/*  66 */         getMethod.releaseConnection();
/*     */       }
/*     */     } 
/*  69 */     return null;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static String PostMethodTest(String paramString, JSONObject paramJSONObject) throws Exception {
/*  75 */     if (paramJSONObject == null) return null;
/*     */     
/*  77 */     HttpClient httpClient = getHttpClient();
/*  78 */     PostMethod postMethod = new PostMethod(paramString);
/*  79 */     String str = "";
/*     */     
/*     */     try {
/*  82 */       postMethod.addRequestHeader(new Header("Content-Type", "application/x-www-form-urlencoded;charset=utf-8"));
/*     */       
/*  84 */       Iterator<String> iterator = paramJSONObject.keys();
/*  85 */       while (iterator.hasNext()) {
/*  86 */         String str1 = iterator.next();
/*  87 */         String str2 = paramJSONObject.getString(str1);
/*  88 */         postMethod.addParameter(new NameValuePair(str1, str2));
/*     */       } 
/*     */ 
/*     */       
/*  92 */       int i = httpClient.executeMethod((HttpMethod)postMethod);
/*  93 */       if (i == 200) {
/*  94 */         str = postMethod.getResponseBodyAsString().trim();
/*  95 */         return str;
/*     */       } 
/*  97 */       throw new Exception("HTTP ERROR Status: " + postMethod.getStatusCode() + ":" + postMethod.getStatusText());
/*     */     }
/*  99 */     catch (Exception exception) {
/* 100 */       exception.printStackTrace();
/* 101 */       return null;
/*     */     } finally {
/* 103 */       postMethod.releaseConnection();
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/ln/HttpClientUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */