/*     */ package weaver.pr.util;
/*     */ 
/*     */ import java.util.Date;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.TimeUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ 
/*     */ public class OperateUtil
/*     */   extends BaseBean
/*     */ {
/*  13 */   private ResourceComInfo rc = null;
/*     */ 
/*     */   
/*     */   public OperateUtil() {
/*     */     try {
/*  18 */       this.rc = new ResourceComInfo();
/*  19 */     } catch (Exception exception) {}
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void addProgramLog(String paramString1, String paramString2, int paramInt) {
/*  29 */     RecordSet recordSet = new RecordSet();
/*     */     
/*  31 */     String str = "insert into PR_PlanProgramLog (programid,operator,operatedate,operatetime,operatetype) values(" + paramString2 + "," + paramString1 + ",'" + TimeUtil.getCurrentDateString() + "','" + TimeUtil.getOnlyCurrentTimeString() + "'," + paramInt + ")";
/*     */     
/*  33 */     recordSet.executeSql(str);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void addPlanLog(String paramString1, String paramString2, int paramInt) {
/*  43 */     RecordSet recordSet = new RecordSet();
/*     */     
/*  45 */     String str = "insert into PR_PlanReportLog (planid,operator,operatedate,operatetime,operatetype) values(" + paramString2 + "," + paramString1 + ",'" + TimeUtil.getCurrentDateString() + "','" + TimeUtil.getOnlyCurrentTimeString() + "'," + paramInt + ")";
/*     */     
/*  47 */     recordSet.executeSql(str);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void approvePlan(String paramString1, String paramString2) {
/*  56 */     RecordSet recordSet = new RecordSet();
/*  57 */     recordSet.executeSql("delete from PR_PlanReportAudit where planid=" + paramString1 + " and userid=" + paramString2);
/*  58 */     addPlanLog(paramString2, paramString1, 5);
/*  59 */     recordSet.executeSql("select count(id) from PR_PlanReportAudit where planid=" + paramString1);
/*  60 */     if (recordSet.next() && recordSet.getInt(1) == 0) {
/*  61 */       recordSet.executeSql("update PR_PlanReport set status=3,finishdate='" + TimeUtil.getCurrentDateString() + "',finishtime='" + TimeUtil.getOnlyCurrentTimeString() + "' where id=" + paramString1);
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void returnPlan(String paramString1, String paramString2) {
/*  70 */     RecordSet recordSet = new RecordSet();
/*  71 */     recordSet.executeSql("delete from PR_PlanReportAudit where planid=" + paramString1);
/*  72 */     recordSet.executeSql("update PR_PlanReport set status=2 where id=" + paramString1);
/*  73 */     addPlanLog(paramString2, paramString1, 4);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void updatePlanBySetting(String paramString) {
/*  81 */     RecordSet recordSet = new RecordSet();
/*  82 */     String str1 = TimeUtil.getCurrentDateString();
/*  83 */     String str2 = "";
/*  84 */     String str3 = "";
/*  85 */     String str4 = "";
/*  86 */     String str5 = "";
/*  87 */     int i = Util.getIntValue(str1.substring(5, 7));
/*  88 */     int j = Util.getIntValue(str1.substring(0, 4));
/*  89 */     int k = TimeUtil.getWeekOfYear(new Date());
/*  90 */     int m = 0;
/*  91 */     int n = j;
/*  92 */     int i1 = 0;
/*  93 */     String str6 = "";
/*  94 */     for (byte b = 1; b < 3; b++) {
/*  95 */       if (b == 1) {
/*  96 */         m = i;
/*  97 */         if (m == 1) {
/*  98 */           n = j - 1;
/*  99 */           i1 = 12;
/*     */         } else {
/* 101 */           i1 = m - 1;
/*     */         } 
/*     */         try {
/* 104 */           str2 = TimeUtil.getYearMonthEndDay(n, i1);
/* 105 */           str3 = TimeUtil.getYearMonthEndDay(j, i);
/* 106 */         } catch (Exception exception) {}
/* 107 */         str6 = "select mstarttype,mstartdays,mendtype,menddays,ismonth from PR_BaseSetting where resourcetype=2 and resourceid=" + paramString;
/* 108 */       } else if (b == 2) {
/* 109 */         m = k;
/* 110 */         if (m == 1) {
/* 111 */           n = j - 1;
/* 112 */           i1 = TimeUtil.getMaxWeekNumOfYear(n);
/*     */         } else {
/* 114 */           i1 = m - 1;
/*     */         } 
/*     */         try {
/* 117 */           str2 = TimeUtil.getDateString(TimeUtil.getLastDayOfWeek(n, i1));
/* 118 */           str3 = TimeUtil.getDateString(TimeUtil.getLastDayOfWeek(j, m));
/* 119 */         } catch (Exception exception) {}
/* 120 */         str6 = "select wstarttype,wstartdays,wendtype,wenddays,isweek from PR_BaseSetting where resourcetype=2 and resourceid=" + paramString;
/*     */       } 
/* 122 */       recordSet.executeSql(str6);
/* 123 */       if (recordSet.next()) {
/* 124 */         int i2 = Util.getIntValue(recordSet.getString(5), 0);
/*     */         
/* 126 */         str4 = TimeUtil.dateAdd(str2, Util.getIntValue(recordSet.getString(1), 1) * Util.getIntValue(recordSet.getString(2), 0));
/* 127 */         str5 = TimeUtil.dateAdd(str2, Util.getIntValue(recordSet.getString(3), 1) * Util.getIntValue(recordSet.getString(4), 0));
/*     */         
/* 129 */         if (i2 == 1) {
/* 130 */           updateData(paramString, n, b, i1, str4, str5);
/*     */         } else {
/* 132 */           closeData(paramString, n, b, i1);
/*     */         } 
/*     */ 
/*     */         
/* 136 */         str4 = TimeUtil.dateAdd(str3, Util.getIntValue(recordSet.getString(1), 1) * Util.getIntValue(recordSet.getString(2), 0));
/* 137 */         str5 = TimeUtil.dateAdd(str3, Util.getIntValue(recordSet.getString(3), 1) * Util.getIntValue(recordSet.getString(4), 0));
/* 138 */         if (i2 == 1) {
/* 139 */           updateData(paramString, j, b, m, str4, str5);
/*     */         } else {
/* 141 */           closeData(paramString, j, b, m);
/*     */         } 
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void updateData(String paramString1, int paramInt1, int paramInt2, int paramInt3, String paramString2, String paramString3) {
/* 154 */     RecordSet recordSet = new RecordSet();
/* 155 */     String str = "update p set p.isvalid=1,p.startdate='" + paramString2 + "',p.enddate='" + paramString3 + "' from PR_PlanReport p where exists(select 1 from HrmResource h where h.id=p.userid and h.subcompanyid1=" + paramString1 + " \tand h.status in (0,1,2,3) and h.loginid is not null and h.loginid<>'') and p.year=" + paramInt1 + " and p.type1=" + paramInt2 + " and p.type2=" + paramInt3;
/*     */ 
/*     */ 
/*     */     
/* 159 */     if ("oracle".equals(recordSet.getDBType())) {
/* 160 */       str = "update PR_PlanReport p set p.isvalid=1,p.startdate='" + paramString2 + "',p.enddate='" + paramString3 + "' where exists(select 1 from HrmResource h where h.id=p.userid and h.subcompanyid1=" + paramString1 + " \tand h.status in (0,1,2,3) and h.loginid is not null) and p.year=" + paramInt1 + " and p.type1=" + paramInt2 + " and p.type2=" + paramInt3;
/*     */     }
/*     */ 
/*     */ 
/*     */     
/* 165 */     recordSet.executeSql(str);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void closeData(String paramString, int paramInt1, int paramInt2, int paramInt3) {
/* 175 */     RecordSet recordSet = new RecordSet();
/* 176 */     recordSet.executeSql("update PR_PlanReport set isvalid=0 where year=" + paramInt1 + " and type1=" + paramInt2 + " and type2=" + paramInt3 + " and exists(select id from HrmResource where HrmResource.id=PR_PlanReport.userid and HrmResource.subcompanyid1=" + paramString + ")");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean isWork(String paramString) {
/* 186 */     if (this.rc.getStatus(paramString).equals("0") || this.rc.getStatus(paramString).equals("1") || this.rc.getStatus(paramString).equals("2") || this.rc.getStatus(paramString).equals("3")) {
/* 187 */       return true;
/*     */     }
/* 189 */     return false;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/pr/util/OperateUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */