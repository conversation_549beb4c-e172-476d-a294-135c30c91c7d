/*     */ package weaver.pr.util;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.crm.Maint.CustomerInfoComInfo;
/*     */ import weaver.docs.docs.DocComInfo;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.ThreadVarLanguage;
/*     */ import weaver.general.TimeUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.workflow.request.RequestComInfo;
/*     */ 
/*     */ public class TransUtil extends BaseBean {
/*  16 */   private ResourceComInfo rc = null;
/*  17 */   private CustomerInfoComInfo ci = null;
/*  18 */   private DocComInfo doc = null;
/*  19 */   private RequestComInfo request = null;
/*     */   
/*     */   public TransUtil() {
/*     */     try {
/*  23 */       this.rc = new ResourceComInfo();
/*  24 */       this.ci = new CustomerInfoComInfo();
/*  25 */       this.doc = new DocComInfo();
/*  26 */       this.request = new RequestComInfo();
/*  27 */     } catch (Exception exception) {
/*  28 */       writeLog(exception);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCustomer(String paramString) {
/*  40 */     String str = "";
/*  41 */     if (paramString != null && !"".equals(paramString)) {
/*  42 */       ArrayList<String> arrayList = Util.TokenizerString(paramString, ",");
/*  43 */       for (byte b = 0; b < arrayList.size(); b++) {
/*  44 */         str = str + "<a href='/CRM/data/ViewCustomer.jsp?log=n&CustomerID=" + (String)arrayList.get(b) + "' target='_blank'>" + this.ci.getCustomerInfoname(arrayList.get(b)) + "</a>&nbsp;";
/*     */       }
/*     */     } 
/*  47 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getPerson(String paramString) {
/*  58 */     String str = "";
/*  59 */     if (paramString != null && !"".equals(paramString)) {
/*  60 */       ArrayList<String> arrayList = Util.TokenizerString(paramString, ",");
/*  61 */       for (byte b = 0; b < arrayList.size(); b++) {
/*  62 */         str = str + "<a href='javaScript:openhrm(" + arrayList.get(b) + ");' onclick='pointerXY(event);'>" + this.rc.getResourcename(arrayList.get(b)) + "</a> ";
/*     */       }
/*     */     } 
/*  65 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getYN(String paramString1, String paramString2) {
/*  75 */     if ("1".equals(paramString1))
/*  76 */       return SystemEnv.getHtmlLabelName(163, Integer.parseInt(paramString2)); 
/*  77 */     if ("0".equals(paramString1)) {
/*  78 */       return SystemEnv.getHtmlLabelName(161, Integer.parseInt(paramString2));
/*     */     }
/*  80 */     return "";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getDateTime(String paramString1, String paramString2) {
/*  92 */     return paramString1 + " " + paramString2;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getMailto(String paramString) {
/* 101 */     if (!"".equals(paramString) && paramString != null) {
/* 102 */       return "<a href='mailto:" + paramString + "'>" + paramString + "</a>";
/*     */     }
/* 104 */     return "";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getPlanOperateType(String paramString) {
/* 114 */     if ("1".equals(paramString))
/* 115 */       return "" + SystemEnv.getHtmlLabelName(384122, ThreadVarLanguage.getLang()) + ""; 
/* 116 */     if ("2".equals(paramString))
/* 117 */       return "" + SystemEnv.getHtmlLabelName(383323, ThreadVarLanguage.getLang()) + ""; 
/* 118 */     if ("3".equals(paramString))
/* 119 */       return "" + SystemEnv.getHtmlLabelName(615, ThreadVarLanguage.getLang()) + ""; 
/* 120 */     if ("4".equals(paramString))
/* 121 */       return "" + SystemEnv.getHtmlLabelName(129829, ThreadVarLanguage.getLang()) + ""; 
/* 122 */     if ("5".equals(paramString))
/* 123 */       return "" + SystemEnv.getHtmlLabelName(142, ThreadVarLanguage.getLang()) + ""; 
/* 124 */     if ("6".equals(paramString))
/* 125 */       return "" + SystemEnv.getHtmlLabelName(509014, ThreadVarLanguage.getLang()) + ""; 
/* 126 */     if ("7".equals(paramString)) {
/* 127 */       return "" + SystemEnv.getHtmlLabelName(509152, ThreadVarLanguage.getLang()) + "";
/*     */     }
/* 129 */     return "";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getProgramOperateType(String paramString) {
/* 139 */     if ("1".equals(paramString))
/* 140 */       return "" + SystemEnv.getHtmlLabelName(82, ThreadVarLanguage.getLang()) + ""; 
/* 141 */     if ("2".equals(paramString))
/* 142 */       return "" + SystemEnv.getHtmlLabelName(383323, ThreadVarLanguage.getLang()) + ""; 
/* 143 */     if ("3".equals(paramString))
/* 144 */       return "" + SystemEnv.getHtmlLabelName(615, ThreadVarLanguage.getLang()) + ""; 
/* 145 */     if ("4".equals(paramString))
/* 146 */       return "" + SystemEnv.getHtmlLabelName(129829, ThreadVarLanguage.getLang()) + ""; 
/* 147 */     if ("5".equals(paramString)) {
/* 148 */       return "" + SystemEnv.getHtmlLabelName(142, ThreadVarLanguage.getLang()) + "";
/*     */     }
/* 150 */     return "";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getType1(String paramString) {
/* 159 */     if ("1".equals(paramString))
/* 160 */       return "" + SystemEnv.getHtmlLabelName(20617, ThreadVarLanguage.getLang()) + " "; 
/* 161 */     if ("2".equals(paramString))
/* 162 */       return "" + SystemEnv.getHtmlLabelName(20619, ThreadVarLanguage.getLang()) + ""; 
/* 163 */     if ("3".equals(paramString))
/* 164 */       return "" + SystemEnv.getHtmlLabelName(20729, ThreadVarLanguage.getLang()) + ""; 
/* 165 */     if ("4".equals(paramString)) {
/* 166 */       return "" + SystemEnv.getHtmlLabelName(20616, ThreadVarLanguage.getLang()) + "";
/*     */     }
/* 168 */     return "";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getPlanLink(String paramString1, String paramString2) {
/* 177 */     String str = "";
/* 178 */     if (paramString1 != null && !"".equals(paramString1)) {
/* 179 */       str = "<a href=\"javascript:openFullWindowHaveBar('/workrelate/plan/data/PlanView.jsp?planid=" + paramString1 + "')\">" + paramString2 + "</a>";
/*     */     }
/* 181 */     return str;
/*     */   }
/*     */   public String getPlanView(String paramString) {
/* 184 */     String str1 = "";
/* 185 */     String str2 = Util.null2String(paramString).trim();
/* 186 */     if (!"".equals(str2)) {
/* 187 */       str1 = "<a href=\"javascript:openFullWindowHaveBar('/workrelate/plan/data/PlanView.jsp?planid=" + str2 + "')\">" + SystemEnv.getHtmlLabelName(126218, ThreadVarLanguage.getLang()) + "</a>";
/*     */     } else {
/* 189 */       str1 = "" + SystemEnv.getHtmlLabelName(126218, ThreadVarLanguage.getLang()) + "";
/*     */     } 
/* 191 */     return str1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getPlanOperate(String paramString) {
/* 200 */     String[] arrayOfString = Util.TokenizerString2(paramString, "+");
/* 201 */     String str1 = arrayOfString[0];
/* 202 */     String str2 = arrayOfString[1];
/* 203 */     String str3 = arrayOfString[2];
/* 204 */     String str4 = "";
/* 205 */     String str5 = TimeUtil.getCurrentDateString();
/* 206 */     if (TimeUtil.dateInterval(str3, str5) > 0) {
/* 207 */       str4 = "<span class='expire'>" + SystemEnv.getHtmlLabelName(382750, ThreadVarLanguage.getLang()) + "</span";
/*     */     } else {
/*     */       
/* 210 */       str4 = "<span class='operatespan'><a class='a_op' href='javascript:doApprove(" + str1 + ")'>" + SystemEnv.getHtmlLabelName(142, ThreadVarLanguage.getLang()) + "</a>&nbsp;<a class='a_op' href='javascript:doReturn(" + str1 + ")'>" + SystemEnv.getHtmlLabelName(129829, ThreadVarLanguage.getLang()) + "</a></span>";
/*     */     } 
/* 212 */     return str4;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getPlanStatus(String paramString) {
/* 220 */     if ("0".equals(paramString))
/* 221 */       return "" + SystemEnv.getHtmlLabelName(129828, ThreadVarLanguage.getLang()) + ""; 
/* 222 */     if ("1".equals(paramString))
/* 223 */       return "" + SystemEnv.getHtmlLabelName(19134, ThreadVarLanguage.getLang()) + ""; 
/* 224 */     if ("2".equals(paramString))
/* 225 */       return "" + SystemEnv.getHtmlLabelName(129829, ThreadVarLanguage.getLang()) + ""; 
/* 226 */     if ("3".equals(paramString)) {
/* 227 */       return "" + SystemEnv.getHtmlLabelName(508866, ThreadVarLanguage.getLang()) + "";
/*     */     }
/* 229 */     return "";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getPlanStatusDetail(String paramString1, String paramString2) throws Exception {
/* 238 */     StringBuffer stringBuffer = new StringBuffer();
/* 239 */     String str1 = Util.null2String(paramString1).trim();
/* 240 */     String[] arrayOfString = Util.TokenizerString2(paramString2, "+");
/* 241 */     String str2 = Util.null2String(arrayOfString[0]).trim();
/* 242 */     String str3 = Util.null2String(arrayOfString[1]).trim();
/* 243 */     String str4 = Util.null2String(arrayOfString[2]).trim();
/* 244 */     String str5 = Util.null2String(arrayOfString[3]).trim();
/* 245 */     String str6 = "/workrelate/plan/data/PlanView.jsp";
/*     */     
/* 247 */     if (str1.equals("")) {
/* 248 */       String str7 = "";
/* 249 */       String str8 = "";
/* 250 */       String str9 = Util.null2String(arrayOfString[4]).trim();
/* 251 */       String str10 = Util.null2String(arrayOfString[5]).trim();
/* 252 */       String str11 = Util.null2String(arrayOfString[6]).trim();
/*     */       
/* 254 */       str6 = str6 + "?resourceid=" + str5 + "&year=" + str9 + "&type1=" + str10 + "&type2=" + str11;
/*     */       
/* 256 */       int i = 0;
/* 257 */       int j = 0;
/* 258 */       int k = 0;
/* 259 */       int m = 0;
/* 260 */       RecordSet recordSet = new RecordSet();
/* 261 */       recordSet.executeSql("select wstarttype,wstartdays,mstarttype,mstartdays from PR_BaseSetting where resourceid=" + this.rc.getSubCompanyID(str5) + " and resourcetype=2");
/* 262 */       if (recordSet.next()) {
/* 263 */         i = Util.getIntValue(recordSet.getString("wstarttype"), 0);
/* 264 */         j = Util.getIntValue(recordSet.getString("wstartdays"), 0);
/* 265 */         k = Util.getIntValue(recordSet.getString("mstarttype"), 0);
/* 266 */         m = Util.getIntValue(recordSet.getString("mstartdays"), 0);
/*     */       } 
/* 268 */       if (str10.equals("1")) {
/* 269 */         str7 = TimeUtil.getYearMonthEndDay(Integer.parseInt(str9), Integer.parseInt(str11));
/* 270 */         str8 = TimeUtil.dateAdd(str7, m * k);
/* 271 */       } else if (str10.equals("2")) {
/* 272 */         str7 = TimeUtil.getDateString(TimeUtil.getLastDayOfWeek(Integer.parseInt(str9), Integer.parseInt(str11)));
/* 273 */         str8 = TimeUtil.dateAdd(str7, j * i);
/*     */       } 
/*     */       
/* 276 */       String str12 = TimeUtil.getCurrentDateString();
/* 277 */       if (TimeUtil.dateInterval(str12, str8) > 0) {
/* 278 */         stringBuffer.append("<span class='status status2' onclick=\"openFullWindowHaveBar('" + str6 + "')\" title='" + SystemEnv.getHtmlLabelName(1979, ThreadVarLanguage.getLang()) + "'></span><span class='status_txt'>" + SystemEnv.getHtmlLabelName(10003628, ThreadVarLanguage.getLang()) + "" + str8 + "</span>");
/*     */       } else {
/* 280 */         stringBuffer.append("<span class='status status8' onclick=\"openFullWindowHaveBar('" + str6 + "')\" title='" + SystemEnv.getHtmlLabelName(508610, ThreadVarLanguage.getLang()) + "'></span>");
/*     */       } 
/*     */     } else {
/*     */       
/* 284 */       str6 = str6 + "?planid=" + str1;
/* 285 */       String str = TimeUtil.getCurrentDateString();
/* 286 */       if (TimeUtil.dateInterval(str, str3) > 0) {
/* 287 */         stringBuffer.append("<span class='status status2' onclick=\"openFullWindowHaveBar('" + str6 + "')\" title='" + SystemEnv.getHtmlLabelName(1979, ThreadVarLanguage.getLang()) + "'></span><span class='status_txt'>" + SystemEnv.getHtmlLabelName(10003628, ThreadVarLanguage.getLang()) + "" + str3 + "</span>");
/* 288 */       } else if (str2.equals("3")) {
/* 289 */         stringBuffer.append("<span class='status status3' onclick=\"openFullWindowHaveBar('" + str6 + "')\" title='" + SystemEnv.getHtmlLabelName(1961, ThreadVarLanguage.getLang()) + "'></span>");
/*     */       } else {
/* 291 */         if ("0".equals(str2) || "2".equals(str2)) {
/* 292 */           if (TimeUtil.dateInterval(str4, str) > 0) {
/* 293 */             stringBuffer.append("<span class='status status6' onclick=\"openFullWindowHaveBar('" + str6 + "')\" title='" + SystemEnv.getHtmlLabelName(509332, ThreadVarLanguage.getLang()) + "'></span>");
/*     */           }
/* 295 */           else if ("0".equals(str2)) {
/* 296 */             stringBuffer.append("<span class='status status4' onclick=\"openFullWindowHaveBar('" + str6 + "')\" title='" + SystemEnv.getHtmlLabelName(129828, ThreadVarLanguage.getLang()) + "'></span>");
/*     */           } else {
/* 298 */             stringBuffer.append("<span class='status status7' onclick=\"openFullWindowHaveBar('" + str6 + "')\" title='" + SystemEnv.getHtmlLabelName(509334, ThreadVarLanguage.getLang()) + "'></span>");
/*     */           }
/*     */         
/* 301 */         } else if ("1".equals(str2)) {
/* 302 */           RecordSet recordSet = new RecordSet();
/* 303 */           recordSet.executeSql("select userid from PR_PlanReportAudit where planid=" + str1);
/* 304 */           String str7 = "";
/* 305 */           while (recordSet.next()) {
/* 306 */             str7 = str7 + recordSet.getString(1) + ",";
/*     */           }
/* 308 */           if (TimeUtil.dateInterval(str4, str) > 0) {
/* 309 */             stringBuffer.append("<span class='status status6' onclick=\"openFullWindowHaveBar('" + str6 + "')\" title='" + SystemEnv.getHtmlLabelName(10003878, ThreadVarLanguage.getLang()) + "'></span>");
/*     */           } else {
/* 311 */             stringBuffer.append("<span class='status status5' onclick=\"openFullWindowHaveBar('" + str6 + "')\" title='" + SystemEnv.getHtmlLabelName(509335, ThreadVarLanguage.getLang()) + "'></span>");
/*     */           } 
/* 313 */           stringBuffer.append("<span class='status_txt'>" + SystemEnv.getHtmlLabelName(10003630, ThreadVarLanguage.getLang()) + "" + getPerson(str7));
/*     */         } 
/* 315 */         stringBuffer.append(" " + SystemEnv.getHtmlLabelName(10003631, ThreadVarLanguage.getLang()) + "" + str4 + "</span>");
/*     */       } 
/*     */     } 
/* 318 */     stringBuffer.append("<span class='operatespan'><a href=\"javascript:openFullWindowHaveBar('" + str6 + "')\">" + SystemEnv.getHtmlLabelName(367, ThreadVarLanguage.getLang()) + "</a></span>");
/* 319 */     return stringBuffer.toString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getMPlanStatusDetail(String paramString1, String paramString2) throws Exception {
/* 327 */     StringBuffer stringBuffer1 = new StringBuffer();
/* 328 */     String str1 = Util.null2String(paramString1).trim();
/* 329 */     String[] arrayOfString = Util.TokenizerString2(paramString2, "+");
/* 330 */     String str2 = Util.null2String(arrayOfString[0]).trim();
/* 331 */     String str3 = Util.null2String(arrayOfString[1]).trim();
/* 332 */     String str4 = Util.null2String(arrayOfString[2]).trim();
/* 333 */     String str5 = Util.null2String(arrayOfString[3]).trim();
/* 334 */     String str6 = "/mobile/plugin/workrelate/PlanView.jsp";
/* 335 */     StringBuffer stringBuffer2 = new StringBuffer();
/* 336 */     StringBuffer stringBuffer3 = new StringBuffer();
/* 337 */     StringBuffer stringBuffer4 = new StringBuffer();
/* 338 */     if (str1.equals("")) {
/* 339 */       String str7 = "";
/* 340 */       String str8 = "";
/* 341 */       String str9 = Util.null2String(arrayOfString[4]).trim();
/* 342 */       String str10 = Util.null2String(arrayOfString[5]).trim();
/* 343 */       String str11 = Util.null2String(arrayOfString[6]).trim();
/*     */       
/* 345 */       str6 = str6 + "?resourceid=" + str5 + "&year=" + str9 + "&type1=" + str10 + "&type2=" + str11;
/*     */       
/* 347 */       int i = 0;
/* 348 */       int j = 0;
/* 349 */       int k = 0;
/* 350 */       int m = 0;
/* 351 */       RecordSet recordSet = new RecordSet();
/* 352 */       recordSet.executeSql("select wstarttype,wstartdays,mstarttype,mstartdays from PR_BaseSetting where resourceid=" + this.rc.getSubCompanyID(str5) + " and resourcetype=2");
/* 353 */       if (recordSet.next()) {
/* 354 */         i = Util.getIntValue(recordSet.getString("wstarttype"), 0);
/* 355 */         j = Util.getIntValue(recordSet.getString("wstartdays"), 0);
/* 356 */         k = Util.getIntValue(recordSet.getString("mstarttype"), 0);
/* 357 */         m = Util.getIntValue(recordSet.getString("mstartdays"), 0);
/*     */       } 
/* 359 */       if (str10.equals("1")) {
/* 360 */         str7 = TimeUtil.getYearMonthEndDay(Integer.parseInt(str9), Integer.parseInt(str11));
/* 361 */         str8 = TimeUtil.dateAdd(str7, m * k);
/* 362 */       } else if (str10.equals("2")) {
/* 363 */         str7 = TimeUtil.getDateString(TimeUtil.getLastDayOfWeek(Integer.parseInt(str9), Integer.parseInt(str11)));
/* 364 */         str8 = TimeUtil.dateAdd(str7, j * i);
/*     */       } 
/*     */       
/* 367 */       String str12 = TimeUtil.getCurrentDateString();
/* 368 */       if (TimeUtil.dateInterval(str12, str8) > 0) {
/*     */         
/* 370 */         stringBuffer2.append("" + SystemEnv.getHtmlLabelName(1979, ThreadVarLanguage.getLang()) + "");
/* 371 */         stringBuffer4.append("2475D1");
/*     */       } else {
/*     */         
/* 374 */         stringBuffer2.append("" + SystemEnv.getHtmlLabelName(508873, ThreadVarLanguage.getLang()) + "");
/* 375 */         stringBuffer4.append("000000");
/*     */       } 
/*     */     } else {
/*     */       
/* 379 */       str6 = str6 + "?planid=" + str1;
/* 380 */       String str = TimeUtil.getCurrentDateString();
/* 381 */       if (TimeUtil.dateInterval(str, str3) > 0) {
/*     */         
/* 383 */         stringBuffer2.append("" + SystemEnv.getHtmlLabelName(1979, ThreadVarLanguage.getLang()) + "");
/* 384 */         stringBuffer4.append("2475D1");
/* 385 */       } else if (str2.equals("3")) {
/*     */         
/* 387 */         stringBuffer2.append("" + SystemEnv.getHtmlLabelName(1961, ThreadVarLanguage.getLang()) + "");
/* 388 */         stringBuffer4.append("49D732");
/*     */       } else {
/* 390 */         if ("0".equals(str2) || "2".equals(str2)) {
/* 391 */           if (TimeUtil.dateInterval(str4, str) > 0) {
/*     */             
/* 393 */             stringBuffer2.append("" + SystemEnv.getHtmlLabelName(382750, ThreadVarLanguage.getLang()) + "");
/* 394 */             stringBuffer4.append("000000");
/*     */           }
/* 396 */           else if ("0".equals(str2)) {
/*     */             
/* 398 */             stringBuffer2.append("" + SystemEnv.getHtmlLabelName(129828, ThreadVarLanguage.getLang()) + "");
/* 399 */             stringBuffer4.append("ACA207");
/*     */           } else {
/*     */             
/* 402 */             stringBuffer2.append("" + SystemEnv.getHtmlLabelName(129829, ThreadVarLanguage.getLang()) + "");
/* 403 */             stringBuffer4.append("B916E8");
/*     */           }
/*     */         
/* 406 */         } else if ("1".equals(str2)) {
/* 407 */           RecordSet recordSet = new RecordSet();
/* 408 */           recordSet.executeSql("select userid from PR_PlanReportAudit where planid=" + str1);
/* 409 */           String str7 = "";
/* 410 */           while (recordSet.next()) {
/* 411 */             str7 = str7 + recordSet.getString(1) + ",";
/*     */           }
/* 413 */           if (TimeUtil.dateInterval(str4, str) > 0) {
/*     */             
/* 415 */             stringBuffer2.append("" + SystemEnv.getHtmlLabelName(382750, ThreadVarLanguage.getLang()) + "");
/* 416 */             stringBuffer4.append("000000");
/*     */           } else {
/*     */             
/* 419 */             stringBuffer2.append("" + SystemEnv.getHtmlLabelName(19134, ThreadVarLanguage.getLang()) + "");
/* 420 */             stringBuffer4.append("5F0E03");
/*     */           } 
/* 422 */           String str8 = "";
/* 423 */           if (str7 != null && !"".equals(str7)) {
/* 424 */             ArrayList<String> arrayList = Util.TokenizerString(str7, ",");
/* 425 */             for (byte b = 0; b < arrayList.size(); b++) {
/* 426 */               str8 = str8 + this.rc.getResourcename(arrayList.get(b)) + " ";
/*     */             }
/*     */           } 
/* 429 */           stringBuffer3.append("" + SystemEnv.getHtmlLabelName(10003630, ThreadVarLanguage.getLang()) + "" + str8);
/*     */         } 
/* 431 */         stringBuffer3.append("" + SystemEnv.getHtmlLabelName(10003631, ThreadVarLanguage.getLang()) + "" + str4 + "");
/*     */       } 
/*     */     } 
/* 434 */     stringBuffer1.append(stringBuffer2 + "," + str6 + "," + stringBuffer4 + "," + stringBuffer3);
/* 435 */     return stringBuffer1.toString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getYearType(String paramString) {
/* 443 */     String[] arrayOfString = Util.TokenizerString2(paramString, "+");
/* 444 */     String str1 = arrayOfString[0];
/* 445 */     String str2 = arrayOfString[1];
/* 446 */     String str3 = arrayOfString[2];
/* 447 */     String str4 = str1 + "年";
/* 448 */     if ("1".equals(str2)) {
/* 449 */       str4 = str4 + str3 + "月";
/* 450 */     } else if ("2".equals(str2)) {
/* 451 */       str4 = str4 + str3 + "" + SystemEnv.getHtmlLabelName(383377, ThreadVarLanguage.getLang()) + "";
/*     */     } 
/* 453 */     return str4;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getTaskName(String paramString) {
/* 461 */     String str = "";
/* 462 */     paramString = cutString(paramString, ",", 3);
/* 463 */     if (paramString != null && !"".equals(paramString)) {
/* 464 */       RecordSet recordSet = new RecordSet();
/* 465 */       recordSet.executeSql("select id,name from TM_TaskInfo where id in (" + paramString + ") and (deleted=0 or deleted is null)");
/* 466 */       while (recordSet.next()) {
/* 467 */         str = str + "<a href='javascript:showTask(" + recordSet.getString(1) + ")'>" + recordSet.getString(2) + "</a> ";
/*     */       }
/*     */     } 
/* 470 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getDocName(String paramString) {
/* 480 */     String str = "";
/* 481 */     if (paramString != null && !"".equals(paramString)) {
/* 482 */       ArrayList<String> arrayList = Util.TokenizerString(paramString, ",");
/* 483 */       for (byte b = 0; b < arrayList.size(); b++) {
/* 484 */         str = str + "<a href=javaScript:openFullWindowHaveBar('/docs/docs/DocDsp.jsp?id=" + arrayList.get(b) + "') >" + this.doc.getDocname(arrayList.get(b)) + "</a> ";
/*     */       }
/*     */     } 
/* 487 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getRequestName(String paramString) {
/* 497 */     String str = "";
/* 498 */     if (paramString != null && !"".equals(paramString.trim())) {
/* 499 */       ArrayList<String> arrayList = Util.TokenizerString(paramString, ",");
/* 500 */       for (byte b = 0; b < arrayList.size(); b++) {
/* 501 */         str = str + "<a href=javaScript:openFullWindowHaveBar('/workflow/request/ViewRequest.jsp?requestid=" + arrayList.get(b) + "') >" + this.request.getRequestname(arrayList.get(b)) + "</a> ";
/*     */       }
/*     */     } 
/* 504 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getProject(String paramString) {
/* 514 */     String str = "";
/* 515 */     if (paramString.startsWith(",")) paramString = paramString.substring(1); 
/* 516 */     if (paramString.endsWith(",")) paramString = paramString.substring(0, paramString.length() - 1); 
/* 517 */     if (paramString != null && !"".equals(paramString)) {
/* 518 */       RecordSet recordSet = new RecordSet();
/* 519 */       recordSet.executeSql("select id,subject from Prj_TaskProcess  where id in (" + paramString + ")");
/* 520 */       while (recordSet.next()) {
/* 521 */         str = str + "<a href=javaScript:openFullWindowHaveBar('/proj/process/ViewTask.jsp?taskrecordid=" + recordSet.getString(1) + "')>" + recordSet.getString(2) + "</a> ";
/*     */       }
/*     */     } 
/* 524 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String cutString(String paramString1, String paramString2, int paramInt) {
/* 534 */     paramString1 = Util.null2String(paramString1);
/* 535 */     paramString2 = Util.null2String(paramString2);
/* 536 */     if (paramString1.equals("") || paramString2.equals("")) {
/* 537 */       return paramString1;
/*     */     }
/* 539 */     if ((paramInt == 1 || paramInt == 3) && 
/* 540 */       paramString1.startsWith(paramString2)) {
/* 541 */       paramString1 = paramString1.substring(paramString2.length());
/*     */     }
/*     */     
/* 544 */     if ((paramInt == 2 || paramInt == 3) && 
/* 545 */       paramString1.endsWith(paramString2)) {
/* 546 */       paramString1 = paramString1.substring(0, paramString1.length() - paramString2.length());
/*     */     }
/*     */     
/* 549 */     return paramString1;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/pr/util/TransUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */