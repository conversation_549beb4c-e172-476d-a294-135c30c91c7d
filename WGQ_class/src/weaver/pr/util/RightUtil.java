/*     */ package weaver.pr.util;
/*     */ 
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.company.DepartmentComInfo;
/*     */ import weaver.hrm.company.SubCompanyComInfo;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ 
/*     */ public class RightUtil extends BaseBean {
/*  12 */   private ResourceComInfo rc = null;
/*  13 */   private SubCompanyComInfo sc = null;
/*  14 */   private DepartmentComInfo dc = null;
/*     */   
/*     */   public RightUtil() {
/*     */     try {
/*  18 */       this.rc = new ResourceComInfo();
/*  19 */       this.sc = new SubCompanyComInfo();
/*  20 */       this.dc = new DepartmentComInfo();
/*  21 */     } catch (Exception exception) {
/*  22 */       writeLog(exception);
/*     */     } 
/*     */   }
/*     */   
/*     */   public int getProgramRight(String paramString1, String paramString2) {
/*  27 */     RecordSet recordSet = new RecordSet();
/*  28 */     byte b = 0;
/*  29 */     boolean bool1 = false;
/*  30 */     boolean bool2 = false;
/*  31 */     boolean bool3 = false;
/*  32 */     boolean bool4 = false;
/*  33 */     if (paramString1.equals(paramString2)) {
/*  34 */       bool1 = true;
/*  35 */       bool3 = true;
/*  36 */     } else if (this.rc.isManager(Integer.parseInt(paramString2), paramString1)) {
/*  37 */       bool2 = true;
/*  38 */       bool3 = true;
/*     */     } 
/*     */     
/*  41 */     String str = "";
/*  42 */     int i = 0;
/*  43 */     int j = 0;
/*  44 */     recordSet.executeSql("select programcreate,isself,ismanager from PR_BaseSetting where (resourceid=" + this.sc.getCompanyid(this.rc.getSubCompanyID(paramString1)) + " and resourcetype=1) or (resourceid=" + this.rc.getSubCompanyID(paramString1) + " and resourcetype=2) order by resourcetype desc");
/*  45 */     if (recordSet.next()) {
/*  46 */       str = Util.null2String(recordSet.getString("programcreate"));
/*  47 */       i = Util.getIntValue(recordSet.getString("isself"), 0);
/*  48 */       j = Util.getIntValue(recordSet.getString("ismanager"), 0);
/*     */     } 
/*  50 */     if ((bool1 && i == 1) || (bool2 && j == 1)) {
/*  51 */       bool4 = true;
/*  52 */     } else if (("," + str + ",").indexOf("," + paramString2 + ",") > -1) {
/*  53 */       bool3 = true;
/*  54 */       bool4 = true;
/*     */     } 
/*  56 */     if (bool3) b = 1; 
/*  57 */     if (bool4) b = 2;
/*     */     
/*  59 */     return b;
/*     */   }
/*     */   public int getProgramRight(String paramString1, User paramUser, String paramString2) {
/*  62 */     byte b = 0;
/*  63 */     boolean bool1 = false;
/*  64 */     boolean bool2 = false;
/*  65 */     boolean bool3 = false;
/*  66 */     boolean bool4 = false;
/*  67 */     if (paramString1.equals("1") && paramUser.getUID() == 1) {
/*  68 */       bool3 = true;
/*  69 */       bool4 = true;
/*     */     } else {
/*  71 */       if (paramString1.equals(paramUser.getUID() + "")) {
/*  72 */         bool1 = true;
/*  73 */         bool3 = true;
/*  74 */       } else if (this.rc.isManager(paramUser.getUID(), paramString1)) {
/*  75 */         bool2 = true;
/*  76 */         bool3 = true;
/*     */       } 
/*  78 */       RecordSet recordSet = new RecordSet();
/*  79 */       String str1 = "";
/*  80 */       int i = 0;
/*  81 */       int j = 0;
/*  82 */       String str2 = "";
/*  83 */       if (paramString2.equals("2")) {
/*  84 */         str2 = paramString1;
/*  85 */       } else if (paramString2.equals("3")) {
/*  86 */         str2 = this.dc.getSubcompanyid1(paramString1);
/*  87 */       } else if (paramString2.equals("4")) {
/*  88 */         str2 = this.rc.getSubCompanyID(paramString1);
/*     */       } 
/*  90 */       recordSet.executeSql("select programcreate,isself,ismanager from PR_BaseSetting where (resourceid=" + this.sc.getCompanyid(str2) + " and resourcetype=1) or (resourceid=" + str2 + " and resourcetype=2) order by resourcetype desc");
/*  91 */       if (recordSet.next()) {
/*  92 */         str1 = Util.null2String(recordSet.getString("programcreate"));
/*  93 */         i = Util.getIntValue(recordSet.getString("isself"), 0);
/*  94 */         j = Util.getIntValue(recordSet.getString("ismanager"), 0);
/*     */       } 
/*  96 */       if ((bool1 && i == 1) || (bool2 && j == 1)) {
/*  97 */         bool4 = true;
/*  98 */       } else if (("," + str1 + ",").indexOf("," + paramUser.getUID() + ",") > -1) {
/*  99 */         bool3 = true;
/* 100 */         bool4 = true;
/*     */       } 
/*     */     } 
/* 103 */     if (bool3) b = 1; 
/* 104 */     if (bool4) b = 2;
/*     */     
/* 106 */     return b;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean isCanAuditPlan(String paramString1, String paramString2) {
/* 115 */     if (paramString1.equals("")) return false; 
/* 116 */     RecordSet recordSet = new RecordSet();
/* 117 */     recordSet.executeSql("select count(id) from PR_PlanReportAudit where planid=" + paramString1 + " and userid=" + paramString2);
/* 118 */     if (recordSet.next() && recordSet.getInt(1) > 0) {
/* 119 */       return true;
/*     */     }
/* 121 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean isCanDelPlan(String paramString1, String paramString2) {
/* 131 */     if (paramString1.equals("")) return false; 
/* 132 */     RecordSet recordSet = new RecordSet();
/* 133 */     recordSet.executeSql("select count(id) from PR_PlanReport where isvalid=1 and status=0 and userid=" + paramString2 + " and id=" + paramString1);
/* 134 */     if (recordSet.next() && recordSet.getInt(1) > 0) {
/* 135 */       return true;
/*     */     }
/* 137 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean isCanResetPlan(String paramString1, String paramString2) {
/* 147 */     if (paramString1.equals("")) return false; 
/* 148 */     RecordSet recordSet = new RecordSet();
/* 149 */     recordSet.executeSql("select count(t1.id) from PR_PlanReport t1 where t1.isvalid=1 and t1.status<>0 and t1.id=" + paramString1 + " and t1.userid=" + paramString2);
/* 150 */     if (recordSet.next() && recordSet.getInt(1) > 0) {
/* 151 */       return true;
/*     */     }
/* 153 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean isCanEditPlan(String paramString1, String paramString2) {
/* 163 */     if (paramString1.equals("")) return false; 
/* 164 */     RecordSet recordSet = new RecordSet();
/* 165 */     recordSet.executeSql("select count(id) from PR_PlanReport where isvalid=1 and (status=0 or status=2) and id=" + paramString1 + " and userid=" + paramString2);
/* 166 */     if (recordSet.next() && recordSet.getInt(1) > 0) {
/* 167 */       return true;
/*     */     }
/* 169 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean isCanViewPlan(String paramString1, String paramString2) {
/* 179 */     if (paramString1.equals("")) return false; 
/* 180 */     RecordSet recordSet = new RecordSet();
/* 181 */     boolean bool = false;
/* 182 */     recordSet.executeSql("select userid,auditids,shareids,type1 from PR_PlanReport where isvalid=1 and id=" + paramString1);
/* 183 */     if (recordSet.next()) {
/* 184 */       String str1 = Util.null2String(recordSet.getString(1));
/* 185 */       String str2 = Util.null2String(recordSet.getString(2));
/* 186 */       String str3 = Util.null2String(recordSet.getString(3));
/* 187 */       String str4 = Util.null2String(recordSet.getString(4));
/*     */       
/* 189 */       if (str1.equals(paramString2)) {
/* 190 */         bool = true;
/* 191 */       } else if (this.rc.isManager(Util.getIntValue(paramString2), str1)) {
/* 192 */         bool = true;
/* 193 */       } else if (("," + str2 + ",").indexOf("," + paramString2 + ",") > -1) {
/* 194 */         bool = true;
/* 195 */       } else if (("," + str3 + ",").indexOf("," + paramString2 + ",") > -1) {
/* 196 */         bool = true;
/*     */       } else {
/* 198 */         String str = "";
/* 199 */         recordSet.executeSql("select id,auditids,shareids from PR_PlanProgram where userid=" + str1 + " and programtype=" + str4);
/* 200 */         if (recordSet.next()) {
/* 201 */           str = Util.null2String(recordSet.getString("shareids"));
/*     */         }
/* 203 */         if (("," + str + ",").indexOf("," + paramString2 + ",") > -1) {
/* 204 */           bool = true;
/*     */         } else {
/* 206 */           recordSet.executeSql("select reportaudit,reportview from PR_BaseSetting where resourceid=" + this.rc.getSubCompanyID(str1) + " and resourcetype=2");
/* 207 */           if (recordSet.next()) {
/* 208 */             String str5 = Util.null2String(recordSet.getString("reportaudit"));
/* 209 */             String str6 = Util.null2String(recordSet.getString("reportview"));
/* 210 */             if (("," + str5 + ",").indexOf("," + paramString2 + ",") > -1) {
/* 211 */               bool = true;
/* 212 */             } else if (("," + str6 + ",").indexOf("," + paramString2 + ",") > -1) {
/* 213 */               bool = true;
/*     */             } 
/*     */           } 
/*     */         } 
/*     */       } 
/*     */     } 
/* 219 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean isCanSharePlan(String paramString1, String paramString2) {
/* 228 */     if (paramString1.equals("")) return false; 
/* 229 */     RecordSet recordSet = new RecordSet();
/* 230 */     boolean bool = false;
/* 231 */     recordSet.executeSql("select userid,auditids from PR_PlanReport where isvalid=1 and id=" + paramString1);
/* 232 */     if (recordSet.next()) {
/* 233 */       String str1 = Util.null2String(recordSet.getString(1));
/* 234 */       String str2 = Util.null2String(recordSet.getString(2));
/*     */       
/* 236 */       if (str1.equals(paramString2)) {
/* 237 */         bool = true;
/* 238 */       } else if (this.rc.isManager(Util.getIntValue(paramString2), str1)) {
/* 239 */         bool = true;
/* 240 */       } else if (("," + str2 + ",").indexOf("," + paramString2 + ",") > -1) {
/* 241 */         bool = true;
/*     */       } else {
/* 243 */         recordSet.executeSql("select reportaudit from PR_BaseSetting where resourceid=" + this.rc.getSubCompanyID(str1) + " and resourcetype=2");
/*     */         
/* 245 */         String str = Util.null2String(recordSet.getString("reportaudit"));
/* 246 */         if (recordSet.next() && ("," + str + ",").indexOf("," + paramString2 + ",") > -1) {
/* 247 */           bool = true;
/*     */         }
/*     */       } 
/*     */     } 
/*     */     
/* 252 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getUnAuditPlanHrm(String paramString) {
/* 261 */     if (paramString.equals("")) return ""; 
/* 262 */     RecordSet recordSet = new RecordSet();
/* 263 */     String str = "";
/* 264 */     recordSet.executeSql("select userid from PR_PlanReportAudit where planid=" + paramString);
/* 265 */     while (recordSet.next()) {
/* 266 */       str = str + "," + Util.null2String(recordSet.getString(1));
/*     */     }
/* 268 */     if (!str.equals("")) str = str.substring(1); 
/* 269 */     return str;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/pr/util/RightUtil.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */