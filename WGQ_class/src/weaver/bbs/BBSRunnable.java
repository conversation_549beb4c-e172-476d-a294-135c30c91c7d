/*    */ package weaver.bbs;
/*    */ 
/*    */ public class BBSRunnable
/*    */   implements Runnable {
/*    */   public String userid;
/*    */   public String passwordnew;
/*    */   
/*    */   public BBSRunnable(String paramString1, String paramString2) {
/*  9 */     this.userid = paramString1;
/* 10 */     this.passwordnew = paramString2;
/*    */   }
/*    */   
/*    */   public void run() {
/* 14 */     UserOAToBBS userOAToBBS = new UserOAToBBS();
/* 15 */     userOAToBBS.changBBSUser(this.userid, this.passwordnew);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/bbs/BBSRunnable.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */