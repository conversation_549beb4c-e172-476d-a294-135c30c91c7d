/*     */ package weaver.bbs;
/*     */ 
/*     */ import java.sql.Connection;
/*     */ import java.sql.ResultSet;
/*     */ import java.sql.Statement;
/*     */ import weaver.conn.ConnectionMysql;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.GCONST;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class UserOAToBBS
/*     */   extends BaseBean
/*     */ {
/*     */   private Connection conns;
/*     */   private Statement statement;
/*     */   private ResultSet resultSet;
/*  30 */   private String usertalbe = "";
/*     */   
/*     */   public UserOAToBBS() {
/*  33 */     this.usertalbe = getPropValue(GCONST.getConfigFile(), "ecologybbs.tables") + "users";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void changBBSUser(String paramString1, String paramString2) {
/*  42 */     String str = "";
/*  43 */     int i = 0;
/*  44 */     paramString2 = paramString2.toLowerCase();
/*     */     
/*     */     try {
/*  47 */       ConnectionMysql connectionMysql = ConnectionMysql.getInstance();
/*  48 */       this.conns = connectionMysql.getConnection();
/*  49 */       this.statement = this.conns.createStatement();
/*  50 */       str = "select * from " + this.usertalbe + " where username='" + paramString1 + "' ";
/*  51 */       this.resultSet = this.statement.executeQuery(str);
/*     */       
/*  53 */       if (this.resultSet.next())
/*     */       {
/*  55 */         this.statement.executeUpdate("update " + this.usertalbe + " set user_password='" + paramString2 + "' where username='" + paramString1 + "' ");
/*     */       }
/*     */       else
/*     */       {
/*  59 */         this.resultSet = this.statement.executeQuery("select max(user_id) from " + this.usertalbe);
/*  60 */         if (this.resultSet.next()) i = this.resultSet.getInt(1) + 1; 
/*  61 */         this.statement.executeUpdate("insert into " + this.usertalbe + " (user_id,username,user_password,user_regdate,user_timezone,user_style,user_dateformat) SELECT " + i + ",'" + paramString1 + "','" + paramString2 + "',user_regdate,8,user_style,user_dateformat FROM " + this.usertalbe + " WHERE user_level=1 ");
/*     */       
/*     */       }
/*     */     
/*     */     }
/*  66 */     catch (Exception exception) {
/*     */ 
/*     */       
/*  69 */       writeLog(exception);
/*     */     } finally {
/*     */ 
/*     */       
/*     */       try {
/*  74 */         this.resultSet.close();
/*  75 */         this.statement.close();
/*  76 */         this.conns.close();
/*     */       }
/*  78 */       catch (Exception exception) {}
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean initBBSUser() throws Exception {
/*  88 */     byte b = 10;
/*  89 */     boolean bool = true;
/*     */     try {
/*  91 */       RecordSet recordSet = new RecordSet();
/*  92 */       recordSet.execute("select lastname,password from hrmresource where (loginid is not null) and status in (0,1,2,3,5) ");
/*  93 */       ConnectionMysql connectionMysql = ConnectionMysql.getInstance();
/*  94 */       this.conns = connectionMysql.getConnection();
/*  95 */       this.statement = this.conns.createStatement();
/*  96 */       this.statement.executeUpdate("delete from " + this.usertalbe + " where user_level!=1 and user_id!=-1");
/*     */       
/*  98 */       while (recordSet.next())
/*     */       {
/* 100 */         String str1 = recordSet.getString(1);
/* 101 */         String str2 = recordSet.getString(2);
/* 102 */         str2 = str2.toLowerCase();
/* 103 */         String str3 = "insert into " + this.usertalbe + " (user_id,username,user_password,user_regdate,user_timezone,user_style,user_dateformat) SELECT " + b + ",'" + str1 + "','" + str2 + "',user_regdate,8,user_style,user_dateformat FROM " + this.usertalbe + " WHERE user_level=1";
/* 104 */         this.statement = this.conns.createStatement();
/* 105 */         this.statement.executeUpdate(str3);
/* 106 */         b++;
/*     */       }
/*     */     
/*     */     }
/* 110 */     catch (Exception exception) {
/*     */       
/* 112 */       writeLog(exception);
/* 113 */       bool = false;
/*     */     } finally {
/*     */ 
/*     */       
/*     */       try {
/*     */         
/* 119 */         this.statement.close();
/* 120 */         this.conns.close();
/*     */       }
/* 122 */       catch (Exception exception) {}
/*     */     } 
/*     */ 
/*     */     
/* 126 */     return bool;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/bbs/UserOAToBBS.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */