/*    */ package weaver.admincenter.file;
/*    */ 
/*    */ import java.io.File;
/*    */ import java.io.FileOutputStream;
/*    */ import java.io.InputStream;
/*    */ import org.apache.commons.fileupload.FileItem;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class UploadFile
/*    */ {
/*    */   public boolean upload(FileItem paramFileItem, File paramFile) {
/*    */     try {
/* 21 */       paramFile.getParentFile().mkdirs();
/*    */       
/* 23 */       InputStream inputStream = paramFileItem.getInputStream();
/*    */       
/* 25 */       FileOutputStream fileOutputStream = new FileOutputStream(paramFile);
/*    */       
/* 27 */       byte[] arrayOfByte = new byte[inputStream.available()];
/*    */       
/*    */       while (true) {
/* 30 */         int i = inputStream.read(arrayOfByte);
/* 31 */         if (i == -1) {
/*    */           break;
/*    */         }
/* 34 */         fileOutputStream.write(arrayOfByte, 0, i);
/*    */       } 
/*    */       
/* 37 */       if (fileOutputStream != null) {
/* 38 */         fileOutputStream.flush();
/* 39 */         fileOutputStream.close();
/*    */       } 
/* 41 */       if (inputStream != null) {
/* 42 */         inputStream.close();
/*    */       }
/* 44 */     } catch (Exception exception) {
/* 45 */       exception.printStackTrace();
/* 46 */       return false;
/*    */     } 
/* 48 */     return true;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/admincenter/file/UploadFile.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */