/*     */ package weaver.admincenter.homepage;
/*     */ 
/*     */ import java.io.ByteArrayOutputStream;
/*     */ import java.net.HttpURLConnection;
/*     */ import java.net.URL;
/*     */ import java.sql.Connection;
/*     */ import java.sql.ResultSet;
/*     */ import java.sql.SQLException;
/*     */ import java.sql.Statement;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import net.sf.json.JSONArray;
/*     */ import net.sf.json.JSONObject;
/*     */ import org.jdom.Document;
/*     */ import org.jdom.Element;
/*     */ import org.jdom.input.SAXBuilder;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.StaticObj;
/*     */ import weaver.general.Util;
/*     */ import weaver.homepage.cominfo.HomepageElementCominfo;
/*     */ import weaver.homepage.cominfo.HomepageElementFieldCominfo;
/*     */ import weaver.hrm.User;
/*     */ import weaver.interfaces.datasource.DataSource;
/*     */ import weaver.page.style.ElementStyleCominfo;
/*     */ import weaver.security.util.SecurityMethodUtil;
/*     */ 
/*     */ 
/*     */ public class WeaverElement
/*     */   extends BaseBean
/*     */ {
/*  33 */   private ElementStyleCominfo esc = new ElementStyleCominfo();
/*  34 */   private HomepageElementCominfo hpec = new HomepageElementCominfo();
/*  35 */   private HomepageElementFieldCominfo hpefc = new HomepageElementFieldCominfo();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getOutDataSource(String paramString1, int paramInt1, int paramInt2, User paramUser, String paramString2, String paramString3) {
/*  49 */     if (paramString3.equals("")) return ""; 
/*  50 */     StringBuffer stringBuffer = new StringBuffer();
/*  51 */     Connection connection = null;
/*  52 */     Statement statement = null;
/*  53 */     ResultSet resultSet = null;
/*  54 */     paramString2 = "datasource." + paramString2;
/*  55 */     DataSource dataSource = (DataSource)StaticObj.getServiceByFullname(paramString2, DataSource.class);
/*     */     
/*     */     try {
/*  58 */       connection = dataSource.getConnection();
/*     */       
/*  60 */       if (connection != null) {
/*  61 */         statement = connection.createStatement();
/*  62 */         resultSet = statement.executeQuery(paramString3);
/*  63 */         boolean bool = true;
/*  64 */         Map map1 = getShowFieldSetting(paramString1, paramInt1, paramInt2);
/*  65 */         int i = Util.getIntValue((String)map1.get("perpage"));
/*  66 */         Map map2 = getShowField(this.hpec.getEbaseid(paramString1), "SQL");
/*  67 */         String str1 = Util.null2String((String)map1.get("showfield"));
/*  68 */         if ("".equals(str1)) bool = false; 
/*  69 */         String[] arrayOfString = Util.TokenizerStringNew(str1, ",");
/*     */         
/*  71 */         stringBuffer.append("<TABLE  id='_contenttable_" + paramString1 + "' class=\"Econtent\"  width=\"100%\">");
/*  72 */         stringBuffer.append("<TR>");
/*  73 */         stringBuffer.append("<TD width=\"1px\"></TD>");
/*  74 */         stringBuffer.append("<TD width=\"*\">");
/*  75 */         stringBuffer.append("<TABLE width=\"100%\">");
/*  76 */         byte b = 0;
/*  77 */         String str2 = "";
/*  78 */         if (!"".equals(this.esc.getIconEsymbol(this.hpec.getStyleid(paramString1))))
/*  79 */           str2 = "<img name='esymbol' src='" + this.esc.getIconEsymbol(this.hpec.getStyleid(paramString1)) + "'>"; 
/*  80 */         while (resultSet.next() && bool) {
/*  81 */           stringBuffer.append("<TR  height=\"18px\">");
/*  82 */           stringBuffer.append("<TD width=\"8\">");
/*  83 */           stringBuffer.append(str2);
/*  84 */           stringBuffer.append("</TD>");
/*  85 */           for (byte b1 = 0; b1 < arrayOfString.length; b1++) {
/*  86 */             if (map2.get(arrayOfString[b1]) != null) {
/*  87 */               String str3 = resultSet.getString(Util.getIntValue("" + map2.get(arrayOfString[b1])));
/*  88 */               String str4 = this.hpefc.getIsLimitLength(arrayOfString[b1]);
/*  89 */               if ("1".equals(str4)) {
/*  90 */                 int j = getLimitLength(paramString1, arrayOfString[b1], paramInt1, paramInt2);
/*  91 */                 Util.getMoreStr(str3, j, "...");
/*  92 */                 str3 = "<font class='font'>" + str3 + "</font>";
/*     */               } 
/*  94 */               String str5 = this.hpefc.getFieldWidth(arrayOfString[b1]);
/*  95 */               stringBuffer.append("<TD width=\"" + str5 + "\"");
/*  96 */               if ("1".equals(str4))
/*  97 */                 stringBuffer.append(" title=\"" + str3 + "\" "); 
/*  98 */               stringBuffer.append("><font class=font>" + str3 + "</font></TD>");
/*     */             } 
/* 100 */           }  stringBuffer.append("</TR>");
/* 101 */           b++;
/* 102 */           if (b < i) {
/* 103 */             stringBuffer.append("<TR class='sparator' style='height:1px' height=1px><td style='padding:0px' colspan=" + (arrayOfString.length + 1) + "></td></TR>");
/*     */           }
/*     */         } 
/* 106 */         stringBuffer.append("</TABLE>");
/* 107 */         stringBuffer.append(" </TD>");
/* 108 */         stringBuffer.append(" <TD width=\"1px\"></TD>");
/* 109 */         stringBuffer.append("</TR>");
/* 110 */         stringBuffer.append("</TABLE>");
/*     */       } 
/* 112 */     } catch (SQLException sQLException) {
/*     */       
/* 114 */       sQLException.printStackTrace();
/*     */     } 
/* 116 */     return stringBuffer.toString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getOutDataSource(String paramString1, int paramInt1, int paramInt2, User paramUser, String paramString2) {
/* 129 */     if (paramString2.equals("")) return ""; 
/* 130 */     StringBuffer stringBuffer = new StringBuffer();
/* 131 */     RecordSet recordSet = new RecordSet();
/* 132 */     recordSet.executeSql(paramString2);
/* 133 */     boolean bool = true;
/*     */     
/* 135 */     Map map1 = getShowFieldSetting(paramString1, paramInt1, paramInt2);
/* 136 */     int i = Util.getIntValue((String)map1.get("perpage"));
/* 137 */     Map map2 = getShowField(this.hpec.getEbaseid(paramString1), "SQL");
/* 138 */     String str1 = Util.null2String((String)map1.get("showfield"));
/* 139 */     if ("".equals(str1)) bool = false; 
/* 140 */     String[] arrayOfString = Util.TokenizerStringNew(str1, ",");
/*     */     
/* 142 */     stringBuffer.append("<TABLE  id='_contenttable_" + paramString1 + "' class=\"Econtent\"  width=\"100%\">");
/* 143 */     stringBuffer.append("<TR>");
/* 144 */     stringBuffer.append("<TD width=\"1px\"></TD>");
/* 145 */     stringBuffer.append("<TD width=\"*\">");
/* 146 */     stringBuffer.append("<TABLE width=\"100%\">");
/* 147 */     byte b = 0;
/* 148 */     String str2 = "";
/* 149 */     if (!"".equals(this.esc.getIconEsymbol(this.hpec.getStyleid(paramString1))))
/* 150 */       str2 = "<img name='esymbol' src='" + this.esc.getIconEsymbol(this.hpec.getStyleid(paramString1)) + "'>"; 
/* 151 */     while (recordSet.next() && bool) {
/* 152 */       stringBuffer.append("<TR  height=\"18px\">");
/* 153 */       stringBuffer.append("<TD width=\"8\">");
/* 154 */       stringBuffer.append(str2);
/* 155 */       stringBuffer.append("</TD>");
/* 156 */       for (byte b1 = 0; b1 < arrayOfString.length; b1++) {
/* 157 */         if (map2.get(arrayOfString[b1]) != null) {
/* 158 */           String str3 = recordSet.getString(Util.getIntValue((String)map2.get(arrayOfString[b1])));
/* 159 */           String str4 = this.hpefc.getIsLimitLength(arrayOfString[b1]);
/* 160 */           if ("1".equals(str4)) {
/* 161 */             int j = getLimitLength(paramString1, arrayOfString[b1], paramInt1, paramInt2);
/* 162 */             Util.getMoreStr(str3, j, "...");
/* 163 */             str3 = "<font class='font'>" + str3 + "</font>";
/*     */           } 
/* 165 */           String str5 = this.hpefc.getFieldWidth(arrayOfString[b1]);
/* 166 */           stringBuffer.append("<TD width=\"" + str5 + "\"");
/* 167 */           if ("1".equals(str4))
/* 168 */             stringBuffer.append(" title=\"" + str3 + "\" "); 
/* 169 */           stringBuffer.append("><font class=font>" + str3 + "</font></TD>");
/*     */         } 
/* 171 */       }  stringBuffer.append("</TR>");
/* 172 */       b++;
/* 173 */       if (b < i) {
/* 174 */         stringBuffer.append("<TR class='sparator' style='height:1px' height=1px><td style='padding:0px' colspan=" + (arrayOfString.length + 1) + "></td></TR>");
/*     */       }
/*     */     } 
/* 177 */     stringBuffer.append("</TABLE>");
/* 178 */     stringBuffer.append(" </TD>");
/* 179 */     stringBuffer.append(" <TD width=\"1px\"></TD>");
/* 180 */     stringBuffer.append("</TR>");
/* 181 */     stringBuffer.append("</TABLE>");
/* 182 */     return stringBuffer.toString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map getShowFieldSetting(String paramString, int paramInt1, int paramInt2) {
/* 193 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 194 */     RecordSet recordSet = new RecordSet();
/* 195 */     String str = "select perpage,linkmode,showfield,sharelevel,hpid,currentTab from hpElementSettingDetail where eid='" + paramString + "' and userid=" + paramInt1 + " and usertype=" + paramInt2;
/* 196 */     recordSet.executeSql(str);
/* 197 */     if (recordSet.next()) {
/* 198 */       hashMap.put("perpage", recordSet.getString("perpage"));
/* 199 */       hashMap.put("linkmode", recordSet.getString("linkmode"));
/* 200 */       hashMap.put("showfield", recordSet.getString("showfield"));
/* 201 */       hashMap.put("sharelevel", recordSet.getString("sharelevel"));
/* 202 */       hashMap.put("hpid", recordSet.getString("hpid"));
/* 203 */       hashMap.put("currentTab", recordSet.getString("currentTab"));
/*     */     } 
/* 205 */     return hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map getShowField(String paramString1, String paramString2) {
/* 214 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 215 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 216 */     HashMap<Object, Object> hashMap3 = new HashMap<>();
/* 217 */     RecordSet recordSet = new RecordSet();
/* 218 */     String str = "select id,fieldColumn,ordernum from hpFieldElement where elementid='" + paramString1 + "' order by ordernum";
/* 219 */     recordSet.executeSql(str);
/* 220 */     byte b = 1;
/* 221 */     while (recordSet.next()) {
/* 222 */       int i = recordSet.getInt("ordernum");
/* 223 */       hashMap2.put(recordSet.getString("id"), Integer.valueOf((i == b) ? i : b));
/* 224 */       hashMap3.put(recordSet.getString("id"), recordSet.getString("fieldColumn"));
/* 225 */       b++;
/*     */     } 
/* 227 */     if ("SQL".equals(paramString2)) {
/* 228 */       hashMap1 = hashMap2;
/* 229 */     } else if ("JSON".equals(paramString2) || "XML".equals(paramString2)) {
/* 230 */       hashMap1 = hashMap3;
/* 231 */     }  return hashMap1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getLimitLength(String paramString1, String paramString2, int paramInt1, int paramInt2) {
/* 245 */     RecordSet recordSet = new RecordSet();
/* 246 */     int i = 8;
/* 247 */     String str = "select charnum from hpFieldLength where eid=" + paramString1 + " and efieldid=" + paramString2 + " and userid=" + paramInt1 + " and usertype=" + paramInt2;
/*     */     
/* 249 */     recordSet.executeSql(str);
/* 250 */     if (recordSet.next())
/* 251 */       i = Util.getIntValue(recordSet.getString("charnum")); 
/* 252 */     return i;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getOutPageSourceToXML(String paramString1, int paramInt1, int paramInt2, User paramUser, String paramString2) {
/* 265 */     StringBuffer stringBuffer = new StringBuffer();
/*     */     try {
/* 267 */       URL uRL = new URL(paramString2);
/* 268 */       SAXBuilder sAXBuilder = new SAXBuilder();
/*     */       
/* 270 */       SecurityMethodUtil.setSaxBuilderFeature(sAXBuilder);
/* 271 */       Document document = sAXBuilder.build(paramString2);
/* 272 */       Element element = document.getRootElement();
/* 273 */       List<Element> list = element.getChildren("item");
/*     */       
/* 275 */       boolean bool = true;
/* 276 */       Map map1 = getShowFieldSetting(paramString1, paramInt1, paramInt2);
/* 277 */       int i = Util.getIntValue((String)map1.get("perpage"));
/* 278 */       Map map2 = getShowField(this.hpec.getEbaseid(paramString1), "XML");
/* 279 */       String str1 = Util.null2String((String)map1.get("showfield"));
/* 280 */       if ("".equals(str1)) bool = false; 
/* 281 */       String[] arrayOfString = Util.TokenizerStringNew(str1, ",");
/*     */       
/* 283 */       stringBuffer.append("<TABLE  id='_contenttable_" + paramString1 + "' class=\"Econtent\"  width=\"100%\">");
/* 284 */       stringBuffer.append("<TR>");
/* 285 */       stringBuffer.append("<TD width=\"1px\"></TD>");
/* 286 */       stringBuffer.append("<TD width=\"*\">");
/* 287 */       stringBuffer.append("<TABLE width=\"100%\">");
/* 288 */       byte b1 = 0;
/* 289 */       String str2 = "";
/* 290 */       if (!"".equals(this.esc.getIconEsymbol(this.hpec.getStyleid(paramString1))))
/* 291 */         str2 = "<img name='esymbol' src='" + this.esc.getIconEsymbol(this.hpec.getStyleid(paramString1)) + "'>"; 
/* 292 */       for (byte b2 = 0; b2 < list.size(); ) {
/* 293 */         Element element1 = list.get(b2);
/* 294 */         stringBuffer.append("<TR  height=\"18px\">");
/* 295 */         stringBuffer.append("<TD width=\"8\">");
/* 296 */         stringBuffer.append(str2);
/* 297 */         stringBuffer.append("</TD>");
/* 298 */         for (byte b = 0; b < arrayOfString.length; b++) {
/* 299 */           String str3 = element1.getChildText((String)map2.get(arrayOfString[b]));
/* 300 */           String str4 = this.hpefc.getIsLimitLength(arrayOfString[b]);
/* 301 */           if ("1".equals(str4)) {
/* 302 */             int j = getLimitLength(paramString1, arrayOfString[b], paramInt1, paramInt2);
/* 303 */             Util.getMoreStr(str3, j, "...");
/* 304 */             str3 = "<font class='font'>" + str3 + "</font>";
/*     */           } 
/* 306 */           String str5 = this.hpefc.getFieldWidth(arrayOfString[b]);
/* 307 */           stringBuffer.append("<TD width=\"" + str5 + "\"");
/* 308 */           if ("1".equals(str4))
/* 309 */             stringBuffer.append(" title=\"" + str3 + "\" "); 
/* 310 */           stringBuffer.append("><font class=font>" + str3 + "</font></TD>");
/*     */         } 
/* 312 */         stringBuffer.append("</TR>");
/* 313 */         b1++;
/* 314 */         if (b1 < i) {
/* 315 */           stringBuffer.append("<TR class='sparator' style='height:1px' height=1px><td style='padding:0px' colspan=" + (arrayOfString.length + 1) + "></td></TR>");
/*     */           b2++;
/*     */         } 
/*     */       } 
/* 319 */       stringBuffer.append("</TABLE>");
/* 320 */       stringBuffer.append(" </TD>");
/* 321 */       stringBuffer.append(" <TD width=\"1px\"></TD>");
/* 322 */       stringBuffer.append("</TR>");
/* 323 */       stringBuffer.append("</TABLE>");
/* 324 */     } catch (Exception exception) {
/*     */       
/* 326 */       exception.printStackTrace();
/*     */     } 
/* 328 */     return stringBuffer.toString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getOutPageSourceToJSON(String paramString1, int paramInt1, int paramInt2, User paramUser, String paramString2) {
/* 341 */     if ("".equals(paramString2)) return ""; 
/* 342 */     StringBuffer stringBuffer = new StringBuffer();
/* 343 */     JSONObject jSONObject = new JSONObject();
/*     */     try {
/* 345 */       URL uRL = new URL(paramString2);
/*     */       
/* 347 */       HttpURLConnection httpURLConnection = (HttpURLConnection)uRL.openConnection();
/*     */       
/* 349 */       httpURLConnection.setConnectTimeout(3000);
/* 350 */       httpURLConnection.setDoInput(true);
/* 351 */       httpURLConnection.setRequestMethod("GET");
/*     */       
/* 353 */       int i = httpURLConnection.getResponseCode();
/* 354 */       if (i == 200) {
/*     */ 
/*     */         
/* 357 */         ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
/* 358 */         byte[] arrayOfByte = new byte[1024];
/* 359 */         int k = 0;
/*     */         
/* 361 */         while ((k = httpURLConnection.getInputStream().read(arrayOfByte, 0, arrayOfByte.length)) != -1)
/*     */         {
/* 363 */           byteArrayOutputStream.write(arrayOfByte, 0, k);
/*     */         }
/*     */         
/* 366 */         String str = new String(byteArrayOutputStream.toByteArray());
/* 367 */         jSONObject = JSONObject.fromObject(new String(byteArrayOutputStream.toByteArray()));
/*     */       } 
/* 369 */       if (jSONObject.isEmpty()) return ""; 
/* 370 */       JSONArray jSONArray = jSONObject.getJSONArray("item");
/*     */       
/* 372 */       boolean bool = true;
/* 373 */       Map map1 = getShowFieldSetting(paramString1, paramInt1, paramInt2);
/* 374 */       int j = Util.getIntValue((String)map1.get("perpage"));
/* 375 */       Map map2 = getShowField(this.hpec.getEbaseid(paramString1), "JSON");
/* 376 */       String str1 = Util.null2String((String)map1.get("showfield"));
/* 377 */       if ("".equals(str1)) bool = false; 
/* 378 */       String[] arrayOfString = Util.TokenizerStringNew(str1, ",");
/*     */       
/* 380 */       stringBuffer.append("<TABLE  id='_contenttable_" + paramString1 + "' class=\"Econtent\"  width=\"100%\">");
/* 381 */       stringBuffer.append("<TR>");
/* 382 */       stringBuffer.append("<TD width=\"1px\"></TD>");
/* 383 */       stringBuffer.append("<TD width=\"*\">");
/* 384 */       stringBuffer.append("<TABLE width=\"100%\">");
/* 385 */       byte b1 = 0;
/* 386 */       String str2 = "";
/* 387 */       if (!"".equals(this.esc.getIconEsymbol(this.hpec.getStyleid(paramString1))))
/* 388 */         str2 = "<img name='esymbol' src='" + this.esc.getIconEsymbol(this.hpec.getStyleid(paramString1)) + "'>"; 
/* 389 */       for (byte b2 = 0; b2 < jSONArray.size(); ) {
/* 390 */         JSONObject jSONObject1 = (JSONObject)jSONArray.get(b2);
/* 391 */         stringBuffer.append("<TR  height=\"18px\">");
/* 392 */         stringBuffer.append("<TD width=\"8\">");
/* 393 */         stringBuffer.append(str2);
/* 394 */         stringBuffer.append("</TD>");
/* 395 */         for (byte b = 0; b < arrayOfString.length; b++) {
/* 396 */           String str3 = jSONObject1.getString((String)map2.get(arrayOfString[b]));
/* 397 */           String str4 = this.hpefc.getIsLimitLength(arrayOfString[b]);
/* 398 */           if ("1".equals(str4)) {
/* 399 */             int k = getLimitLength(paramString1, arrayOfString[b], paramInt1, paramInt2);
/* 400 */             Util.getMoreStr(str3, k, "...");
/* 401 */             str3 = "<font class='font'>" + str3 + "</font>";
/*     */           } 
/* 403 */           String str5 = this.hpefc.getFieldWidth(arrayOfString[b]);
/* 404 */           stringBuffer.append("<TD width=\"" + str5 + "\"");
/* 405 */           if ("1".equals(str4))
/* 406 */             stringBuffer.append(" title=\"" + str3 + "\" "); 
/* 407 */           stringBuffer.append("><font class=font>" + str3 + "</font></TD>");
/*     */         } 
/* 409 */         stringBuffer.append("</TR>");
/* 410 */         b1++;
/* 411 */         if (b1 < j) {
/* 412 */           stringBuffer.append("<TR class='sparator' style='height:1px' height=1px><td style='padding:0px' colspan=" + (arrayOfString.length + 1) + "></td></TR>");
/*     */           b2++;
/*     */         } 
/*     */       } 
/* 416 */       stringBuffer.append("</TABLE>");
/* 417 */       stringBuffer.append(" </TD>");
/* 418 */       stringBuffer.append(" <TD width=\"1px\"></TD>");
/* 419 */       stringBuffer.append("</TR>");
/* 420 */       stringBuffer.append("</TABLE>");
/* 421 */     } catch (Exception exception) {
/*     */       
/* 423 */       exception.printStackTrace();
/* 424 */       stringBuffer.append("URL address to access JSON format error or not, the following is a JSON string :" + jSONObject.toString());
/*     */     } 
/* 426 */     return stringBuffer.toString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getOutPageSourceToWebService(String paramString1, int paramInt1, int paramInt2, User paramUser, String paramString2) {
/* 439 */     StringBuffer stringBuffer = new StringBuffer();
/*     */     try {
/* 441 */       URL uRL = new URL(paramString2);
/* 442 */       SAXBuilder sAXBuilder = new SAXBuilder();
/*     */       
/* 444 */       SecurityMethodUtil.setSaxBuilderFeature(sAXBuilder);
/* 445 */       Document document = sAXBuilder.build(paramString2);
/* 446 */       Element element = document.getRootElement();
/* 447 */       List<Element> list = element.getChildren("item");
/*     */       
/* 449 */       boolean bool = true;
/* 450 */       Map map1 = getShowFieldSetting(paramString1, paramInt1, paramInt2);
/* 451 */       int i = Util.getIntValue((String)map1.get("perpage"));
/* 452 */       Map map2 = getShowField(this.hpec.getEbaseid(paramString1), "XML");
/* 453 */       String str1 = Util.null2String((String)map1.get("showfield"));
/* 454 */       if ("".equals(str1)) bool = false; 
/* 455 */       String[] arrayOfString = Util.TokenizerStringNew(str1, ",");
/*     */       
/* 457 */       stringBuffer.append("<TABLE  id='_contenttable_" + paramString1 + "' class=\"Econtent\"  width=\"100%\">");
/* 458 */       stringBuffer.append("<TR>");
/* 459 */       stringBuffer.append("<TD width=\"1px\"></TD>");
/* 460 */       stringBuffer.append("<TD width=\"*\">");
/* 461 */       stringBuffer.append("<TABLE width=\"100%\">");
/* 462 */       byte b1 = 0;
/* 463 */       String str2 = "";
/* 464 */       if (!"".equals(this.esc.getIconEsymbol(this.hpec.getStyleid(paramString1))))
/* 465 */         str2 = "<img name='esymbol' src='" + this.esc.getIconEsymbol(this.hpec.getStyleid(paramString1)) + "'>"; 
/* 466 */       for (byte b2 = 0; b2 < list.size(); ) {
/* 467 */         Element element1 = list.get(b2);
/* 468 */         stringBuffer.append("<TR  height=\"18px\">");
/* 469 */         stringBuffer.append("<TD width=\"8\">");
/* 470 */         stringBuffer.append(str2);
/* 471 */         stringBuffer.append("</TD>");
/* 472 */         for (byte b = 0; b < arrayOfString.length; b++) {
/* 473 */           String str3 = element1.getChildText((String)map2.get(arrayOfString[b]));
/* 474 */           String str4 = this.hpefc.getIsLimitLength(arrayOfString[b]);
/* 475 */           if ("1".equals(str4)) {
/* 476 */             int j = getLimitLength(paramString1, arrayOfString[b], paramInt1, paramInt2);
/* 477 */             Util.getMoreStr(str3, j, "...");
/* 478 */             str3 = "<font class='font'>" + str3 + "</font>";
/*     */           } 
/* 480 */           String str5 = this.hpefc.getFieldWidth(arrayOfString[b]);
/* 481 */           stringBuffer.append("<TD width=\"" + str5 + "\"");
/* 482 */           if ("1".equals(str4))
/* 483 */             stringBuffer.append(" title=\"" + str3 + "\" "); 
/* 484 */           stringBuffer.append("><font class=font>" + str3 + "</font></TD>");
/*     */         } 
/* 486 */         stringBuffer.append("</TR>");
/* 487 */         b1++;
/* 488 */         if (b1 < i) {
/* 489 */           stringBuffer.append("<TR class='sparator' style='height:1px' height=1px><td style='padding:0px' colspan=" + (arrayOfString.length + 1) + "></td></TR>");
/*     */           b2++;
/*     */         } 
/*     */       } 
/* 493 */       stringBuffer.append("</TABLE>");
/* 494 */       stringBuffer.append(" </TD>");
/* 495 */       stringBuffer.append(" <TD width=\"1px\"></TD>");
/* 496 */       stringBuffer.append("</TR>");
/* 497 */       stringBuffer.append("</TABLE>");
/* 498 */     } catch (Exception exception) {
/*     */       
/* 500 */       exception.printStackTrace();
/*     */     } 
/* 502 */     return stringBuffer.toString();
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/admincenter/homepage/WeaverElement.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */