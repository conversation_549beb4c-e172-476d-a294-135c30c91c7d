/*     */ package weaver.admincenter.homepage;
/*     */ 
/*     */ import java.io.File;
/*     */ import java.io.FileInputStream;
/*     */ import java.io.IOException;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Enumeration;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import java.util.Properties;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.hrm.User;
/*     */ import weaver.page.PageManager;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class PortalDataSource
/*     */ {
/*     */   public List<Map<String, String>> getEcology7Theme(User paramUser, Map<String, String> paramMap, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/*  33 */     List<Map<String, String>> list = new ArrayList();
/*  34 */     String str1 = paramHttpServletRequest.getSession().getServletContext().getRealPath("/");
/*  35 */     if (str1.lastIndexOf("/") != str1.length() - 1 && str1.lastIndexOf("\\") != str1.length() - 1) {
/*  36 */       str1 = str1 + "/";
/*     */     }
/*  38 */     String str2 = paramMap.get("theme");
/*     */     
/*  40 */     str2 = "soft".equals(str2) ? "ecologyBasic" : str2;
/*  41 */     if (str2.equals("ecology8")) {
/*  42 */       List list1 = getEcology8ThemeList(paramUser.getLanguage());
/*     */     } else {
/*  44 */       list = getNatInsThemeList(str1 + "wui/theme/" + str2);
/*     */     } 
/*  46 */     return list;
/*     */   }
/*     */   
/*     */   private List getEcology8ThemeList(int paramInt) {
/*  50 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*  51 */     RecordSet recordSet = new RecordSet();
/*     */     
/*  53 */     recordSet.executeSql("select * from ecology8theme order by id");
/*  54 */     while (recordSet.next()) {
/*  55 */       HashMap<Object, Object> hashMap = new HashMap<>();
/*  56 */       hashMap.put("id", recordSet.getString("id"));
/*  57 */       hashMap.put("name", recordSet.getString("name"));
/*  58 */       hashMap.put("themeName", recordSet.getString("name"));
/*     */       
/*  60 */       hashMap.put("type", recordSet.getString("type"));
/*  61 */       hashMap.put("div", getDiv(recordSet, paramInt));
/*  62 */       arrayList.add(hashMap);
/*     */     } 
/*     */     
/*  65 */     return arrayList;
/*     */   }
/*     */   
/*     */   public String getDiv(RecordSet paramRecordSet, int paramInt) {
/*  69 */     StringBuffer stringBuffer = new StringBuffer();
/*  70 */     String str = "#fff";
/*  71 */     if (paramRecordSet.getString("style").equals("dark")) {
/*  72 */       str = "#000";
/*     */     }
/*  74 */     stringBuffer.append("<div style='position:absolute;margin-left:-59px;margin-top:-34px;left:50%;top:50%;height:68px;width:118px;'>");
/*  75 */     stringBuffer.append("<div style='background:" + paramRecordSet.getString("logocolor") + ";height:28px;color:" + str + ";line-height:28px;text-align:left;padding-left:2px;'>");
/*  76 */     stringBuffer.append("<div style='text-align:left;;font-size:10px;'><span style='-webkit-transform: scale(0.8);display:block;margin-left:5px;'>" + SystemEnv.getHtmlLabelName(500149, paramInt) + "</span></div>");
/*  77 */     stringBuffer.append("</div>");
/*     */     
/*  79 */     stringBuffer.append("<div style='background:" + paramRecordSet.getString("hrmcolor") + ";height:20px;color:" + str + ";line-height:20px;text-align:left;padding-left:2px;'>");
/*  80 */     stringBuffer.append("<div style='text-align:left;font-size:9px'><span style='-webkit-transform: scale(0.8);display:block;margin-left:5px;'>" + SystemEnv.getHtmlLabelName(16139, paramInt) + "</span></div>");
/*  81 */     stringBuffer.append("</div>");
/*     */     
/*  83 */     stringBuffer.append("<div style='background:" + paramRecordSet.getString("leftcolor") + ";height:20px;color:" + str + ";line-height:20px;text-align:left;padding-left:2px;'>");
/*  84 */     stringBuffer.append("<div style='text-align:left;font-size:10px'><span style='-webkit-transform: scale(0.8);display:block;margin-left:5px;'>" + SystemEnv.getHtmlLabelName(582, paramInt) + "</span></div>");
/*  85 */     stringBuffer.append("</div>");
/*  86 */     stringBuffer.append("</div>");
/*  87 */     return stringBuffer.toString();
/*     */   }
/*     */ 
/*     */   
/*     */   private List getNatInsThemeList(String paramString) {
/*  92 */     ArrayList arrayList = null;
/*  93 */     if (paramString == null || "".equals(paramString)) {
/*  94 */       return null;
/*     */     }
/*     */     
/*  97 */     File file = new File(paramString);
/*     */     
/*  99 */     if (!file.exists() || !file.isDirectory()) {
/* 100 */       return null;
/*     */     }
/* 102 */     arrayList = new ArrayList();
/* 103 */     File[] arrayOfFile = file.listFiles();
/* 104 */     for (byte b = 0; b < arrayOfFile.length; b++) {
/* 105 */       File file1 = arrayOfFile[b];
/* 106 */       if (file1.getName().indexOf(".") == -1)
/*     */       {
/*     */         
/* 109 */         if (file1.isDirectory() && "skins".equals(file1.getName())) {
/* 110 */           List list = getNatInsSkinCfgList(paramString + "/skins/", file1.getName());
/* 111 */           if (list != null)
/* 112 */             arrayList.addAll(list); 
/*     */         } 
/*     */       }
/*     */     } 
/* 116 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   private List getNatInsSkinCfgList(String paramString1, String paramString2) {
/* 122 */     ArrayList<Map<String, String>> arrayList = null;
/* 123 */     if (paramString1 == null || "".equals(paramString1)) {
/* 124 */       return null;
/*     */     }
/*     */     
/* 127 */     File file = new File(paramString1);
/*     */     
/* 129 */     if (!file.exists() || !file.isDirectory()) {
/* 130 */       return null;
/*     */     }
/* 132 */     arrayList = new ArrayList();
/* 133 */     File[] arrayOfFile = file.listFiles();
/* 134 */     for (byte b = 0; b < arrayOfFile.length; b++) {
/* 135 */       File file1 = arrayOfFile[b];
/* 136 */       if (file1.getName().indexOf(".") == -1)
/*     */       {
/*     */         
/* 139 */         if (file1.isDirectory()) {
/* 140 */           Map<String, String> map = getProperties(paramString1 + file1.getName() + "/config.properties");
/* 141 */           if (map != null) {
/* 142 */             map.put("themeName", paramString2);
/*     */           }
/* 144 */           arrayList.add(map);
/*     */         } 
/*     */       }
/*     */     } 
/* 148 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private Map getProperties(String paramString) {
/* 156 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/* 158 */     Properties properties = new Properties();
/*     */     try {
/* 160 */       properties.load(new FileInputStream(paramString));
/* 161 */     } catch (IOException iOException) {
/* 162 */       iOException.printStackTrace();
/* 163 */       return hashMap;
/*     */     } 
/*     */     
/* 166 */     Enumeration<?> enumeration = properties.propertyNames();
/*     */     
/* 168 */     while (enumeration.hasMoreElements()) {
/* 169 */       String str1 = (String)enumeration.nextElement();
/* 170 */       String str2 = properties.getProperty(str1, "");
/* 171 */       hashMap.put(str1, str2);
/*     */     } 
/* 173 */     return hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<Map<String, String>> getImageLib(User paramUser, Map<String, String> paramMap, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/* 181 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 182 */     PageManager pageManager = new PageManager();
/* 183 */     String str1 = paramMap.get("dir");
/* 184 */     String str2 = PageManager.getRealPath("/page/resource/userfile/" + str1);
/* 185 */     File[] arrayOfFile = (new File(str2)).listFiles();
/* 186 */     for (byte b = 0; b < arrayOfFile.length; b++) {
/* 187 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 188 */       File file = arrayOfFile[b];
/* 189 */       if (!file.isDirectory()) {
/* 190 */         String str = file.getAbsolutePath();
/* 191 */         hashMap.put("filename", file.getName());
/* 192 */         hashMap.put("filetype", str.substring(str.indexOf(".") + 1));
/* 193 */         hashMap.put("filedir", file.getParentFile().getName());
/* 194 */         hashMap.put("filerealpath", str.substring(str.indexOf("page")));
/* 195 */         arrayList.add(hashMap);
/*     */       } 
/* 197 */     }  return (List)arrayList;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/admincenter/homepage/PortalDataSource.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */