/*     */ package weaver.admincenter.homepage;
/*     */ 
/*     */ import java.io.BufferedInputStream;
/*     */ import java.io.BufferedOutputStream;
/*     */ import java.io.File;
/*     */ import java.io.FileInputStream;
/*     */ import java.io.FileOutputStream;
/*     */ import java.io.IOException;
/*     */ import java.io.InputStream;
/*     */ import java.util.Enumeration;
/*     */ import java.util.zip.ZipEntry;
/*     */ import java.util.zip.ZipOutputStream;
/*     */ import org.apache.tools.zip.ZipEntry;
/*     */ import org.apache.tools.zip.ZipFile;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.security.util.SecurityMethodUtil;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class PortalElements
/*     */ {
/*     */   public static void main(String[] paramArrayOfString) {}
/*     */   
/*     */   public static void unZipFiles(File paramFile, String paramString) throws IOException {
/*  51 */     BaseBean baseBean = new BaseBean();
/*  52 */     File file = new File(paramString);
/*  53 */     if (!file.exists()) {
/*  54 */       file.mkdirs();
/*     */     }
/*  56 */     ZipFile zipFile = new ZipFile(paramFile);
/*  57 */     for (Enumeration<ZipEntry> enumeration = zipFile.getEntries(); enumeration.hasMoreElements(); ) {
/*  58 */       ZipEntry zipEntry = enumeration.nextElement();
/*  59 */       String str1 = zipEntry.getName();
/*  60 */       if (!SecurityMethodUtil.isValidPath(zipEntry.getName())) {
/*  61 */         baseBean.writeLog("Zip压缩包名" + zipEntry.getName() + "不合法，解压失败！");
/*     */         continue;
/*     */       } 
/*  64 */       InputStream inputStream = zipFile.getInputStream(zipEntry);
/*  65 */       String str2 = (paramString + str1).replaceAll("\\*", "/");
/*     */       
/*  67 */       File file1 = new File(str2.substring(0, str2.lastIndexOf('/')));
/*  68 */       if (!file1.exists()) {
/*  69 */         file1.mkdirs();
/*     */       }
/*  71 */       File file2 = new File(str2);
/*     */       
/*  73 */       baseBean.writeLog("start ===" + str2);
/*     */       
/*  75 */       if (file2.isDirectory()) {
/*  76 */         baseBean.writeLog("fname :" + file2.getName());
/*     */         
/*     */         continue;
/*     */       } 
/*     */       
/*  81 */       baseBean.writeLog(str2);
/*     */       
/*  83 */       FileOutputStream fileOutputStream = new FileOutputStream(str2);
/*  84 */       byte[] arrayOfByte = new byte[4096];
/*     */       int i;
/*  86 */       while ((i = inputStream.read(arrayOfByte)) > 0) {
/*  87 */         fileOutputStream.write(arrayOfByte, 0, i);
/*     */       }
/*  89 */       inputStream.close();
/*  90 */       fileOutputStream.close();
/*     */     } 
/*  92 */     baseBean.writeLog("******************解压完毕********************");
/*     */   }
/*     */   
/*     */   public static void zipFile(File paramFile, String paramString, ZipOutputStream paramZipOutputStream) throws IOException {
/*  96 */     BaseBean baseBean = new BaseBean();
/*  97 */     File[] arrayOfFile = null;
/*  98 */     if (paramFile.isDirectory()) {
/*  99 */       arrayOfFile = paramFile.listFiles();
/*     */     } else {
/* 101 */       arrayOfFile = new File[1];
/* 102 */       arrayOfFile[0] = paramFile;
/*     */     } 
/*     */     
/* 105 */     FileInputStream fileInputStream = null;
/*     */     
/* 107 */     byte[] arrayOfByte = new byte[1024];
/* 108 */     int i = 0;
/*     */     try {
/* 110 */       for (File file : arrayOfFile) {
/* 111 */         if (file.isDirectory()) {
/* 112 */           String str = file.getPath().substring(paramString.length() + 1) + "/";
/* 113 */           paramZipOutputStream.putNextEntry((ZipEntry)new ZipEntry(str));
/* 114 */           zipFile(file, paramString, paramZipOutputStream);
/*     */         } else {
/* 116 */           String str = file.getPath().substring(paramString.length() + 1);
/* 117 */           baseBean.writeLog("**************************************" + str);
/* 118 */           fileInputStream = new FileInputStream(file);
/* 119 */           BufferedInputStream bufferedInputStream = new BufferedInputStream(fileInputStream);
/* 120 */           paramZipOutputStream.putNextEntry((ZipEntry)new ZipEntry(str));
/* 121 */           while ((i = bufferedInputStream.read(arrayOfByte)) != -1) {
/* 122 */             baseBean.writeLog(i + "===" + arrayOfByte.length);
/* 123 */             paramZipOutputStream.write(arrayOfByte, 0, i);
/*     */           } 
/*     */         } 
/*     */       } 
/*     */       
/* 128 */       baseBean.writeLog("******************压缩完毕********************");
/*     */     } finally {
/* 130 */       if (fileInputStream != null) {
/* 131 */         fileInputStream.close();
/*     */       }
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   private static void zip(ZipOutputStream paramZipOutputStream, File paramFile, String paramString, BufferedOutputStream paramBufferedOutputStream) throws Exception {
/* 139 */     BaseBean baseBean = new BaseBean();
/* 140 */     if (paramFile.isDirectory()) {
/* 141 */       File[] arrayOfFile = paramFile.listFiles();
/* 142 */       if (arrayOfFile.length == 0) {
/* 143 */         paramZipOutputStream.putNextEntry((ZipEntry)new ZipEntry(paramString + "/"));
/* 144 */         baseBean.writeLog(paramString + "/");
/*     */       } 
/* 146 */       for (byte b = 0; b < arrayOfFile.length; b++) {
/* 147 */         zip(paramZipOutputStream, arrayOfFile[b], paramString + "/" + arrayOfFile[b].getName(), paramBufferedOutputStream);
/*     */       }
/*     */     }
/*     */     else {
/*     */       
/* 152 */       paramZipOutputStream.putNextEntry((ZipEntry)new ZipEntry(paramString));
/* 153 */       baseBean.writeLog(paramString);
/* 154 */       FileInputStream fileInputStream = new FileInputStream(paramFile);
/* 155 */       BufferedInputStream bufferedInputStream = new BufferedInputStream(fileInputStream);
/* 156 */       int i = 0;
/* 157 */       while ((i = bufferedInputStream.read()) != -1) {
/* 158 */         paramBufferedOutputStream.write(i);
/*     */       }
/*     */       
/* 161 */       bufferedInputStream.close();
/* 162 */       fileInputStream.close();
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/admincenter/homepage/PortalElements.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */