/*     */ package weaver.admincenter.homepage;
/*     */ 
/*     */ import java.lang.reflect.Constructor;
/*     */ import java.lang.reflect.Method;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.homepage.cominfo.HomepageElementCominfo;
/*     */ import weaver.hrm.User;
/*     */ import weaver.page.HPTypeEnum;
/*     */ import weaver.page.PageCominfo;
/*     */ import weaver.page.PageUtil;
/*     */ import weaver.page.element.ElementBaseCominfo;
/*     */ import weaver.page.style.ElementStyleCominfo;
/*     */ import weaver.servicefiles.DataSourceXML;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class WeaverPortal
/*     */   extends BaseBean
/*     */ {
/*  31 */   private ElementCustomCominfo ecc = new ElementCustomCominfo();
/*  32 */   private ElementBaseCominfo ebc = new ElementBaseCominfo();
/*  33 */   private ElementStyleCominfo esc = new ElementStyleCominfo();
/*  34 */   private HomepageElementCominfo hpec = new HomepageElementCominfo();
/*  35 */   private WeaverBaseElementCominfo wbec = new WeaverBaseElementCominfo();
/*  36 */   private PageUtil pu = new PageUtil();
/*  37 */   private PageCominfo pc = new PageCominfo();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getElementSetting(HttpServletRequest paramHttpServletRequest) {
/*  49 */     String str1 = Util.null2String(paramHttpServletRequest.getParameter("pagetype"));
/*  50 */     String str2 = Util.null2String(paramHttpServletRequest.getParameter("ebaseid"));
/*  51 */     String str3 = Util.null2String(paramHttpServletRequest.getParameter("eid"));
/*  52 */     String str4 = Util.null2String(paramHttpServletRequest.getParameter("hpid"));
/*  53 */     int i = Util.getIntValue(paramHttpServletRequest.getParameter("subcompanyid"));
/*  54 */     String str5 = this.pc.getConfig().getString("template.share");
/*  55 */     User user = (User)paramHttpServletRequest.getSession(true).getAttribute("weaver_user@bean");
/*     */     
/*  57 */     StringBuffer stringBuffer1 = new StringBuffer("");
/*     */     
/*  59 */     StringBuffer stringBuffer2 = new StringBuffer();
/*  60 */     stringBuffer2.append("<div class=\"setting\" id=\"setting_");
/*  61 */     stringBuffer2.append(str3);
/*  62 */     stringBuffer2.append("\" operationurl=\"");
/*  63 */     String str6 = this.ebc.getOperation(str2);
/*  64 */     str6 = !"".equals(str6) ? str6 : ("".equals(this.ecc.getOperation(str2)) ? "/page/maint/element/ElementOperation.jsp" : this.ecc.getOperation(str2));
/*  65 */     stringBuffer2.append(str6);
/*  66 */     stringBuffer2.append("\">");
/*  67 */     stringBuffer2.append("\n");
/*  68 */     stringBuffer2.append("<div  class=\"weavertabs\">");
/*  69 */     stringBuffer2.append("\n");
/*     */     
/*  71 */     stringBuffer2.append("<div class=\"box tabs\" id=\"basicset\" style=\"height:100%\">");
/*  72 */     stringBuffer2.append("\n");
/*  73 */     stringBuffer2.append("<ul class=\"p_tab_menu\">");
/*  74 */     stringBuffer2.append("\n");
/*  75 */     stringBuffer2.append("<li id=\"tabContent\" class=\"current\" target=\"weavertabs-content-");
/*  76 */     stringBuffer2.append(str3);
/*  77 */     stringBuffer2.append("\" onclick=\"showTabs('" + str3 + "','tabContent')\">");
/*  78 */     stringBuffer2.append(SystemEnv.getHtmlLabelName(345, user.getLanguage()));
/*  79 */     stringBuffer2.append("</li>");
/*  80 */     stringBuffer2.append("\n");
/*  81 */     stringBuffer2.append("<li id=\"tabStyle\" target=\"weavertabs-style-");
/*  82 */     stringBuffer2.append(str3);
/*  83 */     stringBuffer2.append("\" onclick=\"showTabs('" + str3 + "','tabStyle')\">");
/*  84 */     stringBuffer2.append(SystemEnv.getHtmlLabelName(1014, user.getLanguage()));
/*  85 */     stringBuffer2.append("</li>");
/*  86 */     stringBuffer2.append("\n");
/*  87 */     if ("true".equals(str5) && !str1.equals("loginview") && !HPTypeEnum.HP_WORKFLOW_FORM.getName().equals(str1)) {
/*  88 */       stringBuffer2.append("<li id=\"tabShare\" target=\"weavertabs-share-");
/*  89 */       stringBuffer2.append(str3);
/*  90 */       stringBuffer2.append("\" onclick=\"showTabs('" + str3 + "','tabShare')\">");
/*  91 */       stringBuffer2.append(SystemEnv.getHtmlLabelName(119, user.getLanguage()));
/*  92 */       stringBuffer2.append("</li>");
/*  93 */       stringBuffer2.append("\n");
/*     */     } 
/*  95 */     stringBuffer2.append("<div class=\"tab_box\"></div>");
/*  96 */     stringBuffer2.append("</ul>");
/*  97 */     stringBuffer2.append("\n");
/*  98 */     stringBuffer2.append("</div>");
/*  99 */     stringBuffer2.append("\n");
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 130 */     stringBuffer2.append("<div  class=\"weavertabs-content\">");
/* 131 */     stringBuffer2.append("\n");
/* 132 */     stringBuffer2.append("<div id=\"weavertabs-content-");
/* 133 */     stringBuffer2.append(str3);
/* 134 */     stringBuffer2.append("\" url=\"/page/maint/element/ElementSetting.jsp?type=content&eid=" + str3 + "&ebaseid=" + str2 + "&subCompanyId=" + i + "\">");
/*     */     
/* 136 */     stringBuffer2.append("</div>");
/* 137 */     stringBuffer2.append("\n");
/* 138 */     stringBuffer2.append("<div id=\"weavertabs-style-");
/* 139 */     stringBuffer2.append(str3);
/* 140 */     stringBuffer2.append("\" url=\"/page/maint/element/ElementSetting.jsp?type=style&eid=" + str3 + "&ebaseid=" + str2 + "&subCompanyId=" + i + "\">");
/*     */     
/* 142 */     stringBuffer2.append("</div>");
/* 143 */     stringBuffer2.append("\n");
/* 144 */     if ("true".equals(str5) && !str1.equals("loginview")) {
/* 145 */       stringBuffer2.append("<div id=\"weavertabs-share-");
/* 146 */       stringBuffer2.append(str3);
/* 147 */       stringBuffer2.append("\" url=\"/page/maint/element/ElementSetting.jsp?type=share&eid=" + str3 + "&ebaseid=" + str2 + "&subCompanyId=" + i + "\">");
/*     */       
/* 149 */       stringBuffer2.append("</div>");
/* 150 */       stringBuffer2.append("\n");
/*     */     } 
/* 152 */     stringBuffer2.append("</div>");
/*     */     
/* 154 */     stringBuffer2.append("\n");
/* 155 */     stringBuffer2.append("<div class=\"setting_button_row\" align=\"center\">");
/* 156 */     stringBuffer2.append("\n");
/*     */     
/* 158 */     stringBuffer2.append("<input type=button value=\"" + SystemEnv.getHtmlLabelName(86, user.getLanguage()) + "\"  class=\"zd_btn_submit\" onclick=\"onUseSetting('" + str3 + "','" + str2 + "')\">");
/*     */     
/* 160 */     stringBuffer2.append("\n");
/* 161 */     stringBuffer2.append("&nbsp;&nbsp;&nbsp;");
/* 162 */     stringBuffer2.append("\n");
/*     */     
/* 164 */     stringBuffer2.append("<input type=button value=\"" + SystemEnv.getHtmlLabelName(201, user.getLanguage()) + "\"  class=\"zd_btn_cancle\" onclick=\"onNoUseSetting('" + str3 + "','" + str2 + "')\">");
/*     */     
/* 166 */     stringBuffer2.append("\n");
/* 167 */     stringBuffer2.append("</div>");
/* 168 */     stringBuffer2.append("\n");
/* 169 */     stringBuffer2.append("</div>");
/* 170 */     stringBuffer2.append("\n");
/* 171 */     stringBuffer2.append("</div>");
/*     */     
/* 173 */     stringBuffer1.append(stringBuffer2.toString());
/*     */     
/* 175 */     return stringBuffer1.toString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getElementTabContent(HttpServletRequest paramHttpServletRequest) {
/* 189 */     StringBuffer stringBuffer = new StringBuffer();
/* 190 */     RecordSet recordSet = new RecordSet();
/* 191 */     User user = (User)paramHttpServletRequest.getSession(true).getAttribute("weaver_user@bean");
/* 192 */     String str1 = Util.null2String(paramHttpServletRequest.getParameter("eid"));
/* 193 */     String str2 = Util.null2String(paramHttpServletRequest.getParameter("ebaseid"));
/* 194 */     int i = Util.getIntValue(paramHttpServletRequest.getParameter("subCompanyId"), -1);
/* 195 */     String str3 = Util.null2String(paramHttpServletRequest.getParameter("type"));
/* 196 */     String str4 = Util.null2String(paramHttpServletRequest.getParameter("random"));
/*     */     
/* 198 */     String str5 = "";
/* 199 */     String str6 = "";
/* 200 */     String str7 = "5";
/* 201 */     String str8 = "";
/* 202 */     String str9 = "";
/* 203 */     String str10 = "";
/* 204 */     recordSet.executeSql("select hpid,isremind from hpelement where id=" + str1);
/* 205 */     if (recordSet.next()) {
/* 206 */       str5 = recordSet.getString("hpid");
/* 207 */       str6 = recordSet.getString("isremind");
/*     */     } 
/* 209 */     int j = 1;
/* 210 */     int k = 0;
/* 211 */     j = this.pu.getHpUserId(str5, "" + i, user);
/* 212 */     k = this.pu.getHpUserType(str5, "" + i, user);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 220 */     String str11 = "select perpage,linkmode,showfield,sharelevel from hpElementSettingDetail where eid=" + str1 + " and userid=" + j + " and usertype=" + k;
/* 221 */     recordSet.executeSql(str11);
/* 222 */     if (recordSet.next()) {
/* 223 */       str7 = Util.null2String(recordSet.getString("perpage"));
/* 224 */       str8 = Util.null2String(recordSet.getString("linkmode"));
/* 225 */       str9 = Util.null2String(recordSet.getString("showfield"));
/* 226 */       str10 = Util.null2String(recordSet.getString("sharelevel"));
/*     */     } 
/* 228 */     if ("content".equals(str3)) {
/* 229 */       String str = this.hpec.getTitle(str1);
/* 230 */       WeaverBaseElementCominfo weaverBaseElementCominfo = new WeaverBaseElementCominfo();
/* 231 */       stringBuffer.append("<table class='ViewForm' width='100%'>");
/* 232 */       stringBuffer.append("<colgroup>");
/* 233 */       stringBuffer.append("<col width='40%'/>");
/* 234 */       stringBuffer.append("<col width='60%'/>");
/* 235 */       stringBuffer.append("</colgroup>");
/* 236 */       stringBuffer.append("<input type='hidden' name='_esharelevel_" + str1 + "' value='" + str10 + "'>\t");
/* 237 */       if ("2".equals(str10)) {
/* 238 */         stringBuffer.append("<TR >");
/* 239 */         stringBuffer.append("<TD width='20%' class=''>&nbsp;");
/* 240 */         stringBuffer.append(SystemEnv.getHtmlLabelName(19491, user.getLanguage()));
/* 241 */         stringBuffer.append("</TD>");
/* 242 */         stringBuffer.append("<TD width='80%' class=field>");
/* 243 */         stringBuffer.append("\t<INPUT style='width:98%' TYPE='text' maxlength=50 alt='" + SystemEnv.getHtmlLabelName(20246, user.getLanguage()) + "50' id='_eTitel_" + str1 + "' value='" + str + "' class='inputStyle' defautvalue='" + str + "' onblur='checkMaxLength(this)'>");
/* 244 */         stringBuffer.append("</TD>");
/* 245 */         stringBuffer.append("</TR>");
/* 246 */         stringBuffer.append("<tr class='Spacing' style='height:1px' ><td class='Line1' colspan='2'></td></tr>");
/*     */       } 
/*     */       
/* 249 */       if (!str7.equals("-1")) {
/* 250 */         stringBuffer.append("<TR >");
/* 251 */         stringBuffer.append("<TD class='FieldTitle'>&nbsp;");
/* 252 */         stringBuffer.append(SystemEnv.getHtmlLabelName(19493, user.getLanguage()));
/* 253 */         stringBuffer.append("</TD>");
/* 254 */         stringBuffer.append("<TD  class=field><INPUT TYPE='text'  id='_ePerpage_" + str1 + "' value='" + str7 + "' maxlength=3 class='inputStyle' style='width:98%' onkeypress='ItemCount_KeyPress()' onpaste=\"return !clipboardData.getData('text').match(/\\D/)\" ondragenter='return false' style='ime-mode:Disabled'></TD>");
/* 255 */         stringBuffer.append("</TR>");
/* 256 */         stringBuffer.append("<tr class='Spacing' style='height:1px' ><td class='Line1' colspan='2'></td></tr>");
/*     */       } 
/* 258 */       if (!str8.equals("-1") && (str8.equals("1") || str8.equals("2"))) {
/* 259 */         stringBuffer.append("<TR >");
/* 260 */         stringBuffer.append("<TD class='FieldTitle'>&nbsp;");
/* 261 */         stringBuffer.append(SystemEnv.getHtmlLabelName(19494, user.getLanguage()));
/* 262 */         stringBuffer.append("</TD>");
/* 263 */         stringBuffer.append("<TD  class=field>");
/* 264 */         stringBuffer.append("<SELECT id='_eLinkmode_" + str1 + "' >");
/* 265 */         if (Util.getIntValue(str5) > 0) {
/*     */           
/* 267 */           stringBuffer.append("<option value='1' ");
/*     */           
/* 269 */           if ("1".equals(str8)) {
/* 270 */             stringBuffer.append(" 'selected' ");
/*     */           }
/* 272 */           stringBuffer.append(">" + SystemEnv.getHtmlLabelName(19497, user.getLanguage()));
/* 273 */           stringBuffer.append("</option>");
/*     */         } 
/* 275 */         stringBuffer.append("<option value='2'");
/* 276 */         if ("2".equals(str8))
/* 277 */           stringBuffer.append(" 'selected' "); 
/* 278 */         stringBuffer.append(">" + SystemEnv.getHtmlLabelName(19498, user.getLanguage()));
/* 279 */         stringBuffer.append("</option>");
/* 280 */         stringBuffer.append("</SELECT>");
/* 281 */         stringBuffer.append("</TD>");
/* 282 */         stringBuffer.append("</TR>\t");
/* 283 */         stringBuffer.append("<tr class='Spacing' style='height:1px' ><td class='Line1' colspan='2'></td></tr>");
/*     */       } 
/*     */       
/* 286 */       stringBuffer.append(showField(str2, str1, user, str9, str10, j, k));
/*     */ 
/*     */       
/* 289 */       if ("3".equals(this.wbec.getEtype(str2))) {
/* 290 */         stringBuffer.append(getCustomElementSetting(str2, str1, user));
/*     */       } else {
/* 292 */         stringBuffer.append(getWeaverElementSetting(str2, str1, str10, user, str4));
/*     */         
/* 294 */         stringBuffer.append("<TR >");
/* 295 */         stringBuffer.append("<TD class='FieldTitle'>&nbsp;");
/* 296 */         stringBuffer.append(SystemEnv.getHtmlLabelName(18713, user.getLanguage()));
/* 297 */         stringBuffer.append("</TD>");
/* 298 */         stringBuffer.append("<TD class=field>&nbsp;<input type='checkbox' name='isnew' value='isnew' " + ((str6.indexOf("isnew") == -1) ? "" : "checked") + "/>" + SystemEnv.getHtmlLabelName(501873, user.getLanguage()));
/* 299 */         stringBuffer.append("<input type='checkbox' name='isbold' value='isbold' " + ((str6.indexOf("isbold") == -1) ? "" : "checked") + "/>" + SystemEnv.getHtmlLabelName(16198, user.getLanguage()));
/* 300 */         stringBuffer.append("<input type='checkbox' name='islean' value='islean' " + ((str6.indexOf("islean") == -1) ? "" : "checked") + "/>" + SystemEnv.getHtmlLabelName(16199, user.getLanguage()));
/* 301 */         stringBuffer.append("<input type='checkbox' name='isrgb'  value='isrgb'  " + ((str6.indexOf("isrgb") == -1) ? "" : "checked") + "/>" + SystemEnv.getHtmlLabelName(495, user.getLanguage()));
/* 302 */         stringBuffer.append("<input class='color' name='newcolor' value='" + ((str6.indexOf("isrgb") == -1) ? "000" : str6.substring(str6.indexOf("isrgb") + 5)) + "'/>");
/* 303 */         stringBuffer.append("</TD>");
/* 304 */         stringBuffer.append("</TR>");
/* 305 */         stringBuffer.append("<tr class='Spacing' style='height:1px' ><td class='Line1' colspan='2'></td></tr>");
/*     */       } 
/* 307 */       stringBuffer.append("</table>");
/* 308 */     } else if ("style".equals(str3)) {
/* 309 */       stringBuffer.append("<table class='viewform' width='100%'>");
/* 310 */       stringBuffer.append("<colgroup>");
/* 311 */       stringBuffer.append("<col width='40%'/>");
/* 312 */       stringBuffer.append("<col width='60%'/>");
/* 313 */       stringBuffer.append("</colgroup>");
/* 314 */       stringBuffer.append("<tr>");
/* 315 */       stringBuffer.append("<td class='FieldTitle'>");
/* 316 */       stringBuffer.append(SystemEnv.getHtmlLabelName(22913, user.getLanguage()));
/* 317 */       stringBuffer.append("</td>");
/*     */ 
/*     */       
/* 320 */       stringBuffer.append("<td class='field'>");
/* 321 */       stringBuffer.append("<select name='eStyleid_" + str1 + "' id='eStyleid_" + str1 + "' ");
/*     */       
/* 323 */       if (!"2".equals(str10)) {
/* 324 */         stringBuffer.append(" 'disabled' ");
/*     */       }
/* 326 */       stringBuffer.append(" style='width:98%'>");
/*     */       
/* 328 */       this.esc.setTofirstRow();
/* 329 */       while (this.esc.next()) {
/* 330 */         String str12 = this.esc.getId();
/* 331 */         String str13 = this.esc.getTitle();
/* 332 */         if (str12.equals(this.hpec.getStyleid(str1))) {
/* 333 */           stringBuffer.append("<option value='" + str12 + "' selected>" + str13 + "</option>"); continue;
/*     */         } 
/* 335 */         stringBuffer.append("<option value='" + str12 + "'>" + str13 + "</option>");
/*     */       } 
/*     */ 
/*     */ 
/*     */       
/* 340 */       stringBuffer.append("</select>");
/* 341 */       stringBuffer.append("</td>");
/* 342 */       stringBuffer.append("</tr>");
/* 343 */       stringBuffer.append("<tr class='Spacing' style='height:1px' ><td class='Line1' colspan='2'></td></tr>");
/* 344 */       stringBuffer.append("<tr>");
/* 345 */       stringBuffer.append("<td class='FieldTitle'>");
/* 346 */       stringBuffer.append(SystemEnv.getHtmlLabelName(19492, user.getLanguage()));
/* 347 */       stringBuffer.append("</td>");
/* 348 */       stringBuffer.append("<td class='field'><input name='eLogo_" + str1 + "' onchanage='setElementLogo('" + str1 + "',this.value)' id='eLogo_" + str1 + "' ");
/*     */       
/* 350 */       if (!"2".equals(str10)) {
/* 351 */         stringBuffer.append(" 'disabled' ");
/*     */       }
/* 353 */       stringBuffer.append("class='filetree' type='text' value='" + this.hpec.getLogo(str1) + "'>");
/*     */       
/* 355 */       stringBuffer.append("</td>");
/* 356 */       stringBuffer.append("</tr>");
/* 357 */       stringBuffer.append("<tr class='Spacing' style='height:1px' ><td class='Line1' colspan='2'></td></tr>");
/* 358 */       stringBuffer.append("<tr>");
/* 359 */       stringBuffer.append("<td class='FieldTitle'>");
/* 360 */       stringBuffer.append(SystemEnv.getHtmlLabelName(207, user.getLanguage()));
/* 361 */       stringBuffer.append("</td>");
/* 362 */       stringBuffer.append("<td class='field'>");
/*     */       
/* 364 */       int m = Util.getIntValue(this.hpec.getHeight(str1), 0);
/*     */       
/* 366 */       stringBuffer.append("<input  name='eHeight_" + str1 + "' class='inputstyle' onchange='setElementHeight(" + str1 + ",this.value)' onkeypress='ItemCount_KeyPress()'  style='width:98%' id='eHeight_" + str1 + "' ");
/*     */       
/* 368 */       if (!"2".equals(str10)) {
/* 369 */         stringBuffer.append(" 'disabled' ");
/*     */       }
/* 371 */       stringBuffer.append("type='text' value='" + m + "'><br>");
/* 372 */       stringBuffer.append("0:");
/* 373 */       stringBuffer.append(SystemEnv.getHtmlLabelName(22494, user.getLanguage()));
/* 374 */       stringBuffer.append("</td>");
/* 375 */       stringBuffer.append("</tr>");
/* 376 */       stringBuffer.append("<tr class='Spacing' style='height:1px' ><td class='Line1' colspan='2'></td></tr>");
/* 377 */       stringBuffer.append("<tr>");
/* 378 */       stringBuffer.append("<td class='FieldTitle'>");
/* 379 */       stringBuffer.append(SystemEnv.getHtmlLabelName(15932, user.getLanguage()));
/* 380 */       stringBuffer.append("</td>");
/* 381 */       stringBuffer.append("<td class='field'>");
/*     */       
/* 383 */       stringBuffer.append(SystemEnv.getHtmlLabelName(22952, user.getLanguage()));
/* 384 */       stringBuffer.append(":<input  onchange=\"setElementMarginTop('" + str1 + "',this.value)\" name='eMarginTop_" + str1 + "' class='inputstyle' style='width:20px' onkeypress='ItemCount_KeyPress()' id='eMarginTop_" + str1 + "'");
/*     */       
/* 386 */       if (!"2".equals(str10))
/* 387 */         stringBuffer.append(" 'disabled' "); 
/* 388 */       stringBuffer.append("type='text' value='" + this.hpec.getMarginTop(str1) + "'>");
/*     */       
/* 390 */       stringBuffer.append(SystemEnv.getHtmlLabelName(22955, user.getLanguage()));
/* 391 */       stringBuffer.append(":<input  onchange=\"setElementMarginBottom('" + str1 + "',this.value)\" name='eMarginBottom_" + str1 + "' class='inputstyle' style='width:20px' onkeypress='ItemCount_KeyPress()' id='eMarginBottom_" + str1 + "'");
/* 392 */       if (!"2".equals(str10))
/* 393 */         stringBuffer.append(" 'disabled' "); 
/* 394 */       stringBuffer.append("type='text' value='" + this.hpec.getMarginBottom(str1) + "'>");
/*     */       
/* 396 */       stringBuffer.append(SystemEnv.getHtmlLabelName(22953, user.getLanguage()));
/* 397 */       stringBuffer.append(":<input  onchange=\"setElementMarginLeft('" + str1 + "',this.value)\" name=eMarginLeft_" + str1 + " class=inputstyle style='width:20px' onkeypress='ItemCount_KeyPress()' id='eMarginLeft_" + str1 + "'");
/* 398 */       if (!"2".equals(str10))
/* 399 */         stringBuffer.append(" 'disabled' "); 
/* 400 */       stringBuffer.append("type='text' value='" + this.hpec.getMarginLeft(str1) + "'>");
/*     */       
/* 402 */       stringBuffer.append(SystemEnv.getHtmlLabelName(22954, user.getLanguage()));
/* 403 */       stringBuffer.append(":<input  onchange=\"setElementMarginRight('" + str1 + "',this.value)\" name=eMarginRight_" + str1 + " class=inputstyle style='width:20px' onkeypress='ItemCount_KeyPress()' id='eMarginRight_" + str1 + "' ");
/* 404 */       if (!"2".equals(str10))
/* 405 */         stringBuffer.append(" 'disabled' "); 
/* 406 */       stringBuffer.append("type='text' value='" + this.hpec.getMarginRight(str1) + "'>");
/*     */       
/* 408 */       stringBuffer.append("</td>");
/* 409 */       stringBuffer.append("</tr>");
/* 410 */       stringBuffer.append("<tr class='Spacing' style='height:1px' ><td class='Line1' colspan='2'></td></tr>");
/*     */       
/* 412 */       stringBuffer.append("</table>");
/* 413 */     } else if ("share".equals(str3)) {
/* 414 */       if ("2".equals(str10)) {
/* 415 */         stringBuffer.append("<iframe id='eShareIframe_" + str1 + "' name='eShareIframe_" + str1 + "' src='/page/element/ElementShare.jsp?esharelevel=" + str10 + "&eid=" + str1 + "' width=100% frameborder='0' marginheight='0' marginwidth='0'></iframe>");
/*     */       } else {
/* 417 */         stringBuffer.append("<iframe id='eShareIframe_" + str1 + "' name='eShareIframe_" + str1 + "' src='/page/element/noright.jsp' width=100% frameborder='0' marginheight='0' marginwidth='0'></iframe>");
/*     */       } 
/*     */     } 
/*     */     
/* 421 */     return stringBuffer.toString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String showField(String paramString1, String paramString2, User paramUser, String paramString3, String paramString4, int paramInt1, int paramInt2) {
/* 436 */     RecordSet recordSet1 = new RecordSet();
/* 437 */     recordSet1.executeSql("select count(elementid) from hpFieldElement where elementid='" + paramString1 + "'");
/* 438 */     recordSet1.next();
/* 439 */     if (recordSet1.getInt(1) == 0) return "";
/*     */     
/* 441 */     StringBuffer stringBuffer = new StringBuffer();
/* 442 */     RecordSet recordSet2 = new RecordSet();
/* 443 */     ArrayList arrayList = Util.TokenizerString(paramString3, ",");
/* 444 */     recordSet1.executeSql("select id,isLimitLength,fieldname,fieldcolumn,fieldwidth from hpFieldElement where elementid='" + paramString1 + "' order by ordernum");
/* 445 */     stringBuffer.append("<TR >");
/* 446 */     stringBuffer.append("<TD class='FieldTitle'>&nbsp;");
/* 447 */     stringBuffer.append(SystemEnv.getHtmlLabelName(19495, paramUser.getLanguage()));
/* 448 */     stringBuffer.append("</TD>");
/* 449 */     stringBuffer.append("<TD  class=field>");
/* 450 */     while (recordSet1.next()) {
/* 451 */       String str1 = Util.null2String(recordSet1.getString("id"));
/* 452 */       String str2 = Util.null2String(recordSet1.getString("isLimitLength"));
/* 453 */       int i = Util.getIntValue(recordSet1.getString("fieldname"));
/* 454 */       String str3 = Util.null2String(recordSet1.getString("fieldcolumn"));
/* 455 */       String str4 = Util.null2String(recordSet1.getString("fieldwidth"));
/*     */       
/* 457 */       String str5 = "";
/* 458 */       if (arrayList.contains(str1)) str5 = " checked "; 
/* 459 */       stringBuffer.append("&nbsp;<INPUT TYPE=checkbox NAME='_chkField_" + paramString2 + "' value=" + str1 + " " + str5 + ">");
/* 460 */       if (i == -1) {
/* 461 */         stringBuffer.append(recordSet1.getString("fieldname"));
/*     */       } else {
/* 463 */         stringBuffer.append(SystemEnv.getHtmlLabelName(i, paramUser.getLanguage()));
/*     */       } 
/* 465 */       String str6 = "none";
/* 466 */       if ("2".equals(paramString4)) {
/* 467 */         str6 = "";
/*     */       }
/* 469 */       if (str3.toLowerCase().equals("img")) {
/* 470 */         String str7 = "";
/* 471 */         String str8 = "";
/* 472 */         String str9 = "";
/*     */         
/* 474 */         boolean bool = false;
/* 475 */         String str10 = "";
/* 476 */         recordSet2.executeSql("select imgsize from hpFieldLength where eid=" + paramString2 + " and efieldid=" + str1 + " and userid=" + paramInt1 + " and usertype=" + paramInt2);
/* 477 */         if (recordSet2.next()) str7 = Util.null2String(recordSet2.getString("imgsize")); 
/* 478 */         if (!str7.equals("")) {
/* 479 */           ArrayList<String> arrayList1 = Util.TokenizerString(str7, "*");
/* 480 */           str8 = arrayList1.get(0);
/* 481 */           if (arrayList1.size() > 1) {
/* 482 */             str9 = arrayList1.get(1);
/*     */           }
/* 484 */           if (str9.equals("0") || str9.equals("")) {
/* 485 */             bool = true;
/* 486 */             str10 = "checked";
/*     */           } 
/*     */         } else {
/* 489 */           str8 = "120";
/* 490 */           str9 = "108";
/*     */         } 
/* 492 */         if ("2".equals(paramString4)) {
/* 493 */           stringBuffer.append("&nbsp;" + SystemEnv.getHtmlLabelName(203, paramUser.getLanguage()) + ":<INPUT TYPE = text name='_imgWidth" + paramString2 + "' value='" + str8 + "' basefield=" + str1 + " class='inputStyle'  style='display:" + str6 + ";width:35px' onkeypress=\"ItemCount_KeyPress()\" onpaste=\"return !clipboardData.getData('text').match(/\\D/)\" ondragenter=\"return false\" style=\"ime-mode:Disabled\">");
/*     */         } else {
/* 495 */           stringBuffer.append("&nbsp;<INPUT TYPE = text name='_imgWidth" + paramString2 + "' value='" + str8 + "' basefield=" + str1 + " class='inputStyle'  style='display:" + str6 + ";width:35px' onkeypress=\"ItemCount_KeyPress()\" onpaste=\"return !clipboardData.getData('text').match(/\\D/)\" ondragenter=\"return false\" style=\"ime-mode:Disabled\">");
/*     */         } 
/* 497 */         if (!bool) {
/* 498 */           stringBuffer.append("<span id='_imgHeightDiv" + paramString2 + "' style='display:" + str6 + ";width:70px'>&nbsp;" + SystemEnv.getHtmlLabelName(207, paramUser.getLanguage()) + ":<INPUT TYPE = text name='_imgHeight" + paramString2 + "' value='" + str9 + "' basefield=" + str1 + " class='inputStyle'  style='width:35px' onkeypress=\"ItemCount_KeyPress()\" onpaste=\"return !clipboardData.getData('text').match(/\\D/)\" ondragenter=\"return false\" style=\"ime-mode:Disabled\" ></span>");
/*     */         } else {
/* 500 */           stringBuffer.append("<span id='_imgHeightDiv" + paramString2 + "' style='width:70px;display:none' >&nbsp;" + SystemEnv.getHtmlLabelName(207, paramUser.getLanguage()) + ":<INPUT TYPE = text name='_imgHeight" + paramString2 + "' value='" + str9 + "' basefield=" + str1 + " class='inputStyle'  style='width:35px;' onkeypress=\"ItemCount_KeyPress()\" onpaste=\"return !clipboardData.getData('text').match(/\\D/)\" ondragenter=\"return false\" style=\"ime-mode:Disabled\" ></span>");
/*     */         } 
/* 502 */         if ("2".equals(paramString4)) {
/* 503 */           stringBuffer.append("<INPUT style='' TYPE = checkbox name='_imgAutoAdjust" + paramString2 + "' " + str10 + " onclick='if(event.srcElement.checked){document.getElementById(\"_imgHeightDiv" + paramString2 + "\").style.display=\"none\";document.getElementById(\"_imgHeight" + paramString2 + "\").value=0}else{document.getElementById(\"_imgHeightDiv" + paramString2 + "\").style.display=\"\"}'>&nbsp;" + SystemEnv.getHtmlLabelName(22494, paramUser.getLanguage()) + "");
/*     */         } else {
/*     */           
/* 506 */           stringBuffer.append("<INPUT style='display:" + str6 + "' TYPE = checkbox name='_imgAutoAdjust" + paramString2 + "' " + str10 + " onclick='if(event.srcElement.checked){document.getElementById(\"_imgHeightDiv" + paramString2 + "\").style.display=\"none\";document.getElementById(\"_imgHeight" + paramString2 + "\").value=0}else{document.getElementById(\"_imgHeightDiv" + paramString2 + "\").style.display=\"\"}'>");
/*     */         } 
/*     */       } 
/*     */       
/* 510 */       int j = 8;
/* 511 */       if ("1".equals(str2)) {
/* 512 */         recordSet2.executeSql("select charnum from hpFieldLength where eid=" + paramString2 + " and efieldid=" + str1 + " and userid=" + paramInt1 + " and usertype=" + paramInt2);
/* 513 */         if (recordSet2.next()) j = Util.getIntValue(recordSet2.getString("charnum")); 
/* 514 */         if ("2".equals(paramString4)) {
/* 515 */           stringBuffer.append("&nbsp;" + SystemEnv.getHtmlLabelName(19524, paramUser.getLanguage()) + ":<input name=_wordcount_" + paramString2 + " basefield=" + str1 + " type='text' style='width:24px' class=inputstyle title=" + SystemEnv.getHtmlLabelName(19524, paramUser.getLanguage()) + " value='" + j + "' onkeypress=\"ItemCount_KeyPress()\" onpaste=\"return !clipboardData.getData('text').match(/\\D/)\" ondragenter=\"return false\" style=\"ime-mode:Disabled\" >&nbsp;");
/*     */         } else {
/* 517 */           stringBuffer.append("&nbsp;<input name=_wordcount_" + paramString2 + " basefield=" + str1 + " type='text' style='width:24px' class=inputstyle title=" + SystemEnv.getHtmlLabelName(19524, paramUser.getLanguage()) + " value='" + j + "' onkeypress=\"ItemCount_KeyPress()\" onpaste=\"return !clipboardData.getData('text').match(/\\D/)\" ondragenter=\"return false\" style=\"ime-mode:Disabled\">&nbsp;");
/*     */         } 
/*     */       } 
/* 520 */       stringBuffer.append("<br>");
/*     */     } 
/* 522 */     stringBuffer.append("</TD>");
/* 523 */     stringBuffer.append("</TR>");
/* 524 */     stringBuffer.append("<tr class='Spacing' style='height:1px' ><td class='Line1' colspan='2'></td></tr>");
/* 525 */     return stringBuffer.toString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String getCustomElementSetting(String paramString1, String paramString2, User paramUser) {
/* 536 */     RecordSet recordSet = new RecordSet();
/* 537 */     StringBuffer stringBuffer = new StringBuffer();
/* 538 */     ArrayList<Map> arrayList = (ArrayList)this.ecc.getSettingList(paramString1);
/* 539 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 540 */     String str = "select name,value from hpElementSetting  where eid ='" + paramString2 + "'";
/* 541 */     recordSet.executeSql(str);
/* 542 */     while (recordSet.next()) {
/* 543 */       hashMap.put(recordSet.getString("name"), recordSet.getString("value"));
/*     */     }
/* 545 */     for (byte b = 0; b < arrayList.size(); b++) {
/* 546 */       Map map = arrayList.get(b);
/*     */       
/* 548 */       String str1 = (String)map.get("settingtitle");
/* 549 */       String str2 = (String)map.get("settingtype");
/* 550 */       String str3 = (String)map.get("settingname");
/* 551 */       String str4 = (String)map.get("settingdatatype");
/* 552 */       String str5 = (String)map.get("settingdatalength");
/*     */       
/* 554 */       if ("DataSource".equals(str2)) {
/* 555 */         String[] arrayOfString = Util.TokenizerStringNew((String)hashMap.get(str3), "^,^");
/* 556 */         String str6 = "";
/* 557 */         String str7 = "";
/* 558 */         if (arrayOfString.length == 2)
/* 559 */         { str6 = arrayOfString[0];
/* 560 */           str7 = arrayOfString[1]; }
/* 561 */         else if (arrayOfString.length == 1) { str7 = arrayOfString[0]; }
/* 562 */          stringBuffer.append("<TR ><TD class='FieldTitle'>&nbsp;");
/* 563 */         stringBuffer.append(SystemEnv.getHtmlLabelName(28006, paramUser.getLanguage()));
/* 564 */         stringBuffer.append("</TD>");
/* 565 */         stringBuffer.append("<TD class=field>&nbsp;");
/* 566 */         stringBuffer.append("<select id=_whereKey_" + paramString2 + " name=_whereKey_" + paramString2 + ">");
/* 567 */         DataSourceXML dataSourceXML = new DataSourceXML();
/* 568 */         ArrayList<String> arrayList1 = dataSourceXML.getPointArrayList();
/* 569 */         stringBuffer.append("<option value='' " + ("".equals(str6) ? "selected" : "") + ">");
/* 570 */         stringBuffer.append(SystemEnv.getHtmlLabelName(23999, paramUser.getLanguage()));
/* 571 */         stringBuffer.append("</option>");
/* 572 */         for (byte b1 = 0; b1 < arrayList1.size(); b1++) {
/* 573 */           String str8 = arrayList1.get(b1);
/* 574 */           String str9 = str8.equals(str6) ? "selected" : "";
/* 575 */           stringBuffer.append("<option value='" + str8 + "' " + str9 + ">");
/* 576 */           stringBuffer.append(str8);
/* 577 */           stringBuffer.append("</option>");
/*     */         } 
/* 579 */         stringBuffer.append("</select></TD></TR>");
/* 580 */         stringBuffer.append("<tr class='Spacing' style='height:1px' ><td class='Line1' colspan='2'></td></tr>");
/* 581 */         if (str7.contains("#")) {
/* 582 */           str7 = Util.StringReplace(str7, "#", "'");
/*     */         }
/* 584 */         stringBuffer.append("<TR ><TD class='FieldTitle'>&nbsp;");
/* 585 */         stringBuffer.append(SystemEnv.getHtmlLabelName(32311, paramUser.getLanguage()));
/* 586 */         stringBuffer.append("</TD>");
/* 587 */         stringBuffer.append("<TD class=field>&nbsp;");
/* 588 */         stringBuffer.append("<textarea name=_whereKey_" + paramString2 + "   style=\"height:70px;width:98%\" class=inputStyle  onblur=\"checkSql(this)\">");
/* 589 */         stringBuffer.append(str7);
/* 590 */         stringBuffer.append("</textarea></TD></TR>");
/* 591 */         stringBuffer.append("<tr class='Spacing' style='height:1px' ><td class='Line1' colspan='2'></td></tr>");
/* 592 */       } else if ("DataPage".equals(str2)) {
/* 593 */         String str6 = (String)hashMap.get(str3);
/* 594 */         stringBuffer.append("<TR ><TD class='FieldTitle'>&nbsp;");
/* 595 */         stringBuffer.append(str1);
/* 596 */         stringBuffer.append("</TD>");
/* 597 */         stringBuffer.append("<TD class=field>&nbsp;<input type='text' name='" + str3 + "_" + paramString2 + "' value=\"" + ((str6 == null) ? "" : str6) + "\"/></TD></TR>");
/* 598 */         stringBuffer.append("<tr class='Spacing' style='height:1px' ><td class='Line1' colspan='2'></td></tr>");
/*     */       } 
/*     */     } 
/* 601 */     return stringBuffer.toString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String getWeaverElementSetting(String paramString1, String paramString2, String paramString3, User paramUser, String paramString4) {
/* 614 */     StringBuffer stringBuffer = new StringBuffer();
/* 615 */     if ("2".equals(paramString3)) {
/* 616 */       String str1 = this.hpec.getStrsqlwhere(paramString2);
/* 617 */       String str2 = Util.null2String(this.ebc.getSettingMethod(paramString1));
/*     */       
/* 619 */       String str3 = "";
/* 620 */       if (!"".equals(str2) && 
/* 621 */         !"".equals(str2)) {
/*     */         
/*     */         try {
/* 624 */           Class<?> clazz = Class.forName("weaver.page.element.compatible.PageWhere");
/*     */           
/* 626 */           if (paramString1.equals("7") || paramString1.equals("8") || paramString1.equals("1") || paramString1.equals("29")) {
/* 627 */             Method method = clazz.getMethod(str2, new Class[] { String.class, String.class, String.class, String.class, String.class, String.class });
/* 628 */             Constructor<?> constructor = clazz.getConstructor(null);
/*     */             
/* 630 */             str3 = (String)method.invoke(constructor.newInstance(null), new Object[] { paramString2, paramString1, str1, "" + paramUser.getLanguage(), paramString3, paramString4 });
/*     */           } else {
/* 632 */             Method method = clazz.getMethod(str2, new Class[] { String.class, String.class, String.class, String.class, String.class });
/* 633 */             Constructor<?> constructor = clazz.getConstructor(null);
/*     */             
/* 635 */             str3 = (String)method.invoke(constructor.newInstance(null), new Object[] { paramString2, paramString1, str1, "" + paramUser.getLanguage(), paramString3 });
/*     */           } 
/* 637 */         } catch (Exception exception) {
/*     */           
/* 639 */           writeLog("weaver.page.element.compatible.PageWhere." + str2 + " NoMenthod Exception!");
/* 640 */           exception.printStackTrace();
/*     */         } 
/* 642 */         stringBuffer.append("\n" + str3 + "\n");
/*     */       } 
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 649 */     return stringBuffer.toString();
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/admincenter/homepage/WeaverPortal.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */