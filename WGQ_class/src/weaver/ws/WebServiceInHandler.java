/*    */ package weaver.ws;
/*    */ 
/*    */ import javax.servlet.http.HttpServletRequest;
/*    */ import org.codehaus.xfire.MessageContext;
/*    */ import org.codehaus.xfire.handler.AbstractHandler;
/*    */ import org.codehaus.xfire.transport.http.XFireServletController;
/*    */ import org.dom4j.Document;
/*    */ import org.dom4j.io.DOMReader;
/*    */ import org.w3c.dom.Document;
/*    */ import weaver.general.Util;
/*    */ import weaver.integration.logging.Logger;
/*    */ import weaver.integration.logging.LoggerFactory;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class WebServiceInHandler
/*    */   extends AbstractHandler
/*    */ {
/* 20 */   private static Logger log = LoggerFactory.getLogger("ofs", WebServiceInHandler.class.getName());
/*    */   
/*    */   public void invoke(MessageContext paramMessageContext) throws Exception {
/* 23 */     Document document = (Document)paramMessageContext.getInMessage().getProperty("dom.message");
/* 24 */     paramMessageContext.getInMessage().setProperty("dom.message", document);
/* 25 */     if (document != null) {
/*    */       
/* 27 */       log.info("client ip: " + getClientIpXfire());
/* 28 */       log.info("------------input Soap xml-------------");
/* 29 */       log.info(buildDocment(document).asXML());
/*    */     } 
/*    */   }
/*    */ 
/*    */   
/*    */   private Document buildDocment(Document paramDocument) {
/* 35 */     DOMReader dOMReader = new DOMReader();
/* 36 */     return dOMReader.read(paramDocument);
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   private String getClientIpXfire() {
/* 46 */     String str = "";
/*    */     try {
/* 48 */       HttpServletRequest httpServletRequest = XFireServletController.getRequest();
/* 49 */       str = Util.getIpAddr(httpServletRequest);
/* 50 */       log.info("getClientIpXfire()=" + str);
/* 51 */       return str;
/* 52 */     } catch (Exception exception) {
/* 53 */       exception.printStackTrace();
/* 54 */       return "";
/*    */     } 
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/ws/WebServiceInHandler.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */