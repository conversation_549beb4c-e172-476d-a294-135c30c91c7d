/*    */ package weaver.ws;
/*    */ 
/*    */ import org.codehaus.xfire.MessageContext;
/*    */ import org.codehaus.xfire.handler.AbstractHandler;
/*    */ import org.dom4j.Document;
/*    */ import org.dom4j.io.DOMReader;
/*    */ import org.w3c.dom.Document;
/*    */ import weaver.integration.logging.Logger;
/*    */ import weaver.integration.logging.LoggerFactory;
/*    */ 
/*    */ 
/*    */ 
/*    */ public class WebServiceOutHandler
/*    */   extends AbstractHandler
/*    */ {
/* 16 */   private static Logger log = LoggerFactory.getLogger("ofs", WebServiceOutHandler.class.getName());
/*    */ 
/*    */   
/*    */   public void invoke(MessageContext paramMessageContext) throws Exception {
/* 20 */     Document document = (Document)paramMessageContext.getOutMessage().getProperty("dom.message");
/* 21 */     if (document != null) {
/* 22 */       log.info("------------output Soap xml-------------");
/* 23 */       log.info(buildDocment(document).asXML());
/*    */     } 
/*    */   }
/*    */   
/*    */   private Document buildDocment(Document paramDocument) {
/* 28 */     DOMReader dOMReader = new DOMReader();
/* 29 */     return dOMReader.read(paramDocument);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/ws/WebServiceOutHandler.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */