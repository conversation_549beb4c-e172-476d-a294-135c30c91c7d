/*     */ package weaver.dwr;
/*     */ 
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import uk.ltd.getahead.dwr.ExecutionContext;
/*     */ import weaver.conn.RecordSet;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class Validator
/*     */ {
/*     */   public boolean checkInt(String paramString) {
/*  15 */     ExecutionContext executionContext = ExecutionContext.get();
/*  16 */     HttpServletRequest httpServletRequest = executionContext.getHttpServletRequest();
/*  17 */     if (httpServletRequest.getSession(true).getAttribute("weaver_user@bean") == null)
/*  18 */       return true; 
/*     */     try {
/*  20 */       Integer.parseInt(paramString);
/*  21 */       return true;
/*  22 */     } catch (NumberFormatException numberFormatException) {
/*  23 */       return false;
/*     */     } 
/*     */   }
/*     */   
/*     */   public boolean subCompanyIsUsed(String paramString) {
/*  28 */     ExecutionContext executionContext = ExecutionContext.get();
/*  29 */     HttpServletRequest httpServletRequest = executionContext.getHttpServletRequest();
/*  30 */     if (httpServletRequest.getSession(true).getAttribute("weaver_user@bean") == null)
/*  31 */       return true; 
/*  32 */     String str = "select count(*) from hrmroles where subcompanyid= ?";
/*  33 */     RecordSet recordSet = new RecordSet();
/*  34 */     recordSet.executeQuery(str, new Object[] { paramString });
/*  35 */     recordSet.next();
/*  36 */     if (recordSet.getInt(1) > 0) {
/*  37 */       return true;
/*     */     }
/*  39 */     return false;
/*     */   }
/*     */   
/*     */   public boolean departmentIsUsed(String paramString) {
/*  43 */     ExecutionContext executionContext = ExecutionContext.get();
/*  44 */     HttpServletRequest httpServletRequest = executionContext.getHttpServletRequest();
/*  45 */     if (httpServletRequest.getSession(true).getAttribute("weaver_user@bean") == null)
/*  46 */       return true; 
/*  47 */     String str = "select count(*) from hrmresource where status in (0,1,2,3) and departmentid= ?";
/*  48 */     RecordSet recordSet = new RecordSet();
/*  49 */     recordSet.executeQuery(str, new Object[] { paramString });
/*  50 */     recordSet.next();
/*  51 */     if (recordSet.getInt(1) > 0) {
/*  52 */       return true;
/*     */     }
/*  54 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean checkHasChildDepartment(String paramString) {
/*  63 */     ExecutionContext executionContext = ExecutionContext.get();
/*  64 */     HttpServletRequest httpServletRequest = executionContext.getHttpServletRequest();
/*  65 */     if (httpServletRequest.getSession(true).getAttribute("weaver_user@bean") == null)
/*  66 */       return true; 
/*  67 */     String str = " select count(id) from hrmdepartment where (canceled = '0' or canceled is null) and id in (select id from hrmdepartment where supdepid = ?)";
/*  68 */     RecordSet recordSet = new RecordSet();
/*  69 */     recordSet.executeQuery(str, new Object[] { paramString });
/*  70 */     recordSet.next();
/*  71 */     if (recordSet.getInt(1) > 0) {
/*  72 */       return true;
/*     */     }
/*  74 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean checkHasDepartment(String paramString) {
/*  83 */     ExecutionContext executionContext = ExecutionContext.get();
/*  84 */     HttpServletRequest httpServletRequest = executionContext.getHttpServletRequest();
/*  85 */     if (httpServletRequest.getSession(true).getAttribute("weaver_user@bean") == null)
/*  86 */       return true; 
/*  87 */     String str = "select count(*) from HrmDepartment where subcompanyid1= ?";
/*  88 */     RecordSet recordSet = new RecordSet();
/*  89 */     recordSet.executeQuery(str, new Object[] { paramString });
/*  90 */     recordSet.next();
/*  91 */     if (recordSet.getInt(1) > 0) {
/*  92 */       return true;
/*     */     }
/*  94 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean checkHasJob(String paramString) {
/* 103 */     ExecutionContext executionContext = ExecutionContext.get();
/* 104 */     HttpServletRequest httpServletRequest = executionContext.getHttpServletRequest();
/* 105 */     if (httpServletRequest.getSession(true).getAttribute("weaver_user@bean") == null)
/* 106 */       return true; 
/* 107 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String checkHasDepartmentAndCompany(String paramString) {
/* 124 */     ExecutionContext executionContext = ExecutionContext.get();
/* 125 */     HttpServletRequest httpServletRequest = executionContext.getHttpServletRequest();
/* 126 */     if (httpServletRequest.getSession(true).getAttribute("weaver_user@bean") == null)
/* 127 */       return "1"; 
/* 128 */     String str = "select count(*) from HrmDepartment where subcompanyid1= ?";
/* 129 */     RecordSet recordSet = new RecordSet();
/* 130 */     recordSet.executeQuery(str, new Object[] { paramString });
/* 131 */     recordSet.next();
/* 132 */     if (recordSet.getInt(1) > 0) {
/* 133 */       return "2";
/*     */     }
/* 135 */     str = "select count(*) from HrmSubCompany where supsubcomid = ?";
/* 136 */     recordSet.executeQuery(str, new Object[] { paramString });
/* 137 */     recordSet.next();
/* 138 */     if (recordSet.getInt(1) > 0) {
/* 139 */       return "3";
/*     */     }
/* 141 */     return "0";
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/dwr/Validator.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */