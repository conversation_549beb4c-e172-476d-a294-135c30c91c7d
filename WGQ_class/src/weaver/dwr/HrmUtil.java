/*    */ package weaver.dwr;
/*    */ 
/*    */ import javax.servlet.http.HttpServletRequest;
/*    */ import uk.ltd.getahead.dwr.ExecutionContext;
/*    */ import weaver.hrm.city.CityComInfo;
/*    */ import weaver.hrm.country.CountryComInfo;
/*    */ import weaver.hrm.province.ProvinceComInfo;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class HrmUtil
/*    */ {
/*    */   public String getCountryByCity(int paramInt) {
/* 17 */     ExecutionContext executionContext = ExecutionContext.get();
/* 18 */     HttpServletRequest httpServletRequest = executionContext.getHttpServletRequest();
/* 19 */     if (httpServletRequest.getSession(true).getAttribute("weaver_user@bean") == null)
/* 20 */       return ""; 
/*    */     try {
/* 22 */       CityComInfo cityComInfo = new CityComInfo();
/* 23 */       ProvinceComInfo provinceComInfo = new ProvinceComInfo();
/* 24 */       CountryComInfo countryComInfo = new CountryComInfo();
/* 25 */       String str1 = cityComInfo.getCityprovinceid("" + paramInt);
/* 26 */       String str2 = provinceComInfo.getProvincecountryid(str1);
/* 27 */       String str3 = countryComInfo.getCountrydesc(str2);
/* 28 */       return str3 + "_" + str2;
/* 29 */     } catch (Exception exception) {
/* 30 */       return "_";
/*    */     } 
/*    */   }
/*    */   public boolean checkCity(String paramString1, String paramString2) {
/* 34 */     ExecutionContext executionContext = ExecutionContext.get();
/* 35 */     HttpServletRequest httpServletRequest = executionContext.getHttpServletRequest();
/* 36 */     if (httpServletRequest.getSession(true).getAttribute("weaver_user@bean") == null)
/* 37 */       return true; 
/*    */     try {
/* 39 */       CityComInfo cityComInfo = new CityComInfo();
/* 40 */       ProvinceComInfo provinceComInfo = new ProvinceComInfo();
/* 41 */       CountryComInfo countryComInfo = new CountryComInfo();
/* 42 */       String str1 = cityComInfo.getCityprovinceid(paramString1);
/* 43 */       String str2 = provinceComInfo.getProvincecountryid(str1);
/* 44 */       if (str2.equals(paramString2)) {
/* 45 */         return true;
/*    */       }
/* 47 */       return false;
/* 48 */     } catch (Exception exception) {
/* 49 */       return true;
/*    */     } 
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/dwr/HrmUtil.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */