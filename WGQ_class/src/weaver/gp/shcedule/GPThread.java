/*    */ package weaver.gp.shcedule;
/*    */ 
/*    */ import weaver.gp.util.GPThreadManager;
/*    */ import weaver.interfaces.schedule.BaseCronJob;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class GPThread
/*    */   extends BaseCronJob
/*    */ {
/*    */   public void execute() {
/* 13 */     GPThreadManager gPThreadManager = new GPThreadManager(-1);
/* 14 */     gPThreadManager.doThreadWork();
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/gp/shcedule/GPThread.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */