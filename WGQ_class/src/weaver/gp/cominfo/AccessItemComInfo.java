/*     */ package weaver.gp.cominfo;
/*     */ 
/*     */ import weaver.cache.CacheBase;
/*     */ import weaver.cache.CacheColumn;
/*     */ import weaver.cache.CacheColumnType;
/*     */ import weaver.cache.PKColumn;
/*     */ 
/*     */ 
/*     */ public class AccessItemComInfo
/*     */   extends CacheBase
/*     */ {
/*  12 */   protected static String TABLE_NAME = "GP_AccessItem";
/*     */   
/*  14 */   protected static String tableWhere = null;
/*     */   
/*  16 */   protected static String tableOrder = null;
/*     */   @PKColumn(type = CacheColumnType.NUMBER)
/*  18 */   protected static String PK_NAME = "id";
/*     */   
/*     */   @CacheColumn(name = "itemname")
/*     */   protected static int name;
/*     */   
/*     */   @CacheColumn(name = "itemdesc")
/*     */   protected static int desc;
/*     */   
/*     */   @CacheColumn(name = "itemtype")
/*     */   protected static int type;
/*     */   
/*     */   @CacheColumn(name = "itemunit")
/*     */   protected static int unit;
/*     */   
/*     */   @CacheColumn(name = "isvalid")
/*     */   protected static int isvalid;
/*     */   
/*     */   @CacheColumn(name = "formula")
/*     */   protected static int formula;
/*     */   @CacheColumn(name = "formuladetail")
/*     */   protected static int formuladetail;
/*  39 */   private static Object syncObject = new Object();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getNum() {
/*  50 */     return size();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getId() {
/*  58 */     return (String)getRowValue(0);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getName() {
/*  67 */     return (String)getRowValue(name);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getName(String paramString) {
/*  78 */     return getValue(name, paramString).toString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getDesc() {
/*  87 */     return (String)getRowValue(desc);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getDesc(String paramString) {
/*  98 */     return getValue(desc, paramString).toString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getType() {
/* 107 */     return (String)getRowValue(type);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getType(String paramString) {
/* 118 */     return getValue(type, paramString).toString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getUnit() {
/* 127 */     return (String)getRowValue(unit);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getUnit(String paramString) {
/* 138 */     return getValue(unit, paramString).toString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getIsvalid() {
/* 147 */     return (String)getRowValue(isvalid);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getIsvalid(String paramString) {
/* 158 */     return getValue(isvalid, paramString).toString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getFormula() {
/* 166 */     return (String)getRowValue(formula);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getFormula(String paramString) {
/* 177 */     return getValue(formula, paramString).toString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getFormuladetails() {
/* 185 */     return (String)getRowValue(formuladetail);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getFormuladetails(String paramString) {
/* 196 */     return getValue(formuladetail, paramString).toString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void addComInfo(String paramString) {
/* 203 */     synchronized (syncObject) {
/* 204 */       if (!"".equals(paramString))
/* 205 */         addCache(paramString); 
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/gp/cominfo/AccessItemComInfo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */