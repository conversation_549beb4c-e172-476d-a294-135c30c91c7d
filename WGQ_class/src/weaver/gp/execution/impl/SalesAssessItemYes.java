/*    */ package weaver.gp.execution.impl;
/*    */ 
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.general.Util;
/*    */ import weaver.gp.execution.AccessItemInterface;
/*    */ 
/*    */ 
/*    */ public class SalesAssessItemYes
/*    */   implements AccessItemInterface
/*    */ {
/*    */   public Map<String, Object> getAccessItemScore(Map paramMap) {
/* 13 */     double d1 = 0.0D;
/* 14 */     d1 = Util.getDoubleValue(paramMap.get("cval").toString(), 100.0D);
/* 15 */     if (d1 == 0.0D) {
/* 16 */       d1 = 100.0D;
/*    */     }
/* 18 */     double d2 = Util.getDoubleValue(paramMap.get("gval").toString());
/* 19 */     double d3 = 0.0D;
/* 20 */     if (d2 != 0.0D) {
/* 21 */       d3 = d1 / d2 * 5.0D;
/*    */     }
/* 23 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 24 */     hashMap.put("result", Double.valueOf(d3));
/* 25 */     hashMap.put("item_result", Double.valueOf(d1));
/* 26 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/gp/execution/impl/SalesAssessItemYes.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */