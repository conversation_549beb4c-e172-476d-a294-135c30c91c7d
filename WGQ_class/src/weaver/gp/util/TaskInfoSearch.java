/*     */ package weaver.gp.util;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.ThreadVarLanguage;
/*     */ import weaver.general.TimeUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ public class TaskInfoSearch extends BaseBean {
/*     */   public List<Map<String, Object>> getTaskInfoList(User paramUser, int paramInt1, int paramInt2) throws Exception {
/*  17 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*  18 */     String str1 = "";
/*  19 */     String str2 = "";
/*  20 */     String str3 = "";
/*  21 */     RecordSet recordSet = new RecordSet();
/*  22 */     String str4 = paramUser.getUID() + "";
/*  23 */     if ("sqlserver".equals(recordSet.getDBType())) {
/*  24 */       str1 = "isnull";
/*  25 */       str2 = "max(operatedate+' '+operatetime)";
/*  26 */       str3 = "max(createdate+' '+createtime)";
/*  27 */     } else if ("mysql".equals(recordSet.getDBType())) {
/*  28 */       str1 = "ifnull";
/*  29 */       str2 = "max(CONCAT(operatedate,' ',operatetime))";
/*  30 */       str3 = "max(CONCAT(createdate,' ',createtime))";
/*     */     }
/*  32 */     else if ("postgresql".equals(recordSet.getDBType())) {
/*  33 */       str1 = "isnull";
/*  34 */       str2 = "max(operatedate ||' '||operatetime)";
/*  35 */       str3 = "max(createdate ||' '|| createtime)";
/*     */     } else {
/*     */       
/*  38 */       str1 = "nvl";
/*  39 */       str2 = "max(CONCAT(CONCAT(operatedate,' '),operatetime))";
/*  40 */       str3 = "max(CONCAT(CONCAT(createdate,' '),createtime))";
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  48 */     String str5 = " t1.id,t1.name,t1.status,t1.creater,t1.principalid,t1.lev,t1.begindate,t1.enddate,t1.createdate,t1.createtime,t1.showallsub ,(select count(tfb.id) from TM_TaskFeedback tfb where tfb.taskid=t1.id) as fbcount,(select " + str2 + " from TM_TaskLog tlog where tlog.taskid=t1.id and tlog.type=0 and tlog.operator=" + str4 + ") as lastviewdate,(select " + str3 + " from TM_TaskFeedback fb where fb.taskid=t1.id and fb.hrmid<>" + str4 + ") as lastfbdate," + str1 + "((select distinct 1 from TM_TaskSpecial tts where tts.taskid=t1.id and tts.userid=" + str4 + "),0) as special,(select max(tt.tododate) from TM_TaskTodo tt where tt.taskid=t1.id and tt.userid=" + paramUser.getUID() + ") as tododate,(select " + str2 + " from TM_TaskLog tlog where tlog.taskid=t1.id and tlog.type not in (0,11,12)) as lastoperatedate from TM_TaskInfo t1  where (t1.deleted=0 or t1.deleted is null)";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  54 */     String str6 = "select count(t1.id) as amount from TM_TaskInfo t1 where (t1.deleted=0 or t1.deleted is null)";
/*  55 */     String str7 = "";
/*  56 */     StringBuffer stringBuffer = new StringBuffer();
/*  57 */     stringBuffer.append(" and (t1.principalid=" + str4 + " or t1.creater=" + str4 + " or exists (select 1 from TM_TaskPartner tp where tp.taskid=t1.id and tp.partnerid=" + str4 + ") or exists (select 1 from TM_TaskSharer ts where ts.taskid=t1.id and ts.sharerid=" + str4 + ") or exists (select 1 from HrmResource hrm where (hrm.id=t1.principalid or hrm.id=t1.creater) and hrm.managerstr like '%," + str4 + ",%') or exists (select 1 from HrmResource hrm,TM_TaskPartner tp where tp.taskid=t1.id and hrm.id=tp.partnerid and hrm.managerstr like '%," + str4 + ",%'))");
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  63 */     int i = 0;
/*  64 */     recordSet.executeSql(str6 + stringBuffer.toString() + str7);
/*  65 */     if (recordSet.next()) {
/*  66 */       i = recordSet.getInt(1);
/*     */     }
/*  68 */     String str8 = " order by lastoperatedate desc";
/*  69 */     String str9 = " order by lastoperatedate asc";
/*  70 */     String str10 = " order by lastoperatedate desc";
/*  71 */     if (i > 0) {
/*  72 */       int j = paramInt1 * paramInt2;
/*  73 */       String str11 = str5 + stringBuffer.toString() + str7;
/*  74 */       String str12 = "";
/*  75 */       if (recordSet.getDBType().equals("oracle")) {
/*  76 */         str12 = "select " + str11 + str10;
/*  77 */         str12 = "select A.*,rownum rn from (" + str12 + ") A where rownum <= " + j;
/*     */       }
/*  79 */       else if (recordSet.getDBType().equals("mysql")) {
/*  80 */         str12 = "select " + str11 + str10;
/*  81 */         str12 = "select A.* from (" + str12 + ") limit " + j;
/*     */       }
/*  83 */       else if (recordSet.getDBType().equals("postgresql")) {
/*  84 */         str12 = "select " + str11 + str10;
/*  85 */         str12 = "select A.* from (" + str12 + ") limit " + j;
/*     */       } else {
/*     */         
/*  88 */         str12 = "select top " + j + " A.* from (select top " + j + str11 + str10 + ") A " + str9;
/*  89 */         str12 = "select top " + j + " B.* from (" + str12 + ") B " + str8;
/*     */       } 
/*     */       
/*  92 */       recordSet.executeSql(str12);
/*  93 */       ResourceComInfo resourceComInfo = new ResourceComInfo();
/*  94 */       HashMap<Object, Object> hashMap = null;
/*  95 */       while (recordSet.next()) {
/*  96 */         hashMap = new HashMap<>();
/*  97 */         String str13 = resourceComInfo.getLastname(recordSet.getString("principalid"));
/*  98 */         String str14 = recordSet.getString("name");
/*  99 */         String str15 = recordSet.getString("id");
/* 100 */         int k = recordSet.getInt("status");
/*     */         
/* 102 */         String str16 = (recordSet.getString("begindate") == null || "".equals(recordSet.getString("begindate"))) ? ((recordSet.getString("enddate") == null || "".equals(recordSet.getString("enddate"))) ? recordSet.getString("createdate") : recordSet.getString("enddate")) : recordSet.getString("begindate");
/* 103 */         String str17 = "";
/* 104 */         String str18 = "";
/* 105 */         if (str16 != null && !"".equals(str16)) {
/* 106 */           str17 = str16.substring(0, 4);
/* 107 */           str18 = str16.substring(5);
/*     */         } 
/* 109 */         String str19 = "" + SystemEnv.getHtmlLabelName(1960, ThreadVarLanguage.getLang()) + "";
/* 110 */         if (k == 2) {
/* 111 */           str19 = "" + SystemEnv.getHtmlLabelName(555, ThreadVarLanguage.getLang()) + "";
/* 112 */         } else if (k == 3) {
/* 113 */           str19 = "" + SystemEnv.getHtmlLabelName(16210, ThreadVarLanguage.getLang()) + "";
/*     */         } 
/* 115 */         String str20 = Util.null2String(recordSet.getString("lastviewdate"));
/* 116 */         String str21 = Util.null2String(recordSet.getString("lastfbdate"));
/* 117 */         boolean bool = false;
/* 118 */         if (!str21.equals("")) {
/* 119 */           if (str20.equals("") || TimeUtil.timeInterval(str20, str21) > 0L) {
/* 120 */             bool = true;
/*     */           } else {
/* 122 */             bool = false;
/*     */           } 
/*     */         } else {
/* 125 */           bool = false;
/*     */         } 
/* 127 */         String str22 = recordSet.getString("fbcount");
/* 128 */         hashMap.put("dutyMan", str13);
/* 129 */         hashMap.put("name", str14);
/* 130 */         hashMap.put("id", str15);
/* 131 */         hashMap.put("status", str19);
/* 132 */         hashMap.put("year", str17);
/* 133 */         hashMap.put("date", str18);
/* 134 */         hashMap.put("fbcount", str22);
/* 135 */         hashMap.put("noreadfb", Integer.valueOf(bool));
/* 136 */         arrayList.add(hashMap);
/*     */       } 
/*     */     } 
/* 139 */     return (List)arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCurrentYearTaskCount(User paramUser) {
/* 147 */     StringBuffer stringBuffer = new StringBuffer();
/* 148 */     stringBuffer.append("select count(t1.id) as amount from TM_TaskInfo t1 where (t1.deleted=0 or t1.deleted is null)");
/* 149 */     stringBuffer.append(" and (t1.principalid=" + paramUser.getUID() + " or t1.creater=" + paramUser.getUID());
/* 150 */     stringBuffer.append(" or exists (select 1 from TM_TaskPartner tp where tp.taskid=t1.id and tp.partnerid=" + paramUser.getUID() + ")");
/* 151 */     stringBuffer.append(")");
/* 152 */     String str = "0";
/* 153 */     RecordSet recordSet = new RecordSet();
/* 154 */     if (!"oracle".equals(recordSet.getDBType())) {
/* 155 */       stringBuffer.append(" and (");
/* 156 */       stringBuffer.append(" (t1.begindate >= convert(varchar(4),GetDate(),120)+'-01-01' and t1.begindate<= convert(varchar(4),GetDate(),120)+'-12-31')");
/* 157 */       stringBuffer.append(" or (t1.enddate >=  convert(varchar(4),GetDate(),120)+'-01-01' and t1.enddate<= convert(varchar(4),GetDate(),120)+'-12-31')");
/* 158 */       stringBuffer.append(")");
/*     */     } else {
/* 160 */       stringBuffer.append(" and (");
/* 161 */       stringBuffer.append(" (t1.begindate >= to_char(sysdate,'yyyy') || '-01-01' and t1.begindate<= to_char(sysdate,'yyyy') ||'-12-31')");
/* 162 */       stringBuffer.append(" or (t1.enddate >=  to_char(sysdate,'yyyy') ||'-01-01' and t1.enddate<= to_char(sysdate,'yyyy') ||'-12-31')");
/* 163 */       stringBuffer.append(")");
/*     */     } 
/* 165 */     recordSet.executeSql(stringBuffer.toString());
/* 166 */     if (recordSet.next()) {
/* 167 */       str = Util.null2s(recordSet.getString("amount"), "0");
/*     */     }
/* 169 */     return str;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/gp/util/TaskInfoSearch.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */