/*     */ package weaver.gp.util;
/*     */ 
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.crm.Maint.CustomerInfoComInfo;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.company.SubCompanyComInfo;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ 
/*     */ public class RightUtil extends BaseBean {
/*  11 */   private ResourceComInfo rc = null;
/*  12 */   private CustomerInfoComInfo ci = null;
/*  13 */   private SubCompanyComInfo sc = null;
/*     */   
/*     */   public RightUtil() {
/*     */     try {
/*  17 */       this.rc = new ResourceComInfo();
/*  18 */       this.sc = new SubCompanyComInfo();
/*  19 */       this.ci = new CustomerInfoComInfo();
/*  20 */     } catch (Exception exception) {
/*  21 */       writeLog(exception);
/*     */     } 
/*     */   }
/*     */   
/*     */   public int getProgramRight(String paramString1, String paramString2) {
/*  26 */     RecordSet recordSet = new RecordSet();
/*  27 */     byte b = 0;
/*  28 */     boolean bool1 = false;
/*  29 */     boolean bool2 = false;
/*  30 */     boolean bool3 = false;
/*  31 */     boolean bool4 = false;
/*  32 */     if (paramString1.equals(paramString2)) {
/*  33 */       bool1 = true;
/*  34 */       bool3 = true;
/*  35 */     } else if (this.rc.isManager(Integer.parseInt(paramString2), paramString1)) {
/*  36 */       bool2 = true;
/*  37 */       bool3 = true;
/*     */     } 
/*     */     
/*  40 */     String str1 = "";
/*  41 */     String str2 = "";
/*  42 */     int i = 0;
/*  43 */     int j = 0;
/*  44 */     recordSet.executeSql("select * from GP_BaseSetting where (resourceid=" + this.sc.getCompanyid(this.rc.getSubCompanyID(paramString1)) + " and resourcetype=1) or (resourceid=" + this.rc.getSubCompanyID(paramString1) + " and resourcetype=2) or (resourceid=" + this.rc.getDepartmentID(paramString1) + " and resourcetype=3) order by resourcetype desc");
/*  45 */     if (recordSet.next()) {
/*  46 */       str1 = Util.null2String(recordSet.getString("programcreate"));
/*  47 */       str2 = Util.null2String(recordSet.getString("programaudit"));
/*  48 */       i = Util.getIntValue(recordSet.getString("isself"), 0);
/*  49 */       j = Util.getIntValue(recordSet.getString("ismanager"), 0);
/*     */     } 
/*  51 */     if ((bool1 && i == 1) || (bool2 && j == 1)) {
/*  52 */       bool4 = true;
/*  53 */     } else if (("," + str1 + ",").indexOf("," + paramString2 + ",") > -1) {
/*  54 */       bool3 = true;
/*  55 */       bool4 = true;
/*  56 */     } else if (("," + str2 + ",").indexOf("," + paramString2 + ",") > -1) {
/*  57 */       bool3 = true;
/*     */     } 
/*  59 */     if (bool3) b = 1; 
/*  60 */     if (bool4) b = 2;
/*     */     
/*  62 */     return b;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean isCanAuditProgram(String paramString1, String paramString2) {
/*  71 */     if (paramString1.equals("")) return false; 
/*  72 */     RecordSet recordSet = new RecordSet();
/*  73 */     recordSet.executeSql("select count(id) from GP_AccessProgramAudit where programid=" + paramString1 + " and userid=" + paramString2);
/*  74 */     if (recordSet.next() && recordSet.getInt(1) > 0) {
/*  75 */       return true;
/*     */     }
/*  77 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getUnAuditProgramHrm(String paramString) {
/*  87 */     if (paramString.equals("")) return ""; 
/*  88 */     RecordSet recordSet = new RecordSet();
/*  89 */     String str = "";
/*  90 */     recordSet.executeSql("select userid from GP_AccessProgramAudit where programid=" + paramString);
/*  91 */     while (recordSet.next()) {
/*  92 */       str = str + "," + Util.null2String(recordSet.getString(1));
/*     */     }
/*  94 */     if (!str.equals("")) str = str.substring(1); 
/*  95 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean isCanAuditScore(String paramString1, String paramString2) {
/* 104 */     if (paramString1.equals("")) return false; 
/* 105 */     RecordSet recordSet = new RecordSet();
/* 106 */     recordSet.executeSql("select count(id) from GP_AccessScoreAudit where scoreid=" + paramString1 + " and userid=" + paramString2);
/* 107 */     if (recordSet.next() && recordSet.getInt(1) > 0) {
/* 108 */       return true;
/*     */     }
/* 110 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean isCanInitScore(String paramString1, String paramString2) {
/* 120 */     if (paramString1.equals("")) return false; 
/* 121 */     RecordSet recordSet = new RecordSet();
/* 122 */     recordSet.executeSql("select count(id) from GP_AccessScore where isupdate=1 and isfirst=1 and operator=" + paramString2 + " and id=" + paramString1);
/* 123 */     if (recordSet.next() && recordSet.getInt(1) > 0) {
/* 124 */       return true;
/*     */     }
/* 126 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean isCanScoreReturn(String paramString1, String paramString2) {
/* 136 */     if (paramString1.equals("")) return false; 
/* 137 */     RecordSet recordSet = new RecordSet();
/* 138 */     recordSet.executeQuery("select count(id) from GP_AccessScore where status in (0,2) and isfirst=0 and operator=? and id=?", new Object[] { paramString2, paramString1 });
/* 139 */     if (recordSet.next() && recordSet.getInt(1) > 0) {
/* 140 */       return true;
/*     */     }
/* 142 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean isCanResetScore(String paramString1, String paramString2) {
/* 152 */     if (paramString1.equals("")) return false; 
/* 153 */     RecordSet recordSet = new RecordSet();
/* 154 */     recordSet.executeSql("select count(t1.id) from GP_AccessScore t1,GP_AccessScoreCheck t2 where t1.id=t2.scoreid and (t1.status=1 or t1.status=3) and t1.id=" + paramString1 + " and t2.userid=" + paramString2);
/* 155 */     if (recordSet.next() && recordSet.getInt(1) > 0) {
/* 156 */       return true;
/*     */     }
/* 158 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean isCanViewScore(String paramString1, String paramString2) {
/* 168 */     if (paramString1.equals("")) return false; 
/* 169 */     RecordSet recordSet = new RecordSet();
/* 170 */     boolean bool = false;
/* 171 */     recordSet.executeSql("select userid,auditids from GP_AccessScore where isvalid=1 and id=" + paramString1);
/* 172 */     if (recordSet.next()) {
/* 173 */       String str1 = Util.null2String(recordSet.getString(1));
/* 174 */       String str2 = Util.null2String(recordSet.getString(2));
/*     */       
/* 176 */       if (str1.equals(paramString2)) {
/* 177 */         bool = true;
/* 178 */       } else if (this.rc.isManager(Util.getIntValue(paramString2), str1)) {
/* 179 */         bool = true;
/* 180 */       } else if (("," + str2 + ",").indexOf("," + paramString2 + ",") > -1) {
/* 181 */         bool = true;
/*     */       } else {
/* 183 */         recordSet.executeSql("select count(id) from GP_AccessScoreCheck where scoreid=" + paramString1 + " and userid=" + paramString2);
/* 184 */         if (recordSet.next() && recordSet.getInt(1) > 0) {
/* 185 */           bool = true;
/*     */         } else {
/* 187 */           recordSet.executeSql("select accessconfirm,accessview from GP_BaseSetting where (resourceid=" + this.rc.getSubCompanyID(str1) + " and resourcetype=2) or (resourceid=" + this.rc.getDepartmentID(str1) + " and resourcetype=3) order by resourcetype desc ");
/* 188 */           if (recordSet.next()) {
/* 189 */             String str3 = Util.null2String(recordSet.getString("accessconfirm"));
/* 190 */             String str4 = Util.null2String(recordSet.getString("accessview"));
/* 191 */             if (("," + str3 + ",").indexOf("," + paramString2 + ",") > -1) {
/* 192 */               bool = true;
/* 193 */             } else if (("," + str4 + ",").indexOf("," + paramString2 + ",") > -1) {
/* 194 */               bool = true;
/*     */             } 
/*     */           } 
/*     */         } 
/*     */       } 
/*     */     } 
/* 200 */     if (!bool) {
/*     */       
/* 202 */       recordSet.executeSql("select count(id) from GP_AccessScoreLog where scoreid=" + paramString1 + " and operatetype in (4,5) and operator=" + paramString2);
/* 203 */       if (recordSet.next() && recordSet.getInt(1) > 0) {
/* 204 */         bool = true;
/*     */       }
/*     */     } 
/* 207 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getUnAuditScoreHrm(String paramString) {
/* 216 */     if (paramString.equals("")) return ""; 
/* 217 */     RecordSet recordSet = new RecordSet();
/* 218 */     String str = "";
/* 219 */     recordSet.executeSql("select userid from GP_AccessScoreAudit where scoreid=" + paramString);
/* 220 */     while (recordSet.next()) {
/* 221 */       str = str + "," + Util.null2String(recordSet.getString(1));
/*     */     }
/* 223 */     if (!str.equals("")) str = str.substring(1); 
/* 224 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static int hassub(String paramString) {
/* 232 */     RecordSet recordSet = new RecordSet();
/* 233 */     boolean bool = false;
/* 234 */     recordSet.executeSql("select count(id) as amount from hrmresource where " + ("sqlserver".equals(recordSet.getDBType()) ? " loginid<>'' and " : "") + " loginid is not null and (status =0 or status = 1 or status = 2 or status = 3) and managerid=" + paramString);
/* 235 */     if (recordSet.next() && recordSet.getInt(1) > 0) {
/* 236 */       bool = true;
/*     */     }
/* 238 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static int getsubcount(String paramString) {
/* 246 */     RecordSet recordSet = new RecordSet();
/* 247 */     int i = 0;
/* 248 */     recordSet.executeSql("select count(id) as amount from hrmresource where " + ("sqlserver".equals(recordSet.getDBType()) ? " loginid<>'' and " : "") + " loginid is not null and (status =0 or status = 1 or status = 2 or status = 3) and managerid=" + paramString);
/* 249 */     if (recordSet.next()) i = recordSet.getInt(1); 
/* 250 */     return i;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/gp/util/RightUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */