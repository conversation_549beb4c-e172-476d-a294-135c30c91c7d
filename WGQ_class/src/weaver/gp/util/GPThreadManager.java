/*     */ package weaver.gp.util;
/*     */ import com.engine.workrelate.biz.performance.PerformanceOperateUtil;
/*     */ import java.util.Calendar;
/*     */ import java.util.Date;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.file.Prop;
/*     */ import weaver.general.TimeUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.system.ThreadWork;
/*     */ 
/*     */ public class GPThreadManager implements ThreadWork {
/*  12 */   int startuphour = 2;
/*     */ 
/*     */   
/*     */   public GPThreadManager() {}
/*     */ 
/*     */   
/*     */   public GPThreadManager(int paramInt) {
/*  19 */     this.startuphour = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public void doThreadWork() {
/*  25 */     Calendar calendar = Calendar.getInstance();
/*  26 */     calendar.setTime(new Date());
/*  27 */     int i = calendar.get(11);
/*     */ 
/*     */     
/*  30 */     int j = calendar.get(5);
/*  31 */     int k = Util.getIntValue(Prop.getPropValue("gperformance", "initday"), 1);
/*     */     
/*  33 */     if ((i == this.startuphour || this.startuphour == -1) && k <= j) {
/*  34 */       int m = Util.getIntValue(TimeUtil.getCurrentDateString().substring(5, 7));
/*  35 */       int n = Util.getIntValue(TimeUtil.getCurrentSeason());
/*  36 */       int i1 = Util.getIntValue(TimeUtil.getCurrentDateString().substring(0, 4));
/*  37 */       String str1 = "";
/*  38 */       String str2 = "";
/*  39 */       String str3 = "";
/*  40 */       String str4 = "";
/*  41 */       PerformanceOperateUtil performanceOperateUtil = new PerformanceOperateUtil();
/*  42 */       RecordSet recordSet = new RecordSet();
/*     */       
/*  44 */       recordSet.executeSql("select id from GP_InitTag where year=" + i1 + " and type1=1 and type2=" + m);
/*  45 */       if (!recordSet.next()) {
/*     */ 
/*     */         
/*     */         try {
/*  49 */           str4 = TimeUtil.getYearMonthEndDay(i1, m);
/*  50 */         } catch (Exception exception) {}
/*  51 */         recordSet.executeSql("select resourceid,mstarttype,mstartdays,mendtype,menddays,mauditday from GP_BaseSetting where resourcetype=2 and ismonth=1");
/*  52 */         while (recordSet.next()) {
/*  53 */           str1 = Util.null2String(recordSet.getString("resourceid"));
/*  54 */           str2 = TimeUtil.dateAdd(str4, Util.getIntValue(recordSet.getString("mstarttype"), 1) * Util.getIntValue(recordSet.getString("mstartdays"), 0));
/*  55 */           str3 = TimeUtil.dateAdd(str4, Util.getIntValue(recordSet.getString("mendtype"), 1) * Util.getIntValue(recordSet.getString("menddays"), 0));
/*  56 */           String str = TimeUtil.dateAdd(str3, Util.getIntValue(recordSet.getString("mauditday"), 0));
/*  57 */           performanceOperateUtil.initSubcompanyAccessScoreData(str1, i1, 1, m, str2, str3, str);
/*     */         } 
/*  59 */         recordSet.executeSql("insert into GP_InitTag(year,type1,type2) values(" + i1 + ",1," + m + ")");
/*     */       } 
/*     */       
/*  62 */       recordSet.executeSql("select id from GP_InitTag where year=" + i1 + " and type1=2 and type2=" + n);
/*  63 */       if (!recordSet.next()) {
/*     */ 
/*     */         
/*     */         try {
/*  67 */           if (n == 1) str4 = TimeUtil.getYearMonthEndDay(i1, 3); 
/*  68 */           if (n == 2) str4 = TimeUtil.getYearMonthEndDay(i1, 6); 
/*  69 */           if (n == 3) str4 = TimeUtil.getYearMonthEndDay(i1, 9); 
/*  70 */           if (n == 4) str4 = TimeUtil.getYearMonthEndDay(i1, 12); 
/*  71 */         } catch (Exception exception) {}
/*  72 */         recordSet.executeSql("select resourceid,qstarttype,qstartdays,qendtype,qenddays,qauditday from GP_BaseSetting where resourcetype=2 and isquarter=1");
/*  73 */         while (recordSet.next()) {
/*  74 */           str1 = Util.null2String(recordSet.getString("resourceid"));
/*  75 */           str2 = TimeUtil.dateAdd(str4, Util.getIntValue(recordSet.getString("qstarttype"), 1) * Util.getIntValue(recordSet.getString("qstartdays"), 0));
/*  76 */           str3 = TimeUtil.dateAdd(str4, Util.getIntValue(recordSet.getString("qendtype"), 1) * Util.getIntValue(recordSet.getString("qenddays"), 0));
/*  77 */           String str = TimeUtil.dateAdd(str3, Util.getIntValue(recordSet.getString("qauditday"), 0));
/*  78 */           performanceOperateUtil.initSubcompanyAccessScoreData(str1, i1, 2, n, str2, str3, str);
/*     */         } 
/*  80 */         recordSet.executeSql("insert into GP_InitTag(year,type1,type2) values(" + i1 + ",2," + n + ")");
/*     */       } 
/*     */       
/*  83 */       recordSet.executeSql("select id from GP_InitTag where year=" + i1 + " and type1=3 and type2=0");
/*  84 */       if (!recordSet.next()) {
/*     */ 
/*     */         
/*     */         try {
/*  88 */           str4 = TimeUtil.getYearMonthEndDay(i1, 6);
/*  89 */         } catch (Exception exception) {}
/*  90 */         recordSet.executeSql("select resourceid,hstarttype,hstartdays,hendtype,henddays,hauditday from GP_BaseSetting where resourcetype=2 and ishyear=1");
/*  91 */         while (recordSet.next()) {
/*  92 */           str1 = Util.null2String(recordSet.getString("resourceid"));
/*  93 */           str2 = TimeUtil.dateAdd(str4, Util.getIntValue(recordSet.getString("hstarttype"), 1) * Util.getIntValue(recordSet.getString("hstartdays"), 0));
/*  94 */           str3 = TimeUtil.dateAdd(str4, Util.getIntValue(recordSet.getString("hendtype"), 1) * Util.getIntValue(recordSet.getString("henddays"), 0));
/*  95 */           String str = TimeUtil.dateAdd(str3, Util.getIntValue(recordSet.getString("hauditday"), 0));
/*  96 */           performanceOperateUtil.initSubcompanyAccessScoreData(str1, i1, 3, 0, str2, str3, str);
/*     */         } 
/*  98 */         recordSet.executeSql("insert into GP_InitTag(year,type1,type2) values(" + i1 + ",3,0)");
/*     */       } 
/*     */       
/* 101 */       recordSet.executeSql("select id from GP_InitTag where year=" + i1 + " and type1=4 and type2=0");
/* 102 */       if (!recordSet.next()) {
/*     */ 
/*     */         
/*     */         try {
/* 106 */           str4 = TimeUtil.getYearMonthEndDay(i1, 12);
/* 107 */         } catch (Exception exception) {}
/* 108 */         recordSet.executeSql("select resourceid,fstarttype,fstartdays,fendtype,fenddays,fauditday from GP_BaseSetting where resourcetype=2 and isfyear=1");
/* 109 */         while (recordSet.next()) {
/* 110 */           str1 = Util.null2String(recordSet.getString("resourceid"));
/* 111 */           str2 = TimeUtil.dateAdd(str4, Util.getIntValue(recordSet.getString("fstarttype"), 1) * Util.getIntValue(recordSet.getString("fstartdays"), 0));
/* 112 */           str3 = TimeUtil.dateAdd(str4, Util.getIntValue(recordSet.getString("fendtype"), 1) * Util.getIntValue(recordSet.getString("fenddays"), 0));
/* 113 */           String str = TimeUtil.dateAdd(str3, Util.getIntValue(recordSet.getString("fauditday"), 0));
/* 114 */           performanceOperateUtil.initSubcompanyAccessScoreData(str1, i1, 4, 0, str2, str3, str);
/*     */         } 
/* 116 */         recordSet.executeSql("insert into GP_InitTag(year,type1,type2) values(" + i1 + ",4,0)");
/*     */       } 
/*     */       
/*     */       try {
/* 120 */         resetData(i1 + "", "1", m + "");
/* 121 */         resetData(i1 + "", "2", n + "");
/* 122 */         resetData(i1 + "", "3", "0");
/* 123 */         resetData(i1 + "", "4", "0");
/* 124 */       } catch (Exception exception) {}
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/* 129 */     if (i == 23 && k <= j) {
/* 130 */       int m = Util.getIntValue(TimeUtil.getCurrentDateString().substring(5, 7));
/* 131 */       int n = Util.getIntValue(TimeUtil.getCurrentSeason());
/* 132 */       int i1 = Util.getIntValue(TimeUtil.getCurrentDateString().substring(0, 4));
/*     */       
/*     */       try {
/* 135 */         resetData(i1 + "", "1", m + "");
/* 136 */         resetData(i1 + "", "2", n + "");
/* 137 */         resetData(i1 + "", "3", "0");
/* 138 */         resetData(i1 + "", "4", "0");
/* 139 */       } catch (Exception exception) {}
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   private void resetData(String paramString1, String paramString2, String paramString3) throws Exception {
/* 145 */     if (paramString1 == null || "".equals(paramString1)) {
/* 146 */       paramString1 = Util.null2String(TimeUtil.getCurrentDateString().substring(0, 4));
/*     */     }
/* 148 */     if (paramString2 == null || "".equals(paramString2)) {
/* 149 */       paramString2 = "1";
/*     */     }
/* 151 */     if (paramString3 == null || "".equals(paramString3)) {
/* 152 */       paramString3 = Util.null2String(TimeUtil.getCurrentDateString().substring(5, 7));
/*     */     }
/* 154 */     String str1 = "";
/* 155 */     String str2 = "";
/* 156 */     String str3 = "";
/* 157 */     String str4 = "";
/* 158 */     String str5 = "";
/* 159 */     String str6 = "";
/* 160 */     String str7 = "";
/* 161 */     RecordSet recordSet1 = new RecordSet();
/* 162 */     RecordSet recordSet2 = new RecordSet();
/* 163 */     if ("oracle".equals(recordSet1.getDBType())) {
/* 164 */       str7 = "SELECT scoreid,scorecheckid,managerid,userid,lastname,exeorder,operator,checkuserid FROM (select s.id as scoreid,sc.id as scorecheckid,h.managerid,p.userid,h.lastname,sc.exeorder,s.operator,sc.userid checkuserid,ROW_NUMBER() OVER (PARTITION BY p.userid ORDER BY p.startdate DESC , p.id DESC) rn from GP_AccessProgram p join HrmResource h on p.userid=h.id join GP_AccessProgramCheck pd on pd.programid=p.id and pd.userid=-1  join GP_AccessScore s on s.userid=p.userid and s.operator<>h.managerid  and p.startdate <= s.startdate and s.status=0 and s.year=" + paramString1 + " and s.type1=" + paramString2 + " and s.type2=" + paramString3 + " join GP_AccessScorecheck sc on s.id=sc.scoreid and sc.exeorder=pd.exeorder and sc.userid<>h.managerid AND sc.status != 2  where h.status in (0,1,2,3)  and  p.programtype = " + paramString2 + " AND p.status = 3 ) where rn = 1";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*     */     }
/* 175 */     else if ("mysql".equals(recordSet1.getDBType())) {
/* 176 */       str7 = "select s.id as scoreid,sc.id as scorecheckid,h.managerid,p.userid,h.lastname,sc.exeorder,s.operator,sc.userid checkuserid from GP_AccessProgram p join HrmResource h on p.userid=h.id join GP_AccessProgramCheck pd on pd.programid=p.id and pd.userid=-1 join GP_AccessScore s on s.userid=p.userid and s.operator<>h.managerid and s.status=0 and s.year=" + paramString1 + " and s.type1=" + paramString2 + " and s.type2=" + paramString3 + " join GP_AccessScorecheck sc on s.id=sc.scoreid and sc.exeorder=pd.exeorder and sc.userid<>h.managerid AND sc.status != 2  where h.status in (0,1,2,3)  and (p.id=(select p2.id from GP_AccessProgram p2 where p2.programtype=" + paramString2 + " and p2.status=3 and p2.userid=p.userid and p2.startdate<=s.startdate order by p2.startdate desc,p2.id desc limit 1))";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*     */     }
/* 186 */     else if ("postgresql".equals(recordSet1.getDBType())) {
/* 187 */       str7 = "select s.id as scoreid,sc.id as scorecheckid,h.managerid,p.userid,h.lastname,sc.exeorder,s.operator,sc.userid checkuserid from GP_AccessProgram p join HrmResource h on p.userid=h.id join GP_AccessProgramCheck pd on pd.programid=p.id and pd.userid=-1 join GP_AccessScore s on s.userid=p.userid and s.operator<>h.managerid and s.status=0 and s.year=" + paramString1 + " and s.type1=" + paramString2 + " and s.type2=" + paramString3 + " join GP_AccessScorecheck sc on s.id=sc.scoreid and sc.exeorder=pd.exeorder and sc.userid<>h.managerid AND sc.status != 2  where h.status in (0,1,2,3)  and (p.id=(select p2.id from GP_AccessProgram p2 where p2.programtype=" + paramString2 + " and p2.status=3 and p2.userid=p.userid and p2.startdate<=s.startdate order by p2.startdate desc,p2.id desc limit 1))";
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*     */     }
/*     */     else {
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 198 */       str7 = "select s.id as scoreid,sc.id as scorecheckid,h.managerid,p.userid,h.lastname,sc.exeorder,s.operator,sc.userid checkuserid from GP_AccessProgram p join HrmResource h on p.userid=h.id join GP_AccessProgramCheck pd on pd.programid=p.id and pd.userid=-1 join GP_AccessScore s on s.userid=p.userid and s.operator<>h.managerid and s.status=0 and s.year=" + paramString1 + " and s.type1=" + paramString2 + " and s.type2=" + paramString3 + " join GP_AccessScorecheck sc on s.id=sc.scoreid and sc.exeorder=pd.exeorder and sc.userid<>h.managerid AND sc.status != 2  where h.status in (0,1,2,3)  and (p.id=(select top 1 p2.id from GP_AccessProgram p2 where p2.programtype=" + paramString2 + " and p2.status=3 and p2.userid=p.userid and p2.startdate<=s.startdate order by p2.startdate desc,p2.id desc))";
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 208 */     recordSet1.executeSql(str7);
/* 209 */     while (recordSet1.next()) {
/* 210 */       str1 = Util.null2String(recordSet1.getString("scoreid"));
/* 211 */       str2 = Util.null2String(recordSet1.getString("scorecheckid"));
/* 212 */       str3 = Util.null2String(recordSet1.getString("managerid"));
/* 213 */       str4 = Util.null2String(recordSet1.getString("exeorder"));
/* 214 */       str5 = Util.null2String(recordSet1.getString("operator"));
/* 215 */       str6 = Util.null2String(recordSet1.getString("checkuserid"));
/*     */       
/* 217 */       if ("0".equals(str4)) {
/* 218 */         recordSet2.executeSql("update GP_AccessScore set operator=" + str3 + " where id=" + str1);
/* 219 */       } else if (!"0".equals(str4) && str5.equals(str6)) {
/* 220 */         recordSet2.executeSql("update GP_AccessScore set operator=" + str3 + " where id=" + str1);
/*     */       } 
/* 222 */       recordSet2.executeSql("update GP_AccessScorecheck set userid=" + str3 + " where id=" + str2);
/* 223 */       recordSet2.executeSql("insert into GP_ACCESSresetlog(scoreid,operator,operatedate,operatetype) values(" + str1 + ",1,'" + TimeUtil.getCurrentTimeString() + "',1)");
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/gp/util/GPThreadManager.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */