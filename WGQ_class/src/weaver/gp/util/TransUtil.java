/*     */ package weaver.gp.util;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.crm.Maint.CustomerInfoComInfo;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.ThreadVarLanguage;
/*     */ import weaver.general.TimeUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ public class TransUtil extends BaseBean {
/*  14 */   private ResourceComInfo rc = null;
/*  15 */   private CustomerInfoComInfo ci = null;
/*     */   
/*     */   public TransUtil() {
/*     */     try {
/*  19 */       this.rc = new ResourceComInfo();
/*  20 */       this.ci = new CustomerInfoComInfo();
/*  21 */     } catch (Exception exception) {
/*  22 */       writeLog(exception);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCustomer(String paramString) {
/*  34 */     String str = "";
/*  35 */     if (paramString != null && !"".equals(paramString)) {
/*  36 */       ArrayList<String> arrayList = Util.TokenizerString(paramString, ",");
/*  37 */       for (byte b = 0; b < arrayList.size(); b++) {
/*  38 */         str = str + "<a href='/CRM/data/ViewCustomer.jsp?log=n&CustomerID=" + (String)arrayList.get(b) + "' target='_blank'>" + this.ci.getCustomerInfoname(arrayList.get(b)) + "</a>&nbsp;";
/*     */       }
/*     */     } 
/*  41 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getPerson(String paramString) {
/*  52 */     String str = "";
/*  53 */     if (paramString != null && !"".equals(paramString)) {
/*  54 */       ArrayList<String> arrayList = Util.TokenizerString(paramString, ",");
/*  55 */       for (byte b = 0; b < arrayList.size(); b++) {
/*  56 */         str = str + "<a href='javaScript:openhrm(" + arrayList.get(b) + ");' onclick='pointerXY(event);'>" + this.rc.getResourcename(arrayList.get(b)) + "</a> ";
/*     */       }
/*     */     } 
/*  59 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getYN(String paramString1, String paramString2) {
/*  69 */     if ("1".equals(paramString1))
/*  70 */       return SystemEnv.getHtmlLabelName(163, Integer.parseInt(paramString2)); 
/*  71 */     if ("0".equals(paramString1)) {
/*  72 */       return SystemEnv.getHtmlLabelName(161, Integer.parseInt(paramString2));
/*     */     }
/*  74 */     return "";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getDateTime(String paramString1, String paramString2) {
/*  86 */     return paramString1 + " " + paramString2;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getMailto(String paramString) {
/*  95 */     if (!"".equals(paramString) && paramString != null) {
/*  96 */       return "<a href='mailto:" + paramString + "'>" + paramString + "</a>";
/*     */     }
/*  98 */     return "";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getProgramOperateType(String paramString) {
/* 108 */     if ("1".equals(paramString))
/* 109 */       return "" + SystemEnv.getHtmlLabelName(82, ThreadVarLanguage.getLang()) + ""; 
/* 110 */     if ("2".equals(paramString))
/* 111 */       return "" + SystemEnv.getHtmlLabelName(383323, ThreadVarLanguage.getLang()) + ""; 
/* 112 */     if ("3".equals(paramString))
/* 113 */       return "" + SystemEnv.getHtmlLabelName(615, ThreadVarLanguage.getLang()) + ""; 
/* 114 */     if ("4".equals(paramString))
/* 115 */       return "" + SystemEnv.getHtmlLabelName(129829, ThreadVarLanguage.getLang()) + ""; 
/* 116 */     if ("5".equals(paramString))
/* 117 */       return "" + SystemEnv.getHtmlLabelName(142, ThreadVarLanguage.getLang()) + ""; 
/* 118 */     if ("13".equals(paramString))
/* 119 */       return "" + SystemEnv.getHtmlLabelName(506066, ThreadVarLanguage.getLang()) + ""; 
/* 120 */     if ("14".equals(paramString)) {
/* 121 */       return "" + SystemEnv.getHtmlLabelName(26472, ThreadVarLanguage.getLang()) + "";
/*     */     }
/* 123 */     return "";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getSocreOperateType(String paramString) {
/* 133 */     if ("1".equals(paramString))
/* 134 */       return "" + SystemEnv.getHtmlLabelName(384122, ThreadVarLanguage.getLang()) + ""; 
/* 135 */     if ("2".equals(paramString))
/* 136 */       return "" + SystemEnv.getHtmlLabelName(383323, ThreadVarLanguage.getLang()) + ""; 
/* 137 */     if ("3".equals(paramString))
/* 138 */       return "" + SystemEnv.getHtmlLabelName(615, ThreadVarLanguage.getLang()) + ""; 
/* 139 */     if ("4".equals(paramString))
/* 140 */       return "" + SystemEnv.getHtmlLabelName(129829, ThreadVarLanguage.getLang()) + ""; 
/* 141 */     if ("5".equals(paramString))
/* 142 */       return "" + SystemEnv.getHtmlLabelName(142, ThreadVarLanguage.getLang()) + ""; 
/* 143 */     if ("6".equals(paramString)) {
/* 144 */       return "" + SystemEnv.getHtmlLabelName(508884, ThreadVarLanguage.getLang()) + "";
/*     */     }
/* 146 */     return "";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getType1(String paramString) {
/* 155 */     if ("1".equals(paramString))
/* 156 */       return "" + SystemEnv.getHtmlLabelName(19398, ThreadVarLanguage.getLang()) + " "; 
/* 157 */     if ("2".equals(paramString))
/* 158 */       return "" + SystemEnv.getHtmlLabelName(17495, ThreadVarLanguage.getLang()) + ""; 
/* 159 */     if ("3".equals(paramString))
/* 160 */       return "" + SystemEnv.getHtmlLabelName(20729, ThreadVarLanguage.getLang()) + ""; 
/* 161 */     if ("4".equals(paramString)) {
/* 162 */       return "" + SystemEnv.getHtmlLabelName(17138, ThreadVarLanguage.getLang()) + "";
/*     */     }
/* 164 */     return "";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getProgramLink(String paramString1, String paramString2) {
/* 173 */     String str = "";
/* 174 */     if (paramString1 != null && !"".equals(paramString1)) {
/* 175 */       str = "<a href=\"javascript:openFullWindowHaveBar('/performance/program/ProgramView.jsp?programid=" + paramString1 + "')\">" + paramString2 + "</a>";
/*     */     }
/* 177 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getScoreLink(String paramString1, String paramString2) {
/* 185 */     String str = "";
/* 186 */     if (paramString1 != null && !"".equals(paramString1)) {
/* 187 */       str = "<a href=\"javascript:openFullWindowHaveBar('/performance/access/AccessView.jsp?scoreid=" + paramString1 + "')\">" + paramString2 + "</a>";
/*     */     }
/* 189 */     return str;
/*     */   }
/*     */   public String getScoreView(String paramString) {
/* 192 */     String str1 = "";
/* 193 */     String str2 = Util.null2String(paramString).trim();
/* 194 */     if (!"".equals(str2)) {
/* 195 */       str1 = "<a href=\"javascript:openFullWindowHaveBar('/performance/access/AccessView.jsp?scoreid=" + str2 + "')\">" + SystemEnv.getHtmlLabelName(126218, ThreadVarLanguage.getLang()) + "</a>";
/*     */     } else {
/* 197 */       str1 = "" + SystemEnv.getHtmlLabelName(126218, ThreadVarLanguage.getLang()) + "";
/*     */     } 
/* 199 */     return str1;
/*     */   }
/*     */   
/*     */   public String getScoreResultView(String paramString1, String paramString2) {
/* 203 */     String str1 = "";
/* 204 */     double d = Util.getDoubleValue(paramString1, 0.0D);
/* 205 */     String[] arrayOfString = Util.TokenizerString2(paramString2, "+");
/* 206 */     String str2 = Util.null2String(arrayOfString[0]).trim();
/* 207 */     int i = Util.getIntValue(arrayOfString[1], 0);
/* 208 */     String str3 = Util.null2String(arrayOfString[2]).trim();
/* 209 */     String str4 = Util.null2String(arrayOfString[3]).trim();
/* 210 */     String str5 = Util.null2String(arrayOfString[4]).trim();
/* 211 */     String str6 = Util.null2String(arrayOfString[5]).trim();
/* 212 */     if (!"".equals(str2)) {
/* 213 */       str1 = "<span class='status_txt'><a href=\"javascript:openFullWindowHaveBar('/performance/access/AccessView.jsp?scoreid=" + str2 + "')\">" + d + "</a></span>";
/* 214 */       if (i == 1) {
/* 215 */         str1 = str1 + "<span class='rescore' title='" + SystemEnv.getHtmlLabelName(509140, ThreadVarLanguage.getLang()) + "'></span>";
/*     */       }
/*     */     } else {
/* 218 */       str1 = "<span class='status_txt'><a href=\"javascript:openFullWindowHaveBar('/performance/access/AccessView.jsp?resourceid=" + str3 + "&year=" + str4 + "&type1=" + str5 + "&type2=" + str6 + "')\">-</a></span>";
/*     */     } 
/*     */     
/* 221 */     return str1;
/*     */   }
/*     */   
/*     */   public String getScoreReporttView(String paramString1, String paramString2) {
/* 225 */     String str1 = "";
/* 226 */     double d = Util.getDoubleValue(paramString1, 0.0D);
/* 227 */     String[] arrayOfString = Util.TokenizerString2(paramString2, "+");
/* 228 */     String str2 = Util.null2String(arrayOfString[0]).trim();
/* 229 */     int i = Util.getIntValue(arrayOfString[1], 0);
/* 230 */     String str3 = Util.null2String(arrayOfString[2]).trim();
/* 231 */     String str4 = Util.null2String(arrayOfString[3]).trim();
/* 232 */     String str5 = Util.null2String(arrayOfString[4]).trim();
/* 233 */     String str6 = Util.null2String(arrayOfString[5]).trim();
/* 234 */     if (!"".equals(str2)) {
/* 235 */       str1 = "<span class='status_txt'><a href=\"javascript:openFullWindowHaveBar('/performance/access/AccessView.jsp?scoreid=" + str2 + "')\">" + d + "</a></span>";
/*     */     
/*     */     }
/*     */     else {
/*     */       
/* 240 */       str1 = "<span class='status_txt'><a href=\"javascript:openFullWindowHaveBar('/performance/access/AccessView.jsp?resourceid=" + str3 + "&year=" + str4 + "&type1=" + str5 + "&type2=" + str6 + "')\">-</a></span>";
/*     */     } 
/*     */     
/* 243 */     return str1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getProgramAuditOperate(String paramString) {
/* 252 */     return "<span class='operatespan'><a class='a_op' href='javascript:doApprove(" + paramString + ")'>" + SystemEnv.getHtmlLabelName(142, ThreadVarLanguage.getLang()) + "</a>&nbsp;<a class='a_op' href='javascript:doReturn(" + paramString + ")'>" + SystemEnv.getHtmlLabelName(129829, ThreadVarLanguage.getLang()) + "</a></span>";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getScoreOperate(String paramString) {
/* 260 */     String[] arrayOfString = Util.TokenizerString2(paramString, "+");
/* 261 */     String str1 = arrayOfString[0];
/* 262 */     String str2 = arrayOfString[1];
/* 263 */     String str3 = arrayOfString[2];
/* 264 */     String str4 = "";
/* 265 */     String str5 = TimeUtil.getCurrentDateString();
/* 266 */     if (TimeUtil.dateInterval(str3, str5) > 0) {
/* 267 */       str4 = "<span class='expire'>" + SystemEnv.getHtmlLabelName(382750, ThreadVarLanguage.getLang()) + "</span";
/*     */     }
/* 269 */     else if ("0".equals(str2) || "2".equals(str2)) {
/* 270 */       str4 = "<span class='operatespan'><a href=\"javascript:openFullWindowHaveBar('/performance/access/AccessView.jsp?scoreid=" + str1 + "')\">" + SystemEnv.getHtmlLabelName(25622, ThreadVarLanguage.getLang()) + "</a></span>";
/* 271 */     } else if ("1".equals(str2)) {
/*     */       
/* 273 */       str4 = "<span class='operatespan'><a class='a_op' href='javascript:doApprove(" + str1 + ")'>" + SystemEnv.getHtmlLabelName(142, ThreadVarLanguage.getLang()) + "</a>&nbsp;<a class='a_op' href='javascript:doReturn(" + str1 + ")'>" + SystemEnv.getHtmlLabelName(129829, ThreadVarLanguage.getLang()) + "</a></span>";
/*     */     } 
/*     */     
/* 276 */     return str4;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getProgramStatus(String paramString) {
/* 284 */     if ("0".equals(paramString))
/* 285 */       return "" + SystemEnv.getHtmlLabelName(129828, ThreadVarLanguage.getLang()) + ""; 
/* 286 */     if ("1".equals(paramString))
/* 287 */       return "" + SystemEnv.getHtmlLabelName(19134, ThreadVarLanguage.getLang()) + ""; 
/* 288 */     if ("2".equals(paramString))
/* 289 */       return "" + SystemEnv.getHtmlLabelName(129829, ThreadVarLanguage.getLang()) + ""; 
/* 290 */     if ("3".equals(paramString)) {
/* 291 */       return "" + SystemEnv.getHtmlLabelName(508866, ThreadVarLanguage.getLang()) + "";
/*     */     }
/* 293 */     return "" + SystemEnv.getHtmlLabelName(15808, ThreadVarLanguage.getLang()) + "";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getScoreStatus(String paramString) {
/* 302 */     if ("0".equals(paramString))
/* 303 */       return "" + SystemEnv.getHtmlLabelName(508778, ThreadVarLanguage.getLang()) + ""; 
/* 304 */     if ("1".equals(paramString))
/* 305 */       return "" + SystemEnv.getHtmlLabelName(19134, ThreadVarLanguage.getLang()) + ""; 
/* 306 */     if ("2".equals(paramString))
/* 307 */       return "" + SystemEnv.getHtmlLabelName(129829, ThreadVarLanguage.getLang()) + ""; 
/* 308 */     if ("3".equals(paramString)) {
/* 309 */       return "" + SystemEnv.getHtmlLabelName(508866, ThreadVarLanguage.getLang()) + "";
/*     */     }
/* 311 */     return "";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getScoreStatus(String paramString, int paramInt) {
/* 320 */     if ("0".equals(paramString))
/* 321 */       return SystemEnv.getHtmlLabelName(508778, paramInt); 
/* 322 */     if ("1".equals(paramString))
/* 323 */       return SystemEnv.getHtmlLabelName(19134, paramInt); 
/* 324 */     if ("2".equals(paramString))
/* 325 */       return SystemEnv.getHtmlLabelName(236, paramInt); 
/* 326 */     if ("3".equals(paramString)) {
/* 327 */       return SystemEnv.getHtmlLabelName(508866, paramInt);
/*     */     }
/* 329 */     return "";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getScoreStatusDetail(String paramString1, String paramString2) throws Exception {
/* 338 */     StringBuffer stringBuffer = new StringBuffer();
/* 339 */     String str1 = Util.null2String(paramString1).trim();
/* 340 */     String[] arrayOfString = Util.TokenizerString2(paramString2, "+");
/* 341 */     String str2 = Util.null2String(arrayOfString[0]).trim();
/* 342 */     String str3 = Util.null2String(arrayOfString[1]).trim();
/* 343 */     String str4 = Util.null2String(arrayOfString[2]).trim();
/* 344 */     String str5 = Util.null2String(arrayOfString[3]).trim();
/* 345 */     String str6 = Util.null2String(arrayOfString[4]).trim();
/* 346 */     String str7 = Util.null2String(arrayOfString[5]).trim();
/* 347 */     String str8 = "/performance/access/AccessView.jsp";
/* 348 */     if (str1.equals("")) {
/* 349 */       String str9 = "";
/* 350 */       String str10 = "";
/* 351 */       String str11 = Util.null2String(arrayOfString[6]).trim();
/* 352 */       String str12 = Util.null2String(arrayOfString[7]).trim();
/* 353 */       String str13 = Util.null2String(arrayOfString[8]).trim();
/*     */       
/* 355 */       str8 = str8 + "?resourceid=" + str7 + "&year=" + str11 + "&type1=" + str12 + "&type2=" + str13;
/*     */       
/* 357 */       int i = 0;
/* 358 */       int j = 0;
/* 359 */       int k = 0;
/* 360 */       int m = 0;
/* 361 */       int n = 0;
/* 362 */       int i1 = 0;
/* 363 */       int i2 = 0;
/* 364 */       int i3 = 0;
/* 365 */       RecordSet recordSet = new RecordSet();
/* 366 */       recordSet.executeSql("select * from GP_BaseSetting where resourceid=" + this.rc.getSubCompanyID(str7) + " and resourcetype=2");
/* 367 */       if (recordSet.next()) {
/* 368 */         i = Util.getIntValue(recordSet.getString("fstarttype"), 0);
/* 369 */         j = Util.getIntValue(recordSet.getString("fstartdays"), 0);
/* 370 */         k = Util.getIntValue(recordSet.getString("hstarttype"), 0);
/* 371 */         m = Util.getIntValue(recordSet.getString("hstartdays"), 0);
/* 372 */         n = Util.getIntValue(recordSet.getString("qstarttype"), 0);
/* 373 */         i1 = Util.getIntValue(recordSet.getString("qstartdays"), 0);
/* 374 */         i2 = Util.getIntValue(recordSet.getString("mstarttype"), 0);
/* 375 */         i3 = Util.getIntValue(recordSet.getString("mstartdays"), 0);
/*     */       } 
/* 377 */       if (str12.equals("1")) {
/* 378 */         str9 = TimeUtil.getYearMonthEndDay(Integer.parseInt(str11), Integer.parseInt(str13));
/* 379 */         str10 = TimeUtil.dateAdd(str9, i3 * i2);
/* 380 */       } else if (str12.equals("2")) {
/* 381 */         if (str13.equals("1")) str9 = TimeUtil.getYearMonthEndDay(Integer.parseInt(str11), 3); 
/* 382 */         if (str13.equals("2")) str9 = TimeUtil.getYearMonthEndDay(Integer.parseInt(str11), 6); 
/* 383 */         if (str13.equals("3")) str9 = TimeUtil.getYearMonthEndDay(Integer.parseInt(str11), 9); 
/* 384 */         if (str13.equals("4")) str9 = TimeUtil.getYearMonthEndDay(Integer.parseInt(str11), 12); 
/* 385 */         str10 = TimeUtil.dateAdd(str9, i1 * n);
/* 386 */       } else if (str12.equals("3")) {
/* 387 */         str9 = TimeUtil.getYearMonthEndDay(Integer.parseInt(str11), 6);
/* 388 */         str10 = TimeUtil.dateAdd(str9, m * k);
/* 389 */       } else if (str12.equals("4")) {
/* 390 */         str9 = TimeUtil.getYearMonthEndDay(Integer.parseInt(str11), 12);
/* 391 */         str10 = TimeUtil.dateAdd(str9, j * i);
/*     */       } 
/*     */ 
/*     */       
/* 395 */       recordSet.executeSql("select count(id) from GP_AccessProgram where startdate<='" + str10 + "' and programtype=" + str12 + " and userid=" + str7 + " and status=3 ");
/* 396 */       if (recordSet.next() && recordSet.getInt(1) > 0) {
/* 397 */         String str = TimeUtil.getCurrentDateString();
/* 398 */         if (TimeUtil.dateInterval(str, str10) > 0) {
/* 399 */           stringBuffer.append("<span class='status status2' title='" + SystemEnv.getHtmlLabelName(509132, ThreadVarLanguage.getLang()) + "'></span><span class='status_txt'>" + SystemEnv.getHtmlLabelName(10003628, ThreadVarLanguage.getLang()) + "" + str10 + "</span>");
/*     */         } else {
/* 401 */           stringBuffer.append("<span class='status status8' title='" + SystemEnv.getHtmlLabelName(508610, ThreadVarLanguage.getLang()) + "'></span>");
/*     */         } 
/*     */       } else {
/* 404 */         stringBuffer.append("<span class='status status1' title='" + SystemEnv.getHtmlLabelName(509134, ThreadVarLanguage.getLang()) + "'></span>");
/* 405 */         str8 = "/performance/program/ProgramView.jsp?resourceid=" + str7 + "&programtype=" + str6;
/*     */       }
/*     */     
/*     */     } else {
/*     */       
/* 410 */       String str = TimeUtil.getCurrentDateString();
/* 411 */       if (TimeUtil.dateInterval(str, str4) > 0) {
/* 412 */         stringBuffer.append("<span class='status status2' title='" + SystemEnv.getHtmlLabelName(509132, ThreadVarLanguage.getLang()) + "'></span><span class='status_txt'>" + SystemEnv.getHtmlLabelName(10003628, ThreadVarLanguage.getLang()) + "" + str4 + "</span>");
/* 413 */       } else if (str2.equals("3")) {
/* 414 */         stringBuffer.append("<span class='status status3' title='" + SystemEnv.getHtmlLabelName(509135, ThreadVarLanguage.getLang()) + "'></span><span class='status_txt'>" + SystemEnv.getHtmlLabelName(555, ThreadVarLanguage.getLang()) + "</span>");
/*     */       } else {
/* 416 */         if ("0".equals(str2) || "2".equals(str2)) {
/* 417 */           if (TimeUtil.dateInterval(str5, str) > 0) {
/* 418 */             stringBuffer.append("<span class='status status6' title='" + SystemEnv.getHtmlLabelName(509137, ThreadVarLanguage.getLang()) + "'></span>");
/*     */           }
/* 420 */           else if ("0".equals(str2)) {
/* 421 */             stringBuffer.append("<span class='status status4' title='" + SystemEnv.getHtmlLabelName(508847, ThreadVarLanguage.getLang()) + "'></span>");
/*     */           } else {
/* 423 */             stringBuffer.append("<span class='status status7' title='" + SystemEnv.getHtmlLabelName(509138, ThreadVarLanguage.getLang()) + "'></span>");
/*     */           } 
/*     */           
/* 426 */           stringBuffer.append("<span class='status_txt'>" + SystemEnv.getHtmlLabelName(10003629, ThreadVarLanguage.getLang()) + "" + getPerson(str3));
/* 427 */         } else if ("1".equals(str2)) {
/* 428 */           RecordSet recordSet = new RecordSet();
/* 429 */           recordSet.executeSql("select userid from GP_AccessScoreAudit where scoreid=" + str1);
/* 430 */           String str9 = "";
/* 431 */           while (recordSet.next()) {
/* 432 */             str9 = str9 + recordSet.getString(1) + ",";
/*     */           }
/* 434 */           if (TimeUtil.dateInterval(str5, str) > 0) {
/* 435 */             stringBuffer.append("<span class='status status6' title='" + SystemEnv.getHtmlLabelName(509137, ThreadVarLanguage.getLang()) + "'></span>");
/*     */           } else {
/* 437 */             stringBuffer.append("<span class='status status5' title='" + SystemEnv.getHtmlLabelName(509139, ThreadVarLanguage.getLang()) + "'></span>");
/*     */           } 
/* 439 */           stringBuffer.append("<span class='status_txt'>" + SystemEnv.getHtmlLabelName(10003630, ThreadVarLanguage.getLang()) + "" + getPerson(str9));
/*     */         } 
/* 441 */         stringBuffer.append(" " + SystemEnv.getHtmlLabelName(10003631, ThreadVarLanguage.getLang()) + "" + str5 + "</span>");
/*     */       } 
/* 443 */       str8 = str8 + "?scoreid=" + str1;
/*     */     } 
/* 445 */     stringBuffer.append("<span class='operatespan'><a href=\"javascript:openFullWindowHaveBar('" + str8 + "')\">" + SystemEnv.getHtmlLabelName(367, ThreadVarLanguage.getLang()) + "</a></span>");
/* 446 */     return stringBuffer.toString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getMobileScoreStatusDetail(String paramString1, String paramString2) throws Exception {
/* 454 */     StringBuffer stringBuffer1 = new StringBuffer();
/* 455 */     String str1 = Util.null2String(paramString1).trim();
/* 456 */     String[] arrayOfString = Util.TokenizerString2(paramString2, "+");
/* 457 */     String str2 = Util.null2String(arrayOfString[0]).trim();
/* 458 */     String str3 = Util.null2String(arrayOfString[1]).trim();
/* 459 */     String str4 = Util.null2String(arrayOfString[2]).trim();
/* 460 */     String str5 = Util.null2String(arrayOfString[3]).trim();
/* 461 */     String str6 = Util.null2String(arrayOfString[4]).trim();
/* 462 */     String str7 = Util.null2String(arrayOfString[5]).trim();
/* 463 */     String str8 = "AccessView.jsp";
/* 464 */     StringBuffer stringBuffer2 = new StringBuffer();
/* 465 */     StringBuffer stringBuffer3 = new StringBuffer();
/* 466 */     StringBuffer stringBuffer4 = new StringBuffer();
/* 467 */     if (str1.equals("")) {
/* 468 */       String str9 = "";
/* 469 */       String str10 = "";
/* 470 */       String str11 = Util.null2String(arrayOfString[6]).trim();
/* 471 */       String str12 = Util.null2String(arrayOfString[7]).trim();
/* 472 */       String str13 = Util.null2String(arrayOfString[8]).trim();
/*     */       
/* 474 */       str8 = str8 + "?resourceid=" + str7 + "&year=" + str11 + "&type1=" + str12 + "&type2=" + str13;
/*     */       
/* 476 */       int i = 0;
/* 477 */       int j = 0;
/* 478 */       int k = 0;
/* 479 */       int m = 0;
/* 480 */       int n = 0;
/* 481 */       int i1 = 0;
/* 482 */       int i2 = 0;
/* 483 */       int i3 = 0;
/* 484 */       RecordSet recordSet = new RecordSet();
/* 485 */       recordSet.executeSql("select * from GP_BaseSetting where resourceid=" + this.rc.getSubCompanyID(str7) + " and resourcetype=2");
/* 486 */       if (recordSet.next()) {
/* 487 */         i = Util.getIntValue(recordSet.getString("fstarttype"), 0);
/* 488 */         j = Util.getIntValue(recordSet.getString("fstartdays"), 0);
/* 489 */         k = Util.getIntValue(recordSet.getString("hstarttype"), 0);
/* 490 */         m = Util.getIntValue(recordSet.getString("hstartdays"), 0);
/* 491 */         n = Util.getIntValue(recordSet.getString("qstarttype"), 0);
/* 492 */         i1 = Util.getIntValue(recordSet.getString("qstartdays"), 0);
/* 493 */         i2 = Util.getIntValue(recordSet.getString("mstarttype"), 0);
/* 494 */         i3 = Util.getIntValue(recordSet.getString("mstartdays"), 0);
/*     */       } 
/* 496 */       if (str12.equals("1")) {
/* 497 */         str9 = TimeUtil.getYearMonthEndDay(Integer.parseInt(str11), Integer.parseInt(str13));
/* 498 */         str10 = TimeUtil.dateAdd(str9, i3 * i2);
/* 499 */       } else if (str12.equals("2")) {
/* 500 */         if (str13.equals("1")) str9 = TimeUtil.getYearMonthEndDay(Integer.parseInt(str11), 3); 
/* 501 */         if (str13.equals("2")) str9 = TimeUtil.getYearMonthEndDay(Integer.parseInt(str11), 6); 
/* 502 */         if (str13.equals("3")) str9 = TimeUtil.getYearMonthEndDay(Integer.parseInt(str11), 9); 
/* 503 */         if (str13.equals("4")) str9 = TimeUtil.getYearMonthEndDay(Integer.parseInt(str11), 12); 
/* 504 */         str10 = TimeUtil.dateAdd(str9, i1 * n);
/* 505 */       } else if (str12.equals("3")) {
/* 506 */         str9 = TimeUtil.getYearMonthEndDay(Integer.parseInt(str11), 6);
/* 507 */         str10 = TimeUtil.dateAdd(str9, m * k);
/* 508 */       } else if (str12.equals("4")) {
/* 509 */         str9 = TimeUtil.getYearMonthEndDay(Integer.parseInt(str11), 12);
/* 510 */         str10 = TimeUtil.dateAdd(str9, j * i);
/*     */       } 
/*     */ 
/*     */       
/* 514 */       recordSet.executeSql("select count(id) from GP_AccessProgram where startdate<='" + str10 + "' and programtype=" + str12 + " and userid=" + str7 + " and status=3 ");
/* 515 */       if (recordSet.next() && recordSet.getInt(1) > 0) {
/* 516 */         String str = TimeUtil.getCurrentDateString();
/* 517 */         if (TimeUtil.dateInterval(str, str10) > 0) {
/* 518 */           stringBuffer2.append("" + SystemEnv.getHtmlLabelName(1979, ThreadVarLanguage.getLang()) + "");
/* 519 */           stringBuffer4.append("2475D1");
/*     */         } else {
/* 521 */           stringBuffer2.append("" + SystemEnv.getHtmlLabelName(508873, ThreadVarLanguage.getLang()) + "");
/* 522 */           stringBuffer4.append("000000");
/*     */         } 
/*     */       } else {
/* 525 */         stringBuffer2.append("" + SystemEnv.getHtmlLabelName(15808, ThreadVarLanguage.getLang()) + "");
/* 526 */         stringBuffer4.append("D81D19");
/*     */       } 
/*     */     } else {
/*     */       
/* 530 */       String str = TimeUtil.getCurrentDateString();
/* 531 */       if (TimeUtil.dateInterval(str, str4) > 0) {
/* 532 */         stringBuffer2.append("" + SystemEnv.getHtmlLabelName(1979, ThreadVarLanguage.getLang()) + "");
/* 533 */         stringBuffer4.append("2475D1");
/* 534 */       } else if (str2.equals("3")) {
/* 535 */         stringBuffer2.append("" + SystemEnv.getHtmlLabelName(1961, ThreadVarLanguage.getLang()) + "");
/* 536 */         stringBuffer4.append("49D732");
/*     */       } else {
/* 538 */         if ("0".equals(str2) || "2".equals(str2)) {
/* 539 */           if (TimeUtil.dateInterval(str5, str) > 0) {
/* 540 */             stringBuffer2.append("" + SystemEnv.getHtmlLabelName(382750, ThreadVarLanguage.getLang()) + "");
/* 541 */             stringBuffer4.append("000000");
/*     */           }
/* 543 */           else if ("0".equals(str2)) {
/* 544 */             stringBuffer2.append("" + SystemEnv.getHtmlLabelName(508778, ThreadVarLanguage.getLang()) + "");
/* 545 */             stringBuffer4.append("EAA81C");
/*     */           } else {
/* 547 */             stringBuffer2.append("" + SystemEnv.getHtmlLabelName(129829, ThreadVarLanguage.getLang()) + "");
/* 548 */             stringBuffer4.append("B916E8");
/*     */           } 
/*     */           
/* 551 */           stringBuffer3.append("" + SystemEnv.getHtmlLabelName(10003629, ThreadVarLanguage.getLang()) + "" + this.rc.getResourcename(str3));
/* 552 */         } else if ("1".equals(str2)) {
/* 553 */           RecordSet recordSet = new RecordSet();
/* 554 */           recordSet.executeSql("select userid from GP_AccessScoreAudit where scoreid=" + str1);
/* 555 */           String str9 = "";
/* 556 */           while (recordSet.next()) {
/* 557 */             str9 = str9 + recordSet.getString(1) + ",";
/*     */           }
/* 559 */           if (TimeUtil.dateInterval(str5, str) > 0) {
/* 560 */             stringBuffer2.append("" + SystemEnv.getHtmlLabelName(382750, ThreadVarLanguage.getLang()) + "");
/* 561 */             stringBuffer4.append("000000");
/*     */           } else {
/* 563 */             stringBuffer2.append("" + SystemEnv.getHtmlLabelName(19134, ThreadVarLanguage.getLang()) + "");
/* 564 */             stringBuffer4.append("5F0E03");
/*     */           } 
/* 566 */           String str10 = "";
/* 567 */           if (str9 != null && !"".equals(str9)) {
/* 568 */             ArrayList<String> arrayList = Util.TokenizerString(str9, ",");
/* 569 */             for (byte b = 0; b < arrayList.size(); b++) {
/* 570 */               str10 = str10 + this.rc.getResourcename(arrayList.get(b)) + " ";
/*     */             }
/*     */           } 
/* 573 */           stringBuffer3.append("  " + SystemEnv.getHtmlLabelName(10003630, ThreadVarLanguage.getLang()) + "" + str10);
/*     */         } 
/* 575 */         stringBuffer3.append(" " + SystemEnv.getHtmlLabelName(10003631, ThreadVarLanguage.getLang()) + "" + str5);
/*     */       } 
/* 577 */       str8 = str8 + "?scoreid=" + str1;
/*     */     } 
/* 579 */     stringBuffer1.append(stringBuffer2 + "," + str8 + "," + stringBuffer4 + "," + stringBuffer3);
/* 580 */     return stringBuffer1.toString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getProgramStatusDetail(String paramString1, String paramString2) throws Exception {
/* 590 */     String str1 = "";
/* 591 */     String[] arrayOfString = Util.TokenizerString2(paramString2, "+");
/* 592 */     int i = Util.getIntValue(arrayOfString[0], 1);
/* 593 */     int j = Util.getIntValue(arrayOfString[1], 1);
/* 594 */     int k = Util.getIntValue(arrayOfString[2], 1);
/* 595 */     int m = Util.getIntValue(arrayOfString[3], 1);
/*     */     
/* 597 */     int n = Util.getIntValue(arrayOfString[4], 1);
/* 598 */     int i1 = Util.getIntValue(arrayOfString[5], 0);
/*     */     
/* 600 */     int i2 = Util.getIntValue(arrayOfString[6], 1);
/* 601 */     int i3 = Util.getIntValue(arrayOfString[7], 0);
/*     */     
/* 603 */     int i4 = Util.getIntValue(arrayOfString[8], 1);
/* 604 */     int i5 = Util.getIntValue(arrayOfString[9], 0);
/*     */     
/* 606 */     int i6 = Util.getIntValue(arrayOfString[10], 1);
/* 607 */     int i7 = Util.getIntValue(arrayOfString[11], 0);
/*     */     
/* 609 */     String str2 = "";
/* 610 */     String str3 = "";
/* 611 */     int i8 = Util.getIntValue(TimeUtil.getCurrentDateString().substring(0, 4));
/* 612 */     int i9 = Util.getIntValue(TimeUtil.getCurrentDateString().substring(5, 7));
/* 613 */     int i10 = Util.getIntValue(TimeUtil.getCurrentSeason());
/* 614 */     RecordSet recordSet = new RecordSet();
/* 615 */     String str4 = "";
/* 616 */     String str5 = "";
/* 617 */     for (byte b = 4; b > 0; b--) {
/* 618 */       boolean bool = false;
/* 619 */       if (b == 4 && i == 1) {
/* 620 */         str1 = str1 + "<span class='status_txt'>" + SystemEnv.getHtmlLabelName(10003632, ThreadVarLanguage.getLang()) + "</span>";
/* 621 */         str2 = TimeUtil.getYearMonthEndDay(i8, 12);
/* 622 */         str3 = TimeUtil.dateAdd(str2, n * i1);
/* 623 */         bool = true;
/* 624 */       } else if (b == 3 && j == 1) {
/* 625 */         str1 = str1 + "<span class='status_txt'>" + SystemEnv.getHtmlLabelName(10003633, ThreadVarLanguage.getLang()) + "</span>";
/* 626 */         str2 = TimeUtil.getYearMonthEndDay(i8, 6);
/* 627 */         str3 = TimeUtil.dateAdd(str2, i2 * i3);
/* 628 */         bool = true;
/* 629 */       } else if (b == 2 && k == 1) {
/* 630 */         str1 = str1 + "<span class='status_txt'>" + SystemEnv.getHtmlLabelName(10003634, ThreadVarLanguage.getLang()) + "</span>";
/* 631 */         if (i10 == 1) str2 = TimeUtil.getYearMonthEndDay(i8, 3); 
/* 632 */         if (i10 == 2) str2 = TimeUtil.getYearMonthEndDay(i8, 6); 
/* 633 */         if (i10 == 3) str2 = TimeUtil.getYearMonthEndDay(i8, 9); 
/* 634 */         if (i10 == 4) str2 = TimeUtil.getYearMonthEndDay(i8, 12); 
/* 635 */         str3 = TimeUtil.dateAdd(str2, i4 * i5);
/* 636 */         bool = true;
/* 637 */       } else if (b == 1 && m == 1) {
/* 638 */         str1 = str1 + "<span class='status_txt'>" + SystemEnv.getHtmlLabelName(10003635, ThreadVarLanguage.getLang()) + "</span>";
/* 639 */         str2 = TimeUtil.getYearMonthEndDay(i8, i9);
/* 640 */         str3 = TimeUtil.dateAdd(str2, i6 * i7);
/* 641 */         bool = true;
/*     */       } 
/* 643 */       if (bool) {
/* 644 */         String str = "";
/* 645 */         if ("oracle".equals(recordSet.getDBType())) {
/* 646 */           str = "select * from (select id,status from GP_AccessProgram where startdate<='" + str3 + "' and programtype=" + b + " and userid=" + paramString1 + " order by startdate desc,id desc) t where rownum=1";
/* 647 */         } else if ("mysql".equals(recordSet.getDBType())) {
/* 648 */           str = "select id,status from GP_AccessProgram where startdate<='" + str3 + "' and programtype=" + b + " and userid=" + paramString1 + " order by startdate desc,id desc limit 1";
/*     */         }
/* 650 */         else if ("postgresql".equals(recordSet.getDBType())) {
/* 651 */           str = "select id,status from GP_AccessProgram where startdate<='" + str3 + "' and programtype=" + b + " and userid=" + paramString1 + " order by startdate desc,id desc limit 1";
/*     */         } else {
/*     */           
/* 654 */           str = "select top 1 id,status from GP_AccessProgram where startdate<='" + str3 + "' and programtype=" + b + " and userid=" + paramString1 + " order by startdate desc,id desc";
/*     */         } 
/* 656 */         recordSet.executeSql(str);
/* 657 */         if (recordSet.next()) {
/* 658 */           str4 = Util.null2String(recordSet.getString("id"));
/* 659 */           str5 = Util.null2String(recordSet.getString("status"));
/* 660 */           str1 = str1 + "<span class='status status" + str5 + " status_link' onclick=\"openFullWindowHaveBar('/performance/program/ProgramView.jsp?programid=" + str4 + "')\"></span>";
/*     */         } else {
/* 662 */           str1 = str1 + "<span class='status status4 status_link' onclick=\"openFullWindowHaveBar('/performance/program/ProgramView.jsp?resourceid=" + paramString1 + "&programtype=" + b + "')\"></span>";
/*     */         } 
/*     */       } 
/*     */     } 
/* 666 */     return str1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getYearType(String paramString) {
/* 674 */     String[] arrayOfString = Util.TokenizerString2(paramString, "+");
/* 675 */     String str1 = arrayOfString[0];
/* 676 */     String str2 = arrayOfString[1];
/* 677 */     String str3 = arrayOfString[2];
/* 678 */     String str4 = str1 + "年";
/* 679 */     if ("1".equals(str2)) {
/* 680 */       str4 = str4 + str3 + "月";
/* 681 */     } else if ("2".equals(str2)) {
/* 682 */       str4 = str4 + str3 + "" + SystemEnv.getHtmlLabelName(17495, ThreadVarLanguage.getLang()) + "";
/* 683 */     } else if ("3".equals(str2)) {
/* 684 */       str4 = str4 + "" + SystemEnv.getHtmlLabelName(19483, ThreadVarLanguage.getLang()) + "";
/* 685 */     } else if ("4".equals(str2)) {
/* 686 */       str4 = str4 + "" + SystemEnv.getHtmlLabelName(17138, ThreadVarLanguage.getLang()) + "";
/*     */     } 
/* 688 */     return str4;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/gp/util/TransUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */