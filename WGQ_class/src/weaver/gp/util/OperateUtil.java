/*     */ package weaver.gp.util;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.ThreadVarLanguage;
/*     */ import weaver.general.TimeUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ public class OperateUtil extends BaseBean {
/*  11 */   private ResourceComInfo rc = null;
/*     */ 
/*     */   
/*     */   public OperateUtil() {
/*     */     try {
/*  16 */       this.rc = new ResourceComInfo();
/*  17 */     } catch (Exception exception) {}
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void addProgramLog(String paramString1, String paramString2, int paramInt) {
/*  28 */     RecordSet recordSet = new RecordSet();
/*     */     
/*  30 */     String str = "insert into GP_AccessProgramLog (programid,operator,operatedate,operatetime,operatetype) values(" + paramString2 + "," + paramString1 + ",'" + TimeUtil.getCurrentDateString() + "','" + TimeUtil.getOnlyCurrentTimeString() + "'," + paramInt + ")";
/*     */     
/*  32 */     recordSet.executeSql(str);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void approveProgram(String paramString1, String paramString2) {
/*  40 */     RecordSet recordSet = new RecordSet();
/*  41 */     recordSet.executeSql("delete from GP_AccessProgramAudit where programid=" + paramString1 + " and userid=" + paramString2);
/*  42 */     addProgramLog(paramString2, paramString1, 5);
/*  43 */     recordSet.executeSql("select count(id) from GP_AccessProgramAudit where programid=" + paramString1);
/*  44 */     if (recordSet.next() && recordSet.getInt(1) == 0) {
/*     */       
/*  46 */       recordSet.executeSql("update GP_AccessProgram set status=3 where id=" + paramString1);
/*     */       
/*  48 */       updateScoreByProgram(paramString1);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void returnProgram(String paramString1, String paramString2) {
/*  57 */     RecordSet recordSet = new RecordSet();
/*  58 */     recordSet.executeSql("delete from GP_AccessProgramAudit where programid=" + paramString1);
/*  59 */     recordSet.executeSql("update GP_AccessProgram set status=2 where id=" + paramString1);
/*  60 */     addProgramLog(paramString2, paramString1, 4);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void addScoreLog(String paramString1, String paramString2, int paramInt) {
/*  70 */     RecordSet recordSet = new RecordSet();
/*     */     
/*  72 */     String str = "insert into GP_AccessScoreLog (scoreid,operator,operatedate,operatetime,operatetype) values(" + paramString2 + "," + paramString1 + ",'" + TimeUtil.getCurrentDateString() + "','" + TimeUtil.getOnlyCurrentTimeString() + "'," + paramInt + ")";
/*     */     
/*  74 */     recordSet.executeSql(str);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void approveScore(String paramString1, String paramString2) {
/*  83 */     RecordSet recordSet = new RecordSet();
/*  84 */     recordSet.executeSql("delete from GP_AccessScoreAudit where scoreid=" + paramString1 + " and userid=" + paramString2);
/*  85 */     addScoreLog(paramString2, paramString1, 5);
/*  86 */     recordSet.executeSql("select count(id) from GP_AccessScoreAudit where scoreid=" + paramString1);
/*  87 */     if (recordSet.next() && recordSet.getInt(1) == 0) {
/*  88 */       recordSet.executeSql("update GP_AccessScore set status=3,finishdate='" + TimeUtil.getCurrentDateString() + "',finishtime='" + TimeUtil.getOnlyCurrentTimeString() + "' where id=" + paramString1);
/*  89 */       updateTarget(paramString1);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void updateTarget(String paramString) {
/*  97 */     if (!"".equals(paramString)) {
/*  98 */       RecordSet recordSet1 = new RecordSet();
/*  99 */       RecordSet recordSet2 = new RecordSet();
/* 100 */       recordSet1.executeSql("select t2.id from GP_AccessScore t1,GP_AccessScore t2  where t1.userid=t2.userid and t1.type1=t2.type1 and t1.id=" + paramString + " and ( ((t1.type1=3 or t1.type1=4) and t1.year=t2.year-1) or ((t1.type1=1 or t1.type1=2) and t1.year=t2.year and t1.type2=t2.type2-1) or (t1.type1=1 and t1.year=t2.year-1 and t1.type2=12 and t2.type2=1) or (t1.type1=2 and t1.year=t2.year-1 and t1.type2=4 and t2.type2=1))");
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 106 */       if (recordSet1.next()) {
/* 107 */         String str1 = Util.null2String(recordSet1.getString(1));
/* 108 */         recordSet1.executeSql("select accessitemid,next1,next2,description from GP_AccessScoreDetail where scoreid=" + paramString);
/* 109 */         String str2 = "";
/* 110 */         String str3 = "";
/* 111 */         double d = 0.0D;
/* 112 */         String str4 = "";
/* 113 */         while (recordSet1.next()) {
/* 114 */           str2 = Util.null2String(recordSet1.getString("accessitemid"));
/* 115 */           str3 = Util.null2String(recordSet1.getString("description"));
/* 116 */           d = Util.getDoubleValue(recordSet1.getString("next1"), 0.0D);
/* 117 */           str4 = Util.null2String(recordSet1.getString("next2"));
/* 118 */           recordSet2.executeSql("update GP_AccessScoreDetail set target1='" + d + "',target2='" + str4 + "',next1='" + d + "',next2='" + str4 + "' where scoreid=" + str1 + " and accessitemid=" + str2 + " and description='" + str3 + "'");
/*     */         } 
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void returnScore(String paramString1, String paramString2) {
/* 129 */     RecordSet recordSet = new RecordSet();
/* 130 */     String str = "";
/* 131 */     recordSet.executeSql("select userid from GP_AccessScoreCheck where exeorder=0 and scoreid=" + paramString1);
/* 132 */     if (recordSet.next()) {
/* 133 */       str = Util.null2String(recordSet.getString(1));
/*     */     }
/* 135 */     recordSet.executeSql("delete from GP_AccessScoreAudit where scoreid=" + paramString1);
/* 136 */     recordSet.executeSql("update GP_AccessScore set status=2,isfirst=1,operator=" + str + " where id=" + paramString1);
/* 137 */     recordSet.executeSql("update GP_AccessScoreCheck set status=1 where exeorder=0 and scoreid=" + paramString1);
/* 138 */     recordSet.executeSql("update GP_AccessScoreCheck set status=0 where exeorder>0 and scoreid=" + paramString1);
/* 139 */     addScoreLog(paramString2, paramString1, 4);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String resetScore(String paramString1, String paramString2) {
/* 147 */     RecordSet recordSet = new RecordSet();
/* 148 */     String str = "";
/* 149 */     recordSet.executeSql("select userid,year,type1,type2,startdate,enddate,isrescore from GP_AccessScore where id=" + paramString1);
/* 150 */     if (recordSet.next()) {
/* 151 */       String str1 = Util.null2String(recordSet.getString("userid"));
/* 152 */       int i = Util.getIntValue(recordSet.getString("year"));
/* 153 */       int j = Util.getIntValue(recordSet.getString("type1"));
/* 154 */       int k = Util.getIntValue(recordSet.getString("type2"));
/* 155 */       int m = Util.getIntValue(recordSet.getString("isrescore"), 0);
/* 156 */       String str2 = Util.null2String(recordSet.getString("startdate"));
/* 157 */       String str3 = Util.null2String(recordSet.getString("enddate"));
/* 158 */       String str4 = "";
/* 159 */       if ("oracle".equals(recordSet.getDBType())) {
/* 160 */         str4 = "select * from (select id,auditids,remark from GP_AccessProgram where startdate<='" + str2 + "' and programtype=" + j + " and userid=" + str1 + " AND status!=99 order by startdate desc,id desc) t where rownum=1";
/* 161 */       } else if ("mysql".equals(recordSet.getDBType())) {
/* 162 */         str4 = "select id,auditids,remark from GP_AccessProgram where startdate<='" + str2 + "' and programtype=" + j + " and userid=" + str1 + " AND status!=99 order by startdate desc,id desc limit 1";
/*     */       }
/* 164 */       else if ("postgresql".equals(recordSet.getDBType())) {
/* 165 */         str4 = "select id,auditids,remark from GP_AccessProgram where startdate<='" + str2 + "' and programtype=" + j + " and userid=" + str1 + " AND status!=99 order by startdate desc,id desc limit 1";
/*     */       } else {
/*     */         
/* 168 */         str4 = "select top 1 id,auditids,remark from GP_AccessProgram where startdate<='" + str2 + "' and programtype=" + j + " and userid=" + str1 + " AND status!=99 order by startdate desc,id desc";
/*     */       } 
/* 170 */       recordSet.executeSql(str4);
/* 171 */       if (recordSet.next()) {
/* 172 */         String str5 = Util.null2String(recordSet.getString("id"));
/* 173 */         String str6 = Util.null2String(recordSet.getString("auditids"));
/* 174 */         String str7 = Util.null2String(recordSet.getString("remark"));
/* 175 */         recordSet.executeSql("delete from GP_AccessScore where id=" + paramString1);
/* 176 */         recordSet.executeSql("delete from GP_AccessScoreDetail where scoreid=" + paramString1);
/* 177 */         recordSet.executeSql("delete from GP_AccessScoreCheck where scoreid=" + paramString1);
/* 178 */         recordSet.executeSql("delete from GP_AccessScoreCheckDetail where scoreid=" + paramString1);
/* 179 */         str = initData(str1, str5, i, j, k, str2, str3, str6, str7);
/* 180 */         recordSet.executeSql("update GP_AccessScoreLog set scoreid=" + str + " where scoreid=" + paramString1);
/* 181 */         recordSet.executeSql("update GP_AccessScoreExchange set scoreid=" + str + " where scoreid=" + paramString1);
/* 182 */         if (m == 1) recordSet.executeSql("update GP_AccessScore set isrescore=1 where id=" + str); 
/* 183 */         addScoreLog(paramString2, str, 1);
/*     */       } 
/*     */     } 
/* 186 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void updateScoreByProgram(String paramString) {
/* 193 */     RecordSet recordSet = new RecordSet();
/* 194 */     recordSet.executeSql("select userid,programtype,startdate,auditids,remark from GP_AccessProgram where id=" + paramString);
/* 195 */     if (recordSet.next()) {
/* 196 */       String str1 = TimeUtil.getCurrentDateString();
/* 197 */       String str2 = "";
/* 198 */       String str3 = "";
/* 199 */       String str4 = "";
/* 200 */       String str5 = "";
/* 201 */       String str6 = "";
/* 202 */       String str7 = "";
/* 203 */       int i = Util.getIntValue(TimeUtil.getCurrentDateString().substring(5, 7));
/* 204 */       int j = Util.getIntValue(TimeUtil.getCurrentSeason());
/* 205 */       int k = Util.getIntValue(TimeUtil.getCurrentDateString().substring(0, 4));
/* 206 */       String str8 = Util.null2String(recordSet.getString("startdate"));
/* 207 */       String str9 = Util.null2String(recordSet.getString("auditids"));
/* 208 */       String str10 = Util.null2String(recordSet.getString("remark"));
/* 209 */       String str11 = Util.null2String(recordSet.getString("userid"));
/* 210 */       String str12 = this.rc.getSubCompanyID(str11);
/* 211 */       int m = Util.getIntValue(recordSet.getString("programtype"), 0);
/* 212 */       int n = 0;
/* 213 */       int i1 = k;
/* 214 */       int i2 = 0;
/* 215 */       String str13 = "";
/* 216 */       if (m == 1) {
/* 217 */         n = i;
/* 218 */         if (n == 1) {
/* 219 */           i1 = k - 1;
/* 220 */           i2 = 12;
/*     */         } else {
/* 222 */           i2 = n - 1;
/*     */         } 
/*     */         try {
/* 225 */           str2 = TimeUtil.getYearMonthEndDay(i1, i2);
/* 226 */           str3 = TimeUtil.getYearMonthEndDay(k, i);
/* 227 */         } catch (Exception exception) {}
/* 228 */         str13 = "select mstarttype,mstartdays,mendtype,menddays from GP_BaseSetting where resourcetype=2 and resourceid=" + str12 + " and ismonth=1";
/* 229 */       } else if (m == 2) {
/* 230 */         n = j;
/* 231 */         if (n == 1) {
/* 232 */           i1 = k - 1;
/* 233 */           i2 = 4;
/*     */         } else {
/* 235 */           i2 = n - 1;
/*     */         } 
/*     */         try {
/* 238 */           if (i2 == 1) str2 = TimeUtil.getYearMonthEndDay(i1, 3); 
/* 239 */           if (i2 == 2) str2 = TimeUtil.getYearMonthEndDay(i1, 6); 
/* 240 */           if (i2 == 3) str2 = TimeUtil.getYearMonthEndDay(i1, 9); 
/* 241 */           if (i2 == 4) str2 = TimeUtil.getYearMonthEndDay(i1, 12);
/*     */           
/* 243 */           if (j == 1) str3 = TimeUtil.getYearMonthEndDay(k, 3); 
/* 244 */           if (j == 2) str3 = TimeUtil.getYearMonthEndDay(k, 6); 
/* 245 */           if (j == 3) str3 = TimeUtil.getYearMonthEndDay(k, 9); 
/* 246 */           if (j == 4) str3 = TimeUtil.getYearMonthEndDay(k, 12); 
/* 247 */         } catch (Exception exception) {}
/* 248 */         str13 = "select qstarttype,qstartdays,qendtype,qenddays from GP_BaseSetting where resourcetype=2 and resourceid=" + str12 + " and isquarter=1";
/* 249 */       } else if (m == 3) {
/* 250 */         i1 = k - 1;
/*     */         try {
/* 252 */           str2 = TimeUtil.getYearMonthEndDay(i1, 6);
/* 253 */           str3 = TimeUtil.getYearMonthEndDay(k, 6);
/* 254 */         } catch (Exception exception) {}
/* 255 */         str13 = "select hstarttype,hstartdays,hendtype,henddays from GP_BaseSetting where resourcetype=2 and resourceid=" + str12 + " and ishyear=1";
/* 256 */       } else if (m == 4) {
/* 257 */         i1 = k - 1;
/*     */         try {
/* 259 */           str2 = TimeUtil.getYearMonthEndDay(i1, 12);
/* 260 */           str3 = TimeUtil.getYearMonthEndDay(k, 12);
/* 261 */         } catch (Exception exception) {}
/* 262 */         str13 = "select fstarttype,fstartdays,fendtype,fenddays from GP_BaseSetting where resourcetype=2 and resourceid=" + str12 + " and isfyear=1";
/*     */       } 
/* 264 */       recordSet.executeSql(str13);
/* 265 */       if (recordSet.next()) {
/*     */         
/* 267 */         str4 = TimeUtil.dateAdd(str2, Util.getIntValue(recordSet.getString(1), 1) * Util.getIntValue(recordSet.getString(2), 0));
/* 268 */         str5 = TimeUtil.dateAdd(str2, Util.getIntValue(recordSet.getString(3), 1) * Util.getIntValue(recordSet.getString(4), 0));
/* 269 */         str6 = TimeUtil.dateAdd(str3, Util.getIntValue(recordSet.getString(1), 1) * Util.getIntValue(recordSet.getString(2), 0));
/* 270 */         str7 = TimeUtil.dateAdd(str3, Util.getIntValue(recordSet.getString(3), 1) * Util.getIntValue(recordSet.getString(4), 0));
/*     */ 
/*     */         
/* 273 */         if (TimeUtil.dateInterval(str4, str1) >= 0 && TimeUtil.dateInterval(str5, str1) <= 0) {
/* 274 */           updateScoreByProgram(str11, paramString, str8, i1, m, i2, str2, str4, str5, str9, str10);
/*     */         }
/*     */         
/* 277 */         updateScoreByProgram(str11, paramString, str8, k, m, n, str3, str6, str7, str9, str10);
/*     */       } 
/*     */     } 
/*     */   }
/*     */   
/*     */   public void updateScoreByProgram(String paramString1, String paramString2, String paramString3, int paramInt1, int paramInt2, int paramInt3, String paramString4, String paramString5, String paramString6, String paramString7, String paramString8) {
/* 283 */     RecordSet recordSet = new RecordSet();
/* 284 */     recordSet.executeSql("select id,status,operator,isinit,isfirst,programid,isrescore from GP_AccessScore where year=" + paramInt1 + " and type1=" + paramInt2 + " and type2=" + paramInt3 + " and userid=" + paramString1);
/* 285 */     if (recordSet.next()) {
/*     */       
/* 287 */       String str1 = Util.null2String(recordSet.getString("id"));
/* 288 */       String str2 = Util.null2String(recordSet.getString("programid"));
/* 289 */       String str3 = Util.null2String(recordSet.getString("operator"));
/* 290 */       int i = Util.getIntValue(recordSet.getString("isrescore"), 0);
/* 291 */       int j = Util.getIntValue(recordSet.getString("isinit"), 1);
/* 292 */       int k = Util.getIntValue(recordSet.getString("isfirst"), 1);
/*     */       
/* 294 */       String str4 = "";
/* 295 */       if ("oracle".equals(recordSet.getDBType())) {
/* 296 */         str4 = "select * from (select id,auditids from GP_AccessProgram where startdate<='" + paramString5 + "' and programtype=" + paramInt2 + " and userid=" + paramString1 + " and status=3 order by startdate desc,id desc) t where rownum=1";
/* 297 */       } else if ("mysql".equals(recordSet.getDBType())) {
/* 298 */         str4 = "select id,auditids from GP_AccessProgram where startdate<='" + paramString5 + "' and programtype=" + paramInt2 + " and userid=" + paramString1 + " and status=3 order by startdate desc,id desc limit 1";
/*     */       }
/* 300 */       else if ("postgresql".equals(recordSet.getDBType())) {
/* 301 */         str4 = "select id,auditids from GP_AccessProgram where startdate<='" + paramString5 + "' and programtype=" + paramInt2 + " and userid=" + paramString1 + " and status=3 order by startdate desc,id desc limit 1";
/*     */       } else {
/*     */         
/* 304 */         str4 = "select top 1 id,auditids from GP_AccessProgram where startdate<='" + paramString5 + "' and programtype=" + paramInt2 + " and userid=" + paramString1 + " and status=3 order by startdate desc,id desc";
/*     */       } 
/* 306 */       recordSet.executeSql(str4);
/* 307 */       if (recordSet.next()) {
/* 308 */         String str5 = Util.null2String(recordSet.getString("id"));
/* 309 */         String str6 = Util.null2String(recordSet.getString("auditids"));
/* 310 */         if (j == 1) {
/*     */           
/* 312 */           recordSet.executeSql("delete from GP_AccessScore where id=" + str1);
/* 313 */           recordSet.executeSql("delete from GP_AccessScoreDetail where scoreid=" + str1);
/* 314 */           initData(paramString1, str5, paramInt1, paramInt2, paramInt3, paramString5, paramString6, str6, paramString8);
/*     */         }
/* 316 */         else if (str5.equals(paramString2) || (!str5.equals(paramString2) && str2.equals(paramString2))) {
/*     */           
/* 318 */           if (k == 1) {
/* 319 */             recordSet.executeSql("select userid from GP_AccessProgramCheck where exeorder=0 and programid=" + str5);
/* 320 */             if (recordSet.next()) {
/* 321 */               String str = Util.null2String(recordSet.getString("userid"));
/* 322 */               if (str.equals("-1")) str = this.rc.getManagerID(paramString1); 
/* 323 */               if (!str.equals(str3)) {
/*     */                 
/* 325 */                 recordSet.executeSql("delete from GP_AccessScore where id=" + str1);
/* 326 */                 recordSet.executeSql("delete from GP_AccessScoreDetail where scoreid=" + str1);
/* 327 */                 recordSet.executeSql("delete from GP_AccessScoreCheck where scoreid=" + str1);
/* 328 */                 recordSet.executeSql("delete from GP_AccessScoreCheckDetail where scoreid=" + str1);
/* 329 */                 String str7 = initData(paramString1, str5, paramInt1, paramInt2, paramInt3, paramString5, paramString6, str6, paramString8);
/* 330 */                 recordSet.executeSql("update GP_AccessScoreLog set scoreid=" + str7 + " where scoreid=" + str1);
/* 331 */                 recordSet.executeSql("update GP_AccessScoreExchange set scoreid=" + str7 + " where scoreid=" + str1);
/* 332 */                 if (i == 1) recordSet.executeSql("update GP_AccessScore set isrescore=1 where id=" + str7); 
/* 333 */                 addScoreLog("0", str7, 1);
/*     */               } else {
/* 335 */                 recordSet.executeSql("update GP_AccessScore set isupdate=1 where id=" + str1);
/*     */               } 
/*     */             } 
/*     */           } else {
/* 339 */             recordSet.executeSql("update GP_AccessScore set isupdate=1 where id=" + str1);
/*     */           }
/*     */         
/*     */         } 
/*     */       } 
/* 344 */     } else if (TimeUtil.dateInterval(paramString3, paramString5) >= 0) {
/*     */       
/* 346 */       initData(paramString1, paramString2, paramInt1, paramInt2, paramInt3, paramString5, paramString6, paramString7, paramString8);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void initData(String paramString1, int paramInt1, int paramInt2, int paramInt3, String paramString2, String paramString3) {
/* 360 */     RecordSet recordSet = new RecordSet();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 366 */     String str1 = "";
/* 367 */     if ("oracle".equals(recordSet.getDBType())) {
/* 368 */       str1 = " select p.userid,p.id,p.auditids,p.remark from GP_AccessProgram p,HrmResource h where p.userid=h.id and h.subcompanyid1=" + paramString1 + " and h.status in (0,1,2,3) and h.loginid is not null and (p.id=(select id from (select p2.id,p2.userid from GP_AccessProgram p2 where p2.startdate<='" + paramString2 + "' and p2.programtype=" + paramInt2 + " and p2.status=3 order by p2.startdate desc,p2.id desc) t where t.userid=p.userid and rownum=1) or exists(select 1 from GP_AccessScore s where s.userid=p.userid and s.year=" + paramInt1 + " and s.type1=" + paramInt2 + " and s.type2=" + paramInt3 + "))";
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*     */     }
/* 374 */     else if ("mysql".equals(recordSet.getDBType())) {
/* 375 */       str1 = " select p.userid,p.id,p.auditids,p.remark from GP_AccessProgram p,HrmResource h where p.userid=h.id and h.subcompanyid1=" + paramString1 + " and h.status in (0,1,2,3) and h.loginid is not null  and (p.id=(select p2.id from GP_AccessProgram p2 where p2.startdate<='" + paramString2 + "' and p2.programtype=" + paramInt2 + " and p2.status=3 and p2.userid=p.userid order by p2.startdate desc,p2.id desc limit 1) or exists(select 1 from GP_AccessScore s where s.userid=p.userid and s.year=" + paramInt1 + " and s.type1=" + paramInt2 + " and s.type2=" + paramInt3 + "))";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*     */     }
/* 382 */     else if ("postgresql".equals(recordSet.getDBType())) {
/* 383 */       str1 = " select p.userid,p.id,p.auditids,p.remark from GP_AccessProgram p,HrmResource h where p.userid=h.id and h.subcompanyid1=" + paramString1 + " and h.status in (0,1,2,3) and h.loginid is not null  and (p.id=(select p2.id from GP_AccessProgram p2 where p2.startdate<='" + paramString2 + "' and p2.programtype=" + paramInt2 + " and p2.status=3 and p2.userid=p.userid order by p2.startdate desc,p2.id desc limit 1) or exists(select 1 from GP_AccessScore s where s.userid=p.userid and s.year=" + paramInt1 + " and s.type1=" + paramInt2 + " and s.type2=" + paramInt3 + "))";
/*     */ 
/*     */     
/*     */     }
/*     */     else {
/*     */ 
/*     */ 
/*     */       
/* 391 */       str1 = " select p.userid,p.id,p.auditids,p.remark from GP_AccessProgram p,HrmResource h where p.userid=h.id and h.subcompanyid1=" + paramString1 + " and h.status in (0,1,2,3) and h.loginid is not null and h.loginid<>'' and (p.id=(select top 1 p2.id from GP_AccessProgram p2 where p2.startdate<='" + paramString2 + "' and p2.programtype=" + paramInt2 + " and p2.status=3 and p2.userid=p.userid order by p2.startdate desc,p2.id desc) or exists(select 1 from GP_AccessScore s where s.userid=p.userid and s.year=" + paramInt1 + " and s.type1=" + paramInt2 + " and s.type2=" + paramInt3 + "))";
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 398 */     recordSet.executeSql(str1);
/* 399 */     String str2 = "";
/* 400 */     String str3 = "";
/* 401 */     String str4 = "";
/* 402 */     String str5 = "";
/* 403 */     while (recordSet.next()) {
/* 404 */       str2 = Util.null2String(recordSet.getString("userid"));
/* 405 */       str3 = Util.null2String(recordSet.getString("id"));
/* 406 */       str4 = Util.null2String(recordSet.getString("auditids"));
/* 407 */       str5 = Util.null2String(recordSet.getString("remark"));
/* 408 */       initData(str2, str3, paramInt1, paramInt2, paramInt3, paramString2, paramString3, str4, str5);
/*     */     } 
/*     */   }
/*     */   public String initData(String paramString1, String paramString2, int paramInt1, int paramInt2, int paramInt3, String paramString3, String paramString4, String paramString5, String paramString6) {
/* 412 */     RecordSet recordSet1 = new RecordSet();
/* 413 */     RecordSet recordSet2 = new RecordSet();
/* 414 */     String str = "";
/* 415 */     recordSet1.executeSql("select id from GP_AccessScore where year=" + paramInt1 + " and type1=" + paramInt2 + " and type2=" + paramInt3 + " and userid=" + paramString1);
/* 416 */     if (recordSet1.next()) {
/*     */       
/* 418 */       str = Util.null2String(recordSet1.getString(1));
/* 419 */       recordSet1.executeSql("update GP_AccessScore set isvalid=1,startdate='" + paramString3 + "',enddate='" + paramString4 + "' where id=" + str);
/*     */     } else {
/* 421 */       String str1 = paramInt1 + "年";
/* 422 */       int i = paramInt1;
/* 423 */       int j = paramInt3;
/* 424 */       if (paramInt2 == 1) {
/* 425 */         str1 = str1 + paramInt3 + "月";
/* 426 */         if (j == 1) {
/* 427 */           i--;
/* 428 */           j = 12;
/*     */         } else {
/* 430 */           j--;
/*     */         } 
/* 432 */       } else if (paramInt2 == 2) {
/* 433 */         str1 = str1 + "" + SystemEnv.getHtmlLabelName(15323, ThreadVarLanguage.getLang()) + "" + paramInt3 + "" + SystemEnv.getHtmlLabelName(17495, ThreadVarLanguage.getLang()) + "";
/* 434 */         if (j == 1) {
/* 435 */           i--;
/* 436 */           j = 4;
/*     */         } else {
/* 438 */           j--;
/*     */         } 
/* 440 */       } else if (paramInt2 == 3) {
/* 441 */         str1 = str1 + "" + SystemEnv.getHtmlLabelName(19483, ThreadVarLanguage.getLang()) + "";
/* 442 */         i--;
/* 443 */       } else if (paramInt2 == 4) {
/* 444 */         str1 = str1 + "" + SystemEnv.getHtmlLabelName(17138, ThreadVarLanguage.getLang()) + "";
/* 445 */         i--;
/*     */       } 
/* 447 */       str1 = str1 + "" + SystemEnv.getHtmlLabelName(508616, ThreadVarLanguage.getLang()) + "";
/*     */       
/* 449 */       boolean bool = recordSet1.executeSql("insert into GP_AccessScore(scorename,userid,year,type1,type2,status,result,startdate,enddate,operator,isupdate,isinit,isfirst,programid,isvalid,auditids,remark) values('" + this.rc
/* 450 */           .getLastname(paramString1) + str1 + "'," + paramString1 + "," + paramInt1 + "," + paramInt2 + "," + paramInt3 + ",0,0,'" + paramString3 + "','" + paramString4 + "',0,0,1,1," + paramString2 + ",1,'" + paramString5 + "','" + paramString6 + "')");
/* 451 */       if (bool) {
/* 452 */         recordSet1.executeSql("select max(id) from GP_AccessScore");
/*     */         
/* 454 */         if (recordSet1.next()) {
/* 455 */           str = Util.null2String(recordSet1.getString(1));
/* 456 */           recordSet1.executeSql("select t.cate,t.accessitemid,t.description,t.rate,t.target1,t.target2,p.next1,p.next2  from GP_AccessProgramDetail t left join  (select t1.accessitemid,t1.description,t1.next1,t1.next2 from GP_AccessScoreDetail t1,GP_AccessScore t2 where t1.scoreid=t2.id and t2.status=3 and t2.userid=" + paramString1 + " and t2.year=" + i + " and t2.type1=" + paramInt2 + " and t2.type2=" + j + ") p on t.accessitemid=p.accessitemid and t.description=p.description where t.programid=" + paramString2 + " order by t.id");
/*     */ 
/*     */ 
/*     */ 
/*     */           
/* 461 */           while (recordSet1.next()) {
/* 462 */             String str3 = Util.convertInput2DB(recordSet1.getString("cate"));
/* 463 */             String str4 = Util.null2String(recordSet1.getString("accessitemid"));
/* 464 */             String str5 = Util.convertInput2DB(recordSet1.getString("name"));
/* 465 */             String str6 = Util.convertInput2DB(recordSet1.getString("description"));
/* 466 */             String str7 = Util.getDoubleValue(recordSet1.getString("rate"), 0.0D) + "";
/* 467 */             String str8 = Util.null2String(recordSet1.getString("next1")) + "";
/* 468 */             if (str8.equals("")) str8 = Util.null2String(recordSet1.getString("target1")); 
/* 469 */             str8 = Util.getDoubleValue(str8, 0.0D) + "";
/* 470 */             String str9 = Util.convertInput2DB(recordSet1.getString("next2"));
/* 471 */             if (str9.equals("")) str9 = Util.null2String(recordSet1.getString("target2")); 
/* 472 */             recordSet2.executeSql("insert into GP_AccessScoreDetail(scoreid,cate,accessitemid,name,description,rate,target1,target2,result1,result2,next1,next2) values(" + str + ",'" + str3 + "'," + str4 + ",'" + str5 + "','" + str6 + "'," + str7 + "," + str8 + ",'" + str9 + "',0,''," + str8 + ",'" + str9 + "')");
/*     */           } 
/*     */           
/* 475 */           recordSet1.executeSql("select userid,rate,exeorder from GP_AccessProgramCheck where programid=" + paramString2 + " order by exeorder");
/* 476 */           boolean bool1 = false;
/* 477 */           byte b = 0;
/* 478 */           String str2 = "";
/* 479 */           while (recordSet1.next()) {
/*     */             
/* 481 */             str2 = Util.null2String(recordSet1.getString("userid"));
/* 482 */             if (str2.equals("-1")) str2 = this.rc.getManagerID(paramString1); 
/* 483 */             if (isWork(str2)) {
/* 484 */               if (!b) {
/* 485 */                 bool1 = true;
/*     */                 
/* 487 */                 recordSet2.executeSql("update GP_AccessScore set operator=" + str2 + " where id=" + str);
/*     */               } else {
/* 489 */                 bool1 = false;
/*     */               } 
/* 491 */               recordSet2.executeSql("insert into GP_AccessScoreCheck(scoreid,userid,rate,score,revise,result,remark,status,exeorder) values(" + str + "," + str2 + "," + recordSet1
/* 492 */                   .getString("rate") + ",0,0,0,''," + bool1 + "," + b + ")");
/* 493 */               b++; continue;
/*     */             } 
/* 495 */             recordSet2.executeUpdate("insert into GP_ACCESSresetlog(scoreid,operator,operatedate,operatetype) values(?,?,'" + TimeUtil.getCurrentTimeString() + "',2)", new Object[] { str, str2 });
/*     */           } 
/*     */         } 
/*     */       } else {
/*     */         
/* 500 */         writeLog("生成考核数据出错！");
/*     */       } 
/*     */     } 
/* 503 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void updateScoreBySetting(String paramString) {
/* 511 */     RecordSet recordSet = new RecordSet();
/* 512 */     recordSet.executeSql("select * from GP_BaseSetting where resourceid=" + paramString + " and resourcetype=2");
/* 513 */     if (recordSet.next()) {
/* 514 */       String str1 = TimeUtil.getCurrentDateString();
/* 515 */       String str2 = "";
/* 516 */       String str3 = "";
/* 517 */       String str4 = "";
/* 518 */       String str5 = "";
/* 519 */       int i = Util.getIntValue(TimeUtil.getCurrentDateString().substring(5, 7));
/* 520 */       int j = Util.getIntValue(TimeUtil.getCurrentSeason());
/* 521 */       int k = Util.getIntValue(TimeUtil.getCurrentDateString().substring(0, 4));
/* 522 */       int m = 0;
/* 523 */       int n = k;
/* 524 */       int i1 = 0;
/* 525 */       String str6 = "";
/* 526 */       for (byte b = 1; b < 5; b++) {
/* 527 */         if (b == 1) {
/* 528 */           m = i;
/* 529 */           if (m == 1) {
/* 530 */             n = k - 1;
/* 531 */             i1 = 12;
/*     */           } else {
/* 533 */             i1 = m - 1;
/*     */           } 
/*     */           try {
/* 536 */             str2 = TimeUtil.getYearMonthEndDay(n, i1);
/* 537 */             str3 = TimeUtil.getYearMonthEndDay(k, i);
/* 538 */           } catch (Exception exception) {}
/* 539 */           str6 = "select mstarttype,mstartdays,mendtype,menddays,ismonth from GP_BaseSetting where resourcetype=2 and resourceid=" + paramString;
/* 540 */         } else if (b == 2) {
/* 541 */           m = j;
/* 542 */           if (m == 1) {
/* 543 */             n = k - 1;
/* 544 */             i1 = 4;
/*     */           } else {
/* 546 */             i1 = m - 1;
/*     */           } 
/*     */           try {
/* 549 */             if (i1 == 1) str2 = TimeUtil.getYearMonthEndDay(n, 3); 
/* 550 */             if (i1 == 2) str2 = TimeUtil.getYearMonthEndDay(n, 6); 
/* 551 */             if (i1 == 3) str2 = TimeUtil.getYearMonthEndDay(n, 9); 
/* 552 */             if (i1 == 4) str2 = TimeUtil.getYearMonthEndDay(n, 12);
/*     */             
/* 554 */             if (j == 1) str3 = TimeUtil.getYearMonthEndDay(k, 3); 
/* 555 */             if (j == 2) str3 = TimeUtil.getYearMonthEndDay(k, 6); 
/* 556 */             if (j == 3) str3 = TimeUtil.getYearMonthEndDay(k, 9); 
/* 557 */             if (j == 4) str3 = TimeUtil.getYearMonthEndDay(k, 12); 
/* 558 */           } catch (Exception exception) {}
/* 559 */           str6 = "select qstarttype,qstartdays,qendtype,qenddays,isquarter from GP_BaseSetting where resourcetype=2 and resourceid=" + paramString;
/* 560 */         } else if (b == 3) {
/* 561 */           n = k - 1;
/* 562 */           i1 = 0;
/* 563 */           m = 0;
/*     */           try {
/* 565 */             str2 = TimeUtil.getYearMonthEndDay(n, 6);
/* 566 */             str3 = TimeUtil.getYearMonthEndDay(k, 6);
/* 567 */           } catch (Exception exception) {}
/* 568 */           str6 = "select hstarttype,hstartdays,hendtype,henddays,ishyear from GP_BaseSetting where resourcetype=2 and resourceid=" + paramString;
/* 569 */         } else if (b == 4) {
/* 570 */           n = k - 1;
/* 571 */           i1 = 0;
/* 572 */           m = 0;
/*     */           try {
/* 574 */             str2 = TimeUtil.getYearMonthEndDay(n, 12);
/* 575 */             str3 = TimeUtil.getYearMonthEndDay(k, 12);
/* 576 */           } catch (Exception exception) {}
/* 577 */           str6 = "select fstarttype,fstartdays,fendtype,fenddays,isfyear from GP_BaseSetting where resourcetype=2 and resourceid=" + paramString;
/*     */         } 
/* 579 */         recordSet.executeSql(str6);
/* 580 */         if (recordSet.next()) {
/* 581 */           int i2 = Util.getIntValue(recordSet.getString(5), 0);
/*     */           
/* 583 */           str4 = TimeUtil.dateAdd(str2, Util.getIntValue(recordSet.getString(1), 1) * Util.getIntValue(recordSet.getString(2), 0));
/* 584 */           str5 = TimeUtil.dateAdd(str2, Util.getIntValue(recordSet.getString(3), 1) * Util.getIntValue(recordSet.getString(4), 0));
/*     */           
/* 586 */           if (i2 == 1) {
/* 587 */             updateData(paramString, n, b, i1, str4, str5);
/*     */           } else {
/* 589 */             closeData(paramString, n, b, i1);
/*     */           } 
/*     */ 
/*     */           
/* 593 */           str4 = TimeUtil.dateAdd(str3, Util.getIntValue(recordSet.getString(1), 1) * Util.getIntValue(recordSet.getString(2), 0));
/* 594 */           str5 = TimeUtil.dateAdd(str3, Util.getIntValue(recordSet.getString(3), 1) * Util.getIntValue(recordSet.getString(4), 0));
/* 595 */           if (i2 == 1) {
/* 596 */             updateData(paramString, k, b, m, str4, str5);
/*     */           } else {
/* 598 */             closeData(paramString, k, b, m);
/*     */           } 
/*     */         } 
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void updateData(String paramString1, int paramInt1, int paramInt2, int paramInt3, String paramString2, String paramString3) {
/* 612 */     RecordSet recordSet = new RecordSet();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 624 */     recordSet.executeSql("update GP_AccessScore set isvalid=1,startdate='" + paramString2 + "',enddate='" + paramString3 + "' where year=" + paramInt1 + " and type1=" + paramInt2 + " and type2=" + paramInt3 + " and exists(select id from HrmResource where HrmResource.id=GP_AccessScore.userid and HrmResource.subcompanyid1=" + paramString1 + " and HrmResource.status in (0,1,2,3) and HrmResource.loginid is not null" + (
/*     */         
/* 626 */         !"oracle".equals(recordSet.getDBType()) ? " and HrmResource.loginid<>''" : "") + ")");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void closeData(String paramString, int paramInt1, int paramInt2, int paramInt3) {
/* 637 */     RecordSet recordSet = new RecordSet();
/* 638 */     recordSet.executeSql("update GP_AccessScore set isvalid=0 where year=" + paramInt1 + " and type1=" + paramInt2 + " and type2=" + paramInt3 + " and exists(select id from HrmResource where HrmResource.id=GP_AccessScore.userid and HrmResource.subcompanyid1=" + paramString + ")");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int[] getNoProgramCount(String paramString) throws Exception {
/* 649 */     RecordSet recordSet = new RecordSet();
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 654 */     String str1 = "select count(h.id) as amount  from HrmResource h,GP_BaseSetting t where h.subcompanyid1=t.resourceid and t.resourcetype=2 and h.status in (0,1,2,3) and h.loginid is not null " + ("sqlserver".equals(recordSet.getDBType()) ? " and h.loginid<>''" : "") + " and (h.id=" + paramString + " or h.managerstr like '%," + paramString + ",%' or exists(select 1 from GP_BaseSetting bs where bs.resourceid=h.subcompanyid1 and bs.resourcetype=2 and (bs.programcreate like '%," + paramString + ",%' or bs.programaudit like '%," + paramString + ",%')) or exists(select 1 from GP_BaseSetting bs where bs.resourceid=h.departmentid and bs.resourcetype=3 and (bs.programcreate like '%," + paramString + ",%' or bs.programaudit like '%," + paramString + ",%')) )";
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 659 */     String str2 = "";
/* 660 */     int i = Util.getIntValue(TimeUtil.getCurrentDateString().substring(0, 4));
/* 661 */     int j = Util.getIntValue(TimeUtil.getCurrentDateString().substring(5, 7));
/* 662 */     int k = Util.getIntValue(TimeUtil.getCurrentSeason());
/* 663 */     String str3 = "";
/* 664 */     String str4 = "";
/*     */     
/* 666 */     int m = 0;
/* 667 */     str2 = TimeUtil.getYearMonthEndDay(i, 12);
/* 668 */     if ("oracle".equals(recordSet.getDBType())) {
/* 669 */       str4 = "to_char(to_date('" + str2 + "','yyyy-mm-dd')+t.fstartdays*t.fstarttype,'yyyy-mm-dd')";
/* 670 */     } else if ("mysql".equals(recordSet.getDBType())) {
/* 671 */       str4 = "DATE_FORMAT(DATE_ADD('" + str2 + "', INTERVAL t.fstartdays*t.fstarttype DAY),'%Y-%m-%d')";
/*     */     }
/* 673 */     else if ("postgresql".equals(recordSet.getDBType())) {
/* 674 */       str4 = "to_char(to_date('" + str2 + "','yyyy-mm-dd')+t.fstartdays*t.fstarttype,'yyyy-mm-dd')";
/*     */     } else {
/*     */       
/* 677 */       str4 = "Convert(VARCHAR(10),dateadd(d,t.fstartdays*t.fstarttype,'" + str2 + "'),126)";
/*     */     } 
/* 679 */     str3 = " and (t.isfyear=1 and not exists(select 1 from GP_AccessProgram ap where ap.status=3 and ap.startdate<=" + str4 + " and ap.programtype=4 and ap.userid=h.id))";
/*     */     
/* 681 */     recordSet.executeSql(str1 + str3);
/* 682 */     if (recordSet.next()) m = Util.getIntValue(recordSet.getString(1), 0);
/*     */     
/* 684 */     int n = 0;
/* 685 */     str2 = TimeUtil.getYearMonthEndDay(i, 6);
/* 686 */     if ("oracle".equals(recordSet.getDBType())) {
/* 687 */       str4 = "to_char(to_date('" + str2 + "','yyyy-mm-dd')+t.hstartdays*t.hstarttype,'yyyy-mm-dd')";
/* 688 */     } else if ("mysql".equals(recordSet.getDBType())) {
/* 689 */       str4 = "DATE_FORMAT(DATE_ADD('" + str2 + "', INTERVAL t.hstartdays*t.hstarttype DAY),'%Y-%m-%d')";
/*     */     }
/* 691 */     else if ("postgresql".equals(recordSet.getDBType())) {
/* 692 */       str4 = "to_char(to_date('" + str2 + "','yyyy-mm-dd')+t.hstartdays*t.hstarttype,'yyyy-mm-dd')";
/*     */     } else {
/*     */       
/* 695 */       str4 = "Convert(VARCHAR(10),dateadd(d,t.hstartdays*t.hstarttype,'" + str2 + "'),126)";
/*     */     } 
/* 697 */     str3 = " and (t.ishyear=1 and not exists(select 1 from GP_AccessProgram ap where ap.status=3 and ap.startdate<=" + str4 + " and ap.programtype=3 and ap.userid=h.id))";
/* 698 */     recordSet.executeSql(str1 + str3);
/* 699 */     if (recordSet.next()) n = Util.getIntValue(recordSet.getString(1), 0);
/*     */     
/* 701 */     int i1 = 0;
/* 702 */     if (k == 1) str2 = TimeUtil.getYearMonthEndDay(i, 3); 
/* 703 */     if (k == 2) str2 = TimeUtil.getYearMonthEndDay(i, 6); 
/* 704 */     if (k == 3) str2 = TimeUtil.getYearMonthEndDay(i, 9); 
/* 705 */     if (k == 4) str2 = TimeUtil.getYearMonthEndDay(i, 12); 
/* 706 */     if ("oracle".equals(recordSet.getDBType())) {
/* 707 */       str4 = "to_char(to_date('" + str2 + "','yyyy-mm-dd')+t.qstartdays*t.qstarttype,'yyyy-mm-dd')";
/* 708 */     } else if ("mysql".equals(recordSet.getDBType())) {
/* 709 */       str4 = "DATE_FORMAT(DATE_ADD('" + str2 + "', INTERVAL t.qstartdays*t.qstarttype DAY),'%Y-%m-%d')";
/*     */     }
/* 711 */     else if ("postgresql".equals(recordSet.getDBType())) {
/* 712 */       str4 = "to_char(to_date('" + str2 + "','yyyy-mm-dd')+t.qstartdays*t.qstarttype,'yyyy-mm-dd')";
/*     */     } else {
/*     */       
/* 715 */       str4 = "Convert(VARCHAR(10),dateadd(d,t.qstartdays*t.qstarttype,'" + str2 + "'),126)";
/*     */     } 
/* 717 */     if (!str3.equals("")) str3 = str3 + " or "; 
/* 718 */     str3 = " and (t.isquarter=1 and not exists(select 1 from GP_AccessProgram ap where ap.status=3 and ap.startdate<=" + str4 + " and ap.programtype=2 and ap.userid=h.id))";
/* 719 */     recordSet.executeSql(str1 + str3);
/* 720 */     if (recordSet.next()) i1 = Util.getIntValue(recordSet.getString(1), 0);
/*     */     
/* 722 */     int i2 = 0;
/* 723 */     str2 = TimeUtil.getYearMonthEndDay(i, j);
/* 724 */     if ("oracle".equals(recordSet.getDBType())) {
/* 725 */       str4 = "to_char(to_date('" + str2 + "','yyyy-mm-dd')+t.mstartdays*t.mstarttype,'yyyy-mm-dd')";
/* 726 */     } else if ("mysql".equals(recordSet.getDBType())) {
/* 727 */       str4 = "DATE_FORMAT(DATE_ADD('" + str2 + "', INTERVAL t.mstartdays*t.mstarttype DAY),'%Y-%m-%d')";
/*     */     }
/* 729 */     else if ("postgresql".equals(recordSet.getDBType())) {
/* 730 */       str4 = "to_char(to_date('" + str2 + "','yyyy-mm-dd')+t.mstartdays*t.mstarttype,'yyyy-mm-dd')";
/*     */     } else {
/*     */       
/* 733 */       str4 = "Convert(VARCHAR(10),dateadd(d,t.mstartdays*t.mstarttype,'" + str2 + "'),126)";
/*     */     } 
/* 735 */     str3 = " and (t.ismonth=1 and not exists(select 1 from GP_AccessProgram ap where ap.status=3 and ap.startdate<=" + str4 + " and ap.programtype=1 and ap.userid=h.id))";
/* 736 */     recordSet.executeSql(str1 + str3);
/* 737 */     if (recordSet.next()) i2 = Util.getIntValue(recordSet.getString(1), 0);
/*     */     
/* 739 */     return new int[] { m, n, i1, i2 };
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean isWork(String paramString) {
/* 749 */     if (this.rc.getStatus(paramString).equals("0") || this.rc.getStatus(paramString).equals("1") || this.rc.getStatus(paramString).equals("2") || this.rc.getStatus(paramString).equals("3")) {
/* 750 */       return true;
/*     */     }
/* 752 */     return false;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/gp/util/OperateUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */