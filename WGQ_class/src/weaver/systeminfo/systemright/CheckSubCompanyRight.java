/*      */ package weaver.systeminfo.systemright;
/*      */ 
/*      */ import com.engine.common.service.impl.HrmCommonServiceImpl;
/*      */ import com.engine.hrm.biz.HrmSanyuanAdminBiz;
/*      */ import java.util.ArrayList;
/*      */ import java.util.Arrays;
/*      */ import java.util.List;
/*      */ import java.util.Map;
/*      */ import java.util.concurrent.ConcurrentHashMap;
/*      */ import weaver.common.StringUtil;
/*      */ import weaver.conn.RecordSet;
/*      */ import weaver.general.BaseBean;
/*      */ import weaver.general.StaticObj;
/*      */ import weaver.general.Util;
/*      */ import weaver.hrm.User;
/*      */ import weaver.hrm.company.DepartmentComInfo;
/*      */ import weaver.hrm.company.SubCompanyComInfo;
/*      */ import weaver.hrm.moduledetach.ManageDetachComInfo;
/*      */ import weaver.hrm.resource.ResourceComInfo;
/*      */ import weaver.systeminfo.setting.HrmUserSettingComInfo;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ public class CheckSubCompanyRight
/*      */   extends BaseBean
/*      */ {
/*   34 */   private RecordSet rs = null;
/*   35 */   private char flag = Util.getSeparator();
/*   36 */   public static int FORBIT = -1;
/*   37 */   public static int READ = 0;
/*   38 */   public static int EDIT = 1;
/*   39 */   public static int ALL = 2;
/*   40 */   private String subcompanyids = "";
/*   41 */   private String departmentids = "";
/*      */   
/*      */   private boolean isall = false;
/*   44 */   private int isdetail = 0;
/*   45 */   private int isbill = 0;
/*   46 */   private int fieldid = 0;
/*   47 */   private int detachable = 0;
/*      */   private boolean showCanceled = false;
/*   49 */   ManageDetachComInfo ManageDetachComInfo = new ManageDetachComInfo();
/*      */   
/*   51 */   private StaticObj staticobj = null;
/*      */   
/*      */   public void setShowCanceled(boolean paramBoolean) {
/*   54 */     this.showCanceled = paramBoolean;
/*      */   }
/*      */   
/*      */   public int getIsdetail() {
/*   58 */     return this.isdetail;
/*      */   }
/*      */   
/*      */   public void setIsdetail(int paramInt) {
/*   62 */     this.isdetail = paramInt;
/*      */   }
/*      */   
/*      */   public int getIsbill() {
/*   66 */     return this.isbill;
/*      */   }
/*      */   
/*      */   public void setIsbill(int paramInt) {
/*   70 */     this.isbill = paramInt;
/*      */   }
/*      */   
/*      */   public int getFieldid() {
/*   74 */     return this.fieldid;
/*      */   }
/*      */   
/*      */   public void setFieldid(int paramInt) {
/*   78 */     this.fieldid = paramInt;
/*      */   }
/*      */   
/*      */   public int getDetachable() {
/*   82 */     return this.detachable;
/*      */   }
/*      */   
/*      */   public void setDetachable(int paramInt) {
/*   86 */     this.detachable = paramInt;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public CheckSubCompanyRight() {
/*   94 */     this.staticobj = StaticObj.getInstance();
/*   95 */     this.rs = new RecordSet();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getSubcompanyids() {
/*  103 */     return this.subcompanyids;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getDepartmentids() {
/*  111 */     return this.departmentids;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean getIsall() {
/*  119 */     return this.isall;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public int[] getSubComByUserRightId(int paramInt, String paramString) {
/*  129 */     ArrayList<String> arrayList = new ArrayList();
/*  130 */     this.rs = new RecordSet();
/*      */     
/*  132 */     this.rs.executeSql("select detachable,hrmdetachable from SystemSet");
/*  133 */     int i = 0;
/*      */     
/*  135 */     if (this.rs.next()) {
/*  136 */       i = this.rs.getInt("detachable");
/*      */     }
/*      */     
/*  139 */     i = getDetachableByRight(paramString) ? 1 : 0;
/*  140 */     boolean bool = HrmSanyuanAdminBiz.canViewAllSubCompany(paramInt, paramString);
/*  141 */     if (paramInt == 1 || bool)
/*      */       
/*      */       try {
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */         
/*  153 */         SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/*  154 */         subCompanyComInfo.setTofirstRow();
/*  155 */         int[] arrayOfInt1 = new int[subCompanyComInfo.getCompanyNum()];
/*  156 */         byte b1 = 0;
/*  157 */         while (subCompanyComInfo.next()) {
/*  158 */           arrayOfInt1[b1] = Integer.parseInt(subCompanyComInfo.getSubCompanyid());
/*  159 */           b1++;
/*      */         } 
/*  161 */         return arrayOfInt1;
/*  162 */       } catch (Exception exception) {
/*  163 */         exception.printStackTrace();
/*  164 */         return null;
/*      */       }  
/*  166 */     if (i == 0) {
/*  167 */       HrmCommonServiceImpl hrmCommonServiceImpl = new HrmCommonServiceImpl();
/*  168 */       int j = hrmCommonServiceImpl.getMaxRoleLevel(paramInt, paramString);
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*  178 */       if (j > -1 && j < 2)
/*      */         try {
/*  180 */           ResourceComInfo resourceComInfo = new ResourceComInfo();
/*  181 */           DepartmentComInfo departmentComInfo = new DepartmentComInfo();
/*  182 */           String str1 = resourceComInfo.getDepartmentID("" + paramInt);
/*  183 */           String str2 = departmentComInfo.getSubcompanyid1(str1);
/*  184 */           return new int[] { Util.getIntValue(str2) };
/*  185 */         } catch (Exception exception) {
/*  186 */           writeLog(exception);
/*  187 */           return new int[0];
/*      */         }  
/*  189 */       if (j < 0) {
/*  190 */         return new int[0];
/*      */       }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*      */       try {
/*  203 */         SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/*  204 */         subCompanyComInfo.setTofirstRow();
/*  205 */         int[] arrayOfInt1 = new int[subCompanyComInfo.getCompanyNum()];
/*  206 */         byte b1 = 0;
/*  207 */         while (subCompanyComInfo.next()) {
/*  208 */           arrayOfInt1[b1] = Integer.parseInt(subCompanyComInfo.getSubCompanyid());
/*  209 */           b1++;
/*      */         } 
/*  211 */         return arrayOfInt1;
/*  212 */       } catch (Exception exception) {
/*  213 */         exception.printStackTrace();
/*  214 */         return null;
/*      */       } 
/*      */     } 
/*      */ 
/*      */     
/*  219 */     this.rs.executeProc("HrmRoleSR_SeByURId", "" + paramInt + this.flag + paramString);
/*  220 */     while (this.rs.next()) {
/*  221 */       if (this.rs.getInt("rightlevel") > -1) {
/*  222 */         arrayList.add(String.valueOf(this.rs.getInt("subcompanyid"))); continue;
/*  223 */       }  if (this.rs.getInt("rightlevel") == -1) {
/*  224 */         arrayList.add(String.valueOf(-1 * this.rs.getInt("subcompanyid")));
/*      */       }
/*      */     } 
/*      */     
/*  228 */     int[] arrayOfInt = new int[arrayList.size()];
/*  229 */     for (byte b = 0; b < arrayList.size(); b++) {
/*  230 */       arrayOfInt[b] = Integer.parseInt((String)arrayList.get(b));
/*      */     }
/*  232 */     return arrayOfInt;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public int[] getSubComByUserRightId(int paramInt1, String paramString, int paramInt2) {
/*  243 */     ArrayList<String> arrayList = new ArrayList();
/*  244 */     this.rs = new RecordSet();
/*      */     
/*  246 */     this.rs.executeSql("select detachable from SystemSet");
/*  247 */     int i = 0;
/*  248 */     if (this.rs.next()) {
/*  249 */       i = this.rs.getInt("detachable");
/*      */     }
/*  251 */     i = getDetachableByRight(paramString) ? 1 : 0;
/*  252 */     boolean bool = HrmSanyuanAdminBiz.canViewAllSubCompany(paramInt1, paramString);
/*  253 */     if (paramInt1 == 1 || bool) {
/*  254 */       this.rs.executeProc("HrmSubCompany_Select", "");
/*  255 */       int[] arrayOfInt1 = new int[this.rs.getCounts()];
/*  256 */       Arrays.fill(arrayOfInt1, -1);
/*  257 */       byte b1 = 0;
/*  258 */       while (this.rs.next()) {
/*  259 */         String str = this.rs.getString("canceled");
/*  260 */         if (this.showCanceled || "0".equals(str) || "".equals(str)) {
/*  261 */           arrayOfInt1[b1] = this.rs.getInt("id");
/*  262 */           b1++;
/*      */         } 
/*      */       } 
/*  265 */       return arrayOfInt1;
/*  266 */     }  if (i == 0) {
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*  276 */       HrmCommonServiceImpl hrmCommonServiceImpl = new HrmCommonServiceImpl();
/*  277 */       int j = hrmCommonServiceImpl.getMaxRoleLevel(paramInt1, paramString);
/*  278 */       if (j > -1 && j < 2)
/*      */         try {
/*  280 */           ResourceComInfo resourceComInfo = new ResourceComInfo();
/*  281 */           DepartmentComInfo departmentComInfo = new DepartmentComInfo();
/*  282 */           SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/*  283 */           String str1 = resourceComInfo.getDepartmentID("" + paramInt1);
/*  284 */           String str2 = departmentComInfo.getSubcompanyid1(str1);
/*  285 */           ArrayList<String> arrayList1 = new ArrayList();
/*  286 */           arrayList1.add(str2);
/*  287 */           int[] arrayOfInt2 = new int[arrayList1.size()];
/*  288 */           for (byte b2 = 0; b2 < arrayList1.size(); b2++) {
/*  289 */             if (null != arrayList1.get(b2) && !"".equals(arrayList1.get(b2)))
/*  290 */               arrayOfInt2[b2] = Integer.parseInt((String)arrayList1.get(b2)); 
/*      */           } 
/*  292 */           return arrayOfInt2;
/*  293 */         } catch (Exception exception) {
/*  294 */           writeLog(exception);
/*  295 */           return new int[0];
/*      */         }  
/*  297 */       if (j < 0) {
/*  298 */         return new int[0];
/*      */       }
/*  300 */       this.rs.executeProc("HrmSubCompany_Select", "");
/*  301 */       int[] arrayOfInt1 = new int[this.rs.getCounts()];
/*  302 */       Arrays.fill(arrayOfInt1, -1);
/*  303 */       byte b1 = 0;
/*  304 */       while (this.rs.next()) {
/*  305 */         String str = this.rs.getString("canceled");
/*  306 */         if (this.showCanceled || "0".equals(str) || "".equals(str)) {
/*  307 */           arrayOfInt1[b1] = this.rs.getInt("id");
/*  308 */           b1++;
/*      */         } 
/*      */       } 
/*  311 */       return arrayOfInt1;
/*      */     } 
/*      */     
/*  314 */     this.rs.executeProc("HrmRoleSR_SeByURId", "" + paramInt1 + this.flag + paramString);
/*  315 */     while (this.rs.next()) {
/*  316 */       if (this.rs.getInt("rightlevel") >= paramInt2) {
/*  317 */         arrayList.add(String.valueOf(this.rs.getInt("subcompanyid")));
/*      */       }
/*      */     } 
/*  320 */     int[] arrayOfInt = new int[arrayList.size()];
/*  321 */     for (byte b = 0; b < arrayList.size(); b++) {
/*  322 */       arrayOfInt[b] = Integer.parseInt((String)arrayList.get(b));
/*      */     }
/*  324 */     return arrayOfInt;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public int[] getSubComPathByUserRightId(int paramInt, String paramString) {
/*  334 */     ArrayList<String> arrayList = new ArrayList();
/*  335 */     this.rs = new RecordSet();
/*      */     
/*  337 */     this.rs.executeSql("select detachable,hrmdetachable from SystemSet");
/*  338 */     int i = 0;
/*      */     
/*  340 */     if (this.rs.next()) {
/*  341 */       i = this.rs.getInt("detachable");
/*      */     }
/*      */     
/*  344 */     i = getDetachableByRight(paramString) ? 1 : 0;
/*  345 */     if (paramInt == 1 || i == 0) {
/*      */       
/*      */       try {
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */         
/*  357 */         SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/*  358 */         subCompanyComInfo.setTofirstRow();
/*  359 */         int[] arrayOfInt1 = new int[subCompanyComInfo.getCompanyNum()];
/*  360 */         byte b1 = 0;
/*  361 */         while (subCompanyComInfo.next()) {
/*  362 */           arrayOfInt1[b1] = Integer.parseInt(subCompanyComInfo.getSubCompanyid());
/*  363 */           b1++;
/*      */         } 
/*  365 */         return arrayOfInt1;
/*  366 */       } catch (Exception exception) {
/*  367 */         exception.printStackTrace();
/*  368 */         return null;
/*      */       } 
/*      */     }
/*  371 */     if (paramString.equals(""))
/*      */     {
/*  373 */       paramString = "0";
/*      */     }
/*  375 */     this.rs.executeProc("HrmRSRPath_SeByURId", "" + paramInt + this.flag + paramString);
/*  376 */     while (this.rs.next())
/*      */     {
/*  378 */       arrayList.add(String.valueOf(this.rs.getInt("subcompanyid")));
/*      */     }
/*      */ 
/*      */     
/*  382 */     int[] arrayOfInt = new int[arrayList.size()];
/*  383 */     for (byte b = 0; b < arrayList.size(); b++) {
/*  384 */       arrayOfInt[b] = Integer.parseInt((String)arrayList.get(b));
/*      */     }
/*  386 */     return arrayOfInt;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public int[] getSubComPathByUserRightId(int paramInt1, String paramString, int paramInt2) {
/*  398 */     ArrayList<String> arrayList = new ArrayList();
/*  399 */     this.rs = new RecordSet();
/*      */     
/*  401 */     this.rs.executeSql("select detachable from SystemSet");
/*  402 */     int i = 0;
/*  403 */     if (this.rs.next()) {
/*  404 */       i = this.rs.getInt("detachable");
/*      */     }
/*      */     
/*  407 */     i = getDetachableByRight(paramString) ? 1 : 0;
/*  408 */     boolean bool = HrmSanyuanAdminBiz.canViewAllSubCompany(paramInt1, paramString);
/*  409 */     if (paramInt1 == 1 || bool)
/*      */       
/*      */       try {
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */         
/*  425 */         SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/*  426 */         subCompanyComInfo.setTofirstRow();
/*  427 */         int[] arrayOfInt1 = new int[subCompanyComInfo.getCompanyNum()];
/*  428 */         byte b1 = 0;
/*  429 */         while (subCompanyComInfo.next()) {
/*  430 */           if (this.showCanceled || "0".equals(subCompanyComInfo.getCompanyiscanceled()) || "".equals(subCompanyComInfo.getCompanyiscanceled())) {
/*  431 */             arrayOfInt1[b1] = Integer.parseInt(subCompanyComInfo.getSubCompanyid());
/*  432 */             b1++;
/*      */           } 
/*      */         } 
/*  435 */         return arrayOfInt1;
/*  436 */       } catch (Exception exception) {
/*  437 */         exception.printStackTrace();
/*  438 */         return null;
/*      */       }  
/*  440 */     if (i == 0) {
/*  441 */       HrmCommonServiceImpl hrmCommonServiceImpl = new HrmCommonServiceImpl();
/*  442 */       int j = hrmCommonServiceImpl.getMaxRoleLevel(paramInt1, paramString);
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*  452 */       if (j > -1 && j < 2)
/*      */         try {
/*  454 */           ResourceComInfo resourceComInfo = new ResourceComInfo();
/*  455 */           DepartmentComInfo departmentComInfo = new DepartmentComInfo();
/*  456 */           SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/*  457 */           String str1 = resourceComInfo.getDepartmentID("" + paramInt1);
/*  458 */           String str2 = departmentComInfo.getSubcompanyid1(str1);
/*  459 */           ArrayList<String> arrayList1 = new ArrayList();
/*  460 */           arrayList1.add(str2);
/*  461 */           str2 = subCompanyComInfo.getSupsubcomid(str2);
/*  462 */           while (!str2.equals("0") && !str2.equals("")) {
/*  463 */             arrayList1.add(str2);
/*  464 */             str2 = subCompanyComInfo.getSupsubcomid(str2);
/*      */           } 
/*  466 */           int[] arrayOfInt1 = new int[arrayList1.size()];
/*  467 */           for (byte b1 = 0; b1 < arrayList1.size(); b1++) {
/*  468 */             if (null != arrayList1.get(b1) && !"".equals(arrayList1.get(b1)))
/*  469 */               arrayOfInt1[b1] = Integer.parseInt((String)arrayList1.get(b1)); 
/*      */           } 
/*  471 */           return arrayOfInt1;
/*  472 */         } catch (Exception exception) {
/*  473 */           writeLog(exception);
/*  474 */           return new int[0];
/*      */         }  
/*  476 */       if (j < 0) {
/*  477 */         return new int[0];
/*      */       }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*      */       try {
/*  494 */         SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/*  495 */         subCompanyComInfo.setTofirstRow();
/*  496 */         int[] arrayOfInt1 = new int[subCompanyComInfo.getCompanyNum()];
/*  497 */         byte b1 = 0;
/*  498 */         while (subCompanyComInfo.next()) {
/*  499 */           if (this.showCanceled || "0".equals(subCompanyComInfo.getCompanyiscanceled()) || "".equals(subCompanyComInfo.getCompanyiscanceled())) {
/*  500 */             arrayOfInt1[b1] = Integer.parseInt(subCompanyComInfo.getSubCompanyid());
/*  501 */             b1++;
/*      */           } 
/*      */         } 
/*  504 */         return arrayOfInt1;
/*  505 */       } catch (Exception exception) {
/*  506 */         exception.printStackTrace();
/*  507 */         return null;
/*      */       } 
/*      */     } 
/*      */     
/*  511 */     if (paramString.equals(""))
/*      */     {
/*  513 */       paramString = "0";
/*      */     }
/*  515 */     this.rs.executeProc("HrmEditRightPath_SeByURId", "" + paramInt1 + this.flag + paramString + this.flag + paramInt2);
/*  516 */     while (this.rs.next()) {
/*  517 */       arrayList.add(String.valueOf(this.rs.getInt("subcompanyid")));
/*      */     }
/*  519 */     if (arrayList.size() < 1 && 
/*  520 */       this.detachable == 1) {
/*  521 */       OrganizationUtil organizationUtil = new OrganizationUtil();
/*  522 */       organizationUtil.selectData(paramInt1, this.fieldid + "", this.isdetail, this.isbill);
/*  523 */       ArrayList arrayList1 = organizationUtil.getSubcomList();
/*  524 */       for (byte b1 = 0; b1 < arrayList1.size(); b1++) {
/*  525 */         String str = (new StringBuilder()).append(arrayList1.get(b1)).append("").toString();
/*  526 */         if (!arrayList.contains(str)) {
/*  527 */           arrayList.add(str);
/*      */         }
/*      */       } 
/*      */     } 
/*      */     
/*  532 */     int[] arrayOfInt = new int[arrayList.size()];
/*  533 */     for (byte b = 0; b < arrayList.size(); b++) {
/*  534 */       arrayOfInt[b] = Integer.parseInt((String)arrayList.get(b));
/*      */     }
/*  536 */     return arrayOfInt;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*  547 */   public static Map<String, Integer> mapHrmRoleSR = null;
/*      */   public int ChkComRightByUserRightCompanyId(int paramInt1, String paramString, int paramInt2) {
/*  549 */     int i = -1;
/*  550 */     this.detachable = getDetachableByRight(paramString) ? 1 : 0;
/*  551 */     if (this.detachable == 0) return i; 
/*  552 */     String str = paramInt1 + "&" + paramString + "&" + paramInt2;
/*  553 */     mapHrmRoleSR = (Map<String, Integer>)this.staticobj.getObject("mapHrmRoleSR");
/*  554 */     if (mapHrmRoleSR == null)
/*      */     {
/*  556 */       mapHrmRoleSR = new ConcurrentHashMap<>();
/*      */     }
/*  558 */     boolean bool = HrmSanyuanAdminBiz.canViewAllSubCompany(paramInt1, paramString);
/*  559 */     if (paramInt1 == 1 || bool) {
/*  560 */       i = 2;
/*      */     } else {
/*  562 */       if (paramString.equals(""))
/*      */       {
/*  564 */         paramString = "0";
/*      */       }
/*      */       
/*  567 */       if (mapHrmRoleSR.get(str) != null) {
/*  568 */         i = ((Integer)mapHrmRoleSR.get(str)).intValue();
/*      */       } else {
/*  570 */         this.rs.executeProc("HrmRoleSR_SByURCId", "" + paramInt1 + this.flag + paramString + this.flag + paramInt2);
/*  571 */         if (this.rs.next()) {
/*  572 */           i = this.rs.getInt("rightlevel");
/*      */         }
/*  574 */         mapHrmRoleSR.put(str, Integer.valueOf(i));
/*      */       } 
/*      */     } 
/*      */     
/*  578 */     if (i < 0) {
/*  579 */       String str1 = "";
/*      */       try {
/*  581 */         HrmUserSettingComInfo hrmUserSettingComInfo = new HrmUserSettingComInfo();
/*  582 */         str1 = hrmUserSettingComInfo.getBelongtoshowByUserId("" + paramInt1);
/*  583 */       } catch (Exception exception) {}
/*      */ 
/*      */       
/*  586 */       if (str1.equals("1")) {
/*  587 */         List list = User.getBelongtoUsersByUserId(paramInt1);
/*  588 */         if (list != null)
/*  589 */           for (User user : list) {
/*  590 */             paramInt1 = ((User)user).getUID();
/*  591 */             str = paramInt1 + "&" + paramString + "&" + paramInt2;
/*  592 */             if (paramInt1 == 1) {
/*  593 */               i = 2;
/*      */             } else {
/*  595 */               if (paramString.equals(""))
/*      */               {
/*  597 */                 paramString = "0";
/*      */               }
/*      */               
/*  600 */               if (mapHrmRoleSR.get(str) != null) {
/*  601 */                 i = ((Integer)mapHrmRoleSR.get(str)).intValue();
/*      */               } else {
/*  603 */                 this.rs.executeProc("HrmRoleSR_SByURCId", "" + paramInt1 + this.flag + paramString + this.flag + paramInt2);
/*  604 */                 if (this.rs.next()) {
/*  605 */                   i = this.rs.getInt("rightlevel");
/*      */                 }
/*  607 */                 mapHrmRoleSR.put(str, Integer.valueOf(i));
/*      */               } 
/*      */             } 
/*  610 */             if (i >= 0)
/*      */               break; 
/*      */           }  
/*      */       } 
/*      */     } 
/*  615 */     this.staticobj.putObject("mapHrmRoleSR", mapHrmRoleSR);
/*  616 */     return i;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public int[] getSubComByUserEditRightId(int paramInt, String paramString) {
/*  626 */     ArrayList<String> arrayList = new ArrayList();
/*  627 */     this.rs = new RecordSet();
/*      */     
/*  629 */     this.rs.executeSql("select detachable from SystemSet");
/*  630 */     int i = 0;
/*  631 */     if (this.rs.next()) {
/*  632 */       i = this.rs.getInt("detachable");
/*      */     }
/*  634 */     i = getDetachableByRight(paramString) ? 1 : 0;
/*  635 */     boolean bool = HrmSanyuanAdminBiz.canViewAllSubCompany(paramInt, paramString);
/*  636 */     if (paramInt == 1 || i == 0 || bool) {
/*      */       
/*      */       try {
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */         
/*  648 */         SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/*  649 */         subCompanyComInfo.setTofirstRow();
/*  650 */         int[] arrayOfInt1 = new int[subCompanyComInfo.getCompanyNum()];
/*  651 */         byte b1 = 0;
/*  652 */         while (subCompanyComInfo.next()) {
/*  653 */           arrayOfInt1[b1] = Integer.parseInt(subCompanyComInfo.getSubCompanyid());
/*  654 */           b1++;
/*      */         } 
/*  656 */         return arrayOfInt1;
/*  657 */       } catch (Exception exception) {
/*  658 */         exception.printStackTrace();
/*  659 */         return null;
/*      */       } 
/*      */     }
/*      */     
/*  663 */     if (paramString.equals(""))
/*      */     {
/*  665 */       paramString = "0";
/*      */     }
/*  667 */     this.rs.executeProc("HrmRoleSR_SeByURId", "" + paramInt + this.flag + paramString);
/*  668 */     while (this.rs.next()) {
/*  669 */       if (this.rs.getInt("rightlevel") > 0) {
/*  670 */         arrayList.add(String.valueOf(this.rs.getInt("subcompanyid"))); continue;
/*  671 */       }  if (this.rs.getInt("rightlevel") == -1) {
/*  672 */         arrayList.add(String.valueOf(-1 * this.rs.getInt("subcompanyid")));
/*      */       }
/*      */     } 
/*  675 */     if (arrayList.size() < 1 && 
/*  676 */       this.detachable == 1) {
/*  677 */       OrganizationUtil organizationUtil = new OrganizationUtil();
/*  678 */       organizationUtil.selectData(paramInt, this.fieldid + "", this.isdetail, this.isbill);
/*  679 */       ArrayList arrayList1 = organizationUtil.getSubcomList();
/*  680 */       for (byte b1 = 0; b1 < arrayList1.size(); b1++) {
/*  681 */         String str = (new StringBuilder()).append(arrayList1.get(b1)).append("").toString();
/*  682 */         if (!arrayList.contains(str)) {
/*  683 */           arrayList.add(str);
/*      */         }
/*      */       } 
/*      */     } 
/*      */     
/*  688 */     int[] arrayOfInt = new int[arrayList.size()];
/*  689 */     for (byte b = 0; b < arrayList.size(); b++) {
/*  690 */       arrayOfInt[b] = Integer.parseInt((String)arrayList.get(b));
/*      */     }
/*  692 */     return arrayOfInt;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public int[] getSubComByDecUserRightId(int paramInt, String paramString) {
/*  703 */     ArrayList<String> arrayList = new ArrayList();
/*  704 */     this.rs = new RecordSet();
/*      */     
/*  706 */     this.rs.executeSql("select detachable from SystemSet");
/*  707 */     int i = 0;
/*  708 */     if (this.rs.next()) {
/*  709 */       i = this.rs.getInt("detachable");
/*      */     }
/*  711 */     i = getDetachableByRight(paramString) ? 1 : 0;
/*  712 */     boolean bool = HrmSanyuanAdminBiz.canViewAllSubCompany(paramInt, paramString);
/*  713 */     if (paramInt == 1 || bool)
/*      */       
/*      */       try {
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */         
/*  725 */         SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/*  726 */         subCompanyComInfo.setTofirstRow();
/*  727 */         int[] arrayOfInt1 = new int[subCompanyComInfo.getCompanyNum()];
/*  728 */         byte b1 = 0;
/*  729 */         while (subCompanyComInfo.next()) {
/*  730 */           arrayOfInt1[b1] = Integer.parseInt(subCompanyComInfo.getSubCompanyid());
/*  731 */           b1++;
/*      */         } 
/*  733 */         return arrayOfInt1;
/*  734 */       } catch (Exception exception) {
/*  735 */         exception.printStackTrace();
/*  736 */         return null;
/*      */       }  
/*  738 */     if (i == 0) {
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*  748 */       HrmCommonServiceImpl hrmCommonServiceImpl = new HrmCommonServiceImpl();
/*  749 */       int j = hrmCommonServiceImpl.getMaxRoleLevel(paramInt, paramString);
/*  750 */       if (paramString != null && paramString.toLowerCase().equals("subcompanys:decentralization")) {
/*  751 */         j = 2;
/*      */       }
/*  753 */       if (j > -1 && j < 2)
/*      */         try {
/*  755 */           ResourceComInfo resourceComInfo = new ResourceComInfo();
/*  756 */           DepartmentComInfo departmentComInfo = new DepartmentComInfo();
/*  757 */           String str1 = resourceComInfo.getDepartmentID("" + paramInt);
/*  758 */           String str2 = departmentComInfo.getSubcompanyid1(str1);
/*  759 */           return new int[] { Util.getIntValue(str2) };
/*  760 */         } catch (Exception exception) {
/*  761 */           writeLog(exception);
/*  762 */           return new int[0];
/*      */         }  
/*  764 */       if (j < 0) {
/*  765 */         return new int[0];
/*      */       }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*      */       try {
/*  779 */         SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/*  780 */         subCompanyComInfo.setTofirstRow();
/*  781 */         int[] arrayOfInt1 = new int[subCompanyComInfo.getCompanyNum()];
/*  782 */         byte b1 = 0;
/*  783 */         while (subCompanyComInfo.next()) {
/*  784 */           arrayOfInt1[b1] = Integer.parseInt(subCompanyComInfo.getSubCompanyid());
/*  785 */           b1++;
/*      */         } 
/*  787 */         return arrayOfInt1;
/*  788 */       } catch (Exception exception) {
/*  789 */         exception.printStackTrace();
/*  790 */         return null;
/*      */       } 
/*      */     } 
/*      */     
/*  794 */     this.rs.executeProc("HrmRoleSR_SeByURId", "" + paramInt + this.flag + paramString);
/*  795 */     while (this.rs.next()) {
/*  796 */       if (this.rs.getInt("rightlevel") > -1) {
/*  797 */         arrayList.add(String.valueOf(this.rs.getInt("subcompanyid"))); continue;
/*  798 */       }  if (this.rs.getInt("rightlevel") == -1) {
/*  799 */         arrayList.add(String.valueOf(-1 * this.rs.getInt("subcompanyid")));
/*      */       }
/*      */     } 
/*  802 */     if (arrayList.size() < 1) {
/*      */       try {
/*  804 */         ResourceComInfo resourceComInfo = new ResourceComInfo();
/*  805 */         DepartmentComInfo departmentComInfo = new DepartmentComInfo();
/*  806 */         SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/*  807 */         String str1 = resourceComInfo.getDepartmentID("" + paramInt);
/*  808 */         String str2 = departmentComInfo.getSubcompanyid1(str1);
/*  809 */         arrayList.add(str2);
/*  810 */         if (this.detachable == 1) {
/*  811 */           OrganizationUtil organizationUtil = new OrganizationUtil();
/*  812 */           organizationUtil.selectData(paramInt, this.fieldid + "", this.isdetail, this.isbill);
/*  813 */           ArrayList arrayList1 = organizationUtil.getSubcomList();
/*  814 */           for (byte b1 = 0; b1 < arrayList1.size(); b1++) {
/*  815 */             String str = (new StringBuilder()).append(arrayList1.get(b1)).append("").toString();
/*  816 */             if (!arrayList.contains(str)) {
/*  817 */               arrayList.add(str);
/*      */             }
/*      */           } 
/*      */         } 
/*  821 */       } catch (Exception exception) {
/*  822 */         writeLog(exception);
/*  823 */         return new int[0];
/*      */       } 
/*      */     }
/*  826 */     int[] arrayOfInt = new int[arrayList.size()];
/*  827 */     for (byte b = 0; b < arrayList.size(); b++) {
/*  828 */       if (Util.null2String(arrayList.get(b)).length() != 0)
/*  829 */         arrayOfInt[b] = Integer.parseInt((String)arrayList.get(b)); 
/*      */     } 
/*  831 */     return arrayOfInt;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public int[] getSubComByDecUserRightId2(int paramInt, String paramString) {
/*  842 */     ArrayList<String> arrayList = new ArrayList();
/*  843 */     this.rs = new RecordSet();
/*      */     
/*  845 */     this.rs.executeSql("select detachable from SystemSet");
/*  846 */     int i = 0;
/*  847 */     if (this.rs.next()) {
/*  848 */       i = this.rs.getInt("detachable");
/*      */     }
/*  850 */     i = getDetachableByRight(paramString) ? 1 : 0;
/*  851 */     boolean bool = HrmSanyuanAdminBiz.canViewAllSubCompany(paramInt, paramString);
/*  852 */     if (paramInt == 1 || bool)
/*      */       
/*      */       try {
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */         
/*  864 */         SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/*  865 */         subCompanyComInfo.setTofirstRow();
/*  866 */         int[] arrayOfInt1 = new int[subCompanyComInfo.getCompanyNum()];
/*  867 */         byte b1 = 0;
/*  868 */         while (subCompanyComInfo.next()) {
/*  869 */           arrayOfInt1[b1] = Integer.parseInt(subCompanyComInfo.getSubCompanyid());
/*  870 */           b1++;
/*      */         } 
/*  872 */         return arrayOfInt1;
/*  873 */       } catch (Exception exception) {
/*  874 */         exception.printStackTrace();
/*  875 */         return null;
/*      */       }  
/*  877 */     if (paramString != null && paramString.toLowerCase().equals("subcompanys:decentralization"))
/*  878 */     { ResourceComInfo resourceComInfo = null;
/*      */       
/*  880 */       try { resourceComInfo = new ResourceComInfo();
/*  881 */         String str = resourceComInfo.getSubCompanyID(StringUtil.vString(Integer.valueOf(paramInt)));
/*  882 */         int[] arrayOfInt1 = new int[1];
/*  883 */         arrayOfInt1[0] = Integer.parseInt(str);
/*  884 */         return arrayOfInt1; }
/*  885 */       catch (Exception exception)
/*  886 */       { exception.printStackTrace();
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */         
/*  996 */         return new int[0]; }  }  if (i == 0) { HrmCommonServiceImpl hrmCommonServiceImpl = new HrmCommonServiceImpl(); int j = hrmCommonServiceImpl.getMaxRoleLevel(paramInt, paramString); if (paramString != null && paramString.toLowerCase().equals("subcompanys:decentralization"))
/*      */         j = 2;  if (j > -1 && j < 2)
/*      */         try { ResourceComInfo resourceComInfo = new ResourceComInfo(); DepartmentComInfo departmentComInfo = new DepartmentComInfo(); String str1 = resourceComInfo.getDepartmentID("" + paramInt); String str2 = departmentComInfo.getSubcompanyid1(str1); return new int[] { Util.getIntValue(str2) }; } catch (Exception exception) { writeLog(exception); return new int[0]; }   if (j < 0)
/*      */         return new int[0];  try { SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo(); subCompanyComInfo.setTofirstRow(); int[] arrayOfInt1 = new int[subCompanyComInfo.getCompanyNum()]; byte b1 = 0; while (subCompanyComInfo.next()) { arrayOfInt1[b1] = Integer.parseInt(subCompanyComInfo.getSubCompanyid()); b1++; }  return arrayOfInt1; } catch (Exception exception) { exception.printStackTrace(); return null; }  }  this.rs.executeProc("HrmRoleSR_SeByURId", "" + paramInt + this.flag + paramString); while (this.rs.next()) { if (this.rs.getInt("rightlevel") > -1) { arrayList.add(String.valueOf(this.rs.getInt("subcompanyid"))); continue; }  if (this.rs.getInt("rightlevel") == -1)
/*      */         arrayList.add(String.valueOf(-1 * this.rs.getInt("subcompanyid")));  }  if (arrayList.size() < 1)
/*      */       try { String str1 = ""; ArrayList<String> arrayList1 = new ArrayList(); RecordSet recordSet = new RecordSet(); String str2 = "select * from HrmResourceManager where  id='" + paramInt + "'  "; recordSet.executeSql(str2); if (recordSet.next())
/*      */           str1 = recordSet.getString("subcompanyids");  if (str1 != null && !"".equals(str1))
/*      */           arrayList1 = Util.TokenizerString(str1, ",");  if (arrayList1 != null && arrayList1.size() > 0)
/*      */           for (byte b1 = 0; b1 < arrayList1.size(); b1++) { String str = arrayList1.get(b1); arrayList.add(str); }   }
/*      */       catch (Exception exception) { writeLog(exception); return new int[0]; }
/*      */         int[] arrayOfInt = new int[arrayList.size()]; for (byte b = 0; b < arrayList.size(); b++)
/* 1007 */       arrayOfInt[b] = Integer.parseInt((String)arrayList.get(b));  return arrayOfInt; } public int[] getSubComPathByDecUserRightId(int paramInt1, String paramString, int paramInt2) { ArrayList<String> arrayList = new ArrayList();
/* 1008 */     this.rs = new RecordSet();
/* 1009 */     this.rs.executeSql("select detachable from SystemSet");
/* 1010 */     int i = 0;
/* 1011 */     if (this.rs.next()) {
/* 1012 */       i = this.rs.getInt("detachable");
/*      */     }
/* 1014 */     i = getDetachableByRight(paramString) ? 1 : 0;
/* 1015 */     boolean bool = HrmSanyuanAdminBiz.canViewAllSubCompany(paramInt1, paramString);
/* 1016 */     if (paramInt1 == 1 || bool)
/*      */       
/*      */       try {
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */         
/* 1032 */         SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/* 1033 */         subCompanyComInfo.setTofirstRow();
/* 1034 */         int[] arrayOfInt1 = new int[subCompanyComInfo.getCompanyNum()];
/* 1035 */         byte b1 = 0;
/* 1036 */         while (subCompanyComInfo.next()) {
/* 1037 */           if (this.showCanceled || "0".equals(subCompanyComInfo.getCompanyiscanceled()) || "".equals(subCompanyComInfo.getCompanyiscanceled())) {
/* 1038 */             arrayOfInt1[b1] = Integer.parseInt(subCompanyComInfo.getSubCompanyid());
/* 1039 */             b1++;
/*      */           } 
/*      */         } 
/* 1042 */         return arrayOfInt1;
/* 1043 */       } catch (Exception exception) {
/* 1044 */         exception.printStackTrace();
/* 1045 */         return null;
/*      */       }  
/* 1047 */     if (paramString != null && paramString.toLowerCase().equals("subcompanys:decentralization"))
/* 1048 */     { ResourceComInfo resourceComInfo = null;
/* 1049 */       SubCompanyComInfo subCompanyComInfo = null;
/*      */       
/* 1051 */       try { resourceComInfo = new ResourceComInfo();
/* 1052 */         subCompanyComInfo = new SubCompanyComInfo();
/* 1053 */         String str1 = resourceComInfo.getSubCompanyID(StringUtil.vString(Integer.valueOf(paramInt1)));
/*      */         
/* 1055 */         String str2 = subCompanyComInfo.getAllSupCompany(str1);
/* 1056 */         if ("".equals(str2)) {
/* 1057 */           str2 = str1;
/*      */         } else {
/* 1059 */           str2 = str2 + str1;
/*      */         } 
/* 1061 */         String[] arrayOfString = str2.split(",");
/* 1062 */         int[] arrayOfInt1 = new int[arrayOfString.length];
/* 1063 */         for (byte b1 = 0; b1 < arrayOfString.length; b1++) {
/* 1064 */           arrayOfInt1[b1] = Integer.parseInt(arrayOfString[b1]);
/*      */         }
/* 1066 */         return arrayOfInt1; }
/* 1067 */       catch (Exception exception)
/* 1068 */       { exception.printStackTrace();
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */         
/* 1176 */         return new int[0]; }  }  if (i == 0) { HrmCommonServiceImpl hrmCommonServiceImpl = new HrmCommonServiceImpl(); int j = hrmCommonServiceImpl.getMaxRoleLevel(paramInt1, paramString); if (paramString != null && paramString.toLowerCase().equals("subcompanys:decentralization"))
/*      */         j = 2;  if (j > -1 && j < 2)
/*      */         try { ResourceComInfo resourceComInfo = new ResourceComInfo(); DepartmentComInfo departmentComInfo = new DepartmentComInfo(); SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo(); String str1 = resourceComInfo.getDepartmentID("" + paramInt1); String str2 = departmentComInfo.getSubcompanyid1(str1); ArrayList<String> arrayList1 = new ArrayList(); arrayList1.add(str2); str2 = subCompanyComInfo.getSupsubcomid(str2); while (!str2.equals("0") && !str2.equals("")) { arrayList1.add(str2); str2 = subCompanyComInfo.getSupsubcomid(str2); }  int[] arrayOfInt1 = new int[arrayList1.size()]; for (byte b1 = 0; b1 < arrayList1.size(); b1++)
/*      */             arrayOfInt1[b1] = Integer.parseInt((String)arrayList1.get(b1));  return arrayOfInt1; } catch (Exception exception) { writeLog(exception); return new int[0]; }   if (j < 0)
/*      */         return new int[0];  try { SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo(); subCompanyComInfo.setTofirstRow(); int[] arrayOfInt1 = new int[subCompanyComInfo.getCompanyNum()]; byte b1 = 0; while (subCompanyComInfo.next()) { if (this.showCanceled || "0".equals(subCompanyComInfo.getCompanyiscanceled()) || "".equals(subCompanyComInfo.getCompanyiscanceled())) { arrayOfInt1[b1] = Integer.parseInt(subCompanyComInfo.getSubCompanyid()); b1++; }  }  return arrayOfInt1; } catch (Exception exception) { exception.printStackTrace(); return null; }  }  if (paramString.equals(""))
/*      */       paramString = "0";  this.rs.executeProc("HrmEditRightPath_SeByURId", "" + paramInt1 + this.flag + paramString + this.flag + paramInt2); while (this.rs.next())
/*      */       arrayList.add(String.valueOf(this.rs.getInt("subcompanyid")));  if (arrayList.size() < 1)
/*      */       try { ResourceComInfo resourceComInfo = new ResourceComInfo(); DepartmentComInfo departmentComInfo = new DepartmentComInfo(); SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo(); String str1 = resourceComInfo.getDepartmentID("" + paramInt1); String str2 = departmentComInfo.getSubcompanyid1(str1); arrayList.add(str2); str2 = subCompanyComInfo.getSupsubcomid(str2); while (!str2.equals("0") && !str2.equals("")) { arrayList.add(str2); str2 = subCompanyComInfo.getSupsubcomid(str2); }  }
/*      */       catch (Exception exception) { writeLog(exception); return new int[0]; }
/*      */         int[] arrayOfInt = new int[arrayList.size()]; for (byte b = 0; b < arrayList.size(); b++) { if (Util.null2String(arrayList.get(b)).length() != 0)
/*      */         arrayOfInt[b] = Integer.parseInt((String)arrayList.get(b));  }
/* 1187 */      return arrayOfInt; } public int[] getSubComPathByDecUserRightId2(int paramInt1, String paramString, int paramInt2) { ArrayList<String> arrayList = new ArrayList();
/* 1188 */     this.rs = new RecordSet();
/* 1189 */     this.rs.executeSql("select detachable from SystemSet");
/* 1190 */     int i = 0;
/* 1191 */     if (this.rs.next()) {
/* 1192 */       i = this.rs.getInt("detachable");
/*      */     }
/* 1194 */     i = getDetachableByRight(paramString) ? 1 : 0;
/* 1195 */     boolean bool = HrmSanyuanAdminBiz.canViewAllSubCompany(paramInt1, paramString);
/* 1196 */     if (paramInt1 == 1 || bool)
/*      */       
/*      */       try {
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */         
/* 1212 */         SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/* 1213 */         subCompanyComInfo.setTofirstRow();
/* 1214 */         int[] arrayOfInt1 = new int[subCompanyComInfo.getCompanyNum()];
/* 1215 */         byte b1 = 0;
/* 1216 */         while (subCompanyComInfo.next()) {
/* 1217 */           if (this.showCanceled || "0".equals(subCompanyComInfo.getCompanyiscanceled()) || "".equals(subCompanyComInfo.getCompanyiscanceled())) {
/* 1218 */             arrayOfInt1[b1] = Integer.parseInt(subCompanyComInfo.getSubCompanyid());
/* 1219 */             b1++;
/*      */           } 
/*      */         } 
/* 1222 */         return arrayOfInt1;
/* 1223 */       } catch (Exception exception) {
/* 1224 */         exception.printStackTrace();
/* 1225 */         return null;
/*      */       }  
/* 1227 */     if (i == 0) {
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/* 1237 */       HrmCommonServiceImpl hrmCommonServiceImpl = new HrmCommonServiceImpl();
/* 1238 */       int j = hrmCommonServiceImpl.getMaxRoleLevel(paramInt1, paramString);
/* 1239 */       if (paramString != null && paramString.toLowerCase().equals("subcompanys:decentralization")) {
/* 1240 */         j = 2;
/*      */       }
/* 1242 */       if (j > -1 && j < 2)
/*      */         try {
/* 1244 */           ResourceComInfo resourceComInfo = new ResourceComInfo();
/* 1245 */           DepartmentComInfo departmentComInfo = new DepartmentComInfo();
/* 1246 */           SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/* 1247 */           String str1 = resourceComInfo.getDepartmentID("" + paramInt1);
/* 1248 */           String str2 = departmentComInfo.getSubcompanyid1(str1);
/* 1249 */           ArrayList<String> arrayList1 = new ArrayList();
/* 1250 */           arrayList1.add(str2);
/* 1251 */           str2 = subCompanyComInfo.getSupsubcomid(str2);
/* 1252 */           while (!str2.equals("0") && !str2.equals("")) {
/* 1253 */             arrayList1.add(str2);
/* 1254 */             str2 = subCompanyComInfo.getSupsubcomid(str2);
/*      */           } 
/* 1256 */           int[] arrayOfInt1 = new int[arrayList1.size()];
/* 1257 */           for (byte b1 = 0; b1 < arrayList1.size(); b1++) {
/* 1258 */             arrayOfInt1[b1] = Integer.parseInt((String)arrayList1.get(b1));
/*      */           }
/* 1260 */           return arrayOfInt1;
/* 1261 */         } catch (Exception exception) {
/* 1262 */           writeLog(exception);
/* 1263 */           return new int[0];
/*      */         }  
/* 1265 */       if (j < 0) {
/* 1266 */         return new int[0];
/*      */       }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*      */       try {
/* 1283 */         SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/* 1284 */         subCompanyComInfo.setTofirstRow();
/* 1285 */         int[] arrayOfInt1 = new int[subCompanyComInfo.getCompanyNum()];
/* 1286 */         byte b1 = 0;
/* 1287 */         while (subCompanyComInfo.next()) {
/* 1288 */           if (this.showCanceled || "0".equals(subCompanyComInfo.getCompanyiscanceled()) || "".equals(subCompanyComInfo.getCompanyiscanceled())) {
/* 1289 */             arrayOfInt1[b1] = Integer.parseInt(subCompanyComInfo.getSubCompanyid());
/* 1290 */             b1++;
/*      */           } 
/*      */         } 
/* 1293 */         return arrayOfInt1;
/* 1294 */       } catch (Exception exception) {
/* 1295 */         exception.printStackTrace();
/* 1296 */         return null;
/*      */       } 
/*      */     } 
/*      */     
/* 1300 */     if (paramString.equals(""))
/*      */     {
/* 1302 */       paramString = "0";
/*      */     }
/* 1304 */     this.rs.executeProc("HrmEditRightPath_SeByURId", "" + paramInt1 + this.flag + paramString + this.flag + paramInt2);
/* 1305 */     while (this.rs.next()) {
/* 1306 */       arrayList.add(String.valueOf(this.rs.getInt("subcompanyid")));
/*      */     }
/* 1308 */     if (arrayList.size() < 1) {
/*      */       
/*      */       try {
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */         
/* 1334 */         String str1 = "";
/* 1335 */         ArrayList<String> arrayList1 = new ArrayList();
/* 1336 */         RecordSet recordSet = new RecordSet();
/* 1337 */         String str2 = "select * from HrmResourceManager where  id='" + paramInt1 + "'  ";
/* 1338 */         recordSet.executeSql(str2);
/* 1339 */         if (recordSet.next()) {
/* 1340 */           str1 = recordSet.getString("subcompanyids");
/*      */         }
/* 1342 */         if (str1 != null && !"".equals(str1)) {
/* 1343 */           arrayList1 = Util.TokenizerString(str1, ",");
/*      */         }
/* 1345 */         if (arrayList1 != null && arrayList1.size() > 0) {
/* 1346 */           for (byte b1 = 0; b1 < arrayList1.size(); b1++) {
/* 1347 */             String str = arrayList1.get(b1);
/* 1348 */             arrayList.add(str);
/*      */           } 
/*      */         }
/*      */         
/* 1352 */         if (this.detachable == 1) {
/* 1353 */           OrganizationUtil organizationUtil = new OrganizationUtil();
/* 1354 */           organizationUtil.selectData(paramInt1, this.fieldid + "", this.isdetail, this.isbill);
/* 1355 */           ArrayList arrayList2 = organizationUtil.getSubcomList();
/* 1356 */           for (byte b1 = 0; b1 < arrayList2.size(); b1++) {
/* 1357 */             String str = (new StringBuilder()).append(arrayList2.get(b1)).append("").toString();
/* 1358 */             if (!arrayList.contains(str)) {
/* 1359 */               arrayList.add(str);
/*      */             }
/*      */           }
/*      */         
/*      */         } 
/* 1364 */       } catch (Exception exception) {
/* 1365 */         writeLog(exception);
/* 1366 */         return new int[0];
/*      */       } 
/*      */     }
/* 1369 */     int[] arrayOfInt = new int[arrayList.size()];
/* 1370 */     for (byte b = 0; b < arrayList.size(); b++) {
/* 1371 */       arrayOfInt[b] = Integer.parseInt((String)arrayList.get(b));
/*      */     }
/* 1373 */     return arrayOfInt; }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public ArrayList getDepartmentPathByDec(int paramInt, boolean paramBoolean) {
/* 1385 */     ArrayList<String> arrayList = new ArrayList();
/* 1386 */     if (paramBoolean) {
/*      */       try {
/* 1388 */         ResourceComInfo resourceComInfo = new ResourceComInfo();
/* 1389 */         String str = resourceComInfo.getDepartmentID("" + paramInt);
/* 1390 */         DepartmentComInfo departmentComInfo = new DepartmentComInfo();
/* 1391 */         while (Util.getIntValue(str) > 0) {
/* 1392 */           arrayList.add("" + str);
/* 1393 */           str = departmentComInfo.getDepartmentsupdepid("" + str);
/*      */         } 
/* 1395 */       } catch (Exception exception) {
/* 1396 */         exception.printStackTrace();
/* 1397 */         return arrayList;
/*      */       } 
/*      */     }
/* 1400 */     return arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public ArrayList getDepartmentList(int paramInt) {
/* 1409 */     ArrayList<String> arrayList = new ArrayList();
/* 1410 */     if (this.detachable == 1) {
/* 1411 */       OrganizationUtil organizationUtil = new OrganizationUtil();
/* 1412 */       organizationUtil.selectData(paramInt, this.fieldid + "", this.isdetail, this.isbill);
/* 1413 */       ArrayList arrayList1 = organizationUtil.getDepatList();
/* 1414 */       for (byte b = 0; b < arrayList1.size(); b++) {
/* 1415 */         String str = (new StringBuilder()).append(arrayList1.get(b)).append("").toString();
/* 1416 */         if (!arrayList.contains(str)) {
/* 1417 */           arrayList.add(str);
/*      */         }
/*      */       } 
/*      */     } 
/* 1421 */     return arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean getDecentralizationAttr(int paramInt1, String paramString, int paramInt2, int paramInt3, int paramInt4) {
/* 1432 */     boolean bool = false;
/* 1433 */     this.rs = new RecordSet();
/* 1434 */     this.rs.executeSql("select detachable from SystemSet");
/* 1435 */     int i = 0;
/* 1436 */     if (this.rs.next()) {
/* 1437 */       i = this.rs.getInt("detachable");
/*      */     }
/* 1439 */     i = getDetachableByRight(paramString) ? 1 : 0;
/* 1440 */     ResourceComInfo resourceComInfo = null;
/* 1441 */     DepartmentComInfo departmentComInfo = null;
/* 1442 */     boolean bool1 = HrmSanyuanAdminBiz.canViewAllSubCompany(paramInt1, paramString);
/* 1443 */     if (paramInt1 == 1 || bool1) {
/* 1444 */       this.isall = true;
/* 1445 */     } else if (i == 0) {
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/* 1455 */       HrmCommonServiceImpl hrmCommonServiceImpl = new HrmCommonServiceImpl();
/* 1456 */       int j = hrmCommonServiceImpl.getMaxRoleLevel(paramInt1, paramString);
/* 1457 */       if (j == 0) {
/*      */         try {
/* 1459 */           resourceComInfo = new ResourceComInfo();
/* 1460 */           departmentComInfo = new DepartmentComInfo();
/* 1461 */           bool = true;
/* 1462 */           this.departmentids = resourceComInfo.getDepartmentID("" + paramInt1);
/* 1463 */           this.subcompanyids = departmentComInfo.getSubcompanyid1(resourceComInfo.getDepartmentID("" + paramInt1));
/* 1464 */         } catch (Exception exception) {
/* 1465 */           writeLog(exception);
/*      */         } 
/* 1467 */       } else if (j == 1) {
/*      */         try {
/* 1469 */           resourceComInfo = new ResourceComInfo();
/* 1470 */           departmentComInfo = new DepartmentComInfo();
/* 1471 */           this.subcompanyids = departmentComInfo.getSubcompanyid1(resourceComInfo.getDepartmentID("" + paramInt1));
/* 1472 */         } catch (Exception exception) {
/* 1473 */           writeLog(exception);
/*      */         } 
/* 1475 */       } else if (j < 0) {
/*      */         try {
/* 1477 */           resourceComInfo = new ResourceComInfo();
/* 1478 */           departmentComInfo = new DepartmentComInfo();
/* 1479 */         } catch (Exception exception) {
/* 1480 */           writeLog(exception);
/*      */         } 
/* 1482 */         if (paramInt3 == 1) {
/* 1483 */           if (paramInt4 == 0) {
/* 1484 */             this.rs.executeSql("select textheight_2 from workflow_formdictdetail where id=" + paramInt2);
/*      */           } else {
/* 1486 */             this.rs.executeSql("select textheight_2 from workflow_billfield where viewtype=1 and id=" + paramInt2);
/*      */           }
/*      */         
/* 1489 */         } else if (paramInt4 == 0) {
/* 1490 */           this.rs.executeSql("select textheight_2 from workflow_formdict where id=" + paramInt2);
/*      */         } else {
/* 1492 */           this.rs.executeSql("select textheight_2 from workflow_billfield where viewtype=0 and id=" + paramInt2);
/*      */         } 
/*      */         
/* 1495 */         if (this.rs.next()) {
/* 1496 */           String str = Util.null2String(this.rs.getString(1));
/*      */           
/* 1498 */           OrganizationUtil organizationUtil = new OrganizationUtil();
/* 1499 */           organizationUtil.queryData(paramInt1, paramInt2 + "", str, paramInt3, paramInt4);
/*      */           
/* 1501 */           if (organizationUtil.isIsdepat()) {
/* 1502 */             bool = true;
/* 1503 */             this.departmentids = organizationUtil.getDepartmentids();
/*      */           } else {
/* 1505 */             this.subcompanyids = organizationUtil.getSubcompanyids();
/*      */           } 
/*      */         } else {
/* 1508 */           bool = true;
/* 1509 */           this.departmentids = resourceComInfo.getDepartmentID("" + paramInt1);
/* 1510 */           this.subcompanyids = departmentComInfo.getSubcompanyid1(resourceComInfo.getDepartmentID("" + paramInt1));
/*      */         } 
/*      */       } else {
/* 1513 */         this.isall = true;
/*      */       } 
/*      */     } else {
/* 1516 */       this.rs.executeProc("HrmRoleSR_SeByURId", "" + paramInt1 + this.flag + paramString);
/* 1517 */       while (this.rs.next()) {
/* 1518 */         if (this.rs.getInt("rightlevel") > -1) {
/* 1519 */           if (this.subcompanyids.equals("")) {
/* 1520 */             this.subcompanyids = this.rs.getString("subcompanyid"); continue;
/*      */           } 
/* 1522 */           this.subcompanyids += "," + this.rs.getString("subcompanyid");
/*      */         } 
/*      */       } 
/*      */       
/* 1526 */       if (this.subcompanyids.equals("")) {
/*      */         try {
/* 1528 */           resourceComInfo = new ResourceComInfo();
/* 1529 */           departmentComInfo = new DepartmentComInfo();
/* 1530 */         } catch (Exception exception) {
/* 1531 */           writeLog(exception);
/*      */         } 
/*      */         
/* 1534 */         if (paramInt3 == 1) {
/* 1535 */           if (paramInt4 == 0) {
/* 1536 */             this.rs.executeSql("select textheight_2 from workflow_formdictdetail where id=" + paramInt2);
/*      */           } else {
/* 1538 */             this.rs.executeSql("select textheight_2 from workflow_billfield where viewtype=1 and id=" + paramInt2);
/*      */           }
/*      */         
/* 1541 */         } else if (paramInt4 == 0) {
/* 1542 */           this.rs.executeSql("select textheight_2 from workflow_formdict where id=" + paramInt2);
/*      */         } else {
/* 1544 */           this.rs.executeSql("select textheight_2 from workflow_billfield where viewtype=0 and id=" + paramInt2);
/*      */         } 
/*      */         
/* 1547 */         if (this.rs.next()) {
/* 1548 */           String str = Util.null2String(this.rs.getString(1));
/*      */           
/* 1550 */           OrganizationUtil organizationUtil = new OrganizationUtil();
/* 1551 */           organizationUtil.queryData(paramInt1, paramInt2 + "", str, paramInt3, paramInt4);
/*      */           
/* 1553 */           if (organizationUtil.isIsdepat()) {
/* 1554 */             bool = true;
/* 1555 */             this.departmentids = organizationUtil.getDepartmentids();
/*      */           } else {
/* 1557 */             this.subcompanyids = organizationUtil.getSubcompanyids();
/*      */           
/*      */           }
/*      */         
/*      */         }
/*      */         else {
/*      */           
/* 1564 */           bool = true;
/* 1565 */           this.departmentids = resourceComInfo.getDepartmentID("" + paramInt1);
/* 1566 */           this.subcompanyids = departmentComInfo.getSubcompanyid1(resourceComInfo.getDepartmentID("" + paramInt1));
/*      */         } 
/*      */       } 
/*      */     } 
/* 1570 */     return bool;
/*      */   }
/*      */   
/*      */   public boolean getDetachableByRight(String paramString) {
/* 1574 */     RecordSet recordSet = new RecordSet();
/* 1575 */     boolean bool = false;
/* 1576 */     recordSet.executeSql("select detachable from SystemSet");
/* 1577 */     if (recordSet.next() && 
/* 1578 */       recordSet.getInt("detachable") == 1) {
/* 1579 */       bool = true;
/*      */     }
/*      */ 
/*      */     
/* 1583 */     String str1 = "";
/* 1584 */     String str2 = "";
/* 1585 */     String str3 = "SELECT righttype,detachable FROM SystemRights a, SystemRightDetail b  WHERE a.id=b.rightid AND b.rightdetail='" + paramString + "'";
/*      */     
/* 1587 */     recordSet.executeSql(str3);
/* 1588 */     if (recordSet.next()) {
/* 1589 */       str1 = recordSet.getString("righttype");
/* 1590 */       str2 = recordSet.getString("detachable");
/* 1591 */       if (str2.length() == 0) str2 = "0";
/*      */     
/*      */     } 
/* 1594 */     if (str2.equals("1")) {
/* 1595 */       if (str1.equals("1")) {
/*      */         
/* 1597 */         bool = this.ManageDetachComInfo.isUseDocManageDetach();
/* 1598 */       } else if (str1.equals("3")) {
/*      */         
/* 1600 */         bool = this.ManageDetachComInfo.isUseHrmManageDetach();
/* 1601 */       } else if (str1.equals("5")) {
/*      */         
/* 1603 */         bool = this.ManageDetachComInfo.isUseWfManageDetach();
/* 1604 */       } else if (str1.equals("8")) {
/*      */         
/* 1606 */         bool = this.ManageDetachComInfo.isUseCptManageDetach();
/* 1607 */       } else if (str1.equals("9")) {
/*      */         
/* 1609 */         bool = this.ManageDetachComInfo.isUsePortalManageDetach();
/* 1610 */       } else if (str1.equals("21")) {
/*      */         
/* 1612 */         bool = this.ManageDetachComInfo.isUseBlogManageDetach();
/* 1613 */       } else if (str1.equals("20")) {
/*      */         
/* 1615 */         bool = this.ManageDetachComInfo.isUseExecutionManageDetach();
/* 1616 */       } else if (str1.equals("16")) {
/*      */         
/* 1618 */         bool = this.ManageDetachComInfo.isUseGovernManageDetach();
/*      */       } 
/* 1620 */     } else if (str2.equals("0")) {
/*      */       
/* 1622 */       bool = false;
/*      */     } 
/* 1624 */     return bool;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public int[] geDeptPathByUserRightId(int paramInt1, String paramString, int paramInt2) {
/* 1635 */     ArrayList arrayList = new ArrayList();
/* 1636 */     this.rs = new RecordSet();
/*      */     
/* 1638 */     this.rs.executeSql("select detachable from SystemSet");
/* 1639 */     int i = 0;
/* 1640 */     if (this.rs.next()) {
/* 1641 */       i = this.rs.getInt("detachable");
/*      */     }
/*      */     
/* 1644 */     i = getDetachableByRight(paramString) ? 1 : 0;
/* 1645 */     boolean bool = HrmSanyuanAdminBiz.canViewAllSubCompany(paramInt1, paramString);
/* 1646 */     if (paramInt1 == 1 || bool)
/* 1647 */       return null; 
/* 1648 */     if (i == 0) {
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/* 1658 */       HrmCommonServiceImpl hrmCommonServiceImpl = new HrmCommonServiceImpl();
/* 1659 */       int j = hrmCommonServiceImpl.getMaxRoleLevel(paramInt1, paramString);
/* 1660 */       if (j == 0)
/*      */         try {
/* 1662 */           ResourceComInfo resourceComInfo = new ResourceComInfo();
/* 1663 */           DepartmentComInfo departmentComInfo = new DepartmentComInfo();
/* 1664 */           String str1 = resourceComInfo.getDepartmentID("" + paramInt1);
/* 1665 */           String str2 = "";
/* 1666 */           str2 = DepartmentComInfo.getAllParentDepartId(str1, str1);
/* 1667 */           String[] arrayOfString = str2.split(",");
/* 1668 */           int[] arrayOfInt = new int[arrayOfString.length];
/* 1669 */           for (byte b = 0; b < arrayOfString.length; b++) {
/* 1670 */             arrayOfInt[b] = Integer.parseInt(arrayOfString[b]);
/*      */           }
/* 1672 */           return arrayOfInt;
/* 1673 */         } catch (Exception exception) {
/* 1674 */           writeLog(exception);
/* 1675 */           return new int[0];
/*      */         }  
/* 1677 */       if (j < 0) {
/* 1678 */         return new int[0];
/*      */       }
/* 1680 */       return null;
/*      */     } 
/*      */ 
/*      */     
/* 1684 */     return null;
/*      */   }
/*      */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/systeminfo/systemright/CheckSubCompanyRight.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */