/*     */ package weaver.interfaces.encode;
/*     */ 
/*     */ import java.security.MessageDigest;
/*     */ import javax.crypto.Cipher;
/*     */ import javax.crypto.SecretKey;
/*     */ import javax.crypto.SecretKeyFactory;
/*     */ import javax.crypto.spec.PBEKeySpec;
/*     */ import javax.crypto.spec.PBEParameterSpec;
/*     */ import weaver.integration.logging.Logger;
/*     */ import weaver.integration.logging.LoggerFactory;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class PBE
/*     */   implements IEncode
/*     */ {
/*  29 */   private static Logger newlog = LoggerFactory.getLogger(PBE.class);
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static final int ITERATIONS = 20;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  39 */   private String pwd = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public PBE() {}
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public PBE(String paramString) {
/*  53 */     this.pwd = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String encode(String paramString) {
/*  63 */     byte[] arrayOfByte1 = new byte[8];
/*  64 */     byte[] arrayOfByte2 = null;
/*     */     try {
/*  66 */       if (this.pwd == null) {
/*  67 */         this.pwd = "ecology";
/*     */       }
/*  69 */       MessageDigest messageDigest = MessageDigest.getInstance("MD5");
/*  70 */       messageDigest.update(this.pwd.getBytes());
/*  71 */       byte[] arrayOfByte = messageDigest.digest();
/*  72 */       for (byte b = 0; b < 8; b++) {
/*  73 */         arrayOfByte1[b] = arrayOfByte[b];
/*     */       }
/*     */       
/*  76 */       PBEKeySpec pBEKeySpec = new PBEKeySpec(this.pwd.toCharArray());
/*  77 */       SecretKeyFactory secretKeyFactory = SecretKeyFactory.getInstance("PBEWithMD5AndDES");
/*  78 */       SecretKey secretKey = secretKeyFactory.generateSecret(pBEKeySpec);
/*  79 */       PBEParameterSpec pBEParameterSpec = new PBEParameterSpec(arrayOfByte1, 20);
/*     */       
/*  81 */       Cipher cipher = Cipher.getInstance("PBEWithMD5AndDES");
/*  82 */       cipher.init(1, secretKey, pBEParameterSpec);
/*  83 */       arrayOfByte2 = cipher.doFinal(paramString.getBytes());
/*     */     }
/*  85 */     catch (Exception exception) {
/*  86 */       newlog.error("加密异常！", exception);
/*  87 */       return null;
/*     */     } 
/*     */     
/*  90 */     String str1 = parseByte2HexStr(arrayOfByte1);
/*  91 */     String str2 = parseByte2HexStr(arrayOfByte2);
/*  92 */     return str1 + str2;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String decode(String paramString) {
/* 102 */     byte b = 16;
/* 103 */     byte[] arrayOfByte = null;
/*     */     try {
/* 105 */       if (this.pwd == null) {
/* 106 */         this.pwd = "ecology";
/*     */       }
/* 108 */       String str1 = paramString.substring(0, b);
/* 109 */       String str2 = paramString.substring(b, paramString.length());
/* 110 */       byte[] arrayOfByte1 = parseHexStr2Byte(str1);
/* 111 */       byte[] arrayOfByte2 = parseHexStr2Byte(str2);
/*     */       
/* 113 */       PBEKeySpec pBEKeySpec = new PBEKeySpec(this.pwd.toCharArray());
/* 114 */       SecretKeyFactory secretKeyFactory = SecretKeyFactory.getInstance("PBEWithMD5AndDES");
/* 115 */       SecretKey secretKey = secretKeyFactory.generateSecret(pBEKeySpec);
/* 116 */       PBEParameterSpec pBEParameterSpec = new PBEParameterSpec(arrayOfByte1, 20);
/*     */       
/* 118 */       Cipher cipher = Cipher.getInstance("PBEWithMD5AndDES");
/* 119 */       cipher.init(2, secretKey, pBEParameterSpec);
/* 120 */       arrayOfByte = cipher.doFinal(arrayOfByte2);
/*     */     }
/* 122 */     catch (Exception exception) {
/* 123 */       newlog.error("加密异常！", exception);
/* 124 */       return null;
/*     */     } 
/*     */     
/* 127 */     return new String(arrayOfByte);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean setPwd(String paramString) {
/* 137 */     this.pwd = paramString;
/* 138 */     return true;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean setIv(String paramString) {
/* 147 */     return true;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String parseByte2HexStr(byte[] paramArrayOfbyte) {
/* 157 */     StringBuffer stringBuffer = new StringBuffer();
/* 158 */     for (byte b = 0; b < paramArrayOfbyte.length; b++) {
/* 159 */       String str = Integer.toHexString(paramArrayOfbyte[b] & 0xFF);
/* 160 */       if (str.length() == 1) {
/* 161 */         str = '0' + str;
/*     */       }
/* 163 */       stringBuffer.append(str.toUpperCase());
/*     */     } 
/* 165 */     return stringBuffer.toString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private byte[] parseHexStr2Byte(String paramString) {
/* 175 */     if (paramString.length() < 1) {
/* 176 */       return null;
/*     */     }
/* 178 */     byte[] arrayOfByte = new byte[paramString.length() / 2];
/* 179 */     for (byte b = 0; b < paramString.length() / 2; b++) {
/* 180 */       int i = Integer.parseInt(paramString.substring(b * 2, b * 2 + 1), 16);
/* 181 */       int j = Integer.parseInt(paramString.substring(b * 2 + 1, b * 2 + 2), 16);
/* 182 */       arrayOfByte[b] = (byte)(i * 16 + j);
/*     */     } 
/* 184 */     return arrayOfByte;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/interfaces/encode/PBE.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */