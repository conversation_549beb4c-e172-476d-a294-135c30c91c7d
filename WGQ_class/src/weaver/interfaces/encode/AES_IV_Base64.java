/*     */ package weaver.interfaces.encode;
/*     */ 
/*     */ import java.security.SecureRandom;
/*     */ import javax.crypto.Cipher;
/*     */ import javax.crypto.KeyGenerator;
/*     */ import javax.crypto.SecretKey;
/*     */ import javax.crypto.spec.IvParameterSpec;
/*     */ import javax.crypto.spec.SecretKeySpec;
/*     */ import weaver.general.Base64;
/*     */ import weaver.integration.logging.Logger;
/*     */ import weaver.integration.logging.LoggerFactory;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class AES_IV_Base64
/*     */   implements IEncode
/*     */ {
/*  30 */   private static Logger newlog = LoggerFactory.getLogger(AES_IV_Base64.class);
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  35 */   private String pwd = null;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  40 */   private String iv = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public AES_IV_Base64() {}
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public AES_IV_Base64(String paramString1, String paramString2) {
/*  55 */     this.pwd = paramString1;
/*  56 */     this.iv = paramString2;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String encode(String paramString) {
/*  66 */     byte[] arrayOfByte = null;
/*     */     try {
/*  68 */       KeyGenerator keyGenerator = KeyGenerator.getInstance("AES");
/*  69 */       SecureRandom secureRandom = SecureRandom.getInstance("SHA1PRNG");
/*  70 */       if (this.pwd == null) {
/*  71 */         this.pwd = "ecology9";
/*     */       }
/*  73 */       secureRandom.setSeed(this.pwd.getBytes());
/*  74 */       keyGenerator.init(128, secureRandom);
/*  75 */       SecretKey secretKey = keyGenerator.generateKey();
/*  76 */       byte[] arrayOfByte1 = secretKey.getEncoded();
/*  77 */       SecretKeySpec secretKeySpec = new SecretKeySpec(arrayOfByte1, "AES");
/*  78 */       if (this.iv == null) {
/*  79 */         this.iv = "weaver_ecology_9";
/*     */       }
/*     */       
/*  82 */       IvParameterSpec ivParameterSpec = new IvParameterSpec(this.iv.getBytes());
/*     */ 
/*     */       
/*  85 */       Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
/*  86 */       cipher.init(1, secretKeySpec, ivParameterSpec);
/*  87 */       byte[] arrayOfByte2 = paramString.getBytes();
/*  88 */       arrayOfByte = cipher.doFinal(arrayOfByte2);
/*     */     }
/*  90 */     catch (Exception exception) {
/*  91 */       newlog.error("加密异常！", exception);
/*  92 */       return null;
/*     */     } 
/*     */     
/*  95 */     return new String(Base64.encode(arrayOfByte));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String decode(String paramString) {
/* 105 */     byte[] arrayOfByte1 = null;
/* 106 */     byte[] arrayOfByte2 = Base64.decode(paramString.getBytes());
/*     */     try {
/* 108 */       KeyGenerator keyGenerator = KeyGenerator.getInstance("AES");
/* 109 */       SecureRandom secureRandom = SecureRandom.getInstance("SHA1PRNG");
/* 110 */       if (this.pwd == null) {
/* 111 */         this.pwd = "ecology9";
/*     */       }
/* 113 */       secureRandom.setSeed(this.pwd.getBytes());
/* 114 */       keyGenerator.init(128, secureRandom);
/* 115 */       SecretKey secretKey = keyGenerator.generateKey();
/* 116 */       byte[] arrayOfByte = secretKey.getEncoded();
/* 117 */       SecretKeySpec secretKeySpec = new SecretKeySpec(arrayOfByte, "AES");
/* 118 */       if (this.iv == null) {
/* 119 */         this.iv = "weaver_ecology_9";
/*     */       }
/*     */       
/* 122 */       IvParameterSpec ivParameterSpec = new IvParameterSpec(this.iv.getBytes());
/*     */ 
/*     */       
/* 125 */       Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
/* 126 */       cipher.init(2, secretKeySpec, ivParameterSpec);
/* 127 */       arrayOfByte1 = cipher.doFinal(arrayOfByte2);
/*     */     }
/* 129 */     catch (Exception exception) {
/* 130 */       newlog.error("解密异常！", exception);
/* 131 */       return null;
/*     */     } 
/*     */     
/* 134 */     return new String(arrayOfByte1);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean setPwd(String paramString) {
/* 144 */     this.pwd = paramString;
/* 145 */     return true;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean setIv(String paramString) {
/* 154 */     this.iv = paramString;
/* 155 */     return true;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/interfaces/encode/AES_IV_Base64.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */