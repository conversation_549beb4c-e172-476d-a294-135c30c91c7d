/*     */ package weaver.interfaces.encode;
/*     */ 
/*     */ import javax.crypto.Cipher;
/*     */ import javax.crypto.SecretKey;
/*     */ import javax.crypto.SecretKeyFactory;
/*     */ import javax.crypto.spec.DESKeySpec;
/*     */ import javax.crypto.spec.IvParameterSpec;
/*     */ import weaver.integration.logging.Logger;
/*     */ import weaver.integration.logging.LoggerFactory;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class DES_IV
/*     */   implements IEncode
/*     */ {
/*  27 */   private static Logger newlog = LoggerFactory.getLogger(DES_IV.class);
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  32 */   private String pwd = null;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  37 */   private String iv = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public DES_IV() {}
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public DES_IV(String paramString1, String paramString2) {
/*  52 */     this.pwd = paramString1;
/*  53 */     this.iv = paramString2;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String encode(String paramString) {
/*  63 */     byte[] arrayOfByte = null;
/*     */     try {
/*  65 */       if (this.pwd == null) {
/*  66 */         this.pwd = "ecology9";
/*     */       }
/*  68 */       DESKeySpec dESKeySpec = new DESKeySpec(this.pwd.getBytes());
/*  69 */       SecretKeyFactory secretKeyFactory = SecretKeyFactory.getInstance("DES");
/*  70 */       SecretKey secretKey = secretKeyFactory.generateSecret(dESKeySpec);
/*  71 */       if (this.iv == null) {
/*  72 */         this.iv = "ecology9";
/*     */       }
/*     */       
/*  75 */       IvParameterSpec ivParameterSpec = new IvParameterSpec(this.iv.getBytes());
/*     */       
/*  77 */       Cipher cipher = Cipher.getInstance("DES/CBC/PKCS5Padding");
/*  78 */       cipher.init(1, secretKey, ivParameterSpec);
/*  79 */       byte[] arrayOfByte1 = paramString.getBytes();
/*  80 */       arrayOfByte = cipher.doFinal(arrayOfByte1);
/*     */     }
/*  82 */     catch (Exception exception) {
/*  83 */       newlog.error("加密异常！", exception);
/*  84 */       return null;
/*     */     } 
/*     */     
/*  87 */     return parseByte2HexStr(arrayOfByte);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String decode(String paramString) {
/*  97 */     byte[] arrayOfByte1 = null;
/*  98 */     byte[] arrayOfByte2 = parseHexStr2Byte(paramString);
/*     */     try {
/* 100 */       if (this.pwd == null) {
/* 101 */         this.pwd = "ecology9";
/*     */       }
/* 103 */       DESKeySpec dESKeySpec = new DESKeySpec(this.pwd.getBytes());
/* 104 */       SecretKeyFactory secretKeyFactory = SecretKeyFactory.getInstance("DES");
/* 105 */       SecretKey secretKey = secretKeyFactory.generateSecret(dESKeySpec);
/* 106 */       if (this.iv == null) {
/* 107 */         this.iv = "ecology9";
/*     */       }
/*     */       
/* 110 */       IvParameterSpec ivParameterSpec = new IvParameterSpec(this.iv.getBytes());
/*     */       
/* 112 */       Cipher cipher = Cipher.getInstance("DES/CBC/PKCS5Padding");
/* 113 */       cipher.init(2, secretKey, ivParameterSpec);
/* 114 */       arrayOfByte1 = cipher.doFinal(arrayOfByte2);
/*     */     }
/* 116 */     catch (Exception exception) {
/* 117 */       newlog.error("解密异常！", exception);
/* 118 */       return null;
/*     */     } 
/*     */     
/* 121 */     return new String(arrayOfByte1);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean setPwd(String paramString) {
/* 131 */     this.pwd = paramString;
/* 132 */     return true;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean setIv(String paramString) {
/* 141 */     this.iv = paramString;
/* 142 */     return true;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String parseByte2HexStr(byte[] paramArrayOfbyte) {
/* 152 */     StringBuffer stringBuffer = new StringBuffer();
/* 153 */     for (byte b = 0; b < paramArrayOfbyte.length; b++) {
/* 154 */       String str = Integer.toHexString(paramArrayOfbyte[b] & 0xFF);
/* 155 */       if (str.length() == 1) {
/* 156 */         str = '0' + str;
/*     */       }
/* 158 */       stringBuffer.append(str.toUpperCase());
/*     */     } 
/* 160 */     return stringBuffer.toString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private byte[] parseHexStr2Byte(String paramString) {
/* 170 */     if (paramString.length() < 1) {
/* 171 */       return null;
/*     */     }
/* 173 */     byte[] arrayOfByte = new byte[paramString.length() / 2];
/* 174 */     for (byte b = 0; b < paramString.length() / 2; b++) {
/* 175 */       int i = Integer.parseInt(paramString.substring(b * 2, b * 2 + 1), 16);
/* 176 */       int j = Integer.parseInt(paramString.substring(b * 2 + 1, b * 2 + 2), 16);
/* 177 */       arrayOfByte[b] = (byte)(i * 16 + j);
/*     */     } 
/* 179 */     return arrayOfByte;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/interfaces/encode/DES_IV.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */