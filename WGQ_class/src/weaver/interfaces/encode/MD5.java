/*     */ package weaver.interfaces.encode;
/*     */ 
/*     */ import java.security.MessageDigest;
/*     */ import weaver.integration.logging.Logger;
/*     */ import weaver.integration.logging.LoggerFactory;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class MD5
/*     */   implements IEncode
/*     */ {
/*  23 */   private static Logger newlog = LoggerFactory.getLogger(MD5.class);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String encode(String paramString) {
/*  32 */     byte[] arrayOfByte = null;
/*     */     try {
/*  34 */       MessageDigest messageDigest = MessageDigest.getInstance("MD5");
/*  35 */       messageDigest.update(paramString.getBytes("UTF-8"));
/*  36 */       arrayOfByte = messageDigest.digest();
/*     */     }
/*  38 */     catch (Exception exception) {
/*  39 */       newlog.error("加密异常！", exception);
/*  40 */       return null;
/*     */     } 
/*     */     
/*  43 */     return parseByte2HexStr(arrayOfByte);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String decode(String paramString) {
/*  53 */     return "";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean setPwd(String paramString) {
/*  63 */     return true;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean setIv(String paramString) {
/*  72 */     return true;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String parseByte2HexStr(byte[] paramArrayOfbyte) {
/*  82 */     StringBuffer stringBuffer = new StringBuffer();
/*  83 */     for (byte b = 0; b < paramArrayOfbyte.length; b++) {
/*  84 */       String str = Integer.toHexString(paramArrayOfbyte[b] & 0xFF);
/*  85 */       if (str.length() == 1) {
/*  86 */         str = '0' + str;
/*     */       }
/*  88 */       stringBuffer.append(str.toUpperCase());
/*     */     } 
/*  90 */     return stringBuffer.toString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static void main(String[] paramArrayOfString) {
/*  98 */     MD5 mD5 = new MD5();
/*  99 */     String str1 = "测试test";
/*     */ 
/*     */     
/* 102 */     String str2 = mD5.encode(str1);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/interfaces/encode/MD5.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */