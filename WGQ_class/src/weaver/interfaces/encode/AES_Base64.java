/*     */ package weaver.interfaces.encode;
/*     */ 
/*     */ import java.security.SecureRandom;
/*     */ import javax.crypto.Cipher;
/*     */ import javax.crypto.KeyGenerator;
/*     */ import javax.crypto.SecretKey;
/*     */ import javax.crypto.spec.SecretKeySpec;
/*     */ import weaver.general.Base64;
/*     */ import weaver.integration.logging.Logger;
/*     */ import weaver.integration.logging.LoggerFactory;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class AES_Base64
/*     */   implements IEncode
/*     */ {
/*  29 */   private static Logger newlog = LoggerFactory.getLogger(AES_Base64.class);
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  34 */   private String pwd = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public AES_Base64() {}
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public AES_Base64(String paramString) {
/*  48 */     this.pwd = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String encode(String paramString) {
/*  58 */     byte[] arrayOfByte = null;
/*     */     try {
/*  60 */       KeyGenerator keyGenerator = KeyGenerator.getInstance("AES");
/*  61 */       SecureRandom secureRandom = SecureRandom.getInstance("SHA1PRNG");
/*  62 */       if (this.pwd == null) {
/*  63 */         this.pwd = "ecology9";
/*     */       }
/*  65 */       secureRandom.setSeed(this.pwd.getBytes());
/*  66 */       keyGenerator.init(128, secureRandom);
/*  67 */       SecretKey secretKey = keyGenerator.generateKey();
/*  68 */       byte[] arrayOfByte1 = secretKey.getEncoded();
/*  69 */       SecretKeySpec secretKeySpec = new SecretKeySpec(arrayOfByte1, "AES");
/*     */ 
/*     */       
/*  72 */       Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
/*  73 */       cipher.init(1, secretKeySpec);
/*  74 */       byte[] arrayOfByte2 = paramString.getBytes();
/*  75 */       arrayOfByte = cipher.doFinal(arrayOfByte2);
/*     */     }
/*  77 */     catch (Exception exception) {
/*  78 */       newlog.error("加密异常！", exception);
/*  79 */       return null;
/*     */     } 
/*     */     
/*  82 */     return new String(Base64.encode(arrayOfByte));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String decode(String paramString) {
/*  92 */     byte[] arrayOfByte1 = null;
/*  93 */     byte[] arrayOfByte2 = Base64.decode(paramString.getBytes());
/*     */     try {
/*  95 */       KeyGenerator keyGenerator = KeyGenerator.getInstance("AES");
/*  96 */       SecureRandom secureRandom = SecureRandom.getInstance("SHA1PRNG");
/*  97 */       if (this.pwd == null) {
/*  98 */         this.pwd = "ecology9";
/*     */       }
/* 100 */       secureRandom.setSeed(this.pwd.getBytes());
/* 101 */       keyGenerator.init(128, secureRandom);
/* 102 */       SecretKey secretKey = keyGenerator.generateKey();
/* 103 */       byte[] arrayOfByte = secretKey.getEncoded();
/* 104 */       SecretKeySpec secretKeySpec = new SecretKeySpec(arrayOfByte, "AES");
/*     */ 
/*     */       
/* 107 */       Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
/* 108 */       cipher.init(2, secretKeySpec);
/* 109 */       arrayOfByte1 = cipher.doFinal(arrayOfByte2);
/*     */     }
/* 111 */     catch (Exception exception) {
/* 112 */       newlog.error("解密异常！", exception);
/* 113 */       return null;
/*     */     } 
/*     */     
/* 116 */     return new String(arrayOfByte1);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean setPwd(String paramString) {
/* 126 */     this.pwd = paramString;
/* 127 */     return true;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean setIv(String paramString) {
/* 136 */     return true;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/interfaces/encode/AES_Base64.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */