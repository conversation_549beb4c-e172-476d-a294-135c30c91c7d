/*     */ package weaver.interfaces.encode;
/*     */ 
/*     */ import weaver.integration.logging.Logger;
/*     */ import weaver.integration.logging.LoggerFactory;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class Base64
/*     */   implements IEncode
/*     */ {
/*     */   private static final int BASELENGTH = 255;
/*     */   private static final int LOOKUPLENGTH = 64;
/*     */   private static final int TWENTYFOURBITGROUP = 24;
/*     */   private static final int EIGHTBIT = 8;
/*     */   private static final int SIXTEENBIT = 16;
/*     */   private static final int FOURBYTE = 4;
/*     */   private static final int SIGN = -128;
/*     */   private static final byte PAD = 61;
/*  29 */   private static byte[] base64Alphabet = new byte[255];
/*  30 */   private static byte[] lookUpBase64Alphabet = new byte[64];
/*     */   static {
/*     */     byte b1;
/*  33 */     for (b1 = 0; b1 < 'ÿ'; b1++)
/*     */     {
/*  35 */       base64Alphabet[b1] = -1;
/*     */     }
/*  37 */     for (b1 = 90; b1 >= 65; b1--)
/*     */     {
/*  39 */       base64Alphabet[b1] = (byte)(b1 - 65);
/*     */     }
/*  41 */     for (b1 = 122; b1 >= 97; b1--)
/*     */     {
/*  43 */       base64Alphabet[b1] = (byte)(b1 - 97 + 26);
/*     */     }
/*  45 */     for (b1 = 57; b1 >= 48; b1--)
/*     */     {
/*  47 */       base64Alphabet[b1] = (byte)(b1 - 48 + 52);
/*     */     }
/*  49 */     base64Alphabet[43] = 62;
/*  50 */     base64Alphabet[47] = 63;
/*  51 */     for (b1 = 0; b1 <= 25; b1++)
/*  52 */       lookUpBase64Alphabet[b1] = (byte)(65 + b1);  byte b2;
/*  53 */     for (b1 = 26, b2 = 0; b1 <= 51; b1++, b2++)
/*  54 */       lookUpBase64Alphabet[b1] = (byte)(97 + b2); 
/*  55 */     for (b1 = 52, b2 = 0; b1 <= 61; b1++, b2++)
/*  56 */       lookUpBase64Alphabet[b1] = (byte)(48 + b2); 
/*  57 */     lookUpBase64Alphabet[62] = 43;
/*  58 */     lookUpBase64Alphabet[63] = 47;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*  63 */   private static Logger newlog = LoggerFactory.getLogger(Base64.class);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String encode(String paramString) {
/*  72 */     byte[] arrayOfByte = null;
/*     */     try {
/*  74 */       byte[] arrayOfByte1 = paramString.getBytes("utf-8");
/*  75 */       int i = arrayOfByte1.length * 8;
/*  76 */       int j = i % 24;
/*  77 */       int k = i / 24;
/*     */       
/*  79 */       if (j != 0) {
/*     */ 
/*     */         
/*  82 */         arrayOfByte = new byte[(k + 1) * 4];
/*     */       
/*     */       }
/*     */       else {
/*     */         
/*  87 */         arrayOfByte = new byte[k * 4];
/*     */       } 
/*  89 */       byte b1 = 0, b2 = 0, b3 = 0, b4 = 0, b5 = 0;
/*  90 */       int m = 0;
/*  91 */       int n = 0;
/*  92 */       byte b = 0;
/*     */       
/*  94 */       for (b = 0; b < k; b++) {
/*     */         
/*  96 */         n = b * 3;
/*  97 */         b3 = arrayOfByte1[n];
/*  98 */         b4 = arrayOfByte1[n + 1];
/*  99 */         b5 = arrayOfByte1[n + 2];
/*     */         
/* 101 */         b2 = (byte)(b4 & 0xF);
/* 102 */         b1 = (byte)(b3 & 0x3);
/* 103 */         m = b * 4;
/* 104 */         byte b6 = ((b3 & 0xFFFFFF80) == 0) ? (byte)(b3 >> 2) : (byte)(b3 >> 2 ^ 0xC0);
/* 105 */         byte b7 = ((b4 & 0xFFFFFF80) == 0) ? (byte)(b4 >> 4) : (byte)(b4 >> 4 ^ 0xF0);
/* 106 */         byte b8 = ((b5 & 0xFFFFFF80) == 0) ? (byte)(b5 >> 6) : (byte)(b5 >> 6 ^ 0xFC);
/* 107 */         arrayOfByte[m] = lookUpBase64Alphabet[b6];
/*     */ 
/*     */ 
/*     */         
/* 111 */         arrayOfByte[m + 1] = lookUpBase64Alphabet[b7 | b1 << 4];
/* 112 */         arrayOfByte[m + 2] = lookUpBase64Alphabet[b2 << 2 | b8];
/* 113 */         arrayOfByte[m + 3] = lookUpBase64Alphabet[b5 & 0x3F];
/*     */       } 
/*     */       
/* 116 */       n = b * 3;
/* 117 */       m = b * 4;
/* 118 */       if (j == 8) {
/*     */         
/* 120 */         b3 = arrayOfByte1[n];
/* 121 */         b1 = (byte)(b3 & 0x3);
/*     */ 
/*     */         
/* 124 */         byte b6 = ((b3 & 0xFFFFFF80) == 0) ? (byte)(b3 >> 2) : (byte)(b3 >> 2 ^ 0xC0);
/* 125 */         arrayOfByte[m] = lookUpBase64Alphabet[b6];
/* 126 */         arrayOfByte[m + 1] = lookUpBase64Alphabet[b1 << 4];
/* 127 */         arrayOfByte[m + 2] = 61;
/* 128 */         arrayOfByte[m + 3] = 61;
/*     */       }
/* 130 */       else if (j == 16) {
/*     */         
/* 132 */         b3 = arrayOfByte1[n];
/* 133 */         b4 = arrayOfByte1[n + 1];
/* 134 */         b2 = (byte)(b4 & 0xF);
/* 135 */         b1 = (byte)(b3 & 0x3);
/* 136 */         byte b6 = ((b3 & 0xFFFFFF80) == 0) ? (byte)(b3 >> 2) : (byte)(b3 >> 2 ^ 0xC0);
/* 137 */         byte b7 = ((b4 & 0xFFFFFF80) == 0) ? (byte)(b4 >> 4) : (byte)(b4 >> 4 ^ 0xF0);
/* 138 */         arrayOfByte[m] = lookUpBase64Alphabet[b6];
/* 139 */         arrayOfByte[m + 1] = lookUpBase64Alphabet[b7 | b1 << 4];
/* 140 */         arrayOfByte[m + 2] = lookUpBase64Alphabet[b2 << 2];
/* 141 */         arrayOfByte[m + 3] = 61;
/*     */       } 
/* 143 */     } catch (Exception exception) {
/* 144 */       newlog.error("加密异常！", exception);
/* 145 */       return null;
/*     */     } 
/* 147 */     return new String(arrayOfByte);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String decode(String paramString) {
/* 158 */     byte[] arrayOfByte = null;
/*     */     
/*     */     try {
/* 161 */       byte[] arrayOfByte1 = paramString.getBytes("utf-8");
/* 162 */       if (arrayOfByte1.length == 0)
/*     */       {
/* 164 */         return new String(new byte[0]);
/*     */       }
/* 166 */       int i = arrayOfByte1.length / 4;
/* 167 */       byte b1 = 0, b2 = 0, b3 = 0, b4 = 0, b5 = 0, b6 = 0;
/*     */       
/* 169 */       byte b = 0;
/* 170 */       int j = 0;
/*     */ 
/*     */       
/* 173 */       int k = arrayOfByte1.length;
/*     */       
/* 175 */       while (arrayOfByte1[k - 1] == 61) {
/*     */         
/* 177 */         if (--k == 0)
/*     */         {
/* 179 */           return new String(new byte[0]);
/*     */         }
/*     */       } 
/* 182 */       arrayOfByte = new byte[k - i];
/*     */       
/* 184 */       for (k = 0; k < i; k++) {
/*     */         
/* 186 */         j = k * 4;
/* 187 */         b5 = arrayOfByte1[j + 2];
/* 188 */         b6 = arrayOfByte1[j + 3];
/* 189 */         b1 = base64Alphabet[arrayOfByte1[j]];
/* 190 */         b2 = base64Alphabet[arrayOfByte1[j + 1]];
/* 191 */         if (b5 != 61 && b6 != 61) {
/*     */ 
/*     */           
/* 194 */           b3 = base64Alphabet[b5];
/* 195 */           b4 = base64Alphabet[b6];
/* 196 */           arrayOfByte[b] = (byte)(b1 << 2 | b2 >> 4);
/* 197 */           arrayOfByte[b + 1] = (byte)((b2 & 0xF) << 4 | b3 >> 2 & 0xF);
/* 198 */           arrayOfByte[b + 2] = (byte)(b3 << 6 | b4);
/*     */         }
/* 200 */         else if (b5 == 61) {
/*     */ 
/*     */           
/* 203 */           arrayOfByte[b] = (byte)(b1 << 2 | b2 >> 4);
/*     */         }
/* 205 */         else if (b6 == 61) {
/*     */ 
/*     */           
/* 208 */           b3 = base64Alphabet[b5];
/* 209 */           arrayOfByte[b] = (byte)(b1 << 2 | b2 >> 4);
/* 210 */           arrayOfByte[b + 1] = (byte)((b2 & 0xF) << 4 | b3 >> 2 & 0xF);
/*     */         } 
/* 212 */         b += 3;
/*     */       } 
/* 214 */     } catch (Exception exception) {
/* 215 */       newlog.error("解密异常！", exception);
/* 216 */       return null;
/*     */     } 
/* 218 */     return new String(arrayOfByte);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean setPwd(String paramString) {
/* 228 */     return true;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean setIv(String paramString) {
/* 237 */     return true;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String parseByte2HexStr(byte[] paramArrayOfbyte) {
/* 247 */     StringBuffer stringBuffer = new StringBuffer();
/* 248 */     for (byte b = 0; b < paramArrayOfbyte.length; b++) {
/* 249 */       String str = Integer.toHexString(paramArrayOfbyte[b] & 0xFF);
/* 250 */       if (str.length() == 1) {
/* 251 */         str = '0' + str;
/*     */       }
/* 253 */       stringBuffer.append(str.toUpperCase());
/*     */     } 
/* 255 */     return stringBuffer.toString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static void main(String[] paramArrayOfString) {
/* 263 */     Base64 base64 = new Base64();
/* 264 */     String str1 = "测试test";
/*     */ 
/*     */     
/* 267 */     String str2 = base64.encode(str1);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/interfaces/encode/Base64.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */