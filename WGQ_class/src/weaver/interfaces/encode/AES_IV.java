/*     */ package weaver.interfaces.encode;
/*     */ 
/*     */ import java.security.SecureRandom;
/*     */ import javax.crypto.Cipher;
/*     */ import javax.crypto.KeyGenerator;
/*     */ import javax.crypto.SecretKey;
/*     */ import javax.crypto.spec.IvParameterSpec;
/*     */ import javax.crypto.spec.SecretKeySpec;
/*     */ import weaver.integration.logging.Logger;
/*     */ import weaver.integration.logging.LoggerFactory;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class AES_IV
/*     */   implements IEncode
/*     */ {
/*  29 */   private static Logger newlog = LoggerFactory.getLogger(AES_IV.class);
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  34 */   private String pwd = null;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  39 */   private String iv = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public AES_IV() {}
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public AES_IV(String paramString1, String paramString2) {
/*  54 */     this.pwd = paramString1;
/*  55 */     this.iv = paramString2;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String encode(String paramString) {
/*  65 */     byte[] arrayOfByte = null;
/*     */     try {
/*  67 */       KeyGenerator keyGenerator = KeyGenerator.getInstance("AES");
/*  68 */       SecureRandom secureRandom = SecureRandom.getInstance("SHA1PRNG");
/*  69 */       if (this.pwd == null) {
/*  70 */         this.pwd = "ecology9";
/*     */       }
/*  72 */       secureRandom.setSeed(this.pwd.getBytes());
/*  73 */       keyGenerator.init(128, secureRandom);
/*  74 */       SecretKey secretKey = keyGenerator.generateKey();
/*  75 */       byte[] arrayOfByte1 = secretKey.getEncoded();
/*  76 */       SecretKeySpec secretKeySpec = new SecretKeySpec(arrayOfByte1, "AES");
/*  77 */       if (this.iv == null) {
/*  78 */         this.iv = "weaver_ecology_9";
/*     */       }
/*     */       
/*  81 */       IvParameterSpec ivParameterSpec = new IvParameterSpec(this.iv.getBytes());
/*     */ 
/*     */       
/*  84 */       Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
/*  85 */       cipher.init(1, secretKeySpec, ivParameterSpec);
/*  86 */       byte[] arrayOfByte2 = paramString.getBytes();
/*  87 */       arrayOfByte = cipher.doFinal(arrayOfByte2);
/*     */     }
/*  89 */     catch (Exception exception) {
/*  90 */       newlog.error("加密异常！", exception);
/*  91 */       return null;
/*     */     } 
/*     */     
/*  94 */     return parseByte2HexStr(arrayOfByte);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String decode(String paramString) {
/* 104 */     byte[] arrayOfByte1 = null;
/* 105 */     byte[] arrayOfByte2 = parseHexStr2Byte(paramString);
/*     */     try {
/* 107 */       KeyGenerator keyGenerator = KeyGenerator.getInstance("AES");
/* 108 */       SecureRandom secureRandom = SecureRandom.getInstance("SHA1PRNG");
/* 109 */       if (this.pwd == null) {
/* 110 */         this.pwd = "ecology9";
/*     */       }
/* 112 */       secureRandom.setSeed(this.pwd.getBytes());
/* 113 */       keyGenerator.init(128, secureRandom);
/* 114 */       SecretKey secretKey = keyGenerator.generateKey();
/* 115 */       byte[] arrayOfByte = secretKey.getEncoded();
/* 116 */       SecretKeySpec secretKeySpec = new SecretKeySpec(arrayOfByte, "AES");
/* 117 */       if (this.iv == null) {
/* 118 */         this.iv = "weaver_ecology_9";
/*     */       }
/*     */       
/* 121 */       IvParameterSpec ivParameterSpec = new IvParameterSpec(this.iv.getBytes());
/*     */ 
/*     */       
/* 124 */       Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
/* 125 */       cipher.init(2, secretKeySpec, ivParameterSpec);
/* 126 */       arrayOfByte1 = cipher.doFinal(arrayOfByte2);
/*     */     }
/* 128 */     catch (Exception exception) {
/* 129 */       newlog.error("解密异常！", exception);
/* 130 */       return null;
/*     */     } 
/*     */     
/* 133 */     return new String(arrayOfByte1);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean setPwd(String paramString) {
/* 143 */     this.pwd = paramString;
/* 144 */     return true;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean setIv(String paramString) {
/* 153 */     this.iv = paramString;
/* 154 */     return true;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String parseByte2HexStr(byte[] paramArrayOfbyte) {
/* 164 */     StringBuffer stringBuffer = new StringBuffer();
/* 165 */     for (byte b = 0; b < paramArrayOfbyte.length; b++) {
/* 166 */       String str = Integer.toHexString(paramArrayOfbyte[b] & 0xFF);
/* 167 */       if (str.length() == 1) {
/* 168 */         str = '0' + str;
/*     */       }
/* 170 */       stringBuffer.append(str.toUpperCase());
/*     */     } 
/* 172 */     return stringBuffer.toString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private byte[] parseHexStr2Byte(String paramString) {
/* 182 */     if (paramString.length() < 1) {
/* 183 */       return null;
/*     */     }
/* 185 */     byte[] arrayOfByte = new byte[paramString.length() / 2];
/* 186 */     for (byte b = 0; b < paramString.length() / 2; b++) {
/* 187 */       int i = Integer.parseInt(paramString.substring(b * 2, b * 2 + 1), 16);
/* 188 */       int j = Integer.parseInt(paramString.substring(b * 2 + 1, b * 2 + 2), 16);
/* 189 */       arrayOfByte[b] = (byte)(i * 16 + j);
/*     */     } 
/* 191 */     return arrayOfByte;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/interfaces/encode/AES_IV.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */