/*     */ package weaver.interfaces.encode;
/*     */ 
/*     */ import javax.crypto.Cipher;
/*     */ import javax.crypto.SecretKey;
/*     */ import javax.crypto.SecretKeyFactory;
/*     */ import javax.crypto.spec.DESKeySpec;
/*     */ import weaver.integration.logging.Logger;
/*     */ import weaver.integration.logging.LoggerFactory;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class DES
/*     */   implements IEncode
/*     */ {
/*  26 */   private static Logger newlog = LoggerFactory.getLogger(DES.class);
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  31 */   private String pwd = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public DES() {}
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public DES(String paramString) {
/*  45 */     this.pwd = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String encode(String paramString) {
/*  55 */     byte[] arrayOfByte = null;
/*     */     try {
/*  57 */       if (this.pwd == null) {
/*  58 */         this.pwd = "ecology9";
/*     */       }
/*  60 */       DESKeySpec dESKeySpec = new DESKeySpec(this.pwd.getBytes());
/*  61 */       SecretKeyFactory secretKeyFactory = SecretKeyFactory.getInstance("DES");
/*  62 */       SecretKey secretKey = secretKeyFactory.generateSecret(dESKeySpec);
/*     */       
/*  64 */       Cipher cipher = Cipher.getInstance("DES/ECB/PKCS5Padding");
/*  65 */       cipher.init(1, secretKey);
/*  66 */       byte[] arrayOfByte1 = paramString.getBytes("UTF8");
/*  67 */       arrayOfByte = cipher.doFinal(arrayOfByte1);
/*     */     }
/*  69 */     catch (Exception exception) {
/*  70 */       newlog.error("加密异常！", exception);
/*  71 */       return null;
/*     */     } 
/*     */     
/*  74 */     return parseByte2HexStr(arrayOfByte);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String encodeDefChart(String paramString) {
/*  84 */     byte[] arrayOfByte = null;
/*     */     try {
/*  86 */       if (this.pwd == null) {
/*  87 */         this.pwd = "ecology9";
/*     */       }
/*  89 */       DESKeySpec dESKeySpec = new DESKeySpec(this.pwd.getBytes());
/*  90 */       SecretKeyFactory secretKeyFactory = SecretKeyFactory.getInstance("DES");
/*  91 */       SecretKey secretKey = secretKeyFactory.generateSecret(dESKeySpec);
/*     */       
/*  93 */       Cipher cipher = Cipher.getInstance("DES/ECB/PKCS5Padding");
/*  94 */       cipher.init(1, secretKey);
/*  95 */       byte[] arrayOfByte1 = paramString.getBytes();
/*  96 */       arrayOfByte = cipher.doFinal(arrayOfByte1);
/*     */     }
/*  98 */     catch (Exception exception) {
/*  99 */       newlog.error("加密异常！", exception);
/* 100 */       return null;
/*     */     } 
/*     */     
/* 103 */     return parseByte2HexStr(arrayOfByte);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String decode(String paramString) {
/* 113 */     byte[] arrayOfByte1 = null;
/* 114 */     byte[] arrayOfByte2 = parseHexStr2Byte(paramString);
/*     */     try {
/* 116 */       if (this.pwd == null) {
/* 117 */         this.pwd = "ecology9";
/*     */       }
/* 119 */       DESKeySpec dESKeySpec = new DESKeySpec(this.pwd.getBytes());
/* 120 */       SecretKeyFactory secretKeyFactory = SecretKeyFactory.getInstance("DES");
/* 121 */       SecretKey secretKey = secretKeyFactory.generateSecret(dESKeySpec);
/*     */       
/* 123 */       Cipher cipher = Cipher.getInstance("DES/ECB/PKCS5Padding");
/* 124 */       cipher.init(2, secretKey);
/* 125 */       arrayOfByte1 = cipher.doFinal(arrayOfByte2);
/*     */     }
/* 127 */     catch (Exception exception) {
/* 128 */       newlog.error("解密异常！", exception);
/* 129 */       return null;
/*     */     } 
/*     */     
/* 132 */     return new String(arrayOfByte1);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean setPwd(String paramString) {
/* 142 */     this.pwd = paramString;
/* 143 */     return true;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean setIv(String paramString) {
/* 152 */     return true;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String parseByte2HexStr(byte[] paramArrayOfbyte) {
/* 162 */     StringBuffer stringBuffer = new StringBuffer();
/* 163 */     for (byte b = 0; b < paramArrayOfbyte.length; b++) {
/* 164 */       String str = Integer.toHexString(paramArrayOfbyte[b] & 0xFF);
/* 165 */       if (str.length() == 1) {
/* 166 */         str = '0' + str;
/*     */       }
/* 168 */       stringBuffer.append(str.toUpperCase());
/*     */     } 
/* 170 */     return stringBuffer.toString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private byte[] parseHexStr2Byte(String paramString) {
/* 180 */     if (paramString.length() < 1) {
/* 181 */       return null;
/*     */     }
/* 183 */     byte[] arrayOfByte = new byte[paramString.length() / 2];
/* 184 */     for (byte b = 0; b < paramString.length() / 2; b++) {
/* 185 */       int i = Integer.parseInt(paramString.substring(b * 2, b * 2 + 1), 16);
/* 186 */       int j = Integer.parseInt(paramString.substring(b * 2 + 1, b * 2 + 2), 16);
/* 187 */       arrayOfByte[b] = (byte)(i * 16 + j);
/*     */     } 
/* 189 */     return arrayOfByte;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/interfaces/encode/DES.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */