/*     */ package weaver.interfaces.encode;
/*     */ 
/*     */ import java.security.MessageDigest;
/*     */ import javax.crypto.Cipher;
/*     */ import javax.crypto.SecretKey;
/*     */ import javax.crypto.SecretKeyFactory;
/*     */ import javax.crypto.spec.PBEKeySpec;
/*     */ import javax.crypto.spec.PBEParameterSpec;
/*     */ import weaver.general.Base64;
/*     */ import weaver.integration.logging.Logger;
/*     */ import weaver.integration.logging.LoggerFactory;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class PBE_Base64
/*     */   implements IEncode
/*     */ {
/*  30 */   private static Logger newlog = LoggerFactory.getLogger(PBE_Base64.class);
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static final int ITERATIONS = 20;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  40 */   private String pwd = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public PBE_Base64() {}
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public PBE_Base64(String paramString) {
/*  54 */     this.pwd = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String encode(String paramString) {
/*  64 */     byte[] arrayOfByte1 = new byte[8];
/*  65 */     byte[] arrayOfByte2 = null;
/*     */     try {
/*  67 */       if (this.pwd == null) {
/*  68 */         this.pwd = "ecology";
/*     */       }
/*  70 */       MessageDigest messageDigest = MessageDigest.getInstance("MD5");
/*  71 */       messageDigest.update(this.pwd.getBytes());
/*  72 */       byte[] arrayOfByte = messageDigest.digest();
/*  73 */       for (byte b = 0; b < 8; b++) {
/*  74 */         arrayOfByte1[b] = arrayOfByte[b];
/*     */       }
/*     */       
/*  77 */       PBEKeySpec pBEKeySpec = new PBEKeySpec(this.pwd.toCharArray());
/*  78 */       SecretKeyFactory secretKeyFactory = SecretKeyFactory.getInstance("PBEWithMD5AndDES");
/*  79 */       SecretKey secretKey = secretKeyFactory.generateSecret(pBEKeySpec);
/*  80 */       PBEParameterSpec pBEParameterSpec = new PBEParameterSpec(arrayOfByte1, 20);
/*     */       
/*  82 */       Cipher cipher = Cipher.getInstance("PBEWithMD5AndDES");
/*  83 */       cipher.init(1, secretKey, pBEParameterSpec);
/*  84 */       arrayOfByte2 = cipher.doFinal(paramString.getBytes());
/*     */     }
/*  86 */     catch (Exception exception) {
/*  87 */       newlog.error("加密异常！", exception);
/*  88 */       return null;
/*     */     } 
/*     */     
/*  91 */     String str1 = new String(Base64.encode(arrayOfByte1));
/*  92 */     String str2 = new String(Base64.encode(arrayOfByte2));
/*  93 */     return str1 + str2;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String decode(String paramString) {
/* 103 */     byte b = 12;
/* 104 */     byte[] arrayOfByte = null;
/*     */     try {
/* 106 */       if (this.pwd == null) {
/* 107 */         this.pwd = "ecology";
/*     */       }
/* 109 */       String str1 = paramString.substring(0, b);
/* 110 */       String str2 = paramString.substring(b, paramString.length());
/* 111 */       byte[] arrayOfByte1 = Base64.decode(str1.getBytes());
/* 112 */       byte[] arrayOfByte2 = Base64.decode(str2.getBytes());
/*     */       
/* 114 */       PBEKeySpec pBEKeySpec = new PBEKeySpec(this.pwd.toCharArray());
/* 115 */       SecretKeyFactory secretKeyFactory = SecretKeyFactory.getInstance("PBEWithMD5AndDES");
/* 116 */       SecretKey secretKey = secretKeyFactory.generateSecret(pBEKeySpec);
/* 117 */       PBEParameterSpec pBEParameterSpec = new PBEParameterSpec(arrayOfByte1, 20);
/*     */       
/* 119 */       Cipher cipher = Cipher.getInstance("PBEWithMD5AndDES");
/* 120 */       cipher.init(2, secretKey, pBEParameterSpec);
/* 121 */       arrayOfByte = cipher.doFinal(arrayOfByte2);
/*     */     }
/* 123 */     catch (Exception exception) {
/* 124 */       newlog.error("加密异常！", exception);
/* 125 */       return null;
/*     */     } 
/*     */     
/* 128 */     return new String(arrayOfByte);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean setPwd(String paramString) {
/* 138 */     this.pwd = paramString;
/* 139 */     return true;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean setIv(String paramString) {
/* 148 */     return true;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/interfaces/encode/PBE_Base64.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */