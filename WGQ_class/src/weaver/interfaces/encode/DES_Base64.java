/*     */ package weaver.interfaces.encode;
/*     */ 
/*     */ import javax.crypto.Cipher;
/*     */ import javax.crypto.SecretKey;
/*     */ import javax.crypto.SecretKeyFactory;
/*     */ import javax.crypto.spec.DESKeySpec;
/*     */ import weaver.general.Base64;
/*     */ import weaver.integration.logging.Logger;
/*     */ import weaver.integration.logging.LoggerFactory;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class DES_Base64
/*     */   implements IEncode
/*     */ {
/*  27 */   private static Logger newlog = LoggerFactory.getLogger(DES_Base64.class);
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  32 */   private String pwd = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public DES_Base64() {}
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public DES_Base64(String paramString) {
/*  46 */     this.pwd = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String encode(String paramString) {
/*  56 */     byte[] arrayOfByte = null;
/*     */     try {
/*  58 */       if (this.pwd == null) {
/*  59 */         this.pwd = "ecology9";
/*     */       }
/*  61 */       DESKeySpec dESKeySpec = new DESKeySpec(this.pwd.getBytes());
/*  62 */       SecretKeyFactory secretKeyFactory = SecretKeyFactory.getInstance("DES");
/*  63 */       SecretKey secretKey = secretKeyFactory.generateSecret(dESKeySpec);
/*     */       
/*  65 */       Cipher cipher = Cipher.getInstance("DES/ECB/PKCS5Padding");
/*  66 */       cipher.init(1, secretKey);
/*  67 */       byte[] arrayOfByte1 = paramString.getBytes();
/*  68 */       arrayOfByte = cipher.doFinal(arrayOfByte1);
/*     */     }
/*  70 */     catch (Exception exception) {
/*  71 */       newlog.error("加密异常！", exception);
/*  72 */       return null;
/*     */     } 
/*     */     
/*  75 */     return new String(Base64.encode(arrayOfByte));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String decode(String paramString) {
/*  85 */     byte[] arrayOfByte1 = null;
/*  86 */     byte[] arrayOfByte2 = Base64.decode(paramString.getBytes());
/*     */     try {
/*  88 */       if (this.pwd == null) {
/*  89 */         this.pwd = "ecology9";
/*     */       }
/*  91 */       DESKeySpec dESKeySpec = new DESKeySpec(this.pwd.getBytes());
/*  92 */       SecretKeyFactory secretKeyFactory = SecretKeyFactory.getInstance("DES");
/*  93 */       SecretKey secretKey = secretKeyFactory.generateSecret(dESKeySpec);
/*     */       
/*  95 */       Cipher cipher = Cipher.getInstance("DES/ECB/PKCS5Padding");
/*  96 */       cipher.init(2, secretKey);
/*  97 */       arrayOfByte1 = cipher.doFinal(arrayOfByte2);
/*     */     }
/*  99 */     catch (Exception exception) {
/* 100 */       newlog.error("解密异常！", exception);
/* 101 */       return null;
/*     */     } 
/*     */     
/* 104 */     return new String(arrayOfByte1);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean setPwd(String paramString) {
/* 114 */     this.pwd = paramString;
/* 115 */     return true;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean setIv(String paramString) {
/* 124 */     return true;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/interfaces/encode/DES_Base64.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */