/*     */ package weaver.interfaces.encode;
/*     */ 
/*     */ import java.security.SecureRandom;
/*     */ import javax.crypto.Cipher;
/*     */ import javax.crypto.KeyGenerator;
/*     */ import javax.crypto.SecretKey;
/*     */ import javax.crypto.spec.SecretKeySpec;
/*     */ import weaver.integration.logging.Logger;
/*     */ import weaver.integration.logging.LoggerFactory;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class AES
/*     */   implements IEncode
/*     */ {
/*  28 */   private static Logger newlog = LoggerFactory.getLogger(AES.class);
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  33 */   private String pwd = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public AES() {}
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public AES(String paramString) {
/*  47 */     this.pwd = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String encode(String paramString) {
/*  57 */     byte[] arrayOfByte = null;
/*     */     try {
/*  59 */       KeyGenerator keyGenerator = KeyGenerator.getInstance("AES");
/*  60 */       SecureRandom secureRandom = SecureRandom.getInstance("SHA1PRNG");
/*  61 */       if (this.pwd == null) {
/*  62 */         this.pwd = "ecology9";
/*     */       }
/*  64 */       secureRandom.setSeed(this.pwd.getBytes());
/*  65 */       keyGenerator.init(128, secureRandom);
/*  66 */       SecretKey secretKey = keyGenerator.generateKey();
/*  67 */       byte[] arrayOfByte1 = secretKey.getEncoded();
/*  68 */       SecretKeySpec secretKeySpec = new SecretKeySpec(arrayOfByte1, "AES");
/*     */ 
/*     */       
/*  71 */       Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
/*  72 */       cipher.init(1, secretKeySpec);
/*  73 */       byte[] arrayOfByte2 = paramString.getBytes();
/*  74 */       arrayOfByte = cipher.doFinal(arrayOfByte2);
/*     */     }
/*  76 */     catch (Exception exception) {
/*  77 */       newlog.error("加密异常！", exception);
/*  78 */       return null;
/*     */     } 
/*     */     
/*  81 */     return parseByte2HexStr(arrayOfByte);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String decode(String paramString) {
/*  91 */     byte[] arrayOfByte1 = null;
/*  92 */     byte[] arrayOfByte2 = parseHexStr2Byte(paramString);
/*     */     try {
/*  94 */       KeyGenerator keyGenerator = KeyGenerator.getInstance("AES");
/*  95 */       SecureRandom secureRandom = SecureRandom.getInstance("SHA1PRNG");
/*  96 */       if (this.pwd == null) {
/*  97 */         this.pwd = "ecology9";
/*     */       }
/*  99 */       secureRandom.setSeed(this.pwd.getBytes());
/* 100 */       keyGenerator.init(128, secureRandom);
/* 101 */       SecretKey secretKey = keyGenerator.generateKey();
/* 102 */       byte[] arrayOfByte = secretKey.getEncoded();
/* 103 */       SecretKeySpec secretKeySpec = new SecretKeySpec(arrayOfByte, "AES");
/*     */ 
/*     */       
/* 106 */       Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
/* 107 */       cipher.init(2, secretKeySpec);
/* 108 */       arrayOfByte1 = cipher.doFinal(arrayOfByte2);
/*     */     }
/* 110 */     catch (Exception exception) {
/* 111 */       newlog.error("解密异常！", exception);
/* 112 */       return null;
/*     */     } 
/*     */     
/* 115 */     return new String(arrayOfByte1);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean setPwd(String paramString) {
/* 125 */     this.pwd = paramString;
/* 126 */     return true;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean setIv(String paramString) {
/* 135 */     return true;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String parseByte2HexStr(byte[] paramArrayOfbyte) {
/* 145 */     StringBuffer stringBuffer = new StringBuffer();
/* 146 */     for (byte b = 0; b < paramArrayOfbyte.length; b++) {
/* 147 */       String str = Integer.toHexString(paramArrayOfbyte[b] & 0xFF);
/* 148 */       if (str.length() == 1) {
/* 149 */         str = '0' + str;
/*     */       }
/* 151 */       stringBuffer.append(str.toUpperCase());
/*     */     } 
/* 153 */     return stringBuffer.toString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private byte[] parseHexStr2Byte(String paramString) {
/* 163 */     if (paramString.length() < 1) {
/* 164 */       return null;
/*     */     }
/* 166 */     byte[] arrayOfByte = new byte[paramString.length() / 2];
/* 167 */     for (byte b = 0; b < paramString.length() / 2; b++) {
/* 168 */       int i = Integer.parseInt(paramString.substring(b * 2, b * 2 + 1), 16);
/* 169 */       int j = Integer.parseInt(paramString.substring(b * 2 + 1, b * 2 + 2), 16);
/* 170 */       arrayOfByte[b] = (byte)(i * 16 + j);
/*     */     } 
/* 172 */     return arrayOfByte;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/interfaces/encode/AES.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */