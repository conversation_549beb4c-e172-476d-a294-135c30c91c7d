/*    */ package weaver.interfaces.encode;
/*    */ 
/*    */ import com.engine.common.util.Base64Util;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class AuthorizationBasic4OAuth2
/*    */   implements IEncode
/*    */ {
/*    */   public String encode(String paramString) {
/* 12 */     return "Basic " + Base64Util.encoder(paramString);
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public String decode(String paramString) {
/* 18 */     return null;
/*    */   }
/*    */ 
/*    */   
/*    */   public boolean setPwd(String paramString) {
/* 23 */     return false;
/*    */   }
/*    */ 
/*    */   
/*    */   public boolean setIv(String paramString) {
/* 28 */     return false;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/interfaces/encode/AuthorizationBasic4OAuth2.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */