/*    */ package weaver.interfaces.encode;
/*    */ 
/*    */ import java.security.MessageDigest;
/*    */ import weaver.general.Base64;
/*    */ import weaver.integration.logging.Logger;
/*    */ import weaver.integration.logging.LoggerFactory;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class MD5_Base64
/*    */   implements IEncode
/*    */ {
/* 24 */   private static Logger newlog = LoggerFactory.getLogger(MD5_Base64.class);
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String encode(String paramString) {
/* 33 */     byte[] arrayOfByte = null;
/*    */     try {
/* 35 */       MessageDigest messageDigest = MessageDigest.getInstance("MD5");
/* 36 */       messageDigest.update(paramString.getBytes("UTF-8"));
/* 37 */       arrayOfByte = messageDigest.digest();
/*    */     }
/* 39 */     catch (Exception exception) {
/* 40 */       newlog.error("加密异常！", exception);
/* 41 */       return null;
/*    */     } 
/*    */     
/* 44 */     return new String(Base64.encode(arrayOfByte));
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String decode(String paramString) {
/* 54 */     return "";
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public boolean setPwd(String paramString) {
/* 64 */     return true;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public boolean setIv(String paramString) {
/* 73 */     return true;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public static void main(String[] paramArrayOfString) {
/* 81 */     MD5_Base64 mD5_Base64 = new MD5_Base64();
/* 82 */     String str1 = "测试test";
/*    */ 
/*    */     
/* 85 */     String str2 = mD5_Base64.encode(str1);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/interfaces/encode/MD5_Base64.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */