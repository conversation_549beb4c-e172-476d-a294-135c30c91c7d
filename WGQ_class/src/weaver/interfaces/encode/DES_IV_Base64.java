/*     */ package weaver.interfaces.encode;
/*     */ 
/*     */ import javax.crypto.Cipher;
/*     */ import javax.crypto.SecretKey;
/*     */ import javax.crypto.SecretKeyFactory;
/*     */ import javax.crypto.spec.DESKeySpec;
/*     */ import javax.crypto.spec.IvParameterSpec;
/*     */ import weaver.general.Base64;
/*     */ import weaver.integration.logging.Logger;
/*     */ import weaver.integration.logging.LoggerFactory;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class DES_IV_Base64
/*     */   implements IEncode
/*     */ {
/*  28 */   private static Logger newlog = LoggerFactory.getLogger(DES_IV_Base64.class);
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  33 */   private String pwd = null;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  38 */   private String iv = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public DES_IV_Base64() {}
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public DES_IV_Base64(String paramString1, String paramString2) {
/*  53 */     this.pwd = paramString1;
/*  54 */     this.iv = paramString2;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String encode(String paramString) {
/*  64 */     byte[] arrayOfByte = null;
/*     */     try {
/*  66 */       if (this.pwd == null) {
/*  67 */         this.pwd = "ecology9";
/*     */       }
/*  69 */       DESKeySpec dESKeySpec = new DESKeySpec(this.pwd.getBytes());
/*  70 */       SecretKeyFactory secretKeyFactory = SecretKeyFactory.getInstance("DES");
/*  71 */       SecretKey secretKey = secretKeyFactory.generateSecret(dESKeySpec);
/*  72 */       if (this.iv == null) {
/*  73 */         this.iv = "ecology9";
/*     */       }
/*     */       
/*  76 */       IvParameterSpec ivParameterSpec = new IvParameterSpec(this.iv.getBytes());
/*     */       
/*  78 */       Cipher cipher = Cipher.getInstance("DES/CBC/PKCS5Padding");
/*  79 */       cipher.init(1, secretKey, ivParameterSpec);
/*  80 */       byte[] arrayOfByte1 = paramString.getBytes();
/*  81 */       arrayOfByte = cipher.doFinal(arrayOfByte1);
/*     */     }
/*  83 */     catch (Exception exception) {
/*  84 */       newlog.error("加密异常！", exception);
/*  85 */       return null;
/*     */     } 
/*     */     
/*  88 */     return new String(Base64.encode(arrayOfByte));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String decode(String paramString) {
/*  98 */     byte[] arrayOfByte1 = null;
/*  99 */     byte[] arrayOfByte2 = Base64.decode(paramString.getBytes());
/*     */     try {
/* 101 */       if (this.pwd == null) {
/* 102 */         this.pwd = "ecology9";
/*     */       }
/* 104 */       DESKeySpec dESKeySpec = new DESKeySpec(this.pwd.getBytes());
/* 105 */       SecretKeyFactory secretKeyFactory = SecretKeyFactory.getInstance("DES");
/* 106 */       SecretKey secretKey = secretKeyFactory.generateSecret(dESKeySpec);
/* 107 */       if (this.iv == null) {
/* 108 */         this.iv = "ecology9";
/*     */       }
/*     */       
/* 111 */       IvParameterSpec ivParameterSpec = new IvParameterSpec(this.iv.getBytes());
/*     */       
/* 113 */       Cipher cipher = Cipher.getInstance("DES/CBC/PKCS5Padding");
/* 114 */       cipher.init(2, secretKey, ivParameterSpec);
/* 115 */       arrayOfByte1 = cipher.doFinal(arrayOfByte2);
/*     */     }
/* 117 */     catch (Exception exception) {
/* 118 */       newlog.error("解密异常！", exception);
/* 119 */       return null;
/*     */     } 
/*     */     
/* 122 */     return new String(arrayOfByte1);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean setPwd(String paramString) {
/* 132 */     this.pwd = paramString;
/* 133 */     return true;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean setIv(String paramString) {
/* 142 */     this.iv = paramString;
/* 143 */     return true;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/interfaces/encode/DES_IV_Base64.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */