/*    */ package weaver.interfaces.encode;
/*    */ 
/*    */ import java.security.MessageDigest;
/*    */ import org.apache.commons.codec.binary.Hex;
/*    */ import weaver.integration.logging.Logger;
/*    */ import weaver.integration.logging.LoggerFactory;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class SHA1
/*    */   implements IEncode
/*    */ {
/* 24 */   private static Logger newlog = LoggerFactory.getLogger(SHA1.class);
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String encode(String paramString) {
/*    */     try {
/* 34 */       MessageDigest messageDigest = MessageDigest.getInstance("SHA-1");
/* 35 */       messageDigest.update(paramString.getBytes("utf-8"));
/* 36 */       byte[] arrayOfByte = messageDigest.digest();
/* 37 */       return byteToHexString(arrayOfByte);
/*    */     }
/* 39 */     catch (Exception exception) {
/* 40 */       newlog.error("加密异常！", exception);
/* 41 */       return null;
/*    */     } 
/*    */   }
/*    */   public static String byteToHexString(byte[] paramArrayOfbyte) {
/* 45 */     return String.valueOf(Hex.encodeHex(paramArrayOfbyte));
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String decode(String paramString) {
/* 56 */     return "";
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public boolean setPwd(String paramString) {
/* 66 */     return true;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public boolean setIv(String paramString) {
/* 75 */     return true;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public static void main(String[] paramArrayOfString) {
/* 84 */     SHA1 sHA1 = new SHA1();
/* 85 */     String str1 = "测试test";
/*    */ 
/*    */     
/* 88 */     String str2 = sHA1.encode(str1);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/interfaces/encode/SHA1.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */