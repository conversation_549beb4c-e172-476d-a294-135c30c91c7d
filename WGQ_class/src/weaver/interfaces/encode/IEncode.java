package weaver.interfaces.encode;

public interface IEncode {
  String encode(String paramString);
  
  String decode(String paramString);
  
  boolean setPwd(String paramString);
  
  boolean setIv(String paramString);
}


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/interfaces/encode/IEncode.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */