/*    */ package weaver.interfaces.outter;
/*    */ 
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.BaseBean;
/*    */ import weaver.general.IpUtils;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class CheckIpNetWork
/*    */   extends BaseBean
/*    */ {
/*    */   public boolean checkIpSeg(String paramString1, String paramString2) {
/* 37 */     RecordSet recordSet = new RecordSet();
/*    */     
/* 39 */     boolean bool = true;
/* 40 */     String str1 = "";
/* 41 */     String str2 = "";
/* 42 */     String str3 = "select * from outter_network where sysid='" + paramString2 + "'";
/* 43 */     recordSet.executeSql(str3);
/*    */ 
/*    */ 
/*    */     
/* 47 */     while (recordSet.next()) {
/* 48 */       str1 = recordSet.getString("inceptipaddress");
/* 49 */       str2 = recordSet.getString("endipaddress");
/*    */       
/* 51 */       if (str1.contains(":") && str2.contains(":")) {
/*    */         
/* 53 */         String str4 = IpUtils.parseAbbreviationToFullIPv6(str1);
/* 54 */         String str5 = IpUtils.parseAbbreviationToFullIPv6(str2);
/* 55 */         String str6 = IpUtils.parseAbbreviationToFullIPv6(paramString1);
/* 56 */         if (str6.compareTo(str4) >= 0 && str6.compareTo(str5) <= 0) {
/* 57 */           bool = false; break;
/*    */         } 
/*    */         continue;
/*    */       } 
/* 61 */       if (str1.contains(".") && str2.contains(".")) {
/*    */         
/* 63 */         if (paramString1.equals("0:0:0:0:0:0:0:1")) paramString1 = "127.0.0.1"; 
/* 64 */         long l1 = IpUtils.ip2number(str1);
/* 65 */         long l2 = IpUtils.ip2number(str2);
/* 66 */         long l3 = IpUtils.ip2number(paramString1);
/* 67 */         if (l3 >= l1 && l3 <= l2) {
/* 68 */           bool = false;
/*    */           break;
/*    */         } 
/* 71 */         if (l3 <= l1 || l3 >= l2) {
/* 72 */           bool = true;
/*    */         }
/*    */       } 
/*    */     } 
/* 76 */     return bool;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/interfaces/outter/CheckIpNetWork.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */