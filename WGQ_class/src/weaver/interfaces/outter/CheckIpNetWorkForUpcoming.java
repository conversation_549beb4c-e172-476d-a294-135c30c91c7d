/*    */ package weaver.interfaces.outter;
/*    */ 
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.BaseBean;
/*    */ import weaver.general.IpUtils;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class CheckIpNetWorkForUpcoming
/*    */   extends BaseBean
/*    */ {
/*    */   public boolean checkIpSeg(String paramString) {
/* 36 */     RecordSet recordSet = new RecordSet();
/*    */     
/* 38 */     boolean bool = true;
/* 39 */     String str1 = "";
/* 40 */     String str2 = "";
/* 41 */     String str3 = "select * from outer_network_upcoming";
/* 42 */     recordSet.executeSql(str3);
/* 43 */     if (paramString.equals("0:0:0:0:0:0:0:1")) paramString = "127.0.0.1";
/*    */ 
/*    */ 
/*    */     
/* 47 */     while (recordSet.next()) {
/* 48 */       str1 = recordSet.getString("start_ip");
/* 49 */       str2 = recordSet.getString("end_ip");
/* 50 */       if (str1.contains(":") && str2.contains(":")) {
/*    */         
/* 52 */         String str4 = IpUtils.parseAbbreviationToFullIPv6(str1);
/* 53 */         String str5 = IpUtils.parseAbbreviationToFullIPv6(str2);
/* 54 */         String str6 = IpUtils.parseAbbreviationToFullIPv6(paramString);
/* 55 */         if (str6.compareTo(str4) >= 0 && str6.compareTo(str5) <= 0) {
/* 56 */           bool = false; break;
/*    */         } 
/*    */         continue;
/*    */       } 
/* 60 */       if (str1.contains(".") && str2.contains(".")) {
/* 61 */         long l1 = IpUtils.ip2number(str1);
/* 62 */         long l2 = IpUtils.ip2number(str2);
/* 63 */         long l3 = IpUtils.ip2number(paramString);
/* 64 */         if (l3 >= l1 && l3 <= l2) {
/* 65 */           bool = false;
/*    */           break;
/*    */         } 
/* 68 */         if (l3 <= l1 || l3 >= l2) {
/* 69 */           bool = true;
/*    */         }
/*    */       } 
/*    */     } 
/*    */ 
/*    */     
/* 75 */     return bool;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/interfaces/outter/CheckIpNetWorkForUpcoming.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */