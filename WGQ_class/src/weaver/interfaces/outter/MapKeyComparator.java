/*   */ package weaver.interfaces.outter;
/*   */ 
/*   */ import java.util.Comparator;
/*   */ 
/*   */ public class MapKeyComparator implements Comparator<String> {
/*   */   public int compare(String paramString1, String paramString2) {
/* 7 */     return paramString1.compareToIgnoreCase(paramString2);
/*   */   }
/*   */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/interfaces/outter/MapKeyComparator.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */