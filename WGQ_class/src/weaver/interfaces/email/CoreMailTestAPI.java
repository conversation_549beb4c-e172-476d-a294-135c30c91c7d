/*     */ package weaver.interfaces.email;
/*     */ 
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.GCONST;
/*     */ import weaver.general.Util;
/*     */ import weaver.integration.logging.Logger;
/*     */ import weaver.integration.logging.LoggerFactory;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CoreMailTestAPI
/*     */   extends BaseBean
/*     */ {
/*  20 */   private static Logger newlog = LoggerFactory.getLogger(CoreMailTestAPI.class);
/*     */   
/*  22 */   private static String defaultPwd = GCONST.getDefaultPassword();
/*  23 */   private static CoreMailTestAPI instance = null;
/*  24 */   private static CoreMailUtil coremailUtil = null;
/*  25 */   private static String isuse = "0";
/*  26 */   private static String issync = "0";
/*  27 */   private static String systemAddress = "";
/*  28 */   private static Integer emailPort = Integer.valueOf(6195);
/*  29 */   private static String orgId = "";
/*  30 */   private static String providerId = "";
/*  31 */   private static String domain = "";
/*     */   
/*     */   public static String getIsuse() {
/*  34 */     return isuse;
/*     */   }
/*     */   
/*     */   public static String getIssync() {
/*  38 */     return issync;
/*     */   }
/*     */   
/*     */   public static String getSystemAddress() {
/*  42 */     return systemAddress;
/*     */   }
/*     */   
/*     */   public static Integer getEmailPort() {
/*  46 */     return emailPort;
/*     */   }
/*     */   
/*     */   public static String getOrgId() {
/*  50 */     return orgId;
/*     */   }
/*     */   
/*     */   public static String getProviderId() {
/*  54 */     return providerId;
/*     */   }
/*     */   
/*     */   public static String getDomain() {
/*  58 */     return domain;
/*     */   }
/*     */   
/*     */   public CoreMailTestAPI() {
/*  62 */     coremailUtil = new CoreMailUtil();
/*  63 */     defaultPwd = getPropValue("coremail", "DEFAULTPASSWORD");
/*     */   }
/*     */   
/*     */   public static synchronized CoreMailTestAPI getInstance() {
/*  67 */     if (instance == null) {
/*  68 */       synchronized (CoreMailAPI.class) {
/*  69 */         if (instance == null) {
/*  70 */           instance = new CoreMailTestAPI();
/*     */         }
/*     */       } 
/*     */     }
/*  74 */     instance.getCoreMailSet();
/*  75 */     return instance;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void getCoreMailSet() {
/*  82 */     RecordSet recordSet = new RecordSet();
/*  83 */     String str = "select * from coremailsetting ";
/*  84 */     recordSet.executeSql(str);
/*  85 */     if (recordSet.next()) {
/*  86 */       isuse = Util.null2String(recordSet.getString("isuse"));
/*  87 */       issync = Util.null2String(recordSet.getString("issync"));
/*  88 */       systemAddress = Util.null2String(recordSet.getString("systemaddress"));
/*  89 */       emailPort = Integer.valueOf(Util.getIntValue(recordSet.getString("emailPort"), 6195));
/*  90 */       orgId = Util.null2String(recordSet.getString("orgid"));
/*  91 */       providerId = Util.null2String(recordSet.getString("providerid"));
/*  92 */       domain = Util.null2String(recordSet.getString("basedomain"));
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean userExist(String paramString) throws Exception {
/* 103 */     boolean bool = false;
/*     */     
/*     */     try {
/* 106 */       RecordSet recordSet = new RecordSet();
/* 107 */       recordSet.executeSql("select 1 from coremail_user where user_at_domain = '" + paramString + "' ");
/* 108 */       if (recordSet.next()) {
/* 109 */         bool = true;
/*     */       }
/* 111 */       newlog.error("检测CoreMail邮箱账号是否存在：" + paramString + "，result=" + bool);
/* 112 */     } catch (Exception exception) {
/* 113 */       newlog.error("检测CoreMail邮箱账号是否存在出现异常：", exception);
/*     */     } 
/* 115 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static synchronized boolean initOrgAndUser() throws Exception {
/* 124 */     if ("0".equals(isuse)) {
/* 125 */       return false;
/*     */     }
/* 127 */     if ("0".equals(issync)) {
/* 128 */       return false;
/*     */     }
/*     */ 
/*     */     
/*     */     try {
/* 133 */       newlog.error("------------------------------初始化OA组织架构和人员到CoreMail开始------------------------------");
/*     */       
/* 135 */       RecordSet recordSet1 = new RecordSet();
/* 136 */       recordSet1.executeSql("select id, subcompanyname, supsubcomid, canceled from hrmsubcompany order by id asc");
/* 137 */       while (recordSet1.next()) {
/* 138 */         String str1 = Util.null2String(recordSet1.getString("canceled"));
/* 139 */         String str2 = "com_" + Util.null2String(recordSet1.getString("id"));
/* 140 */         String str3 = Util.null2String(recordSet1.getString("subcompanyname"));
/* 141 */         String str4 = Util.null2String(recordSet1.getString("supsubcomid"));
/* 142 */         if (str4 == null || "".equals(str4)) {
/* 143 */           str4 = "0";
/*     */         }
/*     */         
/* 146 */         boolean bool = false;
/*     */         
/* 148 */         RecordSet recordSet4 = new RecordSet();
/* 149 */         RecordSet recordSet5 = new RecordSet();
/* 150 */         recordSet4.executeSql("select 1 from coremail_unit where org_unit_id = '" + str2 + "' ");
/* 151 */         if (recordSet4.next()) {
/* 152 */           if (!"1".equals(str1)) {
/* 153 */             if ("0".equals(str4)) {
/* 154 */               bool = recordSet5.executeSql("update coremail_unit set org_unit_name = '" + str3 + "' where org_unit_id = '" + str2 + "' ");
/*     */             } else {
/*     */               
/* 157 */               bool = recordSet5.executeSql("update coremail_unit set org_unit_name = '" + str3 + "', parent_org_unit_id = 'com_" + str4 + "' where org_unit_id = '" + str2 + "' ");
/*     */             } 
/*     */           } else {
/*     */             
/* 161 */             bool = recordSet5.executeSql("delete from coremail_unit where org_unit_id = '" + str2 + "' ");
/*     */           }
/*     */         
/* 164 */         } else if (!"1".equals(str1)) {
/* 165 */           if ("0".equals(str4)) {
/* 166 */             bool = recordSet5.executeSql("insert into coremail_unit(org_unit_id, org_unit_name, parent_org_unit_id) values('" + str2 + "','" + str3 + "','')");
/*     */           } else {
/* 168 */             bool = recordSet5.executeSql("insert into coremail_unit(org_unit_id, org_unit_name, parent_org_unit_id) values('" + str2 + "','" + str3 + "','com_" + str4 + "')");
/*     */           } 
/*     */         } else {
/*     */           continue;
/*     */         } 
/*     */ 
/*     */         
/* 175 */         if (bool) {
/* 176 */           coremailUtil.insertCoreMailSynLog("1", str3, "4", "1", ""); continue;
/*     */         } 
/* 178 */         coremailUtil.insertCoreMailSynLog("1", str3, "4", "2", "同步分部失败：SQL执行报错！");
/*     */       } 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 184 */       RecordSet recordSet2 = new RecordSet();
/* 185 */       recordSet2.executeSql("select id, departmentname, subcompanyid1, supdepid, canceled from hrmdepartment order by id asc");
/* 186 */       while (recordSet2.next()) {
/* 187 */         String str1 = Util.null2String(recordSet2.getString("canceled"));
/* 188 */         String str2 = Util.null2String(recordSet2.getString("id"));
/* 189 */         String str3 = Util.null2String(recordSet2.getString("departmentname"));
/* 190 */         String str4 = Util.null2String(recordSet2.getString("subcompanyid1"));
/* 191 */         String str5 = Util.null2String(recordSet2.getString("supdepid"));
/* 192 */         if (str5 == null || "".equals(str5)) {
/* 193 */           str5 = "0";
/*     */         }
/*     */         
/* 196 */         boolean bool = false;
/*     */         
/* 198 */         RecordSet recordSet4 = new RecordSet();
/* 199 */         RecordSet recordSet5 = new RecordSet();
/* 200 */         recordSet4.executeSql("select 1 from coremail_unit where org_unit_id = '" + str2 + "' ");
/* 201 */         if (recordSet4.next()) {
/* 202 */           if (!"1".equals(str1)) {
/* 203 */             if ("0".equals(str5)) {
/* 204 */               bool = recordSet5.executeSql("update coremail_unit set org_unit_name = '" + str3 + "', parent_org_unit_id = 'com_" + str4 + "' where org_unit_id = '" + str2 + "' ");
/*     */             } else {
/* 206 */               bool = recordSet5.executeSql("update coremail_unit set org_unit_name = '" + str3 + "', parent_org_unit_id = '" + str5 + "' where org_unit_id = '" + str2 + "' ");
/*     */             } 
/*     */           } else {
/* 209 */             bool = recordSet5.executeSql("delete from coremail_unit where org_unit_id = '" + str2 + "' ");
/*     */           }
/*     */         
/* 212 */         } else if (!"1".equals(str1)) {
/* 213 */           if ("0".equals(str5)) {
/* 214 */             bool = recordSet5.executeSql("insert into coremail_unit(org_unit_id, org_unit_name, parent_org_unit_id) values('" + str2 + "','" + str3 + "','com_" + str4 + "')");
/*     */           } else {
/* 216 */             bool = recordSet5.executeSql("insert into coremail_unit(org_unit_id, org_unit_name, parent_org_unit_id) values('" + str2 + "','" + str3 + "','" + str5 + "')");
/*     */           } 
/*     */         } else {
/*     */           continue;
/*     */         } 
/*     */ 
/*     */         
/* 223 */         if (bool) {
/* 224 */           coremailUtil.insertCoreMailSynLog("2", str3, "4", "1", ""); continue;
/*     */         } 
/* 226 */         coremailUtil.insertCoreMailSynLog("2", str3, "4", "2", "同步部门失败：SQL执行报错！");
/*     */       } 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 232 */       RecordSet recordSet3 = new RecordSet();
/* 233 */       recordSet3.executeSql("select id, loginid, lastname, sex, birthday, telephone, mobile, email, departmentid, status from hrmresource where email like '%" + domain + "%' and (accounttype = 0 or accounttype is null)");
/*     */       
/* 235 */       while (recordSet3.next()) {
/* 236 */         String str1 = Util.null2String(recordSet3.getString("id"));
/* 237 */         String str2 = Util.null2String(recordSet3.getString("loginid"));
/* 238 */         String str3 = Util.null2String(recordSet3.getString("lastname"));
/* 239 */         String str4 = Util.null2String(recordSet3.getString("sex"));
/* 240 */         String str5 = Util.null2String(recordSet3.getString("birthday"));
/* 241 */         String str6 = Util.null2String(recordSet3.getString("telephone"));
/* 242 */         String str7 = Util.null2String(recordSet3.getString("mobile"));
/* 243 */         String str8 = Util.null2String(recordSet3.getString("email"));
/* 244 */         String str9 = Util.null2String(recordSet3.getString("departmentid"));
/* 245 */         String str10 = Util.null2String(recordSet3.getString("status"));
/*     */         
/* 247 */         boolean bool = false;
/* 248 */         RecordSet recordSet = new RecordSet();
/* 249 */         if (userExist(str8)) {
/* 250 */           if (str10.equals("0") || str10.equals("1") || str10.equals("2") || str10.equals("3")) {
/* 251 */             bool = recordSet.executeSql("update coremail_user set smsaddr = '" + str1 + "', true_name = '" + str3 + "', nick_name = '" + str2 + "', domain_name = '" + domain + "', user_status = '0', gender = '" + str4 + "', birthday = '" + str5 + "', mobile_number = '" + str7 + "', company_phone = '" + str6 + "', org_unit_id = '" + str9 + "' where user_at_domain = '" + str8 + "'");
/*     */           }
/*     */           else {
/*     */             
/* 255 */             bool = recordSet.executeSql("delete from coremail_user where user_at_domain = '" + str8 + "' ");
/*     */           }
/*     */         
/* 258 */         } else if (str10.equals("0") || str10.equals("1") || str10.equals("2") || str10.equals("3")) {
/* 259 */           bool = recordSet.executeSql("insert into coremail_user(smsaddr, user_at_domain, true_name, nick_name, domain_name, user_status, gender, birthday, mobile_number, company_phone, password, org_unit_id, cos_id) values('" + str1 + "','" + str8 + "','" + str3 + "','" + str2 + "','" + domain + "','0','" + str4 + "','" + str5 + "','" + str7 + "','" + str6 + "','" + defaultPwd + "','" + str9 + "','1')");
/*     */         } else {
/*     */           continue;
/*     */         } 
/*     */ 
/*     */ 
/*     */ 
/*     */         
/* 267 */         if (bool) {
/* 268 */           coremailUtil.insertCoreMailSynLog("3", str3, "4", "1", ""); continue;
/*     */         } 
/* 270 */         coremailUtil.insertCoreMailSynLog("3", str3, "4", "2", "同步人员失败：SQL执行报错！");
/*     */       } 
/*     */ 
/*     */       
/* 274 */       newlog.error("------------------------------初始化OA组织架构和人员到CoreMail结束------------------------------");
/* 275 */       return true;
/* 276 */     } catch (Exception exception) {
/* 277 */       newlog.error("OA组织架构和人员初始化到CoreMail，出现异常：", exception);
/* 278 */       return false;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean synOrg(String paramString1, String paramString2, String paramString3, String paramString4) throws Exception {
/* 291 */     boolean bool = false;
/*     */     
/* 293 */     if ("0".equals(isuse)) {
/* 294 */       return false;
/*     */     }
/* 296 */     if ("0".equals(issync)) {
/* 297 */       return false;
/*     */     }
/*     */ 
/*     */     
/* 301 */     String str1 = (paramString1.indexOf("com_") > -1) ? "1" : "2";
/* 302 */     String str2 = "";
/*     */ 
/*     */     
/*     */     try {
/* 306 */       RecordSet recordSet1 = new RecordSet();
/* 307 */       RecordSet recordSet2 = new RecordSet();
/* 308 */       String str = "";
/* 309 */       recordSet1.executeSql("select 1 from coremail_unit where org_unit_id = '" + paramString1 + "' ");
/* 310 */       if (recordSet1.next()) {
/* 311 */         if (!"1".equals(paramString4)) {
/* 312 */           str = "update coremail_unit set org_unit_name = '" + paramString2 + "' , parent_org_unit_id = '" + paramString3 + "' where org_unit_id = '" + paramString1 + "' ";
/* 313 */           bool = recordSet2.executeSql(str);
/* 314 */           str2 = "2";
/*     */         } else {
/* 316 */           bool = recordSet2.executeSql("delete from coremail_unit where org_unit_id = '" + paramString1 + "' ");
/* 317 */           str2 = "3";
/*     */         }
/*     */       
/* 320 */       } else if (!"1".equals(paramString4)) {
/* 321 */         str = "insert into coremail_unit(org_unit_id, org_unit_name, parent_org_unit_id) values('" + paramString1 + "','" + paramString2 + "','" + paramString3 + "')";
/* 322 */         bool = recordSet2.executeSql(str);
/* 323 */         str2 = "1";
/*     */       } 
/*     */ 
/*     */       
/* 327 */       if (bool) {
/* 328 */         coremailUtil.insertCoreMailSynLog(str1, paramString2, str2, "1", "");
/* 329 */         bool = true;
/*     */       } else {
/* 331 */         coremailUtil.insertCoreMailSynLog(str1, paramString2, str2, "2", "实时同步分部或部门失败：SQL执行报错！");
/* 332 */         bool = false;
/*     */       } 
/* 334 */     } catch (Exception exception) {
/* 335 */       newlog.error("实时同步分部或部门到CoreMail邮件系统出现异常：", exception);
/* 336 */       bool = false;
/*     */     } 
/* 338 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean synUser(String paramString) throws Exception {
/* 348 */     boolean bool = false;
/*     */     
/* 350 */     if ("0".equals(isuse)) {
/* 351 */       return false;
/*     */     }
/* 353 */     if ("0".equals(issync)) {
/* 354 */       return false;
/*     */     }
/*     */ 
/*     */     
/* 358 */     String str = "";
/*     */     
/*     */     try {
/* 361 */       RecordSet recordSet1 = new RecordSet();
/* 362 */       RecordSet recordSet2 = new RecordSet();
/* 363 */       recordSet1.executeSql("select id, loginid, lastname, sex, birthday, telephone, mobile, email, departmentid from hrmresource where id = " + paramString + " and email like '%" + domain + "%' and (accounttype = 0 or accounttype is null)");
/*     */       
/* 365 */       if (recordSet1.next()) {
/* 366 */         String str1 = Util.null2String(recordSet1.getString("loginid"));
/* 367 */         String str2 = Util.null2String(recordSet1.getString("lastname"));
/* 368 */         String str3 = Util.null2String(recordSet1.getString("sex"));
/* 369 */         String str4 = Util.null2String(recordSet1.getString("birthday"));
/* 370 */         String str5 = Util.null2String(recordSet1.getString("telephone"));
/* 371 */         String str6 = Util.null2String(recordSet1.getString("mobile"));
/* 372 */         String str7 = Util.null2String(recordSet1.getString("email"));
/* 373 */         String str8 = Util.null2String(recordSet1.getString("departmentid"));
/*     */         
/* 375 */         if (userExist(str7)) {
/* 376 */           bool = recordSet2.executeSql("update coremail_user set smsaddr = '" + paramString + "', true_name = '" + str2 + "', nick_name = '" + str1 + "', domain_name = '" + domain + "', user_status = '0', gender = '" + str3 + "', birthday = '" + str4 + "', mobile_number = '" + str6 + "', company_phone = '" + str5 + "', org_unit_id = '" + str8 + "' where user_at_domain = '" + str7 + "'");
/*     */ 
/*     */ 
/*     */           
/* 380 */           str = "2";
/*     */         } else {
/* 382 */           bool = recordSet2.executeSql("insert into coremail_user(smsaddr, user_at_domain, true_name, nick_name, domain_name, user_status, gender, birthday, mobile_number, company_phone, password, org_unit_id, cos_id) values('" + paramString + "','" + str7 + "','" + str2 + "','" + str1 + "','" + domain + "','0','" + str3 + "','" + str4 + "','" + str6 + "','" + str5 + "','" + defaultPwd + "','" + str8 + "','1')");
/*     */ 
/*     */ 
/*     */           
/* 386 */           str = "1";
/*     */         } 
/*     */         
/* 389 */         if (bool) {
/* 390 */           coremailUtil.insertCoreMailSynLog("3", str2, str, "1", "");
/* 391 */           bool = true;
/*     */         } else {
/* 393 */           coremailUtil.insertCoreMailSynLog("3", str2, str, "2", "实时同步人员失败：SQL执行报错！");
/* 394 */           bool = false;
/*     */         } 
/*     */       } 
/* 397 */     } catch (Exception exception) {
/* 398 */       newlog.error("实时同步人员到CoreMail邮件系统出现异常：", exception);
/* 399 */       bool = false;
/*     */     } 
/* 401 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean synLeaveUser(String paramString) throws Exception {
/* 410 */     boolean bool = false;
/*     */     
/* 412 */     if ("0".equals(isuse)) {
/* 413 */       return false;
/*     */     }
/* 415 */     if ("0".equals(issync)) {
/* 416 */       return false;
/*     */     }
/*     */ 
/*     */     
/*     */     try {
/* 421 */       RecordSet recordSet1 = new RecordSet();
/* 422 */       RecordSet recordSet2 = new RecordSet();
/* 423 */       recordSet1.executeSql("select email, lastname from hrmresource where id = " + paramString + " and email like '%" + domain + "%' and (accounttype = 0 or accounttype is null)");
/* 424 */       if (recordSet1.next()) {
/* 425 */         String str1 = Util.null2String(recordSet1.getString("email"));
/* 426 */         String str2 = Util.null2String(recordSet1.getString("lastname"));
/*     */         
/* 428 */         if (userExist(str1)) {
/* 429 */           bool = recordSet2.executeSql("delete from coremail_user where user_at_domain = '" + str1 + "' ");
/*     */         }
/*     */         
/* 432 */         if (bool) {
/* 433 */           coremailUtil.insertCoreMailSynLog("3", str2, "3", "1", "");
/* 434 */           bool = true;
/*     */         } else {
/* 436 */           coremailUtil.insertCoreMailSynLog("3", str2, "3", "2", "实时删除人员失败：SQL执行报错！");
/* 437 */           bool = false;
/*     */         } 
/*     */       } 
/* 440 */     } catch (Exception exception) {
/* 441 */       newlog.error("OA人员离职，删除CoreMail邮件系统账号出现异常：", exception);
/* 442 */       bool = false;
/*     */     } 
/* 444 */     return bool;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/interfaces/email/CoreMailTestAPI.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */