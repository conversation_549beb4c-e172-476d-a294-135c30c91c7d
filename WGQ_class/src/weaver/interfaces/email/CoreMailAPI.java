/*     */ package weaver.interfaces.email;
/*     */ 
/*     */ import java.net.Socket;
/*     */ import tebie.applib.api.APIContext;
/*     */ import tebie.applib.api.IClient;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.ThreadVarLanguage;
/*     */ import weaver.general.Util;
/*     */ import weaver.integration.logging.Logger;
/*     */ import weaver.integration.logging.LoggerFactory;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CoreMailAPI
/*     */   extends BaseBean
/*     */ {
/*  21 */   private static Logger newlog = LoggerFactory.getLogger(CoreMailAPI.class);
/*     */ 
/*     */   
/*  24 */   private static CoreMailAPI instance = null;
/*  25 */   private static CoreMailUtil coremailUtil = null;
/*  26 */   private static String isuse = "0";
/*  27 */   private static String issync = "0";
/*  28 */   private static String systemAddress = "";
/*  29 */   private static Integer emailPort = Integer.valueOf(6195);
/*  30 */   private static String orgId = "";
/*  31 */   private static String providerId = "";
/*  32 */   private static String domain = "";
/*     */   
/*     */   public static String getIsuse() {
/*  35 */     return isuse;
/*     */   }
/*     */   
/*     */   public static String getIssync() {
/*  39 */     return issync;
/*     */   }
/*     */   
/*     */   public static String getSystemAddress() {
/*  43 */     return systemAddress;
/*     */   }
/*     */   
/*     */   public static Integer getEmailPort() {
/*  47 */     return emailPort;
/*     */   }
/*     */   
/*     */   public static String getOrgId() {
/*  51 */     return orgId;
/*     */   }
/*     */   
/*     */   public static String getProviderId() {
/*  55 */     return providerId;
/*     */   }
/*     */   
/*     */   public static String getDomain() {
/*  59 */     return domain;
/*     */   }
/*     */   
/*     */   public CoreMailAPI() {
/*  63 */     coremailUtil = new CoreMailUtil();
/*     */   }
/*     */ 
/*     */   
/*     */   public static synchronized CoreMailAPI getInstance() {
/*  68 */     if (instance == null) {
/*  69 */       synchronized (CoreMailAPI.class) {
/*  70 */         if (instance == null) {
/*  71 */           instance = new CoreMailAPI();
/*     */         }
/*     */       } 
/*     */     }
/*  75 */     instance.getCoreMailSet();
/*  76 */     return instance;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void getCoreMailSet() {
/*  83 */     RecordSet recordSet = new RecordSet();
/*  84 */     String str = "select * from coremailsetting ";
/*  85 */     recordSet.executeSql(str);
/*  86 */     if (recordSet.next()) {
/*  87 */       isuse = Util.null2String(recordSet.getString("isuse"));
/*  88 */       issync = Util.null2String(recordSet.getString("issync"));
/*  89 */       systemAddress = Util.null2String(recordSet.getString("systemaddress"));
/*  90 */       emailPort = Integer.valueOf(Util.getIntValue(recordSet.getString("emailPort"), 6195));
/*  91 */       orgId = Util.null2String(recordSet.getString("orgid"));
/*  92 */       providerId = Util.null2String(recordSet.getString("providerid"));
/*  93 */       domain = Util.null2String(recordSet.getString("basedomain"));
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static synchronized boolean InitClient() throws Exception {
/* 103 */     boolean bool = false;
/* 104 */     Socket socket = null;
/* 105 */     IClient iClient = null;
/*     */     try {
/* 107 */       socket = new Socket(systemAddress, emailPort.intValue());
/* 108 */       iClient = APIContext.getClient(socket);
/* 109 */       if (iClient != null) {
/* 110 */         bool = true;
/*     */       }
/* 112 */       newlog.error("连接CoreMail邮箱，初始化Socket：" + bool);
/* 113 */     } catch (Exception exception) {
/* 114 */       newlog.error("连接CoreMail邮箱，初始化Socket出现异常：", exception);
/*     */     } finally {
/* 116 */       iClient.close();
/* 117 */       socket.close();
/*     */     } 
/* 119 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean userExist(String paramString) throws Exception {
/* 129 */     boolean bool = false;
/* 130 */     Socket socket = null;
/* 131 */     IClient iClient = null;
/* 132 */     APIContext aPIContext = null;
/*     */     
/*     */     try {
/* 135 */       socket = new Socket(systemAddress, emailPort.intValue());
/* 136 */       iClient = APIContext.getClient(socket);
/* 137 */       aPIContext = iClient.userExist(paramString);
/* 138 */       if (aPIContext.getRetCode() == 0) {
/* 139 */         bool = true;
/*     */       }
/* 141 */       newlog.error("检测CoreMail邮箱账号是否存在：" + paramString + "，getRetCode()=" + aPIContext.getRetCode());
/* 142 */     } catch (Exception exception) {
/* 143 */       newlog.error("检测CoreMail邮箱账号是否存在出现异常：", exception);
/*     */     } finally {
/* 145 */       iClient.close();
/* 146 */       socket.close();
/*     */     } 
/* 148 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean authenticate(String paramString1, String paramString2) throws Exception {
/* 158 */     boolean bool = false;
/* 159 */     Socket socket = null;
/* 160 */     IClient iClient = null;
/* 161 */     APIContext aPIContext = null;
/*     */     
/*     */     try {
/* 164 */       socket = new Socket(systemAddress, emailPort.intValue());
/* 165 */       iClient = APIContext.getClient(socket);
/* 166 */       aPIContext = iClient.authenticate(paramString1, paramString2);
/* 167 */       if (aPIContext.getRetCode() == 0) {
/* 168 */         bool = true;
/*     */       }
/* 170 */       newlog.error("CoreMail用户认证通过：" + paramString1 + "，getRetCode()=" + aPIContext.getRetCode());
/* 171 */     } catch (Exception exception) {
/* 172 */       newlog.error("CoreMail用户认证出现异常：", exception);
/*     */     } finally {
/* 174 */       iClient.close();
/* 175 */       socket.close();
/*     */     } 
/* 177 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static synchronized String userLogin(String paramString) throws Exception {
/* 187 */     String str = "";
/* 188 */     Socket socket = null;
/* 189 */     IClient iClient = null;
/* 190 */     APIContext aPIContext = null;
/*     */     
/*     */     try {
/* 193 */       socket = new Socket(systemAddress, emailPort.intValue());
/* 194 */       newlog.error("socket_login：" + socket);
/* 195 */       iClient = APIContext.getClient(socket);
/* 196 */       aPIContext = iClient.userLogin(paramString);
/* 197 */       if (aPIContext.getRetCode() == 0) {
/* 198 */         str = aPIContext.getResult();
/* 199 */         newlog.error("CoreMail登录成功：" + paramString + "，code=" + aPIContext
/* 200 */             .getRetCode());
/*     */       } else {
/* 202 */         newlog.error("CoreMail登录失败：" + paramString + "，code=" + aPIContext
/* 203 */             .getRetCode() + "，msg=" + aPIContext.getErrorInfo());
/*     */       } 
/* 205 */     } catch (Exception exception) {
/* 206 */       newlog.error("CoreMail登录异常：", exception);
/*     */     } finally {
/* 208 */       iClient.close();
/* 209 */       socket.close();
/*     */     } 
/* 211 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static synchronized boolean initOrgAndUser() throws Exception {
/* 220 */     Socket socket = null;
/* 221 */     IClient iClient = null;
/* 222 */     APIContext aPIContext1 = null;
/* 223 */     APIContext aPIContext2 = null;
/*     */     
/* 225 */     if ("0".equals(isuse)) {
/* 226 */       return false;
/*     */     }
/* 228 */     if ("0".equals(issync)) {
/* 229 */       return false;
/*     */     }
/*     */ 
/*     */     
/*     */     try {
/* 234 */       socket = new Socket(systemAddress, emailPort.intValue());
/* 235 */       iClient = APIContext.getClient(socket);
/*     */       
/* 237 */       newlog.error("------------------------------初始化OA组织架构和人员到CoreMail开始------------------------------");
/*     */       
/* 239 */       RecordSet recordSet1 = new RecordSet();
/* 240 */       recordSet1.executeSql("select id, subcompanyname, supsubcomid, canceled from hrmsubcompany order by id asc");
/* 241 */       while (recordSet1.next()) {
/* 242 */         String str1 = Util.null2String(recordSet1.getString("canceled"));
/* 243 */         String str2 = "com_" + Util.null2String(recordSet1.getString("id"));
/* 244 */         String str3 = Util.null2String(recordSet1.getString("subcompanyname"));
/* 245 */         String str4 = Util.null2String(recordSet1.getString("supsubcomid"));
/* 246 */         if (str4 == null || "".equals(str4)) {
/* 247 */           str4 = "0";
/*     */         }
/*     */ 
/*     */         
/* 251 */         aPIContext2 = iClient.getUnitAttrs(orgId, str2, "org_unit_name=");
/* 252 */         if (aPIContext2.getRetCode() == 0) {
/* 253 */           if (!"1".equals(str1)) {
/* 254 */             if ("0".equals(str4)) {
/* 255 */               aPIContext1 = iClient.setUnitAttrs(orgId, str2, "org_unit_name=" + str3);
/*     */             } else {
/*     */               
/* 258 */               aPIContext1 = iClient.setUnitAttrs(orgId, str2, "parent_org_unit_id=com_" + str4 + "&org_unit_name=" + str3);
/*     */             }
/*     */           
/*     */           } else {
/*     */             
/* 263 */             aPIContext1 = iClient.delUnit(orgId, str2);
/*     */           }
/*     */         
/* 266 */         } else if (!"1".equals(str1)) {
/* 267 */           if ("0".equals(str4)) {
/* 268 */             aPIContext1 = iClient.addUnit(orgId, str2, "org_unit_name=" + str3);
/*     */           } else {
/*     */             
/* 271 */             aPIContext1 = iClient.addUnit(orgId, str2, "parent_org_unit_id=com_" + str4 + "&org_unit_name=" + str3);
/*     */           } 
/*     */         } else {
/*     */           continue;
/*     */         } 
/*     */ 
/*     */ 
/*     */ 
/*     */         
/* 280 */         if (aPIContext1.getRetCode() == 0) {
/* 281 */           coremailUtil.insertCoreMailSynLog("1", str3, "4", "1", "");
/*     */           
/* 283 */           newlog.error("分部：com_" + str2 + "(" + str3 + ")，" + 
/* 284 */               SystemEnv.getHtmlLabelName(10003698, ThreadVarLanguage.getLang()) + "com_" + str4 + "，" + SystemEnv.getHtmlLabelName(387135, ThreadVarLanguage.getLang()) + ""); continue;
/*     */         } 
/* 286 */         coremailUtil.insertCoreMailSynLog("1", str3, "4", "2", aPIContext1.getErrorInfo());
/*     */         
/* 288 */         newlog.error("分部：com_" + str2 + "(" + str3 + ")，" + 
/* 289 */             SystemEnv.getHtmlLabelName(10003698, ThreadVarLanguage.getLang()) + "com_" + str4 + "，" + SystemEnv.getHtmlLabelName(10003699, ThreadVarLanguage.getLang()) + "code=" + aPIContext1
/* 290 */             .getRetCode() + "，msg=" + aPIContext1.getErrorInfo());
/*     */       } 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 296 */       RecordSet recordSet2 = new RecordSet();
/* 297 */       recordSet2.executeSql("select id, departmentname, subcompanyid1, supdepid, canceled from hrmdepartment order by id asc");
/* 298 */       while (recordSet2.next()) {
/* 299 */         String str1 = Util.null2String(recordSet2.getString("canceled"));
/* 300 */         String str2 = Util.null2String(recordSet2.getString("id"));
/* 301 */         String str3 = Util.null2String(recordSet2.getString("departmentname"));
/* 302 */         String str4 = Util.null2String(recordSet2.getString("subcompanyid1"));
/* 303 */         String str5 = Util.null2String(recordSet2.getString("supdepid"));
/* 304 */         if (str5 == null || "".equals(str5)) {
/* 305 */           str5 = "0";
/*     */         }
/*     */ 
/*     */         
/* 309 */         aPIContext2 = iClient.getUnitAttrs(orgId, str2, "org_unit_name=");
/* 310 */         if (aPIContext2.getRetCode() == 0) {
/* 311 */           if (!"1".equals(str1)) {
/* 312 */             if ("0".equals(str5)) {
/* 313 */               aPIContext1 = iClient.setUnitAttrs(orgId, str2, "parent_org_unit_id=com_" + str4 + "&org_unit_name=" + str3);
/*     */             }
/*     */             else {
/*     */               
/* 317 */               aPIContext1 = iClient.setUnitAttrs(orgId, str2, "parent_org_unit_id=" + str5 + "&org_unit_name=" + str3);
/*     */             }
/*     */           
/*     */           } else {
/*     */             
/* 322 */             aPIContext1 = iClient.delUnit(orgId, str2);
/*     */           }
/*     */         
/* 325 */         } else if (!"1".equals(str1)) {
/* 326 */           if ("0".equals(str5)) {
/* 327 */             aPIContext1 = iClient.addUnit(orgId, str2, "parent_org_unit_id=com_" + str4 + "&org_unit_name=" + str3);
/*     */           }
/*     */           else {
/*     */             
/* 331 */             aPIContext1 = iClient.addUnit(orgId, str2, "parent_org_unit_id=" + str5 + "&org_unit_name=" + str3);
/*     */           } 
/*     */         } else {
/*     */           continue;
/*     */         } 
/*     */ 
/*     */ 
/*     */ 
/*     */         
/* 340 */         if (aPIContext1.getRetCode() == 0) {
/* 341 */           coremailUtil.insertCoreMailSynLog("2", str3, "4", "1", "");
/*     */           
/* 343 */           newlog.error("部门：" + str2 + "(" + str3 + ")，" + 
/* 344 */               SystemEnv.getHtmlLabelName(10003700, ThreadVarLanguage.getLang()) + "com_" + str4 + "，" + 
/* 345 */               SystemEnv.getHtmlLabelName(********, ThreadVarLanguage.getLang()) + "" + str5 + "，" + SystemEnv.getHtmlLabelName(387135, ThreadVarLanguage.getLang()) + ""); continue;
/*     */         } 
/* 347 */         coremailUtil.insertCoreMailSynLog("2", str3, "4", "2", aPIContext1.getErrorInfo());
/*     */         
/* 349 */         newlog.error("部门：" + str2 + "(" + str3 + ")，" + 
/* 350 */             SystemEnv.getHtmlLabelName(10003700, ThreadVarLanguage.getLang()) + "com_" + str4 + "，" + SystemEnv.getHtmlLabelName(********, ThreadVarLanguage.getLang()) + "" + str5 + "，同步失败，code=" + aPIContext1
/* 351 */             .getRetCode() + "，msg=" + aPIContext1.getErrorInfo());
/*     */       } 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 357 */       RecordSet recordSet3 = new RecordSet();
/* 358 */       recordSet3.executeSql("select id, loginid, lastname, sex, birthday, telephone, mobile, email, departmentid, status,password from hrmresource where email like '%" + domain + "%' and (accounttype = 0 or accounttype is null)");
/*     */       
/* 360 */       while (recordSet3.next()) {
/*     */         try {
/* 362 */           String str1 = Util.null2String(recordSet3.getString("id"));
/* 363 */           String str2 = Util.null2String(recordSet3.getString("loginid"));
/* 364 */           String str3 = Util.null2String(recordSet3.getString("lastname"));
/* 365 */           String str4 = Util.null2String(recordSet3.getString("sex"));
/* 366 */           String str5 = Util.null2String(recordSet3.getString("birthday"));
/* 367 */           String str6 = Util.null2String(recordSet3.getString("telephone"));
/* 368 */           String str7 = Util.null2String(recordSet3.getString("mobile"));
/* 369 */           String str8 = Util.null2String(recordSet3.getString("email"));
/* 370 */           String str9 = Util.null2String(recordSet3.getString("departmentid"));
/* 371 */           String str10 = Util.null2String(recordSet3.getString("status"));
/* 372 */           String str11 = Util.null2String(recordSet3.getString("password")).toLowerCase();
/* 373 */           str11 = "{enc2}" + str11 + "&encrypted=1";
/* 374 */           if (userExist(str8)) {
/* 375 */             if (str10.equals("0") || str10.equals("1") || str10.equals("2") || str10.equals("3")) {
/* 376 */               aPIContext1 = iClient.changeAttrs(str8, "password=" + str11 + "&nick_name=" + str2 + "&true_name=" + str3 + "&gender=" + str4 + "&birthday=" + str5 + "&company_phone=" + str6 + "&mobile_number=" + str7 + "&org_unit_id=" + str9 + "&smsaddr=" + str1);
/*     */ 
/*     */ 
/*     */             
/*     */             }
/*     */             else {
/*     */ 
/*     */ 
/*     */               
/* 385 */               aPIContext1 = iClient.deleteUser(str8);
/*     */             }
/*     */           
/* 388 */           } else if (str10.equals("0") || str10.equals("1") || str10.equals("2") || str10.equals("3")) {
/* 389 */             if (str8.indexOf("@") > -1) {
/* 390 */               aPIContext1 = iClient.createUser(providerId, orgId, str8.substring(0, str8.indexOf("@")), "domain_name=" + domain + "&user_status=0&cos_id=1&org_unit_id=" + str9);
/*     */ 
/*     */               
/* 393 */               aPIContext1 = iClient.changeAttrs(str8, "password=" + str11 + "&nick_name=" + str2 + "&true_name=" + str3 + "&gender=" + str4 + "&birthday=" + str5 + "&company_phone=" + str6 + "&mobile_number=" + str7 + "&smsaddr=" + str1);
/*     */ 
/*     */             
/*     */             }
/*     */             else {
/*     */ 
/*     */ 
/*     */               
/* 401 */               newlog.error("人员：ID=" + str1 + "，loginid=" + str2 + "，email=" + str8 + "，" + 
/* 402 */                   SystemEnv.getHtmlLabelName(10003702, ThreadVarLanguage.getLang()) + "" + str9 + "，" + SystemEnv.getHtmlLabelName(10003703, ThreadVarLanguage.getLang()) + "");
/*     */               
/*     */               continue;
/*     */             } 
/*     */           } else {
/*     */             continue;
/*     */           } 
/*     */           
/* 410 */           if (aPIContext1.getRetCode() == 0) {
/* 411 */             coremailUtil.insertCoreMailSynLog("3", str3, "4", "1", "");
/*     */             
/* 413 */             newlog.error("人员：ID=" + str1 + "，loginid=" + str2 + "，email=" + str8 + "，" + 
/* 414 */                 SystemEnv.getHtmlLabelName(10003702, ThreadVarLanguage.getLang()) + "" + str9 + "，" + SystemEnv.getHtmlLabelName(387135, ThreadVarLanguage.getLang()) + ""); continue;
/*     */           } 
/* 416 */           coremailUtil.insertCoreMailSynLog("3", str3, "4", "2", aPIContext1.getErrorInfo());
/*     */           
/* 418 */           newlog.error("人员：ID=" + str1 + "，loginid=" + str2 + "，email=" + str8 + "，" + 
/* 419 */               SystemEnv.getHtmlLabelName(10003702, ThreadVarLanguage.getLang()) + "" + str9 + "，" + SystemEnv.getHtmlLabelName(10003699, ThreadVarLanguage.getLang()) + "code=" + aPIContext1
/* 420 */               .getRetCode() + "，msg=" + aPIContext1.getErrorInfo());
/*     */         }
/* 422 */         catch (Exception exception) {
/* 423 */           newlog.error("coremail初始化人员时异常！", exception);
/*     */         } 
/*     */       } 
/*     */       
/* 427 */       newlog.error("------------------------------初始化OA组织架构和人员到CoreMail结束------------------------------");
/* 428 */       return true;
/* 429 */     } catch (Exception exception) {
/* 430 */       newlog.error("OA组织架构和人员初始化到CoreMail，出现异常：", exception);
/* 431 */       return false;
/*     */     } finally {
/* 433 */       if (iClient != null) {
/* 434 */         iClient.close();
/*     */       }
/* 436 */       if (socket != null) {
/* 437 */         socket.close();
/*     */       }
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean synOrg(String paramString1, String paramString2, String paramString3, String paramString4) throws Exception {
/* 451 */     boolean bool = true;
/* 452 */     Socket socket = null;
/* 453 */     IClient iClient = null;
/* 454 */     APIContext aPIContext1 = null;
/* 455 */     APIContext aPIContext2 = null;
/*     */     
/* 457 */     if ("0".equals(isuse)) {
/* 458 */       return false;
/*     */     }
/* 460 */     if ("0".equals(issync)) {
/* 461 */       return false;
/*     */     }
/*     */ 
/*     */     
/* 465 */     String str1 = (paramString1.indexOf("com_") > -1) ? "1" : "2";
/* 466 */     String str2 = "";
/*     */     
/*     */     try {
/* 469 */       socket = new Socket(systemAddress, emailPort.intValue());
/* 470 */       iClient = APIContext.getClient(socket);
/*     */ 
/*     */       
/* 473 */       aPIContext2 = iClient.getUnitAttrs(orgId, paramString1, "org_unit_name=");
/* 474 */       if (aPIContext2.getRetCode() == 0) {
/* 475 */         if (!"1".equals(paramString4)) {
/* 476 */           aPIContext1 = iClient.setUnitAttrs(orgId, paramString1, paramString3);
/* 477 */           str2 = "2";
/*     */         } else {
/* 479 */           aPIContext1 = iClient.delUnit(orgId, paramString1);
/* 480 */           str2 = "3";
/*     */         }
/*     */       
/* 483 */       } else if (!"1".equals(paramString4)) {
/* 484 */         aPIContext1 = iClient.addUnit(orgId, paramString1, paramString3);
/* 485 */         str2 = "1";
/*     */       } 
/*     */ 
/*     */       
/* 489 */       if (aPIContext1.getRetCode() == 0) {
/* 490 */         coremailUtil.insertCoreMailSynLog(str1, paramString2, str2, "1", "");
/*     */         
/* 492 */         newlog.error("实时同步分部或部门到CoreMail邮件系统成功，ID=" + paramString1 + "，更新信息：" + paramString3 + "，code=" + aPIContext1
/* 493 */             .getRetCode() + "，msg=" + aPIContext1.getErrorInfo());
/* 494 */         bool = true;
/*     */       } else {
/* 496 */         coremailUtil.insertCoreMailSynLog(str1, paramString2, str2, "2", aPIContext1.getErrorInfo());
/*     */         
/* 498 */         newlog.error("实时同步分部或部门到CoreMail邮件系统失败，ID=" + paramString1 + "，更新信息：" + paramString3 + "，code=" + aPIContext1
/* 499 */             .getRetCode() + "，msg=" + aPIContext1.getErrorInfo());
/* 500 */         bool = false;
/*     */       } 
/* 502 */     } catch (Exception exception) {
/* 503 */       newlog.error("实时同步分部或部门到CoreMail邮件系统出现异常：", exception);
/* 504 */       bool = false;
/*     */     } finally {
/* 506 */       if (iClient != null) {
/* 507 */         iClient.close();
/*     */       }
/* 509 */       if (socket != null) {
/* 510 */         socket.close();
/*     */       }
/*     */     } 
/* 513 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean synUser(String paramString) throws Exception {
/* 523 */     boolean bool = false;
/* 524 */     Socket socket = null;
/* 525 */     IClient iClient = null;
/* 526 */     APIContext aPIContext = null;
/*     */     
/* 528 */     if ("0".equals(isuse)) {
/* 529 */       return false;
/*     */     }
/* 531 */     if ("0".equals(issync)) {
/* 532 */       return false;
/*     */     }
/*     */ 
/*     */     
/* 536 */     String str = "";
/*     */     
/*     */     try {
/* 539 */       socket = new Socket(systemAddress, emailPort.intValue());
/* 540 */       iClient = APIContext.getClient(socket);
/*     */       
/* 542 */       RecordSet recordSet = new RecordSet();
/* 543 */       recordSet.executeSql("select id, loginid, lastname, sex, birthday, telephone, mobile, email, departmentid,password from hrmresource where id = " + paramString + " and email like '%" + domain + "%' and (accounttype = 0 or accounttype is null)");
/*     */       
/* 545 */       if (recordSet.next()) {
/* 546 */         String str1 = Util.null2String(recordSet.getString("loginid"));
/* 547 */         String str2 = Util.null2String(recordSet.getString("lastname"));
/* 548 */         String str3 = Util.null2String(recordSet.getString("sex"));
/* 549 */         String str4 = Util.null2String(recordSet.getString("birthday"));
/* 550 */         String str5 = Util.null2String(recordSet.getString("telephone"));
/* 551 */         String str6 = Util.null2String(recordSet.getString("mobile"));
/* 552 */         String str7 = Util.null2String(recordSet.getString("email"));
/* 553 */         String str8 = Util.null2String(recordSet.getString("departmentid"));
/* 554 */         String str9 = Util.null2String(recordSet.getString("password")).toLowerCase();
/* 555 */         str9 = "{enc2}" + str9 + "&encrypted=1";
/*     */         
/* 557 */         if (userExist(str7)) {
/* 558 */           aPIContext = iClient.changeAttrs(str7, "password=" + str9 + "&nick_name=" + str1 + "&true_name=" + str2 + "&gender=" + str3 + "&birthday=" + str4 + "&company_phone=" + str5 + "&mobile_number=" + str6 + "&org_unit_id=" + str8 + "&smsaddr=" + paramString);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */           
/* 567 */           str = "2";
/*     */         } else {
/* 569 */           aPIContext = iClient.createUser(providerId, orgId, str7.substring(0, str7.indexOf("@")), "domain_name=" + domain + "&user_status=0&cos_id=1&org_unit_id=" + str8);
/*     */ 
/*     */           
/* 572 */           aPIContext = iClient.changeAttrs(str7, "password=" + str9 + "&nick_name=" + str1 + "&true_name=" + str2 + "&gender=" + str3 + "&birthday=" + str4 + "&company_phone=" + str5 + "&mobile_number=" + str6 + "&smsaddr=" + paramString);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */           
/* 580 */           str = "1";
/*     */         } 
/*     */         
/* 583 */         if (aPIContext.getRetCode() == 0) {
/* 584 */           coremailUtil.insertCoreMailSynLog("3", str2, str, "1", "");
/*     */           
/* 586 */           newlog.error("实时同步人员到CoreMail邮件系统成功：ID=" + paramString + "，email=" + str7 + "，" + 
/* 587 */               SystemEnv.getHtmlLabelName(10003702, ThreadVarLanguage.getLang()) + "" + str8 + "，code=" + aPIContext.getRetCode());
/* 588 */           bool = true;
/*     */         } else {
/* 590 */           coremailUtil.insertCoreMailSynLog("3", str2, str, "2", aPIContext.getErrorInfo());
/*     */           
/* 592 */           newlog.error("实时同步人员到CoreMail邮件系统失败：ID=" + paramString + "，email=" + str7 + "，所属部门：" + str8 + "，code=" + aPIContext
/* 593 */               .getRetCode() + "，msg=" + aPIContext.getErrorInfo());
/* 594 */           bool = false;
/*     */         } 
/*     */       } 
/* 597 */     } catch (Exception exception) {
/* 598 */       newlog.error("实时同步人员到CoreMail邮件系统出现异常：", exception);
/* 599 */       bool = false;
/*     */     } finally {
/* 601 */       if (iClient != null) {
/* 602 */         iClient.close();
/*     */       }
/* 604 */       if (socket != null) {
/* 605 */         socket.close();
/*     */       }
/*     */     } 
/* 608 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean synLeaveUser(String paramString) throws Exception {
/* 617 */     boolean bool = false;
/* 618 */     Socket socket = null;
/* 619 */     IClient iClient = null;
/* 620 */     APIContext aPIContext = null;
/*     */     
/* 622 */     if ("0".equals(isuse)) {
/* 623 */       return false;
/*     */     }
/* 625 */     if ("0".equals(issync)) {
/* 626 */       return false;
/*     */     }
/*     */ 
/*     */     
/*     */     try {
/* 631 */       socket = new Socket(systemAddress, emailPort.intValue());
/* 632 */       iClient = APIContext.getClient(socket);
/*     */       
/* 634 */       RecordSet recordSet = new RecordSet();
/* 635 */       recordSet.executeSql("select email, lastname from hrmresource where id = " + paramString + " and email like '%" + domain + "%' and (accounttype = 0 or accounttype is null)");
/* 636 */       if (recordSet.next()) {
/* 637 */         String str1 = Util.null2String(recordSet.getString("email"));
/* 638 */         String str2 = Util.null2String(recordSet.getString("lastname"));
/*     */         
/* 640 */         if (userExist(str1)) {
/* 641 */           aPIContext = iClient.deleteUser(str1);
/*     */         }
/*     */         
/* 644 */         if (aPIContext.getRetCode() == 0) {
/* 645 */           coremailUtil.insertCoreMailSynLog("3", str2, "3", "1", "");
/*     */           
/* 647 */           newlog.error("OA人员离职，删除CoreMail邮件系统账号成功，ID=" + paramString + "，email=" + str1 + "，code=" + aPIContext
/* 648 */               .getRetCode());
/* 649 */           bool = true;
/*     */         } else {
/* 651 */           coremailUtil.insertCoreMailSynLog("3", str2, "3", "2", aPIContext.getErrorInfo());
/*     */           
/* 653 */           newlog.error("OA人员离职，删除CoreMail邮件系统账号失败，ID=" + paramString + "，email=" + str1 + "，code=" + aPIContext
/* 654 */               .getRetCode() + "，msg=" + aPIContext.getErrorInfo());
/* 655 */           bool = false;
/*     */         } 
/*     */       } 
/* 658 */     } catch (Exception exception) {
/* 659 */       newlog.error("OA人员离职，删除CoreMail邮件系统账号出现异常：", exception);
/* 660 */       bool = false;
/*     */     } finally {
/* 662 */       if (iClient != null) {
/* 663 */         iClient.close();
/*     */       }
/* 665 */       if (socket != null) {
/* 666 */         socket.close();
/*     */       }
/*     */     } 
/* 669 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean changeEmail(String paramString) throws Exception {
/* 679 */     boolean bool = false;
/* 680 */     Socket socket = null;
/* 681 */     IClient iClient = null;
/* 682 */     APIContext aPIContext = null;
/*     */     
/* 684 */     if ("0".equals(isuse)) {
/* 685 */       return false;
/*     */     }
/* 687 */     if ("0".equals(issync)) {
/* 688 */       return false;
/*     */     }
/*     */ 
/*     */     
/*     */     try {
/* 693 */       socket = new Socket(systemAddress, emailPort.intValue());
/* 694 */       iClient = APIContext.getClient(socket);
/*     */       
/* 696 */       RecordSet recordSet = new RecordSet();
/* 697 */       recordSet.executeSql("select email, lastname from hrmresource where id = " + paramString + " and email like '%" + domain + "%' ");
/* 698 */       if (recordSet.next()) {
/* 699 */         String str1 = Util.null2String(recordSet.getString("email"));
/* 700 */         String str2 = Util.null2String(recordSet.getString("lastname"));
/*     */         
/* 702 */         if (userExist(str1)) {
/* 703 */           aPIContext = iClient.deleteUser(str1);
/*     */         }
/*     */         
/* 706 */         if (aPIContext.getRetCode() == 0) {
/* 707 */           coremailUtil.insertCoreMailSynLog("3", str2, "3", "1", "");
/*     */           
/* 709 */           newlog.error("OA修改人员Email，删除原CoreMail邮件系统账号成功，ID=" + paramString + "，email=" + str1 + "，code=" + aPIContext
/* 710 */               .getRetCode());
/* 711 */           bool = true;
/*     */         } else {
/* 713 */           coremailUtil.insertCoreMailSynLog("3", str2, "3", "2", aPIContext.getErrorInfo());
/*     */           
/* 715 */           newlog.error("OA修改人员Email，删除原CoreMail邮件系统账号失败，ID=" + paramString + "，email=" + str1 + "，code=" + aPIContext
/* 716 */               .getRetCode() + "，msg=" + aPIContext.getErrorInfo());
/* 717 */           bool = false;
/*     */         } 
/*     */       } 
/* 720 */     } catch (Exception exception) {
/* 721 */       newlog.error("OA修改人员Email，删除原CoreMail邮件系统账号出现异常：", exception);
/* 722 */       bool = false;
/*     */     } finally {
/* 724 */       if (iClient != null) {
/* 725 */         iClient.close();
/*     */       }
/* 727 */       if (socket != null) {
/* 728 */         socket.close();
/*     */       }
/*     */     } 
/* 731 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean changePassword(String paramString1, String paramString2) throws Exception {
/* 741 */     boolean bool = false;
/* 742 */     Socket socket = null;
/* 743 */     IClient iClient = null;
/* 744 */     APIContext aPIContext = null;
/*     */     
/* 746 */     if ("0".equals(isuse)) {
/* 747 */       return false;
/*     */     }
/* 749 */     if ("0".equals(issync)) {
/* 750 */       return false;
/*     */     }
/*     */ 
/*     */     
/*     */     try {
/* 755 */       socket = new Socket(systemAddress, emailPort.intValue());
/* 756 */       iClient = APIContext.getClient(socket);
/* 757 */       if (userExist(paramString1)) {
/* 758 */         aPIContext = iClient.changeAttrs(paramString1, "password=" + paramString2);
/*     */       }
/*     */       
/* 761 */       if (aPIContext.getRetCode() == 0) {
/* 762 */         newlog.error("修改CoreMail邮箱密码成功，code=" + aPIContext.getRetCode());
/* 763 */         bool = true;
/*     */       } 
/* 765 */     } catch (Exception exception) {
/* 766 */       newlog.error("修改CoreMail邮箱密码出现异常：", exception);
/* 767 */       bool = false;
/*     */     } finally {
/* 769 */       if (iClient != null) {
/* 770 */         iClient.close();
/*     */       }
/* 772 */       if (socket != null) {
/* 773 */         socket.close();
/*     */       }
/*     */     } 
/* 776 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getAttrs(String paramString) throws Exception {
/* 786 */     String str = "";
/* 787 */     Socket socket = null;
/* 788 */     IClient iClient = null;
/* 789 */     APIContext aPIContext = null;
/*     */     
/*     */     try {
/* 792 */       socket = new Socket(systemAddress, emailPort.intValue());
/* 793 */       iClient = APIContext.getClient(socket);
/*     */       
/* 795 */       aPIContext = iClient.getAttrs(paramString, "mbox_newmsgcnt");
/* 796 */       if (aPIContext.getRetCode() == 0) {
/* 797 */         return aPIContext.getResult();
/*     */       }
/*     */     }
/* 800 */     catch (Exception exception) {
/* 801 */       newlog.error("获取CoreMail未读邮件明细(XML格式)出现异常：", exception);
/* 802 */       str = "";
/*     */     } finally {
/* 804 */       if (iClient != null) {
/* 805 */         iClient.close();
/*     */       }
/* 807 */       if (socket != null) {
/* 808 */         socket.close();
/*     */       }
/*     */     } 
/* 811 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getNewMailList(String paramString1, String paramString2) throws Exception {
/* 821 */     String str = "";
/* 822 */     Socket socket = null;
/* 823 */     IClient iClient = null;
/* 824 */     APIContext aPIContext = null;
/*     */     
/*     */     try {
/* 827 */       socket = new Socket(systemAddress, emailPort.intValue());
/* 828 */       iClient = APIContext.getClient(socket);
/*     */       
/* 830 */       String str1 = "format=xml&limit=" + paramString2;
/* 831 */       aPIContext = iClient.getNewMailInfos(paramString1, str1);
/* 832 */       if (aPIContext.getRetCode() == 0) {
/* 833 */         return aPIContext.getResult();
/*     */       }
/*     */     }
/* 836 */     catch (Exception exception) {
/* 837 */       newlog.error("获取CoreMail未读邮件明细(XML格式)出现异常：", exception);
/* 838 */       str = "";
/*     */     } finally {
/* 840 */       if (iClient != null) {
/* 841 */         iClient.close();
/*     */       }
/* 843 */       if (socket != null) {
/* 844 */         socket.close();
/*     */       }
/*     */     } 
/* 847 */     return str;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/interfaces/email/CoreMailAPI.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */