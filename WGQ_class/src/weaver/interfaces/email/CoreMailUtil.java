/*    */ package weaver.interfaces.email;
/*    */ 
/*    */ import weaver.conn.ConnStatement;
/*    */ import weaver.general.TimeUtil;
/*    */ import weaver.general.Util;
/*    */ import weaver.integration.logging.Logger;
/*    */ import weaver.integration.logging.LoggerFactory;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class CoreMailUtil
/*    */ {
/* 17 */   private static Logger newlog = LoggerFactory.getLogger(CoreMailUtil.class);
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getDataTypeName(String paramString1, String paramString2) {
/* 24 */     String str = "";
/* 25 */     if ("1".equals(paramString1)) {
/* 26 */       str = SystemEnv.getHtmlLabelName(141, Util.getIntValue(paramString2));
/* 27 */     } else if ("2".equals(paramString1)) {
/* 28 */       str = SystemEnv.getHtmlLabelName(124, Util.getIntValue(paramString2));
/* 29 */     } else if ("3".equals(paramString1)) {
/* 30 */       str = SystemEnv.getHtmlLabelName(1867, Util.getIntValue(paramString2));
/*    */     } 
/* 32 */     return str;
/*    */   }
/*    */   
/*    */   public String getOperateTypeName(String paramString1, String paramString2) {
/* 36 */     String str = "";
/* 37 */     if ("1".equals(paramString1)) {
/* 38 */       str = SystemEnv.getHtmlLabelName(1421, Util.getIntValue(paramString2));
/* 39 */     } else if ("2".equals(paramString1)) {
/* 40 */       str = SystemEnv.getHtmlLabelName(103, Util.getIntValue(paramString2));
/* 41 */     } else if ("3".equals(paramString1)) {
/* 42 */       str = SystemEnv.getHtmlLabelName(91, Util.getIntValue(paramString2));
/* 43 */     } else if ("4".equals(paramString1)) {
/* 44 */       str = SystemEnv.getHtmlLabelName(20873, Util.getIntValue(paramString2));
/*    */     } 
/* 46 */     return str;
/*    */   }
/*    */   
/*    */   public String getOperateResultName(String paramString1, String paramString2) {
/* 50 */     String str = "";
/* 51 */     if ("1".equals(paramString1)) {
/* 52 */       str = SystemEnv.getHtmlLabelName(15242, Util.getIntValue(paramString2));
/* 53 */     } else if ("2".equals(paramString1)) {
/* 54 */       str = SystemEnv.getHtmlLabelName(498, Util.getIntValue(paramString2));
/*    */     } 
/* 56 */     return str;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public void insertCoreMailSynLog(String paramString1, String paramString2, String paramString3, String paramString4, String paramString5) {
/* 68 */     ConnStatement connStatement = null;
/*    */     try {
/* 70 */       String str1 = TimeUtil.getCurrentDateString();
/* 71 */       String str2 = TimeUtil.getOnlyCurrentTimeString();
/*    */       
/* 73 */       connStatement = new ConnStatement();
/* 74 */       String str3 = "insert into coremaillog(datatype, operatedata, operatetype, operateresult, operateremark, logdate, logtime) values(?, ?, ?, ?, ?, ?, ?)";
/* 75 */       connStatement.setStatementSql(str3);
/*    */       
/* 77 */       connStatement.setInt(1, Util.getIntValue(paramString1));
/* 78 */       connStatement.setString(2, paramString2);
/* 79 */       connStatement.setInt(3, Util.getIntValue(paramString3));
/* 80 */       connStatement.setInt(4, Util.getIntValue(paramString4));
/* 81 */       connStatement.setString(5, paramString5.substring(0, (paramString5.length() >= 1000) ? 1000 : paramString5.length()));
/* 82 */       connStatement.setString(6, str1);
/* 83 */       connStatement.setString(7, str2);
/*    */       
/* 85 */       connStatement.executeUpdate();
/* 86 */     } catch (Exception exception) {
/* 87 */       newlog.error("插入CoreMail同步日志出现异常：", exception);
/*    */     } finally {
/* 89 */       if (connStatement != null)
/* 90 */         connStatement.close(); 
/*    */     } 
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/interfaces/email/CoreMailUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */