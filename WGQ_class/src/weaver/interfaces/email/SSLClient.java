/*    */ package weaver.interfaces.email;
/*    */ 
/*    */ import java.security.cert.CertificateException;
/*    */ import java.security.cert.X509Certificate;
/*    */ import javax.net.ssl.SSLContext;
/*    */ import javax.net.ssl.TrustManager;
/*    */ import javax.net.ssl.X509TrustManager;
/*    */ import org.apache.http.conn.ClientConnectionManager;
/*    */ import org.apache.http.conn.scheme.Scheme;
/*    */ import org.apache.http.conn.scheme.SchemeRegistry;
/*    */ import org.apache.http.conn.scheme.SchemeSocketFactory;
/*    */ import org.apache.http.conn.ssl.SSLSocketFactory;
/*    */ import org.apache.http.impl.client.DefaultHttpClient;
/*    */ 
/*    */ public class SSLClient
/*    */   extends DefaultHttpClient
/*    */ {
/*    */   public SSLClient() throws Exception {
/* 19 */     SSLContext sSLContext = SSLContext.getInstance("TLS");
/* 20 */     X509TrustManager x509TrustManager = new X509TrustManager()
/*    */       {
/*    */         public void checkClientTrusted(X509Certificate[] param1ArrayOfX509Certificate, String param1String) throws CertificateException {}
/*    */ 
/*    */ 
/*    */         
/*    */         public void checkServerTrusted(X509Certificate[] param1ArrayOfX509Certificate, String param1String) throws CertificateException {}
/*    */ 
/*    */ 
/*    */         
/*    */         public X509Certificate[] getAcceptedIssuers() {
/* 31 */           return null;
/*    */         }
/*    */       };
/* 34 */     sSLContext.init(null, new TrustManager[] { x509TrustManager }, null);
/* 35 */     SSLSocketFactory sSLSocketFactory = new SSLSocketFactory(sSLContext, SSLSocketFactory.ALLOW_ALL_HOSTNAME_VERIFIER);
/* 36 */     ClientConnectionManager clientConnectionManager = getConnectionManager();
/* 37 */     SchemeRegistry schemeRegistry = clientConnectionManager.getSchemeRegistry();
/* 38 */     schemeRegistry.register(new Scheme("https", 443, (SchemeSocketFactory)sSLSocketFactory));
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/interfaces/email/SSLClient.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */