/*     */ package weaver.interfaces.email;
/*     */ 
/*     */ import net.sf.json.JSONObject;
/*     */ import org.apache.http.HttpEntity;
/*     */ import org.apache.http.HttpResponse;
/*     */ import org.apache.http.client.methods.HttpGet;
/*     */ import org.apache.http.client.methods.HttpUriRequest;
/*     */ import org.apache.http.util.EntityUtils;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.integration.logging.Logger;
/*     */ import weaver.integration.logging.LoggerFactory;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class EntranceQQEmail
/*     */   extends BaseBean
/*     */ {
/*  30 */   private Logger log = LoggerFactory.getLogger(EntranceQQEmail.class);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getSingleUrl(String paramString1, String paramString2) {
/*  38 */     RecordSet recordSet = new RecordSet();
/*  39 */     String str1 = "";
/*  40 */     String str2 = "";
/*  41 */     recordSet.executeSql("select client_id,client_secret from outter_sys where sysid='" + paramString2 + "'");
/*  42 */     if (recordSet.next()) {
/*  43 */       str1 = Util.null2String(recordSet.getString("client_id"));
/*  44 */       str2 = Util.null2String(recordSet.getString("client_secret"));
/*     */     } 
/*  46 */     String str3 = getToken(str1, str2);
/*  47 */     String str4 = getAuthkey(str3, paramString1);
/*  48 */     if ("".equals(str4) || str4 == null) {
/*  49 */       str4 = "https://exmail.qq.com/cgi-bin/login";
/*     */     }
/*  51 */     this.log.info("url:" + str4);
/*  52 */     return str4;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getAuthkey(String paramString1, String paramString2) {
/*  63 */     HttpGet httpGet = new HttpGet("https://api.exmail.qq.com/cgi-bin/service/get_login_url?access_token=" + paramString1 + "&userid=" + paramString2);
/*     */     
/*  65 */     String str = "";
/*     */     try {
/*  67 */       SSLClient sSLClient = new SSLClient();
/*  68 */       HttpResponse httpResponse = sSLClient.execute((HttpUriRequest)httpGet);
/*  69 */       HttpEntity httpEntity = httpResponse.getEntity();
/*  70 */       if (httpEntity != null) {
/*  71 */         str = EntityUtils.toString(httpEntity, "UTF-8");
/*  72 */         this.log.info("getAuthkey:" + str);
/*     */       }
/*     */     
/*  75 */     } catch (Exception exception) {
/*  76 */       this.log.error(exception);
/*  77 */       exception.printStackTrace();
/*     */     } 
/*  79 */     if (!"".equals(str)) {
/*  80 */       JSONObject jSONObject = JSONObject.fromObject(str);
/*  81 */       str = Util.null2String((String)jSONObject.get("login_url"));
/*     */     } 
/*  83 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getToken(String paramString1, String paramString2) {
/*  93 */     HttpGet httpGet = new HttpGet("https://api.exmail.qq.com/cgi-bin/gettoken?corpid=" + paramString1 + "&corpsecret=" + paramString2);
/*  94 */     String str = "";
/*     */     try {
/*  96 */       SSLClient sSLClient = new SSLClient();
/*  97 */       HttpResponse httpResponse = sSLClient.execute((HttpUriRequest)httpGet);
/*  98 */       HttpEntity httpEntity = httpResponse.getEntity();
/*  99 */       if (httpEntity != null) {
/* 100 */         str = EntityUtils.toString(httpEntity, "UTF-8");
/* 101 */         this.log.error(str);
/*     */       }
/*     */     
/*     */     }
/* 105 */     catch (Exception exception) {
/* 106 */       this.log.error(exception);
/* 107 */       exception.printStackTrace();
/*     */     } 
/*     */     
/* 110 */     if (!"".equals(str)) {
/* 111 */       JSONObject jSONObject = JSONObject.fromObject(str);
/* 112 */       str = Util.null2String((String)jSONObject.get("access_token"));
/*     */     } 
/*     */     
/* 115 */     return str;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/interfaces/email/EntranceQQEmail.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */