/*    */ package weaver.interfaces.email;
/*    */ 
/*    */ import com.alibaba.alimei.sso.api.AlimailSSOAPIClient;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.BaseBean;
/*    */ import weaver.general.Util;
/*    */ import weaver.integration.logging.Logger;
/*    */ import weaver.integration.logging.LoggerFactory;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class EntranceALYEmail
/*    */   extends BaseBean
/*    */ {
/* 29 */   private Logger log = LoggerFactory.getLogger(EntranceALYEmail.class);
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getSingleUrl(String paramString1, String paramString2) {
/* 37 */     RecordSet recordSet = new RecordSet();
/* 38 */     String str1 = "";
/* 39 */     String str2 = "";
/* 40 */     recordSet.executeSql("select alssokey,alssosecret from outter_sys where sysid='" + paramString2 + "'");
/* 41 */     if (recordSet.next()) {
/* 42 */       str1 = Util.null2String(recordSet.getString("alssokey"));
/* 43 */       str2 = Util.null2String(recordSet.getString("alssosecret"));
/*    */     } 
/* 45 */     AlimailSSOAPIClient alimailSSOAPIClient = new AlimailSSOAPIClient();
/* 46 */     alimailSSOAPIClient.setAppCode(str1);
/* 47 */     alimailSSOAPIClient.setAppSecret(str2);
/*    */ 
/*    */ 
/*    */     
/* 51 */     alimailSSOAPIClient.setDebugEnable(false);
/*    */ 
/*    */ 
/*    */ 
/*    */     
/* 56 */     String str3 = Util.null2String(alimailSSOAPIClient.getLoginToAlmailUrl(paramString1, "https://mail.mxhichina.com/alimail"));
/* 57 */     this.log.info("url:" + str3);
/* 58 */     return str3;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/interfaces/email/EntranceALYEmail.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */