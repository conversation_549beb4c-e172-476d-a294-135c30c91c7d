/*     */ package weaver.interfaces.email;
/*     */ 
/*     */ import java.io.BufferedReader;
/*     */ import java.io.File;
/*     */ import java.io.FileInputStream;
/*     */ import java.io.InputStream;
/*     */ import java.io.InputStreamReader;
/*     */ import java.io.StringReader;
/*     */ import java.net.HttpURLConnection;
/*     */ import java.net.URL;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Iterator;
/*     */ import org.dom4j.Document;
/*     */ import org.dom4j.Element;
/*     */ import org.dom4j.io.SAXReader;
/*     */ import org.xml.sax.InputSource;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.integration.logging.Logger;
/*     */ import weaver.integration.logging.LoggerFactory;
/*     */ import weaver.security.util.SecurityMethodUtil;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CoreMailXML
/*     */ {
/*  32 */   private static Logger newlog = LoggerFactory.getLogger(CoreMailXML.class);
/*  33 */   private ArrayList<String> nameList = new ArrayList<>();
/*  34 */   private ArrayList<String> valueList = new ArrayList<>();
/*     */   
/*  36 */   private String coreMailList = "";
/*  37 */   private String coreMailPerpage = "";
/*  38 */   private String coreMailLinkMode = "";
/*  39 */   private String coreMailTitle = "";
/*  40 */   private String coreMailUser = "";
/*  41 */   private String coreMailTime = "";
/*     */   
/*  43 */   private String coreMailPrompt = "";
/*  44 */   private String coreMailText = "";
/*  45 */   private String coreMailNumberColor = "";
/*  46 */   private String coreMailLinkColor = "";
/*  47 */   private String coreMailSysid = "";
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public CoreMailXML() {}
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public CoreMailXML(String paramString) {
/*  58 */     getCoreMailElementSet(paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void getCoreMailElementSet(String paramString) {
/*  65 */     this.nameList.add("coreMailList");
/*  66 */     this.nameList.add("coreMailPerpage");
/*  67 */     this.nameList.add("coreMailLinkMode");
/*  68 */     this.nameList.add("coreMailTitle");
/*  69 */     this.nameList.add("coreMailUser");
/*  70 */     this.nameList.add("coreMailTime");
/*     */     
/*  72 */     this.nameList.add("coreMailPrompt");
/*  73 */     this.nameList.add("coreMailText");
/*  74 */     this.nameList.add("coreMailNumberColor");
/*  75 */     this.nameList.add("coreMailLinkColor");
/*  76 */     this.nameList.add("coreMailSysid");
/*     */     
/*  78 */     RecordSet recordSet = new RecordSet();
/*  79 */     recordSet.executeSql("select 1 from hpElementSetting where eid = " + paramString);
/*  80 */     if (recordSet.next()) {
/*  81 */       for (byte b = 0; b < this.nameList.size(); b++) {
/*  82 */         recordSet.executeSql("select value from hpElementSetting where eid = " + paramString + " and name = '" + (String)this.nameList.get(b) + "' ");
/*  83 */         if (recordSet.next()) {
/*  84 */           this.valueList.add(recordSet.getString("value"));
/*     */         }
/*     */       } 
/*     */       
/*  88 */       this.coreMailList = this.valueList.get(this.nameList.indexOf("coreMailList"));
/*  89 */       this.coreMailPerpage = this.valueList.get(this.nameList.indexOf("coreMailPerpage"));
/*  90 */       this.coreMailLinkMode = this.valueList.get(this.nameList.indexOf("coreMailLinkMode"));
/*  91 */       this.coreMailTitle = this.valueList.get(this.nameList.indexOf("coreMailTitle"));
/*  92 */       this.coreMailUser = this.valueList.get(this.nameList.indexOf("coreMailUser"));
/*  93 */       this.coreMailTime = this.valueList.get(this.nameList.indexOf("coreMailTime"));
/*     */       
/*  95 */       this.coreMailPrompt = this.valueList.get(this.nameList.indexOf("coreMailPrompt"));
/*  96 */       this.coreMailText = this.valueList.get(this.nameList.indexOf("coreMailText"));
/*  97 */       this.coreMailNumberColor = this.valueList.get(this.nameList.indexOf("coreMailNumberColor"));
/*  98 */       this.coreMailLinkColor = this.valueList.get(this.nameList.indexOf("coreMailLinkColor"));
/*  99 */       this.coreMailSysid = this.valueList.get(this.nameList.indexOf("coreMailSysid"));
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getXML(String paramString1, String paramString2) {
/* 110 */     String str = "";
/*     */     try {
/* 112 */       URL uRL = new URL(paramString1);
/* 113 */       HttpURLConnection httpURLConnection = (HttpURLConnection)uRL.openConnection();
/* 114 */       httpURLConnection.connect();
/* 115 */       InputStream inputStream = httpURLConnection.getInputStream();
/* 116 */       BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(inputStream, paramString2));
/*     */       
/* 118 */       String str1 = "";
/* 119 */       StringBuffer stringBuffer = new StringBuffer();
/* 120 */       while ((str1 = bufferedReader.readLine()) != null) {
/* 121 */         stringBuffer.append(str1);
/*     */       }
/* 123 */       bufferedReader.close();
/* 124 */       inputStream.close();
/* 125 */       httpURLConnection.disconnect();
/* 126 */       str = stringBuffer.toString();
/* 127 */     } catch (Exception exception) {
/* 128 */       newlog.error("获取CoreMail未读邮件明细(XML格式)出现异常：", exception);
/*     */     } 
/* 130 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getHtml(String paramString) {
/* 139 */     StringBuffer stringBuffer = new StringBuffer();
/* 140 */     StringReader stringReader = new StringReader(paramString);
/* 141 */     InputSource inputSource = new InputSource(stringReader);
/* 142 */     SAXReader sAXReader = new SAXReader();
/*     */     
/*     */     try {
/* 145 */       SecurityMethodUtil.setReaderFeature(sAXReader);
/* 146 */       Document document = sAXReader.read(inputSource);
/* 147 */       Element element = document.getRootElement();
/*     */ 
/*     */ 
/*     */       
/* 151 */       String str = "2".equals(this.coreMailLinkMode) ? "_blank" : "_self";
/* 152 */       byte b1 = 1;
/* 153 */       int i = 1;
/* 154 */       byte b2 = 0;
/*     */       
/* 156 */       if ("1".equals(this.coreMailList) && (
/* 157 */         "1".equals(this.coreMailTitle) || "1".equals(this.coreMailUser) || "1".equals(this.coreMailTime))) {
/* 158 */         Element element1 = null;
/*     */         
/* 160 */         for (Iterator<Element> iterator = element.elementIterator("mail"); iterator.hasNext(); ) {
/* 161 */           element1 = iterator.next();
/* 162 */           String str1 = element1.elementTextTrim("mid");
/* 163 */           String str2 = element1.elementTextTrim("subject");
/* 164 */           String str3 = element1.elementTextTrim("from");
/* 165 */           str3 = str3.replace("\"", "");
/* 166 */           String str4 = element1.elementTextTrim("date");
/* 167 */           if (b2 < Util.getIntValue(this.coreMailPerpage)) {
/* 168 */             stringBuffer.append("<tr>");
/* 169 */             stringBuffer.append("<td height='20' width='8'><img name='esymbol' src='/images/homepage/style/style1/esymbol_wev8.gif'></td>");
/* 170 */             if ("1".equals(this.coreMailTitle)) {
/* 171 */               b1++;
/* 172 */               stringBuffer.append("<td height='20' width='*'>");
/* 173 */               stringBuffer.append("<a class='ellipsis' href='/interface/Entrance_CoreMail.jsp?id=").append(this.coreMailSysid).append("&mid=").append(str1).append("' target='").append(str).append("' title='").append(str2).append("'>").append(str2).append("</a>");
/* 174 */               stringBuffer.append("</td>");
/*     */             } 
/* 176 */             if ("1".equals(this.coreMailUser)) {
/* 177 */               b1++;
/* 178 */               stringBuffer.append("<td height='20' width='30%'><font class='font' title='").append(str3).append("'>").append(str3).append("</font></td>");
/*     */             } 
/* 180 */             if ("1".equals(this.coreMailTime)) {
/* 181 */               b1++;
/* 182 */               stringBuffer.append("<td height='20' width='20%' align='right'><font class='font' title='").append(str4).append("'>").append(str4).append("</font>&nbsp;&nbsp;</td>");
/*     */             } 
/* 184 */             stringBuffer.append("</tr>");
/*     */             
/* 186 */             stringBuffer.append("<tr class='sparator' style='height:1px' height='1px'><td colspan='").append(b1).append("' style='padding:0px'></td></tr>");
/*     */             
/* 188 */             i = b1 - 1;
/*     */           } 
/* 190 */           b2++;
/*     */ 
/*     */ 
/*     */ 
/*     */           
/* 195 */           b1 = 1;
/*     */         } 
/*     */       } 
/*     */ 
/*     */ 
/*     */       
/* 201 */       if ("1".equals(this.coreMailPrompt))
/*     */       {
/*     */         
/* 204 */         String str1 = b2 + "";
/*     */         
/* 206 */         String str2 = "<span style='color:" + this.coreMailNumberColor + "'><b>" + str1 + "</b></span>";
/* 207 */         String str3 = "<a style='color:" + this.coreMailLinkColor + "' href='/interface/Entrance_CoreMail.jsp?id=" + this.coreMailSysid + "&operationType=main' target='" + str + "'><b>";
/* 208 */         String str4 = "</b></a>";
/* 209 */         String str5 = this.coreMailText.replace("$NoReadCnt$", str2).replace("$LoginLinkStart$", str3).replace("$LoginLinkEnd$", str4);
/*     */         
/* 211 */         stringBuffer.append("<tr>");
/* 212 */         stringBuffer.append("<td height='20' width='8'><img name='esymbol' src='/images/homepage/style/style1/esymbol_wev8.gif'></td>");
/* 213 */         stringBuffer.append("<td height='20' width='*' colspan='").append(i).append("'>");
/* 214 */         stringBuffer.append("<font class='font'>").append(str5).append("</font>");
/* 215 */         stringBuffer.append("</td>");
/* 216 */         stringBuffer.append("</tr>");
/*     */       }
/*     */     
/* 219 */     } catch (Exception exception) {
/* 220 */       newlog.error("生成CoreMail元素展示页面出现异常：", exception);
/*     */     } 
/* 222 */     return stringBuffer.toString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getHtmlMore(String paramString) {
/* 231 */     StringBuffer stringBuffer1 = new StringBuffer();
/* 232 */     StringBuffer stringBuffer2 = new StringBuffer();
/* 233 */     StringReader stringReader = new StringReader(paramString);
/* 234 */     InputSource inputSource = new InputSource(stringReader);
/* 235 */     SAXReader sAXReader = new SAXReader();
/*     */     
/*     */     try {
/* 238 */       Document document = sAXReader.read(inputSource);
/* 239 */       Element element = document.getRootElement();
/*     */       
/* 241 */       String str = "2".equals(this.coreMailLinkMode) ? "_blank" : "_self";
/* 242 */       byte b1 = 0;
/* 243 */       byte b2 = 0;
/* 244 */       byte b3 = 0;
/*     */       
/* 246 */       if ("1".equals(this.coreMailList) && (
/* 247 */         "1".equals(this.coreMailTitle) || "1".equals(this.coreMailUser) || "1".equals(this.coreMailTime))) {
/* 248 */         Element element1 = null;
/* 249 */         for (Iterator<Element> iterator = element.elementIterator("mail"); iterator.hasNext(); ) {
/* 250 */           element1 = iterator.next();
/* 251 */           String str1 = element1.elementTextTrim("mid");
/* 252 */           String str2 = element1.elementTextTrim("subject");
/* 253 */           String str3 = element1.elementTextTrim("from");
/* 254 */           str3 = str3.replace("\"", "");
/* 255 */           String str4 = element1.elementTextTrim("date");
/*     */           
/* 257 */           stringBuffer1.append("<tr>");
/* 258 */           if ("1".equals(this.coreMailTitle)) {
/* 259 */             b1++;
/* 260 */             stringBuffer1.append("<td>");
/* 261 */             stringBuffer1.append("<a class='ellipsis' href='/interface/Entrance_CoreMail.jsp?id=").append(this.coreMailSysid).append("&mid=").append(str1).append("' target='").append(str).append("' title='").append(str2).append("'>").append(str2).append("</a>");
/* 262 */             stringBuffer1.append("</td>");
/*     */           } 
/* 264 */           if ("1".equals(this.coreMailUser)) {
/* 265 */             b1++;
/* 266 */             stringBuffer1.append("<td><font class='font' title='").append(str3).append("'>").append(str3).append("</font></td>");
/*     */           } 
/* 268 */           if ("1".equals(this.coreMailTime)) {
/* 269 */             b1++;
/* 270 */             stringBuffer1.append("<td><font class='font' title='").append(str4).append("'>").append(str4).append("</font></td>");
/*     */           } 
/* 272 */           stringBuffer1.append("</tr>");
/* 273 */           b3++;
/* 274 */           b2 = b1;
/* 275 */           b1 = 0;
/*     */         } 
/*     */       } 
/*     */ 
/*     */ 
/*     */       
/* 281 */       if ("1".equals(this.coreMailPrompt))
/*     */       {
/*     */ 
/*     */         
/* 285 */         String str1 = b3 + "";
/*     */         
/* 287 */         String str2 = "<span style='color:" + this.coreMailNumberColor + "'><b>" + b3 + "</b></span>";
/* 288 */         String str3 = "<a style='color:" + this.coreMailLinkColor + "' href='/interface/Entrance_CoreMail.jsp?id=" + this.coreMailSysid + "' target='" + str + "'><b>";
/* 289 */         String str4 = "</b></a>";
/* 290 */         String str5 = this.coreMailText.replace("$NoReadCnt$", str2).replace("$LoginLinkStart$", str3).replace("$LoginLinkEnd$", str4);
/*     */         
/* 292 */         stringBuffer2.append("<tr>");
/* 293 */         stringBuffer2.append("<td colspan='").append(b2).append("'>");
/* 294 */         stringBuffer2.append("<font class='font'>").append(str5).append("</font>");
/* 295 */         stringBuffer2.append("</td>");
/* 296 */         stringBuffer2.append("</tr>");
/*     */       }
/*     */     
/* 299 */     } catch (Exception exception) {
/* 300 */       newlog.error("生成CoreMail元素展示页面出现异常：", exception);
/*     */     } 
/* 302 */     return stringBuffer1.toString() + stringBuffer2.toString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getHead(int paramInt) {
/* 310 */     StringBuffer stringBuffer = new StringBuffer();
/*     */     
/* 312 */     if ("1".equals(this.coreMailList)) {
/* 313 */       stringBuffer.append("<tr>");
/* 314 */       if ("1".equals(this.coreMailTitle)) {
/* 315 */         stringBuffer.append("<th>").append(SystemEnv.getHtmlLabelName(344, paramInt)).append("</th>");
/*     */       }
/* 317 */       if ("1".equals(this.coreMailUser)) {
/* 318 */         stringBuffer.append("<th>").append(SystemEnv.getHtmlLabelName(129935, paramInt)).append("</th>");
/*     */       }
/* 320 */       if ("1".equals(this.coreMailTime)) {
/* 321 */         stringBuffer.append("<th>").append(SystemEnv.getHtmlLabelName(129936, paramInt)).append("</th>");
/*     */       }
/* 323 */       stringBuffer.append("</tr>");
/*     */     } 
/*     */     
/* 326 */     return stringBuffer.toString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getHtmlTest() {
/* 335 */     StringBuffer stringBuffer = new StringBuffer();
/*     */     
/*     */     try {
/* 338 */       File file = new File("E:\\Temp\\test.xml");
/* 339 */       String str1 = "UTF-8";
/* 340 */       InputStreamReader inputStreamReader = new InputStreamReader(new FileInputStream(file), str1);
/* 341 */       BufferedReader bufferedReader = new BufferedReader(inputStreamReader);
/* 342 */       String str2 = null;
/* 343 */       StringBuffer stringBuffer1 = new StringBuffer();
/* 344 */       while ((str2 = bufferedReader.readLine()) != null) {
/* 345 */         stringBuffer1.append(str2);
/*     */       }
/*     */       
/* 348 */       String str3 = stringBuffer1.toString();
/* 349 */       str3 = SecurityMethodUtil.clearEntity(str3);
/* 350 */       StringReader stringReader = new StringReader(str3);
/*     */       
/* 352 */       InputSource inputSource = new InputSource(stringReader);
/* 353 */       SAXReader sAXReader = new SAXReader();
/* 354 */       SecurityMethodUtil.setReaderFeature(sAXReader);
/*     */       
/* 356 */       Document document = sAXReader.read(inputSource);
/* 357 */       Element element = document.getRootElement();
/*     */       
/* 359 */       String str4 = "2".equals(this.coreMailLinkMode) ? "_blank" : "_self";
/* 360 */       byte b = 1;
/* 361 */       int i = 1;
/*     */ 
/*     */       
/* 364 */       if ("1".equals(this.coreMailList) && (
/* 365 */         "1".equals(this.coreMailTitle) || "1".equals(this.coreMailUser) || "1".equals(this.coreMailTime))) {
/* 366 */         Element element1 = null;
/* 367 */         byte b1 = 0;
/* 368 */         for (Iterator<Element> iterator = element.elementIterator("mail"); iterator.hasNext(); ) {
/* 369 */           element1 = iterator.next();
/* 370 */           String str5 = element1.elementTextTrim("subject");
/* 371 */           String str6 = element1.elementTextTrim("from");
/* 372 */           str6 = str6.replace("\"", "");
/* 373 */           String str7 = element1.elementTextTrim("sentdate");
/*     */           
/* 375 */           stringBuffer.append("<tr>");
/* 376 */           stringBuffer.append("<td height='20' width='8'><img name='esymbol' src='/images/homepage/style/style1/esymbol_wev8.gif'></td>");
/* 377 */           if ("1".equals(this.coreMailTitle)) {
/* 378 */             b++;
/* 379 */             stringBuffer.append("<td height='20' width='*'>");
/* 380 */             stringBuffer.append("<a class='ellipsis' href='/interface/Entrance_CoreMail.jsp?id=").append(this.coreMailSysid).append("' target='").append(str4).append("' title='").append(str5).append("'>").append(str5).append("</a>");
/* 381 */             stringBuffer.append("</td>");
/*     */           } 
/* 383 */           if ("1".equals(this.coreMailUser)) {
/* 384 */             b++;
/* 385 */             stringBuffer.append("<td height='20' width='30%'><font class='font' title='").append(str6).append("'>").append(str6).append("</font></td>");
/*     */           } 
/* 387 */           if ("1".equals(this.coreMailTime)) {
/* 388 */             b++;
/* 389 */             stringBuffer.append("<td height='20' width='20%' align='right'><font class='font' title='").append(str7).append("'>").append(str7).append("</font>&nbsp;&nbsp;</td>");
/*     */           } 
/* 391 */           stringBuffer.append("</tr>");
/*     */           
/* 393 */           stringBuffer.append("<tr class='sparator' style='height:1px' height='1px'><td colspan='").append(b).append("' style='padding:0px'></td></tr>");
/*     */           
/* 395 */           i = b - 1;
/*     */           
/* 397 */           b1++;
/* 398 */           if (b1 == Util.getIntValue(this.coreMailPerpage)) {
/*     */             break;
/*     */           }
/*     */           
/* 402 */           b = 1;
/*     */         } 
/*     */       } 
/*     */ 
/*     */ 
/*     */       
/* 408 */       if ("1".equals(this.coreMailPrompt)) {
/* 409 */         Element element1 = null;
/* 410 */         for (Iterator<Element> iterator = element.elementIterator("count"); iterator.hasNext(); ) {
/* 411 */           element1 = iterator.next();
/*     */           
/* 413 */           String str5 = "<span style='color:" + this.coreMailNumberColor + "'><b>" + element1.getTextTrim() + "</b></span>";
/* 414 */           String str6 = "<a style='color:" + this.coreMailLinkColor + "' href='/interface/Entrance_CoreMail.jsp?id=" + this.coreMailSysid + "' target='" + str4 + "'><b>";
/* 415 */           String str7 = "</b></a>";
/* 416 */           String str8 = this.coreMailText.replace("$NoReadCnt$", str5).replace("$LoginLinkStart$", str6).replace("$LoginLinkEnd$", str7);
/*     */           
/* 418 */           stringBuffer.append("<tr>");
/* 419 */           stringBuffer.append("<td height='20' width='8'><img name='esymbol' src='/images/homepage/style/style1/esymbol_wev8.gif'></td>");
/* 420 */           stringBuffer.append("<td height='20' width='*' colspan='").append(i).append("'>");
/* 421 */           stringBuffer.append("<font class='font'>").append(str8).append("</font>");
/* 422 */           stringBuffer.append("</td>");
/* 423 */           stringBuffer.append("</tr>");
/*     */         } 
/*     */       } 
/* 426 */     } catch (Exception exception) {
/* 427 */       newlog.error("生成CoreMail元素展示页面出现异常：", exception);
/*     */     } 
/* 429 */     return stringBuffer.toString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getHtmlTestMore() {
/* 438 */     StringBuffer stringBuffer1 = new StringBuffer();
/* 439 */     StringBuffer stringBuffer2 = new StringBuffer();
/*     */     
/*     */     try {
/* 442 */       File file = new File("E:\\Temp\\test.xml");
/* 443 */       String str1 = "UTF-8";
/* 444 */       InputStreamReader inputStreamReader = new InputStreamReader(new FileInputStream(file), str1);
/* 445 */       BufferedReader bufferedReader = new BufferedReader(inputStreamReader);
/* 446 */       String str2 = null;
/* 447 */       StringBuffer stringBuffer = new StringBuffer();
/* 448 */       while ((str2 = bufferedReader.readLine()) != null) {
/* 449 */         stringBuffer.append(str2);
/*     */       }
/* 451 */       String str3 = stringBuffer.toString();
/* 452 */       str3 = SecurityMethodUtil.clearEntity(str3);
/* 453 */       StringReader stringReader = new StringReader(str3);
/* 454 */       InputSource inputSource = new InputSource(stringReader);
/* 455 */       SAXReader sAXReader = new SAXReader();
/* 456 */       SecurityMethodUtil.setReaderFeature(sAXReader);
/*     */       
/* 458 */       Document document = sAXReader.read(inputSource);
/* 459 */       Element element = document.getRootElement();
/*     */       
/* 461 */       String str4 = "2".equals(this.coreMailLinkMode) ? "_blank" : "_self";
/* 462 */       byte b1 = 0;
/* 463 */       byte b2 = 0;
/*     */ 
/*     */       
/* 466 */       if ("1".equals(this.coreMailList) && (
/* 467 */         "1".equals(this.coreMailTitle) || "1".equals(this.coreMailUser) || "1".equals(this.coreMailTime))) {
/* 468 */         Element element1 = null;
/* 469 */         for (Iterator<Element> iterator = element.elementIterator("mail"); iterator.hasNext(); ) {
/* 470 */           element1 = iterator.next();
/* 471 */           String str5 = element1.elementTextTrim("subject");
/* 472 */           String str6 = element1.elementTextTrim("from");
/* 473 */           str6 = str6.replace("\"", "");
/* 474 */           String str7 = element1.elementTextTrim("sentdate");
/*     */           
/* 476 */           stringBuffer1.append("<tr>");
/* 477 */           if ("1".equals(this.coreMailTitle)) {
/* 478 */             b1++;
/* 479 */             stringBuffer1.append("<td>");
/* 480 */             stringBuffer1.append("<a class='ellipsis' href='/interface/Entrance_CoreMail.jsp?id=").append(this.coreMailSysid).append("' target='").append(str4).append("' title='").append(str5).append("'>").append(str5).append("</a>");
/* 481 */             stringBuffer1.append("</td>");
/*     */           } 
/* 483 */           if ("1".equals(this.coreMailUser)) {
/* 484 */             b1++;
/* 485 */             stringBuffer1.append("<td><font class='font' title='").append(str6).append("'>").append(str6).append("</font></td>");
/*     */           } 
/* 487 */           if ("1".equals(this.coreMailTime)) {
/* 488 */             b1++;
/* 489 */             stringBuffer1.append("<td><font class='font' title='").append(str7).append("'>").append(str7).append("</font></td>");
/*     */           } 
/* 491 */           stringBuffer1.append("</tr>");
/*     */           
/* 493 */           b2 = b1;
/* 494 */           b1 = 0;
/*     */         } 
/*     */       } 
/*     */ 
/*     */ 
/*     */       
/* 500 */       if ("1".equals(this.coreMailPrompt)) {
/* 501 */         Element element1 = null;
/* 502 */         for (Iterator<Element> iterator = element.elementIterator("count"); iterator.hasNext(); ) {
/* 503 */           element1 = iterator.next();
/*     */           
/* 505 */           String str5 = "<span style='color:" + this.coreMailNumberColor + "'><b>" + element1.getTextTrim() + "</b></span>";
/* 506 */           String str6 = "<a style='color:" + this.coreMailLinkColor + "' href='/interface/Entrance_CoreMail.jsp?id=" + this.coreMailSysid + "' target='" + str4 + "'><b>";
/* 507 */           String str7 = "</b></a>";
/* 508 */           String str8 = this.coreMailText.replace("$NoReadCnt$", str5).replace("$LoginLinkStart$", str6).replace("$LoginLinkEnd$", str7);
/*     */           
/* 510 */           stringBuffer2.append("<tr>");
/* 511 */           stringBuffer2.append("<td colspan='").append(b2).append("'>");
/* 512 */           stringBuffer2.append("<font class='font'>").append(str8).append("</font>");
/* 513 */           stringBuffer2.append("</td>");
/* 514 */           stringBuffer2.append("</tr>");
/*     */         } 
/*     */       } 
/* 517 */     } catch (Exception exception) {
/* 518 */       newlog.error("生成CoreMail元素展示页面出现异常：", exception);
/*     */     } 
/* 520 */     return stringBuffer2.toString() + stringBuffer1.toString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getHeadTest(int paramInt) {
/* 528 */     StringBuffer stringBuffer = new StringBuffer();
/*     */     
/* 530 */     if ("1".equals(this.coreMailList)) {
/* 531 */       stringBuffer.append("<tr>");
/* 532 */       if ("1".equals(this.coreMailTitle)) {
/* 533 */         stringBuffer.append("<th>").append(SystemEnv.getHtmlLabelName(344, paramInt)).append("</th>");
/*     */       }
/* 535 */       if ("1".equals(this.coreMailUser)) {
/* 536 */         stringBuffer.append("<th>").append(SystemEnv.getHtmlLabelName(129935, paramInt)).append("</th>");
/*     */       }
/* 538 */       if ("1".equals(this.coreMailTime)) {
/* 539 */         stringBuffer.append("<th>").append(SystemEnv.getHtmlLabelName(129936, paramInt)).append("</th>");
/*     */       }
/* 541 */       stringBuffer.append("</tr>");
/*     */     } 
/*     */     
/* 544 */     return stringBuffer.toString();
/*     */   }
/*     */   
/*     */   public String getCoreMailList() {
/* 548 */     return this.coreMailList;
/*     */   }
/*     */   
/*     */   public String getCoreMailPerpage() {
/* 552 */     return this.coreMailPerpage;
/*     */   }
/*     */   
/*     */   public String getCoreMailLinkMode() {
/* 556 */     return this.coreMailLinkMode;
/*     */   }
/*     */   
/*     */   public String getCoreMailTitle() {
/* 560 */     return this.coreMailTitle;
/*     */   }
/*     */   
/*     */   public String getCoreMailUser() {
/* 564 */     return this.coreMailUser;
/*     */   }
/*     */   
/*     */   public String getCoreMailTime() {
/* 568 */     return this.coreMailTime;
/*     */   }
/*     */   
/*     */   public String getCoreMailPrompt() {
/* 572 */     return this.coreMailPrompt;
/*     */   }
/*     */   
/*     */   public String getCoreMailText() {
/* 576 */     return this.coreMailText;
/*     */   }
/*     */   
/*     */   public String getCoreMailNumberColor() {
/* 580 */     return this.coreMailNumberColor;
/*     */   }
/*     */   
/*     */   public String getCoreMailLinkColor() {
/* 584 */     return this.coreMailLinkColor;
/*     */   }
/*     */   
/*     */   public String getCoreMailSysid() {
/* 588 */     return this.coreMailSysid;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/interfaces/email/CoreMailXML.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */