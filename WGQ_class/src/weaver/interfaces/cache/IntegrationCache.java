package weaver.interfaces.cache;

public interface IntegrationCache {
  Object loadCache();
  
  Object addCache(String paramString, Object paramObject);
  
  Object setCache(String paramString, Object paramObject);
  
  Object delCache(String paramString);
  
  Object getCacheByKey(String paramString);
  
  Object getObjectFromDB(String paramString);
}


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/interfaces/cache/IntegrationCache.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */