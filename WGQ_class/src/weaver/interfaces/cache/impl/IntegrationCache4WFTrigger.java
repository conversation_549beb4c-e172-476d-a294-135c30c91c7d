/*     */ package weaver.interfaces.cache.impl;
/*     */ 
/*     */ import com.alibaba.fastjson.JSON;
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import com.engine.integration.biz.trigger.job.TriggerCronUtil;
/*     */ import java.text.SimpleDateFormat;
/*     */ import java.util.Date;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.crazydream.util.Condition;
/*     */ import weaver.general.Util;
/*     */ import weaver.integration.framework.converter.rule.RuleSwitchProcessor;
/*     */ import weaver.integration.logging.Logger;
/*     */ import weaver.integration.logging.LoggerFactory;
/*     */ import weaver.integration.workflowtrigger.config.WorkflowTriggerConfig;
/*     */ import weaver.integration.workflowtrigger.config.WorkflowTriggerPeriodConfig;
/*     */ import weaver.integration.workflowtrigger.service.WorkflowTriggerConfigService;
/*     */ import weaver.integration.workflowtrigger.service.WorkflowTriggerDetailConfigService;
/*     */ import weaver.integration.workflowtrigger.service.WorkflowTriggerPeriodConfigService;
/*     */ import weaver.interfaces.schedule.BaseCronJob;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class IntegrationCache4WFTrigger
/*     */   extends CacheHook
/*     */ {
/*  33 */   private String SQL = "select * from outerdatawfset order by id";
/*     */ 
/*     */   
/*     */   public Object loadCache() {
/*  37 */     log.info("  初始化历史附件设置数据 ......");
/*     */     try {
/*  39 */       initOldttachmentData();
/*  40 */     } catch (Exception exception) {
/*  41 */       exception.printStackTrace();
/*  42 */       log.error(exception);
/*  43 */     } catch (Throwable throwable) {
/*  44 */       log.error(throwable);
/*     */     } 
/*     */     
/*  47 */     log.info("  初始化转换规则 ......");
/*     */     try {
/*  49 */       initConvertClass();
/*  50 */     } catch (Exception exception) {
/*  51 */       exception.printStackTrace();
/*  52 */       log.error(exception);
/*  53 */     } catch (Throwable throwable) {
/*  54 */       log.error(throwable);
/*     */     } 
/*     */     
/*  57 */     log.info("  初始化触发任务 ......");
/*     */     try {
/*  59 */       initTriggerJob();
/*  60 */     } catch (Exception exception) {
/*  61 */       exception.printStackTrace();
/*  62 */       log.error(exception);
/*  63 */     } catch (Throwable throwable) {
/*  64 */       log.error(throwable);
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  80 */     return null;
/*     */   }
/*     */ 
/*     */   
/*     */   public Object addCache(String paramString, Object paramObject) {
/*  85 */     if (paramObject == null)
/*     */     {
/*  87 */       return null;
/*     */     }
/*  89 */     log.debug("WFTrigger加入缓存成功:" + paramString);
/*     */     
/*  91 */     return super.addCache("wftrigger." + paramString, paramObject);
/*     */   }
/*     */ 
/*     */   
/*     */   public Object setCache(String paramString, Object paramObject) {
/*  96 */     if (paramObject == null) {
/*  97 */       log.debug("WFTrigger更新缓存异常:" + paramString);
/*  98 */       return null;
/*     */     } 
/* 100 */     log.debug("WFTrigger更新缓存成功:" + paramString);
/*     */     
/* 102 */     return super.setCache("wftrigger." + paramString, paramObject);
/*     */   }
/*     */ 
/*     */   
/*     */   public Object delCache(String paramString) {
/* 107 */     Object object = super.delCache("wftrigger." + paramString);
/* 108 */     if (object != null) {
/* 109 */       log.debug("WFTrigger删除缓存成功:" + paramString);
/*     */     }
/* 111 */     return null;
/*     */   }
/*     */ 
/*     */   
/*     */   public Object getCacheByKey(String paramString) {
/* 116 */     return super.getCacheByKey("wftrigger." + paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public Object getObjectFromDB(String paramString) {
/* 122 */     WorkflowTriggerConfig workflowTriggerConfig = (new WorkflowTriggerConfigService()).get(paramString);
/* 123 */     if (workflowTriggerConfig != null) {
/* 124 */       String str = workflowTriggerConfig.getId();
/*     */       
/* 126 */       workflowTriggerConfig.setWorkflowTriggerDetailConfigs((new WorkflowTriggerDetailConfigService()).getList((new Condition()).equals("mainid", str)));
/*     */       
/* 128 */       WorkflowTriggerPeriodConfigService workflowTriggerPeriodConfigService = new WorkflowTriggerPeriodConfigService();
/* 129 */       List<WorkflowTriggerPeriodConfig> list = workflowTriggerPeriodConfigService.getList((new Condition()).equals("scope", str));
/* 130 */       if (list != null && list.size() > 0) {
/* 131 */         workflowTriggerConfig.setWorkflowTriggerPeriodConfig(list.get(0));
/*     */       }
/*     */ 
/*     */       
/* 135 */       workflowTriggerConfig.init();
/*     */       
/* 137 */       return workflowTriggerConfig;
/*     */     } 
/*     */     
/* 140 */     return null;
/*     */   }
/* 142 */   private static String querySql = "select id,wffieldid,customsql,changetype,wffieldhtmltype,wffieldtype,attachment_settings from outerdatawfsetdetail  where ConvertClass is null Or ConvertClass='' ";
/* 143 */   private static String updateSql = "update  outerdatawfsetdetail set ConvertClass=? where id=?";
/*     */ 
/*     */ 
/*     */   
/*     */   public void initConvertClass() {
/*     */     try {
/* 149 */       RuleSwitchProcessor ruleSwitchProcessor = RuleSwitchProcessor.getInstance();
/* 150 */       RecordSet recordSet1 = new RecordSet();
/* 151 */       RecordSet recordSet2 = new RecordSet();
/* 152 */       recordSet1.executeQuery(querySql, new Object[0]);
/* 153 */       while (recordSet1.next()) {
/*     */         try {
/* 155 */           String str1 = Util.null2String(recordSet1.getString("id"));
/* 156 */           HashMap<Object, Object> hashMap = new HashMap<>();
/* 157 */           hashMap.put("fieldId", Util.null2String(recordSet1.getString("wffieldid")));
/* 158 */           hashMap.put("customsql", Util.null2String(recordSet1.getString("customsql")));
/* 159 */           hashMap.put("changetype", Util.null2String(recordSet1.getString("changetype")));
/* 160 */           hashMap.put("fieldhtmltype", Util.null2String(recordSet1.getString("wffieldhtmltype")));
/* 161 */           hashMap.put("fieldtype", Util.null2String(recordSet1.getString("wffieldtype")));
/* 162 */           hashMap.put("attachment_settings", Util.null2String(recordSet1.getString("attachment_settings")));
/* 163 */           String str2 = ruleSwitchProcessor.getRule(hashMap);
/* 164 */           recordSet2.executeUpdate(updateSql, new Object[] { str2, str1 });
/* 165 */         } catch (Throwable throwable) {}
/*     */       
/*     */       }
/*     */ 
/*     */     
/*     */     }
/* 171 */     catch (Exception exception) {}
/*     */   }
/*     */ 
/*     */   
/* 175 */   private static String querySql1 = "select * from outerdatawfsetdetail   where wffieldhtmltype=6 ";
/*     */   
/* 177 */   private static String isInit_SQL = "select * from init_integration_trrigerdata ";
/*     */   
/* 179 */   private static String Update_SQL = "update  init_integration_trrigerdata set isinit=1  ";
/*     */   
/* 181 */   private static String Update_SQL1 = "update  outerdatawfsetdetail set changetype=? where id=?  ";
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean initOldttachmentData() {
/* 187 */     RecordSet recordSet1 = new RecordSet();
/* 188 */     RecordSet recordSet2 = new RecordSet();
/* 189 */     recordSet1.executeQuery(isInit_SQL, new Object[0]);
/* 190 */     int i = 0;
/* 191 */     if (recordSet1.next()) {
/* 192 */       i = recordSet1.getInt(1);
/*     */     }
/* 194 */     if (i == 0) {
/* 195 */       recordSet1.executeQuery(querySql1, new Object[0]);
/* 196 */       while (recordSet1.next()) {
/* 197 */         String str1 = recordSet1.getString("id");
/* 198 */         String str2 = Util.null2String(recordSet1.getString("attachment_settings"));
/* 199 */         int j = 0;
/* 200 */         if (!"".equals(str2)) {
/* 201 */           JSONObject jSONObject = JSON.parseObject(str2);
/* 202 */           j = Util.getIntValue(jSONObject.getString("attachment_type"), -1);
/* 203 */           recordSet2.executeUpdate(Update_SQL1, new Object[] { Integer.valueOf(j), str1 });
/*     */         } 
/*     */       } 
/* 206 */       recordSet2.executeUpdate(Update_SQL, new Object[0]);
/*     */     } 
/*     */     
/* 209 */     return false;
/*     */   }
/*     */   
/* 212 */   private String CLASSPATH = "weaver.interfaces.schedule.TriggerJob";
/* 213 */   private String add_job = "insert into schedulesetting(pointid,classpath,cronexpr,desc_,runstatus,createdate,createtime,tasktype) values (?,?,?,?,?,?,?,1)";
/* 214 */   private String add_jobdetail = "insert into schedulesettingdetail(scheduledbid,attrname,attrvalue,isdatasource) values (?,?,?,?)";
/* 215 */   private String query_sql = "select id from schedulesetting where pointid=? ";
/* 216 */   SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
/* 217 */   SimpleDateFormat timeFormat = new SimpleDateFormat("HH:mm:ss");
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void initTriggerJob() {
/* 224 */     RecordSet recordSet1 = new RecordSet();
/* 225 */     RecordSet recordSet2 = new RecordSet();
/* 226 */     TriggerCronUtil triggerCronUtil = new TriggerCronUtil();
/* 227 */     recordSet1.executeQuery(this.SQL, new Object[0]);
/* 228 */     while (recordSet1.next()) {
/* 229 */       String str1 = Util.null2String(recordSet1.getString("id"));
/* 230 */       String str2 = Util.null2String(recordSet1.getString("setname"));
/* 231 */       if (triggerCronUtil.checkJobIsExist(str1)) {
/*     */         continue;
/*     */       }
/* 234 */       boolean bool = triggerCronUtil.checkCronisNo(str1);
/*     */       
/* 236 */       String str3 = Util.null2String(triggerCronUtil.getSingleTriggerCron(str1));
/* 237 */       String str4 = Util.null2String(triggerCronUtil.getTriggerCron(str1));
/* 238 */       String str5 = "weaver_trigger_" + str1;
/* 239 */       String str6 = "0";
/* 240 */       if (str3.equals("") && bool) {
/* 241 */         str6 = "1";
/* 242 */         str3 = "0 0 0 0 0 ? 2099";
/*     */       } 
/*     */       
/* 245 */       if ("".equals(str4)) {
/*     */         continue;
/*     */       }
/* 248 */       if ("".equals(str3) && !"".equals(str4)) {
/* 249 */         str3 = str4;
/*     */       }
/* 251 */       recordSet2.executeUpdate(this.add_job, new Object[] { str5, this.CLASSPATH, str3, str2, str6, this.dateFormat.format(new Date()), this.timeFormat.format(new Date()) });
/* 252 */       String str7 = "";
/* 253 */       recordSet2.executeQuery(this.query_sql, new Object[] { str5 });
/* 254 */       if (recordSet2.next()) {
/* 255 */         str7 = recordSet2.getString(1);
/* 256 */         recordSet2.executeUpdate(this.add_jobdetail, new Object[] { str7, "sourceid", str1, "0" });
/*     */       } 
/* 258 */       IntegrationCache4Schedule integrationCache4Schedule = new IntegrationCache4Schedule();
/* 259 */       BaseCronJob baseCronJob = (BaseCronJob)integrationCache4Schedule.getObjectFromDB(str5);
/* 260 */       if (baseCronJob != null) {
/* 261 */         integrationCache4Schedule.setCache("" + str5, baseCronJob);
/*     */       }
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/* 269 */   private static Logger log = LoggerFactory.getLogger(IntegrationCache4WFTrigger.class);
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/interfaces/cache/impl/IntegrationCache4WFTrigger.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */