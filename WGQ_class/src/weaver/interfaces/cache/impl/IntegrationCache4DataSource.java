/*     */ package weaver.interfaces.cache.impl;
/*     */ 
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.SecurityHelper;
/*     */ import weaver.general.Util;
/*     */ import weaver.integration.logging.Logger;
/*     */ import weaver.integration.logging.LoggerFactory;
/*     */ import weaver.interfaces.datasource.BaseDataSource;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class IntegrationCache4DataSource
/*     */   extends CacheHook
/*     */ {
/*     */   public Object loadCache() {
/*  17 */     RecordSet recordSet = new RecordSet();
/*  18 */     log.info("  初始化数据源缓存 ......");
/*     */     
/*  20 */     String str = "/c4Q2hAVXFc=";
/*  21 */     recordSet.executeSql("select * from datasourcesetting order by id");
/*  22 */     while (recordSet.next()) {
/*  23 */       String str1 = Util.null2String(recordSet.getString("pointid"));
/*     */       try {
/*  25 */         String str2 = Util.null2String(recordSet.getString("id"));
/*  26 */         String str3 = Util.null2String(recordSet.getString("classpath"));
/*  27 */         String str4 = Util.null2String(recordSet.getString("type"));
/*  28 */         String str5 = Util.null2String(recordSet.getString("iscluster"));
/*  29 */         String str6 = Util.null2String(recordSet.getString("typename"));
/*  30 */         String str7 = Util.null2String(recordSet.getString("datasourcename"));
/*  31 */         String str8 = Util.null2String(recordSet.getString("url"));
/*  32 */         String str9 = Util.null2String(recordSet.getString("host"));
/*  33 */         String str10 = Util.null2String(recordSet.getString("port"));
/*  34 */         String str11 = Util.null2String(recordSet.getString("dbname"));
/*  35 */         String str12 = Util.null2String(recordSet.getString("username"));
/*  36 */         String str13 = Util.null2String(recordSet.getString("password"));
/*  37 */         String str14 = Util.null2String(recordSet.getString("iscode"));
/*  38 */         String str15 = Util.null2String(recordSet.getString("usepool"));
/*  39 */         String str16 = Util.null2String(recordSet.getString("sortid"));
/*  40 */         String str17 = Util.null2String(recordSet.getString("minconn"));
/*  41 */         String str18 = Util.null2String(recordSet.getString("maxconn"));
/*     */         
/*  43 */         String str19 = str8;
/*  44 */         String str20 = str9;
/*  45 */         String str21 = str10;
/*  46 */         String str22 = str11;
/*  47 */         String str23 = str12;
/*  48 */         String str24 = str13;
/*     */ 
/*     */         
/*  51 */         str8 = (str8.indexOf(str) > -1) ? SecurityHelper.decrypt("ecology", str8) : str8;
/*  52 */         str9 = (str9.indexOf(str) > -1) ? SecurityHelper.decrypt("ecology", str9) : str9;
/*  53 */         str10 = (str10.indexOf(str) > -1) ? SecurityHelper.decrypt("ecology", str10) : str10;
/*  54 */         str11 = (str11.indexOf(str) > -1) ? SecurityHelper.decrypt("ecology", str11) : str11;
/*  55 */         str12 = (str12.indexOf(str) > -1) ? SecurityHelper.decrypt("ecology", str12) : str12;
/*  56 */         str13 = (str13.indexOf(str) > -1) ? SecurityHelper.decrypt("ecology", str13) : str13;
/*     */         
/*  58 */         String str25 = "1";
/*  59 */         if ("2".equals(str5)) {
/*  60 */           if ("".equals(str8) || "".equals(str12) || "".equals(str13)) {
/*  61 */             str25 = "0";
/*     */           }
/*     */         }
/*  64 */         else if (str4.toLowerCase().indexOf("hana") > -1 || str4.toLowerCase().indexOf("dm") > -1 || str4.toLowerCase().indexOf("gs") > -1) {
/*  65 */           if ("".equals(str9) || "".equals(str10) || "".equals(str12) || "".equals(str13)) {
/*  66 */             str25 = "0";
/*     */           }
/*  68 */         } else if (str4.toLowerCase().indexOf("odbc") > -1) {
/*  69 */           if ("".equals(str11) || "".equals(str12) || "".equals(str13)) {
/*  70 */             str25 = "0";
/*     */           }
/*  72 */         } else if (str4.toLowerCase().indexOf("access") > -1) {
/*  73 */           if ("".equals(str11)) {
/*  74 */             str25 = "0";
/*     */           }
/*     */         }
/*  77 */         else if ("".equals(str9) || "".equals(str10) || "".equals(str11) || "".equals(str12) || "".equals(str13)) {
/*  78 */           str25 = "0";
/*     */         } 
/*     */ 
/*     */ 
/*     */         
/*  83 */         if ("0".equals(str25)) {
/*  84 */           log.error("Datasource加入缓存异常(pointid=" + str1 + ")：数据源停止加载，初始化DB连接数据不完整。 type=" + str4 + ", url=" + str8 + ", url2=" + str19 + ", host=" + str9 + ", host2=" + str20 + ", port=" + str10 + ", port2=" + str21 + ", dbname=" + str11 + ", dbname2=" + str22 + ", username=" + str12 + ", username2=" + str23 + ", password=" + str13 + ", password2=" + str24);
/*     */           
/*     */           continue;
/*     */         } 
/*  88 */         BaseDataSource baseDataSource = null;
/*     */         try {
/*  90 */           Class<?> clazz = Class.forName(str3);
/*  91 */           baseDataSource = (BaseDataSource)clazz.newInstance();
/*  92 */         } catch (Throwable throwable) {
/*  93 */           log.error("Datasource加入缓存异常(pointid=" + str1 + ")：数据源停止加载，初始化DB的classpath不正确，classpath=" + str3, throwable);
/*     */           continue;
/*     */         } 
/*  96 */         baseDataSource.setType(str4);
/*  97 */         baseDataSource.setIscluster(str5);
/*  98 */         baseDataSource.setTypename(str6);
/*  99 */         baseDataSource.setDatasourcename(str7);
/* 100 */         baseDataSource.setUrl(str8);
/* 101 */         baseDataSource.setHost(str9);
/* 102 */         baseDataSource.setPort(str10);
/* 103 */         baseDataSource.setDbname(str11);
/*     */         
/* 105 */         baseDataSource.setUsepool(Util.getIntValue(str15));
/* 106 */         baseDataSource.setSortid(Util.getIntValue(str16));
/* 107 */         baseDataSource.setMinconn(Util.getIntValue(str17));
/* 108 */         baseDataSource.setMaxconn(Util.getIntValue(str18));
/*     */         
/* 110 */         baseDataSource.setIscode(str14);
/* 111 */         baseDataSource.setUser(str12);
/* 112 */         baseDataSource.setPassword(str13);
/*     */ 
/*     */         
/* 115 */         if (baseDataSource != null) {
/* 116 */           addCache(str1, baseDataSource);
/*     */         }
/* 118 */         log.debug("初始化数据源成功： " + str1);
/* 119 */       } catch (Exception exception) {
/* 120 */         exception.printStackTrace();
/* 121 */         log.error("初始化数据源异常(" + str1 + ")", exception);
/*     */       }
/* 123 */       catch (Throwable throwable) {
/* 124 */         throwable.printStackTrace();
/* 125 */         log.error("初始化数据源异常(" + str1 + ")", throwable);
/*     */       } 
/*     */     } 
/*     */ 
/*     */     
/* 130 */     return null;
/*     */   }
/*     */ 
/*     */   
/*     */   public Object addCache(String paramString, Object paramObject) {
/* 135 */     if (paramObject == null)
/*     */     {
/* 137 */       return null;
/*     */     }
/* 139 */     log.debug("DataSource加入缓存成功:" + paramString);
/*     */     
/* 141 */     return super.addCache("datasource." + paramString, paramObject);
/*     */   }
/*     */ 
/*     */   
/*     */   public Object setCache(String paramString, Object paramObject) {
/* 146 */     if (paramObject == null) {
/* 147 */       log.error("DataSource更新缓存异常:" + paramString);
/* 148 */       return null;
/*     */     } 
/* 150 */     log.debug("DataSource更新缓存成功:" + paramString);
/*     */     
/* 152 */     return super.setCache("datasource." + paramString, paramObject);
/*     */   }
/*     */ 
/*     */   
/*     */   public Object delCache(String paramString) {
/* 157 */     Object object = super.delCache("datasource." + paramString);
/* 158 */     if (object != null) {
/* 159 */       log.debug("DataSource删除缓存成功:" + paramString);
/*     */     }
/* 161 */     return null;
/*     */   }
/*     */ 
/*     */   
/*     */   public Object getCacheByKey(String paramString) {
/* 166 */     return super.getCacheByKey("datasource." + paramString);
/*     */   }
/*     */ 
/*     */   
/*     */   public Object getObjectFromDB(String paramString) {
/* 171 */     String str = "/c4Q2hAVXFc=";
/* 172 */     BaseDataSource baseDataSource = null;
/* 173 */     RecordSet recordSet = new RecordSet();
/* 174 */     recordSet.executeQuery("select * from datasourcesetting where pointid=?", new Object[] { paramString });
/* 175 */     while (recordSet.next()) {
/* 176 */       String str1 = Util.null2String(recordSet.getString("classpath"));
/* 177 */       String str2 = Util.null2String(recordSet.getString("type"));
/* 178 */       String str3 = Util.null2String(recordSet.getString("iscluster"));
/* 179 */       String str4 = Util.null2String(recordSet.getString("typename"));
/* 180 */       String str5 = Util.null2String(recordSet.getString("datasourcename"));
/* 181 */       String str6 = Util.null2String(recordSet.getString("url"));
/* 182 */       String str7 = Util.null2String(recordSet.getString("host"));
/* 183 */       String str8 = Util.null2String(recordSet.getString("port"));
/* 184 */       String str9 = Util.null2String(recordSet.getString("dbname"));
/* 185 */       String str10 = Util.null2String(recordSet.getString("username"));
/* 186 */       String str11 = Util.null2String(recordSet.getString("password"));
/* 187 */       String str12 = Util.null2String(recordSet.getString("iscode"));
/* 188 */       String str13 = Util.null2String(recordSet.getString("usepool"));
/* 189 */       String str14 = Util.null2String(recordSet.getString("sortid"));
/* 190 */       String str15 = Util.null2String(recordSet.getString("minconn"));
/* 191 */       String str16 = Util.null2String(recordSet.getString("maxconn"));
/*     */       
/* 193 */       String str17 = str6;
/* 194 */       String str18 = str7;
/* 195 */       String str19 = str8;
/* 196 */       String str20 = str9;
/* 197 */       String str21 = str10;
/* 198 */       String str22 = str11;
/*     */ 
/*     */       
/* 201 */       str6 = (str6.indexOf(str) > -1) ? SecurityHelper.decrypt("ecology", str6) : str6;
/* 202 */       str7 = (str7.indexOf(str) > -1) ? SecurityHelper.decrypt("ecology", str7) : str7;
/* 203 */       str8 = (str8.indexOf(str) > -1) ? SecurityHelper.decrypt("ecology", str8) : str8;
/* 204 */       str9 = (str9.indexOf(str) > -1) ? SecurityHelper.decrypt("ecology", str9) : str9;
/* 205 */       str10 = (str10.indexOf(str10) > -1) ? SecurityHelper.decrypt("ecology", str10) : str10;
/* 206 */       str11 = (str11.indexOf(str) > -1) ? SecurityHelper.decrypt("ecology", str11) : str11;
/*     */       
/* 208 */       String str23 = "1";
/* 209 */       if ("2".equals(str3)) {
/* 210 */         if ("".equals(str6) || "".equals(str10) || "".equals(str11)) {
/* 211 */           str23 = "0";
/*     */         }
/*     */       }
/* 214 */       else if (str2.toLowerCase().indexOf("hana") > -1 || str2.toLowerCase().indexOf("dm") > -1 || str2.toLowerCase().indexOf("gs") > -1) {
/* 215 */         if ("".equals(str7) || "".equals(str8) || "".equals(str10) || "".equals(str11)) {
/* 216 */           str23 = "0";
/*     */         }
/* 218 */       } else if (str2.toLowerCase().indexOf("odbc") > -1) {
/* 219 */         if ("".equals(str9) || "".equals(str10) || "".equals(str11)) {
/* 220 */           str23 = "0";
/*     */         }
/* 222 */       } else if (str2.toLowerCase().indexOf("access") > -1) {
/* 223 */         if ("".equals(str9)) {
/* 224 */           str23 = "0";
/*     */         }
/*     */       }
/* 227 */       else if ("".equals(str7) || "".equals(str8) || "".equals(str9) || "".equals(str10) || "".equals(str11)) {
/* 228 */         str23 = "0";
/*     */       } 
/*     */ 
/*     */ 
/*     */       
/* 233 */       if ("0".equals(str23)) {
/* 234 */         log.error("Datasource加入缓存异常(pointid=" + paramString + ")：数据源停止加载，初始化DB连接数据不完整。 type=" + str2 + ", url=" + str6 + ", url2=" + str17 + ", host=" + str7 + ", host2=" + str18 + ", port=" + str8 + ", port2=" + str19 + ", dbname=" + str9 + ", dbname2=" + str20 + ", username=" + str10 + ", username2=" + str21 + ", password=" + str11 + ", password2=" + str22);
/*     */         
/*     */         continue;
/*     */       } 
/* 238 */       BaseDataSource baseDataSource1 = null;
/*     */       try {
/* 240 */         Class<?> clazz = Class.forName(str1);
/* 241 */         baseDataSource1 = (BaseDataSource)clazz.newInstance();
/* 242 */       } catch (Throwable throwable) {
/* 243 */         log.error("Datasource加入缓存异常(pointid=" + paramString + ")：数据源停止加载，初始化DB的classpath不正确，classpath=" + str1, throwable);
/*     */         
/*     */         continue;
/*     */       } 
/* 247 */       baseDataSource1.setType(str2);
/* 248 */       baseDataSource1.setIscluster(str3);
/* 249 */       baseDataSource1.setTypename(str4);
/* 250 */       baseDataSource1.setDatasourcename(str5);
/* 251 */       baseDataSource1.setUrl(str6);
/* 252 */       baseDataSource1.setHost(str7);
/* 253 */       baseDataSource1.setPort(str8);
/* 254 */       baseDataSource1.setDbname(str9);
/*     */       
/* 256 */       baseDataSource1.setUsepool(Util.getIntValue(str13));
/* 257 */       baseDataSource1.setSortid(Util.getIntValue(str14));
/* 258 */       baseDataSource1.setMinconn(Util.getIntValue(str15));
/* 259 */       baseDataSource1.setMaxconn(Util.getIntValue(str16));
/*     */       
/* 261 */       baseDataSource1.setIscode(str12);
/* 262 */       baseDataSource1.setUser(str10);
/* 263 */       baseDataSource1.setPassword(str11);
/*     */       
/* 265 */       baseDataSource = baseDataSource1;
/*     */     } 
/* 267 */     return baseDataSource;
/*     */   }
/*     */   
/* 270 */   private static Logger log = LoggerFactory.getLogger(IntegrationCache4DataSource.class);
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/interfaces/cache/impl/IntegrationCache4DataSource.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */