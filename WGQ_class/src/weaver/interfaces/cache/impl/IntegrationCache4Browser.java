/*     */ package weaver.interfaces.cache.impl;
/*     */ 
/*     */ import com.engine.integration.dao.DatashowDao;
/*     */ import java.text.SimpleDateFormat;
/*     */ import java.util.Date;
/*     */ import weaver.conn.ConnectionPool;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.file.Prop;
/*     */ import weaver.general.Util;
/*     */ import weaver.integration.logging.Logger;
/*     */ import weaver.integration.logging.LoggerFactory;
/*     */ import weaver.integration.page.PageUtil;
/*     */ import weaver.integration.util.DataSourceUtil;
/*     */ import weaver.interfaces.workflow.browser.BaseBrowser;
/*     */ import weaver.interfaces.workflow.browser.browsercache.BrowserCacheManger;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class IntegrationCache4Browser
/*     */   extends CacheHook
/*     */ {
/*  26 */   SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
/*  27 */   SimpleDateFormat format1 = new SimpleDateFormat("HH:mm:ss");
/*  28 */   private String CACHE_TABLE = "int_browsercache_";
/*     */   
/*     */   public Object loadCache() {
/*  31 */     DatashowDao datashowDao = new DatashowDao();
/*  32 */     RecordSet recordSet1 = new RecordSet();
/*  33 */     RecordSet recordSet2 = new RecordSet();
/*  34 */     RecordSet recordSet3 = new RecordSet();
/*  35 */     log.info("  初始化浏览框缓存 ......");
/*     */     
/*  37 */     recordSet1.executeSql("select * from datashowset where showclass='1' order by id");
/*  38 */     while (recordSet1.next()) {
/*  39 */       String str1 = Util.null2String(recordSet1.getString("id"));
/*  40 */       String str2 = Util.null2String(recordSet1.getString("showname"));
/*  41 */       String str3 = Util.null2String(recordSet1.getString("subcompanyid"));
/*  42 */       String str4 = Util.null2String(recordSet1.getString("browserfrom"));
/*  43 */       recordSet3.executeQuery(datashowDao.getQUERY_CACHE_SHOWNAME_SQL(), new Object[] { str2 });
/*  44 */       if (!recordSet3.next() && str4.equals("2")) {
/*  45 */         recordSet3.executeUpdate(datashowDao.getAdd_CACHESET_SQL(), new Object[] { str2, str3, Integer.valueOf(0), this.format.format(new Date()), this.format1.format(new Date()), this.format.format(new Date()), this.format1.format(new Date()) });
/*     */       }
/*  47 */       String str5 = Util.null2String(recordSet1.getString("datasourceid"));
/*  48 */       String str6 = Util.null2String(recordSet1.getString("name"));
/*  49 */       String str7 = Util.null2String(recordSet1.getString("sqltext"));
/*  50 */       String str8 = Util.null2String(recordSet1.getString("sqltext1"));
/*  51 */       String str9 = Util.null2String(recordSet1.getString("sqltext2"));
/*  52 */       String str10 = Util.null2s(recordSet1.getString("searchById"), str8);
/*  53 */       String str11 = Util.null2s(recordSet1.getString("searchByName"), str9);
/*  54 */       String str12 = Util.null2String(recordSet1.getString("nameHeader"));
/*  55 */       String str13 = Util.null2String(recordSet1.getString("descriptionHeader"));
/*  56 */       String str14 = Util.null2String(recordSet1.getString("showpageurl"));
/*  57 */       String str15 = Util.null2String(recordSet1.getString("detailpageurl"));
/*  58 */       String str16 = Util.null2String(recordSet1.getString("showtype"));
/*  59 */       String str17 = Util.null2String(recordSet1.getString("showtype"));
/*  60 */       String str18 = Util.null2String(recordSet1.getString("showclass"));
/*  61 */       String str19 = Util.null2String(recordSet1.getString("selecttype"));
/*  62 */       String str20 = Util.null2String(recordSet1.getString("customid"));
/*  63 */       if (!str20.equals("") && !str20.equals("0")) {
/*     */         continue;
/*     */       }
/*  66 */       String str21 = Util.null2String(recordSet1.getString("datasourceid"));
/*  67 */       int i = Util.getIntValue(recordSet1.getString("datafrom"));
/*     */       
/*  69 */       String str22 = Util.null2String(recordSet1.getString("showfield"));
/*  70 */       String str23 = Util.null2String(recordSet1.getString("parentfield"));
/*     */       
/*  72 */       str17 = str17.equals("2") ? "1" : "0";
/*  73 */       str19 = str19.equals("2") ? "1" : "0";
/*  74 */       BaseBrowser baseBrowser = new BaseBrowser();
/*  75 */       baseBrowser.setName(str6);
/*  76 */       baseBrowser.setCustomid(str20);
/*  77 */       baseBrowser.setSearch(str7);
/*  78 */       baseBrowser.setSearchById(str10);
/*  79 */       baseBrowser.setSearchByName(str11);
/*  80 */       baseBrowser.setNameHeader(str12);
/*  81 */       baseBrowser.setDescriptionHeader(str13);
/*  82 */       baseBrowser.setParentfield(str23);
/*  83 */       baseBrowser.setOutPageURL(str14);
/*  84 */       baseBrowser.setFrom(str4);
/*  85 */       baseBrowser.setHref(str15);
/*  86 */       baseBrowser.setShowname(str2);
/*  87 */       baseBrowser.setShowtree(str17);
/*  88 */       baseBrowser.setNodename(str22);
/*  89 */       baseBrowser.setParentid(str23);
/*  90 */       baseBrowser.setIsmutil(str19);
/*  91 */       baseBrowser.setDatasourceid(str21);
/*  92 */       baseBrowser.setDatafrom(i);
/*  93 */       baseBrowser.setDs(DataSourceUtil.getDataSource(str21));
/*  94 */       if (baseBrowser != null) {
/*  95 */         addCache(str2, baseBrowser);
/*     */       }
/*     */ 
/*     */ 
/*     */       
/* 100 */       String str24 = "";
/* 101 */       if (!str21.equals("")) {
/* 102 */         String str = str21.replace("datasource.", "");
/* 103 */         recordSet3.executeQuery("select type from datasourcesetting where pointid=? ", new Object[] { str });
/* 104 */         if (recordSet3.next()) {
/* 105 */           str24 = recordSet3.getString(1);
/*     */         }
/*     */       } else {
/* 108 */         ConnectionPool connectionPool = ConnectionPool.getInstance();
/* 109 */         str24 = connectionPool.getDbtype();
/*     */       } 
/* 111 */       boolean bool = PageUtil.isSupportPage(str24);
/*     */       
/* 113 */       log.info("  初始化浏览框缓存      是否支持分页 ：" + bool + "| dbtype :" + str24 + " | id:" + str1);
/* 114 */       String str25 = Util.null2String(Prop.getPropValue("integration_datashow", "physical_paging"));
/* 115 */       if (str4.equals("2") && bool && i == 1 && str16.equals("1") && str18.equals("1") && str25.equals("1")) {
/* 116 */         recordSet2.executeUpdate("update datashowset set isPhyPage='1' where id=?  and (isPhyPage='' or isPhyPage is null) ", new Object[] { str1 });
/*     */       }
/*     */       
/*     */       try {
/* 120 */         if (str4.equals("2")) {
/* 121 */           BrowserCacheManger browserCacheManger = new BrowserCacheManger();
/* 122 */           boolean bool1 = browserCacheManger.notExistTable(this.CACHE_TABLE + str1);
/* 123 */           if (bool1) {
/* 124 */             browserCacheManger.creataTable(str1);
/*     */           }
/*     */         } 
/* 127 */       } catch (Exception exception) {}
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/* 132 */     return null;
/*     */   }
/*     */ 
/*     */   
/*     */   public Object addCache(String paramString, Object paramObject) {
/* 137 */     if (paramObject == null) {
/* 138 */       log.error("Browser加入缓存异常:" + paramString);
/* 139 */       return null;
/*     */     } 
/* 141 */     log.debug("Browser加入缓存成功:" + paramString);
/*     */     
/* 143 */     return super.addCache("browser." + paramString, paramObject);
/*     */   }
/*     */ 
/*     */   
/*     */   public Object setCache(String paramString, Object paramObject) {
/* 148 */     if (paramObject == null) {
/* 149 */       log.error("Browser更新缓存异常:" + paramString);
/* 150 */       return null;
/*     */     } 
/* 152 */     log.debug("Browser更新缓存成功:" + paramString);
/*     */     
/* 154 */     return super.setCache("browser." + paramString, paramObject);
/*     */   }
/*     */ 
/*     */   
/*     */   public Object delCache(String paramString) {
/* 159 */     Object object = super.delCache("browser." + paramString);
/* 160 */     if (object != null) {
/* 161 */       log.debug("Browser删除缓存成功:" + paramString);
/*     */     }
/* 163 */     return null;
/*     */   }
/*     */ 
/*     */   
/*     */   public Object getCacheByKey(String paramString) {
/* 168 */     return super.getCacheByKey("browser." + paramString);
/*     */   }
/*     */ 
/*     */   
/*     */   public Object getObjectFromDB(String paramString) {
/* 173 */     RecordSet recordSet = new RecordSet();
/* 174 */     recordSet.executeQuery("select * from datashowset where showclass='1' and showname=?", new Object[] { paramString });
/* 175 */     while (recordSet.next()) {
/* 176 */       String str1 = Util.null2String(recordSet.getString("id"));
/* 177 */       String str2 = Util.null2String(recordSet.getString("showname"));
/* 178 */       String str3 = Util.null2String(recordSet.getString("datasourceid"));
/* 179 */       String str4 = Util.null2String(recordSet.getString("name"));
/* 180 */       String str5 = Util.null2String(recordSet.getString("sqltext"));
/* 181 */       String str6 = Util.null2String(recordSet.getString("sqltext1"));
/* 182 */       String str7 = Util.null2String(recordSet.getString("sqltext2"));
/* 183 */       String str8 = Util.null2s(recordSet.getString("searchById"), str6);
/* 184 */       String str9 = Util.null2s(recordSet.getString("searchByName"), str7);
/* 185 */       String str10 = Util.null2String(recordSet.getString("nameHeader"));
/* 186 */       String str11 = Util.null2String(recordSet.getString("descriptionHeader"));
/* 187 */       String str12 = Util.null2String(recordSet.getString("showpageurl"));
/* 188 */       String str13 = Util.null2String(recordSet.getString("detailpageurl"));
/* 189 */       String str14 = Util.null2String(recordSet.getString("browserfrom"));
/* 190 */       String str15 = Util.null2String(recordSet.getString("showtype"));
/* 191 */       String str16 = Util.null2String(recordSet.getString("selecttype"));
/* 192 */       String str17 = Util.null2String(recordSet.getString("customid"));
/* 193 */       if (!str17.equals("") && !str17.equals("0")) {
/*     */         continue;
/*     */       }
/* 196 */       String str18 = Util.null2String(recordSet.getString("datasourceid"));
/* 197 */       int i = Util.getIntValue(recordSet.getString("datafrom"));
/*     */       
/* 199 */       String str19 = Util.null2String(recordSet.getString("showfield"));
/* 200 */       String str20 = Util.null2String(recordSet.getString("parentfield"));
/*     */       
/* 202 */       str15 = str15.equals("2") ? "1" : "0";
/* 203 */       str16 = str16.equals("2") ? "1" : "0";
/* 204 */       BaseBrowser baseBrowser = new BaseBrowser();
/* 205 */       baseBrowser.setName(str4);
/* 206 */       baseBrowser.setCustomid(str17);
/* 207 */       baseBrowser.setSearch(str5);
/* 208 */       baseBrowser.setSearchById(str8);
/* 209 */       baseBrowser.setSearchByName(str9);
/* 210 */       baseBrowser.setNameHeader(str10);
/* 211 */       baseBrowser.setDescriptionHeader(str11);
/* 212 */       baseBrowser.setParentfield(str20);
/* 213 */       baseBrowser.setOutPageURL(str12);
/* 214 */       baseBrowser.setFrom(str14);
/* 215 */       baseBrowser.setHref(str13);
/* 216 */       baseBrowser.setShowname(str2);
/* 217 */       baseBrowser.setShowtree(str15);
/* 218 */       baseBrowser.setNodename(str19);
/* 219 */       baseBrowser.setParentid(str20);
/* 220 */       baseBrowser.setIsmutil(str16);
/* 221 */       baseBrowser.setDatasourceid(str18);
/* 222 */       baseBrowser.setDatafrom(i);
/* 223 */       baseBrowser.setDs(DataSourceUtil.getDataSource(str18));
/*     */       
/*     */       try {
/* 226 */         BrowserCacheManger browserCacheManger = new BrowserCacheManger();
/* 227 */         boolean bool = browserCacheManger.notExistTable(this.CACHE_TABLE + str1);
/* 228 */         if (bool) {
/* 229 */           browserCacheManger.creataTable(str1);
/*     */         }
/* 231 */       } catch (Exception exception) {}
/*     */ 
/*     */       
/* 234 */       return baseBrowser;
/*     */     } 
/*     */ 
/*     */     
/* 238 */     return null;
/*     */   }
/*     */   
/* 241 */   private static Logger log = LoggerFactory.getLogger(IntegrationCache4Browser.class);
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/interfaces/cache/impl/IntegrationCache4Browser.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */