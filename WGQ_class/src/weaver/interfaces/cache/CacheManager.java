/*     */ package weaver.interfaces.cache;
/*     */ 
/*     */ import java.util.concurrent.ConcurrentHashMap;
/*     */ import weaver.conn.ConnectionPool;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.integration.logging.Logger;
/*     */ import weaver.integration.logging.LoggerFactory;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CacheManager
/*     */ {
/*     */   @Deprecated
/*     */   public Object buildCacheConfig() {
/*  21 */     return null;
/*     */   }
/*  23 */   private static ConcurrentHashMap<String, String> cacheKeys = new ConcurrentHashMap<>();
/*     */   
/*     */   private static void initCacheKeys() {
/*  26 */     cacheKeys = new ConcurrentHashMap<>();
/*  27 */     if (ConnectionPool.getInstance().isInitiating())
/*     */       return; 
/*  29 */     RecordSet recordSet = new RecordSet();
/*  30 */     String str = "SELECT * FROM Int_Cache_Plugin ORDER BY orderNum";
/*  31 */     recordSet.executeQuery(str, new Object[0]);
/*  32 */     while (recordSet.next()) {
/*  33 */       String str1 = recordSet.getString("pluginName");
/*  34 */       String str2 = recordSet.getString("pluginClazz");
/*  35 */       cacheKeys.put(str1, str2);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static Object loadAllCache() {
/*  46 */     log.info("==============初始化所有缓存开始...");
/*  47 */     cacheKeys = new ConcurrentHashMap<>();
/*  48 */     RecordSet recordSet = new RecordSet();
/*  49 */     String str = "SELECT * FROM Int_Cache_Plugin ORDER BY orderNum";
/*  50 */     recordSet.executeQuery(str, new Object[0]);
/*  51 */     while (recordSet.next()) {
/*  52 */       String str1 = recordSet.getString("pluginName");
/*  53 */       String str2 = recordSet.getString("pluginClazz");
/*  54 */       log.info("==============初始化" + str2 + " 开始...");
/*  55 */       cacheKeys.put(str1, str2);
/*  56 */       if (!"".equals(str2)) {
/*     */         try {
/*  58 */           IntegrationCache integrationCache = (IntegrationCache)Class.forName(str2).newInstance();
/*  59 */           integrationCache.loadCache();
/*  60 */           log.info("==============初始化" + str2 + " 完成...");
/*  61 */         } catch (Exception exception) {
/*  62 */           exception.printStackTrace();
/*  63 */           log.error("=========loadCache error occured!!!" + exception.getMessage());
/*     */         } 
/*     */       }
/*     */     } 
/*     */ 
/*     */     
/*  69 */     return null;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean isCacheKey(String paramString) {
/*  78 */     if (cacheKeys == null || cacheKeys.size() == 0) {
/*  79 */       initCacheKeys();
/*     */     }
/*  81 */     String[] arrayOfString = Util.TokenizerString2(paramString, ".");
/*  82 */     if (arrayOfString.length > 1 && 
/*  83 */       cacheKeys.containsKey(arrayOfString[0])) {
/*  84 */       return true;
/*     */     }
/*     */     
/*  87 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static Object getObject(String paramString) {
/*  96 */     if (cacheKeys == null || cacheKeys.size() == 0) {
/*  97 */       initCacheKeys();
/*     */     }
/*  99 */     Object object = null;
/* 100 */     String[] arrayOfString = Util.TokenizerString2(paramString, ".");
/* 101 */     String str = "";
/* 102 */     if (arrayOfString.length > 1) {
/* 103 */       String str1 = cacheKeys.get(arrayOfString[0]);
/*     */       try {
/* 105 */         IntegrationCache integrationCache = (IntegrationCache)Class.forName(str1).newInstance();
/* 106 */         str = arrayOfString[1];
/* 107 */         object = integrationCache.getObjectFromDB(str);
/* 108 */         integrationCache.addCache(str, object);
/* 109 */       } catch (Exception exception) {
/* 110 */         exception.printStackTrace();
/* 111 */         log.error("============error occured!!!" + exception.getMessage());
/*     */       } 
/*     */     } 
/* 114 */     return object;
/*     */   }
/*     */   
/* 117 */   private static Logger log = LoggerFactory.getLogger(CacheManager.class);
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/interfaces/cache/CacheManager.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */