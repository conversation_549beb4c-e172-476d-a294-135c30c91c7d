/*     */ package weaver.fna.e9.grid;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.List;
/*     */ import weaver.fna.cache.FnaCityLevelComInfo;
/*     */ import weaver.general.Util;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class GridPopedom
/*     */ {
/*     */   public String checkboxpopedom_fnaCityLevelInner(String paramString) {
/*  30 */     return operates_popedom_fnaCityLevelInner(paramString, "").get(3);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<String> operates_popedom_fnaCityLevelInner(String paramString1, String paramString2) {
/*  40 */     ArrayList<String> arrayList = new ArrayList();
/*     */     
/*  42 */     FnaCityLevelComInfo fnaCityLevelComInfo = new FnaCityLevelComInfo();
/*  43 */     List list = fnaCityLevelComInfo.get_cityIdList_by_cityLevelId(Util.getIntValue(paramString1, 0));
/*  44 */     boolean bool = (list == null) ? false : list.size();
/*     */ 
/*     */     
/*  47 */     arrayList.add("true");
/*     */     
/*  49 */     if (bool) {
/*  50 */       if ("1".equals(paramString2)) {
/*  51 */         arrayList.add("false");
/*  52 */         arrayList.add("true");
/*     */       } else {
/*  54 */         arrayList.add("true");
/*  55 */         arrayList.add("false");
/*     */       } 
/*     */     } else {
/*  58 */       arrayList.add("false");
/*  59 */       arrayList.add("false");
/*     */     } 
/*     */     
/*  62 */     if (bool) {
/*  63 */       arrayList.add("true");
/*     */     } else {
/*  65 */       arrayList.add("false");
/*     */     } 
/*  67 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String checkboxpopedom_fnaPaymentModeInner(String paramString) {
/*  76 */     return operates_popedom_fnaPaymentModeInner(paramString, "").get(3);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<String> operates_popedom_fnaPaymentModeInner(String paramString1, String paramString2) {
/*  86 */     ArrayList<String> arrayList = new ArrayList();
/*     */     
/*  88 */     arrayList.add("true");
/*     */     
/*  90 */     if ("1".equals(paramString2)) {
/*  91 */       arrayList.add("false");
/*  92 */       arrayList.add("true");
/*     */     } else {
/*  94 */       arrayList.add("true");
/*  95 */       arrayList.add("false");
/*     */     } 
/*     */     
/*  98 */     arrayList.add("true");
/*  99 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String checkboxpopedom_fnaBusinessTravelTypeInner(String paramString) {
/* 108 */     return operates_popedom_fnaBusinessTravelTypeInner(paramString, "").get(3);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<String> operates_popedom_fnaBusinessTravelTypeInner(String paramString1, String paramString2) {
/* 118 */     ArrayList<String> arrayList = new ArrayList();
/*     */     
/* 120 */     arrayList.add("true");
/*     */     
/* 122 */     if ("1".equals(paramString2)) {
/* 123 */       arrayList.add("false");
/* 124 */       arrayList.add("true");
/*     */     } else {
/* 126 */       arrayList.add("true");
/* 127 */       arrayList.add("false");
/*     */     } 
/*     */     
/* 130 */     arrayList.add("true");
/* 131 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String checkboxpopedom_fnaTaxRateInner(String paramString) {
/* 140 */     return operates_popedom_fnaTaxRateInner(paramString, "").get(3);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<String> operates_popedom_fnaTaxRateInner(String paramString1, String paramString2) {
/* 150 */     ArrayList<String> arrayList = new ArrayList();
/*     */     
/* 152 */     arrayList.add("true");
/*     */     
/* 154 */     if ("1".equals(paramString2)) {
/* 155 */       arrayList.add("false");
/* 156 */       arrayList.add("true");
/*     */     } else {
/* 158 */       arrayList.add("true");
/* 159 */       arrayList.add("false");
/*     */     } 
/*     */     
/* 162 */     arrayList.add("true");
/* 163 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String checkboxpopedom_fnaInvoiceTypeInner(String paramString) {
/* 172 */     return operates_popedom_fnaInvoiceTypeInner(paramString, "").get(3);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<String> operates_popedom_fnaInvoiceTypeInner(String paramString1, String paramString2) {
/* 182 */     ArrayList<String> arrayList = new ArrayList();
/*     */     
/* 184 */     arrayList.add("true");
/*     */     
/* 186 */     if ("1".equals(paramString2)) {
/* 187 */       arrayList.add("false");
/* 188 */       arrayList.add("true");
/*     */     } else {
/* 190 */       arrayList.add("true");
/* 191 */       arrayList.add("false");
/*     */     } 
/*     */     
/* 194 */     arrayList.add("true");
/* 195 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String checkboxpopedom_costCategoryInner(String paramString) {
/* 204 */     return operates_popedom_costCategoryInner(paramString, "").get(3);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<String> operates_popedom_costCategoryInner(String paramString1, String paramString2) {
/* 214 */     ArrayList<String> arrayList = new ArrayList();
/*     */     
/* 216 */     arrayList.add("true");
/*     */     
/* 218 */     if ("1".equals(paramString2)) {
/* 219 */       arrayList.add("false");
/* 220 */       arrayList.add("true");
/*     */     } else {
/* 222 */       arrayList.add("true");
/* 223 */       arrayList.add("false");
/*     */     } 
/*     */     
/* 226 */     arrayList.add("true");
/* 227 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String checkboxpopedom_fnaVehicleInner(String paramString) {
/* 236 */     return operates_popedom_fnaVehicleInner(paramString, "").get(3);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<String> operates_popedom_fnaVehicleInner(String paramString1, String paramString2) {
/* 246 */     ArrayList<String> arrayList = new ArrayList();
/*     */     
/* 248 */     arrayList.add("true");
/*     */     
/* 250 */     if ("1".equals(paramString2)) {
/* 251 */       arrayList.add("false");
/* 252 */       arrayList.add("true");
/*     */     } else {
/* 254 */       arrayList.add("true");
/* 255 */       arrayList.add("false");
/*     */     } 
/*     */     
/* 258 */     arrayList.add("true");
/* 259 */     return arrayList;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/fna/e9/grid/GridPopedom.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */