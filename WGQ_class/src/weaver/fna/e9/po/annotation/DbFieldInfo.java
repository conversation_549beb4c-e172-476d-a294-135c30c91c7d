/*    */ package weaver.fna.e9.po.annotation;
/*    */ 
/*    */ import java.lang.annotation.Documented;
/*    */ import java.lang.annotation.ElementType;
/*    */ import java.lang.annotation.Retention;
/*    */ import java.lang.annotation.RetentionPolicy;
/*    */ import java.lang.annotation.Target;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ @Target({ElementType.FIELD})
/*    */ @Retention(RetentionPolicy.RUNTIME)
/*    */ @Documented
/*    */ public @interface DbFieldInfo
/*    */ {
/*    */   boolean isDbColumn() default true;
/*    */   
/*    */   boolean isDbIdentityColumn() default false;
/*    */   
/*    */   String name() default "";
/*    */   
/*    */   DbType type() default DbType.UNSET;
/*    */   
/*    */   int prec() default 0;
/*    */   
/*    */   int scale() default 0;
/*    */   
/*    */   boolean isNullable() default true;
/*    */   
/*    */   boolean primaryKey() default false;
/*    */   
/*    */   boolean foreignKey() default false;
/*    */   
/*    */   public enum DbType
/*    */   {
/* 43 */     UNSET, CHAR, VARCHAR, VARCHAR2, INT, INTEGER, DECIMAL, FLOAT, TEXT, CLOB;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/fna/e9/po/annotation/DbFieldInfo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */