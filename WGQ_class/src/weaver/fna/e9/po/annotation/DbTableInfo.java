package weaver.fna.e9.po.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface DbTableInfo {
  String name() default "";
}


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/fna/e9/po/annotation/DbTableInfo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */