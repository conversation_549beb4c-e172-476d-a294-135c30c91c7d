/*    */ package weaver.fna.e9.po.base;
/*    */ 
/*    */ import weaver.fna.general.FnaCommon;
/*    */ import weaver.general.Util;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ @Deprecated
/*    */ public class FnaPeriodDtlHelp
/*    */ {
/*    */   @Deprecated
/*    */   public String getFnaPeriodsNameInput(String paramString1, String paramString2) {
/* 28 */     String[] arrayOfString = Util.splitString(paramString2, "+");
/* 29 */     String str1 = arrayOfString[0];
/* 30 */     String str2 = arrayOfString[1];
/*    */     
/* 32 */     return "<input class=\"inputstyle\" name=\"fnaPeriodsName_" + str2 + "\" id=\"fnaPeriodsName_" + str2 + "\" value=\"" + 
/* 33 */       FnaCommon.escapeHtml(paramString1) + "\" style=\"width:80px;\" /><input class=\"inputstyle\" type=\"hidden\" name=\"fnaPeriodDtlPk_" + str2 + "\" id=\"fnaPeriodDtlPk_" + str2 + "\" value=\"" + str1 + "\" /><input class=\"inputstyle\" type=\"hidden\" name=\"fnaPeriodsList_" + str2 + "\" id=\"fnaPeriodsList_" + str2 + "\" value=\"" + str2 + "\" />";
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   @Deprecated
/*    */   public String getFnaPeriodsList(String paramString1, String paramString2) {
/* 49 */     String[] arrayOfString = Util.splitString(paramString2, "+");
/* 50 */     String str1 = arrayOfString[0];
/* 51 */     String str2 = arrayOfString[1];
/*    */     
/* 53 */     return str2 + "<input class=\"inputstyle\" type=\"hidden\" name=\"fnaPeriodsName_" + str2 + "\" id=\"fnaPeriodsName_" + str2 + "\" value=\"" + 
/*    */       
/* 55 */       FnaCommon.escapeHtml(paramString1) + "\" /><input class=\"inputstyle\" type=\"hidden\" name=\"fnaPeriodDtlPk_" + str2 + "\" id=\"fnaPeriodDtlPk_" + str2 + "\" value=\"" + str1 + "\" /><input class=\"inputstyle\" type=\"hidden\" name=\"fnaPeriodsList_" + str2 + "\" id=\"fnaPeriodsList_" + str2 + "\" value=\"" + str2 + "\" />";
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   @Deprecated
/*    */   public String getStartDate(String paramString1, String paramString2) {
/* 71 */     return "<button class=\"Calendar\" type=\"button\" id=\"startdateBtn_" + paramString2 + "\" onclick=\"onShowDate(startdateSpan_" + paramString2 + ", startdate_" + paramString2 + ")\"></button><span id=\"startdateSpan_" + paramString2 + "\">" + paramString1 + "</span><input class=\"inputstyle\" type=\"hidden\" name=\"startdate_" + paramString2 + "\" id=\"startdate_" + paramString2 + "\" value=\"" + paramString1 + "\" />";
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   @Deprecated
/*    */   public String getEndDate(String paramString1, String paramString2) {
/* 87 */     return "<button class=\"Calendar\" type=\"button\" id=\"enddateBtn_" + paramString2 + "\" onclick=\"onShowDate(enddateSpan_" + paramString2 + ", enddate_" + paramString2 + ")\"></button><span id=\"enddateSpan_" + paramString2 + "\">" + paramString1 + "</span><input class=\"inputstyle\" type=\"hidden\" name=\"enddate_" + paramString2 + "\" id=\"enddate_" + paramString2 + "\" value=\"" + paramString1 + "\" />";
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/fna/e9/po/base/FnaPeriodDtlHelp.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */