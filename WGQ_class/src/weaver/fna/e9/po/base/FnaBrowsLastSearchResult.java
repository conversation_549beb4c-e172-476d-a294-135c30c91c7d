/*     */ package weaver.fna.e9.po.base;
/*     */ 
/*     */ import weaver.fna.e9.po.annotation.DbFieldInfo;
/*     */ import weaver.fna.e9.po.annotation.DbTableInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ @Deprecated
/*     */ @DbTableInfo(name = "fnaBrowsLastSearchResult")
/*     */ public class FnaBrowsLastSearchResult
/*     */ {
/*     */   static {
/*  24 */     FnaBasePo.initStatic(FnaBrowsLastSearchResult.class);
/*     */   }
/*     */   
/*     */   @Deprecated
/*     */   @DbFieldInfo(name = "fullShowName", type = DbFieldInfo.DbType.CHAR, prec = 32, scale = 0, isNullable = true)
/*  29 */   private String fullShowName = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public String getFullShowName() {
/*  39 */     return this.fullShowName;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setFullShowName(String paramString) {
/*  48 */     this.fullShowName = paramString;
/*     */   }
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   @DbFieldInfo(name = "showName", type = DbFieldInfo.DbType.CHAR, prec = 32, scale = 0, isNullable = true)
/*  54 */   private String showName = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public String getShowName() {
/*  64 */     return this.showName;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setShowName(String paramString) {
/*  73 */     this.showName = paramString;
/*     */   }
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   @DbFieldInfo(name = "userId", type = DbFieldInfo.DbType.INT, prec = 10, scale = 0, isNullable = true)
/*  79 */   private Integer userId = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public Integer getUserId() {
/*  89 */     return this.userId;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setUserId(Integer paramInteger) {
/*  98 */     this.userId = paramInteger;
/*     */   }
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   @DbFieldInfo(name = "idPk", type = DbFieldInfo.DbType.INT, prec = 10, scale = 0, isNullable = true)
/* 104 */   private Integer idPk = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public Integer getIdPk() {
/* 114 */     return this.idPk;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setIdPk(Integer paramInteger) {
/* 123 */     this.idPk = paramInteger;
/*     */   }
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   @DbFieldInfo(name = "charPk", type = DbFieldInfo.DbType.CHAR, prec = 32, scale = 0, isNullable = true)
/* 129 */   private String charPk = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public String getCharPk() {
/* 139 */     return this.charPk;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setCharPk(String paramString) {
/* 148 */     this.charPk = paramString;
/*     */   }
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   @DbFieldInfo(name = "idFk", type = DbFieldInfo.DbType.INT, prec = 10, scale = 0, isNullable = true)
/* 154 */   private Integer idFk = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public Integer getIdFk() {
/* 164 */     return this.idFk;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setIdFk(Integer paramInteger) {
/* 173 */     this.idFk = paramInteger;
/*     */   }
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   @DbFieldInfo(name = "charFk", type = DbFieldInfo.DbType.CHAR, prec = 32, scale = 0, isNullable = true)
/* 179 */   private String charFk = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public String getCharFk() {
/* 189 */     return this.charFk;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setCharFk(String paramString) {
/* 198 */     this.charFk = paramString;
/*     */   }
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   @DbFieldInfo(name = "browserType", type = DbFieldInfo.DbType.VARCHAR, prec = 100, scale = 0, isNullable = true)
/* 204 */   private String browserType = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public String getBrowserType() {
/* 214 */     return this.browserType;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setBrowserType(String paramString) {
/* 223 */     this.browserType = paramString;
/*     */   }
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   @DbFieldInfo(name = "showOrder", type = DbFieldInfo.DbType.DECIMAL, prec = 8, scale = 3, isNullable = true)
/* 229 */   private Double showOrder = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public Double getShowOrder() {
/* 239 */     return this.showOrder;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setShowOrder(Double paramDouble) {
/* 248 */     this.showOrder = paramDouble;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/fna/e9/po/base/FnaBrowsLastSearchResult.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */