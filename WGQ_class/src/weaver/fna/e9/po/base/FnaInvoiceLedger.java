/*     */ package weaver.fna.e9.po.base;
/*     */ 
/*     */ import weaver.fna.e9.po.annotation.DbFieldInfo;
/*     */ import weaver.fna.e9.po.annotation.DbTableInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ @DbTableInfo(name = "fnaInvoiceLedger")
/*     */ public class FnaInvoiceLedger
/*     */ {
/*     */   static {
/*  23 */     FnaBasePo.initStatic(FnaInvoiceLedger.class);
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   @DbFieldInfo(name = "id", type = DbFieldInfo.DbType.INT, prec = 10, scale = 0, isNullable = false, isDbIdentityColumn = true, primaryKey = true)
/*  29 */   private Integer id = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Integer getId() {
/*  36 */     return this.id;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setId(Integer paramInteger) {
/*  43 */     this.id = paramInteger;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   @DbFieldInfo(name = "billingDate", type = DbFieldInfo.DbType.CHAR, prec = 10, scale = 0, isNullable = false)
/*  49 */   private String billingDate = null;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @DbFieldInfo(name = "invoiceCode", type = DbFieldInfo.DbType.VARCHAR, prec = 60, scale = 0, isNullable = false)
/*  55 */   private String invoiceCode = null;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @DbFieldInfo(name = "invoiceNumber", type = DbFieldInfo.DbType.VARCHAR, prec = 60, scale = 0, isNullable = false)
/*  61 */   private String invoiceNumber = null;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @DbFieldInfo(name = "invoiceType", type = DbFieldInfo.DbType.INT, prec = 10, scale = 0, isNullable = false)
/*  67 */   private Integer invoiceType = null;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @DbFieldInfo(name = "seller", type = DbFieldInfo.DbType.VARCHAR, prec = 1500, scale = 0, isNullable = false)
/*  73 */   private String seller = null;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @DbFieldInfo(name = "purchaser", type = DbFieldInfo.DbType.VARCHAR, prec = 1500, scale = 0, isNullable = false)
/*  79 */   private String purchaser = null;
/*     */ 
/*     */ 
/*     */   
/*     */   @DbFieldInfo(name = "purchaserTaxNo", type = DbFieldInfo.DbType.VARCHAR, prec = 1500, scale = 0, isNullable = false)
/*  84 */   private String purchaserTaxNo = null;
/*     */ 
/*     */ 
/*     */   
/*     */   @DbFieldInfo(name = "salesTaxNo", type = DbFieldInfo.DbType.VARCHAR, prec = 1500, scale = 0, isNullable = false)
/*  89 */   private String salesTaxNo = null;
/*     */ 
/*     */ 
/*     */   
/*     */   @DbFieldInfo(name = "invoiceServiceYype", type = DbFieldInfo.DbType.VARCHAR, prec = 1500, scale = 0, isNullable = false)
/*  94 */   private String invoiceServiceYype = null;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @DbFieldInfo(name = "priceWithoutTax", type = DbFieldInfo.DbType.DECIMAL, prec = 20, scale = 2, isNullable = true)
/* 100 */   private Double priceWithoutTax = null;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @DbFieldInfo(name = "taxRate", type = DbFieldInfo.DbType.DECIMAL, prec = 8, scale = 2, isNullable = true)
/* 106 */   private Double taxRate = null;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @DbFieldInfo(name = "tax", type = DbFieldInfo.DbType.DECIMAL, prec = 20, scale = 2, isNullable = true)
/* 112 */   private Double tax = null;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @DbFieldInfo(name = "taxIncludedPrice", type = DbFieldInfo.DbType.DECIMAL, prec = 20, scale = 2, isNullable = true)
/* 118 */   private Double taxIncludedPrice = null;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @DbFieldInfo(name = "company_seal", type = DbFieldInfo.DbType.INT, prec = 10, scale = 0, isNullable = true)
/* 124 */   private Integer company_seal = null;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @DbFieldInfo(name = "form_type", type = DbFieldInfo.DbType.VARCHAR, prec = 1500, scale = 0, isNullable = false)
/* 130 */   private String form_type = null;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @DbFieldInfo(name = "form_name", type = DbFieldInfo.DbType.VARCHAR, prec = 1500, scale = 0, isNullable = false)
/* 136 */   private String form_name = null;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @DbFieldInfo(name = "kind", type = DbFieldInfo.DbType.VARCHAR, prec = 1500, scale = 0, isNullable = false)
/* 142 */   private String kind = null;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @DbFieldInfo(name = "ciphertext", type = DbFieldInfo.DbType.VARCHAR, prec = 1500, scale = 0, isNullable = false)
/* 148 */   private String ciphertext = null;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @DbFieldInfo(name = "category", type = DbFieldInfo.DbType.VARCHAR, prec = 1500, scale = 0, isNullable = false)
/* 154 */   private String category = null;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @DbFieldInfo(name = "authenticity", type = DbFieldInfo.DbType.INT, prec = 10, scale = 0, isNullable = true)
/* 160 */   private Integer authenticity = null;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @DbFieldInfo(name = "checkStatus", type = DbFieldInfo.DbType.INT, prec = 10, scale = 0, isNullable = true)
/* 166 */   private Integer checkStatus = null;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @DbFieldInfo(name = "requestId", type = DbFieldInfo.DbType.INT, prec = 10, scale = 0, isNullable = true)
/* 172 */   private Integer requestId = null;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @DbFieldInfo(name = "reimbursementDate", type = DbFieldInfo.DbType.CHAR, prec = 10, scale = 0, isNullable = false)
/* 178 */   private String reimbursementDate = null;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @DbFieldInfo(name = "reimbursePerson", type = DbFieldInfo.DbType.INT, prec = 10, scale = 0, isNullable = true)
/* 184 */   private Integer reimbursePerson = null;
/*     */ 
/*     */ 
/*     */   
/*     */   @DbFieldInfo(name = "userid_new", type = DbFieldInfo.DbType.INT, prec = 10, scale = 0, isNullable = true)
/* 189 */   private Integer userid_new = null;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @DbFieldInfo(name = "entryTime", type = DbFieldInfo.DbType.INT, prec = 10, scale = 0, isNullable = true)
/* 195 */   private String entryTime = null;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @DbFieldInfo(name = "invoiceSource_new", type = DbFieldInfo.DbType.INT, prec = 10, scale = 0, isNullable = true)
/* 201 */   private Integer invoiceSource_new = null;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @DbFieldInfo(name = "checkCode", type = DbFieldInfo.DbType.VARCHAR, prec = 20, scale = 0, isNullable = false)
/* 207 */   private String checkCode = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCheckCode() {
/* 215 */     return this.checkCode;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setCheckCode(String paramString) {
/* 222 */     this.checkCode = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getReimbursementDate() {
/* 229 */     return this.reimbursementDate;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setReimbursementDate(String paramString) {
/* 237 */     this.reimbursementDate = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Integer getReimbursePerson() {
/* 244 */     return this.reimbursePerson;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setReimbursePerson(Integer paramInteger) {
/* 251 */     this.reimbursePerson = paramInteger;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Integer getUserid_new() {
/* 258 */     return this.userid_new;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setUserid_new(Integer paramInteger) {
/* 265 */     this.userid_new = paramInteger;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getEntryTime() {
/* 273 */     return this.entryTime;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setEntryTime(String paramString) {
/* 280 */     this.entryTime = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Integer getInvoiceSource_new() {
/* 289 */     return this.invoiceSource_new;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setInvoiceSource_new(Integer paramInteger) {
/* 296 */     this.invoiceSource_new = paramInteger;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getBillingDate() {
/* 303 */     return this.billingDate;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setBillingDate(String paramString) {
/* 310 */     this.billingDate = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getInvoiceCode() {
/* 317 */     return this.invoiceCode;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setInvoiceCode(String paramString) {
/* 324 */     this.invoiceCode = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getInvoiceNumber() {
/* 331 */     return this.invoiceNumber;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setInvoiceNumber(String paramString) {
/* 338 */     this.invoiceNumber = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Integer getInvoiceType() {
/* 345 */     return this.invoiceType;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setInvoiceType(Integer paramInteger) {
/* 352 */     this.invoiceType = paramInteger;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getSeller() {
/* 359 */     return this.seller;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setSeller(String paramString) {
/* 366 */     this.seller = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getPurchaser() {
/* 373 */     return this.purchaser;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setPurchaser(String paramString) {
/* 380 */     this.purchaser = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getPurchaserTaxNo() {
/* 387 */     return this.purchaserTaxNo;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setPurchaserTaxNo(String paramString) {
/* 394 */     this.purchaserTaxNo = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getSalesTaxNo() {
/* 402 */     return this.salesTaxNo;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setSalesTaxNo(String paramString) {
/* 409 */     this.salesTaxNo = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getInvoiceServiceYype() {
/* 416 */     return this.invoiceServiceYype;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setInvoiceServiceYype(String paramString) {
/* 423 */     this.invoiceServiceYype = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Double getPriceWithoutTax() {
/* 430 */     return this.priceWithoutTax;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setPriceWithoutTax(Double paramDouble) {
/* 437 */     this.priceWithoutTax = paramDouble;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Double getTaxRate() {
/* 444 */     return this.taxRate;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setTaxRate(Double paramDouble) {
/* 451 */     this.taxRate = paramDouble;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Double getTax() {
/* 458 */     return this.tax;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setTax(Double paramDouble) {
/* 465 */     this.tax = paramDouble;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Double getTaxIncludedPrice() {
/* 472 */     return this.taxIncludedPrice;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setTaxIncludedPrice(Double paramDouble) {
/* 479 */     this.taxIncludedPrice = paramDouble;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Integer getCompany_seal() {
/* 486 */     return this.company_seal;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setCompany_seal(Integer paramInteger) {
/* 494 */     this.company_seal = paramInteger;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getForm_type() {
/* 501 */     return this.form_type;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setForm_type(String paramString) {
/* 509 */     this.form_type = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getForm_name() {
/* 516 */     return this.form_name;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setForm_name(String paramString) {
/* 524 */     this.form_name = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getKind() {
/* 531 */     return this.kind;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setKind(String paramString) {
/* 538 */     this.kind = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCiphertext() {
/* 544 */     return this.ciphertext;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setCiphertext(String paramString) {
/* 551 */     this.ciphertext = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCategory() {
/* 557 */     return this.category;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setCategory(String paramString) {
/* 564 */     this.category = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public Integer getAuthenticity() {
/* 570 */     return this.authenticity;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setAuthenticity(Integer paramInteger) {
/* 577 */     this.authenticity = paramInteger;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Integer getCheckStatus() {
/* 585 */     return this.checkStatus;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setCheckStatus(Integer paramInteger) {
/* 592 */     this.checkStatus = paramInteger;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Integer getRequestId() {
/* 601 */     return this.requestId;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setRequestId(Integer paramInteger) {
/* 608 */     this.requestId = paramInteger;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/fna/e9/po/base/FnaInvoiceLedger.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */