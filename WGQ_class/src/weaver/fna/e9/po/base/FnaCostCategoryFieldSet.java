/*     */ package weaver.fna.e9.po.base;
/*     */ 
/*     */ import weaver.fna.e9.po.annotation.DbFieldInfo;
/*     */ import weaver.fna.e9.po.annotation.DbTableInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ @DbTableInfo(name = "fnaCostCategoryFieldSet")
/*     */ public class FnaCostCategoryFieldSet
/*     */ {
/*     */   static {
/*  23 */     FnaBasePo.initStatic(FnaCostCategoryFieldSet.class);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @DbFieldInfo(name = "id", type = DbFieldInfo.DbType.INT, prec = 10, scale = 0, isNullable = false, primaryKey = true, isDbIdentityColumn = true)
/*  30 */   private Integer id = Integer.valueOf(0);
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @DbFieldInfo(name = "fnaCostCategoryId", type = DbFieldInfo.DbType.INT, prec = 10, scale = 0, isNullable = false)
/*  36 */   private Integer fnaCostCategoryId = Integer.valueOf(0);
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @DbFieldInfo(name = "workflowId", type = DbFieldInfo.DbType.INT, prec = 10, scale = 0, isNullable = false)
/*  42 */   private Integer workflowId = Integer.valueOf(0);
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @DbFieldInfo(name = "fieldId", type = DbFieldInfo.DbType.INT, prec = 10, scale = 0, isNullable = false)
/*  48 */   private Integer fieldId = Integer.valueOf(0);
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @DbFieldInfo(name = "isDtl", type = DbFieldInfo.DbType.INT, prec = 10, scale = 0, isNullable = false)
/*  54 */   private Integer isDtl = Integer.valueOf(0);
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @DbFieldInfo(name = "dtlNumber", type = DbFieldInfo.DbType.INT, prec = 10, scale = 0, isNullable = false)
/*  60 */   private Integer dtlNumber = Integer.valueOf(0);
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @DbFieldInfo(name = "type", type = DbFieldInfo.DbType.INT, prec = 10, scale = 0, isNullable = false)
/*  66 */   private Integer type = Integer.valueOf(0);
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @DbFieldInfo(name = "fieldhtmltype", type = DbFieldInfo.DbType.INT, prec = 10, scale = 0, isNullable = false)
/*  72 */   private Integer fieldhtmltype = Integer.valueOf(0);
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @DbFieldInfo(name = "isRequired", type = DbFieldInfo.DbType.INT, prec = 10, scale = 0, isNullable = false)
/*  78 */   private Integer isRequired = Integer.valueOf(0);
/*     */ 
/*     */ 
/*     */   
/*     */   @DbFieldInfo(name = "name", type = DbFieldInfo.DbType.VARCHAR, prec = 200, scale = 0, isNullable = false)
/*  83 */   private String name = "";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @DbFieldInfo(name = "showOrder", type = DbFieldInfo.DbType.DECIMAL, prec = 6, scale = 3, isNullable = true)
/*  90 */   private Double showOrder = Double.valueOf(0.0D);
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Integer getFnaCostCategoryId() {
/*  96 */     return this.fnaCostCategoryId;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setFnaCostCategoryId(Integer paramInteger) {
/* 103 */     this.fnaCostCategoryId = paramInteger;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Integer getWorkflowId() {
/* 110 */     return this.workflowId;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setWorkflowId(Integer paramInteger) {
/* 117 */     this.workflowId = paramInteger;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Integer getFieldId() {
/* 124 */     return this.fieldId;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setFieldId(Integer paramInteger) {
/* 131 */     this.fieldId = paramInteger;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Integer getIsDtl() {
/* 138 */     return this.isDtl;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setIsDtl(Integer paramInteger) {
/* 145 */     this.isDtl = paramInteger;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Integer getDtlNumber() {
/* 152 */     return this.dtlNumber;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setDtlNumber(Integer paramInteger) {
/* 159 */     this.dtlNumber = paramInteger;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Integer getType() {
/* 166 */     return this.type;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setType(Integer paramInteger) {
/* 173 */     this.type = paramInteger;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Integer getFieldhtmltype() {
/* 180 */     return this.fieldhtmltype;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setFieldhtmltype(Integer paramInteger) {
/* 187 */     this.fieldhtmltype = paramInteger;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Integer getIsRequired() {
/* 194 */     return this.isRequired;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setIsRequired(Integer paramInteger) {
/* 201 */     this.isRequired = paramInteger;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getName() {
/* 208 */     return this.name;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setName(String paramString) {
/* 215 */     this.name = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Double getShowOrder() {
/* 222 */     return this.showOrder;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setShowOrder(Double paramDouble) {
/* 229 */     this.showOrder = paramDouble;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Integer getId() {
/* 236 */     return this.id;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setId(Integer paramInteger) {
/* 243 */     this.id = paramInteger;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/fna/e9/po/base/FnaCostCategoryFieldSet.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */