/*     */ package weaver.fna.e9.po.base;
/*     */ 
/*     */ import java.util.List;
/*     */ import weaver.fna.e9.po.annotation.DbFieldInfo;
/*     */ import weaver.fna.e9.po.annotation.DbTableInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ @Deprecated
/*     */ @DbTableInfo(name = "fnaPeriod")
/*     */ public class FnaPeriod
/*     */ {
/*     */   static {
/*  25 */     FnaBasePo.initStatic(FnaPeriod.class);
/*     */   }
/*     */   
/*     */   @Deprecated
/*     */   @DbFieldInfo(name = "id", type = DbFieldInfo.DbType.INT, prec = 10, scale = 0, isNullable = false, isDbIdentityColumn = true)
/*  30 */   private Integer id = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public Integer getId() {
/*  39 */     return this.id;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setId(Integer paramInteger) {
/*  47 */     this.id = paramInteger;
/*     */   }
/*     */   
/*     */   @Deprecated
/*     */   @DbFieldInfo(name = "fnaPeriodPk", type = DbFieldInfo.DbType.CHAR, prec = 32, scale = 0, isNullable = false, primaryKey = true)
/*  52 */   private String fnaPeriodPk = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public String getFnaPeriodPk() {
/*  61 */     return this.fnaPeriodPk;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setFnaPeriodPk(String paramString) {
/*  69 */     this.fnaPeriodPk = paramString;
/*     */   }
/*     */   
/*     */   @Deprecated
/*     */   @DbFieldInfo(name = "fnaPeriodName", type = DbFieldInfo.DbType.CHAR, prec = 50, scale = 0, isNullable = false)
/*  74 */   private String fnaPeriodName = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public String getFnaPeriodName() {
/*  83 */     return this.fnaPeriodName;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setFnaPeriodName(String paramString) {
/*  91 */     this.fnaPeriodName = paramString;
/*     */   }
/*     */   
/*     */   @Deprecated
/*     */   @DbFieldInfo(name = "fnaCycle", type = DbFieldInfo.DbType.INT, prec = 10, scale = 0, isNullable = false)
/*  96 */   private Integer fnaCycle = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public Integer getFnaCycle() {
/* 105 */     return this.fnaCycle;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setFnaCycle(Integer paramInteger) {
/* 113 */     this.fnaCycle = paramInteger;
/*     */   }
/*     */   
/*     */   @Deprecated
/*     */   @DbFieldInfo(name = "startdate", type = DbFieldInfo.DbType.CHAR, prec = 10, scale = 0, isNullable = true)
/* 118 */   private String startdate = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public String getStartdate() {
/* 127 */     return this.startdate;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setStartdate(String paramString) {
/* 135 */     this.startdate = paramString;
/*     */   }
/*     */   
/*     */   @Deprecated
/*     */   @DbFieldInfo(name = "enddate", type = DbFieldInfo.DbType.CHAR, prec = 10, scale = 0, isNullable = true)
/* 140 */   private String enddate = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public String getEnddate() {
/* 149 */     return this.enddate;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setEnddate(String paramString) {
/* 157 */     this.enddate = paramString;
/*     */   }
/*     */   
/*     */   @Deprecated
/*     */   @DbFieldInfo(name = "status", type = DbFieldInfo.DbType.INT, prec = 10, scale = 0, isNullable = false)
/* 162 */   private Integer status = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public Integer getStatus() {
/* 171 */     return this.status;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setStatus(Integer paramInteger) {
/* 179 */     this.status = paramInteger;
/*     */   }
/*     */   
/*     */   @Deprecated
/*     */   @DbFieldInfo(name = "showOrder", type = DbFieldInfo.DbType.DECIMAL, prec = 6, scale = 3, isNullable = true)
/* 184 */   private Double showOrder = null;
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   private List<FnaPeriodDtl> fnaPeriodDtlList;
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public Double getShowOrder() {
/* 193 */     return this.showOrder;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setShowOrder(Double paramDouble) {
/* 201 */     this.showOrder = paramDouble;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public FnaPeriod() {
/* 228 */     this.fnaPeriodDtlList = null; } @Deprecated public FnaPeriod(Integer paramInteger1, String paramString1, String paramString2, Integer paramInteger2, Integer paramInteger3) { this.fnaPeriodDtlList = null;
/*     */     this.id = paramInteger1;
/*     */     this.fnaPeriodPk = paramString1;
/*     */     this.fnaPeriodName = paramString2;
/*     */     this.fnaCycle = paramInteger2;
/*     */     this.status = paramInteger3; }
/*     */    @Deprecated
/*     */   public List<FnaPeriodDtl> getFnaPeriodDtlList() {
/* 236 */     return this.fnaPeriodDtlList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setFnaPeriodDtl(List<FnaPeriodDtl> paramList) {
/* 244 */     this.fnaPeriodDtlList = paramList;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/fna/e9/po/base/FnaPeriod.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */