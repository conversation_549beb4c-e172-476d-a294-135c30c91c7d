/*     */ package weaver.fna.e9.po.base;
/*     */ 
/*     */ import weaver.fna.e9.po.annotation.DbFieldInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public final class FnaBasePoField
/*     */ {
/*     */   private String javaFieldName;
/*     */   private String dbColName;
/*     */   private boolean isDbColumn;
/*     */   private boolean isDbIdentityColumn;
/*     */   private DbFieldInfo.DbType type;
/*     */   private int prec;
/*     */   private int scale;
/*     */   private boolean isNullable;
/*     */   private boolean primaryKey;
/*     */   private boolean foreignKey;
/*     */   private String javaType;
/*     */   
/*     */   public String getDbColName() {
/*  71 */     return this.dbColName;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setDbColName(String paramString) {
/*  78 */     this.dbColName = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getJavaType() {
/*  85 */     return this.javaType;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setJavaType(String paramString) {
/*  92 */     this.javaType = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean isDbColumn() {
/*  99 */     return this.isDbColumn;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setDbColumn(boolean paramBoolean) {
/* 106 */     this.isDbColumn = paramBoolean;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean isDbIdentityColumn() {
/* 113 */     return this.isDbIdentityColumn;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setDbIdentityColumn(boolean paramBoolean) {
/* 120 */     this.isDbIdentityColumn = paramBoolean;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getJavaFieldName() {
/* 127 */     return this.javaFieldName;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setJavaFieldName(String paramString) {
/* 134 */     this.javaFieldName = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public DbFieldInfo.DbType getType() {
/* 141 */     return this.type;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setType(DbFieldInfo.DbType paramDbType) {
/* 148 */     this.type = paramDbType;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getPrec() {
/* 155 */     return this.prec;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setPrec(int paramInt) {
/* 162 */     this.prec = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getScale() {
/* 169 */     return this.scale;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setScale(int paramInt) {
/* 176 */     this.scale = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean isNullable() {
/* 183 */     return this.isNullable;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setNullable(boolean paramBoolean) {
/* 190 */     this.isNullable = paramBoolean;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean isPrimaryKey() {
/* 197 */     return this.primaryKey;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setPrimaryKey(boolean paramBoolean) {
/* 204 */     this.primaryKey = paramBoolean;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean isForeignKey() {
/* 211 */     return this.foreignKey;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setForeignKey(boolean paramBoolean) {
/* 218 */     this.foreignKey = paramBoolean;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/fna/e9/po/base/FnaBasePoField.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */