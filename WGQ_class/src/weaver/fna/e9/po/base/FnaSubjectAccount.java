/*     */ package weaver.fna.e9.po.base;
/*     */ 
/*     */ import java.util.List;
/*     */ import weaver.fna.e9.po.annotation.DbFieldInfo;
/*     */ import weaver.fna.e9.po.annotation.DbTableInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ @Deprecated
/*     */ @DbTableInfo(name = "fnaSubjectAccount")
/*     */ public class FnaSubjectAccount
/*     */ {
/*     */   static {
/*  25 */     FnaBasePo.initStatic(FnaSubjectAccount.class);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public FnaSubjectAccount(Integer paramInteger1, String paramString1, String paramString2, String paramString3, String paramString4, String paramString5, Integer paramInteger2, String paramString6, Integer paramInteger3, Integer paramInteger4, Double paramDouble) {
/*  50 */     this.id = paramInteger1;
/*  51 */     this.fnaSubjectaPk = paramString1;
/*  52 */     this.fnaPeriodPk = paramString2;
/*  53 */     this.supFnaSubjectaPk = paramString3;
/*  54 */     this.subjectaName = paramString4;
/*  55 */     this.subjectaCode = paramString5;
/*  56 */     this.lockedStatus = paramInteger2;
/*  57 */     this.description = paramString6;
/*  58 */     this.subjectaIsLeaf = paramInteger3;
/*  59 */     this.subjectaLevel = paramInteger4;
/*  60 */     this.showOrder = paramDouble;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public final FnaSubjectAccount copy() {
/*  68 */     FnaSubjectAccount fnaSubjectAccount = new FnaSubjectAccount();
/*     */     
/*  70 */     fnaSubjectAccount.setId(new Integer(this.id.intValue()));
/*  71 */     fnaSubjectAccount.setFnaSubjectaPk(this.fnaSubjectaPk);
/*  72 */     fnaSubjectAccount.setFnaPeriodPk(this.fnaPeriodPk);
/*  73 */     fnaSubjectAccount.setSupFnaSubjectaPk(this.supFnaSubjectaPk);
/*  74 */     fnaSubjectAccount.setSubjectaName(this.subjectaName);
/*  75 */     fnaSubjectAccount.setSubjectaCode(this.subjectaCode);
/*  76 */     fnaSubjectAccount.setLockedStatus(new Integer(this.lockedStatus.intValue()));
/*  77 */     fnaSubjectAccount.setDescription(this.description);
/*  78 */     fnaSubjectAccount.setSubjectaIsLeaf(new Integer(this.subjectaIsLeaf.intValue()));
/*  79 */     fnaSubjectAccount.setSubjectaLevel(new Integer(this.subjectaLevel.intValue()));
/*  80 */     fnaSubjectAccount.setShowOrder(new Double(this.showOrder.doubleValue()));
/*     */     
/*  82 */     return fnaSubjectAccount;
/*     */   }
/*     */   
/*     */   @Deprecated
/*     */   @DbFieldInfo(name = "id", type = DbFieldInfo.DbType.INT, prec = 10, scale = 0, isNullable = false, isDbIdentityColumn = true)
/*  87 */   private Integer id = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public Integer getId() {
/*  96 */     return this.id;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setId(Integer paramInteger) {
/* 104 */     this.id = paramInteger;
/*     */   }
/*     */   
/*     */   @Deprecated
/*     */   @DbFieldInfo(name = "fnaSubjectaPk", type = DbFieldInfo.DbType.CHAR, prec = 32, scale = 0, isNullable = false, primaryKey = true)
/* 109 */   private String fnaSubjectaPk = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public String getFnaSubjectaPk() {
/* 118 */     return this.fnaSubjectaPk;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setFnaSubjectaPk(String paramString) {
/* 126 */     this.fnaSubjectaPk = paramString;
/*     */   }
/*     */   
/*     */   @Deprecated
/*     */   @DbFieldInfo(name = "fnaPeriodPk", type = DbFieldInfo.DbType.CHAR, prec = 32, scale = 0, isNullable = false, foreignKey = true)
/* 131 */   private String fnaPeriodPk = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public String getFnaPeriodPk() {
/* 140 */     return this.fnaPeriodPk;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setFnaPeriodPk(String paramString) {
/* 148 */     this.fnaPeriodPk = paramString;
/*     */   }
/*     */   
/*     */   @Deprecated
/*     */   @DbFieldInfo(name = "supFnaSubjectaPk", type = DbFieldInfo.DbType.CHAR, prec = 32, scale = 0, isNullable = true, foreignKey = true)
/* 153 */   private String supFnaSubjectaPk = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public String getSupFnaSubjectaPk() {
/* 162 */     return this.supFnaSubjectaPk;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setSupFnaSubjectaPk(String paramString) {
/* 170 */     this.supFnaSubjectaPk = paramString;
/*     */   }
/*     */   
/*     */   @Deprecated
/*     */   @DbFieldInfo(name = "subjectaName", type = DbFieldInfo.DbType.VARCHAR, prec = 600, scale = 0, isNullable = false)
/* 175 */   private String subjectaName = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public String getSubjectaName() {
/* 184 */     return this.subjectaName;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setSubjectaName(String paramString) {
/* 192 */     this.subjectaName = paramString;
/*     */   }
/*     */   
/*     */   @Deprecated
/*     */   @DbFieldInfo(name = "subjectaCode", type = DbFieldInfo.DbType.VARCHAR, prec = 600, scale = 0, isNullable = false)
/* 197 */   private String subjectaCode = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public String getSubjectaCode() {
/* 206 */     return this.subjectaCode;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setSubjectaCode(String paramString) {
/* 214 */     this.subjectaCode = paramString;
/*     */   }
/*     */   
/*     */   @Deprecated
/*     */   @DbFieldInfo(name = "lockedStatus", type = DbFieldInfo.DbType.INT, prec = 10, scale = 0, isNullable = false)
/* 219 */   private Integer lockedStatus = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public Integer getLockedStatus() {
/* 228 */     return this.lockedStatus;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setLockedStatus(Integer paramInteger) {
/* 236 */     this.lockedStatus = paramInteger;
/*     */   }
/*     */   
/*     */   @Deprecated
/*     */   @DbFieldInfo(name = "description", type = DbFieldInfo.DbType.TEXT, prec = 0, scale = 0, isNullable = true)
/* 241 */   private String description = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public String getDescription() {
/* 250 */     return this.description;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setDescription(String paramString) {
/* 258 */     this.description = paramString;
/*     */   }
/*     */   
/*     */   @Deprecated
/*     */   @DbFieldInfo(name = "showOrder", type = DbFieldInfo.DbType.DECIMAL, prec = 6, scale = 3, isNullable = true)
/* 263 */   private Double showOrder = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public Double getShowOrder() {
/* 272 */     return this.showOrder;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setShowOrder(Double paramDouble) {
/* 280 */     this.showOrder = paramDouble;
/*     */   }
/*     */   
/*     */   @Deprecated
/*     */   @DbFieldInfo(name = "subjectaIsLeaf", type = DbFieldInfo.DbType.INT, prec = 10, scale = 0, isNullable = false, isDbIdentityColumn = true)
/* 285 */   private Integer subjectaIsLeaf = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public Integer getSubjectaIsLeaf() {
/* 294 */     return this.subjectaIsLeaf;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setSubjectaIsLeaf(Integer paramInteger) {
/* 302 */     this.subjectaIsLeaf = paramInteger;
/*     */   }
/*     */   
/*     */   @Deprecated
/*     */   @DbFieldInfo(name = "subjectaLevel", type = DbFieldInfo.DbType.INT, prec = 10, scale = 0, isNullable = false, isDbIdentityColumn = true)
/* 307 */   private Integer subjectaLevel = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public Integer getSubjectaLevel() {
/* 316 */     return this.subjectaLevel;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setSubjectaLevel(Integer paramInteger) {
/* 324 */     this.subjectaLevel = paramInteger;
/*     */   }
/*     */ 
/*     */   
/*     */   @Deprecated
/* 329 */   private List<FnaSubjectAccount> fnaSubjectAccountList = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public List<FnaSubjectAccount> getFnaSubjectAccountList() {
/* 337 */     return this.fnaSubjectAccountList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setFnaSubjectAccount(List<FnaSubjectAccount> paramList) {
/* 345 */     this.fnaSubjectAccountList = paramList;
/*     */   }
/*     */ 
/*     */   
/*     */   @Deprecated
/* 350 */   private List<FnaSubjectBudget> fnaSubjectBudgetList = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public List<FnaSubjectBudget> getFnaSubjectBudgetList() {
/* 358 */     return this.fnaSubjectBudgetList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setFnaSubjectBudget(List<FnaSubjectBudget> paramList) {
/* 366 */     this.fnaSubjectBudgetList = paramList;
/*     */   }
/*     */   
/*     */   @Deprecated
/*     */   public FnaSubjectAccount() {}
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/fna/e9/po/base/FnaSubjectAccount.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */