/*     */ package weaver.fna.e9.po.base;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.List;
/*     */ import org.apache.commons.lang.StringEscapeUtils;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.conn.RecordSetTrans;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ @Deprecated
/*     */ public class FnaSubjectBudgetHelp
/*     */ {
/*     */   @Deprecated
/*     */   public List<String> getFnaSubjectBudgetGrid_popedom(String paramString1, String paramString2) throws Exception {
/*  37 */     String[] arrayOfString = Util.splitString(paramString2, "+");
/*  38 */     int i = arrayOfString.length;
/*  39 */     int j = 0;
/*  40 */     int k = 7;
/*  41 */     if (i >= 1) {
/*  42 */       j = Util.getIntValue(arrayOfString[0]);
/*     */     }
/*  44 */     if (i >= 2) {
/*  45 */       k = Util.getIntValue(arrayOfString[1], 7);
/*     */     }
/*     */     
/*  48 */     ArrayList<String> arrayList = new ArrayList();
/*     */     
/*  50 */     arrayList.add("true");
/*     */     
/*  52 */     StringBuffer stringBuffer = new StringBuffer();
/*  53 */     boolean bool = checkCanDelete(null, paramString1, k, stringBuffer);
/*  54 */     if (bool) {
/*  55 */       arrayList.add("true");
/*     */     } else {
/*  57 */       arrayList.add("false");
/*     */     } 
/*  59 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public boolean checkCanDelete(RecordSetTrans paramRecordSetTrans, String paramString, int paramInt, StringBuffer paramStringBuffer) throws Exception {
/*  74 */     return checkCanDelete(paramRecordSetTrans, paramString, true, paramInt, paramStringBuffer);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public boolean checkCanDelete(RecordSetTrans paramRecordSetTrans, String paramString, boolean paramBoolean, int paramInt, StringBuffer paramStringBuffer) throws Exception {
/*  90 */     RecordSet recordSet = null;
/*  91 */     if (paramRecordSetTrans == null) {
/*  92 */       recordSet = new RecordSet();
/*     */     }
/*  94 */     String str = "";
/*  95 */     boolean bool1 = true;
/*  96 */     if (paramBoolean) {
/*  97 */       str = "select count(*) cnt from fnaSubjectBudget a where a.supFnaSubjectbPk = '" + StringEscapeUtils.escapeSql(paramString) + "'";
/*  98 */       if (paramRecordSetTrans == null) {
/*  99 */         recordSet.executeSql(str);
/* 100 */         if (recordSet.next()) {
/* 101 */           bool1 = (recordSet.getInt("cnt") == 0) ? true : false;
/*     */         }
/*     */       } else {
/* 104 */         paramRecordSetTrans.executeSql(str);
/* 105 */         if (paramRecordSetTrans.next()) {
/* 106 */           bool1 = (paramRecordSetTrans.getInt("cnt") == 0) ? true : false;
/*     */         }
/*     */       } 
/*     */     } 
/* 110 */     boolean bool2 = bool1;
/* 111 */     if (!bool1) {
/* 112 */       paramStringBuffer.append(SystemEnv.getHtmlLabelName(127775, paramInt));
/*     */     }
/* 114 */     return bool2;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public String getLockedStatusName(String paramString1, String paramString2) {
/* 126 */     if ("0".equals(paramString1))
/* 127 */       return SystemEnv.getHtmlLabelName(25456, Util.getIntValue(paramString2)); 
/* 128 */     if ("1".equals(paramString1)) {
/* 129 */       return SystemEnv.getHtmlLabelName(22205, Util.getIntValue(paramString2));
/*     */     }
/* 131 */     return "";
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/fna/e9/po/base/FnaSubjectBudgetHelp.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */