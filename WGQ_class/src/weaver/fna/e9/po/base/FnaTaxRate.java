/*     */ package weaver.fna.e9.po.base;
/*     */ 
/*     */ import weaver.fna.e9.po.annotation.DbFieldInfo;
/*     */ import weaver.fna.e9.po.annotation.DbTableInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ @DbTableInfo(name = "FnaTaxRate")
/*     */ public class FnaTaxRate
/*     */ {
/*     */   static {
/*  23 */     FnaBasePo.initStatic(FnaTaxRate.class);
/*     */   }
/*     */ 
/*     */   
/*     */   @DbFieldInfo(name = "id", type = DbFieldInfo.DbType.INT, prec = 10, scale = 0, isNullable = false, isDbIdentityColumn = true, primaryKey = true)
/*  28 */   private Integer id = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Integer getId() {
/*  35 */     return this.id;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setId(Integer paramInteger) {
/*  42 */     this.id = paramInteger;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   @DbFieldInfo(name = "name", type = DbFieldInfo.DbType.VARCHAR, prec = 200, scale = 0, isNullable = false)
/*  48 */   private String name = null;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @DbFieldInfo(name = "codeName", type = DbFieldInfo.DbType.VARCHAR, prec = 200, scale = 0, isNullable = false)
/*  54 */   private String codeName = null;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @DbFieldInfo(name = "state", type = DbFieldInfo.DbType.INT, prec = 10, scale = 0, isNullable = false)
/*  60 */   private Integer state = null;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @DbFieldInfo(name = "remark", type = DbFieldInfo.DbType.VARCHAR, prec = 4000, scale = 0, isNullable = true)
/*  66 */   private String remark = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @DbFieldInfo(name = "showOrder", type = DbFieldInfo.DbType.DECIMAL, prec = 6, scale = 3, isNullable = true)
/*  73 */   private Double showOrder = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getName() {
/*  80 */     return this.name;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public void setName(String paramString) {
/*  86 */     this.name = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCodeName() {
/*  92 */     return this.codeName;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public void setCodeName(String paramString) {
/*  98 */     this.codeName = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public Integer getState() {
/* 104 */     return this.state;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public void setState(Integer paramInteger) {
/* 110 */     this.state = paramInteger;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public String getRemark() {
/* 116 */     return this.remark;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public void setRemark(String paramString) {
/* 122 */     this.remark = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public Double getShowOrder() {
/* 128 */     return this.showOrder;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public void setShowOrder(Double paramDouble) {
/* 134 */     this.showOrder = paramDouble;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/fna/e9/po/base/FnaTaxRate.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */