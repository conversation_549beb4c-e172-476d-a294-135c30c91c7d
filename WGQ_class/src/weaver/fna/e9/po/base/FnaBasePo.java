/*     */ package weaver.fna.e9.po.base;
/*     */ 
/*     */ import java.lang.reflect.Field;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Collections;
/*     */ import java.util.List;
/*     */ import java.util.concurrent.ConcurrentHashMap;
/*     */ import weaver.fna.e9.po.annotation.DbFieldInfo;
/*     */ import weaver.fna.e9.po.annotation.DbTableInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public final class FnaBasePo
/*     */ {
/*  30 */   private static final ConcurrentHashMap<String, FnaBasePo> PO_CLASS_FIELD_INFO = new ConcurrentHashMap<>();
/*     */ 
/*     */ 
/*     */   
/*  34 */   private Class<?> clazz = null;
/*     */ 
/*     */ 
/*     */   
/*  38 */   private String className = "";
/*     */ 
/*     */ 
/*     */   
/*  42 */   private String tableName = "";
/*     */ 
/*     */ 
/*     */   
/*  46 */   private int fieldsLen = 0;
/*     */ 
/*     */ 
/*     */   
/*  50 */   private final List<FnaBasePoField> fnaBasePoFieldList = Collections.synchronizedList(new ArrayList<>());
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Class<?> getClazz() {
/*  56 */     return this.clazz;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getTableName() {
/*  63 */     return this.tableName;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public final String getClassName() {
/*  70 */     return this.className;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public final int getFieldsLen() {
/*  77 */     return this.fieldsLen;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public final List<FnaBasePoField> getFnaBasePoFieldList() {
/*  84 */     return this.fnaBasePoFieldList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static final FnaBasePo GET_PO_CLASS_FIELD_INFO(String paramString) throws Exception {
/*  93 */     if (!PO_CLASS_FIELD_INFO.containsKey(paramString)) {
/*  94 */       Class<?> clazz = Class.forName(paramString);
/*  95 */       clazz.newInstance();
/*     */     } 
/*  97 */     return PO_CLASS_FIELD_INFO.get(paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static final void initStatic(Class<?> paramClass) {
/* 110 */     FnaBasePo fnaBasePo = new FnaBasePo();
/* 111 */     fnaBasePo.clazz = paramClass;
/* 112 */     fnaBasePo.className = fnaBasePo.clazz.getName();
/*     */     
/* 114 */     if (fnaBasePo.clazz.isAnnotationPresent((Class)DbTableInfo.class)) {
/* 115 */       DbTableInfo dbTableInfo = fnaBasePo.clazz.<DbTableInfo>getAnnotation(DbTableInfo.class);
/* 116 */       fnaBasePo.tableName = dbTableInfo.name();
/* 117 */       if ("".equals(fnaBasePo.tableName)) {
/* 118 */         fnaBasePo.tableName = fnaBasePo.className;
/*     */       }
/*     */     } 
/*     */     
/* 122 */     Field[] arrayOfField = fnaBasePo.clazz.getDeclaredFields();
/* 123 */     fnaBasePo.fieldsLen = arrayOfField.length;
/* 124 */     for (byte b = 0; b < fnaBasePo.fieldsLen; b++) {
/* 125 */       FnaBasePoField fnaBasePoField = new FnaBasePoField();
/* 126 */       fnaBasePo.fnaBasePoFieldList.add(fnaBasePoField);
/*     */       
/* 128 */       Field field = arrayOfField[b];
/* 129 */       String str1 = field.getName();
/* 130 */       String str2 = field.getType().toString().toLowerCase();
/*     */       
/* 132 */       fnaBasePoField.setJavaFieldName(str1);
/* 133 */       fnaBasePoField.setJavaType(str2);
/*     */       
/* 135 */       if (field.isAnnotationPresent((Class)DbFieldInfo.class)) {
/* 136 */         DbFieldInfo dbFieldInfo = field.<DbFieldInfo>getAnnotation(DbFieldInfo.class);
/* 137 */         String str = dbFieldInfo.name();
/* 138 */         if ("".equals(str)) {
/* 139 */           str = str1;
/*     */         }
/* 141 */         fnaBasePoField.setDbColName(str);
/* 142 */         fnaBasePoField.setDbColumn(dbFieldInfo.isDbColumn());
/* 143 */         fnaBasePoField.setPrimaryKey(dbFieldInfo.primaryKey());
/* 144 */         fnaBasePoField.setForeignKey(dbFieldInfo.foreignKey());
/* 145 */         fnaBasePoField.setType(dbFieldInfo.type());
/* 146 */         fnaBasePoField.setDbIdentityColumn(dbFieldInfo.isDbIdentityColumn());
/* 147 */         fnaBasePoField.setNullable(dbFieldInfo.isNullable());
/* 148 */         fnaBasePoField.setPrec(dbFieldInfo.prec());
/* 149 */         fnaBasePoField.setScale(dbFieldInfo.scale());
/*     */       } else {
/* 151 */         fnaBasePoField.setDbColumn(false);
/*     */       } 
/*     */     } 
/*     */     
/* 155 */     if (!PO_CLASS_FIELD_INFO.contains(fnaBasePo.className))
/* 156 */       PO_CLASS_FIELD_INFO.put(fnaBasePo.className, fnaBasePo); 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/fna/e9/po/base/FnaBasePo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */