/*     */ package weaver.fna.e9.po.base;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.List;
/*     */ import org.apache.commons.lang.StringEscapeUtils;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ @Deprecated
/*     */ public class FnaPeriodHelp
/*     */ {
/*     */   @Deprecated
/*     */   public List<String> getFnaPeriodGrid_popedom(String paramString1, String paramString2) {
/*  35 */     String[] arrayOfString = Util.splitString(paramString2, "+");
/*  36 */     int i = arrayOfString.length;
/*  37 */     int j = 0;
/*  38 */     int k = 7;
/*  39 */     if (i >= 1) {
/*  40 */       j = Util.getIntValue(arrayOfString[0], -998);
/*     */     }
/*  42 */     if (i >= 2) {
/*  43 */       k = Util.getIntValue(arrayOfString[1], 7);
/*     */     }
/*     */     
/*  46 */     ArrayList<String> arrayList = new ArrayList();
/*     */     
/*  48 */     arrayList.add("true");
/*     */     
/*  50 */     if (j == 0) {
/*  51 */       arrayList.add("true");
/*     */     } else {
/*  53 */       arrayList.add("false");
/*     */     } 
/*     */     
/*  56 */     if (j == 0 || j == 1) {
/*  57 */       arrayList.add("true");
/*     */     } else {
/*  59 */       arrayList.add("false");
/*     */     } 
/*     */     
/*  62 */     if (j == -1) {
/*  63 */       arrayList.add("true");
/*     */     } else {
/*  65 */       arrayList.add("false");
/*     */     } 
/*     */     
/*  68 */     StringBuffer stringBuffer = new StringBuffer();
/*  69 */     boolean bool = checkCanDelete(paramString1, k, stringBuffer);
/*  70 */     if (bool) {
/*  71 */       arrayList.add("true");
/*     */     } else {
/*  73 */       arrayList.add("false");
/*     */     } 
/*  75 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public boolean checkCanDelete(String paramString, int paramInt, StringBuffer paramStringBuffer) {
/*  88 */     RecordSet recordSet = new RecordSet();
/*  89 */     boolean bool1 = true;
/*  90 */     recordSet.executeSql("select count(*) cnt from fnaSubjectAccount a where a.fnaPeriodPk = '" + StringEscapeUtils.escapeSql(paramString) + "'");
/*  91 */     if (recordSet.next()) {
/*  92 */       bool1 = (recordSet.getInt("cnt") == 0) ? true : false;
/*     */     }
/*  94 */     boolean bool2 = true;
/*  95 */     recordSet.executeSql("select count(*) cnt from fnaSubjectBudget a where a.fnaPeriodPk = '" + StringEscapeUtils.escapeSql(paramString) + "'");
/*  96 */     if (recordSet.next()) {
/*  97 */       bool2 = (recordSet.getInt("cnt") == 0) ? true : false;
/*     */     }
/*  99 */     boolean bool3 = (bool1 && bool2) ? true : false;
/* 100 */     if (!bool1) {
/* 101 */       paramStringBuffer.append(SystemEnv.getHtmlLabelName(127777, paramInt));
/* 102 */     } else if (!bool2) {
/* 103 */       paramStringBuffer.append(SystemEnv.getHtmlLabelName(127778, paramInt));
/*     */     } 
/* 105 */     return bool3;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public String getFnaCycleName(String paramString1, String paramString2) {
/* 117 */     if ("1".equals(paramString1))
/* 118 */       return SystemEnv.getHtmlLabelName(19398, Util.getIntValue(paramString2)); 
/* 119 */     if ("2".equals(paramString1))
/* 120 */       return SystemEnv.getHtmlLabelName(17495, Util.getIntValue(paramString2)); 
/* 121 */     if ("3".equals(paramString1))
/* 122 */       return SystemEnv.getHtmlLabelName(19483, Util.getIntValue(paramString2)); 
/* 123 */     if ("4".equals(paramString1))
/* 124 */       return SystemEnv.getHtmlLabelName(17138, Util.getIntValue(paramString2)); 
/* 125 */     if ("5".equals(paramString1)) {
/* 126 */       return SystemEnv.getHtmlLabelName(127386, Util.getIntValue(paramString2));
/*     */     }
/* 128 */     return "";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public String getStatusName(String paramString1, String paramString2) {
/* 141 */     if ("0".equals(paramString1))
/* 142 */       return SystemEnv.getHtmlLabelName(18430, Util.getIntValue(paramString2)); 
/* 143 */     if ("1".equals(paramString1))
/* 144 */       return SystemEnv.getHtmlLabelName(18431, Util.getIntValue(paramString2)); 
/* 145 */     if ("-1".equals(paramString1)) {
/* 146 */       return SystemEnv.getHtmlLabelName(309, Util.getIntValue(paramString2));
/*     */     }
/* 148 */     return "";
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/fna/e9/po/base/FnaPeriodHelp.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */