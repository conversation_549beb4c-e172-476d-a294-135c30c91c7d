/*     */ package weaver.fna.e9.po.base;
/*     */ 
/*     */ import java.util.List;
/*     */ import weaver.fna.e9.po.annotation.DbFieldInfo;
/*     */ import weaver.fna.e9.po.annotation.DbTableInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ @Deprecated
/*     */ @DbTableInfo(name = "fnaSubjectBudget")
/*     */ public class FnaSubjectBudget
/*     */ {
/*     */   static {
/*  25 */     FnaBasePo.initStatic(FnaSubjectBudget.class);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public FnaSubjectBudget(Integer paramInteger1, String paramString1, String paramString2, String paramString3, String paramString4, String paramString5, Integer paramInteger2, String paramString6, String paramString7, Integer paramInteger3, Integer paramInteger4, Double paramDouble) {
/*  51 */     this.id = paramInteger1;
/*  52 */     this.fnaSubjectbPk = paramString1;
/*  53 */     this.fnaPeriodPk = paramString2;
/*  54 */     this.supFnaSubjectbPk = paramString3;
/*  55 */     this.subjectbName = paramString4;
/*  56 */     this.subjectbCode = paramString5;
/*  57 */     this.lockedStatus = paramInteger2;
/*  58 */     this.description = paramString6;
/*  59 */     this.fnaSubjectaPk = paramString7;
/*  60 */     this.subjectbIsLeaf = paramInteger3;
/*  61 */     this.subjectbLevel = paramInteger4;
/*  62 */     this.showOrder = paramDouble;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public final FnaSubjectBudget copy() {
/*  70 */     FnaSubjectBudget fnaSubjectBudget = new FnaSubjectBudget();
/*     */     
/*  72 */     fnaSubjectBudget.setId(new Integer(this.id.intValue()));
/*  73 */     fnaSubjectBudget.setFnaSubjectbPk(this.fnaSubjectbPk);
/*  74 */     fnaSubjectBudget.setFnaPeriodPk(this.fnaPeriodPk);
/*  75 */     fnaSubjectBudget.setSupFnaSubjectbPk(this.supFnaSubjectbPk);
/*  76 */     fnaSubjectBudget.setSubjectbName(this.subjectbName);
/*  77 */     fnaSubjectBudget.setSubjectbCode(this.subjectbCode);
/*  78 */     fnaSubjectBudget.setLockedStatus(new Integer(this.lockedStatus.intValue()));
/*  79 */     fnaSubjectBudget.setDescription(this.description);
/*  80 */     fnaSubjectBudget.setFnaSubjectaPk(this.fnaSubjectaPk);
/*  81 */     fnaSubjectBudget.setSubjectbIsLeaf(new Integer(this.subjectbIsLeaf.intValue()));
/*  82 */     fnaSubjectBudget.setSubjectbLevel(new Integer(this.subjectbLevel.intValue()));
/*  83 */     fnaSubjectBudget.setShowOrder(new Double(this.showOrder.doubleValue()));
/*     */     
/*  85 */     return fnaSubjectBudget;
/*     */   }
/*     */   
/*     */   @Deprecated
/*     */   @DbFieldInfo(name = "id", type = DbFieldInfo.DbType.INT, prec = 10, scale = 0, isNullable = false, isDbIdentityColumn = true)
/*  90 */   private Integer id = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public Integer getId() {
/*  99 */     return this.id;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setId(Integer paramInteger) {
/* 107 */     this.id = paramInteger;
/*     */   }
/*     */   
/*     */   @Deprecated
/*     */   @DbFieldInfo(name = "fnaSubjectbPk", type = DbFieldInfo.DbType.CHAR, prec = 32, scale = 0, isNullable = false, primaryKey = true)
/* 112 */   private String fnaSubjectbPk = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public String getFnaSubjectbPk() {
/* 121 */     return this.fnaSubjectbPk;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setFnaSubjectbPk(String paramString) {
/* 129 */     this.fnaSubjectbPk = paramString;
/*     */   }
/*     */   
/*     */   @Deprecated
/*     */   @DbFieldInfo(name = "fnaPeriodPk", type = DbFieldInfo.DbType.CHAR, prec = 32, scale = 0, isNullable = false, foreignKey = true)
/* 134 */   private String fnaPeriodPk = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public String getFnaPeriodPk() {
/* 143 */     return this.fnaPeriodPk;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setFnaPeriodPk(String paramString) {
/* 151 */     this.fnaPeriodPk = paramString;
/*     */   }
/*     */   
/*     */   @Deprecated
/*     */   @DbFieldInfo(name = "supFnaSubjectbPk", type = DbFieldInfo.DbType.CHAR, prec = 32, scale = 0, isNullable = true, foreignKey = true)
/* 156 */   private String supFnaSubjectbPk = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public String getSupFnaSubjectbPk() {
/* 165 */     return this.supFnaSubjectbPk;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setSupFnaSubjectbPk(String paramString) {
/* 173 */     this.supFnaSubjectbPk = paramString;
/*     */   }
/*     */   
/*     */   @Deprecated
/*     */   @DbFieldInfo(name = "fnaSubjectaPk", type = DbFieldInfo.DbType.CHAR, prec = 32, scale = 0, isNullable = true, foreignKey = true)
/* 178 */   private String fnaSubjectaPk = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public String getFnaSubjectaPk() {
/* 187 */     return this.fnaSubjectaPk;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setFnaSubjectaPk(String paramString) {
/* 195 */     this.fnaSubjectaPk = paramString;
/*     */   }
/*     */   
/*     */   @Deprecated
/*     */   @DbFieldInfo(name = "subjectbName", type = DbFieldInfo.DbType.VARCHAR, prec = 600, scale = 0, isNullable = false)
/* 200 */   private String subjectbName = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public String getSubjectbName() {
/* 209 */     return this.subjectbName;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setSubjectbName(String paramString) {
/* 217 */     this.subjectbName = paramString;
/*     */   }
/*     */   
/*     */   @Deprecated
/*     */   @DbFieldInfo(name = "subjectbCode", type = DbFieldInfo.DbType.VARCHAR, prec = 600, scale = 0, isNullable = false)
/* 222 */   private String subjectbCode = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public String getSubjectbCode() {
/* 231 */     return this.subjectbCode;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setSubjectbCode(String paramString) {
/* 239 */     this.subjectbCode = paramString;
/*     */   }
/*     */   
/*     */   @Deprecated
/*     */   @DbFieldInfo(name = "lockedStatus", type = DbFieldInfo.DbType.INT, prec = 10, scale = 0, isNullable = false)
/* 244 */   private Integer lockedStatus = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public Integer getLockedStatus() {
/* 253 */     return this.lockedStatus;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setLockedStatus(Integer paramInteger) {
/* 261 */     this.lockedStatus = paramInteger;
/*     */   }
/*     */   
/*     */   @Deprecated
/*     */   @DbFieldInfo(name = "description", type = DbFieldInfo.DbType.TEXT, prec = 0, scale = 0, isNullable = true)
/* 266 */   private String description = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public String getDescription() {
/* 275 */     return this.description;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setDescription(String paramString) {
/* 283 */     this.description = paramString;
/*     */   }
/*     */   
/*     */   @Deprecated
/*     */   @DbFieldInfo(name = "showOrder", type = DbFieldInfo.DbType.DECIMAL, prec = 6, scale = 3, isNullable = true)
/* 288 */   private Double showOrder = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public Double getShowOrder() {
/* 297 */     return this.showOrder;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setShowOrder(Double paramDouble) {
/* 305 */     this.showOrder = paramDouble;
/*     */   }
/*     */   
/*     */   @Deprecated
/*     */   @DbFieldInfo(name = "subjectbIsLeaf", type = DbFieldInfo.DbType.INT, prec = 10, scale = 0, isNullable = false, isDbIdentityColumn = true)
/* 310 */   private Integer subjectbIsLeaf = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public Integer getSubjectbIsLeaf() {
/* 319 */     return this.subjectbIsLeaf;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setSubjectbIsLeaf(Integer paramInteger) {
/* 327 */     this.subjectbIsLeaf = paramInteger;
/*     */   }
/*     */   
/*     */   @Deprecated
/*     */   @DbFieldInfo(name = "subjectbLevel", type = DbFieldInfo.DbType.INT, prec = 10, scale = 0, isNullable = false, isDbIdentityColumn = true)
/* 332 */   private Integer subjectbLevel = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public Integer getSubjectbLevel() {
/* 341 */     return this.subjectbLevel;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setSubjectbLevel(Integer paramInteger) {
/* 349 */     this.subjectbLevel = paramInteger;
/*     */   }
/*     */ 
/*     */   
/*     */   @Deprecated
/* 354 */   private List<FnaSubjectBudget> fnaSubjectBudgetList = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public List<FnaSubjectBudget> getFnaSubjectBudgetList() {
/* 362 */     return this.fnaSubjectBudgetList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setFnaSubjectBudget(List<FnaSubjectBudget> paramList) {
/* 370 */     this.fnaSubjectBudgetList = paramList;
/*     */   }
/*     */   
/*     */   @Deprecated
/*     */   public FnaSubjectBudget() {}
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/fna/e9/po/base/FnaSubjectBudget.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */