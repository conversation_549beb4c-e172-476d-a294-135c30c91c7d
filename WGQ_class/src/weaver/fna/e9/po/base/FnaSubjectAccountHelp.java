/*     */ package weaver.fna.e9.po.base;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.List;
/*     */ import org.apache.commons.lang.StringEscapeUtils;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.conn.RecordSetTrans;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ @Deprecated
/*     */ public class FnaSubjectAccountHelp
/*     */ {
/*     */   @Deprecated
/*     */   public List<String> getFnaSubjectAccountGrid_popedom(String paramString1, String paramString2) throws Exception {
/*  37 */     String[] arrayOfString = Util.splitString(paramString2, "+");
/*  38 */     int i = arrayOfString.length;
/*  39 */     int j = 0;
/*  40 */     int k = 7;
/*  41 */     if (i >= 1) {
/*  42 */       j = Util.getIntValue(arrayOfString[0]);
/*     */     }
/*  44 */     if (i >= 2) {
/*  45 */       k = Util.getIntValue(arrayOfString[1], 7);
/*     */     }
/*     */     
/*  48 */     ArrayList<String> arrayList = new ArrayList();
/*     */     
/*  50 */     arrayList.add("true");
/*     */     
/*  52 */     StringBuffer stringBuffer = new StringBuffer();
/*  53 */     boolean bool = checkCanDelete(null, paramString1, k, stringBuffer);
/*  54 */     if (bool) {
/*  55 */       arrayList.add("true");
/*     */     } else {
/*  57 */       arrayList.add("false");
/*     */     } 
/*  59 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public boolean checkCanDelete(RecordSetTrans paramRecordSetTrans, String paramString, int paramInt, StringBuffer paramStringBuffer) throws Exception {
/*  74 */     return checkCanDelete(paramRecordSetTrans, paramString, true, paramInt, paramStringBuffer);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public boolean checkCanDelete(RecordSetTrans paramRecordSetTrans, String paramString, boolean paramBoolean, int paramInt, StringBuffer paramStringBuffer) throws Exception {
/*  90 */     RecordSet recordSet = null;
/*  91 */     if (paramRecordSetTrans == null) {
/*  92 */       recordSet = new RecordSet();
/*     */     }
/*  94 */     String str = "";
/*  95 */     boolean bool1 = true;
/*  96 */     if (paramBoolean) {
/*  97 */       str = "select count(*) cnt from fnaSubjectAccount a where a.supFnaSubjectaPk = '" + StringEscapeUtils.escapeSql(paramString) + "'";
/*  98 */       if (paramRecordSetTrans == null) {
/*  99 */         recordSet.executeSql(str);
/* 100 */         if (recordSet.next()) {
/* 101 */           bool1 = (recordSet.getInt("cnt") == 0) ? true : false;
/*     */         }
/*     */       } else {
/* 104 */         paramRecordSetTrans.executeSql(str);
/* 105 */         if (paramRecordSetTrans.next()) {
/* 106 */           bool1 = (paramRecordSetTrans.getInt("cnt") == 0) ? true : false;
/*     */         }
/*     */       } 
/*     */     } 
/* 110 */     boolean bool2 = true;
/* 111 */     str = "select count(*) cnt from fnaSubjectBudget a where a.fnaSubjectaPk = '" + StringEscapeUtils.escapeSql(paramString) + "'";
/* 112 */     if (paramRecordSetTrans == null) {
/* 113 */       recordSet.executeSql(str);
/* 114 */       if (recordSet.next()) {
/* 115 */         bool2 = (recordSet.getInt("cnt") == 0) ? true : false;
/*     */       }
/*     */     } else {
/* 118 */       paramRecordSetTrans.executeSql(str);
/* 119 */       if (paramRecordSetTrans.next()) {
/* 120 */         bool2 = (paramRecordSetTrans.getInt("cnt") == 0) ? true : false;
/*     */       }
/*     */     } 
/* 123 */     boolean bool3 = (bool1 && bool2) ? true : false;
/* 124 */     if (!bool1) {
/* 125 */       paramStringBuffer.append(SystemEnv.getHtmlLabelName(127774, paramInt));
/* 126 */     } else if (!bool2) {
/* 127 */       paramStringBuffer.append(SystemEnv.getHtmlLabelName(127776, paramInt));
/*     */     } 
/* 129 */     return bool3;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public String getLockedStatusName(String paramString1, String paramString2) {
/* 141 */     if ("0".equals(paramString1))
/* 142 */       return SystemEnv.getHtmlLabelName(25456, Util.getIntValue(paramString2)); 
/* 143 */     if ("1".equals(paramString1)) {
/* 144 */       return SystemEnv.getHtmlLabelName(22205, Util.getIntValue(paramString2));
/*     */     }
/* 146 */     return "";
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/fna/e9/po/base/FnaSubjectAccountHelp.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */