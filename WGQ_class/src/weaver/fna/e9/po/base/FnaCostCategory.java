/*     */ package weaver.fna.e9.po.base;
/*     */ 
/*     */ import weaver.fna.e9.po.annotation.DbFieldInfo;
/*     */ import weaver.fna.e9.po.annotation.DbTableInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ @DbTableInfo(name = "fnaCostCategory")
/*     */ public class FnaCostCategory
/*     */ {
/*     */   static {
/*  23 */     FnaBasePo.initStatic(FnaCostCategory.class);
/*     */   }
/*     */ 
/*     */   
/*     */   @DbFieldInfo(name = "id", type = DbFieldInfo.DbType.INT, prec = 10, scale = 0, isNullable = false, isDbIdentityColumn = true, primaryKey = true)
/*  28 */   private Integer id = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Integer getId() {
/*  35 */     return this.id;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setId(Integer paramInteger) {
/*  42 */     this.id = paramInteger;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   @DbFieldInfo(name = "name", type = DbFieldInfo.DbType.VARCHAR, prec = 200, scale = 0, isNullable = false)
/*  48 */   private String name = null;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @DbFieldInfo(name = "codeName", type = DbFieldInfo.DbType.VARCHAR, prec = 200, scale = 0, isNullable = false)
/*  54 */   private String codeName = null;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @DbFieldInfo(name = "state", type = DbFieldInfo.DbType.INT, prec = 10, scale = 0, isNullable = false)
/*  60 */   private Integer state = null;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @DbFieldInfo(name = "remark", type = DbFieldInfo.DbType.VARCHAR, prec = 4000, scale = 0, isNullable = true)
/*  66 */   private String remark = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @DbFieldInfo(name = "showOrder", type = DbFieldInfo.DbType.DECIMAL, prec = 6, scale = 3, isNullable = true)
/*  73 */   private Double showOrder = null;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @DbFieldInfo(name = "costCategoryType", type = DbFieldInfo.DbType.INT, prec = 10, scale = 0, isNullable = false)
/*  79 */   private Integer costCategoryType = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getName() {
/*  86 */     return this.name;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public void setName(String paramString) {
/*  92 */     this.name = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCodeName() {
/*  98 */     return this.codeName;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public void setCodeName(String paramString) {
/* 104 */     this.codeName = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public Integer getState() {
/* 110 */     return this.state;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public void setState(Integer paramInteger) {
/* 116 */     this.state = paramInteger;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public String getRemark() {
/* 122 */     return this.remark;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public void setRemark(String paramString) {
/* 128 */     this.remark = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public Double getShowOrder() {
/* 134 */     return this.showOrder;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public void setShowOrder(Double paramDouble) {
/* 140 */     this.showOrder = paramDouble;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/* 146 */   private String workflowIds = null;
/*     */ 
/*     */ 
/*     */   
/*     */   public String getWorkflowIds() {
/* 151 */     return this.workflowIds;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public void setWorkflowIds(String paramString) {
/* 157 */     this.workflowIds = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public Integer getCostCategoryType() {
/* 163 */     return this.costCategoryType;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public void setCostCategoryType(Integer paramInteger) {
/* 169 */     this.costCategoryType = paramInteger;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/fna/e9/po/base/FnaCostCategory.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */