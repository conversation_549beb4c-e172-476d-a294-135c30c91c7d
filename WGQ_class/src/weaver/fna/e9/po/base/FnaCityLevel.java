/*     */ package weaver.fna.e9.po.base;
/*     */ 
/*     */ import weaver.fna.e9.po.annotation.DbFieldInfo;
/*     */ import weaver.fna.e9.po.annotation.DbTableInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ @DbTableInfo(name = "FnaCityLevel")
/*     */ public class FnaCityLevel
/*     */ {
/*     */   static {
/*  23 */     FnaBasePo.initStatic(FnaCityLevel.class);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  29 */   private String cityIds = null;
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCityIds() {
/*  34 */     return this.cityIds;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public void setCityIds(String paramString) {
/*  40 */     this.cityIds = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   @DbFieldInfo(name = "id", type = DbFieldInfo.DbType.INT, prec = 10, scale = 0, isNullable = false, isDbIdentityColumn = true, primaryKey = true)
/*  46 */   private Integer id = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Integer getId() {
/*  53 */     return this.id;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setId(Integer paramInteger) {
/*  60 */     this.id = paramInteger;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   @DbFieldInfo(name = "name", type = DbFieldInfo.DbType.VARCHAR, prec = 200, scale = 0, isNullable = false)
/*  66 */   private String name = null;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @DbFieldInfo(name = "codeName", type = DbFieldInfo.DbType.VARCHAR, prec = 200, scale = 0, isNullable = false)
/*  72 */   private String codeName = null;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @DbFieldInfo(name = "state", type = DbFieldInfo.DbType.INT, prec = 10, scale = 0, isNullable = false)
/*  78 */   private Integer state = null;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @DbFieldInfo(name = "remark", type = DbFieldInfo.DbType.VARCHAR, prec = 4000, scale = 0, isNullable = true)
/*  84 */   private String remark = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @DbFieldInfo(name = "showOrder", type = DbFieldInfo.DbType.DECIMAL, prec = 6, scale = 3, isNullable = true)
/*  91 */   private Double showOrder = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getName() {
/*  98 */     return this.name;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public void setName(String paramString) {
/* 104 */     this.name = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCodeName() {
/* 110 */     return this.codeName;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public void setCodeName(String paramString) {
/* 116 */     this.codeName = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public Integer getState() {
/* 122 */     return this.state;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public void setState(Integer paramInteger) {
/* 128 */     this.state = paramInteger;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public String getRemark() {
/* 134 */     return this.remark;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public void setRemark(String paramString) {
/* 140 */     this.remark = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public Double getShowOrder() {
/* 146 */     return this.showOrder;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public void setShowOrder(Double paramDouble) {
/* 152 */     this.showOrder = paramDouble;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/fna/e9/po/base/FnaCityLevel.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */