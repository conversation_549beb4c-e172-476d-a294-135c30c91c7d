/*     */ package weaver.fna.e9.po.base;
/*     */ 
/*     */ import weaver.fna.e9.po.annotation.DbFieldInfo;
/*     */ import weaver.fna.e9.po.annotation.DbTableInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ @Deprecated
/*     */ @DbTableInfo(name = "fnaPeriodDtl")
/*     */ public class FnaPeriodDtl
/*     */ {
/*     */   static {
/*  24 */     FnaBasePo.initStatic(FnaPeriodDtl.class);
/*     */   }
/*     */ 
/*     */   
/*     */   @DbFieldInfo(name = "id", type = DbFieldInfo.DbType.INT, prec = 10, scale = 0, isNullable = false, isDbIdentityColumn = true)
/*  29 */   private Integer id = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Integer getId() {
/*  36 */     return this.id;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setId(Integer paramInteger) {
/*  43 */     this.id = paramInteger;
/*     */   }
/*     */ 
/*     */   
/*     */   @DbFieldInfo(name = "fnaPeriodDtlPk", type = DbFieldInfo.DbType.CHAR, prec = 32, scale = 0, isNullable = false, primaryKey = true)
/*  48 */   private String fnaPeriodDtlPk = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getFnaPeriodDtlPk() {
/*  55 */     return this.fnaPeriodDtlPk;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setFnaPeriodDtlPk(String paramString) {
/*  62 */     this.fnaPeriodDtlPk = paramString;
/*     */   }
/*     */ 
/*     */   
/*     */   @DbFieldInfo(name = "fnaPeriodPk", type = DbFieldInfo.DbType.CHAR, prec = 32, scale = 0, isNullable = false, foreignKey = true)
/*  67 */   private String fnaPeriodPk = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getFnaPeriodPk() {
/*  74 */     return this.fnaPeriodPk;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setFnaPeriodPk(String paramString) {
/*  81 */     this.fnaPeriodPk = paramString;
/*     */   }
/*     */ 
/*     */   
/*     */   @DbFieldInfo(name = "startdate", type = DbFieldInfo.DbType.CHAR, prec = 10, scale = 0, isNullable = false)
/*  86 */   private String startdate = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getStartdate() {
/*  93 */     return this.startdate;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setStartdate(String paramString) {
/* 100 */     this.startdate = paramString;
/*     */   }
/*     */ 
/*     */   
/*     */   @DbFieldInfo(name = "enddate", type = DbFieldInfo.DbType.CHAR, prec = 10, scale = 0, isNullable = false)
/* 105 */   private String enddate = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getEnddate() {
/* 112 */     return this.enddate;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setEnddate(String paramString) {
/* 119 */     this.enddate = paramString;
/*     */   }
/*     */ 
/*     */   
/*     */   @DbFieldInfo(name = "fnaPeriodsList", type = DbFieldInfo.DbType.INT, prec = 10, scale = 0, isNullable = false)
/* 124 */   private Integer fnaPeriodsList = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Integer getFnaPeriodsList() {
/* 131 */     return this.fnaPeriodsList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setFnaPeriodsList(Integer paramInteger) {
/* 138 */     this.fnaPeriodsList = paramInteger;
/*     */   }
/*     */ 
/*     */   
/*     */   @DbFieldInfo(name = "fnaPeriodsName", type = DbFieldInfo.DbType.CHAR, prec = 50, scale = 0, isNullable = false)
/* 143 */   private String fnaPeriodsName = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getFnaPeriodsName() {
/* 150 */     return this.fnaPeriodsName;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setFnaPeriodsName(String paramString) {
/* 157 */     this.fnaPeriodsName = paramString;
/*     */   }
/*     */ 
/*     */   
/*     */   @DbFieldInfo(name = "showOrder", type = DbFieldInfo.DbType.DECIMAL, prec = 6, scale = 3, isNullable = true)
/* 162 */   private Double showOrder = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Double getShowOrder() {
/* 169 */     return this.showOrder;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setShowOrder(Double paramDouble) {
/* 176 */     this.showOrder = paramDouble;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public FnaPeriodDtl(Integer paramInteger1, String paramString1, String paramString2, String paramString3, String paramString4, Integer paramInteger2, String paramString5) {
/* 193 */     this.id = paramInteger1;
/* 194 */     this.fnaPeriodDtlPk = paramString1;
/* 195 */     this.fnaPeriodPk = paramString2;
/* 196 */     this.startdate = paramString3;
/* 197 */     this.enddate = paramString4;
/* 198 */     this.fnaPeriodsList = paramInteger2;
/* 199 */     this.fnaPeriodsName = paramString5;
/*     */   }
/*     */   
/*     */   public FnaPeriodDtl() {}
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/fna/e9/po/base/FnaPeriodDtl.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */