/*    */ package weaver.fna.e9.vo.base;
/*    */ 
/*    */ import weaver.fna.e9.vo.annotation.PageFieldInfo;
/*    */ import weaver.fna.e9.vo.annotation.PageInfo;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ @Deprecated
/*    */ @PageInfo(isMultipartFormData = true)
/*    */ public class FnaSubjectBatchImportVo
/*    */ {
/*    */   static {
/* 23 */     FnaBaseVo.initStatic(FnaSubjectBatchImportVo.class);
/*    */   }
/*    */   
/*    */   @Deprecated
/*    */   @PageFieldInfo
/* 28 */   String _guid1 = "";
/*    */ 
/*    */ 
/*    */   
/*    */   @Deprecated
/*    */   @PageFieldInfo
/* 34 */   String fnaPeriodPk = "";
/*    */ 
/*    */ 
/*    */   
/*    */   @Deprecated
/*    */   @PageFieldInfo(enableIsNullDefaultValue = true, isNullDefaultValue = "-1")
/* 40 */   Integer importType = null;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   @Deprecated
/*    */   public String get_guid1() {
/* 49 */     return this._guid1;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   @Deprecated
/*    */   public void set_guid1(String paramString) {
/* 57 */     this._guid1 = paramString;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   @Deprecated
/*    */   public String getFnaPeriodPk() {
/* 65 */     return this.fnaPeriodPk;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   @Deprecated
/*    */   public void setFnaPeriodPk(String paramString) {
/* 73 */     this.fnaPeriodPk = paramString;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   @Deprecated
/*    */   public Integer getImportType() {
/* 81 */     return this.importType;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   @Deprecated
/*    */   public void setImportType(Integer paramInteger) {
/* 89 */     this.importType = paramInteger;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/fna/e9/vo/base/FnaSubjectBatchImportVo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */