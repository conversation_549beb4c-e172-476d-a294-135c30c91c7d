/*     */ package weaver.fna.e9.vo.base;
/*     */ 
/*     */ import weaver.fna.e9.vo.annotation.PageFieldInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class FnaCostCategoryFieldSetVo
/*     */ {
/*     */   static {
/*  22 */     FnaBaseVo.initStatic(FnaCostCategoryFieldSetVo.class);
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   @PageFieldInfo(poClassPropertyName = "weaver.fna.e9.po.base.FnaCostCategory.id", enableIsNullDefaultValue = true, isNullDefaultValue = "0")
/*  28 */   private Integer id = null;
/*     */ 
/*     */ 
/*     */   
/*     */   @PageFieldInfo(poClassPropertyName = "weaver.fna.e9.po.base.FnaCostCategory.fnaCostCategoryId", enableIsNullDefaultValue = true, isNullDefaultValue = "0")
/*  33 */   private Integer fnaCostCategoryId = null;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @PageFieldInfo(poClassPropertyName = "weaver.fna.e9.po.base.FnaCostCategory.workflowId", enableIsNullDefaultValue = true, isNullDefaultValue = "0")
/*  39 */   private Integer workflowId = null;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @PageFieldInfo(poClassPropertyName = "weaver.fna.e9.po.base.FnaCostCategory.fieldId", enableIsNullDefaultValue = true, isNullDefaultValue = "0")
/*  45 */   private Integer fieldId = null;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @PageFieldInfo(poClassPropertyName = "weaver.fna.e9.po.base.FnaCostCategory.isRequired", enableIsNullDefaultValue = true, isNullDefaultValue = "1")
/*  51 */   private Integer isRequired = null;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @PageFieldInfo(poClassPropertyName = "weaver.fna.e9.po.base.FnaCostCategory.name", enableIsNullDefaultValue = true, isNullDefaultValue = "")
/*  57 */   private String name = null;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @PageFieldInfo(poClassPropertyName = "weaver.fna.e9.po.base.FnaCostCategory.showOrder", enableIsNullDefaultValue = true, isNullDefaultValue = "0")
/*  63 */   private Double showOrder = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Integer getFnaCostCategoryId() {
/*  70 */     return this.fnaCostCategoryId;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setFnaCostCategoryId(Integer paramInteger) {
/*  77 */     this.fnaCostCategoryId = paramInteger;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Integer getWorkflowId() {
/*  84 */     return this.workflowId;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setWorkflowId(Integer paramInteger) {
/*  91 */     this.workflowId = paramInteger;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Integer getFieldId() {
/*  98 */     return this.fieldId;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setFieldId(Integer paramInteger) {
/* 105 */     this.fieldId = paramInteger;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Integer getIsRequired() {
/* 112 */     return this.isRequired;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setIsRequired(Integer paramInteger) {
/* 119 */     this.isRequired = paramInteger;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getName() {
/* 126 */     return this.name;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setName(String paramString) {
/* 133 */     this.name = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Double getShowOrder() {
/* 140 */     return this.showOrder;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setShowOrder(Double paramDouble) {
/* 147 */     this.showOrder = paramDouble;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Integer getId() {
/* 154 */     return this.id;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setId(Integer paramInteger) {
/* 161 */     this.id = paramInteger;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/fna/e9/vo/base/FnaCostCategoryFieldSetVo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */