/*    */ package weaver.fna.e9.vo.base;
/*    */ 
/*    */ import weaver.fna.e9.vo.annotation.PageFieldInfo;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ @Deprecated
/*    */ public class FnaPeriodVo
/*    */ {
/*    */   static {
/* 21 */     FnaBaseVo.initStatic(FnaPeriodVo.class);
/*    */   }
/*    */   
/*    */   @Deprecated
/*    */   @PageFieldInfo(poClassPropertyName = "weaver.fna.e9.po.base.FnaPeriod.fnaPeriodName")
/* 26 */   String fnaPeriodName = "";
/*    */ 
/*    */ 
/*    */   
/*    */   @Deprecated
/*    */   @PageFieldInfo(isPostParam = false)
/* 32 */   String splitPageTagTableString = "";
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   @Deprecated
/*    */   public String getSplitPageTagTableString() {
/* 41 */     return this.splitPageTagTableString;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   @Deprecated
/*    */   public void setSplitPageTagTableString(String paramString) {
/* 49 */     this.splitPageTagTableString = paramString;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   @Deprecated
/*    */   public String getFnaPeriodName() {
/* 57 */     return this.fnaPeriodName;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   @Deprecated
/*    */   public void setFnaPeriodName(String paramString) {
/* 65 */     this.fnaPeriodName = paramString;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/fna/e9/vo/base/FnaPeriodVo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */