/*     */ package weaver.fna.e9.vo.base;
/*     */ 
/*     */ import weaver.fna.e9.vo.annotation.PageFieldInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ @Deprecated
/*     */ public class FnaSubjectAccountEditVo
/*     */ {
/*     */   static {
/*  21 */     FnaBaseVo.initStatic(FnaSubjectAccountEditVo.class);
/*     */   }
/*     */   
/*     */   @Deprecated
/*     */   @PageFieldInfo(poClassPropertyName = "weaver.fna.e9.po.base.FnaSubjectAccount.fnaSubjectaPk")
/*  26 */   String fnaSubjectaPk = "";
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   @PageFieldInfo(poClassPropertyName = "weaver.fna.e9.po.base.FnaSubjectAccount.fnaPeriodPk")
/*  32 */   String fnaPeriodPk = "";
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   @PageFieldInfo(poClassPropertyName = "weaver.fna.e9.po.base.FnaSubjectAccount.supFnaSubjectaPk")
/*  38 */   String supFnaSubjectaPk = "";
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   @PageFieldInfo(isPostParam = false)
/*  44 */   String supFnaSubjectaName = "";
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   @PageFieldInfo(poClassPropertyName = "weaver.fna.e9.po.base.FnaSubjectAccount.subjectaName")
/*  50 */   String subjectaName = "";
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   @PageFieldInfo(poClassPropertyName = "weaver.fna.e9.po.base.FnaSubjectAccount.subjectaCode")
/*  56 */   String subjectaCode = "";
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   @PageFieldInfo(poClassPropertyName = "weaver.fna.e9.po.base.FnaSubjectAccount.lockedStatus")
/*  62 */   Integer lockedStatus = null;
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   @PageFieldInfo(poClassPropertyName = "weaver.fna.e9.po.base.FnaSubjectAccount.description")
/*  68 */   String description = "";
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   @PageFieldInfo(poClassPropertyName = "weaver.fna.e9.po.base.FnaSubjectAccount.showOrder")
/*  74 */   Double showOrder = null;
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   @PageFieldInfo(isPostParam = false)
/*  80 */   String supFnaSubjectaPk_browserUrl = "";
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   @PageFieldInfo(isPostParam = false)
/*  86 */   String fnaSubjectbPks_browserUrl = "";
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   @PageFieldInfo
/*  92 */   String fnaSubjectbPks = "";
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   @PageFieldInfo(isPostParam = false)
/*  98 */   String fnaSubjectbNames = "";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public String getFnaSubjectbPks() {
/* 107 */     return this.fnaSubjectbPks;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setFnaSubjectbPks(String paramString) {
/* 115 */     this.fnaSubjectbPks = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public String getFnaSubjectbNames() {
/* 123 */     return this.fnaSubjectbNames;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setFnaSubjectbNames(String paramString) {
/* 131 */     this.fnaSubjectbNames = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public String getFnaSubjectbPks_browserUrl() {
/* 139 */     return this.fnaSubjectbPks_browserUrl;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setFnaSubjectbPks_browserUrl(String paramString) {
/* 147 */     this.fnaSubjectbPks_browserUrl = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public String getSupFnaSubjectaPk_browserUrl() {
/* 155 */     return this.supFnaSubjectaPk_browserUrl;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setSupFnaSubjectaPk_browserUrl(String paramString) {
/* 163 */     this.supFnaSubjectaPk_browserUrl = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public String getSupFnaSubjectaName() {
/* 171 */     return this.supFnaSubjectaName;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setSupFnaSubjectaName(String paramString) {
/* 179 */     this.supFnaSubjectaName = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public Double getShowOrder() {
/* 187 */     return this.showOrder;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setShowOrder(Double paramDouble) {
/* 195 */     this.showOrder = paramDouble;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public String getFnaSubjectaPk() {
/* 203 */     return this.fnaSubjectaPk;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setFnaSubjectaPk(String paramString) {
/* 211 */     this.fnaSubjectaPk = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public String getFnaPeriodPk() {
/* 219 */     return this.fnaPeriodPk;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setFnaPeriodPk(String paramString) {
/* 227 */     this.fnaPeriodPk = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public String getSupFnaSubjectaPk() {
/* 235 */     return this.supFnaSubjectaPk;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setSupFnaSubjectaPk(String paramString) {
/* 243 */     this.supFnaSubjectaPk = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public String getSubjectaName() {
/* 251 */     return this.subjectaName;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setSubjectaName(String paramString) {
/* 259 */     this.subjectaName = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public String getSubjectaCode() {
/* 267 */     return this.subjectaCode;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setSubjectaCode(String paramString) {
/* 275 */     this.subjectaCode = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public Integer getLockedStatus() {
/* 283 */     return this.lockedStatus;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setLockedStatus(Integer paramInteger) {
/* 291 */     this.lockedStatus = paramInteger;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public String getDescription() {
/* 299 */     return this.description;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setDescription(String paramString) {
/* 307 */     this.description = paramString;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/fna/e9/vo/base/FnaSubjectAccountEditVo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */