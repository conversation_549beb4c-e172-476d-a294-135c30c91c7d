/*     */ package weaver.fna.e9.vo.base;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.List;
/*     */ import weaver.fna.e9.po.base.FnaPeriod;
/*     */ import weaver.fna.e9.vo.annotation.PageFieldInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ @Deprecated
/*     */ public class InitialFnaPeriodSubjectVo
/*     */ {
/*     */   static {
/*  25 */     FnaBaseVo.initStatic(InitialFnaPeriodSubjectVo.class);
/*     */   }
/*     */   
/*     */   @Deprecated
/*     */   @PageFieldInfo
/*  30 */   String fnaPeriodPk = "";
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   @PageFieldInfo
/*  36 */   String initialFnaPeriodPk = "";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   @PageFieldInfo(enableIsNullDefaultValue = true, isNullDefaultValue = "0")
/*  44 */   Integer initialBudgetSubject = Integer.valueOf(0);
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   @PageFieldInfo(enableIsNullDefaultValue = true, isNullDefaultValue = "0")
/*  50 */   Integer initialIncidenceRelation = Integer.valueOf(0);
/*     */   
/*     */   @Deprecated
/*     */   @PageFieldInfo(isPostParam = false)
/*  54 */   List<FnaPeriod> initialFnaPeriodPkList = new ArrayList<>();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public String getFnaPeriodPk() {
/*  63 */     return this.fnaPeriodPk;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setFnaPeriodPk(String paramString) {
/*  71 */     this.fnaPeriodPk = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public String getInitialFnaPeriodPk() {
/*  79 */     return this.initialFnaPeriodPk;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setInitialFnaPeriodPk(String paramString) {
/*  87 */     this.initialFnaPeriodPk = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public Integer getInitialBudgetSubject() {
/*  95 */     return this.initialBudgetSubject;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setInitialBudgetSubject(Integer paramInteger) {
/* 103 */     this.initialBudgetSubject = paramInteger;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public Integer getInitialIncidenceRelation() {
/* 111 */     return this.initialIncidenceRelation;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setInitialIncidenceRelation(Integer paramInteger) {
/* 119 */     this.initialIncidenceRelation = paramInteger;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public List<FnaPeriod> getInitialFnaPeriodPkList() {
/* 127 */     return this.initialFnaPeriodPkList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setInitialFnaPeriodPkList(List<FnaPeriod> paramList) {
/* 135 */     this.initialFnaPeriodPkList = paramList;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/fna/e9/vo/base/InitialFnaPeriodSubjectVo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */