/*     */ package weaver.fna.e9.vo.base;
/*     */ 
/*     */ import java.lang.reflect.Field;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Collections;
/*     */ import java.util.List;
/*     */ import java.util.concurrent.ConcurrentHashMap;
/*     */ import weaver.fna.e9.vo.annotation.PageFieldInfo;
/*     */ import weaver.fna.e9.vo.annotation.PageInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public final class FnaBaseVo
/*     */ {
/*  31 */   private static final ConcurrentHashMap<String, FnaBaseVo> VO_CLASS_FIELD_INFO = new ConcurrentHashMap<>();
/*     */ 
/*     */ 
/*     */   
/*  35 */   private Class<?> clazz = null;
/*     */ 
/*     */ 
/*     */   
/*  39 */   private String className = "";
/*     */ 
/*     */ 
/*     */   
/*  43 */   private int fieldsLen = 0;
/*     */ 
/*     */ 
/*     */   
/*  47 */   private final List<FnaBaseVoField> fnaBaseVoFieldList = Collections.synchronizedList(new ArrayList<>());
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Class<?> getClazz() {
/*  53 */     return this.clazz;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public final String getClassName() {
/*  60 */     return this.className;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public final int getFieldsLen() {
/*  67 */     return this.fieldsLen;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public final List<FnaBaseVoField> getFnaBaseVoFieldList() {
/*  74 */     return this.fnaBaseVoFieldList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static final FnaBaseVo GET_VO_CLASS_FIELD_INFO(String paramString) throws Exception {
/*  83 */     if (!VO_CLASS_FIELD_INFO.containsKey(paramString)) {
/*  84 */       Class<?> clazz = Class.forName(paramString);
/*  85 */       clazz.newInstance();
/*     */     } 
/*  87 */     return VO_CLASS_FIELD_INFO.get(paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static final void initStatic(Class<?> paramClass) {
/* 100 */     FnaBaseVo fnaBaseVo = new FnaBaseVo();
/* 101 */     fnaBaseVo.clazz = paramClass;
/* 102 */     fnaBaseVo.className = fnaBaseVo.clazz.getName();
/*     */     
/* 104 */     boolean bool = false;
/* 105 */     if (fnaBaseVo.clazz.isAnnotationPresent((Class)PageInfo.class)) {
/* 106 */       PageInfo pageInfo = fnaBaseVo.clazz.<PageInfo>getAnnotation(PageInfo.class);
/* 107 */       if (pageInfo != null) {
/* 108 */         bool = pageInfo.isMultipartFormData();
/*     */       }
/*     */     } 
/*     */     
/* 112 */     Field[] arrayOfField = fnaBaseVo.clazz.getDeclaredFields();
/* 113 */     fnaBaseVo.fieldsLen = arrayOfField.length;
/* 114 */     for (byte b = 0; b < fnaBaseVo.fieldsLen; b++) {
/* 115 */       FnaBaseVoField fnaBaseVoField = new FnaBaseVoField();
/* 116 */       fnaBaseVo.fnaBaseVoFieldList.add(fnaBaseVoField);
/*     */       
/* 118 */       Field field = arrayOfField[b];
/* 119 */       String str1 = field.getName();
/* 120 */       String str2 = field.getType().toString().toLowerCase();
/*     */       
/* 122 */       if (field.isAnnotationPresent((Class)PageFieldInfo.class)) {
/* 123 */         PageFieldInfo pageFieldInfo = field.<PageFieldInfo>getAnnotation(PageFieldInfo.class);
/* 124 */         String str = pageFieldInfo.name();
/* 125 */         if ("".equals(str)) {
/* 126 */           str = str1;
/*     */         }
/*     */         
/* 129 */         fnaBaseVoField.setMultipartFormData(bool);
/* 130 */         fnaBaseVoField.setJavaFieldName(str1);
/* 131 */         fnaBaseVoField.setJavaType(str2);
/* 132 */         fnaBaseVoField.setPostParam(pageFieldInfo.isPostParam());
/* 133 */         fnaBaseVoField.setReadValueMode(pageFieldInfo.readValueMode());
/* 134 */         fnaBaseVoField.setName(str);
/* 135 */         fnaBaseVoField.setArray(pageFieldInfo.isArray());
/* 136 */         fnaBaseVoField.setPoClassPropertyName(pageFieldInfo.poClassPropertyName());
/* 137 */         fnaBaseVoField.setAutoTrim(pageFieldInfo.autoTrim());
/* 138 */         fnaBaseVoField.setDynamicQuantityPostParam(pageFieldInfo.isDynamicQuantityPostParam());
/* 139 */         fnaBaseVoField.setEnableIsNullDefaultValue(pageFieldInfo.enableIsNullDefaultValue());
/* 140 */         fnaBaseVoField.setIsNullDefaultValue(pageFieldInfo.isNullDefaultValue());
/*     */         
/* 142 */         if (!"".equals(fnaBaseVoField.getPoClassPropertyName())) {
/* 143 */           String[] arrayOfString = fnaBaseVoField.getPoClassPropertyName().split("\\.");
/* 144 */           StringBuffer stringBuffer = new StringBuffer();
/* 145 */           String str3 = "";
/* 146 */           int i = arrayOfString.length;
/* 147 */           for (byte b1 = 0; b1 < i; b1++) {
/* 148 */             if (i - 1 == b1) {
/* 149 */               str3 = arrayOfString[b1];
/*     */               break;
/*     */             } 
/* 152 */             if (b1 > 0) {
/* 153 */               stringBuffer.append(".");
/*     */             }
/* 155 */             stringBuffer.append(arrayOfString[b1]);
/*     */           } 
/* 157 */           fnaBaseVoField.setPoClassName(stringBuffer.toString());
/* 158 */           fnaBaseVoField.setPoPropertyName(str3);
/*     */         } 
/*     */       } 
/*     */     } 
/*     */     
/* 163 */     if (!VO_CLASS_FIELD_INFO.contains(fnaBaseVo.className))
/* 164 */       VO_CLASS_FIELD_INFO.put(fnaBaseVo.className, fnaBaseVo); 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/fna/e9/vo/base/FnaBaseVo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */