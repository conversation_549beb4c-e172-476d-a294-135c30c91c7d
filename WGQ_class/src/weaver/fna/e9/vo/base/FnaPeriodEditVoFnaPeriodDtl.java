/*     */ package weaver.fna.e9.vo.base;
/*     */ 
/*     */ import weaver.fna.e9.vo.annotation.PageFieldInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ @Deprecated
/*     */ public class FnaPeriodEditVoFnaPeriodDtl
/*     */ {
/*     */   static {
/*  21 */     FnaBaseVo.initStatic(FnaPeriodEditVoFnaPeriodDtl.class);
/*     */   }
/*     */   
/*     */   @Deprecated
/*     */   @PageFieldInfo(poClassPropertyName = "weaver.fna.e9.po.base.FnaPeriodDtl.fnaPeriodDtlPk", isDynamicQuantityPostParam = true)
/*  26 */   String fnaPeriodDtlPk = "";
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   @PageFieldInfo(poClassPropertyName = "weaver.fna.e9.po.base.FnaPeriodDtl.fnaPeriodPk", isDynamicQuantityPostParam = true)
/*  32 */   String fnaPeriodPk = "";
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   @PageFieldInfo(poClassPropertyName = "weaver.fna.e9.po.base.FnaPeriodDtl.startdate", isDynamicQuantityPostParam = true)
/*  38 */   String startdate = "";
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   @PageFieldInfo(poClassPropertyName = "weaver.fna.e9.po.base.FnaPeriodDtl.enddate", isDynamicQuantityPostParam = true)
/*  44 */   String enddate = "";
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   @PageFieldInfo(poClassPropertyName = "weaver.fna.e9.po.base.FnaPeriodDtl.fnaPeriodsList", isDynamicQuantityPostParam = true)
/*  50 */   Integer fnaPeriodsList = null;
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   @PageFieldInfo(poClassPropertyName = "weaver.fna.e9.po.base.FnaPeriodDtl.fnaPeriodsName", isDynamicQuantityPostParam = true)
/*  56 */   String fnaPeriodsName = "";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public String getFnaPeriodsName() {
/*  65 */     return this.fnaPeriodsName;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setFnaPeriodsName(String paramString) {
/*  73 */     this.fnaPeriodsName = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public String getFnaPeriodDtlPk() {
/*  81 */     return this.fnaPeriodDtlPk;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setFnaPeriodDtlPk(String paramString) {
/*  89 */     this.fnaPeriodDtlPk = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public String getFnaPeriodPk() {
/*  97 */     return this.fnaPeriodPk;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setFnaPeriodPk(String paramString) {
/* 105 */     this.fnaPeriodPk = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public String getStartdate() {
/* 113 */     return this.startdate;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setStartdate(String paramString) {
/* 121 */     this.startdate = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public String getEnddate() {
/* 129 */     return this.enddate;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setEnddate(String paramString) {
/* 137 */     this.enddate = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public Integer getFnaPeriodsList() {
/* 145 */     return this.fnaPeriodsList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setFnaPeriodsList(Integer paramInteger) {
/* 153 */     this.fnaPeriodsList = paramInteger;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/fna/e9/vo/base/FnaPeriodEditVoFnaPeriodDtl.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */