/*     */ package weaver.fna.e9.vo.base;
/*     */ 
/*     */ import weaver.fna.e9.vo.annotation.PageFieldInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public final class FnaBaseVoField
/*     */ {
/*     */   private boolean isMultipartFormData = false;
/*     */   private boolean enableIsNullDefaultValue = false;
/*  28 */   private String isNullDefaultValue = "";
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private boolean isDynamicQuantityPostParam = false;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private boolean isPostParam = false;
/*     */ 
/*     */ 
/*     */   
/*  42 */   private PageFieldInfo.ReadValueMode readValueMode = PageFieldInfo.ReadValueMode.REQUEST;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  47 */   private String name = "";
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private boolean isArray = false;
/*     */ 
/*     */ 
/*     */   
/*  56 */   private String poClassPropertyName = "";
/*     */ 
/*     */ 
/*     */   
/*  60 */   private String poClassName = "";
/*     */ 
/*     */ 
/*     */   
/*  64 */   private String poPropertyName = "";
/*     */ 
/*     */ 
/*     */   
/*  68 */   private String javaType = "";
/*     */ 
/*     */ 
/*     */   
/*     */   private String javaFieldName;
/*     */ 
/*     */ 
/*     */   
/*     */   private boolean autoTrim = true;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean isMultipartFormData() {
/*  82 */     return this.isMultipartFormData;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setMultipartFormData(boolean paramBoolean) {
/*  89 */     this.isMultipartFormData = paramBoolean;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean isDynamicQuantityPostParam() {
/*  96 */     return this.isDynamicQuantityPostParam;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setDynamicQuantityPostParam(boolean paramBoolean) {
/* 103 */     this.isDynamicQuantityPostParam = paramBoolean;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean isAutoTrim() {
/* 110 */     return this.autoTrim;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setAutoTrim(boolean paramBoolean) {
/* 117 */     this.autoTrim = paramBoolean;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getJavaFieldName() {
/* 124 */     return this.javaFieldName;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setJavaFieldName(String paramString) {
/* 131 */     this.javaFieldName = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getJavaType() {
/* 138 */     return this.javaType;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setJavaType(String paramString) {
/* 145 */     this.javaType = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getPoClassName() {
/* 152 */     return this.poClassName;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setPoClassName(String paramString) {
/* 159 */     this.poClassName = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getPoPropertyName() {
/* 166 */     return this.poPropertyName;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setPoPropertyName(String paramString) {
/* 173 */     this.poPropertyName = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getPoClassPropertyName() {
/* 180 */     return this.poClassPropertyName;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setPoClassPropertyName(String paramString) {
/* 187 */     this.poClassPropertyName = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean isPostParam() {
/* 194 */     return this.isPostParam;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setPostParam(boolean paramBoolean) {
/* 201 */     this.isPostParam = paramBoolean;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public PageFieldInfo.ReadValueMode getReadValueMode() {
/* 208 */     return this.readValueMode;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setReadValueMode(PageFieldInfo.ReadValueMode paramReadValueMode) {
/* 215 */     this.readValueMode = paramReadValueMode;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getName() {
/* 222 */     return this.name;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setName(String paramString) {
/* 229 */     this.name = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean isArray() {
/* 236 */     return this.isArray;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setArray(boolean paramBoolean) {
/* 243 */     this.isArray = paramBoolean;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean isEnableIsNullDefaultValue() {
/* 250 */     return this.enableIsNullDefaultValue;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setEnableIsNullDefaultValue(boolean paramBoolean) {
/* 257 */     this.enableIsNullDefaultValue = paramBoolean;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getIsNullDefaultValue() {
/* 264 */     return this.isNullDefaultValue;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setIsNullDefaultValue(String paramString) {
/* 271 */     this.isNullDefaultValue = paramString;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/fna/e9/vo/base/FnaBaseVoField.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */