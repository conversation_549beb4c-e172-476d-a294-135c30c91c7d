/*     */ package weaver.fna.e9.vo.base;
/*     */ 
/*     */ import weaver.fna.e9.vo.annotation.PageFieldInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class FnaPaymentModeVo
/*     */ {
/*     */   static {
/*  22 */     FnaBaseVo.initStatic(FnaPaymentModeVo.class);
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   @PageFieldInfo(poClassPropertyName = "weaver.fna.e9.po.base.FnaPaymentMode.id", enableIsNullDefaultValue = true, isNullDefaultValue = "0")
/*  28 */   private Integer id = null;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @PageFieldInfo(poClassPropertyName = "weaver.fna.e9.po.base.FnaPaymentMode.name", enableIsNullDefaultValue = true, isNullDefaultValue = "")
/*  34 */   private String name = "";
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @PageFieldInfo(poClassPropertyName = "weaver.fna.e9.po.base.FnaPaymentMode.codeName", enableIsNullDefaultValue = true, isNullDefaultValue = "")
/*  40 */   private String codeName = "";
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @PageFieldInfo(poClassPropertyName = "weaver.fna.e9.po.base.FnaPaymentMode.state", enableIsNullDefaultValue = true, isNullDefaultValue = "1")
/*  46 */   private Integer state = null;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @PageFieldInfo(poClassPropertyName = "weaver.fna.e9.po.base.FnaPaymentMode.remark", enableIsNullDefaultValue = true, isNullDefaultValue = "")
/*  52 */   private String remark = "";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @PageFieldInfo(poClassPropertyName = "weaver.fna.e9.po.base.FnaPaymentMode.showOrder")
/*  59 */   private Double showOrder = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Integer getId() {
/*  67 */     return this.id;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setId(Integer paramInteger) {
/*  75 */     this.id = paramInteger;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getName() {
/*  83 */     return this.name;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setName(String paramString) {
/*  91 */     this.name = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCodeName() {
/*  99 */     return this.codeName;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setCodeName(String paramString) {
/* 107 */     this.codeName = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Integer getState() {
/* 115 */     return this.state;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setState(Integer paramInteger) {
/* 123 */     this.state = paramInteger;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getRemark() {
/* 131 */     return this.remark;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setRemark(String paramString) {
/* 139 */     this.remark = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Double getShowOrder() {
/* 147 */     return this.showOrder;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setShowOrder(Double paramDouble) {
/* 155 */     this.showOrder = paramDouble;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/fna/e9/vo/base/FnaPaymentModeVo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */