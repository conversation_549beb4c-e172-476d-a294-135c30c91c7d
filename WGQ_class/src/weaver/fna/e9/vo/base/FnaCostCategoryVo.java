/*     */ package weaver.fna.e9.vo.base;
/*     */ 
/*     */ import weaver.fna.e9.vo.annotation.PageFieldInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class FnaCostCategoryVo
/*     */ {
/*     */   static {
/*  22 */     FnaBaseVo.initStatic(FnaCostCategoryVo.class);
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   @PageFieldInfo(poClassPropertyName = "weaver.fna.e9.po.base.FnaCostCategory.workflowIds", enableIsNullDefaultValue = true, isNullDefaultValue = "")
/*  28 */   private String workflowIds = "";
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @PageFieldInfo(poClassPropertyName = "weaver.fna.e9.po.base.FnaCostCategory.id", enableIsNullDefaultValue = true, isNullDefaultValue = "0")
/*  34 */   private Integer id = null;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @PageFieldInfo(poClassPropertyName = "weaver.fna.e9.po.base.FnaCostCategory.name", enableIsNullDefaultValue = true, isNullDefaultValue = "")
/*  40 */   private String name = "";
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @PageFieldInfo(poClassPropertyName = "weaver.fna.e9.po.base.FnaCostCategory.codeName", enableIsNullDefaultValue = true, isNullDefaultValue = "")
/*  46 */   private String codeName = "";
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @PageFieldInfo(poClassPropertyName = "weaver.fna.e9.po.base.FnaCostCategory.state", enableIsNullDefaultValue = true, isNullDefaultValue = "1")
/*  52 */   private Integer state = null;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @PageFieldInfo(poClassPropertyName = "weaver.fna.e9.po.base.FnaCostCategory.remark", enableIsNullDefaultValue = true, isNullDefaultValue = "")
/*  58 */   private String remark = "";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @PageFieldInfo(poClassPropertyName = "weaver.fna.e9.po.base.FnaCostCategory.showOrder")
/*  65 */   private Double showOrder = null;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @PageFieldInfo(poClassPropertyName = "weaver.fna.e9.po.base.FnaCostCategory.costCategoryType", enableIsNullDefaultValue = true, isNullDefaultValue = "0")
/*  71 */   private Integer costCategoryType = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Integer getId() {
/*  79 */     return this.id;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setId(Integer paramInteger) {
/*  87 */     this.id = paramInteger;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getName() {
/*  95 */     return this.name;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setName(String paramString) {
/* 103 */     this.name = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCodeName() {
/* 111 */     return this.codeName;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setCodeName(String paramString) {
/* 119 */     this.codeName = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Integer getState() {
/* 127 */     return this.state;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setState(Integer paramInteger) {
/* 135 */     this.state = paramInteger;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getRemark() {
/* 143 */     return this.remark;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setRemark(String paramString) {
/* 151 */     this.remark = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Double getShowOrder() {
/* 159 */     return this.showOrder;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setShowOrder(Double paramDouble) {
/* 167 */     this.showOrder = paramDouble;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getWorkflowIds() {
/* 174 */     return this.workflowIds;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setWorkflowIds(String paramString) {
/* 181 */     this.workflowIds = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Integer getCostCategoryType() {
/* 188 */     return this.costCategoryType;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setCostCategoryType(Integer paramInteger) {
/* 195 */     this.costCategoryType = paramInteger;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/fna/e9/vo/base/FnaCostCategoryVo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */