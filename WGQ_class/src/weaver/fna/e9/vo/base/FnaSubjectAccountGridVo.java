/*     */ package weaver.fna.e9.vo.base;
/*     */ 
/*     */ import weaver.fna.e9.vo.annotation.PageFieldInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ @Deprecated
/*     */ public class FnaSubjectAccountGridVo
/*     */ {
/*     */   static {
/*  21 */     FnaBaseVo.initStatic(FnaSubjectAccountGridVo.class);
/*     */   }
/*     */   
/*     */   @Deprecated
/*     */   @PageFieldInfo(poClassPropertyName = "weaver.fna.e9.po.base.FnaSubjectAccount.fnaPeriodPk")
/*  26 */   String fnaPeriodPk = "";
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   @PageFieldInfo(poClassPropertyName = "weaver.fna.e9.po.base.FnaSubjectAccount.subjectaName")
/*  32 */   String subjectaName = "";
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   @PageFieldInfo(poClassPropertyName = "weaver.fna.e9.po.base.FnaSubjectAccount.subjectaCode")
/*  38 */   String subjectaCode = "";
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   @PageFieldInfo(poClassPropertyName = "weaver.fna.e9.po.base.FnaSubjectAccount.lockedStatus")
/*  44 */   Integer lockedStatus = null;
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   @PageFieldInfo(poClassPropertyName = "weaver.fna.e9.po.base.FnaSubjectAccount.fnaSubjectaPk")
/*  50 */   String fnaSubjectaPk = "";
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   @PageFieldInfo(isPostParam = false)
/*  57 */   String splitPageTagTableString = "";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public String getFnaSubjectaPk() {
/*  66 */     return this.fnaSubjectaPk;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setFnaSubjectaPk(String paramString) {
/*  74 */     this.fnaSubjectaPk = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public String getFnaPeriodPk() {
/*  82 */     return this.fnaPeriodPk;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setFnaPeriodPk(String paramString) {
/*  90 */     this.fnaPeriodPk = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public String getSubjectaName() {
/*  98 */     return this.subjectaName;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setSubjectaName(String paramString) {
/* 106 */     this.subjectaName = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public String getSubjectaCode() {
/* 114 */     return this.subjectaCode;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setSubjectaCode(String paramString) {
/* 122 */     this.subjectaCode = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public Integer getLockedStatus() {
/* 130 */     return this.lockedStatus;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setLockedStatus(Integer paramInteger) {
/* 138 */     this.lockedStatus = paramInteger;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public String getSplitPageTagTableString() {
/* 146 */     return this.splitPageTagTableString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setSplitPageTagTableString(String paramString) {
/* 154 */     this.splitPageTagTableString = paramString;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/fna/e9/vo/base/FnaSubjectAccountGridVo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */