/*     */ package weaver.fna.e9.vo.base;
/*     */ 
/*     */ import weaver.fna.e9.vo.annotation.PageFieldInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ @Deprecated
/*     */ public class FnaSubjectBudgetEditVo
/*     */ {
/*     */   static {
/*  21 */     FnaBaseVo.initStatic(FnaSubjectBudgetEditVo.class);
/*     */   }
/*     */   
/*     */   @Deprecated
/*     */   @PageFieldInfo(poClassPropertyName = "weaver.fna.e9.po.base.FnaSubjectBudget.fnaSubjectbPk")
/*  26 */   String fnaSubjectbPk = "";
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   @PageFieldInfo(poClassPropertyName = "weaver.fna.e9.po.base.FnaSubjectBudget.fnaPeriodPk")
/*  32 */   String fnaPeriodPk = "";
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   @PageFieldInfo(poClassPropertyName = "weaver.fna.e9.po.base.FnaSubjectBudget.supFnaSubjectbPk")
/*  38 */   String supFnaSubjectbPk = "";
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   @PageFieldInfo(isPostParam = false)
/*  44 */   String supFnaSubjectbName = "";
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   @PageFieldInfo(poClassPropertyName = "weaver.fna.e9.po.base.FnaSubjectBudget.subjectbName")
/*  50 */   String subjectbName = "";
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   @PageFieldInfo(poClassPropertyName = "weaver.fna.e9.po.base.FnaSubjectBudget.subjectbCode")
/*  56 */   String subjectbCode = "";
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   @PageFieldInfo(poClassPropertyName = "weaver.fna.e9.po.base.FnaSubjectBudget.lockedStatus")
/*  62 */   Integer lockedStatus = null;
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   @PageFieldInfo(poClassPropertyName = "weaver.fna.e9.po.base.FnaSubjectBudget.description")
/*  68 */   String description = "";
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   @PageFieldInfo(poClassPropertyName = "weaver.fna.e9.po.base.FnaSubjectBudget.showOrder")
/*  74 */   Double showOrder = null;
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   @PageFieldInfo(isPostParam = false)
/*  80 */   String supFnaSubjectbPk_browserUrl = "";
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   @PageFieldInfo(isPostParam = false)
/*  86 */   String fnaSubjectaPk_browserUrl = "";
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   @PageFieldInfo(poClassPropertyName = "weaver.fna.e9.po.base.FnaSubjectBudget.fnaSubjectaPk")
/*  93 */   String fnaSubjectaPk = "";
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   @PageFieldInfo(isPostParam = false)
/*  99 */   String fnaSubjectaName = "";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public String getFnaPeriodPk() {
/* 109 */     return this.fnaPeriodPk;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setFnaPeriodPk(String paramString) {
/* 117 */     this.fnaPeriodPk = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public String getFnaSubjectaName() {
/* 125 */     return this.fnaSubjectaName;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setFnaSubjectaName(String paramString) {
/* 133 */     this.fnaSubjectaName = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public String getFnaSubjectaPk() {
/* 141 */     return this.fnaSubjectaPk;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setFnaSubjectaPk(String paramString) {
/* 149 */     this.fnaSubjectaPk = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public String getFnaSubjectbPk() {
/* 157 */     return this.fnaSubjectbPk;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setFnaSubjectbPk(String paramString) {
/* 165 */     this.fnaSubjectbPk = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public String getSupFnaSubjectbPk() {
/* 173 */     return this.supFnaSubjectbPk;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setSupFnaSubjectbPk(String paramString) {
/* 181 */     this.supFnaSubjectbPk = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public String getSupFnaSubjectbName() {
/* 189 */     return this.supFnaSubjectbName;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setSupFnaSubjectbName(String paramString) {
/* 197 */     this.supFnaSubjectbName = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public String getSubjectbName() {
/* 205 */     return this.subjectbName;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setSubjectbName(String paramString) {
/* 213 */     this.subjectbName = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public String getSubjectbCode() {
/* 221 */     return this.subjectbCode;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setSubjectbCode(String paramString) {
/* 229 */     this.subjectbCode = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public Integer getLockedStatus() {
/* 237 */     return this.lockedStatus;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setLockedStatus(Integer paramInteger) {
/* 245 */     this.lockedStatus = paramInteger;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public String getDescription() {
/* 253 */     return this.description;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setDescription(String paramString) {
/* 261 */     this.description = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public Double getShowOrder() {
/* 269 */     return this.showOrder;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setShowOrder(Double paramDouble) {
/* 277 */     this.showOrder = paramDouble;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public String getSupFnaSubjectbPk_browserUrl() {
/* 285 */     return this.supFnaSubjectbPk_browserUrl;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setSupFnaSubjectbPk_browserUrl(String paramString) {
/* 293 */     this.supFnaSubjectbPk_browserUrl = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public String getFnaSubjectaPk_browserUrl() {
/* 301 */     return this.fnaSubjectaPk_browserUrl;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setFnaSubjectaPk_browserUrl(String paramString) {
/* 309 */     this.fnaSubjectaPk_browserUrl = paramString;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/fna/e9/vo/base/FnaSubjectBudgetEditVo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */