/*     */ package weaver.fna.e9.vo.base;
/*     */ 
/*     */ import weaver.fna.e9.vo.annotation.PageFieldInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class FnaCityLevelVo
/*     */ {
/*     */   static {
/*  22 */     FnaBaseVo.initStatic(FnaCityLevelVo.class);
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   @PageFieldInfo(poClassPropertyName = "weaver.fna.e9.po.base.FnaCityLevel.cityIds", enableIsNullDefaultValue = true, isNullDefaultValue = "")
/*  28 */   private String cityIds = "";
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCityIds() {
/*  34 */     return this.cityIds;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public void setCityIds(String paramString) {
/*  40 */     this.cityIds = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @PageFieldInfo(poClassPropertyName = "weaver.fna.e9.po.base.FnaCityLevel.id", enableIsNullDefaultValue = true, isNullDefaultValue = "0")
/*  47 */   private Integer id = null;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @PageFieldInfo(poClassPropertyName = "weaver.fna.e9.po.base.FnaCityLevel.name", enableIsNullDefaultValue = true, isNullDefaultValue = "")
/*  53 */   private String name = "";
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @PageFieldInfo(poClassPropertyName = "weaver.fna.e9.po.base.FnaCityLevel.codeName", enableIsNullDefaultValue = true, isNullDefaultValue = "")
/*  59 */   private String codeName = "";
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @PageFieldInfo(poClassPropertyName = "weaver.fna.e9.po.base.FnaCityLevel.state", enableIsNullDefaultValue = true, isNullDefaultValue = "1")
/*  65 */   private Integer state = null;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @PageFieldInfo(poClassPropertyName = "weaver.fna.e9.po.base.FnaCityLevel.remark", enableIsNullDefaultValue = true, isNullDefaultValue = "")
/*  71 */   private String remark = "";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @PageFieldInfo(poClassPropertyName = "weaver.fna.e9.po.base.FnaCityLevel.showOrder")
/*  78 */   private Double showOrder = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Integer getId() {
/*  86 */     return this.id;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setId(Integer paramInteger) {
/*  94 */     this.id = paramInteger;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getName() {
/* 102 */     return this.name;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setName(String paramString) {
/* 110 */     this.name = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCodeName() {
/* 118 */     return this.codeName;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setCodeName(String paramString) {
/* 126 */     this.codeName = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Integer getState() {
/* 134 */     return this.state;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setState(Integer paramInteger) {
/* 142 */     this.state = paramInteger;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getRemark() {
/* 150 */     return this.remark;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setRemark(String paramString) {
/* 158 */     this.remark = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Double getShowOrder() {
/* 166 */     return this.showOrder;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setShowOrder(Double paramDouble) {
/* 174 */     this.showOrder = paramDouble;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/fna/e9/vo/base/FnaCityLevelVo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */