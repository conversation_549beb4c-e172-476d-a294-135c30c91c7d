/*     */ package weaver.fna.e9.vo.base;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.List;
/*     */ import weaver.fna.e9.vo.annotation.PageFieldInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ @Deprecated
/*     */ public class FnaPeriodEditVo
/*     */ {
/*     */   static {
/*  24 */     FnaBaseVo.initStatic(FnaPeriodEditVo.class);
/*     */   }
/*     */   
/*     */   @Deprecated
/*     */   @PageFieldInfo(poClassPropertyName = "weaver.fna.e9.po.base.FnaPeriod.fnaPeriodPk")
/*  29 */   String fnaPeriodPk = "";
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   @PageFieldInfo(poClassPropertyName = "weaver.fna.e9.po.base.FnaPeriod.fnaPeriodName")
/*  35 */   String fnaPeriodName = "";
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   @PageFieldInfo(poClassPropertyName = "weaver.fna.e9.po.base.FnaPeriod.fnaCycle")
/*  41 */   Integer fnaCycle = null;
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   @PageFieldInfo(poClassPropertyName = "weaver.fna.e9.po.base.FnaPeriod.startdate")
/*  47 */   String startdate = "";
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   @PageFieldInfo(poClassPropertyName = "weaver.fna.e9.po.base.FnaPeriod.enddate")
/*  53 */   String enddate = "";
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   @PageFieldInfo(poClassPropertyName = "weaver.fna.e9.po.base.FnaPeriod.status")
/*  59 */   Integer status = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   @PageFieldInfo(poClassPropertyName = "weaver.fna.e9.po.base.FnaPeriod.showOrder")
/*  67 */   Double showOrder = Double.valueOf(0.0D);
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   @PageFieldInfo(isPostParam = false)
/*     */   boolean showBtnEffect = false;
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   @PageFieldInfo(isPostParam = false)
/*     */   boolean showBtnClose = false;
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   @PageFieldInfo(isPostParam = false)
/*     */   boolean showBtnReopen = false;
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   @PageFieldInfo(isPostParam = false)
/*  91 */   String splitPageTagTableString = "";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public boolean isShowBtnEffect() {
/* 100 */     return this.showBtnEffect;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setShowBtnEffect(boolean paramBoolean) {
/* 108 */     this.showBtnEffect = paramBoolean;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public boolean isShowBtnClose() {
/* 116 */     return this.showBtnClose;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setShowBtnClose(boolean paramBoolean) {
/* 124 */     this.showBtnClose = paramBoolean;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public boolean isShowBtnReopen() {
/* 132 */     return this.showBtnReopen;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setShowBtnReopen(boolean paramBoolean) {
/* 140 */     this.showBtnReopen = paramBoolean;
/*     */   }
/*     */   
/*     */   @Deprecated
/*     */   @PageFieldInfo(isPostParam = false, isDynamicQuantityPostParam = true)
/* 145 */   List<FnaPeriodEditVoFnaPeriodDtl> fnaPeriodEditVoFnaPeriodDtlList = new ArrayList<>();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public Double getShowOrder() {
/* 154 */     return this.showOrder;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setShowOrder(Double paramDouble) {
/* 162 */     this.showOrder = paramDouble;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public String getSplitPageTagTableString() {
/* 170 */     return this.splitPageTagTableString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setSplitPageTagTableString(String paramString) {
/* 178 */     this.splitPageTagTableString = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public Integer getFnaCycle() {
/* 186 */     return this.fnaCycle;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setFnaCycle(Integer paramInteger) {
/* 194 */     this.fnaCycle = paramInteger;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public String getFnaPeriodPk() {
/* 202 */     return this.fnaPeriodPk;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setFnaPeriodPk(String paramString) {
/* 210 */     this.fnaPeriodPk = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public String getFnaPeriodName() {
/* 218 */     return this.fnaPeriodName;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setFnaPeriodName(String paramString) {
/* 226 */     this.fnaPeriodName = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public String getStartdate() {
/* 234 */     return this.startdate;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setStartdate(String paramString) {
/* 242 */     this.startdate = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public String getEnddate() {
/* 250 */     return this.enddate;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setEnddate(String paramString) {
/* 258 */     this.enddate = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public Integer getStatus() {
/* 266 */     return this.status;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setStatus(Integer paramInteger) {
/* 274 */     this.status = paramInteger;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public List<FnaPeriodEditVoFnaPeriodDtl> getFnaPeriodEditVoFnaPeriodDtlList() {
/* 282 */     return this.fnaPeriodEditVoFnaPeriodDtlList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setFnaPeriodEditVoFnaPeriodDtlList(List<FnaPeriodEditVoFnaPeriodDtl> paramList) {
/* 291 */     this.fnaPeriodEditVoFnaPeriodDtlList = paramList;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/fna/e9/vo/base/FnaPeriodEditVo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */