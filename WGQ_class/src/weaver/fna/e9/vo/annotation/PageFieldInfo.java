/*    */ package weaver.fna.e9.vo.annotation;
/*    */ 
/*    */ import java.lang.annotation.Documented;
/*    */ import java.lang.annotation.ElementType;
/*    */ import java.lang.annotation.Retention;
/*    */ import java.lang.annotation.RetentionPolicy;
/*    */ import java.lang.annotation.Target;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ @Target({ElementType.FIELD})
/*    */ @Retention(RetentionPolicy.RUNTIME)
/*    */ @Documented
/*    */ public @interface PageFieldInfo
/*    */ {
/*    */   boolean enableIsNullDefaultValue() default false;
/*    */   
/*    */   String isNullDefaultValue() default "";
/*    */   
/*    */   boolean isDynamicQuantityPostParam() default false;
/*    */   
/*    */   boolean isPostParam() default true;
/*    */   
/*    */   ReadValueMode readValueMode() default ReadValueMode.REQUEST;
/*    */   
/*    */   String name() default "";
/*    */   
/*    */   boolean isArray() default false;
/*    */   
/*    */   String poClassPropertyName() default "";
/*    */   
/*    */   boolean autoTrim() default true;
/*    */   
/*    */   public enum ReadValueMode
/*    */   {
/* 48 */     REQUEST, SESSION;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/fna/e9/vo/annotation/PageFieldInfo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */