/*     */ package weaver.fna.e9.bo.base;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import weaver.conn.RecordSetTrans;
/*     */ import weaver.fna.e9.dao.base.FnaBaseDao;
/*     */ import weaver.fna.e9.exception.FnaException;
/*     */ import weaver.fna.e9.po.base.FnaCostCategoryFieldSet;
/*     */ import weaver.fna.general.RecordSet4Action;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class FnaCostCategoryFieldSetBo
/*     */ {
/*  31 */   BaseBean bb = new BaseBean();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void load_workflowFieldInfo_for_fnaCostCategoryFieldSet(RecordSet4Action paramRecordSet4Action, FnaCostCategoryFieldSet paramFnaCostCategoryFieldSet, int paramInt1, int paramInt2) throws Exception {
/*  42 */     boolean bool = paramRecordSet4Action.executeQuery("select a.detailtable, a.billid from workflow_billfield a where a.id = ?", new Object[] { Integer.valueOf(paramInt1) });
/*  43 */     if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(paramInt2))); 
/*  44 */     if (paramRecordSet4Action.next()) {
/*  45 */       String str = Util.null2String(paramRecordSet4Action.getString("detailtable"));
/*  46 */       int i = Util.getIntValue(paramRecordSet4Action.getString("billid"), 0);
/*     */       
/*  48 */       int j = Math.abs(i);
/*  49 */       boolean bool1 = "".equals(str) ? false : true;
/*  50 */       int k = 0;
/*  51 */       if (bool1 == true) {
/*  52 */         k = Util.getIntValue(str.toLowerCase().replaceAll("formtable_main_" + j + "_dt", ""), -1);
/*     */       }
/*     */       
/*  55 */       paramFnaCostCategoryFieldSet.setFieldId(Integer.valueOf(paramInt1));
/*  56 */       paramFnaCostCategoryFieldSet.setIsDtl(Integer.valueOf(bool1));
/*  57 */       paramFnaCostCategoryFieldSet.setDtlNumber(Integer.valueOf(k));
/*     */     } else {
/*  59 */       paramFnaCostCategoryFieldSet.setFieldId(Integer.valueOf(0));
/*  60 */       paramFnaCostCategoryFieldSet.setIsDtl(Integer.valueOf(0));
/*  61 */       paramFnaCostCategoryFieldSet.setDtlNumber(Integer.valueOf(0));
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public FnaCostCategoryFieldSet loadData(RecordSet4Action paramRecordSet4Action, int paramInt1, int paramInt2, int paramInt3, int paramInt4) throws Exception {
/*  76 */     List<FnaCostCategoryFieldSet> list = queryData(paramRecordSet4Action, paramInt1, paramInt2, paramInt3, paramInt4);
/*  77 */     if (list.size() > 0) {
/*  78 */       return list.get(0);
/*     */     }
/*  80 */     return null;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<FnaCostCategoryFieldSet> queryData(RecordSet4Action paramRecordSet4Action, int paramInt1, int paramInt2, int paramInt3, int paramInt4) throws Exception {
/*  94 */     FnaBaseDao fnaBaseDao = new FnaBaseDao();
/*  95 */     ArrayList<Integer> arrayList = new ArrayList();
/*     */     
/*  97 */     StringBuffer stringBuffer = new StringBuffer("select a.* from FnaCostCategoryFieldSet a where 1=1 ");
/*  98 */     if (paramInt1 > 0) {
/*  99 */       stringBuffer.append(" and a.fnaCostCategoryId=?");
/* 100 */       arrayList.add(Integer.valueOf(paramInt1));
/*     */     } 
/* 102 */     if (paramInt2 > 0) {
/* 103 */       stringBuffer.append(" and a.workflowId=?");
/* 104 */       arrayList.add(Integer.valueOf(paramInt2));
/*     */     } 
/* 106 */     if (paramInt3 > 0) {
/* 107 */       stringBuffer.append(" and a.fieldId=?");
/* 108 */       arrayList.add(Integer.valueOf(paramInt3));
/*     */     } 
/* 110 */     stringBuffer.append(" order by a.showOrder, a.name, a.id");
/* 111 */     Object[] arrayOfObject1 = new Object[arrayList.size()];
/* 112 */     Object[] arrayOfObject2 = arrayList.toArray(arrayOfObject1);
/*     */     
/* 114 */     return fnaBaseDao.queryForObjectList(paramRecordSet4Action, FnaCostCategoryFieldSet.class.getName(), stringBuffer.toString(), arrayOfObject2);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int queryCount(RecordSet4Action paramRecordSet4Action, int paramInt1, int paramInt2, int paramInt3, int paramInt4) throws Exception {
/* 128 */     ArrayList<Integer> arrayList = new ArrayList();
/*     */     
/* 130 */     StringBuffer stringBuffer = new StringBuffer("select count(*) cnt from FnaCostCategoryFieldSet a where 1=1 ");
/* 131 */     if (paramInt1 > 0) {
/* 132 */       stringBuffer.append(" and a.fnaCostCategoryId=?");
/* 133 */       arrayList.add(Integer.valueOf(paramInt1));
/*     */     } 
/* 135 */     if (paramInt2 > 0) {
/* 136 */       stringBuffer.append(" and a.workflowId=?");
/* 137 */       arrayList.add(Integer.valueOf(paramInt2));
/*     */     } 
/* 139 */     if (paramInt3 > 0) {
/* 140 */       stringBuffer.append(" and a.fieldId=?");
/* 141 */       arrayList.add(Integer.valueOf(paramInt3));
/*     */     } 
/* 143 */     Object[] arrayOfObject1 = new Object[arrayList.size()];
/* 144 */     Object[] arrayOfObject2 = arrayList.toArray(arrayOfObject1);
/*     */     
/* 146 */     boolean bool = paramRecordSet4Action.executeQuery(stringBuffer.toString(), arrayOfObject2);
/* 147 */     if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(paramInt4))); 
/* 148 */     if (paramRecordSet4Action.next()) {
/* 149 */       return paramRecordSet4Action.getInt("cnt").intValue();
/*     */     }
/* 151 */     return 0;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void createData(RecordSetTrans paramRecordSetTrans, FnaCostCategoryFieldSet paramFnaCostCategoryFieldSet, int paramInt, HashMap<String, Object> paramHashMap) throws Exception {
/* 164 */     RecordSet4Action recordSet4Action = new RecordSet4Action(paramRecordSetTrans);
/* 165 */     verifyFnaCostCategoryId(paramFnaCostCategoryFieldSet.getFnaCostCategoryId().intValue(), paramInt);
/* 166 */     verifyWorkflowId(paramFnaCostCategoryFieldSet.getWorkflowId().intValue(), paramInt);
/* 167 */     verifyName(recordSet4Action, paramFnaCostCategoryFieldSet.getName(), paramFnaCostCategoryFieldSet.getId().intValue(), paramFnaCostCategoryFieldSet.getFnaCostCategoryId().intValue(), paramInt);
/*     */     
/* 169 */     FnaBaseDao fnaBaseDao = new FnaBaseDao();
/* 170 */     fnaBaseDao.saveObject(recordSet4Action, paramFnaCostCategoryFieldSet);
/* 171 */     paramHashMap.put("needRollback", "true");
/*     */     
/* 173 */     boolean bool = paramRecordSetTrans.executeQuery("select max(a.id) maxId from FnaCostCategoryFieldSet a where fnaCostCategoryId=? and workflowId=? and fieldId=?", new Object[] { paramFnaCostCategoryFieldSet
/* 174 */           .getFnaCostCategoryId(), paramFnaCostCategoryFieldSet.getWorkflowId(), paramFnaCostCategoryFieldSet.getFieldId() });
/* 175 */     if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(paramInt))); 
/* 176 */     if (paramRecordSetTrans.next()) {
/* 177 */       paramFnaCostCategoryFieldSet.setId(Integer.valueOf(paramRecordSetTrans.getInt("maxId")));
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void updateData(RecordSetTrans paramRecordSetTrans, FnaCostCategoryFieldSet paramFnaCostCategoryFieldSet, int paramInt, HashMap<String, Object> paramHashMap) throws Exception {
/* 190 */     RecordSet4Action recordSet4Action = new RecordSet4Action(paramRecordSetTrans);
/* 191 */     verifyFnaCostCategoryId(paramFnaCostCategoryFieldSet.getFnaCostCategoryId().intValue(), paramInt);
/* 192 */     verifyWorkflowId(paramFnaCostCategoryFieldSet.getWorkflowId().intValue(), paramInt);
/* 193 */     verifyFieldId(recordSet4Action, paramFnaCostCategoryFieldSet.getFieldId().intValue(), paramFnaCostCategoryFieldSet.getIsRequired().intValue(), paramFnaCostCategoryFieldSet
/* 194 */         .getId().intValue(), paramFnaCostCategoryFieldSet.getDtlNumber().intValue(), paramFnaCostCategoryFieldSet.getFnaCostCategoryId().intValue(), paramFnaCostCategoryFieldSet
/* 195 */         .getName(), paramInt);
/* 196 */     verifyName(recordSet4Action, paramFnaCostCategoryFieldSet.getName(), paramFnaCostCategoryFieldSet.getId().intValue(), paramFnaCostCategoryFieldSet.getFnaCostCategoryId().intValue(), paramInt);
/*     */     
/* 198 */     FnaBaseDao fnaBaseDao = new FnaBaseDao();
/* 199 */     fnaBaseDao.updateObject(recordSet4Action, paramFnaCostCategoryFieldSet);
/* 200 */     paramHashMap.put("needRollback", "true");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void deleteData(RecordSetTrans paramRecordSetTrans, int paramInt1, int paramInt2, int paramInt3, int paramInt4, HashMap<String, Object> paramHashMap) throws Exception {
/* 214 */     ArrayList<Integer> arrayList = new ArrayList();
/*     */     
/* 216 */     StringBuffer stringBuffer = new StringBuffer("delete from FnaCostCategoryFieldSet where 1=1");
/* 217 */     if (paramInt1 > 0) {
/* 218 */       stringBuffer.append(" and fnaCostCategoryId=?");
/* 219 */       arrayList.add(Integer.valueOf(paramInt1));
/*     */     } 
/* 221 */     if (paramInt2 > 0) {
/* 222 */       stringBuffer.append(" and workflowId=?");
/* 223 */       arrayList.add(Integer.valueOf(paramInt2));
/*     */     } 
/* 225 */     if (paramInt3 > 0) {
/* 226 */       stringBuffer.append(" and fieldId=?");
/* 227 */       arrayList.add(Integer.valueOf(paramInt3));
/*     */     } 
/* 229 */     Object[] arrayOfObject1 = new Object[arrayList.size()];
/* 230 */     Object[] arrayOfObject2 = arrayList.toArray(arrayOfObject1);
/*     */     
/* 232 */     boolean bool = paramRecordSetTrans.executeUpdate(stringBuffer.toString(), arrayOfObject2);
/* 233 */     if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(paramInt4))); 
/* 234 */     paramHashMap.put("needRollback", "true");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void verifyName(RecordSet4Action paramRecordSet4Action, String paramString, int paramInt1, int paramInt2, int paramInt3) throws Exception {
/* 249 */     if ("".equals(paramString)) {
/* 250 */       throw new FnaException(SystemEnv.getHtmlLabelName(81968, Util.getIntValue(paramInt3)));
/*     */     }
/* 252 */     boolean bool = paramRecordSet4Action.executeQuery("select count(*) cnt from FnaCostCategoryFieldSet a where name=? and id <> ? and fnaCostCategoryId=?", new Object[] { paramString, 
/* 253 */           Integer.valueOf(paramInt1), Integer.valueOf(paramInt2) });
/* 254 */     if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(paramInt3))); 
/* 255 */     if (paramRecordSet4Action.next() && paramRecordSet4Action.getInt("cnt").intValue() > 0) {
/* 256 */       throw new FnaException(SystemEnv.getHtmlLabelName(10000022, Util.getIntValue(paramInt3)).replace("#replaceString#", paramString));
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void verifyFnaCostCategoryId(int paramInt1, int paramInt2) throws Exception {
/* 267 */     if (paramInt1 <= 0) {
/* 268 */       throw new FnaException(SystemEnv.getHtmlLabelName(10000024, Util.getIntValue(paramInt2)));
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void verifyWorkflowId(int paramInt1, int paramInt2) throws Exception {
/* 279 */     if (paramInt1 <= 0) {
/* 280 */       throw new FnaException(SystemEnv.getHtmlLabelName(10000023, Util.getIntValue(paramInt2)));
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void verifyFieldId(RecordSet4Action paramRecordSet4Action, int paramInt1, int paramInt2, int paramInt3, int paramInt4, int paramInt5, String paramString, int paramInt6) throws Exception {
/* 299 */     if (paramInt1 <= 0 && paramInt2 == 1) {
/* 300 */       throw new FnaException("【" + paramString + "】" + SystemEnv.getHtmlLabelName(130927, Util.getIntValue(paramInt6)));
/*     */     }
/* 302 */     boolean bool = paramRecordSet4Action.executeQuery("select count(*) cnt from FnaCostCategoryFieldSet a where a.dtlNumber <> 0 and a.dtlNumber <> ? and a.id <> ? and fnaCostCategoryId=?", new Object[] {
/* 303 */           Integer.valueOf(paramInt4), Integer.valueOf(paramInt3), Integer.valueOf(paramInt5) });
/* 304 */     if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(paramInt6))); 
/* 305 */     if (paramRecordSet4Action.next() && paramRecordSet4Action.getInt("cnt").intValue() > 0) {
/* 306 */       throw new FnaException("【" + paramString + "】" + SystemEnv.getHtmlLabelName(130928, Util.getIntValue(paramInt6)));
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<FnaCostCategoryFieldSet> getFieldInfoListByCostcategorytype(int paramInt) {
/* 316 */     FnaCostCategoryFieldSet fnaCostCategoryFieldSet = null;
/* 317 */     ArrayList<FnaCostCategoryFieldSet> arrayList = new ArrayList();
/* 318 */     if (paramInt == 1) {
/*     */ 
/*     */       
/* 321 */       fnaCostCategoryFieldSet = new FnaCostCategoryFieldSet();
/* 322 */       arrayList.add(fnaCostCategoryFieldSet);
/* 323 */       fnaCostCategoryFieldSet.setName("费用日期");
/* 324 */       fnaCostCategoryFieldSet.setFieldhtmltype(Integer.valueOf(3));
/* 325 */       fnaCostCategoryFieldSet.setType(Integer.valueOf(2));
/* 326 */       fnaCostCategoryFieldSet.setIsRequired(Integer.valueOf(1));
/* 327 */       fnaCostCategoryFieldSet.setShowOrder(Double.valueOf(1.0D));
/*     */       
/* 329 */       fnaCostCategoryFieldSet = new FnaCostCategoryFieldSet();
/* 330 */       arrayList.add(fnaCostCategoryFieldSet);
/* 331 */       fnaCostCategoryFieldSet.setName("发票张数");
/* 332 */       fnaCostCategoryFieldSet.setFieldhtmltype(Integer.valueOf(1));
/* 333 */       fnaCostCategoryFieldSet.setType(Integer.valueOf(2));
/* 334 */       fnaCostCategoryFieldSet.setIsRequired(Integer.valueOf(1));
/* 335 */       fnaCostCategoryFieldSet.setShowOrder(Double.valueOf(2.0D));
/*     */       
/* 337 */       fnaCostCategoryFieldSet = new FnaCostCategoryFieldSet();
/* 338 */       arrayList.add(fnaCostCategoryFieldSet);
/* 339 */       fnaCostCategoryFieldSet.setName("交通工具");
/* 340 */       fnaCostCategoryFieldSet.setFieldhtmltype(Integer.valueOf(3));
/* 341 */       fnaCostCategoryFieldSet.setType(Integer.valueOf(284));
/* 342 */       fnaCostCategoryFieldSet.setIsRequired(Integer.valueOf(1));
/* 343 */       fnaCostCategoryFieldSet.setShowOrder(Double.valueOf(3.0D));
/*     */       
/* 345 */       fnaCostCategoryFieldSet = new FnaCostCategoryFieldSet();
/* 346 */       arrayList.add(fnaCostCategoryFieldSet);
/* 347 */       fnaCostCategoryFieldSet.setName("费用金额");
/* 348 */       fnaCostCategoryFieldSet.setFieldhtmltype(Integer.valueOf(1));
/* 349 */       fnaCostCategoryFieldSet.setType(Integer.valueOf(3));
/* 350 */       fnaCostCategoryFieldSet.setIsRequired(Integer.valueOf(1));
/* 351 */       fnaCostCategoryFieldSet.setShowOrder(Double.valueOf(4.0D));
/*     */       
/* 353 */       fnaCostCategoryFieldSet = new FnaCostCategoryFieldSet();
/* 354 */       arrayList.add(fnaCostCategoryFieldSet);
/* 355 */       fnaCostCategoryFieldSet.setName("附件");
/* 356 */       fnaCostCategoryFieldSet.setFieldhtmltype(Integer.valueOf(6));
/* 357 */       fnaCostCategoryFieldSet.setType(Integer.valueOf(1));
/* 358 */       fnaCostCategoryFieldSet.setIsRequired(Integer.valueOf(0));
/* 359 */       fnaCostCategoryFieldSet.setShowOrder(Double.valueOf(5.0D));
/*     */       
/* 361 */       fnaCostCategoryFieldSet = new FnaCostCategoryFieldSet();
/* 362 */       arrayList.add(fnaCostCategoryFieldSet);
/* 363 */       fnaCostCategoryFieldSet.setName("描述");
/* 364 */       fnaCostCategoryFieldSet.setFieldhtmltype(Integer.valueOf(2));
/* 365 */       fnaCostCategoryFieldSet.setType(Integer.valueOf(1));
/* 366 */       fnaCostCategoryFieldSet.setIsRequired(Integer.valueOf(0));
/* 367 */       fnaCostCategoryFieldSet.setShowOrder(Double.valueOf(6.0D));
/*     */     
/*     */     }
/* 370 */     else if (paramInt == 2) {
/*     */ 
/*     */       
/* 373 */       fnaCostCategoryFieldSet = new FnaCostCategoryFieldSet();
/* 374 */       arrayList.add(fnaCostCategoryFieldSet);
/* 375 */       fnaCostCategoryFieldSet.setName("费用日期");
/* 376 */       fnaCostCategoryFieldSet.setFieldhtmltype(Integer.valueOf(3));
/* 377 */       fnaCostCategoryFieldSet.setType(Integer.valueOf(2));
/* 378 */       fnaCostCategoryFieldSet.setIsRequired(Integer.valueOf(1));
/* 379 */       fnaCostCategoryFieldSet.setShowOrder(Double.valueOf(1.0D));
/*     */       
/* 381 */       fnaCostCategoryFieldSet = new FnaCostCategoryFieldSet();
/* 382 */       arrayList.add(fnaCostCategoryFieldSet);
/* 383 */       fnaCostCategoryFieldSet.setName("发票张数");
/* 384 */       fnaCostCategoryFieldSet.setFieldhtmltype(Integer.valueOf(1));
/* 385 */       fnaCostCategoryFieldSet.setType(Integer.valueOf(2));
/* 386 */       fnaCostCategoryFieldSet.setIsRequired(Integer.valueOf(1));
/* 387 */       fnaCostCategoryFieldSet.setShowOrder(Double.valueOf(2.0D));
/*     */       
/* 389 */       fnaCostCategoryFieldSet = new FnaCostCategoryFieldSet();
/* 390 */       arrayList.add(fnaCostCategoryFieldSet);
/* 391 */       fnaCostCategoryFieldSet.setName("住宿地点");
/* 392 */       fnaCostCategoryFieldSet.setFieldhtmltype(Integer.valueOf(3));
/* 393 */       fnaCostCategoryFieldSet.setType(Integer.valueOf(58));
/* 394 */       fnaCostCategoryFieldSet.setIsRequired(Integer.valueOf(1));
/* 395 */       fnaCostCategoryFieldSet.setShowOrder(Double.valueOf(3.0D));
/*     */       
/* 397 */       fnaCostCategoryFieldSet = new FnaCostCategoryFieldSet();
/* 398 */       arrayList.add(fnaCostCategoryFieldSet);
/* 399 */       fnaCostCategoryFieldSet.setName("住宿天数");
/* 400 */       fnaCostCategoryFieldSet.setFieldhtmltype(Integer.valueOf(1));
/* 401 */       fnaCostCategoryFieldSet.setType(Integer.valueOf(2));
/* 402 */       fnaCostCategoryFieldSet.setIsRequired(Integer.valueOf(1));
/* 403 */       fnaCostCategoryFieldSet.setShowOrder(Double.valueOf(4.0D));
/*     */       
/* 405 */       fnaCostCategoryFieldSet = new FnaCostCategoryFieldSet();
/* 406 */       arrayList.add(fnaCostCategoryFieldSet);
/* 407 */       fnaCostCategoryFieldSet.setName("费用金额");
/* 408 */       fnaCostCategoryFieldSet.setFieldhtmltype(Integer.valueOf(1));
/* 409 */       fnaCostCategoryFieldSet.setType(Integer.valueOf(3));
/* 410 */       fnaCostCategoryFieldSet.setIsRequired(Integer.valueOf(1));
/* 411 */       fnaCostCategoryFieldSet.setShowOrder(Double.valueOf(5.0D));
/*     */       
/* 413 */       fnaCostCategoryFieldSet = new FnaCostCategoryFieldSet();
/* 414 */       arrayList.add(fnaCostCategoryFieldSet);
/* 415 */       fnaCostCategoryFieldSet.setName("附件");
/* 416 */       fnaCostCategoryFieldSet.setFieldhtmltype(Integer.valueOf(6));
/* 417 */       fnaCostCategoryFieldSet.setType(Integer.valueOf(1));
/* 418 */       fnaCostCategoryFieldSet.setIsRequired(Integer.valueOf(0));
/* 419 */       fnaCostCategoryFieldSet.setShowOrder(Double.valueOf(6.0D));
/*     */       
/* 421 */       fnaCostCategoryFieldSet = new FnaCostCategoryFieldSet();
/* 422 */       arrayList.add(fnaCostCategoryFieldSet);
/* 423 */       fnaCostCategoryFieldSet.setName("描述");
/* 424 */       fnaCostCategoryFieldSet.setFieldhtmltype(Integer.valueOf(2));
/* 425 */       fnaCostCategoryFieldSet.setType(Integer.valueOf(1));
/* 426 */       fnaCostCategoryFieldSet.setIsRequired(Integer.valueOf(0));
/* 427 */       fnaCostCategoryFieldSet.setShowOrder(Double.valueOf(7.0D));
/*     */     }
/* 429 */     else if (paramInt == 3) {
/*     */ 
/*     */       
/* 432 */       fnaCostCategoryFieldSet = new FnaCostCategoryFieldSet();
/* 433 */       arrayList.add(fnaCostCategoryFieldSet);
/* 434 */       fnaCostCategoryFieldSet.setName("费用日期");
/* 435 */       fnaCostCategoryFieldSet.setFieldhtmltype(Integer.valueOf(3));
/* 436 */       fnaCostCategoryFieldSet.setType(Integer.valueOf(2));
/* 437 */       fnaCostCategoryFieldSet.setIsRequired(Integer.valueOf(1));
/* 438 */       fnaCostCategoryFieldSet.setShowOrder(Double.valueOf(1.0D));
/*     */       
/* 440 */       fnaCostCategoryFieldSet = new FnaCostCategoryFieldSet();
/* 441 */       arrayList.add(fnaCostCategoryFieldSet);
/* 442 */       fnaCostCategoryFieldSet.setName("发票张数");
/* 443 */       fnaCostCategoryFieldSet.setFieldhtmltype(Integer.valueOf(1));
/* 444 */       fnaCostCategoryFieldSet.setType(Integer.valueOf(2));
/* 445 */       fnaCostCategoryFieldSet.setIsRequired(Integer.valueOf(1));
/* 446 */       fnaCostCategoryFieldSet.setShowOrder(Double.valueOf(2.0D));
/*     */       
/* 448 */       fnaCostCategoryFieldSet = new FnaCostCategoryFieldSet();
/* 449 */       arrayList.add(fnaCostCategoryFieldSet);
/* 450 */       fnaCostCategoryFieldSet.setName("费用金额");
/* 451 */       fnaCostCategoryFieldSet.setFieldhtmltype(Integer.valueOf(1));
/* 452 */       fnaCostCategoryFieldSet.setType(Integer.valueOf(3));
/* 453 */       fnaCostCategoryFieldSet.setIsRequired(Integer.valueOf(1));
/* 454 */       fnaCostCategoryFieldSet.setShowOrder(Double.valueOf(3.0D));
/*     */       
/* 456 */       fnaCostCategoryFieldSet = new FnaCostCategoryFieldSet();
/* 457 */       arrayList.add(fnaCostCategoryFieldSet);
/* 458 */       fnaCostCategoryFieldSet.setName("附件");
/* 459 */       fnaCostCategoryFieldSet.setFieldhtmltype(Integer.valueOf(6));
/* 460 */       fnaCostCategoryFieldSet.setType(Integer.valueOf(1));
/* 461 */       fnaCostCategoryFieldSet.setIsRequired(Integer.valueOf(0));
/* 462 */       fnaCostCategoryFieldSet.setShowOrder(Double.valueOf(4.0D));
/*     */       
/* 464 */       fnaCostCategoryFieldSet = new FnaCostCategoryFieldSet();
/* 465 */       arrayList.add(fnaCostCategoryFieldSet);
/* 466 */       fnaCostCategoryFieldSet.setName("描述");
/* 467 */       fnaCostCategoryFieldSet.setFieldhtmltype(Integer.valueOf(2));
/* 468 */       fnaCostCategoryFieldSet.setType(Integer.valueOf(1));
/* 469 */       fnaCostCategoryFieldSet.setIsRequired(Integer.valueOf(0));
/* 470 */       fnaCostCategoryFieldSet.setShowOrder(Double.valueOf(5.0D));
/*     */     }
/* 472 */     else if (paramInt == 4) {
/*     */ 
/*     */       
/* 475 */       fnaCostCategoryFieldSet = new FnaCostCategoryFieldSet();
/* 476 */       arrayList.add(fnaCostCategoryFieldSet);
/* 477 */       fnaCostCategoryFieldSet.setName("费用日期");
/* 478 */       fnaCostCategoryFieldSet.setFieldhtmltype(Integer.valueOf(3));
/* 479 */       fnaCostCategoryFieldSet.setType(Integer.valueOf(2));
/* 480 */       fnaCostCategoryFieldSet.setIsRequired(Integer.valueOf(1));
/* 481 */       fnaCostCategoryFieldSet.setShowOrder(Double.valueOf(1.0D));
/*     */       
/* 483 */       fnaCostCategoryFieldSet = new FnaCostCategoryFieldSet();
/* 484 */       arrayList.add(fnaCostCategoryFieldSet);
/* 485 */       fnaCostCategoryFieldSet.setName("发票张数");
/* 486 */       fnaCostCategoryFieldSet.setFieldhtmltype(Integer.valueOf(1));
/* 487 */       fnaCostCategoryFieldSet.setType(Integer.valueOf(2));
/* 488 */       fnaCostCategoryFieldSet.setIsRequired(Integer.valueOf(1));
/* 489 */       fnaCostCategoryFieldSet.setShowOrder(Double.valueOf(2.0D));
/*     */       
/* 491 */       fnaCostCategoryFieldSet = new FnaCostCategoryFieldSet();
/* 492 */       arrayList.add(fnaCostCategoryFieldSet);
/* 493 */       fnaCostCategoryFieldSet.setName("天数");
/* 494 */       fnaCostCategoryFieldSet.setFieldhtmltype(Integer.valueOf(1));
/* 495 */       fnaCostCategoryFieldSet.setType(Integer.valueOf(2));
/* 496 */       fnaCostCategoryFieldSet.setIsRequired(Integer.valueOf(1));
/* 497 */       fnaCostCategoryFieldSet.setShowOrder(Double.valueOf(3.0D));
/*     */       
/* 499 */       fnaCostCategoryFieldSet = new FnaCostCategoryFieldSet();
/* 500 */       arrayList.add(fnaCostCategoryFieldSet);
/* 501 */       fnaCostCategoryFieldSet.setName("费用金额");
/* 502 */       fnaCostCategoryFieldSet.setFieldhtmltype(Integer.valueOf(1));
/* 503 */       fnaCostCategoryFieldSet.setType(Integer.valueOf(3));
/* 504 */       fnaCostCategoryFieldSet.setIsRequired(Integer.valueOf(1));
/* 505 */       fnaCostCategoryFieldSet.setShowOrder(Double.valueOf(4.0D));
/*     */       
/* 507 */       fnaCostCategoryFieldSet = new FnaCostCategoryFieldSet();
/* 508 */       arrayList.add(fnaCostCategoryFieldSet);
/* 509 */       fnaCostCategoryFieldSet.setName("附件");
/* 510 */       fnaCostCategoryFieldSet.setFieldhtmltype(Integer.valueOf(6));
/* 511 */       fnaCostCategoryFieldSet.setType(Integer.valueOf(1));
/* 512 */       fnaCostCategoryFieldSet.setIsRequired(Integer.valueOf(0));
/* 513 */       fnaCostCategoryFieldSet.setShowOrder(Double.valueOf(5.0D));
/*     */       
/* 515 */       fnaCostCategoryFieldSet = new FnaCostCategoryFieldSet();
/* 516 */       arrayList.add(fnaCostCategoryFieldSet);
/* 517 */       fnaCostCategoryFieldSet.setName("描述");
/* 518 */       fnaCostCategoryFieldSet.setFieldhtmltype(Integer.valueOf(2));
/* 519 */       fnaCostCategoryFieldSet.setType(Integer.valueOf(1));
/* 520 */       fnaCostCategoryFieldSet.setIsRequired(Integer.valueOf(0));
/* 521 */       fnaCostCategoryFieldSet.setShowOrder(Double.valueOf(6.0D));
/*     */     }
/* 523 */     else if (paramInt == 5) {
/*     */ 
/*     */       
/* 526 */       fnaCostCategoryFieldSet = new FnaCostCategoryFieldSet();
/* 527 */       arrayList.add(fnaCostCategoryFieldSet);
/* 528 */       fnaCostCategoryFieldSet.setName("费用日期");
/* 529 */       fnaCostCategoryFieldSet.setFieldhtmltype(Integer.valueOf(3));
/* 530 */       fnaCostCategoryFieldSet.setType(Integer.valueOf(2));
/* 531 */       fnaCostCategoryFieldSet.setIsRequired(Integer.valueOf(1));
/* 532 */       fnaCostCategoryFieldSet.setShowOrder(Double.valueOf(1.0D));
/*     */       
/* 534 */       fnaCostCategoryFieldSet = new FnaCostCategoryFieldSet();
/* 535 */       arrayList.add(fnaCostCategoryFieldSet);
/* 536 */       fnaCostCategoryFieldSet.setName("发票张数");
/* 537 */       fnaCostCategoryFieldSet.setFieldhtmltype(Integer.valueOf(1));
/* 538 */       fnaCostCategoryFieldSet.setType(Integer.valueOf(2));
/* 539 */       fnaCostCategoryFieldSet.setIsRequired(Integer.valueOf(1));
/* 540 */       fnaCostCategoryFieldSet.setShowOrder(Double.valueOf(2.0D));
/*     */       
/* 542 */       fnaCostCategoryFieldSet = new FnaCostCategoryFieldSet();
/* 543 */       arrayList.add(fnaCostCategoryFieldSet);
/* 544 */       fnaCostCategoryFieldSet.setName("费用金额");
/* 545 */       fnaCostCategoryFieldSet.setFieldhtmltype(Integer.valueOf(1));
/* 546 */       fnaCostCategoryFieldSet.setType(Integer.valueOf(3));
/* 547 */       fnaCostCategoryFieldSet.setIsRequired(Integer.valueOf(1));
/* 548 */       fnaCostCategoryFieldSet.setShowOrder(Double.valueOf(3.0D));
/*     */       
/* 550 */       fnaCostCategoryFieldSet = new FnaCostCategoryFieldSet();
/* 551 */       arrayList.add(fnaCostCategoryFieldSet);
/* 552 */       fnaCostCategoryFieldSet.setName("附件");
/* 553 */       fnaCostCategoryFieldSet.setFieldhtmltype(Integer.valueOf(6));
/* 554 */       fnaCostCategoryFieldSet.setType(Integer.valueOf(1));
/* 555 */       fnaCostCategoryFieldSet.setIsRequired(Integer.valueOf(0));
/* 556 */       fnaCostCategoryFieldSet.setShowOrder(Double.valueOf(4.0D));
/*     */       
/* 558 */       fnaCostCategoryFieldSet = new FnaCostCategoryFieldSet();
/* 559 */       arrayList.add(fnaCostCategoryFieldSet);
/* 560 */       fnaCostCategoryFieldSet.setName("描述");
/* 561 */       fnaCostCategoryFieldSet.setFieldhtmltype(Integer.valueOf(2));
/* 562 */       fnaCostCategoryFieldSet.setType(Integer.valueOf(1));
/* 563 */       fnaCostCategoryFieldSet.setIsRequired(Integer.valueOf(0));
/* 564 */       fnaCostCategoryFieldSet.setShowOrder(Double.valueOf(5.0D));
/*     */     }
/* 566 */     else if (paramInt == 6) {
/*     */ 
/*     */       
/* 569 */       fnaCostCategoryFieldSet = new FnaCostCategoryFieldSet();
/* 570 */       arrayList.add(fnaCostCategoryFieldSet);
/* 571 */       fnaCostCategoryFieldSet.setName("费用日期");
/* 572 */       fnaCostCategoryFieldSet.setFieldhtmltype(Integer.valueOf(3));
/* 573 */       fnaCostCategoryFieldSet.setType(Integer.valueOf(2));
/* 574 */       fnaCostCategoryFieldSet.setIsRequired(Integer.valueOf(1));
/* 575 */       fnaCostCategoryFieldSet.setShowOrder(Double.valueOf(1.0D));
/*     */       
/* 577 */       fnaCostCategoryFieldSet = new FnaCostCategoryFieldSet();
/* 578 */       arrayList.add(fnaCostCategoryFieldSet);
/* 579 */       fnaCostCategoryFieldSet.setName("发票张数");
/* 580 */       fnaCostCategoryFieldSet.setFieldhtmltype(Integer.valueOf(1));
/* 581 */       fnaCostCategoryFieldSet.setType(Integer.valueOf(2));
/* 582 */       fnaCostCategoryFieldSet.setIsRequired(Integer.valueOf(1));
/* 583 */       fnaCostCategoryFieldSet.setShowOrder(Double.valueOf(2.0D));
/*     */       
/* 585 */       fnaCostCategoryFieldSet = new FnaCostCategoryFieldSet();
/* 586 */       arrayList.add(fnaCostCategoryFieldSet);
/* 587 */       fnaCostCategoryFieldSet.setName("费用金额");
/* 588 */       fnaCostCategoryFieldSet.setFieldhtmltype(Integer.valueOf(1));
/* 589 */       fnaCostCategoryFieldSet.setType(Integer.valueOf(3));
/* 590 */       fnaCostCategoryFieldSet.setIsRequired(Integer.valueOf(1));
/* 591 */       fnaCostCategoryFieldSet.setShowOrder(Double.valueOf(3.0D));
/*     */       
/* 593 */       fnaCostCategoryFieldSet = new FnaCostCategoryFieldSet();
/* 594 */       arrayList.add(fnaCostCategoryFieldSet);
/* 595 */       fnaCostCategoryFieldSet.setName("附件");
/* 596 */       fnaCostCategoryFieldSet.setFieldhtmltype(Integer.valueOf(6));
/* 597 */       fnaCostCategoryFieldSet.setType(Integer.valueOf(1));
/* 598 */       fnaCostCategoryFieldSet.setIsRequired(Integer.valueOf(0));
/* 599 */       fnaCostCategoryFieldSet.setShowOrder(Double.valueOf(4.0D));
/*     */       
/* 601 */       fnaCostCategoryFieldSet = new FnaCostCategoryFieldSet();
/* 602 */       arrayList.add(fnaCostCategoryFieldSet);
/* 603 */       fnaCostCategoryFieldSet.setName("描述");
/* 604 */       fnaCostCategoryFieldSet.setFieldhtmltype(Integer.valueOf(2));
/* 605 */       fnaCostCategoryFieldSet.setType(Integer.valueOf(1));
/* 606 */       fnaCostCategoryFieldSet.setIsRequired(Integer.valueOf(0));
/* 607 */       fnaCostCategoryFieldSet.setShowOrder(Double.valueOf(5.0D));
/*     */     }
/* 609 */     else if (paramInt == 7) {
/*     */ 
/*     */       
/* 612 */       fnaCostCategoryFieldSet = new FnaCostCategoryFieldSet();
/* 613 */       arrayList.add(fnaCostCategoryFieldSet);
/* 614 */       fnaCostCategoryFieldSet.setName("费用日期");
/* 615 */       fnaCostCategoryFieldSet.setFieldhtmltype(Integer.valueOf(3));
/* 616 */       fnaCostCategoryFieldSet.setType(Integer.valueOf(2));
/* 617 */       fnaCostCategoryFieldSet.setIsRequired(Integer.valueOf(1));
/* 618 */       fnaCostCategoryFieldSet.setShowOrder(Double.valueOf(1.0D));
/*     */       
/* 620 */       fnaCostCategoryFieldSet = new FnaCostCategoryFieldSet();
/* 621 */       arrayList.add(fnaCostCategoryFieldSet);
/* 622 */       fnaCostCategoryFieldSet.setName("发票张数");
/* 623 */       fnaCostCategoryFieldSet.setFieldhtmltype(Integer.valueOf(1));
/* 624 */       fnaCostCategoryFieldSet.setType(Integer.valueOf(2));
/* 625 */       fnaCostCategoryFieldSet.setIsRequired(Integer.valueOf(1));
/* 626 */       fnaCostCategoryFieldSet.setShowOrder(Double.valueOf(2.0D));
/*     */       
/* 628 */       fnaCostCategoryFieldSet = new FnaCostCategoryFieldSet();
/* 629 */       arrayList.add(fnaCostCategoryFieldSet);
/* 630 */       fnaCostCategoryFieldSet.setName("费用金额");
/* 631 */       fnaCostCategoryFieldSet.setFieldhtmltype(Integer.valueOf(1));
/* 632 */       fnaCostCategoryFieldSet.setType(Integer.valueOf(3));
/* 633 */       fnaCostCategoryFieldSet.setIsRequired(Integer.valueOf(1));
/* 634 */       fnaCostCategoryFieldSet.setShowOrder(Double.valueOf(3.0D));
/*     */       
/* 636 */       fnaCostCategoryFieldSet = new FnaCostCategoryFieldSet();
/* 637 */       arrayList.add(fnaCostCategoryFieldSet);
/* 638 */       fnaCostCategoryFieldSet.setName("附件");
/* 639 */       fnaCostCategoryFieldSet.setFieldhtmltype(Integer.valueOf(6));
/* 640 */       fnaCostCategoryFieldSet.setType(Integer.valueOf(1));
/* 641 */       fnaCostCategoryFieldSet.setIsRequired(Integer.valueOf(0));
/* 642 */       fnaCostCategoryFieldSet.setShowOrder(Double.valueOf(4.0D));
/*     */       
/* 644 */       fnaCostCategoryFieldSet = new FnaCostCategoryFieldSet();
/* 645 */       arrayList.add(fnaCostCategoryFieldSet);
/* 646 */       fnaCostCategoryFieldSet.setName("描述");
/* 647 */       fnaCostCategoryFieldSet.setFieldhtmltype(Integer.valueOf(2));
/* 648 */       fnaCostCategoryFieldSet.setType(Integer.valueOf(1));
/* 649 */       fnaCostCategoryFieldSet.setIsRequired(Integer.valueOf(0));
/* 650 */       fnaCostCategoryFieldSet.setShowOrder(Double.valueOf(5.0D));
/*     */     } 
/*     */     
/* 653 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/* 672 */   private static final FnaCostCategoryFieldSetBo thisClassObj = new FnaCostCategoryFieldSetBo();
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static FnaCostCategoryFieldSetBo getInstance() {
/* 678 */     return thisClassObj;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/fna/e9/bo/base/FnaCostCategoryFieldSetBo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */