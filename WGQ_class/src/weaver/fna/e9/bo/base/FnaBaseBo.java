/*    */ package weaver.fna.e9.bo.base;
/*    */ 
/*    */ import weaver.general.BaseBean;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class FnaBaseBo
/*    */ {
/* 27 */   BaseBean bb = new BaseBean();
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public boolean nonEmptyCheck(String paramString) {
/* 35 */     return (paramString != null && !"".equals(paramString));
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public boolean nonNullCheck(Object paramObject) {
/* 44 */     return (paramObject != null);
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/* 55 */   private static final FnaBaseBo thisClassObj = new FnaBaseBo();
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public static FnaBaseBo getInstance() {
/* 61 */     return thisClassObj;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/fna/e9/bo/base/FnaBaseBo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */