/*     */ package weaver.fna.e9.bo.base;
/*     */ 
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import weaver.conn.RecordSetTrans;
/*     */ import weaver.fna.e9.dao.base.FnaBaseDao;
/*     */ import weaver.fna.e9.exception.FnaException;
/*     */ import weaver.fna.e9.po.base.FnaPaymentMode;
/*     */ import weaver.fna.general.FnaCommon;
/*     */ import weaver.fna.general.RecordSet4Action;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class FnaPaymentModeBo
/*     */ {
/*  31 */   BaseBean bb = new BaseBean();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public double queryMaxShowOrder(RecordSet4Action paramRecordSet4Action, int paramInt) throws Exception {
/*  41 */     boolean bool = paramRecordSet4Action.executeQuery("select max(showOrder) maxShowOrder from FnaPaymentMode", new Object[0]);
/*  42 */     if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(paramInt))); 
/*  43 */     if (paramRecordSet4Action.next()) {
/*  44 */       return Util.getDoubleValue(paramRecordSet4Action.getString("maxShowOrder"), 0.0D);
/*     */     }
/*  46 */     return 0.0D;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int createData(RecordSetTrans paramRecordSetTrans, FnaPaymentMode paramFnaPaymentMode, int paramInt, HashMap<String, Object> paramHashMap) throws Exception {
/*  59 */     if (paramFnaPaymentMode.getId().intValue() > 0) {
/*  60 */       throw new FnaException(SystemEnv.getHtmlLabelName(130805, Util.getIntValue(paramInt)));
/*     */     }
/*  62 */     verifyState(paramFnaPaymentMode.getState().intValue(), paramInt);
/*  63 */     verifyName(paramFnaPaymentMode.getName(), paramFnaPaymentMode.getId().intValue(), paramInt);
/*  64 */     verifyCodeName(paramFnaPaymentMode.getCodeName(), paramFnaPaymentMode.getId().intValue(), paramInt);
/*     */     
/*  66 */     RecordSet4Action recordSet4Action = new RecordSet4Action(paramRecordSetTrans);
/*  67 */     FnaBaseDao fnaBaseDao = new FnaBaseDao();
/*  68 */     fnaBaseDao.saveObject(recordSet4Action, paramFnaPaymentMode);
/*  69 */     paramHashMap.put("needRollback", "true");
/*     */     
/*  71 */     boolean bool = recordSet4Action.executeQuery("select max(id) maxId from FnaPaymentMode where name=? and codeName=?", new Object[] { paramFnaPaymentMode.getName(), paramFnaPaymentMode.getCodeName() });
/*  72 */     if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(paramInt))); 
/*  73 */     if (recordSet4Action.next()) {
/*  74 */       paramFnaPaymentMode.setId(recordSet4Action.getInt("maxId"));
/*     */     }
/*  76 */     return paramFnaPaymentMode.getId().intValue();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void updateData(RecordSetTrans paramRecordSetTrans, FnaPaymentMode paramFnaPaymentMode, int paramInt, HashMap<String, Object> paramHashMap) throws Exception {
/*  88 */     if (paramFnaPaymentMode.getId().intValue() <= 0) {
/*  89 */       throw new FnaException(SystemEnv.getHtmlLabelName(130806, Util.getIntValue(paramInt)));
/*     */     }
/*  91 */     verifyState(paramFnaPaymentMode.getState().intValue(), paramInt);
/*  92 */     verifyName(paramFnaPaymentMode.getName(), paramFnaPaymentMode.getId().intValue(), paramInt);
/*  93 */     verifyCodeName(paramFnaPaymentMode.getCodeName(), paramFnaPaymentMode.getId().intValue(), paramInt);
/*     */     
/*  95 */     RecordSet4Action recordSet4Action = new RecordSet4Action(paramRecordSetTrans);
/*  96 */     FnaBaseDao fnaBaseDao = new FnaBaseDao();
/*  97 */     fnaBaseDao.updateObject(recordSet4Action, paramFnaPaymentMode);
/*  98 */     paramHashMap.put("needRollback", "true");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void deleteData(RecordSetTrans paramRecordSetTrans, String[] paramArrayOfString, int paramInt, HashMap<String, Object> paramHashMap) throws Exception {
/* 110 */     int i = paramArrayOfString.length;
/* 111 */     if (i <= 0) {
/* 112 */       throw new FnaException(SystemEnv.getHtmlLabelName(10000019, Util.getIntValue(paramInt)));
/*     */     }
/*     */ 
/*     */     
/* 116 */     StringBuffer stringBuffer = new StringBuffer("delete from FnaPaymentMode where (1=2");
/* 117 */     List<String> list = FnaCommon.initData1(paramArrayOfString);
/* 118 */     int j = list.size();
/* 119 */     for (byte b = 0; b < j; b++) {
/* 120 */       stringBuffer.append(" or id in (").append(list.get(b)).append(")");
/*     */     }
/* 122 */     stringBuffer.append(" )");
/* 123 */     boolean bool = paramRecordSetTrans.executeSql(stringBuffer.toString());
/* 124 */     if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(paramInt))); 
/* 125 */     paramHashMap.put("needRollback", "true");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void changeState(RecordSetTrans paramRecordSetTrans, int paramInt1, int paramInt2, int paramInt3, HashMap<String, Object> paramHashMap) throws Exception {
/* 139 */     verifyState(paramInt2, paramInt3);
/*     */     
/* 141 */     boolean bool = paramRecordSetTrans.executeUpdate("update FnaPaymentMode set state=? where id=?", new Object[] { Integer.valueOf(paramInt2), Integer.valueOf(paramInt1) });
/* 142 */     if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(paramInt3))); 
/* 143 */     paramHashMap.put("needRollback", "true");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void verifyCodeName(String paramString, int paramInt1, int paramInt2) throws Exception {
/* 154 */     if ("".equals(paramString)) {
/* 155 */       throw new FnaException(SystemEnv.getHtmlLabelName(10000020, Util.getIntValue(paramInt2)));
/*     */     }
/* 157 */     RecordSet4Action recordSet4Action = new RecordSet4Action();
/* 158 */     boolean bool = recordSet4Action.executeQuery("select count(*) cnt from FnaPaymentMode a where codeName=? and id<>?", new Object[] { paramString, Integer.valueOf(paramInt1) });
/* 159 */     if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(paramInt2))); 
/* 160 */     if (recordSet4Action.next() && recordSet4Action.getInt("cnt").intValue() > 0) {
/* 161 */       throw new FnaException(SystemEnv.getHtmlLabelName(10000021, Util.getIntValue(paramInt2)).replace("#replaceString#", paramString));
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void verifyName(String paramString, int paramInt1, int paramInt2) throws Exception {
/* 173 */     if ("".equals(paramString)) {
/* 174 */       throw new FnaException(SystemEnv.getHtmlLabelName(81968, Util.getIntValue(paramInt2)));
/*     */     }
/* 176 */     RecordSet4Action recordSet4Action = new RecordSet4Action();
/* 177 */     boolean bool = recordSet4Action.executeQuery("select count(*) cnt from FnaPaymentMode a where name=? and id<>?", new Object[] { paramString, Integer.valueOf(paramInt1) });
/* 178 */     if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(paramInt2))); 
/* 179 */     if (recordSet4Action.next() && recordSet4Action.getInt("cnt").intValue() > 0) {
/* 180 */       throw new FnaException(SystemEnv.getHtmlLabelName(10000022, Util.getIntValue(paramInt2)).replace("#replaceString#", paramString));
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void verifyState(int paramInt1, int paramInt2) throws Exception {
/* 191 */     if (paramInt1 < 0 && paramInt1 > 1) {
/* 192 */       throw new FnaException(SystemEnv.getHtmlLabelName(130802, Util.getIntValue(paramInt2)));
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/* 212 */   private static final FnaPaymentModeBo thisClassObj = new FnaPaymentModeBo();
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static FnaPaymentModeBo getInstance() {
/* 218 */     return thisClassObj;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/fna/e9/bo/base/FnaPaymentModeBo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */