/*     */ package weaver.fna.e9.bo.base;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import weaver.conn.RecordSetTrans;
/*     */ import weaver.fna.e9.dao.base.FnaBaseDao;
/*     */ import weaver.fna.e9.exception.FnaException;
/*     */ import weaver.fna.e9.po.base.FnaCityLevel;
/*     */ import weaver.fna.general.FnaCommon;
/*     */ import weaver.fna.general.RecordSet4Action;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.city.CityComInfo;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class FnaCityLevelBo
/*     */ {
/*  33 */   BaseBean bb = new BaseBean();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<Integer> queryCityByCityLevel(RecordSet4Action paramRecordSet4Action, int paramInt1, int paramInt2) throws Exception {
/*  44 */     ArrayList<Integer> arrayList = new ArrayList();
/*  45 */     boolean bool = paramRecordSet4Action.executeQuery("select a.cityId from fnaCityLevelCity a where a.fnaCityLevelId = ?", new Object[] { Integer.valueOf(paramInt1) });
/*  46 */     if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(paramInt2))); 
/*  47 */     while (paramRecordSet4Action.next()) {
/*  48 */       int i = paramRecordSet4Action.getInt("cityId").intValue();
/*  49 */       arrayList.add(Integer.valueOf(i));
/*     */     } 
/*  51 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public double queryMaxShowOrder(RecordSet4Action paramRecordSet4Action, int paramInt) throws Exception {
/*  62 */     boolean bool = paramRecordSet4Action.executeQuery("select max(showOrder) maxShowOrder from FnaCityLevel", new Object[0]);
/*  63 */     if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(paramInt))); 
/*  64 */     if (paramRecordSet4Action.next()) {
/*  65 */       return Util.getDoubleValue(paramRecordSet4Action.getString("maxShowOrder"), 0.0D);
/*     */     }
/*  67 */     return 0.0D;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int createData(RecordSetTrans paramRecordSetTrans, FnaCityLevel paramFnaCityLevel, int paramInt, HashMap<String, Object> paramHashMap) throws Exception {
/*  80 */     if (paramFnaCityLevel.getId().intValue() > 0) {
/*  81 */       throw new FnaException(SystemEnv.getHtmlLabelName(130805, Util.getIntValue(paramInt)));
/*     */     }
/*  83 */     verifyState(paramFnaCityLevel.getState().intValue(), paramInt);
/*  84 */     verifyName(paramFnaCityLevel.getName(), paramFnaCityLevel.getId().intValue(), paramInt);
/*  85 */     verifyCodeName(paramFnaCityLevel.getCodeName(), paramFnaCityLevel.getId().intValue(), paramInt);
/*     */     
/*  87 */     RecordSet4Action recordSet4Action = new RecordSet4Action(paramRecordSetTrans);
/*  88 */     FnaBaseDao fnaBaseDao = new FnaBaseDao();
/*  89 */     fnaBaseDao.saveObject(recordSet4Action, paramFnaCityLevel);
/*  90 */     paramHashMap.put("needRollback", "true");
/*     */     
/*  92 */     boolean bool = recordSet4Action.executeQuery("select max(id) maxId from FnaCityLevel where name=? and codeName=?", new Object[] { paramFnaCityLevel.getName(), paramFnaCityLevel.getCodeName() });
/*  93 */     if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(paramInt))); 
/*  94 */     if (recordSet4Action.next()) {
/*  95 */       paramFnaCityLevel.setId(recordSet4Action.getInt("maxId"));
/*     */       
/*  97 */       saveData_fnaCityLevelCity(recordSet4Action, paramFnaCityLevel.getId().intValue(), paramFnaCityLevel.getCityIds(), paramInt, paramHashMap);
/*     */     } 
/*     */     
/* 100 */     return paramFnaCityLevel.getId().intValue();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void updateData(RecordSetTrans paramRecordSetTrans, FnaCityLevel paramFnaCityLevel, int paramInt, HashMap<String, Object> paramHashMap) throws Exception {
/* 112 */     if (paramFnaCityLevel.getId().intValue() <= 0) {
/* 113 */       throw new FnaException(SystemEnv.getHtmlLabelName(130806, Util.getIntValue(paramInt)));
/*     */     }
/* 115 */     verifyState(paramFnaCityLevel.getState().intValue(), paramInt);
/* 116 */     verifyName(paramFnaCityLevel.getName(), paramFnaCityLevel.getId().intValue(), paramInt);
/* 117 */     verifyCodeName(paramFnaCityLevel.getCodeName(), paramFnaCityLevel.getId().intValue(), paramInt);
/*     */     
/* 119 */     RecordSet4Action recordSet4Action = new RecordSet4Action(paramRecordSetTrans);
/* 120 */     FnaBaseDao fnaBaseDao = new FnaBaseDao();
/* 121 */     fnaBaseDao.updateObject(recordSet4Action, paramFnaCityLevel);
/* 122 */     paramHashMap.put("needRollback", "true");
/*     */     
/* 124 */     saveData_fnaCityLevelCity(recordSet4Action, paramFnaCityLevel.getId().intValue(), paramFnaCityLevel.getCityIds(), paramInt, paramHashMap);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void saveData_fnaCityLevelCity(RecordSet4Action paramRecordSet4Action, int paramInt1, String paramString, int paramInt2, HashMap<String, Object> paramHashMap) throws Exception {
/* 137 */     verify_fnaCityLevelCity(paramInt1, paramString, paramInt2);
/*     */     
/* 139 */     boolean bool = paramRecordSet4Action.executeUpdate("delete from fnaCityLevelCity where fnaCityLevelId=?", new Object[] { Integer.valueOf(paramInt1) });
/* 140 */     if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(paramInt2))); 
/* 141 */     paramHashMap.put("needRollback", "true");
/* 142 */     if (!"".equals(paramString)) {
/* 143 */       String[] arrayOfString = paramString.split(",");
/* 144 */       int i = arrayOfString.length;
/* 145 */       StringBuffer stringBuffer = new StringBuffer();
/* 146 */       for (byte b = 0; b < i; b++) {
/* 147 */         int j = Util.getIntValue(arrayOfString[b]);
/* 148 */         if (j > 0) {
/* 149 */           if (stringBuffer.length() > 0) {
/* 150 */             stringBuffer.append(" union all \n");
/*     */           }
/* 152 */           stringBuffer.append(" select ");
/* 153 */           stringBuffer.append(paramInt1).append(",");
/* 154 */           stringBuffer.append(j);
/* 155 */           if ("oracle".equals(paramRecordSet4Action.getDBType())) {
/* 156 */             stringBuffer.append(" from dual ");
/*     */           }
/* 158 */           stringBuffer.append(" \r\n");
/*     */         } 
/*     */       } 
/* 161 */       if (stringBuffer.length() > 0) {
/* 162 */         bool = paramRecordSet4Action.executeSql("insert into fnaCityLevelCity(fnaCityLevelId,cityId) \r\n" + stringBuffer.toString());
/* 163 */         if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(paramInt2))); 
/* 164 */         paramHashMap.put("needRollback", "true");
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void deleteData(RecordSetTrans paramRecordSetTrans, String[] paramArrayOfString, int paramInt, HashMap<String, Object> paramHashMap) throws Exception {
/* 178 */     int i = paramArrayOfString.length;
/* 179 */     if (i <= 0) {
/* 180 */       throw new FnaException(SystemEnv.getHtmlLabelName(10000019, Util.getIntValue(paramInt)));
/*     */     }
/*     */ 
/*     */     
/* 184 */     StringBuffer stringBuffer = new StringBuffer("delete from FnaCityLevel where (1=2");
/* 185 */     List<String> list = FnaCommon.initData1(paramArrayOfString);
/* 186 */     int j = list.size();
/* 187 */     for (byte b1 = 0; b1 < j; b1++) {
/* 188 */       stringBuffer.append(" or id in (").append(list.get(b1)).append(")");
/*     */     }
/* 190 */     stringBuffer.append(" )");
/* 191 */     boolean bool = paramRecordSetTrans.executeSql(stringBuffer.toString());
/* 192 */     if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(paramInt))); 
/* 193 */     paramHashMap.put("needRollback", "true");
/*     */     
/* 195 */     stringBuffer = new StringBuffer("delete from fnaCityLevelCity where (1=2");
/* 196 */     for (byte b2 = 0; b2 < j; b2++) {
/* 197 */       stringBuffer.append(" or fnaCityLevelId in (").append(list.get(b2)).append(")");
/*     */     }
/* 199 */     stringBuffer.append(" )");
/* 200 */     bool = paramRecordSetTrans.executeSql(stringBuffer.toString());
/* 201 */     if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(paramInt))); 
/* 202 */     paramHashMap.put("needRollback", "true");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void changeState(RecordSetTrans paramRecordSetTrans, int paramInt1, int paramInt2, int paramInt3, HashMap<String, Object> paramHashMap) throws Exception {
/* 216 */     verifyState(paramInt2, paramInt3);
/*     */     
/* 218 */     boolean bool = paramRecordSetTrans.executeUpdate("update FnaCityLevel set state=? where id=?", new Object[] { Integer.valueOf(paramInt2), Integer.valueOf(paramInt1) });
/* 219 */     if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(paramInt3))); 
/* 220 */     paramHashMap.put("needRollback", "true");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void verify_fnaCityLevelCity(int paramInt1, String paramString, int paramInt2) throws Exception {
/* 231 */     if (!"".equals(paramString)) {
/* 232 */       RecordSet4Action recordSet4Action = new RecordSet4Action();
/* 233 */       CityComInfo cityComInfo = new CityComInfo();
/* 234 */       StringBuffer stringBuffer1 = new StringBuffer();
/*     */       
/* 236 */       List<String> list = FnaCommon.initData1(paramString.split(","));
/* 237 */       int i = list.size();
/*     */       
/* 239 */       StringBuffer stringBuffer2 = new StringBuffer("select distinct cityId from fnaCityLevelCity where fnaCityLevelId<>? and (1=2");
/* 240 */       for (byte b = 0; b < i; b++) {
/* 241 */         stringBuffer2.append(" or cityId in (").append(list.get(b)).append(")");
/*     */       }
/* 243 */       stringBuffer2.append(" )");
/* 244 */       boolean bool = recordSet4Action.executeQuery(stringBuffer2.toString(), new Object[] { Integer.valueOf(paramInt1) });
/* 245 */       if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(paramInt2))); 
/* 246 */       while (recordSet4Action.next()) {
/* 247 */         String str = recordSet4Action.getString("cityId");
/* 248 */         if (stringBuffer1.length() > 0) {
/* 249 */           stringBuffer1.append(",");
/*     */         }
/* 251 */         stringBuffer1.append(cityComInfo.getCityname(str));
/*     */       } 
/* 253 */       if (stringBuffer1.length() > 0) {
/* 254 */         stringBuffer1.append("：" + SystemEnv.getHtmlLabelName(130843, Util.getIntValue(paramInt2)));
/* 255 */         throw new FnaException(stringBuffer1.toString());
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void verifyCodeName(String paramString, int paramInt1, int paramInt2) throws Exception {
/* 268 */     if ("".equals(paramString)) {
/* 269 */       throw new FnaException(SystemEnv.getHtmlLabelName(10000020, Util.getIntValue(paramInt2)));
/*     */     }
/* 271 */     RecordSet4Action recordSet4Action = new RecordSet4Action();
/* 272 */     boolean bool = recordSet4Action.executeQuery("select count(*) cnt from FnaCityLevel a where codeName=? and id<>?", new Object[] { paramString, Integer.valueOf(paramInt1) });
/* 273 */     if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(paramInt2))); 
/* 274 */     if (recordSet4Action.next() && recordSet4Action.getInt("cnt").intValue() > 0) {
/* 275 */       throw new FnaException(SystemEnv.getHtmlLabelName(10000021, Util.getIntValue(paramInt2)).replace("#replaceString#", paramString));
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void verifyName(String paramString, int paramInt1, int paramInt2) throws Exception {
/* 287 */     if ("".equals(paramString)) {
/* 288 */       throw new FnaException(SystemEnv.getHtmlLabelName(81968, Util.getIntValue(paramInt2)));
/*     */     }
/* 290 */     RecordSet4Action recordSet4Action = new RecordSet4Action();
/* 291 */     boolean bool = recordSet4Action.executeQuery("select count(*) cnt from FnaCityLevel a where name=? and id<>?", new Object[] { paramString, Integer.valueOf(paramInt1) });
/* 292 */     if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(paramInt2))); 
/* 293 */     if (recordSet4Action.next() && recordSet4Action.getInt("cnt").intValue() > 0) {
/* 294 */       throw new FnaException(SystemEnv.getHtmlLabelName(10000022, Util.getIntValue(paramInt2)).replace("#replaceString#", paramString));
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void verifyState(int paramInt1, int paramInt2) throws Exception {
/* 305 */     if (paramInt1 < 0 && paramInt1 > 1) {
/* 306 */       throw new FnaException(SystemEnv.getHtmlLabelName(130802, Util.getIntValue(paramInt2)));
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/* 326 */   private static final FnaCityLevelBo thisClassObj = new FnaCityLevelBo();
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static FnaCityLevelBo getInstance() {
/* 332 */     return thisClassObj;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/fna/e9/bo/base/FnaCityLevelBo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */