/*     */ package weaver.fna.e9.bo.base;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.Calendar;
/*     */ import java.util.List;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.conn.RecordSetTrans;
/*     */ import weaver.fna.e9.dao.base.FnaPeriodDao;
/*     */ import weaver.fna.e9.dao.base.FnaPeriodDtlDao;
/*     */ import weaver.fna.e9.exception.FnaException;
/*     */ import weaver.fna.e9.po.base.FnaPeriod;
/*     */ import weaver.fna.e9.po.base.FnaPeriodDtl;
/*     */ import weaver.fna.e9.po.base.FnaPeriodHelp;
/*     */ import weaver.fna.general.FnaCommon;
/*     */ import weaver.fna.general.RecordSet4Action;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.TimeUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ @Deprecated
/*     */ public class FnaPeriodBo
/*     */ {
/*     */   @Deprecated
/*  36 */   BaseBean bb = new BaseBean();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void doEffect(String paramString, int paramInt) throws Exception {
/*  47 */     RecordSetTrans recordSetTrans = null;
/*     */     try {
/*  49 */       recordSetTrans = new RecordSetTrans();
/*  50 */       boolean bool = recordSetTrans.setAutoCommit(false);
/*  51 */       if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(paramInt)));
/*     */       
/*  53 */       bool = recordSetTrans.executeUpdate("update fnaPeriod set status=? where fnaPeriodPk=?", new Object[] { Integer.valueOf(1), paramString });
/*  54 */       if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(paramInt)));
/*     */       
/*  56 */       bool = recordSetTrans.commit();
/*  57 */       if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(paramInt))); 
/*  58 */     } catch (Exception exception) {
/*     */       try {
/*  60 */         if (recordSetTrans != null) {
/*  61 */           recordSetTrans.rollback();
/*     */         }
/*  63 */       } catch (Exception exception1) {}
/*  64 */       throw exception;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void doClose(String paramString, int paramInt) throws Exception {
/*  76 */     RecordSetTrans recordSetTrans = null;
/*     */     try {
/*  78 */       recordSetTrans = new RecordSetTrans();
/*  79 */       boolean bool = recordSetTrans.setAutoCommit(false);
/*  80 */       if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(paramInt)));
/*     */       
/*  82 */       bool = recordSetTrans.executeUpdate("update fnaPeriod set status=? where fnaPeriodPk=?", new Object[] { Integer.valueOf(-1), paramString });
/*  83 */       if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(paramInt)));
/*     */       
/*  85 */       bool = recordSetTrans.commit();
/*  86 */       if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(paramInt))); 
/*  87 */     } catch (Exception exception) {
/*     */       try {
/*  89 */         if (recordSetTrans != null) {
/*  90 */           recordSetTrans.rollback();
/*     */         }
/*  92 */       } catch (Exception exception1) {}
/*  93 */       throw exception;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void doReopen(String paramString, int paramInt) throws Exception {
/* 105 */     RecordSetTrans recordSetTrans = null;
/*     */     try {
/* 107 */       recordSetTrans = new RecordSetTrans();
/* 108 */       boolean bool = recordSetTrans.setAutoCommit(false);
/* 109 */       if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(paramInt)));
/*     */       
/* 111 */       bool = recordSetTrans.executeUpdate("update fnaPeriod set status=? where fnaPeriodPk=?", new Object[] { Integer.valueOf(0), paramString });
/* 112 */       if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(paramInt)));
/*     */       
/* 114 */       bool = recordSetTrans.commit();
/* 115 */       if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(paramInt))); 
/* 116 */     } catch (Exception exception) {
/*     */       try {
/* 118 */         if (recordSetTrans != null) {
/* 119 */           recordSetTrans.rollback();
/*     */         }
/* 121 */       } catch (Exception exception1) {}
/* 122 */       throw exception;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void deleteFnaPeriod(RecordSetTrans paramRecordSetTrans, String[] paramArrayOfString, int paramInt) throws Exception {
/* 135 */     boolean bool = true;
/* 136 */     FnaPeriodHelp fnaPeriodHelp = new FnaPeriodHelp();
/* 137 */     int i = paramArrayOfString.length;
/* 138 */     for (byte b = 0; b < i; b++) {
/* 139 */       String str = paramArrayOfString[b];
/*     */       
/* 141 */       StringBuffer stringBuffer = new StringBuffer();
/* 142 */       boolean bool1 = fnaPeriodHelp.checkCanDelete(str, paramInt, stringBuffer);
/* 143 */       if (!bool1) throw new FnaException(stringBuffer.toString());
/*     */       
/* 145 */       bool = paramRecordSetTrans.executeUpdate("delete from fnaPeriodDtl where fnaPeriodPk=?", new Object[] { str });
/* 146 */       if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(paramInt)));
/*     */       
/* 148 */       bool = paramRecordSetTrans.executeUpdate("delete from fnaPeriod where fnaPeriodPk=?", new Object[] { str });
/* 149 */       if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(paramInt)));
/*     */     
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   private String checkFnaPeriodUpdate(List<String> paramList1, List<String> paramList2, int paramInt) {
/* 162 */     ArrayList<String> arrayList1 = new ArrayList();
/* 163 */     ArrayList<String> arrayList2 = new ArrayList();
/* 164 */     String str1 = "";
/* 165 */     String str2 = "";
/* 166 */     int i = paramList1.size();
/* 167 */     for (byte b1 = 0; b1 < i; b1++) {
/* 168 */       String str4 = paramList1.get(b1);
/* 169 */       String str5 = paramList2.get(b1);
/* 170 */       if (b1 == 0) {
/* 171 */         str1 = str4;
/*     */       }
/* 173 */       if (b1 == i - 1) {
/* 174 */         str2 = str5;
/*     */       }
/* 176 */       String str6 = "";
/* 177 */       if ("".equals(str6)) {
/* 178 */         if (str4.compareTo(str5) > 0) {
/* 179 */           return SystemEnv.getHtmlLabelName(33937, paramInt) + "（" + str4 + "~" + str5 + "）";
/*     */         }
/* 181 */         String str = str4;
/* 182 */         while (str.compareTo(str5) <= 0) {
/* 183 */           if (arrayList1.contains(str)) {
/* 184 */             return SystemEnv.getHtmlLabelName(33939, paramInt) + "（" + str4 + "~" + str5 + "）";
/*     */           }
/* 186 */           arrayList1.add(str);
/* 187 */           str = TimeUtil.dateAdd(str, 1);
/*     */         } 
/*     */       } else {
/* 190 */         return str6;
/*     */       } 
/*     */     } 
/* 193 */     if (str1.compareTo(str2) > 0) {
/* 194 */       return SystemEnv.getHtmlLabelName(33937, paramInt) + "（" + str1 + "~" + str2 + "）";
/*     */     }
/* 196 */     String str3 = str1;
/* 197 */     while (str3.compareTo(str2) <= 0) {
/* 198 */       arrayList2.add(str3);
/* 199 */       str3 = TimeUtil.dateAdd(str3, 1);
/*     */     } 
/* 201 */     int j = arrayList1.size();
/* 202 */     int k = arrayList2.size();
/* 203 */     if (j != k)
/* 204 */       return SystemEnv.getHtmlLabelName(33938, paramInt) + "!"; 
/*     */     byte b2;
/* 206 */     for (b2 = 0; b2 < j; b2++) {
/* 207 */       if (!arrayList2.contains(arrayList1.get(b2))) {
/* 208 */         return SystemEnv.getHtmlLabelName(33938, paramInt) + "!!";
/*     */       }
/*     */     } 
/* 211 */     for (b2 = 0; b2 < k; b2++) {
/* 212 */       if (!arrayList1.contains(arrayList2.get(b2))) {
/* 213 */         return SystemEnv.getHtmlLabelName(33938, paramInt) + "!!!";
/*     */       }
/*     */     } 
/* 216 */     return "";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void updateFnaPeriod(FnaPeriod paramFnaPeriod, List<FnaPeriodDtl> paramList, int paramInt) throws Exception {
/* 228 */     RecordSetTrans recordSetTrans = null;
/*     */     try {
/* 230 */       FnaBaseBo fnaBaseBo = FnaBaseBo.getInstance();
/* 231 */       if (!fnaBaseBo.nonEmptyCheck(paramFnaPeriod.getFnaPeriodName())) {
/* 232 */         throw new FnaException(SystemEnv.getHtmlLabelName(10000050, Util.getIntValue(paramInt)));
/*     */       }
/* 234 */       if (!fnaBaseBo.nonNullCheck(paramFnaPeriod.getFnaCycle())) {
/* 235 */         throw new FnaException(SystemEnv.getHtmlLabelName(10000051, Util.getIntValue(paramInt)));
/*     */       }
/* 237 */       if (!fnaBaseBo.nonEmptyCheck(paramFnaPeriod.getStartdate())) {
/* 238 */         throw new FnaException(SystemEnv.getHtmlLabelName(10000052, Util.getIntValue(paramInt)));
/*     */       }
/*     */       
/* 241 */       if (paramFnaPeriod.getFnaCycle().intValue() < 1 && paramFnaPeriod.getFnaCycle().intValue() > 5) {
/* 242 */         throw new FnaException(SystemEnv.getHtmlLabelName(10000053, Util.getIntValue(paramInt)));
/*     */       }
/*     */       
/* 245 */       int i = paramList.size();
/* 246 */       if (i <= 0) {
/* 247 */         throw new FnaException(SystemEnv.getHtmlLabelName(10000054, Util.getIntValue(paramInt)));
/*     */       }
/*     */       
/* 250 */       if (paramFnaPeriod.getShowOrder() == null) {
/* 251 */         paramFnaPeriod.setShowOrder(Double.valueOf(0.0D));
/*     */       }
/*     */       
/* 254 */       RecordSet recordSet = new RecordSet();
/*     */       
/* 256 */       recordSet.executeQuery("select count(*) cnt from fnaPeriod where fnaPeriodName=? and fnaPeriodPk<>?", new Object[] { paramFnaPeriod
/* 257 */             .getFnaPeriodName(), paramFnaPeriod.getFnaPeriodPk() });
/* 258 */       if (recordSet.next() && recordSet.getInt("cnt") > 0) {
/* 259 */         throw new FnaException(SystemEnv.getHtmlLabelName(10000055, Util.getIntValue(paramInt)));
/*     */       }
/*     */       
/* 262 */       ArrayList<String> arrayList1 = new ArrayList();
/* 263 */       ArrayList<String> arrayList2 = new ArrayList();
/* 264 */       for (byte b1 = 0; b1 < i; b1++) {
/* 265 */         FnaPeriodDtl fnaPeriodDtl = paramList.get(b1);
/* 266 */         arrayList1.add(fnaPeriodDtl.getStartdate());
/* 267 */         arrayList2.add(fnaPeriodDtl.getEnddate());
/*     */       } 
/* 269 */       String str = checkFnaPeriodUpdate(arrayList1, arrayList2, paramInt);
/* 270 */       if (!"".equals(str)) {
/* 271 */         throw new FnaException(str);
/*     */       }
/*     */       
/* 274 */       recordSetTrans = new RecordSetTrans();
/* 275 */       boolean bool = recordSetTrans.setAutoCommit(false);
/* 276 */       if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(paramInt)));
/*     */       
/* 278 */       RecordSet4Action recordSet4Action = new RecordSet4Action(recordSetTrans);
/*     */       
/* 280 */       FnaPeriodDao fnaPeriodDao = new FnaPeriodDao();
/*     */       
/* 282 */       FnaPeriodDtlDao fnaPeriodDtlDao = new FnaPeriodDtlDao();
/*     */       
/* 284 */       fnaPeriodDao.update(recordSet4Action, paramFnaPeriod);
/*     */ 
/*     */       
/* 287 */       for (byte b2 = 0; b2 < i; b2++) {
/* 288 */         fnaPeriodDtlDao.saveOrUpdate(recordSet4Action, paramList.get(b2));
/*     */       }
/*     */       
/* 291 */       bool = recordSetTrans.commit();
/* 292 */       if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(paramInt))); 
/* 293 */     } catch (Exception exception) {
/*     */       try {
/* 295 */         if (recordSetTrans != null) {
/* 296 */           recordSetTrans.rollback();
/*     */         }
/* 298 */       } catch (Exception exception1) {}
/* 299 */       throw exception;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void saveFnaPeriod(FnaPeriod paramFnaPeriod, Integer paramInteger, int paramInt) throws Exception {
/* 312 */     RecordSetTrans recordSetTrans = null;
/*     */     try {
/* 314 */       FnaBaseBo fnaBaseBo = FnaBaseBo.getInstance();
/* 315 */       if (!fnaBaseBo.nonEmptyCheck(paramFnaPeriod.getFnaPeriodName())) {
/* 316 */         throw new FnaException(SystemEnv.getHtmlLabelName(10000050, Util.getIntValue(paramInt)));
/*     */       }
/* 318 */       if (!fnaBaseBo.nonNullCheck(paramFnaPeriod.getFnaCycle())) {
/* 319 */         throw new FnaException(SystemEnv.getHtmlLabelName(10000051, Util.getIntValue(paramInt)));
/*     */       }
/* 321 */       if (!fnaBaseBo.nonEmptyCheck(paramFnaPeriod.getStartdate())) {
/* 322 */         throw new FnaException(SystemEnv.getHtmlLabelName(10000052, Util.getIntValue(paramInt)));
/*     */       }
/*     */       
/* 325 */       if (paramFnaPeriod.getFnaCycle().intValue() < 1 && paramFnaPeriod.getFnaCycle().intValue() > 5) {
/* 326 */         throw new FnaException(SystemEnv.getHtmlLabelName(10000053, Util.getIntValue(paramInt)));
/*     */       }
/*     */       
/* 329 */       if (paramFnaPeriod.getShowOrder() == null) {
/* 330 */         paramFnaPeriod.setShowOrder(Double.valueOf(0.0D));
/*     */       }
/*     */ 
/*     */       
/* 334 */       String str1 = FnaCommon.getPrimaryKeyGuid1();
/* 335 */       paramFnaPeriod.setFnaPeriodPk(str1);
/* 336 */       paramFnaPeriod.setStatus(Integer.valueOf(0));
/*     */       
/* 338 */       RecordSet recordSet = new RecordSet();
/*     */       
/* 340 */       recordSet.executeQuery("select count(*) cnt from fnaPeriod where fnaPeriodName=? and fnaPeriodPk<>?", new Object[] { paramFnaPeriod
/* 341 */             .getFnaPeriodName(), paramFnaPeriod.getFnaPeriodPk() });
/* 342 */       if (recordSet.next() && recordSet.getInt("cnt") > 0) {
/* 343 */         throw new FnaException(SystemEnv.getHtmlLabelName(10000055, Util.getIntValue(paramInt)));
/*     */       }
/*     */       
/* 346 */       int i = 0;
/* 347 */       int j = 0;
/* 348 */       if (paramFnaPeriod.getFnaCycle().intValue() == 5) {
/* 349 */         if (!fnaBaseBo.nonNullCheck(paramInteger) || paramInteger.intValue() <= 0) {
/* 350 */           throw new FnaException(SystemEnv.getHtmlLabelName(10000054, Util.getIntValue(paramInt)));
/*     */         }
/*     */ 
/*     */         
/* 354 */         int m = TimeUtil.dateInterval(paramFnaPeriod.getStartdate(), paramFnaPeriod.getEnddate()) + 1;
/* 355 */         if (m <= 0) {
/* 356 */           throw new FnaException(SystemEnv.getHtmlLabelName(127396, Util.getIntValue(paramInt)));
/*     */         }
/*     */         
/* 359 */         i = m / paramInteger.intValue();
/* 360 */         if (i <= 0) {
/* 361 */           throw new FnaException(SystemEnv.getHtmlLabelName(127397, Util.getIntValue(paramInt)));
/*     */         }
/* 363 */         j = m % paramInteger.intValue();
/*     */       } else {
/*     */         
/* 366 */         String str = dateAddByMonth(paramFnaPeriod.getStartdate(), 12);
/* 367 */         str = TimeUtil.dateAdd(str, -1);
/* 368 */         paramFnaPeriod.setEnddate(str);
/*     */       } 
/*     */       
/* 371 */       if (!fnaBaseBo.nonEmptyCheck(paramFnaPeriod.getEnddate())) {
/* 372 */         throw new FnaException(SystemEnv.getHtmlLabelName(10000056, Util.getIntValue(paramInt)));
/*     */       }
/*     */ 
/*     */       
/* 376 */       ArrayList<FnaPeriodDtl> arrayList = new ArrayList();
/*     */       
/* 378 */       if (paramFnaPeriod.getFnaCycle().intValue() == 1) {
/*     */         
/* 380 */         String str3 = paramFnaPeriod.getStartdate();
/* 381 */         String str4 = dateAddByMonth(str3, 1);
/* 382 */         str4 = TimeUtil.dateAdd(str4, -1);
/* 383 */         for (byte b = 1; b <= 12; b++) {
/* 384 */           FnaPeriodDtl fnaPeriodDtl = new FnaPeriodDtl();
/* 385 */           arrayList.add(fnaPeriodDtl);
/* 386 */           fnaPeriodDtl.setFnaPeriodDtlPk(FnaCommon.getPrimaryKeyGuid1());
/* 387 */           fnaPeriodDtl.setFnaPeriodPk(str1);
/* 388 */           fnaPeriodDtl.setStartdate(str3);
/* 389 */           fnaPeriodDtl.setEnddate(str4);
/* 390 */           fnaPeriodDtl.setFnaPeriodsList(Integer.valueOf(b));
/* 391 */           fnaPeriodDtl.setFnaPeriodsName(b + "");
/* 392 */           fnaPeriodDtl.setShowOrder(Double.valueOf(b * 1.0D));
/*     */           
/* 394 */           str3 = TimeUtil.dateAdd(str4, 1);
/* 395 */           str4 = dateAddByMonth(str3, 1);
/* 396 */           str4 = TimeUtil.dateAdd(str4, -1);
/*     */         }
/*     */       
/* 399 */       } else if (paramFnaPeriod.getFnaCycle().intValue() == 2) {
/*     */         
/* 401 */         String str3 = paramFnaPeriod.getStartdate();
/* 402 */         String str4 = dateAddByMonth(str3, 3);
/* 403 */         str4 = TimeUtil.dateAdd(str4, -1);
/* 404 */         for (byte b = 1; b <= 4; b++) {
/* 405 */           FnaPeriodDtl fnaPeriodDtl = new FnaPeriodDtl();
/* 406 */           arrayList.add(fnaPeriodDtl);
/* 407 */           fnaPeriodDtl.setFnaPeriodDtlPk(FnaCommon.getPrimaryKeyGuid1());
/* 408 */           fnaPeriodDtl.setFnaPeriodPk(str1);
/* 409 */           fnaPeriodDtl.setStartdate(str3);
/* 410 */           fnaPeriodDtl.setEnddate(str4);
/* 411 */           fnaPeriodDtl.setFnaPeriodsList(Integer.valueOf(b));
/* 412 */           fnaPeriodDtl.setFnaPeriodsName(b + "");
/* 413 */           fnaPeriodDtl.setShowOrder(Double.valueOf(b * 1.0D));
/*     */           
/* 415 */           str3 = TimeUtil.dateAdd(str4, 1);
/* 416 */           str4 = dateAddByMonth(str3, 3);
/* 417 */           str4 = TimeUtil.dateAdd(str4, -1);
/*     */         }
/*     */       
/* 420 */       } else if (paramFnaPeriod.getFnaCycle().intValue() == 3) {
/*     */         
/* 422 */         String str3 = paramFnaPeriod.getStartdate();
/* 423 */         String str4 = dateAddByMonth(str3, 6);
/* 424 */         str4 = TimeUtil.dateAdd(str4, -1);
/* 425 */         for (byte b = 1; b <= 2; b++) {
/* 426 */           FnaPeriodDtl fnaPeriodDtl = new FnaPeriodDtl();
/* 427 */           arrayList.add(fnaPeriodDtl);
/* 428 */           fnaPeriodDtl.setFnaPeriodDtlPk(FnaCommon.getPrimaryKeyGuid1());
/* 429 */           fnaPeriodDtl.setFnaPeriodPk(str1);
/* 430 */           fnaPeriodDtl.setStartdate(str3);
/* 431 */           fnaPeriodDtl.setEnddate(str4);
/* 432 */           fnaPeriodDtl.setFnaPeriodsList(Integer.valueOf(b));
/* 433 */           fnaPeriodDtl.setFnaPeriodsName(b + "");
/* 434 */           fnaPeriodDtl.setShowOrder(Double.valueOf(b * 1.0D));
/*     */           
/* 436 */           str3 = TimeUtil.dateAdd(str4, 1);
/* 437 */           str4 = dateAddByMonth(str3, 6);
/* 438 */           str4 = TimeUtil.dateAdd(str4, -1);
/*     */         }
/*     */       
/* 441 */       } else if (paramFnaPeriod.getFnaCycle().intValue() == 4) {
/*     */         
/* 443 */         String str3 = paramFnaPeriod.getStartdate();
/* 444 */         String str4 = dateAddByMonth(str3, 12);
/* 445 */         str4 = TimeUtil.dateAdd(str4, -1);
/* 446 */         FnaPeriodDtl fnaPeriodDtl = new FnaPeriodDtl();
/* 447 */         arrayList.add(fnaPeriodDtl);
/* 448 */         fnaPeriodDtl.setFnaPeriodDtlPk(FnaCommon.getPrimaryKeyGuid1());
/* 449 */         fnaPeriodDtl.setFnaPeriodPk(str1);
/* 450 */         fnaPeriodDtl.setStartdate(str3);
/* 451 */         fnaPeriodDtl.setEnddate(str4);
/* 452 */         fnaPeriodDtl.setFnaPeriodsList(Integer.valueOf(1));
/* 453 */         fnaPeriodDtl.setFnaPeriodsName("1");
/* 454 */         fnaPeriodDtl.setShowOrder(Double.valueOf(1.0D));
/*     */       }
/* 456 */       else if (paramFnaPeriod.getFnaCycle().intValue() == 5) {
/*     */         
/* 458 */         String str3 = paramFnaPeriod.getStartdate();
/* 459 */         String str4 = TimeUtil.dateAdd(str3, i - 1);
/* 460 */         for (byte b = 1; b <= paramInteger.intValue() && 
/* 461 */           TimeUtil.dateInterval(paramFnaPeriod.getStartdate(), str4) >= 0; b++) {
/*     */ 
/*     */           
/* 464 */           if (b == paramInteger.intValue() && j > 0) {
/* 465 */             str4 = TimeUtil.dateAdd(str4, j);
/*     */           }
/* 467 */           FnaPeriodDtl fnaPeriodDtl = new FnaPeriodDtl();
/* 468 */           arrayList.add(fnaPeriodDtl);
/* 469 */           fnaPeriodDtl.setFnaPeriodDtlPk(FnaCommon.getPrimaryKeyGuid1());
/* 470 */           fnaPeriodDtl.setFnaPeriodPk(str1);
/* 471 */           fnaPeriodDtl.setStartdate(str3);
/* 472 */           fnaPeriodDtl.setEnddate(str4);
/* 473 */           fnaPeriodDtl.setFnaPeriodsList(Integer.valueOf(b));
/* 474 */           fnaPeriodDtl.setFnaPeriodsName(b + "");
/* 475 */           fnaPeriodDtl.setShowOrder(Double.valueOf(b * 1.0D));
/*     */           
/* 477 */           str3 = TimeUtil.dateAdd(str4, 1);
/* 478 */           str4 = TimeUtil.dateAdd(str3, i - 1);
/*     */         } 
/*     */       } 
/*     */ 
/*     */ 
/*     */       
/* 484 */       int k = arrayList.size();
/* 485 */       if (k <= 0) {
/* 486 */         throw new FnaException(SystemEnv.getHtmlLabelName(10000054, Util.getIntValue(paramInt)));
/*     */       }
/*     */       
/* 489 */       ArrayList<String> arrayList1 = new ArrayList();
/* 490 */       ArrayList<String> arrayList2 = new ArrayList();
/* 491 */       for (byte b1 = 0; b1 < k; b1++) {
/* 492 */         FnaPeriodDtl fnaPeriodDtl = arrayList.get(b1);
/* 493 */         arrayList1.add(fnaPeriodDtl.getStartdate());
/* 494 */         arrayList2.add(fnaPeriodDtl.getEnddate());
/*     */       } 
/* 496 */       String str2 = checkFnaPeriodUpdate(arrayList1, arrayList2, paramInt);
/* 497 */       if (!"".equals(str2)) {
/* 498 */         throw new FnaException(str2);
/*     */       }
/*     */       
/* 501 */       recordSetTrans = new RecordSetTrans();
/* 502 */       boolean bool = recordSetTrans.setAutoCommit(false);
/* 503 */       if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(paramInt)));
/*     */       
/* 505 */       RecordSet4Action recordSet4Action = new RecordSet4Action(recordSetTrans);
/*     */       
/* 507 */       FnaPeriodDao fnaPeriodDao = new FnaPeriodDao();
/*     */       
/* 509 */       FnaPeriodDtlDao fnaPeriodDtlDao = new FnaPeriodDtlDao();
/*     */       
/* 511 */       fnaPeriodDao.save(recordSet4Action, paramFnaPeriod);
/*     */ 
/*     */       
/* 514 */       for (byte b2 = 0; b2 < k; b2++) {
/* 515 */         fnaPeriodDtlDao.save(recordSet4Action, arrayList.get(b2));
/*     */       }
/*     */       
/* 518 */       bool = recordSetTrans.commit();
/* 519 */       if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(paramInt))); 
/* 520 */     } catch (Exception exception) {
/*     */       try {
/* 522 */         if (recordSetTrans != null) {
/* 523 */           recordSetTrans.rollback();
/*     */         }
/* 525 */       } catch (Exception exception1) {}
/* 526 */       throw exception;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   private static String dateAddByMonth(String paramString, int paramInt) {
/* 538 */     Calendar calendar = TimeUtil.getCalendar(paramString);
/* 539 */     if (calendar == null) return ""; 
/* 540 */     calendar.add(2, paramInt);
/* 541 */     return TimeUtil.getDateString(calendar);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/* 561 */   private static final FnaPeriodBo thisClassObj = new FnaPeriodBo();
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public static FnaPeriodBo getInstance() {
/* 568 */     return thisClassObj;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/fna/e9/bo/base/FnaPeriodBo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */