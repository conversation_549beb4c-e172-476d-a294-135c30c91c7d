/*     */ package weaver.fna.e9.bo.base;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.List;
/*     */ import weaver.conn.RecordSetTrans;
/*     */ import weaver.fna.e9.dao.base.FnaBaseDao;
/*     */ import weaver.fna.e9.exception.FnaException;
/*     */ import weaver.fna.e9.po.base.FnaBrowsLastSearchResult;
/*     */ import weaver.fna.general.RecordSet4Action;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ @Deprecated
/*     */ public class FnaBrowsLastSearchResultBo
/*     */ {
/*     */   @Deprecated
/*     */   public void saveData(String paramString1, String paramString2, int paramInt1, int paramInt2) throws Exception {
/*  37 */     RecordSetTrans recordSetTrans = null;
/*     */     try {
/*  39 */       recordSetTrans = new RecordSetTrans();
/*  40 */       boolean bool = recordSetTrans.setAutoCommit(false);
/*  41 */       if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(paramInt2)));
/*     */       
/*  43 */       bool = recordSetTrans.executeUpdate("delete from fnaBrowsLastSearchResult where charFk=? and browserType=? and userId=?", new Object[] { paramString2, paramString1, 
/*  44 */             Integer.valueOf(paramInt1) });
/*  45 */       if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(paramInt2)));
/*     */       
/*  47 */       bool = recordSetTrans.executeUpdate("update fnaBrowsLastSearchResult  set showOrder = showOrder+1  where browserType=? and userId=?", new Object[] { paramString1, 
/*     */ 
/*     */             
/*  50 */             Integer.valueOf(paramInt1) });
/*  51 */       if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(paramInt2)));
/*     */       
/*  53 */       bool = recordSetTrans.executeUpdate("insert into fnaBrowsLastSearchResult(charFk, browserType, userId, showOrder)values(?, ?, ?, ?)", new Object[] { paramString2, paramString1, 
/*  54 */             Integer.valueOf(paramInt1), "0" });
/*  55 */       if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(paramInt2)));
/*     */       
/*  57 */       bool = recordSetTrans.executeUpdate("delete from fnaBrowsLastSearchResult where showOrder>10 and browserType=? and userId=?", new Object[] { paramString1, 
/*  58 */             Integer.valueOf(paramInt1) });
/*  59 */       if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(paramInt2)));
/*     */       
/*  61 */       bool = recordSetTrans.commit();
/*  62 */       if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(paramInt2))); 
/*  63 */     } catch (Exception exception) {
/*     */       try {
/*  65 */         if (recordSetTrans != null) {
/*  66 */           recordSetTrans.rollback();
/*     */         }
/*  68 */       } catch (Exception exception1) {}
/*  69 */       throw exception;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public List<FnaBrowsLastSearchResult> loadAllTreeList(RecordSet4Action paramRecordSet4Action, String paramString1, String paramString2, String paramString3, int paramInt1, int paramInt2) throws Exception {
/*  86 */     FnaBaseBo fnaBaseBo = FnaBaseBo.getInstance();
/*  87 */     FnaBaseDao fnaBaseDao = new FnaBaseDao();
/*     */     
/*  89 */     ArrayList<FnaBrowsLastSearchResult> arrayList = new ArrayList();
/*  90 */     if ("fnaSubjectAccount".equals(paramString1)) {
/*  91 */       FnaSubjectAccountBo fnaSubjectAccountBo = FnaSubjectAccountBo.getInstance();
/*  92 */       List<String> list = fnaSubjectAccountBo.getAllSubSubjectaPkList(paramString3, paramInt2);
/*     */       
/*  94 */       ArrayList<String> arrayList1 = new ArrayList();
/*  95 */       StringBuffer stringBuffer = new StringBuffer("select a.fnaSubjectaPk charPk, a.supFnaSubjectaPk charFk, a.subjectaName showName, a.subjectaName fullShowName ");
/*  96 */       stringBuffer.append(" from fnaSubjectAccount a where 1=1 ");
/*  97 */       if (fnaBaseBo.nonEmptyCheck(paramString3)) {
/*  98 */         stringBuffer.append(" and a.fnaSubjectaPk <> ? ");
/*  99 */         arrayList1.add(paramString3);
/*     */       } 
/* 101 */       if (!"ALL_fnaPeriodPk".equals(paramString2)) {
/* 102 */         stringBuffer.append(" and a.fnaPeriodPk=? ");
/* 103 */         arrayList1.add(paramString2);
/*     */       } 
/* 105 */       stringBuffer.append(" order by a.showOrder asc, a.subjectaCode asc, a.subjectaName asc, a.id desc");
/* 106 */       List<FnaBrowsLastSearchResult> list1 = fnaBaseDao.queryForObjectList(paramRecordSet4Action, FnaBrowsLastSearchResult.class.getName(), stringBuffer.toString(), arrayList1.toArray());
/*     */       
/* 108 */       loadFnaBrowsLastSearchResult_dataList_fnaSubjectAccount(list1, list, arrayList, paramInt2);
/*     */     }
/* 110 */     else if ("fnaSubjectBudget".equals(paramString1)) {
/* 111 */       FnaSubjectBudgetBo fnaSubjectBudgetBo = FnaSubjectBudgetBo.getInstance();
/* 112 */       List<String> list = fnaSubjectBudgetBo.getAllSubSubjectbPkList(paramString3, paramInt2);
/*     */       
/* 114 */       ArrayList<String> arrayList1 = new ArrayList();
/* 115 */       StringBuffer stringBuffer = new StringBuffer("select a.fnaSubjectbPk charPk, a.supFnaSubjectbPk charFk, a.subjectbName showName, a.subjectbName fullShowName ");
/* 116 */       stringBuffer.append(" from fnaSubjectBudget a where 1=1 ");
/* 117 */       if (fnaBaseBo.nonEmptyCheck(paramString3)) {
/* 118 */         stringBuffer.append(" and a.fnaSubjectbPk <> ? ");
/* 119 */         arrayList1.add(paramString3);
/*     */       } 
/* 121 */       if (!"ALL_fnaPeriodPk".equals(paramString2)) {
/* 122 */         stringBuffer.append(" and a.fnaPeriodPk=? ");
/* 123 */         arrayList1.add(paramString2);
/*     */       } 
/* 125 */       stringBuffer.append(" order by a.showOrder asc, a.subjectbCode asc, a.subjectbName asc, a.id desc");
/* 126 */       List<FnaBrowsLastSearchResult> list1 = fnaBaseDao.queryForObjectList(paramRecordSet4Action, FnaBrowsLastSearchResult.class.getName(), stringBuffer.toString(), arrayList1.toArray());
/*     */       
/* 128 */       loadFnaBrowsLastSearchResult_dataList_fnaSubjectBudget(list1, list, arrayList, paramInt2);
/*     */     } 
/*     */ 
/*     */     
/* 132 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public List<FnaBrowsLastSearchResult> loadTreeList(RecordSet4Action paramRecordSet4Action, String paramString1, String paramString2, String paramString3, String paramString4, int paramInt1, int paramInt2) throws Exception {
/* 149 */     FnaBaseBo fnaBaseBo = FnaBaseBo.getInstance();
/* 150 */     FnaBaseDao fnaBaseDao = new FnaBaseDao();
/*     */     
/* 152 */     ArrayList<FnaBrowsLastSearchResult> arrayList = new ArrayList();
/* 153 */     if ("fnaSubjectAccount".equals(paramString1)) {
/* 154 */       FnaSubjectAccountBo fnaSubjectAccountBo = FnaSubjectAccountBo.getInstance();
/* 155 */       List<String> list = fnaSubjectAccountBo.getAllSubSubjectaPkList(paramString3, paramInt2);
/*     */       
/* 157 */       ArrayList<String> arrayList1 = new ArrayList();
/* 158 */       StringBuffer stringBuffer = new StringBuffer("select a.fnaSubjectaPk charPk, a.supFnaSubjectaPk charFk, a.subjectaName showName, a.subjectaName fullShowName ");
/* 159 */       stringBuffer.append(" from fnaSubjectAccount a where 1=1 ");
/* 160 */       if (fnaBaseBo.nonEmptyCheck(paramString3)) {
/* 161 */         stringBuffer.append(" and a.fnaSubjectaPk <> ? ");
/* 162 */         arrayList1.add(paramString3);
/*     */       } 
/* 164 */       if (!"ALL_fnaPeriodPk".equals(paramString2)) {
/* 165 */         stringBuffer.append(" and a.fnaPeriodPk=? ");
/* 166 */         arrayList1.add(paramString2);
/*     */       } 
/* 168 */       if (fnaBaseBo.nonEmptyCheck(paramString4)) {
/* 169 */         stringBuffer.append(" and a.supFnaSubjectaPk=? ");
/* 170 */         arrayList1.add(paramString4);
/*     */       } else {
/* 172 */         stringBuffer.append(" and (a.supFnaSubjectaPk is null or a.supFnaSubjectaPk = '') ");
/*     */       } 
/* 174 */       stringBuffer.append(" order by a.showOrder asc, a.subjectaCode asc, a.subjectaName asc, a.id desc");
/* 175 */       List<FnaBrowsLastSearchResult> list1 = fnaBaseDao.queryForObjectList(paramRecordSet4Action, FnaBrowsLastSearchResult.class.getName(), stringBuffer.toString(), arrayList1.toArray());
/*     */       
/* 177 */       loadFnaBrowsLastSearchResult_dataList_fnaSubjectAccount(list1, list, arrayList, paramInt2);
/*     */     }
/* 179 */     else if ("fnaSubjectBudget".equals(paramString1)) {
/* 180 */       FnaSubjectBudgetBo fnaSubjectBudgetBo = FnaSubjectBudgetBo.getInstance();
/* 181 */       List<String> list = fnaSubjectBudgetBo.getAllSubSubjectbPkList(paramString3, paramInt2);
/*     */       
/* 183 */       ArrayList<String> arrayList1 = new ArrayList();
/* 184 */       StringBuffer stringBuffer = new StringBuffer("select a.fnaSubjectbPk charPk, a.supFnaSubjectbPk charFk, a.subjectbName showName, a.subjectbName fullShowName ");
/* 185 */       stringBuffer.append(" from fnaSubjectBudget a where 1=1 ");
/* 186 */       if (fnaBaseBo.nonEmptyCheck(paramString3)) {
/* 187 */         stringBuffer.append(" and a.fnaSubjectbPk <> ? ");
/* 188 */         arrayList1.add(paramString3);
/*     */       } 
/* 190 */       if (!"ALL_fnaPeriodPk".equals(paramString2)) {
/* 191 */         stringBuffer.append(" and a.fnaPeriodPk=? ");
/* 192 */         arrayList1.add(paramString2);
/*     */       } 
/* 194 */       if (fnaBaseBo.nonEmptyCheck(paramString4)) {
/* 195 */         stringBuffer.append(" and a.supFnaSubjectbPk=? ");
/* 196 */         arrayList1.add(paramString4);
/*     */       } else {
/* 198 */         stringBuffer.append(" and (a.supFnaSubjectbPk is null or a.supFnaSubjectbPk = '') ");
/*     */       } 
/* 200 */       stringBuffer.append(" order by a.showOrder asc, a.subjectbCode asc, a.subjectbName asc, a.id desc");
/* 201 */       List<FnaBrowsLastSearchResult> list1 = fnaBaseDao.queryForObjectList(paramRecordSet4Action, FnaBrowsLastSearchResult.class.getName(), stringBuffer.toString(), arrayList1.toArray());
/*     */       
/* 203 */       loadFnaBrowsLastSearchResult_dataList_fnaSubjectBudget(list1, list, arrayList, paramInt2);
/*     */     } 
/*     */ 
/*     */     
/* 207 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public List<FnaBrowsLastSearchResult> loadDataListByQryName(RecordSet4Action paramRecordSet4Action, String paramString1, String paramString2, String paramString3, String paramString4, int paramInt) throws Exception {
/* 223 */     FnaBaseBo fnaBaseBo = FnaBaseBo.getInstance();
/* 224 */     FnaBaseDao fnaBaseDao = new FnaBaseDao();
/*     */     
/* 226 */     ArrayList<FnaBrowsLastSearchResult> arrayList = new ArrayList();
/* 227 */     if ("fnaSubjectAccount".equals(paramString1)) {
/* 228 */       FnaSubjectAccountBo fnaSubjectAccountBo = FnaSubjectAccountBo.getInstance();
/* 229 */       List<String> list = fnaSubjectAccountBo.getAllSubSubjectaPkList(paramString3, paramInt);
/*     */       
/* 231 */       ArrayList<String> arrayList1 = new ArrayList();
/* 232 */       StringBuffer stringBuffer = new StringBuffer("select a.fnaSubjectaPk charPk, a.supFnaSubjectaPk charFk, a.subjectaName showName, a.subjectaName fullShowName ");
/* 233 */       stringBuffer.append(" from fnaSubjectAccount a where 1=1 ");
/* 234 */       if (fnaBaseBo.nonEmptyCheck(paramString4)) {
/* 235 */         stringBuffer.append(" and a.subjectaName like ? ");
/* 236 */         arrayList1.add("%" + paramString4 + "%");
/*     */       } 
/* 238 */       if (fnaBaseBo.nonEmptyCheck(paramString3)) {
/* 239 */         stringBuffer.append(" and a.fnaSubjectaPk <> ? ");
/* 240 */         arrayList1.add(paramString3);
/*     */       } 
/* 242 */       if (!"ALL_fnaPeriodPk".equals(paramString2)) {
/* 243 */         stringBuffer.append(" and a.fnaPeriodPk=? ");
/* 244 */         arrayList1.add(paramString2);
/*     */       } 
/* 246 */       stringBuffer.append(" order by a.showOrder asc, a.subjectaCode asc, a.subjectaName asc, a.id desc");
/* 247 */       List<FnaBrowsLastSearchResult> list1 = fnaBaseDao.queryForObjectList(paramRecordSet4Action, FnaBrowsLastSearchResult.class.getName(), stringBuffer.toString(), arrayList1.toArray());
/*     */       
/* 249 */       loadFnaBrowsLastSearchResult_dataList_fnaSubjectAccount(list1, list, arrayList, paramInt);
/*     */     }
/* 251 */     else if ("fnaSubjectBudget".equals(paramString1)) {
/* 252 */       FnaSubjectBudgetBo fnaSubjectBudgetBo = FnaSubjectBudgetBo.getInstance();
/* 253 */       List<String> list = fnaSubjectBudgetBo.getAllSubSubjectbPkList(paramString3, paramInt);
/*     */       
/* 255 */       ArrayList<String> arrayList1 = new ArrayList();
/* 256 */       StringBuffer stringBuffer = new StringBuffer("select a.fnaSubjectbPk charPk, a.supFnaSubjectbPk charFk, a.subjectbName showName, a.subjectbName fullShowName ");
/* 257 */       stringBuffer.append(" from fnaSubjectBudget a where 1=1 ");
/* 258 */       if (fnaBaseBo.nonEmptyCheck(paramString4)) {
/* 259 */         stringBuffer.append(" and a.subjectbName like ? ");
/* 260 */         arrayList1.add("%" + paramString4 + "%");
/*     */       } 
/* 262 */       if (fnaBaseBo.nonEmptyCheck(paramString3)) {
/* 263 */         stringBuffer.append(" and a.fnaSubjectbPk <> ? ");
/* 264 */         arrayList1.add(paramString3);
/*     */       } 
/* 266 */       if (!"ALL_fnaPeriodPk".equals(paramString2)) {
/* 267 */         stringBuffer.append(" and a.fnaPeriodPk=? ");
/* 268 */         arrayList1.add(paramString2);
/*     */       } 
/* 270 */       stringBuffer.append(" order by a.showOrder asc, a.subjectbCode asc, a.subjectbName asc, a.id desc");
/* 271 */       List<FnaBrowsLastSearchResult> list1 = fnaBaseDao.queryForObjectList(paramRecordSet4Action, FnaBrowsLastSearchResult.class.getName(), stringBuffer.toString(), arrayList1.toArray());
/*     */       
/* 273 */       loadFnaBrowsLastSearchResult_dataList_fnaSubjectBudget(list1, list, arrayList, paramInt);
/*     */     } 
/*     */ 
/*     */     
/* 277 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public List<FnaBrowsLastSearchResult> loadDataList(RecordSet4Action paramRecordSet4Action, String paramString1, String paramString2, String paramString3, int paramInt1, int paramInt2) throws Exception {
/* 293 */     FnaBaseBo fnaBaseBo = FnaBaseBo.getInstance();
/* 294 */     FnaBaseDao fnaBaseDao = new FnaBaseDao();
/*     */     
/* 296 */     ArrayList<FnaBrowsLastSearchResult> arrayList = new ArrayList();
/* 297 */     if ("fnaSubjectAccount".equals(paramString1)) {
/* 298 */       FnaSubjectAccountBo fnaSubjectAccountBo = FnaSubjectAccountBo.getInstance();
/* 299 */       List<String> list = fnaSubjectAccountBo.getAllSubSubjectaPkList(paramString3, paramInt2);
/*     */       
/* 301 */       ArrayList<String> arrayList1 = new ArrayList();
/* 302 */       StringBuffer stringBuffer = new StringBuffer("select b.fnaSubjectaPk charPk, b.supFnaSubjectaPk charFk, b.subjectaName showName, b.subjectaName fullShowName ");
/* 303 */       stringBuffer.append(" from fnaBrowsLastSearchResult a ");
/* 304 */       stringBuffer.append(" join fnaSubjectAccount b on a.charFk = b.fnaSubjectaPk ");
/* 305 */       stringBuffer.append(" where 1=1 ");
/* 306 */       if (fnaBaseBo.nonEmptyCheck(paramString3)) {
/* 307 */         stringBuffer.append(" and b.fnaSubjectaPk <> ? ");
/* 308 */         arrayList1.add(paramString3);
/*     */       } 
/* 310 */       if (!"ALL_fnaPeriodPk".equals(paramString2)) {
/* 311 */         stringBuffer.append(" and b.fnaPeriodPk=? ");
/* 312 */         arrayList1.add(paramString2);
/*     */       } 
/* 314 */       stringBuffer.append(" and a.browserType=? and a.userId=? ");
/* 315 */       arrayList1.add(paramString1);
/* 316 */       arrayList1.add(Integer.valueOf(paramInt1));
/* 317 */       stringBuffer.append(" order by a.showOrder asc, b.showOrder asc, b.subjectaCode asc, b.subjectaName asc, b.id desc");
/* 318 */       List<FnaBrowsLastSearchResult> list1 = fnaBaseDao.queryForObjectList(paramRecordSet4Action, FnaBrowsLastSearchResult.class.getName(), stringBuffer.toString(), arrayList1.toArray());
/*     */       
/* 320 */       loadFnaBrowsLastSearchResult_dataList_fnaSubjectAccount(list1, list, arrayList, paramInt2);
/*     */     }
/* 322 */     else if ("fnaSubjectBudget".equals(paramString1)) {
/* 323 */       FnaSubjectBudgetBo fnaSubjectBudgetBo = FnaSubjectBudgetBo.getInstance();
/* 324 */       List<String> list = fnaSubjectBudgetBo.getAllSubSubjectbPkList(paramString3, paramInt2);
/*     */       
/* 326 */       ArrayList<String> arrayList1 = new ArrayList();
/* 327 */       StringBuffer stringBuffer = new StringBuffer("select b.fnaSubjectbPk charPk, b.supFnaSubjectbPk charFk, b.subjectbName showName, b.subjectbName fullShowName ");
/* 328 */       stringBuffer.append(" from fnaBrowsLastSearchResult a ");
/* 329 */       stringBuffer.append(" join fnaSubjectBudget b on a.charFk = b.fnaSubjectbPk ");
/* 330 */       stringBuffer.append(" where 1=1 ");
/* 331 */       if (fnaBaseBo.nonEmptyCheck(paramString3)) {
/* 332 */         stringBuffer.append(" and b.fnaSubjectbPk <> ? ");
/* 333 */         arrayList1.add(paramString3);
/*     */       } 
/* 335 */       if (!"ALL_fnaPeriodPk".equals(paramString2)) {
/* 336 */         stringBuffer.append(" and b.fnaPeriodPk=? ");
/* 337 */         arrayList1.add(paramString2);
/*     */       } 
/* 339 */       stringBuffer.append(" and a.browserType=? and a.userId=? ");
/* 340 */       arrayList1.add(paramString1);
/* 341 */       arrayList1.add(Integer.valueOf(paramInt1));
/* 342 */       stringBuffer.append(" order by a.showOrder asc, b.showOrder asc, b.subjectbCode asc, b.subjectbName asc, b.id desc");
/* 343 */       List<FnaBrowsLastSearchResult> list1 = fnaBaseDao.queryForObjectList(paramRecordSet4Action, FnaBrowsLastSearchResult.class.getName(), stringBuffer.toString(), arrayList1.toArray());
/*     */       
/* 345 */       loadFnaBrowsLastSearchResult_dataList_fnaSubjectBudget(list1, list, arrayList, paramInt2);
/*     */     } 
/*     */ 
/*     */     
/* 349 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void loadFnaBrowsLastSearchResult_dataList_fnaSubjectBudget(List<FnaBrowsLastSearchResult> paramList1, List<String> paramList, List<FnaBrowsLastSearchResult> paramList2, int paramInt) throws Exception {
/* 363 */     FnaSubjectBudgetBo fnaSubjectBudgetBo = FnaSubjectBudgetBo.getInstance();
/* 364 */     int i = paramList1.size();
/* 365 */     for (byte b = 0; b < i; b++) {
/* 366 */       FnaBrowsLastSearchResult fnaBrowsLastSearchResult = paramList1.get(b);
/*     */       
/* 368 */       if (!paramList.contains(fnaBrowsLastSearchResult.getCharPk())) {
/* 369 */         paramList2.add(fnaBrowsLastSearchResult);
/* 370 */         fnaBrowsLastSearchResult.setFullShowName(fnaSubjectBudgetBo.getFullShowName(fnaBrowsLastSearchResult.getCharPk(), paramInt));
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void loadFnaBrowsLastSearchResult_dataList_fnaSubjectAccount(List<FnaBrowsLastSearchResult> paramList1, List<String> paramList, List<FnaBrowsLastSearchResult> paramList2, int paramInt) throws Exception {
/* 386 */     FnaSubjectAccountBo fnaSubjectAccountBo = FnaSubjectAccountBo.getInstance();
/* 387 */     int i = paramList1.size();
/* 388 */     for (byte b = 0; b < i; b++) {
/* 389 */       FnaBrowsLastSearchResult fnaBrowsLastSearchResult = paramList1.get(b);
/*     */       
/* 391 */       if (!paramList.contains(fnaBrowsLastSearchResult.getCharPk())) {
/* 392 */         paramList2.add(fnaBrowsLastSearchResult);
/* 393 */         fnaBrowsLastSearchResult.setFullShowName(fnaSubjectAccountBo.getFullShowName(fnaBrowsLastSearchResult.getCharPk(), paramInt));
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/* 413 */   private static final FnaBrowsLastSearchResultBo thisClassObj = new FnaBrowsLastSearchResultBo();
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static FnaBrowsLastSearchResultBo getInstance() {
/* 419 */     return thisClassObj;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/fna/e9/bo/base/FnaBrowsLastSearchResultBo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */