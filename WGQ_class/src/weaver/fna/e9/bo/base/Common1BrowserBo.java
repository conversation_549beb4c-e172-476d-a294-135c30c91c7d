/*    */ package weaver.fna.e9.bo.base;
/*    */ 
/*    */ import weaver.hrm.HrmUserVarify;
/*    */ import weaver.hrm.User;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ @Deprecated
/*    */ public class Common1BrowserBo
/*    */ {
/*    */   @Deprecated
/*    */   public boolean validate(String paramString, User paramUser) throws Exception {
/* 28 */     boolean bool = false;
/* 29 */     if ("fnaSubjectAccount".equals(paramString) || "fnaSubjectBudget".equals(paramString)) {
/* 30 */       bool = (HrmUserVarify.checkUserRight("FnaLedgerAdd:Add", paramUser) || HrmUserVarify.checkUserRight("FnaLedgerEdit:Edit", paramUser)) ? true : false;
/*    */     }
/* 32 */     return bool;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/* 43 */   private static final Common1BrowserBo thisClassObj = new Common1BrowserBo();
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public static Common1BrowserBo getInstance() {
/* 49 */     return thisClassObj;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/fna/e9/bo/base/Common1BrowserBo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */