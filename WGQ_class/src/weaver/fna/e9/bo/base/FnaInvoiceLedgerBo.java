/*     */ package weaver.fna.e9.bo.base;
/*     */ 
/*     */ import java.math.RoundingMode;
/*     */ import java.text.DecimalFormat;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.regex.Matcher;
/*     */ import java.util.regex.Pattern;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.conn.RecordSetTrans;
/*     */ import weaver.fna.e9.dao.base.FnaBaseDao;
/*     */ import weaver.fna.e9.exception.FnaException;
/*     */ import weaver.fna.e9.po.base.FnaInvoiceLedger;
/*     */ import weaver.fna.general.FnaCommon;
/*     */ import weaver.fna.general.RecordSet4Action;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.TimeUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class FnaInvoiceLedgerBo
/*     */ {
/*  38 */   BaseBean bb = new BaseBean();
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  43 */   DecimalFormat df2 = new DecimalFormat("####################################################0.00");
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int queryMaxId(RecordSet4Action paramRecordSet4Action, String paramString, int paramInt) throws Exception {
/*  54 */     ArrayList<Integer> arrayList = new ArrayList();
/*  55 */     StringBuffer stringBuffer = new StringBuffer("select max(a.id) maxId from fnaInvoiceLedger a where 1=?");
/*  56 */     arrayList.add(Integer.valueOf(1));
/*     */     
/*  58 */     if (!"".equals(paramString)) {
/*  59 */       stringBuffer.append(" and a.invoiceCode = ?");
/*  60 */       arrayList.add(paramString);
/*     */     } 
/*     */     
/*  63 */     boolean bool = paramRecordSet4Action.executeQuery(stringBuffer.toString(), arrayList.toArray());
/*  64 */     if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(paramInt))); 
/*  65 */     if (paramRecordSet4Action.next()) {
/*  66 */       return Util.getIntValue(paramRecordSet4Action.getString("maxId"), 0);
/*     */     }
/*  68 */     return 0;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int insertOrUpdateData_for_workflowImport(RecordSetTrans paramRecordSetTrans, FnaInvoiceLedger paramFnaInvoiceLedger, int paramInt, HashMap<String, Object> paramHashMap) throws Exception {
/*  81 */     if (paramFnaInvoiceLedger.getId().intValue() <= 0) {
/*  82 */       insertData(paramRecordSetTrans, paramFnaInvoiceLedger, true, paramInt, paramHashMap);
/*     */     } else {
/*  84 */       updateData(paramRecordSetTrans, paramFnaInvoiceLedger, true, paramInt, paramHashMap);
/*     */     } 
/*  86 */     return paramFnaInvoiceLedger.getId().intValue();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int insertOrUpdateData(RecordSetTrans paramRecordSetTrans, FnaInvoiceLedger paramFnaInvoiceLedger, int paramInt, HashMap<String, Object> paramHashMap) throws Exception {
/*  99 */     if (paramFnaInvoiceLedger.getId().intValue() <= 0) {
/* 100 */       insertData(paramRecordSetTrans, paramFnaInvoiceLedger, false, paramInt, paramHashMap);
/*     */     } else {
/* 102 */       updateData(paramRecordSetTrans, paramFnaInvoiceLedger, false, paramInt, paramHashMap);
/*     */     } 
/* 104 */     return paramFnaInvoiceLedger.getId().intValue();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void verifyFnaInvoiceLedger(FnaInvoiceLedger paramFnaInvoiceLedger, boolean paramBoolean, int paramInt) throws Exception {
/* 115 */     int i = 0;
/* 116 */     if (paramFnaInvoiceLedger.getId() != null) {
/* 117 */       i = paramFnaInvoiceLedger.getId().intValue();
/*     */     }
/* 119 */     verifyInvoiceType(paramFnaInvoiceLedger.getInvoiceType().intValue(), paramInt);
/*     */     
/* 121 */     verifyInvoiceCode(paramFnaInvoiceLedger.getInvoiceCode(), paramFnaInvoiceLedger.getInvoiceType().intValue(), paramInt);
/* 122 */     verifyInvoiceNumber(paramFnaInvoiceLedger.getInvoiceNumber(), paramFnaInvoiceLedger.getInvoiceCode(), i, paramBoolean, paramInt);
/*     */     
/* 124 */     verifyBillingDate(paramFnaInvoiceLedger.getBillingDate(), paramInt);
/* 125 */     verifyAuthenticitye(paramFnaInvoiceLedger.getAuthenticity().intValue(), paramInt);
/*     */     
/* 127 */     verifySeller(paramFnaInvoiceLedger.getSeller(), paramInt);
/* 128 */     verifyPurchaser(paramFnaInvoiceLedger.getPurchaser(), paramInt);
/* 129 */     verifyInvoiceServiceYype(paramFnaInvoiceLedger.getInvoiceServiceYype(), paramInt);
/*     */     
/* 131 */     verifyPriceWithoutTax(paramFnaInvoiceLedger.getPriceWithoutTax(), paramInt);
/* 132 */     verifyTaxRate(paramFnaInvoiceLedger.getTaxRate(), paramInt);
/* 133 */     verifyTax(paramFnaInvoiceLedger.getTax(), paramFnaInvoiceLedger.getTaxRate(), paramFnaInvoiceLedger.getPriceWithoutTax(), paramInt);
/* 134 */     verifyTaxIncludedPrice(paramFnaInvoiceLedger.getTaxIncludedPrice(), paramFnaInvoiceLedger.getPriceWithoutTax(), paramFnaInvoiceLedger.getTax(), paramInt);
/*     */     
/* 136 */     if (paramFnaInvoiceLedger.getRequestId() != null && paramFnaInvoiceLedger.getRequestId().intValue() > 0) {
/* 137 */       verifyRequestid(new String[] { String.valueOf(paramFnaInvoiceLedger.getRequestId()) }, false, paramInt);
/*     */     }
/*     */     
/* 140 */     verifyReimbursementDate(paramFnaInvoiceLedger.getReimbursementDate(), paramInt);
/*     */     
/* 142 */     verifyReimbursePerson(paramFnaInvoiceLedger.getReimbursePerson(), paramInt);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void verifyFnaInvoiceLedger_for_workflowImport(FnaInvoiceLedger paramFnaInvoiceLedger, boolean paramBoolean, int paramInt) throws Exception {
/* 153 */     int i = 0;
/* 154 */     if (paramFnaInvoiceLedger.getId() != null) {
/* 155 */       i = paramFnaInvoiceLedger.getId().intValue();
/*     */     }
/* 157 */     verifyInvoiceType(paramFnaInvoiceLedger.getInvoiceType().intValue(), paramInt);
/*     */     
/* 159 */     verifyInvoiceCode(paramFnaInvoiceLedger.getInvoiceCode(), paramFnaInvoiceLedger.getInvoiceType().intValue(), paramInt);
/* 160 */     verifyInvoiceNumber(paramFnaInvoiceLedger.getInvoiceNumber(), paramFnaInvoiceLedger.getInvoiceCode(), i, paramBoolean, paramInt);
/*     */     
/* 162 */     verifyBillingDate(paramFnaInvoiceLedger.getBillingDate(), paramInt);
/* 163 */     verifyAuthenticitye(paramFnaInvoiceLedger.getAuthenticity().intValue(), paramInt);
/*     */     
/* 165 */     verifyPriceWithoutTax(paramFnaInvoiceLedger.getPriceWithoutTax(), paramInt);
/*     */     
/* 167 */     if (paramFnaInvoiceLedger.getRequestId() != null && paramFnaInvoiceLedger.getRequestId().intValue() > 0) {
/* 168 */       verifyRequestid(new String[] { String.valueOf(paramFnaInvoiceLedger.getRequestId()) }, false, paramInt);
/*     */     } else {
/* 170 */       throw new FnaException("requestId " + SystemEnv.getHtmlLabelName(130807, Util.getIntValue(paramInt)));
/*     */     } 
/*     */     
/* 173 */     verifyReimbursementDate(paramFnaInvoiceLedger.getReimbursementDate(), paramInt);
/*     */     
/* 175 */     verifyReimbursePerson(paramFnaInvoiceLedger.getReimbursePerson(), paramInt);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int insertData(RecordSetTrans paramRecordSetTrans, FnaInvoiceLedger paramFnaInvoiceLedger, boolean paramBoolean, int paramInt, HashMap<String, Object> paramHashMap) throws Exception {
/* 189 */     if (paramFnaInvoiceLedger.getId().intValue() > 0) {
/* 190 */       throw new FnaException(SystemEnv.getHtmlLabelName(130805, Util.getIntValue(paramInt)));
/*     */     }
/*     */     
/* 193 */     if (paramBoolean) {
/* 194 */       verifyFnaInvoiceLedger_for_workflowImport(paramFnaInvoiceLedger, false, paramInt);
/*     */     } else {
/* 196 */       verifyFnaInvoiceLedger(paramFnaInvoiceLedger, false, paramInt);
/*     */     } 
/*     */     
/* 199 */     if (paramFnaInvoiceLedger.getRequestId().intValue() <= 0)
/*     */     {
/* 201 */       paramFnaInvoiceLedger.setRequestId(Integer.valueOf(0));
/*     */     }
/*     */     
/* 204 */     RecordSet4Action recordSet4Action = new RecordSet4Action(paramRecordSetTrans);
/* 205 */     FnaBaseDao fnaBaseDao = new FnaBaseDao();
/* 206 */     fnaBaseDao.saveObject(recordSet4Action, paramFnaInvoiceLedger);
/* 207 */     paramHashMap.put("needRollback", "true");
/*     */     
/* 209 */     int i = queryMaxId(recordSet4Action, paramFnaInvoiceLedger.getInvoiceCode(), paramInt);
/* 210 */     paramFnaInvoiceLedger.setId(Integer.valueOf(i));
/*     */     
/* 212 */     return paramFnaInvoiceLedger.getId().intValue();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void updateData(RecordSetTrans paramRecordSetTrans, FnaInvoiceLedger paramFnaInvoiceLedger, boolean paramBoolean, int paramInt, HashMap<String, Object> paramHashMap) throws Exception {
/* 225 */     if (paramFnaInvoiceLedger.getId().intValue() <= 0) {
/* 226 */       throw new FnaException(SystemEnv.getHtmlLabelName(130806, Util.getIntValue(paramInt)));
/*     */     }
/*     */     
/* 229 */     if (paramBoolean) {
/* 230 */       verifyFnaInvoiceLedger_for_workflowImport(paramFnaInvoiceLedger, true, paramInt);
/*     */     } else {
/* 232 */       verifyFnaInvoiceLedger(paramFnaInvoiceLedger, true, paramInt);
/*     */     } 
/*     */     
/* 235 */     if (paramFnaInvoiceLedger.getRequestId().intValue() <= 0)
/*     */     {
/* 237 */       paramFnaInvoiceLedger.setRequestId(Integer.valueOf(0));
/*     */     }
/*     */     
/* 240 */     RecordSet4Action recordSet4Action = new RecordSet4Action(paramRecordSetTrans);
/* 241 */     FnaBaseDao fnaBaseDao = new FnaBaseDao();
/* 242 */     fnaBaseDao.updateObject(recordSet4Action, paramFnaInvoiceLedger);
/* 243 */     paramHashMap.put("needRollback", "true");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void deleteData_by_requestId(RecordSetTrans paramRecordSetTrans, int paramInt1, int paramInt2, HashMap<String, Object> paramHashMap) throws Exception {
/* 255 */     StringBuffer stringBuffer = new StringBuffer("delete from fnaInvoiceLedger where requestId=?");
/* 256 */     boolean bool = paramRecordSetTrans.executeUpdate(stringBuffer.toString(), new Object[] { Integer.valueOf(paramInt1) });
/* 257 */     if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(paramInt2))); 
/* 258 */     paramHashMap.put("needRollback", "true");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void batchDeleteData(RecordSetTrans paramRecordSetTrans, String[] paramArrayOfString, int paramInt, HashMap<String, Object> paramHashMap) throws Exception {
/* 270 */     int i = paramArrayOfString.length;
/* 271 */     if (i <= 0) {
/* 272 */       throw new FnaException(SystemEnv.getHtmlLabelName(10000019, Util.getIntValue(paramInt)));
/*     */     }
/*     */     
/* 275 */     List<String> list = FnaCommon.initData1(paramArrayOfString);
/* 276 */     int j = list.size();
/*     */ 
/*     */     
/* 279 */     StringBuffer stringBuffer = new StringBuffer("select distinct a.requestid from fnaInvoiceLedger a where (a.requestid is not null and a.requestid > 0) and (1=2");
/* 280 */     for (byte b1 = 0; b1 < j; b1++) {
/* 281 */       stringBuffer.append(" or a.id in (").append(list.get(b1)).append(")");
/*     */     }
/* 283 */     stringBuffer.append(")");
/* 284 */     boolean bool1 = paramRecordSetTrans.executeSql(stringBuffer.toString());
/* 285 */     if (!bool1) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(paramInt))); 
/* 286 */     String[] arrayOfString = new String[paramRecordSetTrans.getCounts()];
/* 287 */     byte b2 = 0;
/* 288 */     while (paramRecordSetTrans.next()) {
/* 289 */       arrayOfString[b2] = Util.null2String(paramRecordSetTrans.getString("requestid")).trim();
/* 290 */       b2++;
/*     */     } 
/* 292 */     verifyRequestid(arrayOfString, true, paramInt);
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 297 */     RecordSet recordSet = new RecordSet();
/* 298 */     String str = "";
/* 299 */     boolean bool2 = false;
/* 300 */     for (b2 = 0; b2 < paramArrayOfString.length; b2++) {
/*     */       
/* 302 */       recordSet.executeQuery("select * from fnainvoiceledger where id=?", new Object[] { paramArrayOfString[b2] });
/* 303 */       if (recordSet.next()) {
/* 304 */         str = recordSet.getString("invoiceType");
/*     */       }
/* 306 */       if ("1".equals(str) || "2".equals(str) || "15".equals(str) || "16".equals(str)) {
/* 307 */         bool2 = recordSet.executeUpdate("delete from fnainvoiceledgerDetail where mainid =?", new Object[] { paramArrayOfString[b2] });
/* 308 */       } else if ("3".equals(str)) {
/* 309 */         bool2 = recordSet.executeUpdate("delete from machineInvoice where mainid =?", new Object[] { paramArrayOfString[b2] });
/* 310 */       } else if ("7".equals(str)) {
/* 311 */         bool2 = recordSet.executeUpdate("delete from taxiInvoice where mainid =?", new Object[] { paramArrayOfString[b2] });
/* 312 */       } else if ("8".equals(str)) {
/* 313 */         bool2 = recordSet.executeUpdate("delete from trainInvoice where mainid =?", new Object[] { paramArrayOfString[b2] });
/* 314 */       } else if ("9".equals(str)) {
/* 315 */         bool2 = recordSet.executeUpdate("delete from tollInvoice where mainid =?", new Object[] { paramArrayOfString[b2] });
/* 316 */       } else if ("10".equals(str)) {
/* 317 */         bool2 = recordSet.executeUpdate("delete from carInvoice where mainid =?", new Object[] { paramArrayOfString[b2] });
/* 318 */       } else if ("11".equals(str)) {
/* 319 */         bool2 = recordSet.executeUpdate("delete from second_carInvoice where mainid =?", new Object[] { paramArrayOfString[b2] });
/* 320 */       } else if ("12".equals(str)) {
/* 321 */         bool2 = recordSet.executeUpdate("delete from motor_VehicleInvoice where mainid =?", new Object[] { paramArrayOfString[b2] });
/* 322 */       } else if ("13".equals(str)) {
/* 323 */         bool2 = recordSet.executeUpdate("delete from smallInvoice where mainid =?", new Object[] { paramArrayOfString[b2] });
/* 324 */       } else if ("14".equals(str)) {
/* 325 */         bool2 = recordSet.executeUpdate("delete from airInvoice where mainid =?", new Object[] { paramArrayOfString[b2] });
/* 326 */         bool2 = recordSet.executeUpdate("delete from airDtlInvoice where mainid =?", new Object[] { paramArrayOfString[b2] });
/*     */       } 
/* 328 */       if (!bool2) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(paramInt))); 
/* 329 */       bool2 = recordSet.executeUpdate("delete from fnainvoiceledger where id =?", new Object[] { paramArrayOfString[b2] });
/* 330 */       if (!bool2) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(paramInt))); 
/*     */     } 
/* 332 */     paramHashMap.put("needRollback", "true");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void verifyRequestid(String[] paramArrayOfString, boolean paramBoolean, int paramInt) throws Exception {
/* 344 */     int i = paramArrayOfString.length;
/* 345 */     if (i > 0) {
/* 346 */       ArrayList<String> arrayList = new ArrayList();
/* 347 */       List<String> list = FnaCommon.initData1(paramArrayOfString);
/* 348 */       int j = list.size();
/* 349 */       RecordSet4Action recordSet4Action = new RecordSet4Action();
/* 350 */       StringBuffer stringBuffer = new StringBuffer("select a.requestid from workflow_requestbase a where (1=2");
/* 351 */       for (byte b1 = 0; b1 < j; b1++) {
/* 352 */         stringBuffer.append(" or a.requestid in (").append(list.get(b1)).append(")");
/*     */       }
/* 354 */       stringBuffer.append(")");
/* 355 */       boolean bool = recordSet4Action.executeQuery(stringBuffer.toString(), new Object[0]);
/* 356 */       if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(paramInt))); 
/* 357 */       while (recordSet4Action.next()) {
/* 358 */         arrayList.add(recordSet4Action.getString("requestid"));
/*     */       }
/* 360 */       for (byte b2 = 0; b2 < i; b2++) {
/* 361 */         String str = paramArrayOfString[b2];
/* 362 */         boolean bool1 = arrayList.contains(str);
/* 363 */         if (paramBoolean) {
/* 364 */           if (bool1) {
/* 365 */             StringBuffer stringBuffer1 = new StringBuffer();
/* 366 */             String str1 = "";
/* 367 */             bool = recordSet4Action.executeQuery("select a.requestName, b.invoiceNumber  from workflow_requestbase a join fnaInvoiceLedger b on a.requestid = b.requestid  where a.requestid = ?", new Object[] { str });
/*     */ 
/*     */             
/* 370 */             if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(paramInt))); 
/* 371 */             while (recordSet4Action.next()) {
/* 372 */               String str2 = Util.null2String(recordSet4Action.getString("invoiceNumber")).trim();
/* 373 */               str1 = Util.null2String(recordSet4Action.getString("requestName")).trim();
/* 374 */               if (stringBuffer1.length() > 0) {
/* 375 */                 stringBuffer1.append(", ");
/*     */               }
/* 377 */               stringBuffer1.append(str2);
/*     */             } 
/*     */             
/* 380 */             throw new FnaException(SystemEnv.getHtmlLabelName(131524, Util.getIntValue(paramInt))
/* 381 */                 .replace("invoiceNumber", stringBuffer1.toString()).replace("requestName", str1));
/*     */           }
/*     */         
/* 384 */         } else if (!bool1) {
/* 385 */           throw new FnaException("【" + str + "】" + SystemEnv.getHtmlLabelName(128462, Util.getIntValue(paramInt)));
/*     */         } 
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void verifyInvoiceNumber(String paramString1, String paramString2, int paramInt1, boolean paramBoolean, int paramInt2) throws Exception {
/* 403 */     if ("".equals(paramString1)) {
/* 404 */       throw new FnaException(SystemEnv.getHtmlLabelName(10000025, Util.getIntValue(paramInt2)));
/*     */     }
/* 406 */     Pattern pattern = Pattern.compile("[0-9]*");
/* 407 */     Matcher matcher = pattern.matcher(paramString1);
/* 408 */     if (!matcher.matches()) {
/* 409 */       throw new FnaException(SystemEnv.getHtmlLabelName(10000026, Util.getIntValue(paramInt2)));
/*     */     }
/* 411 */     if (paramString1.length() != 8) {
/* 412 */       throw new FnaException(SystemEnv.getHtmlLabelName(10000027, Util.getIntValue(paramInt2)) + "！！");
/*     */     }
/* 414 */     RecordSet4Action recordSet4Action = new RecordSet4Action();
/*     */     
/* 416 */     boolean bool1 = recordSet4Action.executeQuery("select count(*) cnt from fnaInvoiceLedger a where invoiceNumber=? and invoiceCode=? and id<>?", new Object[] { paramString1, paramString2, Integer.valueOf(paramInt1) });
/* 417 */     if (!bool1) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(paramInt2))); 
/* 418 */     boolean bool2 = recordSet4Action.next();
/* 419 */     int i = 0;
/* 420 */     if (bool2) {
/* 421 */       i = recordSet4Action.getInt("cnt").intValue();
/*     */     }
/* 423 */     if (i > 0) {
/* 424 */       if (paramBoolean) {
/* 425 */         throw new FnaException(SystemEnv.getHtmlLabelName(131488, Util.getIntValue(paramInt2)) + "【" + paramString2 + "】【" + paramString1 + "】" + SystemEnv.getHtmlLabelName(131555, Util.getIntValue(paramInt2)));
/*     */       }
/* 427 */       throw new FnaException(SystemEnv.getHtmlLabelName(10000028, Util.getIntValue(paramInt2)).replace("#replaceString#", "【" + paramString2 + "】【" + paramString1 + "】"));
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void verifyInvoiceCode(String paramString, int paramInt1, int paramInt2) throws Exception {
/* 443 */     if ("".equals(paramString)) {
/* 444 */       throw new FnaException(SystemEnv.getHtmlLabelName(10000029, Util.getIntValue(paramInt2)));
/*     */     }
/* 446 */     Pattern pattern = Pattern.compile("[0-9]*");
/* 447 */     Matcher matcher = pattern.matcher(paramString);
/* 448 */     if (!matcher.matches()) {
/* 449 */       throw new FnaException(SystemEnv.getHtmlLabelName(10000026, Util.getIntValue(paramInt2)));
/*     */     }
/* 451 */     if (paramString.length() != 10) {
/* 452 */       throw new FnaException(SystemEnv.getHtmlLabelName(10000026, Util.getIntValue(paramInt2)) + "！");
/*     */     }
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 458 */     if (paramInt1 == 1) {
/* 459 */       if (!"6".equals(paramString.substring(7, 8))) {
/* 460 */         throw new FnaException(SystemEnv.getHtmlLabelName(10000026, Util.getIntValue(paramInt2)) + "！！");
/*     */       }
/* 462 */       if (!"0".equals(paramString.substring(9, 10))) {
/* 463 */         throw new FnaException(SystemEnv.getHtmlLabelName(10000026, Util.getIntValue(paramInt2)) + "！！！");
/*     */       }
/* 465 */     } else if (paramInt1 == 2) {
/*     */     
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void verifyAuthenticitye(int paramInt1, int paramInt2) throws Exception {
/* 476 */     if (paramInt1 < 0 || paramInt1 > 2) {
/* 477 */       throw new FnaException(SystemEnv.getHtmlLabelName(10000030, Util.getIntValue(paramInt2)));
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void verifyInvoiceType(int paramInt1, int paramInt2) throws Exception {
/* 488 */     if (paramInt1 < 1 || paramInt1 > 2) {
/* 489 */       throw new FnaException(SystemEnv.getHtmlLabelName(10000031, Util.getIntValue(paramInt2)));
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void verifyBillingDate(String paramString, int paramInt) throws Exception {
/* 500 */     if ("".equals(paramString)) {
/* 501 */       throw new FnaException(SystemEnv.getHtmlLabelName(10000032, Util.getIntValue(paramInt)));
/*     */     }
/* 503 */     if (paramString.length() != 10) {
/* 504 */       throw new FnaException(SystemEnv.getHtmlLabelName(10000033, Util.getIntValue(paramInt)) + "！");
/*     */     }
/* 506 */     if (paramString.compareToIgnoreCase("1500-12-31") < 0) {
/* 507 */       throw new FnaException(SystemEnv.getHtmlLabelName(10000033, Util.getIntValue(paramInt)) + "！！");
/*     */     }
/* 509 */     String str = TimeUtil.getCurrentDateString();
/* 510 */     if (paramString.compareToIgnoreCase(str) > 0) {
/* 511 */       throw new FnaException(SystemEnv.getHtmlLabelName(10000033, Util.getIntValue(paramInt)) + "！！！");
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void verifyPriceWithoutTax(Double paramDouble, int paramInt) throws Exception {
/* 522 */     if (paramDouble == null) {
/* 523 */       throw new FnaException(SystemEnv.getHtmlLabelName(10000034, Util.getIntValue(paramInt)));
/*     */     }
/* 525 */     if (paramDouble.doubleValue() <= 0.0D) {
/* 526 */       throw new FnaException(SystemEnv.getHtmlLabelName(10000035, Util.getIntValue(paramInt)) + "！");
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void verifyTaxRate(Double paramDouble, int paramInt) throws Exception {
/* 537 */     if (paramDouble == null) {
/* 538 */       throw new FnaException(SystemEnv.getHtmlLabelName(10000036, Util.getIntValue(paramInt)));
/*     */     }
/* 540 */     if (paramDouble.doubleValue() < 0.0D) {
/* 541 */       throw new FnaException(SystemEnv.getHtmlLabelName(10000037, Util.getIntValue(paramInt)) + "！");
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void verifyTax(Double paramDouble1, Double paramDouble2, Double paramDouble3, int paramInt) throws Exception {
/* 554 */     if (paramDouble1 == null) {
/* 555 */       throw new FnaException(SystemEnv.getHtmlLabelName(10000038, Util.getIntValue(paramInt)));
/*     */     }
/* 557 */     if (paramDouble1.doubleValue() < 0.0D) {
/* 558 */       throw new FnaException(SystemEnv.getHtmlLabelName(10000039, Util.getIntValue(paramInt)) + "！");
/*     */     }
/* 560 */     if (paramDouble1.doubleValue() != Util.getDoubleValue(this.df2.format(paramDouble3.doubleValue() * paramDouble2.doubleValue() / 100.0D))) {
/* 561 */       throw new FnaException(SystemEnv.getHtmlLabelName(131517, Util.getIntValue(paramInt)) + "！！");
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void verifyTaxIncludedPrice(Double paramDouble1, Double paramDouble2, Double paramDouble3, int paramInt) throws Exception {
/* 574 */     if (paramDouble1 == null) {
/* 575 */       throw new FnaException(SystemEnv.getHtmlLabelName(10000040, Util.getIntValue(paramInt)));
/*     */     }
/* 577 */     if (paramDouble1.doubleValue() <= 0.0D) {
/* 578 */       throw new FnaException(SystemEnv.getHtmlLabelName(10000041, Util.getIntValue(paramInt)) + "！");
/*     */     }
/* 580 */     if (paramDouble1.doubleValue() != Util.getDoubleValue(this.df2.format(paramDouble2.doubleValue() + paramDouble3.doubleValue()))) {
/* 581 */       throw new FnaException(SystemEnv.getHtmlLabelName(131516, Util.getIntValue(paramInt)) + "！！");
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void verifySeller(String paramString, int paramInt) throws Exception {
/* 592 */     if ("".equals(paramString)) {
/* 593 */       throw new FnaException(SystemEnv.getHtmlLabelName(10000042, Util.getIntValue(paramInt)));
/*     */     }
/* 595 */     if (paramString.length() > 500) {
/* 596 */       throw new FnaException(SystemEnv.getHtmlLabelName(10000043, Util.getIntValue(paramInt)) + "：500");
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void verifyPurchaser(String paramString, int paramInt) throws Exception {
/* 607 */     if ("".equals(paramString)) {
/* 608 */       throw new FnaException(SystemEnv.getHtmlLabelName(10000044, Util.getIntValue(paramInt)));
/*     */     }
/* 610 */     if (paramString.length() > 500) {
/* 611 */       throw new FnaException(SystemEnv.getHtmlLabelName(10000045, Util.getIntValue(paramInt)) + "：500");
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void verifyInvoiceServiceYype(String paramString, int paramInt) throws Exception {
/* 622 */     if ("".equals(paramString)) {
/* 623 */       throw new FnaException(SystemEnv.getHtmlLabelName(10000046, Util.getIntValue(paramInt)));
/*     */     }
/* 625 */     if (paramString.length() > 500) {
/* 626 */       throw new FnaException(SystemEnv.getHtmlLabelName(10000047, Util.getIntValue(paramInt)) + "：500");
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void verifyReimbursementDate(String paramString, int paramInt) throws Exception {
/* 637 */     if (paramString == null || "".equals(paramString)) {
/*     */       return;
/*     */     }
/* 640 */     if (paramString.length() != 10) {
/* 641 */       throw new FnaException(SystemEnv.getHtmlLabelName(10000048, Util.getIntValue(paramInt)) + "！");
/*     */     }
/* 643 */     if (paramString.compareToIgnoreCase("1500-12-31") < 0) {
/* 644 */       throw new FnaException(SystemEnv.getHtmlLabelName(10000048, Util.getIntValue(paramInt)) + "！！");
/*     */     }
/* 646 */     String str = TimeUtil.getCurrentDateString();
/* 647 */     if (paramString.compareToIgnoreCase(str) > 0) {
/* 648 */       throw new FnaException(SystemEnv.getHtmlLabelName(10000048, Util.getIntValue(paramInt)) + "！！！");
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void verifyReimbursePerson(Integer paramInteger, int paramInt) throws Exception {
/* 659 */     if (paramInteger == null) {
/*     */       return;
/*     */     }
/* 662 */     if (paramInteger.intValue() < 0) {
/* 663 */       throw new FnaException(SystemEnv.getHtmlLabelName(10000049, Util.getIntValue(paramInt)) + "！");
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private FnaInvoiceLedgerBo() {
/* 682 */     this.df2.setRoundingMode(RoundingMode.HALF_UP);
/*     */   }
/*     */ 
/*     */ 
/*     */   
/* 687 */   private static final FnaInvoiceLedgerBo thisClassObj = new FnaInvoiceLedgerBo();
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static FnaInvoiceLedgerBo getInstance() {
/* 693 */     return thisClassObj;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/fna/e9/bo/base/FnaInvoiceLedgerBo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */