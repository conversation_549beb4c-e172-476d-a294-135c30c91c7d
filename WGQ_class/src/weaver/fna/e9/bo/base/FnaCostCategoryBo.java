/*     */ package weaver.fna.e9.bo.base;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import weaver.conn.RecordSetTrans;
/*     */ import weaver.fna.e9.dao.base.FnaBaseDao;
/*     */ import weaver.fna.e9.exception.FnaException;
/*     */ import weaver.fna.e9.po.base.FnaCostCategory;
/*     */ import weaver.fna.general.FnaCommon;
/*     */ import weaver.fna.general.RecordSet4Action;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class FnaCostCategoryBo
/*     */ {
/*  32 */   BaseBean bb = new BaseBean();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public FnaCostCategory loadFnaCostCategoryId(RecordSet4Action paramRecordSet4Action, int paramInt1, int paramInt2) throws Exception {
/*  43 */     FnaBaseDao fnaBaseDao = new FnaBaseDao();
/*  44 */     FnaCostCategory fnaCostCategory = (FnaCostCategory)fnaBaseDao.queryForObject(paramRecordSet4Action, FnaCostCategory.class.getName(), "select a.* from fnaCostCategory a where a.id = ?", new Object[] { Integer.valueOf(paramInt1) });
/*  45 */     if (fnaCostCategory != null) {
/*  46 */       StringBuffer stringBuffer = new StringBuffer();
/*  47 */       boolean bool = paramRecordSet4Action.executeQuery("select a.workflowId from fnaCostCategoryWf a where a.fnaCostCategoryId = ?", new Object[] { Integer.valueOf(paramInt1) });
/*  48 */       if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(paramInt2))); 
/*  49 */       while (paramRecordSet4Action.next()) {
/*  50 */         int i = paramRecordSet4Action.getInt("workflowId").intValue();
/*  51 */         if (i > 0) {
/*  52 */           if (stringBuffer.length() > 0) {
/*  53 */             stringBuffer.append(",");
/*     */           }
/*  55 */           stringBuffer.append(i);
/*     */         } 
/*     */       } 
/*  58 */       fnaCostCategory.setWorkflowIds(stringBuffer.toString());
/*     */     } 
/*  60 */     return fnaCostCategory;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<Integer> queryWorkflowByFnaCostCategoryId(RecordSet4Action paramRecordSet4Action, int paramInt1, int paramInt2) throws Exception {
/*  72 */     ArrayList<Integer> arrayList = new ArrayList();
/*  73 */     boolean bool = paramRecordSet4Action.executeQuery("select a.workflowId from fnaCostCategoryWf a where a.fnaCostCategoryId = ?", new Object[] { Integer.valueOf(paramInt1) });
/*  74 */     if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(paramInt2))); 
/*  75 */     while (paramRecordSet4Action.next()) {
/*  76 */       int i = paramRecordSet4Action.getInt("workflowId").intValue();
/*  77 */       arrayList.add(Integer.valueOf(i));
/*     */     } 
/*  79 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public double queryMaxShowOrder(RecordSet4Action paramRecordSet4Action, int paramInt) throws Exception {
/*  90 */     boolean bool = paramRecordSet4Action.executeQuery("select max(showOrder) maxShowOrder from fnaCostCategory", new Object[0]);
/*  91 */     if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(paramInt))); 
/*  92 */     if (paramRecordSet4Action.next()) {
/*  93 */       return Util.getDoubleValue(paramRecordSet4Action.getString("maxShowOrder"), 0.0D);
/*     */     }
/*  95 */     return 0.0D;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int createData(RecordSetTrans paramRecordSetTrans, FnaCostCategory paramFnaCostCategory, int paramInt, HashMap<String, Object> paramHashMap) throws Exception {
/* 108 */     if (paramFnaCostCategory.getId().intValue() > 0) {
/* 109 */       throw new FnaException(SystemEnv.getHtmlLabelName(130805, Util.getIntValue(paramInt)));
/*     */     }
/* 111 */     verifyState(paramFnaCostCategory.getState().intValue(), paramInt);
/* 112 */     verifyName(paramFnaCostCategory.getName(), paramFnaCostCategory.getId().intValue(), paramInt);
/* 113 */     verifyCodeName(paramFnaCostCategory.getCodeName(), paramFnaCostCategory.getId().intValue(), paramInt);
/*     */     
/* 115 */     RecordSet4Action recordSet4Action = new RecordSet4Action(paramRecordSetTrans);
/* 116 */     FnaBaseDao fnaBaseDao = new FnaBaseDao();
/* 117 */     fnaBaseDao.saveObject(recordSet4Action, paramFnaCostCategory);
/* 118 */     paramHashMap.put("needRollback", "true");
/*     */     
/* 120 */     boolean bool = recordSet4Action.executeQuery("select max(id) maxId from fnaCostCategory where name=? and codeName=?", new Object[] { paramFnaCostCategory.getName(), paramFnaCostCategory.getCodeName() });
/* 121 */     if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(paramInt))); 
/* 122 */     if (recordSet4Action.next()) {
/* 123 */       paramFnaCostCategory.setId(recordSet4Action.getInt("maxId"));
/*     */       
/* 125 */       saveData_fnaCostCategoryWf(recordSet4Action, paramFnaCostCategory.getId().intValue(), paramFnaCostCategory.getWorkflowIds(), paramInt, paramHashMap);
/*     */     } 
/* 127 */     return paramFnaCostCategory.getId().intValue();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void updateData(RecordSetTrans paramRecordSetTrans, FnaCostCategory paramFnaCostCategory, String[] paramArrayOfString, int paramInt, HashMap<String, Object> paramHashMap) throws Exception {
/* 140 */     if (paramFnaCostCategory.getId().intValue() <= 0) {
/* 141 */       throw new FnaException(SystemEnv.getHtmlLabelName(130806, Util.getIntValue(paramInt)));
/*     */     }
/* 143 */     verifyState(paramFnaCostCategory.getState().intValue(), paramInt);
/* 144 */     verifyName(paramFnaCostCategory.getName(), paramFnaCostCategory.getId().intValue(), paramInt);
/* 145 */     verifyCodeName(paramFnaCostCategory.getCodeName(), paramFnaCostCategory.getId().intValue(), paramInt);
/*     */     
/* 147 */     RecordSet4Action recordSet4Action = new RecordSet4Action(paramRecordSetTrans);
/* 148 */     FnaBaseDao fnaBaseDao = new FnaBaseDao();
/* 149 */     fnaBaseDao.updateObject2(recordSet4Action, paramFnaCostCategory, paramArrayOfString);
/* 150 */     paramHashMap.put("needRollback", "true");
/*     */     
/* 152 */     saveData_fnaCostCategoryWf(recordSet4Action, paramFnaCostCategory.getId().intValue(), paramFnaCostCategory.getWorkflowIds(), paramInt, paramHashMap);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void saveData_fnaCostCategoryWf(RecordSet4Action paramRecordSet4Action, int paramInt1, String paramString, int paramInt2, HashMap<String, Object> paramHashMap) throws Exception {
/* 165 */     verify_fnaCostCategoryWf(paramInt1, paramString, paramInt2);
/*     */     
/* 167 */     boolean bool = paramRecordSet4Action.executeUpdate("delete from fnaCostCategoryWf where fnaCostCategoryId=?", new Object[] { Integer.valueOf(paramInt1) });
/* 168 */     if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(paramInt2))); 
/* 169 */     paramHashMap.put("needRollback", "true");
/* 170 */     if (!"".equals(paramString)) {
/* 171 */       String[] arrayOfString = paramString.split(",");
/* 172 */       int i = arrayOfString.length;
/* 173 */       StringBuffer stringBuffer = new StringBuffer();
/* 174 */       for (byte b = 0; b < i; b++) {
/* 175 */         int j = Util.getIntValue(arrayOfString[b]);
/* 176 */         if (j > 0) {
/* 177 */           if (stringBuffer.length() > 0) {
/* 178 */             stringBuffer.append(" union all \n");
/*     */           }
/* 180 */           stringBuffer.append(" select ");
/* 181 */           stringBuffer.append(paramInt1).append(",");
/* 182 */           stringBuffer.append(j);
/* 183 */           if ("oracle".equals(paramRecordSet4Action.getDBType())) {
/* 184 */             stringBuffer.append(" from dual ");
/*     */           }
/* 186 */           stringBuffer.append(" \r\n");
/*     */         } 
/*     */       } 
/* 189 */       if (stringBuffer.length() > 0) {
/* 190 */         bool = paramRecordSet4Action.executeSql("insert into fnaCostCategoryWf(fnaCostCategoryId,workflowId) \r\n" + stringBuffer.toString());
/* 191 */         if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(paramInt2))); 
/* 192 */         paramHashMap.put("needRollback", "true");
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void deleteData(RecordSetTrans paramRecordSetTrans, String[] paramArrayOfString, int paramInt, HashMap<String, Object> paramHashMap) throws Exception {
/* 206 */     int i = paramArrayOfString.length;
/* 207 */     if (i <= 0) {
/* 208 */       throw new FnaException(SystemEnv.getHtmlLabelName(10000019, Util.getIntValue(paramInt)));
/*     */     }
/*     */ 
/*     */     
/* 212 */     StringBuffer stringBuffer = new StringBuffer("delete from fnaCostCategory where (1=2");
/* 213 */     List<String> list = FnaCommon.initData1(paramArrayOfString);
/* 214 */     int j = list.size();
/* 215 */     for (byte b = 0; b < j; b++) {
/* 216 */       stringBuffer.append(" or id in (").append(list.get(b)).append(")");
/*     */     }
/* 218 */     stringBuffer.append(" )");
/* 219 */     boolean bool = paramRecordSetTrans.executeSql(stringBuffer.toString());
/* 220 */     if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(paramInt))); 
/* 221 */     paramHashMap.put("needRollback", "true");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void changeState(RecordSetTrans paramRecordSetTrans, int paramInt1, int paramInt2, int paramInt3, HashMap<String, Object> paramHashMap) throws Exception {
/* 235 */     verifyState(paramInt2, paramInt3);
/*     */     
/* 237 */     boolean bool = paramRecordSetTrans.executeUpdate("update fnaCostCategory set state=? where id=?", new Object[] { Integer.valueOf(paramInt2), Integer.valueOf(paramInt1) });
/* 238 */     if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(paramInt3))); 
/* 239 */     paramHashMap.put("needRollback", "true");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void verify_fnaCostCategoryWf(int paramInt1, String paramString, int paramInt2) throws Exception {
/* 251 */     if (!"".equals(paramString)) {
/*     */       return;
/*     */     }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 278 */     throw new FnaException(SystemEnv.getHtmlLabelName(10000023, Util.getIntValue(paramInt2)));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void verifyCodeName(String paramString, int paramInt1, int paramInt2) throws Exception {
/* 290 */     if ("".equals(paramString)) {
/* 291 */       throw new FnaException(SystemEnv.getHtmlLabelName(10000020, Util.getIntValue(paramInt2)));
/*     */     }
/* 293 */     RecordSet4Action recordSet4Action = new RecordSet4Action();
/* 294 */     boolean bool = recordSet4Action.executeQuery("select count(*) cnt from fnaCostCategory a where codeName=? and id<>?", new Object[] { paramString, Integer.valueOf(paramInt1) });
/* 295 */     if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(paramInt2))); 
/* 296 */     if (recordSet4Action.next() && recordSet4Action.getInt("cnt").intValue() > 0) {
/* 297 */       throw new FnaException(SystemEnv.getHtmlLabelName(10000021, Util.getIntValue(paramInt2)).replace("#replaceString#", paramString));
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void verifyName(String paramString, int paramInt1, int paramInt2) throws Exception {
/* 309 */     if ("".equals(paramString)) {
/* 310 */       throw new FnaException(SystemEnv.getHtmlLabelName(81968, Util.getIntValue(paramInt2)));
/*     */     }
/* 312 */     RecordSet4Action recordSet4Action = new RecordSet4Action();
/* 313 */     boolean bool = recordSet4Action.executeQuery("select count(*) cnt from fnaCostCategory a where name=? and id<>?", new Object[] { paramString, Integer.valueOf(paramInt1) });
/* 314 */     if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(paramInt2))); 
/* 315 */     if (recordSet4Action.next() && recordSet4Action.getInt("cnt").intValue() > 0) {
/* 316 */       throw new FnaException(SystemEnv.getHtmlLabelName(10000022, Util.getIntValue(paramInt2)).replace("#replaceString#", paramString));
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void verifyState(int paramInt1, int paramInt2) throws Exception {
/* 327 */     if (paramInt1 < 0 && paramInt1 > 1) {
/* 328 */       throw new FnaException(SystemEnv.getHtmlLabelName(130802, Util.getIntValue(paramInt2)));
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/* 348 */   private static final FnaCostCategoryBo thisClassObj = new FnaCostCategoryBo();
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static FnaCostCategoryBo getInstance() {
/* 354 */     return thisClassObj;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/fna/e9/bo/base/FnaCostCategoryBo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */