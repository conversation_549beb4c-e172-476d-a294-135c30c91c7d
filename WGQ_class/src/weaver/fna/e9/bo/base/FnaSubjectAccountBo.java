/*      */ package weaver.fna.e9.bo.base;
/*      */ 
/*      */ import java.io.File;
/*      */ import java.io.FileInputStream;
/*      */ import java.text.DecimalFormat;
/*      */ import java.util.ArrayList;
/*      */ import java.util.HashMap;
/*      */ import java.util.List;
/*      */ import javax.servlet.http.HttpSession;
/*      */ import org.apache.poi.hssf.usermodel.HSSFCell;
/*      */ import org.apache.poi.hssf.usermodel.HSSFRow;
/*      */ import org.apache.poi.hssf.usermodel.HSSFSheet;
/*      */ import org.apache.poi.hssf.usermodel.HSSFWorkbook;
/*      */ import org.apache.poi.poifs.filesystem.POIFSFileSystem;
/*      */ import weaver.conn.RecordSet;
/*      */ import weaver.conn.RecordSetTrans;
/*      */ import weaver.file.ExcelFile;
/*      */ import weaver.file.ExcelRow;
/*      */ import weaver.file.ExcelSheet;
/*      */ import weaver.file.ExcelStyle;
/*      */ import weaver.file.FileManage;
/*      */ import weaver.fna.e9.FnaHelp;
/*      */ import weaver.fna.e9.dao.base.FnaPeriodDao;
/*      */ import weaver.fna.e9.dao.base.FnaSubjectAccountDao;
/*      */ import weaver.fna.e9.dao.base.FnaSubjectBudgetDao;
/*      */ import weaver.fna.e9.exception.FnaException;
/*      */ import weaver.fna.e9.po.base.FnaPeriod;
/*      */ import weaver.fna.e9.po.base.FnaSubjectAccount;
/*      */ import weaver.fna.e9.po.base.FnaSubjectAccountHelp;
/*      */ import weaver.fna.e9.po.base.FnaSubjectBudget;
/*      */ import weaver.fna.general.FnaCommon;
/*      */ import weaver.fna.general.RecordSet4Action;
/*      */ import weaver.general.BaseBean;
/*      */ import weaver.general.GCONST;
/*      */ import weaver.general.ThreadVarLanguage;
/*      */ import weaver.general.Util;
/*      */ import weaver.systeminfo.SystemEnv;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ @Deprecated
/*      */ public class FnaSubjectAccountBo
/*      */ {
/*      */   @Deprecated
/*   58 */   BaseBean bb = new BaseBean();
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   @Deprecated
/*      */   public static String getSubjectSplitStr() {
/*   67 */     return " / ";
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   @Deprecated
/*      */   public void exportAllSubjects(String paramString, ExcelFile paramExcelFile, int paramInt) throws Exception {
/*   79 */     RecordSet4Action recordSet4Action = new RecordSet4Action();
/*   80 */     FnaSubjectAccountHelp fnaSubjectAccountHelp = new FnaSubjectAccountHelp();
/*   81 */     FnaSubjectAccountDao fnaSubjectAccountDao = new FnaSubjectAccountDao();
/*      */ 
/*      */ 
/*      */     
/*   85 */     String str1 = null;
/*      */     
/*   87 */     if (!"".equals(paramString)) {
/*   88 */       FnaPeriodDao fnaPeriodDao = new FnaPeriodDao();
/*   89 */       FnaPeriod fnaPeriod = fnaPeriodDao.find(recordSet4Action, paramString);
/*   90 */       String str = "";
/*   91 */       if (fnaPeriod != null) {
/*   92 */         str = fnaPeriod.getFnaPeriodName();
/*      */       }
/*   94 */       str1 = SystemEnv.getHtmlLabelName(127860, paramInt) + "（" + SystemEnv.getHtmlLabelName(127470, paramInt) + "） " + str;
/*      */     } else {
/*   96 */       str1 = SystemEnv.getHtmlLabelName(64, paramInt) + "（" + SystemEnv.getHtmlLabelName(127470, paramInt) + "）";
/*      */     } 
/*      */ 
/*      */ 
/*      */     
/*  101 */     List<FnaSubjectAccount> list = fnaSubjectAccountDao.queryForList(recordSet4Action, "select a.*  from fnaSubjectAccount a  where a.fnaPeriodPk=?  order by a.subjectaLevel,a.showOrder,a.subjectaCode,a.subjectaName,(a.id*-1)", new Object[] { paramString });
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  106 */     int i = list.size();
/*      */ 
/*      */     
/*  109 */     ExcelStyle excelStyle1 = paramExcelFile.newExcelStyle("Header");
/*  110 */     excelStyle1.setFontcolor(ExcelStyle.RED_Color);
/*  111 */     excelStyle1.setFontbold(ExcelStyle.WeaverHeaderFontbold);
/*  112 */     excelStyle1.setAlign(ExcelStyle.WeaverHeaderAlign);
/*  113 */     excelStyle1.setCellBorder(ExcelStyle.WeaverBorderThin);
/*  114 */     excelStyle1.setFontheight(20);
/*  115 */     excelStyle1.setValign(ExcelStyle.VALIGN_CENTER);
/*  116 */     excelStyle1.setAlign(ExcelStyle.ALIGN_CENTER);
/*      */     
/*  118 */     ExcelStyle excelStyle2 = paramExcelFile.newExcelStyle("Header1");
/*  119 */     excelStyle2.setFontcolor(ExcelStyle.RED_Color);
/*  120 */     excelStyle2.setFontbold(ExcelStyle.WeaverHeaderFontbold);
/*  121 */     excelStyle2.setAlign(ExcelStyle.WeaverHeaderAlign);
/*  122 */     excelStyle2.setCellBorder(ExcelStyle.WeaverBorderThin);
/*  123 */     excelStyle2.setFontheight(12);
/*  124 */     excelStyle2.setValign(ExcelStyle.VALIGN_CENTER);
/*  125 */     excelStyle2.setAlign(ExcelStyle.ALIGN_LEFT);
/*      */     
/*  127 */     ExcelStyle excelStyle3 = paramExcelFile.newExcelStyle("td1");
/*  128 */     excelStyle3.setFontcolor(ExcelStyle.RED_Color);
/*  129 */     excelStyle3.setFontbold(ExcelStyle.WeaverHeaderFontbold);
/*  130 */     excelStyle3.setAlign(ExcelStyle.WeaverHeaderAlign);
/*  131 */     excelStyle3.setCellBorder(ExcelStyle.WeaverBorderThin);
/*  132 */     excelStyle3.setFontheight(11);
/*  133 */     excelStyle3.setValign(ExcelStyle.VALIGN_CENTER);
/*  134 */     excelStyle3.setAlign(ExcelStyle.ALIGN_LEFT);
/*      */     
/*  136 */     ExcelStyle excelStyle4 = paramExcelFile.newExcelStyle("td2");
/*  137 */     excelStyle4.setFontcolor(ExcelStyle.WeaverHeaderFontcolor);
/*  138 */     excelStyle4.setFontbold(ExcelStyle.WeaverHeaderFontbold);
/*  139 */     excelStyle4.setAlign(ExcelStyle.WeaverHeaderAlign);
/*  140 */     excelStyle4.setCellBorder(ExcelStyle.WeaverBorderThin);
/*  141 */     excelStyle4.setFontheight(11);
/*  142 */     excelStyle4.setValign(ExcelStyle.VALIGN_CENTER);
/*  143 */     excelStyle4.setAlign(ExcelStyle.ALIGN_LEFT);
/*      */     
/*  145 */     ExcelStyle excelStyle5 = paramExcelFile.newExcelStyle("td3");
/*  146 */     excelStyle5.setFontcolor(ExcelStyle.WeaverHeaderFontcolor);
/*  147 */     excelStyle5.setAlign(ExcelStyle.WeaverHeaderAlign);
/*  148 */     excelStyle5.setCellBorder(ExcelStyle.WeaverBorderThin);
/*  149 */     excelStyle5.setFontheight(11);
/*  150 */     excelStyle5.setValign(ExcelStyle.VALIGN_CENTER);
/*  151 */     excelStyle5.setAlign(ExcelStyle.ALIGN_LEFT);
/*      */     
/*  153 */     ExcelStyle excelStyle6 = paramExcelFile.newExcelStyle("Header2");
/*  154 */     excelStyle6.setFontcolor(ExcelStyle.WeaverHeaderFontcolor);
/*  155 */     excelStyle6.setFontbold(ExcelStyle.WeaverHeaderFontbold);
/*  156 */     excelStyle6.setAlign(ExcelStyle.WeaverHeaderAlign);
/*  157 */     excelStyle6.setCellBorder(ExcelStyle.WeaverBorderThin);
/*  158 */     excelStyle6.setFontheight(12);
/*  159 */     excelStyle6.setValign(ExcelStyle.VALIGN_CENTER);
/*  160 */     excelStyle6.setAlign(ExcelStyle.ALIGN_LEFT);
/*      */ 
/*      */ 
/*      */     
/*  164 */     String str2 = SystemEnv.getHtmlLabelName(521784, ThreadVarLanguage.getLang());
/*      */     
/*  166 */     ExcelSheet excelSheet1 = new ExcelSheet();
/*  167 */     paramExcelFile.addSheet(str2, excelSheet1);
/*      */     
/*  169 */     excelSheet1.addColumnwidth(5500);
/*  170 */     excelSheet1.addColumnwidth(15000);
/*      */     
/*  172 */     ExcelRow excelRow = excelSheet1.newExcelRow();
/*  173 */     excelRow.addStringValue(SystemEnv.getHtmlLabelName(521784, ThreadVarLanguage.getLang()), "Header", 2);
/*      */     
/*  175 */     excelRow = excelSheet1.newExcelRow();
/*      */ 
/*      */     
/*  178 */     excelRow.addStringValue(SystemEnv.getHtmlLabelName(527709, ThreadVarLanguage.getLang()), "td1");
/*  179 */     excelRow.addStringValue(SystemEnv.getHtmlLabelName(527710, ThreadVarLanguage.getLang()), "td3");
/*  180 */     excelRow.setHight(28);
/*      */     
/*  182 */     excelRow = excelSheet1.newExcelRow();
/*      */ 
/*      */     
/*  185 */     excelRow.addStringValue(SystemEnv.getHtmlLabelName(527709, ThreadVarLanguage.getLang()), "td1");
/*  186 */     excelRow.addStringValue(SystemEnv.getHtmlLabelName(527710, ThreadVarLanguage.getLang()), "td3");
/*  187 */     excelRow.setHight(28);
/*      */     
/*  189 */     excelRow = excelSheet1.newExcelRow();
/*      */ 
/*      */     
/*  192 */     excelRow.addStringValue(SystemEnv.getHtmlLabelName(527711, ThreadVarLanguage.getLang()), "td1");
/*  193 */     excelRow.addStringValue(SystemEnv.getHtmlLabelName(527712, ThreadVarLanguage.getLang()), "td3");
/*  194 */     excelRow.setHight(28);
/*      */     
/*  196 */     excelRow = excelSheet1.newExcelRow();
/*      */ 
/*      */     
/*  199 */     excelRow.addStringValue(SystemEnv.getHtmlLabelName(523502, ThreadVarLanguage.getLang()), "td2");
/*  200 */     excelRow.addStringValue(SystemEnv.getHtmlLabelName(128892, ThreadVarLanguage.getLang()), "td3");
/*  201 */     excelRow.setHight(28);
/*      */     
/*  203 */     excelRow = excelSheet1.newExcelRow();
/*      */ 
/*      */     
/*  206 */     excelRow.addStringValue(SystemEnv.getHtmlLabelName(521056, ThreadVarLanguage.getLang()), "td2");
/*  207 */     excelRow.addStringValue(SystemEnv.getHtmlLabelName(527713, ThreadVarLanguage.getLang()), "td3");
/*  208 */     excelRow.setHight(28);
/*      */     
/*  210 */     excelRow = excelSheet1.newExcelRow();
/*      */ 
/*      */     
/*  213 */     excelRow.addStringValue(SystemEnv.getHtmlLabelName(527372, ThreadVarLanguage.getLang()), "td2");
/*  214 */     excelRow.addStringValue(SystemEnv.getHtmlLabelName(526773, ThreadVarLanguage.getLang()), "td3");
/*  215 */     excelRow.setHight(28);
/*      */ 
/*      */     
/*  218 */     String str3 = SystemEnv.getHtmlLabelName(10003001, ThreadVarLanguage.getLang());
/*  219 */     ExcelSheet excelSheet2 = new ExcelSheet();
/*  220 */     paramExcelFile.addSheet(str3, excelSheet2);
/*      */     
/*  222 */     excelSheet2.addColumnwidth(5500);
/*  223 */     excelSheet2.addColumnwidth(5500);
/*  224 */     excelSheet2.addColumnwidth(5500);
/*  225 */     excelSheet2.addColumnwidth(3500);
/*  226 */     excelSheet2.addColumnwidth(3500);
/*  227 */     excelSheet2.addColumnwidth(25000);
/*      */     
/*  229 */     excelRow = excelSheet2.newExcelRow();
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  236 */     excelRow.addStringValue(SystemEnv.getHtmlLabelName(527709, ThreadVarLanguage.getLang()), "Header1");
/*  237 */     excelRow.addStringValue(SystemEnv.getHtmlLabelName(132177, ThreadVarLanguage.getLang()), "Header1");
/*  238 */     excelRow.addStringValue(SystemEnv.getHtmlLabelName(527711, ThreadVarLanguage.getLang()), "Header1");
/*  239 */     excelRow.addStringValue(SystemEnv.getHtmlLabelName(523502, ThreadVarLanguage.getLang()), "Header2");
/*  240 */     excelRow.addStringValue(SystemEnv.getHtmlLabelName(521056, ThreadVarLanguage.getLang()), "Header2");
/*  241 */     excelRow.addStringValue(SystemEnv.getHtmlLabelName(527372, ThreadVarLanguage.getLang()), "Header2");
/*  242 */     excelRow.setHight(28);
/*      */     
/*  244 */     for (byte b = 0; b < i; b++) {
/*  245 */       FnaSubjectAccount fnaSubjectAccount = list.get(b);
/*      */       
/*  247 */       String str4 = "";
/*  248 */       String str5 = Util.null2String(fnaSubjectAccount.getSupFnaSubjectaPk()).trim();
/*  249 */       if (!"".equals(str5))
/*      */       {
/*  251 */         str4 = fnaSubjectAccountDao.find(recordSet4Action, str5).getSubjectaCode();
/*      */       }
/*      */ 
/*      */       
/*  255 */       String str6 = fnaSubjectAccountHelp.getLockedStatusName(fnaSubjectAccount.getLockedStatus().toString(), paramInt + "");
/*      */       
/*  257 */       excelRow = excelSheet2.newExcelRow();
/*  258 */       excelRow.addStringValue(Util.null2String(fnaSubjectAccount.getSubjectaName()).trim(), "td3");
/*  259 */       excelRow.addStringValue(Util.null2String(fnaSubjectAccount.getSubjectaCode()).trim(), "td3");
/*  260 */       excelRow.addStringValue(str4, "td3");
/*  261 */       excelRow.addStringValue(Util.null2String(str6).trim(), "td3");
/*  262 */       excelRow.addValue(fnaSubjectAccount.getShowOrder().doubleValue(), "td3");
/*  263 */       excelRow.addStringValue(Util.null2String(fnaSubjectAccount.getDescription()).trim(), "td3");
/*  264 */       excelRow.setHight(28);
/*      */     } 
/*      */ 
/*      */     
/*  268 */     paramExcelFile.setFilename(str1);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   @Deprecated
/*      */   public List<FnaSubjectAccount> batchImportFnaSubjectAccount(RecordSetTrans paramRecordSetTrans, String paramString1, String paramString2, int paramInt1, int paramInt2, String paramString3, String paramString4, HttpSession paramHttpSession, int paramInt3) throws Exception {
/*  289 */     RecordSet4Action recordSet4Action = new RecordSet4Action(paramRecordSetTrans);
/*  290 */     boolean bool = true;
/*  291 */     FnaBaseBo fnaBaseBo = FnaBaseBo.getInstance();
/*      */     
/*  293 */     StringBuffer stringBuffer = new StringBuffer();
/*      */     
/*  295 */     String str1 = "\r";
/*      */     
/*  297 */     if (paramInt2 != 0 && paramInt2 != 1)
/*      */     {
/*  299 */       stringBuffer.append(SystemEnv.getHtmlLabelName(127854, paramInt3) + str1);
/*      */     }
/*      */     
/*  302 */     String str2 = paramInt1 + "_" + FnaCommon.getPrimaryKeyGuid1() + ".xls";
/*  303 */     FileInputStream fileInputStream = null;
/*      */     
/*  305 */     String str3 = "";
/*  306 */     String str4 = "";
/*  307 */     String str5 = "";
/*  308 */     String str6 = "select filerealpath, isaesencrypt, aescode from imagefile where imagefileid = " + paramInt1;
/*  309 */     bool = paramRecordSetTrans.executeSql(str6);
/*  310 */     if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(********, Util.getIntValue(paramInt3))); 
/*  311 */     if (paramRecordSetTrans.next()) {
/*  312 */       str3 = paramRecordSetTrans.getString("filerealpath");
/*  313 */       str4 = Util.null2String(paramRecordSetTrans.getString("isaesencrypt"));
/*  314 */       str5 = Util.null2String(paramRecordSetTrans.getString("aescode"));
/*      */     } 
/*      */     
/*  317 */     String str7 = GCONST.getRootPath() + "WEB-INF" + File.separatorChar + "fna" + File.separatorChar + "tmpUpLoad" + File.separatorChar + "";
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  323 */     if ("".equals(str3)) {
/*  324 */       throw new FnaException(SystemEnv.getHtmlLabelName(127851, Util.getIntValue(paramInt3)) + "：" + paramString3);
/*      */     }
/*      */ 
/*      */     
/*  328 */     ArrayList<FnaSubjectAccount> arrayList = new ArrayList();
/*  329 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*      */ 
/*      */     
/*      */     try {
/*  333 */       FileManage.copy(str3, str7, str4, str5);
/*      */ 
/*      */       
/*  336 */       HSSFSheet hSSFSheet = null;
/*      */       
/*  338 */       int k = 0;
/*      */       
/*      */       try {
/*  341 */         fileInputStream = new FileInputStream(str7);
/*      */         
/*  343 */         POIFSFileSystem pOIFSFileSystem = new POIFSFileSystem(fileInputStream);
/*      */         
/*  345 */         HSSFWorkbook hSSFWorkbook = new HSSFWorkbook(pOIFSFileSystem);
/*  346 */         hSSFSheet = hSSFWorkbook.getSheetAt(1);
/*  347 */         k = hSSFSheet.getLastRowNum();
/*  348 */       } catch (Exception exception) {
/*  349 */         this.bb.writeLog(exception);
/*  350 */         throw new FnaException(SystemEnv.getHtmlLabelName(20040, Util.getIntValue(paramInt3)) + "：" + paramString3);
/*      */       } 
/*      */       
/*  353 */       paramHttpSession.setAttribute("index:" + paramString2, "0/" + k + " " + SystemEnv.getHtmlLabelName(34119, paramInt3));
/*      */       
/*  355 */       DecimalFormat decimalFormat = new DecimalFormat("##0.000");
/*      */       
/*  357 */       for (byte b = 1; b < k + 1; b++) {
/*      */         try {
/*  359 */           paramHttpSession.setAttribute("index:" + paramString2, b + "/" + k + " " + SystemEnv.getHtmlLabelName(34119, paramInt3));
/*      */ 
/*      */           
/*  362 */           String str9 = SystemEnv.getHtmlLabelName(125001, paramInt3);
/*      */           
/*  364 */           HSSFRow hSSFRow = hSSFSheet.getRow(b);
/*  365 */           HSSFCell hSSFCell = null;
/*      */           
/*  367 */           hSSFCell = hSSFRow.getCell(0);
/*      */           
/*  369 */           String str10 = Util.null2String(FnaHelp.getCellStringIntValue_4PoiHSSFCell(hSSFCell, SystemEnv.getHtmlLabelName(525928, ThreadVarLanguage.getLang()) + str9)).trim();
/*      */           
/*  371 */           hSSFCell = hSSFRow.getCell(1);
/*      */           
/*  373 */           String str11 = Util.null2String(FnaHelp.getCellStringIntValue_4PoiHSSFCell(hSSFCell, SystemEnv.getHtmlLabelName(10003003, ThreadVarLanguage.getLang()) + str9)).trim();
/*      */ 
/*      */           
/*  376 */           hSSFCell = hSSFRow.getCell(2);
/*      */           
/*  378 */           String str12 = Util.null2String(FnaHelp.getCellStringIntValue_4PoiHSSFCell(hSSFCell, SystemEnv.getHtmlLabelName(10003004, ThreadVarLanguage.getLang()) + str9)).trim();
/*      */           
/*  380 */           hSSFCell = hSSFRow.getCell(3);
/*      */           
/*  382 */           String str13 = Util.null2String(FnaHelp.getCellStringIntValue_4PoiHSSFCell(hSSFCell, SystemEnv.getHtmlLabelName(10002611, ThreadVarLanguage.getLang()) + str9)).trim();
/*      */           
/*  384 */           hSSFCell = hSSFRow.getCell(4);
/*  385 */           double d = Util.getDoubleValue(FnaHelp.getCellDoubleValue_4PoiHSSFCell(hSSFCell, decimalFormat), 0.0D);
/*      */           
/*  387 */           hSSFCell = hSSFRow.getCell(5);
/*      */           
/*  389 */           String str14 = Util.null2String(FnaHelp.getCellStringIntValue_4PoiHSSFCell(hSSFCell, SystemEnv.getHtmlLabelName(10003006, ThreadVarLanguage.getLang()) + str9)).trim();
/*      */           
/*  391 */           Integer integer = null;
/*      */           
/*  393 */           if ("".equals(str13) || SystemEnv.getHtmlLabelName(25456, ThreadVarLanguage.getLang()).equals(str13)) {
/*  394 */             integer = Integer.valueOf(0);
/*      */           }
/*  396 */           else if (SystemEnv.getHtmlLabelName(22205, ThreadVarLanguage.getLang()).equals(str13)) {
/*  397 */             integer = Integer.valueOf(1);
/*      */           } else {
/*      */             
/*  400 */             stringBuffer.append(str10 + "(" + str11 + ")：" + SystemEnv.getHtmlLabelName(********, Util.getIntValue(paramInt3)) + "：" + SystemEnv.getHtmlLabelName(********, ThreadVarLanguage.getLang()) + str1);
/*      */           } 
/*      */ 
/*      */           
/*  404 */           FnaSubjectAccount fnaSubjectAccount = new FnaSubjectAccount();
/*  405 */           arrayList.add(fnaSubjectAccount);
/*      */           
/*  407 */           fnaSubjectAccount.setFnaPeriodPk(paramString1);
/*  408 */           fnaSubjectAccount.setSubjectaName(str10);
/*  409 */           fnaSubjectAccount.setSubjectaCode(str11);
/*  410 */           fnaSubjectAccount.setSupFnaSubjectaPk(null);
/*  411 */           fnaSubjectAccount.setLockedStatus(integer);
/*  412 */           fnaSubjectAccount.setShowOrder(Double.valueOf(d));
/*  413 */           fnaSubjectAccount.setDescription(str14);
/*  414 */           fnaSubjectAccount.setSubjectaIsLeaf(Integer.valueOf(0));
/*  415 */           fnaSubjectAccount.setSubjectaLevel(Integer.valueOf(0));
/*      */ 
/*      */           
/*  418 */           hashMap.put("fnaPeriodPk:" + paramString1 + "_subjectaCode:" + str11 + "_supFnaSubjectaCode", str12);
/*      */         }
/*  420 */         catch (Exception exception) {
/*  421 */           stringBuffer.append(exception.getMessage() + str1);
/*      */         } 
/*      */       } 
/*      */     } finally {
/*      */       
/*      */       try {
/*  427 */         if (fileInputStream != null) {
/*  428 */           fileInputStream.close();
/*      */         }
/*  430 */       } catch (Exception exception) {}
/*      */       try {
/*  432 */         if (paramInt1 > 0 && str2 != null && !"".equals(str2) && str7 != null && !"".equals(str7)) {
/*  433 */           FileManage.DeleteFile(str7);
/*      */         }
/*  435 */       } catch (Exception exception) {}
/*      */     } 
/*      */ 
/*      */     
/*  439 */     if (stringBuffer.length() > 0) {
/*  440 */       throw new FnaException(stringBuffer.toString());
/*      */     }
/*      */ 
/*      */     
/*  444 */     int i = arrayList.size();
/*      */     
/*  446 */     if (i <= 0) {
/*  447 */       throw new FnaException(SystemEnv.getHtmlLabelName(84660, paramInt3));
/*      */     }
/*      */     
/*  450 */     FnaSubjectAccountDao fnaSubjectAccountDao = new FnaSubjectAccountDao();
/*      */     
/*      */     byte b1;
/*  453 */     for (b1 = 0; b1 < i; b1++) {
/*      */       try {
/*  455 */         paramHttpSession.setAttribute("index:" + paramString2, b1 + "/" + i + " " + SystemEnv.getHtmlLabelName(25649, paramInt3));
/*      */ 
/*      */         
/*  458 */         boolean bool1 = false;
/*      */         
/*  460 */         FnaSubjectAccount fnaSubjectAccount1 = arrayList.get(b1);
/*      */         
/*  462 */         FnaSubjectAccount fnaSubjectAccount2 = (FnaSubjectAccount)fnaSubjectAccountDao.queryForObject(recordSet4Action, FnaSubjectAccount.class
/*  463 */             .getName(), "select * from fnaSubjectAccount where fnaPeriodPk=? and subjectaCode=?", new Object[] { fnaSubjectAccount1
/*      */               
/*  465 */               .getFnaPeriodPk(), fnaSubjectAccount1.getSubjectaCode() });
/*  466 */         if (fnaSubjectAccount2 == null) {
/*  467 */           bool1 = true;
/*      */         
/*      */         }
/*  470 */         else if (paramInt2 == 0) {
/*  471 */           stringBuffer.append(fnaSubjectAccount1.getSubjectaName() + "(" + fnaSubjectAccount1.getSubjectaCode() + ")：" + 
/*  472 */               SystemEnv.getHtmlLabelName(127826, paramInt3) + str1);
/*      */         }
/*  474 */         else if (paramInt2 == 1) {
/*  475 */           bool1 = true;
/*      */ 
/*      */           
/*  478 */           fnaSubjectAccount1.setFnaSubjectaPk(fnaSubjectAccount2.getFnaSubjectaPk());
/*  479 */           fnaSubjectAccount1.setSubjectaIsLeaf(fnaSubjectAccount2.getSubjectaIsLeaf());
/*  480 */           fnaSubjectAccount1.setSubjectaLevel(fnaSubjectAccount2.getSubjectaLevel());
/*      */           
/*  482 */           String str = fnaSubjectAccount1.getSubjectaCode();
/*      */ 
/*      */           
/*  485 */           hashMap.put("fnaPeriodPk:" + paramString1 + "_subjectaCode:" + str + "_old_supFnaSubjectaPk", fnaSubjectAccount2.getSupFnaSubjectaPk());
/*      */         } 
/*      */ 
/*      */ 
/*      */         
/*  490 */         if (bool1) {
/*  491 */           saveAndUpdateFnaSubjectAccount(paramRecordSetTrans, fnaSubjectAccount1, "", false, paramInt3);
/*      */         }
/*      */       }
/*  494 */       catch (Exception exception) {
/*  495 */         stringBuffer.append(exception.getMessage() + str1);
/*      */       } 
/*      */     } 
/*      */ 
/*      */     
/*  500 */     if (stringBuffer.length() > 0) {
/*  501 */       throw new FnaException(stringBuffer.toString());
/*      */     }
/*      */ 
/*      */     
/*  505 */     for (b1 = 0; b1 < i; b1++) {
/*      */       try {
/*  507 */         paramHttpSession.setAttribute("index:" + paramString2, b1 + "/" + i + " " + SystemEnv.getHtmlLabelName(127857, paramInt3));
/*      */         
/*  509 */         FnaSubjectAccount fnaSubjectAccount = arrayList.get(b1);
/*  510 */         String str9 = fnaSubjectAccount.getSubjectaCode();
/*  511 */         String str10 = (String)hashMap.get("fnaPeriodPk:" + paramString1 + "_subjectaCode:" + str9 + "_supFnaSubjectaCode");
/*      */ 
/*      */         
/*  514 */         String str11 = null;
/*  515 */         if (!"".equals(str10)) {
/*  516 */           bool = paramRecordSetTrans.executeQuery("select fnaSubjectaPk from fnaSubjectAccount where fnaPeriodPk=? and subjectaCode=?", new Object[] { paramString1, str10 });
/*      */           
/*  518 */           if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(********, Util.getIntValue(paramInt3))); 
/*  519 */           if (paramRecordSetTrans.next()) {
/*  520 */             str11 = Util.null2String(paramRecordSetTrans.getString("fnaSubjectaPk")).trim();
/*      */           } else {
/*  522 */             String str = fnaSubjectAccount.getSubjectaName();
/*  523 */             stringBuffer.append(str + "(" + str9 + ")：" + SystemEnv.getHtmlLabelName(********, Util.getIntValue(paramInt3)) + str1);
/*      */           } 
/*      */         } 
/*  526 */         fnaSubjectAccount.setSupFnaSubjectaPk(str11);
/*      */ 
/*      */         
/*  529 */         if (fnaBaseBo.nonEmptyCheck(str11))
/*      */         {
/*  531 */           saveAndUpdateFnaSubjectAccount(paramRecordSetTrans, fnaSubjectAccount, "", false, paramInt3);
/*      */         }
/*      */       }
/*  534 */       catch (Exception exception) {
/*  535 */         stringBuffer.append(exception.getMessage() + str1);
/*      */       } 
/*      */     } 
/*      */ 
/*      */     
/*  540 */     if (stringBuffer.length() > 0) {
/*  541 */       throw new FnaException(stringBuffer.toString());
/*      */     }
/*      */ 
/*      */     
/*  545 */     String str8 = paramRecordSetTrans.getDBType();
/*      */ 
/*      */     
/*  548 */     ArrayList<String> arrayList1 = new ArrayList();
/*      */ 
/*      */     
/*  551 */     ArrayList<String> arrayList2 = new ArrayList();
/*      */     int j;
/*  553 */     for (j = 0; j < i; j++) {
/*      */       try {
/*  555 */         paramHttpSession.setAttribute("index:" + paramString2, j + "/" + i + " " + SystemEnv.getHtmlLabelName(127858, paramInt3));
/*      */         
/*  557 */         FnaSubjectAccount fnaSubjectAccount = arrayList.get(j);
/*  558 */         String str9 = fnaSubjectAccount.getSubjectaCode();
/*  559 */         String str10 = fnaSubjectAccount.getFnaSubjectaPk();
/*  560 */         String str11 = fnaSubjectAccount.getSupFnaSubjectaPk();
/*  561 */         String str12 = (String)hashMap.get("fnaPeriodPk:" + paramString1 + "_subjectaCode:" + str9 + "_old_supFnaSubjectaPk");
/*      */         
/*  563 */         if (!arrayList2.contains(str10)) {
/*      */           
/*  565 */           calculateSubjectaLevel(paramRecordSetTrans, str10, paramInt3);
/*  566 */           arrayList2.add(str10);
/*      */         } 
/*      */         
/*  569 */         if (str11 != null && !"".equals(str11) && !arrayList2.contains(str11)) {
/*      */           
/*  571 */           calculateSubjectaLevel(paramRecordSetTrans, str11, paramInt3);
/*  572 */           arrayList2.add(str11);
/*      */         } 
/*      */         
/*  575 */         if (str12 != null && !"".equals(str12) && !arrayList2.contains(str12)) {
/*      */           
/*  577 */           calculateSubjectaLevel(paramRecordSetTrans, str12, paramInt3);
/*  578 */           arrayList2.add(str12);
/*      */         } 
/*      */ 
/*      */         
/*  582 */         String str13 = "";
/*  583 */         if ("oracle".equals(str8)) {
/*  584 */           str13 = "select a.fnaSubjectaPk \n from fnaSubjectAccount a \n where a.fnasubjectapk <> ? \n start with a.fnaSubjectaPk = ? \n connect by prior a.fnaSubjectaPk = a.supFnaSubjectaPk";
/*      */ 
/*      */ 
/*      */         
/*      */         }
/*  589 */         else if ("postgresql".equals(str8)) {
/*  590 */           str13 = "WITH RECURSIVE  allsub(fnaSubjectaPk,subjectaName,supFnaSubjectaPk)\n as (\n   SELECT fnaSubjectaPk,subjectaName,supFnaSubjectaPk \n   FROM fnaSubjectAccount \n   where fnaSubjectaPk = ? \n   UNION ALL \n   SELECT a.fnaSubjectaPk,a.subjectaName,a.supFnaSubjectaPk \n   FROM fnaSubjectAccount a,allsub b \n   where a.supFnaSubjectaPk = b.fnaSubjectaPk\n ) select * from allsub\n where fnaSubjectaPk <> ? ";
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */         
/*      */         }
/*      */         else {
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */           
/*  603 */           str13 = "WITH allsub(fnaSubjectaPk,subjectaName,supFnaSubjectaPk)\n as (\n   SELECT fnaSubjectaPk,subjectaName,supFnaSubjectaPk \n   FROM fnaSubjectAccount \n   where fnaSubjectaPk = ? \n   UNION ALL \n   SELECT a.fnaSubjectaPk,a.subjectaName,a.supFnaSubjectaPk \n   FROM fnaSubjectAccount a,allsub b \n   where a.supFnaSubjectaPk = b.fnaSubjectaPk\n ) select * from allsub\n where fnaSubjectaPk <> ? ";
/*      */         } 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */         
/*  615 */         bool = paramRecordSetTrans.executeQuery(str13, new Object[] { str10, str10 });
/*  616 */         if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(********, Util.getIntValue(paramInt3))); 
/*  617 */         while (paramRecordSetTrans.next()) {
/*  618 */           String str = Util.null2String(paramRecordSetTrans.getString("fnaSubjectaPk"));
/*  619 */           if (!arrayList2.contains(str) && !arrayList1.contains(str)) {
/*  620 */             arrayList1.add(str);
/*      */           }
/*      */         }
/*      */       
/*  624 */       } catch (Exception exception) {
/*  625 */         stringBuffer.append(exception.getMessage() + str1);
/*      */       } 
/*      */     } 
/*      */ 
/*      */     
/*  630 */     if (stringBuffer.length() > 0) {
/*  631 */       throw new FnaException(stringBuffer.toString());
/*      */     }
/*      */     
/*  634 */     j = arrayList1.size();
/*      */     
/*  636 */     for (byte b2 = 0; b2 < j; b2++) {
/*      */       try {
/*  638 */         paramHttpSession.setAttribute("index:" + paramString2, b2 + "/" + j + " " + SystemEnv.getHtmlLabelName(127859, paramInt3));
/*      */         
/*  640 */         String str = arrayList1.get(b2);
/*      */         
/*  642 */         if (str != null && !"".equals(str) && !arrayList2.contains(str))
/*      */         {
/*  644 */           calculateSubjectaLevel(paramRecordSetTrans, str, paramInt3);
/*  645 */           arrayList2.add(str);
/*      */         }
/*      */       
/*  648 */       } catch (Exception exception) {
/*  649 */         stringBuffer.append(exception.getMessage());
/*      */       } 
/*      */     } 
/*      */ 
/*      */     
/*  654 */     if (stringBuffer.length() > 0) {
/*  655 */       throw new FnaException(stringBuffer.toString());
/*      */     }
/*      */     
/*  658 */     return arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   @Deprecated
/*      */   public void initializeSubjectAccountFromFnaPeriodA2FnaPeriodB(RecordSetTrans paramRecordSetTrans, String paramString1, String paramString2, List<FnaSubjectAccount> paramList1, List<FnaSubjectAccount> paramList2, HashMap<String, String> paramHashMap, int paramInt) throws Exception {
/*  677 */     RecordSet4Action recordSet4Action = new RecordSet4Action(paramRecordSetTrans);
/*  678 */     boolean bool = true;
/*  679 */     FnaBaseBo fnaBaseBo = FnaBaseBo.getInstance();
/*  680 */     if (!fnaBaseBo.nonEmptyCheck(paramString1)) {
/*  681 */       throw new FnaException(SystemEnv.getHtmlLabelName(********, Util.getIntValue(paramInt)));
/*      */     }
/*  683 */     if (!fnaBaseBo.nonEmptyCheck(paramString2)) {
/*  684 */       throw new FnaException(SystemEnv.getHtmlLabelName(********, Util.getIntValue(paramInt)));
/*      */     }
/*      */     
/*  687 */     FnaSubjectAccountDao fnaSubjectAccountDao = new FnaSubjectAccountDao();
/*      */ 
/*      */     
/*  690 */     bool = paramRecordSetTrans.executeUpdate("delete from fnaSubjectAccount where fnaPeriodPk=?", new Object[] { paramString2 });
/*  691 */     if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(********, Util.getIntValue(paramInt)));
/*      */ 
/*      */     
/*  694 */     List<FnaSubjectAccount> list = fnaSubjectAccountDao.queryForList(recordSet4Action, "select * \n from fnaSubjectAccount a \n where a.fnaPeriodPk = ? \n order by a.subjectaLevel asc", new Object[] { paramString1 });
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  699 */     int i = list.size();
/*  700 */     for (byte b = 0; b < i; b++) {
/*  701 */       FnaSubjectAccount fnaSubjectAccount1 = list.get(b);
/*      */ 
/*      */       
/*  704 */       FnaSubjectAccount fnaSubjectAccount2 = fnaSubjectAccount1.copy();
/*      */       
/*  706 */       paramList1.add(fnaSubjectAccount2);
/*      */       
/*  708 */       String str1 = fnaSubjectAccount1.getFnaSubjectaPk();
/*  709 */       String str2 = FnaCommon.getPrimaryKeyGuid1();
/*      */       
/*  711 */       paramHashMap.put(str1, str2);
/*      */       
/*  713 */       fnaSubjectAccount1.setFnaSubjectaPk(str2);
/*  714 */       fnaSubjectAccount1.setFnaPeriodPk(paramString2);
/*      */ 
/*      */       
/*  717 */       String str3 = Util.null2String(fnaSubjectAccount1.getSupFnaSubjectaPk()).trim();
/*  718 */       fnaSubjectAccount1.setSupFnaSubjectaPk(null);
/*  719 */       if (!"".equals(str3) && paramHashMap.containsKey(str3)) {
/*  720 */         fnaSubjectAccount1.setSupFnaSubjectaPk(paramHashMap.get(str3));
/*      */       }
/*      */       
/*  723 */       fnaSubjectAccountDao.save(recordSet4Action, fnaSubjectAccount1);
/*  724 */       paramList2.add(fnaSubjectAccount1);
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   @Deprecated
/*      */   public void saveAndUpdateFnaSubjectAccount(RecordSetTrans paramRecordSetTrans, FnaSubjectAccount paramFnaSubjectAccount, String paramString, int paramInt) throws Exception {
/*  739 */     saveAndUpdateFnaSubjectAccount(paramRecordSetTrans, paramFnaSubjectAccount, paramString, true, paramInt);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   @Deprecated
/*      */   public void saveAndUpdateFnaSubjectAccount(RecordSetTrans paramRecordSetTrans, FnaSubjectAccount paramFnaSubjectAccount, String paramString, boolean paramBoolean, int paramInt) throws Exception {
/*  753 */     RecordSet4Action recordSet4Action = new RecordSet4Action(paramRecordSetTrans);
/*  754 */     boolean bool = true;
/*  755 */     FnaBaseBo fnaBaseBo = FnaBaseBo.getInstance();
/*  756 */     if (!fnaBaseBo.nonEmptyCheck(paramFnaSubjectAccount.getFnaPeriodPk())) {
/*  757 */       throw new FnaException(SystemEnv.getHtmlLabelName(********, Util.getIntValue(paramInt)));
/*      */     }
/*  759 */     if (!fnaBaseBo.nonEmptyCheck(paramFnaSubjectAccount.getSubjectaName())) {
/*  760 */       throw new FnaException(SystemEnv.getHtmlLabelName(********, Util.getIntValue(paramInt)));
/*      */     }
/*  762 */     if (!fnaBaseBo.nonEmptyCheck(paramFnaSubjectAccount.getSubjectaCode())) {
/*  763 */       throw new FnaException(SystemEnv.getHtmlLabelName(********, Util.getIntValue(paramInt)));
/*      */     }
/*  765 */     if (!fnaBaseBo.nonNullCheck(paramFnaSubjectAccount.getLockedStatus())) {
/*  766 */       throw new FnaException(SystemEnv.getHtmlLabelName(********, Util.getIntValue(paramInt)));
/*      */     }
/*  768 */     if (!fnaBaseBo.nonNullCheck(paramFnaSubjectAccount.getShowOrder())) {
/*  769 */       throw new FnaException(SystemEnv.getHtmlLabelName(********, Util.getIntValue(paramInt)));
/*      */     }
/*      */     
/*  772 */     if (!fnaBaseBo.nonEmptyCheck(paramFnaSubjectAccount.getSupFnaSubjectaPk())) {
/*  773 */       paramFnaSubjectAccount.setSupFnaSubjectaPk(null);
/*      */     }
/*      */     
/*  776 */     paramFnaSubjectAccount.setSubjectaIsLeaf(Integer.valueOf(0));
/*  777 */     paramFnaSubjectAccount.setSubjectaLevel(Integer.valueOf(0));
/*      */     
/*  779 */     if (fnaBaseBo.nonEmptyCheck(paramFnaSubjectAccount.getFnaSubjectaPk()) && fnaBaseBo.nonEmptyCheck(paramFnaSubjectAccount.getSupFnaSubjectaPk()) && paramFnaSubjectAccount
/*  780 */       .getFnaSubjectaPk().equals(paramFnaSubjectAccount.getSupFnaSubjectaPk())) {
/*  781 */       throw new FnaException(paramFnaSubjectAccount.getSubjectaName() + "(" + paramFnaSubjectAccount.getSubjectaCode() + "):" + SystemEnv.getHtmlLabelName(127856, paramInt));
/*      */     }
/*      */ 
/*      */     
/*  785 */     paramRecordSetTrans.executeQuery("select count(*) cnt from fnaSubjectAccount where fnaSubjectaPk<>? and fnaPeriodPk=? and subjectaCode=?", new Object[] { paramFnaSubjectAccount
/*  786 */           .getFnaSubjectaPk(), paramFnaSubjectAccount.getFnaPeriodPk(), paramFnaSubjectAccount.getSubjectaCode() });
/*  787 */     if (paramRecordSetTrans.next() && paramRecordSetTrans.getInt("cnt") > 0) {
/*  788 */       throw new FnaException(SystemEnv.getHtmlLabelName(********, Util.getIntValue(paramInt)));
/*      */     }
/*      */ 
/*      */     
/*  792 */     FnaSubjectAccountDao fnaSubjectAccountDao = new FnaSubjectAccountDao();
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  797 */     if (fnaBaseBo.nonEmptyCheck(paramFnaSubjectAccount.getSupFnaSubjectaPk()) && paramFnaSubjectAccount.getLockedStatus().intValue() == 0) {
/*  798 */       FnaSubjectAccount fnaSubjectAccount = fnaSubjectAccountDao.find(recordSet4Action, paramFnaSubjectAccount.getSupFnaSubjectaPk());
/*  799 */       if (fnaSubjectAccount.getLockedStatus().intValue() == 1) {
/*  800 */         throw new FnaException(SystemEnv.getHtmlLabelName(127855, paramInt));
/*      */       }
/*      */     } 
/*      */ 
/*      */     
/*  805 */     boolean bool1 = !fnaBaseBo.nonEmptyCheck(paramFnaSubjectAccount.getFnaSubjectaPk()) ? true : false;
/*      */     
/*  807 */     String str1 = "";
/*      */     
/*  809 */     String str2 = "";
/*  810 */     if (bool1) {
/*      */       
/*  812 */       str2 = FnaCommon.getPrimaryKeyGuid1();
/*  813 */       paramFnaSubjectAccount.setFnaSubjectaPk(str2);
/*  814 */       fnaSubjectAccountDao.save(recordSet4Action, paramFnaSubjectAccount);
/*      */     } else {
/*  816 */       str2 = paramFnaSubjectAccount.getFnaSubjectaPk();
/*      */       
/*  818 */       FnaSubjectAccount fnaSubjectAccount = fnaSubjectAccountDao.find(recordSet4Action, str2);
/*  819 */       str1 = fnaSubjectAccount.getSupFnaSubjectaPk();
/*      */       
/*  821 */       paramFnaSubjectAccount.setSubjectaIsLeaf(fnaSubjectAccount.getSubjectaIsLeaf());
/*  822 */       paramFnaSubjectAccount.setSubjectaLevel(fnaSubjectAccount.getSubjectaLevel());
/*      */       
/*  824 */       fnaSubjectAccountDao.update(recordSet4Action, paramFnaSubjectAccount);
/*      */     } 
/*      */ 
/*      */     
/*  828 */     bool = paramRecordSetTrans.executeUpdate("update fnaSubjectBudget set fnaSubjectaPk=null where fnaSubjectaPk=?", new Object[] { str2 });
/*  829 */     if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(********, Util.getIntValue(paramInt)));
/*      */     
/*  831 */     if (!"".equals(paramString)) {
/*  832 */       String[] arrayOfString = paramString.split(",");
/*  833 */       int i = arrayOfString.length;
/*  834 */       for (byte b = 0; b < i; b++) {
/*  835 */         String str = arrayOfString[b];
/*      */         
/*  837 */         bool = paramRecordSetTrans.executeUpdate("update fnaSubjectBudget set fnaSubjectaPk=? where fnaSubjectbPk=?", new Object[] { str2, str });
/*  838 */         if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(********, Util.getIntValue(paramInt)));
/*      */       
/*      */       } 
/*      */     } 
/*      */     
/*  843 */     String str3 = paramRecordSetTrans.getDBType();
/*      */     
/*  845 */     if (paramBoolean) {
/*      */       
/*  847 */       ArrayList<String> arrayList = new ArrayList();
/*  848 */       String str = "";
/*  849 */       if ("oracle".equals(str3)) {
/*  850 */         str = "select a.fnaSubjectaPk \n from fnaSubjectAccount a \n where a.fnasubjectapk <> ? \n start with a.fnaSubjectaPk = ? \n connect by prior a.fnaSubjectaPk = a.supFnaSubjectaPk";
/*      */ 
/*      */ 
/*      */       
/*      */       }
/*  855 */       else if ("postgresql".equals(str3)) {
/*      */         
/*  857 */         str = "WITH RECURSIVE  allsub(fnaSubjectaPk,subjectaName,supFnaSubjectaPk)\n as (\n   SELECT fnaSubjectaPk,subjectaName,supFnaSubjectaPk \n   FROM fnaSubjectAccount \n   where fnaSubjectaPk = ? \n   UNION ALL \n   SELECT a.fnaSubjectaPk,a.subjectaName,a.supFnaSubjectaPk \n   FROM fnaSubjectAccount a,allsub b \n   where a.supFnaSubjectaPk = b.fnaSubjectaPk\n ) select * from allsub\n where fnaSubjectaPk <> ? ";
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*      */       }
/*      */       else {
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */         
/*  870 */         str = "WITH allsub(fnaSubjectaPk,subjectaName,supFnaSubjectaPk)\n as (\n   SELECT fnaSubjectaPk,subjectaName,supFnaSubjectaPk \n   FROM fnaSubjectAccount \n   where fnaSubjectaPk = ? \n   UNION ALL \n   SELECT a.fnaSubjectaPk,a.subjectaName,a.supFnaSubjectaPk \n   FROM fnaSubjectAccount a,allsub b \n   where a.supFnaSubjectaPk = b.fnaSubjectaPk\n ) select * from allsub\n where fnaSubjectaPk <> ? ";
/*      */       } 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*  882 */       bool = paramRecordSetTrans.executeQuery(str, new Object[] { str2, str2 });
/*  883 */       if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(********, Util.getIntValue(paramInt))); 
/*  884 */       while (paramRecordSetTrans.next()) {
/*  885 */         arrayList.add(Util.null2String(paramRecordSetTrans.getString("fnaSubjectaPk")));
/*      */       }
/*  887 */       int i = arrayList.size(); byte b;
/*  888 */       for (b = 0; b < i; b++) {
/*  889 */         String str4 = arrayList.get(b);
/*      */         
/*  891 */         str = "update fnaSubjectAccount set lockedStatus=? where fnaSubjectaPk=?";
/*  892 */         bool = paramRecordSetTrans.executeUpdate(str, new Object[] { paramFnaSubjectAccount.getLockedStatus(), str4 });
/*  893 */         if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(********, Util.getIntValue(paramInt)));
/*      */ 
/*      */         
/*  896 */         calculateSubjectaLevel(paramRecordSetTrans, str4, paramInt);
/*      */       } 
/*  898 */       b = (arrayList.size() > 0) ? 0 : 1;
/*      */ 
/*      */       
/*  901 */       calculateSubjectaLevel(paramRecordSetTrans, str2, b, (paramFnaSubjectAccount.getSupFnaSubjectaPk() == null) ? 1 : -1, paramInt);
/*      */ 
/*      */       
/*  904 */       calculateSubjectaLevel(paramRecordSetTrans, paramFnaSubjectAccount.getSupFnaSubjectaPk(), paramInt);
/*      */ 
/*      */       
/*  907 */       calculateSubjectaLevel(paramRecordSetTrans, str1, paramInt);
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   @Deprecated
/*      */   public void calculateSubjectaLevel(RecordSetTrans paramRecordSetTrans, String paramString, int paramInt) throws Exception {
/*  920 */     calculateSubjectaLevel(paramRecordSetTrans, paramString, -1, -1, paramInt);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   @Deprecated
/*      */   public void calculateSubjectaLevel(RecordSetTrans paramRecordSetTrans, String paramString, int paramInt1, int paramInt2, int paramInt3) throws Exception {
/*  934 */     boolean bool = true;
/*  935 */     String str1 = paramRecordSetTrans.getDBType();
/*  936 */     String str2 = "";
/*      */     
/*  938 */     if (paramInt1 < 0) {
/*  939 */       str2 = "";
/*  940 */       int i = 0;
/*  941 */       if ("oracle".equals(str1)) {
/*  942 */         str2 = "select count(a.fnaSubjectaPk) cnt \n from fnaSubjectAccount a \n where a.fnasubjectapk <> ? \n start with a.fnaSubjectaPk = ? \n connect by prior a.fnaSubjectaPk = a.supFnaSubjectaPk";
/*      */ 
/*      */ 
/*      */       
/*      */       }
/*  947 */       else if ("postgresql".equals(str1)) {
/*  948 */         str2 = "WITH RECURSIVE  allsub(fnaSubjectaPk,subjectaName,supFnaSubjectaPk)\n as (\n   SELECT fnaSubjectaPk,subjectaName,supFnaSubjectaPk \n   FROM fnaSubjectAccount \n   where fnaSubjectaPk = ? \n   UNION ALL \n   SELECT a.fnaSubjectaPk,a.subjectaName,a.supFnaSubjectaPk \n   FROM fnaSubjectAccount a,allsub b \n   where a.supFnaSubjectaPk = b.fnaSubjectaPk\n ) select count(fnaSubjectaPk) cnt from allsub\n where fnaSubjectaPk <> ? ";
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*      */       }
/*      */       else {
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */         
/*  961 */         str2 = "WITH allsub(fnaSubjectaPk,subjectaName,supFnaSubjectaPk)\n as (\n   SELECT fnaSubjectaPk,subjectaName,supFnaSubjectaPk \n   FROM fnaSubjectAccount \n   where fnaSubjectaPk = ? \n   UNION ALL \n   SELECT a.fnaSubjectaPk,a.subjectaName,a.supFnaSubjectaPk \n   FROM fnaSubjectAccount a,allsub b \n   where a.supFnaSubjectaPk = b.fnaSubjectaPk\n ) select count(fnaSubjectaPk) cnt from allsub\n where fnaSubjectaPk <> ? ";
/*      */       } 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*  973 */       bool = paramRecordSetTrans.executeQuery(str2, new Object[] { paramString, paramString });
/*  974 */       if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(********, Util.getIntValue(paramInt3))); 
/*  975 */       if (paramRecordSetTrans.next()) {
/*  976 */         i = Util.getIntValue(paramRecordSetTrans.getString("cnt"), 0);
/*      */       }
/*  978 */       paramInt1 = (i > 0) ? 0 : 1;
/*      */     } 
/*      */     
/*  981 */     if (paramInt2 < 0) {
/*      */       
/*  983 */       str2 = "";
/*  984 */       if ("oracle".equals(str1)) {
/*  985 */         str2 = "select count(a.fnaSubjectaPk) cnt \n from fnaSubjectAccount a \n where a.fnasubjectapk <> ? \n start with a.fnaSubjectaPk = ? \n connect by prior a.supFnaSubjectaPk = a.fnaSubjectaPk";
/*      */ 
/*      */ 
/*      */       
/*      */       }
/*  990 */       else if ("postgresql".equals(str1)) {
/*      */         
/*  992 */         str2 = "WITH RECURSIVE  allsub(fnaSubjectaPk,subjectaName,supFnaSubjectaPk)\n as (\n   SELECT fnaSubjectaPk,subjectaName,supFnaSubjectaPk \n   FROM fnaSubjectAccount \n   where fnaSubjectaPk = ? \n   UNION ALL \n   SELECT a.fnaSubjectaPk,a.subjectaName,a.supFnaSubjectaPk \n   FROM fnaSubjectAccount a,allsub b \n   where a.fnaSubjectaPk = b.supFnaSubjectaPk\n ) select count(fnaSubjectaPk) cnt from allsub\n where fnaSubjectaPk <> ? ";
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*      */       }
/*      */       else {
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */         
/* 1005 */         str2 = "WITH allsub(fnaSubjectaPk,subjectaName,supFnaSubjectaPk)\n as (\n   SELECT fnaSubjectaPk,subjectaName,supFnaSubjectaPk \n   FROM fnaSubjectAccount \n   where fnaSubjectaPk = ? \n   UNION ALL \n   SELECT a.fnaSubjectaPk,a.subjectaName,a.supFnaSubjectaPk \n   FROM fnaSubjectAccount a,allsub b \n   where a.fnaSubjectaPk = b.supFnaSubjectaPk\n ) select count(fnaSubjectaPk) cnt from allsub\n where fnaSubjectaPk <> ? ";
/*      */       } 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/* 1017 */       bool = paramRecordSetTrans.executeQuery(str2, new Object[] { paramString, paramString });
/* 1018 */       if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(********, Util.getIntValue(paramInt3))); 
/* 1019 */       if (paramRecordSetTrans.next()) {
/* 1020 */         paramInt2 = Util.getIntValue(paramRecordSetTrans.getString("cnt"), 0);
/*      */       }
/* 1022 */       paramInt2++;
/*      */     } 
/*      */     
/* 1025 */     str2 = "update fnaSubjectAccount set subjectaIsLeaf=?, subjectaLevel=? where fnaSubjectaPk=?";
/* 1026 */     bool = paramRecordSetTrans.executeUpdate(str2, new Object[] { Integer.valueOf(paramInt1), Integer.valueOf(paramInt2), paramString });
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   @Deprecated
/*      */   public void deleteFnaSubjectAccount(RecordSetTrans paramRecordSetTrans, String[] paramArrayOfString, int paramInt) throws Exception {
/* 1038 */     FnaSubjectAccountHelp fnaSubjectAccountHelp = new FnaSubjectAccountHelp();
/* 1039 */     int i = paramArrayOfString.length;
/*      */ 
/*      */     
/* 1042 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*      */     
/* 1044 */     ArrayList<String> arrayList1 = new ArrayList();
/*      */     
/* 1046 */     int j = 0;
/* 1047 */     ArrayList<String> arrayList2 = new ArrayList(); int k;
/* 1048 */     for (k = 0; k < i; k++) {
/* 1049 */       String str = paramArrayOfString[k];
/*      */ 
/*      */       
/* 1052 */       boolean bool = paramRecordSetTrans.executeQuery("select subjectaLevel from fnaSubjectAccount where fnaSubjectaPk=?", new Object[] { str });
/* 1053 */       if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(********, Util.getIntValue(paramInt))); 
/* 1054 */       if (paramRecordSetTrans.next()) {
/* 1055 */         int n = Util.getIntValue(paramRecordSetTrans.getString("subjectaLevel"), 0);
/* 1056 */         if (n < 0) {
/* 1057 */           n = 0;
/*      */         }
/*      */         
/* 1060 */         if (n > j) {
/* 1061 */           j = n;
/*      */         }
/*      */ 
/*      */         
/* 1065 */         if (!arrayList1.contains(n + "")) {
/* 1066 */           arrayList1.add(n + "");
/*      */         }
/*      */         
/* 1069 */         List<String> list = null;
/*      */         
/* 1071 */         if (hashMap.containsKey(n + "")) {
/*      */           
/* 1073 */           list = (List)hashMap.get(n + "");
/*      */         } else {
/*      */           
/* 1076 */           list = new ArrayList();
/* 1077 */           hashMap.put(n + "", list);
/*      */         } 
/* 1079 */         list.add(str);
/*      */       } 
/*      */     } 
/*      */ 
/*      */     
/* 1084 */     k = j;
/*      */     
/* 1086 */     while (k >= 0) {
/*      */       
/* 1088 */       if (arrayList1.contains(k + "")) {
/*      */         
/* 1090 */         List<String> list = (List)hashMap.get(k + "");
/* 1091 */         int n = list.size();
/*      */         
/* 1093 */         for (byte b1 = 0; b1 < n; b1++) {
/* 1094 */           String str = list.get(b1);
/*      */           
/* 1096 */           StringBuffer stringBuffer = new StringBuffer();
/*      */           
/* 1098 */           boolean bool = fnaSubjectAccountHelp.checkCanDelete(paramRecordSetTrans, str, paramInt, stringBuffer);
/* 1099 */           if (!bool) throw new FnaException(stringBuffer.toString());
/*      */ 
/*      */           
/* 1102 */           bool = paramRecordSetTrans.executeQuery("select supFnaSubjectaPk from fnaSubjectAccount where fnaSubjectaPk=?", new Object[] { str });
/* 1103 */           if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(********, Util.getIntValue(paramInt))); 
/* 1104 */           if (paramRecordSetTrans.next()) {
/* 1105 */             String str1 = Util.null2String(paramRecordSetTrans.getString("supFnaSubjectaPk")).trim();
/* 1106 */             if (!"".equals(str1) && !arrayList2.contains(str1)) {
/* 1107 */               arrayList2.add(str1);
/*      */             }
/*      */           } 
/*      */           
/* 1111 */           bool = paramRecordSetTrans.executeUpdate("delete from fnaSubjectAccount where fnaSubjectaPk=?", new Object[] { str });
/* 1112 */           if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(********, Util.getIntValue(paramInt))); 
/*      */         } 
/*      */       } 
/* 1115 */       k--;
/*      */     } 
/*      */     
/* 1118 */     int m = arrayList2.size();
/* 1119 */     for (byte b = 0; b < m; b++)
/*      */     {
/* 1121 */       calculateSubjectaLevel(paramRecordSetTrans, arrayList2.get(b), paramInt);
/*      */     }
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   @Deprecated
/*      */   public String getFullShowName(String paramString, int paramInt) throws Exception {
/* 1134 */     RecordSet recordSet = new RecordSet();
/* 1135 */     String str1 = recordSet.getDBType();
/* 1136 */     boolean bool = true;
/*      */     
/* 1138 */     StringBuffer stringBuffer = new StringBuffer();
/* 1139 */     String str2 = "";
/* 1140 */     if ("oracle".equals(str1)) {
/* 1141 */       str2 = "select a.subjectaName \n from fnaSubjectAccount a \n start with a.fnaSubjectaPk = ? \n connect by prior a.supFnaSubjectaPk = a.fnaSubjectaPk";
/*      */ 
/*      */     
/*      */     }
/* 1145 */     else if ("postgresql".equals(str1)) {
/*      */       
/* 1147 */       str2 = "WITH RECURSIVE  allsub(fnaSubjectaPk,subjectaName,supFnaSubjectaPk)\n as (\n   SELECT fnaSubjectaPk,subjectaName,supFnaSubjectaPk \n   FROM fnaSubjectAccount \n   where fnaSubjectaPk = ? \n   UNION ALL \n   SELECT a.fnaSubjectaPk,a.subjectaName,a.supFnaSubjectaPk \n   FROM fnaSubjectAccount a,allsub b \n   where a.fnaSubjectaPk = b.supFnaSubjectaPk\n ) select * from allsub";
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*      */     }
/*      */     else {
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/* 1159 */       str2 = "WITH allsub(fnaSubjectaPk,subjectaName,supFnaSubjectaPk)\n as (\n   SELECT fnaSubjectaPk,subjectaName,supFnaSubjectaPk \n   FROM fnaSubjectAccount \n   where fnaSubjectaPk = ? \n   UNION ALL \n   SELECT a.fnaSubjectaPk,a.subjectaName,a.supFnaSubjectaPk \n   FROM fnaSubjectAccount a,allsub b \n   where a.fnaSubjectaPk = b.supFnaSubjectaPk\n ) select * from allsub";
/*      */     } 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/* 1170 */     bool = recordSet.executeQuery(str2, new Object[] { paramString });
/* 1171 */     if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(********, Util.getIntValue(paramInt))); 
/* 1172 */     byte b = 0;
/* 1173 */     while (recordSet.next()) {
/* 1174 */       if (b) {
/* 1175 */         stringBuffer.insert(0, getSubjectSplitStr());
/*      */       }
/* 1177 */       stringBuffer.insert(0, Util.null2String(recordSet.getString("subjectaName")));
/* 1178 */       b++;
/*      */     } 
/*      */     
/* 1181 */     return stringBuffer.toString();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   @Deprecated
/*      */   public List<String> getAllSubSubjectaPkList(String paramString, int paramInt) throws Exception {
/* 1193 */     RecordSet recordSet = new RecordSet();
/* 1194 */     String str1 = recordSet.getDBType();
/* 1195 */     boolean bool = true;
/*      */     
/* 1197 */     ArrayList<String> arrayList = new ArrayList();
/* 1198 */     String str2 = "";
/* 1199 */     if ("oracle".equals(str1)) {
/* 1200 */       str2 = "select a.fnaSubjectaPk \n from fnaSubjectAccount a \n start with a.fnaSubjectaPk = ? \n connect by prior a.fnaSubjectaPk = a.supFnaSubjectaPk";
/*      */ 
/*      */     
/*      */     }
/* 1204 */     else if ("postgresql".equals(str1)) {
/*      */       
/* 1206 */       str2 = "WITH RECURSIVE  allsub(fnaSubjectaPk,subjectaName,supFnaSubjectaPk)\n as (\n   SELECT fnaSubjectaPk,subjectaName,supFnaSubjectaPk \n   FROM fnaSubjectAccount \n   where fnaSubjectaPk = ? \n   UNION ALL \n   SELECT a.fnaSubjectaPk,a.subjectaName,a.supFnaSubjectaPk \n   FROM fnaSubjectAccount a,allsub b \n   where a.supFnaSubjectaPk = b.fnaSubjectaPk\n ) select * from allsub";
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*      */     }
/*      */     else {
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/* 1218 */       str2 = "WITH allsub(fnaSubjectaPk,subjectaName,supFnaSubjectaPk)\n as (\n   SELECT fnaSubjectaPk,subjectaName,supFnaSubjectaPk \n   FROM fnaSubjectAccount \n   where fnaSubjectaPk = ? \n   UNION ALL \n   SELECT a.fnaSubjectaPk,a.subjectaName,a.supFnaSubjectaPk \n   FROM fnaSubjectAccount a,allsub b \n   where a.supFnaSubjectaPk = b.fnaSubjectaPk\n ) select * from allsub";
/*      */     } 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/* 1229 */     bool = recordSet.executeQuery(str2, new Object[] { paramString });
/* 1230 */     if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(********, Util.getIntValue(paramInt))); 
/* 1231 */     while (recordSet.next()) {
/* 1232 */       arrayList.add(Util.null2String(recordSet.getString("fnaSubjectaPk")));
/*      */     }
/*      */     
/* 1235 */     return arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   @Deprecated
/*      */   public String loadAllSubjectbStrsBySubjectaPk(String paramString, int paramInt) throws Exception {
/* 1247 */     StringBuffer stringBuffer = new StringBuffer();
/* 1248 */     return loadAllSubjectbStrsBySubjectaPk(paramString, stringBuffer, null, paramInt);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   @Deprecated
/*      */   public String loadAllSubjectbStrsBySubjectaPk(String paramString, StringBuffer paramStringBuffer1, StringBuffer paramStringBuffer2, int paramInt) throws Exception {
/* 1262 */     RecordSet4Action recordSet4Action = new RecordSet4Action();
/* 1263 */     FnaSubjectBudgetDao fnaSubjectBudgetDao = new FnaSubjectBudgetDao();
/* 1264 */     FnaSubjectBudgetBo fnaSubjectBudgetBo = FnaSubjectBudgetBo.getInstance();
/* 1265 */     List<FnaSubjectBudget> list = fnaSubjectBudgetDao.queryForList(recordSet4Action, "select * from fnaSubjectBudget  where fnaSubjectaPk=?  order by showOrder, subjectbCode, subjectbName", new Object[] { paramString });
/*      */ 
/*      */ 
/*      */     
/* 1269 */     int i = list.size();
/* 1270 */     for (byte b = 0; b < i; b++) {
/* 1271 */       FnaSubjectBudget fnaSubjectBudget = list.get(b);
/* 1272 */       if (b > 0) {
/* 1273 */         paramStringBuffer1.append(",");
/* 1274 */         if (paramStringBuffer2 != null) {
/* 1275 */           paramStringBuffer2.append(",");
/*      */         }
/*      */       } 
/* 1278 */       paramStringBuffer1.append(fnaSubjectBudget.getFnaSubjectbPk());
/* 1279 */       if (paramStringBuffer2 != null) {
/* 1280 */         paramStringBuffer2.append(fnaSubjectBudgetBo.getFullShowName(fnaSubjectBudget.getFnaSubjectbPk(), paramInt));
/*      */       }
/*      */     } 
/* 1283 */     return paramStringBuffer1.toString();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   @Deprecated
/* 1304 */   private static final FnaSubjectAccountBo thisClassObj = new FnaSubjectAccountBo();
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   @Deprecated
/*      */   public static FnaSubjectAccountBo getInstance() {
/* 1311 */     return thisClassObj;
/*      */   }
/*      */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/fna/e9/bo/base/FnaSubjectAccountBo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */