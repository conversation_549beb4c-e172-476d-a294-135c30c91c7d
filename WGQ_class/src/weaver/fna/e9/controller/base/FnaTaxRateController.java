/*     */ package weaver.fna.e9.controller.base;
/*     */ 
/*     */ import java.util.HashMap;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import javax.servlet.jsp.JspWriter;
/*     */ import net.sf.json.JSONObject;
/*     */ import weaver.conn.RecordSetTrans;
/*     */ import weaver.fna.cache.FnaTaxRateComInfo;
/*     */ import weaver.fna.e9.bo.base.FnaTaxRateBo;
/*     */ import weaver.fna.e9.exception.FnaException;
/*     */ import weaver.fna.e9.po.base.FnaTaxRate;
/*     */ import weaver.fna.e9.vo.base.FnaTaxRateVo;
/*     */ import weaver.fna.general.RecordSet4Action;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class FnaTaxRateController
/*     */ {
/*  38 */   BaseBean bb = new BaseBean();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public double queryMaxShowOrder(int paramInt) throws Exception {
/*  47 */     FnaTaxRateBo fnaTaxRateBo = FnaTaxRateBo.getInstance();
/*  48 */     RecordSet4Action recordSet4Action = new RecordSet4Action();
/*  49 */     return fnaTaxRateBo.queryMaxShowOrder(recordSet4Action, paramInt);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void save(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse, JspWriter paramJspWriter, User paramUser) throws Exception {
/*  61 */     int i = paramUser.getLanguage();
/*  62 */     FnaBaseController fnaBaseController = FnaBaseController.getInstance();
/*  63 */     FnaTaxRateBo fnaTaxRateBo = FnaTaxRateBo.getInstance();
/*     */     
/*  65 */     FnaTaxRateVo fnaTaxRateVo = (FnaTaxRateVo)fnaBaseController.initVoFromHttpServletRequest(paramHttpServletRequest, FnaTaxRateVo.class.getName());
/*     */     
/*  67 */     FnaTaxRate fnaTaxRate = new FnaTaxRate();
/*  68 */     fnaBaseController.loadPoDataFromVo(fnaTaxRateVo, fnaTaxRate);
/*     */ 
/*     */     
/*  71 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  72 */     RecordSetTrans recordSetTrans = null;
/*     */     try {
/*  74 */       recordSetTrans = new RecordSetTrans();
/*  75 */       boolean bool = recordSetTrans.setAutoCommit(false);
/*  76 */       if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(i)));
/*     */       
/*  78 */       if (fnaTaxRate.getId().intValue() > 0) {
/*  79 */         fnaTaxRateBo.updateData(recordSetTrans, fnaTaxRate, i, hashMap);
/*     */       } else {
/*  81 */         fnaTaxRateBo.createData(recordSetTrans, fnaTaxRate, i, hashMap);
/*     */       } 
/*     */       
/*  84 */       bool = recordSetTrans.commit();
/*  85 */       if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(i)));
/*     */       
/*  87 */       FnaTaxRateComInfo fnaTaxRateComInfo = new FnaTaxRateComInfo();
/*  88 */       fnaTaxRateComInfo.addCache(fnaTaxRate.getId().toString());
/*  89 */     } catch (Exception exception) {
/*     */       try {
/*  91 */         if (recordSetTrans != null) {
/*  92 */           boolean bool = Boolean.parseBoolean(Util.null2String((String)hashMap.get("needRollback"), "false"));
/*  93 */           if (bool) recordSetTrans.rollback(); 
/*     */         } 
/*  95 */       } catch (Exception exception1) {}
/*  96 */       throw exception;
/*     */     } 
/*     */     
/*  99 */     JSONObject jSONObject = new JSONObject();
/* 100 */     jSONObject.element("flag", true);
/* 101 */     paramJspWriter.print(jSONObject.toString());
/* 102 */     paramHttpServletResponse.flushBuffer();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void createData(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse, JspWriter paramJspWriter, User paramUser) throws Exception {
/* 114 */     int i = paramUser.getLanguage();
/* 115 */     FnaBaseController fnaBaseController = FnaBaseController.getInstance();
/* 116 */     FnaTaxRateBo fnaTaxRateBo = FnaTaxRateBo.getInstance();
/*     */     
/* 118 */     FnaTaxRateVo fnaTaxRateVo = (FnaTaxRateVo)fnaBaseController.initVoFromHttpServletRequest(paramHttpServletRequest, FnaTaxRateVo.class.getName());
/*     */     
/* 120 */     FnaTaxRate fnaTaxRate = new FnaTaxRate();
/* 121 */     fnaBaseController.loadPoDataFromVo(fnaTaxRateVo, fnaTaxRate);
/*     */ 
/*     */     
/* 124 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 125 */     RecordSetTrans recordSetTrans = null;
/*     */     try {
/* 127 */       recordSetTrans = new RecordSetTrans();
/* 128 */       boolean bool = recordSetTrans.setAutoCommit(false);
/* 129 */       if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(i)));
/*     */       
/* 131 */       fnaTaxRateBo.createData(recordSetTrans, fnaTaxRate, i, hashMap);
/*     */       
/* 133 */       bool = recordSetTrans.commit();
/* 134 */       if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(i)));
/*     */       
/* 136 */       FnaTaxRateComInfo fnaTaxRateComInfo = new FnaTaxRateComInfo();
/* 137 */       fnaTaxRateComInfo.addCache(fnaTaxRate.getId().toString());
/* 138 */     } catch (Exception exception) {
/*     */       try {
/* 140 */         if (recordSetTrans != null) {
/* 141 */           boolean bool = Boolean.parseBoolean(Util.null2String((String)hashMap.get("needRollback"), "false"));
/* 142 */           if (bool) recordSetTrans.rollback(); 
/*     */         } 
/* 144 */       } catch (Exception exception1) {}
/* 145 */       throw exception;
/*     */     } 
/*     */     
/* 148 */     JSONObject jSONObject = new JSONObject();
/* 149 */     jSONObject.element("flag", true);
/* 150 */     paramJspWriter.print(jSONObject.toString());
/* 151 */     paramHttpServletResponse.flushBuffer();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void updateData(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse, JspWriter paramJspWriter, User paramUser) throws Exception {
/* 163 */     int i = paramUser.getLanguage();
/* 164 */     FnaBaseController fnaBaseController = FnaBaseController.getInstance();
/* 165 */     FnaTaxRateBo fnaTaxRateBo = FnaTaxRateBo.getInstance();
/*     */     
/* 167 */     FnaTaxRateVo fnaTaxRateVo = (FnaTaxRateVo)fnaBaseController.initVoFromHttpServletRequest(paramHttpServletRequest, FnaTaxRateVo.class.getName());
/*     */     
/* 169 */     FnaTaxRate fnaTaxRate = new FnaTaxRate();
/* 170 */     fnaBaseController.loadPoDataFromVo(fnaTaxRateVo, fnaTaxRate);
/*     */ 
/*     */     
/* 173 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 174 */     RecordSetTrans recordSetTrans = null;
/*     */     try {
/* 176 */       recordSetTrans = new RecordSetTrans();
/* 177 */       boolean bool = recordSetTrans.setAutoCommit(false);
/* 178 */       if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(i)));
/*     */       
/* 180 */       fnaTaxRateBo.updateData(recordSetTrans, fnaTaxRate, i, hashMap);
/*     */       
/* 182 */       bool = recordSetTrans.commit();
/* 183 */       if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(i)));
/*     */       
/* 185 */       FnaTaxRateComInfo fnaTaxRateComInfo = new FnaTaxRateComInfo();
/* 186 */       fnaTaxRateComInfo.updateCache(fnaTaxRate.getId().toString());
/* 187 */     } catch (Exception exception) {
/*     */       try {
/* 189 */         if (recordSetTrans != null) {
/* 190 */           boolean bool = Boolean.parseBoolean(Util.null2String((String)hashMap.get("needRollback"), "false"));
/* 191 */           if (bool) recordSetTrans.rollback(); 
/*     */         } 
/* 193 */       } catch (Exception exception1) {}
/* 194 */       throw exception;
/*     */     } 
/*     */     
/* 197 */     JSONObject jSONObject = new JSONObject();
/* 198 */     jSONObject.element("flag", true);
/* 199 */     paramJspWriter.print(jSONObject.toString());
/* 200 */     paramHttpServletResponse.flushBuffer();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void deleteData(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse, JspWriter paramJspWriter, User paramUser) throws Exception {
/* 212 */     int i = paramUser.getLanguage();
/* 213 */     FnaTaxRateBo fnaTaxRateBo = FnaTaxRateBo.getInstance();
/*     */     
/* 215 */     String str = Util.null2String(paramHttpServletRequest.getParameter("ids"));
/* 216 */     String[] arrayOfString = str.split(",");
/*     */     
/* 218 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 219 */     RecordSetTrans recordSetTrans = null;
/*     */     try {
/* 221 */       recordSetTrans = new RecordSetTrans();
/* 222 */       boolean bool = recordSetTrans.setAutoCommit(false);
/* 223 */       if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(i)));
/*     */       
/* 225 */       fnaTaxRateBo.deleteData(recordSetTrans, arrayOfString, i, hashMap);
/*     */       
/* 227 */       bool = recordSetTrans.commit();
/* 228 */       if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(i)));
/*     */       
/* 230 */       FnaTaxRateComInfo fnaTaxRateComInfo = new FnaTaxRateComInfo();
/* 231 */       int j = arrayOfString.length;
/* 232 */       for (byte b = 0; b < j; b++) {
/* 233 */         fnaTaxRateComInfo.deleteCache(arrayOfString[b]);
/*     */       }
/* 235 */     } catch (Exception exception) {
/*     */       try {
/* 237 */         if (recordSetTrans != null) {
/* 238 */           boolean bool = Boolean.parseBoolean(Util.null2String((String)hashMap.get("needRollback"), "false"));
/* 239 */           if (bool) recordSetTrans.rollback(); 
/*     */         } 
/* 241 */       } catch (Exception exception1) {}
/* 242 */       throw exception;
/*     */     } 
/*     */     
/* 245 */     JSONObject jSONObject = new JSONObject();
/* 246 */     jSONObject.element("flag", true);
/* 247 */     paramJspWriter.print(jSONObject.toString());
/* 248 */     paramHttpServletResponse.flushBuffer();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void changeState(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse, JspWriter paramJspWriter, User paramUser) throws Exception {
/* 260 */     int i = paramUser.getLanguage();
/* 261 */     FnaTaxRateBo fnaTaxRateBo = FnaTaxRateBo.getInstance();
/*     */     
/* 263 */     int j = Util.getIntValue(paramHttpServletRequest.getParameter("id"));
/* 264 */     int k = Util.getIntValue(paramHttpServletRequest.getParameter("state"));
/*     */     
/* 266 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 267 */     RecordSetTrans recordSetTrans = null;
/*     */     try {
/* 269 */       recordSetTrans = new RecordSetTrans();
/* 270 */       boolean bool = recordSetTrans.setAutoCommit(false);
/* 271 */       if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(i)));
/*     */       
/* 273 */       fnaTaxRateBo.changeState(recordSetTrans, j, k, i, hashMap);
/*     */       
/* 275 */       bool = recordSetTrans.commit();
/* 276 */       if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(i)));
/*     */       
/* 278 */       FnaTaxRateComInfo fnaTaxRateComInfo = new FnaTaxRateComInfo();
/* 279 */       fnaTaxRateComInfo.updateCache(String.valueOf(j));
/* 280 */     } catch (Exception exception) {
/*     */       try {
/* 282 */         if (recordSetTrans != null) {
/* 283 */           boolean bool = Boolean.parseBoolean(Util.null2String((String)hashMap.get("needRollback"), "false"));
/* 284 */           if (bool) recordSetTrans.rollback(); 
/*     */         } 
/* 286 */       } catch (Exception exception1) {}
/* 287 */       throw exception;
/*     */     } 
/*     */     
/* 290 */     JSONObject jSONObject = new JSONObject();
/* 291 */     jSONObject.element("flag", true);
/* 292 */     paramJspWriter.print(jSONObject.toString());
/* 293 */     paramHttpServletResponse.flushBuffer();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/* 306 */   private static final FnaTaxRateController thisClassObj = new FnaTaxRateController();
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static FnaTaxRateController getInstance() {
/* 312 */     return thisClassObj;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/fna/e9/controller/base/FnaTaxRateController.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */