/*     */ package weaver.fna.e9.controller.base;
/*     */ 
/*     */ import java.util.HashMap;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import javax.servlet.jsp.JspWriter;
/*     */ import net.sf.json.JSONObject;
/*     */ import weaver.conn.RecordSetTrans;
/*     */ import weaver.fna.cache.FnaVehicleComInfo;
/*     */ import weaver.fna.e9.bo.base.FnaVehicleBo;
/*     */ import weaver.fna.e9.exception.FnaException;
/*     */ import weaver.fna.e9.po.base.FnaVehicle;
/*     */ import weaver.fna.e9.vo.base.FnaVehicleVo;
/*     */ import weaver.fna.general.RecordSet4Action;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class FnaVehicleController
/*     */ {
/*  38 */   BaseBean bb = new BaseBean();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public double queryMaxShowOrder(int paramInt) throws Exception {
/*  47 */     FnaVehicleBo fnaVehicleBo = FnaVehicleBo.getInstance();
/*  48 */     RecordSet4Action recordSet4Action = new RecordSet4Action();
/*  49 */     return fnaVehicleBo.queryMaxShowOrder(recordSet4Action, paramInt);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void save(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse, JspWriter paramJspWriter, User paramUser) throws Exception {
/*  61 */     int i = paramUser.getLanguage();
/*  62 */     FnaBaseController fnaBaseController = FnaBaseController.getInstance();
/*  63 */     FnaVehicleBo fnaVehicleBo = FnaVehicleBo.getInstance();
/*     */     
/*  65 */     FnaVehicleVo fnaVehicleVo = (FnaVehicleVo)fnaBaseController.initVoFromHttpServletRequest(paramHttpServletRequest, FnaVehicleVo.class.getName());
/*     */     
/*  67 */     FnaVehicle fnaVehicle = new FnaVehicle();
/*  68 */     fnaBaseController.loadPoDataFromVo(fnaVehicleVo, fnaVehicle);
/*     */ 
/*     */     
/*  71 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  72 */     RecordSetTrans recordSetTrans = null;
/*     */     try {
/*  74 */       recordSetTrans = new RecordSetTrans();
/*  75 */       boolean bool = recordSetTrans.setAutoCommit(false);
/*  76 */       if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(i)));
/*     */       
/*  78 */       if (fnaVehicle.getId().intValue() > 0) {
/*  79 */         fnaVehicleBo.updateData(recordSetTrans, fnaVehicle, i, hashMap);
/*     */       } else {
/*  81 */         fnaVehicleBo.createData(recordSetTrans, fnaVehicle, i, hashMap);
/*     */       } 
/*     */       
/*  84 */       bool = recordSetTrans.commit();
/*  85 */       if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(i)));
/*     */       
/*  87 */       FnaVehicleComInfo fnaVehicleComInfo = new FnaVehicleComInfo();
/*  88 */       fnaVehicleComInfo.addCache(fnaVehicle.getId().toString());
/*  89 */     } catch (Exception exception) {
/*     */       try {
/*  91 */         if (recordSetTrans != null) {
/*  92 */           boolean bool = Boolean.parseBoolean(Util.null2String((String)hashMap.get("needRollback"), "false"));
/*  93 */           if (bool) recordSetTrans.rollback(); 
/*     */         } 
/*  95 */       } catch (Exception exception1) {}
/*  96 */       throw exception;
/*     */     } 
/*     */     
/*  99 */     JSONObject jSONObject = new JSONObject();
/* 100 */     jSONObject.element("flag", true);
/* 101 */     paramJspWriter.print(jSONObject.toString());
/* 102 */     paramHttpServletResponse.flushBuffer();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void createData(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse, JspWriter paramJspWriter, User paramUser) throws Exception {
/* 114 */     int i = paramUser.getLanguage();
/* 115 */     FnaBaseController fnaBaseController = FnaBaseController.getInstance();
/* 116 */     FnaVehicleBo fnaVehicleBo = FnaVehicleBo.getInstance();
/*     */     
/* 118 */     FnaVehicleVo fnaVehicleVo = (FnaVehicleVo)fnaBaseController.initVoFromHttpServletRequest(paramHttpServletRequest, FnaVehicleVo.class.getName());
/*     */     
/* 120 */     FnaVehicle fnaVehicle = new FnaVehicle();
/* 121 */     fnaBaseController.loadPoDataFromVo(fnaVehicleVo, fnaVehicle);
/*     */ 
/*     */     
/* 124 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 125 */     RecordSetTrans recordSetTrans = null;
/*     */     try {
/* 127 */       recordSetTrans = new RecordSetTrans();
/* 128 */       boolean bool = recordSetTrans.setAutoCommit(false);
/* 129 */       if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(i)));
/*     */       
/* 131 */       fnaVehicleBo.createData(recordSetTrans, fnaVehicle, i, hashMap);
/*     */       
/* 133 */       bool = recordSetTrans.commit();
/* 134 */       if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(i)));
/*     */       
/* 136 */       FnaVehicleComInfo fnaVehicleComInfo = new FnaVehicleComInfo();
/* 137 */       fnaVehicleComInfo.addCache(fnaVehicle.getId().toString());
/* 138 */     } catch (Exception exception) {
/*     */       try {
/* 140 */         if (recordSetTrans != null) {
/* 141 */           boolean bool = Boolean.parseBoolean(Util.null2String((String)hashMap.get("needRollback"), "false"));
/* 142 */           if (bool) recordSetTrans.rollback(); 
/*     */         } 
/* 144 */       } catch (Exception exception1) {}
/* 145 */       throw exception;
/*     */     } 
/*     */     
/* 148 */     JSONObject jSONObject = new JSONObject();
/* 149 */     jSONObject.element("flag", true);
/* 150 */     paramJspWriter.print(jSONObject.toString());
/* 151 */     paramHttpServletResponse.flushBuffer();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void updateData(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse, JspWriter paramJspWriter, User paramUser) throws Exception {
/* 163 */     int i = paramUser.getLanguage();
/* 164 */     FnaBaseController fnaBaseController = FnaBaseController.getInstance();
/* 165 */     FnaVehicleBo fnaVehicleBo = FnaVehicleBo.getInstance();
/*     */     
/* 167 */     FnaVehicleVo fnaVehicleVo = (FnaVehicleVo)fnaBaseController.initVoFromHttpServletRequest(paramHttpServletRequest, FnaVehicleVo.class.getName());
/*     */     
/* 169 */     FnaVehicle fnaVehicle = new FnaVehicle();
/* 170 */     fnaBaseController.loadPoDataFromVo(fnaVehicleVo, fnaVehicle);
/*     */ 
/*     */     
/* 173 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 174 */     RecordSetTrans recordSetTrans = null;
/*     */     try {
/* 176 */       recordSetTrans = new RecordSetTrans();
/* 177 */       boolean bool = recordSetTrans.setAutoCommit(false);
/* 178 */       if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(i)));
/*     */       
/* 180 */       fnaVehicleBo.updateData(recordSetTrans, fnaVehicle, i, hashMap);
/*     */       
/* 182 */       bool = recordSetTrans.commit();
/* 183 */       if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(i)));
/*     */       
/* 185 */       FnaVehicleComInfo fnaVehicleComInfo = new FnaVehicleComInfo();
/* 186 */       fnaVehicleComInfo.updateCache(fnaVehicle.getId().toString());
/* 187 */     } catch (Exception exception) {
/*     */       try {
/* 189 */         if (recordSetTrans != null) {
/* 190 */           boolean bool = Boolean.parseBoolean(Util.null2String((String)hashMap.get("needRollback"), "false"));
/* 191 */           if (bool) recordSetTrans.rollback(); 
/*     */         } 
/* 193 */       } catch (Exception exception1) {}
/* 194 */       throw exception;
/*     */     } 
/*     */     
/* 197 */     JSONObject jSONObject = new JSONObject();
/* 198 */     jSONObject.element("flag", true);
/* 199 */     paramJspWriter.print(jSONObject.toString());
/* 200 */     paramHttpServletResponse.flushBuffer();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void deleteData(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse, JspWriter paramJspWriter, User paramUser) throws Exception {
/* 212 */     int i = paramUser.getLanguage();
/* 213 */     FnaVehicleBo fnaVehicleBo = FnaVehicleBo.getInstance();
/*     */     
/* 215 */     String str = Util.null2String(paramHttpServletRequest.getParameter("ids"));
/* 216 */     String[] arrayOfString = str.split(",");
/*     */     
/* 218 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 219 */     RecordSetTrans recordSetTrans = null;
/*     */     try {
/* 221 */       recordSetTrans = new RecordSetTrans();
/* 222 */       boolean bool = recordSetTrans.setAutoCommit(false);
/* 223 */       if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(i)));
/*     */       
/* 225 */       fnaVehicleBo.deleteData(recordSetTrans, arrayOfString, i, hashMap);
/*     */       
/* 227 */       bool = recordSetTrans.commit();
/* 228 */       if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(i)));
/*     */       
/* 230 */       FnaVehicleComInfo fnaVehicleComInfo = new FnaVehicleComInfo();
/* 231 */       int j = arrayOfString.length;
/* 232 */       for (byte b = 0; b < j; b++) {
/* 233 */         fnaVehicleComInfo.deleteCache(arrayOfString[b]);
/*     */       }
/* 235 */     } catch (Exception exception) {
/*     */       try {
/* 237 */         if (recordSetTrans != null) {
/* 238 */           boolean bool = Boolean.parseBoolean(Util.null2String((String)hashMap.get("needRollback"), "false"));
/* 239 */           if (bool) recordSetTrans.rollback(); 
/*     */         } 
/* 241 */       } catch (Exception exception1) {}
/* 242 */       throw exception;
/*     */     } 
/*     */     
/* 245 */     JSONObject jSONObject = new JSONObject();
/* 246 */     jSONObject.element("flag", true);
/* 247 */     paramJspWriter.print(jSONObject.toString());
/* 248 */     paramHttpServletResponse.flushBuffer();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void changeState(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse, JspWriter paramJspWriter, User paramUser) throws Exception {
/* 260 */     int i = paramUser.getLanguage();
/* 261 */     FnaVehicleBo fnaVehicleBo = FnaVehicleBo.getInstance();
/*     */     
/* 263 */     int j = Util.getIntValue(paramHttpServletRequest.getParameter("id"));
/* 264 */     int k = Util.getIntValue(paramHttpServletRequest.getParameter("state"));
/*     */     
/* 266 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 267 */     RecordSetTrans recordSetTrans = null;
/*     */     try {
/* 269 */       recordSetTrans = new RecordSetTrans();
/* 270 */       boolean bool = recordSetTrans.setAutoCommit(false);
/* 271 */       if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(i)));
/*     */       
/* 273 */       fnaVehicleBo.changeState(recordSetTrans, j, k, i, hashMap);
/*     */       
/* 275 */       bool = recordSetTrans.commit();
/* 276 */       if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(i)));
/*     */       
/* 278 */       FnaVehicleComInfo fnaVehicleComInfo = new FnaVehicleComInfo();
/* 279 */       fnaVehicleComInfo.updateCache(String.valueOf(j));
/* 280 */     } catch (Exception exception) {
/*     */       try {
/* 282 */         if (recordSetTrans != null) {
/* 283 */           boolean bool = Boolean.parseBoolean(Util.null2String((String)hashMap.get("needRollback"), "false"));
/* 284 */           if (bool) recordSetTrans.rollback(); 
/*     */         } 
/* 286 */       } catch (Exception exception1) {}
/* 287 */       throw exception;
/*     */     } 
/*     */     
/* 290 */     JSONObject jSONObject = new JSONObject();
/* 291 */     jSONObject.element("flag", true);
/* 292 */     paramJspWriter.print(jSONObject.toString());
/* 293 */     paramHttpServletResponse.flushBuffer();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/* 306 */   private static final FnaVehicleController thisClassObj = new FnaVehicleController();
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static FnaVehicleController getInstance() {
/* 312 */     return thisClassObj;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/fna/e9/controller/base/FnaVehicleController.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */