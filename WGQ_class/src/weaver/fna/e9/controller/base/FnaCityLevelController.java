/*     */ package weaver.fna.e9.controller.base;
/*     */ 
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import javax.servlet.jsp.JspWriter;
/*     */ import net.sf.json.JSONObject;
/*     */ import weaver.conn.RecordSetTrans;
/*     */ import weaver.fna.cache.FnaCityLevelComInfo;
/*     */ import weaver.fna.e9.bo.base.FnaCityLevelBo;
/*     */ import weaver.fna.e9.exception.FnaException;
/*     */ import weaver.fna.e9.po.base.FnaCityLevel;
/*     */ import weaver.fna.e9.vo.base.FnaCityLevelVo;
/*     */ import weaver.fna.general.RecordSet4Action;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class FnaCityLevelController
/*     */ {
/*  39 */   BaseBean bb = new BaseBean();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<Integer> queryCityByCityLevel(int paramInt1, int paramInt2) throws Exception {
/*  49 */     FnaCityLevelBo fnaCityLevelBo = FnaCityLevelBo.getInstance();
/*  50 */     RecordSet4Action recordSet4Action = new RecordSet4Action();
/*  51 */     return fnaCityLevelBo.queryCityByCityLevel(recordSet4Action, paramInt1, paramInt2);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public double queryMaxShowOrder(int paramInt) throws Exception {
/*  61 */     FnaCityLevelBo fnaCityLevelBo = FnaCityLevelBo.getInstance();
/*  62 */     RecordSet4Action recordSet4Action = new RecordSet4Action();
/*  63 */     return fnaCityLevelBo.queryMaxShowOrder(recordSet4Action, paramInt);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void save(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse, JspWriter paramJspWriter, User paramUser) throws Exception {
/*  75 */     int i = paramUser.getLanguage();
/*  76 */     FnaBaseController fnaBaseController = FnaBaseController.getInstance();
/*  77 */     FnaCityLevelBo fnaCityLevelBo = FnaCityLevelBo.getInstance();
/*     */     
/*  79 */     FnaCityLevelVo fnaCityLevelVo = (FnaCityLevelVo)fnaBaseController.initVoFromHttpServletRequest(paramHttpServletRequest, FnaCityLevelVo.class.getName());
/*     */     
/*  81 */     FnaCityLevel fnaCityLevel = new FnaCityLevel();
/*  82 */     fnaBaseController.loadPoDataFromVo(fnaCityLevelVo, fnaCityLevel);
/*     */ 
/*     */     
/*  85 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  86 */     RecordSetTrans recordSetTrans = null;
/*     */     try {
/*  88 */       recordSetTrans = new RecordSetTrans();
/*  89 */       boolean bool = recordSetTrans.setAutoCommit(false);
/*  90 */       if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(i)));
/*     */       
/*  92 */       if (fnaCityLevel.getId().intValue() > 0) {
/*  93 */         fnaCityLevelBo.updateData(recordSetTrans, fnaCityLevel, i, hashMap);
/*     */       } else {
/*  95 */         fnaCityLevelBo.createData(recordSetTrans, fnaCityLevel, i, hashMap);
/*     */       } 
/*     */       
/*  98 */       bool = recordSetTrans.commit();
/*  99 */       if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(i)));
/*     */       
/* 101 */       FnaCityLevelComInfo fnaCityLevelComInfo = new FnaCityLevelComInfo();
/* 102 */       if (fnaCityLevel.getId().intValue() > 0) {
/* 103 */         fnaCityLevelComInfo.updateCache(fnaCityLevel.getId().toString());
/*     */       } else {
/* 105 */         fnaCityLevelComInfo.addCache(fnaCityLevel.getId().toString());
/*     */       } 
/* 107 */     } catch (Exception exception) {
/*     */       try {
/* 109 */         if (recordSetTrans != null) {
/* 110 */           boolean bool = Boolean.parseBoolean(Util.null2String((String)hashMap.get("needRollback"), "false"));
/* 111 */           if (bool) recordSetTrans.rollback(); 
/*     */         } 
/* 113 */       } catch (Exception exception1) {}
/* 114 */       throw exception;
/*     */     } 
/*     */     
/* 117 */     JSONObject jSONObject = new JSONObject();
/* 118 */     jSONObject.element("flag", true);
/* 119 */     paramJspWriter.print(jSONObject.toString());
/* 120 */     paramHttpServletResponse.flushBuffer();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void createData(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse, JspWriter paramJspWriter, User paramUser) throws Exception {
/* 132 */     int i = paramUser.getLanguage();
/* 133 */     FnaBaseController fnaBaseController = FnaBaseController.getInstance();
/* 134 */     FnaCityLevelBo fnaCityLevelBo = FnaCityLevelBo.getInstance();
/*     */     
/* 136 */     FnaCityLevelVo fnaCityLevelVo = (FnaCityLevelVo)fnaBaseController.initVoFromHttpServletRequest(paramHttpServletRequest, FnaCityLevelVo.class.getName());
/*     */     
/* 138 */     FnaCityLevel fnaCityLevel = new FnaCityLevel();
/* 139 */     fnaBaseController.loadPoDataFromVo(fnaCityLevelVo, fnaCityLevel);
/*     */ 
/*     */     
/* 142 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 143 */     RecordSetTrans recordSetTrans = null;
/*     */     try {
/* 145 */       recordSetTrans = new RecordSetTrans();
/* 146 */       boolean bool = recordSetTrans.setAutoCommit(false);
/* 147 */       if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(i)));
/*     */       
/* 149 */       fnaCityLevelBo.createData(recordSetTrans, fnaCityLevel, i, hashMap);
/*     */       
/* 151 */       bool = recordSetTrans.commit();
/* 152 */       if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(i)));
/*     */       
/* 154 */       FnaCityLevelComInfo fnaCityLevelComInfo = new FnaCityLevelComInfo();
/* 155 */       fnaCityLevelComInfo.addCache(fnaCityLevel.getId().toString());
/* 156 */     } catch (Exception exception) {
/*     */       try {
/* 158 */         if (recordSetTrans != null) {
/* 159 */           boolean bool = Boolean.parseBoolean(Util.null2String((String)hashMap.get("needRollback"), "false"));
/* 160 */           if (bool) recordSetTrans.rollback(); 
/*     */         } 
/* 162 */       } catch (Exception exception1) {}
/* 163 */       throw exception;
/*     */     } 
/*     */     
/* 166 */     JSONObject jSONObject = new JSONObject();
/* 167 */     jSONObject.element("flag", true);
/* 168 */     paramJspWriter.print(jSONObject.toString());
/* 169 */     paramHttpServletResponse.flushBuffer();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void updateData(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse, JspWriter paramJspWriter, User paramUser) throws Exception {
/* 181 */     int i = paramUser.getLanguage();
/* 182 */     FnaBaseController fnaBaseController = FnaBaseController.getInstance();
/* 183 */     FnaCityLevelBo fnaCityLevelBo = FnaCityLevelBo.getInstance();
/*     */     
/* 185 */     FnaCityLevelVo fnaCityLevelVo = (FnaCityLevelVo)fnaBaseController.initVoFromHttpServletRequest(paramHttpServletRequest, FnaCityLevelVo.class.getName());
/*     */     
/* 187 */     FnaCityLevel fnaCityLevel = new FnaCityLevel();
/* 188 */     fnaBaseController.loadPoDataFromVo(fnaCityLevelVo, fnaCityLevel);
/*     */ 
/*     */     
/* 191 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 192 */     RecordSetTrans recordSetTrans = null;
/*     */     try {
/* 194 */       recordSetTrans = new RecordSetTrans();
/* 195 */       boolean bool = recordSetTrans.setAutoCommit(false);
/* 196 */       if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(i)));
/*     */       
/* 198 */       fnaCityLevelBo.updateData(recordSetTrans, fnaCityLevel, i, hashMap);
/*     */       
/* 200 */       bool = recordSetTrans.commit();
/* 201 */       if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(i)));
/*     */       
/* 203 */       FnaCityLevelComInfo fnaCityLevelComInfo = new FnaCityLevelComInfo();
/* 204 */       fnaCityLevelComInfo.updateCache(fnaCityLevel.getId().toString());
/* 205 */     } catch (Exception exception) {
/*     */       try {
/* 207 */         if (recordSetTrans != null) {
/* 208 */           boolean bool = Boolean.parseBoolean(Util.null2String((String)hashMap.get("needRollback"), "false"));
/* 209 */           if (bool) recordSetTrans.rollback(); 
/*     */         } 
/* 211 */       } catch (Exception exception1) {}
/* 212 */       throw exception;
/*     */     } 
/*     */     
/* 215 */     JSONObject jSONObject = new JSONObject();
/* 216 */     jSONObject.element("flag", true);
/* 217 */     paramJspWriter.print(jSONObject.toString());
/* 218 */     paramHttpServletResponse.flushBuffer();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void deleteData(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse, JspWriter paramJspWriter, User paramUser) throws Exception {
/* 230 */     int i = paramUser.getLanguage();
/* 231 */     FnaCityLevelBo fnaCityLevelBo = FnaCityLevelBo.getInstance();
/*     */     
/* 233 */     String str = Util.null2String(paramHttpServletRequest.getParameter("ids"));
/* 234 */     String[] arrayOfString = str.split(",");
/*     */     
/* 236 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 237 */     RecordSetTrans recordSetTrans = null;
/*     */     try {
/* 239 */       recordSetTrans = new RecordSetTrans();
/* 240 */       boolean bool = recordSetTrans.setAutoCommit(false);
/* 241 */       if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(i)));
/*     */       
/* 243 */       fnaCityLevelBo.deleteData(recordSetTrans, arrayOfString, i, hashMap);
/*     */       
/* 245 */       bool = recordSetTrans.commit();
/* 246 */       if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(i)));
/*     */       
/* 248 */       FnaCityLevelComInfo fnaCityLevelComInfo = new FnaCityLevelComInfo();
/* 249 */       int j = arrayOfString.length;
/* 250 */       for (byte b = 0; b < j; b++) {
/* 251 */         fnaCityLevelComInfo.deleteCache(arrayOfString[b]);
/*     */       }
/* 253 */     } catch (Exception exception) {
/*     */       try {
/* 255 */         if (recordSetTrans != null) {
/* 256 */           boolean bool = Boolean.parseBoolean(Util.null2String((String)hashMap.get("needRollback"), "false"));
/* 257 */           if (bool) recordSetTrans.rollback(); 
/*     */         } 
/* 259 */       } catch (Exception exception1) {}
/* 260 */       throw exception;
/*     */     } 
/*     */     
/* 263 */     JSONObject jSONObject = new JSONObject();
/* 264 */     jSONObject.element("flag", true);
/* 265 */     paramJspWriter.print(jSONObject.toString());
/* 266 */     paramHttpServletResponse.flushBuffer();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void changeState(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse, JspWriter paramJspWriter, User paramUser) throws Exception {
/* 278 */     int i = paramUser.getLanguage();
/* 279 */     FnaCityLevelBo fnaCityLevelBo = FnaCityLevelBo.getInstance();
/*     */     
/* 281 */     int j = Util.getIntValue(paramHttpServletRequest.getParameter("id"));
/* 282 */     int k = Util.getIntValue(paramHttpServletRequest.getParameter("state"));
/*     */     
/* 284 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 285 */     RecordSetTrans recordSetTrans = null;
/*     */     try {
/* 287 */       recordSetTrans = new RecordSetTrans();
/* 288 */       boolean bool = recordSetTrans.setAutoCommit(false);
/* 289 */       if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(i)));
/*     */       
/* 291 */       fnaCityLevelBo.changeState(recordSetTrans, j, k, i, hashMap);
/*     */       
/* 293 */       bool = recordSetTrans.commit();
/* 294 */       if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(i)));
/*     */       
/* 296 */       FnaCityLevelComInfo fnaCityLevelComInfo = new FnaCityLevelComInfo();
/* 297 */       fnaCityLevelComInfo.updateCache(String.valueOf(j));
/* 298 */     } catch (Exception exception) {
/*     */       try {
/* 300 */         if (recordSetTrans != null) {
/* 301 */           boolean bool = Boolean.parseBoolean(Util.null2String((String)hashMap.get("needRollback"), "false"));
/* 302 */           if (bool) recordSetTrans.rollback(); 
/*     */         } 
/* 304 */       } catch (Exception exception1) {}
/* 305 */       throw exception;
/*     */     } 
/*     */     
/* 308 */     JSONObject jSONObject = new JSONObject();
/* 309 */     jSONObject.element("flag", true);
/* 310 */     paramJspWriter.print(jSONObject.toString());
/* 311 */     paramHttpServletResponse.flushBuffer();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/* 324 */   private static final FnaCityLevelController thisClassObj = new FnaCityLevelController();
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static FnaCityLevelController getInstance() {
/* 330 */     return thisClassObj;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/fna/e9/controller/base/FnaCityLevelController.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */