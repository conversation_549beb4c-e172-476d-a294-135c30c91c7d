/*     */ package weaver.fna.e9.controller.base;
/*     */ 
/*     */ import java.util.HashMap;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import javax.servlet.jsp.JspWriter;
/*     */ import net.sf.json.JSONObject;
/*     */ import weaver.conn.RecordSetTrans;
/*     */ import weaver.fna.cache.FnaInvoiceTypeComInfo;
/*     */ import weaver.fna.e9.bo.base.FnaInvoiceTypeBo;
/*     */ import weaver.fna.e9.exception.FnaException;
/*     */ import weaver.fna.e9.po.base.FnaInvoiceType;
/*     */ import weaver.fna.e9.vo.base.FnaInvoiceTypeVo;
/*     */ import weaver.fna.general.RecordSet4Action;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class FnaInvoiceTypeController
/*     */ {
/*  38 */   BaseBean bb = new BaseBean();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public double queryMaxShowOrder(int paramInt) throws Exception {
/*  47 */     FnaInvoiceTypeBo fnaInvoiceTypeBo = FnaInvoiceTypeBo.getInstance();
/*  48 */     RecordSet4Action recordSet4Action = new RecordSet4Action();
/*  49 */     return fnaInvoiceTypeBo.queryMaxShowOrder(recordSet4Action, paramInt);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void save(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse, JspWriter paramJspWriter, User paramUser) throws Exception {
/*  61 */     int i = paramUser.getLanguage();
/*  62 */     FnaBaseController fnaBaseController = FnaBaseController.getInstance();
/*  63 */     FnaInvoiceTypeBo fnaInvoiceTypeBo = FnaInvoiceTypeBo.getInstance();
/*     */     
/*  65 */     FnaInvoiceTypeVo fnaInvoiceTypeVo = (FnaInvoiceTypeVo)fnaBaseController.initVoFromHttpServletRequest(paramHttpServletRequest, FnaInvoiceTypeVo.class.getName());
/*     */     
/*  67 */     FnaInvoiceType fnaInvoiceType = new FnaInvoiceType();
/*  68 */     fnaBaseController.loadPoDataFromVo(fnaInvoiceTypeVo, fnaInvoiceType);
/*     */ 
/*     */     
/*  71 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  72 */     RecordSetTrans recordSetTrans = null;
/*     */     try {
/*  74 */       recordSetTrans = new RecordSetTrans();
/*  75 */       boolean bool = recordSetTrans.setAutoCommit(false);
/*  76 */       if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(i)));
/*     */       
/*  78 */       if (fnaInvoiceType.getId().intValue() > 0) {
/*  79 */         fnaInvoiceTypeBo.updateData(recordSetTrans, fnaInvoiceType, i, hashMap);
/*     */       } else {
/*  81 */         fnaInvoiceTypeBo.createData(recordSetTrans, fnaInvoiceType, i, hashMap);
/*     */       } 
/*     */       
/*  84 */       bool = recordSetTrans.commit();
/*  85 */       if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(i)));
/*     */       
/*  87 */       FnaInvoiceTypeComInfo fnaInvoiceTypeComInfo = new FnaInvoiceTypeComInfo();
/*  88 */       fnaInvoiceTypeComInfo.addCache(fnaInvoiceType.getId().toString());
/*  89 */     } catch (Exception exception) {
/*     */       try {
/*  91 */         if (recordSetTrans != null) {
/*  92 */           boolean bool = Boolean.parseBoolean(Util.null2String((String)hashMap.get("needRollback"), "false"));
/*  93 */           if (bool) recordSetTrans.rollback(); 
/*     */         } 
/*  95 */       } catch (Exception exception1) {}
/*  96 */       throw exception;
/*     */     } 
/*     */     
/*  99 */     JSONObject jSONObject = new JSONObject();
/* 100 */     jSONObject.element("flag", true);
/* 101 */     paramJspWriter.print(jSONObject.toString());
/* 102 */     paramHttpServletResponse.flushBuffer();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void createData(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse, JspWriter paramJspWriter, User paramUser) throws Exception {
/* 114 */     int i = paramUser.getLanguage();
/* 115 */     FnaBaseController fnaBaseController = FnaBaseController.getInstance();
/* 116 */     FnaInvoiceTypeBo fnaInvoiceTypeBo = FnaInvoiceTypeBo.getInstance();
/*     */     
/* 118 */     FnaInvoiceTypeVo fnaInvoiceTypeVo = (FnaInvoiceTypeVo)fnaBaseController.initVoFromHttpServletRequest(paramHttpServletRequest, FnaInvoiceTypeVo.class.getName());
/*     */     
/* 120 */     FnaInvoiceType fnaInvoiceType = new FnaInvoiceType();
/* 121 */     fnaBaseController.loadPoDataFromVo(fnaInvoiceTypeVo, fnaInvoiceType);
/*     */ 
/*     */     
/* 124 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 125 */     RecordSetTrans recordSetTrans = null;
/*     */     try {
/* 127 */       recordSetTrans = new RecordSetTrans();
/* 128 */       boolean bool = recordSetTrans.setAutoCommit(false);
/* 129 */       if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(i)));
/*     */       
/* 131 */       fnaInvoiceTypeBo.createData(recordSetTrans, fnaInvoiceType, i, hashMap);
/*     */       
/* 133 */       bool = recordSetTrans.commit();
/* 134 */       if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(i)));
/*     */       
/* 136 */       FnaInvoiceTypeComInfo fnaInvoiceTypeComInfo = new FnaInvoiceTypeComInfo();
/* 137 */       fnaInvoiceTypeComInfo.addCache(fnaInvoiceType.getId().toString());
/* 138 */     } catch (Exception exception) {
/*     */       try {
/* 140 */         if (recordSetTrans != null) {
/* 141 */           boolean bool = Boolean.parseBoolean(Util.null2String((String)hashMap.get("needRollback"), "false"));
/* 142 */           if (bool) recordSetTrans.rollback(); 
/*     */         } 
/* 144 */       } catch (Exception exception1) {}
/* 145 */       throw exception;
/*     */     } 
/*     */     
/* 148 */     JSONObject jSONObject = new JSONObject();
/* 149 */     jSONObject.element("flag", true);
/* 150 */     paramJspWriter.print(jSONObject.toString());
/* 151 */     paramHttpServletResponse.flushBuffer();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void updateData(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse, JspWriter paramJspWriter, User paramUser) throws Exception {
/* 163 */     int i = paramUser.getLanguage();
/* 164 */     FnaBaseController fnaBaseController = FnaBaseController.getInstance();
/* 165 */     FnaInvoiceTypeBo fnaInvoiceTypeBo = FnaInvoiceTypeBo.getInstance();
/*     */     
/* 167 */     FnaInvoiceTypeVo fnaInvoiceTypeVo = (FnaInvoiceTypeVo)fnaBaseController.initVoFromHttpServletRequest(paramHttpServletRequest, FnaInvoiceTypeVo.class.getName());
/*     */     
/* 169 */     FnaInvoiceType fnaInvoiceType = new FnaInvoiceType();
/* 170 */     fnaBaseController.loadPoDataFromVo(fnaInvoiceTypeVo, fnaInvoiceType);
/*     */ 
/*     */     
/* 173 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 174 */     RecordSetTrans recordSetTrans = null;
/*     */     try {
/* 176 */       recordSetTrans = new RecordSetTrans();
/* 177 */       boolean bool = recordSetTrans.setAutoCommit(false);
/* 178 */       if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(i)));
/*     */       
/* 180 */       fnaInvoiceTypeBo.updateData(recordSetTrans, fnaInvoiceType, i, hashMap);
/*     */       
/* 182 */       bool = recordSetTrans.commit();
/* 183 */       if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(i)));
/*     */       
/* 185 */       FnaInvoiceTypeComInfo fnaInvoiceTypeComInfo = new FnaInvoiceTypeComInfo();
/* 186 */       fnaInvoiceTypeComInfo.updateCache(fnaInvoiceType.getId().toString());
/* 187 */     } catch (Exception exception) {
/*     */       try {
/* 189 */         if (recordSetTrans != null) {
/* 190 */           boolean bool = Boolean.parseBoolean(Util.null2String((String)hashMap.get("needRollback"), "false"));
/* 191 */           if (bool) recordSetTrans.rollback(); 
/*     */         } 
/* 193 */       } catch (Exception exception1) {}
/* 194 */       throw exception;
/*     */     } 
/*     */     
/* 197 */     JSONObject jSONObject = new JSONObject();
/* 198 */     jSONObject.element("flag", true);
/* 199 */     paramJspWriter.print(jSONObject.toString());
/* 200 */     paramHttpServletResponse.flushBuffer();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void deleteData(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse, JspWriter paramJspWriter, User paramUser) throws Exception {
/* 212 */     int i = paramUser.getLanguage();
/* 213 */     FnaInvoiceTypeBo fnaInvoiceTypeBo = FnaInvoiceTypeBo.getInstance();
/*     */     
/* 215 */     String str = Util.null2String(paramHttpServletRequest.getParameter("ids"));
/* 216 */     String[] arrayOfString = str.split(",");
/*     */     
/* 218 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 219 */     RecordSetTrans recordSetTrans = null;
/*     */     try {
/* 221 */       recordSetTrans = new RecordSetTrans();
/* 222 */       boolean bool = recordSetTrans.setAutoCommit(false);
/* 223 */       if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(i)));
/*     */       
/* 225 */       fnaInvoiceTypeBo.deleteData(recordSetTrans, arrayOfString, i, hashMap);
/*     */       
/* 227 */       bool = recordSetTrans.commit();
/* 228 */       if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(i)));
/*     */       
/* 230 */       FnaInvoiceTypeComInfo fnaInvoiceTypeComInfo = new FnaInvoiceTypeComInfo();
/* 231 */       int j = arrayOfString.length;
/* 232 */       for (byte b = 0; b < j; b++) {
/* 233 */         fnaInvoiceTypeComInfo.deleteCache(arrayOfString[b]);
/*     */       }
/* 235 */     } catch (Exception exception) {
/*     */       try {
/* 237 */         if (recordSetTrans != null) {
/* 238 */           boolean bool = Boolean.parseBoolean(Util.null2String((String)hashMap.get("needRollback"), "false"));
/* 239 */           if (bool) recordSetTrans.rollback(); 
/*     */         } 
/* 241 */       } catch (Exception exception1) {}
/* 242 */       throw exception;
/*     */     } 
/*     */     
/* 245 */     JSONObject jSONObject = new JSONObject();
/* 246 */     jSONObject.element("flag", true);
/* 247 */     paramJspWriter.print(jSONObject.toString());
/* 248 */     paramHttpServletResponse.flushBuffer();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void changeState(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse, JspWriter paramJspWriter, User paramUser) throws Exception {
/* 260 */     int i = paramUser.getLanguage();
/* 261 */     FnaInvoiceTypeBo fnaInvoiceTypeBo = FnaInvoiceTypeBo.getInstance();
/*     */     
/* 263 */     int j = Util.getIntValue(paramHttpServletRequest.getParameter("id"));
/* 264 */     int k = Util.getIntValue(paramHttpServletRequest.getParameter("state"));
/*     */     
/* 266 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 267 */     RecordSetTrans recordSetTrans = null;
/*     */     try {
/* 269 */       recordSetTrans = new RecordSetTrans();
/* 270 */       boolean bool = recordSetTrans.setAutoCommit(false);
/* 271 */       if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(i)));
/*     */       
/* 273 */       fnaInvoiceTypeBo.changeState(recordSetTrans, j, k, i, hashMap);
/*     */       
/* 275 */       bool = recordSetTrans.commit();
/* 276 */       if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(i)));
/*     */       
/* 278 */       FnaInvoiceTypeComInfo fnaInvoiceTypeComInfo = new FnaInvoiceTypeComInfo();
/* 279 */       fnaInvoiceTypeComInfo.updateCache(String.valueOf(j));
/* 280 */     } catch (Exception exception) {
/*     */       try {
/* 282 */         if (recordSetTrans != null) {
/* 283 */           boolean bool = Boolean.parseBoolean(Util.null2String((String)hashMap.get("needRollback"), "false"));
/* 284 */           if (bool) recordSetTrans.rollback(); 
/*     */         } 
/* 286 */       } catch (Exception exception1) {}
/* 287 */       throw exception;
/*     */     } 
/*     */     
/* 290 */     JSONObject jSONObject = new JSONObject();
/* 291 */     jSONObject.element("flag", true);
/* 292 */     paramJspWriter.print(jSONObject.toString());
/* 293 */     paramHttpServletResponse.flushBuffer();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/* 306 */   private static final FnaInvoiceTypeController thisClassObj = new FnaInvoiceTypeController();
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static FnaInvoiceTypeController getInstance() {
/* 312 */     return thisClassObj;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/fna/e9/controller/base/FnaInvoiceTypeController.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */