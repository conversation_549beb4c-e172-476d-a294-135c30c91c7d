/*     */ package weaver.fna.e9.controller.base;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.Arrays;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import javax.servlet.jsp.JspWriter;
/*     */ import net.sf.json.JSONObject;
/*     */ import weaver.fna.e9.bo.base.Common1BrowserBo;
/*     */ import weaver.fna.e9.bo.base.FnaBaseBo;
/*     */ import weaver.fna.e9.bo.base.FnaBrowsLastSearchResultBo;
/*     */ import weaver.fna.e9.po.base.FnaBrowsLastSearchResult;
/*     */ import weaver.fna.e9.vo.base.Common1BrowserVo;
/*     */ import weaver.fna.general.RecordSet4Action;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ @Deprecated
/*     */ public class Common1BrowserController
/*     */ {
/*  42 */   BaseBean bb = new BaseBean();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void saveSelectedInfo(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse, JspWriter paramJspWriter, User paramUser) throws Exception {
/*  53 */     String str1 = Util.null2String(paramHttpServletRequest.getParameter("browserType"));
/*  54 */     String str2 = Util.null2String(paramHttpServletRequest.getParameter("pkVal"));
/*     */     
/*  56 */     FnaBrowsLastSearchResultBo fnaBrowsLastSearchResultBo = FnaBrowsLastSearchResultBo.getInstance();
/*  57 */     fnaBrowsLastSearchResultBo.saveData(str1, str2, paramUser.getUID(), paramUser.getLanguage());
/*     */     
/*  59 */     JSONObject jSONObject = new JSONObject();
/*  60 */     jSONObject.element("flag", true);
/*     */     
/*  62 */     paramJspWriter.print(jSONObject.toString());
/*  63 */     paramHttpServletResponse.flushBuffer();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void loadTree(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse, JspWriter paramJspWriter, User paramUser) throws Exception {
/*  75 */     String str1 = Util.null2String(paramHttpServletRequest.getParameter("browserType"));
/*  76 */     String str2 = Util.null2String(paramHttpServletRequest.getParameter("fnaPeriodPk"));
/*  77 */     String str3 = Util.null2String(paramHttpServletRequest.getParameter("outPk"));
/*  78 */     String str4 = Util.null2String(paramHttpServletRequest.getParameter("id"));
/*     */     
/*  80 */     FnaBaseBo fnaBaseBo = FnaBaseBo.getInstance();
/*  81 */     FnaBrowsLastSearchResultBo fnaBrowsLastSearchResultBo = FnaBrowsLastSearchResultBo.getInstance();
/*     */     
/*  83 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*     */     
/*  85 */     RecordSet4Action recordSet4Action = new RecordSet4Action();
/*  86 */     List<FnaBrowsLastSearchResult> list = fnaBrowsLastSearchResultBo.loadTreeList(recordSet4Action, str1, str2, str3, str4, paramUser.getUID(), paramUser.getLanguage());
/*  87 */     int i = list.size();
/*  88 */     for (byte b = 0; b < i; b++) {
/*  89 */       HashMap<Object, Object> hashMap = new HashMap<>();
/*  90 */       arrayList.add(hashMap);
/*     */       
/*  92 */       FnaBrowsLastSearchResult fnaBrowsLastSearchResult = list.get(b);
/*     */       
/*  94 */       String str5 = fnaBrowsLastSearchResult.getCharPk();
/*  95 */       if (!fnaBaseBo.nonEmptyCheck(str5) && fnaBaseBo.nonNullCheck(fnaBrowsLastSearchResult.getIdPk())) {
/*  96 */         str5 = fnaBrowsLastSearchResult.getIdPk().intValue() + "";
/*     */       }
/*     */       
/*  99 */       String str6 = fnaBrowsLastSearchResult.getCharFk();
/* 100 */       if (!fnaBaseBo.nonEmptyCheck(str5) && fnaBaseBo.nonNullCheck(fnaBrowsLastSearchResult.getIdFk())) {
/* 101 */         str6 = fnaBrowsLastSearchResult.getIdFk().intValue() + "";
/*     */       }
/*     */       
/* 104 */       hashMap.put("id", str5);
/* 105 */       hashMap.put("name", fnaBrowsLastSearchResult.getShowName());
/* 106 */       hashMap.put("isParent", Boolean.valueOf(true));
/* 107 */       hashMap.put("fullShowName", fnaBrowsLastSearchResult.getFullShowName());
/*     */     } 
/*     */     
/* 110 */     JSONObject jSONObject = new JSONObject();
/* 111 */     jSONObject.element("treeNodeArray", arrayList.toArray());
/*     */     
/* 113 */     paramJspWriter.print(jSONObject.getJSONArray("treeNodeArray").toString());
/* 114 */     paramHttpServletResponse.flushBuffer();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void jsSourceSearch(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse, JspWriter paramJspWriter, User paramUser) throws Exception {
/* 126 */     String str1 = Util.null2String(paramHttpServletRequest.getParameter("browserType"));
/* 127 */     String str2 = Util.null2String(paramHttpServletRequest.getParameter("qryName"));
/* 128 */     String str3 = Util.null2String(paramHttpServletRequest.getParameter("fnaPeriodPk"));
/* 129 */     String str4 = Util.null2String(paramHttpServletRequest.getParameter("outPk"));
/*     */     
/* 131 */     FnaBaseBo fnaBaseBo = FnaBaseBo.getInstance();
/* 132 */     FnaBrowsLastSearchResultBo fnaBrowsLastSearchResultBo = FnaBrowsLastSearchResultBo.getInstance();
/*     */     
/* 134 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*     */     
/* 136 */     RecordSet4Action recordSet4Action = new RecordSet4Action();
/* 137 */     List<FnaBrowsLastSearchResult> list = fnaBrowsLastSearchResultBo.loadDataListByQryName(recordSet4Action, str1, str3, str4, str2, paramUser.getLanguage());
/* 138 */     int i = list.size();
/* 139 */     for (byte b = 0; b < i; b++) {
/* 140 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 141 */       arrayList.add(hashMap);
/*     */       
/* 143 */       FnaBrowsLastSearchResult fnaBrowsLastSearchResult = list.get(b);
/*     */       
/* 145 */       String str5 = fnaBrowsLastSearchResult.getCharPk();
/* 146 */       if (!fnaBaseBo.nonEmptyCheck(str5) && fnaBaseBo.nonNullCheck(fnaBrowsLastSearchResult.getIdPk())) {
/* 147 */         str5 = fnaBrowsLastSearchResult.getIdPk().intValue() + "";
/*     */       }
/*     */       
/* 150 */       String str6 = fnaBrowsLastSearchResult.getCharFk();
/* 151 */       if (!fnaBaseBo.nonEmptyCheck(str5) && fnaBaseBo.nonNullCheck(fnaBrowsLastSearchResult.getIdFk())) {
/* 152 */         str6 = fnaBrowsLastSearchResult.getIdFk().intValue() + "";
/*     */       }
/*     */       
/* 155 */       hashMap.put("id", str5);
/* 156 */       hashMap.put("showName", fnaBrowsLastSearchResult.getShowName());
/* 157 */       hashMap.put("fullShowName", fnaBrowsLastSearchResult.getFullShowName());
/*     */     } 
/*     */     
/* 160 */     JSONObject jSONObject = new JSONObject();
/* 161 */     jSONObject.element("flag", true);
/* 162 */     jSONObject.element("dataArray", arrayList.toArray());
/*     */     
/* 164 */     paramJspWriter.print(jSONObject.toString());
/* 165 */     paramHttpServletResponse.flushBuffer();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void loadDfData(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse, JspWriter paramJspWriter, User paramUser) throws Exception {
/* 177 */     String str1 = Util.null2String(paramHttpServletRequest.getParameter("browserType"));
/* 178 */     String str2 = Util.null2String(paramHttpServletRequest.getParameter("fnaPeriodPk"));
/* 179 */     String str3 = Util.null2String(paramHttpServletRequest.getParameter("outPk"));
/*     */     
/* 181 */     FnaBaseBo fnaBaseBo = FnaBaseBo.getInstance();
/* 182 */     FnaBrowsLastSearchResultBo fnaBrowsLastSearchResultBo = FnaBrowsLastSearchResultBo.getInstance();
/*     */     
/* 184 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*     */     
/* 186 */     RecordSet4Action recordSet4Action = new RecordSet4Action();
/* 187 */     List<FnaBrowsLastSearchResult> list = fnaBrowsLastSearchResultBo.loadDataList(recordSet4Action, str1, str2, str3, paramUser.getUID(), paramUser.getLanguage());
/* 188 */     int i = list.size();
/* 189 */     for (byte b = 0; b < i; b++) {
/* 190 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 191 */       arrayList.add(hashMap);
/*     */       
/* 193 */       FnaBrowsLastSearchResult fnaBrowsLastSearchResult = list.get(b);
/*     */       
/* 195 */       String str4 = fnaBrowsLastSearchResult.getCharPk();
/* 196 */       if (!fnaBaseBo.nonEmptyCheck(str4) && fnaBaseBo.nonNullCheck(fnaBrowsLastSearchResult.getIdPk())) {
/* 197 */         str4 = fnaBrowsLastSearchResult.getIdPk().intValue() + "";
/*     */       }
/*     */       
/* 200 */       String str5 = fnaBrowsLastSearchResult.getCharFk();
/* 201 */       if (!fnaBaseBo.nonEmptyCheck(str4) && fnaBaseBo.nonNullCheck(fnaBrowsLastSearchResult.getIdFk())) {
/* 202 */         str5 = fnaBrowsLastSearchResult.getIdFk().intValue() + "";
/*     */       }
/*     */       
/* 205 */       hashMap.put("id", str4);
/* 206 */       hashMap.put("showName", fnaBrowsLastSearchResult.getShowName());
/* 207 */       hashMap.put("fullShowName", fnaBrowsLastSearchResult.getFullShowName());
/*     */     } 
/*     */     
/* 210 */     JSONObject jSONObject = new JSONObject();
/* 211 */     jSONObject.element("flag", true);
/* 212 */     jSONObject.element("dataArray", arrayList.toArray());
/*     */     
/* 214 */     paramJspWriter.print(jSONObject.toString());
/* 215 */     paramHttpServletResponse.flushBuffer();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public Common1BrowserVo commonN1Browser(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse, User paramUser) throws Exception {
/* 228 */     FnaBaseController fnaBaseController = FnaBaseController.getInstance();
/* 229 */     Common1BrowserBo common1BrowserBo = Common1BrowserBo.getInstance();
/* 230 */     FnaBaseBo fnaBaseBo = FnaBaseBo.getInstance();
/*     */     
/* 232 */     Common1BrowserVo common1BrowserVo = (Common1BrowserVo)fnaBaseController.initVoFromHttpServletRequest(paramHttpServletRequest, Common1BrowserVo.class.getName());
/*     */     
/* 234 */     common1BrowserVo.setCanView(Boolean.valueOf(common1BrowserBo.validate(common1BrowserVo.getBrowserType(), paramUser)));
/*     */     
/* 236 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*     */     
/* 238 */     if (common1BrowserVo.getCanView().booleanValue()) {
/* 239 */       String str1 = common1BrowserVo.getBrowserType();
/* 240 */       String str2 = common1BrowserVo.getFnaPeriodPk();
/* 241 */       String str3 = common1BrowserVo.getOutPk();
/* 242 */       String str4 = common1BrowserVo.getSelectedPks();
/*     */       
/* 244 */       if ("fnaSubjectAccount".equals(str1)) {
/* 245 */         common1BrowserVo.setNavName(SystemEnv.getHtmlLabelName(127470, paramUser.getLanguage()));
/* 246 */       } else if ("fnaSubjectBudget".equals(str1)) {
/* 247 */         common1BrowserVo.setNavName(SystemEnv.getHtmlLabelName(1462, paramUser.getLanguage()));
/*     */       } 
/*     */       
/* 250 */       List<String> list = null;
/* 251 */       if (!"".equals(str4)) {
/* 252 */         String[] arrayOfString = str4.split(",");
/* 253 */         list = Arrays.asList(arrayOfString);
/*     */       } else {
/* 255 */         list = new ArrayList<>();
/*     */       } 
/*     */       
/* 258 */       FnaBrowsLastSearchResultBo fnaBrowsLastSearchResultBo = FnaBrowsLastSearchResultBo.getInstance();
/*     */       
/* 260 */       RecordSet4Action recordSet4Action = new RecordSet4Action();
/* 261 */       List<FnaBrowsLastSearchResult> list1 = fnaBrowsLastSearchResultBo.loadAllTreeList(recordSet4Action, str1, str2, str3, paramUser.getUID(), paramUser.getLanguage());
/* 262 */       int i = list1.size();
/* 263 */       for (byte b = 0; b < i; b++) {
/* 264 */         HashMap<Object, Object> hashMap = new HashMap<>();
/* 265 */         arrayList.add(hashMap);
/*     */         
/* 267 */         FnaBrowsLastSearchResult fnaBrowsLastSearchResult = list1.get(b);
/*     */         
/* 269 */         String str5 = fnaBrowsLastSearchResult.getCharPk();
/* 270 */         if (!fnaBaseBo.nonEmptyCheck(str5) && fnaBaseBo.nonNullCheck(fnaBrowsLastSearchResult.getIdPk())) {
/* 271 */           str5 = fnaBrowsLastSearchResult.getIdPk().intValue() + "";
/*     */         }
/*     */         
/* 274 */         String str6 = fnaBrowsLastSearchResult.getCharFk();
/* 275 */         if (!fnaBaseBo.nonEmptyCheck(str5) && fnaBaseBo.nonNullCheck(fnaBrowsLastSearchResult.getIdFk())) {
/* 276 */           str6 = fnaBrowsLastSearchResult.getIdFk().intValue() + "";
/*     */         }
/*     */         
/* 279 */         boolean bool = true;
/*     */         
/* 281 */         hashMap.put("id", str5);
/* 282 */         hashMap.put("name", fnaBrowsLastSearchResult.getShowName());
/* 283 */         hashMap.put("isParent", Boolean.valueOf(bool));
/* 284 */         hashMap.put("fullShowName", fnaBrowsLastSearchResult.getFullShowName());
/* 285 */         if (list.contains(str5)) {
/* 286 */           hashMap.put("checked", Boolean.valueOf(true));
/*     */         }
/* 288 */         hashMap.put("pId", str6);
/*     */       } 
/*     */     } 
/*     */ 
/*     */     
/* 293 */     JSONObject jSONObject = new JSONObject();
/* 294 */     jSONObject.element("treeNodeArray", arrayList.toArray());
/* 295 */     common1BrowserVo.setFnaWfTree_zNodes(jSONObject.getJSONArray("treeNodeArray").toString());
/*     */     
/* 297 */     return common1BrowserVo;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public Common1BrowserVo common1Browser(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse, User paramUser) throws Exception {
/* 310 */     FnaBaseController fnaBaseController = FnaBaseController.getInstance();
/* 311 */     Common1BrowserBo common1BrowserBo = Common1BrowserBo.getInstance();
/* 312 */     FnaBaseBo fnaBaseBo = FnaBaseBo.getInstance();
/*     */     
/* 314 */     Common1BrowserVo common1BrowserVo = (Common1BrowserVo)fnaBaseController.initVoFromHttpServletRequest(paramHttpServletRequest, Common1BrowserVo.class.getName());
/*     */     
/* 316 */     common1BrowserVo.setCanView(Boolean.valueOf(common1BrowserBo.validate(common1BrowserVo.getBrowserType(), paramUser)));
/*     */     
/* 318 */     if (common1BrowserVo.getCanView().booleanValue() && 
/* 319 */       !fnaBaseBo.nonEmptyCheck(common1BrowserVo.getTabId())) {
/* 320 */       common1BrowserVo.setTabId("1");
/*     */     }
/*     */ 
/*     */     
/* 324 */     return common1BrowserVo;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/* 338 */   private static final Common1BrowserController thisClassObj = new Common1BrowserController();
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public static Common1BrowserController getInstance() {
/* 345 */     return thisClassObj;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/fna/e9/controller/base/Common1BrowserController.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */