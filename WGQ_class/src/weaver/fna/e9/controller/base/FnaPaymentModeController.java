/*     */ package weaver.fna.e9.controller.base;
/*     */ 
/*     */ import java.util.HashMap;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import javax.servlet.jsp.JspWriter;
/*     */ import net.sf.json.JSONObject;
/*     */ import weaver.conn.RecordSetTrans;
/*     */ import weaver.fna.cache.FnaPaymentModeComInfo;
/*     */ import weaver.fna.e9.bo.base.FnaPaymentModeBo;
/*     */ import weaver.fna.e9.exception.FnaException;
/*     */ import weaver.fna.e9.po.base.FnaPaymentMode;
/*     */ import weaver.fna.e9.vo.base.FnaPaymentModeVo;
/*     */ import weaver.fna.general.RecordSet4Action;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class FnaPaymentModeController
/*     */ {
/*  38 */   BaseBean bb = new BaseBean();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public double queryMaxShowOrder(int paramInt) throws Exception {
/*  47 */     FnaPaymentModeBo fnaPaymentModeBo = FnaPaymentModeBo.getInstance();
/*  48 */     RecordSet4Action recordSet4Action = new RecordSet4Action();
/*  49 */     return fnaPaymentModeBo.queryMaxShowOrder(recordSet4Action, paramInt);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void save(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse, JspWriter paramJspWriter, User paramUser) throws Exception {
/*  61 */     int i = paramUser.getLanguage();
/*  62 */     FnaBaseController fnaBaseController = FnaBaseController.getInstance();
/*  63 */     FnaPaymentModeBo fnaPaymentModeBo = FnaPaymentModeBo.getInstance();
/*     */     
/*  65 */     FnaPaymentModeVo fnaPaymentModeVo = (FnaPaymentModeVo)fnaBaseController.initVoFromHttpServletRequest(paramHttpServletRequest, FnaPaymentModeVo.class.getName());
/*     */     
/*  67 */     FnaPaymentMode fnaPaymentMode = new FnaPaymentMode();
/*  68 */     fnaBaseController.loadPoDataFromVo(fnaPaymentModeVo, fnaPaymentMode);
/*     */ 
/*     */     
/*  71 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  72 */     RecordSetTrans recordSetTrans = null;
/*     */     try {
/*  74 */       recordSetTrans = new RecordSetTrans();
/*  75 */       boolean bool = recordSetTrans.setAutoCommit(false);
/*  76 */       if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(i)));
/*     */       
/*  78 */       if (fnaPaymentMode.getId().intValue() > 0) {
/*  79 */         fnaPaymentModeBo.updateData(recordSetTrans, fnaPaymentMode, i, hashMap);
/*     */       } else {
/*  81 */         fnaPaymentModeBo.createData(recordSetTrans, fnaPaymentMode, i, hashMap);
/*     */       } 
/*     */       
/*  84 */       bool = recordSetTrans.commit();
/*  85 */       if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(i)));
/*     */       
/*  87 */       FnaPaymentModeComInfo fnaPaymentModeComInfo = new FnaPaymentModeComInfo();
/*  88 */       fnaPaymentModeComInfo.addCache(fnaPaymentMode.getId().toString());
/*  89 */     } catch (Exception exception) {
/*     */       try {
/*  91 */         if (recordSetTrans != null) {
/*  92 */           boolean bool = Boolean.parseBoolean(Util.null2String((String)hashMap.get("needRollback"), "false"));
/*  93 */           if (bool) recordSetTrans.rollback(); 
/*     */         } 
/*  95 */       } catch (Exception exception1) {}
/*  96 */       throw exception;
/*     */     } 
/*     */     
/*  99 */     JSONObject jSONObject = new JSONObject();
/* 100 */     jSONObject.element("flag", true);
/* 101 */     paramJspWriter.print(jSONObject.toString());
/* 102 */     paramHttpServletResponse.flushBuffer();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void createData(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse, JspWriter paramJspWriter, User paramUser) throws Exception {
/* 114 */     int i = paramUser.getLanguage();
/* 115 */     FnaBaseController fnaBaseController = FnaBaseController.getInstance();
/* 116 */     FnaPaymentModeBo fnaPaymentModeBo = FnaPaymentModeBo.getInstance();
/*     */     
/* 118 */     FnaPaymentModeVo fnaPaymentModeVo = (FnaPaymentModeVo)fnaBaseController.initVoFromHttpServletRequest(paramHttpServletRequest, FnaPaymentModeVo.class.getName());
/*     */     
/* 120 */     FnaPaymentMode fnaPaymentMode = new FnaPaymentMode();
/* 121 */     fnaBaseController.loadPoDataFromVo(fnaPaymentModeVo, fnaPaymentMode);
/*     */ 
/*     */     
/* 124 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 125 */     RecordSetTrans recordSetTrans = null;
/*     */     try {
/* 127 */       recordSetTrans = new RecordSetTrans();
/* 128 */       boolean bool = recordSetTrans.setAutoCommit(false);
/* 129 */       if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(i)));
/*     */       
/* 131 */       fnaPaymentModeBo.createData(recordSetTrans, fnaPaymentMode, i, hashMap);
/*     */       
/* 133 */       bool = recordSetTrans.commit();
/* 134 */       if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(i)));
/*     */       
/* 136 */       FnaPaymentModeComInfo fnaPaymentModeComInfo = new FnaPaymentModeComInfo();
/* 137 */       fnaPaymentModeComInfo.addCache(fnaPaymentMode.getId().toString());
/* 138 */     } catch (Exception exception) {
/*     */       try {
/* 140 */         if (recordSetTrans != null) {
/* 141 */           boolean bool = Boolean.parseBoolean(Util.null2String((String)hashMap.get("needRollback"), "false"));
/* 142 */           if (bool) recordSetTrans.rollback(); 
/*     */         } 
/* 144 */       } catch (Exception exception1) {}
/* 145 */       throw exception;
/*     */     } 
/*     */     
/* 148 */     JSONObject jSONObject = new JSONObject();
/* 149 */     jSONObject.element("flag", true);
/* 150 */     paramJspWriter.print(jSONObject.toString());
/* 151 */     paramHttpServletResponse.flushBuffer();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void updateData(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse, JspWriter paramJspWriter, User paramUser) throws Exception {
/* 163 */     int i = paramUser.getLanguage();
/* 164 */     FnaBaseController fnaBaseController = FnaBaseController.getInstance();
/* 165 */     FnaPaymentModeBo fnaPaymentModeBo = FnaPaymentModeBo.getInstance();
/*     */     
/* 167 */     FnaPaymentModeVo fnaPaymentModeVo = (FnaPaymentModeVo)fnaBaseController.initVoFromHttpServletRequest(paramHttpServletRequest, FnaPaymentModeVo.class.getName());
/*     */     
/* 169 */     FnaPaymentMode fnaPaymentMode = new FnaPaymentMode();
/* 170 */     fnaBaseController.loadPoDataFromVo(fnaPaymentModeVo, fnaPaymentMode);
/*     */ 
/*     */     
/* 173 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 174 */     RecordSetTrans recordSetTrans = null;
/*     */     try {
/* 176 */       recordSetTrans = new RecordSetTrans();
/* 177 */       boolean bool = recordSetTrans.setAutoCommit(false);
/* 178 */       if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(i)));
/*     */       
/* 180 */       fnaPaymentModeBo.updateData(recordSetTrans, fnaPaymentMode, i, hashMap);
/*     */       
/* 182 */       bool = recordSetTrans.commit();
/* 183 */       if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(i)));
/*     */       
/* 185 */       FnaPaymentModeComInfo fnaPaymentModeComInfo = new FnaPaymentModeComInfo();
/* 186 */       fnaPaymentModeComInfo.updateCache(fnaPaymentMode.getId().toString());
/* 187 */     } catch (Exception exception) {
/*     */       try {
/* 189 */         if (recordSetTrans != null) {
/* 190 */           boolean bool = Boolean.parseBoolean(Util.null2String((String)hashMap.get("needRollback"), "false"));
/* 191 */           if (bool) recordSetTrans.rollback(); 
/*     */         } 
/* 193 */       } catch (Exception exception1) {}
/* 194 */       throw exception;
/*     */     } 
/*     */     
/* 197 */     JSONObject jSONObject = new JSONObject();
/* 198 */     jSONObject.element("flag", true);
/* 199 */     paramJspWriter.print(jSONObject.toString());
/* 200 */     paramHttpServletResponse.flushBuffer();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void deleteData(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse, JspWriter paramJspWriter, User paramUser) throws Exception {
/* 212 */     int i = paramUser.getLanguage();
/* 213 */     FnaPaymentModeBo fnaPaymentModeBo = FnaPaymentModeBo.getInstance();
/*     */     
/* 215 */     String str = Util.null2String(paramHttpServletRequest.getParameter("ids"));
/* 216 */     String[] arrayOfString = str.split(",");
/*     */     
/* 218 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 219 */     RecordSetTrans recordSetTrans = null;
/*     */     try {
/* 221 */       recordSetTrans = new RecordSetTrans();
/* 222 */       boolean bool = recordSetTrans.setAutoCommit(false);
/* 223 */       if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(i)));
/*     */       
/* 225 */       fnaPaymentModeBo.deleteData(recordSetTrans, arrayOfString, i, hashMap);
/*     */       
/* 227 */       bool = recordSetTrans.commit();
/* 228 */       if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(i)));
/*     */       
/* 230 */       FnaPaymentModeComInfo fnaPaymentModeComInfo = new FnaPaymentModeComInfo();
/* 231 */       int j = arrayOfString.length;
/* 232 */       for (byte b = 0; b < j; b++) {
/* 233 */         fnaPaymentModeComInfo.deleteCache(arrayOfString[b]);
/*     */       }
/* 235 */     } catch (Exception exception) {
/*     */       try {
/* 237 */         if (recordSetTrans != null) {
/* 238 */           boolean bool = Boolean.parseBoolean(Util.null2String((String)hashMap.get("needRollback"), "false"));
/* 239 */           if (bool) recordSetTrans.rollback(); 
/*     */         } 
/* 241 */       } catch (Exception exception1) {}
/* 242 */       throw exception;
/*     */     } 
/*     */     
/* 245 */     JSONObject jSONObject = new JSONObject();
/* 246 */     jSONObject.element("flag", true);
/* 247 */     paramJspWriter.print(jSONObject.toString());
/* 248 */     paramHttpServletResponse.flushBuffer();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void changeState(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse, JspWriter paramJspWriter, User paramUser) throws Exception {
/* 260 */     int i = paramUser.getLanguage();
/* 261 */     FnaPaymentModeBo fnaPaymentModeBo = FnaPaymentModeBo.getInstance();
/*     */     
/* 263 */     int j = Util.getIntValue(paramHttpServletRequest.getParameter("id"));
/* 264 */     int k = Util.getIntValue(paramHttpServletRequest.getParameter("state"));
/*     */     
/* 266 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 267 */     RecordSetTrans recordSetTrans = null;
/*     */     try {
/* 269 */       recordSetTrans = new RecordSetTrans();
/* 270 */       boolean bool = recordSetTrans.setAutoCommit(false);
/* 271 */       if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(i)));
/*     */       
/* 273 */       fnaPaymentModeBo.changeState(recordSetTrans, j, k, i, hashMap);
/*     */       
/* 275 */       bool = recordSetTrans.commit();
/* 276 */       if (!bool) throw new FnaException(SystemEnv.getHtmlLabelName(10000018, Util.getIntValue(i)));
/*     */       
/* 278 */       FnaPaymentModeComInfo fnaPaymentModeComInfo = new FnaPaymentModeComInfo();
/* 279 */       fnaPaymentModeComInfo.updateCache(String.valueOf(j));
/* 280 */     } catch (Exception exception) {
/*     */       try {
/* 282 */         if (recordSetTrans != null) {
/* 283 */           boolean bool = Boolean.parseBoolean(Util.null2String((String)hashMap.get("needRollback"), "false"));
/* 284 */           if (bool) recordSetTrans.rollback(); 
/*     */         } 
/* 286 */       } catch (Exception exception1) {}
/* 287 */       throw exception;
/*     */     } 
/*     */     
/* 290 */     JSONObject jSONObject = new JSONObject();
/* 291 */     jSONObject.element("flag", true);
/* 292 */     paramJspWriter.print(jSONObject.toString());
/* 293 */     paramHttpServletResponse.flushBuffer();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/* 306 */   private static final FnaPaymentModeController thisClassObj = new FnaPaymentModeController();
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static FnaPaymentModeController getInstance() {
/* 312 */     return thisClassObj;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/fna/e9/controller/base/FnaPaymentModeController.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */