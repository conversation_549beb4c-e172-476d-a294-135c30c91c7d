/*     */ package weaver.fna.e9.controller.base;
/*     */ 
/*     */ import com.esotericsoftware.reflectasm.MethodAccess;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import net.sf.json.JSONArray;
/*     */ import net.sf.json.JSONObject;
/*     */ import weaver.file.FileUpload;
/*     */ import weaver.fna.cache.MethodAccessCache;
/*     */ import weaver.fna.e9.exception.FnaException;
/*     */ import weaver.fna.e9.po.base.FnaBasePo;
/*     */ import weaver.fna.e9.po.base.FnaBasePoField;
/*     */ import weaver.fna.e9.vo.annotation.PageFieldInfo;
/*     */ import weaver.fna.e9.vo.base.FnaBaseVo;
/*     */ import weaver.fna.e9.vo.base.FnaBaseVoField;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class FnaBaseController
/*     */ {
/*  41 */   BaseBean bb = new BaseBean();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean loadPoDataFromComInfo(String paramString, Object paramObject1, Object paramObject2) throws Exception {
/*  58 */     MethodAccess methodAccess = MethodAccessCache.getMethodAccessByClass(paramObject1.getClass());
/*  59 */     boolean bool = ((Boolean)methodAccess.invoke(paramObject1, "containsId", new Object[] { paramString })).booleanValue();
/*  60 */     if (bool) {
/*  61 */       String str = paramObject2.getClass().getName();
/*  62 */       FnaBasePo fnaBasePo = FnaBasePo.GET_PO_CLASS_FIELD_INFO(str);
/*  63 */       MethodAccess methodAccess1 = MethodAccessCache.getMethodAccessByClass(fnaBasePo.getClazz());
/*     */       
/*  65 */       for (byte b = 0; b < fnaBasePo.getFieldsLen(); b++) {
/*     */         try {
/*  67 */           FnaBasePoField fnaBasePoField = fnaBasePo.getFnaBasePoFieldList().get(b);
/*  68 */           String str1 = captureName(fnaBasePoField.getJavaFieldName());
/*  69 */           String str2 = "get" + str1 + "_4MethodAccess";
/*  70 */           String str3 = "set" + str1;
/*     */           
/*  72 */           Object object = methodAccess.invoke(paramObject1, str2, new Object[] { paramString });
/*  73 */           if (object != null) {
/*  74 */             String str4 = fnaBasePoField.getJavaType();
/*  75 */             if (str4.endsWith("string")) {
/*  76 */               methodAccess1.invoke(paramObject2, str3, new Object[] { object });
/*  77 */             } else if (str4.endsWith("int") || str4.endsWith("integer")) {
/*  78 */               if (!"".equals(object)) {
/*     */                 try {
/*  80 */                   Integer integer = Integer.valueOf(Integer.parseInt((String)object));
/*  81 */                   methodAccess1.invoke(paramObject2, str3, new Object[] { integer });
/*  82 */                 } catch (Exception exception) {}
/*     */               }
/*  84 */             } else if (str4.endsWith("double")) {
/*  85 */               if (!"".equals(object)) {
/*     */                 try {
/*  87 */                   Double double_ = Double.valueOf(Double.parseDouble((String)object));
/*  88 */                   methodAccess1.invoke(paramObject2, str3, new Object[] { double_ });
/*  89 */                 } catch (Exception exception) {}
/*     */               }
/*     */             } else {
/*  92 */               throw new FnaException("试图读取和设置(" + str3 + ")，系统未处理的java属性类型(" + str4 + ")");
/*     */             } 
/*     */           } 
/*  95 */         } catch (Exception exception) {}
/*     */       } 
/*  97 */       return true;
/*     */     } 
/*  99 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void loadVoDataFromPo(Object paramObject1, Object paramObject2) throws Exception {
/* 110 */     FnaBaseVo fnaBaseVo = FnaBaseVo.GET_VO_CLASS_FIELD_INFO(paramObject2.getClass().getName());
/* 111 */     MethodAccess methodAccess1 = MethodAccessCache.getMethodAccessByClass(fnaBaseVo.getClazz());
/* 112 */     String str = paramObject1.getClass().getName();
/* 113 */     FnaBasePo fnaBasePo = FnaBasePo.GET_PO_CLASS_FIELD_INFO(str);
/* 114 */     MethodAccess methodAccess2 = MethodAccessCache.getMethodAccessByClass(fnaBasePo.getClazz());
/*     */     
/* 116 */     for (byte b = 0; b < fnaBaseVo.getFieldsLen(); b++) {
/* 117 */       FnaBaseVoField fnaBaseVoField = fnaBaseVo.getFnaBaseVoFieldList().get(b);
/* 118 */       if (str.equals(fnaBaseVoField.getPoClassName())) {
/* 119 */         String str1 = "set" + captureName(fnaBaseVoField.getJavaFieldName());
/* 120 */         String str2 = "get" + captureName(fnaBaseVoField.getPoPropertyName());
/*     */         
/* 122 */         Object object = methodAccess2.invoke(paramObject1, str2, new Object[0]);
/* 123 */         methodAccess1.invoke(paramObject2, str1, new Object[] { object });
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void loadPoDataFromVo(Object paramObject1, Object paramObject2) throws Exception {
/* 135 */     FnaBaseVo fnaBaseVo = FnaBaseVo.GET_VO_CLASS_FIELD_INFO(paramObject1.getClass().getName());
/* 136 */     MethodAccess methodAccess1 = MethodAccessCache.getMethodAccessByClass(fnaBaseVo.getClazz());
/* 137 */     String str = paramObject2.getClass().getName();
/* 138 */     FnaBasePo fnaBasePo = FnaBasePo.GET_PO_CLASS_FIELD_INFO(str);
/* 139 */     MethodAccess methodAccess2 = MethodAccessCache.getMethodAccessByClass(fnaBasePo.getClazz());
/*     */     
/* 141 */     for (byte b = 0; b < fnaBaseVo.getFieldsLen(); b++) {
/* 142 */       FnaBaseVoField fnaBaseVoField = fnaBaseVo.getFnaBaseVoFieldList().get(b);
/* 143 */       if (str.equals(fnaBaseVoField.getPoClassName())) {
/* 144 */         String str1 = "get" + captureName(fnaBaseVoField.getJavaFieldName());
/* 145 */         String str2 = "set" + captureName(fnaBaseVoField.getPoPropertyName());
/*     */         
/* 147 */         Object object = methodAccess1.invoke(paramObject1, str1, new Object[0]);
/*     */         
/* 149 */         methodAccess2.invoke(paramObject2, str2, new Object[] { object });
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void loadPoDataFromVo(Object paramObject, List<Object> paramList) throws Exception {
/* 161 */     FnaBaseVo fnaBaseVo = FnaBaseVo.GET_VO_CLASS_FIELD_INFO(paramObject.getClass().getName());
/* 162 */     MethodAccess methodAccess = MethodAccessCache.getMethodAccessByClass(fnaBaseVo.getClazz());
/*     */     
/* 164 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 165 */     int i = paramList.size(); byte b;
/* 166 */     for (b = 0; b < i; b++) {
/* 167 */       Object object = paramList.get(b);
/* 168 */       String str = object.getClass().getName();
/* 169 */       FnaBasePo fnaBasePo = FnaBasePo.GET_PO_CLASS_FIELD_INFO(str);
/* 170 */       MethodAccess methodAccess1 = MethodAccessCache.getMethodAccessByClass(fnaBasePo.getClazz());
/* 171 */       hashMap.put(str, new Object[] { object, fnaBasePo, methodAccess1 });
/*     */     } 
/*     */     
/* 174 */     for (b = 0; b < fnaBaseVo.getFieldsLen(); b++) {
/* 175 */       FnaBaseVoField fnaBaseVoField = fnaBaseVo.getFnaBaseVoFieldList().get(b);
/* 176 */       if (hashMap.containsKey(fnaBaseVoField.getPoClassName())) {
/* 177 */         String str1 = "get" + captureName(fnaBaseVoField.getJavaFieldName());
/* 178 */         String str2 = "set" + captureName(fnaBaseVoField.getPoPropertyName());
/*     */         
/* 180 */         Object object1 = methodAccess.invoke(paramObject, str1, new Object[0]);
/*     */         
/* 182 */         Object[] arrayOfObject = (Object[])hashMap.get(fnaBaseVoField.getPoClassName());
/* 183 */         Object object2 = arrayOfObject[0];
/* 184 */         MethodAccess methodAccess1 = (MethodAccess)arrayOfObject[2];
/* 185 */         methodAccess1.invoke(object2, str2, new Object[] { object1 });
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setVoObjectDefaultValue(Object paramObject) throws Exception {
/* 196 */     String str = paramObject.getClass().getName();
/* 197 */     FnaBaseVo fnaBaseVo = FnaBaseVo.GET_VO_CLASS_FIELD_INFO(str);
/* 198 */     MethodAccess methodAccess = MethodAccessCache.getMethodAccessByClass(fnaBaseVo.getClazz());
/*     */ 
/*     */     
/* 201 */     for (byte b = 0; b < fnaBaseVo.getFieldsLen(); b++) {
/* 202 */       FnaBaseVoField fnaBaseVoField = fnaBaseVo.getFnaBaseVoFieldList().get(b);
/* 203 */       if (fnaBaseVoField.isPostParam() && 
/* 204 */         !fnaBaseVoField.isDynamicQuantityPostParam())
/*     */       {
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */         
/* 213 */         if (fnaBaseVoField.getReadValueMode() == PageFieldInfo.ReadValueMode.REQUEST)
/*     */         {
/* 215 */           if (!fnaBaseVoField.isArray()) {
/*     */             
/* 217 */             String str1 = fnaBaseVoField.getJavaType().toLowerCase();
/* 218 */             String str2 = "set" + captureName(fnaBaseVoField.getJavaFieldName());
/* 219 */             String str3 = "get" + captureName(fnaBaseVoField.getJavaFieldName());
/*     */             
/* 221 */             Object object = methodAccess.invoke(paramObject, str3, new Object[0]);
/*     */ 
/*     */             
/* 224 */             if (str1.endsWith("string")) {
/*     */               
/* 226 */               if (object == null && fnaBaseVoField.isEnableIsNullDefaultValue() && fnaBaseVoField.getIsNullDefaultValue() != null) {
/* 227 */                 String str4 = fnaBaseVoField.getIsNullDefaultValue();
/*     */                 
/* 229 */                 methodAccess.invoke(paramObject, str2, new Object[] { str4 });
/*     */               }
/* 231 */               else if (fnaBaseVoField.isAutoTrim()) {
/* 232 */                 object = Util.null2String(object).trim();
/*     */                 
/* 234 */                 methodAccess.invoke(paramObject, str2, new Object[] { object });
/*     */               }
/*     */             
/* 237 */             } else if (str1.endsWith("int") || str1.endsWith("integer")) {
/*     */               
/* 239 */               if (object == null && fnaBaseVoField.isEnableIsNullDefaultValue() && fnaBaseVoField.getIsNullDefaultValue() != null) {
/* 240 */                 Integer integer = Integer.valueOf(Util.getIntValue(fnaBaseVoField.getIsNullDefaultValue(), 0));
/*     */                 
/* 242 */                 methodAccess.invoke(paramObject, str2, new Object[] { integer });
/*     */               } 
/* 244 */             } else if (str1.endsWith("double")) {
/*     */               
/* 246 */               if (object == null && fnaBaseVoField.isEnableIsNullDefaultValue() && fnaBaseVoField.getIsNullDefaultValue() != null) {
/* 247 */                 Double double_ = Double.valueOf(Util.getDoubleValue(fnaBaseVoField.getIsNullDefaultValue(), 0.0D));
/*     */                 
/* 249 */                 methodAccess.invoke(paramObject, str2, new Object[] { double_ });
/*     */               } 
/*     */             } else {
/* 252 */               throw new FnaException("试图读取和设置(" + str2 + ")，系统未处理的java属性类型(" + str1 + ")");
/*     */             } 
/*     */           } 
/*     */         }
/*     */       }
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Object initVoFromJSONObject(JSONObject paramJSONObject, String paramString) throws Exception {
/* 268 */     return initVoFromJSONObject(paramJSONObject, "", paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Object initVoFromJSONObject(JSONObject paramJSONObject, String paramString1, String paramString2) throws Exception {
/* 280 */     return initVo(paramJSONObject, null, null, paramString1, paramString2);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Object initVoFromHttpServletRequest(HttpServletRequest paramHttpServletRequest, String paramString) throws Exception {
/* 291 */     return initVoFromHttpServletRequest(paramHttpServletRequest, "", paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Object initVoFromHttpServletRequest(HttpServletRequest paramHttpServletRequest, String paramString1, String paramString2) throws Exception {
/* 303 */     return initVo(null, paramHttpServletRequest, null, paramString1, paramString2);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Object initVoFromHttpServletRequest(HttpServletRequest paramHttpServletRequest, FileUpload paramFileUpload, String paramString) throws Exception {
/* 315 */     return initVo(null, paramHttpServletRequest, paramFileUpload, "", paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private Object initVo(JSONObject paramJSONObject, HttpServletRequest paramHttpServletRequest, FileUpload paramFileUpload, String paramString1, String paramString2) throws Exception {
/* 331 */     FnaBaseVo fnaBaseVo = FnaBaseVo.GET_VO_CLASS_FIELD_INFO(paramString2);
/* 332 */     Object object = fnaBaseVo.getClazz().newInstance();
/* 333 */     MethodAccess methodAccess = MethodAccessCache.getMethodAccessByClass(fnaBaseVo.getClazz());
/*     */     
/* 335 */     for (byte b = 0; b < fnaBaseVo.getFieldsLen(); b++) {
/* 336 */       FnaBaseVoField fnaBaseVoField = fnaBaseVo.getFnaBaseVoFieldList().get(b);
/* 337 */       if (fnaBaseVoField.isPostParam() && (
/* 338 */         !fnaBaseVoField.isDynamicQuantityPostParam() || (!"".equals(paramString1) && paramString1 != null))) {
/*     */ 
/*     */         
/* 341 */         String str1 = fnaBaseVoField.getJavaType().toLowerCase();
/* 342 */         String str2 = "set" + captureName(fnaBaseVoField.getJavaFieldName());
/* 343 */         String str3 = fnaBaseVoField.getName() + paramString1;
/*     */         
/* 345 */         if (fnaBaseVoField.getReadValueMode() == PageFieldInfo.ReadValueMode.REQUEST) {
/* 346 */           if (fnaBaseVoField.isArray()) {
/* 347 */             String[] arrayOfString = null;
/* 348 */             if (paramJSONObject != null) {
/* 349 */               JSONArray jSONArray = paramJSONObject.getJSONArray(str3);
/* 350 */               if (jSONArray != null) {
/* 351 */                 int i = jSONArray.size();
/* 352 */                 arrayOfString = new String[i];
/* 353 */                 for (byte b1 = 0; b1 < i; b1++) {
/* 354 */                   arrayOfString[b1] = jSONArray.getString(b1);
/*     */                 }
/*     */               }
/*     */             
/* 358 */             } else if (fnaBaseVoField.isMultipartFormData() && paramFileUpload != null) {
/* 359 */               arrayOfString = paramFileUpload.getParameterValues(str3);
/*     */             } else {
/* 361 */               arrayOfString = paramHttpServletRequest.getParameterValues(str3);
/*     */             } 
/*     */             
/* 364 */             if (arrayOfString != null) {
/* 365 */               int i = arrayOfString.length;
/* 366 */               Object[] arrayOfObject = new Object[i];
/* 367 */               for (byte b1 = 0; b1 < i; b1++) {
/* 368 */                 String str = arrayOfString[b1];
/* 369 */                 if (fnaBaseVoField.isAutoTrim()) {
/* 370 */                   str = Util.null2String(str).trim();
/*     */                 }
/* 372 */                 if (str1.endsWith("string")) {
/* 373 */                   if (str == null && fnaBaseVoField.isEnableIsNullDefaultValue() && fnaBaseVoField.getIsNullDefaultValue() != null) {
/* 374 */                     arrayOfObject[b1] = fnaBaseVoField.getIsNullDefaultValue();
/*     */                   } else {
/* 376 */                     arrayOfObject[b1] = str;
/*     */                   } 
/* 378 */                 } else if (str1.endsWith("int") || str1.endsWith("integer")) {
/* 379 */                   Integer integer = (str == null || "".equals(str)) ? null : Integer.valueOf(Util.getIntValue(str, 0));
/* 380 */                   if (integer == null && fnaBaseVoField.isEnableIsNullDefaultValue() && fnaBaseVoField.getIsNullDefaultValue() != null) {
/* 381 */                     arrayOfObject[b1] = Integer.valueOf(Util.getIntValue(fnaBaseVoField.getIsNullDefaultValue(), 0));
/*     */                   } else {
/* 383 */                     arrayOfObject[b1] = integer;
/*     */                   } 
/* 385 */                 } else if (str1.endsWith("double")) {
/* 386 */                   Double double_ = (str == null || "".equals(str)) ? null : Double.valueOf(Util.getDoubleValue(str, 0.0D));
/* 387 */                   if (double_ == null && fnaBaseVoField.isEnableIsNullDefaultValue() && fnaBaseVoField.getIsNullDefaultValue() != null) {
/* 388 */                     arrayOfObject[b1] = Double.valueOf(Util.getDoubleValue(fnaBaseVoField.getIsNullDefaultValue(), 0.0D));
/*     */                   } else {
/* 390 */                     arrayOfObject[b1] = double_;
/*     */                   } 
/*     */                 } else {
/* 393 */                   throw new FnaException("试图从HttpServletRequest读取(" + str2 + ")未处理的java属性类型(" + str1 + ")");
/*     */                 } 
/*     */               } 
/* 396 */               methodAccess.invoke(object, str2, arrayOfObject);
/*     */             }
/*     */           
/*     */           }
/* 400 */           else if (paramJSONObject != null) {
/* 401 */             Object object1 = paramJSONObject.get(str3);
/* 402 */             invokeSetValueByMa(methodAccess, object, str2, object1, false, fnaBaseVoField
/* 403 */                 .isEnableIsNullDefaultValue(), fnaBaseVoField.getIsNullDefaultValue(), fnaBaseVoField.isAutoTrim(), str1);
/*     */           } else {
/* 405 */             String str = null;
/* 406 */             if (fnaBaseVoField.isMultipartFormData() && paramFileUpload != null) {
/* 407 */               str = paramFileUpload.getParameter(str3);
/*     */             } else {
/* 409 */               str = paramHttpServletRequest.getParameter(str3);
/*     */             } 
/* 411 */             invokeSetValueByMa(methodAccess, object, str2, str, true, fnaBaseVoField
/* 412 */                 .isEnableIsNullDefaultValue(), fnaBaseVoField.getIsNullDefaultValue(), fnaBaseVoField.isAutoTrim(), str1);
/*     */           }
/*     */         
/*     */         }
/* 416 */         else if (fnaBaseVoField.getReadValueMode() == PageFieldInfo.ReadValueMode.SESSION) {
/* 417 */           methodAccess.invoke(object, str2, new Object[] { paramHttpServletRequest.getSession().getAttribute(str3) });
/*     */         } 
/*     */       } 
/*     */     } 
/*     */ 
/*     */     
/* 423 */     return object;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void invokeSetValueByMa(MethodAccess paramMethodAccess, Object paramObject1, String paramString1, Object paramObject2, boolean paramBoolean1, boolean paramBoolean2, String paramString2, boolean paramBoolean3, String paramString3) throws FnaException {
/* 444 */     if (paramBoolean1) {
/* 445 */       String str = (String)paramObject2;
/* 446 */       if (paramString3.endsWith("string")) {
/* 447 */         if (paramBoolean3) {
/* 448 */           str = Util.null2String(str).trim();
/*     */         }
/* 450 */         if (str == null && paramBoolean2 && paramString2 != null) {
/* 451 */           str = paramString2;
/*     */         }
/* 453 */         paramMethodAccess.invoke(paramObject1, paramString1, new Object[] { str });
/* 454 */       } else if (paramString3.endsWith("int") || paramString3.endsWith("integer")) {
/* 455 */         Integer integer = (str == null || "".equals(str)) ? null : Integer.valueOf(Util.getIntValue(str, 0));
/* 456 */         if (integer == null && paramBoolean2 && paramString2 != null) {
/* 457 */           integer = Integer.valueOf(Util.getIntValue(paramString2, 0));
/*     */         }
/* 459 */         paramMethodAccess.invoke(paramObject1, paramString1, new Object[] { integer });
/* 460 */       } else if (paramString3.endsWith("double")) {
/* 461 */         Double double_ = (str == null || "".equals(str)) ? null : Double.valueOf(Util.getDoubleValue(str, 0.0D));
/* 462 */         if (double_ == null && paramBoolean2 && paramString2 != null) {
/* 463 */           double_ = Double.valueOf(Util.getDoubleValue(paramString2, 0.0D));
/*     */         }
/* 465 */         paramMethodAccess.invoke(paramObject1, paramString1, new Object[] { double_ });
/*     */       } else {
/* 467 */         throw new FnaException("试图从HttpServletRequest读取(" + paramString1 + ")未处理的java属性类型(" + paramString3 + ")");
/*     */       }
/*     */     
/* 470 */     } else if (paramString3.endsWith("string")) {
/* 471 */       String str = (String)paramObject2;
/* 472 */       if (paramBoolean3) {
/* 473 */         str = Util.null2String(str).trim();
/*     */       }
/* 475 */       if (str == null && paramBoolean2 && paramString2 != null) {
/* 476 */         str = paramString2;
/*     */       }
/* 478 */       paramMethodAccess.invoke(paramObject1, paramString1, new Object[] { str });
/* 479 */     } else if (paramString3.endsWith("int") || paramString3.endsWith("integer")) {
/* 480 */       Integer integer = (paramObject2 == null) ? null : (Integer)paramObject2;
/* 481 */       if (integer == null && paramBoolean2 && paramString2 != null) {
/* 482 */         integer = Integer.valueOf(Util.getIntValue(paramString2, 0));
/*     */       }
/* 484 */       paramMethodAccess.invoke(paramObject1, paramString1, new Object[] { integer });
/* 485 */     } else if (paramString3.endsWith("double")) {
/* 486 */       Double double_ = (paramObject2 == null) ? null : (Double)paramObject2;
/* 487 */       if (double_ == null && paramBoolean2 && paramString2 != null) {
/* 488 */         double_ = Double.valueOf(Util.getDoubleValue(paramString2, 0.0D));
/*     */       }
/* 490 */       paramMethodAccess.invoke(paramObject1, paramString1, new Object[] { double_ });
/*     */     } else {
/* 492 */       throw new FnaException("试图从HttpServletRequest读取(" + paramString1 + ")未处理的java属性类型(" + paramString3 + ")");
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String captureName(String paramString) {
/* 504 */     if (paramString != null && !"".equals(paramString)) {
/* 505 */       char[] arrayOfChar = paramString.toCharArray();
/* 506 */       arrayOfChar[0] = (arrayOfChar[0] + "").toUpperCase().toCharArray()[0];
/* 507 */       return String.valueOf(arrayOfChar);
/*     */     } 
/* 509 */     return paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/* 520 */   private static final FnaBaseController thisClassObj = new FnaBaseController();
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static FnaBaseController getInstance() {
/* 526 */     return thisClassObj;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/fna/e9/controller/base/FnaBaseController.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */