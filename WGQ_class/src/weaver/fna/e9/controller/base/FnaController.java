/*    */ package weaver.fna.e9.controller.base;
/*    */ 
/*    */ import java.util.HashMap;
/*    */ import java.util.List;
/*    */ import org.apache.commons.lang.StringEscapeUtils;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class FnaController
/*    */ {
/*    */   public String getCommonCaseWhenSqlByListHashMap(List<HashMap<String, Integer>> paramList, String paramString1, String paramString2, int paramInt) {
/* 31 */     StringBuffer stringBuffer = new StringBuffer();
/* 32 */     int i = paramList.size();
/* 33 */     if (i > 0) {
/* 34 */       stringBuffer.append("case ");
/* 35 */       for (byte b = 0; b < i; b++) {
/* 36 */         HashMap hashMap = paramList.get(b);
/* 37 */         int j = ((Integer)hashMap.get("value")).intValue();
/* 38 */         int k = ((Integer)hashMap.get("labelId")).intValue();
/* 39 */         String str = "";
/* 40 */         if (k != 0) {
/* 41 */           str = SystemEnv.getHtmlLabelName(k, paramInt);
/*    */         }
/* 43 */         stringBuffer.append("when ").append(paramString2).append("=").append(j)
/* 44 */           .append(" then '").append(StringEscapeUtils.escapeSql(str)).append("' ");
/*    */       } 
/* 46 */       stringBuffer.append("end ").append(paramString1);
/*    */     } 
/* 48 */     return stringBuffer.toString();
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/fna/e9/controller/base/FnaController.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */