/*    */ package weaver.fna.e9.exception;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class FnaSynchronizedException
/*    */   extends Exception
/*    */ {
/*    */   private static final long serialVersionUID = 2017091509170001L;
/*    */   private String content;
/*    */   
/*    */   public FnaSynchronizedException(String paramString) {
/* 28 */     super(paramString);
/* 29 */     this.content = paramString;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getContent() {
/* 36 */     return this.content;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public void setContent(String paramString) {
/* 43 */     this.content = paramString;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/fna/e9/exception/FnaSynchronizedException.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */