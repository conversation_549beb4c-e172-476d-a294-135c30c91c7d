/*    */ package weaver.fna.e9.exception;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class FnaException
/*    */   extends Exception
/*    */ {
/*    */   private static final long serialVersionUID = 2017062213100001L;
/*    */   private String content;
/*    */   
/*    */   public FnaException(String paramString) {
/* 28 */     super(paramString);
/* 29 */     this.content = paramString;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getContent() {
/* 36 */     return this.content;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public void setContent(String paramString) {
/* 43 */     this.content = paramString;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/fna/e9/exception/FnaException.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */