/*     */ package weaver.fna.e9.dao.base;
/*     */ 
/*     */ import java.text.DecimalFormat;
/*     */ import java.util.ArrayList;
/*     */ import java.util.List;
/*     */ import weaver.fna.e9.exception.FnaException;
/*     */ import weaver.fna.e9.po.base.FnaSubjectAccount;
/*     */ import weaver.fna.general.RecordSet4Action;
/*     */ import weaver.general.Util;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ @Deprecated
/*     */ public class FnaSubjectAccountDao
/*     */   extends FnaBaseDao
/*     */ {
/*     */   @Deprecated
/*     */   public void save(RecordSet4Action paramRecordSet4Action, FnaSubjectAccount paramFnaSubjectAccount) throws Exception {
/*  42 */     boolean bool = true;
/*  43 */     String str1 = "oralce".equals(paramRecordSet4Action.getDBType()) ? "NVL" : "ISNULL";
/*  44 */     String str2 = Util.null2String(paramFnaSubjectAccount.getFnaSubjectaPk()).trim();
/*  45 */     if ("".equals(str2)) {
/*  46 */       throw new FnaException("主键不能为空！");
/*     */     }
/*  48 */     StringBuffer stringBuffer = new StringBuffer();
/*  49 */     stringBuffer.append("insert into fnaSubjectAccount(");
/*  50 */     stringBuffer.append("fnaSubjectaPk");
/*  51 */     stringBuffer.append(" , ");
/*  52 */     stringBuffer.append("fnaPeriodPk");
/*  53 */     stringBuffer.append(" , ");
/*  54 */     stringBuffer.append("supFnaSubjectaPk");
/*  55 */     stringBuffer.append(" , ");
/*  56 */     stringBuffer.append("subjectaName");
/*  57 */     stringBuffer.append(" , ");
/*  58 */     stringBuffer.append("subjectaCode");
/*  59 */     stringBuffer.append(" , ");
/*  60 */     stringBuffer.append("lockedStatus");
/*  61 */     stringBuffer.append(" , ");
/*  62 */     stringBuffer.append("subjectaIsLeaf");
/*  63 */     stringBuffer.append(" , ");
/*  64 */     stringBuffer.append("subjectaLevel");
/*  65 */     stringBuffer.append(" , ");
/*  66 */     stringBuffer.append("showOrder");
/*  67 */     stringBuffer.append(")values(");
/*  68 */     stringBuffer.append("?");
/*  69 */     stringBuffer.append(" , ");
/*  70 */     stringBuffer.append("?");
/*  71 */     stringBuffer.append(" , ");
/*  72 */     stringBuffer.append("?");
/*  73 */     stringBuffer.append(" , ");
/*  74 */     stringBuffer.append("?");
/*  75 */     stringBuffer.append(" , ");
/*  76 */     stringBuffer.append("?");
/*  77 */     stringBuffer.append(" , ");
/*  78 */     stringBuffer.append("?");
/*  79 */     stringBuffer.append(" , ");
/*  80 */     stringBuffer.append("?");
/*  81 */     stringBuffer.append(" , ");
/*  82 */     stringBuffer.append("?");
/*  83 */     stringBuffer.append(" , ");
/*  84 */     stringBuffer.append("?");
/*  85 */     stringBuffer.append(")");
/*  86 */     bool = paramRecordSet4Action.executeUpdate(stringBuffer.toString(), new Object[] { paramFnaSubjectAccount
/*  87 */           .getFnaSubjectaPk(), paramFnaSubjectAccount
/*  88 */           .getFnaPeriodPk(), paramFnaSubjectAccount
/*  89 */           .getSupFnaSubjectaPk(), paramFnaSubjectAccount
/*  90 */           .getSubjectaName(), paramFnaSubjectAccount
/*  91 */           .getSubjectaCode(), paramFnaSubjectAccount
/*  92 */           .getLockedStatus(), paramFnaSubjectAccount
/*  93 */           .getSubjectaIsLeaf(), paramFnaSubjectAccount
/*  94 */           .getSubjectaLevel(), 
/*  95 */           (paramFnaSubjectAccount.getShowOrder() == null) ? null : (new DecimalFormat("#.###")).format(paramFnaSubjectAccount.getShowOrder()) });
/*     */     
/*  97 */     if (!bool) throw new FnaException("保存数据失败请联系管理员查询日志错误信息！");
/*     */   
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void update(RecordSet4Action paramRecordSet4Action, FnaSubjectAccount paramFnaSubjectAccount) throws Exception {
/* 107 */     boolean bool = true;
/* 108 */     String str1 = "oralce".equals(paramRecordSet4Action.getDBType()) ? "NVL" : "ISNULL";
/* 109 */     String str2 = Util.null2String(paramFnaSubjectAccount.getFnaSubjectaPk()).trim();
/* 110 */     if ("".equals(str2)) {
/* 111 */       throw new FnaException("主键不能为空！");
/*     */     }
/* 113 */     StringBuffer stringBuffer = new StringBuffer();
/* 114 */     stringBuffer.append("update fnaSubjectAccount set ");
/* 115 */     stringBuffer.append("fnaPeriodPk=?");
/* 116 */     stringBuffer.append(" , ");
/* 117 */     stringBuffer.append("supFnaSubjectaPk=?");
/* 118 */     stringBuffer.append(" , ");
/* 119 */     stringBuffer.append("subjectaName=?");
/* 120 */     stringBuffer.append(" , ");
/* 121 */     stringBuffer.append("subjectaCode=?");
/* 122 */     stringBuffer.append(" , ");
/* 123 */     stringBuffer.append("lockedStatus=?");
/* 124 */     stringBuffer.append(" , ");
/* 125 */     stringBuffer.append("subjectaIsLeaf=?");
/* 126 */     stringBuffer.append(" , ");
/* 127 */     stringBuffer.append("subjectaLevel=?");
/* 128 */     stringBuffer.append(" , ");
/* 129 */     stringBuffer.append("showOrder=?");
/* 130 */     stringBuffer.append(" where ");
/* 131 */     stringBuffer.append("fnaSubjectaPk=?");
/* 132 */     bool = paramRecordSet4Action.executeUpdate(stringBuffer.toString(), new Object[] { paramFnaSubjectAccount
/* 133 */           .getFnaPeriodPk(), paramFnaSubjectAccount
/* 134 */           .getSupFnaSubjectaPk(), paramFnaSubjectAccount
/* 135 */           .getSubjectaName(), paramFnaSubjectAccount
/* 136 */           .getSubjectaCode(), paramFnaSubjectAccount
/* 137 */           .getLockedStatus(), paramFnaSubjectAccount
/* 138 */           .getSubjectaIsLeaf(), paramFnaSubjectAccount
/* 139 */           .getSubjectaLevel(), 
/* 140 */           (paramFnaSubjectAccount.getShowOrder() == null) ? null : (new DecimalFormat("#.###")).format(paramFnaSubjectAccount.getShowOrder()), paramFnaSubjectAccount
/* 141 */           .getFnaSubjectaPk() });
/*     */     
/* 143 */     if (!bool) throw new FnaException("更新数据失败请联系管理员查询日志错误信息！");
/*     */   
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void saveOrUpdate(RecordSet4Action paramRecordSet4Action, FnaSubjectAccount paramFnaSubjectAccount) throws Exception {
/* 153 */     if (find(paramRecordSet4Action, paramFnaSubjectAccount.getFnaSubjectaPk()) == null) {
/* 154 */       save(paramRecordSet4Action, paramFnaSubjectAccount);
/*     */     } else {
/* 156 */       update(paramRecordSet4Action, paramFnaSubjectAccount);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void delete(RecordSet4Action paramRecordSet4Action, List<FnaSubjectAccount> paramList) throws Exception {
/* 167 */     String str = "oralce".equals(paramRecordSet4Action.getDBType()) ? "NVL" : "ISNULL";
/* 168 */     int i = paramList.size();
/* 169 */     for (byte b = 0; b < i; b++) {
/* 170 */       boolean bool = true;
/* 171 */       FnaSubjectAccount fnaSubjectAccount = paramList.get(b);
/* 172 */       String str1 = fnaSubjectAccount.getFnaSubjectaPk();
/* 173 */       if (str1 == null) {
/* 174 */         throw new FnaException("主键不能为空！");
/*     */       }
/* 176 */       StringBuffer stringBuffer = new StringBuffer();
/* 177 */       stringBuffer.append("delete from fnaSubjectAccount where ");
/* 178 */       stringBuffer.append("fnaSubjectaPk=?");
/* 179 */       bool = paramRecordSet4Action.executeUpdate(stringBuffer.toString(), new Object[] { str1 });
/*     */ 
/*     */       
/* 182 */       if (!bool) throw new FnaException("删除数据失败请联系管理员查询日志错误信息！");
/*     */     
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public FnaSubjectAccount find(RecordSet4Action paramRecordSet4Action, String paramString) throws Exception {
/* 194 */     boolean bool = true;
/* 195 */     String str = "select * from fnaSubjectAccount where fnaSubjectaPk=?";
/* 196 */     bool = paramRecordSet4Action.executeQuery(str, new Object[] { paramString });
/*     */ 
/*     */     
/* 199 */     if (!bool) throw new FnaException("查询失败请联系管理员查询日志错误信息！"); 
/* 200 */     if (paramRecordSet4Action.next()) {
/* 201 */       FnaSubjectAccount fnaSubjectAccount = new FnaSubjectAccount();
/*     */       
/* 203 */       String str1 = paramRecordSet4Action.getString("id");
/* 204 */       fnaSubjectAccount.setId((str1 == null || "".equals(str1)) ? null : new Integer(Util.getIntValue(str1, 0)));
/*     */       
/* 206 */       String str2 = paramRecordSet4Action.getString("fnaSubjectaPk");
/* 207 */       fnaSubjectAccount.setFnaSubjectaPk(str2);
/*     */       
/* 209 */       String str3 = paramRecordSet4Action.getString("fnaPeriodPk");
/* 210 */       fnaSubjectAccount.setFnaPeriodPk(str3);
/*     */       
/* 212 */       String str4 = paramRecordSet4Action.getString("supFnaSubjectaPk");
/* 213 */       fnaSubjectAccount.setSupFnaSubjectaPk(str4);
/*     */       
/* 215 */       String str5 = paramRecordSet4Action.getString("subjectaName");
/* 216 */       fnaSubjectAccount.setSubjectaName(str5);
/*     */       
/* 218 */       String str6 = paramRecordSet4Action.getString("subjectaCode");
/* 219 */       fnaSubjectAccount.setSubjectaCode(str6);
/*     */       
/* 221 */       String str7 = paramRecordSet4Action.getString("lockedStatus");
/* 222 */       fnaSubjectAccount.setLockedStatus((str7 == null || "".equals(str7)) ? null : new Integer(Util.getIntValue(str7, 0)));
/*     */       
/* 224 */       String str8 = paramRecordSet4Action.getString("description");
/* 225 */       fnaSubjectAccount.setDescription(str8);
/*     */       
/* 227 */       String str9 = paramRecordSet4Action.getString("subjectaIsLeaf");
/* 228 */       fnaSubjectAccount.setSubjectaIsLeaf((str9 == null || "".equals(str9)) ? null : new Integer(Util.getIntValue(str9, 0)));
/*     */       
/* 230 */       String str10 = paramRecordSet4Action.getString("subjectaLevel");
/* 231 */       fnaSubjectAccount.setSubjectaLevel((str10 == null || "".equals(str10)) ? null : new Integer(Util.getIntValue(str10, 0)));
/*     */       
/* 233 */       String str11 = paramRecordSet4Action.getString("showOrder");
/* 234 */       fnaSubjectAccount.setShowOrder((str11 == null || "".equals(str11)) ? null : new Double(Util.getDoubleValue(str11, 0.0D)));
/*     */       
/* 236 */       return fnaSubjectAccount;
/*     */     } 
/* 238 */     return null;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public List<FnaSubjectAccount> queryForList(RecordSet4Action paramRecordSet4Action, String paramString, Object... paramVarArgs) throws Exception {
/* 250 */     ArrayList<FnaSubjectAccount> arrayList = new ArrayList();
/* 251 */     boolean bool = paramRecordSet4Action.executeQuery(paramString, paramVarArgs);
/* 252 */     if (!bool) throw new FnaException("查询失败请联系管理员查询日志错误信息！"); 
/* 253 */     while (paramRecordSet4Action.next()) {
/* 254 */       FnaSubjectAccount fnaSubjectAccount = new FnaSubjectAccount();
/*     */       
/* 256 */       String str1 = paramRecordSet4Action.getString("id");
/* 257 */       fnaSubjectAccount.setId((str1 == null || "".equals(str1)) ? null : new Integer(Util.getIntValue(str1, 0)));
/*     */       
/* 259 */       String str2 = paramRecordSet4Action.getString("fnaSubjectaPk");
/* 260 */       fnaSubjectAccount.setFnaSubjectaPk(str2);
/*     */       
/* 262 */       String str3 = paramRecordSet4Action.getString("fnaPeriodPk");
/* 263 */       fnaSubjectAccount.setFnaPeriodPk(str3);
/*     */       
/* 265 */       String str4 = paramRecordSet4Action.getString("supFnaSubjectaPk");
/* 266 */       fnaSubjectAccount.setSupFnaSubjectaPk(str4);
/*     */       
/* 268 */       String str5 = paramRecordSet4Action.getString("subjectaName");
/* 269 */       fnaSubjectAccount.setSubjectaName(str5);
/*     */       
/* 271 */       String str6 = paramRecordSet4Action.getString("subjectaCode");
/* 272 */       fnaSubjectAccount.setSubjectaCode(str6);
/*     */       
/* 274 */       String str7 = paramRecordSet4Action.getString("lockedStatus");
/* 275 */       fnaSubjectAccount.setLockedStatus((str7 == null || "".equals(str7)) ? null : new Integer(Util.getIntValue(str7, 0)));
/*     */       
/* 277 */       String str8 = paramRecordSet4Action.getString("description");
/* 278 */       fnaSubjectAccount.setDescription(str8);
/*     */       
/* 280 */       String str9 = paramRecordSet4Action.getString("subjectaIsLeaf");
/* 281 */       fnaSubjectAccount.setSubjectaIsLeaf((str9 == null || "".equals(str9)) ? null : new Integer(Util.getIntValue(str9, 0)));
/*     */       
/* 283 */       String str10 = paramRecordSet4Action.getString("subjectaLevel");
/* 284 */       fnaSubjectAccount.setSubjectaLevel((str10 == null || "".equals(str10)) ? null : new Integer(Util.getIntValue(str10, 0)));
/*     */       
/* 286 */       String str11 = paramRecordSet4Action.getString("showOrder");
/* 287 */       fnaSubjectAccount.setShowOrder((str11 == null || "".equals(str11)) ? null : new Double(Util.getDoubleValue(str11, 0.0D)));
/*     */       
/* 289 */       arrayList.add(fnaSubjectAccount);
/*     */     } 
/* 291 */     return arrayList;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/fna/e9/dao/base/FnaSubjectAccountDao.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */