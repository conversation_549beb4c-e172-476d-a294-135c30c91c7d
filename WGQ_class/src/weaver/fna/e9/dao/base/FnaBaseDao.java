/*     */ package weaver.fna.e9.dao.base;
/*     */ 
/*     */ import com.esotericsoftware.reflectasm.MethodAccess;
/*     */ import java.text.DecimalFormat;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Arrays;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import weaver.conn.RecordSetTrans;
/*     */ import weaver.fna.cache.MethodAccessCache;
/*     */ import weaver.fna.e9.exception.FnaException;
/*     */ import weaver.fna.e9.po.annotation.DbFieldInfo;
/*     */ import weaver.fna.e9.po.base.FnaBasePo;
/*     */ import weaver.fna.e9.po.base.FnaBasePoField;
/*     */ import weaver.fna.general.RecordSet4Action;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class FnaBaseDao
/*     */ {
/*     */   private boolean setExecuteSql = false;
/*     */   
/*     */   public void setSetExecuteSql(boolean paramBoolean) {
/*  50 */     this.setExecuteSql = paramBoolean;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*  55 */   private String executeSql = "";
/*     */ 
/*     */ 
/*     */   
/*     */   public String getExecuteSql() {
/*  60 */     return this.executeSql;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void deleteObject(Object paramObject) throws Exception {
/*  69 */     deleteObject(new RecordSet4Action(), paramObject);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void deleteObject(RecordSetTrans paramRecordSetTrans, Object paramObject) throws Exception {
/*  78 */     deleteObject(new RecordSet4Action(paramRecordSetTrans), paramObject);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void deleteObject(RecordSet4Action paramRecordSet4Action, Object paramObject) throws Exception {
/*  87 */     StringBuffer stringBuffer = new StringBuffer();
/*     */     
/*  89 */     Class<?> clazz = paramObject.getClass();
/*  90 */     String str = clazz.getName();
/*     */     
/*  92 */     FnaBasePo fnaBasePo = FnaBasePo.GET_PO_CLASS_FIELD_INFO(str);
/*  93 */     MethodAccess methodAccess = MethodAccessCache.getMethodAccessByClass(clazz);
/*     */     
/*  95 */     stringBuffer.append("delete from ").append(fnaBasePo.getTableName()).append(" where ");
/*     */     
/*  97 */     ArrayList<Object> arrayList = new ArrayList();
/*  98 */     byte b1 = 0; int i;
/*  99 */     for (i = 0; i < fnaBasePo.getFieldsLen(); i++) {
/* 100 */       FnaBasePoField fnaBasePoField = fnaBasePo.getFnaBasePoFieldList().get(i);
/* 101 */       if (fnaBasePoField.isDbColumn() && fnaBasePoField.isPrimaryKey()) {
/* 102 */         if (b1) {
/* 103 */           stringBuffer.append(" and ");
/*     */         }
/* 105 */         stringBuffer.append(fnaBasePoField.getDbColName()).append("=?");
/*     */         
/* 107 */         arrayList.add(getFieldValue(methodAccess, paramObject, "get" + captureName(fnaBasePoField.getJavaFieldName()), fnaBasePoField
/* 108 */               .getJavaType().toLowerCase(), fnaBasePoField.getScale()));
/*     */         
/* 110 */         b1++;
/*     */       } 
/*     */     } 
/* 113 */     if (b1 == 0) {
/* 114 */       throw new FnaException("未能从传入po对象中获取数据库信息");
/*     */     }
/*     */     
/* 117 */     i = arrayList.size();
/* 118 */     Object[] arrayOfObject = new Object[i];
/*     */     
/* 120 */     for (byte b2 = 0; b2 < i; b2++) {
/* 121 */       Object object = arrayList.get(b2);
/* 122 */       arrayOfObject[b2] = object;
/*     */     } 
/*     */     
/* 125 */     if (this.setExecuteSql) this.executeSql = stringBuffer.toString() + ";[" + Arrays.toString(arrayOfObject) + "]"; 
/* 126 */     boolean bool = paramRecordSet4Action.executeUpdate(stringBuffer.toString(), arrayOfObject);
/* 127 */     if (!bool) throw new FnaException("删除失败请联系管理员查询日志错误信息！");
/*     */   
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void updateObject(Object paramObject) throws Exception {
/* 136 */     _updateObject(new RecordSet4Action(), paramObject, null, null);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void updateObject(RecordSetTrans paramRecordSetTrans, Object paramObject) throws Exception {
/* 145 */     _updateObject(new RecordSet4Action(paramRecordSetTrans), paramObject, null, null);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void updateObject(RecordSet4Action paramRecordSet4Action, Object paramObject) throws Exception {
/* 154 */     _updateObject(paramRecordSet4Action, paramObject, null, null);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void updateObject(Object paramObject, String... paramVarArgs) throws Exception {
/* 164 */     updateObject(new RecordSet4Action(), paramObject, paramVarArgs);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void updateObject(RecordSetTrans paramRecordSetTrans, Object paramObject, String... paramVarArgs) throws Exception {
/* 174 */     updateObject(new RecordSet4Action(paramRecordSetTrans), paramObject, paramVarArgs);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void updateObject(RecordSet4Action paramRecordSet4Action, Object paramObject, String... paramVarArgs) throws Exception {
/* 184 */     List<String> list = null;
/* 185 */     if (paramVarArgs != null) {
/* 186 */       list = Arrays.asList(paramVarArgs);
/*     */     }
/* 188 */     _updateObject(paramRecordSet4Action, paramObject, list, null);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void updateObject2(Object paramObject, String... paramVarArgs) throws Exception {
/* 198 */     updateObject2(new RecordSet4Action(), paramObject, paramVarArgs);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void updateObject2(RecordSetTrans paramRecordSetTrans, Object paramObject, String... paramVarArgs) throws Exception {
/* 208 */     updateObject2(new RecordSet4Action(paramRecordSetTrans), paramObject, paramVarArgs);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void updateObject2(RecordSet4Action paramRecordSet4Action, Object paramObject, String... paramVarArgs) throws Exception {
/* 218 */     List<String> list = null;
/* 219 */     if (paramVarArgs != null) {
/* 220 */       list = Arrays.asList(paramVarArgs);
/*     */     }
/* 222 */     _updateObject(paramRecordSet4Action, paramObject, null, list);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void _updateObject(RecordSet4Action paramRecordSet4Action, Object paramObject, List<String> paramList1, List<String> paramList2) throws Exception {
/* 235 */     int i = 0;
/* 236 */     if (paramList1 != null) {
/* 237 */       i = paramList1.size();
/*     */     }
/* 239 */     int j = 0;
/* 240 */     if (paramList2 != null) {
/* 241 */       j = paramList2.size();
/*     */     }
/*     */     
/* 244 */     StringBuffer stringBuffer = new StringBuffer();
/*     */     
/* 246 */     Class<?> clazz = paramObject.getClass();
/* 247 */     String str = clazz.getName();
/*     */     
/* 249 */     FnaBasePo fnaBasePo = FnaBasePo.GET_PO_CLASS_FIELD_INFO(str);
/* 250 */     MethodAccess methodAccess = MethodAccessCache.getMethodAccessByClass(clazz);
/*     */     
/* 252 */     stringBuffer.append("update ").append(fnaBasePo.getTableName()).append(" set ");
/*     */     
/* 254 */     ArrayList<Object> arrayList = new ArrayList();
/* 255 */     byte b1 = 0; int k;
/* 256 */     for (k = 0; k < fnaBasePo.getFieldsLen(); k++) {
/* 257 */       FnaBasePoField fnaBasePoField = fnaBasePo.getFnaBasePoFieldList().get(k);
/* 258 */       String str1 = fnaBasePoField.getJavaFieldName();
/* 259 */       boolean bool1 = (i == 0 || (i > 0 && paramList1.contains(str1.toLowerCase()))) ? true : false;
/* 260 */       boolean bool2 = (j == 0 || (j > 0 && !paramList2.contains(str1.toLowerCase()))) ? true : false;
/* 261 */       if (bool1 && bool2 && 
/* 262 */         fnaBasePoField.isDbColumn() && !fnaBasePoField.isDbIdentityColumn() && !fnaBasePoField.isPrimaryKey() && 
/* 263 */         DbFieldInfo.DbType.TEXT != fnaBasePoField.getType() && DbFieldInfo.DbType.CLOB != fnaBasePoField.getType()) {
/*     */         
/* 265 */         if (b1) {
/* 266 */           stringBuffer.append(",");
/*     */         }
/* 268 */         stringBuffer.append(fnaBasePoField.getDbColName()).append("=?");
/*     */         
/* 270 */         arrayList.add(getFieldValue(methodAccess, paramObject, "get" + captureName(str1), fnaBasePoField
/* 271 */               .getJavaType().toLowerCase(), fnaBasePoField.getScale()));
/*     */         
/* 273 */         b1++;
/*     */       } 
/*     */     } 
/*     */ 
/*     */     
/* 278 */     if (b1 == 0) {
/* 279 */       throw new FnaException("没有要更新的数据库记录表字段！");
/*     */     }
/* 281 */     stringBuffer.append(" where ");
/* 282 */     b1 = 0;
/* 283 */     for (k = 0; k < fnaBasePo.getFieldsLen(); k++) {
/* 284 */       FnaBasePoField fnaBasePoField = fnaBasePo.getFnaBasePoFieldList().get(k);
/* 285 */       if (fnaBasePoField.isDbColumn() && fnaBasePoField.isPrimaryKey()) {
/* 286 */         if (b1 > 0) {
/* 287 */           stringBuffer.append(" and ");
/*     */         }
/* 289 */         stringBuffer.append(fnaBasePoField.getDbColName()).append("=?");
/*     */         
/* 291 */         arrayList.add(getFieldValue(methodAccess, paramObject, "get" + captureName(fnaBasePoField.getJavaFieldName()), fnaBasePoField
/* 292 */               .getJavaType().toLowerCase(), fnaBasePoField.getScale()));
/*     */         
/* 294 */         b1++;
/*     */       } 
/*     */     } 
/* 297 */     if (b1 == 0) {
/* 298 */       throw new FnaException("未能从传入po对象中获取数据库信息");
/*     */     }
/*     */     
/* 301 */     k = arrayList.size();
/* 302 */     Object[] arrayOfObject = new Object[k];
/*     */     
/* 304 */     for (byte b2 = 0; b2 < k; b2++) {
/* 305 */       Object object = arrayList.get(b2);
/* 306 */       arrayOfObject[b2] = object;
/*     */     } 
/*     */     
/* 309 */     if (this.setExecuteSql) this.executeSql = stringBuffer.toString() + ";[" + Arrays.toString(arrayOfObject) + "]"; 
/* 310 */     boolean bool = paramRecordSet4Action.executeUpdate(stringBuffer.toString(), arrayOfObject);
/* 311 */     if (!bool) throw new FnaException("更新失败请联系管理员查询日志错误信息！");
/*     */   
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void saveObject(Object paramObject) throws Exception {
/* 320 */     saveObject(new RecordSet4Action(), paramObject);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void saveObject(RecordSetTrans paramRecordSetTrans, Object paramObject) throws Exception {
/* 329 */     saveObject(new RecordSet4Action(paramRecordSetTrans), paramObject);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void saveObject(RecordSet4Action paramRecordSet4Action, Object paramObject) throws Exception {
/* 338 */     StringBuffer stringBuffer1 = new StringBuffer();
/* 339 */     StringBuffer stringBuffer2 = new StringBuffer();
/*     */     
/* 341 */     Class<?> clazz = paramObject.getClass();
/* 342 */     String str = clazz.getName();
/*     */     
/* 344 */     FnaBasePo fnaBasePo = FnaBasePo.GET_PO_CLASS_FIELD_INFO(str);
/* 345 */     MethodAccess methodAccess = MethodAccessCache.getMethodAccessByClass(clazz);
/*     */     
/* 347 */     stringBuffer1.append("insert into ").append(fnaBasePo.getTableName()).append("(");
/* 348 */     stringBuffer2.append("values(");
/*     */     
/* 350 */     boolean bool = true;
/* 351 */     ArrayList<Object> arrayList = new ArrayList(); int i;
/* 352 */     for (i = 0; i < fnaBasePo.getFieldsLen(); i++) {
/* 353 */       FnaBasePoField fnaBasePoField = fnaBasePo.getFnaBasePoFieldList().get(i);
/* 354 */       if (DbFieldInfo.DbType.TEXT != fnaBasePoField.getType() && DbFieldInfo.DbType.CLOB != fnaBasePoField.getType())
/*     */       {
/* 356 */         if (fnaBasePoField.isDbColumn() && !fnaBasePoField.isDbIdentityColumn()) {
/* 357 */           if (arrayList.size() > 0) {
/* 358 */             stringBuffer1.append(",");
/* 359 */             stringBuffer2.append(",");
/*     */           } 
/* 361 */           stringBuffer1.append(fnaBasePoField.getDbColName());
/* 362 */           stringBuffer2.append("?");
/*     */           
/* 364 */           arrayList.add(getFieldValue(methodAccess, paramObject, "get" + captureName(fnaBasePoField.getJavaFieldName()), fnaBasePoField
/* 365 */                 .getJavaType().toLowerCase(), fnaBasePoField.getScale()));
/*     */           
/* 367 */           bool = false;
/*     */         } 
/*     */       }
/*     */     } 
/* 371 */     if (bool) {
/* 372 */       throw new FnaException("未能从传入po对象中获取数据库信息");
/*     */     }
/*     */     
/* 375 */     stringBuffer1.append(")");
/* 376 */     stringBuffer2.append(")");
/*     */     
/* 378 */     i = arrayList.size();
/* 379 */     Object[] arrayOfObject = new Object[i];
/*     */     
/* 381 */     for (byte b = 0; b < i; b++) {
/* 382 */       Object object = arrayList.get(b);
/* 383 */       arrayOfObject[b] = object;
/*     */     } 
/*     */     
/* 386 */     if (this.setExecuteSql) this.executeSql = stringBuffer1.toString() + stringBuffer2.toString() + ";[" + Arrays.toString(arrayOfObject) + "]"; 
/* 387 */     boolean bool1 = paramRecordSet4Action.executeUpdate(stringBuffer1.toString() + stringBuffer2.toString(), arrayOfObject);
/* 388 */     if (!bool1) throw new FnaException("保存失败请联系管理员查询日志错误信息！");
/*     */   
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private Object getFieldValue(MethodAccess paramMethodAccess, Object paramObject, String paramString1, String paramString2, int paramInt) throws Exception {
/* 410 */     Object object = paramMethodAccess.invoke(paramObject, paramString1, new Object[0]);
/* 411 */     if (paramString2.endsWith("string")) {
/* 412 */       if (object == null) {
/* 413 */         return null;
/*     */       }
/* 415 */       return object;
/* 416 */     }  if (paramString2.endsWith("int") || paramString2.endsWith("integer")) {
/* 417 */       if (object == null) {
/* 418 */         return null;
/*     */       }
/* 420 */       return object;
/* 421 */     }  if (paramString2.endsWith("double")) {
/* 422 */       if (object == null) {
/* 423 */         return null;
/*     */       }
/* 425 */       return (new DecimalFormat(getDecimalFormatStr(paramInt))).format(object);
/*     */     } 
/* 427 */     throw new FnaException("试图赋值(" + paramString1 + ")未处理的数据库字段类型(" + paramString2 + ")");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Object queryForObject(String paramString1, String paramString2, Object... paramVarArgs) throws Exception {
/* 439 */     return queryForObject(new RecordSet4Action(), paramString1, paramString2, paramVarArgs);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Object queryForObject(RecordSetTrans paramRecordSetTrans, String paramString1, String paramString2, Object... paramVarArgs) throws Exception {
/* 451 */     return queryForObject(new RecordSet4Action(paramRecordSetTrans), paramString1, paramString2, paramVarArgs);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Object queryForObject(RecordSet4Action paramRecordSet4Action, String paramString1, String paramString2, Object... paramVarArgs) throws Exception {
/* 463 */     boolean bool = true;
/* 464 */     if (this.setExecuteSql) this.executeSql = paramString2 + ";[" + Arrays.toString(paramVarArgs) + "]"; 
/* 465 */     bool = paramRecordSet4Action.executeQuery(paramString2, paramVarArgs);
/* 466 */     if (!bool) throw new FnaException("查询失败请联系管理员查询日志错误信息！"); 
/* 467 */     if (paramRecordSet4Action.next()) {
/* 468 */       return instanceAndLoadFromRecordSet(paramRecordSet4Action, paramString1);
/*     */     }
/* 470 */     return null;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<?> queryForObjectList(String paramString1, String paramString2, Object... paramVarArgs) throws Exception {
/* 481 */     return queryForObjectList(new RecordSet4Action(), paramString1, paramString2, paramVarArgs);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<?> queryForObjectList(RecordSetTrans paramRecordSetTrans, String paramString1, String paramString2, Object... paramVarArgs) throws Exception {
/* 493 */     return queryForObjectList(new RecordSet4Action(paramRecordSetTrans), paramString1, paramString2, paramVarArgs);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<?> queryForObjectList(RecordSet4Action paramRecordSet4Action, String paramString1, String paramString2, Object... paramVarArgs) throws Exception {
/* 506 */     boolean bool = true;
/* 507 */     ArrayList<Object> arrayList = new ArrayList();
/* 508 */     if (this.setExecuteSql) this.executeSql = paramString2 + ";[" + Arrays.toString(paramVarArgs) + "]"; 
/* 509 */     bool = paramRecordSet4Action.executeQuery(paramString2, paramVarArgs);
/* 510 */     if (!bool) throw new FnaException("查询失败请联系管理员查询日志错误信息！"); 
/* 511 */     while (paramRecordSet4Action.next()) {
/* 512 */       arrayList.add(instanceAndLoadFromRecordSet(paramRecordSet4Action, paramString1));
/*     */     }
/* 514 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<HashMap<String, Object>> queryForObjectsList(List<String> paramList, String paramString, Object... paramVarArgs) throws Exception {
/* 525 */     return queryForObjectsList(new RecordSet4Action(), paramList, paramString, paramVarArgs);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<HashMap<String, Object>> queryForObjectsList(RecordSetTrans paramRecordSetTrans, List<String> paramList, String paramString, Object... paramVarArgs) throws Exception {
/* 537 */     return queryForObjectsList(new RecordSet4Action(paramRecordSetTrans), paramList, paramString, paramVarArgs);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<HashMap<String, Object>> queryForObjectsList(RecordSet4Action paramRecordSet4Action, List<String> paramList, String paramString, Object... paramVarArgs) throws Exception {
/* 549 */     boolean bool = true;
/* 550 */     int i = paramList.size();
/* 551 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 552 */     if (this.setExecuteSql) this.executeSql = paramString + ";[" + Arrays.toString(paramVarArgs) + "]"; 
/* 553 */     bool = paramRecordSet4Action.executeQuery(paramString, paramVarArgs);
/* 554 */     if (!bool) throw new FnaException("查询失败请联系管理员查询日志错误信息！"); 
/* 555 */     while (paramRecordSet4Action.next()) {
/* 556 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 557 */       for (byte b = 0; b < i; b++) {
/* 558 */         String str = paramList.get(b);
/* 559 */         hashMap.put(str, instanceAndLoadFromRecordSet(paramRecordSet4Action, str));
/*     */       } 
/* 561 */       arrayList.add(hashMap);
/*     */     } 
/* 563 */     return (List)arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private Object instanceAndLoadFromRecordSet(RecordSet4Action paramRecordSet4Action, String paramString) throws Exception {
/* 578 */     String[] arrayOfString = paramRecordSet4Action.getColumnName();
/* 579 */     int i = arrayOfString.length;
/* 580 */     ArrayList<String> arrayList = new ArrayList();
/* 581 */     for (byte b1 = 0; b1 < i; b1++) {
/* 582 */       arrayList.add(arrayOfString[b1].trim().toLowerCase());
/*     */     }
/*     */     
/* 585 */     FnaBasePo fnaBasePo = FnaBasePo.GET_PO_CLASS_FIELD_INFO(paramString);
/* 586 */     Object object = fnaBasePo.getClazz().newInstance();
/* 587 */     MethodAccess methodAccess = MethodAccessCache.getMethodAccessByClass(fnaBasePo.getClazz());
/*     */     
/* 589 */     boolean bool = true;
/* 590 */     for (byte b2 = 0; b2 < fnaBasePo.getFieldsLen(); b2++) {
/* 591 */       FnaBasePoField fnaBasePoField = fnaBasePo.getFnaBasePoFieldList().get(b2);
/* 592 */       if (fnaBasePoField.isDbColumn()) {
/* 593 */         String str1 = fnaBasePoField.getDbColName();
/* 594 */         String str2 = fnaBasePo.getTableName() + "_" + str1;
/* 595 */         String str3 = "";
/* 596 */         if (arrayList.contains(str1.toLowerCase())) {
/* 597 */           str3 = str1;
/* 598 */         } else if (arrayList.contains(str2.toLowerCase())) {
/* 599 */           str3 = str2;
/*     */         } 
/* 601 */         if (!"".equals(str3)) {
/* 602 */           String str4 = fnaBasePoField.getJavaType().toLowerCase();
/* 603 */           String str5 = "set" + captureName(fnaBasePoField.getJavaFieldName());
/*     */           
/* 605 */           if (str4.endsWith("string")) {
/* 606 */             methodAccess.invoke(object, str5, new Object[] { paramRecordSet4Action.getString(str3) });
/* 607 */           } else if (str4.endsWith("int") || str4.endsWith("integer")) {
/* 608 */             methodAccess.invoke(object, str5, new Object[] { paramRecordSet4Action.getInt(str3) });
/* 609 */           } else if (str4.endsWith("double")) {
/* 610 */             methodAccess.invoke(object, str5, new Object[] { paramRecordSet4Action.getDouble(str3) });
/*     */           } else {
/* 612 */             throw new FnaException("试图从数据库读取(" + str5 + ")未处理的java属性类型(" + str4 + ")");
/*     */           } 
/* 614 */           bool = false;
/*     */         } 
/*     */       } 
/*     */     } 
/*     */     
/* 619 */     if (bool) {
/* 620 */       throw new FnaException("未能从传入po对象中获取数据库信息");
/*     */     }
/*     */     
/* 623 */     return object;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String getDecimalFormatStr(int paramInt) {
/* 631 */     StringBuffer stringBuffer = new StringBuffer("#");
/* 632 */     for (byte b = 0; b < paramInt; b++) {
/* 633 */       if (b == 0) {
/* 634 */         stringBuffer.append(".");
/*     */       }
/* 636 */       stringBuffer.append("#");
/*     */     } 
/* 638 */     return stringBuffer.toString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String captureName(String paramString) {
/* 646 */     if (paramString != null && !"".equals(paramString)) {
/* 647 */       char[] arrayOfChar = paramString.toCharArray();
/* 648 */       arrayOfChar[0] = (arrayOfChar[0] + "").toUpperCase().toCharArray()[0];
/* 649 */       return String.valueOf(arrayOfChar);
/*     */     } 
/* 651 */     return paramString;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/fna/e9/dao/base/FnaBaseDao.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */