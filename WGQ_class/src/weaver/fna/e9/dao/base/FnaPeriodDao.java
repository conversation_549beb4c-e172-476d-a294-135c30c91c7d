/*     */ package weaver.fna.e9.dao.base;
/*     */ 
/*     */ import java.text.DecimalFormat;
/*     */ import java.util.ArrayList;
/*     */ import java.util.List;
/*     */ import weaver.fna.e9.exception.FnaException;
/*     */ import weaver.fna.e9.po.base.FnaPeriod;
/*     */ import weaver.fna.general.RecordSet4Action;
/*     */ import weaver.general.Util;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ @Deprecated
/*     */ public class FnaPeriodDao
/*     */   extends FnaBaseDao
/*     */ {
/*     */   @Deprecated
/*     */   public void save(RecordSet4Action paramRecordSet4Action, FnaPeriod paramFnaPeriod) throws Exception {
/*  41 */     boolean bool = true;
/*  42 */     String str1 = "oralce".equals(paramRecordSet4Action.getDBType()) ? "NVL" : "ISNULL";
/*  43 */     String str2 = Util.null2String(paramFnaPeriod.getFnaPeriodPk()).trim();
/*  44 */     if ("".equals(str2)) {
/*  45 */       throw new FnaException("主键不能为空！");
/*     */     }
/*  47 */     StringBuffer stringBuffer = new StringBuffer();
/*  48 */     stringBuffer.append("insert into fnaPeriod(");
/*  49 */     stringBuffer.append("fnaPeriodPk");
/*  50 */     stringBuffer.append(" , ");
/*  51 */     stringBuffer.append("fnaPeriodName");
/*  52 */     stringBuffer.append(" , ");
/*  53 */     stringBuffer.append("fnaCycle");
/*  54 */     stringBuffer.append(" , ");
/*  55 */     stringBuffer.append("startdate");
/*  56 */     stringBuffer.append(" , ");
/*  57 */     stringBuffer.append("enddate");
/*  58 */     stringBuffer.append(" , ");
/*  59 */     stringBuffer.append("status");
/*  60 */     stringBuffer.append(" , ");
/*  61 */     stringBuffer.append("showOrder");
/*  62 */     stringBuffer.append(")values(");
/*  63 */     stringBuffer.append("?");
/*  64 */     stringBuffer.append(" , ");
/*  65 */     stringBuffer.append("?");
/*  66 */     stringBuffer.append(" , ");
/*  67 */     stringBuffer.append("?");
/*  68 */     stringBuffer.append(" , ");
/*  69 */     stringBuffer.append("?");
/*  70 */     stringBuffer.append(" , ");
/*  71 */     stringBuffer.append("?");
/*  72 */     stringBuffer.append(" , ");
/*  73 */     stringBuffer.append("?");
/*  74 */     stringBuffer.append(" , ");
/*  75 */     stringBuffer.append("?");
/*  76 */     stringBuffer.append(")");
/*  77 */     bool = paramRecordSet4Action.executeUpdate(stringBuffer.toString(), new Object[] { paramFnaPeriod
/*  78 */           .getFnaPeriodPk(), paramFnaPeriod
/*  79 */           .getFnaPeriodName(), paramFnaPeriod
/*  80 */           .getFnaCycle(), paramFnaPeriod
/*  81 */           .getStartdate(), paramFnaPeriod
/*  82 */           .getEnddate(), paramFnaPeriod
/*  83 */           .getStatus(), 
/*  84 */           (paramFnaPeriod.getShowOrder() == null) ? null : (new DecimalFormat("#.###")).format(paramFnaPeriod.getShowOrder()) });
/*     */     
/*  86 */     if (!bool) throw new FnaException("保存数据失败请联系管理员查询日志错误信息！");
/*     */   
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void update(RecordSet4Action paramRecordSet4Action, FnaPeriod paramFnaPeriod) throws Exception {
/*  96 */     boolean bool = true;
/*  97 */     String str1 = "oralce".equals(paramRecordSet4Action.getDBType()) ? "NVL" : "ISNULL";
/*  98 */     String str2 = Util.null2String(paramFnaPeriod.getFnaPeriodPk()).trim();
/*  99 */     if ("".equals(str2)) {
/* 100 */       throw new FnaException("主键不能为空！");
/*     */     }
/* 102 */     StringBuffer stringBuffer = new StringBuffer();
/* 103 */     stringBuffer.append("update fnaPeriod set ");
/* 104 */     stringBuffer.append("fnaPeriodName=?");
/* 105 */     stringBuffer.append(" , ");
/* 106 */     stringBuffer.append("fnaCycle=?");
/* 107 */     stringBuffer.append(" , ");
/* 108 */     stringBuffer.append("startdate=?");
/* 109 */     stringBuffer.append(" , ");
/* 110 */     stringBuffer.append("enddate=?");
/* 111 */     stringBuffer.append(" , ");
/* 112 */     stringBuffer.append("status=?");
/* 113 */     stringBuffer.append(" , ");
/* 114 */     stringBuffer.append("showOrder=?");
/* 115 */     stringBuffer.append(" where ");
/* 116 */     stringBuffer.append("fnaPeriodPk=?");
/* 117 */     bool = paramRecordSet4Action.executeUpdate(stringBuffer.toString(), new Object[] { paramFnaPeriod
/* 118 */           .getFnaPeriodName(), paramFnaPeriod
/* 119 */           .getFnaCycle(), paramFnaPeriod
/* 120 */           .getStartdate(), paramFnaPeriod
/* 121 */           .getEnddate(), paramFnaPeriod
/* 122 */           .getStatus(), 
/* 123 */           (paramFnaPeriod.getShowOrder() == null) ? null : (new DecimalFormat("#.###")).format(paramFnaPeriod.getShowOrder()), paramFnaPeriod
/* 124 */           .getFnaPeriodPk() });
/*     */     
/* 126 */     if (!bool) throw new FnaException("更新数据失败请联系管理员查询日志错误信息！");
/*     */   
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void saveOrUpdate(RecordSet4Action paramRecordSet4Action, FnaPeriod paramFnaPeriod) throws Exception {
/* 136 */     if (find(paramRecordSet4Action, paramFnaPeriod.getFnaPeriodPk()) == null) {
/* 137 */       save(paramRecordSet4Action, paramFnaPeriod);
/*     */     } else {
/* 139 */       update(paramRecordSet4Action, paramFnaPeriod);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void delete(RecordSet4Action paramRecordSet4Action, List<FnaPeriod> paramList) throws Exception {
/* 150 */     String str = "oralce".equals(paramRecordSet4Action.getDBType()) ? "NVL" : "ISNULL";
/* 151 */     int i = paramList.size();
/* 152 */     for (byte b = 0; b < i; b++) {
/* 153 */       boolean bool = true;
/* 154 */       FnaPeriod fnaPeriod = paramList.get(b);
/* 155 */       String str1 = fnaPeriod.getFnaPeriodPk();
/* 156 */       if (str1 == null) {
/* 157 */         throw new FnaException("主键不能为空！");
/*     */       }
/* 159 */       StringBuffer stringBuffer = new StringBuffer();
/* 160 */       stringBuffer.append("delete from fnaPeriod where ");
/* 161 */       stringBuffer.append("fnaPeriodPk=?");
/* 162 */       bool = paramRecordSet4Action.executeUpdate(stringBuffer.toString(), new Object[] { str1 });
/*     */ 
/*     */       
/* 165 */       if (!bool) throw new FnaException("删除数据失败请联系管理员查询日志错误信息！");
/*     */     
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public FnaPeriod find(RecordSet4Action paramRecordSet4Action, String paramString) throws Exception {
/* 177 */     boolean bool = true;
/* 178 */     String str = "select * from fnaPeriod where fnaPeriodPk=?";
/* 179 */     bool = paramRecordSet4Action.executeQuery(str, new Object[] { paramString });
/*     */ 
/*     */     
/* 182 */     if (!bool) throw new FnaException("查询失败请联系管理员查询日志错误信息！"); 
/* 183 */     if (paramRecordSet4Action.next()) {
/* 184 */       FnaPeriod fnaPeriod = new FnaPeriod();
/*     */       
/* 186 */       String str1 = paramRecordSet4Action.getString("id");
/* 187 */       fnaPeriod.setId((str1 == null || "".equals(str1)) ? null : new Integer(Util.getIntValue(str1, 0)));
/*     */       
/* 189 */       String str2 = paramRecordSet4Action.getString("fnaPeriodPk");
/* 190 */       fnaPeriod.setFnaPeriodPk(str2);
/*     */       
/* 192 */       String str3 = paramRecordSet4Action.getString("fnaPeriodName");
/* 193 */       fnaPeriod.setFnaPeriodName(str3);
/*     */       
/* 195 */       String str4 = paramRecordSet4Action.getString("fnaCycle");
/* 196 */       fnaPeriod.setFnaCycle((str4 == null || "".equals(str4)) ? null : new Integer(Util.getIntValue(str4, 0)));
/*     */       
/* 198 */       String str5 = paramRecordSet4Action.getString("startdate");
/* 199 */       fnaPeriod.setStartdate(str5);
/*     */       
/* 201 */       String str6 = paramRecordSet4Action.getString("enddate");
/* 202 */       fnaPeriod.setEnddate(str6);
/*     */       
/* 204 */       String str7 = paramRecordSet4Action.getString("status");
/* 205 */       fnaPeriod.setStatus((str7 == null || "".equals(str7)) ? null : new Integer(Util.getIntValue(str7, 0)));
/*     */       
/* 207 */       String str8 = paramRecordSet4Action.getString("showOrder");
/* 208 */       fnaPeriod.setShowOrder((str8 == null || "".equals(str8)) ? null : new Double(Util.getDoubleValue(str8, 0.0D)));
/*     */       
/* 210 */       return fnaPeriod;
/*     */     } 
/* 212 */     return null;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public List<FnaPeriod> queryForList(RecordSet4Action paramRecordSet4Action, String paramString, Object... paramVarArgs) throws Exception {
/* 224 */     ArrayList<FnaPeriod> arrayList = new ArrayList();
/* 225 */     boolean bool = paramRecordSet4Action.executeQuery(paramString, paramVarArgs);
/* 226 */     if (!bool) throw new FnaException("查询失败请联系管理员查询日志错误信息！"); 
/* 227 */     while (paramRecordSet4Action.next()) {
/* 228 */       FnaPeriod fnaPeriod = new FnaPeriod();
/*     */       
/* 230 */       String str1 = paramRecordSet4Action.getString("id");
/* 231 */       fnaPeriod.setId((str1 == null || "".equals(str1)) ? null : new Integer(Util.getIntValue(str1, 0)));
/*     */       
/* 233 */       String str2 = paramRecordSet4Action.getString("fnaPeriodPk");
/* 234 */       fnaPeriod.setFnaPeriodPk(str2);
/*     */       
/* 236 */       String str3 = paramRecordSet4Action.getString("fnaPeriodName");
/* 237 */       fnaPeriod.setFnaPeriodName(str3);
/*     */       
/* 239 */       String str4 = paramRecordSet4Action.getString("fnaCycle");
/* 240 */       fnaPeriod.setFnaCycle((str4 == null || "".equals(str4)) ? null : new Integer(Util.getIntValue(str4, 0)));
/*     */       
/* 242 */       String str5 = paramRecordSet4Action.getString("startdate");
/* 243 */       fnaPeriod.setStartdate(str5);
/*     */       
/* 245 */       String str6 = paramRecordSet4Action.getString("enddate");
/* 246 */       fnaPeriod.setEnddate(str6);
/*     */       
/* 248 */       String str7 = paramRecordSet4Action.getString("status");
/* 249 */       fnaPeriod.setStatus((str7 == null || "".equals(str7)) ? null : new Integer(Util.getIntValue(str7, 0)));
/*     */       
/* 251 */       String str8 = paramRecordSet4Action.getString("showOrder");
/* 252 */       fnaPeriod.setShowOrder((str8 == null || "".equals(str8)) ? null : new Double(Util.getDoubleValue(str8, 0.0D)));
/*     */       
/* 254 */       arrayList.add(fnaPeriod);
/*     */     } 
/* 256 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void setFnaPeriodDtlByPk(RecordSet4Action paramRecordSet4Action, FnaPeriod paramFnaPeriod) throws Exception {
/* 266 */     paramFnaPeriod.setFnaPeriodDtl((new FnaPeriodDtlDao()).queryFnaPeriodDtlByFk(paramRecordSet4Action, paramFnaPeriod.getFnaPeriodPk()));
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/fna/e9/dao/base/FnaPeriodDao.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */