/*     */ package weaver.fna.e9.dao.base;
/*     */ 
/*     */ import java.text.DecimalFormat;
/*     */ import java.util.ArrayList;
/*     */ import java.util.List;
/*     */ import weaver.fna.e9.exception.FnaException;
/*     */ import weaver.fna.e9.po.base.FnaSubjectBudget;
/*     */ import weaver.fna.general.RecordSet4Action;
/*     */ import weaver.general.Util;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ @Deprecated
/*     */ public class FnaSubjectBudgetDao
/*     */   extends FnaBaseDao
/*     */ {
/*     */   @Deprecated
/*     */   public void save(RecordSet4Action paramRecordSet4Action, FnaSubjectBudget paramFnaSubjectBudget) throws Exception {
/*  42 */     boolean bool = true;
/*  43 */     String str1 = "oralce".equals(paramRecordSet4Action.getDBType()) ? "NVL" : "ISNULL";
/*  44 */     String str2 = Util.null2String(paramFnaSubjectBudget.getFnaSubjectbPk()).trim();
/*  45 */     if ("".equals(str2)) {
/*  46 */       throw new FnaException("主键不能为空！");
/*     */     }
/*  48 */     StringBuffer stringBuffer = new StringBuffer();
/*  49 */     stringBuffer.append("insert into fnaSubjectBudget(");
/*  50 */     stringBuffer.append("fnaSubjectbPk");
/*  51 */     stringBuffer.append(" , ");
/*  52 */     stringBuffer.append("fnaPeriodPk");
/*  53 */     stringBuffer.append(" , ");
/*  54 */     stringBuffer.append("supFnaSubjectbPk");
/*  55 */     stringBuffer.append(" , ");
/*  56 */     stringBuffer.append("fnaSubjectaPk");
/*  57 */     stringBuffer.append(" , ");
/*  58 */     stringBuffer.append("subjectbName");
/*  59 */     stringBuffer.append(" , ");
/*  60 */     stringBuffer.append("subjectbCode");
/*  61 */     stringBuffer.append(" , ");
/*  62 */     stringBuffer.append("lockedStatus");
/*  63 */     stringBuffer.append(" , ");
/*  64 */     stringBuffer.append("subjectbIsLeaf");
/*  65 */     stringBuffer.append(" , ");
/*  66 */     stringBuffer.append("subjectbLevel");
/*  67 */     stringBuffer.append(" , ");
/*  68 */     stringBuffer.append("showOrder");
/*  69 */     stringBuffer.append(")values(");
/*  70 */     stringBuffer.append("?");
/*  71 */     stringBuffer.append(" , ");
/*  72 */     stringBuffer.append("?");
/*  73 */     stringBuffer.append(" , ");
/*  74 */     stringBuffer.append("?");
/*  75 */     stringBuffer.append(" , ");
/*  76 */     stringBuffer.append("?");
/*  77 */     stringBuffer.append(" , ");
/*  78 */     stringBuffer.append("?");
/*  79 */     stringBuffer.append(" , ");
/*  80 */     stringBuffer.append("?");
/*  81 */     stringBuffer.append(" , ");
/*  82 */     stringBuffer.append("?");
/*  83 */     stringBuffer.append(" , ");
/*  84 */     stringBuffer.append("?");
/*  85 */     stringBuffer.append(" , ");
/*  86 */     stringBuffer.append("?");
/*  87 */     stringBuffer.append(" , ");
/*  88 */     stringBuffer.append("?");
/*  89 */     stringBuffer.append(")");
/*  90 */     bool = paramRecordSet4Action.executeUpdate(stringBuffer.toString(), new Object[] { paramFnaSubjectBudget
/*  91 */           .getFnaSubjectbPk(), paramFnaSubjectBudget
/*  92 */           .getFnaPeriodPk(), paramFnaSubjectBudget
/*  93 */           .getSupFnaSubjectbPk(), paramFnaSubjectBudget
/*  94 */           .getFnaSubjectaPk(), paramFnaSubjectBudget
/*  95 */           .getSubjectbName(), paramFnaSubjectBudget
/*  96 */           .getSubjectbCode(), paramFnaSubjectBudget
/*  97 */           .getLockedStatus(), paramFnaSubjectBudget
/*  98 */           .getSubjectbIsLeaf(), paramFnaSubjectBudget
/*  99 */           .getSubjectbLevel(), 
/* 100 */           (paramFnaSubjectBudget.getShowOrder() == null) ? null : (new DecimalFormat("#.###")).format(paramFnaSubjectBudget.getShowOrder()) });
/*     */     
/* 102 */     if (!bool) throw new FnaException("保存数据失败请联系管理员查询日志错误信息！");
/*     */   
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void update(RecordSet4Action paramRecordSet4Action, FnaSubjectBudget paramFnaSubjectBudget) throws Exception {
/* 112 */     boolean bool = true;
/* 113 */     String str1 = "oralce".equals(paramRecordSet4Action.getDBType()) ? "NVL" : "ISNULL";
/* 114 */     String str2 = Util.null2String(paramFnaSubjectBudget.getFnaSubjectbPk()).trim();
/* 115 */     if ("".equals(str2)) {
/* 116 */       throw new FnaException("主键不能为空！");
/*     */     }
/* 118 */     StringBuffer stringBuffer = new StringBuffer();
/* 119 */     stringBuffer.append("update fnaSubjectBudget set ");
/* 120 */     stringBuffer.append("fnaPeriodPk=?");
/* 121 */     stringBuffer.append(" , ");
/* 122 */     stringBuffer.append("supFnaSubjectbPk=?");
/* 123 */     stringBuffer.append(" , ");
/* 124 */     stringBuffer.append("fnaSubjectaPk=?");
/* 125 */     stringBuffer.append(" , ");
/* 126 */     stringBuffer.append("subjectbName=?");
/* 127 */     stringBuffer.append(" , ");
/* 128 */     stringBuffer.append("subjectbCode=?");
/* 129 */     stringBuffer.append(" , ");
/* 130 */     stringBuffer.append("lockedStatus=?");
/* 131 */     stringBuffer.append(" , ");
/* 132 */     stringBuffer.append("subjectbIsLeaf=?");
/* 133 */     stringBuffer.append(" , ");
/* 134 */     stringBuffer.append("subjectbLevel=?");
/* 135 */     stringBuffer.append(" , ");
/* 136 */     stringBuffer.append("showOrder=?");
/* 137 */     stringBuffer.append(" where ");
/* 138 */     stringBuffer.append("fnaSubjectbPk=?");
/* 139 */     bool = paramRecordSet4Action.executeUpdate(stringBuffer.toString(), new Object[] { paramFnaSubjectBudget
/* 140 */           .getFnaPeriodPk(), paramFnaSubjectBudget
/* 141 */           .getSupFnaSubjectbPk(), paramFnaSubjectBudget
/* 142 */           .getFnaSubjectaPk(), paramFnaSubjectBudget
/* 143 */           .getSubjectbName(), paramFnaSubjectBudget
/* 144 */           .getSubjectbCode(), paramFnaSubjectBudget
/* 145 */           .getLockedStatus(), paramFnaSubjectBudget
/* 146 */           .getSubjectbIsLeaf(), paramFnaSubjectBudget
/* 147 */           .getSubjectbLevel(), 
/* 148 */           (paramFnaSubjectBudget.getShowOrder() == null) ? null : (new DecimalFormat("#.###")).format(paramFnaSubjectBudget.getShowOrder()), paramFnaSubjectBudget
/* 149 */           .getFnaSubjectbPk() });
/*     */     
/* 151 */     if (!bool) throw new FnaException("更新数据失败请联系管理员查询日志错误信息！");
/*     */   
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void saveOrUpdate(RecordSet4Action paramRecordSet4Action, FnaSubjectBudget paramFnaSubjectBudget) throws Exception {
/* 161 */     if (find(paramRecordSet4Action, paramFnaSubjectBudget.getFnaSubjectbPk()) == null) {
/* 162 */       save(paramRecordSet4Action, paramFnaSubjectBudget);
/*     */     } else {
/* 164 */       update(paramRecordSet4Action, paramFnaSubjectBudget);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void delete(RecordSet4Action paramRecordSet4Action, List<FnaSubjectBudget> paramList) throws Exception {
/* 175 */     String str = "oralce".equals(paramRecordSet4Action.getDBType()) ? "NVL" : "ISNULL";
/* 176 */     int i = paramList.size();
/* 177 */     for (byte b = 0; b < i; b++) {
/* 178 */       boolean bool = true;
/* 179 */       FnaSubjectBudget fnaSubjectBudget = paramList.get(b);
/* 180 */       String str1 = fnaSubjectBudget.getFnaSubjectbPk();
/* 181 */       if (str1 == null) {
/* 182 */         throw new FnaException("主键不能为空！");
/*     */       }
/* 184 */       StringBuffer stringBuffer = new StringBuffer();
/* 185 */       stringBuffer.append("delete from fnaSubjectBudget where ");
/* 186 */       stringBuffer.append("fnaSubjectbPk=?");
/* 187 */       bool = paramRecordSet4Action.executeUpdate(stringBuffer.toString(), new Object[] { str1 });
/*     */ 
/*     */       
/* 190 */       if (!bool) throw new FnaException("删除数据失败请联系管理员查询日志错误信息！");
/*     */     
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public FnaSubjectBudget find(RecordSet4Action paramRecordSet4Action, String paramString) throws Exception {
/* 202 */     boolean bool = true;
/* 203 */     String str = "select * from fnaSubjectBudget where fnaSubjectbPk=?";
/* 204 */     bool = paramRecordSet4Action.executeQuery(str, new Object[] { paramString });
/*     */ 
/*     */     
/* 207 */     if (!bool) throw new FnaException("查询失败请联系管理员查询日志错误信息！"); 
/* 208 */     if (paramRecordSet4Action.next()) {
/* 209 */       FnaSubjectBudget fnaSubjectBudget = new FnaSubjectBudget();
/*     */       
/* 211 */       String str1 = paramRecordSet4Action.getString("id");
/* 212 */       fnaSubjectBudget.setId((str1 == null || "".equals(str1)) ? null : new Integer(Util.getIntValue(str1, 0)));
/*     */       
/* 214 */       String str2 = paramRecordSet4Action.getString("fnaSubjectbPk");
/* 215 */       fnaSubjectBudget.setFnaSubjectbPk(str2);
/*     */       
/* 217 */       String str3 = paramRecordSet4Action.getString("fnaPeriodPk");
/* 218 */       fnaSubjectBudget.setFnaPeriodPk(str3);
/*     */       
/* 220 */       String str4 = paramRecordSet4Action.getString("supFnaSubjectbPk");
/* 221 */       fnaSubjectBudget.setSupFnaSubjectbPk(str4);
/*     */       
/* 223 */       String str5 = paramRecordSet4Action.getString("fnaSubjectaPk");
/* 224 */       fnaSubjectBudget.setFnaSubjectaPk(str5);
/*     */       
/* 226 */       String str6 = paramRecordSet4Action.getString("subjectbName");
/* 227 */       fnaSubjectBudget.setSubjectbName(str6);
/*     */       
/* 229 */       String str7 = paramRecordSet4Action.getString("subjectbCode");
/* 230 */       fnaSubjectBudget.setSubjectbCode(str7);
/*     */       
/* 232 */       String str8 = paramRecordSet4Action.getString("lockedStatus");
/* 233 */       fnaSubjectBudget.setLockedStatus((str8 == null || "".equals(str8)) ? null : new Integer(Util.getIntValue(str8, 0)));
/*     */       
/* 235 */       String str9 = paramRecordSet4Action.getString("description");
/* 236 */       fnaSubjectBudget.setDescription(str9);
/*     */       
/* 238 */       String str10 = paramRecordSet4Action.getString("subjectbIsLeaf");
/* 239 */       fnaSubjectBudget.setSubjectbIsLeaf((str10 == null || "".equals(str10)) ? null : new Integer(Util.getIntValue(str10, 0)));
/*     */       
/* 241 */       String str11 = paramRecordSet4Action.getString("subjectbLevel");
/* 242 */       fnaSubjectBudget.setSubjectbLevel((str11 == null || "".equals(str11)) ? null : new Integer(Util.getIntValue(str11, 0)));
/*     */       
/* 244 */       String str12 = paramRecordSet4Action.getString("showOrder");
/* 245 */       fnaSubjectBudget.setShowOrder((str12 == null || "".equals(str12)) ? null : new Double(Util.getDoubleValue(str12, 0.0D)));
/*     */       
/* 247 */       return fnaSubjectBudget;
/*     */     } 
/* 249 */     return null;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public List<FnaSubjectBudget> queryForList(RecordSet4Action paramRecordSet4Action, String paramString, Object... paramVarArgs) throws Exception {
/* 261 */     ArrayList<FnaSubjectBudget> arrayList = new ArrayList();
/* 262 */     boolean bool = paramRecordSet4Action.executeQuery(paramString, paramVarArgs);
/* 263 */     if (!bool) throw new FnaException("查询失败请联系管理员查询日志错误信息！"); 
/* 264 */     while (paramRecordSet4Action.next()) {
/* 265 */       FnaSubjectBudget fnaSubjectBudget = new FnaSubjectBudget();
/*     */       
/* 267 */       String str1 = paramRecordSet4Action.getString("id");
/* 268 */       fnaSubjectBudget.setId((str1 == null || "".equals(str1)) ? null : new Integer(Util.getIntValue(str1, 0)));
/*     */       
/* 270 */       String str2 = paramRecordSet4Action.getString("fnaSubjectbPk");
/* 271 */       fnaSubjectBudget.setFnaSubjectbPk(str2);
/*     */       
/* 273 */       String str3 = paramRecordSet4Action.getString("fnaPeriodPk");
/* 274 */       fnaSubjectBudget.setFnaPeriodPk(str3);
/*     */       
/* 276 */       String str4 = paramRecordSet4Action.getString("supFnaSubjectbPk");
/* 277 */       fnaSubjectBudget.setSupFnaSubjectbPk(str4);
/*     */       
/* 279 */       String str5 = paramRecordSet4Action.getString("fnaSubjectaPk");
/* 280 */       fnaSubjectBudget.setFnaSubjectaPk(str5);
/*     */       
/* 282 */       String str6 = paramRecordSet4Action.getString("subjectbName");
/* 283 */       fnaSubjectBudget.setSubjectbName(str6);
/*     */       
/* 285 */       String str7 = paramRecordSet4Action.getString("subjectbCode");
/* 286 */       fnaSubjectBudget.setSubjectbCode(str7);
/*     */       
/* 288 */       String str8 = paramRecordSet4Action.getString("lockedStatus");
/* 289 */       fnaSubjectBudget.setLockedStatus((str8 == null || "".equals(str8)) ? null : new Integer(Util.getIntValue(str8, 0)));
/*     */       
/* 291 */       String str9 = paramRecordSet4Action.getString("description");
/* 292 */       fnaSubjectBudget.setDescription(str9);
/*     */       
/* 294 */       String str10 = paramRecordSet4Action.getString("subjectbIsLeaf");
/* 295 */       fnaSubjectBudget.setSubjectbIsLeaf((str10 == null || "".equals(str10)) ? null : new Integer(Util.getIntValue(str10, 0)));
/*     */       
/* 297 */       String str11 = paramRecordSet4Action.getString("subjectbLevel");
/* 298 */       fnaSubjectBudget.setSubjectbLevel((str11 == null || "".equals(str11)) ? null : new Integer(Util.getIntValue(str11, 0)));
/*     */       
/* 300 */       String str12 = paramRecordSet4Action.getString("showOrder");
/* 301 */       fnaSubjectBudget.setShowOrder((str12 == null || "".equals(str12)) ? null : new Double(Util.getDoubleValue(str12, 0.0D)));
/*     */       
/* 303 */       arrayList.add(fnaSubjectBudget);
/*     */     } 
/* 305 */     return arrayList;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/fna/e9/dao/base/FnaSubjectBudgetDao.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */