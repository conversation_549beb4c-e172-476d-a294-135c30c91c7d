/*     */ package weaver.fna.e9.dao.base;
/*     */ 
/*     */ import java.text.DecimalFormat;
/*     */ import java.util.ArrayList;
/*     */ import java.util.List;
/*     */ import weaver.fna.e9.exception.FnaException;
/*     */ import weaver.fna.e9.po.base.FnaPeriodDtl;
/*     */ import weaver.fna.general.RecordSet4Action;
/*     */ import weaver.general.Util;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ @Deprecated
/*     */ public class FnaPeriodDtlDao
/*     */   extends FnaBaseDao
/*     */ {
/*     */   @Deprecated
/*     */   public void save(RecordSet4Action paramRecordSet4Action, FnaPeriodDtl paramFnaPeriodDtl) throws Exception {
/*  42 */     boolean bool = true;
/*  43 */     String str1 = "oralce".equals(paramRecordSet4Action.getDBType()) ? "NVL" : "ISNULL";
/*  44 */     String str2 = Util.null2String(paramFnaPeriodDtl.getFnaPeriodDtlPk()).trim();
/*  45 */     if ("".equals(str2)) {
/*  46 */       throw new FnaException("主键不能为空！");
/*     */     }
/*  48 */     StringBuffer stringBuffer = new StringBuffer();
/*  49 */     stringBuffer.append("insert into fnaPeriodDtl(");
/*  50 */     stringBuffer.append("fnaPeriodDtlPk");
/*  51 */     stringBuffer.append(" , ");
/*  52 */     stringBuffer.append("fnaPeriodPk");
/*  53 */     stringBuffer.append(" , ");
/*  54 */     stringBuffer.append("startdate");
/*  55 */     stringBuffer.append(" , ");
/*  56 */     stringBuffer.append("enddate");
/*  57 */     stringBuffer.append(" , ");
/*  58 */     stringBuffer.append("fnaPeriodsList");
/*  59 */     stringBuffer.append(" , ");
/*  60 */     stringBuffer.append("fnaPeriodsName");
/*  61 */     stringBuffer.append(" , ");
/*  62 */     stringBuffer.append("showOrder");
/*  63 */     stringBuffer.append(")values(");
/*  64 */     stringBuffer.append("?");
/*  65 */     stringBuffer.append(" , ");
/*  66 */     stringBuffer.append("?");
/*  67 */     stringBuffer.append(" , ");
/*  68 */     stringBuffer.append("?");
/*  69 */     stringBuffer.append(" , ");
/*  70 */     stringBuffer.append("?");
/*  71 */     stringBuffer.append(" , ");
/*  72 */     stringBuffer.append("?");
/*  73 */     stringBuffer.append(" , ");
/*  74 */     stringBuffer.append("?");
/*  75 */     stringBuffer.append(" , ");
/*  76 */     stringBuffer.append("?");
/*  77 */     stringBuffer.append(")");
/*  78 */     bool = paramRecordSet4Action.executeUpdate(stringBuffer.toString(), new Object[] { paramFnaPeriodDtl
/*  79 */           .getFnaPeriodDtlPk(), paramFnaPeriodDtl
/*  80 */           .getFnaPeriodPk(), paramFnaPeriodDtl
/*  81 */           .getStartdate(), paramFnaPeriodDtl
/*  82 */           .getEnddate(), paramFnaPeriodDtl
/*  83 */           .getFnaPeriodsList(), paramFnaPeriodDtl
/*  84 */           .getFnaPeriodsName(), 
/*  85 */           (paramFnaPeriodDtl.getShowOrder() == null) ? null : (new DecimalFormat("#.###")).format(paramFnaPeriodDtl.getShowOrder()) });
/*     */     
/*  87 */     if (!bool) throw new FnaException("保存数据失败请联系管理员查询日志错误信息！");
/*     */   
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void update(RecordSet4Action paramRecordSet4Action, FnaPeriodDtl paramFnaPeriodDtl) throws Exception {
/*  97 */     boolean bool = true;
/*  98 */     String str1 = "oralce".equals(paramRecordSet4Action.getDBType()) ? "NVL" : "ISNULL";
/*  99 */     String str2 = Util.null2String(paramFnaPeriodDtl.getFnaPeriodDtlPk()).trim();
/* 100 */     if ("".equals(str2)) {
/* 101 */       throw new FnaException("主键不能为空！");
/*     */     }
/* 103 */     StringBuffer stringBuffer = new StringBuffer();
/* 104 */     stringBuffer.append("update fnaPeriodDtl set ");
/* 105 */     stringBuffer.append("fnaPeriodPk=?");
/* 106 */     stringBuffer.append(" , ");
/* 107 */     stringBuffer.append("startdate=?");
/* 108 */     stringBuffer.append(" , ");
/* 109 */     stringBuffer.append("enddate=?");
/* 110 */     stringBuffer.append(" , ");
/* 111 */     stringBuffer.append("fnaPeriodsList=?");
/* 112 */     stringBuffer.append(" , ");
/* 113 */     stringBuffer.append("fnaPeriodsName=?");
/* 114 */     stringBuffer.append(" , ");
/* 115 */     stringBuffer.append("showOrder=?");
/* 116 */     stringBuffer.append(" where ");
/* 117 */     stringBuffer.append("fnaPeriodDtlPk=?");
/* 118 */     bool = paramRecordSet4Action.executeUpdate(stringBuffer.toString(), new Object[] { paramFnaPeriodDtl
/* 119 */           .getFnaPeriodPk(), paramFnaPeriodDtl
/* 120 */           .getStartdate(), paramFnaPeriodDtl
/* 121 */           .getEnddate(), paramFnaPeriodDtl
/* 122 */           .getFnaPeriodsList(), paramFnaPeriodDtl
/* 123 */           .getFnaPeriodsName(), 
/* 124 */           (paramFnaPeriodDtl.getShowOrder() == null) ? null : (new DecimalFormat("#.###")).format(paramFnaPeriodDtl.getShowOrder()), paramFnaPeriodDtl
/* 125 */           .getFnaPeriodDtlPk() });
/*     */     
/* 127 */     if (!bool) throw new FnaException("更新数据失败请联系管理员查询日志错误信息！");
/*     */   
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void saveOrUpdate(RecordSet4Action paramRecordSet4Action, FnaPeriodDtl paramFnaPeriodDtl) throws Exception {
/* 137 */     if (find(paramRecordSet4Action, paramFnaPeriodDtl.getFnaPeriodDtlPk()) == null) {
/* 138 */       save(paramRecordSet4Action, paramFnaPeriodDtl);
/*     */     } else {
/* 140 */       update(paramRecordSet4Action, paramFnaPeriodDtl);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public void delete(RecordSet4Action paramRecordSet4Action, List<FnaPeriodDtl> paramList) throws Exception {
/* 151 */     String str = "oralce".equals(paramRecordSet4Action.getDBType()) ? "NVL" : "ISNULL";
/* 152 */     int i = paramList.size();
/* 153 */     for (byte b = 0; b < i; b++) {
/* 154 */       boolean bool = true;
/* 155 */       FnaPeriodDtl fnaPeriodDtl = paramList.get(b);
/* 156 */       String str1 = fnaPeriodDtl.getFnaPeriodDtlPk();
/* 157 */       if (str1 == null) {
/* 158 */         throw new FnaException("主键不能为空！");
/*     */       }
/* 160 */       StringBuffer stringBuffer = new StringBuffer();
/* 161 */       stringBuffer.append("delete from fnaPeriodDtl where ");
/* 162 */       stringBuffer.append("fnaPeriodDtlPk=?");
/* 163 */       bool = paramRecordSet4Action.executeUpdate(stringBuffer.toString(), new Object[] { str1 });
/*     */ 
/*     */       
/* 166 */       if (!bool) throw new FnaException("删除数据失败请联系管理员查询日志错误信息！");
/*     */     
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public FnaPeriodDtl find(RecordSet4Action paramRecordSet4Action, String paramString) throws Exception {
/* 178 */     boolean bool = true;
/* 179 */     String str = "select * from fnaPeriodDtl where fnaPeriodDtlPk=?";
/* 180 */     bool = paramRecordSet4Action.executeQuery(str, new Object[] { paramString });
/*     */ 
/*     */     
/* 183 */     if (!bool) throw new FnaException("查询失败请联系管理员查询日志错误信息！"); 
/* 184 */     if (paramRecordSet4Action.next()) {
/* 185 */       FnaPeriodDtl fnaPeriodDtl = new FnaPeriodDtl();
/*     */       
/* 187 */       String str1 = paramRecordSet4Action.getString("id");
/* 188 */       fnaPeriodDtl.setId((str1 == null || "".equals(str1)) ? null : new Integer(Util.getIntValue(str1, 0)));
/*     */       
/* 190 */       String str2 = paramRecordSet4Action.getString("fnaPeriodDtlPk");
/* 191 */       fnaPeriodDtl.setFnaPeriodDtlPk(str2);
/*     */       
/* 193 */       String str3 = paramRecordSet4Action.getString("fnaPeriodPk");
/* 194 */       fnaPeriodDtl.setFnaPeriodPk(str3);
/*     */       
/* 196 */       String str4 = paramRecordSet4Action.getString("startdate");
/* 197 */       fnaPeriodDtl.setStartdate(str4);
/*     */       
/* 199 */       String str5 = paramRecordSet4Action.getString("enddate");
/* 200 */       fnaPeriodDtl.setEnddate(str5);
/*     */       
/* 202 */       String str6 = paramRecordSet4Action.getString("fnaPeriodsList");
/* 203 */       fnaPeriodDtl.setFnaPeriodsList((str6 == null || "".equals(str6)) ? null : new Integer(Util.getIntValue(str6, 0)));
/*     */       
/* 205 */       String str7 = paramRecordSet4Action.getString("fnaPeriodsName");
/* 206 */       fnaPeriodDtl.setFnaPeriodsName(str7);
/*     */       
/* 208 */       String str8 = paramRecordSet4Action.getString("showOrder");
/* 209 */       fnaPeriodDtl.setShowOrder((str8 == null || "".equals(str8)) ? null : new Double(Util.getDoubleValue(str8, 0.0D)));
/*     */       
/* 211 */       return fnaPeriodDtl;
/*     */     } 
/* 213 */     return null;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public List<FnaPeriodDtl> queryForList(RecordSet4Action paramRecordSet4Action, String paramString, Object... paramVarArgs) throws Exception {
/* 225 */     ArrayList<FnaPeriodDtl> arrayList = new ArrayList();
/* 226 */     boolean bool = paramRecordSet4Action.executeQuery(paramString, paramVarArgs);
/* 227 */     if (!bool) throw new FnaException("查询失败请联系管理员查询日志错误信息！"); 
/* 228 */     while (paramRecordSet4Action.next()) {
/* 229 */       FnaPeriodDtl fnaPeriodDtl = new FnaPeriodDtl();
/*     */       
/* 231 */       String str1 = paramRecordSet4Action.getString("id");
/* 232 */       fnaPeriodDtl.setId((str1 == null || "".equals(str1)) ? null : new Integer(Util.getIntValue(str1, 0)));
/*     */       
/* 234 */       String str2 = paramRecordSet4Action.getString("fnaPeriodDtlPk");
/* 235 */       fnaPeriodDtl.setFnaPeriodDtlPk(str2);
/*     */       
/* 237 */       String str3 = paramRecordSet4Action.getString("fnaPeriodPk");
/* 238 */       fnaPeriodDtl.setFnaPeriodPk(str3);
/*     */       
/* 240 */       String str4 = paramRecordSet4Action.getString("startdate");
/* 241 */       fnaPeriodDtl.setStartdate(str4);
/*     */       
/* 243 */       String str5 = paramRecordSet4Action.getString("enddate");
/* 244 */       fnaPeriodDtl.setEnddate(str5);
/*     */       
/* 246 */       String str6 = paramRecordSet4Action.getString("fnaPeriodsList");
/* 247 */       fnaPeriodDtl.setFnaPeriodsList((str6 == null || "".equals(str6)) ? null : new Integer(Util.getIntValue(str6, 0)));
/*     */       
/* 249 */       String str7 = paramRecordSet4Action.getString("fnaPeriodsName");
/* 250 */       fnaPeriodDtl.setFnaPeriodsName(str7);
/*     */       
/* 252 */       String str8 = paramRecordSet4Action.getString("showOrder");
/* 253 */       fnaPeriodDtl.setShowOrder((str8 == null || "".equals(str8)) ? null : new Double(Util.getDoubleValue(str8, 0.0D)));
/*     */       
/* 255 */       arrayList.add(fnaPeriodDtl);
/*     */     } 
/* 257 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public List<FnaPeriodDtl> queryFnaPeriodDtlByFk(RecordSet4Action paramRecordSet4Action, String paramString) throws Exception {
/* 268 */     boolean bool = true;
/* 269 */     ArrayList<FnaPeriodDtl> arrayList = new ArrayList();
/* 270 */     String str = "select * from fnaPeriodDtl where fnaPeriodPk=? order by showOrder asc";
/* 271 */     bool = paramRecordSet4Action.executeQuery(str, new Object[] { paramString });
/*     */ 
/*     */     
/* 274 */     if (!bool) throw new FnaException("查询失败请联系管理员查询日志错误信息！"); 
/* 275 */     while (paramRecordSet4Action.next()) {
/* 276 */       FnaPeriodDtl fnaPeriodDtl = new FnaPeriodDtl();
/*     */       
/* 278 */       String str1 = paramRecordSet4Action.getString("id");
/* 279 */       fnaPeriodDtl.setId((str1 == null || "".equals(str1)) ? null : new Integer(Util.getIntValue(str1, 0)));
/*     */       
/* 281 */       String str2 = paramRecordSet4Action.getString("fnaPeriodDtlPk");
/* 282 */       fnaPeriodDtl.setFnaPeriodDtlPk(str2);
/*     */       
/* 284 */       String str3 = paramRecordSet4Action.getString("fnaPeriodPk");
/* 285 */       fnaPeriodDtl.setFnaPeriodPk(str3);
/*     */       
/* 287 */       String str4 = paramRecordSet4Action.getString("startdate");
/* 288 */       fnaPeriodDtl.setStartdate(str4);
/*     */       
/* 290 */       String str5 = paramRecordSet4Action.getString("enddate");
/* 291 */       fnaPeriodDtl.setEnddate(str5);
/*     */       
/* 293 */       String str6 = paramRecordSet4Action.getString("fnaPeriodsList");
/* 294 */       fnaPeriodDtl.setFnaPeriodsList((str6 == null || "".equals(str6)) ? null : new Integer(Util.getIntValue(str6, 0)));
/*     */       
/* 296 */       String str7 = paramRecordSet4Action.getString("fnaPeriodsName");
/* 297 */       fnaPeriodDtl.setFnaPeriodsName(str7);
/*     */       
/* 299 */       String str8 = paramRecordSet4Action.getString("showOrder");
/* 300 */       fnaPeriodDtl.setShowOrder((str8 == null || "".equals(str8)) ? null : new Double(Util.getDoubleValue(str8, 0.0D)));
/*     */       
/* 302 */       arrayList.add(fnaPeriodDtl);
/*     */     } 
/* 304 */     return arrayList;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/fna/e9/dao/base/FnaPeriodDtlDao.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */