/*    */ package weaver.aes;
/*    */ 
/*    */ import java.io.IOException;
/*    */ import java.lang.reflect.InvocationTargetException;
/*    */ import java.lang.reflect.Method;
/*    */ import javax.servlet.Filter;
/*    */ import javax.servlet.FilterChain;
/*    */ import javax.servlet.FilterConfig;
/*    */ import javax.servlet.ServletException;
/*    */ import javax.servlet.ServletRequest;
/*    */ import javax.servlet.ServletResponse;
/*    */ import javax.servlet.http.HttpServletRequest;
/*    */ import javax.servlet.http.HttpServletResponse;
/*    */ import weaver.filter.ServerDetector;
/*    */ import weaver.general.BaseBean;
/*    */ 
/*    */ 
/*    */ public class AESFilter
/*    */   implements Filter
/*    */ {
/* 21 */   private static String serverId = "";
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public void destroy() {}
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public void doFilter(ServletRequest paramServletRequest, ServletResponse paramServletResponse, FilterChain paramFilterChain) throws IOException, ServletException {
/* 33 */     HttpServletRequest httpServletRequest = (HttpServletRequest)paramServletRequest;
/* 34 */     AES aES = new AES();
/* 35 */     if (aES.isEnable()) {
/* 36 */       RequestEncodingUtils.setCharacterEncoding(httpServletRequest);
/* 37 */       BaseBean baseBean = new BaseBean();
/* 38 */       Class<?> clazz = null;
/* 39 */       Method method = null;
/* 40 */       Object object = null;
/* 41 */       if (serverId == null || "".equals(serverId)) {
/* 42 */         serverId = ServerDetector.getServerId();
/* 43 */         baseBean.writeLog("The server is " + serverId);
/*    */       } 
/*    */       
/* 46 */       if ("resin3".equals(serverId)) {
/*    */         try {
/* 48 */           clazz = Class.forName("weaver.aes.webcontainer.RequestForResin3");
/* 49 */           object = clazz.newInstance();
/* 50 */           method = clazz.getMethod("doFilter", new Class[] { HttpServletRequest.class, HttpServletResponse.class, FilterChain.class });
/* 51 */         } catch (Exception exception) {
/* 52 */           baseBean.writeLog(exception);
/*    */         } 
/*    */       } else {
/*    */         try {
/* 56 */           clazz = Class.forName("weaver.aes.webcontainer.RequestForWeblogic");
/* 57 */           object = clazz.newInstance();
/* 58 */           method = clazz.getMethod("doFilter", new Class[] { HttpServletRequest.class, HttpServletResponse.class, FilterChain.class });
/* 59 */         } catch (Exception exception) {
/* 60 */           baseBean.writeLog(exception);
/*    */         } 
/*    */       } 
/*    */       
/*    */       try {
/* 65 */         method.invoke(object, new Object[] { httpServletRequest, paramServletResponse, paramFilterChain });
/* 66 */       } catch (IllegalArgumentException illegalArgumentException) {
/*    */         
/* 68 */         baseBean.writeLog(illegalArgumentException);
/* 69 */         throw new ServletException(illegalArgumentException);
/* 70 */       } catch (IllegalAccessException illegalAccessException) {
/*    */         
/* 72 */         baseBean.writeLog(illegalAccessException);
/* 73 */         throw new ServletException(illegalAccessException);
/* 74 */       } catch (InvocationTargetException invocationTargetException) {
/*    */         
/* 76 */         baseBean.writeLog(invocationTargetException);
/* 77 */         throw new ServletException(invocationTargetException);
/*    */       } 
/*    */     } else {
/*    */       
/* 81 */       paramFilterChain.doFilter(paramServletRequest, paramServletResponse);
/*    */     } 
/*    */   }
/*    */   
/*    */   public void init(FilterConfig paramFilterConfig) throws ServletException {}
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/aes/AESFilter.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */