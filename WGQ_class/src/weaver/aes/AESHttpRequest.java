/*    */ package weaver.aes;
/*    */ 
/*    */ import java.util.Collections;
/*    */ import java.util.Enumeration;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import javax.servlet.http.HttpServletRequest;
/*    */ import javax.servlet.http.HttpServletRequestWrapper;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class AESHttpRequest
/*    */   extends HttpServletRequestWrapper
/*    */ {
/*    */   private HttpServletRequest request;
/* 18 */   private Map parameters = null;
/*    */ 
/*    */ 
/*    */   
/*    */   public AESHttpRequest(HttpServletRequest paramHttpServletRequest) {
/* 23 */     super(paramHttpServletRequest);
/* 24 */     this.request = paramHttpServletRequest;
/*    */   }
/*    */   
/*    */   public String getParameter(String paramString) {
/* 28 */     String[] arrayOfString = getParameterValues(paramString);
/*    */ 
/*    */ 
/*    */     
/* 32 */     return (arrayOfString == null) ? null : arrayOfString[0];
/*    */   }
/*    */   
/*    */   public Map getParameterMap() {
/* 36 */     if (this.parameters != null) return this.parameters; 
/* 37 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 38 */     Map map = this.request.getParameterMap();
/* 39 */     AES aES = new AES();
/* 40 */     if (map != null) {
/* 41 */       String str = this.request.getRequestURI();
/* 42 */       for (String str1 : map.keySet()) {
/* 43 */         Object object = map.get(str1);
/* 44 */         if (object instanceof String[]) {
/* 45 */           String[] arrayOfString1 = (String[])object;
/* 46 */           String[] arrayOfString2 = new String[arrayOfString1.length];
/*    */           
/* 48 */           for (byte b = 0; b < arrayOfString1.length; b++) {
/* 49 */             if (arrayOfString1[b] != null) {
/* 50 */               arrayOfString2[b] = aES.decrypt(str1, arrayOfString1[b], this.request);
/*    */             } else {
/* 52 */               arrayOfString2[b] = arrayOfString1[b];
/*    */             } 
/*    */           } 
/* 55 */           hashMap.put(str1, arrayOfString2); continue;
/*    */         } 
/* 57 */         hashMap.put(str1, aES.decrypt(str1, (String)object, this.request));
/*    */       } 
/*    */     } 
/*    */     
/* 61 */     this.parameters = Collections.unmodifiableMap(hashMap);
/* 62 */     return this.parameters;
/*    */   }
/*    */   
/*    */   public Enumeration getParameterNames() {
/* 66 */     return this.request.getParameterNames();
/*    */   }
/*    */ 
/*    */   
/*    */   public String[] getParameterValues(String paramString) {
/* 71 */     Object object = null;
/*    */     try {
/* 73 */       object = getParameterMap().get(paramString);
/* 74 */     } catch (Exception exception) {
/* 75 */       object = null;
/*    */     } 
/* 77 */     return (object == null) ? null : (String[])object;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/aes/AESHttpRequest.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */