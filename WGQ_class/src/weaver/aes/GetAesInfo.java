/*    */ package weaver.aes;
/*    */ 
/*    */ import javax.servlet.http.HttpServlet;
/*    */ import javax.servlet.http.HttpServletRequest;
/*    */ import javax.servlet.http.HttpServletResponse;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class GetAesInfo
/*    */   extends HttpServlet
/*    */ {
/*    */   private static final long serialVersionUID = -6553700966877955786L;
/*    */   
/*    */   public void doGet(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/* 21 */     doPost(paramHttpServletRequest, paramHttpServletResponse);
/*    */   }
/*    */   
/*    */   public void doPost(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {}
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/aes/GetAesInfo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */