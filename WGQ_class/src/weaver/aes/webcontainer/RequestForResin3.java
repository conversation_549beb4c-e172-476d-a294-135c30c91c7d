/*    */ package weaver.aes.webcontainer;
/*    */ 
/*    */ import com.caucho.server.connection.CauchoRequest;
/*    */ import java.io.IOException;
/*    */ import javax.servlet.FilterChain;
/*    */ import javax.servlet.ServletException;
/*    */ import javax.servlet.ServletRequest;
/*    */ import javax.servlet.ServletResponse;
/*    */ import javax.servlet.http.HttpServletRequest;
/*    */ import javax.servlet.http.HttpServletResponse;
/*    */ import org.apache.commons.logging.Log;
/*    */ import org.apache.commons.logging.LogFactory;
/*    */ import weaver.aes.AESHttpRequestForResin;
/*    */ import weaver.aes.AESHttpResponse;
/*    */ 
/*    */ 
/*    */ 
/*    */ public class RequestForResin3
/*    */ {
/* 20 */   private static final Log logger = LogFactory.getLog(RequestForResin3.class);
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public void doFilter(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse, FilterChain paramFilterChain) throws IOException, ServletException {
/* 26 */     String str1 = paramHttpServletRequest.getRequestURI();
/*    */     
/* 28 */     if (str1 == null) str1 = "";
/*    */     
/* 30 */     String str2 = str1.toLowerCase();
/*    */     
/* 32 */     if (str2.endsWith(".cur") || str2.endsWith(".ico") || str2.endsWith(".css") || str2.endsWith(".js") || str2.endsWith(".htm") || str2.endsWith(".html") || str2.endsWith(".png") || str2.endsWith(".jpg") || str2.endsWith(".gif")) {
/*    */       
/* 34 */       paramFilterChain.doFilter((ServletRequest)paramHttpServletRequest, (ServletResponse)paramHttpServletResponse);
/*    */       
/*    */       return;
/*    */     } 
/* 38 */     AESHttpRequestForResin aESHttpRequestForResin = new AESHttpRequestForResin((CauchoRequest)paramHttpServletRequest);
/* 39 */     AESHttpResponse aESHttpResponse = new AESHttpResponse(paramHttpServletRequest, paramHttpServletResponse);
/*    */     
/* 41 */     paramFilterChain.doFilter((ServletRequest)aESHttpRequestForResin, (ServletResponse)aESHttpResponse);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/aes/webcontainer/RequestForResin3.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */