/*    */ package weaver.aes.webcontainer;
/*    */ 
/*    */ import java.io.IOException;
/*    */ import javax.servlet.FilterChain;
/*    */ import javax.servlet.ServletException;
/*    */ import javax.servlet.ServletRequest;
/*    */ import javax.servlet.ServletResponse;
/*    */ import javax.servlet.http.HttpServletRequest;
/*    */ import javax.servlet.http.HttpServletResponse;
/*    */ import org.apache.commons.logging.Log;
/*    */ import org.apache.commons.logging.LogFactory;
/*    */ import weaver.aes.AESHttpRequest;
/*    */ import weaver.aes.AESHttpResponse;
/*    */ import weaver.session.WSessionClusterFilter;
/*    */ 
/*    */ 
/*    */ public class RequestForWeblogic
/*    */ {
/* 19 */   private static final Log logger = LogFactory.getLog(WSessionClusterFilter.class);
/*    */ 
/*    */ 
/*    */   
/*    */   public void doFilter(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse, FilterChain paramFilterChain) throws IOException, ServletException {
/* 24 */     String str1 = paramHttpServletRequest.getRequestURI();
/*    */     
/* 26 */     if (str1 == null) str1 = "";
/*    */     
/* 28 */     String str2 = str1.toLowerCase();
/*    */     
/* 30 */     if (str2.endsWith(".cur") || str2.endsWith(".ico") || str2.endsWith(".css") || str2.endsWith(".js") || str2.endsWith(".htm") || str2.endsWith(".html") || str2.endsWith(".png") || str2.endsWith(".jpg") || str2.endsWith(".gif")) {
/*    */       
/* 32 */       paramFilterChain.doFilter((ServletRequest)paramHttpServletRequest, (ServletResponse)paramHttpServletResponse);
/*    */       return;
/*    */     } 
/* 35 */     AESHttpRequest aESHttpRequest = new AESHttpRequest(paramHttpServletRequest);
/* 36 */     AESHttpResponse aESHttpResponse = new AESHttpResponse(paramHttpServletRequest, paramHttpServletResponse);
/*    */     
/* 38 */     paramFilterChain.doFilter((ServletRequest)aESHttpRequest, (ServletResponse)aESHttpResponse);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/aes/webcontainer/RequestForWeblogic.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */