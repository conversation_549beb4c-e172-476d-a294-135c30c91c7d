/*     */ package weaver.aes;
/*     */ 
/*     */ import java.util.List;
/*     */ import java.util.UUID;
/*     */ import java.util.concurrent.CopyOnWriteArrayList;
/*     */ import javax.crypto.Cipher;
/*     */ import javax.crypto.spec.IvParameterSpec;
/*     */ import javax.crypto.spec.SecretKeySpec;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import net.sf.json.JSONArray;
/*     */ import net.sf.json.JSONObject;
/*     */ import org.apache.commons.lang3.StringUtils;
/*     */ import weaver.file.FileUpload;
/*     */ import weaver.file.Prop;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.rsa.security.Base64;
/*     */ import weaver.rsa.security.RSA;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class AES
/*     */   extends BaseBean
/*     */ {
/*  30 */   private static String aesKey = null;
/*     */   
/*  32 */   private static String iv = null;
/*     */   
/*     */   private static final String ALGORITHM = "AES/CBC/NoPadding";
/*     */   
/*  36 */   private String encoding = "UTF-8";
/*     */   
/*     */   protected static final String KEY = "rsaAes01";
/*     */   
/*     */   protected static final String IV_KEY = "rsaAes02";
/*     */   
/*  42 */   private static Object lock = new Object();
/*     */   
/*  44 */   private static String status = null;
/*     */   
/*     */   private static boolean returnOriDataIfDecryptFailed = false;
/*     */   
/*  48 */   private static List<String> urlPattern = new CopyOnWriteArrayList<>();
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void initAesKey() {
/*  54 */     if (aesKey == null || "".equals(aesKey)) {
/*  55 */       BaseBean baseBean = new BaseBean();
/*  56 */       synchronized (lock) {
/*  57 */         String str = Util.null2String(baseBean.getPropValue("weaver_security_aes", "key"));
/*  58 */         if ("".equals(str)) {
/*  59 */           str = UUID.randomUUID().toString().substring(0, 16);
/*  60 */           Prop.setPropValue("weaver_security_aes", "key", str);
/*     */         } 
/*  62 */         aesKey = str;
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void initIvKey() {
/*  71 */     if (iv == null || "".equals(iv)) {
/*  72 */       BaseBean baseBean = new BaseBean();
/*  73 */       synchronized (lock) {
/*  74 */         String str = Util.null2String(baseBean.getPropValue("weaver_security_aes", "iv"));
/*  75 */         if ("".equals(str)) {
/*  76 */           str = UUID.randomUUID().toString().substring(0, 16);
/*  77 */           Prop.setPropValue("weaver_security_aes", "iv", str);
/*     */         } 
/*  79 */         iv = str;
/*     */       } 
/*     */     } 
/*     */   }
/*     */   
/*     */   static {
/*  85 */     initAesParams();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static void initAesParams() {
/*  92 */     BaseBean baseBean = new BaseBean();
/*  93 */     status = Util.null2String(baseBean.getPropValue("weaver_security_aes", "status"));
/*  94 */     returnOriDataIfDecryptFailed = "true".equals(Util.null2String(baseBean.getPropValue("weaver_security_aes", "returnOriDataIfDecryptFailed")));
/*  95 */     String str = Util.null2String(baseBean.getPropValue("weaver_security_aes", "urlPattern"));
/*  96 */     if (!"".equals(str)) {
/*  97 */       String[] arrayOfString = str.split(";");
/*  98 */       for (byte b = 0; b < arrayOfString.length; b++) {
/*  99 */         String str1 = Util.null2String(arrayOfString[b]).toLowerCase();
/* 100 */         if (!urlPattern.contains(str1)) {
/* 101 */           urlPattern.add(str1);
/*     */         }
/*     */       } 
/*     */     } else {
/* 105 */       urlPattern.add("/api/");
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean isEnable() {
/* 115 */     return "true".equals(status);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public JSONArray getUrlPattern() {
/* 123 */     JSONArray jSONArray = new JSONArray();
/* 124 */     for (String str : urlPattern) {
/* 125 */       JSONObject jSONObject = new JSONObject();
/* 126 */       jSONObject.put("url", str);
/* 127 */       jSONArray.add(jSONObject);
/*     */     } 
/* 129 */     return jSONArray;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean isNeedDecrypt(String paramString) {
/* 138 */     if (!isEnable() || paramString == null || "".equals(paramString)) return false;
/*     */     
/* 140 */     for (String str : urlPattern) {
/* 141 */       if (paramString.toLowerCase().contains(str)) {
/* 142 */         return true;
/*     */       }
/*     */     } 
/* 145 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getAesKey() {
/* 153 */     initAesKey();
/* 154 */     return aesKey;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getIvKey() {
/* 162 */     initIvKey();
/* 163 */     return iv;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String encrypt(String paramString) {
/* 174 */     return encrypt(paramString, aesKey, iv);
/*     */   }
/*     */   public String encrypt(String paramString1, String paramString2, String paramString3) {
/*     */     try {
/* 178 */       initAesKey();
/* 179 */       initIvKey();
/* 180 */       Cipher cipher = Cipher.getInstance("AES/CBC/NoPadding");
/* 181 */       int i = cipher.getBlockSize();
/*     */       
/* 183 */       byte[] arrayOfByte1 = paramString1.getBytes(getEncoding());
/* 184 */       int j = arrayOfByte1.length;
/* 185 */       if (j % i != 0) {
/* 186 */         j += i - j % i;
/*     */       }
/*     */       
/* 189 */       byte[] arrayOfByte2 = new byte[j];
/* 190 */       System.arraycopy(arrayOfByte1, 0, arrayOfByte2, 0, arrayOfByte1.length);
/*     */       
/* 192 */       SecretKeySpec secretKeySpec = new SecretKeySpec(paramString2.getBytes(), "AES");
/* 193 */       IvParameterSpec ivParameterSpec = new IvParameterSpec(paramString3.getBytes());
/*     */       
/* 195 */       cipher.init(1, secretKeySpec, ivParameterSpec);
/* 196 */       byte[] arrayOfByte3 = cipher.doFinal(arrayOfByte2);
/*     */       
/* 198 */       return (new Base64()).encodeToString(arrayOfByte3);
/*     */     }
/* 200 */     catch (Exception exception) {
/*     */       
/* 202 */       writeLog(exception);
/* 203 */       return null;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String decrypt(String paramString1, String paramString2, HttpServletRequest paramHttpServletRequest) {
/* 215 */     if (!isNeedDecrypt(paramHttpServletRequest.getRequestURI())) return paramString2; 
/* 216 */     String str1 = paramHttpServletRequest.getParameter("rsaAes01");
/* 217 */     String str2 = paramHttpServletRequest.getParameter("rsaAes02");
/* 218 */     if (str1 != null && str2 != null) {
/* 219 */       RSA rSA = new RSA();
/* 220 */       str1 = rSA.decryptCommon(paramHttpServletRequest, str1);
/* 221 */       str2 = rSA.decryptCommon(paramHttpServletRequest, str2);
/*     */     } 
/* 223 */     return decrypt(paramString1, paramString2, str1, str2, paramHttpServletRequest.getRequestURI());
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String decrypt(String paramString1, String paramString2, HttpServletRequest paramHttpServletRequest, FileUpload paramFileUpload) {
/* 236 */     if (!isNeedDecrypt(paramHttpServletRequest.getRequestURI())) return paramString2; 
/* 237 */     String str1 = paramFileUpload.getParameter("rsaAes01");
/* 238 */     String str2 = paramFileUpload.getParameter("rsaAes02");
/* 239 */     if (str1 != null && str2 != null) {
/* 240 */       RSA rSA = new RSA();
/* 241 */       str1 = rSA.decryptCommon(paramHttpServletRequest, str1);
/* 242 */       str2 = rSA.decryptCommon(paramHttpServletRequest, str2);
/*     */     } 
/* 244 */     return decrypt(paramString1, paramString2, str1, str2, paramHttpServletRequest.getRequestURI());
/*     */   }
/*     */   
/*     */   public String decrypt(String paramString1, String paramString2, String paramString3, String paramString4, String paramString5) {
/* 248 */     if ("rsaAes01".equals(paramString1) || "rsaAes02".equals(paramString1)) {
/* 249 */       return paramString2;
/*     */     }
/* 251 */     if (paramString2 == null || "".equals(paramString2.trim())) return paramString2; 
/* 252 */     if (!isNeedDecrypt(paramString5)) return paramString2; 
/*     */     try {
/* 254 */       initAesKey();
/* 255 */       initIvKey();
/* 256 */       if (paramString3 == null) {
/* 257 */         paramString3 = aesKey;
/*     */       }
/* 259 */       if (paramString4 == null) paramString4 = iv; 
/* 260 */       SecretKeySpec secretKeySpec = new SecretKeySpec(paramString3.getBytes(), "AES");
/*     */       
/* 262 */       Cipher cipher = Cipher.getInstance("AES/CBC/NoPadding");
/* 263 */       IvParameterSpec ivParameterSpec = new IvParameterSpec(paramString4.getBytes());
/* 264 */       cipher.init(2, secretKeySpec, ivParameterSpec);
/*     */ 
/*     */       
/* 267 */       byte[] arrayOfByte = cipher.doFinal(base64Decode(paramString2));
/*     */       
/* 269 */       return StringUtils.replace(new String(arrayOfByte, getEncoding()), "\000", "");
/* 270 */     } catch (Exception exception) {
/*     */       
/* 272 */       writeLog(">>>data decrypt failed: param is " + paramString1 + "\n content is " + paramString2 + "\n key is " + paramString3 + "\n ivKey is " + paramString4 + "\n url is " + paramString5);
/* 273 */       writeLog(exception);
/* 274 */       if (returnOriDataIfDecryptFailed) {
/* 275 */         return paramString2;
/*     */       }
/*     */       
/* 278 */       return null;
/*     */     } 
/*     */   }
/*     */   public byte[] base64Decode(String paramString) {
/* 282 */     Base64 base64 = new Base64();
/* 283 */     return base64.decode(paramString);
/*     */   }
/*     */ 
/*     */   
/*     */   public String getEncoding() {
/* 288 */     if (this.encoding == null || "".equals(this.encoding)) {
/* 289 */       this.encoding = "UTF-8";
/*     */     }
/* 291 */     return this.encoding;
/*     */   }
/*     */   
/*     */   public void setEncoding(String paramString) {
/* 295 */     this.encoding = paramString;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/aes/AES.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */