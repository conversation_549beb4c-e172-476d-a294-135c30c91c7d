/*     */ package weaver.aes;
/*     */ 
/*     */ import com.caucho.server.connection.CauchoRequest;
/*     */ import com.caucho.server.webapp.WebApp;
/*     */ import com.caucho.vfs.ReadStream;
/*     */ import java.io.BufferedReader;
/*     */ import java.io.IOException;
/*     */ import java.io.UnsupportedEncodingException;
/*     */ import java.security.Principal;
/*     */ import java.util.Collections;
/*     */ import java.util.Enumeration;
/*     */ import java.util.HashMap;
/*     */ import java.util.Locale;
/*     */ import java.util.Map;
/*     */ import javax.servlet.RequestDispatcher;
/*     */ import javax.servlet.ServletException;
/*     */ import javax.servlet.ServletInputStream;
/*     */ import javax.servlet.http.Cookie;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpSession;
/*     */ import org.apache.commons.logging.Log;
/*     */ import org.apache.commons.logging.LogFactory;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class AESHttpRequestForResin
/*     */   implements CauchoRequest
/*     */ {
/*  31 */   private static final Log logger = LogFactory.getLog(AESHttpRequestForResin.class);
/*     */   
/*     */   protected CauchoRequest request;
/*     */   
/*  35 */   private Map parameters = null;
/*     */ 
/*     */   
/*     */   public AESHttpRequestForResin(CauchoRequest paramCauchoRequest) {
/*  39 */     this.request = paramCauchoRequest;
/*     */   }
/*     */ 
/*     */   
/*     */   public String getParameter(String paramString) {
/*  44 */     String[] arrayOfString = getParameterValues(paramString);
/*     */ 
/*     */ 
/*     */     
/*  48 */     return (arrayOfString == null) ? null : arrayOfString[0];
/*     */   }
/*     */   
/*     */   public Map getParameterMap() {
/*  52 */     if (this.parameters != null) return this.parameters; 
/*  53 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  54 */     Map map = this.request.getParameterMap();
/*  55 */     AES aES = new AES();
/*  56 */     if (map != null) {
/*  57 */       String str = this.request.getRequestURI();
/*  58 */       for (String str1 : map.keySet()) {
/*  59 */         Object object = map.get(str1);
/*  60 */         if (object instanceof String[]) {
/*  61 */           String[] arrayOfString1 = (String[])object;
/*  62 */           String[] arrayOfString2 = new String[arrayOfString1.length];
/*     */           
/*  64 */           for (byte b = 0; b < arrayOfString1.length; b++) {
/*  65 */             if (arrayOfString1[b] != null) {
/*  66 */               arrayOfString2[b] = aES.decrypt(str1, arrayOfString1[b], (HttpServletRequest)this.request);
/*     */             } else {
/*  68 */               arrayOfString2[b] = arrayOfString1[b];
/*     */             } 
/*     */           } 
/*  71 */           hashMap.put(str1, arrayOfString2); continue;
/*     */         } 
/*  73 */         hashMap.put(str1, aES.decrypt(str1, (String)object, (HttpServletRequest)this.request));
/*     */       } 
/*     */     } 
/*     */     
/*  77 */     this.parameters = Collections.unmodifiableMap(hashMap);
/*  78 */     return this.parameters;
/*     */   }
/*     */   
/*     */   public Enumeration getParameterNames() {
/*  82 */     return this.request.getParameterNames();
/*     */   }
/*     */ 
/*     */   
/*     */   public String[] getParameterValues(String paramString) {
/*  87 */     Object object = null;
/*     */     try {
/*  89 */       object = getParameterMap().get(paramString);
/*  90 */     } catch (Exception exception) {
/*  91 */       object = null;
/*     */     } 
/*  93 */     return (object == null) ? null : (String[])object;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean allowKeepalive() {
/* 138 */     return this.request.allowKeepalive();
/*     */   }
/*     */   
/*     */   public boolean authenticate() throws ServletException, IOException {
/* 142 */     return this.request.authenticate();
/*     */   }
/*     */   
/*     */   public Object getAttribute(String paramString) {
/* 146 */     return this.request.getAttribute(paramString);
/*     */   }
/*     */   
/*     */   public Enumeration getAttributeNames() {
/* 150 */     return this.request.getAttributeNames();
/*     */   }
/*     */   
/*     */   public String getAuthType() {
/* 154 */     return this.request.getAuthType();
/*     */   }
/*     */   
/*     */   public String getCharacterEncoding() {
/* 158 */     return this.request.getCharacterEncoding();
/*     */   }
/*     */   
/*     */   public int getContentLength() {
/* 162 */     return this.request.getContentLength();
/*     */   }
/*     */   
/*     */   public String getContentType() {
/* 166 */     return this.request.getContentType();
/*     */   }
/*     */   
/*     */   public String getContextPath() {
/* 170 */     return this.request.getContextPath();
/*     */   }
/*     */   
/*     */   public Cookie getCookie(String paramString) {
/* 174 */     return this.request.getCookie(paramString);
/*     */   }
/*     */   
/*     */   public Cookie[] getCookies() {
/* 178 */     return this.request.getCookies();
/*     */   }
/*     */   
/*     */   public long getDateHeader(String paramString) {
/* 182 */     return this.request.getDateHeader(paramString);
/*     */   }
/*     */   
/*     */   public boolean getHasCookie() {
/* 186 */     return this.request.getHasCookie();
/*     */   }
/*     */   
/*     */   public String getHeader(String paramString) {
/* 190 */     return this.request.getHeader(paramString);
/*     */   }
/*     */   
/*     */   public Enumeration getHeaderNames() {
/* 194 */     return this.request.getHeaderNames();
/*     */   }
/*     */   
/*     */   public Enumeration getHeaders(String paramString) {
/* 198 */     return this.request.getHeaders(paramString);
/*     */   }
/*     */   
/*     */   public ServletInputStream getInputStream() throws IOException {
/* 202 */     return this.request.getInputStream();
/*     */   }
/*     */   
/*     */   public int getIntHeader(String paramString) {
/* 206 */     return this.request.getIntHeader(paramString);
/*     */   }
/*     */   
/*     */   public String getLocalAddr() {
/* 210 */     return this.request.getLocalAddr();
/*     */   }
/*     */   
/*     */   public Locale getLocale() {
/* 214 */     return this.request.getLocale();
/*     */   }
/*     */   
/*     */   public Enumeration getLocales() {
/* 218 */     return this.request.getLocales();
/*     */   }
/*     */   
/*     */   public String getLocalName() {
/* 222 */     return this.request.getLocalName();
/*     */   }
/*     */   
/*     */   public int getLocalPort() {
/* 226 */     return this.request.getLocalPort();
/*     */   }
/*     */   
/*     */   public HttpSession getMemorySession() {
/* 230 */     return this.request.getMemorySession();
/*     */   }
/*     */   
/*     */   public String getMethod() {
/* 234 */     return this.request.getMethod();
/*     */   }
/*     */   
/*     */   public String getPageContextPath() {
/* 238 */     return this.request.getPageContextPath();
/*     */   }
/*     */   
/*     */   public String getPagePathInfo() {
/* 242 */     return this.request.getPagePathInfo();
/*     */   }
/*     */   
/*     */   public String getPageQueryString() {
/* 246 */     return this.request.getPageQueryString();
/*     */   }
/*     */   
/*     */   public String getPageServletPath() {
/* 250 */     return this.request.getPageServletPath();
/*     */   }
/*     */   
/*     */   public String getPageURI() {
/* 254 */     return this.request.getPageURI();
/*     */   }
/*     */ 
/*     */   
/*     */   public String getPathInfo() {
/* 259 */     return this.request.getPathInfo();
/*     */   }
/*     */   
/*     */   public String getPathTranslated() {
/* 263 */     return this.request.getPathTranslated();
/*     */   }
/*     */   
/*     */   public String getProtocol() {
/* 267 */     return this.request.getProtocol();
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public BufferedReader getReader() throws IOException, IllegalStateException {
/* 273 */     return this.request.getReader();
/*     */   }
/*     */   
/*     */   public String getRealPath(String paramString) {
/* 277 */     return this.request.getRealPath(paramString);
/*     */   }
/*     */   
/*     */   public String getRemoteAddr() {
/* 281 */     return this.request.getRemoteAddr();
/*     */   }
/*     */   
/*     */   public String getRemoteHost() {
/* 285 */     return this.request.getRemoteHost();
/*     */   }
/*     */   
/*     */   public int getRemotePort() {
/* 289 */     return this.request.getRemotePort();
/*     */   }
/*     */   
/*     */   public String getRemoteUser() {
/* 293 */     return this.request.getRemoteUser();
/*     */   }
/*     */   
/*     */   public int getRequestDepth(int paramInt) {
/* 297 */     return this.request.getRequestDepth(paramInt);
/*     */   }
/*     */   
/*     */   public RequestDispatcher getRequestDispatcher(String paramString) {
/* 301 */     return this.request.getRequestDispatcher(paramString);
/*     */   }
/*     */   
/*     */   public String getRequestedSessionId() {
/* 305 */     return this.request.getRequestedSessionId();
/*     */   }
/*     */   
/*     */   public String getRequestURI() {
/* 309 */     return this.request.getRequestURI();
/*     */   }
/*     */   
/*     */   public StringBuffer getRequestURL() {
/* 313 */     return this.request.getRequestURL();
/*     */   }
/*     */   
/*     */   public String getScheme() {
/* 317 */     return this.request.getScheme();
/*     */   }
/*     */   
/*     */   public String getServerName() {
/* 321 */     return this.request.getServerName();
/*     */   }
/*     */   
/*     */   public int getServerPort() {
/* 325 */     return this.request.getServerPort();
/*     */   }
/*     */   
/*     */   public String getServletPath() {
/* 329 */     return this.request.getServletPath();
/*     */   }
/*     */   
/*     */   public HttpSession getSession() {
/* 333 */     return this.request.getSession();
/*     */   }
/*     */   
/*     */   public HttpSession getSession(boolean paramBoolean) {
/* 337 */     return this.request.getSession(paramBoolean);
/*     */   }
/*     */   
/*     */   public ReadStream getStream() throws IOException {
/* 341 */     return this.request.getStream();
/*     */   }
/*     */   
/*     */   public Principal getUserPrincipal() {
/* 345 */     return this.request.getUserPrincipal();
/*     */   }
/*     */   
/*     */   public String getVaryCookie() {
/* 349 */     return this.request.getVaryCookie();
/*     */   }
/*     */   
/*     */   public boolean getVaryCookies() {
/* 353 */     return this.request.getVaryCookies();
/*     */   }
/*     */   
/*     */   public WebApp getWebApp() {
/* 357 */     return this.request.getWebApp();
/*     */   }
/*     */   
/*     */   public boolean isRequestedSessionIdFromCookie() {
/* 361 */     return this.request.isRequestedSessionIdFromCookie();
/*     */   }
/*     */   
/*     */   public boolean isRequestedSessionIdFromUrl() {
/* 365 */     return this.request.isRequestedSessionIdFromUrl();
/*     */   }
/*     */   
/*     */   public boolean isRequestedSessionIdFromURL() {
/* 369 */     return this.request.isRequestedSessionIdFromURL();
/*     */   }
/*     */   
/*     */   public boolean isRequestedSessionIdValid() {
/* 373 */     return this.request.isRequestedSessionIdValid();
/*     */   }
/*     */   
/*     */   public boolean isSecure() {
/* 377 */     return this.request.isSecure();
/*     */   }
/*     */   
/*     */   public boolean isTop() {
/* 381 */     return this.request.isTop();
/*     */   }
/*     */   
/*     */   public boolean isUserInRole(String paramString) {
/* 385 */     return this.request.isUserInRole(paramString);
/*     */   }
/*     */   
/*     */   public void killKeepalive() {
/* 389 */     this.request.killKeepalive();
/*     */   }
/*     */   
/*     */   public void removeAttribute(String paramString) {
/* 393 */     this.request.removeAttribute(paramString);
/*     */   }
/*     */   
/*     */   public void setAttribute(String paramString, Object paramObject) {
/* 397 */     this.request.setAttribute(paramString, paramObject);
/*     */   }
/*     */   
/*     */   public void setCharacterEncoding(String paramString) throws UnsupportedEncodingException {
/* 401 */     this.request.setCharacterEncoding(paramString);
/*     */   }
/*     */   
/*     */   public void setHasCookie() {
/* 405 */     this.request.setHasCookie();
/*     */   }
/*     */   
/*     */   public void setHeader(String paramString1, String paramString2) {
/* 409 */     this.request.setHeader(paramString1, paramString2);
/*     */   }
/*     */   
/*     */   public void setVaryCookie(String paramString) {
/* 413 */     this.request.setVaryCookie(paramString);
/*     */   }
/*     */ 
/*     */   
/*     */   public String getQueryString() {
/* 418 */     return this.request.getQueryString();
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/aes/AESHttpRequestForResin.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */