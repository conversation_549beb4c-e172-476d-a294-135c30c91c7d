/*    */ package weaver.aes;
/*    */ 
/*    */ import java.io.IOException;
/*    */ import javax.servlet.http.HttpServletRequest;
/*    */ import javax.servlet.http.HttpServletResponse;
/*    */ import javax.servlet.http.HttpServletResponseWrapper;
/*    */ 
/*    */ public class AESHttpResponse
/*    */   extends HttpServletResponseWrapper {
/*    */   private HttpServletResponse response;
/*    */   private HttpServletRequest request;
/*    */   
/*    */   public AESHttpResponse(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/* 14 */     super(paramHttpServletResponse);
/* 15 */     this.request = paramHttpServletRequest;
/* 16 */     this.response = paramHttpServletResponse;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public void sendRedirect(String paramString) throws IOException {
/* 26 */     String str1 = null;
/* 27 */     String str2 = null;
/* 28 */     if (paramString.indexOf("rsaAes01") == -1) {
/* 29 */       str1 = this.request.getParameter("rsaAes01");
/*    */     }
/* 31 */     if (paramString.indexOf("rsaAes02") == -1) {
/* 32 */       str2 = this.request.getParameter("rsaAes02");
/*    */     }
/* 34 */     if (str1 != null && str2 != null && !"".equals(str1) && !"".equals(str2))
/*    */     {
/* 36 */       if (paramString.indexOf("?") > 0) {
/* 37 */         paramString = paramString + "&" + "rsaAes01" + "=" + str1 + "&" + "rsaAes02" + "=" + str2;
/*    */       } else {
/* 39 */         paramString = paramString + "?" + "rsaAes01" + "=" + str1 + "&" + "rsaAes02" + "=" + str2;
/*    */       } 
/*    */     }
/* 42 */     this.response.sendRedirect(paramString);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/aes/AESHttpResponse.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */