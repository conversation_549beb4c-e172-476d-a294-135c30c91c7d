/*    */ package weaver.aes;
/*    */ 
/*    */ import java.io.UnsupportedEncodingException;
/*    */ import javax.servlet.http.HttpServletRequest;
/*    */ import weaver.filter.XssUtil;
/*    */ import weaver.general.BaseBean;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class RequestEncodingUtils
/*    */ {
/*    */   public static void setCharacterEncoding(HttpServletRequest paramHttpServletRequest) {
/* 17 */     String str1 = paramHttpServletRequest.getCharacterEncoding();
/* 18 */     XssUtil xssUtil = new XssUtil();
/* 19 */     String str2 = paramHttpServletRequest.getRequestURI();
/* 20 */     String str3 = xssUtil.getSpecialEncodingPath(str2);
/* 21 */     str1 = str3;
/* 22 */     if (str1 == null) {
/* 23 */       str1 = xssUtil.null2String(xssUtil.getRule().get("encoding"));
/* 24 */       if (!xssUtil.getIsInitSuccess()) {
/* 25 */         str1 = xssUtil.getFuEncoding();
/*    */       }
/*    */     } 
/* 28 */     boolean bool1 = xssUtil.isEncodingExcept(str2);
/* 29 */     boolean bool2 = xssUtil.isSkipAnyCheck(str2);
/* 30 */     if (!bool2 && 
/* 31 */       !str1.equals("")) {
/* 32 */       String str = xssUtil.null2String(paramHttpServletRequest.getHeader("X-Requested-With"));
/* 33 */       if (str.equals("XMLHttpRequest") && str3 == null) {
/* 34 */         str1 = "UTF-8";
/*    */       }
/* 36 */       if (!bool1) {
/* 37 */         String str4 = paramHttpServletRequest.getCharacterEncoding();
/*    */ 
/*    */ 
/*    */         
/* 41 */         if (str.equals("XMLHttpRequest") && str4 == null) {
/* 42 */           str4 = "UTF-8";
/* 43 */         } else if (str4 == null) {
/* 44 */           str4 = str1;
/*    */         } 
/* 46 */         xssUtil.getRule().put("encoding_" + Thread.currentThread().getId() + "_" + Thread.currentThread().hashCode(), str4);
/*    */         try {
/* 48 */           paramHttpServletRequest.setCharacterEncoding(str1);
/* 49 */         } catch (UnsupportedEncodingException unsupportedEncodingException) {
/*    */           
/* 51 */           (new BaseBean()).writeLog(unsupportedEncodingException);
/*    */         } 
/*    */       } 
/*    */     } 
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/aes/RequestEncodingUtils.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */