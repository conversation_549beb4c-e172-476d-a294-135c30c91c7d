/*     */ package weaver.app;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ import weaver.cache.CacheBase;
/*     */ import weaver.cache.CacheColumn;
/*     */ import weaver.cache.CacheColumnType;
/*     */ import weaver.cache.PKColumn;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class AppKeyPathCominfo
/*     */   extends CacheBase
/*     */   implements Serializable
/*     */ {
/*  27 */   protected static String TABLE_NAME = "AppKeyPath";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  33 */   protected static String TABLE_WHERE = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  40 */   protected static String TABLE_ORDER = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @PKColumn(type = CacheColumnType.NUMBER)
/*  47 */   protected static String PK_NAME = "id";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @CacheColumn
/*     */   protected static int appid;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @CacheColumn
/*     */   protected static int keyPath;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getAssetNum() {
/*  68 */     return size();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setTofirstRow() {
/*  75 */     super.setTofirstRow();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean next() {
/*  82 */     return super.next();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getId() {
/*  90 */     return (String)getRowValue(0);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getAppId() {
/*  98 */     return (String)getRowValue(appid);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getKeyPath() {
/* 106 */     return (String)getRowValue(keyPath);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getAppId(String paramString) {
/* 115 */     return (String)getValue(appid, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getKeyPath(String paramString) {
/* 124 */     return (String)getValue(keyPath, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean addHpCache(String paramString) {
/* 134 */     if ("".equals(paramString)) {
/* 135 */       return false;
/*     */     }
/* 137 */     addCache(paramString);
/* 138 */     return true;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void reloadHpCache() {
/* 146 */     removeCache();
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/app/AppKeyPathCominfo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */