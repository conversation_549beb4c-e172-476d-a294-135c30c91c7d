/*     */ package weaver.workflow.bean;
/*     */ 
/*     */ public class Trackdetail {
/*     */   public int id;
/*     */   public int sn;
/*     */   public String optKind;
/*     */   public int optType;
/*     */   public int requestId;
/*     */   public int nodeId;
/*     */   public int isBill;
/*     */   public int fieldLableId;
/*     */   public int fieldGroupId;
/*     */   public int fieldId;
/*     */   public String fieldHtmlType;
/*     */   public String fieldType;
/*     */   public String fieldNameCn;
/*     */   public String fieldNameEn;
/*     */   public String fieldNameTw;
/*     */   public String fieldOldText;
/*     */   public String fieldNewText;
/*     */   public int modifierType;
/*     */   public int modifierId;
/*     */   public String modifierIP;
/*     */   public String modifyTime;
/*     */   public String fieldName;
/*     */   
/*     */   public String getFieldName() {
/*  28 */     return this.fieldName;
/*     */   }
/*     */   public void setFieldName(String paramString) {
/*  31 */     this.fieldName = paramString;
/*     */   }
/*     */   public int getId() {
/*  34 */     return this.id;
/*     */   }
/*     */   public void setId(int paramInt) {
/*  37 */     this.id = paramInt;
/*     */   }
/*     */   public int getOptType() {
/*  40 */     return this.optType;
/*     */   }
/*     */   public void setOptType(int paramInt) {
/*  43 */     this.optType = paramInt;
/*     */   }
/*     */   public int getRequestId() {
/*  46 */     return this.requestId;
/*     */   }
/*     */   public void setRequestId(int paramInt) {
/*  49 */     this.requestId = paramInt;
/*     */   }
/*     */   public int getNodeId() {
/*  52 */     return this.nodeId;
/*     */   }
/*     */   public void setNodeId(int paramInt) {
/*  55 */     this.nodeId = paramInt;
/*     */   }
/*     */   public int getIsBill() {
/*  58 */     return this.isBill;
/*     */   }
/*     */   public void setIsBill(int paramInt) {
/*  61 */     this.isBill = paramInt;
/*     */   }
/*     */   public int getFieldGroupId() {
/*  64 */     return this.fieldGroupId;
/*     */   }
/*     */   public void setFieldGroupId(int paramInt) {
/*  67 */     this.fieldGroupId = paramInt;
/*     */   }
/*     */   public int getFieldId() {
/*  70 */     return this.fieldId;
/*     */   }
/*     */   public void setFieldId(int paramInt) {
/*  73 */     this.fieldId = paramInt;
/*     */   }
/*     */   public String getFieldHtmlType() {
/*  76 */     return this.fieldHtmlType;
/*     */   }
/*     */   public void setFieldHtmlType(String paramString) {
/*  79 */     this.fieldHtmlType = paramString;
/*     */   }
/*     */   public String getFieldType() {
/*  82 */     return this.fieldType;
/*     */   }
/*     */   public void setFieldType(String paramString) {
/*  85 */     this.fieldType = paramString;
/*     */   }
/*     */   public String getFieldNameCn() {
/*  88 */     return this.fieldNameCn;
/*     */   }
/*     */   public void setFieldNameCn(String paramString) {
/*  91 */     this.fieldNameCn = paramString;
/*     */   }
/*     */   public String getFieldNameEn() {
/*  94 */     return this.fieldNameEn;
/*     */   }
/*     */   public void setFieldNameEn(String paramString) {
/*  97 */     this.fieldNameEn = paramString;
/*     */   }
/*     */   public String getFieldOldText() {
/* 100 */     return this.fieldOldText;
/*     */   }
/*     */   public void setFieldOldText(String paramString) {
/* 103 */     this.fieldOldText = paramString;
/*     */   }
/*     */   public String getFieldNewText() {
/* 106 */     return this.fieldNewText;
/*     */   }
/*     */   public void setFieldNewText(String paramString) {
/* 109 */     this.fieldNewText = paramString;
/*     */   }
/*     */   public int getModifierType() {
/* 112 */     return this.modifierType;
/*     */   }
/*     */   public void setModifierType(int paramInt) {
/* 115 */     this.modifierType = paramInt;
/*     */   }
/*     */   public int getModifierId() {
/* 118 */     return this.modifierId;
/*     */   }
/*     */   public void setModifierId(int paramInt) {
/* 121 */     this.modifierId = paramInt;
/*     */   }
/*     */   public String getModifierIP() {
/* 124 */     return this.modifierIP;
/*     */   }
/*     */   public void setModifierIP(String paramString) {
/* 127 */     this.modifierIP = paramString;
/*     */   }
/*     */   public String getModifyTime() {
/* 130 */     return this.modifyTime;
/*     */   }
/*     */   public void setModifyTime(String paramString) {
/* 133 */     this.modifyTime = paramString;
/*     */   }
/*     */   public String getOptKind() {
/* 136 */     return this.optKind;
/*     */   }
/*     */   public void setOptKind(String paramString) {
/* 139 */     this.optKind = paramString;
/*     */   }
/*     */   public int getFieldLableId() {
/* 142 */     return this.fieldLableId;
/*     */   }
/*     */   public void setFieldLableId(int paramInt) {
/* 145 */     this.fieldLableId = paramInt;
/*     */   }
/*     */   public int getSn() {
/* 148 */     return this.sn;
/*     */   }
/*     */   public void setSn(int paramInt) {
/* 151 */     this.sn = paramInt;
/*     */   }
/*     */   
/*     */   public String getFieldNameTw() {
/* 155 */     return this.fieldNameTw;
/*     */   }
/*     */   
/*     */   public void setFieldNameTw(String paramString) {
/* 159 */     this.fieldNameTw = paramString;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/bean/Trackdetail.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */