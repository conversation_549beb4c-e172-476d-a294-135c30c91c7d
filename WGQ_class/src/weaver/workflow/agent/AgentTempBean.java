/*    */ package weaver.workflow.agent;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class AgentTempBean
/*    */ {
/*    */   private String name;
/*    */   private int agenterid;
/*    */   private int beagenterid;
/*    */   private String userselect;
/*    */   private int rangeselect;
/* 14 */   private String workflowrange = "";
/*    */   private int iscreateagenter;
/*    */   private String isSysCreateAgenter;
/*    */   private int isproxydeal;
/*    */   private int ispendthing;
/*    */   private int creater;
/*    */   
/*    */   public String getName() {
/* 22 */     return this.name;
/*    */   }
/*    */   public void setName(String paramString) {
/* 25 */     this.name = paramString;
/*    */   }
/*    */   public int getAgenterid() {
/* 28 */     return this.agenterid;
/*    */   }
/*    */   public void setAgenterid(int paramInt) {
/* 31 */     this.agenterid = paramInt;
/*    */   }
/*    */   public int getBeagenterid() {
/* 34 */     return this.beagenterid;
/*    */   }
/*    */   public void setBeagenterid(int paramInt) {
/* 37 */     this.beagenterid = paramInt;
/*    */   }
/*    */   public String getUserselect() {
/* 40 */     return this.userselect;
/*    */   }
/*    */   public void setUserselect(String paramString) {
/* 43 */     this.userselect = paramString;
/*    */   }
/*    */   public int getRangeselect() {
/* 46 */     return this.rangeselect;
/*    */   }
/*    */   public void setRangeselect(int paramInt) {
/* 49 */     this.rangeselect = paramInt;
/*    */   }
/*    */   public String getWorkflowrange() {
/* 52 */     return this.workflowrange;
/*    */   }
/*    */   public void setWorkflowrange(String paramString) {
/* 55 */     this.workflowrange = paramString;
/*    */   }
/*    */   public int getIscreateagenter() {
/* 58 */     return this.iscreateagenter;
/*    */   }
/*    */   public void setIscreateagenter(int paramInt) {
/* 61 */     this.iscreateagenter = paramInt;
/*    */   }
/*    */   public int getIsproxydeal() {
/* 64 */     return this.isproxydeal;
/*    */   }
/*    */   public void setIsproxydeal(int paramInt) {
/* 67 */     this.isproxydeal = paramInt;
/*    */   }
/*    */   public int getIspendthing() {
/* 70 */     return this.ispendthing;
/*    */   }
/*    */   public void setIspendthing(int paramInt) {
/* 73 */     this.ispendthing = paramInt;
/*    */   }
/*    */   public int getCreater() {
/* 76 */     return this.creater;
/*    */   }
/*    */   public void setCreater(int paramInt) {
/* 79 */     this.creater = paramInt;
/*    */   }
/*    */   
/*    */   public String getIsSysCreateAgenter() {
/* 83 */     return this.isSysCreateAgenter;
/*    */   }
/*    */   
/*    */   public void setIsSysCreateAgenter(String paramString) {
/* 87 */     this.isSysCreateAgenter = paramString;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/agent/AgentTempBean.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */