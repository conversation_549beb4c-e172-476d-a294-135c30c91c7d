/*     */ package weaver.workflow.search;
/*     */ 
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ 
/*     */ 
/*     */ public class WfMultiPrintManager
/*     */ {
/*     */   public String getSqlStr(HttpServletRequest paramHttpServletRequest) {
/*  11 */     boolean bool = false;
/*  12 */     RecordSet recordSet = new RecordSet();
/*  13 */     String[] arrayOfString = paramHttpServletRequest.getParameterValues("check_con");
/*  14 */     String str = "";
/*  15 */     for (byte b = 0; b < arrayOfString.length; b++) {
/*  16 */       String str1 = "" + arrayOfString[b];
/*  17 */       String str2 = "" + Util.null2String(paramHttpServletRequest.getParameter("con" + arrayOfString[b] + "_colname"));
/*  18 */       String str3 = "" + Util.null2String(paramHttpServletRequest.getParameter("con" + arrayOfString[b] + "_htmltype"));
/*  19 */       String str4 = "" + Util.null2String(paramHttpServletRequest.getParameter("con" + arrayOfString[b] + "_type"));
/*  20 */       String str5 = "" + Util.null2String(paramHttpServletRequest.getParameter("con" + arrayOfString[b] + "_opt"));
/*  21 */       String str6 = "" + Util.null2String(paramHttpServletRequest.getParameter("con" + arrayOfString[b] + "_value"));
/*  22 */       String str7 = "" + Util.null2String(paramHttpServletRequest.getParameter("con" + arrayOfString[b] + "_name"));
/*  23 */       String str8 = "" + Util.null2String(paramHttpServletRequest.getParameter("con" + arrayOfString[b] + "_opt1"));
/*  24 */       String str9 = "" + Util.null2String(paramHttpServletRequest.getParameter("con" + arrayOfString[b] + "_value1"));
/*     */       
/*  26 */       if ((str3.equals("1") && str4.equals("1")) || str3.equals("2")) {
/*  27 */         if (!str6.equals("")) {
/*  28 */           bool = true;
/*  29 */           str = str + "and (" + str2;
/*  30 */           if (str5.equals("1")) str = str + " ='" + str6 + "' "; 
/*  31 */           if (str5.equals("2")) str = str + " <>'" + str6 + "' "; 
/*  32 */           if (str5.equals("3")) str = str + " like '%" + str6 + "%' "; 
/*  33 */           if (str5.equals("4")) str = str + " not like '%" + str6 + "%' "; 
/*     */         } 
/*  35 */       } else if (str3.equals("1") && !str4.equals("1")) {
/*  36 */         if (!str6.equals("")) {
/*  37 */           bool = true;
/*  38 */           str = str + "and (" + str2;
/*  39 */           if (str5.equals("1")) str = str + " >" + str6 + " "; 
/*  40 */           if (str5.equals("2")) str = str + " >=" + str6 + " "; 
/*  41 */           if (str5.equals("3")) str = str + " <" + str6 + " "; 
/*  42 */           if (str5.equals("4")) str = str + " <=" + str6 + " "; 
/*  43 */           if (str5.equals("5")) str = str + " =" + str6 + " "; 
/*  44 */           if (str5.equals("6")) str = str + " <>" + str6 + " "; 
/*     */         } 
/*  46 */         if (!str9.equals("")) {
/*  47 */           bool = true;
/*  48 */           str = str + " and " + str2;
/*  49 */           if (str8.equals("1")) str = str + " >" + str9 + " "; 
/*  50 */           if (str8.equals("2")) str = str + " >=" + str9 + " "; 
/*  51 */           if (str8.equals("3")) str = str + " <" + str9 + " "; 
/*  52 */           if (str8.equals("4")) str = str + " <=" + str9 + " "; 
/*  53 */           if (str8.equals("5")) str = str + " =" + str9 + " "; 
/*  54 */           if (str8.equals("6")) str = str + " <>" + str9 + " "; 
/*     */         } 
/*  56 */       } else if (str3.equals("4")) {
/*  57 */         if (str6.equals("1")) {
/*  58 */           bool = true;
/*  59 */           str = str + "and (" + str2;
/*  60 */           str = str + "='1' ";
/*     */         } 
/*  62 */       } else if (str3.equals("5")) {
/*     */         
/*  64 */         if (!str6.equals("")) {
/*  65 */           bool = true;
/*  66 */           str = str + "and (" + str2;
/*  67 */           if (str5.equals("1")) str = str + " =" + str6 + " "; 
/*  68 */           if (str5.equals("2")) str = str + " <>" + str6 + " "; 
/*     */         } 
/*  70 */       } else if (str3.equals("3") && (str4.equals("1") || str4.equals("9") || str4.equals("4") || str4.equals("7") || str4.equals("8") || str4.equals("16"))) {
/*  71 */         if (!str6.equals("")) {
/*  72 */           bool = true;
/*  73 */           str = str + "and (" + str2;
/*  74 */           if (str5.equals("1")) str = str + " in (" + str6 + ") "; 
/*  75 */           if (str5.equals("2")) str = str + " not in (" + str6 + ") "; 
/*     */         } 
/*  77 */       } else if (str3.equals("3") && str4.equals("24")) {
/*  78 */         if (!str6.equals("")) {
/*  79 */           bool = true;
/*  80 */           str = str + "and (" + str2;
/*  81 */           if (str5.equals("1")) str = str + " >" + str6 + " "; 
/*  82 */           if (str5.equals("2")) str = str + " >=" + str6 + " "; 
/*  83 */           if (str5.equals("3")) str = str + " <" + str6 + " "; 
/*  84 */           if (str5.equals("4")) str = str + " <=" + str6 + " "; 
/*  85 */           if (str5.equals("5")) str = str + " =" + str6 + " "; 
/*  86 */           if (str5.equals("6")) str = str + " <>" + str6 + " "; 
/*     */         } 
/*  88 */         if (!str9.equals("")) {
/*  89 */           bool = true;
/*  90 */           str = str + " and " + str2;
/*  91 */           if (str8.equals("1")) str = str + " >" + str9 + " "; 
/*  92 */           if (str8.equals("2")) str = str + " >=" + str9 + " "; 
/*  93 */           if (str8.equals("3")) str = str + " <" + str9 + " "; 
/*  94 */           if (str8.equals("4")) str = str + " <=" + str9 + " "; 
/*  95 */           if (str8.equals("5")) str = str + " =" + str9 + " "; 
/*  96 */           if (str8.equals("6")) str = str + " <>" + str9 + " "; 
/*     */         } 
/*  98 */       } else if (str3.equals("3") && (str4.equals("2") || str4.equals("19"))) {
/*  99 */         if (!str6.equals("")) {
/* 100 */           bool = true;
/* 101 */           str = str + "and (" + str2;
/* 102 */           if (str5.equals("1")) str = str + " >'" + str6 + "' "; 
/* 103 */           if (str5.equals("2")) str = str + " >='" + str6 + "' "; 
/* 104 */           if (str5.equals("3")) str = str + " <'" + str6 + "' "; 
/* 105 */           if (str5.equals("4")) str = str + " <='" + str6 + "' "; 
/* 106 */           if (str5.equals("5")) str = str + " ='" + str6 + "' "; 
/* 107 */           if (str5.equals("6")) str = str + " <>'" + str6 + "' "; 
/*     */         } 
/* 109 */         if (!str9.equals("")) {
/* 110 */           bool = true;
/* 111 */           str = str + " and " + str2;
/* 112 */           if (str8.equals("1")) str = str + " >'" + str9 + "' "; 
/* 113 */           if (str8.equals("2")) str = str + " >='" + str9 + "' "; 
/* 114 */           if (str8.equals("3")) str = str + " <'" + str9 + "' "; 
/* 115 */           if (str8.equals("4")) str = str + " <='" + str9 + "' "; 
/* 116 */           if (str8.equals("5")) str = str + " ='" + str9 + "' "; 
/* 117 */           if (str8.equals("6")) str = str + " <>'" + str9 + "' "; 
/*     */         } 
/* 119 */       } else if (str3.equals("3") && (str4.equals("17") || str4.equals("57") || str4.equals("135") || str4.equals("152") || str4.equals("18") || str4.equals("160"))) {
/* 120 */         if (!str6.equals("")) {
/* 121 */           bool = true;
/* 122 */           if (recordSet.getDBType().equalsIgnoreCase("oracle")) {
/* 123 */             str = str + "and (','||" + str2 + "||','";
/* 124 */           } else if (recordSet.getDBType().equals("mysql")) {
/* 125 */             str = str + "and concat((','," + str2 + ",',')";
/*     */           }
/* 127 */           else if (recordSet.getDBType().equalsIgnoreCase("postgresql")) {
/* 128 */             str = str + "and (','||" + str2 + "||','";
/*     */           } else {
/*     */             
/* 131 */             str = str + "and (','+CONVERT(varchar," + str2 + ")+',' ";
/*     */           } 
/* 133 */           if (str5.equals("1")) str = str + " like '%," + str6 + ",%' "; 
/* 134 */           if (str5.equals("2")) str = str + " not like '%," + str6 + ",%' "; 
/*     */         } 
/* 136 */       } else if (str3.equals("3") && (str4.equals("141") || str4.equals("56") || str4.equals("27") || str4.equals("118") || str4.equals("65") || str4.equals("64") || str4.equals("137") || str4.equals("142"))) {
/*     */         
/* 138 */         if (!str6.equals("")) {
/* 139 */           bool = true;
/* 140 */           if (recordSet.getDBType().equalsIgnoreCase("oracle")) {
/* 141 */             str = str + "and (','||" + str2 + "||','";
/* 142 */           } else if (recordSet.getDBType().equals("mysql")) {
/* 143 */             str = str + "and concat((','," + str2 + ",',')";
/*     */           }
/* 145 */           else if (recordSet.getDBType().equalsIgnoreCase("postgresql")) {
/* 146 */             str = str + "and (','||" + str2 + "||','";
/*     */           } else {
/*     */             
/* 149 */             str = str + "and (','+CONVERT(varchar," + str2 + ")+',' ";
/*     */           } 
/* 151 */           if (str5.equals("1")) str = str + " like '%," + str6 + ",%' "; 
/* 152 */           if (str5.equals("2")) str = str + " not like '%," + str6 + ",%' "; 
/*     */         } 
/* 154 */       } else if (str3.equals("3")) {
/* 155 */         if (!str6.equals("")) {
/* 156 */           bool = true;
/* 157 */           if (recordSet.getDBType().equalsIgnoreCase("oracle")) {
/* 158 */             str = str + "and (','||" + str2 + "||','";
/* 159 */           } else if (recordSet.getDBType().equals("mysql")) {
/* 160 */             str = str + "and concat((','," + str2 + ",',')";
/*     */           }
/* 162 */           else if (recordSet.getDBType().equalsIgnoreCase("postgresql")) {
/* 163 */             str = str + "and (','||" + str2 + "||','";
/*     */           } else {
/*     */             
/* 166 */             str = str + "and (','+CONVERT(varchar," + str2 + ")+',' ";
/*     */           } 
/* 168 */           if (str5.equals("1")) str = str + " like '%," + str6 + ",%' "; 
/* 169 */           if (str5.equals("2")) str = str + " not like '%," + str6 + ",%' "; 
/*     */         } 
/* 171 */       } else if (str3.equals("6") && 
/* 172 */         !str6.equals("")) {
/* 173 */         bool = true;
/* 174 */         if (recordSet.getDBType().equalsIgnoreCase("oracle")) {
/* 175 */           str = str + "and (','||" + str2 + "||','";
/* 176 */         } else if (recordSet.getDBType().equals("mysql")) {
/* 177 */           str = str + "and concat((','," + str2 + ",',')";
/*     */         }
/* 179 */         else if (recordSet.getDBType().equalsIgnoreCase("postgresql")) {
/* 180 */           str = str + "and (','||" + str2 + "||','";
/*     */         } else {
/*     */           
/* 183 */           str = str + "and (','+CONVERT(varchar," + str2 + ")+',' ";
/*     */         } 
/* 185 */         if (str5.equals("1")) str = str + " like '%," + str6 + ",%' "; 
/* 186 */         if (str5.equals("2")) str = str + " not like '%," + str6 + ",%' ";
/*     */       
/*     */       } 
/* 189 */       if (!str6.equals("")) {
/* 190 */         bool = true;
/* 191 */         str = str + ") ";
/*     */       } 
/*     */     } 
/* 194 */     return str;
/*     */   }
/*     */   
/*     */   public static void main(String[] paramArrayOfString) {}
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/search/WfMultiPrintManager.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */