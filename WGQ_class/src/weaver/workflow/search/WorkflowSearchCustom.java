/*     */ package weaver.workflow.search;
/*     */ 
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class WorkflowSearchCustom
/*     */ {
/*     */   public String getSearchCustomStr(RecordSet paramRecordSet, String paramString, HttpServletRequest paramHttpServletRequest) {
/*  21 */     paramRecordSet.executeSql("select workflow_formfield.fieldid,workflow_formdict.fieldname,WorkFlow_FieldLable.fieldlable,workflow_formdict.fielddbtype,workflow_formdict.fieldhtmltype,workflow_formdict.type from workflow_formdict,workflow_formfield,WorkFlow_FieldLable where workflow_fieldlable.formid = workflow_formfield.formid and workflow_fieldlable.fieldid =workflow_formfield.fieldid and  workflow_formdict.id = workflow_formfield.fieldid and workflow_formfield.formid in ( select formID from Workflow_Custom where id=" + paramString + ")");
/*     */ 
/*     */ 
/*     */     
/*  25 */     String str = "";
/*  26 */     while (paramRecordSet.next()) {
/*  27 */       String str1 = paramRecordSet.getString("fieldname");
/*  28 */       String str2 = paramRecordSet.getString("fieldhtmltype");
/*  29 */       String str3 = paramRecordSet.getString("type");
/*  30 */       if ((str2.equals("1") && str3.equals("1")) || str2.equals("2")) {
/*  31 */         str = str + str1 + "_opt=" + Util.null2String(paramHttpServletRequest.getParameter(str1 + "_opt")) + "&";
/*  32 */         str = str + str1 + "_value=" + Util.null2String(paramHttpServletRequest.getParameter(str1 + "_value")) + "&"; continue;
/*  33 */       }  if (str2.equals("1")) {
/*  34 */         str = str + str1 + "_01opt=" + Util.null2String(paramHttpServletRequest.getParameter(str1 + "_01opt")) + "&";
/*  35 */         str = str + str1 + "_01value=" + Util.null2String(paramHttpServletRequest.getParameter(str1 + "_01value")) + "&";
/*  36 */         str = str + str1 + "_02opt=" + Util.null2String(paramHttpServletRequest.getParameter(str1 + "_02opt")) + "&";
/*  37 */         str = str + str1 + "_02value=" + Util.null2String(paramHttpServletRequest.getParameter(str1 + "_02value")) + "&"; continue;
/*  38 */       }  if (str2.equals("3")) {
/*  39 */         if (str3.equals("24") || str3.equals("2") || str3.equals("19")) {
/*  40 */           str = str + str1 + "_01opt=" + Util.null2String(paramHttpServletRequest.getParameter(str1 + "_01opt")) + "&";
/*  41 */           str = str + str1 + "_01value=" + Util.null2String(paramHttpServletRequest.getParameter(str1 + "_01value")) + "&";
/*  42 */           str = str + str1 + "_02opt=" + Util.null2String(paramHttpServletRequest.getParameter(str1 + "_02opt")) + "&";
/*  43 */           str = str + str1 + "_02value=" + Util.null2String(paramHttpServletRequest.getParameter(str1 + "_02value")) + "&"; continue;
/*     */         } 
/*  45 */         str = str + str1 + "_opt=" + Util.null2String(paramHttpServletRequest.getParameter(str1 + "_opt")) + "&";
/*  46 */         str = str + str1 + "_value=" + Util.null2String(paramHttpServletRequest.getParameter(str1 + "_value")) + "&";
/*  47 */         str = str + str1 + "_name=" + Util.null2String(paramHttpServletRequest.getParameter(str1 + "_name")) + "&"; continue;
/*     */       } 
/*  49 */       if (str2.equals("4")) {
/*  50 */         str = str + str1 + "_value=" + Util.null2String(paramHttpServletRequest.getParameter(str1 + "_value")) + "&"; continue;
/*  51 */       }  if (str2.equals("5")) {
/*  52 */         str = str + str1 + "_opt=" + Util.null2String(paramHttpServletRequest.getParameter(str1 + "_opt")) + "&"; continue;
/*  53 */       }  if (str2.equals("6")) {
/*  54 */         str = str + str1 + "_opt=" + Util.null2String(paramHttpServletRequest.getParameter(str1 + "_opt")) + "&";
/*  55 */         str = str + str1 + "_value=" + Util.null2String(paramHttpServletRequest.getParameter(str1 + "_value")) + "&";
/*  56 */         str = str + str1 + "_name=" + Util.null2String(paramHttpServletRequest.getParameter(str1 + "_name")) + "&";
/*     */       } 
/*     */     } 
/*  59 */     if (str.length() > 0) {
/*  60 */       str = str.substring(0, str.length() - 1);
/*     */     }
/*  62 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getResultCustomStr(RecordSet paramRecordSet, String paramString, HttpServletRequest paramHttpServletRequest) {
/*  73 */     paramRecordSet.executeSql("select formID from Workflow_Custom where id=" + paramString);
/*  74 */     int i = 0;
/*  75 */     while (paramRecordSet.next()) {
/*  76 */       i = paramRecordSet.getInt("formID");
/*     */     }
/*  78 */     String str1 = "";
/*  79 */     paramRecordSet.executeSql("select workflow_formfield.fieldid,workflow_formdict.fieldname,WorkFlow_FieldLable.fieldlable,workflow_formdict.fielddbtype,workflow_formdict.fieldhtmltype,workflow_formdict.type from workflow_formdict,workflow_formfield,WorkFlow_FieldLable where workflow_fieldlable.formid = workflow_formfield.formid and workflow_fieldlable.fieldid =workflow_formfield.fieldid and  workflow_formdict.id = workflow_formfield.fieldid and workflow_formfield.formid=" + i);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  85 */     while (paramRecordSet.next()) {
/*  86 */       String str3 = paramRecordSet.getString("fieldname");
/*  87 */       String str4 = paramRecordSet.getString("fieldhtmltype");
/*  88 */       String str5 = paramRecordSet.getString("type");
/*  89 */       if ((str4.equals("1") && str5.equals("1")) || str4.equals("2")) {
/*     */         
/*  91 */         String str6 = Util.null2String(paramHttpServletRequest.getParameter(str3 + "_opt"));
/*  92 */         String str7 = Util.null2String(paramHttpServletRequest.getParameter(str3 + "_value"));
/*  93 */         if (!str7.equals("")) {
/*  94 */           if (str6.equals("1")) {
/*  95 */             str1 = str1 + str3 + "='" + str7 + "' and "; continue;
/*  96 */           }  if (str6.equals("2")) {
/*  97 */             str1 = str1 + str3 + "!='" + str7 + "' and "; continue;
/*  98 */           }  if (str6.equals("3")) {
/*  99 */             str1 = str1 + str3 + " like '%" + str7 + "%' and "; continue;
/* 100 */           }  if (str6.equals("4"))
/* 101 */             str1 = str1 + str3 + " not like '%" + str7 + "%' and "; 
/*     */         }  continue;
/*     */       } 
/* 104 */       if (str4.equals("1")) {
/* 105 */         String str6 = Util.null2String(paramHttpServletRequest.getParameter(str3 + "_01opt"));
/* 106 */         String str7 = Util.null2String(paramHttpServletRequest.getParameter(str3 + "_01value"));
/* 107 */         if (!str7.equals("")) {
/* 108 */           if (str6.equals("1")) {
/* 109 */             str1 = str1 + str3 + ">" + str7 + " and ";
/* 110 */           } else if (str6.equals("2")) {
/* 111 */             str1 = str1 + str3 + ">=" + str7 + " and ";
/* 112 */           } else if (str6.equals("3")) {
/* 113 */             str1 = str1 + str3 + "<" + str7 + " and ";
/* 114 */           } else if (str6.equals("4")) {
/* 115 */             str1 = str1 + str3 + "<=" + str7 + " and ";
/* 116 */           } else if (str6.equals("5")) {
/* 117 */             str1 = str1 + str3 + "=" + str7 + " and ";
/* 118 */           } else if (str6.equals("6")) {
/* 119 */             str1 = str1 + str3 + "!=" + str7 + " and ";
/*     */           } 
/*     */         }
/* 122 */         str6 = Util.null2String(paramHttpServletRequest.getParameter(str3 + "_02opt"));
/* 123 */         str7 = Util.null2String(paramHttpServletRequest.getParameter(str3 + "_02value"));
/* 124 */         if (!str7.equals("")) {
/* 125 */           if (str6.equals("1")) {
/* 126 */             str1 = str1 + str3 + ">" + str7 + " and "; continue;
/* 127 */           }  if (str6.equals("2")) {
/* 128 */             str1 = str1 + str3 + ">=" + str7 + " and "; continue;
/* 129 */           }  if (str6.equals("3")) {
/* 130 */             str1 = str1 + str3 + "<" + str7 + " and "; continue;
/* 131 */           }  if (str6.equals("4")) {
/* 132 */             str1 = str1 + str3 + "<=" + str7 + " and "; continue;
/* 133 */           }  if (str6.equals("5")) {
/* 134 */             str1 = str1 + str3 + "=" + str7 + " and "; continue;
/* 135 */           }  if (str6.equals("6"))
/* 136 */             str1 = str1 + str3 + "!=" + str7 + " and "; 
/*     */         }  continue;
/*     */       } 
/* 139 */       if (str4.equals("3")) {
/* 140 */         if (str5.equals("24") || str5.equals("2") || str5.equals("19")) {
/* 141 */           String str8 = Util.null2String(paramHttpServletRequest.getParameter(str3 + "_01opt"));
/* 142 */           String str9 = Util.null2String(paramHttpServletRequest.getParameter(str3 + "_01value"));
/* 143 */           if (!str9.equals("")) {
/* 144 */             if (str8.equals("1")) {
/* 145 */               str1 = str1 + str3 + ">" + str9 + " and ";
/* 146 */             } else if (str8.equals("2")) {
/* 147 */               str1 = str1 + str3 + ">=" + str9 + " and ";
/* 148 */             } else if (str8.equals("3")) {
/* 149 */               str1 = str1 + str3 + "<" + str9 + " and ";
/* 150 */             } else if (str8.equals("4")) {
/* 151 */               str1 = str1 + str3 + "<=" + str9 + " and ";
/* 152 */             } else if (str8.equals("5")) {
/* 153 */               str1 = str1 + str3 + "=" + str9 + " and ";
/* 154 */             } else if (str8.equals("6")) {
/* 155 */               str1 = str1 + str3 + "!=" + str9 + " and ";
/*     */             } 
/*     */           }
/* 158 */           str8 = Util.null2String(paramHttpServletRequest.getParameter(str3 + "_02opt"));
/* 159 */           str9 = Util.null2String(paramHttpServletRequest.getParameter(str3 + "_02value"));
/* 160 */           if (!str9.equals("")) {
/* 161 */             if (str8.equals("1")) {
/* 162 */               str1 = str1 + str3 + ">" + str9 + " and "; continue;
/* 163 */             }  if (str8.equals("2")) {
/* 164 */               str1 = str1 + str3 + ">=" + str9 + " and "; continue;
/* 165 */             }  if (str8.equals("3")) {
/* 166 */               str1 = str1 + str3 + "<" + str9 + " and "; continue;
/* 167 */             }  if (str8.equals("4")) {
/* 168 */               str1 = str1 + str3 + "<=" + str9 + " and "; continue;
/* 169 */             }  if (str8.equals("5")) {
/* 170 */               str1 = str1 + str3 + "=" + str9 + " and "; continue;
/* 171 */             }  if (str8.equals("6"))
/* 172 */               str1 = str1 + str3 + "!=" + str9 + " and "; 
/*     */           }  continue;
/*     */         } 
/* 175 */         if (str5.equals("17") || str5.equals("37") || str5.equals("57") || str5.equals("135") || str5.equals("152") || str5.equals("18") || str5.equals("160") || str5.equals("142") || str5.equals("141") || str5.equals("56") || str5.equals("27") || str5.equals("118") || str5.equals("65") || str5.equals("64") || str5.equals("137")) {
/* 176 */           String str8 = Util.null2String(paramHttpServletRequest.getParameter(str3 + "_opt"));
/* 177 */           String str9 = Util.null2String(paramHttpServletRequest.getParameter(str3 + "_value"));
/* 178 */           if (!str9.equals("")) {
/* 179 */             if (str8.equals("1")) {
/* 180 */               str1 = str1 + "(" + str3 + " like '%," + str9 + "%' or like '%" + str9 + ",%' or like '" + str9 + "') and "; continue;
/* 181 */             }  if (str8.equals("2"))
/* 182 */               str1 = str1 + "(" + str3 + " not like '%," + str9 + "%' and not like '%" + str9 + ",%' and not like '" + str9 + "') and "; 
/*     */           } 
/*     */           continue;
/*     */         } 
/* 186 */         String str6 = Util.null2String(paramHttpServletRequest.getParameter(str3 + "_opt"));
/* 187 */         String str7 = Util.null2String(paramHttpServletRequest.getParameter(str3 + "_value"));
/* 188 */         if (!str7.equals("")) {
/* 189 */           if (str6.equals("1")) {
/* 190 */             str1 = str1 + str3 + " in (" + str7 + ") and "; continue;
/* 191 */           }  if (str6.equals("2"))
/* 192 */             str1 = str1 + str3 + " not in (" + str7 + ") and "; 
/*     */         } 
/*     */         continue;
/*     */       } 
/* 196 */       if (str4.equals("4")) {
/* 197 */         String str = Util.null2String(paramHttpServletRequest.getParameter(str3 + "_value"));
/* 198 */         if (!str.equals(""))
/* 199 */           str1 = str1 + str3 + "=" + str + " and ";  continue;
/*     */       } 
/* 201 */       if (str4.equals("5")) {
/* 202 */         String str = Util.null2String(paramHttpServletRequest.getParameter(str3 + "_opt"));
/* 203 */         if (!str.equals(""))
/* 204 */           str1 = str1 + str3 + "=" + str + " and ";  continue;
/*     */       } 
/* 206 */       if (str4.equals("6")) {
/* 207 */         String str6 = Util.null2String(paramHttpServletRequest.getParameter(str3 + "_opt"));
/* 208 */         String str7 = Util.null2String(paramHttpServletRequest.getParameter(str3 + "_value"));
/* 209 */         if (!str7.equals("")) {
/* 210 */           if (str6.equals("1")) {
/* 211 */             str1 = str1 + "(" + str3 + " like '%," + str7 + "%' or like '%" + str7 + ",%' or like '" + str7 + "') and "; continue;
/* 212 */           }  if (str6.equals("2")) {
/* 213 */             str1 = str1 + "(" + str3 + " not like '%," + str7 + "%' and not like '%" + str7 + ",%' and not like '" + str7 + "') and ";
/*     */           }
/*     */         } 
/*     */       } 
/*     */     } 
/* 218 */     String str2 = "";
/* 219 */     if (i > 0) {
/*     */       
/* 221 */       str2 = "";
/* 222 */     } else if (i < 0) {
/*     */       
/* 224 */       str2 = "formtable_main_" + (0 - i);
/*     */     } 
/* 226 */     if (str1.length() > 0) {
/* 227 */       str1 = "select requestid from " + str2 + " where " + str1 + " 1=1";
/*     */     }
/* 229 */     return str1;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/search/WorkflowSearchCustom.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */