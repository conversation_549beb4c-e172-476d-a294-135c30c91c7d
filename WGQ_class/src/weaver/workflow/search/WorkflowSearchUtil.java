/*     */ package weaver.workflow.search;
/*     */ 
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.FormFieldTransMethod;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.workflow.field.BrowserComInfo;
/*     */ import weaver.workflow.mode.FieldInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class WorkflowSearchUtil
/*     */ {
/*  17 */   private static FieldInfo fi = new FieldInfo();
/*  18 */   private static BrowserComInfo bci = new BrowserComInfo();
/*     */   
/*  20 */   private static FormFieldTransMethod fftm = new FormFieldTransMethod();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getFieldValueShowString(String paramString1, String paramString2, int paramInt1, int paramInt2, String paramString3, int paramInt3) {
/*  33 */     RecordSet recordSet = new RecordSet();
/*  34 */     if (!"".equals(paramString1)) {
/*  35 */       switch (paramInt1) {
/*     */         case 1:
/*     */         case 2:
/*  38 */           paramString1 = Util.toScreenToEdit(paramString1, paramInt3);
/*     */           break;
/*     */         case 3:
/*  41 */           paramString1 = fi.getFieldName(paramString1, paramInt2, paramString3);
/*     */           break;
/*     */         case 4:
/*  44 */           if ("1".equals(paramString1)) {
/*  45 */             paramString1 = SystemEnv.getHtmlLabelName(25104, paramInt3); break;
/*     */           } 
/*  47 */           paramString1 = "";
/*     */           break;
/*     */         
/*     */         case 5:
/*  51 */           if (paramInt2 == 2) {
/*  52 */             String[] arrayOfString = Util.TokenizerString2(paramString1, ",");
/*  53 */             String str1 = "";
/*  54 */             for (byte b = 0; b < arrayOfString.length; b++) {
/*  55 */               if (b > 0) {
/*  56 */                 str1 = str1 + ",";
/*     */               }
/*  58 */               str1 = str1 + arrayOfString[b];
/*     */             } 
/*  60 */             String str2 = "";
/*  61 */             if (str1.length() > 0) {
/*  62 */               recordSet = new RecordSet();
/*  63 */               recordSet.executeSql("select listorder,selectvalue, selectname from workflow_SelectItem where fieldid=" + paramString2 + " and selectvalue in (" + str1 + ") order by listorder,selectvalue ");
/*  64 */               while (recordSet.next())
/*     */               {
/*  66 */                 str2 = str2 + "," + recordSet.getString("selectname");
/*     */               }
/*  68 */               if (str2.startsWith(",")) {
/*  69 */                 str2 = str2.substring(1);
/*     */               }
/*     */             } 
/*  72 */             paramString1 = str2;
/*     */             break;
/*     */           } 
/*  75 */           recordSet.executeSql("SELECT selectname FROM workflow_selectitem WHERE selectvalue='" + paramString1 + "' AND fieldid='" + paramString2 + "'");
/*  76 */           if (recordSet.next()) {
/*  77 */             paramString1 = Util.null2String(recordSet.getString("selectname")); break;
/*     */           } 
/*  79 */           paramString1 = "";
/*     */           break;
/*     */       } 
/*     */ 
/*     */     
/*     */     }
/*  85 */     return paramString1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getFieldName(String paramString1, String paramString2, String paramString3) {
/*  96 */     if (!"0".equals(paramString2))
/*     */     {
/*     */       
/*  99 */       paramString1 = fftm.getFieldname(paramString1, paramString3);
/*     */     }
/* 101 */     return paramString1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getFieldName(String paramString1, String paramString2, String paramString3, String paramString4) {
/* 113 */     String str = getFieldLabel(paramString1, paramString2, paramString3, paramString4);
/* 114 */     return getFieldName(str, paramString3, paramString4);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getBrowserName(String paramString1, String paramString2) {
/* 124 */     return SystemEnv.getHtmlLabelName(Util.getIntValue(bci.getBrowserlabelid(paramString1), 0), Util.getIntValue(paramString2, 7));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getFieldValueShowStringFromTable(String paramString1, String paramString2) {
/* 134 */     if (paramString2 != null) {
/* 135 */       String[] arrayOfString = paramString2.split("\\+");
/* 136 */       String str = "";
/* 137 */       if (arrayOfString.length == 5) {
/* 138 */         String str1 = arrayOfString[0];
/* 139 */         int i = Util.getIntValue(arrayOfString[1]);
/* 140 */         int j = Util.getIntValue(arrayOfString[2]);
/* 141 */         String str2 = arrayOfString[3];
/* 142 */         int k = Util.getIntValue(arrayOfString[4]);
/* 143 */         paramString1 = getFieldValueShowString(paramString1, str1, i, j, str2, k);
/* 144 */         RecordSet recordSet = new RecordSet();
/* 145 */         if (i == 6) {
/* 146 */           if (paramString1.indexOf(",") != -1) {
/* 147 */             String[] arrayOfString1 = paramString1.split(",");
/* 148 */             for (byte b = 0; b < arrayOfString1.length; b++) {
/* 149 */               String str3 = "";
/* 150 */               recordSet.executeSql("select docsubject from docdetail where id=" + arrayOfString1[b]);
/* 151 */               if (recordSet.next()) {
/* 152 */                 str3 = recordSet.getString("docsubject");
/*     */               }
/* 154 */               str = str + "<a href=\"javascript:openFullWindowHaveBar('/docs/docs/DocDsp.jsp?id=" + arrayOfString1[b] + "')\">" + str3 + "</a></br>";
/*     */             } 
/*     */           } else {
/* 157 */             RecordSet recordSet1 = new RecordSet();
/* 158 */             if (paramString1 != null && paramString1.length() != 0) {
/* 159 */               recordSet1.executeSql("select docsubject from docdetail where id=" + paramString1);
/*     */             }
/* 161 */             String str3 = "";
/* 162 */             if (recordSet1.next()) {
/* 163 */               str3 = recordSet1.getString("docsubject");
/*     */             }
/* 165 */             str = "<a href=\"javascript:openFullWindowHaveBar('/docs/docs/DocDsp.jsp?id=" + paramString1 + "')\">" + str3 + "</a>";
/*     */           } 
/*     */           
/* 168 */           paramString1 = str;
/*     */         } 
/* 170 */         if (i == 7) {
/* 171 */           recordSet.executeQuery("select * from workflow_specialfield where fieldid=?", new Object[] { str1 });
/* 172 */           if (recordSet.next()) {
/* 173 */             if (j == 1) {
/* 174 */               paramString1 = "<a href=\"" + recordSet.getString("linkaddress") + "\" target=\"_blank\">" + recordSet.getString("displayname") + "</a>";
/*     */             } else {
/* 176 */               paramString1 = Util.null2String(recordSet.getString("descriptivetext"));
/*     */             } 
/*     */           }
/*     */         } 
/* 180 */         if (i == 3 && j == 9) {
/*     */           
/* 182 */           if (paramString1.indexOf(",") != -1) {
/* 183 */             String[] arrayOfString1 = paramString1.split(",");
/* 184 */             for (byte b = 0; b < arrayOfString1.length; b++) {
/* 185 */               str = str + "<a href=\"/docs/docs/DocDsp.jsp?isrequest=1&id=" + arrayOfString1[b] + "&requestid=2068700\" target=\"_new\">" + arrayOfString1[b] + "</a>&nbsp";
/*     */             }
/*     */           } else {
/* 188 */             str = "<a href=\"/docs/docs/DocDsp.jsp?isrequest=1&id=" + str1 + "&requestid=2068700\" target=\"_new\">" + paramString1 + "</a>";
/*     */           } 
/* 190 */           paramString1 = str;
/*     */         } 
/* 192 */         if (i == 9) {
/* 193 */           String[] arrayOfString1 = paramString1.split("/////~~weaversplit~~/////");
/* 194 */           paramString1 = "";
/* 195 */           for (byte b = 0; b < arrayOfString1.length; b++) {
/* 196 */             String[] arrayOfString2 = arrayOfString1[b].split("////~~weaversplit~~////");
/* 197 */             if (arrayOfString2 != null && arrayOfString2.length >= 4 && !arrayOfString2[3].equals("")) {
/* 198 */               if (b == arrayOfString1.length - 1) {
/* 199 */                 paramString1 = paramString1 + arrayOfString2[3];
/*     */               } else {
/* 201 */                 paramString1 = paramString1 + arrayOfString2[3] + ",";
/*     */               } 
/*     */             }
/*     */           } 
/* 205 */           if (!paramString1.equals("") && paramString1.length() > 1 && 
/* 206 */             paramString1.substring(paramString1.length() - 1).equals(",")) {
/* 207 */             paramString1 = paramString1.substring(0, paramString1.length() - 1);
/*     */           }
/*     */         } 
/*     */       } 
/*     */     } 
/*     */     
/* 213 */     return paramString1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getFieldNameFromTable(String paramString1, String paramString2) {
/* 223 */     if (paramString2 != null) {
/* 224 */       String[] arrayOfString = paramString2.split("\\+");
/* 225 */       if (arrayOfString.length == 2) {
/* 226 */         String str1 = arrayOfString[0];
/* 227 */         String str2 = arrayOfString[1];
/* 228 */         paramString1 = getFieldName(paramString1, str1, str2);
/*     */       } 
/*     */     } 
/* 231 */     return paramString1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getBrowserCompleteUrl(String paramString) {
/* 241 */     if ("267".equals(paramString)) {
/* 242 */       return "/data.jsp?type=65";
/*     */     }
/* 244 */     return "/data.jsp?type=" + paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getFieldSource(String paramString1, String paramString2) {
/* 254 */     if (paramString1 == null || paramString1.isEmpty()) {
/* 255 */       return SystemEnv.getHtmlLabelName(18020, Util.getIntValue(paramString2, 7));
/*     */     }
/* 257 */     return SystemEnv.getHtmlLabelName(18021, Util.getIntValue(paramString2, 7));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static String getFieldLabel(String paramString1, String paramString2, String paramString3, String paramString4) {
/* 269 */     String str = "";
/* 270 */     if ("0".equals(paramString3)) {
/* 271 */       str = "SELECT fieldlable as label FROM workflow_fieldlable WHERE formid='" + paramString1 + "' AND fieldid='" + paramString2 + "' AND langurageid='" + paramString4 + "'";
/*     */     } else {
/* 273 */       str = "SELECT fieldlabel as label FROM workflow_billfield WHERE billid='" + paramString1 + "' AND id='" + paramString2 + "'";
/*     */     } 
/* 275 */     RecordSet recordSet = new RecordSet();
/* 276 */     recordSet.executeSql(str);
/* 277 */     if (recordSet.next()) {
/* 278 */       return Util.null2String(recordSet.getString("label"));
/*     */     }
/* 280 */     return "";
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/search/WorkflowSearchUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */