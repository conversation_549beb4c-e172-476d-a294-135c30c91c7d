/*     */ package weaver.workflow.search;
/*     */ 
/*     */ import com.engine.hrm.biz.HrmClassifiedProtectionBiz;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Hashtable;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.crm.Maint.CustomerInfoComInfo;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.HrmUserVarify;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.resource.AllManagers;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ import weaver.system.SystemComInfo;
/*     */ import weaver.systeminfo.menuconfig.LeftMenuInfo;
/*     */ import weaver.systeminfo.menuconfig.LeftMenuInfoHandler;
/*     */ import weaver.workflow.request.todo.RequestUtil;
/*     */ 
/*     */ 
/*     */ public class WorkflowRequestUtil
/*     */ {
/*     */   public int getRequestCount(User paramUser, String paramString) {
/*  22 */     return getRequestCount(paramUser, false, 0, paramString, 0, null, null, 0);
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public int getRequestCount(User paramUser, boolean paramBoolean, int paramInt1, String paramString1, int paramInt2, String paramString2, String paramString3, int paramInt3) {
/*  28 */     RecordSet recordSet = new RecordSet();
/*  29 */     String str1 = String.valueOf(paramUser.getUID());
/*  30 */     int i = paramUser.getUID();
/*  31 */     String str2 = "";
/*  32 */     recordSet.executeSql("select * from HrmUserSetting where resourceId = " + str1);
/*     */     
/*  34 */     if (recordSet.next()) {
/*  35 */       str2 = recordSet.getString("belongtoshow");
/*     */     }
/*     */     
/*  38 */     if (!"".equals(paramString1) && !("" + str1).equals(paramString1)) str2 = "";
/*     */     
/*  40 */     String str3 = String.valueOf(paramUser.getUID());
/*  41 */     String str4 = paramUser.getBelongtoids();
/*  42 */     int j = 0;
/*  43 */     String[] arrayOfString = null;
/*  44 */     ArrayList<String> arrayList = new ArrayList();
/*  45 */     arrayList.add(i + "");
/*  46 */     if (!"".equals(str4)) {
/*  47 */       str3 = str1 + "," + str4;
/*  48 */       arrayOfString = str4.split(",");
/*  49 */       for (byte b = 0; b < arrayOfString.length; b++) {
/*  50 */         j = Util.getIntValue(arrayOfString[b]);
/*  51 */         arrayList.add(j + "");
/*     */       } 
/*     */     } 
/*  54 */     SystemComInfo systemComInfo = new SystemComInfo();
/*  55 */     boolean bool = systemComInfo.isUseOldWfMode();
/*     */     
/*  57 */     AllManagers allManagers = new AllManagers();
/*     */     
/*     */     try {
/*  60 */       allManagers.getAll(paramString1);
/*  61 */     } catch (Exception exception) {
/*     */       
/*  63 */       exception.printStackTrace();
/*     */     } 
/*  65 */     if ("".equals(paramString1)) {
/*  66 */       paramString1 = "" + paramUser.getUID();
/*     */     }
/*  68 */     boolean bool1 = false;
/*  69 */     boolean bool2 = false;
/*  70 */     recordSet.executeProc("HrmResource_SelectByID", paramString1);
/*  71 */     recordSet.next();
/*  72 */     String str5 = Util.toScreen(recordSet.getString("departmentid"), paramUser
/*  73 */         .getLanguage());
/*  74 */     if (paramString1.equals("" + paramUser.getUID())) {
/*  75 */       bool1 = true;
/*     */     }
/*  77 */     while (allManagers.next()) {
/*  78 */       String str = allManagers.getManagerID();
/*  79 */       if (str.equals("" + paramUser.getUID())) {
/*  80 */         bool2 = true;
/*     */       }
/*     */     } 
/*  83 */     if (bool1 || bool2 || !HrmUserVarify.checkUserRight("HrmResource:Workflow", paramUser, str5));
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  88 */     String str6 = "" + paramUser.getLogintype();
/*  89 */     boolean bool3 = false;
/*  90 */     if (str6.equals("2")) {
/*  91 */       bool3 = true;
/*     */     }
/*     */ 
/*     */     
/*  95 */     if (paramString2 != null && paramString2.startsWith("key_")) {
/*  96 */       String str = paramString2.substring(4);
/*  97 */       recordSet.executeSql("select * from menuResourceNode where contentindex = '" + str + "'");
/*     */       
/*  99 */       paramString2 = "";
/* 100 */       while (recordSet.next()) {
/* 101 */         String str18 = recordSet.getString(2);
/* 102 */         paramString2 = paramString2 + str18 + "|";
/*     */       } 
/* 104 */       if (paramString2.indexOf("|") != -1)
/* 105 */         paramString2 = paramString2.substring(0, paramString2
/* 106 */             .length() - 1); 
/*     */     } 
/* 108 */     if (paramInt2 == 1)
/*     */     {
/* 110 */       return 0;
/*     */     }
/* 112 */     String str7 = "";
/* 113 */     LeftMenuInfoHandler leftMenuInfoHandler = new LeftMenuInfoHandler();
/* 114 */     LeftMenuInfo leftMenuInfo = leftMenuInfoHandler.getLeftMenuInfo(paramInt3);
/* 115 */     if (leftMenuInfo != null) {
/* 116 */       str7 = leftMenuInfo.getSelectedContent();
/*     */     }
/* 118 */     if (!"".equals(paramString2)) {
/* 119 */       str7 = paramString2;
/*     */     }
/* 121 */     str7 = str7 + "|";
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 126 */     boolean bool4 = false;
/*     */     
/* 128 */     if ("".equals(paramString1) || str1.equals(paramString1)) {
/* 129 */       paramString1 = str1;
/* 130 */       bool4 = true;
/*     */     } else {
/* 132 */       recordSet.executeSql("SELECT * FROM HrmResource WHERE ID = " + paramString1 + " AND managerStr LIKE '%," + str1 + ",%'");
/*     */ 
/*     */       
/* 135 */       if (recordSet.next()) {
/* 136 */         bool4 = true;
/*     */       }
/*     */     } 
/*     */     
/* 140 */     char c = Util.getSeparator();
/*     */     
/* 142 */     ResourceComInfo resourceComInfo = null;
/* 143 */     CustomerInfoComInfo customerInfoComInfo = null;
/*     */     try {
/* 145 */       resourceComInfo = new ResourceComInfo();
/* 146 */       customerInfoComInfo = new CustomerInfoComInfo();
/* 147 */     } catch (Exception exception) {
/*     */       
/* 149 */       exception.printStackTrace();
/*     */     } 
/* 151 */     String str8 = Util.toScreen(resourceComInfo.getResourcename(paramString1), paramUser
/* 152 */         .getLanguage());
/*     */     
/* 154 */     if (str6.equals("2")) {
/* 155 */       str8 = Util.toScreen(customerInfoComInfo
/* 156 */           .getCustomerInfoname("" + paramUser.getUID()), paramUser
/* 157 */           .getLanguage());
/*     */     }
/* 159 */     String str9 = "";
/* 160 */     String str10 = "";
/* 161 */     String str11 = "";
/* 162 */     String str12 = "";
/* 163 */     String str13 = "";
/* 164 */     String str14 = "";
/* 165 */     String str15 = "";
/* 166 */     String str16 = "";
/* 167 */     String str17 = "";
/*     */     
/* 169 */     ArrayList arrayList1 = new ArrayList();
/* 170 */     ArrayList arrayList2 = new ArrayList();
/* 171 */     ArrayList arrayList3 = new ArrayList();
/* 172 */     ArrayList arrayList4 = new ArrayList();
/* 173 */     ArrayList arrayList5 = new ArrayList();
/* 174 */     ArrayList arrayList6 = new ArrayList();
/* 175 */     ArrayList arrayList7 = new ArrayList();
/* 176 */     ArrayList arrayList8 = new ArrayList();
/* 177 */     Hashtable<Object, Object> hashtable = new Hashtable<>();
/* 178 */     int k = 0;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 185 */     StringBuffer stringBuffer = new StringBuffer();
/* 186 */     if ("1".equals(str2)) {
/* 187 */       stringBuffer.append("select count(a.requestid) wfCount ");
/*     */     } else {
/* 189 */       stringBuffer.append("select count(distinct a.requestid) wfCount ");
/*     */     } 
/* 191 */     stringBuffer.append("  from workflow_currentoperator a ");
/*     */     
/* 193 */     if (recordSet.getDBType().equals("mysql")) {
/* 194 */       stringBuffer.append(", workflow_requestbase c  ");
/*     */     }
/*     */     
/* 197 */     stringBuffer
/*     */       
/* 199 */       .append("  where (isremark in('1','5','8','9','7','11') or ((isremark='0' and (takisremark is null or takisremark='0' )))) ");
/* 200 */     stringBuffer.append("   and islasttimes = 1 ");
/* 201 */     if ("1".equals(str2)) {
/* 202 */       stringBuffer.append("   and a.userid in ( ").append(str3);
/*     */     } else {
/* 204 */       stringBuffer.append("   and a.userid in ( ").append(paramString1);
/*     */     } 
/* 206 */     stringBuffer.append("  ) and a.usertype = ").append(bool3);
/* 207 */     stringBuffer.append(" and (a.isprocessing = '' or a.isprocessing is null) ");
/*     */     
/* 209 */     if (recordSet.getDBType().equals("mysql")) {
/* 210 */       stringBuffer.append("   and a.requestid = c.requestid");
/* 211 */       stringBuffer.append("   and ");
/*     */     } else {
/* 213 */       stringBuffer.append("   and exists (select 1 from workflow_requestbase c ");
/* 214 */       stringBuffer.append("   where ");
/*     */     } 
/* 216 */     if (recordSet.getDBType().equals("postgresql")) {
/* 217 */       stringBuffer.append("   (c.deleted <> 1 or c.deleted is null ) ");
/*     */     } else {
/* 219 */       stringBuffer.append("   (c.deleted <> 1 or c.deleted is null or c.deleted='') ");
/* 220 */     }  if (HrmClassifiedProtectionBiz.isOpenClassification()) {
/* 221 */       stringBuffer.append(" and c.seclevel >= " + (new HrmClassifiedProtectionBiz()).getMaxResourceSecLevelById(paramUser.getUID() + "") + " ");
/*     */     }
/*     */     
/* 224 */     stringBuffer.append(" and a.workflowid in(select id from workflow_base where (isvalid='1' or isvalid='3') and (activeversionid is null or activeversionid=0 or activeversionid in(select id from workflow_base where isvalid='1')) )");
/* 225 */     if (recordSet.getDBType().equals("oracle")) {
/* 226 */       stringBuffer.append(" and (nvl(c.currentstatus,-1) = -1 or (nvl(c.currentstatus,-1)=0 and c.creater=" + paramUser
/* 227 */           .getUID() + ")) ");
/* 228 */     } else if (recordSet.getDBType().equals("mysql")) {
/* 229 */       stringBuffer.append(" and (ifnull(c.currentstatus,-1) = -1 or (ifnull(c.currentstatus,-1)=0 and c.creater=" + paramUser
/* 230 */           .getUID() + ")) ");
/*     */     } else {
/* 232 */       stringBuffer.append(" and (isnull(c.currentstatus,-1) = -1 or (isnull(c.currentstatus,-1)=0 and c.creater=" + paramUser
/* 233 */           .getUID() + ")) ");
/*     */     } 
/* 235 */     if (!recordSet.getDBType().equals("mysql")) {
/* 236 */       stringBuffer.append("           and c.requestid = a.requestid)");
/*     */     }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 251 */     if (!bool4)
/*     */     {
/*     */ 
/*     */       
/* 255 */       if ("1".equals(str2)) {
/* 256 */         stringBuffer
/* 257 */           .append(" AND EXISTS (SELECT NULL FROM workFlow_CurrentOperator b WHERE a.workflowid = b.workflowid AND a.requestid = b.requestid AND b.userid=" + str3 + " and b.usertype= " + bool3 + ") ");
/*     */       
/*     */       }
/*     */       else {
/*     */         
/* 262 */         stringBuffer
/* 263 */           .append(" AND EXISTS (SELECT NULL FROM workFlow_CurrentOperator b WHERE a.workflowid = b.workflowid AND a.requestid = b.requestid AND b.userid in (" + paramUser
/* 264 */             .getUID() + ")  and b.usertype= " + bool3 + ") ");
/*     */       } 
/*     */     }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 275 */     recordSet.executeSql(stringBuffer.toString());
/*     */     
/* 277 */     while (recordSet.next()) {
/* 278 */       k = Util.getIntValue(recordSet.getString("wfCount"));
/*     */     }
/* 280 */     RequestUtil requestUtil = new RequestUtil();
/* 281 */     if (requestUtil.getOfsSetting().getIsuse() == 1) {
/* 282 */       String str = "select COUNT(distinct requestid) from ofs_todo_data where 1=1 " + RequestUtil.getSqlWhere("0", "1".equals(str2) ? str3 : paramString1, "");
/* 283 */       if (!bool4) {
/* 284 */         str = str + " and exists (select 1 from ofs_todo_data otd where otd.requestid=ofs_todo_data.requestid and otd.workflowid=ofs_todo_data.workflowid and otd.userid in (" + ("1".equals(str2) ? str3 : (String)Integer.valueOf(paramUser.getUID())) + "))";
/*     */       }
/* 286 */       recordSet.executeSql(str);
/* 287 */       if (recordSet.next()) {
/* 288 */         k += Util.getIntValue(recordSet.getString(1), 0);
/*     */       }
/*     */     } 
/*     */     
/* 292 */     return k;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/search/WorkflowRequestUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */