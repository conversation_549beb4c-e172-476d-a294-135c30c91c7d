/*      */ package weaver.workflow.layout;
/*      */ 
/*      */ import com.engine.workflow.util.WorkflowOvertimeSettingsUtil;
/*      */ import java.awt.Point;
/*      */ import java.io.BufferedReader;
/*      */ import java.io.ByteArrayInputStream;
/*      */ import java.io.IOException;
/*      */ import java.io.InputStream;
/*      */ import java.io.PrintWriter;
/*      */ import java.util.ArrayList;
/*      */ import java.util.HashMap;
/*      */ import java.util.Hashtable;
/*      */ import java.util.Iterator;
/*      */ import java.util.List;
/*      */ import java.util.Map;
/*      */ import javax.servlet.ServletException;
/*      */ import javax.servlet.ServletInputStream;
/*      */ import javax.servlet.http.HttpServlet;
/*      */ import javax.servlet.http.HttpServletRequest;
/*      */ import javax.servlet.http.HttpServletResponse;
/*      */ import oracle.sql.CLOB;
/*      */ import org.dom4j.Document;
/*      */ import org.dom4j.DocumentHelper;
/*      */ import org.dom4j.Element;
/*      */ import org.dom4j.io.SAXReader;
/*      */ import weaver.conn.ConnStatement;
/*      */ import weaver.conn.RecordSet;
/*      */ import weaver.file.Prop;
/*      */ import weaver.general.TimeUtil;
/*      */ import weaver.general.Util;
/*      */ import weaver.hrm.HrmUserVarify;
/*      */ import weaver.hrm.User;
/*      */ import weaver.rdeploy.workflow.WorkflowInitialization;
/*      */ import weaver.security.util.SecurityMethodUtil;
/*      */ import weaver.systeminfo.SysMaintenanceLog;
/*      */ import weaver.systeminfo.SystemEnv;
/*      */ import weaver.workflow.workflow.WFNodeMainManager;
/*      */ import weaver.workflow.workflow.WFNodePortalMainManager;
/*      */ import weaver.workflow.workflow.WorkflowNodeComInfo;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ public class WorkflowXmlParser
/*      */   extends HttpServlet
/*      */ {
/*   49 */   private static String XML_ElEMENT_TOPFLOW = "TopFlow";
/*   50 */   private static String XML_ELEMENT_PROCS = "Procs";
/*   51 */   private static String XML_ELEMENT_STEPS = "Steps";
/*   52 */   private static String XML_ELEMENT_PROC = "Proc";
/*   53 */   private static String XML_ELEMENT_STEP = "Step";
/*      */   
/*   55 */   private static String XML_ELEMENT_GROUPS = "Groups";
/*   56 */   private static String XML_ELEMENT_GROUP = "Group";
/*      */   
/*   58 */   private static String XML_ELEMENT_BASEPROPERTIES = "BaseProperties";
/*   59 */   private static String XML_ELEMENT_VMLPROPERTIES = "VMLProperties";
/*      */ 
/*      */   
/*   62 */   private static String XML_ATTRIBUTE_ID = "id";
/*   63 */   private static String XML_ATTRIBUTE_TEXT = "text";
/*   64 */   private static String XML_ATTRIBUTE_PROCTYPE = "procType";
/*   65 */   private static String XML_ATTRIBUTE_SHAPETYPE = "shapetype";
/*   66 */   private static String XML_ATTRIBUTE_X = "x";
/*   67 */   private static String XML_ATTRIBUTE_Y = "y";
/*   68 */   private static String XML_ATTRIBUTE_WIDTH = "width";
/*   69 */   private static String XML_ATTRIBUTE_HEIGHT = "height";
/*   70 */   private static String XML_ATTRIBUTE_ZINDEX = "zIndex";
/*   71 */   private static String XML_ATTRIBUTE_FROM = "from";
/*   72 */   private static String XML_ATTRIBUTE_FORMID = "formid";
/*   73 */   private static String XML_ATTRIBUTE_TO = "to";
/*   74 */   private static String XML_ATTRIBUTE_POINTS = "points";
/*      */   
/*   76 */   private static String XML_ATTRIBUTE_NEW_POINTS = "newPoints";
/*      */   
/*   78 */   private static String XML_ATTRIBUTE_ISBILL = "isBill";
/*   79 */   private static String XML_ATTRIBUTE_ISCUST = "isCust";
/*   80 */   private static String XML_ATTRIBUTE_REMINDMSG = "remindMsg";
/*   81 */   private static String XML_ATTRIBUTE_ISBUILDCODE = "isBuildCode";
/*   82 */   private static String XML_ATTRIBUTE_ISREJECT = "isreject";
/*   83 */   private static String XML_ATTRIBUTE_HASROLE = "hasRole";
/*   84 */   private static String XML_ATTRIBUTE_HASCONDITION = "hasCondition";
/*      */   
/*   86 */   private static String XML_ATTRIBUTE_HASNODEPRO = "hasNodePro";
/*   87 */   private static String XML_ATTRIBUTE_HASCUSRIGKEY = "hasCusRigKey";
/*   88 */   private static String XML_ATTRIBUTE_HASNODEBEFADDOPR = "hasNodeBefAddOpr";
/*   89 */   private static String XML_ATTRIBUTE_HASNODEAFTADDOPR = "hasNodeAftAddOpr";
/*   90 */   private static String XML_ATTRIBUTE_HASLOGVIEWSCO = "hasLogViewSco";
/*   91 */   private static String XML_ATTRIBUTE_HASNODEFORFIE = "hasNodeForFie";
/*      */ 
/*      */   
/*   94 */   private static String XML_ATTRIBUTE_HASOPERATETITLE = "hasOperateTitle";
/*   95 */   private static String XML_ATTRIBUTE_HASOPERATESIGN = "hasOperateSign";
/*   96 */   private static String XML_ATTRIBUTE_HASOPERATEFORWARD = "hasOperateForward";
/*   97 */   private static String XML_ATTRIBUTE_HASOPERATEFREEWF = "hasOperateFreewf";
/*   98 */   private static String XML_ATTRIBUTE_HASOPERATESUBWF = "hasOperateSubwf";
/*   99 */   private static String XML_ATTRIBUTE_HASOPERATEEXCEPTION = "hasOperateException";
/*      */   
/*  101 */   private static String XML_ATTRIBUTE_PASSNUM = "passNum";
/*  102 */   private static String XML_ATTRIBUTE_DELNODE = "delNodeIds";
/*  103 */   private static String XML_ATTRIBUTE_DELSTEP = "delStepIds";
/*  104 */   private static String XML_ATTRIBUTE_DELGROUP = "delGroupIds";
/*      */   
/*  106 */   private static String XML_ATTRIBUTE_DIRECTIONFROM = "directionfrom";
/*  107 */   private static String XML_ATTRIBUTE_DIRECTIONTO = "directionto";
/*  108 */   private static String XML_ATTRIBUTE_NODEATTRIBUTE = "nodeattribute";
/*  109 */   private static String XML_ATTRIBUTE_ISMUSTPASS = "ismustpass";
/*  110 */   private static String XML_ATTRIBUTE_NODETYPE = "nodetype";
/*  111 */   private static String XML_ATTRIBUTE_PASSTYPE = "passtype";
/*      */   
/*  113 */   private static String XML_ATTRIBUTE_STARTDIRECTION = "startDirection";
/*  114 */   private static String XML_ATTRIBUTE_ENDDIRECTION = "endDirection";
/*  115 */   private static String XML_ATTRIBUTE_DIRECTION = "direction";
/*      */ 
/*      */   
/*  118 */   private static String SHAPETYPE_PROC = "RoundRect";
/*  119 */   private static String SHAPETYPE_STEP = "PolyLine";
/*      */ 
/*      */   
/*  122 */   private static String NODETYPE_CREATE = "create";
/*  123 */   private static String NODETYPE_REALIZE = "realize";
/*  124 */   private static String NODETYPE_APPROVE = "approve";
/*  125 */   private static String NODETYPE_FORK = "fork";
/*  126 */   private static String NODETYPE_JOIN = "join";
/*  127 */   private static String NODETYPE_CHILD = "child";
/*  128 */   private static String NODETYPE_PROCESS = "process";
/*      */   
/*  130 */   private static int MAX_WIDTH = 1000;
/*  131 */   private static int NODE_SPACE = 50;
/*  132 */   private static int NODE_LEFT_PANDING = 25;
/*      */ 
/*      */   
/*  135 */   private String workflowId = "";
/*  136 */   private String delNodeIds = "";
/*  137 */   private String delStepIds = "";
/*  138 */   private String delGroupIds = "";
/*      */ 
/*      */   
/*      */   private User user;
/*      */ 
/*      */   
/*  144 */   private int status = 0;
/*  145 */   private String errormsg = "";
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String parseWorkflowToXML(String paramString) {
/*  153 */     Workflow workflow = readWorkflowFromDB(paramString, this.workflowId, this.user.getLanguage());
/*      */ 
/*      */     
/*  156 */     Document document = null;
/*  157 */     document = DocumentHelper.createDocument();
/*      */ 
/*      */     
/*  160 */     Element element1 = document.addElement(XML_ElEMENT_TOPFLOW);
/*  161 */     element1.addAttribute(XML_ATTRIBUTE_ID, workflow.id + "");
/*  162 */     element1.addAttribute(XML_ATTRIBUTE_FORMID, workflow.getFormID());
/*  163 */     element1.addAttribute(XML_ATTRIBUTE_ISBILL, workflow.getIsBill() + "");
/*  164 */     element1.addAttribute(XML_ATTRIBUTE_ISCUST, workflow.getIsCust() + "");
/*  165 */     element1.addAttribute(XML_ATTRIBUTE_TEXT, workflow.getWorkflowName());
/*      */ 
/*      */     
/*  168 */     Element element2 = element1.addElement(XML_ELEMENT_PROCS);
/*      */ 
/*      */     
/*  171 */     for (byte b1 = 0; b1 < workflow.nodes.size(); b1++) {
/*  172 */       WorkflowNode workflowNode = workflow.nodes.get(b1);
/*      */       
/*  174 */       Element element5 = element2.addElement(XML_ELEMENT_PROC);
/*      */       
/*  176 */       Element element6 = element5.addElement(XML_ELEMENT_BASEPROPERTIES);
/*  177 */       element6.addAttribute(XML_ATTRIBUTE_ID, workflowNode.getId() + "");
/*  178 */       element6.addAttribute(XML_ATTRIBUTE_TEXT, workflowNode.getName());
/*      */       
/*  180 */       element6.addAttribute(XML_ATTRIBUTE_HASNODEPRO, workflowNode.isHasNodePro() + "");
/*  181 */       element6.addAttribute(XML_ATTRIBUTE_HASCUSRIGKEY, workflowNode.isHasCusRigKey() + "");
/*  182 */       element6.addAttribute(XML_ATTRIBUTE_HASNODEBEFADDOPR, workflowNode.isHasNodeBefAddOpr() + "");
/*  183 */       element6.addAttribute(XML_ATTRIBUTE_HASNODEAFTADDOPR, workflowNode.isHasNodeAftAddOpr() + "");
/*  184 */       element6.addAttribute(XML_ATTRIBUTE_HASLOGVIEWSCO, workflowNode.isHasLogViewSco() + "");
/*  185 */       element6.addAttribute(XML_ATTRIBUTE_HASNODEFORFIE, workflowNode.isHasNodeForFie() + "");
/*      */ 
/*      */       
/*  188 */       element6.addAttribute(XML_ATTRIBUTE_HASOPERATETITLE, workflowNode.isHasOperateTitle() + "");
/*  189 */       element6.addAttribute(XML_ATTRIBUTE_HASOPERATESIGN, workflowNode.isHasOperateSign() + "");
/*  190 */       element6.addAttribute(XML_ATTRIBUTE_HASOPERATEFORWARD, workflowNode.isHasOperateForward() + "");
/*  191 */       element6.addAttribute(XML_ATTRIBUTE_HASOPERATEFREEWF, workflowNode.isHasOperateFreewf() + "");
/*  192 */       element6.addAttribute(XML_ATTRIBUTE_HASOPERATESUBWF, workflowNode.isHasOperateSubwf() + "");
/*  193 */       element6.addAttribute(XML_ATTRIBUTE_HASOPERATEEXCEPTION, workflowNode.isHasOperateException() + "");
/*      */ 
/*      */       
/*  196 */       int i = Util.getIntValue(workflowNode.getNodeAttribute());
/*  197 */       if (i == 1) {
/*  198 */         element6.addAttribute(XML_ATTRIBUTE_PROCTYPE, NODETYPE_FORK);
/*  199 */       } else if (i == 3 || i == 4 || i == 5) {
/*  200 */         element6.addAttribute(XML_ATTRIBUTE_PASSTYPE, i + "");
/*  201 */         element6.addAttribute(XML_ATTRIBUTE_PROCTYPE, NODETYPE_JOIN);
/*      */       } else {
/*  203 */         switch (Util.getIntValue(workflowNode.getNodeType())) {
/*      */           case 0:
/*  205 */             element6.addAttribute(XML_ATTRIBUTE_PROCTYPE, NODETYPE_CREATE);
/*      */             break;
/*      */           case 1:
/*  208 */             element6.addAttribute(XML_ATTRIBUTE_PROCTYPE, NODETYPE_APPROVE);
/*      */             break;
/*      */           case 2:
/*  211 */             element6.addAttribute(XML_ATTRIBUTE_PROCTYPE, NODETYPE_REALIZE);
/*      */             break;
/*      */           case 3:
/*  214 */             element6.addAttribute(XML_ATTRIBUTE_PROCTYPE, NODETYPE_PROCESS);
/*      */             break;
/*      */         } 
/*      */ 
/*      */       
/*      */       } 
/*  220 */       element6.addAttribute(XML_ATTRIBUTE_NODETYPE, Util.getIntValue(workflowNode.getNodeType()) + "");
/*  221 */       Element element7 = element5.addElement(XML_ELEMENT_VMLPROPERTIES);
/*  222 */       element7.addAttribute(XML_ATTRIBUTE_SHAPETYPE, SHAPETYPE_PROC);
/*  223 */       element7.addAttribute(XML_ATTRIBUTE_X, workflowNode.x + "");
/*  224 */       element7.addAttribute(XML_ATTRIBUTE_Y, workflowNode.y + "");
/*  225 */       element7.addAttribute(XML_ATTRIBUTE_WIDTH, workflowNode.getWidth() + "");
/*  226 */       element7.addAttribute(XML_ATTRIBUTE_HEIGHT, workflowNode.getHeight() + "");
/*  227 */       element7.addAttribute(XML_ATTRIBUTE_ZINDEX, workflowNode.getLevel() + "");
/*  228 */       element7.addAttribute(XML_ATTRIBUTE_NODEATTRIBUTE, workflowNode.getNodeAttribute());
/*  229 */       element7.addAttribute(XML_ATTRIBUTE_PASSNUM, "" + workflowNode.getPassnum());
/*      */     } 
/*      */ 
/*      */ 
/*      */     
/*  234 */     Element element3 = element1.addElement(XML_ELEMENT_STEPS);
/*  235 */     for (byte b2 = 0; b2 < workflow.lines.size(); b2++) {
/*  236 */       WorkflowLine workflowLine = workflow.lines.get(b2);
/*  237 */       Element element5 = element3.addElement(XML_ELEMENT_STEP);
/*      */       
/*  239 */       Element element6 = element5.addElement(XML_ELEMENT_BASEPROPERTIES);
/*  240 */       element6.addAttribute(XML_ATTRIBUTE_ID, workflowLine.getId() + "");
/*  241 */       element6.addAttribute(XML_ATTRIBUTE_TEXT, workflowLine.getLineName());
/*  242 */       element6.addAttribute(XML_ATTRIBUTE_FROM, workflowLine.fromNodeId + "");
/*  243 */       element6.addAttribute(XML_ATTRIBUTE_TO, workflowLine.toNodeId + "");
/*  244 */       element6.addAttribute(XML_ATTRIBUTE_REMINDMSG, workflowLine.getRemindMsg());
/*  245 */       element6.addAttribute(XML_ATTRIBUTE_ISBUILDCODE, workflowLine.getIsBuildCodeString().equals("1") ? "true" : "false");
/*  246 */       element6.addAttribute(XML_ATTRIBUTE_ISREJECT, workflowLine.getIsReject().equals("1") ? "true" : "false");
/*  247 */       element6.addAttribute(XML_ATTRIBUTE_ISMUSTPASS, workflowLine.getIsMustpass());
/*  248 */       element6.addAttribute(XML_ATTRIBUTE_HASROLE, workflowLine.isHasRole() + "");
/*      */       
/*  250 */       element6.addAttribute(XML_ATTRIBUTE_STARTDIRECTION, workflowLine.getStartDirection() + "");
/*  251 */       element6.addAttribute(XML_ATTRIBUTE_ENDDIRECTION, workflowLine.getEndDirection() + "");
/*  252 */       element6.addAttribute(XML_ATTRIBUTE_NEW_POINTS, workflowLine.getNewPoints() + "");
/*  253 */       if (!workflowLine.getCondition().equals("") || workflowLine.getNodePassHour() + workflowLine.getNodePassMinute() > 0) {
/*  254 */         element6.addAttribute(XML_ATTRIBUTE_HASCONDITION, "true");
/*      */       } else {
/*  256 */         element6.addAttribute(XML_ATTRIBUTE_HASCONDITION, "false");
/*      */       } 
/*      */       
/*  259 */       Element element7 = element5.addElement(XML_ELEMENT_VMLPROPERTIES);
/*  260 */       String str1 = "";
/*  261 */       for (byte b = 0; b < workflowLine.getValidCtrlPointCount(); b++) {
/*  262 */         Point point = workflowLine.getCtrlPoint(b);
/*  263 */         str1 = str1 + point.getX() + "," + point.getY() + ",";
/*      */       } 
/*  265 */       if (str1.length() > 0) {
/*  266 */         str1 = str1.substring(0, str1.length() - 1);
/*      */       }
/*  268 */       element7.addAttribute(XML_ATTRIBUTE_POINTS, str1);
/*  269 */       element7.addAttribute(XML_ATTRIBUTE_SHAPETYPE, SHAPETYPE_STEP);
/*      */     } 
/*      */ 
/*      */     
/*  273 */     Element element4 = element1.addElement(XML_ELEMENT_GROUPS);
/*  274 */     for (byte b3 = 0; b3 < workflow.getGroups().size(); b3++) {
/*  275 */       WorkflowGroup workflowGroup = workflow.getGroups().get(b3);
/*  276 */       Element element = element4.addElement(XML_ELEMENT_GROUP);
/*      */       
/*  278 */       element.addAttribute(XML_ATTRIBUTE_ID, workflowGroup.getId() + "");
/*  279 */       element.addAttribute(XML_ATTRIBUTE_TEXT, workflowGroup.getGroupname());
/*  280 */       element.addAttribute(XML_ATTRIBUTE_DIRECTION, workflowGroup.getDirection() + "");
/*  281 */       element.addAttribute(XML_ATTRIBUTE_X, workflowGroup.getX() + "");
/*  282 */       element.addAttribute(XML_ATTRIBUTE_Y, workflowGroup.getY() + "");
/*  283 */       element.addAttribute(XML_ATTRIBUTE_WIDTH, workflowGroup.getWidth() + "");
/*  284 */       element.addAttribute(XML_ATTRIBUTE_HEIGHT, workflowGroup.getHeight() + "");
/*  285 */       element.addAttribute("isNew", "false");
/*      */     } 
/*      */ 
/*      */     
/*  289 */     String str = "";
/*  290 */     str = document.asXML();
/*      */     try {
/*  292 */       str = Util.replace(str, "\n", "", 0);
/*  293 */     } catch (Exception exception) {
/*      */       
/*  295 */       exception.printStackTrace();
/*      */     } 
/*  297 */     return str;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public Workflow parseXmlToWorkflow(HttpServletRequest paramHttpServletRequest) {
/*  306 */     this.errormsg = SystemEnv.getHtmlLabelName(18758, this.user.getLanguage());
/*  307 */     Hashtable<Object, Object> hashtable = new Hashtable<>();
/*      */     
/*  309 */     Workflow workflow = new Workflow();
/*      */ 
/*      */     
/*  312 */     SAXReader sAXReader = new SAXReader();
/*      */     try {
/*  314 */       SecurityMethodUtil.setReaderFeature(sAXReader);
/*  315 */     } catch (Exception exception) {
/*  316 */       exception.printStackTrace();
/*      */     } 
/*  318 */     Document document = null; try {
/*      */       ServletInputStream servletInputStream;
/*  320 */       ByteArrayInputStream byteArrayInputStream = null;
/*  321 */       String str = Util.null2String(paramHttpServletRequest.getParameter("isPost"));
/*  322 */       if ("1".equals(str)) {
/*  323 */         String str1 = Util.null2String(paramHttpServletRequest.getParameter("xml"));
/*      */         
/*  325 */         if (!"".equals(str1)) {
/*  326 */           byteArrayInputStream = new ByteArrayInputStream(str1.getBytes("ISO8859_1"));
/*      */         } else {
/*  328 */           servletInputStream = paramHttpServletRequest.getInputStream();
/*      */         } 
/*      */       } else {
/*  331 */         servletInputStream = paramHttpServletRequest.getInputStream();
/*      */       } 
/*  333 */       document = sAXReader.read((InputStream)servletInputStream);
/*      */       
/*  335 */       Element element = document.getRootElement();
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*      */       try {
/*  346 */         this.delNodeIds = Util.null2String(element.attributeValue(XML_ATTRIBUTE_DELNODE));
/*  347 */         this.delStepIds = Util.null2String(element.attributeValue(XML_ATTRIBUTE_DELSTEP));
/*  348 */         this.delGroupIds = Util.null2String(element.attributeValue(XML_ATTRIBUTE_DELGROUP));
/*      */       }
/*  350 */       catch (Exception exception) {
/*  351 */         this.status = 1;
/*  352 */         this.errormsg = SystemEnv.getHtmlLabelName(16332, this.user.getLanguage());
/*  353 */         exception.printStackTrace();
/*      */       } 
/*  355 */       workflow.id = Util.getIntValue(element.attributeValue(XML_ATTRIBUTE_ID));
/*      */       
/*  357 */       List<Element> list1 = element.elements(XML_ELEMENT_PROCS);
/*  358 */       List<Element> list2 = ((Element)list1.get(0)).elements(XML_ELEMENT_PROC);
/*      */ 
/*      */       
/*  361 */       RecordSet recordSet = new RecordSet();
/*  362 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  363 */       recordSet.executeSql("select nodeid, nodeorder from workflow_flownode where workflowid=" + workflow.id);
/*  364 */       while (recordSet.next()) {
/*  365 */         String str1 = Util.null2String(recordSet.getString("nodeid"));
/*  366 */         String str2 = Util.null2String(recordSet.getString("nodeorder"));
/*  367 */         hashMap1.put(str1, str2);
/*      */       } 
/*      */       
/*  370 */       byte b1 = 0;
/*  371 */       byte b2 = 1;
/*      */ 
/*      */       
/*  374 */       for (byte b3 = 0; b3 < list2.size(); b3++) {
/*      */         
/*  376 */         WorkflowNode workflowNode = new WorkflowNode();
/*      */ 
/*      */         
/*  379 */         Element element1 = list2.get(b3);
/*      */ 
/*      */         
/*  382 */         Element element2 = element1.elements(XML_ELEMENT_BASEPROPERTIES).get(0);
/*      */         
/*  384 */         workflowNode.id = Util.getIntValue(element2.attributeValue(XML_ATTRIBUTE_ID), 0);
/*      */         
/*  386 */         workflowNode.name = Util.null2String(element2.attributeValue(XML_ATTRIBUTE_TEXT));
/*  387 */         workflowNode.nodeType = Util.null2String(element2.attributeValue(XML_ATTRIBUTE_NODETYPE));
/*  388 */         String str1 = workflowNode.nodeType.equals("1") ? "1" : "0";
/*      */         
/*  390 */         workflowNode.setHasNodePro("true".equals(Util.null2String(element2.attributeValue(XML_ATTRIBUTE_HASNODEPRO))));
/*  391 */         workflowNode.setHasCusRigKey("true".equals(Util.null2String(element2.attributeValue(XML_ATTRIBUTE_HASCUSRIGKEY))));
/*  392 */         workflowNode.setHasNodeBefAddOpr("true".equals(Util.null2String(element2.attributeValue(XML_ATTRIBUTE_HASNODEBEFADDOPR))));
/*  393 */         workflowNode.setHasNodeAftAddOpr("true".equals(Util.null2String(element2.attributeValue(XML_ATTRIBUTE_HASNODEAFTADDOPR))));
/*  394 */         workflowNode.setHasLogViewSco("true".equals(Util.null2String(element2.attributeValue(XML_ATTRIBUTE_HASLOGVIEWSCO))));
/*  395 */         workflowNode.setHasNodeForFie("true".equals(Util.null2String(element2.attributeValue(XML_ATTRIBUTE_HASNODEFORFIE))));
/*      */ 
/*      */         
/*  398 */         workflowNode.setHasOperateTitle("true".equals(Util.null2String(element2.attributeValue(XML_ATTRIBUTE_HASOPERATETITLE))));
/*  399 */         workflowNode.setHasOperateSign("true".equals(Util.null2String(element2.attributeValue(XML_ATTRIBUTE_HASOPERATESIGN))));
/*  400 */         workflowNode.setHasOperateForward("true".equals(Util.null2String(element2.attributeValue(XML_ATTRIBUTE_HASOPERATEFORWARD))));
/*  401 */         workflowNode.setHasOperateFreewf("true".equals(Util.null2String(element2.attributeValue(XML_ATTRIBUTE_HASOPERATEFREEWF))));
/*  402 */         workflowNode.setHasOperateSubwf("true".equals(Util.null2String(element2.attributeValue(XML_ATTRIBUTE_HASOPERATESUBWF))));
/*  403 */         workflowNode.setHasOperateException("true".equals(Util.null2String(element2.attributeValue(XML_ATTRIBUTE_HASOPERATEEXCEPTION))));
/*      */ 
/*      */ 
/*      */         
/*  407 */         workflowNode.attrMap.put("nodename", workflowNode.name);
/*  408 */         if (!Util.null2String(element2.attributeValue(XML_ATTRIBUTE_NODEATTRIBUTE)).equals("")) {
/*  409 */           workflowNode.attrMap.put("nodeattribute", Util.null2String(element2.attributeValue(XML_ATTRIBUTE_NODEATTRIBUTE)));
/*  410 */           workflowNode.setNodeAttribute(Util.null2String(element2.attributeValue(XML_ATTRIBUTE_NODEATTRIBUTE)));
/*      */         } else {
/*      */           
/*  413 */           workflowNode.attrMap.put("nodeattribute", "0");
/*  414 */           workflowNode.setNodeAttribute("0");
/*      */         } 
/*  416 */         workflowNode.attrMap.put("nodeattribute", Util.null2String(element2.attributeValue(XML_ATTRIBUTE_NODEATTRIBUTE)));
/*  417 */         workflowNode.attrMap.put("passnum", Util.null2String(element2.attributeValue(XML_ATTRIBUTE_PASSNUM)));
/*  418 */         workflowNode.attrMap.put("isreject", str1);
/*      */ 
/*      */         
/*  421 */         Element element3 = element1.elements(XML_ELEMENT_VMLPROPERTIES).get(0);
/*  422 */         workflowNode.x = (int)Util.getDoubleValue(Util.replace(element3.attributeValue(XML_ATTRIBUTE_X), "px", "", 0));
/*  423 */         workflowNode.y = (int)Util.getDoubleValue(Util.replace(element3.attributeValue(XML_ATTRIBUTE_Y), "px", "", 0));
/*      */ 
/*      */         
/*  426 */         if (Util.null2String(element2.attributeValue("isNew")).equals("true")) {
/*      */ 
/*      */           
/*  429 */           workflowNode.setNodeorder(list2.size() + b1);
/*  430 */           b1++;
/*      */           
/*  432 */           String str2 = WorkflowDesignOperatoinServlet.addWorkflowNode(Util.getIntValue(element.attributeValue(XML_ATTRIBUTE_ID)), Util.getIntValue(element.attributeValue(XML_ATTRIBUTE_FORMID)), Util.getIntValue(element.attributeValue(XML_ATTRIBUTE_ISBILL)), this.delNodeIds, paramHttpServletRequest.getRemoteAddr(), this.user, workflowNode);
/*  433 */           if (Util.getIntValue(str2) <= 0) {
/*      */             continue;
/*      */           }
/*      */           
/*  437 */           hashtable.put(workflowNode.id + "", str2);
/*      */           
/*  439 */           this.delNodeIds = "";
/*      */         } else {
/*  441 */           String str2 = (String)hashMap1.get("" + workflowNode.id);
/*  442 */           if ("".equals(str2)) {
/*  443 */             str2 = "" + b2;
/*      */           }
/*  445 */           workflowNode.setNodeorder(Util.getIntValue(str2));
/*  446 */           b2++;
/*      */         } 
/*      */         
/*  449 */         workflow.addNode(workflowNode);
/*      */         
/*      */         continue;
/*      */       } 
/*  453 */       List<Element> list3 = element.elements(XML_ELEMENT_STEPS);
/*  454 */       List<Element> list4 = ((Element)list3.get(0)).elements(XML_ELEMENT_STEP);
/*      */ 
/*      */       
/*  457 */       HashMap<Object, Object> hashMap2 = new HashMap<>();
/*  458 */       recordSet.executeSql("select id, linkorder from workflow_nodelink where workflowid=" + workflow.id);
/*  459 */       while (recordSet.next()) {
/*  460 */         String str1 = Util.null2String(recordSet.getString("id"));
/*  461 */         String str2 = Util.null2String(recordSet.getString("linkorder"));
/*  462 */         hashMap2.put(str1, str2);
/*      */       } 
/*      */       
/*  465 */       byte b4 = 0;
/*  466 */       byte b5 = 1;
/*      */       
/*  468 */       for (byte b6 = 0; b6 < list4.size(); b6++) {
/*      */         
/*  470 */         WorkflowLine workflowLine = new WorkflowLine();
/*      */         
/*  472 */         Element element1 = list4.get(b6);
/*      */         
/*  474 */         Element element2 = element1.elements(XML_ELEMENT_BASEPROPERTIES).get(0);
/*  475 */         workflowLine.id = Util.getIntValue(element2.attributeValue(XML_ATTRIBUTE_ID));
/*  476 */         workflowLine.fromNodeId = Util.getIntValue(element2.attributeValue(XML_ATTRIBUTE_FROM));
/*  477 */         workflowLine.toNodeId = Util.getIntValue(element2.attributeValue(XML_ATTRIBUTE_TO));
/*  478 */         if (hashtable.containsKey(workflowLine.fromNodeId + "")) {
/*  479 */           workflowLine.fromNodeId = Util.getIntValue((String)hashtable.get(workflowLine.fromNodeId + ""));
/*      */         }
/*  481 */         if (hashtable.containsKey(workflowLine.toNodeId + "")) {
/*  482 */           workflowLine.toNodeId = Util.getIntValue((String)hashtable.get(workflowLine.toNodeId + ""));
/*      */         }
/*  484 */         workflowLine.setLineName(Util.null2String(element2.attributeValue(XML_ATTRIBUTE_TEXT)));
/*  485 */         workflowLine.setRemindMsg(Util.null2String(element2.attributeValue(XML_ATTRIBUTE_REMINDMSG)));
/*  486 */         workflowLine.setIsBuildCodeString(Util.null2String(element2.attributeValue(XML_ATTRIBUTE_ISBUILDCODE)).equals("true") ? "1" : "");
/*  487 */         workflowLine.setIsMustpass(Util.null2String(element2.attributeValue(XML_ATTRIBUTE_ISMUSTPASS)));
/*      */         
/*  489 */         workflowLine.setHasRole("true".equals(Util.null2String(element2.attributeValue(XML_ATTRIBUTE_HASROLE))));
/*  490 */         workflowLine.setCondition(Util.null2String(element2.attributeValue(XML_ATTRIBUTE_HASCONDITION)));
/*  491 */         workflowLine.setStartDirection(Util.getIntValue(element2.attributeValue(XML_ATTRIBUTE_STARTDIRECTION)));
/*  492 */         workflowLine.setEndDirection(Util.getIntValue(element2.attributeValue(XML_ATTRIBUTE_ENDDIRECTION)));
/*      */ 
/*      */         
/*  495 */         Element element3 = element1.elements(XML_ELEMENT_VMLPROPERTIES).get(0);
/*  496 */         workflowLine.setNewPoints(Util.null2String(element3.attributeValue(XML_ATTRIBUTE_NEW_POINTS)));
/*      */ 
/*      */         
/*  499 */         workflowLine.attrMap.put("linkname", workflowLine.getLineName());
/*  500 */         workflowLine.attrMap.put("tipsinfo", workflowLine.getRemindMsg());
/*  501 */         workflowLine.attrMap.put("destnodeid", workflowLine.toNodeId + "");
/*  502 */         workflowLine.attrMap.put("isBulidCode", workflowLine.getIsBuildCodeString());
/*  503 */         workflowLine.attrMap.put("isreject", Util.null2String(element2.attributeValue(XML_ATTRIBUTE_ISREJECT).equals("true") ? "1" : ""));
/*  504 */         workflowLine.attrMap.put("ismustpass", workflowLine.getIsMustpass());
/*  505 */         workflowLine.attrMap.put(XML_ATTRIBUTE_DIRECTIONFROM, "" + Util.getIntValue(element2.attributeValue(XML_ATTRIBUTE_DIRECTIONFROM), -1));
/*  506 */         workflowLine.attrMap.put(XML_ATTRIBUTE_DIRECTIONTO, "" + Util.getIntValue(element2.attributeValue(XML_ATTRIBUTE_DIRECTIONTO), -1));
/*      */         
/*  508 */         workflowLine.attrMap.put("startDirection", Integer.valueOf(workflowLine.getStartDirection()));
/*  509 */         workflowLine.attrMap.put("endDirection", Integer.valueOf(workflowLine.getEndDirection()));
/*      */         
/*  511 */         workflowLine.attrMap.put("points", workflowLine.getNewPoints());
/*      */         
/*  513 */         String str1 = element3.attributeValue(XML_ATTRIBUTE_POINTS);
/*      */         
/*  515 */         String[] arrayOfString = Util.TokenizerString2(str1, ",");
/*  516 */         byte b8 = 1;
/*  517 */         byte b9 = 1;
/*  518 */         Point point = new Point();
/*  519 */         int i = arrayOfString.length;
/*  520 */         for (byte b10 = 0; b10 < arrayOfString.length; b10++) {
/*  521 */           if (b8 == 1) {
/*  522 */             point.x = Util.getIntValue(arrayOfString[b10]);
/*  523 */             b8++;
/*      */           }
/*  525 */           else if (b8 == 2) {
/*  526 */             point.y = Util.getIntValue(arrayOfString[b10]);
/*      */             
/*  528 */             if (b9 > 1 && b9 < i / 2)
/*      */             {
/*  530 */               workflowLine.controlPoints.add(point);
/*      */             }
/*  532 */             point = new Point();
/*      */             
/*  534 */             b8 = 1;
/*  535 */             b9++;
/*      */           } 
/*      */         } 
/*      */         
/*  539 */         if (Util.null2String(element2.attributeValue("isNew")).equals("true")) {
/*      */           
/*  541 */           workflowLine.setLinkorder(list4.size() + b4);
/*  542 */           b4++;
/*      */           
/*  544 */           String str2 = WorkflowDesignOperatoinServlet.addWorkflowLink(Util.getIntValue(element.attributeValue(XML_ATTRIBUTE_ID)), this.delStepIds, paramHttpServletRequest.getRemoteAddr(), this.user, workflowLine);
/*  545 */           workflowLine.id = Util.getIntValue(str2);
/*      */           
/*  547 */           this.delStepIds = "";
/*      */         } else {
/*  549 */           String str2 = (String)hashMap2.get("" + workflowLine.id);
/*  550 */           if ("".equals(str2)) {
/*  551 */             str2 = "" + b5;
/*      */           }
/*  553 */           workflowLine.setLinkorder(Util.getIntValue(str2));
/*  554 */           b5++;
/*      */         } 
/*  556 */         workflowLine.attrMap.put("linkorder", Integer.valueOf(workflowLine.getLinkorder()));
/*      */ 
/*      */         
/*  559 */         workflow.addLine(workflowLine);
/*      */       } 
/*      */ 
/*      */       
/*  563 */       ArrayList<WorkflowGroup> arrayList = new ArrayList();
/*      */       
/*  565 */       List<Element> list5 = element.elements(XML_ELEMENT_GROUPS);
/*  566 */       List<Element> list6 = new ArrayList();
/*      */       
/*  568 */       if (list5 != null && list5.size() > 0) {
/*  569 */         list6 = ((Element)list5.get(0)).elements(XML_ELEMENT_GROUP);
/*      */       }
/*      */       
/*  572 */       for (byte b7 = 0; b7 < list6.size(); b7++) {
/*  573 */         WorkflowGroup workflowGroup = new WorkflowGroup();
/*  574 */         Element element1 = list6.get(b7);
/*  575 */         workflowGroup.setId(Util.getIntValue(element1.attributeValue(XML_ATTRIBUTE_ID)));
/*  576 */         workflowGroup.setWorkflowid(workflow.id);
/*  577 */         workflowGroup.setGroupname(element1.attributeValue(XML_ATTRIBUTE_TEXT));
/*  578 */         workflowGroup.setDirection(Util.getIntValue(element1.attributeValue(XML_ATTRIBUTE_DIRECTION)));
/*  579 */         workflowGroup.setX(Double.parseDouble(element1.attributeValue(XML_ATTRIBUTE_X)));
/*  580 */         workflowGroup.setY(Double.parseDouble(element1.attributeValue(XML_ATTRIBUTE_Y)));
/*  581 */         workflowGroup.setWidth(Double.parseDouble(element1.attributeValue(XML_ATTRIBUTE_WIDTH)));
/*  582 */         workflowGroup.setHeight(Double.parseDouble(element1.attributeValue(XML_ATTRIBUTE_HEIGHT)));
/*  583 */         workflowGroup.setNew(Boolean.parseBoolean(element1.attributeValue("isNew")));
/*  584 */         arrayList.add(workflowGroup);
/*      */       } 
/*  586 */       workflow.setGroups(arrayList);
/*      */     }
/*  588 */     catch (Exception exception) {
/*  589 */       this.status = 1;
/*  590 */       this.errormsg = SystemEnv.getHtmlLabelName(16332, this.user.getLanguage());
/*  591 */       exception.printStackTrace();
/*      */     } 
/*      */     
/*  594 */     return workflow;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void doPost(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws ServletException, IOException {
/*  605 */     PrintWriter printWriter = paramHttpServletResponse.getWriter();
/*  606 */     String str = "";
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  617 */     this.user = HrmUserVarify.checkUser(paramHttpServletRequest, paramHttpServletResponse);
/*      */     
/*  619 */     if (this.user != null) {
/*      */       
/*  621 */       Workflow workflow = parseXmlToWorkflow(paramHttpServletRequest);
/*  622 */       if (this.status == 0) {
/*  623 */         writeWorkflowToDB(workflow, paramHttpServletRequest);
/*      */       }
/*      */       
/*  626 */       str = "var result = {status:\"" + this.status + "\",errormsg:\"" + this.errormsg + "\"}";
/*  627 */       paramHttpServletResponse.setContentType("text/html;charset=UTF-8");
/*      */       
/*  629 */       printWriter.print(str);
/*  630 */       printWriter.close();
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public Workflow readWorkflowFromDB(String paramString1, String paramString2, int paramInt) {
/*  644 */     Workflow workflow = new Workflow();
/*  645 */     RecordSet recordSet = new RecordSet();
/*      */     
/*  647 */     recordSet.executeSql("select max(drawypos) as drawypos from workflow_nodebase where id in (select nodeid from workflow_flownode where workflowid = " + paramString2 + ")");
/*  648 */     int i = 0;
/*  649 */     if (recordSet.next()) i = recordSet.getInt("drawypos"); 
/*  650 */     if (i == -1) i = 0; 
/*  651 */     if (i > 0) {
/*  652 */       i += NODE_SPACE;
/*      */     }
/*  654 */     int j = NODE_LEFT_PANDING;
/*  655 */     int k = NODE_LEFT_PANDING + i;
/*  656 */     boolean bool = false;
/*      */     
/*  658 */     recordSet.execute("select * from workflow_base where id=" + paramString2);
/*  659 */     while (recordSet.next()) {
/*  660 */       workflow.setFormID(recordSet.getString("formid"));
/*  661 */       workflow.setIsBill(recordSet.getInt("isbill"));
/*  662 */       workflow.setIsCust(recordSet.getInt("iscust"));
/*  663 */       workflow.id = recordSet.getInt("id");
/*  664 */       workflow.setWorkflowName(recordSet.getString("workflowname"));
/*      */     } 
/*      */ 
/*      */     
/*  668 */     ArrayList<WorkflowGroup> arrayList = new ArrayList();
/*      */     
/*  670 */     recordSet.execute("select * from workflow_groupinfo where workflowid=" + paramString2);
/*  671 */     while (recordSet.next()) {
/*  672 */       WorkflowGroup workflowGroup = new WorkflowGroup();
/*  673 */       workflowGroup.setId(recordSet.getInt("id"));
/*  674 */       workflowGroup.setWorkflowid(recordSet.getInt("workflowid"));
/*  675 */       workflowGroup.setGroupname(recordSet.getString("groupname"));
/*  676 */       workflowGroup.setDirection(recordSet.getInt("direction"));
/*  677 */       workflowGroup.setX(recordSet.getDouble("x"));
/*  678 */       workflowGroup.setY(recordSet.getDouble("y"));
/*  679 */       workflowGroup.setWidth(recordSet.getDouble("width"));
/*  680 */       workflowGroup.setHeight(recordSet.getDouble("height"));
/*  681 */       arrayList.add(workflowGroup);
/*      */     } 
/*  683 */     workflow.setGroups(arrayList);
/*      */ 
/*      */     
/*  686 */     recordSet.executeSql("select t1.*,t2.* from workflow_nodebase t1, workflow_flownode t2 where (t1.IsFreeNode is null or t1.IsFreeNode!='1') and t2.workflowid = " + paramString2 + " and t1.id = t2.nodeid order by t1.id");
/*  687 */     while (recordSet.next()) {
/*  688 */       WorkflowNode workflowNode = new WorkflowNode();
/*  689 */       workflowNode.id = recordSet.getInt("id");
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*  711 */       String str1 = Util.null2String(recordSet.getString("nodetitle"));
/*  712 */       if (!str1.equals("")) {
/*  713 */         workflowNode.setHasOperateTitle(true);
/*      */       }
/*  715 */       int m = Util.getIntValue(recordSet.getString("isFormSignature"), 0);
/*  716 */       int n = Util.getIntValue(recordSet.getString("issignmustinput"), 0);
/*  717 */       int i1 = Util.getIntValue(recordSet.getString("ishideinput"), 0);
/*  718 */       int i2 = Util.getIntValue(recordSet.getString("ishidearea"), 0);
/*  719 */       int i3 = Util.getIntValue(recordSet.getString("isfeedback"), 0);
/*  720 */       if (m == 1 || n == 1 || i1 == 1 || i2 == 1 || i3 == 1) {
/*  721 */         workflowNode.setHasOperateSign(true);
/*      */       }
/*  723 */       int i4 = Util.getIntValue(recordSet.getString("IsPendingForward"), 0);
/*  724 */       int i5 = Util.getIntValue(recordSet.getString("IsWaitForwardOpinion"), 0);
/*  725 */       int i6 = Util.getIntValue(recordSet.getString("IsSubmitedOpinion"), 0);
/*  726 */       int i7 = Util.getIntValue(recordSet.getString("IsSubmitForward"), 0);
/*  727 */       int i8 = Util.getIntValue(recordSet.getString("IsBeForwardSubmit"), 0);
/*  728 */       int i9 = Util.getIntValue(recordSet.getString("IsBeForwardModify"), 0);
/*  729 */       int i10 = Util.getIntValue(recordSet.getString("IsBeForwardPending"), 0);
/*  730 */       int i11 = Util.getIntValue(recordSet.getString("IsShowPendingForward"), 0);
/*  731 */       int i12 = Util.getIntValue(recordSet.getString("IsShowWaitForwardOpinion"), 0);
/*  732 */       int i13 = Util.getIntValue(recordSet.getString("IsShowBeForward"), 0);
/*  733 */       int i14 = Util.getIntValue(recordSet.getString("IsShowSubmitedOpinion"), 0);
/*  734 */       int i15 = Util.getIntValue(recordSet.getString("IsShowSubmitForward"), 0);
/*  735 */       int i16 = Util.getIntValue(recordSet.getString("IsShowBeForwardSubmit"), 0);
/*  736 */       int i17 = Util.getIntValue(recordSet.getString("IsShowBeForwardModify"), 0);
/*  737 */       int i18 = Util.getIntValue(recordSet.getString("IsShowBeForwardPending"), 0);
/*  738 */       int i19 = Util.getIntValue(recordSet.getString("IsAlreadyForward"), 0);
/*  739 */       if (i4 == 1 || i5 == 1 || i6 == 1 || i7 == 1 || i8 == 1 || i9 == 1 || i10 == 1 || i11 == 1 || i12 == 1 || i13 == 1 || i14 == 1 || i15 == 1 || i16 == 1 || i17 == 1 || i18 == 1 || i19 == 1)
/*      */       {
/*      */ 
/*      */ 
/*      */         
/*  744 */         workflowNode.setHasOperateForward(true);
/*      */       }
/*  746 */       int i20 = Util.getIntValue(recordSet.getString("IsFreeWorkflow"), 0);
/*  747 */       if (i20 == 1) {
/*  748 */         workflowNode.setHasOperateFreewf(true);
/*      */       }
/*      */       
/*  751 */       int i21 = Util.getIntValue(recordSet.getString("issubwfAllEnd"), 0);
/*  752 */       int i22 = Util.getIntValue(recordSet.getString("issubwfremind"), 0);
/*  753 */       int i23 = Util.getIntValue(recordSet.getString("subwffreeforword"), 0);
/*  754 */       if (i21 == 1 || i22 == 1 || i23 == 1) {
/*  755 */         workflowNode.setHasOperateSubwf(true);
/*      */       }
/*      */       
/*  758 */       int i24 = Util.getIntValue(recordSet.getString("useExceptionHandle"), 0);
/*  759 */       if (i24 == 1) {
/*  760 */         workflowNode.setHasOperateException(true);
/*      */       }
/*      */ 
/*      */       
/*  764 */       String str2 = "0";
/*  765 */       String str3 = "";
/*  766 */       String str4 = "";
/*  767 */       String str5 = "";
/*  768 */       String str6 = "";
/*  769 */       String str7 = "";
/*  770 */       String str8 = "";
/*  771 */       String str9 = "";
/*  772 */       String str10 = "";
/*  773 */       String str11 = "";
/*  774 */       String str12 = "";
/*  775 */       String str13 = "";
/*  776 */       String str14 = "";
/*  777 */       String str15 = "";
/*  778 */       String str16 = "";
/*  779 */       String str17 = "";
/*  780 */       String str18 = "";
/*  781 */       String str19 = "";
/*  782 */       String str20 = "";
/*  783 */       String str21 = "";
/*  784 */       String str22 = "";
/*  785 */       String str23 = "";
/*  786 */       String str24 = "";
/*  787 */       String str25 = "";
/*  788 */       String str26 = "";
/*  789 */       String str27 = "";
/*  790 */       String str28 = "";
/*  791 */       String str29 = "";
/*  792 */       Prop prop = Prop.getInstance();
/*  793 */       String str30 = WorkflowOvertimeSettingsUtil.getOverTimeSettingsEntity().getChangestatus() + "";
/*  794 */       RecordSet recordSet1 = new RecordSet();
/*  795 */       recordSet1.executeSql("select * from workflow_nodecustomrcmenu where wfid=" + paramString2 + " and nodeid=" + workflowNode.id);
/*  796 */       if (recordSet1.next()) {
/*  797 */         str3 = Util.null2String(recordSet1.getString("submitName7"));
/*  798 */         str4 = Util.null2String(recordSet1.getString("submitName8"));
/*  799 */         str5 = Util.null2String(recordSet1.getString("submitName9"));
/*  800 */         str6 = Util.null2String(recordSet1.getString("forwardName7"));
/*  801 */         str7 = Util.null2String(recordSet1.getString("forwardName8"));
/*  802 */         str8 = Util.null2String(recordSet1.getString("forwardName9"));
/*  803 */         str9 = Util.null2String(recordSet1.getString("saveName7"));
/*  804 */         str10 = Util.null2String(recordSet1.getString("saveName8"));
/*  805 */         str11 = Util.null2String(recordSet1.getString("saveName9"));
/*  806 */         str12 = Util.null2String(recordSet1.getString("rejectName7"));
/*  807 */         str13 = Util.null2String(recordSet1.getString("rejectName8"));
/*  808 */         str14 = Util.null2String(recordSet1.getString("rejectName9"));
/*  809 */         str15 = Util.null2String(recordSet1.getString("forsubName7"));
/*  810 */         str16 = Util.null2String(recordSet1.getString("forsubName8"));
/*  811 */         str17 = Util.null2String(recordSet1.getString("forsubName9"));
/*  812 */         str18 = Util.null2String(recordSet1.getString("ccsubName7"));
/*  813 */         str19 = Util.null2String(recordSet1.getString("ccsubName8"));
/*  814 */         str20 = Util.null2String(recordSet1.getString("ccsubName9"));
/*  815 */         str21 = Util.null2String(recordSet1.getString("haswfrm"));
/*  816 */         str22 = Util.null2String(recordSet1.getString("hassmsrm"));
/*  817 */         str23 = Util.null2String(recordSet1.getString("hasnoback"));
/*  818 */         str24 = Util.null2String(recordSet1.getString("hasback"));
/*  819 */         str25 = Util.null2String(recordSet1.getString("hasfornoback"));
/*  820 */         str26 = Util.null2String(recordSet1.getString("hasforback"));
/*  821 */         str27 = Util.null2String(recordSet1.getString("hasccnoback"));
/*  822 */         str28 = Util.null2String(recordSet1.getString("hasccback"));
/*  823 */         str29 = Util.null2String(recordSet1.getString("hasovertime"));
/*      */       } 
/*  825 */       if ((!"".equals(str6) || !"".equals(str7) || !"".equals(str8) || !"".equals(str9) || !"".equals(str10) || !"".equals(str11) || !"".equals(str12) || !"".equals(str13) || !"".equals(str14) || "1".equals(str21) || "1".equals(str22) || "1".equals(str23) || "1".equals(str24) || "1".equals(str25) || "1".equals(str26) || "1".equals(str27) || "1".equals(str28) || "1".equals(str29)) && !"".equals(str30)) {
/*  826 */         str2 = "1";
/*  827 */       } else if ((!"".equals(str6) || !"".equals(str7) || !"".equals(str8) || !"".equals(str9) || !"".equals(str10) || !"".equals(str11) || !"".equals(str12) || !"".equals(str13) || !"".equals(str14) || !"".equals(str3) || !"".equals(str4) || !"".equals(str5) || !"".equals(str15) || !"".equals(str16) || !"".equals(str17) || !"".equals(str18) || !"".equals(str19) || !"".equals(str20) || "1".equals(str21) || "1".equals(str22) || "1".equals(str29)) && "".equals(str30)) {
/*  828 */         str2 = "1";
/*      */       } 
/*  830 */       RecordSet recordSet2 = new RecordSet();
/*  831 */       String str31 = null;
/*  832 */       recordSet2.executeSql("select isTriDiffWorkflow from workflow_base where id=" + paramString2);
/*  833 */       if (recordSet2.next()) {
/*  834 */         str31 = Util.null2String(recordSet2.getString("isTriDiffWorkflow"));
/*      */       }
/*  836 */       if (!"1".equals(str31)) {
/*  837 */         str31 = "0";
/*      */       }
/*  839 */       String str32 = "Workflow_SubwfSet";
/*  840 */       if ("1".equals(str31)) {
/*  841 */         str32 = "Workflow_TriDiffWfDiffField";
/*      */       }
/*  843 */       String str33 = null;
/*  844 */       String str34 = null;
/*  845 */       recordSet2.executeSql("select triSubwfName7,triSubwfName8 from Workflow_TriSubwfButtonName where workflowId=" + paramString2 + " and nodeId=" + workflowNode.id + " and subwfSetTableName='" + str32 + "'");
/*  846 */       while (recordSet2.next()) {
/*  847 */         str33 = Util.null2String(recordSet2.getString("triSubwfName7"));
/*  848 */         str34 = Util.null2String(recordSet2.getString("triSubwfName8"));
/*  849 */         if (!str33.equals("") || !str34.equals("")) {
/*  850 */           str2 = "1";
/*      */           break;
/*      */         } 
/*      */       } 
/*  854 */       workflowNode.setHasCusRigKey("1".equals(str2));
/*      */ 
/*      */       
/*  857 */       String str35 = "select objid from workflow_addinoperate where workflowid = " + paramString2 + " and isnode=1 and ispreadd = '1'";
/*  858 */       String str36 = "";
/*  859 */       recordSet2.executeSql(str35);
/*  860 */       while (recordSet2.next()) {
/*  861 */         str36 = str36 + "," + recordSet2.getString("objid");
/*      */       }
/*      */       
/*  864 */       str35 = "select w_nodeid from int_BrowserbaseInfo where w_fid = " + paramString2 + " and ispreoperator=1 and w_enable=1";
/*      */       
/*  866 */       while (recordSet2.next()) {
/*  867 */         str36 = str36 + "," + recordSet2.getString("w_nodeid");
/*      */       }
/*      */ 
/*      */       
/*  871 */       if (str36.indexOf(workflowNode.id + "") != -1) {
/*  872 */         workflowNode.setHasNodeBefAddOpr(true);
/*      */       } else {
/*  874 */         workflowNode.setHasNodeBefAddOpr(false);
/*      */       } 
/*      */ 
/*      */       
/*  878 */       String str37 = "select objid from workflow_addinoperate where workflowid = " + paramString2 + " and isnode=1 and ispreadd = '0'";
/*  879 */       String str38 = "";
/*  880 */       recordSet2.executeSql(str37);
/*  881 */       while (recordSet2.next()) {
/*  882 */         str38 = str38 + "," + recordSet2.getString("objid");
/*      */       }
/*      */ 
/*      */       
/*  886 */       str37 = "select w_nodeid from int_BrowserbaseInfo where w_fid = " + paramString2 + " and ispreoperator=0 and w_enable=1";
/*      */       
/*  888 */       while (recordSet2.next()) {
/*  889 */         str38 = str38 + "," + recordSet2.getString("w_nodeid");
/*      */       }
/*      */       
/*  892 */       if (str38.indexOf(workflowNode.id + "") != -1) {
/*  893 */         workflowNode.setHasNodeAftAddOpr(true);
/*      */       } else {
/*  895 */         workflowNode.setHasNodeAftAddOpr(false);
/*      */       } 
/*      */ 
/*      */       
/*  899 */       String str39 = Util.null2String(recordSet.getString("viewnodeids"));
/*  900 */       if (!"".equals(str39) && str39 != null) {
/*  901 */         workflowNode.setHasLogViewSco(true);
/*      */       } else {
/*  903 */         workflowNode.setHasLogViewSco(false);
/*      */       } 
/*      */ 
/*      */       
/*  907 */       int i25 = recordSet.getInt("drawxpos");
/*  908 */       int i26 = recordSet.getInt("drawypos");
/*  909 */       if (i25 == -1 && i26 == -1) {
/*  910 */         i25 = j;
/*  911 */         i26 = k;
/*      */         
/*  913 */         if (bool) { j -= NODE_SPACE + 120; }
/*  914 */         else { j += NODE_SPACE + 120; }
/*  915 */          if (j > MAX_WIDTH) {
/*  916 */           bool = true;
/*  917 */           j -= NODE_SPACE + 120;
/*  918 */           k += NODE_SPACE + 80;
/*      */         } 
/*  920 */         if (j < NODE_LEFT_PANDING) {
/*  921 */           bool = false;
/*  922 */           j = NODE_LEFT_PANDING;
/*  923 */           k += NODE_SPACE + 80;
/*      */         } 
/*      */       } else {
/*      */         
/*  927 */         i25 -= 60;
/*  928 */         i26 -= 40;
/*      */       } 
/*  930 */       workflowNode.x = i25;
/*  931 */       workflowNode.y = i26;
/*  932 */       workflowNode.nodeType = recordSet.getString("nodetype");
/*  933 */       workflowNode.name = recordSet.getString("nodename");
/*  934 */       workflowNode.setPassnum(Util.getIntValue(recordSet.getString("passnum"), 0));
/*  935 */       workflowNode.setNodeAttribute(recordSet.getString("nodeattribute"));
/*  936 */       workflow.addNode(workflowNode);
/*      */     } 
/*      */ 
/*      */     
/*  940 */     bool = false;
/*  941 */     String str = "";
/*  942 */     recordSet.execute("select objid from workflow_addinoperate where workflowid = " + paramString2 + " and isnode=0");
/*  943 */     while (recordSet.next()) {
/*  944 */       str = str + "," + recordSet.getString("objid");
/*      */     }
/*  946 */     ConnStatement connStatement = new ConnStatement();
/*      */     try {
/*  948 */       connStatement.setStatementSql("select * from workflow_nodelink where wfrequestid is null and EXISTS(select 1 from workflow_nodebase b where workflow_nodelink.nodeid=b.id and (IsFreeNode is null or IsFreeNode !='1')) and EXISTS(select 1 from workflow_nodebase b where workflow_nodelink.destnodeid=b.id and (b.IsFreeNode is null or b.IsFreeNode !='1')) and workflowid = " + paramString2 + " order by nodeid");
/*  949 */       connStatement.executeQuery();
/*      */       
/*  951 */       while (connStatement.next()) {
/*  952 */         WorkflowLine workflowLine = new WorkflowLine();
/*  953 */         workflowLine.id = connStatement.getInt("id");
/*  954 */         workflowLine.fromNodeId = connStatement.getInt("nodeid");
/*  955 */         workflowLine.toNodeId = connStatement.getInt("destnodeid");
/*      */         
/*  957 */         if (connStatement.getDBType().equals("oracle") && Util.null2String(connStatement.getOrgindbtype()).equals("oracle")) {
/*  958 */           CLOB cLOB = connStatement.getClob("condition");
/*  959 */           String str3 = "";
/*  960 */           StringBuffer stringBuffer = new StringBuffer("");
/*  961 */           if (cLOB != null) {
/*  962 */             BufferedReader bufferedReader = new BufferedReader(cLOB.getCharacterStream());
/*  963 */             for (; (str3 = bufferedReader.readLine()) != null; stringBuffer = stringBuffer.append(str3));
/*  964 */             bufferedReader.close();
/*      */           } 
/*  966 */           String str4 = stringBuffer.toString();
/*  967 */           workflowLine.setCondition(str4);
/*      */         } else {
/*  969 */           workflowLine.setCondition(connStatement.getString("condition"));
/*      */         } 
/*      */         
/*  972 */         workflowLine.setNodePassHour(connStatement.getInt("nodepasshour"));
/*  973 */         workflowLine.setNodePassMinute(connStatement.getInt("nodepassminute"));
/*      */         
/*  975 */         if (str.indexOf(workflowLine.id + "") != -1) {
/*  976 */           workflowLine.setHasRole(true);
/*      */         }
/*      */         
/*  979 */         WorkflowNode workflowNode = workflow.findNode(workflowLine.fromNodeId);
/*  980 */         int m = connStatement.getInt("directionfrom");
/*  981 */         int n = connStatement.getInt("directionto");
/*  982 */         if (m == -1 && n == -1)
/*      */         {
/*  984 */           if (workflowNode.x + NODE_SPACE + 120 > MAX_WIDTH || (workflowNode.y > NODE_LEFT_PANDING + i && bool == true)) {
/*  985 */             m = 4;
/*  986 */             n = 12;
/*  987 */             if (bool) { bool = false; }
/*  988 */             else { bool = true; }
/*      */           
/*      */           } else {
/*  991 */             m = 0;
/*  992 */             n = 8;
/*      */           } 
/*      */         }
/*  995 */         workflowLine.fromPointId = m;
/*  996 */         workflowLine.toPointId = n;
/*  997 */         workflowLine.setLineName(connStatement.getString("linkname"));
/*  998 */         workflowLine.setRemindMsg(connStatement.getString("tipsinfo"));
/*  999 */         workflowLine.setIsBuildCodeString(connStatement.getString("isBulidCode"));
/* 1000 */         String str1 = Util.null2String(connStatement.getString("ismustpass"));
/* 1001 */         if (!str1.equals("")) workflowLine.setIsMustpass(connStatement.getString("ismustpass")); 
/* 1002 */         String str2 = Util.null2String(connStatement.getString("isreject"));
/* 1003 */         if (!str2.equals("")) workflowLine.setIsReject(connStatement.getString("isreject"));
/*      */         
/* 1005 */         for (byte b = 1; b <= 5; b++) {
/* 1006 */           int i1 = connStatement.getInt("x" + b);
/* 1007 */           int i2 = connStatement.getInt("y" + b);
/* 1008 */           if (i1 > 0 && i2 > 0) {
/* 1009 */             workflowLine.controlPoints.add(new Point(i1, i2));
/*      */           }
/*      */         } 
/*      */ 
/*      */         
/* 1014 */         Point point = workflowNode.getConnectionPoint(workflowLine.fromPointId);
/* 1015 */         workflowLine.controlPoints.add(0, point);
/* 1016 */         workflowNode = workflow.findNode(workflowLine.toNodeId);
/* 1017 */         point = workflowNode.getConnectionPoint(workflowLine.toPointId);
/* 1018 */         workflowLine.controlPoints.add(workflowLine.controlPoints.size(), point);
/*      */         
/* 1020 */         workflowLine.setStartDirection(Util.getIntValue(connStatement.getString("startDirection"), -1));
/* 1021 */         workflowLine.setEndDirection(Util.getIntValue(connStatement.getString("endDirection"), -1));
/* 1022 */         workflowLine.setNewPoints(Util.null2String(connStatement.getString("points")));
/*      */         
/* 1024 */         workflow.addLine(workflowLine);
/*      */       } 
/* 1026 */     } catch (Exception exception) {
/* 1027 */       exception.printStackTrace();
/*      */     } finally {
/* 1029 */       connStatement.close();
/*      */     } 
/*      */     
/* 1032 */     if (Util.null2String(paramString1).equals("edit")) {
/* 1033 */       this; wfCheckStatus("checkout", paramString2, this.user.getUID());
/*      */     } 
/*      */     
/* 1036 */     return workflow;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static boolean wfCheckStatus(String paramString1, String paramString2, int paramInt) {
/* 1045 */     RecordSet recordSet = new RecordSet();
/*      */     
/* 1047 */     boolean bool = false;
/* 1048 */     if (Util.null2String(paramString1).equals("checkout")) {
/* 1049 */       bool = recordSet.executeSql("update workflow_base set isEdit='1',editor='" + paramInt + "',editdate='" + TimeUtil.getCurrentDateString() + "',edittime='" + TimeUtil.getCurrentTimeString().substring(11) + "' where id=" + paramString2);
/*      */     
/*      */     }
/* 1052 */     else if (recordSet.getDBType().equals("mysql")) {
/* 1053 */       bool = recordSet.executeSql("update workflow_base set isEdit='0',editor=null where id=" + paramString2);
/*      */     } else {
/* 1055 */       bool = recordSet.executeSql("update workflow_base set isEdit='0',editor='' where id=" + paramString2);
/*      */     } 
/*      */     
/* 1058 */     return bool;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private void writeWorkflowToDB(Workflow paramWorkflow, HttpServletRequest paramHttpServletRequest) {
/* 1068 */     RecordSet recordSet = new RecordSet();
/*      */     
/* 1070 */     SysMaintenanceLog sysMaintenanceLog = new SysMaintenanceLog();
/*      */     
/* 1072 */     int i = paramWorkflow.id;
/*      */     byte b1;
/* 1074 */     for (b1 = 0; b1 < paramWorkflow.nodes.size(); b1++) {
/* 1075 */       WorkflowNode workflowNode = paramWorkflow.getNode(b1);
/* 1076 */       String str = "select nodeid from workflow_flownode where workflowid=" + i + " and nodeid=" + workflowNode.id;
/* 1077 */       recordSet.execute(str);
/* 1078 */       if (recordSet.next()) {
/*      */ 
/*      */         
/* 1081 */         String str1 = "";
/*      */         
/* 1083 */         Iterator<Map.Entry> iterator = workflowNode.attrMap.entrySet().iterator();
/* 1084 */         while (iterator.hasNext()) {
/* 1085 */           Map.Entry entry = iterator.next();
/* 1086 */           if (entry.getKey().equals("nodename")) {
/* 1087 */             recordSet.executeSql("select nodename from workflow_nodebase where id =" + workflowNode.id);
/* 1088 */             if (recordSet.next()) {
/* 1089 */               String str2 = recordSet.getString("nodename");
/* 1090 */               if (str2 != null && (
/* 1091 */                 entry.getValue().toString().equals(str2) || str2.indexOf("~`~`7 " + entry.getValue().toString()) != -1)) {
/*      */                 continue;
/*      */               }
/*      */             } 
/*      */           } 
/*      */ 
/*      */           
/* 1098 */           str1 = str1 + "," + entry.getKey() + "='" + entry.getValue() + "' ";
/*      */         } 
/* 1100 */         str = "update workflow_nodebase ";
/* 1101 */         str = str + "set drawxpos = " + (workflowNode.x + 60) + ", drawypos = " + (workflowNode.y + 40) + " ";
/* 1102 */         str = str + str1;
/* 1103 */         str = str + "where id = " + workflowNode.id;
/* 1104 */         recordSet.executeSql(str);
/*      */         
/* 1106 */         WorkflowNodeComInfo workflowNodeComInfo = new WorkflowNodeComInfo();
/* 1107 */         workflowNodeComInfo.updateNodeCache(workflowNode.id + "");
/*      */         
/* 1109 */         str = "update workflow_flownode set nodetype = " + workflowNode.nodeType + ", nodeorder=" + workflowNode.getNodeorder() + " where nodeid = " + workflowNode.id;
/*      */         try {
/* 1111 */           recordSet.executeSql(str);
/* 1112 */           sysMaintenanceLog.resetParameter();
/* 1113 */           sysMaintenanceLog.setOperateItem("86");
/* 1114 */           sysMaintenanceLog.setRelatedId(i);
/* 1115 */           sysMaintenanceLog.setRelatedName(workflowNode.getName());
/* 1116 */           sysMaintenanceLog.setOperateUserid(this.user.getUID());
/* 1117 */           sysMaintenanceLog.setClientAddress(paramHttpServletRequest.getRemoteAddr());
/* 1118 */           sysMaintenanceLog.setOperateType("2");
/* 1119 */           sysMaintenanceLog.setOperateDesc("WrokFlowNode_update");
/* 1120 */           sysMaintenanceLog.setSysLogInfo();
/*      */         }
/* 1122 */         catch (Exception exception) {
/* 1123 */           this.status = 1;
/* 1124 */           this.errormsg = SystemEnv.getHtmlLabelName(16332, this.user.getLanguage()) + ":" + SystemEnv.getHtmlLabelName(15599, this.user.getLanguage());
/* 1125 */           exception.printStackTrace();
/*      */ 
/*      */           
/*      */           return;
/*      */         } 
/*      */       } 
/*      */     } 
/*      */     
/* 1133 */     for (b1 = 0; b1 < paramWorkflow.lines.size(); b1++) {
/* 1134 */       WorkflowLine workflowLine = paramWorkflow.getLine(b1);
/* 1135 */       String str2 = "";
/*      */       
/* 1137 */       Iterator<Map.Entry> iterator = workflowLine.attrMap.entrySet().iterator();
/* 1138 */       while (iterator.hasNext()) {
/* 1139 */         Map.Entry entry = iterator.next();
/* 1140 */         if (entry.getKey().equals("linkname")) {
/* 1141 */           recordSet.executeSql("select linkname from workflow_nodelink where id =" + workflowLine.id + " and workflowid=" + i);
/* 1142 */           if (recordSet.next()) {
/* 1143 */             String str = recordSet.getString("linkname");
/* 1144 */             if (str != null && (
/* 1145 */               entry.getValue().toString().equals(str) || str.indexOf("~`~`7 " + entry.getValue().toString()) != -1)) {
/*      */               continue;
/*      */             }
/*      */           } 
/*      */         } 
/*      */ 
/*      */         
/* 1152 */         str2 = str2 + "," + entry.getKey() + "='" + entry.getValue() + "' ";
/*      */       } 
/* 1154 */       String str1 = "update workflow_nodelink ";
/* 1155 */       str1 = str1 + "set ";
/* 1156 */       for (byte b = 0; b < WorkflowLine.getMaxCtrlPointCount(); b++) {
/* 1157 */         if (b < workflowLine.getValidCtrlPointCount()) {
/* 1158 */           Point point = workflowLine.getCtrlPoint(b);
/* 1159 */           str1 = str1 + "x" + (b + 1) + " = " + point.x + ", y" + (b + 1) + " = " + point.y;
/*      */           
/* 1161 */           if (b < WorkflowLine.getMaxCtrlPointCount() - 1) str1 = str1 + ", "; 
/*      */         } else {
/* 1163 */           str1 = str1 + "x" + (b + 1) + " = -1, y" + (b + 1) + " = -1";
/* 1164 */           if (b < WorkflowLine.getMaxCtrlPointCount() - 1) str1 = str1 + ", "; 
/*      */         } 
/*      */       } 
/* 1167 */       str1 = str1 + str2;
/* 1168 */       str1 = str1 + " where id = " + workflowLine.id + " and workflowid=" + i;
/*      */ 
/*      */       
/*      */       try {
/* 1172 */         recordSet.executeSql(str1);
/* 1173 */         sysMaintenanceLog.resetParameter();
/* 1174 */         sysMaintenanceLog.setOperateItem("88");
/* 1175 */         sysMaintenanceLog.setRelatedId(i);
/* 1176 */         sysMaintenanceLog.setRelatedName(workflowLine.getLineName());
/* 1177 */         sysMaintenanceLog.setOperateUserid(this.user.getUID());
/* 1178 */         sysMaintenanceLog.setClientAddress(paramHttpServletRequest.getRemoteAddr());
/* 1179 */         sysMaintenanceLog.setOperateType("2");
/* 1180 */         sysMaintenanceLog.setOperateDesc("WrokFlowNodePortal_update");
/* 1181 */         sysMaintenanceLog.setSysLogInfo();
/*      */       }
/* 1183 */       catch (Exception exception) {
/* 1184 */         this.status = 1;
/* 1185 */         this.errormsg = SystemEnv.getHtmlLabelName(16332, this.user.getLanguage()) + ":" + SystemEnv.getHtmlLabelName(15599, this.user.getLanguage());
/* 1186 */         exception.printStackTrace();
/*      */         
/*      */         return;
/*      */       } 
/*      */     } 
/*      */     
/* 1192 */     if (paramWorkflow.nodes.size() > 0 || paramWorkflow.lines.size() > 0) {
/* 1193 */       WorkflowInitialization workflowInitialization = new WorkflowInitialization();
/* 1194 */       workflowInitialization.recordInformation(i);
/*      */     } 
/*      */ 
/*      */ 
/*      */     
/* 1199 */     for (byte b2 = 0; b2 < paramWorkflow.getGroups().size(); b2++) {
/* 1200 */       WorkflowGroup workflowGroup = paramWorkflow.getGroups().get(b2);
/*      */       
/* 1202 */       String str = "";
/* 1203 */       if (workflowGroup.isNew()) {
/* 1204 */         str = "insert into workflow_groupinfo(workflowid, groupname, direction, x, y, width, height) values (";
/* 1205 */         str = str + workflowGroup.getWorkflowid() + ",";
/* 1206 */         str = str + "'" + workflowGroup.getGroupname() + "' ,";
/* 1207 */         str = str + workflowGroup.getDirection() + ",";
/* 1208 */         str = str + workflowGroup.getX() + ",";
/* 1209 */         str = str + workflowGroup.getY() + ",";
/* 1210 */         str = str + workflowGroup.getWidth() + ",";
/* 1211 */         str = str + workflowGroup.getHeight();
/* 1212 */         str = str + ")";
/*      */       } else {
/* 1214 */         str = "update workflow_groupinfo set ";
/* 1215 */         str = str + "groupname='" + workflowGroup.getGroupname() + "', ";
/* 1216 */         str = str + "x=" + workflowGroup.getX() + ", ";
/* 1217 */         str = str + "y=" + workflowGroup.getY() + ", ";
/* 1218 */         str = str + "width=" + workflowGroup.getWidth() + ", ";
/* 1219 */         str = str + "height=" + workflowGroup.getHeight();
/* 1220 */         str = str + " where id=" + workflowGroup.getId();
/*      */       } 
/*      */       try {
/* 1223 */         recordSet.execute(str);
/* 1224 */       } catch (Exception exception) {
/* 1225 */         exception.printStackTrace();
/*      */       } 
/*      */     } 
/*      */ 
/*      */     
/* 1230 */     if (!"".equals(this.delGroupIds)) {
/* 1231 */       if (this.delGroupIds.indexOf(",") == 0) {
/* 1232 */         this.delGroupIds = this.delGroupIds.substring(1);
/*      */       }
/* 1234 */       String str = "delete from workflow_groupinfo where id in (" + this.delGroupIds + ")";
/*      */       try {
/* 1236 */         recordSet.execute(str);
/* 1237 */       } catch (Exception exception) {
/* 1238 */         exception.printStackTrace();
/*      */       } 
/*      */     } 
/*      */ 
/*      */     
/*      */     try {
/* 1244 */       if (!this.delNodeIds.equals("")) {
/* 1245 */         if (this.delNodeIds.indexOf(",") == 0) {
/* 1246 */           this.delNodeIds = this.delNodeIds.substring(1);
/*      */         }
/* 1248 */         String[] arrayOfString = Util.TokenizerString2(this.delNodeIds, ",");
/* 1249 */         WFNodeMainManager wFNodeMainManager = new WFNodeMainManager();
/* 1250 */         wFNodeMainManager.resetParameter();
/* 1251 */         wFNodeMainManager.deleteWfNode(arrayOfString, this.user.getUID());
/* 1252 */         sysMaintenanceLog.resetParameter();
/* 1253 */         sysMaintenanceLog.setRelatedId(i);
/* 1254 */         sysMaintenanceLog.setRelatedName(SystemEnv.getHtmlLabelName(15070, this.user.getLanguage()));
/* 1255 */         sysMaintenanceLog.setOperateType("3");
/* 1256 */         sysMaintenanceLog.setOperateDesc("WrokFlowNode_delete");
/* 1257 */         sysMaintenanceLog.setOperateItem("86");
/* 1258 */         sysMaintenanceLog.setOperateUserid(this.user.getUID());
/* 1259 */         sysMaintenanceLog.setClientAddress(paramHttpServletRequest.getRemoteAddr());
/* 1260 */         sysMaintenanceLog.setSysLogInfo();
/*      */       }
/*      */     
/* 1263 */     } catch (Exception exception) {
/* 1264 */       this.status = 1;
/* 1265 */       this.errormsg = SystemEnv.getHtmlLabelName(16332, this.user.getLanguage()) + ":" + SystemEnv.getHtmlLabelName(15599, this.user.getLanguage());
/* 1266 */       exception.printStackTrace();
/*      */       
/*      */       return;
/*      */     } 
/*      */     
/*      */     try {
/* 1272 */       if (!this.delStepIds.equals("")) {
/* 1273 */         if (this.delStepIds.indexOf(",") == 0) {
/* 1274 */           this.delStepIds = this.delStepIds.substring(1);
/*      */         }
/*      */         
/* 1277 */         String[] arrayOfString = Util.TokenizerString2(this.delStepIds, ",");
/* 1278 */         for (b1 = 0; b1 < arrayOfString.length; b1++) {
/* 1279 */           WFNodePortalMainManager wFNodePortalMainManager = new WFNodePortalMainManager();
/* 1280 */           wFNodePortalMainManager.resetParameter();
/* 1281 */           wFNodePortalMainManager.setId(Util.getIntValue(arrayOfString[b1], 0));
/* 1282 */           wFNodePortalMainManager.deleteWfNodePortal();
/* 1283 */           sysMaintenanceLog.resetParameter();
/* 1284 */           sysMaintenanceLog.setRelatedId(i);
/* 1285 */           sysMaintenanceLog.setRelatedName(SystemEnv.getHtmlLabelName(15611, this.user.getLanguage()));
/* 1286 */           sysMaintenanceLog.setOperateType("3");
/* 1287 */           sysMaintenanceLog.setOperateDesc("WrokFlowNodePortal_delete");
/* 1288 */           sysMaintenanceLog.setOperateItem("88");
/* 1289 */           sysMaintenanceLog.setOperateUserid(this.user.getUID());
/* 1290 */           sysMaintenanceLog.setClientAddress(paramHttpServletRequest.getRemoteAddr());
/* 1291 */           sysMaintenanceLog.setSysLogInfo();
/*      */         }
/*      */       
/*      */       }
/*      */     
/* 1296 */     } catch (Exception exception) {
/* 1297 */       this.status = 1;
/* 1298 */       this.errormsg = SystemEnv.getHtmlLabelName(16332, this.user.getLanguage()) + ":" + SystemEnv.getHtmlLabelName(15608, this.user.getLanguage());
/* 1299 */       exception.printStackTrace();
/*      */       return;
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public Workflow readWorkflowFromDBH5(String paramString1, String paramString2, int paramInt) {
/* 1314 */     Workflow workflow = new Workflow();
/* 1315 */     RecordSet recordSet = new RecordSet();
/*      */     
/* 1317 */     recordSet.executeSql("select max(drawypos) as drawypos from workflow_nodebase where id in (select nodeid from workflow_flownode where workflowid = " + paramString2 + ")");
/* 1318 */     int i = 0;
/* 1319 */     if (recordSet.next()) i = recordSet.getInt("drawypos"); 
/* 1320 */     if (i == -1) i = 0; 
/* 1321 */     if (i > 0) {
/* 1322 */       i += NODE_SPACE;
/*      */     }
/* 1324 */     int j = NODE_LEFT_PANDING;
/* 1325 */     int k = NODE_LEFT_PANDING + i;
/* 1326 */     boolean bool = false;
/*      */     
/* 1328 */     recordSet.execute("select * from workflow_base where id=" + paramString2);
/* 1329 */     while (recordSet.next()) {
/* 1330 */       workflow.setFormID(recordSet.getString("formid"));
/* 1331 */       workflow.setIsBill(recordSet.getInt("isbill"));
/* 1332 */       workflow.setIsCust(recordSet.getInt("iscust"));
/* 1333 */       workflow.id = recordSet.getInt("id");
/* 1334 */       workflow.setWorkflowName(recordSet.getString("workflowname"));
/*      */     } 
/*      */ 
/*      */     
/* 1338 */     ArrayList<WorkflowGroup> arrayList = new ArrayList();
/*      */     
/* 1340 */     recordSet.execute("select * from workflow_groupinfo where workflowid=" + paramString2);
/* 1341 */     while (recordSet.next()) {
/* 1342 */       WorkflowGroup workflowGroup = new WorkflowGroup();
/* 1343 */       workflowGroup.setId(recordSet.getInt("id"));
/* 1344 */       workflowGroup.setWorkflowid(recordSet.getInt("workflowid"));
/* 1345 */       workflowGroup.setGroupname(recordSet.getString("groupname"));
/* 1346 */       workflowGroup.setDirection(recordSet.getInt("direction"));
/* 1347 */       workflowGroup.setX(recordSet.getDouble("x"));
/* 1348 */       workflowGroup.setY(recordSet.getDouble("y"));
/* 1349 */       workflowGroup.setWidth(recordSet.getDouble("width"));
/* 1350 */       workflowGroup.setHeight(recordSet.getDouble("height"));
/* 1351 */       arrayList.add(workflowGroup);
/*      */     } 
/* 1353 */     workflow.setGroups(arrayList);
/*      */ 
/*      */     
/* 1356 */     recordSet.executeSql("select t1.*,t2.* from workflow_nodebase t1, workflow_flownode t2 where (t1.IsFreeNode is null or t1.IsFreeNode!='1') and t2.workflowid = " + paramString2 + " and t1.id = t2.nodeid order by t1.id");
/* 1357 */     while (recordSet.next()) {
/* 1358 */       WorkflowNode workflowNode = new WorkflowNode();
/* 1359 */       workflowNode.id = recordSet.getInt("id");
/*      */       
/* 1361 */       int m = recordSet.getInt("drawxpos");
/* 1362 */       int n = recordSet.getInt("drawypos");
/* 1363 */       if (m == -1 && n == -1) {
/* 1364 */         m = j;
/* 1365 */         n = k;
/*      */         
/* 1367 */         if (bool) { j -= NODE_SPACE + 120; }
/* 1368 */         else { j += NODE_SPACE + 120; }
/* 1369 */          if (j > MAX_WIDTH) {
/* 1370 */           bool = true;
/* 1371 */           j -= NODE_SPACE + 120;
/* 1372 */           k += NODE_SPACE + 80;
/*      */         } 
/* 1374 */         if (j < NODE_LEFT_PANDING) {
/* 1375 */           bool = false;
/* 1376 */           j = NODE_LEFT_PANDING;
/* 1377 */           k += NODE_SPACE + 80;
/*      */         } 
/*      */       } else {
/*      */         
/* 1381 */         m -= 60;
/* 1382 */         n -= 40;
/*      */       } 
/* 1384 */       workflowNode.x = m;
/* 1385 */       workflowNode.y = n;
/* 1386 */       workflowNode.nodeType = recordSet.getString("nodetype");
/* 1387 */       workflowNode.name = recordSet.getString("nodename");
/* 1388 */       workflowNode.setPassnum(Util.getIntValue(recordSet.getString("passnum"), 0));
/* 1389 */       workflowNode.setNodeAttribute(recordSet.getString("nodeattribute"));
/* 1390 */       workflow.addNode(workflowNode);
/*      */     } 
/*      */     
/* 1393 */     ConnStatement connStatement = new ConnStatement();
/*      */     try {
/* 1395 */       connStatement.setStatementSql("select * from workflow_nodelink where wfrequestid is null and EXISTS(select 1 from workflow_nodebase b where workflow_nodelink.nodeid=b.id and (IsFreeNode is null or IsFreeNode !='1')) and EXISTS(select 1 from workflow_nodebase b where workflow_nodelink.destnodeid=b.id and (b.IsFreeNode is null or b.IsFreeNode !='1')) and workflowid = " + paramString2 + " order by nodeid");
/* 1396 */       connStatement.executeQuery();
/*      */       
/* 1398 */       while (connStatement.next()) {
/* 1399 */         WorkflowLine workflowLine = new WorkflowLine();
/* 1400 */         workflowLine.id = connStatement.getInt("id");
/* 1401 */         workflowLine.fromNodeId = connStatement.getInt("nodeid");
/* 1402 */         workflowLine.toNodeId = connStatement.getInt("destnodeid");
/*      */         
/* 1404 */         if (connStatement.getDBType().equals("oracle") && !Util.null2String(connStatement.getOrgindbtype()).equals("oracle")) {
/* 1405 */           CLOB cLOB = connStatement.getClob("condition");
/* 1406 */           String str3 = "";
/* 1407 */           StringBuffer stringBuffer = new StringBuffer("");
/* 1408 */           if (cLOB != null) {
/* 1409 */             BufferedReader bufferedReader = new BufferedReader(cLOB.getCharacterStream());
/* 1410 */             for (; (str3 = bufferedReader.readLine()) != null; stringBuffer = stringBuffer.append(str3));
/* 1411 */             bufferedReader.close();
/*      */           } 
/* 1413 */           String str4 = stringBuffer.toString();
/* 1414 */           workflowLine.setCondition(str4);
/*      */         } else {
/* 1416 */           workflowLine.setCondition(connStatement.getString("condition"));
/*      */         } 
/*      */         
/* 1419 */         workflowLine.setNodePassHour(connStatement.getInt("nodepasshour"));
/* 1420 */         workflowLine.setNodePassMinute(connStatement.getInt("nodepassminute"));
/*      */ 
/*      */         
/* 1423 */         WorkflowNode workflowNode = workflow.findNode(workflowLine.fromNodeId);
/* 1424 */         int m = connStatement.getInt("directionfrom");
/* 1425 */         int n = connStatement.getInt("directionto");
/* 1426 */         if (m == -1 && n == -1)
/*      */         {
/* 1428 */           if (workflowNode.x + NODE_SPACE + 120 > MAX_WIDTH || (workflowNode.y > NODE_LEFT_PANDING + i && bool == true)) {
/* 1429 */             m = 4;
/* 1430 */             n = 12;
/* 1431 */             if (bool) { bool = false; }
/* 1432 */             else { bool = true; }
/*      */           
/*      */           } else {
/* 1435 */             m = 0;
/* 1436 */             n = 8;
/*      */           } 
/*      */         }
/* 1439 */         workflowLine.fromPointId = m;
/* 1440 */         workflowLine.toPointId = n;
/* 1441 */         workflowLine.setLineName(connStatement.getString("linkname"));
/* 1442 */         workflowLine.setRemindMsg(connStatement.getString("tipsinfo"));
/* 1443 */         workflowLine.setIsBuildCodeString(connStatement.getString("isBulidCode"));
/* 1444 */         String str1 = Util.null2String(connStatement.getString("ismustpass"));
/* 1445 */         if (!str1.equals("")) workflowLine.setIsMustpass(connStatement.getString("ismustpass")); 
/* 1446 */         String str2 = Util.null2String(connStatement.getString("isreject"));
/* 1447 */         if (!str2.equals("")) workflowLine.setIsReject(connStatement.getString("isreject"));
/*      */         
/* 1449 */         for (byte b = 1; b <= 5; b++) {
/* 1450 */           int i1 = connStatement.getInt("x" + b);
/* 1451 */           int i2 = connStatement.getInt("y" + b);
/* 1452 */           if (i1 > 0 && i2 > 0) {
/* 1453 */             workflowLine.controlPoints.add(new Point(i1, i2));
/*      */           }
/*      */         } 
/*      */ 
/*      */         
/* 1458 */         Point point = workflowNode.getConnectionPoint(workflowLine.fromPointId);
/* 1459 */         workflowLine.controlPoints.add(0, point);
/* 1460 */         workflowNode = workflow.findNode(workflowLine.toNodeId);
/* 1461 */         point = workflowNode.getConnectionPoint(workflowLine.toPointId);
/* 1462 */         workflowLine.controlPoints.add(workflowLine.controlPoints.size(), point);
/*      */         
/* 1464 */         workflowLine.setStartDirection(Util.getIntValue(connStatement.getString("startDirection"), -1));
/* 1465 */         workflowLine.setEndDirection(Util.getIntValue(connStatement.getString("endDirection"), -1));
/* 1466 */         workflowLine.setNewPoints(Util.null2String(connStatement.getString("points")));
/*      */         
/* 1468 */         workflow.addLine(workflowLine);
/*      */       } 
/* 1470 */     } catch (Exception exception) {
/* 1471 */       exception.printStackTrace();
/*      */     } finally {
/* 1473 */       connStatement.close();
/*      */     } 
/* 1475 */     return workflow;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setWorkflowId(String paramString) {
/* 1483 */     this.workflowId = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setUser(User paramUser) {
/* 1491 */     this.user = paramUser;
/*      */   }
/*      */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/layout/WorkflowXmlParser.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */