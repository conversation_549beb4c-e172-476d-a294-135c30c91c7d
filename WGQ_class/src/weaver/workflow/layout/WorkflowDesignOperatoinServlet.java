/*     */ package weaver.workflow.layout;
/*     */ 
/*     */ import java.io.IOException;
/*     */ import java.io.PrintWriter;
/*     */ import java.io.UnsupportedEncodingException;
/*     */ import java.net.URLDecoder;
/*     */ import java.util.ArrayList;
/*     */ import javax.servlet.ServletException;
/*     */ import javax.servlet.http.HttpServlet;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.HrmUserVarify;
/*     */ import weaver.hrm.User;
/*     */ import weaver.systeminfo.SysMaintenanceLog;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.workflow.workflow.WFNodeMainManager;
/*     */ import weaver.workflow.workflow.WFNodePortalMainManager;
/*     */ 
/*     */ 
/*     */ public class WorkflowDesignOperatoinServlet
/*     */   extends HttpServlet
/*     */ {
/*     */   private User user;
/*  26 */   private int status = 0;
/*  27 */   private String errormsg = "";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void doPost(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws ServletException, IOException {
/*  34 */     this.user = HrmUserVarify.checkUser(paramHttpServletRequest, paramHttpServletResponse);
/*     */     
/*  36 */     if (this.user != null) {
/*     */       
/*  38 */       PrintWriter printWriter = paramHttpServletResponse.getWriter();
/*  39 */       String str1 = "";
/*     */       
/*  41 */       String str2 = Util.null2String(paramHttpServletRequest.getParameter("method"));
/*  42 */       if (str2.equals("wfnodeadd")) {
/*     */ 
/*     */         
/*  45 */         String str = addWorkflowNode(paramHttpServletRequest, paramHttpServletResponse);
/*  46 */         if (!str.equals("")) {
/*  47 */           this.status = 0;
/*     */         } else {
/*  49 */           this.status = 1;
/*     */         } 
/*  51 */         str1 = "var result= {status :\"" + this.status + "\",id :\"" + str + "\",errormsg :\"" + this.errormsg + "\"};";
/*     */       
/*     */       }
/*  54 */       else if (str2.equals("wfnodeportal")) {
/*  55 */         String str = addWorkflowLink(paramHttpServletRequest, paramHttpServletResponse);
/*  56 */         if (!str.equals("")) {
/*  57 */           this.status = 0;
/*     */         } else {
/*  59 */           this.status = 1;
/*     */         } 
/*  61 */         str1 = "var result= {status :\"" + this.status + "\",id :\"" + str + "\",errormsg :\"" + this.errormsg + "\"};";
/*     */       
/*     */       }
/*  64 */       else if (str2.equals("checkin")) {
/*  65 */         boolean bool = WorkflowXmlParser.wfCheckStatus("checkin", "" + Util.getIntValue(paramHttpServletRequest.getParameter("wfId"), -1), 0);
/*  66 */         if (bool) { this.status = 0; }
/*  67 */         else { this.status = 1; }
/*  68 */          str1 = "var result = {status:\"" + this.status + "\",errormsg:\"\"}";
/*     */       } 
/*     */ 
/*     */       
/*  72 */       paramHttpServletResponse.setContentType("text/html;charset=UTF-8");
/*  73 */       printWriter.print(str1);
/*  74 */       printWriter.close();
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String addWorkflowNode(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/*  87 */     String str1 = "";
/*  88 */     int i = Util.getIntValue(Util.null2String(paramHttpServletRequest
/*  89 */           .getParameter("wfid")), 0);
/*  90 */     int j = Util.getIntValue(Util.null2String(paramHttpServletRequest
/*  91 */           .getParameter("formid")), 0);
/*     */     
/*  93 */     String str2 = Util.null2String(paramHttpServletRequest.getParameter("delids"));
/*  94 */     int k = Util.getIntValue(Util.null2String(paramHttpServletRequest
/*  95 */           .getParameter("nodesnum")));
/*     */     
/*  97 */     int m = 0;
/*     */     
/*  99 */     WFNodeMainManager wFNodeMainManager = new WFNodeMainManager();
/* 100 */     RecordSet recordSet1 = new RecordSet();
/* 101 */     RecordSet recordSet2 = new RecordSet();
/* 102 */     SysMaintenanceLog sysMaintenanceLog = new SysMaintenanceLog();
/*     */     
/* 104 */     String str3 = "";
/* 105 */     ArrayList<String> arrayList = new ArrayList();
/* 106 */     str3 = "select nodeid from workflow_flownode where nodetype='0' and workflowid=" + i;
/* 107 */     recordSet1.execute(str3);
/* 108 */     while (recordSet1.next()) {
/* 109 */       int i1 = Util.getIntValue(recordSet1.getString(1));
/* 110 */       arrayList.add("" + i1);
/* 111 */       m++;
/*     */     } 
/* 113 */     if (!str2.equals("")) {
/* 114 */       str2 = str2.substring(1);
/* 115 */       String[] arrayOfString = Util.TokenizerString2(str2, ",");
/* 116 */       for (byte b1 = 0; b1 < arrayOfString.length; b1++) {
/* 117 */         int i1 = Util.getIntValue(arrayOfString[b1]);
/* 118 */         str3 = " select count(nodeid) from workflow_flownode where nodeid=" + i1 + " and nodetype='0'";
/* 119 */         recordSet1.executeSql(str3);
/* 120 */         if (recordSet1.next())
/* 121 */           m -= recordSet1.getInt(1); 
/*     */       } 
/*     */     } 
/*     */     int n;
/* 125 */     for (n = 0; n < k; n++) {
/* 126 */       String str = Util.null2String(paramHttpServletRequest.getParameter("node_" + n + "_type"));
/* 127 */       int i1 = Util.getIntValue(Util.null2String(paramHttpServletRequest
/* 128 */             .getParameter("node_" + n + "_id")), 0);
/* 129 */       if (str.equals("0") && !arrayList.contains("" + i1)) {
/* 130 */         m++;
/*     */       }
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*     */     try {
/* 140 */       if (!str2.equals("")) {
/* 141 */         String[] arrayOfString = Util.TokenizerString2(str2, ",");
/* 142 */         wFNodeMainManager.resetParameter();
/* 143 */         wFNodeMainManager.deleteWfNode(arrayOfString);
/*     */ 
/*     */ 
/*     */         
/* 147 */         for (byte b1 = 0; b1 < arrayOfString.length; b1++) {
/* 148 */           sysMaintenanceLog.resetParameter();
/*     */           
/* 150 */           sysMaintenanceLog.setRelatedId(i);
/* 151 */           sysMaintenanceLog.setRelatedName(
/* 152 */               SystemEnv.getHtmlLabelName(15070, this.user.getLanguage()));
/* 153 */           sysMaintenanceLog.setOperateType("3");
/* 154 */           sysMaintenanceLog.setOperateDesc("WrokFlowNode_delete");
/* 155 */           sysMaintenanceLog.setOperateItem("86");
/* 156 */           sysMaintenanceLog.setOperateUserid(this.user.getUID());
/* 157 */           sysMaintenanceLog.setClientAddress(paramHttpServletRequest.getRemoteAddr());
/* 158 */           sysMaintenanceLog.setSysLogInfo();
/*     */         }
/*     */       
/*     */       } 
/* 162 */     } catch (Exception exception) {
/*     */       
/* 164 */       this.status = 1;
/* 165 */       exception.printStackTrace();
/*     */     } 
/*     */ 
/*     */     
/* 169 */     n = Util.getIntValue(Util.null2String(paramHttpServletRequest
/* 170 */           .getParameter("isbill")), 0);
/* 171 */     String str4 = "0";
/* 172 */     recordSet1.executeSql("select id from workflow_formmode where formid=" + j + " and isbill='" + n + "'");
/*     */     
/* 174 */     if (recordSet1.next()) {
/* 175 */       str4 = "1";
/*     */     }
/*     */ 
/*     */     
/* 179 */     for (byte b = 0; b < k; b++) {
/* 180 */       wFNodeMainManager.resetParameter();
/* 181 */       wFNodeMainManager.setWfid(i);
/* 182 */       wFNodeMainManager.setFormid(j);
/* 183 */       String str5 = "";
/*     */       try {
/* 185 */         str5 = URLDecoder.decode(Util.null2String(paramHttpServletRequest.getParameter("node_" + b + "_name")), "UTF-8");
/*     */       }
/* 187 */       catch (UnsupportedEncodingException unsupportedEncodingException) {
/*     */         
/* 189 */         unsupportedEncodingException.printStackTrace();
/*     */       } 
/* 191 */       int i1 = Util.getIntValue(Util.null2String(paramHttpServletRequest
/* 192 */             .getParameter("node_" + b + "_id")), 0);
/* 193 */       String str6 = Util.null2String(paramHttpServletRequest.getParameter("node_" + b + "_type"));
/*     */       
/* 195 */       String str7 = Util.null2String(paramHttpServletRequest
/* 196 */           .getParameter("node_" + b + "_attribute"));
/* 197 */       int i2 = Util.getIntValue(paramHttpServletRequest.getParameter("node_" + b + "_passnum"), 0);
/*     */ 
/*     */ 
/*     */       
/* 201 */       m--;
/* 202 */       if ("0".equals(str6) && m > 0) {
/* 203 */         str6 = "1";
/*     */       }
/*     */ 
/*     */       
/* 207 */       wFNodeMainManager.setNodename(str5);
/* 208 */       wFNodeMainManager.setNodetype(str6);
/* 209 */       wFNodeMainManager.setNodeattribute(str7);
/* 210 */       wFNodeMainManager.setNodepassnum(i2);
/* 211 */       wFNodeMainManager.setIsbill(n);
/* 212 */       wFNodeMainManager.setDrawxpos(Util.getIntValue(paramHttpServletRequest.getParameter("node_" + b + "_drawxpos")) + 60);
/* 213 */       wFNodeMainManager.setDrawypos(Util.getIntValue(paramHttpServletRequest.getParameter("node_" + b + "_drawypos")) + 40);
/*     */       
/* 215 */       sysMaintenanceLog.resetParameter();
/* 216 */       sysMaintenanceLog.setRelatedId(i);
/* 217 */       sysMaintenanceLog.setRelatedName(str5);
/* 218 */       sysMaintenanceLog.setOperateItem("86");
/* 219 */       sysMaintenanceLog.setOperateUserid(this.user.getUID());
/* 220 */       sysMaintenanceLog.setClientAddress(paramHttpServletRequest.getRemoteAddr());
/*     */       
/*     */       try {
/* 223 */         if (!str5.equals("") && !str6.equals("") && i1 != 0) {
/*     */           
/* 225 */           sysMaintenanceLog.setOperateType("2");
/* 226 */           sysMaintenanceLog.setOperateDesc("WrokFlowNode_update");
/* 227 */           sysMaintenanceLog.setSysLogInfo();
/* 228 */           wFNodeMainManager.setNodeid(i1);
/* 229 */           wFNodeMainManager.updateWfNode();
/*     */         } 
/*     */         
/* 232 */         if (!str5.equals("") && !str6.equals("") && i1 == 0) {
/*     */           
/* 234 */           sysMaintenanceLog.setOperateType("1");
/* 235 */           sysMaintenanceLog.setOperateDesc("WrokFlowNode_insert");
/* 236 */           sysMaintenanceLog.setSysLogInfo();
/* 237 */           wFNodeMainManager.saveWfNode();
/* 238 */           i1 = wFNodeMainManager.getNodeid2();
/*     */         } 
/*     */         
/* 241 */         if (str4.equals("1")) {
/* 242 */           recordSet1.executeSql("select  distinct * from workflow_modeview where formid=" + j + " and nodeid=0 and not exists(select * from workflow_modeview where formid=" + j + " and nodeid=" + i1 + " and fieldId=workflow_modeview.fieldId)");
/* 243 */           while (recordSet1.next()) {
/* 244 */             int i3 = recordSet1.getInt("isbill");
/* 245 */             int i4 = recordSet1.getInt("fieldid");
/* 246 */             String str8 = recordSet1.getString("isview");
/* 247 */             String str9 = recordSet1.getString("isedit");
/* 248 */             String str10 = recordSet1.getString("ismandatory");
/*     */             
/* 250 */             recordSet2.executeSql("insert into workflow_modeview(formId,nodeId,isBill,fieldId,isview,isedit,ismandatory)  values(" + j + "," + i1 + "," + i3 + "," + i4 + ",'" + str8 + "','" + str9 + "','" + str10 + "')");
/*     */           }
/*     */         
/*     */         } 
/* 254 */       } catch (Exception exception) {
/*     */         
/* 256 */         this.status = 1;
/* 257 */         exception.printStackTrace();
/*     */       } 
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 274 */     if ("oracle".equals(recordSet1.getDBType())) {
/* 275 */       recordSet1.execute("select nodeid from ( select nodeid from workflow_flownode where workflowid = " + i + " order by nodeid desc ) where rownum <=" + k);
/* 276 */     } else if (recordSet1.getDBType().equals("mysql")) {
/* 277 */       recordSet1.execute("select nodeid from workflow_flownode where workflowid = " + i + " order by nodeid desc limit " + k);
/*     */     }
/* 279 */     else if (recordSet1.getDBType().equals("postgresql")) {
/* 280 */       recordSet1.execute("select nodeid from workflow_flownode where workflowid = " + i + " order by nodeid desc limit " + k);
/*     */     } else {
/*     */       
/* 283 */       recordSet1.execute("select top " + k + " nodeid from workflow_flownode where workflowid = " + i + " order by nodeid desc ");
/*     */     } 
/*     */     
/* 286 */     while (recordSet1.next()) {
/* 287 */       str1 = str1 + "" + recordSet1.getInt(1) + ",";
/*     */     }
/* 289 */     if (str1.length() > 0) {
/* 290 */       str1 = str1.substring(0, str1.length() - 1);
/*     */     }
/* 292 */     return str1;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static String addWorkflowNode(int paramInt1, int paramInt2, int paramInt3, String paramString1, String paramString2, User paramUser, WorkflowNode paramWorkflowNode) {
/* 298 */     String str1 = "";
/*     */     
/* 300 */     byte b1 = 1;
/*     */     
/* 302 */     int i = 0;
/*     */     
/* 304 */     WFNodeMainManager wFNodeMainManager = new WFNodeMainManager();
/* 305 */     RecordSet recordSet1 = new RecordSet();
/* 306 */     RecordSet recordSet2 = new RecordSet();
/* 307 */     SysMaintenanceLog sysMaintenanceLog = new SysMaintenanceLog();
/*     */     
/* 309 */     String str2 = "";
/* 310 */     ArrayList<String> arrayList = new ArrayList();
/* 311 */     str2 = "select nodeid from workflow_flownode where nodetype='0' and workflowid=" + paramInt1;
/* 312 */     recordSet1.execute(str2);
/* 313 */     while (recordSet1.next()) {
/* 314 */       int k = Util.getIntValue(recordSet1.getString(1));
/* 315 */       arrayList.add("" + k);
/* 316 */       i++;
/*     */     } 
/* 318 */     if (!paramString1.equals("")) {
/* 319 */       paramString1 = paramString1.substring(1);
/* 320 */       String[] arrayOfString = Util.TokenizerString2(paramString1, ",");
/* 321 */       for (byte b = 0; b < arrayOfString.length; b++) {
/* 322 */         int k = Util.getIntValue(arrayOfString[b]);
/* 323 */         str2 = " select count(nodeid) from workflow_flownode where nodeid=" + k + " and nodetype='0'";
/*     */         
/* 325 */         recordSet1.executeSql(str2);
/* 326 */         if (recordSet1.next()) {
/* 327 */           i -= recordSet1.getInt(1);
/*     */         }
/*     */       } 
/*     */     } 
/* 331 */     for (byte b2 = 0; b2 < b1; b2++) {
/* 332 */       String str = paramWorkflowNode.nodeType;
/* 333 */       if (str.equals("0")) {
/* 334 */         i++;
/*     */       }
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*     */     try {
/* 344 */       if (!paramString1.equals("")) {
/* 345 */         String[] arrayOfString = Util.TokenizerString2(paramString1, ",");
/* 346 */         wFNodeMainManager.resetParameter();
/* 347 */         wFNodeMainManager.deleteWfNode(arrayOfString, paramUser.getUID());
/*     */ 
/*     */ 
/*     */         
/* 351 */         for (byte b = 0; b < arrayOfString.length; b++) {
/* 352 */           sysMaintenanceLog.resetParameter();
/*     */           
/* 354 */           sysMaintenanceLog.setRelatedId(paramInt1);
/* 355 */           sysMaintenanceLog.setRelatedName(
/* 356 */               SystemEnv.getHtmlLabelName(15070, paramUser.getLanguage()));
/* 357 */           sysMaintenanceLog.setOperateType("3");
/* 358 */           sysMaintenanceLog.setOperateDesc("WrokFlowNode_delete");
/* 359 */           sysMaintenanceLog.setOperateItem("86");
/* 360 */           sysMaintenanceLog.setOperateUserid(paramUser.getUID());
/* 361 */           sysMaintenanceLog.setClientAddress(paramString2);
/* 362 */           sysMaintenanceLog.setSysLogInfo();
/*     */         }
/*     */       
/*     */       } 
/* 366 */     } catch (Exception exception) {
/*     */ 
/*     */       
/* 369 */       exception.printStackTrace();
/* 370 */       return "false";
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/* 375 */     String str3 = "0";
/* 376 */     recordSet1.executeSql("select id from workflow_formmode where formid=" + paramInt2 + " and isbill='" + paramInt3 + "'");
/*     */     
/* 378 */     if (recordSet1.next()) {
/* 379 */       str3 = "1";
/*     */     }
/*     */     
/* 382 */     int j = 0;
/* 383 */     for (byte b3 = 0; b3 < b1; b3++) {
/* 384 */       wFNodeMainManager.resetParameter();
/* 385 */       wFNodeMainManager.setWfid(paramInt1);
/* 386 */       wFNodeMainManager.setFormid(paramInt2);
/* 387 */       String str4 = paramWorkflowNode.name;
/*     */       
/* 389 */       String str5 = paramWorkflowNode.nodeType;
/* 390 */       int k = paramWorkflowNode.getNodeorder();
/* 391 */       String str6 = paramWorkflowNode.getNodeAttribute();
/* 392 */       int m = paramWorkflowNode.getPassnum();
/*     */ 
/*     */       
/* 395 */       i--;
/* 396 */       if ("0".equals(str5) && i > 0) {
/* 397 */         str5 = "1";
/*     */       }
/*     */ 
/*     */       
/* 401 */       wFNodeMainManager.setNodename(str4);
/* 402 */       wFNodeMainManager.setNodetype(str5);
/* 403 */       wFNodeMainManager.setNodeorder(k);
/* 404 */       wFNodeMainManager.setNodeattribute(str6);
/* 405 */       wFNodeMainManager.setNodepassnum(m);
/* 406 */       wFNodeMainManager.setIsbill(paramInt3);
/* 407 */       wFNodeMainManager.setDrawxpos(paramWorkflowNode.x + 60);
/* 408 */       wFNodeMainManager.setDrawypos(paramWorkflowNode.y + 40);
/*     */       
/* 410 */       sysMaintenanceLog.resetParameter();
/* 411 */       sysMaintenanceLog.setRelatedId(paramInt1);
/* 412 */       sysMaintenanceLog.setRelatedName(str4);
/* 413 */       sysMaintenanceLog.setOperateItem("86");
/* 414 */       sysMaintenanceLog.setOperateUserid(paramUser.getUID());
/* 415 */       sysMaintenanceLog.setClientAddress(paramString2);
/*     */       
/*     */       try {
/* 418 */         if (!str4.equals("") && !str5.equals("") && j) {
/*     */           
/* 420 */           sysMaintenanceLog.setOperateType("2");
/* 421 */           sysMaintenanceLog.setOperateDesc("WrokFlowNode_update");
/* 422 */           sysMaintenanceLog.setSysLogInfo();
/* 423 */           wFNodeMainManager.setNodeid(j);
/* 424 */           wFNodeMainManager.updateWfNode();
/*     */         } 
/*     */         
/* 427 */         if (!str4.equals("") && !str5.equals("") && !j) {
/*     */           
/* 429 */           sysMaintenanceLog.setOperateType("1");
/* 430 */           sysMaintenanceLog.setOperateDesc("WrokFlowNode_insert");
/* 431 */           sysMaintenanceLog.setSysLogInfo();
/* 432 */           wFNodeMainManager.saveWfNode();
/* 433 */           j = wFNodeMainManager.getNodeid2();
/*     */         } 
/*     */         
/* 436 */         if (str3.equals("1")) {
/* 437 */           recordSet1.executeSql("select  distinct * from workflow_modeview where formid=" + paramInt2 + " and nodeid=0 and not exists(select * from workflow_modeview where formid=" + paramInt2 + " and nodeid=" + j + " and fieldId=workflow_modeview.fieldId)");
/* 438 */           while (recordSet1.next()) {
/* 439 */             int n = recordSet1.getInt("isbill");
/* 440 */             int i1 = recordSet1.getInt("fieldid");
/* 441 */             String str7 = recordSet1.getString("isview");
/* 442 */             String str8 = recordSet1.getString("isedit");
/* 443 */             String str9 = recordSet1.getString("ismandatory");
/*     */             
/* 445 */             recordSet2.executeSql("insert into workflow_modeview(formId,nodeId,isBill,fieldId,isview,isedit,ismandatory)  values(" + paramInt2 + "," + j + "," + n + "," + i1 + ",'" + str7 + "','" + str8 + "','" + str9 + "')");
/*     */           }
/*     */         
/*     */         } 
/* 449 */       } catch (Exception exception) {
/*     */ 
/*     */ 
/*     */         
/* 453 */         exception.printStackTrace();
/* 454 */         return "false";
/*     */       } 
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 471 */     if ("oracle".equals(recordSet1.getDBType())) {
/* 472 */       recordSet1.execute("select nodeid from ( select nodeid from workflow_flownode where nodeid=" + j + " and workflowid = " + paramInt1 + " order by nodeid desc ) where rownum <=" + b1);
/* 473 */     } else if (recordSet1.getDBType().equals("mysql")) {
/* 474 */       recordSet1.execute("select nodeid from workflow_flownode where workflowid = " + paramInt1 + " order by nodeid desc limit " + b1);
/*     */     }
/* 476 */     else if (recordSet1.getDBType().equals("postgresql")) {
/* 477 */       recordSet1.execute("select nodeid from workflow_flownode where workflowid = " + paramInt1 + " order by nodeid desc limit " + b1);
/*     */     } else {
/*     */       
/* 480 */       recordSet1.execute("select top " + b1 + " nodeid from workflow_flownode where nodeid=" + j + " and workflowid = " + paramInt1 + " order by nodeid desc ");
/*     */     } 
/*     */     
/* 483 */     if (recordSet1.next()) {
/* 484 */       str1 = "" + recordSet1.getInt(1);
/*     */     }
/* 486 */     if (str1.length() > 0);
/*     */ 
/*     */     
/* 489 */     return str1;
/*     */   }
/*     */ 
/*     */   
/*     */   public static String addWorkflowLink(int paramInt, String paramString1, String paramString2, User paramUser, WorkflowLine paramWorkflowLine) {
/* 494 */     String str = "";
/*     */     
/* 496 */     WFNodePortalMainManager wFNodePortalMainManager = new WFNodePortalMainManager();
/* 497 */     SysMaintenanceLog sysMaintenanceLog = new SysMaintenanceLog();
/* 498 */     if (!paramString1.equals("")) {
/* 499 */       paramString1 = paramString1.substring(1);
/* 500 */       String[] arrayOfString = Util.TokenizerString2(paramString1, ",");
/*     */       try {
/* 502 */         for (byte b = 0; b < arrayOfString.length; b++) {
/* 503 */           wFNodePortalMainManager.resetParameter();
/* 504 */           wFNodePortalMainManager.setId(Util.getIntValue(arrayOfString[b], 0));
/* 505 */           wFNodePortalMainManager.deleteWfNodePortal();
/*     */ 
/*     */           
/* 508 */           sysMaintenanceLog.resetParameter();
/* 509 */           sysMaintenanceLog.setRelatedId(paramInt);
/* 510 */           sysMaintenanceLog.setRelatedName(SystemEnv.getHtmlLabelName(15611, paramUser.getLanguage()));
/* 511 */           sysMaintenanceLog.setOperateType("3");
/* 512 */           sysMaintenanceLog.setOperateDesc("WrokFlowNodePortal_delete");
/* 513 */           sysMaintenanceLog.setOperateItem("88");
/* 514 */           sysMaintenanceLog.setOperateUserid(paramUser.getUID());
/* 515 */           sysMaintenanceLog.setClientAddress(paramString2);
/* 516 */           sysMaintenanceLog.setSysLogInfo();
/*     */         }
/*     */       
/* 519 */       } catch (Exception exception) {}
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 525 */     byte b1 = 1;
/* 526 */     for (byte b2 = 0; b2 < b1; b2++) {
/* 527 */       wFNodePortalMainManager.resetParameter();
/* 528 */       wFNodePortalMainManager.setWfid(paramInt);
/*     */       
/* 530 */       boolean bool = false;
/*     */       
/* 532 */       wFNodePortalMainManager.setNodeid(paramWorkflowLine.fromNodeId);
/* 533 */       wFNodePortalMainManager.setIsreject(paramWorkflowLine.getIsReject());
/* 534 */       wFNodePortalMainManager.setLinkorder(paramWorkflowLine.getLinkorder());
/*     */       
/* 536 */       wFNodePortalMainManager.setLinkname(paramWorkflowLine.getLineName());
/* 537 */       wFNodePortalMainManager.setCondition("");
/*     */       
/* 539 */       wFNodePortalMainManager.setConditioncn("");
/* 540 */       wFNodePortalMainManager.setDestnodeid(paramWorkflowLine.toNodeId);
/* 541 */       wFNodePortalMainManager.setPasstime(Util.getFloatValue("", -1.0F));
/* 542 */       wFNodePortalMainManager.setIsBulidCode("");
/* 543 */       wFNodePortalMainManager.setIsMustPass("");
/* 544 */       wFNodePortalMainManager.setTipsinfo("");
/* 545 */       wFNodePortalMainManager.setDirectionfrom(Util.getIntValue((String)paramWorkflowLine.attrMap.get("directionfrom")));
/* 546 */       wFNodePortalMainManager.setDirectionto(Util.getIntValue((String)paramWorkflowLine.attrMap.get("directionto")));
/*     */ 
/*     */       
/* 549 */       sysMaintenanceLog.resetParameter();
/* 550 */       sysMaintenanceLog.setRelatedId(paramInt);
/* 551 */       sysMaintenanceLog.setRelatedName(paramWorkflowLine.getLineName());
/* 552 */       sysMaintenanceLog.setOperateItem("88");
/* 553 */       sysMaintenanceLog.setOperateUserid(paramUser.getUID());
/* 554 */       sysMaintenanceLog.setClientAddress(paramString2);
/*     */       try {
/* 556 */         if (paramWorkflowLine.toNodeId != -1 && !bool) {
/* 557 */           wFNodePortalMainManager.saveWfNodePortal();
/*     */           
/* 559 */           sysMaintenanceLog.setOperateType("1");
/* 560 */           sysMaintenanceLog.setOperateDesc("WrokFlowNodePortal_insert");
/* 561 */           sysMaintenanceLog.setSysLogInfo();
/*     */         } 
/* 563 */         if (paramWorkflowLine.toNodeId != -1 && bool) {
/* 564 */           wFNodePortalMainManager.setId(bool);
/* 565 */           wFNodePortalMainManager.updateWfNodePortal();
/*     */           
/* 567 */           sysMaintenanceLog.setOperateType("2");
/* 568 */           sysMaintenanceLog.setOperateDesc("WrokFlowNodePortal_update");
/* 569 */           sysMaintenanceLog.setSysLogInfo();
/*     */         } 
/* 571 */       } catch (Exception exception) {}
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 583 */     RecordSet recordSet = new RecordSet();
/* 584 */     if ("oracle".equals(recordSet.getDBType())) {
/* 585 */       recordSet.execute("select id from( select id from workflow_nodelink where workflowid = " + paramInt + " order by id desc ) where rownum <=" + b1 + " ");
/* 586 */     } else if (recordSet.getDBType().equals("mysql")) {
/* 587 */       recordSet.execute("select id from workflow_nodelink where workflowid = " + paramInt + " order by id desc limit " + b1);
/*     */     }
/* 589 */     else if (recordSet.getDBType().equals("postgresql")) {
/* 590 */       recordSet.execute("select id from workflow_nodelink where workflowid = " + paramInt + " order by id desc limit " + b1);
/*     */     } else {
/*     */       
/* 593 */       recordSet.execute("select top " + b1 + " id from workflow_nodelink where workflowid = " + paramInt + " order by id desc");
/*     */     } 
/* 595 */     while (recordSet.next()) {
/* 596 */       str = str + recordSet.getInt(1) + ",";
/*     */     }
/* 598 */     if (str.length() > 0) {
/* 599 */       str = str.substring(0, str.length() - 1);
/*     */     }
/* 601 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String addWorkflowLink(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/* 610 */     String str1 = "";
/* 611 */     int i = Util.getIntValue(Util.null2String(paramHttpServletRequest.getParameter("wfid")), 0);
/*     */     
/* 613 */     String str2 = Util.null2String(paramHttpServletRequest.getParameter("delids"));
/* 614 */     WFNodePortalMainManager wFNodePortalMainManager = new WFNodePortalMainManager();
/* 615 */     SysMaintenanceLog sysMaintenanceLog = new SysMaintenanceLog();
/* 616 */     if (!str2.equals("")) {
/* 617 */       str2 = str2.substring(1);
/* 618 */       String[] arrayOfString = Util.TokenizerString2(str2, ",");
/*     */       try {
/* 620 */         for (byte b1 = 0; b1 < arrayOfString.length; b1++) {
/* 621 */           wFNodePortalMainManager.resetParameter();
/* 622 */           wFNodePortalMainManager.setId(Util.getIntValue(arrayOfString[b1], 0));
/* 623 */           wFNodePortalMainManager.deleteWfNodePortal();
/*     */ 
/*     */           
/* 626 */           sysMaintenanceLog.resetParameter();
/* 627 */           sysMaintenanceLog.setRelatedId(i);
/* 628 */           sysMaintenanceLog.setRelatedName(SystemEnv.getHtmlLabelName(15611, this.user.getLanguage()));
/* 629 */           sysMaintenanceLog.setOperateType("3");
/* 630 */           sysMaintenanceLog.setOperateDesc("WrokFlowNodePortal_delete");
/* 631 */           sysMaintenanceLog.setOperateItem("88");
/* 632 */           sysMaintenanceLog.setOperateUserid(this.user.getUID());
/* 633 */           sysMaintenanceLog.setClientAddress(paramHttpServletRequest.getRemoteAddr());
/* 634 */           sysMaintenanceLog.setSysLogInfo();
/*     */         }
/*     */       
/* 637 */       } catch (Exception exception) {
/*     */         
/* 639 */         this.status = 1;
/*     */       } 
/*     */     } 
/*     */     
/* 643 */     int j = Util.getIntValue(Util.null2String(paramHttpServletRequest.getParameter("stepssum")));
/* 644 */     for (byte b = 0; b < j; b++) {
/* 645 */       wFNodePortalMainManager.resetParameter();
/* 646 */       wFNodePortalMainManager.setWfid(i);
/*     */       
/* 648 */       int k = Util.getIntValue(Util.null2String(paramHttpServletRequest.getParameter("por" + b + "_id")), 0);
/*     */       
/* 650 */       wFNodePortalMainManager.setNodeid(Util.getIntValue(Util.null2String(paramHttpServletRequest.getParameter("por" + b + "_nodeid")), 0));
/* 651 */       wFNodePortalMainManager.setIsreject(Util.null2String(paramHttpServletRequest.getParameter("por" + b + "_rej")));
/* 652 */       String str = "";
/*     */       try {
/* 654 */         str = URLDecoder.decode(Util.null2String(paramHttpServletRequest.getParameter("por" + b + "_link")), "UTF-8");
/* 655 */       } catch (UnsupportedEncodingException unsupportedEncodingException) {
/*     */         
/* 657 */         unsupportedEncodingException.printStackTrace();
/*     */       } 
/* 659 */       wFNodePortalMainManager.setLinkname(str);
/* 660 */       wFNodePortalMainManager.setCondition(Util.null2String(paramHttpServletRequest.getParameter("por" + b + "_con")));
/*     */       
/* 662 */       wFNodePortalMainManager.setConditioncn(Util.null2String(paramHttpServletRequest.getParameter("por" + b + "_con_cn")));
/* 663 */       wFNodePortalMainManager.setDestnodeid(Util.getIntValue(paramHttpServletRequest.getParameter("por" + b + "_des")));
/* 664 */       wFNodePortalMainManager.setPasstime(Util.getFloatValue((String)paramHttpServletRequest.getSession(true).getAttribute("por" + b + "_passtime"), -1.0F));
/* 665 */       wFNodePortalMainManager.setIsBulidCode(Util.null2String(paramHttpServletRequest.getParameter("por" + b + "_isBulidCode")));
/* 666 */       wFNodePortalMainManager.setIsMustPass(Util.null2String(paramHttpServletRequest.getParameter("por" + b + "_ismustpass")));
/* 667 */       wFNodePortalMainManager.setTipsinfo(Util.null2String(paramHttpServletRequest.getParameter("por" + b + "_tipsinfo")));
/* 668 */       wFNodePortalMainManager.setDirectionfrom(Util.getIntValue(paramHttpServletRequest.getParameter("por" + b + "_directionfrom")));
/* 669 */       wFNodePortalMainManager.setDirectionto(Util.getIntValue(paramHttpServletRequest.getParameter("por" + b + "_directionto")));
/*     */ 
/*     */       
/* 672 */       sysMaintenanceLog.resetParameter();
/* 673 */       sysMaintenanceLog.setRelatedId(i);
/*     */       
/* 675 */       sysMaintenanceLog.setRelatedName(str);
/* 676 */       sysMaintenanceLog.setOperateItem("88");
/* 677 */       sysMaintenanceLog.setOperateUserid(this.user.getUID());
/* 678 */       sysMaintenanceLog.setClientAddress(paramHttpServletRequest.getRemoteAddr());
/*     */       try {
/* 680 */         if (Util.getIntValue(paramHttpServletRequest.getParameter("por" + b + "_des")) != -1 && k == 0) {
/* 681 */           wFNodePortalMainManager.saveWfNodePortal();
/*     */           
/* 683 */           sysMaintenanceLog.setOperateType("1");
/* 684 */           sysMaintenanceLog.setOperateDesc("WrokFlowNodePortal_insert");
/* 685 */           sysMaintenanceLog.setSysLogInfo();
/*     */         } 
/* 687 */         if (Util.getIntValue(paramHttpServletRequest.getParameter("por" + b + "_des")) != -1 && k != 0) {
/* 688 */           wFNodePortalMainManager.setId(k);
/* 689 */           wFNodePortalMainManager.updateWfNodePortal();
/*     */           
/* 691 */           sysMaintenanceLog.setOperateType("2");
/* 692 */           sysMaintenanceLog.setOperateDesc("WrokFlowNodePortal_update");
/* 693 */           sysMaintenanceLog.setSysLogInfo();
/*     */         } 
/* 695 */       } catch (Exception exception) {
/*     */         
/* 697 */         this.status = 1;
/*     */       } 
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 707 */     RecordSet recordSet = new RecordSet();
/* 708 */     if ("oracle".equals(recordSet.getDBType())) {
/* 709 */       recordSet.execute("select id from( select id from workflow_nodelink where workflowid = " + i + " order by id desc ) where rownum <=" + j + " ");
/* 710 */     } else if (recordSet.getDBType().equals("mysql")) {
/* 711 */       recordSet.execute("select id from workflow_nodelink where workflowid = " + i + " order by id desc limit " + j);
/*     */     }
/* 713 */     else if (recordSet.getDBType().equals("postgresql")) {
/* 714 */       recordSet.execute("select id from workflow_nodelink where workflowid = " + i + " order by id desc limit " + j);
/*     */     } else {
/*     */       
/* 717 */       recordSet.execute("select top " + j + " id from workflow_nodelink where workflowid = " + i + " order by id desc");
/*     */     } 
/* 719 */     while (recordSet.next()) {
/* 720 */       str1 = str1 + recordSet.getInt(1) + ",";
/*     */     }
/* 722 */     if (str1.length() > 0) {
/* 723 */       str1 = str1.substring(0, str1.length() - 1);
/*     */     }
/* 725 */     return str1;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/layout/WorkflowDesignOperatoinServlet.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */