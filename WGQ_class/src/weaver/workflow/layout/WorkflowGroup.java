/*     */ package weaver.workflow.layout;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class WorkflowGroup
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 1L;
/*     */   private int id;
/*     */   private int workflowid;
/*     */   private String groupname;
/*     */   private double direction;
/*     */   private double x;
/*     */   private double y;
/*     */   private double width;
/*     */   private double height;
/*     */   private boolean isNew = false;
/*     */   
/*     */   public int getId() {
/*  70 */     return this.id;
/*     */   }
/*     */   
/*     */   public void setId(int paramInt) {
/*  74 */     this.id = paramInt;
/*     */   }
/*     */   
/*     */   public int getWorkflowid() {
/*  78 */     return this.workflowid;
/*     */   }
/*     */   
/*     */   public void setWorkflowid(int paramInt) {
/*  82 */     this.workflowid = paramInt;
/*     */   }
/*     */   
/*     */   public String getGroupname() {
/*  86 */     return this.groupname;
/*     */   }
/*     */   
/*     */   public void setGroupname(String paramString) {
/*  90 */     this.groupname = paramString;
/*     */   }
/*     */   
/*     */   public double getDirection() {
/*  94 */     return this.direction;
/*     */   }
/*     */   
/*     */   public void setDirection(double paramDouble) {
/*  98 */     this.direction = paramDouble;
/*     */   }
/*     */   
/*     */   public double getX() {
/* 102 */     return this.x;
/*     */   }
/*     */   
/*     */   public void setX(double paramDouble) {
/* 106 */     this.x = paramDouble;
/*     */   }
/*     */   
/*     */   public double getY() {
/* 110 */     return this.y;
/*     */   }
/*     */   
/*     */   public void setY(double paramDouble) {
/* 114 */     this.y = paramDouble;
/*     */   }
/*     */   
/*     */   public double getWidth() {
/* 118 */     return this.width;
/*     */   }
/*     */   
/*     */   public void setWidth(double paramDouble) {
/* 122 */     this.width = paramDouble;
/*     */   }
/*     */   
/*     */   public double getHeight() {
/* 126 */     return this.height;
/*     */   }
/*     */   
/*     */   public void setHeight(double paramDouble) {
/* 130 */     this.height = paramDouble;
/*     */   }
/*     */   
/*     */   public boolean isNew() {
/* 134 */     return this.isNew;
/*     */   }
/*     */   
/*     */   public void setNew(boolean paramBoolean) {
/* 138 */     this.isNew = paramBoolean;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/layout/WorkflowGroup.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */