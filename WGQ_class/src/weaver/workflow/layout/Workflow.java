/*     */ package weaver.workflow.layout;
/*     */ 
/*     */ import java.awt.Point;
/*     */ import java.awt.geom.Line2D;
/*     */ import java.io.Serializable;
/*     */ import java.util.List;
/*     */ import java.util.Vector;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class Workflow
/*     */   implements Serializable
/*     */ {
/*     */   public static final int DISTANCE = 9;
/*     */   public boolean hasNoLayout = true;
/*     */   public boolean hasFullLayout = true;
/*  27 */   public Vector nodes = new Vector();
/*     */   
/*  29 */   public Vector lines = new Vector();
/*     */   
/*  31 */   protected int maxLevel = -1;
/*     */   
/*  33 */   protected int id = -1;
/*     */   
/*  35 */   protected Point maxPos = new Point();
/*     */   
/*     */   protected boolean needRedirect = false;
/*     */   
/*  39 */   private String formID = "";
/*     */   
/*  41 */   private int isBill = 0;
/*     */   
/*  43 */   private int isCust = 0;
/*     */   
/*  45 */   private String workflowName = "";
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  50 */   private List groups = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getMaxLevel() {
/*  61 */     return this.maxLevel;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Point getMaxPos() {
/*  69 */     return this.maxPos;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void addNode(WorkflowNode paramWorkflowNode) {
/*  77 */     this.nodes.add(paramWorkflowNode);
/*  78 */     refreshMaxPos(paramWorkflowNode);
/*  79 */     if (paramWorkflowNode.x < 0 || paramWorkflowNode.y < 0) {
/*  80 */       this.hasFullLayout = false;
/*     */     } else {
/*  82 */       this.hasNoLayout = false;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void addLine(WorkflowLine paramWorkflowLine) {
/*  91 */     this.lines.add(paramWorkflowLine);
/*  92 */     refreshMaxPos(paramWorkflowLine);
/*  93 */     if (paramWorkflowLine.fromPointId < 0 || paramWorkflowLine.toPointId < 0) {
/*  94 */       this.hasFullLayout = false;
/*     */     } else {
/*  96 */       this.hasNoLayout = false;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public WorkflowNode findNode(int paramInt) {
/* 106 */     for (byte b = 0; b < this.nodes.size(); b++) {
/* 107 */       WorkflowNode workflowNode = getNode(b);
/* 108 */       if (workflowNode.id == paramInt) {
/* 109 */         return workflowNode;
/*     */       }
/*     */     } 
/* 112 */     return null;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public WorkflowNode getNode(int paramInt) {
/* 120 */     return this.nodes.get(paramInt);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public WorkflowLine getLine(int paramInt) {
/* 128 */     return this.lines.get(paramInt);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Vector getNodesByLevel(int paramInt) {
/* 137 */     Vector<WorkflowNode> vector = new Vector();
/* 138 */     for (byte b = 0; b < this.nodes.size(); b++) {
/* 139 */       WorkflowNode workflowNode = getNode(b);
/* 140 */       if (workflowNode.level == paramInt) {
/* 141 */         vector.add(workflowNode);
/*     */       }
/*     */     } 
/* 144 */     return vector;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public WorkflowNode getStartNode() {
/* 152 */     for (byte b = 0; b < this.nodes.size(); b++) {
/* 153 */       if (getNode(b).isStartNode()) {
/* 154 */         return getNode(b);
/*     */       }
/*     */     } 
/* 157 */     return null;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void clearObjRef() {
/*     */     byte b;
/* 165 */     for (b = 0; b < this.nodes.size(); b++) {
/* 166 */       WorkflowNode workflowNode = getNode(b);
/* 167 */       workflowNode.inLines.clear();
/* 168 */       workflowNode.outLines.clear();
/*     */     } 
/* 170 */     for (b = 0; b < this.lines.size(); b++) {
/* 171 */       WorkflowLine workflowLine = getLine(b);
/* 172 */       workflowLine.fromNode = null;
/* 173 */       workflowLine.toNode = null;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void buildObjRef() {
/* 182 */     Vector<WorkflowLine> vector = new Vector(); byte b;
/* 183 */     for (b = 0; b < this.lines.size(); b++) {
/* 184 */       WorkflowLine workflowLine = getLine(b);
/* 185 */       workflowLine.fromNode = findNode(workflowLine.fromNodeId);
/* 186 */       workflowLine.toNode = findNode(workflowLine.toNodeId);
/* 187 */       if (workflowLine.fromNode != null) {
/* 188 */         workflowLine.fromNode.outLines.add(workflowLine);
/*     */       } else {
/* 190 */         vector.add(workflowLine);
/*     */       } 
/* 192 */       if (workflowLine.toNode != null) {
/* 193 */         workflowLine.toNode.inLines.add(workflowLine);
/*     */       } else {
/* 195 */         vector.add(workflowLine);
/*     */       } 
/*     */     } 
/* 198 */     for (b = 0; b < vector.size(); b++) {
/* 199 */       this.lines.remove(vector.get(b));
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void buildTree() {
/* 207 */     buildTree(getStartNode(), 0);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void checkAndAutosetLayout(int paramInt1, int paramInt2, int paramInt3, int paramInt4) {
/* 221 */     if (this.hasNoLayout) {
/*     */       
/* 223 */       autosetLayout(paramInt1, paramInt2, paramInt3, paramInt4);
/* 224 */     } else if (!this.hasFullLayout) {
/* 225 */       if (this.maxPos.y > paramInt2) {
/* 226 */         autosetPart(paramInt1, this.maxPos.y + paramInt3, paramInt4);
/*     */       } else {
/* 228 */         autosetPart(paramInt1, paramInt2 + paramInt3, paramInt4);
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void autosetLayout(int paramInt1, int paramInt2, int paramInt3, int paramInt4) {
/* 241 */     paramInt2 = (paramInt2 > paramInt3 / 2 + 10) ? paramInt2 : (paramInt3 / 2 + 10);
/*     */     
/* 243 */     autosetLayoutForNode(paramInt1, paramInt2, paramInt3, paramInt4, getStartNode());
/*     */     
/* 245 */     autosetLayoutForLine(paramInt3, paramInt4);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void autosetPart(int paramInt1, int paramInt2, int paramInt3) {
/* 256 */     int i = paramInt1;
/* 257 */     for (byte b = 0; b < this.nodes.size(); b++) {
/* 258 */       WorkflowNode workflowNode = getNode(b);
/* 259 */       if (workflowNode.x < 0 || workflowNode.y < 0) {
/* 260 */         workflowNode.x = i;
/* 261 */         workflowNode.y = paramInt2;
/* 262 */         i = i + workflowNode.getWidth() + paramInt3; byte b1;
/* 263 */         for (b1 = 0; b1 < workflowNode.inLines.size(); b1++) {
/* 264 */           ((WorkflowLine)workflowNode.inLines.get(b1)).toPointId = 8;
/*     */         }
/* 266 */         for (b1 = 0; b1 < workflowNode.outLines.size(); b1++) {
/* 267 */           ((WorkflowLine)workflowNode.outLines.get(b1)).fromPointId = 0;
/*     */         }
/*     */       } 
/*     */     } 
/* 271 */     if (i - paramInt3 > this.maxPos.x) this.maxPos.x = i - paramInt3;
/*     */   
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean approximateEqual(Point paramPoint1, Point paramPoint2) {
/* 281 */     if (paramPoint1.distance(paramPoint2) <= 9.0D) {
/* 282 */       return true;
/*     */     }
/* 284 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean approximateIntersect(int paramInt1, int paramInt2, int paramInt3, int paramInt4, int paramInt5, int paramInt6) {
/* 299 */     if (Line2D.Double.ptSegDist(paramInt1, paramInt2, paramInt3, paramInt4, paramInt5, paramInt6) <= 9.0D) {
/* 300 */       return true;
/*     */     }
/* 302 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   protected void refreshMaxPos(WorkflowNode paramWorkflowNode) {
/* 310 */     if (paramWorkflowNode.x + paramWorkflowNode.getWidth() > this.maxPos.x) this.maxPos.x = paramWorkflowNode.x + paramWorkflowNode.getWidth(); 
/* 311 */     if (paramWorkflowNode.y + paramWorkflowNode.getHeight() > this.maxPos.y) this.maxPos.y = paramWorkflowNode.y + paramWorkflowNode.getHeight();
/*     */   
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   protected void refreshMaxPos(WorkflowLine paramWorkflowLine) {
/* 318 */     for (byte b = 0; b < paramWorkflowLine.getValidCtrlPointCount(); b++) {
/* 319 */       Point point = paramWorkflowLine.getCtrlPoint(b);
/* 320 */       if (point.x > this.maxPos.x) this.maxPos.x = point.x; 
/* 321 */       if (point.y > this.maxPos.y) this.maxPos.y = point.y;
/*     */     
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   protected void buildTree(WorkflowNode paramWorkflowNode, int paramInt) {
/* 332 */     if (paramWorkflowNode == null)
/* 333 */       return;  if (paramWorkflowNode.level < 0 || paramWorkflowNode.level > paramInt) {
/* 334 */       paramWorkflowNode.level = paramInt;
/* 335 */       Vector<WorkflowNode> vector = paramWorkflowNode.getForwardNodes();
/* 336 */       for (byte b = 0; b < vector.size(); b++) {
/* 337 */         buildTree(vector.get(b), paramInt + 1);
/*     */       }
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   protected void autosetLayoutForNode(int paramInt1, int paramInt2, int paramInt3, int paramInt4, WorkflowNode paramWorkflowNode) {
/* 353 */     if (paramWorkflowNode != null) {
/* 354 */       paramWorkflowNode.x = paramInt1;
/* 355 */       paramWorkflowNode.y = paramInt2;
/* 356 */       refreshMaxPos(paramWorkflowNode);
/* 357 */       Vector<WorkflowNode> vector = paramWorkflowNode.getForwardNodes();
/* 358 */       int i = paramInt2;
/* 359 */       for (byte b = 0; b < vector.size(); b++) {
/* 360 */         WorkflowNode workflowNode = vector.get(b);
/* 361 */         if (workflowNode.level == paramWorkflowNode.level + 1) {
/* 362 */           autosetLayoutForNode(paramInt1 + paramWorkflowNode.getWidth() + paramInt4, i, paramInt3, paramInt4, workflowNode);
/* 363 */           i = i + workflowNode.getHeight() + paramInt3;
/*     */         } 
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   protected void autosetLayoutForLine(int paramInt1, int paramInt2) {
/* 375 */     for (byte b = 0; b < this.lines.size(); b++) {
/* 376 */       WorkflowLine workflowLine = getLine(b);
/* 377 */       WorkflowNode workflowNode1 = workflowLine.getFromNode();
/* 378 */       WorkflowNode workflowNode2 = workflowLine.getToNode();
/* 379 */       if (workflowNode1.y > workflowNode2.y) {
/*     */         
/* 381 */         if (workflowNode1.x < workflowNode2.x) {
/*     */           
/* 383 */           workflowLine.fromPointId = 15;
/* 384 */           workflowLine.toPointId = 7;
/* 385 */           workflowLine.controlPoints.clear();
/* 386 */           workflowLine.controlPoints.add(new Point(workflowNode1.x + workflowNode1.getWidth() + paramInt2 / 2, (workflowNode1.getConnectionPoint(15)).y));
/* 387 */           workflowLine.controlPoints.add(new Point(workflowNode1.x + workflowNode1.getWidth() + paramInt2 / 2, (workflowNode2.getConnectionPoint(7)).y));
/* 388 */         } else if (workflowNode1.x > workflowNode2.x) {
/*     */           
/* 390 */           workflowLine.fromPointId = 9;
/* 391 */           workflowLine.toPointId = 1;
/* 392 */           workflowLine.controlPoints.clear();
/* 393 */           workflowLine.controlPoints.add(new Point(workflowNode1.x - paramInt2 / 2, (workflowNode1.getConnectionPoint(9)).y));
/* 394 */           workflowLine.controlPoints.add(new Point(workflowNode1.x - paramInt2 / 2, (workflowNode2.getConnectionPoint(1)).y));
/*     */         } else {
/*     */           
/* 397 */           workflowLine.fromPointId = 12;
/* 398 */           workflowLine.toPointId = 4;
/* 399 */           workflowLine.controlPoints.clear();
/*     */         } 
/* 401 */       } else if (workflowNode1.y < workflowNode2.y) {
/*     */         
/* 403 */         if (workflowNode1.x < workflowNode2.x) {
/*     */           
/* 405 */           workflowLine.fromPointId = 1;
/* 406 */           workflowLine.toPointId = 9;
/* 407 */           workflowLine.controlPoints.clear();
/* 408 */           workflowLine.controlPoints.add(new Point(workflowNode1.x + workflowNode1.getWidth() + paramInt2 / 2, (workflowNode1.getConnectionPoint(1)).y));
/* 409 */           workflowLine.controlPoints.add(new Point(workflowNode1.x + workflowNode1.getWidth() + paramInt2 / 2, (workflowNode2.getConnectionPoint(9)).y));
/* 410 */         } else if (workflowNode1.x > workflowNode2.x) {
/*     */           
/* 412 */           workflowLine.fromPointId = 7;
/* 413 */           workflowLine.toPointId = 15;
/* 414 */           workflowLine.controlPoints.clear();
/* 415 */           workflowLine.controlPoints.add(new Point(workflowNode1.x - paramInt2 / 2, (workflowNode1.getConnectionPoint(7)).y));
/* 416 */           workflowLine.controlPoints.add(new Point(workflowNode1.x - paramInt2 / 2, (workflowNode2.getConnectionPoint(15)).y));
/*     */         } else {
/*     */           
/* 419 */           workflowLine.fromPointId = 4;
/* 420 */           workflowLine.toPointId = 12;
/* 421 */           workflowLine.controlPoints.clear();
/*     */         }
/*     */       
/*     */       }
/* 425 */       else if (workflowNode1.x < workflowNode2.x) {
/*     */         
/* 427 */         workflowLine.fromPointId = 0;
/* 428 */         workflowLine.toPointId = 8;
/* 429 */         workflowLine.controlPoints.clear();
/* 430 */       } else if (workflowNode1.x > workflowNode2.x) {
/*     */         
/* 432 */         workflowLine.fromPointId = 11;
/* 433 */         workflowLine.toPointId = 13;
/* 434 */         workflowLine.controlPoints.clear();
/* 435 */         workflowLine.controlPoints.add(new Point((workflowNode1.getConnectionPoint(11)).x, workflowNode1.y - paramInt1 / 2));
/* 436 */         workflowLine.controlPoints.add(new Point((workflowNode2.getConnectionPoint(13)).x, workflowNode1.y - paramInt1 / 2));
/*     */       } else {
/*     */         
/* 439 */         workflowLine.fromPointId = 1;
/* 440 */         workflowLine.toPointId = 3;
/* 441 */         workflowLine.controlPoints.clear();
/* 442 */         workflowLine.controlPoints.add(new Point(workflowNode1.x + workflowNode1.getWidth() + paramInt2 / 2, (workflowNode1.getConnectionPoint(1)).y));
/* 443 */         workflowLine.controlPoints.add(new Point(workflowNode1.x + workflowNode1.getWidth() + paramInt2 / 2, workflowNode1.y + workflowNode1.getHeight() + paramInt1 / 2));
/* 444 */         workflowLine.controlPoints.add(new Point((workflowNode2.getConnectionPoint(3)).x, workflowNode1.y + workflowNode1.getHeight() + paramInt1 / 2));
/*     */       } 
/*     */       
/* 447 */       refreshMaxPos(workflowLine);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getFormID() {
/* 456 */     return this.formID;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setFormID(String paramString) {
/* 463 */     this.formID = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getIsBill() {
/* 471 */     return this.isBill;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setIsBill(int paramInt) {
/* 479 */     this.isBill = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getIsCust() {
/* 487 */     return this.isCust;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setIsCust(int paramInt) {
/* 495 */     this.isCust = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getWorkflowName() {
/* 502 */     return this.workflowName;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setWorkflowName(String paramString) {
/* 510 */     this.workflowName = paramString;
/*     */   }
/*     */   
/*     */   public List getGroups() {
/* 514 */     return this.groups;
/*     */   }
/*     */   
/*     */   public void setGroups(List paramList) {
/* 518 */     this.groups = paramList;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/layout/Workflow.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */