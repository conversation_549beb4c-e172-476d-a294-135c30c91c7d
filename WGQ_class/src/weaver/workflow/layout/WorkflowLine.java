/*     */ package weaver.workflow.layout;
/*     */ 
/*     */ import java.awt.Graphics;
/*     */ import java.awt.Point;
/*     */ import java.awt.geom.AffineTransform;
/*     */ import java.io.Serializable;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import java.util.Vector;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class WorkflowLine
/*     */   implements Serializable
/*     */ {
/*     */   public static final int INDEX_FROM = -2;
/*     */   public static final int INDEX_TO = -3;
/*  25 */   protected int id = -1;
/*     */   
/*  27 */   protected int fromNodeId = -1;
/*     */   
/*  29 */   protected int toNodeId = -1;
/*     */   
/*  31 */   protected int linkorder = -1;
/*     */   
/*  33 */   protected int fromPointId = -1;
/*     */   
/*  35 */   protected int toPointId = -1;
/*     */   
/*  37 */   protected WorkflowNode fromNode = null;
/*     */   
/*  39 */   protected WorkflowNode toNode = null;
/*     */   
/*  41 */   protected Vector controlPoints = new Vector();
/*     */ 
/*     */   
/*     */   protected static final int WING_LENGTH = 8;
/*     */   
/*     */   protected static final double WING_ANGLE = 0.5235987755982988D;
/*     */   
/*  48 */   private String lineName = "";
/*     */ 
/*     */   
/*  51 */   private String remindMsg = "";
/*     */ 
/*     */   
/*  54 */   private String isBuildCodeString = "0";
/*  55 */   private String isMustpass = "0";
/*  56 */   private String isReject = "0";
/*     */   
/*     */   private boolean hasRole = false;
/*     */   
/*  60 */   private String condition = "";
/*     */   
/*  62 */   private int nodePassHour = 0;
/*  63 */   private int nodePassMinute = 0;
/*     */   
/*  65 */   private int startDirection = 0;
/*     */   
/*  67 */   private int endDirection = 0;
/*     */ 
/*     */   
/*  70 */   private String newPoints = "";
/*     */ 
/*     */   
/*  73 */   public Map attrMap = new HashMap<Object, Object>();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getId() {
/*  84 */     return this.id;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public WorkflowNode getFromNode() {
/*  92 */     return this.fromNode;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public WorkflowNode getToNode() {
/* 100 */     return this.toNode;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Point getFromPoint() {
/* 108 */     return this.fromNode.getConnectionPoint(this.fromPointId);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Point getToPoint() {
/* 116 */     return this.toNode.getConnectionPoint(this.toPointId);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static int getMaxCtrlPointCount() {
/* 124 */     return 5;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getValidCtrlPointCount() {
/* 132 */     return this.controlPoints.size();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int isHited(int paramInt1, int paramInt2) {
/* 145 */     Point point1 = getFromPoint();
/* 146 */     if (this.controlPoints.size() > 0) {
/* 147 */       for (byte b = 0; b < this.controlPoints.size(); b++) {
/* 148 */         Point point = getCtrlPoint(b);
/* 149 */         if (Workflow.approximateIntersect(point1.x, point1.y, point.x, point.y, paramInt1, paramInt2)) {
/* 150 */           return b;
/*     */         }
/* 152 */         point1 = point;
/*     */       } 
/*     */     }
/* 155 */     Point point2 = getToPoint();
/* 156 */     if (Workflow.approximateIntersect(point1.x, point1.y, point2.x, point2.y, paramInt1, paramInt2)) {
/* 157 */       return this.controlPoints.size();
/*     */     }
/* 159 */     return -1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void addCtrPoint(int paramInt1, int paramInt2, int paramInt3) {
/* 170 */     this.controlPoints.add(paramInt1, new Point(paramInt2, paramInt3));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int searchCtrlPoint(int paramInt1, int paramInt2) {
/* 183 */     Point point = new Point(paramInt1, paramInt2);
/* 184 */     for (byte b = 0; b < this.controlPoints.size(); b++) {
/* 185 */       Point point1 = getCtrlPoint(b);
/* 186 */       if (Workflow.approximateEqual(point1, point)) {
/* 187 */         return b;
/*     */       }
/*     */     } 
/* 190 */     if (Workflow.approximateEqual(getFromPoint(), point)) return -2; 
/* 191 */     if (Workflow.approximateEqual(getToPoint(), point)) return -3; 
/* 192 */     return -1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int searchNearestCtrlPoint(int paramInt1, int paramInt2) {
/* 203 */     Point point = new Point(paramInt1, paramInt2);
/* 204 */     double d1 = Double.MAX_VALUE;
/*     */     
/* 206 */     byte b = -1;
/*     */     
/* 208 */     for (byte b1 = 0; b1 < this.controlPoints.size(); b1++) {
/* 209 */       Point point1 = getCtrlPoint(b1);
/* 210 */       double d = point1.distance(point);
/* 211 */       if (d < d1) {
/* 212 */         d1 = d;
/* 213 */         b = b1;
/*     */       } 
/*     */     } 
/*     */     
/* 217 */     double d2 = getFromPoint().distance(point);
/* 218 */     if (d2 < d1) {
/* 219 */       d1 = d2;
/* 220 */       b = -2;
/*     */     } 
/*     */     
/* 223 */     d2 = getToPoint().distance(point);
/* 224 */     if (d2 < d1) {
/* 225 */       d1 = d2;
/* 226 */       b = -3;
/*     */     } 
/*     */     
/* 229 */     return b;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Point getCtrlPoint(int paramInt) {
/* 238 */     if (paramInt == -2)
/* 239 */       return getFromPoint(); 
/* 240 */     if (paramInt == -3)
/* 241 */       return getToPoint(); 
/* 242 */     if (paramInt >= 0 && paramInt < this.controlPoints.size()) {
/* 243 */       return this.controlPoints.get(paramInt);
/*     */     }
/* 245 */     return null;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void moveCtrlPoint(int paramInt1, int paramInt2, int paramInt3) {
/*     */     int i;
/* 257 */     switch (paramInt1) {
/*     */       case -2:
/* 259 */         i = this.fromNode.searchConnectionPoint(paramInt2, paramInt3);
/* 260 */         if (i >= 0) {
/* 261 */           this.fromPointId = i;
/*     */         }
/*     */         return;
/*     */       case -3:
/* 265 */         i = this.toNode.searchConnectionPoint(paramInt2, paramInt3);
/* 266 */         if (i >= 0) {
/* 267 */           this.toPointId = i;
/*     */         }
/*     */         return;
/*     */     } 
/* 271 */     if (paramInt1 >= 0 && paramInt1 < this.controlPoints.size()) {
/* 272 */       Point point = getCtrlPoint(paramInt1);
/* 273 */       point.x = paramInt2;
/* 274 */       point.y = paramInt3;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void deleteCtrlPoint(int paramInt) {
/* 284 */     this.controlPoints.remove(paramInt);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public double calcAngle(double paramDouble1, double paramDouble2) {
/* 293 */     paramDouble2 = -paramDouble2;
/* 294 */     if (paramDouble1 == 0.0D) {
/* 295 */       return (paramDouble2 > 0.0D) ? -1.5707963267948966D : 1.5707963267948966D;
/*     */     }
/* 297 */     return (paramDouble1 > 0.0D) ? (Math.PI + Math.atan(paramDouble2 / paramDouble1)) : Math.atan(paramDouble2 / paramDouble1);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void drawSelf(Graphics paramGraphics) {
/* 308 */     if (this.fromNode != null && this.toNode != null) {
/*     */       
/* 310 */       Point point1 = this.fromNode.getConnectionPoint(this.fromPointId);
/* 311 */       if (this.controlPoints.size() > 0) {
/* 312 */         for (byte b = 0; b < this.controlPoints.size(); b++) {
/* 313 */           Point point = this.controlPoints.get(b);
/* 314 */           paramGraphics.drawLine(point1.x, point1.y, point.x, point.y);
/* 315 */           point1 = point;
/*     */         } 
/*     */       }
/* 318 */       Point point2 = this.toNode.getConnectionPoint(this.toPointId);
/* 319 */       paramGraphics.drawLine(point1.x, point1.y, point2.x, point2.y);
/*     */ 
/*     */       
/* 322 */       double d = calcAngle((point1.x - point2.x), (point1.y - point2.y));
/* 323 */       AffineTransform affineTransform = new AffineTransform();
/* 324 */       Point point3 = new Point(-8, 0);
/* 325 */       Point point4 = new Point();
/*     */       
/* 327 */       affineTransform.rotate(d + 0.5235987755982988D);
/* 328 */       affineTransform.deltaTransform(point3, point4);
/* 329 */       paramGraphics.drawLine(point4.x + point2.x, point2.y - point4.y, point2.x, point2.y);
/*     */       
/* 331 */       affineTransform.rotate(-d - 0.5235987755982988D);
/* 332 */       affineTransform.rotate(d - 0.5235987755982988D);
/* 333 */       affineTransform.deltaTransform(point3, point4);
/* 334 */       paramGraphics.drawLine(point4.x + point2.x, point2.y - point4.y, point2.x, point2.y);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getLineName() {
/* 342 */     return this.lineName;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setLineName(String paramString) {
/* 350 */     this.lineName = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getRemindMsg() {
/* 358 */     return this.remindMsg;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setRemindMsg(String paramString) {
/* 366 */     this.remindMsg = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getIsBuildCodeString() {
/* 374 */     return this.isBuildCodeString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setIsBuildCodeString(String paramString) {
/* 382 */     this.isBuildCodeString = paramString;
/*     */   }
/*     */   
/*     */   public Map getAttrMap() {
/* 386 */     return this.attrMap;
/*     */   }
/*     */   
/*     */   public void setAttrMap(Map paramMap) {
/* 390 */     this.attrMap = paramMap;
/*     */   }
/*     */   
/*     */   public String getIsMustpass() {
/* 394 */     return this.isMustpass;
/*     */   }
/*     */   
/*     */   public void setIsMustpass(String paramString) {
/* 398 */     this.isMustpass = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getIsReject() {
/* 406 */     return this.isReject;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setIsReject(String paramString) {
/* 414 */     this.isReject = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean isHasRole() {
/* 422 */     return this.hasRole;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setHasRole(boolean paramBoolean) {
/* 430 */     this.hasRole = paramBoolean;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCondition() {
/* 438 */     return this.condition;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setCondition(String paramString) {
/* 446 */     this.condition = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getNodePassHour() {
/* 455 */     return this.nodePassHour;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setNodePassHour(int paramInt) {
/* 463 */     this.nodePassHour = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getNodePassMinute() {
/* 471 */     return this.nodePassMinute;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setNodePassMinute(int paramInt) {
/* 479 */     this.nodePassMinute = paramInt;
/*     */   }
/*     */   
/*     */   public int getStartDirection() {
/* 483 */     return this.startDirection;
/*     */   }
/*     */   
/*     */   public void setStartDirection(int paramInt) {
/* 487 */     this.startDirection = paramInt;
/*     */   }
/*     */   
/*     */   public int getEndDirection() {
/* 491 */     return this.endDirection;
/*     */   }
/*     */   
/*     */   public void setEndDirection(int paramInt) {
/* 495 */     this.endDirection = paramInt;
/*     */   }
/*     */   
/*     */   public String getNewPoints() {
/* 499 */     return this.newPoints;
/*     */   }
/*     */   
/*     */   public void setNewPoints(String paramString) {
/* 503 */     this.newPoints = paramString;
/*     */   }
/*     */   
/*     */   public int getFromNodeId() {
/* 507 */     return this.fromNodeId;
/*     */   }
/*     */   
/*     */   public void setFromNodeId(int paramInt) {
/* 511 */     this.fromNodeId = paramInt;
/*     */   }
/*     */   
/*     */   public int getToNodeId() {
/* 515 */     return this.toNodeId;
/*     */   }
/*     */   
/*     */   public void setToNodeId(int paramInt) {
/* 519 */     this.toNodeId = paramInt;
/*     */   }
/*     */   
/*     */   public int getLinkorder() {
/* 523 */     return this.linkorder;
/*     */   }
/*     */   
/*     */   public void setLinkorder(int paramInt) {
/* 527 */     this.linkorder = paramInt;
/*     */   }
/*     */   
/*     */   public void setId(int paramInt) {
/* 531 */     this.id = paramInt;
/*     */   }
/*     */   
/*     */   public int getFromPointId() {
/* 535 */     return this.fromPointId;
/*     */   }
/*     */   
/*     */   public void setFromPointId(int paramInt) {
/* 539 */     this.fromPointId = paramInt;
/*     */   }
/*     */   
/*     */   public int getToPointId() {
/* 543 */     return this.toPointId;
/*     */   }
/*     */   
/*     */   public void setToPointId(int paramInt) {
/* 547 */     this.toPointId = paramInt;
/*     */   }
/*     */   
/*     */   public Vector getControlPoints() {
/* 551 */     return this.controlPoints;
/*     */   }
/*     */   
/*     */   public void setControlPoints(Vector paramVector) {
/* 555 */     this.controlPoints = paramVector;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/layout/WorkflowLine.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */