/*     */ package weaver.workflow.layout;
/*     */ 
/*     */ import java.awt.Point;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Iterator;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class WorkflowPicDisplayData
/*     */ {
/*     */   private int requestid;
/*     */   private int workflowid;
/*     */   private int formid;
/*     */   private int isbill;
/*     */   private User user;
/*     */   private String workflowname;
/*     */   
/*     */   public Map<String, Object> getDisplayData(int paramInt) throws Exception {
/*  32 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  33 */     hashMap1.put("workflowid", Integer.valueOf(this.workflowid));
/*  34 */     hashMap1.put("requestid", Integer.valueOf(this.requestid));
/*  35 */     hashMap1.put("formid", Integer.valueOf(this.formid));
/*  36 */     hashMap1.put("isbill", Integer.valueOf(this.isbill));
/*     */     
/*  38 */     hashMap1.put("text", Util.toXmlText(this.workflowname));
/*     */     
/*  40 */     ArrayList<HashMap<Object, Object>> arrayList1 = new ArrayList();
/*     */     
/*  42 */     ArrayList<HashMap<Object, Object>> arrayList2 = new ArrayList();
/*     */     
/*  44 */     ArrayList<HashMap<Object, Object>> arrayList3 = new ArrayList();
/*  45 */     hashMap1.put("nodes", arrayList1);
/*  46 */     hashMap1.put("links", arrayList2);
/*  47 */     hashMap1.put("groups", arrayList3);
/*  48 */     hashMap1.put("workflowid", Integer.valueOf(this.workflowid));
/*  49 */     RequestDisplayInfo requestDisplayInfo = null;
/*  50 */     RequestDisplayInfoH5 requestDisplayInfoH5 = null;
/*  51 */     Map<String, Object> map = null;
/*     */ 
/*     */     
/*  54 */     if (paramInt == 1) {
/*  55 */       requestDisplayInfoH5 = new RequestDisplayInfoH5(String.valueOf(this.workflowid), String.valueOf(this.requestid));
/*  56 */       requestDisplayInfoH5.setUser(this.user);
/*  57 */       map = requestDisplayInfoH5.getReqDisInfo();
/*     */     } else {
/*  59 */       requestDisplayInfo = new RequestDisplayInfo(String.valueOf(this.workflowid), String.valueOf(this.requestid));
/*  60 */       requestDisplayInfo.setUser(this.user);
/*  61 */       map = requestDisplayInfo.getReqDisInfo();
/*     */     } 
/*     */     
/*  64 */     List list1 = (List)map.get("nodeinfo");
/*  65 */     List list2 = (List)map.get("lineinfo");
/*     */     
/*  67 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/*     */     
/*  69 */     Iterator<Map> iterator = list1.iterator();
/*  70 */     for (byte b = 0; iterator.hasNext(); b++) {
/*  71 */       Map map1 = iterator.next();
/*  72 */       int[] arrayOfInt = (int[])map1.get("nodeDecPoint");
/*  73 */       int i = Util.getIntValue((String)map1.get("ntype"));
/*  74 */       double d1 = 0.0D;
/*  75 */       double d2 = 0.0D;
/*     */       
/*  77 */       HashMap<Object, Object> hashMap3 = new HashMap<>();
/*     */ 
/*     */       
/*  80 */       hashMap3.put("id", map1.get("nodeid"));
/*  81 */       hashMap3.put("name", Util.toXmlText((String)map1.get("nodeName")));
/*     */       
/*  83 */       hashMap3.put("nodeType", Integer.valueOf(i));
/*  84 */       hashMap3.put("nodeAttribute", map1.get("nodeattribute"));
/*     */ 
/*     */       
/*  87 */       hashMap3.put("shapeType", "RoundRect");
/*  88 */       if (i == 0 || i == 3) {
/*  89 */         d1 = 110.0D;
/*  90 */         d2 = 110.0D;
/*  91 */       } else if (i == 1) {
/*  92 */         d1 = 130.0D;
/*  93 */         d2 = 90.0D;
/*     */       } else {
/*  95 */         d1 = 130.0D;
/*  96 */         d2 = 120.0D;
/*     */       } 
/*     */       
/*  99 */       hashMap3.put("width", Double.valueOf(d1));
/* 100 */       hashMap3.put("height", Double.valueOf(d2));
/* 101 */       HashMap<Object, Object> hashMap4 = new HashMap<>();
/*     */ 
/*     */       
/* 104 */       hashMap4.put("x", Integer.valueOf(arrayOfInt[0]));
/* 105 */       hashMap4.put("y", Integer.valueOf(arrayOfInt[1]));
/* 106 */       hashMap3.put("point", hashMap4);
/* 107 */       hashMap3.put("zIndex", Integer.valueOf(-1));
/* 108 */       hashMap3.put("status", Integer.valueOf(((Integer)map1.get("nodeType")).intValue()));
/* 109 */       hashMap3.put("operator", map1.get("nodeOperatorName"));
/* 110 */       hashMap3.put("viewers", parseResourceInfo((List<String>)map1.get("nodeViewNameList")));
/* 111 */       hashMap3.put("operators", parseResourceInfo((List<String>)map1.get("nodeOperatorNameList")));
/* 112 */       hashMap3.put("notOperators", parseResourceInfo((List<String>)map1.get("nodeNotOperatorNameList")));
/* 113 */       arrayList1.add(hashMap3);
/*     */       
/* 115 */       hashMap2.put(Util.null2String(map1.get("nodeid")), hashMap3);
/*     */     } 
/*     */ 
/*     */     
/* 119 */     for (Map map1 : list2) {
/*     */ 
/*     */ 
/*     */       
/* 123 */       List list = (List)map1.get("lines");
/* 124 */       String str1 = Util.null2String((String)map1.get("ispass"));
/*     */       
/* 126 */       String str2 = Util.null2String(map1.get("directfrom"));
/* 127 */       String str3 = Util.null2String(map1.get("directto"));
/*     */       
/* 129 */       Map map2 = (Map)hashMap2.get(str2);
/* 130 */       Map map3 = (Map)hashMap2.get(str3);
/* 131 */       if (map2 == null || map3 == null) {
/*     */         continue;
/*     */       }
/* 134 */       String str4 = Util.null2String(map1.get("points"));
/* 135 */       String str5 = Util.null2String(map1.get("newPoints"));
/*     */       
/* 137 */       int i = Util.getIntValue((String)map1.get("startDirection"), -1);
/* 138 */       int j = Util.getIntValue((String)map1.get("endDirection"), -1);
/* 139 */       Map map4 = (Map)map2.get("point");
/* 140 */       Map map5 = (Map)map3.get("point");
/* 141 */       int k = ((Integer)map4.get("x")).intValue();
/* 142 */       int m = ((Integer)map4.get("y")).intValue();
/* 143 */       int n = ((Integer)map5.get("x")).intValue();
/* 144 */       int i1 = ((Integer)map5.get("y")).intValue();
/*     */       
/* 146 */       int i2 = Util.getIntValue(Util.null2String(map2.get("width")));
/* 147 */       int i3 = Util.getIntValue(Util.null2String(map2.get("height")));
/* 148 */       int i4 = Util.getIntValue(Util.null2String(map3.get("width")));
/* 149 */       int i5 = Util.getIntValue(Util.null2String(map3.get("height")));
/*     */       
/* 151 */       double d1 = 0.0D;
/* 152 */       double d2 = 0.0D;
/* 153 */       double d3 = 0.0D;
/* 154 */       double d4 = 0.0D;
/*     */       
/* 156 */       ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 157 */       if (!"".equals(str5)) {
/* 158 */         String[] arrayOfString = str5.split(",");
/* 159 */         d1 = Util.getDoubleValue(arrayOfString[0]);
/* 160 */         d3 = Util.getDoubleValue(arrayOfString[1]);
/* 161 */         d2 = Util.getDoubleValue(arrayOfString[arrayOfString.length - 2]);
/* 162 */         d4 = Util.getDoubleValue(arrayOfString[arrayOfString.length - 1]);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */         
/*     */         byte b1;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */         
/* 204 */         for (b1 = 0; b1 < arrayOfString.length - 1; b1 += 2) {
/* 205 */           Point point = new Point(Util.getIntValue(arrayOfString[b1]), Util.getIntValue(arrayOfString[b1 + 1]));
/* 206 */           HashMap<Object, Object> hashMap3 = new HashMap<>();
/* 207 */           hashMap3.put("x", Double.valueOf(Util.getDoubleValue(arrayOfString[b1])));
/* 208 */           hashMap3.put("y", Double.valueOf(Util.getDoubleValue(arrayOfString[b1 + 1])));
/* 209 */           arrayList.add(hashMap3);
/*     */         } 
/*     */ 
/*     */ 
/*     */ 
/*     */         
/* 215 */         b1 = 0;
/* 216 */         boolean bool = false;
/* 217 */         if (!str2.equals(str3) && ((i == 90 && j == -90) || (i == -90 && j == 90))) {
/* 218 */           if (Math.abs(d1 - d2) <= 100.0D && Math.abs(m + i3 / 2 - i1 + i5 / 2) <= 18) {
/* 219 */             b1 = 1;
/* 220 */             bool = true;
/*     */           } 
/* 222 */         } else if (!str2.equals(str3) && ((i == 180 && j == 0) || (i == 0 && j == 180)) && 
/* 223 */           Math.abs(d3 - d4) <= 100.0D && Math.abs(k + i2 / 2 - n + i4 / 2) <= 18) {
/* 224 */           b1 = 1;
/*     */         } 
/*     */ 
/*     */         
/* 228 */         if (b1 != 0 && 
/* 229 */           d1 != 0.0D && d3 != 0.0D && d2 != 0.0D && d4 != 0.0D) {
/*     */           
/* 231 */           Map map6 = arrayList.get(arrayList.size() - 1);
/* 232 */           if (bool) {
/* 233 */             d3 = ((Double)map6.get("y")).doubleValue();
/* 234 */             d1 += 3.0D;
/*     */           } else {
/* 236 */             d1 = ((Double)map6.get("x")).doubleValue();
/* 237 */             d3 += 3.0D;
/*     */           } 
/*     */           
/* 240 */           arrayList.clear();
/* 241 */           HashMap<Object, Object> hashMap3 = new HashMap<>();
/* 242 */           hashMap3.put("x", Double.valueOf(d1));
/* 243 */           hashMap3.put("y", Double.valueOf(d3));
/* 244 */           HashMap<Object, Object> hashMap4 = new HashMap<>();
/* 245 */           hashMap4.put("x", Double.valueOf(d2));
/* 246 */           hashMap4.put("y", Double.valueOf(d4));
/* 247 */           arrayList.add(hashMap3);
/* 248 */           arrayList.add(hashMap4);
/* 249 */           arrayList.add(map6);
/*     */         } 
/*     */       } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 266 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 267 */       hashMap.put("id", map1.get("linkid"));
/* 268 */       hashMap.put("text", Util.toXmlText((String)map1.get("linkname")));
/*     */       
/* 270 */       hashMap.put("from", str2);
/* 271 */       hashMap.put("to", str3);
/* 272 */       hashMap.put("remindMsg", "");
/* 273 */       hashMap.put("isBuildCode", Boolean.valueOf(false));
/* 274 */       hashMap.put("isreject", map1.get("isreject"));
/* 275 */       hashMap.put("ismustpass", Integer.valueOf(0));
/* 276 */       hashMap.put("directionfrom", Integer.valueOf(0));
/* 277 */       hashMap.put("directionto", Integer.valueOf(0));
/* 278 */       hashMap.put("startDirection", Integer.valueOf(i));
/* 279 */       hashMap.put("endDirection", Integer.valueOf(j));
/* 280 */       hashMap.put("points", arrayList);
/* 281 */       hashMap.put("oldPoints", str4);
/* 282 */       hashMap.put("shapetype", "PolyLine");
/* 283 */       hashMap.put("zIndex", Integer.valueOf(0));
/* 284 */       hashMap.put("fromRelX", Integer.valueOf(0));
/* 285 */       hashMap.put("fromRelY", Integer.valueOf(0));
/* 286 */       hashMap.put("toRelX", Integer.valueOf(0));
/* 287 */       hashMap.put("toRelY", Integer.valueOf(0));
/* 288 */       hashMap.put("hasRole", Boolean.valueOf(false));
/* 289 */       hashMap.put("hasCondition", Boolean.valueOf(((Boolean)map1.get("hasCondition")).booleanValue()));
/* 290 */       hashMap.put("ispass", Boolean.valueOf(Boolean.parseBoolean(Util.null2String((String)map1.get("ispass")))));
/* 291 */       arrayList2.add(hashMap);
/*     */     } 
/* 293 */     RecordSet recordSet = new RecordSet();
/* 294 */     recordSet.execute("select * from workflow_groupinfo where workflowid=" + this.workflowid);
/* 295 */     while (recordSet.next()) {
/* 296 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 297 */       hashMap.put("id", Integer.valueOf(recordSet.getInt("id")));
/* 298 */       hashMap.put("workflowid", Integer.valueOf(recordSet.getInt("workflowid")));
/* 299 */       hashMap.put("groupName", Util.toXmlText(recordSet.getString("groupname")));
/* 300 */       hashMap.put("direction", Integer.valueOf(recordSet.getInt("direction")));
/* 301 */       hashMap.put("x", Double.valueOf(recordSet.getDouble("x")));
/* 302 */       hashMap.put("y", Double.valueOf(recordSet.getDouble("y")));
/* 303 */       hashMap.put("width", Double.valueOf(recordSet.getDouble("width")));
/* 304 */       hashMap.put("height", Double.valueOf(recordSet.getDouble("height")));
/* 305 */       hashMap.put("isNew", Boolean.valueOf(false));
/*     */       
/* 307 */       arrayList3.add(hashMap);
/*     */     } 
/* 309 */     return (Map)hashMap1;
/*     */   }
/*     */   
/*     */   public List<Map<String, Object>> parseResourceInfo(List<String> paramList) {
/* 313 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 314 */     String str1 = "_#WFSPSTR_OPTTP#_";
/* 315 */     String str2 = "->";
/* 316 */     RecordSet recordSet = new RecordSet();
/*     */     try {
/* 318 */       Iterator<String> iterator = paramList.iterator();
/* 319 */       while (iterator.hasNext()) {
/* 320 */         String str = Util.null2String(iterator.next());
/* 321 */         String[] arrayOfString1 = str.split(str2);
/* 322 */         String[] arrayOfString2 = arrayOfString1[0].split(str1);
/* 323 */         HashMap<Object, Object> hashMap = new HashMap<>();
/* 324 */         hashMap.put("type", Integer.valueOf(Util.getIntValue(arrayOfString2[0])));
/* 325 */         hashMap.put("id", Integer.valueOf(Util.getIntValue(arrayOfString2[1])));
/*     */         
/* 327 */         hashMap.put("name", arrayOfString2[2]);
/* 328 */         if (arrayOfString1.length > 1) {
/* 329 */           hashMap.put("hasAgent", Boolean.valueOf(true));
/* 330 */           arrayOfString2 = arrayOfString1[1].split(str1);
/* 331 */           HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 332 */           hashMap1.put("type", Integer.valueOf(Util.getIntValue(arrayOfString2[0])));
/* 333 */           hashMap1.put("id", Integer.valueOf(Util.getIntValue(arrayOfString2[1])));
/* 334 */           hashMap1.put("name", arrayOfString2[2]);
/* 335 */           hashMap.put("agent", hashMap1);
/*     */         } 
/*     */         
/* 338 */         arrayList.add(hashMap);
/*     */       }
/*     */     
/* 341 */     } catch (Exception exception) {
/* 342 */       exception.printStackTrace();
/*     */     } 
/* 344 */     return (List)arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int targetNodePosition(int paramInt1, int paramInt2, Point paramPoint1, Point paramPoint2) {
/* 354 */     byte b = -1;
/*     */     
/* 356 */     if (paramInt1 == paramInt2 || (paramPoint1.x == paramPoint2.x && paramPoint1.y == paramPoint2.y))
/*     */     {
/* 358 */       return 5;
/*     */     }
/*     */ 
/*     */     
/* 362 */     if (paramPoint1.x == paramPoint2.x) {
/*     */       
/* 364 */       if (paramPoint1.y > paramPoint2.y) {
/* 365 */         return 2;
/*     */       }
/*     */       
/* 368 */       if (paramPoint1.y < paramPoint2.y) {
/* 369 */         return 8;
/*     */       }
/*     */     } 
/*     */ 
/*     */     
/* 374 */     if (paramPoint1.y == paramPoint2.y) {
/*     */       
/* 376 */       if (paramPoint1.x > paramPoint2.x) {
/* 377 */         return 4;
/*     */       }
/*     */       
/* 380 */       if (paramPoint1.x < paramPoint2.x) {
/* 381 */         return 6;
/*     */       }
/*     */     } 
/*     */     
/* 385 */     if (paramPoint1.x > paramPoint2.x && paramPoint1.y > paramPoint2.y) {
/* 386 */       return 1;
/*     */     }
/*     */ 
/*     */     
/* 390 */     if (paramPoint1.x < paramPoint2.x && paramPoint1.y > paramPoint2.y) {
/* 391 */       return 3;
/*     */     }
/*     */ 
/*     */     
/* 395 */     if (paramPoint1.x > paramPoint2.x && paramPoint1.y < paramPoint2.y) {
/* 396 */       return 7;
/*     */     }
/*     */ 
/*     */     
/* 400 */     if (paramPoint1.x < paramPoint2.x && paramPoint1.y < paramPoint2.y) {
/* 401 */       return 9;
/*     */     }
/*     */     
/* 404 */     return b;
/*     */   }
/*     */   
/*     */   public int getRequestid() {
/* 408 */     return this.requestid;
/*     */   }
/*     */   
/*     */   public void setRequestid(int paramInt) {
/* 412 */     this.requestid = paramInt;
/*     */   }
/*     */   
/*     */   public int getWorkflowid() {
/* 416 */     return this.workflowid;
/*     */   }
/*     */   
/*     */   public void setWorkflowid(int paramInt) {
/* 420 */     this.workflowid = paramInt;
/*     */   }
/*     */   
/*     */   public int getFormid() {
/* 424 */     return this.formid;
/*     */   }
/*     */   
/*     */   public void setFormid(int paramInt) {
/* 428 */     this.formid = paramInt;
/*     */   }
/*     */   
/*     */   public int getIsbill() {
/* 432 */     return this.isbill;
/*     */   }
/*     */   
/*     */   public void setIsbill(int paramInt) {
/* 436 */     this.isbill = paramInt;
/*     */   }
/*     */   
/*     */   public User getUser() {
/* 440 */     return this.user;
/*     */   }
/*     */   
/*     */   public void setUser(User paramUser) {
/* 444 */     this.user = paramUser;
/*     */   }
/*     */   
/*     */   public String getWorkflowname() {
/* 448 */     return this.workflowname;
/*     */   }
/*     */   
/*     */   public void setWorkflowname(String paramString) {
/* 452 */     this.workflowname = paramString;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/layout/WorkflowPicDisplayData.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */