/*     */ package weaver.workflow.layout;
/*     */ 
/*     */ import java.awt.Color;
/*     */ import java.awt.Container;
/*     */ import java.awt.event.ActionEvent;
/*     */ import java.awt.event.ActionListener;
/*     */ import java.io.InputStream;
/*     */ import java.io.ObjectInputStream;
/*     */ import java.io.ObjectOutputStream;
/*     */ import java.io.OutputStream;
/*     */ import java.net.URL;
/*     */ import java.net.URLConnection;
/*     */ import javax.swing.BorderFactory;
/*     */ import javax.swing.JApplet;
/*     */ import javax.swing.JButton;
/*     */ import javax.swing.JOptionPane;
/*     */ import javax.swing.JToolBar;
/*     */ import javax.swing.border.Border;
/*     */ import netscape.javascript.JSObject;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class WorkflowEditor
/*     */   extends JApplet
/*     */   implements ActionListener
/*     */ {
/*  29 */   protected Border bdUncheck = BorderFactory.createEtchedBorder(1);
/*     */   
/*  31 */   protected Border bdCheck = BorderFactory.createLineBorder(Color.blue);
/*     */   
/*     */   protected JButton jbtnSave;
/*     */   
/*     */   protected JToolBar jtbarMain;
/*     */   
/*  37 */   protected String ACT_SAVE = "0";
/*     */   
/*  39 */   protected String actionRedirectToLogin = "";
/*     */ 
/*     */   
/*     */   protected WorkflowView view;
/*     */ 
/*     */   
/*     */   protected WorkflowController controller;
/*     */ 
/*     */   
/*     */   protected void initData() throws Exception {
/*  49 */     this.view = new WorkflowView();
/*  50 */     this.view.wf = downloadWorkflow(getParameter("downloadUrl"), getParameter("id"));
/*  51 */     this.controller = new WorkflowController(this.view);
/*  52 */     this.view.wf.checkAndAutosetLayout(10, 10, 40, 50);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   protected void initToolBar() {
/*  59 */     this.jbtnSave = new JButton("保存");
/*  60 */     this.jbtnSave.setActionCommand(this.ACT_SAVE);
/*  61 */     this.jbtnSave.addActionListener(this);
/*  62 */     this.jbtnSave.setBorder(this.bdUncheck);
/*     */     
/*  64 */     this.jtbarMain = new JToolBar();
/*  65 */     this.jtbarMain.add(this.jbtnSave);
/*  66 */     this.jtbarMain.setBorder(BorderFactory.createEtchedBorder());
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void init() {
/*     */     try {
/*  75 */       this.actionRedirectToLogin = getParameter("actionRedirectToLogin");
/*  76 */       initData();
/*  77 */       initToolBar();
/*  78 */     } catch (Exception e) {
/*     */       
/*  80 */       e.printStackTrace();
/*     */     } 
/*     */     
/*  83 */     Container contentPane = getContentPane();
/*  84 */     contentPane.add(this.jtbarMain, "North");
/*     */     
/*  86 */     this.view.setBorder(BorderFactory.createEtchedBorder());
/*  87 */     this.view.addMouseListener(this.controller);
/*  88 */     this.view.addMouseMotionListener(this.controller);
/*  89 */     contentPane.add(this.view);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void start() {}
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void actionPerformed(ActionEvent e) {
/* 101 */     if (e.getActionCommand().equals(this.ACT_SAVE)) {
/* 102 */       uploadWorkflow(getParameter("uploadUrl"), this.view.wf);
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   protected Workflow downloadWorkflow(String downloadUrl, String id) {
/*     */     try {
/* 114 */       if (id != null && !id.equals("")) {
/* 115 */         URL url = new URL(String.valueOf(downloadUrl) + "?id=" + id);
/*     */         
/* 117 */         URLConnection con = url.openConnection();
/* 118 */         con.setUseCaches(false);
/* 119 */         con.setDoInput(true);
/* 120 */         con.setDoOutput(false);
/* 121 */         InputStream in = con.getInputStream();
/* 122 */         ObjectInputStream objStream = new ObjectInputStream(in);
/* 123 */         Workflow result = new Workflow();
/* 124 */         objStream.close();
/* 125 */         if (result.needRedirect) {
/* 126 */           submitContainersForm(this.actionRedirectToLogin);
/*     */         }
/* 128 */         return result;
/*     */       } 
/* 130 */       return new Workflow();
/*     */     }
/* 132 */     catch (Exception e) {
/* 133 */       e.printStackTrace();
/* 134 */       return new Workflow();
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   protected void uploadWorkflow(String uploadUrl, Workflow wf) {
/*     */     try {
/* 145 */       URL url = new URL(uploadUrl);
/* 146 */       URLConnection con = url.openConnection();
/* 147 */       con.setUseCaches(false);
/* 148 */       con.setDoOutput(true);
/*     */       
/* 150 */       OutputStream out = con.getOutputStream();
/* 151 */       ObjectOutputStream objStream = new ObjectOutputStream(out);
/*     */       
/* 153 */       objStream.writeObject(wf);
/* 154 */       objStream.close();
/* 155 */       String length = Integer.toString(con.getContentLength());
/* 156 */       JOptionPane.showMessageDialog(null, "保存成功", "工作流程图", 1);
/* 157 */     } catch (Exception exception) {}
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   protected void submitContainersForm(String action) {
/* 167 */     if (action != null && !action.equals(""))
/*     */       try {
/* 169 */         JSObject win = JSObject.getWindow(this);
/* 170 */         JSObject doc = (JSObject)win.getMember("document");
/* 171 */         JSObject form1 = (JSObject)doc.getMember(action);
/* 172 */         form1.call("submit", null);
/* 173 */       } catch (Exception exception) {
/* 174 */         exception.printStackTrace();
/*     */       }  
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/layout/WorkflowEditor.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */