/*    */ package weaver.workflow.layout;
/*    */ 
/*    */ import java.awt.Color;
/*    */ import java.awt.Graphics;
/*    */ import javax.swing.JPanel;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class WorkflowView
/*    */   extends JPanel
/*    */ {
/*    */   protected Workflow wf;
/*    */   
/*    */   public WorkflowView() {
/* 27 */     setColor(Color.white);
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public void paintComponent(Graphics paramGraphics) {
/* 35 */     super.paintComponent(paramGraphics);
/* 36 */     paintTo(paramGraphics);
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public void paintTo(Graphics paramGraphics) {
/* 44 */     paintNodes(paramGraphics);
/* 45 */     paintLines(paramGraphics);
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public void paintNodes(Graphics paramGraphics) {
/* 54 */     for (byte b = 0; b < this.wf.nodes.size(); b++) {
/* 55 */       this.wf.getNode(b).drawSelf(paramGraphics);
/*    */     }
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public void paintLines(Graphics paramGraphics) {
/* 65 */     for (byte b = 0; b < this.wf.lines.size(); b++) {
/* 66 */       this.wf.getLine(b).drawSelf(paramGraphics);
/*    */     }
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public void setColor(Color paramColor) {
/* 75 */     setBackground(paramColor);
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public Color getColor() {
/* 83 */     return getBackground();
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public boolean isPointVisible(int paramInt1, int paramInt2) {
/* 93 */     if (paramInt1 > 0 && paramInt1 < getWidth() && paramInt2 > 0 && paramInt2 < getHeight()) {
/* 94 */       return true;
/*    */     }
/* 96 */     return false;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/layout/WorkflowView.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */