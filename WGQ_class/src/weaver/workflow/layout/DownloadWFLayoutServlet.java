/*     */ package weaver.workflow.layout;
/*     */ 
/*     */ import java.awt.Point;
/*     */ import java.io.IOException;
/*     */ import java.io.InputStream;
/*     */ import java.io.ObjectInputStream;
/*     */ import java.io.ObjectOutputStream;
/*     */ import java.io.OutputStream;
/*     */ import javax.servlet.ServletConfig;
/*     */ import javax.servlet.ServletException;
/*     */ import javax.servlet.ServletInputStream;
/*     */ import javax.servlet.ServletOutputStream;
/*     */ import javax.servlet.http.HttpServlet;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.hrm.HrmUserVarify;
/*     */ import weaver.hrm.User;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class DownloadWFLayoutServlet
/*     */   extends HttpServlet
/*     */ {
/*     */   public void init(ServletConfig config) throws ServletException {
/*  34 */     super.init(config);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String ch2China(String src, int languageid, boolean isForApplet) {
/*  45 */     return src;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static Workflow readWFLayoutFromDB(int id, int languageid, boolean isForApplet) {
/*  66 */     Workflow result = new Workflow();
/*  67 */     RecordSet rs = new RecordSet();
/*     */     
/*  69 */     rs.executeSql("select * from workflow_nodebase t1, workflow_flownode t2 where t2.workflowid = " + id + " and t1.id = t2.nodeid and (t1.IsFreeNode is null or t1.IsFreeNode !='1')");
/*  70 */     while (rs.next()) {
/*  71 */       WorkflowNode node = new WorkflowNode();
/*  72 */       node.id = rs.getInt("id");
/*  73 */       node.x = rs.getInt("drawxpos") - 60;
/*  74 */       node.y = rs.getInt("drawypos") - 40;
/*  75 */       node.name = ch2China(rs.getString("nodename"), languageid, isForApplet);
/*  76 */       node.nodeType = rs.getString("nodetype");
/*  77 */       result.addNode(node);
/*     */     } 
/*     */     
/*  80 */     rs.executeSql("select * from workflow_nodelink where wfrequestid is null and EXISTS(select 1 from workflow_nodebase b where workflow_nodelink.nodeid=b.id and (b.IsFreeNode is null or b.IsFreeNode !='1')) and EXISTS(select 1 from workflow_nodebase b where workflow_nodelink.destnodeid=b.id and (b.IsFreeNode is null or b.IsFreeNode !='1')) and workflowid = " + id);
/*  81 */     while (rs.next()) {
/*  82 */       WorkflowLine line = new WorkflowLine();
/*  83 */       line.id = rs.getInt("id");
/*  84 */       line.fromNodeId = rs.getInt("nodeid");
/*  85 */       line.toNodeId = rs.getInt("destnodeid");
/*  86 */       line.fromPointId = rs.getInt("directionfrom");
/*  87 */       line.toPointId = rs.getInt("directionto");
/*     */       
/*  89 */       for (int i = 1; i <= 5; i++) {
/*  90 */         int x = rs.getInt("x" + i);
/*  91 */         int y = rs.getInt("y" + i);
/*  92 */         if (x >= 0 && y >= 0) {
/*  93 */           line.controlPoints.add(new Point(x, y));
/*     */         }
/*     */       } 
/*  96 */       result.addLine(line);
/*     */     } 
/*     */     
/*  99 */     return result;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static Workflow readWFLayout(int id, int languageid, boolean isForApplet) {
/* 110 */     Workflow result = readWFLayoutFromDB(id, languageid, isForApplet);
/* 111 */     result.id = id;
/* 112 */     result.buildObjRef();
/* 113 */     result.buildTree();
/* 114 */     return result;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static void writeWFLayout(Workflow wf) {
/* 124 */     RecordSet rs = new RecordSet();
/*     */     int i;
/* 126 */     for (i = 0; i < wf.nodes.size(); i++) {
/* 127 */       WorkflowNode node = wf.getNode(i);
/* 128 */       String sql = "update workflow_nodebase ";
/* 129 */       sql = String.valueOf(sql) + "set drawxpos = " + (node.x + 60) + ", " + "drawypos = " + (node.y + 40) + " ";
/* 130 */       sql = String.valueOf(sql) + "where id = " + node.id;
/* 131 */       rs.executeSql(sql);
/*     */     } 
/*     */     
/* 134 */     for (i = 0; i < wf.lines.size(); i++) {
/* 135 */       WorkflowLine line = wf.getLine(i);
/* 136 */       String sql = "update workflow_nodelink ";
/* 137 */       sql = String.valueOf(sql) + "set directionfrom = " + line.fromPointId + ", " + "directionto = " + line.toPointId + ", ";
/* 138 */       for (int j = 0; j < WorkflowLine.getMaxCtrlPointCount(); j++) {
/* 139 */         if (j < line.getValidCtrlPointCount()) {
/* 140 */           Point p = line.getCtrlPoint(j);
/* 141 */           sql = String.valueOf(sql) + "x" + (j + 1) + " = " + p.x + ", y" + (j + 1) + " = " + p.y;
/* 142 */           if (j < WorkflowLine.getMaxCtrlPointCount() - 1) sql = String.valueOf(sql) + ", "; 
/*     */         } else {
/* 144 */           sql = String.valueOf(sql) + "x" + (j + 1) + " = -1, y" + (j + 1) + " = -1";
/* 145 */           if (j < WorkflowLine.getMaxCtrlPointCount() - 1) sql = String.valueOf(sql) + ", "; 
/*     */         } 
/*     */       } 
/* 148 */       sql = String.valueOf(sql) + " where id = " + line.id;
/* 149 */       rs.executeSql(sql);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
/* 162 */     User user = HrmUserVarify.getUser(request, response);
/* 163 */     if (user == null) {
/* 164 */       response.sendRedirect("/login/Login.jsp");
/*     */ 
/*     */       
/*     */       return;
/*     */     } 
/*     */     
/* 170 */     ServletOutputStream servletOutputStream = response.getOutputStream();
/* 171 */     ObjectOutputStream objStream = new ObjectOutputStream((OutputStream)servletOutputStream);
/* 172 */     String id = request.getParameter("id");
/*     */     
/*     */     try {
/*     */       Workflow wf;
/* 176 */       if (user == null) {
/* 177 */         wf = new Workflow();
/* 178 */         wf.needRedirect = true;
/*     */       } else {
/* 180 */         wf = readWFLayout(Integer.parseInt(id), user.getLanguage(), true);
/*     */       } 
/* 182 */       objStream.writeObject(wf);
/* 183 */     } catch (Exception e) {
/* 184 */       e.printStackTrace();
/*     */     } finally {
/* 186 */       objStream.close();
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
/* 200 */     User user = HrmUserVarify.getUser(request, response);
/* 201 */     if (user == null) {
/* 202 */       response.sendRedirect("/login/Login.jsp");
/*     */       return;
/*     */     } 
/* 205 */     ServletInputStream servletInputStream = request.getInputStream();
/* 206 */     ObjectInputStream objStream = new ObjectInputStream((InputStream)servletInputStream);
/*     */     try {
/* 208 */       Workflow wf = new Workflow();
/* 209 */       writeWFLayout(wf);
/* 210 */     } catch (Exception e) {
/* 211 */       e.printStackTrace();
/*     */     } finally {
/* 213 */       objStream.close();
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/layout/DownloadWFLayoutServlet.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */