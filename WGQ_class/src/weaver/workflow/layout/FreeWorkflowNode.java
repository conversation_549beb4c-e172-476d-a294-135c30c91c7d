/*     */ package weaver.workflow.layout;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class FreeWorkflowNode
/*     */ {
/*     */   private static final int yInit = 80;
/*     */   private static final int xInit = 100;
/*     */   private static final int width = 120;
/*     */   private static final int height = 80;
/*     */   private static final int xStep = 160;
/*     */   private static final int yStep = 160;
/*     */   private static final int rowCapacity = 5;
/*     */   
/*     */   public static Map<String, Map<String, Integer>> getNodePositions(String paramString1, String paramString2) {
/*  30 */     List<String> list = getOrderedNodes(paramString1, paramString2);
/*  31 */     Map<String, Map<String, Integer>> map = getNodePoints(list);
/*     */     
/*  33 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  34 */     for (byte b = 0; b < list.size(); b++) {
/*  35 */       String str = list.get(b);
/*  36 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/*     */       
/*  38 */       hashMap1.put("width", Integer.valueOf(120));
/*  39 */       hashMap1.put("height", Integer.valueOf(80));
/*  40 */       hashMap1.put("x", Integer.valueOf(100 + 160 * ((Integer)((Map)map.get(str)).get("x")).intValue()));
/*  41 */       hashMap1.put("y", Integer.valueOf(80 + 160 * ((Integer)((Map)map.get(str)).get("y")).intValue()));
/*     */       
/*  43 */       hashMap.put(list.get(b), hashMap1);
/*     */     } 
/*  45 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static void updateNodeLinkDirection(String paramString1, String paramString2) {
/*  54 */     List<String> list = getOrderedNodes(paramString1, paramString2);
/*  55 */     Map<String, Map<String, Integer>> map = getNodePoints(list);
/*     */ 
/*     */ 
/*     */     
/*  59 */     if (list.size() < 3) {
/*     */       return;
/*     */     }
/*     */     
/*  63 */     RecordSet recordSet = new RecordSet();
/*  64 */     for (byte b = 0; b < list.size() - 1; b++) {
/*  65 */       String str4 = list.get(b);
/*  66 */       String str5 = list.get(b + 1);
/*     */       
/*  68 */       Map map1 = map.get(str4);
/*  69 */       Map map2 = map.get(str5);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/*  76 */       short s1 = 0;
/*  77 */       short s2 = 0;
/*  78 */       if (((Integer)map1.get("x")).equals(map2.get("x"))) {
/*  79 */         int i = ((Integer)map2.get("y")).intValue() - ((Integer)map1.get("y")).intValue();
/*  80 */         if (i > 0) {
/*  81 */           s1 = 0;
/*  82 */           s2 = 180;
/*  83 */         } else if (i < 0) {
/*  84 */           s1 = 180;
/*  85 */           s2 = 0;
/*     */         } 
/*  87 */       } else if (((Integer)map1.get("y")).equals(map2.get("y"))) {
/*  88 */         int i = ((Integer)map2.get("x")).intValue() - ((Integer)map1.get("x")).intValue();
/*  89 */         if (i > 0) {
/*  90 */           s1 = -90;
/*  91 */           s2 = 90;
/*  92 */         } else if (i < 0) {
/*  93 */           s1 = 90;
/*  94 */           s2 = -90;
/*     */         } 
/*     */       } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 103 */       String str6 = "update workflow_nodelink set startDirection=" + s1 + ",endDirection=" + s2 + " where workflowid=" + paramString1 + " and nodeid=" + str4 + " and destnodeid=" + str5;
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 108 */       recordSet.executeSql(str6);
/*     */     } 
/*     */     
/* 111 */     String str1 = list.get(0);
/* 112 */     String str2 = list.get(list.size() - 1);
/*     */ 
/*     */     
/* 115 */     String str3 = "update workflow_nodelink set startDirection=90,endDirection=0 where workflowid=" + paramString1 + " and nodeid=" + str1 + " and destnodeid=" + str2;
/*     */     
/* 117 */     recordSet.executeSql(str3);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static Map<String, Map<String, Integer>> getNodePoints(List<String> paramList) {
/* 126 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 127 */     for (byte b = 0; b < paramList.size(); b++) {
/* 128 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/*     */       
/* 130 */       int i = b / 5;
/* 131 */       int j = (i % 2 == 0) ? (b % 5) : (4 - b % 5);
/*     */       
/* 133 */       hashMap1.put("x", Integer.valueOf(j));
/* 134 */       hashMap1.put("y", Integer.valueOf(i));
/*     */       
/* 136 */       hashMap.put(paramList.get(b), hashMap1);
/*     */     } 
/* 138 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static List<String> getOrderedNodes(String paramString1, String paramString2) {
/* 149 */     String str1 = "select wf.nodeid,wf.nodetype from workflow_flownode wf,workflow_nodebase wn where (wn.isfreenode is null or wn.isfreenode !='1') \tand wf.nodeid = wn.id and wf.workflowid = " + paramString1 + " union select id nodeid, '-1' nodetype from workflow_nodebase where requestid = " + paramString2;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 155 */     RecordSet recordSet = new RecordSet();
/* 156 */     recordSet.executeSql(str1);
/*     */     
/* 158 */     String str2 = "0";
/* 159 */     String str3 = "0";
/* 160 */     byte b = 0;
/* 161 */     String str4 = new String();
/*     */     
/* 163 */     while (recordSet.next()) {
/* 164 */       String str6 = recordSet.getString("nodeid");
/* 165 */       String str7 = recordSet.getString("nodetype");
/*     */       
/* 167 */       if ("0".equals(str7)) {
/* 168 */         str2 = str6;
/* 169 */       } else if ("3".equals(str7)) {
/* 170 */         str3 = str6;
/*     */       } 
/*     */       
/* 173 */       str4 = str4 + str6 + ",";
/* 174 */       b++;
/*     */     } 
/*     */     
/* 177 */     if (str4.length() > 0) {
/* 178 */       str4 = str4.substring(0, str4.length() - 1);
/*     */     }
/*     */ 
/*     */     
/* 182 */     str1 = "select nodeid from_node, destnodeid to_node from workflow_nodelink where nodeid in (" + str4 + ") and destnodeid  in (" + str4 + ")";
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 187 */     if (b > 2) {
/* 188 */       str1 = str1 + " and id not in( select id from workflow_nodelink where nodeid = " + str2 + " and destnodeid = " + str3 + ")";
/*     */     }
/*     */ 
/*     */     
/* 192 */     recordSet.executeSql(str1);
/*     */     
/* 194 */     ArrayList<String> arrayList = new ArrayList(b);
/* 195 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 196 */     while (recordSet.next()) {
/* 197 */       hashMap.put(recordSet.getString("from_node"), recordSet.getString("to_node"));
/*     */     }
/*     */ 
/*     */     
/* 201 */     arrayList.add(str2);
/*     */     
/* 203 */     String str5 = str2;
/*     */     
/* 205 */     while (str5 != null && !str5.equals(str3)) {
/* 206 */       String str = (String)hashMap.get(str5);
/* 207 */       arrayList.add(str);
/*     */       
/* 209 */       str5 = str;
/*     */     } 
/* 211 */     return arrayList;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/layout/FreeWorkflowNode.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */