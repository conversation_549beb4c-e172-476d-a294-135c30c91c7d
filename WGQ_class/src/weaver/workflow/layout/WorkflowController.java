/*     */ package weaver.workflow.layout;
/*     */ 
/*     */ import java.awt.Point;
/*     */ import java.awt.event.MouseEvent;
/*     */ import java.awt.event.MouseListener;
/*     */ import java.awt.event.MouseMotionListener;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class WorkflowController
/*     */   implements MouseListener, MouseMotionListener
/*     */ {
/*     */   protected Workflow data;
/*     */   protected WorkflowView view;
/*  29 */   protected Object selected = null;
/*     */   
/*     */   protected int xOff;
/*     */   protected int yOff;
/*  33 */   protected int controlPointIndex = -1;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public WorkflowController(WorkflowView paramWorkflowView) {
/*  39 */     this.view = paramWorkflowView;
/*  40 */     this.data = this.view.wf;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void mouseClicked(MouseEvent paramMouseEvent) {
/*  48 */     if (paramMouseEvent.getModifiers() == 16 && paramMouseEvent.getClickCount() == 2) {
/*     */       
/*  50 */       for (byte b = 0; b < this.data.lines.size(); b++) {
/*  51 */         WorkflowLine workflowLine = this.data.getLine(b);
/*  52 */         int i = workflowLine.searchCtrlPoint(paramMouseEvent.getX(), paramMouseEvent.getY());
/*  53 */         if (i >= 0 && i < workflowLine.getValidCtrlPointCount()) {
/*  54 */           workflowLine.deleteCtrlPoint(i);
/*  55 */           i = -1;
/*  56 */           this.view.update(this.view.getGraphics());
/*     */           return;
/*     */         } 
/*     */       } 
/*  60 */       this.controlPointIndex = -1;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void mouseEntered(MouseEvent paramMouseEvent) {}
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void mouseExited(MouseEvent paramMouseEvent) {}
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void mousePressed(MouseEvent paramMouseEvent) {
/*  83 */     if (paramMouseEvent.getModifiers() != 16) {
/*     */       return;
/*     */     }
/*     */     
/*     */     byte b;
/*     */     
/*  89 */     for (b = 0; b < this.data.lines.size(); b++) {
/*  90 */       WorkflowLine workflowLine = this.data.getLine(b);
/*  91 */       this.controlPointIndex = workflowLine.searchCtrlPoint(paramMouseEvent.getX(), paramMouseEvent.getY());
/*  92 */       if (this.controlPointIndex != -1) {
/*  93 */         this.selected = workflowLine;
/*  94 */         Point point = workflowLine.getCtrlPoint(this.controlPointIndex);
/*  95 */         this.xOff = paramMouseEvent.getX() - point.x;
/*  96 */         this.yOff = paramMouseEvent.getY() - point.y;
/*     */         
/*     */         return;
/*     */       } 
/*     */     } 
/* 101 */     for (b = 0; b < this.data.lines.size(); b++) {
/* 102 */       WorkflowLine workflowLine = this.data.getLine(b);
/* 103 */       int i = workflowLine.isHited(paramMouseEvent.getX(), paramMouseEvent.getY());
/* 104 */       if (i >= 0) {
/* 105 */         if (workflowLine.getValidCtrlPointCount() < WorkflowLine.getMaxCtrlPointCount()) {
/*     */           
/* 107 */           this.selected = workflowLine;
/* 108 */           Point point = new Point(paramMouseEvent.getX(), paramMouseEvent.getY());
/* 109 */           workflowLine.controlPoints.add(i, point);
/* 110 */           this.controlPointIndex = i;
/* 111 */           this.xOff = 0;
/* 112 */           this.yOff = 0;
/*     */           
/*     */           return;
/*     */         } 
/* 116 */         this.controlPointIndex = workflowLine.searchNearestCtrlPoint(paramMouseEvent.getX(), paramMouseEvent.getY());
/* 117 */         if (this.controlPointIndex != -1) {
/* 118 */           this.selected = workflowLine;
/* 119 */           Point point = workflowLine.getCtrlPoint(i);
/*     */ 
/*     */           
/* 122 */           this.xOff = 0;
/* 123 */           this.yOff = 0;
/*     */           return;
/*     */         } 
/* 126 */         this.selected = null;
/*     */       } 
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/* 132 */     for (b = 0; b < this.data.nodes.size(); b++) {
/* 133 */       WorkflowNode workflowNode = this.data.getNode(b);
/* 134 */       if (workflowNode.isOnMe(paramMouseEvent.getX(), paramMouseEvent.getY())) {
/* 135 */         this.selected = workflowNode;
/* 136 */         this.xOff = paramMouseEvent.getX() - workflowNode.x;
/* 137 */         this.yOff = paramMouseEvent.getY() - workflowNode.y;
/*     */         return;
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void mouseReleased(MouseEvent paramMouseEvent) {
/* 148 */     if (paramMouseEvent.getModifiers() != 16) {
/*     */       return;
/*     */     }
/* 151 */     this.selected = null;
/* 152 */     this.controlPointIndex = -1;
/* 153 */     this.xOff = 0;
/* 154 */     this.yOff = 0;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void mouseDragged(MouseEvent paramMouseEvent) {
/* 162 */     if (paramMouseEvent.getModifiers() != 16 || this.selected == null) {
/*     */       return;
/*     */     }
/* 165 */     if (!this.view.isPointVisible(paramMouseEvent.getX() - this.xOff, paramMouseEvent.getY() - this.yOff)) {
/*     */       return;
/*     */     }
/*     */     
/* 169 */     if (this.selected instanceof WorkflowNode) {
/* 170 */       ((WorkflowNode)this.selected).x = paramMouseEvent.getX() - this.xOff;
/* 171 */       ((WorkflowNode)this.selected).y = paramMouseEvent.getY() - this.yOff;
/* 172 */     } else if (this.selected instanceof WorkflowLine && this.controlPointIndex != -1) {
/* 173 */       ((WorkflowLine)this.selected).moveCtrlPoint(this.controlPointIndex, paramMouseEvent.getX() - this.xOff, paramMouseEvent.getY() - this.yOff);
/*     */     } 
/*     */     
/* 176 */     this.view.update(this.view.getGraphics());
/*     */   }
/*     */   
/*     */   public void mouseMoved(MouseEvent paramMouseEvent) {}
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/layout/WorkflowController.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */