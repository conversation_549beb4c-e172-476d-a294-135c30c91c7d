/*    */ package weaver.workflow.layout;
/*    */ 
/*    */ import java.util.regex.Matcher;
/*    */ import java.util.regex.Pattern;
/*    */ import javax.servlet.http.HttpServletRequest;
/*    */ import weaver.general.BaseBean;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class WorkflowPicDisplayUtil
/*    */ {
/* 13 */   private static int chromeVersion = 0;
/*    */   
/*    */   static {
/* 16 */     init();
/*    */   }
/*    */   
/*    */   public static void init() {
/* 20 */     BaseBean baseBean = new BaseBean();
/* 21 */     chromeVersion = 1;
/*    */   }
/*    */ 
/*    */   
/*    */   public static boolean isHVChrome(HttpServletRequest paramHttpServletRequest) {
/* 26 */     return true;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public static boolean isHVChrome(String paramString) {
/* 38 */     if (paramString == null || paramString.indexOf("Trident") != -1) {
/* 39 */       return false;
/*    */     }
/*    */     
/* 42 */     if (paramString.indexOf("Chrome") == -1) {
/* 43 */       return false;
/*    */     }
/*    */     
/* 46 */     String str = "Chrome/(\\d+\\.\\d+)";
/* 47 */     Pattern pattern = Pattern.compile(str);
/* 48 */     Matcher matcher = pattern.matcher(paramString);
/* 49 */     if (matcher.find()) {
/*    */       try {
/* 51 */         double d = Double.parseDouble(matcher.group(1));
/* 52 */         if (d >= chromeVersion) {
/* 53 */           return true;
/*    */         }
/* 55 */       } catch (Exception exception) {}
/*    */     }
/*    */ 
/*    */     
/* 59 */     return false;
/*    */   }
/*    */   
/*    */   public int getChromeVersion() {
/* 63 */     return chromeVersion;
/*    */   }
/*    */   
/*    */   public void setChromeVersion(int paramInt) {
/* 67 */     this; chromeVersion = paramInt;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/layout/WorkflowPicDisplayUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */