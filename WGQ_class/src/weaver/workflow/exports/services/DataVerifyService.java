/*    */ package weaver.workflow.exports.services;
/*    */ 
/*    */ import sun.misc.BASE64Encoder;
/*    */ import weaver.general.Util;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class DataVerifyService
/*    */ {
/*    */   public static String getFileToBase64(String paramString) {
/* 19 */     String str = "";
/*    */     
/* 21 */     BASE64Encoder bASE64Encoder = new BASE64Encoder();
/*    */     
/* 23 */     str = Util.StringReplace(bASE64Encoder.encode(paramString.getBytes()), " ", "");
/*    */     
/* 25 */     return str;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/exports/services/DataVerifyService.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */