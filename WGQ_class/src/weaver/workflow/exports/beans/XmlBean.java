/*    */ package weaver.workflow.exports.beans;
/*    */ 
/*    */ import java.util.List;
/*    */ import org.jdom.Element;
/*    */ 
/*    */ 
/*    */ public class XmlBean
/*    */ {
/*    */   private Element element;
/*    */   private boolean needelement;
/*    */   private List childs;
/*    */   private String eid;
/*    */   private String ename;
/*    */   private String rootid;
/*    */   private String parentid;
/*    */   private String tablename;
/*    */   
/*    */   public Element getElement() {
/* 19 */     return this.element;
/*    */   }
/*    */   
/*    */   public void setElement(Element paramElement) {
/* 23 */     this.element = paramElement;
/*    */   }
/*    */   
/*    */   public String getEid() {
/* 27 */     return this.eid;
/*    */   }
/*    */   
/*    */   public void setEid(String paramString) {
/* 31 */     this.eid = paramString;
/*    */   }
/*    */   
/*    */   public String getParentid() {
/* 35 */     return this.parentid;
/*    */   }
/*    */   
/*    */   public void setParentid(String paramString) {
/* 39 */     this.parentid = paramString;
/*    */   }
/*    */   
/*    */   public String getTablename() {
/* 43 */     return this.tablename;
/*    */   }
/*    */   
/*    */   public void setTablename(String paramString) {
/* 47 */     this.tablename = paramString;
/*    */   }
/*    */   
/*    */   public String getRootid() {
/* 51 */     return this.rootid;
/*    */   }
/*    */   
/*    */   public void setRootid(String paramString) {
/* 55 */     this.rootid = paramString;
/*    */   }
/*    */   
/*    */   public String getEname() {
/* 59 */     return this.ename;
/*    */   }
/*    */   
/*    */   public void setEname(String paramString) {
/* 63 */     this.ename = paramString;
/*    */   }
/*    */   
/*    */   public boolean isNeedelement() {
/* 67 */     return this.needelement;
/*    */   }
/*    */   
/*    */   public void setNeedelement(boolean paramBoolean) {
/* 71 */     this.needelement = paramBoolean;
/*    */   }
/*    */   
/*    */   public List getChilds() {
/* 75 */     return this.childs;
/*    */   }
/*    */   
/*    */   public void setChilds(List paramList) {
/* 79 */     this.childs = paramList;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/exports/beans/XmlBean.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */