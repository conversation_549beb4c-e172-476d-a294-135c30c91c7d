/*     */ package weaver.workflow.exports.datas;
/*     */ 
/*     */ import java.io.BufferedReader;
/*     */ import java.io.File;
/*     */ import java.io.FileReader;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import java.util.TreeMap;
/*     */ import oracle.sql.CLOB;
/*     */ import weaver.conn.ConnStatement;
/*     */ import weaver.general.Util;
/*     */ import weaver.workflow.exceldesign.ExcelLayoutManager;
/*     */ import weaver.workflow.exports.services.DataVerifyService;
/*     */ import weaver.workflow.request.WorkflowOperator;
/*     */ 
/*     */ 
/*     */ 
/*     */ public class DataCenter
/*     */ {
/*  22 */   private Map dataMap = new TreeMap<>();
/*  23 */   private Map metaMap = new HashMap<>();
/*  24 */   private List fieldLabelIds = new ArrayList();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void getWorkflowDataBySql(String paramString1, String paramString2) {
/*  39 */     ConnStatement connStatement = new ConnStatement();
/*  40 */     this.dataMap.clear();
/*  41 */     this.metaMap.clear();
/*     */     
/*     */     try {
/*  44 */       if (paramString2.equals("groupdetail"))
/*     */       {
/*  46 */         if (!paramString1.equals(""))
/*     */         {
/*  48 */           byte b = 0;
/*  49 */           Object object1 = null;
/*  50 */           Object object2 = null;
/*  51 */           String str = "";
/*  52 */           boolean bool = false;
/*  53 */           Object object3 = null;
/*  54 */           connStatement.setStatementSql(paramString1);
/*  55 */           connStatement.executeQuery();
/*  56 */           while (connStatement.next())
/*     */           {
/*  58 */             HashMap<Object, Object> hashMap = new HashMap<>();
/*  59 */             ArrayList arrayList = new ArrayList();
/*  60 */             int i = connStatement.getColumnCount();
/*  61 */             for (byte b1 = 1; b1 <= i; b1++) {
/*     */               
/*  63 */               String str1 = connStatement.getColumnName(b1).toLowerCase();
/*  64 */               this.metaMap.put(str1, connStatement.getColumnTypeName(b1).toLowerCase());
/*     */               
/*  66 */               if (str1.equals("objid")) {
/*  67 */                 String str2 = Util.null2String(connStatement.getString("type"));
/*  68 */                 String str3 = Util.null2String(connStatement.getString("id"));
/*  69 */                 if (str2.equals("3")) {
/*  70 */                   WorkflowOperator workflowOperator = new WorkflowOperator();
/*  71 */                   String str4 = WorkflowOperator.getHrmOperator(str3);
/*  72 */                   hashMap.put("objid", str4);
/*     */                 } else {
/*  74 */                   hashMap.put("objid", connStatement.getString(str1));
/*     */                 } 
/*     */               } else {
/*     */                 
/*  78 */                 hashMap.put(str1, connStatement.getString(str1));
/*     */               } 
/*     */             } 
/*     */             
/*  82 */             this.dataMap.put("" + b, hashMap);
/*  83 */             b++;
/*     */           
/*     */           }
/*     */         
/*     */         }
/*     */       
/*     */       }
/*     */     }
/*  91 */     catch (Exception exception) {
/*     */ 
/*     */       
/*  94 */       exception.printStackTrace();
/*     */     }
/*     */     finally {
/*     */       
/*  98 */       connStatement.close();
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void getWorkflowDataBySql(String paramString) {
/* 109 */     ConnStatement connStatement = new ConnStatement();
/* 110 */     this.dataMap.clear();
/* 111 */     this.metaMap.clear();
/*     */     
/*     */     try {
/* 114 */       if (!paramString.equals("")) {
/*     */         
/* 116 */         byte b = 0;
/* 117 */         FileReader fileReader = null;
/* 118 */         File file = null;
/* 119 */         String str = "";
/* 120 */         int i = 0;
/* 121 */         char[] arrayOfChar = null;
/* 122 */         connStatement.setStatementSql(paramString);
/* 123 */         connStatement.executeQuery();
/* 124 */         while (connStatement.next())
/*     */         {
/* 126 */           HashMap<Object, Object> hashMap = new HashMap<>();
/* 127 */           ArrayList arrayList = new ArrayList();
/* 128 */           int j = connStatement.getColumnCount();
/* 129 */           for (byte b1 = 1; b1 <= j; b1++) {
/*     */ 
/*     */             
/* 132 */             String str1 = connStatement.getColumnName(b1).toLowerCase();
/* 133 */             this.metaMap.put(str1, connStatement.getColumnTypeName(b1).toLowerCase());
/*     */             
/* 135 */             if (str1.equals("modedesc")) {
/* 136 */               String str2 = "";
/* 137 */               if (connStatement.getDBType().equals("oracle") && !Util.null2String(connStatement.getOrgindbtype()).equals("dm") && 
/* 138 */                 !Util.null2String(connStatement.getOrgindbtype()).equals("st") && !"jc".equalsIgnoreCase(connStatement.getOrgindbtype())) {
/* 139 */                 CLOB cLOB = connStatement.getClob(str1);
/* 140 */                 String str3 = "";
/* 141 */                 StringBuffer stringBuffer = new StringBuffer("");
/* 142 */                 BufferedReader bufferedReader = new BufferedReader(cLOB.getCharacterStream());
/* 143 */                 while ((str3 = bufferedReader.readLine()) != null)
/* 144 */                   stringBuffer = stringBuffer.append(str3); 
/* 145 */                 bufferedReader.close();
/* 146 */                 str2 = stringBuffer.toString();
/*     */               } else {
/* 148 */                 str2 = connStatement.getString(str1);
/*     */               } 
/* 150 */               hashMap.put(str1, DataVerifyService.getFileToBase64(str2));
/* 151 */             } else if (str1.equals("syspath")) {
/*     */               try {
/* 153 */                 int k = Util.getIntValue(connStatement.getString("version"));
/* 154 */                 if (k <= 1) {
/* 155 */                   file = new File(connStatement.getString(str1));
/* 156 */                   if (file.exists()) {
/* 157 */                     i = (int)file.length();
/* 158 */                     arrayOfChar = new char[i];
/* 159 */                     fileReader = new FileReader(file);
/* 160 */                     fileReader.read(arrayOfChar);
/* 161 */                     fileReader.close();
/* 162 */                     str = new String(arrayOfChar);
/*     */                   }
/*     */                 
/* 165 */                 } else if (Util.getIntValue(connStatement.getString("isactive")) == 1) {
/* 166 */                   str = (new ExcelLayoutManager()).convertLayoutJsonToHtmlSource(Util.getIntValue(connStatement.getString("id")));
/*     */                 } 
/* 168 */               } catch (Exception exception) {
/* 169 */                 exception.printStackTrace();
/*     */               } 
/*     */               
/* 172 */               hashMap.put(str1, connStatement.getString(str1));
/*     */             }
/*     */             else {
/*     */               
/* 176 */               if ((str1.equals("namelabel") || str1.equals("fieldlabel")) && 
/* 177 */                 this.fieldLabelIds.indexOf(connStatement.getString(str1)) < 0) {
/* 178 */                 this.fieldLabelIds.add(connStatement.getString(str1));
/*     */               }
/*     */               
/* 181 */               hashMap.put(str1, connStatement.getString(str1));
/*     */             } 
/*     */           } 
/* 184 */           this.dataMap.put("" + b, hashMap);
/* 185 */           b++;
/*     */         }
/*     */       
/*     */       } 
/* 189 */     } catch (Exception exception) {
/*     */ 
/*     */       
/* 192 */       exception.printStackTrace();
/*     */     }
/*     */     finally {
/*     */       
/* 196 */       connStatement.close();
/*     */     } 
/*     */   }
/*     */   
/*     */   private String getDBFieldType(String paramString1, String paramString2) {
/* 201 */     String str = "";
/* 202 */     ConnStatement connStatement = new ConnStatement();
/*     */     
/*     */     try {
/* 205 */       StringBuffer stringBuffer = new StringBuffer();
/* 206 */       stringBuffer.append("select t.name as coltype, c.prec as collength, m.text as coldefault ");
/* 207 */       stringBuffer.append("from syscolumns c ");
/* 208 */       stringBuffer.append("inner join systypes t on c.xusertype = t.xusertype ");
/* 209 */       stringBuffer.append("left join sysproperties p on c.id = p.id ");
/* 210 */       stringBuffer.append("                      and c.colid = p.smallid ");
/* 211 */       stringBuffer.append("left join syscomments m on c.cdefault = m.id ");
/* 212 */       stringBuffer.append("where objectproperty(c.id, 'IsUserTable') = 1 ");
/* 213 */       stringBuffer.append("and object_name(c.id) = ? ");
/* 214 */       stringBuffer.append("and c.name = ?");
/* 215 */       connStatement.setStatementSql(stringBuffer.toString());
/* 216 */       connStatement.setString(1, paramString1);
/* 217 */       connStatement.setString(2, paramString2);
/* 218 */       connStatement.executeQuery();
/* 219 */       while (connStatement.next())
/*     */       {
/* 221 */         str = connStatement.getString("coltype") + "(" + connStatement.getString("collength") + ")";
/*     */       }
/*     */     }
/* 224 */     catch (Exception exception) {
/*     */       
/* 226 */       exception.printStackTrace();
/*     */     }
/*     */     finally {
/*     */       
/* 230 */       connStatement.close();
/*     */     } 
/* 232 */     return str;
/*     */   }
/*     */   
/*     */   public Map getDataMap() {
/* 236 */     return this.dataMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map getMetaMap() {
/* 241 */     return this.metaMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public List getFieldLabelIds() {
/* 246 */     return this.fieldLabelIds;
/*     */   }
/*     */ 
/*     */   
/*     */   public void setFieldLabelIds(List paramList) {
/* 251 */     this.fieldLabelIds = paramList;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/exports/datas/DataCenter.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */