/*    */ package weaver.workflow.request;
/*    */ 
/*    */ import java.util.List;
/*    */ import weaver.general.Util;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class DiffWfTriggerSetting
/*    */   extends WfTriggerSetting
/*    */ {
/*    */   private String diffFieldId;
/*    */   private String diffFieldType;
/*    */   private String diffFiledValue;
/*    */   private String innerChange;
/*    */   private String autoSend;
/*    */   private String autoReceive;
/*    */   private String conditioncn;
/*    */   
/*    */   public String getConditioncn() {
/* 30 */     return this.conditioncn;
/*    */   }
/*    */   
/*    */   public void setConditioncn(String paramString) {
/* 34 */     this.conditioncn = paramString;
/*    */   }
/*    */   
/*    */   public String getInnerChange() {
/* 38 */     return this.innerChange;
/*    */   }
/*    */   
/*    */   public void setInnerChange(String paramString) {
/* 42 */     this.innerChange = paramString;
/*    */   }
/*    */   
/*    */   public String getAutoSend() {
/* 46 */     return this.autoSend;
/*    */   }
/*    */   
/*    */   public void setAutoSend(String paramString) {
/* 50 */     this.autoSend = paramString;
/*    */   }
/*    */   
/*    */   public String getAutoReceive() {
/* 54 */     return this.autoReceive;
/*    */   }
/*    */   
/*    */   public void setAutoReceive(String paramString) {
/* 58 */     this.autoReceive = paramString;
/*    */   }
/*    */   
/*    */   public String getDiffFieldId() {
/* 62 */     return this.diffFieldId;
/*    */   }
/*    */   
/*    */   public void setDiffFieldId(String paramString) {
/* 66 */     this.diffFieldId = paramString;
/*    */   }
/*    */   
/*    */   public String getDiffFieldType() {
/* 70 */     return this.diffFieldType;
/*    */   }
/*    */   
/*    */   public void setDiffFieldType(String paramString) {
/* 74 */     this.diffFieldType = paramString;
/*    */   }
/*    */   
/*    */   public String getDiffFiledValue() {
/* 78 */     return this.diffFiledValue;
/*    */   }
/*    */   
/*    */   public void setDiffFiledValue(String paramString) {
/* 82 */     this.diffFiledValue = paramString;
/*    */   }
/*    */   
/*    */   public List<String> getDiffFieldValueList() {
/* 86 */     return Util.TokenizerString(this.diffFiledValue, ",");
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/DiffWfTriggerSetting.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */