/*    */ package weaver.workflow.request;
/*    */ 
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.crm.Maint.CustomerInfoComInfo;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.resource.ResourceComInfo;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class RequestBrowser
/*    */ {
/*    */   public String getWfNewLink(String paramString1, String paramString2) {
/* 16 */     String[] arrayOfString = Util.TokenizerString2(paramString2, "+");
/* 17 */     String str1 = Util.null2String(arrayOfString[0]);
/* 18 */     String str2 = "";
/*    */     
/* 20 */     int i = Util.getIntValue(Util.null2String(arrayOfString[1]), 7);
/* 21 */     int j = 0;
/* 22 */     int k = 0;
/*    */     
/* 24 */     RecordSet recordSet = new RecordSet();
/*    */     
/* 26 */     recordSet.executeSql("select  workflowid from workflow_currentoperator where requestid =" + str1);
/* 27 */     if (recordSet.next())
/* 28 */       str2 = recordSet.getString("workflowid"); 
/* 29 */     recordSet.execute("select formid,isbill from workflow_base where id=" + str2);
/* 30 */     if (recordSet.next()) {
/*    */       
/* 32 */       k = recordSet.getInt(1);
/* 33 */       j = recordSet.getInt(2);
/*    */     } 
/*    */     
/* 36 */     MailAndMessage mailAndMessage = new MailAndMessage();
/* 37 */     String str3 = mailAndMessage.getTitle(Util.getIntValue(str1, -1), Util.getIntValue(str2, -1), k, i, j);
/* 38 */     if (!str3.equals("")) {
/* 39 */       paramString1 = paramString1 + "<font>（" + str3 + "）</font>";
/*    */     }
/* 41 */     return paramString1;
/*    */   }
/*    */   
/*    */   public String getWfCreaterName(String paramString1, String paramString2) {
/* 45 */     String[] arrayOfString = Util.TokenizerString2(paramString2, "+");
/* 46 */     String str1 = Util.null2String(arrayOfString[0]);
/* 47 */     String str2 = "";
/* 48 */     int i = Util.getIntValue(Util.null2String(arrayOfString[1]), 7);
/*    */     try {
/* 50 */       if ("0".equals(str1)) {
/* 51 */         ResourceComInfo resourceComInfo = new ResourceComInfo();
/* 52 */         str2 = Util.toScreen(resourceComInfo.getResourcename(paramString1), i);
/*    */       } else {
/* 54 */         CustomerInfoComInfo customerInfoComInfo = new CustomerInfoComInfo();
/* 55 */         str2 = Util.toScreen(customerInfoComInfo.getCustomerInfoname(paramString1), i);
/*    */       } 
/* 57 */     } catch (Exception exception) {
/* 58 */       exception.printStackTrace();
/*    */     } 
/* 60 */     return str2;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/RequestBrowser.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */