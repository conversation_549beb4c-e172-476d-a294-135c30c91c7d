/*     */ package weaver.workflow.request;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.StringTokenizer;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class RequestSplitDetailManager
/*     */   extends BaseBean
/*     */ {
/*     */   private HttpServletRequest request;
/*     */   private int requestId;
/*     */   
/*     */   public RequestSplitDetailManager() {}
/*     */   
/*     */   public RequestSplitDetailManager(HttpServletRequest paramHttpServletRequest, int paramInt) {
/*  37 */     this.request = paramHttpServletRequest;
/*  38 */     this.requestId = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setHttpRequest(HttpServletRequest paramHttpServletRequest) {
/*  47 */     this.request = paramHttpServletRequest;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setRequestID(int paramInt) {
/*  55 */     this.requestId = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean saveRequestSplitDetailInfo() {
/*  63 */     if (this.request == null)
/*  64 */       return false; 
/*  65 */     RecordSet recordSet = new RecordSet();
/*  66 */     String str1 = Util.null2String(this.request.getParameter("formid"));
/*  67 */     if (str1.equals("")) {
/*  68 */       return false;
/*     */     }
/*  70 */     String str2 = "";
/*  71 */     String str3 = "";
/*  72 */     recordSet.executeProc("bill_includepages_SelectByID", str1);
/*  73 */     if (recordSet.next()) {
/*  74 */       str2 = Util.null2String(recordSet.getString("detailtablename"));
/*  75 */       str3 = Util.null2String(recordSet.getString("tablename"));
/*     */     } else {
/*  77 */       return false;
/*     */     } 
/*  79 */     if (!str3.equals("") && !str2.equals("")) {
/*  80 */       str3 = str3 + "Split";
/*     */     } else {
/*  82 */       return false;
/*     */     } 
/*     */     
/*  85 */     String str4 = "";
/*  86 */     str4 = "DELETE from " + str2 + " WHERE requestid  =" + String.valueOf(this.requestId);
/*  87 */     recordSet.executeSql(str4);
/*     */     
/*  89 */     str4 = "DELETE from " + str3 + " WHERE requestid  =" + String.valueOf(this.requestId);
/*  90 */     recordSet.executeSql(str4);
/*     */ 
/*     */     
/*  93 */     ArrayList<String> arrayList1 = new ArrayList();
/*  94 */     ArrayList<String> arrayList2 = new ArrayList();
/*  95 */     ArrayList<String> arrayList3 = new ArrayList();
/*     */     
/*  97 */     String str5 = "";
/*  98 */     String str6 = "";
/*  99 */     String str7 = "requestid";
/* 100 */     String str8 = "requestid";
/* 101 */     recordSet.executeProc("workflow_billfield_Select", str1 + "");
/* 102 */     while (recordSet.next()) {
/* 103 */       str6 = Util.null2String(recordSet.getString("viewtype"));
/* 104 */       if (str6.equals("0")) {
/*     */         continue;
/*     */       }
/* 107 */       arrayList1.add(Util.null2String(recordSet.getString("id")));
/* 108 */       arrayList2.add(Util.null2String(recordSet.getString("fieldname")));
/* 109 */       arrayList3.add(Util.null2String(recordSet.getString("fielddbtype")));
/* 110 */       if (!str5.equals("")) {
/* 111 */         str7 = str7 + "," + str5;
/* 112 */         str8 = str8 + "," + str5;
/*     */       } 
/*     */     } 
/*     */     
/* 116 */     str8 = str8 + ",detailId";
/*     */     
/* 118 */     int i = Util.getIntValue(Util.null2String(this.request.getParameter("rowcount")));
/* 119 */     String str9 = Util.null2String(this.request.getParameter("splitrownums"));
/* 120 */     String str10 = Util.null2String(this.request.getParameter("splitrowcounts"));
/*     */     
/* 122 */     ArrayList<String> arrayList4 = new ArrayList();
/* 123 */     ArrayList<String> arrayList5 = new ArrayList();
/* 124 */     StringTokenizer stringTokenizer = new StringTokenizer(str9, ",");
/* 125 */     while (stringTokenizer.hasMoreTokens()) {
/* 126 */       arrayList4.add(stringTokenizer.nextToken().trim());
/*     */     }
/* 128 */     stringTokenizer = new StringTokenizer(str10, ",");
/* 129 */     while (stringTokenizer.hasMoreTokens()) {
/* 130 */       arrayList5.add(stringTokenizer.nextToken().trim());
/*     */     }
/* 132 */     String str11 = "";
/* 133 */     String str12 = "";
/* 134 */     String str13 = "";
/*     */ 
/*     */     
/* 137 */     ArrayList<String> arrayList6 = new ArrayList();
/* 138 */     String str14 = "";
/* 139 */     for (byte b1 = 1; b1 <= i; b1++) {
/* 140 */       for (byte b = 0; b < arrayList1.size(); b++) {
/* 141 */         str11 = arrayList1.get(b);
/* 142 */         str13 = "field" + str11 + "_" + String.valueOf(b1);
/* 143 */         str14 = Util.null2String(this.request.getParameter(str13));
/*     */         
/* 145 */         if (!str14.equals("")) {
/* 146 */           arrayList6.add(String.valueOf(b1));
/*     */           
/*     */           break;
/*     */         } 
/*     */       } 
/*     */     } 
/* 152 */     String str15 = "";
/* 153 */     String str16 = "";
/* 154 */     String str17 = "";
/* 155 */     int j = -1;
/* 156 */     int k = 0;
/* 157 */     String str18 = "";
/* 158 */     String str19 = "";
/* 159 */     for (byte b2 = 0; b2 < arrayList6.size(); b2++) {
/*     */       
/* 161 */       str4 = "INSERT INTO " + str2 + " (requestid) VALUES (" + String.valueOf(this.requestId) + ")";
/* 162 */       recordSet.executeSql(str4);
/*     */ 
/*     */ 
/*     */       
/* 166 */       str4 = "SELECT MAX(id) FROM " + str2;
/* 167 */       recordSet.executeSql(str4);
/* 168 */       if (recordSet.next()) {
/* 169 */         str18 = Util.null2String(recordSet.getString(1));
/*     */       } else {
/* 171 */         return false;
/*     */       } 
/*     */       
/* 174 */       str15 = arrayList6.get(b2); byte b;
/* 175 */       for (b = 0; b < arrayList1.size(); b++) {
/* 176 */         str11 = arrayList1.get(b);
/* 177 */         str5 = Util.null2String(arrayList2.get(b));
/* 178 */         str12 = arrayList3.get(b);
/* 179 */         str13 = "field" + str11 + "_" + str15;
/* 180 */         if (str12.toUpperCase().indexOf("INT") >= 0) {
/* 181 */           str16 = str16 + str5 + "=" + Util.getIntValue(this.request.getParameter(str13), 0) + ",";
/* 182 */         } else if (str12.toUpperCase().indexOf("DECIMAL") >= 0) {
/* 183 */           str16 = str16 + str5 + "=" + Util.getFloatValue(this.request.getParameter(str13), 0.0F) + ",";
/*     */         } else {
/* 185 */           str16 = str16 + str5 + "='" + Util.null2String(this.request.getParameter(str13)) + "',";
/*     */         } 
/*     */       } 
/* 188 */       if (!str16.equals("")) {
/* 189 */         str16 = str16.substring(0, str16.lastIndexOf(","));
/*     */       }
/*     */       
/* 192 */       str4 = "UPDATE " + str2 + " SET " + str16 + " WHERE id=" + str18;
/* 193 */       recordSet.executeSql(str4);
/*     */ 
/*     */       
/* 196 */       j = arrayList4.indexOf(str15);
/* 197 */       if (j != -1) {
/* 198 */         k = Integer.parseInt(arrayList5.get(j));
/* 199 */         for (b = 1; b <= k; b++) {
/*     */ 
/*     */           
/* 202 */           str4 = "INSERT INTO " + str3 + " (detailId, requestid) VALUES (" + str18 + "," + String.valueOf(this.requestId) + ")";
/* 203 */           recordSet.executeSql(str4);
/*     */ 
/*     */ 
/*     */           
/* 207 */           str4 = "SELECT MAX(id) FROM " + str3;
/* 208 */           recordSet.executeSql(str4);
/* 209 */           if (recordSet.next()) {
/* 210 */             str19 = Util.null2String(recordSet.getString(1));
/*     */           } else {
/* 212 */             return false;
/*     */           } 
/*     */           
/* 215 */           for (byte b3 = 0; b3 < arrayList1.size(); b3++) {
/* 216 */             str11 = arrayList1.get(b3);
/* 217 */             str5 = Util.null2String(arrayList2.get(b3));
/* 218 */             str12 = arrayList3.get(b3);
/* 219 */             int m = Integer.parseInt(str15) + b;
/* 220 */             str13 = "splitfield" + str11 + "_" + m;
/* 221 */             if (str12.toUpperCase().indexOf("INT") >= 0) {
/* 222 */               str17 = str17 + str5 + "=" + Util.getIntValue(this.request.getParameter(str13), 0) + ",";
/* 223 */             } else if (str12.toUpperCase().indexOf("DECIMAL") >= 0) {
/* 224 */               str17 = str17 + str5 + "=" + Util.getFloatValue(this.request.getParameter(str13), 0.0F) + ",";
/*     */             } else {
/* 226 */               str17 = str17 + str5 + "='" + Util.null2String(this.request.getParameter(str13)) + "',";
/*     */             } 
/*     */           } 
/* 229 */           if (!str17.equals("")) {
/* 230 */             str17 = str17.substring(0, str17.lastIndexOf(","));
/*     */           }
/*     */           
/* 233 */           str4 = "UPDATE " + str3 + " SET " + str17 + " WHERE id=" + str19;
/* 234 */           recordSet.executeSql(str4);
/*     */ 
/*     */           
/* 237 */           str17 = "";
/*     */         } 
/*     */       } 
/*     */       
/* 241 */       str16 = "";
/*     */     } 
/*     */     
/* 244 */     return true;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/RequestSplitDetailManager.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */