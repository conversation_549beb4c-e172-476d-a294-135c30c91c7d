/*      */ package weaver.workflow.request;
/*      */ 
/*      */ import java.util.ArrayList;
/*      */ import java.util.HashMap;
/*      */ import java.util.Hashtable;
/*      */ import java.util.Iterator;
/*      */ import java.util.List;
/*      */ import java.util.Map;
/*      */ import org.apache.commons.logging.Log;
/*      */ import org.apache.commons.logging.LogFactory;
/*      */ import weaver.conn.ConnStatement;
/*      */ import weaver.conn.RecordSet;
/*      */ import weaver.docs.senddoc.DocReceiveUnitComInfo;
/*      */ import weaver.docs.senddoc.DocReceiveUnitManager;
/*      */ import weaver.general.BaseBean;
/*      */ import weaver.general.ThreadVarManager;
/*      */ import weaver.general.Util;
/*      */ import weaver.hrm.User;
/*      */ import weaver.hrm.UserManager;
/*      */ import weaver.workflow.mode.FieldInfo;
/*      */ import weaver.workflow.workflow.WorkflowComInfo;
/*      */ import weaver.workflow.workflow.WorkflowVersion;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ public class RequestSubwfTriggerManager
/*      */   extends BaseBean
/*      */ {
/*   41 */   private static final Log log = LogFactory.getLog(RequestSubwfTriggerManager.class);
/*      */   
/*   43 */   private RequestIdUpdate requestIdUpdate = new RequestIdUpdate();
/*      */   
/*   45 */   private RequestTriBillSpecOperManager requestTriBillSpecOperManager = null;
/*   46 */   private DocReceiveUnitComInfo docReceiveUnitComInfo = null;
/*      */   private boolean executeSuccess = true;
/*   48 */   private char flag = Util.getSeparator();
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public RequestSubwfTriggerManager() {
/*   56 */     this.requestTriBillSpecOperManager = new RequestTriBillSpecOperManager();
/*      */     try {
/*   58 */       this.docReceiveUnitComInfo = new DocReceiveUnitComInfo();
/*   59 */     } catch (Exception exception) {}
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String TriggerSubwf(RequestManager paramRequestManager, int paramInt, String paramString1, String paramString2, User paramUser) {
/*   77 */     return TriggerSubwf(paramRequestManager, paramInt, paramString1, paramString2, paramUser, "1", 0);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String TriggerSubwf(RequestManager paramRequestManager, int paramInt1, String paramString1, String paramString2, User paramUser, String paramString3, int paramInt2) {
/*  101 */     int i = paramRequestManager.getRequestid();
/*  102 */     String str1 = paramRequestManager.getSrc();
/*  103 */     RecordSet recordSet = new RecordSet();
/*      */     
/*  105 */     if (i <= 0 || paramInt1 <= 0 || paramString1 == null || (
/*      */       
/*  107 */       !paramString1.equals("1") && !paramString1.equals("2")))
/*      */     {
/*  109 */       return "";
/*      */     }
/*      */     
/*  112 */     if (paramString2 == null) {
/*  113 */       paramString2 = "";
/*      */     }
/*      */ 
/*      */     
/*  117 */     String str2 = "0";
/*  118 */     String str3 = "";
/*  119 */     String str4 = "";
/*  120 */     str2 = "" + paramRequestManager.getWorkflowid();
/*  121 */     str3 = "" + paramRequestManager.getCreater();
/*  122 */     str4 = "" + paramRequestManager.getCreatertype();
/*      */ 
/*      */     
/*  125 */     String str5 = "";
/*  126 */     String str6 = "";
/*  127 */     int j = 0;
/*  128 */     int k = 0;
/*  129 */     recordSet.executeSql("select isTriDiffWorkflow,isbill,formid from workflow_base where id=" + str2);
/*  130 */     if (recordSet.next()) {
/*  131 */       str5 = Util.null2String(recordSet.getString("isTriDiffWorkflow"));
/*  132 */       j = recordSet.getInt("isbill");
/*  133 */       k = recordSet.getInt("formid");
/*      */     } 
/*  135 */     if (str5.equals("1")) {
/*  136 */       RequestTriDiffWfManager requestTriDiffWfManager = new RequestTriDiffWfManager();
/*      */       
/*  138 */       SubWorkflowTriggerService subWorkflowTriggerService = new SubWorkflowTriggerService(paramRequestManager, paramInt1, paramString2, paramUser);
/*      */       
/*  140 */       str6 = subWorkflowTriggerService.triggerSubWorkflow(paramString3, paramString1);
/*      */       
/*  142 */       return str6;
/*      */     } 
/*      */     
/*  145 */     WorkflowComInfo workflowComInfo = null;
/*      */     try {
/*  147 */       workflowComInfo = new WorkflowComInfo();
/*  148 */     } catch (Exception exception) {
/*  149 */       return "";
/*      */     } 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  156 */     int m = j;
/*  157 */     int n = k;
/*  158 */     int i1 = 0;
/*      */     
/*  160 */     String str7 = "0";
/*  161 */     String str8 = "0";
/*  162 */     String str9 = "1";
/*  163 */     String str10 = "0";
/*  164 */     String str11 = null;
/*  165 */     HashMap<Object, Object> hashMap1 = null;
/*  166 */     ArrayList<HashMap<Object, Object>> arrayList1 = new ArrayList();
/*      */     
/*  168 */     StringBuffer stringBuffer = new StringBuffer();
/*  169 */     stringBuffer.append(" select id,subWorkflowId,subwfCreatorType,subwfCreatorFieldId,triggerOperation ")
/*  170 */       .append("   from workflow_SubwfSet ")
/*  171 */       .append("   where mainWorkflowid=").append(str2)
/*  172 */       .append("     and triggerType='").append(paramString3).append("'")
/*  173 */       .append("     and triggerNodeId=").append(paramInt1);
/*  174 */     if ("1".equals(paramString3)) {
/*  175 */       stringBuffer.append(" and triggerTime='").append(paramString1).append("'");
/*      */     } else {
/*      */       
/*  178 */       stringBuffer.append(" and id=").append(paramInt2);
/*      */     } 
/*      */ 
/*      */     
/*  182 */     recordSet.executeSql(stringBuffer.toString());
/*      */     
/*  184 */     while (recordSet.next()) {
/*  185 */       str7 = recordSet.getString(1);
/*  186 */       str8 = recordSet.getString(2);
/*  187 */       str9 = recordSet.getString(3);
/*  188 */       str10 = recordSet.getString(4);
/*  189 */       str11 = Util.null2String(recordSet.getString("triggerOperation"));
/*      */       
/*  191 */       if (!str11.equals("")) {
/*  192 */         if (paramString1.equals("1")) {
/*  193 */           if (str11.equals("1") && !str1.equals("submit")) {
/*      */             continue;
/*      */           }
/*  196 */           if (str11.equals("2") && !str1.equals("reject")) {
/*      */             continue;
/*      */           }
/*      */         } 
/*  200 */         if (paramString1.equals("2")) {
/*  201 */           if (str11.equals("3") && !str1.equals("submit")) {
/*      */             continue;
/*      */           }
/*  204 */           if (str11.equals("4") && !str1.equals("reject")) {
/*      */             continue;
/*      */           }
/*      */         } 
/*      */       } 
/*      */ 
/*      */       
/*  211 */       str8 = WorkflowVersion.getActiveVersionWFID(str8);
/*      */       
/*  213 */       hashMap1 = new HashMap<>();
/*  214 */       hashMap1.put("subwfSetId", str7);
/*  215 */       hashMap1.put("subWorkflowId", str8);
/*  216 */       hashMap1.put("subwfCreatorType", str9);
/*  217 */       hashMap1.put("subwfCreatorFieldId", str10);
/*      */       
/*  219 */       arrayList1.add(hashMap1);
/*      */     } 
/*      */ 
/*      */     
/*  223 */     String str12 = "0";
/*  224 */     String str13 = "0";
/*  225 */     String str14 = "0";
/*  226 */     HashMap<Object, Object> hashMap2 = null;
/*  227 */     ArrayList<HashMap<Object, Object>> arrayList2 = new ArrayList();
/*      */     
/*  229 */     RequestManager requestManager = null;
/*      */     
/*  231 */     String str15 = "";
/*  232 */     String str16 = "";
/*  233 */     String str17 = "";
/*  234 */     String str18 = "";
/*  235 */     String str19 = "";
/*  236 */     String[] arrayOfString = null;
/*  237 */     Map<Object, Object> map = null;
/*  238 */     int i2 = -1;
/*  239 */     User user = null;
/*      */     
/*  241 */     Map map1 = null;
/*  242 */     if (arrayList1.size() > 0)
/*      */     {
/*  244 */       map1 = getValueOfField(paramRequestManager, Util.getIntValue(str2, 0));
/*      */     }
/*  246 */     if (map1 == null) {
/*  247 */       return "";
/*      */     }
/*  249 */     for (byte b = 0; b < arrayList1.size(); b++) {
/*  250 */       Map map2 = arrayList1.get(b);
/*  251 */       str7 = (String)map2.get("subwfSetId");
/*  252 */       str8 = (String)map2.get("subWorkflowId");
/*  253 */       str9 = (String)map2.get("subwfCreatorType");
/*  254 */       str10 = (String)map2.get("subwfCreatorFieldId");
/*      */       
/*  256 */       if (paramString2.indexOf("," + str8 + ",") > -1 || paramString2.endsWith("," + str8)) {
/*      */         continue;
/*      */       }
/*  259 */       i1 = Util.getIntValue(workflowComInfo.getIsBill(str8), 0);
/*      */       
/*  261 */       stringBuffer = new StringBuffer();
/*  262 */       arrayList2.clear();
/*  263 */       stringBuffer.append(" select subWorkflowFieldId,mainWorkflowFieldId,ifSplitField ")
/*  264 */         .append("   from workflow_SubwfSetDetail ")
/*  265 */         .append("  where (isdetail is null or isdetail<>1) and subwfSetId=").append(str7);
/*      */       
/*  267 */       recordSet.executeSql(stringBuffer.toString());
/*  268 */       while (recordSet.next()) {
/*  269 */         str12 = recordSet.getString(1);
/*  270 */         str13 = recordSet.getString(2);
/*  271 */         str14 = recordSet.getString(3);
/*      */         
/*  273 */         hashMap2 = new HashMap<>();
/*  274 */         hashMap2.put("subWorkflowFieldId", str12);
/*  275 */         hashMap2.put("mainWorkflowFieldId", str13);
/*  276 */         hashMap2.put("ifSplitField", str14);
/*      */         
/*  278 */         arrayList2.add(hashMap2);
/*      */       } 
/*      */ 
/*      */       
/*  282 */       ArrayList<HashMap<Object, Object>> arrayList3 = new ArrayList();
/*  283 */       ArrayList<HashMap<Object, Object>> arrayList4 = new ArrayList();
/*  284 */       ArrayList<HashMap<Object, Object>> arrayList5 = new ArrayList();
/*  285 */       String str20 = "";
/*  286 */       String str21 = "";
/*  287 */       stringBuffer = new StringBuffer();
/*  288 */       if (m == 1) {
/*  289 */         stringBuffer.append(" select a.mainworkflowfieldid,b.DETAILTABLE,c.DETAILTABLENAME,c.detailkeyfield ")
/*  290 */           .append("   from workflow_SubwfSetDetail a,workflow_billfield b,workflow_bill c ")
/*  291 */           .append("  where a.mainworkflowfieldid=b.ID and b.VIEWTYPE=1 and b.billid=c.id and a.isdetail=1 and a.subwfSetId=")
/*  292 */           .append(str7)
/*  293 */           .append(" order by b.DETAILTABLE,c.DETAILTABLENAME");
/*      */       } else {
/*  295 */         stringBuffer.append(" select a.mainworkflowfieldid,'workflow_formdetail' as DETAILTABLE,'workflow_formdetail' as DETAILTABLENAME,b.groupid as detailkeyfield ")
/*  296 */           .append("   from workflow_SubwfSetDetail a,workflow_formfield b ")
/*  297 */           .append("  where a.mainworkflowfieldid=b.fieldid and b.isdetail='1' and a.isdetail=1 and a.subwfSetId=")
/*  298 */           .append(str7)
/*      */ 
/*      */ 
/*      */           
/*  302 */           .append("   and b.formid=").append(n)
/*  303 */           .append(" order by b.groupid,a.mainworkflowfieldid");
/*      */       } 
/*  305 */       String str22 = "";
/*  306 */       String str23 = "";
/*  307 */       String str24 = "";
/*  308 */       String str25 = "";
/*  309 */       recordSet.executeSql(stringBuffer.toString());
/*  310 */       while (recordSet.next()) {
/*  311 */         String str = Util.null2String(recordSet.getString(1));
/*  312 */         str25 = Util.null2String(recordSet.getString(2));
/*  313 */         if (str25.trim().equals("")) {
/*  314 */           str25 = Util.null2String(recordSet.getString(3));
/*      */         }
/*  316 */         str23 = Util.null2String(recordSet.getString(4));
/*  317 */         if (i1 == 1) {
/*  318 */           if (!str25.trim().equals("") && !str.trim().equals("")) {
/*  319 */             if (!str21.equals(str25)) {
/*  320 */               if (!str21.equals("")) {
/*  321 */                 hashMap2 = new HashMap<>();
/*  322 */                 hashMap2.put("mainworkflowdetailtable", str21);
/*  323 */                 hashMap2.put("mainworkflowfields", str20);
/*  324 */                 hashMap2.put("mainworkflowdltkey", str23);
/*  325 */                 arrayList4.add(hashMap2);
/*  326 */                 str20 = "";
/*      */               } 
/*  328 */               str21 = str25;
/*      */             } 
/*  330 */             if (str20.equals("")) {
/*  331 */               str20 = str; continue;
/*      */             } 
/*  333 */             str20 = str20 + "," + str;
/*      */           } 
/*      */           continue;
/*      */         } 
/*  337 */         if (!str25.trim().equals("") && !str.trim().equals("")) {
/*  338 */           if (!str22.equals(str23)) {
/*  339 */             if (!str22.equals("")) {
/*  340 */               hashMap2 = new HashMap<>();
/*  341 */               hashMap2.put("mainworkflowdetailtable", str25);
/*  342 */               hashMap2.put("mainworkflowfields", str20);
/*  343 */               hashMap2.put("mainworkflowdltkey", str22);
/*  344 */               arrayList4.add(hashMap2);
/*  345 */               str20 = "";
/*      */             } 
/*  347 */             str22 = str23;
/*      */           } 
/*  349 */           if (str20.equals("")) {
/*  350 */             str20 = str; continue;
/*      */           } 
/*  352 */           str20 = str20 + "," + str;
/*      */         } 
/*      */       } 
/*      */ 
/*      */       
/*  357 */       if (!str20.equals("")) {
/*  358 */         hashMap2 = new HashMap<>();
/*  359 */         hashMap2.put("mainworkflowdetailtable", str25);
/*  360 */         hashMap2.put("mainworkflowfields", str20);
/*  361 */         hashMap2.put("mainworkflowdltkey", str23);
/*  362 */         arrayList4.add(hashMap2);
/*  363 */         str20 = "";
/*      */       } 
/*  365 */       str21 = "";
/*  366 */       stringBuffer = new StringBuffer();
/*  367 */       if (i1 == 1) {
/*  368 */         stringBuffer.append(" select a.subworkflowfieldid,b.DETAILTABLE,c.DETAILTABLENAME,c.detailkeyfield ")
/*  369 */           .append("   from workflow_SubwfSetDetail a,workflow_billfield b,workflow_bill c ")
/*  370 */           .append("  where a.subworkflowfieldid=b.ID and b.VIEWTYPE=1 and b.billid=c.id and a.isdetail=1 and a.subwfSetId=")
/*  371 */           .append(str7)
/*  372 */           .append(" order by b.DETAILTABLE,c.DETAILTABLENAME");
/*      */       } else {
/*  374 */         stringBuffer.append(" select a.subworkflowfieldid,'workflow_formdetail' as DETAILTABLE,'workflow_formdetail' as DETAILTABLENAME,b.groupid as detailkeyfield ")
/*  375 */           .append("   from workflow_SubwfSetDetail a,workflow_formfield b ")
/*  376 */           .append("  where a.subworkflowfieldid=b.fieldid and b.isdetail='1' and a.isdetail=1 and a.subwfSetId=")
/*  377 */           .append(str7)
/*  378 */           .append(" and b.formid=(select e.formid from workflow_SubwfSet d,workflow_base e where d.subworkflowid=e.id and (e.isbill is null or e.isbill<>'1') and d.id=")
/*  379 */           .append(str7)
/*  380 */           .append(") order by b.groupid,a.subworkflowfieldid");
/*      */       } 
/*  382 */       recordSet.executeSql(stringBuffer.toString());
/*      */       
/*  384 */       while (recordSet.next()) {
/*  385 */         String str = Util.null2String(recordSet.getString(1));
/*  386 */         str24 = Util.null2String(recordSet.getString(2));
/*  387 */         if (str24.trim().equals("")) {
/*  388 */           str24 = Util.null2String(recordSet.getString(3));
/*      */         }
/*  390 */         str23 = Util.null2String(recordSet.getString(4));
/*  391 */         if (i1 == 1) {
/*  392 */           if (!str24.trim().equals("") && !str.trim().equals("")) {
/*  393 */             if (!str21.equals(str24)) {
/*  394 */               if (!str21.equals("")) {
/*  395 */                 hashMap2 = new HashMap<>();
/*  396 */                 hashMap2.put("subworkflowdetailtable", str21);
/*  397 */                 hashMap2.put("subworkflowfields", str20);
/*  398 */                 hashMap2.put("subworkflowdltkey", str23);
/*  399 */                 arrayList5.add(hashMap2);
/*  400 */                 str20 = "";
/*      */               } 
/*  402 */               str21 = str24;
/*      */             } 
/*  404 */             if (str20.equals("")) {
/*  405 */               str20 = str; continue;
/*      */             } 
/*  407 */             str20 = str20 + "," + str;
/*      */           } 
/*      */           continue;
/*      */         } 
/*  411 */         if (!str24.trim().equals("") && !str.trim().equals("")) {
/*  412 */           if (!str22.equals(str23)) {
/*  413 */             if (!str22.equals("")) {
/*  414 */               hashMap2 = new HashMap<>();
/*  415 */               hashMap2.put("subworkflowdetailtable", str24);
/*  416 */               hashMap2.put("subworkflowfields", str20);
/*  417 */               hashMap2.put("subworkflowdltkey", str22);
/*  418 */               arrayList5.add(hashMap2);
/*  419 */               str20 = "";
/*      */             } 
/*  421 */             str22 = str23;
/*      */           } 
/*  423 */           if (str20.equals("")) {
/*  424 */             str20 = str; continue;
/*      */           } 
/*  426 */           str20 = str20 + "," + str;
/*      */         } 
/*      */       } 
/*      */ 
/*      */       
/*  431 */       if (!str20.equals("")) {
/*  432 */         hashMap2 = new HashMap<>();
/*  433 */         hashMap2.put("subworkflowdetailtable", str24);
/*  434 */         hashMap2.put("subworkflowfields", str20);
/*  435 */         hashMap2.put("subworkflowdltkey", str23);
/*  436 */         arrayList5.add(hashMap2);
/*  437 */         str20 = "";
/*      */       } 
/*      */       
/*  440 */       hashMap2 = new HashMap<>();
/*  441 */       hashMap2.put("subworkflowdetailtables", arrayList5);
/*  442 */       hashMap2.put("mainworkflowdetailtables", arrayList4);
/*  443 */       arrayList3.add(hashMap2);
/*      */ 
/*      */       
/*  446 */       str15 = getUnionSplitedFieldValue(arrayList2, map1);
/*      */       
/*  448 */       if (str15 != null && !str15.equals("")) {
/*  449 */         int i3 = str15.indexOf("_");
/*  450 */         int i4 = str15.indexOf("_", i3 + 1);
/*  451 */         int i5 = str15.indexOf("_", i4 + 1);
/*  452 */         str17 = str15.substring(0, i3);
/*  453 */         str18 = str15.substring(i3 + 1, i4);
/*  454 */         str19 = str15.substring(i4 + 1, i5);
/*  455 */         str16 = str15.substring(i5 + 1);
/*  456 */         arrayOfString = Util.TokenizerString2(str16, ",");
/*      */       } 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*  462 */       if (str9 == null || !str9.equals("3") || !str18.equals(str10)) {
/*  463 */         map = getSubWorkflowCreatorMap(str9, str10, paramUser, str3, str4, map1);
/*  464 */         if (map == null) {
/*      */           continue;
/*      */         }
/*  467 */         i2 = Util.getIntValue((String)map.get("subWorkflowCreatorId"), -1);
/*  468 */         if (i2 <= 0) {
/*      */           continue;
/*      */         }
/*      */       } 
/*      */ 
/*      */ 
/*      */       
/*  475 */       if (str15 == null || str15.equals("")) {
/*  476 */         requestManager = new RequestManager();
/*  477 */         requestManager.setHasTriggeredSubwf(paramString2 + "," + str8);
/*  478 */         requestManager.setWorkflowid(Integer.parseInt(str8));
/*  479 */         user = getSubWorkflowCreatorByMap(requestManager, map);
/*  480 */         requestManager.setUser(user);
/*      */         
/*  482 */         boolean bool1 = initRequestManager(requestManager, i);
/*  483 */         if (!bool1) {
/*  484 */           writeLog("initStatus=false##mainRequestId=" + i);
/*      */         }
/*      */         
/*  487 */         Hashtable hashtable = getPreAddRule_hs(requestManager);
/*      */ 
/*      */         
/*  490 */         boolean bool2 = updateDataOfRelatedTable(requestManager, i, arrayList2, map1, str2, "", "", paramUser, hashtable);
/*  491 */         if (bool2) bool2 = updateDltDataOfRelatedTable(requestManager, i, arrayList3, str2, str7, hashtable); 
/*  492 */         if (!bool2) {
/*  493 */           writeLog("updateStatus=false##mainRequestId=" + i + "##mainWorkflowId=" + str2);
/*      */         }
/*      */ 
/*      */         
/*  497 */         writeLog("szg 进入了执行节点后附加操作1");
/*  498 */         int i3 = -1;
/*  499 */         int i4 = -1;
/*  500 */         int i5 = -1;
/*  501 */         int i6 = -1;
/*  502 */         writeLog("szg 进入了执行节点后附加操作1");
/*  503 */         i3 = requestManager.getWorkflowid();
/*  504 */         writeLog("szg 进入了执行节点后附加操作workflowId1" + i3);
/*  505 */         recordSet.executeProc("workflow_CreateNode_Select", i3 + "");
/*  506 */         if (recordSet.next()) {
/*      */           
/*  508 */           i4 = Util.getIntValue(recordSet.getString(1), 0);
/*  509 */           writeLog("szg 进入了执行节点后附加操作nodeId1" + i4);
/*      */         } 
/*      */ 
/*      */         
/*  513 */         recordSet.executeSql(" select workflowName,formId,isBill from workflow_base where id= " + i3);
/*  514 */         writeLog("szg 进入了执行节点后附加操作nodeId1" + i4);
/*  515 */         if (recordSet.next()) {
/*      */ 
/*      */           
/*  518 */           i5 = recordSet.getInt(2);
/*  519 */           i6 = recordSet.getInt(3);
/*  520 */           writeLog("szg 进入了执行节点后附加操作formId1" + i5);
/*  521 */           writeLog("szg 进入了执行节点后附加操作isBill1" + i6);
/*      */         } 
/*      */ 
/*      */ 
/*      */         
/*      */         try {
/*  527 */           writeLog("szg 进入了执行节点后附加操作requestCheckAddinRules");
/*  528 */           RequestCheckAddinRules requestCheckAddinRules = new RequestCheckAddinRules();
/*  529 */           requestCheckAddinRules.resetParameter();
/*  530 */           requestCheckAddinRules.setWorkflowid(i3);
/*  531 */           requestCheckAddinRules.setRequestid(i);
/*  532 */           requestCheckAddinRules.setObjid(i4);
/*  533 */           requestCheckAddinRules.setObjtype(1);
/*  534 */           requestCheckAddinRules.setIsbill(i6);
/*  535 */           requestCheckAddinRules.setFormid(i5);
/*  536 */           requestCheckAddinRules.setIspreadd("0");
/*  537 */           requestCheckAddinRules.setRequestManager(requestManager);
/*  538 */           writeLog("szg 进入了执行节点后附加操作requestCheckAddinRules");
/*  539 */           requestCheckAddinRules.checkAddinRules();
/*  540 */           writeLog("szg 执行完了节点后附加操作requestCheckAddinRules");
/*  541 */         } catch (Exception exception) {
/*  542 */           if (exception.getMessage().indexOf("workflow interface action error") > -1) {
/*  543 */             writeLog(exception);
/*      */           }
/*      */         } 
/*      */ 
/*      */ 
/*      */         
/*  549 */         int i7 = requestManager.getWorkflowid();
/*  550 */         int i8 = requestManager.getRequestid();
/*  551 */         boolean bool3 = hasNeedInputField(i8, i7);
/*      */ 
/*      */         
/*  554 */         if (!bool3) {
/*  555 */           boolean bool = requestManager.flowNextNode();
/*  556 */           if (!bool) {
/*  557 */             writeLog("flowStatus=false");
/*      */           }
/*      */         } 
/*      */         
/*  561 */         this.requestTriBillSpecOperManager.triBillSpecOper(requestManager);
/*      */       } else {
/*  563 */         String str26 = null;
/*  564 */         String str27 = null;
/*      */         
/*  566 */         for (byte b1 = 0; b1 < arrayOfString.length; b1++) {
/*      */           
/*  568 */           str26 = Util.null2String(arrayOfString[b1]);
/*      */           
/*  570 */           if (str19.equals("142")) {
/*  571 */             str27 = Util.null2String(this.docReceiveUnitComInfo.getCanStartChildRequest(str26));
/*      */             
/*  573 */             if (!"1".equals(str27)) {
/*      */               continue;
/*      */             }
/*      */           } 
/*      */           
/*  578 */           requestManager = new RequestManager();
/*  579 */           requestManager.setHasTriggeredSubwf(paramString2 + "," + str8);
/*  580 */           requestManager.setWorkflowid(Integer.parseInt(str8));
/*  581 */           if (str9 == null || !str9.equals("3") || !str18.equals(str10)) {
/*  582 */             user = getSubWorkflowCreatorByMap(requestManager, map);
/*  583 */             requestManager.setUser(user);
/*      */           
/*      */           }
/*  586 */           else if (str19.equals("142")) {
/*  587 */             String str28 = "";
/*  588 */             String str29 = "";
/*      */             try {
/*  590 */               DocReceiveUnitManager docReceiveUnitManager = new DocReceiveUnitManager();
/*  591 */               str28 = docReceiveUnitManager.getUserIdsByDocReceiveUnit(arrayOfString[b1]);
/*  592 */             } catch (Exception exception) {
/*  593 */               str28 = "";
/*      */             } 
/*  595 */             if (str28 == null || str28.equals("")) {
/*  596 */               str29 = "";
/*  597 */             } else if (str28.indexOf(",") == -1) {
/*  598 */               str29 = str28;
/*      */             } else {
/*  600 */               str29 = str28.substring(0, str28.indexOf(","));
/*      */             } 
/*      */             
/*  603 */             if (str29 == null || str29.equals("")) {
/*      */               continue;
/*      */             }
/*  606 */             map = new HashMap<>();
/*  607 */             map.put("subWorkflowCreatorId", str29);
/*  608 */             map.put("subWorkflowCreatorType", "1");
/*  609 */             user = getSubWorkflowCreatorByMap(requestManager, map);
/*  610 */             requestManager.setUser(user);
/*      */           } else {
/*      */             
/*  613 */             map = new HashMap<>();
/*  614 */             map.put("subWorkflowCreatorId", arrayOfString[b1]);
/*  615 */             map.put("subWorkflowCreatorType", "1");
/*  616 */             user = getSubWorkflowCreatorByMap(requestManager, map);
/*  617 */             requestManager.setUser(user);
/*      */           } 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */           
/*  624 */           boolean bool1 = initRequestManager(requestManager, i);
/*  625 */           if (!bool1) {
/*  626 */             writeLog("initStatus=false##mainRequestId=" + i);
/*      */           }
/*      */           
/*  629 */           Hashtable hashtable = getPreAddRule_hs(requestManager);
/*      */ 
/*      */           
/*  632 */           boolean bool2 = updateDataOfRelatedTable(requestManager, i, arrayList2, map1, str2, str17, arrayOfString[b1], paramUser, hashtable);
/*  633 */           if (bool2) bool2 = updateDltDataOfRelatedTable(requestManager, i, arrayList3, str2, str7, hashtable); 
/*  634 */           if (!bool2) {
/*  635 */             writeLog("updateStatus=false##mainRequestId=" + i + "##mainWorkflowId=" + str2 + "##subWorkflowFieldIdRelateToSplit=" + str17 + "##splitedFieldValueArray[j]=" + arrayOfString[b1]);
/*      */           }
/*      */ 
/*      */           
/*  639 */           writeLog("szg 进入了执行节点后附加操作2");
/*  640 */           int i3 = -1;
/*  641 */           int i4 = -1;
/*  642 */           int i5 = -1;
/*  643 */           int i6 = -1;
/*  644 */           writeLog("szg 进入了执行节点后附加操作2");
/*  645 */           i3 = requestManager.getWorkflowid();
/*  646 */           writeLog("szg 进入了执行节点后附加操作workflowId1" + i3);
/*  647 */           recordSet.executeProc("workflow_CreateNode_Select", i3 + "");
/*  648 */           if (recordSet.next()) {
/*      */             
/*  650 */             i4 = Util.getIntValue(recordSet.getString(1), 0);
/*  651 */             writeLog("szg 进入了执行节点后附加操作nodeId1" + i4);
/*      */           } 
/*      */ 
/*      */           
/*  655 */           recordSet.executeSql(" select workflowName,formId,isBill from workflow_base where id= " + i3);
/*  656 */           writeLog("szg 进入了执行节点后附加操作nodeId1" + i4);
/*  657 */           if (recordSet.next()) {
/*      */ 
/*      */             
/*  660 */             i5 = recordSet.getInt(2);
/*  661 */             i6 = recordSet.getInt(3);
/*  662 */             writeLog("szg 进入了执行节点后附加操作formId1" + i5);
/*  663 */             writeLog("szg 进入了执行节点后附加操作isBill1" + i6);
/*      */           } 
/*      */ 
/*      */ 
/*      */           
/*      */           try {
/*  669 */             writeLog("szg 进入了执行节点后附加操作requestCheckAddinRules");
/*  670 */             RequestCheckAddinRules requestCheckAddinRules = new RequestCheckAddinRules();
/*  671 */             requestCheckAddinRules.resetParameter();
/*  672 */             requestCheckAddinRules.setWorkflowid(i3);
/*  673 */             requestCheckAddinRules.setRequestid(i);
/*  674 */             requestCheckAddinRules.setObjid(i4);
/*  675 */             requestCheckAddinRules.setObjtype(1);
/*  676 */             requestCheckAddinRules.setIsbill(i6);
/*  677 */             requestCheckAddinRules.setFormid(i5);
/*  678 */             requestCheckAddinRules.setIspreadd("0");
/*  679 */             requestCheckAddinRules.setRequestManager(requestManager);
/*  680 */             writeLog("szg 进入了执行节点后附加操作requestCheckAddinRules");
/*  681 */             requestCheckAddinRules.checkAddinRules();
/*  682 */             writeLog("szg 执行完了节点后附加操作requestCheckAddinRules");
/*  683 */           } catch (Exception exception) {
/*  684 */             if (exception.getMessage().indexOf("workflow interface action error") > -1) {
/*  685 */               writeLog(exception);
/*      */             }
/*      */           } 
/*      */ 
/*      */ 
/*      */ 
/*      */           
/*  692 */           int i7 = requestManager.getWorkflowid();
/*  693 */           int i8 = requestManager.getRequestid();
/*  694 */           boolean bool3 = hasNeedInputField(i8, i7);
/*      */ 
/*      */           
/*  697 */           if (!bool3) {
/*      */             
/*  699 */             boolean bool = requestManager.flowNextNode();
/*  700 */             if (!bool) {
/*  701 */               writeLog("flowStatus=false");
/*      */             }
/*      */           } 
/*  704 */           this.requestTriBillSpecOperManager.triBillSpecOper(requestManager);
/*      */           continue;
/*      */         } 
/*      */       } 
/*      */       continue;
/*      */     } 
/*  710 */     return "";
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static boolean hasNeedInputField(int paramInt1, int paramInt2) {
/*  721 */     RecordSet recordSet = new RecordSet();
/*  722 */     int i = -1;
/*  723 */     String str = "SELECT nodeid FROM workflow_flownode WHERE workflowid=" + paramInt2 + "  AND nodetype='0'";
/*  724 */     recordSet.execute(str);
/*  725 */     if (recordSet.next()) {
/*  726 */       i = Util.getIntValue(recordSet.getString("nodeid"));
/*      */     }
/*      */     
/*  729 */     List<List> list = null;
/*  730 */     Map map = null;
/*      */     try {
/*  732 */       WorkflowComInfo workflowComInfo = new WorkflowComInfo();
/*  733 */       int j = Util.getIntValue(workflowComInfo.getIsBill(String.valueOf(paramInt2)));
/*  734 */       int k = Util.getIntValue(workflowComInfo.getFormId(String.valueOf(paramInt2)));
/*  735 */       FieldInfo fieldInfo = new FieldInfo();
/*  736 */       fieldInfo.setRequestid(paramInt1);
/*  737 */       fieldInfo.GetManTableField(k, j, 7);
/*  738 */       fieldInfo.GetDetailTblFields(k, j, 7);
/*      */       
/*  740 */       map = fieldInfo.getMainFieldValues();
/*  741 */       list = fieldInfo.getDetailFieldValues();
/*  742 */     } catch (Exception exception) {
/*  743 */       log.info("Catch a exception during instantiate the WorkflowComInfo.", exception);
/*  744 */       return true;
/*      */     } 
/*      */     
/*  747 */     str = "select a.ismode,a.showdes from workflow_flownode a where a.workflowid=" + paramInt2 + " and a.nodeid=" + i;
/*  748 */     recordSet.executeSql(str);
/*  749 */     if (recordSet.next()) {
/*  750 */       int j = Util.getIntValue(recordSet.getString("ismode"), 0);
/*  751 */       int k = Util.getIntValue(recordSet.getString("showdes"), 0);
/*  752 */       if (j == 0 || j == 2 || (j == 1 && k == 1)) {
/*  753 */         str = "select fieldid from workflow_nodeform where ismandatory='1' and nodeid=" + i;
/*      */       } else {
/*  755 */         str = "select fieldid from workflow_modeview where ismandatory='1' and nodeid=" + i;
/*      */       } 
/*  757 */       recordSet.executeSql(str);
/*  758 */       while (recordSet.next()) {
/*  759 */         String str1 = recordSet.getString("fieldid");
/*      */         
/*  761 */         if (map.containsKey(str1)) {
/*  762 */           String str2 = (String)map.get(str1);
/*  763 */           if (str2 == null || "".equals(str2)) {
/*  764 */             return true;
/*      */           }
/*      */           continue;
/*      */         } 
/*  768 */         for (byte b = 0; b < list.size(); b++) {
/*  769 */           List<Map> list1 = list.get(b);
/*  770 */           for (byte b1 = 0; b1 < list1.size(); b1++) {
/*  771 */             Map map1 = list1.get(b1);
/*  772 */             if (!map1.containsKey(str1)) {
/*      */               break;
/*      */             }
/*  775 */             String str2 = (String)map1.get(str1);
/*  776 */             if (str2 == null || "".equals(str2)) {
/*  777 */               return true;
/*      */             }
/*      */           } 
/*      */         } 
/*      */       } 
/*      */     } 
/*      */ 
/*      */ 
/*      */     
/*  786 */     return false;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private Map getSubWorkflowCreatorMap(String paramString1, String paramString2, User paramUser, String paramString3, String paramString4, Map paramMap) {
/*  805 */     int i = -1;
/*  806 */     String str = "1";
/*  807 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*      */ 
/*      */     
/*  810 */     if (paramString1 == null || (
/*  811 */       !paramString1.equals("1") && 
/*  812 */       !paramString1.equals("2") && 
/*  813 */       !paramString1.equals("3")))
/*      */     {
/*      */ 
/*      */ 
/*      */       
/*  818 */       return hashMap;
/*      */     }
/*      */ 
/*      */     
/*  822 */     if ("1".equals(paramString1) && paramUser != null) {
/*  823 */       i = paramUser.getUID();
/*  824 */       str = paramUser.getLogintype();
/*  825 */       hashMap.put("subWorkflowCreatorId", "" + i);
/*  826 */       hashMap.put("subWorkflowCreatorType", "" + str);
/*      */     } 
/*      */ 
/*      */     
/*  830 */     if ("2".equals(paramString1)) {
/*      */ 
/*      */ 
/*      */       
/*  834 */       i = Util.getIntValue(paramString3, -1);
/*  835 */       str = paramString4.equals("0") ? "1" : "2";
/*  836 */       hashMap.put("subWorkflowCreatorId", "" + i);
/*  837 */       hashMap.put("subWorkflowCreatorType", "" + str);
/*      */     } 
/*      */ 
/*      */     
/*  841 */     if ("3".equals(paramString1)) {
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*  858 */       String str1 = Util.null2String((String)paramMap.get("fieldHtmlType" + paramString2));
/*  859 */       String str2 = Util.null2String((String)paramMap.get("type" + paramString2));
/*  860 */       String str3 = Util.null2String((String)paramMap.get("field" + paramString2));
/*      */       
/*  862 */       String str4 = "";
/*  863 */       String str5 = "";
/*      */       
/*  865 */       if ("3".equals(str1) && "1".equals(str2)) {
/*  866 */         str4 = str3;
/*  867 */       } else if ("3".equals(str1) && ("17".equals(str2) || "166".equals(str2))) {
/*  868 */         if (str3.indexOf(",") == -1) {
/*  869 */           str4 = str3;
/*      */         } else {
/*  871 */           str4 = str3.substring(0, str3.indexOf(","));
/*      */         } 
/*  873 */       } else if ("3".equals(str1) && "141".equals(str2)) {
/*  874 */         ResourceConditionManager resourceConditionManager = new ResourceConditionManager();
/*  875 */         str5 = resourceConditionManager.getUserIdsByResourceCondition(str3);
/*  876 */         if (str5 == null || str5.equals("")) {
/*  877 */           str4 = "";
/*  878 */         } else if (str5.indexOf(",") == -1) {
/*  879 */           str4 = str5;
/*      */         } else {
/*  881 */           str4 = str5.substring(0, str5.indexOf(","));
/*      */         } 
/*  883 */       } else if ("3".equals(str1) && "142".equals(str2)) {
/*      */         
/*      */         try {
/*  886 */           DocReceiveUnitManager docReceiveUnitManager = new DocReceiveUnitManager();
/*  887 */           str5 = docReceiveUnitManager.getUserIdsByDocReceiveUnit(str3);
/*  888 */         } catch (Exception exception) {
/*  889 */           str5 = "";
/*      */         } 
/*  891 */         if (str5 == null || str5.equals("")) {
/*  892 */           str4 = "";
/*  893 */         } else if (str5.indexOf(",") == -1) {
/*  894 */           str4 = str5;
/*      */         } else {
/*  896 */           str4 = str5.substring(0, str5.indexOf(","));
/*      */         } 
/*      */       } 
/*      */ 
/*      */       
/*  901 */       if (str4 == null || str4.equals(""))
/*      */       {
/*      */         
/*  904 */         return hashMap;
/*      */       }
/*      */       
/*  907 */       i = Util.getIntValue(str4, -1);
/*  908 */       hashMap.put("subWorkflowCreatorId", "" + i);
/*  909 */       hashMap.put("subWorkflowCreatorType", "1");
/*      */     } 
/*      */ 
/*      */     
/*  913 */     return hashMap;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private User getSubWorkflowCreatorByMap(RequestManager paramRequestManager, Map paramMap) {
/*  927 */     User user = null;
/*  928 */     RecordSet recordSet = new RecordSet();
/*      */     
/*  930 */     if (paramRequestManager == null || paramMap == null) {
/*  931 */       return null;
/*      */     }
/*      */     
/*  934 */     int i = Util.getIntValue((String)paramMap.get("subWorkflowCreatorId"), -1);
/*  935 */     String str1 = Util.null2String((String)paramMap.get("subWorkflowCreatorType"));
/*      */     
/*  937 */     UserManager userManager = new UserManager();
/*  938 */     if (!str1.equals("1")) {
/*  939 */       user = userManager.getUserByUserIdAndLoginType(i, str1);
/*  940 */       return user;
/*      */     } 
/*      */     
/*  943 */     String str2 = paramRequestManager.getCurrentDate();
/*  944 */     String str3 = paramRequestManager.getCurrentTime();
/*  945 */     int j = paramRequestManager.getWorkflowid();
/*      */     
/*  947 */     int k = -1;
/*  948 */     int m = -1;
/*  949 */     String str4 = "";
/*  950 */     String str5 = "";
/*  951 */     String str6 = "";
/*  952 */     String str7 = "";
/*  953 */     recordSet.executeSql("select agentuid,beginDate,beginTime,endDate,endTime from workflow_agentConditionSet where iscreateagenter=1 and agenttype = '1' and workflowId=" + j + "  and bagentuid=" + i + " order by agentbatch asc  ,id asc ");
/*  954 */     while (recordSet.next()) {
/*  955 */       k = Util.getIntValue(recordSet.getString("agentuid"), -1);
/*  956 */       str4 = Util.null2String(recordSet.getString("beginDate"));
/*  957 */       str5 = Util.null2String(recordSet.getString("beginTime"));
/*  958 */       str6 = Util.null2String(recordSet.getString("endDate"));
/*  959 */       str7 = Util.null2String(recordSet.getString("endTime"));
/*  960 */       if ("".equals(str5)) {
/*  961 */         str5 = "00:00:00";
/*      */       }
/*  963 */       if ("".equals(str7)) {
/*  964 */         str7 = "00:00:00";
/*      */       }
/*  966 */       if (!str4.equals("") && (
/*  967 */         str4 + " " + str5).compareTo(str2 + " " + str3) > 0) {
/*      */         continue;
/*      */       }
/*  970 */       if (!str6.equals("") && (
/*  971 */         str6 + " " + str7).compareTo(str2 + " " + str3) < 0) {
/*      */         continue;
/*      */       }
/*  974 */       m = k;
/*      */     } 
/*      */ 
/*      */     
/*  978 */     if (m > 0) {
/*  979 */       paramRequestManager.setIsagentCreater(1);
/*  980 */       paramRequestManager.setBeAgenter(i);
/*      */     } else {
/*  982 */       m = i;
/*      */     } 
/*      */     
/*  985 */     user = userManager.getUserByUserIdAndLoginType(m, "1");
/*  986 */     return user;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private String getUnionSplitedFieldValue(List<Map> paramList, Map paramMap) {
/* 1003 */     String str1 = "";
/*      */ 
/*      */     
/* 1006 */     if (paramList == null || paramList.size() == 0 || paramMap == null)
/*      */     {
/* 1008 */       return "";
/*      */     }
/*      */     
/* 1011 */     Map map = null;
/* 1012 */     String str2 = "0";
/* 1013 */     String str3 = "0";
/* 1014 */     String str4 = "0";
/* 1015 */     String str5 = "0";
/* 1016 */     String str6 = "0";
/*      */     
/* 1018 */     for (byte b = 0; b < paramList.size(); b++) {
/* 1019 */       map = paramList.get(b);
/*      */       
/* 1021 */       str2 = (String)map.get("subWorkflowFieldId");
/* 1022 */       str3 = (String)map.get("mainWorkflowFieldId");
/* 1023 */       str4 = (String)map.get("ifSplitField");
/*      */       
/* 1025 */       if (str4 != null && str4.equals("1")) {
/* 1026 */         str5 = str2;
/* 1027 */         str6 = str3;
/*      */       } 
/*      */     } 
/*      */ 
/*      */     
/* 1032 */     if (str5 == null || str5.equals("") || str6 == null || str6
/* 1033 */       .equals(""))
/*      */     {
/* 1035 */       return "";
/*      */     }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/* 1043 */     String str7 = "";
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/* 1052 */     String str8 = Util.null2String((String)paramMap.get("fieldHtmlType" + str6));
/* 1053 */     String str9 = Util.null2String((String)paramMap.get("type" + str6));
/* 1054 */     String str10 = Util.null2String((String)paramMap.get("field" + str6));
/*      */     
/* 1056 */     if ("3".equals(str8) && ("17".equals(str9) || "166".equals(str9))) {
/* 1057 */       str1 = str5 + "_" + str6 + "_" + str9 + "_" + str10;
/* 1058 */     } else if ("3".equals(str8) && "141".equals(str9)) {
/* 1059 */       ResourceConditionManager resourceConditionManager = new ResourceConditionManager();
/* 1060 */       str7 = resourceConditionManager.getUserIdsByResourceCondition(str10);
/* 1061 */       str1 = str5 + "_" + str6 + "_141_" + str7;
/* 1062 */     } else if ("3".equals(str8) && "142".equals(str9)) {
/* 1063 */       str1 = str5 + "_" + str6 + "_142_" + str10;
/*      */     } 
/*      */     
/* 1066 */     return str1;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private boolean initRequestManager(RequestManager paramRequestManager, int paramInt) {
/* 1077 */     RecordSet recordSet = new RecordSet();
/* 1078 */     ThreadVarManager.setMultiLang(Boolean.valueOf(true));
/* 1079 */     int i = paramRequestManager.getWorkflowid();
/*      */ 
/*      */     
/* 1082 */     String str1 = "";
/* 1083 */     String str2 = "";
/* 1084 */     int j = 0;
/* 1085 */     int k = 0;
/* 1086 */     int m = 0;
/* 1087 */     String str3 = "";
/* 1088 */     int n = 0;
/* 1089 */     String str4 = "";
/* 1090 */     String str5 = "";
/*      */ 
/*      */     
/* 1093 */     recordSet.executeSql(" select workflowName,workflowType,formId,isBill,messageType,smsAlertsType from workflow_base where id= " + i);
/*      */     
/* 1095 */     if (recordSet.next()) {
/* 1096 */       str1 = recordSet.getString(1);
/* 1097 */       str2 = recordSet.getString(2);
/* 1098 */       j = recordSet.getInt(3);
/* 1099 */       k = recordSet.getInt(4);
/* 1100 */       str4 = Util.null2String(recordSet.getString(5));
/* 1101 */       str5 = Util.null2String(recordSet.getString(6));
/*      */     } 
/*      */     
/* 1104 */     if (k == 1) {
/* 1105 */       recordSet.executeSql("select tablename from workflow_bill where id = " + j);
/* 1106 */       if (recordSet.next()) {
/* 1107 */         str3 = recordSet.getString("tablename");
/*      */       } else {
/* 1109 */         return false;
/*      */       } 
/*      */     } 
/*      */     
/* 1113 */     int[] arrayOfInt = this.requestIdUpdate.getRequestNewId(str3);
/* 1114 */     n = arrayOfInt[0];
/* 1115 */     if (n == -1) {
/* 1116 */       return false;
/*      */     }
/*      */     
/* 1119 */     if (k == 1) {
/* 1120 */       m = arrayOfInt[1];
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/* 1129 */       if (m < 1) return false;
/*      */     
/*      */     } 
/* 1132 */     this.executeSuccess = recordSet.executeSql("insert into workflow_form (requestid,billformid,billid) values(" + n + "," + j + "," + m + ")");
/* 1133 */     if (!this.executeSuccess) return false;
/*      */ 
/*      */     
/* 1136 */     int i1 = 0;
/* 1137 */     String str6 = "0";
/* 1138 */     User user = paramRequestManager.getUser();
/* 1139 */     int i2 = paramRequestManager.getUserId();
/* 1140 */     int i3 = paramRequestManager.getUserType();
/*      */     
/* 1142 */     recordSet.executeProc("workflow_CreateNode_Select", i + "");
/* 1143 */     if (recordSet.next()) {
/* 1144 */       i1 = Util.getIntValue(recordSet.getString(1), 0);
/*      */     } else {
/* 1146 */       return false;
/*      */     } 
/*      */     
/*      */     try {
/* 1150 */       String str7 = paramRequestManager.getCurrentDate();
/* 1151 */       String str8 = paramRequestManager.getCurrentTime();
/* 1152 */       int i4 = i1;
/* 1153 */       String str9 = str6;
/* 1154 */       String str10 = "";
/* 1155 */       boolean bool1 = false;
/* 1156 */       boolean bool2 = false;
/* 1157 */       boolean bool3 = false;
/* 1158 */       boolean bool4 = false;
/* 1159 */       String str11 = "";
/* 1160 */       String str12 = "";
/* 1161 */       boolean bool5 = false;
/* 1162 */       byte b1 = -1;
/* 1163 */       byte b2 = -1;
/* 1164 */       boolean bool6 = false;
/* 1165 */       boolean bool7 = false;
/* 1166 */       int i5 = 0;
/* 1167 */       recordSet.executeSql("select id from workflow_groupdetail\twhere groupid in(select id from workflow_nodegroup where nodeid=" + i1 + ")");
/* 1168 */       if (recordSet.next()) {
/* 1169 */         i5 = recordSet.getInt(1);
/*      */       }
/* 1171 */       int i6 = i2;
/* 1172 */       int i7 = i3;
/* 1173 */       int i8 = paramRequestManager.getIsagentCreater();
/* 1174 */       int i9 = paramRequestManager.getBeAgenter();
/* 1175 */       String str13 = "";
/* 1176 */       String str14 = "";
/* 1177 */       String str15 = "";
/* 1178 */       String str16 = "";
/* 1179 */       if (i8 == 1) {
/* 1180 */         if (i9 < 1) {
/* 1181 */           recordSet.executeSql("select bagentuid,beginDate,beginTime,endDate,endTime from workflow_agentConditionSet where iscreateagenter=1 and agenttype = '1' and agentuid=" + i2 + " and workflowid=" + i + " order by agentbatch asc  ,id asc  ");
/* 1182 */           while (recordSet.next()) {
/* 1183 */             str13 = Util.null2String(recordSet.getString("beginDate"));
/* 1184 */             str14 = Util.null2String(recordSet.getString("beginTime"));
/* 1185 */             str15 = Util.null2String(recordSet.getString("endDate"));
/* 1186 */             str16 = Util.null2String(recordSet.getString("endTime"));
/* 1187 */             if ("".equals(str14)) {
/* 1188 */               str14 = "00:00:00";
/*      */             }
/* 1190 */             if ("".equals(str16)) {
/* 1191 */               str16 = "00:00:00";
/*      */             }
/* 1193 */             if (!str13.equals("") && (
/* 1194 */               str13 + " " + str14).compareTo(str7 + " " + str8) > 0) {
/*      */               continue;
/*      */             }
/*      */             
/* 1198 */             if (!str15.equals("") && (
/* 1199 */               str15 + " " + str16).compareTo(str7 + " " + str8) < 0) {
/*      */               continue;
/*      */             }
/*      */             
/* 1203 */             i6 = recordSet.getInt(1);
/* 1204 */             i7 = 0;
/*      */           } 
/*      */         } else {
/*      */           
/* 1208 */           i6 = i9;
/* 1209 */           i7 = 0;
/*      */         } 
/*      */       }
/*      */       
/* 1213 */       boolean bool8 = false;
/* 1214 */       String str17 = "";
/* 1215 */       String str18 = "";
/* 1216 */       String str19 = "";
/* 1217 */       String str20 = "";
/* 1218 */       String str21 = "";
/* 1219 */       if (str4.equals("1")) {
/* 1220 */         str4 = str5;
/*      */       } else {
/* 1222 */         str4 = "0";
/*      */       } 
/* 1224 */       WorkflowComInfo workflowComInfo = new WorkflowComInfo();
/*      */       
/* 1226 */       String str22 = workflowComInfo.getWorkflowname(String.valueOf(i)) + "-" + user.getUsername() + "-" + str7;
/*      */ 
/*      */       
/* 1229 */       String str23 = "" + n + this.flag + i + this.flag + i4 + this.flag + str9 + this.flag + i1 + this.flag + str6 + this.flag + str10 + this.flag + bool1 + this.flag + bool2 + this.flag + str22 + this.flag + i6 + this.flag + str7 + this.flag + str8 + this.flag + bool3 + this.flag + bool4 + this.flag + str12 + this.flag + bool5 + this.flag + i7 + this.flag + bool4 + this.flag + b1 + this.flag + b2 + this.flag + str17 + this.flag + str18 + this.flag + str19 + this.flag + str20 + this.flag + str21 + this.flag + str4;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/* 1237 */       this.executeSuccess = recordSet.executeProc("workflow_Requestbase_Insert", str23);
/* 1238 */       if (!this.executeSuccess) {
/* 1239 */         return false;
/*      */       }
/* 1241 */       recordSet.executeSql("update  workflow_requestbase set requestLevel=" + bool8 + ",mainRequestId=" + paramInt + " where requestId=" + n);
/*      */       
/* 1243 */       paramRequestManager.setSrc("submit");
/* 1244 */       paramRequestManager.setIscreate("1");
/* 1245 */       paramRequestManager.setWorkflowtype(str2);
/* 1246 */       paramRequestManager.setFormid(j);
/* 1247 */       paramRequestManager.setIsbill(k);
/* 1248 */       paramRequestManager.setBillid(m);
/* 1249 */       paramRequestManager.setBilltablename(str3);
/* 1250 */       paramRequestManager.setRequestid(n);
/* 1251 */       paramRequestManager.setRequestname(str22);
/* 1252 */       paramRequestManager.setCreatedate(str7);
/* 1253 */       paramRequestManager.setCreatetime(str8);
/* 1254 */       paramRequestManager.setNodeid(i1);
/* 1255 */       paramRequestManager.setNodetype(str6);
/* 1256 */       paramRequestManager.setLastNodeid(i4);
/* 1257 */       paramRequestManager.setLastnodetype(str9);
/* 1258 */       paramRequestManager.setCreater(i6);
/* 1259 */       paramRequestManager.setCreatertype(i7);
/*      */       
/* 1261 */       if (i8 == 1 && i9 > 0) {
/* 1262 */         str23 = "" + n + this.flag + i2 + this.flag + bool7 + this.flag + i + this.flag + str2 + this.flag + i7 + this.flag + bool6 + this.flag + i4 + this.flag + i9 + this.flag + "2" + this.flag + -1 + this.flag + i5;
/*      */       }
/*      */       else {
/*      */         
/* 1266 */         str23 = "" + n + this.flag + i2 + this.flag + bool7 + this.flag + i + this.flag + str2 + this.flag + i7 + this.flag + bool6 + this.flag + i4 + this.flag + -1 + this.flag + "0" + this.flag + -1 + this.flag + i5;
/*      */       } 
/*      */ 
/*      */       
/* 1270 */       this.executeSuccess = recordSet.executeProc("workflow_CurrentOperator_I", str23);
/*      */       
/* 1272 */       if (!this.executeSuccess) {
/* 1273 */         return false;
/*      */       }
/*      */ 
/*      */       
/*      */       try {
/* 1278 */         RequestCheckAddinRules requestCheckAddinRules = new RequestCheckAddinRules();
/* 1279 */         requestCheckAddinRules.resetParameter();
/* 1280 */         requestCheckAddinRules.setRequestid(n);
/* 1281 */         requestCheckAddinRules.setObjid(i1);
/* 1282 */         requestCheckAddinRules.setObjtype(1);
/* 1283 */         requestCheckAddinRules.setIsbill(k);
/* 1284 */         requestCheckAddinRules.setFormid(j);
/* 1285 */         requestCheckAddinRules.setIspreadd("0");
/* 1286 */         requestCheckAddinRules.setRequestManager(paramRequestManager);
/* 1287 */         requestCheckAddinRules.checkAddinRules();
/* 1288 */       } catch (Exception exception) {
/* 1289 */         if (exception.getMessage().indexOf("workflow interface action error") > -1) {
/* 1290 */           writeLog(exception);
/*      */         }
/* 1292 */         return false;
/*      */       } 
/* 1294 */       return true;
/* 1295 */     } catch (Exception exception) {
/* 1296 */       return false;
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private boolean updateDataOfRelatedTable(RequestManager paramRequestManager, int paramInt, List<Map> paramList, Map paramMap, String paramString1, String paramString2, String paramString3, User paramUser, Hashtable paramHashtable) {
/* 1320 */     if (paramRequestManager == null || paramInt <= 0 || paramList == null || paramString1 == null || paramString1.equals("") || paramMap == null) {
/* 1321 */       return false;
/*      */     }
/* 1323 */     RecordSet recordSet = new RecordSet();
/* 1324 */     String str1 = "";
/* 1325 */     String str2 = "";
/* 1326 */     String str3 = "";
/* 1327 */     Map map = null;
/*      */ 
/*      */     
/* 1330 */     String str4 = "";
/* 1331 */     String str5 = "";
/* 1332 */     int i = paramRequestManager.getIsbill();
/* 1333 */     int j = paramRequestManager.getBillid();
/* 1334 */     int k = paramRequestManager.getRequestid();
/* 1335 */     String str6 = paramRequestManager.getBillTableName();
/* 1336 */     String str7 = "";
/* 1337 */     String str8 = "";
/* 1338 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*      */ 
/*      */     
/*      */     try {
/* 1342 */       ArrayList<String> arrayList = new ArrayList();
/* 1343 */       Hashtable<Object, Object> hashtable = new Hashtable<>();
/*      */       
/* 1345 */       arrayList = (ArrayList)paramHashtable.get("inoperatefields");
/* 1346 */       hashtable = (Hashtable<Object, Object>)paramHashtable.get("inoperatevalue_hs");
/* 1347 */       if (arrayList != null && arrayList.size() > 0) {
/* 1348 */         String str19 = "";
/* 1349 */         for (byte b = 0; b < arrayList.size(); b++) {
/* 1350 */           str19 = str19 + (String)arrayList.get(b) + ",";
/*      */         }
/* 1352 */         str19 = str19.substring(0, str19.length() - 1);
/* 1353 */         String str20 = "";
/* 1354 */         String str21 = "";
/* 1355 */         if (i == 0) {
/* 1356 */           recordSet.executeSql(" select id,fieldName,fielddbtype,fieldhtmltype,type from workflow_formdict where id in (" + str19 + ")");
/* 1357 */           while (recordSet.next()) {
/* 1358 */             str20 = Util.null2String(recordSet.getString("fieldhtmltype"));
/* 1359 */             str21 = Util.null2String(recordSet.getString("type"));
/* 1360 */             if (str20.equals("2") && str21.equals("2")) {
/* 1361 */               hashMap1.put(Util.null2String(recordSet.getString("fieldName")), hashtable.get("inoperatevalue" + recordSet.getString("id"))); continue;
/*      */             } 
/* 1363 */             str4 = str4 + getPartOfUpdateClauseNew(recordSet.getString("fielddbtype"), recordSet.getString("fieldName"), (String)hashtable.get("inoperatevalue" + recordSet.getString("id")));
/*      */           } 
/*      */         } else {
/*      */           
/* 1367 */           recordSet.executeSql(" select id,fieldName,fielddbtype,fieldhtmltype,type from workflow_billfield where id in (" + str19 + ") and (viewtype is null or viewtype<>1)");
/* 1368 */           while (recordSet.next()) {
/* 1369 */             str20 = Util.null2String(recordSet.getString("fieldhtmltype"));
/* 1370 */             str21 = Util.null2String(recordSet.getString("type"));
/* 1371 */             if (str20.equals("2") && str21.equals("2")) {
/* 1372 */               hashMap1.put(Util.null2String(recordSet.getString("fieldName")), hashtable.get("inoperatevalue" + recordSet.getString("id"))); continue;
/*      */             } 
/* 1374 */             str4 = str4 + getPartOfUpdateClauseNew(recordSet.getString("fielddbtype"), recordSet.getString("fieldName"), (String)hashtable.get("inoperatevalue" + recordSet.getString("id")));
/*      */           } 
/*      */         } 
/*      */         
/* 1378 */         if (!str4.equals("")) {
/* 1379 */           str4 = str4.substring(0, str4.length() - 1);
/* 1380 */           if (i == 1) {
/* 1381 */             str4 = " update " + str6 + " set " + str4 + " where requestid = " + k;
/*      */           } else {
/* 1383 */             str4 = "update workflow_form set " + str4 + " where requestid=" + k;
/*      */           } 
/* 1385 */           recordSet.executeSql(str4);
/*      */         } 
/*      */         
/*      */         try {
/* 1389 */           String str22 = "";
/* 1390 */           if (i == 1) {
/* 1391 */             str22 = " update " + str6 + " set ";
/*      */           } else {
/* 1393 */             str22 = "update workflow_form set ";
/*      */           } 
/* 1395 */           byte b1 = 0;
/* 1396 */           String str23 = "";
/* 1397 */           Iterator<Map.Entry> iterator = hashMap1.entrySet().iterator();
/* 1398 */           while (iterator.hasNext()) {
/* 1399 */             b1++;
/* 1400 */             Map.Entry entry = iterator.next();
/* 1401 */             String str24 = entry.getKey().toString();
/* 1402 */             String str25 = "";
/* 1403 */             if (entry.getValue() != null)
/* 1404 */               if (String.valueOf(entry.getValue()).equals(" ")) { str25 = ""; }
/* 1405 */               else { str25 = String.valueOf(entry.getValue()); }
/*      */                
/* 1407 */             str22 = str22 + str23 + " " + str24 + "=? ";
/* 1408 */             str23 = ",";
/*      */           } 
/* 1410 */           str22 = str22 + " where requestid=" + k;
/* 1411 */           if (b1 > 0) {
/* 1412 */             ConnStatement connStatement = null;
/*      */             try {
/* 1414 */               connStatement = new ConnStatement();
/* 1415 */               connStatement.setStatementSql(str22);
/* 1416 */               iterator = hashMap1.entrySet().iterator();
/* 1417 */               b1 = 0;
/* 1418 */               while (iterator.hasNext()) {
/* 1419 */                 b1++;
/* 1420 */                 Map.Entry entry = iterator.next();
/* 1421 */                 String str24 = entry.getKey().toString();
/* 1422 */                 String str25 = "";
/* 1423 */                 if (entry.getValue() != null)
/* 1424 */                   if (String.valueOf(entry.getValue()).equals(" ")) { str25 = ""; }
/* 1425 */                   else { str25 = String.valueOf(entry.getValue()); }
/*      */                    
/* 1427 */                 connStatement.setString(b1, str25);
/*      */               } 
/* 1429 */               connStatement.executeUpdate();
/* 1430 */             } catch (Exception exception) {
/* 1431 */               writeLog(exception);
/*      */             } finally {
/* 1433 */               if (connStatement != null) connStatement.close(); 
/*      */             } 
/*      */           } 
/* 1436 */         } catch (Exception exception) {
/* 1437 */           writeLog(exception);
/*      */         } 
/* 1439 */         str4 = "";
/*      */       } 
/* 1441 */     } catch (Exception exception) {}
/*      */ 
/*      */     
/* 1444 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/*      */     
/* 1446 */     String str9 = "";
/* 1447 */     String str10 = "";
/* 1448 */     String str11 = "";
/* 1449 */     String str12 = "";
/* 1450 */     String str13 = "";
/*      */ 
/*      */     
/* 1453 */     String str14 = "";
/* 1454 */     String str15 = "";
/* 1455 */     String str16 = "";
/* 1456 */     String str17 = "";
/* 1457 */     String str18 = "";
/*      */     
/* 1459 */     hashMap1 = new HashMap<>();
/*      */     int m;
/* 1461 */     for (m = 0; m < paramList.size(); m++) {
/* 1462 */       map = paramList.get(m);
/*      */       
/* 1464 */       str1 = (String)map.get("subWorkflowFieldId");
/* 1465 */       str2 = (String)map.get("mainWorkflowFieldId");
/* 1466 */       str3 = (String)map.get("ifSplitField");
/*      */ 
/*      */       
/* 1469 */       if (str1 != null && str1.equals(paramString2)) {
/* 1470 */         str8 = paramString3;
/*      */ 
/*      */       
/*      */       }
/*      */       else {
/*      */ 
/*      */         
/* 1477 */         str8 = Util.null2String((String)paramMap.get("field" + str2));
/*      */       } 
/*      */ 
/*      */       
/* 1481 */       String str19 = "";
/* 1482 */       String str20 = "";
/* 1483 */       String str21 = "";
/* 1484 */       String str22 = "";
/* 1485 */       String str23 = "";
/*      */       
/* 1487 */       if (i == 0) {
/* 1488 */         recordSet.executeSql(" select fieldName,fielddbtype,fieldHtmlType,type from workflow_formdict where id=" + str1);
/* 1489 */         if (recordSet.next()) {
/* 1490 */           str20 = recordSet.getString(1);
/* 1491 */           str21 = recordSet.getString(2);
/* 1492 */           str22 = recordSet.getString(3);
/* 1493 */           str23 = recordSet.getString(4);
/*      */         } 
/*      */       } else {
/* 1496 */         recordSet.executeSql(" select billId,fieldName,fielddbtype,fieldHtmlType,type from workflow_billfield where id= " + str1);
/*      */         
/* 1498 */         if (recordSet.next()) {
/* 1499 */           str19 = recordSet.getString(1);
/* 1500 */           str20 = recordSet.getString(2);
/* 1501 */           str21 = recordSet.getString(3);
/* 1502 */           str22 = recordSet.getString(4);
/* 1503 */           str23 = recordSet.getString(5);
/*      */         } 
/*      */       } 
/*      */ 
/*      */       
/* 1508 */       if (str1.equals("-3")) {
/* 1509 */         str5 = str5 + ",requestName='" + Util.toHtml100(str8) + "'";
/* 1510 */         paramRequestManager.setRequestname(str8);
/* 1511 */       } else if (str1.equals("-4")) {
/* 1512 */         str5 = str5 + ",requestlevel=" + Util.getIntValue(str8, 0);
/* 1513 */         paramRequestManager.setRequestlevel(str8);
/* 1514 */       } else if (str1.equals("-5")) {
/* 1515 */         str5 = str5 + ",messageType=" + Util.getIntValue(str8, 0);
/* 1516 */         paramRequestManager.setMessageType(str8);
/* 1517 */       } else if (str22.equals("2") && str23.equals("2")) {
/* 1518 */         hashMap1.put(Util.null2String(str20), str8);
/*      */       } else {
/* 1520 */         str4 = str4 + getPartOfUpdateClause(str21, str20, str8);
/*      */       } 
/*      */       
/* 1523 */       updateRequestRelatedDataMap(hashMap2, str22, str23, str8);
/*      */       
/* 1525 */       str14 = (String)hashMap2.get("docIds");
/* 1526 */       str15 = (String)hashMap2.get("crmIds");
/* 1527 */       str16 = (String)hashMap2.get("hrmIds");
/* 1528 */       str17 = (String)hashMap2.get("prjIds");
/* 1529 */       str18 = (String)hashMap2.get("cptIds");
/*      */       
/* 1531 */       if (str14 != null && !str14.equals("")) {
/* 1532 */         str9 = str9 + "," + str14;
/*      */       }
/* 1534 */       if (str15 != null && !str15.equals("")) {
/* 1535 */         str10 = str10 + "," + str15;
/*      */       }
/* 1537 */       if (str16 != null && !str16.equals("")) {
/* 1538 */         str11 = str11 + "," + str16;
/*      */       }
/* 1540 */       if (str17 != null && !str17.equals("")) {
/* 1541 */         str12 = str12 + "," + str17;
/*      */       }
/* 1543 */       if (str18 != null && !str18.equals("")) {
/* 1544 */         str13 = str13 + "," + str18;
/*      */       }
/*      */     } 
/*      */ 
/*      */ 
/*      */     
/* 1550 */     if (!str9.equals("")) {
/* 1551 */       str9 = str9.substring(1);
/*      */     }
/* 1553 */     if (!str10.equals("")) {
/* 1554 */       str10 = str10.substring(1);
/*      */     }
/* 1556 */     if (!str11.equals("")) {
/* 1557 */       str11 = str11.substring(1);
/*      */     }
/* 1559 */     if (!str12.equals("")) {
/* 1560 */       str12 = str12.substring(1);
/*      */     }
/* 1562 */     if (!str13.equals("")) {
/* 1563 */       str13 = str13.substring(1);
/*      */     }
/*      */     
/* 1566 */     paramRequestManager.setDocids(str9);
/* 1567 */     paramRequestManager.setCrmids(str10);
/* 1568 */     paramRequestManager.setHrmids(str11);
/* 1569 */     paramRequestManager.setPrjids(str12);
/* 1570 */     paramRequestManager.setCptids(str13);
/*      */ 
/*      */     
/* 1573 */     if (!str4.equals("")) {
/* 1574 */       str4 = str4.substring(0, str4.length() - 1);
/* 1575 */       if (i == 1) {
/* 1576 */         str4 = " update " + str6 + " set " + str4 + " where requestid = " + k;
/*      */       } else {
/* 1578 */         str4 = "update workflow_form set " + str4 + " where requestid=" + k;
/*      */       } 
/* 1580 */       if (!recordSet.executeSql(str4)) {
/* 1581 */         writeLog("UpdateClause Error:" + str4);
/* 1582 */         return false;
/*      */       } 
/*      */     } 
/*      */ 
/*      */     
/*      */     try {
/* 1588 */       String str19 = "";
/* 1589 */       if (i == 1) {
/* 1590 */         str19 = " update " + str6 + " set ";
/*      */       } else {
/* 1592 */         str19 = "update workflow_form set ";
/*      */       } 
/* 1594 */       byte b = 0;
/* 1595 */       String str20 = "";
/* 1596 */       Iterator<Map.Entry> iterator = hashMap1.entrySet().iterator();
/* 1597 */       while (iterator.hasNext()) {
/* 1598 */         b++;
/* 1599 */         Map.Entry entry = iterator.next();
/* 1600 */         String str21 = entry.getKey().toString();
/* 1601 */         String str22 = "";
/* 1602 */         if (entry.getValue() != null)
/* 1603 */           if (String.valueOf(entry.getValue()).equals(" ")) { str22 = ""; }
/* 1604 */           else { str22 = String.valueOf(entry.getValue()); }
/*      */            
/* 1606 */         str19 = str19 + str20 + " " + str21 + "=? ";
/* 1607 */         str20 = ",";
/*      */       } 
/* 1609 */       str19 = str19 + " where requestid=" + k;
/* 1610 */       if (b > 0) {
/* 1611 */         ConnStatement connStatement = null;
/*      */         try {
/* 1613 */           connStatement = new ConnStatement();
/* 1614 */           connStatement.setStatementSql(str19);
/* 1615 */           iterator = hashMap1.entrySet().iterator();
/* 1616 */           b = 0;
/* 1617 */           while (iterator.hasNext()) {
/* 1618 */             b++;
/* 1619 */             Map.Entry entry = iterator.next();
/* 1620 */             String str21 = entry.getKey().toString();
/* 1621 */             String str22 = "";
/* 1622 */             if (entry.getValue() != null)
/* 1623 */               if (String.valueOf(entry.getValue()).equals(" ")) { str22 = ""; }
/* 1624 */               else { str22 = String.valueOf(entry.getValue()); }
/*      */                
/* 1626 */             connStatement.setString(b, str22);
/*      */           } 
/* 1628 */           connStatement.executeUpdate();
/* 1629 */         } catch (Exception exception) {
/* 1630 */           writeLog(exception);
/*      */         } finally {
/* 1632 */           if (connStatement != null) connStatement.close(); 
/*      */         } 
/*      */       } 
/* 1635 */     } catch (Exception exception) {
/* 1636 */       writeLog(exception);
/*      */     } 
/*      */     
/* 1639 */     if (!str5.equals("")) {
/* 1640 */       str5 = str5.substring(1);
/*      */       
/* 1642 */       str5 = "update workflow_requestbase set " + str5 + " where requestid=" + k;
/* 1643 */       if (!recordSet.executeSql(str5)) {
/* 1644 */         writeLog("UpdateClause Error:" + str5);
/* 1645 */         return false;
/*      */       } 
/*      */     } 
/*      */ 
/*      */     
/* 1650 */     if (!recordSet.executeSql("update workflow_requestbase set docids='" + str9 + "',crmids='" + str10 + "',hrmids='" + str11 + "',prjids='" + str12 + "',cptids='" + str13 + "' where requestid=" + k)) {
/* 1651 */       return false;
/*      */     }
/*      */     
/*      */     try {
/* 1655 */       m = paramRequestManager.getWorkflowid();
/* 1656 */       int n = paramRequestManager.getNodeid();
/* 1657 */       int i1 = paramRequestManager.getIsbill();
/* 1658 */       User user = paramRequestManager.getUser();
/*      */       
/* 1660 */       RequestAddShareInfo requestAddShareInfo = new RequestAddShareInfo();
/* 1661 */       requestAddShareInfo.setRequestid(k);
/* 1662 */       requestAddShareInfo.SetWorkFlowID(m);
/* 1663 */       requestAddShareInfo.SetNowNodeID(n);
/* 1664 */       requestAddShareInfo.SetNextNodeID(n);
/* 1665 */       requestAddShareInfo.setIsbill(i1);
/* 1666 */       requestAddShareInfo.setUser(user);
/* 1667 */       requestAddShareInfo.SetIsWorkFlow(1);
/* 1668 */       requestAddShareInfo.setBillTableName(str6);
/* 1669 */       requestAddShareInfo.setHaspassnode(false);
/* 1670 */       boolean bool = false;
/* 1671 */       if (paramUser != null && user != null && paramUser.getUID() != user.getUID()) {
/* 1672 */         bool = true;
/*      */       }
/* 1674 */       requestAddShareInfo.setIsNewUser(bool);
/*      */       
/* 1676 */       requestAddShareInfo.addShareInfo();
/* 1677 */     } catch (Exception exception) {
/* 1678 */       return false;
/*      */     } 
/*      */ 
/*      */     
/* 1682 */     return true;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private boolean updateDltDataOfRelatedTable(RequestManager paramRequestManager, int paramInt, List<Map> paramList, String paramString1, String paramString2, Hashtable paramHashtable) {
/* 1701 */     if (paramRequestManager == null || paramInt <= 0 || paramList == null || paramString1 == null || paramString1.equals("")) {
/* 1702 */       return false;
/*      */     }
/* 1704 */     RecordSet recordSet = new RecordSet();
/*      */     
/* 1706 */     String str1 = "";
/* 1707 */     String str2 = "";
/* 1708 */     Map map = null;
/* 1709 */     int i = paramRequestManager.getIsbill();
/* 1710 */     String str3 = "";
/* 1711 */     for (byte b = 0; b < paramList.size(); b++) {
/* 1712 */       Map map1 = new HashMap<>();
/* 1713 */       ArrayList arrayList = new ArrayList();
/* 1714 */       map = paramList.get(b);
/* 1715 */       ArrayList<Map> arrayList1 = (ArrayList)map.get("subworkflowdetailtables");
/* 1716 */       ArrayList<Map> arrayList2 = (ArrayList)map.get("mainworkflowdetailtables");
/*      */       byte b1;
/* 1718 */       for (b1 = 0; b1 < arrayList2.size(); b1++) {
/* 1719 */         map = arrayList2.get(b1);
/* 1720 */         String str4 = (String)map.get("mainworkflowdetailtable");
/* 1721 */         String str5 = (String)map.get("mainworkflowfields");
/* 1722 */         String str6 = (String)map.get("mainworkflowdltkey");
/* 1723 */         map1 = getVauleOfDltField(paramInt, Util.getIntValue(paramString1, 0), str4, str5, map1, str6);
/*      */       } 
/*      */       
/* 1726 */       for (b1 = 0; b1 < arrayList1.size(); b1++) {
/* 1727 */         map = arrayList1.get(b1);
/* 1728 */         String str4 = (String)map.get("subworkflowdetailtable");
/* 1729 */         String str5 = (String)map.get("subworkflowfields");
/* 1730 */         String str6 = (String)map.get("subworkflowdltkey");
/*      */         
/* 1732 */         ArrayList<String> arrayList3 = new ArrayList();
/* 1733 */         ArrayList<String> arrayList4 = new ArrayList();
/* 1734 */         ArrayList<String> arrayList5 = new ArrayList();
/* 1735 */         ArrayList<String> arrayList6 = new ArrayList();
/* 1736 */         if (str5 != null && !str5.trim().equals("") && !str4.trim().equals("")) {
/* 1737 */           if (i == 0) {
/* 1738 */             str3 = " select b.mainworkflowfieldid,a.fieldName,a.fielddbtype,fieldhtmltype from workflow_formdictdetail a,workflow_SubwfSetDetail b where a.id=b.subworkflowfieldid and b.isdetail=1 and a.id in(" + str5 + ") and b.subwfSetId=" + paramString2;
/*      */           } else {
/* 1740 */             str3 = " select b.mainworkflowfieldid,a.fieldName,a.fielddbtype,fieldhtmltype from workflow_billfield a,workflow_SubwfSetDetail b where a.id=b.subworkflowfieldid and b.isdetail=1 and a.viewtype=1 and a.id in( " + str5 + ") and b.subwfSetId=" + paramString2;
/*      */           } 
/* 1742 */           recordSet.executeSql(str3);
/* 1743 */           while (recordSet.next()) {
/* 1744 */             arrayList5.add(Util.null2String(recordSet.getString(1)));
/* 1745 */             arrayList4.add(Util.null2String(recordSet.getString(2)));
/* 1746 */             arrayList3.add(Util.null2String(recordSet.getString(3)));
/* 1747 */             arrayList6.add(Util.null2String(recordSet.getString(4)));
/*      */           } 
/* 1749 */           ArrayList<String> arrayList7 = getInsertClause(paramRequestManager, arrayList3, arrayList6, arrayList4, arrayList5, map1, str4, str6, paramHashtable);
/* 1750 */           for (byte b2 = 0; b2 < arrayList7.size(); b2++) {
/* 1751 */             if (!recordSet.executeSql(arrayList7.get(b2))) {
/* 1752 */               writeLog("InsertClause Error:" + arrayList7.get(b2));
/* 1753 */               return false;
/*      */             } 
/*      */           } 
/*      */         } 
/*      */       } 
/*      */     } 
/* 1759 */     return true;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private String getValueOfSingleField(int paramInt1, int paramInt2, int paramInt3) {
/* 1773 */     String str1 = "";
/* 1774 */     RecordSet recordSet = new RecordSet();
/*      */ 
/*      */     
/* 1777 */     String str2 = "";
/* 1778 */     String str3 = "";
/* 1779 */     recordSet.executeSql(" select formId,isBill from workflow_base where id=" + paramInt2);
/* 1780 */     if (recordSet.next()) {
/* 1781 */       str2 = recordSet.getString(1);
/* 1782 */       str3 = recordSet.getString(2);
/*      */     } 
/*      */ 
/*      */     
/* 1786 */     String str4 = "";
/* 1787 */     String str5 = "";
/* 1788 */     String str6 = "";
/* 1789 */     String str7 = "";
/* 1790 */     String str8 = "workflow_form";
/* 1791 */     String str9 = "";
/* 1792 */     if ("0".equals(str3)) {
/* 1793 */       str9 = " select fieldName,fieldHtmlType,type from workflow_formdict where id=" + paramInt3;
/*      */     } else {
/* 1795 */       recordSet.executeSql(" select tableName from workflow_bill where id=" + str2);
/* 1796 */       if (recordSet.next()) {
/* 1797 */         str8 = recordSet.getString(1);
/*      */       }
/* 1799 */       str9 = " select billId,fieldName,fieldHtmlType,type from workflow_billfield where (viewtype is null or viewtype<>1) and id= " + paramInt3;
/*      */     } 
/* 1801 */     recordSet.executeSql(str9);
/* 1802 */     if (recordSet.next()) {
/* 1803 */       str4 = recordSet.getString(1);
/* 1804 */       str5 = recordSet.getString(2);
/* 1805 */       str6 = recordSet.getString(3);
/*      */     } 
/*      */     
/* 1808 */     if (str4 != null && !str4.trim().equals("") && str8 != null && 
/* 1809 */       !str8.trim().equals("")) {
/*      */       
/* 1811 */       recordSet.executeSql(" select " + str4 + " from " + str8 + " where requestid= " + paramInt1);
/* 1812 */       if (recordSet.next()) {
/* 1813 */         str7 = recordSet.getString(1);
/*      */       }
/*      */     } 
/*      */     
/* 1817 */     if (str5 == null || str5.equals("") || str6 == null || str6
/* 1818 */       .equals("") || str7 == null || str7
/* 1819 */       .equals("")) {
/*      */       
/* 1821 */       str1 = "";
/*      */     } else {
/* 1823 */       str1 = str5 + "_" + str6 + "_" + str7;
/*      */     } 
/*      */     
/* 1826 */     return str1;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private Map getValueOfField(RequestManager paramRequestManager, int paramInt) {
/* 1839 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 1840 */     RecordSet recordSet = new RecordSet();
/*      */ 
/*      */     
/* 1843 */     String str1 = "";
/* 1844 */     String str2 = "";
/* 1845 */     recordSet.executeSql(" select formId,isBill from workflow_base where id=" + paramInt);
/* 1846 */     if (recordSet.next()) {
/* 1847 */       str1 = recordSet.getString(1);
/* 1848 */       str2 = recordSet.getString(2);
/*      */     } 
/*      */     
/* 1851 */     int i = paramRequestManager.getRequestid();
/*      */     
/* 1853 */     hashMap1.put("field-2", "" + i);
/* 1854 */     hashMap1.put("field-3", paramRequestManager.getRequestname());
/* 1855 */     hashMap1.put("field-4", paramRequestManager.getRequestlevel());
/* 1856 */     hashMap1.put("field-5", paramRequestManager.getMessageType());
/*      */ 
/*      */     
/* 1859 */     String str3 = "";
/* 1860 */     String str4 = "";
/* 1861 */     String str5 = "";
/* 1862 */     String str6 = "";
/* 1863 */     String str7 = "";
/* 1864 */     String str8 = "workflow_form";
/*      */     
/* 1866 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 1867 */     String str9 = "";
/* 1868 */     byte b = 0;
/* 1869 */     String str10 = "";
/*      */     
/* 1871 */     if ("0".equals(str2)) {
/* 1872 */       str10 = "select b.id,b.fieldName,b.fieldHtmlType,b.type from workflow_formfield a,workflow_formdict b where a.fieldId=b.id and  (a.isdetail<>'1' or a.isdetail is null) and  a.formId=" + str1;
/*      */     } else {
/* 1874 */       recordSet.executeSql(" select tableName from workflow_bill where id=" + str1);
/* 1875 */       if (recordSet.next()) {
/* 1876 */         str8 = Util.null2String(recordSet.getString("tableName"));
/*      */       }
/* 1878 */       str10 = "select id,fieldName,fieldHtmlType,type from workflow_billfield where (viewtype is null or viewtype<>1) and billId=" + str1;
/*      */     } 
/* 1880 */     recordSet.executeSql(str10);
/* 1881 */     while (recordSet.next()) {
/* 1882 */       str3 = Util.null2String(recordSet.getString("id"));
/* 1883 */       str4 = Util.null2String(recordSet.getString("fieldName"));
/* 1884 */       str6 = Util.null2String(recordSet.getString("fieldHtmlType"));
/* 1885 */       str7 = Util.null2String(recordSet.getString("type"));
/*      */       
/* 1887 */       if (!str4.equals("")) {
/* 1888 */         if (str9.equals("")) {
/* 1889 */           str9 = str4;
/*      */         } else {
/* 1891 */           str9 = str9 + "," + str4;
/*      */         } 
/* 1893 */         hashMap2.put("col" + b, str3);
/* 1894 */         b++;
/*      */       } 
/*      */       
/* 1897 */       hashMap1.put("fieldHtmlType" + str3, str6);
/* 1898 */       hashMap1.put("type" + str3, str7);
/*      */     } 
/*      */     
/* 1901 */     if (str9.equals("") || str8.equals("")) {
/* 1902 */       return hashMap1;
/*      */     }
/*      */     
/* 1905 */     str10 = "select " + str9 + " from " + str8 + " where requestid= " + i;
/* 1906 */     recordSet.executeSql(str10);
/* 1907 */     while (recordSet.next()) {
/* 1908 */       for (byte b1 = 0; b1 < b; b1++) {
/* 1909 */         str5 = Util.null2String(recordSet.getString(b1 + 1));
/* 1910 */         hashMap1.put("field" + hashMap2.get("col" + b1), str5);
/*      */       } 
/*      */     } 
/*      */     
/* 1914 */     return hashMap1;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private Map getVauleOfDltField(int paramInt1, int paramInt2, String paramString1, String paramString2, Map<String, ArrayList> paramMap, String paramString3) {
/* 1928 */     RecordSet recordSet = new RecordSet();
/*      */ 
/*      */     
/* 1931 */     String str1 = "";
/* 1932 */     String str2 = "";
/* 1933 */     recordSet.executeSql(" select formId,isBill from workflow_base where id=" + paramInt2);
/* 1934 */     if (recordSet.next()) {
/* 1935 */       str1 = recordSet.getString(1);
/* 1936 */       str2 = recordSet.getString(2);
/*      */     } 
/*      */ 
/*      */     
/* 1940 */     String str3 = "";
/* 1941 */     String str4 = "";
/* 1942 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 1943 */     String str5 = "";
/* 1944 */     String str6 = "";
/* 1945 */     byte b = 0;
/* 1946 */     if ("0".equals(str2)) {
/* 1947 */       str6 = " select fieldName,id from workflow_formdictdetail where id in(" + paramString2 + ") order by id";
/*      */     } else {
/* 1949 */       str6 = " select fieldName,id from workflow_billfield where viewtype=1 and id in(" + paramString2 + ") order by id";
/*      */     } 
/* 1951 */     recordSet.executeSql(str6);
/* 1952 */     while (recordSet.next()) {
/* 1953 */       str3 = Util.null2String(recordSet.getString(1));
/* 1954 */       str4 = recordSet.getString(2);
/* 1955 */       if (!str3.equals("")) {
/* 1956 */         if (str5.equals("")) {
/* 1957 */           str5 = str3;
/*      */         } else {
/* 1959 */           str5 = str5 + "," + str3;
/*      */         } 
/* 1961 */         hashMap.put("col" + b, str4);
/* 1962 */         b++;
/*      */       } 
/*      */     } 
/* 1965 */     if (!str5.equals("")) {
/* 1966 */       ArrayList[] arrayOfArrayList = new ArrayList[b]; byte b1;
/* 1967 */       for (b1 = 0; b1 < b; b1++) {
/* 1968 */         arrayOfArrayList[b1] = new ArrayList();
/*      */       }
/* 1970 */       b1 = 0;
/* 1971 */       String str7 = "";
/* 1972 */       String str8 = "";
/* 1973 */       if ("1".equals(str2)) {
/* 1974 */         if (recordSet.getDBType().toUpperCase().equals("ORACLE")) {
/* 1975 */           str6 = "select * from " + paramString1 + " where rownum<2";
/* 1976 */         } else if (recordSet.getDBType().equals("mysql")) {
/* 1977 */           str6 = "select * from " + paramString1 + " limit 1";
/*      */         }
/* 1979 */         else if (recordSet.getDBType().equals("postgresql")) {
/* 1980 */           str6 = "select * from " + paramString1 + " limit 1";
/*      */         } else {
/*      */           
/* 1983 */           str6 = "select top 1 * from " + paramString1;
/*      */         } 
/* 1985 */         if (recordSet.executeSql(str6)) {
/* 1986 */           String[] arrayOfString = recordSet.getColumnName();
/* 1987 */           if (arrayOfString != null) {
/* 1988 */             for (byte b3 = 0; b3 < arrayOfString.length; b3++) {
/* 1989 */               if (arrayOfString[b3].toUpperCase().equals("REQUESTID")) {
/* 1990 */                 b1 = 1;
/*      */                 break;
/*      */               } 
/*      */             } 
/*      */           }
/*      */         } 
/* 1996 */         if (b1 == 0) {
/*      */           
/* 1998 */           str6 = "select c.tablename,c.DETAILKEYFIELD from workflow_base b,workflow_bill c where b.FORMID=c.ID and b.ISBILL='1' and b.id=" + paramInt2;
/* 1999 */           recordSet.executeSql(str6);
/* 2000 */           if (recordSet.next()) {
/* 2001 */             str7 = Util.null2String(recordSet.getString(1));
/* 2002 */             str8 = Util.null2String(recordSet.getString(2));
/*      */           } 
/*      */         } 
/* 2005 */         if (b1 != 0) {
/* 2006 */           str6 = "select " + str5 + " from " + paramString1 + " where requestid= " + paramInt1;
/*      */         } else {
/* 2008 */           str6 = "select " + str5 + " from " + paramString1 + " where " + str8 + "= (select id from " + str7 + " where requestid= " + paramInt1 + ")";
/*      */         } 
/*      */       } else {
/* 2011 */         str6 = "select " + str5 + " from " + paramString1 + " where requestid= " + paramInt1 + " and groupid=" + Util.getIntValue(paramString3, 0);
/*      */       } 
/* 2013 */       str6 = str6 + " order by id asc";
/* 2014 */       recordSet.executeSql(str6);
/* 2015 */       while (recordSet.next()) {
/* 2016 */         for (byte b3 = 0; b3 < b; b3++) {
/* 2017 */           String str = Util.null2String(recordSet.getString(b3 + 1));
/* 2018 */           arrayOfArrayList[b3].add(str);
/*      */         } 
/*      */       } 
/* 2021 */       for (byte b2 = 0; b2 < b; b2++) {
/* 2022 */         paramMap.put("field" + hashMap.get("col" + b2), arrayOfArrayList[b2]);
/*      */       }
/*      */     } 
/*      */     
/* 2026 */     return paramMap;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private String getPartOfUpdateClause(String paramString1, String paramString2, String paramString3) {
/* 2039 */     String str = "";
/* 2040 */     if (paramString3 != null && !paramString3.trim().equals("") && paramString1 != null && !paramString1.trim().equals("") && paramString2 != null && !paramString2.trim().equals("")) {
/* 2041 */       if (paramString1.toUpperCase().indexOf("INT") >= 0) {
/* 2042 */         paramString3 = "" + Util.getIntValue(paramString3, 0);
/* 2043 */       } else if (paramString1.toUpperCase().indexOf("DECIMAL") >= 0 || paramString1.toUpperCase().indexOf("NUMBER") >= 0) {
/* 2044 */         paramString3 = "" + Util.getDoubleValue(paramString3, 0.0D);
/*      */       } else {
/* 2046 */         paramString3 = "'" + Util.convertInput2DB(paramString3) + "'";
/*      */       } 
/* 2048 */       str = paramString2 + "=" + paramString3 + ",";
/*      */     } 
/*      */     
/* 2051 */     return str;
/*      */   }
/*      */   
/*      */   private String getPartOfUpdateClauseNew(String paramString1, String paramString2, String paramString3) {
/* 2055 */     String str = "";
/* 2056 */     if (paramString3 != null && !paramString3.trim().equals("") && paramString1 != null && !paramString1.trim().equals("") && paramString2 != null && !paramString2.trim().equals("")) {
/* 2057 */       if (paramString1.toUpperCase().indexOf("INT") >= 0) {
/* 2058 */         paramString3 = "" + Util.getIntValue(paramString3, 0);
/* 2059 */       } else if (paramString1.toUpperCase().indexOf("DECIMAL") >= 0 || paramString1.toUpperCase().indexOf("NUMBER") >= 0) {
/* 2060 */         paramString3 = "" + Util.getDoubleValue(paramString3, 0.0D);
/*      */       } else {
/* 2062 */         paramString3 = "'" + Util.convertInput2DB(paramString3) + "'";
/*      */       } 
/* 2064 */       str = paramString2 + "=" + paramString3 + ",";
/*      */     } 
/*      */     
/* 2067 */     return str;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private ArrayList getInsertClause(RequestManager paramRequestManager, ArrayList<String> paramArrayList1, ArrayList<String> paramArrayList2, ArrayList<String> paramArrayList3, ArrayList paramArrayList4, Map paramMap, String paramString1, String paramString2, Hashtable paramHashtable) {
/* 2082 */     ArrayList<String> arrayList = new ArrayList();
/* 2083 */     RecordSet recordSet = new RecordSet();
/* 2084 */     if (paramRequestManager != null && paramArrayList1 != null && paramArrayList3 != null && paramArrayList4 != null && paramMap != null && paramString1 != null && !paramString1.trim().equals("")) {
/* 2085 */       int i = paramRequestManager.getIsbill();
/*      */       
/* 2087 */       ArrayList<String> arrayList1 = new ArrayList();
/* 2088 */       ArrayList<String> arrayList2 = new ArrayList();
/*      */       try {
/* 2090 */         ArrayList<String> arrayList6 = new ArrayList();
/* 2091 */         Hashtable<Object, Object> hashtable = new Hashtable<>();
/*      */         
/* 2093 */         arrayList6 = (ArrayList)paramHashtable.get("inoperatefields");
/* 2094 */         hashtable = (Hashtable<Object, Object>)paramHashtable.get("inoperatevalue_hs");
/* 2095 */         if (arrayList6 != null && arrayList6.size() > 0) {
/* 2096 */           String str = "";
/* 2097 */           for (byte b1 = 0; b1 < arrayList6.size(); b1++) {
/* 2098 */             str = str + (String)arrayList6.get(b1) + ",";
/*      */           }
/* 2100 */           str = str.substring(0, str.length() - 1);
/* 2101 */           if (i == 0) {
/* 2102 */             recordSet.executeSql(" select id,fieldName,fielddbtype,fieldhtmltype,'workflow_formdetail' as detailTable from workflow_formdictdetail where id in (" + str + ")");
/*      */           } else {
/* 2104 */             recordSet.executeSql(" select id,fieldName,fielddbtype,fieldhtmltype,detailTable from workflow_billfield where id in (" + str + ") and viewtype=1");
/*      */           } 
/* 2106 */           while (recordSet.next()) {
/* 2107 */             String str1 = Util.null2String(recordSet.getString("fieldName"));
/* 2108 */             String str2 = Util.null2String(recordSet.getString("detailTable"));
/* 2109 */             if (!str2.equals("") && !str2.equals(paramString1)) {
/*      */               continue;
/*      */             }
/*      */             
/* 2113 */             if (!paramArrayList3.contains(str1)) {
/* 2114 */               arrayList1.add(str1);
/* 2115 */               String str3 = Util.null2String(recordSet.getString("fielddbtype"));
/* 2116 */               String str4 = Util.null2String(recordSet.getString("fieldhtmltype"));
/* 2117 */               String str5 = Util.null2String((String)hashtable.get("inoperatevalue" + recordSet.getString("id")));
/* 2118 */               if (str3.toUpperCase().indexOf("INT") >= 0) {
/* 2119 */                 if (str4.equals("5")) {
/* 2120 */                   str5 = "" + str5;
/*      */                 } else {
/* 2122 */                   str5 = "" + Util.getIntValue(str5, 0);
/*      */                 } 
/* 2124 */               } else if (str3.toUpperCase().indexOf("DECIMAL") >= 0 || str3.toUpperCase().indexOf("NUMBER") >= 0) {
/* 2125 */                 str5 = "" + Util.getDoubleValue(str5, 0.0D);
/*      */               } else {
/* 2127 */                 str5 = "'" + Util.convertInput2DB(str5) + "'";
/*      */               } 
/* 2129 */               arrayList2.add(str5);
/*      */             } 
/*      */           } 
/*      */         } 
/* 2133 */       } catch (Exception exception) {}
/*      */       
/* 2135 */       ArrayList arrayList3 = new ArrayList();
/* 2136 */       ArrayList arrayList4 = new ArrayList();
/* 2137 */       int j = 0;
/* 2138 */       ArrayList<ArrayList> arrayList5 = new ArrayList();
/*      */       byte b;
/* 2140 */       for (b = 0; b < paramArrayList3.size(); b++) {
/* 2141 */         ArrayList arrayList6 = (ArrayList)paramMap.get("field" + paramArrayList4.get(b));
/* 2142 */         arrayList5.add(arrayList6);
/* 2143 */         if (arrayList6 != null && arrayList6.size() > j) j = arrayList6.size(); 
/*      */       } 
/* 2145 */       for (b = 0; b < j; b++) {
/* 2146 */         String str1 = "";
/* 2147 */         String str2 = "";
/*      */         byte b1;
/* 2149 */         for (b1 = 0; b1 < paramArrayList3.size(); b1++) {
/* 2150 */           String str3 = Util.null2String(paramArrayList3.get(b1));
/* 2151 */           String str4 = Util.null2String(paramArrayList1.get(b1));
/* 2152 */           String str5 = Util.null2String(paramArrayList2.get(b1));
/* 2153 */           ArrayList<String> arrayList6 = arrayList5.get(b1);
/* 2154 */           if (arrayList6 != null && !str4.trim().equals("") && !str3.trim().equals("") && arrayList6.size() > b) {
/* 2155 */             String str = Util.null2String(arrayList6.get(b));
/* 2156 */             if (str != null && !str.trim().equals("") && str4.toUpperCase().indexOf("INT") >= 0) {
/* 2157 */               if (str5.equals("5")) {
/* 2158 */                 str = "" + str;
/*      */               } else {
/* 2160 */                 str = "" + Util.getIntValue(str, 0);
/*      */               } 
/* 2162 */             } else if ((str != null && !str.trim().equals("") && str4.toUpperCase().indexOf("DECIMAL") >= 0) || str4.toUpperCase().indexOf("NUMBER") >= 0) {
/* 2163 */               str = "" + Util.getDoubleValue(str, 0.0D);
/*      */ 
/*      */             
/*      */             }
/* 2167 */             else if (str4.toUpperCase().indexOf("INT") >= 0 || str4.toUpperCase().indexOf("DECIMAL") >= 0 || str4.toUpperCase().indexOf("NUMBER") >= 0) {
/* 2168 */               str = "";
/*      */             } else {
/* 2170 */               str = "'" + Util.convertInput2DB(str) + "'";
/*      */             } 
/*      */             
/* 2173 */             if (str.equals("")) {
/* 2174 */               str = "null";
/*      */             }
/* 2176 */             if (!str1.equals("")) {
/* 2177 */               str1 = str1 + "," + str3;
/* 2178 */               str2 = str2 + "," + str;
/*      */             } else {
/* 2180 */               str1 = str3;
/* 2181 */               str2 = str;
/*      */             } 
/* 2183 */             if (arrayList1.contains(str3)) {
/* 2184 */               int k = arrayList1.indexOf(str3);
/* 2185 */               arrayList1.remove(k);
/* 2186 */               arrayList2.remove(k);
/*      */             } 
/*      */           } 
/*      */         } 
/*      */         
/* 2191 */         if (arrayList1.size() > 0) {
/* 2192 */           for (b1 = 0; b1 < arrayList1.size(); b1++) {
/* 2193 */             if (!"".equals(str1)) {
/* 2194 */               str1 = str1 + "," + (String)arrayList1.get(b1);
/* 2195 */               str2 = str2 + "," + (String)arrayList2.get(b1);
/*      */             } else {
/* 2197 */               str1 = arrayList1.get(b1);
/* 2198 */               str2 = arrayList2.get(b1);
/*      */             } 
/*      */           } 
/*      */         }
/* 2202 */         if (!str1.equals("")) {
/* 2203 */           if (i == 1) {
/*      */             
/* 2205 */             String str = "";
/* 2206 */             if (recordSet.getDBType().toUpperCase().equals("ORACLE")) {
/* 2207 */               str = "select * from " + paramString1 + " where rownum<2";
/* 2208 */             } else if (recordSet.getDBType().equals("mysql")) {
/* 2209 */               str = "select * from " + paramString1 + " limit 1";
/*      */             }
/* 2211 */             else if (recordSet.getDBType().equals("postgresql")) {
/* 2212 */               str = "select * from " + paramString1 + " limit 1";
/*      */             } else {
/*      */               
/* 2215 */               str = "select top 1 * from " + paramString1;
/*      */             } 
/* 2217 */             if (recordSet.executeSql(str)) {
/* 2218 */               String[] arrayOfString = recordSet.getColumnName();
/* 2219 */               if (arrayOfString != null) {
/* 2220 */                 for (byte b2 = 0; b2 < arrayOfString.length; b2++) {
/* 2221 */                   if (arrayOfString[b2].toUpperCase().equals("REQUESTID")) {
/* 2222 */                     str1 = str1 + ",requestid";
/* 2223 */                     str2 = str2 + "," + paramRequestManager.getRequestid();
/*      */                     
/*      */                     break;
/*      */                   } 
/*      */                 } 
/*      */               }
/*      */             } 
/* 2230 */             if (paramString2 != null && !paramString2.trim().equals("")) {
/* 2231 */               str1 = str1 + "," + paramString2;
/* 2232 */               if (paramRequestManager.getFormid() == 201 || paramRequestManager.getFormid() == 19) {
/* 2233 */                 str2 = str2 + "," + paramRequestManager.getRequestid();
/*      */               } else {
/* 2235 */                 str2 = str2 + "," + paramRequestManager.getBillid();
/*      */               } 
/*      */             } 
/*      */           } else {
/*      */             
/* 2240 */             str1 = str1 + ",groupid";
/* 2241 */             str2 = str2 + "," + Util.getIntValue(paramString2, 0);
/* 2242 */             str1 = str1 + ",requestid";
/* 2243 */             str2 = str2 + "," + paramRequestManager.getRequestid();
/*      */           } 
/* 2245 */           arrayList.add("Insert into " + paramString1 + " (" + str1 + ") values(" + str2 + ")");
/*      */         } 
/*      */       } 
/*      */     } 
/* 2249 */     return arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private void updateRequestRelatedDataMap(Map<String, String> paramMap, String paramString1, String paramString2, String paramString3) {
/* 2264 */     String str1 = "";
/* 2265 */     String str2 = "";
/* 2266 */     String str3 = "";
/* 2267 */     String str4 = "";
/* 2268 */     String str5 = "";
/*      */ 
/*      */     
/* 2271 */     boolean bool = true;
/*      */     
/* 2273 */     if (paramString3 == null || paramString3.equals("") || paramString1 == null || paramString1
/* 2274 */       .equals("") || paramString2 == null || paramString2
/* 2275 */       .equals("")) {
/*      */       
/* 2277 */       bool = false;
/*      */     } else {
/* 2279 */       int i = 0;
/* 2280 */       String str = "";
/* 2281 */       ArrayList<String> arrayList = Util.TokenizerString(paramString3, ",");
/* 2282 */       for (byte b = 0; b < arrayList.size(); b++) {
/* 2283 */         str = arrayList.get(b);
/*      */         try {
/* 2285 */           i = Integer.parseInt(str);
/* 2286 */         } catch (NumberFormatException numberFormatException) {
/* 2287 */           bool = false;
/*      */           
/*      */           break;
/*      */         } 
/*      */       } 
/*      */     } 
/* 2293 */     if (!bool) {
/* 2294 */       paramMap.put("docIds", str1);
/* 2295 */       paramMap.put("crmIds", str2);
/* 2296 */       paramMap.put("hrmIds", str3);
/* 2297 */       paramMap.put("prjIds", str4);
/* 2298 */       paramMap.put("cptIds", str5);
/*      */       
/*      */       return;
/*      */     } 
/* 2302 */     if (paramString1.equals("3") && (paramString2
/* 2303 */       .equals("9") || paramString2.equals("37"))) {
/*      */       
/* 2305 */       str1 = paramString3;
/* 2306 */     } else if (paramString1.equals("3") && (paramString2
/* 2307 */       .equals("7") || paramString2.equals("18"))) {
/* 2308 */       str2 = paramString3;
/* 2309 */     } else if (paramString1.equals("3") && (paramString2
/* 2310 */       .equals("1") || paramString2.equals("17") || paramString2.equals("166"))) {
/* 2311 */       str3 = paramString3;
/* 2312 */     } else if (paramString1.equals("3") && (paramString2
/* 2313 */       .equals("8") || paramString2.equals("135"))) {
/* 2314 */       str4 = paramString3;
/* 2315 */     } else if (paramString1.equals("3") && paramString2
/* 2316 */       .equals("23")) {
/* 2317 */       str5 = paramString3;
/*      */     } 
/*      */     
/* 2320 */     paramMap.put("docIds", str1);
/* 2321 */     paramMap.put("crmIds", str2);
/* 2322 */     paramMap.put("hrmIds", str3);
/* 2323 */     paramMap.put("prjIds", str4);
/* 2324 */     paramMap.put("cptIds", str5);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private Hashtable getPreAddRule_hs(RequestManager paramRequestManager) {
/* 2339 */     RequestPreAddinoperateManager requestPreAddinoperateManager = new RequestPreAddinoperateManager();
/*      */     
/* 2341 */     requestPreAddinoperateManager.setCreater(paramRequestManager.getUser().getUID());
/* 2342 */     requestPreAddinoperateManager.setOptor(paramRequestManager.getUser().getUID());
/* 2343 */     requestPreAddinoperateManager.setWorkflowid(paramRequestManager.getWorkflowid());
/* 2344 */     requestPreAddinoperateManager.setNodeid(paramRequestManager.getNodeid());
/* 2345 */     return requestPreAddinoperateManager.getPreAddRule();
/*      */   }
/*      */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/RequestSubwfTriggerManager.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */