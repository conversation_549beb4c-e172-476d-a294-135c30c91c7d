/*     */ package weaver.workflow.request;
/*     */ 
/*     */ import java.util.List;
/*     */ import weaver.general.Util;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HumanResourceWorkflowMapping
/*     */ {
/*     */   private String mappingId;
/*     */   private String humanResource;
/*     */   private String workflowId;
/*     */   private String isStopCreateNode;
/*     */   private String creatorSourceType;
/*     */   private String creatorSource;
/*     */   private String creatorType;
/*     */   private String creatorFieldId;
/*     */   private String creatorFieldType;
/*     */   private String creatorFieldValue;
/*     */   private String isCreateForAnyone;
/*     */   private String isUnitSplit;
/*     */   private List mainTableFieldMappings;
/*     */   private List detailTableFieldMappings;
/*     */   
/*     */   public boolean hasSplitField() {
/*     */     byte b;
/*  38 */     for (b = 0; b < this.mainTableFieldMappings.size(); b++) {
/*  39 */       SubWorkflowTriggerService.FieldMapping fieldMapping = this.mainTableFieldMappings.get(b);
/*     */       
/*  41 */       if ("1".equals(fieldMapping.getIsSplit())) {
/*  42 */         return true;
/*     */       }
/*     */     } 
/*     */ 
/*     */     
/*  47 */     for (b = 0; b < this.detailTableFieldMappings.size(); b++) {
/*  48 */       SubWorkflowTriggerService.DetailTableFieldMappings detailTableFieldMappings = this.detailTableFieldMappings.get(b);
/*  49 */       List<SubWorkflowTriggerService.FieldMapping> list = detailTableFieldMappings.getFieldMappings();
/*  50 */       for (byte b1 = 0; b1 < list.size(); b1++) {
/*  51 */         SubWorkflowTriggerService.FieldMapping fieldMapping = list.get(b);
/*     */         
/*  53 */         if ("1".equals(fieldMapping.getIsSplit())) {
/*  54 */           return true;
/*     */         }
/*     */       } 
/*     */     } 
/*     */     
/*  59 */     return false;
/*     */   }
/*     */   
/*     */   public String getIsUnitSplit() {
/*  63 */     return this.isUnitSplit;
/*     */   }
/*     */   
/*     */   public void setIsUnitSplit(String paramString) {
/*  67 */     this.isUnitSplit = paramString;
/*     */   }
/*     */   
/*     */   public void setMappingId(String paramString) {
/*  71 */     this.mappingId = paramString;
/*     */   }
/*     */   public String getMappingId() {
/*  74 */     return this.mappingId;
/*     */   }
/*     */   
/*     */   public void setHumanResource(String paramString) {
/*  78 */     this.humanResource = paramString;
/*     */   }
/*     */   
/*     */   public String getHumanResource() {
/*  82 */     return this.humanResource;
/*     */   }
/*     */   
/*     */   public String getWorkflowId() {
/*  86 */     return this.workflowId;
/*     */   }
/*     */   public void setWorkflowId(String paramString) {
/*  89 */     this.workflowId = paramString;
/*     */   }
/*     */   
/*     */   public String getIsStopCreateNode() {
/*  93 */     return this.isStopCreateNode;
/*     */   }
/*     */   
/*     */   public void setIsStopCreateNode(String paramString) {
/*  97 */     this.isStopCreateNode = paramString;
/*     */   }
/*     */   
/*     */   public String getCreatorType() {
/* 101 */     return this.creatorType;
/*     */   }
/*     */   
/*     */   public void setCreatorType(String paramString) {
/* 105 */     this.creatorType = paramString;
/*     */   }
/*     */   
/*     */   public String getCreatorFieldId() {
/* 109 */     return this.creatorFieldId;
/*     */   }
/*     */   
/*     */   public void setCreatorFieldId(String paramString) {
/* 113 */     this.creatorFieldId = paramString;
/*     */   }
/*     */   
/*     */   public String getCreatorFieldType() {
/* 117 */     return this.creatorFieldType;
/*     */   }
/*     */   
/*     */   public void setCreatorFieldType(String paramString) {
/* 121 */     this.creatorFieldType = paramString;
/*     */   }
/*     */   
/*     */   public String getCreatorFieldValue() {
/* 125 */     return this.creatorFieldValue;
/*     */   }
/*     */   
/*     */   public void setCreatorFieldValue(String paramString) {
/* 129 */     this.creatorFieldValue = paramString;
/*     */   }
/*     */   
/*     */   public String getIsCreateForAnyone() {
/* 133 */     return this.isCreateForAnyone;
/*     */   }
/*     */   
/*     */   public void setIsCreateForAnyone(String paramString) {
/* 137 */     this.isCreateForAnyone = paramString;
/*     */   }
/*     */   
/*     */   public List getMainTableFieldMappings() {
/* 141 */     return this.mainTableFieldMappings;
/*     */   }
/*     */   
/*     */   public void setMainTableFieldMappings(List paramList) {
/* 145 */     this.mainTableFieldMappings = paramList;
/*     */   }
/*     */   
/*     */   public List getDetailTableFieldMappings() {
/* 149 */     return this.detailTableFieldMappings;
/*     */   }
/*     */   
/*     */   public void setDetailTableFieldMappings(List paramList) {
/* 153 */     this.detailTableFieldMappings = paramList;
/*     */   }
/*     */   public List<String> getHumanResourceList() {
/* 156 */     return Util.TokenizerString(this.humanResource, ",");
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/HumanResourceWorkflowMapping.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */