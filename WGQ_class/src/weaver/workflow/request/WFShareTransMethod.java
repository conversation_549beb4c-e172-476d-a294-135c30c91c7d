/*     */ package weaver.workflow.request;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.List;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.GCONST;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.check.JobComInfo;
/*     */ import weaver.hrm.company.DepartmentComInfo;
/*     */ import weaver.hrm.company.SubCompanyComInfo;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ import weaver.hrm.roles.RolesComInfo;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.workflow.monitor.Monitor;
/*     */ import weaver.workflow.monitor.MonitorDTO;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class WFShareTransMethod
/*     */ {
/*     */   public String getCheckbox(String paramString) {
/*  26 */     String[] arrayOfString = Util.TokenizerString2(paramString, "+");
/*  27 */     String str1 = arrayOfString[0];
/*  28 */     String str2 = arrayOfString[1];
/*  29 */     String str3 = arrayOfString[2];
/*     */     
/*  31 */     String str4 = "false";
/*  32 */     RecordSet recordSet1 = new RecordSet();
/*  33 */     RecordSet recordSet2 = new RecordSet();
/*     */     
/*  35 */     String str5 = "";
/*  36 */     String str6 = "";
/*  37 */     String str7 = "";
/*  38 */     String str8 = "";
/*  39 */     String str9 = "";
/*  40 */     recordSet2.executeSql("select isshared from workflow_base where id = " + str2);
/*  41 */     if (recordSet2.next()) {
/*  42 */       str9 = recordSet2.getString("isshared");
/*     */     }
/*  44 */     if ("1".equals(str9)) {
/*  45 */       recordSet1.executeSql("select isremark,preisremark,agentorbyagentid,agenttype from WORKFLOW_CURRENTOPERATOR where requestid = " + str1 + " and workflowid = " + str2 + " and userid = " + str3);
/*  46 */       while (recordSet1.next()) {
/*  47 */         str5 = recordSet1.getString("isremark");
/*  48 */         str6 = recordSet1.getString("preisremark");
/*  49 */         str7 = recordSet1.getString("agentorbyagentid");
/*  50 */         str8 = recordSet1.getString("agenttype");
/*  51 */         if ("0".equals(str5) || ("2".equals(str5) && "0".equals(str6)) || "4".equals(str5) || "1".equals(str8)) {
/*  52 */           str4 = "true";
/*     */           
/*     */           break;
/*     */         } 
/*     */       } 
/*     */       
/*  58 */       String str = "";
/*  59 */       recordSet1.executeSql("select currentnodetype,creater from workflow_Requestbase where requestid = " + str1);
/*  60 */       while (recordSet1.next()) {
/*  61 */         str = Util.null2String(recordSet1.getString(2));
/*     */       }
/*  63 */       MonitorDTO monitorDTO = (new Monitor()).getMonitorInfo(str3 + "", str, str2);
/*  64 */       if (monitorDTO.getIsview()) {
/*  65 */         str4 = "true";
/*     */       }
/*     */     } 
/*  68 */     return str4;
/*     */   }
/*     */   
/*     */   public String getWFSearchCheckbox(String paramString) {
/*  72 */     User user = new User();
/*  73 */     RecordSet recordSet1 = new RecordSet();
/*  74 */     RecordSet recordSet2 = new RecordSet();
/*  75 */     String[] arrayOfString = Util.TokenizerString2(paramString, "+");
/*  76 */     String str1 = arrayOfString[0];
/*  77 */     String str2 = arrayOfString[1];
/*  78 */     String str3 = arrayOfString[2];
/*  79 */     String str4 = arrayOfString[3];
/*  80 */     String str5 = user.getBelongtoids();
/*  81 */     String str6 = "false";
/*  82 */     String str7 = "";
/*  83 */     String str8 = "";
/*  84 */     String str9 = "";
/*  85 */     if (str5.indexOf(str3) > -1) {
/*  86 */       str4 = str3;
/*     */     }
/*  88 */     String str10 = "";
/*  89 */     String str11 = "";
/*  90 */     recordSet2.executeSql("select isshared from workflow_base where id = " + str1);
/*  91 */     if (recordSet2.next()) {
/*  92 */       str7 = recordSet2.getString("isshared");
/*     */     }
/*  94 */     if ("1".equals(str7)) {
/*  95 */       recordSet1.executeSql("select isremark,preisremark,agentorbyagentid,agenttype from WORKFLOW_CURRENTOPERATOR where requestid = " + str2 + " and workflowid = " + str1 + " and userid = " + str4);
/*  96 */       if (recordSet1.next()) {
/*  97 */         str8 = recordSet1.getString("isremark");
/*  98 */         str9 = recordSet1.getString("preisremark");
/*  99 */         str10 = recordSet1.getString("agentorbyagentid");
/* 100 */         str11 = recordSet1.getString("agenttype");
/* 101 */         if ("0".equals(str8) || ("2".equals(str8) && "0".equals(str9)) || "4".equals(str8) || "1".equals(str11)) {
/* 102 */           str6 = "true";
/*     */         }
/*     */       } 
/*     */ 
/*     */       
/* 107 */       String str = "";
/* 108 */       recordSet1.executeSql("select currentnodetype,creater from workflow_Requestbase where requestid = " + str2);
/* 109 */       while (recordSet1.next()) {
/* 110 */         str = Util.null2String(recordSet1.getString(2));
/*     */       }
/* 112 */       MonitorDTO monitorDTO = (new Monitor()).getMonitorInfo(str4 + "", str, str1);
/* 113 */       if (monitorDTO.getIsview()) {
/* 114 */         str6 = "true";
/*     */       }
/*     */     } 
/* 117 */     return str6;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getWFPermissiontype(String paramString1, String paramString2) throws Exception {
/* 128 */     int i = Util.getIntValue(paramString2, 7);
/*     */     
/* 130 */     switch (Util.getIntValue(paramString1)) {
/*     */       
/*     */       case 1:
/* 133 */         return SystemEnv.getHtmlLabelName(124, i);
/*     */       
/*     */       case 2:
/* 136 */         return SystemEnv.getHtmlLabelName(122, i);
/*     */       
/*     */       case 3:
/* 139 */         return SystemEnv.getHtmlLabelName(1340, i);
/*     */       
/*     */       case 5:
/* 142 */         return SystemEnv.getHtmlLabelName(179, i);
/*     */       
/*     */       case 6:
/* 145 */         return SystemEnv.getHtmlLabelName(141, i);
/*     */       
/*     */       case 7:
/* 148 */         return SystemEnv.getHtmlLabelName(6086, i);
/*     */     } 
/* 150 */     return "";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getWFPermissionObj(String paramString1, String paramString2) throws Exception {
/* 161 */     ArrayList<E> arrayList = Util.TokenizerString(paramString2, "+");
/* 162 */     if (arrayList != null && arrayList.size() > 0) {
/* 163 */       DepartmentComInfo departmentComInfo1; int j; String str2; ResourceComInfo resourceComInfo; SubCompanyComInfo subCompanyComInfo1; JobComInfo jobComInfo; SubCompanyComInfo subCompanyComInfo2; DepartmentComInfo departmentComInfo2; String str3, str4, str5, str6, str7, str1 = "";
/* 164 */       int i = Util.getIntValue((arrayList.get(5) == null) ? "7" : arrayList.get(5).toString());
/* 165 */       switch (Util.getIntValue(paramString1)) {
/*     */         
/*     */         case 1:
/* 168 */           str1 = (arrayList.get(0) == null) ? "" : arrayList.get(0).toString();
/* 169 */           departmentComInfo1 = new DepartmentComInfo();
/*     */           
/* 171 */           return "<a href='" + GCONST.getContextPath() + "/spa/hrm/engine.html#/hrmengine/organization?showTree=false&isView=1&type=department&id=" + str1 + "' target='_blank'>" + Util.toScreen(departmentComInfo1.getDepartmentname(str1), i) + "</a>";
/*     */         
/*     */         case 2:
/* 174 */           str1 = (arrayList.get(3) == null) ? "" : arrayList.get(3).toString();
/* 175 */           j = Util.getIntValue((arrayList.get(4) == null) ? "-1" : arrayList.get(4).toString());
/*     */           
/* 177 */           str2 = (new RolesComInfo()).getRolesRemark(str1);
/* 178 */           switch (j) {
/*     */             case 0:
/* 180 */               str2 = str2 + "/" + SystemEnv.getHtmlLabelName(124, i);
/*     */               break;
/*     */             case 1:
/* 183 */               str2 = str2 + "/" + SystemEnv.getHtmlLabelName(141, i);
/*     */               break;
/*     */             case 2:
/* 186 */               str2 = str2 + "/" + SystemEnv.getHtmlLabelName(140, i);
/*     */               break;
/*     */           } 
/* 189 */           return str2;
/*     */         
/*     */         case 3:
/* 192 */           return "";
/*     */         
/*     */         case 5:
/* 195 */           str1 = (arrayList.get(2) == null) ? "" : arrayList.get(2).toString();
/* 196 */           resourceComInfo = new ResourceComInfo();
/*     */           
/* 198 */           return "<a href='" + GCONST.getContextPath() + "/spa/hrm/index_mobx.html#/main/hrm/card/cardInfo/" + str1 + "' target='_blank'>" + Util.toScreen(resourceComInfo.getLastname(str1), i) + "</a>";
/*     */         
/*     */         case 6:
/* 201 */           str1 = (arrayList.get(1) == null) ? "" : arrayList.get(1).toString();
/* 202 */           subCompanyComInfo1 = new SubCompanyComInfo();
/*     */           
/* 204 */           return "<a href='" + GCONST.getContextPath() + "/spa/hrm/engine.html#/hrmengine/organization?showTree=false&isView=1&type=subcompany&id=" + str1 + "' target='_blank'>" + Util.toScreen(subCompanyComInfo1.getSubCompanyname(str1), i) + "</a>";
/*     */         
/*     */         case 7:
/* 207 */           jobComInfo = new JobComInfo();
/* 208 */           subCompanyComInfo2 = new SubCompanyComInfo();
/* 209 */           departmentComInfo2 = new DepartmentComInfo();
/* 210 */           str3 = (arrayList.get(6) == null) ? "" : arrayList.get(6).toString();
/* 211 */           str4 = (arrayList.get(7) == null) ? "" : arrayList.get(7).toString();
/* 212 */           str5 = (arrayList.get(8) == null) ? "" : arrayList.get(8).toString();
/* 213 */           str6 = "";
/*     */           
/* 215 */           str7 = jobComInfo.getJobName(str3);
/*     */           
/* 217 */           if (str4.equals("0")) {
/* 218 */             str6 = "/" + SystemEnv.getHtmlLabelName(19438, i) + "(";
/*     */ 
/*     */ 
/*     */ 
/*     */             
/* 223 */             str6 = str6 + "<a href='" + GCONST.getContextPath() + "/spa/hrm/engine.html#/hrmengine/organization?showTree=false&isView=1&type=department&id=" + str5 + "' target='_blank'>" + Util.toScreen(departmentComInfo2.getDepartmentname(str5), i) + "</a>,";
/* 224 */             str6 = str6.substring(0, str6.length() - 1);
/* 225 */             str7 = str7 + str6 + ")";
/* 226 */           } else if (str4.equals("1")) {
/* 227 */             str6 = "/" + SystemEnv.getHtmlLabelName(19437, i) + "(";
/*     */ 
/*     */ 
/*     */ 
/*     */             
/* 232 */             str6 = str6 + "<a href='" + GCONST.getContextPath() + "/spa/hrm/engine.html#/hrmengine/organization?showTree=false&isView=1&type=subcompany&id=" + str5 + "' target='_blank'>" + Util.toScreen(subCompanyComInfo2.getSubCompanyname(str5), i) + "</a>,";
/* 233 */             str6 = str6.substring(0, str6.length() - 1);
/* 234 */             str7 = str7 + str6 + ")";
/*     */           } else {
/* 236 */             str6 = "/" + SystemEnv.getHtmlLabelName(140, i);
/* 237 */             str7 = str7 + str6;
/*     */           } 
/* 239 */           return str7;
/*     */       } 
/*     */     
/*     */     } 
/* 243 */     return "";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getWFPermissionlevel(String paramString1, String paramString2) throws Exception {
/* 254 */     ArrayList<E> arrayList = Util.TokenizerString(paramString2, "+");
/* 255 */     byte b1 = -1;
/* 256 */     byte b2 = -1;
/* 257 */     switch (Util.getIntValue(paramString1)) {
/*     */       
/*     */       case 1:
/* 260 */         b1 = 0;
/* 261 */         b2 = 1;
/*     */         break;
/*     */       
/*     */       case 2:
/* 265 */         b1 = 6;
/* 266 */         b2 = 7;
/*     */         break;
/*     */       
/*     */       case 3:
/* 270 */         b1 = 4;
/* 271 */         b2 = 5;
/*     */         break;
/*     */ 
/*     */ 
/*     */ 
/*     */       
/*     */       case 6:
/* 278 */         b1 = 2;
/* 279 */         b2 = 3;
/*     */         break;
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/* 285 */     String str = "";
/* 286 */     if (b1 != -1 && b2 != -1 && arrayList != null) {
/* 287 */       str = (arrayList.get(b1) == null) ? "" : arrayList.get(b1).toString();
/* 288 */       if (arrayList.size() > 1) {
/* 289 */         String str1 = (arrayList.get(b2) == null) ? "" : arrayList.get(b2).toString();
/* 290 */         if (!"".equals(str1)) {
/* 291 */           str = str + "-" + str1;
/*     */         }
/*     */       } 
/*     */     } 
/* 295 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List checkWFPrmOperate(String paramString) {
/* 305 */     ArrayList<String> arrayList = new ArrayList();
/*     */ 
/*     */     
/* 308 */     arrayList.add("true");
/*     */     
/* 310 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getWFIsCanread(String paramString1, String paramString2) throws Exception {
/* 321 */     String str = "";
/* 322 */     int i = Util.getIntValue(paramString2, 7);
/* 323 */     if (paramString1.equals("0")) {
/* 324 */       str = SystemEnv.getHtmlLabelName(82613, i);
/*     */     } else {
/* 326 */       str = SystemEnv.getHtmlLabelName(20306, i);
/*     */     } 
/* 328 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getWFOperator(String paramString) throws Exception {
/* 340 */     String str = "";
/*     */     try {
/* 342 */       ResourceComInfo resourceComInfo = new ResourceComInfo();
/*     */       
/* 344 */       str = "<a href='" + GCONST.getContextPath() + "/hrm/resource/HrmResource.jsp?id=" + paramString + "' target='_blank'>" + resourceComInfo.getResourcename(paramString) + "</a>";
/* 345 */     } catch (Exception exception) {
/*     */       
/* 347 */       exception.printStackTrace();
/*     */     } 
/* 349 */     return str;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/WFShareTransMethod.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */