/*    */ package weaver.workflow.request;
/*    */ 
/*    */ public class LockDTO {
/*    */   private boolean lock;
/*    */   private String userid;
/*    */   private String lockdate;
/*    */   private String locktime;
/*    */   
/*    */   public boolean isLock() {
/* 10 */     return this.lock;
/*    */   }
/*    */   
/*    */   public void setLock(boolean paramBoolean) {
/* 14 */     this.lock = paramBoolean;
/*    */   }
/*    */   
/*    */   public String getUserid() {
/* 18 */     return this.userid;
/*    */   }
/*    */   
/*    */   public void setUserid(String paramString) {
/* 22 */     this.userid = paramString;
/*    */   }
/*    */   
/*    */   public String getLockdate() {
/* 26 */     return this.lockdate;
/*    */   }
/*    */   
/*    */   public void setLockdate(String paramString) {
/* 30 */     this.lockdate = paramString;
/*    */   }
/*    */   
/*    */   public String getLocktime() {
/* 34 */     return this.locktime;
/*    */   }
/*    */   
/*    */   public void setLocktime(String paramString) {
/* 38 */     this.locktime = paramString;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/LockDTO.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */