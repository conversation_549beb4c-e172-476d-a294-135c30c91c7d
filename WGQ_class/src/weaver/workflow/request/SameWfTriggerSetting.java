/*     */ package weaver.workflow.request;
/*     */ 
/*     */ import java.util.List;
/*     */ import weaver.general.Util;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class SameWfTriggerSetting
/*     */   extends WfTriggerSetting
/*     */ {
/*     */   private String subWorkflowId;
/*     */   private String creatorType;
/*     */   private String creatorFieldId;
/*     */   private String creatorFieldType;
/*     */   private String creatorFieldValue;
/*     */   private String isStopCreateNode;
/*     */   private String isUnitSplit;
/*     */   private List mainTableFieldMappings;
/*     */   private List detailTableFieldMappings;
/*     */   private List<String> anyoneList;
/*     */   private Field anyoneField;
/*     */   private String innerChange;
/*     */   private String autoSend;
/*     */   private String autoReceive;
/*     */   private String conditioncn;
/*     */   
/*     */   public String getIsCreateForAnyone() {
/*  37 */     String str = "0";
/*     */     
/*  39 */     if ("main".equals(getTriggerSourceType())) {
/*  40 */       List<SubWorkflowTriggerService.FieldMapping> list = getMainTableFieldMappings();
/*  41 */       for (byte b = 0; b < list.size(); b++) {
/*  42 */         SubWorkflowTriggerService.FieldMapping fieldMapping = list.get(b);
/*  43 */         if (fieldMapping.getIsCreateForAnynone().equals("1")) {
/*  44 */           str = "1";
/*  45 */           Field field = (Field)getTriggerSourceDataRow().get(fieldMapping.getFromFieldId());
/*  46 */           this.anyoneList = Util.TokenizerString(field.getFieldValue(), ",");
/*  47 */           this.anyoneField = field;
/*     */           
/*     */           break;
/*     */         } 
/*     */       } 
/*  52 */     } else if ("detail".equals(getTriggerSourceType())) {
/*  53 */       List<SubWorkflowTriggerService.DetailTableFieldMappings> list = getDetailTableFieldMappings();
/*  54 */       for (byte b = 0; b < list.size(); b++) {
/*  55 */         SubWorkflowTriggerService.DetailTableFieldMappings detailTableFieldMappings = list.get(b);
/*  56 */         List<SubWorkflowTriggerService.FieldMapping> list1 = detailTableFieldMappings.getFieldMappings();
/*     */         
/*  58 */         for (byte b1 = 0; b1 < list1.size(); b1++) {
/*  59 */           SubWorkflowTriggerService.FieldMapping fieldMapping = list1.get(b1);
/*  60 */           if (fieldMapping.getIsCreateForAnynone().equals("1")) {
/*  61 */             str = "1";
/*  62 */             Field field = (Field)getTriggerSourceDataRow().get(fieldMapping.getFromFieldId());
/*  63 */             this.anyoneList = Util.TokenizerString(field.getFieldValue(), ",");
/*  64 */             this.anyoneField = field;
/*     */             
/*     */             break;
/*     */           } 
/*     */         } 
/*     */       } 
/*     */       
/*  71 */       if (!"1".equals(str)) {
/*  72 */         List<SubWorkflowTriggerService.FieldMapping> list1 = getMainTableFieldMappings();
/*  73 */         for (byte b1 = 0; b1 < list1.size(); b1++) {
/*  74 */           SubWorkflowTriggerService.FieldMapping fieldMapping = list1.get(b1);
/*  75 */           if (fieldMapping.getIsCreateForAnynone().equals("1")) {
/*  76 */             str = "1";
/*  77 */             Field field = (Field)getTriggerSourceDataRow().get(fieldMapping.getFromFieldId());
/*  78 */             this.anyoneList = Util.TokenizerString(field.getFieldValue(), ",");
/*  79 */             this.anyoneField = field;
/*     */             
/*     */             break;
/*     */           } 
/*     */         } 
/*     */       } 
/*     */     } 
/*  86 */     return str;
/*     */   }
/*     */   
/*     */   public List<String> getAnyoneList() {
/*  90 */     if (this.anyoneList == null) {
/*  91 */       getIsCreateForAnyone();
/*     */     }
/*  93 */     return this.anyoneList;
/*     */   }
/*     */   
/*     */   public Field getAnyoneField() {
/*  97 */     if (this.anyoneField == null) {
/*  98 */       getIsCreateForAnyone();
/*     */     }
/* 100 */     return this.anyoneField;
/*     */   }
/*     */   
/*     */   public String getIsUnitSplit() {
/* 104 */     return this.isUnitSplit;
/*     */   }
/*     */   
/*     */   public void setIsUnitSplit(String paramString) {
/* 108 */     this.isUnitSplit = paramString;
/*     */   }
/*     */   
/*     */   public String getConditioncn() {
/* 112 */     return this.conditioncn;
/*     */   }
/*     */   
/*     */   public void setConditioncn(String paramString) {
/* 116 */     this.conditioncn = paramString;
/*     */   }
/*     */   
/*     */   public String getCreatorType() {
/* 120 */     return this.creatorType;
/*     */   }
/*     */   
/*     */   public void setCreatorType(String paramString) {
/* 124 */     this.creatorType = paramString;
/*     */   }
/*     */   
/*     */   public String getCreatorFieldId() {
/* 128 */     return this.creatorFieldId;
/*     */   }
/*     */   
/*     */   public void setCreatorFieldId(String paramString) {
/* 132 */     this.creatorFieldId = paramString;
/*     */   }
/*     */   
/*     */   public String getCreatorFieldType() {
/* 136 */     return this.creatorFieldType;
/*     */   }
/*     */   
/*     */   public void setCreatorFieldType(String paramString) {
/* 140 */     this.creatorFieldType = paramString;
/*     */   }
/*     */   
/*     */   public String getCreatorFieldValue() {
/* 144 */     return this.creatorFieldValue;
/*     */   }
/*     */   
/*     */   public void setCreatorFieldValue(String paramString) {
/* 148 */     this.creatorFieldValue = paramString;
/*     */   }
/*     */   
/*     */   public String getIsStopCreateNode() {
/* 152 */     return this.isStopCreateNode;
/*     */   }
/*     */   
/*     */   public void setIsStopCreateNode(String paramString) {
/* 156 */     this.isStopCreateNode = paramString;
/*     */   }
/*     */   
/*     */   public String getSubWorkflowId() {
/* 160 */     return this.subWorkflowId;
/*     */   }
/*     */   
/*     */   public void setSubWorkflowId(String paramString) {
/* 164 */     this.subWorkflowId = paramString;
/*     */   }
/*     */   
/*     */   public List getMainTableFieldMappings() {
/* 168 */     return this.mainTableFieldMappings;
/*     */   }
/*     */   
/*     */   public void setMainTableFieldMappings(List paramList) {
/* 172 */     this.mainTableFieldMappings = paramList;
/*     */   }
/*     */   
/*     */   public List getDetailTableFieldMappings() {
/* 176 */     return this.detailTableFieldMappings;
/*     */   }
/*     */   
/*     */   public void setDetailTableFieldMappings(List paramList) {
/* 180 */     this.detailTableFieldMappings = paramList;
/*     */   }
/*     */   
/*     */   public String getInnerChange() {
/* 184 */     return this.innerChange;
/*     */   }
/*     */   
/*     */   public void setInnerChange(String paramString) {
/* 188 */     this.innerChange = paramString;
/*     */   }
/*     */   
/*     */   public String getAutoSend() {
/* 192 */     return this.autoSend;
/*     */   }
/*     */   
/*     */   public void setAutoSend(String paramString) {
/* 196 */     this.autoSend = paramString;
/*     */   }
/*     */   
/*     */   public String getAutoReceive() {
/* 200 */     return this.autoReceive;
/*     */   }
/*     */   
/*     */   public void setAutoReceive(String paramString) {
/* 204 */     this.autoReceive = paramString;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/SameWfTriggerSetting.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */