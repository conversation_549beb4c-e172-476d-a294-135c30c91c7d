/*    */ package weaver.workflow.request;
/*    */ 
/*    */ public class Field
/*    */ {
/*    */   private String fieldId;
/*    */   private String fieldName;
/*    */   private String fieldValue;
/*    */   private String fieldHtmlType;
/*    */   private String fieldType;
/*    */   private String fieldDbType;
/*    */   
/*    */   public Field() {
/* 13 */     this.fieldId = "";
/* 14 */     this.fieldValue = "";
/* 15 */     this.fieldHtmlType = "";
/* 16 */     this.fieldType = "";
/*    */   }
/*    */   
/*    */   public Field(String paramString1, String paramString2) {
/* 20 */     this.fieldId = paramString1;
/* 21 */     this.fieldValue = paramString2;
/*    */   }
/*    */   
/*    */   public String getFieldId() {
/* 25 */     return this.fieldId;
/*    */   }
/*    */   public void setFieldId(String paramString) {
/* 28 */     this.fieldId = paramString;
/*    */   }
/*    */   public String getFieldName() {
/* 31 */     return this.fieldName;
/*    */   }
/*    */   
/*    */   public void setFieldName(String paramString) {
/* 35 */     this.fieldName = paramString;
/*    */   }
/*    */   public String getFieldValue() {
/* 38 */     return this.fieldValue;
/*    */   }
/*    */   public void setFieldValue(String paramString) {
/* 41 */     this.fieldValue = paramString;
/*    */   }
/*    */   public String getFieldHtmlType() {
/* 44 */     return this.fieldHtmlType;
/*    */   }
/*    */   public void setFieldHtmlType(String paramString) {
/* 47 */     this.fieldHtmlType = paramString;
/*    */   }
/*    */   public String getFieldType() {
/* 50 */     return this.fieldType;
/*    */   }
/*    */   public void setFieldType(String paramString) {
/* 53 */     this.fieldType = paramString;
/*    */   }
/*    */   
/*    */   public String getFieldDbType() {
/* 57 */     return this.fieldDbType;
/*    */   }
/*    */   
/*    */   public void setFieldDbType(String paramString) {
/* 61 */     this.fieldDbType = paramString;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/Field.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */