/*     */ package weaver.workflow.request;
/*     */ 
/*     */ import java.io.BufferedInputStream;
/*     */ import java.io.BufferedOutputStream;
/*     */ import java.io.ByteArrayInputStream;
/*     */ import java.io.ByteArrayOutputStream;
/*     */ import java.io.File;
/*     */ import java.io.FileOutputStream;
/*     */ import java.io.InputStream;
/*     */ import java.io.OutputStream;
/*     */ import java.util.UUID;
/*     */ import java.util.zip.ZipEntry;
/*     */ import java.util.zip.ZipOutputStream;
/*     */ import org.apache.commons.lang.StringUtils;
/*     */ import org.apache.commons.logging.Log;
/*     */ import org.apache.commons.logging.LogFactory;
/*     */ import org.apache.ws.commons.util.Base64;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.docs.category.SecCategoryComInfo;
/*     */ import weaver.docs.docs.DocCoder;
/*     */ import weaver.docs.docs.DocComInfo;
/*     */ import weaver.docs.docs.DocImageManager;
/*     */ import weaver.docs.docs.DocManager;
/*     */ import weaver.docs.docs.DocViewer;
/*     */ import weaver.docs.docs.ImageFileIdUpdate;
/*     */ import weaver.docs.webservices.DocAttachment;
/*     */ import weaver.file.AESCoder;
/*     */ import weaver.file.FileManage;
/*     */ import weaver.file.FileUpload;
/*     */ import weaver.file.ImageFileManager;
/*     */ import weaver.file.multipart.DefaultFileRenamePolicy;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.ThreadVarLanguage;
/*     */ import weaver.general.TimeUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.sm.SM4Utils;
/*     */ import weaver.system.SystemComInfo;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.workflow.workflow.WorkflowComInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class WorkflowSpeechAppend
/*     */ {
/*  51 */   private static final Log log = LogFactory.getLog(WorkflowSpeechAppend.class);
/*     */   
/*     */   public static final String FMT_SPEECHATTACHMENT = "application/mp3";
/*     */   
/*     */   public static final String FMT_HANDWRITTEN_SIGN = "image/png";
/*  56 */   private static final String[] suffixs = new String[] { "<br/><br/><span style='font-size:11px;color:#666;'>" + 
/*  57 */       SystemEnv.getHtmlLabelName(31505, ThreadVarLanguage.getLang()) + "iPhone" + SystemEnv.getHtmlLabelName(108, ThreadVarLanguage.getLang()) + "</span>", "<br/><br/><span style='font-size:11px;color:#666;'>" + 
/*  58 */       SystemEnv.getHtmlLabelName(31505, ThreadVarLanguage.getLang()) + "iPad" + SystemEnv.getHtmlLabelName(108, ThreadVarLanguage.getLang()) + "</span>", "<br/><br/><span style='font-size:11px;color:#666;'>" + 
/*  59 */       SystemEnv.getHtmlLabelName(31505, ThreadVarLanguage.getLang()) + "Android" + SystemEnv.getHtmlLabelName(108, ThreadVarLanguage.getLang()) + "</span>", "<br/><br/><span style='font-size:11px;color:#666;'>" + 
/*  60 */       SystemEnv.getHtmlLabelName(31505, ThreadVarLanguage.getLang()) + "android" + SystemEnv.getHtmlLabelName(108, ThreadVarLanguage.getLang()) + "</span>", "<br/><br/><span style='font-size:11px;color:#666;'>" + 
/*  61 */       SystemEnv.getHtmlLabelName(31505, ThreadVarLanguage.getLang()) + "AndPad" + SystemEnv.getHtmlLabelName(108, ThreadVarLanguage.getLang()) + "</span>", "<br/><br/><span style='font-size:11px;color:#666;'>" + 
/*  62 */       SystemEnv.getHtmlLabelName(31505, ThreadVarLanguage.getLang()) + "andPad" + SystemEnv.getHtmlLabelName(108, ThreadVarLanguage.getLang()) + "</span>", "<br/><br/><span style='font-size:11px;color:#666;'>" + 
/*  63 */       SystemEnv.getHtmlLabelName(31505, ThreadVarLanguage.getLang()) + "Web" + SystemEnv.getHtmlLabelName(31506, ThreadVarLanguage.getLang()) + "</span>", "<br/><br/><span style='font-size:11px;color:#666;'>" + 
/*  64 */       SystemEnv.getHtmlLabelName(31505, ThreadVarLanguage.getLang()) + "Web" + SystemEnv.getHtmlLabelName(10004333, ThreadVarLanguage.getLang()) + "</span>" };
/*     */   
/*     */   public static String getMobileSuffix(String paramString) {
/*  67 */     if (paramString == null || paramString.trim().equals("")) {
/*  68 */       return null;
/*     */     }
/*     */     
/*  71 */     for (byte b = 0; b < suffixs.length; b++) {
/*  72 */       if (paramString.indexOf(suffixs[b]) > -1) {
/*  73 */         return suffixs[b];
/*     */       }
/*     */     } 
/*  76 */     return null;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getElectrSignatrue(String paramString) {
/*  85 */     int i = paramString.lastIndexOf("<br/><img alt='electricSignature'");
/*  86 */     if (i > 0) {
/*  87 */       return paramString.substring(i);
/*     */     }
/*  89 */     return null;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String converBrowserBtnVal(String paramString) {
/* 101 */     log.info("Start to run 'converBrowserBtnVal' value, the value of param 'originalVal': " + paramString);
/* 102 */     String str = paramString;
/* 103 */     if (paramString.indexOf(",") != -1) {
/* 104 */       StringBuffer stringBuffer = new StringBuffer();
/* 105 */       String[] arrayOfString = paramString.split(",");
/* 106 */       for (String str1 : arrayOfString) {
/* 107 */         stringBuffer.append(",").append(str1);
/*     */       }
/* 109 */       if (stringBuffer.length() > 0) {
/* 110 */         str = stringBuffer.toString().substring(1);
/*     */       } else {
/* 112 */         str = stringBuffer.toString();
/*     */       } 
/*     */     } 
/* 115 */     log.info("End to run 'converBrowserBtnVal' value, return value: " + str);
/* 116 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static int uploadAppend(String paramString1, String paramString2) {
/* 128 */     log.info("Start to upload append data.");
/* 129 */     byte[] arrayOfByte = null;
/*     */     try {
/* 131 */       arrayOfByte = Base64.decode(paramString1);
/* 132 */     } catch (org.apache.ws.commons.util.Base64.DecodingException decodingException) {
/* 133 */       log.error("Catch a exception during decode data, return -1.", (Throwable)decodingException);
/* 134 */       return -1;
/*     */     } 
/*     */     
/* 137 */     DocAttachment docAttachment = writeData(arrayOfByte);
/* 138 */     if (docAttachment == null) {
/* 139 */       log.error("It is fialure to write the data, return -1.");
/* 140 */       return -1;
/*     */     } 
/*     */     
/* 143 */     docAttachment.setFiletype(paramString2);
/*     */     
/* 145 */     int i = saveAttachment(docAttachment);
/* 146 */     if (i == -1) {
/* 147 */       log.error("It is fialure to save doc attenchent into database, return -1.");
/* 148 */       return -1;
/*     */     } 
/*     */     
/* 151 */     log.info("End upload append data, return " + i);
/* 152 */     return i;
/*     */   }
/*     */   
/*     */   private static DocAttachment writeData(byte[] paramArrayOfbyte) {
/* 156 */     String str = Util.getRandom() + ".png";
/*     */     
/* 158 */     return writeData(paramArrayOfbyte, str);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static DocAttachment writeData(byte[] paramArrayOfbyte, String paramString) {
/* 169 */     DocAttachment docAttachment = new DocAttachment();
/* 170 */     int i = 0;
/* 171 */     if (paramArrayOfbyte != null) {
/* 172 */       i = paramArrayOfbyte.length;
/*     */     }
/* 174 */     String str1 = UUID.randomUUID().toString();
/* 175 */     docAttachment.setFilename(str1);
/*     */ 
/*     */     
/* 178 */     SystemComInfo systemComInfo = new SystemComInfo();
/* 179 */     String str2 = systemComInfo.getNeedzip();
/* 180 */     String str3 = systemComInfo.getIsaesencrypt();
/*     */     
/* 182 */     OutputStream outputStream = null;
/* 183 */     BufferedInputStream bufferedInputStream = null;
/* 184 */     ZipOutputStream zipOutputStream = null;
/*     */     
/*     */     try {
/* 187 */       boolean bool = false;
/* 188 */       if (str2.equals("1")) {
/* 189 */         bool = true;
/*     */       }
/* 191 */       docAttachment.setIszip(bool ? 1 : 0);
/*     */ 
/*     */ 
/*     */       
/* 195 */       String str4 = FileUpload.getCreateDir(systemComInfo.getFilesystem());
/* 196 */       if (str4 != null) {
/* 197 */         FileManage.createDir(str4);
/*     */       }
/*     */       
/* 200 */       String str5 = null;
/* 201 */       if (str1 != null) {
/* 202 */         if (bool) {
/* 203 */           String str = null;
/* 204 */           str5 = str1;
/*     */           
/* 206 */           int k = str1.lastIndexOf(".");
/* 207 */           if (k != -1) {
/* 208 */             str = str1.substring(0, k);
/*     */           } else {
/* 210 */             str = str1;
/*     */           } 
/* 212 */           str1 = str + ".zip";
/*     */         } 
/*     */ 
/*     */         
/* 216 */         File file = new File(new String(str4.getBytes("ISO8859_1"), "UTF-8"), str1);
/* 217 */         docAttachment.setFilerealpath(file.getPath());
/*     */         
/* 219 */         DefaultFileRenamePolicy defaultFileRenamePolicy = new DefaultFileRenamePolicy();
/* 220 */         if (defaultFileRenamePolicy != null) {
/* 221 */           file = defaultFileRenamePolicy.rename(file);
/* 222 */           str1 = new String(file.getName().getBytes("UTF-8"), "ISO8859_1");
/*     */         } 
/*     */         
/* 225 */         if (bool) {
/* 226 */           zipOutputStream = new ZipOutputStream(new FileOutputStream(file));
/* 227 */           zipOutputStream.setMethod(8);
/* 228 */           zipOutputStream.putNextEntry(new ZipEntry(new String(str5.getBytes("UTF-8"), "ISO8859_1")));
/* 229 */           outputStream = zipOutputStream;
/*     */         } else {
/* 231 */           outputStream = new BufferedOutputStream(new FileOutputStream(file));
/*     */         } 
/*     */ 
/*     */         
/* 235 */         if ("1".equals(str3)) {
/*     */           
/* 237 */           String str6 = Util.getRandomString(13);
/* 238 */           docAttachment.setAesCode(str6);
/* 239 */           docAttachment.setIsAesEncrype(Util.getIntValue(str3, 0));
/* 240 */           outputStream = AESCoder.encrypt(outputStream, str6);
/* 241 */           BaseBean baseBean = new BaseBean();
/* 242 */           String str7 = Util.null2String(baseBean.getPropValue("weaver_security_type", "reversible_enc_type"));
/* 243 */           if ("sm4".equalsIgnoreCase(str7) && str6.startsWith("sm4start") && str6.endsWith("sm4end")) {
/* 244 */             SM4Utils sM4Utils = new SM4Utils();
/* 245 */             byte[] arrayOfByte1 = sM4Utils.getSMCode(str6);
/* 246 */             paramArrayOfbyte = sM4Utils.encodeBytes(paramArrayOfbyte, arrayOfByte1);
/*     */           } 
/*     */         } 
/*     */ 
/*     */         
/* 251 */         bufferedInputStream = new BufferedInputStream(new ByteArrayInputStream(paramArrayOfbyte));
/* 252 */         byte[] arrayOfByte = new byte[8192]; int j;
/* 253 */         while ((j = bufferedInputStream.read(arrayOfByte)) != -1) {
/* 254 */           outputStream.write(arrayOfByte, 0, j);
/*     */         }
/*     */         
/* 257 */         outputStream.flush();
/* 258 */         docAttachment.setImagefilesize(i);
/*     */       } 
/* 260 */     } catch (Exception exception) {
/* 261 */       log.error("Catch a Exception during write data. ", exception);
/* 262 */       return null;
/*     */     } finally {
/* 264 */       if (outputStream != null) {
/*     */         try {
/* 266 */           outputStream.close();
/* 267 */         } catch (Exception exception) {
/* 268 */           log.error("Catch a exception during closing output stream.", exception);
/*     */         } 
/*     */       }
/* 271 */       if (zipOutputStream != null) {
/*     */         try {
/* 273 */           zipOutputStream.close();
/* 274 */         } catch (Exception exception) {
/* 275 */           log.error("Catch a exception during closing output stream.", exception);
/*     */         } 
/*     */       }
/* 278 */       if (bufferedInputStream != null) {
/*     */         try {
/* 280 */           bufferedInputStream.close();
/* 281 */         } catch (Exception exception) {
/* 282 */           log.error("Catch a exception during closing input stream.", exception);
/*     */         } 
/*     */       }
/*     */     } 
/*     */     
/* 287 */     return docAttachment;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static int saveAttachment(DocAttachment paramDocAttachment) {
/* 298 */     int i = -1;
/*     */     try {
/* 300 */       int j = paramDocAttachment.getImagefilesize();
/* 301 */       String str1 = paramDocAttachment.getFilerealpath();
/* 302 */       String str2 = "1";
/* 303 */       String str3 = String.valueOf(paramDocAttachment.getIszip());
/* 304 */       String str4 = "0";
/* 305 */       String str5 = paramDocAttachment.getFilename();
/* 306 */       String str6 = paramDocAttachment.getFiletype();
/* 307 */       int k = paramDocAttachment.getIsAesEncrype();
/* 308 */       String str7 = paramDocAttachment.getAesCode();
/* 309 */       RecordSet recordSet = new RecordSet();
/* 310 */       char c = Util.getSeparator();
/* 311 */       i = (new ImageFileIdUpdate()).getImageFileNewId();
/* 312 */       String str8 = "" + i + c + str5 + c + str6 + c + str2 + c + str1 + c + str3 + c + str4 + c + j + c + k + c + str7;
/*     */ 
/*     */       
/* 315 */       recordSet.executeProc("ImageFile_Insert_New", str8);
/* 316 */     } catch (Exception exception) {
/* 317 */       log.error("Catch a exception during save data into database, return -1.", exception);
/* 318 */       return i;
/*     */     } 
/* 320 */     return i;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getAppend(int paramInt) {
/* 330 */     log.info("Start to get append data.");
/*     */     
/* 332 */     if (paramInt == -1) {
/* 333 */       log.info("The value of the imageFileID is -1, return null.");
/* 334 */       return null;
/*     */     } 
/*     */ 
/*     */     
/* 338 */     DocAttachment docAttachment = getAttachment(paramInt);
/* 339 */     if (docAttachment == null) {
/* 340 */       log.info("The 'DocAttachment' object is null, return null.");
/* 341 */       return null;
/*     */     } 
/*     */     
/* 344 */     byte[] arrayOfByte = readAttachment(docAttachment);
/* 345 */     if (arrayOfByte == null) {
/* 346 */       log.info("The 'speedAttachment' object is null, return null.");
/* 347 */       return null;
/*     */     } 
/*     */     
/* 350 */     String str = Base64.encode(arrayOfByte);
/* 351 */     log.info("End upload append data, return correct result.");
/* 352 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static DocAttachment getAttachment(int paramInt) {
/* 363 */     DocAttachment docAttachment = null;
/* 364 */     RecordSet recordSet = new RecordSet();
/* 365 */     String str = "Select * from ImageFile where imagefileid = " + paramInt;
/* 366 */     recordSet.execute(str);
/*     */     
/* 368 */     if (recordSet.next()) {
/* 369 */       docAttachment = new DocAttachment();
/* 370 */       docAttachment.setImagefileid(paramInt);
/* 371 */       docAttachment.setFilename(recordSet.getString("imageFileName"));
/* 372 */       docAttachment.setFiletype(recordSet.getString("imageFiletype"));
/* 373 */       docAttachment.setFileused(recordSet.getInt("imagefileused"));
/* 374 */       docAttachment.setFilerealpath(recordSet.getString("filerealpath"));
/* 375 */       docAttachment.setIszip(recordSet.getInt("iszip"));
/* 376 */       docAttachment.setIsencrype(recordSet.getInt("isencrypt"));
/* 377 */       docAttachment.setImagefilesize(recordSet.getInt("fileSize"));
/* 378 */       docAttachment.setIsAesEncrype(recordSet.getInt("isaesencrypt"));
/* 379 */       docAttachment.setAesCode(recordSet.getString("aescode"));
/*     */     } 
/* 381 */     return docAttachment;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static byte[] readAttachment(DocAttachment paramDocAttachment) {
/* 393 */     boolean bool = (paramDocAttachment.getIszip() == 1) ? true : false;
/* 394 */     String str = paramDocAttachment.getFilerealpath();
/*     */     
/* 396 */     byte[] arrayOfByte = null;
/* 397 */     InputStream inputStream = null;
/*     */     
/* 399 */     ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*     */     try {
/* 416 */       ImageFileManager imageFileManager = new ImageFileManager();
/* 417 */       imageFileManager.getImageFileInfoById(paramDocAttachment.getImagefileid());
/* 418 */       inputStream = imageFileManager.getInputStream();
/*     */ 
/*     */       
/* 421 */       byte[] arrayOfByte1 = new byte[8192]; int i;
/* 422 */       while ((i = inputStream.read(arrayOfByte1)) != -1) {
/* 423 */         byteArrayOutputStream.write(arrayOfByte1, 0, i);
/*     */       }
/* 425 */       arrayOfByte = byteArrayOutputStream.toByteArray();
/* 426 */     } catch (Exception exception) {
/* 427 */       log.error("Catch a exception during reading data from file system.", exception);
/* 428 */       return null;
/*     */     } finally {
/* 430 */       if (inputStream != null) {
/*     */         try {
/* 432 */           inputStream.close();
/* 433 */         } catch (Exception exception) {
/* 434 */           log.error("Catch a exception during closing input stream.", exception);
/*     */         } 
/*     */       }
/*     */       try {
/* 438 */         byteArrayOutputStream.close();
/* 439 */       } catch (Exception exception) {
/* 440 */         log.error("Catch a exception during closing output stream.", exception);
/*     */       } 
/*     */     } 
/* 443 */     return arrayOfByte;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static int[] getDocCategory(String paramString, FileUpload paramFileUpload) {
/* 454 */     if (paramString == null) {
/* 455 */       return null;
/*     */     }
/*     */ 
/*     */     
/* 459 */     String str1 = WorkflowComInfo.getDocCategory(paramString);
/* 460 */     log.info("The original 'docCategory' value :\t" + str1);
/* 461 */     if (str1 == null || "".equals(str1)) {
/* 462 */       return null;
/*     */     }
/*     */     
/* 465 */     int[] arrayOfInt = null;
/*     */     
/* 467 */     if (str1.indexOf("field") != 0) {
/* 468 */       String[] arrayOfString1 = str1.split(",");
/* 469 */       arrayOfInt = new int[arrayOfString1.length];
/* 470 */       for (byte b1 = 0; b1 < arrayOfString1.length; b1++) {
/* 471 */         arrayOfInt[b1] = Util.getIntValue(arrayOfString1[b1]);
/*     */       }
/* 473 */       return arrayOfInt;
/*     */     } 
/*     */     
/* 476 */     if (paramFileUpload == null) {
/* 477 */       return null;
/*     */     }
/*     */     
/* 480 */     String str2 = paramFileUpload.getParameter3(str1);
/* 481 */     log.info("The 'fieldValue' value :\t" + str2);
/* 482 */     str2 = (str2 == null || "".equals(str2)) ? "0" : str2;
/*     */     
/* 484 */     String str3 = str1.substring(5);
/* 485 */     String str4 = String.format("select docCategory from workflow_selectitem where selectvalue = %1$s and fieldid = %2$s", new Object[] { str2, str3 });
/* 486 */     log.info("Following is the run SQL : \n" + str4);
/*     */     
/* 488 */     RecordSet recordSet = new RecordSet();
/* 489 */     recordSet.executeSql(str4);
/* 490 */     if (recordSet.next()) {
/* 491 */       str1 = Util.null2String(recordSet.getString("docCategory"));
/*     */     }
/*     */     
/* 494 */     log.info("The real 'docCategory' value :\t" + str1);
/* 495 */     if (str1 == null || "".equals(str1)) {
/* 496 */       return null;
/*     */     }
/*     */     
/* 499 */     String[] arrayOfString = str1.split(",");
/* 500 */     arrayOfInt = new int[arrayOfString.length];
/* 501 */     for (byte b = 0; b < arrayOfString.length; b++) {
/* 502 */       arrayOfInt[b] = Util.getIntValue(arrayOfString[b]);
/*     */     }
/* 504 */     return arrayOfInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static int uploadAppdix(String paramString1, String paramString2, User paramUser, int[] paramArrayOfint, String paramString3) {
/* 518 */     log.info("Start to upload append data.");
/*     */     
/* 520 */     if (paramArrayOfint == null || paramArrayOfint.length < 3) {
/* 521 */       log.info("The parameter 'docCategory' is null, return -1.");
/* 522 */       return -1;
/*     */     } 
/* 524 */     int i = paramArrayOfint[0];
/* 525 */     int j = paramArrayOfint[1];
/* 526 */     int k = paramArrayOfint[2];
/*     */ 
/*     */     
/* 529 */     byte[] arrayOfByte = null;
/*     */     try {
/* 531 */       arrayOfByte = Base64.decode(paramString1);
/* 532 */     } catch (org.apache.ws.commons.util.Base64.DecodingException decodingException) {
/* 533 */       log.error("Catch a exception during decode data, return -1.", (Throwable)decodingException);
/* 534 */       return -1;
/*     */     } 
/*     */     
/* 537 */     DocAttachment docAttachment = writeData(arrayOfByte, paramString2);
/* 538 */     if (docAttachment == null) {
/* 539 */       log.error("It is fialure to write the data, return -1.");
/* 540 */       return -1;
/*     */     } 
/*     */     
/* 543 */     docAttachment.setFiletype("application/octet-stream");
/*     */     
/* 545 */     int m = saveAttachment(docAttachment);
/* 546 */     if (m == -1) {
/* 547 */       log.error("It is fialure to save doc attenchent into database, return -1.");
/* 548 */       return -1;
/*     */     } 
/*     */     
/*     */     try {
/* 552 */       DocViewer docViewer = new DocViewer();
/* 553 */       DocComInfo docComInfo = new DocComInfo();
/* 554 */       SecCategoryComInfo secCategoryComInfo = new SecCategoryComInfo();
/*     */       
/* 556 */       String str1 = "1";
/*     */       
/* 558 */       DocManager docManager = new DocManager();
/* 559 */       int n = docManager.getNextDocId(new RecordSet());
/*     */       
/* 561 */       DocImageManager docImageManager = new DocImageManager();
/* 562 */       docImageManager.resetParameter();
/* 563 */       docImageManager.setImagefilename(paramString2);
/*     */       
/* 565 */       String str2 = getFileMainName(paramString2);
/*     */       
/* 567 */       String str3 = getFileExt(paramString2);
/* 568 */       if (str3.equalsIgnoreCase("doc")) {
/* 569 */         docImageManager.setDocfiletype("3");
/* 570 */       } else if (str3.equalsIgnoreCase("xls")) {
/* 571 */         docImageManager.setDocfiletype("4");
/* 572 */       } else if (str3.equalsIgnoreCase("ppt")) {
/* 573 */         docImageManager.setDocfiletype("5");
/* 574 */       } else if (str3.equalsIgnoreCase("wps")) {
/* 575 */         docImageManager.setDocfiletype("6");
/* 576 */       } else if (str3.equalsIgnoreCase("docx")) {
/* 577 */         docImageManager.setDocfiletype("7");
/* 578 */       } else if (str3.equalsIgnoreCase("xlsx")) {
/* 579 */         docImageManager.setDocfiletype("8");
/* 580 */       } else if (str3.equalsIgnoreCase("pptx")) {
/* 581 */         docImageManager.setDocfiletype("9");
/* 582 */       } else if (str3.equalsIgnoreCase("et")) {
/* 583 */         docImageManager.setDocfiletype("10");
/*     */       } else {
/* 585 */         docImageManager.setDocfiletype("2");
/*     */       } 
/* 587 */       docImageManager.setDocid(n);
/* 588 */       docImageManager.setImagefileid(m);
/* 589 */       docImageManager.setIsextfile("1");
/* 590 */       docImageManager.AddDocImageInfo();
/*     */       
/* 592 */       String str4 = TimeUtil.getCurrentDateString();
/* 593 */       String str5 = TimeUtil.getOnlyCurrentTimeString();
/* 594 */       docManager.setId(n);
/* 595 */       docManager.setMaincategory(i);
/* 596 */       docManager.setSubcategory(j);
/* 597 */       docManager.setSeccategory(k);
/* 598 */       docManager.setLanguageid(paramUser.getLanguage());
/* 599 */       docManager.setDoccontent("");
/* 600 */       docManager.setDocstatus(str1);
/* 601 */       docManager.setDocsubject(str2);
/* 602 */       docManager.setDoccreaterid(paramUser.getUID());
/* 603 */       docManager.setDocCreaterType(paramUser.getLogintype());
/* 604 */       docManager.setUsertype(paramUser.getLogintype());
/* 605 */       docManager.setOwnerid(paramUser.getUID());
/* 606 */       docManager.setOwnerType(paramUser.getLogintype());
/* 607 */       docManager.setDoclastmoduserid(paramUser.getUID());
/* 608 */       docManager.setDocLastModUserType(paramUser.getLogintype());
/* 609 */       docManager.setDoccreatedate(str4);
/* 610 */       docManager.setDoclastmoddate(str4);
/* 611 */       docManager.setDoccreatetime(str5);
/* 612 */       docManager.setDoclastmodtime(str5);
/* 613 */       docManager.setDoclangurage(paramUser.getLanguage());
/* 614 */       docManager.setKeyword(str2);
/* 615 */       docManager.setIsapprover("0");
/* 616 */       docManager.setIsreply("");
/* 617 */       docManager.setDocdepartmentid(paramUser.getUserDepartment());
/* 618 */       docManager.setDocreplyable("1");
/* 619 */       docManager.setAccessorycount(1);
/* 620 */       docManager.setParentids("" + n);
/* 621 */       docManager.setOrderable("" + secCategoryComInfo.getSecOrderable(k));
/* 622 */       docManager.setClientAddress(paramString3);
/* 623 */       docManager.setUserid(paramUser.getUID());
/* 624 */       DocCoder docCoder = new DocCoder();
/* 625 */       docManager.setDocCode(docCoder.getDocCoder("" + k));
/* 626 */       int i1 = -1;
/* 627 */       int i2 = -1;
/*     */       
/* 629 */       if (secCategoryComInfo.isEditionOpen(k)) {
/*     */         
/* 631 */         if (i2 == -1) {
/* 632 */           i2 = docManager.getNextEditionId(new RecordSet());
/*     */         }
/* 634 */         i1 = docComInfo.getEdition(i2) + 1;
/*     */       } 
/* 636 */       docManager.setDocEditionId(i2);
/* 637 */       docManager.setDocEdition(i1);
/* 638 */       docManager.AddDocInfo();
/*     */       
/* 640 */       docManager.AddShareInfo();
/* 641 */       docComInfo.addDocInfoCache("" + n);
/* 642 */       docViewer.setDocShareByDoc("" + n);
/* 643 */       return n;
/* 644 */     } catch (Exception exception) {
/* 645 */       log.error("Catch a exception.", exception);
/* 646 */       return -1;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static String getFileMainName(String paramString) {
/* 657 */     if (paramString == null) {
/* 658 */       return "";
/*     */     }
/* 660 */     int i = paramString.lastIndexOf(".");
/* 661 */     if (i > -1) {
/* 662 */       paramString = paramString.substring(0, i);
/*     */     }
/* 664 */     return paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getFileExt(String paramString) {
/* 674 */     if (paramString == null || paramString.trim().equals("")) {
/* 675 */       return "";
/*     */     }
/* 677 */     int i = paramString.lastIndexOf(".");
/* 678 */     if (i == -1) {
/* 679 */       return "";
/*     */     }
/* 681 */     if (i + 1 >= paramString.length()) {
/* 682 */       return "";
/*     */     }
/* 684 */     return paramString.substring(i + 1);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean isFromMobile(String paramString) {
/* 698 */     if (paramString == null) {
/* 699 */       return false;
/*     */     }
/*     */     
/* 702 */     paramString = StringUtils.trim(paramString);
/* 703 */     if ("".equals(paramString) || "0".equals(paramString)) {
/* 704 */       return false;
/*     */     }
/*     */     
/* 707 */     int i = Util.getIntValue(paramString, 0);
/* 708 */     return (i > 0);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getAllOperateredResource(String paramString) {
/* 717 */     log.info("Start to invoke 'getAllOperateredResource' method.");
/* 718 */     if (StringUtils.isEmpty(paramString)) {
/* 719 */       log.warn("The requestid is emtpy, program return emtpy directly.");
/* 720 */       return "";
/*     */     } 
/*     */     
/* 723 */     String str1 = "Select distinct operator from workflow_requestLog where requestid = %1$s";
/* 724 */     str1 = String.format(str1, new Object[] { paramString });
/* 725 */     log.info("Following is the run sql:\n" + str1);
/*     */     
/* 727 */     RecordSet recordSet = new RecordSet();
/* 728 */     recordSet.executeSql(str1);
/* 729 */     StringBuffer stringBuffer = new StringBuffer();
/* 730 */     while (recordSet.next()) {
/* 731 */       stringBuffer.append(",").append(recordSet.getString(1));
/*     */     }
/*     */     
/* 734 */     String str2 = stringBuffer.toString();
/* 735 */     str2 = "".equals(str2) ? "" : str2.substring(1);
/* 736 */     return str2;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static int uploadImage(String paramString1, String paramString2, User paramUser) {
/* 747 */     log.info("Start to upload append data.");
/*     */ 
/*     */     
/* 750 */     byte[] arrayOfByte = null;
/*     */     try {
/* 752 */       arrayOfByte = Base64.decode(paramString1);
/* 753 */     } catch (org.apache.ws.commons.util.Base64.DecodingException decodingException) {
/* 754 */       log.error("Catch a exception during decode data, return -1.", (Throwable)decodingException);
/* 755 */       return -1;
/*     */     } 
/*     */     
/* 758 */     DocAttachment docAttachment = writeData(arrayOfByte, paramString2);
/* 759 */     if (docAttachment == null) {
/* 760 */       log.error("It is fialure to write the data, return -1.");
/* 761 */       return -1;
/*     */     } 
/*     */     
/* 764 */     docAttachment.setFiletype("application/octet-stream");
/*     */     
/* 766 */     int i = saveAttachment(docAttachment);
/* 767 */     if (i == -1) {
/* 768 */       log.error("It is fialure to save doc attenchent into database, return -1.");
/* 769 */       return -1;
/*     */     } 
/*     */     
/* 772 */     return i;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public void saveHandWrittenMessage(String paramString1, String paramString2, String paramString3, String paramString4, String paramString5, String paramString6, int paramInt) {
/*     */     try {
/* 779 */       RecordSet recordSet = new RecordSet();
/* 780 */       String str = "insert into workflow_handwrittensigninfo(fileids,datas,singleTrimUrls,allurls,isEmpty,modeType,ismobile)values(?,?,?,?,?,?,?)";
/* 781 */       recordSet.executeUpdate(str, new Object[] { paramString1, paramString2, paramString3, paramString4, paramString5, paramString6, Integer.valueOf(paramInt) });
/* 782 */     } catch (Exception exception) {
/* 783 */       (new BaseBean()).writeLog("save handwrittensigninfo failed!", exception);
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/WorkflowSpeechAppend.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */