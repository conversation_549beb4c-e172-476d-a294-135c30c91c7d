/*     */ package weaver.workflow.request;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashSet;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpSession;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class RequestShare
/*     */   extends BaseBean
/*     */ {
/*     */   private static final String REQ_SPLIT = "_";
/*  20 */   RequestShare reqshare = new RequestShare();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static int parserRequestidinfo(HttpServletRequest paramHttpServletRequest) {
/*  27 */     HttpSession httpSession = paramHttpServletRequest.getSession();
/*     */     
/*  29 */     int i = Util.getIntValue(paramHttpServletRequest.getParameter("wflinkno"));
/*  30 */     String str = Util.null2String(httpSession.getAttribute("resrequestid" + i));
/*     */     
/*  32 */     return parserParentRequestidinfo(str, 0);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static int parserRequestidinfo(String paramString) {
/*  41 */     return parserParentRequestidinfo(paramString, 0);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static int parserParentRequestidinfo(String paramString) {
/*  50 */     return parserParentRequestidinfo(paramString, 1);
/*     */   }
/*     */   
/*     */   private static int parserParentRequestidinfo(String paramString, int paramInt) {
/*  54 */     boolean bool = false;
/*     */     
/*  56 */     if ("".equals(paramString) || paramString.indexOf("_") == -1) {
/*  57 */       return bool;
/*     */     }
/*     */     
/*  60 */     String[] arrayOfString = paramString.split("_");
/*  61 */     if (arrayOfString.length > paramInt) {
/*  62 */       return Util.getIntValue(arrayOfString[paramInt], 0);
/*     */     }
/*     */     
/*  65 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static void addRequestViewRightInfo(HttpServletRequest paramHttpServletRequest) {
/*  75 */     int i = Util.getIntValue(paramHttpServletRequest.getParameter("wflinkno"));
/*  76 */     HttpSession httpSession = paramHttpServletRequest.getSession();
/*     */     
/*  78 */     int j = Util.getIntValue(String.valueOf(httpSession.getAttribute("resrequestid" + i)), 0);
/*  79 */     if (j == 0) {
/*  80 */       j = Util.getIntValue(paramHttpServletRequest.getParameter("requestid"), 0);
/*     */     }
/*     */     
/*  83 */     int k = Util.getIntValue(String.valueOf(httpSession.getAttribute("slinkwfnum")));
/*  84 */     for (byte b = 0; b <= k; b++) {
/*  85 */       int m = Util.getIntValue(String.valueOf(httpSession.getAttribute("resrequestid" + b)));
/*     */       
/*  87 */       if (m > 0)
/*     */       {
/*  89 */         httpSession.setAttribute("resrequestid" + b, m + "_" + j);
/*     */       }
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean requestRightCheck(int paramInt1, int paramInt2, String paramString1, int paramInt3, String paramString2, Map<String, String> paramMap) {
/* 105 */     boolean bool = false;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 112 */     if (paramInt3 == 1) {
/* 113 */       RecordSet recordSet = new RecordSet();
/* 114 */       ArrayList<String> arrayList1 = new ArrayList();
/* 115 */       ArrayList<String> arrayList2 = new ArrayList();
/* 116 */       recordSet.executeSql("select userid, usertype  from workflow_currentoperator where requestid=" + paramString2);
/* 117 */       while (recordSet.next()) {
/* 118 */         String str = recordSet.getString("userid") + "_" + recordSet.getString("usertype");
/* 119 */         arrayList1.add(str);
/*     */       } 
/*     */       
/* 122 */       recordSet.executeSql("select userid, usertype  from workflow_currentoperator where requestid=" + paramString1);
/* 123 */       while (recordSet.next()) {
/* 124 */         String str = recordSet.getString("userid") + "_" + recordSet.getString("usertype");
/* 125 */         arrayList2.add(str);
/*     */       } 
/*     */       
/* 128 */       arrayList1.retainAll(arrayList2);
/*     */       
/* 130 */       if (arrayList1.size() > 0) {
/* 131 */         return true;
/*     */       }
/* 133 */       recordSet.executeSql("select mainrequestid from workflow_requestbase where requestid=" + paramString2);
/* 134 */       if (recordSet.next()) {
/* 135 */         String str = recordSet.getString("mainrequestid");
/* 136 */         if (str.equals(paramString1)) return true;
/*     */       
/*     */       } 
/*     */ 
/*     */       
/* 141 */       List<String> list = getSubRequestIds(Util.getIntValue(paramString2, 0));
/* 142 */       if (list.size() > 0) {
/* 143 */         ArrayList<String> arrayList = new ArrayList();
/* 144 */         for (byte b = 0; b < list.size(); b++) {
/* 145 */           recordSet.executeSql("select userid, usertype  from workflow_currentoperator where requestid=" + (String)list.get(b));
/* 146 */           while (recordSet.next()) {
/* 147 */             String str = recordSet.getString("userid") + "_" + recordSet.getString("usertype");
/* 148 */             arrayList.add(str);
/*     */           } 
/* 150 */           arrayList.retainAll(arrayList2);
/* 151 */           if (arrayList.size() > 0) {
/* 152 */             return true;
/*     */           }
/*     */         } 
/*     */       } 
/*     */     } 
/*     */     
/* 158 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static List<String> getSubRequestIds(int paramInt) {
/* 166 */     ArrayList<String> arrayList = new ArrayList();
/* 167 */     RecordSet recordSet = new RecordSet();
/*     */     
/* 169 */     StringBuilder stringBuilder = new StringBuilder();
/* 170 */     stringBuilder.append("SELECT sub.subrequestid requestid FROM workflow_subwfrequest sub LEFT OUTER JOIN workflow_requestbase req ON req.requestid=sub.subrequestid ");
/*     */ 
/*     */ 
/*     */     
/* 174 */     stringBuilder.append(" where sub.mainrequestid='").append(paramInt).append("' and req.currentnodetype = '3' ");
/*     */     
/* 176 */     recordSet.executeSql(stringBuilder.toString());
/* 177 */     while (recordSet.next()) {
/* 178 */       arrayList.add(Util.null2String(recordSet.getString("requestid")));
/*     */     }
/* 180 */     stringBuilder.setLength(0);
/* 181 */     stringBuilder.append("SELECT * FROM workflow_requestbase WHERE mainrequestid='").append(paramInt).append("'");
/*     */ 
/*     */ 
/*     */     
/* 185 */     stringBuilder.append(" and currentnodetype = '3' ");
/*     */     
/* 187 */     recordSet.executeSql(stringBuilder.toString());
/* 188 */     while (recordSet.next()) {
/* 189 */       arrayList.add(Util.null2String(recordSet.getString("requestid")));
/*     */     }
/*     */ 
/*     */     
/* 193 */     HashSet<String> hashSet = new HashSet<String>(arrayList);
/* 194 */     arrayList.clear();
/* 195 */     arrayList.addAll(hashSet);
/*     */     
/* 197 */     return arrayList;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/RequestShare.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */