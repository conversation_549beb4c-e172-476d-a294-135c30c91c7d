/*     */ package weaver.workflow.request;
/*     */ 
/*     */ import java.io.BufferedReader;
/*     */ import java.io.IOException;
/*     */ import java.io.InputStream;
/*     */ import java.io.InputStreamReader;
/*     */ import java.io.OutputStream;
/*     */ import java.io.OutputStreamWriter;
/*     */ import java.io.PrintWriter;
/*     */ import java.net.Socket;
/*     */ import java.net.URL;
/*     */ import java.util.HashMap;
/*     */ import java.util.Iterator;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class RequestDataPost
/*     */ {
/*  27 */   private HashMap param = new HashMap<Object, Object>();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setParam(String paramString1, String paramString2) {
/*  36 */     this.param.put(paramString1, paramString2);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String getData() {
/*  44 */     Iterator<String> iterator = this.param.keySet().iterator();
/*  45 */     String str1 = "";
/*  46 */     String str2 = "";
/*  47 */     String str3 = "";
/*  48 */     while (iterator.hasNext()) {
/*  49 */       str1 = iterator.next();
/*  50 */       str2 = (String)this.param.get(str1);
/*  51 */       str3 = str3 + "&" + str1 + "=" + str2;
/*     */     } 
/*  53 */     if (str3.length() > 0) {
/*  54 */       str3 = str3.substring(1);
/*     */     }
/*  56 */     return str3;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String doPost(String paramString) {
/*  67 */     URL uRL = null;
/*  68 */     Socket socket = null;
/*  69 */     int i = 80;
/*     */     
/*  71 */     InputStream inputStream = null;
/*  72 */     OutputStream outputStream = null;
/*  73 */     BufferedReader bufferedReader = null;
/*  74 */     PrintWriter printWriter = null;
/*     */     
/*  76 */     String str1 = "";
/*  77 */     String str2 = "";
/*     */     
/*  79 */     String str3 = getData();
/*     */     
/*     */     try {
/*  82 */       uRL = new URL(paramString);
/*  83 */       str2 = uRL.getPath();
/*  84 */       str1 = uRL.getHost();
/*  85 */       i = uRL.getPort();
/*  86 */       if (i == -1) {
/*  87 */         i = 80;
/*     */       }
/*  89 */       socket = new Socket(str1, 80);
/*  90 */       if (socket == null) {
/*  91 */         return "2";
/*     */       }
/*     */       
/*  94 */       inputStream = socket.getInputStream();
/*  95 */       outputStream = socket.getOutputStream();
/*     */       
/*  97 */       bufferedReader = new BufferedReader(new InputStreamReader(inputStream));
/*  98 */       printWriter = new PrintWriter(new OutputStreamWriter(outputStream), true);
/*     */       
/* 100 */       if (inputStream == null || outputStream == null || bufferedReader == null || printWriter == null) {
/* 101 */         return "2";
/*     */       }
/*     */ 
/*     */       
/* 105 */       printWriter.print("POST " + str2 + " HTTP/1.1\r\n");
/* 106 */       printWriter.print("Accept:*/*\r\n");
/*     */ 
/*     */       
/* 109 */       printWriter.print("Cache-Control:no-cache\r\n");
/* 110 */       printWriter.print("Connection:Keep-Alive\r\n");
/* 111 */       printWriter.print("Content-Length:" + (str3.getBytes()).length + "\r\n");
/* 112 */       printWriter.print("Content-Type:application/x-www-form-urlencoded\r\n");
/* 113 */       printWriter.print("Host:" + str1 + "\r\n");
/* 114 */       printWriter.print("User-Agent:Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.0)\r\n");
/* 115 */       printWriter.print("Pragma:no-cache\r\n");
/* 116 */       printWriter.print("Connection:close\r\n\r\n");
/*     */       
/* 118 */       printWriter.println(str3);
/*     */       
/* 120 */       String str4 = "";
/* 121 */       String str5 = "";
/*     */ 
/*     */       
/* 124 */       while ((str5 = bufferedReader.readLine()) != null) {
/* 125 */         str4 = str4 + str5 + "\r\n";
/*     */       }
/*     */       
/* 128 */       if (str4.indexOf("\r\n\r\n") != -1) {
/* 129 */         str4 = str4.substring(str4.indexOf("\r\n\r\n"));
/*     */       }
/* 131 */       return str4.trim();
/*     */     }
/* 133 */     catch (IOException iOException) {
/* 134 */       return "2";
/* 135 */     } catch (NumberFormatException numberFormatException) {
/* 136 */       return "2";
/*     */     } finally {
/*     */       try {
/* 139 */         if (printWriter != null) {
/* 140 */           printWriter.close();
/*     */         }
/* 142 */         if (bufferedReader != null) {
/* 143 */           bufferedReader.close();
/*     */         }
/* 145 */       } catch (IOException iOException) {}
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/RequestDataPost.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */