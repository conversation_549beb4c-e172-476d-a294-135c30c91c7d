/*     */ package weaver.workflow.request;
/*     */ 
/*     */ import java.util.Calendar;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.crm.Maint.CustomerInfoComInfo;
/*     */ import weaver.file.FileUpload;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ import weaver.workflow.workflow.WorkflowComInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class RequestLog
/*     */   extends BaseBean
/*     */ {
/*  44 */   private String currentdate = "";
/*  45 */   private String currenttime = "";
/*  46 */   private char flag = Util.getSeparator();
/*     */   private boolean isoracle = false;
/*  48 */   private ResourceComInfo resourceComInfo = null;
/*  49 */   private CustomerInfoComInfo customerInfoComInfo = null;
/*     */   
/*     */   private HttpServletRequest request;
/*     */   private boolean isRequest;
/*     */   private FileUpload fu;
/*  54 */   private int isremark = -1;
/*  55 */   private int currId = -1;
/*     */ 
/*     */   
/*     */   public void setRequest(HttpServletRequest paramHttpServletRequest) {
/*  59 */     this.request = paramHttpServletRequest;
/*  60 */     this.isRequest = true;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setRequest(FileUpload paramFileUpload) {
/*  69 */     this.fu = paramFileUpload;
/*  70 */     this.isRequest = false;
/*     */   }
/*     */ 
/*     */   
/*     */   public RequestLog() {
/*  75 */     RecordSet recordSet = new RecordSet();
/*  76 */     Calendar calendar = Calendar.getInstance();
/*  77 */     this
/*     */       
/*  79 */       .currentdate = Util.add0(calendar.get(1), 4) + "-" + Util.add0(calendar.get(2) + 1, 2) + "-" + Util.add0(calendar.get(5), 2);
/*     */     
/*  81 */     this
/*     */       
/*  83 */       .currenttime = Util.add0(calendar.get(11), 2) + ":" + Util.add0(calendar.get(12), 2) + ":" + Util.add0(calendar.get(13), 2);
/*     */ 
/*     */ 
/*     */     
/*  87 */     recordSet.executeProc("GetDBDateAndTime", "");
/*  88 */     if (recordSet.next()) {
/*  89 */       this.currentdate = recordSet.getString("dbdate");
/*  90 */       this.currenttime = recordSet.getString("dbtime");
/*     */     } 
/*     */ 
/*     */     
/*  94 */     this.isoracle = recordSet.getDBType().equals("oracle");
/*     */     try {
/*  96 */       this.resourceComInfo = new ResourceComInfo();
/*  97 */       this.customerInfoComInfo = new CustomerInfoComInfo();
/*  98 */     } catch (Exception exception) {}
/*     */   }
/*     */ 
/*     */   
/*     */   public void saveLog(int paramInt1, int paramInt2, int paramInt3, String paramString1, String paramString2, User paramUser) {
/* 103 */     saveLog(paramInt1, paramInt2, paramInt3, paramString1, paramString2, paramUser, "0", 0, "", "", "");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void saveLog(int paramInt1, int paramInt2, int paramInt3, String paramString1, String paramString2, User paramUser, String paramString3, int paramInt4, String paramString4, String paramString5) {
/* 110 */     saveLog2(paramInt1, paramInt2, paramInt3, paramString1, paramString2, paramUser, paramString3, paramInt4, paramString4, paramString5, "");
/*     */   }
/*     */   public void saveLog(int paramInt1, int paramInt2, int paramInt3, String paramString1, String paramString2, User paramUser, String paramString3, int paramInt4, int paramInt5, String paramString4) {
/* 113 */     saveLog2(paramInt1, paramInt2, paramInt3, paramString1, paramString2, paramUser, paramString3, paramInt4, paramInt5, paramString4, "");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void saveLog(int paramInt1, int paramInt2, int paramInt3, String paramString1, String paramString2, User paramUser, String paramString3, int paramInt4, String paramString4, String paramString5, String paramString6) {
/* 120 */     saveLog2(paramInt1, paramInt2, paramInt3, paramString1, paramString2, paramUser, paramString3, paramInt4, paramString4, paramString5, paramString6);
/*     */   }
/*     */   
/*     */   public void saveLog(int paramInt1, int paramInt2, int paramInt3, String paramString1, String paramString2, User paramUser, String paramString3, int paramInt4, int paramInt5, String paramString4, String paramString5) {
/* 124 */     saveLog2(paramInt1, paramInt2, paramInt3, paramString1, paramString2, paramUser, paramString3, paramInt4, paramInt5, paramString4, paramString5);
/*     */   }
/*     */   
/*     */   public String saveLog2(int paramInt1, int paramInt2, int paramInt3, String paramString1, String paramString2, User paramUser, String paramString3, int paramInt4, String paramString4, String paramString5) {
/* 128 */     return saveLog2(paramInt1, paramInt2, paramInt3, paramString1, paramString2, paramUser, paramString3, paramInt4, paramString4, paramString5, "");
/*     */   }
/*     */   
/*     */   public String saveLog2(int paramInt1, int paramInt2, int paramInt3, String paramString1, String paramString2, User paramUser, String paramString3, int paramInt4, int paramInt5, String paramString4) {
/* 132 */     return saveLog2(paramInt1, paramInt2, paramInt3, paramString1, paramString2, paramUser, paramString3, paramInt4, paramInt5, paramString4, "");
/*     */   }
/*     */   
/*     */   public String saveLog2(int paramInt1, int paramInt2, int paramInt3, String paramString1, String paramString2, User paramUser, String paramString3, int paramInt4, String paramString4, String paramString5, String paramString6) {
/* 136 */     return saveLog2(paramInt1, paramInt2, paramInt3, paramString1, paramString2, paramUser, paramString3, paramInt4, paramString4, paramString5, paramString6, "", "");
/*     */   }
/*     */   
/*     */   public String saveLog2(int paramInt1, int paramInt2, int paramInt3, String paramString1, String paramString2, User paramUser, String paramString3, int paramInt4, int paramInt5, String paramString4, String paramString5) {
/* 140 */     return saveLog2(paramInt1, paramInt2, paramInt3, paramString1, paramString2, paramUser, paramString3, paramInt4, paramInt5, paramString4, paramString5, "", "");
/*     */   }
/*     */   
/*     */   public String saveLog2(int paramInt1, int paramInt2, int paramInt3, String paramString1, String paramString2, User paramUser, String paramString3, int paramInt4, int paramInt5, String paramString4, String paramString5, String paramString6, String paramString7) {
/* 144 */     return saveLog2(paramInt1, paramInt2, paramInt3, paramString1, paramString2, paramUser, paramString3, paramInt4, String.valueOf(paramInt5), paramString4, paramString5, paramString6, paramString7);
/*     */   }
/*     */   public String saveLog2(int paramInt1, int paramInt2, int paramInt3, String paramString1, String paramString2, User paramUser, String paramString3, int paramInt4, String paramString4, String paramString5, String paramString6, String paramString7, String paramString8) {
/* 147 */     RecordSet recordSet = new RecordSet();
/* 148 */     boolean bool = paramUser.getLogintype().equals("1") ? false : true;
/* 149 */     String str1 = "";
/* 150 */     if (this.isRequest) {
/* 151 */       if (this.request != null) {
/* 152 */         str1 = Util.null2String(this.request.getRemoteAddr());
/*     */       }
/*     */     }
/* 155 */     else if (this.fu != null) {
/* 156 */       str1 = Util.null2String(this.fu.getRemoteAddr());
/*     */     } 
/*     */ 
/*     */     
/* 160 */     int i = 0;
/* 161 */     String str2 = "";
/* 162 */     String str3 = "";
/* 163 */     if (this.isRequest) {
/* 164 */       if (this.request != null) {
/* 165 */         i = Util.getIntValue(this.request.getParameter("workflowRequestLogId"), 0);
/* 166 */         str2 = Util.null2String(this.request.getParameter("signdocids"));
/* 167 */         str3 = Util.null2String(this.request.getParameter("signworkflowids"));
/*     */       }
/*     */     
/* 170 */     } else if (this.fu != null) {
/* 171 */       i = Util.getIntValue(this.fu.getParameter("workflowRequestLogId"), 0);
/* 172 */       str2 = Util.null2String(this.fu.getParameter("signdocids"));
/* 173 */       str3 = Util.null2String(this.fu.getParameter("signworkflowids"));
/*     */     } 
/*     */ 
/*     */     
/* 177 */     int j = paramInt3;
/* 178 */     String str4 = "";
/* 179 */     String str5 = "";
/*     */     
/* 181 */     String str6 = "";
/*     */     
/* 183 */     if (WorkflowSpeechAppend.isFromMobile(paramString3)) {
/* 184 */       str6 = paramString5;
/*     */     } else {
/* 186 */       int i2 = 0;
/* 187 */       recordSet.executeSql("select ismode from workflow_flownode where workflowid=" + paramInt1 + " and nodeid=" + paramInt3);
/* 188 */       if (recordSet.next()) {
/* 189 */         i2 = Util.getIntValue(recordSet.getString("ismode"), 0);
/*     */       }
/* 191 */       if (i2 != 1) {
/* 192 */         RequestAnnexUpload requestAnnexUpload = new RequestAnnexUpload();
/* 193 */         requestAnnexUpload.setRequest(this.fu);
/* 194 */         requestAnnexUpload.setUser(paramUser);
/* 195 */         str6 = requestAnnexUpload.AnnexUpload();
/*     */       } else {
/* 197 */         int i3 = 0;
/*     */         try {
/* 199 */           WorkflowComInfo workflowComInfo = new WorkflowComInfo();
/* 200 */           i3 = Util.getIntValue(workflowComInfo.getFormId("" + paramInt1), 0);
/* 201 */         } catch (Exception exception) {
/* 202 */           i3 = 0;
/*     */         } 
/* 204 */         String str = "0";
/* 205 */         recordSet.executeSql("select * from workflow_modeview where formid=" + i3 + " and nodeid=" + paramInt3 + " and fieldid=-4");
/* 206 */         if (recordSet.next()) {
/* 207 */           str = "1";
/*     */         }
/* 209 */         if ("1".equals(str)) {
/* 210 */           if (this.isRequest) {
/* 211 */             if (this.request != null) {
/* 212 */               str6 = Util.null2String(this.request.getParameter("qianzi"));
/*     */             }
/*     */           }
/* 215 */           else if (this.fu != null) {
/* 216 */             str6 = Util.null2String(this.fu.getParameter("qianzi"));
/*     */           } 
/*     */         } else {
/*     */           
/* 220 */           RequestAnnexUpload requestAnnexUpload = new RequestAnnexUpload();
/* 221 */           requestAnnexUpload.setRequest(this.fu);
/* 222 */           requestAnnexUpload.setUser(paramUser);
/* 223 */           str6 = requestAnnexUpload.AnnexUpload();
/*     */         } 
/*     */       } 
/*     */     } 
/*     */ 
/*     */     
/* 229 */     String str7 = "";
/* 230 */     int k = -1;
/* 231 */     int m = 0;
/* 232 */     int n = 1;
/*     */     
/* 234 */     ResourceComInfo resourceComInfo = null;
/*     */     try {
/* 236 */       resourceComInfo = new ResourceComInfo();
/* 237 */     } catch (Exception exception) {}
/*     */ 
/*     */ 
/*     */     
/* 241 */     int i1 = 0;
/* 242 */     String str8 = getReceiverSql(paramInt2, this.currId, this.isremark, paramString1, paramUser);
/* 243 */     if (!"".equals(str8)) {
/* 244 */       recordSet.execute(str8);
/* 245 */       i1 = recordSet.getCounts();
/*     */     } 
/* 247 */     if (i1 <= 0) {
/* 248 */       recordSet.executeSql("select userid,usertype,agentorbyagentid, agenttype,nodeid from workflow_currentoperator where isremark in ('0','4')  and requestid = " + paramInt2 + " and agenttype <> 1 order by showorder asc");
/*     */     }
/* 250 */     while (recordSet.next()) {
/* 251 */       if ("0".equals(recordSet.getString("usertype"))) {
/* 252 */         if (recordSet.getInt("agenttype") == 0) {
/* 253 */           String str11 = Util.toScreen(resourceComInfo.getResourcename(recordSet.getString("userid")), paramUser.getLanguage());
/* 254 */           if (str4.indexOf("," + str11 + ",") == -1 && str4.indexOf(str11 + ",") == -1) {
/* 255 */             str4 = str4 + str11 + ",";
/* 256 */             str5 = str5 + Util.null2String(recordSet.getString("userid")) + ",";
/*     */           }  continue;
/* 258 */         }  if (recordSet.getInt("agenttype") == 1) {
/* 259 */           int i2 = recordSet.getInt("agentorbyagentid");
/* 260 */           int i3 = recordSet.getInt("nodeid");
/* 261 */           RecordSet recordSet1 = new RecordSet();
/* 262 */           recordSet1.executeSql("select isremark from workflow_currentoperator where  requestid = " + paramInt2 + " and preisremark ='9' and userid = " + i2 + " and nodeid=" + i3);
/* 263 */           if (recordSet1.getCounts() > 0) {
/*     */             continue;
/*     */           }
/* 266 */           String str11 = Util.toScreen(resourceComInfo.getResourcename(recordSet.getString("userid")), paramUser.getLanguage());
/* 267 */           if (str4.indexOf("," + str11 + ",") == -1 && str4.indexOf(str11 + ",") == -1) {
/* 268 */             str4 = str4 + str11 + ",";
/* 269 */             str5 = str5 + Util.null2String(recordSet.getString("userid")) + ",";
/*     */           } 
/*     */           continue;
/*     */         } 
/* 273 */         String str10 = Util.toScreen(resourceComInfo.getResourcename(recordSet.getString("agentorbyagentid")), paramUser.getLanguage()) + "->" + Util.toScreen(resourceComInfo.getResourcename(recordSet.getString("userid")), paramUser.getLanguage());
/* 274 */         if (recordSet.getInt("agenttype") == 2 && str4.indexOf("," + str10 + ",") == -1 && str4.indexOf(str10 + ",") == -1) {
/* 275 */           str4 = str4 + str10 + ",";
/* 276 */           str5 = str5 + Util.null2String(recordSet.getString("userid")) + ",";
/*     */         } 
/*     */         
/*     */         continue;
/*     */       } 
/* 281 */       String str = Util.toScreen(this.customerInfoComInfo.getCustomerInfoname(recordSet.getString("userid")), paramUser.getLanguage());
/* 282 */       if (str4.indexOf("," + str + ",") == -1 && str4.indexOf(str + ",") == -1) {
/* 283 */         str4 = str4 + str + ",";
/* 284 */         str5 = str5 + Util.null2String(recordSet.getString("userid")) + ",";
/*     */       } 
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/* 290 */     str7 = "select agentorbyagentid, agenttype, showorder from workflow_currentoperator where nodeid = " + paramInt3 + " and requestid = " + paramInt2 + " and userid = " + paramUser.getUID();
/* 291 */     if (paramString1.equals("9")) {
/* 292 */       str7 = str7 + " order by receivedate desc,receivetime desc,showorder asc";
/* 293 */     } else if (paramString1.equals("b")) {
/* 294 */       str7 = str7 + " order by operatedate desc, operatetime desc, id ";
/*     */     } else {
/* 296 */       str7 = str7 + " order by showorder asc";
/*     */     } 
/* 298 */     recordSet.executeSql(str7);
/* 299 */     if (recordSet.next()) {
/* 300 */       k = recordSet.getInt("agentorbyagentid");
/* 301 */       m = recordSet.getInt("agenttype");
/* 302 */       n = recordSet.getInt("showorder");
/*     */     } 
/* 304 */     if (paramString1.equals("9")) {
/* 305 */       recordSet.executeSql("select max(showorder) as maxshow from workflow_requestlog where requestid = " + paramInt2 + " and nodeid = " + paramInt3 + " and logtype = '9'");
/* 306 */       recordSet.next();
/* 307 */       if (recordSet.getInt("maxshow") != -1) {
/* 308 */         n = recordSet.getInt("maxshow") + 1;
/*     */       } else {
/* 310 */         n = 10000;
/*     */       } 
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 317 */     String str9 = "" + paramInt2 + this.flag + paramInt1 + this.flag + paramInt3 + this.flag + paramString1 + this.flag + this.currentdate + this.flag + this.currenttime + this.flag + paramUser.getUID() + this.flag + str1 + this.flag + bool + this.flag + j + this.flag + str4.trim() + this.flag + k + this.flag + m + this.flag + n + this.flag + str6 + this.flag + i + this.flag + str2 + this.flag + str3 + this.flag + paramString3 + this.flag + paramInt4 + this.flag + paramString4 + this.flag + str5 + this.flag + paramString6 + this.flag + paramString7 + this.flag + paramString8;
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 322 */     return (new RequestManager()).execRequestlog(str9, recordSet, this.flag, paramString2);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, String> getReceivedPersonInfo(int paramInt1, int paramInt2, int paramInt3, String paramString, User paramUser) {
/* 335 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/* 337 */     ResourceComInfo resourceComInfo = null;
/*     */     try {
/* 339 */       resourceComInfo = new ResourceComInfo();
/* 340 */     } catch (Exception exception) {}
/*     */ 
/*     */     
/* 343 */     RecordSet recordSet = new RecordSet();
/* 344 */     String str1 = "";
/* 345 */     String str2 = "";
/*     */     
/* 347 */     String str3 = getReceiverSql(paramInt1, paramInt2, paramInt3, paramString, paramUser);
/* 348 */     if (!"".equals(str3)) {
/* 349 */       recordSet.execute(str3);
/* 350 */       while (recordSet.next()) {
/* 351 */         if ("0".equals(recordSet.getString("usertype"))) {
/* 352 */           if (recordSet.getInt("agenttype") == 0 || recordSet.getInt("agenttype") == 1) {
/* 353 */             String str5 = Util.toScreen(resourceComInfo.getResourcename(recordSet.getString("userid")), paramUser.getLanguage());
/* 354 */             if (str1.indexOf("," + str5 + ",") == -1 && str1.indexOf(str5 + ",") == -1) {
/* 355 */               str1 = str1 + str5 + ",";
/* 356 */               str2 = str2 + Util.null2String(recordSet.getString("userid")) + ",";
/*     */             }  continue;
/*     */           } 
/* 359 */           String str4 = Util.toScreen(resourceComInfo.getResourcename(recordSet.getString("agentorbyagentid")), paramUser.getLanguage()) + "->" + Util.toScreen(resourceComInfo.getResourcename(recordSet.getString("userid")), paramUser.getLanguage());
/* 360 */           if (recordSet.getInt("agenttype") == 2 && str1.indexOf("," + str4 + ",") == -1 && str1.indexOf(str4 + ",") == -1) {
/* 361 */             str1 = str1 + str4 + ",";
/* 362 */             str2 = str2 + Util.null2String(recordSet.getString("userid")) + ",";
/*     */           } 
/*     */           
/*     */           continue;
/*     */         } 
/* 367 */         String str = Util.toScreen(this.customerInfoComInfo.getCustomerInfoname(recordSet.getString("userid")), paramUser.getLanguage());
/* 368 */         if (str1.indexOf("," + str + ",") == -1 && str1.indexOf(str + ",") == -1) {
/* 369 */           str1 = str1 + str + ",";
/* 370 */           str2 = str2 + Util.null2String(recordSet.getString("userid")) + ",";
/*     */         } 
/*     */       } 
/*     */     } 
/*     */     
/* 375 */     hashMap.put("personStr", str1);
/* 376 */     hashMap.put("receivedPersonids", str2);
/* 377 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public String getReceiverSql(int paramInt1, int paramInt2, int paramInt3, String paramString, User paramUser) {
/* 382 */     String str1 = "";
/* 383 */     String str2 = "";
/* 384 */     if (this.isRequest && 
/* 385 */       this.request != null) {
/* 386 */       str2 = Util.null2String(this.request.getParameter("oldrequestlog"));
/*     */     }
/*     */     
/* 389 */     writeLog("==getReceiverSql requestid:" + paramInt1 + "  oldrequestlog:" + str2);
/* 390 */     if ("1".equals(str2)) {
/* 391 */       return str1;
/*     */     }
/* 393 */     String str3 = getLogids(paramInt1, paramInt2, paramInt3, paramString, paramUser);
/* 394 */     RecordSet recordSet = new RecordSet();
/* 395 */     if (!"".equals(str3)) {
/* 396 */       str1 = "SELECT operator AS userid,operatortype AS usertype,agentorbyagentid,agenttype FROM workflow_requestlog WHERE logid IN (" + str3 + ") ORDER BY logid ";
/*     */     } else {
/* 398 */       String str = "";
/* 399 */       if (paramInt2 > 0 && paramInt3 == 1) {
/* 400 */         int i = paramInt2;
/* 401 */         int j = -1;
/* 402 */         recordSet.executeQuery("select forwardid from workflow_forward where requestid = ? and beforwardid = ?", new Object[] { Integer.valueOf(paramInt1), Integer.valueOf(i) });
/* 403 */         if (recordSet.next()) {
/* 404 */           j = Util.getIntValue(Util.null2String(recordSet.getString("forwardid")), -1);
/*     */         }
/* 406 */         if (j == -1) {
/* 407 */           recordSet.executeQuery("select takid from workflow_currentoperator where id = ?", new Object[] { Integer.valueOf(i) });
/* 408 */           if (recordSet.next()) {
/* 409 */             j = Util.getIntValue(Util.null2String(recordSet.getString("takid")), -1);
/*     */           }
/*     */         } 
/* 412 */         str = j + "";
/* 413 */         if (!"".equals(str) && !"-1".equals(str)) {
/* 414 */           str1 = "select userid,usertype,agentorbyagentid, agenttype from workflow_currentoperator where id in (" + str + ") order by showorder asc";
/*     */         }
/* 416 */       } else if (paramInt3 == 11 && "y".equals(paramString)) {
/* 417 */         String str4 = "";
/* 418 */         recordSet.executeQuery("select uuid from workflow_chuanyue where requestid = ? and userid = ? and issubmitsign = '1' order by id desc ", new Object[] { Integer.valueOf(paramInt1), Integer.valueOf(paramUser.getUID()) });
/* 419 */         if (recordSet.next()) {
/* 420 */           str4 = Util.null2String(recordSet.getString("uuid"));
/*     */         }
/* 422 */         str1 = "SELECT operator AS userid,operatortype AS usertype,agentorbyagentid,agenttype FROM workflow_requestlog WHERE requestid = " + paramInt1 + " and logtype = 'c' and uuid = '" + str4 + "'";
/*     */       } 
/*     */     } 
/* 425 */     writeLog("==getReceiverSql currId:" + paramInt2 + " isremark:" + paramInt3 + " logtype:" + paramString + " logids:" + str3 + ";result:" + str1);
/* 426 */     return str1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getLogids(int paramInt1, int paramInt2, int paramInt3, String paramString, User paramUser) {
/* 439 */     String str = "";
/* 440 */     if (paramInt2 <= 0 || paramInt3 == -1) {
/* 441 */       return str;
/*     */     }
/* 443 */     if (paramInt3 == 1 || paramInt3 == 11) {
/* 444 */       RecordSet recordSet = new RecordSet();
/* 445 */       String str1 = "";
/* 446 */       String str2 = "";
/* 447 */       String str3 = "SELECT receivedate,receivetime FROM workflow_currentoperator WHERE id = " + paramInt2;
/* 448 */       recordSet.execute(str3);
/* 449 */       if (recordSet.next()) {
/* 450 */         str1 = recordSet.getString("receivedate");
/* 451 */         str2 = recordSet.getString("receivetime");
/*     */       } 
/* 453 */       String str4 = "";
/* 454 */       if ("b".equals(paramString)) {
/* 455 */         str4 = "a";
/* 456 */       } else if ("9".equals(paramString)) {
/* 457 */         str4 = "7";
/* 458 */       } else if ("y".equals(paramString)) {
/* 459 */         str4 = "c";
/*     */       } 
/*     */       
/* 462 */       if (!"".equals(str4)) {
/* 463 */         str3 = "SELECT logid,receivedpersonids FROM workflow_requestlog WHERE requestid = " + paramInt1 + " AND logtype='" + str4 + "' AND operatedate >= '" + str1 + "' and operatetime>='" + str2 + "' ORDER BY logid ";
/* 464 */         recordSet.execute(str3);
/* 465 */         while (recordSet.next()) {
/* 466 */           int i = recordSet.getInt("logid");
/* 467 */           String str5 = recordSet.getString("receivedpersonids");
/* 468 */           if (("," + str5 + ",").indexOf("," + paramUser.getUID() + ",") > -1) {
/* 469 */             str = str + "," + i;
/*     */           }
/*     */         } 
/* 472 */         if (!"".equals(str)) {
/* 473 */           str = str.substring(1);
/*     */         }
/*     */       } 
/*     */     } 
/* 477 */     return str;
/*     */   }
/*     */   
/*     */   public String getCurrentdate() {
/* 481 */     return this.currentdate;
/*     */   }
/*     */   
/*     */   public void setCurrentdate(String paramString) {
/* 485 */     this.currentdate = paramString;
/*     */   }
/*     */   
/*     */   public String getCurrenttime() {
/* 489 */     return this.currenttime;
/*     */   }
/*     */   
/*     */   public void setCurrenttime(String paramString) {
/* 493 */     this.currenttime = paramString;
/*     */   }
/*     */   
/*     */   public int getIsremark() {
/* 497 */     return this.isremark;
/*     */   }
/*     */   
/*     */   public void setIsremark(int paramInt) {
/* 501 */     this.isremark = paramInt;
/*     */   }
/*     */   
/*     */   public int getCurrId() {
/* 505 */     return this.currId;
/*     */   }
/*     */   
/*     */   public void setCurrId(int paramInt) {
/* 509 */     this.currId = paramInt;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/RequestLog.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */