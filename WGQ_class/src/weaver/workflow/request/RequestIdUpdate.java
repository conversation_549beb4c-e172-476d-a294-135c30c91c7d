/*    */ package weaver.workflow.request;
/*    */ 
/*    */ import com.engine.workflow.util.RequestIdSequenceUtil;
/*    */ import weaver.general.BaseBean;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class RequestIdUpdate
/*    */   extends BaseBean
/*    */ {
/* 26 */   private RequestIdSequenceUtil requestIdSequenceUtil = RequestIdSequenceUtil.getInstance();
/*    */ 
/*    */   
/*    */   public static RequestIdUpdate getInstance() {
/* 30 */     return new RequestIdUpdate();
/*    */   }
/*    */   
/*    */   public int[] getRequestNewId(String paramString) {
/* 34 */     return this.requestIdSequenceUtil.getNewRequestIdAndBillId(paramString);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/RequestIdUpdate.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */