/*    */ package weaver.workflow.request;
/*    */ 
/*    */ import weaver.general.BaseBean;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class AgentLog
/*    */   extends BaseBean
/*    */ {
/*    */   public String getDetailListLink(String paramString1, String paramString2) {
/* 13 */     String[] arrayOfString = paramString2.split("\\+");
/* 14 */     String str1 = arrayOfString[0];
/* 15 */     String str2 = arrayOfString[1];
/* 16 */     return "<a href='javascript:void(0);' onclick='searchDetail(" + paramString1 + "," + str1 + ")'>" + str2 + "</a>";
/*    */   }
/*    */   
/*    */   public String getBackDateString(String paramString1, String paramString2) {
/* 20 */     String[] arrayOfString = paramString2.split("\\+");
/* 21 */     String str1 = arrayOfString[0];
/* 22 */     String str2 = arrayOfString[1];
/* 23 */     if ("0".equals(str1) && "".equals(paramString1)) {
/* 24 */       paramString1 = str2;
/*    */     }
/* 26 */     return paramString1;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/AgentLog.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */