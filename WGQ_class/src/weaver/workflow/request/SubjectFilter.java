/*     */ package weaver.workflow.request;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.fna.general.FnaCommon;
/*     */ import weaver.fna.maintenance.BudgetfeeTypeComInfo;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class SubjectFilter
/*     */ {
/*     */   public void filterSubject(int paramInt1, int paramInt2, String paramString1, String paramString2) throws Exception {
/*  41 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  42 */     String str = FnaCommon.getFnaWfFieldInfo4Expense(paramInt2, hashMap);
/*     */ 
/*     */ 
/*     */     
/*  46 */     paramString1 = Util.null2String(paramString1).trim();
/*  47 */     paramString2 = Util.null2String(paramString2).trim();
/*     */     
/*  49 */     if ("fnaFeeWf".equals(str) && paramString2.equalsIgnoreCase(paramString1 + "_dt1")) {
/*     */ 
/*     */       
/*  52 */       String str1 = (String)hashMap.get("fieldIdSubject_fieldName");
/*  53 */       String str2 = (String)hashMap.get("fieldIdOrgType_fieldName");
/*  54 */       String str3 = (String)hashMap.get("fieldIdOrgId_fieldName");
/*     */       
/*  56 */       RecordSet recordSet1 = new RecordSet();
/*  57 */       RecordSet recordSet2 = new RecordSet();
/*     */       
/*  59 */       StringBuffer stringBuffer = new StringBuffer();
/*     */       
/*  61 */       stringBuffer.append(" select fmd.* from ").append(paramString2).append(" fmd ");
/*  62 */       stringBuffer.append(" join ").append(paramString1).append(" fm on fmd.mainid = fm.id ");
/*  63 */       stringBuffer.append(" where fm.requestId = ").append(paramInt1);
/*     */       
/*  65 */       recordSet1.executeSql(stringBuffer.toString());
/*     */       
/*  67 */       while (recordSet1.next()) {
/*     */         
/*  69 */         int i = Util.getIntValue(recordSet1.getString("id"), 0);
/*  70 */         int j = Util.getIntValue(recordSet1.getString(str1), 0);
/*  71 */         int k = Util.getIntValue(recordSet1.getString(str2), -1);
/*  72 */         int m = Util.getIntValue(recordSet1.getString(str3), 0);
/*     */         
/*  74 */         if (j > 0 && m > 0 && (k >= 0 || k <= 3)) {
/*     */           ResourceComInfo resourceComInfo;
/*  76 */           char c = Character.MIN_VALUE;
/*     */           
/*  78 */           switch (k) {
/*     */             case 0:
/*  80 */               resourceComInfo = new ResourceComInfo();
/*  81 */               c = '\002';
/*  82 */               m = Util.getIntValue(resourceComInfo.getDepartmentID(m + ""));
/*     */               break;
/*     */             
/*     */             case 1:
/*  86 */               c = '\002';
/*     */               break;
/*     */             case 2:
/*  89 */               c = '\001';
/*     */               break;
/*     */             case 3:
/*  92 */               c = '䙔';
/*     */               break;
/*     */           } 
/*     */ 
/*     */           
/*  97 */           BudgetfeeTypeComInfo budgetfeeTypeComInfo = new BudgetfeeTypeComInfo();
/*  98 */           boolean bool = budgetfeeTypeComInfo.checkRuleSetRight(c, m, j);
/*     */ 
/*     */           
/* 101 */           if (!bool) {
/*     */             
/* 103 */             StringBuffer stringBuffer1 = new StringBuffer();
/*     */             
/* 105 */             stringBuffer1.append(" update ").append(paramString2);
/* 106 */             stringBuffer1.append(" set ").append(str1).append(" = '' ");
/* 107 */             stringBuffer1.append(" where id = ").append(i);
/*     */             
/* 109 */             recordSet2.executeSql(stringBuffer1.toString());
/*     */           } 
/*     */         } 
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<String> multiDimensionFilterSubject(int paramInt1, int paramInt2, String paramString1, String paramString2) {
/* 130 */     int i = 0;
/*     */     
/* 132 */     int j = 0;
/*     */     
/* 134 */     int k = 0;
/* 135 */     int m = 0;
/* 136 */     ArrayList arrayList = new ArrayList();
/*     */     
/* 138 */     BaseBean baseBean = new BaseBean();
/*     */ 
/*     */     
/* 141 */     String str1 = "SELECT a.id,a.isFilter, a.onlyEnd, a.choice  FROM FnaMultiAccountSubject a  WHERE a.workflowId=? AND a.fieldId=? AND accountId = ? ";
/* 142 */     RecordSet recordSet = new RecordSet();
/* 143 */     recordSet.executeQuery(str1, new Object[] { Integer.valueOf(paramInt1), Integer.valueOf(paramInt2), paramString2 });
/* 144 */     if (recordSet.next()) {
/*     */       
/* 146 */       i = recordSet.getInt("isFilter");
/*     */       
/* 148 */       j = recordSet.getInt("onlyEnd");
/*     */       
/* 150 */       k = recordSet.getInt("choice");
/* 151 */       m = recordSet.getInt("id");
/*     */     } 
/* 153 */     baseBean.writeLog("SubjectFilter获取主表数据-SQL：" + str1);
/* 154 */     baseBean.writeLog("SubjectFilter获取主表数据-参数-isFilter:" + i + "  onlyEnd:" + j + "  choice:" + k + "   mainId:" + m);
/*     */     
/* 156 */     boolean bool1 = (i >= 1) ? true : false;
/* 157 */     boolean bool2 = (j >= 1) ? true : false;
/* 158 */     boolean bool3 = (k >= 1) ? true : false;
/* 159 */     baseBean.writeLog("SubjectFilter获取主表数据-转换为布尔值-filterFlag:" + bool1 + "  onlyEndFlag:" + bool2 + "  choiceFlag:" + bool3);
/*     */     
/* 161 */     String str2 = recordSet.getDBType();
/*     */     
/* 163 */     String str3 = paramString1 + " a ";
/* 164 */     String str4 = " where 1=1 and (a.isArchive <> 1  or a.isArchive is null) ";
/* 165 */     if (bool1) {
/*     */       String str;
/*     */ 
/*     */       
/* 169 */       if (bool3) {
/*     */         
/* 171 */         str = " = ";
/*     */       } else {
/*     */         
/* 174 */         str = " <> ";
/*     */       } 
/*     */ 
/*     */       
/* 178 */       if ("mysql".equalsIgnoreCase(str2)) {
/* 179 */         str3 = str3 + " join FnaMultiAccountSubjectDetail c ON left(a.subjectcode, length(c.subjectCode)) " + str + " c.subjectCode  join FnaMultiAccountSubject b ON b.id = c.mainId  ";
/*     */       
/*     */       }
/* 182 */       else if ("postgresql".equalsIgnoreCase(str2)) {
/* 183 */         str3 = str3 + " join FnaMultiAccountSubjectDetail c ON left(a.subjectcode, length(c.subjectCode)) " + str + " c.subjectCode  join FnaMultiAccountSubject b ON b.id = c.mainId  ";
/*     */       
/*     */       }
/* 186 */       else if ("sqlserver".equalsIgnoreCase(str2)) {
/* 187 */         str3 = str3 + " join FnaMultiAccountSubjectDetail c on left(a.subjectcode, len(c.subjectCode)) " + str + " c.subjectCode  join FnaMultiAccountSubject b ON b.id = c.mainId ";
/*     */       }
/*     */       else {
/*     */         
/* 191 */         str3 = str3 + " join FnaMultiAccountSubjectDetail c on substr(a.subjectcode, 0, length(c.subjectCode)) " + str + " c.subjectCode  join FnaMultiAccountSubject b ON b.id = c.mainId ";
/*     */       } 
/*     */     } 
/*     */ 
/*     */     
/* 196 */     if (bool2)
/*     */     {
/* 198 */       if ("mysql".equalsIgnoreCase(str2)) {
/* 199 */         str4 = str4 + " and NOT exists(select 1 from " + paramString1 + " t where t.subjectcodenew like concat(a.subjectcodenew,'_%')) ";
/*     */       }
/* 201 */       else if ("postgresql".equalsIgnoreCase(str2)) {
/* 202 */         str4 = str4 + " and NOT exists(select 1 from " + paramString1 + " t where t.subjectcodenew like concat(a.subjectcodenew,'_%')) ";
/*     */       }
/* 204 */       else if ("sqlserver".equalsIgnoreCase(str2)) {
/* 205 */         str4 = str4 + " and NOT exists(select 1 from " + paramString1 + " t where t.subjectcodenew like a.subjectcodenew+'_%') ";
/*     */       } else {
/*     */         
/* 208 */         str4 = str4 + " and NOT exists(select 1 from " + paramString1 + " t where t.subjectcodenew like a.subjectcodenew||'_%') ";
/*     */       } 
/*     */     }
/*     */     
/* 212 */     if (bool1) {
/*     */       
/* 214 */       str4 = str4 + " AND b.workflowId=" + paramInt1 + " AND b.fieldId=" + paramInt2;
/* 215 */       if (!bool3)
/*     */       {
/* 217 */         str4 = str4 + " and a.id not in (  select ta.subjectId from FnaMultiAccountSubjectDetail ta join FnaMultiAccountSubject tb on tb.id=ta.mainId  where tb.workflowId=" + paramInt1 + " and tb.fieldId=" + paramInt2 + ") ";
/*     */       }
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/* 223 */     ArrayList<String> arrayList1 = new ArrayList();
/*     */     
/* 225 */     str1 = "SELECT a.id from " + str3 + str4;
/* 226 */     recordSet.executeQuery(str1, new Object[0]);
/* 227 */     while (recordSet.next()) {
/* 228 */       String str = Util.null2String(recordSet.getString("id"));
/*     */       
/* 230 */       arrayList1.add(str);
/*     */     } 
/* 232 */     baseBean.writeLog("SubjectFilter===最终执行的SQL：" + str1);
/* 233 */     baseBean.writeLog("SubjectFilter===subjectsConditionList：" + arrayList1.toString());
/* 234 */     return arrayList1;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/SubjectFilter.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */