/*    */ package weaver.workflow.request;
/*    */ 
/*    */ public class FileUploadDTO {
/*    */   private String mainId;
/*    */   private String subId;
/*    */   private String secId;
/*    */   private String picfiletypes;
/*    */   private String filetypedesc;
/*    */   private int uploadType;
/*    */   private boolean canuse;
/*    */   private String selectedCateLog;
/*    */   private int maxUploadFileSize;
/*    */   
/*    */   public String getMainId() {
/* 15 */     return this.mainId;
/*    */   }
/*    */   
/*    */   public void setMainId(String paramString) {
/* 19 */     this.mainId = paramString;
/*    */   }
/*    */   
/*    */   public String getSubId() {
/* 23 */     return this.subId;
/*    */   }
/*    */   
/*    */   public void setSubId(String paramString) {
/* 27 */     this.subId = paramString;
/*    */   }
/*    */   
/*    */   public String getSecId() {
/* 31 */     return this.secId;
/*    */   }
/*    */   
/*    */   public void setSecId(String paramString) {
/* 35 */     this.secId = paramString;
/*    */   }
/*    */   
/*    */   public String getPicfiletypes() {
/* 39 */     return this.picfiletypes;
/*    */   }
/*    */   
/*    */   public void setPicfiletypes(String paramString) {
/* 43 */     this.picfiletypes = paramString;
/*    */   }
/*    */   
/*    */   public String getFiletypedesc() {
/* 47 */     return this.filetypedesc;
/*    */   }
/*    */   
/*    */   public void setFiletypedesc(String paramString) {
/* 51 */     this.filetypedesc = paramString;
/*    */   }
/*    */   
/*    */   public int getUploadType() {
/* 55 */     return this.uploadType;
/*    */   }
/*    */   
/*    */   public void setUploadType(int paramInt) {
/* 59 */     this.uploadType = paramInt;
/*    */   }
/*    */   
/*    */   public boolean isCanuse() {
/* 63 */     return this.canuse;
/*    */   }
/*    */   
/*    */   public void setCanuse(boolean paramBoolean) {
/* 67 */     this.canuse = paramBoolean;
/*    */   }
/*    */   
/*    */   public String getSelectedCateLog() {
/* 71 */     return this.selectedCateLog;
/*    */   }
/*    */   
/*    */   public void setSelectedCateLog(String paramString) {
/* 75 */     this.selectedCateLog = paramString;
/*    */   }
/*    */   
/*    */   public int getMaxUploadFileSize() {
/* 79 */     return this.maxUploadFileSize;
/*    */   }
/*    */   
/*    */   public void setMaxUploadFileSize(int paramInt) {
/* 83 */     this.maxUploadFileSize = paramInt;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/FileUploadDTO.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */