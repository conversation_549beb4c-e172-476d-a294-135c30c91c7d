/*     */ package weaver.workflow.request;
/*     */ 
/*     */ import java.io.IOException;
/*     */ import javax.servlet.ServletException;
/*     */ import javax.servlet.ServletOutputStream;
/*     */ import javax.servlet.http.HttpServlet;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class RequestOutOperation
/*     */   extends HttpServlet
/*     */ {
/*     */   public void doGet(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws ServletException, IOException {
/*  30 */     doPost(paramHttpServletRequest, paramHttpServletResponse);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void doPost(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws ServletException, IOException {
/*  39 */     paramHttpServletResponse.setContentType("text/html;charset=UTF-8");
/*  40 */     RequestManager requestManager = new RequestManager();
/*     */     
/*  42 */     ServletOutputStream servletOutputStream = paramHttpServletResponse.getOutputStream();
/*     */     
/*  44 */     String str1 = Util.null2String(paramHttpServletRequest.getParameter("src"));
/*  45 */     String str2 = Util.null2String(paramHttpServletRequest.getParameter("iscreate"));
/*  46 */     int i = Util.getIntValue(paramHttpServletRequest.getParameter("requestid"), -1);
/*  47 */     int j = Util.getIntValue(paramHttpServletRequest.getParameter("workflowid"), -1);
/*  48 */     String str3 = Util.null2String(paramHttpServletRequest.getParameter("workflowtype"));
/*  49 */     int k = Util.getIntValue(paramHttpServletRequest.getParameter("isremark"), -1);
/*  50 */     int m = Util.getIntValue(paramHttpServletRequest.getParameter("formid"), -1);
/*  51 */     int n = Util.getIntValue(paramHttpServletRequest.getParameter("isbill"), -1);
/*  52 */     int i1 = Util.getIntValue(paramHttpServletRequest.getParameter("billid"), -1);
/*  53 */     int i2 = Util.getIntValue(paramHttpServletRequest.getParameter("nodeid"), -1);
/*  54 */     String str4 = Util.null2String(paramHttpServletRequest.getParameter("nodetype"));
/*  55 */     String str5 = Util.null2String(paramHttpServletRequest.getParameter("requestname"));
/*  56 */     String str6 = Util.null2String(paramHttpServletRequest.getParameter("requestlevel"));
/*  57 */     String str7 = Util.null2String(paramHttpServletRequest.getParameter("remark"));
/*     */ 
/*     */     
/*  60 */     int i3 = Util.getIntValue(paramHttpServletRequest.getParameter("userid"));
/*  61 */     String str8 = Util.null2String(paramHttpServletRequest.getParameter("logintype"));
/*  62 */     User user = new User();
/*  63 */     user.setUid(i3);
/*  64 */     user.setLogintype(str8);
/*     */ 
/*     */     
/*  67 */     if (str1.equals("") || j == -1 || m == -1 || n == -1 || i2 == -1 || str4.equals("")) {
/*  68 */       servletOutputStream.print("issuccess=0&errormsg=参数错误");
/*     */       
/*     */       return;
/*     */     } 
/*  72 */     requestManager.setSrc(str1);
/*  73 */     requestManager.setIscreate(str2);
/*  74 */     requestManager.setRequestid(i);
/*  75 */     requestManager.setWorkflowid(j);
/*  76 */     requestManager.setWorkflowtype(str3);
/*  77 */     requestManager.setIsremark(k);
/*  78 */     requestManager.setFormid(m);
/*  79 */     requestManager.setIsbill(n);
/*  80 */     requestManager.setBillid(i1);
/*  81 */     requestManager.setNodeid(i2);
/*  82 */     requestManager.setNodetype(str4);
/*  83 */     requestManager.setRequestname(str5);
/*  84 */     requestManager.setRequestlevel(str6);
/*  85 */     requestManager.setRemark(str7);
/*  86 */     requestManager.setRequest(paramHttpServletRequest);
/*  87 */     requestManager.setUser(user);
/*     */ 
/*     */     
/*  90 */     boolean bool1 = requestManager.saveRequestInfo();
/*  91 */     i = requestManager.getRequestid();
/*  92 */     if (!bool1) {
/*  93 */       servletOutputStream.print("issuccess=0&errormsg=工作流保存错误");
/*     */       
/*     */       return;
/*     */     } 
/*  97 */     boolean bool2 = requestManager.flowNextNode();
/*  98 */     if (!bool2) {
/*  99 */       servletOutputStream.print("issuccess=0&errormsg=工作流流转错误");
/*     */       
/*     */       return;
/*     */     } 
/* 103 */     boolean bool3 = requestManager.saveRequestLog();
/*     */     
/* 105 */     if (!bool3) {
/* 106 */       servletOutputStream.print("issuccess=0&errormsg=工作流日志保存错误");
/*     */       
/*     */       return;
/*     */     } 
/* 110 */     servletOutputStream.print("issuccess=1&errormsg=&requestid=" + i + "&workflowid=" + j + "&workflowtype=" + str3);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/RequestOutOperation.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */