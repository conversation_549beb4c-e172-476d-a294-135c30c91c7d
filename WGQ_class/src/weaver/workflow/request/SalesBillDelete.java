/*    */ package weaver.workflow.request;
/*    */ 
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.Util;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class SalesBillDelete
/*    */ {
/*    */   private String tableName;
/*    */   private int requestId;
/*    */   boolean isSuccess;
/*    */   
/*    */   public SalesBillDelete(String paramString, int paramInt) {
/* 30 */     this.tableName = paramString;
/* 31 */     this.requestId = paramInt;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public SalesBillDelete(int paramInt) {
/* 41 */     this.requestId = paramInt;
/*    */ 
/*    */     
/* 44 */     initTableName();
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   private void initTableName() {
/* 51 */     boolean bool = false;
/* 52 */     int i = 0;
/* 53 */     RecordSet recordSet = new RecordSet();
/* 54 */     recordSet.executeProc("workflow_form_SByRequestid", String.valueOf(this.requestId));
/* 55 */     if (recordSet.next()) {
/* 56 */       i = Util.getIntValue(recordSet.getString("billformid"), 0);
/*    */     }
/* 58 */     recordSet.executeProc("bill_includepages_SelectByID", i + "");
/* 59 */     if (recordSet.next()) {
/* 60 */       this.tableName = Util.null2String(recordSet.getString("tablename"));
/*    */     }
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public boolean doDelete() {
/* 68 */     if (this.tableName == null || this.tableName.equals(""))
/* 69 */       return false; 
/* 70 */     RecordSet recordSet = new RecordSet();
/* 71 */     recordSet.executeSql("UPDATE " + this.tableName + " SET status = -1 WHERE requestid =" + String.valueOf(this.requestId));
/*    */     
/* 73 */     return true;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/SalesBillDelete.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */