/*    */ package weaver.workflow.request;
/*    */ 
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.Util;
/*    */ import weaver.system.SysWFLMonitor;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class WorkflowPrivilegeManager
/*    */ {
/*    */   public static void main(String[] paramArrayOfString) {}
/*    */   
/*    */   public boolean getViewWorkflowRight(int paramInt1, int paramInt2, int paramInt3, String paramString) {
/* 18 */     SysWFLMonitor sysWFLMonitor = new SysWFLMonitor();
/* 19 */     WFUrgerManager wFUrgerManager = new WFUrgerManager();
/* 20 */     boolean bool = false;
/* 21 */     RecordSet recordSet = new RecordSet();
/* 22 */     recordSet.executeSql("select requestid from workflow_currentoperator where userid=" + paramInt1 + "  and requestid=" + paramInt2);
/* 23 */     if (recordSet.next()) {
/* 24 */       bool = true;
/*    */     }
/*    */     
/* 27 */     if (!bool) {
/* 28 */       int i = sysWFLMonitor.getWFInterventorRightBymonitor(paramInt1, paramInt2);
/*    */       
/* 30 */       if (i > 0) {
/* 31 */         bool = true;
/*    */       }
/*    */     } 
/*    */     
/* 35 */     if (!bool) {
/* 36 */       bool = wFUrgerManager.UrgerHaveWorkflowViewRight(paramInt2, paramInt1, Util.getIntValue("" + paramString, 1));
/*    */     }
/*    */     
/* 39 */     if (!bool) {
/* 40 */       bool = wFUrgerManager.getMonitorViewRight(paramInt2, paramInt1);
/*    */     }
/* 42 */     return bool;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/WorkflowPrivilegeManager.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */