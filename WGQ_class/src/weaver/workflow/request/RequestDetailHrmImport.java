/*     */ package weaver.workflow.request;
/*     */ 
/*     */ import com.engine.hrm.biz.HrmOrganizationPathBiz;
/*     */ import com.engine.hrm.biz.OrganizationShowSetBiz;
/*     */ import com.google.common.base.Strings;
/*     */ import java.util.LinkedHashSet;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.StringUtil;
/*     */ import weaver.general.Util;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class RequestDetailHrmImport
/*     */   extends BaseBean
/*     */ {
/*  18 */   final String HRM_CODE = "workcode_";
/*  19 */   final String DEPT_CODE = "deptcode_";
/*     */   
/*     */   private Integer language;
/*     */   
/*     */   public RequestDetailHrmImport(int paramInt) {
/*  24 */     this.language = Integer.valueOf(paramInt);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String querySqlByNameAndId(String paramString1, String paramString2, String paramString3, String paramString4, String paramString5) {
/*  37 */     StringBuffer stringBuffer1 = (new StringBuffer("SELECT ")).append(paramString2).append(" FROM ").append(paramString1).append(" WHERE 1=1 ");
/*  38 */     StringBuffer stringBuffer2 = new StringBuffer();
/*     */ 
/*     */     
/*  41 */     boolean bool = paramString4.startsWith("##");
/*  42 */     if (bool) {
/*  43 */       stringBuffer2.append(" AND (").append(paramString2).append(" = ").append(paramString4.replace("##", "")).append(") ");
/*     */     } else {
/*  45 */       String[] arrayOfString = paramString4.split("##");
/*  46 */       if (!Strings.isNullOrEmpty(arrayOfString[0])) {
/*  47 */         stringBuffer2.append("AND (").append(paramString3).append(" = '").append(arrayOfString[0]).append("' ");
/*  48 */         String str = this.language + ((this.language.intValue() > 9) ? "" : " ");
/*  49 */         if (Util.isEnableMultiLang()) {
/*  50 */           stringBuffer2.append(" or ").append(paramString3).append(" like '%`~`").append(str).append(arrayOfString[0]).append("`~`%'");
/*     */         }
/*  52 */         stringBuffer2.append(") ");
/*     */       } 
/*  54 */       if (arrayOfString.length > 1 && !Strings.isNullOrEmpty(arrayOfString[1])) {
/*  55 */         stringBuffer2.append(" AND (").append(paramString2).append(" = ").append(arrayOfString[1]).append(") ");
/*     */       }
/*     */     } 
/*     */     
/*  59 */     return Strings.isNullOrEmpty(stringBuffer2.toString()) ? "" : stringBuffer1.append(stringBuffer2).append(paramString5).toString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String querySqlByNameAndId(String paramString1, String paramString2, String paramString3, String paramString4, String paramString5, String paramString6) {
/*  74 */     if (Strings.isNullOrEmpty(paramString5)) {
/*  75 */       (new BaseBean()).writeLog("明细导入部门或分部excel值为空：" + paramString5);
/*  76 */       return "";
/*     */     } 
/*  78 */     RecordSet recordSet = new RecordSet();
/*  79 */     String str1 = "";
/*     */ 
/*     */     
/*  82 */     boolean bool = paramString5.startsWith("##");
/*  83 */     if (bool) {
/*  84 */       String str = paramString5.replace("##", "");
/*  85 */       str1 = String.format("SELECT %s FROM %s WHERE ( %s = %s ) %s ", new Object[] { paramString2, paramString1, paramString2, str, paramString6 });
/*  86 */       recordSet.executeQuery(str1, new Object[0]);
/*  87 */       recordSet.next();
/*  88 */       return recordSet.getString(1);
/*     */     } 
/*     */     
/*  91 */     String str2 = this.language + ((this.language.intValue() > 9) ? "" : " ");
/*  92 */     String[] arrayOfString = paramString5.split("##");
/*  93 */     String str3 = (arrayOfString.length >= 1) ? arrayOfString[0] : "";
/*  94 */     String str4 = (arrayOfString.length >= 2) ? arrayOfString[1] : "";
/*  95 */     if (!Strings.isNullOrEmpty(str3)) {
/*  96 */       str1 = str1 + "SELECT id FROM " + paramString1 + " WHERE ( " + paramString3 + " = '" + str3 + "' OR " + paramString3 + " LIKE '%`~`" + str2 + str3 + "`~`%' ) ";
/*     */     }
/*  98 */     if (!Strings.isNullOrEmpty(str4)) {
/*  99 */       str1 = str1 + " AND id = " + str4;
/*     */     }
/* 101 */     if (!Strings.isNullOrEmpty(paramString6)) {
/* 102 */       str1 = str1 + paramString6;
/*     */     }
/* 104 */     str1 = str1 + " order by id asc";
/* 105 */     recordSet.executeQuery(str1, new Object[0]);
/* 106 */     if (recordSet.next()) {
/* 107 */       return recordSet.getString(1);
/*     */     }
/* 109 */     str1 = "";
/* 110 */     if (!Strings.isNullOrEmpty(str3)) {
/* 111 */       str1 = str1 + "SELECT id FROM " + paramString1 + " WHERE ( " + paramString4 + " = '" + str3 + "' OR " + paramString4 + " LIKE '%`~`" + str2 + str3 + "`~`%'  ) ";
/*     */     }
/* 113 */     if (!Strings.isNullOrEmpty(str4)) {
/* 114 */       str1 = str1 + " AND id = " + str4;
/*     */     }
/* 116 */     if (!Strings.isNullOrEmpty(paramString6)) {
/* 117 */       str1 = str1 + paramString6;
/*     */     }
/* 119 */     str1 = str1 + " order by id asc";
/* 120 */     recordSet.executeQuery(str1, new Object[0]);
/* 121 */     recordSet.next();
/* 122 */     return recordSet.getString(1);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getOrgFieldValue(int paramInt, String paramString) {
/* 133 */     if (Strings.isNullOrEmpty(paramString)) return paramString; 
/* 134 */     RecordSet recordSet = new RecordSet();
/* 135 */     LinkedHashSet<String> linkedHashSet = new LinkedHashSet();
/* 136 */     HrmOrganizationPathBiz hrmOrganizationPathBiz = new HrmOrganizationPathBiz();
/* 137 */     OrganizationShowSetBiz organizationShowSetBiz = new OrganizationShowSetBiz();
/* 138 */     String str = organizationShowSetBiz.getSepCharacter();
/* 139 */     str = "＞".equals(str) ? ">" : str;
/*     */ 
/*     */     
/* 142 */     String[] arrayOfString = paramString.split(",");
/* 143 */     for (String str1 : arrayOfString) {
/* 144 */       if (!Strings.isNullOrEmpty(str1)) {
/* 145 */         boolean bool = (str1.indexOf(str) > 0) ? true : false;
/* 146 */         String str2 = "";
/* 147 */         if (isHrmFieldType(paramInt)) {
/* 148 */           if (str1.startsWith("workcode_")) {
/* 149 */             String str3 = str1.replace("workcode_", "");
/* 150 */             if (StringUtil.isEmpty(str3)) {
/* 151 */               return "";
/*     */             }
/* 153 */             str2 = hrmOrganizationPathBiz.getResourceIdByWorkCode(str1.replace("workcode_", ""));
/* 154 */           } else if (!str1.startsWith("workcode_") && str1.indexOf("workcode_") != -1) {
/* 155 */             int i = str1.indexOf("workcode_");
/* 156 */             str1 = str1.substring(i);
/* 157 */             String str3 = str1.replace("workcode_", "");
/* 158 */             if (StringUtil.isEmpty(str3)) {
/* 159 */               return "";
/*     */             }
/* 161 */             str2 = hrmOrganizationPathBiz.getResourceIdByWorkCode(str1.replace("workcode_", ""));
/* 162 */           } else if (bool) {
/* 163 */             writeLog("[LOG] getResourceIdByPath value:" + str1 + ", sepCharacter:" + str);
/* 164 */             str2 = hrmOrganizationPathBiz.getResourceIdByPath(str1, str, String.valueOf(this.language));
/*     */           } else {
/* 166 */             String str3 = " AND status in (0,1,2,3) ";
/* 167 */             String str4 = querySqlByNameAndId("HrmResource", "id", "lastname", str1, str3);
/* 168 */             recordSet.executeQuery(str4, new Object[0]);
/* 169 */             if (recordSet.next()) {
/* 170 */               str2 = Util.null2String(recordSet.getString(1));
/*     */             } else {
/* 172 */               writeLog("[LOG] getOrgFieldValue sql not result,sql:" + str4);
/* 173 */               str4 = querySqlByNameAndId("HrmResourceManager", "id", "lastname", str1, str3);
/* 174 */               recordSet.executeQuery(str4, new Object[0]);
/* 175 */               if (recordSet.next()) {
/* 176 */                 str2 = Util.null2String(recordSet.getString(1));
/*     */               } else {
/* 178 */                 writeLog("[LOG] getOrgFieldValue sql not result,sql:" + str4);
/*     */               } 
/*     */             } 
/*     */           } 
/* 182 */         } else if (isDepartmentFieldType(paramInt)) {
/* 183 */           if (str1.startsWith("deptcode_")) {
/* 184 */             String str3 = str1.replace("deptcode_", "");
/* 185 */             if (StringUtil.isEmpty(str3)) {
/* 186 */               return "";
/*     */             }
/* 188 */             str2 = hrmOrganizationPathBiz.getDeptIdByCode(str1.replace("deptcode_", ""));
/* 189 */           } else if (!str1.startsWith("deptcode_") && str1.indexOf("deptcode_") != -1) {
/* 190 */             int i = str1.indexOf("deptcode_");
/* 191 */             str1 = str1.substring(i);
/* 192 */             String str3 = str1.replace("deptcode_", "");
/* 193 */             if (StringUtil.isEmpty(str3)) {
/* 194 */               return "";
/*     */             }
/* 196 */             str2 = hrmOrganizationPathBiz.getDeptIdByCode(str1.replace("deptcode_", ""));
/* 197 */           } else if (bool) {
/* 198 */             writeLog("[LOG] getDeptIdByFlowPath value:" + str1 + ", sepCharacter:" + str);
/* 199 */             str2 = hrmOrganizationPathBiz.getDeptIdByFlowPath(str1, str, String.valueOf(this.language));
/*     */           } else {
/* 201 */             String str3 = " AND (canceled = 0 or canceled is null) ";
/* 202 */             str2 = querySqlByNameAndId("HrmDepartmentAllView", "id", "departmentname", "departmentmark", str1, str3);
/*     */           } 
/* 204 */         } else if (isSubCompanyFieldType(paramInt)) {
/* 205 */           if (bool) {
/* 206 */             writeLog("[LOG] getSubcomIdByFlowPath value:" + str1 + ", sepCharacter:" + str);
/* 207 */             str2 = hrmOrganizationPathBiz.getSubcomIdByFlowPath(str1, str, String.valueOf(this.language));
/*     */           } else {
/* 209 */             String str3 = " AND (canceled = 0 or canceled is null) ";
/* 210 */             str2 = querySqlByNameAndId("HrmSubCompanyAllView", "id", "subcompanydesc", "subcompanyname", str1, str3);
/*     */           } 
/*     */         } 
/*     */         
/* 214 */         linkedHashSet.add(str2);
/*     */       } 
/*     */     } 
/* 217 */     return String.join(",", (Iterable)linkedHashSet);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean isOrgField(int paramInt) {
/* 226 */     return (isHrmFieldType(paramInt) || isDepartmentFieldType(paramInt) || isSubCompanyFieldType(paramInt));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean isHrmFieldType(int paramInt) {
/* 235 */     return (paramInt == 1 || paramInt == 17 || paramInt == 160 || paramInt == 165 || paramInt == 166);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean isDepartmentFieldType(int paramInt) {
/* 244 */     return (paramInt == 4 || paramInt == 57 || paramInt == 167 || paramInt == 168);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean isSubCompanyFieldType(int paramInt) {
/* 253 */     return (paramInt == 164 || paramInt == 194 || paramInt == 169 || paramInt == 170);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/RequestDetailHrmImport.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */