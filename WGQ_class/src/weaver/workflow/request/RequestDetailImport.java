/*      */ package weaver.workflow.request;
/*      */ 
/*      */ import com.alibaba.fastjson.JSONObject;
/*      */ import com.engine.common.service.FnaCommonService;
/*      */ import com.engine.common.service.impl.FnaCommonServiceImpl;
/*      */ import com.engine.fnaMulDimensions.util.FnaBrowserUtils;
/*      */ import com.engine.fnaMulDimensions.util.FnaReimbursementUtils;
/*      */ import com.engine.fnaMulDimensions.util.constants.FnaAccTypeConstant;
/*      */ import com.engine.workflow.biz.detailFilter.DetailFilterBiz;
/*      */ import com.engine.workflow.biz.freeNode.FreeNodeBiz;
/*      */ import com.engine.workflow.biz.requestFlow.DetailDataModifyTrackBiz;
/*      */ import com.engine.workflow.biz.requestSubmit.RequestOperationBiz;
/*      */ import com.google.common.base.Strings;
/*      */ import com.greenpineyu.fel.FelEngineImpl;
/*      */ import com.greenpineyu.fel.context.FelContext;
/*      */ import java.io.InputStream;
/*      */ import java.math.BigDecimal;
/*      */ import java.text.DateFormat;
/*      */ import java.text.DecimalFormat;
/*      */ import java.text.SimpleDateFormat;
/*      */ import java.util.ArrayList;
/*      */ import java.util.Calendar;
/*      */ import java.util.Date;
/*      */ import java.util.HashMap;
/*      */ import java.util.HashSet;
/*      */ import java.util.Hashtable;
/*      */ import java.util.Iterator;
/*      */ import java.util.List;
/*      */ import java.util.Map;
/*      */ import java.util.Set;
/*      */ import java.util.StringJoiner;
/*      */ import java.util.TreeMap;
/*      */ import java.util.concurrent.CountDownLatch;
/*      */ import java.util.regex.Matcher;
/*      */ import java.util.regex.Pattern;
/*      */ import org.apache.commons.lang.StringEscapeUtils;
/*      */ import org.apache.log4j.Logger;
/*      */ import org.apache.poi.ss.usermodel.Sheet;
/*      */ import org.apache.poi.ss.usermodel.Workbook;
/*      */ import org.apache.poi.ss.usermodel.WorkbookFactory;
/*      */ import weaver.conn.RecordSet;
/*      */ import weaver.dateformat.DateTransformer;
/*      */ import weaver.file.FileUpload;
/*      */ import weaver.file.ImageFileManager;
/*      */ import weaver.fna.general.FnaCommon;
/*      */ import weaver.fna.general.RequestDetailImportUtil;
/*      */ import weaver.fna.maintenance.BudgetfeeTypeComInfo;
/*      */ import weaver.fna.maintenance.FnaSystemSetComInfo;
/*      */ import weaver.formmode.excel.POIUtil;
/*      */ import weaver.formmode.view.ModeDetailImport;
/*      */ import weaver.general.BaseBean;
/*      */ import weaver.general.TimeUtil;
/*      */ import weaver.general.Util;
/*      */ import weaver.hrm.User;
/*      */ import weaver.interfaces.workflow.browser.BrowserForDetailImportService;
/*      */ import weaver.systeminfo.SystemEnv;
/*      */ import weaver.workflow.logging.debug.Logger4WorkflowDebug;
/*      */ import weaver.workflow.mode.FieldInfo;
/*      */ import weaver.workflow.workflow.WFNodeDtlFieldManager;
/*      */ import weaver.workflow.workflow.WorkflowBillComInfo;
/*      */ import weaver.workflow.workflow.WorkflowConfigComInfo;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ public class RequestDetailImport
/*      */   extends BaseBean
/*      */ {
/*   82 */   private int currentUserLang = 7;
/*   83 */   private Logger wfLog = Logger4WorkflowDebug.getLogger();
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean getAllowesImport(int paramInt1, int paramInt2, int paramInt3, int paramInt4, User paramUser) throws Exception {
/*   96 */     boolean bool = false;
/*   97 */     if (paramInt4 == 0 || paramInt4 == 1) {
/*   98 */       RecordSet recordSet = new RecordSet();
/*      */       
/*  100 */       recordSet.executeSql("SELECT nodetype FROM workflow_flownode WHERE nodetype<>3 AND nodeid=" + paramInt3);
/*  101 */       if (recordSet.next()) {
/*      */         
/*  103 */         String str = Util.null2String(recordSet.getString("nodetype"));
/*  104 */         if ("0".equals(str)) {
/*      */ 
/*      */           
/*  107 */           recordSet.executeSql("SELECT formid,isbill FROM workflow_base WHERE isImportDetail in ('1', '2') AND id=" + paramInt2);
/*      */         }
/*      */         else {
/*      */           
/*  111 */           recordSet.executeSql("SELECT formid,isbill FROM workflow_base WHERE isImportDetail='2' AND id=" + paramInt2);
/*      */         } 
/*  113 */         if (recordSet.next()) {
/*  114 */           int i = recordSet.getInt("formid");
/*  115 */           int j = recordSet.getInt("isbill");
/*  116 */           if (j == 1 && i > 0)
/*      */           {
/*  118 */             return false;
/*      */           }
/*  120 */           String str1 = "";
/*  121 */           int k = 0;
/*  122 */           int m = 0;
/*      */ 
/*      */ 
/*      */ 
/*      */           
/*  127 */           recordSet.executeSql("SELECT ismode,showdes,printdes,toexcel FROM workflow_flownode WHERE workflowid=" + paramInt2 + " AND nodeid=" + paramInt3);
/*  128 */           if (recordSet.next()) {
/*  129 */             str1 = Util.null2String(recordSet.getString("ismode"));
/*  130 */             k = Util.getIntValue(Util.null2String(recordSet.getString("showdes")), 0);
/*      */           } 
/*      */           
/*  133 */           if ("1".equals(str1) && k != 1) {
/*      */             
/*  135 */             recordSet.executeSql("SELECT id FROM workflow_nodemode WHERE isprint='0' AND workflowid=" + paramInt2 + " AND nodeid=" + paramInt3);
/*  136 */             if (recordSet.next()) {
/*  137 */               m = recordSet.getInt("id");
/*      */             } else {
/*  139 */               recordSet.executeSql("SELECT id FROM workflow_formmode WHERE isprint='0' AND formid=" + i + " AND isbill='" + j + "'");
/*  140 */               if (recordSet.next()) {
/*  141 */                 m = recordSet.getInt("id");
/*      */               }
/*      */             } 
/*      */             
/*  145 */             bool = true;
/*      */           } 
/*      */           
/*  148 */           recordSet.executeSql("SELECT DISTINCT groupId FROM workflow_nodeformgroup WHERE nodeid=" + paramInt3 + " ORDER BY groupid");
/*      */           
/*  150 */           WFNodeDtlFieldManager wFNodeDtlFieldManager = new WFNodeDtlFieldManager();
/*  151 */           while (recordSet.next()) {
/*  152 */             wFNodeDtlFieldManager.resetParameter();
/*  153 */             wFNodeDtlFieldManager.setNodeid(paramInt3);
/*  154 */             wFNodeDtlFieldManager.setGroupid(recordSet.getInt(1));
/*  155 */             wFNodeDtlFieldManager.selectWfNodeDtlField();
/*      */             
/*  157 */             if ("1".equals(wFNodeDtlFieldManager.getIsadd()) || "1".equals(wFNodeDtlFieldManager.getIsedit()) || "1".equals(wFNodeDtlFieldManager.getIsdelete())) {
/*      */               
/*  159 */               if ("1".equals(str1) && m > 0) {
/*  160 */                 recordSet.executeSql("SELECT fieldid FROM workflow_modeview WHERE isedit='1' AND formid=" + i + " and isbill=" + j + " and nodeid=" + paramInt3);
/*      */               } else {
/*  162 */                 recordSet.executeSql("SELECT fieldid FROM workflow_nodeform WHERE isedit='1' AND nodeid=" + paramInt3);
/*      */               } 
/*  164 */               ArrayList<String> arrayList = new ArrayList();
/*  165 */               while (recordSet.next()) {
/*  166 */                 arrayList.add("field" + recordSet.getString("fieldid"));
/*      */               }
/*  168 */               FieldInfo fieldInfo = new FieldInfo();
/*  169 */               fieldInfo.setRequestid(paramInt1);
/*  170 */               fieldInfo.setUser(paramUser);
/*  171 */               fieldInfo.GetDetailTableField(i, j, 7);
/*  172 */               ArrayList arrayList1 = fieldInfo.getDetailTableFieldNames();
/*  173 */               ArrayList<ArrayList> arrayList2 = fieldInfo.getDetailTableFields();
/*      */               
/*  175 */               for (byte b = 0; b < arrayList1.size(); b++) {
/*  176 */                 ArrayList<String> arrayList3 = arrayList2.get(b);
/*  177 */                 for (byte b1 = 0; b1 < arrayList3.size(); b1++) {
/*  178 */                   if (arrayList.indexOf(Util.TokenizerString(arrayList3.get(b1), "_").get(0)) >= 0) {
/*  179 */                     bool = true;
/*      */                     break;
/*      */                   } 
/*      */                 } 
/*  183 */                 if (bool) {
/*      */                   break;
/*      */                 }
/*      */               } 
/*      */             } 
/*  188 */             if (bool) {
/*      */               break;
/*      */             }
/*      */           } 
/*      */         } 
/*      */       } 
/*      */     } 
/*  195 */     return bool;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean getImportRight(int paramInt1, int paramInt2, int paramInt3, int paramInt4) {
/*      */     RecordSet recordSet1, recordSet2;
/*  208 */     boolean bool = false;
/*  209 */     switch (paramInt4) {
/*      */       
/*      */       case 1:
/*  212 */         recordSet1 = new RecordSet();
/*  213 */         recordSet1.executeSql("SELECT requestid FROM workflow_currentoperator WHERE isremark='0' AND requestid=" + paramInt1 + " AND userid=" + paramInt3 + " AND nodeid=" + paramInt2 + " AND EXISTS(SELECT nodeid FROM workflow_flownode WHERE nodetype=0 AND nodeid=" + paramInt2 + ")");
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */         
/*  221 */         if (recordSet1.next()) {
/*  222 */           bool = true;
/*      */         }
/*      */         break;
/*      */       
/*      */       case 2:
/*  227 */         recordSet2 = new RecordSet();
/*  228 */         recordSet2.executeSql("SELECT requestid FROM workflow_currentoperator WHERE isremark='0' AND requestid=" + paramInt1 + " AND userid=" + paramInt3 + " AND nodeid=" + paramInt2 + " AND EXISTS(SELECT nodeid FROM workflow_flownode WHERE nodetype<>3 AND nodeid=" + paramInt2 + ")");
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */         
/*  236 */         if (recordSet2.next()) {
/*  237 */           bool = true;
/*      */         }
/*      */         break;
/*      */     } 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  246 */     return bool;
/*      */   }
/*      */   
/*      */   private String getFdName(String paramString, int paramInt1, int paramInt2) {
/*      */     String str;
/*  251 */     RecordSet recordSet = new RecordSet();
/*      */     
/*  253 */     if (paramInt1 > 0) {
/*  254 */       str = "select fieldname from workflow_billfield where id = " + paramString;
/*      */     } else {
/*  256 */       str = "SELECT  a.fieldname FROM workflow_formfield wf , ( SELECT DISTINCT id , fielddbtype , fieldname ,description FROM workflow_formdictdetail ) a WHERE wf.formid = " + paramInt2 + " AND wf.isdetail = '1' AND wf.fieldid = a.id AND a.id =  " + paramString;
/*      */     } 
/*      */ 
/*      */ 
/*      */     
/*  261 */     recordSet.executeSql(str);
/*  262 */     if (recordSet.next()) {
/*  263 */       return recordSet.getString("fieldname");
/*      */     }
/*      */     
/*  266 */     return null;
/*      */   }
/*      */   
/*      */   private String getDtName(String paramString, int paramInt1, int paramInt2) {
/*  270 */     RecordSet recordSet = new RecordSet();
/*      */     
/*  272 */     if (paramInt1 > 0) {
/*  273 */       String str = "select detailtable from workflow_billfield where id = " + paramString;
/*      */       
/*  275 */       recordSet.executeSql(str);
/*  276 */       if (recordSet.next()) {
/*  277 */         return recordSet.getString("detailtable");
/*      */       }
/*      */     } else {
/*  280 */       return "workflow_formdetail";
/*  281 */     }  return null;
/*      */   }
/*      */   
/*      */   private String getDtValue(String paramString1, String paramString2, String paramString3) {
/*  285 */     RecordSet recordSet = new RecordSet();
/*  286 */     String str = "select dtFieldId from mainKey where id = " + paramString3;
/*      */     
/*  288 */     recordSet.executeSql(str);
/*  289 */     if (recordSet.next()) {
/*  290 */       return recordSet.getString("detailtable");
/*      */     }
/*  292 */     return null;
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   private void colCal(String paramString, int paramInt1, int paramInt2, Map paramMap, int paramInt3, int paramInt4) {
/*  298 */     String str1 = "";
/*  299 */     HashSet<String> hashSet = new HashSet();
/*      */     
/*  301 */     RecordSet recordSet1 = new RecordSet();
/*      */     
/*  303 */     RecordSet recordSet2 = new RecordSet();
/*  304 */     RecordSet recordSet3 = new RecordSet();
/*  305 */     String str2 = "";
/*  306 */     String str3 = "";
/*  307 */     Pattern pattern = Pattern.compile("detailfield[_](\\d+)");
/*  308 */     Matcher matcher = null;
/*  309 */     matcher = pattern.matcher(paramString);
/*  310 */     while (matcher.find()) {
/*  311 */       if ("".equals(str2)) {
/*  312 */         str2 = matcher.group(1); continue;
/*      */       } 
/*  314 */       hashSet.add(matcher.group(1));
/*      */     } 
/*      */ 
/*      */     
/*  318 */     str3 = getFdName(str2, paramInt1, paramInt2);
/*      */     
/*  320 */     String str4 = getDtName(str2, paramInt1, paramInt2);
/*      */     
/*  322 */     ArrayList<String> arrayList = new ArrayList();
/*  323 */     arrayList.addAll(hashSet);
/*  324 */     Iterator<String> iterator = arrayList.iterator();
/*  325 */     String str5 = "";
/*  326 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  327 */     byte b = 0;
/*  328 */     while (iterator.hasNext()) {
/*  329 */       String str6 = iterator.next();
/*  330 */       String str7 = getFdName(str6 + "", paramInt1, paramInt2);
/*  331 */       str5 = str5 + str7 + ",";
/*  332 */       hashMap.put(str7, str6);
/*  333 */       b++;
/*      */     } 
/*  335 */     if (str5.length() > 0) {
/*  336 */       str5 = str5.substring(0, str5.length() - 1);
/*  337 */       String str = (String)paramMap.get(str4);
/*  338 */       if (paramInt1 > 0) {
/*  339 */         str1 = "select " + str5 + ",id from " + str4 + " where " + str + " = " + paramInt3;
/*      */       } else {
/*      */         
/*  342 */         str1 = "select " + str5 + ",id from " + str4 + " where requestid = " + paramInt4;
/*      */       } 
/*      */       
/*  345 */       recordSet3.executeSql(str1);
/*  346 */       while (recordSet3.next()) {
/*  347 */         FelEngineImpl felEngineImpl = new FelEngineImpl();
/*  348 */         FelContext felContext = felEngineImpl.getContext();
/*  349 */         Iterator<String> iterator1 = hashMap.keySet().iterator();
/*  350 */         while (iterator1.hasNext()) {
/*  351 */           String str7 = iterator1.next();
/*  352 */           int i = 1 + arrayList.indexOf(hashMap.get(str7));
/*  353 */           String str8 = recordSet3.getString(i);
/*      */           
/*  355 */           if (str8 == null || "".equals(str8)) {
/*  356 */             felContext.set("detailfield_" + hashMap.get(str7), new Double(0.0D));
/*      */ 
/*      */             
/*      */             continue;
/*      */           } 
/*      */           
/*  362 */           if (str8.indexOf(",") > -1) {
/*  363 */             str8 = str8.replaceAll(",", "");
/*      */           }
/*  365 */           felContext.set("detailfield_" + hashMap.get(str7), new Double(str8));
/*      */         } 
/*      */         
/*  368 */         String str6 = recordSet3.getString(arrayList.size() + 1);
/*  369 */         Object object = felEngineImpl.eval(paramString.substring(paramString
/*  370 */               .indexOf("=") + 1, paramString.length()));
/*  371 */         object = "NaN".equals(object.toString()) ? "0" : object;
/*      */         
/*  373 */         if (paramInt1 > 0) {
/*  374 */           str1 = "select * from workflow_billfield where fieldname = '" + str3 + "'  and billid = " + paramInt2;
/*      */           
/*  376 */           if (str4 != null && !"".equals(str4)) {
/*  377 */             str1 = str1 + " and detailtable = '" + str4 + "' ";
/*      */           }
/*  379 */           recordSet1.executeSql(str1);
/*  380 */           if (recordSet1.next()) {
/*  381 */             String str7 = Util.null2String(recordSet1.getString("fieldhtmltype"));
/*  382 */             String str8 = Util.null2String(recordSet1.getString("type"));
/*  383 */             String str9 = Util.null2String(recordSet1.getString("fielddbtype")).toUpperCase();
/*  384 */             if (!"".equals(object) && "1".equals(str7)) {
/*      */               
/*  386 */               if (object.toString().indexOf("E") > -1 || object.toString().indexOf("e") > -1) {
/*  387 */                 BigDecimal bigDecimal = new BigDecimal(object.toString());
/*  388 */                 object = bigDecimal.toPlainString();
/*      */               } 
/*  390 */               if (str9.indexOf("INT") >= 0) {
/*  391 */                 object = Long.valueOf(Math.round(Double.parseDouble(object.toString())));
/*  392 */               } else if (str9.indexOf("NUMBER") >= 0 || str9.indexOf("FLOAT") >= 0 || str9.indexOf("DECIMAL") >= 0) {
/*  393 */                 int i = str9.indexOf(",");
/*  394 */                 int j = 2;
/*  395 */                 if (i > -1)
/*  396 */                   j = Util.getIntValue(str9.substring(i + 1, str9.length() - 1).trim(), 2); 
/*  397 */                 if ("NaN".equalsIgnoreCase(Util.null2String(object))) {
/*  398 */                   object = "";
/*      */                 } else {
/*  400 */                   BigDecimal bigDecimal = new BigDecimal(object.toString());
/*  401 */                   bigDecimal = bigDecimal.setScale(j, 4);
/*  402 */                   object = bigDecimal.toPlainString();
/*      */                 } 
/*  404 */               } else if ("5".equals(str8) && object.toString().length() > 3) {
/*  405 */                 int i = Util.getIntValue(recordSet1.getString("qfws"), 2);
/*  406 */                 String str10 = "";
/*  407 */                 for (byte b1 = 0; b1 < i; b1++) {
/*  408 */                   str10 = str10 + "#";
/*      */                 }
/*  410 */                 DecimalFormat decimalFormat = new DecimalFormat("###,###." + str10);
/*  411 */                 object = decimalFormat.format(Double.parseDouble(object.toString()));
/*      */               } 
/*      */             } 
/*      */           } 
/*      */         } 
/*  416 */         str1 = "update " + str4 + " set " + str3 + " = '" + object + "' where id = " + str6;
/*      */         
/*  418 */         recordSet2.execute(str1);
/*      */       } 
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   public void getColCalStr(int paramInt1, int paramInt2, String paramString1, Map paramMap, int paramInt3, int paramInt4, String paramString2) throws Exception {
/*  426 */     RecordSet recordSet1 = new RecordSet();
/*  427 */     RecordSet recordSet2 = new RecordSet();
/*  428 */     String str1 = "";
/*  429 */     String str2 = "";
/*  430 */     String str3 = "";
/*  431 */     String str4 = "select * from workflow_formdetailinfo where formid =" + paramInt1;
/*      */     
/*  433 */     if (paramInt1 != 0)
/*  434 */       recordSet1.executeSql(str4); 
/*  435 */     if (recordSet1.next()) {
/*  436 */       str1 = recordSet1.getString("colcalstr");
/*  437 */       str2 = recordSet1.getString("maincalstr");
/*  438 */       str3 = recordSet1.getString("rowcalstr");
/*      */     } 
/*      */ 
/*      */     
/*  442 */     if (str3 != null && !"".equals(str3.trim())) {
/*  443 */       String[] arrayOfString = str3.split(";");
/*  444 */       for (byte b = 0; b < arrayOfString.length; b++) {
/*  445 */         colCal(arrayOfString[b], paramInt4, paramInt1, paramMap, paramInt3, paramInt2);
/*      */       }
/*      */     } 
/*      */ 
/*      */     
/*  450 */     if (str2 != null && !"".equals(str2.trim())) {
/*  451 */       String[] arrayOfString = str2.split(";");
/*  452 */       for (byte b = 0; b < arrayOfString.length; b++) {
/*  453 */         String str5 = "";
/*  454 */         String str6 = "";
/*  455 */         String str7 = "";
/*      */         
/*  457 */         int i = 0;
/*  458 */         int j = 0;
/*  459 */         int k = 2;
/*  460 */         String[] arrayOfString1 = arrayOfString[b].split("=");
/*  461 */         String str8 = arrayOfString1[0].replace("mainfield_", "");
/*  462 */         String str9 = arrayOfString1[1].replace("detailfield_", "");
/*  463 */         if (paramInt4 > 0) {
/*  464 */           str4 = "select fieldname,fieldhtmltype,type,qfws from workflow_billfield where id = " + str8;
/*      */         } else {
/*      */           
/*  467 */           str4 = "select fieldname,fieldhtmltype,type,qfws from workflow_formdict where id = " + str8;
/*      */         } 
/*      */ 
/*      */         
/*  471 */         recordSet2.executeSql(str4);
/*  472 */         if (recordSet2.next()) {
/*  473 */           str7 = recordSet2.getString("fieldname");
/*  474 */           i = recordSet2.getInt("fieldhtmltype");
/*  475 */           j = recordSet2.getInt("type");
/*  476 */           k = recordSet2.getInt("qfws");
/*      */         } 
/*      */ 
/*      */         
/*  480 */         str6 = getFdName(str9, paramInt4, paramInt1);
/*      */         
/*  482 */         str5 = getDtName(str9, paramInt4, paramInt1);
/*      */         
/*  484 */         String str10 = (String)paramMap.get(str5);
/*  485 */         if (str10 == null || "".equals(str10.trim())) {
/*  486 */           str10 = "requestid";
/*      */         }
/*  488 */         double d = 0.0D;
/*  489 */         if (recordSet1.getDBType().equals("oracle")) {
/*  490 */           str4 = "select sum(replace(" + str6 + ",',','')) dd from " + str5 + " where " + str10 + " = " + paramInt3;
/*      */         } else {
/*      */           
/*  493 */           str4 = "select sum(cast(replace(" + str6 + ",',','') as decimal(18,4))) dd from " + str5 + " where " + str10 + " = " + paramInt3;
/*      */         } 
/*      */         
/*  496 */         recordSet2.executeSql(str4);
/*  497 */         if (recordSet2.next()) {
/*  498 */           d = Util.getDoubleValue(recordSet2.getString("dd"), 0.0D);
/*      */         }
/*      */ 
/*      */         
/*  502 */         if (paramInt4 > 0 && paramInt1 < 0) {
/*  503 */           if (i == 1 && j == 5) {
/*  504 */             String str11 = "###,###";
/*  505 */             if (k > 0) {
/*  506 */               str11 = str11 + ".";
/*  507 */               for (byte b1 = 0; b1 < k; b1++) {
/*  508 */                 str11 = str11 + "#";
/*      */               }
/*      */             } 
/*  511 */             DecimalFormat decimalFormat = new DecimalFormat(str11);
/*  512 */             String str12 = decimalFormat.format(d);
/*  513 */             str4 = " update  " + paramString1 + " set " + str7 + " = '" + str12 + "' where id = " + paramInt3;
/*      */           } else {
/*      */             
/*  516 */             str4 = " update  " + paramString1 + " set " + str7 + " = " + d + " where id = " + paramInt3;
/*      */           }
/*      */         
/*      */         } else {
/*      */           
/*  521 */           str4 = " update  workflow_form set " + str7 + " = " + d + " where requestid = " + paramInt2;
/*      */         } 
/*      */         
/*  524 */         recordSet1.executeSql(str4);
/*      */       } 
/*      */     } 
/*      */   }
/*      */   
/*      */   public String ImportDetail(FileUpload paramFileUpload, User paramUser) throws Exception {
/*  530 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  531 */     String str1 = Util.null2String(paramFileUpload.getParameter("ismode"));
/*  532 */     int i = Util.getIntValue(paramFileUpload.getParameter("requestid"));
/*  533 */     int j = Util.getIntValue(paramFileUpload.getParameter("formid"));
/*  534 */     int k = Util.getIntValue(paramFileUpload.getParameter("isbill"));
/*  535 */     int m = Util.getIntValue(paramFileUpload.getParameter("nodeid"));
/*  536 */     int n = Util.getIntValue(paramFileUpload.getParameter("modeid"));
/*  537 */     String str2 = Util.null2String(paramFileUpload.getParameter("isFieldTrigger"));
/*  538 */     String str3 = paramFileUpload.uploadFiles("excelfile");
/*  539 */     String str4 = Util.null2String(paramFileUpload.getRemoteAddr());
/*      */     
/*  541 */     hashMap.put("ismode", str1);
/*  542 */     hashMap.put("requestid", Integer.valueOf(i));
/*  543 */     hashMap.put("formid", Integer.valueOf(j));
/*  544 */     hashMap.put("isbill", Integer.valueOf(k));
/*  545 */     hashMap.put("nodeid", Integer.valueOf(m));
/*  546 */     hashMap.put("modeid", Integer.valueOf(n));
/*  547 */     hashMap.put("isFieldtrigger", str2);
/*  548 */     hashMap.put("clientIp", str4);
/*  549 */     hashMap.put("fileid", str3);
/*      */     
/*  551 */     return ImportDetail((Map)hashMap, paramUser);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String ImportDetail(Map<String, Object> paramMap, final User user) throws Exception {
/*  562 */     long l1 = System.currentTimeMillis();
/*  563 */     final int requestid = Util.getIntValue(Util.null2String(paramMap.get("requestid")));
/*  564 */     final SimpleDateFormat dataFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss:SSS");
/*  565 */     writeLog("---1.开始执行明细,requestid=" + i + ",导入开始的时间---" + simpleDateFormat.format(new Date()));
/*  566 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*      */     
/*  568 */     final HashMap<Object, Object> filterSubjectConditionMap = new HashMap<>();
/*      */     
/*  570 */     final HashMap<Object, Object> filterBearerConditionMap = new HashMap<>();
/*      */     
/*  572 */     final HashMap<Object, Object> filterPeriodConditionMap = new HashMap<>();
/*      */ 
/*      */     
/*  575 */     RequestFieldTrigger requestFieldTrigger = new RequestFieldTrigger();
/*  576 */     FnaSystemSetComInfo fnaSystemSetComInfo = new FnaSystemSetComInfo();
/*  577 */     final int optionalSubject = Util.getIntValue(fnaSystemSetComInfo.get_optionalSubject(), 0);
/*  578 */     final int wfDtlImpRole4Subject = Util.getIntValue(fnaSystemSetComInfo.get_wfDtlImpRole4Subject(), 0);
/*  579 */     final int wfDtlImpRole4Fcc = Util.getIntValue(fnaSystemSetComInfo.get_wfDtlImpRole4Fcc(), 0);
/*      */     
/*  581 */     final FnaCommonServiceImpl fnaCommonService = new FnaCommonServiceImpl();
/*  582 */     final ModeDetailImport modeDetailImport = new ModeDetailImport();
/*      */     
/*  584 */     final HashMap<Object, Object> dbDataHm = new HashMap<>();
/*  585 */     final HashMap<Object, Object> dbDataRepeatKeyHm = new HashMap<>();
/*  586 */     final RequestDetailHrmImport hrmImport = new RequestDetailHrmImport(this.currentUserLang);
/*  587 */     final BrowserForDetailImportService cusBrowserService = new BrowserForDetailImportService();
/*  588 */     final boolean isEnableMultiLang = Util.isEnableMultiLang();
/*  589 */     this.currentUserLang = user.getLanguage();
/*      */     
/*  591 */     final String _br = "\\n";
/*  592 */     final StringBuffer Errmsg = new StringBuffer();
/*  593 */     final String[] sql = { "" };
/*  594 */     String str2 = "";
/*      */     
/*  596 */     int n = 0;
/*  597 */     int i1 = 0;
/*  598 */     int i2 = 0;
/*  599 */     int i3 = 0;
/*  600 */     String str3 = "";
/*  601 */     String str4 = "";
/*  602 */     String str5 = "";
/*  603 */     str2 = Util.null2String(Util.null2String(paramMap.get("ismode")));
/*      */     
/*  605 */     n = Util.getIntValue(Util.null2String(paramMap.get("formid")));
/*  606 */     i1 = Util.getIntValue(Util.null2String(paramMap.get("isbill")));
/*  607 */     i2 = Util.getIntValue(Util.null2String(paramMap.get("nodeid")));
/*  608 */     i3 = Util.getIntValue(Util.null2String(paramMap.get("modeid")));
/*  609 */     str3 = Util.null2String(paramMap.get("isFieldTrigger"));
/*  610 */     str4 = Util.null2String(paramMap.get("clientIp"));
/*  611 */     str5 = Util.null2String(paramMap.get("fileid"));
/*  612 */     i2 = FreeNodeBiz.getExtendNodeId(i, i2);
/*  613 */     RecordSet recordSet1 = new RecordSet();
/*      */     
/*  615 */     int i4 = 0;
/*  616 */     recordSet1.executeSql("select workflowid from workflow_requestbase where requestid=" + i);
/*  617 */     if (recordSet1.next()) {
/*  618 */       i4 = recordSet1.getInt("workflowid");
/*      */     }
/*      */     
/*  621 */     hashMap1.put("wfid", Integer.valueOf(i4));
/*  622 */     hashMap1.put("requestid", Integer.valueOf(i));
/*  623 */     final int[] fnaWfTypeFilter = { 1 };
/*  624 */     String str6 = " select fnaWfType from FnaWorkflow where workflowid = ? ";
/*  625 */     RecordSet recordSet2 = new RecordSet();
/*  626 */     recordSet2.executeQuery(str6, new Object[] { Integer.valueOf(i4) });
/*  627 */     if (recordSet2.next()) {
/*  628 */       arrayOfInt1[0] = Util.getIntValue(recordSet2.getString("fnaWfType"), 1);
/*      */     }
/*  630 */     String str7 = "";
/*  631 */     recordSet1.executeQuery("select isModifyLog from workflow_base where id = ? ", new Object[] { Integer.valueOf(i4) });
/*  632 */     if (recordSet1.next()) {
/*  633 */       str7 = recordSet1.getString(1);
/*      */     }
/*      */     
/*  636 */     DetailDataModifyTrackBiz detailDataModifyTrackBiz = null;
/*  637 */     if ("1".equals(str7)) {
/*  638 */       int i6 = RequestOperationBiz.judgeAgentOperation(i, i2, user.getUID(), user.getType());
/*  639 */       detailDataModifyTrackBiz = new DetailDataModifyTrackBiz(user, i4, i2, n, i1, i6);
/*  640 */       detailDataModifyTrackBiz.generalModifyDetailBefore(i);
/*      */     } 
/*      */     
/*  643 */     FnaCommon fnaCommon = new FnaCommon();
/*  644 */     final Hashtable<Object, Object> otherPara_hs = new Hashtable<>();
/*  645 */     hashtable.put("hrmid", user.getUID() + "");
/*  646 */     hashtable.put("reqid", i + "");
/*      */     
/*  648 */     fnaCommon.loadWFLayoutToHtmlFnaInfo(n, i4, i, hashtable);
/*      */     
/*  650 */     HashMap<Object, Object> hashMap7 = (HashMap)hashtable.get("_isEnableFnaWfHm_fnaBudgetControl.getFnaWfFieldInfo4Expense_workflowid=" + i4 + "__requestId=" + i);
/*  651 */     if (hashMap7 == null) {
/*  652 */       hashMap7 = new HashMap<>();
/*      */     }
/*  654 */     final HashMap reqDataMap = null;
/*  655 */     final String fnaWfType = (String)hashMap7.get("fnaWfType");
/*      */     
/*  657 */     ArrayList<String> arrayList1 = new ArrayList();
/*  658 */     ArrayList<String> arrayList2 = new ArrayList();
/*  659 */     final FieldInfo fi = new FieldInfo();
/*  660 */     fieldInfo.setRequestid(i);
/*  661 */     fieldInfo.setUser(user);
/*  662 */     fieldInfo.getDetailTableFieldWithSort(n, i1, user.getLanguage(), String.valueOf(i4), String.valueOf(i2));
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  669 */     ArrayList<ArrayList> arrayList3 = fieldInfo.getDetailTableFields();
/*  670 */     ArrayList<ArrayList> arrayList4 = fieldInfo.getDetailDBFieldNames();
/*  671 */     ArrayList<ArrayList> arrayList5 = fieldInfo.getDetailFieldDBTypes();
/*  672 */     ArrayList<String> arrayList6 = fieldInfo.getDetailTableNames();
/*  673 */     ArrayList<String> arrayList7 = fieldInfo.getDetailTableKeys();
/*  674 */     ArrayList<List> arrayList = fieldInfo.getDetailTableIds();
/*      */ 
/*      */     
/*  677 */     if ("1".equals(str2) && i3 > 0) {
/*  678 */       arrayOfString[0] = "SELECT fieldid,isview,isedit FROM workflow_modeview WHERE formid=" + n + " AND isbill=" + i1 + " AND nodeid=" + i2;
/*      */ 
/*      */     
/*      */     }
/*      */     else {
/*      */ 
/*      */       
/*  685 */       arrayOfString[0] = "SELECT fieldid,isview,isedit,isonlyshow FROM workflow_nodeform WHERE nodeid=" + i2;
/*      */     } 
/*  687 */     recordSet1.executeSql(arrayOfString[0]);
/*  688 */     while (recordSet1.next()) {
/*  689 */       if ("1".equals(recordSet1.getString("isedit")) && !"1".equals(recordSet1.getString("isonlyshow"))) {
/*  690 */         arrayList1.add("field" + recordSet1.getString("fieldid")); continue;
/*  691 */       }  if ("1".equals(recordSet1.getString("isview")) || "1".equals(recordSet1.getString("isonlyshow"))) {
/*  692 */         arrayList2.add("field" + recordSet1.getString("fieldid"));
/*      */       }
/*      */     } 
/*  695 */     int i5 = 0;
/*  696 */     String str9 = "";
/*      */     
/*  698 */     if (i1 == 1) {
/*  699 */       arrayOfString[0] = "SELECT tablename FROM workflow_bill WHERE id=" + n;
/*  700 */       recordSet1.executeSql(arrayOfString[0]);
/*  701 */       if (recordSet1.next()) {
/*  702 */         str9 = Util.null2String(recordSet1.getString("tablename"));
/*  703 */         arrayOfString[0] = "SELECT id FROM " + str9 + " WHERE requestid=" + i;
/*  704 */         recordSet1.executeSql(arrayOfString[0]);
/*  705 */         if (recordSet1.next()) {
/*  706 */           i5 = recordSet1.getInt("id");
/*      */         }
/*      */       } 
/*      */     } else {
/*  710 */       i5 = i;
/*      */     } 
/*  712 */     String str10 = "";
/*  713 */     String str11 = "";
/*      */     
/*  715 */     HashMap<Object, Object> hashMap8 = new HashMap<>();
/*  716 */     WFNodeDtlFieldManager wFNodeDtlFieldManager = new WFNodeDtlFieldManager();
/*  717 */     Map<String, Integer> map = getTableGroupId(n);
/*  718 */     final Map requestFormInfoMap = getTableFieldValueMap(String.valueOf(i)).get("mainValueMap");
/*  719 */     Workbook workbook = null;
/*  720 */     final ArrayList errList = new ArrayList();
/*  721 */     JSONObject jSONObject = new JSONObject();
/*  722 */     final int[] errNum = { 0 };
/*  723 */     final int[] totalData = { 0 };
/*  724 */     long l2 = System.currentTimeMillis();
/*  725 */     writeLog("requestid=" + i + ",前期工作耗时=" + (l2 - l1));
/*      */     try {
/*  727 */       InputStream inputStream = ImageFileManager.getInputStreamById(Util.getIntValue(str5));
/*  728 */       workbook = WorkbookFactory.create(inputStream);
/*  729 */       int i6 = workbook.getNumberOfSheets();
/*  730 */       HashSet<String> hashSet = new HashSet();
/*  731 */       for (byte b1 = 0; b1 < i6; b1++) {
/*  732 */         String str = workbook.getSheetName(b1);
/*  733 */         hashSet.add(getNumeric(str));
/*      */       } 
/*  735 */       jSONObject.put("Parsing", Boolean.valueOf(true));
/*  736 */       final int[] num = { 0 };
/*  737 */       byte b2 = 0;
/*      */       
/*  739 */       ArrayList<ArrayList> arrayList9 = new ArrayList();
/*  740 */       ArrayList<ArrayList> arrayList10 = new ArrayList();
/*  741 */       ArrayList<ArrayList> arrayList11 = new ArrayList();
/*  742 */       ArrayList<String> arrayList12 = new ArrayList();
/*  743 */       ArrayList<String> arrayList13 = new ArrayList();
/*  744 */       ArrayList<List> arrayList14 = new ArrayList();
/*      */       
/*  746 */       for (byte b3 = 0; b3 < arrayList6.size(); b3++) {
/*  747 */         String str = getNumeric(Util.null2String(arrayList6.get(b3)));
/*  748 */         if (hashSet.contains(str)) {
/*  749 */           arrayList9.add(arrayList3.get(b3));
/*  750 */           arrayList10.add(arrayList4.get(b3));
/*  751 */           arrayList11.add(arrayList5.get(b3));
/*  752 */           if (i1 == 1) {
/*  753 */             arrayList12.add(arrayList6.get(b3));
/*  754 */             arrayList13.add(arrayList7.get(b3));
/*      */           } 
/*  756 */           arrayList14.add(arrayList.get(b3));
/*      */         } 
/*      */       } 
/*      */ 
/*      */ 
/*      */       
/*  762 */       arrayList3 = arrayList9;
/*  763 */       arrayList4 = arrayList10;
/*  764 */       arrayList5 = arrayList11;
/*  765 */       arrayList6 = arrayList12;
/*  766 */       arrayList7 = arrayList13;
/*  767 */       arrayList = arrayList14;
/*  768 */       final ArrayList detailtableidsTemp = (ArrayList)arrayList.clone();
/*  769 */       Map map2 = DetailFilterBiz.getNeedFilterDetailMap(i, i4, i2, 1, user);
/*  770 */       Iterator<String> iterator = hashSet.iterator();
/*  771 */       for (byte b4 = 0; b4 < arrayList3.size() && b4 < i6; b4++) {
/*  772 */         writeLog("---2.第【" + (b4 + 1) + "】个sheet开始导入---" + simpleDateFormat.format(new Date()));
/*  773 */         final Sheet sheet = workbook.getSheetAt(b4);
/*  774 */         if (sheet != null) {
/*  775 */           int i7 = sheet.getPhysicalNumberOfRows();
/*  776 */           final int olderDetailId = Integer.parseInt(iterator.next()) - 1;
/*      */           
/*  778 */           int i9 = i8;
/*  779 */           if (i1 != 1) {
/*  780 */             str10 = "workflow_formdetail";
/*      */           } else {
/*  782 */             str10 = Util.null2String(arrayList6.get(b4));
/*  783 */             str11 = Util.null2String(arrayList7.get(b4));
/*  784 */             if (str11.equals("")) str11 = "mainid"; 
/*  785 */             hashMap8.put(str10, str11);
/*  786 */             i9 = ((Integer)map.get(str10)).intValue() - 1;
/*      */           } 
/*      */           
/*  789 */           wFNodeDtlFieldManager.resetParameter();
/*  790 */           wFNodeDtlFieldManager.setNodeid(i2);
/*  791 */           wFNodeDtlFieldManager.setGroupid(i9);
/*  792 */           wFNodeDtlFieldManager.selectWfNodeDtlField();
/*  793 */           final String dtladd = wFNodeDtlFieldManager.getIsadd();
/*  794 */           final String dtledit = wFNodeDtlFieldManager.getIsedit();
/*  795 */           String str16 = wFNodeDtlFieldManager.getIsdelete();
/*      */           
/*  797 */           ArrayList<String> arrayList16 = arrayList3.get(b4);
/*  798 */           ArrayList arrayList17 = arrayList4.get(b4);
/*  799 */           ArrayList arrayList18 = arrayList5.get(b4);
/*  800 */           final ArrayList<String> fieldids = new ArrayList();
/*  801 */           final ArrayList<String> fieldnames = new ArrayList();
/*  802 */           final ArrayList<String> fieldbdtypes = new ArrayList();
/*  803 */           final ArrayList viewfieldids = new ArrayList();
/*  804 */           final ArrayList viewfieldnames = new ArrayList();
/*  805 */           final ArrayList viewfieldbdtypes = new ArrayList();
/*      */ 
/*      */           
/*  808 */           HashMap<Object, Object> hashMap9 = new HashMap<>();
/*  809 */           HashMap<Object, Object> hashMap10 = new HashMap<>();
/*  810 */           for (byte b = 0; b < arrayList16.size(); b++) {
/*  811 */             String[] arrayOfString1 = Util.TokenizerString2(arrayList16.get(b), "_");
/*  812 */             if (!"6".equals(arrayOfString1[3])) {
/*      */ 
/*      */ 
/*      */               
/*  816 */               if (arrayList1.indexOf(Util.TokenizerString((String)arrayList16.get(b), "_").get(0)) > -1) {
/*  817 */                 final String finalIsmode = Util.null2String(arrayList17.get(b));
/*  818 */                 final String finalDetailtable = Util.null2String(arrayList16.get(b));
/*  819 */                 final String finalDetailtablekey = Util.null2String(arrayList18.get(b));
/*  820 */                 arrayList19.add(str19);
/*  821 */                 arrayList20.add(str18);
/*  822 */                 arrayList21.add(str20);
/*      */                 
/*  824 */                 hashMap9.put(str18, str19);
/*  825 */                 hashMap10.put(str18, str20);
/*      */               } 
/*  827 */               if (arrayList2.indexOf(Util.TokenizerString((String)arrayList16.get(b), "_").get(0)) > -1) {
/*  828 */                 arrayList22.add(arrayList16.get(b));
/*  829 */                 arrayList23.add(arrayList17.get(b));
/*  830 */                 arrayList24.add(arrayList18.get(b));
/*      */               } 
/*      */             } 
/*      */           } 
/*  834 */           final boolean isNewExcelTemplate = isNewExcelTemplate(sheet);
/*  835 */           Map<String, Integer> map3 = null;
/*  836 */           if (bool1) {
/*  837 */             map3 = getImportField(sheet, arrayList20);
/*  838 */             arrayList20.clear();
/*  839 */             arrayList19.clear();
/*  840 */             arrayList21.clear();
/*  841 */             for (String str : map3.keySet()) {
/*  842 */               arrayList20.add(str);
/*  843 */               arrayList19.add((String)hashMap9.get(str));
/*  844 */               arrayList21.add((String)hashMap10.get(str));
/*      */             } 
/*      */           } 
/*      */ 
/*      */           
/*  849 */           final ArrayList<String> errorCell = new ArrayList();
/*  850 */           if (arrayList20.size() > 0) {
/*  851 */             boolean bool2 = true;
/*  852 */             byte b5 = 2;
/*      */             
/*  854 */             final HashMap<Object, Object> buttonTypeHm = new HashMap<>();
/*  855 */             while (bool2) {
/*  856 */               if ("fnaFeeWf".equals(str8) || "change".equals(str8) || "share".equals(str8)) {
/*  857 */                 boolean bool3 = true;
/*      */                 
/*  859 */                 for (byte b7 = 0; b7 < arrayList20.size(); b7++) {
/*  860 */                   ArrayList<String> arrayList33 = Util.TokenizerString(arrayList19.get(b7), "_");
/*  861 */                   int i21 = -1;
/*      */                   try {
/*  863 */                     i21 = Util.getIntValue(arrayList33.get(2));
/*  864 */                   } catch (Exception exception) {}
/*      */ 
/*      */                   
/*  867 */                   String str22 = "";
/*  868 */                   int i22 = 0;
/*      */                   try {
/*  870 */                     str22 = Util.null2String(arrayList19.get(b7)).trim();
/*  871 */                   } catch (Exception exception) {}
/*      */                   
/*      */                   try {
/*  874 */                     i22 = Util.getIntValue(str22.split("_")[0].replaceAll("field", ""));
/*  875 */                   } catch (Exception exception) {}
/*      */ 
/*      */ 
/*      */                   
/*  879 */                   String str23 = Util.null2String(arrayList20.get(b7)).trim();
/*  880 */                   String str24 = "";
/*  881 */                   if (bool1) {
/*  882 */                     int i23 = ((Integer)map3.get(str23)).intValue();
/*  883 */                     str24 = Util.null2String(POIUtil.getValue(sheet, b5 - 1, i23));
/*      */                   } else {
/*  885 */                     str24 = Util.null2String(POIUtil.getValue(sheet, b5 - 1, b7 + 1));
/*      */                   } 
/*      */                   
/*  888 */                   if (!"".equals(str24)) {
/*  889 */                     int i23 = Util.getIntValue(arrayList33.get(3));
/*      */                     
/*  891 */                     if ((i23 != 1 || i21 != 1) && (i23 != 2 || i21 != 2)) {
/*  892 */                       str24 = str24.trim();
/*      */                     }
/*  894 */                     if (i23 == 1 && i21 == 1) {
/*  895 */                       str24 = str24.replaceAll("<br>", "").replaceAll("\r", "").replaceAll("\n", "");
/*      */                     }
/*  897 */                     if (i23 == 2 && i21 == 1) {
/*  898 */                       str24 = str24.replaceAll("\n", "<br>");
/*      */                     }
/*      */                   } 
/*      */                   
/*  902 */                   if (!"".equals(str24)) bool3 = false;
/*      */                   
/*  904 */                   String str25 = "_sheetcount_" + (b4 + 1) + "_rows_" + (b5 - 1);
/*      */                   
/*  906 */                   if ("fieldIdOrgId_fieldId".equals(hashMap7.get(i22 + ""))) {
/*  907 */                     hashMap11.put("orgIdFieldId" + str25, str22 + "");
/*  908 */                     hashMap11.put("orgIdFieldId_int_" + str25, i22 + "");
/*      */                   }
/*  910 */                   else if ("fieldIdOrgId2_fieldId".equals(hashMap7.get(i22 + ""))) {
/*  911 */                     hashMap11.put("orgId2FieldId" + str25, str22 + "");
/*  912 */                     hashMap11.put("orgId2FieldId_int_" + str25, i22 + "");
/*      */                   }
/*  914 */                   else if ("fieldIdOrgType_fieldId".equals(hashMap7.get(i22 + ""))) {
/*  915 */                     hashMap11.put("orgTypeFieldId" + str25, i22 + "");
/*  916 */                     hashMap11.put("orgTypeFieldValue" + str25, str24 + "");
/*      */                   }
/*  918 */                   else if ("fieldIdOrgType2_fieldId".equals(hashMap7.get(i22 + ""))) {
/*  919 */                     hashMap11.put("orgType2FieldId" + str25, i22 + "");
/*  920 */                     hashMap11.put("orgType2FieldValue" + str25, str24 + "");
/*      */                   } 
/*      */                 } 
/*      */                 
/*  924 */                 if (bool3) {
/*  925 */                   bool2 = false;
/*      */                 }
/*      */               } else {
/*  928 */                 bool2 = false;
/*      */               } 
/*  930 */               b5++;
/*      */             } 
/*      */             
/*  933 */             bool2 = true;
/*  934 */             b5 = 1;
/*      */ 
/*      */             
/*  937 */             final int oldRowSize = ((List)arrayList.get(b4)).size();
/*  938 */             final ArrayList sns = new ArrayList();
/*  939 */             final TreeMap<Object, Object> insertParams = new TreeMap<>();
/*  940 */             final TreeMap<Object, Object> updateParams = new TreeMap<>();
/*  941 */             final StringBuilder[] inserBatchSql = { null };
/*  942 */             final StringBuilder[] updateBatchSql = { null };
/*  943 */             final ArrayList<String> sql1List = new ArrayList();
/*  944 */             final ArrayList<String> sql2List = new ArrayList();
/*  945 */             final ArrayList<Boolean> isnullvalueList = new ArrayList();
/*  946 */             final ArrayList<Boolean> isnullList = new ArrayList();
/*  947 */             final ArrayList<Integer> rowList = new ArrayList();
/*      */             
/*  949 */             final Map<String, Integer> finalImportFields = map3;
/*  950 */             final String finalIsmode = str2;
/*  951 */             final int finalRequestid = i;
/*  952 */             final HashMap<Object, Object> finalFnaWfSetMap = hashMap7;
/*  953 */             final byte finalI = b4;
/*  954 */             final int finalWorkflowid = i4;
/*  955 */             final int finalFormid = n;
/*  956 */             final int finalIsbill = i1;
/*  957 */             final String finalDetailtable = str10;
/*  958 */             final String finalDetailtablekey = str11;
/*  959 */             final int finalMainid = i5;
/*  960 */             final int finalNodeid = i2;
/*      */ 
/*      */             
/*  963 */             final String finalMaintable = str9;
/*      */             
/*  965 */             int i17 = Util.getIntValue((new WorkflowConfigComInfo()).getValue("detailImport_threadNum"), 5);
/*  966 */             int i18 = i7 / i17;
/*  967 */             writeLog("当前sheet【" + (b4 + 1) + "】总行数=" + i7 + ",开启线程数：" + i17);
/*  968 */             ArrayList<HashMap<Object, Object>> arrayList32 = new ArrayList();
/*  969 */             int i19 = 0;
/*  970 */             int i20 = 0;
/*  971 */             if ((i7 - 1) / i17 > 0) {
/*  972 */               for (byte b7 = 0; b7 < i17; b7++) {
/*  973 */                 HashMap<Object, Object> hashMap13 = new HashMap<>();
/*  974 */                 i19 = i18 * b7 + 1;
/*      */ 
/*      */ 
/*      */                 
/*  978 */                 i20 = i18 * (b7 + 1);
/*  979 */                 hashMap13.put("start", Integer.valueOf(i19));
/*  980 */                 hashMap13.put("end", Integer.valueOf(i20));
/*  981 */                 arrayList32.add(hashMap13);
/*      */               } 
/*      */             }
/*  984 */             if (i20 < i7) {
/*  985 */               HashMap<Object, Object> hashMap13 = new HashMap<>();
/*  986 */               hashMap13.put("start", Integer.valueOf(i20 + 1));
/*  987 */               hashMap13.put("end", Integer.valueOf(i7));
/*  988 */               arrayList32.add(hashMap13);
/*      */             } 
/*  990 */             writeLog("线程集合=" + arrayList32.toString() + ",线程执行开始>>>" + simpleDateFormat.format(new Date()));
/*  991 */             long l4 = System.currentTimeMillis();
/*  992 */             final CountDownLatch countDownLatch = new CountDownLatch(arrayList32.size());
/*  993 */             for (Map<Object, Object> map5 : arrayList32) {
/*  994 */               Thread thread = new Thread(new Runnable()
/*      */                   {
/*      */                     public void run()
/*      */                     {
/*  998 */                       RecordSet recordSet = new RecordSet();
/*  999 */                       RequestDetailImport.this.writeLog(System.identityHashCode(rowMap) + "开始第[" + rowMap.get("start") + "]行至[" + rowMap.get("end") + "]行的导入：" + dataFormat.format(new Date()));
/* 1000 */                       for (int i = ((Integer)rowMap.get("start")).intValue(); i <= ((Integer)rowMap.get("end")).intValue(); i++) {
/*      */                         try {
/* 1002 */                           HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 1003 */                           hashMap1.put("col", "");
/* 1004 */                           hashMap1.put("info", "");
/* 1005 */                           boolean bool1 = false;
/* 1006 */                           String str1 = "";
/* 1007 */                           String str2 = "";
/* 1008 */                           ArrayList<Integer> arrayList1 = new ArrayList();
/* 1009 */                           ArrayList<String> arrayList = new ArrayList();
/* 1010 */                           ArrayList<Integer> arrayList2 = new ArrayList();
/*      */                           
/* 1012 */                           boolean bool2 = false;
/* 1013 */                           boolean bool3 = true;
/* 1014 */                           boolean bool4 = false;
/* 1015 */                           boolean bool5 = true;
/* 1016 */                           HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 1017 */                           String str3 = "";
/*      */ 
/*      */                           
/* 1020 */                           StringBuilder stringBuilder1 = new StringBuilder();
/* 1021 */                           StringBuilder stringBuilder2 = new StringBuilder();
/*      */ 
/*      */                           
/* 1024 */                           StringBuilder stringBuilder3 = new StringBuilder();
/* 1025 */                           StringBuilder stringBuilder4 = new StringBuilder();
/*      */ 
/*      */ 
/*      */                           
/* 1029 */                           boolean bool6 = false;
/* 1030 */                           int j = Util.getIntValue(Util.null2String(POIUtil.getValue(sheet, i, 0)).trim());
/* 1031 */                           if (j > 0 && j <= oldRowSize) {
/* 1032 */                             sns.add(Integer.valueOf(j));
/*      */ 
/*      */                             
/* 1035 */                             if ("1".equals(dtledit) || "1".equals(finalIsmode)) {
/* 1036 */                               bool6 = true;
/*      */                             } else {
/* 1038 */                               bool6 = false;
/*      */                             }
/*      */                           
/*      */                           }
/* 1042 */                           else if ("1".equals(dtladd) || "1".equals(finalIsmode)) {
/* 1043 */                             bool6 = true;
/*      */                           } else {
/* 1045 */                             bool6 = false;
/*      */                           } 
/*      */                           
/* 1048 */                           ArrayList<HashMap<Object, Object>> arrayList3 = new ArrayList();
/* 1049 */                           HashMap<Object, Object> hashMap3 = new HashMap<>();
/* 1050 */                           int k = 0;
/* 1051 */                           for (byte b = 0; b < fieldnames.size(); b++) {
/*      */                             
/* 1053 */                             String str = Util.null2String(fieldnames.get(b)).trim();
/*      */                             
/* 1055 */                             if (!viewfieldnames.contains(str)) {
/*      */ 
/*      */ 
/*      */                               
/* 1059 */                               String str4 = "_sheetcount_" + (finalI + 1) + "_rows_" + i;
/* 1060 */                               String str5 = "";
/* 1061 */                               int m = 0;
/*      */                               try {
/* 1063 */                                 str5 = Util.null2String(fieldids.get(b)).trim();
/* 1064 */                               } catch (Exception exception) {}
/*      */                               
/*      */                               try {
/* 1067 */                                 m = Util.getIntValue(str5.split("_")[0].replaceAll("field", ""));
/* 1068 */                               } catch (Exception exception) {}
/*      */ 
/*      */ 
/*      */                               
/* 1072 */                               ArrayList<String> arrayList4 = Util.TokenizerString(fieldids.get(b), "_");
/* 1073 */                               int n = Util.getIntValue(arrayList4.get(2));
/* 1074 */                               int i1 = Util.getIntValue(arrayList4.get(3));
/* 1075 */                               String str6 = null;
/* 1076 */                               if (isNewExcelTemplate) {
/* 1077 */                                 int i2 = ((Integer)finalImportFields.get(str)).intValue();
/* 1078 */                                 str6 = Util.null2String(POIUtil.getValue(sheet, i, i2));
/*      */                               } else {
/* 1080 */                                 str6 = Util.null2String(POIUtil.getValue(sheet, i, b + 1));
/*      */                               } 
/*      */ 
/*      */                               
/* 1084 */                               if (i1 == 1 && n == 1) {
/* 1085 */                                 str6 = str6.replaceAll("<br>", "").replaceAll("\r", "").replaceAll("\n", "");
/*      */                               }
/* 1087 */                               if ((i1 != 1 || n != 1) && i1 != 2) {
/* 1088 */                                 str6 = str6.trim();
/*      */                               }
/* 1090 */                               if (i1 == 2 && n == 1) {
/* 1091 */                                 str6 = str6.replaceAll("\n", "<br>");
/*      */                               }
/* 1093 */                               boolean bool = false;
/*      */ 
/*      */                               
/* 1096 */                               if (!"".equals(str6)) {
/* 1097 */                                 bool3 = false;
/*      */                                 
/* 1099 */                                 if (bool6) {
/* 1100 */                                   str6 = str6.replace("'", "''");
/* 1101 */                                   if (i1 == 3) {
/* 1102 */                                     String str7 = Util.null2String((String)buttonTypeHm.get("orgIdFieldId" + str4)).trim();
/* 1103 */                                     int i2 = Util.getIntValue((String)buttonTypeHm.get("orgIdFieldId_int_" + str4), 0);
/* 1104 */                                     String str8 = Util.null2String((String)buttonTypeHm.get("orgId2FieldId" + str4)).trim();
/* 1105 */                                     int i3 = Util.getIntValue((String)buttonTypeHm.get("orgId2FieldId_int_" + str4), 0);
/*      */                                     
/* 1107 */                                     if (str7.equals(str5)) {
/* 1108 */                                       String str9 = Util.null2String((String)buttonTypeHm.get("orgTypeFieldValue" + str4)).trim();
/* 1109 */                                       int i4 = Util.getIntValue((String)buttonTypeHm.get("orgTypeFieldId" + str4));
/*      */                                       
/* 1111 */                                       n = FnaCommon.getOrgSelectValueByOrgTypeSelectName(n, i2, i4, str9, finalRequestid, user
/* 1112 */                                           .getLanguage(), finalFnaWfSetMap, reqDataMap, otherPara_hs);
/*      */                                     
/*      */                                     }
/* 1115 */                                     else if (str8.equals(str5)) {
/* 1116 */                                       String str9 = Util.null2String((String)buttonTypeHm.get("orgType2FieldValue" + str4)).trim();
/* 1117 */                                       int i4 = Util.getIntValue((String)buttonTypeHm.get("orgType2FieldId" + str4));
/*      */                                       
/* 1119 */                                       n = FnaCommon.getOrgSelectValueByOrgTypeSelectName(n, i3, i4, str9, finalRequestid, user
/* 1120 */                                           .getLanguage(), finalFnaWfSetMap, reqDataMap, otherPara_hs);
/*      */                                     } 
/*      */ 
/*      */ 
/*      */                                     
/* 1125 */                                     if (!isEnableMultiLang && n == 22 && wfDtlImpRole4Subject != 2) {
/* 1126 */                                       ArrayList<String> arrayList5 = Util.TokenizerString(str6, ",");
/*      */                                       
/* 1128 */                                       String str9 = "name";
/* 1129 */                                       if (wfDtlImpRole4Subject == 1) {
/* 1130 */                                         str9 = "codeName";
/*      */                                       }
/* 1132 */                                       String str10 = " FnaBudgetfeeType ";
/* 1133 */                                       String str11 = " count(*) as cnt ";
/* 1134 */                                       String str12 = str9;
/* 1135 */                                       String str13 = StringEscapeUtils.escapeSql(((String)arrayList5.get(0)).trim());
/* 1136 */                                       String str14 = " and (Archive is null or Archive = 0) and isEditFeeTypeId > 0 ";
/* 1137 */                                       String str15 = RequestDetailImport.this.forMultiLangSql(str10, str11, str12, str13, str14);
/*      */                                       
/* 1139 */                                       recordSet.executeSql(str15);
/* 1140 */                                       int i4 = 0;
/* 1141 */                                       if (recordSet.next()) {
/* 1142 */                                         i4 = recordSet.getInt("cnt");
/*      */                                       }
/*      */                                       
/* 1145 */                                       String str16 = ((String)arrayList5.get(0)).trim();
/* 1146 */                                       if (i4 < 1) {
/*      */                                         
/* 1148 */                                         Errmsg.append(SystemEnv.getHtmlLabelName(383991, user.getLanguage()).replace("#rows#", String.valueOf(i)).replace("#subjectName#", str16) + _br);
/* 1149 */                                         str6 = "";
/*      */                                       } 
/* 1151 */                                       if (i4 > 1) {
/*      */                                         
/* 1153 */                                         Errmsg.append(SystemEnv.getHtmlLabelName(383992, user.getLanguage()).replace("#rows#", String.valueOf(i)).replace("#subjectName#", str16) + _br);
/* 1154 */                                         str6 = "";
/*      */                                       } 
/*      */                                     } 
/*      */ 
/*      */                                     
/* 1159 */                                     if (n == 2) {
/*      */                                       try {
/* 1161 */                                         SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
/* 1162 */                                         simpleDateFormat.setLenient(false);
/* 1163 */                                         simpleDateFormat.parse(str6);
/* 1164 */                                         if (str6.indexOf("-") < 0) {
/* 1165 */                                           str6 = Util.null2String(POIUtil.getValue(sheet, i, b + 1));
/*      */                                         } else {
/* 1167 */                                           str6 = TimeUtil.SetDateFormat(str6, "yyyy'-'MM'-'dd");
/*      */                                         } 
/* 1169 */                                       } catch (Exception exception) {
/* 1170 */                                         hashMap1.put("col", (new StringBuilder()).append(hashMap1.get("col")).append(RequestDetailImport.this.getColChar(((Integer)finalImportFields.get(str)).intValue())).append(";").toString());
/* 1171 */                                         hashMap1.put("info", (new StringBuilder()).append(hashMap1.get("info")).append("“").append(str6).append("”").append(SystemEnv.getHtmlLabelName(530134, user.getLanguage())).append(";").toString());
/* 1172 */                                         bool1 = true;
/* 1173 */                                         errorCell.add((finalI + 1) + "," + i + "," + (b + 1));
/* 1174 */                                         str6 = "";
/*      */                                       } 
/* 1176 */                                     } else if (n == 19) {
/*      */                                       try {
/* 1178 */                                         SimpleDateFormat simpleDateFormat = new SimpleDateFormat("HH:mm");
/* 1179 */                                         simpleDateFormat.setLenient(false);
/* 1180 */                                         simpleDateFormat.parse(str6);
/* 1181 */                                         if (str6.indexOf(":") < 0) {
/* 1182 */                                           str6 = Util.null2String(POIUtil.getValue(sheet, i, b + 1));
/*      */                                         } else {
/* 1184 */                                           SimpleDateFormat simpleDateFormat1 = new SimpleDateFormat("HH:mm");
/* 1185 */                                           Date date = simpleDateFormat1.parse(str6);
/* 1186 */                                           str6 = simpleDateFormat1.format(date);
/*      */                                         } 
/* 1188 */                                       } catch (Exception exception) {
/* 1189 */                                         hashMap1.put("col", (new StringBuilder()).append(hashMap1.get("col")).append(RequestDetailImport.this.getColChar(((Integer)finalImportFields.get(str)).intValue())).append(";").toString());
/* 1190 */                                         hashMap1.put("info", (new StringBuilder()).append(hashMap1.get("info")).append("“").append(str6).append("”").append(SystemEnv.getHtmlLabelName(530134, user.getLanguage())).append(";").toString());
/* 1191 */                                         bool1 = true;
/* 1192 */                                         errorCell.add((finalI + 1) + "," + i + "," + (b + 1));
/* 1193 */                                         str6 = "";
/*      */                                       } 
/* 1195 */                                     } else if (n == 290) {
/*      */                                       try {
/* 1197 */                                         String str9 = Util.null2String((new BaseBean()).getPropValue("weaver_timezone_conversion", "timeZoneConversion")).trim();
/* 1198 */                                         SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm");
/* 1199 */                                         simpleDateFormat.setLenient(false);
/* 1200 */                                         simpleDateFormat.parse(str6);
/* 1201 */                                         DateTransformer dateTransformer = new DateTransformer();
/* 1202 */                                         if (str6.indexOf(":") != -1 && str6.indexOf("-") != -1) {
/* 1203 */                                           if ("1".equals(str9)) {
/* 1204 */                                             str6 = dateTransformer.getServerDateTime(str6);
/* 1205 */                                             str6 = TimeUtil.SetDateFormat(str6, "yyyy-MM-dd HH:mm");
/*      */                                           } 
/*      */                                         } else {
/* 1208 */                                           str6 = Util.null2String(POIUtil.getValue(sheet, i, b + 1));
/* 1209 */                                           if ("1".equals(str9)) {
/* 1210 */                                             str6 = dateTransformer.getServerDateTime(str6);
/*      */                                           }
/*      */                                         } 
/* 1213 */                                       } catch (Exception exception) {
/* 1214 */                                         hashMap1.put("col", (new StringBuilder()).append(hashMap1.get("col")).append(RequestDetailImport.this.getColChar(((Integer)finalImportFields.get(str)).intValue())).append(";").toString());
/* 1215 */                                         hashMap1.put("info", (new StringBuilder()).append(hashMap1.get("info")).append("“").append(str6).append("”").append(SystemEnv.getHtmlLabelName(530134, user.getLanguage())).append(";").toString());
/* 1216 */                                         bool1 = true;
/* 1217 */                                         errorCell.add((finalI + 1) + "," + i + "," + (b + 1));
/* 1218 */                                         str6 = "";
/*      */                                       } 
/* 1220 */                                     } else if (n == 402) {
/*      */                                       try {
/* 1222 */                                         str6 = TimeUtil.SetDateFormat(str6, "yyyy");
/* 1223 */                                       } catch (Exception exception) {
/* 1224 */                                         exception.printStackTrace();
/* 1225 */                                         hashMap1.put("col", (new StringBuilder()).append(hashMap1.get("col")).append(RequestDetailImport.this.getColChar(((Integer)finalImportFields.get(str)).intValue())).append(";").toString());
/* 1226 */                                         hashMap1.put("info", (new StringBuilder()).append(hashMap1.get("info")).append("“").append(str6).append("”").append(SystemEnv.getHtmlLabelName(530134, user.getLanguage())).append(";").toString());
/* 1227 */                                         bool1 = true;
/* 1228 */                                         errorCell.add((finalI + 1) + "," + i + "," + (b + 1));
/* 1229 */                                         str6 = "";
/*      */                                       } 
/* 1231 */                                     } else if (n == 403) {
/* 1232 */                                       if (str6.indexOf("-") < 0) {
/* 1233 */                                         str6 = Util.null2String(POIUtil.getValue(sheet, i, b + 1));
/*      */                                       } else {
/*      */                                         try {
/* 1236 */                                           str6 = TimeUtil.SetDateFormat(str6, "yyyy'-'MM");
/* 1237 */                                         } catch (Exception exception) {
/* 1238 */                                           exception.printStackTrace();
/* 1239 */                                           hashMap1.put("col", (new StringBuilder()).append(hashMap1.get("col")).append(RequestDetailImport.this.getColChar(((Integer)finalImportFields.get(str)).intValue())).append(";").toString());
/* 1240 */                                           hashMap1.put("info", (new StringBuilder()).append(hashMap1.get("info")).append("“").append(str6).append("”").append(SystemEnv.getHtmlLabelName(530134, user.getLanguage())).append(";").toString());
/* 1241 */                                           bool1 = true;
/* 1242 */                                           errorCell.add((finalI + 1) + "," + i + "," + (b + 1));
/* 1243 */                                           str6 = "";
/*      */                                         } 
/*      */                                       } 
/* 1246 */                                     } else if (hrmImport.isOrgField(n)) {
/* 1247 */                                       String str9 = str6;
/* 1248 */                                       str6 = hrmImport.getOrgFieldValue(n, str6);
/* 1249 */                                       if (Strings.isNullOrEmpty(str6)) {
/* 1250 */                                         if (!Strings.isNullOrEmpty(str9)) {
/* 1251 */                                           str6 = null;
/*      */                                         }
/* 1253 */                                         hashMap1.put("col", (new StringBuilder()).append(hashMap1.get("col")).append(RequestDetailImport.this.getColChar(((Integer)finalImportFields.get(str)).intValue())).append(";").toString());
/* 1254 */                                         hashMap1.put("info", (new StringBuilder()).append(hashMap1.get("info")).append("“").append(str9).append("”").append(SystemEnv.getHtmlLabelName(530134, user.getLanguage())).append(";").toString());
/* 1255 */                                         bool1 = true;
/* 1256 */                                         errorCell.add((finalI + 1) + "," + i + "," + (b + 1));
/*      */                                       } 
/* 1258 */                                     } else if (n == 161 || n == 162) {
/* 1259 */                                       HashMap<Object, Object> hashMap = new HashMap<>();
/* 1260 */                                       hashMap.put("fieldname", str);
/* 1261 */                                       hashMap.put("fieldvalue", str6);
/* 1262 */                                       hashMap.put("browsertype", n + "");
/* 1263 */                                       hashMap.put("dbtype", fieldbdtypes.get(b));
/* 1264 */                                       arrayList3.add(hashMap);
/* 1265 */                                       bool = true;
/* 1266 */                                     } else if (n == 256 || n == 257) {
/*      */                                       try {
/* 1268 */                                         String str9 = fieldbdtypes.get(b);
/* 1269 */                                         Map map = modeDetailImport.importTreeData(str6, str9, n);
/* 1270 */                                         if (!map.isEmpty()) {
/* 1271 */                                           str6 = Util.null2String((String)map.get("importData"));
/*      */                                         }
/* 1273 */                                       } catch (Exception exception) {
/* 1274 */                                         hashMap1.put("col", (new StringBuilder()).append(hashMap1.get("col")).append(RequestDetailImport.this.getColChar(((Integer)finalImportFields.get(str)).intValue())).append(";").toString());
/* 1275 */                                         hashMap1.put("info", (new StringBuilder()).append(hashMap1.get("info")).append("“").append(str6).append("”").append(SystemEnv.getHtmlLabelName(530134, user.getLanguage())).append(";").toString());
/* 1276 */                                         bool1 = true;
/* 1277 */                                         errorCell.add((finalI + 1) + "," + i + "," + (b + 1));
/* 1278 */                                         str6 = "";
/*      */                                       } 
/* 1280 */                                     } else if (n != 224 && n != 225) {
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */                                       
/* 1294 */                                       if (n != 226 && n != 227)
/*      */                                       {
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */                                         
/* 1302 */                                         if (n == 296) {
/* 1303 */                                           List<String> list; RequestDetailImport.this.writeLog("fna 多维度预算周期 oldvalue:" + str6 + ",workflowid" + finalWorkflowid + ",requestid" + finalRequestid);
/*      */                                           try {
/* 1305 */                                             str6 = fnaCommonService.getPeriodIdByName(finalWorkflowid, finalRequestid + "", user, str6);
/* 1306 */                                           } catch (Exception exception) {
/* 1307 */                                             exception.printStackTrace();
/*      */                                           } 
/* 1309 */                                           RequestDetailImport.this.writeLog("fna 多维度预算周期 value:" + str6);
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */                                           
/* 1317 */                                           String str9 = FnaReimbursementUtils.checkWorkflowAccount(Util.null2String(Integer.valueOf(requestid)), String.valueOf(finalWorkflowid), finalFormid, user.getLanguage(), fnaWfTypeFilter[0]);
/* 1318 */                                           String str10 = FnaBrowserUtils.getTableName(str9, FnaAccTypeConstant.BUDGET_PERIOD.intValue());
/*      */ 
/*      */                                           
/* 1321 */                                           String str11 = "" + finalWorkflowid + m;
/* 1322 */                                           if (filterPeriodConditionMap.containsKey(str11)) {
/*      */                                             
/* 1324 */                                             list = (List)filterPeriodConditionMap.get(str11);
/*      */                                           } else {
/* 1326 */                                             FnaPeriodFilter fnaPeriodFilter = new FnaPeriodFilter();
/* 1327 */                                             list = fnaPeriodFilter.multiDimensionFilterPeriod(finalWorkflowid, m, str10, str9);
/* 1328 */                                             filterBearerConditionMap.put(str11, list);
/*      */                                           } 
/* 1330 */                                           int i4 = list.size();
/*      */                                           
/* 1332 */                                           if (i4 <= 0 || !list.contains(str6)) {
/* 1333 */                                             str6 = "";
/*      */                                           }
/* 1335 */                                         } else if (n == 295) {
/* 1336 */                                           List<String> list; RequestDetailImport.this.writeLog("fna 多维度承担主体 oldvalue:" + str6 + ",workflowid" + finalWorkflowid + ",requestid" + finalRequestid);
/*      */                                           try {
/* 1338 */                                             str6 = fnaCommonService.getBearerIdByName(finalWorkflowid, finalRequestid + "", user, str6);
/* 1339 */                                           } catch (Exception exception) {
/* 1340 */                                             exception.printStackTrace();
/*      */                                           } 
/* 1342 */                                           RequestDetailImport.this.writeLog("fna 多维度承担主体 value:" + str6);
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */                                           
/* 1350 */                                           String str9 = FnaReimbursementUtils.checkWorkflowAccount(Util.null2String(Integer.valueOf(requestid)), String.valueOf(finalWorkflowid), finalFormid, user.getLanguage(), fnaWfTypeFilter[0]);
/* 1351 */                                           String str10 = FnaBrowserUtils.getTableName(str9, FnaAccTypeConstant.BUDGET_BEARER.intValue());
/*      */                                           
/* 1353 */                                           String str11 = "" + finalWorkflowid + m;
/* 1354 */                                           if (filterBearerConditionMap.containsKey(str11)) {
/*      */                                             
/* 1356 */                                             list = (List)filterBearerConditionMap.get(str11);
/*      */                                           } else {
/* 1358 */                                             FnaBearerFilter fnaBearerFilter = new FnaBearerFilter();
/* 1359 */                                             list = fnaBearerFilter.multiDimensionFilterBearer(finalWorkflowid, m, str10, str9);
/* 1360 */                                             filterBearerConditionMap.put(str11, list);
/*      */                                           } 
/* 1362 */                                           int i4 = list.size();
/*      */                                           
/* 1364 */                                           if (i4 <= 0 || !list.contains(str6)) {
/* 1365 */                                             str6 = "";
/*      */                                           }
/* 1367 */                                         } else if (n == 294) {
/* 1368 */                                           RequestDetailImport.this.writeLog("fna 多维度预算科目 oldvalue:" + str6 + ",workflowid" + finalWorkflowid + ",requestid" + finalRequestid);
/*      */                                           try {
/* 1370 */                                             str6 = fnaCommonService.getSubjectIdByName(finalWorkflowid, finalRequestid + "", user, str6);
/* 1371 */                                           } catch (Exception exception) {
/* 1372 */                                             exception.printStackTrace();
/*      */                                           } 
/* 1374 */                                           RequestDetailImport.this.writeLog("fna 多维度预算科目 value:" + str6);
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */                                           
/* 1383 */                                           BaseBean baseBean = new BaseBean();
/* 1384 */                                           baseBean.writeLog("================xhxhxhxhxh==RequestDetailImport===抵达===========================");
/*      */ 
/*      */                                           
/* 1387 */                                           String str9 = FnaReimbursementUtils.checkWorkflowAccount(Util.null2String(Integer.valueOf(requestid)), String.valueOf(finalWorkflowid), finalFormid, user.getLanguage(), fnaWfTypeFilter[0]);
/* 1388 */                                           String str10 = FnaBrowserUtils.getTableName(str9, FnaAccTypeConstant.BUDGET_SUBJECT.intValue());
/*      */                                           
/* 1390 */                                           baseBean.writeLog("===xhxhxhhx==accountId:" + str9 + "  tableName:" + str10);
/*      */                                           
/* 1392 */                                           List<String> list = new ArrayList();
/*      */                                           
/* 1394 */                                           String str11 = "" + finalWorkflowid + m;
/* 1395 */                                           if (filterSubjectConditionMap.containsKey(str11)) {
/*      */                                             
/* 1397 */                                             List list1 = (List)filterSubjectConditionMap.get(str11);
/*      */                                           } else {
/* 1399 */                                             SubjectFilter subjectFilter = new SubjectFilter();
/*      */                                             try {
/* 1401 */                                               list = subjectFilter.multiDimensionFilterSubject(finalWorkflowid, m, str10, str9);
/* 1402 */                                             } catch (Exception exception) {
/* 1403 */                                               exception.printStackTrace();
/*      */                                             } 
/*      */                                             
/* 1406 */                                             filterSubjectConditionMap.put(str11, list);
/*      */                                           } 
/*      */                                           
/* 1409 */                                           baseBean.writeLog("RequestDetailImport===执行过滤前tempvalue：" + str6);
/* 1410 */                                           baseBean.writeLog("RequestDetailImport===过滤subjectsConditionList：" + list.toString());
/* 1411 */                                           int i4 = list.size();
/*      */                                           
/* 1413 */                                           if (i4 <= 0 || !list.contains(str6)) {
/* 1414 */                                             str6 = "";
/*      */                                           }
/* 1416 */                                           baseBean.writeLog("RequestDetailImport===执行过滤后tempvalue：" + str6);
/* 1417 */                                           baseBean.writeLog("================xhxhxhxhxh==RequestDetailImport===结束===========================");
/*      */                                         }
/*      */                                         else {
/*      */                                           
/* 1421 */                                           if (n == 22 && wfDtlImpRole4Subject == 2) {
/* 1422 */                                             ArrayList<String> arrayList5 = Util.TokenizerString(str6, ",");
/*      */                                             
/* 1424 */                                             RequestDetailImportUtil requestDetailImportUtil = new RequestDetailImportUtil();
/* 1425 */                                             requestDetailImportUtil.setWorkflowId(finalWorkflowid);
/* 1426 */                                             StringBuffer stringBuffer = new StringBuffer();
/*      */                                             
/* 1428 */                                             int i4 = requestDetailImportUtil.checkSubjectRepeat(isEnableMultiLang, user, ((String)arrayList5.get(0)).trim(), stringBuffer, finalI + 1, i, _br);
/* 1429 */                                             if ("".equals(stringBuffer.toString())) {
/* 1430 */                                               str6 = String.valueOf(i4);
/*      */                                             } else {
/* 1432 */                                               Errmsg.append(stringBuffer.toString());
/* 1433 */                                               str6 = "";
/*      */                                             } 
/*      */                                           } else {
/* 1436 */                                             recordSet.executeSql("SELECT tablename,columname,keycolumname FROM workflow_browserurl WHERE id=" + n);
/* 1437 */                                             if (recordSet.next()) {
/* 1438 */                                               ArrayList<String> arrayList5 = Util.TokenizerString(str6, ",");
/* 1439 */                                               str6 = "";
/* 1440 */                                               String str9 = Util.null2String(recordSet.getString("keycolumname"));
/* 1441 */                                               String str10 = Util.null2String(recordSet.getString("tablename"));
/* 1442 */                                               String str11 = Util.null2String(recordSet.getString("columname"));
/* 1443 */                                               if (!"".equals(str9) && !"".equals(str10) && !"".equals(str11)) {
/* 1444 */                                                 String str12 = null;
/* 1445 */                                                 String str13 = null;
/* 1446 */                                                 String str14 = null;
/* 1447 */                                                 String str15 = null;
/* 1448 */                                                 String str16 = null;
/*      */                                                 
/* 1450 */                                                 for (byte b1 = 0; b1 < arrayList5.size(); b1++) {
/*      */                                                   
/* 1452 */                                                   if ("FnaCostCenter".equalsIgnoreCase(str10.toLowerCase()) && wfDtlImpRole4Fcc == 2) {
/* 1453 */                                                     RequestDetailImportUtil requestDetailImportUtil = new RequestDetailImportUtil();
/* 1454 */                                                     requestDetailImportUtil.setWorkflowId(finalWorkflowid);
/* 1455 */                                                     StringBuffer stringBuffer = new StringBuffer();
/*      */                                                     
/* 1457 */                                                     String str17 = Util.null2String(arrayList5.get(b1));
/*      */                                                     
/* 1459 */                                                     String str18 = requestDetailImportUtil.checkCostCenterImport(isEnableMultiLang, user, str17, stringBuffer, _br);
/* 1460 */                                                     if ("".equals(stringBuffer.toString())) {
/* 1461 */                                                       if (str6.equals("")) {
/* 1462 */                                                         str6 = str18;
/*      */                                                       } else {
/* 1464 */                                                         str6 = str6 + "," + str18;
/*      */                                                       } 
/*      */                                                     } else {
/* 1467 */                                                       Errmsg.append(stringBuffer.toString());
/* 1468 */                                                       str6 = "";
/*      */                                                     }
/*      */                                                   
/*      */                                                   }
/* 1472 */                                                   else if (isEnableMultiLang && ("FnaCostCenter".equalsIgnoreCase(str10.toLowerCase()) || n == 22)) {
/* 1473 */                                                     HashMap<Object, Object> hashMap = null;
/* 1474 */                                                     List<String> list = null;
/* 1475 */                                                     if ("FnaCostCenter".equalsIgnoreCase(str10.toLowerCase())) {
/* 1476 */                                                       if (dbDataHm.containsKey("FnaCostCenter")) {
/* 1477 */                                                         hashMap = (HashMap)dbDataHm.get("FnaCostCenter");
/* 1478 */                                                         list = (List)dbDataRepeatKeyHm.get("FnaCostCenter");
/*      */                                                       } else {
/* 1480 */                                                         hashMap = new HashMap<>();
/* 1481 */                                                         dbDataHm.put("FnaCostCenter", hashMap);
/* 1482 */                                                         list = new ArrayList();
/* 1483 */                                                         dbDataRepeatKeyHm.put("FnaCostCenter", list);
/*      */                                                         
/* 1485 */                                                         RecordSet recordSet2 = new RecordSet();
/* 1486 */                                                         recordSet2.executeQuery("select a.id, a.name, a.code from FnaCostCenter a where (a.Archive is null or a.Archive=0) and a.type=1 order by a.id DESC", new Object[0]);
/* 1487 */                                                         while (recordSet2.next()) {
/* 1488 */                                                           String str18 = recordSet2.getString("id");
/* 1489 */                                                           String str19 = null;
/* 1490 */                                                           if (wfDtlImpRole4Fcc == 1) {
/* 1491 */                                                             str19 = Util.null2String(recordSet2.getString("code")).trim();
/*      */                                                           } else {
/* 1493 */                                                             str19 = Util.null2String(recordSet2.getString("name")).trim();
/*      */                                                           } 
/*      */                                                           
/* 1496 */                                                           if (str19.indexOf("`~`") >= 0) {
/* 1497 */                                                             String str20 = Util.formatMultiLang(str19, String.valueOf(user.getLanguage()));
/* 1498 */                                                             if (!"".equals(str20)) {
/* 1499 */                                                               if (hashMap.containsKey(str20)) {
/* 1500 */                                                                 list.add(str20); continue;
/*      */                                                               } 
/* 1502 */                                                               hashMap.put(str20, str18);
/*      */                                                             } 
/*      */                                                             continue;
/*      */                                                           } 
/* 1506 */                                                           if (!"".equals(str19)) {
/* 1507 */                                                             if (hashMap.containsKey(str19)) {
/* 1508 */                                                               list.add(str19); continue;
/*      */                                                             } 
/* 1510 */                                                             hashMap.put(str19, str18);
/*      */                                                           }
/*      */                                                         
/*      */                                                         }
/*      */                                                       
/*      */                                                       }
/*      */                                                     
/* 1517 */                                                     } else if (n == 22) {
/* 1518 */                                                       if (dbDataHm.containsKey("22")) {
/* 1519 */                                                         hashMap = (HashMap<Object, Object>)dbDataHm.get("22");
/* 1520 */                                                         list = (List<String>)dbDataRepeatKeyHm.get("22");
/*      */                                                       } else {
/* 1522 */                                                         String str18 = "";
/* 1523 */                                                         if ("change".equals(fnaWfType) || (finalIsbill == 1 && finalFormid == 159)) {
/* 1524 */                                                           str18 = str18 + " a.isEditFeeType = 1 and ";
/*      */                                                         }
/* 1526 */                                                         else if (optionalSubject == 1) {
/* 1527 */                                                           str18 = str18 + " not EXISTS (select 1 from FnaBudgetfeeType a1 where a1.supsubject = a.id) and ";
/*      */                                                         } 
/*      */ 
/*      */                                                         
/* 1531 */                                                         hashMap = new HashMap<>();
/* 1532 */                                                         dbDataHm.put("22", hashMap);
/* 1533 */                                                         list = new ArrayList<>();
/* 1534 */                                                         dbDataRepeatKeyHm.put("22", list);
/*      */                                                         
/* 1536 */                                                         RecordSet recordSet2 = new RecordSet();
/* 1537 */                                                         recordSet2.executeQuery("select a.id, a.name, a.codeName from FnaBudgetfeeType a where " + str18 + " (a.Archive is null or a.Archive=0) and a.ISEDITFEETYPEID > 0 order by a.id DESC", new Object[0]);
/* 1538 */                                                         while (recordSet2.next()) {
/* 1539 */                                                           String str19 = recordSet2.getString("id");
/* 1540 */                                                           String str20 = null;
/* 1541 */                                                           if (wfDtlImpRole4Subject == 1) {
/* 1542 */                                                             str20 = Util.null2String(recordSet2.getString("codeName")).trim();
/*      */                                                           } else {
/* 1544 */                                                             str20 = Util.null2String(recordSet2.getString("name")).trim();
/*      */                                                           } 
/*      */                                                           
/* 1547 */                                                           if (str20.indexOf("`~`") >= 0) {
/* 1548 */                                                             String str21 = Util.formatMultiLang(str20, String.valueOf(user.getLanguage()));
/* 1549 */                                                             if (!"".equals(str21)) {
/* 1550 */                                                               if (hashMap.containsKey(str21)) {
/* 1551 */                                                                 list.add(str21); continue;
/*      */                                                               } 
/* 1553 */                                                               hashMap.put(str21, str19);
/*      */                                                             } 
/*      */                                                             continue;
/*      */                                                           } 
/* 1557 */                                                           if (!"".equals(str20)) {
/* 1558 */                                                             if (hashMap.containsKey(str20)) {
/* 1559 */                                                               list.add(str20); continue;
/*      */                                                             } 
/* 1561 */                                                             hashMap.put(str20, str19);
/*      */                                                           } 
/*      */                                                         } 
/*      */                                                       } 
/*      */                                                     } 
/*      */ 
/*      */ 
/*      */ 
/*      */                                                     
/* 1570 */                                                     String str17 = Util.null2String(arrayList5.get(b1));
/*      */                                                     
/* 1572 */                                                     boolean bool7 = true;
/* 1573 */                                                     if (list.contains(str17)) {
/* 1574 */                                                       if ("FnaCostCenter".equalsIgnoreCase(str10.toLowerCase())) {
/* 1575 */                                                         bool7 = false;
/*      */                                                         
/* 1577 */                                                         Errmsg.append(" " + SystemEnv.getHtmlLabelName(383993, user.getLanguage()).replace("#impKey#", str17) + _br);
/* 1578 */                                                         str6 = "";
/* 1579 */                                                       } else if (n == 22) {
/* 1580 */                                                         bool7 = false;
/*      */                                                         
/* 1582 */                                                         Errmsg.append(" " + SystemEnv.getHtmlLabelName(383995, user.getLanguage()).replace("#impKey#", str17) + _br);
/* 1583 */                                                         str6 = "";
/*      */                                                       }
/*      */                                                     
/* 1586 */                                                     } else if (hashMap.containsKey(str17)) {
/* 1587 */                                                       String str18 = (String)hashMap.get(str17);
/* 1588 */                                                       if (str6.equals("")) {
/* 1589 */                                                         str6 = str18;
/*      */                                                       } else {
/* 1591 */                                                         str6 = str6 + "," + str18;
/*      */                                                       }
/*      */                                                     
/* 1594 */                                                     } else if ("FnaCostCenter".equalsIgnoreCase(str10.toLowerCase())) {
/* 1595 */                                                       bool7 = false;
/*      */                                                       
/* 1597 */                                                       Errmsg.append(" " + SystemEnv.getHtmlLabelName(383996, user.getLanguage()).replace("#impKey#", str17) + _br);
/* 1598 */                                                       str6 = "";
/* 1599 */                                                     } else if (n == 22) {
/* 1600 */                                                       bool7 = false;
/*      */                                                       
/* 1602 */                                                       Errmsg.append(" " + SystemEnv.getHtmlLabelName(383997, user.getLanguage()).replace("#impKey#", str17) + _br);
/* 1603 */                                                       str6 = "";
/*      */ 
/*      */ 
/*      */ 
/*      */                                                     
/*      */                                                     }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */                                                   
/*      */                                                   }
/*      */                                                   else {
/*      */ 
/*      */ 
/*      */ 
/*      */                                                     
/* 1620 */                                                     String str17 = str10;
/* 1621 */                                                     String str18 = str9;
/* 1622 */                                                     String str19 = str11;
/* 1623 */                                                     String str20 = ((String)arrayList5.get(b1)).trim();
/*      */ 
/*      */                                                     
/* 1626 */                                                     String str21 = " and (canceled=0 or canceled is null)  ORDER BY " + str9 + " DESC";
/* 1627 */                                                     str12 = RequestDetailImport.this.forMultiLangSql(str17, str18, str19, str20, str21);
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */                                                     
/* 1640 */                                                     str21 = " and canceled=1 ORDER BY " + str9 + " DESC";
/* 1641 */                                                     str13 = RequestDetailImport.this.forMultiLangSql(str17, str18, str19, str20, str21);
/*      */                                                     
/* 1643 */                                                     if ("FnaCostCenter".equalsIgnoreCase(str10.toLowerCase())) {
/* 1644 */                                                       if (wfDtlImpRole4Fcc == 1) {
/* 1645 */                                                         str11 = "code";
/*      */                                                       }
/* 1647 */                                                       str17 = str10;
/* 1648 */                                                       str18 = str9;
/* 1649 */                                                       str19 = str11;
/* 1650 */                                                       str20 = StringEscapeUtils.escapeSql(((String)arrayList5.get(b1)).trim());
/* 1651 */                                                       str21 = " and (Archive is null or Archive=0) and type=1  ORDER BY " + str9 + " DESC";
/* 1652 */                                                       str14 = RequestDetailImport.this.forMultiLangSql(str17, str18, str19, str20, str21);
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */                                                       
/* 1667 */                                                       str17 = str10;
/* 1668 */                                                       str18 = "count(*) cnt";
/* 1669 */                                                       str19 = str11;
/* 1670 */                                                       str20 = StringEscapeUtils.escapeSql(((String)arrayList5.get(b1)).trim());
/* 1671 */                                                       str21 = " and (Archive is null or Archive=0) and type=1 ";
/* 1672 */                                                       str15 = RequestDetailImport.this.forMultiLangSql(str17, str18, str19, str20, str21);
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */                                                     
/*      */                                                     }
/* 1685 */                                                     else if (n == 22) {
/* 1686 */                                                       if (wfDtlImpRole4Subject == 1) {
/* 1687 */                                                         str11 = "codeName";
/*      */                                                       }
/*      */                                                       
/* 1690 */                                                       String str22 = "";
/* 1691 */                                                       if ("change".equals(fnaWfType) || (finalIsbill == 1 && finalFormid == 159)) {
/* 1692 */                                                         str22 = str22 + " FnaBudgetfeeType.isEditFeeType = 1 and ";
/*      */                                                       }
/* 1694 */                                                       else if (optionalSubject == 1) {
/* 1695 */                                                         str22 = str22 + " not EXISTS (select 1 from FnaBudgetfeeType a1 where a1.supsubject = FnaBudgetfeeType.id) and ";
/*      */                                                       } 
/*      */                                                       
/* 1698 */                                                       str17 = str10;
/* 1699 */                                                       str18 = str9;
/* 1700 */                                                       str19 = str11;
/* 1701 */                                                       str20 = StringEscapeUtils.escapeSql(((String)arrayList5.get(b1)).trim());
/* 1702 */                                                       str21 = " and " + str22 + " (Archive is null or Archive=0) and ISEDITFEETYPEID > 0  ORDER BY " + str9 + " DESC";
/* 1703 */                                                       str16 = RequestDetailImport.this.forMultiLangSql(str17, str18, str19, str20, str21);
/*      */                                                     } 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */                                                     
/* 1721 */                                                     str17 = str10;
/* 1722 */                                                     str18 = str9;
/* 1723 */                                                     str19 = str11;
/* 1724 */                                                     str20 = StringEscapeUtils.escapeSql(((String)arrayList5.get(b1)).trim());
/* 1725 */                                                     str21 = " ORDER BY " + str9 + " DESC";
/*      */                                                     
/* 1727 */                                                     if (n == 23 || n == 179) {
/* 1728 */                                                       if (Util.isEnableMultiLang()) {
/*      */                                                         
/* 1730 */                                                         String str22 = "";
/* 1731 */                                                         if (RequestDetailImport.this.currentUserLang > 9) {
/* 1732 */                                                           str22 = RequestDetailImport.this.currentUserLang + "";
/*      */                                                         } else {
/* 1734 */                                                           str22 = RequestDetailImport.this.currentUserLang + " ";
/*      */                                                         } 
/*      */                                                         
/* 1737 */                                                         if (n == 23) {
/* 1738 */                                                           sql[0] = "SELECT " + str18 + " FROM " + str17 + " WHERE isdata =2 and (mark ='" + str20 + "'  or mark like '%`~`" + str22 + str20 + "`~`%')";
/*      */ 
/*      */ 
/*      */                                                         
/*      */                                                         }
/*      */                                                         else {
/*      */ 
/*      */ 
/*      */ 
/*      */                                                           
/* 1748 */                                                           sql[0] = "SELECT " + str18 + " FROM " + str17 + " WHERE isdata =1 and (mark ='" + str20 + "'  or mark like '%`~`" + str22 + str20 + "`~`%')";
/*      */ 
/*      */ 
/*      */ 
/*      */                                                         
/*      */                                                         }
/*      */ 
/*      */ 
/*      */ 
/*      */                                                       
/*      */                                                       }
/* 1759 */                                                       else if (n == 23) {
/* 1760 */                                                         sql[0] = "SELECT " + str18 + " FROM " + str17 + " WHERE isdata=2 and mark='" + str20 + "' ";
/*      */ 
/*      */ 
/*      */                                                       
/*      */                                                       }
/*      */                                                       else {
/*      */ 
/*      */ 
/*      */                                                         
/* 1769 */                                                         sql[0] = "SELECT " + str18 + " FROM " + str17 + " WHERE isdata=1 and mark='" + str20 + "' ";
/*      */                                                       } 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */                                                       
/* 1780 */                                                       if (str21 != null && !str21.equals("")) {
/* 1781 */                                                         sql[0] = sql[0] + str21;
/*      */                                                       }
/*      */                                                     } else {
/*      */                                                       
/* 1785 */                                                       sql[0] = hrmImport.querySqlByNameAndId(str17, str18, str19, str20, str21);
/*      */                                                     } 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */                                                     
/* 1816 */                                                     boolean bool7 = true;
/* 1817 */                                                     if ("hrmdepartmentallview".equals(str10.toLowerCase()) || "hrmsubcompanyallview".equals(str10.toLowerCase())) {
/* 1818 */                                                       recordSet.executeSql(str12);
/*      */                                                       
/* 1820 */                                                       if (recordSet.getCounts() >= 1) {
/*      */                                                         
/* 1822 */                                                         recordSet.executeSql(str12);
/*      */                                                       } else {
/*      */                                                         
/* 1825 */                                                         recordSet.executeSql(str13);
/*      */                                                       } 
/* 1827 */                                                     } else if ("FnaCostCenter".equalsIgnoreCase(str10.toLowerCase())) {
/* 1828 */                                                       int i4 = 0;
/* 1829 */                                                       recordSet.executeQuery(str15, new Object[0]);
/* 1830 */                                                       if (recordSet.next()) {
/* 1831 */                                                         i4 = recordSet.getInt("cnt");
/*      */                                                       }
/* 1833 */                                                       String str22 = ((String)arrayList5.get(b1)).trim();
/* 1834 */                                                       if (i4 < 1) {
/* 1835 */                                                         bool7 = false;
/*      */                                                         
/* 1837 */                                                         Errmsg.append(" " + SystemEnv.getHtmlLabelName(383996, user.getLanguage()).replace("#impKey#", str22) + _br);
/* 1838 */                                                         str6 = "";
/* 1839 */                                                       } else if (i4 > 1) {
/* 1840 */                                                         bool7 = false;
/*      */                                                         
/* 1842 */                                                         Errmsg.append(" " + SystemEnv.getHtmlLabelName(383993, user.getLanguage()).replace("#impKey#", str22) + _br);
/* 1843 */                                                         str6 = "";
/*      */                                                       } 
/* 1845 */                                                       recordSet.executeSql(str14);
/* 1846 */                                                     } else if (n == 22) {
/* 1847 */                                                       recordSet.executeSql(str16);
/*      */                                                     } else {
/* 1849 */                                                       recordSet.executeQuery(sql[0], new Object[0]);
/*      */                                                     } 
/* 1851 */                                                     if (bool7 && recordSet.next()) {
/* 1852 */                                                       if (str6.equals("")) {
/* 1853 */                                                         str6 = recordSet.getString(1);
/*      */                                                       } else {
/* 1855 */                                                         str6 = str6 + "," + recordSet.getString(1);
/*      */                                                       } 
/*      */                                                     } else {
/* 1858 */                                                       hashMap1.put("col", (new StringBuilder()).append(hashMap1.get("col")).append(RequestDetailImport.this.getColChar(((Integer)finalImportFields.get(str)).intValue())).append(";").toString());
/* 1859 */                                                       hashMap1.put("info", (new StringBuilder()).append(hashMap1.get("info")).append("“").append(str20).append("”").append(SystemEnv.getHtmlLabelName(530134, user.getLanguage())).append(";").toString());
/* 1860 */                                                       bool1 = true;
/* 1861 */                                                       errorCell.add((finalI + 1) + "," + i + "," + (b + 1));
/* 1862 */                                                       bool4 = true;
/*      */                                                     } 
/*      */                                                   } 
/*      */                                                 } 
/*      */                                               } 
/*      */                                             } else {
/*      */                                               
/* 1869 */                                               str6 = "";
/*      */                                             } 
/*      */                                           } 
/*      */ 
/*      */ 
/*      */                                           
/* 1875 */                                           if (n == 22) {
/* 1876 */                                             BudgetfeeTypeComInfo budgetfeeTypeComInfo = new BudgetfeeTypeComInfo();
/*      */                                             
/* 1878 */                                             ArrayList arrayList5 = new ArrayList();
/* 1879 */                                             budgetfeeTypeComInfo.getWfBrowdefListByFeelevel(finalWorkflowid + "", m + "", "22", arrayList5);
/* 1880 */                                             boolean bool7 = true;
/* 1881 */                                             if (arrayList5.size() == 0) {
/* 1882 */                                               bool7 = false;
/*      */                                             }
/* 1884 */                                             ArrayList arrayList6 = new ArrayList();
/* 1885 */                                             if (bool7) {
/* 1886 */                                               budgetfeeTypeComInfo.getWfBrowdefListByFeelevel_canSelect(finalWorkflowid + "", m + "", "22", arrayList6);
/*      */                                             }
/*      */                                             
/* 1889 */                                             if (bool7 && !arrayList6.contains(str6)) {
/* 1890 */                                               str6 = "";
/*      */                                             }
/*      */                                           } 
/*      */ 
/*      */                                           
/* 1895 */                                           if (n == 17 && recordSet.getDBType().equals("oracle"))
/* 1896 */                                           { hashMap2.put(fieldnames.get(b), str6);
/* 1897 */                                             str6 = " "; } 
/*      */                                         }  } 
/*      */                                     } 
/* 1900 */                                   } else if (i1 == 4) {
/* 1901 */                                     if (!"".equalsIgnoreCase(str6)) {
/* 1902 */                                       if ("1".equalsIgnoreCase(str6) || str6.toLowerCase().equals(SystemEnv.getHtmlLabelName(25104, user.getLanguage()).toLowerCase())) {
/* 1903 */                                         str6 = "1";
/* 1904 */                                       } else if ("0".equalsIgnoreCase(str6) || str6.toLowerCase().equals(SystemEnv.getHtmlLabelName(25105, user.getLanguage()).toLowerCase())) {
/* 1905 */                                         str6 = "0";
/*      */                                       } else {
/* 1907 */                                         str6 = "";
/*      */                                       } 
/*      */                                     }
/* 1910 */                                   } else if (i1 == 5) {
/*      */                                     
/* 1912 */                                     if (n == 2) {
/* 1913 */                                       String str7 = str6;
/* 1914 */                                       boolean bool7 = false;
/* 1915 */                                       ArrayList<String> arrayList5 = Util.TokenizerString(str6, ",");
/* 1916 */                                       str6 = "";
/* 1917 */                                       for (byte b1 = 0; b1 < arrayList5.size(); b1++) {
/* 1918 */                                         if (((String)arrayList5.get(b1)).length() > 0) {
/*      */ 
/*      */                                           
/* 1921 */                                           String str8 = "workflow_selectitem";
/* 1922 */                                           String str9 = "selectvalue";
/* 1923 */                                           String str10 = "selectname";
/* 1924 */                                           String str11 = arrayList5.get(b1);
/* 1925 */                                           String str12 = " and fieldid=" + ((String)arrayList4.get(0)).substring(5);
/* 1926 */                                           sql[0] = RequestDetailImport.this.forMultiLangSql(str8, str9, str10, str11, str12);
/* 1927 */                                           recordSet.executeSql(sql[0]);
/* 1928 */                                           if (recordSet.next()) {
/* 1929 */                                             if (finalIsmode.equals("1")) {
/* 1930 */                                               str6 = recordSet.getString(1);
/*      */                                               break;
/*      */                                             } 
/* 1933 */                                             if (str6.equals("")) {
/* 1934 */                                               str6 = recordSet.getString(1);
/*      */                                             } else {
/* 1936 */                                               str6 = str6 + "," + recordSet.getString(1);
/*      */                                             } 
/*      */                                           } else {
/*      */                                             
/* 1940 */                                             RequestDetailImport.this.wfLog.error("流程明细导入失败，Excel中复选框值无法在系统内匹配：sql=" + sql[0]);
/* 1941 */                                             hashMap1.put("col", (new StringBuilder()).append(hashMap1.get("col")).append(RequestDetailImport.this.getColChar(((Integer)finalImportFields.get(str)).intValue())).append(";").toString());
/* 1942 */                                             hashMap1.put("info", (new StringBuilder()).append(hashMap1.get("info")).append("“").append(str6).append("”").append(SystemEnv.getHtmlLabelName(530134, user.getLanguage())).append(";").toString());
/* 1943 */                                             bool1 = true;
/* 1944 */                                             errorCell.add((finalI + 1) + "," + i + "," + (b + 1));
/* 1945 */                                             bool7 = true;
/*      */                                           } 
/*      */                                         } 
/* 1948 */                                       }  if (str6.equals("") && !str7.trim().equals("") && !bool7) {
/* 1949 */                                         RequestDetailImport.this.wfLog.error("流程明细导入失败，Excel中复选框值为：" + str6 + "tempvalueBak：" + str7);
/*      */                                         
/* 1951 */                                         hashMap1.put("col", (new StringBuilder()).append(hashMap1.get("col")).append(RequestDetailImport.this.getColChar(((Integer)finalImportFields.get(str)).intValue())).append(";").toString());
/* 1952 */                                         hashMap1.put("info", (new StringBuilder()).append(hashMap1.get("info")).append("“").append(str6).append("”").append(SystemEnv.getHtmlLabelName(530134, user.getLanguage())).append(";").toString());
/* 1953 */                                         errorCell.add((finalI + 1) + "," + i + "," + (b + 1));
/*      */                                       } 
/*      */                                     } else {
/* 1956 */                                       String str7 = "workflow_selectitem";
/* 1957 */                                       String str8 = "selectvalue";
/* 1958 */                                       String str9 = "selectname";
/* 1959 */                                       String str10 = str6;
/* 1960 */                                       String str11 = "  and cancel=0  and fieldid=" + ((String)arrayList4.get(0)).substring(5);
/* 1961 */                                       sql[0] = RequestDetailImport.this.forMultiLangSql(str7, str8, str9, str10, str11);
/* 1962 */                                       recordSet.executeSql(sql[0]);
/* 1963 */                                       if (recordSet.next()) {
/* 1964 */                                         str6 = recordSet.getString(1);
/*      */                                       } else {
/* 1966 */                                         (new BaseBean()).writeLog("流程明细导入失败，Excel中选择框值无法在系统内匹配：sql=" + sql[0]);
/* 1967 */                                         RequestDetailImport.this.wfLog.error("流程明细导入失败，Excel中选择框值无法在系统内匹配：sql=" + sql[0]);
/* 1968 */                                         hashMap1.put("col", (new StringBuilder()).append(hashMap1.get("col")).append(RequestDetailImport.this.getColChar(((Integer)finalImportFields.get(str)).intValue())).append(";").toString());
/* 1969 */                                         hashMap1.put("info", (new StringBuilder()).append(hashMap1.get("info")).append("“").append(str6).append("”").append(SystemEnv.getHtmlLabelName(530134, user.getLanguage())).append(";").toString());
/* 1970 */                                         bool1 = true;
/* 1971 */                                         str6 = "";
/* 1972 */                                         errorCell.add((finalI + 1) + "," + i + "," + (b + 1));
/*      */                                       } 
/*      */                                     } 
/*      */                                   } 
/* 1976 */                                   RecordSet recordSet1 = new RecordSet();
/* 1977 */                                   if (i1 == 1 && n == 5) {
/* 1978 */                                     int i2 = 2;
/* 1979 */                                     if (finalIsbill == 0) {
/* 1980 */                                       recordSet1.executeSql("select fielddbtype,qfws  from workflow_formdictdetail where id=" + ((String)arrayList4.get(0)).substring(5));
/*      */                                     } else {
/* 1982 */                                       recordSet1.executeSql("select fielddbtype,qfws from workflow_billfield where id=" + ((String)arrayList4.get(0)).substring(5));
/*      */                                     } 
/* 1984 */                                     if (recordSet1.next()) {
/* 1985 */                                       i2 = Util.getIntValue(recordSet1.getString("qfws"), 2);
/*      */                                     }
/*      */                                     
/* 1988 */                                     if (!str6.equals("")) {
/* 1989 */                                       str6 = str6.replace(",", "");
/* 1990 */                                       BigDecimal bigDecimal = null;
/*      */                                       try {
/* 1992 */                                         bigDecimal = new BigDecimal(str6);
/* 1993 */                                         double d = bigDecimal.setScale(i2, 4).doubleValue();
/* 1994 */                                         String str7 = "0.";
/* 1995 */                                         for (byte b1 = 0; b1 < i2; b1++) {
/* 1996 */                                           str7 = str7 + "0";
/*      */                                         }
/* 1998 */                                         DecimalFormat decimalFormat = new DecimalFormat(str7);
/* 1999 */                                         str6 = decimalFormat.format(d);
/* 2000 */                                         str6 = Util.milfloatFormat(str6);
/* 2001 */                                       } catch (Exception exception) {
/* 2002 */                                         hashMap1.put("col", (new StringBuilder()).append(hashMap1.get("col")).append(RequestDetailImport.this.getColChar(((Integer)finalImportFields.get(str)).intValue())).append(";").toString());
/* 2003 */                                         hashMap1.put("info", (new StringBuilder()).append(hashMap1.get("info")).append("“").append(str6).append("”").append(SystemEnv.getHtmlLabelName(530133, user.getLanguage())).append(";").toString());
/* 2004 */                                         bool1 = true;
/* 2005 */                                         str6 = null;
/*      */                                       } 
/*      */                                     } 
/*      */                                   } 
/*      */ 
/*      */                                   
/* 2011 */                                   if (!"".equals(str6))
/*      */                                   {
/* 2013 */                                     if (null == str6 && i1 == 3 && (n == 168 || n == 57)) {
/* 2014 */                                       str6 = "";
/*      */                                     }
/* 2016 */                                     hashMap3.put("$" + ((finalIsbill == 1) ? finalDetailtable : "detail") + "_" + str + "$", str6);
/* 2017 */                                     if (j > 0 && j <= oldRowSize) {
/*      */                                       
/* 2019 */                                       if ("".equals(str1)) {
/* 2020 */                                         str1 = "UPDATE " + finalDetailtable + " SET ";
/* 2021 */                                         if (stringBuilder3.length() == 0) {
/* 2022 */                                           stringBuilder3.append("UPDATE " + finalDetailtable + " SET ");
/*      */                                         }
/* 2024 */                                         String str7 = "";
/* 2025 */                                         List<String> list = RequestDetailImport.this.getPKCondition(fi, finalMaintable, finalDetailtable, finalFormid, finalIsbill, detailtableidsTemp.get(finalI), j - 1);
/*      */                                         
/* 2027 */                                         str2 = " WHERE " + (String)list.get(0) + "=" + (String)list.get(1);
/* 2028 */                                         stringBuilder4.append(" WHERE " + (String)list.get(0) + "=?");
/* 2029 */                                         arrayList2.add(list.get(1));
/* 2030 */                                         if (finalIsbill == 1) {
/* 2031 */                                           str2 = str2 + " AND " + finalDetailtablekey + "=" + finalMainid;
/* 2032 */                                           stringBuilder4.append(" AND " + finalDetailtablekey + "= ?");
/* 2033 */                                           arrayList2.add(Integer.valueOf(finalMainid));
/* 2034 */                                           list = RequestDetailImport.this.getPKCondition(fi, finalMaintable, finalDetailtable, finalFormid, finalIsbill, detailtableidsTemp.get(finalI), j - 1);
/* 2035 */                                           str7 = (String)list.get(0) + "=" + (String)list.get(1) + " AND " + finalDetailtablekey + "=" + finalMainid;
/* 2036 */                                           str3 = " WHERE " + str7;
/*      */                                         } else {
/* 2038 */                                           str2 = str2 + " AND requestid=" + finalRequestid + " AND groupid=" + finalI;
/* 2039 */                                           stringBuilder4.append(" AND requestid= ? AND groupid = ?");
/* 2040 */                                           arrayList2.add(Integer.valueOf(finalRequestid));
/* 2041 */                                           arrayList2.add(Integer.valueOf(finalI));
/* 2042 */                                           list = RequestDetailImport.this.getPKCondition(fi, finalMaintable, finalDetailtable, finalFormid, finalIsbill, detailtableidsTemp.get(finalI), j - 1);
/* 2043 */                                           str7 = (String)list.get(0) + "=" + (String)list.get(1) + " AND requestid=" + finalRequestid + " AND groupid=" + finalI;
/* 2044 */                                           str3 = " WHERE " + str7;
/*      */                                         } 
/* 2046 */                                         if (!bool) {
/* 2047 */                                           str1 = str1 + fieldnames.get(b) + "=";
/* 2048 */                                           if (stringBuilder3.toString().endsWith("SET ")) {
/* 2049 */                                             stringBuilder3.append((new StringBuilder()).append(fieldnames.get(b)).append("=?").toString());
/*      */                                           } else {
/* 2051 */                                             stringBuilder3.append("," + fieldnames.get(b) + "=?");
/*      */                                           } 
/* 2053 */                                           if ((((String)fieldbdtypes.get(b)).toLowerCase().contains("int") || ((String)fieldbdtypes
/* 2054 */                                             .get(b)).toLowerCase().contains("number") || ((String)fieldbdtypes
/* 2055 */                                             .get(b)).toLowerCase().contains("decimal") || ((String)fieldbdtypes
/* 2056 */                                             .get(b)).toLowerCase().contains("browser.")) && n != 226) {
/* 2057 */                                             str1 = str1 + "'" + str6 + "'";
/*      */                                           } else {
/* 2059 */                                             str1 = str1 + "'" + str6 + "'";
/*      */                                           } 
/* 2061 */                                           arrayList.add(str6);
/* 2062 */                                           bool5 = false;
/*      */                                         }
/*      */                                       
/* 2065 */                                       } else if (!bool) {
/* 2066 */                                         if (bool5) {
/* 2067 */                                           str1 = str1 + fieldnames.get(b) + "=";
/* 2068 */                                           if (stringBuilder3.toString().endsWith("SET ")) {
/* 2069 */                                             stringBuilder3.append((new StringBuilder()).append(fieldnames.get(b)).append("=?").toString());
/*      */                                           } else {
/* 2071 */                                             stringBuilder3.append("," + fieldnames.get(b) + "=?");
/*      */                                           } 
/* 2073 */                                           bool5 = false;
/*      */                                         } else {
/* 2075 */                                           str1 = str1 + "," + fieldnames.get(b) + "=";
/* 2076 */                                           if (stringBuilder3.toString().endsWith("SET ")) {
/* 2077 */                                             stringBuilder3.append((new StringBuilder()).append(fieldnames.get(b)).append("=?").toString());
/*      */                                           } else {
/* 2079 */                                             stringBuilder3.append("," + fieldnames.get(b) + "=?");
/*      */                                           } 
/*      */                                         } 
/*      */                                         
/* 2083 */                                         arrayList.add(str6);
/* 2084 */                                         if ((((String)fieldbdtypes.get(b)).toLowerCase().contains("int") || ((String)fieldbdtypes
/* 2085 */                                           .get(b)).toLowerCase().contains("number") || ((String)fieldbdtypes
/* 2086 */                                           .get(b)).toLowerCase().contains("decimal") || ((String)fieldbdtypes
/* 2087 */                                           .get(b)).toLowerCase().contains("browser.")) && n != 226) {
/* 2088 */                                           Pattern pattern = Pattern.compile("^(-?\\d+)(\\.\\d+)?$");
/* 2089 */                                           if (null != str6) {
/* 2090 */                                             Matcher matcher = pattern.matcher(str6);
/* 2091 */                                             if (!matcher.matches()) {
/* 2092 */                                               hashMap1.put("col", (new StringBuilder()).append(hashMap1.get("col")).append(RequestDetailImport.this.getColChar(((Integer)finalImportFields.get(str)).intValue())).append(";").toString());
/* 2093 */                                               hashMap1.put("info", (new StringBuilder()).append(hashMap1.get("info")).append("“").append(str6).append("”").append(SystemEnv.getHtmlLabelName(530133, user.getLanguage())).append(";").toString());
/* 2094 */                                               bool1 = true;
/* 2095 */                                               str6 = null;
/*      */                                             } 
/*      */                                           } 
/* 2098 */                                           str1 = str1 + str6;
/*      */                                         } else {
/* 2100 */                                           str1 = str1 + "'" + str6 + "'";
/*      */                                         }
/*      */                                       
/*      */                                       } 
/*      */                                     } else {
/*      */                                       
/* 2106 */                                       bool5 = false;
/*      */                                       
/* 2108 */                                       if ("".equals(str1)) {
/* 2109 */                                         str1 = "INSERT INTO " + finalDetailtable + "(";
/* 2110 */                                         str2 = " VALUES(";
/* 2111 */                                         stringBuilder1 = new StringBuilder("INSERT INTO " + finalDetailtable + "(");
/* 2112 */                                         stringBuilder2 = new StringBuilder(" VALUES(");
/* 2113 */                                         if (finalIsbill == 1) {
/* 2114 */                                           str1 = str1 + finalDetailtablekey;
/* 2115 */                                           str2 = str2 + finalMainid;
/* 2116 */                                           stringBuilder1.append(finalDetailtablekey + ",");
/* 2117 */                                           stringBuilder2.append("?,");
/*      */                                           
/* 2119 */                                           arrayList1.add(null);
/* 2120 */                                           arrayList1.set(0, Integer.valueOf(finalMainid));
/* 2121 */                                           k++;
/*      */                                         } else {
/* 2123 */                                           str1 = str1 + "requestid,groupid";
/* 2124 */                                           str2 = str2 + finalRequestid + "," + finalI;
/* 2125 */                                           stringBuilder1.append("requestid,groupid,");
/* 2126 */                                           stringBuilder2.append("?,?,");
/* 2127 */                                           arrayList1.add(null);
/* 2128 */                                           arrayList1.add(null);
/* 2129 */                                           arrayList1.set(0, Integer.valueOf(finalRequestid));
/* 2130 */                                           arrayList1.set(1, Integer.valueOf(olderDetailId));
/* 2131 */                                           k += k + 2;
/*      */                                         } 
/* 2133 */                                         for (byte b1 = 0; b1 < fieldnames.size(); b1++) {
/* 2134 */                                           stringBuilder1.append((new StringBuilder()).append(fieldnames.get(b1)).append(",").toString());
/* 2135 */                                           stringBuilder2.append("?,");
/* 2136 */                                           arrayList1.add(null);
/*      */                                         } 
/* 2138 */                                         if (!bool) {
/* 2139 */                                           str1 = str1 + "," + fieldnames.get(b);
/* 2140 */                                           if ((((String)fieldbdtypes.get(b)).toLowerCase().contains("int") || ((String)fieldbdtypes
/* 2141 */                                             .get(b)).toLowerCase().contains("number") || ((String)fieldbdtypes
/* 2142 */                                             .get(b)).toLowerCase().contains("decimal") || ((String)fieldbdtypes
/* 2143 */                                             .get(b)).toLowerCase().contains("browser.")) && n != 226) {
/* 2144 */                                             Pattern pattern = Pattern.compile("^(-?\\d+)(\\.\\d+)?$");
/* 2145 */                                             if (null != str6) {
/* 2146 */                                               Matcher matcher = pattern.matcher(str6);
/* 2147 */                                               if (!matcher.matches()) {
/* 2148 */                                                 hashMap1.put("col", (new StringBuilder()).append(hashMap1.get("col")).append(RequestDetailImport.this.getColChar(((Integer)finalImportFields.get(str)).intValue())).append(";").toString());
/* 2149 */                                                 hashMap1.put("info", (new StringBuilder()).append(hashMap1.get("info")).append("“").append(str6).append("”").append(SystemEnv.getHtmlLabelName(530133, user.getLanguage())).append(";").toString());
/* 2150 */                                                 bool1 = true;
/* 2151 */                                                 str6 = null;
/*      */                                               } 
/*      */                                             } 
/*      */                                             
/* 2155 */                                             str2 = str2 + "," + str6 + "";
/* 2156 */                                             arrayList1.set(b + k, str6);
/*      */                                           } else {
/* 2158 */                                             str2 = str2 + ",'" + str6 + "'";
/* 2159 */                                             arrayList1.set(b + k, str6);
/*      */                                           }
/*      */                                         
/*      */                                         } 
/* 2163 */                                       } else if (!bool) {
/* 2164 */                                         str1 = str1 + "," + fieldnames.get(b);
/* 2165 */                                         if ((((String)fieldbdtypes.get(b)).toLowerCase().contains("int") || ((String)fieldbdtypes
/* 2166 */                                           .get(b)).toLowerCase().contains("number") || ((String)fieldbdtypes
/* 2167 */                                           .get(b)).toLowerCase().contains("decimal") || ((String)fieldbdtypes
/* 2168 */                                           .get(b)).toLowerCase().contains("browser.")) && n != 226) {
/* 2169 */                                           Pattern pattern = Pattern.compile("^(-?\\d+)(\\.\\d+)?$");
/*      */                                           
/* 2171 */                                           if (null != str6) {
/* 2172 */                                             Matcher matcher = pattern.matcher(str6);
/* 2173 */                                             if (!matcher.matches()) {
/* 2174 */                                               hashMap1.put("col", (new StringBuilder()).append(hashMap1.get("col")).append(RequestDetailImport.this.getColChar(((Integer)finalImportFields.get(str)).intValue())).append(";").toString());
/* 2175 */                                               hashMap1.put("info", (new StringBuilder()).append(hashMap1.get("info")).append("“").append(str6).append("”").append(SystemEnv.getHtmlLabelName(530133, user.getLanguage())).append(";").toString());
/* 2176 */                                               bool1 = true;
/* 2177 */                                               str6 = null;
/*      */                                             } 
/*      */                                           } 
/* 2180 */                                           str2 = str2 + "," + str6 + "";
/* 2181 */                                           arrayList1.set(b + k, str6);
/*      */                                         } else {
/* 2183 */                                           str2 = str2 + ",'" + str6 + "'";
/* 2184 */                                           arrayList1.set(b + k, str6);
/*      */                                         }
/*      */                                       
/*      */                                       }
/*      */                                     
/*      */                                     }
/*      */                                   
/*      */                                   }
/*      */                                 
/*      */                                 }
/*      */                                 else {
/*      */                                   
/*      */                                   break;
/*      */                                 } 
/* 2198 */                               } else if (j > 0 && j <= oldRowSize && !bool) {
/* 2199 */                                 if (stringBuilder3.length() == 0) {
/* 2200 */                                   stringBuilder3.append("UPDATE " + finalDetailtable + " SET ");
/* 2201 */                                   stringBuilder3.append("" + fieldnames.get(b) + "=?");
/*      */                                 } else {
/* 2203 */                                   stringBuilder3.append("," + fieldnames.get(b) + "=?");
/*      */                                 } 
/*      */                                 
/* 2206 */                                 arrayList.add(null);
/*      */                               } 
/*      */                             } 
/*      */                           } 
/* 2210 */                           System.out.println("hasPermission=" + bool6 + ",upBeforeSql=" + stringBuilder3 + ",updateParam=" + arrayList);
/* 2211 */                           if (bool6) {
/* 2212 */                             if (!"".equals(str1)) {
/* 2213 */                               hashMap3.putAll((Map<?, ?>)RequestDetailImport.this.getTableFieldValueMap(String.valueOf(requestid), finalDetailtable, j).get("detailValueMap"));
/* 2214 */                               Map map = cusBrowserService.getBrowerData(requestFormInfoMap, hashMap3, user, finalDetailtable, arrayList3);
/* 2215 */                               if (map != null && arrayList3 != null && arrayList3.size() != map.size()) {
/* 2216 */                                 RequestDetailImport.this.writeLog(" RequestDetailImport  自定义浏览框数据 start------------------------requestid:" + finalRequestid);
/* 2217 */                                 RequestDetailImport.this.writeLog("customBrowserFieldInfo:" + arrayList3.toString());
/* 2218 */                                 RequestDetailImport.this.writeLog("curBrowserValues:" + map.toString());
/* 2219 */                                 RequestDetailImport.this.writeLog(" RequestDetailImport  自定义浏览框数据 end------------------------");
/*      */                               } 
/* 2221 */                               if (j > 0 && j <= oldRowSize) {
/*      */ 
/*      */                                 
/* 2224 */                                 for (byte b1 = 0; b1 < fieldnames.size(); b1++) {
/* 2225 */                                   String str = Util.null2String(fieldnames.get(b1));
/*      */                                   
/* 2227 */                                   for (Map.Entry entry : map.entrySet()) {
/* 2228 */                                     String str4 = (String)entry.getKey();
/* 2229 */                                     Object object = entry.getValue();
/* 2230 */                                     if (str.equalsIgnoreCase(str4)) {
/* 2231 */                                       System.out.println("浏览按钮key=" + (String)entry.getKey() + ",value=" + entry.getValue());
/* 2232 */                                       if (bool5) {
/* 2233 */                                         str1 = str1 + (String)entry.getKey() + " = '" + entry.getValue() + "' ";
/*      */                                         
/* 2235 */                                         stringBuilder3.append((String)entry.getKey() + " = ?");
/* 2236 */                                         bool5 = false;
/*      */                                       } else {
/* 2238 */                                         str1 = str1 + "," + (String)entry.getKey() + " = '" + entry.getValue() + "' ";
/* 2239 */                                         stringBuilder3.append("," + (String)entry.getKey() + " = ?");
/*      */                                       } 
/* 2241 */                                       arrayList.add((String)entry.getValue());
/*      */ 
/*      */                                       
/*      */                                       break;
/*      */                                     } 
/*      */                                   } 
/*      */                                 } 
/*      */                               } else {
/* 2249 */                                 boolean bool = true;
/* 2250 */                                 for (Map.Entry entry : map.entrySet()) {
/* 2251 */                                   str1 = str1 + "," + (String)entry.getKey();
/* 2252 */                                   str2 = str2 + ",'" + entry.getValue() + "'";
/*      */ 
/*      */                                   
/* 2255 */                                   for (byte b2 = 0; b2 < fieldnames.size(); b2++) {
/* 2256 */                                     if (fieldnames.get(b2).equals(entry.getKey())) {
/* 2257 */                                       arrayList1.set(b2 + 1, (Integer)entry.getValue());
/*      */                                       
/*      */                                       break;
/*      */                                     } 
/*      */                                   } 
/*      */                                 } 
/* 2263 */                                 for (byte b1 = 0; b1 < viewfieldids.size(); b1++) {
/* 2264 */                                   ArrayList<String> arrayList4 = Util.TokenizerString(viewfieldids.get(b1), "_");
/* 2265 */                                   String str = "";
/* 2266 */                                   int m = Util.getIntValue(arrayList4.get(2));
/* 2267 */                                   int n = Util.getIntValue(arrayList4.get(3));
/* 2268 */                                   if (n == 3) {
/* 2269 */                                     if (m == 1 || m == 165 || m == 17 || m == 166) {
/* 2270 */                                       str = "" + user.getUID();
/* 2271 */                                     } else if (m == 2) {
/* 2272 */                                       str = TimeUtil.getCurrentDateString();
/* 2273 */                                     } else if (m == 19) {
/*      */ 
/*      */                                       
/* 2276 */                                       String str4 = "HH:mm";
/* 2277 */                                       SimpleDateFormat simpleDateFormat = new SimpleDateFormat(str4);
/* 2278 */                                       Calendar calendar = Calendar.getInstance();
/* 2279 */                                       str = simpleDateFormat.format(calendar.getTime());
/* 2280 */                                     } else if (m == 290) {
/* 2281 */                                       String str4 = "HH:mm";
/* 2282 */                                       SimpleDateFormat simpleDateFormat = new SimpleDateFormat(str4);
/* 2283 */                                       Calendar calendar = Calendar.getInstance();
/* 2284 */                                       str = TimeUtil.getCurrentDateString() + " " + simpleDateFormat.format(calendar.getTime());
/* 2285 */                                     } else if (m == 4 || m == 167 || m == 57 || m == 168) {
/* 2286 */                                       str = "" + user.getUserDepartment();
/* 2287 */                                     } else if (m == 42 || m == 164 || m == 169 || m == 170 || m == 194) {
/* 2288 */                                       str = "" + user.getUserSubCompany1();
/* 2289 */                                     } else if (m == 24 || m == 278) {
/* 2290 */                                       str = "" + user.getJobtitle();
/* 2291 */                                     } else if (m == 402) {
/* 2292 */                                       String str4 = "YYYY";
/* 2293 */                                       SimpleDateFormat simpleDateFormat = new SimpleDateFormat(str4);
/* 2294 */                                       Calendar calendar = Calendar.getInstance();
/* 2295 */                                       str = simpleDateFormat.format(calendar.getTime());
/* 2296 */                                     } else if (m == 403) {
/* 2297 */                                       String str4 = "YYYY-MM";
/* 2298 */                                       SimpleDateFormat simpleDateFormat = new SimpleDateFormat(str4);
/* 2299 */                                       Calendar calendar = Calendar.getInstance();
/* 2300 */                                       str = simpleDateFormat.format(calendar.getTime());
/*      */                                     } 
/*      */                                   }
/* 2303 */                                   sql[0] = "SELECT customervalue FROM workflow_addinoperate WHERE ispreadd='1' AND isnode=1 AND isdisable=0 AND objid=" + finalNodeid + " AND fieldid=" + ((String)arrayList4
/*      */ 
/*      */                                     
/* 2306 */                                     .get(0)).substring(5);
/* 2307 */                                   recordSet.executeSql(sql[0]);
/* 2308 */                                   if (recordSet.next()) {
/* 2309 */                                     str = Util.null2String(recordSet.getString("customervalue"));
/*      */                                   }
/* 2311 */                                   if (!str.equals("")) {
/* 2312 */                                     str1 = str1 + "," + viewfieldnames.get(b1);
/* 2313 */                                     stringBuilder1.append((new StringBuilder()).append(viewfieldnames.get(b1)).append(",").toString());
/* 2314 */                                     stringBuilder2.append("?,");
/* 2315 */                                     if (((String)viewfieldbdtypes.get(b1)).toLowerCase().contains("int") || ((String)viewfieldbdtypes
/* 2316 */                                       .get(b1)).toLowerCase().contains("number") || ((String)viewfieldbdtypes
/* 2317 */                                       .get(b1)).toLowerCase().contains("decimal")) {
/* 2318 */                                       str2 = str2 + ",'" + str + "'";
/* 2319 */                                       arrayList1.add(str);
/*      */                                     } else {
/* 2321 */                                       str2 = str2 + ",'" + str + "'";
/* 2322 */                                       arrayList1.add(str);
/*      */                                     } 
/*      */                                   } 
/*      */                                 } 
/* 2326 */                                 str1 = str1 + ")";
/* 2327 */                                 str2 = str2 + ")";
/* 2328 */                                 if (stringBuilder1.length() > 0) {
/* 2329 */                                   stringBuilder1.deleteCharAt(stringBuilder1.length() - 1);
/*      */                                 }
/* 2331 */                                 if (stringBuilder2.length() > 0) {
/* 2332 */                                   stringBuilder2.deleteCharAt(stringBuilder2.length() - 1);
/*      */                                 }
/* 2334 */                                 stringBuilder1.append(")");
/* 2335 */                                 stringBuilder2.append(")");
/*      */                               } 
/* 2337 */                               System.out.println("增加浏览按钮后===,upBeforeSql=" + stringBuilder3 + ",updateParam=" + arrayList);
/*      */                               try {
/* 2339 */                                 if (!bool5) {
/* 2340 */                                   totalData[0] = totalData[0] + 1;
/* 2341 */                                   if (str1.startsWith("UPDATE")) {
/* 2342 */                                     updateBatchSql[0] = stringBuilder3.append(stringBuilder4);
/* 2343 */                                     arrayList.addAll(arrayList2);
/* 2344 */                                     updateParams.put(Integer.valueOf(i), arrayList);
/*      */                                   } else {
/* 2346 */                                     insertParams.put(Integer.valueOf(i), arrayList1);
/* 2347 */                                     inserBatchSql[0] = stringBuilder1.append(stringBuilder2);
/* 2348 */                                     sql1List.add(str1);
/* 2349 */                                     sql2List.add(str2);
/*      */                                   } 
/* 2351 */                                   if (!bool1) {
/* 2352 */                                     errNum[0] = errNum[0] - 1;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */                                   
/*      */                                   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */                                 
/*      */                                 }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */                               
/*      */                               }
/* 2472 */                               catch (Exception exception) {
/* 2473 */                                 errorCell.add((finalI + 1) + "," + i + "," + -1);
/*      */                               } 
/*      */                             } 
/* 2476 */                             isnullList.add(Boolean.valueOf(bool3));
/* 2477 */                             isnullvalueList.add(Boolean.valueOf(bool4));
/* 2478 */                             rowList.add(Integer.valueOf(i));
/* 2479 */                             if (!bool3 && (!bool2 || bool4) && !finalIsmode.equals("1")) {
/* 2480 */                               errorCell.add((finalI + 1) + "," + i + "," + -1);
/*      */                             }
/*      */                           } 
/* 2483 */                           if (!Util.null2String(hashMap1.get("col")).equals("")) {
/* 2484 */                             hashMap1.put("key", Integer.valueOf(num[0] = num[0] + 1));
/* 2485 */                             hashMap1.put("title", SystemEnv.getHtmlLabelName(19325, user.getLanguage()) + (finalI + 1));
/* 2486 */                             hashMap1.put("row", Integer.valueOf(i + 1));
/* 2487 */                             String str4 = Util.null2String(hashMap1.get("info"));
/* 2488 */                             if (str4.endsWith(";")) {
/* 2489 */                               str4 = str4.substring(0, str4.length() - 1);
/* 2490 */                               hashMap1.put("info", str4);
/*      */                             } 
/* 2492 */                             String str5 = Util.null2String(hashMap1.get("col"));
/* 2493 */                             if (str5.endsWith(";")) {
/* 2494 */                               str5 = str5.substring(0, str5.length() - 1);
/* 2495 */                               hashMap1.put("col", str5);
/*      */                             } 
/*      */                             
/* 2498 */                             errList.add(hashMap1);
/*      */                           
/*      */                           }
/*      */                         
/*      */                         }
/* 2503 */                         catch (Exception exception) {}
/*      */                       } 
/*      */ 
/*      */                       
/* 2507 */                       RequestDetailImport.this.writeLog(System.identityHashCode(rowMap) + "结束第" + rowMap.get("start") + "行至" + rowMap.get("end") + "行的导入：" + dataFormat.format(new Date()));
/* 2508 */                       countDownLatch.countDown();
/*      */                     }
/*      */                   });
/*      */ 
/*      */               
/* 2513 */               thread.start();
/*      */             } 
/* 2515 */             countDownLatch.await();
/* 2516 */             writeLog("sheet【" + (b4 + 1) + "】线程数据处理耗时=" + (System.currentTimeMillis() - l4));
/* 2517 */             long l5 = System.currentTimeMillis();
/*      */             
/* 2519 */             writeLog("---start更新---" + simpleDateFormat.format(new Date()));
/* 2520 */             if (!Util.null2String(arrayOfStringBuilder2[0]).isEmpty()) {
/* 2521 */               ArrayList<V> arrayList33 = new ArrayList();
/* 2522 */               for (Map.Entry<Object, Object> entry : treeMap2.entrySet()) {
/* 2523 */                 arrayList33.add("".equals(entry.getValue()) ? null : (V)entry.getValue());
/*      */               }
/*      */               
/* 2526 */               RecordSet recordSet = new RecordSet();
/* 2527 */               writeLog("更新sql=" + arrayOfStringBuilder2[0].toString() + "====updatesqlparams=" + arrayList33);
/* 2528 */               boolean bool3 = recordSet.executeBatchSql(arrayOfStringBuilder2[0].toString(), arrayList33);
/* 2529 */               writeLog("---end更新---" + simpleDateFormat.format(new Date()) + "更新成功：" + bool3);
/*      */             } 
/* 2531 */             if (!Util.null2String(arrayOfStringBuilder1[0]).isEmpty()) {
/*      */               try {
/* 2533 */                 writeLog("---star插入---" + simpleDateFormat.format(new Date()));
/* 2534 */                 ArrayList arrayList33 = new ArrayList();
/* 2535 */                 for (Map.Entry<Object, Object> entry : treeMap1.entrySet()) {
/* 2536 */                   arrayList33.add(entry.getValue());
/*      */                 }
/* 2538 */                 writeLog("插入sql=" + arrayOfStringBuilder1[0].toString() + "====sqlparams=" + arrayList33);
/* 2539 */                 RecordSet recordSet = new RecordSet();
/* 2540 */                 recordSet.executeBatchSql(arrayOfStringBuilder1[0].toString(), arrayList33);
/* 2541 */               } catch (Exception exception) {
/* 2542 */                 exception.printStackTrace();
/* 2543 */                 writeLog("glb-->requestid=" + i + ",批量失败=detail import batchInsert failed start Insert separately");
/* 2544 */                 RecordSet recordSet = new RecordSet();
/* 2545 */                 for (byte b7 = 0; b7 < arrayList27.size(); b7++) {
/* 2546 */                   String str22 = arrayList27.get(b7);
/* 2547 */                   String str23 = arrayList28.get(b7);
/* 2548 */                   writeLog("sql+sql2=" + str22 + str23);
/* 2549 */                   boolean bool3 = recordSet.executeSql(str22 + str23);
/* 2550 */                   boolean bool4 = ((Boolean)arrayList29.get(b7)).booleanValue();
/* 2551 */                   boolean bool5 = ((Boolean)arrayList30.get(b7)).booleanValue();
/* 2552 */                   int i21 = ((Integer)arrayList31.get(b7)).intValue();
/* 2553 */                   if (!bool5 && (!bool3 || bool4)) {
/* 2554 */                     arrayList25.add((b7 + 1) + "," + i21 + "," + -1);
/*      */                   }
/* 2556 */                   if (!bool3) {
/* 2557 */                     int i22 = str22.indexOf("(");
/* 2558 */                     int i23 = str22.lastIndexOf(")");
/* 2559 */                     String str = str22.substring(i22 + 1, i23);
/* 2560 */                     String[] arrayOfString1 = str.split(",");
/* 2561 */                     StringBuilder stringBuilder = new StringBuilder(" VALUES(" + i5 + ",");
/* 2562 */                     for (byte b8 = 1; b8 < arrayOfString1.length; b8++) {
/* 2563 */                       stringBuilder.append("'',");
/*      */                     }
/* 2565 */                     stringBuilder.deleteCharAt(stringBuilder.length() - 1);
/* 2566 */                     stringBuilder.append(")");
/* 2567 */                     bool3 = recordSet.executeSql(str22 + stringBuilder);
/*      */                   } 
/*      */                 } 
/*      */               } 
/*      */             }
/*      */             
/* 2573 */             writeLog("sheet【" + (b4 + 1) + "】---end批处理sql耗时---" + (System.currentTimeMillis() - l5));
/*      */             
/* 2575 */             long l6 = System.currentTimeMillis();
/* 2576 */             writeLog("---start删除---");
/*      */ 
/*      */             
/* 2579 */             if ("1".equals(str16) || "1".equals(str2)) {
/* 2580 */               StringBuilder stringBuilder = new StringBuilder();
/* 2581 */               StringJoiner stringJoiner = new StringJoiner(",");
/* 2582 */               String str = "";
/*      */               
/* 2584 */               for (byte b7 = 0; b7 < i10; b7++) {
/* 2585 */                 int i21 = b7 + 1;
/*      */                 
/* 2587 */                 if (!arrayList26.contains(Integer.valueOf(i21))) {
/*      */                   
/* 2589 */                   if (i1 == 1) {
/* 2590 */                     arrayOfString[0] = "delete from " + str10 + " where " + str11 + "=" + i5;
/*      */                   } else {
/* 2592 */                     arrayOfString[0] = "delete from " + str10 + " where requestid=" + i + " and groupid=" + i8;
/*      */                   } 
/* 2594 */                   if (stringBuilder.length() == 0) {
/* 2595 */                     stringBuilder.append(arrayOfString[0]);
/*      */                   }
/*      */                   
/* 2598 */                   List<String> list = getPKCondition(fieldInfo, str9, str10, n, i1, arrayList15.get(b6), b7);
/* 2599 */                   arrayOfString[0] = arrayOfString[0] + " AND " + (String)list.get(0) + "=" + (String)list.get(1);
/* 2600 */                   if (stringJoiner.length() == 0) {
/* 2601 */                     str = list.get(0);
/*      */                   }
/* 2603 */                   stringJoiner.add(list.get(1));
/*      */                   
/* 2605 */                   List list1 = (List)map2.get(Integer.valueOf(b4 + 1));
/*      */                   
/* 2607 */                   String str22 = "";
/* 2608 */                   for (Integer integer : list1) {
/* 2609 */                     str22 = str22 + integer + ",";
/*      */                   }
/* 2611 */                   if (!"".equals(str22)) {
/* 2612 */                     str22 = str22.substring(0, str22.length() - 1);
/* 2613 */                     arrayOfString[0] = arrayOfString[0] + " and (" + Util.getSubINClause(str22, "id", "not in") + ")";
/* 2614 */                     stringBuilder.append(arrayOfString[0]);
/*      */                   } 
/*      */                 } 
/*      */               } 
/*      */               
/* 2619 */               if (stringJoiner.length() > 0) {
/* 2620 */                 stringBuilder.append(" and (" + Util.getSubINClause(stringJoiner.toString(), str, "in") + ")");
/*      */               }
/* 2622 */               writeLog("sheet【" + (b4 + 1) + "】删除sql=" + stringBuilder.toString());
/* 2623 */               if (stringBuilder.length() > 0) {
/* 2624 */                 recordSet1.executeUpdate(stringBuilder.toString(), new Object[0]);
/*      */               }
/*      */             } 
/* 2627 */             writeLog("---sheet【" + (b4 + 1) + "】end删除耗时---" + (System.currentTimeMillis() - l6));
/*      */           } 
/*      */           
/* 2630 */           long l = System.currentTimeMillis();
/*      */           
/* 2632 */           SubjectFilter subjectFilter = new SubjectFilter();
/* 2633 */           subjectFilter.filterSubject(i, i4, str9, str10);
/*      */           
/* 2635 */           if ("1".equals(str3)) {
/* 2636 */             requestFieldTrigger.calFieldTrigger(n, i4, i, i1, str9, str10, i2);
/*      */           }
/*      */           
/*      */           try {
/* 2640 */             getColCalStr(n, i, str9, hashMap8, i5, i1, str10);
/* 2641 */           } catch (Exception exception) {
/* 2642 */             exception.printStackTrace();
/*      */           } 
/*      */ 
/*      */           
/* 2646 */           String str17 = " ";
/* 2647 */           if (user.getLanguage() != 8) {
/* 2648 */             str17 = "";
/*      */           }
/*      */           
/* 2651 */           stringBuffer.append(errorCellMsg(arrayList25, user, str17));
/* 2652 */           b2++;
/* 2653 */           writeLog("sheet【" + (b4 + 1) + "】字段联动等耗时=" + (System.currentTimeMillis() - l));
/*      */         }
/*      */       
/*      */       } 
/* 2657 */     } catch (Exception exception) {
/* 2658 */       writeLog(exception);
/* 2659 */       if ("1".equals(str2)) {
/* 2660 */         stringBuffer.setLength(0);
/* 2661 */         stringBuffer.append(SystemEnv.getHtmlLabelName(27593, user.getLanguage()) + "!\\n");
/* 2662 */         return stringBuffer.toString();
/*      */       } 
/* 2664 */       if (workbook == null) {
/* 2665 */         jSONObject.put("parsing", Boolean.valueOf(false));
/* 2666 */         jSONObject.put("msg", SystemEnv.getHtmlLabelName(521385, this.currentUserLang));
/* 2667 */         return jSONObject.toString();
/*      */       } 
/*      */     } 
/*      */ 
/*      */     
/* 2672 */     long l3 = System.currentTimeMillis();
/* 2673 */     writeLog("requestid=" + i + ",导入耗时=" + (l3 - l2));
/*      */     
/* 2675 */     if (detailDataModifyTrackBiz != null);
/*      */ 
/*      */ 
/*      */     
/* 2679 */     while (arrayOfInt3[0] > arrayList8.size() + -arrayOfInt2[0]) {
/* 2680 */       arrayOfInt3[0] = arrayOfInt3[0] - 1;
/*      */     }
/*      */     
/* 2683 */     jSONObject.put("totalData", Integer.valueOf(arrayOfInt3[0]));
/* 2684 */     jSONObject.put("dataSource", arrayList8);
/* 2685 */     jSONObject.put("failData", Integer.valueOf(arrayList8.size()));
/* 2686 */     jSONObject.put("successData", Integer.valueOf(arrayOfInt3[0] - arrayList8.size()));
/* 2687 */     String str12 = "";
/* 2688 */     String str13 = SystemEnv.getHtmlLabelName(527163, user.getLanguage()) + jSONObject.get("totalData").toString() + SystemEnv.getHtmlLabelName(10003396, user.getLanguage()) + jSONObject.get("successData").toString() + SystemEnv.getHtmlLabelName(527982, user.getLanguage());
/* 2689 */     if (Util.getIntValue(Util.null2String(jSONObject.get("failData")), -1) == 0) {
/* 2690 */       str13 = SystemEnv.getHtmlLabelName(527163, user.getLanguage()) + jSONObject.get("totalData").toString() + SystemEnv.getHtmlLabelName(10003396, user.getLanguage()) + SystemEnv.getHtmlLabelName(506874, user.getLanguage());
/*      */     }
/* 2692 */     if (null != arrayList8 && arrayList8.size() > 0) {
/* 2693 */       str12 = SystemEnv.getHtmlLabelName(524950, user.getLanguage()) + jSONObject.get("failData") + SystemEnv.getHtmlLabelName(530135, user.getLanguage());
/*      */     }
/*      */ 
/*      */ 
/*      */ 
/*      */     
/* 2699 */     jSONObject.put("msg2", str12);
/* 2700 */     jSONObject.put("msg", str13);
/* 2701 */     if ("1".equals(str2)) {
/* 2702 */       return stringBuffer.toString();
/*      */     }
/* 2704 */     return jSONObject.toString();
/*      */   }
/*      */ 
/*      */   
/*      */   private String getNumeric(String paramString) {
/* 2709 */     return paramString.replaceAll(".*[^\\d](?=(\\d+))", "");
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   private Set<String> getDetailAttr(int paramInt) {
/* 2715 */     HashSet<String> hashSet = new HashSet();
/* 2716 */     boolean bool = false;
/* 2717 */     String str = "select DATAJSON from workflow_nodehtmllayout where id = ?";
/* 2718 */     RecordSet recordSet = new RecordSet();
/* 2719 */     recordSet.executeQuery(str, new Object[] { Integer.valueOf(paramInt) });
/* 2720 */     JSONObject jSONObject1 = new JSONObject();
/* 2721 */     while (recordSet.next()) {
/* 2722 */       jSONObject1 = JSONObject.parseObject(recordSet.getString("DATAJSON"));
/*      */     }
/* 2724 */     JSONObject jSONObject2 = (JSONObject)jSONObject1.get("eformdesign");
/* 2725 */     JSONObject jSONObject3 = (JSONObject)jSONObject2.get("etables");
/* 2726 */     Set set = jSONObject3.entrySet();
/* 2727 */     for (Map.Entry entry : set) {
/* 2728 */       String str1 = (String)entry.getKey();
/* 2729 */       if (str1.startsWith("detail_")) {
/* 2730 */         String str2 = str1.replace("detail_", "");
/* 2731 */         hashSet.add(str2);
/*      */       } 
/*      */     } 
/* 2734 */     return hashSet;
/*      */   }
/*      */ 
/*      */   
/*      */   private String getColChar(int paramInt) {
/* 2739 */     paramInt++;
/* 2740 */     if (paramInt <= 0) {
/* 2741 */       return null;
/*      */     }
/* 2743 */     String str = "";
/* 2744 */     paramInt--;
/*      */     while (true) {
/* 2746 */       if (str.length() > 0) {
/* 2747 */         paramInt--;
/*      */       }
/* 2749 */       str = (char)(paramInt % 26 + 65) + str;
/* 2750 */       paramInt = (paramInt - paramInt % 26) / 26;
/* 2751 */       if (paramInt <= 0) {
/* 2752 */         return str;
/*      */       }
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private String errorCellMsg(List<String> paramList, User paramUser, String paramString) {
/* 2766 */     StringBuffer stringBuffer = new StringBuffer();
/* 2767 */     if (paramList.size() > 0) {
/* 2768 */       HashSet<String> hashSet = new HashSet();
/* 2769 */       for (String str : paramList) {
/* 2770 */         String[] arrayOfString = str.split(",");
/* 2771 */         if ("-1".equals(arrayOfString[2])) hashSet.add(arrayOfString[1]); 
/*      */       } 
/* 2773 */       Iterator<String> iterator = paramList.iterator();
/* 2774 */       while (iterator.hasNext()) {
/* 2775 */         String str = iterator.next();
/* 2776 */         String[] arrayOfString = str.split(",");
/* 2777 */         stringBuffer.append(paramString + 
/* 2778 */             SystemEnv.getHtmlLabelName(15323, paramUser.getLanguage()) + paramString + arrayOfString[0] + paramString + 
/*      */ 
/*      */ 
/*      */             
/* 2782 */             SystemEnv.getHtmlLabelName(27591, paramUser.getLanguage()) + paramString + "sheet" + paramString + 
/*      */ 
/*      */ 
/*      */             
/* 2786 */             SystemEnv.getHtmlLabelName(15323, paramUser.getLanguage()) + paramString + arrayOfString[1] + paramString + 
/*      */ 
/*      */ 
/*      */             
/* 2790 */             SystemEnv.getHtmlLabelName(27592, paramUser.getLanguage()) + paramString);
/* 2791 */         if (!"-1".equals(arrayOfString[2]) && !hashSet.contains(arrayOfString[1])) {
/* 2792 */           stringBuffer.append(paramString + 
/* 2793 */               SystemEnv.getHtmlLabelName(15323, paramUser.getLanguage()) + paramString + arrayOfString[2] + paramString + 
/*      */ 
/*      */ 
/*      */               
/* 2797 */               SystemEnv.getHtmlLabelName(18621, paramUser.getLanguage()) + paramString);
/*      */         }
/* 2799 */         stringBuffer.append(paramString + SystemEnv.getHtmlLabelName(27593, paramUser.getLanguage()) + "!\\n");
/*      */       } 
/*      */     } 
/* 2802 */     return stringBuffer.toString();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private List<String> getPKCondition(FieldInfo paramFieldInfo, String paramString1, String paramString2, int paramInt1, int paramInt2, Object paramObject, int paramInt3) {
/* 2817 */     ArrayList<String> arrayList = new ArrayList();
/*      */     
/*      */     try {
/*      */       String str;
/* 2821 */       if (paramInt2 == 0) {
/* 2822 */         str = "id";
/*      */       }
/* 2824 */       else if (paramInt1 == 156 || paramInt1 == 157 || paramInt1 == 158 || paramInt1 == 159) {
/* 2825 */         str = "dsporder";
/* 2826 */       } else if (paramInt1 == 7 || paramInt1 == 14 || paramInt1 == 18) {
/* 2827 */         str = "id";
/* 2828 */       } else if ((paramString1.indexOf("formtable_main_") == 0 || paramString1.indexOf("uf_") == 0) && (paramString2
/* 2829 */         .indexOf("formtable_main_") == 0 || paramString2.indexOf("uf_") == 0)) {
/* 2830 */         str = "id";
/* 2831 */       } else if (paramInt1 < 0) {
/* 2832 */         str = "inputid";
/*      */       } else {
/* 2834 */         str = "id";
/*      */       } 
/*      */       
/* 2837 */       arrayList.add(str);
/* 2838 */       arrayList.add((new StringBuilder()).append(((List)paramObject).get(paramInt3)).append("").toString());
/*      */     
/*      */     }
/* 2841 */     catch (Exception exception) {
/*      */       
/* 2843 */       exception.printStackTrace();
/* 2844 */       (new BaseBean()).writeLog(exception);
/*      */     } 
/* 2846 */     return arrayList;
/*      */   }
/*      */   private Map<String, Map> getTableFieldValueMap(String paramString) {
/* 2849 */     return getTableFieldValueMap(paramString, (String)null, -1);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private Map<String, Map> getTableFieldValueMap(String paramString1, String paramString2, int paramInt) {
/* 2858 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 2859 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 2860 */     HashMap<Object, Object> hashMap3 = new HashMap<>();
/* 2861 */     ArrayList<String> arrayList1 = new ArrayList();
/* 2862 */     ArrayList<String> arrayList2 = new ArrayList();
/* 2863 */     ArrayList arrayList = new ArrayList();
/*      */     
/* 2865 */     String str1 = "";
/* 2866 */     RecordSet recordSet = new RecordSet();
/*      */ 
/*      */     
/* 2869 */     String str2 = "";
/* 2870 */     String str3 = "";
/* 2871 */     str1 = "select t2.workflowid,t1.formid,t1.isbill from workflow_base t1 join workflow_requestbase t2 on t1.id=t2.workflowid where t2.requestid=" + paramString1;
/* 2872 */     recordSet.executeSql(str1);
/* 2873 */     if (recordSet.next()) {
/*      */       
/* 2875 */       str3 = Util.null2String(recordSet.getString("formid"));
/* 2876 */       str2 = Util.null2String(recordSet.getString("isbill"));
/*      */     } 
/* 2878 */     if (str2.equals("1")) {
/*      */       
/* 2880 */       str1 = "select id,fieldname,detailtable from workflow_billfield where (detailtable is null or detailtable='') and billid = " + str3;
/* 2881 */       recordSet.executeSql(str1);
/* 2882 */       while (recordSet.next()) {
/* 2883 */         String str = Util.null2String(recordSet.getString("fieldname")).toLowerCase();
/* 2884 */         arrayList1.add(str);
/*      */       } 
/*      */       
/* 2887 */       WorkflowBillComInfo workflowBillComInfo = null;
/*      */       try {
/* 2889 */         workflowBillComInfo = new WorkflowBillComInfo();
/* 2890 */       } catch (Exception exception) {
/* 2891 */         exception.printStackTrace();
/*      */       } 
/*      */       
/* 2894 */       String str4 = workflowBillComInfo.getTablename(str3);
/* 2895 */       String str5 = workflowBillComInfo.getDetailkeyfield(str3);
/* 2896 */       String str6 = "";
/*      */ 
/*      */       
/* 2899 */       str1 = "select * from " + str4 + " where requestid=" + paramString1;
/* 2900 */       recordSet.executeSql(str1);
/* 2901 */       if (recordSet.next()) {
/* 2902 */         str6 = Util.null2String(recordSet.getString("id"));
/* 2903 */         for (byte b = 0; b < arrayList1.size(); b++) {
/* 2904 */           String str7 = arrayList1.get(b);
/* 2905 */           String str8 = Util.null2String(recordSet.getString(str7));
/* 2906 */           hashMap3.put("$" + str7 + "$", str8);
/*      */         } 
/*      */       } 
/*      */       
/* 2910 */       if (paramInt >= 0) {
/*      */         
/* 2912 */         str1 = "select distinct detailtable from workflow_billfield where detailtable is not null and detailtable !='' and billid = " + str3;
/* 2913 */         recordSet.executeSql(str1);
/* 2914 */         while (recordSet.next()) {
/*      */ 
/*      */           
/* 2917 */           RecordSet recordSet1 = new RecordSet();
/*      */           
/* 2919 */           str1 = "select fieldname from workflow_billfield where detailtable='" + paramString2 + "' and billid = " + str3;
/* 2920 */           recordSet1.executeSql(str1);
/* 2921 */           while (recordSet1.next()) {
/* 2922 */             String str = Util.null2String(recordSet1.getString("fieldname")).toLowerCase();
/* 2923 */             arrayList2.add(str);
/*      */           } 
/*      */           
/* 2926 */           str1 = "select * from " + paramString2 + " where " + str5 + "=" + str6;
/* 2927 */           recordSet1.executeSql(str1);
/* 2928 */           int i = 0;
/* 2929 */           while (recordSet1.next()) {
/* 2930 */             for (byte b = 0; b < arrayList2.size(); b++) {
/* 2931 */               String str7 = arrayList2.get(b);
/* 2932 */               String str8 = Util.null2String(recordSet1.getString(str7));
/* 2933 */               if (i == paramInt) {
/* 2934 */                 hashMap2.put("$" + paramString2 + "_" + str7 + "$", str8);
/*      */               }
/*      */             } 
/* 2937 */             i++;
/*      */           }
/*      */         
/*      */         } 
/*      */       } 
/*      */     } else {
/*      */       
/* 2944 */       str1 = "select b.id,b.fieldname from workflow_formfield a,workflow_formdict b where a.fieldid = b.id and formid = " + str3;
/* 2945 */       recordSet.executeSql(str1);
/* 2946 */       while (recordSet.next()) {
/* 2947 */         String str4 = Util.null2String(recordSet.getString("fieldname"));
/* 2948 */         arrayList1.add(str4);
/*      */       } 
/* 2950 */       String str = "";
/*      */ 
/*      */       
/* 2953 */       str1 = "select * from workflow_form where requestid=" + paramString1;
/* 2954 */       recordSet.executeSql(str1);
/* 2955 */       if (recordSet.next()) {
/* 2956 */         str = paramString1;
/* 2957 */         for (byte b = 0; b < arrayList1.size(); b++) {
/* 2958 */           String str4 = arrayList1.get(b);
/* 2959 */           String str5 = Util.null2String(recordSet.getString(str4));
/* 2960 */           hashMap3.put("$" + str4 + "$", str5);
/*      */         } 
/*      */       } 
/*      */       
/* 2964 */       if (paramInt > 0) {
/*      */         
/* 2966 */         str1 = "select b.id,b.fieldname from workflow_formfield a,workflow_formdictdetail b where a.fieldid = b.id and formid = " + str3;
/* 2967 */         recordSet.executeSql(str1);
/* 2968 */         while (recordSet.next()) {
/* 2969 */           String str4 = Util.null2String(recordSet.getString("fieldname"));
/* 2970 */           arrayList2.add(str4);
/*      */         } 
/*      */         
/* 2973 */         str1 = "select * from workflow_formdetail where requestid=" + paramString1;
/* 2974 */         recordSet.executeSql(str1);
/* 2975 */         if (recordSet.next()) {
/* 2976 */           str = paramString1;
/* 2977 */           for (byte b = 0; b < arrayList2.size(); b++) {
/* 2978 */             String str4 = arrayList2.get(b);
/* 2979 */             String str5 = Util.null2String(recordSet.getString(str4));
/* 2980 */             if (b == paramInt) {
/* 2981 */               hashMap2.put("$detail_" + str4 + "$", str5);
/*      */             }
/*      */           } 
/*      */         } 
/*      */       } 
/*      */     } 
/*      */     
/* 2988 */     hashMap1.put("detailValueMap", hashMap2);
/* 2989 */     hashMap1.put("mainValueMap", hashMap3);
/* 2990 */     return (Map)hashMap1;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String forMultiLangSql(String paramString1, String paramString2, String paramString3, String paramString4) {
/* 3003 */     return forMultiLangSql(paramString1, paramString2, paramString3, paramString4, this.currentUserLang, (String)null);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String forMultiLangSql(String paramString1, String paramString2, String paramString3, String paramString4, String paramString5) {
/* 3017 */     return forMultiLangSql(paramString1, paramString2, paramString3, paramString4, this.currentUserLang, paramString5);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String forMultiLangSql(String paramString1, String paramString2, String paramString3, String paramString4, int paramInt, String paramString5) {
/* 3033 */     String str1 = "";
/* 3034 */     if (paramInt > 9) {
/* 3035 */       str1 = paramInt + "";
/*      */     } else {
/* 3037 */       str1 = paramInt + " ";
/*      */     } 
/* 3039 */     String str2 = "";
/* 3040 */     if (Util.isEnableMultiLang()) {
/* 3041 */       str2 = "SELECT " + paramString2 + " FROM " + paramString1 + " WHERE (" + paramString3 + "='" + paramString4 + "'  or " + paramString3 + " like '%`~`" + str1 + paramString4 + "`~`%')";
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/* 3051 */       if (paramString5 != null && !paramString5.equals("")) {
/* 3052 */         str2 = str2 + paramString5;
/*      */       }
/*      */     } else {
/* 3055 */       str2 = "SELECT " + paramString2 + " FROM " + paramString1 + " WHERE " + paramString3 + "='" + paramString4 + "' ";
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/* 3064 */       if (paramString5 != null && !paramString5.equals("")) {
/* 3065 */         str2 = str2 + paramString5;
/*      */       }
/*      */     } 
/* 3068 */     return str2;
/*      */   }
/*      */   
/*      */   private Map<String, Integer> getTableGroupId(int paramInt) {
/* 3072 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 3073 */     RecordSet recordSet = new RecordSet();
/* 3074 */     recordSet.executeQuery("select DISTINCT TABLEname,orderid from  workflow_billdetailtable where billid = ? order by orderid asc", new Object[] { Integer.valueOf(paramInt) });
/* 3075 */     byte b = 0;
/* 3076 */     while (recordSet.next()) {
/* 3077 */       String str = Util.null2String(recordSet.getString(1));
/* 3078 */       hashMap.put(str, Integer.valueOf(++b));
/*      */     } 
/* 3080 */     return (Map)hashMap;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private boolean isNewExcelTemplate(Sheet paramSheet) {
/* 3090 */     int i = POIUtil.getLastCellNum(paramSheet);
/* 3091 */     for (byte b = 1; b < i; b++) {
/* 3092 */       String str = POIUtil.getValue(paramSheet, 0, b);
/* 3093 */       if (str.indexOf("##") == -1) return false; 
/*      */     } 
/* 3095 */     return true;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private Map<String, Integer> getImportField(Sheet paramSheet, ArrayList paramArrayList) {
/* 3106 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 3107 */     int i = POIUtil.getLastCellNum(paramSheet);
/* 3108 */     for (byte b = 1; b < i; b++) {
/* 3109 */       String str = POIUtil.getValue(paramSheet, 0, b);
/* 3110 */       str = str.substring(str.indexOf("##") + 2, str.length());
/* 3111 */       if (paramArrayList.contains(str))
/* 3112 */         hashMap.put(str, Integer.valueOf(b)); 
/*      */     } 
/* 3114 */     return (Map)hashMap;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private boolean isOnlyViewField(List<String> paramList, Sheet paramSheet, int paramInt) {
/* 3125 */     String str = POIUtil.getValue(paramSheet, 0, paramInt);
/* 3126 */     if (Strings.isNullOrEmpty(str)) return false; 
/* 3127 */     String[] arrayOfString = str.split("##");
/* 3128 */     if (arrayOfString.length == 2) {
/* 3129 */       str = arrayOfString[1];
/* 3130 */       return paramList.contains(str);
/*      */     } 
/* 3132 */     return false;
/*      */   }
/*      */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/RequestDetailImport.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */