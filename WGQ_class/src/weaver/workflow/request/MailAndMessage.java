/*     */ package weaver.workflow.request;
/*     */ 
/*     */ import com.engine.common.biz.EncryptConfigBiz;
/*     */ import com.engine.workflow.biz.customizeBrowser.CustomBrowserBiz;
/*     */ import com.engine.workflow.entity.SystemFieldInfoEntity;
/*     */ import com.engine.workflow.util.CollectionUtil;
/*     */ import com.engine.workflow.util.GetCustomLevelUtil;
/*     */ import com.engine.workflow.util.ListUtil;
/*     */ import com.engine.workflow.util.SystemFieldUtil;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.crm.Maint.CustomerInfoComInfo;
/*     */ import weaver.email.EmailWorkRunnable;
/*     */ import weaver.file.FileUpload;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.GCONST;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.HrmUserVarify;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.workflow.field.FieldValue;
/*     */ import weaver.workflow.mode.FieldInfo;
/*     */ import weaver.workflow.workflow.WorkflowBillComInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class MailAndMessage
/*     */   extends BaseBean
/*     */ {
/*  49 */   private CustomerInfoComInfo customerInfoComInfo = null;
/*  50 */   private ResourceComInfo resourceComInfo = null;
/*     */ 
/*     */   
/*     */   private HttpServletRequest request;
/*     */   
/*     */   private FileUpload fu;
/*     */   
/*     */   private boolean isRequest;
/*     */   
/*     */   private int forwardflag;
/*     */ 
/*     */   
/*     */   public void setRequest(HttpServletRequest paramHttpServletRequest) {
/*  63 */     this.request = paramHttpServletRequest;
/*  64 */     this.isRequest = true;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setRequest(FileUpload paramFileUpload) {
/*  73 */     this.fu = paramFileUpload;
/*  74 */     this.isRequest = false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public MailAndMessage() {
/*     */     try {
/*  82 */       this.resourceComInfo = new ResourceComInfo();
/*  83 */       this.customerInfoComInfo = new CustomerInfoComInfo();
/*  84 */     } catch (Exception exception) {}
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getTitle(int paramInt1, int paramInt2, int paramInt3, int paramInt4, int paramInt5) {
/*  97 */     RecordSet recordSet1 = new RecordSet();
/*  98 */     RecordSet recordSet2 = new RecordSet();
/*  99 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 100 */     User user = new User();
/* 101 */     CustomBrowserBiz customBrowserBiz = new CustomBrowserBiz();
/* 102 */     user.setLanguage(paramInt4);
/* 103 */     FieldValue fieldValue = new FieldValue();
/*     */     
/* 105 */     String str1 = "select mainrequestid,requestid,requestname,requestlevel,mainrequestid,creater,creatertype,createdate,createtime,workflowId,currentstatus,currentnodeid,currentnodetype,status,remindTypes,docids,crmids,prjids,cptids , lastnodeid  from workflow_requestbase where requestid=?";
/* 106 */     recordSet1.executeQuery(str1, new Object[] { Integer.valueOf(paramInt1) });
/* 107 */     if (recordSet1.next()) {
/* 108 */       String str = recordSet1.getString("creatertype");
/* 109 */       List list = SystemFieldUtil.getSystemFields("wfcustitle", user, null);
/* 110 */       for (SystemFieldInfoEntity systemFieldInfoEntity : list) {
/* 111 */         String str5 = systemFieldInfoEntity.getFieldname().toLowerCase();
/* 112 */         String str6 = systemFieldInfoEntity.getId() + "";
/* 113 */         String str7 = systemFieldInfoEntity.getLabel();
/* 114 */         String str8 = Util.null2String(recordSet1.getString(str5));
/* 115 */         if (str5.equalsIgnoreCase("creater")) {
/* 116 */           if ("0".equals(str)) {
/* 117 */             str8 = Util.null2String(this.resourceComInfo.getResourcename(str8));
/*     */           } else {
/* 119 */             str8 = Util.null2String(this.customerInfoComInfo.getCustomerInfoname(str8));
/*     */           } 
/*     */         }
/* 122 */         if (str5.equalsIgnoreCase("requestlevel")) str8 = Util.null2String(GetCustomLevelUtil.getLevel(str8, paramInt4)); 
/* 123 */         if (str5.equalsIgnoreCase("createtime")) str8 = Util.null2String(recordSet1.getString("createdate")) + " " + Util.null2String(recordSet1.getString("createtime"));
/*     */         
/* 125 */         HashMap<Object, Object> hashMap = new HashMap<>();
/* 126 */         hashMap.put("fieldid", str6);
/* 127 */         hashMap.put("fieldname", str5);
/* 128 */         hashMap.put("fieldlabel", str7);
/* 129 */         hashMap.put("fieldval", str8);
/*     */         
/* 131 */         hashMap1.put(str6, hashMap);
/*     */       } 
/*     */     } 
/*     */     
/* 135 */     ArrayList<String> arrayList1 = new ArrayList();
/* 136 */     ArrayList<String> arrayList2 = new ArrayList();
/* 137 */     String str2 = "-999";
/* 138 */     String str3 = "";
/* 139 */     str1 = "select * from workflow_titleSet where flowId=" + paramInt2 + " order by gradation";
/* 140 */     recordSet1.executeSql(str1);
/* 141 */     while (recordSet1.next()) {
/* 142 */       String str = recordSet1.getString("fieldId");
/*     */       
/* 144 */       if (arrayList1.contains(str)) {
/*     */         continue;
/*     */       }
/* 147 */       str2 = str2 + "," + str;
/* 148 */       arrayList1.add(str);
/*     */     } 
/*     */     
/* 151 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 152 */     WorkflowBillComInfo workflowBillComInfo = new WorkflowBillComInfo();
/* 153 */     String str4 = "workflow_form";
/* 154 */     if (paramInt5 == 1) {
/* 155 */       str4 = workflowBillComInfo.getTablename(paramInt3 + "");
/*     */     }
/*     */     
/* 158 */     HashMap<Object, Object> hashMap3 = new HashMap<>();
/* 159 */     if (paramInt5 == 0) {
/* 160 */       str1 = "select workflow_formdict.id,workflow_formdict.fieldname,workflow_fieldlable.fieldlable as fieldlabel,fieldhtmltype,type,workflow_formdict.fielddbtype from workflow_formdict,workflow_fieldlable where workflow_formdict.id in(" + str2 + ") and workflow_formdict.id=workflow_fieldlable.fieldid and workflow_fieldlable.formid=" + paramInt3 + " and langurageid=" + paramInt4;
/* 161 */       recordSet1.execute(str1);
/*     */     } else {
/* 163 */       recordSet1.execute("select id,fieldname,fieldlabel,fieldhtmltype,type,fielddbtype from workflow_billfield where id in(" + str2 + ")");
/*     */     } 
/* 165 */     while (recordSet1.next()) {
/* 166 */       String str5 = recordSet1.getString("id");
/* 167 */       String str6 = recordSet1.getString("fieldname");
/* 168 */       arrayList2.add(str6);
/* 169 */       String str7 = recordSet1.getString("fieldlabel");
/* 170 */       String str8 = recordSet1.getString("fieldhtmltype");
/* 171 */       String str9 = recordSet1.getString("type");
/* 172 */       String str10 = recordSet1.getString("fielddbtype");
/* 173 */       ArrayList<String> arrayList = new ArrayList();
/* 174 */       arrayList.add(str6);
/* 175 */       arrayList.add(str7);
/* 176 */       arrayList.add(str8);
/* 177 */       arrayList.add(str9);
/* 178 */       arrayList.add(str10);
/* 179 */       hashMap3.put(str5, arrayList);
/*     */     } 
/*     */     
/* 182 */     if (arrayList2.size() > 0) {
/* 183 */       recordSet1.executeQuery("select " + CollectionUtil.list2String(arrayList2, ",") + " from " + str4 + " where requestid = ?", new Object[] { Integer.valueOf(paramInt1) });
/* 184 */       recordSet1.isAutoDecrypt(false);
/* 185 */       if (recordSet1.next()) {
/* 186 */         for (String str5 : arrayList2) {
/* 187 */           String str6 = Util.null2String(recordSet1.getString(str5));
/* 188 */           str6 = EncryptConfigBiz.forceFormatDecryptData(str6);
/* 189 */           hashMap2.put(str5, str6);
/*     */         } 
/*     */       }
/*     */     } 
/* 193 */     FieldInfo fieldInfo = new FieldInfo();
/* 194 */     fieldInfo.setRequestid(paramInt1);
/* 195 */     for (String str5 : arrayList1) {
/* 196 */       List<String> list = (List)hashMap3.get(str5);
/* 197 */       if ((list == null || list.isEmpty()) && !hashMap1.containsKey(str5)) {
/*     */         continue;
/*     */       }
/* 200 */       if (hashMap1.containsKey(str5)) {
/* 201 */         Map map = (Map)hashMap1.get(str5);
/* 202 */         String str11 = (String)map.get("fieldlabel");
/* 203 */         String str12 = (String)map.get("fieldval");
/* 204 */         if (str3.equals("")) {
/* 205 */           str3 = str11 + ":" + str12; continue;
/*     */         } 
/* 207 */         str3 = str3 + ", " + str11 + ":" + str12;
/*     */         continue;
/*     */       } 
/* 210 */       String str6 = list.get(1);
/* 211 */       String str7 = list.get(2);
/* 212 */       String str8 = list.get(3);
/* 213 */       String str9 = list.get(4);
/* 214 */       if (paramInt5 == 1) {
/* 215 */         str6 = SystemEnv.getHtmlLabelName(Util.getIntValue(str6, -1), paramInt4);
/*     */       }
/* 217 */       if ("7".equals(str7)) {
/* 218 */         str1 = "select type,fieldid,displayname,linkaddress,descriptivetext from workflow_specialfield a, workflow_billfield b where a.fieldid = b.id and a.isform='0' and a.isbill='1' and a.fieldid=" + str5;
/* 219 */         recordSet2.executeSql(str1);
/* 220 */         while (recordSet2.next()) {
/* 221 */           int i = recordSet2.getInt("type");
/* 222 */           String str11 = recordSet2.getString("fieldid");
/* 223 */           String str12 = "";
/* 224 */           if (i == 1) {
/*     */             
/* 226 */             str12 = recordSet2.getString("displayname");
/*     */           } else {
/* 228 */             str12 = recordSet2.getString("descriptivetext");
/*     */           } 
/* 230 */           if (!str12.equals("")) {
/* 231 */             if (str3.equals("")) {
/* 232 */               str3 = str6 + ":" + str12; continue;
/*     */             } 
/* 234 */             str3 = str3 + "," + str6 + ":" + str12;
/*     */           } 
/*     */         } 
/*     */         continue;
/*     */       } 
/* 239 */       String str10 = Util.null2String((String)hashMap2.get(list.get(0)));
/*     */       
/* 241 */       if (str7.equals("4") && "0".equals(str10)) {
/* 242 */         str10 = "";
/*     */       }
/*     */ 
/*     */       
/* 246 */       if (str7.equals("2") && str8.equals("2")) {
/* 247 */         str10 = Util.delHtmlWithSpace(str10);
/*     */       }
/* 249 */       if (str7.equals("3") && str8.equals("290")) {
/* 250 */         if (!str10.equals(""))
/*     */         {
/* 252 */           if (str3.equals("")) {
/* 253 */             str3 = str6 + ":" + str10;
/*     */           } else {
/* 255 */             str3 = str3 + ", " + str6 + ":" + str10;
/*     */           } 
/*     */         }
/* 258 */       } else if (str7.equals("3") && (str8.equals("161") || str8.equals("162"))) {
/* 259 */         str10 = fieldInfo.getFieldName(str10, Util.getIntValue(str8), str9);
/*     */       }
/* 261 */       else if (str7.equals("3") || str7.equals("6") || str7.equals("5")) {
/*     */         try {
/* 263 */           str10 = fieldValue.getFieldValue(user, Util.getIntValue(str5, 0), Util.getIntValue(str7, 0), Util.getIntValue(str8, 0), str10, paramInt5);
/* 264 */         } catch (Exception exception) {
/* 265 */           exception.printStackTrace();
/*     */         } 
/* 267 */       } else if (str7.equals("9") && 
/* 268 */         !str10.equals("")) {
/* 269 */         String str = "";
/*     */         
/* 271 */         String[] arrayOfString = str10.split("/////~~weaversplit~~/////");
/* 272 */         for (int i = arrayOfString.length - 1; i >= 0; i--) {
/* 273 */           if (!arrayOfString[i].equals("")) {
/*     */ 
/*     */             
/* 276 */             String[] arrayOfString1 = arrayOfString[i].split("////~~weaversplit~~////");
/* 277 */             if (!arrayOfString1[3].equals("")) {
/*     */               
/* 279 */               str = arrayOfString1[3]; break;
/*     */             } 
/*     */           } 
/*     */         } 
/* 283 */         str10 = str;
/*     */       } 
/*     */       
/* 286 */       if (!str10.equals("") && (!str7.equals("3") || !str8.equals("290")) && (
/* 287 */         !str7.equals("9") || !str10.equals(""))) {
/*     */         
/* 289 */         if (str3.equals("")) {
/* 290 */           str3 = str6 + ":" + str10; continue;
/*     */         } 
/* 292 */         str3 = str3 + ", " + str6 + ":" + str10;
/*     */       } 
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/* 298 */     return str3;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getMessage(int paramInt1, int paramInt2, int paramInt3, int paramInt4, int paramInt5) {
/* 309 */     return getCustomMessage(paramInt1, paramInt2, paramInt3, paramInt4, paramInt5);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getChats(int paramInt1, int paramInt2, int paramInt3, int paramInt4, int paramInt5) {
/* 321 */     return getCustomMessage(paramInt1, paramInt2, paramInt3, paramInt4, paramInt5);
/*     */   }
/*     */ 
/*     */   
/*     */   private String getCustomMessage(int paramInt1, int paramInt2, int paramInt3, int paramInt4, int paramInt5) {
/* 326 */     RecordSet recordSet1 = new RecordSet();
/* 327 */     RecordSet recordSet2 = new RecordSet();
/* 328 */     String str1 = "";
/* 329 */     String str2 = "workflow_form";
/* 330 */     RecordSet recordSet3 = new RecordSet();
/* 331 */     recordSet1.execute("select requestname from workflow_requestbase where requestid=" + paramInt1);
/* 332 */     if (recordSet1.next()) {
/* 333 */       str1 = Util.null2String(recordSet1.getString("requestname"));
/*     */     }
/* 335 */     String str3 = "select * from workflow_nodeCustomNewMenu where id=" + paramInt5;
/* 336 */     recordSet3.executeSql(str3);
/* 337 */     if (recordSet3.next()) {
/* 338 */       String str4 = Util.null2String(recordSet3.getString("newcustommessage"));
/* 339 */       if (!"".equals(str4)) {
/* 340 */         return getCustomMessageE9(paramInt1, paramInt2, paramInt3, paramInt4, paramInt5, str4);
/*     */       }
/*     */       
/* 343 */       int i = Util.getIntValue(recordSet3.getString("fieldid"), 0);
/* 344 */       String str5 = Util.null2String(recordSet3.getString("customMessage"));
/* 345 */       if (!"".equals(str5)) {
/* 346 */         str1 = str5 + " " + str1;
/*     */       }
/* 348 */       recordSet3.execute("select formid, isbill from workflow_base where id=" + paramInt2);
/* 349 */       int j = 0;
/* 350 */       int k = 0;
/* 351 */       if (recordSet3.next()) {
/* 352 */         j = Util.getIntValue(recordSet3.getString(1), 0);
/* 353 */         k = Util.getIntValue(recordSet3.getString(2), 0);
/*     */       } 
/* 355 */       if (k == 0) {
/* 356 */         str3 = "select workflow_formdict.fieldname, workflow_fieldlable.fieldlable, fieldhtmltype, type from workflow_formdict,workflow_fieldlable where workflow_formdict.id=" + i + " and workflow_formdict.id=workflow_fieldlable.fieldid and workflow_fieldlable.formid=" + j + " and langurageid=" + paramInt4;
/* 357 */         recordSet1.execute(str3);
/*     */       } else {
/* 359 */         recordSet1.execute("select fieldname, fieldlabel, fieldhtmltype, type from workflow_billfield where id=" + i);
/*     */       } 
/* 361 */       if (recordSet1.next()) {
/* 362 */         String str6 = recordSet1.getString(2);
/* 363 */         String str7 = recordSet1.getString(3);
/* 364 */         String str8 = recordSet1.getString(4);
/* 365 */         if (k == 1) {
/* 366 */           str6 = SystemEnv.getHtmlLabelName(Util.getIntValue(str6, -1), paramInt4);
/* 367 */           recordSet2.executeSql("select tablename from workflow_bill where id = " + j);
/* 368 */           if (recordSet2.next()) {
/* 369 */             str2 = recordSet2.getString(1);
/*     */           }
/*     */         } 
/* 372 */         recordSet1.execute("select " + recordSet1.getString(1) + " from " + str2 + " where requestid=" + paramInt1);
/* 373 */         if (recordSet1.next()) {
/* 374 */           String str = recordSet1.getString(1);
/* 375 */           if (str7.equals("3") || str7.equals("6") || str7.equals("5")) {
/*     */             try {
/* 377 */               RequestDoc requestDoc = new RequestDoc();
/* 378 */               str = requestDoc.getFieldValue(str7, str8, str, paramInt4, "" + i);
/* 379 */             } catch (Exception exception) {}
/*     */           }
/*     */           
/* 382 */           if (!recordSet1.getString(1).equals("")) {
/* 383 */             if (str1.equals("")) {
/* 384 */               str1 = str6 + ":" + str;
/*     */             } else {
/* 386 */               str1 = str1 + "（" + str6 + ":" + str + "）";
/*     */             } 
/*     */           }
/*     */         } 
/*     */       } 
/*     */     } 
/* 392 */     return str1;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   private String getCustomMessageE9(int paramInt1, int paramInt2, int paramInt3, int paramInt4, int paramInt5, String paramString) {
/* 398 */     RecordSet recordSet1 = new RecordSet();
/* 399 */     RecordSet recordSet2 = new RecordSet();
/* 400 */     RecordSet recordSet3 = new RecordSet();
/* 401 */     String str1 = "workflow_form";
/*     */     
/* 403 */     if (paramString.indexOf("$requestname$") > -1) {
/* 404 */       recordSet1.execute("select requestname from workflow_requestbase where requestid=" + paramInt1);
/* 405 */       if (recordSet1.next()) {
/* 406 */         paramString = paramString.replaceAll("\\$requestname\\$", recordSet1.getString(1));
/*     */       }
/*     */     } 
/* 409 */     String str2 = "\\$[0-9]+\\$";
/* 410 */     List list = ListUtil.getMatchers(str2, paramString);
/*     */     
/* 412 */     if (list.isEmpty()) {
/* 413 */       return paramString;
/*     */     }
/*     */     
/* 416 */     String str3 = "";
/* 417 */     String str4 = ListUtil.listToStr(list);
/* 418 */     recordSet3.execute("select formid, isbill from workflow_base where id=" + paramInt2);
/* 419 */     int i = 0;
/* 420 */     int j = 0;
/* 421 */     if (recordSet3.next()) {
/* 422 */       i = Util.getIntValue(recordSet3.getString(1), 0);
/* 423 */       j = Util.getIntValue(recordSet3.getString(2), 0);
/*     */     } 
/* 425 */     if (j == 0) {
/* 426 */       str3 = "select workflow_formdict.fieldname, workflow_fieldlable.fieldlable, fieldhtmltype, type,workflow_formdict.id from workflow_formdict,workflow_fieldlable where workflow_formdict.id in(" + str4 + ") and workflow_formdict.id=workflow_fieldlable.fieldid and workflow_fieldlable.formid=" + i + " and langurageid=" + paramInt4;
/*     */ 
/*     */       
/* 429 */       recordSet1.execute(str3);
/*     */     } else {
/* 431 */       recordSet1.execute("select fieldname, fieldlabel, fieldhtmltype, type,id from workflow_billfield where id in( " + str4 + " )");
/*     */     } 
/* 433 */     if (j == 1) {
/*     */       
/* 435 */       recordSet2.executeSql("select tablename from workflow_bill where id = " + i);
/* 436 */       if (recordSet2.next()) {
/* 437 */         str1 = recordSet2.getString(1);
/*     */       }
/*     */     } 
/* 440 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 441 */     while (recordSet1.next()) {
/* 442 */       HashMap<Object, Object> hashMap = new HashMap<>();
/*     */       
/* 444 */       String str5 = Util.null2String(recordSet1.getString(1));
/* 445 */       String str6 = Util.null2String(recordSet1.getString(2));
/* 446 */       String str7 = Util.null2String(recordSet1.getString(3));
/* 447 */       String str8 = Util.null2String(recordSet1.getString(4));
/* 448 */       String str9 = Util.null2String(recordSet1.getString(5));
/* 449 */       if (j == 1) {
/* 450 */         str6 = SystemEnv.getHtmlLabelName(Util.getIntValue(str6, -1), paramInt4);
/*     */       }
/* 452 */       hashMap.put("fieldname", str5);
/* 453 */       hashMap.put("tempLabel", str6);
/* 454 */       hashMap.put("htmtype", str7);
/* 455 */       hashMap.put("types", str8);
/* 456 */       hashMap.put("id", str9);
/* 457 */       arrayList.add(hashMap);
/*     */     } 
/*     */ 
/*     */     
/* 461 */     recordSet1.execute("select * from " + str1 + " where requestid=" + paramInt1);
/*     */     
/* 463 */     if (recordSet1.next()) {
/* 464 */       RequestDoc requestDoc = null;
/*     */       try {
/* 466 */         requestDoc = new RequestDoc();
/* 467 */       } catch (Exception exception) {
/* 468 */         exception.printStackTrace();
/*     */       } 
/* 470 */       for (Map<Object, Object> map : arrayList) {
/* 471 */         String str5 = "";
/*     */         try {
/* 473 */           str5 = recordSet1.getString((String)map.get("fieldname"));
/* 474 */         } catch (Exception exception) {
/* 475 */           writeLog("前端短信内发送解析主表内不存在" + (String)map.get("fieldname") + "字段");
/*     */           
/*     */           continue;
/*     */         } 
/* 479 */         String str6 = (String)map.get("tempLabel");
/* 480 */         String str7 = (String)map.get("htmtype");
/* 481 */         String str8 = (String)map.get("types");
/* 482 */         if (str7.equals("3") || str7.equals("6") || str7.equals("5")) {
/*     */           try {
/* 484 */             str5 = requestDoc.getFieldValue(str7, str8, str5, paramInt4, "" + (String)map.get("id"));
/* 485 */           } catch (Exception exception) {}
/*     */         }
/*     */ 
/*     */         
/* 489 */         if (!"".equals(str5)) {
/* 490 */           str5 = "（" + str6 + ":" + str5 + "）";
/*     */         }
/*     */         try {
/* 493 */           if (!"".equals(str5) && str5.indexOf("$") > -1)
/* 494 */             str5 = str5.replaceAll(str2, ""); 
/* 495 */           paramString = paramString.replaceAll("\\$" + (String)map.get("id") + "\\$", str5);
/*     */         }
/* 497 */         catch (Exception exception) {
/* 498 */           writeLog(exception);
/*     */         } 
/*     */       } 
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 508 */     return paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void sendMailAndMessage(int paramInt, ArrayList<String> paramArrayList, User paramUser) {
/* 519 */     int i = 0;
/* 520 */     String str1 = "";
/* 521 */     int j = 0;
/* 522 */     int k = 0;
/* 523 */     RecordSet recordSet1 = new RecordSet();
/*     */     
/* 525 */     recordSet1.executeSql("select workflowid,messageType,requestname,requestlevel from workflow_requestbase where requestid=" + paramInt);
/* 526 */     if (recordSet1.next()) {
/* 527 */       k = recordSet1.getInt("workflowid");
/* 528 */       i = recordSet1.getInt("messageType");
/* 529 */       str1 = recordSet1.getString("requestname");
/* 530 */       j = Util.getIntValue(recordSet1.getString("requestlevel"), 0);
/*     */     } 
/* 532 */     if (i == 1 || i == 2)
/*     */     {
/*     */       
/* 535 */       for (byte b = 0; b < paramArrayList.size(); b++) {
/* 536 */         String str = "" + paramArrayList.get(b);
/*     */         try {
/* 538 */           if ((i == 1 && !HrmUserVarify.isUserOnline(str)) || i == 2) {
/* 539 */             String str7 = this.resourceComInfo.getMobile(str);
/* 540 */             String str8 = "";
/* 541 */             String str9 = "";
/* 542 */             String str10 = "";
/* 543 */             if (!str8.equals("") || !str7.equals("")) {
/*     */               
/* 545 */               ArrayList arrayList1 = new ArrayList();
/* 546 */               ArrayList arrayList2 = new ArrayList();
/* 547 */               (new Thread(new SmsWorkRunnable(str7, str8, str9, str10, str1, paramUser, paramInt, arrayList1, arrayList2))).start();
/*     */             } 
/*     */           } 
/* 550 */         } catch (Exception exception) {}
/*     */       } 
/*     */     }
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 557 */     recordSet1.executeSql("select mailMessageType from  workflow_base  where id = " + k);
/* 558 */     String str2 = "0";
/* 559 */     String str3 = "-1";
/* 560 */     String str4 = "";
/*     */     
/* 562 */     String str5 = SystemEnv.getHtmlLabelName(21794, paramUser.getLanguage()) + "：";
/* 563 */     RecordSet recordSet2 = new RecordSet();
/* 564 */     if (recordSet1.next()) {
/* 565 */       str2 = recordSet1.getString("mailMessageType");
/*     */     }
/* 567 */     String str6 = "";
/* 568 */     recordSet2.executeSql("select oaaddress from systemset");
/* 569 */     if (recordSet2.next()) str6 = recordSet2.getString("oaaddress");
/*     */ 
/*     */ 
/*     */     
/* 573 */     if ("1".equals(str2)) {
/*     */       
/* 575 */       str5 = str5 + str1;
/* 576 */       str5 = str5 + "(" + Util.null2String(GetCustomLevelUtil.getLevel(j + "", paramUser.getLanguage())) + ")" + SystemEnv.getHtmlLabelName(21795, paramUser.getLanguage());
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 587 */       if (this.forwardflag == 3) {
/* 588 */         str5 = SystemEnv.getHtmlLabelName(23762, paramUser.getLanguage()) + "：(" + str1 + ")" + SystemEnv.getHtmlLabelName(23761, paramUser.getLanguage());
/* 589 */       } else if (this.forwardflag == 2) {
/* 590 */         str5 = SystemEnv.getHtmlLabelName(82578, paramUser.getLanguage()) + "：(" + str1 + ")" + SystemEnv.getHtmlLabelName(129591, paramUser.getLanguage());
/*     */       } 
/* 592 */       if (this.request != null || this.fu != null) {
/* 593 */         String str7 = "";
/* 594 */         if (this.isRequest && this.request != null) {
/* 595 */           str7 = Util.getRequestHost(this.request);
/*     */         }
/* 597 */         else if (this.fu != null) {
/* 598 */           str7 = Util.getRequestHost(this.fu.getRequest());
/*     */         } 
/* 600 */         if (!str6.equals("")) {
/* 601 */           str7 = str6;
/*     */         } else {
/* 603 */           str7 = "http://" + str7;
/*     */         } 
/* 605 */         String str8 = "login/Login.jsp";
/* 606 */         String str9 = "workflow/request/ViewRequest.jsp";
/* 607 */         if (GCONST.getMailReminderSet()) {
/* 608 */           str8 = GCONST.getMailLoginPage();
/* 609 */           str9 = GCONST.getMailGotoPage();
/*     */         } else {
/* 611 */           str8 = "login/LoginMail.jsp";
/*     */         } 
/*     */         
/* 614 */         if ("".equals(str8)) {
/* 615 */           str8 = "login/LoginMail.jsp";
/*     */         }
/*     */         
/* 618 */         if ("".equals(str9)) {
/* 619 */           str9 = "workflow/request/ViewRequest.jsp";
/*     */         }
/*     */ 
/*     */         
/* 623 */         if (this.forwardflag == 3) {
/*     */           
/* 625 */           str1 = SystemEnv.getHtmlLabelName(23762, paramUser.getLanguage()) + "：(<a style='text-decoration: underline; color: blue;cursor:hand'  target='_blank' href=\"" + str7 + "/" + str8 + "?gopage=/" + str9 + "?requestid=" + paramInt + "\" >" + str1 + "</a>)" + SystemEnv.getHtmlLabelName(23761, paramUser.getLanguage());
/* 626 */         } else if (this.forwardflag == 2) {
/*     */           
/* 628 */           str1 = SystemEnv.getHtmlLabelName(82578, paramUser.getLanguage()) + "：(<a style='text-decoration: underline; color: blue;cursor:hand'  target='_blank' href=\"" + str7 + "/" + str8 + "?gopage=/" + str9 + "?requestid=" + paramInt + "\" >" + str1 + "</a>)" + SystemEnv.getHtmlLabelName(129591, paramUser.getLanguage());
/*     */         } else {
/*     */           
/* 631 */           str1 = SystemEnv.getHtmlLabelName(21794, paramUser.getLanguage()) + "：(<a style='text-decoration: underline; color: blue;cursor:hand'  target='_blank' href=\"" + str7 + "/" + str8 + "?gopage=/" + str9 + "?requestid=" + paramInt + "\" >" + str1 + "</a>)" + SystemEnv.getHtmlLabelName(21795, paramUser.getLanguage());
/*     */         }
/*     */       
/* 634 */       } else if (!"".equals(str6)) {
/* 635 */         String str7 = str6;
/* 636 */         String str8 = "login/Login.jsp";
/* 637 */         String str9 = "workflow/request/ViewRequest.jsp";
/* 638 */         if (GCONST.getMailReminderSet()) {
/* 639 */           str8 = GCONST.getMailLoginPage();
/* 640 */           str9 = GCONST.getMailGotoPage();
/*     */         } else {
/* 642 */           str8 = "login/LoginMail.jsp";
/*     */         } 
/*     */         
/* 645 */         if ("".equals(str8)) {
/* 646 */           str8 = "login/LoginMail.jsp";
/*     */         }
/*     */         
/* 649 */         if ("".equals(str9)) {
/* 650 */           str9 = "workflow/request/ViewRequest.jsp";
/*     */         }
/*     */ 
/*     */         
/* 654 */         if (this.forwardflag == 3) {
/*     */           
/* 656 */           str1 = SystemEnv.getHtmlLabelName(23762, paramUser.getLanguage()) + "：(<a style='text-decoration: underline; color: blue;cursor:hand'  target='_blank' href=\"" + str7 + "/" + str8 + "?gopage=/" + str9 + "?requestid=" + paramInt + "\" >" + str1 + "</a>)" + SystemEnv.getHtmlLabelName(23761, paramUser.getLanguage());
/* 657 */         } else if (this.forwardflag == 2) {
/*     */           
/* 659 */           str1 = SystemEnv.getHtmlLabelName(82578, paramUser.getLanguage()) + "：(<a style='text-decoration: underline; color: blue;cursor:hand'  target='_blank' href=\"" + str7 + "/" + str8 + "?gopage=/" + str9 + "?requestid=" + paramInt + "\" >" + str1 + "</a>)" + SystemEnv.getHtmlLabelName(129591, paramUser.getLanguage());
/*     */         } else {
/*     */           
/* 662 */           str1 = SystemEnv.getHtmlLabelName(21794, paramUser.getLanguage()) + "：(<a style='text-decoration: underline; color: blue;cursor:hand'  target='_blank' href=\"" + str7 + "/" + str8 + "?gopage=/" + str9 + "?requestid=" + paramInt + "\" >" + str1 + "</a>)" + SystemEnv.getHtmlLabelName(21795, paramUser.getLanguage());
/*     */         
/*     */         }
/*     */       
/*     */       }
/* 667 */       else if (this.forwardflag == 3) {
/* 668 */         str1 = SystemEnv.getHtmlLabelName(23762, paramUser.getLanguage()) + "：(" + str1 + ")" + SystemEnv.getHtmlLabelName(23761, paramUser.getLanguage());
/* 669 */       } else if (this.forwardflag == 2) {
/* 670 */         str1 = SystemEnv.getHtmlLabelName(82578, paramUser.getLanguage()) + "：(" + str1 + ")" + SystemEnv.getHtmlLabelName(129591, paramUser.getLanguage());
/*     */       } else {
/* 672 */         str1 = SystemEnv.getHtmlLabelName(21794, paramUser.getLanguage()) + "：(" + str1 + ")" + SystemEnv.getHtmlLabelName(21795, paramUser.getLanguage());
/*     */       } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 680 */       for (byte b = 0; b < paramArrayList.size(); b++) {
/* 681 */         str3 = "" + paramArrayList.get(b);
/* 682 */         recordSet2.executeSql("select email from hrmresource where id =" + str3);
/* 683 */         if (recordSet2.next() && 
/* 684 */           !"".equals(recordSet2.getString("email")) && recordSet2.getString("email") != null) {
/* 685 */           str4 = str4 + recordSet2.getString("email") + ",";
/*     */         }
/*     */       } 
/*     */       
/* 689 */       if (!"".equals(str4))
/*     */       {
/* 691 */         (new Thread((Runnable)new EmailWorkRunnable(str4, str5, str1))).start();
/*     */       }
/*     */     } 
/*     */   }
/*     */   
/*     */   public int getForwardflag() {
/* 697 */     return this.forwardflag;
/*     */   }
/*     */   
/*     */   public void setForwardflag(int paramInt) {
/* 701 */     this.forwardflag = paramInt;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/MailAndMessage.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */