/*     */ package weaver.workflow.request;
/*     */ 
/*     */ import java.util.HashSet;
/*     */ import java.util.Set;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class RequestRemarkRight
/*     */   extends BaseBean
/*     */ {
/*     */   private int requestid;
/*     */   private int nodeid;
/*     */   private int workflow_currentid;
/*     */   
/*     */   public int getRequestid() {
/*  34 */     return this.requestid;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setRequestid(int paramInt) {
/*  41 */     this.requestid = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getNodeid() {
/*  48 */     return this.nodeid;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setNodeid(int paramInt) {
/*  55 */     this.nodeid = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getWorkflow_currentid() {
/*  62 */     return this.workflow_currentid;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setWorkflow_currentid(int paramInt) {
/*  69 */     this.workflow_currentid = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void saveRemarkRight(int paramInt, String paramString) {
/*  79 */     HashSet<String> hashSet1 = new HashSet();
/*     */     
/*  81 */     getViewUsers(hashSet1, this.workflow_currentid);
/*  82 */     getViewUsers4CY(hashSet1, this.workflow_currentid);
/*  83 */     String[] arrayOfString = paramString.split(",");
/*  84 */     for (byte b = 0; b < arrayOfString.length; b++) {
/*  85 */       String str1 = arrayOfString[b];
/*  86 */       if (!"".equals(str1) && !"-1".equals(str1)) {
/*  87 */         hashSet1.add(Util.getIntValue(str1, 0) + "");
/*     */       }
/*     */     } 
/*     */ 
/*     */     
/*  92 */     String str = "select userid from workflow_logviewusers where logid = " + paramInt;
/*  93 */     RecordSet recordSet1 = new RecordSet();
/*  94 */     recordSet1.executeSql(str);
/*  95 */     HashSet<String> hashSet2 = new HashSet();
/*  96 */     while (recordSet1.next()) {
/*  97 */       hashSet2.add(recordSet1.getString("userid"));
/*     */     }
/*     */     
/* 100 */     RecordSet recordSet2 = new RecordSet();
/* 101 */     for (String str1 : hashSet1) {
/*     */       
/* 103 */       if (hashSet2.contains(str1)) {
/*     */         continue;
/*     */       }
/* 106 */       String str2 = "insert into workflow_logviewusers (logid,userid) values (" + paramInt + "," + str1 + ")";
/* 107 */       recordSet2.executeSql(str2);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void deleteRemarkRight() {
/* 115 */     RecordSet recordSet1 = new RecordSet();
/* 116 */     RecordSet recordSet2 = new RecordSet();
/* 117 */     String str1 = "";
/* 118 */     String str2 = "";
/* 119 */     recordSet1.executeQuery("select workflowid from workflow_requestbase where requestid = ?", new Object[] { Integer.valueOf(this.requestid) });
/* 120 */     if (recordSet1.next()) {
/* 121 */       str2 = Util.null2String(recordSet1.getString("workflowid"));
/*     */     }
/* 123 */     int i = 0;
/* 124 */     String str3 = "select notseeeachother from workflow_flownode where workflowid = " + str2 + " and nodeid = " + this.nodeid;
/* 125 */     recordSet1.executeQuery(str3, new Object[0]);
/* 126 */     if (recordSet1.next()) {
/* 127 */       i = recordSet1.getInt("notseeeachother");
/*     */     }
/* 129 */     if (i == 1) {
/* 130 */       recordSet1.executeQuery("select a.logid,a.userid from workflow_logviewusers a,workflow_requestlog b where a.logid = b.logid and b.requestid = ? and b.nodeid = ?", new Object[] { Integer.valueOf(this.requestid), Integer.valueOf(this.nodeid) });
/* 131 */       while (recordSet1.next()) {
/* 132 */         String str4 = Util.null2String(recordSet1.getString("logid"));
/* 133 */         String str5 = Util.null2String(recordSet1.getString("userid"));
/* 134 */         recordSet2.executeQuery("select 1 from workflow_logviewusers_dellog where logid = ? and userid = ?", new Object[] { str4, str5 });
/* 135 */         if (!recordSet2.next()) {
/* 136 */           recordSet2.executeUpdate("insert into workflow_logviewusers_dellog(logid,userid)values(?,?)", new Object[] { str4, str5 });
/*     */         }
/*     */       } 
/*     */     } 
/* 140 */     if ("mysql".equals(recordSet1.getDBType())) {
/* 141 */       str1 = "DELETE t1 FROM  workflow_logviewusers t1,workflow_requestlog t2  WHERE t1.logid = t2.logid  AND t2.requestid = " + this.requestid + " AND t2.nodeid= " + this.nodeid;
/*     */     
/*     */     }
/*     */     else {
/*     */ 
/*     */       
/* 147 */       str1 = " delete from workflow_logviewusers where  exists (select 1 from workflow_requestlog where workflow_requestlog.logid = workflow_logviewusers.logid and workflow_requestlog.requestid = " + this.requestid + " and nodeid = " + this.nodeid + ")";
/*     */     } 
/*     */ 
/*     */     
/* 151 */     recordSet1.executeSql(str1);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void deleteAllRight() {
/* 158 */     RecordSet recordSet1 = new RecordSet();
/* 159 */     RecordSet recordSet2 = new RecordSet();
/* 160 */     String str1 = "";
/*     */     
/* 162 */     String str2 = "";
/* 163 */     recordSet1.executeQuery("select workflowid from workflow_requestbase where requestid = ?", new Object[] { Integer.valueOf(this.requestid) });
/* 164 */     if (recordSet1.next()) {
/* 165 */       str2 = Util.null2String(recordSet1.getString("workflowid"));
/*     */     }
/* 167 */     int i = 0;
/* 168 */     String str3 = "select notseeeachother from workflow_flownode where workflowid = " + str2 + " and nodeid = " + this.nodeid;
/* 169 */     recordSet1.executeQuery(str3, new Object[0]);
/* 170 */     if (recordSet1.next()) {
/* 171 */       i = recordSet1.getInt("notseeeachother");
/*     */     }
/* 173 */     if (i == 1) {
/* 174 */       recordSet1.executeQuery("select a.logid,a.userid from workflow_logviewusers a,workflow_requestlog b where a.logid = b.logid and b.requestid = ? and b.nodeid = ?", new Object[] { Integer.valueOf(this.requestid), Integer.valueOf(this.nodeid) });
/* 175 */       while (recordSet1.next()) {
/* 176 */         String str4 = Util.null2String(recordSet1.getString("logid"));
/* 177 */         String str5 = Util.null2String(recordSet1.getString("userid"));
/* 178 */         recordSet2.executeQuery("select 1 from workflow_logviewusers_dellog where logid = ? and userid = ?", new Object[] { str4, str5 });
/* 179 */         if (!recordSet2.next()) {
/* 180 */           recordSet2.executeUpdate("insert into workflow_logviewusers_dellog(logid,userid)values(?,?)", new Object[] { str4, str5 });
/*     */         }
/*     */       } 
/*     */     } 
/*     */     
/* 185 */     if ("mysql".equals(recordSet1.getDBType())) {
/* 186 */       str1 = "DELETE t1 FROM  workflow_logviewusers t1,workflow_requestlog t2  WHERE t1.logid = t2.logid  AND t2.requestid = " + this.requestid;
/*     */     
/*     */     }
/*     */     else {
/*     */       
/* 191 */       str1 = " delete from workflow_logviewusers where  exists (select 1 from workflow_requestlog where workflow_requestlog.logid = workflow_logviewusers.logid and workflow_requestlog.requestid = " + this.requestid + ")";
/*     */     } 
/*     */ 
/*     */     
/* 195 */     recordSet1.executeSql(str1);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private Set<String> getViewUsers(Set<String> paramSet, int paramInt) {
/* 204 */     String str = "select forwardid from workflow_forward where beforwardid = " + paramInt + " and forwardid <> beforwardid";
/* 205 */     RecordSet recordSet = new RecordSet();
/* 206 */     recordSet.executeSql(str);
/* 207 */     if (recordSet.next()) {
/* 208 */       int i = recordSet.getInt("forwardid");
/* 209 */       str = "select userid from workflow_currentoperator where id = " + i;
/* 210 */       RecordSet recordSet1 = new RecordSet();
/* 211 */       recordSet1.executeSql(str);
/* 212 */       if (recordSet1.next()) {
/* 213 */         paramSet.add(recordSet1.getInt("userid") + "");
/*     */       }
/* 215 */       getViewUsers(paramSet, i);
/*     */     } 
/* 217 */     return paramSet;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private Set<String> getViewUsers4CY(Set<String> paramSet, int paramInt) {
/* 226 */     if (paramInt < 0) return paramSet; 
/* 227 */     String str = "select becurrid from workflow_chuanyue where currid = " + paramInt + " and currid <> becurrid";
/* 228 */     RecordSet recordSet = new RecordSet();
/* 229 */     recordSet.executeSql(str);
/* 230 */     if (recordSet.next()) {
/* 231 */       int i = recordSet.getInt("becurrid");
/* 232 */       str = "select userid from workflow_currentoperator where id = " + i;
/* 233 */       RecordSet recordSet1 = new RecordSet();
/* 234 */       recordSet1.executeSql(str);
/* 235 */       if (recordSet1.next()) {
/* 236 */         paramSet.add(recordSet1.getInt("userid") + "");
/*     */       }
/* 238 */       getViewUsers4CY(paramSet, i);
/*     */     } 
/*     */     
/* 241 */     return paramSet;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getRightCondition(int paramInt1, int paramInt2, int paramInt3) {
/* 253 */     String str1 = " and 1=1";
/* 254 */     String str2 = "select mainrequestid,requestid,requestname,requestlevel,mainrequestid,creater,creatertype,createdate,createtime,workflowId,currentstatus,currentnodeid,currentnodetype,status,remindTypes,docids,crmids,prjids,cptids , lastnodeid  from workflow_requestbase where requestid=?";
/* 255 */     RecordSet recordSet = new RecordSet();
/* 256 */     recordSet.executeQuery(str2, new Object[] { Integer.valueOf(paramInt1) });
/* 257 */     int i = 0;
/* 258 */     String str3 = "";
/* 259 */     if (recordSet.next()) {
/* 260 */       i = recordSet.getInt("currentnodeid");
/* 261 */       str3 = recordSet.getString("currentnodetype");
/*     */     } 
/* 263 */     if (!"3".equals(str3)) {
/* 264 */       WFLinkInfo wFLinkInfo = new WFLinkInfo();
/* 265 */       String str = wFLinkInfo.getNowNodeids(paramInt1);
/* 266 */       if (!"".equals(str)) {
/* 267 */         String[] arrayOfString = str.split(",");
/* 268 */         String str4 = " 1=1";
/* 269 */         String str5 = " 1=2";
/* 270 */         for (byte b = 0; b < arrayOfString.length; b++) {
/* 271 */           String str6 = arrayOfString[b];
/* 272 */           String str7 = "select notseeeachother from workflow_flownode where workflowid = " + paramInt2 + " and nodeid = " + str6;
/* 273 */           recordSet.executeSql(str7);
/* 274 */           int j = 0;
/* 275 */           if (recordSet.next()) {
/* 276 */             j = recordSet.getInt(1);
/*     */           }
/* 278 */           if (j == 1) {
/* 279 */             str4 = str4 + " and t1.nodeid <> " + str6;
/* 280 */             str5 = str5 + " or (t1.nodeid = " + str6 + " and (not exists (select 1 from workflow_logviewusers tt where t1.logid = tt.logid)  or exists (select 1 from workflow_logviewusers ts where t1.logid = ts.logid and (ts.userid = " + paramInt3 + " or ts.userid = -1))))";
/*     */           } 
/*     */         } 
/*     */ 
/*     */ 
/*     */         
/* 286 */         str1 = str1 + " and ((" + str4 + ") or (" + str5 + "))";
/*     */       } 
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 301 */     return str1;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/RequestRemarkRight.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */