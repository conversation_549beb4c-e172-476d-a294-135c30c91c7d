/*      */ package weaver.workflow.request;
/*      */ 
/*      */ import com.api.odoc.util.OdocFileUtil;
/*      */ import com.api.odoc.util.RequestIdUtil;
/*      */ import com.engine.hrm.biz.HrmClassifiedProtectionBiz;
/*      */ import com.engine.workflow.biz.requestForm.RequestSecLevelBiz;
/*      */ import com.google.common.base.Strings;
/*      */ import java.net.URLDecoder;
/*      */ import java.util.ArrayList;
/*      */ import java.util.HashMap;
/*      */ import java.util.List;
/*      */ import java.util.Map;
/*      */ import org.apache.commons.lang.StringUtils;
/*      */ import weaver.common.StringUtil;
/*      */ import weaver.conn.RecordSet;
/*      */ import weaver.cpt.capital.CapitalComInfo;
/*      */ import weaver.crm.Maint.CustomerInfoComInfo;
/*      */ import weaver.docs.docs.DocComInfo;
/*      */ import weaver.docs.senddoc.DocReceiveUnitComInfo;
/*      */ import weaver.formmode.tree.CustomTreeUtil;
/*      */ import weaver.general.BaseBean;
/*      */ import weaver.general.StaticObj;
/*      */ import weaver.general.Util;
/*      */ import weaver.hrm.User;
/*      */ import weaver.hrm.company.DepartmentComInfo;
/*      */ import weaver.hrm.company.SubCompanyComInfo;
/*      */ import weaver.hrm.resource.ResourceComInfo;
/*      */ import weaver.interfaces.workflow.browser.Browser;
/*      */ import weaver.interfaces.workflow.browser.BrowserBean;
/*      */ import weaver.proj.Maint.ProjectInfoComInfo;
/*      */ import weaver.systeminfo.SystemEnv;
/*      */ import weaver.workflow.field.BrowserComInfo;
/*      */ import weaver.workflow.workflow.WorkflowComInfo;
/*      */ import weaver.workflow.workflow.WorkflowRequestComInfo;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ public class RequestDocDocAddExt
/*      */   extends BaseBean
/*      */ {
/*   60 */   private WorkflowComInfo workflowComInfo = new WorkflowComInfo();
/*   61 */   private User user = null;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setUser(User paramUser) {
/*   68 */     this.user = paramUser;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public List getBookmarkList(Map paramMap) {
/*   76 */     int i = 0;
/*   77 */     if (null != paramMap.get("requestId")) {
/*   78 */       i = Util.getIntValue(paramMap.get("requestId").toString());
/*      */     }
/*      */     
/*   81 */     int j = 0;
/*   82 */     if (null != paramMap.get("workflowId")) {
/*   83 */       j = Util.getIntValue(paramMap.get("workflowId").toString(), 0);
/*      */     }
/*   85 */     if (j == 0 && 
/*   86 */       null != paramMap.get("workflowid")) {
/*   87 */       j = Util.getIntValue(paramMap.get("workflowid").toString(), 0);
/*      */     }
/*      */     
/*   90 */     (new BaseBean()).writeLog("RequestDocDocAddExt.getBookmarkList  workflowId=" + j);
/*   91 */     int k = 0;
/*   92 */     if (null != paramMap.get("languageId")) {
/*   93 */       k = Util.getIntValue(paramMap.get("languageId").toString(), 0);
/*      */     } else {
/*   95 */       k = this.user.getLanguage();
/*      */     } 
/*   97 */     int m = 0;
/*   98 */     if (null != paramMap.get("editMouldId")) {
/*   99 */       m = Util.getIntValue(paramMap.get("editMouldId").toString(), 0);
/*      */     }
/*  101 */     int n = 0;
/*  102 */     if (null != paramMap.get("reShowMouldId")) {
/*  103 */       n = Util.getIntValue(paramMap.get("reShowMouldId").toString(), 0);
/*      */     }
/*  105 */     int i1 = 0;
/*  106 */     if (null != paramMap.get("ISSHOWMOULD")) {
/*  107 */       i1 = Util.getIntValue(paramMap.get("ISSHOWMOULD").toString(), 0);
/*      */     }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  114 */     RecordSet recordSet = new RecordSet();
/*      */     
/*  116 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*      */     try {
/*  118 */       RequestDoc requestDoc = new RequestDoc();
/*  119 */       ArrayList<String> arrayList1 = requestDoc.getDocFiled("" + j);
/*      */       
/*  121 */       if (arrayList1 != null && arrayList1.size() > 0) {
/*      */         
/*  123 */         ArrayList arrayList2 = new ArrayList();
/*  124 */         ArrayList arrayList3 = new ArrayList();
/*  125 */         ArrayList<String> arrayList4 = new ArrayList();
/*  126 */         ArrayList<String> arrayList5 = new ArrayList();
/*      */         
/*  128 */         ArrayList arrayList6 = new ArrayList();
/*  129 */         ArrayList arrayList7 = new ArrayList();
/*  130 */         ArrayList arrayList8 = new ArrayList();
/*  131 */         ArrayList arrayList9 = new ArrayList();
/*  132 */         String str1 = "";
/*      */         
/*  134 */         String str2 = "";
/*      */         
/*  136 */         int i2 = Util.getIntValue(this.workflowComInfo.getFormId("" + j), 0);
/*  137 */         String str3 = Util.null2String(this.workflowComInfo.getIsBill("" + j));
/*  138 */         if (!str3.equals("1")) {
/*  139 */           str3 = "0";
/*      */         }
/*      */         
/*  142 */         writeLog("===getBookmarkList==========requestId:" + i + ",isBill=" + str3 + ",formId=" + i2);
/*  143 */         String str4 = "";
/*  144 */         String str5 = "-1";
/*  145 */         String str6 = "" + arrayList1.get(3);
/*      */         
/*  147 */         Object object = null;
/*  148 */         String str7 = null;
/*  149 */         String str8 = "";
/*  150 */         String str9 = "";
/*  151 */         if (i1 == 1) {
/*  152 */           if (n > 0) {
/*  153 */             str9 = "  and docMouldId=" + n + " ";
/*      */           }
/*      */         }
/*  156 */         else if (m > 0) {
/*  157 */           str8 = "  and docMouldId=" + m + " ";
/*      */         } 
/*      */ 
/*      */         
/*  161 */         String str10 = "workflow_form";
/*  162 */         if (str3.equals("1")) {
/*  163 */           recordSet.executeSql("select tablename from workflow_bill where id = " + i2);
/*  164 */           if (recordSet.next()) {
/*  165 */             str10 = Util.null2String(recordSet.getString("tablename"));
/*      */           }
/*      */         } 
/*      */ 
/*      */         
/*  170 */         writeLog("===getBookmarkList==========ISUSEURLPARAM:" + paramMap.get("ISUSEURLPARAM"));
/*  171 */         writeLog("===getBookmarkList==========parpMap:" + paramMap.toString());
/*      */         
/*  173 */         int i3 = Util.getIntValue(Util.null2String(paramMap.get("docid")), -1);
/*  174 */         if (i3 < 0 && i > 0) {
/*  175 */           i3 = Util.getIntValue(RequestIdUtil.getDocumentTextIdByRequestId("" + i, new User(1)));
/*      */         }
/*  177 */         String str11 = Util.null2String(paramMap.get("SELECTEDITTEMPLATE"));
/*  178 */         writeLog("===getBookmarkList==========docid=" + i3 + "selectEditTemplate=" + str11 + "isShowMould=" + i1);
/*  179 */         if (i3 > 0 && !str11.equals("selectEditTemplate") && i1 != 1) {
/*  180 */           recordSet.executeQuery("select editMouldId,editSelectValue from odoc_docMouldInfo where docid = ?", new Object[] { Integer.valueOf(i3) });
/*  181 */           if (recordSet.next()) {
/*  182 */             str5 = Util.null2String(recordSet.getString(2));
/*  183 */             m = Util.getIntValue(recordSet.getString(1));
/*  184 */             if (m > 0) {
/*  185 */               str8 = "  and docMouldId=" + m + " ";
/*      */             }
/*      */           } 
/*  188 */         } else if (paramMap.get("ISUSEURLPARAM") != null && paramMap.get("ISUSEURLPARAM").equals("true")) {
/*  189 */           if (i1 == 1) {
/*  190 */             if (Util.getIntValue((String)paramMap.get("showMouldFieldid")) >= 0) {
/*  191 */               str5 = "" + Util.getIntValue((String)paramMap.get("field" + paramMap.get("showMouldFieldid")), 0);
/*  192 */               String str = Util.null2String(paramMap.get("isShowSelectField"));
/*  193 */               writeLog("===selectValue==========selectValue:" + str5 + "parpMap.get(\"showMouldFieldid\")=" + paramMap.get("showMouldFieldid"));
/*  194 */               if ("false".equals(str)) {
/*  195 */                 str5 = Util.null2String(Integer.valueOf(Util.getIntValue(str5, 0) + 100));
/*      */               }
/*  197 */               if (isDocNumber((String)paramMap.get("showMouldFieldid"))) {
/*  198 */                 str5 = "" + (Util.getIntValue(str5) * -1);
/*      */               }
/*      */             }
/*      */           
/*  202 */           } else if (Util.getIntValue((String)paramMap.get("editMouldFieldid")) >= 0) {
/*  203 */             str5 = "" + Util.getIntValue((String)paramMap.get("field" + paramMap.get("editMouldFieldid")), 0);
/*  204 */             String str = Util.null2String(paramMap.get("isSelectField"));
/*  205 */             if ("false".equals(str)) {
/*  206 */               str5 = Util.null2String(Integer.valueOf(Util.getIntValue(str5, 0) + 100));
/*      */             }
/*      */           } 
/*      */         } else {
/*      */ 
/*      */           
/*      */           try {
/*  213 */             String str = arrayList1.get(11).toString();
/*  214 */             writeLog("get selectValue tmpShowMouldFieldId" + str + " requestid=" + i);
/*  215 */             if (StringUtil.isNotNull(str)) {
/*  216 */               str5 = (new RequestIdUtil()).getFieldValueByRequestIdAndFieldId(i, Integer.valueOf(str).intValue());
/*  217 */               writeLog("get selectValue1 selectValue" + str5);
/*  218 */               if (isDocNumber(str)) {
/*  219 */                 str5 = "" + ((Util.getIntValue(str5) + 100) * -1);
/*      */               }
/*  221 */               writeLog("get selectValue2 selectValue" + str5);
/*      */             } 
/*  223 */           } catch (NumberFormatException numberFormatException) {
/*  224 */             writeLog("exception:", numberFormatException);
/*  225 */             writeLog("get selectValue exception! requestid=" + i);
/*      */           } 
/*      */         } 
/*  228 */         writeLog("===getBookmarkList==========selectValue:" + str5);
/*      */         
/*  230 */         if (i1 == 1) {
/*  231 */           str7 = "select * from workflow_docshow where flowId=" + j + " and (fieldid<>-1 or (fieldid=-1 and workflowcontenttype=2)) and fieldid>-100 and selectItemId=" + str5 + " " + str9 + " order by fieldId";
/*      */         } else {
/*  233 */           str7 = "select * from workflow_docshowedit where flowId=" + j + " and fieldid<>-1 and fieldid>-100 and selectItemId=" + str5 + " " + str8 + " order by fieldId";
/*      */         } 
/*      */ 
/*      */         
/*  237 */         recordSet.execute(str7);
/*  238 */         if (!recordSet.next()) {
/*  239 */           if (i1 == 1) {
/*  240 */             str7 = "select * from workflow_docshow where flowId=" + j + " and (fieldid<>-1 or (fieldid=-1 and workflowcontenttype=2)) and fieldid>-100 and selectItemId=-1 " + str9 + " order by fieldId";
/*      */           } else {
/*  242 */             str7 = "select * from workflow_docshowedit where flowId=" + j + " and fieldid<>-1 and fieldid>-100 and selectItemId=-1 " + str8 + " order by fieldId";
/*      */           } 
/*      */         }
/*      */         
/*  246 */         String str12 = null;
/*  247 */         String str13 = null;
/*  248 */         String str14 = null;
/*  249 */         String str15 = null;
/*  250 */         String str16 = null;
/*  251 */         String str17 = null;
/*      */ 
/*      */ 
/*      */         
/*  255 */         ArrayList<String> arrayList10 = new ArrayList();
/*  256 */         HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  257 */         HashMap<Object, Object> hashMap2 = null;
/*  258 */         ArrayList<HashMap<Object, Object>> arrayList11 = new ArrayList();
/*  259 */         String str18 = "";
/*  260 */         ArrayList<String> arrayList12 = new ArrayList();
/*  261 */         ArrayList<String> arrayList13 = new ArrayList();
/*  262 */         ArrayList<String> arrayList14 = new ArrayList();
/*  263 */         ArrayList<String> arrayList15 = new ArrayList();
/*  264 */         ArrayList<String> arrayList16 = new ArrayList();
/*  265 */         String str19 = "";
/*      */         
/*  267 */         writeLog("===getBookmarkList======1====sqlEdit:" + str7);
/*  268 */         recordSet.execute(str7);
/*  269 */         while (recordSet.next()) {
/*  270 */           if (i1 == 1) {
/*  271 */             if ("2".equals(recordSet.getString("workflowcontenttype"))) {
/*  272 */               if (StringUtil.isNotNull(recordSet.getString("modulId")))
/*  273 */                 str1 = str1 + recordSet.getString("modulId") + ","; 
/*      */               continue;
/*      */             } 
/*  276 */             arrayList4.add(recordSet.getString("modulId"));
/*  277 */             str18 = str18 + recordSet.getString("fieldId") + ",";
/*  278 */             arrayList10.add(recordSet.getString("fieldId"));
/*  279 */             arrayList15.add(Util.null2String(recordSet.getString("dateShowType")));
/*      */             continue;
/*      */           } 
/*  282 */           arrayList4.add(recordSet.getString("modulId"));
/*  283 */           str18 = str18 + recordSet.getString("fieldId") + ",";
/*  284 */           arrayList10.add(recordSet.getString("fieldId"));
/*  285 */           arrayList15.add(Util.null2String(recordSet.getString("dateShowType")));
/*      */         } 
/*      */ 
/*      */         
/*  289 */         if (StringUtil.isNotNull(str1) && str1.endsWith(",")) {
/*  290 */           str1 = str1.substring(0, str1.length() - 1);
/*      */         }
/*  292 */         str18 = str18 + "-100";
/*  293 */         if (!str18.equals("")) {
/*  294 */           if (str3.equals("1")) {
/*  295 */             str7 = "select a.fieldname,a.fieldhtmltype,a.type, a.id,a.fielddbtype from workflow_billfield a where a.id in (" + str18 + ") order by id";
/*      */           } else {
/*  297 */             str7 = "select a.fieldname,a.fieldhtmltype,a.type, a.id,a.fielddbtype from workflow_formdict a where a.id in (" + str18 + ") order by id";
/*      */           } 
/*      */         }
/*      */         
/*  301 */         writeLog("===getBookmarkList======2====sqlEdit:" + str7);
/*  302 */         recordSet.execute(str7);
/*  303 */         int i4 = 0;
/*  304 */         while (recordSet.next()) {
/*  305 */           str13 = recordSet.getString(1);
/*  306 */           str14 = recordSet.getString(2);
/*  307 */           str15 = recordSet.getString(3);
/*  308 */           str16 = recordSet.getString(4);
/*  309 */           str17 = recordSet.getString(5);
/*      */           
/*  311 */           if (str16 != null && !str16.trim().equals("")) {
/*  312 */             hashMap1.put(str16, String.valueOf(i4));
/*      */           }
/*      */           
/*  315 */           hashMap2 = new HashMap<>();
/*  316 */           hashMap2.put("tempFieldName", str13);
/*  317 */           hashMap2.put("tempFieldHtmlType", str14);
/*  318 */           hashMap2.put("tempType", str15);
/*  319 */           hashMap2.put("tempId", str16);
/*  320 */           hashMap2.put("tempFieldDbType", str17);
/*      */           
/*  322 */           arrayList11.add(hashMap2);
/*      */           
/*  324 */           i4++;
/*      */         } 
/*      */         
/*  327 */         writeLog("===getBookmarkList==========fieldIdListEdit:" + arrayList10.size()); int i5;
/*  328 */         for (i5 = 0; i5 < arrayList10.size(); i5++) {
/*  329 */           str12 = arrayList10.get(i5);
/*  330 */           i4 = Util.getIntValue((String)hashMap1.get(str12), 0);
/*      */ 
/*      */           
/*  333 */           if (arrayList11.size() > 0) {
/*  334 */             Map map = arrayList11.get(i4);
/*  335 */             str13 = (String)map.get("tempFieldName");
/*  336 */             str14 = (String)map.get("tempFieldHtmlType");
/*  337 */             str15 = (String)map.get("tempType");
/*  338 */             str16 = (String)map.get("tempId");
/*  339 */             str17 = (String)map.get("tempFieldDbType");
/*      */           } else {
/*  341 */             str13 = "1";
/*  342 */             str14 = "";
/*  343 */             str15 = "";
/*  344 */             str16 = "";
/*  345 */             str17 = "";
/*      */           } 
/*      */           
/*  348 */           if ("-3".equals(str12)) {
/*  349 */             str16 = "-3";
/*      */           }
/*  351 */           if ("-10".equals(str12)) {
/*  352 */             str16 = "-10";
/*      */           }
/*      */           
/*  355 */           arrayList12.add(str14);
/*  356 */           arrayList13.add(str15);
/*  357 */           arrayList14.add(str16);
/*  358 */           arrayList16.add(str17);
/*  359 */           if (str19.equals("")) {
/*  360 */             str19 = str13;
/*      */           } else {
/*  362 */             str19 = str19 + "," + str13;
/*      */           } 
/*      */         } 
/*  365 */         writeLog("===getBookmarkList==========fieldIdListEdit.size():" + arrayList10.size());
/*      */         
/*  367 */         if (!str19.equals("")) {
/*  368 */           writeLog("===getBookmarkList==========ISUSEURLPARAM:" + paramMap.get("ISUSEURLPARAM"));
/*      */           
/*  370 */           if (paramMap.get("ISUSEURLPARAM") != null && paramMap.get("ISUSEURLPARAM").equals("true")) {
/*      */             
/*  372 */             for (i5 = 0; i5 < arrayList10.size(); i5++) {
/*  373 */               String str21 = arrayList12.get(i5);
/*  374 */               String str22 = arrayList13.get(i5);
/*  375 */               String str23 = arrayList14.get(i5);
/*  376 */               String str24 = arrayList15.get(i5);
/*  377 */               String str25 = arrayList16.get(i5);
/*  378 */               String str26 = URLDecoder.decode(Util.null2String(paramMap.get("field" + arrayList10.get(i5))), "UTF-8");
/*  379 */               if ("-1".equals(str23)) {
/*  380 */                 str26 = "";
/*  381 */               } else if ("-3".equals(str23)) {
/*      */                 try {
/*  383 */                   str26 = URLDecoder.decode((String)paramMap.get("requestname"), "UTF-8");
/*  384 */                 } catch (Exception exception) {
/*  385 */                   str26 = "";
/*  386 */                   writeLog("------ decode requestname error :", exception);
/*      */                 } 
/*  388 */               } else if ("-10".equals(str23)) {
/*  389 */                 String str27 = Util.null2String(paramMap.get("field-10"));
/*  390 */                 String str28 = "";
/*  391 */                 if (!"".equals(str27)) {
/*  392 */                   str28 = HrmClassifiedProtectionBiz.getResourceClassificationValidityDefaultValue(str27);
/*      */                 }
/*  394 */                 if ("".equals(str27) && i > 0) {
/*  395 */                   Map map = (new RequestSecLevelBiz()).getSecInfoByRequestId(Util.getIntValue(i), this.user);
/*  396 */                   str27 = Util.null2String(map.get("secLevel"));
/*  397 */                   str28 = Util.null2String(map.get("secValidity"));
/*      */                 } 
/*      */                 
/*  400 */                 String str29 = (new HrmClassifiedProtectionBiz()).getResourceSecLevelShowName(str27 + "", k + "");
/*  401 */                 str26 = ("1".equals(str24) || "".equals(str28)) ? str29 : (str29 + "★" + str28);
/*  402 */               } else if ("2".equals(str21) && "2".equals(str22)) {
/*      */                 
/*  404 */                 str26 = str26.replace("​", "");
/*  405 */                 str26 = str26.replace("<p>", "<br>");
/*  406 */                 str26 = str26.replace("</p>", "</br>");
/*  407 */                 str26 = Util.delHtmlWithSpace(str26);
/*  408 */                 str26 = str26.replace("&dt;&at;", "\n");
/*  409 */               } else if ("1".equals(str21) && ("5".equals(str22) || "4".equals(str22))) {
/*  410 */                 str26 = getValueByAmountDateShowType(str26, str24, str22);
/*  411 */               } else if (!str21.equals("3") && !str21.equals("6") && !str21.equals("5") && !str21.equals("4")) {
/*  412 */                 str26 = str26.replaceAll("\n\n", "\n \n");
/*  413 */               } else if ("3".equals(str21) && "2".equals(str22)) {
/*  414 */                 str26 = getValueByDateShowType(str26, str24);
/*      */               } else {
/*  416 */                 str26 = getFieldValue(str21, str22, str26, k, str23, str3, str25, Util.getIntValue(str24, -9), str24);
/*      */               } 
/*  418 */               str26 = Util.formatMultiLang(str26, k + "");
/*  419 */               str26 = OdocFileUtil.richTextReplaceHtml(str26);
/*  420 */               writeLog("===getBookmarkList====1======tempvalue:" + str26);
/*  421 */               arrayList5.add(str26);
/*      */             }
/*      */           
/*      */           } else {
/*      */             
/*  426 */             String str = "";
/*  427 */             recordSet.executeSql("select requestName from workflow_requestbase where requestId=" + i);
/*  428 */             if (recordSet.next()) {
/*  429 */               str = Util.null2String(recordSet.getString("requestName"));
/*      */             }
/*  431 */             str7 = "select " + str19 + " from " + str10 + " where requestid=" + i;
/*  432 */             writeLog("===getBookmarkList=====3=====sqlEdit:" + str7);
/*      */             
/*  434 */             recordSet.execute(str7);
/*  435 */             if (recordSet.next()) {
/*  436 */               for (byte b = 0; b < arrayList4.size(); b++) {
/*  437 */                 String str21 = arrayList12.get(b);
/*  438 */                 String str22 = arrayList13.get(b);
/*  439 */                 String str23 = arrayList14.get(b);
/*  440 */                 String str24 = arrayList15.get(b);
/*  441 */                 String str25 = arrayList16.get(b);
/*  442 */                 String str26 = recordSet.getString(b + 1);
/*  443 */                 if ("-3".equals(str23)) {
/*  444 */                   str26 = str;
/*  445 */                 } else if ("-10".equals(str23)) {
/*  446 */                   String str27 = Util.null2String(paramMap.get("field-10"));
/*  447 */                   String str28 = "";
/*  448 */                   if (!"".equals(str27)) {
/*  449 */                     str28 = HrmClassifiedProtectionBiz.getResourceClassificationValidityDefaultValue(str27);
/*      */                   }
/*  451 */                   if ("".equals(str27) && i > 0) {
/*  452 */                     Map map = (new RequestSecLevelBiz()).getSecInfoByRequestId(Util.getIntValue(i), this.user);
/*  453 */                     str27 = Util.null2String(map.get("secLevel"));
/*  454 */                     str28 = Util.null2String(map.get("secValidity"));
/*      */                   } 
/*      */                   
/*  457 */                   String str29 = (new HrmClassifiedProtectionBiz()).getResourceSecLevelShowName(str27 + "", k + "");
/*  458 */                   str26 = ("1".equals(str24) || "".equals(str28)) ? str29 : (str29 + "★" + str28);
/*  459 */                 } else if ("2".equals(str21) && "2".equals(str22)) {
/*      */                   
/*  461 */                   writeLog("===getBookmarkList====1111======tempvalue:" + str26);
/*  462 */                   str26 = str26.replace("​", "");
/*  463 */                   str26 = str26.replace("</br>", "<br>");
/*  464 */                   str26 = str26.replace("<br />", "<br>");
/*      */ 
/*      */                   
/*  467 */                   writeLog("===getBookmarkList====1112======tempvalue:" + str26);
/*  468 */                   str26 = Util.delHtmlWithSpace(str26);
/*  469 */                   writeLog("===getBookmarkList====1113======tempvalue:" + str26);
/*  470 */                   str26 = str26.replace("&dt;&at;", "\n");
/*  471 */                 } else if ("1".equals(str21) && ("5".equals(str22) || "4".equals(str22))) {
/*  472 */                   str26 = getValueByAmountDateShowType(str26, str24, str22);
/*  473 */                 } else if (!str21.equals("3") && !str21.equals("6") && !str21.equals("5") && !str21.equals("4")) {
/*  474 */                   str26 = recordSet.getString(b + 1);
/*  475 */                   str26 = str26.replaceAll("\n\n", "\n \n");
/*  476 */                 } else if ("3".equals(str21) && "2".equals(str22)) {
/*  477 */                   str26 = getValueByDateShowType(str26, str24);
/*      */                 } else {
/*  479 */                   str26 = getFieldValue(str21, str22, str26, k, str23, str3, str25, Util.getIntValue(str24, -9), str24);
/*      */                 } 
/*  481 */                 str26 = Util.formatMultiLang(str26, k + "");
/*  482 */                 str26 = OdocFileUtil.richTextReplaceHtml(str26);
/*  483 */                 writeLog("===getBookmarkList====2======tempvalue:" + str26);
/*  484 */                 arrayList5.add(str26);
/*      */               } 
/*      */             }
/*      */           } 
/*      */         } 
/*      */ 
/*      */ 
/*      */         
/*  492 */         i5 = (arrayList4.size() <= arrayList5.size()) ? arrayList4.size() : arrayList5.size();
/*  493 */         int i6 = 0;
/*  494 */         String str20 = null;
/*  495 */         HashMap<Object, Object> hashMap3 = null; int i7;
/*  496 */         for (i7 = 0; i7 < i5; i7++) {
/*  497 */           i6 = Util.getIntValue(arrayList4.get(i7), -1);
/*  498 */           str20 = arrayList5.get(i7);
/*  499 */           str20 = fromHtmlToWordDsp(str20);
/*      */           
/*  501 */           hashMap3 = new HashMap<>();
/*  502 */           hashMap3.put("bookmarkid", "" + i6);
/*  503 */           hashMap3.put("bookmarkvalue", str20);
/*  504 */           writeLog("===getBookmarkList====22222222======tempBookMarkValue:" + str20);
/*  505 */           arrayList.add(hashMap3);
/*      */         } 
/*      */         
/*  508 */         i7 = Util.getIntValue(Util.null2String(paramMap.get("docid")), -1);
/*  509 */         if (i7 > 0 && StringUtil.isNotNull(str1)) {
/*  510 */           recordSet.executeQuery("select bookmarkid,bookmarkvalue from docmouldbookmark where docid=? and mouldid=? and bookmarkid in(" + str1 + ")", new Object[] { Integer.valueOf(i7), Integer.valueOf(n) });
/*  511 */           while (recordSet.next()) {
/*  512 */             hashMap3 = new HashMap<>();
/*  513 */             hashMap3.put("bookmarkid", recordSet.getString("bookmarkid"));
/*  514 */             hashMap3.put("bookmarkvalue", recordSet.getString("bookmarkvalue"));
/*  515 */             arrayList.add(hashMap3);
/*      */           }
/*      */         
/*      */         } 
/*      */       } 
/*  520 */     } catch (Exception exception) {
/*  521 */       (new BaseBean()).writeLog("======RequestDocDocAddExt==========exception:", exception);
/*      */     } 
/*      */     
/*  524 */     return arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private boolean isDocNumber(String paramString) {
/*  536 */     boolean bool = false;
/*  537 */     writeLog("=======================================isDocNumber fieldId=" + paramString);
/*  538 */     if (StringUtil.isNotNull(paramString)) {
/*  539 */       RecordSet recordSet = new RecordSet();
/*  540 */       String str = "select type,fieldhtmltype from workflow_billfield where id=?";
/*  541 */       writeLog("=======================================isDocNumber sql4SelectValue=" + str + " fieldId=" + paramString);
/*  542 */       recordSet.executeQuery(str, new Object[] { paramString });
/*  543 */       if (recordSet.next()) {
/*  544 */         String str1 = Util.null2String(recordSet.getString("type"));
/*  545 */         String str2 = Util.null2String(recordSet.getString("fieldhtmltype"));
/*  546 */         writeLog("=======================================isDocNumber fieldHtmlType=" + str2 + " type=" + str1);
/*  547 */         if ("3".equals(str2) && "324".equals(str1)) {
/*  548 */           bool = true;
/*      */         }
/*      */       } 
/*      */     } 
/*  552 */     return bool;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private String getFieldValue(String paramString1, String paramString2, String paramString3, int paramInt, String paramString4, String paramString5, String paramString6) throws Exception {
/*  567 */     return getFieldValue(paramString1, paramString2, paramString3, paramInt, paramString4, paramString5, paramString6, -9, "");
/*      */   }
/*      */   
/*      */   private String getFieldValue(String paramString1, String paramString2, String paramString3, int paramInt1, String paramString4, String paramString5, String paramString6, int paramInt2, String paramString7) throws Exception {
/*  571 */     String str1 = "";
/*  572 */     String str2 = "";
/*  573 */     String str3 = OdocFileUtil.getProcessFieldType(paramString7);
/*  574 */     RecordSet recordSet = new RecordSet();
/*  575 */     if (paramString1.equals("3")) {
/*  576 */       if (paramString2.equals("2") || paramString2.equals("19") || paramString2.equals("290") || paramString2.equals("403") || paramString2.equals("402")) {
/*  577 */         str1 = paramString3;
/*  578 */       } else if (!paramString3.equals("")) {
/*  579 */         ArrayList<String> arrayList = Util.TokenizerString(paramString3, ",");
/*  580 */         if (paramString2.equals("8") || paramString2.equals("135")) {
/*      */           
/*  582 */           ProjectInfoComInfo projectInfoComInfo = new ProjectInfoComInfo();
/*  583 */           for (byte b = 0; b < arrayList.size(); b++) {
/*  584 */             str1 = str1 + projectInfoComInfo.getProjectInfoname(arrayList.get(b)) + str3;
/*      */           }
/*  586 */           str1 = OdocFileUtil.getProcessFieldValue(str1, paramString7);
/*  587 */         } else if (paramString2.equals("1") || paramString2.equals("17") || paramString2.equals("166")) {
/*      */           
/*  589 */           ResourceComInfo resourceComInfo = new ResourceComInfo();
/*  590 */           for (byte b = 0; b < arrayList.size(); b++) {
/*  591 */             str1 = str1 + resourceComInfo.getResourcename(arrayList.get(b)) + str3;
/*      */           }
/*  593 */           str1 = OdocFileUtil.getProcessFieldValue(str1, paramString7);
/*  594 */         } else if (paramString2.equals("7") || paramString2.equals("18")) {
/*      */           
/*  596 */           CustomerInfoComInfo customerInfoComInfo = new CustomerInfoComInfo();
/*  597 */           for (byte b = 0; b < arrayList.size(); b++) {
/*  598 */             str1 = str1 + customerInfoComInfo.getCustomerInfoname(arrayList.get(b)) + str3;
/*      */           }
/*  600 */           str1 = OdocFileUtil.getProcessFieldValue(str1, paramString7);
/*  601 */         } else if (paramString2.equals("4") || paramString2.equals("57")) {
/*      */           
/*  603 */           DepartmentComInfo departmentComInfo = new DepartmentComInfo();
/*  604 */           for (byte b = 0; b < arrayList.size(); b++) {
/*  605 */             str1 = str1 + departmentComInfo.getDepartmentname(arrayList.get(b)) + str3;
/*      */           }
/*  607 */           str1 = OdocFileUtil.getProcessFieldValue(str1, paramString7);
/*  608 */         } else if (paramString2.equals("164") || paramString2.equals("194") || paramString2.equals("170")) {
/*      */           
/*  610 */           SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/*  611 */           for (byte b = 0; b < arrayList.size(); b++) {
/*  612 */             str1 = str1 + subCompanyComInfo.getSubCompanyname(arrayList.get(b)) + str3;
/*      */           }
/*  614 */           str1 = OdocFileUtil.getProcessFieldValue(str1, paramString7);
/*  615 */         } else if (paramString2.equals("9") || paramString2.equals("37")) {
/*      */           
/*  617 */           DocComInfo docComInfo = new DocComInfo();
/*  618 */           for (byte b = 0; b < arrayList.size(); b++) {
/*  619 */             str1 = str1 + docComInfo.getDocname(arrayList.get(b)) + str3;
/*      */           }
/*  621 */           str1 = OdocFileUtil.getProcessFieldValue(str1, paramString7);
/*  622 */         } else if (paramString2.equals("23")) {
/*      */           
/*  624 */           CapitalComInfo capitalComInfo = new CapitalComInfo();
/*  625 */           for (byte b = 0; b < arrayList.size(); b++) {
/*  626 */             str1 = str1 + capitalComInfo.getCapitalname(arrayList.get(b)) + str3;
/*      */           }
/*  628 */           str1 = OdocFileUtil.getProcessFieldValue(str1, paramString7);
/*  629 */         } else if (paramString2.equals("16")) {
/*      */           
/*  631 */           WorkflowRequestComInfo workflowRequestComInfo = new WorkflowRequestComInfo();
/*  632 */           for (byte b = 0; b < arrayList.size(); b++) {
/*  633 */             str1 = str1 + workflowRequestComInfo.getRequestName(arrayList.get(b)) + str3;
/*      */           }
/*  635 */           str1 = OdocFileUtil.getProcessFieldValue(str1, paramString7);
/*  636 */         } else if (paramString2.equals("141")) {
/*      */           
/*  638 */           ResourceConditionManager resourceConditionManager = new ResourceConditionManager();
/*  639 */           str1 = str1 + resourceConditionManager.getFormShowNameOfNoLink(paramString3, paramInt1);
/*  640 */         } else if ("142".equals(paramString2)) {
/*  641 */           DocReceiveUnitComInfo docReceiveUnitComInfo = new DocReceiveUnitComInfo();
/*  642 */           for (byte b = 0; b < arrayList.size(); b++) {
/*  643 */             str1 = str1 + docReceiveUnitComInfo.getReceiveUnitName(arrayList.get(b)) + str3;
/*      */           }
/*  645 */           str1 = OdocFileUtil.getProcessFieldValue(str1, paramString7);
/*  646 */         } else if (paramString2.equals("161")) {
/*  647 */           str1 = "";
/*  648 */           String str = "";
/*  649 */           str2 = paramString3;
/*      */           try {
/*  651 */             Browser browser = (Browser)StaticObj.getServiceByFullname(paramString6, Browser.class);
/*  652 */             BrowserBean browserBean = browser.searchById(str2);
/*  653 */             String str4 = Util.null2String(browserBean.getDescription());
/*  654 */             String str5 = Util.null2String(browserBean.getName());
/*  655 */             str1 = str5;
/*  656 */           } catch (Exception exception) {}
/*      */         }
/*  658 */         else if (paramString2.equals("162")) {
/*  659 */           str1 = "";
/*  660 */           str2 = paramString3;
/*      */           try {
/*  662 */             Browser browser = (Browser)StaticObj.getServiceByFullname(paramString6, Browser.class);
/*  663 */             ArrayList<String> arrayList1 = Util.TokenizerString(str2, ",");
/*  664 */             for (byte b = 0; b < arrayList1.size(); b++) {
/*  665 */               String str4 = arrayList1.get(b);
/*  666 */               BrowserBean browserBean = browser.searchById(str4);
/*  667 */               String str5 = Util.null2String(browserBean.getName());
/*      */               
/*  669 */               String str6 = Util.null2String(browserBean.getDescription());
/*  670 */               str1 = str1 + str5 + " ";
/*      */             } 
/*  672 */           } catch (Exception exception) {}
/*      */         }
/*  674 */         else if (paramString2.equals("256") || paramString2.equals("257")) {
/*      */           
/*  676 */           str1 = "";
/*  677 */           str2 = paramString3;
/*  678 */           CustomTreeUtil customTreeUtil = new CustomTreeUtil();
/*      */           
/*  680 */           str1 = str1 + customTreeUtil.getTreeFieldShowName(str2, paramString6, "onlyname");
/*      */         } else {
/*  682 */           BrowserComInfo browserComInfo = new BrowserComInfo();
/*  683 */           String str4 = browserComInfo.getBrowsertablename(paramString2);
/*  684 */           String str5 = browserComInfo.getBrowsercolumname(paramString2);
/*  685 */           String str6 = browserComInfo.getBrowserkeycolumname(paramString2);
/*  686 */           String str7 = "";
/*  687 */           if (paramString3.indexOf(",") != -1) {
/*  688 */             str7 = "select " + str6 + "," + str5 + " from " + str4 + " where " + str6 + " in( " + paramString3 + ")";
/*      */           } else {
/*  690 */             str7 = "select " + str6 + "," + str5 + " from " + str4 + " where " + str6 + "=" + paramString3;
/*      */           } 
/*  692 */           HashMap<Object, Object> hashMap = new HashMap<>();
/*  693 */           recordSet.executeSql(str7);
/*  694 */           while (recordSet.next()) {
/*  695 */             str2 = Util.null2String(recordSet.getString(1));
/*  696 */             String str = Util.toScreen(recordSet.getString(2), paramInt1);
/*  697 */             str1 = str1 + str + " ";
/*  698 */             hashMap.put(str2, str);
/*      */           } 
/*  700 */           if (paramString2.equals("160")) {
/*  701 */             str1 = "";
/*  702 */             String[] arrayOfString = paramString3.split(",");
/*  703 */             for (String str : arrayOfString) {
/*  704 */               str1 = str1 + hashMap.get(str) + " ";
/*      */             }
/*      */           } 
/*      */         } 
/*      */       } 
/*  709 */     } else if (paramString1.equals("6")) {
/*  710 */       if (!paramString3.equals("")) {
/*  711 */         String str = "select id,docsubject,accessorycount from docdetail where id in(" + paramString3 + ") order by id asc";
/*  712 */         recordSet.executeSql(str);
/*  713 */         while (recordSet.next()) {
/*  714 */           str1 = str1 + Util.toScreen(recordSet.getString(2), paramInt1) + str3;
/*      */         }
/*  716 */         str1 = OdocFileUtil.getProcessFieldValue(str1, paramString7);
/*      */       } 
/*  718 */     } else if (paramString1.equals("5")) {
/*      */       
/*  720 */       if (paramString2.equals("2")) {
/*      */         
/*  722 */         if (paramInt2 == -3 || paramInt2 == -9) {
/*  723 */           if (StringUtils.isBlank(paramString3)) {
/*  724 */             return "";
/*      */           }
/*  726 */           String str = null;
/*  727 */           if ("1".equals(paramString5) || "0".equals(paramString5)) {
/*  728 */             str = "select * from workflow_SelectItem where selectvalue in (" + paramString3 + ") and fieldid=" + paramString4 + " and isBill=" + paramString5;
/*      */           } else {
/*  730 */             str = "select * from workflow_SelectItem where selectvalue in (" + paramString3 + ") and fieldid=" + paramString4;
/*      */           } 
/*      */           
/*  733 */           recordSet.executeSql(str);
/*  734 */           while (recordSet.next()) {
/*  735 */             if (!str1.equals("")) str1 = str1 + " "; 
/*  736 */             str1 = str1 + recordSet.getString("selectname");
/*      */           }
/*      */         
/*  739 */         } else if (paramInt2 == -2) {
/*      */           
/*  741 */           String str = null;
/*  742 */           if ("1".equals(paramString5) || "0".equals(paramString5)) {
/*  743 */             str = "select * from workflow_SelectItem where fieldid=" + paramString4 + " and isBill=" + paramString5;
/*      */           } else {
/*  745 */             str = "select * from workflow_SelectItem where fieldid=" + paramString4;
/*      */           } 
/*      */           
/*  748 */           recordSet.executeSql(str);
/*  749 */           while (recordSet.next()) {
/*  750 */             if (!str1.equals("")) str1 = str1 + " "; 
/*  751 */             String str4 = (("," + paramString3 + ",").indexOf("," + recordSet.getString("selectvalue") + ",") >= 0) ? "√" : "□";
/*  752 */             str1 = str1 + str4 + " " + recordSet.getString("selectname");
/*      */           }
/*      */         
/*  755 */         } else if (!Strings.isNullOrEmpty(paramString3) && ("," + paramString3 + ",").indexOf("," + paramInt2 + ",") >= 0) {
/*  756 */           String str = null;
/*  757 */           if ("1".equals(paramString5) || "0".equals(paramString5)) {
/*  758 */             str = "select * from workflow_SelectItem where selectvalue in (" + paramInt2 + ") and fieldid=" + paramString4 + " and isBill=" + paramString5;
/*      */           } else {
/*  760 */             str = "select * from workflow_SelectItem where selectvalue in (" + paramInt2 + ") and fieldid=" + paramString4;
/*      */           } 
/*  762 */           recordSet.executeSql(str);
/*  763 */           while (recordSet.next()) {
/*  764 */             if (!str1.equals("")) str1 = str1 + " "; 
/*  765 */             str1 = str1 + recordSet.getString("selectname");
/*      */           }
/*      */         
/*      */         }
/*      */       
/*  770 */       } else if (!"".equals(paramString3)) {
/*  771 */         String str = null;
/*  772 */         if ("1".equals(paramString5) || "0".equals(paramString5)) {
/*  773 */           str = "select * from workflow_SelectItem where selectvalue in (" + paramString3 + ") and fieldid=" + paramString4 + " and isBill=" + paramString5;
/*      */         } else {
/*  775 */           str = "select * from workflow_SelectItem where selectvalue in (" + paramString3 + ") and fieldid=" + paramString4;
/*      */         } 
/*      */         
/*  778 */         recordSet.executeSql(str);
/*  779 */         while (recordSet.next()) {
/*  780 */           if (!str1.equals("")) str1 = str1 + " "; 
/*  781 */           str1 = str1 + recordSet.getString("selectname");
/*      */         }
/*      */       
/*      */       } 
/*  785 */     } else if (paramString1.equals("4")) {
/*      */       
/*  787 */       if (paramInt2 == 2) {
/*  788 */         if ("1".equals(paramString3)) {
/*  789 */           str1 = "√";
/*      */         } else {
/*  791 */           str1 = "□";
/*      */         }
/*      */       
/*      */       }
/*  795 */       else if ("1".equals(paramString3)) {
/*  796 */         str1 = SystemEnv.getHtmlLabelName(163, paramInt1);
/*      */       } else {
/*  798 */         str1 = SystemEnv.getHtmlLabelName(161, paramInt1);
/*      */       } 
/*      */     } 
/*      */     
/*  802 */     return str1.trim();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private String getValueByDateShowType(String paramString1, String paramString2) {
/*  810 */     String str = paramString1;
/*      */ 
/*      */     
/*  813 */     if (paramString1 == null || paramString1.trim().equals("") || paramString2 == null || paramString2
/*  814 */       .trim().equals("") || paramString1
/*  815 */       .length() < 10)
/*      */     {
/*  817 */       return str;
/*      */     }
/*      */     
/*  820 */     if ("1".equals(paramString2)) {
/*  821 */       str = "";
/*  822 */       String str1 = "";
/*  823 */       int i = 0;
/*  824 */       int j = 0;
/*  825 */       int k = paramString1.indexOf("-");
/*  826 */       int m = paramString1.lastIndexOf("-");
/*  827 */       if (k < 0 || m < 0) {
/*  828 */         return str;
/*      */       }
/*  830 */       str1 = paramString1.substring(0, k);
/*  831 */       i = Util.getIntValue(paramString1.substring(k + 1, m), 0);
/*  832 */       j = Util.getIntValue(paramString1.substring(m + 1), 0);
/*      */       
/*  834 */       char[] arrayOfChar = str1.toCharArray();
/*  835 */       byte b = 0;
/*      */       
/*  837 */       while (b < arrayOfChar.length) {
/*  838 */         char c = arrayOfChar[b++];
/*  839 */         if (c == '0') {
/*  840 */           str = str + "○"; continue;
/*  841 */         }  if (c == '1') {
/*  842 */           str = str + SystemEnv.getHtmlLabelName(385821, 7); continue;
/*  843 */         }  if (c == '2') {
/*  844 */           str = str + SystemEnv.getHtmlLabelName(385822, 7); continue;
/*  845 */         }  if (c == '3') {
/*  846 */           str = str + SystemEnv.getHtmlLabelName(383911, 7); continue;
/*  847 */         }  if (c == '4') {
/*  848 */           str = str + SystemEnv.getHtmlLabelName(385824, 7); continue;
/*  849 */         }  if (c == '5') {
/*  850 */           str = str + SystemEnv.getHtmlLabelName(385825, 7); continue;
/*  851 */         }  if (c == '6') {
/*  852 */           str = str + SystemEnv.getHtmlLabelName(385826, 7); continue;
/*  853 */         }  if (c == '7') {
/*  854 */           str = str + SystemEnv.getHtmlLabelName(82514, 7); continue;
/*  855 */         }  if (c == '8') {
/*  856 */           str = str + SystemEnv.getHtmlLabelName(82515, 7); continue;
/*  857 */         }  if (c == '9') {
/*  858 */           str = str + SystemEnv.getHtmlLabelName(82516, 7);
/*      */         }
/*      */       } 
/*  861 */       str = str + SystemEnv.getHtmlLabelName(383372, 7);
/*  862 */       switch (i) {
/*      */         case 1:
/*  864 */           str = str + SystemEnv.getHtmlLabelName(385821, 7);
/*      */           break;
/*      */         case 2:
/*  867 */           str = str + SystemEnv.getHtmlLabelName(385822, 7);
/*      */           break;
/*      */         case 3:
/*  870 */           str = str + SystemEnv.getHtmlLabelName(383911, 7);
/*      */           break;
/*      */         case 4:
/*  873 */           str = str + SystemEnv.getHtmlLabelName(385824, 7);
/*      */           break;
/*      */         case 5:
/*  876 */           str = str + SystemEnv.getHtmlLabelName(385825, 7);
/*      */           break;
/*      */         case 6:
/*  879 */           str = str + SystemEnv.getHtmlLabelName(385826, 7);
/*      */           break;
/*      */         case 7:
/*  882 */           str = str + SystemEnv.getHtmlLabelName(82514, 7);
/*      */           break;
/*      */         case 8:
/*  885 */           str = str + SystemEnv.getHtmlLabelName(82515, 7);
/*      */           break;
/*      */         case 9:
/*  888 */           str = str + SystemEnv.getHtmlLabelName(82516, 7);
/*      */           break;
/*      */         case 10:
/*  891 */           str = str + SystemEnv.getHtmlLabelName(82517, 7);
/*      */           break;
/*      */         case 11:
/*  894 */           str = str + SystemEnv.getHtmlLabelName(82518, 7);
/*      */           break;
/*      */         case 12:
/*  897 */           str = str + SystemEnv.getHtmlLabelName(82519, 7);
/*      */           break;
/*      */       } 
/*      */ 
/*      */       
/*  902 */       str = str + SystemEnv.getHtmlLabelName(383373, 7);
/*  903 */       switch (j) {
/*      */         case 1:
/*  905 */           str = str + SystemEnv.getHtmlLabelName(385821, 7);
/*      */           break;
/*      */         case 2:
/*  908 */           str = str + SystemEnv.getHtmlLabelName(385822, 7);
/*      */           break;
/*      */         case 3:
/*  911 */           str = str + SystemEnv.getHtmlLabelName(383911, 7);
/*      */           break;
/*      */         case 4:
/*  914 */           str = str + SystemEnv.getHtmlLabelName(385824, 7);
/*      */           break;
/*      */         case 5:
/*  917 */           str = str + SystemEnv.getHtmlLabelName(385825, 7);
/*      */           break;
/*      */         case 6:
/*  920 */           str = str + SystemEnv.getHtmlLabelName(385826, 7);
/*      */           break;
/*      */         case 7:
/*  923 */           str = str + SystemEnv.getHtmlLabelName(82514, 7);
/*      */           break;
/*      */         case 8:
/*  926 */           str = str + SystemEnv.getHtmlLabelName(82515, 7);
/*      */           break;
/*      */         case 9:
/*  929 */           str = str + SystemEnv.getHtmlLabelName(82516, 7);
/*      */           break;
/*      */         case 10:
/*  932 */           str = str + SystemEnv.getHtmlLabelName(82517, 7);
/*      */           break;
/*      */         case 11:
/*  935 */           str = str + SystemEnv.getHtmlLabelName(82518, 7);
/*      */           break;
/*      */         case 12:
/*  938 */           str = str + SystemEnv.getHtmlLabelName(82519, 7);
/*      */           break;
/*      */         case 13:
/*  941 */           str = str + SystemEnv.getHtmlLabelName(502962, 7);
/*      */           break;
/*      */         case 14:
/*  944 */           str = str + SystemEnv.getHtmlLabelName(502963, 7);
/*      */           break;
/*      */         case 15:
/*  947 */           str = str + SystemEnv.getHtmlLabelName(502964, 7);
/*      */           break;
/*      */         case 16:
/*  950 */           str = str + SystemEnv.getHtmlLabelName(502965, 7);
/*      */           break;
/*      */         case 17:
/*  953 */           str = str + SystemEnv.getHtmlLabelName(502966, 7);
/*      */           break;
/*      */         case 18:
/*  956 */           str = str + SystemEnv.getHtmlLabelName(502968, 7);
/*      */           break;
/*      */         case 19:
/*  959 */           str = str + SystemEnv.getHtmlLabelName(502969, 7);
/*      */           break;
/*      */         case 20:
/*  962 */           str = str + SystemEnv.getHtmlLabelName(502970, 7);
/*      */           break;
/*      */         case 21:
/*  965 */           str = str + SystemEnv.getHtmlLabelName(502971, 7);
/*      */           break;
/*      */         case 22:
/*  968 */           str = str + SystemEnv.getHtmlLabelName(502972, 7);
/*      */           break;
/*      */         case 23:
/*  971 */           str = str + SystemEnv.getHtmlLabelName(502973, 7);
/*      */           break;
/*      */         case 24:
/*  974 */           str = str + SystemEnv.getHtmlLabelName(502974, 7);
/*      */           break;
/*      */         case 25:
/*  977 */           str = str + SystemEnv.getHtmlLabelName(502976, 7);
/*      */           break;
/*      */         case 26:
/*  980 */           str = str + SystemEnv.getHtmlLabelName(502977, 7);
/*      */           break;
/*      */         case 27:
/*  983 */           str = str + SystemEnv.getHtmlLabelName(502978, 7);
/*      */           break;
/*      */         case 28:
/*  986 */           str = str + SystemEnv.getHtmlLabelName(502979, 7);
/*      */           break;
/*      */         case 29:
/*  989 */           str = str + SystemEnv.getHtmlLabelName(502980, 7);
/*      */           break;
/*      */         case 30:
/*  992 */           str = str + SystemEnv.getHtmlLabelName(502981, 7);
/*      */           break;
/*      */         case 31:
/*  995 */           str = str + SystemEnv.getHtmlLabelName(502982, 7);
/*      */           break;
/*      */       } 
/*  998 */       str = str + SystemEnv.getHtmlLabelName(385820, 7);
/*      */     }
/* 1000 */     else if ("2".equals(paramString2)) {
/* 1001 */       String str1 = "";
/* 1002 */       int i = 0;
/* 1003 */       int j = 0;
/* 1004 */       int k = paramString1.indexOf("-");
/* 1005 */       int m = paramString1.lastIndexOf("-");
/* 1006 */       if (k < 0 || m < 0) {
/* 1007 */         return str;
/*      */       }
/* 1009 */       str1 = paramString1.substring(0, k);
/* 1010 */       i = Util.getIntValue(paramString1.substring(k + 1, m), 0);
/* 1011 */       j = Util.getIntValue(paramString1.substring(m + 1), 0);
/* 1012 */       str = str1 + SystemEnv.getHtmlLabelName(383372, 7) + i + SystemEnv.getHtmlLabelName(383373, 7) + j + SystemEnv.getHtmlLabelName(385820, 7);
/*      */     } 
/*      */     
/* 1015 */     return str;
/*      */   }
/*      */ 
/*      */   
/*      */   private String getValueByAmountDateShowType(String paramString1, String paramString2, String paramString3) {
/* 1020 */     String str = paramString1;
/*      */ 
/*      */     
/* 1023 */     if (paramString1 == null || paramString1.trim().equals("") || paramString2 == null || paramString2
/* 1024 */       .trim().equals(""))
/*      */     {
/* 1026 */       return str;
/*      */     }
/* 1028 */     paramString1 = paramString1.replace(",", "");
/* 1029 */     Double double_ = Double.valueOf(Util.getDoubleValue(paramString1, 0.0D));
/* 1030 */     OdocFileUtil odocFileUtil = new OdocFileUtil();
/* 1031 */     if ("5".equals(paramString3)) {
/* 1032 */       if ("1".equals(paramString2)) {
/* 1033 */         str = odocFileUtil.digitUppercase(double_.doubleValue());
/* 1034 */       } else if ("2".equals(paramString2)) {
/* 1035 */         str = odocFileUtil.thousandsCase(double_.doubleValue());
/*      */       } 
/* 1037 */     } else if ("4".equals(paramString3)) {
/* 1038 */       if ("1".equals(paramString2)) {
/* 1039 */         str = odocFileUtil.digitUppercase(double_.doubleValue());
/* 1040 */       } else if ("2".equals(paramString2)) {
/* 1041 */         str = odocFileUtil.thousandsCase(double_.doubleValue());
/* 1042 */       } else if (paramString2 == null || paramString2.trim().equals("")) {
/*      */         
/* 1044 */         str = paramString1;
/*      */       } 
/*      */     } 
/* 1047 */     return str;
/*      */   }
/*      */ 
/*      */   
/*      */   private static String fromHtmlToWordDsp(String paramString) {
/* 1052 */     if (paramString == null) {
/* 1053 */       return "";
/*      */     }
/* 1055 */     paramString = Util.StringReplace(paramString, "<br>", "\n");
/* 1056 */     paramString = Util.StringReplace(paramString, "&lt;", "<");
/* 1057 */     paramString = Util.StringReplace(paramString, "&gt;", ">");
/* 1058 */     paramString = Util.StringReplace(paramString, "&quot;", "\"");
/* 1059 */     paramString = Util.StringReplace(paramString, "&nbsp;", " ");
/*      */     
/* 1061 */     return paramString;
/*      */   }
/*      */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/RequestDocDocAddExt.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */