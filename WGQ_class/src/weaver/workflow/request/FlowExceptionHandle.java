/*     */ package weaver.workflow.request;
/*     */ 
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class FlowExceptionHandle
/*     */   extends BaseBean
/*     */ {
/*     */   public Map<String, String> getExceptionHandleSetting(int paramInt1, int paramInt2) {
/*  20 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  21 */     RecordSet recordSet = new RecordSet();
/*  22 */     recordSet.executeSql("SELECT useExceptionHandle,exceptionHandleWay,flowToAssignNode FROM workflow_flownode WHERE workflowid=" + paramInt1 + " AND nodeid=" + paramInt2);
/*  23 */     if (recordSet.next()) {
/*  24 */       hashMap.put("useExceptionHandle", Util.null2String(recordSet.getString("useExceptionHandle")));
/*  25 */       hashMap.put("exceptionHandleWay", Util.null2String(recordSet.getString("exceptionHandleWay")));
/*  26 */       hashMap.put("flowToAssignNode", Util.null2String(recordSet.getString("flowToAssignNode")));
/*     */     } 
/*  28 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean hasSettingException(int paramInt1, int paramInt2) {
/*  35 */     boolean bool = false;
/*  36 */     RecordSet recordSet = new RecordSet();
/*  37 */     recordSet.executeSql("SELECT useExceptionHandle,exceptionHandleWay,flowToAssignNode FROM workflow_flownode WHERE workflowid=" + paramInt1 + " AND nodeid=" + paramInt2);
/*  38 */     if (recordSet.next() && 
/*  39 */       Util.getIntValue(recordSet.getString("useExceptionHandle")) == 1 && 
/*  40 */       Util.getIntValue(recordSet.getString("exceptionHandleWay")) > 0) {
/*  41 */       bool = true;
/*     */     }
/*     */     
/*  44 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void saveRequestExceptionFlowInfo(int paramInt1, int paramInt2, int paramInt3, int paramInt4, Map<String, Object> paramMap) {
/*  51 */     int i = Util.getIntValue((String)paramMap.get("eh_relationship"));
/*  52 */     String str1 = Util.null2String(paramMap.get("eh_operators"));
/*  53 */     String str2 = Util.null2String(paramMap.get("passedNodes"));
/*  54 */     saveRequestExceptionFlowInfo(paramInt1, paramInt2, paramInt3, paramInt4, i, str1, str2);
/*     */   }
/*     */   
/*     */   public void saveRequestExceptionFlowInfo(int paramInt1, int paramInt2, int paramInt3, int paramInt4, int paramInt5, String paramString1, String paramString2) {
/*     */     try {
/*  59 */       String str = "";
/*     */       
/*  61 */       RecordSet recordSet = new RecordSet();
/*  62 */       str = "delete from workflow_requestexception where requestid=" + paramInt1 + " and nodeid=" + paramInt2 + " and destnodeid=" + paramInt3;
/*  63 */       recordSet.executeSql(str);
/*  64 */       if (paramInt4 <= 0)
/*     */         return; 
/*  66 */       if (paramInt4 == 3) {
/*  67 */         str = "insert into workflow_requestexception(requestid, nodeid, destnodeid, exceptiontype, signtype, flowoperator)  values(" + paramInt1 + "," + paramInt2 + "," + paramInt3 + ",'" + paramInt4 + "','" + paramInt5 + "','" + paramString1 + "')";
/*     */       } else {
/*     */         
/*  70 */         str = "insert into workflow_requestexception(requestid, nodeid, destnodeid, exceptiontype, passedNodes)  values(" + paramInt1 + "," + paramInt2 + "," + paramInt3 + ",'" + paramInt4 + "','" + paramString2 + "')";
/*     */       } 
/*     */       
/*  73 */       recordSet.executeSql(str);
/*  74 */     } catch (Exception exception) {
/*  75 */       exception.printStackTrace();
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void buildRejectOperatorMap(Map<String, Object> paramMap, int paramInt1, int paramInt2) {
/*  83 */     RecordSet recordSet = new RecordSet();
/*  84 */     recordSet.executeSql(" select * from workflow_requestexception where requestid=" + paramInt1 + " and destnodeid=" + paramInt2 + " order by keyid desc");
/*  85 */     if (recordSet.next() && Util.getIntValue(recordSet.getString("exceptiontype")) == 3) {
/*  86 */       paramMap.put("eh_relationship", Util.null2String(recordSet.getString("signtype")));
/*  87 */       paramMap.put("eh_operators", Util.null2String(recordSet.getString("flowoperator")));
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getExceptionOrderManagerOperator(int paramInt1, int paramInt2) {
/*  96 */     RecordSet recordSet = new RecordSet();
/*  97 */     recordSet.executeSql(" select * from workflow_requestexception where requestid=" + paramInt1 + " and destnodeid=" + paramInt2 + " order by keyid desc");
/*  98 */     if (recordSet.next() && Util.getIntValue(recordSet.getString("exceptiontype")) == 3 && 
/*  99 */       "2".equals(recordSet.getString("signtype"))) {
/* 100 */       return recordSet.getString("flowoperator");
/*     */     }
/* 102 */     return "";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean isExceptionJumpOverBranchNode(int paramInt1, int paramInt2, int paramInt3) {
/* 110 */     RecordSet recordSet = new RecordSet();
/* 111 */     String str = " select keyid from workflow_requestexception where requestid=" + paramInt1 + " and nodeid=" + paramInt2 + " and destnodeid=" + paramInt3 + " and exceptiontype='1' ";
/*     */     
/* 113 */     recordSet.executeSql(str);
/* 114 */     if (recordSet.next())
/* 115 */       return true; 
/* 116 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCurrentBranchAllNodes(int paramInt1, int paramInt2) {
/* 123 */     int i = getNodeAttribute(paramInt1, paramInt2);
/* 124 */     if (i != 2)
/* 125 */       return ""; 
/* 126 */     String str1 = "";
/* 127 */     String str2 = "";
/*     */     
/* 129 */     str1 = str1 + getPreMiddleNode(paramInt1, paramInt2, "");
/*     */     
/* 131 */     str1 = str1 + getNextMiddleNode(paramInt1, paramInt2, "");
/*     */     
/* 133 */     if (str1.endsWith(",")) {
/* 134 */       str1 = str1.substring(0, str1.length() - 1);
/*     */     }
/*     */     
/* 137 */     return str1;
/*     */   }
/*     */ 
/*     */   
/*     */   public String getPreMiddleNode(int paramInt1, int paramInt2, String paramString) {
/* 142 */     paramString = Util.null2String(paramString);
/* 143 */     if ((paramString.split(",")).length > 1000) {
/* 144 */       writeLog("FlowExceptionHandle.getPreMiddleNode 可能死循环，注意排查 workflowid = " + paramInt1 + " preNodes = " + paramString);
/* 145 */       return paramString;
/*     */     } 
/* 147 */     RecordSet recordSet = new RecordSet();
/* 148 */     String str = " select a.nodeid,b.nodeattribute from workflow_NodeLink a,workflow_nodebase b  where a.wfrequestid is null and (b.IsFreeNode is null or b.IsFreeNode!='1')  and a.nodeid=b.id and (a.isreject is null or a.isreject<>'1')  and a.destnodeid=" + paramInt2 + " and a.workflowid=" + paramInt1;
/*     */ 
/*     */ 
/*     */     
/* 152 */     recordSet.execute(str);
/* 153 */     while (recordSet.next()) {
/* 154 */       paramInt2 = recordSet.getInt(1);
/* 155 */       if (recordSet.getInt(2) == 2 && ("," + paramString + ",").indexOf("," + paramInt2 + ",") == -1) {
/* 156 */         paramString = paramString + paramInt2 + ",";
/* 157 */         paramString = getPreMiddleNode(paramInt1, paramInt2, paramString);
/*     */       } 
/*     */     } 
/* 160 */     return paramString;
/*     */   }
/*     */ 
/*     */   
/*     */   public String getNextMiddleNode(int paramInt1, int paramInt2, String paramString) {
/* 165 */     paramString = Util.null2String(paramString);
/* 166 */     if ((paramString.split(",")).length > 1000) {
/* 167 */       writeLog("FlowExceptionHandle.getNextMiddleNode 可能死循环，注意排查 workflowid = " + paramInt1 + " nextNodes = " + paramString);
/* 168 */       return paramString;
/*     */     } 
/* 170 */     RecordSet recordSet = new RecordSet();
/* 171 */     String str = " select a.destnodeid,b.nodeattribute from workflow_NodeLink a,workflow_nodebase b  where a.wfrequestid is null and (b.IsFreeNode is null or b.IsFreeNode!='1')  and a.destnodeid=b.id and (a.isreject is null or a.isreject<>'1')  and a.nodeid=" + paramInt2 + " and a.workflowid=" + paramInt1;
/*     */ 
/*     */ 
/*     */     
/* 175 */     recordSet.execute(str);
/* 176 */     while (recordSet.next()) {
/* 177 */       paramInt2 = recordSet.getInt(1);
/* 178 */       int i = recordSet.getInt(2);
/* 179 */       if (i == 2 && ("," + paramString + ",").indexOf("," + paramInt2 + ",") == -1) {
/* 180 */         paramString = paramString + paramInt2 + ",";
/* 181 */         paramString = getNextMiddleNode(paramInt1, paramInt2, paramString);
/*     */       } 
/*     */     } 
/* 184 */     return paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean judgeCanFlowAssignNode(int paramInt1, int paramInt2, int paramInt3) {
/* 192 */     int i = getNodeAttribute(paramInt1, paramInt2);
/* 193 */     int j = getNodeAttribute(paramInt1, paramInt3);
/* 194 */     if (i == -1 || j == -1)
/* 195 */       return false; 
/* 196 */     if (i == 2) {
/* 197 */       String str = getCurrentBranchAllNodes(paramInt1, paramInt2);
/* 198 */       if (("," + str + ",").indexOf("," + paramInt3 + ",") > -1) {
/* 199 */         return true;
/*     */       }
/* 201 */     } else if (j != 2) {
/* 202 */       return true;
/*     */     } 
/* 204 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getNodeAttribute(int paramInt1, int paramInt2) {
/* 211 */     RecordSet recordSet = new RecordSet();
/* 212 */     int i = -1;
/* 213 */     String str = " SELECT nodeattribute FROM workflow_nodebase nb,workflow_flownode fn  WHERE nb.id=fn.nodeid and fn.nodeid=" + paramInt2 + " AND fn.workflowid=" + paramInt1;
/*     */     
/* 215 */     recordSet.executeSql(str);
/* 216 */     if (recordSet.next()) {
/* 217 */       i = Util.getIntValue(recordSet.getString("nodeattribute"), 0);
/*     */     }
/* 219 */     return i;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getNodeName(int paramInt1, int paramInt2) {
/* 226 */     RecordSet recordSet = new RecordSet();
/* 227 */     String str = "";
/* 228 */     recordSet.executeSql("SELECT nodename FROM workflow_nodebase nb,workflow_flownode fn WHERE nb.id=fn.nodeid AND fn.nodeid=" + paramInt2 + " AND fn.workflowid=" + paramInt1);
/* 229 */     if (recordSet.next())
/* 230 */       str = Util.null2String(recordSet.getString("nodename")); 
/* 231 */     return str;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/FlowExceptionHandle.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */