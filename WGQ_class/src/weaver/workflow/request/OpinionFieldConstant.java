/*     */ package weaver.workflow.request;
/*     */ 
/*     */ import weaver.general.ThreadVarLanguage;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class OpinionFieldConstant
/*     */ {
/*     */   public static final String DOC_NAME = "DocumentId";
/*     */   public static final String MUTI_DOC_NAME = "Muti_documentId";
/*     */   public static final String PROJECT_NAME = "ProjectId";
/*     */   public static final String MUTI_PROJECT_NAME = "Muti_projectId";
/*     */   public static final String CUSTOMER_NAME = "CustomerId";
/*     */   public static final String MUTI_CUSTOMER_NAME = "Muti_customerId";
/*     */   public static final String RESOURCES_NAME = "ResourcesId";
/*     */   public static final String WORKFLOW_NAME = "WorkflowId";
/*     */   public static final String ACCESSORIES_NAME = "AccessoriesId";
/*     */   public static final String DOCUMENT_TYPE = "1";
/*     */   public static final String MUTI_DOCUMENT_TYPE = "2";
/*     */   public static final String PROJECT_TYPE = "3";
/*     */   public static final String MUTI_PROJECT_TYPE = "4";
/*     */   public static final String CUSTOMER_TYPE = "5";
/*     */   public static final String MUTI_CUSTOMER_TYPE = "6";
/*     */   public static final String RESOURCES_TYPE = "7";
/*     */   public static final String WORKFLOW_TYPE = "8";
/*     */   public static final String ACCESSORIES_TYPE = "9";
/*     */   public static final String DOCUMENT_TYPE_VALUE = "9";
/*     */   public static final String MUTI_DOCUMENT_TYPE_VALUE = "37";
/*     */   public static final String PROJECT_TYPE_VALUE = "8";
/*     */   public static final String MUTI_PROJECT_TYPE_VALUE = "135";
/*     */   public static final String CUSTOMER_TYPE_VALUE = "7";
/*     */   public static final String MUTI_CUSTOMER_TYPE_VALUE = "18";
/*     */   public static final String RESOURCES_TYPE_VALUE = "23";
/*     */   public static final String WORKFLOW_TYPE_VALUE = "16";
/*     */   public static final String ACCESSORIES_TYPE_VALUE = "8";
/*     */   public static final String TABLE_NAME_PREFIX = "WFOpinionFieldData";
/*  74 */   public static final String NODE_TYPE_BUILD = "" + SystemEnv.getHtmlLabelName(83443, ThreadVarLanguage.getLang()) + "";
/*     */   
/*  76 */   public static final String NODE_TYPE_CHECK = "" + SystemEnv.getHtmlLabelName(30835, ThreadVarLanguage.getLang()) + "";
/*     */   
/*  78 */   public static final String NODE_TYPE_PROCESS = "" + SystemEnv.getHtmlLabelName(10004307, ThreadVarLanguage.getLang()) + "";
/*     */   
/*  80 */   public static final String NODE_TYPE_OVER = "" + SystemEnv.getHtmlLabelName(251, ThreadVarLanguage.getLang()) + "";
/*     */ 
/*     */   
/*  83 */   public static final String Doc_STATUS_DRAFT = "" + SystemEnv.getHtmlLabelName(129828, ThreadVarLanguage.getLang()) + "";
/*     */   
/*  85 */   public static final String Doc_STATUS_NORMAL = "" + SystemEnv.getHtmlLabelName(1984, ThreadVarLanguage.getLang()) + "";
/*     */   
/*  87 */   public static final String Doc_STATUS_NORMAL_AFTER = "" + SystemEnv.getHtmlLabelName(1984, ThreadVarLanguage.getLang()) + "";
/*     */   
/*  89 */   public static final String Doc_STATUS_OPEN_RE = "" + SystemEnv.getHtmlLabelName(360, ThreadVarLanguage.getLang()) + "-" + SystemEnv.getHtmlLabelName(2242, ThreadVarLanguage.getLang()) + "/" + SystemEnv.getHtmlLabelName(244, ThreadVarLanguage.getLang()) + "";
/*     */   
/*  91 */   public static final String Doc_STATUS_REJECT = "" + SystemEnv.getHtmlLabelName(129829, ThreadVarLanguage.getLang()) + "";
/*     */   
/*  93 */   public static final String Doc_STATUS_OVER = "" + SystemEnv.getHtmlLabelName(251, ThreadVarLanguage.getLang()) + "";
/*     */ 
/*     */   
/*     */   public static final String IS_SELECTED = "1";
/*     */ 
/*     */   
/*     */   public static final String IS_NOT_SELECTED = "0";
/*     */ 
/*     */   
/*     */   public static final String DOCUMENT_FIELD_TYPE = "1";
/*     */   
/*     */   public static final String MUTI_DOCUMENT_FIELD_TYPE = "2";
/*     */   
/*     */   public static final String PROJECT_FIELD_TYPE = "3";
/*     */   
/*     */   public static final String MUTI_PROJECT_FIELD_TYPE = "4";
/*     */   
/*     */   public static final String CUSTOMER_FIELD_TYPE = "5";
/*     */   
/*     */   public static final String MUTI_CUSTOMER_FIELD_TYPE = "6";
/*     */   
/*     */   public static final String RESOURCES_FIELD_TYPE = "7";
/*     */   
/*     */   public static final String WORKFLOW_FIELD_TYPE = "8";
/*     */   
/*     */   public static final String ACCESSORIES_FIELD_TYPE = "9";
/*     */   
/*     */   public static final String DOCUMENT_FIELD_TYPE_VALUE = "DocumentId VARCHAR(200) NULL";
/*     */   
/*     */   public static final String MUTI_DOCUMENT_FIELD_TYPE_VALUE = "Muti_documentId VARCHAR(200) NULL";
/*     */   
/*     */   public static final String PROJECT_FIELD_TYPE_VALUE = "ProjectId INT NULL";
/*     */   
/*     */   public static final String MUTI_PROJECT_FIELD_TYPE_VALUE = "Muti_projectId VARCHAR(200) NULL";
/*     */   
/*     */   public static final String CUSTOMER_FIELD_TYPE_VALUE = "CustomerId INT NULL";
/*     */   
/*     */   public static final String MUTI_CUSTOMER_FIELD_TYPE_VALUE = "Muti_customerId VARCHAR(200) NULL";
/*     */   
/*     */   public static final String RESOURCES_FIELD_TYPE_VALUE = "ResourcesId VARCHAR(200) NULL";
/*     */   
/*     */   public static final String WORKFLOW_FIELD_TYPE_VALUE = "WorkflowId INT NULL";
/*     */   
/*     */   public static final String ACCESSORIES_FIELD_TYPE_VALUE = "AccessoriesId VARCHAR(200) NULL";
/*     */   
/*     */   public static final int DOCUMENT_LABEL_NO = 857;
/*     */   
/*     */   public static final int PROJECT_LABEL_NO = 782;
/*     */   
/*     */   public static final int CUSTOMER_LABEL_NO = 783;
/*     */   
/*     */   public static final int RESOURCE_LABEL_NO = 858;
/*     */   
/*     */   public static final int WORKFLOW_LABEL_NO = 1044;
/*     */   
/* 148 */   public static final String PROJECT_STATUS_DRAFT = "" + SystemEnv.getHtmlLabelName(129828, ThreadVarLanguage.getLang()) + "";
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/OpinionFieldConstant.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */