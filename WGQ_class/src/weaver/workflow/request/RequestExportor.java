/*     */ package weaver.workflow.request;
/*     */ 
/*     */ import java.io.FileWriter;
/*     */ import java.util.ArrayList;
/*     */ import org.exolab.castor.xml.Marshaller;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.GCONST;
/*     */ import weaver.general.Util;
/*     */ import weaver.soa.workflow.request.Cell;
/*     */ import weaver.soa.workflow.request.DetailTable;
/*     */ import weaver.soa.workflow.request.DetailTableInfo;
/*     */ import weaver.soa.workflow.request.Log;
/*     */ import weaver.soa.workflow.request.MainTableInfo;
/*     */ import weaver.soa.workflow.request.Property;
/*     */ import weaver.soa.workflow.request.RequestInfo;
/*     */ import weaver.soa.workflow.request.RequestLog;
/*     */ import weaver.soa.workflow.request.Row;
/*     */ import weaver.workflow.workflow.WorkflowBillComInfo;
/*     */ import weaver.workflow.workflow.WorkflowComInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ public class RequestExportor
/*     */ {
/*     */   public boolean getRequest(int paramInt) {
/*     */     try {
/*  27 */       RecordSet recordSet1 = new RecordSet();
/*  28 */       RecordSet recordSet2 = new RecordSet();
/*  29 */       RecordSet recordSet3 = new RecordSet();
/*     */       
/*  31 */       String str = "select * from workflow_requestbase where requestid=" + paramInt;
/*  32 */       recordSet1.executeSql(str);
/*  33 */       if (recordSet1.next()) {
/*     */         
/*  35 */         RequestInfo requestInfo = new RequestInfo();
/*  36 */         String str1 = recordSet1.getString("workflowid");
/*  37 */         requestInfo.setWorkflowid(str1);
/*  38 */         requestInfo.setCreatorid(recordSet1.getString("creater"));
/*  39 */         requestInfo.setRequestid("" + paramInt);
/*  40 */         requestInfo.setDescription(recordSet1.getString("requestname"));
/*  41 */         requestInfo.setRequestlevel(recordSet1.getString("requestlevel"));
/*  42 */         requestInfo.setRemindtype(recordSet1.getString("messagetype"));
/*  43 */         requestInfo.setLastoperator(recordSet1.getString("lastoperator"));
/*     */         
/*  45 */         WorkflowComInfo workflowComInfo = new WorkflowComInfo();
/*  46 */         String str2 = workflowComInfo.getFormId(str1);
/*  47 */         String str3 = workflowComInfo.getIsBill(str1);
/*     */         
/*  49 */         if (!str3.equals("1")) {
/*  50 */           str = "select * from workflow_form where requestid=" + paramInt;
/*  51 */           recordSet1.executeSql(str);
/*  52 */           if (recordSet1.next()) {
/*     */ 
/*     */             
/*  55 */             recordSet2.executeSql("select b.id,b.fieldhtmltype,b.type,c.fieldlable,b.fieldname from workflow_formfield a,workflow_formdict b,workflow_fieldlable c where a.formid=c.formid and a.fieldid=c.fieldid and c.langurageid=7 and a.formid=" + str2 + " and a.fieldid=b.id and a.isdetail is null and b.fieldname<>'manager' order by a.fieldorder");
/*  56 */             MainTableInfo mainTableInfo = new MainTableInfo();
/*  57 */             ArrayList<Property> arrayList1 = new ArrayList();
/*  58 */             while (recordSet2.next()) {
/*  59 */               Property property = new Property();
/*  60 */               String str4 = recordSet2.getString("fieldname");
/*  61 */               property.setName(str4);
/*  62 */               String str5 = recordSet2.getString("fieldhtmltype");
/*  63 */               String str6 = recordSet2.getString("type");
/*  64 */               String str7 = recordSet1.getString(str4);
/*  65 */               if (str5.equals("6") || (str5.equals("3") && str6.equals("157"))) {
/*  66 */                 ArrayList arrayList2 = Util.TokenizerString(str7, ",");
/*  67 */                 StringBuffer stringBuffer = new StringBuffer();
/*  68 */                 for (String str8 : arrayList2) {
/*     */                   
/*  70 */                   if (!str8.equals("")) {
/*  71 */                     recordSet3.executeSql("select b.IMAGEFILENAME,b.FILEREALPATH from docimagefile a,imagefile b where a.IMAGEFILEID=b.IMAGEFILEID and a.DOCID=" + str8);
/*  72 */                     if (recordSet3.last()) {
/*  73 */                       stringBuffer.append(recordSet3.getString("IMAGEFILENAME")).append("|").append(recordSet3.getString("FILEREALPATH")).append(";");
/*     */                     }
/*     */                   } 
/*     */                 } 
/*  77 */                 property.setValue(stringBuffer.toString());
/*     */               }
/*  79 */               else if (str5.equals("3") && (str6.equals("153") || str6.equals("154"))) {
/*     */                 
/*  81 */                 StringBuffer stringBuffer = new StringBuffer();
/*  82 */                 if (!str7.equals("")) {
/*  83 */                   recordSet3.executeSql("select IMAGEFILENAME,FILEREALPATH from imagefile  where IMAGEFILEID=" + str7);
/*  84 */                   if (recordSet3.next()) {
/*  85 */                     stringBuffer.append(recordSet3.getString("IMAGEFILENAME")).append("|").append(recordSet3.getString("FILEREALPATH"));
/*     */                   }
/*     */                 } 
/*  88 */                 property.setValue(stringBuffer.toString());
/*  89 */               } else if (str5.equals("3") && str6.equals("152")) {
/*     */                 
/*  91 */                 RequestComInfo requestComInfo = new RequestComInfo();
/*  92 */                 ArrayList arrayList2 = Util.TokenizerString(str7, ",");
/*  93 */                 StringBuffer stringBuffer = new StringBuffer();
/*  94 */                 for (String str8 : arrayList2) {
/*     */                   
/*  96 */                   if (!str8.equals("")) {
/*  97 */                     stringBuffer.append(requestComInfo.getRequestname(str8));
/*     */                   }
/*     */                 } 
/* 100 */                 property.setValue(stringBuffer.toString());
/*     */               } else {
/*     */                 
/* 103 */                 property.setValue(str7);
/*     */               } 
/* 105 */               arrayList1.add(property);
/*     */             } 
/*     */             
/* 108 */             Property[] arrayOfProperty = new Property[arrayList1.size()];
/* 109 */             arrayList1.toArray(arrayOfProperty);
/* 110 */             mainTableInfo.setProperty(arrayOfProperty);
/* 111 */             requestInfo.setMainTableInfo(mainTableInfo);
/*     */           } 
/*     */         } else {
/*     */           
/* 115 */           WorkflowBillComInfo workflowBillComInfo = new WorkflowBillComInfo();
/* 116 */           String str4 = workflowBillComInfo.getTablename(str2);
/* 117 */           str = "select * from " + str4 + " where requestid=" + paramInt;
/* 118 */           recordSet1.executeSql(str);
/* 119 */           if (recordSet1.next()) {
/*     */ 
/*     */             
/* 122 */             recordSet2.executeSql("select * from workflow_billfield where viewtype=0 and billid=" + str2);
/* 123 */             MainTableInfo mainTableInfo = new MainTableInfo();
/* 124 */             ArrayList<Property> arrayList1 = new ArrayList();
/* 125 */             while (recordSet2.next()) {
/* 126 */               Property property = new Property();
/* 127 */               String str5 = recordSet2.getString("fieldname");
/* 128 */               property.setName(str5);
/* 129 */               String str6 = recordSet2.getString("fieldhtmltype");
/* 130 */               String str7 = recordSet2.getString("type");
/* 131 */               String str8 = recordSet1.getString(str5);
/* 132 */               if (str6.equals("6") || (str6.equals("3") && str7.equals("157"))) {
/* 133 */                 ArrayList arrayList2 = Util.TokenizerString(str8, ",");
/* 134 */                 StringBuffer stringBuffer = new StringBuffer();
/* 135 */                 for (String str9 : arrayList2) {
/*     */                   
/* 137 */                   if (!str9.equals("")) {
/* 138 */                     recordSet3.executeSql("select b.IMAGEFILENAME,b.FILEREALPATH from docimagefile a,imagefile b where a.IMAGEFILEID=b.IMAGEFILEID and a.DOCID=" + str9);
/* 139 */                     if (recordSet3.last()) {
/* 140 */                       stringBuffer.append(recordSet3.getString("IMAGEFILENAME")).append("|").append(recordSet3.getString("FILEREALPATH")).append(";");
/*     */                     }
/*     */                   } 
/*     */                 } 
/* 144 */                 property.setValue(stringBuffer.toString());
/*     */               }
/* 146 */               else if (str6.equals("3") && (str7.equals("153") || str7.equals("154"))) {
/*     */ 
/*     */                 
/* 149 */                 StringBuffer stringBuffer = new StringBuffer();
/* 150 */                 if (!str8.equals("")) {
/* 151 */                   recordSet3.executeSql("select IMAGEFILENAME,FILEREALPATH from imagefile  where IMAGEFILEID=" + str8);
/* 152 */                   if (recordSet3.next()) {
/* 153 */                     stringBuffer.append(recordSet3.getString("IMAGEFILENAME")).append("|").append(recordSet3.getString("FILEREALPATH"));
/*     */                   }
/*     */                 } 
/* 156 */                 property.setValue(stringBuffer.toString());
/* 157 */               } else if (str6.equals("3") && str7.equals("152")) {
/*     */                 
/* 159 */                 RequestComInfo requestComInfo = new RequestComInfo();
/* 160 */                 ArrayList arrayList2 = Util.TokenizerString(str8, ",");
/* 161 */                 StringBuffer stringBuffer = new StringBuffer();
/* 162 */                 for (String str9 : arrayList2) {
/*     */                   
/* 164 */                   if (!str9.equals("")) {
/* 165 */                     stringBuffer.append(requestComInfo.getRequestname(str9));
/*     */                   }
/*     */                 } 
/* 168 */                 property.setValue(stringBuffer.toString());
/*     */               } else {
/*     */                 
/* 171 */                 property.setValue(recordSet1.getString(recordSet2.getString("fieldname")));
/*     */               } 
/* 173 */               arrayList1.add(property);
/*     */             } 
/*     */             
/* 176 */             Property[] arrayOfProperty = new Property[arrayList1.size()];
/* 177 */             arrayList1.toArray(arrayOfProperty);
/* 178 */             mainTableInfo.setProperty(arrayOfProperty);
/* 179 */             requestInfo.setMainTableInfo(mainTableInfo);
/*     */           } 
/*     */         } 
/*     */         
/* 183 */         DetailTableInfo detailTableInfo = new DetailTableInfo();
/*     */         
/* 185 */         if (!str3.equals("1")) {
/* 186 */           str = "select max(groupid) as groupcount,count(*) as dfcount  from workflow_formfield where isdetail='1' and formid=" + str2;
/* 187 */           recordSet1.executeSql(str);
/* 188 */           recordSet1.next();
/* 189 */           int i = recordSet1.getInt("groupcount");
/* 190 */           boolean bool = false;
/* 191 */           int j = recordSet1.getInt("dfcount");
/* 192 */           if (j != 0) {
/*     */ 
/*     */             
/* 195 */             if (i == -1) {
/* 196 */               bool = true;
/* 197 */               i = 0;
/*     */             } 
/*     */             
/* 200 */             ArrayList<DetailTable> arrayList1 = new ArrayList();
/* 201 */             for (byte b1 = 0; b1 <= i; b1++) {
/* 202 */               DetailTable detailTable = new DetailTable();
/* 203 */               detailTable.setId("" + (b1 + 1));
/*     */               
/* 205 */               if (bool) {
/* 206 */                 str = "select * from workflow_formdetail where requestid=" + paramInt;
/*     */               } else {
/* 208 */                 str = "select * from workflow_formdetail where requestid=" + paramInt + " and groupid=" + b1;
/* 209 */               }  recordSet1.executeSql(str);
/*     */               
/* 211 */               if (bool) {
/* 212 */                 str = "select b.id,b.fieldhtmltype,b.type,c.fieldlable,b.fieldname from workflow_formfield a,workflow_formdictdetail b,workflow_fieldlable c where a.formid=c.formid and a.fieldid=c.fieldid and c.langurageid=7 and a.formid=" + str2 + " and a.fieldid=b.id and a.isdetail='1' and b.fieldname<>'manager' order by a.fieldorder";
/*     */               } else {
/* 214 */                 str = "select b.id,b.fieldhtmltype,b.type,c.fieldlable,b.fieldname from workflow_formfield a,workflow_formdictdetail b,workflow_fieldlable c where a.formid=c.formid and a.fieldid=c.fieldid and c.langurageid=7 and a.formid=" + str2 + " and a.fieldid=b.id and a.isdetail='1' and a.groupid=" + b1 + " and b.fieldname<>'manager' order by a.fieldorder";
/* 215 */               }  recordSet2.executeSql(str);
/* 216 */               ArrayList<Row> arrayList2 = new ArrayList();
/*     */               
/* 218 */               while (recordSet1.next()) {
/* 219 */                 Row row = new Row();
/* 220 */                 row.setId(recordSet1.getString("id"));
/* 221 */                 ArrayList<Cell> arrayList3 = new ArrayList();
/* 222 */                 recordSet2.beforFirst();
/* 223 */                 while (recordSet2.next()) {
/* 224 */                   Cell cell = new Cell();
/* 225 */                   cell.setName(recordSet2.getString("fieldname"));
/* 226 */                   cell.setValue(recordSet1.getString(recordSet2.getString("fieldname")));
/* 227 */                   arrayList3.add(cell);
/*     */                 } 
/*     */                 
/* 230 */                 Cell[] arrayOfCell = new Cell[arrayList3.size()];
/* 231 */                 arrayList3.toArray(arrayOfCell);
/* 232 */                 row.setCell(arrayOfCell);
/* 233 */                 arrayList2.add(row);
/*     */               } 
/* 235 */               Row[] arrayOfRow = new Row[arrayList2.size()];
/* 236 */               arrayList2.toArray(arrayOfRow);
/* 237 */               detailTable.setRow(arrayOfRow);
/* 238 */               arrayList1.add(detailTable);
/*     */             } 
/* 240 */             DetailTable[] arrayOfDetailTable = new DetailTable[arrayList1.size()];
/* 241 */             arrayList1.toArray(arrayOfDetailTable);
/* 242 */             detailTableInfo.setDetailTable(arrayOfDetailTable);
/* 243 */             requestInfo.setDetailTableInfo(detailTableInfo);
/*     */           } 
/*     */         } else {
/*     */           
/* 247 */           WorkflowBillComInfo workflowBillComInfo = new WorkflowBillComInfo();
/* 248 */           String str4 = workflowBillComInfo.getTablename(str2);
/* 249 */           String str5 = workflowBillComInfo.getDetailkeyfield(str2);
/* 250 */           if (str5.equals(""))
/* 251 */             str5 = "mainid"; 
/* 252 */           RecordSet recordSet = new RecordSet();
/* 253 */           str = "select tablename as detailtablename from workflow_billdetailtable where billid=" + str2 + " order by orderid";
/* 254 */           recordSet.executeSql(str);
/* 255 */           int i = recordSet.getCounts();
/* 256 */           boolean bool1 = false;
/* 257 */           boolean bool2 = false;
/* 258 */           if (i == 0) {
/* 259 */             bool1 = true;
/* 260 */             str = "select detailtablename from workflow_bill where id=" + str2;
/* 261 */             recordSet.executeSql(str);
/* 262 */             i = recordSet.getCounts();
/* 263 */             recordSet.next();
/* 264 */             String str6 = recordSet.getString("detailtablename");
/* 265 */             if (str6.equals(""))
/* 266 */               bool2 = true; 
/*     */           } 
/* 268 */           if (!bool2) {
/*     */ 
/*     */             
/* 271 */             recordSet.beforFirst();
/* 272 */             ArrayList<DetailTable> arrayList1 = new ArrayList();
/* 273 */             for (byte b1 = 0; b1 < i; b1++) {
/* 274 */               DetailTable detailTable = new DetailTable();
/* 275 */               detailTable.setId("" + (b1 + 1));
/*     */               
/* 277 */               recordSet.next();
/* 278 */               String str6 = recordSet.getString("detailtablename");
/* 279 */               str = "select * from " + str4 + " a," + str6 + " b where a.id=b." + str5 + " and  a.requestid=" + paramInt;
/* 280 */               recordSet1.executeSql(str);
/*     */               
/* 282 */               if (bool1) {
/* 283 */                 str = "select * from workflow_billfield where billid=" + str2 + " and viewtype='1' ";
/*     */               } else {
/* 285 */                 str = "select * from workflow_billfield where billid=" + str2 + " and viewtype='1' where detailtable='" + str6 + "'";
/*     */               } 
/* 287 */               recordSet2.executeSql(str);
/* 288 */               ArrayList<Row> arrayList2 = new ArrayList();
/*     */               
/* 290 */               while (recordSet1.next()) {
/* 291 */                 Row row = new Row();
/* 292 */                 row.setId(recordSet1.getString("id"));
/* 293 */                 ArrayList<Cell> arrayList3 = new ArrayList();
/* 294 */                 recordSet2.beforFirst();
/* 295 */                 while (recordSet2.next()) {
/* 296 */                   Cell cell = new Cell();
/* 297 */                   cell.setName(recordSet2.getString("fieldname"));
/* 298 */                   cell.setValue(recordSet1.getString(recordSet2.getString("fieldname")));
/* 299 */                   arrayList3.add(cell);
/*     */                 } 
/* 301 */                 Cell[] arrayOfCell = new Cell[arrayList3.size()];
/* 302 */                 arrayList3.toArray(arrayOfCell);
/* 303 */                 row.setCell(arrayOfCell);
/* 304 */                 arrayList2.add(row);
/*     */               } 
/* 306 */               Row[] arrayOfRow = new Row[arrayList2.size()];
/* 307 */               arrayList2.toArray(arrayOfRow);
/* 308 */               detailTable.setRow(arrayOfRow);
/* 309 */               arrayList1.add(detailTable);
/*     */             } 
/* 311 */             DetailTable[] arrayOfDetailTable = new DetailTable[arrayList1.size()];
/* 312 */             arrayList1.toArray(arrayOfDetailTable);
/* 313 */             detailTableInfo.setDetailTable(arrayOfDetailTable);
/*     */           } 
/*     */           
/* 316 */           requestInfo.setDetailTableInfo(detailTableInfo);
/*     */         } 
/*     */         
/* 319 */         RequestLog requestLog = new RequestLog();
/* 320 */         ArrayList<Log> arrayList = new ArrayList();
/* 321 */         str = "select * from workflow_requestlog where requestid=" + paramInt + " order by operatedate,operatetime";
/* 322 */         recordSet1.executeSql(str);
/* 323 */         byte b = 0;
/* 324 */         while (recordSet1.next()) {
/* 325 */           Log log = new Log();
/* 326 */           log.setId("" + b);
/* 327 */           recordSet2.executeSql("select linkname from workflow_nodelink where workflowid=" + str1 + " and nodeid=" + recordSet1.getString("nodeid"));
/* 328 */           recordSet2.next();
/* 329 */           log.setNode(recordSet2.getString("linkname"));
/* 330 */           log.setOpdate(recordSet1.getString("operatedate"));
/* 331 */           log.setOptime(recordSet1.getString("operatetime"));
/* 332 */           log.setOperator(recordSet1.getString("operator"));
/* 333 */           log.setOptype(recordSet1.getString("logtype"));
/* 334 */           log.setReceiver(recordSet1.getString("receivedpersons"));
/* 335 */           log.setComment(recordSet1.getString("remark"));
/* 336 */           arrayList.add(log);
/* 337 */           b++;
/*     */         } 
/* 339 */         Log[] arrayOfLog = new Log[arrayList.size()];
/* 340 */         arrayList.toArray(arrayOfLog);
/* 341 */         requestLog.setLog(arrayOfLog);
/* 342 */         requestInfo.setRequestLog(requestLog);
/* 343 */         FileWriter fileWriter = new FileWriter(GCONST.getRootPath() + "requestxml\\xml" + requestInfo.getWorkflowid() + "_" + requestInfo.getRequestid() + ".xml");
/* 344 */         Marshaller marshaller = new Marshaller(fileWriter);
/* 345 */         marshaller.setSuppressNamespaces(true);
/* 346 */         marshaller.setSuppressXSIType(true);
/* 347 */         marshaller.setEncoding(GCONST.XML_UTF8);
/* 348 */         marshaller.marshal(requestInfo);
/* 349 */         return true;
/*     */       } 
/* 351 */       return false;
/* 352 */     } catch (Exception exception) {
/*     */       
/* 354 */       return false;
/*     */     } 
/*     */   }
/*     */   
/*     */   public static void main(String[] paramArrayOfString) {
/* 359 */     GCONST.setRootPath("D:\\tomcat-5.5.16\\webapps\\ROOT\\");
/* 360 */     GCONST.setServerName("ecology");
/* 361 */     RequestExportor requestExportor = new RequestExportor();
/* 362 */     requestExportor.getRequest(1236);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/RequestExportor.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */