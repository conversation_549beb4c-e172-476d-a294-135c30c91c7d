/*     */ package weaver.workflow.request;
/*     */ 
/*     */ import java.io.BufferedReader;
/*     */ import java.io.IOException;
/*     */ import java.io.InputStream;
/*     */ import java.io.InputStreamReader;
/*     */ import java.io.OutputStream;
/*     */ import java.io.OutputStreamWriter;
/*     */ import java.io.PrintWriter;
/*     */ import java.net.Socket;
/*     */ import java.net.URL;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class TestPost
/*     */ {
/*     */   public static void main(String[] paramArrayOfString) {
/*  23 */     TestPost testPost = new TestPost();
/*  24 */     testPost.test();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void test() {
/*  32 */     URL uRL = null;
/*  33 */     String str1 = "";
/*  34 */     Socket socket = null;
/*  35 */     InputStream inputStream = null;
/*  36 */     OutputStream outputStream = null;
/*  37 */     BufferedReader bufferedReader = null;
/*  38 */     PrintWriter printWriter = null;
/*  39 */     String str2 = "";
/*     */ 
/*     */     
/*  42 */     String str3 = "http://localhost/weaver/weaver.workflow.request.RequestOutDataFormatOperation";
/*     */ 
/*     */     
/*  45 */     String str4 = "userid=17&logintype=1&requestid=117690&src=submit&requestlevel=0&subject=<a href=http://localhost/abc.jsp?abc=abc target=about:blank>相关信息</a>&subject_label=附加信息&subject_type=1&main1=3&main1_label=主字段1&main1_type=2&main2=33.000&main2_label=主字段2&main2_type=3&main3=333&main3_label=主字段3&main3_type=1&misoanodeid=approve&misoanodeid_label=节点标识&misoanodeid_type=1";
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  50 */     try { uRL = new URL(str3);
/*  51 */       str1 = uRL.getPath();
/*  52 */       str2 = uRL.getHost();
/*  53 */       socket = new Socket(str2, 80);
/*  54 */       if (socket == null)
/*     */         return; 
/*  56 */       inputStream = socket.getInputStream();
/*  57 */       outputStream = socket.getOutputStream();
/*     */ 
/*     */       
/*  60 */       bufferedReader = new BufferedReader(new InputStreamReader(inputStream));
/*  61 */       printWriter = new PrintWriter(new OutputStreamWriter(outputStream), true);
/*     */       
/*  63 */       if (inputStream == null || outputStream == null || bufferedReader == null || printWriter == null) {
/*     */         return;
/*     */       }
/*     */       
/*  67 */       printWriter.print("POST " + str1 + " HTTP/1.1\r\n");
/*  68 */       printWriter.print("Accept:*/*\r\n");
/*     */ 
/*     */       
/*  71 */       printWriter.print("Cache-Control:no-cache\r\n");
/*  72 */       printWriter.print("Connection:Keep-Alive\r\n");
/*  73 */       printWriter.print("Content-Length:" + (str4.getBytes()).length + "\r\n");
/*  74 */       printWriter.print("Content-Type:application/x-www-form-urlencoded\r\n");
/*  75 */       printWriter.print("Host:" + str2 + "\r\n");
/*  76 */       printWriter.print("User-Agent:Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.0)\r\n");
/*  77 */       printWriter.print("Pragma:no-cache\r\n");
/*  78 */       printWriter.print("Connection:close\r\n\r\n");
/*     */       
/*  80 */       printWriter.println(str4);
/*     */       
/*  82 */       String str5 = "";
/*  83 */       String str6 = "";
/*     */ 
/*     */       
/*  86 */       while ((str6 = bufferedReader.readLine()) != null) {
/*  87 */         str5 = str5 + str6 + "\r\n";
/*     */       }
/*     */ 
/*     */       
/*  91 */       if (str5.indexOf("\r\n\r\n") != -1) {
/*  92 */         str5 = str5.substring(str5.indexOf("\r\n\r\n"));
/*     */ 
/*     */       
/*     */       }
/*     */        }
/*     */     
/*  98 */     catch (IOException iOException)
/*     */     
/*     */     { 
/*     */       try {
/*     */ 
/*     */         
/* 104 */         if (printWriter != null) {
/* 105 */           printWriter.close();
/*     */         }
/* 107 */         if (bufferedReader != null) {
/* 108 */           bufferedReader.close();
/*     */         }
/* 110 */       } catch (IOException iOException1) {} } catch (NumberFormatException numberFormatException) { try { if (printWriter != null) printWriter.close();  if (bufferedReader != null) bufferedReader.close();  } catch (IOException iOException) {} } finally { try { if (printWriter != null) printWriter.close();  if (bufferedReader != null) bufferedReader.close();  } catch (IOException iOException) {} }
/*     */   
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/TestPost.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */