/*     */ package weaver.workflow.request;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.List;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ 
/*     */ public class WorkflowIsFreeStartNode {
/*   9 */   private String isshow = "";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static void main(String[] paramArrayOfString) {}
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getIsFreeStartNode(String paramString) {
/*  23 */     RecordSet recordSet = new RecordSet();
/*  24 */     String str1 = paramString;
/*  25 */     String str2 = "";
/*  26 */     recordSet.executeSql("select startnodeid from workflow_nodebase where IsFreeNode='1'  and  id in(" + paramString + ")");
/*  27 */     if (recordSet.next()) {
/*  28 */       str2 = Util.null2String(recordSet.getString("startnodeid"));
/*     */     }
/*  30 */     if (!str2.equals("")) {
/*  31 */       if (!this.isshow.equals("1")) {
/*  32 */         str1 = getIsFreeStartNode(str2);
/*     */       }
/*     */     } else {
/*  35 */       this.isshow = "1";
/*  36 */       return str1;
/*     */     } 
/*  38 */     return str1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getIsFreeStart01Node(String paramString) {
/*  47 */     RecordSet recordSet = new RecordSet();
/*  48 */     String str = "";
/*  49 */     recordSet.executeSql("select startnodeid from workflow_nodebase where IsFreeNode='1'  and  id in(" + paramString + ")");
/*  50 */     if (recordSet.next()) {
/*  51 */       str = Util.null2String(recordSet.getString("startnodeid"));
/*     */     }
/*  53 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getNodeid(String paramString) {
/*  61 */     RecordSet recordSet = new RecordSet();
/*  62 */     recordSet.executeSql(" select freefs from workflow_flownode where nodeid=" + paramString + " ");
/*  63 */     if (recordSet.next()) {
/*  64 */       return Util.null2String(recordSet.getString("freefs"));
/*     */     }
/*  66 */     return "";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String isornotFree(String paramString) {
/*  74 */     RecordSet recordSet = new RecordSet();
/*  75 */     recordSet.executeSql(" select isfreenode from workflow_nodebase where   id=" + paramString + " ");
/*  76 */     if (recordSet.next()) {
/*  77 */       return Util.null2String(recordSet.getString("isfreenode"));
/*     */     }
/*  79 */     return "";
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean IScreateNode(String paramString) {
/*  85 */     RecordSet recordSet = new RecordSet();
/*  86 */     recordSet.executeQuery("select mainrequestid,requestid,requestname,requestlevel,mainrequestid,creater,creatertype,createdate,createtime,workflowId,currentstatus,currentnodeid,currentnodetype,status,remindTypes,docids,crmids,prjids,cptids , lastnodeid  from workflow_requestbase where requestid=?", new Object[] { paramString });
/*  87 */     String str1 = "";
/*  88 */     String str2 = "";
/*  89 */     if (recordSet.next()) {
/*  90 */       str1 = recordSet.getString("status");
/*  91 */       str2 = recordSet.getString("currentnodetype");
/*     */     } 
/*  93 */     if (str1.equals("")) {
/*  94 */       if (str2.equals("0")) {
/*  95 */         return true;
/*     */       }
/*     */     } else {
/*  98 */       return false;
/*     */     } 
/* 100 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List getAllNodeid(String paramString1, String paramString2, String paramString3, ArrayList<String> paramArrayList) {
/* 107 */     RecordSet recordSet = new RecordSet();
/* 108 */     String str1 = "";
/* 109 */     if ("oracle".equals(recordSet.getDBType())) {
/* 110 */       str1 = "select nodeid from workflow_nodelink where workflowid=" + paramString2 + " and destnodeid=" + paramString1 + " and  nvl(isreject,0)!=1   order by id asc ";
/* 111 */     } else if (recordSet.getDBType().equals("mysql")) {
/* 112 */       str1 = "select nodeid from workflow_nodelink where workflowid=" + paramString2 + " and destnodeid=" + paramString1 + " and  ifnull(isreject,0)!=1   order by id asc ";
/*     */     } else {
/* 114 */       str1 = "select nodeid from workflow_nodelink where workflowid=" + paramString2 + " and destnodeid=" + paramString1 + "  and isnull(isreject,0)!=1  order by id asc ";
/*     */     } 
/* 116 */     recordSet.executeSql(str1);
/* 117 */     String str2 = "";
/* 118 */     if (recordSet.next()) {
/* 119 */       str2 = recordSet.getString("nodeid");
/*     */     }
/* 121 */     if (!str2.equals("")) {
/*     */       
/* 123 */       paramArrayList.add(str2);
/* 124 */       if (str2.equals(paramString3)) {
/* 125 */         return paramArrayList;
/*     */       }
/* 127 */       getAllNodeid(str2, paramString2, paramString3, paramArrayList);
/*     */     } 
/*     */     
/* 130 */     return paramArrayList;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/WorkflowIsFreeStartNode.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */