/*    */ package weaver.workflow.request;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class MultiHrmCompositorOrderBean
/*    */   implements Serializable
/*    */ {
/*    */   private int compositorOrder;
/*    */   private String hrmId;
/*    */   
/*    */   public void setCompositorOrder(int paramInt) {
/* 28 */     this.compositorOrder = paramInt;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public int getCompositorOrder() {
/* 35 */     return this.compositorOrder;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public void setHrmId(String paramString) {
/* 42 */     this.hrmId = paramString;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getHrmId() {
/* 49 */     return this.hrmId;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/MultiHrmCompositorOrderBean.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */