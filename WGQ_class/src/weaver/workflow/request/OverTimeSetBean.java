/*     */ package weaver.workflow.request;
/*     */ 
/*     */ import com.engine.hrm.biz.ScheduleUtil4Flow;
/*     */ import com.engine.workflow.biz.workflowOvertime.OvertimeBiz;
/*     */ import com.engine.workflow.entity.workflowOvertime.OvertimeEntity;
/*     */ import com.engine.workflow.util.WorkflowOvertimeSettingsUtil;
/*     */ import java.text.SimpleDateFormat;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Calendar;
/*     */ import java.util.Date;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.TimeUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.report.schedulediff.HrmScheduleDiffUtil;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ import weaver.hrm.schedule.manager.HrmScheduleManager;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class OverTimeSetBean
/*     */ {
/*  30 */   private static SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean getRight(int paramInt1, int paramInt2, int paramInt3, int paramInt4, int paramInt5) {
/*  43 */     boolean bool = false;
/*  44 */     String str = "select hasovertime from workflow_nodecustomrcmenu where hasovertime='1' and wfid=" + paramInt2 + " and nodeid=" + paramInt3;
/*  45 */     RecordSet recordSet = new RecordSet();
/*  46 */     recordSet.executeSql(str);
/*  47 */     if (recordSet.next()) {
/*  48 */       str = "select userid from workflow_currentoperator where isremark='0' and requestid=" + paramInt1 + " and (userid=" + paramInt4 + " or (agenttype = 2 and agentorbyagentid = " + paramInt4 + ")) and usertype=" + paramInt5 + " and nodeid=" + paramInt3;
/*  49 */       recordSet.executeSql(str);
/*  50 */       if (recordSet.next()) {
/*  51 */         bool = true;
/*     */       }
/*     */     } 
/*  54 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean dosave(String paramString, int paramInt1, int paramInt2, HttpServletRequest paramHttpServletRequest) {
/*  67 */     boolean bool = true;
/*  68 */     if (!paramString.equals("")) {
/*  69 */       String str2; ArrayList<String> arrayList = Util.TokenizerString(paramString, ",");
/*  70 */       String str1 = (new BaseBean()).getPropValue("WorkflowOvertimeIsNew", "isNew");
/*     */       
/*  72 */       if ("1".equals(str1)) {
/*  73 */         str2 = "delete from workflow_nodeOvertime where requestid=" + paramInt1 + " and nodeid in(" + paramString + ")";
/*     */       } else {
/*  75 */         str2 = "delete from workflow_nodelink where wfrequestid=" + paramInt1 + " and nodeid in(" + paramString + ")";
/*     */       } 
/*  77 */       RecordSet recordSet = new RecordSet();
/*  78 */       recordSet.executeSql(str2);
/*  79 */       for (byte b = 0; b < arrayList.size(); b++) {
/*  80 */         String str3 = "" + arrayList.get(b);
/*  81 */         String str4 = Util.null2String(paramHttpServletRequest.getParameter("nodepasshour_" + str3));
/*  82 */         String str5 = Util.null2String(paramHttpServletRequest.getParameter("nodepassminute_" + str3));
/*  83 */         String str6 = Util.null2String(paramHttpServletRequest.getParameter("ProcessorOpinion_" + str3));
/*  84 */         if (Util.getIntValue(str4) > -1 || Util.getIntValue(str5) > -1) {
/*  85 */           if (str4.trim().equals("")) str4 = "NULL"; 
/*  86 */           if (str5.trim().equals("")) str5 = "NULL";
/*     */           
/*  88 */           if ("1".equals(str1)) {
/*  89 */             str2 = "insert into workflow_nodeOvertime(workflowid,nodeid,nodepasshour,nodepassminute,requestid) values(?,?,?,?,?) ";
/*  90 */             recordSet.executeUpdate(str2, new Object[] { Integer.valueOf(paramInt2), str3, str4, str5, Integer.valueOf(paramInt1) });
/*  91 */             OvertimeBiz.getInstance().addOvertimeTaskThread(paramInt1, paramInt2, Util.getIntValue(str3, 0));
/*     */           } else {
/*  93 */             str2 = "insert into workflow_nodelink(workflowid,linkname,destnodeid,nodepasshour,nodepassminute,ProcessorOpinion,wfrequestid,nodeid) select  " + paramInt2 + ",linkname,destnodeid," + str4 + "," + str5 + ",'" + str6.trim() + "'," + paramInt1 + "," + str3 + " from workflow_nodelink where wfrequestid is null and (isreject is null or isreject<>'1') and exists(select 1 from workflow_nodebase where (isfreenode is null or isfreenode!='1' or (isfreenode='1' and requestid=" + paramInt1 + ")) and id=workflow_nodelink.destnodeid) and nodeid=" + str3;
/*  94 */             System.out.println(str2);
/*  95 */             recordSet.executeSql(str2);
/*     */           } 
/*     */         } 
/*     */       } 
/*     */     } 
/* 100 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCurrentNodeToEndNode(int paramInt1, int paramInt2, int paramInt3, String paramString) {
/* 112 */     RecordSet recordSet = new RecordSet();
/* 113 */     String str = "select distinct c.nodetype,a.destnodeid,b.isend from workflow_NodeLink a,workflow_nodebase b,workflow_flownode c where ((b.IsFreeNode='1' and b.requestid=" + paramInt3 + ") or b.IsFreeNode!='1' or b.IsFreeNode is null) and a.wfrequestid is null and a.destnodeid=b.id and (a.isreject is null or a.isreject<>'1') and a.destnodeid=c.nodeid and a.workflowid=c.workflowid and a.nodeid=" + paramInt1 + " and a.workflowid=" + paramInt2;
/* 114 */     if (paramString != null && !paramString.trim().equals("")) {
/* 115 */       str = str + " and a.destnodeid not in (" + paramString + ")";
/*     */     }
/* 117 */     str = str + " order by c.nodetype,a.destnodeid";
/* 118 */     recordSet.executeSql(str);
/* 119 */     while (recordSet.next()) {
/* 120 */       int i = recordSet.getInt("destnodeid");
/* 121 */       int j = recordSet.getInt("isend");
/* 122 */       if (("," + paramString + ",").indexOf("," + i + ",") < 0 && 
/* 123 */         j != 1) {
/* 124 */         if (paramString.equals("")) {
/* 125 */           paramString = "" + i;
/*     */         } else {
/* 127 */           paramString = paramString + "," + i;
/*     */         } 
/* 129 */         paramString = getCurrentNodeToEndNode(i, paramInt2, paramInt3, paramString);
/*     */       } 
/*     */     } 
/*     */     
/* 133 */     return paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getNodeName(int paramInt) {
/* 143 */     RecordSet recordSet = new RecordSet();
/* 144 */     String str1 = "";
/* 145 */     String str2 = "select nodename from workflow_nodebase where id=" + paramInt;
/* 146 */     recordSet.executeSql(str2);
/* 147 */     if (recordSet.next()) {
/* 148 */       str1 = Util.null2String(recordSet.getString(1));
/*     */     }
/* 150 */     return str1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean getFlowCondition(int paramInt1, int paramInt2, int paramInt3, int paramInt4, String paramString) {
/* 164 */     if (paramString.trim().equals("")) {
/* 165 */       return true;
/*     */     }
/* 167 */     RecordSet recordSet = new RecordSet();
/* 168 */     String str1 = "";
/* 169 */     boolean bool = false;
/* 170 */     String str2 = "";
/* 171 */     if (paramInt2 == 1) {
/* 172 */       str2 = "select tablename from workflow_bill where id = " + paramInt3;
/* 173 */       recordSet.executeSql(str2);
/* 174 */       if (recordSet.next()) {
/* 175 */         str1 = recordSet.getString(1);
/*     */       }
/* 177 */       str2 = "select count(id) from " + str1 + " where id=" + paramInt4 + " and " + paramString;
/*     */     } else {
/* 179 */       str2 = "select count(requestid) from workflow_form where requestid=" + paramInt1 + " and " + paramString;
/*     */     } 
/* 181 */     recordSet.executeSql(str2);
/* 182 */     if (recordSet.next() && recordSet.getInt(1) > 0) {
/* 183 */       bool = true;
/*     */     }
/* 185 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public ArrayList[] getOverTimeInfo(int paramInt1, int paramInt2, int paramInt3, int paramInt4, int paramInt5, int paramInt6, String paramString) {
/* 201 */     ArrayList[] arrayOfArrayList = new ArrayList[4];
/* 202 */     ArrayList arrayList = Util.TokenizerString(paramString, ",");
/* 203 */     ArrayList<String> arrayList1 = new ArrayList();
/* 204 */     ArrayList<String> arrayList2 = new ArrayList();
/* 205 */     for (byte b = 0; b < arrayList.size(); b++) {
/* 206 */       arrayList1.add("");
/* 207 */       arrayList2.add("");
/*     */     } 
/* 209 */     if (!paramString.equals("")) {
/* 210 */       RecordSet recordSet = new RecordSet();
/*     */       
/* 212 */       String str = "select nodeid,nodepasshour,nodepassminute,ProcessorOpinion from workflow_nodeOvertime where requestid=" + paramInt1 + " and nodeid in(" + paramString + ")";
/*     */       
/* 214 */       recordSet.executeSql(str);
/* 215 */       while (recordSet.next()) {
/* 216 */         String str1 = Util.null2String(recordSet.getString("nodeid"));
/* 217 */         String str2 = Util.null2String(recordSet.getString("nodepasshour"));
/* 218 */         String str3 = Util.null2String(recordSet.getString("nodepassminute"));
/* 219 */         int i = arrayList.indexOf(str1);
/* 220 */         if (i != -1) {
/* 221 */           arrayList1.set(i, str2);
/* 222 */           arrayList2.set(i, str3);
/* 223 */           paramString = Util.StringReplace("," + paramString + ",", "," + str1 + ",", "");
/*     */         } 
/*     */       } 
/* 226 */       if (!paramString.equals("") && paramString.startsWith(",")) paramString = paramString.substring(1); 
/* 227 */       if (!paramString.equals("") && paramString.endsWith(",")) paramString = paramString.substring(0, paramString.length() - 1);
/*     */     
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 256 */     arrayOfArrayList[0] = arrayList;
/* 257 */     arrayOfArrayList[1] = arrayList1;
/* 258 */     arrayOfArrayList[2] = arrayList2;
/* 259 */     return arrayOfArrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public ArrayList getPubHoliday() {
/* 268 */     ArrayList<String> arrayList = new ArrayList();
/* 269 */     Calendar calendar = Calendar.getInstance();
/* 270 */     String str = "select holidaydate from HrmPubHoliday where countryid =(select min(id) from hrmcountry) and (changetype=1 or changetype=3) and holidaydate like '" + Util.add0(calendar.get(1), 4) + "%' order by holidaydate";
/* 271 */     RecordSet recordSet = new RecordSet();
/* 272 */     recordSet.executeSql(str);
/* 273 */     while (recordSet.next()) {
/* 274 */       arrayList.add(recordSet.getString(1));
/*     */     }
/* 276 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public ArrayList getOtherWorkday() {
/* 285 */     ArrayList<String> arrayList = new ArrayList();
/* 286 */     Calendar calendar = Calendar.getInstance();
/* 287 */     String str = "select holidaydate from HrmPubHoliday where countryid =(select min(id) from hrmcountry) and changetype=2 and holidaydate like '" + Util.add0(calendar.get(1), 4) + "%' order by holidaydate";
/* 288 */     RecordSet recordSet = new RecordSet();
/* 289 */     recordSet.executeSql(str);
/* 290 */     while (recordSet.next()) {
/* 291 */       arrayList.add(recordSet.getString(1));
/*     */     }
/* 293 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getnextworkday(String paramString, ArrayList paramArrayList1, ArrayList paramArrayList2) {
/* 304 */     String str = paramString;
/*     */     
/* 306 */     if ((TimeUtil.dateWeekday(paramString) == 6 || TimeUtil.dateWeekday(paramString) == 0 || paramArrayList2.indexOf(paramString) != -1) && paramArrayList1.indexOf(paramString) == -1) {
/* 307 */       paramString = TimeUtil.dateAdd(paramString, 1);
/* 308 */       str = getnextworkday(paramString, paramArrayList1, paramArrayList2);
/*     */     } 
/* 310 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public long gettimeInterval(ArrayList paramArrayList1, ArrayList paramArrayList2, String paramString1, String paramString2, String paramString3, String paramString4, long paramLong) {
/* 326 */     String str = getnextworkday(paramString3, paramArrayList1, paramArrayList2);
/*     */     
/* 328 */     if (!str.equals(paramString3)) paramString1 = str + " 00:00:00"; 
/* 329 */     int i = TimeUtil.dateInterval(paramString4, str);
/* 330 */     if (i < 0) {
/* 331 */       paramString1 = Util.StringReplace(paramString1, paramString3, str);
/* 332 */       String str1 = TimeUtil.dateAdd(str, 1);
/* 333 */       String str2 = str1 + " 00:00:00";
/* 334 */       paramLong += TimeUtil.timeInterval(paramString1, str2);
/*     */       
/* 336 */       paramLong = gettimeInterval(paramArrayList1, paramArrayList2, str2, paramString2, str1, paramString4, paramLong);
/* 337 */     } else if (i == 0) {
/* 338 */       paramLong += TimeUtil.timeInterval(paramString1, paramString2);
/*     */     }
/* 340 */     else if (paramString1.indexOf(" 00:00:00") == -1) {
/* 341 */       String str1 = TimeUtil.dateAdd(str, 1) + " 00:00:00";
/* 342 */       paramLong += TimeUtil.timeInterval(paramString1, str1);
/*     */     } 
/*     */ 
/*     */     
/* 346 */     return paramLong;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public long getOverTime(String paramString1, String paramString2) {
/* 356 */     long l = 0L;
/* 357 */     String str = WorkflowOvertimeSettingsUtil.getOverTimeSettingsEntity().getIsSkipWorkingDay() + "";
/* 358 */     if ("1".equals(str)) {
/* 359 */       String str1 = paramString1.substring(0, 10);
/* 360 */       String str2 = paramString1.substring(11, 16);
/* 361 */       String str3 = paramString2.substring(0, 10);
/* 362 */       String str4 = paramString2.substring(11, 16);
/* 363 */       HrmScheduleDiffUtil hrmScheduleDiffUtil = new HrmScheduleDiffUtil();
/* 364 */       double d1 = getWorkHoureTime();
/* 365 */       double d2 = Util.getDoubleValue(hrmScheduleDiffUtil.getTotalWorkingDays(str1, str2, str3, str4, 1));
/* 366 */       l = (long)(d2 * d1 * 3600.0D);
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*     */     }
/*     */     else {
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 377 */       l = TimeUtil.timeInterval(paramString1, paramString2);
/*     */     } 
/* 379 */     return l;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public long getOverTime(int paramInt, String paramString, long paramLong) {
/* 391 */     SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
/* 392 */     long l = 0L;
/* 393 */     String str = WorkflowOvertimeSettingsUtil.getOverTimeSettingsEntity().getIsSkipWorkingDay() + "";
/*     */     
/* 395 */     boolean bool = true;
/*     */     
/* 397 */     if ("1".equals(str)) {
/* 398 */       l = (new ScheduleUtil4Flow()).workingTimeAdd(paramInt, paramString, paramLong);
/* 399 */       bool = (l <= 0L) ? false : true;
/*     */     } 
/*     */ 
/*     */     
/* 403 */     if (!"1".equals(str) || !bool) {
/*     */       try {
/* 405 */         Date date = simpleDateFormat.parse(paramString);
/* 406 */         l = date.getTime() + paramLong;
/* 407 */       } catch (Exception exception) {
/* 408 */         exception.printStackTrace();
/*     */       } 
/*     */     }
/* 411 */     return l;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static void isNeedAddTime(OvertimeEntity paramOvertimeEntity) {
/* 419 */     if (paramOvertimeEntity.getHandleEntity() != null) {
/* 420 */       long l1 = 0L;
/* 421 */       long l2 = (WorkflowOvertimeSettingsUtil.getIntervalOverTime() * 60 * 1000);
/* 422 */       long l3 = paramOvertimeEntity.getOvertimeHandleMs();
/* 423 */       long l4 = (new Date()).getTime();
/* 424 */       long l5 = (l3 > l4) ? l3 : l4;
/* 425 */       String str = "select max(overtime) maxtime from workflow_currentoperator where requestid = " + paramOvertimeEntity.getRequestId();
/* 426 */       RecordSet recordSet = new RecordSet();
/* 427 */       recordSet.executeQuery(str, new Object[0]);
/* 428 */       if (recordSet.next()) {
/*     */         try {
/* 430 */           String str1 = Util.null2String(recordSet.getString("maxtime"));
/* 431 */           l1 = Long.parseLong("".equals(str1) ? "0" : str1);
/* 432 */         } catch (Exception exception) {
/* 433 */           exception.printStackTrace();
/*     */         } 
/*     */       }
/* 436 */       if (l1 != 0L && l5 - l1 < l2) {
/*     */ 
/*     */         
/* 439 */         paramOvertimeEntity.setOvertimeHandleMs(l1 + l2);
/* 440 */         (new BaseBean()).writeLog("requestid:" + paramOvertimeEntity.getRequestId() + "上一次超时处理：" + getDate(l1) + "与此次处理时间（当前时间）:" + getDate((new Date()).getTime()) + "不足" + (l2 / 60000L) + "分钟,故延长超时处理时间到:" + getDate(l1 + l2));
/*     */       } 
/*     */     } 
/*     */   }
/*     */   
/*     */   public static long getOverWorkTime(String paramString1, String paramString2, User paramUser) {
/* 446 */     long l = 0L;
/* 447 */     ResourceComInfo resourceComInfo = null;
/*     */     try {
/* 449 */       resourceComInfo = new ResourceComInfo();
/* 450 */     } catch (Exception exception) {}
/*     */ 
/*     */ 
/*     */     
/*     */     try {
/* 455 */       String str1 = paramString1.substring(0, 10);
/* 456 */       String str2 = paramString1.substring(11, 16);
/* 457 */       String str3 = paramString2.substring(0, 10);
/* 458 */       String str4 = paramString2.substring(11, 16);
/* 459 */       HrmScheduleManager hrmScheduleManager = new HrmScheduleManager(paramUser);
/* 460 */       String str5 = hrmScheduleManager.getTotalWorkHours(str1, str2, str3, str4, Util.getIntValue(resourceComInfo.getSubCompanyID(paramUser.getUID() + ""), 0), paramUser, false);
/* 461 */       if ("0.0".equals(str5)) {
/* 462 */         l = 0L;
/*     */       } else {
/* 464 */         l = (new Double(Util.getDoubleValue(str5, 0.0D) * 60.0D * 60.0D * 1000.0D)).longValue();
/*     */       } 
/* 466 */     } catch (Exception exception) {
/* 467 */       exception.printStackTrace();
/*     */     } 
/* 469 */     return l;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getDate(long paramLong) {
/* 479 */     String str = "";
/* 480 */     Date date = new Date(paramLong);
/*     */     try {
/* 482 */       str = sdf.format(date);
/* 483 */     } catch (Exception exception) {
/* 484 */       exception.printStackTrace();
/*     */     } 
/* 486 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static Date getDate(String paramString) {
/* 496 */     Date date = null;
/*     */     try {
/* 498 */       date = sdf.parse(paramString);
/* 499 */     } catch (Exception exception) {
/* 500 */       exception.printStackTrace();
/*     */     } 
/* 502 */     return date;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public double getWorkHoureTime() {
/* 510 */     String str1 = "";
/* 511 */     String str2 = "";
/* 512 */     String str3 = "";
/* 513 */     String str4 = "";
/* 514 */     RecordSet recordSet = new RecordSet();
/* 515 */     recordSet.executeSql("select monendtime1,monstarttime1,monendtime2,monstarttime2 from HrmSchedule");
/* 516 */     if (recordSet.next()) {
/* 517 */       str1 = recordSet.getString("monendtime1");
/* 518 */       str2 = recordSet.getString("monstarttime1");
/* 519 */       str3 = recordSet.getString("monendtime2");
/* 520 */       str4 = recordSet.getString("monstarttime2");
/*     */     } 
/* 522 */     String str5 = Util.addTime(Util.subTime(str1, str2), Util.subTime(str3, str4));
/* 523 */     double d = 0.0D;
/* 524 */     if (!"".equals(str5)) {
/* 525 */       d = Util.getDoubleValue(str5.substring(0, 2), 0.0D) * 1.0D + Util.getDoubleValue(str5.substring(3, 5), 0.0D) / 60.0D;
/*     */     }
/* 527 */     return d;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/OverTimeSetBean.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */