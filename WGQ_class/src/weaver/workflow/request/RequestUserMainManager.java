/*    */ package weaver.workflow.request;
/*    */ 
/*    */ import weaver.conn.ConnStatement;
/*    */ import weaver.general.BaseBean;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class RequestUserMainManager
/*    */   extends BaseBean
/*    */ {
/*    */   private int currecord;
/*    */   private int requestid;
/*    */   private String sql_where;
/*    */   
/*    */   public void RequestUserMainManager() throws Exception {}
/*    */   
/*    */   public void resetParameter() {
/* 31 */     this.requestid = 0;
/* 32 */     this.sql_where = "";
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public void setRequestid(int paramInt) {
/* 39 */     this.requestid = paramInt;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public void setSql_where(String paramString) {
/* 46 */     this.sql_where = paramString;
/*    */   }
/*    */ 
/*    */   
/*    */   public void updateRequestUser() throws Exception {
/* 51 */     String str1 = "select id from hrm_userinfo where " + this.sql_where;
/* 52 */     String str2 = getPropValue("request", "delete_requestuser");
/* 53 */     String str3 = getPropValue("request", "insert_requestuser");
/* 54 */     ConnStatement connStatement1 = new ConnStatement();
/* 55 */     ConnStatement connStatement2 = new ConnStatement();
/* 56 */     writeLog(str1 + "fuck***********");
/* 57 */     connStatement2 = new ConnStatement();
/*    */     try {
/* 59 */       connStatement2.setStatementSql(str1);
/* 60 */       writeLog(str1 + "fuck***********");
/* 61 */       connStatement2.executeQuery();
/*    */ 
/*    */ 
/*    */       
/* 65 */       connStatement1.setStatementSql(str2);
/* 66 */       connStatement1.setInt(1, this.requestid);
/* 67 */       connStatement1.executeQuery();
/*    */       
/* 69 */       while (connStatement2.next()) {
/* 70 */         connStatement1.setStatementSql(str3);
/* 71 */         connStatement1.setInt(1, this.requestid);
/* 72 */         connStatement1.setInt(2, connStatement2.getInt("id"));
/* 73 */         connStatement1.executeQuery();
/*    */       }
/*    */     
/* 76 */     } catch (Exception exception) {
/* 77 */       writeLog(exception);
/* 78 */       throw exception;
/*    */     } finally {
/*    */       
/* 81 */       try { connStatement2.close(); connStatement1.close(); } catch (Exception exception) {}
/*    */     } 
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/RequestUserMainManager.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */