/*     */ package weaver.workflow.request;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.workflow.field.FieldComInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class RequestOutData
/*     */   extends BaseBean
/*     */ {
/*  28 */   private int requestid = 0;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  33 */   private int curIndex = -1;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  38 */   private HashMap data = new HashMap<Object, Object>();
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  43 */   private ArrayList detailData = new ArrayList();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public RequestOutData(int paramInt) {
/*  52 */     this.requestid = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean loadData() {
/*  61 */     RecordSet recordSet = new RecordSet();
/*  62 */     FieldComInfo fieldComInfo = new FieldComInfo();
/*     */     
/*  64 */     int i = 0;
/*  65 */     int j = 0;
/*  66 */     int k = 0;
/*     */     
/*  68 */     ArrayList<String> arrayList1 = new ArrayList();
/*  69 */     ArrayList<String> arrayList2 = new ArrayList();
/*     */ 
/*     */     
/*  72 */     recordSet.executeProc("workflow_Requestbase_SByID", this.requestid + "");
/*  73 */     if (recordSet.next()) {
/*  74 */       j = Util.getIntValue(recordSet.getString("workflowid"), 0);
/*     */     } else {
/*  76 */       return false;
/*     */     } 
/*     */ 
/*     */     
/*  80 */     recordSet.executeProc("workflow_Workflowbase_SByID", j + "");
/*  81 */     if (recordSet.next()) {
/*  82 */       i = Util.getIntValue(recordSet.getString("formid"), 0);
/*  83 */       k = Util.getIntValue(recordSet.getString("isbill"), 0);
/*     */     } else {
/*  85 */       return false;
/*     */     } 
/*     */ 
/*     */     
/*  89 */     if (k == 1) {
/*  90 */       return false;
/*     */     }
/*     */ 
/*     */     
/*  94 */     recordSet.executeProc("workflow_FieldID_Select", i + "");
/*  95 */     while (recordSet.next()) {
/*  96 */       arrayList1.add(Util.null2String(recordSet.getString(1)));
/*     */     }
/*     */ 
/*     */     
/* 100 */     recordSet.executeProc("workflow_FieldValue_Select", this.requestid + "");
/* 101 */     recordSet.next();
/* 102 */     String str1 = null;
/* 103 */     for (byte b = 0; b < arrayList1.size(); b++) {
/* 104 */       str1 = fieldComInfo.getFieldname(arrayList1.get(b));
/* 105 */       this.data.put(str1, Util.null2String(recordSet.getString(str1)));
/*     */     } 
/*     */ 
/*     */     
/* 109 */     String str2 = "SELECT b.fieldname FROM workflow_formfield a, workflow_formdictdetail b WHERE a.fieldid=b.id and a.isdetail='1' and a.formid=" + i;
/* 110 */     recordSet.executeSql(str2);
/* 111 */     while (recordSet.next()) {
/* 112 */       arrayList2.add(recordSet.getString(1));
/*     */     }
/*     */ 
/*     */     
/* 116 */     str2 = "select * from workflow_formdetail where requestid=" + this.requestid;
/* 117 */     recordSet.executeSql(str2);
/* 118 */     HashMap<Object, Object> hashMap = null;
/* 119 */     while (recordSet.next()) {
/* 120 */       hashMap = new HashMap<Object, Object>();
/* 121 */       for (byte b1 = 0; b1 < arrayList2.size(); b1++) {
/* 122 */         hashMap.put(arrayList2.get(b1), Util.null2String(recordSet.getString(arrayList2.get(b1))));
/*     */       }
/* 124 */       this.detailData.add(hashMap);
/*     */     } 
/*     */     
/* 127 */     return true;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getData(String paramString) {
/* 136 */     return (String)this.data.get(paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean next() {
/* 145 */     if (this.detailData.size() == 0) {
/* 146 */       return false;
/*     */     }
/* 148 */     if (this.curIndex == -1) {
/* 149 */       this.curIndex = 0;
/*     */     } else {
/* 151 */       if (this.curIndex >= this.detailData.size() - 1) {
/* 152 */         return false;
/*     */       }
/* 154 */       this.curIndex++;
/*     */     } 
/*     */ 
/*     */     
/* 158 */     return true;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getDetailData(String paramString) {
/* 167 */     return (String)((HashMap)this.detailData.get(this.curIndex)).get(paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getDetailCount() {
/* 175 */     return this.detailData.size();
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/RequestOutData.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */