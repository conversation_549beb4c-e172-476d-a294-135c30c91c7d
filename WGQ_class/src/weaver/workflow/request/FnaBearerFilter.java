/*     */ package weaver.workflow.request;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.List;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class FnaBearerFilter
/*     */ {
/*     */   public List<String> multiDimensionFilterBearer(int paramInt1, int paramInt2, String paramString1, String paramString2) {
/*  32 */     int i = 0;
/*     */     
/*  34 */     int j = 0;
/*     */     
/*  36 */     int k = 0;
/*  37 */     int m = 0;
/*     */ 
/*     */     
/*  40 */     String str1 = "SELECT a.id,a.isFilter, a.onlyEnd, a.choice  FROM FnaMultiBearerFilter a  WHERE a.workflowId=? AND a.fieldId= ? and a.accountId = ? ";
/*  41 */     RecordSet recordSet = new RecordSet();
/*  42 */     recordSet.executeQuery(str1, new Object[] { Integer.valueOf(paramInt1), Integer.valueOf(paramInt2), paramString2 });
/*  43 */     if (recordSet.next()) {
/*     */       
/*  45 */       i = recordSet.getInt("isFilter");
/*     */       
/*  47 */       j = recordSet.getInt("onlyEnd");
/*     */       
/*  49 */       k = recordSet.getInt("choice");
/*  50 */       m = recordSet.getInt("id");
/*     */     } 
/*  52 */     boolean bool1 = (i >= 1) ? true : false;
/*  53 */     boolean bool2 = (j >= 1) ? true : false;
/*  54 */     boolean bool3 = (k >= 1) ? true : false;
/*     */ 
/*     */ 
/*     */     
/*  58 */     String str2 = recordSet.getDBType();
/*  59 */     String str3 = paramString1 + " a ";
/*  60 */     String str4 = " where 1=1 and (a.isArchive <> 1  or a.isArchive is null) ";
/*  61 */     if (bool1) {
/*     */       String str;
/*     */ 
/*     */       
/*  65 */       if (bool3) {
/*     */         
/*  67 */         str = " = ";
/*     */       } else {
/*     */         
/*  70 */         str = " <> ";
/*     */       } 
/*     */       
/*  73 */       if ("mysql".equalsIgnoreCase(str2)) {
/*  74 */         str3 = str3 + " join FnaMultiBearerFilterDetail c ON left(a.autoCode, length(c.bearerCode)) " + str + " c.bearerCode  join FnaMultiBearerFilter b ON b.id = c.mainId  ";
/*     */       
/*     */       }
/*  77 */       else if ("postgresql".equalsIgnoreCase(str2)) {
/*  78 */         str3 = str3 + " join FnaMultiBearerFilterDetail c ON left(a.autoCode, length(c.bearerCode)) " + str + " c.bearerCode  join FnaMultiBearerFilter b ON b.id = c.mainId  ";
/*     */       
/*     */       }
/*  81 */       else if ("sqlserver".equalsIgnoreCase(str2)) {
/*  82 */         str3 = str3 + " join FnaMultiBearerFilterDetail c on left(a.autoCode, len(c.bearerCode)) " + str + " c.bearerCode  join FnaMultiBearerFilter b ON b.id = c.mainId ";
/*     */       }
/*     */       else {
/*     */         
/*  86 */         str3 = str3 + " join FnaMultiBearerFilterDetail c on substr(a.autoCode, 0, length(c.bearerCode)) " + str + " c.bearerCode  join FnaMultiBearerFilter b ON b.id = c.mainId ";
/*     */       } 
/*     */     } 
/*     */ 
/*     */     
/*  91 */     if (bool2)
/*     */     {
/*  93 */       if ("mysql".equalsIgnoreCase(str2)) {
/*  94 */         str4 = str4 + " and NOT exists(select 1 from " + paramString1 + " t where t.autoCode like concat(a.autoCode,'_%')) ";
/*  95 */       } else if ("sqlserver".equalsIgnoreCase(str2)) {
/*  96 */         str4 = str4 + " and NOT exists(select 1 from " + paramString1 + " t where t.autoCode like a.autoCode+'_%') ";
/*     */       } else {
/*     */         
/*  99 */         str4 = str4 + " and NOT exists(select 1 from " + paramString1 + " t where t.autoCode like a.autoCode||'_%') ";
/*     */       } 
/*     */     }
/*     */     
/* 103 */     if (bool1) {
/*     */       
/* 105 */       str4 = str4 + " AND b.workflowId=" + paramInt1 + " AND b.fieldId=" + paramInt2;
/* 106 */       if (!bool3)
/*     */       {
/* 108 */         str4 = str4 + " and a.id not in (  select ta.bearerCode from FnaMultiBearerFilterDetail ta join FnaMultiBearerFilter tb on tb.id=ta.mainId  where tb.workflowId=" + paramInt1 + " and tb.fieldId=" + paramInt2 + ") ";
/*     */       }
/*     */     } 
/*     */ 
/*     */     
/* 113 */     ArrayList<String> arrayList = new ArrayList();
/*     */     
/* 115 */     str1 = "SELECT a.id from " + str3 + str4;
/* 116 */     recordSet.executeQuery(str1, new Object[0]);
/* 117 */     while (recordSet.next()) {
/* 118 */       String str = Util.null2String(recordSet.getString("id"));
/*     */       
/* 120 */       arrayList.add(str);
/*     */     } 
/* 122 */     return arrayList;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/FnaBearerFilter.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */