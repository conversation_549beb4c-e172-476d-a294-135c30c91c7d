/*    */ package weaver.workflow.request;
/*    */ 
/*    */ import java.util.concurrent.ExecutorService;
/*    */ import java.util.concurrent.Executors;
/*    */ import weaver.general.Util;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class RequestAddShareThreadPoolUtil
/*    */ {
/* 13 */   private static ExecutorService fixedThreadPool = null;
/*    */   
/*    */   static {
/* 16 */     if (fixedThreadPool == null) {
/* 17 */       int i = Util.getIntValue(Util.null2String((new WFPathUtil()).getPropValue("ThreadPoolConfig", "requestAddShareThreadcount")));
/* 18 */       if (i < 5) {
/* 19 */         i = 5;
/*    */       }
/* 21 */       fixedThreadPool = Executors.newFixedThreadPool(i);
/*    */     } 
/*    */   }
/*    */   
/*    */   public static ExecutorService getFixedThreadPool() {
/* 26 */     return fixedThreadPool;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/RequestAddShareThreadPoolUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */