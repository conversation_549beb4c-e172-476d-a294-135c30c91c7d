/*     */ package weaver.workflow.request;
/*     */ 
/*     */ import java.util.List;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.workflow.workflow.GroupDetailMatrix;
/*     */ import weaver.workflow.workflow.GroupDetailMatrixDetail;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class MatrixConvert
/*     */ {
/*     */   public static void main(String[] paramArrayOfString) {}
/*     */   
/*     */   public static void convert(List<String[]> paramList) throws Exception {
/*  20 */     if (paramList == null)
/*     */       return; 
/*  22 */     String[] arrayOfString1 = paramList.get(0);
/*  23 */     String[] arrayOfString2 = paramList.get(1);
/*     */     
/*  25 */     convert2Matrix2(arrayOfString1[0], arrayOfString1[1]);
/*  26 */     convert2Matrix(arrayOfString2[0], arrayOfString2[1]);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static void convert2Matrix(String paramString1, String paramString2) throws Exception {
/*  36 */     RecordSet recordSet1 = new RecordSet();
/*  37 */     RecordSet recordSet2 = new RecordSet();
/*  38 */     String str = "";
/*  39 */     if (recordSet1.getDBType().equals("oracle")) {
/*  40 */       str = "select id, subcompanyfield, deptField, objid from workflow_groupdetail where (subcompanyfield is not null and 'a'||subcompanyfield<>'a')";
/*     */     } else {
/*  42 */       str = "select id, subcompanyfield, deptField, objid from workflow_groupdetail where (subcompanyfield is not null and subcompanyfield<>'')";
/*     */     } 
/*  44 */     recordSet1.executeSql(str);
/*     */     
/*  46 */     while (recordSet1.next()) {
/*  47 */       String str1 = Util.null2String(recordSet1.getString("id"));
/*  48 */       String str2 = Util.null2String(recordSet1.getString("subcompanyfield"));
/*  49 */       String str3 = Util.null2String(recordSet1.getString("objid"));
/*     */       
/*  51 */       String[] arrayOfString = Util.TokenizerString2(str2, "[_]");
/*  52 */       if (arrayOfString.length < 3)
/*     */         continue; 
/*  54 */       String str4 = "";
/*  55 */       for (byte b = 1; b < arrayOfString.length - 1; b++) {
/*  56 */         if (b == 1) {
/*  57 */           str4 = arrayOfString[b];
/*     */         } else {
/*  59 */           str4 = str4 + "_" + arrayOfString[b];
/*     */         } 
/*     */       } 
/*     */ 
/*     */       
/*  64 */       if (str4 == null || "".equals(str4)) {
/*     */         continue;
/*     */       }
/*     */       
/*  68 */       String str5 = "select id from MatrixFieldInfo WHERE fieldname='" + str4 + "' and matrixid=" + paramString1;
/*  69 */       RecordSet recordSet = new RecordSet();
/*  70 */       recordSet.executeSql(str5);
/*  71 */       if (recordSet.next()) {
/*  72 */         String str6 = Util.null2String(recordSet.getString("id"));
/*  73 */         GroupDetailMatrix groupDetailMatrix = new GroupDetailMatrix();
/*  74 */         groupDetailMatrix.setGroupdetailid(str1);
/*  75 */         groupDetailMatrix.setMatrix(paramString1);
/*  76 */         groupDetailMatrix.setValue_field(str6);
/*  77 */         recordSet2.executeSql("select * from workflow_groupdetail_matrix where groupdetailid ='" + str1 + "'");
/*  78 */         if (recordSet2.next()) {
/*  79 */           groupDetailMatrix.update(recordSet);
/*     */         } else {
/*  81 */           groupDetailMatrix.save(recordSet);
/*     */         } 
/*     */         
/*  84 */         GroupDetailMatrixDetail groupDetailMatrixDetail = new GroupDetailMatrixDetail();
/*  85 */         groupDetailMatrixDetail.setGroupdetailid(str1);
/*  86 */         groupDetailMatrixDetail.setCondition_field(paramString2);
/*  87 */         groupDetailMatrixDetail.setWorkflow_field(str3);
/*  88 */         recordSet2.executeSql("select * from workflow_matrixdetail where groupdetailid ='" + str1 + "'");
/*  89 */         if (recordSet2.next()) {
/*  90 */           recordSet2.executeSql("update workflow_matrixdetail set condition_field='" + paramString2 + "',workflow_field='" + str3 + "' where groupdetailid ='" + str1 + "'");
/*     */         } else {
/*  92 */           groupDetailMatrixDetail.save(recordSet);
/*     */         } 
/*     */         
/*  95 */         String str7 = "update workflow_groupdetail set type=99 where id=" + str1;
/*  96 */         recordSet.executeSql(str7);
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static void convert2Matrix2(String paramString1, String paramString2) throws Exception {
/* 110 */     RecordSet recordSet1 = new RecordSet();
/* 111 */     RecordSet recordSet2 = new RecordSet();
/* 112 */     String str = "";
/* 113 */     if (recordSet1.getDBType().equals("oracle")) {
/* 114 */       str = "select id, subcompanyfield, deptField, objid from workflow_groupdetail where deptField is not null and 'a'||deptField<>'a'";
/*     */     } else {
/* 116 */       str = "select id, subcompanyfield, deptField, objid from workflow_groupdetail where deptField is not null and deptField<>''";
/*     */     } 
/* 118 */     recordSet1.executeSql(str);
/*     */     
/* 120 */     while (recordSet1.next()) {
/* 121 */       String str1 = Util.null2String(recordSet1.getString("id"));
/* 122 */       String str2 = Util.null2String(recordSet1.getString("deptField"));
/* 123 */       String str3 = Util.null2String(recordSet1.getString("objid"));
/*     */       
/* 125 */       String[] arrayOfString = Util.TokenizerString2(str2, "[_]");
/* 126 */       if (arrayOfString.length < 3)
/*     */         continue; 
/* 128 */       String str4 = "";
/* 129 */       for (byte b = 1; b < arrayOfString.length - 1; b++) {
/* 130 */         if (b == 1) {
/* 131 */           str4 = arrayOfString[b];
/*     */         } else {
/* 133 */           str4 = str4 + "_" + arrayOfString[b];
/*     */         } 
/*     */       } 
/*     */       
/* 137 */       if (str4 == null || "".equals(str4)) {
/*     */         continue;
/*     */       }
/*     */       
/* 141 */       String str5 = "select id from MatrixFieldInfo WHERE fieldname='" + str4 + "' and matrixid=" + paramString1;
/* 142 */       RecordSet recordSet = new RecordSet();
/* 143 */       recordSet.executeSql(str5);
/* 144 */       if (recordSet.next()) {
/* 145 */         String str6 = Util.null2String(recordSet.getString("id"));
/* 146 */         GroupDetailMatrix groupDetailMatrix = new GroupDetailMatrix();
/* 147 */         groupDetailMatrix.setGroupdetailid(str1);
/* 148 */         groupDetailMatrix.setMatrix(paramString1);
/* 149 */         groupDetailMatrix.setValue_field(str6);
/* 150 */         recordSet2.executeSql("select * from workflow_groupdetail_matrix where groupdetailid ='" + str1 + "'");
/* 151 */         if (recordSet2.next()) {
/* 152 */           groupDetailMatrix.update(recordSet);
/*     */         } else {
/* 154 */           groupDetailMatrix.save(recordSet);
/*     */         } 
/*     */         
/* 157 */         GroupDetailMatrixDetail groupDetailMatrixDetail = new GroupDetailMatrixDetail();
/* 158 */         groupDetailMatrixDetail.setGroupdetailid(str1);
/* 159 */         groupDetailMatrixDetail.setCondition_field(paramString2);
/* 160 */         groupDetailMatrixDetail.setWorkflow_field(str3);
/* 161 */         recordSet2.executeSql("select * from workflow_matrixdetail where groupdetailid ='" + str1 + "'");
/* 162 */         if (recordSet2.next()) {
/* 163 */           recordSet2.executeSql("update workflow_matrixdetail set condition_field='" + paramString2 + "',workflow_field='" + str3 + "' where groupdetailid ='" + str1 + "'");
/*     */         } else {
/* 165 */           groupDetailMatrixDetail.save(recordSet);
/*     */         } 
/*     */         
/* 168 */         String str7 = "update workflow_groupdetail set type=99 where id=" + str1;
/* 169 */         recordSet.executeSql(str7);
/*     */       } 
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/MatrixConvert.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */