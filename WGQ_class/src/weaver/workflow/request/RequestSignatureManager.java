/*    */ package weaver.workflow.request;
/*    */ 
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.BaseBean;
/*    */ import weaver.general.Util;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class RequestSignatureManager
/*    */   extends BaseBean
/*    */ {
/*    */   public boolean ifHasSignatureSucceed(int paramInt1, int paramInt2, int paramInt3, int paramInt4) {
/* 32 */     boolean bool = false;
/* 33 */     RecordSet recordSet = new RecordSet();
/*    */     
/* 35 */     int i = 0;
/* 36 */     int j = 0;
/* 37 */     String str = "";
/*    */     
/* 39 */     recordSet.executeSql("select workflowId,currentNodeId from workflow_Requestbase where requestid=" + paramInt1);
/* 40 */     if (recordSet.next()) {
/* 41 */       i = Util.getIntValue(recordSet.getString("workflowId"), 0);
/* 42 */       j = Util.getIntValue(recordSet.getString("currentNodeId"), 0);
/*    */     } 
/*    */     
/* 45 */     if (paramInt2 != j) {
/* 46 */       return bool;
/*    */     }
/*    */ 
/*    */ 
/*    */     
/* 51 */     int k = 0;
/* 52 */     recordSet.executeSql("select sum(signNum) from Workflow_RequestSign where requestId=" + paramInt1);
/* 53 */     if (recordSet.next()) {
/* 54 */       k = Util.getIntValue(recordSet.getString(1), 0);
/*    */     }
/*    */     
/* 57 */     if (k > 0) {
/* 58 */       bool = true;
/*    */     }
/*    */     
/* 61 */     return bool;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/RequestSignatureManager.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */