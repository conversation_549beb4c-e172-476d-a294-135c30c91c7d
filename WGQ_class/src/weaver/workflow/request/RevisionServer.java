/*     */ package weaver.workflow.request;
/*     */ 
/*     */ import DBstep.iMsgServer2000;
/*     */ import java.io.Writer;
/*     */ import javax.servlet.ServletOutputStream;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import oracle.sql.CLOB;
/*     */ import org.apache.commons.lang.StringUtils;
/*     */ import weaver.conn.ConnStatement;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.GCONST;
/*     */ import weaver.general.ThreadVarLanguage;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.HrmUserVarify;
/*     */ import weaver.hrm.User;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class RevisionServer
/*     */   extends BaseBean
/*     */ {
/*     */   private RevisionInfo revisionInfo;
/*     */   private iMsgServer2000 msgObj;
/*     */   private HttpServletRequest request;
/*     */   private HttpServletResponse response;
/*     */   private User user;
/*     */   
/*     */   public RevisionServer(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/*  44 */     this.revisionInfo = new RevisionInfo();
/*  45 */     this.msgObj = new iMsgServer2000();
/*  46 */     this.user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/*  47 */     this.request = paramHttpServletRequest;
/*  48 */     this.response = paramHttpServletResponse;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void doCommand() throws Exception {
/*  57 */     String str1 = "";
/*  58 */     String str2 = "";
/*     */     try {
/*  60 */       String str = GCONST.getRootPath() + "tempfile";
/*  61 */       this.revisionInfo.setFilePath(str);
/*     */       
/*  63 */       if (this.request.getMethod().equalsIgnoreCase("POST")) {
/*     */         
/*  65 */         this.msgObj.Load(this.request);
/*  66 */         if (this.msgObj.GetMsgByName("DBSTEP").equalsIgnoreCase("DBSTEP")) {
/*  67 */           str1 = this.msgObj.GetMsgByName("OPTION");
/*  68 */           writeLog("doCommand() option=" + str1);
/*     */ 
/*     */           
/*  71 */           if (str1.equalsIgnoreCase("SIGNATRUELIST")) {
/*  72 */             this.revisionInfo.setUserName(this.msgObj.GetMsgByName("USERID"));
/*     */             
/*  74 */             if (SignatureList(this.revisionInfo, this.msgObj)) {
/*  75 */               this.msgObj.SetMsgByName("SIGNATRUELIST", this.revisionInfo.getMarkList());
/*  76 */               this.msgObj.MsgError("");
/*     */             } else {
/*     */               
/*  79 */               this.msgObj.MsgError("创建印章列表失败!");
/*     */             }
/*     */           
/*  82 */           } else if (str1.equalsIgnoreCase("SIGNATRUEIMAGE")) {
/*  83 */             this.revisionInfo.setMarkName(this.msgObj.GetMsgByName("IMAGENAME"));
/*  84 */             this.revisionInfo.setUserName(this.msgObj.GetMsgByName("USERID"));
/*  85 */             this.revisionInfo.setPassword(this.msgObj.GetMsgByName("PASSWORD"));
/*  86 */             this.revisionInfo.setFileType("_wev8.jpg");
/*     */             
/*  88 */             if (SignatureImage(this.revisionInfo, this.msgObj)) {
/*  89 */               this.msgObj.SetMsgByName("IMAGETYPE", this.revisionInfo.getFileType());
/*  90 */               if (this.revisionInfo.getFileBody() != null && (this.revisionInfo.getFileBody()).length > 0) {
/*  91 */                 this.msgObj.MsgFileBody(this.revisionInfo.getFileBody());
/*     */               } else {
/*  93 */                 this.msgObj.MsgFileLoad(this.revisionInfo.getFilePath());
/*     */               } 
/*  95 */               this.msgObj.SetMsgByName("STATUS", "" + SystemEnv.getHtmlLabelName(10003406, ThreadVarLanguage.getLang()) + "!");
/*  96 */               this.msgObj.MsgError("");
/*     */             } else {
/*     */               
/*  99 */               this.msgObj.MsgError("签名或密码错误!");
/*     */             }
/*     */           
/* 102 */           } else if (str1.equalsIgnoreCase("SAVESIGNATURE")) {
/* 103 */             this.revisionInfo.setRecordID(this.msgObj.GetMsgByName("RECORDID"));
/* 104 */             this.revisionInfo.setFieldName(this.msgObj.GetMsgByName("FIELDNAME"));
/* 105 */             this.revisionInfo.setFieldValue(this.msgObj.GetMsgByName("FIELDVALUE"));
/* 106 */             this.revisionInfo.setUserName(this.msgObj.GetMsgByName("USERNAME"));
/* 107 */             this.revisionInfo.setDateTime(this.msgObj.GetMsgByName("DATETIME"));
/* 108 */             this.revisionInfo.setHostName(this.request.getRemoteAddr());
/* 109 */             String str3 = StringUtils.trimToEmpty(this.msgObj.GetMsgByName("TEMPCALL") + "");
/* 110 */             String str4 = StringUtils.trimToEmpty(this.msgObj.GetMsgByName("TEMPID") + "");
/* 111 */             writeLog("doCommand() tempCall=" + str3 + ",tempId=" + str4);
/* 112 */             if ("1".equals(str3)) {
/* 113 */               updateRemark(this.revisionInfo, str4);
/* 114 */               this.msgObj.SetMsgByName("STATUS", "" + SystemEnv.getHtmlLabelName(10004320, ThreadVarLanguage.getLang()) + "!");
/* 115 */               this.msgObj.MsgError("");
/*     */             }
/* 117 */             else if (SaveSignature(this.revisionInfo, this.msgObj)) {
/* 118 */               this.msgObj.SetMsgByName("STATUS", "" + SystemEnv.getHtmlLabelName(10004320, ThreadVarLanguage.getLang()) + "!");
/* 119 */               this.msgObj.MsgError("");
/*     */             } else {
/*     */               
/* 122 */               this.msgObj.MsgError("保存签章信息失败!");
/*     */             } 
/*     */             
/* 125 */             writeLog("SAVESIGNATURE RECORDID end ", this.msgObj.GetMsgByName("RECORDID"));
/* 126 */             this.msgObj.SetMsgByName("TEMPRECORDID", this.msgObj.GetMsgByName("RECORDID"));
/* 127 */             writeLog("SAVESIGNATURE TEMPRECORDID end ", this.msgObj.GetMsgByName("TEMPRECORDID"));
/* 128 */             this.revisionInfo.setRecordID(this.msgObj.GetMsgByName("RECORDID"));
/*     */           }
/* 130 */           else if (str1.equalsIgnoreCase("LOADSIGNATURE")) {
/* 131 */             this.revisionInfo.setRecordID(this.msgObj.GetMsgByName("RECORDID"));
/* 132 */             this.revisionInfo.setFieldName(this.msgObj.GetMsgByName("FIELDNAME"));
/* 133 */             this.revisionInfo.setUserName(this.msgObj.GetMsgByName("USERNAME"));
/*     */             
/* 135 */             if (LoadSignature(this.revisionInfo, this.msgObj)) {
/* 136 */               this.msgObj.SetMsgByName("FIELDVALUE", this.revisionInfo.getFieldValue());
/* 137 */               this.msgObj.SetMsgByName("STATUS", "" + SystemEnv.getHtmlLabelName(10003414, ThreadVarLanguage.getLang()) + "!");
/* 138 */               this.msgObj.MsgError("");
/*     */             } else {
/*     */               
/* 141 */               this.msgObj.MsgError("调入标签失败!");
/*     */             }
/*     */           
/* 144 */           } else if (str1.equalsIgnoreCase("SAVEHISTORY")) {
/* 145 */             writeLog("SAVEHISTORY TEMPRECORDID", this.msgObj.GetMsgByName("TEMPRECORDID"));
/* 146 */             writeLog("SAVEHISTORY RECORDID", this.msgObj.GetMsgByName("RECORDID"));
/* 147 */             this.revisionInfo.setRecordID(this.msgObj.GetMsgByName("TEMPRECORDID"));
/* 148 */             this.revisionInfo.setFieldName(this.msgObj.GetMsgByName("FIELDNAME"));
/* 149 */             this.revisionInfo.setMarkName(this.msgObj.GetMsgByName("MARKNAME"));
/* 150 */             this.revisionInfo.setUserName(this.msgObj.GetMsgByName("USERNAME"));
/* 151 */             this.revisionInfo.setDateTime(this.msgObj.GetMsgByName("DATETIME"));
/* 152 */             this.revisionInfo.setHostName(this.request.getRemoteAddr());
/* 153 */             this.revisionInfo.setMarkGuid(this.msgObj.GetMsgByName("MARKGUID"));
/* 154 */             this.msgObj.SetMsgByName("RECORDID", this.msgObj.GetMsgByName("TEMPRECORDID"));
/*     */             
/* 156 */             if (SaveHistory(this.revisionInfo, this.msgObj)) {
/* 157 */               this.msgObj.SetMsgByName("MARKNAME", this.revisionInfo.getMarkName());
/* 158 */               this.msgObj.SetMsgByName("USERNAME", this.revisionInfo.getUserName());
/* 159 */               this.msgObj.SetMsgByName("DATETIME", this.revisionInfo.getDateTime());
/* 160 */               this.msgObj.SetMsgByName("HOSTNAME", this.revisionInfo.getHostName());
/* 161 */               this.msgObj.SetMsgByName("MARKGUID", this.revisionInfo.getMarkGuid());
/* 162 */               this.msgObj.SetMsgByName("STATUS", "" + SystemEnv.getHtmlLabelName(10004321, ThreadVarLanguage.getLang()) + "!");
/* 163 */               this.msgObj.MsgError("");
/*     */             } else {
/*     */               
/* 166 */               this.msgObj.MsgError("保存印章日志失败!");
/*     */             }
/*     */           
/* 169 */           } else if (str1.equalsIgnoreCase("SHOWHISTORY")) {
/* 170 */             this.revisionInfo.setRecordID(this.msgObj.GetMsgByName("RECORDID"));
/* 171 */             this.revisionInfo.setFieldName(this.msgObj.GetMsgByName("FIELDNAME"));
/* 172 */             this.revisionInfo.setUserName(this.msgObj.GetMsgByName("USERNAME"));
/*     */             
/* 174 */             if (ShowHistory(this.revisionInfo, this.msgObj)) {
/* 175 */               this.msgObj.SetMsgByName("MARKNAME", this.revisionInfo.getMarkName());
/* 176 */               this.msgObj.SetMsgByName("USERNAME", this.revisionInfo.getUserName());
/* 177 */               this.msgObj.SetMsgByName("DATETIME", this.revisionInfo.getDateTime());
/* 178 */               this.msgObj.SetMsgByName("HOSTNAME", this.revisionInfo.getHostName());
/* 179 */               this.msgObj.SetMsgByName("MARKGUID", this.revisionInfo.getMarkGuid());
/* 180 */               this.msgObj.SetMsgByName("STATUS", "" + SystemEnv.getHtmlLabelName(10004322, ThreadVarLanguage.getLang()) + "");
/* 181 */               this.msgObj.MsgError("");
/*     */             } else {
/*     */               
/* 184 */               this.msgObj.SetMsgByName("STATUS", "" + SystemEnv.getHtmlLabelName(10004323, ThreadVarLanguage.getLang()) + "");
/* 185 */               this.msgObj.MsgError("调入印章日志失败");
/*     */             }
/*     */           
/* 188 */           } else if (str1.equalsIgnoreCase("SAVEASIMG")) {
/* 189 */             this.revisionInfo.setRecordID(this.msgObj.GetMsgByName("RECORDID"));
/* 190 */             this.revisionInfo.setFileName(this.msgObj.GetMsgByName("FILENAME"));
/* 191 */             boolean bool = SaveSignature(this.revisionInfo, this.msgObj);
/* 192 */             writeLog("doCommand() flag=" + bool);
/*     */             
/* 194 */             if (bool) {
/* 195 */               this.msgObj.SetMsgByName("STATUS", "" + SystemEnv.getHtmlLabelName(10004324, ThreadVarLanguage.getLang()) + "");
/* 196 */               this.msgObj.MsgError("");
/*     */             } else {
/* 198 */               this.msgObj.SetMsgByName("STATUS", "" + SystemEnv.getHtmlLabelName(10004325, ThreadVarLanguage.getLang()) + "");
/* 199 */               this.msgObj.MsgError("图片保存失败");
/*     */             } 
/*     */           } 
/*     */         } else {
/*     */           
/* 204 */           this.msgObj.MsgError("客户端发送数据包错误!");
/* 205 */           this.msgObj.MsgTextClear();
/* 206 */           this.msgObj.MsgFileClear();
/*     */         } 
/*     */       } else {
/*     */         
/* 210 */         this.msgObj.MsgError("请使用Post方法");
/* 211 */         this.msgObj.MsgTextClear();
/* 212 */         this.msgObj.MsgFileClear();
/*     */       } 
/*     */       
/* 215 */       this.msgObj.Send(this.response);
/* 216 */     } catch (Exception exception) {
/*     */       
/* 218 */       exception.printStackTrace();
/* 219 */       throw exception;
/*     */     } 
/*     */   }
/*     */   
/*     */   private void updateRemark(RevisionInfo paramRevisionInfo, String paramString) {
/* 224 */     int i = Util.getIntValue(paramString, 0);
/* 225 */     ConnStatement connStatement = null;
/*     */     try {
/* 227 */       RecordSet recordSet = new RecordSet();
/* 228 */       connStatement = new ConnStatement();
/* 229 */       boolean bool = ("oracle".equalsIgnoreCase(recordSet.getDBType()) && Util.null2String(recordSet.getOrgindbtype()).equals("oracle")) ? true : false;
/* 230 */       if (bool) {
/* 231 */         String str = "update Workflow_FormSignRemark set remark=empty_clob() where requestLogId=" + i;
/* 232 */         connStatement.setStatementSql(str);
/* 233 */         connStatement.executeUpdate();
/* 234 */         str = "select remark from Workflow_FormSignRemark where requestLogId= " + i;
/* 235 */         connStatement.setStatementSql(str, false);
/* 236 */         connStatement.executeQuery();
/* 237 */         if (connStatement.next()) {
/* 238 */           CLOB cLOB = connStatement.getClob(1);
/* 239 */           char[] arrayOfChar = Util.null2String(paramRevisionInfo.getFieldValue()).toCharArray();
/* 240 */           Writer writer = cLOB.getCharacterOutputStream();
/* 241 */           writer.write(arrayOfChar);
/* 242 */           writer.flush();
/* 243 */           writer.close();
/*     */         } 
/*     */       } else {
/* 246 */         String str = "update Workflow_FormSignRemark set remark=? where requestLogId=?";
/* 247 */         connStatement.setStatementSql(str);
/* 248 */         connStatement.setString(1, paramRevisionInfo.getFieldValue());
/* 249 */         connStatement.setInt(2, i);
/* 250 */         connStatement.executeUpdate();
/*     */       } 
/* 252 */     } catch (Exception exception) {
/* 253 */       exception.printStackTrace();
/*     */     } finally {
/* 255 */       if (null != connStatement) {
/*     */         try {
/* 257 */           connStatement.close();
/* 258 */         } catch (Exception exception) {
/* 259 */           exception.printStackTrace();
/*     */         } 
/*     */       }
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   protected boolean SignatureList(RevisionInfo paramRevisionInfo, iMsgServer2000 paramiMsgServer2000) {
/* 273 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   protected boolean SignatureImage(RevisionInfo paramRevisionInfo, iMsgServer2000 paramiMsgServer2000) {
/* 283 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   protected boolean SaveSignature(RevisionInfo paramRevisionInfo, iMsgServer2000 paramiMsgServer2000) {
/* 293 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   protected boolean LoadSignature(RevisionInfo paramRevisionInfo, iMsgServer2000 paramiMsgServer2000) {
/* 303 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   protected boolean SaveHistory(RevisionInfo paramRevisionInfo, iMsgServer2000 paramiMsgServer2000) {
/* 313 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   protected boolean ShowHistory(RevisionInfo paramRevisionInfo, iMsgServer2000 paramiMsgServer2000) {
/* 323 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   protected boolean SaveAsJpgEx(RevisionInfo paramRevisionInfo, iMsgServer2000 paramiMsgServer2000) {
/* 333 */     return false;
/*     */   }
/*     */ 
/*     */   
/*     */   private void SendPackage(HttpServletResponse paramHttpServletResponse) {
/*     */     try {
/* 339 */       ServletOutputStream servletOutputStream = paramHttpServletResponse.getOutputStream();
/* 340 */       servletOutputStream.write(this.msgObj.MsgVariant());
/* 341 */       servletOutputStream.flush();
/* 342 */       servletOutputStream.close();
/*     */     }
/* 344 */     catch (Exception exception) {}
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public User getUser() {
/* 353 */     return this.user;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setUser(User paramUser) {
/* 360 */     this.user = paramUser;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/RevisionServer.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */