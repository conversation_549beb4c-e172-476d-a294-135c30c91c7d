/*     */ package weaver.workflow.request;
/*     */ 
/*     */ import com.engine.common.service.impl.HrmCommonServiceImpl;
/*     */ import java.util.ArrayList;
/*     */ import org.json.JSONArray;
/*     */ import org.json.JSONObject;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.ThreadVarLanguage;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.check.JobComInfo;
/*     */ import weaver.hrm.company.DepartmentComInfo;
/*     */ import weaver.hrm.company.SubCompanyComInfo;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ import weaver.hrm.roles.RolesComInfo;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class ResourceConditionManager
/*     */   extends BaseBean
/*     */ {
/*     */   public ResourceComInfo resourceComInfo;
/*     */   public SubCompanyComInfo subCompanyComInfo;
/*     */   public DepartmentComInfo departmentComInfo;
/*     */   public RolesComInfo rolesComInfo;
/*     */   public JobComInfo jobComInfo;
/*     */   private int workflowid;
/*     */   
/*     */   public int getWorkflowid() {
/*  41 */     return this.workflowid;
/*     */   }
/*     */   
/*     */   public void setWorkflowid(int paramInt) {
/*  45 */     this.workflowid = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public ResourceConditionManager() {
/*     */     try {
/*  56 */       this.resourceComInfo = new ResourceComInfo();
/*  57 */       this.subCompanyComInfo = new SubCompanyComInfo();
/*  58 */       this.departmentComInfo = new DepartmentComInfo();
/*  59 */       this.rolesComInfo = new RolesComInfo();
/*  60 */       this.jobComInfo = new JobComInfo();
/*     */     }
/*  62 */     catch (Exception exception) {
/*  63 */       exception.printStackTrace();
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getBrowserTRString(String paramString, int paramInt) {
/*  73 */     return getBrowserTRString(paramString, paramInt, false);
/*     */   }
/*     */   public String getBrowserTRString(String paramString, int paramInt, boolean paramBoolean) {
/*  76 */     String str1 = "";
/*  77 */     JSONArray jSONArray1 = new JSONArray();
/*  78 */     if (paramString == null || paramString.equals("")) {
/*  79 */       if (paramBoolean) return jSONArray1.toString(); 
/*  80 */       return "";
/*     */     } 
/*  82 */     ArrayList<String> arrayList1 = Util.TokenizerString(paramString, "~");
/*     */     
/*  84 */     String str2 = "";
/*  85 */     ArrayList<String> arrayList2 = null;
/*  86 */     String str3 = "";
/*  87 */     int i = 0;
/*  88 */     String str4 = "";
/*  89 */     String str5 = "";
/*  90 */     ArrayList<String> arrayList3 = null;
/*  91 */     String str6 = "";
/*  92 */     String str7 = "";
/*  93 */     String str8 = "";
/*  94 */     int j = 0;
/*  95 */     String str9 = "";
/*  96 */     String str10 = "";
/*  97 */     String str11 = "";
/*     */     
/*  99 */     String str12 = "dataLight";
/* 100 */     JSONArray jSONArray2 = null;
/* 101 */     JSONObject jSONObject = null;
/* 102 */     for (byte b = 0; b < arrayList1.size(); b++) {
/*     */       byte b1; String str14, str15; byte b2; String arrayOfString[], str16, str17, str18; ArrayList arrayList;
/* 104 */       if (b % 2 == 0) {
/* 105 */         str12 = "dataLight";
/*     */       } else {
/* 107 */         str12 = "dataDark";
/*     */       } 
/*     */       
/* 110 */       str2 = arrayList1.get(b);
/* 111 */       arrayList2 = Util.TokenizerString(str2, "_");
/* 112 */       str3 = arrayList2.get(0);
/* 113 */       str5 = arrayList2.get(1);
/* 114 */       str8 = arrayList2.get(2);
/* 115 */       if (arrayList2.size() > 3) {
/* 116 */         str10 = arrayList2.get(3);
/*     */       }
/* 118 */       String str13 = str10;
/* 119 */       if ((str3.equals("2") || str3.equals("3") || str3.equals("4") || str3.equals("5")) && 
/* 120 */         str10.indexOf("|@|") > -1) {
/* 121 */         String[] arrayOfString1 = str10.split("\\|@\\|");
/* 122 */         str10 = arrayOfString1[0] + "-" + arrayOfString1[1];
/*     */       } 
/*     */ 
/*     */       
/* 126 */       str11 = str10;
/* 127 */       if (str11.equals("0")) str11 = "";
/*     */       
/* 129 */       str10 = str13;
/* 130 */       i = Integer.parseInt(str3);
/* 131 */       switch (i) {
/*     */         case 1:
/* 133 */           str4 = SystemEnv.getHtmlLabelName(1867, paramInt);
/* 134 */           str9 = "";
/* 135 */           str6 = "";
/* 136 */           str7 = "";
/* 137 */           arrayList3 = Util.TokenizerString(str5, ",");
/* 138 */           for (b1 = 0; b1 < arrayList3.size(); b1++) {
/*     */             
/* 140 */             str6 = str6 + " <a href='javaScript:openhrm(" + (String)arrayList3.get(b1) + ");' onclick='pointerXY(event);'>" + this.resourceComInfo.getLastname("" + (String)arrayList3.get(b1)) + "</a>";
/* 141 */             str7 = str7 + " " + this.resourceComInfo.getLastname("" + (String)arrayList3.get(b1));
/*     */           } 
/*     */           break;
/*     */         case 2:
/* 145 */           str4 = SystemEnv.getHtmlLabelName(141, paramInt);
/* 146 */           str9 = "";
/* 147 */           str6 = "";
/* 148 */           str7 = "";
/* 149 */           arrayList3 = Util.TokenizerString(str5, ",");
/* 150 */           for (b1 = 0; b1 < arrayList3.size(); b1++) {
/*     */             
/* 152 */             str6 = str6 + " <a href='/hrm/company/HrmSubCompanyDsp.jsp?id=" + (String)arrayList3.get(b1) + "'>" + this.subCompanyComInfo.getSubCompanyname("" + (String)arrayList3.get(b1)) + "</a>";
/* 153 */             str7 = str7 + " " + this.subCompanyComInfo.getSubCompanyname("" + (String)arrayList3.get(b1));
/*     */           } 
/*     */           break;
/*     */         case 3:
/* 157 */           str4 = SystemEnv.getHtmlLabelName(124, paramInt);
/* 158 */           str9 = "";
/* 159 */           str6 = "";
/* 160 */           str7 = "";
/* 161 */           arrayList3 = Util.TokenizerString(str5, ",");
/* 162 */           for (b1 = 0; b1 < arrayList3.size(); b1++) {
/*     */             
/* 164 */             str6 = str6 + " <a href='/hrm/company/HrmDepartmentDsp.jsp?id=" + (String)arrayList3.get(b1) + "'>" + this.departmentComInfo.getDepartmentname("" + (String)arrayList3.get(b1)) + "</a>";
/* 165 */             str7 = str7 + " " + this.departmentComInfo.getDepartmentname("" + (String)arrayList3.get(b1));
/*     */           } 
/*     */           break;
/*     */         case 4:
/* 169 */           str4 = SystemEnv.getHtmlLabelName(122, paramInt);
/* 170 */           str9 = "";
/* 171 */           str6 = "";
/* 172 */           str7 = "";
/* 173 */           j = Integer.parseInt(str8);
/* 174 */           switch (j) {
/*     */             
/*     */             case 0:
/* 177 */               str9 = SystemEnv.getHtmlLabelName(18939, paramInt); break;
/*     */             case 1:
/* 179 */               str9 = SystemEnv.getHtmlLabelName(141, paramInt); break;
/*     */             case 2:
/* 181 */               str9 = SystemEnv.getHtmlLabelName(140, paramInt); break;
/*     */           } 
/* 183 */           arrayList3 = Util.TokenizerString(str5, ",");
/* 184 */           str14 = ""; str15 = "";
/* 185 */           for (b2 = 0; b2 < arrayList3.size(); b2++) {
/* 186 */             str14 = arrayList3.get(b2);
/* 187 */             str15 = this.rolesComInfo.getRolesRemark(str14);
/* 188 */             str6 = str6 + " <a href='/hrm/roles/HrmRolesEdit.jsp?id=" + str14 + "'>" + str15 + "</a>";
/* 189 */             str7 = str7 + " " + str15;
/*     */           } 
/*     */           break;
/*     */         case 5:
/* 193 */           str4 = SystemEnv.getHtmlLabelName(1340, paramInt);
/* 194 */           str9 = "";
/* 195 */           str6 = "";
/* 196 */           str7 = "";
/*     */           break;
/*     */         case 6:
/* 199 */           str4 = SystemEnv.getHtmlLabelName(6086, paramInt);
/* 200 */           str9 = "";
/* 201 */           str6 = this.jobComInfo.getJobName(str5);
/* 202 */           str7 = this.jobComInfo.getJobName(str5);
/* 203 */           str11 = "";
/*     */           
/* 205 */           arrayOfString = str10.split("\\|@\\|");
/* 206 */           str16 = arrayOfString[0];
/* 207 */           str17 = arrayOfString[1];
/* 208 */           str18 = "";
/* 209 */           arrayList = Util.TokenizerString(str17, ",");
/*     */           
/* 211 */           if (str16.equals("0")) {
/* 212 */             str18 = "/" + SystemEnv.getHtmlLabelName(19438, paramInt) + "(";
/* 213 */             String[] arrayOfString1 = Util.TokenizerString2(str17, ",");
/* 214 */             for (byte b3 = 0; b3 < arrayOfString1.length; b3++) {
/* 215 */               str18 = str18 + "<a href='/hrm/company/HrmDepartmentDsp.jsp?id=" + arrayOfString1[b3] + "' target='_blank'>" + Util.toScreen(this.departmentComInfo.getDepartmentname(arrayOfString1[b3]), paramInt) + "</a>,";
/*     */             }
/* 217 */             str18.substring(0, str18.length() - 1);
/* 218 */             str6 = str6 + str18 + ")"; break;
/* 219 */           }  if (str16.equals("1")) {
/* 220 */             str18 = "/" + SystemEnv.getHtmlLabelName(19437, paramInt) + "(";
/* 221 */             String[] arrayOfString1 = Util.TokenizerString2(str17, ",");
/* 222 */             for (byte b3 = 0; b3 < arrayOfString1.length; b3++) {
/* 223 */               str18 = str18 + "<a href='/hrm/company/HrmSubCompanyDsp.jsp?id=" + arrayOfString1[b3] + "' target='_blank'>" + Util.toScreen(this.subCompanyComInfo.getSubCompanyname(arrayOfString1[b3]), paramInt) + "</a>,";
/*     */             }
/* 225 */             str18.substring(0, str18.length() - 1);
/* 226 */             str6 = str6 + str18 + ")"; break;
/*     */           } 
/* 228 */           str18 = "/" + SystemEnv.getHtmlLabelName(140, paramInt);
/* 229 */           str6 = str6 + str18;
/*     */           break;
/*     */       } 
/*     */       
/* 233 */       if (paramBoolean) {
/*     */         try {
/* 235 */           jSONArray2 = new JSONArray();
/*     */           
/* 237 */           jSONObject = new JSONObject();
/* 238 */           jSONObject.put("name", "chkShareDetail");
/* 239 */           jSONObject.put("value", str2);
/* 240 */           jSONObject.put("type", "checkbox");
/* 241 */           jSONArray2.put(jSONObject);
/*     */           
/* 243 */           jSONObject = new JSONObject();
/* 244 */           jSONObject.put("name", "shareTypeValue");
/* 245 */           jSONObject.put("value", str3);
/* 246 */           jSONObject.put("type", "input");
/* 247 */           jSONArray2.put(jSONObject);
/*     */           
/* 249 */           jSONObject = new JSONObject();
/* 250 */           jSONObject.put("name", "shareTypeText");
/* 251 */           jSONObject.put("value", str4);
/* 252 */           jSONObject.put("type", "input");
/* 253 */           jSONArray2.put(jSONObject);
/*     */           
/* 255 */           jSONObject = new JSONObject();
/* 256 */           jSONObject.put("name", "shareTypeSpan");
/* 257 */           jSONObject.put("value", str4);
/* 258 */           jSONObject.put("type", "span");
/* 259 */           jSONArray2.put(jSONObject);
/*     */           
/* 261 */           jSONObject = new JSONObject();
/* 262 */           jSONObject.put("name", "relatedShareIds");
/* 263 */           jSONObject.put("value", str5);
/* 264 */           jSONObject.put("type", "input");
/* 265 */           jSONArray2.put(jSONObject);
/*     */           
/* 267 */           jSONObject = new JSONObject();
/* 268 */           jSONObject.put("name", "relatedShareNames");
/* 269 */           jSONObject.put("value", str6);
/* 270 */           jSONObject.put("type", "input");
/* 271 */           jSONArray2.put(jSONObject);
/*     */           
/* 273 */           jSONObject = new JSONObject();
/* 274 */           jSONObject.put("name", "relatedShareNamesOfNoLink");
/* 275 */           jSONObject.put("value", str7);
/* 276 */           jSONObject.put("type", "input");
/* 277 */           jSONArray2.put(jSONObject);
/*     */           
/* 279 */           jSONObject = new JSONObject();
/* 280 */           jSONObject.put("name", "relatedShareSpan");
/* 281 */           jSONObject.put("value", str6);
/* 282 */           jSONObject.put("type", "span");
/* 283 */           jSONArray2.put(jSONObject);
/*     */           
/* 285 */           jSONObject = new JSONObject();
/* 286 */           jSONObject.put("name", "rolelevelValue");
/* 287 */           jSONObject.put("value", str8);
/* 288 */           jSONObject.put("type", "input");
/* 289 */           jSONArray2.put(jSONObject);
/*     */           
/* 291 */           jSONObject = new JSONObject();
/* 292 */           jSONObject.put("name", "rolelevelText");
/* 293 */           jSONObject.put("value", str9);
/* 294 */           jSONObject.put("type", "input");
/* 295 */           jSONArray2.put(jSONObject);
/*     */           
/* 297 */           jSONObject = new JSONObject();
/* 298 */           jSONObject.put("name", "rolelevelSpan");
/* 299 */           jSONObject.put("value", str9);
/* 300 */           jSONObject.put("type", "span");
/* 301 */           jSONArray2.put(jSONObject);
/*     */           
/* 303 */           jSONObject = new JSONObject();
/* 304 */           jSONObject.put("name", "secLevelValue");
/* 305 */           jSONObject.put("value", str10);
/* 306 */           jSONObject.put("type", "input");
/* 307 */           jSONArray2.put(jSONObject);
/*     */           
/* 309 */           jSONObject = new JSONObject();
/* 310 */           jSONObject.put("name", "secLevelText");
/* 311 */           jSONObject.put("value", str11);
/* 312 */           jSONObject.put("type", "input");
/* 313 */           jSONArray2.put(jSONObject);
/*     */           
/* 315 */           jSONObject = new JSONObject();
/* 316 */           jSONObject.put("name", "secLevelSpan");
/* 317 */           jSONObject.put("value", str11);
/* 318 */           jSONObject.put("type", "span");
/* 319 */           jSONArray2.put(jSONObject);
/* 320 */           jSONArray2.put(jSONObject);
/* 321 */           jSONArray1.put(jSONArray2);
/*     */         }
/* 323 */         catch (Exception exception) {
/* 324 */           writeLog(exception);
/*     */         } 
/*     */       } else {
/*     */         
/* 328 */         str1 = str1 + "<TR CLASS='" + str12 + "'>\n";
/* 329 */         str1 = str1 + "\t<TD><input class='inputStyle' type='checkbox' name='chkShareDetail' value='" + str2 + "'></TD>\n";
/* 330 */         str1 = str1 + "\t<TD>" + str4 + "<input type='hidden' name='shareTypeValue' value='" + str3 + "'><input type='hidden' name='shareTypeText' value='" + str4 + "'></TD>\n";
/* 331 */         str1 = str1 + "\t<TD>" + str6 + "<input type='hidden' name='relatedShareIds' value='" + str5 + "'><input type='hidden' name='relatedShareNames' value=\"" + str6 + "\"><input type='hidden' name='relatedShareNamesOfNoLink' value=\"" + str7 + "\"></TD>\n";
/* 332 */         str1 = str1 + "\t<TD>" + str9 + "<input type='hidden' name='rolelevelValue' value='" + str8 + "'><input type='hidden' name='rolelevelText' value='" + str9 + "'></TD>\n";
/* 333 */         str1 = str1 + "\t<TD>" + str11 + "<input type='hidden' name='secLevelValue' value='" + str10 + "'><input type='hidden' name='secLevelText' value='" + str11 + "'></TD>\n";
/* 334 */         str1 = str1 + "</TR>\n";
/*     */       } 
/*     */     } 
/* 337 */     if (paramBoolean) {
/* 338 */       return jSONArray1.toString();
/*     */     }
/* 340 */     return str1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getFormShowName(String paramString, int paramInt) {
/* 350 */     if (paramString == null || paramString.equals("")) {
/* 351 */       return "";
/*     */     }
/* 353 */     ArrayList<String> arrayList1 = Util.TokenizerString(paramString, "~");
/*     */     
/* 355 */     String str1 = "";
/* 356 */     ArrayList<String> arrayList2 = null;
/* 357 */     String str2 = "";
/* 358 */     int i = 0;
/* 359 */     String str3 = "";
/* 360 */     ArrayList<String> arrayList3 = null;
/* 361 */     String str4 = "";
/* 362 */     int j = 0;
/* 363 */     String str5 = "";
/* 364 */     String str6 = "";
/* 365 */     String str7 = "";
/* 366 */     int k = 0;
/* 367 */     RecordSet recordSet = new RecordSet();
/* 368 */     recordSet.executeSql("select hrmConditionShowType from workflow_base where id = " + this.workflowid);
/* 369 */     if (recordSet.next()) {
/* 370 */       k = recordSet.getInt("hrmConditionShowType");
/*     */     }
/* 372 */     for (byte b = 0; b < arrayList1.size(); b++) {
/* 373 */       byte b1; String str9, str10; byte b2; String arrayOfString[], str11, str12, str13; ArrayList arrayList; str1 = arrayList1.get(b);
/* 374 */       arrayList2 = Util.TokenizerString(str1, "_");
/*     */ 
/*     */       
/* 377 */       if (arrayList2 == null || arrayList2.size() < 4) {
/* 378 */         return "";
/*     */       }
/* 380 */       str2 = arrayList2.get(0);
/* 381 */       str3 = arrayList2.get(1);
/* 382 */       str4 = arrayList2.get(2);
/* 383 */       str6 = arrayList2.get(3);
/*     */ 
/*     */       
/* 386 */       String str8 = "";
/* 387 */       if ((str2.equals("2") || str2.equals("3") || str2.equals("4") || str2.equals("5")) && 
/* 388 */         str6.indexOf("|@|") > -1) {
/*     */         
/* 390 */         String[] arrayOfString1 = str6.split("\\|@\\|");
/* 391 */         str6 = arrayOfString1[0] + "-" + arrayOfString1[1];
/* 392 */         str8 = str6;
/*     */       } 
/*     */ 
/*     */       
/* 396 */       i = Integer.parseInt(str2);
/* 397 */       switch (i) {
/*     */         case 1:
/* 399 */           str7 = str7 + "，" + SystemEnv.getHtmlLabelName(1867, paramInt) + "(";
/* 400 */           arrayList3 = Util.TokenizerString(str3, ",");
/* 401 */           for (b1 = 0; b1 < arrayList3.size(); b1++)
/*     */           {
/* 403 */             str7 = str7 + " <a href='javaScript:openhrm(" + (String)arrayList3.get(b1) + ");' onclick='pointerXY(event);'>" + this.resourceComInfo.getLastname("" + (String)arrayList3.get(b1)) + "</a>";
/*     */           }
/* 405 */           str7 = str7 + ")";
/*     */           break;
/*     */         case 2:
/* 408 */           str7 = str7 + "，" + SystemEnv.getHtmlLabelName(141, paramInt) + "(";
/* 409 */           arrayList3 = Util.TokenizerString(str3, ",");
/* 410 */           for (b1 = 0; b1 < arrayList3.size(); b1++)
/*     */           {
/* 412 */             str7 = str7 + " <a href='/hrm/company/HrmSubCompanyDsp.jsp?id=" + (String)arrayList3.get(b1) + "'>" + this.subCompanyComInfo.getSubCompanyname("" + (String)arrayList3.get(b1)) + "</a>";
/*     */           }
/* 414 */           str7 = str7 + ")";
/* 415 */           if (k != 2) {
/* 416 */             if (!"".equals(str8)) {
/* 417 */               str7 = str7 + SystemEnv.getHtmlLabelName(10000151, paramInt) + str8 + SystemEnv.getHtmlLabelName(18941, paramInt); break;
/*     */             } 
/* 419 */             str7 = str7 + SystemEnv.getHtmlLabelName(683, paramInt) + ">=" + str6 + SystemEnv.getHtmlLabelName(18941, paramInt);
/*     */           } 
/*     */           break;
/*     */         
/*     */         case 3:
/* 424 */           str7 = str7 + "，" + SystemEnv.getHtmlLabelName(124, paramInt) + "(";
/* 425 */           arrayList3 = Util.TokenizerString(str3, ",");
/* 426 */           for (b1 = 0; b1 < arrayList3.size(); b1++)
/*     */           {
/* 428 */             str7 = str7 + " <a href='/hrm/company/HrmDepartmentDsp.jsp?id=" + (String)arrayList3.get(b1) + "'>" + this.departmentComInfo.getDepartmentname("" + (String)arrayList3.get(b1)) + "</a>";
/*     */           }
/* 430 */           str7 = str7 + ")";
/* 431 */           if (k != 2) {
/* 432 */             if (!"".equals(str8)) {
/* 433 */               str7 = str7 + SystemEnv.getHtmlLabelName(10000151, paramInt) + str8 + SystemEnv.getHtmlLabelName(18942, paramInt); break;
/*     */             } 
/* 435 */             str7 = str7 + SystemEnv.getHtmlLabelName(683, paramInt) + ">=" + str6 + SystemEnv.getHtmlLabelName(18942, paramInt);
/*     */           } 
/*     */           break;
/*     */         
/*     */         case 4:
/* 440 */           j = Integer.parseInt(str4);
/* 441 */           switch (j) {
/*     */             
/*     */             case 0:
/* 444 */               str5 = SystemEnv.getHtmlLabelName(18939, paramInt); break;
/*     */             case 1:
/* 446 */               str5 = SystemEnv.getHtmlLabelName(141, paramInt); break;
/*     */             case 2:
/* 448 */               str5 = SystemEnv.getHtmlLabelName(140, paramInt); break;
/*     */           } 
/* 450 */           str7 = str7 + "，" + SystemEnv.getHtmlLabelName(122, paramInt) + "(";
/* 451 */           arrayList3 = Util.TokenizerString(str3, ",");
/* 452 */           str9 = ""; str10 = "";
/* 453 */           for (b2 = 0; b2 < arrayList3.size(); b2++) {
/* 454 */             str9 = arrayList3.get(b2);
/* 455 */             str10 = this.rolesComInfo.getRolesRemark(str9);
/* 456 */             str7 = str7 + " <a href='/hrm/roles/HrmRolesEdit.jsp?id=" + str9 + "'>" + str10 + "</a>";
/*     */           } 
/* 458 */           str7 = str7 + ")";
/* 459 */           if (k != 2) {
/* 460 */             if (!"".equals(str8)) {
/* 461 */               str7 = str7 + SystemEnv.getHtmlLabelName(3005, paramInt) + "=" + str5 + "  " + SystemEnv.getHtmlLabelName(10000151, paramInt) + str8 + SystemEnv.getHtmlLabelName(18945, paramInt); break;
/*     */             } 
/* 463 */             str7 = str7 + SystemEnv.getHtmlLabelName(3005, paramInt) + "=" + str5 + "  " + SystemEnv.getHtmlLabelName(683, paramInt) + ">=" + str6 + SystemEnv.getHtmlLabelName(18945, paramInt);
/*     */           } 
/*     */           break;
/*     */         
/*     */         case 5:
/* 468 */           if (k != 2) {
/* 469 */             if (!"".equals(str8)) {
/* 470 */               str7 = str7 + "，" + SystemEnv.getHtmlLabelName(10000151, paramInt) + str8 + SystemEnv.getHtmlLabelName(18943, paramInt); break;
/*     */             } 
/* 472 */             str7 = str7 + "，" + SystemEnv.getHtmlLabelName(683, paramInt) + ">=" + str6 + SystemEnv.getHtmlLabelName(18943, paramInt);
/*     */             break;
/*     */           } 
/* 475 */           str7 = str7 + "，" + SystemEnv.getHtmlLabelName(1340, paramInt);
/*     */           break;
/*     */         
/*     */         case 6:
/* 479 */           str7 = str7 + "，" + SystemEnv.getHtmlLabelName(6086, paramInt) + "(";
/* 480 */           str7 = str7 + this.jobComInfo.getJobName(str3);
/*     */ 
/*     */           
/* 483 */           arrayOfString = str6.split("\\|@\\|");
/* 484 */           str11 = arrayOfString[0];
/* 485 */           str12 = arrayOfString[1];
/* 486 */           str13 = "";
/* 487 */           arrayList = Util.TokenizerString(str12, ",");
/*     */           
/* 489 */           if (str11.equals("0")) {
/* 490 */             str13 = "/" + SystemEnv.getHtmlLabelName(19438, paramInt) + "(";
/* 491 */             String[] arrayOfString1 = Util.TokenizerString2(str12, ",");
/* 492 */             for (byte b3 = 0; b3 < arrayOfString1.length; b3++) {
/* 493 */               str13 = str13 + " <a href='/hrm/company/HrmDepartmentDsp.jsp?id=" + arrayOfString1[b3] + "' target='_blank'>" + Util.toScreen(this.departmentComInfo.getDepartmentname(arrayOfString1[b3]), paramInt) + "</a>";
/*     */             }
/* 495 */             str13.substring(0, str13.length() - 1);
/* 496 */             str7 = str7 + str13 + ")";
/* 497 */           } else if (str11.equals("1")) {
/* 498 */             str13 = "/" + SystemEnv.getHtmlLabelName(19437, paramInt) + "(";
/* 499 */             String[] arrayOfString1 = Util.TokenizerString2(str12, ",");
/* 500 */             for (byte b3 = 0; b3 < arrayOfString1.length; b3++) {
/* 501 */               str13 = str13 + " <a href='/hrm/company/HrmSubCompanyDsp.jsp?id=" + arrayOfString1[b3] + "' target='_blank'>" + Util.toScreen(this.subCompanyComInfo.getSubCompanyname(arrayOfString1[b3]), paramInt) + "</a>";
/*     */             }
/* 503 */             str13.substring(0, str13.length() - 1);
/* 504 */             str7 = str7 + str13 + ")";
/*     */           } else {
/* 506 */             str13 = "/" + SystemEnv.getHtmlLabelName(140, paramInt);
/* 507 */             str7 = str7 + str13;
/*     */           } 
/* 509 */           str7 = str7 + ")";
/*     */           break;
/*     */       } 
/*     */ 
/*     */     
/*     */     } 
/* 515 */     if (!str7.equals("")) {
/* 516 */       str7 = str7.substring(1);
/*     */     }
/*     */     
/* 519 */     return str7;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getFormShowNameOfNoLink(String paramString, int paramInt) {
/* 529 */     return getFormShowNameOfNoLink(paramString, paramInt, false);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getFormShowNameOfNoLink(String paramString, int paramInt, boolean paramBoolean) {
/* 540 */     if (paramString == null || paramString.equals("")) {
/* 541 */       return "";
/*     */     }
/* 543 */     ArrayList<String> arrayList1 = Util.TokenizerString(paramString, "~");
/*     */     
/* 545 */     String str1 = "";
/* 546 */     ArrayList<String> arrayList2 = null;
/* 547 */     String str2 = "";
/* 548 */     int i = 0;
/* 549 */     String str3 = "";
/* 550 */     ArrayList<String> arrayList3 = null;
/* 551 */     String str4 = "";
/* 552 */     int j = 0;
/* 553 */     String str5 = "";
/* 554 */     String str6 = "";
/* 555 */     String str7 = "";
/*     */     
/* 557 */     int k = 0;
/* 558 */     RecordSet recordSet = new RecordSet();
/* 559 */     recordSet.executeSql("select hrmConditionShowType from workflow_base where id = " + this.workflowid);
/* 560 */     if (recordSet.next()) {
/* 561 */       k = recordSet.getInt("hrmConditionShowType");
/*     */     }
/* 563 */     for (byte b = 0; b < arrayList1.size(); b++) {
/* 564 */       byte b1; String str9, str10; byte b2; String arrayOfString[], str11, str12, str13; ArrayList arrayList; str1 = arrayList1.get(b);
/* 565 */       arrayList2 = Util.TokenizerString(str1, "_");
/*     */ 
/*     */       
/* 568 */       if (arrayList2 == null || arrayList2.size() < 4) {
/* 569 */         return "";
/*     */       }
/* 571 */       str2 = arrayList2.get(0);
/* 572 */       str3 = arrayList2.get(1);
/* 573 */       str4 = arrayList2.get(2);
/* 574 */       str6 = arrayList2.get(3);
/*     */ 
/*     */       
/* 577 */       String str8 = "";
/* 578 */       if ((str2.equals("2") || str2.equals("3") || str2.equals("4") || str2.equals("5")) && 
/* 579 */         str6.indexOf("|@|") > -1) {
/*     */         
/* 581 */         String[] arrayOfString1 = str6.split("\\|@\\|");
/* 582 */         str6 = arrayOfString1[0] + "-" + arrayOfString1[1];
/* 583 */         str8 = str6;
/*     */       } 
/*     */ 
/*     */       
/* 587 */       i = Integer.parseInt(str2);
/* 588 */       switch (i) {
/*     */         case 1:
/* 590 */           str7 = str7 + "，" + SystemEnv.getHtmlLabelName(1867, paramInt) + "(";
/* 591 */           arrayList3 = Util.TokenizerString(str3, ",");
/* 592 */           for (b1 = 0; b1 < arrayList3.size(); b1++) {
/* 593 */             str7 = str7 + " " + this.resourceComInfo.getLastname("" + (String)arrayList3.get(b1));
/*     */           }
/* 595 */           str7 = str7 + ")";
/*     */           break;
/*     */         case 2:
/* 598 */           str7 = str7 + "，" + SystemEnv.getHtmlLabelName(141, paramInt) + "(";
/* 599 */           arrayList3 = Util.TokenizerString(str3, ",");
/* 600 */           for (b1 = 0; b1 < arrayList3.size(); b1++) {
/* 601 */             str7 = str7 + " " + this.subCompanyComInfo.getSubCompanyname("" + (String)arrayList3.get(b1));
/*     */           }
/* 603 */           str7 = str7 + ")";
/*     */           
/* 605 */           if (k != 2 && (k != 1 || !paramBoolean)) {
/* 606 */             if (!"".equals(str8)) {
/* 607 */               str7 = str7 + SystemEnv.getHtmlLabelName(683, paramInt) + "" + SystemEnv.getHtmlLabelName(24358, ThreadVarLanguage.getLang()) + "" + str8 + SystemEnv.getHtmlLabelName(18941, paramInt); break;
/*     */             } 
/* 609 */             str7 = str7 + SystemEnv.getHtmlLabelName(683, paramInt) + ">=" + str6 + SystemEnv.getHtmlLabelName(18941, paramInt);
/*     */           } 
/*     */           break;
/*     */         
/*     */         case 3:
/* 614 */           str7 = str7 + "，" + SystemEnv.getHtmlLabelName(124, paramInt) + "(";
/* 615 */           arrayList3 = Util.TokenizerString(str3, ",");
/* 616 */           for (b1 = 0; b1 < arrayList3.size(); b1++) {
/* 617 */             str7 = str7 + " " + this.departmentComInfo.getDepartmentname("" + (String)arrayList3.get(b1));
/*     */           }
/* 619 */           str7 = str7 + ")";
/*     */           
/* 621 */           if (k != 2 && (k != 1 || !paramBoolean)) {
/* 622 */             if (!"".equals(str8)) {
/* 623 */               str7 = str7 + SystemEnv.getHtmlLabelName(683, paramInt) + "" + SystemEnv.getHtmlLabelName(24358, ThreadVarLanguage.getLang()) + "" + str8 + SystemEnv.getHtmlLabelName(18942, paramInt); break;
/*     */             } 
/* 625 */             str7 = str7 + SystemEnv.getHtmlLabelName(683, paramInt) + ">=" + str6 + SystemEnv.getHtmlLabelName(18942, paramInt);
/*     */           } 
/*     */           break;
/*     */         
/*     */         case 4:
/* 630 */           j = Integer.parseInt(str4);
/* 631 */           switch (j) {
/*     */             
/*     */             case 0:
/* 634 */               str5 = SystemEnv.getHtmlLabelName(18939, paramInt); break;
/*     */             case 1:
/* 636 */               str5 = SystemEnv.getHtmlLabelName(141, paramInt); break;
/*     */             case 2:
/* 638 */               str5 = SystemEnv.getHtmlLabelName(140, paramInt); break;
/*     */           } 
/* 640 */           str7 = str7 + "，" + SystemEnv.getHtmlLabelName(122, paramInt) + "(";
/* 641 */           arrayList3 = Util.TokenizerString(str3, ",");
/* 642 */           str9 = ""; str10 = "";
/* 643 */           for (b2 = 0; b2 < arrayList3.size(); b2++) {
/* 644 */             str9 = arrayList3.get(b2);
/* 645 */             str10 = this.rolesComInfo.getRolesname(str9);
/* 646 */             str7 = str7 + " " + str10;
/*     */           } 
/* 648 */           str7 = str7 + ")";
/* 649 */           if (k != 2 && (k != 1 || !paramBoolean)) {
/* 650 */             if (!"".equals(str8)) {
/* 651 */               str7 = str7 + SystemEnv.getHtmlLabelName(3005, paramInt) + "=" + str5 + "  " + SystemEnv.getHtmlLabelName(683, paramInt) + "" + SystemEnv.getHtmlLabelName(24358, ThreadVarLanguage.getLang()) + "" + str8 + SystemEnv.getHtmlLabelName(18945, paramInt); break;
/*     */             } 
/* 653 */             str7 = str7 + SystemEnv.getHtmlLabelName(3005, paramInt) + "=" + str5 + "  " + SystemEnv.getHtmlLabelName(683, paramInt) + ">=" + str6 + SystemEnv.getHtmlLabelName(18945, paramInt);
/*     */           } 
/*     */           break;
/*     */         
/*     */         case 5:
/* 658 */           if (k != 2 && (k != 1 || !paramBoolean)) {
/* 659 */             if (!"".equals(str8)) {
/* 660 */               str7 = str7 + "，" + SystemEnv.getHtmlLabelName(10000151, paramInt) + str8 + SystemEnv.getHtmlLabelName(18943, paramInt); break;
/*     */             } 
/* 662 */             str7 = str7 + "，" + SystemEnv.getHtmlLabelName(683, paramInt) + ">=" + str6 + SystemEnv.getHtmlLabelName(18943, paramInt);
/*     */             break;
/*     */           } 
/* 665 */           str7 = str7 + "，" + SystemEnv.getHtmlLabelName(1340, paramInt);
/*     */           break;
/*     */         
/*     */         case 6:
/* 669 */           str7 = str7 + "，" + this.jobComInfo.getJobName(str3);
/*     */ 
/*     */           
/* 672 */           arrayOfString = str6.split("\\|@\\|");
/* 673 */           str11 = arrayOfString[0];
/* 674 */           str12 = arrayOfString[1];
/* 675 */           str13 = "";
/* 676 */           arrayList = Util.TokenizerString(str12, ",");
/*     */           
/* 678 */           if (str11.equals("0")) {
/* 679 */             str13 = "/" + SystemEnv.getHtmlLabelName(19438, paramInt) + "(";
/* 680 */             String[] arrayOfString1 = Util.TokenizerString2(str12, ",");
/* 681 */             for (byte b3 = 0; b3 < arrayOfString1.length; b3++) {
/* 682 */               str13 = str13 + Util.toScreen(this.departmentComInfo.getDepartmentname(arrayOfString1[b3]), paramInt);
/*     */             }
/*     */             
/* 685 */             str7 = str7 + str13 + ")"; break;
/* 686 */           }  if (str11.equals("1")) {
/* 687 */             str13 = "/" + SystemEnv.getHtmlLabelName(19437, paramInt) + "(";
/* 688 */             String[] arrayOfString1 = Util.TokenizerString2(str12, ",");
/* 689 */             for (byte b3 = 0; b3 < arrayOfString1.length; b3++) {
/* 690 */               str13 = str13 + Util.toScreen(this.subCompanyComInfo.getSubCompanyname(arrayOfString1[b3]), paramInt);
/*     */             }
/*     */             
/* 693 */             str7 = str7 + str13 + ")"; break;
/*     */           } 
/* 695 */           str13 = "/" + SystemEnv.getHtmlLabelName(140, paramInt);
/* 696 */           str7 = str7 + str13;
/*     */           break;
/*     */       } 
/*     */ 
/*     */     
/*     */     } 
/* 702 */     if (!str7.equals("")) {
/* 703 */       str7 = str7.substring(1);
/*     */     }
/*     */     
/* 706 */     return str7;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getUserIdsByResourceCondition(String paramString) {
/* 715 */     RecordSet recordSet = new RecordSet();
/*     */     
/* 717 */     if (paramString == null || paramString.equals("")) {
/* 718 */       return "";
/*     */     }
/* 720 */     ArrayList<String> arrayList1 = Util.TokenizerString(paramString, "~");
/*     */     
/* 722 */     String str1 = "";
/* 723 */     ArrayList<String> arrayList2 = null;
/* 724 */     String str2 = "";
/* 725 */     int i = 0;
/* 726 */     String str3 = "";
/* 727 */     Object object = null;
/* 728 */     String str4 = "";
/* 729 */     boolean bool = false;
/* 730 */     String str5 = "";
/* 731 */     String str6 = "";
/* 732 */     String str7 = "";
/*     */     
/* 734 */     String str8 = "";
/* 735 */     boolean bool1 = recordSet.getDBType().equals("oracle");
/*     */     
/* 737 */     for (byte b = 0; b < arrayList1.size(); b++) {
/* 738 */       str1 = arrayList1.get(b);
/* 739 */       arrayList2 = Util.TokenizerString(str1, "_");
/* 740 */       str2 = arrayList2.get(0);
/* 741 */       str3 = arrayList2.get(1);
/* 742 */       str4 = arrayList2.get(2);
/* 743 */       str6 = arrayList2.get(3);
/*     */ 
/*     */       
/* 746 */       String str = "";
/* 747 */       if ((str2.equals("2") || str2.equals("3") || str2.equals("4") || str2.equals("5") || str2.equals("6")) && 
/* 748 */         str6.indexOf("|@|") > -1) {
/*     */         
/* 750 */         String[] arrayOfString = str6.split("\\|@\\|");
/* 751 */         str6 = arrayOfString[0];
/* 752 */         str = arrayOfString[1];
/*     */       } 
/*     */ 
/*     */       
/* 756 */       i = Integer.parseInt(str2);
/* 757 */       switch (i) {
/*     */         case 1:
/* 759 */           str7 = str7 + "," + str3;
/*     */           break;
/*     */         case 2:
/* 762 */           if ((str3 != null && !str3.equals("")) || (str6 != null && 
/* 763 */             !str6.equals("") && !str.equals("") && str != null)) {
/*     */             
/* 765 */             if (!"".equals(str)) {
/* 766 */               str8 = "select id from HrmResource where subCompanyid1 in(" + str3 + ") and secLevel>=" + str6 + " and secLevel <= " + str + " and (status =0 or status = 1 or status = 2 or status = 3) and loginId is not null ";
/* 767 */               if (!bool1) {
/* 768 */                 str8 = str8 + " and loginId<>'' ";
/*     */               }
/* 770 */               recordSet.executeSql(str8);
/*     */               
/* 772 */               while (recordSet.next())
/* 773 */                 str7 = str7 + "," + recordSet.getString(1); 
/*     */               break;
/*     */             } 
/* 776 */             str8 = "select id from HrmResource where subCompanyid1 in(" + str3 + ") and secLevel>=" + str6 + " and (status =0 or status = 1 or status = 2 or status = 3) and loginId is not null ";
/* 777 */             if (!bool1) {
/* 778 */               str8 = str8 + " and loginId<>'' ";
/*     */             }
/* 780 */             recordSet.executeSql(str8);
/*     */             
/* 782 */             while (recordSet.next()) {
/* 783 */               str7 = str7 + "," + recordSet.getString(1);
/*     */             }
/*     */           } 
/*     */           break;
/*     */ 
/*     */         
/*     */         case 3:
/* 790 */           if ((str3 != null && !str3.equals("")) || (str6 != null && 
/* 791 */             !str6.equals("") && !str.equals("") && str != null)) {
/*     */             
/* 793 */             if (!"".equals(str)) {
/* 794 */               str8 = "select id from HrmResource where departmentid in(" + str3 + ") and secLevel>=" + str6 + " and secLevel <= " + str + " and (status =0 or status = 1 or status = 2 or status = 3)  and loginId is not null ";
/* 795 */               if (!bool1) {
/* 796 */                 str8 = str8 + " and loginId<>'' ";
/*     */               }
/* 798 */               recordSet.executeSql(str8);
/*     */               
/* 800 */               while (recordSet.next())
/* 801 */                 str7 = str7 + "," + recordSet.getString(1); 
/*     */               break;
/*     */             } 
/* 804 */             str8 = "select id from HrmResource where departmentid in(" + str3 + ") and secLevel>=" + str6 + " and (status =0 or status = 1 or status = 2 or status = 3)  and loginId is not null ";
/* 805 */             if (!bool1) {
/* 806 */               str8 = str8 + " and loginId<>'' ";
/*     */             }
/* 808 */             recordSet.executeSql(str8);
/*     */             
/* 810 */             while (recordSet.next()) {
/* 811 */               str7 = str7 + "," + recordSet.getString(1);
/*     */             }
/*     */           } 
/*     */           break;
/*     */ 
/*     */         
/*     */         case 4:
/* 818 */           if ((str3 != null && !str3.equals("")) || (str4 != null && 
/* 819 */             !str4.equals("")) || (str6 != null && 
/* 820 */             !str6.equals("") && !str.equals("") && str != null)) {
/*     */ 
/*     */             
/* 823 */             HrmCommonServiceImpl hrmCommonServiceImpl = new HrmCommonServiceImpl();
/* 824 */             String str9 = hrmCommonServiceImpl.getHrmRoleMemeberSqlByRoleIds(str3);
/* 825 */             if (!"".equals(str)) {
/* 826 */               str8 = "select b.id from (" + str9 + ") a ,HrmResource B where a.resourceid=b.id and a.roleLevel>=" + str4 + " and b.secLevel>=" + str6 + " and b.secLevel <= " + str + " and (b.status =0 or b.status = 1 or b.status = 2 or b.status = 3) and b.loginId is not null ";
/* 827 */               if (!bool1) {
/* 828 */                 str8 = str8 + " and b.loginId<>'' ";
/*     */               }
/* 830 */               recordSet.executeSql(str8);
/*     */               
/* 832 */               while (recordSet.next())
/* 833 */                 str7 = str7 + "," + recordSet.getString(1); 
/*     */               break;
/*     */             } 
/* 836 */             str8 = "select b.id from (" + str9 + ") a ,HrmResource B where a.resourceid=b.id and a.roleLevel>=" + str4 + " and b.secLevel>=" + str6 + " and (b.status =0 or b.status = 1 or b.status = 2 or b.status = 3) and b.loginId is not null ";
/* 837 */             if (!bool1) {
/* 838 */               str8 = str8 + " and b.loginId<>'' ";
/*     */             }
/* 840 */             recordSet.executeSql(str8);
/*     */             
/* 842 */             while (recordSet.next()) {
/* 843 */               str7 = str7 + "," + recordSet.getString(1);
/*     */             }
/*     */           } 
/*     */           break;
/*     */         
/*     */         case 5:
/* 849 */           if ((str3 != null && !str3.equals("")) || (str6 != null && 
/* 850 */             !str6.equals("") && !str.equals("") && str != null)) {
/*     */ 
/*     */             
/* 853 */             if (!"".equals(str)) {
/* 854 */               str8 = "select id from HrmResource where secLevel>=" + str6 + " and secLevel <= " + str + " and (status =0 or status = 1 or status = 2 or status = 3)  and loginId is not null ";
/* 855 */               if (!bool1) {
/* 856 */                 str8 = str8 + " and loginId<>'' ";
/*     */               }
/* 858 */               recordSet.executeSql(str8);
/*     */               
/* 860 */               while (recordSet.next())
/* 861 */                 str7 = str7 + "," + recordSet.getString(1); 
/*     */               break;
/*     */             } 
/* 864 */             str8 = "select id from HrmResource where secLevel>=" + str6 + "  and (status =0 or status = 1 or status = 2 or status = 3)  and loginId is not null ";
/* 865 */             if (!bool1) {
/* 866 */               str8 = str8 + " and loginId<>'' ";
/*     */             }
/* 868 */             recordSet.executeSql(str8);
/*     */             
/* 870 */             while (recordSet.next()) {
/* 871 */               str7 = str7 + "," + recordSet.getString(1);
/*     */             }
/*     */           } 
/*     */           break;
/*     */         
/*     */         case 6:
/* 877 */           if ((str3 != null && !str3.equals("")) || (str6 != null && 
/* 878 */             !str6.equals(""))) {
/* 879 */             if ("0".equals(str6)) {
/* 880 */               str8 = " select b.id from HrmJobTitles a,HrmResource b where a.id=b.jobtitle and a.id = " + str3 + " and b.departmentid in (" + str + ") and (status =0 or status = 1 or status = 2 or status = 3) and loginId is not null ";
/* 881 */               if (!bool1) {
/* 882 */                 str8 = str8 + " and b.loginId<>'' ";
/*     */               }
/* 884 */               recordSet.executeSql(str8);
/*     */               
/* 886 */               while (recordSet.next())
/* 887 */                 str7 = str7 + "," + recordSet.getString(1);  break;
/*     */             } 
/* 889 */             if ("1".equals(str6)) {
/* 890 */               str8 = " select b.id from HrmJobTitles a,HrmResource b where a.id=b.jobtitle and a.id = " + str3 + " and b.subcompanyid1 in (" + str + ") and (status =0 or status = 1 or status = 2 or status = 3) and loginId is not null ";
/* 891 */               if (!bool1) {
/* 892 */                 str8 = str8 + " and b.loginId<>'' ";
/*     */               }
/* 894 */               recordSet.executeSql(str8);
/*     */               
/* 896 */               while (recordSet.next())
/* 897 */                 str7 = str7 + "," + recordSet.getString(1); 
/*     */               break;
/*     */             } 
/* 900 */             str8 = " select b.id from HrmJobTitles a,HrmResource b where a.id=b.jobtitle and a.id = " + str3 + " and (status =0 or status = 1 or status = 2 or status = 3) and loginId is not null ";
/* 901 */             if (!bool1) {
/* 902 */               str8 = str8 + " and b.loginId<>'' ";
/*     */             }
/* 904 */             recordSet.executeSql(str8);
/*     */             
/* 906 */             while (recordSet.next()) {
/* 907 */               str7 = str7 + "," + recordSet.getString(1);
/*     */             }
/*     */           } 
/*     */           break;
/*     */       } 
/*     */ 
/*     */ 
/*     */     
/*     */     } 
/* 916 */     if (!str7.equals("")) {
/* 917 */       str7 = str7.substring(1);
/* 918 */       str7 = removeRepeatedValue(str7);
/*     */     } 
/*     */     
/* 921 */     return str7;
/*     */   }
/*     */   
/*     */   private String removeRepeatedValue(String paramString) {
/* 925 */     String str1 = "";
/*     */     
/* 927 */     if (paramString == null || paramString.equals("")) {
/* 928 */       return str1;
/*     */     }
/*     */     
/* 931 */     ArrayList<String> arrayList = Util.TokenizerString(paramString, ",");
/* 932 */     String str2 = "";
/* 933 */     for (byte b = 0; b < arrayList.size(); b++) {
/* 934 */       str2 = arrayList.get(b);
/* 935 */       if (!str1.endsWith("," + str2) && str1.indexOf("," + str2 + ",") == -1) {
/* 936 */         str1 = str1 + "," + str2;
/*     */       }
/*     */     } 
/*     */     
/* 940 */     if (!str1.equals("")) {
/* 941 */       str1 = str1.substring(1);
/*     */     }
/*     */     
/* 944 */     return str1;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/ResourceConditionManager.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */