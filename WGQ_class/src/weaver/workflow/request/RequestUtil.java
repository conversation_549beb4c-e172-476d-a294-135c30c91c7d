/*     */ package weaver.workflow.request;
/*     */ 
/*     */ import java.io.UnsupportedEncodingException;
/*     */ import java.text.SimpleDateFormat;
/*     */ import java.util.Map;
/*     */ import java.util.TimeZone;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public final class RequestUtil
/*     */ {
/*  41 */   private static SimpleDateFormat format = new SimpleDateFormat(" EEEE, dd-MMM-yy kk:mm:ss zz");
/*     */ 
/*     */   
/*     */   static {
/*  45 */     format.setTimeZone(TimeZone.getTimeZone("GMT"));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String filter(String paramString) {
/*  58 */     if (paramString == null) {
/*  59 */       return null;
/*     */     }
/*  61 */     char[] arrayOfChar = new char[paramString.length()];
/*  62 */     paramString.getChars(0, paramString.length(), arrayOfChar, 0);
/*  63 */     StringBuffer stringBuffer = new StringBuffer(arrayOfChar.length + 50);
/*  64 */     for (byte b = 0; b < arrayOfChar.length; b++) {
/*  65 */       switch (arrayOfChar[b]) {
/*     */         case '<':
/*  67 */           stringBuffer.append("&lt;");
/*     */           break;
/*     */         case '>':
/*  70 */           stringBuffer.append("&gt;");
/*     */           break;
/*     */         case '&':
/*  73 */           stringBuffer.append("&amp;");
/*     */           break;
/*     */         case '"':
/*  76 */           stringBuffer.append("&quot;");
/*     */           break;
/*     */         default:
/*  79 */           stringBuffer.append(arrayOfChar[b]); break;
/*     */       } 
/*     */     } 
/*  82 */     return stringBuffer.toString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String normalize(String paramString) {
/*  99 */     return normalize(paramString, true);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String normalize(String paramString, boolean paramBoolean) {
/* 117 */     if (paramString == null) {
/* 118 */       return null;
/*     */     }
/*     */ 
/*     */     
/* 122 */     String str = paramString;
/*     */     
/* 124 */     if (paramBoolean && str.indexOf('\\') >= 0) {
/* 125 */       str = str.replace('\\', '/');
/*     */     }
/*     */     
/* 128 */     if (!str.startsWith("/")) {
/* 129 */       str = "/" + str;
/*     */     }
/*     */     
/* 132 */     byte b1 = 0;
/* 133 */     while (b1 < 100) {
/* 134 */       b1++;
/* 135 */       int i = str.indexOf("//");
/* 136 */       if (i < 0) {
/*     */         break;
/*     */       }
/* 139 */       str = str.substring(0, i) + str.substring(i + 1);
/*     */     } 
/*     */ 
/*     */     
/* 143 */     byte b2 = 0;
/* 144 */     while (b2 < 100) {
/* 145 */       b2++;
/* 146 */       int i = str.indexOf("/./");
/* 147 */       if (i < 0) {
/*     */         break;
/*     */       }
/* 150 */       str = str.substring(0, i) + str.substring(i + 2);
/*     */     } 
/*     */ 
/*     */     
/* 154 */     byte b3 = 0;
/* 155 */     while (b3 < 100) {
/* 156 */       b3++;
/* 157 */       int i = str.indexOf("/../");
/* 158 */       if (i < 0) {
/*     */         break;
/*     */       }
/* 161 */       if (i == 0) {
/* 162 */         return null;
/*     */       }
/* 164 */       int j = str.lastIndexOf('/', i - 1);
/* 165 */       str = str.substring(0, j) + str.substring(i + 3);
/*     */     } 
/*     */     
/* 168 */     if (str.equals("/.")) {
/* 169 */       return "/";
/*     */     }
/*     */     
/* 172 */     if (str.equals("/..")) {
/* 173 */       return null;
/*     */     }
/*     */ 
/*     */     
/* 177 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static void parseParameters(Map paramMap, String paramString1, String paramString2) throws UnsupportedEncodingException {
/* 200 */     if (paramString1 != null && paramString1.length() > 0) {
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 205 */       byte[] arrayOfByte = null;
/*     */       try {
/* 207 */         if (paramString2 == null) {
/* 208 */           arrayOfByte = paramString1.getBytes();
/*     */         } else {
/* 210 */           arrayOfByte = paramString1.getBytes(paramString2);
/*     */         } 
/* 212 */       } catch (UnsupportedEncodingException unsupportedEncodingException) {
/* 213 */         unsupportedEncodingException.printStackTrace();
/*     */       } 
/* 215 */       parseParameters(paramMap, arrayOfByte, paramString2);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String URLDecode(String paramString) {
/* 233 */     return URLDecode(paramString, (String)null);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String URLDecode(String paramString1, String paramString2) {
/* 247 */     return URLDecode(paramString1, paramString2, false);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String URLDecode(String paramString1, String paramString2, boolean paramBoolean) {
/* 260 */     if (paramString1 == null) {
/* 261 */       return null;
/*     */     }
/*     */ 
/*     */ 
/*     */     
/* 266 */     byte[] arrayOfByte = null;
/*     */     try {
/* 268 */       if (paramString2 == null) {
/* 269 */         arrayOfByte = paramString1.getBytes();
/*     */       } else {
/* 271 */         arrayOfByte = paramString1.getBytes(paramString2);
/*     */       } 
/* 273 */     } catch (UnsupportedEncodingException unsupportedEncodingException) {}
/*     */     
/* 275 */     return URLDecode(arrayOfByte, paramString2, paramBoolean);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String URLDecode(byte[] paramArrayOfbyte) {
/* 289 */     return URLDecode(paramArrayOfbyte, (String)null);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String URLDecode(byte[] paramArrayOfbyte, String paramString) {
/* 303 */     return URLDecode(paramArrayOfbyte, paramString, false);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String URLDecode(byte[] paramArrayOfbyte, String paramString, boolean paramBoolean) {
/* 317 */     if (paramArrayOfbyte == null) {
/* 318 */       return null;
/*     */     }
/* 320 */     int i = paramArrayOfbyte.length;
/* 321 */     byte b1 = 0;
/* 322 */     byte b2 = 0;
/* 323 */     while (b1 < i) {
/* 324 */       byte b = paramArrayOfbyte[b1++];
/* 325 */       if (b == 43 && paramBoolean) {
/* 326 */         b = 32;
/* 327 */       } else if (b == 37) {
/*     */         
/* 329 */         b = (byte)((convertHexDigit(paramArrayOfbyte[b1++]) << 4) + convertHexDigit(paramArrayOfbyte[b1++]));
/*     */       } 
/* 331 */       paramArrayOfbyte[b2++] = b;
/*     */     } 
/* 333 */     if (paramString != null) {
/*     */       try {
/* 335 */         return new String(paramArrayOfbyte, 0, b2, paramString);
/* 336 */       } catch (Exception exception) {
/* 337 */         exception.printStackTrace();
/*     */       } 
/*     */     }
/* 340 */     return new String(paramArrayOfbyte, 0, b2);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static byte convertHexDigit(byte paramByte) {
/* 351 */     if (paramByte >= 48 && paramByte <= 57) return (byte)(paramByte - 48); 
/* 352 */     if (paramByte >= 97 && paramByte <= 102) return (byte)(paramByte - 97 + 10); 
/* 353 */     if (paramByte >= 65 && paramByte <= 70) return (byte)(paramByte - 65 + 10); 
/* 354 */     return 0;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static void putMapEntry(Map<String, String[]> paramMap, String paramString1, String paramString2) {
/* 367 */     String[] arrayOfString1 = null;
/* 368 */     String[] arrayOfString2 = (String[])paramMap.get(paramString1);
/* 369 */     if (arrayOfString2 == null) {
/* 370 */       arrayOfString1 = new String[1];
/* 371 */       arrayOfString1[0] = paramString2;
/*     */     } else {
/* 373 */       arrayOfString1 = new String[arrayOfString2.length + 1];
/* 374 */       System.arraycopy(arrayOfString2, 0, arrayOfString1, 0, arrayOfString2.length);
/* 375 */       arrayOfString1[arrayOfString2.length] = paramString2;
/*     */     } 
/* 377 */     paramMap.put(paramString1, arrayOfString1);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static void parseParameters(Map paramMap, byte[] paramArrayOfbyte, String paramString) throws UnsupportedEncodingException {
/* 403 */     if (paramArrayOfbyte != null && paramArrayOfbyte.length > 0) {
/* 404 */       byte b1 = 0;
/* 405 */       byte b2 = 0;
/* 406 */       String str1 = null;
/* 407 */       String str2 = null;
/* 408 */       while (b1 < paramArrayOfbyte.length) {
/* 409 */         byte b = paramArrayOfbyte[b1++];
/* 410 */         switch ((char)b) {
/*     */           case '&':
/* 412 */             str2 = new String(paramArrayOfbyte, 0, b2, paramString);
/* 413 */             if (str1 != null) {
/* 414 */               putMapEntry(paramMap, str1, str2);
/* 415 */               str1 = null;
/*     */             } 
/* 417 */             b2 = 0;
/*     */             continue;
/*     */           case '=':
/* 420 */             if (str1 == null) {
/* 421 */               str1 = new String(paramArrayOfbyte, 0, b2, paramString);
/* 422 */               b2 = 0; continue;
/*     */             } 
/* 424 */             paramArrayOfbyte[b2++] = b;
/*     */             continue;
/*     */           
/*     */           case '+':
/* 428 */             paramArrayOfbyte[b2++] = 32;
/*     */             continue;
/*     */           case '%':
/* 431 */             paramArrayOfbyte[b2++] = 
/* 432 */               (byte)((convertHexDigit(paramArrayOfbyte[b1++]) << 4) + convertHexDigit(paramArrayOfbyte[b1++]));
/*     */             continue;
/*     */         } 
/* 435 */         paramArrayOfbyte[b2++] = b;
/*     */       } 
/*     */ 
/*     */       
/* 439 */       if (str1 != null) {
/* 440 */         str2 = new String(paramArrayOfbyte, 0, b2, paramString);
/* 441 */         putMapEntry(paramMap, str1, str2);
/*     */       } 
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/RequestUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */