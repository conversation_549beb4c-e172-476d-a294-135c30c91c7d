/*     */ package weaver.workflow.request;
/*     */ 
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.conn.RecordSetTrans;
/*     */ import weaver.general.Util;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class WFCoadjutantManager
/*     */ {
/*     */   private String signtype;
/*     */   private String issubmitdesc;
/*     */   private String ispending;
/*     */   private String isforward;
/*     */   private String ismodify;
/*     */   private boolean isMainSubmitted = false;
/*     */   private boolean isCoadjutant = false;
/*  21 */   private int groupdetailid = 0;
/*  22 */   private int signorder = 1;
/*     */   public WFCoadjutantManager() {
/*  24 */     init();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean isMainSubmitted() {
/*  33 */     return this.isMainSubmitted;
/*     */   }
/*     */   
/*     */   public void setMainSubmitted(boolean paramBoolean) {
/*  37 */     this.isMainSubmitted = paramBoolean;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getSignorder() {
/*  45 */     return this.signorder;
/*     */   }
/*     */   
/*     */   public void setSignorder(int paramInt) {
/*  49 */     this.signorder = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean isCoadjutant() {
/*  57 */     return this.isCoadjutant;
/*     */   }
/*     */   
/*     */   public void setisCoadjutant(boolean paramBoolean) {
/*  61 */     this.isCoadjutant = paramBoolean;
/*     */   }
/*     */   
/*     */   public void init() {
/*  65 */     this.signtype = "2";
/*  66 */     this.issubmitdesc = "0";
/*  67 */     this.ispending = "0";
/*  68 */     this.isforward = "0";
/*  69 */     this.ismodify = "0";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getSigntype() {
/*  77 */     return Util.getIntValue(this.signtype, 2) + "";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setSigntype(String paramString) {
/*  85 */     this.signtype = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getIssubmitdesc() {
/*  93 */     return Util.getIntValue(this.issubmitdesc, 0) + "";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setIssubmitdesc(String paramString) {
/* 101 */     this.issubmitdesc = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getIspending() {
/* 109 */     return Util.getIntValue(this.ispending, 0) + "";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setIspending(String paramString) {
/* 117 */     this.ispending = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getIsforward() {
/* 125 */     return Util.getIntValue(this.isforward, 0) + "";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setIsforward(String paramString) {
/* 133 */     this.isforward = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getIsmodify() {
/* 141 */     return Util.getIntValue(this.ismodify, 0) + "";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setIsmodify(String paramString) {
/* 149 */     this.ismodify = paramString;
/*     */   }
/*     */ 
/*     */   
/*     */   public int getGroupdetailid() {
/* 154 */     return this.groupdetailid;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void getCoadjutantRights(int paramInt) {
/* 163 */     init();
/* 164 */     this.groupdetailid = paramInt;
/* 165 */     RecordSet recordSet = new RecordSet();
/* 166 */     String str = "select signtype,issubmitdesc,ispending,isforward,ismodify,signorder from workflow_groupdetail where IsCoadjutant='1' and id=" + paramInt;
/* 167 */     recordSet.executeSql(str);
/* 168 */     if (recordSet.next()) {
/* 169 */       this.isCoadjutant = true;
/* 170 */       this.signtype = Util.null2String(recordSet.getString("signtype"));
/* 171 */       this.issubmitdesc = Util.null2String(recordSet.getString("issubmitdesc"));
/* 172 */       this.ispending = Util.null2String(recordSet.getString("ispending"));
/* 173 */       this.isforward = Util.null2String(recordSet.getString("isforward"));
/* 174 */       this.ismodify = Util.null2String(recordSet.getString("ismodify"));
/* 175 */       this.signorder = Util.getIntValue(recordSet.getString("signorder"));
/* 176 */       if ("2".equals(this.signtype) && !"1".equals(this.issubmitdesc)) this.ispending = "1";
/*     */     
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean getCoadjutantCanNextNode(int paramInt1, int paramInt2, int paramInt3, int paramInt4) {
/* 185 */     boolean bool = false;
/* 186 */     RecordSet recordSet = new RecordSet();
/*     */     
/* 188 */     String str = "select t1.id from workflow_currentoperator t1 where t1.requestid=" + paramInt1 + " and t1.agenttype=1 and t1.isremark='2' and t1.preisremark='7' and exists(select 1 from workflow_currentoperator where userid=t1.agentorbyagentid and agenttype=2 and id=" + paramInt3 + ") order by t1.id desc";
/* 189 */     recordSet.executeSql(str);
/* 190 */     if (recordSet.next()) {
/* 191 */       paramInt3 = recordSet.getInt("id");
/*     */     }
/* 193 */     if (paramInt2 == 0 || paramInt2 == 1)
/* 194 */     { if (paramInt4 == 7) {
/* 195 */         str = "select 1 from workflow_currentoperator where requestid=" + paramInt1 + " and isremark not in('2','4') and exists(select 1 from workflow_coadjutant where workflow_currentoperator.id=organizedid" + " and requestid=" + paramInt1 + " and coadjutantid=" + paramInt3 + ")";
/*     */       } else {
/*     */         
/* 198 */         str = "select 1 from workflow_currentoperator where requestid=" + paramInt1 + " and isremark not in('2','4') and exists(select 1 from workflow_coadjutant where workflow_currentoperator.id=coadjutantid" + " and requestid=" + paramInt1 + " and organizedid=" + paramInt3 + ")";
/*     */       } 
/*     */       
/* 201 */       recordSet.executeSql(str);
/* 202 */       if (recordSet.getCounts() < 1) bool = true;
/*     */        }
/* 204 */     else if (paramInt4 != 7) { bool = true; }
/*     */     
/* 206 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean getCoadjutantCanSubmit(int paramInt1, int paramInt2, String paramString1, String paramString2) {
/* 217 */     boolean bool = false;
/* 218 */     if ("7".equals(paramString1)) {
/* 219 */       RecordSet recordSet = new RecordSet();
/* 220 */       String str1 = "0";
/* 221 */       int i = 0;
/* 222 */       String str2 = "";
/*     */       
/* 224 */       str2 = "select * from workflow_currentoperator where isremark <>'7' and preisremark='0' and (isreject is null or isreject=0) and requestid=" + paramInt1 + " and groupdetailid ='" + this.groupdetailid + "'";
/* 225 */       recordSet.executeSql(str2);
/* 226 */       if (recordSet.next()) i = recordSet.getCounts();
/*     */       
/* 228 */       str2 = "select signorder from workflow_groupdetail where id=" + this.groupdetailid;
/* 229 */       recordSet.executeSql(str2);
/* 230 */       if (recordSet.next()) {
/* 231 */         str1 = recordSet.getString("signorder");
/*     */       }
/*     */ 
/*     */       
/* 235 */       if ("0".equals(str1)) {
/* 236 */         if ("0".equals(paramString2)) {
/* 237 */           str2 = "select count(*) from workflow_currentoperator where isremark='2' and preisremark='0' and (isreject is null or isreject=0) and requestid=" + paramInt1 + " and groupdetailid ='" + this.groupdetailid + "'";
/* 238 */           recordSet.executeSql(str2);
/* 239 */           if (recordSet.next() && recordSet.getInt(1) < 1) bool = true; 
/* 240 */         } else if ("1".equals(paramString2)) {
/* 241 */           bool = true;
/*     */         } 
/* 243 */       } else if ("1".equals(str1) || "2".equals(str1)) {
/* 244 */         if ("0".equals(paramString2)) {
/* 245 */           str2 = "select count(*) from workflow_currentoperator where isremark = '2' and preisremark='0' and (isreject is null or isreject=0)  and requestid=" + paramInt1 + " and groupdetailid=" + this.groupdetailid;
/* 246 */           recordSet.executeSql(str2);
/* 247 */           if (recordSet.next() && recordSet.getInt(1) < i) bool = true; 
/* 248 */         } else if ("1".equals(paramString2)) {
/* 249 */           bool = true;
/*     */         } 
/*     */       } 
/*     */       
/* 253 */       if ("2".equals(paramString2)) {
/* 254 */         if (this.issubmitdesc.equals("1")) {
/* 255 */           str2 = "select * from workflow_currentoperator where isremark='0' and  requestid='" + paramInt1 + "' and groupdetailid='" + this.groupdetailid + "'";
/* 256 */           recordSet.executeSql(str2);
/* 257 */           if (recordSet.next()) {
/* 258 */             bool = true;
/*     */           } else {
/* 260 */             bool = false;
/*     */           } 
/*     */         } else {
/* 263 */           bool = true;
/*     */         } 
/*     */       }
/*     */     } 
/* 267 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean getCoadjutantIsPengding(int paramInt1, int paramInt2, String paramString1, String paramString2) {
/* 278 */     boolean bool = false;
/* 279 */     if ("7".equals(paramString1))
/* 280 */       if ("1".equals(paramString2)) {
/* 281 */         bool = true;
/*     */       } else {
/* 283 */         RecordSet recordSet = new RecordSet();
/*     */         
/* 285 */         String str = "select t1.id from workflow_currentoperator t1 where t1.agenttype=1 and t1.isremark='2' and t1.preisremark='7' and exists(select 1 from workflow_currentoperator where userid=t1.agentorbyagentid and agenttype=2 and id=" + paramInt2 + ") order by t1.id desc";
/* 286 */         recordSet.executeSql(str);
/* 287 */         if (recordSet.next()) {
/* 288 */           paramInt2 = recordSet.getInt("id");
/*     */         }
/* 290 */         str = "select 1 from workflow_coadjutant where requestid=" + paramInt1 + " and coadjutantid=" + paramInt2 + " and exists(select 1 from workflow_currentoperator b where b.id=workflow_coadjutant.organizedid and isremark='0')";
/* 291 */         recordSet.executeSql(str);
/* 292 */         if (recordSet.next()) bool = true;
/*     */       
/*     */       }  
/* 295 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getCoadjutant(int paramInt) {
/* 305 */     int i = 0;
/* 306 */     RecordSet recordSet = new RecordSet();
/* 307 */     String str = "select coadjutant from HrmDepartment where exists(select 1 from hrmresource where HrmDepartment.id=departmentid and id=" + paramInt + ")";
/* 308 */     recordSet.executeSql(str);
/* 309 */     if (recordSet.next()) {
/* 310 */       i = recordSet.getInt("coadjutant");
/*     */     }
/* 312 */     return i;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean SaveCoadjutantRights(int paramInt1, int paramInt2, int paramInt3, RecordSetTrans paramRecordSetTrans) throws Exception {
/* 323 */     boolean bool = false;
/* 324 */     String str = "";
/* 325 */     if (paramInt3 > 0 && paramInt2 > 0) {
/* 326 */       str = "select requestid from workflow_coadjutant where requestid=" + paramInt1 + " and organizedid=" + paramInt2 + " and coadjutantid=" + paramInt3;
/* 327 */       paramRecordSetTrans.executeSql(str);
/* 328 */       if (paramRecordSetTrans.getCounts() < 1) {
/* 329 */         str = "insert into workflow_coadjutant(requestid,organizedid,coadjutantid,issubmitdesc,ispending,isforward,ismodify) values(" + paramInt1 + "," + paramInt2 + "," + paramInt3 + ",'" + getIssubmitdesc() + "','" + getIspending() + "','" + getIsforward() + "','" + getIsmodify() + "')";
/*     */       } else {
/*     */         
/* 332 */         str = "update workflow_coadjutant set issubmitdesc='" + getIssubmitdesc() + "',ispending='" + getIspending() + "',isforward='" + getIsforward() + "',ismodify='" + getIsmodify() + "' where requestid=" + paramInt1 + " and organizedid=" + paramInt2 + " and coadjutantid=" + paramInt3;
/*     */       } 
/*     */       
/* 335 */       bool = paramRecordSetTrans.executeSql(str);
/*     */     } 
/* 337 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean SaveCoadjutantRightsByOverTime(int paramInt1, int paramInt2, int paramInt3, RecordSet paramRecordSet) {
/* 348 */     boolean bool = false;
/* 349 */     String str = "";
/* 350 */     if (paramInt3 > 0 && paramInt2 > 0) {
/* 351 */       str = "select requestid from workflow_coadjutant where requestid=" + paramInt1 + " and organizedid=" + paramInt2 + " and coadjutantid=" + paramInt3;
/* 352 */       paramRecordSet.executeSql(str);
/* 353 */       if (paramRecordSet.getCounts() < 1) {
/* 354 */         str = "insert into workflow_coadjutant(requestid,organizedid,coadjutantid,issubmitdesc,ispending,isforward,ismodify) values(" + paramInt1 + "," + paramInt2 + "," + paramInt3 + ",'" + getIssubmitdesc() + "','" + getIspending() + "','" + getIsforward() + "','" + getIsmodify() + "')";
/*     */       } else {
/*     */         
/* 357 */         str = "update workflow_coadjutant set issubmitdesc='" + getIssubmitdesc() + "',ispending='" + getIspending() + "',isforward='" + getIsforward() + "',ismodify='" + getIsmodify() + "' where requestid=" + paramInt1 + " and organizedid=" + paramInt2 + " and coadjutantid=" + paramInt3;
/*     */       } 
/*     */       
/* 360 */       bool = paramRecordSet.executeSql(str);
/*     */     } 
/* 362 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getTotalnumSubmitGroups(int paramInt1, int paramInt2, int paramInt3, int paramInt4, RecordSetTrans paramRecordSetTrans) throws Exception {
/* 375 */     RecordSet recordSet = new RecordSet();
/* 376 */     int i = 0;
/* 377 */     int j = getCoadjutantUser(this.groupdetailid, paramInt2, paramInt3, paramRecordSetTrans);
/* 378 */     if (paramInt1 == 0) {
/* 379 */       if (this.signorder == 0 && ("0".equals(this.signtype) || "2".equals(this.signtype))) {
/* 380 */         i = 1;
/* 381 */       } else if ((this.signorder == 1 || this.signorder == 2) && ("0".equals(this.signtype) || "2".equals(this.signtype))) {
/* 382 */         paramRecordSetTrans.executeSql("select * from workflow_currentoperator where isremark='0' and requestid='" + paramInt3 + "' and nodeid='" + paramInt2 + "' and groupdetailid='" + this.groupdetailid + "'");
/* 383 */         i = paramRecordSetTrans.getCounts();
/* 384 */       } else if (this.signorder == 0 && "1".equals(this.signtype)) {
/* 385 */         paramRecordSetTrans.executeSql("select * from workflow_currentoperator where isremark='7' and requestid='" + paramInt3 + "' and nodeid='" + paramInt2 + "' and groupdetailid='" + this.groupdetailid + "'");
/* 386 */         if (paramRecordSetTrans.next())
/* 387 */         { i = paramRecordSetTrans.getCounts() + 1; }
/*     */         else
/* 389 */         { i = 1; } 
/* 390 */       } else if ((this.signorder == 1 || this.signorder == 2) && "1".equals(this.signtype)) {
/* 391 */         paramRecordSetTrans.executeSql("select * from workflow_currentoperator where (isremark='7' or isremark='0') and requestid='" + paramInt3 + "' and nodeid='" + paramInt2 + "' and groupdetailid='" + this.groupdetailid + "'");
/* 392 */         i = paramRecordSetTrans.getCounts();
/*     */         
/* 394 */         paramRecordSetTrans.executeSql("select * from workflow_currentoperator where isremark='0' and requestid='" + paramInt3 + "' and nodeid='" + paramInt2 + "' and groupdetailid='" + this.groupdetailid + "'");
/* 395 */         if (j == paramInt4 && paramRecordSetTrans.getCounts() == 1) {
/* 396 */           paramRecordSetTrans.executeSql("select * from workflow_currentoperator where isremark='7'  and requestid='" + paramInt3 + "' and nodeid='" + paramInt2 + "' and groupdetailid='" + this.groupdetailid + "'");
/* 397 */           i -= paramRecordSetTrans.getCounts();
/*     */         }
/*     */       
/*     */       } 
/* 401 */     } else if (this.signorder == 0 && "0".equals(this.signtype)) {
/* 402 */       i = 1;
/* 403 */     } else if ((this.signorder == 1 || this.signorder == 2) && "0".equals(this.signtype)) {
/* 404 */       i = 1;
/* 405 */     } else if (this.signorder == 0 && "1".equals(this.signtype)) {
/* 406 */       paramRecordSetTrans.executeSql("select * from workflow_currentoperator where isremark = '2' and preisremark='0' and (isreject is null or isreject=0)  and requestid='" + paramInt3 + "' and nodeid='" + paramInt2 + "' and groupdetailid='" + this.groupdetailid + "'");
/* 407 */       if (paramRecordSetTrans.next()) {
/* 408 */         paramRecordSetTrans.executeSql("select id from workflow_currentoperator where isremark = '7' and requestid='" + paramInt3 + "' and nodeid='" + paramInt2 + "' and groupdetailid='" + this.groupdetailid + "'");
/* 409 */         i = paramRecordSetTrans.getCounts();
/*     */       } else {
/* 411 */         paramRecordSetTrans.executeSql("select * from workflow_currentoperator where (isremark='7' or isremark='0') and requestid='" + paramInt3 + "' and nodeid='" + paramInt2 + "' and groupdetailid='" + this.groupdetailid + "'");
/* 412 */         i = paramRecordSetTrans.getCounts();
/*     */       } 
/* 414 */     } else if ((this.signorder == 1 || this.signorder == 2) && "1".equals(this.signtype)) {
/* 415 */       paramRecordSetTrans.executeSql("select * from workflow_currentoperator where (isremark='7' or isremark='0') and requestid='" + paramInt3 + "' and nodeid='" + paramInt2 + "' and groupdetailid='" + this.groupdetailid + "'");
/* 416 */       i = paramRecordSetTrans.getCounts();
/* 417 */       if (paramInt4 == j) {
/* 418 */         paramRecordSetTrans.executeSql("select * from workflow_currentoperator where  isremark='0' and userid='" + paramInt4 + "' and requestid='" + paramInt3 + "' and nodeid='" + paramInt2 + "' and groupdetailid='" + this.groupdetailid + "'");
/* 419 */         if (paramRecordSetTrans.next()) {
/* 420 */           i--;
/*     */         }
/*     */       } 
/*     */     } 
/*     */     
/* 425 */     recordSet.executeSql("select id from workflow_groupdetail where groupid in(select id from workflow_nodegroup where nodeid='" + paramInt2 + "' and id<>(select groupid from workflow_groupdetail where id='" + this.groupdetailid + "'))");
/* 426 */     while (recordSet.next()) {
/* 427 */       String str = recordSet.getString("id");
/* 428 */       paramRecordSetTrans.executeSql("select count(id) from workflow_currentoperator where (isremark='7' or isremark='0') and requestid='" + paramInt3 + "' and nodeid='" + paramInt2 + "' and groupdetailid='" + str + "'");
/* 429 */       paramRecordSetTrans.next();
/* 430 */       i += paramRecordSetTrans.getInt(1);
/*     */     } 
/* 432 */     return i;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getCoadjutantUser(int paramInt1, int paramInt2, int paramInt3, RecordSetTrans paramRecordSetTrans) throws Exception {
/* 443 */     int i = 0;
/* 444 */     paramRecordSetTrans.executeSql("select userid from workflow_currentoperator where (isremark='7' or preisremark='7') and groupdetailid='" + paramInt1 + "' and nodeid='" + paramInt2 + "' and requestid='" + paramInt3 + "'");
/* 445 */     if (paramRecordSetTrans.next()) {
/* 446 */       i = paramRecordSetTrans.getInt("userid");
/*     */     }
/* 448 */     return i;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean getOtherGourpUser(int paramInt) {
/* 457 */     boolean bool = false;
/* 458 */     RecordSet recordSet = new RecordSet();
/* 459 */     recordSet.executeSql("select COUNT(id) from workflow_groupdetail wg where exists(select 1 from workflow_nodegroup where wg.groupid=id and nodeid='" + paramInt + "' and not exists(select 1 from workflow_groupdetail where id='" + this.groupdetailid + "' and workflow_nodegroup.id=groupid))");
/* 460 */     if (recordSet.next() && recordSet.getInt(1) > 0) bool = true; 
/* 461 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getSameGroupdetailids() {
/* 469 */     String str = "";
/* 470 */     RecordSet recordSet = new RecordSet();
/* 471 */     recordSet.executeSql("select id from workflow_groupdetail where groupid =( select groupid from workflow_groupdetail where id='" + this.groupdetailid + "')");
/* 472 */     while (recordSet.next()) {
/* 473 */       str = str + Util.null2String(recordSet.getString("id")) + ",";
/*     */     }
/*     */     
/* 476 */     if (!"".equals(str)) str = str.substring(0, str.length() - 1); 
/* 477 */     return str;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/WFCoadjutantManager.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */