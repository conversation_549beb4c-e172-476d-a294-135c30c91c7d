/*    */ package weaver.workflow.request;
/*    */ 
/*    */ import java.util.HashMap;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class TestWork
/*    */   implements RequestOutWork
/*    */ {
/*    */   public boolean execute(int paramInt, HashMap paramHashMap) throws RequestOutWorkException {
/* 38 */     boolean bool = false;
/* 39 */     RequestOutData requestOutData = new RequestOutData(paramInt);
/* 40 */     bool = requestOutData.loadData();
/*    */ 
/*    */ 
/*    */ 
/*    */     
/* 45 */     while (requestOutData.next());
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */     
/* 51 */     return bool;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/TestWork.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */