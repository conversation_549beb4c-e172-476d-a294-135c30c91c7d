/*     */ package weaver.workflow.request;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import org.apache.commons.logging.Log;
/*     */ import org.apache.commons.logging.LogFactory;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.docs.docs.DocComInfo;
/*     */ import weaver.docs.util.DocTriggerUtils;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ import weaver.workflow.workflow.WorkflowComInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class RequestDocPropManager
/*     */   extends BaseBean
/*     */ {
/*  40 */   private static final Log LOG = LogFactory.getLog(RequestDocPropManager.class);
/*     */   
/*  42 */   private char flag = Util.getSeparator();
/*     */ 
/*     */   
/*     */   private WorkflowComInfo workflowComInfo;
/*     */ 
/*     */   
/*     */   private DocComInfo docComInfo;
/*     */ 
/*     */   
/*     */   public RequestDocPropManager() {
/*     */     try {
/*  53 */       this.workflowComInfo = new WorkflowComInfo();
/*  54 */       this.docComInfo = new DocComInfo();
/*  55 */     } catch (Exception exception) {}
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void changeDocProp(RequestManager paramRequestManager, String paramString) throws Exception {
/*  68 */     LOG.error("changeDocProp() docIds=" + paramString);
/*     */     
/*  70 */     int i = paramRequestManager.getRequestid();
/*  71 */     int j = paramRequestManager.getWorkflowid();
/*  72 */     int k = paramRequestManager.getCreater();
/*     */     
/*  74 */     LOG.error("changeDocProp() requestId=" + i + ",workflowId=" + j + ",creater=" + k);
/*     */     
/*  76 */     if (i <= 0 || j <= 0 || paramString == null || paramString
/*     */ 
/*     */       
/*  79 */       .trim().equals("")) {
/*     */       return;
/*     */     }
/*     */ 
/*     */ 
/*     */     
/*  85 */     String str1 = Util.null2String(paramRequestManager.getRequestname());
/*  86 */     LOG.error("changeDocProp() requestName=" + str1);
/*     */     
/*  88 */     RequestDoc requestDoc = new RequestDoc();
/*  89 */     ArrayList<String> arrayList1 = requestDoc.getDocFiled("" + j);
/*  90 */     if (arrayList1 == null || arrayList1.size() == 0) {
/*     */       return;
/*     */     }
/*  93 */     int m = Util.getIntValue(arrayList1.get(1), -1);
/*  94 */     int n = Util.getIntValue(arrayList1.get(3), -1);
/*     */     
/*  96 */     if (m <= 0) {
/*     */       return;
/*     */     }
/*     */     
/* 100 */     int i1 = 0;
/* 101 */     String str2 = "0";
/* 102 */     RecordSet recordSet1 = new RecordSet();
/* 103 */     recordSet1.executeSql(" select formId,isBill from workflow_base where id= " + j);
/* 104 */     if (recordSet1.next()) {
/* 105 */       i1 = Util.getIntValue(recordSet1.getString("formId"), 0);
/* 106 */       str2 = Util.null2String(recordSet1.getString("isBill"));
/*     */     } 
/* 108 */     if (!str2.equals("1")) {
/* 109 */       str2 = "0";
/*     */     }
/* 111 */     LOG.error("changeDocProp() formId=" + i1 + ",isBill=" + str2);
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 116 */     String str3 = null;
/* 117 */     String str4 = null;
/*     */     
/* 119 */     String str5 = "workflow_form";
/* 120 */     String str6 = null;
/* 121 */     if (str2.equals("1")) {
/* 122 */       recordSet1.executeSql("select tablename from workflow_bill where id = " + i1);
/* 123 */       if (recordSet1.next()) {
/* 124 */         str5 = Util.null2String(recordSet1.getString("tablename"));
/*     */       }
/* 126 */       str6 = "select a.fieldname from workflow_billfield a where a.id=" + m;
/*     */     } else {
/* 128 */       str6 = "select a.fieldname from workflow_formdict a where a.id=" + m;
/*     */     } 
/* 130 */     recordSet1.executeSql(str6);
/* 131 */     if (recordSet1.next()) {
/* 132 */       str3 = Util.null2String(recordSet1.getString(1));
/*     */     }
/*     */     
/* 135 */     if (str2.equals("1")) {
/* 136 */       str6 = "select a.fieldname from workflow_billfield a where a.id=" + n;
/*     */     } else {
/* 138 */       str6 = "select a.fieldname from workflow_formdict a where a.id=" + n;
/*     */     } 
/* 140 */     recordSet1.executeSql(str6);
/* 141 */     if (recordSet1.next()) {
/* 142 */       str4 = Util.null2String(recordSet1.getString(1));
/*     */     }
/*     */     
/* 145 */     int i2 = 0;
/* 146 */     int i3 = -1;
/* 147 */     if (str4 != null && !str4.trim().equals("")) {
/* 148 */       str6 = "select " + str3 + "," + str4 + " from " + str5 + " where requestId=" + i;
/*     */     } else {
/* 150 */       str6 = "select " + str3 + ",-1 from " + str5 + " where requestId=" + i;
/*     */     } 
/* 152 */     recordSet1.executeSql(str6);
/* 153 */     if (recordSet1.next()) {
/* 154 */       i2 = Util.getIntValue(recordSet1.getString(1), -1);
/* 155 */       i3 = Util.getIntValue(recordSet1.getString(2), -1);
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/* 160 */     if (i2 <= 0 || paramString == null || paramString.trim().equals("")) {
/*     */       return;
/*     */     }
/* 163 */     LOG.error("changeDocProp() docId=" + i2);
/*     */     
/* 165 */     int i4 = -1;
/* 166 */     recordSet1.executeSql("select secCategory from DocDetail where id=" + i2);
/* 167 */     if (recordSet1.next()) {
/* 168 */       i4 = Util.getIntValue(recordSet1.getString("secCategory"), -1);
/*     */     }
/* 170 */     LOG.error("changeDocProp() secCategoryId=" + i4);
/* 171 */     if (i4 <= 0) {
/*     */       return;
/*     */     }
/*     */ 
/*     */     
/* 176 */     if (paramString.startsWith(",")) {
/* 177 */       paramString = paramString.substring(1);
/*     */     }
/* 179 */     if (paramString.endsWith(",")) {
/* 180 */       paramString = paramString.substring(0, paramString.length() - 1);
/*     */     }
/*     */     
/* 183 */     if (paramString.trim().equals("")) {
/*     */       return;
/*     */     }
/* 186 */     if (("," + paramString + ",").indexOf("," + i2 + ",") == -1) {
/* 187 */       paramString = paramString + "," + i2;
/*     */     }
/*     */ 
/*     */     
/* 191 */     int i5 = -1;
/* 192 */     recordSet1.executeSql("select id from Workflow_DocProp where workflowId=" + j + " and selectItemId=" + i3 + " and secCategoryId=" + i4);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 198 */     String str7 = "0";
/* 199 */     recordSet1.executeSql("select isAccordToSubCom from workflow_selectitem where fieldId=" + n + " and isBill=" + str2 + " and selectValue=" + i3);
/* 200 */     if (recordSet1.next()) {
/* 201 */       str7 = Util.null2String(recordSet1.getString("isAccordToSubCom"));
/*     */     }
/*     */     
/* 204 */     if ("1".equals(str7)) {
/* 205 */       int i13 = 0;
/* 206 */       ResourceComInfo resourceComInfo = new ResourceComInfo();
/* 207 */       i13 = Util.getIntValue(resourceComInfo.getSubCompanyID("" + k));
/*     */       
/* 209 */       recordSet1.executeSql("select id from Workflow_DocProp where workflowId=" + j + " and selectItemId=" + i3 + " and secCategoryId=" + i4 + " and objId=" + i13 + " and objType=1");
/* 210 */       if (recordSet1.next()) {
/* 211 */         i5 = Util.getIntValue(recordSet1.getString("id"), -1);
/*     */       }
/* 213 */       if (i5 <= 0) {
/* 214 */         recordSet1.executeSql("select id from Workflow_DocProp where workflowId=" + j + " and selectItemId=" + i3 + " and secCategoryId=" + i4 + " and objId=-1");
/* 215 */         if (recordSet1.next()) {
/* 216 */           i5 = Util.getIntValue(recordSet1.getString("id"), -1);
/*     */         }
/*     */       } 
/*     */       
/* 220 */       if (i5 <= 0) {
/* 221 */         recordSet1.executeSql("select id from Workflow_DocProp where workflowId=" + j + " and selectItemId=-1 and secCategoryId=" + i4 + " and objId=-1");
/* 222 */         if (recordSet1.next()) {
/* 223 */           i5 = Util.getIntValue(recordSet1.getString("id"), -1);
/*     */         }
/*     */       } 
/*     */     } else {
/*     */       
/* 228 */       recordSet1.executeSql("select id from Workflow_DocProp where workflowId=" + j + " and selectItemId=" + i3 + " and secCategoryId=" + i4 + " and objId=-1");
/* 229 */       if (recordSet1.next()) {
/* 230 */         i5 = Util.getIntValue(recordSet1.getString("id"), -1);
/*     */       }
/*     */       
/* 233 */       if (i5 <= 0) {
/* 234 */         recordSet1.executeSql("select id from Workflow_DocProp where workflowId=" + j + " and selectItemId=-1 and secCategoryId=" + i4 + " and objId=-1");
/* 235 */         if (recordSet1.next()) {
/* 236 */           i5 = Util.getIntValue(recordSet1.getString("id"), -1);
/*     */         }
/*     */       } 
/*     */     } 
/*     */     
/* 241 */     if (i5 <= 0) {
/*     */       return;
/*     */     }
/*     */ 
/*     */     
/* 246 */     int i6 = -1;
/* 247 */     int i7 = -1;
/* 248 */     Object object = null;
/* 249 */     String str8 = "";
/* 250 */     String str9 = "";
/*     */ 
/*     */     
/* 253 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*     */ 
/*     */     
/* 256 */     recordSet1.executeSql("select docPropFieldId,workflowFieldId from Workflow_DocPropDetail where docPropId=" + i5 + " and (workflowFieldId>=0 or workflowFieldId=-3) order by id asc");
/* 257 */     while (recordSet1.next()) {
/* 258 */       i6 = Util.getIntValue(recordSet1.getString("docPropFieldId"), -1);
/* 259 */       i7 = Util.getIntValue(recordSet1.getString("workflowFieldId"), -1);
/*     */ 
/*     */ 
/*     */       
/* 263 */       hashMap1.put("" + i6, "" + i7);
/*     */       
/* 265 */       str8 = str8 + "," + i6;
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 284 */     if (str8.equals("")) {
/*     */       return;
/*     */     }
/* 287 */     str8 = str8.substring(1);
/*     */ 
/*     */     
/* 290 */     Map map = getValueOfField(paramRequestManager, j);
/* 291 */     String str10 = null;
/* 292 */     String str11 = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 316 */     String str12 = "update DocDetail set ";
/* 317 */     String str13 = "";
/*     */ 
/*     */ 
/*     */     
/* 321 */     String str14 = "";
/*     */     
/* 323 */     String str15 = "";
/* 324 */     String str16 = "";
/*     */ 
/*     */     
/* 327 */     int i8 = -1;
/* 328 */     int i9 = -1;
/* 329 */     int i10 = -1;
/* 330 */     String str17 = null;
/*     */ 
/*     */     
/* 333 */     String str18 = null;
/* 334 */     String str19 = null;
/* 335 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 336 */     int i11 = 0;
/*     */     
/* 338 */     recordSet1.executeSql("select id as fieldId,fieldDbType,fieldHtmlType,type from cus_formdict");
/* 339 */     while (recordSet1.next()) {
/* 340 */       i10 = Util.getIntValue(recordSet1.getString("fieldId"), -1);
/* 341 */       str17 = Util.null2String(recordSet1.getString("fieldDbType"));
/* 342 */       str18 = Util.null2String(recordSet1.getString("fieldHtmlType"));
/* 343 */       str19 = Util.null2String(recordSet1.getString("type"));
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 350 */       hashMap2.put("fieldDbType" + i10, "" + str17);
/* 351 */       hashMap2.put("fieldHtmlType" + i10, "" + str18);
/* 352 */       hashMap2.put("type" + i10, "" + str19);
/*     */     } 
/*     */     
/* 355 */     StringBuffer stringBuffer = new StringBuffer();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 362 */     stringBuffer.append(" select id as docPropertyId,type as docPropertyType,fieldId ")
/* 363 */       .append("   from DocSecCategoryDocProperty")
/* 364 */       .append("  where secCategoryId=").append(i4)
/* 365 */       .append("    and id in(").append(str8).append(") ")
/* 366 */       .append("  order by viewIndex ");
/*     */     
/* 368 */     recordSet1.executeSql(stringBuffer.toString());
/*     */     
/* 370 */     RecordSet recordSet2 = new RecordSet();
/* 371 */     RecordSet recordSet3 = new RecordSet();
/* 372 */     while (recordSet1.next()) {
/* 373 */       i8 = Util.getIntValue(recordSet1.getString("docPropertyId"), -1);
/* 374 */       i9 = Util.getIntValue(recordSet1.getString("docPropertyType"), -1);
/* 375 */       i10 = Util.getIntValue(recordSet1.getString("fieldId"), -1);
/*     */       
/* 377 */       str17 = Util.null2String((String)hashMap2.get("fieldDbType" + i10));
/* 378 */       str18 = Util.null2String((String)hashMap2.get("fieldHtmlType" + i10));
/*     */ 
/*     */       
/* 381 */       i7 = Util.getIntValue((String)hashMap1.get("" + i8), 0);
/* 382 */       str10 = Util.null2String((String)map.get("field" + i7));
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 399 */       if (i9 == 2 && str13.indexOf(",docCode=") < 0) {
/* 400 */         str13 = str13 + ",docCode='" + interceptString(str10, 200) + "'"; continue;
/* 401 */       }  if (i9 == 3 && str13.indexOf(",docPublishType=") < 0) {
/* 402 */         str13 = str13 + ",docPublishType='" + interceptString(str10, 1) + "'"; continue;
/* 403 */       }  if (i9 == 4 && str13.indexOf(",docedition=") < 0) {
/* 404 */         str13 = str13 + ",docedition=" + Util.getIntValue(str10, -1); continue;
/* 405 */       }  if (i9 == 5 && str13.indexOf(",docStatus=") < 0) {
/* 406 */         str13 = str13 + ",docStatus=" + Util.getIntValue(str10, -1); continue;
/* 407 */       }  if (i9 == 9 && str13.indexOf(",docDepartmentId=") < 0) {
/* 408 */         str13 = str13 + ",docDepartmentId=" + Util.getIntValue(str10, -1); continue;
/* 409 */       }  if (i9 == 12 && str13.indexOf(",keyword=") < 0) {
/* 410 */         str13 = str13 + ",keyword='" + interceptString(str10, 255) + "'"; continue;
/* 411 */       }  if (i9 == 19 && str13.indexOf(",mainDoc=") < 0) {
/* 412 */         str13 = str13 + ",mainDoc=" + Util.getIntValue(str10, i2); continue;
/* 413 */       }  if (i9 == 21 && str13.indexOf(",ownerId=") < 0) {
/* 414 */         str13 = str13 + ",ownerId=" + Util.getIntValue(str10, -1); continue;
/* 415 */       }  if (i9 == 22 && str13.indexOf(",invalidationDate=") < 0) {
/* 416 */         str13 = str13 + ",invalidationDate='" + interceptString(str10, 10) + "'"; continue;
/* 417 */       }  if (i9 == 25 && str13.indexOf(",canPrintedNum=") < 0) {
/* 418 */         str13 = str13 + ",canPrintedNum=" + Util.getIntValue(str10, 0); continue;
/* 419 */       }  if (i9 == 0) {
/* 420 */         if (str14.indexOf("," + getFieldNameByFieldId(i10) + "=") < 0) {
/* 421 */           str14 = str14 + "," + getFieldNameByFieldId(i10) + "=";
/* 422 */           if (str17.startsWith("text") || str17.startsWith("char") || str17
/* 423 */             .startsWith("varchar") || str17.startsWith("browser")) {
/* 424 */             if (str17.indexOf("(") >= 0 && str17.indexOf(")") > str17.indexOf("(") + 1) {
/* 425 */               i11 = Util.getIntValue(str17.substring(str17.indexOf("(") + 1, str17.indexOf(")")), 0);
/*     */             }
/* 427 */             if (i11 > 0) {
/* 428 */               str14 = str14 + "'" + interceptString(str10, i11) + "'";
/*     */             } else {
/* 430 */               str14 = str14 + "'" + str10 + "'";
/*     */             }
/*     */           
/* 433 */           } else if (str18.equals("5")) {
/* 434 */             str11 = (String)map.get("fieldShowName" + i7);
/* 435 */             str10 = getNewSelectValue(str11, str10, i10);
/* 436 */             str14 = str14 + Util.getIntValue(str10, -1);
/* 437 */           } else if (str18.equals("1")) {
/* 438 */             str14 = str14 + Util.getDoubleValue(str10, 0.0D);
/*     */           } else {
/* 440 */             str14 = str14 + Util.getIntValue(str10, 0);
/*     */           } 
/*     */         } 
/*     */ 
/*     */ 
/*     */         
/* 446 */         if ((str15 + ",").indexOf("," + getFieldNameByFieldId(i10) + ",") < 0) {
/* 447 */           str15 = str15 + "," + getFieldNameByFieldId(i10);
/* 448 */           if (str17.startsWith("text") || str17.startsWith("char") || str17
/* 449 */             .startsWith("varchar") || str17.startsWith("browser")) {
/* 450 */             if (str17.indexOf("(") >= 0 && str17.indexOf(")") > str17.indexOf("(") + 1) {
/* 451 */               i11 = Util.getIntValue(str17.substring(str17.indexOf("(") + 1, str17.indexOf(")")), 0);
/*     */             }
/* 453 */             if (i11 > 0) {
/* 454 */               str16 = str16 + ",'" + interceptString(str10, i11) + "'"; continue;
/*     */             } 
/* 456 */             str16 = str16 + ",'" + str10 + "'";
/*     */             continue;
/*     */           } 
/* 459 */           if (str18.equals("5")) {
/* 460 */             str11 = (String)map.get("fieldShowName" + i7);
/* 461 */             str10 = getNewSelectValue(str11, str10, i10);
/* 462 */             str16 = str16 + "," + Util.getIntValue(str10, -1); continue;
/* 463 */           }  if (str18.equals("1")) {
/* 464 */             str16 = str16 + "," + Util.getDoubleValue(str10, 0.0D); continue;
/*     */           } 
/* 466 */           str16 = str16 + "," + Util.getIntValue(str10, 0);
/*     */         } 
/*     */       } 
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 474 */     LOG.error("changeDocProp() docIds=" + paramString);
/* 475 */     ArrayList<String> arrayList2 = Util.TokenizerString(paramString, ",");
/* 476 */     for (byte b1 = 0; b1 < arrayList2.size(); b1++) {
/*     */       
/* 478 */       String str = (new StringBuilder()).append(arrayList2.get(b1)).append("").toString();
/* 479 */       recordSet1.executeQuery("select id from docimagefile where docid = ? and (isextfile = 0 or isextfile is null)", new Object[] { str });
/* 480 */       if (recordSet1.next())
/*     */       {
/* 482 */         if (!str13.equals("")) {
/* 483 */           str13 = str13.substring(1);
/* 484 */           str12 = str12 + str13 + " where id in(" + str + ")";
/* 485 */           recordSet1.executeSql(str12);
/*     */           
/* 487 */           DocTriggerUtils docTriggerUtils = new DocTriggerUtils();
/* 488 */           docTriggerUtils.docdetail_getpinyin(paramString, recordSet1);
/*     */         } 
/*     */       }
/*     */     } 
/*     */     
/* 493 */     String str20 = null;
/* 494 */     String str21 = null;
/* 495 */     int i12 = 0;
/* 496 */     boolean bool = false;
/* 497 */     if (!str14.equals("")) {
/* 498 */       str14 = str14.substring(1);
/*     */     }
/* 500 */     if (!str15.equals("")) {
/* 501 */       str15 = str15.substring(1);
/* 502 */       str16 = str16.substring(1);
/*     */     } 
/* 504 */     for (byte b2 = 0; b2 < arrayList2.size(); b2++) {
/* 505 */       i12 = Util.getIntValue(arrayList2.get(b2));
/* 506 */       recordSet1.executeSql("select secCategory from DocDetail where id=" + i12);
/* 507 */       if (recordSet1.next()) {
/* 508 */         i4 = Util.getIntValue(recordSet1.getString("secCategory"), -1);
/*     */       }
/* 510 */       bool = false;
/* 511 */       str20 = "update cus_fielddata set ";
/* 512 */       str21 = "insert into cus_fielddata";
/* 513 */       recordSet1.executeSql("select id from cus_fielddata where scope='DocCustomFieldBySecCategory' and scopeid=" + i4 + " and id=" + i12);
/* 514 */       if (recordSet1.next()) {
/* 515 */         bool = true;
/*     */       }
/* 517 */       if (bool) {
/* 518 */         if (!str14.equals("")) {
/* 519 */           str20 = str20 + str14 + " where scope='DocCustomFieldBySecCategory' and scopeid=" + i4 + " and id=" + i12;
/* 520 */           recordSet1.executeSql(str20);
/*     */         }
/*     */       
/* 523 */       } else if (!str15.equals("")) {
/* 524 */         str21 = str21 + "(scope,scopeid,id," + str15 + ") values('DocCustomFieldBySecCategory'," + i4 + "," + i12 + "," + str16 + ")";
/*     */         
/* 526 */         recordSet1.executeSql(str21);
/*     */       } 
/*     */       
/* 529 */       LOG.error("changeDocProp() docId = " + i12 + ",isEditCustomData=" + bool + ",secCategoryId=" + i4);
/* 530 */       LOG.error("changeDocProp() cusFieldDataSql_update=" + str20);
/* 531 */       LOG.error("changeDocProp() cusFieldDataSql_insert=" + str21);
/*     */       try {
/* 533 */         this.docComInfo.updateDocInfoCache("" + i12);
/* 534 */       } catch (Exception exception) {
/* 535 */         writeLog("ex=" + exception);
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static String interceptString(String paramString, int paramInt) {
/*     */     try {
/* 550 */       byte b1 = 0;
/* 551 */       byte[] arrayOfByte = paramString.getBytes("UTF-8");
/* 552 */       if (arrayOfByte.length <= paramInt)
/* 553 */         return paramString; 
/* 554 */       for (byte b2 = 0; b2 < paramInt; b2++) {
/* 555 */         if (arrayOfByte[b2] < 0) {
/* 556 */           b1++;
/*     */         }
/*     */       } 
/* 559 */       if (b1 % 2 == 0) {
/* 560 */         return new String(arrayOfByte, 0, paramInt, "UTF-8");
/*     */       }
/* 562 */       return new String(arrayOfByte, 0, paramInt - 1, "UTF-8");
/*     */     }
/* 564 */     catch (Exception exception) {
/* 565 */       return "";
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private Map getValueOfField(RequestManager paramRequestManager, int paramInt) {
/* 582 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*     */ 
/*     */ 
/*     */     
/* 586 */     String str1 = "";
/* 587 */     String str2 = "";
/* 588 */     RecordSet recordSet = new RecordSet();
/* 589 */     recordSet.executeSql(" select formId,isBill from workflow_base where id=" + paramInt);
/* 590 */     if (recordSet.next()) {
/* 591 */       str1 = recordSet.getString(1);
/* 592 */       str2 = recordSet.getString(2);
/*     */     } 
/* 594 */     if (!"1".equals(str2)) {
/* 595 */       str2 = "0";
/*     */     }
/* 597 */     int i = paramRequestManager.getRequestid();
/*     */     
/* 599 */     hashMap1.put("field-2", "" + i);
/* 600 */     hashMap1.put("field-3", paramRequestManager.getRequestname());
/* 601 */     hashMap1.put("field-4", paramRequestManager.getRequestlevel());
/* 602 */     hashMap1.put("field-5", paramRequestManager.getMessageType());
/*     */ 
/*     */     
/* 605 */     String str3 = "";
/* 606 */     String str4 = "";
/* 607 */     String str5 = "";
/* 608 */     String str6 = "";
/* 609 */     String str7 = "";
/*     */     
/* 611 */     String str8 = "workflow_form";
/*     */     
/* 613 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 614 */     String str9 = "";
/* 615 */     byte b = 0;
/* 616 */     String str10 = "";
/*     */     
/* 618 */     if ("0".equals(str2)) {
/* 619 */       str10 = "select b.id,b.fieldName,b.fieldHtmlType,b.type from workflow_formfield a,workflow_formdict b where a.fieldId=b.id and  (a.isdetail<>'1' or a.isdetail is null) and  a.formId=" + str1;
/*     */     } else {
/*     */       
/* 622 */       recordSet.executeSql(" select tableName from workflow_bill where id=" + str1);
/* 623 */       if (recordSet.next()) {
/* 624 */         str8 = Util.null2String(recordSet.getString("tableName"));
/*     */       }
/*     */       
/* 627 */       str10 = "select id,fieldName,fieldHtmlType,type from workflow_billfield where (viewtype is null or viewtype<>1) and billId=" + str1;
/*     */     } 
/* 629 */     recordSet.executeSql(str10);
/* 630 */     while (recordSet.next()) {
/* 631 */       str3 = Util.null2String(recordSet.getString("id"));
/* 632 */       str4 = Util.null2String(recordSet.getString("fieldName"));
/* 633 */       str6 = Util.null2String(recordSet.getString("fieldHtmlType"));
/* 634 */       str7 = Util.null2String(recordSet.getString("type"));
/*     */       
/* 636 */       if (!str4.equals("")) {
/* 637 */         if (str9.equals("")) {
/* 638 */           str9 = str4;
/*     */         } else {
/* 640 */           str9 = str9 + "," + str4;
/*     */         } 
/* 642 */         hashMap2.put("col" + b, str3);
/* 643 */         b++;
/*     */       } 
/*     */       
/* 646 */       hashMap1.put("fieldHtmlType" + str3, str6);
/* 647 */       hashMap1.put("type" + str3, str7);
/*     */     } 
/*     */     
/* 650 */     if (str9.equals("") || str8.equals("")) {
/* 651 */       return hashMap1;
/*     */     }
/*     */     
/* 654 */     String str11 = "";
/*     */     
/* 656 */     str10 = "select " + str9 + " from " + str8 + " where requestid= " + i;
/* 657 */     recordSet.executeSql(str10);
/* 658 */     while (recordSet.next()) {
/* 659 */       for (byte b1 = 0; b1 < b; b1++) {
/* 660 */         str5 = Util.null2String(recordSet.getString(b1 + 1));
/*     */         
/* 662 */         str3 = Util.null2String((String)hashMap2.get("col" + b1));
/* 663 */         str6 = Util.null2String((String)hashMap1.get("fieldHtmlType" + str3));
/*     */         
/* 665 */         if (str6.equals("5") && !"".equals(str5)) {
/* 666 */           RecordSet recordSet1 = new RecordSet();
/* 667 */           recordSet1.executeSql("  select selectName from workflow_selectitem where fieldId=" + str3 + " and isBill=" + str2 + " and selectValue=" + str5);
/* 668 */           if (recordSet1.next()) {
/* 669 */             str11 = Util.null2String(recordSet1.getString("selectName"));
/* 670 */             hashMap1.put("fieldShowName" + str3, str11);
/*     */           } 
/*     */         } 
/*     */         
/* 674 */         hashMap1.put("field" + str3, str5);
/*     */       } 
/*     */     } 
/*     */     
/* 678 */     return hashMap1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String getNewSelectValue(String paramString1, String paramString2, int paramInt) {
/*     */     try {
/* 692 */       String str = "";
/* 693 */       paramString1 = Util.null2String(paramString1);
/* 694 */       RecordSet recordSet = new RecordSet();
/* 695 */       recordSet.executeSql("select selectValue from cus_selectitem where fieldId=" + paramInt + " and selectName='" + Util.toHtml100(paramString1) + "'");
/* 696 */       if (recordSet.next()) {
/* 697 */         str = Util.null2String(recordSet.getString("selectValue"));
/*     */       }
/* 699 */       if (str.equals("")) {
/* 700 */         str = paramString2;
/*     */       }
/* 702 */       return str;
/* 703 */     } catch (Exception exception) {
/* 704 */       return paramString2;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String getFieldNameByFieldId(int paramInt) {
/* 716 */     String str = "field" + paramInt;
/*     */     try {
/* 718 */       RecordSet recordSet = new RecordSet();
/* 719 */       recordSet.executeSql("select fieldName from cus_formdict where id=" + paramInt);
/* 720 */       if (recordSet.next()) {
/* 721 */         str = Util.null2String(recordSet.getString("fieldName"));
/*     */       }
/* 723 */     } catch (Exception exception) {}
/*     */     
/* 725 */     return str;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/RequestDocPropManager.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */