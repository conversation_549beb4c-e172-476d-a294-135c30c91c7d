/*    */ package weaver.workflow.request;
/*    */ 
/*    */ import java.util.List;
/*    */ import java.util.Vector;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.file.Prop;
/*    */ import weaver.general.Util;
/*    */ 
/*    */ 
/*    */ public class WfAutoflowLogThread
/*    */   extends Thread
/*    */ {
/* 13 */   public static List<List> autoFlowLogBeanList = new Vector<>();
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   private static WfAutoflowLogThread wfAutoflowLogThread;
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public static WfAutoflowLogThread getInstance() {
/* 24 */     if (null == wfAutoflowLogThread) {
/* 25 */       synchronized (WfAutoflowLogThread.class) {
/* 26 */         if (null == wfAutoflowLogThread) {
/* 27 */           wfAutoflowLogThread = new WfAutoflowLogThread();
/*    */         }
/*    */       } 
/*    */     }
/* 31 */     return wfAutoflowLogThread;
/*    */   }
/*    */ 
/*    */   
/*    */   public void run() {
/* 36 */     Prop.getInstance(); int i = Util.getIntValue(Prop.getPropValue("wfAutoflowLog", "wfAutoflowLogThreadSleepTime"));
/* 37 */     Vector<List> vector = new Vector(); while (true) {
/*    */       try {
/*    */         while (true)
/* 40 */         { this; vector.addAll(autoFlowLogBeanList);
/* 41 */           this; autoFlowLogBeanList.clear();
/* 42 */           InitWfAutoflowLog(vector);
/* 43 */           vector.clear();
/* 44 */           Thread.sleep((i * 1000)); }  break;
/* 45 */       } catch (InterruptedException interruptedException) {
/* 46 */         interruptedException.printStackTrace();
/*    */       } 
/*    */     } 
/*    */   }
/*    */   
/*    */   public void InitWfAutoflowLog(List<List> paramList) {
/* 52 */     RecordSet recordSet = new RecordSet();
/* 53 */     String str = "";
/* 54 */     if (paramList != null && paramList.size() > 0) {
/* 55 */       str = "insert into workflow_autoFlowLog(requestid, issuccess, nodeid, nextnodeid,type, failedtype,operator, operatedate,operatetime) values(?,?,?,?,?,?,?,?,?)";
/*    */     }
/* 57 */     recordSet.executeBatchSql(str, paramList);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/WfAutoflowLogThread.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */