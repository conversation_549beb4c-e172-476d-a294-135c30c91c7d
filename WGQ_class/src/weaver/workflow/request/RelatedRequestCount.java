/*     */ package weaver.workflow.request;
/*     */ 
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class RelatedRequestCount
/*     */   extends BaseBean
/*     */ {
/*     */   private int relatedid;
/*     */   private String relatedtype;
/*     */   private int userid;
/*     */   private int usertype;
/*     */   private int totalcount;
/*     */   private int count0;
/*     */   private int count1;
/*     */   private int count2;
/*     */   private int count3;
/*     */   
/*     */   public void resetParameter() {
/*  40 */     this.relatedid = 0;
/*  41 */     this.userid = 0;
/*  42 */     this.usertype = 0;
/*  43 */     this.relatedtype = "";
/*  44 */     this.totalcount = 0;
/*  45 */     this.count0 = 0;
/*  46 */     this.count1 = 0;
/*  47 */     this.count2 = 0;
/*  48 */     this.count3 = 0;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setRelatedid(int paramInt) {
/*  55 */     this.relatedid = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setUserid(int paramInt) {
/*  62 */     this.userid = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setUsertype(int paramInt) {
/*  69 */     this.usertype = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setRelatedtype(String paramString) {
/*  76 */     this.relatedtype = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getTotalcount() {
/*  83 */     return this.totalcount;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getCount0() {
/*  90 */     return this.count0;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getCount1() {
/*  97 */     return this.count1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getCount2() {
/* 104 */     return this.count2;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getCount3() {
/* 111 */     return this.count3;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void selectRelatedCount() {
/* 118 */     String str = "";
/* 119 */     RecordSet recordSet = new RecordSet();
/*     */     
/* 121 */     str = "select count(distinct t1.requestid),t1.currentnodetype from workflow_requestbase t1,workflow_currentoperator t2 where t1.requestid = t2.requestid and (exists (select 1 from workflow_base where (isvalid=1 or isvalid=3) and workflow_base.id=t1.workflowid))  and t2.islasttimes=1 and t2.userid = " + this.userid + " and t2.usertype=" + this.usertype;
/*     */     
/* 123 */     if (recordSet.getDBType().equals("oracle")) {
/* 124 */       if (this.relatedtype.equals("resource")) {
/* 125 */         str = str + " and ( concat(concat(',' , to_char(t1.hrmids) ) , ',') LIKE '%," + this.relatedid + ",%') ";
/* 126 */       } else if (this.relatedtype.equals("crm")) {
/* 127 */         str = str + " and ( concat(concat(',' , to_char(t1.crmids) ) , ',') LIKE '%," + this.relatedid + ",%') ";
/* 128 */       } else if (this.relatedtype.equals("proj")) {
/* 129 */         str = str + " and ( concat(concat(',' , to_char(t1.prjids) ) , ',') LIKE '%," + this.relatedid + ",%') ";
/*     */       }
/*     */     
/* 132 */     } else if (recordSet.getDBType().equals("postgresql")) {
/* 133 */       if (this.relatedtype.equals("resource")) {
/* 134 */         str = str + " and ( concat(concat(',' , to_char(t1.hrmids) ) , ',') LIKE '%," + this.relatedid + ",%') ";
/* 135 */       } else if (this.relatedtype.equals("crm")) {
/* 136 */         str = str + " and ( concat(concat(',' , to_char(t1.crmids) ) , ',') LIKE '%," + this.relatedid + ",%') ";
/* 137 */       } else if (this.relatedtype.equals("proj")) {
/* 138 */         str = str + " and ( concat(concat(',' , to_char(t1.prjids) ) , ',') LIKE '%," + this.relatedid + ",%') ";
/*     */       } 
/* 140 */     } else if (recordSet.getDBType().equals("db2")) {
/* 141 */       if (this.relatedtype.equals("resource"))
/* 142 */       { str = str + " and ( concat(concat(',' , varchar(t1.hrmids) ) , ',') LIKE '%," + this.relatedid + ",%') "; }
/* 143 */       else if (this.relatedtype.equals("crm"))
/* 144 */       { str = str + " and ( concat(concat(',' , varchar(t1.crmids) ) , ',') LIKE '%," + this.relatedid + ",%') "; }
/* 145 */       else if (this.relatedtype.equals("proj"))
/* 146 */       { str = str + " and ( concat(concat(',' , varchar(t1.prjids) ) , ',') LIKE '%," + this.relatedid + ",%') "; } 
/* 147 */     } else if (recordSet.getDBType().equals("mysql")) {
/* 148 */       if (this.relatedtype.equals("resource")) {
/* 149 */         str = str + " and ( concat(',' , hrmids , ',') LIKE '%," + this.relatedid + ",%') ";
/* 150 */       } else if (this.relatedtype.equals("crm")) {
/* 151 */         str = str + " and ( concat(',' , crmids , ',') LIKE '%," + this.relatedid + ",%') ";
/* 152 */       } else if (this.relatedtype.equals("proj")) {
/* 153 */         str = str + " and ( concat(',' , prjids , ',') LIKE '%," + this.relatedid + ",%') ";
/*     */       }
/*     */     
/* 156 */     } else if (this.relatedtype.equals("resource")) {
/* 157 */       str = str + " and (',' + CONVERT(varchar,t1.hrmids) + ',' LIKE '%," + this.relatedid + ",%') ";
/* 158 */     } else if (this.relatedtype.equals("crm")) {
/* 159 */       str = str + " and (',' + CONVERT(varchar,t1.crmids) + ',' LIKE '%," + this.relatedid + ",%') ";
/* 160 */     } else if (this.relatedtype.equals("proj")) {
/* 161 */       str = str + " and (',' + CONVERT(varchar,t1.prjids) + ',' LIKE '%," + this.relatedid + ",%') ";
/*     */     } 
/*     */     
/* 164 */     str = str + "group by t1.currentnodetype order by t1.currentnodetype";
/*     */     
/* 166 */     if (this.relatedtype.equals("hrmresource")) {
/* 167 */       str = " select count(distinct t1.requestid) , t3.nodetype  from workflow_requestLog t1, workflow_currentoperator t2 ,workflow_flownode t3   where t1.requestid = t2.requestid and t1.nodeid = t3.nodeid and t1.logtype in ('0','2') and  t1.operator=" + this.relatedid + " and t1.operatortype =0 and t2.userid = " + this.userid + " and t2.usertype=" + this.usertype + " group by t3.nodetype order by t3.nodetype ";
/*     */     }
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 173 */     recordSet.executeSql(str);
/*     */ 
/*     */     
/* 176 */     while (recordSet.next()) {
/* 177 */       if (recordSet.getString(2).equals("0")) this.count0 = recordSet.getInt(1); 
/* 178 */       if (recordSet.getString(2).equals("1")) this.count1 = recordSet.getInt(1); 
/* 179 */       if (recordSet.getString(2).equals("2")) this.count2 = recordSet.getInt(1); 
/* 180 */       if (recordSet.getString(2).equals("3")) this.count3 = recordSet.getInt(1);
/*     */     
/*     */     } 
/* 183 */     if (this.relatedtype.equals("hrmresource")) {
/* 184 */       str = " select count(distinct t1.requestid)  from workflow_currentoperator t1,workflow_requestbase t2   where t1.userid=" + this.relatedid + " and t1.usertype=0 and t1.requestid=t2.requestid and (t2.deleted=0 or t2.deleted is null) and t2.currentnodetype='3'  and t1.requestid in ( select requestid from workflow_currentoperator  where userid=" + this.userid + " and usertype= " + this.usertype + ") ";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 191 */       recordSet.executeSql(str);
/*     */       
/* 193 */       if (recordSet.next()) {
/* 194 */         this.count3 = recordSet.getInt(1);
/*     */       }
/*     */       
/* 197 */       str = "select count(distinct t1.requestid) from workflow_requestbase t1, workflow_currentoperator t2  where t2.requestid = t1.requestid and (  (t2.isremark ='2' and exists(select nodeid from workflow_flownode f where f.nodeid=t2.nodeid and f.nodetype in ('0','2'))) or (t1.currentnodetype='3')  )   and t2.userid = " + this.relatedid + " and t2.usertype=" + this.usertype + " and (t1.deleted=0 or t1.deleted is null)";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 204 */       recordSet.executeSql(str);
/*     */       
/* 206 */       if (recordSet.next()) {
/* 207 */         this.totalcount = recordSet.getInt(1);
/*     */       }
/*     */     } else {
/* 210 */       this.totalcount = this.count0 + this.count1 + this.count2 + this.count3;
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/RelatedRequestCount.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */