/*    */ package weaver.workflow.request;
/*    */ 
/*    */ import java.util.concurrent.ExecutorService;
/*    */ import java.util.concurrent.Executors;
/*    */ import weaver.general.BaseBean;
/*    */ import weaver.general.Util;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class WFAutoApproveThreadPoolUtil
/*    */   extends BaseBean
/*    */ {
/* 16 */   private static ExecutorService fixedThreadPool = null;
/*    */   
/*    */   static {
/* 19 */     if (fixedThreadPool == null) {
/* 20 */       int i = Util.getIntValue(Util.null2String((new WFPathUtil()).getPropValue("ThreadPoolConfig", "wfautoapprovethreadcount")));
/* 21 */       if (i < 5) {
/* 22 */         i = 5;
/*    */       }
/* 24 */       fixedThreadPool = Executors.newFixedThreadPool(i);
/*    */     } 
/*    */   }
/*    */   
/*    */   public static ExecutorService getFixedThreadPool() {
/* 29 */     return fixedThreadPool;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/WFAutoApproveThreadPoolUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */