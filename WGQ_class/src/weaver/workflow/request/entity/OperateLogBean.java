/*     */ package weaver.workflow.request.entity;
/*     */ 
/*     */ import com.alibaba.fastjson.JSONArray;
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import com.google.common.base.Strings;
/*     */ 
/*     */ 
/*     */ public class OperateLogBean
/*     */ {
/*  10 */   private int id = 0;
/*  11 */   private int requestId = 0;
/*  12 */   private int nodeId = 0;
/*  13 */   private String isremark = "";
/*  14 */   private int operatorId = 0;
/*  15 */   private int operatorType = 0;
/*  16 */   private String operateDate = "";
/*  17 */   private String operateTime = "";
/*  18 */   private String operateType = "";
/*  19 */   private String operateName = "";
/*  20 */   private JSONObject detailInfo = new JSONObject();
/*     */   
/*     */   public int getId() {
/*  23 */     return this.id;
/*     */   }
/*     */   public void setId(int paramInt) {
/*  26 */     this.id = paramInt;
/*     */   }
/*     */   public int getRequestId() {
/*  29 */     return this.requestId;
/*     */   }
/*     */   public void setRequestId(int paramInt) {
/*  32 */     this.requestId = paramInt;
/*     */   }
/*     */   public int getNodeId() {
/*  35 */     return this.nodeId;
/*     */   }
/*     */   public void setNodeId(int paramInt) {
/*  38 */     this.nodeId = paramInt;
/*     */   }
/*     */   public String getIsremark() {
/*  41 */     return this.isremark;
/*     */   }
/*     */   public void setIsremark(String paramString) {
/*  44 */     this.isremark = paramString;
/*     */   }
/*     */   public int getOperatorId() {
/*  47 */     return this.operatorId;
/*     */   }
/*     */   public void setOperatorId(int paramInt) {
/*  50 */     this.operatorId = paramInt;
/*     */   }
/*     */   public String getOperateDate() {
/*  53 */     return this.operateDate;
/*     */   }
/*     */   public void setOperateDate(String paramString) {
/*  56 */     this.operateDate = paramString;
/*     */   }
/*     */   public String getOperateTime() {
/*  59 */     return this.operateTime;
/*     */   }
/*     */   public void setOperateTime(String paramString) {
/*  62 */     this.operateTime = paramString;
/*     */   }
/*     */   public String getOperateType() {
/*  65 */     return this.operateType;
/*     */   }
/*     */   public void setOperateType(String paramString) {
/*  68 */     this.operateType = paramString;
/*     */   }
/*     */   public String getoperateName() {
/*  71 */     return this.operateName;
/*     */   }
/*     */   public void setoperateName(String paramString) {
/*  74 */     this.operateName = paramString;
/*     */   }
/*     */   public int getOperatorType() {
/*  77 */     return this.operatorType;
/*     */   }
/*     */   public void setOperatorType(int paramInt) {
/*  80 */     this.operatorType = paramInt;
/*     */   }
/*     */ 
/*     */   
/*     */   public JSONObject getDetailInfo() {
/*  85 */     return this.detailInfo;
/*     */   }
/*     */   
/*     */   public void setDetailInfo(JSONObject paramJSONObject) {
/*  89 */     this.detailInfo = paramJSONObject;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getNewRecordEntityIds(RequestOperationTableInfo paramRequestOperationTableInfo) {
/*  99 */     String str = "0";
/*     */     
/*     */     try {
/* 102 */       if (this.detailInfo != null && paramRequestOperationTableInfo != null) {
/* 103 */         JSONObject jSONObject = this.detailInfo.getJSONObject(paramRequestOperationTableInfo.getTableName());
/* 104 */         if (jSONObject != null) {
/* 105 */           String str1 = jSONObject.getString("newIds");
/* 106 */           if (!Strings.isNullOrEmpty(str1)) {
/* 107 */             str = str + "," + str1;
/*     */           }
/*     */         } 
/*     */       } 
/* 111 */     } catch (Exception exception) {}
/*     */ 
/*     */     
/* 114 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public JSONArray getModifyDatasOfTable(RequestOperationTableInfo paramRequestOperationTableInfo) {
/* 124 */     JSONArray jSONArray = new JSONArray();
/*     */     
/* 126 */     JSONObject jSONObject = this.detailInfo.getJSONObject(paramRequestOperationTableInfo.getTableName());
/* 127 */     if (jSONObject != null) {
/* 128 */       jSONArray = jSONObject.getJSONArray("modifyData");
/*     */     }
/* 130 */     return jSONArray;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getAutoSubmitId(RequestOperationTableInfo paramRequestOperationTableInfo) {
/* 140 */     String str = "";
/* 141 */     JSONObject jSONObject = this.detailInfo.getJSONObject(paramRequestOperationTableInfo.getTableName());
/* 142 */     if (jSONObject != null) {
/* 143 */       str = jSONObject.getString("autoSubmitIds");
/*     */     }
/*     */     
/* 146 */     return str;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/entity/OperateLogBean.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */