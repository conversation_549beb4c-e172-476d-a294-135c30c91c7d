/*    */ package weaver.workflow.request.entity;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class OperateOtherInfoBean
/*    */ {
/* 11 */   private int optLogId = 0;
/* 12 */   private int requestId = 0;
/* 13 */   private int entitytype = 0;
/* 14 */   private int entityid = 0;
/* 15 */   private int count = 0;
/*    */   
/*    */   public int getOptLogId() {
/* 18 */     return this.optLogId;
/*    */   }
/*    */   public void setOptLogId(int paramInt) {
/* 21 */     this.optLogId = paramInt;
/*    */   }
/*    */   public int getRequestId() {
/* 24 */     return this.requestId;
/*    */   }
/*    */   public void setRequestId(int paramInt) {
/* 27 */     this.requestId = paramInt;
/*    */   }
/*    */   public int getEntitytype() {
/* 30 */     return this.entitytype;
/*    */   }
/*    */   public void setEntitytype(int paramInt) {
/* 33 */     this.entitytype = paramInt;
/*    */   }
/*    */   public int getEntityid() {
/* 36 */     return this.entityid;
/*    */   }
/*    */   public void setEntityid(int paramInt) {
/* 39 */     this.entityid = paramInt;
/*    */   }
/*    */   public int getCount() {
/* 42 */     return this.count;
/*    */   }
/*    */   public void setCount(int paramInt) {
/* 45 */     this.count = paramInt;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/entity/OperateOtherInfoBean.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */