/*     */ package weaver.workflow.request.entity;
/*     */ 
/*     */ import com.alibaba.fastjson.JSONArray;
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import com.engine.common.util.LogUtil;
/*     */ import com.google.common.base.Strings;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.HashSet;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import java.util.Set;
/*     */ import org.apache.commons.lang.StringUtils;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class RequestOperationTableInfo
/*     */ {
/*     */   private String tableName;
/*  26 */   private String columns = " * ";
/*     */ 
/*     */   
/*     */   private String otherSql;
/*     */   
/*  31 */   private Set<String> stringColumn = new HashSet<>();
/*     */ 
/*     */   
/*     */   private String pKey;
/*     */   
/*     */   private Map<String, Map<String, Object>> oldDatas;
/*     */   
/*     */   private List<Map<String, Object>> newDatas;
/*     */ 
/*     */   
/*     */   public RequestOperationTableInfo() {}
/*     */ 
/*     */   
/*     */   public RequestOperationTableInfo(String paramString1, String paramString2, String paramString3) {
/*  45 */     this.tableName = paramString1;
/*  46 */     this.columns = paramString2;
/*  47 */     this.pKey = paramString3;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Map<String, Object>> getOldDatas() throws Exception {
/*  56 */     if (Strings.isNullOrEmpty(this.tableName) || Strings.isNullOrEmpty(this.pKey)) throw new Exception("未指定tableName或pKey");
/*     */     
/*  58 */     if (this.oldDatas == null) {
/*  59 */       this.oldDatas = new HashMap<>();
/*     */       
/*  61 */       RecordSet recordSet = new RecordSet();
/*     */       
/*  63 */       String str = "select " + this.columns + " from " + this.tableName + " ";
/*  64 */       if (!Strings.isNullOrEmpty(this.otherSql)) {
/*  65 */         str = str + this.otherSql;
/*     */       }
/*     */       
/*  68 */       recordSet.executeQuery(str, new Object[0]);
/*     */       
/*  70 */       int i = recordSet.getColCounts();
/*  71 */       while (recordSet.next()) {
/*     */         
/*  73 */         HashMap<Object, Object> hashMap = new HashMap<>();
/*     */         
/*  75 */         for (byte b = 1; b <= i; b++) {
/*  76 */           String str2 = recordSet.getColumnName(b).toLowerCase();
/*  77 */           hashMap.put(str2, proSpecialFieldOldValue(str2, Util.null2String(recordSet.getString(b))));
/*     */         } 
/*     */         
/*  80 */         String str1 = Util.null2String(recordSet.getString(this.pKey));
/*  81 */         this.oldDatas.put(str1, hashMap);
/*     */       } 
/*     */     } 
/*  84 */     return this.oldDatas;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<Map<String, Object>> getNewDatas() throws Exception {
/*  94 */     if (Strings.isNullOrEmpty(this.tableName) || Strings.isNullOrEmpty(this.pKey)) throw new Exception("未指定tableName或pKey");
/*     */     
/*  96 */     if (this.newDatas == null) {
/*     */       
/*  98 */       this.newDatas = new ArrayList<>();
/*     */       
/* 100 */       StringBuffer stringBuffer = new StringBuffer();
/* 101 */       stringBuffer.append("select ").append(this.columns).append(" from ").append(this.tableName).append(" ").append(this.otherSql);
/*     */       
/* 103 */       RecordSet recordSet = new RecordSet();
/* 104 */       recordSet.executeQuery(stringBuffer.toString(), new Object[0]);
/*     */       
/* 106 */       int i = recordSet.getColCounts();
/*     */       
/* 108 */       for (HashMap<Object, Object> hashMap = new HashMap<>(); recordSet.next(); hashMap = new HashMap<>()) {
/* 109 */         for (byte b = 1; b <= i; b++) {
/* 110 */           hashMap.put(recordSet.getColumnName(b).toLowerCase(), Util.null2String(recordSet.getString(b)));
/*     */         }
/*     */         
/* 113 */         this.newDatas.add(hashMap);
/*     */       } 
/*     */     } 
/*     */     
/* 117 */     return this.newDatas;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public JSONObject getDiffData() {
/* 126 */     JSONObject jSONObject = new JSONObject();
/*     */     
/* 128 */     HashSet<Integer> hashSet1 = new HashSet();
/* 129 */     HashSet<Integer> hashSet2 = new HashSet();
/* 130 */     HashSet<Integer> hashSet3 = new HashSet();
/* 131 */     JSONArray jSONArray = new JSONArray();
/*     */     
/* 133 */     byte b = 0;
/* 134 */     byte b1 = -1;
/* 135 */     for (Map<String, Object> map : this.newDatas) {
/*     */       
/* 137 */       int i = Util.getIntValue(Util.null2String(map.get(this.pKey)), 0);
/* 138 */       Map map1 = this.oldDatas.get(String.valueOf(i));
/*     */ 
/*     */       
/* 141 */       if (map1 == null) {
/*     */         
/* 143 */         if (RequestOperateEntityTableNameEnum.CURRENTOPERATOR.getTableName().equals(this.tableName)) {
/* 144 */           String str1 = Util.null2String(map.get("isremark"));
/* 145 */           String str2 = Util.null2String(map.get("takisremark"));
/* 146 */           String str3 = Util.null2String(map.get("agenttype"));
/* 147 */           if ("2".equals(str1) && !"2".equals(str2)) {
/* 148 */             if ("1".equals(str3)) {
/* 149 */               hashSet3.add(Integer.valueOf(i)); continue;
/*     */             } 
/* 151 */             hashSet2.add(Integer.valueOf(i));
/*     */             
/*     */             continue;
/*     */           } 
/*     */         } 
/* 156 */         hashSet1.add(Integer.valueOf(i));
/* 157 */         b1 = (b1 < i) ? i : b1;
/* 158 */         b++;
/*     */         
/*     */         continue;
/*     */       } 
/*     */       
/* 163 */       LogUtil.removeIntersectionEntry(map1, map);
/*     */       
/* 165 */       if (!map1.isEmpty() || !map.isEmpty()) {
/* 166 */         for (String str : map.keySet()) {
/* 167 */           if (str.equals(this.pKey))
/* 168 */             continue;  JSONObject jSONObject1 = new JSONObject();
/* 169 */           jSONObject1.put("entityId", Integer.valueOf(i));
/* 170 */           jSONObject1.put("fieldName", str);
/* 171 */           jSONObject1.put("oVal", map1.get(str));
/* 172 */           jSONObject1.put("nVal", map.get(str));
/* 173 */           jSONObject1.put("isModify", Integer.valueOf(1));
/*     */           
/* 175 */           jSONArray.add(jSONObject1);
/*     */         } 
/*     */       }
/*     */     } 
/* 179 */     jSONObject.put("modifyData", jSONArray);
/* 180 */     jSONObject.put("newIds", StringUtils.join(hashSet1, ","));
/* 181 */     jSONObject.put("autoSubmitIds", StringUtils.join(hashSet2, ","));
/* 182 */     jSONObject.put("beAgentNewids", StringUtils.join(hashSet3, ","));
/* 183 */     jSONObject.put("newIdCount", Integer.valueOf(b));
/* 184 */     jSONObject.put("maxNewId", Integer.valueOf(b1));
/*     */     
/* 186 */     return jSONObject;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   private String proSpecialFieldOldValue(String paramString1, String paramString2) {
/* 192 */     if ("workflow_currentoperator".equalsIgnoreCase(this.tableName) && "isremark".equalsIgnoreCase(paramString1) && "6".equals(paramString2)) {
/* 193 */       return "0";
/*     */     }
/* 195 */     return paramString2;
/*     */   }
/*     */   
/*     */   public String getTableName() {
/* 199 */     return this.tableName;
/*     */   }
/*     */   
/*     */   public void setTableName(String paramString) {
/* 203 */     this.tableName = paramString;
/*     */   }
/*     */   
/*     */   public String getpKey() {
/* 207 */     return this.pKey;
/*     */   }
/*     */   
/*     */   public void setpKey(String paramString) {
/* 211 */     this.pKey = paramString;
/*     */   }
/*     */   
/*     */   public Set<String> getStringColumn() {
/* 215 */     return this.stringColumn;
/*     */   }
/*     */ 
/*     */   
/*     */   public void setStringColumn(String paramString) {
/* 220 */     String[] arrayOfString = paramString.split(",");
/* 221 */     for (String str : arrayOfString) {
/* 222 */       this.stringColumn.add(str);
/*     */     }
/*     */   }
/*     */   
/*     */   public String getOtherSql() {
/* 227 */     return this.otherSql;
/*     */   }
/*     */   
/*     */   public void setOtherSql(String paramString) {
/* 231 */     this.otherSql = paramString;
/*     */   }
/*     */   
/*     */   public String getColumns() {
/* 235 */     return this.columns;
/*     */   }
/*     */   
/*     */   public void setColumns(String paramString) {
/* 239 */     this.columns = paramString;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/entity/RequestOperationTableInfo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */