/*    */ package weaver.workflow.request.entity;
/*    */ 
/*    */ 
/*    */ public enum RequestOperateTypeEnum
/*    */ {
/*  6 */   SUBMIT("提交", "submit", 1),
/*  7 */   REJECT("退回", "reject", 2),
/*  8 */   INTERVENOR("干预", "intervenor", 3),
/*  9 */   TRANS("转办", "trans", 4),
/* 10 */   TAKTRANS("征询转办", "takTrans", -4),
/* 11 */   TAKE("意见征询", "take", 5),
/* 12 */   TAKEFORWARD("征询转办", "takForward", 6),
/* 13 */   TAKEEND("结束征询", "takEnd", 7),
/* 14 */   FORWARD("转发", "forward", -2),
/* 15 */   FORCEOVER("强制归档", "forceover", 9),
/* 16 */   FORWARDREPLY("转发批注", "forwardreply", 10),
/* 17 */   TAKEREPLY("意见征询回复", "takereply", 11),
/* 18 */   WITHDRAW("撤回", "withdraw", 12);
/*    */ 
/*    */   
/* 21 */   private String name = null;
/* 22 */   private String id = null;
/*    */   
/* 24 */   private int code = 0;
/*    */   
/*    */   RequestOperateTypeEnum(String paramString1, String paramString2, int paramInt1) {
/* 27 */     this.name = paramString1;
/* 28 */     this.id = paramString2;
/* 29 */     this.code = paramInt1;
/*    */   }
/*    */   
/*    */   public String getName() {
/* 33 */     return this.name;
/*    */   }
/*    */   
/*    */   public static String getName(String paramString) {
/* 37 */     for (RequestOperateTypeEnum requestOperateTypeEnum : values()) {
/* 38 */       if (requestOperateTypeEnum.id.equals(paramString)) {
/* 39 */         return requestOperateTypeEnum.name;
/*    */       }
/*    */     } 
/* 42 */     return null;
/*    */   }
/*    */   
/*    */   public static int getCode(String paramString) {
/* 46 */     for (RequestOperateTypeEnum requestOperateTypeEnum : values()) {
/* 47 */       if (requestOperateTypeEnum.id.equals(paramString)) {
/* 48 */         return requestOperateTypeEnum.code;
/*    */       }
/*    */     } 
/* 51 */     return 0;
/*    */   }
/*    */   
/*    */   public int getCode() {
/* 55 */     return this.code;
/*    */   }
/*    */   
/*    */   public String getId() {
/* 59 */     return this.id;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/entity/RequestOperateTypeEnum.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */