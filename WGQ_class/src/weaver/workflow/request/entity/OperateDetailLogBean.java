/*    */ package weaver.workflow.request.entity;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class OperateDetailLogBean
/*    */ {
/* 14 */   private int optLogId = 0;
/* 15 */   private int requestId = 0;
/* 16 */   private int entitytype = 0;
/* 17 */   private int entityid = 0;
/* 18 */   private int ismodify = 0;
/* 19 */   private String fieldname = "";
/* 20 */   private String ovalue = "";
/* 21 */   private String nvalue = "";
/*    */ 
/*    */   
/*    */   public int getOptLogId() {
/* 25 */     return this.optLogId;
/*    */   }
/*    */   public void setOptLogId(int paramInt) {
/* 28 */     this.optLogId = paramInt;
/*    */   }
/*    */   public int getRequestId() {
/* 31 */     return this.requestId;
/*    */   }
/*    */   public void setRequestId(int paramInt) {
/* 34 */     this.requestId = paramInt;
/*    */   }
/*    */   public int getEntitytype() {
/* 37 */     return this.entitytype;
/*    */   }
/*    */   public void setEntitytype(int paramInt) {
/* 40 */     this.entitytype = paramInt;
/*    */   }
/*    */   public int getEntityid() {
/* 43 */     return this.entityid;
/*    */   }
/*    */   public void setEntityid(int paramInt) {
/* 46 */     this.entityid = paramInt;
/*    */   }
/*    */   public int getIsmodify() {
/* 49 */     return this.ismodify;
/*    */   }
/*    */   public void setIsmodify(int paramInt) {
/* 52 */     this.ismodify = paramInt;
/*    */   }
/*    */   public String getFieldname() {
/* 55 */     return this.fieldname;
/*    */   }
/*    */   public void setFieldname(String paramString) {
/* 58 */     this.fieldname = paramString;
/*    */   }
/*    */   public String getOvalue() {
/* 61 */     return this.ovalue;
/*    */   }
/*    */   public void setOvalue(String paramString) {
/* 64 */     this.ovalue = paramString;
/*    */   }
/*    */   public String getNvalue() {
/* 67 */     return this.nvalue;
/*    */   }
/*    */   public void setNvalue(String paramString) {
/* 70 */     this.nvalue = paramString;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/entity/OperateDetailLogBean.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */