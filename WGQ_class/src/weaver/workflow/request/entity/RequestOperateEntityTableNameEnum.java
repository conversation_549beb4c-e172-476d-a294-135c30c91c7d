/*    */ package weaver.workflow.request.entity;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public enum RequestOperateEntityTableNameEnum
/*    */ {
/*  9 */   CURRENTOPERATOR(1, "workflow_currentoperator", "ID"),
/* 10 */   REQUESTLOG(2, "workflow_requestlog", "logid"),
/* 11 */   AGENTPERSONS(3, "workflow_agentpersons", "groupdetailid"),
/* 12 */   REQUESTBASE(4, "workflow_requestbase", "requestid"),
/* 13 */   REQUESTFLOWINFO(5, "workflow_requestflowinfo", "ID"),
/*    */   
/* 15 */   REQFREENODE(6, "workflow_freenode", "ID"),
/* 16 */   REQFREENODEGROUP(7, "workflow_freenode_group", "ID"),
/* 17 */   REQSIMPLEADDSIGNLOG(8, "workflow_simpleaddsignlog", "ID"),
/* 18 */   REQBANCHLOG(9, "workflow_branchlog", "ID");
/*    */   
/* 20 */   private int id = 0;
/* 21 */   private String tableName = "";
/* 22 */   private String idColumnName = "";
/*    */   RequestOperateEntityTableNameEnum(int paramInt1, String paramString1, String paramString2) {
/* 24 */     this.id = paramInt1;
/* 25 */     this.tableName = paramString1;
/* 26 */     this.idColumnName = paramString2;
/*    */   }
/*    */   
/*    */   public static RequestOperateEntityTableNameEnum getEnetity(int paramInt) {
/* 30 */     for (RequestOperateEntityTableNameEnum requestOperateEntityTableNameEnum : values()) {
/* 31 */       if (requestOperateEntityTableNameEnum.id == paramInt) {
/* 32 */         return requestOperateEntityTableNameEnum;
/*    */       }
/*    */     } 
/* 35 */     return null;
/*    */   }
/*    */   
/*    */   public int getId() {
/* 39 */     return this.id;
/*    */   }
/*    */   public String getTableName() {
/* 42 */     return this.tableName;
/*    */   }
/*    */   public String getIdColumnName() {
/* 45 */     return this.idColumnName;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/entity/RequestOperateEntityTableNameEnum.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */