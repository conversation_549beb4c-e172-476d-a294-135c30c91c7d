/*     */ package weaver.workflow.request;
/*     */ 
/*     */ import com.google.common.base.Strings;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.HashSet;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.ThreadVarLanguage;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.workflow.datainput.DynamicDataInput;
/*     */ 
/*     */ public class RequestFieldTriggerCheck extends BaseBean {
/*  17 */   private int workflowId = -1;
/*  18 */   private int formId = -1;
/*  19 */   private int isBill = -1;
/*  20 */   private String mainTable = "";
/*  21 */   private List<String> detailTables = null;
/*     */ 
/*     */   
/*     */   public RequestFieldTriggerCheck(int paramInt) {
/*  25 */     this.workflowId = paramInt;
/*  26 */     RecordSet recordSet = new RecordSet();
/*  27 */     getWorkflowInfo(recordSet);
/*  28 */     getTableInfo(recordSet);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void execDetailCheck(int paramInt, List<List<String>> paramList, List<String> paramList1, List<String> paramList2, Map<String, Map<String, List<Map<String, Map<String, String>>>>> paramMap) {
/*  37 */     RequestFieldTrigger requestFieldTrigger = new RequestFieldTrigger();
/*  38 */     RecordSet recordSet = new RecordSet();
/*  39 */     String str = SystemEnv.getHtmlLabelName(524198, ThreadVarLanguage.getLang());
/*  40 */     for (String str1 : this.detailTables) {
/*     */       
/*  42 */       HashMap<Object, Object> hashMap = new HashMap<>();
/*     */ 
/*     */       
/*     */       try {
/*  46 */         StringBuilder stringBuilder = new StringBuilder();
/*  47 */         if (this.isBill == 1) {
/*  48 */           stringBuilder.append("select d.* from ").append(this.mainTable).append(" m,");
/*  49 */           stringBuilder.append(str1).append(" d where m.id = d.mainid and m.requestid = ? ");
/*     */         } else {
/*  51 */           stringBuilder.append("select d.* from ");
/*  52 */           stringBuilder.append(str1).append(" d where d.requestid = ? ");
/*     */         } 
/*  54 */         Map<String, String> map = requestFieldTrigger.getFieldNameMap(this.isBill, this.formId, str1);
/*  55 */         HashSet<String> hashSet = new HashSet();
/*  56 */         for (byte b = 0; b < paramList.size(); b++) {
/*     */           
/*  58 */           List<String> list = paramList.get(b);
/*  59 */           for (int i = list.size() - 1; i > -1; i--) {
/*  60 */             String str2 = Util.null2String(list.get(i));
/*  61 */             String str3 = "select e.triggerFieldName,e.triggerName,e.type,e.detailindex from Workflow_DataInput_entry e,Workflow_DataInput_main m where m.entryid = e.id and m.id =? ";
/*  62 */             recordSet.executeQuery(str3, new Object[] { str2 });
/*  63 */             recordSet.next();
/*     */             
/*  65 */             String str4 = Util.null2String(recordSet.getString("triggerName"));
/*  66 */             String str5 = Util.null2String(recordSet.getString("triggerFieldName"));
/*  67 */             String str6 = Util.null2String(recordSet.getString("detailindex"));
/*     */             
/*  69 */             String str7 = String.format("%s(%s%s.%s)", new Object[] { str4, str, str6, str5 });
/*     */             
/*  71 */             paramList1.add(str7);
/*     */             
/*  73 */             if (Strings.isNullOrEmpty(str5) || hashSet.contains(str2));
/*     */ 
/*     */             
/*  76 */             paramList2.add(str7);
/*     */             
/*  78 */             hashSet.add(str2);
/*     */ 
/*     */             
/*  81 */             recordSet.executeQuery(stringBuilder.toString(), new Object[] { Integer.valueOf(paramInt) });
/*     */             
/*  83 */             ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*  84 */             while (recordSet.next()) {
/*     */               
/*  86 */               DynamicDataInput dynamicDataInput = new DynamicDataInput(String.valueOf(this.workflowId), str5, String.valueOf(this.isBill));
/*     */               
/*  88 */               ArrayList<String> arrayList1 = dynamicDataInput.GetInFieldName(str2);
/*  89 */               for (byte b1 = 0; b1 < arrayList1.size(); b1++) {
/*  90 */                 String str8 = arrayList1.get(b1);
/*  91 */                 str8 = str8.replace("field", "");
/*  92 */                 String str9 = map.get(str8);
/*  93 */                 if (!Strings.isNullOrEmpty(str9)) {
/*  94 */                   String str10 = Util.null2String(recordSet.getString(str9));
/*     */                   
/*  96 */                   dynamicDataInput.SetInFields(arrayList1.get(b1), str10);
/*     */                 } 
/*     */               } 
/*  99 */               dynamicDataInput.GetOutData(str2);
/* 100 */               ArrayList<Map> arrayList2 = dynamicDataInput.GetOutDataList();
/*     */               
/* 102 */               HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 103 */               HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 104 */               for (byte b2 = 0; b2 < arrayList2.size(); b2++) {
/*     */                 
/* 106 */                 Map map1 = arrayList2.get(b2);
/* 107 */                 if (map1 != null)
/*     */                 {
/* 109 */                   for (String str8 : map1.keySet()) {
/* 110 */                     String str9 = str8.replace("field", "");
/* 111 */                     String str10 = map.get(str9);
/* 112 */                     if (Strings.isNullOrEmpty(str10))
/* 113 */                       continue;  String str11 = (String)map1.get(str8);
/*     */                     
/* 115 */                     hashMap2.put(str10, Util.null2String(recordSet.getString(str10)));
/* 116 */                     hashMap1.put(str10, str11);
/*     */                   } 
/*     */                 }
/*     */               } 
/*     */               
/* 121 */               HashMap<Object, Object> hashMap3 = new HashMap<>();
/* 122 */               hashMap3.put("oldValue", hashMap2);
/* 123 */               hashMap3.put("newValue", hashMap1);
/*     */               
/* 125 */               arrayList.add(hashMap3);
/*     */             } 
/*     */             
/* 128 */             hashMap.put(str4, arrayList);
/*     */           } 
/*     */         } 
/* 131 */       } catch (Exception exception) {
/* 132 */         writeLog("明细导入字段联动计算异常：" + exception.getMessage());
/*     */       } 
/*     */       
/* 135 */       paramMap.put(str1, hashMap);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void execMainCheck(int paramInt, List<List<String>> paramList, List<String> paramList1, List<String> paramList2, Map<String, List<Map<String, Map<String, String>>>> paramMap) {
/* 149 */     RecordSet recordSet = new RecordSet();
/*     */ 
/*     */     
/*     */     try {
/* 153 */       StringBuilder stringBuilder = new StringBuilder();
/* 154 */       if (this.isBill == 1) {
/* 155 */         stringBuilder.append("select m.* from ").append(this.mainTable).append(" m where m.requestid = ?");
/*     */       } else {
/* 157 */         stringBuilder.append("select m.* from workflow_form m where m.requestid = ? ");
/*     */       } 
/* 159 */       Map<String, String> map = getFieldNameMap(this.isBill, this.formId);
/* 160 */       HashSet<String> hashSet = new HashSet();
/* 161 */       String str = SystemEnv.getHtmlLabelName(517799, ThreadVarLanguage.getLang());
/* 162 */       for (byte b = 0; b < paramList.size(); b++) {
/*     */         
/* 164 */         List<String> list = paramList.get(b);
/* 165 */         for (int i = list.size() - 1; i > -1; i--) {
/* 166 */           String str1 = Util.null2String(list.get(i));
/* 167 */           String str2 = "select e.triggerFieldName,e.triggerName,e.type,e.detailindex from Workflow_DataInput_entry e,Workflow_DataInput_main m where m.entryid = e.id and m.id =? ";
/* 168 */           recordSet.executeQuery(str2, new Object[] { str1 });
/* 169 */           recordSet.next();
/*     */           
/* 171 */           String str3 = Util.null2String(recordSet.getString("triggerName"));
/* 172 */           String str4 = Util.null2String(recordSet.getString("triggerFieldName"));
/*     */           
/* 174 */           String str5 = String.format("%s(%s.%s)", new Object[] { str3, str, str4 });
/*     */           
/* 176 */           paramList1.add(str5);
/*     */           
/* 178 */           if (Strings.isNullOrEmpty(str4) || hashSet.contains(str1));
/*     */ 
/*     */           
/* 181 */           paramList2.add(str5);
/*     */           
/* 183 */           hashSet.add(str1);
/*     */           
/* 185 */           recordSet.executeQuery(stringBuilder.toString(), new Object[] { Integer.valueOf(paramInt) });
/* 186 */           ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 187 */           while (recordSet.next()) {
/*     */             
/* 189 */             DynamicDataInput dynamicDataInput = new DynamicDataInput(String.valueOf(this.workflowId), str4, String.valueOf(this.isBill));
/*     */             
/* 191 */             ArrayList<String> arrayList1 = dynamicDataInput.GetInFieldName(str1);
/* 192 */             for (byte b1 = 0; b1 < arrayList1.size(); b1++) {
/* 193 */               String str6 = arrayList1.get(b1);
/* 194 */               str6 = str6.replace("field", "");
/* 195 */               String str7 = map.get(str6);
/* 196 */               if (!Strings.isNullOrEmpty(str7)) {
/* 197 */                 String str8 = Util.null2String(recordSet.getString(str7));
/*     */                 
/* 199 */                 dynamicDataInput.SetInFields(arrayList1.get(b1), str8);
/*     */               } 
/*     */             } 
/* 202 */             dynamicDataInput.GetOutData(str1);
/* 203 */             ArrayList<Map> arrayList2 = dynamicDataInput.GetOutDataList();
/*     */             
/* 205 */             HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 206 */             HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 207 */             for (byte b2 = 0; b2 < arrayList2.size(); b2++) {
/*     */               
/* 209 */               Map map1 = arrayList2.get(b2);
/* 210 */               if (map1 != null)
/*     */               {
/* 212 */                 for (String str6 : map1.keySet()) {
/* 213 */                   String str7 = str6.replace("field", "");
/* 214 */                   String str8 = map.get(str7);
/* 215 */                   if (Strings.isNullOrEmpty(str8))
/* 216 */                     continue;  String str9 = (String)map1.get(str6);
/*     */                   
/* 218 */                   hashMap2.put(str8, Util.null2String(recordSet.getString(str8)));
/* 219 */                   hashMap1.put(str8, str9);
/*     */                 } 
/*     */               }
/*     */             } 
/*     */             
/* 224 */             HashMap<Object, Object> hashMap3 = new HashMap<>();
/* 225 */             hashMap3.put("oldValue", hashMap2);
/* 226 */             hashMap3.put("newValue", hashMap1);
/*     */             
/* 228 */             arrayList.add(hashMap3);
/*     */           } 
/* 230 */           paramMap.put(str3, arrayList);
/*     */         } 
/*     */       } 
/* 233 */     } catch (Exception exception) {
/* 234 */       writeLog("明细导入字段联动计算异常：" + exception.getMessage());
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void getWorkflowInfo(RecordSet paramRecordSet) {
/* 244 */     if (this.workflowId == -1)
/*     */       return; 
/* 246 */     paramRecordSet.executeQuery("select formid,isbill from workflow_base where id = ? ", new Object[] { Integer.valueOf(this.workflowId) });
/* 247 */     paramRecordSet.next();
/* 248 */     this.formId = Util.getIntValue(Util.null2String(paramRecordSet.getString("formid")));
/* 249 */     this.isBill = Util.getIntValue(Util.null2String(paramRecordSet.getString("isbill")));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void getTableInfo(RecordSet paramRecordSet) {
/* 256 */     if (this.detailTables == null) {
/* 257 */       this.detailTables = new ArrayList<>();
/*     */     }
/* 259 */     if (this.isBill == 1) {
/* 260 */       paramRecordSet.executeQuery("SELECT tablename FROM workflow_bill WHERE id= ?", new Object[] { Integer.valueOf(this.formId) });
/* 261 */       if (paramRecordSet.next()) {
/* 262 */         this.mainTable = Util.null2String(paramRecordSet.getString("tablename"));
/*     */       }
/*     */       
/* 265 */       paramRecordSet.executeQuery("select tablename from Workflow_billdetailtable where billid =?", new Object[] { Integer.valueOf(this.formId) });
/* 266 */       while (paramRecordSet.next()) {
/* 267 */         this.detailTables.add(Util.null2String(paramRecordSet.getString("tablename")));
/*     */       }
/*     */     } else {
/* 270 */       this.detailTables.add("workflow_formdetail");
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   private Map<String, String> getFieldNameMap(int paramInt1, int paramInt2) {
/* 276 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/* 278 */     RecordSet recordSet = new RecordSet();
/* 279 */     if (paramInt1 == 1) {
/* 280 */       String str = "select id,fieldname,fieldhtmltype from workflow_billfield where billid = ? and viewtype = 0";
/* 281 */       recordSet.executeQuery(str, new Object[] { Integer.valueOf(paramInt2) });
/*     */     } else {
/* 283 */       String str = "select dd.id,dd.fieldname,dd.fieldhtmltype from workflow_formfield ff,workflow_formdict dd where ff.formid = ? and ff.isdetail <> 1 and ff.fieldid = dd.id";
/* 284 */       recordSet.executeQuery(str, new Object[] { Integer.valueOf(paramInt2) });
/*     */     } 
/* 286 */     while (recordSet.next()) {
/* 287 */       String str1 = Util.null2String(recordSet.getString("fieldhtmltype"));
/* 288 */       if ("6".equals(str1))
/* 289 */         continue;  String str2 = Util.null2String(recordSet.getString("id"));
/* 290 */       String str3 = Util.null2String(recordSet.getString("fieldname"));
/* 291 */       hashMap.put(str2, str3);
/*     */     } 
/* 293 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/RequestFieldTriggerCheck.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */