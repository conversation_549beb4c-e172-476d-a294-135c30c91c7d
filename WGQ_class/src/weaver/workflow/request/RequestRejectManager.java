/*     */ package weaver.workflow.request;
/*     */ 
/*     */ import com.engine.workflow.biz.freeNode.FreeNodeBiz;
/*     */ import com.engine.workflow.biz.freeNode.RequestFreeNodeFlowBiz;
/*     */ import com.engine.workflow.entity.freeNode.FreeNodeGroupEntity;
/*     */ import com.engine.workflow.util.CollectionUtil;
/*     */ import java.util.ArrayList;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import org.apache.commons.lang.StringUtils;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class RequestRejectManager
/*     */ {
/*     */   private String selectedNodeids;
/*     */   private User user;
/*     */   
/*     */   public ArrayList[] getProcessNodes(int paramInt) {
/*  39 */     RecordSet recordSet = new RecordSet();
/*  40 */     ArrayList<String> arrayList1 = new ArrayList();
/*  41 */     ArrayList<String> arrayList2 = new ArrayList();
/*     */     
/*  43 */     String str = "SELECT DISTINCT  a.nodeid,  b.nodename  FROM  workflow_currentoperator a,  workflow_nodebase_view b  WHERE  a.nodeid = b.id  AND a.requestid = ?  AND a.nodeid IN (  SELECT  c.nodeid AS id  FROM  workflow_flownode c,  workflow_requestbase d  WHERE  c.workflowid = d.workflowid  AND d.requestid = ?  UNION  SELECT  id  FROM  workflow_freenode  WHERE  requestid = ? ) ORDER BY  a.nodeid,  b.nodename";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  72 */     recordSet.executeQuery(str, new Object[] { Integer.valueOf(paramInt), Integer.valueOf(paramInt), Integer.valueOf(paramInt) });
/*  73 */     while (recordSet.next()) {
/*  74 */       arrayList1.add(Util.null2String(recordSet.getString("nodeid")));
/*  75 */       arrayList2.add(Util.null2String(recordSet.getString("nodename")));
/*     */     } 
/*  77 */     ArrayList[] arrayOfArrayList = new ArrayList[2];
/*  78 */     arrayOfArrayList[0] = arrayList1;
/*  79 */     arrayOfArrayList[1] = arrayList2;
/*  80 */     return arrayOfArrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public ArrayList[] getPathWayNodes(int paramInt1, int paramInt2, int paramInt3) {
/*  91 */     ArrayList<String> arrayList1 = new ArrayList();
/*  92 */     ArrayList<String> arrayList2 = new ArrayList();
/*  93 */     ArrayList<String> arrayList3 = new ArrayList();
/*  94 */     WFLinkInfo wFLinkInfo = new WFLinkInfo();
/*  95 */     String str = "";
/*  96 */     RecordSet recordSet = new RecordSet();
/*  97 */     int i = wFLinkInfo.getNodeAttribute(paramInt3);
/*  98 */     if (i == 2) {
/*     */ 
/*     */       
/* 101 */       String str1 = wFLinkInfo.getAllNodeidByNodeid(paramInt1, paramInt3, "");
/*     */ 
/*     */       
/* 104 */       String str2 = getAllPreNode(paramInt3 + "");
/*     */       
/* 106 */       if (!str1.equals("")) {
/*     */         
/* 108 */         ArrayList arrayList = Util.TokenizerString(str1, ",");
/*     */         
/* 110 */         str = "select distinct a.nodeid,b.nodename,b.nodeattribute,c.nodeorder from workflow_currentoperator a,workflow_nodebase b,workflow_flownode c where a.nodeid=b.id and c.nodeid=b.id and a.requestid=" + paramInt2 + " and a.nodeid in(" + StringUtils.join(arrayList, ",") + ")  order by c.nodeorder,a.nodeid,b.nodename";
/*     */         
/* 112 */         recordSet.executeSql(str);
/* 113 */         while (recordSet.next()) {
/* 114 */           arrayList1.add(Util.null2String(recordSet.getString("nodeid")));
/* 115 */           arrayList2.add(Util.null2String(recordSet.getString("nodename")));
/* 116 */           arrayList3.add(Util.null2String(recordSet.getString("nodeattribute")));
/*     */         }
/*     */       
/*     */       } 
/*     */     } else {
/*     */       
/* 122 */       String str1 = "";
/* 123 */       Map<Integer, FreeNodeGroupEntity> map = null;
/* 124 */       if (FreeNodeBiz.isFreeNode(paramInt3)) {
/* 125 */         RequestFreeNodeFlowBiz requestFreeNodeFlowBiz = new RequestFreeNodeFlowBiz(this.user, paramInt2, paramInt3);
/* 126 */         map = requestFreeNodeFlowBiz.getFreeRejectNodeids(1);
/* 127 */         FreeNodeGroupEntity freeNodeGroupEntity = requestFreeNodeFlowBiz.getRootNodeGroup();
/* 128 */         map.put(Integer.valueOf(freeNodeGroupEntity.getFreeStartNodeid()), freeNodeGroupEntity);
/* 129 */         str1 = CollectionUtil.list2String(map.keySet(), ",");
/*     */       } 
/*     */       
/* 132 */       int j = FreeNodeBiz.getExtendNodeId(paramInt2, paramInt3);
/* 133 */       boolean bool = false;
/* 134 */       String str2 = "";
/*     */       
/* 136 */       if (wFLinkInfo.getNodeAttribute(j) == 2) {
/* 137 */         bool = true;
/*     */       } else {
/* 139 */         str2 = getAllPreNode2(j + "", "");
/* 140 */         String str3 = str2;
/* 141 */         if (!"".equals(str3)) {
/* 142 */           str2 = "";
/* 143 */           String[] arrayOfString = str3.split(",");
/* 144 */           for (byte b = 0; b < arrayOfString.length; b++) {
/* 145 */             if (!"".equals(arrayOfString[b]) && paramInt3 != Util.getIntValue(arrayOfString[b])) {
/* 146 */               str2 = str2 + "," + arrayOfString[b];
/*     */             }
/*     */           } 
/* 149 */           if (!"".equals(str2)) str2 = str2.substring(1); 
/*     */         } 
/*     */       } 
/* 152 */       if (FreeNodeBiz.isFreeNode(paramInt3)) {
/* 153 */         if (!"".equals(str2)) str2 = str2 + ","; 
/* 154 */         str2 = str2 + j;
/*     */       } 
/* 156 */       if ("".equals(str2)) str2 = "-1";
/*     */       
/* 158 */       str = "select ";
/* 159 */       if ("oracle".equalsIgnoreCase(recordSet.getDBType())) {
/* 160 */         str = str + " /*+OPTIMIZER_OR_NBEXP(0)*/ ";
/*     */       }
/* 162 */       str = str + "distinct a.nodeid,b.nodename,b.nodeattribute,c.nodeorder from workflow_currentoperator a,workflow_nodebase b,workflow_flownode c where a.nodeid=b.id and c.nodeid=b.id and a.requestid=" + paramInt2;
/*     */ 
/*     */       
/* 165 */       str = str + " and (" + Util.getSubINClause(str2, "a.nodeid", "in") + ")";
/* 166 */       if (!bool) {
/* 167 */         str = str + "and (b.nodeattribute is null or b.nodeattribute!='2') ";
/*     */       }
/* 169 */       str = str + " order by c.nodeorder,a.nodeid,b.nodename";
/* 170 */       recordSet.executeSql(str);
/* 171 */       while (recordSet.next()) {
/* 172 */         String str3 = recordSet.getString("nodeattribute");
/* 173 */         int k = recordSet.getInt("nodeid");
/* 174 */         String str4 = String.valueOf(k);
/* 175 */         if (map != null && map.get(Integer.valueOf(k)) != null) {
/* 176 */           FreeNodeGroupEntity freeNodeGroupEntity = map.get(Integer.valueOf(k));
/* 177 */           List<FreeNodeGroupEntity> list = freeNodeGroupEntity.getParallelNodeGroupDatas();
/* 178 */           if (list.size() > 0 && !parallGroupHasCurrentNode(list, paramInt3)) {
/* 179 */             str4 = k + "_3";
/*     */           }
/*     */         } 
/* 182 */         arrayList1.add(str4);
/* 183 */         arrayList2.add(Util.null2String(recordSet.getString("nodename")));
/* 184 */         arrayList3.add(Util.null2String(recordSet.getString("nodeattribute")));
/*     */       } 
/*     */       
/* 187 */       if (!"".equals(str1)) {
/* 188 */         str = "select distinct id,nodename,nodeorder from workflow_freenode where requestid=" + paramInt2 + " and id in(" + str1 + ") order by nodeorder ";
/* 189 */         recordSet.executeQuery(str, new Object[0]);
/* 190 */         while (recordSet.next()) {
/* 191 */           int k = recordSet.getInt(1);
/* 192 */           FreeNodeGroupEntity freeNodeGroupEntity = map.get(Integer.valueOf(k));
/* 193 */           String str3 = String.valueOf(k);
/* 194 */           if (freeNodeGroupEntity != null) {
/*     */             
/* 196 */             List<FreeNodeGroupEntity> list = freeNodeGroupEntity.getParallelNodeGroupDatas();
/* 197 */             if (list.size() > 0 && !parallGroupHasCurrentNode(list, paramInt3)) {
/* 198 */               str3 = k + "_3";
/*     */             }
/*     */           } 
/* 201 */           arrayList1.add(str3);
/* 202 */           arrayList2.add(recordSet.getString(2));
/* 203 */           arrayList3.add("0");
/*     */         } 
/*     */       } 
/*     */     } 
/* 207 */     ArrayList[] arrayOfArrayList = new ArrayList[3];
/* 208 */     arrayOfArrayList[0] = arrayList1;
/* 209 */     arrayOfArrayList[1] = arrayList2;
/* 210 */     arrayOfArrayList[2] = arrayList3;
/* 211 */     return arrayOfArrayList;
/*     */   }
/*     */   
/*     */   private static boolean parallGroupHasCurrentNode(List<FreeNodeGroupEntity> paramList, int paramInt) {
/* 215 */     for (FreeNodeGroupEntity freeNodeGroupEntity : paramList) {
/* 216 */       if (freeNodeGroupEntity.getNodeid() == paramInt) {
/* 217 */         return true;
/*     */       }
/*     */     } 
/* 220 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getAllPreNode(String paramString) {
/* 228 */     String str = "";
/* 229 */     String[] arrayOfString = new String[3];
/*     */     
/* 231 */     this.selectedNodeids = "";
/* 232 */     byte b = 0;
/*     */     do {
/* 234 */       arrayOfString = getPreNode(paramString);
/* 235 */       str = str + arrayOfString[0] + ",";
/* 236 */       paramString = arrayOfString[0];
/* 237 */       ++b;
/* 238 */     } while (b < 'È' && !"1".equals(arrayOfString[1]));
/* 239 */     if (!str.equals(""))
/* 240 */       str = str.substring(0, str.length() - 1); 
/* 241 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getAllPreNode2(String paramString1, String paramString2) {
/* 250 */     List<String[]> list = null;
/*     */     
/* 252 */     String str = "";
/*     */ 
/*     */     
/* 255 */     list = getPreNodes(paramString1);
/*     */     
/* 257 */     for (byte b = 0; b < list.size(); b++) {
/* 258 */       String[] arrayOfString = list.get(b);
/*     */ 
/*     */       
/* 261 */       if (!"2".equals(arrayOfString[1])) {
/* 262 */         str = str + arrayOfString[0] + ",";
/*     */       }
/* 264 */       paramString1 = arrayOfString[0];
/*     */       
/* 266 */       if (arrayOfString[2] != null && !"1".equals(arrayOfString[2]) && paramString2.indexOf("," + paramString1 + ",") == -1) {
/*     */         
/* 268 */         paramString2 = "," + paramString2 + "," + str + "," + paramString1 + ",";
/* 269 */         str = str + getAllPreNode2(paramString1, paramString2) + ",";
/*     */       } 
/*     */     } 
/*     */     
/* 273 */     if (str != null && !"".equals(str)) {
/* 274 */       str = str.substring(0, str.length() - 1);
/*     */     }
/* 276 */     return str;
/*     */   }
/*     */   
/*     */   private String getAllNodeIds(int paramInt1, int paramInt2) {
/* 280 */     RecordSet recordSet = new RecordSet();
/* 281 */     String str = "";
/* 282 */     recordSet.executeQuery("select * from workflow_flownode where workflowid = ?", new Object[] { Integer.valueOf(paramInt1) });
/* 283 */     while (recordSet.next()) {
/* 284 */       int i = Util.getIntValue(recordSet.getString("nodeid"));
/* 285 */       if (i != paramInt2) {
/* 286 */         str = str + "," + i;
/*     */       }
/*     */     } 
/* 289 */     if (!"".equals(str)) {
/* 290 */       str = str.substring(1);
/*     */     }
/* 292 */     return str;
/*     */   }
/*     */ 
/*     */   
/*     */   private String[] getPreNode(String paramString) {
/* 297 */     RecordSet recordSet = new RecordSet();
/* 298 */     String str = "SELECT nodeid ,  nd.nodeattribute,nd.isstart,destnodeid FROM workflow_nodelink nl ,  workflow_nodebase nd WHERE nd.id = nl.nodeid AND destnodeid = " + paramString + " AND (nl.isreject is null or nl.isreject <> '1') ";
/*     */ 
/*     */     
/* 301 */     if (this.selectedNodeids != null && !this.selectedNodeids.equals("")) {
/* 302 */       str = str + " and nd.id not in (" + this.selectedNodeids + ")";
/*     */     }
/* 304 */     String[] arrayOfString = new String[3];
/* 305 */     recordSet.executeSql(str);
/* 306 */     if (recordSet.next()) {
/* 307 */       if (this.selectedNodeids.equals("")) {
/* 308 */         this.selectedNodeids = recordSet.getString("destnodeid");
/*     */       } else {
/* 310 */         this.selectedNodeids += "," + recordSet.getString("destnodeid");
/*     */       } 
/* 312 */       arrayOfString[0] = recordSet.getString("nodeid");
/* 313 */       arrayOfString[1] = recordSet.getString("nodeattribute");
/* 314 */       arrayOfString[2] = recordSet.getString("isstart");
/*     */     } 
/* 316 */     return arrayOfString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private List getPreNodes(String paramString) {
/* 325 */     String str1 = "SELECT nodeid ,  nd.nodeattribute,nd.isstart FROM workflow_nodelink nl ,  workflow_nodebase nd WHERE nd.id = nl.nodeid AND destnodeid = " + paramString + " AND (nl.isreject is null or nl.isreject <> '1') and nodeid <> " + paramString;
/*     */     
/* 327 */     String str2 = "SELECT nodeid ,  nd.nodeattribute,nd.isstart FROM workflow_nodelink nl ,  workflow_nodebase nd WHERE nd.id = nl.nodeid AND destnodeid = " + paramString + " AND (nl.isreject is null or nl.isreject <> '1') and nodeid <> " + paramString + " and (nd.isfreenode is null or nd.isfreenode<> '1')";
/*     */     
/* 329 */     RecordSet recordSet = new RecordSet();
/* 330 */     ArrayList<String[]> arrayList = new ArrayList();
/* 331 */     if (FreeNodeBiz.isFreeNode(Util.getIntValue(paramString))) {
/* 332 */       recordSet.executeSql(str1);
/*     */     } else {
/* 334 */       recordSet.executeSql(str2);
/*     */     } 
/* 336 */     while (recordSet.next()) {
/* 337 */       String[] arrayOfString = new String[3];
/* 338 */       arrayOfString[0] = recordSet.getString("nodeid");
/* 339 */       arrayOfString[1] = recordSet.getString("nodeattribute");
/* 340 */       arrayOfString[2] = recordSet.getString("isstart");
/*     */       
/* 342 */       arrayList.add(arrayOfString);
/*     */     } 
/* 344 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getNodeToCreate(int paramInt1, int paramInt2, int paramInt3, String paramString) {
/* 356 */     RecordSet recordSet = new RecordSet();
/* 357 */     String str = "select distinct a.nodeid,b.isstart from workflow_nodelink a,workflow_nodebase b where a.wfrequestid is null and (b.IsFreeNode is null or b.IsFreeNode!='1' or (b.IsFreeNode='1' and b.requestid=" + paramInt2 + ")) and a.nodeid=b.id and (b.nodeattribute is null or b.nodeattribute<>'2') and (a.isreject is null or a.isreject<>'1') and a.destnodeid=" + paramInt3 + " and a.workflowid=" + paramInt1;
/*     */     
/* 359 */     if (paramString != null && !paramString.trim().equals("")) {
/* 360 */       str = str + " and a.nodeid not in (" + paramString + ")";
/*     */     }
/* 362 */     recordSet.executeSql(str);
/* 363 */     while (recordSet.next()) {
/* 364 */       int i = recordSet.getInt(1);
/* 365 */       int j = recordSet.getInt(2);
/* 366 */       if (paramString.equals("")) {
/* 367 */         paramString = "" + i;
/*     */       } else {
/* 369 */         paramString = paramString + "," + i;
/*     */       } 
/*     */       
/* 372 */       if (j != 1) paramString = getNodeToCreate(paramInt1, paramInt2, i, paramString); 
/*     */     } 
/* 374 */     return paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getDefaultRejectNode(int paramInt1, int paramInt2) {
/* 384 */     int i = 0;
/* 385 */     char c = Util.getSeparator();
/* 386 */     RecordSet recordSet = new RecordSet();
/* 387 */     ArrayList<String> arrayList1 = new ArrayList();
/* 388 */     ArrayList<String> arrayList2 = new ArrayList();
/* 389 */     int j = 0;
/* 390 */     int k = 0;
/* 391 */     int m = 0;
/* 392 */     String str1 = "";
/*     */     
/* 394 */     String str2 = "select a.formid,a.isbill from workflow_base a,workflow_requestbase b where a.id=b.workflowid and b.requestid=" + paramInt1;
/* 395 */     recordSet.executeSql(str2);
/* 396 */     if (recordSet.next()) {
/* 397 */       j = recordSet.getInt("formid");
/* 398 */       k = recordSet.getInt("isbill");
/*     */     } 
/*     */     
/* 401 */     if (k == 1) {
/*     */       
/* 403 */       str2 = "select tablename from workflow_bill where id=" + j;
/* 404 */       recordSet.executeSql(str2);
/* 405 */       if (recordSet.next()) {
/* 406 */         str1 = recordSet.getString("tablename");
/*     */       }
/*     */       
/* 409 */       str2 = "select billid from workflow_form where requestid=" + paramInt1;
/* 410 */       recordSet.executeSql(str2);
/* 411 */       if (recordSet.next()) {
/* 412 */         m = recordSet.getInt("billid");
/*     */       }
/*     */     } 
/*     */     
/* 416 */     recordSet.executeProc("workflow_NodeLink_Select", paramInt2 + "" + c + "1" + c + "" + paramInt1);
/* 417 */     while (recordSet.next()) {
/* 418 */       arrayList1.add(Util.null2String(recordSet.getString("condition")));
/* 419 */       arrayList2.add(Util.null2String(recordSet.getString("destnodeid")));
/*     */     } 
/*     */     
/* 422 */     for (byte b = 0; b < arrayList2.size(); b++) {
/* 423 */       String str = arrayList1.get(b);
/* 424 */       i = Util.getIntValue(arrayList2.get(b));
/* 425 */       if (str.trim().equals("")) {
/*     */         break;
/*     */       }
/* 428 */       if (k == 1) {
/* 429 */         str2 = "select count(id) from " + str1 + " where id=" + m + " and " + str;
/*     */       } else {
/* 431 */         str2 = "select count(requestid) from workflow_form where requestid=" + paramInt1 + " and " + str;
/* 432 */       }  recordSet.executeSql(str2);
/* 433 */       if (recordSet.next() && recordSet.getInt(1) > 0) {
/*     */         break;
/*     */       }
/*     */     } 
/*     */     
/* 438 */     return i;
/*     */   }
/*     */   
/*     */   public User getUser() {
/* 442 */     return this.user;
/*     */   }
/*     */   
/*     */   public void setUser(User paramUser) {
/* 446 */     this.user = paramUser;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/RequestRejectManager.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */