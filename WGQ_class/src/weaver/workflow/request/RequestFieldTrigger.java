/*     */ package weaver.workflow.request;
/*     */ 
/*     */ import com.google.common.base.Strings;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Hashtable;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import org.jsoup.helper.StringUtil;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.workflow.datainput.DynamicDataInput;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class RequestFieldTrigger
/*     */   extends BaseBean
/*     */ {
/*     */   public void calFieldTrigger(int paramInt1, int paramInt2, int paramInt3, int paramInt4, String paramString1, String paramString2, int paramInt5) {
/*  29 */     if (paramInt2 == -1)
/*     */       return;  try {
/*  31 */       writeLog("[LOG] 0、明细导入执行字段联动requestid：" + paramInt3);
/*  32 */       List<List<String>> list = getSortDataInputIds(paramInt2, 1, paramInt5);
/*  33 */       ArrayList<List<String>> arrayList = new ArrayList();
/*     */       
/*     */       byte b1;
/*     */       int i;
/*  37 */       for (b1 = 0, i = list.size(); b1 < i; b1++) {
/*  38 */         List<String> list1 = list.get(b1);
/*  39 */         if (list1.size() == 1) {
/*  40 */           String str = list1.get(0);
/*  41 */           boolean bool = false; byte b; int j;
/*  42 */           for (b = 0, j = list.size(); b < j; b++) {
/*  43 */             List list2 = list.get(b);
/*  44 */             if (list2.size() > 1 && list2.contains(str)) {
/*  45 */               bool = true;
/*     */               break;
/*     */             } 
/*     */           } 
/*  49 */           if (!bool) arrayList.add(list1); 
/*     */         } else {
/*  51 */           arrayList.add(list1);
/*     */         } 
/*     */       } 
/*  54 */       writeLog("[LOG] 2、去除重复项后sortList:" + arrayList);
/*  55 */       if (arrayList.isEmpty())
/*     */         return; 
/*  57 */       Map<String, String> map = getFieldNameMap(paramInt4, paramInt1, paramString2);
/*     */       
/*  59 */       StringBuilder stringBuilder = new StringBuilder();
/*  60 */       if (paramInt4 == 1) {
/*  61 */         stringBuilder.append("select d.* from ").append(paramString1).append(" m,");
/*  62 */         stringBuilder.append(paramString2).append(" d where m.id = d.mainid and m.requestid = ? ");
/*     */       } else {
/*  64 */         stringBuilder.append("select d.* from ");
/*  65 */         stringBuilder.append(paramString2).append(" d where d.requestid = ? ");
/*     */       } 
/*     */       
/*  68 */       RecordSet recordSet1 = new RecordSet();
/*  69 */       RecordSet recordSet2 = new RecordSet();
/*     */ 
/*     */       
/*  72 */       for (byte b2 = 0; b2 < arrayList.size(); b2++) {
/*     */         
/*  74 */         List<String> list1 = arrayList.get(b2);
/*     */         
/*  76 */         for (int j = list1.size() - 1; j > -1; j--) {
/*     */           
/*  78 */           String str1 = Util.null2String(list1.get(j));
/*  79 */           String str2 = getTriggerFieldName(str1);
/*  80 */           if (Strings.isNullOrEmpty(str2)) {
/*  81 */             writeLog("[LOG] 3、not calculate datainputId :" + str1);
/*     */           
/*     */           }
/*     */           else {
/*     */             
/*  86 */             recordSet1.executeQuery(stringBuilder.toString(), new Object[] { Integer.valueOf(paramInt3) });
/*  87 */             ArrayList<ArrayList<String>> arrayList1 = new ArrayList();
/*  88 */             String str = "update " + paramString2 + " set ";
/*  89 */             boolean bool = true;
/*  90 */             ArrayList<List<Map>> arrayList2 = new ArrayList();
/*  91 */             ArrayList<String> arrayList3 = new ArrayList();
/*  92 */             ArrayList<String> arrayList4 = new ArrayList();
/*  93 */             ArrayList<Map<String, String>> arrayList5 = new ArrayList();
/*  94 */             while (recordSet1.next()) {
/*  95 */               List<Map> list2 = getOutDatas(paramInt2, str2, paramInt4, str1, map, recordSet1);
/*  96 */               ArrayList<String> arrayList6 = new ArrayList();
/*  97 */               if (null != list2 && list2.size() > 0) {
/*  98 */                 for (byte b = 0; b < list2.size(); b++) {
/*  99 */                   Map map1 = list2.get(b);
/* 100 */                   for (Map.Entry entry : map1.entrySet()) {
/* 101 */                     String str4 = map.get(entry.getKey());
/* 102 */                     if (Strings.isNullOrEmpty(str4)) {
/* 103 */                       writeLog("glb--->fieldName is null continue");
/*     */                       continue;
/*     */                     } 
/* 106 */                     String str5 = (String)entry.getValue();
/* 107 */                     if ("".equals(str5)) {
/* 108 */                       str5 = null;
/*     */                     }
/* 110 */                     arrayList6.add(str5);
/* 111 */                     if (bool) {
/* 112 */                       str = str + str4 + " = ?, ";
/*     */                     }
/*     */                   } 
/*     */                 } 
/* 116 */                 bool = false;
/*     */               } 
/* 118 */               String str3 = Util.null2String(recordSet1.getString("id"));
/* 119 */               arrayList6.add(str3);
/* 120 */               arrayList1.add(arrayList6);
/*     */               
/* 122 */               arrayList2.add(list2);
/* 123 */               arrayList3.add(paramString2);
/* 124 */               arrayList4.add(str3);
/* 125 */               arrayList5.add(map);
/*     */             } 
/*     */             
/* 128 */             if (str.lastIndexOf(",") != -1) {
/*     */ 
/*     */               
/* 131 */               str = str.substring(0, str.lastIndexOf(","));
/* 132 */               str = str + " where id = ?";
/* 133 */               writeLog("字段联动批量更新1：" + str + "params:" + arrayList1);
/*     */ 
/*     */               
/* 136 */               int k = appearNumber(str, "?");
/* 137 */               for (byte b = 0; b < arrayList1.size(); b++) {
/* 138 */                 List list2 = arrayList1.get(b);
/* 139 */                 if (list2.size() == 1) {
/* 140 */                   ArrayList<String> arrayList6 = new ArrayList();
/* 141 */                   for (byte b3 = 0; b3 < k - 1; b3++) {
/* 142 */                     arrayList6.add(null);
/*     */                   }
/* 144 */                   arrayList6.add(list2.get(0));
/* 145 */                   arrayList1.set(b, arrayList6);
/*     */                 } 
/*     */               } 
/*     */               
/* 149 */               writeLog("字段联动批量更新2：" + str + "params:" + arrayList1);
/* 150 */               boolean bool1 = recordSet2.executeBatchSql(str, arrayList1);
/* 151 */               if (!bool1)
/* 152 */                 for (byte b3 = 0; b3 < arrayList2.size(); b3++)
/* 153 */                 { List list2 = arrayList2.get(b3);
/* 154 */                   paramString2 = arrayList3.get(b3);
/* 155 */                   String str3 = arrayList4.get(b3);
/* 156 */                   map = arrayList5.get(b3);
/* 157 */                   updateData(list2, paramString2, str3, map, recordSet2); }  
/*     */             } 
/*     */           } 
/*     */         } 
/*     */       } 
/* 162 */     } catch (Exception exception) {
/* 163 */       writeLog("明细导入字段联动计算异常：", exception);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private int appearNumber(String paramString1, String paramString2) {
/* 171 */     byte b = 0;
/* 172 */     int i = 0;
/* 173 */     while ((i = paramString1.indexOf(paramString2, i)) != -1) {
/* 174 */       i += paramString2.length();
/* 175 */       b++;
/*     */     } 
/* 177 */     return b;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private List getOutDatas(int paramInt1, String paramString1, int paramInt2, String paramString2, Map<String, String> paramMap, RecordSet paramRecordSet) {
/* 188 */     DynamicDataInput dynamicDataInput = new DynamicDataInput(String.valueOf(paramInt1), paramString1, String.valueOf(paramInt2));
/*     */     
/* 190 */     ArrayList<String> arrayList1 = dynamicDataInput.GetInFieldName(paramString2);
/* 191 */     for (byte b = 0; b < arrayList1.size(); b++) {
/* 192 */       String str1 = arrayList1.get(b);
/* 193 */       String str2 = paramMap.get(str1);
/* 194 */       if (!Strings.isNullOrEmpty(str2)) {
/* 195 */         String str = Util.null2String(paramRecordSet.getString(str2));
/*     */         
/* 197 */         dynamicDataInput.SetInFields(arrayList1.get(b), str);
/*     */       } 
/* 199 */     }  dynamicDataInput.GetOutData(paramString2);
/* 200 */     ArrayList<String> arrayList2 = dynamicDataInput.GetOutFieldNameList();
/* 201 */     ArrayList arrayList = dynamicDataInput.GetOutDataList();
/* 202 */     if (arrayList.isEmpty()) {
/* 203 */       Hashtable<Object, Object> hashtable = new Hashtable<>();
/* 204 */       for (byte b1 = 0; b1 < arrayList2.size(); b1++) {
/* 205 */         hashtable.put(arrayList2.get(b1), "");
/*     */       }
/* 207 */       ArrayList<Hashtable<Object, Object>> arrayList3 = new ArrayList();
/* 208 */       arrayList3.add(hashtable);
/* 209 */       return arrayList3;
/*     */     } 
/* 211 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void updateData(List<Map> paramList, String paramString1, String paramString2, Map<String, String> paramMap, RecordSet paramRecordSet) {
/* 220 */     if (paramList.isEmpty())
/* 221 */       return;  for (byte b = 0; b < paramList.size(); b++) {
/*     */       
/* 223 */       Map map = paramList.get(b);
/* 224 */       if (map != null && !map.isEmpty()) {
/*     */         
/* 226 */         String str = "update " + paramString1 + " set ";
/* 227 */         ArrayList<ArrayList<String>> arrayList = new ArrayList();
/* 228 */         ArrayList<String> arrayList1 = new ArrayList();
/*     */         
/* 230 */         ArrayList<String> arrayList2 = new ArrayList();
/* 231 */         ArrayList<ArrayList<ArrayList<String>>> arrayList3 = new ArrayList();
/* 232 */         for (String str1 : map.keySet()) {
/*     */           
/* 234 */           String str2 = paramMap.get(str1);
/* 235 */           if (Strings.isNullOrEmpty(str2))
/* 236 */             continue;  String str3 = (String)map.get(str1);
/*     */           
/* 238 */           str = str + str2 + " = ?, ";
/* 239 */           if ("".equalsIgnoreCase(str3)) {
/* 240 */             arrayList1.add(null);
/*     */           } else {
/* 242 */             arrayList1.add(str3);
/*     */           } 
/*     */           
/* 245 */           arrayList2.add("update " + paramString1 + " set " + str2 + " = ? where id =" + paramString2);
/* 246 */           ArrayList<String> arrayList4 = new ArrayList();
/* 247 */           arrayList4.add(str3);
/* 248 */           ArrayList<ArrayList<String>> arrayList5 = new ArrayList();
/* 249 */           arrayList5.add(arrayList4);
/* 250 */           arrayList3.add(arrayList5);
/*     */         } 
/*     */         try {
/* 253 */           arrayList.add(arrayList1);
/* 254 */           str = str.substring(0, str.lastIndexOf(","));
/* 255 */           str = str + " where id =" + paramString2;
/* 256 */           writeLog("updateSql:" + str + ",params:" + arrayList);
/* 257 */           boolean bool = paramRecordSet.executeBatchSql(str, arrayList);
/*     */ 
/*     */           
/* 260 */           if (!bool) {
/* 261 */             byte b1 = 0;
/* 262 */             while (b1 < arrayList2.size()) {
/* 263 */               paramRecordSet.executeBatchSql(arrayList2.get(b1), arrayList3.get(b1));
/* 264 */               writeLog("updateSql:" + (String)arrayList2.get(b1) + ",params:" + arrayList3.get(b1));
/* 265 */               b1++;
/*     */             } 
/*     */           } 
/* 268 */         } catch (Exception exception) {
/* 269 */           writeLog("字段赋值类型不匹配异常：", str);
/*     */         } 
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<List<String>> getSortDataInputIds(int paramInt1, int paramInt2, int paramInt3) {
/* 282 */     RecordSet recordSet = new RecordSet();
/* 283 */     ArrayList<List<String>> arrayList = new ArrayList();
/*     */     
/* 285 */     String str1 = "select id,TRIGGERFIELDNAME from workflow_datainput_entry where workflowId = ? and (enable is null or enable <> 1) and type = ?";
/* 286 */     recordSet.executeQuery(str1, new Object[] { Integer.valueOf(paramInt1), Integer.valueOf(paramInt2) });
/*     */     
/* 288 */     ArrayList<String> arrayList1 = new ArrayList();
/* 289 */     while (recordSet.next()) {
/* 290 */       String str3 = recordSet.getString("id");
/* 291 */       String str4 = recordSet.getString("TRIGGERFIELDNAME");
/* 292 */       String str5 = "select ISEDIT from workflow_nodeform where nodeid =? and FIELDID = ?";
/* 293 */       RecordSet recordSet1 = new RecordSet();
/* 294 */       recordSet1.executeQuery(str5, new Object[] { Integer.valueOf(paramInt3), str4.replace("field", "") });
/* 295 */       if (recordSet1.next() && "1".equals(recordSet1.getString("ISEDIT"))) {
/* 296 */         arrayList1.add(Util.null2String(str3));
/*     */       }
/*     */     } 
/* 299 */     if (arrayList1.isEmpty()) return arrayList; 
/* 300 */     String str2 = StringUtil.join(arrayList1, ",");
/* 301 */     str1 = "select f.id,f.datainputId,f.type,f.pageFieldName from Workflow_DataInput_main m,Workflow_DataInput_field f where f.datainputid = m.id and  " + Util.getSubINClause(str2, "m.entryid", "in");
/* 302 */     recordSet.executeQuery(str1, new Object[0]);
/*     */     
/* 304 */     ArrayList<Field> arrayList2 = new ArrayList();
/* 305 */     ArrayList<Field> arrayList3 = new ArrayList();
/* 306 */     while (recordSet.next()) {
/*     */       
/* 308 */       String str3 = Util.null2String(recordSet.getString("id"));
/* 309 */       String str4 = Util.null2String(recordSet.getString("datainputId"));
/* 310 */       String str5 = Util.null2String(recordSet.getString("type"));
/* 311 */       String str6 = Util.null2String(recordSet.getString("pageFieldName"));
/* 312 */       Field field = new Field(str3, str4, str5, str6);
/* 313 */       if ("1".equals(str5)) {
/* 314 */         arrayList2.add(field); continue;
/*     */       } 
/* 316 */       arrayList3.add(field);
/*     */     } 
/*     */ 
/*     */     
/* 320 */     List<List<String>> list = linkedDataInputId(arrayList2, arrayList3, arrayList, arrayList2, (List<String>)null);
/* 321 */     writeLog("[LOG] 1、字段联动排序后顺序：" + list);
/* 322 */     return list;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private List<List<String>> linkedDataInputId(List<Field> paramList1, List<Field> paramList2, List<List<String>> paramList, List<Field> paramList3, List<String> paramList4) {
/* 334 */     for (Field field : paramList1) {
/* 335 */       if (paramList4 == null) paramList4 = new ArrayList<>(); 
/* 336 */       if (!paramList4.contains(field.getDatainputId())) {
/* 337 */         paramList4.add(field.getDatainputId());
/* 338 */         for (Field field1 : paramList2) {
/*     */           
/* 340 */           if (field1.getPageFieldName().equals(field.getPageFieldName())) {
/* 341 */             String str = field1.getDatainputId();
/* 342 */             if (paramList4.isEmpty()) paramList4.add(field.getDatainputId()); 
/* 343 */             ArrayList<Field> arrayList1 = new ArrayList();
/* 344 */             for (Field field2 : paramList3) {
/* 345 */               if (field2.getDatainputId().equals(str)) arrayList1.add(field2); 
/*     */             } 
/* 347 */             linkedDataInputId(arrayList1, paramList2, paramList, paramList3, paramList4);
/*     */           } 
/*     */         } 
/*     */       } 
/* 351 */       ArrayList<String> arrayList = new ArrayList();
/* 352 */       for (String str : paramList4) arrayList.add(str);
/*     */       
/* 354 */       paramList.add(arrayList);
/* 355 */       paramList4.clear();
/*     */     } 
/*     */     
/* 358 */     return paramList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, String> getFieldNameMap(int paramInt1, int paramInt2, String paramString) {
/* 368 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/* 370 */     RecordSet recordSet = new RecordSet();
/* 371 */     if (paramInt1 == 1) {
/* 372 */       String str = "select id,fieldname,fieldhtmltype from workflow_billfield where detailtable = ?";
/* 373 */       recordSet.executeQuery(str, new Object[] { paramString });
/*     */     } else {
/* 375 */       String str = "select dd.id,dd.fieldname,dd.fieldhtmltype from workflow_formfield ff,workflow_formdictdetail dd where ff.formid = ? and ff.isdetail = 1 and ff.fieldid = dd.id";
/* 376 */       recordSet.executeQuery(str, new Object[] { Integer.valueOf(paramInt2) });
/*     */     } 
/* 378 */     while (recordSet.next()) {
/* 379 */       String str1 = Util.null2String(recordSet.getString("fieldhtmltype"));
/* 380 */       if ("6".equals(str1))
/* 381 */         continue;  String str2 = Util.null2String(recordSet.getString("id"));
/* 382 */       String str3 = Util.null2String(recordSet.getString("fieldname"));
/* 383 */       hashMap.put("field" + str2, str3);
/*     */     } 
/* 385 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getTriggerFieldName(String paramString) {
/* 395 */     String str = "select e.triggerFieldName from Workflow_DataInput_entry e,Workflow_DataInput_main m where m.entryid = e.id and m.id =? ";
/* 396 */     RecordSet recordSet = new RecordSet();
/* 397 */     recordSet.executeQuery(str, new Object[] { paramString });
/* 398 */     recordSet.next();
/* 399 */     return Util.null2String(recordSet.getString("triggerFieldName"));
/*     */   }
/*     */ 
/*     */   
/*     */   public class Field
/*     */   {
/*     */     private String id;
/*     */     
/*     */     private String datainputId;
/*     */     
/*     */     private String type;
/*     */     private String pageFieldName;
/*     */     
/*     */     public Field(String param1String1, String param1String2, String param1String3, String param1String4) {
/* 413 */       this.id = param1String1;
/* 414 */       this.datainputId = param1String2;
/* 415 */       this.type = param1String3;
/* 416 */       this.pageFieldName = param1String4;
/*     */     }
/*     */ 
/*     */     
/*     */     public Field() {}
/*     */     
/*     */     public String getId() {
/* 423 */       return this.id;
/*     */     }
/*     */     
/*     */     public String getDatainputId() {
/* 427 */       return this.datainputId;
/*     */     }
/*     */     
/*     */     public String getType() {
/* 431 */       return this.type;
/*     */     }
/*     */     
/*     */     public String getPageFieldName() {
/* 435 */       return this.pageFieldName;
/*     */     }
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/RequestFieldTrigger.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */