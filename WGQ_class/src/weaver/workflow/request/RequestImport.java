/*      */ package weaver.workflow.request;
/*      */ 
/*      */ import java.util.ArrayList;
/*      */ import java.util.Calendar;
/*      */ import java.util.HashMap;
/*      */ import java.util.Hashtable;
/*      */ import java.util.Iterator;
/*      */ import java.util.Map;
/*      */ import weaver.conn.ConnStatement;
/*      */ import weaver.conn.RecordSet;
/*      */ import weaver.docs.docs.DocManager;
/*      */ import weaver.general.TimeUtil;
/*      */ import weaver.general.Util;
/*      */ import weaver.hrm.resource.ResourceComInfo;
/*      */ import weaver.system.code.CodeBuild;
/*      */ import weaver.workflow.exceldesign.ExcelLayoutManager;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ public class RequestImport
/*      */ {
/*      */   private int imprequestid;
/*      */   private int newworkflowid;
/*      */   private String newworkflowtype;
/*      */   private int newisbill;
/*      */   private int newformid;
/*      */   private int newuserid;
/*      */   private int newusertype;
/*      */   private int newisagent;
/*      */   private int newbeagenter;
/*      */   private int newnodeid;
/*      */   private String newnodetype;
/*      */   private int newmodeid;
/*      */   private int ismode;
/*      */   private String newrequestname;
/*      */   private String newmessageType;
/*      */   private String newrequestlevel;
/*      */   private String newchatsType;
/*      */   
/*      */   public int getNewmodeid() {
/*   51 */     return this.newmodeid;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setNewmodeid(int paramInt) {
/*   59 */     this.newmodeid = paramInt;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public int getIsmode() {
/*   67 */     return this.ismode;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setIsmode(int paramInt) {
/*   75 */     this.ismode = paramInt;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getNewmessageType() {
/*   83 */     return this.newmessageType;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setNewmessageType(String paramString) {
/*   91 */     this.newmessageType = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getNewrequestlevel() {
/*   99 */     return this.newrequestlevel;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setNewrequestlevel(String paramString) {
/*  107 */     this.newrequestlevel = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public int getNewnodeid() {
/*  115 */     return this.newnodeid;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setNewnodeid(int paramInt) {
/*  123 */     this.newnodeid = paramInt;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getNewnodetype() {
/*  131 */     return this.newnodetype;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setNewnodetype(String paramString) {
/*  139 */     this.newnodetype = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public int getImprequestid() {
/*  148 */     return this.imprequestid;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setImprequestid(int paramInt) {
/*  157 */     this.imprequestid = paramInt;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public int getNewworkflowid() {
/*  166 */     return this.newworkflowid;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setNewworkflowid(int paramInt) {
/*  175 */     this.newworkflowid = paramInt;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getNewworkflowtype() {
/*  184 */     return this.newworkflowtype;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setNewworkflowtype(String paramString) {
/*  193 */     this.newworkflowtype = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public int getNewisbill() {
/*  202 */     return this.newisbill;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setNewisbill(int paramInt) {
/*  211 */     this.newisbill = paramInt;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public int getNewformid() {
/*  220 */     return this.newformid;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setNewformid(int paramInt) {
/*  229 */     this.newformid = paramInt;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public int getNewuserid() {
/*  238 */     return this.newuserid;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setNewuserid(int paramInt) {
/*  247 */     this.newuserid = paramInt;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public int getNewusertype() {
/*  256 */     return this.newusertype;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setNewusertype(int paramInt) {
/*  265 */     this.newusertype = paramInt;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public int getNewisagent() {
/*  274 */     return this.newisagent;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setNewisagent(int paramInt) {
/*  283 */     this.newisagent = paramInt;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public int getNewbeagenter() {
/*  292 */     return this.newbeagenter;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setNewbeagenter(int paramInt) {
/*  301 */     this.newbeagenter = paramInt;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getNewrequestname() {
/*  310 */     return this.newrequestname;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setNewrequestname(String paramString) {
/*  319 */     this.newrequestname = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public int Import() {
/*  328 */     int i = 0;
/*  329 */     if (this.imprequestid > 0 && this.newworkflowid > 0 && this.newuserid > 0) {
/*      */       try {
/*  331 */         Calendar calendar = Calendar.getInstance();
/*      */ 
/*      */         
/*  334 */         String str1 = Util.add0(calendar.get(1), 4) + "-" + Util.add0(calendar.get(2) + 1, 2) + "-" + Util.add0(calendar.get(5), 2);
/*      */ 
/*      */ 
/*      */         
/*  338 */         String str2 = Util.add0(calendar.get(11), 2) + ":" + Util.add0(calendar.get(12), 2) + ":" + Util.add0(calendar.get(13), 2);
/*  339 */         String str3 = TimeUtil.getCurrentDateString().substring(0, 4);
/*  340 */         String str4 = TimeUtil.getCurrentDateString().substring(0, 7);
/*  341 */         RecordSet recordSet1 = new RecordSet();
/*  342 */         RecordSet recordSet2 = new RecordSet();
/*  343 */         RecordSet recordSet3 = new RecordSet();
/*  344 */         RequestIdUpdate requestIdUpdate = new RequestIdUpdate();
/*  345 */         ResourceComInfo resourceComInfo = new ResourceComInfo();
/*  346 */         String str5 = "";
/*  347 */         char c = Util.getSeparator();
/*  348 */         int j = -1;
/*  349 */         String str6 = "";
/*  350 */         if (this.newisbill == 1) {
/*  351 */           recordSet1.executeSql("select tablename from workflow_bill where id = " + this.newformid);
/*  352 */           if (recordSet1.next()) {
/*  353 */             str6 = recordSet1.getString("tablename");
/*      */           } else {
/*      */             
/*  356 */             return 0;
/*      */           } 
/*      */         } 
/*      */         
/*  360 */         int[] arrayOfInt = requestIdUpdate.getRequestNewId(str6);
/*  361 */         i = arrayOfInt[0];
/*  362 */         if (this.newisbill == 1) {
/*  363 */           j = arrayOfInt[1];
/*  364 */           if (j == -1) return 0; 
/*      */         } 
/*  366 */         str5 = "insert into workflow_form (requestid,billformid,billid) values(" + i + "," + this.newformid + "," + j + ")";
/*  367 */         recordSet1.executeSql(str5);
/*      */         
/*  369 */         int k = this.newnodeid;
/*  370 */         String str7 = this.newnodetype;
/*  371 */         String str8 = "";
/*  372 */         boolean bool1 = false;
/*  373 */         boolean bool2 = false;
/*  374 */         boolean bool3 = false;
/*  375 */         boolean bool4 = false;
/*  376 */         String str9 = "";
/*  377 */         String str10 = "";
/*  378 */         boolean bool5 = false;
/*  379 */         byte b1 = -1;
/*  380 */         byte b2 = -1;
/*  381 */         boolean bool6 = false;
/*  382 */         boolean bool7 = false;
/*  383 */         boolean bool8 = false;
/*  384 */         int m = this.newuserid;
/*  385 */         int n = this.newusertype;
/*  386 */         String str11 = "";
/*  387 */         String str12 = "";
/*  388 */         String str13 = "";
/*  389 */         String str14 = "";
/*  390 */         String str15 = "";
/*  391 */         if (this.newisagent == 1) {
/*  392 */           if (this.newbeagenter < 1) {
/*  393 */             recordSet1.executeSql("select bagentuid from workflow_agentConditionSet where iscreateagenter=1 and agentuid=" + this.newuserid + " and workflowid=" + this.newworkflowid + " order by agentbatch desc  ,id desc");
/*  394 */             if (recordSet1.next()) {
/*  395 */               m = recordSet1.getInt(1);
/*  396 */               n = 0;
/*      */             } 
/*      */           } else {
/*  399 */             m = this.newbeagenter;
/*  400 */             n = 0;
/*      */           } 
/*      */         }
/*  403 */         recordSet1.executeSql("select * from workflow_Requestbase where requestid=" + this.imprequestid);
/*  404 */         if (recordSet1.next()) {
/*  405 */           str11 = Util.null2String(recordSet1.getString("docids"));
/*  406 */           str12 = Util.null2String(recordSet1.getString("crmids"));
/*  407 */           str13 = Util.null2String(recordSet1.getString("hrmids"));
/*  408 */           str14 = Util.null2String(recordSet1.getString("prjids"));
/*  409 */           str15 = Util.null2String(recordSet1.getString("cptids"));
/*      */         } 
/*      */ 
/*      */         
/*  413 */         String str16 = "";
/*  414 */         if (recordSet1.getDBType().equals("oracle")) {
/*  415 */           str16 = "" + i + c + this.newworkflowid + c + k + c + str7 + c + this.newnodeid + c + this.newnodetype + c + str8 + c + bool1 + c + bool2 + c + this.newrequestname + c + m + c + str1 + c + str2 + c + bool3 + c + str9 + c + str10 + c + bool5 + c + n + c + bool4 + c + b1 + c + b2 + c + str11 + c + str12 + c + "''" + c + str14 + c + str15 + c + this.newmessageType + c + this.newchatsType;
/*      */ 
/*      */         
/*      */         }
/*      */         else {
/*      */ 
/*      */           
/*  422 */           str16 = "" + i + c + this.newworkflowid + c + k + c + str7 + c + this.newnodeid + c + this.newnodetype + c + str8 + c + bool1 + c + bool2 + c + this.newrequestname + c + m + c + str1 + c + str2 + c + bool3 + c + str9 + c + str10 + c + bool5 + c + n + c + bool4 + c + b1 + c + b2 + c + str11 + c + str12 + c + str13 + c + str14 + c + str15 + c + this.newmessageType + c + this.newchatsType;
/*      */         } 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */         
/*  429 */         recordSet1.executeProc("workflow_requestbase_insertnew", str16);
/*  430 */         recordSet1.executeProc("workflow_Rbase_UpdateLevel", "" + i + c + this.newrequestlevel);
/*      */ 
/*      */         
/*  433 */         if (recordSet1.getDBType().equals("oracle")) {
/*  434 */           ConnStatement connStatement = null;
/*      */           
/*      */           try {
/*  437 */             String str = "update workflow_requestbase set hrmids = ? where requestid = " + i;
/*  438 */             connStatement = new ConnStatement();
/*  439 */             connStatement.setStatementSql(str);
/*  440 */             connStatement.setString(1, str13);
/*  441 */             connStatement.executeUpdate();
/*  442 */           } catch (Exception exception) {
/*  443 */             recordSet1.writeLog(exception);
/*  444 */             exception.printStackTrace();
/*      */           } finally {
/*  446 */             if (connStatement != null) connStatement.close();
/*      */           
/*      */           } 
/*      */         } 
/*      */         
/*  451 */         if (this.newisagent == 1 && this.newbeagenter > 0) {
/*  452 */           str16 = "" + i + c + this.newuserid + c + bool7 + c + this.newworkflowid + c + this.newworkflowtype + c + n + c + bool6 + c + k + c + this.newbeagenter + c + "2" + c + -1 + c + bool8;
/*      */         }
/*      */         else {
/*      */           
/*  456 */           str16 = "" + i + c + this.newuserid + c + bool7 + c + this.newworkflowid + c + this.newworkflowtype + c + n + c + bool6 + c + k + c + -1 + c + "0" + c + -1 + c + bool8;
/*      */         } 
/*      */ 
/*      */         
/*  460 */         recordSet1.executeProc("workflow_CurrentOperator_I", str16);
/*  461 */         recordSet1.executeSql("insert into workflow_nownode(requestid,nownodeid,nownodetype,nownodeattribute) values(" + i + "," + k + ",0,0)");
/*  462 */         str5 = "select importReadOnlyField, fieldNotImport from workflow_base where id=" + this.newworkflowid;
/*  463 */         String str17 = "";
/*  464 */         String str18 = "";
/*  465 */         recordSet1.executeSql(str5);
/*  466 */         if (recordSet1.next()) {
/*  467 */           str17 = Util.null2String(recordSet1.getString("importReadOnlyField"));
/*  468 */           str18 = Util.null2String(recordSet1.getString("fieldNotImport"));
/*      */         } 
/*  470 */         ArrayList arrayList1 = new ArrayList();
/*  471 */         if (!"".equals(str18)) {
/*  472 */           arrayList1 = Util.TokenizerString(str18, ",");
/*      */         }
/*      */         
/*  475 */         ArrayList arrayList2 = new ArrayList();
/*  476 */         arrayList2.addAll((new ExcelLayoutManager()).getNodeFormulaAssignFields(this.newworkflowid, k));
/*      */         
/*  478 */         String str19 = "";
/*  479 */         if (this.newisbill == 1) {
/*  480 */           recordSet2.isReturnDecryptData(true);
/*  481 */           recordSet2.executeQuery("select * from " + str6 + " where requestid=" + this.imprequestid, new Object[0]);
/*  482 */           recordSet2.next();
/*  483 */           if (this.newmodeid > 0 && this.ismode == 1) {
/*  484 */             str5 = "select a.id,a.fieldname,a.fielddbtype,a.fieldhtmltype,a.type,b.isedit from workflow_billfield a,workflow_modeview b where a.billid=b.formid and b.isbill='1' and b.nodeid=" + this.newnodeid + " and a.id=b.fieldid and b.isview='1' and (a.viewtype is null or a.viewtype<>1) and a.billid=" + this.newformid;
/*      */           } else {
/*  486 */             str5 = "select a.id,a.fieldname,a.fielddbtype,a.fieldhtmltype,a.type,b.isedit from workflow_billfield a,workflow_nodeform b where b.nodeid=" + this.newnodeid + " and a.id=b.fieldid and b.isview='1' and (a.viewtype is null or a.viewtype<>1) and a.billid=" + this.newformid;
/*      */           } 
/*      */ 
/*      */ 
/*      */           
/*  491 */           str5 = str5 + " order by a.dsporder";
/*      */         } else {
/*      */           
/*  494 */           recordSet2.executeSql("select * from workflow_form where requestid=" + this.imprequestid);
/*  495 */           recordSet2.next();
/*  496 */           if (this.newmodeid > 0 && this.ismode == 1) {
/*  497 */             str5 = "select a.id,a.fieldname,a.fielddbtype,a.fieldhtmltype,a.type,b.isedit from workflow_formdict a,workflow_formfield c,workflow_modeview b where a.id=c.fieldid and (c.isdetail<>'1' or c.isdetail is null) and b.isbill='0' and b.nodeid=" + this.newnodeid + " and a.id=b.fieldid and b.isview='1' and c.formid=" + this.newformid;
/*      */           } else {
/*  499 */             str5 = "select a.id,a.fieldname,a.fielddbtype,a.fieldhtmltype,a.type,b.isedit from workflow_formdict a,workflow_formfield c,workflow_nodeform b where a.id=c.fieldid and (c.isdetail<>'1' or c.isdetail is null) and b.nodeid=" + this.newnodeid + " and a.id=b.fieldid and b.isview='1' and c.formid=" + this.newformid;
/*      */           } 
/*      */ 
/*      */ 
/*      */           
/*  504 */           str5 = str5 + " order by c.fieldorder";
/*      */         } 
/*      */ 
/*      */         
/*  508 */         recordSet1.executeSql(str5);
/*      */         
/*  510 */         ArrayList<String> arrayList3 = new ArrayList();
/*      */         
/*  512 */         HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  513 */         HashMap<Object, Object> hashMap2 = new HashMap<>();
/*  514 */         HashMap<Object, Object> hashMap3 = new HashMap<>();
/*      */         
/*  516 */         ArrayList arrayList4 = new ArrayList();
/*  517 */         ArrayList arrayList5 = new ArrayList();
/*  518 */         boolean bool9 = false;
/*  519 */         RequestPreAddinoperateManager requestPreAddinoperateManager = new RequestPreAddinoperateManager();
/*  520 */         requestPreAddinoperateManager.setCreater(this.newuserid);
/*  521 */         requestPreAddinoperateManager.setOptor(this.newuserid);
/*  522 */         requestPreAddinoperateManager.setWorkflowid(this.newworkflowid);
/*  523 */         requestPreAddinoperateManager.setNodeid(this.newnodeid);
/*  524 */         Hashtable hashtable = requestPreAddinoperateManager.getPreAddRule();
/*  525 */         arrayList4 = (ArrayList)hashtable.get("inoperatefields");
/*  526 */         arrayList5 = (ArrayList)hashtable.get("inoperatevalues");
/*  527 */         String str20 = "0";
/*  528 */         recordSet3.executeQuery("select value from workflow_config where type = 'form' and name = 'isImportBrowserField'", new Object[0]);
/*  529 */         if (recordSet3.next()) {
/*  530 */           str20 = recordSet3.getString("value");
/*      */         }
/*  532 */         while (recordSet1.next()) {
/*  533 */           String str23 = Util.null2String(recordSet1.getString("fieldname"));
/*  534 */           if (arrayList3.indexOf(str23) != -1) {
/*      */             continue;
/*      */           }
/*  537 */           arrayList3.add(str23);
/*  538 */           String str24 = Util.getIntValues(recordSet1.getString("id"));
/*  539 */           int i1 = Util.getIntValue(recordSet1.getString("fieldhtmltype"));
/*  540 */           String str25 = Util.null2String(recordSet1.getString("fielddbtype"));
/*  541 */           int i2 = Util.getIntValue(recordSet1.getString("type"));
/*  542 */           int i3 = Util.getIntValue(recordSet1.getString("isedit"));
/*  543 */           String str26 = recordSet2.getString(str23);
/*  544 */           if (i1 == 9) {
/*      */             continue;
/*      */           }
/*  547 */           if (2 == i1 && i2 == 2) {
/*  548 */             str26 = Util.null2String(str26);
/*  549 */             str26 = str26.replaceAll("'{10,}", "&#39;");
/*  550 */             str26 = str26.replaceAll("(&#39;){10,}", "&#39;");
/*      */           } else {
/*      */             
/*  553 */             str26 = Util.StringReplace(str26, "'", "''");
/*      */           } 
/*      */           
/*  556 */           int i4 = arrayList4.indexOf(str24);
/*  557 */           if (i4 > -1 && ((i3 != 1 && !"1".equals(str17)) || (i3 == 1 && arrayList1.contains(str24)))) {
/*  558 */             String str = Util.null2String(arrayList5.get(i4));
/*  559 */             if ((i1 == 3 && (i2 == 1 || i2 == 165)) || str25.toLowerCase().indexOf("int") > -1 || str25.toLowerCase().indexOf("number") > -1 || str25.toLowerCase().indexOf("decimal") > -1) {
/*  560 */               if (str == null || str.trim().equals("")) str = "NULL"; 
/*  561 */               str19 = str19 + "," + str23 + "=" + str; continue;
/*  562 */             }  if (recordSet1.getDBType().equals("oracle") && i1 == 3 && i2 == 17) {
/*  563 */               str19 = str19 + "," + str23 + "= '' ";
/*  564 */               hashMap2.put(str23, str); continue;
/*      */             } 
/*  566 */             str19 = str19 + "," + str23 + "='" + str + "'";
/*      */             
/*      */             continue;
/*      */           } 
/*  570 */           if (arrayList2.contains(str24) && (i1 == 1 || i1 == 5)) {
/*  571 */             i3 = 1;
/*      */           }
/*  573 */           if (i3 != 1 && i1 == 3 && (!"1".equals(str17) || !"1".equals(str20))) {
/*  574 */             if (i2 == 1 || i2 == 165) {
/*  575 */               str19 = str19 + "," + str23 + "=" + this.newuserid; continue;
/*  576 */             }  if (i2 == 17 || i2 == 166) {
/*  577 */               if (recordSet1.getDBType().equals("oracle") && i2 == 17) {
/*  578 */                 str19 = str19 + "," + str23 + "= '' ";
/*  579 */                 hashMap2.put(str23, Integer.valueOf(this.newuserid)); continue;
/*      */               } 
/*  581 */               str19 = str19 + "," + str23 + "='" + this.newuserid + "'"; continue;
/*      */             } 
/*  583 */             if (i2 == 2) {
/*  584 */               str19 = str19 + "," + str23 + "='" + str1 + "'"; continue;
/*  585 */             }  if (i2 == 19) {
/*  586 */               str19 = str19 + "," + str23 + "='" + str2.substring(0, 5) + "'"; continue;
/*  587 */             }  if (i2 == 290) {
/*  588 */               String str = str1 + " " + str2.substring(0, 5);
/*  589 */               str19 = str19 + "," + str23 + "='" + str + "'"; continue;
/*  590 */             }  if (i2 == 402) {
/*  591 */               str19 = str19 + "," + str23 + "='" + str3 + "'"; continue;
/*  592 */             }  if (i2 == 403) {
/*  593 */               str19 = str19 + "," + str23 + "='" + str4 + "'"; continue;
/*  594 */             }  if (i2 == 4 || i2 == 167) {
/*  595 */               str19 = str19 + "," + str23 + "=" + resourceComInfo.getDepartmentID("" + this.newuserid); continue;
/*  596 */             }  if (i2 == 57 || i2 == 168) {
/*  597 */               str19 = str19 + "," + str23 + "='" + resourceComInfo.getDepartmentID("" + this.newuserid) + "'"; continue;
/*  598 */             }  if (i2 == 42 || i2 == 164 || i2 == 169) {
/*  599 */               str19 = str19 + "," + str23 + "=" + resourceComInfo.getSubCompanyID("" + this.newuserid); continue;
/*  600 */             }  if (i2 == 170 || i2 == 194) {
/*  601 */               str19 = str19 + "," + str23 + "='" + resourceComInfo.getSubCompanyID("" + this.newuserid) + "'"; continue;
/*  602 */             }  if (i2 == 24 || i2 == 278) {
/*  603 */               str19 = str19 + "," + str23 + "='" + resourceComInfo.getJobTitle("" + this.newuserid) + "'"; continue;
/*      */             } 
/*  605 */             if (!arrayList1.contains(str24) && "1".equals(str17)) {
/*  606 */               if (!str25.toLowerCase().contains("browser") && (str25.toLowerCase().indexOf("int") > -1 || str25.toLowerCase().indexOf("number") > -1 || str25.toLowerCase().indexOf("decimal") > -1)) {
/*  607 */                 if (str26 == null || str26.trim().equals("")) str26 = "NULL"; 
/*  608 */                 str19 = str19 + "," + str23 + "=" + str26; continue;
/*      */               } 
/*  610 */               str19 = str19 + "," + str23 + "='" + str26 + "'";
/*      */             } 
/*      */             continue;
/*      */           } 
/*  614 */           if (i3 == 1 || (i3 != 1 && "1".equals(str17))) {
/*  615 */             String str = Util.null2String(recordSet1.getString("id"));
/*  616 */             if (!"".equals(str) && arrayList1.contains(str)) {
/*  617 */               if (i1 == 3) {
/*  618 */                 if (i2 == 1 || i2 == 165) {
/*  619 */                   str19 = str19 + "," + str23 + "=" + this.newuserid; continue;
/*  620 */                 }  if (i2 == 17 || i2 == 166) {
/*  621 */                   if (recordSet1.getDBType().equals("oracle") && i2 == 17) {
/*  622 */                     str19 = str19 + "," + str23 + "= '' ";
/*  623 */                     hashMap2.put(str23, Integer.valueOf(this.newuserid)); continue;
/*      */                   } 
/*  625 */                   str19 = str19 + "," + str23 + "='" + this.newuserid + "'"; continue;
/*      */                 } 
/*  627 */                 if (i2 == 2) {
/*  628 */                   str19 = str19 + "," + str23 + "='" + str1 + "'"; continue;
/*  629 */                 }  if (i2 == 19) {
/*  630 */                   str19 = str19 + "," + str23 + "='" + str2.substring(0, 5) + "'"; continue;
/*  631 */                 }  if (i2 == 290) {
/*  632 */                   String str27 = str1 + " " + str2.substring(0, 5);
/*  633 */                   str19 = str19 + "," + str23 + "='" + str27 + "'"; continue;
/*  634 */                 }  if (i2 == 402) {
/*  635 */                   str19 = str19 + "," + str23 + "='" + str3 + "'"; continue;
/*  636 */                 }  if (i2 == 403) {
/*  637 */                   str19 = str19 + "," + str23 + "='" + str4 + "'"; continue;
/*  638 */                 }  if (i2 == 4 || i2 == 167) {
/*  639 */                   str19 = str19 + "," + str23 + "=" + resourceComInfo.getDepartmentID("" + this.newuserid); continue;
/*  640 */                 }  if (i2 == 57 || i2 == 168) {
/*  641 */                   str19 = str19 + "," + str23 + "='" + resourceComInfo.getDepartmentID("" + this.newuserid) + "'"; continue;
/*  642 */                 }  if (i2 == 42 || i2 == 164 || i2 == 169) {
/*  643 */                   str19 = str19 + "," + str23 + "=" + resourceComInfo.getSubCompanyID("" + this.newuserid); continue;
/*  644 */                 }  if (i2 == 170 || i2 == 194) {
/*  645 */                   str19 = str19 + "," + str23 + "='" + resourceComInfo.getSubCompanyID("" + this.newuserid) + "'"; continue;
/*  646 */                 }  if (i2 == 24 || i2 == 278) {
/*  647 */                   str19 = str19 + "," + str23 + "='" + resourceComInfo.getJobTitle("" + this.newuserid) + "'";
/*      */                 }
/*      */               } 
/*      */ 
/*      */ 
/*      */ 
/*      */               
/*      */               continue;
/*      */             } 
/*      */ 
/*      */ 
/*      */ 
/*      */             
/*  660 */             if (!str25.toLowerCase().contains("browser") && (str25.toLowerCase().indexOf("int") > -1 || str25.toLowerCase().indexOf("number") > -1 || str25.toLowerCase().indexOf("decimal") > -1)) {
/*  661 */               if (str26 == null || str26.trim().equals("")) str26 = "NULL"; 
/*  662 */               str19 = str19 + "," + str23 + "=" + str26; continue;
/*      */             } 
/*  664 */             if (i1 == 6) {
/*  665 */               String str27 = "";
/*  666 */               str26 = Util.null2String(str26).trim();
/*  667 */               if (!"".equals(str26)) {
/*  668 */                 String[] arrayOfString = str26.split(",");
/*  669 */                 if (arrayOfString != null && arrayOfString.length > 0) {
/*  670 */                   for (byte b = 0; b < arrayOfString.length; b++) {
/*  671 */                     int i5 = Util.getIntValue(arrayOfString[b], 0);
/*  672 */                     if (i5 > 0) {
/*  673 */                       String str28 = "";
/*  674 */                       int i6 = 0;
/*  675 */                       int i7 = 0;
/*  676 */                       int i8 = 0;
/*  677 */                       RecordSet recordSet = new RecordSet();
/*      */                       
/*  679 */                       recordSet.executeSql("select a.docSubject,a.secCategory as secCategoryId,a.subcategory as subCategoryId,a.maincategory as mainCategoryId from DocDetail a where a.id =" + i5);
/*  680 */                       if (recordSet.next()) {
/*  681 */                         str28 = Util.null2String(recordSet.getString("docSubject"));
/*  682 */                         i6 = Util.getIntValue(recordSet.getString("secCategoryId"), 0);
/*  683 */                         i7 = Util.getIntValue(recordSet.getString("subCategoryId"), 0);
/*  684 */                         i8 = Util.getIntValue(recordSet.getString("mainCategoryId"), 0);
/*      */                       } 
/*  686 */                       String str29 = "" + (n + 1);
/*  687 */                       DocManager docManager = new DocManager();
/*  688 */                       docManager.setId(i5);
/*  689 */                       docManager.setUserid(m);
/*  690 */                       docManager.setUsertype(str29);
/*  691 */                       docManager.setDocsubject(str28);
/*  692 */                       docManager.setMaincategory(i8);
/*  693 */                       docManager.setSubcategory(i7);
/*  694 */                       docManager.setSeccategory(i6);
/*  695 */                       docManager.copyDocForNoRightAndNoChangeStatus();
/*  696 */                       int i9 = docManager.getId();
/*  697 */                       str27 = str27 + i9 + ",";
/*      */                     } 
/*      */                   } 
/*      */                 }
/*      */               } 
/*  702 */               if (!str27.equals("")) {
/*  703 */                 str26 = str27.substring(0, str27.length() - 1);
/*      */               }
/*      */             } 
/*      */             
/*  707 */             if (i1 == 2 && i2 == 2) {
/*  708 */               hashMap1.put(str23, str26); continue;
/*      */             } 
/*  710 */             if (recordSet1.getDBType().equals("oracle") && i2 == 17 && Util.null2String(recordSet1.getOrgindbtype()).equals("oracle")) {
/*  711 */               str19 = str19 + "," + str23 + "= empty_clob() ";
/*  712 */               hashMap2.put(str23, str26); continue;
/*      */             } 
/*  714 */             str19 = str19 + "," + str23 + "='" + str26 + "'";
/*      */           } 
/*      */         } 
/*      */ 
/*      */ 
/*      */ 
/*      */         
/*  721 */         arrayList3 = null;
/*      */         
/*  723 */         if (!str19.equals("")) {
/*  724 */           str19 = str19.substring(1);
/*      */           
/*  726 */           if (this.newisbill == 1) {
/*  727 */             str5 = "update " + str6 + " set " + str19 + " where requestid=" + i;
/*      */           } else {
/*  729 */             str5 = "update workflow_form set " + str19 + " where requestid=" + i;
/*      */           } 
/*      */ 
/*      */           
/*  733 */           recordSet1.executeSql(str5);
/*      */           
/*  735 */           if (recordSet1.getDBType().equals("oracle")) {
/*      */             try {
/*  737 */               String str23 = "";
/*  738 */               if (this.newisbill == 1) {
/*  739 */                 str23 = " update " + str6 + " set";
/*      */               } else {
/*  741 */                 str23 = "update workflow_form set ";
/*      */               } 
/*  743 */               byte b = 0;
/*  744 */               String str24 = " ";
/*  745 */               Iterator<Map.Entry> iterator = hashMap2.entrySet().iterator();
/*  746 */               while (iterator.hasNext()) {
/*  747 */                 b++;
/*  748 */                 Map.Entry entry = iterator.next();
/*  749 */                 String str25 = entry.getKey().toString();
/*  750 */                 String str26 = "";
/*  751 */                 if (entry.getValue() != null)
/*  752 */                   if (String.valueOf(entry.getValue()).equals(" ")) { str26 = ""; }
/*  753 */                   else { str26 = String.valueOf(entry.getValue()); }
/*      */                    
/*  755 */                 if (b > 1) {
/*  756 */                   str23 = str23 + str24 + " , " + str25 + "=? "; continue;
/*      */                 } 
/*  758 */                 str23 = str23 + str24 + " " + str25 + "=? ";
/*      */               } 
/*      */               
/*  761 */               if (this.newisbill == 1) {
/*  762 */                 str23 = str23 + " where requestid = " + i;
/*      */               } else {
/*  764 */                 str23 = str23 + " where requestid=" + i;
/*      */               } 
/*      */               
/*  767 */               if (b > 0) {
/*  768 */                 ConnStatement connStatement = null;
/*      */                 try {
/*  770 */                   connStatement = new ConnStatement();
/*  771 */                   connStatement.setStatementSql(str23);
/*  772 */                   iterator = hashMap2.entrySet().iterator();
/*  773 */                   b = 0;
/*  774 */                   while (iterator.hasNext()) {
/*  775 */                     b++;
/*  776 */                     Map.Entry entry = iterator.next();
/*  777 */                     String str25 = entry.getKey().toString();
/*  778 */                     String str26 = "";
/*  779 */                     if (entry.getValue() != null)
/*  780 */                       if (String.valueOf(entry.getValue()).equals(" ")) { str26 = ""; }
/*  781 */                       else { str26 = String.valueOf(entry.getValue()); }
/*      */                        
/*  783 */                     connStatement.setString(b, str26);
/*      */                   } 
/*  785 */                   connStatement.executeUpdate();
/*  786 */                 } catch (Exception exception) {
/*  787 */                   recordSet1.writeLog(exception);
/*  788 */                   exception.printStackTrace();
/*      */                 } finally {
/*  790 */                   if (connStatement != null) connStatement.close(); 
/*      */                 } 
/*      */               } 
/*  793 */             } catch (Exception exception) {
/*  794 */               recordSet1.writeLog(exception);
/*  795 */               exception.printStackTrace();
/*      */             } 
/*      */           }
/*      */         } 
/*      */ 
/*      */ 
/*      */ 
/*      */         
/*      */         try {
/*  804 */           String str23 = "";
/*  805 */           byte b = 0;
/*  806 */           if (this.newisbill == 1) {
/*  807 */             str23 = " update " + str6 + " set ";
/*      */           } else {
/*  809 */             str23 = "update workflow_form set ";
/*      */           } 
/*  811 */           String str24 = "";
/*  812 */           Iterator<Map.Entry> iterator = hashMap1.entrySet().iterator();
/*  813 */           while (iterator.hasNext()) {
/*  814 */             b++;
/*  815 */             Map.Entry entry = iterator.next();
/*  816 */             String str25 = entry.getKey().toString();
/*  817 */             String str26 = "";
/*  818 */             if (entry.getValue() != null)
/*  819 */               if (String.valueOf(entry.getValue()).equals(" ")) { str26 = ""; }
/*  820 */               else { str26 = String.valueOf(entry.getValue()); }
/*      */                
/*  822 */             str23 = str23 + str24 + " " + str25 + "=? ";
/*  823 */             str24 = ",";
/*      */           } 
/*  825 */           if (this.newisbill == 1) {
/*  826 */             str23 = str23 + " where requestid = " + i;
/*      */           } else {
/*  828 */             str23 = str23 + " where requestid=" + i;
/*      */           } 
/*      */           
/*  831 */           if (b > 0) {
/*  832 */             ConnStatement connStatement = null;
/*      */             try {
/*  834 */               connStatement = new ConnStatement();
/*  835 */               connStatement.setStatementSql(str23);
/*  836 */               iterator = hashMap1.entrySet().iterator();
/*  837 */               b = 0;
/*  838 */               while (iterator.hasNext()) {
/*  839 */                 b++;
/*  840 */                 Map.Entry entry = iterator.next();
/*  841 */                 String str25 = entry.getKey().toString();
/*  842 */                 String str26 = "";
/*  843 */                 if (entry.getValue() != null)
/*  844 */                   if (String.valueOf(entry.getValue()).equals(" ")) { str26 = ""; }
/*  845 */                   else { str26 = String.valueOf(entry.getValue()); }
/*      */                    
/*  847 */                 connStatement.setString(b, str26);
/*      */               } 
/*  849 */               connStatement.executeUpdate();
/*  850 */             } catch (Exception exception) {
/*  851 */               recordSet1.writeLog(exception);
/*  852 */               exception.printStackTrace();
/*      */             } finally {
/*  854 */               if (connStatement != null) connStatement.close(); 
/*      */             } 
/*      */           } 
/*  857 */         } catch (Exception exception) {
/*  858 */           recordSet1.writeLog(exception);
/*  859 */           exception.printStackTrace();
/*      */         } 
/*      */         
/*  862 */         String str21 = "";
/*  863 */         String str22 = "";
/*  864 */         ArrayList<String> arrayList6 = new ArrayList();
/*  865 */         if (this.newisbill == 1) {
/*      */           
/*  867 */           recordSet1.executeSql(" select detailkeyfield,detailtablename from Workflow_bill where id=" + this.newformid);
/*  868 */           if (recordSet1.next()) {
/*  869 */             str21 = recordSet1.getString("detailkeyfield");
/*  870 */             str22 = Util.null2String(recordSet1.getString("detailtablename"));
/*  871 */             if (str21 == null || str21.trim().equals("")) {
/*  872 */               str21 = "mainid";
/*      */             }
/*  874 */             if (!str22.trim().equals("")) {
/*  875 */               arrayList6.add(str22);
/*      */             }
/*      */           } 
/*      */           
/*  879 */           recordSet1.executeSql("select detailtable from workflow_billfield where billid=" + this.newformid + " and  viewtype=1 group by detailtable");
/*  880 */           while (recordSet1.next()) {
/*  881 */             String str = Util.null2String(recordSet1.getString("detailtable"));
/*  882 */             if (!str.trim().equals("") && arrayList6.indexOf(str) < 0) {
/*  883 */               arrayList6.add(str);
/*      */             }
/*      */           } 
/*      */         } else {
/*  887 */           recordSet1.execute("select distinct groupId from workflow_formfield where formid=" + this.newformid + " and isdetail='1' order by groupId");
/*  888 */           while (recordSet1.next()) {
/*  889 */             arrayList6.add(recordSet1.getString(1));
/*      */           }
/*      */         } 
/*  892 */         if (arrayList6.size() > 0) {
/*  893 */           String str = getRowCalCulateStr(this.newformid);
/*  894 */           if (this.newisbill == 1) {
/*  895 */             for (byte b = 0; b < arrayList6.size(); b++) {
/*  896 */               HashMap<Object, Object> hashMap = new HashMap<>();
/*  897 */               String str23 = str21;
/*  898 */               String str24 = "" + j;
/*  899 */               String str25 = arrayList6.get(b);
/*  900 */               if (this.newmodeid > 0 && this.ismode == 1) {
/*  901 */                 str5 = "select a.id,a.fieldname,a.fielddbtype,a.fieldhtmltype,a.type,b.isedit from workflow_billfield a,workflow_modeview b where a.billid=b.formid and b.isbill='1' and b.nodeid=" + this.newnodeid + " and a.id=b.fieldid and b.isview='1' and a.viewtype=1 and a.billid=" + this.newformid;
/*      */               } else {
/*  903 */                 str5 = "select a.id,a.fieldname,a.fielddbtype,a.fieldhtmltype,a.type,b.isedit from workflow_billfield a,workflow_nodeform b where b.nodeid=" + this.newnodeid + " and a.id=b.fieldid and b.isview='1' and a.viewtype=1 and a.billid=" + this.newformid;
/*      */               } 
/*  905 */               if (str25.equals(str22)) {
/*  906 */                 str5 = str5 + " and (a.detailtable is null or a.detailtable='' or a.detailtable='" + str25 + "') ";
/*      */               } else {
/*  908 */                 str5 = str5 + " and a.detailtable='" + str25 + "' ";
/*      */               } 
/*      */ 
/*      */ 
/*      */               
/*  913 */               str5 = str5 + " order by a.detailtable,a.dsporder";
/*      */               
/*  915 */               recordSet1.executeSql(str5);
/*      */               
/*  917 */               while (recordSet1.next()) {
/*  918 */                 String str27 = Util.null2String(recordSet1.getString("id"));
/*  919 */                 String str28 = Util.null2String(recordSet1.getString("fieldname"));
/*  920 */                 int i2 = Util.getIntValue(recordSet1.getString("fieldhtmltype"));
/*  921 */                 String str29 = Util.null2String(recordSet1.getString("fielddbtype"));
/*  922 */                 int i3 = Util.getIntValue(recordSet1.getString("type"));
/*  923 */                 int i4 = Util.getIntValue(recordSet1.getString("isedit"));
/*      */                 
/*  925 */                 hashMap.put(str28, str29);
/*  926 */                 if (arrayList1.contains(str27) || (
/*  927 */                   !"1".equals(str17) && i4 != 1)) {
/*      */                   continue;
/*      */                 }
/*  930 */                 int i5 = arrayList4.indexOf(str27);
/*  931 */                 if (i5 > -1 && (i4 != 1 || (i4 == 1 && arrayList1.contains(str27)))) {
/*  932 */                   String str30 = Util.null2String(arrayList5.get(i5));
/*  933 */                   if (!str29.toLowerCase().contains("browser") && ((i2 == 3 && (i3 == 1 || i3 == 165)) || str29.toLowerCase().indexOf("int") > -1 || str29.toLowerCase().indexOf("number") > -1 || str29.toLowerCase().indexOf("decimal") > -1)) {
/*  934 */                     if (str30 == null || str30.trim().equals("")) str30 = "NULL"; 
/*  935 */                     str24 = str24 + "," + str30;
/*  936 */                   } else if (recordSet1.getDBType().equals("oracle") && i2 == 3 && i3 == 17) {
/*  937 */                     str24 = str24 + ",'" + str30 + "' ";
/*  938 */                     hashMap3.put(str28, str30);
/*      */                   } else {
/*  940 */                     str24 = str24 + ",'" + str30 + "'";
/*      */                   } 
/*  942 */                   str23 = str23 + "," + str28;
/*      */                   continue;
/*      */                 } 
/*  945 */                 if (arrayList2.contains(str27) && (i2 == 1 || i2 == 5)) {
/*  946 */                   i4 = 1;
/*      */                 }
/*  948 */                 if (i4 != 1) {
/*  949 */                   if (i2 == 1) {
/*  950 */                     if (!"".equals(str27) && !"".equals(str) && i3 != 1) {
/*  951 */                       if (str.indexOf("detailfield_" + str27 + "=") > -1) {
/*  952 */                         str24 = str24 + "," + str28;
/*  953 */                         str23 = str23 + "," + str28; continue;
/*      */                       } 
/*  955 */                       if (!arrayList1.contains(str27) && "1".equals(str17)) {
/*  956 */                         str24 = str24 + "," + str28;
/*  957 */                         str23 = str23 + "," + str28;
/*      */                       } 
/*      */                       continue;
/*      */                     } 
/*  961 */                     if (!arrayList1.contains(str27) && "1".equals(str17)) {
/*  962 */                       str24 = str24 + "," + str28;
/*  963 */                       str23 = str23 + "," + str28;
/*      */                     }  continue;
/*      */                   } 
/*  966 */                   if (i2 == 3 && (!"1".equals(str17) || !"1".equals(str20))) {
/*  967 */                     if (i3 == 1 || i3 == 165) {
/*  968 */                       str24 = str24 + "," + this.newuserid;
/*  969 */                       str23 = str23 + "," + str28; continue;
/*  970 */                     }  if (i3 == 17 || i3 == 166) {
/*  971 */                       if (recordSet1.getDBType().equals("oracle") && i3 == 17) {
/*      */ 
/*      */                         
/*  974 */                         str24 = str24 + ",'" + this.newuserid + "'";
/*  975 */                         str23 = str23 + "," + str28;
/*  976 */                         hashMap3.put(str28, Integer.valueOf(this.newuserid)); continue;
/*      */                       } 
/*  978 */                       str24 = str24 + ",'" + this.newuserid + "'";
/*  979 */                       str23 = str23 + "," + str28; continue;
/*      */                     } 
/*  981 */                     if (i3 == 2) {
/*  982 */                       str24 = str24 + ",'" + str1 + "'";
/*  983 */                       str23 = str23 + "," + str28; continue;
/*  984 */                     }  if (i3 == 19) {
/*  985 */                       str24 = str24 + ",'" + str2.substring(0, 5) + "'";
/*  986 */                       str23 = str23 + "," + str28; continue;
/*  987 */                     }  if (i3 == 290) {
/*  988 */                       String str30 = str1 + " " + str2.substring(0, 5);
/*  989 */                       str24 = str24 + ",'" + str30 + "'";
/*  990 */                       str23 = str23 + "," + str28; continue;
/*  991 */                     }  if (i3 == 402) {
/*  992 */                       str24 = str24 + ",'" + str3 + "'";
/*  993 */                       str23 = str23 + "," + str28; continue;
/*  994 */                     }  if (i3 == 403) {
/*  995 */                       str24 = str24 + ",'" + str4 + "'";
/*  996 */                       str23 = str23 + "," + str28; continue;
/*  997 */                     }  if (i3 == 4 || i3 == 167) {
/*  998 */                       str24 = str24 + "," + resourceComInfo.getDepartmentID("" + this.newuserid);
/*  999 */                       str23 = str23 + "," + str28; continue;
/* 1000 */                     }  if (i3 == 57 || i3 == 168) {
/* 1001 */                       str24 = str24 + ",'" + resourceComInfo.getDepartmentID("" + this.newuserid) + "'";
/* 1002 */                       str23 = str23 + "," + str28; continue;
/* 1003 */                     }  if (i3 == 42 || i3 == 164 || i3 == 169) {
/* 1004 */                       str24 = str24 + "," + resourceComInfo.getSubCompanyID("" + this.newuserid);
/* 1005 */                       str23 = str23 + "," + str28; continue;
/* 1006 */                     }  if (i3 == 170 || i3 == 194) {
/* 1007 */                       str24 = str24 + ",'" + resourceComInfo.getSubCompanyID("" + this.newuserid) + "'";
/* 1008 */                       str23 = str23 + "," + str28; continue;
/* 1009 */                     }  if (i3 == 24 || i3 == 278) {
/* 1010 */                       str24 = str24 + "," + resourceComInfo.getJobTitle("" + this.newuserid);
/* 1011 */                       str23 = str23 + "," + str28; continue;
/*      */                     } 
/* 1013 */                     if (!arrayList1.contains(str27) && "1".equals(str17)) {
/* 1014 */                       str24 = str24 + "," + str28;
/* 1015 */                       str23 = str23 + "," + str28;
/*      */                     } 
/*      */                     continue;
/*      */                   } 
/* 1019 */                   if (!arrayList1.contains(str27) && "1".equals(str17)) {
/* 1020 */                     str24 = str24 + "," + str28;
/* 1021 */                     str23 = str23 + "," + str28;
/*      */                   }  continue;
/*      */                 } 
/* 1024 */                 if (i4 == 1 || (i4 != 1 && "1".equals(str17))) {
/* 1025 */                   if (!"".equals(str27) && arrayList1.contains(str27)) {
/* 1026 */                     if (i2 == 3) {
/* 1027 */                       if (i3 == 1 || i3 == 165) {
/* 1028 */                         str24 = str24 + "," + this.newuserid;
/* 1029 */                         str23 = str23 + "," + str28; continue;
/* 1030 */                       }  if (i3 == 17 || i3 == 166) {
/* 1031 */                         if (recordSet1.getDBType().equals("oracle") && i3 == 17) {
/*      */ 
/*      */                           
/* 1034 */                           str24 = str24 + ",'" + this.newuserid + "'";
/* 1035 */                           str23 = str23 + "," + str28;
/* 1036 */                           hashMap3.put(str28, Integer.valueOf(this.newuserid)); continue;
/*      */                         } 
/* 1038 */                         str24 = str24 + ",'" + this.newuserid + "'";
/* 1039 */                         str23 = str23 + "," + str28; continue;
/*      */                       } 
/* 1041 */                       if (i3 == 2) {
/* 1042 */                         str24 = str24 + ",'" + str1 + "'";
/* 1043 */                         str23 = str23 + "," + str28; continue;
/* 1044 */                       }  if (i3 == 19) {
/* 1045 */                         str24 = str24 + ",'" + str2.substring(0, 5) + "'";
/* 1046 */                         str23 = str23 + "," + str28; continue;
/* 1047 */                       }  if (i3 == 290) {
/* 1048 */                         String str30 = str1 + " " + str2.substring(0, 5);
/* 1049 */                         str24 = str24 + ",'" + str30 + "'";
/* 1050 */                         str23 = str23 + "," + str28; continue;
/* 1051 */                       }  if (i3 == 402) {
/* 1052 */                         str24 = str24 + ",'" + str3 + "'";
/* 1053 */                         str23 = str23 + "," + str28; continue;
/* 1054 */                       }  if (i3 == 403) {
/* 1055 */                         str24 = str24 + ",'" + str4 + "'";
/* 1056 */                         str23 = str23 + "," + str28; continue;
/* 1057 */                       }  if (i3 == 4 || i3 == 167) {
/* 1058 */                         str24 = str24 + "," + resourceComInfo.getDepartmentID("" + this.newuserid);
/* 1059 */                         str23 = str23 + "," + str28; continue;
/* 1060 */                       }  if (i3 == 57 || i3 == 168) {
/* 1061 */                         str24 = str24 + ",'" + resourceComInfo.getDepartmentID("" + this.newuserid) + "'";
/* 1062 */                         str23 = str23 + "," + str28; continue;
/* 1063 */                       }  if (i3 == 42 || i3 == 164 || i3 == 169) {
/* 1064 */                         str24 = str24 + "," + resourceComInfo.getSubCompanyID("" + this.newuserid);
/* 1065 */                         str23 = str23 + "," + str28; continue;
/* 1066 */                       }  if (i3 == 170 || i3 == 194) {
/* 1067 */                         str24 = str24 + ",'" + resourceComInfo.getSubCompanyID("" + this.newuserid) + "'";
/* 1068 */                         str23 = str23 + "," + str28; continue;
/* 1069 */                       }  if (i3 == 24 || i3 == 278) {
/* 1070 */                         str24 = str24 + "," + resourceComInfo.getJobTitle("" + this.newuserid);
/* 1071 */                         str23 = str23 + "," + str28;
/*      */                       } 
/*      */                     } 
/*      */ 
/*      */                     
/*      */                     continue;
/*      */                   } 
/*      */                   
/* 1079 */                   str24 = str24 + "," + str28;
/* 1080 */                   str23 = str23 + "," + str28;
/*      */                 } 
/*      */               } 
/* 1083 */               if (Util.null2String(str25).trim().toLowerCase().indexOf("Bill_Fna".toLowerCase()) == 0 && 
/* 1084 */                 Util.null2String(str23).trim().toLowerCase().indexOf(",dsporder") < 0 && 
/* 1085 */                 Util.null2String(str23).trim().toLowerCase().indexOf("dsporder,") < 0) {
/* 1086 */                 str23 = str23 + ",dsporder ";
/* 1087 */                 str24 = str24 + ",dsporder ";
/*      */               } 
/* 1089 */               hashMap3.clear();
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */               
/* 1095 */               int i1 = (str23.split(",")).length;
/* 1096 */               String str26 = "insert into " + str25 + " (" + str23 + ") values (";
/* 1097 */               for (byte b3 = 1; b3 <= i1; b3++) {
/* 1098 */                 str26 = str26 + "?,";
/*      */               }
/* 1100 */               if (str26.endsWith(","))
/* 1101 */                 str26 = str26.substring(0, str26.length() - 1); 
/* 1102 */               str26 = str26 + ")";
/* 1103 */               ArrayList<ArrayList<String>> arrayList = new ArrayList();
/* 1104 */               recordSet1.isReturnDecryptData(true);
/* 1105 */               recordSet1.executeQuery("select " + str24 + " from " + str25 + " where exists(select 1 from " + str6 + " where id=" + str25 + "." + str21 + " and requestid=" + this.imprequestid + ") order by id", new Object[0]);
/* 1106 */               while (recordSet1.next()) {
/* 1107 */                 ArrayList<String> arrayList7 = new ArrayList();
/* 1108 */                 for (byte b4 = 1; b4 <= i1; b4++) {
/*      */                   
/* 1110 */                   if ("".equals(recordSet1.getString(b4))) {
/* 1111 */                     arrayList7.add(null);
/*      */                   } else {
/* 1113 */                     arrayList7.add(recordSet1.getString(b4));
/*      */                   } 
/*      */                 } 
/* 1116 */                 arrayList.add(arrayList7);
/*      */               } 
/* 1118 */               recordSet1.executeBatchSql(str26, arrayList);
/*      */             } 
/*      */           } else {
/* 1121 */             String str23 = "requestid,groupid";
/* 1122 */             String str24 = i + ",groupid";
/* 1123 */             if (this.newmodeid > 0 && this.ismode == 1) {
/* 1124 */               str5 = "select a.id,a.fieldname,a.fielddbtype,a.fieldhtmltype,a.type,b.isedit from workflow_formdictdetail a,workflow_formfield c,workflow_modeview b where a.id=c.fieldid and c.isdetail='1' and b.isbill='0' and b.nodeid=" + this.newnodeid + " and a.id=b.fieldid and b.isview='1' and c.formid=" + this.newformid;
/*      */             } else {
/* 1126 */               str5 = "select a.id,a.fieldname,a.fielddbtype,a.fieldhtmltype,a.type,b.isedit from workflow_formdictdetail a,workflow_formfield c,workflow_nodeform b where a.id=c.fieldid and c.isdetail='1' and b.nodeid=" + this.newnodeid + " and a.id=b.fieldid and b.isview='1' and c.formid=" + this.newformid;
/*      */             } 
/*      */ 
/*      */ 
/*      */             
/* 1131 */             str5 = str5 + " order by c.fieldorder";
/*      */             
/* 1133 */             recordSet1.executeSql(str5);
/*      */             
/* 1135 */             while (recordSet1.next()) {
/* 1136 */               String str25 = Util.null2String(recordSet1.getString("id"));
/* 1137 */               String str26 = Util.null2String(recordSet1.getString("fieldname"));
/* 1138 */               int i1 = Util.getIntValue(recordSet1.getString("fieldhtmltype"));
/* 1139 */               String str27 = Util.null2String(recordSet1.getString("fielddbtype"));
/* 1140 */               int i2 = Util.getIntValue(recordSet1.getString("type"));
/* 1141 */               int i3 = Util.getIntValue(recordSet1.getString("isedit"));
/* 1142 */               int i4 = arrayList4.indexOf(str25);
/* 1143 */               if (i4 > -1 && (i3 != 1 || (i3 == 1 && arrayList1.contains(str25)))) {
/* 1144 */                 String str28 = Util.null2String(arrayList5.get(i4));
/* 1145 */                 if (!str27.toLowerCase().contains("browser") && ((i1 == 3 && (i2 == 1 || i2 == 165)) || str27.toLowerCase().indexOf("int") > -1 || str27.toLowerCase().indexOf("number") > -1 || str27.toLowerCase().indexOf("decimal") > -1)) {
/* 1146 */                   if (str28 == null || str28.trim().equals("")) str28 = "NULL"; 
/* 1147 */                   str24 = str24 + "," + str28;
/* 1148 */                 } else if (recordSet1.getDBType().equals("oracle") && i1 == 3 && i2 == 17) {
/* 1149 */                   str24 = str24 + ", '" + str28 + "' ";
/* 1150 */                   hashMap3.put(str26, str28);
/*      */                 } else {
/* 1152 */                   str24 = str24 + ",'" + str28 + "'";
/*      */                 } 
/* 1154 */                 str23 = str23 + "," + str26;
/*      */                 continue;
/*      */               } 
/* 1157 */               if (arrayList2.contains(str25) && (i1 == 1 || i1 == 5)) {
/* 1158 */                 i3 = 1;
/*      */               }
/* 1160 */               if (i3 != 1) {
/* 1161 */                 if (i1 == 1) {
/* 1162 */                   if (!"".equals(str25) && !"".equals(str) && i2 != 1) {
/* 1163 */                     if (str.indexOf("detailfield_" + str25 + "=") > -1) {
/* 1164 */                       str24 = str24 + "," + str26;
/* 1165 */                       str23 = str23 + "," + str26; continue;
/*      */                     } 
/* 1167 */                     if (!arrayList1.contains(str25) && "1".equals(str17)) {
/* 1168 */                       str24 = str24 + "," + str26;
/* 1169 */                       str23 = str23 + "," + str26;
/*      */                     } 
/*      */                     continue;
/*      */                   } 
/* 1173 */                   if (!arrayList1.contains(str25) && "1".equals(str17)) {
/* 1174 */                     str24 = str24 + "," + str26;
/* 1175 */                     str23 = str23 + "," + str26;
/*      */                   }  continue;
/*      */                 } 
/* 1178 */                 if (i1 == 3 && (!"1".equals(str17) || !"1".equals(str20))) {
/* 1179 */                   if (i2 == 1 || i2 == 165) {
/* 1180 */                     str24 = str24 + "," + this.newuserid;
/* 1181 */                     str23 = str23 + "," + str26; continue;
/* 1182 */                   }  if (i2 == 17 || i2 == 166) {
/* 1183 */                     if (recordSet1.getDBType().equals("oracle") && i2 == 17) {
/*      */ 
/*      */                       
/* 1186 */                       str23 = str23 + "," + str26;
/* 1187 */                       str24 = str24 + ",'" + this.newuserid + "'";
/* 1188 */                       hashMap3.put(str26, Integer.valueOf(this.newuserid)); continue;
/*      */                     } 
/* 1190 */                     str24 = str24 + ",'" + this.newuserid + "'";
/* 1191 */                     str23 = str23 + "," + str26;
/*      */                     
/*      */                     continue;
/*      */                   } 
/* 1195 */                   if (i2 == 2) {
/* 1196 */                     str24 = str24 + ",'" + str1 + "'";
/* 1197 */                     str23 = str23 + "," + str26; continue;
/* 1198 */                   }  if (i2 == 19) {
/* 1199 */                     str24 = str24 + ",'" + str2.substring(0, 5) + "'";
/* 1200 */                     str23 = str23 + "," + str26; continue;
/* 1201 */                   }  if (i2 == 290) {
/* 1202 */                     String str28 = str1 + " " + str2.substring(0, 5);
/* 1203 */                     str24 = str24 + ",'" + str28 + "'";
/* 1204 */                     str23 = str23 + "," + str26; continue;
/* 1205 */                   }  if (i2 == 402) {
/* 1206 */                     str24 = str24 + ",'" + str3 + "'";
/* 1207 */                     str23 = str23 + "," + str26; continue;
/* 1208 */                   }  if (i2 == 403) {
/* 1209 */                     str24 = str24 + ",'" + str4 + "'";
/* 1210 */                     str23 = str23 + "," + str26; continue;
/* 1211 */                   }  if (i2 == 4 || i2 == 167) {
/* 1212 */                     str24 = str24 + "," + resourceComInfo.getDepartmentID("" + this.newuserid);
/* 1213 */                     str23 = str23 + "," + str26; continue;
/* 1214 */                   }  if (i2 == 57 || i2 == 168) {
/* 1215 */                     str24 = str24 + ",'" + resourceComInfo.getDepartmentID("" + this.newuserid) + "'";
/* 1216 */                     str23 = str23 + "," + str26; continue;
/* 1217 */                   }  if (i2 == 42 || i2 == 164 || i2 == 169) {
/* 1218 */                     str24 = str24 + "," + resourceComInfo.getSubCompanyID("" + this.newuserid);
/* 1219 */                     str23 = str23 + "," + str26; continue;
/* 1220 */                   }  if (i2 == 170 || i2 == 194) {
/* 1221 */                     str24 = str24 + ",'" + resourceComInfo.getSubCompanyID("" + this.newuserid) + "'";
/* 1222 */                     str23 = str23 + "," + str26; continue;
/* 1223 */                   }  if (i2 == 24 || i2 == 278) {
/* 1224 */                     str24 = str24 + "," + resourceComInfo.getJobTitle("" + this.newuserid);
/* 1225 */                     str23 = str23 + "," + str26; continue;
/*      */                   } 
/* 1227 */                   if (!arrayList1.contains(str25) && "1".equals(str17)) {
/* 1228 */                     str24 = str24 + "," + str26;
/* 1229 */                     str23 = str23 + "," + str26;
/*      */                   } 
/*      */                   continue;
/*      */                 } 
/* 1233 */                 if (!arrayList1.contains(str25) && "1".equals(str17)) {
/* 1234 */                   str24 = str24 + "," + str26;
/* 1235 */                   str23 = str23 + "," + str26;
/*      */                 }  continue;
/*      */               } 
/* 1238 */               if (i3 == 1 || (i3 != 1 && "1".equals(str17))) {
/* 1239 */                 if (!"".equals(str25) && arrayList1.contains(str25)) {
/* 1240 */                   if (i1 == 3) {
/* 1241 */                     if (i2 == 1 || i2 == 165) {
/* 1242 */                       str24 = str24 + "," + this.newuserid;
/* 1243 */                       str23 = str23 + "," + str26; continue;
/* 1244 */                     }  if (i2 == 17 || i2 == 166) {
/* 1245 */                       if (recordSet1.getDBType().equals("oracle") && i2 == 17) {
/*      */ 
/*      */                         
/* 1248 */                         str24 = str24 + ",'" + this.newuserid + "'";
/* 1249 */                         str23 = str23 + "," + str26;
/* 1250 */                         hashMap3.put(str26, Integer.valueOf(this.newuserid)); continue;
/*      */                       } 
/* 1252 */                       str24 = str24 + ",'" + this.newuserid + "'";
/* 1253 */                       str23 = str23 + "," + str26;
/*      */                       
/*      */                       continue;
/*      */                     } 
/*      */                     
/* 1258 */                     if (i2 == 2) {
/* 1259 */                       str24 = str24 + ",'" + str1 + "'";
/* 1260 */                       str23 = str23 + "," + str26; continue;
/* 1261 */                     }  if (i2 == 19) {
/* 1262 */                       str24 = str24 + ",'" + str2.substring(0, 5) + "'";
/* 1263 */                       str23 = str23 + "," + str26; continue;
/* 1264 */                     }  if (i2 == 290) {
/* 1265 */                       String str28 = str1 + " " + str2.substring(0, 5);
/* 1266 */                       str24 = str24 + ",'" + str28 + "'";
/* 1267 */                       str23 = str23 + "," + str26; continue;
/* 1268 */                     }  if (i2 == 402) {
/* 1269 */                       str24 = str24 + ",'" + str3 + "'";
/* 1270 */                       str23 = str23 + "," + str26; continue;
/* 1271 */                     }  if (i2 == 403) {
/* 1272 */                       str24 = str24 + ",'" + str4 + "'";
/* 1273 */                       str23 = str23 + "," + str26; continue;
/* 1274 */                     }  if (i2 == 4 || i2 == 167) {
/* 1275 */                       str24 = str24 + "," + resourceComInfo.getDepartmentID("" + this.newuserid);
/* 1276 */                       str23 = str23 + "," + str26; continue;
/* 1277 */                     }  if (i2 == 57 || i2 == 168) {
/* 1278 */                       str24 = str24 + ",'" + resourceComInfo.getDepartmentID("" + this.newuserid) + "'";
/* 1279 */                       str23 = str23 + "," + str26; continue;
/* 1280 */                     }  if (i2 == 42 || i2 == 164 || i2 == 169) {
/* 1281 */                       str24 = str24 + "," + resourceComInfo.getSubCompanyID("" + this.newuserid);
/* 1282 */                       str23 = str23 + "," + str26; continue;
/* 1283 */                     }  if (i2 == 170 || i2 == 194) {
/* 1284 */                       str24 = str24 + ",'" + resourceComInfo.getSubCompanyID("" + this.newuserid) + "'";
/* 1285 */                       str23 = str23 + "," + str26; continue;
/* 1286 */                     }  if (i2 == 24 || i2 == 278) {
/* 1287 */                       str24 = str24 + "," + resourceComInfo.getJobTitle("" + this.newuserid);
/* 1288 */                       str23 = str23 + "," + str26;
/*      */                     } 
/*      */                   } 
/*      */ 
/*      */                   
/*      */                   continue;
/*      */                 } 
/*      */                 
/* 1296 */                 str24 = str24 + "," + str26;
/* 1297 */                 str23 = str23 + "," + str26;
/*      */               } 
/*      */             } 
/* 1300 */             str5 = "insert into workflow_formdetail (" + str23 + ") select " + str24 + " from workflow_formdetail where requestid=" + this.imprequestid + " order by groupid,id";
/*      */             
/* 1302 */             recordSet1.executeSql(str5);
/*      */           } 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */           
/* 1377 */           if (this.newisbill == 1) {
/* 1378 */             String str23 = "";
/* 1379 */             String str24 = "";
/* 1380 */             String str25 = "";
/* 1381 */             recordSet1.executeSql("select tablename,detailkeyfield from workflow_bill where id = " + this.newformid);
/* 1382 */             if (recordSet1.next()) {
/* 1383 */               str23 = Util.null2String(recordSet1.getString("tablename"));
/* 1384 */               str24 = Util.null2String(recordSet1.getString("detailkeyfield"));
/*      */             } 
/* 1386 */             if (this.newmodeid > 0 && this.ismode == 1) {
/* 1387 */               str5 = "select a.id,a.fieldname,a.fielddbtype,a.fieldhtmltype,a.type,a.detailtable,b.isedit from workflow_billfield a,workflow_modeview b where a.billid=b.formid and b.isbill='1' and b.nodeid=" + this.newnodeid + " and a.id=b.fieldid and b.isview='1' and a.viewtype=1 and a.fieldhtmltype=6 and a.billid=" + this.newformid;
/*      */             } else {
/* 1389 */               str5 = "select a.id,a.fieldname,a.fielddbtype,a.fieldhtmltype,a.type,a.detailtable,b.isedit from workflow_billfield a,workflow_nodeform b where b.nodeid=" + this.newnodeid + " and a.id=b.fieldid and b.isview='1' and a.viewtype=1 and a.fieldhtmltype=6 and a.billid=" + this.newformid;
/*      */             } 
/* 1391 */             str5 = str5 + " order by a.detailtable,a.dsporder";
/*      */ 
/*      */             
/* 1394 */             recordSet1.executeSql(str5);
/* 1395 */             while (recordSet1.next()) {
/* 1396 */               String str26 = recordSet1.getString("id");
/* 1397 */               String str27 = recordSet1.getString("fieldname");
/* 1398 */               String str28 = Util.null2String(recordSet1.getString("detailtable"));
/*      */               
/* 1400 */               if (!"".equals(str26) && arrayList1.contains(str26)) {
/*      */                 continue;
/*      */               }
/*      */               
/* 1404 */               if (!str27.equals("")) {
/* 1405 */                 str25 = "select dt.id,dt." + str27 + " from " + str23 + " m," + str28 + " dt where m.id=dt." + str24 + " and m.requestid= " + i;
/* 1406 */                 recordSet2.executeSql(str25);
/*      */ 
/*      */ 
/*      */                 
/* 1410 */                 while (recordSet2.next()) {
/* 1411 */                   String str29 = Util.null2String(recordSet2.getString("id")).trim();
/* 1412 */                   String str30 = Util.null2String(recordSet2.getString(str27)).trim();
/* 1413 */                   if (str30.equals("")) {
/*      */                     continue;
/*      */                   }
/*      */ 
/*      */                   
/* 1418 */                   String str31 = "";
/* 1419 */                   String[] arrayOfString = str30.split(",");
/* 1420 */                   if (arrayOfString != null && arrayOfString.length > 0) {
/* 1421 */                     for (byte b = 0; b < arrayOfString.length; b++) {
/* 1422 */                       int i1 = Util.getIntValue(arrayOfString[b], 0);
/* 1423 */                       if (i1 > 0) {
/* 1424 */                         String str32 = "";
/* 1425 */                         int i2 = 0;
/* 1426 */                         int i3 = 0;
/* 1427 */                         int i4 = 0;
/* 1428 */                         RecordSet recordSet = new RecordSet();
/*      */                         
/* 1430 */                         recordSet.executeSql("select a.docSubject,a.secCategory as secCategoryId,a.subcategory as subCategoryId,a.maincategory as mainCategoryId from DocDetail a where a.id =" + i1);
/* 1431 */                         if (recordSet.next()) {
/* 1432 */                           str32 = Util.null2String(recordSet.getString("docSubject"));
/* 1433 */                           i2 = Util.getIntValue(recordSet.getString("secCategoryId"), 0);
/* 1434 */                           i3 = Util.getIntValue(recordSet.getString("subCategoryId"), 0);
/* 1435 */                           i4 = Util.getIntValue(recordSet.getString("mainCategoryId"), 0);
/*      */                         } 
/* 1437 */                         String str33 = "" + (n + 1);
/* 1438 */                         DocManager docManager = new DocManager();
/* 1439 */                         docManager.setId(i1);
/* 1440 */                         docManager.setUserid(m);
/* 1441 */                         docManager.setUsertype(str33);
/* 1442 */                         docManager.setDocsubject(str32);
/* 1443 */                         docManager.setMaincategory(i4);
/* 1444 */                         docManager.setSubcategory(i3);
/* 1445 */                         docManager.setSeccategory(i2);
/* 1446 */                         docManager.copyDocForNoRightAndNoChangeStatus();
/* 1447 */                         int i5 = docManager.getId();
/* 1448 */                         str31 = str31 + i5 + ",";
/*      */                       } 
/*      */                     } 
/*      */                   }
/* 1452 */                   if (!str31.equals("")) {
/* 1453 */                     str31 = str31.substring(0, str31.length() - 1);
/*      */                   }
/* 1455 */                   str25 = "update " + str28 + " set " + str27 + "='" + str31 + "' where id=" + str29;
/* 1456 */                   recordSet3.executeSql(str25);
/*      */                 }
/*      */               
/*      */               }
/*      */             
/*      */             }
/*      */           
/*      */           }
/*      */           else {
/*      */             
/* 1466 */             String str23 = "";
/* 1467 */             if (this.newmodeid > 0 && this.ismode == 1) {
/* 1468 */               str5 = "select a.id,a.fieldname,a.fielddbtype,a.fieldhtmltype,a.type,b.isedit from workflow_formdictdetail a,workflow_formfield c,workflow_modeview b where a.id=c.fieldid and c.isdetail='1' and b.isbill='0' and b.nodeid=" + this.newnodeid + " and a.id=b.fieldid and b.isview='1' and a.fieldhtmltype=6 and c.formid=" + this.newformid;
/*      */             } else {
/* 1470 */               str5 = "select a.id,a.fieldname,a.fielddbtype,a.fieldhtmltype,a.type,b.isedit from workflow_formdictdetail a,workflow_formfield c,workflow_nodeform b where a.id=c.fieldid and c.isdetail='1' and b.nodeid=" + this.newnodeid + " and a.id=b.fieldid and b.isview='1' and a.fieldhtmltype=6 and c.formid=" + this.newformid;
/*      */             } 
/* 1472 */             str5 = str5 + " order by c.fieldorder";
/* 1473 */             recordSet1.executeSql(str5);
/* 1474 */             while (recordSet1.next()) {
/* 1475 */               String str24 = recordSet1.getString("id");
/* 1476 */               String str25 = recordSet1.getString("fieldname");
/* 1477 */               if (!"".equals(str24) && arrayList1.contains(str24)) {
/*      */                 continue;
/*      */               }
/* 1480 */               if (!str25.equals("")) {
/* 1481 */                 str23 = "select dt.id,dt." + str25 + " from workflow_formdetail dt where dt.requestid= " + i;
/* 1482 */                 recordSet2.executeSql(str23);
/* 1483 */                 while (recordSet2.next()) {
/* 1484 */                   String str26 = Util.null2String(recordSet2.getString("id")).trim();
/* 1485 */                   String str27 = Util.null2String(recordSet2.getString(str25)).trim();
/* 1486 */                   if (str27.equals("")) {
/*      */                     continue;
/*      */                   }
/* 1489 */                   String str28 = "";
/* 1490 */                   String[] arrayOfString = str27.split(",");
/* 1491 */                   if (arrayOfString != null && arrayOfString.length > 0) {
/* 1492 */                     for (byte b = 0; b < arrayOfString.length; b++) {
/* 1493 */                       int i1 = Util.getIntValue(arrayOfString[b], 0);
/* 1494 */                       if (i1 > 0) {
/* 1495 */                         String str29 = "";
/* 1496 */                         int i2 = 0;
/* 1497 */                         int i3 = 0;
/* 1498 */                         int i4 = 0;
/* 1499 */                         RecordSet recordSet = new RecordSet();
/*      */                         
/* 1501 */                         recordSet.executeSql("select a.docSubject,a.secCategory as secCategoryId,a.subcategory as subCategoryId,a.maincategory as mainCategoryId from DocDetail a where a.id =" + i1);
/* 1502 */                         if (recordSet.next()) {
/* 1503 */                           str29 = Util.null2String(recordSet.getString("docSubject"));
/* 1504 */                           i2 = Util.getIntValue(recordSet.getString("secCategoryId"), 0);
/* 1505 */                           i3 = Util.getIntValue(recordSet.getString("subCategoryId"), 0);
/* 1506 */                           i4 = Util.getIntValue(recordSet.getString("mainCategoryId"), 0);
/*      */                         } 
/* 1508 */                         String str30 = "" + (n + 1);
/* 1509 */                         DocManager docManager = new DocManager();
/* 1510 */                         docManager.setId(i1);
/* 1511 */                         docManager.setUserid(m);
/* 1512 */                         docManager.setUsertype(str30);
/* 1513 */                         docManager.setDocsubject(str29);
/* 1514 */                         docManager.setMaincategory(i4);
/* 1515 */                         docManager.setSubcategory(i3);
/* 1516 */                         docManager.setSeccategory(i2);
/* 1517 */                         docManager.copyDocForNoRightAndNoChangeStatus();
/* 1518 */                         int i5 = docManager.getId();
/* 1519 */                         str28 = str28 + i5 + ",";
/*      */                       } 
/*      */                     } 
/*      */                   }
/* 1523 */                   if (!str28.equals("")) {
/* 1524 */                     str28 = str28.substring(0, str28.length() - 1);
/*      */                   }
/* 1526 */                   str23 = "update workflow_formdetail set " + str25 + "='" + str28 + "' where id=" + str26;
/* 1527 */                   recordSet3.executeSql(str23);
/*      */                 } 
/*      */               } 
/*      */             } 
/*      */           } 
/*      */         } 
/* 1533 */       } catch (Exception exception) {
/* 1534 */         exception.printStackTrace();
/*      */       } 
/*      */     }
/* 1537 */     if (i > 0) {
/* 1538 */       CodeBuild codeBuild = new CodeBuild(this.newformid, this.newisbill + "", this.newworkflowid);
/* 1539 */       String str = codeBuild.haveCode();
/* 1540 */       if (!str.equals("")) {
/* 1541 */         RecordSet recordSet1 = new RecordSet();
/* 1542 */         String str1 = "workflow_form";
/* 1543 */         String str2 = "";
/* 1544 */         String str3 = "select fieldName  from workflow_formdict where id=" + str;
/* 1545 */         if (this.newisbill == 1) {
/* 1546 */           str3 = "select fieldName  from workflow_billfield where id=" + str;
/* 1547 */           recordSet1.executeSql("select tablename from workflow_bill where id = " + this.newformid);
/* 1548 */           if (recordSet1.next()) {
/* 1549 */             str1 = Util.null2String(recordSet1.getString("tablename"));
/*      */           }
/*      */         } 
/* 1552 */         recordSet1.execute(str3);
/* 1553 */         if (recordSet1.next()) {
/* 1554 */           str2 = Util.null2String(recordSet1.getString(1));
/*      */         }
/* 1556 */         if (!"".equals(str2)) {
/* 1557 */           recordSet1.executeSql("update " + str1 + " set " + str2 + "='' where requestid='" + i + "'");
/*      */         }
/*      */       } 
/*      */       
/* 1561 */       RecordSet recordSet = new RecordSet();
/*      */       
/* 1563 */       recordSet.executeQuery("select " + i + ", fieldid, type, typeid, ids, md5 from workflow_reqbrowextrainfo where requestid=" + this.imprequestid, new Object[0]);
/* 1564 */       ArrayList<ArrayList<Integer>> arrayList = new ArrayList();
/* 1565 */       while (recordSet.next()) {
/* 1566 */         ArrayList<Integer> arrayList1 = new ArrayList();
/* 1567 */         int j = Util.getIntValue(recordSet.getString(1));
/* 1568 */         int k = Util.getIntValue(recordSet.getString(2));
/* 1569 */         int m = Util.getIntValue(recordSet.getString(3));
/* 1570 */         int n = Util.getIntValue(recordSet.getString(4));
/* 1571 */         String str1 = recordSet.getString(5);
/* 1572 */         String str2 = recordSet.getString(6);
/* 1573 */         arrayList1.add(Integer.valueOf(j));
/* 1574 */         arrayList1.add(Integer.valueOf(k));
/* 1575 */         arrayList1.add(Integer.valueOf(m));
/* 1576 */         arrayList1.add(Integer.valueOf(n));
/* 1577 */         arrayList1.add(str1);
/* 1578 */         arrayList1.add(str2);
/* 1579 */         arrayList.add(arrayList1);
/*      */       } 
/* 1581 */       recordSet.executeBatchSql("insert into workflow_reqbrowextrainfo(requestid, fieldid, type, typeid, ids, md5) values(?,?,?,?,?,?)", arrayList);
/*      */       
/*      */       try {
/* 1584 */         (new SetNewRequestTitle()).getAllRequestName(recordSet, i + "", this.newrequestname, this.newworkflowid + "", this.newnodeid + "", this.newformid, this.newisbill, 7);
/*      */       }
/* 1586 */       catch (Exception exception) {}
/*      */     } 
/*      */ 
/*      */     
/* 1590 */     return i;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private String getRowCalCulateStr(int paramInt) {
/* 1597 */     String str = "";
/* 1598 */     RecordSet recordSet = new RecordSet();
/* 1599 */     recordSet.executeSql("SELECT rowcalstr FROM workflow_formdetailinfo WHERE formid=" + paramInt);
/* 1600 */     if (recordSet.next())
/* 1601 */       str = Util.null2String(recordSet.getString("rowcalstr")); 
/* 1602 */     return str;
/*      */   }
/*      */   
/*      */   public String getNewchatsType() {
/* 1606 */     return this.newchatsType;
/*      */   }
/*      */   
/*      */   public void setNewchatsType(String paramString) {
/* 1610 */     this.newchatsType = paramString;
/*      */   }
/*      */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/RequestImport.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */