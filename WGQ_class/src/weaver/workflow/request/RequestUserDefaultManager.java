/*     */ package weaver.workflow.request;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.List;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.share.ShareManager;
/*     */ import weaver.workflow.workflow.WorkflowAllComInfo;
/*     */ import weaver.workflow.workflow.WorkflowComInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class RequestUserDefaultManager
/*     */   extends BaseBean
/*     */ {
/*     */   public void addDefaultOfSysAdmin(String paramString) {
/*  43 */     if (paramString == null || paramString.equals("")) {
/*     */       return;
/*     */     }
/*  46 */     RecordSet recordSet = new RecordSet();
/*     */ 
/*     */     
/*  49 */     String str1 = "";
/*  50 */     String str2 = "1";
/*     */     
/*  52 */     String str3 = "insert";
/*  53 */     recordSet.executeSql("select selectedWorkflow from workflow_requestUserdefault where userid=" + str2);
/*  54 */     if (recordSet.next()) {
/*  55 */       str1 = Util.null2String(recordSet.getString(1));
/*  56 */       str3 = "update";
/*     */     } 
/*     */ 
/*     */     
/*     */     try {
/*  61 */       if (str1.equals("")) {
/*     */         return;
/*     */       }
/*  64 */       String str4 = str1;
/*     */ 
/*     */       
/*  67 */       WorkflowComInfo workflowComInfo = new WorkflowComInfo();
/*  68 */       String str5 = workflowComInfo.getWorkflowtype(paramString);
/*     */       
/*  70 */       if (str4.indexOf("T" + str5 + "|") == -1 && !str4.endsWith("T" + str5)) {
/*  71 */         str4 = str4 + "|T" + str5;
/*     */       }
/*     */ 
/*     */       
/*  75 */       if (str4.indexOf("W" + paramString + "|") == -1 && !str4.endsWith("W" + paramString)) {
/*  76 */         str4 = str4 + "|W" + paramString;
/*     */       }
/*     */       
/*  79 */       if (str3.equals("update") && !str4.equals(str1)) {
/*  80 */         recordSet.executeSql("update workflow_requestUserdefault set selectedWorkflow='" + str4 + "' where userid=" + str2);
/*     */       
/*     */       }
/*     */     }
/*  84 */     catch (Exception exception) {
/*     */       return;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getWorkflowCreater(String paramString) {
/*  99 */     if (paramString == null || paramString.equals("")) {
/* 100 */       return "";
/*     */     }
/* 102 */     ShareManager shareManager = new ShareManager();
/* 103 */     return shareManager.getWorkflowCreater(Integer.parseInt(paramString));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void addDefaultOfNoSysAdmin(String paramString1, String paramString2) {
/* 115 */     if (paramString1 == null || paramString1.equals("")) {
/*     */       return;
/*     */     }
/* 118 */     RecordSet recordSet = new RecordSet();
/* 119 */     if (paramString2 == null) {
/* 120 */       paramString2 = "";
/*     */     }
/*     */     
/*     */     try {
/* 124 */       String str1 = getWorkflowCreater(paramString1);
/* 125 */       if (str1 == null || str1.equals("")) {
/*     */         return;
/*     */       }
/*     */       
/* 129 */       ArrayList<String> arrayList = Util.TokenizerString(str1, ",");
/* 130 */       String str2 = "";
/* 131 */       WorkflowComInfo workflowComInfo = new WorkflowComInfo();
/* 132 */       String str3 = workflowComInfo.getWorkflowtype(paramString1);
/*     */       
/* 134 */       for (byte b = 0; b < arrayList.size(); b++) {
/* 135 */         str2 = arrayList.get(b);
/*     */ 
/*     */         
/* 138 */         if (str2 != null && !str2.equals(""))
/*     */         {
/*     */ 
/*     */ 
/*     */           
/* 143 */           String str4 = "";
/*     */           
/* 145 */           String str5 = "insert";
/* 146 */           recordSet.executeSql("select selectedWorkflow from workflow_requestUserdefault where userid=" + str2);
/* 147 */           if (recordSet.next()) {
/* 148 */             str4 = Util.null2String(recordSet.getString(1));
/* 149 */             str5 = "update";
/*     */           } 
/* 151 */           if (!str4.equals(""))
/*     */           {
/*     */ 
/*     */             
/* 155 */             String str = str4;
/*     */ 
/*     */             
/* 158 */             if (str.indexOf("T" + str3 + "|") == -1 && !str.endsWith("T" + str3)) {
/* 159 */               str = str + "|T" + str3;
/*     */             }
/*     */ 
/*     */             
/* 163 */             if (str.indexOf("W" + paramString1 + "|") == -1 && !str.endsWith("W" + paramString1)) {
/* 164 */               str = str + "|W" + paramString1;
/*     */             }
/*     */             
/* 167 */             if (str5.equals("update") && !str.equals(str4)) {
/* 168 */               recordSet.executeSql("update workflow_requestUserdefault set selectedWorkflow='" + str + "' where userid=" + str2);
/*     */             }
/*     */           }
/*     */         
/*     */         }
/*     */       
/*     */       } 
/* 175 */     } catch (Exception exception) {
/*     */       return;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void addUserUsedTodoWf(String paramString1, String paramString2) {
/* 188 */     paramString1 = Util.null2String(paramString1);
/* 189 */     if ("".equals(paramString1))
/* 190 */       return;  WorkflowAllComInfo workflowAllComInfo = new WorkflowAllComInfo();
/* 191 */     String str1 = workflowAllComInfo.getWorkflowtype(paramString1);
/*     */     
/* 193 */     List<String> list = Util.splitString2List(paramString2, ",");
/* 194 */     RecordSet recordSet = new RecordSet();
/*     */     
/* 196 */     String str2 = "select selectedWorkflow from workflow_requestUserdefault where userid= ? ";
/* 197 */     String str3 = "update workflow_requestUserdefault set selectedWorkflow = ? where userid  = ? ";
/* 198 */     ArrayList<ArrayList<String>> arrayList = new ArrayList();
/* 199 */     for (byte b = 0; b < list.size(); b++) {
/* 200 */       String str = Util.null2String(list.get(b));
/* 201 */       if (!"".equals(str.trim())) {
/* 202 */         int i = Util.getIntValue(str);
/* 203 */         recordSet.executeQuery(str2, new Object[] { Integer.valueOf(i) });
/* 204 */         if (recordSet.next()) {
/* 205 */           String str4 = Util.null2String(recordSet.getString("selectedWorkflow"));
/* 206 */           String str5 = str4;
/* 207 */           if (str5.indexOf("T" + str1 + "|") == -1 && !str5.endsWith("T" + str1)) {
/* 208 */             str5 = str5 + "|T" + str1;
/*     */           }
/*     */           
/* 211 */           if (str5.indexOf("W" + paramString1 + "|") == -1 && !str5.endsWith("W" + paramString1)) {
/* 212 */             str5 = str5 + "|W" + paramString1;
/*     */           }
/*     */           
/* 215 */           if (!str4.equals(str5)) {
/* 216 */             ArrayList<String> arrayList1 = new ArrayList();
/* 217 */             arrayList1.add(str5);
/* 218 */             arrayList1.add(Integer.valueOf(i));
/* 219 */             arrayList.add(arrayList1);
/*     */           } 
/*     */         } 
/*     */       } 
/* 223 */     }  recordSet.executeBatchSql(str3, arrayList);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/RequestUserDefaultManager.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */