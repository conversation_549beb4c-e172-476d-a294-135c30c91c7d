/*      */ package weaver.workflow.request;
/*      */ 
/*      */ import com.engine.workflow.biz.WorkflowBaseBiz;
/*      */ import java.util.ArrayList;
/*      */ import java.util.HashMap;
/*      */ import java.util.Iterator;
/*      */ import java.util.Map;
/*      */ import java.util.regex.Matcher;
/*      */ import java.util.regex.Pattern;
/*      */ import org.apache.commons.lang3.StringUtils;
/*      */ import org.apache.commons.logging.Log;
/*      */ import org.apache.commons.logging.LogFactory;
/*      */ import org.json.JSONException;
/*      */ import org.json.JSONObject;
/*      */ import weaver.common.StringUtil;
/*      */ import weaver.conn.RecordSet;
/*      */ import weaver.general.BaseBean;
/*      */ import weaver.general.GCONST;
/*      */ import weaver.general.ThreadVarLanguage;
/*      */ import weaver.general.Util;
/*      */ import weaver.hrm.User;
/*      */ import weaver.hrm.companyvirtual.CompanyVirtualComInfo;
/*      */ import weaver.hrm.moduledetach.ManageDetachComInfo;
/*      */ import weaver.systeminfo.SystemEnv;
/*      */ import weaver.workflow.monitor.Monitor;
/*      */ import weaver.workflow.monitor.MonitorDTO;
/*      */ import weaver.workflow.ruleDesign.RuleBusiness;
/*      */ import weaver.workflow.workflow.UserWFOperateLevel;
/*      */ import weaver.workflow.workflow.WFManager;
/*      */ import weaver.workflow.workflow.WfFunctionManageUtil;
/*      */ import weaver.workflow.workflow.WfRightManager;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ public class WorkflowRequestMessage
/*      */ {
/*      */   public static final String WF_CUSTOM_ERROR = "1";
/*      */   public static final String WF_CUSTOM_LINK_TIP = "2";
/*      */   public static final String WF_NO_RIGHT = "-2";
/*      */   public static final String WF_FAIL_CREATE_REQUESTID = "-3";
/*      */   public static final String WF_SAVE_FAIL = "-4";
/*      */   public static final String WF_VALUE_IS_TOO_LANG = "-9";
/*      */   public static final String WF_REQUEST_ERROR_CODE_01 = "126223";
/*      */   public static final String WF_REQUEST_ERROR_CODE_02 = "21266";
/*      */   public static final String WF_REQUEST_ERROR_CODE_03 = "21270";
/*      */   public static final String WF_REQUEST_ERROR_CODE_04 = "24676";
/*      */   public static final String WF_REQUEST_ERROR_CODE_05 = "28083";
/*      */   public static final String WF_REQUEST_ERROR_CODE_06 = "126222";
/*      */   public static final String WF_REQUEST_ERROR_CODE_07 = "126221";
/*      */   public static final String WF_REQUEST_ERROR_CODE_08 = "389512";
/*      */   public static final String WF_REQUEST_ERROR_CODE_09 = "506175,83071";
/*      */   public static final String WF_REQUEST_ERROR_CODE_10 = "522594";
/*      */   public static final String WF_REQUEST_ERROR_CODE_11 = "524680";
/*      */   public static final String WF_REQUEST_ERROR_CODE_12 = "533505";
/*      */   public static final String WF_REQUEST_ERROR_CODE_13 = "533607";
/*      */   public static final String UPDATE_INTERVENTION_URL = "/workflow/request/ViewRequestForwardSPA.jsp?isintervenor=1&requestid=";
/*      */   public static final String UPDATE_FORMFIELD_NOBILL_URL = "/workflow/form/addformfield.jsp?formid=";
/*      */   public static final String UPDATE_FORMFIELD_BILL_URL = "/workflow/form/editformfield.jsp?formid=";
/*      */   public static final int WF_NODE_SET = 1;
/*      */   public static final int WF_LINK_SET = 2;
/*      */   public static final int WF_INVERVENTION = 3;
/*      */   public static final int WF_FORMFIELD_SET = 4;
/*   75 */   private static Map<String, String> OLD_MESSAGE_MAPPING = new HashMap<>();
/*      */ 
/*      */   
/*   78 */   private static Pattern PATTERN = Pattern.compile("\\{(.*?)\\}");
/*      */   
/*   80 */   public static final Pattern FIELDDBTYPE_PATTERN = Pattern.compile("^.*\\((\\d+)\\)$");
/*   81 */   private static Log LOG = LogFactory.getLog(WorkflowRequestMessage.class);
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   static {
/*   88 */     OLD_MESSAGE_MAPPING.put("1", "16332");
/*   89 */     OLD_MESSAGE_MAPPING.put("2", "16333");
/*   90 */     OLD_MESSAGE_MAPPING.put("3", "19455");
/*   91 */     OLD_MESSAGE_MAPPING.put("4", "21266");
/*   92 */     OLD_MESSAGE_MAPPING.put("5", "21270");
/*   93 */     OLD_MESSAGE_MAPPING.put("6", "21766");
/*   94 */     OLD_MESSAGE_MAPPING.put("7", "22751");
/*   95 */     OLD_MESSAGE_MAPPING.put("8", "24676");
/*   96 */     OLD_MESSAGE_MAPPING.put("182", "21624");
/*   97 */     OLD_MESSAGE_MAPPING.put("19", "17273,15508,1446");
/*      */     
/*   99 */     OLD_MESSAGE_MAPPING.put("183", "131656");
/*  100 */     OLD_MESSAGE_MAPPING.put("24569", "24569");
/*  101 */     OLD_MESSAGE_MAPPING.put("201", "15313,15508,,1446");
/*      */     
/*  103 */     OLD_MESSAGE_MAPPING.put("126223", "126223");
/*  104 */     OLD_MESSAGE_MAPPING.put("21266", "21266");
/*  105 */     OLD_MESSAGE_MAPPING.put("21270", "21270");
/*  106 */     OLD_MESSAGE_MAPPING.put("24676", "24676");
/*  107 */     OLD_MESSAGE_MAPPING.put("28083", "28083");
/*  108 */     OLD_MESSAGE_MAPPING.put("126222", "126222");
/*  109 */     OLD_MESSAGE_MAPPING.put("126221", "126221");
/*  110 */     OLD_MESSAGE_MAPPING.put("389512", "389512");
/*  111 */     OLD_MESSAGE_MAPPING.put("506175,83071", "506175,83071");
/*  112 */     OLD_MESSAGE_MAPPING.put("522594", "522594");
/*  113 */     OLD_MESSAGE_MAPPING.put("524680", "524680");
/*  114 */     OLD_MESSAGE_MAPPING.put("533505", "533505");
/*  115 */     OLD_MESSAGE_MAPPING.put("533607", "533607");
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static String getNewMessageId(String paramString, int paramInt) {
/*  123 */     String str = Util.null2String(OLD_MESSAGE_MAPPING.get(paramString));
/*  124 */     if (StringUtils.isNotEmpty(str)) {
/*  125 */       return SystemEnv.getHtmlLabelNames(str, paramInt);
/*      */     }
/*      */     
/*  128 */     return paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static String assemMsgInfo(String paramString, int paramInt) {
/*  138 */     Matcher matcher = PATTERN.matcher(paramString);
/*  139 */     while (matcher.find()) {
/*  140 */       int i = Util.getIntValue(matcher.group(1));
/*  141 */       if (i > 0) {
/*  142 */         String str = SystemEnv.getHtmlLabelName(i, paramInt);
/*  143 */         if (StringUtil.isNotNull(str)) {
/*  144 */           paramString = paramString.replaceFirst("\\{" + i + "\\}", str);
/*      */         }
/*      */       } 
/*      */     } 
/*  148 */     return paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static String assemMsgInfo(String paramString, String... paramVarArgs) {
/*  157 */     Matcher matcher = PATTERN.matcher(paramString);
/*  158 */     while (matcher.find()) {
/*  159 */       String str = matcher.group(1);
/*  160 */       if (StringUtils.isNotEmpty(str)) {
/*  161 */         paramString = paramString.replaceFirst("\\{" + str + "\\}", getBoldDetailInfo(paramVarArgs[Integer.parseInt(str)]));
/*      */       }
/*      */     } 
/*  164 */     return paramString;
/*      */   }
/*      */   
/*      */   public static String getTypeName(int paramInt1, String paramString, int paramInt2, int paramInt3, int paramInt4) {
/*  168 */     String str = "";
/*  169 */     if (paramString.equals("0") && paramInt2 != -1) {
/*  170 */       if (paramInt1 == 30 || paramInt1 == 1) {
/*  171 */         if (paramInt2 == 1) {
/*  172 */           str = " (" + SystemEnv.getHtmlLabelName(353, paramInt4) + ")";
/*  173 */         } else if (paramInt2 == 2) {
/*  174 */           str = " (" + SystemEnv.getHtmlLabelName(21473, paramInt4) + ")";
/*      */         } 
/*  176 */       } else if (paramInt1 == 2) {
/*  177 */         if (paramInt2 == 1) {
/*  178 */           str = " (" + SystemEnv.getHtmlLabelName(346, paramInt4) + ")";
/*  179 */         } else if (paramInt2 == 2) {
/*  180 */           str = " (" + SystemEnv.getHtmlLabelName(15507, paramInt4) + ")";
/*      */         } 
/*      */       } 
/*      */     }
/*      */     
/*  185 */     switch (paramInt1) {
/*      */       case 1:
/*  187 */         return SystemEnv.getHtmlLabelName(124, paramInt4) + str;
/*      */       case 2:
/*  189 */         return SystemEnv.getHtmlLabelName(122, paramInt4) + str;
/*      */       case 3:
/*  191 */         return SystemEnv.getHtmlLabelName(179, paramInt4);
/*      */       case 4:
/*  193 */         return SystemEnv.getHtmlLabelName(1340, paramInt4);
/*      */       case 5:
/*  195 */         return SystemEnv.getHtmlLabelName(15555, paramInt4);
/*      */       case 6:
/*  197 */         return SystemEnv.getHtmlLabelName(15559, paramInt4);
/*      */       case 7:
/*  199 */         return SystemEnv.getHtmlLabelName(15562, paramInt4);
/*      */       case 8:
/*  201 */         return SystemEnv.getHtmlLabelName(15564, paramInt4);
/*      */       case 9:
/*  203 */         return SystemEnv.getHtmlLabelName(15566, paramInt4);
/*      */       case 10:
/*  205 */         return SystemEnv.getHtmlLabelName(15567, paramInt4);
/*      */       case 11:
/*  207 */         return SystemEnv.getHtmlLabelName(15569, paramInt4);
/*      */       case 12:
/*  209 */         return SystemEnv.getHtmlLabelName(15570, paramInt4);
/*      */       case 13:
/*  211 */         return SystemEnv.getHtmlLabelName(15571, paramInt4);
/*      */       case 14:
/*  213 */         return SystemEnv.getHtmlLabelName(15573, paramInt4);
/*      */       case 15:
/*  215 */         return SystemEnv.getHtmlLabelName(15574, paramInt4);
/*      */       case 16:
/*  217 */         return SystemEnv.getHtmlLabelName(15575, paramInt4);
/*      */       case 17:
/*  219 */         return SystemEnv.getHtmlLabelName(15079, paramInt4);
/*      */       case 18:
/*  221 */         str = getVirtualname(paramInt3, paramInt4);
/*  222 */         return SystemEnv.getHtmlLabelName(15080, paramInt4) + ("".equals(str) ? "" : ("(" + str + ")"));
/*      */       case 19:
/*  224 */         str = getVirtualname(paramInt3, paramInt4);
/*  225 */         return SystemEnv.getHtmlLabelName(15081, paramInt4) + ("".equals(str) ? "" : ("(" + str + ")"));
/*      */       case 20:
/*  227 */         return SystemEnv.getHtmlLabelName(1282, paramInt4);
/*      */       case 21:
/*  229 */         return SystemEnv.getHtmlLabelName(15078, paramInt4);
/*      */       case 22:
/*  231 */         return SystemEnv.getHtmlLabelName(15579, paramInt4);
/*      */       case 23:
/*  233 */         return SystemEnv.getHtmlLabelName(1278, paramInt4);
/*      */       case 24:
/*  235 */         return SystemEnv.getHtmlLabelName(15580, paramInt4);
/*      */       case 25:
/*  237 */         return SystemEnv.getHtmlLabelName(15581, paramInt4);
/*      */       case 30:
/*  239 */         return SystemEnv.getHtmlLabelName(141, paramInt4) + str;
/*      */       case 31:
/*  241 */         return SystemEnv.getHtmlLabelName(15560, paramInt4);
/*      */       case 32:
/*  243 */         return SystemEnv.getHtmlLabelName(15561, paramInt4);
/*      */       case 33:
/*  245 */         return SystemEnv.getHtmlLabelName(15565, paramInt4);
/*      */       case 34:
/*  247 */         return SystemEnv.getHtmlLabelName(15568, paramInt4);
/*      */       case 35:
/*  249 */         return SystemEnv.getHtmlLabelName(15572, paramInt4);
/*      */       case 36:
/*  251 */         str = getVirtualname(paramInt3, paramInt4);
/*  252 */         return SystemEnv.getHtmlLabelName(15576, paramInt4) + ("".equals(str) ? "" : ("(" + str + ")"));
/*      */       case 37:
/*  254 */         str = getVirtualname(paramInt3, paramInt4);
/*  255 */         return SystemEnv.getHtmlLabelName(15577, paramInt4) + ("".equals(str) ? "" : ("(" + str + ")"));
/*      */       case 38:
/*  257 */         return SystemEnv.getHtmlLabelName(15563, paramInt4);
/*      */       case 39:
/*  259 */         str = getVirtualname(paramInt3, paramInt4);
/*  260 */         return SystemEnv.getHtmlLabelName(15578, paramInt4) + ("".equals(str) ? "" : ("(" + str + ")"));
/*      */       case 40:
/*  262 */         return SystemEnv.getHtmlLabelName(18676, paramInt4);
/*      */       case 41:
/*  264 */         return SystemEnv.getHtmlLabelName(18677, paramInt4);
/*      */       case 42:
/*  266 */         return SystemEnv.getHtmlLabelName(124, paramInt4);
/*      */       case 43:
/*  268 */         return SystemEnv.getHtmlLabelName(122, paramInt4);
/*      */       case 44:
/*  270 */         return SystemEnv.getHtmlLabelName(17204, paramInt4);
/*      */       case 45:
/*  272 */         return SystemEnv.getHtmlLabelName(18678, paramInt4);
/*      */       case 46:
/*  274 */         return SystemEnv.getHtmlLabelName(18679, paramInt4);
/*      */       case 47:
/*  276 */         return SystemEnv.getHtmlLabelName(18680, paramInt4);
/*      */       case 48:
/*  278 */         return SystemEnv.getHtmlLabelName(18681, paramInt4);
/*      */       case 49:
/*  280 */         return SystemEnv.getHtmlLabelName(19309, paramInt4);
/*      */       case 50:
/*  282 */         return SystemEnv.getHtmlLabelName(20570, paramInt4);
/*      */       case 51:
/*  284 */         return SystemEnv.getHtmlLabelName(141, paramInt4);
/*      */       case 52:
/*  286 */         return SystemEnv.getHtmlLabelName(27107, paramInt4);
/*      */       case 53:
/*  288 */         return SystemEnv.getHtmlLabelName(27108, paramInt4);
/*      */       case 54:
/*  290 */         return SystemEnv.getHtmlLabelName(27109, paramInt4);
/*      */       case 55:
/*  292 */         return SystemEnv.getHtmlLabelName(27110, paramInt4);
/*      */       case 56:
/*  294 */         return SystemEnv.getHtmlLabelName(26592, paramInt4);
/*      */       case 57:
/*  296 */         return SystemEnv.getHtmlLabelName(28442, paramInt4);
/*      */       case 99:
/*  298 */         return "" + SystemEnv.getHtmlLabelName(84494, ThreadVarLanguage.getLang()) + "";
/*      */     } 
/*  300 */     return "";
/*      */   }
/*      */ 
/*      */   
/*      */   public static String getVirtualname(int paramInt1, int paramInt2) {
/*  305 */     if (1 == paramInt1) {
/*  306 */       return SystemEnv.getHtmlLabelName(83179, paramInt2);
/*      */     }
/*      */     try {
/*  309 */       CompanyVirtualComInfo companyVirtualComInfo = new CompanyVirtualComInfo();
/*  310 */       return companyVirtualComInfo.getVirtualType(String.valueOf(paramInt1));
/*  311 */     } catch (Exception exception) {
/*  312 */       exception.printStackTrace();
/*      */ 
/*      */       
/*  315 */       return "";
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static String getBottomWorkflowInfo(String paramString, int paramInt1, User paramUser, int paramInt2) {
/*  327 */     String str1 = paramString.replaceAll("<span class=\"importantInfo\">", "~0~").replace("</span>", "~1~").replaceAll("<span class=\"importantDetailInfo\">", "~2~");
/*  328 */     String str2 = "";
/*  329 */     String str3 = "";
/*      */     
/*  331 */     String[] arrayOfString = getWFSetUrl(paramInt1, paramInt2, paramUser);
/*  332 */     str2 = arrayOfString[0];
/*  333 */     str3 = arrayOfString[1];
/*      */     
/*  335 */     if (paramInt1 == 3) {
/*      */       
/*  337 */       if (wfInterventionCompetence(paramUser, paramInt2)) {
/*  338 */         return paramString + "，" + SystemEnv.getHtmlLabelName(126554, paramUser.getLanguage()) + " <span class=\"sendMsgBtn\" onclick=\"resetWorkflow('" + str2 + "','" + str3 + "','" + paramInt1 + "')\" href=\"#\"> " + SystemEnv.getHtmlLabelName(126555, paramUser.getLanguage()) + " </span>" + SystemEnv.getHtmlLabelName(126556, paramUser.getLanguage());
/*      */       }
/*  340 */       return paramString + "，" + SystemEnv.getHtmlLabelName(126554, paramUser.getLanguage()) + " <span class=\"sendMsgBtn\" onclick=\"javascript:triggerSystemWorkflow('" + str1 + "','" + str2 + "','" + str3 + "','" + paramUser.getUID() + "','" + paramInt1 + "')\"> " + SystemEnv.getHtmlLabelName(126555, paramUser.getLanguage()) + " </span>" + SystemEnv.getHtmlLabelName(126557, paramUser.getLanguage());
/*      */     } 
/*      */     
/*  343 */     int i = paramUser.getUID();
/*  344 */     WfRightManager wfRightManager = new WfRightManager();
/*  345 */     boolean bool1 = wfRightManager.hasPermission3(paramInt2, 0, paramUser, 1);
/*  346 */     String str4 = "";
/*  347 */     int j = 0;
/*  348 */     WFManager wFManager = new WFManager();
/*  349 */     wFManager.setWfid(paramInt2);
/*      */     try {
/*  351 */       wFManager.getWfInfo();
/*  352 */       j = wFManager.getSubCompanyId2();
/*  353 */     } catch (Exception exception) {
/*  354 */       exception.printStackTrace();
/*      */     } 
/*  356 */     boolean bool2 = (new ManageDetachComInfo()).isUseWfManageDetach();
/*  357 */     int k = UserWFOperateLevel.checkUserWfOperateLevel(bool2 ? 1 : 0, j, paramUser, bool1, "WorkflowManage:All");
/*      */     
/*  359 */     if (paramInt1 == 1 || Util.null2String(SystemEnv.getHtmlLabelName(126540, paramUser.getLanguage())).equals(paramString)) {
/*  360 */       int m = -1;
/*  361 */       String str = "";
/*  362 */       if (k > 0) {
/*  363 */         m = 513918;
/*  364 */         str = " <span class=\"sendMsgBtn\" onclick=\"resetWorkflow('" + str2 + "','" + str3 + "','" + paramInt1 + "')\"> " + SystemEnv.getHtmlLabelName(126555, paramUser.getLanguage()) + " </span>";
/*      */       } else {
/*  366 */         m = 513919;
/*  367 */         str = " <span class=\"sendMsgBtn\" onclick=\"triggerSystemWorkflow('" + str1 + "','" + str2 + "','" + str3 + "','" + i + "','" + paramInt1 + "')\"> " + SystemEnv.getHtmlLabelName(126555, paramUser.getLanguage()) + " </span>";
/*      */       } 
/*  369 */       str4 = Util.null2String(SystemEnv.getHtmlLabelName(m, paramUser.getLanguage())).replace("#{?}", str);
/*      */     
/*      */     }
/*  372 */     else if (k > 0) {
/*  373 */       str4 = paramString + "，" + SystemEnv.getHtmlLabelName(126554, paramUser.getLanguage()) + " <span class=\"sendMsgBtn\" onclick=\"resetWorkflow('" + str2 + "','" + str3 + "','" + paramInt1 + "')\"> " + SystemEnv.getHtmlLabelName(126555, paramUser.getLanguage()) + " </span>" + SystemEnv.getHtmlLabelName(126558, paramUser.getLanguage());
/*      */     } else {
/*  375 */       str4 = paramString + "，" + SystemEnv.getHtmlLabelName(126554, paramUser.getLanguage()) + " <span class=\"sendMsgBtn\" onclick=\"triggerSystemWorkflow('" + str1 + "','" + str2 + "','" + str3 + "','" + i + "','" + paramInt1 + "')\"> " + SystemEnv.getHtmlLabelName(126555, paramUser.getLanguage()) + " </span>" + SystemEnv.getHtmlLabelName(126559, paramUser.getLanguage());
/*      */     } 
/*      */     
/*  378 */     return str4;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private static boolean wfInterventionCompetence(User paramUser, int paramInt) {
/*  389 */     RecordSet recordSet = new RecordSet();
/*  390 */     int i = 0;
/*  391 */     int j = 0;
/*  392 */     int k = 0;
/*  393 */     String str = "";
/*  394 */     recordSet.executeQuery("select currentstatus,creater,currentnodetype,workflowid from workflow_requestbase where requestid = ?", new Object[] { Integer.valueOf(paramInt) });
/*  395 */     if (recordSet.next()) {
/*  396 */       j = Util.getIntValue(recordSet.getString("currentstatus"), -1);
/*  397 */       k = recordSet.getInt("currentnodetype");
/*  398 */       i = recordSet.getInt("creater");
/*  399 */       str = recordSet.getString("workflowid");
/*      */     } 
/*      */     
/*  402 */     Monitor monitor = new Monitor();
/*  403 */     MonitorDTO monitorDTO = monitor.getMonitorInfo(paramUser.getUID() + "", i + "", str);
/*  404 */     boolean bool1 = monitorDTO.getIsintervenor();
/*  405 */     boolean bool2 = monitorDTO.getIssooperator();
/*      */     
/*  407 */     boolean bool = false;
/*  408 */     recordSet.executeSql("select isoverrb,isoveriv from workflow_base where id=" + str);
/*  409 */     if (recordSet.next() && 
/*  410 */       Util.null2String(recordSet.getString("isoveriv")).equals("1")) {
/*  411 */       bool = true;
/*      */     }
/*      */ 
/*      */     
/*  415 */     if (k == 3) {
/*  416 */       bool2 = false;
/*      */     }
/*  418 */     WfFunctionManageUtil wfFunctionManageUtil = new WfFunctionManageUtil();
/*  419 */     boolean bool3 = wfFunctionManageUtil.haveRestartright2(j, i, paramUser, "" + k, -1, bool2, str);
/*  420 */     if (!bool3 && 
/*  421 */       GCONST.getWorkflowIntervenorByMonitor() && 
/*  422 */       bool1) {
/*  423 */       return (bool || k != 3);
/*      */     }
/*      */ 
/*      */     
/*  427 */     return false;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static String[] resolveSystemwfInfo(String paramString) {
/*  436 */     String[] arrayOfString = { "", "" };
/*      */     try {
/*  438 */       if (StringUtils.isNotEmpty(paramString)) {
/*  439 */         JSONObject jSONObject = new JSONObject(paramString);
/*  440 */         if (jSONObject.has("details")) {
/*  441 */           String str = jSONObject.getString("details");
/*  442 */           str = "<div class=\"message-detail\">" + str + "</div>";
/*  443 */           arrayOfString[0] = str;
/*      */         } 
/*  445 */         if (jSONObject.has("resetoperator")) {
/*  446 */           arrayOfString[1] = "y";
/*      */         }
/*      */       } 
/*  449 */     } catch (JSONException jSONException) {}
/*      */     
/*  451 */     return arrayOfString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static String getBoldInfo(String paramString) {
/*  460 */     if (StringUtil.isNotNull(paramString)) {
/*  461 */       return "<span class=\"importantInfo\">" + paramString + "</span>";
/*      */     }
/*  463 */     return "";
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static String getBoldDetailInfo(String paramString) {
/*  471 */     if (StringUtil.isNotNull(paramString)) {
/*  472 */       return "<span class=\"importantDetailInfo\">" + paramString + "</span>";
/*      */     }
/*  474 */     return "";
/*      */   }
/*      */   
/*      */   public static String resolveDetailInfo(String paramString) {
/*  478 */     JSONObject jSONObject = new JSONObject();
/*      */     try {
/*  480 */       jSONObject.put("details", paramString);
/*  481 */     } catch (JSONException jSONException) {
/*  482 */       jSONException.printStackTrace();
/*      */     } 
/*  484 */     return jSONObject.toString();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static JSONObject getBottomInfo(String paramString, int paramInt1, int paramInt2) {
/*  496 */     JSONObject jSONObject = new JSONObject();
/*      */     try {
/*  498 */       jSONObject.put("bottomprefix", paramString);
/*  499 */       jSONObject.put("msgurlparm", paramInt1);
/*  500 */       jSONObject.put("msgtype", paramInt2);
/*  501 */     } catch (Exception exception) {}
/*      */ 
/*      */     
/*  504 */     return jSONObject;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static void getDetailInfomx(StringBuffer paramStringBuffer, String paramString1, String paramString2, String paramString3, String paramString4, int paramInt1, int paramInt2) {
/*  519 */     int i = Util.getIntValue((new BaseBean()).getPropValue("workflow_Message", "REQUESTMESSAGELENGTH"), 5000);
/*  520 */     if (paramStringBuffer.length() > i) {
/*      */       return;
/*      */     }
/*  523 */     paramStringBuffer.append("<span class=\"detail-info-icon\"></span><span>");
/*  524 */     if (StringUtil.isNotNull(paramString1)) {
/*  525 */       paramStringBuffer.append(SystemEnv.getHtmlLabelName(15072, paramInt2)).append(getBoldInfo(paramString1));
/*      */     }
/*  527 */     if (StringUtil.isNotNull(paramString2)) {
/*  528 */       paramStringBuffer.append(SystemEnv.getHtmlLabelName(17892, paramInt2)).append(getBoldInfo(paramString2));
/*      */     }
/*  530 */     if (StringUtil.isNotNull(paramString3)) {
/*  531 */       paramStringBuffer.append(paramString3).append("</span><br/>");
/*      */     }
/*  533 */     if (StringUtil.isNotNull(paramString4)) {
/*  534 */       paramString4 = SystemEnv.getHtmlLabelName(125506, paramInt2) + "：" + paramString4.replaceAll("AND", "AND<br/>").replaceAll("OR", "OR<br/>");
/*  535 */       paramStringBuffer.append("<span class=\"condition\" index=\"" + paramInt1 + "\">").append(SystemEnv.getHtmlLabelName(126542, paramInt2)).append("</span>").append(SystemEnv.getHtmlLabelName(126543, paramInt2)).append("</span><br/>");
/*  536 */       paramStringBuffer.append("<div id=\"condit").append(paramInt1).append("\" class=\"message-detail-condition\">").append(paramString4).append("</div>");
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static String[] getWFSetUrl(int paramInt1, int paramInt2, User paramUser) {
/*  550 */     String[] arrayOfString = new String[2];
/*  551 */     if (paramInt1 == 1) {
/*  552 */       arrayOfString[0] = "" + paramInt2;
/*  553 */       arrayOfString[1] = SystemEnv.getHtmlLabelName(126552, paramUser.getLanguage());
/*  554 */     } else if (paramInt1 == 2) {
/*  555 */       arrayOfString[0] = "" + paramInt2;
/*  556 */       arrayOfString[1] = SystemEnv.getHtmlLabelName(126553, paramUser.getLanguage());
/*  557 */     } else if (paramInt1 == 3) {
/*  558 */       arrayOfString[0] = "/workflow/request/ViewRequestForwardSPA.jsp?isintervenor=1&requestid=" + paramInt2;
/*  559 */       arrayOfString[1] = SystemEnv.getHtmlLabelName(18913, paramUser.getLanguage());
/*  560 */     } else if (paramInt1 == 4) {
/*  561 */       RecordSet recordSet = new RecordSet();
/*  562 */       recordSet.executeProc("workflow_Workflowbase_SByID", paramInt2 + "");
/*  563 */       if (recordSet.next()) {
/*  564 */         int i = Util.getIntValue(recordSet.getString("formid"), 0);
/*  565 */         int j = Util.getIntValue(recordSet.getString("isbill"), 0);
/*  566 */         if (j == 1) {
/*  567 */           arrayOfString[0] = "/workflow/form/editformfield.jsp?formid=" + i;
/*      */         } else {
/*  569 */           arrayOfString[0] = "/workflow/form/addformfield.jsp?formid=" + i;
/*      */         } 
/*      */       } else {
/*  572 */         arrayOfString[0] = "/workflow/form/addformfield.jsp?formid=-1";
/*      */       } 
/*  574 */       arrayOfString[1] = SystemEnv.getHtmlLabelName(15449, paramUser.getLanguage());
/*      */     } else {
/*  576 */       arrayOfString[0] = "";
/*  577 */       arrayOfString[1] = "";
/*      */     } 
/*  579 */     return arrayOfString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static String getClientInfoHtml(String paramString1, String paramString2, User paramUser) {
/*  588 */     StringBuffer stringBuffer = new StringBuffer();
/*      */     try {
/*  590 */       String str = getNewMessageId(paramString1, paramUser.getLanguage());
/*  591 */       if ("null".equals(str) || StringUtils.isEmpty(str)) {
/*  592 */         return "";
/*      */       }
/*      */       
/*  595 */       stringBuffer.append("<div class=\"message-padding-div\"></div><div class=\"message-box\">");
/*  596 */       stringBuffer.append("<div class=\"message-title-div\">");
/*  597 */       stringBuffer.append("<span class=\"message-title-icon\"></span>");
/*  598 */       stringBuffer.append("<span class=\"message-title\">");
/*  599 */       stringBuffer.append(str);
/*  600 */       stringBuffer.append("</span></div>");
/*      */       
/*  602 */       if (StringUtils.isNotEmpty(paramString2)) {
/*  603 */         JSONObject jSONObject = new JSONObject(paramString2);
/*  604 */         if (jSONObject.has("details")) {
/*  605 */           stringBuffer.append(" <div class=\"message-detail\">");
/*  606 */           String str1 = jSONObject.getString("details");
/*  607 */           str1 = str1.replaceAll("<span>", "").replaceAll("&nbsp;", "");
/*  608 */           Pattern pattern1 = Pattern.compile("<span class=\"condition\".*?index=\"(\\d+)\".*?</span>");
/*  609 */           Pattern pattern2 = Pattern.compile("<div id=\"condit(.*?)\".*class=\"message-detail-condition\">(.*)</div>");
/*  610 */           Pattern pattern3 = Pattern.compile("<span class=\"detail-info-icon\"></span>");
/*  611 */           String[] arrayOfString = str1.split("</span><br/>");
/*      */           
/*  613 */           for (String str2 : arrayOfString) {
/*  614 */             Matcher matcher1 = pattern2.matcher(str2);
/*  615 */             if (matcher1.find()) {
/*  616 */               stringBuffer.append("<div id=\"condition" + matcher1.group(1) + "\" class=\"message-detail-condition\"><div index=\"conditiondetail\">").append(matcher1.group(2)).append("</div></div>");
/*  617 */               str2 = matcher1.replaceAll("");
/*      */             } 
/*      */             
/*  620 */             Matcher matcher2 = pattern3.matcher(str2);
/*  621 */             if (matcher2.find()) {
/*  622 */               str2 = matcher2.replaceAll("<span class=\"detail-info-icon\"><img src=\"/mobile/plugin/1/images/detail-icon.png\"></span>");
/*      */             }
/*      */             
/*  625 */             Matcher matcher3 = pattern1.matcher(str2);
/*  626 */             if (matcher3.find()) {
/*  627 */               str2 = matcher3.replaceAll("<a index=\"" + matcher3.group(1) + "\" ontouchend=\"opencondition('condition" + matcher3.group(1) + "')\">" + SystemEnv.getHtmlLabelName(126542, paramUser.getLanguage()) + "</a>");
/*  628 */               stringBuffer.append("<div class=\"message-detail-info\">").append(str2).append("</div>");
/*      */             } else {
/*  630 */               stringBuffer.append("<div class=\"message-detail-info\">").append(str2).append("</div>");
/*      */             } 
/*      */           } 
/*  633 */           stringBuffer.append("</div>");
/*      */         } 
/*      */         
/*  636 */         if (jSONObject.has("bottomprefix")) {
/*  637 */           String str1 = jSONObject.getString("bottomprefix");
/*  638 */           int i = jSONObject.getInt("msgtype");
/*  639 */           int j = jSONObject.getInt("msgurlparm");
/*      */           
/*  641 */           String str2 = str1.replaceAll("<span class=\"importantInfo\">", "~0~").replaceAll("</span>", "~1~").replaceAll("<span class=\"importantDetailInfo\">", "~2~");
/*  642 */           String[] arrayOfString = getWFSetUrl(i, j, paramUser);
/*  643 */           String str3 = "'" + str2 + "','" + arrayOfString[0] + "','" + arrayOfString[1] + "','" + paramUser.getUID() + "','" + i + "'";
/*      */           
/*  645 */           stringBuffer.append("<div class=\"message-bottom\">");
/*  646 */           stringBuffer.append("<span>").append(str1).append("</span>");
/*  647 */           stringBuffer.append("</div>");
/*  648 */           stringBuffer.append("<div class=\"message-button-div\">");
/*  649 */           stringBuffer.append("<a class=\"message-button\" ontouchend=\"triggerSystemWorkflow(").append(str3).append(")\">");
/*  650 */           stringBuffer.append(SystemEnv.getHtmlLabelName(126364, paramUser.getLanguage()));
/*  651 */           stringBuffer.append("</a>");
/*  652 */           stringBuffer.append("</div>");
/*      */         } 
/*      */         
/*  655 */         if (jSONObject.has("resetoperator")) {
/*  656 */           stringBuffer.append("<div class=\"message-detail\">");
/*  657 */           stringBuffer.append("<div class=\"message-detail-info\">").append(SystemEnv.getHtmlLabelName(126560, paramUser.getLanguage()));
/*  658 */           stringBuffer.append("，" + SystemEnv.getHtmlLabelName(126554, paramUser.getLanguage()) + "<a href=\"#\" ontouchend=\"rechoseoperator()\"> " + SystemEnv.getHtmlLabelName(126555, paramUser.getLanguage()) + " </a>" + SystemEnv.getHtmlLabelName(126561, paramUser.getLanguage()) + "</div>");
/*  659 */           stringBuffer.append("</div>");
/*      */         } 
/*      */       } 
/*  662 */       stringBuffer.append("<div class=\"message-padding-div\"></div>");
/*  663 */     } catch (JSONException jSONException) {
/*  664 */       stringBuffer.append(" <div class=\"message-detail\">");
/*  665 */       stringBuffer.append(" <div class=\"message-detail-info\">");
/*  666 */       stringBuffer.append(paramString2);
/*  667 */       stringBuffer.append("</div></div>");
/*      */     } 
/*  669 */     return stringBuffer.toString();
/*      */   }
/*      */   
/*      */   public static void getNodeErrorMsgTitle(StringBuffer paramStringBuffer, String paramString, int paramInt) {
/*  673 */     paramStringBuffer.append("<span>").append(getBoldInfo(paramString)).append(SystemEnv.getHtmlLabelName(126546, paramInt)).append("：</span><br/>");
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static String linkConditionNSInfo(ArrayList paramArrayList1, ArrayList paramArrayList2, ArrayList<String> paramArrayList3, RecordSet paramRecordSet, int paramInt1, int paramInt2, int paramInt3, StringBuffer paramStringBuffer, int paramInt4, ArrayList paramArrayList4) {
/*      */     try {
/*  684 */       String str1 = getWorkflowNodename(paramInt1);
/*  685 */       String str2 = "";
/*      */       
/*  687 */       JSONObject jSONObject = getBottomInfo(SystemEnv.getHtmlLabelName(126540, paramInt3), paramInt2, 2);
/*  688 */       paramStringBuffer.append("<span>").append(SystemEnv.getHtmlLabelName(126541, paramInt3)).append("：</span><br/>");
/*  689 */       for (byte b = 0; b < paramArrayList2.size(); b++) {
/*  690 */         if (paramArrayList4.indexOf("" + paramArrayList2.get(b)) > -1) {
/*  691 */           paramStringBuffer.setLength(0);
/*  692 */           paramStringBuffer.append("<span>").append(SystemEnv.getHtmlLabelName(126541, paramInt3)).append("：</span><br/>");
/*      */         } 
/*  694 */         str2 = getWorkflowNodename(Integer.parseInt(String.valueOf(paramArrayList2.get(b))));
/*      */         
/*  696 */         int i = 1;
/*  697 */         String str3 = "select ruleRelationship from workflow_nodelink where id = " + paramArrayList3.get(b);
/*  698 */         paramRecordSet.executeSql(str3);
/*  699 */         if (paramRecordSet.next()) {
/*  700 */           i = Util.getIntValue(Util.null2String(paramRecordSet.getString("ruleRelationship")), 1);
/*      */         }
/*  702 */         String str4 = (1 == i) ? "AND" : "OR";
/*      */         
/*  704 */         paramRecordSet.execute("select t2.condit from rule_maplist t1,rule_base  t2 where t1.ruleid = t2.id and (t1.rulesrc=1 or t1.rulesrc=-1) and t1.linkid= " + paramArrayList3.get(b));
/*  705 */         String str5 = "";
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */         
/*  713 */         User user = new User(1);
/*  714 */         user.setUseAttrLanguage(true);
/*  715 */         user.setLanguage(paramInt3);
/*  716 */         str5 = RuleBusiness.getConditionCn(Util.getIntValue(Util.null2String(paramArrayList3.get(b))), 1, user);
/*  717 */         jSONObject.put("condit" + paramInt4, str5);
/*  718 */         paramStringBuffer.append("<span class=\"detail-info-icon\"></span><span>").append(getBoldInfo(str1));
/*  719 */         paramStringBuffer.append(SystemEnv.getHtmlLabelName(33417, paramInt3)).append("->").append(getBoldInfo(str2));
/*  720 */         paramStringBuffer.append(SystemEnv.getHtmlLabelName(33417, paramInt3)).append("，").append(SystemEnv.getHtmlLabelName(33413, paramInt3)).append("：");
/*  721 */         paramStringBuffer.append("<span class=\"condition\" index=\"" + paramInt4 + "\">").append(SystemEnv.getHtmlLabelName(126542, paramInt3)).append("</span>").append(SystemEnv.getHtmlLabelName(126543, paramInt3)).append("</span><br/>");
/*      */         
/*  723 */         str5 = SystemEnv.getHtmlLabelName(33413, paramInt3) + "：" + str5.replaceAll("AND", "AND<br/>").replaceAll("OR", "OR<br/>");
/*  724 */         paramStringBuffer.append("<div id=\"condit").append(paramInt4).append("\" class=\"message-detail-condition\">").append(str5).append("</div>");
/*  725 */         paramInt4++;
/*  726 */         if (paramArrayList4.indexOf("" + paramArrayList2.get(b)) > -1) {
/*      */           break;
/*      */         }
/*      */       } 
/*      */       
/*  731 */       jSONObject.put("details", paramStringBuffer.toString());
/*  732 */       return jSONObject.toString();
/*  733 */     } catch (Exception exception) {
/*  734 */       return "";
/*      */     } 
/*      */   }
/*      */   
/*      */   public static String getWorkflowNodename(int paramInt) {
/*  739 */     return WorkflowBaseBiz.getWorkflowNodeName(paramInt);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static String getRMOperatorOutInfo(int paramInt1, int paramInt2, int paramInt3, String paramString) {
/*  751 */     StringBuffer stringBuffer = new StringBuffer();
/*  752 */     String str = getWorkflowNodename(paramInt1);
/*  753 */     stringBuffer.append("<span>").append(getBoldInfo(str)).append(SystemEnv.getHtmlLabelName(126546, paramInt2) + "：").append("</span><br/>");
/*  754 */     JSONObject jSONObject = getBottomInfo(SystemEnv.getHtmlLabelName(126540, paramInt2), paramInt3, 1);
/*  755 */     stringBuffer.append("<span>").append(getBoldInfo(paramString)).append(SystemEnv.getHtmlLabelName(126527, paramInt2) + "</span></br>");
/*      */     try {
/*  757 */       jSONObject.put("details", stringBuffer.toString());
/*  758 */     } catch (JSONException jSONException) {
/*  759 */       jSONException.printStackTrace();
/*      */     } 
/*  761 */     return jSONObject.toString();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static boolean checkBillFieldAndFMTableField(int paramInt1, String paramString1, String paramString2, int paramInt2, Map<String, String> paramMap, String paramString3) {
/*  774 */     RecordSet recordSet1 = new RecordSet();
/*  775 */     boolean bool1 = recordSet1.getDBType().equals("oracle");
/*  776 */     StringBuffer stringBuffer = new StringBuffer();
/*  777 */     if (paramInt2 == 1) {
/*  778 */       if (bool1) {
/*  779 */         stringBuffer.append("select fieldname,fielddbtype,fieldhtmltype,a.type from workflow_billfield a where billid =").append(paramInt1);
/*  780 */         if ("".equals(paramString2)) {
/*  781 */           stringBuffer.append(" and detailtable is null ");
/*      */         } else {
/*  783 */           stringBuffer.append(" and detailtable = '").append(paramString2).append("'");
/*      */         } 
/*  785 */         stringBuffer.append(" and not exists (select 1 from user_tab_columns b where b.table_name = upper('");
/*  786 */         if ("".equals(paramString2)) {
/*  787 */           stringBuffer.append(paramString1);
/*      */         } else {
/*  789 */           stringBuffer.append(paramString2);
/*      */         } 
/*  791 */         stringBuffer.append("') and upper(a.fieldname) = b.column_name)");
/*  792 */         recordSet1.execute(stringBuffer.toString());
/*  793 */       } else if (recordSet1.getDBType().equals("mysql")) {
/*  794 */         String str = getMysqlCurrentDBName();
/*  795 */         stringBuffer.append("select fieldname,fielddbtype,fieldhtmltype,a.type from workflow_billfield a where billid =").append(paramInt1);
/*  796 */         if ("".equals(paramString2)) {
/*  797 */           stringBuffer.append(" and (detailtable is null or detailtable = '') ");
/*      */         } else {
/*  799 */           stringBuffer.append(" and detailtable = '").append(paramString2).append("'");
/*      */         } 
/*  801 */         stringBuffer.append(" and not exists (select 1 from Information_schema.columns b where b.table_schema = database() and b.table_name = upper('");
/*  802 */         if ("".equals(paramString2)) {
/*  803 */           stringBuffer.append(paramString1);
/*      */         } else {
/*  805 */           stringBuffer.append(paramString2);
/*      */         } 
/*  807 */         stringBuffer.append("') and upper(a.fieldname) = b.column_name and b.table_schema = '" + str + "')");
/*  808 */         recordSet1.execute(stringBuffer.toString());
/*      */       }
/*  810 */       else if (recordSet1.getDBType().equals("postgresql")) {
/*  811 */         stringBuffer.append("select fieldname,fielddbtype,fieldhtmltype,a.type from workflow_billfield a where billid =").append(paramInt1);
/*  812 */         if ("".equals(paramString2)) {
/*  813 */           stringBuffer.append(" and (detailtable is null or detailtable = '') ");
/*      */         } else {
/*  815 */           stringBuffer.append(" and detailtable = '").append(paramString2).append("'");
/*      */         } 
/*  817 */         stringBuffer.append(" and not exists (select 1 from Information_schema.columns b where b.table_schema = 'public' and b.table_name = upper('");
/*  818 */         if ("".equals(paramString2)) {
/*  819 */           stringBuffer.append(paramString1);
/*      */         } else {
/*  821 */           stringBuffer.append(paramString2);
/*      */         } 
/*  823 */         stringBuffer.append("') and upper(a.fieldname) = b.column_name and b.table_schema = 'public')");
/*  824 */         recordSet1.execute(stringBuffer.toString());
/*      */       } else {
/*      */         
/*  827 */         stringBuffer.append("select fieldname,fielddbtype,fieldhtmltype,a.type from workflow_billfield a where billid = ? and detailtable = ? and not exists (select 1 from syscolumns b where id = OBJECT_ID(?) AND b.name = a.fieldname)");
/*  828 */         if ("".equals(paramString2)) {
/*  829 */           recordSet1.executeQuery(stringBuffer.toString(), new Object[] { Integer.valueOf(paramInt1), paramString2, paramString1 });
/*      */         } else {
/*  831 */           recordSet1.executeQuery(stringBuffer.toString(), new Object[] { Integer.valueOf(paramInt1), paramString2, paramString2 });
/*      */         } 
/*      */       } 
/*      */     } else {
/*  835 */       String str3 = "workflow_formdict";
/*  836 */       String str4 = "workflow_form";
/*  837 */       if (StringUtil.isNotNull(paramString2)) {
/*  838 */         str3 = "workflow_formdictdetail";
/*  839 */         str4 = "workflow_formdetail";
/*      */       } 
/*  841 */       stringBuffer.append("select b.fieldname,b.fielddbtype,b.fieldhtmltype,b.type from workflow_formfield a,").append(str3);
/*  842 */       stringBuffer.append(" b where a.fieldid = b.id ");
/*  843 */       if (StringUtil.isNotNull(paramString2)) {
/*  844 */         stringBuffer.append(" and a.isdetail = 1");
/*      */       } else {
/*  846 */         stringBuffer.append(" and a.isdetail is null");
/*      */       } 
/*  848 */       if (bool1) {
/*  849 */         stringBuffer.append(" and not exists (select 1 from user_tab_columns c where c.table_name = '").append(str4.toUpperCase()).append("' and upper(b.fieldname) = c.column_name)");
/*  850 */       } else if (recordSet1.getDBType().equals("mysql")) {
/*  851 */         String str = getMysqlCurrentDBName();
/*  852 */         stringBuffer.append(" and not exists (select 1 from Information_schema.columns c where c.table_schema = database() and c.table_name = '").append(str4.toUpperCase()).append("' and upper(b.fieldname) = c.column_name and c.table_schema = '" + str + "')");
/*      */       }
/*  854 */       else if (recordSet1.getDBType().equals("postgresql")) {
/*      */         
/*  856 */         stringBuffer.append(" and not exists (select 1 from Information_schema.columns c where c.table_schema = 'public' and c.table_name = lower('").append(str4.toUpperCase()).append("') and upper(b.fieldname) = c.column_name )");
/*      */       } else {
/*      */         
/*  859 */         stringBuffer.append(" and not exists (select 1 from syscolumns c where id = OBJECT_ID('").append(str4).append("') AND c.name = b.fieldname)");
/*      */       } 
/*  861 */       stringBuffer.append(" and a.formid = ?  ");
/*  862 */       recordSet1.executeQuery(stringBuffer.toString(), new Object[] { Integer.valueOf(paramInt1) });
/*      */     } 
/*  864 */     LogFactory.getLog(RequestManager.class).error(stringBuffer.toString());
/*  865 */     stringBuffer.setLength(0);
/*  866 */     boolean bool2 = true;
/*  867 */     RecordSet recordSet2 = new RecordSet();
/*  868 */     String str1 = "";
/*  869 */     if (paramInt2 == 1) {
/*  870 */       if ("".equals(paramString2)) {
/*  871 */         str1 = paramString1;
/*      */       } else {
/*  873 */         str1 = paramString2;
/*      */       }
/*      */     
/*  876 */     } else if (StringUtil.isNotNull(paramString2)) {
/*  877 */       str1 = "workflow_formdetail";
/*      */     } else {
/*  879 */       str1 = "workflow_form";
/*      */     } 
/*      */     
/*  882 */     while (recordSet1.next()) {
/*  883 */       String str = recordSet1.getString("fielddbtype");
/*  884 */       if (recordSet1.getDBType().equals("mysql") && 
/*  885 */         str.indexOf("varchar2") != -1) {
/*  886 */         String str3 = StringUtils.substringBefore(str, ")");
/*  887 */         str3 = StringUtils.substringAfter(str3, "(");
/*      */         try {
/*  889 */           int i = Integer.parseInt(str3);
/*  890 */           if (i >= 1000) {
/*  891 */             str = "text";
/*      */           } else {
/*  893 */             str = "char(" + i + ")";
/*      */           } 
/*  895 */         } catch (Exception exception) {
/*  896 */           (new BaseBean()).writeLog(exception);
/*  897 */           str.replace("char2", "char");
/*      */         } 
/*      */       } 
/*      */       
/*  901 */       if ("3".equals(recordSet1.getString("fieldhtmltype"))) {
/*  902 */         String str3 = getFielddbtype(recordSet1.getInt("type"), recordSet1);
/*  903 */         if (StringUtil.isNotNull(str3)) {
/*  904 */           str = str3;
/*      */         }
/*      */       } 
/*  907 */       stringBuffer.append("alter table ").append(str1).append(" add ").append(recordSet1.getString("fieldname")).append(" ").append(str);
/*  908 */       LogFactory.getLog(RequestManager.class).error(stringBuffer.toString());
/*  909 */       bool2 = recordSet2.execute(stringBuffer.toString());
/*  910 */       if (!bool2) {
/*      */         break;
/*      */       }
/*  913 */       stringBuffer.setLength(0);
/*      */     } 
/*  915 */     String str2 = Util.null2String((new BaseBean()).getPropValue("workflowsave", "AUTOALTERTABLECOLUMNLEN"));
/*  916 */     if (paramMap != null && "1".equals(str2)) {
/*  917 */       bool2 = checkValueLen(paramMap, paramString3, str1);
/*      */     }
/*  919 */     return bool2;
/*      */   }
/*      */   private static String getFielddbtype(int paramInt, RecordSet paramRecordSet) {
/*  922 */     boolean bool1 = "oracle".equalsIgnoreCase(paramRecordSet.getDBType());
/*  923 */     boolean bool2 = "db2".equalsIgnoreCase(paramRecordSet.getDBType());
/*  924 */     String str = "";
/*  925 */     if (paramInt == 118)
/*  926 */       if (bool1) { str = "varchar2(200)"; }
/*  927 */       else { str = "varchar(200)"; }
/*      */        
/*  929 */     if (paramInt == 161 || paramInt == 162) {
/*  930 */       if (paramInt == 161)
/*  931 */       { if (bool1) { str = "varchar2(1000)"; }
/*  932 */         else if (bool2) { str = "varchar(1000)"; }
/*  933 */         else { str = "varchar(1000)"; }
/*      */          }
/*  935 */       else if (bool1) { str = "varchar2(4000)"; }
/*  936 */       else if (bool2) { str = "varchar(2000)"; }
/*  937 */       else { str = "text"; }
/*      */     
/*      */     }
/*  940 */     if (paramInt == 256 || paramInt == 257) {
/*  941 */       if (paramInt == 256)
/*  942 */       { if (bool1) { str = "varchar2(1000)"; }
/*  943 */         else if (bool2) { str = "varchar(1000)"; }
/*  944 */         else { str = "varchar(1000)"; }
/*      */          }
/*  946 */       else if (bool1) { str = "varchar2(4000)"; }
/*  947 */       else if (bool2) { str = "varchar(2000)"; }
/*  948 */       else { str = "varchar(4000)"; }
/*      */     
/*      */     }
/*  951 */     if (paramInt == 224 || paramInt == 225) {
/*  952 */       if (paramInt == 224)
/*  953 */       { if (bool1) { str = "varchar2(1000)"; }
/*  954 */         else if (bool2) { str = "varchar(1000)"; }
/*  955 */         else { str = "varchar(1000)"; }
/*      */          }
/*  957 */       else if (bool1) { str = "varchar2(4000)"; }
/*  958 */       else if (bool2) { str = "varchar(2000)"; }
/*  959 */       else { str = "text"; }
/*      */     
/*      */     }
/*  962 */     if (paramInt == 226 || paramInt == 227) {
/*  963 */       if (paramInt == 226)
/*  964 */       { if (bool1) { str = "varchar2(1000)"; }
/*  965 */         else if (bool2) { str = "varchar(1000)"; }
/*  966 */         else { str = "varchar(1000)"; }
/*      */          }
/*  968 */       else if (bool1) { str = "varchar2(4000)"; }
/*  969 */       else if (bool2) { str = "varchar(2000)"; }
/*  970 */       else { str = "text"; }
/*      */     
/*      */     }
/*  973 */     if (paramInt == 17) {
/*  974 */       if (bool1) {
/*  975 */         str = "clob";
/*  976 */       } else if (bool2) {
/*  977 */         str = "varchar(2000)";
/*      */       } else {
/*      */         
/*  980 */         str = "text";
/*      */       } 
/*      */     }
/*  983 */     return str;
/*      */   }
/*      */   public static boolean checkValueLen(Map<String, String> paramMap, String paramString1, String paramString2) {
/*  986 */     boolean bool = true;
/*  987 */     Iterator<String> iterator = paramMap.keySet().iterator();
/*  988 */     Map<String, Integer> map = getTableUpdateColumnLog(paramString2);
/*  989 */     while (iterator.hasNext()) {
/*  990 */       String str = iterator.next();
/*  991 */       Pattern pattern = Pattern.compile(str + " = '(.*?)'");
/*  992 */       Matcher matcher = pattern.matcher(paramString1);
/*  993 */       if (matcher.find()) {
/*  994 */         String str1 = matcher.group(1);
/*  995 */         int i = StringUtil.getStringLengthUTF8(str1);
/*  996 */         Integer integer = map.get(str);
/*  997 */         boolean bool1 = (integer == null) ? true : false;
/*  998 */         if (integer == null) {
/*  999 */           Matcher matcher1 = FIELDDBTYPE_PATTERN.matcher(paramMap.get(str));
/* 1000 */           if (matcher1.find()) {
/* 1001 */             integer = Integer.valueOf(Integer.parseInt(matcher1.group(1)));
/*      */           }
/*      */         } 
/* 1004 */         if (i > integer.intValue()) {
/* 1005 */           bool = alterTableColumnLen(i, str, paramMap.get(str), paramString2, bool1);
/*      */         }
/*      */       } 
/*      */     } 
/* 1009 */     return bool;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static boolean alterTableColumnLen(int paramInt, String paramString1, String paramString2, String paramString3, boolean paramBoolean) {
/* 1021 */     boolean bool = true;
/*      */     try {
/* 1023 */       RecordSet recordSet = new RecordSet();
/* 1024 */       boolean bool1 = recordSet.getDBType().equals("oracle");
/* 1025 */       paramString2 = paramString2.replaceAll("\\(\\d+\\)", "(" + paramInt + ")");
/* 1026 */       String str = "";
/*      */       
/* 1028 */       if (bool1) {
/* 1029 */         str = "alter table " + paramString3 + " modify ( " + paramString1 + " " + paramString2 + " )";
/* 1030 */       } else if (recordSet.getDBType().equals("mysql")) {
/* 1031 */         str = "alter table " + paramString3 + " modify column " + paramString1 + " " + paramString2;
/* 1032 */       } else if (recordSet.getDBType().equals("postgresql")) {
/* 1033 */         str = "alter table " + paramString3 + " alter column " + paramString1 + " type " + paramString2;
/*      */       } else {
/*      */         
/* 1036 */         str = "alter table " + paramString3 + " alter column " + paramString1 + " " + paramString2;
/*      */       } 
/* 1038 */       LOG.error(str);
/* 1039 */       recordSet.execute(str);
/* 1040 */       if (paramBoolean) {
/* 1041 */         str = "insert into workflow_updateColumnLenLog(tablename,tablecolumn,columnLength) values ('" + paramString3 + "','" + paramString1 + "','" + paramInt + "')";
/*      */       } else {
/* 1043 */         str = "update workflow_updateColumnLenLog set columnLength = '" + paramInt + "' where tablename = '" + paramString3 + "' and tablecolumn = '" + paramString1 + "'";
/*      */       } 
/* 1045 */       LOG.error(str);
/* 1046 */       recordSet.execute(str);
/* 1047 */     } catch (Exception exception) {
/* 1048 */       LOG.error(exception);
/* 1049 */       bool = false;
/*      */     } 
/* 1051 */     return bool;
/*      */   }
/*      */   private static Map<String, Integer> getTableUpdateColumnLog(String paramString) {
/* 1054 */     String str = "select tablecolumn,columnLength from workflow_updateColumnLenLog where tablename = '" + paramString + "'";
/* 1055 */     RecordSet recordSet = new RecordSet();
/* 1056 */     recordSet.executeSql(str);
/* 1057 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 1058 */     while (recordSet.next()) {
/* 1059 */       hashMap.put(recordSet.getString(1), Integer.valueOf(Integer.parseInt(recordSet.getString(2))));
/*      */     }
/* 1061 */     return (Map)hashMap;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static String getMysqlCurrentDBName() {
/* 1070 */     RecordSet recordSet = new RecordSet();
/* 1071 */     recordSet.executeQuery("select database()", new Object[0]);
/* 1072 */     String str = "";
/* 1073 */     if (recordSet.next()) str = Util.null2String(recordSet.getString(1)); 
/* 1074 */     return str;
/*      */   }
/*      */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/WorkflowRequestMessage.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */