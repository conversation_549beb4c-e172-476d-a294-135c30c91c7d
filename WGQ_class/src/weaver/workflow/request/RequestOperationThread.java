/*      */ package weaver.workflow.request;
/*      */ 
/*      */ import com.engine.workflow.biz.requestForm.RequestFormBiz;
/*      */ import java.text.DecimalFormat;
/*      */ import java.util.ArrayList;
/*      */ import java.util.Calendar;
/*      */ import java.util.Iterator;
/*      */ import java.util.List;
/*      */ import java.util.Map;
/*      */ import javax.servlet.http.HttpServletRequest;
/*      */ import javax.servlet.http.HttpServletResponse;
/*      */ import javax.servlet.http.HttpSession;
/*      */ import weaver.WorkPlan.WorkPlanLogMan;
/*      */ import weaver.WorkPlan.WorkPlanViewer;
/*      */ import weaver.conn.RecordSet;
/*      */ import weaver.crm.Maint.CustomerInfoComInfo;
/*      */ import weaver.docs.docs.DocManager;
/*      */ import weaver.general.BaseBean;
/*      */ import weaver.general.GCONST;
/*      */ import weaver.general.TimeUtil;
/*      */ import weaver.general.Util;
/*      */ import weaver.hrm.User;
/*      */ import weaver.hrm.attendance.domain.HrmLeaveTypeColor;
/*      */ import weaver.hrm.attendance.manager.HrmLeaveTypeColorManager;
/*      */ import weaver.hrm.attendance.manager.HrmPaidLeaveManager;
/*      */ import weaver.hrm.report.schedulediff.HrmScheduleDiffUtil;
/*      */ import weaver.hrm.resource.ResourceComInfo;
/*      */ import weaver.hrm.schedule.HrmAnnualManagement;
/*      */ import weaver.hrm.schedule.HrmPaidSickManagement;
/*      */ import weaver.systeminfo.SystemEnv;
/*      */ import weaver.workflow.field.FieldComInfo;
/*      */ import weaver.workflow.msg.PoppupRemindInfoUtil;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ public class RequestOperationThread
/*      */   extends BaseBean
/*      */   implements Runnable
/*      */ {
/*   48 */   private String isMultiDoc = "";
/*   49 */   private String src = "";
/*   50 */   private String iscreate = "";
/*      */   private int requestid;
/*      */   private int workflowid;
/*   53 */   private String workflowtype = "";
/*      */   private int isremark;
/*      */   private int formid;
/*      */   private int isbill;
/*      */   private int billid;
/*      */   private int nodeid;
/*   59 */   private String nodetype = "";
/*   60 */   private String requestname = "";
/*   61 */   private String requestlevel = "";
/*   62 */   private String remark = "";
/*      */   private HttpServletRequest request;
/*      */   private HttpServletResponse response;
/*   65 */   private String submitNodeId = "";
/*   66 */   private String Intervenorid = "";
/*      */   private int SignType;
/*   68 */   private String isFromEditDocument = "";
/*      */   private User user;
/*      */   private int isagentCreater;
/*      */   private int beagenter;
/*      */   private int ispending;
/*      */   private int wfcurrrid;
/*      */   private boolean IsCanModify;
/*   75 */   private String coadsigntype = "";
/*      */   private int enableIntervenor;
/*   77 */   private String remarkLocation = "";
/*   78 */   private String isFirstSubmit = "";
/*   79 */   private String messageType = "";
/*   80 */   private String chatsType = "";
/*   81 */   private String bulkrequest = "";
/*   82 */   private String requestidlist = "";
/*   83 */   private Map<String, String> requestkeymap = null;
/*      */ 
/*      */   
/*      */   private String requestkeystr;
/*      */ 
/*      */ 
/*      */   
/*      */   public RequestOperationThread() {}
/*      */ 
/*      */   
/*      */   public RequestOperationThread(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse, User paramUser, String paramString1, Map<String, String> paramMap, String paramString2, String paramString3) {
/*   94 */     this.request = paramHttpServletRequest;
/*   95 */     this.response = paramHttpServletResponse;
/*   96 */     this.user = paramUser;
/*   97 */     this.bulkrequest = paramString1;
/*   98 */     this.requestkeymap = paramMap;
/*   99 */     this.remark = paramString2;
/*  100 */     this.requestidlist = paramString3;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public RequestOperationThread(String paramString1, String paramString2, String paramString3, int paramInt1, int paramInt2, String paramString4, int paramInt3, int paramInt4, int paramInt5, int paramInt6, int paramInt7, String paramString5, String paramString6, String paramString7, String paramString8, HttpServletRequest paramHttpServletRequest, String paramString9, String paramString10, int paramInt8, String paramString11, User paramUser, int paramInt9, int paramInt10, int paramInt11, int paramInt12, boolean paramBoolean, String paramString12, int paramInt13, String paramString13, String paramString14, String paramString15, String paramString16) {
/*  107 */     this.isMultiDoc = paramString1;
/*  108 */     this.src = paramString2;
/*  109 */     this.iscreate = paramString3;
/*  110 */     this.requestid = paramInt1;
/*  111 */     this.workflowid = paramInt2;
/*  112 */     this.workflowtype = paramString4;
/*  113 */     this.isremark = paramInt3;
/*  114 */     this.formid = paramInt4;
/*  115 */     this.isbill = paramInt5;
/*  116 */     this.billid = paramInt6;
/*  117 */     this.nodeid = paramInt7;
/*  118 */     this.nodetype = paramString5;
/*  119 */     this.requestname = paramString6;
/*  120 */     this.requestlevel = paramString7;
/*  121 */     this.remark = paramString8;
/*  122 */     this.request = paramHttpServletRequest;
/*  123 */     this.submitNodeId = paramString9;
/*  124 */     this.Intervenorid = paramString10;
/*  125 */     this.SignType = paramInt8;
/*  126 */     this.isFromEditDocument = paramString11;
/*  127 */     this.user = paramUser;
/*  128 */     this.isagentCreater = paramInt9;
/*  129 */     this.beagenter = paramInt10;
/*  130 */     this.ispending = paramInt11;
/*  131 */     this.wfcurrrid = paramInt12;
/*  132 */     this.IsCanModify = paramBoolean;
/*  133 */     this.coadsigntype = paramString12;
/*  134 */     this.enableIntervenor = paramInt13;
/*  135 */     this.remarkLocation = paramString13;
/*  136 */     this.isFirstSubmit = paramString14;
/*  137 */     this.messageType = paramString15;
/*  138 */     this.chatsType = paramString16;
/*      */   }
/*      */ 
/*      */   
/*      */   public void run() {
/*  143 */     RecordSet recordSet1 = new RecordSet();
/*  144 */     RecordSet recordSet2 = new RecordSet();
/*  145 */     WFLinkInfo wFLinkInfo = new WFLinkInfo();
/*  146 */     HttpSession httpSession = this.request.getSession(false);
/*  147 */     boolean bool = true;
/*  148 */     int i = this.user.getUID();
/*  149 */     boolean bool1 = (i == 3855) ? true : false;
/*  150 */     boolean bool2 = false;
/*      */     try {
/*  152 */       ResourceComInfo resourceComInfo = new ResourceComInfo();
/*  153 */       CustomerInfoComInfo customerInfoComInfo = new CustomerInfoComInfo();
/*  154 */       BaseBean baseBean = new BaseBean();
/*  155 */       RequestLog requestLog = new RequestLog();
/*  156 */       DocManager docManager = new DocManager();
/*  157 */       WorkPlanViewer workPlanViewer = new WorkPlanViewer();
/*  158 */       PoppupRemindInfoUtil poppupRemindInfoUtil = new PoppupRemindInfoUtil();
/*  159 */       HrmScheduleDiffUtil hrmScheduleDiffUtil = new HrmScheduleDiffUtil();
/*  160 */       HrmPaidLeaveManager hrmPaidLeaveManager = new HrmPaidLeaveManager();
/*  161 */       RecordSet recordSet3 = new RecordSet();
/*  162 */       RecordSet recordSet4 = new RecordSet();
/*  163 */       RecordSet recordSet5 = new RecordSet();
/*      */       
/*  165 */       if ("1".equals(this.bulkrequest)) {
/*      */         
/*  167 */         int j = this.user.getUID();
/*  168 */         boolean bool3 = false;
/*  169 */         String str1 = this.user.getLogintype();
/*  170 */         String str2 = "";
/*      */         
/*  172 */         if (str1.equals("1"))
/*  173 */           str2 = Util.toScreen(resourceComInfo.getResourcename("" + j), this.user.getLanguage()); 
/*  174 */         if (str1.equals("2")) {
/*  175 */           str2 = Util.toScreen(customerInfoComInfo.getCustomerInfoname("" + j), this.user.getLanguage());
/*      */         }
/*  177 */         String str3 = "";
/*  178 */         if (recordSet1.getDBType().equalsIgnoreCase("oracle")) {
/*  179 */           str3 = "select to_char(sysdate,'yyyy-mm-dd') as currentdate, to_char(sysdate,'hh24:mi:ss') as currenttime from dual";
/*      */         } else {
/*  181 */           str3 = "select convert(char(10),getdate(),20) as currentdate, convert(char(8),getdate(),108) as currenttime";
/*      */         } 
/*  183 */         String str4 = "";
/*  184 */         String str5 = "";
/*  185 */         recordSet1.execute(str3);
/*  186 */         if (recordSet1.next()) {
/*  187 */           str4 = Util.null2String(recordSet1.getString(1));
/*  188 */           str5 = Util.null2String(recordSet1.getString(2));
/*      */         } else {
/*  190 */           Calendar calendar = Calendar.getInstance();
/*      */ 
/*      */           
/*  193 */           str4 = Util.add0(calendar.get(1), 4) + "-" + Util.add0(calendar.get(2) + 1, 2) + "-" + Util.add0(calendar.get(5), 2);
/*      */ 
/*      */ 
/*      */           
/*  197 */           str5 = Util.add0(calendar.get(11), 2) + ":" + Util.add0(calendar.get(12), 2) + ":" + Util.add0(calendar.get(13), 2);
/*      */         } 
/*      */         
/*  200 */         String str6 = "submit";
/*  201 */         String str7 = "0";
/*  202 */         byte b1 = 0;
/*      */         
/*  204 */         if (this.remark.equals("")) this.remark = "\n" + str2 + " " + str4 + " " + str5; 
/*  205 */         String str8 = "";
/*  206 */         int k = -1;
/*  207 */         int m = -1;
/*  208 */         int n = -1;
/*  209 */         String str9 = "";
/*  210 */         int i1 = -1;
/*  211 */         String str10 = "";
/*  212 */         String str11 = "";
/*  213 */         String str12 = "";
/*      */ 
/*      */         
/*  216 */         String[] arrayOfString1 = Util.TokenizerString2(this.requestidlist, ",");
/*      */ 
/*      */         
/*  219 */         String str13 = this.request.getParameter("belongtoUserids");
/*  220 */         String[] arrayOfString2 = Util.TokenizerString2(str13, ",");
/*  221 */         ArrayList<String> arrayList = new ArrayList();
/*  222 */         if (bool1) recordSet1.writeLog("batchSubmit--begin"); 
/*  223 */         byte b2 = 0; while (true) { int i2; if (b2 < arrayOfString1.length) {
/*  224 */             String str14 = "";
/*  225 */             if (arrayOfString2.length > 0) {
/*  226 */               if (b2 < arrayOfString2.length) {
/*  227 */                 str14 = arrayOfString2[b2];
/*      */               }
/*  229 */               if (str14 != null && str14 != "" && !str14.equals(Integer.valueOf(this.user.getUID()))) {
/*      */                 
/*  231 */                 j = Util.getIntValue(str14);
/*      */               } else {
/*      */                 
/*  234 */                 j = this.user.getUID();
/*      */               } 
/*      */             } 
/*      */             
/*  238 */             i2 = Util.getIntValue(arrayOfString1[b2], -1);
/*  239 */             RequestFormBiz.updateRequestInfo(i2, this.user, false, false, false, false, this.request.getRemoteAddr());
/*  240 */             if (bool1) recordSet1.writeLog("batchSubmit--requestid--" + i2); 
/*  241 */             String str15 = j + "_submit_token";
/*  242 */             List list = (List)httpSession.getAttribute(str15);
/*  243 */             if (list != null && list.contains("" + i2)) {
/*      */               continue;
/*      */             }
/*  246 */             arrayList.add("" + i2);
/*  247 */             httpSession.setAttribute(str15, arrayList);
/*      */ 
/*      */             
/*  250 */             if (this.user == null) {
/*      */               continue;
/*      */             }
/*      */ 
/*      */ 
/*      */ 
/*      */             
/*  257 */             str6 = "submit";
/*  258 */             b1 = 0;
/*  259 */             recordSet3.executeSql("select min(isremark) from workflow_currentoperator where requestid = " + i2 + " and userid = " + this.user.getUID());
/*  260 */             if (recordSet3.next()) {
/*  261 */               int i4 = recordSet3.getInt(1);
/*  262 */               if (i4 == 1) {
/*  263 */                 str6 = "save";
/*  264 */                 b1 = 1;
/*      */               } 
/*      */             } 
/*      */             
/*  268 */             recordSet3.executeSql("select isremark,isreminded,preisremark,id,groupdetailid,nodeid,(CASE WHEN isremark=9 THEN '7.5' ELSE isremark END) orderisremark from workflow_currentoperator where requestid=" + i2 + " and userid=" + j + " and usertype=" + bool3 + " order by orderisremark,id ");
/*  269 */             boolean bool4 = false;
/*  270 */             while (recordSet3.next()) {
/*  271 */               String str = Util.null2String(recordSet3.getString("isremark"));
/*  272 */               int i4 = Util.getIntValue(recordSet3.getString("nodeid"));
/*      */               
/*  274 */               if (str.equals("1") || str.equals("5") || str.equals("7") || str.equals("9") || (str.equals("0") && !str10.equals("3"))) {
/*      */                 
/*  276 */                 if (str.equals("9")) {
/*  277 */                   b1 = 9;
/*      */                 }
/*      */                 
/*      */                 break;
/*      */               } 
/*      */             } 
/*      */             
/*  284 */             if (recordSet3.getDBType().equals("sqlserver")) {
/*  285 */               recordSet3.executeSql("select t1.requestid from workflow_requestbase t1, workflow_currentoperator t2 where t1.requestid = t2.requestid and t2.userid = " + this.user
/*  286 */                   .getUID() + " and t2.usertype = 0 and (isnull(t1.currentstatus, -1) = -1 or (isnull(t1.currentstatus, -1) = 0 and t1.creater = " + this.user.getUID() + ")) and t2.isremark in ('0', '1', '5', '8', '9', '7')  and t1.requestid = " + i2);
/*      */               
/*  288 */               if (!recordSet3.next()) {
/*      */                 continue;
/*      */               }
/*      */             } else {
/*      */               
/*  293 */               recordSet3.executeSql("select t1.requestid from workflow_requestbase t1, workflow_currentoperator t2 where t1.requestid = t2.requestid and t2.userid = " + this.user
/*  294 */                   .getUID() + " and t2.usertype = 0 and (nvl(t1.currentstatus, -1) = -1 or (nvl(t1.currentstatus, -1) = 0 and t1.creater = " + this.user.getUID() + ")) and t2.isremark in ('0', '1', '5', '8', '9', '7')  and t1.requestid = " + i2);
/*      */               
/*  296 */               if (!recordSet3.next()) {
/*      */                 continue;
/*      */               }
/*      */             } 
/*      */ 
/*      */ 
/*      */ 
/*      */             
/*  304 */             int i3 = -1;
/*  305 */             if (bool1) recordSet1.writeLog("batchSubmit--requestid--" + i2 + "--11--"); 
/*  306 */             if (i2 != -1) {
/*  307 */               boolean bool5 = false;
/*  308 */               int i4 = 0;
/*  309 */               recordSet3.executeSql("select currentnodeid,currentnodetype,requestname,requestlevel,workflowid,status from workflow_requestbase where requestid=" + i2);
/*  310 */               if (recordSet3.next()) {
/*  311 */                 str11 = recordSet3.getString("requestname");
/*  312 */                 str12 = recordSet3.getString("requestlevel");
/*  313 */                 i3 = recordSet3.getInt("workflowid");
/*  314 */                 i4 = recordSet3.getInt("currentnodeid");
/*  315 */                 bool5 = (recordSet3.getString("status") != null && !"".equals(recordSet3.getString("status"))) ? true : false;
/*      */               } 
/*      */               
/*  318 */               i1 = wFLinkInfo.getCurrentNodeid(i2, j, Util.getIntValue(str1, 1));
/*  319 */               str10 = wFLinkInfo.getNodeType(i1);
/*  320 */               int i5 = 0;
/*  321 */               i5 = Util.getIntValue(this.requestkeymap.get(i2 + ""), -1);
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */               
/*  327 */               boolean bool6 = true;
/*  328 */               recordSet3.executeSql("select workflowtype,formid,isbill,messageType,isModifyLog from workflow_base where id=" + i3);
/*  329 */               if (recordSet3.next()) {
/*  330 */                 str8 = recordSet3.getString("workflowtype");
/*  331 */                 k = recordSet3.getInt("formid");
/*  332 */                 m = recordSet3.getInt("isbill");
/*      */                 
/*  334 */                 str9 = recordSet3.getString("messageType");
/*  335 */                 bool6 = (recordSet3.getString("ismodifylog") != null && "1".equals(recordSet3.getString("ismodifylog"))) ? true : false;
/*      */               } 
/*      */               
/*  338 */               if (m == 1) {
/*  339 */                 recordSet3.execute("select tablename from workflow_bill where id=" + k);
/*  340 */                 if (recordSet3.next()) {
/*  341 */                   String str = Util.null2String(recordSet3.getString(1));
/*  342 */                   if (!"".equals(str)) {
/*  343 */                     recordSet3.execute("select id from " + str + " where requestid=" + i2);
/*  344 */                     if (recordSet3.next()) {
/*  345 */                       n = recordSet3.getInt("id");
/*      */                     }
/*      */                   } else {
/*      */                     continue;
/*      */                   } 
/*      */                 } 
/*      */               } 
/*      */               
/*  353 */               if (bool1) recordSet1.writeLog("batchSubmit--requestid--" + i2 + "--22--");
/*      */               
/*  355 */               if (str6.equals("") || i3 == -1 || k == -1 || m == -1 || i1 == -1 || str10.equals("")) {
/*      */                 continue;
/*      */               }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */               
/*  363 */               if (b1 == 9) {
/*      */                 
/*  365 */                 RequestOperationLogManager requestOperationLogManager = new RequestOperationLogManager(i2);
/*  366 */                 byte b = -1;
/*      */                 
/*  368 */                 int i8 = -1;
/*  369 */                 recordSet3.executeSql("select id from workflow_currentoperator where requestid=" + i2 + " and userid=" + j + " and usertype=" + bool3 + " and isremark=9");
/*  370 */                 if (recordSet3.next()) {
/*  371 */                   i8 = Util.getIntValue(recordSet3.getString(1));
/*      */                 }
/*      */ 
/*      */                 
/*  375 */                 char c1 = Util.getSeparator();
/*  376 */                 recordSet3.executeProc("workflow_CurOpe_UbySendNB", "" + i2 + c1 + j + c1 + bool3 + c1 + b1);
/*      */                 
/*  378 */                 String str28 = "";
/*  379 */                 String str29 = "";
/*  380 */                 recordSet3.executeSql("select isfeedback,isnullnotfeedback from workflow_flownode where workflowid=" + i3 + " and nodeid=" + i1);
/*  381 */                 if (recordSet3.next()) {
/*  382 */                   str28 = Util.null2String(recordSet3.getString("isfeedback"));
/*  383 */                   str29 = Util.null2String(recordSet3.getString("isnullnotfeedback"));
/*      */                 } 
/*      */                 
/*  386 */                 String str30 = Util.null2String(baseBean.getPropValue(GCONST.getConfigFile(), "ecology.changestatus"));
/*  387 */                 if (!str30.equals("") && str28.equals("1") && ((str29.equals("1") && !Util.replace(this.remark, "\\<script\\>initFlashVideo\\(\\)\\;\\<\\/script\\>", "", 0, false).equals("")) || !str29.equals("1"))) {
/*  388 */                   recordSet3.executeSql("update workflow_currentoperator set viewtype =-1  where needwfback='1' and requestid=" + i2 + " and userid<>" + j + " and viewtype=-2");
/*      */                 }
/*      */                 
/*  391 */                 String str31 = "";
/*  392 */                 recordSet3.executeSql("select currentnodetype from workflow_Requestbase where requestid=" + i2);
/*  393 */                 if (recordSet3.next()) str31 = Util.null2String(recordSet3.getString(1)); 
/*  394 */                 if (str31.equals("3"))
/*      */                 {
/*  396 */                   recordSet3.executeSql("update workflow_currentoperator set iscomplete=1 where userid=" + j + " and usertype=" + bool3 + " and requestid=" + i2);
/*      */                 }
/*      */                 
/*  399 */                 requestLog.saveLog(i3, i2, i1, "9", this.remark, this.user);
/*      */ 
/*      */                 
/*  402 */                 recordSet3.executeSql("select logid from workflow_requestlog where requestid = " + i2 + " and nodeid = " + i1 + " and operator=" + this.user.getUID() + " and operatortype=" + bool3 + " and logtype = '9' order by operatedate, operatetime");
/*  403 */                 if (recordSet3.next()) {
/*  404 */                   int i9 = recordSet3.getInt(1);
/*  405 */                   if (i9 > 0);
/*      */                 } 
/*      */ 
/*      */                 
/*      */                 continue;
/*      */               } 
/*      */ 
/*      */               
/*  413 */               if (bool1) recordSet1.writeLog("batchSubmit--requestid--" + i2 + "--33--"); 
/*  414 */               int i6 = 0;
/*  415 */               if (b1 == 0) {
/*  416 */                 String str = " select a.nodeid lastnodeid, a.logtype from workflow_requestlog a, workflow_nownode b where a.requestid = b.requestid and a.destnodeid = b.nownodeid  and b.requestid=" + i2 + " and a.destnodeid=" + i1 + " and a.nodeid != " + i1 + " order by a.logid desc";
/*      */                 
/*  418 */                 recordSet3.executeSql(str);
/*  419 */                 while (recordSet3.next()) {
/*  420 */                   String str28 = Util.null2String(recordSet3.getString("logtype"));
/*  421 */                   if ("3".equals(str28)) {
/*  422 */                     i6 = Util.getIntValue(Util.null2String(recordSet3.getString("lastnodeid")), 0);
/*      */                     break;
/*      */                   } 
/*  425 */                   if ("0".equals(str28) || "2".equals(str28) || "e".equals(str28) || "i".equals(str28) || "j".equals(str28)) {
/*      */                     break;
/*      */                   }
/*      */                 } 
/*  429 */                 if (i6 != 0 && !(new WFLinkInfo()).isCanSubmitToRejectNode(i2, i4, i6)) {
/*  430 */                   i6 = 0;
/*      */                 }
/*  432 */                 if (i6 != 0) {
/*  433 */                   String str28 = "";
/*  434 */                   recordSet3.executeSql("select * from workflow_flownode where workflowid=" + i3 + " and nodeid=" + i6);
/*  435 */                   if (recordSet3.next()) {
/*  436 */                     str28 = Util.null2String(recordSet3.getString("isSubmitDirectNode"));
/*      */                   }
/*  438 */                   if (!"1".equals(str28)) {
/*  439 */                     i6 = 0;
/*      */                   }
/*      */                 } 
/*      */               } 
/*  443 */               if (bool1) recordSet1.writeLog("batchSubmit--requestid--" + i2 + "--44--"); 
/*  444 */               RequestManager requestManager = new RequestManager();
/*      */               
/*  446 */               requestManager.setSrc(str6);
/*  447 */               requestManager.setIscreate(str7);
/*  448 */               requestManager.setRequestid(i2);
/*  449 */               requestManager.setWorkflowid(i3);
/*  450 */               requestManager.setWorkflowtype(str8);
/*  451 */               requestManager.setIsremark(b1);
/*  452 */               requestManager.setFormid(k);
/*  453 */               requestManager.setIsbill(m);
/*  454 */               requestManager.setBillid(n);
/*  455 */               requestManager.setNodeid(i1);
/*  456 */               requestManager.setNodetype(str10);
/*  457 */               requestManager.setRequestname(str11);
/*  458 */               requestManager.setRequestlevel(str12);
/*  459 */               requestManager.setRemark(this.remark);
/*  460 */               requestManager.setRequest(this.request);
/*  461 */               requestManager.setMessageType(str9);
/*  462 */               requestManager.setUser(this.user);
/*  463 */               requestManager.setRequestKey(i5);
/*  464 */               requestManager.setSubmitToNodeid(i6);
/*      */ 
/*      */               
/*  467 */               if (!str6.equals("save")) {
/*      */                 try {
/*  469 */                   if (bool1) recordSet1.writeLog("batchSubmit--requestid--" + i2 + "--55--");
/*      */                   
/*  471 */                   RequestCheckAddinRules requestCheckAddinRules = new RequestCheckAddinRules();
/*  472 */                   requestCheckAddinRules.resetParameter();
/*      */                   
/*  474 */                   requestCheckAddinRules.setTrack(bool6);
/*  475 */                   requestCheckAddinRules.setStart(bool5);
/*  476 */                   requestCheckAddinRules.setNodeid(i1);
/*      */                   
/*  478 */                   requestCheckAddinRules.setRequestid(i2);
/*  479 */                   requestCheckAddinRules.setWorkflowid(i3);
/*  480 */                   requestCheckAddinRules.setObjid(i1);
/*  481 */                   requestCheckAddinRules.setObjtype(1);
/*      */                   
/*  483 */                   requestCheckAddinRules.setIsbill(m);
/*  484 */                   requestCheckAddinRules.setFormid(k);
/*  485 */                   requestCheckAddinRules.setIspreadd("0");
/*  486 */                   requestCheckAddinRules.setRequestManager(requestManager);
/*  487 */                   requestCheckAddinRules.setUser(this.user);
/*  488 */                   requestCheckAddinRules.checkAddinRules();
/*  489 */                 } catch (Exception exception) {
/*  490 */                   if (bool1) recordSet1.writeLog("batchSubmit--requestid--" + i2 + "--66--");
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */                   
/*      */                   continue;
/*      */                 } 
/*      */               }
/*      */ 
/*      */ 
/*      */ 
/*      */               
/*  503 */               if (bool1) recordSet1.writeLog("batchSubmit--requestid--" + i2 + "--77--");
/*      */ 
/*      */ 
/*      */               
/*  507 */               int i7 = 0;
/*  508 */               recordSet1.execute("select docRightByOperator from workflow_base where id=" + i3);
/*  509 */               if (recordSet1.next()) {
/*  510 */                 i7 = Util.getIntValue(recordSet1.getString("docRightByOperator"), 0);
/*      */               }
/*  512 */               String str16 = "workflow_form";
/*  513 */               if (m == 1) {
/*  514 */                 recordSet1.execute("select tablename from workflow_bill where id = " + k);
/*  515 */                 if (recordSet1.next()) {
/*  516 */                   str16 = Util.null2String(recordSet1.getString("tablename"));
/*      */                 }
/*  518 */                 recordSet1.executeProc("workflow_billfield_Select", k + "");
/*      */               } else {
/*  520 */                 recordSet1.executeSql("select t2.fieldid,t2.fieldorder,t2.isdetail,t1.fieldlable,t1.langurageid from workflow_fieldlable t1,workflow_formfield t2 where t1.formid=t2.formid and t1.fieldid=t2.fieldid and (t2.isdetail<>'1' or t2.isdetail is null)  and t2.formid=" + k + "  and t1.langurageid=" + this.user.getLanguage() + " order by t2.fieldorder");
/*      */               } 
/*      */               
/*  523 */               ArrayList<String> arrayList1 = new ArrayList();
/*  524 */               ArrayList<String> arrayList2 = new ArrayList();
/*  525 */               ArrayList<String> arrayList3 = new ArrayList();
/*  526 */               ArrayList<String> arrayList4 = new ArrayList();
/*  527 */               ArrayList<String> arrayList5 = new ArrayList();
/*  528 */               String str17 = "";
/*  529 */               String str18 = "";
/*  530 */               String str19 = "";
/*  531 */               String str20 = "";
/*  532 */               String str21 = "";
/*  533 */               String str22 = "";
/*  534 */               FieldComInfo fieldComInfo = new FieldComInfo();
/*  535 */               String str23 = "";
/*  536 */               String str24 = "";
/*  537 */               String str25 = "";
/*  538 */               String str26 = "";
/*  539 */               String str27 = "";
/*  540 */               boolean bool7 = false;
/*  541 */               char c = Util.getSeparator();
/*  542 */               while (recordSet1.next()) {
/*  543 */                 if (m == 1) {
/*  544 */                   String str = Util.null2String(recordSet1.getString("viewtype"));
/*      */                   
/*  546 */                   if (str.equals("1"))
/*  547 */                     continue;  str18 = Util.null2String(recordSet1.getString("id"));
/*  548 */                   str19 = Util.null2String(recordSet1.getString("fieldname"));
/*  549 */                   str20 = Util.null2String(recordSet1.getString("fielddbtype"));
/*  550 */                   str21 = Util.null2String(recordSet1.getString("fieldhtmltype"));
/*  551 */                   str22 = Util.null2String(recordSet1.getString("type"));
/*      */                 } else {
/*  553 */                   str18 = Util.null2String(recordSet1.getString(1));
/*  554 */                   str19 = Util.null2String(fieldComInfo.getFieldname(str18));
/*  555 */                   str20 = Util.null2String(fieldComInfo.getFielddbtype(str18));
/*  556 */                   str21 = Util.null2String(fieldComInfo.getFieldhtmltype(str18));
/*  557 */                   str22 = Util.null2String(fieldComInfo.getFieldType(str18));
/*      */                 } 
/*  559 */                 if (str19.toLowerCase().equals("manager")) {
/*  560 */                   bool7 = true;
/*      */                 }
/*  562 */                 arrayList1.add(str18);
/*  563 */                 arrayList2.add(str19);
/*  564 */                 arrayList3.add(str20);
/*  565 */                 arrayList4.add(str21);
/*  566 */                 arrayList5.add(str22);
/*  567 */                 str17 = str17 + str19 + ",";
/*      */               } 
/*      */               
/*  570 */               if (bool7) {
/*  571 */                 String str28 = "" + j;
/*      */                 
/*  573 */                 recordSet3.executeSql("select agentorbyagentid from workflow_currentoperator where usertype=0 and isremark='0' and requestid=" + i2 + " and userid=" + j + " and nodeid=" + i1 + " order by id desc");
/*  574 */                 if (recordSet3.next()) {
/*  575 */                   int i8 = recordSet3.getInt(1);
/*  576 */                   if (i8 > 0) str28 = "" + i8; 
/*      */                 } 
/*  578 */                 String str29 = resourceComInfo.getManagerID(str28);
/*  579 */                 recordSet5.executeSql("update " + str16 + " set manager=" + str29 + " where requestid=" + i2);
/*      */               } 
/*  581 */               if (str17.length() > 0) {
/*  582 */                 str17 = str17.substring(0, str17.length() - 1);
/*  583 */                 recordSet5.execute("select " + str17 + " from " + str16 + " where requestid=" + i2);
/*  584 */                 if (recordSet5.next()) {
/*  585 */                   for (byte b = 0; b < arrayList1.size(); b++) {
/*  586 */                     str18 = Util.null2String(arrayList1.get(b));
/*  587 */                     str19 = Util.null2String(arrayList2.get(b));
/*  588 */                     str20 = Util.null2String(arrayList3.get(b));
/*  589 */                     str21 = Util.null2String(arrayList4.get(b));
/*  590 */                     str22 = Util.null2String(arrayList5.get(b));
/*  591 */                     if (str21.equals("3") && (str22.equals("1") || str22.equals("17"))) {
/*  592 */                       String str = "";
/*  593 */                       str = Util.null2String(recordSet5.getString(str19));
/*  594 */                       if (!str.equals("")) str23 = str23 + "," + str; 
/*  595 */                     } else if (str21.equals("3") && (str22.equals("7") || str22.equals("18"))) {
/*  596 */                       String str = "";
/*  597 */                       str = Util.null2String(recordSet5.getString(str19));
/*  598 */                       if (!str.equals("")) str24 = str24 + "," + str; 
/*  599 */                     } else if (str21.equals("3") && (str22.equals("8") || str22.equals("135"))) {
/*  600 */                       String str = "";
/*  601 */                       str = Util.null2String(recordSet5.getString(str19));
/*  602 */                       if (!str.equals("")) str25 = str25 + "," + str; 
/*  603 */                     } else if (str21.equals("3") && (str22.equals("9") || str22.equals("37"))) {
/*  604 */                       String str = "";
/*  605 */                       str = Util.null2String(recordSet5.getString(str19));
/*  606 */                       if (!str.equals("")) str26 = str26 + "," + str;
/*      */ 
/*      */                       
/*  609 */                       if (i7 == 1) {
/*      */                         
/*  611 */                         if (!str.equals("")) {
/*  612 */                           recordSet2.execute("delete from Workflow_DocSource where requestid =" + i2 + " and fieldid =" + str18 + " and docid not in (" + str + ")");
/*      */                         } else {
/*  614 */                           recordSet2.execute("delete from Workflow_DocSource where requestid =" + i2 + " and fieldid =" + str18);
/*      */                         } 
/*      */                         
/*  617 */                         String[] arrayOfString = Util.TokenizerString2(str, ",");
/*  618 */                         for (byte b3 = 0; b3 < arrayOfString.length; b3++) {
/*  619 */                           if (arrayOfString[b3] != null && !arrayOfString[b3].equals("")) {
/*  620 */                             recordSet2.executeProc("Workflow_DocSource_Insert", "" + i2 + c + i1 + c + str18 + c + arrayOfString[b3] + c + j + c + "1");
/*      */                           }
/*      */                         } 
/*      */                       } 
/*  624 */                     } else if (str21.equals("3") && str22.equals("23")) {
/*  625 */                       String str = "";
/*  626 */                       str = Util.null2String(recordSet5.getString(str19));
/*  627 */                       if (!str.equals("")) str27 = str27 + "," + str; 
/*      */                     } 
/*      */                   } 
/*  630 */                   if (!str23.equals("")) str23 = str23.substring(1); 
/*  631 */                   if (!str24.equals("")) str24 = str24.substring(1); 
/*  632 */                   if (!str25.equals("")) str25 = str25.substring(1); 
/*  633 */                   if (!str26.equals("")) str26 = str26.substring(1); 
/*  634 */                   if (!str27.equals("")) str27 = str27.substring(1); 
/*  635 */                   requestManager.setHrmids(str23);
/*  636 */                   requestManager.setCrmids(str24);
/*  637 */                   requestManager.setPrjids(str25);
/*  638 */                   requestManager.setDocids(str26);
/*  639 */                   requestManager.setCptids(str27);
/*      */                 } 
/*      */               } 
/*  642 */               if (bool1) recordSet1.writeLog("batchSubmit--requestid--" + i2 + "--88--");
/*      */ 
/*      */ 
/*      */               
/*  646 */               if (k != 180) {
/*  647 */                 boolean bool8 = requestManager.flowNextNode();
/*  648 */                 if (bool1) recordSet1.writeLog("batchSubmit--requestid--" + i2 + "--99--" + bool8);
/*      */                 
/*  650 */                 if (bool8) {
/*  651 */                   arrayList.remove("" + i2);
/*      */ 
/*      */                   
/*  654 */                   if (requestManager.getNextNodetype().equals("3") && m == 1) {
/*      */                     
/*  656 */                     if (k == 28) {
/*  657 */                       recordSet3.executeSql("update bill_Approve set status='1' where requestid=" + i2);
/*  658 */                       recordSet3.executeSql("select approveid from bill_Approve where requestid=" + i2);
/*  659 */                       if (recordSet3.next()) {
/*  660 */                         String str = Util.null2String(recordSet3.getString("approveid"));
/*  661 */                         int i10 = Util.getIntValue(str, 0);
/*  662 */                         recordSet3.executeSql("select max(b.id) from DocDetail a,DocDetail b where a.docEditionId=b.docEditionId and a.docEditionId>0 and a.id=" + i10);
/*  663 */                         if (recordSet3.next()) {
/*  664 */                           i10 = Util.getIntValue(recordSet3.getString(1), i10);
/*  665 */                           if (i10 > 0) {
/*  666 */                             str = "" + i10;
/*      */                           }
/*      */                         } 
/*  669 */                         docManager.approveDocFromWF("approve", str, str4, str5, j + "");
/*      */                       } 
/*      */                     } 
/*      */                     
/*  673 */                     if (k == 74) {
/*  674 */                       recordSet3.executeSql("select approveid from Bill_ApproveProj where requestid=" + i2);
/*  675 */                       if (recordSet3.next()) {
/*  676 */                         byte b = 2;
/*  677 */                         String str30 = recordSet3.getString("approveid");
/*  678 */                         recordSet3.executeProc("Prj_Plan_Approve", str30);
/*  679 */                         String str31 = "update prj_taskprocess set isactived=2 where prjid=" + str30;
/*  680 */                         recordSet3.executeSql(str31);
/*  681 */                         str31 = "update Prj_ProjectInfo set status = 5 where id = " + str30;
/*  682 */                         recordSet3.executeSql(str31);
/*      */ 
/*      */                         
/*  685 */                         String str32 = "";
/*  686 */                         String str33 = "";
/*      */                         
/*  688 */                         recordSet3.executeProc("Prj_TaskProcess_Sum", "" + str30);
/*  689 */                         if (recordSet3.next() && !recordSet3.getString("workday").equals("")) {
/*      */                           
/*  691 */                           if (!recordSet3.getString("begindate").equals("x")) str32 = recordSet3.getString("begindate"); 
/*  692 */                           if (!recordSet3.getString("enddate").equals("-")) str33 = recordSet3.getString("enddate");
/*      */                         
/*      */                         } 
/*  695 */                         if (!str32.equals("")) {
/*  696 */                           recordSet3.executeSql("update workplan set status = '0',begindate = '" + str32 + "',enddate = '" + str33 + "' where type_n = '2' and projectid = '" + str30 + "' and taskid = -1");
/*      */                         }
/*      */ 
/*      */ 
/*      */                         
/*  701 */                         String str34 = "";
/*  702 */                         String str35 = "";
/*  703 */                         String str36 = "";
/*  704 */                         String str37 = "";
/*  705 */                         recordSet3.executeProc("Prj_ProjectInfo_SelectByID", str30);
/*  706 */                         if (recordSet3.next()) {
/*  707 */                           str36 = recordSet3.getString("manager");
/*      */                         }
/*      */                         
/*  710 */                         str31 = "SELECT * FROM Prj_TaskProcess WHERE prjid = " + str30 + " and isdelete<>'1' order by id";
/*  711 */                         recordSet3.executeSql(str31);
/*      */                         
/*  713 */                         while (recordSet3.next()) {
/*  714 */                           str37 = recordSet3.getString("id");
/*  715 */                           str34 = "2";
/*  716 */                           str34 = str34 + b + Util.toScreen(recordSet3.getString("subject"), this.user.getLanguage());
/*  717 */                           str34 = str34 + b + Util.toScreen(recordSet3.getString("hrmid"), this.user.getLanguage());
/*  718 */                           str34 = str34 + b + Util.toScreen(recordSet3.getString("begindate"), this.user.getLanguage());
/*  719 */                           str34 = str34 + b + "";
/*  720 */                           str34 = str34 + b + Util.toScreen(recordSet3.getString("enddate"), this.user.getLanguage());
/*  721 */                           str34 = str34 + b + "";
/*  722 */                           str34 = str34 + b + Util.toScreen(recordSet3.getString("content"), this.user.getLanguage());
/*  723 */                           str34 = str34 + b + "0";
/*  724 */                           str34 = str34 + b + str30;
/*  725 */                           str34 = str34 + b + "0";
/*  726 */                           str34 = str34 + b + "0";
/*  727 */                           str34 = str34 + b + "0";
/*  728 */                           str34 = str34 + b + "0";
/*  729 */                           str34 = str34 + b + "1";
/*  730 */                           str34 = str34 + b + "0";
/*  731 */                           str34 = str34 + b + str36;
/*  732 */                           str34 = str34 + b + str4;
/*  733 */                           str34 = str34 + b + str5;
/*  734 */                           str34 = str34 + b + "0";
/*  735 */                           str34 = str34 + b + "0";
/*  736 */                           str34 = str34 + b + "1";
/*  737 */                           str34 = str34 + b + "0";
/*      */                           
/*  739 */                           recordSet4.executeProc("WorkPlan_Insert", str34);
/*  740 */                           if (recordSet4.next()) str35 = recordSet4.getString("id");
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */                           
/*  746 */                           String[] arrayOfString = { str35, "1", String.valueOf(j), this.request.getRemoteAddr() };
/*  747 */                           WorkPlanLogMan workPlanLogMan = new WorkPlanLogMan();
/*  748 */                           workPlanLogMan.writeViewLog(arrayOfString);
/*      */ 
/*      */                           
/*  751 */                           recordSet4.executeSql("update workplan set taskid = " + str37 + " where id =" + str35);
/*  752 */                           workPlanViewer.setWorkPlanShareById(str35);
/*      */                         } 
/*      */                       } 
/*      */                     } 
/*      */ 
/*      */                     
/*  758 */                     if (k == 79) {
/*  759 */                       String str30 = "select approveid,approvevalue,approvetype from bill_ApproveCustomer where requestid=" + i2;
/*  760 */                       recordSet3.executeSql(str30);
/*  761 */                       String str31 = "";
/*  762 */                       String str32 = "";
/*  763 */                       String str33 = "";
/*  764 */                       if (recordSet3.next()) {
/*  765 */                         str31 = recordSet3.getString("approveid");
/*  766 */                         str32 = recordSet3.getString("approvetype");
/*  767 */                         str33 = recordSet3.getString("approvevalue");
/*      */                       } 
/*      */ 
/*      */                       
/*  771 */                       recordSet3.executeSql("update bill_ApproveCustomer set status=1 where requestid=" + i2);
/*  772 */                       recordSet3.executeProc("CRM_CustomerInfo_SelectByID", str31);
/*  773 */                       recordSet3.first();
/*  774 */                       String str34 = recordSet3.getString("status");
/*  775 */                       String str35 = recordSet3.getString("manager");
/*  776 */                       String str36 = recordSet3.getString("name");
/*  777 */                       String str37 = "";
/*  778 */                       byte b = 2;
/*  779 */                       String str38 = "";
/*  780 */                       if (str32.equals("1")) {
/*  781 */                         str37 = str31;
/*  782 */                         str37 = str37 + b + str33;
/*  783 */                         str37 = str37 + b + "1";
/*      */                         
/*  785 */                         recordSet3.executeProc("CRM_CustomerInfo_Approve", str37);
/*      */                         
/*  787 */                         str37 = str31;
/*  788 */                         str37 = str37 + b + "a";
/*  789 */                         str37 = str37 + b + "0";
/*  790 */                         str37 = str37 + b + "a";
/*  791 */                         str37 = str37 + b + str4;
/*  792 */                         str37 = str37 + b + str5;
/*  793 */                         str37 = str37 + b + "" + this.user.getUID();
/*  794 */                         str37 = str37 + b + "" + this.user.getLogintype();
/*  795 */                         str37 = str37 + b + this.request.getRemoteAddr();
/*  796 */                         recordSet3.executeProc("CRM_Log_Insert", str37);
/*      */                         
/*  798 */                         str38 = SystemEnv.getHtmlLabelName(23247, this.user.getLanguage());
/*      */                         
/*  800 */                         str37 = str31 + b + "1" + b + "0" + b + "0";
/*  801 */                         str37 = str37 + b + str38 + b + str4 + b + str5 + b + str34 + b + str33;
/*  802 */                         str37 = str37 + b + "" + this.user.getUID() + b + "" + this.user.getLogintype() + b + this.request.getRemoteAddr();
/*  803 */                         recordSet3.executeProc("CRM_Modify_Insert", str37);
/*  804 */                       } else if (str32.equals("2")) {
/*  805 */                         str37 = str31;
/*  806 */                         str37 = str37 + b + str33;
/*      */                         
/*  808 */                         recordSet3.executeProc("CRM_CustomerInfo_Portal", str37);
/*  809 */                         String str39 = "";
/*  810 */                         String str40 = "";
/*      */                         
/*  812 */                         if (str33.equals("2")) {
/*  813 */                           if (str31.length() < 5) {
/*  814 */                             str39 = "U" + Util.add0(Util.getIntValue(str31), 5);
/*      */                           } else {
/*  816 */                             str39 = "U" + str31;
/*      */                           } 
/*      */                           
/*  819 */                           str40 = Util.getPortalPassword();
/*      */                           
/*  821 */                           str37 = str31;
/*  822 */                           str37 = str37 + b + str39;
/*  823 */                           str37 = str37 + b + str40;
/*      */                           
/*  825 */                           recordSet3.executeProc("CRM_CustomerInfo_PortalPasswor", str37);
/*      */                         } 
/*  827 */                         str37 = str31;
/*  828 */                         str37 = str37 + b + "p";
/*  829 */                         str37 = str37 + b + "0";
/*  830 */                         str37 = str37 + b + "p";
/*  831 */                         str37 = str37 + b + str4;
/*  832 */                         str37 = str37 + b + str5;
/*  833 */                         str37 = str37 + b + "" + this.user.getUID();
/*  834 */                         str37 = str37 + b + "" + this.user.getLogintype();
/*  835 */                         str37 = str37 + b + this.request.getRemoteAddr();
/*  836 */                         recordSet3.executeProc("CRM_Log_Insert", str37);
/*      */                         
/*  838 */                         str38 = SystemEnv.getHtmlLabelName(23249, this.user.getLanguage());
/*      */                         
/*  840 */                         str37 = str31 + b + "1" + b + "0" + b + "0";
/*  841 */                         str37 = str37 + b + str38 + b + str4 + b + str5 + b + str34 + b + str33;
/*  842 */                         str37 = str37 + b + "" + this.user.getUID() + b + "" + this.user.getLogintype() + b + this.request.getRemoteAddr();
/*  843 */                         recordSet3.executeProc("CRM_Modify_Insert", str37);
/*  844 */                       } else if (str32.equals("3")) {
/*  845 */                         String str39 = "";
/*  846 */                         String str40 = "";
/*      */                         
/*  848 */                         if (str33.equals("2")) {
/*  849 */                           if (str31.length() < 5) {
/*  850 */                             str39 = "U" + Util.add0(Util.getIntValue(str31), 5);
/*      */                           } else {
/*  852 */                             str39 = "U" + str31;
/*      */                           } 
/*      */                           
/*  855 */                           str40 = Util.getPortalPassword();
/*      */                           
/*  857 */                           str37 = str31;
/*  858 */                           str37 = str37 + b + str39;
/*  859 */                           str37 = str37 + b + str40;
/*      */                           
/*  861 */                           recordSet3.executeProc("CRM_CustomerInfo_PortalPasswor", str37);
/*      */                         } 
/*      */                       } 
/*      */                     } 
/*      */                   } 
/*  866 */                   if (bool1) recordSet1.writeLog("batchSubmit--requestid--" + i2 + "--101--"); 
/*  867 */                   poppupRemindInfoUtil.updatePoppupRemindInfo(j, 0, str1.equals("1") ? "0" : "1", i2);
/*  868 */                   int i8 = -1;
/*  869 */                   int i9 = -1;
/*  870 */                   String str28 = "select * from workflow_currentoperator where requestid= " + i2 + "and nodeid = " + i1 + " and userid = " + j;
/*  871 */                   recordSet3.executeSql(str28);
/*  872 */                   if (recordSet3.next()) {
/*  873 */                     i8 = Util.getIntValue(recordSet3.getString("takisremark"));
/*  874 */                     i9 = Util.getIntValue(recordSet3.getString("handleforwardid"));
/*      */                   } 
/*  876 */                   if (i8 == 2)
/*      */                   {
/*  878 */                     recordSet3.executeSql("update workflow_requestlog set logtype='b' where  requestid= " + i2 + " and nodeid = " + i1 + " and operator = " + j);
/*      */                   }
/*  880 */                   if (i9 > 0) {
/*  881 */                     recordSet3.executeSql("update workflow_requestlog set logtype='j' where  requestid= " + i2 + "and nodeid = " + i1 + " and operator = " + j);
/*      */                   }
/*  883 */                   if (i8 != 2 && i9 < 0) {
/*  884 */                     boolean bool9 = requestManager.saveRequestLog();
/*      */                   }
/*      */                   
/*  887 */                   String str29 = "select * from workflow_currentoperator where requestid= " + i2 + "and nodeid = " + i1 + " and userid = " + j + " and takisremark = 2";
/*  888 */                   recordSet3.executeSql(str29);
/*  889 */                   if (recordSet3.next()) {
/*  890 */                     String str = "select count(*) as cou from workflow_currentoperator where requestid= " + i2 + "and nodeid = " + i1 + " and takisremark = 2 and isremark=1";
/*  891 */                     recordSet3.executeSql(str);
/*  892 */                     if (recordSet3.next() && 
/*  893 */                       recordSet3.getInt("cou") == 0) {
/*  894 */                       String str30 = "select * from workflow_currentoperator where requestid= " + i2 + "and nodeid = " + i1 + " and isremark = 0 and takisremark = -2";
/*  895 */                       recordSet3.executeSql(str30);
/*  896 */                       if (recordSet3.next()) {
/*  897 */                         String str31 = "update workflow_currentoperator set takisremark=0 where requestid= " + i2 + "and nodeid = " + i1 + " and isremark = 0 and takisremark = -2";
/*  898 */                         recordSet3.executeSql(str31);
/*      */                       } 
/*      */                     } 
/*      */                   } 
/*      */                 } 
/*      */               } 
/*      */ 
/*      */               
/*  906 */               if (bool1) recordSet1.writeLog("batchSubmit--requestid--" + i2 + "--102--");
/*      */               
/*  908 */               if (k == 180 && m == 1) {
/*      */                 
/*  910 */                 String str28 = "";
/*  911 */                 int i8 = Util.getIntValue((String)httpSession.getAttribute(this.user.getUID() + "_" + i2 + "urger"), 0);
/*      */ 
/*      */ 
/*      */                 
/*  915 */                 int i9 = Util.getIntValue((String)httpSession.getAttribute(i3 + "isagent" + this.user.getUID()));
/*  916 */                 int i10 = Util.getIntValue((String)httpSession.getAttribute(i3 + "beagenter" + this.user.getUID()), 0);
/*  917 */                 requestManager.setIsagentCreater(i9);
/*  918 */                 requestManager.setBeAgenter(i10);
/*      */ 
/*      */                 
/*  921 */                 str28 = "select resourceid from Bill_BoHaiLeave where requestid = " + i2;
/*  922 */                 recordSet3.executeSql(str28);
/*  923 */                 recordSet3.next();
/*  924 */                 String str29 = recordSet3.getString("resourceid");
/*      */                 
/*  926 */                 str28 = "select * from Bill_BoHaiLeave where requestid = " + i2;
/*  927 */                 recordSet3.executeSql(str28);
/*  928 */                 recordSet3.next();
/*      */                 
/*  930 */                 if ("submit".equalsIgnoreCase(str6) && requestManager.getIsremark() == 0) {
/*      */ 
/*      */                   
/*  933 */                   String str33 = Util.null2String(recordSet3.getString("fromDate")).trim();
/*  934 */                   String str34 = Util.null2String(recordSet3.getString("fromTime")).trim();
/*  935 */                   String str35 = Util.null2String(recordSet3.getString("toDate")).trim();
/*  936 */                   String str36 = Util.null2String(recordSet3.getString("toTime")).trim();
/*  937 */                   if (!"".equals(str33) && !"".equals(str35)) {
/*  938 */                     if ("".equals(str34)) {
/*  939 */                       str34 = "00:00:00";
/*  940 */                     } else if (str34.length() == 5) {
/*  941 */                       str34 = str34 + ":00";
/*      */                     } 
/*  943 */                     if ("".equals(str36)) {
/*  944 */                       str36 = "23:59:59";
/*  945 */                     } else if (str36.length() == 5) {
/*  946 */                       str36 = str36 + ":00";
/*      */                     } 
/*      */                     
/*  949 */                     long l = TimeUtil.timeInterval(str33 + " " + str34, str35 + " " + str36);
/*      */                     
/*  951 */                     if (l < 0L) {
/*  952 */                       baseBean.writeLog("requestid=" + i2 + "&message=24569");
/*      */ 
/*      */ 
/*      */ 
/*      */                       
/*      */                       continue;
/*      */                     } 
/*      */                   } 
/*      */                 } 
/*      */ 
/*      */ 
/*      */ 
/*      */                 
/*  965 */                 String str30 = recordSet3.getString("newLeaveType");
/*  966 */                 String str31 = recordSet3.getString("otherLeaveType");
/*      */                 
/*  968 */                 String str32 = (new HrmLeaveTypeColorManager()).getPaidleaveStr();
/*  969 */                 HrmLeaveTypeColorManager hrmLeaveTypeColorManager = new HrmLeaveTypeColorManager();
/*  970 */                 HrmLeaveTypeColor hrmLeaveTypeColor = (HrmLeaveTypeColor)hrmLeaveTypeColorManager.get(hrmLeaveTypeColorManager.getMapParam("field004:" + str30));
/*  971 */                 hrmLeaveTypeColor = (hrmLeaveTypeColor == null) ? new HrmLeaveTypeColor() : hrmLeaveTypeColor;
/*  972 */                 int i11 = hrmLeaveTypeColor.getIsCalWorkDay().intValue();
/*  973 */                 int i12 = hrmLeaveTypeColor.getRelateweekday().intValue();
/*  974 */                 ArrayList<String> arrayList6 = new ArrayList();
/*  975 */                 float f = 0.0F;
/*      */                 
/*  977 */                 if (str6.equals("submit") && !str10.equals("3") && str30.equals(String.valueOf(-6)))
/*  978 */                 { if (bool1) recordSet1.writeLog("batchSubmit--requestid--" + i2 + "--103--"); 
/*  979 */                   String str33 = recordSet3.getString("fromDate");
/*  980 */                   String str34 = recordSet3.getString("fromTime");
/*  981 */                   String str35 = recordSet3.getString("toDate");
/*  982 */                   String str36 = recordSet3.getString("toTime");
/*      */                   
/*  984 */                   String str37 = recordSet3.getString("resourceid");
/*      */                   
/*  986 */                   int i13 = this.user.getUserSubCompany1();
/*  987 */                   recordSet3.executeSql("select b.subcompanyid1 from hrmresource a,hrmdepartment b where a.departmentid=b.id and a.id=" + str37);
/*  988 */                   if (recordSet3.next()) {
/*  989 */                     i13 = Util.getIntValue(recordSet3.getString("subcompanyid1"), -1);
/*  990 */                     if (i13 <= 0) {
/*  991 */                       i13 = this.user.getUserSubCompany1();
/*      */                     }
/*      */                   } 
/*      */ 
/*      */                   
/*  996 */                   User user = User.getUser(Util.getIntValue(str37), 0);
/*  997 */                   hrmScheduleDiffUtil.setUser(user);
/*  998 */                   String str38 = hrmScheduleDiffUtil.getTotalWorkingDays(str33, str34, str35, str36, i13);
/*      */                   
/* 1000 */                   Calendar calendar = Calendar.getInstance();
/* 1001 */                   String str39 = Util.add0(calendar.get(1), 4);
/* 1002 */                   String str40 = Util.add0(calendar.get(1) - 1, 4);
/*      */                   
/* 1004 */                   String str41 = "";
/*      */                   
/* 1006 */                   String str42 = "";
/*      */                   
/* 1008 */                   String str43 = "";
/*      */                   
/*      */                   try {
/* 1011 */                     String str = HrmAnnualManagement.getUserAannualInfo(str29, str4);
/* 1012 */                     str43 = Util.TokenizerString2(str, "#")[0];
/* 1013 */                     str42 = Util.TokenizerString2(str, "#")[1];
/* 1014 */                     str41 = Util.TokenizerString2(str, "#")[2];
/* 1015 */                   } catch (Exception exception) {}
/*      */ 
/*      */ 
/*      */                   
/* 1019 */                   float f1 = Util.getFloatValue(str38, 0.0F);
/* 1020 */                   f = f1;
/* 1021 */                   float f2 = Util.getFloatValue(str41, 0.0F);
/* 1022 */                   float f3 = Util.getFloatValue(str42, 0.0F);
/* 1023 */                   float f4 = Util.getFloatValue(str43, 0.0F);
/* 1024 */                   DecimalFormat decimalFormat = new DecimalFormat("0.##");
/*      */                   
/* 1026 */                   if (f2 < f1)
/* 1027 */                   { baseBean.writeLog("requestid=" + i2 + "&message=24569allannualdays==" + f2 + "leavedays==" + f1);
/*      */ 
/*      */ 
/*      */ 
/*      */                     
/*      */                      }
/*      */                   
/*      */                   else
/*      */                   
/*      */                   { 
/*      */ 
/*      */ 
/*      */                     
/* 1040 */                     if (f1 < f3) {
/* 1041 */                       str28 = "update hrmannualmanagement set annualdays = (annualdays - " + f1 + ") where annualyear = " + str40 + " and resourceid = " + str37;
/* 1042 */                       arrayList6.add(str28);
/*      */                     } else {
/* 1044 */                       str28 = "update hrmannualmanagement set annualdays = 0 where annualyear = " + str40 + " and resourceid = " + str37;
/* 1045 */                       arrayList6.add(str28);
/* 1046 */                       str28 = "update hrmannualmanagement set annualdays = (annualdays - " + Util.getFloatValue(decimalFormat.format((f1 - f3)), 0.0F) + ") where annualyear = " + str39 + " and resourceid = " + str37;
/* 1047 */                       arrayList6.add(str28);
/*      */                     } 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */                     
/* 1155 */                     if (bool1) recordSet1.writeLog("batchSubmit--requestid--" + i2 + "--105--");  }  } else if (str6.equals("submit") && !str10.equals("3") && str30.equals(String.valueOf(-12))) { if (bool1) recordSet1.writeLog("batchSubmit--requestid--" + i2 + "--104--");  String str33 = recordSet3.getString("fromDate"); String str34 = recordSet3.getString("fromTime"); String str35 = recordSet3.getString("toDate"); String str36 = recordSet3.getString("toTime"); String str37 = recordSet3.getString("resourceid"); int i13 = this.user.getUserSubCompany1(); recordSet3.executeSql("select b.subcompanyid1 from hrmresource a,hrmdepartment b where a.departmentid=b.id and a.id=" + str37); if (recordSet3.next()) { i13 = Util.getIntValue(recordSet3.getString("subcompanyid1"), -1); if (i13 <= 0) i13 = this.user.getUserSubCompany1();  }  User user = User.getUser(Util.getIntValue(str37), 0); hrmScheduleDiffUtil.setUser(user); String str38 = hrmScheduleDiffUtil.getTotalWorkingDays(str33, str34, str35, str36, i13); Calendar calendar = Calendar.getInstance(); String str39 = Util.add0(calendar.get(1), 4); String str40 = Util.add0(calendar.get(1) - 1, 4); String str41 = ""; String str42 = ""; String str43 = ""; try { String str = HrmPaidSickManagement.getUserPaidSickInfo(str29, str4); str43 = Util.TokenizerString2(str, "#")[0]; str42 = Util.TokenizerString2(str, "#")[1]; str41 = Util.TokenizerString2(str, "#")[2]; } catch (Exception exception) {} float f1 = Util.getFloatValue(str38, 0.0F); f = f1; float f2 = Util.getFloatValue(str41, 0.0F); float f3 = Util.getFloatValue(str42, 0.0F); float f4 = Util.getFloatValue(str43, 0.0F); DecimalFormat decimalFormat = new DecimalFormat("0.##"); if (f2 < f1) { baseBean.writeLog("requestid=" + i2 + "&message=183"); } else { if (f1 < f3) { str28 = "update HrmPSLManagement set psldays = (psldays - " + f1 + ") where pslyear = " + str40 + " and resourceid = " + str37; arrayList6.add(str28); } else { str28 = "update HrmPSLManagement set psldays = 0 where pslyear = " + str40 + " and resourceid = " + str37; arrayList6.add(str28); str28 = "update HrmPSLManagement set psldays = (psldays - " + Util.getFloatValue(decimalFormat.format((f1 - f3)), 0.0F) + ") where pslyear = " + str39 + " and resourceid = " + str37; arrayList6.add(str28); }  if (bool1) recordSet1.writeLog("batchSubmit--requestid--" + i2 + "--105--");  }  } else { String str33 = recordSet3.getString("fromDate"); String str34 = recordSet3.getString("fromTime"); String str35 = recordSet3.getString("toDate"); String str36 = recordSet3.getString("toTime"); String str37 = recordSet3.getString("resourceid"); String str38 = "select locationid from HrmResource where id =" + str37; recordSet3.executeSql(str38); String str39 = ""; if (recordSet3.next()) str39 = recordSet3.getString("locationid");  String str40 = "select countryid from HrmLocations where id=" + str39; recordSet3.executeSql(str40); String str41 = ""; if (recordSet3.next()) str41 = recordSet3.getString("countryid");  User user = User.getUser(Util.getIntValue(str37), 0); hrmScheduleDiffUtil.setUser(user); int i13 = this.user.getUserSubCompany1(); recordSet3.executeSql("select b.subcompanyid1 from hrmresource a,hrmdepartment b where a.departmentid=b.id and a.id=" + str37); if (recordSet3.next()) { i13 = Util.getIntValue(recordSet3.getString("subcompanyid1"), -1); if (i13 <= 0) i13 = this.user.getUserSubCompany1();  }  String str42 = hrmScheduleDiffUtil.getTotalWorkingDays(str33, str34, str35, str36, i13); float f1 = Util.getFloatValue(str42, 0.0F); f = f1; if (bool1) recordSet1.writeLog("batchSubmit--requestid--" + i2 + "--105--");
/*      */                    }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */                 
/*      */                 continue;
/*      */               } 
/*      */             } else {
/*      */               continue;
/*      */             } 
/*      */           } else {
/*      */             break;
/*      */           } 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */           
/* 1331 */           if (bool1) recordSet1.writeLog("batchSubmit--requestid--" + i2 + "--108--");
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */           
/*      */           b2++; }
/*      */       
/*      */       } else {
/* 1360 */         RequestManager requestManager = new RequestManager();
/* 1361 */         requestManager.setIsMultiDoc(this.isMultiDoc);
/* 1362 */         requestManager.setSrc(this.src);
/* 1363 */         requestManager.setIscreate(this.iscreate);
/* 1364 */         requestManager.setRequestid(this.requestid);
/* 1365 */         requestManager.setWorkflowid(this.workflowid);
/* 1366 */         requestManager.setWorkflowtype(this.workflowtype);
/* 1367 */         requestManager.setIsremark(this.isremark);
/* 1368 */         requestManager.setFormid(this.formid);
/* 1369 */         requestManager.setIsbill(this.isbill);
/* 1370 */         requestManager.setBillid(this.billid);
/* 1371 */         requestManager.setNodeid(this.nodeid);
/* 1372 */         requestManager.setNodetype(this.nodetype);
/* 1373 */         requestManager.setRequestname(this.requestname);
/* 1374 */         requestManager.setRequestlevel(this.requestlevel);
/* 1375 */         requestManager.setRemark(this.remark);
/* 1376 */         requestManager.setRequest(this.request);
/* 1377 */         requestManager.setSubmitNodeId(this.submitNodeId);
/* 1378 */         requestManager.setIntervenorid(this.Intervenorid);
/* 1379 */         requestManager.setSignType(this.SignType);
/* 1380 */         requestManager.setIsFromEditDocument(this.isFromEditDocument);
/* 1381 */         requestManager.setUser(this.user);
/* 1382 */         requestManager.setIsagentCreater(this.isagentCreater);
/* 1383 */         requestManager.setBeAgenter(this.beagenter);
/* 1384 */         requestManager.setIsPending(this.ispending);
/* 1385 */         requestManager.setRequestKey(this.wfcurrrid);
/* 1386 */         requestManager.setCanModify(this.IsCanModify);
/* 1387 */         requestManager.setCoadsigntype(this.coadsigntype);
/* 1388 */         requestManager.setEnableIntervenor(this.enableIntervenor);
/* 1389 */         requestManager.setRemarkLocation(this.remarkLocation);
/* 1390 */         requestManager.setIsFirstSubmit(this.isFirstSubmit);
/* 1391 */         requestManager.setMessageType(this.messageType);
/* 1392 */         requestManager.setChatsType(this.chatsType);
/*      */         
/* 1394 */         requestManager.saveRequestInfo();
/* 1395 */         this.requestid = requestManager.getRequestid();
/*      */         
/* 1397 */         requestManager.flowNextNode();
/*      */       } 
/* 1399 */     } catch (Exception exception) {
/* 1400 */       exception.printStackTrace();
/*      */     } finally {
/* 1402 */       if ("1".equals(this.bulkrequest)) {
/* 1403 */         if (bool1) recordSet1.writeLog("batchSubmit--requestid--" + this.requestid + "--111--");
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */         
/* 1410 */         httpSession.setAttribute(i + "_submit_token", new ArrayList());
/* 1411 */         String str1 = "";
/* 1412 */         String str2 = "";
/* 1413 */         byte b = 0;
/* 1414 */         if (this.requestkeymap != null) {
/* 1415 */           Iterator<Map.Entry> iterator = this.requestkeymap.entrySet().iterator();
/* 1416 */           while (iterator.hasNext()) {
/* 1417 */             Map.Entry entry = iterator.next();
/* 1418 */             str1 = (String)entry.getKey();
/* 1419 */             str2 = (String)entry.getValue();
/* 1420 */             bool = recordSet1.execute(" update workflow_currentoperator set isprocessing = '' where id = " + str2);
/* 1421 */             if (bool) {
/* 1422 */               recordSet2.execute(" delete from requestlistinfo where currentid = " + str2);
/* 1423 */               iterator.remove(); continue;
/*      */             } 
/* 1425 */             b++;
/*      */           } 
/*      */         } 
/*      */         
/* 1429 */         if (b > 0) {
/* 1430 */           savelasttime(b);
/*      */         }
/*      */ 
/*      */         
/* 1434 */         if (!"".equals(Util.null2String(this.requestkeystr))) {
/* 1435 */           recordSet2.executeSql(" update workflow_currentoperator set isprocessing = '' where id in (" + this.requestkeystr + ") ");
/*      */         }
/*      */       } 
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   private void savelasttime(int paramInt) {
/*      */     try {
/* 1445 */       for (byte b = 0; b < 10; b++) {
/* 1446 */         Thread.sleep(2000L);
/*      */         
/* 1448 */         savelasttime();
/*      */       } 
/* 1450 */     } catch (Exception exception) {
/*      */       
/* 1452 */       exception.printStackTrace();
/*      */     } 
/*      */   }
/*      */   private void savelasttime() {
/* 1456 */     RecordSet recordSet1 = new RecordSet();
/* 1457 */     RecordSet recordSet2 = new RecordSet();
/* 1458 */     String str1 = "";
/* 1459 */     String str2 = "";
/* 1460 */     boolean bool = true;
/* 1461 */     if (this.requestkeymap != null) {
/*      */       
/* 1463 */       Iterator<Map.Entry> iterator = this.requestkeymap.entrySet().iterator();
/* 1464 */       while (iterator.hasNext()) {
/* 1465 */         Map.Entry entry = iterator.next();
/* 1466 */         str1 = (String)entry.getKey();
/* 1467 */         str2 = (String)entry.getValue();
/* 1468 */         bool = recordSet1.execute(" update workflow_currentoperator set isprocessing = '' where id = " + str2);
/* 1469 */         if (bool) {
/* 1470 */           recordSet2.execute(" delete from requestlistinfo where currentid = " + str2);
/* 1471 */           iterator.remove();
/*      */         } 
/*      */       } 
/*      */     } 
/*      */   }
/*      */   
/*      */   public String getRequestkeystr() {
/* 1478 */     return this.requestkeystr;
/*      */   }
/*      */   
/*      */   public void setRequestkeystr(String paramString) {
/* 1482 */     this.requestkeystr = paramString;
/*      */   }
/*      */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/RequestOperationThread.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */