/*     */ package weaver.workflow.request;
/*     */ 
/*     */ import weaver.conn.RecordSet;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class ApproveCustomerParameter
/*     */ {
/*     */   private int workflowid;
/*     */   private int formid;
/*     */   private int nodeid;
/*     */   private String nodetype;
/*     */   private String requestname;
/*     */   private int approveid;
/*     */   private int managerid;
/*     */   private String seclevel;
/*     */   private String approvetype;
/*     */   private String approvevalue;
/*     */   private String gopage;
/*     */   private String backpage;
/*     */   
/*     */   public void resetParameter() {
/*  39 */     this.workflowid = 0;
/*  40 */     this.formid = 0;
/*  41 */     this.nodeid = 0;
/*  42 */     this.nodetype = "";
/*  43 */     this.requestname = "";
/*  44 */     this.approveid = 0;
/*  45 */     this.approvetype = "";
/*  46 */     this.approvevalue = "";
/*  47 */     this.seclevel = "";
/*  48 */     this.gopage = "";
/*  49 */     this.backpage = "";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setWorkflowid(int paramInt) {
/*  56 */     this.workflowid = paramInt;
/*  57 */     RecordSet recordSet = new RecordSet();
/*  58 */     recordSet.executeProc("workflow_Workflowbase_SByID", this.workflowid + "");
/*  59 */     if (recordSet.next()) {
/*  60 */       this.formid = recordSet.getInt("formid");
/*     */     }
/*     */ 
/*     */     
/*  64 */     recordSet.executeProc("workflow_CreateNode_Select", this.workflowid + "");
/*  65 */     if (recordSet.next()) {
/*  66 */       this.nodeid = recordSet.getInt(1);
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public void setNodetype(String paramString) {
/*  73 */     this.nodetype = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setApproveid(int paramInt) {
/*  80 */     this.approveid = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setRequestname(String paramString) {
/*  87 */     this.requestname = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setApprovetype(String paramString) {
/*  94 */     this.approvetype = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setGopage(String paramString) {
/* 101 */     this.gopage = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setBackpage(String paramString) {
/* 108 */     this.backpage = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getWorkflowid() {
/* 115 */     return this.workflowid;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getFormid() {
/* 122 */     return this.formid;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getNodeid() {
/* 129 */     return this.nodeid;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getNodetype() {
/* 136 */     return this.nodetype;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getApproveid() {
/* 143 */     return this.approveid;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getRequestname() {
/* 150 */     return this.requestname;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getApprovetype() {
/* 157 */     return this.approvetype;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getGopage() {
/* 164 */     return this.gopage;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getBackpage() {
/* 171 */     return this.backpage;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getApprovevalue() {
/* 178 */     return this.approvevalue;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setApprovevalue(String paramString) {
/* 185 */     this.approvevalue = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getSeclevel() {
/* 193 */     return this.seclevel;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setSeclevel(String paramString) {
/* 200 */     this.seclevel = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getManagerid() {
/* 208 */     return this.managerid;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setManagerid(int paramInt) {
/* 215 */     this.managerid = paramInt;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/ApproveCustomerParameter.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */