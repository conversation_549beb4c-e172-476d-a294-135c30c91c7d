/*     */ package weaver.workflow.request;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class RequestOpinionBrowserInfo
/*     */ {
/*     */   private String url;
/*     */   private String linkurl;
/*     */   private int fieldid;
/*     */   private int isMust;
/*     */   private String fieldtype;
/*     */   
/*     */   public int getFieldid() {
/*  31 */     return this.fieldid;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setFieldid(int paramInt) {
/*  39 */     this.fieldid = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getIsMust() {
/*  47 */     return this.isMust;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setIsMust(int paramInt) {
/*  55 */     this.isMust = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getLinkurl() {
/*  63 */     return this.linkurl;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setLinkurl(String paramString) {
/*  71 */     this.linkurl = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getUrl() {
/*  79 */     return this.url;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setUrl(String paramString) {
/*  87 */     this.url = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getFieldtype() {
/*  95 */     return this.fieldtype;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setFieldtype(String paramString) {
/* 103 */     this.fieldtype = paramString;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/RequestOpinionBrowserInfo.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */