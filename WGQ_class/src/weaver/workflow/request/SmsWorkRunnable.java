/*    */ package weaver.workflow.request;
/*    */ 
/*    */ import com.api.sms.util.SmsSendUtil;
/*    */ import java.util.ArrayList;
/*    */ import weaver.general.BaseBean;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.User;
/*    */ import weaver.sms.SMSSaveAndSend;
/*    */ import weaver.sms.SmsFromMouldEnum;
/*    */ 
/*    */ public class SmsWorkRunnable
/*    */   extends BaseBean
/*    */   implements Runnable {
/* 14 */   private String rechrmnumbers = "";
/* 15 */   private String reccrmnumbers = "";
/* 16 */   private String rechrmids = "";
/* 17 */   private String reccrmids = "";
/* 18 */   private String requestname = "";
/* 19 */   private ArrayList prefixList = null;
/* 20 */   private ArrayList suffixList = null;
/* 21 */   private User user = null;
/* 22 */   private int requestid = 0;
/*    */   
/*    */   public SmsWorkRunnable(String paramString1, String paramString2, String paramString3, String paramString4, String paramString5, User paramUser, int paramInt, ArrayList paramArrayList1, ArrayList paramArrayList2) {
/* 25 */     this.rechrmnumbers = paramString1;
/* 26 */     this.reccrmnumbers = paramString2;
/* 27 */     this.rechrmids = paramString3;
/* 28 */     this.reccrmids = paramString4;
/* 29 */     this.requestname = paramString5;
/* 30 */     this.user = paramUser;
/* 31 */     this.requestid = paramInt;
/* 32 */     this.prefixList = paramArrayList1;
/* 33 */     this.suffixList = paramArrayList2;
/*    */   }
/*    */   
/*    */   public void run() {
/* 37 */     if ("".equals(this.rechrmnumbers) || "".equals(this.rechrmids) || this.prefixList == null || this.suffixList == null || this.prefixList.size() < 1 || this.suffixList.size() < 1) {
/*    */       return;
/*    */     }
/*    */     try {
/* 41 */       String[] arrayOfString1 = this.rechrmnumbers.split(",");
/* 42 */       String[] arrayOfString2 = this.rechrmids.split(",");
/* 43 */       for (byte b = 0; b < this.prefixList.size(); b++) {
/* 44 */         String str1 = Util.null2String(this.prefixList.get(b));
/* 45 */         String str2 = Util.null2String(this.suffixList.get(b));
/* 46 */         String str3 = "";
/* 47 */         String str4 = "";
/* 48 */         if (arrayOfString1.length > b) {
/* 49 */           str3 = arrayOfString1[b];
/*    */         }
/* 51 */         if (arrayOfString2.length > b) {
/* 52 */           str4 = arrayOfString2[b];
/*    */         }
/* 54 */         if (str3 != null && !"".equals(str3)) {
/*    */ 
/*    */           
/* 57 */           if (str1 != null && !"".equals(str1)) {
/* 58 */             str1 = str1 + ":";
/*    */           }
/* 60 */           if (str2 != null && !"".equals(str2))
/*    */           {
/* 62 */             str2 = str2;
/*    */           }
/*    */           
/* 65 */           try { SMSSaveAndSend sMSSaveAndSend = new SMSSaveAndSend();
/* 66 */             sMSSaveAndSend.reset();
/*    */             
/* 68 */             String str = str1 + Util.toHtmlMode(this.requestname) + str2;
/*    */             
/* 70 */             sMSSaveAndSend.setMessage(SmsSendUtil.getSignMessage(this.user, str));
/* 71 */             sMSSaveAndSend.setRechrmnumber(str3);
/* 72 */             sMSSaveAndSend.setReccrmnumber("");
/* 73 */             sMSSaveAndSend.setCustomernumber("");
/*    */             
/* 75 */             sMSSaveAndSend.setRechrmids(str4);
/* 76 */             sMSSaveAndSend.setReccrmids("");
/* 77 */             sMSSaveAndSend.setSendnumber("");
/* 78 */             sMSSaveAndSend.setRequestid(this.requestid);
/* 79 */             sMSSaveAndSend.setUserid(this.user.getUID());
/* 80 */             sMSSaveAndSend.setUsertype(this.user.getLogintype());
/* 81 */             sMSSaveAndSend.setFrommould(SmsFromMouldEnum.WORKFLOW);
/* 82 */             sMSSaveAndSend.send(); }
/* 83 */           catch (Exception exception)
/* 84 */           { writeLog(exception); } 
/*    */         } 
/*    */       } 
/* 87 */     } catch (Exception exception) {
/* 88 */       writeLog(exception);
/*    */     } 
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/SmsWorkRunnable.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */