/*     */ package weaver.workflow.request;
/*     */ 
/*     */ import java.sql.Connection;
/*     */ import java.sql.SQLException;
/*     */ import java.sql.Statement;
/*     */ import java.util.ArrayList;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.conn.RecordSetDataSource;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.integration.logging.Logger;
/*     */ import weaver.integration.logging.LoggerFactory;
/*     */ import weaver.workflow.workflow.WorkflowComInfo;
/*     */ import weaver.workflow.workflow.WorkflowVersion;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class RequestManageWriteBackAction
/*     */   extends BaseBean
/*     */ {
/*  33 */   private static Logger newlog = LoggerFactory.getLogger(RequestManageWriteBackAction.class);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void doWriteBack(int paramInt) {
/*  41 */     Connection connection = null;
/*  42 */     WorkflowVersion workflowVersion = new WorkflowVersion();
/*  43 */     Statement statement = null;
/*     */     
/*     */     try {
/*  46 */       WorkflowComInfo workflowComInfo = new WorkflowComInfo();
/*  47 */       RecordSet recordSet = new RecordSet();
/*  48 */       String str1 = "";
/*  49 */       String str2 = "";
/*  50 */       String str3 = "";
/*  51 */       recordSet.executeSql("select workflowid from workflow_requestbase where requestid=" + paramInt);
/*  52 */       if (recordSet.next()) {
/*  53 */         str1 = recordSet.getString("workflowid");
/*  54 */         str2 = Util.null2String(workflowComInfo.getIsBill(str1));
/*  55 */         str3 = Util.null2String(workflowComInfo.getFormId(str1));
/*     */       } 
/*  57 */       String str4 = WorkflowVersion.getVersionStringByWfid(str1);
/*  58 */       if (str4.startsWith(",")) {
/*  59 */         str4 = str4.substring(1, str4.length());
/*     */       }
/*  61 */       if (str4.endsWith(",")) {
/*  62 */         str4 = str4.substring(0, str4.length() - 1);
/*     */       }
/*  64 */       String str5 = "";
/*  65 */       if (!str3.equals("")) {
/*  66 */         if (str2.equals("0")) {
/*  67 */           str5 = "workflow_form";
/*  68 */         } else if (str2.equals("1")) {
/*  69 */           recordSet.executeSql("select tablename from workflow_bill where id=" + str3);
/*  70 */           if (recordSet.next()) str5 = recordSet.getString("tablename");
/*     */         
/*     */         } 
/*     */       }
/*  74 */       String str6 = "";
/*  75 */       ArrayList<String> arrayList1 = new ArrayList();
/*  76 */       ArrayList<String> arrayList2 = new ArrayList();
/*  77 */       String str7 = "";
/*  78 */       String str8 = "";
/*  79 */       String str9 = "";
/*  80 */       String str10 = "requestid";
/*  81 */       String str11 = "";
/*     */ 
/*     */       
/*  84 */       recordSet.executeQuery("select mainid from outerdatawfdetail where requestid = ?", new Object[] { Integer.valueOf(paramInt) });
/*  85 */       if (recordSet.next()) {
/*  86 */         str11 = recordSet.getString(1);
/*     */       }
/*     */       
/*  89 */       if (Util.null2String(str11).trim().length() > 0 && Util.null2String(str1).trim().length() > 0) {
/*  90 */         recordSet.executeSql("select a.requestid, a.outermaintable,b.wffieldname,b.outerfieldname,a.datasourceid from outerdatawfset a, outerdatawfsetdetail b where a.id=" + str11 + " and a.workflowid in (" + str4 + ") and a.id = b.mainid and b.iswriteback='1' and datarecordtype='2'  and (a.isview<>1  or a.isview is null)");
/*  91 */         newlog.info("查询回写字段sql:select a.requestid, a.outermaintable,b.wffieldname,b.outerfieldname,a.datasourceid from outerdatawfset a, outerdatawfsetdetail b where a.id=" + str11 + " and a.workflowid in (" + str4 + ") and a.id = b.mainid and b.iswriteback='1' and datarecordtype='2'  and (a.isview<>1  or a.isview is null)");
/*  92 */         while (recordSet.next()) {
/*  93 */           str8 = Util.null2String(recordSet.getString("outermaintable"));
/*  94 */           if (str8.equals("")) {
/*     */             
/*  96 */             newlog.error("流程触发集成_归档回写：TABLE FOT WRITEBACK IS NOT FIXED!");
/*     */             break;
/*     */           } 
/*  99 */           str9 = Util.null2String(recordSet.getString("datasourceid"));
/* 100 */           String str12 = Util.null2String(recordSet.getString("outerfieldname"));
/* 101 */           if (str12.equals(""))
/* 102 */             continue;  String str13 = Util.null2String(recordSet.getString("wffieldname"));
/* 103 */           arrayList1.add(str12);
/* 104 */           arrayList2.add(str13);
/* 105 */           str7 = str7 + str13 + ",";
/* 106 */           str10 = Util.null2String(recordSet.getString("requestid"));
/*     */         } 
/*     */       } 
/* 109 */       if (!str7.equals("")) {
/* 110 */         str7 = str7.substring(0, str7.length() - 1);
/*     */       }
/* 112 */       if (!str8.equals("") && !str7.equals("")) {
/* 113 */         recordSet.executeSql("select " + str7 + " from " + str5 + " where requestid=" + paramInt);
/* 114 */         if (recordSet.next()) {
/* 115 */           str6 = "update " + str8 + " set ";
/* 116 */           for (byte b = 0; b < arrayList1.size(); b++) {
/* 117 */             String str12 = arrayList1.get(b);
/* 118 */             String str13 = arrayList2.get(b);
/*     */             
/* 120 */             str12 = str12.replace(str8.trim() + ".", "");
/* 121 */             newlog.error("流程触发集成_归档回写outerfieldname：" + str12 + "    outermaintable:" + str8.trim() + ".");
/* 122 */             String str14 = Util.null2String(recordSet.getString(str13));
/*     */             
/* 124 */             str6 = str6 + str12 + "='" + str14 + "',";
/*     */           } 
/*     */           
/* 127 */           str6 = str6.substring(0, str6.lastIndexOf(","));
/* 128 */           str6 = str6 + " where " + str10 + "='" + paramInt + "'";
/*     */           
/* 130 */           newlog.error("流程触发集成_归档回写update语句：" + str6);
/*     */         } 
/*     */       } 
/*     */       
/* 134 */       if (!str6.equals(""))
/*     */       {
/*     */ 
/*     */ 
/*     */         
/* 139 */         RecordSetDataSource recordSetDataSource = new RecordSetDataSource(str9);
/* 140 */         boolean bool = recordSetDataSource.executeSql(str6);
/* 141 */         newlog.error("流程触发集成_归档回写requestid:" + paramInt + " 回写结果:" + bool);
/*     */       }
/*     */     
/* 144 */     } catch (Exception exception) {
/* 145 */       newlog.error("流程触发集成_归档回写：", exception);
/*     */     } finally {
/*     */       try {
/* 148 */         if (statement != null) {
/* 149 */           statement.close();
/*     */         }
/* 151 */       } catch (SQLException sQLException) {
/* 152 */         sQLException.printStackTrace();
/*     */       } 
/*     */       try {
/* 155 */         if (connection != null) {
/* 156 */           connection.close();
/*     */         }
/* 158 */       } catch (SQLException sQLException) {
/* 159 */         sQLException.printStackTrace();
/*     */       } 
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/RequestManageWriteBackAction.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */