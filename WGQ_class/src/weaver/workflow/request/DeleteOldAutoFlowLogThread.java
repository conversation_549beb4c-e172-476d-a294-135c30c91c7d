/*    */ package weaver.workflow.request;
/*    */ 
/*    */ import java.text.SimpleDateFormat;
/*    */ import java.util.Calendar;
/*    */ import java.util.Date;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.file.Prop;
/*    */ import weaver.general.BaseBean;
/*    */ import weaver.general.Util;
/*    */ import weaver.system.ThreadWork;
/*    */ 
/*    */ 
/*    */ public class DeleteOldAutoFlowLogThread
/*    */   extends BaseBean
/*    */   implements ThreadWork
/*    */ {
/*    */   public void doThreadWork() {
/* 18 */     deleteOldAutoFlowLog();
/* 19 */     deleteOldRequestFlowLog();
/*    */   }
/*    */   
/*    */   private Boolean deleteOldAutoFlowLog() {
/* 23 */     Boolean bool = Boolean.valueOf(true);
/*    */     try {
/* 25 */       RecordSet recordSet = new RecordSet();
/* 26 */       String str1 = getDeleteLogData();
/* 27 */       String str2 = "delete from workflow_autoflowlog where  operatedate<=? ";
/* 28 */       bool = Boolean.valueOf(recordSet.executeUpdate(str2, new Object[] { str1 }));
/* 29 */     } catch (Exception exception) {
/* 30 */       exception.printStackTrace();
/*    */     } 
/* 32 */     return bool;
/*    */   }
/*    */ 
/*    */   
/*    */   private Boolean deleteOldRequestFlowLog() {
/* 37 */     Boolean bool = Boolean.valueOf(true);
/*    */     try {
/* 39 */       RecordSet recordSet = new RecordSet();
/* 40 */       String str1 = getDeleteLogData();
/* 41 */       String str2 = "delete from workflow_requestflowlog where  operatedate<=? ";
/* 42 */       bool = Boolean.valueOf(recordSet.executeUpdate(str2, new Object[] { str1 }));
/* 43 */     } catch (Exception exception) {
/* 44 */       exception.printStackTrace();
/*    */     } 
/* 46 */     return bool;
/*    */   }
/*    */ 
/*    */   
/*    */   private String getDeleteLogData() {
/* 51 */     Prop.getInstance(); int i = Util.getIntValue(Prop.getPropValue("wfAutoflowLog", "deleteData"), 30);
/* 52 */     SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
/* 53 */     Calendar calendar = Calendar.getInstance();
/* 54 */     calendar.add(5, -i);
/* 55 */     Date date = calendar.getTime();
/* 56 */     return simpleDateFormat.format(date);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/DeleteOldAutoFlowLogThread.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */