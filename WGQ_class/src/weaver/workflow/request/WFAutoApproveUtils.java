/*     */ package weaver.workflow.request;
/*     */ 
/*     */ import com.api.doc.detail.util.ImageConvertUtil;
/*     */ import com.cloudstore.dev.api.util.Util_TableMap;
/*     */ import com.engine.workflow.biz.requestFlowLog.RequestFlowLogBiz;
/*     */ import com.engine.workflow.biz.requestForm.RequestAutoApproveBiz;
/*     */ import com.engine.workflow.biz.requestForm.RequestFlowRemindBiz;
/*     */ import com.engine.workflow.biz.requestForm.RequestRemindBiz;
/*     */ import com.engine.workflow.biz.requestForm.WorkflowPenetrateBiz;
/*     */ import com.engine.workflow.constant.RequestFlowLogSrcType;
/*     */ import com.engine.workflow.constant.SignSource;
/*     */ import com.engine.workflow.entity.RequestFlowLogEntity;
/*     */ import com.engine.workflow.entity.requestForm.AutoApproveEntity;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Calendar;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import java.util.Queue;
/*     */ import java.util.concurrent.ConcurrentLinkedQueue;
/*     */ import java.util.regex.Matcher;
/*     */ import java.util.regex.Pattern;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import org.json.JSONException;
/*     */ import org.json.JSONObject;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.conn.RecordSetTrans;
/*     */ import weaver.docs.docs.interfaces.PdfConvertor;
/*     */ import weaver.docs.docs.util.DocImagefileToPdf;
/*     */ import weaver.docs.docs.util.DocImagefileToPdfUseWps;
/*     */ import weaver.file.Prop;
/*     */ import weaver.filter.WeaverRequest;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.TimeUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ import weaver.odoc.docs.MaintextToPDF;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.workflow.msg.MsgPushUtil;
/*     */ import weaver.workflow.msg.entity.MsgEntity;
/*     */ import weaver.workflow.msg.entity.MsgOperateType;
/*     */ 
/*     */ public class WFAutoApproveUtils implements Runnable {
/*     */   public static final String AUTO_APPROVE_IS_REMARK = "6";
/*     */   private RequestManager requestManager;
/*  47 */   private static final Pattern pattern = Pattern.compile("<br/><br/><span style='font-size:11px;color:#666;'>.*?</span>");
/*     */   private WeaverRequest request;
/*  49 */   private Queue<AutoApproveParams> queue = new ConcurrentLinkedQueue<>();
/*     */ 
/*     */   
/*     */   private int flowNextLinkId;
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean isAutoApprove(RequestManager paramRequestManager, RecordSetTrans paramRecordSetTrans, Map<Integer, AutoApproveParams> paramMap, ArrayList paramArrayList, int paramInt) throws Exception {
/*     */     Map map;
/*  58 */     int i = paramRequestManager.getWorkflowid();
/*  59 */     int j = paramRequestManager.getFormid();
/*  60 */     int k = paramRequestManager.getRequestid();
/*  61 */     int m = paramRequestManager.getNextNodeid();
/*  62 */     String str1 = paramRequestManager.getNextNodetype();
/*     */ 
/*     */     
/*  65 */     AutoApproveEntity autoApproveEntity = initAutoApproveEntity(paramRequestManager, paramInt);
/*  66 */     autoApproveEntity.setRst(paramRecordSetTrans);
/*     */     
/*  68 */     boolean bool = false;
/*  69 */     boolean bool1 = false;
/*  70 */     if (getIsAutoApprove(i)) {
/*  71 */       map = RequestAutoApproveBiz.judgeAutoApprove(autoApproveEntity, paramRequestManager.getUser());
/*  72 */       if (map == null || map.isEmpty()) {
/*  73 */         return false;
/*     */       }
/*  75 */       boolean bool2 = ((Boolean)map.get("autoApproveFlag")).booleanValue();
/*  76 */       int i1 = Util.getIntValue((String)map.get("autoApproveFailedFlag"), 0);
/*  77 */       if (!bool2) {
/*  78 */         if (i1 != 0 && i1 != -1 && i != 1 && k > 0 && !"".equals(Util.null2String(paramRequestManager.getCurrentDate()))) {
/*  79 */           List list = WfAutoflowLogUtil.initParams(k, 0, paramRequestManager.getNodeid(), m, "2", i1, paramRequestManager.getUserId(), paramRequestManager.getCurrentDate(), paramRequestManager.getCurrentTime());
/*  80 */           WfAutoflowLogUtil.putWfautoParamList(list);
/*     */         } 
/*  82 */         return false;
/*     */       } 
/*  84 */       if (bool2 && i1 == 0 && i != 1 && k > 0 && !"".equals(Util.null2String(paramRequestManager.getCurrentDate()))) {
/*  85 */         List list = WfAutoflowLogUtil.initParams(k, 1, paramRequestManager.getNodeid(), m, "2", i1, paramRequestManager.getUserId(), paramRequestManager.getCurrentDate(), paramRequestManager.getCurrentTime());
/*  86 */         WfAutoflowLogUtil.putWfautoParamList(list);
/*     */       } 
/*  88 */       bool1 = autoFlowRequestlogTrail(i);
/*     */     } else {
/*     */       
/*  91 */       map = WorkflowPenetrateBiz.judgeWorkflowPenetrate(autoApproveEntity, paramRequestManager.getUser(), paramRequestManager);
/*  92 */       if (map == null || map.isEmpty()) {
/*  93 */         return false;
/*     */       }
/*  95 */       boolean bool2 = (Util.getIntValue(Prop.getPropValue("wfAutoFlowLog", "wfAutoFlowLogisclose")) == 1) ? true : false;
/*  96 */       boolean bool3 = ((Boolean)map.get("autoApproveFlag")).booleanValue();
/*  97 */       int i1 = Util.getIntValue((String)map.get("autoApproveFailedFlag"), 0);
/*  98 */       if (bool2) {
/*  99 */         if (!bool3) {
/* 100 */           return false;
/*     */         }
/*     */       } else {
/* 103 */         String str5 = paramRequestManager.getCurrentDate();
/* 104 */         String str6 = paramRequestManager.getCurrentTime();
/* 105 */         if (!bool3) {
/* 106 */           if (i1 != 0 && i1 != -1 && i != 1 && str5 != null && !"".equals(str5) && k > 0) {
/* 107 */             List list = WfAutoflowLogUtil.initParams(k, 0, autoApproveEntity.getNodeid(), m, "1", i1, paramRequestManager.getUser().getUID(), str5, str6);
/* 108 */             WfAutoflowLogUtil.putWfautoParamList(list);
/*     */           } 
/* 110 */           return false;
/*     */         } 
/* 112 */         if (bool3 && i1 == 0 && i != 1 && str5 != null && !"".equals(str5) && k > 0) {
/* 113 */           List list = WfAutoflowLogUtil.initParams(k, 1, autoApproveEntity.getNodeid(), m, "1", i1, paramRequestManager.getUser().getUID(), str5, str6);
/* 114 */           WfAutoflowLogUtil.putWfautoParamList(list);
/*     */         } 
/*     */       } 
/* 117 */       paramRequestManager.setIsAutoRemark(Util.null2String(map.get("isAutoRemark")));
/* 118 */       bool = true;
/* 119 */       bool1 = penetrateRequestlogTrail();
/*     */     } 
/*     */     
/* 122 */     int n = Util.getIntValue(Util.null2String(map.get("autoApproveUserId")));
/* 123 */     String str2 = Util.null2String(map.get("autoApproveCurrentOperatorId"));
/* 124 */     String str3 = Util.null2String(map.get("signremark"));
/* 125 */     String str4 = Util.null2String(map.get("handWrittenSign"));
/*     */     
/* 127 */     AutoApproveParams autoApproveParams = new AutoApproveParams();
/* 128 */     autoApproveParams.setNodeid(m);
/* 129 */     autoApproveParams.setNodetype(str1);
/* 130 */     autoApproveParams.setUserid(n);
/* 131 */     autoApproveParams.setLaseUserId(paramRequestManager.getUserId());
/* 132 */     autoApproveParams.setPenetrate(bool);
/* 133 */     setRemarkInfo(Util.getIntValue(str1), str3, paramRequestManager.getIsAutoRemark(), autoApproveParams, str4);
/*     */     
/* 135 */     autoApproveParams.setPoppuplist(paramArrayList);
/* 136 */     autoApproveParams.setSrc("submit");
/* 137 */     autoApproveParams.setCurrentoperatorid(str2);
/* 138 */     autoApproveParams.setApproveTime(Util.null2String(map.get("approveTime")));
/* 139 */     autoApproveParams.setLeaveMarks(bool1);
/* 140 */     paramMap.put(Integer.valueOf(m), autoApproveParams);
/*     */ 
/*     */     
/* 143 */     paramRecordSetTrans.executeUpdate("update workflow_currentoperator set isremark = ?,viewtype = -2  where id in (" + str2 + ") and requestid = ?", new Object[] { "6", Integer.valueOf(k) });
/* 144 */     return true;
/*     */   }
/*     */ 
/*     */   
/*     */   private AutoApproveEntity initAutoApproveEntity(RequestManager paramRequestManager, int paramInt) {
/* 149 */     AutoApproveEntity autoApproveEntity = new AutoApproveEntity();
/* 150 */     autoApproveEntity.setHasEflowToAssignNode(paramRequestManager.isHasEflowToAssignNode());
/* 151 */     autoApproveEntity.setNextnodeid(paramRequestManager.getNextNodeid());
/* 152 */     autoApproveEntity.setNextnodeattr(paramInt);
/* 153 */     autoApproveEntity.setNextnodetype(Util.getIntValue(paramRequestManager.getNextNodetype()));
/* 154 */     autoApproveEntity.setRequestid(paramRequestManager.getRequestid());
/* 155 */     autoApproveEntity.setWorkflowid(paramRequestManager.getWorkflowid());
/* 156 */     autoApproveEntity.setNodeid(paramRequestManager.getNodeid());
/* 157 */     autoApproveEntity.setNodetype(Util.getIntValue(paramRequestManager.getNodetype()));
/* 158 */     autoApproveEntity.setNodeattr(paramRequestManager.getNodeattribute());
/* 159 */     autoApproveEntity.setFormid(paramRequestManager.getFormid());
/* 160 */     autoApproveEntity.setIsbill(paramRequestManager.getIsbill());
/* 161 */     autoApproveEntity.setSrc(paramRequestManager.getSrc());
/* 162 */     autoApproveEntity.setNextnodeids(paramRequestManager.getNextnodeids());
/* 163 */     return autoApproveEntity;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void setRemarkInfo(int paramInt, String paramString1, String paramString2, AutoApproveParams paramAutoApproveParams, String paramString3) {
/* 176 */     SignSource signSource = (paramInt == 1) ? SignSource.AUTO_APPROVE : SignSource.AUTO_SUBMIT;
/* 177 */     signSource = paramAutoApproveParams.isPenetrate ? SignSource.WORKFLOW_PENETREATE : signSource;
/* 178 */     if ("1".equals(paramString2)) {
/* 179 */       if (!"".equals(paramString1)) {
/* 180 */         Matcher matcher = pattern.matcher(paramString1);
/* 181 */         if (matcher.find()) {
/* 182 */           paramString1 = matcher.replaceAll("");
/*     */         }
/*     */         
/* 185 */         paramAutoApproveParams.setSignremark(paramString1);
/*     */       } 
/* 187 */       paramAutoApproveParams.setHandWrittenSign(paramString3);
/*     */     } 
/* 189 */     paramAutoApproveParams.setSignSource(signSource);
/*     */   }
/*     */ 
/*     */   
/*     */   public class AutoApproveParams
/*     */   {
/*     */     private int nodeid;
/*     */     
/*     */     private String nodetype;
/*     */     
/*     */     private int userid;
/*     */     
/*     */     private String signremark;
/*     */     
/*     */     private ArrayList poppuplist;
/*     */     
/*     */     private String src;
/*     */     
/*     */     private String currentoperatorid;
/*     */     
/*     */     private String approveTime;
/*     */     private SignSource signSource;
/*     */     private int laseUserId;
/*     */     private boolean isPenetrate;
/*     */     private boolean isLeaveMarks;
/*     */     private int startLogId;
/*     */     private String receiveUserIds;
/*     */     private String receiveUserNames;
/*     */     private String ccUserIds;
/*     */     private String ccUserNames;
/*     */     private String handWrittenSign;
/*     */     
/*     */     public int getNodeid() {
/* 222 */       return this.nodeid;
/*     */     }
/*     */     
/*     */     public void setNodeid(int param1Int) {
/* 226 */       this.nodeid = param1Int;
/*     */     }
/*     */     
/*     */     public String getNodetype() {
/* 230 */       return this.nodetype;
/*     */     }
/*     */     
/*     */     public void setNodetype(String param1String) {
/* 234 */       this.nodetype = param1String;
/*     */     }
/*     */     
/*     */     public int getUserid() {
/* 238 */       return this.userid;
/*     */     }
/*     */     
/*     */     public void setUserid(int param1Int) {
/* 242 */       this.userid = param1Int;
/*     */     }
/*     */     
/*     */     public String getSignremark() {
/* 246 */       return this.signremark;
/*     */     }
/*     */     
/*     */     public void setSignremark(String param1String) {
/* 250 */       this.signremark = param1String;
/*     */     }
/*     */     
/*     */     public ArrayList getPoppuplist() {
/* 254 */       return this.poppuplist;
/*     */     }
/*     */     
/*     */     public void setPoppuplist(ArrayList param1ArrayList) {
/* 258 */       this.poppuplist = param1ArrayList;
/*     */     }
/*     */     
/*     */     public String getSrc() {
/* 262 */       return this.src;
/*     */     }
/*     */     
/*     */     public void setSrc(String param1String) {
/* 266 */       this.src = param1String;
/*     */     }
/*     */     
/*     */     public String getCurrentoperatorid() {
/* 270 */       return this.currentoperatorid;
/*     */     }
/*     */     
/*     */     public void setCurrentoperatorid(String param1String) {
/* 274 */       this.currentoperatorid = param1String;
/*     */     }
/*     */     
/*     */     public String getApproveTime() {
/* 278 */       return this.approveTime;
/*     */     }
/*     */     
/*     */     public void setApproveTime(String param1String) {
/* 282 */       this.approveTime = param1String;
/*     */     }
/*     */     
/*     */     public SignSource getSignSource() {
/* 286 */       return this.signSource;
/*     */     }
/*     */     
/*     */     public void setSignSource(SignSource param1SignSource) {
/* 290 */       this.signSource = param1SignSource;
/*     */     }
/*     */     
/*     */     public int getLaseUserId() {
/* 294 */       return this.laseUserId;
/*     */     }
/*     */     
/*     */     public void setLaseUserId(int param1Int) {
/* 298 */       this.laseUserId = param1Int;
/*     */     }
/*     */     
/*     */     public boolean isPenetrate() {
/* 302 */       return this.isPenetrate;
/*     */     }
/*     */     
/*     */     public void setPenetrate(boolean param1Boolean) {
/* 306 */       this.isPenetrate = param1Boolean;
/*     */     }
/*     */     
/*     */     public boolean isLeaveMarks() {
/* 310 */       return this.isLeaveMarks;
/*     */     }
/*     */     
/*     */     public void setLeaveMarks(boolean param1Boolean) {
/* 314 */       this.isLeaveMarks = param1Boolean;
/*     */     }
/*     */     
/*     */     public int getStartLogId() {
/* 318 */       return this.startLogId;
/*     */     }
/*     */     
/*     */     public void setStartLogId(int param1Int) {
/* 322 */       this.startLogId = param1Int;
/*     */     }
/*     */     
/*     */     public String getReceiveUserIds() {
/* 326 */       return this.receiveUserIds;
/*     */     }
/*     */     
/*     */     public void setReceiveUserIds(String param1String) {
/* 330 */       this.receiveUserIds = param1String;
/*     */     }
/*     */     
/*     */     public String getReceiveUserNames() {
/* 334 */       return this.receiveUserNames;
/*     */     }
/*     */     
/*     */     public void setReceiveUserNames(String param1String) {
/* 338 */       this.receiveUserNames = param1String;
/*     */     }
/*     */     
/*     */     public String getCcUserIds() {
/* 342 */       return this.ccUserIds;
/*     */     }
/*     */     
/*     */     public void setCcUserIds(String param1String) {
/* 346 */       this.ccUserIds = param1String;
/*     */     }
/*     */     
/*     */     public String getCcUserNames() {
/* 350 */       return this.ccUserNames;
/*     */     }
/*     */     
/*     */     public void setCcUserNames(String param1String) {
/* 354 */       this.ccUserNames = param1String;
/*     */     }
/*     */     
/*     */     public String getHandWrittenSign() {
/* 358 */       return this.handWrittenSign;
/*     */     }
/*     */     
/*     */     public void setHandWrittenSign(String param1String) {
/* 362 */       this.handWrittenSign = param1String;
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static boolean updateManagerFieldValue(RequestManager paramRequestManager, int paramInt) {
/* 373 */     RecordSet recordSet = new RecordSet();
/* 374 */     String str1 = "";
/* 375 */     int i = paramRequestManager.getFormid();
/* 376 */     int j = paramRequestManager.getIsbill();
/* 377 */     String str2 = paramRequestManager.getUser().getManagerid();
/* 378 */     recordSet.executeQuery("select userid,agenttype,agentorbyagentid from workflow_currentoperator where id = ?", new Object[] { Integer.valueOf(paramInt) });
/* 379 */     if (recordSet.next() && 
/* 380 */       "2".equals(Util.null2String(recordSet.getString("agenttype")))) {
/*     */       try {
/* 382 */         ResourceComInfo resourceComInfo = new ResourceComInfo();
/* 383 */         str2 = resourceComInfo.getManagerID(recordSet.getString("agentorbyagentid"));
/* 384 */       } catch (Exception exception) {
/* 385 */         exception.printStackTrace();
/*     */       } 
/*     */     }
/*     */     
/* 389 */     if (j == 1) {
/* 390 */       recordSet.executeQuery(" select tablename from workflow_bill a where exists (select 1 from workflow_billfield b where b.billid = a.id and b.billid  = ? and b.fieldname = 'manager')", new Object[] { Integer.valueOf(i) });
/* 391 */       if (recordSet.next()) {
/* 392 */         str1 = Util.null2String(recordSet.getString("tablename"));
/*     */       }
/*     */     } else {
/* 395 */       recordSet.executeQuery("select fieldname from workflow_formfield a left join workflow_formdict b on a.fieldid  = b.id where a.formid  = ? and b.fieldname ='manager'", new Object[] { Integer.valueOf(i) });
/* 396 */       if (recordSet.next()) {
/* 397 */         str1 = "workflow_form";
/*     */       }
/*     */     } 
/* 400 */     if (str2 == null || "".equals(str2)) {
/* 401 */       str2 = "0";
/*     */     }
/* 403 */     if (!"".equals(str1)) {
/* 404 */       return recordSet.executeUpdate("update " + str1 + " set manager = ?  where requestid  = ?", new Object[] { str2, Integer.valueOf(paramRequestManager.getRequestid()) });
/*     */     }
/*     */     
/* 407 */     return true;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static boolean executeNodeCheckAddinRules(RequestManager paramRequestManager) {
/* 416 */     User user = paramRequestManager.getUser();
/*     */     
/*     */     try {
/* 419 */       RequestCheckAddinRules requestCheckAddinRules = new RequestCheckAddinRules();
/* 420 */       requestCheckAddinRules.resetParameter();
/*     */ 
/*     */ 
/*     */       
/* 424 */       requestCheckAddinRules.setNodeid(paramRequestManager.getNodeid());
/*     */       
/* 426 */       requestCheckAddinRules.setRequestid(paramRequestManager.getRequestid());
/* 427 */       requestCheckAddinRules.setWorkflowid(paramRequestManager.getWorkflowid());
/* 428 */       requestCheckAddinRules.setObjid(paramRequestManager.getNodeid());
/* 429 */       requestCheckAddinRules.setObjtype(1);
/* 430 */       requestCheckAddinRules.setIsbill(paramRequestManager.getIsbill());
/* 431 */       requestCheckAddinRules.setFormid(paramRequestManager.getFormid());
/* 432 */       requestCheckAddinRules.setIspreadd("0");
/* 433 */       requestCheckAddinRules.setRequestManager(paramRequestManager);
/* 434 */       requestCheckAddinRules.setUser(paramRequestManager.getUser());
/* 435 */       requestCheckAddinRules.checkAddinRules();
/* 436 */       if (requestCheckAddinRules.getDoDmlResult().startsWith("-1")) {
/* 437 */         if ("-1".equals(requestCheckAddinRules.getDoDmlResult())) {
/* 438 */           paramRequestManager.setMessage("126221");
/* 439 */           paramRequestManager.setMessagecontent(WorkflowRequestMessage.resolveDetailInfo(SystemEnv.getHtmlLabelName(10000171, Util.getIntValue(user.getLanguage()))));
/*     */         } else {
/* 441 */           paramRequestManager.setMessage("126221");
/* 442 */           paramRequestManager.setMessagecontent(WorkflowRequestMessage.resolveDetailInfo(SystemEnv.getHtmlLabelName(10000192, Util.getIntValue(user.getLanguage())) + ":" + requestCheckAddinRules.getDoDmlResult().replace("-1,", "")));
/*     */         } 
/* 444 */         return false;
/*     */       } 
/* 446 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 447 */       hashMap.put("objId", "" + paramRequestManager.getNodeid());
/* 448 */       hashMap.put("objType", "1");
/* 449 */       hashMap.put("isPreAdd", "0");
/* 450 */       paramRequestManager.getRequestCheckAddinRulesList().add(hashMap);
/* 451 */       return true;
/* 452 */     } catch (Exception exception) {
/* 453 */       paramRequestManager.setMessage("126221");
/* 454 */       if (exception.getMessage().indexOf("workflow interface action error") > -1) {
/* 455 */         paramRequestManager.writeLog(exception);
/*     */       }
/* 457 */       if ("".equals(paramRequestManager.getMessage())) {
/* 458 */         paramRequestManager.setMessage("126221");
/*     */       }
/* 460 */       if ("".equals(paramRequestManager.getMessagecontent())) {
/* 461 */         paramRequestManager.setMessagecontent(WorkflowRequestMessage.resolveDetailInfo(SystemEnv.getHtmlLabelName(10000171, Util.getIntValue(user.getLanguage()))));
/*     */       }
/* 463 */       return false;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public void run() {
/*     */     try {
/* 471 */       while (!this.queue.isEmpty()) {
/* 472 */         AutoApproveParams autoApproveParams = this.queue.poll();
/* 473 */         Thread.sleep(1000L);
/* 474 */         executeflownextnode(autoApproveParams);
/*     */       } 
/* 476 */     } catch (Exception exception) {
/* 477 */       exception.printStackTrace();
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public WFAutoApproveUtils() {}
/*     */ 
/*     */   
/*     */   public WFAutoApproveUtils(RequestManager paramRequestManager, Map<Integer, AutoApproveParams> paramMap) {
/* 487 */     this.requestManager = paramRequestManager;
/* 488 */     for (Map.Entry<Integer, AutoApproveParams> entry : paramMap.entrySet()) {
/* 489 */       this.queue.offer((AutoApproveParams)entry.getValue());
/*     */     }
/*     */   }
/*     */   
/*     */   public void excuteflownextnodeForTest() {
/*     */     try {
/* 495 */       while (!this.queue.isEmpty()) {
/* 496 */         AutoApproveParams autoApproveParams = this.queue.poll();
/* 497 */         executeflownextnode(autoApproveParams, true);
/*     */       } 
/* 499 */     } catch (Exception exception) {
/* 500 */       exception.printStackTrace();
/*     */     } 
/*     */   }
/*     */   
/*     */   public void executeflownextnode(AutoApproveParams paramAutoApproveParams) {
/* 505 */     executeflownextnode(paramAutoApproveParams, false);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void executeflownextnode(AutoApproveParams paramAutoApproveParams, boolean paramBoolean) {
/* 515 */     User user1 = new User(paramAutoApproveParams.getUserid());
/* 516 */     User user2 = new User(paramAutoApproveParams.getLaseUserId());
/* 517 */     RequestRemindBiz requestRemindBiz1 = new RequestRemindBiz(user1);
/* 518 */     RequestRemindBiz requestRemindBiz2 = new RequestRemindBiz(user2);
/* 519 */     RequestManager requestManager = new RequestManager();
/* 520 */     requestManager.setSrc(paramAutoApproveParams.getSrc());
/* 521 */     requestManager.setIscreate("0");
/* 522 */     requestManager.setRequestid(this.requestManager.getRequestid());
/* 523 */     requestManager.setWorkflowid(this.requestManager.getWorkflowid());
/* 524 */     requestManager.setWorkflowtype(this.requestManager.getWorkflowtype());
/* 525 */     requestManager.setIsremark(0);
/* 526 */     requestManager.setFormid(this.requestManager.getFormid());
/* 527 */     requestManager.setIsbill(this.requestManager.getIsbill());
/* 528 */     requestManager.setBillid(this.requestManager.getBillid());
/* 529 */     requestManager.setBilltablename(this.requestManager.getBillTableName());
/* 530 */     requestManager.setNodeid(paramAutoApproveParams.getNodeid());
/* 531 */     requestManager.setNodetype(paramAutoApproveParams.getNodetype());
/* 532 */     requestManager.setRequestname(this.requestManager.getRequestname());
/* 533 */     requestManager.setRemark(paramAutoApproveParams.getSignremark());
/* 534 */     requestManager.setClientType(paramAutoApproveParams.getSignSource().getType());
/* 535 */     requestManager.setDoAutoApprove(true);
/* 536 */     requestManager.setUser(user1);
/* 537 */     requestManager.setAutoApproveQueue(this.queue);
/* 538 */     requestManager.setAutoApproveParams(paramAutoApproveParams);
/* 539 */     requestManager.setHandWrittenSign(paramAutoApproveParams.getHandWrittenSign());
/* 540 */     requestManager.setRequest((HttpServletRequest)this.request);
/*     */     
/* 542 */     RequestFlowLogEntity requestFlowLogEntity = RequestFlowLogBiz.initRequestFlowLogEntity(user1, RequestFlowLogSrcType.AUTOFLOWLOG, (HttpServletRequest)this.request);
/* 543 */     requestFlowLogEntity.setRequestid(requestManager.getRequestid());
/* 544 */     requestFlowLogEntity.setNodeid(paramAutoApproveParams.getNodeid());
/* 545 */     requestManager.setRequestFlowLogEntity(requestFlowLogEntity);
/*     */     
/* 547 */     boolean bool = updateManagerFieldValue(requestManager, Util.getIntValue(paramAutoApproveParams.getCurrentoperatorid()));
/* 548 */     if (!bool) {
/* 549 */       String str = requestManager.getUser().getManagerid();
/* 550 */       requestManager.writeLog("--自动批准更新managerid报错--" + str + "reqid--" + this.requestManager.getRequestid());
/* 551 */       bool = true;
/*     */     } 
/*     */ 
/*     */     
/* 555 */     if (bool) bool = executeNodeCheckAddinRules(requestManager);
/*     */     
/* 557 */     if (bool) bool = SubWorkflowTriggerService.isAllSubWorkflowEnded(requestManager, String.valueOf(paramAutoApproveParams.getNodeid()));
/*     */     
/*     */     try {
/* 560 */       maintextToPDF(this.requestManager.getRequestid(), this.requestManager.getWorkflowid(), user1);
/* 561 */     } catch (Exception exception) {
/* 562 */       (new BaseBean()).writeLog("WFAutoApproveUtils maintextToPDF exception:", exception);
/*     */     } 
/*     */ 
/*     */     
/* 566 */     if (!paramBoolean) requestRemindBiz2.autoApproveCCRemind(paramAutoApproveParams.getNodeid(), requestManager); 
/* 567 */     if (bool) bool = requestManager.flowNextNode();
/*     */ 
/*     */     
/* 570 */     RecordSet recordSet = new RecordSet();
/*     */     
/* 572 */     if (!bool) {
/* 573 */       requestFlowLogEntity.setResult("FAILD");
/*     */       
/* 575 */       recordSet.executeUpdate("update workflow_currentoperator set isremark = '0',viewtype = '0'  where id in (" + paramAutoApproveParams.getCurrentoperatorid() + ")", new Object[0]);
/*     */ 
/*     */       
/* 578 */       recordSet.executeUpdate("delete from workflow_requestlog where requestid = ? and operator = ? and nodeid = ? and operatedate = ? and operatetime = ?", new Object[] { Integer.valueOf(requestManager.getRequestid()), Integer.valueOf(paramAutoApproveParams.getUserid()), Integer.valueOf(requestManager.getNodeid()), requestManager.getLogdate(), requestManager.getLogtime() });
/*     */       
/* 580 */       recordSet.executeUpdate("insert into workflow_approveerrorlog (requestid ,nodeid,operator,errorremark) values(?,?,?,?)", new Object[] { Integer.valueOf(requestManager.getRequestid()), Integer.valueOf(requestManager.getNodeid()), Integer.valueOf(requestManager.getUserId()), resolveMsgContent(requestManager.getMessagecontent()) });
/*     */       
/* 582 */       pushMsg(recordSet, paramAutoApproveParams);
/* 583 */       if (!paramBoolean) requestRemindBiz2.failAutoApproveRemind(requestManager); 
/* 584 */       if (!"1".equals(this.requestManager.getIscreate())) {
/*     */         
/* 586 */         RequestFlowRemindBiz requestFlowRemindBiz = new RequestFlowRemindBiz();
/*     */         
/* 588 */         ArrayList<String> arrayList = new ArrayList();
/* 589 */         arrayList.add(paramAutoApproveParams.getNodeid() + "");
/* 590 */         requestFlowRemindBiz.doRequestflowRemind(this.requestManager.getRequestid(), this.requestManager.getWorkflowid(), arrayList, user1.getUID(), this.requestManager.getCurrentDate(), this.requestManager.getCurrentTime());
/*     */       } 
/*     */     } else {
/* 593 */       requestFlowLogEntity.setResult("SUCCESS");
/*     */       
/* 595 */       if (!paramAutoApproveParams.isLeaveMarks) {
/* 596 */         int i = requestManager.getNextNodeid();
/* 597 */         String str1 = paramAutoApproveParams.getCcUserIds();
/* 598 */         String str2 = paramAutoApproveParams.getCcUserNames();
/* 599 */         boolean bool1 = recordSet.executeUpdate("update workflow_requestlog set destnodeid = ?, receivedPersons=?,receivedpersonids=? where LOGID=?", new Object[] { Integer.valueOf(i), paramAutoApproveParams.getReceiveUserNames(), paramAutoApproveParams.getReceiveUserIds(), Integer.valueOf(paramAutoApproveParams.getStartLogId()) });
/*     */         
/* 601 */         if (bool1 && !"".equals(str1)) {
/* 602 */           char c = Util.getSeparator();
/* 603 */           recordSet.executeQuery("select nodeid,showorder,annexdocids,speechAttachment,handWrittenSign,fulltextannotation,remarkLocation,speechAttachmente9,remarkquote,agenttype,agentorbyagentid,remark from workflow_requestlog where logid = ?", new Object[] { Integer.valueOf(paramAutoApproveParams.getStartLogId()) });
/* 604 */           if (recordSet.next()) {
/* 605 */             String str3 = Util.null2String(recordSet.getString("nodeid"));
/* 606 */             String str4 = Util.null2String(recordSet.getString("agentorbyagentid"));
/* 607 */             String str5 = Util.null2String(recordSet.getString("agenttype"));
/* 608 */             String str6 = Util.null2String(recordSet.getString("showorder"));
/* 609 */             String str7 = Util.null2String(recordSet.getString("annexdocids"));
/* 610 */             String str8 = Util.null2String(recordSet.getString("speechAttachment"));
/* 611 */             String str9 = Util.null2String(recordSet.getString("handWrittenSign"));
/* 612 */             String str10 = Util.null2String(recordSet.getString("remarkLocation"));
/* 613 */             String str11 = Util.null2String(recordSet.getString("signdocids"));
/* 614 */             String str12 = Util.null2String(recordSet.getString("signworkflowids"));
/* 615 */             String str13 = Util.null2String(recordSet.getString("clientType"));
/* 616 */             String str14 = Util.null2String(recordSet.getString("speechAttachmente9"));
/* 617 */             String str15 = Util.null2String(recordSet.getString("fulltextannotation"));
/* 618 */             String str16 = Util.null2String(recordSet.getString("remarkquote"));
/* 619 */             String str17 = Util.null2String(recordSet.getString("remark"));
/* 620 */             String str18 = "Request Email Approve";
/* 621 */             String str19 = "-1";
/* 622 */             if (this.request != null) {
/* 623 */               str18 = this.request.getRemoteAddr();
/* 624 */               str19 = this.request.getParameter("workflowRequestLogId");
/*     */             } 
/*     */ 
/*     */ 
/*     */             
/* 629 */             String str20 = "" + this.requestManager.getRequestid() + c + this.requestManager.getWorkflowid() + c + str3 + c + "t" + c + "" + c + "" + c + paramAutoApproveParams.getUserid() + c + str18 + c + this.requestManager.getUserType() + c + i + c + str2.trim() + c + str4 + c + str5 + c + str6 + c + str7 + c + Util.getIntValue(str19, 0) + c + str11 + c + str12 + c + str13 + c + str8 + c + str9 + c + str1 + c + str10 + c + str15 + c + str14 + c + str16;
/*     */ 
/*     */             
/* 632 */             requestManager.execRequestlog(str20, recordSet, c, "");
/*     */           } 
/*     */         } 
/*     */       } 
/*     */       
/* 637 */       if (paramAutoApproveParams.isPenetrate) {
/* 638 */         savePenetrateLog(requestManager);
/*     */       }
/*     */       
/* 641 */       updateRequestOperateLogDtl(recordSet, this.requestManager.getRequestid(), paramAutoApproveParams.getCurrentoperatorid());
/* 642 */       addApproveInfoInSession(requestManager.getRequestid(), requestManager.getNodeid(), requestManager.getUserId(), paramAutoApproveParams.getApproveTime());
/*     */       
/* 644 */       RequestOperationMsgManager requestOperationMsgManager = new RequestOperationMsgManager();
/* 645 */       (new MsgPushUtil()).pushMsg(requestOperationMsgManager.autoApproveSendMsg(requestManager, paramAutoApproveParams));
/*     */       
/* 647 */       if (!paramBoolean) requestRemindBiz1.successAutoApproveRemind(requestManager);
/*     */     
/*     */     } 
/* 650 */     RequestFlowLogBiz.SaveRequestFlowLog(requestFlowLogEntity);
/*     */   }
/*     */ 
/*     */   
/*     */   private void maintextToPDF(int paramInt1, int paramInt2, User paramUser) {
/*     */     try {
/* 656 */       RecordSet recordSet = new RecordSet();
/* 657 */       boolean bool1 = false;
/* 658 */       boolean bool2 = false;
/* 659 */       int i = 0;
/* 660 */       int j = 0;
/* 661 */       recordSet.executeQuery("select useyozo,useWps from workflow_yozotopdfconfig", new Object[0]);
/* 662 */       if (recordSet.next()) {
/* 663 */         i = recordSet.getInt("useyozo");
/* 664 */         j = recordSet.getInt("useWps");
/*     */       } 
/* 666 */       if (j == 1) {
/* 667 */         bool2 = true;
/*     */       } else {
/* 669 */         ImageConvertUtil imageConvertUtil = new ImageConvertUtil();
/* 670 */         boolean bool = imageConvertUtil.convertForClient();
/* 671 */         String str = imageConvertUtil.getConvertIp();
/* 672 */         (new BaseBean()).writeLog("======useyozo:" + i + "======openConverForClient:" + bool + "=======_clientAddress:" + str + "=====openyozo:" + bool1);
/* 673 */         if (bool && !str.isEmpty() && i == 1) {
/* 674 */           bool1 = true;
/*     */         }
/*     */       } 
/* 677 */       if (!bool1 && i == 1) {
/* 678 */         (new BaseBean()).writeLog("================请检查配置文件：doc_custom_for_weaver.properties是否正常配置启用yozo服务！");
/*     */       }
/* 680 */       if (bool1 || bool2) {
/* 681 */         int k = -1;
/*     */         
/* 683 */         MaintextToPDF maintextToPDF = new MaintextToPDF();
/* 684 */         k = maintextToPDF.textToPDF(paramUser, paramInt1, paramInt2, "1", bool1 ? (PdfConvertor)new DocImagefileToPdf() : (PdfConvertor)new DocImagefileToPdfUseWps());
/* 685 */         String str = "select docids from workflow_requestbase where requestid=?";
/* 686 */         recordSet.executeQuery(str, new Object[] { Integer.valueOf(paramInt1) });
/* 687 */         if (recordSet.next()) {
/* 688 */           String str1 = recordSet.getString("docids");
/* 689 */           if (k > 0) {
/* 690 */             str1 = str1 + "," + k;
/* 691 */             String str2 = "update workflow_requestbase set docids=? where requestid=?";
/* 692 */             recordSet.executeUpdate(str2, new Object[] { str1, Integer.valueOf(paramInt1) });
/*     */           } 
/*     */         } 
/*     */       } 
/* 696 */     } catch (Exception exception) {
/* 697 */       (new BaseBean()).writeLog("WFAutoApproveUtils maintextToPDF exception:", exception);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void fixPenetrateSign(AutoApproveParams paramAutoApproveParams1, AutoApproveParams paramAutoApproveParams2) {}
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void pushMsg(RecordSet paramRecordSet, AutoApproveParams paramAutoApproveParams) {
/* 714 */     String str1 = "select max(id) from workflow_requestoperatelog where requestid = ? and operatorid = ? and (isinvalid is null  or isinvalid <> 1) and operatecode>0 ";
/* 715 */     String str2 = "";
/* 716 */     paramRecordSet.executeQuery(str1, new Object[] { Integer.valueOf(this.requestManager.getRequestid()), Integer.valueOf(this.requestManager.getUserId()) });
/* 717 */     if (paramRecordSet.next()) {
/* 718 */       str2 = Util.null2String(paramRecordSet.getString(1));
/*     */     }
/* 720 */     RequestOperationMsgManager requestOperationMsgManager = new RequestOperationMsgManager(paramAutoApproveParams.getNodeid(), Util.getIntValue(paramAutoApproveParams.getNodetype()));
/* 721 */     List<MsgEntity> list = requestOperationMsgManager.getOperateMsg(str2, MsgOperateType.OTHER);
/* 722 */     (new MsgPushUtil()).pushMsg(list);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static void processApproveLog(RecordSetTrans paramRecordSetTrans, RequestManager paramRequestManager) {
/*     */     try {
/* 732 */       boolean bool1 = false;
/* 733 */       if (paramRequestManager.isHasEflowToAssignNode()) {
/* 734 */         paramRecordSetTrans.executeQuery("select nodeid from workflow_currentoperator where requestid = ? and nodeid  = ? and isremark  = '2' ", new Object[] { Integer.valueOf(paramRequestManager.getRequestid()), Integer.valueOf(paramRequestManager.getNextNodeid()) });
/* 735 */         if (paramRecordSetTrans.next()) {
/* 736 */           bool1 = true;
/*     */         }
/*     */       } 
/*     */ 
/*     */       
/* 741 */       boolean bool2 = false;
/* 742 */       if (paramRequestManager.getIsremark() == 0) {
/* 743 */         paramRecordSetTrans.executeQuery("select 1 from workflow_currentoperator where requestid  = ? and nodeid = ? and isremark = ? and userid  = ?", new Object[] { Integer.valueOf(paramRequestManager.getRequestid()), Integer.valueOf(paramRequestManager.getNodeid()), "6", Integer.valueOf(paramRequestManager.getUserId()) });
/* 744 */         if (paramRecordSetTrans.next()) {
/* 745 */           bool2 = true;
/*     */         }
/*     */       } 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 752 */       if ("submit".equals(paramRequestManager.getSrc()) && !bool1 && !bool2) {
/* 753 */         Calendar calendar = Calendar.getInstance();
/*     */ 
/*     */         
/* 756 */         String str1 = Util.add0(calendar.get(1), 4) + "-" + Util.add0(calendar.get(2) + 1, 2) + "-" + Util.add0(calendar.get(5), 2);
/*     */ 
/*     */         
/* 759 */         String str2 = Util.add0(calendar.get(11), 2) + ":" + Util.add0(calendar.get(12), 2) + ":" + Util.add0(calendar.get(13), 2);
/* 760 */         String str3 = paramRequestManager.getHandWrittenSign();
/* 761 */         paramRecordSetTrans.executeUpdate("insert into workflow_approvelog (requestid,nodeid,remark,operator,logdate,logtime,handwrittensign,usertype) values(?,?,?,?,?,?,?,?)", new Object[] { Integer.valueOf(paramRequestManager.getRequestid()), Integer.valueOf(paramRequestManager.getNodeid()), paramRequestManager.getRemark(), Integer.valueOf(paramRequestManager.getUserId()), str1, str2, str3, Integer.valueOf(paramRequestManager.getUserType()) });
/* 762 */         paramRequestManager.getHandWrittenSign();
/*     */       } 
/* 764 */       if ("intervenor".equals(paramRequestManager.getSrc()) || "reject".equals(paramRequestManager.getSrc())) {
/* 765 */         AutoApproveEntity autoApproveEntity = new AutoApproveEntity();
/* 766 */         autoApproveEntity.setRst(paramRecordSetTrans);
/* 767 */         autoApproveEntity.setRequestid(paramRequestManager.getRequestid());
/* 768 */         autoApproveEntity.setSrc(paramRequestManager.getSrc());
/* 769 */         RequestAutoApproveBiz.delApproveLog(autoApproveEntity, paramRequestManager.getUser());
/*     */       } 
/* 771 */     } catch (Exception exception) {
/* 772 */       paramRequestManager.writeLog(exception);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static void updateRequestOperateLogDtl(RecordSet paramRecordSet, int paramInt, String paramString) {
/* 784 */     String str = " select optlogid from workflow_requestoperatelog_dtl where requestid  = ? and entitytype  = 1 and entityid  = ? and ismodify = 1";
/* 785 */     paramRecordSet.executeQuery(str, new Object[] { Integer.valueOf(paramInt), paramString });
/* 786 */     if (paramRecordSet.next()) {
/* 787 */       String str1 = paramRecordSet.getString("optlogid");
/* 788 */       String str2 = "insert into workflow_requestoperatelog_dtl(requestid, optlogid, entitytype, entityid, ismodify, fieldname, ovalue, nvalue) values (?,?,?,?,?,?,?,?)";
/* 789 */       paramRecordSet.executeUpdate(str2, new Object[] { Integer.valueOf(paramInt), str1, "1", paramString, "1", "isremark", "0", "2" });
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   public static String resolveMsgContent(String paramString) {
/* 795 */     paramString = paramString.replaceAll("<span.*?>", "");
/* 796 */     paramString = paramString.replaceAll("<.*?span>", "");
/* 797 */     paramString = paramString.replaceAll("<br/>", "");
/*     */     
/*     */     try {
/* 800 */       JSONObject jSONObject = new JSONObject(paramString);
/* 801 */       if (jSONObject.has("details")) {
/* 802 */         return Util.null2String(jSONObject.getString("details"));
/*     */       }
/* 804 */     } catch (JSONException jSONException) {}
/*     */ 
/*     */     
/* 807 */     return paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   private static void addApproveInfoInSession(int paramInt1, int paramInt2, int paramInt3, String paramString) {
/*     */     try {
/* 814 */       String str = TimeUtil.getCurrentTimeString();
/* 815 */       Util_TableMap.setVal("wfAutoApprove_" + paramInt1 + "_" + paramInt2 + "_" + paramInt3, str);
/* 816 */     } catch (Exception exception) {}
/*     */   }
/*     */ 
/*     */   
/*     */   public static void removeAprroveSessionInfo(int paramInt1, int paramInt2, int paramInt3) {
/* 821 */     Util_TableMap.clearVal("wfAutoApprove_" + paramInt1 + "_" + paramInt2 + "_" + paramInt3);
/*     */   }
/*     */   
/*     */   private boolean getIsAutoApprove(int paramInt) {
/* 825 */     RecordSet recordSet = new RecordSet();
/* 826 */     String str = "0";
/* 827 */     recordSet.executeQuery("select isAutoApprove,isAutoCommit,isAutoRemark,isOnlyOneAutoApprove from workflow_base where id = ?", new Object[] { Integer.valueOf(paramInt) });
/* 828 */     if (recordSet.next()) {
/* 829 */       str = Util.null2s(recordSet.getString("isAutoApprove"), "0");
/*     */     }
/* 831 */     if ("1".equals(str) || "2".equals(str)) {
/* 832 */       return true;
/*     */     }
/* 834 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean autoFlowRequestlogTrail(int paramInt) {
/* 845 */     RecordSet recordSet = new RecordSet();
/* 846 */     String str1 = "";
/* 847 */     String str2 = "";
/* 848 */     String str3 = "";
/* 849 */     String str4 = "select autoFlowRequestlogTrail from workflow_base where id=?";
/* 850 */     recordSet.executeQuery(str4, new Object[] { Integer.valueOf(paramInt) });
/* 851 */     if (recordSet.next()) {
/* 852 */       str3 = Util.null2String(recordSet.getString("autoFlowRequestlogTrail"));
/*     */     }
/* 854 */     if ("1".equals(str3)) {
/* 855 */       return true;
/*     */     }
/* 857 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean penetrateRequestlogTrail() {
/* 865 */     RecordSet recordSet = new RecordSet();
/* 866 */     String str1 = "";
/* 867 */     String str2 = " select autoflowlogtrail from SystemSet";
/* 868 */     recordSet.executeQuery(str2, new Object[0]);
/* 869 */     if (recordSet.next()) {
/* 870 */       str1 = Util.null2String(recordSet.getString("autoflowlogtrail"));
/*     */     }
/* 872 */     if ("1".equals(str1)) {
/* 873 */       return true;
/*     */     }
/* 875 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void savePenetrateLog(RequestManager paramRequestManager) {
/* 883 */     int i = paramRequestManager.getRequestid();
/* 884 */     int j = paramRequestManager.getWorkflowid();
/* 885 */     int k = paramRequestManager.getNodeid();
/* 886 */     int m = paramRequestManager.getUserId();
/* 887 */     int n = paramRequestManager.getUserType();
/* 888 */     int i1 = paramRequestManager.getNextNodeid();
/* 889 */     int i2 = this.flowNextLinkId;
/*     */     
/* 891 */     RecordSet recordSet = new RecordSet();
/* 892 */     String str = "insert into workflow_penetrateLog(requestid,workflowid,nodeid,operator,operatortype,destnodeid,logtype, linkid)values ('" + i + "','" + j + "','" + k + "','" + m + "','" + n + "','" + i1 + "','2', " + i2 + ")";
/*     */     
/* 894 */     if ("oracle".equals(recordSet.getDBType())) {
/* 895 */       str = "insert into workflow_penetrateLog(id,requestid,workflowid,nodeid,operator,operatortype,destnodeid,logtype, linkid)values (workflowpenetratelog_Id.nextval,'" + i + "','" + j + "','" + k + "','" + m + "','" + n + "','" + i1 + "','2', " + i2 + ")";
/*     */     }
/*     */     
/* 898 */     recordSet.executeSql(str);
/*     */   }
/*     */   
/*     */   public WeaverRequest getRequest() {
/* 902 */     return this.request;
/*     */   }
/*     */   
/*     */   public void setRequest(WeaverRequest paramWeaverRequest) {
/* 906 */     this.request = paramWeaverRequest;
/*     */   }
/*     */   
/*     */   public int getFlowNextLinkId() {
/* 910 */     return this.flowNextLinkId;
/*     */   }
/*     */   
/*     */   public void setFlowNextLinkId(int paramInt) {
/* 914 */     this.flowNextLinkId = paramInt;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/WFAutoApproveUtils.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */