/*    */ package weaver.workflow.request;
/*    */ 
/*    */ import java.io.IOException;
/*    */ import javax.servlet.ServletException;
/*    */ import javax.servlet.ServletOutputStream;
/*    */ import javax.servlet.http.HttpServlet;
/*    */ import javax.servlet.http.HttpServletRequest;
/*    */ import javax.servlet.http.HttpServletResponse;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.resource.ResourceComInfo;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class RequestList
/*    */   extends HttpServlet
/*    */ {
/* 27 */   private ResourceComInfo resourceComInfo = null;
/*    */   
/*    */   public void init() throws ServletException {
/* 30 */     super.init();
/*    */     try {
/* 32 */       this.resourceComInfo = new ResourceComInfo();
/* 33 */     } catch (Exception exception) {
/* 34 */       throw new ServletException(exception.toString());
/*    */     } 
/*    */   }
/*    */   
/*    */   public void doGet(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws ServletException, IOException {
/* 39 */     doPost(paramHttpServletRequest, paramHttpServletResponse);
/*    */   }
/*    */   
/*    */   public void doPost(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws ServletException, IOException {
/* 43 */     paramHttpServletResponse.setContentType("text/html;charset=UTF-8");
/*    */     
/* 45 */     RecordSet recordSet = new RecordSet();
/* 46 */     int i = -1;
/* 47 */     String str1 = Util.null2String(paramHttpServletRequest.getParameter("username"));
/* 48 */     recordSet.executeSql("SELECT id FROM HrmResource where loginid='" + str1 + "'");
/* 49 */     if (recordSet.next()) {
/* 50 */       i = recordSet.getInt("id");
/*    */     }
/* 52 */     String str2 = Util.null2String(paramHttpServletRequest.getParameter("logintype"));
/* 53 */     boolean bool = false;
/* 54 */     if (str2.equals("1")) bool = false; 
/* 55 */     if (str2.equals("2")) bool = true;
/*    */     
/* 57 */     int j = Util.getIntValue(paramHttpServletRequest.getParameter("workflowid"), -1);
/*    */     
/* 59 */     ServletOutputStream servletOutputStream = paramHttpServletResponse.getOutputStream();
/*    */     
/* 61 */     StringBuffer stringBuffer = new StringBuffer();
/* 62 */     stringBuffer.append("issuccess=1&errormsg=");
/*    */     
/* 64 */     String str3 = "select t2.requestid,t2.createdate,t2.creater,t2.requestlevel,t2.requestname,t2.status from workflow_currentoperator t1,workflow_requestbase t2 where t1.isremark in( '0','1') and t1.userid=" + i + " and t1.usertype=" + bool + " and t1.requestid=t2.requestid and ( t2.deleted=0 or t2.deleted is null ) and t2.currentnodetype<>'3' and t1.workflowid = " + j;
/*    */     
/* 66 */     recordSet.executeSql(str3);
/* 67 */     byte b = 0;
/* 68 */     while (recordSet.next()) {
/* 69 */       stringBuffer.append("&requestid_" + b + "=" + recordSet.getString("requestid"));
/* 70 */       stringBuffer.append("&createdate_" + b + "=" + recordSet.getString("createdate"));
/* 71 */       stringBuffer.append("&creater_" + b + "=" + this.resourceComInfo.getLastname(recordSet.getString("creater")));
/* 72 */       stringBuffer.append("&requestlevel_" + b + "=" + recordSet.getString("requestlevel"));
/* 73 */       stringBuffer.append("&requestname_" + b + "=" + recordSet.getString("requestname"));
/* 74 */       stringBuffer.append("&status_" + b + "=" + recordSet.getString("status"));
/* 75 */       b++;
/*    */     } 
/*    */     
/* 78 */     stringBuffer.append("&nodenum=" + b);
/*    */     
/* 80 */     servletOutputStream.print(stringBuffer.toString());
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/RequestList.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */