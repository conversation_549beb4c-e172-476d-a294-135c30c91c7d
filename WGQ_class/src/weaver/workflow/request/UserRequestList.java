/*    */ package weaver.workflow.request;
/*    */ 
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.BaseBean;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class UserRequestList
/*    */   extends BaseBean
/*    */ {
/*    */   private RecordSet statement;
/*    */   
/*    */   public void reset() {
/* 27 */     this.statement.beforFirst();
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public int getUserid() throws Exception {
/* 34 */     return this.statement.getInt("userid");
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public int getRequestid() throws Exception {
/* 41 */     return this.statement.getInt("requestid");
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public int getWfid() throws Exception {
/* 48 */     return this.statement.getInt("workflowid");
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public int getFormid() throws Exception {
/* 55 */     return this.statement.getInt("formid");
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public int getNodeid() throws Exception {
/* 62 */     return this.statement.getInt("nodeid");
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getNodetype() throws Exception {
/* 69 */     return this.statement.getString("nodetype");
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public void selectAllRequest() throws Exception {
/* 75 */     this.statement = new RecordSet();
/*    */     try {
/* 77 */       String str = "";
/* 78 */       str = getPropValue("weaversql", "all_request");
/*    */       
/* 80 */       this.statement.executeSql(str);
/*    */     }
/* 82 */     catch (Exception exception) {
/* 83 */       writeLog(exception);
/* 84 */       throw exception;
/*    */     } 
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public boolean next() throws Exception {
/* 93 */     return this.statement.next();
/*    */   }
/*    */   
/*    */   public void closeStatement() {}
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/UserRequestList.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */