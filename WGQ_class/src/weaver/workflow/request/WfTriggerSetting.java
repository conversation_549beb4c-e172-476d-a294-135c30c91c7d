/*     */ package weaver.workflow.request;
/*     */ 
/*     */ import java.util.Map;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class WfTriggerSetting
/*     */ {
/*     */   public static final String TRIGGER_SOURCE_MAIN = "main";
/*     */   public static final String TRIGGER_SOURCE_DETAIL = "detail";
/*     */   public static final String TRIGGER_TYPE_AUTO = "1";
/*     */   public static final String TRIGGER_TYPE_MANUAL = "2";
/*     */   public static final String TRIGGER_TIME_ARRIVE = "1";
/*     */   public static final String TRIGGER_TIME_LEAVE = "2";
/*     */   public static final String TRIGGER_OPERATION_APPROVE_PREVNODE = "1";
/*     */   public static final String TRIGGER_OPERATION_REJECT_PREVNODE = "2";
/*     */   public static final String TRIGGER_OPERATION_APPROVE = "3";
/*     */   public static final String TRIGGER_OPERATION_REJECT = "4";
/*     */   private String mainWorkflowId;
/*     */   private String settingId;
/*     */   private String triggerNodeId;
/*     */   private String triggerTime;
/*     */   private String triggerOpreation;
/*     */   private String triggerType;
/*     */   private String triggerSource;
/*     */   private String triggerSourceType;
/*     */   private String triggerSourceOrder;
/*     */   private String triggerCondition;
/*     */   private Map mainMainFieldValues;
/*     */   private int isSplitDetail;
/*     */   private int row;
/*     */   
/*     */   public String getMainWorkflowId() {
/*  49 */     return this.mainWorkflowId;
/*     */   }
/*     */   
/*     */   public void setMainWorkflowId(String paramString) {
/*  53 */     this.mainWorkflowId = paramString;
/*     */   }
/*     */   
/*     */   public Map getTriggerSourceDataRow() {
/*  57 */     return this.mainMainFieldValues;
/*     */   }
/*     */   public void setTriggerSourceDataRow(Map paramMap) {
/*  60 */     this.mainMainFieldValues = paramMap;
/*     */   }
/*     */   
/*     */   public String getSettingId() {
/*  64 */     return this.settingId;
/*     */   }
/*     */   
/*     */   public void setSettingId(String paramString) {
/*  68 */     this.settingId = paramString;
/*     */   }
/*     */   
/*     */   public String getTriggerNodeId() {
/*  72 */     return this.triggerNodeId;
/*     */   }
/*     */   
/*     */   public void setTriggerNodeId(String paramString) {
/*  76 */     this.triggerNodeId = paramString;
/*     */   }
/*     */   
/*     */   public String getTriggerTime() {
/*  80 */     return this.triggerTime;
/*     */   }
/*     */   
/*     */   public void setTriggerTime(String paramString) {
/*  84 */     this.triggerTime = paramString;
/*     */   }
/*     */   
/*     */   public String getTriggerOpreation() {
/*  88 */     return this.triggerOpreation;
/*     */   }
/*     */   
/*     */   public void setTriggerOpreation(String paramString) {
/*  92 */     this.triggerOpreation = paramString;
/*     */   }
/*     */   
/*     */   public String getTriggerType() {
/*  96 */     return this.triggerType;
/*     */   }
/*     */   
/*     */   public void setTriggerType(String paramString) {
/* 100 */     this.triggerType = paramString;
/*     */   }
/*     */   
/*     */   public String getTriggerSource() {
/* 104 */     return this.triggerSource;
/*     */   }
/*     */   
/*     */   public void setTriggerSource(String paramString) {
/* 108 */     this.triggerSource = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public String getTriggerSourceType() {
/* 114 */     if (this.triggerSourceType == null || this.triggerSourceType.trim().equals("") || (
/*     */       
/* 116 */       !this.triggerSourceType.trim().equals("main") && 
/* 117 */       !this.triggerSourceType.trim().equals("detail")))
/*     */     {
/*     */       
/* 120 */       return "main";
/*     */     }
/* 122 */     return this.triggerSourceType;
/*     */   }
/*     */   
/*     */   public void setTriggerSourceType(String paramString) {
/* 126 */     this.triggerSourceType = paramString;
/*     */   }
/*     */   
/*     */   public String getTriggerSourceOrder() {
/* 130 */     return this.triggerSourceOrder;
/*     */   }
/*     */   
/*     */   public void setTriggerSourceOrder(String paramString) {
/* 134 */     this.triggerSourceOrder = paramString;
/*     */   }
/*     */   
/*     */   public String getTriggerCondition() {
/* 138 */     return this.triggerCondition;
/*     */   }
/*     */   
/*     */   public void setTriggerCondition(String paramString) {
/* 142 */     this.triggerCondition = paramString;
/*     */   }
/*     */   
/*     */   public int getRow() {
/* 146 */     return this.row;
/*     */   }
/*     */   
/*     */   public void setRow(int paramInt) {
/* 150 */     this.row = paramInt;
/*     */   }
/*     */   
/*     */   public int getIsSplitDetail() {
/* 154 */     return this.isSplitDetail;
/*     */   }
/*     */   
/*     */   public void setIsSplitDetail(int paramInt) {
/* 158 */     this.isSplitDetail = paramInt;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/WfTriggerSetting.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */