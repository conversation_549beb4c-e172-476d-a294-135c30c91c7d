/*      */ package weaver.workflow.request;
/*      */ import com.alibaba.fastjson.JSONArray;
/*      */ import com.alibaba.fastjson.JSONObject;
/*      */ import com.cloudstore.dev.api.bean.MessageBean;
/*      */ import com.cloudstore.dev.api.bean.MessageType;
/*      */ import com.cloudstore.dev.api.util.Util_Message;
/*      */ import com.engine.msgcenter.biz.ConfigManager;
/*      */ import com.engine.msgcenter.biz.WeaMessageTypeConfig;
/*      */ import com.engine.workflow.biz.freeNode.FreeNodeBiz;
/*      */ import com.engine.workflow.biz.requestList.RequestAttentionBiz;
/*      */ import com.engine.workflow.biz.workflowCore.RequestSubTableBiz;
/*      */ import com.engine.workflow.biz.workflowCore.WorkflowBaseBiz;
/*      */ import com.engine.workflow.entity.core.NodeInfoEntity;
/*      */ import com.engine.workflow.entity.requestForm.RequestOperationResultBean;
/*      */ import com.google.common.base.Strings;
/*      */ import com.google.common.collect.Sets;
/*      */ import java.text.SimpleDateFormat;
/*      */ import java.util.ArrayList;
/*      */ import java.util.Date;
/*      */ import java.util.HashMap;
/*      */ import java.util.HashSet;
/*      */ import java.util.List;
/*      */ import java.util.Map;
/*      */ import java.util.Queue;
/*      */ import java.util.Set;
/*      */ import org.apache.commons.lang.StringUtils;
/*      */ import org.apache.commons.lang3.StringUtils;
/*      */ import weaver.conn.RecordSet;
/*      */ import weaver.general.BaseBean;
/*      */ import weaver.general.LabelUtil;
/*      */ import weaver.general.ThreadVarLanguage;
/*      */ import weaver.general.Util;
/*      */ import weaver.hrm.User;
/*      */ import weaver.hrm.resource.ResourceComInfo;
/*      */ import weaver.systeminfo.SystemEnv;
/*      */ import weaver.workflow.logging.Logger;
/*      */ import weaver.workflow.logging.LoggerFactory;
/*      */ import weaver.workflow.msg.MsgPushUtil;
/*      */ import weaver.workflow.msg.entity.MsgEntity;
/*      */ import weaver.workflow.msg.entity.MsgNoticeType;
/*      */ import weaver.workflow.msg.entity.MsgOperateType;
/*      */ import weaver.workflow.request.entity.RequestOperateEntityTableNameEnum;
/*      */ import weaver.workflow.request.entity.RequestOperateTypeEnum;
/*      */ 
/*      */ public class RequestOperationMsgManager extends BaseBean {
/*   46 */   private static final Logger log = LoggerFactory.getLogger(RequestOperationMsgManager.class);
/*      */ 
/*      */ 
/*      */   
/*      */   private int curNodeId;
/*      */ 
/*      */ 
/*      */   
/*      */   private int lastNodetype;
/*      */ 
/*      */ 
/*      */   
/*      */   private boolean doAutoApprove = false;
/*      */ 
/*      */   
/*      */   private boolean nextNodeisAutoApprove = false;
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean isDoAutoApprove() {
/*   66 */     return this.doAutoApprove;
/*      */   }
/*      */   
/*      */   public void setDoAutoApprove(boolean paramBoolean) {
/*   70 */     this.doAutoApprove = paramBoolean;
/*      */   }
/*      */ 
/*      */   
/*      */   public RequestOperationMsgManager() {}
/*      */ 
/*      */   
/*      */   public RequestOperationMsgManager(int paramInt1, int paramInt2) {
/*   78 */     this.curNodeId = paramInt1;
/*   79 */     this.lastNodetype = paramInt2;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public List<MsgEntity> getOperateMsg(String paramString, MsgOperateType paramMsgOperateType) {
/*   92 */     if (this.doAutoApprove) return null;
/*      */     
/*   94 */     RecordSet recordSet = new RecordSet();
/*      */     
/*   96 */     List<MsgEntity> list = new ArrayList();
/*      */ 
/*      */ 
/*      */     
/*      */     try {
/*  101 */       recordSet.executeQuery("select requestid,operatorid,nodeid,operateDate,operateTime,operateType,detailinfo from  workflow_requestoperatelog where id = ?", new Object[] { paramString });
/*      */       
/*  103 */       writeLog("0、push ec optId:" + paramString);
/*  104 */       if (recordSet.next()) {
/*      */         
/*  106 */         String str1 = Util.null2String(recordSet.getString("requestid"));
/*  107 */         String str2 = Util.null2String(recordSet.getString("operatorid"));
/*  108 */         String str3 = Util.null2String(recordSet.getString("operateType"));
/*  109 */         String str4 = Util.null2String(recordSet.getString("detailInfo"));
/*  110 */         String str5 = Util.null2String(recordSet.getString("nodeid"));
/*      */         
/*  112 */         if ("supervise".equals(str3.toLowerCase())) {
/*  113 */           return getSuperviseMsg(str1, str2);
/*      */         }
/*  115 */         JSONObject jSONObject1 = JSONObject.parseObject(str4);
/*  116 */         if (jSONObject1 == null) return null;
/*      */         
/*  118 */         JSONObject jSONObject2 = jSONObject1.getJSONObject(RequestOperateEntityTableNameEnum.CURRENTOPERATOR.getTableName());
/*  119 */         if (jSONObject2 == null) return null;
/*      */         
/*  121 */         String str6 = jSONObject2.getString("newIds");
/*  122 */         String str7 = jSONObject2.getString("beAgentNewids");
/*  123 */         MsgEntity msgEntity = getBaseInfo(str1);
/*  124 */         if (msgEntity == null) return null;
/*      */         
/*  126 */         if (paramMsgOperateType.getId() == MsgOperateType.DRAW_BACK.getId()) {
/*      */           
/*  128 */           JSONArray jSONArray = jSONObject2.getJSONArray("modifyData");
/*      */ 
/*      */ 
/*      */ 
/*      */           
/*  133 */           if (!"forward".equals(str3) && !"intervenor".equals(str3) && !"forceover".equals(str3)) {
/*  134 */             MsgEntity msgEntity1 = getMsgEntity(msgEntity, jSONArray, MessageType.WF_NEW_ARRIVAL);
/*  135 */             list.add(msgEntity1);
/*      */           } 
/*      */ 
/*      */           
/*  139 */           if ("intervenor".equals(str3) || "forceover".equals(str3)) {
/*  140 */             list.add(getMsgEntity(msgEntity, jSONArray, MessageType.WF_NEW_ARRIVAL));
/*      */           }
/*      */           
/*  143 */           delMsgOnDrawBACK(recordSet, str6, str1, str3);
/*      */         
/*      */         }
/*      */         else {
/*      */           
/*  148 */           list.addAll(getAttentionMsg(msgEntity.clone(), str1, str2));
/*      */           
/*  150 */           if (RequestOperateTypeEnum.TAKEREPLY.getId().equals(str3)) {
/*  151 */             return getRemarkReplyMsg(str1, str2, paramString);
/*      */           }
/*  153 */           if (RequestOperateTypeEnum.FORWARDREPLY.getId().equals(str3)) {
/*  154 */             return getForwardReplyMsg(str1, str2);
/*      */           }
/*  156 */           if (RequestOperateTypeEnum.TAKEEND.getId().equals(str3)) return null;
/*      */ 
/*      */           
/*  159 */           boolean bool = ("forward".equals(str3) || "forceover".equals(str3) || "intervenor".equals(str3) || "chuanyue".equals(str3) || "withdraw".equals(str3)) ? true : false;
/*  160 */           if (!bool) {
/*      */             
/*  162 */             MsgEntity msgEntity1 = msgEntity.clone();
/*  163 */             msgEntity1.setOperatorId(str2);
/*  164 */             msgEntity1.setMsgType(MessageType.WF_COMPLETED);
/*  165 */             recordSet.executeQuery("select userid, id from workflow_currentoperator where requestid = ? and userid = ? and usertype = 0 and isremark in(2,4) and islasttimes = 1", new Object[] { str1, str2 });
/*      */             
/*  167 */             addOperatorId(recordSet, msgEntity1);
/*      */             
/*  169 */             if ("submit".equals(str3) || "reject".equals(str3)) groupDetialUserIds(msgEntity1, jSONObject2, str2); 
/*  170 */             list.add(msgEntity1);
/*      */           } 
/*      */ 
/*      */           
/*  174 */           if ("intervenor".equals(str3) || "withdraw".equals(str3)) {
/*  175 */             JSONArray jSONArray = jSONObject2.getJSONArray("modifyData");
/*  176 */             list.add(getMsgEntity(msgEntity, jSONArray, MessageType.WF_COMPLETED));
/*      */           } 
/*      */           
/*  179 */           boolean bool1 = isArchived(recordSet, str1);
/*  180 */           if (("forceover".equals(str3) || "submit".equals(str3) || "intervenor".equals(str3)) && this.lastNodetype != 3 && bool1) {
/*      */ 
/*      */ 
/*      */             
/*  184 */             list = archivedMsg(list, msgEntity, str1, jSONObject2, recordSet, str3);
/*      */           }
/*  186 */           else if (!Strings.isNullOrEmpty(str6) && !this.nextNodeisAutoApprove) {
/*      */             
/*  188 */             MsgEntity msgEntity1 = beAgentAndOtherMsg(recordSet, msgEntity, str7, str2, str5);
/*  189 */             if (msgEntity1 != null) list.add(msgEntity1);
/*      */ 
/*      */             
/*  192 */             recordSet.executeQuery("select userid,id,receivedate,receivetime,isremark from workflow_currentoperator where isremark <> 2 and usertype = 0 and " + Util.getSubINClause(str6, "id", "in"), new Object[0]);
/*      */             
/*  194 */             recordSet.next();
/*  195 */             String str8 = Util.null2String(recordSet.getString("receivedate"));
/*  196 */             String str9 = Util.null2String(recordSet.getString("receivetime"));
/*  197 */             msgEntity.setReceiveDate(String.format("%s %s", new Object[] { str8, str9 }));
/*  198 */             msgEntity.setOperatorId(str2);
/*  199 */             recordSet.beforFirst();
/*      */ 
/*      */             
/*  202 */             msgEntity.setMsgType(MessageType.WF_NEW_ARRIVAL);
/*  203 */             msgEntity.setNoticeType(MsgNoticeType.NEW_ARRIVAL);
/*      */             
/*  205 */             if ("reject".equals(str3)) {
/*  206 */               msgEntity.setMsgType(MessageType.WF_RETURN);
/*  207 */               msgEntity.setNoticeType(MsgNoticeType.RETURN);
/*      */             } 
/*  209 */             if ("forward".equals(str3)) {
/*  210 */               msgEntity.setMsgType(MessageType.WF_FORWARD);
/*  211 */               msgEntity.setNoticeType(MsgNoticeType.FORWARD);
/*      */             } 
/*  213 */             if ("take".equals(str3)) {
/*  214 */               msgEntity.setMsgType(MessageType.WF_INQUIRY);
/*  215 */               msgEntity.setNoticeType(MsgNoticeType.INQUIRY);
/*      */             } 
/*  217 */             if ("chuanyue".equals(str3)) {
/*  218 */               msgEntity.setMsgType(MessageType.WF_CIRCULATED);
/*  219 */               msgEntity.setNoticeType(MsgNoticeType.CIRCULATED);
/*      */             } 
/*      */             
/*  222 */             ArrayList<String> arrayList = new ArrayList();
/*  223 */             HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  224 */             HashMap<Object, Object> hashMap2 = new HashMap<>();
/*  225 */             while (recordSet.next()) {
/*  226 */               String str10 = Util.null2String(recordSet.getString("userid"));
/*  227 */               String str11 = Util.null2String(recordSet.getString("isremark"));
/*  228 */               String str12 = Util.null2String(recordSet.getString("id"));
/*      */               
/*  230 */               if ("9".equals(str11) || "8".equals(str11)) {
/*  231 */                 arrayList.add(str10);
/*  232 */                 List<String> list2 = (List)hashMap2.get(str10);
/*  233 */                 if (list2 == null) {
/*  234 */                   list2 = new ArrayList();
/*  235 */                   list2.add(str12);
/*  236 */                   hashMap2.put(str10, list2); continue;
/*      */                 } 
/*  238 */                 list2.add(str12);
/*      */                 continue;
/*      */               } 
/*  241 */               msgEntity.addUserId(str10);
/*  242 */               List<String> list1 = (List)hashMap1.get(str10);
/*  243 */               if (list1 == null) {
/*  244 */                 list1 = new ArrayList();
/*  245 */                 list1.add(str12);
/*  246 */                 hashMap1.put(str10, list1); continue;
/*      */               } 
/*  248 */               list1.add(str12);
/*      */             } 
/*      */ 
/*      */             
/*  252 */             for (String str : hashMap1.keySet()) {
/*  253 */               msgEntity.addCurrentOperatorId(str, String.join(",", (Iterable<? extends CharSequence>)hashMap1.get(str)));
/*      */             }
/*      */             
/*  256 */             if (!arrayList.isEmpty()) {
/*  257 */               MsgEntity msgEntity2 = msgEntity.clone();
/*  258 */               msgEntity2.setMsgType(MessageType.WF_COPY);
/*  259 */               msgEntity2.setNoticeType(MsgNoticeType.CC);
/*  260 */               msgEntity2.addAllUserId(arrayList);
/*  261 */               for (String str : hashMap2.keySet()) {
/*  262 */                 msgEntity2.addCurrentOperatorId(str, String.join(",", (Iterable<? extends CharSequence>)hashMap2.get(str)));
/*      */               }
/*  264 */               list.add(msgEntity2);
/*      */             } 
/*  266 */             list.add(msgEntity);
/*      */           } 
/*      */         } 
/*      */         
/*  270 */         newListDoingSubTableHandle(jSONObject2);
/*      */       } 
/*  272 */     } catch (Exception exception) {
/*  273 */       writeLog(exception);
/*      */     } 
/*  275 */     return list;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private void delMsgOnDrawBACK(RecordSet paramRecordSet, String paramString1, String paramString2, String paramString3) throws Exception {
/*  289 */     if (!Strings.isNullOrEmpty(paramString1)) {
/*      */       
/*  291 */       MessageBean messageBean1 = new MessageBean();
/*  292 */       messageBean1.setTargetId(paramString2);
/*  293 */       MessageBean messageBean2 = new MessageBean();
/*  294 */       messageBean2.setTargetId(paramString2);
/*      */       
/*  296 */       paramRecordSet.executeQuery("select userid,id,nodeId from workflow_currentoperator where usertype = 0 and " + Util.getSubINClause(paramString1, "id", "in"), new Object[0]);
/*  297 */       while (paramRecordSet.next()) {
/*  298 */         String str1 = Util.null2String(paramRecordSet.getString("userid"));
/*  299 */         messageBean1.getUserList().add(str1);
/*      */       } 
/*  301 */       switch (paramString3) {
/*      */         case "submit":
/*  303 */           messageBean1.setMessageType(MessageType.WF_NEW_ARRIVAL);
/*  304 */           messageBean2.setMessageType(MessageType.WF_COPY);
/*  305 */           messageBean2.setUserList(messageBean1.getUserList());
/*      */           break;
/*      */         case "take":
/*  308 */           messageBean1.setMessageType(MessageType.WF_INQUIRY);
/*      */           break;
/*      */         case "forward":
/*  311 */           messageBean1.setMessageType(MessageType.WF_FORWARD);
/*      */           break;
/*      */       } 
/*      */       
/*  315 */       if (messageBean1.getUserList() != null && !messageBean1.getUserList().isEmpty()) {
/*  316 */         Util_Message.delMessageTargetid(messageBean1);
/*      */       }
/*  318 */       if (messageBean2.getUserList() != null && !messageBean2.getUserList().isEmpty()) {
/*  319 */         Util_Message.delMessageTargetid(messageBean2);
/*      */       }
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*  325 */       ArrayList<MsgEntity> arrayList = new ArrayList();
/*  326 */       HashSet<String> hashSet1 = new HashSet();
/*  327 */       paramRecordSet.executeQuery("select userid from workflow_currentoperator where requestid =  ? and usertype = 0 and " + Util.getSubINClause(paramString1, "id", "in"), new Object[] { paramString2 });
/*  328 */       while (paramRecordSet.next()) {
/*  329 */         hashSet1.add(paramRecordSet.getString(1));
/*      */       }
/*  331 */       String str = String.join(",", (Iterable)hashSet1);
/*      */       
/*  333 */       HashSet<String> hashSet2 = new HashSet();
/*  334 */       paramRecordSet.executeQuery("select userid from workflow_currentoperator where requestid =  ? and usertype = 0 and " + Util.getSubINClause(paramString1, "id", "not in") + " and " + Util.getSubINClause(str, "userid", "in"), new Object[] { paramString2 });
/*  335 */       while (paramRecordSet.next()) {
/*  336 */         hashSet2.add(paramRecordSet.getString(1));
/*      */       }
/*      */ 
/*      */       
/*  340 */       if (hashSet2.isEmpty()) {
/*  341 */         String str1 = "select userid,id,nodeId from workflow_currentoperator where requestid =  ? and usertype = 0 and " + Util.getSubINClause(paramString1, "id", "in");
/*  342 */         MsgEntity msgEntity = getBaseInfo(paramString2);
/*  343 */         msgEntity.setMsgType(MessageType.WF_DRAW_BACK);
/*  344 */         paramRecordSet.executeQuery(str1, new Object[] { paramString2 });
/*  345 */         while (paramRecordSet.next()) {
/*  346 */           String str2 = Util.null2String(paramRecordSet.getString("userid"));
/*  347 */           String str3 = Util.null2String(paramRecordSet.getString("id"));
/*  348 */           String str4 = Util.null2String(paramRecordSet.getString("nodeId"));
/*      */           
/*  350 */           msgEntity.addUserId(str2);
/*  351 */           msgEntity.addCurrentOperatorId(str2, str3);
/*  352 */           msgEntity.addCurrentOperatorNode(str3, str4);
/*      */         } 
/*  354 */         arrayList.add(msgEntity);
/*      */       } else {
/*      */         
/*  357 */         String str1 = String.join(",", (Iterable)hashSet2);
/*  358 */         String str2 = "select userid,id,nodeId from workflow_currentoperator where requestid =  ? and usertype = 0 and " + Util.getSubINClause(paramString1, "id", "in") + " and " + Util.getSubINClause(str1, "userid", "not in");
/*  359 */         MsgEntity msgEntity1 = getBaseInfo(paramString2);
/*  360 */         msgEntity1.setMsgType(MessageType.WF_DRAW_BACK);
/*  361 */         paramRecordSet.executeQuery(str2, new Object[] { paramString2 });
/*  362 */         while (paramRecordSet.next()) {
/*  363 */           String str3 = Util.null2String(paramRecordSet.getString("userid"));
/*  364 */           String str4 = Util.null2String(paramRecordSet.getString("id"));
/*  365 */           String str5 = Util.null2String(paramRecordSet.getString("nodeId"));
/*      */           
/*  367 */           msgEntity1.addUserId(str3);
/*  368 */           msgEntity1.addCurrentOperatorId(str3, str4);
/*  369 */           msgEntity1.addCurrentOperatorNode(str4, str5);
/*      */         } 
/*  371 */         arrayList.add(msgEntity1);
/*      */         
/*  373 */         str2 = "select userid,id,nodeId from workflow_currentoperator where requestid =  ? and usertype = 0 and " + Util.getSubINClause(paramString1, "id", "not in") + " and " + Util.getSubINClause(str1, "userid", "in");
/*  374 */         MsgEntity msgEntity2 = msgEntity1.clone();
/*  375 */         msgEntity2.setMsgType(MessageType.WF_COMPLETED);
/*  376 */         paramRecordSet.executeQuery(str2, new Object[] { paramString2 });
/*  377 */         while (paramRecordSet.next()) {
/*  378 */           String str3 = Util.null2String(paramRecordSet.getString("userid"));
/*  379 */           String str4 = Util.null2String(paramRecordSet.getString("id"));
/*  380 */           String str5 = Util.null2String(paramRecordSet.getString("nodeId"));
/*      */           
/*  382 */           msgEntity2.addUserId(str3);
/*  383 */           msgEntity2.addCurrentOperatorId(str3, str4);
/*  384 */           msgEntity2.addCurrentOperatorNode(str4, str5);
/*      */         } 
/*  386 */         arrayList.add(msgEntity2);
/*      */       } 
/*      */ 
/*      */       
/*  390 */       (new MsgPushUtil()).pushMsg(arrayList);
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private List<MsgEntity> archivedMsg(List<MsgEntity> paramList, MsgEntity paramMsgEntity, String paramString1, JSONObject paramJSONObject, RecordSet paramRecordSet, String paramString2) {
/*  403 */     String str1 = paramJSONObject.getString("newIds");
/*  404 */     String str2 = paramJSONObject.getString("beAgentNewids");
/*  405 */     str1 = str1 + "," + str2;
/*      */     
/*  407 */     MsgEntity msgEntity1 = paramMsgEntity.clone();
/*  408 */     msgEntity1.setMsgType((MessageType)JSONObject.parseObject(JSONObject.toJSONString(MessageType.WF_COMPLETED), MessageType.class));
/*  409 */     msgEntity1.setNoticeType(MsgNoticeType.COMPLETED);
/*      */     
/*  411 */     MsgEntity msgEntity2 = paramMsgEntity.clone();
/*  412 */     msgEntity2.setMsgType((MessageType)JSONObject.parseObject(JSONObject.toJSONString(MessageType.WF_COPY), MessageType.class));
/*  413 */     msgEntity2.setNoticeType(MsgNoticeType.CC);
/*      */ 
/*      */     
/*  416 */     paramRecordSet.executeQuery("select userid,isremark,id from workflow_currentoperator where usertype = 0 and " + Util.getSubINClause(str1, "id", "in"), new Object[0]);
/*      */     
/*  418 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  419 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/*  420 */     while (paramRecordSet.next()) {
/*  421 */       String str3 = Util.null2String(paramRecordSet.getString("userid"));
/*  422 */       String str4 = Util.null2String(paramRecordSet.getString("isremark"));
/*  423 */       String str5 = Util.null2String(paramRecordSet.getString("id"));
/*      */       
/*  425 */       if ("9".equals(str4) || "8".equals(str4)) {
/*  426 */         msgEntity2.addUserId(str3);
/*  427 */         msgEntity2.addCurrentOperatorId(str3, str5);
/*  428 */         List<String> list1 = (List)hashMap2.get(str3);
/*  429 */         if (list1 == null) {
/*  430 */           list1 = new ArrayList();
/*  431 */           list1.add(str5);
/*  432 */           hashMap2.put(str3, list1); continue;
/*      */         } 
/*  434 */         list1.add(str5);
/*      */         continue;
/*      */       } 
/*  437 */       msgEntity1.addUserId(str3);
/*  438 */       msgEntity1.addCurrentOperatorId(str3, str5);
/*  439 */       List<String> list = (List)hashMap1.get(str3);
/*  440 */       if (list == null) {
/*  441 */         list = new ArrayList();
/*  442 */         list.add(str5);
/*  443 */         hashMap1.put(str3, list); continue;
/*      */       } 
/*  445 */       list.add(str5);
/*      */     } 
/*      */ 
/*      */     
/*  449 */     for (String str : hashMap2.keySet()) {
/*  450 */       msgEntity2.addCurrentOperatorId(str, String.join(",", (Iterable<? extends CharSequence>)hashMap2.get(str)));
/*      */     }
/*  452 */     for (String str : hashMap1.keySet()) {
/*  453 */       msgEntity1.addCurrentOperatorId(str, String.join(",", (Iterable<? extends CharSequence>)hashMap1.get(str)));
/*      */     }
/*      */ 
/*      */     
/*  457 */     if ("forceover".equals(paramString2)) {
/*      */       
/*  459 */       HashSet<String> hashSet = new HashSet();
/*  460 */       JSONArray jSONArray = paramJSONObject.getJSONArray("modifyData"); byte b; int i;
/*  461 */       for (b = 0, i = jSONArray.size(); b < i; b++) {
/*  462 */         JSONObject jSONObject = jSONArray.getJSONObject(b);
/*  463 */         String str = jSONObject.getString("fieldName");
/*  464 */         if ("isremark".equals(str)) {
/*  465 */           int j = jSONObject.getIntValue("nVal");
/*  466 */           int k = jSONObject.getIntValue("oVal");
/*  467 */           if (j == 2 && k != 2) {
/*  468 */             hashSet.add(jSONObject.getString("entityId"));
/*      */           }
/*      */         } 
/*      */       } 
/*  472 */       if (!hashSet.isEmpty()) {
/*  473 */         MsgEntity msgEntity = paramMsgEntity.clone();
/*  474 */         msgEntity.setMsgType((MessageType)JSONObject.parseObject(JSONObject.toJSONString(MessageType.WF_COMPLETED), MessageType.class));
/*      */         
/*  476 */         paramRecordSet.executeQuery("select userid,id from workflow_currentoperator where usertype = 0 and " + Util.getSubINClause(String.join(",", (Iterable)hashSet), "id", "in"), new Object[0]);
/*  477 */         addOperatorId(paramRecordSet, msgEntity);
/*  478 */         paramList.add(msgEntity);
/*      */       } 
/*      */     } 
/*      */     
/*  482 */     paramList.add(msgEntity1);
/*  483 */     paramList.add(msgEntity2);
/*  484 */     (new MsgPushUtil()).pushMsg(paramList);
/*      */     
/*      */     try {
/*  487 */       paramRecordSet.executeQuery("select userid from workflow_currentoperator where usertype = 0 and requestid = ? and isremark = 2", new Object[] { paramString1 });
/*  488 */       MessageBean messageBean = new MessageBean();
/*  489 */       messageBean.setTargetId(paramString1);
/*  490 */       while (paramRecordSet.next()) {
/*  491 */         messageBean.getUserList().add(paramRecordSet.getString(1));
/*      */       }
/*  493 */       Util_Message.approvalMessage(messageBean);
/*  494 */     } catch (Exception exception) {
/*  495 */       exception.printStackTrace();
/*      */     } 
/*  497 */     return null;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public List<MsgEntity> getOperateMsg(String[] paramArrayOfString, MsgOperateType paramMsgOperateType) {
/*  508 */     ArrayList<MsgEntity> arrayList = new ArrayList();
/*  509 */     for (String str : paramArrayOfString) {
/*  510 */       arrayList.addAll(getOperateMsg(str, paramMsgOperateType));
/*      */     }
/*  512 */     return arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public List<MsgEntity> requestDeletMsg(String paramString) {
/*  522 */     ArrayList<MsgEntity> arrayList = new ArrayList();
/*      */     
/*      */     try {
/*  525 */       MsgEntity msgEntity = getBaseInfo(paramString);
/*  526 */       msgEntity.setMsgType(MessageType.WF_DELETE);
/*      */       
/*  528 */       RecordSet recordSet = new RecordSet();
/*  529 */       recordSet.executeQuery("select userid,id from workflow_currentoperator where requestid = ? and usertype = 0", new Object[] { paramString });
/*  530 */       addOperatorId(recordSet, msgEntity);
/*  531 */       recordSet.executeQuery("select userid,id from workflow_curroperator_dellog where requestid = ? and usertype = 0 ", new Object[] { paramString });
/*  532 */       addOperatorId(recordSet, msgEntity);
/*  533 */       arrayList.add(msgEntity);
/*  534 */     } catch (Exception exception) {
/*  535 */       writeLog(exception);
/*      */     } 
/*  537 */     return arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public List<MsgEntity> alreadyReadMsg(String paramString1, String paramString2) {
/*  547 */     ArrayList<MsgEntity> arrayList = new ArrayList();
/*      */     
/*      */     try {
/*  550 */       MsgEntity msgEntity = getBaseInfo(paramString1);
/*  551 */       if (msgEntity == null) return null; 
/*  552 */       msgEntity.setMsgType(MessageType.WF_READED);
/*  553 */       RecordSet recordSet = new RecordSet();
/*  554 */       recordSet.executeQuery("select userid,id from workflow_currentoperator where requestid = ? and usertype = 0 and userid = ? and islasttimes = 1", new Object[] { paramString1, paramString2 });
/*  555 */       recordSet.next();
/*  556 */       paramString2 = Util.null2String(recordSet.getString("userid"));
/*  557 */       String str = Util.null2String(recordSet.getString("id"));
/*  558 */       msgEntity.addUserId(paramString2);
/*  559 */       msgEntity.addCurrentOperatorId(paramString2, str);
/*  560 */       arrayList.add(msgEntity);
/*      */     }
/*  562 */     catch (Exception exception) {
/*  563 */       writeLog(exception);
/*      */     } 
/*  565 */     return arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public List<MsgEntity> doneAlreadyReadMsg(String paramString1, String paramString2) {
/*  576 */     ArrayList<MsgEntity> arrayList = new ArrayList();
/*      */     
/*      */     try {
/*  579 */       MsgEntity msgEntity = getBaseInfo(paramString1);
/*  580 */       if (msgEntity == null) return null; 
/*  581 */       msgEntity.setMsgType(MessageType.WF_DONE_READED);
/*  582 */       RecordSet recordSet = new RecordSet();
/*  583 */       recordSet.executeQuery("select userid,id from workflow_currentoperator where requestid = ? and usertype = 0 and userid = ? and islasttimes = 1", new Object[] { paramString1, paramString2 });
/*  584 */       recordSet.next();
/*  585 */       paramString2 = Util.null2String(recordSet.getString("userid"));
/*  586 */       String str = Util.null2String(recordSet.getString("id"));
/*  587 */       msgEntity.addUserId(paramString2);
/*  588 */       msgEntity.addCurrentOperatorId(paramString2, str);
/*  589 */       arrayList.add(msgEntity);
/*      */     }
/*  591 */     catch (Exception exception) {
/*  592 */       writeLog(exception);
/*      */     } 
/*  594 */     return arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public List<MsgEntity> getChuanyueMsg(int paramInt1, String paramString, int paramInt2, int paramInt3) {
/*  607 */     ArrayList<MsgEntity> arrayList = new ArrayList();
/*  608 */     RecordSet recordSet = new RecordSet();
/*  609 */     HashSet<String> hashSet1 = new HashSet();
/*  610 */     HashSet<String> hashSet2 = new HashSet();
/*  611 */     recordSet.executeQuery("SELECT becurrid,currid from WORKFLOW_CHUANYUE where requestid = ? and nodeid = ? and userid = ?", new Object[] {
/*  612 */           Integer.valueOf(paramInt1), Integer.valueOf(paramInt2), paramString });
/*  613 */     while (recordSet.next()) {
/*  614 */       hashSet1.add(recordSet.getString(1));
/*  615 */       hashSet2.add(recordSet.getString(2));
/*      */     } 
/*      */     
/*  618 */     if (hashSet1.isEmpty()) return arrayList; 
/*      */     try {
/*  620 */       MsgEntity msgEntity = getBaseInfo(paramInt1 + "");
/*  621 */       if (paramInt3 != 1) {
/*  622 */         arrayList.addAll(getAttentionMsg(msgEntity.clone(), paramInt1 + "", paramString));
/*      */       }
/*  624 */       msgEntity.setOperatorId(paramString);
/*  625 */       msgEntity.setMsgType((paramInt3 == 0) ? MessageType.WF_CHUANYUE_DOING : MessageType.WF_CHUANYUE_SUBJECT);
/*  626 */       msgEntity.setNoticeType((paramInt3 == 0) ? MsgNoticeType.WF_CHUANYUE_DOING : MsgNoticeType.WF_CHUANYUE_SUBJECT);
/*  627 */       ResourceComInfo resourceComInfo = new ResourceComInfo();
/*  628 */       if (paramInt3 == 1) {
/*  629 */         msgEntity.setDetailTitleParams("read_operator", resourceComInfo.getLastname(paramString));
/*      */       } else {
/*  631 */         msgEntity.setDetailTitleParams("command_operator", resourceComInfo.getLastname(paramString));
/*      */       } 
/*      */       
/*  634 */       String str = "select userid,id from workflow_currentoperator  where " + Util.getSubINClause(String.join(",", (Iterable)hashSet1), "id", "in") + " and isremark not in (2,11)";
/*      */       
/*  636 */       recordSet.executeQuery(str, new Object[0]);
/*  637 */       addOperatorId(recordSet, msgEntity);
/*  638 */       if (paramInt3 == 1) {
/*  639 */         recordSet.executeQuery("select 1 from workflow_currentoperator where viewtype <> -2 and " + 
/*  640 */             Util.getSubINClause(String.join(",", (Iterable)hashSet2), "id", "in"), new Object[0]);
/*  641 */         if (recordSet.next()) {
/*  642 */           arrayList.add(msgEntity);
/*      */         }
/*      */       } else {
/*  645 */         arrayList.add(msgEntity);
/*  646 */         MsgEntity msgEntity1 = msgEntity.clone();
/*  647 */         msgEntity1.setNoticeType(null);
/*  648 */         msgEntity1.setOperatorId(paramString);
/*  649 */         msgEntity1.setMsgType(MessageType.WF_COMPLETED);
/*  650 */         recordSet.executeQuery("select userid,id from workflow_currentoperator where requestid = ? and userid = ? and usertype = 0 and islasttimes = 1", new Object[] { Integer.valueOf(paramInt1), paramString });
/*  651 */         recordSet.next();
/*  652 */         paramString = Util.null2String(recordSet.getString("userid"));
/*  653 */         String str1 = Util.null2String(recordSet.getString("id"));
/*  654 */         msgEntity1.addUserId(paramString);
/*  655 */         msgEntity1.addCurrentOperatorId(paramString, str1);
/*  656 */         arrayList.add(msgEntity1);
/*      */       }
/*      */     
/*  659 */     } catch (Exception exception) {
/*  660 */       writeLog(exception);
/*      */     } 
/*  662 */     return arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void remarkAtMsg(String paramString1, String paramString2, List<String> paramList) {
/*      */     try {
/*  674 */       RecordSet recordSet = new RecordSet();
/*  675 */       recordSet.executeQuery("select requestnamenew,workflowid from workflow_requestbase where requestid  =  " + paramString1, new Object[0]);
/*  676 */       String str1 = "";
/*  677 */       String str2 = "";
/*  678 */       if (recordSet.next()) {
/*  679 */         str1 = recordSet.getString("requestnamenew");
/*  680 */         str2 = Util.null2String(recordSet.getString("workflowid"));
/*      */       } 
/*  682 */       String str3 = String.format("" + SystemEnv.getHtmlLabelName(10004316, ThreadVarLanguage.getLang()) + "%s", new Object[] { str1 });
/*      */       
/*  684 */       String str4 = "?requestid=" + paramString1;
/*  685 */       String str5 = "/spa/workflow/index_form.jsp#/main/workflow/req" + str4;
/*  686 */       String str6 = "/spa/workflow/static4mobileform/index.html#/req" + str4;
/*  687 */       String str7 = remarkAtMsgContext(recordSet, paramString1, paramString2);
/*  688 */       ConfigManager configManager = new ConfigManager();
/*  689 */       Map map = configManager.defaultRuleCheckConfig(MessageType.MENTIONS_OF_ME, paramList, null);
/*  690 */       for (Map.Entry entry : map.entrySet()) {
/*  691 */         MessageBean messageBean = Util_Message.createMessage(MessageType.MENTIONS_OF_ME, Util.getIntValue(paramString2), str3, Util.null2String(Integer.valueOf(MessageType.MENTIONS_OF_ME.getLabelId())), str7, str5, str6, Util.getIntValue(paramString2));
/*  692 */         messageBean.setUserList(Sets.newHashSet((Iterable)entry.getValue()));
/*  693 */         messageBean.setMessageConfig((WeaMessageTypeConfig)entry.getKey());
/*  694 */         Util_Message.sendAndpublishMessage(messageBean);
/*      */       } 
/*  696 */     } catch (Exception exception) {
/*  697 */       writeLog(exception);
/*      */     } 
/*      */   }
/*      */   
/*      */   private String remarkAtMsgContext(RecordSet paramRecordSet, String paramString1, String paramString2) throws Exception {
/*  702 */     String str1 = null;
/*  703 */     paramRecordSet.executeQuery("select remark from workflow_requestLog  where requestid = ? and operator= ? order by logid desc ", new Object[] { paramString1, paramString2 });
/*  704 */     paramRecordSet.next();
/*  705 */     String str2 = Util.null2String(paramRecordSet.getString(1));
/*      */     try {
/*  707 */       JSONObject jSONObject = JSONObject.parseObject(str2);
/*  708 */       str2 = jSONObject.getString("remark");
/*  709 */     } catch (Exception exception) {
/*  710 */       str2 = Util.null2String(paramRecordSet.getString(1));
/*      */     } 
/*  712 */     if (str2.startsWith("<p>")) {
/*  713 */       str2 = str2.substring(3, str2.length());
/*      */     }
/*  715 */     str1 = "<p>" + (new ResourceComInfo()).getLastname(paramString2) + "：" + str2;
/*      */     
/*  717 */     return str1;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public List<MsgEntity> getOperateMsgByReqId(String paramString1, User paramUser, String paramString2) {
/*  728 */     RecordSet recordSet = new RecordSet();
/*      */     
/*  730 */     ArrayList<MsgEntity> arrayList = new ArrayList();
/*      */     
/*      */     try {
/*  733 */       MsgEntity msgEntity = getBaseInfo(paramString1);
/*  734 */       if (msgEntity == null) return null; 
/*  735 */       msgEntity.setOperatorId(String.valueOf(paramUser.getUID()));
/*      */ 
/*      */       
/*  738 */       MessageType messageType = null;
/*  739 */       if ("stop".equals(paramString2) || "cancel".equals(paramString2) || "delete".equals(paramString2)) {
/*  740 */         if ("stop".equals(paramString2)) {
/*  741 */           messageType = (MessageType)JSONObject.parseObject(JSONObject.toJSONString(MessageType.WF_STOP), MessageType.class);
/*  742 */           msgEntity.setMsgType(messageType);
/*  743 */           recordSet.executeQuery("select userid, id,nodeid from workflow_currentoperator where userid <> ? and usertype = 0 and requestid = ?", new Object[] { msgEntity.getCreator(), paramString1 });
/*      */         }
/*      */         else {
/*      */           
/*  747 */           messageType = "delete".equals(paramString2) ? (MessageType)JSONObject.parseObject(JSONObject.toJSONString(MessageType.WF_DELETE), MessageType.class) : (MessageType)JSONObject.parseObject(JSONObject.toJSONString(MessageType.WF_CANCEL), MessageType.class);
/*      */           
/*  749 */           msgEntity.setMsgType(messageType);
/*  750 */           recordSet.executeQuery("select userid, id,nodeid from workflow_currentoperator where requestid = ? and usertype = 0", new Object[] { paramString1 });
/*      */         } 
/*  752 */         addOperatorId(recordSet, msgEntity);
/*  753 */         arrayList.add(msgEntity);
/*      */       } 
/*      */ 
/*      */       
/*  757 */       if ("restart".equals(paramString2))
/*      */       {
/*      */         
/*  760 */         messageType = (MessageType)JSONObject.parseObject(JSONObject.toJSONString(MessageType.WF_COMPLETED), MessageType.class);
/*  761 */         MsgEntity msgEntity1 = msgEntity.clone();
/*  762 */         msgEntity1.setMsgType(messageType);
/*  763 */         recordSet.executeQuery("select userid,id from workflow_currentoperator where requestid = ? and isremark = 2 and usertype = 0", new Object[] { paramString1 });
/*  764 */         addOperatorId(recordSet, msgEntity1);
/*      */ 
/*      */         
/*  767 */         messageType = (MessageType)JSONObject.parseObject(JSONObject.toJSONString(MessageType.WF_NEW_ARRIVAL), MessageType.class);
/*  768 */         SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
/*  769 */         String str = simpleDateFormat.format(new Date());
/*  770 */         MsgEntity msgEntity2 = msgEntity.clone();
/*  771 */         msgEntity2.setMsgType(messageType);
/*  772 */         msgEntity2.setNoticeType(MsgNoticeType.NEW_ARRIVAL);
/*  773 */         msgEntity2.setReceiveDate(str);
/*  774 */         recordSet.executeQuery("select userid,id from workflow_currentoperator where requestid = ? and isremark <> 2 and usertype = 0 ", new Object[] { paramString1 });
/*  775 */         addOperatorId(recordSet, msgEntity2);
/*  776 */         if (msgEntity1.getUserId() != null && !msgEntity1.getUserId().isEmpty()) {
/*  777 */           arrayList.add(msgEntity1);
/*      */         }
/*  779 */         if (msgEntity2.getUserId() != null && !msgEntity2.getUserId().isEmpty()) {
/*  780 */           arrayList.add(msgEntity2);
/*      */         }
/*      */       }
/*      */     
/*  784 */     } catch (Exception exception) {
/*  785 */       writeLog(exception);
/*      */     } 
/*  787 */     return arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public List<MsgEntity> getRemarkReplyMsg(String paramString1, String paramString2, String paramString3) {
/*  799 */     RecordSet recordSet = new RecordSet();
/*  800 */     ArrayList<MsgEntity> arrayList = new ArrayList();
/*      */     
/*      */     try {
/*  803 */       MsgEntity msgEntity = getBaseInfo(paramString1);
/*  804 */       if (msgEntity == null) return null;
/*      */       
/*  806 */       arrayList.addAll(getAttentionMsg(msgEntity.clone(), paramString1 + "", paramString2));
/*      */       
/*  808 */       msgEntity.setOperatorId(paramString2);
/*  809 */       msgEntity.setMsgType(MessageType.WF_COMPLETED);
/*  810 */       recordSet.executeQuery("select userid,id from workflow_currentoperator where requestid = ? and userid = ? and usertype = 0 and islasttimes = 1", new Object[] { paramString1, paramString2 });
/*  811 */       recordSet.next();
/*  812 */       String str1 = Util.null2String(recordSet.getString("userid"));
/*  813 */       String str2 = Util.null2String(recordSet.getString("id"));
/*  814 */       msgEntity.addUserId(str1);
/*  815 */       msgEntity.addCurrentOperatorId(str1, str2);
/*  816 */       arrayList.add(msgEntity);
/*      */ 
/*      */       
/*  819 */       recordSet.executeQuery("select detailinfo from workflow_requestoperatelog where requestid = ? and operatorid= ? and operatecode = 11 and id = ? and (isinvalid is null  or isinvalid <> 1)", new Object[] { paramString1, paramString2, paramString3 });
/*  820 */       if (recordSet.next()) {
/*      */         
/*  822 */         String str = Util.null2String(recordSet.getString(1));
/*  823 */         JSONObject jSONObject1 = JSONObject.parseObject(str);
/*  824 */         if (jSONObject1 == null || jSONObject1.getJSONObject("workflow_currentoperator") == null) return null; 
/*  825 */         JSONObject jSONObject2 = jSONObject1.getJSONObject("workflow_currentoperator");
/*  826 */         JSONArray jSONArray = jSONObject2.getJSONArray("modifyData");
/*      */         
/*  828 */         boolean bool = false;
/*  829 */         HashSet<String> hashSet = new HashSet(); byte b; int i;
/*  830 */         for (b = 0, i = jSONArray.size(); b < i; b++) {
/*      */           
/*  832 */           JSONObject jSONObject = jSONArray.getJSONObject(b);
/*      */           
/*  834 */           String str3 = jSONObject.getString("fieldName");
/*  835 */           if ("takisremark".equals(str3)) {
/*  836 */             int j = Util.getIntValue(jSONObject.getString("nVal"));
/*  837 */             int k = Util.getIntValue(jSONObject.getString("oVal"));
/*  838 */             if (j == 0 && k == -2) {
/*  839 */               bool = true;
/*  840 */               hashSet.add(jSONObject.getString("entityId"));
/*      */             } 
/*      */           } 
/*  843 */           if ("istakout".equals(str3)) {
/*  844 */             int j = Util.getIntValue(jSONObject.getString("nVal"));
/*  845 */             int k = Util.getIntValue(jSONObject.getString("oVal"));
/*  846 */             if (j == 0 && k == 1) {
/*  847 */               bool = true;
/*  848 */               hashSet.add(jSONObject.getString("entityId"));
/*      */             } 
/*      */           } 
/*  851 */           if ("isremark".equals(str3)) {
/*  852 */             int j = Util.getIntValue(jSONObject.getString("nVal"));
/*  853 */             int k = Util.getIntValue(jSONObject.getString("oVal"));
/*  854 */             int m = Util.getIntValue(jSONObject.getString("entityId"));
/*  855 */             if (j == 2 && k == 1) {
/*  856 */               recordSet.executeQuery("select forwardid from workflow_forward where requestid = ? and beforwardid = ?", new Object[] { paramString1, Integer.valueOf(m) });
/*  857 */               recordSet.next();
/*  858 */               hashSet.add(Util.null2String(recordSet.getString("forwardid")));
/*      */             } 
/*      */           } 
/*      */         } 
/*      */         
/*  863 */         MsgEntity msgEntity1 = msgEntity.clone();
/*  864 */         arrayList.add(msgEntity1);
/*      */         
/*  866 */         recordSet.executeQuery("select userid,id from workflow_currentoperator where usertype = 0 and " + Util.getSubINClause(String.join(",", (Iterable)hashSet), "id", "in"), new Object[0]);
/*  867 */         addOperatorId(recordSet, msgEntity1);
/*  868 */         if (bool) {
/*  869 */           msgEntity1.setMsgType(MessageType.WF_NEW_ARRIVAL);
/*  870 */           msgEntity1.setNoticeType(MsgNoticeType.NEW_ARRIVAL);
/*      */         } else {
/*  872 */           msgEntity1.setMsgType(MessageType.WF_TAKE_REPET);
/*      */         }
/*      */       
/*      */       } 
/*  876 */     } catch (Exception exception) {
/*  877 */       writeLog(exception);
/*      */     } 
/*  879 */     return arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public List<MsgEntity> getForwardReplyMsg(String paramString1, String paramString2) {
/*  889 */     return getForwardReplyMsg(paramString1, paramString2, "");
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public List<MsgEntity> getTakEndMsg(int paramInt1, int paramInt2, String paramString) {
/*  901 */     RecordSet recordSet = new RecordSet();
/*  902 */     ArrayList<MsgEntity> arrayList = new ArrayList();
/*  903 */     MsgEntity msgEntity1 = getBaseInfo(String.valueOf(paramInt1));
/*      */     
/*  905 */     recordSet.executeQuery("select userid, id from workflow_currentoperator where requestid = ? and id = ? and usertype = 0", new Object[] { Integer.valueOf(paramInt1), Integer.valueOf(paramInt2) });
/*  906 */     if (recordSet.next()) {
/*  907 */       String str1 = Util.null2String(recordSet.getString(1));
/*  908 */       String str2 = Util.null2String(recordSet.getString(2));
/*  909 */       MsgEntity msgEntity = msgEntity1.clone();
/*  910 */       msgEntity.addUserId(str1);
/*  911 */       msgEntity.addCurrentOperatorId(str1, str2);
/*  912 */       msgEntity.setMsgType(MessageType.WF_NEW_ARRIVAL);
/*  913 */       msgEntity.setOperatorId(str1);
/*  914 */       arrayList.add(msgEntity);
/*      */     } 
/*      */     
/*  917 */     MsgEntity msgEntity2 = msgEntity1.clone();
/*  918 */     msgEntity2.setMsgType(MessageType.WF_COMPLETED);
/*  919 */     recordSet.executeQuery("select userid, id from workflow_currentoperator where requestid = ? and usertype = 0 and isremark = 1 and " + Util.getSubINClause(paramString, "id", "IN"), new Object[] { Integer.valueOf(paramInt1) });
/*  920 */     addOperatorId(recordSet, msgEntity2);
/*      */     
/*  922 */     arrayList.add(msgEntity2);
/*  923 */     return arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public List<MsgEntity> getForwardReplyMsg(String paramString1, String paramString2, String paramString3) {
/*  935 */     ArrayList<MsgEntity> arrayList = new ArrayList();
/*      */ 
/*      */     
/*      */     try {
/*  939 */       MsgEntity msgEntity = getBaseInfo(paramString1);
/*  940 */       if (msgEntity == null) return null;
/*      */       
/*  942 */       msgEntity.setMsgType(MessageType.WF_COMPLETED);
/*  943 */       msgEntity.setOperatorId(paramString2);
/*  944 */       RecordSet recordSet = new RecordSet();
/*  945 */       recordSet.executeQuery("select userid, id from workflow_currentoperator where requestid = ? and usertype = 0 and userid = ? and islasttimes = 1", new Object[] { paramString1, paramString2 });
/*  946 */       recordSet.next();
/*  947 */       String str = Util.null2String(recordSet.getString("userid"));
/*  948 */       msgEntity.addUserId(str);
/*  949 */       msgEntity.addCurrentOperatorId(str, Util.null2String(recordSet.getString("id")));
/*      */       
/*  951 */       if (!"8".equals(paramString3)) arrayList.addAll(getAttentionMsg(msgEntity.clone(), paramString1 + "", paramString2)); 
/*  952 */       arrayList.add(msgEntity);
/*      */     }
/*  954 */     catch (Exception exception) {
/*  955 */       writeLog(exception);
/*      */     } 
/*  957 */     return arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public List<MsgEntity> forwardReplyNotice(int paramInt, User paramUser) {
/*  968 */     ArrayList<MsgEntity> arrayList = new ArrayList();
/*      */     try {
/*  970 */       RecordSet recordSet = new RecordSet();
/*  971 */       ArrayList<String> arrayList1 = new ArrayList();
/*      */ 
/*      */       
/*  974 */       HashMap<Object, Object> hashMap = new HashMap<>();
/*  975 */       recordSet.executeQuery("select id,receivedate,receivetime from workflow_currentoperator where requestid = ? and usertype = 0 and userid = ? and isremark = 1", new Object[] { Integer.valueOf(paramInt), Integer.valueOf(paramUser.getUID()) });
/*  976 */       while (recordSet.next()) {
/*  977 */         String str1 = Util.null2String(recordSet.getString(2));
/*  978 */         String str2 = Util.null2String(recordSet.getString(3));
/*  979 */         hashMap.put(Util.null2String(recordSet.getString("id")), String.format("%s %s", new Object[] { str1, str2 }));
/*      */       } 
/*      */       
/*  982 */       if (!hashMap.isEmpty()) {
/*      */         
/*  984 */         HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  985 */         String str = "select forwardid,beforwardid from workflow_forward where requestid = ? and isShowReplyNotice = '1'";
/*  986 */         str = str + " and " + Util.getSubINClause(String.join(",", hashMap.keySet()), "beforwardid", "in");
/*  987 */         recordSet.executeQuery(str, new Object[] { Integer.valueOf(paramInt) });
/*  988 */         while (recordSet.next()) {
/*  989 */           hashMap1.put(Util.null2String(recordSet.getString("forwardid")), Util.null2String(recordSet.getString("beforwardid")));
/*      */         }
/*      */ 
/*      */         
/*  993 */         if (!hashMap1.isEmpty()) {
/*  994 */           recordSet.executeQuery("select id,userid,receivedate,receivetime,agentorbyagentid,agenttype from workflow_currentoperator where usertype = 0 and " + Util.getSubINClause(String.join(",", hashMap1.keySet()), "id", "in"), new Object[0]);
/*  995 */           while (recordSet.next()) {
/*  996 */             String str1 = Util.null2String(recordSet.getString("userid"));
/*      */             
/*  998 */             String str2 = Util.null2String(recordSet.getString("agenttype"));
/*  999 */             if ("2".equals(str2)) {
/*      */               
/* 1001 */               String str3 = Util.null2String(recordSet.getString("receivedate"));
/* 1002 */               String str4 = Util.null2String(recordSet.getString("receivetime"));
/* 1003 */               String str5 = String.format("%s %s", new Object[] { str3, str4 });
/*      */               
/* 1005 */               String str6 = Util.null2String(recordSet.getString("id"));
/* 1006 */               String str7 = (String)hashMap1.get(str6);
/* 1007 */               String str8 = (String)hashMap.get(str7);
/*      */               
/* 1009 */               SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
/* 1010 */               Date date1 = simpleDateFormat.parse(str8);
/* 1011 */               Date date2 = simpleDateFormat.parse(str5);
/* 1012 */               if (date1.getTime() < date2.getTime()) {
/* 1013 */                 arrayList1.add(Util.null2String(recordSet.getString("agentorbyagentid"))); continue;
/*      */               } 
/* 1015 */               arrayList1.add(str1);
/*      */               continue;
/*      */             } 
/* 1018 */             arrayList1.add(str1);
/*      */           } 
/*      */         } 
/*      */       } 
/*      */ 
/*      */       
/* 1024 */       MsgEntity msgEntity = getBaseInfo(String.valueOf(paramInt));
/* 1025 */       if (msgEntity == null) return null;
/*      */       
/* 1027 */       msgEntity.setMsgType(MessageType.newInstance(136));
/* 1028 */       msgEntity.setNoticeType(MsgNoticeType.WF_FORWARD_ANNOTATE);
/* 1029 */       msgEntity.addAllUserId(arrayList1);
/* 1030 */       msgEntity.setOperatorId(String.valueOf(paramUser.getUID()));
/* 1031 */       msgEntity.setDetailTitleParams("requestname", "");
/* 1032 */       msgEntity.setDetailTitleParams("username", paramUser.getLastname());
/* 1033 */       arrayList.add(msgEntity);
/*      */     }
/* 1035 */     catch (Exception exception) {
/* 1036 */       writeLog(exception);
/*      */     } 
/* 1038 */     return arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public List<MsgEntity> autoApproveSendMsg(RequestManager paramRequestManager, WFAutoApproveUtils.AutoApproveParams paramAutoApproveParams) {
/* 1050 */     ArrayList<MsgEntity> arrayList = new ArrayList();
/*      */     
/* 1052 */     int i = paramRequestManager.getRequestid();
/* 1053 */     int j = paramAutoApproveParams.getUserid();
/* 1054 */     int k = paramAutoApproveParams.getNodeid();
/* 1055 */     Queue<WFAutoApproveUtils.AutoApproveParams> queue = paramRequestManager.getAutoApproveQueue();
/* 1056 */     if (queue.isEmpty()) {
/*      */       
/* 1058 */       MsgEntity msgEntity = getBaseInfo(String.valueOf(i));
/* 1059 */       if (msgEntity == null) return null;
/*      */       
/* 1061 */       RecordSet recordSet = new RecordSet();
/*      */ 
/*      */       
/* 1064 */       String str1 = paramAutoApproveParams.getCurrentoperatorid();
/* 1065 */       HashSet<String> hashSet1 = new HashSet();
/* 1066 */       if (k > 0) {
/* 1067 */         recordSet.executeQuery("select a.nodeid,b.workflowid from workflow_NodeLink a,workflow_currentoperator b  where a.destnodeid=b.nodeid and b.id = ? ", new Object[] { str1 });
/* 1068 */         if (recordSet.next()) {
/* 1069 */           String str4 = recordSet.getString(1);
/* 1070 */           String str5 = recordSet.getString(2);
/* 1071 */           NodeInfoEntity nodeInfoEntity = WorkflowBaseBiz.getNodeInfo(Util.getIntValue(str4));
/* 1072 */           int m = Util.getIntValue(nodeInfoEntity.getNodeAttribute());
/* 1073 */           if (m == 1) {
/* 1074 */             String str = " SELECT  destnodeid FROM workflow_NodeLink a, workflow_nodebase b WHERE a.wfrequestid IS NULL  AND ( a.isreject IS NULL OR a.isreject <> '1' )  AND a.nodeid = ?  AND a.workflowid = ? AND a.destnodeid = b.id AND b.nodeattribute  = 2";
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */             
/* 1086 */             recordSet.executeQuery(str, new Object[] { str4, str5 });
/* 1087 */             while (recordSet.next()) {
/* 1088 */               hashSet1.add(Util.null2String(recordSet.getString("destnodeid")));
/*      */             }
/*      */           }
/*      */         
/*      */         } 
/*      */       } else {
/*      */         
/* 1095 */         recordSet.executeQuery("select c.nodeid from workflow_freenode_group a,      workflow_currentoperator b,      workflow_freenode_group c where a.requestid = b.requestid   and a.nodeid = b.nodeid   and a.parentid = c.parentid   and c.nodeid <> b.nodeid   and b.id = ? ", new Object[] { str1 });
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */         
/* 1104 */         while (recordSet.next()) {
/* 1105 */           hashSet1.add(Util.null2String(recordSet.getString("nodeid")));
/*      */         }
/*      */       } 
/*      */       
/* 1109 */       String str2 = "";
/* 1110 */       if (!hashSet1.isEmpty()) {
/* 1111 */         str2 = " or a.nodeid in (" + String.join(",", (Iterable)hashSet1) + ")";
/*      */       }
/*      */       
/* 1114 */       HashSet<String> hashSet2 = new HashSet();
/* 1115 */       hashSet2.add(str1);
/*      */ 
/*      */       
/* 1118 */       recordSet.executeQuery("select detailInfo from workflow_requestoperatelog where operatorid =? and operatetype ='submit' and (isinvalid is null or isinvalid='') and requestid = ? order by id DESC", new Object[] {
/* 1119 */             Integer.valueOf(j), Integer.valueOf(i) });
/* 1120 */       if (recordSet.next()) {
/* 1121 */         String str = recordSet.getString(1);
/* 1122 */         JSONObject jSONObject = JSONObject.parseObject(str);
/* 1123 */         if (jSONObject != null) {
/* 1124 */           JSONObject jSONObject1 = jSONObject.getJSONObject(RequestOperateEntityTableNameEnum.CURRENTOPERATOR.getTableName());
/* 1125 */           if (jSONObject1 != null) {
/* 1126 */             hashSet2.add(jSONObject1.getString("newIds"));
/*      */           }
/*      */         } 
/*      */       } 
/* 1130 */       String str3 = hashSet2.isEmpty() ? " " : (" AND " + Util.getSubINClause(String.join(",", (Iterable)hashSet2), "a.id", "not in"));
/* 1131 */       recordSet.executeQuery("SELECT  a.userid, a.id  FROM workflow_currentoperator a, workflow_currentoperator b WHERE b.id = ? AND a.isremark = 0 AND a.usertype = 0  AND (a.nodeid = b.nodeid " + str2 + ")" + str3 + " AND a.requestid = b.requestid ", new Object[] { str1 });
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/* 1144 */       if (recordSet.getCounts() > 0) {
/* 1145 */         MsgEntity msgEntity1 = msgEntity.clone();
/* 1146 */         arrayList.add(msgEntity1);
/* 1147 */         msgEntity1.setOperatorId(String.valueOf(j));
/* 1148 */         msgEntity1.setMsgType(MessageType.WF_NEW_ARRIVAL);
/* 1149 */         msgEntity1.setNoticeType(MsgNoticeType.NEW_ARRIVAL);
/* 1150 */         addOperatorId(recordSet, msgEntity1);
/*      */       } 
/*      */ 
/*      */       
/* 1154 */       hashSet1.add(String.valueOf(k));
/* 1155 */       msgEntity.setOperatorId(String.valueOf(j));
/* 1156 */       msgEntity.setMsgType(MessageType.WF_COPY);
/* 1157 */       msgEntity.setNoticeType(MsgNoticeType.CC);
/* 1158 */       recordSet.executeQuery("select userid,id from workflow_currentoperator where (isremark = 8 or isremark = 9) and usertype = 0 and requestid = ? and nodeid in (" + String.join(",", (Iterable)hashSet1) + ")", new Object[] { Integer.valueOf(i) });
/* 1159 */       if (recordSet.getCounts() > 0) {
/* 1160 */         addOperatorId(recordSet, msgEntity);
/* 1161 */         arrayList.add(msgEntity);
/*      */       } 
/*      */       
/* 1164 */       if (isArchived(recordSet, String.valueOf(i))) {
/*      */         
/*      */         try {
/* 1167 */           recordSet.executeQuery("select userid from workflow_currentoperator where usertype = 0 and requestid = ? and isremark = 2", new Object[] { Integer.valueOf(i) });
/* 1168 */           MessageBean messageBean = new MessageBean();
/* 1169 */           messageBean.setTargetId(String.valueOf(i));
/* 1170 */           while (recordSet.next()) {
/* 1171 */             messageBean.getUserList().add(recordSet.getString(1));
/*      */           }
/* 1173 */           Util_Message.approvalMessage(messageBean);
/* 1174 */         } catch (Exception exception) {
/* 1175 */           exception.printStackTrace();
/*      */         }
/*      */       
/*      */       }
/*      */     } else {
/*      */       
/* 1181 */       WFAutoApproveUtils.AutoApproveParams autoApproveParams = queue.peek();
/* 1182 */       int m = autoApproveParams.getNodeid();
/*      */ 
/*      */       
/* 1185 */       if (k != m) {
/* 1186 */         MsgEntity msgEntity = getBaseInfo(String.valueOf(i));
/* 1187 */         if (msgEntity == null) return null;
/*      */ 
/*      */         
/* 1190 */         msgEntity.setOperatorId(String.valueOf(j));
/* 1191 */         msgEntity.setMsgType(MessageType.WF_COPY);
/* 1192 */         msgEntity.setNoticeType(MsgNoticeType.CC);
/* 1193 */         RecordSet recordSet = new RecordSet();
/* 1194 */         recordSet.executeQuery("select userid,id from workflow_currentoperator where (isremark = 8 or isremark = 9)  and usertype = 0 and requestid = ? and nodeid = ?", new Object[] {
/* 1195 */               Integer.valueOf(i), Integer.valueOf(k) });
/* 1196 */         if (recordSet.getCounts() > 0) {
/* 1197 */           addOperatorId(recordSet, msgEntity);
/* 1198 */           arrayList.add(msgEntity);
/*      */         } 
/*      */       } 
/*      */     } 
/*      */     
/* 1203 */     return arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setAttentionMsg(List<MsgEntity> paramList, String paramString1, String paramString2) {
/* 1213 */     MsgEntity msgEntity = getBaseInfo(paramString1);
/*      */     try {
/* 1215 */       if (null != msgEntity) {
/* 1216 */         List list = (new RequestAttentionBiz()).getUserByRequestId(Util.getIntValue(paramString1), paramString2);
/* 1217 */         for (Map map : list) {
/* 1218 */           msgEntity.addUserId(String.valueOf(map.get("id")));
/*      */         }
/* 1220 */         msgEntity.setMsgType(MessageType.WF_CONCERN);
/* 1221 */         msgEntity.setNoticeType(MsgNoticeType.WF_CONCERN);
/* 1222 */         msgEntity.setCurrentUserid(paramString2);
/* 1223 */         msgEntity.setDetailTypeName(Util.formatMultiLang(LabelUtil.getMultiLangLabel(String.valueOf(MessageType.WF_CONCERN.getLableId()))));
/*      */         
/* 1225 */         paramList.add(msgEntity);
/*      */       } 
/* 1227 */     } catch (Exception exception) {
/* 1228 */       writeLog(exception);
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public List<MsgEntity> getAttentionMsg(MsgEntity paramMsgEntity, String paramString1, String paramString2) {
/* 1240 */     ArrayList<MsgEntity> arrayList = new ArrayList();
/*      */ 
/*      */     
/* 1243 */     if (isDoAutoApprove()) {
/* 1244 */       return arrayList;
/*      */     }
/*      */     
/*      */     try {
/* 1248 */       if (paramMsgEntity == null) return null; 
/* 1249 */       RecordSet recordSet = new RecordSet();
/* 1250 */       recordSet.executeQuery("SELECT distinct userid FROM workflow_attention a WHERE requestid = ? and usertype = 0 ", new Object[] { paramString1 });
/* 1251 */       while (recordSet.next()) {
/* 1252 */         paramMsgEntity.addUserId(recordSet.getString(1));
/*      */       }
/*      */       
/* 1255 */       paramMsgEntity.setMsgType(MessageType.WF_CONCERN);
/* 1256 */       paramMsgEntity.setNoticeType(MsgNoticeType.WF_CONCERN);
/* 1257 */       paramMsgEntity.setCurrentUserid(paramString2);
/* 1258 */       paramMsgEntity.setDetailTypeName(Util.formatMultiLang(LabelUtil.getMultiLangLabel(String.valueOf(MessageType.WF_CONCERN.getLableId()))));
/*      */       
/* 1260 */       arrayList.add(paramMsgEntity);
/* 1261 */     } catch (Exception exception) {
/* 1262 */       writeLog(exception);
/*      */     } 
/* 1264 */     return arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private List<MsgEntity> getSuperviseMsg(String paramString1, String paramString2) {
/* 1276 */     ArrayList<MsgEntity> arrayList = new ArrayList();
/* 1277 */     RecordSet recordSet = new RecordSet();
/*      */     
/*      */     try {
/* 1280 */       MsgEntity msgEntity = getBaseInfo(paramString1);
/* 1281 */       if (msgEntity == null) return null;
/*      */ 
/*      */       
/* 1284 */       arrayList.addAll(getAttentionMsg(msgEntity.clone(), paramString1 + "", paramString2));
/*      */       
/* 1286 */       String str1 = "select distinct userid, id from workflow_currentoperator where (isremark in ('0','4') or (isremark = '1' and takisremark = '2')) and userid != ? and usertype = 0 and requestid = ? order by userid";
/* 1287 */       recordSet.executeQuery(str1, new Object[] { paramString2, paramString1 });
/* 1288 */       addOperatorId(recordSet, msgEntity);
/*      */       
/* 1290 */       String str2 = "";
/* 1291 */       recordSet.executeQuery("select remark from workflow_requestlog where requestid = ? and operator = ? and logtype = ? order by logid desc", new Object[] { paramString1, paramString2, "s" });
/* 1292 */       if (recordSet.next()) {
/* 1293 */         str2 = Util.null2String(recordSet.getString(1));
/*      */       }
/*      */       
/* 1296 */       msgEntity.setMsgType(MessageType.WF_SUPERVISE);
/* 1297 */       msgEntity.setCurrentUserid(paramString2);
/* 1298 */       msgEntity.setRemark(str2);
/* 1299 */       msgEntity.setDetailTypeName(Util.formatMultiLang(LabelUtil.getMultiLangLabel(String.valueOf(MessageType.WF_SUPERVISE.getLableId()))));
/*      */       
/* 1301 */       arrayList.add(msgEntity);
/* 1302 */     } catch (Exception exception) {
/* 1303 */       writeLog(exception);
/*      */     } 
/* 1305 */     return arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public List<MsgEntity> getAgentMsg(String paramString1, String paramString2, String paramString3) {
/* 1319 */     ArrayList<MsgEntity> arrayList = new ArrayList();
/*      */     
/*      */     try {
/* 1322 */       MsgEntity msgEntity1 = getBaseInfo(paramString1);
/* 1323 */       if (msgEntity1 == null) return null; 
/* 1324 */       msgEntity1.setMsgType(MessageType.WF_COMPLETED);
/* 1325 */       RecordSet recordSet = new RecordSet();
/* 1326 */       recordSet.executeQuery("select userid, id from workflow_currentoperator where requestid = ? and usertype = 0 and userid = ? and islasttimes = 1", new Object[] { paramString1, paramString3 });
/* 1327 */       recordSet.next();
/* 1328 */       String str = Util.null2String(recordSet.getString("userid"));
/* 1329 */       msgEntity1.addUserId(str);
/* 1330 */       msgEntity1.addCurrentOperatorId(str, Util.null2String(recordSet.getString("id")));
/*      */       
/* 1332 */       MsgEntity msgEntity2 = msgEntity1.clone();
/* 1333 */       msgEntity2.setMsgType(MessageType.WF_NEW_ARRIVAL);
/* 1334 */       msgEntity2.setNoticeType(MsgNoticeType.NEW_ARRIVAL);
/* 1335 */       recordSet.executeQuery("select userid, id from workflow_currentoperator where requestid = ? and usertype = 0 and userid = ? and islasttimes = 1", new Object[] { paramString1, paramString2 });
/* 1336 */       recordSet.next();
/* 1337 */       str = Util.null2String(recordSet.getString("userid"));
/* 1338 */       msgEntity2.addUserId(str);
/* 1339 */       msgEntity2.addCurrentOperatorId(str, Util.null2String(recordSet.getString("id")));
/*      */ 
/*      */       
/* 1342 */       arrayList.add(msgEntity1);
/* 1343 */       arrayList.add(msgEntity2);
/*      */     }
/* 1345 */     catch (Exception exception) {
/* 1346 */       writeLog(exception);
/*      */     } 
/* 1348 */     return arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public List<MsgEntity> getAgentBackMsg(int paramInt1, int paramInt2) {
/* 1358 */     ArrayList<MsgEntity> arrayList = new ArrayList();
/*      */     try {
/* 1360 */       RecordSet recordSet = new RecordSet();
/* 1361 */       recordSet.executeQuery("select userid,id from workflow_currentoperator where requestid =? and userid = ? and islasttimes=1", new Object[] { Integer.valueOf(paramInt1), Integer.valueOf(paramInt2) });
/* 1362 */       MsgEntity msgEntity = getBaseInfo(String.valueOf(paramInt1));
/* 1363 */       msgEntity.setMsgType((MessageType)JSONObject.parseObject(JSONObject.toJSONString(MessageType.WF_DRAW_BACK), MessageType.class));
/* 1364 */       addOperatorId(recordSet, msgEntity);
/* 1365 */       arrayList.add(msgEntity);
/* 1366 */     } catch (Exception exception) {
/* 1367 */       exception.printStackTrace();
/*      */     } 
/* 1369 */     return arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public List<MsgEntity> getBeAgentBackMsg(int paramInt1, int paramInt2) {
/* 1379 */     ArrayList<MsgEntity> arrayList = new ArrayList();
/*      */     try {
/* 1381 */       RecordSet recordSet = new RecordSet();
/* 1382 */       recordSet.executeQuery("select userid,id,isremark from workflow_currentoperator where requestid =? and userid = ? and islasttimes=1", new Object[] { Integer.valueOf(paramInt1), Integer.valueOf(paramInt2) });
/* 1383 */       if (recordSet.next()) {
/* 1384 */         MsgEntity msgEntity = getBaseInfo(String.valueOf(paramInt1));
/* 1385 */         int i = Util.getIntValue(Util.null2String(recordSet.getString("isremark")));
/* 1386 */         if (i == 2 || i == 4) {
/* 1387 */           msgEntity.setMsgType((MessageType)JSONObject.parseObject(JSONObject.toJSONString(MessageType.WF_COMPLETED), MessageType.class));
/*      */         } else {
/* 1389 */           msgEntity.setMsgType((MessageType)JSONObject.parseObject(JSONObject.toJSONString(MessageType.WF_NEW_ARRIVAL), MessageType.class));
/*      */         } 
/* 1391 */         recordSet.executeQuery("select userid,id,isremark from workflow_currentoperator where requestid =? and userid = ? and islasttimes=1", new Object[] { Integer.valueOf(paramInt1), Integer.valueOf(paramInt2) });
/* 1392 */         addOperatorId(recordSet, msgEntity);
/* 1393 */         arrayList.add(msgEntity);
/*      */       }
/*      */     
/* 1396 */     } catch (Exception exception) {
/* 1397 */       exception.printStackTrace();
/*      */     } 
/* 1399 */     return arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public List<MsgEntity> getAgentBackMsg(String paramString, int paramInt1, int paramInt2, int paramInt3) {
/* 1414 */     ArrayList<MsgEntity> arrayList = new ArrayList();
/*      */ 
/*      */     
/*      */     try {
/* 1418 */       MsgEntity msgEntity1 = getBaseInfo(paramString);
/* 1419 */       if (msgEntity1 == null) return null; 
/* 1420 */       RecordSet recordSet = new RecordSet();
/* 1421 */       recordSet.executeQuery("select beforwardid from workflow_Forward where requestid = ? and forwardid = ?", new Object[] { paramString, Integer.valueOf(paramInt2) });
/* 1422 */       boolean bool = true;
/* 1423 */       if (recordSet.getCounts() > 0) {
/* 1424 */         msgEntity1.setMsgType((MessageType)JSONObject.parseObject(JSONObject.toJSONString(MessageType.WF_COMPLETED), MessageType.class));
/*      */       } else {
/* 1426 */         recordSet.executeQuery("select isremark AS isremark  from workflow_currentoperator where id = ?", new Object[] { Integer.valueOf(paramInt3) });
/* 1427 */         if (recordSet.next()) {
/* 1428 */           String str = recordSet.getString("isremark");
/* 1429 */           if ("2".equals(str)) {
/* 1430 */             msgEntity1.setMsgType((MessageType)JSONObject.parseObject(JSONObject.toJSONString(MessageType.WF_COMPLETED), MessageType.class));
/*      */           } else {
/* 1432 */             bool = false;
/*      */           } 
/*      */         } else {
/* 1435 */           msgEntity1.setMsgType((MessageType)JSONObject.parseObject(JSONObject.toJSONString(MessageType.WF_DRAW_BACK), MessageType.class));
/*      */         } 
/*      */       } 
/* 1438 */       if (bool) {
/* 1439 */         recordSet.executeQuery("select userid, id from workflow_currentoperator where id = ?", new Object[] { Integer.valueOf(paramInt2) });
/* 1440 */         addOperatorId(recordSet, msgEntity1);
/*      */       } 
/*      */       
/* 1443 */       MsgEntity msgEntity2 = getBaseInfo(paramString);
/* 1444 */       if (msgEntity2 == null) return null; 
/* 1445 */       msgEntity2.setMsgType((MessageType)JSONObject.parseObject(JSONObject.toJSONString(MessageType.WF_NEW_ARRIVAL), MessageType.class));
/*      */       
/* 1447 */       recordSet.executeQuery("select userid, id from workflow_currentoperator where id = ?", new Object[] { Integer.valueOf(paramInt1) });
/* 1448 */       addOperatorId(recordSet, msgEntity2);
/* 1449 */       if (bool) {
/* 1450 */         arrayList.add(msgEntity1);
/*      */       }
/* 1452 */       arrayList.add(msgEntity2);
/*      */     }
/* 1454 */     catch (Exception exception) {
/* 1455 */       writeLog(exception);
/*      */     } 
/* 1457 */     return arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public List<MsgEntity> workflowOverTimeMsg(int paramInt1, List<String> paramList, int paramInt2) {
/* 1470 */     ArrayList<MsgEntity> arrayList = new ArrayList();
/*      */     
/*      */     try {
/* 1473 */       MsgEntity msgEntity = getBaseInfo(String.valueOf(paramInt1));
/* 1474 */       if (msgEntity == null) return null; 
/* 1475 */       RecordSet recordSet = new RecordSet();
/* 1476 */       if (paramInt2 == 1 || paramInt2 == 2 || paramInt2 == 3 || paramInt2 == 4) {
/* 1477 */         MsgEntity msgEntity1 = msgEntity.clone();
/* 1478 */         msgEntity1.setMsgType(MessageType.WF_COMPLETED);
/* 1479 */         recordSet.executeQuery("select userid, id from workflow_currentoperator where requestid = ? and usertype = 0 and islasttimes = 1 and " + Util.getSubINClause(StringUtils.join(paramList, ","), "userid", "in"), new Object[] { Integer.valueOf(paramInt1) });
/* 1480 */         addOperatorId(recordSet, msgEntity1);
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */         
/* 1491 */         arrayList.add(msgEntity1);
/*      */       } else {
/* 1493 */         MsgEntity msgEntity1 = msgEntity.clone();
/* 1494 */         msgEntity1.setMsgType(MessageType.WF_NEW_ARRIVAL);
/* 1495 */         msgEntity1.setNoticeType(MsgNoticeType.NEW_ARRIVAL);
/* 1496 */         recordSet.executeQuery("select userid, id from workflow_currentoperator where requestid = ? and usertype = 0 and islasttimes = 1 and " + Util.getSubINClause(StringUtils.join(paramList, ","), "userid", "in"), new Object[] { Integer.valueOf(paramInt1) });
/* 1497 */         addOperatorId(recordSet, msgEntity1);
/* 1498 */         arrayList.add(msgEntity1);
/*      */       }
/*      */     
/* 1501 */     } catch (Exception exception) {
/* 1502 */       writeLog(exception);
/*      */     } 
/* 1504 */     return arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public List<MsgEntity> requestOverTimeNoticeMsg(int paramInt1, List<String> paramList, int paramInt2) {
/* 1517 */     ArrayList<MsgEntity> arrayList = new ArrayList();
/* 1518 */     if (paramList == null || paramList.size() <= 0) {
/* 1519 */       return arrayList;
/*      */     }
/*      */     try {
/* 1522 */       MsgEntity msgEntity = getBaseInfo(Util.null2String(Integer.valueOf(paramInt1)));
/* 1523 */       if (msgEntity == null) return null; 
/* 1524 */       msgEntity.setMsgType((paramInt2 == 0) ? MessageType.WF_WILL_TIMEOUT : MessageType.WF_ALREADY_TIMEOUT);
/* 1525 */       msgEntity.setNoticeType((paramInt2 == 0) ? MsgNoticeType.OVERING_TIME : MsgNoticeType.OVERED_TIME);
/*      */       
/* 1527 */       RecordSet recordSet = new RecordSet();
/* 1528 */       recordSet.executeQuery("select userid, id from workflow_currentoperator where usertype = 0 and requestid = ? and " + Util.getSubINClause(StringUtils.join(paramList, ","), "userid", "in"), new Object[] { Integer.valueOf(paramInt1) });
/* 1529 */       addOperatorId(recordSet, msgEntity);
/* 1530 */       arrayList.add(msgEntity);
/* 1531 */     } catch (Exception exception) {
/* 1532 */       writeLog(exception);
/*      */     } 
/* 1534 */     return arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public List<MsgEntity> requestWithdrawMsg(int paramInt1, List<String> paramList, int paramInt2) {
/* 1548 */     ArrayList<MsgEntity> arrayList = new ArrayList();
/*      */     try {
/* 1550 */       MsgEntity msgEntity = getBaseInfo(Util.null2String(Integer.valueOf(paramInt1)));
/* 1551 */       if (msgEntity == null) return null; 
/* 1552 */       msgEntity.setMsgType(MessageType.WF_WITHDRAW);
/* 1553 */       msgEntity.setNoticeType(MsgNoticeType.WF_REQUEST_WITHDRAW);
/*      */       
/* 1555 */       msgEntity.addAllUserId(paramList);
/*      */       
/* 1557 */       RecordSet recordSet = new RecordSet();
/* 1558 */       recordSet.executeQuery("select userid, id from workflow_currentoperator where usertype = 0 and requestid = ? and " + Util.getSubINClause(StringUtils.join(paramList, ","), "userid", "in"), new Object[] { Integer.valueOf(paramInt1) });
/* 1559 */       addOperatorId(recordSet, msgEntity);
/*      */       
/* 1561 */       ResourceComInfo resourceComInfo = new ResourceComInfo();
/* 1562 */       msgEntity.setDetailTitleParams("recaller", resourceComInfo.getLastname(paramInt2 + ""));
/* 1563 */       arrayList.add(msgEntity);
/* 1564 */     } catch (Exception exception) {
/* 1565 */       writeLog(exception);
/*      */     } 
/* 1567 */     return arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public List<MsgEntity> requestCompletedMsg(int paramInt, List<String> paramList, boolean paramBoolean) {
/* 1580 */     ArrayList<MsgEntity> arrayList = new ArrayList();
/*      */     try {
/* 1582 */       MsgEntity msgEntity = getBaseInfo(Util.null2String(Integer.valueOf(paramInt)));
/* 1583 */       if (msgEntity == null) return null; 
/* 1584 */       msgEntity.setMsgType(MessageType.WF_COMPLETED);
/* 1585 */       if (paramBoolean) msgEntity.setNoticeType(MsgNoticeType.COMPLETED);
/*      */       
/* 1587 */       msgEntity.addAllUserId(paramList);
/*      */       
/* 1589 */       RecordSet recordSet = new RecordSet();
/* 1590 */       recordSet.executeQuery("select userid, id from workflow_currentoperator where usertype = 0 and requestid = ? and " + Util.getSubINClause(StringUtils.join(paramList, ","), "userid", "in"), new Object[] { Integer.valueOf(paramInt) });
/* 1591 */       addOperatorId(recordSet, msgEntity);
/*      */       
/* 1593 */       arrayList.add(msgEntity);
/* 1594 */     } catch (Exception exception) {
/* 1595 */       writeLog(exception);
/*      */     } 
/* 1597 */     return arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public List<MsgEntity> requestFlowMsg(int paramInt, List<String> paramList, String paramString1, String paramString2) {
/* 1610 */     ArrayList<MsgEntity> arrayList = new ArrayList();
/*      */     try {
/* 1612 */       MsgEntity msgEntity = getBaseInfo(Util.null2String(Integer.valueOf(paramInt)));
/* 1613 */       if (msgEntity == null) return null; 
/* 1614 */       msgEntity.setMsgType(MessageType.WF_FLOW);
/* 1615 */       msgEntity.setNoticeType(MsgNoticeType.WF_REQUEST_FLOW);
/*      */       
/* 1617 */       msgEntity.addAllUserId(paramList);
/* 1618 */       msgEntity.setDetailTitleParams("current_node", paramString1);
/* 1619 */       msgEntity.setDetailId(paramString2);
/* 1620 */       arrayList.add(msgEntity);
/* 1621 */     } catch (Exception exception) {
/* 1622 */       writeLog(exception);
/*      */     } 
/* 1624 */     return arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public List<MsgEntity> requestRestoreMsg(int paramInt) {
/* 1635 */     ArrayList<MsgEntity> arrayList = new ArrayList();
/*      */ 
/*      */     
/*      */     try {
/* 1639 */       MsgEntity msgEntity1 = getBaseInfo(String.valueOf(paramInt));
/* 1640 */       if (msgEntity1 == null) return null;
/*      */       
/* 1642 */       msgEntity1.setMsgType((MessageType)JSONObject.parseObject(JSONObject.toJSONString(MessageType.WF_RESTORE_DONE), MessageType.class));
/* 1643 */       MsgEntity msgEntity2 = msgEntity1.clone();
/* 1644 */       msgEntity2.setMsgType((MessageType)JSONObject.parseObject(JSONObject.toJSONString(MessageType.WF_RESTORE_DOING), MessageType.class));
/*      */       
/* 1646 */       RecordSet recordSet = new RecordSet();
/* 1647 */       recordSet.executeQuery("select isremark,userid,id from workflow_currentoperator where requestid = ? and usertype = 0", new Object[] { Integer.valueOf(paramInt) });
/*      */       
/* 1649 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 1650 */       HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 1651 */       while (recordSet.next()) {
/* 1652 */         int i = Util.getIntValue(Util.null2String(recordSet.getString("isremark")));
/* 1653 */         String str1 = Util.null2String(recordSet.getString("userid"));
/* 1654 */         String str2 = Util.null2String(recordSet.getString("id"));
/* 1655 */         if (i == 2) {
/* 1656 */           msgEntity1.addUserId(str1);
/* 1657 */           List<String> list1 = (List)hashMap1.get(str1);
/* 1658 */           if (list1 == null) list1 = new ArrayList(); 
/* 1659 */           list1.add(str2);
/* 1660 */           hashMap1.put(str1, list1); continue;
/*      */         } 
/* 1662 */         msgEntity2.addUserId(str1);
/* 1663 */         List<String> list = (List)hashMap2.get(str1);
/* 1664 */         if (list == null) list = new ArrayList(); 
/* 1665 */         list.add(str2);
/* 1666 */         hashMap2.put(str1, list);
/*      */       } 
/*      */       
/* 1669 */       for (String str : hashMap1.keySet()) {
/* 1670 */         msgEntity1.addCurrentOperatorId(str, String.join(",", (Iterable<? extends CharSequence>)hashMap1.get(str)));
/*      */       }
/* 1672 */       for (String str : hashMap2.keySet()) {
/* 1673 */         msgEntity2.addCurrentOperatorId(str, String.join(",", (Iterable<? extends CharSequence>)hashMap2.get(str)));
/*      */       }
/*      */       
/* 1676 */       arrayList.add(msgEntity1);
/* 1677 */       arrayList.add(msgEntity2);
/* 1678 */     } catch (Exception exception) {
/* 1679 */       writeLog(exception);
/*      */     } 
/* 1681 */     return arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public List<MsgEntity> doingTransferMsgBefore(List<String> paramList, String paramString) {
/* 1692 */     ArrayList<MsgEntity> arrayList = new ArrayList();
/* 1693 */     if (Strings.isNullOrEmpty(paramString) || paramList == null) return arrayList; 
/*      */     try {
/* 1695 */       MessageType messageType = (MessageType)JSONObject.parseObject(JSONObject.toJSONString(MessageType.WF_DRAW_BACK), MessageType.class);
/* 1696 */       for (String str : paramList) {
/* 1697 */         MsgEntity msgEntity = getBaseInfo(String.valueOf(str));
/* 1698 */         msgEntity.setMsgType(messageType);
/* 1699 */         RecordSet recordSet = new RecordSet();
/* 1700 */         recordSet.executeQuery("select userid, id from workflow_currentoperator where requestid = ? and isRemark IN ('0','1','5','8','9','7','11') and userid = ? and usertype = 0 and isLastTimes=1", new Object[] { str, paramString });
/* 1701 */         addOperatorId(recordSet, msgEntity);
/* 1702 */         arrayList.add(msgEntity);
/*      */       } 
/* 1704 */     } catch (Exception exception) {
/* 1705 */       writeLog(exception);
/*      */     } 
/* 1707 */     return arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public List<MsgEntity> doingTransferMsgAfter(List<String> paramList, String paramString) {
/* 1717 */     ArrayList<MsgEntity> arrayList = new ArrayList();
/* 1718 */     if (Strings.isNullOrEmpty(paramString) || paramList == null) return arrayList; 
/*      */     try {
/* 1720 */       RecordSet recordSet = new RecordSet();
/*      */       
/* 1722 */       MessageType messageType1 = (MessageType)JSONObject.parseObject(JSONObject.toJSONString(MessageType.WF_NEW_ARRIVAL), MessageType.class);
/*      */       
/* 1724 */       MessageType messageType2 = (MessageType)JSONObject.parseObject(JSONObject.toJSONString(MessageType.WF_COPY), MessageType.class);
/* 1725 */       for (String str : paramList) {
/* 1726 */         MsgEntity msgEntity1 = getBaseInfo(String.valueOf(str));
/* 1727 */         msgEntity1.setMsgType(messageType1);
/* 1728 */         msgEntity1.setNoticeType(MsgNoticeType.NEW_ARRIVAL);
/* 1729 */         recordSet.executeQuery("select userid, id from workflow_currentoperator where requestid = ? and isRemark IN ('0','1','5','7','11') and userid = ? and usertype = 0 and isLastTimes=1", new Object[] { str, paramString });
/* 1730 */         addOperatorId(recordSet, msgEntity1);
/* 1731 */         arrayList.add(msgEntity1);
/*      */         
/* 1733 */         MsgEntity msgEntity2 = msgEntity1.clone();
/* 1734 */         msgEntity2.setMsgType(messageType2);
/* 1735 */         msgEntity2.setNoticeType(MsgNoticeType.CC);
/* 1736 */         recordSet.executeQuery("select userid, id from workflow_currentoperator where requestid = ? and isRemark IN ('8','9') and userid = ? and usertype = 0 and isLastTimes=1", new Object[] { str, paramString });
/* 1737 */         addOperatorId(recordSet, msgEntity2);
/* 1738 */         arrayList.add(msgEntity2);
/*      */       } 
/* 1740 */     } catch (Exception exception) {
/* 1741 */       writeLog(exception);
/*      */     } 
/* 1743 */     return arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public List<MsgEntity> doneTransferMsgBefore(List<String> paramList, String paramString) {
/* 1753 */     ArrayList<MsgEntity> arrayList = new ArrayList();
/* 1754 */     if (Strings.isNullOrEmpty(paramString) || paramList == null) return arrayList; 
/*      */     try {
/* 1756 */       RecordSet recordSet = new RecordSet();
/* 1757 */       MessageType messageType = (MessageType)JSONObject.parseObject(JSONObject.toJSONString(MessageType.WF_DRAW_BACK), MessageType.class);
/* 1758 */       for (String str : paramList) {
/* 1759 */         MsgEntity msgEntity = getBaseInfo(String.valueOf(str));
/* 1760 */         msgEntity.setMsgType(messageType);
/* 1761 */         recordSet.executeQuery("select userid, id from workflow_currentoperator where requestid = ? and isRemark IN ('2', '4') and userid = ? and usertype = 0 and isLastTimes = 1", new Object[] { str, paramString });
/* 1762 */         addOperatorId(recordSet, msgEntity);
/* 1763 */         arrayList.add(msgEntity);
/*      */       } 
/* 1765 */     } catch (Exception exception) {
/* 1766 */       writeLog(exception);
/*      */     } 
/* 1768 */     return arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public List<MsgEntity> doneTransferMsgAfter(List<String> paramList, String paramString) {
/* 1779 */     ArrayList<MsgEntity> arrayList = new ArrayList();
/* 1780 */     if (Strings.isNullOrEmpty(paramString) || paramList == null) return arrayList; 
/*      */     try {
/* 1782 */       RecordSet recordSet = new RecordSet();
/* 1783 */       MessageType messageType = (MessageType)JSONObject.parseObject(JSONObject.toJSONString(MessageType.WF_COMPLETED), MessageType.class);
/* 1784 */       for (String str : paramList) {
/* 1785 */         MsgEntity msgEntity = getBaseInfo(String.valueOf(str));
/* 1786 */         msgEntity.setMsgType(messageType);
/* 1787 */         msgEntity.setNoticeType(MsgNoticeType.NEW_ARRIVAL);
/* 1788 */         recordSet.executeQuery("select userid, id from workflow_currentoperator where requestid = ? and isRemark IN ('2', '4') and userid = ? and usertype = 0 and isLastTimes = 1", new Object[] { str, paramString });
/* 1789 */         addOperatorId(recordSet, msgEntity);
/* 1790 */         arrayList.add(msgEntity);
/*      */       } 
/* 1792 */     } catch (Exception exception) {
/* 1793 */       writeLog(exception);
/*      */     } 
/* 1795 */     return arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public List<MsgEntity> deleteMsgByUserId(List<String> paramList, String paramString) {
/* 1806 */     ArrayList<MsgEntity> arrayList = new ArrayList();
/*      */     try {
/* 1808 */       for (String str : paramList) {
/* 1809 */         MsgEntity msgEntity = new MsgEntity();
/* 1810 */         msgEntity.setDetailId(str);
/* 1811 */         msgEntity.setMsgType(MessageType.WF_DRAW_BACK);
/* 1812 */         msgEntity.addUserId(paramString);
/* 1813 */         arrayList.add(msgEntity);
/*      */       } 
/* 1815 */     } catch (Exception exception) {
/* 1816 */       writeLog(exception);
/*      */     } 
/* 1818 */     return arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public List<MsgEntity> copyByResourceMsg(String paramString1, String paramString2) {
/* 1830 */     ArrayList<MsgEntity> arrayList = new ArrayList();
/* 1831 */     if (Strings.isNullOrEmpty(paramString1) || Strings.isNullOrEmpty(paramString2)) return arrayList; 
/*      */     try {
/* 1833 */       MessageType messageType = (MessageType)JSONObject.parseObject(JSONObject.toJSONString(MessageType.WF_COMPLETED), MessageType.class);
/*      */       
/* 1835 */       RecordSet recordSet = new RecordSet();
/* 1836 */       String[] arrayOfString = paramString1.split(",");
/* 1837 */       for (String str : arrayOfString) {
/* 1838 */         MsgEntity msgEntity = getBaseInfo(String.valueOf(str));
/* 1839 */         if (msgEntity != null)
/* 1840 */         { msgEntity.setMsgType(messageType);
/* 1841 */           msgEntity.setNoticeType(MsgNoticeType.NEW_ARRIVAL);
/* 1842 */           recordSet.executeQuery("select userid, id from workflow_currentoperator where islasttimes = 1 and userid = ? and usertype = 0 and isRemark in ('2', '4') and requestid= ?", new Object[] { str, paramString2 });
/* 1843 */           addOperatorId(recordSet, msgEntity);
/* 1844 */           arrayList.add(msgEntity); } 
/*      */       } 
/* 1846 */     } catch (Exception exception) {
/* 1847 */       writeLog("权限复制消息异常,requestId=" + paramString1, exception);
/*      */     } 
/* 1849 */     return arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public List<MsgEntity> requestSubmitErrorMsg(String paramString, RequestOperationResultBean paramRequestOperationResultBean, int paramInt) {
/* 1861 */     ArrayList<MsgEntity> arrayList = new ArrayList();
/*      */     
/*      */     try {
/* 1864 */       MsgEntity msgEntity = getBaseInfo(paramString);
/* 1865 */       if (msgEntity == null) return null; 
/* 1866 */       msgEntity.setMsgType(MessageType.WF_ERROR_REMIND);
/* 1867 */       msgEntity.setNoticeType(MsgNoticeType.ERROR);
/* 1868 */       msgEntity.addUserId(String.valueOf(paramInt));
/* 1869 */       arrayList.add(msgEntity);
/*      */     }
/* 1871 */     catch (Exception exception) {
/* 1872 */       writeLog(exception);
/*      */     } 
/* 1874 */     return arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public List<MsgEntity> requestCommunicationMsg(String paramString1, List<String> paramList, String paramString2) {
/* 1886 */     ArrayList<MsgEntity> arrayList = new ArrayList();
/* 1887 */     if (paramList == null || paramList.isEmpty()) return arrayList;
/*      */ 
/*      */     
/*      */     try {
/* 1891 */       MsgEntity msgEntity = getBaseInfo(paramString1);
/* 1892 */       if (msgEntity == null) return null; 
/* 1893 */       msgEntity.setContext(paramString2);
/* 1894 */       msgEntity.setMsgType(MessageType.WF_EXCANGE_REMIND);
/* 1895 */       msgEntity.setNoticeType(MsgNoticeType.NEW_OPTION);
/* 1896 */       msgEntity.addAllUserId(paramList);
/* 1897 */       arrayList.add(msgEntity);
/* 1898 */     } catch (Exception exception) {
/* 1899 */       writeLog(exception);
/*      */     } 
/* 1901 */     return arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public List<MsgEntity> triggerSubRequestMsg(String paramString1, String paramString2, int paramInt) {
/* 1913 */     ArrayList<MsgEntity> arrayList = new ArrayList();
/* 1914 */     if (Strings.isNullOrEmpty(paramString2)) return arrayList;
/*      */     
/*      */     try {
/* 1917 */       if (paramInt == 1) {
/* 1918 */         MsgEntity msgEntity = getBaseInfo(paramString1);
/* 1919 */         if (msgEntity == null) return null; 
/* 1920 */         msgEntity.setMsgType(MessageType.WF_NEW_ARRIVAL);
/* 1921 */         msgEntity.setNoticeType(MsgNoticeType.NEW_ARRIVAL);
/* 1922 */         RecordSet recordSet = new RecordSet();
/*      */ 
/*      */         
/* 1925 */         recordSet.executeQuery("select agentorbyagentid from workflow_currentoperator where requestid=?  and islasttimes = 1 and isremark='2' and userid = ? and agenttype=1", new Object[] { paramString1, paramString2 });
/* 1926 */         if (recordSet.next()) {
/* 1927 */           paramString2 = Util.null2String(recordSet.getString("agentorbyagentid"));
/*      */         }
/*      */         
/* 1930 */         recordSet.executeQuery("select userid, id from workflow_currentoperator where requestid = ? and islasttimes = 1 and userid = ? and usertype = 0", new Object[] { paramString1, paramString2 });
/* 1931 */         addOperatorId(recordSet, msgEntity);
/* 1932 */         arrayList.add(msgEntity);
/*      */       } 
/* 1934 */     } catch (Exception exception) {
/* 1935 */       writeLog(exception);
/*      */     } 
/* 1937 */     return arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public List<MsgEntity> createRequestMsg(int paramInt1, int paramInt2) {
/* 1948 */     ArrayList<MsgEntity> arrayList = new ArrayList();
/* 1949 */     if (paramInt1 == -1) {
/* 1950 */       writeLog("createRequestMsg requestId=" + paramInt1 + ",createrId=" + paramInt2);
/* 1951 */       return null;
/*      */     } 
/*      */     try {
/* 1954 */       MsgEntity msgEntity = getBaseInfo(String.valueOf(paramInt1));
/* 1955 */       if (msgEntity == null) return null; 
/* 1956 */       msgEntity.setMsgType(MessageType.WF_NEW_ARRIVAL);
/* 1957 */       RecordSet recordSet = new RecordSet();
/* 1958 */       recordSet.executeQuery("select userid, id from workflow_currentoperator where requestid = ? and islasttimes = 1 and userid = ? and usertype = 0", new Object[] { Integer.valueOf(paramInt1), Integer.valueOf(paramInt2) });
/* 1959 */       addOperatorId(recordSet, msgEntity);
/* 1960 */       arrayList.add(msgEntity);
/*      */     }
/* 1962 */     catch (Exception exception) {
/* 1963 */       writeLog(exception);
/*      */     } 
/* 1965 */     return arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public List<MsgEntity> rejectNoticeMsg(String paramString1, String paramString2) {
/* 1976 */     RecordSet recordSet = new RecordSet();
/* 1977 */     ArrayList<MsgEntity> arrayList = new ArrayList();
/*      */ 
/*      */     
/* 1980 */     recordSet.executeQuery("select workflowid,currentnodeid,lastnodeid  from  workflow_requestbase where requestid = ?", new Object[] { paramString1 });
/* 1981 */     if (recordSet.next()) {
/* 1982 */       String str1 = Util.null2String(recordSet.getString(1));
/* 1983 */       String str2 = Util.null2String(recordSet.getString(2));
/* 1984 */       String str3 = Util.null2String(recordSet.getString(3));
/*      */       
/* 1986 */       boolean bool = FreeNodeBiz.isFreeNode(Util.getIntValue(str3));
/* 1987 */       if (bool) {
/* 1988 */         recordSet.executeQuery(" select isrejectremind,ischangrejectnode from workflow_freenode where workflowid  = ? and id = ?", new Object[] { str1, str3 });
/*      */       } else {
/* 1990 */         recordSet.executeQuery(" select isrejectremind,ischangrejectnode from workflow_flownode where workflowid  = ? and nodeid = ?", new Object[] { str1, str3 });
/*      */       } 
/* 1992 */       recordSet.next();
/*      */       
/* 1994 */       String str4 = Util.null2String(recordSet.getString(1));
/* 1995 */       String str5 = Util.null2String(recordSet.getString(2));
/*      */       
/* 1997 */       if (!"1".equals(str4)) return arrayList;
/*      */       
/* 1999 */       if (!"1".equals(str5)) {
/* 2000 */         paramString2 = str2;
/*      */       }
/*      */       
/*      */       try {
/* 2004 */         MsgEntity msgEntity = getBaseInfo(paramString1);
/* 2005 */         if (msgEntity == null) return arrayList; 
/* 2006 */         msgEntity.setMsgType(MessageType.WF_RETURN_REMIND);
/* 2007 */         msgEntity.setNoticeType(MsgNoticeType.WF_RETURN_REMIND);
/*      */         
/* 2009 */         String str = "";
/* 2010 */         if (!"1".equals(str5)) {
/* 2011 */           str = str + " and isremark = 0 and isbereject = 1 ";
/*      */         }
/* 2013 */         recordSet.executeQuery("select distinct userid from workflow_currentoperator where usertype = 0 and requestid = ? " + str + " and " + Util.getSubINClause(paramString2, "nodeid", "in"), new Object[] { paramString1 });
/* 2014 */         while (recordSet.next()) {
/* 2015 */           msgEntity.addUserId(Util.null2String(recordSet.getString(1)));
/*      */         }
/* 2017 */         arrayList.add(msgEntity);
/* 2018 */       } catch (Exception exception) {
/* 2019 */         writeLog(exception);
/*      */       } 
/*      */     } 
/*      */     
/* 2023 */     return arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void updateBizState(List<MessageBean> paramList) {
/*      */     try {
/* 2035 */       for (int i = paramList.size() - 1; i >= 0; i--) {
/* 2036 */         MessageBean messageBean = paramList.get(i);
/*      */         
/* 2038 */         if (null != messageBean && messageBean.getMessageType().getCode() == MessageType.WF_CONCERN.getCode()) {
/* 2039 */           paramList.remove(messageBean);
/*      */         }
/*      */       } 
/* 2042 */       Util_Message.updateBizState(paramList);
/* 2043 */     } catch (Exception exception) {
/* 2044 */       writeLog("消息中心修改流程业务数据状态异常:", exception);
/*      */     } 
/*      */   }
/*      */   
/*      */   private void newListDoingSubTableHandle(JSONObject paramJSONObject) {
/* 2049 */     JSONArray jSONArray = paramJSONObject.getJSONArray("modifyData");
/* 2050 */     HashSet<String> hashSet = new HashSet(); byte b; int i;
/* 2051 */     for (b = 0, i = jSONArray.size(); b < i; b++) {
/* 2052 */       JSONObject jSONObject = jSONArray.getJSONObject(b);
/* 2053 */       String str = jSONObject.getString("fieldName");
/* 2054 */       if ("islasttimes".equals(str)) {
/* 2055 */         hashSet.add(jSONObject.getString("entityId"));
/*      */       }
/*      */     } 
/* 2058 */     if (hashSet.size() > 0) {
/* 2059 */       String str = String.join(",", (Iterable)hashSet);
/* 2060 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 2061 */       RecordSet recordSet = new RecordSet();
/* 2062 */       recordSet.executeQuery("select userid,id from workflow_currentoperator where usertype = 0 and " + Util.getSubINClause(str, "id", "IN"), new Object[0]);
/* 2063 */       while (recordSet.next()) {
/* 2064 */         String str1 = Util.null2String(recordSet.getString("userid"));
/* 2065 */         if (!RequestSubTableBiz.isQueryRequestFromNewTable(Util.getIntValue(str1)))
/* 2066 */           continue;  String str2 = Util.null2String(recordSet.getString("id"));
/* 2067 */         Set<String> set = (Set)hashMap.get(str1);
/* 2068 */         if (set == null) {
/* 2069 */           set = new HashSet();
/* 2070 */           hashMap.put(str1, set);
/*      */         } 
/* 2072 */         set.add(str2);
/*      */       } 
/* 2074 */       if (hashMap.size() > 0) {
/* 2075 */         log.info("~~newListDoingSubTableHandle~~" + JSONObject.toJSONString(hashMap));
/* 2076 */         RequestSubTableBiz requestSubTableBiz = new RequestSubTableBiz();
/* 2077 */         requestSubTableBiz.asyncCurrentOperatorData(hashMap);
/*      */       } 
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private void groupDetialUserIds(MsgEntity paramMsgEntity, JSONObject paramJSONObject, String paramString) {
/* 2088 */     JSONArray jSONArray = paramJSONObject.getJSONArray("modifyData");
/* 2089 */     HashSet<String> hashSet = new HashSet(); byte b; int i;
/* 2090 */     for (b = 0, i = jSONArray.size(); b < i; b++) {
/* 2091 */       JSONObject jSONObject = jSONArray.getJSONObject(b);
/* 2092 */       String str = jSONObject.getString("fieldName");
/* 2093 */       if ("isremark".equals(str)) {
/* 2094 */         hashSet.add(jSONObject.getString("entityId"));
/*      */       }
/*      */     } 
/*      */     
/* 2098 */     if (hashSet.isEmpty())
/*      */       return; 
/* 2100 */     String str1 = String.join(",", (Iterable)hashSet);
/* 2101 */     RecordSet recordSet = new RecordSet();
/* 2102 */     String str2 = "select userid,id from workflow_currentoperator where usertype = 0 and userid != ? and" + Util.getSubINClause(str1, "id", "IN");
/* 2103 */     recordSet.executeQuery(str2, new Object[] { paramString });
/* 2104 */     addOperatorId(recordSet, paramMsgEntity);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private MsgEntity getMsgEntity(MsgEntity paramMsgEntity, JSONArray paramJSONArray, MessageType paramMessageType) {
/* 2114 */     MsgEntity msgEntity = paramMsgEntity.clone();
/* 2115 */     msgEntity.setMsgType(paramMessageType);
/*      */     
/* 2117 */     if (paramJSONArray != null) {
/* 2118 */       ArrayList<String> arrayList = new ArrayList(); byte b; int i;
/* 2119 */       for (b = 0, i = paramJSONArray.size(); b < i; b++) {
/* 2120 */         JSONObject jSONObject = paramJSONArray.getJSONObject(b);
/* 2121 */         String str1 = jSONObject.getString("fieldName");
/* 2122 */         if ("isremark".equals(str1) || "takisremark".equals(str1)) {
/* 2123 */           String str2 = jSONObject.getString("entityId");
/* 2124 */           arrayList.add(str2);
/*      */         } 
/*      */       } 
/* 2127 */       String str = StringUtils.join(arrayList, ",");
/* 2128 */       if (Strings.isNullOrEmpty(str)) return msgEntity; 
/* 2129 */       RecordSet recordSet = new RecordSet();
/* 2130 */       recordSet.executeQuery("select userid,id from workflow_currentoperator where usertype = 0 and " + Util.getSubINClause(str, "id", "in"), new Object[0]);
/* 2131 */       addOperatorId(recordSet, msgEntity);
/*      */     } 
/* 2133 */     return msgEntity;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private MsgEntity getBaseInfo(String paramString) {
/* 2143 */     MsgEntity msgEntity = null;
/*      */     
/* 2145 */     RecordSet recordSet = new RecordSet();
/* 2146 */     recordSet.executeQuery("select requestname,requestnamenew,workflowid,creater,createdate,createtime from workflow_requestbase where requestid = ? and (deleted = 0 or deleted is null)", new Object[] { paramString });
/* 2147 */     if (recordSet.next()) {
/* 2148 */       msgEntity = new MsgEntity();
/* 2149 */       String str1 = Util.null2String(recordSet.getString("requestname"));
/* 2150 */       String str2 = Util.null2String(recordSet.getString("requestnamenew"));
/* 2151 */       String str3 = Util.null2String(recordSet.getString("workflowid"));
/* 2152 */       String str4 = Util.null2String(recordSet.getString("creater"));
/* 2153 */       String str5 = Util.null2String(recordSet.getString("createdate"));
/* 2154 */       String str6 = Util.null2String(recordSet.getString("createtime"));
/*      */       
/* 2156 */       msgEntity.setDetailId(paramString);
/* 2157 */       msgEntity.setDetailName(str1);
/* 2158 */       msgEntity.setDetailTitle(Strings.isNullOrEmpty(str2) ? str1 : str2);
/* 2159 */       msgEntity.setDetailBaseId(str3);
/* 2160 */       msgEntity.setCreator(str4);
/* 2161 */       msgEntity.setCreateDate(str5);
/* 2162 */       msgEntity.setCreateTime(str6);
/*      */       
/* 2164 */       recordSet.executeQuery("select wfb.workflowname,wft.id,wft.typename from workflow_base wfb,workflow_type wft where wfb.id = ? and wfb.workflowtype = wft.id", new Object[] { str3 });
/* 2165 */       if (recordSet.next()) {
/* 2166 */         msgEntity.setDetailBaseName(Util.null2String(recordSet.getString("workflowname")));
/* 2167 */         msgEntity.setDetailTypeId(Util.null2String(recordSet.getString("id")));
/* 2168 */         msgEntity.setDetailTypeName(Util.null2String(recordSet.getString("typename")));
/*      */       } 
/* 2170 */       SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
/* 2171 */       msgEntity.setOperaterDate(simpleDateFormat.format(new Date()));
/*      */     } 
/*      */     
/* 2174 */     return msgEntity;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private MsgEntity addOperatorId(RecordSet paramRecordSet, MsgEntity paramMsgEntity) {
/* 2184 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*      */     
/* 2186 */     while (paramRecordSet.next()) {
/* 2187 */       String str1 = Util.null2String(paramRecordSet.getString(1));
/* 2188 */       String str2 = Util.null2String(paramRecordSet.getString(2));
/* 2189 */       String str3 = Util.null2String(paramRecordSet.getString(3));
/* 2190 */       paramMsgEntity.addUserId(str1);
/* 2191 */       paramMsgEntity.addCurrentOperatorNode(str2, str3);
/*      */       
/* 2193 */       List<String> list = (List)hashMap.get(str1);
/* 2194 */       if (list == null) {
/* 2195 */         list = new ArrayList();
/* 2196 */         list.add(str2);
/* 2197 */         hashMap.put(str1, list); continue;
/*      */       } 
/* 2199 */       list.add(str2);
/*      */     } 
/*      */     
/* 2202 */     for (String str : hashMap.keySet()) {
/* 2203 */       paramMsgEntity.addCurrentOperatorId(str, String.join(",", (Iterable<? extends CharSequence>)hashMap.get(str)));
/*      */     }
/*      */     
/* 2206 */     return paramMsgEntity;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private boolean isArchived(RecordSet paramRecordSet, String paramString) {
/* 2215 */     boolean bool = false;
/* 2216 */     paramRecordSet.executeQuery("select mainrequestid,requestid,requestname,requestlevel,mainrequestid,creater,creatertype,createdate,createtime,workflowId,currentstatus,currentnodeid,currentnodetype,status,remindTypes,docids,crmids,prjids,cptids , lastnodeid  from workflow_requestbase where requestid=?", new Object[] { paramString });
/* 2217 */     paramRecordSet.next();
/* 2218 */     String str = Util.null2String(paramRecordSet.getString("currentnodetype"));
/* 2219 */     bool = "3".equals(str);
/* 2220 */     return bool;
/*      */   }
/*      */   
/*      */   public boolean isNextNodeisAutoApprove() {
/* 2224 */     return this.nextNodeisAutoApprove;
/*      */   }
/*      */   
/*      */   public void setNextNodeisAutoApprove(boolean paramBoolean) {
/* 2228 */     this.nextNodeisAutoApprove = paramBoolean;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private MsgEntity beAgentAndOtherMsg(RecordSet paramRecordSet, MsgEntity paramMsgEntity, String paramString1, String paramString2, String paramString3) {
/* 2241 */     MsgEntity msgEntity = paramMsgEntity.clone();
/* 2242 */     msgEntity.setMsgType(MessageType.WF_COMPLETED);
/* 2243 */     msgEntity.setOperatorId(paramString2);
/*      */ 
/*      */     
/* 2246 */     paramRecordSet.executeQuery("select userid,id from workflow_currentoperator a,workflow_flownode b where requestid = ?  and b.nodetype = 0 and  b.nodeid = ? and a.nodeid = b.nodeid and a.agenttype=1 and a.isremark = 2", new Object[] { paramMsgEntity
/* 2247 */           .getDetailId(), paramString3 });
/* 2248 */     addOperatorId(paramRecordSet, msgEntity);
/*      */ 
/*      */     
/* 2251 */     if (!Strings.isNullOrEmpty(paramString1)) {
/* 2252 */       paramRecordSet.executeQuery("select userid,id from workflow_currentoperator where isremark in (2,4) and " + Util.getSubINClause(paramString1, "id", "in"), new Object[0]);
/* 2253 */       addOperatorId(paramRecordSet, msgEntity);
/*      */     } 
/* 2255 */     if (!msgEntity.getUserId().isEmpty()) return msgEntity; 
/* 2256 */     return null;
/*      */   }
/*      */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/RequestOperationMsgManager.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */