/*     */ package weaver.workflow.request;
/*     */ 
/*     */ import weaver.conn.ConnStatement;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class RequestFieldManager
/*     */   extends BaseBean
/*     */ {
/*     */   private RecordSet statement;
/*     */   private int requestid;
/*     */   private int wfid;
/*     */   
/*     */   public void resetParameter() {
/*  31 */     this.requestid = 0;
/*  32 */     this.wfid = 0;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setRequestid(int paramInt) {
/*  39 */     this.requestid = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getRequestid() throws Exception {
/*  47 */     return this.requestid;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setWfid(int paramInt) {
/*  54 */     this.wfid = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void insertRequestField(String paramString) throws Exception {
/*  63 */     String str = "";
/*  64 */     str = str + "insert into " + paramString + "(requestid) values(" + this.requestid + ")";
/*     */     
/*  66 */     ConnStatement connStatement = new ConnStatement();
/*     */     try {
/*  68 */       connStatement.setStatementSql(str);
/*  69 */       connStatement.executeQuery();
/*     */     }
/*  71 */     catch (Exception exception) {
/*  72 */       writeLog(exception);
/*  73 */       throw exception;
/*     */     } finally {
/*     */       
/*  76 */       try { connStatement.close(); } catch (Exception exception) {}
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void updateRequestField(String[] paramArrayOfString1, String paramString, String[] paramArrayOfString2) throws Exception {
/*  87 */     String str = "";
/*  88 */     str = "update " + paramString + " set ";
/*  89 */     for (byte b = 0; b < paramArrayOfString1.length - 1; b++) {
/*  90 */       str = str + paramArrayOfString1[b] + "='" + paramArrayOfString2[b] + "',";
/*     */     }
/*  92 */     str = str + paramArrayOfString1[paramArrayOfString1.length - 1] + "='" + paramArrayOfString2[paramArrayOfString2.length - 1] + "' where requestid=" + this.requestid;
/*     */     
/*  94 */     ConnStatement connStatement = new ConnStatement();
/*     */     try {
/*  96 */       connStatement.setStatementSql(str);
/*  97 */       writeLog(str);
/*  98 */       connStatement.executeQuery();
/*     */     }
/* 100 */     catch (Exception exception) {
/* 101 */       writeLog(exception);
/* 102 */       throw exception;
/*     */     } finally {
/*     */       
/* 105 */       try { connStatement.close(); } catch (Exception exception) {}
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void selectRequestFieldValue(String paramString, int paramInt) throws Exception {
/* 115 */     String str = "";
/* 116 */     str = "select * from " + paramString + " where requestid=" + paramInt;
/* 117 */     this.statement = new RecordSet();
/*     */     try {
/* 119 */       this.statement.executeSql(str);
/* 120 */       this.statement.next();
/*     */     
/*     */     }
/* 123 */     catch (Exception exception) {
/* 124 */       writeLog(exception);
/* 125 */       throw exception;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void closeStatement() {}
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getFieldValue(String paramString) throws Exception {
/* 142 */     return Util.null2String(this.statement.getString(paramString));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getNodeStatus(String paramString, int paramInt) throws Exception {
/* 152 */     String str = "";
/*     */     
/* 154 */     if (paramString.equals("0") && paramInt == 1) str = "new"; 
/* 155 */     if (paramString.equals("0") && paramInt == 2) str = "open"; 
/* 156 */     if (paramString.equals("1") && paramInt == 1) str = "open"; 
/* 157 */     if (paramString.equals("1") && paramInt == 2) str = "approved"; 
/* 158 */     if (paramString.equals("1") && paramInt == 3) str = "rejected"; 
/* 159 */     if (paramString.equals("2") && paramInt == 1) str = "approved"; 
/* 160 */     if (paramString.equals("2") && paramInt == 2) str = "realized"; 
/* 161 */     if (paramString.equals("2") && paramInt == 3) str = "rejected"; 
/* 162 */     if (paramString.equals("3") && paramInt == 1) str = "realized"; 
/* 163 */     if (paramString.equals("3") && paramInt == 2) str = "processed"; 
/* 164 */     return str;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/RequestFieldManager.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */