/*     */ package weaver.workflow.request;
/*     */ 
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class RequestUseTempletManager
/*     */   extends BaseBean
/*     */ {
/*     */   public boolean ifHasUseTempletSucceed(int paramInt) {
/*  32 */     boolean bool = true;
/*     */ 
/*     */     
/*  35 */     int i = 0;
/*  36 */     int j = 0;
/*  37 */     int k = 0;
/*  38 */     String str1 = "0";
/*  39 */     int m = 0;
/*  40 */     int n = -1;
/*  41 */     RecordSet recordSet = new RecordSet();
/*  42 */     recordSet.executeSql("select workflowId,currentNodeId from workflow_Requestbase where requestid=" + paramInt);
/*  43 */     if (recordSet.next()) {
/*  44 */       i = Util.getIntValue(recordSet.getString("workflowId"), 0);
/*  45 */       j = Util.getIntValue(recordSet.getString("currentNodeId"), 0);
/*     */     } 
/*     */     
/*  48 */     recordSet.executeSql("select formId,isBill from workflow_base where id=" + i);
/*  49 */     if (recordSet.next()) {
/*  50 */       k = Util.getIntValue(recordSet.getString("formId"), 0);
/*  51 */       str1 = Util.null2String(recordSet.getString("isBill"));
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  58 */     recordSet.executeSql("select flowDocField,useTempletNode from workflow_createdoc  where status='1' and workflowId=" + i);
/*  59 */     if (recordSet.next()) {
/*  60 */       m = Util.getIntValue(recordSet.getString("flowDocField"), 0);
/*  61 */       n = Util.getIntValue(recordSet.getString("useTempletNode"), -1);
/*     */     } 
/*     */     
/*  64 */     if (j != n) {
/*  65 */       return bool;
/*     */     }
/*     */ 
/*     */     
/*  69 */     String str2 = "";
/*  70 */     int i1 = 0;
/*  71 */     String str3 = "";
/*  72 */     String str4 = "workflow_form";
/*  73 */     String str5 = "";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  79 */     if ("0".equals(str1)) {
/*  80 */       str5 = " select fieldName from workflow_formdict where id=" + m;
/*     */     } else {
/*  82 */       recordSet.executeSql(" select tableName from workflow_bill where id=" + k);
/*  83 */       if (recordSet.next()) {
/*  84 */         str4 = Util.null2String(recordSet.getString("tableName"));
/*     */       }
/*  86 */       str5 = " select fieldName from workflow_billfield where (viewtype is null or viewtype<>1) and id= " + m;
/*     */     } 
/*  88 */     recordSet.executeSql(str5);
/*  89 */     if (recordSet.next()) {
/*  90 */       str2 = Util.null2String(recordSet.getString("fieldName"));
/*     */     }
/*  92 */     if (str2.trim().equals("") || str4.trim().equals("")) {
/*  93 */       return bool;
/*     */     }
/*     */ 
/*     */     
/*  97 */     recordSet.executeSql("select " + str2 + " from " + str4 + " where requestid=" + paramInt);
/*  98 */     if (recordSet.next()) {
/*  99 */       i1 = Util.getIntValue(recordSet.getString(1), 0);
/*     */     }
/*     */     
/* 102 */     recordSet.executeSql("select hasUsedTemplet from DocDetail where id=" + i1);
/* 103 */     if (recordSet.next()) {
/* 104 */       str3 = Util.null2String(recordSet.getString("hasUsedTemplet"));
/*     */     }
/*     */     
/* 107 */     if (!"1".equals(str3)) {
/* 108 */       bool = false;
/*     */     }
/* 110 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean ifIsUseTempletNode(int paramInt1, int paramInt2, String paramString) {
/* 119 */     boolean bool = false;
/*     */ 
/*     */     
/* 122 */     int i = 0;
/* 123 */     int j = 0;
/* 124 */     int k = -1;
/*     */     
/* 126 */     WFLinkInfo wFLinkInfo = new WFLinkInfo();
/* 127 */     int m = wFLinkInfo.getCurrentNodeid(paramInt1, paramInt2, Util.getIntValue(paramString, 1));
/* 128 */     RecordSet recordSet = new RecordSet();
/* 129 */     recordSet.executeSql("select workflowId,currentNodeId from workflow_Requestbase where requestid=" + paramInt1);
/* 130 */     if (recordSet.next()) {
/* 131 */       i = Util.getIntValue(recordSet.getString("workflowId"), 0);
/* 132 */       j = Util.getIntValue(recordSet.getString("currentNodeId"), 0);
/*     */     } 
/*     */     
/* 135 */     recordSet.executeSql("select flowDocField,useTempletNode from workflow_createdoc  where status='1' and workflowId=" + i);
/* 136 */     if (recordSet.next()) {
/* 137 */       k = Util.getIntValue(recordSet.getString("useTempletNode"), -1);
/*     */     }
/*     */     
/* 140 */     if (m == k && m > 0 && m == j) {
/* 141 */       bool = true;
/*     */     }
/*     */     
/* 144 */     return bool;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/RequestUseTempletManager.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */