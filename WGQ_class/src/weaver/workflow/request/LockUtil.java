/*     */ package weaver.workflow.request;
/*     */ 
/*     */ import com.engine.workflow.constant.requestForm.PromptType;
/*     */ import java.text.ParseException;
/*     */ import java.text.SimpleDateFormat;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Date;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.ThreadVarLanguage;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class LockUtil
/*     */ {
/*     */   public List<String> getAllNodes(int paramInt1, int paramInt2) {
/*  27 */     WFLinkInfo wFLinkInfo = new WFLinkInfo();
/*  28 */     int i = wFLinkInfo.getNodeAttribute(paramInt2);
/*  29 */     ArrayList<String> arrayList = new ArrayList();
/*  30 */     if (i == 2) {
/*  31 */       int j = wFLinkInfo.getStartNodeidByEndNodeid(paramInt1, paramInt2);
/*  32 */       ArrayList<String> arrayList1 = wFLinkInfo.getBranchNodesByEach(paramInt1, j);
/*  33 */       for (String str : arrayList1) {
/*  34 */         if (!"".equals(str)) {
/*  35 */           ArrayList arrayList2 = Util.TokenizerString(str, ",");
/*  36 */           arrayList.addAll(arrayList2);
/*     */         } 
/*     */       } 
/*     */     } else {
/*  40 */       arrayList.add(paramInt2 + "");
/*     */     } 
/*     */     
/*  43 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getAllNodeids(int paramInt1, int paramInt2) {
/*  53 */     String str = "";
/*  54 */     List<String> list = getAllNodes(paramInt1, paramInt2);
/*  55 */     for (String str1 : list)
/*  56 */       str = str + "," + str1; 
/*  57 */     if (!"".equals(str))
/*  58 */       str = str.substring(1); 
/*  59 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean isNeedCheckLock(int paramInt1, int paramInt2, int paramInt3, int paramInt4, int paramInt5, int paramInt6) {
/*  69 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public synchronized boolean updateLockInfo(String paramString1, String paramString2, String paramString3, String paramString4) {
/* 112 */     RecordSet recordSet = new RecordSet();
/* 113 */     boolean bool = checkEffictive(paramString1, paramString2, paramString3, paramString4);
/* 114 */     if (bool) {
/* 115 */       Date date = new Date();
/* 116 */       recordSet.execute("update workflow_requestlock set lockdate = '" + getDate(date) + "',locktime = '" + getTime(date) + "' where requestid = " + paramString1 + " and nodeid in (" + getAllNodeids(getWfid(paramString1), Util.getIntValue(paramString2)) + ") and userid = " + paramString3 + " and timestamp = " + paramString4 + " and islock = 1");
/*     */     } 
/*     */     
/* 119 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public synchronized void lockRequest(String paramString1, String paramString2, String paramString3, String paramString4) {
/* 131 */     RecordSet recordSet = new RecordSet();
/* 132 */     Date date = new Date();
/* 133 */     recordSet.execute("select 1 from workflow_requestlock where requestid = " + paramString1 + " and nodeid in (" + getAllNodeids(getWfid(paramString1), Util.getIntValue(paramString2)) + ")");
/* 134 */     if (recordSet.next()) {
/* 135 */       recordSet.execute("update workflow_requestlock set lockdate = '" + getDate(date) + "',locktime = '" + getTime(date) + "',islock = 1 ,userid = " + paramString3 + ",timestamp = " + paramString4 + " where requestid = " + paramString1 + " and nodeid in (" + getAllNodeids(getWfid(paramString1), Util.getIntValue(paramString2)) + ")");
/*     */     } else {
/* 137 */       recordSet.execute("insert into workflow_requestlock(requestid,nodeid,userid,islock,lockdate,locktime,timestamp) values (" + paramString1 + "," + paramString2 + "," + paramString3 + ",1,'" + getDate(date) + "','" + getTime(date) + "'," + paramString4 + ")");
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public LockDTO isLocked(String paramString1, String paramString2) {
/* 148 */     LockDTO lockDTO = new LockDTO();
/* 149 */     RecordSet recordSet = new RecordSet();
/* 150 */     recordSet.execute("select * from workflow_requestlock where requestid = " + paramString1 + " and nodeid in (" + getAllNodeids(getWfid(paramString1), Util.getIntValue(paramString2)) + ")");
/* 151 */     if (recordSet.next())
/* 152 */     { String str = Util.null2String(recordSet.getString("islock"));
/* 153 */       if ("1".equals(str)) {
/* 154 */         String str1 = Util.null2String(recordSet.getString("userid"));
/* 155 */         String str2 = Util.null2String(recordSet.getString("lockdate"));
/* 156 */         String str3 = Util.null2String(recordSet.getString("locktime"));
/* 157 */         long l = 0L;
/*     */         try {
/* 159 */           l = getOverTime(str2, str3);
/* 160 */         } catch (ParseException parseException) {
/* 161 */           l = 0L;
/* 162 */           parseException.printStackTrace();
/*     */         } 
/*     */ 
/*     */ 
/*     */         
/* 167 */         recordSet.executeProc("SystemSet_Select", "");
/* 168 */         recordSet.next();
/* 169 */         int i = Util.getIntValue(Util.null2String(recordSet.getString("lockscan")), 5);
/* 170 */         if (i == 0) {
/* 171 */           i = 5;
/*     */         }
/* 173 */         if (l >= (i * 60000)) {
/* 174 */           recordSet.execute("update workflow_requestlock set islock = 0 where requestid = " + paramString1 + " and nodeid in (" + getAllNodeids(getWfid(paramString1), Util.getIntValue(paramString2)) + ")");
/* 175 */           lockDTO.setLock(false);
/*     */         } else {
/* 177 */           lockDTO.setLock(true);
/* 178 */           lockDTO.setLockdate(str2);
/* 179 */           lockDTO.setLocktime(str3);
/* 180 */           lockDTO.setUserid(str1);
/*     */         } 
/*     */       } else {
/* 183 */         lockDTO.setLock(false);
/*     */       }  }
/* 185 */     else { lockDTO.setLock(false); }
/*     */ 
/*     */     
/* 188 */     return lockDTO;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public synchronized void unlockRequest(String paramString1, String paramString2) {
/* 197 */     RecordSet recordSet = new RecordSet();
/* 198 */     recordSet.execute("update workflow_requestlock set islock = 0 where requestid = " + paramString1 + " and nodeid in (" + getAllNodeids(getWfid(paramString1), Util.getIntValue(paramString2)) + ")");
/*     */   }
/*     */   
/*     */   public boolean unLockRequest(String paramString1, String paramString2, String paramString3, String paramString4) {
/* 202 */     boolean bool = false;
/* 203 */     if (checkEffictive(paramString1, paramString2, paramString3, paramString4)) {
/* 204 */       RecordSet recordSet = new RecordSet();
/* 205 */       String str = "update workflow_requestlock set islock = 0 where requestid =? and nodeid in (" + getAllNodeids(getWfid(paramString1), Util.getIntValue(paramString2)) + ") and userid=? and timestamp=?";
/* 206 */       bool = recordSet.executeUpdate(str, new Object[] { paramString1, paramString3, paramString4 });
/*     */     } 
/* 208 */     return bool;
/*     */   }
/*     */   
/*     */   public boolean deleteOldLocked(String paramString1, String paramString2, String paramString3) {
/* 212 */     boolean bool = false;
/* 213 */     RecordSet recordSet = new RecordSet();
/* 214 */     String str = "delete from workflow_requestlock where requestid =? and nodeid in (" + getAllNodeids(getWfid(paramString1), Util.getIntValue(paramString2)) + ") and userid=?";
/* 215 */     bool = recordSet.executeUpdate(str, new Object[] { paramString1, paramString3 });
/* 216 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean checkEffictive(String paramString1, String paramString2, String paramString3, String paramString4) {
/* 228 */     return true;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public long getOverTime(String paramString1, String paramString2) throws ParseException {
/* 237 */     SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
/* 238 */     Date date1 = simpleDateFormat.parse(paramString1 + " " + paramString2);
/* 239 */     Date date2 = new Date();
/* 240 */     return date2.getTime() - date1.getTime();
/*     */   }
/*     */   
/*     */   public String getDate(Date paramDate) {
/* 244 */     SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
/* 245 */     return simpleDateFormat.format(paramDate);
/*     */   }
/*     */ 
/*     */   
/*     */   public String getTime(Date paramDate) {
/* 250 */     SimpleDateFormat simpleDateFormat = new SimpleDateFormat("HH:mm:ss");
/* 251 */     return simpleDateFormat.format(paramDate);
/*     */   }
/*     */ 
/*     */   
/*     */   public int getWfid(String paramString) {
/* 256 */     RecordSet recordSet = new RecordSet();
/* 257 */     recordSet.execute("select workflowid from workflow_requestbase where requestid = " + paramString);
/* 258 */     recordSet.next();
/* 259 */     return recordSet.getInt(1);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> generateLockPromptInfo(int paramInt1, int paramInt2, String paramString, User paramUser) throws Exception {
/* 267 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 268 */     int i = paramUser.getLanguage();
/* 269 */     int j = paramUser.getUID();
/* 270 */     boolean bool = false;
/* 271 */     if (!"".equals(paramString)) {
/* 272 */       RecordSet recordSet = new RecordSet();
/* 273 */       recordSet.executeQuery("select 1 from workflow_requestlock where requestid=? and nodeid=? and userid=? and islock=1 and timestamp=?", new Object[] { Integer.valueOf(paramInt1), Integer.valueOf(paramInt2), Integer.valueOf(j), paramString });
/* 274 */       bool = recordSet.next();
/*     */     } 
/*     */     
/* 277 */     String str = "";
/* 278 */     boolean bool1 = false;
/* 279 */     if (bool) {
/* 280 */       str = getSelfLockedStr(i);
/* 281 */       updateLockInfo(paramInt1 + "", paramInt2 + "", j + "", paramString);
/*     */     } else {
/* 283 */       LockDTO lockDTO = isLocked(paramInt1 + "", paramInt2 + "");
/* 284 */       boolean bool2 = lockDTO.isLock();
/* 285 */       if (bool2) {
/* 286 */         bool1 = true;
/* 287 */         String str1 = lockDTO.getUserid();
/* 288 */         String str2 = (new ResourceComInfo()).getResourcename(str1);
/* 289 */         if ((paramUser.getUID() + "").equals(str1)) {
/* 290 */           if (i == 7) {
/* 291 */             str = "" + SystemEnv.getHtmlLabelName(10004290, ThreadVarLanguage.getLang()) + "" + lockDTO.getLockdate() + " " + lockDTO.getLocktime() + "  " + SystemEnv.getHtmlLabelName(10004291, ThreadVarLanguage.getLang()) + "<a notreplace='true' style='color:blue;' href=\"javascript:\" onclick=\"javascript:lockUtil.doActivate();\" target=\"_self\">" + SystemEnv.getHtmlLabelName(10004292, ThreadVarLanguage.getLang()) + "</a>（" + SystemEnv.getHtmlLabelName(10004293, ThreadVarLanguage.getLang()) + "）";
/* 292 */           } else if (i == 9) {
/* 293 */             str = "" + SystemEnv.getHtmlLabelName(10004294, ThreadVarLanguage.getLang()) + "" + lockDTO.getLockdate() + " " + lockDTO.getLocktime() + "  " + SystemEnv.getHtmlLabelName(10004295, ThreadVarLanguage.getLang()) + "<a style='color:blue;' href=\"javascript:\" onclick=\"javascript:lockUtil.doActivate();\" target=\"_self\">" + SystemEnv.getHtmlLabelName(10004296, ThreadVarLanguage.getLang()) + "</a>（" + SystemEnv.getHtmlLabelName(10004297, ThreadVarLanguage.getLang()) + "）";
/*     */           } else {
/* 295 */             str = "The request has been checked by yourself at " + lockDTO.getLockdate() + " " + lockDTO.getLocktime() + ",you can operate now or you can <a style='color:blue;' href=\"javascript:\" onclick=\"javascript:lockUtil.doActivate();\" target=\"_self\">click here</a> to unlock the request(the early window will be invalid)";
/*     */           }
/*     */         
/* 298 */         } else if (i == 7) {
/* 299 */           str = "" + SystemEnv.getHtmlLabelName(10004298, ThreadVarLanguage.getLang()) + " <a style='color:blue;' onclick=\"pointerXY(event);\" href=\"javaScript:openhrm(" + str1 + ");\">" + str2 + "</a> " + SystemEnv.getHtmlLabelName(82894, ThreadVarLanguage.getLang()) + "" + lockDTO.getLockdate() + " " + lockDTO.getLocktime() + " " + SystemEnv.getHtmlLabelName(10004299, ThreadVarLanguage.getLang()) + "";
/* 300 */         } else if (i == 9) {
/* 301 */           str = "" + SystemEnv.getHtmlLabelName(10004300, ThreadVarLanguage.getLang()) + " <a style='color:blue;' onclick=\"pointerXY(event);\" href=\"javaScript:openhrm(" + str1 + ");\">" + str2 + "</a> " + SystemEnv.getHtmlLabelName(10004301, ThreadVarLanguage.getLang()) + "" + lockDTO.getLockdate() + " " + lockDTO.getLocktime() + " " + SystemEnv.getHtmlLabelName(10004302, ThreadVarLanguage.getLang()) + "";
/*     */         } else {
/* 303 */           str = "The request has been checked by <a style='color:blue;' onclick=\"pointerXY(event);\" href=\"javaScript:openhrm(" + str1 + ");\">" + str2 + "</a> at " + lockDTO.getLockdate() + " " + lockDTO.getLocktime() + ",you can not operate now";
/*     */         }
/*     */       
/*     */       } else {
/*     */         
/* 308 */         lockRequest(paramInt1 + "", paramInt2 + "", paramUser.getUID() + "", paramString);
/* 309 */         str = getSelfLockedStr(i);
/*     */       } 
/*     */     } 
/* 312 */     hashMap.put("prompttype", PromptType.EDITLOCK.getType());
/* 313 */     hashMap.put("msg", str);
/* 314 */     hashMap.put("haveLocked", Boolean.valueOf(bool1));
/* 315 */     hashMap.put("timestamp", paramString);
/* 316 */     return (Map)hashMap;
/*     */   }
/*     */   
/*     */   private String getSelfLockedStr(int paramInt) {
/* 320 */     String str = "";
/* 321 */     if (paramInt == 7) {
/* 322 */       str = "" + SystemEnv.getHtmlLabelName(10004303, ThreadVarLanguage.getLang()) + "<a style='color:blue;' href=\"javascript:\" onclick=\"lockUtil.doUnLock();\" target=\"_self\">" + SystemEnv.getHtmlLabelName(10004304, ThreadVarLanguage.getLang()) + "</a>";
/* 323 */     } else if (paramInt == 9) {
/* 324 */       str = "" + SystemEnv.getHtmlLabelName(10004305, ThreadVarLanguage.getLang()) + "<a style='color:blue;' href=\"javascript:\" onclick=\"lockUtil.doUnLock();\" target=\"_self\">" + SystemEnv.getHtmlLabelName(10004306, ThreadVarLanguage.getLang()) + "</a>";
/*     */     } else {
/* 326 */       str = "The request has been checked by yourself，if you want to checkout，please <a style='color:blue;' href=\"javascript:\" onclick=\"lockUtil.doUnLock();\" target=\"_self\">click here </a> to unlock";
/*     */     } 
/* 328 */     return str;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/LockUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */