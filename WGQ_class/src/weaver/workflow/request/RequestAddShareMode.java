/*     */ package weaver.workflow.request;
/*     */ 
/*     */ import com.weaver.formmodel.util.StringHelper;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Hashtable;
/*     */ import org.apache.commons.lang3.StringUtils;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.formmode.setup.ModeRightInfo;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class RequestAddShareMode
/*     */   extends BaseBean
/*     */ {
/*     */   private static final String splitstring = "#";
/*     */   private static final boolean isdebug = false;
/*     */   
/*     */   public void addShareInof(int paramInt1, int paramInt2) {
/*  31 */     ArrayList arrayList = getDataList(paramInt1);
/*  32 */     if (arrayList.size() > 0) {
/*  33 */       addModeShareInfo(paramInt1, arrayList, paramInt2 + "", "0");
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void addShareInfo(int paramInt1, int paramInt2, int paramInt3, String paramString) {
/*  43 */     RecordSet recordSet = new RecordSet();
/*  44 */     ArrayList<String> arrayList = new ArrayList();
/*     */     
/*     */     try {
/*  47 */       recordSet.executeSql("select userid from workflow_currentoperator where requestid=" + paramInt1 + " group by userid");
/*  48 */       while (recordSet.next()) {
/*  49 */         arrayList.add(recordSet.getString(1));
/*     */       }
/*  51 */       if (arrayList.size() > 0) {
/*  52 */         paramString = StringUtils.join(arrayList, ",");
/*     */       }
/*     */ 
/*     */       
/*  56 */       ArrayList arrayList1 = getDataList(paramInt1);
/*     */       
/*  58 */       if (arrayList1.size() > 0) {
/*  59 */         addModeShareInfo(paramInt1, arrayList1, paramString, "");
/*     */       }
/*  61 */     } catch (Exception exception) {
/*  62 */       writeLog(exception);
/*     */     } 
/*     */   }
/*     */   
/*     */   public void addModeShareInfo(int paramInt, ArrayList<E> paramArrayList, String paramString1, String paramString2) {
/*  67 */     RecordSet recordSet = new RecordSet();
/*  68 */     ModeRightInfo modeRightInfo = new ModeRightInfo();
/*  69 */     ArrayList<String> arrayList = Util.TokenizerString(paramString1, ",");
/*  70 */     Hashtable<Object, Object> hashtable = new Hashtable<>();
/*     */ 
/*     */     
/*     */     try {
/*  74 */       for (byte b = 0; b < arrayList.size(); b++) {
/*  75 */         String str1 = ((String)arrayList.get(b)).toString();
/*  76 */         String str2 = "0";
/*     */ 
/*     */ 
/*     */         
/*  80 */         hashtable.put(Integer.valueOf(Util.getIntValue(str1)), Integer.valueOf(Util.getIntValue(str2)));
/*     */       } 
/*  82 */     } catch (Exception exception) {
/*  83 */       writeLog(exception);
/*     */     } 
/*  85 */     if (paramArrayList == null || paramArrayList.size() == 0) {
/*     */       return;
/*     */     }
/*     */     try {
/*  89 */       String str = "";
/*  90 */       for (byte b = 0; b < paramArrayList.size(); b++) {
/*  91 */         String str1 = paramArrayList.get(b).toString();
/*  92 */         String[] arrayOfString = Util.TokenizerString2(str1, "#");
/*     */         
/*  94 */         if (arrayOfString != null && arrayOfString.length == 2) {
/*     */ 
/*     */           
/*  97 */           String str2 = arrayOfString[0];
/*  98 */           String str3 = arrayOfString[1];
/*     */           
/* 100 */           if (!str2.equalsIgnoreCase("null") && !str2.equals("")) {
/*     */ 
/*     */             
/* 103 */             int i = 0;
/* 104 */             String str4 = "";
/* 105 */             if (str3.indexOf("browser.") != -1) {
/* 106 */               String str5 = "";
/* 107 */               String str6 = "";
/* 108 */               str3 = str3.replace("browser.", "");
/*     */               
/* 110 */               str = "SELECT d.id,d.showname,b.modeid,b.customname,(select tablename from workflow_bill where id=b.formid) as tablename,b.id as customid,b.detailtable FROM mode_browser d ,mode_custombrowser b WHERE d.customid=b.id AND d.showname='" + str3 + "'";
/* 111 */               recordSet.executeSql(str);
/* 112 */               String str7 = "";
/* 113 */               String str8 = "id";
/* 114 */               if (recordSet.next()) {
/* 115 */                 i = recordSet.getInt("modeid");
/* 116 */                 str5 = recordSet.getString("tablename");
/* 117 */                 str6 = recordSet.getString("detailtable");
/* 118 */                 str4 = recordSet.getString("customid");
/* 119 */                 String str9 = "select b.fieldname from mode_CustombrowserDspField a,workflow_billfield b where a.fieldid=b.id and customid=" + str4 + " and ispk=1";
/* 120 */                 recordSet.execute(str9);
/* 121 */                 if (recordSet.next()) {
/* 122 */                   str8 = recordSet.getString("fieldname");
/*     */                 }
/* 124 */                 if (!StringHelper.isEmpty(str6)) {
/* 125 */                   str7 = "select mainid from " + str6 + " where " + str8 + "='$fieldvalue$'";
/*     */                 }
/* 127 */                 else if (!str8.equals("id")) {
/* 128 */                   str7 = "select id from " + str5 + " where " + str8 + "='$fieldvalue$'";
/*     */                 } 
/*     */               } 
/*     */ 
/*     */               
/* 133 */               if (i > 0) {
/* 134 */                 ArrayList arrayList1 = Util.TokenizerString(str2, ",");
/* 135 */                 for (byte b1 = 0; b1 < arrayList1.size(); b1++) {
/* 136 */                   String str9 = Util.null2String(arrayList1.get(b1));
/* 137 */                   if (!str9.equals("")) {
/* 138 */                     String str10 = "";
/* 139 */                     if (!StringHelper.isEmpty(str7)) {
/* 140 */                       str10 = str7.replace("$fieldvalue$", str9);
/* 141 */                       recordSet.execute(str10);
/* 142 */                       if (recordSet.next()) {
/* 143 */                         if (!StringHelper.isEmpty(str6)) {
/* 144 */                           str9 = recordSet.getString("mainid");
/*     */                         }
/* 146 */                         else if (!str8.equals("id")) {
/* 147 */                           str9 = recordSet.getString("id");
/*     */                         } 
/*     */                       }
/*     */                     } 
/*     */ 
/*     */                     
/* 153 */                     modeRightInfo.shareModeByWorkflow(hashtable, paramInt, i, Util.getIntValue(str9));
/*     */                   } 
/*     */                 } 
/*     */               } 
/*     */             } else {
/* 158 */               String str5 = "";
/* 159 */               String str6 = "";
/* 160 */               str = "SELECT d.sourceid,d.tablename,d.tablekey  FROM mode_customtree t,mode_customtreedetail d WHERE d.sourcefrom=1 and d.mainid=t.id AND t.id=" + str3;
/* 161 */               recordSet.executeSql(str);
/* 162 */               String str7 = "";
/* 163 */               HashMap<Object, Object> hashMap = new HashMap<>();
/* 164 */               while (recordSet.next()) {
/* 165 */                 i = recordSet.getInt("sourceid");
/* 166 */                 str6 = recordSet.getString("tablekey");
/*     */                 
/* 168 */                 String str8 = Util.null2String(recordSet.getString("tablename"));
/* 169 */                 str7 = str7 + "'" + str8 + "',";
/* 170 */                 hashMap.put(str8, Integer.valueOf(i));
/*     */               } 
/* 172 */               if (str7.endsWith(",")) {
/* 173 */                 str7 = str7.substring(0, str7.length() - 1);
/*     */               }
/* 175 */               if (i > 0) {
/* 176 */                 ArrayList arrayList1 = Util.TokenizerString(str2, ",");
/* 177 */                 str2 = "";
/* 178 */                 for (byte b1 = 0; b1 < arrayList1.size(); b1++) {
/* 179 */                   String str8 = arrayList1.get(b1).toString().split("_")[1];
/* 180 */                   String str9 = arrayList1.get(b1).toString().split("_")[0];
/* 181 */                   recordSet.executeSql("select tablekey,showfield,tablename from mode_customtreedetail where tablename in (" + str7 + ") and id=" + str9);
/* 182 */                   if (recordSet.next()) {
/* 183 */                     str6 = recordSet.getString("tablekey");
/* 184 */                     str5 = recordSet.getString("tablename");
/*     */                   } 
/* 186 */                   String str10 = "select id from " + str5 + " where " + str6 + "='" + str8 + "'";
/* 187 */                   recordSet.executeSql(str10);
/* 188 */                   if (recordSet.next()) {
/* 189 */                     str2 = "," + recordSet.getString(1);
/*     */                   }
/*     */ 
/*     */                   
/* 193 */                   ArrayList arrayList2 = Util.TokenizerString(str2, ",");
/* 194 */                   for (byte b2 = 0; b2 < arrayList2.size(); b2++)
/* 195 */                   { String str11 = Util.null2String(arrayList2.get(b2));
/* 196 */                     if (!str11.equals(""))
/*     */                     {
/* 198 */                       modeRightInfo.shareModeByWorkflow(hashtable, paramInt, Util.getIntValue((new StringBuilder()).append(hashMap.get(str5)).append("").toString(), 0), Util.getIntValue(str11)); }  } 
/*     */                 } 
/*     */               } 
/*     */             } 
/*     */           } 
/*     */         } 
/*     */       } 
/* 205 */     } catch (Exception exception) {
/* 206 */       writeLog(exception);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void getWfShareUserid(int paramInt1, String paramString1, String paramString2, ArrayList<E> paramArrayList, int paramInt2, int paramInt3, int paramInt4) {
/* 217 */     RecordSet recordSet = new RecordSet();
/* 218 */     int i = 0;
/* 219 */     ModeRightInfo modeRightInfo = new ModeRightInfo();
/*     */     try {
/* 221 */       RecordSet recordSet1 = new RecordSet();
/* 222 */       String str = "";
/* 223 */       ArrayList<E> arrayList = getDataList(paramInt1);
/* 224 */       for (byte b = 0; b < arrayList.size(); b++) {
/* 225 */         String str1 = arrayList.get(b).toString();
/* 226 */         String[] arrayOfString = Util.TokenizerString2(str1, "#");
/*     */ 
/*     */ 
/*     */         
/* 230 */         if (arrayOfString != null && arrayOfString.length == 2) {
/*     */ 
/*     */           
/* 233 */           String str2 = arrayOfString[0];
/* 234 */           String str3 = arrayOfString[1];
/*     */ 
/*     */ 
/*     */           
/* 238 */           if (str3.indexOf("browser.") != -1) {
/* 239 */             str3 = str3.replace("browser.", "");
/*     */ 
/*     */             
/* 242 */             str = "SELECT d.id,d.showname,b.modeid,b.customname FROM mode_browser d ,mode_custombrowser b WHERE d.customid=b.id AND d.showname='" + str3 + "'";
/*     */             
/* 244 */             recordSet.executeSql(str);
/* 245 */             if (recordSet.next()) {
/* 246 */               i = recordSet.getInt("modeid");
/*     */             }
/*     */           }
/*     */           else {
/*     */             
/* 251 */             String str4 = "";
/* 252 */             String str5 = "";
/* 253 */             str = "SELECT d.sourceid,d.tablename,d.tablekey  FROM mode_customtree t,mode_customtreedetail d WHERE d.sourcefrom=1 and d.mainid=t.id AND t.id=" + str3;
/* 254 */             recordSet.executeSql(str);
/* 255 */             String str6 = "";
/* 256 */             while (recordSet.next()) {
/* 257 */               i = recordSet.getInt("sourceid");
/* 258 */               str5 = recordSet.getString("tablekey");
/*     */               
/* 260 */               String str7 = Util.null2String(recordSet.getString("tablename"));
/* 261 */               str6 = str6 + "'" + str7 + "',";
/*     */             } 
/* 263 */             if (str6.endsWith(",")) {
/* 264 */               str6 = str6.substring(0, str6.length() - 1);
/*     */             }
/* 266 */             if (i > 0) {
/* 267 */               ArrayList arrayList1 = Util.TokenizerString(str2, ",");
/* 268 */               str2 = "";
/* 269 */               for (byte b1 = 0; b1 < arrayList1.size(); b1++) {
/* 270 */                 String str7 = arrayList1.get(b1).toString().split("_")[1];
/* 271 */                 String str8 = arrayList1.get(b1).toString().split("_")[0];
/* 272 */                 recordSet.executeSql("select tablekey,showfield,tablename from mode_customtreedetail where tablename in (" + str6 + ") and id=" + str8);
/* 273 */                 if (recordSet.next()) {
/* 274 */                   str5 = recordSet.getString("tablekey");
/* 275 */                   str4 = recordSet.getString("tablename");
/*     */                 } 
/* 277 */                 String str9 = "select id from " + str4 + " where " + str5 + "='" + str7 + "'";
/* 278 */                 recordSet.executeSql(str9);
/* 279 */                 if (recordSet.next()) {
/* 280 */                   str2 = str2 + "," + recordSet.getString(1);
/*     */                 }
/*     */               } 
/*     */             } 
/*     */           } 
/*     */ 
/*     */           
/* 287 */           if (i > 0) {
/* 288 */             ArrayList<E> arrayList1 = Util.TokenizerString(str2, ",");
/* 289 */             for (byte b1 = 0; b1 < arrayList1.size(); b1++) {
/* 290 */               int j = Util.getIntValue(arrayList1.get(b1).toString(), 0);
/* 291 */               if (j > 0)
/*     */               {
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */                 
/* 299 */                 if (paramArrayList.size() == 0) {
/* 300 */                   if (paramString2.equals("3")) {
/* 301 */                     modeRightInfo.ShareModeByWorkflow(paramInt1, i, j, 5, "0", 0, paramInt3, paramInt4);
/*     */                   }
/*     */                 } else {
/* 304 */                   for (byte b2 = 0; b2 < paramArrayList.size(); b2++) {
/* 305 */                     String str4 = paramArrayList.get(b2).toString();
/*     */ 
/*     */ 
/*     */                     
/* 309 */                     if (paramString2.equals("1")) {
/* 310 */                       modeRightInfo.ShareModeByWorkflow(paramInt1, i, j, 3, str4, 0, paramInt3, paramInt4);
/* 311 */                     } else if (paramString2.equals("2")) {
/* 312 */                       modeRightInfo.ShareModeByWorkflow(paramInt1, i, j, 4, str4, paramInt2, paramInt3, paramInt4);
/* 313 */                     } else if (paramString2.equals("5")) {
/* 314 */                       modeRightInfo.ShareModeByWorkflow(paramInt1, i, j, 1, str4, 0, 0, 0);
/* 315 */                     } else if (paramString2.equals("6")) {
/* 316 */                       modeRightInfo.ShareModeByWorkflow(paramInt1, i, j, 2, str4, 0, paramInt3, paramInt4);
/*     */                     } 
/*     */                   } 
/*     */                 }  } 
/*     */             } 
/*     */           } 
/*     */         } 
/*     */       } 
/* 324 */     } catch (Exception exception) {
/* 325 */       writeLog(exception);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public ArrayList getDataList(int paramInt) {
/* 336 */     ArrayList<String> arrayList = new ArrayList();
/* 337 */     RecordSet recordSet1 = new RecordSet();
/* 338 */     RecordSet recordSet2 = new RecordSet();
/* 339 */     String str1 = "";
/* 340 */     recordSet1.executeSql("select formid,isbill from workflow_base where id=(select workflowid from workflow_requestbase where requestid=" + paramInt + ")");
/* 341 */     recordSet1.next();
/* 342 */     int i = Util.getIntValue(recordSet1.getString(1), 0);
/* 343 */     int j = Util.getIntValue(recordSet1.getString(2), 0);
/*     */     
/* 345 */     int k = 0;
/* 346 */     String str2 = "mainid";
/* 347 */     if (j == 1) {
/* 348 */       recordSet1.executeSql("select tablename,detailkeyfield from workflow_bill where id=" + i);
/* 349 */       if (recordSet1.next()) {
/* 350 */         String str = recordSet1.getString(1);
/* 351 */         str2 = recordSet1.getString(2);
/* 352 */         recordSet2.executeSql("select id from " + str + " where requestid=" + paramInt);
/* 353 */         if (recordSet2.next()) {
/* 354 */           k = recordSet2.getInt(1);
/*     */         }
/*     */       } 
/*     */     } else {
/* 358 */       str2 = "requestid";
/* 359 */       k = paramInt;
/*     */     } 
/*     */     
/* 362 */     if (j == 0) {
/* 363 */       str1 = "select id ,fieldname,'0' as viewtype,fielddbtype,'workflow_form' as tablename from workflow_formdict c,workflow_formfield f WHERE f.fieldid=c.id AND c.fieldhtmltype='3'  and ( type='161' or type='162' or type='256' or type='257' ) AND (f.isdetail IS NULL OR f.isdetail='' OR f.isdetail='0') and f.formid=" + i + " union  select id ,fieldname,'1' as viewtype,fielddbtype,'workflow_formdetail' as tablename  from workflow_formdictdetail c ,workflow_formfield f WHERE f.fieldid=c.id AND f.isdetail='1' and c.fieldhtmltype='3'  and ( type='161' or type='162' or type='256' or type='257' ) AND f.formid=" + i;
/*     */     
/*     */     }
/*     */     else {
/*     */       
/* 368 */       str1 = "SELECT id,fieldname,viewtype,fielddbtype,detailtable as tablename FROM workflow_billfield WHERE billid=" + i + " AND fieldhtmltype='3'  and ( type='161' or type='162' or type='256' or type='257' )";
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/*     */     try {
/* 374 */       recordSet1.executeSql(str1);
/* 375 */       while (recordSet1.next()) {
/* 376 */         int m = Util.getIntValue(recordSet1.getString("viewtype"));
/* 377 */         String str3 = Util.null2String(recordSet1.getString("fieldname"));
/* 378 */         String str4 = Util.null2String(recordSet1.getString("fielddbtype"));
/* 379 */         String str5 = Util.null2String(recordSet1.getString("tablename"));
/* 380 */         if (j == 0) {
/* 381 */           recordSet2.executeSql("select " + str3 + " from " + str5 + " where requestid=" + paramInt);
/* 382 */           while (recordSet2.next()) {
/* 383 */             String str = Util.null2String(recordSet2.getString(1));
/* 384 */             if (!str.equals("") && !str.equalsIgnoreCase("null"))
/* 385 */               arrayList.add(str + "#" + str4); 
/*     */           } 
/*     */           continue;
/*     */         } 
/* 389 */         if (str5.equals("")) {
/* 390 */           recordSet2.executeSql("select tablename from workflow_bill where id=" + i);
/* 391 */           if (recordSet2.next()) {
/* 392 */             str5 = recordSet2.getString(1);
/*     */           }
/* 394 */           recordSet2.executeSql("select " + str3 + " from " + str5 + " where requestid=" + paramInt);
/* 395 */           while (recordSet2.next()) {
/*     */             
/* 397 */             String str = Util.null2String(recordSet2.getString(1));
/* 398 */             if (!str.equals("") && !str.equalsIgnoreCase("null"))
/* 399 */               arrayList.add(str + "#" + str4); 
/*     */           } 
/*     */           continue;
/*     */         } 
/* 403 */         recordSet2.executeSql("select " + str3 + " from " + str5 + " where " + str2 + "=" + k);
/* 404 */         while (recordSet2.next())
/*     */         {
/* 406 */           String str = Util.null2String(recordSet2.getString(1));
/* 407 */           if (!str.equals("") && !str.equalsIgnoreCase("null")) {
/* 408 */             arrayList.add(str + "#" + str4);
/*     */           }
/*     */         }
/*     */       
/*     */       }
/*     */     
/*     */     }
/* 415 */     catch (Exception exception) {}
/*     */ 
/*     */     
/* 418 */     return arrayList;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/RequestAddShareMode.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */