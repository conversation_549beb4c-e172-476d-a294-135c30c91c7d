/*     */ package weaver.workflow.request;
/*     */ 
/*     */ import com.engine.workflow.biz.requestForm.RequestSecLevelBiz;
/*     */ import java.util.ArrayList;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class RequestComInfo
/*     */   extends BaseBean
/*     */ {
/*  23 */   private ArrayList ids = null;
/*  24 */   private ArrayList names = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getRequestname(String paramString) {
/*  38 */     if (paramString == null || paramString.trim().equals("")) {
/*  39 */       return "";
/*     */     }
/*  41 */     RecordSet recordSet = new RecordSet();
/*  42 */     recordSet.executeProc("workflow_Requestbase_SByID", paramString);
/*  43 */     if (recordSet.next()) return recordSet.getString("requestname"); 
/*  44 */     return "";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getRequestCreateTime(String paramString) {
/*  52 */     if (paramString == null || paramString.trim().equals("")) {
/*  53 */       return "";
/*     */     }
/*  55 */     RecordSet recordSet = new RecordSet();
/*  56 */     recordSet.executeProc("workflow_Requestbase_SByID", paramString);
/*  57 */     if (recordSet.next()) return recordSet.getString("createdate") + " " + recordSet.getString("createtime"); 
/*  58 */     return "";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getRequestCreater(String paramString) {
/*  66 */     if (paramString == null || paramString.trim().equals("")) {
/*  67 */       return "";
/*     */     }
/*  69 */     RecordSet recordSet = new RecordSet();
/*  70 */     recordSet.executeProc("workflow_Requestbase_SByID", paramString);
/*  71 */     if (recordSet.next()) return recordSet.getString("creater"); 
/*  72 */     return "";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getRequestStatus(String paramString) {
/*  80 */     if (paramString == null || paramString.trim().equals("")) {
/*  81 */       return "";
/*     */     }
/*  83 */     RecordSet recordSet = new RecordSet();
/*  84 */     recordSet.executeProc("workflow_Requestbase_SByID", paramString);
/*  85 */     if (recordSet.next()) return recordSet.getString("status"); 
/*  86 */     return "";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getRequestType(String paramString) {
/*  94 */     if (paramString == null || paramString.trim().equals(""))
/*  95 */       return ""; 
/*  96 */     String str = "select b.workflowname from workflow_requestbase a,workflow_base b where a.workflowid=b.id and a.requestid=" + paramString;
/*     */ 
/*     */     
/*  99 */     RecordSet recordSet = new RecordSet();
/* 100 */     recordSet.executeSql(str);
/* 101 */     if (recordSet.next()) return recordSet.getString("workflowname"); 
/* 102 */     return "";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getRequestCurrentNodeType(String paramString) {
/* 111 */     if (paramString == null || paramString.trim().equals(""))
/* 112 */       return ""; 
/* 113 */     RecordSet recordSet = new RecordSet();
/* 114 */     recordSet.executeProc("workflow_Requestbase_SByID", paramString);
/* 115 */     if (recordSet.next()) return recordSet.getString("currentnodetype"); 
/* 116 */     return "";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static int getRequestSecLevel(String paramString) {
/* 126 */     return (new RequestSecLevelBiz()).getSecLevelByRequestId(paramString);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/RequestComInfo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */