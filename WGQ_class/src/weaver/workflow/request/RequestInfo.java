/*     */ package weaver.workflow.request;
/*     */ 
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.hrm.User;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class RequestInfo
/*     */   extends BaseBean
/*     */ {
/*  21 */   private int requestid = 0;
/*  22 */   private int workflowid = 0;
/*  23 */   private int nodeid = 0;
/*  24 */   private String nodetype = "";
/*  25 */   private String requestname = "";
/*  26 */   private int formid = 0;
/*  27 */   private int billid = 0;
/*  28 */   private int hasright = 0;
/*  29 */   private int isremark = 0;
/*  30 */   private int userid = 0;
/*  31 */   private int usertype = 0;
/*     */   
/*  33 */   private String isreject = "";
/*  34 */   private String isend = "";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public RequestInfo(int paramInt, User paramUser) {
/*  41 */     initUser(paramUser);
/*  42 */     initWorkflow(paramInt);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public RequestInfo() {}
/*     */ 
/*     */ 
/*     */   
/*     */   private void initUser(User paramUser) {
/*  53 */     String str = paramUser.getLogintype();
/*  54 */     if (str.equals("1"))
/*  55 */       setUsertype(0); 
/*  56 */     if (str.equals("2")) {
/*  57 */       setUsertype(1);
/*     */     }
/*  59 */     setUserid(paramUser.getUID());
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void initWorkflow(int paramInt) {
/*  67 */     setRequestid(paramInt);
/*  68 */     RecordSet recordSet = new RecordSet();
/*  69 */     recordSet.executeProc("workflow_Requestbase_SByID", getRequestid() + "");
/*     */     
/*  71 */     if (recordSet.next()) {
/*  72 */       setWorkflowid(recordSet.getInt("workflowid"));
/*  73 */       setNodeid(recordSet.getInt("currentnodeid"));
/*  74 */       setNodetype(recordSet.getString("currentnodetype"));
/*  75 */       setRequestname(recordSet.getString("requestname"));
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  81 */     recordSet.executeSql("select * from workflow_currentoperator where requestid=" + getRequestid() + " and userid=" + getUserid() + " and usertype = " + getUsertype() + " and isremark='0'");
/*  82 */     if (recordSet.next()) setHasright(1); 
/*  83 */     recordSet.executeSql("select * from workflow_currentoperator where requestid=" + getRequestid() + " and userid=" + getUserid() + " and usertype=" + getUsertype() + " and isremark='1'");
/*  84 */     if (recordSet.next()) setIsremark(1);
/*     */     
/*  86 */     recordSet.executeProc("workflow_Nodebase_SelectByID", getNodeid() + "");
/*  87 */     if (recordSet.next()) {
/*  88 */       setIsreject(recordSet.getString("isreject"));
/*  89 */       setIsend(recordSet.getString("isend"));
/*     */     } 
/*     */     
/*  92 */     recordSet.executeProc("workflow_form_SByRequestid", getRequestid() + "");
/*  93 */     if (recordSet.next()) {
/*  94 */       setFormid(recordSet.getInt("billformid"));
/*  95 */       setBillid(recordSet.getInt("billid"));
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setRequestid(int paramInt) {
/* 103 */     this.requestid = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getRequestid() {
/* 110 */     return this.requestid;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setWorkflowid(int paramInt) {
/* 117 */     this.workflowid = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getWorkflowid() {
/* 124 */     return this.workflowid;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setNodeid(int paramInt) {
/* 131 */     this.nodeid = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getNodeid() {
/* 138 */     return this.nodeid;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setFormid(int paramInt) {
/* 145 */     this.formid = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getFormid() {
/* 152 */     return this.formid;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setBillid(int paramInt) {
/* 159 */     this.billid = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getBillid() {
/* 166 */     return this.billid;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setHasright(int paramInt) {
/* 173 */     this.hasright = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getHasright() {
/* 180 */     return this.hasright;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setIsremark(int paramInt) {
/* 187 */     this.isremark = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getIsremark() {
/* 194 */     return this.isremark;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setUserid(int paramInt) {
/* 201 */     this.userid = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getUserid() {
/* 208 */     return this.userid;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setUsertype(int paramInt) {
/* 215 */     this.usertype = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getUsertype() {
/* 222 */     return this.usertype;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setNodetype(String paramString) {
/* 229 */     this.nodetype = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getNodetype() {
/* 236 */     return this.nodetype;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setRequestname(String paramString) {
/* 243 */     this.requestname = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getRequestname() {
/* 250 */     return this.requestname;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setIsreject(String paramString) {
/* 257 */     this.isreject = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getIsreject() {
/* 264 */     return this.isreject;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setIsend(String paramString) {
/* 271 */     this.isend = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getIsend() {
/* 278 */     return this.isend;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/RequestInfo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */