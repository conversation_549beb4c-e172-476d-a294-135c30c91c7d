/*    */ package weaver.workflow.request;
/*    */ 
/*    */ import java.io.IOException;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import javax.servlet.ServletException;
/*    */ import javax.servlet.ServletOutputStream;
/*    */ import javax.servlet.http.HttpServlet;
/*    */ import javax.servlet.http.HttpServletRequest;
/*    */ import javax.servlet.http.HttpServletResponse;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.Util;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class WorkflowList
/*    */   extends HttpServlet
/*    */ {
/*    */   public void doGet(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws ServletException, IOException {
/* 32 */     doPost(paramHttpServletRequest, paramHttpServletResponse);
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public void doPost(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws ServletException, IOException {
/* 38 */     paramHttpServletResponse.setContentType("text/html;charset=UTF-8");
/*    */     
/* 40 */     RecordSet recordSet = new RecordSet();
/* 41 */     int i = -1;
/* 42 */     String str1 = Util.null2String(paramHttpServletRequest.getParameter("username"));
/* 43 */     recordSet.executeSql("SELECT id FROM HrmResource where loginid='" + str1 + "'");
/* 44 */     if (recordSet.next()) {
/* 45 */       i = recordSet.getInt("id");
/*    */     }
/* 47 */     String str2 = Util.null2String(paramHttpServletRequest.getParameter("logintype"));
/* 48 */     boolean bool = false;
/* 49 */     if (str2.equals("1")) bool = false; 
/* 50 */     if (str2.equals("2")) bool = true;
/*    */     
/* 52 */     int j = Util.getIntValue(paramHttpServletRequest.getParameter("workflowtype"), -1);
/*    */     
/* 54 */     ServletOutputStream servletOutputStream = paramHttpServletResponse.getOutputStream();
/*    */     
/* 56 */     StringBuffer stringBuffer = new StringBuffer();
/* 57 */     stringBuffer.append("issuccess=1&errormsg=");
/*    */     
/* 59 */     String str3 = "select distinct t2.id, t2.workflowname from workflow_requestbase t1,workflow_base t2,workflow_currentoperator t3 where t2.id=t1.workflowid and t2.workflowtype=" + j + " and t3.isremark in( '0','1') and t3.userid=" + i + " and t3.usertype=" + bool + " and t3.requestid=t1.requestid and ( t1.deleted=0 or t1.deleted is null ) and t1.currentnodetype<>'3'";
/*    */ 
/*    */     
/* 62 */     ArrayList<String> arrayList1 = new ArrayList();
/* 63 */     ArrayList<String> arrayList2 = new ArrayList();
/* 64 */     HashMap<Object, Object> hashMap = new HashMap<Object, Object>();
/*    */     
/* 66 */     recordSet.executeSql(str3);
/* 67 */     while (recordSet.next()) {
/* 68 */       arrayList1.add(recordSet.getString("id"));
/* 69 */       arrayList2.add(recordSet.getString("workflowname"));
/*    */     } 
/*    */     
/* 72 */     str3 = "select count(t2.id) wfcount, t2.id from workflow_requestbase t1,workflow_base t2,workflow_currentoperator t3 where t2.id=t1.workflowid and t2.workflowtype=" + j + " and t3.isremark in( '0','1') and t3.userid=" + i + " and t3.usertype=" + bool + " and t3.requestid=t1.requestid and ( t1.deleted=0 or t1.deleted is null ) and t1.currentnodetype<>'3' " + "group by t2.id";
/*    */ 
/*    */ 
/*    */     
/* 76 */     recordSet.executeSql(str3);
/* 77 */     while (recordSet.next()) {
/* 78 */       hashMap.put(recordSet.getString("id"), recordSet.getString("wfcount"));
/*    */     }
/*    */     
/* 81 */     byte b1 = 0;
/* 82 */     for (byte b2 = 0; b2 < arrayList1.size(); b2++) {
/* 83 */       stringBuffer.append("&workflowid_" + b2 + "=" + arrayList1.get(b2));
/* 84 */       stringBuffer.append("&workfowname_" + b2 + "=" + arrayList2.get(b2));
/* 85 */       stringBuffer.append("&workflowcount_" + b2 + "=" + hashMap.get(arrayList1.get(b2)));
/* 86 */       b1++;
/*    */     } 
/* 88 */     stringBuffer.append("&nodenum=" + b1);
/*    */     
/* 90 */     servletOutputStream.print(stringBuffer.toString());
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/WorkflowList.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */