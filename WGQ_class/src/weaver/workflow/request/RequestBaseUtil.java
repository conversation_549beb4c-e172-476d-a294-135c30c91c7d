/*     */ package weaver.workflow.request;
/*     */ 
/*     */ import com.engine.workflow.util.WfListCusTitleUtil;
/*     */ import com.google.common.base.Strings;
/*     */ import java.util.ArrayList;
/*     */ import java.util.List;
/*     */ import java.util.regex.Matcher;
/*     */ import java.util.regex.Pattern;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.workflow.workflow.WorkflowAllComInfo;
/*     */ import weaver.workflow.workflow.WorkflowBillComInfo;
/*     */ import weaver.workflow.workflow.WorkflowComInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class RequestBaseUtil
/*     */ {
/*     */   public String getFieldname(String paramString1, String paramString2) {
/*  28 */     String str1 = "";
/*  29 */     RecordSet recordSet = new RecordSet();
/*  30 */     WorkflowComInfo workflowComInfo = new WorkflowComInfo();
/*     */     
/*  32 */     String str2 = workflowComInfo.getIsBill(paramString1);
/*     */     
/*  34 */     String str3 = "";
/*  35 */     if (str2.equals("1")) {
/*  36 */       str3 = "select fieldname from workflow_billfield where id = " + paramString2;
/*     */     } else {
/*  38 */       str3 = "select fieldname from workflow_formdict where id =  " + paramString2 + " union  select fieldname from workflow_formdictdetail where id = " + paramString2;
/*     */     } 
/*     */     
/*  41 */     recordSet.execute(str3);
/*  42 */     if (recordSet.next()) {
/*  43 */       str1 = Util.null2String(recordSet.getString(1));
/*     */     }
/*  45 */     return str1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getFieldValue(String paramString1, String paramString2) {
/*  55 */     RecordSet recordSet = new RecordSet();
/*  56 */     recordSet.execute("select workflowid from workflow_requestbase where requestid = " + paramString1);
/*  57 */     if (recordSet.next()) {
/*  58 */       String str = recordSet.getString(1);
/*  59 */       return getFieldValue(paramString1, paramString2, str);
/*     */     } 
/*  61 */     return "";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getFieldValue(String paramString1, String paramString2, String paramString3) {
/*  73 */     String str1 = "";
/*  74 */     RecordSet recordSet = new RecordSet();
/*  75 */     WorkflowComInfo workflowComInfo = new WorkflowComInfo();
/*  76 */     WorkflowBillComInfo workflowBillComInfo = new WorkflowBillComInfo();
/*     */     
/*  78 */     String str2 = workflowComInfo.getIsBill(paramString3);
/*  79 */     String str3 = workflowComInfo.getFormId(paramString3);
/*  80 */     String str4 = "workflow_form";
/*  81 */     if ("1".equals(str2)) {
/*  82 */       str4 = workflowBillComInfo.getTablename(str3);
/*     */     }
/*  84 */     recordSet.execute("select " + paramString2 + " from " + str4 + " where requestid = " + paramString1);
/*  85 */     if (recordSet.next()) {
/*  86 */       str1 = Util.null2String(recordSet.getString(1));
/*     */     }
/*     */     
/*  89 */     return str1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getFieldLabel(String paramString1, String paramString2, int paramInt) {
/* 100 */     String str1 = "";
/* 101 */     RecordSet recordSet = new RecordSet();
/* 102 */     WorkflowComInfo workflowComInfo = new WorkflowComInfo();
/* 103 */     String str2 = workflowComInfo.getIsBill(paramString2);
/* 104 */     String str3 = workflowComInfo.getFormId(paramString2);
/* 105 */     String str4 = "";
/* 106 */     if (str2.equals("1")) {
/* 107 */       str4 = "select fieldlabel from workflow_billfield where id = " + paramString1;
/*     */     }
/* 109 */     else if (!Strings.isNullOrEmpty(paramString1) && !Strings.isNullOrEmpty(str3)) {
/* 110 */       str4 = "select fieldlable from workflow_fieldlable where fieldid = " + paramString1 + " and formid = " + str3 + " and langurageid = " + paramInt;
/*     */     } 
/*     */     
/* 113 */     recordSet.execute(str4);
/* 114 */     if (recordSet.next()) {
/* 115 */       str1 = Util.null2String(recordSet.getString(1));
/* 116 */       if ("1".equals(str2))
/* 117 */         str1 = SystemEnv.getHtmlLabelName(Util.getIntValue(str1), paramInt); 
/*     */     } 
/* 119 */     return str1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<String> checkTemplateContent(String paramString1, String paramString2, String paramString3) {
/* 128 */     ArrayList<Integer> arrayList1 = new ArrayList();
/* 129 */     ArrayList<Integer> arrayList2 = new ArrayList();
/* 130 */     ArrayList<String> arrayList = new ArrayList();
/* 131 */     Matcher matcher1 = Pattern.compile(paramString2).matcher(paramString1);
/* 132 */     Matcher matcher2 = Pattern.compile(paramString3).matcher(paramString1);
/* 133 */     while (matcher1.find()) {
/* 134 */       arrayList1.add(Integer.valueOf(matcher1.start()));
/*     */     }
/* 136 */     while (matcher2.find()) {
/* 137 */       arrayList2.add(Integer.valueOf(matcher2.start()));
/*     */     }
/* 139 */     for (byte b = 0; b < arrayList1.size(); b++) {
/* 140 */       String str = paramString1.substring(((Integer)arrayList1.get(b)).intValue(), ((Integer)arrayList2.get(b)).intValue() + paramString3.length());
/* 141 */       arrayList.add(str);
/*     */     } 
/*     */     
/* 144 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String formatRequestname(String paramString1, String paramString2, String paramString3, int paramInt1, int paramInt2, int paramInt3) {
/* 163 */     String str1 = Util.null2String((new WorkflowAllComInfo()).getTitleset(paramString2));
/* 164 */     String str2 = "";
/* 165 */     if ("1".equals(str1)) {
/* 166 */       WfListCusTitleUtil wfListCusTitleUtil = new WfListCusTitleUtil();
/* 167 */       str2 = wfListCusTitleUtil.getCusTitle(Util.getIntValue(paramString3, -1), paramInt3);
/*     */     } else {
/*     */       
/* 170 */       MailAndMessage mailAndMessage = new MailAndMessage();
/* 171 */       String str = mailAndMessage.getTitle(Util.getIntValue(paramString3, -1), Util.getIntValue(paramString2, -1), paramInt2, paramInt3, paramInt1);
/* 172 */       if (!"".equals(str)) {
/* 173 */         str2 = paramString1 + "<B>（" + str + "）</B>";
/*     */       } else {
/* 175 */         str2 = paramString1;
/*     */       } 
/*     */     } 
/*     */     
/* 179 */     str2 = Util.StringReplace(str2, "\n", " ");
/* 180 */     str2 = Util.StringReplace(str2, "\r", "");
/* 181 */     str2 = Util.StringReplace(str2, "<br>", "");
/* 182 */     str2 = Util.StringReplace(str2, "<br />", "");
/* 183 */     String str3 = Util.delHtmlWithSpace(str2).replaceAll(" ", "");
/* 184 */     if ("".equals(str3)) {
/* 185 */       str2 = paramString1;
/*     */     }
/* 187 */     return str2;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getA2BStr(String paramString1, String paramString2, String paramString3) {
/* 198 */     String str = "";
/* 199 */     int i = paramString1.indexOf(paramString2);
/* 200 */     if (i != -1) {
/* 201 */       int j = paramString1.substring(i + paramString2.length()).indexOf(paramString3);
/* 202 */       if (j != -1) {
/* 203 */         str = paramString1.substring(i, i + j + paramString2.length() + paramString3.length());
/*     */       }
/*     */     } 
/* 206 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getFieldid(String paramString) {
/* 218 */     String str = "";
/* 219 */     if (paramString != null && !"".equals(paramString))
/* 220 */       str = paramString.substring(paramString.indexOf("\"$") + 2, paramString.indexOf("$\"")); 
/* 221 */     return str;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/RequestBaseUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */