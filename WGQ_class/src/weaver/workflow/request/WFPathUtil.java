/*     */ package weaver.workflow.request;
/*     */ 
/*     */ import com.engine.workflow.biz.requestForm.RequestFormBiz;
/*     */ import com.engine.workflow.util.ChuanyueUtil;
/*     */ import java.util.Calendar;
/*     */ import java.util.List;
/*     */ import java.util.concurrent.ExecutorService;
/*     */ import java.util.concurrent.Executors;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.cowork.CoworkDAO;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.mobile.webservices.workflow.soa.RequestPreProcessing;
/*     */ import weaver.system.SystemComInfo;
/*     */ import weaver.workflow.msg.MsgPushUtil;
/*     */ import weaver.workflow.msg.entity.MsgEntity;
/*     */ import weaver.workflow.workflow.WFManager;
/*     */ import weaver.workflow.workflow.WorkflowComInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class WFPathUtil
/*     */   extends BaseBean
/*     */ {
/*  39 */   private static final int[] cptypeArray = new int[] { 30, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 97, 98, 99 };
/*     */ 
/*     */   
/*  42 */   private static ExecutorService fixedThreadPool = null;
/*  43 */   private static ExecutorService msgThreadPool = null;
/*  44 */   private static ExecutorService subRequestThreadPool = null;
/*  45 */   private static int count = 10;
/*     */   static {
/*     */     try {
/*  48 */       int i = Util.getIntValue(Util.null2String((new WFPathUtil()).getPropValue("ThreadPoolConfig", "threadcount")));
/*  49 */       if (i < 5) {
/*  50 */         i = 5;
/*     */       }
/*  52 */       fixedThreadPool = Executors.newFixedThreadPool(i);
/*  53 */       msgThreadPool = Executors.newFixedThreadPool(count);
/*  54 */       subRequestThreadPool = Executors.newFixedThreadPool(count);
/*  55 */     } catch (Exception exception) {
/*  56 */       exception.printStackTrace();
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static RecordSet getNodeLinkrs(int paramInt1, int paramInt2, int paramInt3, int paramInt4, RecordSet paramRecordSet) {
/*     */     String str;
/*  70 */     if (paramRecordSet == null) {
/*  71 */       paramRecordSet = new RecordSet();
/*     */     }
/*     */ 
/*     */     
/*  75 */     if (paramInt4 == 1) {
/*  76 */       str = "select * from workflow_nodelink where workflowid=" + paramInt1 + " and nodeid=" + paramInt3 + " and isreject='1' order by linkorder ,id";
/*     */     } else {
/*  78 */       str = "select * from (select * from workflow_nodelink where workflowid=" + paramInt1 + " and nodeid=" + paramInt3 + " and (workflow_nodelink.wfrequestid is null or workflow_nodelink.wfrequestid ='') and (isreject is null or isreject !='1' ) and EXISTS (select 1 from workflow_nodebase b where workflow_nodelink.destnodeid=b.id and ((b.requestid=" + paramInt2 + " and b.IsFreeNode='1') or (b.IsFreeNode is null or b.IsFreeNode!='1')))) t order by linkorder, id";
/*  79 */       if (paramRecordSet.getDBType().equalsIgnoreCase("postgresql")) {
/*  80 */         str = "select * from (select * from workflow_nodelink where workflowid=" + paramInt1 + " and nodeid=" + paramInt3 + " and (workflow_nodelink.wfrequestid is null) and (isreject is null or isreject !='1' ) and EXISTS (select 1 from workflow_nodebase b where workflow_nodelink.destnodeid=b.id and ((b.requestid=" + paramInt2 + " and b.IsFreeNode='1') or (b.IsFreeNode is null or b.IsFreeNode!='1')))) t order by linkorder, id";
/*     */       }
/*     */     } 
/*     */     
/*  84 */     paramRecordSet.executeSql(str);
/*     */     
/*  86 */     return paramRecordSet;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean isContinuousProcessing(int paramInt) {
/* 104 */     for (byte b = 0; b < cptypeArray.length; b++) {
/* 105 */       if (paramInt == cptypeArray[b]) return true;
/*     */     
/*     */     } 
/* 108 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean hasNodeLinkTips(String paramString1, String paramString2) {
/* 118 */     RecordSet recordSet = new RecordSet();
/* 119 */     boolean bool = recordSet.getDBType().equals("oracle");
/*     */     
/* 121 */     String str1 = "";
/* 122 */     String str2 = "select 1 from workflow_nodelink where workflowid=" + paramString1 + " and nodeid=" + paramString2 + " and tipsinfo is not null ";
/* 123 */     if (!bool) {
/* 124 */       str2 = str2 + " and tipsinfo<>'' ";
/*     */     }
/*     */     
/* 127 */     recordSet.executeSql(str2);
/* 128 */     if (recordSet.next()) {
/* 129 */       return true;
/*     */     }
/*     */     
/* 132 */     return false;
/*     */   }
/*     */   
/*     */   public static void updateFormSignature(int paramInt1, int paramInt2, int paramInt3, int paramInt4, String paramString) {
/* 136 */     String str = getFormValMD5(paramInt1, paramInt2, paramInt3, paramInt4, paramString, 0);
/* 137 */     if (!"".equals(str)) {
/* 138 */       RecordSet recordSet = new RecordSet();
/* 139 */       recordSet.executeSql("update workflow_requestbase set formsignaturemd5='" + str + "' where requestid=" + paramInt2);
/*     */     } 
/*     */   }
/*     */   
/*     */   public static String getFormValMD5(int paramInt1, int paramInt2, int paramInt3, int paramInt4, String paramString) {
/* 144 */     return getFormValMD5(paramInt1, paramInt2, paramInt3, paramInt4, paramString, 0);
/*     */   }
/*     */   
/*     */   public static String getFormValMD5(int paramInt1, int paramInt2, int paramInt3, int paramInt4, String paramString, int paramInt5) {
/* 148 */     WFPathUtil wFPathUtil = new WFPathUtil();
/*     */ 
/*     */     
/*     */     try {
/* 152 */       String str6 = Util.null2String(wFPathUtil.getPropValue("mobileClientWorkflowCache", "iscache"));
/*     */       
/* 154 */       String str7 = Util.null2String(wFPathUtil.getPropValue("mobileClientWorkflowCache", "cachewfids"));
/*     */ 
/*     */ 
/*     */       
/* 158 */       if (!"1".equals(str6) || ("," + str7 + ",").indexOf("," + paramInt1 + ",") == -1) {
/* 159 */         return "";
/*     */       }
/*     */     }
/* 162 */     catch (Exception exception) {
/* 163 */       wFPathUtil.writeLog("unable to get through the configuration.!");
/* 164 */       exception.printStackTrace();
/*     */     } 
/*     */ 
/*     */     
/* 168 */     String str1 = "";
/* 169 */     RecordSet recordSet1 = new RecordSet();
/*     */     
/* 171 */     String str2 = "";
/* 172 */     String str3 = "";
/*     */     
/* 174 */     String str4 = "";
/*     */     
/* 176 */     if (paramInt3 == 1) {
/*     */ 
/*     */       
/* 179 */       recordSet1.executeSql("select * from workflow_bill where id =" + paramInt4);
/* 180 */       recordSet1.next();
/* 181 */       str3 = Util.null2String(recordSet1.getString("tablename"));
/*     */ 
/*     */       
/* 184 */       str2 = "select fieldname from workflow_billfield where billid=" + paramInt4 + " and (viewtype is null or viewtype<>1) ";
/* 185 */       recordSet1.executeSql(str2);
/*     */     } else {
/* 187 */       str3 = "workflow_form";
/* 188 */       str2 = "select fieldname from workflow_formdict where id in (select fieldid from workflow_formfield where formid=" + paramInt4 + " and (isdetail<>1 or isdetail is null))";
/* 189 */       recordSet1.executeSql(str2);
/*     */     } 
/*     */     
/* 192 */     while (recordSet1.next()) {
/* 193 */       str4 = str4 + "," + Util.null2String(recordSet1.getString(1));
/*     */     }
/*     */ 
/*     */     
/* 197 */     String str5 = "";
/* 198 */     if (!"".equals(paramString)) {
/* 199 */       str5 = ",requestname:" + paramString;
/*     */     }
/*     */     
/* 202 */     str5 = str5 + ",currentnodeid:" + paramInt5;
/*     */     
/* 204 */     if (!"".equals(str4)) {
/* 205 */       str4 = str4.substring(1);
/*     */       
/* 207 */       str2 = "select " + str4 + " from " + str3 + " where requestid=" + paramInt2;
/* 208 */       recordSet1.executeSql(str2);
/* 209 */       int i = recordSet1.getColCounts();
/* 210 */       if (recordSet1.next()) {
/* 211 */         for (byte b = 1; b <= i; b++) {
/* 212 */           String str6 = Util.null2String(recordSet1.getString(b));
/* 213 */           String str7 = Util.null2String(recordSet1.getColumnName(b));
/* 214 */           if (!"".equals(str7)) {
/* 215 */             str5 = str5 + "," + str7 + ":" + str6;
/*     */           }
/*     */         } 
/*     */       }
/*     */     } 
/* 220 */     str1 = str3;
/*     */     
/* 222 */     RecordSet recordSet2 = new RecordSet();
/* 223 */     if (paramInt3 == 1) {
/* 224 */       recordSet1.executeSql("select * from Workflow_billdetailtable where billid =" + paramInt4);
/* 225 */       while (recordSet1.next()) {
/* 226 */         str3 = Util.null2String(recordSet1.getString("tablename"));
/* 227 */         str4 = "";
/*     */         
/* 229 */         str2 = "select fieldname from workflow_billfield where billid=" + paramInt4 + " and viewtype is not null and viewtype=1 and detailtable='" + str3 + "'";
/* 230 */         recordSet2.executeSql(str2);
/* 231 */         while (recordSet2.next()) {
/* 232 */           str4 = str4 + "," + Util.null2String(recordSet2.getString(1));
/*     */         }
/*     */         
/* 235 */         if (!"".equals(str4)) {
/* 236 */           str4 = str4.substring(1);
/*     */           
/* 238 */           str2 = "select " + str4 + " from " + str3 + " where mainid in( select id from " + str1 + " where requestid=" + paramInt2 + ")";
/* 239 */           recordSet2.executeSql(str2);
/* 240 */           int i = recordSet2.getColCounts();
/* 241 */           while (recordSet2.next()) {
/* 242 */             for (byte b = 1; b <= i; b++) {
/* 243 */               String str6 = Util.null2String(recordSet2.getString(b));
/* 244 */               String str7 = Util.null2String(recordSet2.getColumnName(b));
/* 245 */               if (!"".equals(str7)) {
/* 246 */                 str5 = str5 + "," + str7 + ":" + str6;
/*     */               }
/*     */             } 
/*     */           } 
/*     */         } 
/*     */       } 
/*     */     } else {
/* 253 */       str3 = "workflow_formdetail";
/* 254 */       str4 = "";
/* 255 */       str2 = "select fieldname from workflow_formdictdetail where id in (select fieldid from workflow_formfield where formid=" + paramInt4 + " and (isdetail=1 and isdetail is not null))";
/* 256 */       recordSet1.executeSql(str2);
/* 257 */       while (recordSet1.next()) {
/* 258 */         str4 = str4 + "," + Util.null2String(recordSet1.getString(1));
/*     */       }
/*     */       
/* 261 */       if (!"".equals(str4)) {
/* 262 */         str4 = str4.substring(1);
/*     */         
/* 264 */         str2 = "select " + str4 + " from " + str3 + " where requestid=" + paramInt2;
/* 265 */         recordSet2.executeSql(str2);
/* 266 */         int i = recordSet2.getColCounts();
/* 267 */         while (recordSet2.next()) {
/* 268 */           for (byte b = 1; b <= i; b++) {
/* 269 */             String str6 = Util.null2String(recordSet2.getString(b));
/* 270 */             String str7 = Util.null2String(recordSet2.getColumnName(b));
/* 271 */             if (!"".equals(str7)) {
/* 272 */               str5 = str5 + "," + str7 + ":" + str6;
/*     */             }
/*     */           } 
/*     */         } 
/*     */       } 
/*     */     } 
/*     */     
/* 279 */     if (!"".equals(str5)) {
/* 280 */       str5 = str5.substring(1);
/*     */     }
/* 282 */     return Util.getEncrypt(str5);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static void executewfread(User paramUser, int paramInt) throws Exception {
/* 293 */     SystemComInfo systemComInfo = new SystemComInfo();
/*     */     
/* 295 */     WFLinkInfo wFLinkInfo = new WFLinkInfo();
/* 296 */     RecordSet recordSet1 = new RecordSet();
/* 297 */     RecordSet recordSet2 = new RecordSet();
/* 298 */     WorkflowComInfo workflowComInfo = new WorkflowComInfo();
/* 299 */     WFUrgerManager wFUrgerManager = new WFUrgerManager();
/* 300 */     CoworkDAO coworkDAO = new CoworkDAO();
/*     */     
/* 302 */     WFManager wFManager = new WFManager();
/* 303 */     WFForwardManager wFForwardManager = new WFForwardManager();
/* 304 */     WFCoadjutantManager wFCoadjutantManager = new WFCoadjutantManager();
/*     */     
/* 306 */     boolean bool1 = systemComInfo.isUseOldWfMode();
/* 307 */     boolean bool2 = false;
/* 308 */     boolean bool3 = false;
/* 309 */     String str1 = "";
/* 310 */     String str2 = "";
/* 311 */     String str3 = "";
/* 312 */     String str4 = "0";
/* 313 */     int i = 0;
/* 314 */     int j = 0;
/* 315 */     int k = 0;
/* 316 */     int m = 0;
/* 317 */     String str5 = "";
/* 318 */     int n = 0;
/* 319 */     String str6 = "";
/* 320 */     int i1 = 0;
/* 321 */     int i2 = 0;
/* 322 */     String str7 = "";
/* 323 */     String str8 = "";
/* 324 */     String str9 = "";
/*     */     
/* 326 */     int i3 = 0;
/* 327 */     String str10 = "";
/* 328 */     String str11 = "";
/*     */     
/* 330 */     int i4 = paramUser.getUID();
/* 331 */     String str12 = paramUser.getLogintype();
/* 332 */     byte b = 0;
/* 333 */     if (str12.equals("1"))
/* 334 */       b = 0; 
/* 335 */     if (str12.equals("2"))
/* 336 */       b = 1; 
/* 337 */     int i5 = wFLinkInfo.getCurrentNodeid(paramInt, i4, Util.getIntValue(str12, 1));
/* 338 */     String str13 = wFLinkInfo.getNodeType(i5);
/*     */ 
/*     */     
/* 341 */     String str14 = "";
/* 342 */     int i6 = 0;
/*     */     
/* 344 */     boolean bool4 = false;
/* 345 */     boolean bool5 = false;
/* 346 */     boolean bool6 = false;
/* 347 */     boolean bool7 = false;
/* 348 */     boolean bool8 = false;
/* 349 */     boolean bool9 = false;
/* 350 */     boolean bool10 = false;
/* 351 */     boolean bool11 = false;
/* 352 */     boolean bool12 = true;
/*     */     
/* 354 */     int i7 = 0;
/*     */     
/* 356 */     String str15 = "";
/* 357 */     char c = Util.getSeparator();
/* 358 */     String str16 = "0";
/* 359 */     String str17 = "0";
/* 360 */     recordSet1.executeSql("select requestid from workflow_currentoperator where userid=" + i4 + " and usertype=" + b + " and requestid=" + paramInt);
/* 361 */     if (recordSet1.next()) {
/* 362 */       bool4 = true;
/*     */     }
/* 364 */     int i8 = -1;
/*     */     
/* 366 */     recordSet2.executeProc("workflow_Requestbase_SByID", paramInt + "");
/* 367 */     if (recordSet2.next()) {
/* 368 */       str8 = Util.null2String(recordSet2.getString("status"));
/* 369 */       str1 = Util.null2String(recordSet2.getString("requestname"));
/* 370 */       str2 = Util.null2String(recordSet2.getString("requestlevel"));
/* 371 */       str3 = Util.null2String(recordSet2.getString("requestmark"));
/* 372 */       i = Util.getIntValue(recordSet2.getString("creater"), 0);
/* 373 */       j = Util.getIntValue(recordSet2.getString("creatertype"), 0);
/* 374 */       k = Util.getIntValue(recordSet2.getString("deleted"), 0);
/* 375 */       n = Util.getIntValue(recordSet2.getString("workflowid"), 0);
/* 376 */       i6 = Util.getIntValue(recordSet2.getString("currentnodeid"), 0);
/* 377 */       if (i5 < 1)
/* 378 */         i5 = i6; 
/* 379 */       str14 = Util.null2String(recordSet2.getString("currentnodetype"));
/* 380 */       if (str13.equals(""))
/* 381 */         str13 = str14; 
/* 382 */       str9 = Util.null2String(recordSet2.getString("docCategory"));
/* 383 */       str7 = workflowComInfo.getWorkflowname(n + "");
/* 384 */       str6 = workflowComInfo.getWorkflowtype(n + "");
/*     */       
/* 386 */       i3 = Util.getIntValue(recordSet2.getString("lastOperator"), 0);
/* 387 */       str10 = Util.null2String(recordSet2.getString("lastOperateDate"));
/* 388 */       str11 = Util.null2String(recordSet2.getString("lastOperateTime"));
/* 389 */       i8 = Util.getIntValue(recordSet2.getString("currentstatus"), -1);
/*     */     } else {
/*     */       return;
/*     */     } 
/*     */     
/* 394 */     if (i == i4 && j == b) {
/* 395 */       bool4 = true;
/* 396 */       bool5 = true;
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/* 401 */     int i9 = -1;
/* 402 */     String str18 = "";
/* 403 */     String str19 = "";
/* 404 */     int i10 = 0;
/* 405 */     recordSet2.executeSql("select viewtype,isremark,isreminded,preisremark,id,groupdetailid,nodeid,(CASE WHEN isremark=9 THEN 7.5 WHEN isremark=11 THEN 0.98 WHEN (isremark=1 and takisremark=2) THEN 0.9 WHEN (preisremark=1 and takisremark=2) THEN 0.9 ELSE isremark END) orderisremark from workflow_currentoperator where requestid=" + paramInt + " and userid=" + i4 + " and usertype=" + b + " order by orderisremark,id ");
/* 406 */     boolean bool13 = false;
/* 407 */     while (recordSet2.next()) {
/* 408 */       String str = Util.null2String(recordSet2.getString("isremark"));
/* 409 */       str18 = str;
/* 410 */       str19 = Util.null2String(recordSet2.getString("viewtype"));
/* 411 */       i9 = Util.getIntValue(recordSet2.getString("preisremark"), 0);
/* 412 */       i7 = Util.getIntValue(recordSet2.getString("id"));
/* 413 */       i10 = Util.getIntValue(recordSet2.getString("groupdetailid"), 0);
/* 414 */       int i11 = Util.getIntValue(recordSet2.getString("nodeid"));
/*     */       
/* 416 */       if (str.equals("1") || str.equals("5") || str.equals("7") || str.equals("9") || (str.equals("0") && !str13.equals("3")) || str.equals("11")) {
/*     */         
/* 418 */         bool13 = true;
/* 419 */         bool4 = true;
/* 420 */         i5 = i11;
/* 421 */         str13 = wFLinkInfo.getNodeType(i5);
/*     */         break;
/*     */       } 
/* 424 */       if (str.equals("8")) {
/* 425 */         bool4 = true;
/*     */         break;
/*     */       } 
/* 428 */       bool4 = true;
/*     */     } 
/*     */     
/* 431 */     List<MsgEntity> list = dealReadMsg(str18, paramInt, n, i5, paramUser.getUID(), b, str19, i7);
/* 432 */     String str20 = "";
/* 433 */     if (str14.equals("3")) {
/* 434 */       str20 = "2";
/*     */     }
/* 436 */     else if ("1".equals(str18) || "0".equals(str18) || "7".equals(str18) || "8".equals(str18) || "9".equals(str18) || "11".equals(str18)) {
/* 437 */       str20 = "0";
/* 438 */     } else if ("2".equals(str18)) {
/* 439 */       str20 = "1";
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 445 */     if (!bool4)
/* 446 */       bool6 = wFUrgerManager.UrgerHaveWorkflowViewRight(paramInt, i4, Util.getIntValue(str12, 1)); 
/* 447 */     if (!bool4 && !bool6) {
/* 448 */       bool7 = wFUrgerManager.getMonitorViewRight(paramInt, i4);
/*     */     }
/* 450 */     if (!bool4 && !bool6 && !bool7 && !coworkDAO.haveRightToViewWorkflow(Integer.toString(i4), Integer.toString(paramInt)) && 
/* 451 */       !wFUrgerManager.UrgerHaveWorkflowViewRight(bool2, i4, Util.getIntValue(str12, 1)) && !wFUrgerManager.getMonitorViewRight(bool2, i4)) {
/*     */       return;
/*     */     }
/*     */ 
/*     */     
/* 456 */     wFManager.setWfid(n);
/* 457 */     wFManager.getWfInfo();
/*     */     
/* 459 */     recordSet2.executeProc("workflow_Workflowbase_SByID", n + "");
/*     */     
/* 461 */     if (recordSet2.next()) {
/* 462 */       str5 = Util.null2String(recordSet2.getString("isModifyLog"));
/* 463 */       i1 = Util.getIntValue(recordSet2.getString("formid"), 0);
/* 464 */       str4 = "" + Util.getIntValue(recordSet2.getString("isbill"), 0);
/* 465 */       i2 = Util.getIntValue(recordSet2.getString("helpdocid"), 0);
/*     */     } 
/* 467 */     recordSet2.executeSql("select issignmustinput from workflow_flownode where workflowid=" + n + " and nodeid=" + i5);
/* 468 */     if (recordSet2.next()) {
/* 469 */       str16 = "" + Util.getIntValue(recordSet2.getString("issignmustinput"), 0);
/*     */     }
/* 471 */     boolean bool14 = false;
/* 472 */     if (str18.equals("0")) {
/* 473 */       recordSet2.execute("select c1.id from workflow_currentoperator c1 where c1.isremark='2' and c1.preisremark='7' and c1.requestid=" + paramInt + " and exists(select 1 from workflow_currentoperator c2 where c2.id=" + i7 + " and c1.receivedate=c2.receivedate and c1.receivetime=c2.receivetime and c1.nodeid=c2.nodeid and c1.groupdetailid=c2.groupdetailid ) and exists(select id from workflow_groupdetail g where g.id=c1.groupdetailid and g.signtype='0')");
/*     */       
/* 475 */       if (recordSet2.next()) {
/* 476 */         int i11 = Util.getIntValue(recordSet2.getString("id"));
/* 477 */         if (i11 > 0) {
/* 478 */           bool14 = true;
/* 479 */           str18 = "2";
/* 480 */           bool13 = false;
/*     */         } 
/*     */       } 
/*     */     } 
/*     */     
/* 485 */     wFForwardManager.init();
/* 486 */     wFForwardManager.setWorkflowid(n);
/* 487 */     wFForwardManager.setNodeid(i5);
/* 488 */     wFForwardManager.setIsremark(str18);
/* 489 */     wFForwardManager.setRequestid(paramInt);
/* 490 */     wFForwardManager.setBeForwardid(i7);
/* 491 */     wFForwardManager.getWFNodeInfo();
/* 492 */     String str21 = wFForwardManager.getIsPendingForward();
/* 493 */     String str22 = wFForwardManager.getIsBeForward();
/* 494 */     String str23 = wFForwardManager.getIsSubmitedOpinion();
/* 495 */     String str24 = wFForwardManager.getIsSubmitForward();
/* 496 */     String str25 = wFForwardManager.getIsWaitForwardOpinion();
/* 497 */     String str26 = wFForwardManager.getIsBeForwardSubmit();
/* 498 */     String str27 = wFForwardManager.getIsBeForwardModify();
/* 499 */     String str28 = wFForwardManager.getIsBeForwardPending();
/* 500 */     String str29 = wFForwardManager.getIsBeForwardTodo();
/* 501 */     String str30 = wFForwardManager.getIsBeForwardSubmitAlready();
/* 502 */     String str31 = wFForwardManager.getIsBeForwardSubmitNotaries();
/* 503 */     String str32 = wFForwardManager.getIsFromWFRemark();
/* 504 */     boolean bool15 = wFForwardManager.getIsFreeWorkflow(paramInt, i5, Util.getIntValue(str18));
/* 505 */     String str33 = wFForwardManager.getIsFreeNode(i5);
/* 506 */     boolean bool16 = wFForwardManager.getCanSubmit();
/* 507 */     boolean bool17 = wFForwardManager.getBeForwardCanSubmitOpinion();
/* 508 */     boolean bool18 = wFForwardManager.getCanModify();
/* 509 */     wFCoadjutantManager.getCoadjutantRights(i10);
/* 510 */     String str34 = wFCoadjutantManager.getSigntype();
/* 511 */     String str35 = wFCoadjutantManager.getIssubmitdesc();
/* 512 */     String str36 = wFCoadjutantManager.getIsforward();
/* 513 */     String str37 = wFCoadjutantManager.getIsmodify();
/* 514 */     String str38 = wFCoadjutantManager.getIspending();
/* 515 */     if (!bool18 && str37.equals("1"))
/* 516 */       bool18 = true; 
/* 517 */     if (i5 != i6 && str37.equals("1") && str18.equals("7"))
/* 518 */       bool18 = false; 
/* 519 */     boolean bool19 = wFCoadjutantManager.getCoadjutantCanSubmit(paramInt, i7, str18, str34);
/* 520 */     boolean bool20 = wFCoadjutantManager.isMainSubmitted();
/*     */     
/* 522 */     Calendar calendar = Calendar.getInstance();
/* 523 */     String str39 = Util.add0(calendar.get(1), 4) + "-" + Util.add0(calendar.get(2) + 1, 2) + "-" + Util.add0(calendar.get(5), 2);
/*     */     
/* 525 */     String str40 = Util.add0(calendar.get(11), 2) + ":" + Util.add0(calendar.get(12), 2) + ":" + Util.add0(calendar.get(13), 2);
/* 526 */     boolean bool21 = false;
/* 527 */     String str41 = str18;
/* 528 */     if ("0".equals(str18))
/* 529 */       bool21 = true; 
/* 530 */     boolean bool22 = ChuanyueUtil.getIsCanSubmit(paramInt, i4, b);
/* 531 */     if (str18.equals("8") || (str18.equals("11") && !bool22) || (str18.equals("1") && !bool16 && !str23.equals("1")) || ("7".equals(str18) && !bool19) || bool14) {
/*     */       
/* 533 */       if (str18.equals("1") && wFForwardManager.hasChildCanSubmit(paramInt + "", i4 + "")) {
/* 534 */         recordSet2.executeProc("workflow_CurrentOperator_Copy", paramInt + "" + c + i4 + c + b + "");
/* 535 */       } else if (str18.equals("8") || ("7".equals(str18) && !bool19) || bool14) {
/* 536 */         recordSet2.executeProc("workflow_CurrentOperator_Copy", paramInt + "" + c + i4 + c + b + "");
/* 537 */       } else if (str18.equals("11")) {
/* 538 */         String str = "2";
/* 539 */         if (str14.equals("3")) {
/* 540 */           str = "4";
/*     */         }
/* 542 */         if (recordSet2.getDBType().equals("oracle")) {
/* 543 */           recordSet2.executeUpdate("update workflow_currentoperator set isremark=?,operatedate=to_char(sysdate,'yyyy-mm-dd'),operatetime=to_char(sysdate,'hh24:mi:ss') where requestid=? and userid=? and usertype=? and isremark in(1,7,8,9,11)", new Object[] { str, Integer.valueOf(paramInt), Integer.valueOf(i4), Integer.valueOf(b) });
/* 544 */         } else if (recordSet2.getDBType().equals("db2")) {
/* 545 */           recordSet2.executeUpdate("update workflow_currentoperator set isremark=?,operatedate=to_char(current date,'yyyy-mm-dd'),operatetime=to_char(current time,'hh24:mi:ss') where requestid=? and userid=? and usertype=? and isremark in(1,7,8,9,11)", new Object[] { str, Integer.valueOf(paramInt), Integer.valueOf(i4), Integer.valueOf(b) });
/* 546 */         } else if (recordSet2.getDBType().equals("mysql")) {
/* 547 */           recordSet2.executeUpdate("update workflow_currentoperator set isremark=?,operatedate=date_format(NOW(),'%Y-%m-%d'),operatetime=date_format(now(),'%H:%i:%s') where requestid=? and userid=? and usertype=? and isremark in(1,7,8,9,11)", new Object[] { str, Integer.valueOf(paramInt), Integer.valueOf(i4), Integer.valueOf(b) });
/*     */         }
/* 549 */         else if (recordSet2.getDBType().equals("postgresql")) {
/* 550 */           recordSet2.executeUpdate("update workflow_currentoperator set isremark=?,operatedate=to_char(now(),'yyyy-mm-dd'),operatetime=to_char(now(),'hh24:mi:ss') where requestid=? and userid=? and usertype=? and isremark in(1,7,8,9,11)", new Object[] { str, Integer.valueOf(paramInt), Integer.valueOf(i4), Integer.valueOf(b) });
/*     */         } else {
/*     */           
/* 553 */           recordSet2.executeUpdate("update workflow_currentoperator set isremark=?,operatedate=convert(char(10),getdate(),20),operatetime=convert(char(8),getdate(),108) where requestid=? and userid=? and usertype=? and isremark in(1,7,8,9,11)", new Object[] { str, Integer.valueOf(paramInt), Integer.valueOf(i4), Integer.valueOf(b) });
/*     */         } 
/*     */       } 
/* 556 */       if (str14.equals("3")) {
/* 557 */         recordSet2.executeSql("update workflow_currentoperator set iscomplete=1 where requestid=" + paramInt + " and userid=" + i4 + " and usertype=" + b);
/*     */       }
/* 559 */       bool21 = true;
/* 560 */       str18 = "2";
/* 561 */       bool13 = false;
/*     */       
/* 563 */       if (list != null) (new MsgPushUtil()).pushMsg(list); 
/*     */     } 
/* 565 */     if (str4.equals("1")) {
/* 566 */       recordSet2.executeProc("workflow_form_SByRequestid", paramInt + "");
/* 567 */       if (recordSet2.next()) {
/* 568 */         i1 = Util.getIntValue(recordSet2.getString("billformid"), 0);
/* 569 */         m = Util.getIntValue(recordSet2.getString("billid"));
/*     */       } 
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 585 */     if (((str18.equals("0") || str18.equals("1")) && !bool16) || (str18.equals("7") && !bool19) || (str18.equals("11") && !bool22))
/* 586 */       bool13 = false; 
/* 587 */     if (bool13 && !bool3 && !bool7) {
/* 588 */       if (recordSet2.getDBType().equals("oracle")) {
/* 589 */         recordSet2.executeSql("update workflow_currentoperator set viewtype=-2,operatedate=( case isremark when 2 then operatedate else to_char(sysdate,'yyyy-mm-dd') end  ) ,operatetime=( case isremark when 2 then operatetime else to_char(sysdate,'hh24:mi:ss') end  ) where requestid = " + paramInt + "  and userid =" + i4 + " and usertype = " + b);
/*     */       }
/* 591 */       else if (recordSet2.getDBType().equals("db2")) {
/* 592 */         recordSet2
/* 593 */           .executeSql("update workflow_currentoperator set viewtype=-2,operatedate=( case isremark when 2 then operatedate else to_char(current date,'yyyy-mm-dd') end ),operatetime=( case isremark when 2 then operatetime else to_char(current time,'hh24:mi:ss') end ) where requestid = " + paramInt + "  and userid =" + i4 + " and usertype = " + b);
/*     */       }
/* 595 */       else if (recordSet2.getDBType().equals("mysql")) {
/* 596 */         recordSet2.executeSql("update workflow_currentoperator set viewtype=-2,operatedate=( case isremark when 2 then operatedate else date_format(NOW(),'%Y-%m-%d') end  ) ,operatetime=( case isremark when 2 then operatetime else date_format(now(),'%H:%i:%s') end  ) where requestid = " + paramInt + "  and userid =" + i4 + " and usertype = " + b);
/*     */       }
/* 598 */       else if (recordSet2.getDBType().equals("postgresql")) {
/* 599 */         recordSet2.executeSql("update workflow_currentoperator set viewtype=-2,operatedate=( case isremark when 2 then operatedate else to_char(now(),'yyyy-mm-dd') end  ) ,operatetime=( case isremark when 2 then operatetime else to_char(now(),'hh24:mi:ss') end  ) where requestid = " + paramInt + "  and userid =" + i4 + " and usertype = " + b);
/*     */       } else {
/*     */         
/* 602 */         recordSet2.executeSql("update workflow_currentoperator set viewtype=-2,operatedate=( case isremark when 2 then operatedate else convert(char(10),getdate(),20) end ),operatetime=( case isremark when 2 then operatetime else convert(char(8),getdate(),108) end ) where requestid = " + paramInt + "  and userid =" + i4 + " and usertype = " + b);
/*     */       } 
/*     */ 
/*     */ 
/*     */       
/* 607 */       RequestFormBiz.updateLastViewDate(paramInt, i4, i5);
/*     */ 
/*     */       
/*     */       return;
/*     */     } 
/*     */ 
/*     */     
/* 614 */     if (!bool3) {
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 626 */       recordSet2.executeSql("update workflow_currentoperator set viewtype=-2 where requestid = " + paramInt + "  and userid =" + i4 + " and usertype = " + b + " and viewtype<>-2");
/*     */       
/* 628 */       RequestFormBiz.updateLastViewDate(paramInt, i4, i5);
/*     */ 
/*     */ 
/*     */       
/* 632 */       recordSet2.executeProc("workflow_CurOpe_UpdatebyView", "" + paramInt + c + i4 + c + b);
/*     */       
/* 634 */       if (!str14.equals("3")) {
/* 635 */         recordSet2.executeProc("SysRemindInfo_DeleteHasnewwf", "" + i4 + c + b + c + paramInt);
/*     */       } else {
/* 637 */         recordSet2.executeProc("SysRemindInfo_DeleteHasendwf", "" + i4 + c + b + c + paramInt);
/*     */       } 
/*     */     } 
/*     */     
/* 641 */     (new Thread((Runnable)new RequestPreProcessing(n, Util.getIntValue(str4), i1, paramInt, str1, "", i5, 0, false, "", paramUser, true))).start();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static List<MsgEntity> dealReadMsg(String paramString1, int paramInt1, int paramInt2, int paramInt3, int paramInt4, int paramInt5, String paramString2, int paramInt6) {
/* 654 */     if (!"0".equals(paramString2)) return null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 672 */     List<MsgEntity> list = null;
/* 673 */     if ("8".equals(paramString1)) {
/* 674 */       list = (new RequestOperationMsgManager()).getForwardReplyMsg(String.valueOf(paramInt1), String.valueOf(paramInt4));
/*     */     }
/* 676 */     return list;
/*     */   }
/*     */ 
/*     */   
/*     */   public ExecutorService getFixedThreadPool() {
/* 681 */     return fixedThreadPool;
/*     */   }
/*     */   
/*     */   public ExecutorService getMsgThreadPool() {
/* 685 */     return msgThreadPool;
/*     */   }
/*     */   public ExecutorService getSubRequestThreadPool() {
/* 688 */     return subRequestThreadPool;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/WFPathUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */