/*     */ package weaver.workflow.request;
/*     */ 
/*     */ import java.util.Calendar;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.file.FileUpload;
/*     */ import weaver.general.Util;
/*     */ import weaver.workflow.workflow.WFModeNodeFieldManager;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class RequestSaveCheckManager
/*     */ {
/*     */   public String getReturnMessage(RequestManager paramRequestManager, boolean paramBoolean, FileUpload paramFileUpload, HttpServletRequest paramHttpServletRequest) {
/*  35 */     String str1 = "";
/*     */ 
/*     */     
/*  38 */     if (!getHasEditItem(paramRequestManager)) {
/*  39 */       return str1;
/*     */     }
/*     */     
/*  42 */     int i = paramRequestManager.getRequestid();
/*  43 */     int j = paramRequestManager.getUserId();
/*  44 */     String str2 = paramRequestManager.getCurrentDate();
/*  45 */     String str3 = paramRequestManager.getCurrentTime();
/*     */     
/*  47 */     int k = 0;
/*  48 */     String str4 = "";
/*  49 */     String str5 = "";
/*  50 */     if (paramBoolean) {
/*  51 */       k = Util.getIntValue(paramHttpServletRequest.getParameter("lastOperator"), 0);
/*  52 */       str4 = Util.null2String(paramHttpServletRequest.getParameter("lastOperateDate"));
/*  53 */       str5 = Util.null2String(paramHttpServletRequest.getParameter("lastOperateTime"));
/*     */     } else {
/*  55 */       k = Util.getIntValue(paramFileUpload.getParameter("lastOperator"), 0);
/*  56 */       str4 = Util.null2String(paramFileUpload.getParameter("lastOperateDate"));
/*  57 */       str5 = Util.null2String(paramFileUpload.getParameter("lastOperateTime"));
/*     */     } 
/*     */ 
/*     */     
/*  61 */     int m = 0;
/*  62 */     String str6 = "";
/*  63 */     String str7 = "";
/*     */     
/*  65 */     RecordSet recordSet = new RecordSet();
/*  66 */     recordSet.executeSql("select lastOperator,lastOperateDate,lastOperateTime from workflow_requestbase where requestid=" + i);
/*  67 */     if (recordSet.next()) {
/*  68 */       m = Util.getIntValue(recordSet.getString("lastOperator"), 0);
/*  69 */       str6 = Util.null2String(recordSet.getString("lastOperateDate"));
/*  70 */       str7 = Util.null2String(recordSet.getString("lastOperateTime"));
/*     */     } 
/*  72 */     recordSet.executeSql("update workflow_requestbase set lastOperator=" + j + ",lastOperateDate='" + str2 + "',lastOperateTime='" + str3 + "' where requestid=" + i);
/*     */ 
/*     */     
/*  75 */     if (k > 0 && !str4.equals("") && !str5.equals("") && (
/*  76 */       k != m || !str4.equals(str6) || !str5.equals(str7))) {
/*  77 */       str1 = "8";
/*     */     }
/*     */ 
/*     */     
/*  81 */     return str1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getReturnMessage(int paramInt1, String paramString1, String paramString2, int paramInt2, int paramInt3, int paramInt4, int paramInt5) {
/*  96 */     String str1 = "";
/*     */ 
/*     */     
/*  99 */     if (!getHasEditItem(paramInt2, paramInt3)) {
/* 100 */       return str1;
/*     */     }
/*     */     
/* 103 */     Calendar calendar = Calendar.getInstance();
/* 104 */     String str2 = Util.add0(calendar.get(1), 4) + "-" + Util.add0(calendar.get(2) + 1, 2) + "-" + Util.add0(calendar.get(5), 2);
/*     */ 
/*     */ 
/*     */     
/* 108 */     String str3 = Util.add0(calendar.get(11), 2) + ":" + Util.add0(calendar.get(12), 2) + ":" + Util.add0(calendar.get(13), 2);
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 113 */     int i = 0;
/* 114 */     String str4 = "";
/* 115 */     String str5 = "";
/*     */     
/* 117 */     RecordSet recordSet = new RecordSet();
/* 118 */     recordSet.executeSql("select lastOperator,lastOperateDate,lastOperateTime from workflow_requestbase where requestid=" + paramInt5);
/* 119 */     if (recordSet.next()) {
/* 120 */       i = Util.getIntValue(recordSet.getString("lastOperator"), 0);
/* 121 */       str4 = Util.null2String(recordSet.getString("lastOperateDate"));
/* 122 */       str5 = Util.null2String(recordSet.getString("lastOperateTime"));
/*     */     } 
/* 124 */     recordSet.executeSql("update workflow_requestbase set lastOperator=" + paramInt4 + ",lastOperateDate='" + str2 + "',lastOperateTime='" + str3 + "' where requestid=" + paramInt5);
/*     */ 
/*     */     
/* 127 */     if (paramInt1 > 0 && !paramString1.equals("") && !paramString2.equals("") && (
/* 128 */       paramInt1 != i || !paramString1.equals(str4) || !paramString2.equals(str5))) {
/* 129 */       str1 = "24676";
/*     */     }
/*     */     
/* 132 */     return str1;
/*     */   }
/*     */ 
/*     */   
/*     */   private boolean getHasEditItem(RequestManager paramRequestManager) {
/* 137 */     int i = paramRequestManager.getWorkflowid();
/* 138 */     int j = paramRequestManager.getNodeid();
/* 139 */     return getHasEditItem(i, j);
/*     */   }
/*     */   
/*     */   public static boolean getHasEditItem(int paramInt1, int paramInt2) {
/* 143 */     WFModeNodeFieldManager wFModeNodeFieldManager = new WFModeNodeFieldManager();
/* 144 */     boolean bool = wFModeNodeFieldManager.getIsModeByWorkflowIdAndNodeId(paramInt1, paramInt2);
/* 145 */     RecordSet recordSet = new RecordSet();
/* 146 */     if (bool) {
/* 147 */       recordSet.executeSql("select count(nodeid) from workflow_modeview where isedit = '1' and nodeid = " + paramInt2);
/*     */     } else {
/* 149 */       recordSet.executeSql("select count(nodeid) from workflow_nodeform where isedit = '1' and nodeid = " + paramInt2);
/*     */     } 
/* 151 */     if (recordSet.next()) {
/* 152 */       return (Util.getIntValue(recordSet.getString(1), 0) > 0);
/*     */     }
/* 154 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getReturnMessageForTipsinfo(RequestManagerForTipsinfo paramRequestManagerForTipsinfo, boolean paramBoolean, FileUpload paramFileUpload, HttpServletRequest paramHttpServletRequest) {
/* 167 */     String str1 = "";
/*     */ 
/*     */     
/* 170 */     if (!getHasEditItemForTipsinfo(paramRequestManagerForTipsinfo)) {
/* 171 */       return str1;
/*     */     }
/*     */     
/* 174 */     int i = paramRequestManagerForTipsinfo.getRequestid();
/* 175 */     int j = paramRequestManagerForTipsinfo.getUserId();
/* 176 */     String str2 = paramRequestManagerForTipsinfo.getCurrentDate();
/* 177 */     String str3 = paramRequestManagerForTipsinfo.getCurrentTime();
/*     */     
/* 179 */     int k = 0;
/* 180 */     String str4 = "";
/* 181 */     String str5 = "";
/* 182 */     if (paramBoolean) {
/* 183 */       k = Util.getIntValue(paramHttpServletRequest.getParameter("lastOperator"), 0);
/* 184 */       str4 = Util.null2String(paramHttpServletRequest.getParameter("lastOperateDate"));
/* 185 */       str5 = Util.null2String(paramHttpServletRequest.getParameter("lastOperateTime"));
/*     */     } else {
/* 187 */       k = Util.getIntValue(paramFileUpload.getParameter("lastOperator"), 0);
/* 188 */       str4 = Util.null2String(paramFileUpload.getParameter("lastOperateDate"));
/* 189 */       str5 = Util.null2String(paramFileUpload.getParameter("lastOperateTime"));
/*     */     } 
/*     */     
/* 192 */     int m = 0;
/* 193 */     String str6 = "";
/* 194 */     String str7 = "";
/*     */     
/* 196 */     RecordSet recordSet = new RecordSet();
/* 197 */     recordSet.executeSql("select lastOperator,lastOperateDate,lastOperateTime from workflow_requestbase where requestid=" + i);
/* 198 */     if (recordSet.next()) {
/* 199 */       m = Util.getIntValue(recordSet.getString("lastOperator"), 0);
/* 200 */       str6 = Util.null2String(recordSet.getString("lastOperateDate"));
/* 201 */       str7 = Util.null2String(recordSet.getString("lastOperateTime"));
/*     */     } 
/*     */ 
/*     */     
/* 205 */     if (k > 0 && !str4.equals("") && !str5.equals("") && (
/* 206 */       k != m || !str4.equals(str6) || !str5.equals(str7))) {
/* 207 */       str1 = "8";
/*     */     }
/*     */ 
/*     */     
/* 211 */     return str1;
/*     */   }
/*     */   
/*     */   private boolean getHasEditItemForTipsinfo(RequestManagerForTipsinfo paramRequestManagerForTipsinfo) {
/* 215 */     int i = paramRequestManagerForTipsinfo.getWorkflowid();
/* 216 */     int j = paramRequestManagerForTipsinfo.getNodeid();
/* 217 */     return getHasEditItem(i, j);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/RequestSaveCheckManager.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */