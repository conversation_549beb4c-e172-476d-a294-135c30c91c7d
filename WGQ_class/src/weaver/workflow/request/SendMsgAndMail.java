/*     */ package weaver.workflow.request;
/*     */ 
/*     */ import com.engine.workflow.util.GetCustomLevelUtil;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.conn.RecordSetTrans;
/*     */ import weaver.email.EmailWorkRunnable;
/*     */ import weaver.file.FileUpload;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.GCONST;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.HrmUserVarify;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.wechat.SaveAndSendWechat;
/*     */ import weaver.wechat.cache.ReminderCache;
/*     */ import weaver.workflow.workflow.WFNodeDtlFieldManager;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class SendMsgAndMail
/*     */   extends BaseBean
/*     */ {
/*  43 */   private ResourceComInfo resourceComInfo = null;
/*  44 */   private ReminderCache reminderCache = null;
/*  45 */   private SaveAndSendWechat saveAndSendWechat = null;
/*  46 */   private WFNodeDtlFieldManager wFNodeDtlFieldManager = null;
/*     */   
/*  48 */   private String isIntervene = "0";
/*  49 */   private ArrayList InterveneOperators = null;
/*     */   
/*     */   public SendMsgAndMail() {
/*     */     try {
/*  53 */       this.resourceComInfo = new ResourceComInfo();
/*  54 */       this.reminderCache = new ReminderCache();
/*  55 */       this.saveAndSendWechat = new SaveAndSendWechat();
/*  56 */       this.wFNodeDtlFieldManager = new WFNodeDtlFieldManager();
/*  57 */     } catch (Exception exception) {}
/*     */   }
/*     */   
/*     */   public String getIsIntervene() {
/*  61 */     return this.isIntervene;
/*     */   }
/*     */   public void setIsIntervene(String paramString) {
/*  64 */     this.isIntervene = paramString;
/*     */   }
/*     */   public ArrayList getInterveneOperators() {
/*  67 */     return this.InterveneOperators;
/*     */   }
/*     */   public void setInterveneOperators(ArrayList paramArrayList) {
/*  70 */     this.InterveneOperators = paramArrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void sendMsg(RecordSetTrans paramRecordSetTrans, int paramInt1, int paramInt2, User paramUser, String paramString1, String paramString2) throws Exception {
/*  86 */     String str1 = " select b.archiveNoMsgAlert from workflow_requestbase a, workflow_base b where a.workflowid = b.id and a.currentnodetype = 3 and a.requestid=" + paramInt1;
/*  87 */     paramRecordSetTrans.executeSql(str1);
/*  88 */     if (paramRecordSetTrans.next()) {
/*  89 */       String str = Util.null2String(paramRecordSetTrans.getString("archiveNoMsgAlert"));
/*  90 */       if ("1".equals(str)) {
/*     */         return;
/*     */       }
/*     */     } 
/*     */ 
/*     */     
/*  96 */     int i = 0;
/*  97 */     String str2 = "";
/*  98 */     ArrayList<String> arrayList1 = new ArrayList();
/*  99 */     ArrayList<String> arrayList2 = new ArrayList();
/* 100 */     paramRecordSetTrans.executeSql("select messageType,requestname,requestlevel from workflow_requestbase where requestid=" + paramInt1);
/* 101 */     if (paramRecordSetTrans.next()) {
/* 102 */       i = paramRecordSetTrans.getInt("messageType");
/* 103 */       str2 = paramRecordSetTrans.getString("requestname");
/*     */     } 
/* 105 */     if (i == 1 || i == 2) {
/* 106 */       String str3 = "";
/* 107 */       String str4 = "";
/*     */       
/* 109 */       String str5 = "'0','1','4','8','9','7'";
/* 110 */       if (paramString1.equals("reject")) str5 = "'0','4','8','9','7'"; 
/* 111 */       if ("1".equals(this.isIntervene)) {
/* 112 */         str5 = str5 + ",'5'";
/*     */       }
/*     */       
/* 115 */       paramRecordSetTrans.executeSql("select userid,userType,isremark from workflow_currentoperator where requestid = " + paramInt1 + " and isremark in (" + str5 + ") and nodeid=" + paramInt2);
/* 116 */       while (paramRecordSetTrans.next()) {
/* 117 */         String str6 = paramRecordSetTrans.getString("userid");
/* 118 */         String str7 = paramRecordSetTrans.getString("userType");
/* 119 */         int j = paramRecordSetTrans.getInt("isremark");
/*     */         
/* 121 */         if ("1".equals(this.isIntervene) && null != this.InterveneOperators && 
/* 122 */           !this.InterveneOperators.contains(str6)) {
/*     */           continue;
/*     */         }
/*     */ 
/*     */         
/* 127 */         String str8 = "";
/* 128 */         String str9 = "";
/* 129 */         if (paramString1.equals("reject")) {
/* 130 */           str8 = SystemEnv.getHtmlLabelName(21784, paramUser.getLanguage());
/* 131 */           str9 = SystemEnv.getHtmlLabelName(21786, paramUser.getLanguage());
/*     */         } 
/* 133 */         if (paramString1.equals("submit") || paramString1.equals("intervenor")) {
/* 134 */           if (j == 0 || j == 5) {
/* 135 */             if (paramString2.equals("1")) {
/* 136 */               str8 = SystemEnv.getHtmlLabelName(21782, paramUser.getLanguage());
/*     */             } else {
/* 138 */               str8 = SystemEnv.getHtmlLabelName(21796, paramUser.getLanguage());
/* 139 */               str9 = SystemEnv.getHtmlLabelName(21797, paramUser.getLanguage());
/*     */             } 
/* 141 */           } else if (j == 4) {
/* 142 */             str8 = SystemEnv.getHtmlLabelName(21792, paramUser.getLanguage());
/* 143 */             str9 = SystemEnv.getHtmlLabelName(21793, paramUser.getLanguage());
/*     */           } else {
/* 145 */             str8 = SystemEnv.getHtmlLabelName(21783, paramUser.getLanguage());
/* 146 */             str9 = SystemEnv.getHtmlLabelName(21785, paramUser.getLanguage());
/*     */           } 
/*     */         }
/*     */         
/*     */         try {
/* 151 */           if ((i == 1 && !HrmUserVarify.isUserOnline(str6)) || i == 2) {
/* 152 */             String str10 = "";
/* 153 */             String str11 = "";
/* 154 */             if (!"1".equals(str7)) {
/* 155 */               str11 = "" + str6;
/* 156 */               str10 = this.resourceComInfo.getMobile(str6);
/*     */             } 
/* 158 */             if (!str10.equals("")) {
/* 159 */               str3 = str3 + "," + str10;
/* 160 */               str4 = str4 + "," + str11;
/* 161 */               arrayList1.add(str8);
/* 162 */               arrayList2.add(str9);
/*     */             } 
/*     */           } 
/* 165 */         } catch (Exception exception) {}
/*     */       } 
/* 167 */       if (!str3.equals("")) {
/* 168 */         str3 = str3.substring(1);
/* 169 */         str4 = str4.substring(1);
/*     */         
/* 171 */         (new Thread(new SmsWorkRunnable(str3, "", str4, "", str2, paramUser, paramInt1, arrayList1, arrayList2))).start();
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void sendMail(RecordSetTrans paramRecordSetTrans, int paramInt1, int paramInt2, int paramInt3, HttpServletRequest paramHttpServletRequest, FileUpload paramFileUpload, boolean paramBoolean, String paramString1, String paramString2, User paramUser) throws Exception {
/* 199 */     String str1 = " select b.archiveNoMailAlert from workflow_requestbase a, workflow_base b where a.workflowid = b.id and a.currentnodetype = 3 and a.requestid=" + paramInt2;
/* 200 */     paramRecordSetTrans.executeSql(str1);
/* 201 */     if (paramRecordSetTrans.next()) {
/* 202 */       String str = Util.null2String(paramRecordSetTrans.getString("archiveNoMailAlert"));
/* 203 */       if ("1".equals(str)) {
/*     */         return;
/*     */       }
/*     */     } 
/*     */     
/* 208 */     String str2 = "0";
/* 209 */     String str3 = "-1";
/* 210 */     String str4 = "";
/* 211 */     String str5 = "";
/* 212 */     String str6 = "";
/* 213 */     String str7 = "";
/* 214 */     String str8 = "";
/* 215 */     String str9 = "";
/* 216 */     String str10 = "";
/* 217 */     String str11 = "";
/* 218 */     String str12 = "";
/* 219 */     String str13 = "";
/* 220 */     String str14 = SystemEnv.getHtmlLabelName(15031, paramUser.getLanguage());
/*     */     
/* 222 */     String str15 = "";
/* 223 */     String str16 = "";
/* 224 */     int i = 0;
/* 225 */     paramRecordSetTrans.executeSql("select messageType,requestname,requestlevel from workflow_requestbase where requestid=" + paramInt2);
/* 226 */     if (paramRecordSetTrans.next()) {
/* 227 */       str15 = paramRecordSetTrans.getString("requestname");
/* 228 */       i = Util.getIntValue(paramRecordSetTrans.getString("requestlevel"), 0);
/* 229 */       str16 = str15;
/*     */     } 
/* 231 */     paramRecordSetTrans.executeSql("select mailMessageType from  workflow_base  where id = " + paramInt1);
/* 232 */     if (paramRecordSetTrans.next()) {
/* 233 */       str2 = paramRecordSetTrans.getString("mailMessageType");
/*     */     }
/* 235 */     if ("1".equals(str2)) {
/* 236 */       String str17 = ",";
/* 237 */       String str18 = "";
/* 238 */       paramRecordSetTrans.executeSql("select userId from workflow_requestUserdefault where noReceiveMailRemind='1' ");
/* 239 */       while (paramRecordSetTrans.next()) {
/* 240 */         str18 = paramRecordSetTrans.getString("userid");
/* 241 */         if (str18 != null && !str18.equals("")) {
/* 242 */           str17 = str17 + str18 + ",";
/*     */         }
/*     */       } 
/* 245 */       str14 = str15;
/* 246 */       str14 = str14 + "(" + Util.null2String(GetCustomLevelUtil.getLevel(i + "", paramUser.getLanguage())) + ")";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 256 */       String str19 = "";
/* 257 */       paramRecordSetTrans.executeSql("select oaaddress from systemset");
/* 258 */       if (paramRecordSetTrans.next()) str19 = paramRecordSetTrans.getString("oaaddress");
/*     */       
/* 260 */       String str20 = "'0','1','4','8','9','7'";
/* 261 */       if (paramString1.equals("reject")) str20 = "'0','4','8','9','7'"; 
/* 262 */       if ("1".equals(this.isIntervene)) {
/* 263 */         str20 = str20 + ",'5'";
/*     */       }
/*     */       
/* 266 */       paramRecordSetTrans.executeSql("select distinct isremark,userid,usertype from workflow_currentoperator where requestid = " + paramInt2 + " and isremark in (" + str20 + ") and nodeid=" + paramInt3);
/* 267 */       while (paramRecordSetTrans.next()) {
/* 268 */         str3 = paramRecordSetTrans.getString("userid");
/* 269 */         int j = paramRecordSetTrans.getInt("usertype");
/* 270 */         int k = paramRecordSetTrans.getInt("isremark");
/*     */         
/* 272 */         if ("1".equals(this.isIntervene) && null != this.InterveneOperators && 
/* 273 */           !this.InterveneOperators.contains(str3)) {
/*     */           continue;
/*     */         }
/*     */ 
/*     */         
/* 278 */         String str = "";
/* 279 */         if (j == 1) {
/* 280 */           RecordSet recordSet = new RecordSet();
/* 281 */           recordSet.executeSql("select email from CRM_CUSTOMERINFO where id =" + str3);
/* 282 */           if (recordSet.next()) {
/* 283 */             str = Util.null2String(recordSet.getString("email"));
/* 284 */             if (!"".equals(str)) {
/* 285 */               str9 = str9 + str + ",";
/* 286 */               if (k == 0 || k == 5) {
/* 287 */                 if (paramString2.equals("1")) {
/* 288 */                   str11 = str11 + str + ","; continue;
/*     */                 } 
/* 290 */                 str13 = str13 + str + ","; continue;
/* 291 */               }  if (k == 4) {
/* 292 */                 str12 = str12 + str + ","; continue;
/*     */               } 
/* 294 */               str10 = str10 + str + ",";
/*     */             } 
/*     */           } 
/*     */           continue;
/*     */         } 
/* 299 */         if (str17.indexOf("," + str3 + ",") == -1) {
/* 300 */           str = this.resourceComInfo.getEmail(str3);
/* 301 */           if (!"".equals(str) && str != null) {
/* 302 */             str4 = str4 + str + ",";
/* 303 */             if (k == 0 || k == 5) {
/* 304 */               if (paramString2.equals("1")) {
/* 305 */                 str6 = str6 + str + ","; continue;
/*     */               } 
/* 307 */               str8 = str8 + str + ","; continue;
/* 308 */             }  if (k == 4) {
/* 309 */               str7 = str7 + str + ","; continue;
/*     */             } 
/* 311 */             str5 = str5 + str + ",";
/*     */           } 
/*     */         } 
/*     */       } 
/*     */ 
/*     */ 
/*     */       
/* 318 */       if (paramHttpServletRequest != null || paramFileUpload != null || !"".equals(str19)) {
/* 319 */         String str21 = "";
/* 320 */         if (paramBoolean && paramHttpServletRequest != null) {
/* 321 */           str21 = Util.getRequestHost(paramHttpServletRequest);
/*     */         }
/* 323 */         else if (paramFileUpload != null) {
/* 324 */           str21 = Util.getRequestHost(paramFileUpload.getRequest());
/*     */         } 
/* 326 */         if (!"".equals(str19)) {
/*     */           
/* 328 */           str21 = str19;
/*     */         }
/*     */         else {
/*     */           
/* 332 */           str21 = "http://" + str21;
/*     */         } 
/* 334 */         String str22 = "login/Login.jsp";
/* 335 */         String str23 = "workflow/request/ViewRequest.jsp";
/* 336 */         if (GCONST.getMailReminderSet()) {
/* 337 */           str22 = GCONST.getMailLoginPage();
/* 338 */           str23 = GCONST.getMailGotoPage();
/*     */         } else {
/* 340 */           str22 = "login/LoginMail.jsp";
/*     */         } 
/*     */         
/* 343 */         if ("".equals(str22)) {
/* 344 */           str22 = "login/LoginMail.jsp";
/*     */         }
/*     */         
/* 347 */         if ("".equals(str23)) {
/* 348 */           str23 = "workflow/request/ViewRequest.jsp";
/*     */         }
/*     */         
/* 351 */         str15 = "(<a style='text-decoration: underline; color: blue;cursor:hand'  target='_blank' href=\"" + str21 + "/" + str22 + "?gopage=/" + str23 + "?requestid=" + paramInt2 + "\" >" + str15 + "</a>)";
/*     */         
/* 353 */         str16 = "(<a style='text-decoration: underline; color: blue;cursor:hand'  target='_blank' href=\"" + str21 + "/" + str22 + "?logintype=2&gopage=/" + str23 + "?requestid=" + paramInt2 + "\" >" + str16 + "</a>)";
/*     */       
/*     */       }
/* 356 */       else if (!"".equals(str19)) {
/* 357 */         String str21 = str19;
/* 358 */         String str22 = "login/Login.jsp";
/* 359 */         String str23 = "workflow/request/ViewRequest.jsp";
/* 360 */         if (GCONST.getMailReminderSet()) {
/* 361 */           str22 = GCONST.getMailLoginPage();
/* 362 */           str23 = GCONST.getMailGotoPage();
/*     */         } else {
/* 364 */           str22 = "login/LoginMail.jsp";
/*     */         } 
/*     */         
/* 367 */         if ("".equals(str22)) {
/* 368 */           str22 = "login/LoginMail.jsp";
/*     */         }
/*     */         
/* 371 */         if ("".equals(str23)) {
/* 372 */           str23 = "workflow/request/ViewRequest.jsp";
/*     */         }
/*     */         
/* 375 */         str15 = "(<a style='text-decoration: underline; color: blue;cursor:hand'  target='_blank' href=\"" + str21 + "/" + str22 + "?gopage=/" + str23 + "?requestid=" + paramInt2 + "\" >" + str15 + "</a>)";
/*     */         
/* 377 */         str16 = "(<a style='text-decoration: underline; color: blue;cursor:hand'  target='_blank' href=\"" + str21 + "/" + str22 + "?logintype=2&gopage=/" + str23 + "?requestid=" + paramInt2 + "\" >" + str16 + "</a>)";
/*     */       } else {
/*     */         
/* 380 */         str15 = "(" + str15 + ")";
/* 381 */         str16 = "(" + str16 + ")";
/*     */       } 
/*     */ 
/*     */       
/* 385 */       if (paramString1.equals("reject") && !"".equals(str4)) {
/* 386 */         String str21 = SystemEnv.getHtmlLabelName(21784, paramUser.getLanguage()) + "：" + str15 + SystemEnv.getHtmlLabelName(21786, paramUser.getLanguage());
/* 387 */         String str22 = SystemEnv.getHtmlLabelName(21784, paramUser.getLanguage()) + "：" + str14 + SystemEnv.getHtmlLabelName(21786, paramUser.getLanguage());
/* 388 */         String str23 = Util.null2String(getPropValue("MailMessage", "mailMSG.reject.title.begin"));
/* 389 */         String str24 = Util.null2String(getPropValue("MailMessage", "mailMSG.reject.title.end"));
/* 390 */         String str25 = Util.null2String(getPropValue("MailMessage", "mailMSG.reject.content.begin"));
/* 391 */         String str26 = Util.null2String(getPropValue("MailMessage", "mailMSG.reject.content.end"));
/* 392 */         str23 = new String(str23.getBytes("iso8859_1"));
/* 393 */         str24 = new String(str24.getBytes("iso8859_1"));
/* 394 */         str25 = new String(str25.getBytes("iso8859_1"));
/* 395 */         str26 = new String(str26.getBytes("iso8859_1"));
/* 396 */         if (!str23.equals("") || !str24.equals(""))
/* 397 */           str22 = str23 + str14 + str24; 
/* 398 */         if (!str25.equals("") || !str26.equals(""))
/* 399 */           str21 = str25 + str15 + str26; 
/* 400 */         SendRemindMail(str4, str22, str21);
/*     */       } 
/* 402 */       if (paramString1.equals("reject") && !"".equals(str9)) {
/* 403 */         String str21 = SystemEnv.getHtmlLabelName(21784, paramUser.getLanguage()) + "：" + str16 + SystemEnv.getHtmlLabelName(21786, paramUser.getLanguage());
/* 404 */         String str22 = SystemEnv.getHtmlLabelName(21784, paramUser.getLanguage()) + "：" + str14 + SystemEnv.getHtmlLabelName(21786, paramUser.getLanguage());
/* 405 */         SendRemindMail(str9, str22, str21);
/*     */       } 
/*     */       
/* 408 */       if (paramString1.equals("submit") || paramString1.equals("intervenor")) {
/* 409 */         if (!str5.equals("")) {
/* 410 */           String str21 = SystemEnv.getHtmlLabelName(21783, paramUser.getLanguage()) + "：" + str15 + SystemEnv.getHtmlLabelName(21785, paramUser.getLanguage());
/* 411 */           String str22 = SystemEnv.getHtmlLabelName(21783, paramUser.getLanguage()) + "：" + str14 + SystemEnv.getHtmlLabelName(21785, paramUser.getLanguage());
/* 412 */           String str23 = Util.null2String(getPropValue("MailMessage", "mailMSG.copy.title.begin"));
/* 413 */           String str24 = Util.null2String(getPropValue("MailMessage", "mailMSG.copy.title.end"));
/* 414 */           String str25 = Util.null2String(getPropValue("MailMessage", "mailMSG.copy.content.begin"));
/* 415 */           String str26 = Util.null2String(getPropValue("MailMessage", "mailMSG.copy.content.end"));
/* 416 */           str23 = new String(str23.getBytes("iso8859_1"));
/* 417 */           str24 = new String(str24.getBytes("iso8859_1"));
/* 418 */           str25 = new String(str25.getBytes("iso8859_1"));
/* 419 */           str26 = new String(str26.getBytes("iso8859_1"));
/* 420 */           if (!str23.equals("") || !str24.equals(""))
/* 421 */             str22 = str23 + str14 + str24; 
/* 422 */           if (!str25.equals("") || !str26.equals(""))
/* 423 */             str21 = str25 + str15 + str26; 
/* 424 */           SendRemindMail(str5, str22, str21);
/*     */         } 
/* 426 */         if (!str6.equals("")) {
/* 427 */           String str21 = SystemEnv.getHtmlLabelName(21782, paramUser.getLanguage()) + str15;
/* 428 */           String str22 = SystemEnv.getHtmlLabelName(21782, paramUser.getLanguage()) + str14;
/* 429 */           String str23 = Util.null2String(getPropValue("MailMessage", "mailMSG.approve.title.begin"));
/* 430 */           String str24 = Util.null2String(getPropValue("MailMessage", "mailMSG.approve.title.end"));
/* 431 */           String str25 = Util.null2String(getPropValue("MailMessage", "mailMSG.approve.content.begin"));
/* 432 */           String str26 = Util.null2String(getPropValue("MailMessage", "mailMSG.approve.content.end"));
/* 433 */           str23 = new String(str23.getBytes("iso8859_1"));
/* 434 */           str24 = new String(str24.getBytes("iso8859_1"));
/* 435 */           str25 = new String(str25.getBytes("iso8859_1"));
/* 436 */           str26 = new String(str26.getBytes("iso8859_1"));
/* 437 */           if (!str23.equals("") || !str24.equals(""))
/* 438 */             str22 = str23 + str14 + str24; 
/* 439 */           if (!str25.equals("") || !str26.equals(""))
/* 440 */             str21 = str25 + str15 + str26; 
/* 441 */           SendRemindMail(str6, str22, str21);
/*     */         } 
/* 443 */         if (!str8.equals("")) {
/* 444 */           String str21 = SystemEnv.getHtmlLabelName(21796, paramUser.getLanguage()) + "：" + str15 + SystemEnv.getHtmlLabelName(21797, paramUser.getLanguage());
/* 445 */           String str22 = SystemEnv.getHtmlLabelName(21796, paramUser.getLanguage()) + "：" + str14 + SystemEnv.getHtmlLabelName(21797, paramUser.getLanguage());
/* 446 */           String str23 = Util.null2String(getPropValue("MailMessage", "mailMSG.confirm.title.begin"));
/* 447 */           String str24 = Util.null2String(getPropValue("MailMessage", "mailMSG.confirm.title.end"));
/* 448 */           String str25 = Util.null2String(getPropValue("MailMessage", "mailMSG.confirm.content.begin"));
/* 449 */           String str26 = Util.null2String(getPropValue("MailMessage", "mailMSG.confirm.content.end"));
/* 450 */           str23 = new String(str23.getBytes("iso8859_1"));
/* 451 */           str24 = new String(str24.getBytes("iso8859_1"));
/* 452 */           str25 = new String(str25.getBytes("iso8859_1"));
/* 453 */           str26 = new String(str26.getBytes("iso8859_1"));
/* 454 */           if (!str23.equals("") || !str24.equals(""))
/* 455 */             str22 = str23 + str14 + str24; 
/* 456 */           if (!str25.equals("") || !str26.equals(""))
/* 457 */             str21 = str25 + str15 + str26; 
/* 458 */           SendRemindMail(str8, str22, str21);
/*     */         } 
/* 460 */         if (!str7.equals("")) {
/* 461 */           String str21 = SystemEnv.getHtmlLabelName(21792, paramUser.getLanguage()) + "：" + str15 + SystemEnv.getHtmlLabelName(21793, paramUser.getLanguage());
/* 462 */           String str22 = SystemEnv.getHtmlLabelName(21792, paramUser.getLanguage()) + "：" + str14 + SystemEnv.getHtmlLabelName(21793, paramUser.getLanguage());
/* 463 */           String str23 = Util.null2String(getPropValue("MailMessage", "mailMSG.processed.title.begin"));
/* 464 */           String str24 = Util.null2String(getPropValue("MailMessage", "mailMSG.processed.title.end"));
/* 465 */           String str25 = Util.null2String(getPropValue("MailMessage", "mailMSG.processed.content.begin"));
/* 466 */           String str26 = Util.null2String(getPropValue("MailMessage", "mailMSG.processed.content.end"));
/* 467 */           str23 = new String(str23.getBytes("iso8859_1"));
/* 468 */           str24 = new String(str24.getBytes("iso8859_1"));
/* 469 */           str25 = new String(str25.getBytes("iso8859_1"));
/* 470 */           str26 = new String(str26.getBytes("iso8859_1"));
/* 471 */           if (!str23.equals("") || !str24.equals(""))
/* 472 */             str22 = str23 + str14 + str24; 
/* 473 */           if (!str25.equals("") || !str26.equals(""))
/* 474 */             str21 = str25 + str15 + str26; 
/* 475 */           SendRemindMail(str7, str22, str21);
/*     */         } 
/*     */         
/* 478 */         if (!str10.equals("")) {
/* 479 */           String str21 = SystemEnv.getHtmlLabelName(21783, paramUser.getLanguage()) + "：" + str16 + SystemEnv.getHtmlLabelName(21785, paramUser.getLanguage());
/* 480 */           String str22 = SystemEnv.getHtmlLabelName(21783, paramUser.getLanguage()) + "：" + str14 + SystemEnv.getHtmlLabelName(21785, paramUser.getLanguage());
/* 481 */           SendRemindMail(str10, str22, str21);
/*     */         } 
/* 483 */         if (!str11.equals("")) {
/* 484 */           String str21 = SystemEnv.getHtmlLabelName(21782, paramUser.getLanguage()) + str16;
/* 485 */           String str22 = SystemEnv.getHtmlLabelName(21782, paramUser.getLanguage()) + str14;
/* 486 */           SendRemindMail(str11, str22, str21);
/*     */         } 
/* 488 */         if (!str13.equals("")) {
/* 489 */           String str21 = SystemEnv.getHtmlLabelName(21796, paramUser.getLanguage()) + "：" + str16 + SystemEnv.getHtmlLabelName(21797, paramUser.getLanguage());
/* 490 */           String str22 = SystemEnv.getHtmlLabelName(21796, paramUser.getLanguage()) + "：" + str14 + SystemEnv.getHtmlLabelName(21797, paramUser.getLanguage());
/* 491 */           SendRemindMail(str13, str22, str21);
/*     */         } 
/* 493 */         if (!str12.equals("")) {
/* 494 */           String str21 = SystemEnv.getHtmlLabelName(21792, paramUser.getLanguage()) + "：" + str16 + SystemEnv.getHtmlLabelName(21793, paramUser.getLanguage());
/* 495 */           String str22 = SystemEnv.getHtmlLabelName(21792, paramUser.getLanguage()) + "：" + str14 + SystemEnv.getHtmlLabelName(21793, paramUser.getLanguage());
/* 496 */           SendRemindMail(str12, str22, str21);
/*     */         } 
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void SendRemindMail(String paramString1, String paramString2, String paramString3) {
/* 511 */     (new WFPathUtil()).getFixedThreadPool().execute((Runnable)new EmailWorkRunnable(paramString1, paramString2, paramString3));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void sendChats(RecordSetTrans paramRecordSetTrans, int paramInt1, int paramInt2, int paramInt3, User paramUser, String paramString1, String paramString2) throws Exception {
/* 522 */     int i = 0;
/* 523 */     String str1 = "";
/* 524 */     String str2 = "";
/* 525 */     paramRecordSetTrans.executeSql("select chatsType,requestname,requestlevel from workflow_requestbase where requestid=" + paramInt2);
/* 526 */     if (paramRecordSetTrans.next()) {
/* 527 */       i = paramRecordSetTrans.getInt("chatsType");
/* 528 */       str1 = paramRecordSetTrans.getString("requestname");
/*     */     } 
/* 530 */     String str3 = "'0','1','4','8','9','7'";
/* 531 */     if (paramString1.equals("reject")) str3 = "'0','4','8','9','7'"; 
/* 532 */     if ("1".equals(this.isIntervene)) {
/* 533 */       str3 = str3 + ",'5'";
/*     */     }
/* 535 */     if (i == 1) {
/* 536 */       StringBuffer stringBuffer1 = new StringBuffer();
/* 537 */       StringBuffer stringBuffer2 = new StringBuffer();
/* 538 */       StringBuffer stringBuffer3 = new StringBuffer();
/* 539 */       StringBuffer stringBuffer4 = new StringBuffer();
/* 540 */       StringBuffer stringBuffer5 = new StringBuffer();
/* 541 */       paramRecordSetTrans.executeSql("select userid,userType,isremark from workflow_currentoperator where requestid = " + paramInt2 + " and isremark in (" + str3 + ") and nodeid=" + paramInt3);
/* 542 */       while (paramRecordSetTrans.next()) {
/* 543 */         String str4 = paramRecordSetTrans.getString("userid");
/* 544 */         String str5 = paramRecordSetTrans.getString("userType");
/* 545 */         int j = paramRecordSetTrans.getInt("isremark");
/*     */         
/* 547 */         if ("1".equals(this.isIntervene) && null != this.InterveneOperators && 
/* 548 */           !this.InterveneOperators.contains(str4)) {
/*     */           continue;
/*     */         }
/*     */ 
/*     */         
/* 553 */         if (paramString1.equals("reject")) {
/* 554 */           stringBuffer5.append(str4);
/* 555 */           stringBuffer5.append(",");
/*     */         } 
/* 557 */         if (paramString1.equals("submit") || paramString1.equals("intervenor")) {
/* 558 */           if (j == 0 || j == 5) {
/* 559 */             if (paramString2.equals("1")) {
/* 560 */               stringBuffer1.append(str4);
/* 561 */               stringBuffer1.append(","); continue;
/*     */             } 
/* 563 */             stringBuffer2.append(str4);
/* 564 */             stringBuffer2.append(","); continue;
/*     */           } 
/* 566 */           if (j == 4) {
/* 567 */             stringBuffer3.append(str4);
/* 568 */             stringBuffer3.append(","); continue;
/*     */           } 
/* 570 */           stringBuffer4.append(str4);
/* 571 */           stringBuffer4.append(",");
/*     */         } 
/*     */       } 
/*     */ 
/*     */       
/* 576 */       if (stringBuffer1.indexOf(",") != -1) {
/* 577 */         str2 = ReminderCache.getReminderStr(str1, "wf_approve", false);
/* 578 */         sendChatsInfo(paramRecordSetTrans, paramInt1, paramInt2, paramString2, str2, stringBuffer1.toString());
/*     */       } 
/* 580 */       if (stringBuffer2.indexOf(",") != -1) {
/* 581 */         str2 = ReminderCache.getReminderStr(str1, "wf_submit", false);
/* 582 */         sendChatsInfo(paramRecordSetTrans, paramInt1, paramInt2, paramString2, str2, stringBuffer2.toString());
/*     */       } 
/* 584 */       if (stringBuffer3.indexOf(",") != -1) {
/* 585 */         str2 = ReminderCache.getReminderStr(str1, "wf_archive", false);
/* 586 */         sendChatsInfo(paramRecordSetTrans, paramInt1, paramInt2, paramString2, str2, stringBuffer3.toString());
/*     */       } 
/* 588 */       if (stringBuffer4.indexOf(",") != -1) {
/* 589 */         str2 = ReminderCache.getReminderStr(str1, "wf_notice", false);
/* 590 */         sendChatsInfo(paramRecordSetTrans, paramInt1, paramInt2, paramString2, str2, stringBuffer4.toString());
/*     */       } 
/* 592 */       if (stringBuffer5.indexOf(",") != -1) {
/* 593 */         str2 = ReminderCache.getReminderStr(str1, "wf_reject", false);
/* 594 */         sendChatsInfo(paramRecordSetTrans, paramInt1, paramInt2, paramString2, str2, stringBuffer5.toString());
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   private void sendChatsInfo(RecordSetTrans paramRecordSetTrans, int paramInt1, int paramInt2, String paramString1, String paramString2, String paramString3) throws Exception {
/* 601 */     if (paramString1.equals("3")) {
/* 602 */       int i = 0;
/* 603 */       paramRecordSetTrans.execute("select notRemindifArchived from workflow_base where id = " + paramInt1);
/* 604 */       while (paramRecordSetTrans.next()) {
/* 605 */         i = paramRecordSetTrans.getInt("notRemindifArchived");
/*     */       }
/* 607 */       if (i != 1) {
/* 608 */         HashMap<Object, Object> hashMap = new HashMap<>();
/* 609 */         hashMap.put("detailid", Integer.valueOf(paramInt2));
/* 610 */         this.saveAndSendWechat.setHrmid(paramString3);
/* 611 */         this.saveAndSendWechat.setMsg(paramString2);
/* 612 */         this.saveAndSendWechat.setMode(1);
/* 613 */         this.saveAndSendWechat.setParams(hashMap);
/* 614 */         this.saveAndSendWechat.send();
/*     */       } 
/*     */     } else {
/* 617 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 618 */       hashMap.put("detailid", Integer.valueOf(paramInt2));
/* 619 */       this.saveAndSendWechat.setHrmid(paramString3);
/* 620 */       this.saveAndSendWechat.setMsg(paramString2);
/* 621 */       this.saveAndSendWechat.setMode(1);
/* 622 */       this.saveAndSendWechat.setParams(hashMap);
/* 623 */       this.saveAndSendWechat.send();
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/SendMsgAndMail.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */