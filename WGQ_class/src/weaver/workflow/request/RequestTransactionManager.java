/*     */ package weaver.workflow.request;
/*     */ 
/*     */ import java.math.BigDecimal;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class RequestTransactionManager
/*     */   extends BaseBean
/*     */ {
/*     */   private int billid;
/*     */   private String currentdate;
/*     */   private int operator;
/*     */   
/*     */   public void resetParameter() {
/*  31 */     this.billid = 0;
/*  32 */     this.currentdate = "";
/*  33 */     this.operator = 0;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setBillid(int paramInt) {
/*  40 */     this.billid = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setCurrentdate(String paramString) {
/*  47 */     this.currentdate = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setOperator(int paramInt) {
/*  54 */     this.operator = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void insertTransaction() throws Exception {
/*  61 */     RecordSet recordSet = new RecordSet();
/*  62 */     String str1 = "";
/*  63 */     String str2 = "";
/*  64 */     String str3 = "";
/*  65 */     String str4 = "";
/*  66 */     String str5 = "";
/*  67 */     String str6 = "";
/*  68 */     String str7 = "";
/*  69 */     String str8 = "";
/*  70 */     String str9 = "";
/*  71 */     String str10 = "";
/*  72 */     String str11 = "";
/*  73 */     String str12 = "";
/*     */     
/*  75 */     String str13 = "";
/*  76 */     String str14 = "";
/*  77 */     String str15 = "";
/*     */     
/*  79 */     String str16 = "";
/*  80 */     byte b = 2;
/*  81 */     String str17 = "";
/*     */     try {
/*  83 */       str16 = "select * from bill_hrmfinance where id=" + this.billid;
/*  84 */       recordSet.executeSql(str16);
/*  85 */       recordSet.next();
/*  86 */       str1 = recordSet.getString("debitledgeid");
/*  87 */       str2 = recordSet.getString("debitremark");
/*  88 */       str3 = recordSet.getString("creditledgeid");
/*  89 */       str4 = recordSet.getString("creditremark");
/*  90 */       str5 = recordSet.getString("currencyid");
/*  91 */       str6 = recordSet.getString("exchangerate");
/*  92 */       str7 = recordSet.getString("crmid");
/*  93 */       str8 = recordSet.getString("projectid");
/*  94 */       str9 = recordSet.getString("accessory");
/*  95 */       str10 = recordSet.getString("resourceid");
/*  96 */       str11 = recordSet.getString("amount");
/*  97 */       str12 = recordSet.getString("description");
/*     */       
/*  99 */       str16 = "select * from hrmresource where id=" + str10;
/* 100 */       recordSet.executeSql(str16);
/* 101 */       recordSet.next();
/* 102 */       str13 = recordSet.getString("costcenterid");
/* 103 */       str14 = recordSet.getString("departmentid");
/* 104 */       str15 = recordSet.getString("currencyid");
/*     */       
/* 106 */       BigDecimal bigDecimal1 = new BigDecimal(str11);
/* 107 */       BigDecimal bigDecimal2 = new BigDecimal(str6);
/* 108 */       BigDecimal bigDecimal3 = new BigDecimal("0");
/* 109 */       bigDecimal3 = bigDecimal1.divide(bigDecimal2, 6);
/* 110 */       String str18 = "";
/* 111 */       recordSet.executeProc("FnaTransaction_SelectByMaxmark", "");
/* 112 */       if (recordSet.next()) {
/* 113 */         str18 = "" + (Util.getIntValue(recordSet.getString(1), 0) + 1);
/*     */       }
/*     */ 
/*     */       
/* 117 */       str17 = str18 + b + this.currentdate + b + str14 + b + str13 + b + str5 + b + str15 + b + str6 + b + str9 + b + str10 + b + str7 + b + "" + b + "" + b + str8 + b + str11 + b + str11 + b + bigDecimal3.toString() + b + bigDecimal3.toString() + b + str12 + b + "0" + b + this.operator + b + this.currentdate;
/*     */       
/* 119 */       recordSet.executeProc("FnaTransaction_Insert", str17);
/* 120 */       recordSet.next();
/* 121 */       String str19 = recordSet.getString(1);
/*     */       
/* 123 */       str16 = "update FnaTransaction set manual='1' where id=" + str19;
/* 124 */       recordSet.executeSql(str16);
/* 125 */       if (!str1.equals("0")) {
/*     */         
/* 127 */         str17 = str19 + b + str1 + b + str11 + b + bigDecimal3.toString() + b + "1" + b + str2;
/* 128 */         recordSet.executeProc("FnaTransactionDetail_Insert", str17);
/*     */       } 
/* 130 */       if (!str3.equals("0"))
/*     */       {
/* 132 */         str17 = str19 + b + str3 + b + str11 + b + bigDecimal3.toString() + b + "2" + b + str4;
/* 133 */         recordSet.executeProc("FnaTransactionDetail_Insert", str17);
/*     */       }
/*     */     
/* 136 */     } catch (Exception exception) {
/* 137 */       writeLog(exception);
/* 138 */       throw exception;
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/RequestTransactionManager.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */