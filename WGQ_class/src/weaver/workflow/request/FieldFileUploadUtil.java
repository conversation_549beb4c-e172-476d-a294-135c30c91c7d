/*     */ package weaver.workflow.request;
/*     */ 
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.docs.category.SecCategoryComInfo;
/*     */ import weaver.general.Util;
/*     */ import weaver.workflow.workflow.WFManager;
/*     */ import weaver.workflow.workflow.WorkflowComInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class FieldFileUploadUtil
/*     */ {
/*     */   public FileUploadDTO getFileUploadInfo(String paramString1, String paramString2, String paramString3) {
/*  19 */     FileUploadDTO fileUploadDTO = new FileUploadDTO();
/*  20 */     RequestBaseUtil requestBaseUtil = new RequestBaseUtil();
/*  21 */     RecordSet recordSet = new RecordSet();
/*     */ 
/*     */     
/*  24 */     WFManager wFManager = new WFManager();
/*  25 */     wFManager.setWfid(Integer.parseInt(paramString2));
/*     */     try {
/*  27 */       wFManager.getWfInfo();
/*  28 */     } catch (Exception exception) {
/*  29 */       exception.printStackTrace();
/*     */     } 
/*     */     
/*  32 */     int i = wFManager.getCatelogType();
/*  33 */     String str1 = wFManager.getDocCategory();
/*  34 */     int j = wFManager.getSelectedCateLog();
/*  35 */     String str2 = Util.null2String(wFManager.getLimitvalue()).trim();
/*     */ 
/*     */     
/*  38 */     recordSet.execute("select * from workflow_fileupload where workflowid = " + paramString2 + " and fieldid = " + paramString1);
/*  39 */     if (recordSet.next()) {
/*  40 */       String str8 = Util.null2String(recordSet.getString("limittype"));
/*  41 */       String str9 = Util.null2String(recordSet.getString("limitvalue")).trim();
/*  42 */       String str10 = Util.null2String(recordSet.getString("docCategory"));
/*  43 */       int m = Util.getIntValue(recordSet.getString("selectedCateLog"), 0);
/*     */       
/*  45 */       int n = Util.getIntValue(recordSet.getString("catelogType"), 9);
/*     */       
/*  47 */       if ("1".equals(str8)) {
/*  48 */         str2 = str9;
/*     */       }
/*  50 */       if (n != 9) {
/*  51 */         i = n;
/*  52 */         str1 = str10;
/*  53 */         j = m;
/*     */       } 
/*     */     } 
/*     */ 
/*     */     
/*  58 */     String str3 = "";
/*  59 */     String str4 = "";
/*  60 */     String str5 = "";
/*  61 */     String str6 = formatFiletypes(str2);
/*  62 */     String str7 = getTypeDesc(str2);
/*     */     
/*  64 */     if (!"".equals(paramString3) && !"-1".equals(paramString3) && 
/*  65 */       i == 1) {
/*  66 */       String str8 = requestBaseUtil.getFieldname(paramString2, j + "");
/*  67 */       String str9 = requestBaseUtil.getFieldValue(paramString3, str8, paramString2);
/*  68 */       str1 = getSelectCategory(paramString2, j + "", str9);
/*     */     } 
/*     */ 
/*     */     
/*  72 */     if (!"".equals(str1)) {
/*  73 */       String[] arrayOfString = str1.split(",");
/*  74 */       if (arrayOfString.length == 3) {
/*  75 */         str3 = arrayOfString[0];
/*  76 */         str4 = arrayOfString[1];
/*  77 */         str5 = arrayOfString[2];
/*     */       } 
/*     */     } 
/*     */     
/*  81 */     boolean bool = false;
/*  82 */     if (i == 1 && j > 0) {
/*  83 */       bool = isSelectCategoryCanuse(paramString2, j + "");
/*     */     }
/*     */ 
/*     */     
/*  87 */     SecCategoryComInfo secCategoryComInfo = null;
/*     */     try {
/*  89 */       secCategoryComInfo = new SecCategoryComInfo();
/*  90 */     } catch (Exception exception) {
/*  91 */       exception.printStackTrace();
/*     */     } 
/*  93 */     int k = 5;
/*  94 */     if (!"-1".equals(str5) || !"".equals(str5)) {
/*  95 */       k = Util.getIntValue(secCategoryComInfo.getMaxUploadFileSize(str5), 5);
/*     */     }
/*  97 */     if (k == 0)
/*  98 */       k = 5; 
/*  99 */     fileUploadDTO.setPicfiletypes(str6);
/* 100 */     fileUploadDTO.setFiletypedesc(str7);
/* 101 */     fileUploadDTO.setMainId(str3);
/* 102 */     fileUploadDTO.setSubId(str4);
/* 103 */     fileUploadDTO.setSecId(str5);
/* 104 */     fileUploadDTO.setUploadType(i);
/* 105 */     fileUploadDTO.setCanuse(bool);
/* 106 */     fileUploadDTO.setSelectedCateLog(j + "");
/* 107 */     fileUploadDTO.setMaxUploadFileSize(k);
/* 108 */     return fileUploadDTO;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getSelectCategory(String paramString1, String paramString2, String paramString3) {
/* 119 */     String str1 = "";
/* 120 */     WorkflowComInfo workflowComInfo = new WorkflowComInfo();
/* 121 */     String str2 = workflowComInfo.getIsBill(paramString1);
/* 122 */     RecordSet recordSet = new RecordSet();
/* 123 */     recordSet.execute("select doccategory from workflow_selectitem where isbill = " + str2 + " and fieldid = " + paramString2 + " and selectvalue = " + paramString3);
/* 124 */     if (recordSet.next())
/* 125 */       str1 = Util.null2String(recordSet.getString(1)); 
/* 126 */     return str1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean isSelectCategoryCanuse(String paramString1, String paramString2) {
/* 137 */     boolean bool = true;
/* 138 */     WorkflowComInfo workflowComInfo = new WorkflowComInfo();
/* 139 */     String str = workflowComInfo.getIsBill(paramString1);
/* 140 */     RecordSet recordSet = new RecordSet();
/* 141 */     recordSet.execute("select 1 from workflow_selectitem where fieldid = " + paramString2 + " and isbill = " + str + " and (docPath is null or docCategory is null or docPath='' or docCategory='') and isAccordToSubCom='0'");
/* 142 */     if (recordSet.next()) {
/* 143 */       bool = false;
/*     */     }
/* 145 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String formatFiletypes(String paramString) {
/* 155 */     paramString = Util.null2String(paramString).trim().toLowerCase();
/*     */     
/* 157 */     if (!"".equals(paramString)) {
/* 158 */       if (paramString.startsWith(","))
/* 159 */         paramString = paramString.substring(1); 
/* 160 */       if (paramString.endsWith(",")) {
/* 161 */         paramString = paramString.substring(0, paramString.length() - 1);
/*     */       }
/* 163 */       paramString = paramString.replaceAll(",", ";*.");
/*     */       
/* 165 */       paramString = "*." + paramString;
/*     */     } else {
/* 167 */       paramString = "*.*";
/*     */     } 
/* 169 */     return paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getTypeDesc(String paramString) {
/* 178 */     String str = "";
/* 179 */     paramString = Util.null2String(paramString).trim().toLowerCase();
/* 180 */     if (!"".equals(paramString)) {
/* 181 */       str = "Only " + paramString + " Files";
/*     */     } else {
/* 183 */       str = "All Files";
/*     */     } 
/* 185 */     return str;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/FieldFileUploadUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */