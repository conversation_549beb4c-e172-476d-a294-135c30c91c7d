/*     */ package weaver.workflow.request;
/*     */ 
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class WFForwardInit
/*     */   extends BaseBean
/*     */ {
/*     */   private static String IsPendingForward;
/*     */   private static String IsTakingOpinions;
/*     */   private static String IsHandleForward;
/*     */   private static String IsWaitForwardOpinion;
/*     */   private static String IsBeForwardModify;
/*     */   private static String IsBeForward;
/*     */   private static String IsBeForwardAlready;
/*     */   private static String IsBeForwardTodo;
/*     */   private static String IsFromWFRemark;
/*     */   private static String IsSubmitedOpinion;
/*     */   private static String IsBeForwardSubmitAlready;
/*     */   private static String IsBeForwardSubmitNotaries;
/*     */   private static String currentnodetype;
/*     */   private static int workflowid;
/*     */   private static int nodeid;
/*     */   
/*     */   public static String getWFUnionNodeInfo() {
/*  45 */     String str = "";
/*     */     try {
/*  47 */       RecordSet recordSet1 = new RecordSet();
/*  48 */       RecordSet recordSet2 = new RecordSet();
/*  49 */       RecordSet recordSet3 = new RecordSet();
/*  50 */       RecordSet recordSet4 = new RecordSet();
/*  51 */       String str1 = "select * from workflow_flownode where IsPendingForward =1 ";
/*     */       
/*  53 */       recordSet1.executeSql(str1);
/*  54 */       while (recordSet1.next()) {
/*  55 */         workflowid = Util.getIntValue(recordSet1.getString("workflowid"), 0);
/*  56 */         nodeid = Util.getIntValue(recordSet1.getString("nodeid"), 0);
/*  57 */         IsPendingForward = Util.null2String(recordSet1.getString("IsPendingForward"));
/*  58 */         IsTakingOpinions = Util.null2String(recordSet1.getString("IsTakingOpinions"));
/*  59 */         IsHandleForward = Util.null2String(recordSet1.getString("IsHandleForward"));
/*  60 */         IsWaitForwardOpinion = Util.null2String(recordSet1.getString("IsWaitForwardOpinion"));
/*  61 */         IsBeForwardModify = Util.null2String(recordSet1.getString("IsBeForwardModify"));
/*     */ 
/*     */         
/*  64 */         if ("1".equals(IsPendingForward) && "1".equals(IsWaitForwardOpinion) && !"1".equals(IsTakingOpinions)) {
/*  65 */           recordSet2.executeSql("update workflow_flownode set IsTakingOpinions=1 where  IsPendingForward =1 and workflowid=" + workflowid + " and nodeid=" + nodeid);
/*     */         }
/*  67 */         if ("1".equals(IsPendingForward) && "1".equals(IsBeForwardModify) && !"1".equals(IsHandleForward)) {
/*  68 */           recordSet2.executeSql("update workflow_flownode set IsHandleForward=1 where IsPendingForward =1 and workflowid=" + workflowid + " and nodeid=" + nodeid);
/*     */         }
/*     */       } 
/*     */       
/*  72 */       str1 = "select IsBeForward,IsBeForwardAlready,IsBeForwardTodo,IsSubmitedOpinion,requestid from workflow_Forward where IsFromWFRemark is null or IsFromWFRemark ='' order by requestid desc ";
/*  73 */       recordSet1.executeSql(str1);
/*  74 */       while (recordSet1.next()) {
/*  75 */         IsBeForward = Util.null2String(recordSet1.getString("IsBeForward"));
/*  76 */         IsBeForwardAlready = Util.null2String(recordSet1.getString("IsBeForwardAlready"));
/*  77 */         IsBeForwardTodo = Util.null2String(recordSet1.getString("IsBeForwardTodo"));
/*  78 */         IsSubmitedOpinion = Util.null2String(recordSet1.getString("IsSubmitedOpinion"));
/*  79 */         int i = Util.getIntValue(recordSet1.getString("requestid"), 0);
/*  80 */         recordSet2.executeSql("select currentnodetype from workflow_requestbase where requestid = " + i);
/*  81 */         if (recordSet2.next()) {
/*  82 */           currentnodetype = Util.null2String(recordSet2.getString("currentnodetype"));
/*     */         }
/*  84 */         if ("1".equals(IsBeForward) && "3".equals(currentnodetype)) {
/*  85 */           recordSet3.executeSql("update workflow_Forward set IsFromWFRemark=2 where  IsBeForward =1 and requestid = " + i);
/*  86 */         } else if ("1".equals(IsBeForwardAlready) && !"3".equals(currentnodetype)) {
/*  87 */           recordSet3.executeSql("update workflow_Forward set IsFromWFRemark=1 where  IsBeForwardAlready =1 and requestid = " + i);
/*  88 */         } else if ("1".equals(IsBeForwardTodo) && !"3".equals(currentnodetype)) {
/*  89 */           recordSet3.executeSql("update workflow_Forward set IsFromWFRemark=0 where  IsBeForwardTodo =1 and requestid = " + i);
/*     */         } 
/*     */         
/*  92 */         if ("1".equals(IsSubmitedOpinion) && !"3".equals(currentnodetype)) {
/*  93 */           recordSet4.executeSql("update workflow_Forward set IsFromWFRemark=0 where  IsSubmitedOpinion =1 and requestid = " + i);
/*     */         }
/*     */       } 
/*     */       
/*  97 */       str = "1";
/*  98 */     } catch (Exception exception) {
/*  99 */       str = exception.getMessage();
/*     */     } 
/* 101 */     return str;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/WFForwardInit.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */