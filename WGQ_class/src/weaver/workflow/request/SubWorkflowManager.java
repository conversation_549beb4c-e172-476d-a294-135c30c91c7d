/*     */ package weaver.workflow.request;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.List;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpSession;
/*     */ import weaver.conn.RecordSet;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class SubWorkflowManager
/*     */ {
/*     */   public static final String RELATION_TYPE_MAIN = "2";
/*     */   public static final String RELATION_TYPE_SUB = "3";
/*     */   public static final String RELATION_TYPE_PARA = "4";
/*     */   
/*     */   public static boolean hasRelation(HttpServletRequest paramHttpServletRequest) {
/*  26 */     if (paramHttpServletRequest == null) {
/*  27 */       return false;
/*     */     }
/*  29 */     HttpSession httpSession = paramHttpServletRequest.getSession(false);
/*  30 */     String str1 = paramHttpServletRequest.getParameter("requestid");
/*  31 */     String str2 = paramHttpServletRequest.getParameter("isrequest");
/*  32 */     String str3 = paramHttpServletRequest.getParameter("relaterequest");
/*     */     
/*  34 */     if (httpSession == null || str1 == null || str2 == null || str3 == null) {
/*  35 */       return false;
/*     */     }
/*     */     
/*  38 */     if (str1.trim().equals("") || str2.trim().equals("") || str3.trim().equals("")) {
/*  39 */       return false;
/*     */     }
/*     */     
/*  42 */     return hasRelation(httpSession, str1, str3, str2);
/*     */   }
/*     */ 
/*     */   
/*     */   private static boolean hasRelation(HttpSession paramHttpSession, String paramString1, String paramString2, String paramString3) {
/*  47 */     if (paramString3.equals("2")) {
/*  48 */       String str = "main_" + paramString2;
/*  49 */       List<String> list = (List)paramHttpSession.getAttribute(str);
/*  50 */       for (byte b = 0; list != null && b < list.size(); b++) {
/*  51 */         if (((String)list.get(b)).equals(paramString1)) {
/*  52 */           return true;
/*     */         }
/*     */       } 
/*  55 */       return false;
/*  56 */     }  if (paramString3.equals("3")) {
/*  57 */       String str1 = "sub_" + paramString2;
/*  58 */       String str2 = (String)paramHttpSession.getAttribute(str1);
/*  59 */       if (str2 == null) {
/*  60 */         return false;
/*     */       }
/*     */       
/*  63 */       if (str2.equals(paramString1)) {
/*  64 */         return true;
/*     */       }
/*  66 */       return false;
/*     */     } 
/*  68 */     if (paramString3.equals("4")) {
/*  69 */       String str = "para_" + paramString2;
/*  70 */       List<String> list = (List)paramHttpSession.getAttribute(str);
/*  71 */       for (byte b = 0; list != null && b < list.size(); b++) {
/*  72 */         if (((String)list.get(b)).equals(paramString1)) {
/*  73 */           return true;
/*     */         }
/*     */       } 
/*  76 */       return false;
/*     */     } 
/*  78 */     return true;
/*     */   }
/*     */   
/*     */   public static void loadRelatedRequest(HttpServletRequest paramHttpServletRequest) {
/*  82 */     if (paramHttpServletRequest == null) {
/*     */       return;
/*     */     }
/*  85 */     HttpSession httpSession = paramHttpServletRequest.getSession(false);
/*  86 */     String str1 = paramHttpServletRequest.getParameter("requestid");
/*     */     
/*  88 */     String str2 = "";
/*  89 */     RecordSet recordSet = new RecordSet();
/*  90 */     recordSet.executeQuery("select mainrequestid,requestid,requestname,requestlevel,mainrequestid,creater,creatertype,createdate,createtime,workflowId,currentstatus,currentnodeid,currentnodetype,status,remindTypes,docids,crmids,prjids,cptids , lastnodeid  from workflow_requestbase where requestid=?", new Object[] { str1 });
/*  91 */     if (recordSet.next()) {
/*  92 */       str2 = recordSet.getString("workflowid");
/*     */     }
/*     */     
/*  95 */     if (httpSession == null || str1 == null) {
/*     */       return;
/*     */     }
/*     */     
/*  99 */     if (str1.trim().equals("")) {
/*     */       return;
/*     */     }
/*     */     
/* 103 */     loadRelatedRequest(httpSession, str1, str2);
/*     */   }
/*     */ 
/*     */   
/*     */   private static void loadRelatedRequest(HttpSession paramHttpSession, String paramString1, String paramString2) {
/* 108 */     RecordSet recordSet = new RecordSet();
/* 109 */     String str1 = new String();
/* 110 */     str1 = "select subwfid,subrequestid,mainrequestid,isSame from Workflow_SubwfRequest where mainrequestid=" + paramString1 + " or subrequestid= " + paramString1;
/* 111 */     recordSet.executeSql(str1);
/*     */     
/* 113 */     ArrayList<String> arrayList1 = new ArrayList();
/* 114 */     String str2 = "";
/* 115 */     while (recordSet.next()) {
/* 116 */       if (recordSet.getString("mainrequestid").equalsIgnoreCase(paramString1))
/* 117 */         arrayList1.add(recordSet.getString("subrequestid")); 
/* 118 */       if (recordSet.getString("subrequestid").equalsIgnoreCase(paramString1))
/* 119 */         str2 = recordSet.getString("mainrequestid"); 
/*     */     } 
/* 121 */     paramHttpSession.setAttribute("main_" + paramString1, arrayList1);
/* 122 */     boolean bool = false;
/*     */     
/* 124 */     if (!recordSet.getString("mainrequestid").equalsIgnoreCase("")) {
/* 125 */       paramHttpSession.setAttribute("sub_" + paramString1, str2);
/*     */     } else {
/* 127 */       bool = true;
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 149 */     if (bool) {
/* 150 */       recordSet.executeQuery("select mainrequestid,requestid,requestname,requestlevel,mainrequestid,creater,creatertype,createdate,createtime,workflowId,currentstatus,currentnodeid,currentnodetype,status,remindTypes,docids,crmids,prjids,cptids , lastnodeid  from workflow_requestbase where requestid=?", new Object[] { paramString1 });
/* 151 */       if (recordSet.next() && 
/* 152 */         recordSet.getInt("mainrequestid") > -1) {
/* 153 */         String str = recordSet.getString("mainrequestid");
/* 154 */         recordSet.executeSql("select * from workflow_requestbase where requestid = " + str);
/* 155 */         if (recordSet.next()) {
/* 156 */           String str3 = recordSet.getString("workflowid");
/* 157 */           recordSet.executeSql("select 1 from Workflow_SubwfSet where mainworkflowid = " + str3 + " and subworkflowid =" + paramString2 + " and isread = 1 union select 1 from Workflow_TriDiffWfDiffField a, Workflow_TriDiffWfSubWf b where a.id=b.triDiffWfDiffFieldId and b.isRead=1 and a.mainworkflowid=" + str3 + " and b.subWorkflowId=" + paramString2);
/*     */           
/* 159 */           if (recordSet.next()) {
/* 160 */             paramHttpSession.setAttribute("sub_" + paramString1, str);
/*     */           }
/*     */         } 
/*     */       } 
/*     */     } 
/*     */ 
/*     */     
/* 167 */     str1 = "select subrequestid from Workflow_SubwfRequest where subwfid = (select subwfid from Workflow_SubwfRequest where subrequestid=" + paramString1 + ") and mainrequestid =  (select mainrequestid from Workflow_SubwfRequest where subrequestid=" + paramString1 + ")";
/* 168 */     recordSet.executeSql(str1);
/*     */     
/* 170 */     ArrayList<String> arrayList2 = new ArrayList();
/* 171 */     while (recordSet.next()) {
/* 172 */       arrayList2.add(recordSet.getString("subrequestid"));
/*     */     }
/* 174 */     paramHttpSession.setAttribute("para_" + paramString1, arrayList2);
/*     */   }
/*     */   
/*     */   public static String getCanViewNodes(HttpServletRequest paramHttpServletRequest) {
/* 178 */     String str1 = "all";
/* 179 */     if (paramHttpServletRequest == null) {
/* 180 */       return str1;
/*     */     }
/* 182 */     HttpSession httpSession = paramHttpServletRequest.getSession(false);
/* 183 */     String str2 = paramHttpServletRequest.getParameter("requestid");
/* 184 */     String str3 = paramHttpServletRequest.getParameter("isrequest");
/* 185 */     String str4 = paramHttpServletRequest.getParameter("relaterequest");
/*     */     
/* 187 */     if (httpSession == null || str2 == null || str3 == null || str4 == null) {
/* 188 */       return str1;
/*     */     }
/*     */     
/* 191 */     if (str2.trim().equals("") || str3.trim().equals("") || str4.trim().equals("")) {
/* 192 */       return str1;
/*     */     }
/*     */     
/* 195 */     return getCanViewNodes(str2, str4, str3);
/*     */   }
/*     */   
/*     */   private static String getCanViewNodes(String paramString1, String paramString2, String paramString3) {
/* 199 */     String str1 = "all";
/* 200 */     RecordSet recordSet = new RecordSet();
/* 201 */     String str2 = "";
/*     */     
/* 203 */     if (paramString3.equals("2")) {
/* 204 */       str2 = "select subwfid,isSame from Workflow_SubwfRequest where mainrequestid = " + paramString2 + " and subrequestid=" + paramString1;
/* 205 */     } else if (paramString3.equals("3")) {
/* 206 */       str2 = "select subwfid,isSame from Workflow_SubwfRequest where mainrequestid = " + paramString1 + " and subrequestid=" + paramString2;
/* 207 */     } else if (paramString3.equals("4")) {
/* 208 */       str2 = "select subwfid,isSame from Workflow_SubwfRequest where subrequestid=" + paramString2;
/*     */     } 
/*     */     
/* 211 */     recordSet.executeSql(str2);
/* 212 */     if (recordSet.next()) {
/* 213 */       String str3 = recordSet.getString("subwfid");
/* 214 */       String str4 = recordSet.getString("isSame");
/*     */       
/* 216 */       if ("1".equals(str4)) {
/* 217 */         str2 = "select isread,isreadnodes,isreadmainwf,isreadmainwfnodes,isreadparallelwf,isreadparallelwfnodes from workflow_tridiffwfsubwf where id=" + str3;
/*     */       } else {
/* 219 */         str2 = "select isread,isreadnodes,isreadmainwf,isreadmainwfnodes,isreadparallelwf,isreadparallelwfnodes from workflow_subwfset where id=" + str3;
/*     */       } 
/*     */       
/* 222 */       recordSet.executeSql(str2);
/* 223 */       if (recordSet.next()) {
/* 224 */         if (paramString3.equals("2")) {
/* 225 */           str1 = recordSet.getString("isreadnodes");
/* 226 */         } else if (paramString3.equals("3")) {
/* 227 */           str1 = recordSet.getString("isreadmainwfnodes");
/* 228 */         } else if (paramString3.equals("4")) {
/* 229 */           str1 = recordSet.getString("isreadparallelwfnodes");
/*     */         } 
/*     */       }
/*     */     } 
/*     */     
/* 234 */     return str1;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/SubWorkflowManager.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */