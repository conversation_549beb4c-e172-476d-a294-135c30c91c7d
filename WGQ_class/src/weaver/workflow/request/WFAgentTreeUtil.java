/*     */ package weaver.workflow.request;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import org.json.JSONObject;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.workflow.workflow.WorkTypeComInfo;
/*     */ import weaver.workflow.workflow.WorkflowComInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class WFAgentTreeUtil
/*     */ {
/*     */   private WorkflowComInfo wci;
/*     */   private WorkTypeComInfo wtci;
/*     */   
/*     */   public WFAgentTreeUtil() {
/*     */     try {
/*  26 */       this.wci = new WorkflowComInfo();
/*  27 */       this.wtci = new WorkTypeComInfo();
/*  28 */     } catch (Exception exception) {}
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public void setViewType(String paramString, Map<String, Object> paramMap) {
/*  34 */     if ("0".equals(paramString)) {
/*  35 */       paramMap.put("flowNew", (Integer.parseInt((new StringBuilder()).append(paramMap.get("flowNew")).append("").toString()) + 1) + "");
/*     */     
/*     */     }
/*  38 */     else if ("flowResponse".equals(paramString)) {
/*  39 */       paramMap.put("flowResponse", (Integer.parseInt((new StringBuilder())
/*  40 */             .append(paramMap.get("flowResponse")).append("").toString()) + 1) + "");
/*     */     } 
/*     */ 
/*     */     
/*  44 */     paramMap.put("flowAll", (
/*  45 */         Integer.parseInt((new StringBuilder()).append(paramMap.get("flowAll")).append("").toString()) + 1) + "");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getAgentTreeList(String paramString1, String paramString2) {
/*  56 */     RecordSet recordSet1 = new RecordSet();
/*  57 */     RecordSet recordSet2 = new RecordSet();
/*  58 */     String str1 = "";
/*     */     
/*  60 */     if (paramString2.equals("")) {
/*  61 */       paramString2 = "0";
/*     */     }
/*  63 */     String str2 = "";
/*     */ 
/*     */     
/*  66 */     if ("0".equals(paramString2)) {
/*  67 */       str2 = " and t2.agentorbyagentid ='" + paramString1 + "'";
/*     */     } else {
/*  69 */       str2 = " and t2.userid ='" + paramString1 + "'";
/*  70 */     }  String str3 = "select count( distinct t1.requestid)  from workflow_requestbase t1,workflow_currentoperator t2 ,workflow_base t3  where t2.requestid=t1.requestid and t1.workflowid=t3.id and (t3.isvalid='1' or t3.isvalid='3')  and t2.agenttype='2' and t2.isremark='2'" + str2;
/*     */     
/*  72 */     recordSet2.executeSql(str3);
/*  73 */     recordSet2.first();
/*  74 */     int i = recordSet2.getInt(1);
/*  75 */     if (recordSet2.getDBType().equals("oracle")) {
/*     */       
/*  77 */       str1 = "";
/*  78 */     } else if (recordSet2.getDBType().equals("mysql")) {
/*  79 */       str1 = "select * from (  select * from (   select   distinct    t1.requestid, t1.createdate, t1.createtime,t1.creater, t1.creatertype, t1.workflowid, t1.requestname, t1.status,t1.requestlevel,t1.currentnodeid,t2.viewtype,t2.receivedate,t2.receivetime,t2.operatedate,t2.operatetime,t2.isremark,t2.nodeid,t2.agentorbyagentid,t2.agenttype   from workflow_requestbase t1,workflow_currentoperator t2 ,workflow_base t3  where t2.requestid=t1.requestid and t3.id=t1.workflowid and (t3.isvalid='1' or t3.isvalid='3')  and t2.agenttype='2' and t2.isremark='2'  " + str2 + "  order by t1.requestid desc,t2.operatedate desc,t2.operatetime desc limit " + i + " ) tbltemp1  order by requestid asc,operatedate asc,operatetime asc limit  " + i + " ) tbltemp2  order by requestid desc,operatedate desc,operatetime desc limit " + i;
/*     */     }
/*  81 */     else if (recordSet2.getDBType().equals("postgresql")) {
/*  82 */       str1 = "select * from (  select * from (   select   distinct    t1.requestid, t1.createdate, t1.createtime,t1.creater, t1.creatertype, t1.workflowid, t1.requestname, t1.status,t1.requestlevel,t1.currentnodeid,t2.viewtype,t2.receivedate,t2.receivetime,t2.operatedate,t2.operatetime,t2.isremark,t2.nodeid,t2.agentorbyagentid,t2.agenttype   from workflow_requestbase t1,workflow_currentoperator t2 ,workflow_base t3  where t2.requestid=t1.requestid and t3.id=t1.workflowid and (t3.isvalid='1' or t3.isvalid='3')  and t2.agenttype='2' and t2.isremark='2'  " + str2 + "  order by t1.requestid desc,t2.operatedate desc,t2.operatetime desc limit " + i + " ) tbltemp1  order by requestid asc,operatedate asc,operatetime asc limit  " + i + " ) tbltemp2  order by requestid desc,operatedate desc,operatetime desc limit " + i;
/*     */     }
/*  84 */     else if (recordSet2.getDBType().equals("sqlserver")) {
/*     */       
/*  86 */       str1 = "select top " + i + " * from (    select top  " + i + " * from (        select   distinct  top " + i + "  t1.requestid, t1.createdate, t1.createtime,t1.creater, t1.creatertype, t1.workflowid, t1.requestname, t1.status,t1.requestlevel,t1.currentnodeid,t2.viewtype,t2.receivedate,t2.receivetime,t2.operatedate,t2.operatetime,t2.isremark,t2.nodeid,t2.agentorbyagentid,t2.agenttype   from workflow_requestbase t1,workflow_currentoperator t2 ,workflow_base t3  where t2.requestid=t1.requestid and t3.id=t1.workflowid and (t3.isvalid='1' or t3.isvalid='3')  and t2.agenttype='2' and t2.isremark='2'  " + str2 + "  order by t1.requestid desc,t2.operatedate desc,t2.operatetime desc ) tbltemp1  order by requestid asc,operatedate asc,operatetime asc ) tbltemp2  order by requestid desc,operatedate desc,operatetime desc  ";
/*     */     } 
/*     */     
/*  89 */     recordSet1.execute(str1);
/*     */     
/*  91 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*     */     
/*  93 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 105 */     String str4 = "";
/* 106 */     String str5 = "";
/*     */     
/* 108 */     String str6 = "";
/* 109 */     String str7 = "";
/*     */     
/* 111 */     String str8 = "";
/* 112 */     while (recordSet1.next()) {
/* 113 */       str7 = recordSet1.getString("workflowid");
/* 114 */       str8 = recordSet1.getString("viewtype");
/* 115 */       str6 = this.wci.getWorkflowname(str7);
/* 116 */       str5 = this.wci.getWorkflowtype(str7);
/* 117 */       str4 = this.wtci.getWorkTypename(str5);
/* 118 */       if (!hashMap1.containsKey(str5)) {
/* 119 */         HashMap<Object, Object> hashMap3 = new HashMap<>();
/* 120 */         hashMap1.put(str5, hashMap3);
/*     */ 
/*     */         
/* 123 */         hashMap3.put("name", str4);
/*     */         
/* 125 */         HashMap<Object, Object> hashMap5 = new HashMap<>();
/* 126 */         hashMap5.put("objid", str5);
/* 127 */         hashMap5.put("method", "type");
/* 128 */         hashMap5.put("agentFlag", paramString2);
/*     */ 
/*     */         
/* 131 */         hashMap3.put("attr", hashMap5);
/*     */ 
/*     */         
/* 134 */         ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 135 */         hashMap3.put("submenus", arrayList);
/*     */ 
/*     */         
/* 138 */         HashMap<Object, Object> hashMap6 = new HashMap<>();
/* 139 */         hashMap6.put("flowNew", Integer.valueOf(0));
/* 140 */         hashMap6.put("flowResponse", Integer.valueOf(0));
/* 141 */         hashMap6.put("flowAll", Integer.valueOf(0));
/* 142 */         setViewType(str8, (Map)hashMap6);
/*     */         
/* 144 */         hashMap3.put("numbers", hashMap6);
/*     */ 
/*     */         
/* 147 */         HashMap<Object, Object> hashMap4 = new HashMap<>();
/* 148 */         hashMap4.put("name", str6);
/*     */         
/* 150 */         hashMap5 = new HashMap<>();
/* 151 */         hashMap5.put("objid", str7);
/* 152 */         hashMap5.put("method", "workflow");
/* 153 */         hashMap5.put("agentFlag", paramString2);
/* 154 */         hashMap4.put("attr", hashMap5);
/*     */         
/* 156 */         hashMap6 = new HashMap<>();
/* 157 */         hashMap6.put("flowNew", Integer.valueOf(0));
/* 158 */         hashMap6.put("flowResponse", Integer.valueOf(0));
/* 159 */         hashMap6.put("flowAll", Integer.valueOf(0));
/* 160 */         setViewType(str8, (Map)hashMap6);
/*     */         
/* 162 */         hashMap4.put("numbers", hashMap6);
/*     */         
/* 164 */         arrayList.add(hashMap4);
/*     */         
/* 166 */         hashMap2.put(str7, hashMap4);
/*     */         continue;
/*     */       } 
/* 169 */       Map map1 = (Map)hashMap1.get(str5);
/*     */       
/* 171 */       if (hashMap2.containsKey(str7)) {
/* 172 */         Map map2 = (Map)hashMap2.get(str7);
/* 173 */         Map<String, Object> map3 = (Map)map2.get("numbers");
/* 174 */         setViewType(str8, map3);
/*     */       }
/*     */       else {
/*     */         
/* 178 */         List<HashMap<Object, Object>> list = (List)map1.get("submenus");
/*     */         
/* 180 */         HashMap<Object, Object> hashMap3 = new HashMap<>();
/* 181 */         hashMap3.put("name", str6);
/*     */         
/* 183 */         HashMap<Object, Object> hashMap4 = new HashMap<>();
/* 184 */         hashMap4.put("objid", str7);
/* 185 */         hashMap4.put("method", "workflow");
/* 186 */         hashMap4.put("agentFlag", paramString2);
/* 187 */         hashMap3.put("attr", hashMap4);
/*     */         
/* 189 */         HashMap<Object, Object> hashMap5 = new HashMap<>();
/* 190 */         hashMap5.put("flowNew", Integer.valueOf(0));
/* 191 */         hashMap5.put("flowResponse", Integer.valueOf(0));
/* 192 */         hashMap5.put("flowAll", Integer.valueOf(0));
/* 193 */         setViewType(str8, (Map)hashMap5);
/*     */         
/* 195 */         hashMap3.put("numbers", hashMap5);
/*     */         
/* 197 */         list.add(hashMap3);
/* 198 */         hashMap2.put(str7, hashMap3);
/*     */       } 
/*     */ 
/*     */       
/* 202 */       Map<String, Object> map = (Map)map1.get("numbers");
/* 203 */       setViewType(str8, map);
/*     */     } 
/*     */     
/* 206 */     StringBuffer stringBuffer = new StringBuffer("[");
/*     */ 
/*     */     
/* 209 */     for (Map.Entry<Object, Object> entry : hashMap1.entrySet()) {
/* 210 */       Map map = (Map)entry.getValue();
/* 211 */       JSONObject jSONObject = new JSONObject(map);
/* 212 */       stringBuffer.append(jSONObject.toString()).append(",");
/*     */     } 
/* 214 */     String str9 = "";
/* 215 */     if (stringBuffer.length() == 1) {
/* 216 */       str9 = "[]";
/*     */     } else {
/* 218 */       str9 = stringBuffer.substring(0, stringBuffer.length() - 1) + "]";
/*     */     } 
/*     */ 
/*     */     
/* 222 */     return str9;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static void main(String[] paramArrayOfString) {
/* 228 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*     */     
/* 230 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 231 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/*     */     
/* 233 */     HashMap<Object, Object> hashMap3 = new HashMap<>();
/* 234 */     hashMap3.put("workflowid", "1");
/* 235 */     hashMap3.put("workflowiddata", "1");
/*     */     
/* 237 */     HashMap<Object, Object> hashMap4 = new HashMap<>();
/* 238 */     hashMap4.put("workflowid", "1");
/* 239 */     hashMap4.put("workflowiddata", "1");
/*     */     
/* 241 */     hashMap1.put("attr", hashMap3);
/* 242 */     hashMap2.put("attr", hashMap3);
/*     */     
/* 244 */     hashMap1.put("submenu", arrayList);
/*     */     
/* 246 */     hashMap1.put("name", "系统工作流提醒");
/* 247 */     hashMap2.put("name", "系统工作流提醒1");
/*     */     
/* 249 */     arrayList.add(hashMap2);
/*     */     
/* 251 */     JSONObject jSONObject = new JSONObject(hashMap1);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/WFAgentTreeUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */