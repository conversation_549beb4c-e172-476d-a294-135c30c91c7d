/*     */ package weaver.workflow.request;
/*     */ 
/*     */ import java.util.List;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.file.FileUpload;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.workflow.workflow.WFOpinionInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class RemarkOperaterManager
/*     */ {
/*     */   public void processRemark(int paramInt1, String paramString, int paramInt2, User paramUser, FileUpload paramFileUpload) {
/*  37 */     String str = "WFOpinionFieldData" + paramInt1;
/*  38 */     if (isExistTable(str)) {
/*     */       try {
/*  40 */         RequestOpinionFieldManager requestOpinionFieldManager = new RequestOpinionFieldManager();
/*  41 */         String str1 = String.valueOf(paramInt1);
/*  42 */         String str2 = String.valueOf(paramInt2);
/*  43 */         String str3 = "";
/*  44 */         int i = Util.getIntValue(paramString, 0);
/*  45 */         int j = getNewRequestLogId(paramUser.getUID(), i);
/*  46 */         List<WFOpinionInfo> list = requestOpinionFieldManager.getAllOpinionFieldNames(str1, str2);
/*  47 */         StringBuffer stringBuffer = (new StringBuffer()).append("INSERT INTO ").append(str).append(" VALUES(").append(i).append(",").append(j).append(",").append(paramInt2).append(",").append(getLogType()).append(",");
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */         
/*  53 */         if (list != null) {
/*  54 */           for (byte b = 0; b < list.size(); b++) {
/*  55 */             WFOpinionInfo wFOpinionInfo = list.get(b);
/*  56 */             RequestOpinionBrowserInfo requestOpinionBrowserInfo = requestOpinionFieldManager.getBrowserInfo(wFOpinionInfo.getType_cn());
/*     */             
/*  58 */             String str5 = String.valueOf(wFOpinionInfo.getId());
/*  59 */             str3 = Util.null2String(paramFileUpload.getParameter("field" + requestOpinionBrowserInfo.getFieldid() + str5));
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */             
/*  65 */             if (str3.startsWith(",")) {
/*  66 */               str3 = str3.substring(1);
/*     */             }
/*  68 */             stringBuffer.append("'").append(str3).append("',");
/*     */           } 
/*     */ 
/*     */           
/*  72 */           String str4 = stringBuffer.toString();
/*  73 */           str4 = str4.substring(0, str4.length() - 1) + ")";
/*  74 */           RecordSet recordSet = new RecordSet();
/*  75 */           recordSet.executeSql(str4);
/*     */         } 
/*  77 */       } catch (Exception exception) {
/*  78 */         exception.printStackTrace();
/*     */       } 
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private int getNewRequestLogId(int paramInt1, int paramInt2) {
/*  91 */     int i = 0;
/*  92 */     StringBuffer stringBuffer = (new StringBuffer()).append("SELECT MAX(LOGID) AS ID FROM WORKFLOW_REQUESTLOG WHERE OPERATOR=").append(paramInt1).append(" AND REQUESTID=").append(paramInt2);
/*     */ 
/*     */ 
/*     */     
/*  96 */     RecordSet recordSet = new RecordSet();
/*  97 */     recordSet.executeSql(stringBuffer.toString());
/*  98 */     if (recordSet.next()) {
/*  99 */       i = recordSet.getInt("ID");
/*     */     }
/* 101 */     return i;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private int getLogType() {
/* 110 */     return 2;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private boolean isExistTable(String paramString) {
/* 121 */     StringBuffer stringBuffer = (new StringBuffer()).append("SELECT ID FROM WFOpinionTableNames ").append(" WHERE NAME='").append(paramString).append("'");
/*     */ 
/*     */     
/* 124 */     RecordSet recordSet = new RecordSet();
/* 125 */     recordSet.executeSql(stringBuffer.toString());
/* 126 */     if (recordSet.next()) {
/* 127 */       return true;
/*     */     }
/* 129 */     return false;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/RemarkOperaterManager.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */