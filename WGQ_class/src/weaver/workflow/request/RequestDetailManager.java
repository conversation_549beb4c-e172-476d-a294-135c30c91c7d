/*     */ package weaver.workflow.request;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class RequestDetailManager
/*     */   extends BaseBean
/*     */ {
/*     */   private HttpServletRequest request;
/*     */   private int recId;
/*     */   private String countFieldStr;
/*     */   
/*     */   public RequestDetailManager() {}
/*     */   
/*     */   public RequestDetailManager(HttpServletRequest paramHttpServletRequest, int paramInt) {
/*  38 */     this.request = paramHttpServletRequest;
/*  39 */     this.recId = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setHttpRequest(HttpServletRequest paramHttpServletRequest) {
/*  48 */     this.request = paramHttpServletRequest;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setRequestID(int paramInt) {
/*  56 */     this.recId = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean saveRequestDetailInfo() {
/*  64 */     if (this.request == null) {
/*  65 */       return false;
/*     */     }
/*  67 */     String str1 = Util.null2String(this.request.getParameter("formid"));
/*  68 */     if (str1.equals(""))
/*  69 */       return false; 
/*  70 */     RecordSet recordSet = new RecordSet();
/*  71 */     String str2 = "";
/*  72 */     recordSet.executeProc("bill_includepages_SelectByID", str1);
/*  73 */     if (recordSet.next()) {
/*  74 */       str2 = Util.null2String(recordSet.getString("detailtablename"));
/*     */     } else {
/*  76 */       return false;
/*     */     } 
/*     */     
/*  79 */     String str3 = "";
/*  80 */     str3 = "DELETE from " + str2 + " WHERE requestid  =" + String.valueOf(this.recId);
/*  81 */     recordSet.executeSql(str3);
/*     */ 
/*     */     
/*  84 */     ArrayList<String> arrayList1 = new ArrayList();
/*  85 */     ArrayList<String> arrayList2 = new ArrayList();
/*  86 */     ArrayList<String> arrayList3 = new ArrayList();
/*  87 */     String str4 = "";
/*  88 */     String str5 = "";
/*  89 */     String str6 = "";
/*  90 */     recordSet.executeProc("workflow_billfield_Select", str1);
/*  91 */     while (recordSet.next()) {
/*  92 */       str6 = Util.null2String(recordSet.getString("viewtype"));
/*  93 */       if (str6.equals("0")) {
/*     */         continue;
/*     */       }
/*  96 */       arrayList1.add(Util.null2String(recordSet.getString("id")));
/*  97 */       arrayList2.add(Util.null2String(recordSet.getString("fieldname")));
/*  98 */       arrayList3.add(Util.null2String(recordSet.getString("fielddbtype")));
/*     */     } 
/*     */     
/* 101 */     int i = Util.getIntValue(Util.null2String(this.request.getParameter("rowcount")));
/* 102 */     String str7 = "";
/* 103 */     String str8 = "";
/*     */ 
/*     */     
/* 106 */     ArrayList<String> arrayList4 = new ArrayList();
/* 107 */     String str9 = "";
/* 108 */     for (byte b1 = 1; b1 <= i; b1++) {
/* 109 */       for (byte b = 0; b < arrayList1.size(); b++) {
/* 110 */         str4 = arrayList1.get(b);
/* 111 */         str8 = "field" + str4 + "_" + String.valueOf(b1);
/* 112 */         str9 = Util.null2String(this.request.getParameter(str8));
/*     */         
/* 114 */         if (!str9.equals("")) {
/* 115 */           arrayList4.add(String.valueOf(b1));
/*     */           
/*     */           break;
/*     */         } 
/*     */       } 
/*     */     } 
/* 121 */     String str10 = "";
/* 122 */     String str11 = "";
/* 123 */     String str12 = "";
/* 124 */     for (byte b2 = 0; b2 < arrayList4.size(); b2++) {
/*     */       
/* 126 */       str3 = "INSERT INTO " + str2 + " (requestid) VALUES (" + String.valueOf(this.recId) + ")";
/* 127 */       recordSet.executeSql(str3);
/*     */ 
/*     */ 
/*     */       
/* 131 */       str3 = "SELECT MAX(id) FROM " + str2;
/* 132 */       recordSet.executeSql(str3);
/* 133 */       if (recordSet.next()) {
/* 134 */         str12 = Util.null2String(recordSet.getString(1));
/*     */       } else {
/* 136 */         return false;
/*     */       } 
/*     */       
/* 139 */       str10 = arrayList4.get(b2);
/* 140 */       for (byte b = 0; b < arrayList1.size(); b++) {
/* 141 */         str4 = Util.null2String(arrayList1.get(b));
/* 142 */         str5 = Util.null2String(arrayList2.get(b));
/* 143 */         str8 = "field" + str4 + "_" + str10;
/* 144 */         if (this.countFieldStr != null && this.countFieldStr.equals(str5)) {
/* 145 */           str11 = str11 + str5 + "=" + String.valueOf(0.0F - Math.abs(Util.getFloatValue(this.request.getParameter(str8), 0.0F))) + ",";
/*     */         } else {
/* 147 */           str7 = arrayList3.get(b);
/* 148 */           if (str7.toUpperCase().indexOf("INT") >= 0) {
/* 149 */             str11 = str11 + str5 + "=" + Util.getIntValue(this.request.getParameter(str8), 0) + ",";
/* 150 */           } else if (str7.toUpperCase().indexOf("DECIMAL") >= 0) {
/* 151 */             str11 = str11 + str5 + "=" + Util.getFloatValue(this.request.getParameter(str8), 0.0F) + ",";
/*     */           } else {
/* 153 */             str11 = str11 + str5 + "='" + Util.null2String(this.request.getParameter(str8)) + "',";
/*     */           } 
/*     */         } 
/*     */       } 
/* 157 */       if (!str11.equals("")) {
/* 158 */         str11 = str11.substring(0, str11.lastIndexOf(","));
/*     */       }
/*     */       
/* 161 */       str3 = "UPDATE " + str2 + " SET " + str11 + " WHERE id=" + str12;
/* 162 */       recordSet.executeSql(str3);
/*     */ 
/*     */       
/* 165 */       str11 = "";
/*     */     } 
/*     */     
/* 168 */     return true;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setCountFieldStr(String paramString) {
/* 176 */     this.countFieldStr = paramString;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/RequestDetailManager.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */