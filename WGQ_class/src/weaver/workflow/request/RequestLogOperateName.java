/*     */ package weaver.workflow.request;
/*     */ 
/*     */ import com.engine.workflow.biz.freeNode.FreeNodeBiz;
/*     */ import com.engine.workflow.constant.menu.SystemMenuType;
/*     */ import com.engine.workflow.entity.requestForm.RightMenu;
/*     */ import com.engine.workflow.util.MenuOrderSetUtil;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class RequestLogOperateName
/*     */   extends BaseBean
/*     */ {
/*     */   public String getOperateName(String paramString1, String paramString2, String paramString3, String paramString4, String paramString5, int paramInt) {
/*  38 */     return getOperateName(paramString1, paramString2, paramString3, paramString4, paramString5, paramInt, "", "");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getOperateName(String paramString1, String paramString2, String paramString3, String paramString4, String paramString5, int paramInt, String paramString6, String paramString7) {
/*  50 */     String str1 = "";
/*  51 */     RecordSet recordSet = new RecordSet();
/*  52 */     String str2 = "0";
/*  53 */     if ("".equals(paramString6) && "".equals(paramString7)) {
/*  54 */       recordSet.executeSql("select needwfback from workflow_currentoperator where userid=" + paramString5 + " and nodeid=" + paramString3 + " and requestid=" + paramString2);
/*     */     } else {
/*  56 */       recordSet.executeSql("select needwfback from workflow_currentoperator where userid=" + paramString5 + " and nodeid=" + paramString3 + " and requestid=" + paramString2 + " and operatedate='" + paramString6 + "' and operatetime='" + paramString7 + "'");
/*     */     } 
/*  58 */     if (recordSet.next()) {
/*  59 */       str2 = Util.null2String(recordSet.getString("needwfback"));
/*     */     }
/*     */ 
/*     */     
/*  63 */     if ("t".equals(paramString4)) {
/*  64 */       return SystemEnv.getHtmlLabelName(2084, paramInt);
/*     */     }
/*     */ 
/*     */     
/*  68 */     if ("i".equals(paramString4)) {
/*  69 */       return SystemEnv.getHtmlLabelName(18913, paramInt);
/*     */     }
/*     */ 
/*     */     
/*  73 */     if ("j".equals(paramString4)) {
/*  74 */       return SystemEnv.getHtmlLabelName(10000191, Util.getIntValue(paramInt));
/*     */     }
/*     */     
/*  77 */     RightMenu rightMenu = new RightMenu();
/*  78 */     SystemMenuType systemMenuType = MenuOrderSetUtil.getSystemMenuType(paramString4);
/*  79 */     if (paramString4.equals("9")) {
/*  80 */       String str = "";
/*  81 */       int j = 1;
/*  82 */       RecordSet recordSet1 = new RecordSet();
/*  83 */       recordSet1.executeSql("select preisremark,takisremark from workflow_currentoperator where userid=" + paramString5 + " and nodeid=" + paramString3 + " and isremark=2 and (preisremark=9 or preisremark=1) and requestid=" + paramString2 + " order by preisremark");
/*  84 */       if (recordSet1.next()) {
/*  85 */         str = Util.null2String(recordSet1.getString("preisremark"));
/*  86 */         j = Util.getIntValue(recordSet1.getString("takisremark"));
/*     */       } 
/*  88 */       if (str.equals("1") && j != 2) {
/*  89 */         systemMenuType = SystemMenuType.POSTIL_FORWARD;
/*  90 */       } else if (str.equals("9")) {
/*  91 */         systemMenuType = SystemMenuType.POSTIL_CC;
/*     */       } 
/*     */     } 
/*     */     
/*  95 */     rightMenu.setSystemMenuType(systemMenuType);
/*     */     
/*  97 */     if ("1".equals(str2)) {
/*  98 */       rightMenu.setSystemSmallType(1);
/*     */     } else {
/* 100 */       rightMenu.setSystemSmallType(2);
/*     */     } 
/* 102 */     int i = FreeNodeBiz.getExtendNodeId(Util.getIntValue(paramString2), Util.getIntValue(paramString3));
/* 103 */     str1 = MenuOrderSetUtil.getLogName(Util.getIntValue(paramString1), i, paramInt, rightMenu);
/* 104 */     return str1;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/RequestLogOperateName.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */