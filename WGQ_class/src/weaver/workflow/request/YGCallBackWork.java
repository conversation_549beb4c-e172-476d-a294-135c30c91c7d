/*    */ package weaver.workflow.request;
/*    */ 
/*    */ import java.util.HashMap;
/*    */ import weaver.hrm.resource.ResourceComInfo;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class YGCallBackWork
/*    */   implements RequestOutWork
/*    */ {
/*    */   public boolean execute(int paramInt, HashMap paramHashMap) throws RequestOutWorkException {
/* 36 */     boolean bool = true;
/* 37 */     RequestOutData requestOutData = new RequestOutData(paramInt);
/* 38 */     ResourceComInfo resourceComInfo = null;
/*    */     try {
/* 40 */       resourceComInfo = new ResourceComInfo();
/* 41 */     } catch (Exception exception) {
/* 42 */       throw new RequestOutWorkException();
/*    */     } 
/* 44 */     RequestDataPost requestDataPost = new RequestDataPost();
/* 45 */     String str1 = "http://www.1stl.com/wf/pur_pay_request.jsp";
/* 46 */     bool = requestOutData.loadData();
/*    */     
/* 48 */     String str2 = "1";
/* 49 */     if (paramHashMap.get("usertype").equals("0")) {
/* 50 */       str2 = "1";
/*    */     } else {
/* 52 */       str2 = "2";
/*    */     } 
/* 54 */     requestDataPost.setParam("requestid", paramInt + "");
/* 55 */     requestDataPost.setParam("workflowid", paramHashMap.get("workflowid").toString());
/* 56 */     requestDataPost.setParam("username", resourceComInfo.getLoginID(paramHashMap.get("userid").toString()));
/* 57 */     requestDataPost.setParam("logintype", str2);
/* 58 */     requestDataPost.setParam("src", paramHashMap.get("src").toString());
/* 59 */     requestDataPost.setParam("iscreate", paramHashMap.get("iscreate").toString());
/* 60 */     requestDataPost.setParam("requestlevel", paramHashMap.get("requestlevel").toString());
/* 61 */     requestDataPost.setParam("requestmark", paramHashMap.get("requestmark").toString());
/* 62 */     requestDataPost.setParam("misoanodeid", requestOutData.getData("misoanodeid").toString());
/*    */     
/* 64 */     requestDataPost.doPost(str1);
/*    */     
/* 66 */     return bool;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/YGCallBackWork.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */