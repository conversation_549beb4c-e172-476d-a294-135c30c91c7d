/*     */ package weaver.workflow.request;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.List;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class FnaPeriodFilter
/*     */ {
/*     */   public List<String> multiDimensionFilterPeriod(int paramInt1, int paramInt2, String paramString1, String paramString2) {
/*  33 */     int i = 0;
/*     */     
/*  35 */     int j = 0;
/*     */     
/*  37 */     int k = 0;
/*  38 */     int m = 0;
/*     */ 
/*     */     
/*  41 */     String str1 = "SELECT a.id,a.isFilter, a.onlyEnd, a.choice  FROM FnaMultiPeriodFilter a  WHERE a.workflowId=? AND a.fieldId=? and a.accountId = ?";
/*  42 */     RecordSet recordSet = new RecordSet();
/*  43 */     recordSet.executeQuery(str1, new Object[] { Integer.valueOf(paramInt1), Integer.valueOf(paramInt2), paramString2 });
/*  44 */     if (recordSet.next()) {
/*     */       
/*  46 */       i = recordSet.getInt("isFilter");
/*     */       
/*  48 */       j = recordSet.getInt("onlyEnd");
/*     */       
/*  50 */       k = recordSet.getInt("choice");
/*  51 */       m = recordSet.getInt("id");
/*     */     } 
/*     */     
/*  54 */     boolean bool1 = (i >= 1) ? true : false;
/*  55 */     boolean bool2 = (j >= 1) ? true : false;
/*  56 */     boolean bool3 = (k >= 1) ? true : false;
/*     */ 
/*     */     
/*  59 */     String str2 = recordSet.getDBType();
/*     */     
/*  61 */     String str3 = paramString1 + " a ";
/*  62 */     String str4 = " where 1=1 and (a.isArchive <> 1  or a.isArchive is null) ";
/*     */     
/*  64 */     if (bool1) {
/*     */       String str;
/*     */ 
/*     */       
/*  68 */       if (bool3) {
/*     */         
/*  70 */         str = " = ";
/*     */       } else {
/*     */         
/*  73 */         str = " <> ";
/*     */       } 
/*     */ 
/*     */       
/*  77 */       if ("mysql".equalsIgnoreCase(str2)) {
/*  78 */         str3 = str3 + " join FnaMultiPeriodFilterDtl c ON left(a.autoCode, length(c.periodCode)) " + str + " c.periodCode  join FnaMultiPeriodFilter b ON b.id = c.mainId  ";
/*     */       
/*     */       }
/*  81 */       else if ("postgresql".equalsIgnoreCase(str2)) {
/*  82 */         str3 = str3 + " join FnaMultiPeriodFilterDtl c ON left(a.autoCode, length(c.periodCode)) " + str + " c.periodCode  join FnaMultiPeriodFilter b ON b.id = c.mainId  ";
/*     */       
/*     */       }
/*  85 */       else if ("sqlserver".equalsIgnoreCase(str2)) {
/*  86 */         str3 = str3 + " join FnaMultiPeriodFilterDtl c on left(a.autoCode, len(c.periodCode)) " + str + " c.periodCode  join FnaMultiPeriodFilter b ON b.id = c.mainId ";
/*     */       } else {
/*     */         
/*  89 */         str3 = str3 + " join FnaMultiPeriodFilterDtl c on substr(a.autoCode, 0, length(c.periodCode)) " + str + " c.periodCode  join FnaMultiPeriodFilter b ON b.id = c.mainId ";
/*     */       } 
/*     */     } 
/*     */ 
/*     */     
/*  94 */     if (bool2)
/*     */     {
/*  96 */       if ("mysql".equalsIgnoreCase(str2)) {
/*  97 */         str4 = str4 + " and NOT exists(select 1 from " + paramString1 + " t where t.autoCode like concat(a.autoCode,'_%')) ";
/*  98 */       } else if ("sqlserver".equalsIgnoreCase(str2)) {
/*  99 */         str4 = str4 + " and NOT exists(select 1 from " + paramString1 + " t where t.autoCode like a.autoCode+'_%') ";
/*     */       } else {
/* 101 */         str4 = str4 + " and NOT exists(select 1 from " + paramString1 + " t where t.autoCode like a.autoCode||'_%') ";
/*     */       } 
/*     */     }
/*     */     
/* 105 */     if (bool1) {
/*     */       
/* 107 */       str4 = str4 + " AND b.workflowId=" + paramInt1 + " AND b.fieldId=" + paramInt2;
/* 108 */       if (!bool3)
/*     */       {
/* 110 */         str4 = str4 + " and a.id not in (  select ta.periodAutoCode from FnaMultiPeriodFilterDtl ta join FnaMultiPeriodFilter tb on tb.id=ta.mainId  where tb.workflowId=" + paramInt1 + " and tb.fieldId=" + paramInt2 + ") ";
/*     */       }
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/* 116 */     ArrayList<String> arrayList = new ArrayList();
/*     */     
/* 118 */     str1 = "SELECT a.id from " + str3 + str4;
/*     */     
/* 120 */     recordSet.executeQuery(str1, new Object[0]);
/* 121 */     while (recordSet.next()) {
/* 122 */       String str = Util.null2String(recordSet.getString("id"));
/*     */       
/* 124 */       arrayList.add(str);
/*     */     } 
/* 126 */     return arrayList;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/FnaPeriodFilter.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */