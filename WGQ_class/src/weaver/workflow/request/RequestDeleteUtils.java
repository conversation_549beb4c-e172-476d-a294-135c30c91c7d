/*      */ package weaver.workflow.request;
/*      */ 
/*      */ import com.alibaba.fastjson.JSONArray;
/*      */ import com.alibaba.fastjson.JSONObject;
/*      */ import com.api.doc.search.service.OperateService;
/*      */ import com.api.workflow.util.ServiceUtil;
/*      */ import com.cloudstore.dev.api.util.Util_TableMap;
/*      */ import com.engine.kq.biz.KQFlowActiontBiz;
/*      */ import com.engine.workflow.biz.requestForm.RequestFlowRemindBiz;
/*      */ import com.engine.workflow.biz.requestList.RequestAttentionBiz;
/*      */ import com.engine.workflow.biz.workflowCore.WorkflowBaseBiz;
/*      */ import com.engine.workflow.entity.core.NodeInfoEntity;
/*      */ import java.util.ArrayList;
/*      */ import java.util.Calendar;
/*      */ import java.util.HashMap;
/*      */ import java.util.Iterator;
/*      */ import java.util.LinkedList;
/*      */ import java.util.List;
/*      */ import java.util.Map;
/*      */ import weaver.common.StringUtil;
/*      */ import weaver.conn.RecordSet;
/*      */ import weaver.conn.RecordSetTrans;
/*      */ import weaver.cpt.util.CptWfUtil;
/*      */ import weaver.docs.docs.util.DocumentDeleteSecurityUtil;
/*      */ import weaver.docs.docs.util.DocumentDeleteStatusMould;
/*      */ import weaver.docs.docs.util.MouldStatus;
/*      */ import weaver.fna.general.FnaCommon;
/*      */ import weaver.general.BaseBean;
/*      */ import weaver.general.DBColumnTypeUtils;
/*      */ import weaver.general.Util;
/*      */ import weaver.hrm.User;
/*      */ import weaver.hrm.attendance.manager.HrmAttVacationManager;
/*      */ import weaver.hrm.attendance.manager.HrmPaidLeaveManager;
/*      */ import weaver.systeminfo.SysMaintenanceLog;
/*      */ import weaver.workflow.monitor.Monitor;
/*      */ import weaver.workflow.monitor.MonitorDTO;
/*      */ import weaver.workflow.msg.MsgPushUtil;
/*      */ import weaver.workflow.msg.PoppupRemindInfoUtil;
/*      */ import weaver.workflow.msg.entity.MsgEntity;
/*      */ import weaver.workflow.report.RequestDeleteLog;
/*      */ import weaver.workflow.workflow.WfFunctionManageUtil;
/*      */ import weaver.workflow.workflow.WorkflowConfigComInfo;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ public class RequestDeleteUtils
/*      */   extends BaseBean
/*      */ {
/*      */   private Map<String, RequestDeleteInfo> deleteTableMap;
/*      */   private int requestid;
/*      */   private RecordSetTrans rst;
/*      */   private RequestDeleteLog log;
/*      */   private JSONObject delDataInfo;
/*      */   private int isbill;
/*      */   private int formid;
/*      */   private int workflowid;
/*      */   private User user;
/*   61 */   private DocumentDeleteSecurityUtil docDeleteUtil = new DocumentDeleteSecurityUtil();
/*      */   
/*      */   private void init() {
/*   64 */     RecordSet recordSet = new RecordSet();
/*   65 */     this.delDataInfo = new JSONObject();
/*   66 */     boolean bool = recordSet.getDBType().equals("oracle");
/*      */     
/*   68 */     this.deleteTableMap = new HashMap<>();
/*   69 */     RequestDeleteInfo requestDeleteInfo = new RequestDeleteInfo();
/*   70 */     ArrayList<String> arrayList = new ArrayList();
/*      */ 
/*      */     
/*   73 */     String str = " where requestid = " + this.requestid;
/*   74 */     if (bool) {
/*   75 */       arrayList.add("HRMIDS");
/*   76 */       requestDeleteInfo.setClobColumn(arrayList);
/*      */     } 
/*   78 */     requestDeleteInfo.setCopytablename("workflow_requestbase_dellog");
/*   79 */     requestDeleteInfo.setPrimarykey("requestid");
/*   80 */     requestDeleteInfo.setQueryWhere(str);
/*   81 */     this.deleteTableMap.put("workflow_requestbase", requestDeleteInfo);
/*      */ 
/*      */     
/*   84 */     requestDeleteInfo = new RequestDeleteInfo();
/*   85 */     requestDeleteInfo.setCopytablename("workflow_curroperator_dellog");
/*   86 */     requestDeleteInfo.setQueryWhere(str);
/*   87 */     this.deleteTableMap.put("workflow_currentoperator", requestDeleteInfo);
/*      */ 
/*      */     
/*   90 */     requestDeleteInfo = new RequestDeleteInfo();
/*   91 */     if (bool) {
/*   92 */       arrayList = new ArrayList<>();
/*   93 */       arrayList.add("REMARK");
/*   94 */       arrayList.add("RECEIVEDPERSONS");
/*   95 */       arrayList.add("RECEIVEDPERSONIDS");
/*   96 */       requestDeleteInfo.setClobColumn(arrayList);
/*      */     } 
/*   98 */     requestDeleteInfo.setCopytablename("workflow_requestLog_dellog");
/*   99 */     requestDeleteInfo.setPrimarykey("logid");
/*  100 */     requestDeleteInfo.setQueryWhere(str);
/*  101 */     this.deleteTableMap.put("workflow_requestLog", requestDeleteInfo);
/*      */ 
/*      */     
/*  104 */     requestDeleteInfo = new RequestDeleteInfo();
/*  105 */     requestDeleteInfo.setCopytablename("workflow_nownode_dellog");
/*  106 */     requestDeleteInfo.setQueryWhere(str);
/*  107 */     this.deleteTableMap.put("workflow_nownode", requestDeleteInfo);
/*      */ 
/*      */     
/*  110 */     str = " where exists (select 1 from workflow_requestLog where workflow_requestLog.requestid = " + this.requestid + " and workflow_requestLog.logid = workflow_logviewusers.logid)";
/*  111 */     requestDeleteInfo = new RequestDeleteInfo();
/*  112 */     requestDeleteInfo.setQueryWhere(str);
/*  113 */     this.deleteTableMap.put("workflow_logviewusers", requestDeleteInfo);
/*      */     
/*  115 */     str = " where id = " + this.requestid;
/*  116 */     requestDeleteInfo = new RequestDeleteInfo();
/*  117 */     requestDeleteInfo.setQueryWhere(str);
/*  118 */     this.deleteTableMap.put("workflow_requestViewLog", requestDeleteInfo);
/*      */     
/*  120 */     str = " where requestid = " + this.requestid;
/*  121 */     requestDeleteInfo = new RequestDeleteInfo();
/*  122 */     requestDeleteInfo.setQueryWhere(str);
/*  123 */     this.deleteTableMap.put("Workflow_SharedScope", requestDeleteInfo);
/*      */     
/*  125 */     str = "where id in (select a.id from (select wgt.id from workflow_groupdetail wgt, workflow_nodegroup wng , workflow_nodebase wnb where wgt.groupid = wng.id and wng.nodeid = wnb.id and wnb.IsFreeNode = '1' and wnb.requestid = " + this.requestid + ") a)";
/*  126 */     requestDeleteInfo = new RequestDeleteInfo();
/*  127 */     requestDeleteInfo.setQueryWhere(str);
/*  128 */     this.deleteTableMap.put("workflow_groupdetail", requestDeleteInfo);
/*      */     
/*  130 */     str = " where EXISTS(select 1 from workflow_nodebase b where workflow_nodegroup.nodeid=b.id and b.IsFreeNode='1' and b.requestid=" + this.requestid + ")";
/*  131 */     requestDeleteInfo = new RequestDeleteInfo();
/*  132 */     requestDeleteInfo.setQueryWhere(str);
/*  133 */     this.deleteTableMap.put("workflow_nodegroup", requestDeleteInfo);
/*      */     
/*  135 */     str = " where EXISTS(select 1 from workflow_nodebase b where workflow_nodemode.nodeid=b.id and b.IsFreeNode='1' and b.requestid= " + this.requestid + ")";
/*  136 */     requestDeleteInfo = new RequestDeleteInfo();
/*  137 */     requestDeleteInfo.setQueryWhere(str);
/*  138 */     this.deleteTableMap.put("workflow_nodemode", requestDeleteInfo);
/*      */     
/*  140 */     str = " where EXISTS(select 1 from workflow_nodebase b where workflow_nodeform.nodeid=b.id and b.IsFreeNode='1' and b.requestid= " + this.requestid + ")";
/*  141 */     requestDeleteInfo = new RequestDeleteInfo();
/*  142 */     requestDeleteInfo.setQueryWhere(str);
/*  143 */     this.deleteTableMap.put("workflow_nodeform", requestDeleteInfo);
/*      */     
/*  145 */     str = " where EXISTS(select 1 from workflow_nodebase b where workflow_flownode.nodeid=b.id and b.IsFreeNode='1' and b.requestid=" + this.requestid + ")";
/*  146 */     requestDeleteInfo = new RequestDeleteInfo();
/*  147 */     requestDeleteInfo.setQueryWhere(str);
/*  148 */     this.deleteTableMap.put("workflow_flownode", requestDeleteInfo);
/*      */     
/*  150 */     str = " where EXISTS(select 1 from workflow_nodebase b where workflow_nodelink.nodeid=b.id and b.IsFreeNode='1' and b.requestid=" + this.requestid + ") or EXISTS(select 1 from workflow_nodebase b where workflow_nodelink.destnodeid=b.id and b.IsFreeNode='1' and b.requestid=" + this.requestid + ") or wfrequestid=" + this.requestid;
/*      */     
/*  152 */     requestDeleteInfo = new RequestDeleteInfo();
/*  153 */     requestDeleteInfo.setQueryWhere(str);
/*  154 */     this.deleteTableMap.put("workflow_nodelink", requestDeleteInfo);
/*      */     
/*  156 */     str = " where IsFreeNode='1' and requestid=" + this.requestid;
/*  157 */     requestDeleteInfo = new RequestDeleteInfo();
/*  158 */     requestDeleteInfo.setQueryWhere(str);
/*  159 */     this.deleteTableMap.put("workflow_nodebase", requestDeleteInfo);
/*      */ 
/*      */     
/*  162 */     recordSet.executeQuery("select workflowid from workflow_requestbase where requestid = ? ", new Object[] { Integer.valueOf(this.requestid) });
/*  163 */     if (recordSet.next()) {
/*  164 */       this.workflowid = recordSet.getInt(1);
/*  165 */       this.log.setWorkflowId(String.valueOf(this.workflowid));
/*      */     } 
/*      */ 
/*      */     
/*  169 */     recordSet.executeQuery("select isbill,formid from workflow_base where id = ? ", new Object[] { Integer.valueOf(this.workflowid) });
/*  170 */     if (recordSet.next()) {
/*  171 */       this.isbill = recordSet.getInt(1);
/*  172 */       this.formid = recordSet.getInt(2);
/*      */     } 
/*      */   }
/*      */ 
/*      */   
/*      */   public RequestDeleteUtils() {}
/*      */   
/*      */   public RequestDeleteUtils(int paramInt, RecordSetTrans paramRecordSetTrans, RequestDeleteLog paramRequestDeleteLog) {
/*  180 */     this.requestid = paramInt;
/*  181 */     this.rst = paramRecordSetTrans;
/*  182 */     this.log = paramRequestDeleteLog;
/*      */   }
/*      */   
/*      */   public RequestDeleteUtils(int paramInt, RecordSetTrans paramRecordSetTrans, RequestDeleteLog paramRequestDeleteLog, User paramUser) {
/*  186 */     this.requestid = paramInt;
/*  187 */     this.rst = paramRecordSetTrans;
/*  188 */     this.log = paramRequestDeleteLog;
/*  189 */     this.user = paramUser;
/*      */   }
/*      */   
/*      */   public void executeDeleteRequest() throws Exception {
/*  193 */     RequestOperationMsgManager requestOperationMsgManager = new RequestOperationMsgManager();
/*  194 */     List<MsgEntity> list = requestOperationMsgManager.requestDeletMsg(String.valueOf(this.requestid));
/*      */     
/*  196 */     init();
/*  197 */     (new RequestFlowRemindBiz()).deleteFlowMsgByRequestid(this.requestid + "");
/*  198 */     executeDeleteAddinOperate();
/*  199 */     updateCodeseqReserved();
/*  200 */     checkAndDeleteAcc();
/*  201 */     deleteBaseTableInfo();
/*  202 */     deleteFormData();
/*  203 */     deletePoppupRemindInfo();
/*  204 */     deleteSubWFRequestInfo();
/*      */     
/*  206 */     this.log.save(this.rst);
/*  207 */     String str = "update workflow_requestdeletelog set deletetabledata = ?,isold='0' where request_id = ?";
/*  208 */     this.rst.executeUpdate(str, new Object[] { this.delDataInfo.toString(), Integer.valueOf(this.requestid) });
/*  209 */     deleteSuperviseByRequestId();
/*      */     
/*  211 */     (new RequestAttentionBiz()).cancelAttention(this.requestid, "delete", this.rst);
/*  212 */     (new MsgPushUtil()).pushMsg(list);
/*      */   }
/*      */ 
/*      */   
/*      */   private void executeDeleteAddinOperate() throws Exception {
/*      */     try {
/*  218 */       RequestCheckAddinRules requestCheckAddinRules = new RequestCheckAddinRules();
/*  219 */       requestCheckAddinRules.resetParameter();
/*  220 */       requestCheckAddinRules.setTrack(false);
/*  221 */       requestCheckAddinRules.setStart(false);
/*  222 */       requestCheckAddinRules.setNodeid(-3);
/*  223 */       requestCheckAddinRules.setRequestid(this.requestid);
/*  224 */       requestCheckAddinRules.setWorkflowid(this.workflowid);
/*  225 */       requestCheckAddinRules.setObjid(-3);
/*  226 */       requestCheckAddinRules.setObjtype(1);
/*  227 */       requestCheckAddinRules.setIsbill(this.isbill);
/*  228 */       requestCheckAddinRules.setFormid(this.formid);
/*  229 */       requestCheckAddinRules.setIspreadd("0");
/*  230 */       requestCheckAddinRules.setUser(this.user);
/*  231 */       requestCheckAddinRules.setSpecialOperate(true);
/*  232 */       requestCheckAddinRules.checkAddinRules();
/*  233 */     } catch (Exception exception) {
/*  234 */       exception.printStackTrace();
/*  235 */       throw new Exception("workflow interface action error:" + exception.getMessage());
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void deleteSubWFRequestInfo() {
/*  244 */     ArrayList<ArrayList<Integer>> arrayList = new ArrayList();
/*  245 */     RecordSet recordSet = new RecordSet();
/*  246 */     recordSet.executeQuery("select * from workflow_subwfrequest where subrequestid = ?", new Object[] { Integer.valueOf(this.requestid) });
/*  247 */     while (recordSet.next()) {
/*  248 */       ArrayList<Integer> arrayList1 = new ArrayList();
/*  249 */       arrayList1.add(Integer.valueOf(recordSet.getInt(1)));
/*  250 */       arrayList1.add(Integer.valueOf(recordSet.getInt(2)));
/*  251 */       arrayList1.add(Integer.valueOf(recordSet.getInt(3)));
/*  252 */       arrayList1.add(recordSet.getString(4));
/*  253 */       arrayList1.add(Integer.valueOf(recordSet.getInt(5)));
/*  254 */       arrayList1.add(Integer.valueOf(recordSet.getInt(6)));
/*  255 */       arrayList1.add(recordSet.getString(7));
/*  256 */       arrayList.add(arrayList1);
/*      */     } 
/*  258 */     recordSet.executeBatchSql("insert into Workflow_SubwfRequest_dellog values (?,?,?,?,?,?,?)", arrayList);
/*  259 */     recordSet.executeUpdate("delete from workflow_subwfrequest where subrequestid = ?", new Object[] { Integer.valueOf(this.requestid) });
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private void deleteSuperviseByRequestId() throws Exception {
/*  266 */     this.rst.executeUpdate("delete from WORKFLOW_SUPERVISEOPERATOR where requestid = ?", new Object[] { Integer.valueOf(this.requestid) });
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void checkAndDeleteAcc() {
/*  273 */     ArrayList arrayList = new ArrayList();
/*  274 */     RecordSet recordSet = new RecordSet();
/*  275 */     recordSet.execute("select isneeddelacc from workflow_base where id in (select workflowid from workflow_requestbase where requestid =" + this.requestid + ")");
/*  276 */     if (recordSet.next()) {
/*  277 */       String str = recordSet.getString("isneeddelacc");
/*  278 */       if ("1".equals(str)) {
/*  279 */         delWfFormAcc(this.requestid, arrayList);
/*  280 */         delWfSignAcc(this.requestid, arrayList);
/*      */       } 
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private void delWfFormAcc(int paramInt, ArrayList paramArrayList) {
/*  292 */     RecordSet recordSet1 = new RecordSet();
/*  293 */     RecordSet recordSet2 = new RecordSet();
/*  294 */     RecordSet recordSet3 = new RecordSet();
/*  295 */     RecordSet recordSet4 = new RecordSet();
/*  296 */     OperateService operateService = new OperateService();
/*      */     
/*  298 */     String str1 = "";
/*  299 */     String str2 = "";
/*  300 */     String str3 = "";
/*      */ 
/*      */     
/*  303 */     recordSet1.executeSql("select b.formid, b.isbill from workflow_requestbase a, workflow_base b where requestid = " + paramInt + " and a.workflowid = b.id");
/*  304 */     if (recordSet1.next()) {
/*  305 */       int i = Util.getIntValue(recordSet1.getString("formid"));
/*  306 */       int j = Util.getIntValue(recordSet1.getString("isbill"));
/*  307 */       if (j == 1) {
/*  308 */         String str4 = "";
/*  309 */         String str5 = "";
/*  310 */         String str6 = "";
/*  311 */         recordSet1.execute("select tablename,detailtablename,detailkeyfield from workflow_bill where id=" + i);
/*  312 */         if (recordSet1.next()) {
/*  313 */           str4 = Util.null2String(recordSet1.getString(1));
/*  314 */           str5 = Util.null2String(recordSet1.getString(2)).trim();
/*  315 */           str6 = Util.null2String(recordSet1.getString(3));
/*  316 */           String str = "";
/*  317 */           boolean bool = false;
/*  318 */           if (!"".equals(str4)) {
/*  319 */             recordSet3.executeSql("select fieldname,viewtype,detailtable from workflow_billfield where billid= " + i + " and fieldhtmltype = '6'");
/*  320 */             while (recordSet3.next()) {
/*  321 */               String str7 = Util.null2String(recordSet3.getString(1));
/*  322 */               int k = Util.getIntValue(recordSet3.getString(2), 0);
/*  323 */               String str8 = Util.null2String(recordSet3.getString(3));
/*  324 */               if (!str7.trim().equals("")) {
/*  325 */                 if (k == 0) {
/*  326 */                   str1 = "select " + str7 + " from " + str4 + " where requestid=" + paramInt;
/*      */                 } else {
/*  328 */                   if (str8.trim().equals("")) str8 = str5; 
/*  329 */                   boolean bool1 = false;
/*  330 */                   if (str.equals(str8)) {
/*  331 */                     bool1 = bool;
/*      */                   } else {
/*  333 */                     if (recordSet1.getDBType().toUpperCase().equals("ORACLE")) {
/*  334 */                       str1 = "select * from " + str8 + " where rownum<2";
/*      */                     } else {
/*  336 */                       str1 = "select top 1 * from " + str8;
/*      */                     } 
/*  338 */                     if (recordSet2.executeSql(str1)) {
/*  339 */                       String[] arrayOfString = recordSet2.getColumnName();
/*  340 */                       if (arrayOfString != null) {
/*  341 */                         for (byte b = 0; b < arrayOfString.length; b++) {
/*  342 */                           if (arrayOfString[b].toUpperCase().equals("REQUESTID")) {
/*  343 */                             bool1 = true;
/*      */                             break;
/*      */                           } 
/*      */                         } 
/*      */                       }
/*      */                     } 
/*  349 */                     bool = bool1;
/*  350 */                     str = str8;
/*      */                   } 
/*  352 */                   if (bool1) {
/*  353 */                     str1 = "select " + str7 + " from " + str8 + " where requestid=" + paramInt;
/*      */                   } else {
/*  355 */                     str1 = "select a." + str7 + " from " + str8 + " a," + str4 + " b where a." + str6 + "=b.id and b.requestid=" + paramInt;
/*      */                   } 
/*      */                 } 
/*  358 */                 recordSet4.executeSql(str1);
/*  359 */                 while (recordSet4.next()) {
/*  360 */                   str2 = recordSet4.getString(str7);
/*  361 */                   if (str2 != null && !"".equals(str2)) {
/*  362 */                     String[] arrayOfString = Util.TokenizerString2(str2, ",");
/*  363 */                     for (byte b = 0; b < arrayOfString.length; b++) {
/*  364 */                       if (isCanDel(Util.getIntValue(arrayOfString[b], 0))) {
/*      */                         try {
/*  366 */                           securityDeleteDoc(arrayOfString[b]);
/*  367 */                         } catch (Exception exception) {
/*  368 */                           exception.printStackTrace();
/*      */                         } 
/*      */                       }
/*      */                     } 
/*      */                   } 
/*      */                 } 
/*      */               } 
/*      */             } 
/*      */           } 
/*      */         } 
/*      */       } else {
/*      */         
/*  380 */         recordSet3.executeSql("select fieldname from workflow_formfield a,workflow_formdict b where a.formid = " + i + " and a.fieldid = b.id and b.fieldhtmltype = '6' and (a.isdetail is null or a.isdetail<>'1')");
/*  381 */         while (recordSet3.next()) {
/*  382 */           String str = recordSet3.getString("fieldname");
/*  383 */           recordSet4.executeSql("select " + str + " from workflow_form where requestid = " + paramInt);
/*  384 */           if (recordSet4.next()) {
/*  385 */             str2 = recordSet4.getString(str);
/*  386 */             if (str2 != null && !"".equals(str2)) {
/*  387 */               String[] arrayOfString = Util.TokenizerString2(str2, ",");
/*  388 */               for (byte b = 0; b < arrayOfString.length; b++) {
/*  389 */                 if (isCanDel(Util.getIntValue(arrayOfString[b], 0))) {
/*      */                   
/*      */                   try {
/*  392 */                     securityDeleteDoc(arrayOfString[b]);
/*  393 */                   } catch (Exception exception) {
/*  394 */                     exception.printStackTrace();
/*      */                   } 
/*      */                 }
/*      */               } 
/*      */             } 
/*      */           } 
/*      */         } 
/*      */         
/*  402 */         str1 = "select a.fieldname from workflow_formdictdetail a,workflow_formfield b where a.id=b.fieldid and a.fieldhtmltype='6' and b.isdetail='1' and b.formid=" + i;
/*  403 */         recordSet3.executeSql(str1);
/*  404 */         while (recordSet3.next()) {
/*  405 */           String str = recordSet3.getString("fieldname");
/*  406 */           recordSet4.executeSql("select " + str + " from workflow_formdetail where requestid = " + paramInt);
/*  407 */           while (recordSet4.next()) {
/*  408 */             str2 = recordSet4.getString(str);
/*  409 */             if (str2 != null && !"".equals(str2)) {
/*  410 */               String[] arrayOfString = Util.TokenizerString2(str2, ",");
/*  411 */               for (byte b = 0; b < arrayOfString.length; b++) {
/*  412 */                 if (isCanDel(Util.getIntValue(arrayOfString[b], 0))) {
/*      */                   
/*      */                   try {
/*  415 */                     securityDeleteDoc(arrayOfString[b]);
/*  416 */                   } catch (Exception exception) {
/*  417 */                     exception.printStackTrace();
/*      */                   } 
/*      */                 }
/*      */               } 
/*      */             } 
/*      */           } 
/*      */         } 
/*      */       } 
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private void delWfSignAcc(int paramInt, ArrayList paramArrayList) {
/*  434 */     OperateService operateService = new OperateService();
/*  435 */     String str = "select annexdocids from workflow_requestLog where requestid=" + paramInt;
/*  436 */     RecordSet recordSet = new RecordSet();
/*  437 */     recordSet.executeSql(str);
/*  438 */     while (recordSet.next()) {
/*  439 */       String str1 = Util.null2String(recordSet.getString("annexdocids"));
/*  440 */       String[] arrayOfString = Util.TokenizerString2(str1, ",");
/*  441 */       for (byte b = 0; b < arrayOfString.length; b++) {
/*  442 */         if (isCanDel(Util.getIntValue(arrayOfString[b], 0))) {
/*      */           try {
/*  444 */             securityDeleteDoc(arrayOfString[b]);
/*  445 */           } catch (Exception exception) {
/*  446 */             exception.printStackTrace();
/*      */           } 
/*      */         }
/*      */       } 
/*      */     } 
/*      */   }
/*      */   
/*      */   private boolean isCanDel(int paramInt) {
/*  454 */     RecordSet recordSet = new RecordSet();
/*  455 */     String str = "";
/*  456 */     if ("mysql".equals(recordSet.getDBType())) {
/*  457 */       str = "SELECT 1 FROM workflow_requestbase WHERE  FIND_IN_SET(" + paramInt + ",docids)  AND (deleted = 0 OR deleted IS NULL)";
/*  458 */     } else if ("oracle".equals(recordSet.getDBType())) {
/*  459 */       str = "select 1 from workflow_requestbase where ','||docids||',' like '%," + paramInt + ",%'  and (deleted = 0 or deleted is null)";
/*      */     }
/*  461 */     else if ("postgresql".equals(recordSet.getDBType())) {
/*  462 */       str = "select 1 from workflow_requestbase where ','||docids||',' like '%," + paramInt + ",%'  and (deleted = 0 or deleted is null)";
/*      */     } else {
/*      */       
/*  465 */       str = "select 1 from workflow_requestbase where ','+cast(docids AS varchar(4000))+',' like '%," + paramInt + ",%'  and (deleted = 0 or deleted is null)";
/*      */     } 
/*  467 */     recordSet.executeSql(str);
/*      */     
/*  469 */     if (recordSet.next()) {
/*  470 */       return false;
/*      */     }
/*  472 */     return true;
/*      */   }
/*      */ 
/*      */   
/*      */   private void securityDeleteDoc(String paramString) {
/*  477 */     this.docDeleteUtil.deleteDocids(paramString, DocumentDeleteStatusMould.WORKFLOW.getMouldCode(), this.user, this.requestid + "", MouldStatus.WF_DELETE_REQUEST.getItemCode());
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String executeMonitorDelete(String paramString1, User paramUser, String paramString2) {
/*  488 */     RecordSetTrans recordSetTrans = new RecordSetTrans();
/*      */     try {
/*  490 */       if (paramString1 == null || "".equals(paramString1)) return "fail"; 
/*  491 */       if (paramString1.endsWith(",")) paramString1 = paramString1.substring(0, paramString1.length() - 1); 
/*  492 */       RecordSet recordSet1 = new RecordSet();
/*  493 */       recordSetTrans.setAutoCommit(false);
/*  494 */       Monitor monitor = new Monitor();
/*  495 */       String[] arrayOfString = Util.TokenizerString2(paramString1, ",");
/*  496 */       String str = "";
/*  497 */       SysMaintenanceLog sysMaintenanceLog = new SysMaintenanceLog();
/*  498 */       RecordSet recordSet2 = new RecordSet();
/*  499 */       boolean bool = false;
/*      */       
/*  501 */       for (String str1 : arrayOfString) {
/*  502 */         List<String> list; synchronized (RequestDeleteUtils.class) {
/*  503 */           Object object = Util_TableMap.getObjVal("deleteIngRequestList");
/*  504 */           if (object == null) {
/*  505 */             object = new LinkedList();
/*  506 */             Util_TableMap.setObjVal("deleteIngRequestList", object);
/*      */           } 
/*  508 */           list = (List)object;
/*  509 */           recordSet1.writeLog("lyy=====>deleteIngRequestList: " + list);
/*  510 */           if (list.contains(str1)) {
/*      */             continue;
/*      */           }
/*  513 */           list.add(str1);
/*  514 */           recordSet1.writeLog("lyy=====>deleteIngRequestList1: " + list);
/*      */         } 
/*      */         
/*  517 */         String str2 = "";
/*  518 */         recordSet1.execute("select creater,workflowid from workflow_requestbase where requestid = " + str1);
/*  519 */         if (recordSet1.next()) {
/*  520 */           String str5 = recordSet1.getString(1);
/*  521 */           String str6 = recordSet1.getString(2);
/*  522 */           str2 = str6;
/*  523 */           MonitorDTO monitorDTO = monitor.getMonitorInfo(String.valueOf(paramUser.getUID()), str5, str6);
/*      */ 
/*      */           
/*  526 */           recordSet2.executeSql("select wfid from cpt_cptwfconf where wfid=" + str6);
/*  527 */           if (recordSet2.next()) {
/*  528 */             bool = true;
/*      */           }
/*  530 */           recordSet2.executeSql("select formid from workflow_base where id=" + str6);
/*  531 */           if (recordSet2.next()) {
/*  532 */             int i = recordSet2.getInt(1);
/*  533 */             if (i == 18 || i == 19 || i == 201 || i == 220 || i == 221 || i == 222 || i == 224) {
/*  534 */               bool = true;
/*      */             }
/*      */           } 
/*      */           
/*  538 */           boolean bool1 = monitorDTO.getIsdelete();
/*  539 */           if (bool1) {
/*  540 */             str = str + str1 + ",";
/*      */           } else {
/*      */             continue;
/*      */           } 
/*      */         } 
/*      */         
/*      */         try {
/*  547 */           FnaCommon fnaCommon = new FnaCommon();
/*  548 */           fnaCommon.doWfForceOver(Util.getIntValue(str1, 0), 0, true);
/*  549 */         } catch (Exception exception) {
/*  550 */           (new BaseBean()).writeLog(exception);
/*      */         } 
/*      */ 
/*      */         
/*      */         try {
/*  555 */           (new KQFlowActiontBiz()).delTest(Util.getIntValue(str1, 0), str2, "RequestDeleteUtils");
/*  556 */         } catch (Exception exception) {
/*  557 */           (new BaseBean()).writeLog(exception);
/*      */         } 
/*      */         
/*  560 */         recordSet1.executeSql("select requestname,workflowid from workflow_requestbase where requestid=" + str1);
/*  561 */         recordSet1.next();
/*  562 */         String str3 = recordSet1.getString("requestname");
/*  563 */         String str4 = recordSet1.getString("workflowid");
/*      */         
/*  565 */         sysMaintenanceLog.resetParameter();
/*  566 */         sysMaintenanceLog.setRelatedId(Util.getIntValue(str1));
/*  567 */         sysMaintenanceLog.setRelatedName(str3);
/*  568 */         sysMaintenanceLog.setOperateType("3");
/*  569 */         sysMaintenanceLog.setOperateDesc("delete workflow_currentoperator where requestid=" + str1 + ";delete workflow_form where requestid=" + str1 + ";delete workflow_formdetail where requestid=" + str1 + ";delete workflow_requestLog where requestid=" + str1 + ";delete workflow_requestViewLog where id=" + str1 + ";delete workflow_requestbase where requestid=" + str1);
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */         
/*  575 */         sysMaintenanceLog.setOperateItem("85");
/*  576 */         sysMaintenanceLog.setOperateUserid(paramUser.getUID());
/*  577 */         sysMaintenanceLog.setClientAddress(paramString2);
/*  578 */         sysMaintenanceLog.setOperatesmalltype(1);
/*  579 */         sysMaintenanceLog.setSysLogInfo();
/*      */ 
/*      */ 
/*      */ 
/*      */         
/*  584 */         try { RequestDeleteLog requestDeleteLog = initRequestDeleteLog(paramUser, paramString2, str1);
/*      */           
/*  586 */           this.requestid = Util.getIntValue(str1);
/*  587 */           this.rst = recordSetTrans;
/*  588 */           this.log = requestDeleteLog;
/*  589 */           this.user = paramUser;
/*  590 */           executeDeleteRequest();
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */           
/*  598 */           list.remove(str1); } catch (Exception exception) { exception.printStackTrace(); writeLog(exception); if (exception.getMessage().indexOf("workflow interface action error") > -1) return "actionfail";  } finally { list.remove(str1); }
/*      */       
/*      */       } 
/*      */       
/*  602 */       if (str.length() > 0) {
/*  603 */         str = str.substring(0, str.length() - 1);
/*      */         
/*  605 */         String[] arrayOfString1 = Util.TokenizerString2(str, ",");
/*      */         
/*  607 */         if (bool) {
/*  608 */           recordSet1.executeSql("update cptcapital set frozennum=0 where isdata='2' and frozennum > 0");
/*  609 */           CptWfUtil cptWfUtil = new CptWfUtil();
/*  610 */           cptWfUtil.DoFrozenCpt_new();
/*      */         } 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */         
/*  625 */         recordSet1.executeSql(" select r.requestid,r.workflowid,r.currentnodetype from workflow_requestbase r,workflow_base b where requestid in ( " + str + " ) and r.workflowid=b.id and b.isbill=1");
/*  626 */         while (recordSet1.next()) {
/*  627 */           String str1 = recordSet1.getString("workflowid");
/*  628 */           String str2 = recordSet1.getString("requestid");
/*  629 */           String str3 = recordSet1.getString("currentnodetype");
/*      */ 
/*      */           
/*  632 */           if (!"0".equals(str3) && !"3".equals(str3)) {
/*  633 */             HrmAttVacationManager hrmAttVacationManager = new HrmAttVacationManager();
/*  634 */             hrmAttVacationManager.handle(Util.getIntValue(str2, 0), Util.getIntValue(str1, 0), 3);
/*      */             
/*  636 */             (new HrmPaidLeaveManager()).handle(StringUtil.parseToLong(str2), Util.getIntValue(str1, 0), 3);
/*      */           } 
/*      */         } 
/*      */ 
/*      */         
/*  641 */         recordSetTrans.commit();
/*      */         
/*  643 */         if (!"".equals(str) && bool) {
/*  644 */           (new Thread((Runnable)new CptWfUtil(str, "releaseFrozenCptnum"))).start();
/*      */         }
/*      */       }
/*      */     
/*  648 */     } catch (Exception exception) {
/*  649 */       recordSetTrans.rollback();
/*  650 */       writeLog(exception);
/*  651 */       return "fail";
/*      */     } 
/*  653 */     return "success";
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean requestDelete(User paramUser, String paramString, int paramInt) {
/*  664 */     WorkflowConfigComInfo workflowConfigComInfo = new WorkflowConfigComInfo();
/*  665 */     boolean bool = "1".equals(Util.null2String(workflowConfigComInfo.getValue("wbservice_delreq_verify_right")));
/*  666 */     if (bool && !verifyDelRight(paramInt, paramUser, false) && !verifyDelRight(paramInt, paramUser, true)) {
/*  667 */       writeLog("用户" + paramUser.getUID() + "无" + paramInt + "删除权限!");
/*  668 */       return false;
/*      */     } 
/*      */     
/*  671 */     RecordSetTrans recordSetTrans = new RecordSetTrans();
/*  672 */     recordSetTrans.setAutoCommit(false);
/*  673 */     RequestDeleteLog requestDeleteLog = initRequestDeleteLog(paramUser, paramString, String.valueOf(paramInt));
/*      */     
/*  675 */     this.log = requestDeleteLog;
/*  676 */     this.rst = recordSetTrans;
/*  677 */     this.requestid = paramInt;
/*  678 */     this.user = paramUser;
/*      */     
/*      */     try {
/*  681 */       executeDeleteRequest();
/*  682 */       recordSetTrans.commit();
/*  683 */     } catch (Exception exception) {
/*  684 */       writeLog(exception);
/*  685 */       recordSetTrans.rollback();
/*  686 */       return false;
/*      */     } 
/*  688 */     return true;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean requestDeleteForOdoc(User paramUser, String paramString, int paramInt) {
/*  699 */     RecordSetTrans recordSetTrans = new RecordSetTrans();
/*  700 */     recordSetTrans.setAutoCommit(false);
/*  701 */     RequestDeleteLog requestDeleteLog = initRequestDeleteLog(paramUser, paramString, String.valueOf(paramInt));
/*      */     
/*  703 */     this.log = requestDeleteLog;
/*  704 */     this.rst = recordSetTrans;
/*  705 */     this.requestid = paramInt;
/*  706 */     this.user = paramUser;
/*      */     
/*      */     try {
/*  709 */       executeDeleteRequest();
/*  710 */       recordSetTrans.commit();
/*  711 */     } catch (Exception exception) {
/*  712 */       writeLog(exception);
/*  713 */       recordSetTrans.rollback();
/*  714 */       return false;
/*      */     } 
/*  716 */     return true;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public RequestDeleteLog initRequestDeleteLog(User paramUser, String paramString1, String paramString2) {
/*  728 */     Calendar calendar = Calendar.getInstance();
/*  729 */     String str1 = Util.add0(calendar.get(1), 4) + "-" + Util.add0(calendar.get(2) + 1, 2) + "-" + Util.add0(calendar.get(5), 2);
/*  730 */     String str2 = Util.add0(calendar.get(11), 2) + ":" + Util.add0(calendar.get(12), 2) + ":" + Util.add0(calendar.get(13), 2);
/*      */     
/*  732 */     RecordSet recordSet = new RecordSet();
/*  733 */     RequestDeleteLog requestDeleteLog = new RequestDeleteLog();
/*  734 */     recordSet.executeQuery("select requestname,workflowid from workflow_requestbase where requestid  = ?", new Object[] { paramString2 });
/*  735 */     if (recordSet.next()) {
/*  736 */       requestDeleteLog.setRequestName(Util.fromScreen2(recordSet.getString(1), paramUser.getLanguage()));
/*  737 */       requestDeleteLog.setWorkflowId(recordSet.getString(2));
/*      */     } 
/*  739 */     requestDeleteLog.setRequestId(String.valueOf(paramString2));
/*  740 */     requestDeleteLog.setOperateUserId(String.valueOf(paramUser.getUID()));
/*  741 */     requestDeleteLog.setOperateUserType("2".equals(paramUser.getLogintype()) ? "1" : "0");
/*  742 */     requestDeleteLog.setOperateDate(str1);
/*  743 */     requestDeleteLog.setOperateTime(str2);
/*  744 */     requestDeleteLog.setClientAddress(Util.null2String(paramString1));
/*      */     
/*  746 */     return requestDeleteLog;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private void deleteBaseTableInfo() throws Exception {
/*  754 */     Iterator<String> iterator = this.deleteTableMap.keySet().iterator();
/*  755 */     while (iterator.hasNext()) {
/*  756 */       String str1 = iterator.next();
/*  757 */       RequestDeleteInfo requestDeleteInfo = this.deleteTableMap.get(str1);
/*  758 */       String str2 = Util.null2String(requestDeleteInfo.getCopytablename());
/*  759 */       if ("".equals(str2)) {
/*  760 */         deleteTableData(str1, requestDeleteInfo.getQueryWhere(), (String)null); continue;
/*      */       } 
/*  762 */       String str3 = "";
/*  763 */       if (requestDeleteInfo.getClobColumn() != null) {
/*  764 */         str3 = getTableColumn(str1, requestDeleteInfo.getClobColumn());
/*      */       } else {
/*  766 */         str3 = getTableColumn(str1, (List<String>)null);
/*      */       } 
/*      */       
/*  769 */       if ("st".equalsIgnoreCase(this.rst.getOrgindbtype()) || "postgresql".equalsIgnoreCase(this.rst.getDBType())) {
/*  770 */         DBColumnTypeUtils.syncLogTableField(str1, str2, this.rst);
/*      */       }
/*  772 */       String str4 = "insert into " + str2 + "(" + str3 + ") select " + str3 + " from " + str1 + requestDeleteInfo.getQueryWhere();
/*      */       try {
/*  774 */         this.rst.executeSql(str4);
/*  775 */       } catch (Exception exception) {
/*  776 */         boolean bool = DBColumnTypeUtils.syncLogTableField(str1, str2, this.rst);
/*  777 */         if (bool) {
/*  778 */           this.rst.executeSql(str4);
/*      */         } else {
/*  780 */           throw exception;
/*      */         } 
/*      */       } 
/*      */       
/*  784 */       updateClobColumn(str1, requestDeleteInfo);
/*  785 */       this.rst.executeSql("delete from " + str1 + requestDeleteInfo.getQueryWhere());
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private void deletePoppupRemindInfo() {
/*  796 */     PoppupRemindInfoUtil poppupRemindInfoUtil = new PoppupRemindInfoUtil();
/*  797 */     poppupRemindInfoUtil.deletePoppupRemindInfo(this.requestid, 0);
/*  798 */     poppupRemindInfoUtil.deletePoppupRemindInfo(this.requestid, 1);
/*  799 */     poppupRemindInfoUtil.deletePoppupRemindInfo(this.requestid, 10);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private void updateClobColumn(String paramString, RequestDeleteInfo paramRequestDeleteInfo) throws Exception {
/*  810 */     String str1 = paramRequestDeleteInfo.toClobColumString();
/*  811 */     RecordSet recordSet = new RecordSet();
/*  812 */     if (str1 == null)
/*  813 */       return;  String str2 = "select " + str1 + "," + paramRequestDeleteInfo.getPrimarykey() + " from " + paramString + paramRequestDeleteInfo.getQueryWhere();
/*  814 */     recordSet.executeSql(str2);
/*  815 */     while (recordSet.next()) {
/*  816 */       for (String str3 : paramRequestDeleteInfo.getClobColumn()) {
/*  817 */         String str4 = "update " + paramRequestDeleteInfo.getCopytablename() + " set " + str3 + " = ? where " + paramRequestDeleteInfo.getPrimarykey() + " = ? ";
/*  818 */         this.rst.executeUpdate(str4, new Object[] { recordSet.getString(str3), recordSet.getString(paramRequestDeleteInfo.getPrimarykey()) });
/*      */       } 
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private void deleteTableData(String paramString1, String paramString2, String paramString3) {
/*  831 */     RecordSet recordSet = new RecordSet();
/*  832 */     if ("".equals(Util.null2String(paramString3))) {
/*  833 */       paramString3 = getTableColumn(paramString1, (List<String>)null);
/*      */     }
/*  835 */     String[] arrayOfString = paramString3.split(",");
/*  836 */     String str1 = "select " + paramString3 + " from " + paramString1 + " " + paramString2;
/*  837 */     recordSet.executeSql(str1);
/*  838 */     recordSet.isReturnDecryptData(true);
/*  839 */     JSONArray jSONArray = new JSONArray();
/*  840 */     while (recordSet.next()) {
/*  841 */       JSONObject jSONObject = new JSONObject();
/*  842 */       for (byte b = 0; b < arrayOfString.length; b++) {
/*  843 */         jSONObject.put(arrayOfString[b], recordSet.getString(arrayOfString[b]));
/*      */       }
/*  845 */       jSONArray.add(jSONObject);
/*      */     } 
/*  847 */     String str2 = "delete from " + paramString1 + " " + paramString2;
/*  848 */     if ("mysql".equals(recordSet.getDBType())) {
/*  849 */       switch (paramString1) {
/*      */         case "workflow_logviewusers":
/*  851 */           str2 = "delete t1 from workflow_logviewusers t1,workflow_requestlog t2 where t1.logid=t2.logid and t2.requestid= " + this.requestid;
/*      */           break;
/*      */         case "workflow_nodeform":
/*  854 */           str2 = "delete t1 from workflow_nodeform t1,workflow_nodebase t2 where t1.nodeid=t2.id and  t2.isfreenode='1' and t2.requestid= " + this.requestid;
/*      */           break;
/*      */       } 
/*      */ 
/*      */ 
/*      */     
/*      */     }
/*  861 */     recordSet.executeUpdate(str2, new Object[0]);
/*  862 */     this.delDataInfo.put(paramString1, jSONArray);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private void deleteFormData() {
/*  869 */     RecordSet recordSet = new RecordSet();
/*  870 */     if (this.isbill == 1) {
/*  871 */       recordSet.executeQuery("select tablename,detailkeyfield from workflow_bill where id  = ?", new Object[] { Integer.valueOf(this.formid) });
/*  872 */       String str1 = "";
/*  873 */       String str2 = "";
/*  874 */       String str3 = "where requestid= " + this.requestid;
/*  875 */       if (recordSet.next()) {
/*  876 */         str1 = recordSet.getString("tablename");
/*  877 */         str2 = recordSet.getString("detailkeyfield");
/*      */       } 
/*      */       
/*  880 */       if ("".equals(str2)) {
/*  881 */         str2 = "mainid";
/*      */       }
/*  883 */       recordSet.executeQuery("select tablename from workflow_billdetailtable where billid = ?", new Object[] { Integer.valueOf(this.formid) });
/*  884 */       while (recordSet.next()) {
/*  885 */         String str = recordSet.getString(1);
/*  886 */         str3 = "where exists (select 1 from " + str1 + " b where b.id  = " + str + "." + str2 + " and b.requestid  = " + this.requestid + ")";
/*  887 */         deleteTableData(str, str3, (String)null);
/*      */       } 
/*      */       
/*  890 */       if (!"".equals(str1)) {
/*  891 */         deleteTableData(str1, "where requestid = " + this.requestid, (String)null);
/*      */       }
/*  893 */       str3 = "where requestid= " + this.requestid;
/*  894 */       String str4 = "REQUESTID,BILLFORMID,BILLID";
/*  895 */       deleteTableData("workflow_form", str3, str4);
/*      */     } else {
/*  897 */       deleteOldFormData(recordSet);
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private void deleteOldFormData(RecordSet paramRecordSet) {
/*  906 */     String str1 = "select b.fieldname from workflow_formfield a left join workflow_formdict b on a.fieldid = b.id where (a.isdetail<>'1' or a.isdetail is null) and  a.formid  = ?";
/*  907 */     paramRecordSet.executeQuery(str1, new Object[] { Integer.valueOf(this.formid) });
/*  908 */     String str2 = "";
/*  909 */     while (paramRecordSet.next()) {
/*  910 */       String str = Util.null2String(paramRecordSet.getString(1));
/*  911 */       if ("".equals(str)) {
/*      */         continue;
/*      */       }
/*  914 */       str2 = str2 + str + ",";
/*      */     } 
/*  916 */     if (str2.length() > 0) {
/*  917 */       str2 = str2 + "requestid,billformid,billid";
/*      */     }
/*  919 */     String str3 = "where requestid  = " + this.requestid;
/*  920 */     deleteTableData("workflow_form", str3, str2);
/*      */ 
/*      */     
/*  923 */     String str4 = "select b.fieldname from workflow_formfield a left join workflow_formdictdetail b on a.fieldid = b.id and a.isdetail = '1' and a.formid  = ?";
/*  924 */     paramRecordSet.executeQuery(str4, new Object[] { Integer.valueOf(this.formid) });
/*  925 */     str2 = "";
/*  926 */     while (paramRecordSet.next()) {
/*  927 */       String str = Util.null2String(paramRecordSet.getString(1));
/*  928 */       if ("".equals(str)) {
/*      */         continue;
/*      */       }
/*  931 */       str2 = str2 + str + ",";
/*      */     } 
/*  933 */     if (str2.length() > 0) {
/*  934 */       str2 = str2 + "id,requestid,groupid";
/*      */     }
/*  936 */     deleteTableData("workflow_formdetail", str3, str2);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private void updateCodeseqReserved() {
/*  946 */     if (this.requestid <= 0) {
/*      */       return;
/*      */     }
/*      */ 
/*      */     
/*  951 */     RecordSet recordSet1 = new RecordSet();
/*  952 */     RecordSet recordSet2 = new RecordSet();
/*  953 */     String str = "select codeSeqId,sequenceId from workflow_codeseqrecord where requestId = " + this.requestid;
/*  954 */     recordSet1.executeSql(str);
/*      */     
/*  956 */     while (recordSet1.next()) {
/*  957 */       String str1 = recordSet1.getString(1);
/*  958 */       String str2 = recordSet1.getString(2);
/*      */       
/*  960 */       recordSet2.executeSql("update workflow_codeseqreserved set hasUsed=0,hasDeleted=0 where codeSeqId=" + str1 + " and reservedId=" + str2);
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static String getTableColumn(String paramString, List<String> paramList) {
/*  972 */     RecordSet recordSet = new RecordSet();
/*  973 */     String str1 = "select column_name from user_tab_columns  where table_name = upper(?)";
/*  974 */     if ("sqlserver".equalsIgnoreCase(recordSet.getDBType())) {
/*  975 */       str1 = "select name from syscolumns b where id = OBJECT_ID(?)";
/*  976 */     } else if (recordSet.getDBType().equals("mysql")) {
/*  977 */       String str = DBColumnTypeUtils.getMysqlCurrentDBName();
/*  978 */       str1 = "select DISTINCT column_name from Information_schema.columns  where table_schema = database() and table_name = upper(?)";
/*  979 */       if (!"".equalsIgnoreCase(str)) str1 = str1 + " and table_schema = '" + str + "' "; 
/*  980 */     } else if (recordSet.getDBType().equals("postgresql")) {
/*  981 */       str1 = "select DISTINCT column_name from Information_schema.columns  where table_schema = 'public' and lower(table_name) = lower(?)";
/*      */     } 
/*      */     
/*  984 */     recordSet.executeQuery(str1, new Object[] { paramString });
/*  985 */     StringBuffer stringBuffer = new StringBuffer();
/*  986 */     while (recordSet.next()) {
/*  987 */       String str = recordSet.getString(1).toUpperCase();
/*      */       
/*  989 */       if (paramList != null && paramList.size() > 0 && paramList.contains(str)) {
/*      */         continue;
/*      */       }
/*  992 */       if (recordSet.getDBType().equals("mysql") && "CONDITION".equals(str)) {
/*  993 */         str = "`CONDITION`";
/*      */       }
/*  995 */       stringBuffer.append(str).append(",");
/*      */     } 
/*  997 */     String str2 = "";
/*  998 */     if (stringBuffer.length() > 0) {
/*  999 */       str2 = stringBuffer.substring(0, stringBuffer.length() - 1);
/*      */     }
/* 1001 */     stringBuffer = null;
/* 1002 */     return str2;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static boolean verifyDelRight(int paramInt, User paramUser, boolean paramBoolean) {
/* 1013 */     boolean bool = "2".equals(paramUser.getLogintype()) ? true : false;
/* 1014 */     RecordSet recordSet = new RecordSet();
/* 1015 */     if (paramBoolean) {
/* 1016 */       recordSet.executeQuery("select creater,workflowId from workflow_requestbase where requestid  = ?", new Object[] { Integer.valueOf(paramInt) });
/* 1017 */       if (recordSet.next()) {
/* 1018 */         String str1 = recordSet.getString("workflowId");
/* 1019 */         String str2 = recordSet.getString("creater");
/* 1020 */         Monitor monitor = new Monitor();
/* 1021 */         MonitorDTO monitorDTO = monitor.getMonitorInfo(String.valueOf(paramUser.getUID()), str2, str1);
/* 1022 */         return monitorDTO.getIsdelete();
/*      */       } 
/*      */     } else {
/* 1025 */       String str = ServiceUtil.calculateCurrentNodeSql(String.valueOf(paramInt), paramUser.getUID(), bool);
/* 1026 */       recordSet.executeQuery(str, new Object[0]);
/* 1027 */       if (recordSet.next()) {
/* 1028 */         int i = recordSet.getInt("nodeid");
/* 1029 */         int j = recordSet.getInt("isremark");
/* 1030 */         int k = recordSet.getInt("workflowid");
/* 1031 */         NodeInfoEntity nodeInfoEntity = WorkflowBaseBiz.getNodeInfo(i);
/* 1032 */         if (nodeInfoEntity == null) return false; 
/* 1033 */         WfFunctionManageUtil wfFunctionManageUtil = new WfFunctionManageUtil();
/* 1034 */         return (nodeInfoEntity.getNodetype() == 0 && j == 0 && wfFunctionManageUtil.IsShowDelButtonByReject(paramInt, k));
/*      */       } 
/*      */     } 
/* 1037 */     return false;
/*      */   }
/*      */   
/*      */   public void setRequestid(int paramInt) {
/* 1041 */     this.requestid = paramInt;
/*      */   }
/*      */   private class RequestDeleteInfo { private String queryWhere;
/*      */     private String copytablename;
/*      */     private String primarykey;
/*      */     private List<String> clobColumn;
/*      */     
/*      */     private RequestDeleteInfo() {}
/*      */     
/*      */     public String getQueryWhere() {
/* 1051 */       return this.queryWhere;
/*      */     }
/*      */     public void setQueryWhere(String param1String) {
/* 1054 */       this.queryWhere = param1String;
/*      */     }
/*      */     public String getCopytablename() {
/* 1057 */       return this.copytablename;
/*      */     }
/*      */     public void setCopytablename(String param1String) {
/* 1060 */       this.copytablename = param1String;
/*      */     }
/*      */     public String getPrimarykey() {
/* 1063 */       return this.primarykey;
/*      */     }
/*      */     public void setPrimarykey(String param1String) {
/* 1066 */       this.primarykey = param1String;
/*      */     }
/*      */     public List<String> getClobColumn() {
/* 1069 */       return this.clobColumn;
/*      */     }
/*      */     public void setClobColumn(List<String> param1List) {
/* 1072 */       this.clobColumn = param1List;
/*      */     }
/*      */     
/*      */     public String toClobColumString() {
/* 1076 */       if (this.clobColumn == null) return null; 
/* 1077 */       String str = "";
/* 1078 */       for (String str1 : this.clobColumn) {
/* 1079 */         str = str + str1 + ",";
/*      */       }
/* 1081 */       return (str.length() > 0) ? str.substring(0, str.length() - 1) : null;
/*      */     } }
/*      */ 
/*      */ 
/*      */   
/*      */   public User getUser() {
/* 1087 */     return this.user;
/*      */   }
/*      */   
/*      */   public void setUser(User paramUser) {
/* 1091 */     this.user = paramUser;
/*      */   }
/*      */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/RequestDeleteUtils.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */