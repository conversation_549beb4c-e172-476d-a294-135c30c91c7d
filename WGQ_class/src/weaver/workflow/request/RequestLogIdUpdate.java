/*    */ package weaver.workflow.request;
/*    */ 
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.BaseBean;
/*    */ import weaver.general.Util;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class RequestLogIdUpdate
/*    */   extends BaseBean
/*    */ {
/* 28 */   private int requestLogId = -1;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public synchronized int getRequestLogNewId() {
/* 40 */     RecordSet recordSet = new RecordSet();
/*    */     try {
/* 42 */       recordSet.executeProc("workflow_RequestLogID_Update", "");
/* 43 */       if (recordSet.next()) {
/* 44 */         this.requestLogId = Util.getIntValue(recordSet.getString(1), 0);
/*    */       }
/*    */     }
/* 47 */     catch (Exception exception) {
/* 48 */       writeLog(exception.toString() + "保存请求日志时无法生成新的ID!!!");
/* 49 */       this.requestLogId = -1;
/*    */     } 
/* 51 */     return this.requestLogId;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/RequestLogIdUpdate.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */