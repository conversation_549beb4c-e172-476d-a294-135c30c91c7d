/*      */ package weaver.workflow.request.todo;
/*      */ 
/*      */ import com.alibaba.fastjson.JSONObject;
/*      */ import com.cloudstore.dev.api.bean.MessageBean;
/*      */ import com.cloudstore.dev.api.bean.MessageType;
/*      */ import com.google.common.base.Strings;
/*      */ import java.lang.reflect.Field;
/*      */ import java.lang.reflect.InvocationTargetException;
/*      */ import java.lang.reflect.Method;
/*      */ import java.util.ArrayList;
/*      */ import java.util.HashMap;
/*      */ import java.util.Iterator;
/*      */ import java.util.List;
/*      */ import java.util.Map;
/*      */ import java.util.Set;
/*      */ import java.util.concurrent.ExecutorService;
/*      */ import java.util.concurrent.Executors;
/*      */ import org.apache.commons.lang.StringUtils;
/*      */ import weaver.conn.RecordSet;
/*      */ import weaver.general.BaseBean;
/*      */ import weaver.general.TimeUtil;
/*      */ import weaver.general.Util;
/*      */ import weaver.general.WorkFlowTransMethod;
/*      */ import weaver.hrm.User;
/*      */ import weaver.hrm.company.DepartmentComInfo;
/*      */ import weaver.hrm.company.SubCompanyComInfo;
/*      */ import weaver.hrm.resource.ResourceComInfo;
/*      */ import weaver.hrm.roles.RolesComInfo;
/*      */ import weaver.systeminfo.SystemEnv;
/*      */ import weaver.workflow.logging.Logger;
/*      */ import weaver.workflow.logging.LoggerFactory;
/*      */ import weaver.workflow.workflow.WorkflowComInfo;
/*      */ 
/*      */ 
/*      */ public class RequestUtil
/*      */   extends BaseBean
/*      */ {
/*      */   private boolean isdebug = true;
/*   39 */   private String sendUserids = "";
/*   40 */   private String operatedatetime = "";
/*   41 */   private static final Logger log = LoggerFactory.getLogger(RequestUtil.class);
/*      */ 
/*      */   
/*      */   public String getSendUserids() {
/*   45 */     return this.sendUserids;
/*      */   }
/*      */   
/*      */   public void setSendUserids(String paramString) {
/*   49 */     this.sendUserids = paramString;
/*      */   }
/*      */   
/*      */   public String getOperatedatetime() {
/*   53 */     return this.sendUserids;
/*      */   }
/*      */   
/*      */   public void setOperatedatetime(String paramString) {
/*   57 */     this.operatedatetime = paramString;
/*      */   }
/*      */   
/*      */   public boolean isIsdebug() {
/*   61 */     return this.isdebug;
/*      */   }
/*      */   
/*      */   public void setIsdebug(boolean paramBoolean) {
/*   65 */     this.isdebug = paramBoolean;
/*      */   }
/*      */   
/*   68 */   ResourceComInfo rci = null;
/*   69 */   DepartmentComInfo dci = null;
/*   70 */   SubCompanyComInfo sci = null;
/*   71 */   RolesComInfo rolesComInfo = null;
/*      */   public RequestUtil() {
/*      */     try {
/*   74 */       this.rci = new ResourceComInfo();
/*   75 */       this.dci = new DepartmentComInfo();
/*   76 */       this.sci = new SubCompanyComInfo();
/*   77 */       this.rolesComInfo = new RolesComInfo();
/*   78 */     } catch (Exception exception) {}
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   public static final int WF_OP_TJ = 1;
/*      */ 
/*      */   
/*      */   public static boolean isOpenOtherSystemToDo() {
/*   87 */     RecordSet recordSet = new RecordSet();
/*   88 */     recordSet.executeSql("select isuse from ofs_setting where isuse=1");
/*   89 */     if (recordSet.getCounts() > 0) {
/*   90 */       return true;
/*      */     }
/*   92 */     return false;
/*      */   }
/*      */   
/*      */   public OfsSettingObject getOfsSetting() {
/*   96 */     RecordSet recordSet = new RecordSet();
/*   97 */     OfsSettingObject ofsSettingObject = new OfsSettingObject();
/*   98 */     recordSet.executeSql("select * from ofs_setting where isuse=1");
/*   99 */     if (recordSet.next()) {
/*  100 */       ofsSettingObject.setIsuse(1);
/*  101 */       ofsSettingObject.setOafullname(Util.null2String(recordSet.getString("oafullname")));
/*  102 */       ofsSettingObject.setOashortname(Util.null2String(recordSet.getString("oashortname")));
/*  103 */       ofsSettingObject.setShowdone(recordSet.getString("showdone"));
/*  104 */       ofsSettingObject.setShowsysname(Util.null2String(recordSet.getString("showsysname")));
/*      */     } 
/*  106 */     return ofsSettingObject;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getSysname(String paramString1, String paramString2) {
/*  117 */     return getSysname(Util.getIntValue(paramString1), paramString2);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getSysname(int paramInt, String paramString) {
/*  126 */     String str = "";
/*  127 */     if (paramInt < 0) {
/*  128 */       RecordSet recordSet = new RecordSet();
/*  129 */       recordSet.executeSql("select sysshortname,sysfullname from ofs_sysinfo where sysid=" + paramInt);
/*  130 */       if (recordSet.next()) {
/*  131 */         if (paramString.equals("1")) {
/*  132 */           str = recordSet.getString(1);
/*  133 */         } else if (paramString.equals("2")) {
/*  134 */           str = recordSet.getString(2);
/*      */         } else {
/*  136 */           str = "";
/*      */         } 
/*      */       }
/*      */     } else {
/*  140 */       OfsSettingObject ofsSettingObject = getOfsSetting();
/*  141 */       if (paramString.equals("1")) {
/*  142 */         str = ofsSettingObject.getOashortname();
/*  143 */       } else if (paramString.equals("2")) {
/*  144 */         str = ofsSettingObject.getOafullname();
/*      */       } else {
/*  146 */         str = "";
/*      */       } 
/*      */     } 
/*  149 */     if (paramString.equals("0")) {
/*  150 */       str = "";
/*      */     }
/*  152 */     return str;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public ArrayList<OtherSystemInfoObject> getOtherSystemInfosWithOutWF() {
/*  159 */     return getOtherSystemInfosWithWF(false);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public ArrayList<OtherSystemInfoObject> getOtherSystemInfosWithWF() {
/*  167 */     return getOtherSystemInfosWithWF(true);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public ArrayList<OtherSystemInfoObject> getOtherSystemInfosWithWF(boolean paramBoolean) {
/*  176 */     ArrayList<OtherSystemInfoObject> arrayList = new ArrayList();
/*  177 */     RecordSet recordSet1 = new RecordSet();
/*  178 */     RecordSet recordSet2 = new RecordSet();
/*  179 */     recordSet1.executeSql("select * from ofs_sysinfo where cancel=0");
/*  180 */     while (recordSet1.next()) {
/*  181 */       int i = recordSet1.getInt("sysid");
/*  182 */       String str1 = recordSet1.getString("syscode");
/*  183 */       String str2 = recordSet1.getString("sysshortname");
/*  184 */       String str3 = recordSet1.getString("sysfullname");
/*  185 */       String str4 = recordSet1.getString("pcprefixurl");
/*  186 */       String str5 = recordSet1.getString("appprefixurl");
/*  187 */       OtherSystemInfoObject otherSystemInfoObject = new OtherSystemInfoObject();
/*  188 */       otherSystemInfoObject.setAppprefixurl(str5);
/*  189 */       otherSystemInfoObject.setPcprefixurl(str4);
/*  190 */       otherSystemInfoObject.setSyscode(str1);
/*  191 */       otherSystemInfoObject.setSysfullname(str3);
/*  192 */       otherSystemInfoObject.setSysid(i);
/*  193 */       otherSystemInfoObject.setSysshortname(str2);
/*      */       
/*  195 */       if (paramBoolean) {
/*  196 */         ArrayList<OtherSystemWorkflowObject> arrayList1 = new ArrayList();
/*  197 */         recordSet2.executeSql("select workflowid,workflowname from ofs_workflow where sysid=" + i + " and cancel=0");
/*  198 */         while (recordSet2.next()) {
/*  199 */           OtherSystemWorkflowObject otherSystemWorkflowObject = new OtherSystemWorkflowObject();
/*  200 */           otherSystemWorkflowObject.setSysid(i);
/*  201 */           otherSystemWorkflowObject.setWorkflowid(recordSet1.getInt("workflowid"));
/*  202 */           otherSystemWorkflowObject.setWorkflowname(Util.null2String(recordSet1.getString("workflowname")));
/*  203 */           arrayList1.add(otherSystemWorkflowObject);
/*      */         } 
/*  205 */         otherSystemInfoObject.setOtherSystemWorkflowObjectList(arrayList1);
/*      */       } 
/*      */       
/*  208 */       arrayList.add(otherSystemInfoObject);
/*      */     } 
/*  210 */     return arrayList;
/*      */   }
/*      */   
/*      */   public String getWorkflowName(String paramString1, String paramString2) {
/*  214 */     String str1 = "";
/*  215 */     RequestUtil requestUtil = new RequestUtil();
/*  216 */     OfsSettingObject ofsSettingObject = requestUtil.getOfsSetting();
/*  217 */     boolean bool = (ofsSettingObject.getIsuse() == 1) ? true : false;
/*  218 */     String str2 = ofsSettingObject.getShowsysname();
/*      */     try {
/*  220 */       if (Util.getIntValue(paramString1) > 0) {
/*  221 */         WorkflowComInfo workflowComInfo = new WorkflowComInfo();
/*  222 */         str1 = workflowComInfo.getWorkflowname(paramString1);
/*  223 */         if (bool && paramString2.startsWith("S+")) {
/*  224 */           if ("1".equals(str2)) {
/*  225 */             str1 = "【" + ofsSettingObject.getOashortname() + "】-" + str1;
/*      */           }
/*  227 */           else if ("2".equals(str2)) {
/*  228 */             str1 = "【" + ofsSettingObject.getOafullname() + "】-" + str1;
/*      */           } 
/*      */         }
/*      */       } else {
/*      */         
/*  233 */         RecordSet recordSet = new RecordSet();
/*  234 */         recordSet.executeSql("select workflowname from ofs_workflow where sysid=" + (paramString2.startsWith("S+") ? paramString2.substring(2) : paramString2) + " and workflowid=" + paramString1);
/*  235 */         if (recordSet.next()) {
/*  236 */           str1 = recordSet.getString("workflowname");
/*      */         }
/*  238 */         if (bool && paramString2.startsWith("S+") && (
/*  239 */           "1".equals(str2) || "2".equals(str2))) {
/*  240 */           str1 = "【" + requestUtil.getSysname(paramString2.substring(2), str2) + "】-" + str1;
/*      */         }
/*      */       }
/*      */     
/*  244 */     } catch (Exception exception) {
/*  245 */       writeLog("getWorkflowName : " + exception.getMessage());
/*      */     } 
/*  247 */     return str1;
/*      */   }
/*      */   
/*      */   public String getWorkflowName(String paramString) {
/*  251 */     return "";
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getCurrentNode(String paramString1, String paramString2) {
/*  261 */     String str1 = "";
/*  262 */     String[] arrayOfString = Util.TokenizerString2(paramString2, "+");
/*  263 */     int i = Util.getIntValue(arrayOfString[0], 0);
/*  264 */     String str2 = Util.null2String(arrayOfString[1]);
/*  265 */     String str3 = Util.null2String(arrayOfString[2]);
/*      */     
/*  267 */     if (i > 0) {
/*  268 */       return (new WorkFlowTransMethod()).getCurrentNode(paramString1);
/*      */     }
/*  270 */     RecordSet recordSet = new RecordSet();
/*  271 */     if (recordSet.getDBType().equals("oracle")) {
/*  272 */       recordSet.executeSql("select nodename from  (select nodename,operatedate, operatetime from ofs_todo_data where requestid = " + i + " union all select nodename,operatedate, operatetime from ofs_done_data where requestid = " + i + ") t1 where rownum = 1 order by operatedate desc, operatetime desc");
/*  273 */     } else if (recordSet.getDBType().equals("mysql")) {
/*  274 */       recordSet.executeSql("select nodename from  (select nodename,operatedate, operatetime from ofs_todo_data where requestid = " + i + " union all select nodename,operatedate, operatetime from ofs_done_data where requestid = " + i + ") t1 order by operatedate desc, operatetime desc limit 1");
/*  275 */     } else if (recordSet.getDBType().equals("postgresql")) {
/*  276 */       recordSet.executeSql("select nodename from  (select nodename,operatedate, operatetime from ofs_todo_data where requestid = " + i + " union all select nodename,operatedate, operatetime from ofs_done_data where requestid = " + i + ") t1 order by operatedate desc, operatetime desc limit 1");
/*      */     } else {
/*  278 */       recordSet.executeSql("select top 1 nodename from  (select nodename,operatedate, operatetime from ofs_todo_data where requestid = " + i + " union all select nodename,operatedate, operatetime from ofs_done_data where requestid = " + i + ") t1 order by operatedate desc, operatetime desc");
/*      */     } 
/*  280 */     if (recordSet.next()) {
/*  281 */       str1 = Util.null2String(recordSet.getString(1));
/*      */     }
/*      */     
/*  284 */     return str1;
/*      */   }
/*      */   
/*      */   public String getShowUrl(String paramString1, String paramString2, String paramString3) {
/*  288 */     RecordSet recordSet = new RecordSet();
/*  289 */     String str1 = "";
/*  290 */     String str2 = "";
/*  291 */     recordSet.executeSql("select sysid,workflowid from ofs_todo_data where requestid=" + paramString1);
/*  292 */     if (recordSet.next()) {
/*  293 */       str1 = recordSet.getString("sysid");
/*  294 */       str2 = recordSet.getString("workflowid");
/*      */     } 
/*  296 */     return getShowUrl(str1, str2, paramString1, paramString2, paramString3);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getShowUrl(String paramString1, String paramString2, String paramString3, String paramString4, String paramString5) {
/*  309 */     String str1 = "";
/*  310 */     RecordSet recordSet = new RecordSet();
/*  311 */     String str2 = "";
/*  312 */     String str3 = "";
/*  313 */     if ("".equals(paramString5)) {
/*  314 */       paramString5 = "0";
/*      */     }
/*      */     
/*  317 */     String str4 = "0";
/*      */     
/*  319 */     String str5 = "select pcprefixurl,pcurl,appprefixurl,appurl,appentranceurl from ofs_sysinfo s,ofs_todo_data d where d.syscode=s.syscode and d.sysid=s.sysid and d.requestid=" + paramString3 + " and d.workflowid=" + paramString2 + " and d.sysid=" + paramString1 + " and d.userid=" + paramString4 + " and d.islasttimes=1";
/*  320 */     if (paramString1.equals("")) {
/*  321 */       str5 = "select pcprefixurl,pcurl,appprefixurl,appurl,appentranceurl from ofs_sysinfo s,ofs_todo_data d where d.syscode=s.syscode and d.sysid=s.sysid and d.requestid=" + paramString3 + " and d.workflowid=" + paramString2 + " and  d.userid=" + paramString4 + " and d.islasttimes=1";
/*      */     }
/*      */     
/*  324 */     recordSet.executeSql(str5);
/*  325 */     if (recordSet.next()) {
/*  326 */       if (paramString5.equals("0")) {
/*  327 */         str2 = Util.null2String(recordSet.getString(1));
/*  328 */         str3 = Util.null2String(recordSet.getString(2));
/*      */       } else {
/*  330 */         str2 = Util.null2String(recordSet.getString(3));
/*  331 */         str3 = Util.null2String(recordSet.getString(4));
/*      */         
/*  333 */         RecordSet recordSet1 = new RecordSet();
/*  334 */         recordSet1.executeQuery("select * from ofs_sysinfo where sysid = ?", new Object[] { paramString1 });
/*  335 */         recordSet1.next();
/*  336 */         String str = recordSet1.getString("appentranceurl");
/*  337 */         if (str == null || "".equals(str)) {
/*  338 */           str2 = Util.null2String(recordSet.getString(3));
/*  339 */           str3 = Util.null2String(recordSet.getString(4));
/*      */         } else {
/*      */           
/*  342 */           str2 = "";
/*  343 */           str3 = Util.null2String(recordSet.getString(4)) + "&isremark=" + str4;
/*      */         } 
/*      */       } 
/*      */     } else {
/*  347 */       String str = "select pcprefixurl,pcurl,appprefixurl,appurl,appentranceurl from ofs_sysinfo s,ofs_done_data d where d.syscode=s.syscode and d.sysid=s.sysid and d.requestid=" + paramString3 + " and d.workflowid=" + paramString2 + " and d.sysid=" + paramString1 + " and d.userid=" + paramString4 + " and d.islasttimes=1";
/*  348 */       if (paramString1.equals("")) {
/*  349 */         str = "select pcprefixurl,pcurl,appprefixurl,appurl,appentranceurl from ofs_sysinfo s,ofs_done_data d where d.syscode=s.syscode and d.sysid=s.sysid and d.requestid=" + paramString3 + " and d.workflowid=" + paramString2 + " and  d.userid=" + paramString4 + " and d.islasttimes=1";
/*      */       }
/*  351 */       str4 = "2";
/*  352 */       recordSet.executeSql(str5);
/*  353 */       if (recordSet.next()) {
/*  354 */         if (paramString5.equals("0")) {
/*  355 */           str2 = Util.null2String(recordSet.getString(1));
/*  356 */           str3 = Util.null2String(recordSet.getString(2));
/*      */         } else {
/*  358 */           str2 = Util.null2String(recordSet.getString(3));
/*  359 */           str3 = Util.null2String(recordSet.getString(4));
/*      */           
/*  361 */           RecordSet recordSet1 = new RecordSet();
/*  362 */           recordSet1.executeQuery("select * from ofs_sysinfo where sysid = ?", new Object[] { paramString1 });
/*  363 */           recordSet1.next();
/*  364 */           String str6 = recordSet1.getString("appentranceurl");
/*  365 */           if (str6 == null || "".equals(str6)) {
/*  366 */             str2 = Util.null2String(recordSet.getString(3));
/*  367 */             str3 = Util.null2String(recordSet.getString(4));
/*      */           } else {
/*      */             
/*  370 */             str2 = "";
/*  371 */             str3 = Util.null2String(recordSet.getString(4)) + "&isremark=" + str4;
/*      */           } 
/*      */         } 
/*      */       }
/*      */     } 
/*  376 */     str1 = str2 + str3;
/*  377 */     if (str3.startsWith("http") || str3.startsWith("https")) {
/*  378 */       str1 = str3;
/*      */     }
/*  380 */     return str1;
/*      */   }
/*      */   
/*      */   public String getViewType(String paramString1, String paramString2) {
/*  384 */     String str = "0";
/*  385 */     RecordSet recordSet = new RecordSet();
/*  386 */     recordSet.executeSql("select viewtype from ofs_todo_data where requestid=" + paramString1 + " and userid=" + paramString2 + " and islasttimes=1");
/*  387 */     if (recordSet.next() && 
/*  388 */       Util.null2String(recordSet.getString("viewtype")).equals("0")) {
/*  389 */       str = "1";
/*      */     }
/*      */     
/*  392 */     return str;
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   public void SendRequestData(String paramString1, String paramString2) {
/*  398 */     this.sendUserids = paramString2;
/*  399 */     SendRequestData(paramString1, 1, (ArrayList<RequestStatusObj>)null);
/*      */   }
/*      */ 
/*      */   
/*      */   public void SendRequestData(String paramString) {
/*  404 */     SendRequestData(paramString, 1, (ArrayList<RequestStatusObj>)null);
/*      */   }
/*      */   public void SendRequestData2(String paramString) {
/*  407 */     sendRequest(paramString);
/*      */   }
/*      */   
/*  410 */   private static ExecutorService executorService = Executors.newFixedThreadPool(3);
/*      */   public void DoSendRequestData(final ArrayList<DataObj> datas) {
/*  412 */     executorService.submit(new Runnable() {
/*      */           public void run() {
/*  414 */             RecordSet recordSet1 = new RecordSet();
/*  415 */             RecordSet recordSet2 = new RecordSet();
/*  416 */             OfsSendInfoComInfo ofsSendInfoComInfo = new OfsSendInfoComInfo();
/*  417 */             recordSet1.executeQuery("select * from ofs_sendinfo where isvalid=1", new Object[0]);
/*  418 */             while (recordSet1.next()) {
/*  419 */               String str1 = recordSet1.getString("id");
/*  420 */               String str2 = Util.null2String(recordSet1.getString("classimpl"));
/*  421 */               String str3 = Util.null2String(recordSet1.getString("syscode"));
/*  422 */               String str4 = Util.null2String(recordSet1.getString("serverurl"));
/*      */               try {
/*  424 */                 Class<?> clazz = Class.forName(str2);
/*  425 */                 Object object = clazz.newInstance();
/*  426 */                 Field field1 = clazz.getDeclaredField("id");
/*  427 */                 field1.setAccessible(true);
/*  428 */                 field1.set(object, str1);
/*      */                 
/*  430 */                 Field field2 = clazz.getDeclaredField("syscode");
/*  431 */                 field2.setAccessible(true);
/*  432 */                 field2.set(object, str3);
/*  433 */                 Field field3 = clazz.getDeclaredField("serverurl");
/*  434 */                 field3.setAccessible(true);
/*  435 */                 field3.set(object, str4);
/*      */ 
/*      */                 
/*  438 */                 List<ArrayList<String>> list1 = ofsSendInfoComInfo.getCValFields(str1, "wf");
/*  439 */                 if (list1.size() > 0) {
/*      */                   try {
/*  441 */                     Field field = clazz.getDeclaredField("workflowwhitelist");
/*  442 */                     field.setAccessible(true);
/*  443 */                     field.set(object, list1.get(0));
/*  444 */                     RequestUtil.this.writeLog("syscode ：： " + str3 + " 流程白名单：：" + StringUtils.join(list1.get(0), ","));
/*  445 */                   } catch (Exception exception) {
/*  446 */                     RequestUtil.this.writeLog("ofsSendInfoComInfo.getCValFields Workflow " + exception.getMessage());
/*      */                   } 
/*      */                 }
/*  449 */                 List<ArrayList<String>> list2 = ofsSendInfoComInfo.getCValFields(str1, "rs");
/*  450 */                 if (list2.size() > 0) {
/*      */                   try {
/*  452 */                     Field field = clazz.getDeclaredField("userwhitelist");
/*  453 */                     field.setAccessible(true);
/*  454 */                     field.set(object, list2.get(0));
/*  455 */                     RequestUtil.this.writeLog("syscode ：： " + str3 + " 人员白名单：：" + StringUtils.join(list2.get(0), ","));
/*  456 */                   } catch (Exception exception) {
/*  457 */                     RequestUtil.this.writeLog("ofsSendInfoComInfo.getCValFields Resource " + exception.getMessage());
/*      */                   } 
/*      */                 }
/*  460 */                 recordSet2.executeQuery("select paramname,paramvalue from ofs_sendinfodetail where mainid=?", new Object[] { str1 });
/*  461 */                 while (recordSet2.next()) {
/*  462 */                   String str5 = Util.null2String(recordSet2.getString("paramname"));
/*  463 */                   String str6 = recordSet2.getString("paramvalue");
/*  464 */                   if (!str5.equals("")) {
/*  465 */                     Field field = clazz.getDeclaredField(str5);
/*  466 */                     field.setAccessible(true);
/*  467 */                     field.set(object, str6);
/*      */                   } 
/*      */                 } 
/*  470 */                 Method method = clazz.getMethod("SendRequestStatusData", new Class[] { ArrayList.class });
/*  471 */                 method.invoke(object, new Object[] { this.val$datas });
/*  472 */               } catch (ClassNotFoundException classNotFoundException) {
/*  473 */                 RequestUtil.this.writeLog("[" + str2 + "] ClassNotFoundException :" + classNotFoundException.getMessage());
/*  474 */               } catch (NoSuchFieldException noSuchFieldException) {
/*  475 */                 RequestUtil.this.writeLog("[" + str2 + "] NoSuchFieldException :" + noSuchFieldException.getMessage());
/*  476 */               } catch (InstantiationException instantiationException) {
/*  477 */                 RequestUtil.this.writeLog("[" + str2 + "] InstantiationException :" + instantiationException.getMessage());
/*  478 */               } catch (IllegalAccessException illegalAccessException) {
/*  479 */                 RequestUtil.this.writeLog("[" + str2 + "] IllegalAccessException ", illegalAccessException);
/*  480 */               } catch (InvocationTargetException invocationTargetException) {
/*  481 */                 RequestUtil.this.writeLog("[" + str2 + "] InvocationTargetException ", invocationTargetException);
/*  482 */               } catch (NoSuchMethodException noSuchMethodException) {
/*  483 */                 RequestUtil.this.writeLog("[" + str2 + "] NoSuchMethodException :" + noSuchMethodException.getMessage());
/*  484 */               } catch (Exception exception) {
/*  485 */                 RequestUtil.this.writeLog("统一待办推送----[" + str2 + "] Exception :" + exception.getMessage());
/*  486 */                 exception.printStackTrace();
/*      */               } 
/*      */             } 
/*      */           }
/*      */         });
/*      */   }
/*      */ 
/*      */   
/*      */   public void SendDeleteRequestData(String paramString) {
/*  495 */     WorkflowComInfo workflowComInfo = null;
/*      */     try {
/*  497 */       workflowComInfo = new WorkflowComInfo();
/*  498 */     } catch (Exception exception) {
/*  499 */       exception.printStackTrace();
/*      */     } 
/*  501 */     long l = System.currentTimeMillis();
/*  502 */     ArrayList<RequestStatusObj> arrayList = new ArrayList();
/*  503 */     RecordSet recordSet1 = new RecordSet();
/*  504 */     RecordSet recordSet2 = new RecordSet();
/*  505 */     ArrayList<DataObj> arrayList1 = new ArrayList();
/*  506 */     String[] arrayOfString = Util.TokenizerString2(paramString, ",");
/*  507 */     for (byte b = 0; b < arrayOfString.length; b++) {
/*  508 */       String str1 = arrayOfString[b];
/*  509 */       DataObj dataObj = new DataObj();
/*  510 */       String str2 = "select r.requestid,r.requestname,r.requestnamenew,r.workflowid,r.creater,r.createdate,r.createtime,r.requestlevel,r.status,c.id,c.nodeid,(select nodename from workflow_nodebase where id=r.currentnodeid) as nodename,c.userid,c.receivedate,c.receivetime,c.operatedate,c.operatetime,c.isremark,c.preisremark,c.islasttimes,c.takisremark,c.viewtype,c.iscomplete from workflow_requestbase r,workflow_currentoperator c where c.requestid=r.requestid  and r.requestid=" + str1;
/*      */ 
/*      */ 
/*      */       
/*  514 */       recordSet1.executeQuery(str2, new Object[0]);
/*  515 */       if (this.isdebug) {
/*  516 */         writeLog("count : " + recordSet1.getCounts() + "  sql : " + str2);
/*      */       }
/*  518 */       while (recordSet1.next()) {
/*  519 */         String str3 = recordSet1.getString("userid");
/*  520 */         String str4 = recordSet1.getString("workflowid");
/*  521 */         String str5 = recordSet1.getString("isremark");
/*  522 */         String str6 = recordSet1.getString("takisremark");
/*  523 */         RequestStatusObj requestStatusObj = new RequestStatusObj();
/*  524 */         requestStatusObj.setCid(recordSet1.getInt("id"));
/*  525 */         requestStatusObj.setCreatedate(recordSet1.getString("createdate"));
/*  526 */         requestStatusObj.setCreatetime(recordSet1.getString("createtime"));
/*  527 */         requestStatusObj.setCreator(new User(Util.getIntValue(recordSet1.getString("creater"))));
/*  528 */         requestStatusObj.setCreatorid(Util.getIntValue(recordSet1.getString("creater")));
/*  529 */         requestStatusObj.setIscomplete(recordSet1.getString("iscomplete"));
/*  530 */         requestStatusObj.setIsremark(str5);
/*  531 */         requestStatusObj.setNodeid(Util.getIntValue(recordSet1.getString("nodeid")));
/*  532 */         requestStatusObj.setNodename(recordSet1.getString("nodename"));
/*  533 */         requestStatusObj.setOperatedate(recordSet1.getString("operatedate"));
/*  534 */         requestStatusObj.setOperatetime(recordSet1.getString("operatetime"));
/*  535 */         requestStatusObj.setReceivedate(recordSet1.getString("receivedate"));
/*  536 */         requestStatusObj.setReceivetime(recordSet1.getString("receivetime"));
/*  537 */         requestStatusObj.setRequestid(Util.getIntValue(str1));
/*  538 */         requestStatusObj.setRequestnamenew(recordSet1.getString("requestnamenew"));
/*  539 */         requestStatusObj.setRequeststatus(recordSet1.getString("status"));
/*  540 */         requestStatusObj.setRequstname(recordSet1.getString("requestname"));
/*  541 */         requestStatusObj.setUser(new User(Util.getIntValue(recordSet1.getString("userid"))));
/*  542 */         requestStatusObj.setViewtype(recordSet1.getString("viewtype"));
/*  543 */         requestStatusObj.setWorkflowid(recordSet1.getInt("workflowid"));
/*  544 */         requestStatusObj.setWorkflowname(workflowComInfo.getWorkflowname(str4));
/*  545 */         requestStatusObj.setSendTime(l + "");
/*      */ 
/*      */         
/*  548 */         arrayList.add(requestStatusObj);
/*      */       } 
/*      */       
/*  551 */       dataObj.setRequestid(str1);
/*  552 */       dataObj.setDeldatas(arrayList);
/*  553 */       dataObj.setSendtimestamp(l + "");
/*  554 */       arrayList1.add(dataObj);
/*      */     } 
/*      */     
/*  557 */     DoSendRequestData(arrayList1);
/*      */   }
/*      */ 
/*      */   
/*      */   public void SendRequestData(String paramString, int paramInt, ArrayList<RequestStatusObj> paramArrayList) {
/*  562 */     long l = System.currentTimeMillis();
/*  563 */     ResourceComInfo resourceComInfo = null;
/*  564 */     WorkflowComInfo workflowComInfo = null;
/*      */     try {
/*  566 */       resourceComInfo = new ResourceComInfo();
/*  567 */       workflowComInfo = new WorkflowComInfo();
/*  568 */     } catch (Exception exception) {
/*  569 */       exception.printStackTrace();
/*      */     } 
/*      */     
/*  572 */     String str1 = "";
/*  573 */     ArrayList<RequestStatusObj> arrayList1 = new ArrayList();
/*  574 */     ArrayList<RequestStatusObj> arrayList2 = new ArrayList();
/*  575 */     if (paramArrayList == null) {
/*  576 */       paramArrayList = new ArrayList<>();
/*      */     }
/*      */     
/*  579 */     RecordSet recordSet1 = new RecordSet();
/*  580 */     RecordSet recordSet2 = new RecordSet();
/*  581 */     recordSet1.executeProc("GetDBDateAndTime", "");
/*  582 */     String str2 = "";
/*  583 */     String str3 = "";
/*  584 */     if (recordSet1.next()) {
/*  585 */       str2 = recordSet1.getString("dbdate");
/*  586 */       str3 = recordSet1.getString("dbtime");
/*  587 */       str1 = TimeUtil.timeAdd(str2 + " " + str3, -15);
/*      */     } 
/*      */ 
/*      */ 
/*      */     
/*  592 */     String str4 = "select r.requestid,r.requestname,r.requestnamenew,r.workflowid,r.creater,r.createdate,r.createtime,r.requestlevel,r.status,c.id,c.nodeid,(select nodename from workflow_nodebase where id=r.currentnodeid) as nodename,c.userid,c.receivedate,c.receivetime,c.operatedate,c.operatetime,c.isremark,c.preisremark,c.islasttimes,c.takisremark,c.viewtype,c.iscomplete,c.groupid,opdatetime from workflow_requestbase r,workflow_currentoperator c where c.requestid=r.requestid and r.workflowid=c.workflowid and r.workflowid in (select id from workflow_base where (isvalid='1' or isvalid='3')) and r.requestid=" + paramString;
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  597 */     if (this.isdebug) {
/*  598 */       recordSet1.writeLog("operatedatetime = " + this.operatedatetime + " sendUserids = " + this.sendUserids);
/*      */     }
/*  600 */     if (!"".equals(Util.null2String(this.operatedatetime))) {
/*  601 */       if (recordSet1.getDBType().equalsIgnoreCase("oracle")) {
/*  602 */         str4 = str4 + " and ((c.operatedate||' '||c.operatetime>='" + str1 + "' or (isremark in (2,4) and (c.operatedate is null or c.opdatetime>='" + this.operatedatetime + "')))";
/*  603 */         str4 = str4 + " or (isremark in  ('0','1','5','7','8','9','6','11') and c.islasttimes=1  and c.receivedate||' '||c.receivetime>='" + this.operatedatetime + "'))";
/*      */       } else {
/*  605 */         str4 = str4 + " and ((c.operatedate+' '+c.operatetime>='" + str1 + "'  or (isremark in (2,4) and (c.operatedate is null or c.opdatetime>='" + this.operatedatetime + "')))";
/*  606 */         str4 = str4 + " or (isremark in  ('0','1','5','7','8','9','6','11') and c.islasttimes=1  and c.receivedate+' '+c.receivetime>='" + this.operatedatetime + "'))";
/*      */       } 
/*      */     } else {
/*  609 */       if (!"".equals(Util.null2String(this.sendUserids))) {
/*  610 */         if (this.sendUserids.startsWith(","))
/*  611 */           this.sendUserids = this.sendUserids.substring(1); 
/*  612 */         if (this.sendUserids.endsWith(",")) {
/*  613 */           this.sendUserids = this.sendUserids.substring(0, this.sendUserids.length() - 1);
/*      */         }
/*  615 */         String str = Util.getSubINClause(this.sendUserids, "c.userid", "IN");
/*  616 */         str4 = str4 + " and " + str;
/*      */       } 
/*      */       
/*  619 */       if (recordSet1.getDBType().equalsIgnoreCase("oracle")) {
/*  620 */         str4 = str4 + " and ((c.operatedate||' '||c.operatetime>='" + str1 + "' or  c.receivedate||' '||c.receivetime>='" + str1 + "')  or (isremark in (2,4) and ( c.operatedate is null or c.opdatetime>='" + this.operatedatetime + "' ) ) ";
/*      */       } else {
/*  622 */         str4 = str4 + " and ((c.operatedate+' '+c.operatetime>='" + str1 + "' or  c.receivedate+' '+c.receivetime>='" + str1 + "')  or (isremark in (2,4) and ( c.operatedate is null or c.opdatetime>='" + this.operatedatetime + "' ) ) ";
/*      */       } 
/*  624 */       str4 = str4 + " or ((isremark in  ('0','1','5','7','8','9','6','11') or c.operatedate is null) and c.islasttimes=1 ))";
/*      */     } 
/*  626 */     recordSet1.executeQuery(str4, new Object[0]);
/*  627 */     if (this.isdebug) {
/*  628 */       recordSet1.writeLog(str4);
/*      */     }
/*  630 */     while (recordSet1.next()) {
/*  631 */       String str5 = recordSet1.getString("userid");
/*  632 */       String str6 = recordSet1.getString("viewtype");
/*  633 */       String str7 = recordSet1.getString("nodeid");
/*  634 */       String str8 = recordSet1.getString("groupid");
/*  635 */       String str9 = Util.null2String(recordSet1.getString("receivedate"));
/*  636 */       String str10 = Util.null2String(recordSet1.getString("receivetime"));
/*      */       
/*  638 */       String str11 = Util.null2String(recordSet1.getString("operatedate"));
/*  639 */       String str12 = Util.null2String(recordSet1.getString("operatetime"));
/*  640 */       String str13 = Util.null2String(recordSet1.getString("opdatetime"));
/*      */       
/*  642 */       if (str11.equals("") && !str13.equals("")) {
/*  643 */         str11 = str13.substring(0, 10);
/*  644 */         str12 = str13.substring(11, str13.length());
/*      */       } 
/*      */       
/*  647 */       if ("-1".equals(str6)) {
/*  648 */         str6 = "-2";
/*      */       }
/*  650 */       String str14 = recordSet1.getString("workflowid");
/*  651 */       String str15 = recordSet1.getString("isremark");
/*  652 */       String str16 = recordSet1.getString("takisremark");
/*  653 */       RequestStatusObj requestStatusObj = new RequestStatusObj();
/*  654 */       requestStatusObj.setSendTime(l + "");
/*  655 */       requestStatusObj.setCid(recordSet1.getInt("id"));
/*  656 */       requestStatusObj.setCreatedate(recordSet1.getString("createdate"));
/*  657 */       requestStatusObj.setCreatetime(recordSet1.getString("createtime"));
/*  658 */       requestStatusObj.setCreator(new User(Util.getIntValue(recordSet1.getString("creater"))));
/*  659 */       requestStatusObj.setCreatorid(Util.getIntValue(recordSet1.getString("creater")));
/*  660 */       requestStatusObj.setIscomplete(recordSet1.getString("iscomplete"));
/*  661 */       requestStatusObj.setIsremark(str15);
/*  662 */       requestStatusObj.setNodeid(Util.getIntValue(recordSet1.getString("nodeid")));
/*  663 */       requestStatusObj.setNodename(recordSet1.getString("nodename"));
/*      */       
/*  665 */       requestStatusObj.setOperatedate(str11);
/*  666 */       requestStatusObj.setOperatetime(str12);
/*  667 */       requestStatusObj.setReceivedate(recordSet1.getString("receivedate"));
/*  668 */       requestStatusObj.setReceivetime(recordSet1.getString("receivetime"));
/*      */       
/*  670 */       requestStatusObj.setRequestid(Util.getIntValue(paramString));
/*  671 */       requestStatusObj.setRequestnamenew(recordSet1.getString("requestnamenew"));
/*  672 */       requestStatusObj.setRequeststatus(recordSet1.getString("status"));
/*  673 */       requestStatusObj.setRequstname(recordSet1.getString("requestname"));
/*  674 */       requestStatusObj.setUser(new User(Util.getIntValue(recordSet1.getString("userid"))));
/*  675 */       requestStatusObj.setViewtype(str6);
/*  676 */       requestStatusObj.setWorkflowid(recordSet1.getInt("workflowid"));
/*  677 */       requestStatusObj.setWorkflowname(workflowComInfo.getWorkflowname(str14));
/*      */       
/*  679 */       if (str15.equals("6") || str15.equals("2") || str15.equals("4") || (str15.equals("0") && str16.equals("-2"))) {
/*  680 */         if (str15.equals("0") && str16.equals("-2")) {
/*  681 */           requestStatusObj.setIsremark("2");
/*      */         }
/*  683 */         arrayList2.add(requestStatusObj); continue;
/*      */       } 
/*  685 */       arrayList1.add(requestStatusObj);
/*      */     } 
/*      */ 
/*      */ 
/*      */     
/*  690 */     DataObj dataObj = new DataObj();
/*  691 */     dataObj.setRequestid(paramString);
/*  692 */     dataObj.setTododatas(arrayList1);
/*  693 */     dataObj.setDonedatas(arrayList2);
/*  694 */     dataObj.setDeldatas(paramArrayList);
/*  695 */     dataObj.setSendtimestamp(l + "");
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  702 */     ArrayList<DataObj> arrayList = new ArrayList();
/*  703 */     arrayList.add(dataObj);
/*      */     
/*  705 */     if (arrayList1.size() > 0 || arrayList2.size() > 0 || paramArrayList.size() > 0) {
/*  706 */       DoSendRequestData(arrayList);
/*      */     }
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static String getSqlWhere(String paramString1, String paramString2, String paramString3) {
/*  718 */     String str = "";
/*  719 */     if ("0".equals(paramString1)) {
/*  720 */       str = str + " and userid in(" + paramString2 + ") and isremark in(0,8,9) and islasttimes=1 ";
/*  721 */     } else if ("1".equals(paramString1)) {
/*  722 */       str = str + " and userid in(" + paramString2 + ") and isremark in ('2','4') and islasttimes=1 and iscomplete=0";
/*  723 */     } else if ("2".equals(paramString1)) {
/*  724 */       str = str + "  and userid in(" + paramString2 + ") and islasttimes = 1 and isremark in ('2','4') and iscomplete=1 ";
/*  725 */     } else if ("3".equals(paramString1)) {
/*  726 */       str = str + "  and creatorid in(" + paramString2 + ") and creatorid=userid and islasttimes=1 ";
/*  727 */     } else if ("4".equals(paramString1)) {
/*  728 */       str = str + " and userid in(" + paramString2 + ") and isremark in ('2','4') and islasttimes=1 ";
/*      */     } else {
/*  730 */       str = str + " and 1=2";
/*      */     } 
/*  732 */     str = str + " and workflowid IN (SELECT workflowid  FROM   ofs_workflow WHERE  cancel = 0)";
/*  733 */     return str + paramString3;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String secLevel(String paramString1, String paramString2) {
/*  744 */     return paramString1 + " - " + paramString2;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String secLevel2(String paramString1, String paramString2) {
/*  753 */     if (paramString2 != null && paramString2.indexOf("+") >= 0) {
/*  754 */       String str1 = paramString2.split("\\+")[0];
/*  755 */       String str2 = paramString2.split("\\+")[1];
/*  756 */       if ("1".equals(str2)) {
/*  757 */         return "";
/*      */       }
/*  759 */       return paramString1 + " - " + str1;
/*      */     } 
/*      */     
/*  762 */     return paramString1 + " - " + paramString2;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String showType(String paramString1, String paramString2) {
/*  772 */     String str = "";
/*  773 */     if (paramString1.equals("1")) {
/*  774 */       str = SystemEnv.getHtmlLabelName(179, Util.getIntValue(paramString2, 7));
/*  775 */     } else if (paramString1.equals("2")) {
/*  776 */       str = SystemEnv.getHtmlLabelName(141, Util.getIntValue(paramString2, 7));
/*  777 */     } else if (paramString1.equals("3")) {
/*  778 */       str = SystemEnv.getHtmlLabelName(124, Util.getIntValue(paramString2, 7));
/*  779 */     } else if (paramString1.equals("4")) {
/*  780 */       str = SystemEnv.getHtmlLabelName(122, Util.getIntValue(paramString2, 7));
/*  781 */     } else if (paramString1.equals("5")) {
/*  782 */       str = SystemEnv.getHtmlLabelName(1340, Util.getIntValue(paramString2, 7));
/*      */     } else {
/*  784 */       str = "";
/*      */     } 
/*  786 */     return str;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String ObjName(String paramString1, String paramString2) {
/*  796 */     String str = "";
/*  797 */     ArrayList<E> arrayList = Util.TokenizerString(paramString2, "\\+");
/*      */     try {
/*  799 */       String str1 = arrayList.get(0).toString();
/*  800 */       String str2 = arrayList.get(1).toString();
/*  801 */       int i = Util.getIntValue(arrayList.get(2).toString(), 7);
/*  802 */       if (str1.equals("1")) {
/*  803 */         str = this.rci.getLastname(paramString1);
/*  804 */       } else if (str1.equals("2")) {
/*  805 */         str = this.sci.getSubCompanyname(paramString1);
/*  806 */       } else if (str1.equals("3")) {
/*  807 */         str = this.dci.getDepartmentname(paramString1);
/*  808 */       } else if (str1.equals("4")) {
/*  809 */         str = this.rolesComInfo.getRolesRemark(paramString1);
/*  810 */         if (str2.equals("0")) {
/*  811 */           str = str + " ( " + SystemEnv.getHtmlLabelName(124, i) + " )";
/*  812 */         } else if (str2.equals("1")) {
/*  813 */           str = str + " ( " + SystemEnv.getHtmlLabelName(141, i) + " )";
/*  814 */         } else if (str2.equals("2")) {
/*  815 */           str = str + " ( " + SystemEnv.getHtmlLabelName(140, i) + " )";
/*      */         } 
/*  817 */       } else if (str1.equals("5")) {
/*  818 */         str = SystemEnv.getHtmlLabelName(1340, i);
/*      */       } 
/*  820 */     } catch (Exception exception) {}
/*      */ 
/*      */ 
/*      */     
/*  824 */     return str;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public ArrayList<String> InitUserid(String paramString) {
/*  832 */     RecordSet recordSet1 = new RecordSet();
/*  833 */     RecordSet recordSet2 = new RecordSet();
/*  834 */     ArrayList<String> arrayList = new ArrayList();
/*  835 */     recordSet1.executeQuery("select * from ofs_senduser where mainid=?", new Object[] { paramString });
/*  836 */     while (recordSet1.next()) {
/*  837 */       String str1 = recordSet1.getString("type");
/*  838 */       String str2 = recordSet1.getString("objid");
/*  839 */       String str3 = recordSet1.getString("rolelevel");
/*  840 */       int i = Util.getIntValue(recordSet1.getString("seclevel"), 0);
/*  841 */       int j = Util.getIntValue(recordSet1.getString("seclevel1"), 100);
/*  842 */       if (str1.equals("1")) {
/*  843 */         if (!arrayList.contains(str2))
/*  844 */           arrayList.add(str2);  continue;
/*      */       } 
/*  846 */       if (str1.equals("2")) {
/*  847 */         recordSet2.executeQuery("select id from hrmresource where status in (0,1,2,3) and subcompanyid1=? and seclevel>=? and seclevel<=?", new Object[] { str2, Integer.valueOf(i), Integer.valueOf(j) });
/*  848 */         while (recordSet2.next()) {
/*  849 */           String str = recordSet1.getString(1);
/*  850 */           if (!arrayList.contains(str))
/*  851 */             arrayList.add(str); 
/*      */         }  continue;
/*      */       } 
/*  854 */       if (str1.equals("3")) {
/*  855 */         recordSet2.executeQuery("select id from hrmresource where status in (0,1,2,3) and departmentid=? and seclevel>=? and seclevel<=?", new Object[] { str2, Integer.valueOf(i), Integer.valueOf(j) });
/*  856 */         while (recordSet2.next()) {
/*  857 */           String str = recordSet1.getString(1);
/*  858 */           if (!arrayList.contains(str))
/*  859 */             arrayList.add(str); 
/*      */         }  continue;
/*      */       } 
/*  862 */       if (str1.equals("4")) {
/*  863 */         recordSet2.executeQuery("select h.id from hrmresource h,hrmrolemembers r where h.id = r.resourceid and r.roleid=? and r.rolelevel=? and h.status in (0,1,2,3) and h.seclevel>=? and h.seclevel<=?", new Object[] { str2, str3, Integer.valueOf(i), Integer.valueOf(j) });
/*  864 */         while (recordSet2.next()) {
/*  865 */           String str = recordSet1.getString(1);
/*  866 */           if (!arrayList.contains(str))
/*  867 */             arrayList.add(str); 
/*      */         }  continue;
/*      */       } 
/*  870 */       if (str1.equals("5")) {
/*  871 */         recordSet2.executeQuery("select id from hrmresource where status in (0,1,2,3) and seclevel>=? and seclevel<=?", new Object[] { Integer.valueOf(i), Integer.valueOf(j) });
/*  872 */         while (recordSet2.next()) {
/*  873 */           String str = recordSet1.getString(1);
/*  874 */           if (!arrayList.contains(str)) {
/*  875 */             arrayList.add(str);
/*      */           }
/*      */         } 
/*      */       } 
/*      */     } 
/*      */     
/*  881 */     return arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void SendRequestDataWithMessageCenter(List<MessageBean> paramList) {
/*  890 */     String str1 = "0";
/*  891 */     boolean bool = true;
/*      */     
/*  893 */     long l = System.currentTimeMillis();
/*  894 */     ResourceComInfo resourceComInfo = null;
/*  895 */     WorkflowComInfo workflowComInfo = null;
/*      */     try {
/*  897 */       resourceComInfo = new ResourceComInfo();
/*  898 */       workflowComInfo = new WorkflowComInfo();
/*  899 */     } catch (Exception exception) {
/*  900 */       exception.printStackTrace();
/*      */     } 
/*      */     
/*  903 */     String str2 = "";
/*  904 */     ArrayList<RequestStatusObj> arrayList1 = new ArrayList();
/*  905 */     ArrayList<RequestStatusObj> arrayList2 = new ArrayList();
/*  906 */     ArrayList<RequestStatusObj> arrayList3 = new ArrayList();
/*      */     
/*  908 */     RecordSet recordSet1 = new RecordSet();
/*  909 */     RecordSet recordSet2 = new RecordSet();
/*  910 */     recordSet1.executeProc("GetDBDateAndTime", "");
/*  911 */     String str3 = "";
/*  912 */     String str4 = "";
/*  913 */     if (recordSet1.next()) {
/*  914 */       str3 = recordSet1.getString("dbdate");
/*  915 */       str4 = recordSet1.getString("dbtime");
/*  916 */       str2 = TimeUtil.timeAdd(str3 + " " + str4, -15);
/*      */     } 
/*      */ 
/*      */ 
/*      */     
/*  921 */     String str5 = "select r.requestid,r.requestname,r.requestnamenew,r.workflowid,r.creater,r.createdate,r.createtime,r.requestlevel,r.status,c.id,c.nodeid,(select nodename from workflow_nodebase where id=r.currentnodeid) as nodename,c.userid,c.receivedate,c.receivetime,c.operatedate,c.operatetime,c.isremark,c.preisremark,c.islasttimes,c.takisremark,c.viewtype,c.iscomplete,c.groupid,opdatetime from workflow_requestbase r,workflow_currentoperator c where c.requestid=r.requestid and r.workflowid=c.workflowid and r.workflowid in (select id from workflow_base where (isvalid='1' or isvalid='3')) and r.requestid=" + str1;
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  926 */     if (this.isdebug) {
/*  927 */       recordSet1.writeLog("operatedatetime = " + this.operatedatetime + " sendUserids = " + this.sendUserids);
/*      */     }
/*  929 */     if (!"".equals(Util.null2String(this.operatedatetime))) {
/*  930 */       if (recordSet1.getDBType().equalsIgnoreCase("oracle")) {
/*  931 */         str5 = str5 + " and ((c.operatedate||' '||c.operatetime>='" + str2 + "' or (isremark in (2,4) and (c.operatedate is null or c.opdatetime>='" + this.operatedatetime + "')))";
/*  932 */         str5 = str5 + " or (isremark in  ('0','1','5','7','8','9','6','11') and c.islasttimes=1  and c.receivedate||' '||c.receivetime>='" + this.operatedatetime + "'))";
/*      */       } else {
/*  934 */         str5 = str5 + " and ((c.operatedate+' '+c.operatetime>='" + str2 + "'  or (isremark in (2,4) and (c.operatedate is null or c.opdatetime>='" + this.operatedatetime + "')))";
/*  935 */         str5 = str5 + " or (isremark in  ('0','1','5','7','8','9','6','11') and c.islasttimes=1  and c.receivedate+' '+c.receivetime>='" + this.operatedatetime + "'))";
/*      */       } 
/*      */     } else {
/*  938 */       if (!"".equals(Util.null2String(this.sendUserids))) {
/*  939 */         if (this.sendUserids.startsWith(","))
/*  940 */           this.sendUserids = this.sendUserids.substring(1); 
/*  941 */         if (this.sendUserids.endsWith(",")) {
/*  942 */           this.sendUserids = this.sendUserids.substring(0, this.sendUserids.length() - 1);
/*      */         }
/*  944 */         String str = Util.getSubINClause(this.sendUserids, "c.userid", "IN");
/*  945 */         str5 = str5 + " and " + str;
/*      */       } 
/*      */       
/*  948 */       if (recordSet1.getDBType().equalsIgnoreCase("oracle")) {
/*  949 */         str5 = str5 + " and ((c.operatedate||' '||c.operatetime>='" + str2 + "' or  c.receivedate||' '||c.receivetime>='" + str2 + "')  or (isremark in (2,4) and ( c.operatedate is null or c.opdatetime>='" + this.operatedatetime + "' ) ) ";
/*      */       } else {
/*  951 */         str5 = str5 + " and ((c.operatedate+' '+c.operatetime>='" + str2 + "' or  c.receivedate+' '+c.receivetime>='" + str2 + "')  or (isremark in (2,4) and ( c.operatedate is null or c.opdatetime>='" + this.operatedatetime + "' ) ) ";
/*      */       } 
/*  953 */       str5 = str5 + " or ((isremark in  ('0','1','5','7','8','9','6','11') or c.operatedate is null) and c.islasttimes=1 ))";
/*      */     } 
/*  955 */     recordSet1.executeQuery(str5, new Object[0]);
/*  956 */     if (this.isdebug) {
/*  957 */       recordSet1.writeLog(str5);
/*      */     }
/*  959 */     while (recordSet1.next()) {
/*  960 */       String str6 = recordSet1.getString("userid");
/*  961 */       String str7 = recordSet1.getString("viewtype");
/*  962 */       String str8 = recordSet1.getString("nodeid");
/*  963 */       String str9 = recordSet1.getString("groupid");
/*  964 */       String str10 = Util.null2String(recordSet1.getString("receivedate"));
/*  965 */       String str11 = Util.null2String(recordSet1.getString("receivetime"));
/*      */       
/*  967 */       String str12 = Util.null2String(recordSet1.getString("operatedate"));
/*  968 */       String str13 = Util.null2String(recordSet1.getString("operatetime"));
/*  969 */       String str14 = Util.null2String(recordSet1.getString("opdatetime"));
/*      */       
/*  971 */       if (str12.equals("") && !str14.equals("")) {
/*  972 */         str12 = str14.substring(0, 10);
/*  973 */         str13 = str14.substring(11, str14.length());
/*      */       } 
/*      */       
/*  976 */       if ("-1".equals(str7)) {
/*  977 */         str7 = "-2";
/*      */       }
/*  979 */       String str15 = recordSet1.getString("workflowid");
/*  980 */       String str16 = recordSet1.getString("isremark");
/*  981 */       String str17 = recordSet1.getString("takisremark");
/*  982 */       RequestStatusObj requestStatusObj = new RequestStatusObj();
/*  983 */       requestStatusObj.setSendTime(l + "");
/*  984 */       requestStatusObj.setCid(recordSet1.getInt("id"));
/*  985 */       requestStatusObj.setCreatedate(recordSet1.getString("createdate"));
/*  986 */       requestStatusObj.setCreatetime(recordSet1.getString("createtime"));
/*  987 */       requestStatusObj.setCreator(new User(Util.getIntValue(recordSet1.getString("creater"))));
/*  988 */       requestStatusObj.setCreatorid(Util.getIntValue(recordSet1.getString("creater")));
/*  989 */       requestStatusObj.setIscomplete(recordSet1.getString("iscomplete"));
/*  990 */       requestStatusObj.setIsremark(str16);
/*  991 */       requestStatusObj.setNodeid(Util.getIntValue(recordSet1.getString("nodeid")));
/*  992 */       requestStatusObj.setNodename(recordSet1.getString("nodename"));
/*      */       
/*  994 */       requestStatusObj.setOperatedate(str12);
/*  995 */       requestStatusObj.setOperatetime(str13);
/*  996 */       requestStatusObj.setReceivedate(recordSet1.getString("receivedate"));
/*  997 */       requestStatusObj.setReceivetime(recordSet1.getString("receivetime"));
/*      */       
/*  999 */       requestStatusObj.setRequestid(Util.getIntValue(str1));
/* 1000 */       requestStatusObj.setRequestnamenew(recordSet1.getString("requestnamenew"));
/* 1001 */       requestStatusObj.setRequeststatus(recordSet1.getString("status"));
/* 1002 */       requestStatusObj.setRequstname(recordSet1.getString("requestname"));
/* 1003 */       requestStatusObj.setUser(new User(Util.getIntValue(recordSet1.getString("userid"))));
/* 1004 */       requestStatusObj.setViewtype(str7);
/* 1005 */       requestStatusObj.setWorkflowid(recordSet1.getInt("workflowid"));
/* 1006 */       requestStatusObj.setWorkflowname(workflowComInfo.getWorkflowname(str15));
/*      */       
/* 1008 */       if (str16.equals("6") || str16.equals("2") || str16.equals("4") || (str16.equals("0") && str17.equals("-2"))) {
/* 1009 */         if (str16.equals("0") && str17.equals("-2")) {
/* 1010 */           requestStatusObj.setIsremark("2");
/*      */         }
/* 1012 */         arrayList2.add(requestStatusObj); continue;
/*      */       } 
/* 1014 */       arrayList1.add(requestStatusObj);
/*      */     } 
/*      */ 
/*      */ 
/*      */     
/* 1019 */     DataObj dataObj = new DataObj();
/* 1020 */     dataObj.setRequestid(str1);
/* 1021 */     dataObj.setTododatas(arrayList1);
/* 1022 */     dataObj.setDonedatas(arrayList2);
/* 1023 */     dataObj.setDeldatas(arrayList3);
/* 1024 */     dataObj.setSendtimestamp(l + "");
/*      */     
/* 1026 */     ArrayList<DataObj> arrayList = new ArrayList();
/* 1027 */     arrayList.add(dataObj);
/*      */     
/* 1029 */     if (arrayList1.size() > 0 || arrayList2.size() > 0 || arrayList3.size() > 0) {
/* 1030 */       DoSendRequestData(arrayList);
/*      */     }
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void sendDeleteRequestDataWithMessageCenter(List<MessageBean> paramList) {
/* 1041 */     WorkflowComInfo workflowComInfo = null;
/* 1042 */     RecordSet recordSet = new RecordSet();
/*      */     try {
/* 1044 */       Thread.sleep(1000L);
/* 1045 */     } catch (InterruptedException interruptedException) {
/* 1046 */       interruptedException.printStackTrace();
/*      */     } 
/*      */     try {
/* 1049 */       workflowComInfo = new WorkflowComInfo();
/* 1050 */     } catch (Exception exception) {
/* 1051 */       exception.printStackTrace();
/*      */     } 
/* 1053 */     long l = System.currentTimeMillis();
/* 1054 */     ArrayList<RequestStatusObj> arrayList = new ArrayList();
/* 1055 */     ArrayList<DataObj> arrayList1 = new ArrayList();
/* 1056 */     if (paramList != null && paramList.size() > 0) {
/* 1057 */       for (byte b = 0; b < paramList.size(); b++) {
/* 1058 */         MessageBean messageBean = paramList.get(b);
/* 1059 */         DataObj dataObj = new DataObj();
/* 1060 */         if (messageBean.getMessageType().getCode() == MessageType.WF_DELETE.getCode() || messageBean.getMessageType().getCode() == MessageType.WF_DRAW_BACK.getCode() || messageBean
/* 1061 */           .getMessageType().getCode() == MessageType.WF_STOP.getCode() || messageBean
/* 1062 */           .getMessageType().getCode() == MessageType.WF_CANCEL.getCode()) {
/*      */           
/* 1064 */           String str1 = messageBean.getTargetId();
/* 1065 */           HashMap<Object, Object> hashMap = new HashMap<>();
/* 1066 */           Map map = new HashMap<>();
/* 1067 */           if (messageBean.getMessageType().getCode() == MessageType.WF_DELETE.getCode()) {
/* 1068 */             recordSet.executeQuery("select id,userid,nodeId from workflow_curroperator_dellog where requestid = ? order by userid", new Object[] { str1 });
/* 1069 */             while (recordSet.next()) {
/* 1070 */               String str = recordSet.getString("userid");
/* 1071 */               int m = recordSet.getInt("id");
/* 1072 */               int n = recordSet.getInt("nodeId");
/* 1073 */               List<Integer> list = (List)hashMap.get(str);
/* 1074 */               if (list == null) {
/* 1075 */                 list = new ArrayList();
/*      */               }
/* 1077 */               list.add(Integer.valueOf(m));
/* 1078 */               hashMap.put(str, list);
/* 1079 */               map.put(String.valueOf(m), String.valueOf(n));
/*      */             } 
/*      */           } else {
/* 1082 */             Map map1 = (Map)messageBean.getParams().get("currentOperatorIds");
/* 1083 */             for (Map.Entry entry : map1.entrySet()) {
/* 1084 */               ArrayList<Integer> arrayList2 = new ArrayList();
/* 1085 */               arrayList2.add(Integer.valueOf(Util.getIntValue((String)entry.getValue())));
/* 1086 */               hashMap.put(entry.getKey(), arrayList2);
/*      */             } 
/* 1088 */             map = (Map)messageBean.getParams().get("currentOperatorNodes");
/*      */           } 
/* 1090 */           int i = Util.getIntValue((String)messageBean.getParams().get("detailBaseId"));
/* 1091 */           String str2 = messageBean.getMessageId();
/* 1092 */           int j = messageBean.getCreater();
/* 1093 */           boolean bool = false;
/* 1094 */           int k = messageBean.getState();
/* 1095 */           String str3 = "0";
/* 1096 */           String str4 = messageBean.getDate();
/* 1097 */           String str5 = messageBean.getTime();
/* 1098 */           Set<?> set = messageBean.getUserList();
/* 1099 */           if (set != null && set.size() > 0) {
/* 1100 */             ArrayList arrayList2 = new ArrayList(set);
/* 1101 */             for (String str : arrayList2) {
/* 1102 */               List list = (List)hashMap.get(str);
/* 1103 */               if (list == null) {
/*      */                 continue;
/*      */               }
/* 1106 */               hashMap.remove(str);
/* 1107 */               for (Iterator<Integer> iterator = list.iterator(); iterator.hasNext(); ) { int m = ((Integer)iterator.next()).intValue();
/* 1108 */                 String str6 = "";
/* 1109 */                 String str7 = "";
/* 1110 */                 RequestStatusObj requestStatusObj = new RequestStatusObj();
/* 1111 */                 requestStatusObj.setCid(m);
/* 1112 */                 requestStatusObj.setCreatedate(str4);
/* 1113 */                 requestStatusObj.setCreatetime(str5);
/* 1114 */                 requestStatusObj.setCreator(new User(j));
/* 1115 */                 requestStatusObj.setCreatorid(j);
/* 1116 */                 requestStatusObj.setIscomplete("1");
/* 1117 */                 requestStatusObj.setIsremark(str6);
/* 1118 */                 requestStatusObj.setNodeid(Util.getIntValue((String)map.get(String.valueOf(m))));
/* 1119 */                 requestStatusObj.setNodename("");
/* 1120 */                 requestStatusObj.setOperatedate(messageBean.getDate());
/* 1121 */                 requestStatusObj.setOperatetime(messageBean.getTime());
/* 1122 */                 requestStatusObj.setReceivedate(messageBean.getDate());
/* 1123 */                 requestStatusObj.setReceivetime(messageBean.getTime());
/* 1124 */                 requestStatusObj.setRequestid(Util.getIntValue(str1));
/* 1125 */                 requestStatusObj.setRequestnamenew(messageBean.getTargetName());
/* 1126 */                 requestStatusObj.setRequeststatus("" + k);
/* 1127 */                 requestStatusObj.setRequstname(messageBean.getTitle());
/* 1128 */                 requestStatusObj.setUser(new User(Util.getIntValue(str)));
/* 1129 */                 requestStatusObj.setViewtype(str3);
/* 1130 */                 requestStatusObj.setWorkflowid(i);
/* 1131 */                 requestStatusObj.setWorkflowname(workflowComInfo.getWorkflowname("" + i));
/* 1132 */                 requestStatusObj.setSendTime(l + "");
/*      */                 
/* 1134 */                 arrayList.add(requestStatusObj); }
/*      */             
/*      */             } 
/*      */           } 
/* 1138 */           dataObj.setRequestid(str1);
/* 1139 */           dataObj.setDeldatas(arrayList);
/* 1140 */           dataObj.setSendtimestamp(l + "");
/* 1141 */           arrayList1.add(dataObj);
/*      */         } 
/*      */       } 
/*      */     }
/*      */ 
/*      */ 
/*      */ 
/*      */     
/* 1149 */     DoSendRequestData(arrayList1);
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   public void sendDeleteRequestDataWithMessageCenter4DrawBack(String paramString1, String paramString2) {
/* 1155 */     WorkflowComInfo workflowComInfo = null;
/*      */     try {
/* 1157 */       workflowComInfo = new WorkflowComInfo();
/* 1158 */     } catch (Exception exception) {
/* 1159 */       exception.printStackTrace();
/*      */     } 
/* 1161 */     long l = System.currentTimeMillis();
/* 1162 */     ArrayList<RequestStatusObj> arrayList = new ArrayList();
/* 1163 */     RecordSet recordSet1 = new RecordSet();
/* 1164 */     RecordSet recordSet2 = new RecordSet();
/* 1165 */     ArrayList<DataObj> arrayList1 = new ArrayList();
/* 1166 */     String[] arrayOfString = Util.TokenizerString2(paramString1, ",");
/* 1167 */     for (byte b = 0; b < arrayOfString.length; b++) {
/* 1168 */       String str1 = arrayOfString[b];
/* 1169 */       DataObj dataObj = new DataObj();
/* 1170 */       String str2 = "select r.requestid,r.requestname,r.requestnamenew,r.workflowid,r.creater,r.createdate,r.createtime,r.requestlevel,r.status,c.id,c.nodeid,(select nodename from workflow_nodebase where id=r.currentnodeid) as nodename,c.userid,c.receivedate,c.receivetime,c.operatedate,c.operatetime,c.isremark,c.preisremark,c.islasttimes,c.takisremark,c.viewtype,c.iscomplete from workflow_requestbase r,workflow_currentoperator c where c.requestid=r.requestid  and r.requestid=" + str1 + " and c.userid in(" + paramString2 + ")";
/*      */ 
/*      */ 
/*      */       
/* 1174 */       recordSet1.executeQuery(str2, new Object[0]);
/* 1175 */       if (this.isdebug) {
/* 1176 */         writeLog("count : " + recordSet1.getCounts() + "  sql : " + str2);
/*      */       }
/* 1178 */       while (recordSet1.next()) {
/* 1179 */         String str3 = recordSet1.getString("userid");
/* 1180 */         String str4 = recordSet1.getString("workflowid");
/* 1181 */         String str5 = recordSet1.getString("isremark");
/* 1182 */         String str6 = recordSet1.getString("takisremark");
/* 1183 */         RequestStatusObj requestStatusObj = new RequestStatusObj();
/* 1184 */         requestStatusObj.setCid(recordSet1.getInt("id"));
/* 1185 */         requestStatusObj.setCreatedate(recordSet1.getString("createdate"));
/* 1186 */         requestStatusObj.setCreatetime(recordSet1.getString("createtime"));
/* 1187 */         requestStatusObj.setCreator(new User(Util.getIntValue(recordSet1.getString("creater"))));
/* 1188 */         requestStatusObj.setCreatorid(Util.getIntValue(recordSet1.getString("creater")));
/* 1189 */         requestStatusObj.setIscomplete(recordSet1.getString("iscomplete"));
/* 1190 */         requestStatusObj.setIsremark(str5);
/* 1191 */         requestStatusObj.setNodeid(Util.getIntValue(recordSet1.getString("nodeid")));
/* 1192 */         requestStatusObj.setNodename(recordSet1.getString("nodename"));
/* 1193 */         requestStatusObj.setOperatedate(recordSet1.getString("operatedate"));
/* 1194 */         requestStatusObj.setOperatetime(recordSet1.getString("operatetime"));
/* 1195 */         requestStatusObj.setReceivedate(recordSet1.getString("receivedate"));
/* 1196 */         requestStatusObj.setReceivetime(recordSet1.getString("receivetime"));
/* 1197 */         requestStatusObj.setRequestid(Util.getIntValue(str1));
/* 1198 */         requestStatusObj.setRequestnamenew(recordSet1.getString("requestnamenew"));
/* 1199 */         requestStatusObj.setRequeststatus(recordSet1.getString("status"));
/* 1200 */         requestStatusObj.setRequstname(recordSet1.getString("requestname"));
/* 1201 */         requestStatusObj.setUser(new User(Util.getIntValue(recordSet1.getString("userid"))));
/* 1202 */         requestStatusObj.setViewtype(recordSet1.getString("viewtype"));
/* 1203 */         requestStatusObj.setWorkflowid(recordSet1.getInt("workflowid"));
/* 1204 */         requestStatusObj.setWorkflowname(workflowComInfo.getWorkflowname(str4));
/* 1205 */         requestStatusObj.setSendTime(l + "");
/*      */ 
/*      */         
/* 1208 */         arrayList.add(requestStatusObj);
/*      */       } 
/*      */       
/* 1211 */       dataObj.setRequestid(str1);
/* 1212 */       dataObj.setDeldatas(arrayList);
/* 1213 */       dataObj.setSendtimestamp(l + "");
/* 1214 */       arrayList1.add(dataObj);
/*      */     } 
/*      */     
/* 1217 */     DoSendRequestData(arrayList1);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void SendRequestData2(String paramString, int paramInt, ArrayList<RequestStatusObj> paramArrayList) {
/* 1224 */     long l = System.currentTimeMillis();
/* 1225 */     ResourceComInfo resourceComInfo = null;
/* 1226 */     WorkflowComInfo workflowComInfo = null;
/*      */     try {
/* 1228 */       resourceComInfo = new ResourceComInfo();
/* 1229 */       workflowComInfo = new WorkflowComInfo();
/* 1230 */     } catch (Exception exception) {
/* 1231 */       exception.printStackTrace();
/*      */     } 
/*      */     
/* 1234 */     String str1 = "";
/* 1235 */     ArrayList<RequestStatusObj> arrayList1 = new ArrayList();
/* 1236 */     ArrayList<RequestStatusObj> arrayList2 = new ArrayList();
/* 1237 */     if (paramArrayList == null) {
/* 1238 */       paramArrayList = new ArrayList<>();
/*      */     }
/*      */     
/* 1241 */     RecordSet recordSet1 = new RecordSet();
/* 1242 */     RecordSet recordSet2 = new RecordSet();
/* 1243 */     recordSet1.executeProc("GetDBDateAndTime", "");
/* 1244 */     String str2 = "";
/* 1245 */     String str3 = "";
/* 1246 */     if (recordSet1.next()) {
/* 1247 */       str2 = recordSet1.getString("dbdate");
/* 1248 */       str3 = recordSet1.getString("dbtime");
/* 1249 */       str1 = TimeUtil.timeAdd(str2 + " " + str3, -15);
/*      */     } 
/*      */ 
/*      */ 
/*      */     
/* 1254 */     String str4 = "select r.requestid,r.requestname,r.requestnamenew,r.workflowid,r.creater,r.createdate,r.createtime,r.requestlevel,r.status,c.id,c.nodeid,(select nodename from workflow_nodebase where id=r.currentnodeid) as nodename,c.userid,c.receivedate,c.receivetime,c.operatedate,c.operatetime,c.isremark,c.preisremark,c.islasttimes,c.takisremark,c.viewtype,c.iscomplete,c.groupid,opdatetime from workflow_requestbase r,workflow_currentoperator c where c.requestid=r.requestid and r.workflowid=c.workflowid and r.workflowid in (select id from workflow_base where (isvalid='1' or isvalid='3')) and r.requestid=" + paramString;
/*      */ 
/*      */ 
/*      */ 
/*      */     
/* 1259 */     if (this.isdebug) {
/* 1260 */       recordSet1.writeLog("operatedatetime = " + this.operatedatetime + " sendUserids = " + this.sendUserids);
/*      */     }
/* 1262 */     if (!"".equals(Util.null2String(this.operatedatetime))) {
/* 1263 */       if (recordSet1.getDBType().equalsIgnoreCase("oracle")) {
/* 1264 */         str4 = str4 + " and ((c.operatedate||' '||c.operatetime>='" + str1 + "' or (isremark in (2,4) and (c.operatedate is null or c.opdatetime>='" + this.operatedatetime + "')))";
/* 1265 */         str4 = str4 + " or (isremark in  ('0','1','5','7','8','9','6','11') and c.islasttimes=1  and c.receivedate||' '||c.receivetime>='" + this.operatedatetime + "'))";
/* 1266 */       } else if (recordSet1.getDBType().equalsIgnoreCase("mysql")) {
/* 1267 */         str4 = str4 + " and ((concat(c.operatedate,' ',c.operatetime)>='" + str1 + "' or (isremark in (2,4) and (c.operatedate is null or c.opdatetime>='" + this.operatedatetime + "')))";
/* 1268 */         str4 = str4 + " or (isremark in  ('0','1','5','7','8','9','6','11') and c.islasttimes=1  and concat(c.receivedate,' ',c.receivetime)>='" + this.operatedatetime + "'))";
/*      */       }
/* 1270 */       else if (recordSet1.getDBType().equalsIgnoreCase("postgresql")) {
/* 1271 */         str4 = str4 + " and ((c.operatedate||' '||c.operatetime>='" + str1 + "' or (isremark in (2,4) and (c.operatedate is null or c.opdatetime>='" + this.operatedatetime + "')))";
/* 1272 */         str4 = str4 + " or (isremark in  ('0','1','5','7','8','9','6','11') and c.islasttimes=1  and c.receivedate||' '||c.receivetime>='" + this.operatedatetime + "'))";
/*      */       } else {
/*      */         
/* 1275 */         str4 = str4 + " and ((c.operatedate+' '+c.operatetime>='" + str1 + "'  or (isremark in (2,4) and (c.operatedate is null or c.opdatetime>='" + this.operatedatetime + "')))";
/* 1276 */         str4 = str4 + " or (isremark in  ('0','1','5','7','8','9','6','11') and c.islasttimes=1  and c.receivedate+' '+c.receivetime>='" + this.operatedatetime + "'))";
/*      */       } 
/*      */     } else {
/* 1279 */       if (!"".equals(Util.null2String(this.sendUserids))) {
/* 1280 */         if (this.sendUserids.startsWith(","))
/* 1281 */           this.sendUserids = this.sendUserids.substring(1); 
/* 1282 */         if (this.sendUserids.endsWith(",")) {
/* 1283 */           this.sendUserids = this.sendUserids.substring(0, this.sendUserids.length() - 1);
/*      */         }
/* 1285 */         String str = Util.getSubINClause(this.sendUserids, "c.userid", "IN");
/* 1286 */         str4 = str4 + " and " + str;
/*      */       } 
/*      */       
/* 1289 */       if (recordSet1.getDBType().equalsIgnoreCase("oracle")) {
/* 1290 */         str4 = str4 + " and ((c.operatedate||' '||c.operatetime>='" + str1 + "' or  c.receivedate||' '||c.receivetime>='" + str1 + "')  or (isremark in (2,4) and ( c.operatedate is null or c.opdatetime>='" + this.operatedatetime + "' ) ) ";
/* 1291 */       } else if (recordSet1.getDBType().equalsIgnoreCase("mysql")) {
/* 1292 */         str4 = str4 + " and ((concat(c.operatedate,' ',c.operatetime)>='" + str1 + "' or  concat(c.receivedate,' ',c.receivetime)>='" + str1 + "')  or (isremark in (2,4) and ( c.operatedate is null or c.opdatetime>='" + this.operatedatetime + "' ) ) ";
/*      */       }
/* 1294 */       else if (recordSet1.getDBType().equalsIgnoreCase("postgresql")) {
/* 1295 */         str4 = str4 + " and ((c.operatedate||' '||c.operatetime>='" + str1 + "' or  c.receivedate||' '||c.receivetime>='" + str1 + "')  or (isremark in (2,4) and ( c.operatedate is null or c.opdatetime>='" + this.operatedatetime + "' ) ) ";
/*      */       } else {
/*      */         
/* 1298 */         str4 = str4 + " and ((c.operatedate+' '+c.operatetime>='" + str1 + "' or  c.receivedate+' '+c.receivetime>='" + str1 + "')  or (isremark in (2,4) and ( c.operatedate is null or c.opdatetime>='" + this.operatedatetime + "' ) ) ";
/*      */       } 
/* 1300 */       str4 = str4 + " or ((isremark in  ('0','1','5','7','8','9','6','11') or c.operatedate is null) and c.islasttimes=1 ))";
/*      */     } 
/* 1302 */     str4 = str4 + " order by id desc";
/* 1303 */     recordSet1.executeQuery(str4, new Object[0]);
/* 1304 */     if (this.isdebug) {
/* 1305 */       recordSet1.writeLog(str4);
/*      */     }
/* 1307 */     String str5 = ",";
/* 1308 */     String str6 = ",";
/* 1309 */     while (recordSet1.next()) {
/* 1310 */       String str7 = recordSet1.getString("userid");
/* 1311 */       String str8 = recordSet1.getString("viewtype");
/* 1312 */       String str9 = recordSet1.getString("nodeid");
/* 1313 */       String str10 = recordSet1.getString("groupid");
/* 1314 */       String str11 = Util.null2String(recordSet1.getString("receivedate"));
/* 1315 */       String str12 = Util.null2String(recordSet1.getString("receivetime"));
/*      */       
/* 1317 */       String str13 = Util.null2String(recordSet1.getString("operatedate"));
/* 1318 */       String str14 = Util.null2String(recordSet1.getString("operatetime"));
/* 1319 */       String str15 = Util.null2String(recordSet1.getString("opdatetime"));
/*      */       
/* 1321 */       if (str13.equals("") && !str15.equals("")) {
/* 1322 */         str13 = str15.substring(0, 10);
/* 1323 */         str14 = str15.substring(11, str15.length());
/*      */       } 
/*      */       
/* 1326 */       if ("-1".equals(str8)) {
/* 1327 */         str8 = "-2";
/*      */       }
/* 1329 */       String str16 = recordSet1.getString("workflowid");
/* 1330 */       String str17 = recordSet1.getString("isremark");
/* 1331 */       String str18 = recordSet1.getString("takisremark");
/* 1332 */       RequestStatusObj requestStatusObj = new RequestStatusObj();
/* 1333 */       requestStatusObj.setSendTime(l + "");
/* 1334 */       requestStatusObj.setCid(recordSet1.getInt("id"));
/* 1335 */       requestStatusObj.setCreatedate(recordSet1.getString("createdate"));
/* 1336 */       requestStatusObj.setCreatetime(recordSet1.getString("createtime"));
/* 1337 */       requestStatusObj.setCreator(new User(Util.getIntValue(recordSet1.getString("creater"))));
/* 1338 */       requestStatusObj.setCreatorid(Util.getIntValue(recordSet1.getString("creater")));
/* 1339 */       requestStatusObj.setIscomplete(recordSet1.getString("iscomplete"));
/* 1340 */       requestStatusObj.setIsremark(str17);
/* 1341 */       requestStatusObj.setNodeid(Util.getIntValue(recordSet1.getString("nodeid")));
/* 1342 */       requestStatusObj.setNodename(recordSet1.getString("nodename"));
/*      */       
/* 1344 */       requestStatusObj.setOperatedate(str13);
/* 1345 */       requestStatusObj.setOperatetime(str14);
/* 1346 */       requestStatusObj.setReceivedate(recordSet1.getString("receivedate"));
/* 1347 */       requestStatusObj.setReceivetime(recordSet1.getString("receivetime"));
/*      */       
/* 1349 */       requestStatusObj.setRequestid(Util.getIntValue(paramString));
/* 1350 */       requestStatusObj.setRequestnamenew(recordSet1.getString("requestnamenew"));
/* 1351 */       requestStatusObj.setRequeststatus(recordSet1.getString("status"));
/* 1352 */       requestStatusObj.setRequstname(recordSet1.getString("requestname"));
/* 1353 */       requestStatusObj.setUser(new User(Util.getIntValue(str7)));
/* 1354 */       requestStatusObj.setViewtype(str8);
/* 1355 */       requestStatusObj.setWorkflowid(recordSet1.getInt("workflowid"));
/* 1356 */       requestStatusObj.setWorkflowname(workflowComInfo.getWorkflowname(str16));
/* 1357 */       requestStatusObj.setRequestlevel(recordSet1.getInt("requestlevel"));
/*      */       
/* 1359 */       if (str17.equals("6") || str17.equals("2") || str17.equals("4") || (str17.equals("0") && str18.equals("-2"))) {
/* 1360 */         if (str17.equals("0") && str18.equals("-2")) {
/* 1361 */           requestStatusObj.setIsremark("2");
/*      */         }
/* 1363 */         if (str6.indexOf("," + str7 + ",") < 0) {
/* 1364 */           arrayList2.add(requestStatusObj);
/* 1365 */           str6 = str6 + str7 + ",";
/*      */         }  continue;
/*      */       } 
/* 1368 */       if (str5.indexOf("," + str7 + ",") < 0) {
/* 1369 */         arrayList1.add(requestStatusObj);
/* 1370 */         str5 = str5 + str7 + ",";
/*      */       } 
/*      */     } 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/* 1377 */     DataObj dataObj = new DataObj();
/* 1378 */     dataObj.setRequestid(paramString);
/* 1379 */     dataObj.setTododatas(arrayList1);
/* 1380 */     dataObj.setDonedatas(arrayList2);
/* 1381 */     dataObj.setDeldatas(paramArrayList);
/* 1382 */     dataObj.setSendtimestamp(l + "");
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/* 1389 */     ArrayList<DataObj> arrayList = new ArrayList();
/* 1390 */     arrayList.add(dataObj);
/*      */     
/* 1392 */     if (arrayList1.size() > 0 || arrayList2.size() > 0 || paramArrayList.size() > 0) {
/* 1393 */       DoSendRequestData(arrayList);
/*      */     }
/*      */   }
/*      */ 
/*      */   
/*      */   public void sendRequest(String paramString) {
/* 1399 */     String str1 = getRequestBaseDataJSON(paramString);
/* 1400 */     if ("".equals(str1)) {
/* 1401 */       writeLog("requestId 没有查询到 流程数据:" + paramString);
/*      */       return;
/*      */     } 
/* 1404 */     String str2 = Util.null2String(Long.valueOf(System.currentTimeMillis()));
/*      */     
/* 1406 */     StringBuffer stringBuffer = new StringBuffer("select * from workflow_currentoperator where requestid = ? and isremark in  ('0','1','5','7','8','9','6','11','2','4') and islasttimes=1 ");
/* 1407 */     if (!"".equals(Util.null2String(this.sendUserids))) {
/* 1408 */       if (this.sendUserids.startsWith(","))
/* 1409 */         this.sendUserids = this.sendUserids.substring(1); 
/* 1410 */       if (this.sendUserids.endsWith(",")) {
/* 1411 */         this.sendUserids = this.sendUserids.substring(0, this.sendUserids.length() - 1);
/*      */       }
/* 1413 */       String str = Util.getSubINClause(this.sendUserids, "userid", "IN");
/* 1414 */       stringBuffer.append(" and ").append(str);
/*      */     } 
/* 1416 */     RecordSet recordSet = new RecordSet();
/* 1417 */     recordSet.executeQuery(stringBuffer.toString(), new Object[] { paramString });
/* 1418 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 1419 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/*      */     
/* 1421 */     while (recordSet.next()) {
/* 1422 */       String str3 = Util.null2String(recordSet.getString("isremark"));
/* 1423 */       String str4 = Util.null2String(recordSet.getString("viewtype"));
/* 1424 */       RequestStatusObj requestStatusObj = (RequestStatusObj)JSONObject.parseObject(str1, RequestStatusObj.class);
/* 1425 */       requestStatusObj.setSendTime(str2);
/* 1426 */       requestStatusObj.setCid(Util.getIntValue(recordSet.getString("id"), 0));
/* 1427 */       requestStatusObj.setIscomplete(Util.null2String(recordSet.getString("iscomplete")));
/*      */ 
/*      */       
/* 1430 */       requestStatusObj.setNodeid(Util.getIntValue(recordSet.getString("nodeid")));
/* 1431 */       requestStatusObj.setNodename(getNodeNameByNodeId(requestStatusObj.getNodeid()));
/*      */       
/* 1433 */       requestStatusObj.setReceivedate(Util.null2String(recordSet.getString("receivedate")));
/* 1434 */       requestStatusObj.setReceivetime(Util.null2String(recordSet.getString("receivetime")));
/*      */       
/* 1436 */       requestStatusObj.setOperatedate(Util.null2String(recordSet.getString("operatedate")));
/* 1437 */       requestStatusObj.setOperatetime(Util.null2String(recordSet.getString("operatetime")));
/*      */       
/* 1439 */       int i = Util.getIntValue(recordSet.getString("userid"), 0);
/* 1440 */       requestStatusObj.setUser(new User(i));
/*      */ 
/*      */       
/* 1443 */       if ("-1".equals(str4)) {
/* 1444 */         str4 = "-2";
/*      */       }
/* 1446 */       requestStatusObj.setViewtype(str4);
/* 1447 */       requestStatusObj.setIsremark(str3);
/*      */       
/* 1449 */       String str5 = Util.null2String(recordSet.getString("takisremark"));
/* 1450 */       if ("6".equals(str3) || "2".equals(str3) || "4".equals(str3) || ("0".equals(str3) && "-2".equals(str5))) {
/* 1451 */         if ("0".equals(str3) && "-2".equals(str5)) {
/* 1452 */           requestStatusObj.setIsremark("2");
/*      */         }
/* 1454 */         hashMap2.put(Integer.valueOf(i), requestStatusObj); continue;
/*      */       } 
/* 1456 */       hashMap1.put(Integer.valueOf(i), requestStatusObj);
/*      */     } 
/*      */     
/* 1459 */     DataObj dataObj = new DataObj();
/* 1460 */     dataObj.setRequestid(paramString);
/* 1461 */     dataObj.setDeldatas(new ArrayList<>());
/* 1462 */     dataObj.setTododatas(new ArrayList<>(hashMap1.values()));
/* 1463 */     dataObj.setDonedatas(new ArrayList<>(hashMap2.values()));
/* 1464 */     dataObj.setSendtimestamp(str2);
/*      */     
/* 1466 */     ArrayList<DataObj> arrayList = new ArrayList();
/* 1467 */     arrayList.add(dataObj);
/*      */     
/* 1469 */     if (dataObj.getDonedatas().size() > 0 || dataObj.getTododatas().size() > 0 || dataObj.getDeldatas().size() > 0) {
/* 1470 */       DoSendRequestData(arrayList);
/*      */     }
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   private String getRequestBaseDataJSON(String paramString) {
/* 1477 */     String str = "select a.creater,a.createdate,a.createtime,a.workflowid,a.requestname,a.requestnamenew,a.status,a.requestlevel,b.workflowname from workflow_requestbase a,workflow_base b where a.requestid = ? and a.workflowid = b.id and (b.isvalid='1' or b.isvalid='3')";
/*      */     
/* 1479 */     RecordSet recordSet = new RecordSet();
/* 1480 */     recordSet.executeQuery(str, new Object[] { paramString });
/* 1481 */     if (recordSet.getCounts() == 0) {
/* 1482 */       str = "select a.creater,a.createdate,a.createtime,a.workflowid,a.requestname,a.requestnamenew,a.status,a.requestlevel,b.workflowname from workflow_requestbase_dellog a,workflow_base b where a.requestid = ? and a.workflowid = b.id and (b.isvalid='1' or b.isvalid='3')";
/*      */       
/* 1484 */       recordSet.executeQuery(str, new Object[] { paramString });
/*      */     } 
/* 1486 */     if (recordSet.next()) {
/* 1487 */       RequestStatusObj requestStatusObj = new RequestStatusObj();
/*      */       
/* 1489 */       requestStatusObj.setRequestid(Util.getIntValue(paramString, 0));
/* 1490 */       requestStatusObj.setCreatorid(Util.getIntValue(recordSet.getString("creater"), 0));
/* 1491 */       requestStatusObj.setCreator(new User(requestStatusObj.getCreatorid()));
/* 1492 */       requestStatusObj.setCreatedate(Util.null2String(recordSet.getString("createdate")));
/* 1493 */       requestStatusObj.setCreatetime(Util.null2String(recordSet.getString("createtime")));
/*      */       
/* 1495 */       requestStatusObj.setWorkflowid(Util.getIntValue(recordSet.getString("workflowid"), 0));
/* 1496 */       requestStatusObj.setWorkflowname(recordSet.getString("workflowname"));
/*      */       
/* 1498 */       requestStatusObj.setRequstname(Util.null2String(recordSet.getString("requestname")));
/* 1499 */       requestStatusObj.setRequestnamenew(Util.null2String(recordSet.getString("requestnamenew")));
/*      */       
/* 1501 */       requestStatusObj.setRequeststatus(Util.null2String(recordSet.getString("status")));
/* 1502 */       requestStatusObj.setRequestlevel(recordSet.getInt("requestlevel"));
/* 1503 */       return JSONObject.toJSONString(requestStatusObj);
/*      */     } 
/* 1505 */     return "";
/*      */   }
/*      */   
/*      */   private String getNodeNameByNodeId(int paramInt) {
/* 1509 */     String str = "select nodename from workflow_nodebase where id= ? ";
/* 1510 */     RecordSet recordSet = new RecordSet();
/* 1511 */     recordSet.executeQuery(str, new Object[] { Integer.valueOf(paramInt) });
/* 1512 */     if (recordSet.next()) {
/* 1513 */       return Util.null2String(recordSet.getString("nodename"));
/*      */     }
/* 1515 */     return "";
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void sendRequestByCID(String paramString1, String paramString2) {
/* 1524 */     String str1 = getRequestBaseDataJSON(paramString1);
/* 1525 */     outlog("统一待办流程推送数据start----requestid:" + paramString1 + ";cids:" + paramString2 + ";tread:" + Thread.currentThread().getId() + ";requestBaseDataJson:" + str1);
/* 1526 */     if ("".equals(str1)) {
/* 1527 */       writeLog("requestId 没有查询到 流程数据:" + paramString1);
/*      */       
/*      */       return;
/*      */     } 
/* 1531 */     String str2 = Util.null2String(Long.valueOf(System.currentTimeMillis()));
/* 1532 */     RecordSet recordSet = new RecordSet();
/* 1533 */     String str3 = "select isremark,viewtype,id,iscomplete,nodeid,receivedate,receivetime,operatedate,operatetime,userid,takisremark from workflow_currentoperator where id in(" + paramString2 + ") and islasttimes=1 ";
/* 1534 */     recordSet.executeQuery(str3, new Object[0]);
/* 1535 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 1536 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 1537 */     String str4 = "";
/* 1538 */     String str5 = "";
/*      */     
/* 1540 */     while (recordSet.next()) {
/* 1541 */       String str6 = Util.null2String(recordSet.getString("isremark"));
/* 1542 */       String str7 = Util.null2String(recordSet.getString("viewtype"));
/* 1543 */       String str8 = Util.null2String(recordSet.getString("takisremark"));
/* 1544 */       int i = Util.getIntValue(recordSet.getString("id"), 0);
/* 1545 */       int j = Util.getIntValue(recordSet.getString("userid"), 0);
/*      */ 
/*      */       
/* 1548 */       RequestStatusObj requestStatusObj = (RequestStatusObj)JSONObject.parseObject(str1, RequestStatusObj.class);
/* 1549 */       requestStatusObj.setSendTime(str2);
/* 1550 */       requestStatusObj.setCid(i);
/* 1551 */       requestStatusObj.setIscomplete(Util.null2String(recordSet.getString("iscomplete")));
/* 1552 */       requestStatusObj.setNodeid(Util.getIntValue(recordSet.getString("nodeid")));
/* 1553 */       requestStatusObj.setNodename(getNodeNameByNodeId(requestStatusObj.getNodeid()));
/* 1554 */       requestStatusObj.setReceivedate(Util.null2String(recordSet.getString("receivedate")));
/* 1555 */       requestStatusObj.setReceivetime(Util.null2String(recordSet.getString("receivetime")));
/* 1556 */       requestStatusObj.setOperatedate(Util.null2String(recordSet.getString("operatedate")));
/* 1557 */       requestStatusObj.setOperatetime(Util.null2String(recordSet.getString("operatetime")));
/* 1558 */       requestStatusObj.setUser(new User(j));
/*      */       
/* 1560 */       if ("-1".equals(str7)) {
/* 1561 */         str7 = "-2";
/*      */       }
/* 1563 */       requestStatusObj.setViewtype(str7);
/* 1564 */       requestStatusObj.setIsremark(str6);
/*      */       
/* 1566 */       if ("6".equals(str6) || "2".equals(str6) || "4".equals(str6) || ("0".equals(str6) && "-2".equals(str8))) {
/* 1567 */         if ("0".equals(str6) && "-2".equals(str8)) {
/* 1568 */           requestStatusObj.setIsremark("2");
/*      */         }
/* 1570 */         hashMap2.put(Integer.valueOf(j), requestStatusObj);
/* 1571 */         str5 = str5 + j + "_" + i + ","; continue;
/*      */       } 
/* 1573 */       hashMap1.put(Integer.valueOf(j), requestStatusObj);
/* 1574 */       str4 = str4 + j + "_" + i + ",";
/*      */     } 
/*      */ 
/*      */     
/* 1578 */     DataObj dataObj = new DataObj();
/* 1579 */     dataObj.setRequestid(paramString1);
/* 1580 */     dataObj.setDeldatas(new ArrayList<>());
/* 1581 */     dataObj.setTododatas(new ArrayList<>(hashMap1.values()));
/* 1582 */     dataObj.setDonedatas(new ArrayList<>(hashMap2.values()));
/* 1583 */     dataObj.setSendtimestamp(str2);
/*      */     
/* 1585 */     ArrayList<DataObj> arrayList = new ArrayList();
/* 1586 */     arrayList.add(dataObj);
/* 1587 */     outlog("统一待办流程推送数据end----requestid:" + paramString1 + ";cids:" + paramString2 + ";tread:" + Thread.currentThread().getId() + ";todoSize:" + hashMap1.size() + ";doneSize:" + hashMap2.size() + ";todoCids:" + str4 + ";doneCids:" + str5);
/*      */     
/* 1589 */     if (dataObj.getDonedatas().size() > 0 || dataObj.getTododatas().size() > 0 || dataObj.getDeldatas().size() > 0) {
/* 1590 */       DoSendRequestData(arrayList);
/*      */     }
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public List<RequestStatusObj> getRequestObjsByCID(String paramString1, String paramString2) {
/* 1601 */     ArrayList<RequestStatusObj> arrayList = new ArrayList();
/* 1602 */     String str1 = getRequestBaseDataJSON(paramString1);
/* 1603 */     outlog("统一待办推送初始数据----requestid:" + paramString1 + ",cids:" + paramString2 + ",thread:" + Thread.currentThread().getId() + ",requestBaseDataJson:" + str1);
/* 1604 */     if ("".equals(str1)) {
/* 1605 */       writeLog("requestId 没有查询到 流程数据:" + paramString1);
/* 1606 */       return arrayList;
/*      */     } 
/*      */     
/* 1609 */     String str2 = Util.null2String(Long.valueOf(System.currentTimeMillis()));
/* 1610 */     RecordSet recordSet = new RecordSet();
/* 1611 */     String str3 = "select isremark,viewtype,id,iscomplete,nodeid,receivedate,receivetime,operatedate,operatetime,userid,takisremark from workflow_currentoperator where" + Util.getSubINClause(paramString2, "id", "in");
/* 1612 */     recordSet.executeQuery(str3, new Object[0]);
/*      */     
/* 1614 */     if (recordSet.getCounts() == 0) {
/* 1615 */       str3 = "select isremark,viewtype,id,iscomplete,nodeid,receivedate,receivetime,operatedate,operatetime,userid,takisremark from workflow_curroperator_dellog where" + Util.getSubINClause(paramString2, "id", "in");
/* 1616 */       recordSet.executeQuery(str3, new Object[0]);
/*      */     } 
/*      */     
/* 1619 */     if (recordSet.getCounts() == 0) {
/* 1620 */       str3 = "select isremark,viewtype,id,iscomplete,nodeid,receivedate,receivetime,operatedate,operatetime,userid,takisremark from workflow_operator_backlog where" + Util.getSubINClause(paramString2, "id", "in");
/* 1621 */       recordSet.executeQuery(str3, new Object[0]);
/*      */     } 
/*      */     
/* 1624 */     while (recordSet.next()) {
/* 1625 */       String str4 = Util.null2String(recordSet.getString("isremark"));
/* 1626 */       String str5 = Util.null2String(recordSet.getString("viewtype"));
/* 1627 */       String str6 = Util.null2String(recordSet.getString("takisremark"));
/* 1628 */       int i = Util.getIntValue(recordSet.getString("id"), 0);
/* 1629 */       int j = Util.getIntValue(recordSet.getString("userid"), 0);
/*      */ 
/*      */       
/* 1632 */       RequestStatusObj requestStatusObj = (RequestStatusObj)JSONObject.parseObject(str1, RequestStatusObj.class);
/* 1633 */       requestStatusObj.setSendTime(str2);
/* 1634 */       requestStatusObj.setCid(i);
/* 1635 */       requestStatusObj.setIscomplete(Util.null2String(recordSet.getString("iscomplete")));
/* 1636 */       requestStatusObj.setNodeid(Util.getIntValue(recordSet.getString("nodeid")));
/* 1637 */       requestStatusObj.setNodename(getNodeNameByNodeId(requestStatusObj.getNodeid()));
/* 1638 */       requestStatusObj.setReceivedate(Util.null2String(recordSet.getString("receivedate")));
/* 1639 */       requestStatusObj.setReceivetime(Util.null2String(recordSet.getString("receivetime")));
/* 1640 */       requestStatusObj.setOperatedate(Util.null2String(recordSet.getString("operatedate")));
/* 1641 */       requestStatusObj.setOperatetime(Util.null2String(recordSet.getString("operatetime")));
/* 1642 */       requestStatusObj.setUser(new User(j));
/*      */       
/* 1644 */       str5 = str5.equals("-1") ? "-2" : str5;
/* 1645 */       requestStatusObj.setViewtype(str5);
/*      */       
/* 1647 */       requestStatusObj.setIsremark(str4);
/* 1648 */       if ("0".equals(str4) && "-2".equals(str6)) {
/* 1649 */         requestStatusObj.setIsremark("2");
/*      */       }
/*      */       
/* 1652 */       if (Strings.isNullOrEmpty(requestStatusObj.getRequestnamenew())) {
/* 1653 */         requestStatusObj.setRequestnamenew(requestStatusObj.getRequstname());
/*      */       }
/* 1655 */       arrayList.add(requestStatusObj);
/*      */     } 
/* 1657 */     outlog("统一待办推送实际数据----requestid:" + paramString1 + ",thread:" + Thread.currentThread().getId() + ",requestObjs:" + JSONObject.toJSONString(arrayList));
/* 1658 */     return arrayList;
/*      */   }
/*      */   
/*      */   private void outlog(String paramString) {
/* 1662 */     RecordSet recordSet = new RecordSet();
/* 1663 */     recordSet.executeQuery("select value from workflow_config where type = 'msg' and name ='out_log'", new Object[0]);
/* 1664 */     recordSet.next();
/* 1665 */     boolean bool = "1".equals(recordSet.getString(1));
/* 1666 */     if (bool) log.info(paramString); 
/*      */   }
/*      */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/todo/RequestUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */