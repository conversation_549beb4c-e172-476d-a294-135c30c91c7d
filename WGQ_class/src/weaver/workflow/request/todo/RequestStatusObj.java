/*     */ package weaver.workflow.request.todo;
/*     */ 
/*     */ import weaver.hrm.User;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class RequestStatusObj
/*     */ {
/*     */   private int requestid;
/*     */   private int cid;
/*     */   private int workflowid;
/*     */   private String workflowname;
/*     */   private int creatorid;
/*     */   private User creator;
/*     */   private String createdate;
/*     */   private String createtime;
/*     */   private int nodeid;
/*     */   private String nodename;
/*     */   private String requstname;
/*     */   private String requestnamenew;
/*     */   private String userid;
/*     */   private String usertype;
/*     */   private String username;
/*     */   private User user;
/*     */   private String receivedate;
/*     */   private String receivetime;
/*     */   private String operatedate;
/*     */   private String operatetime;
/*     */   private String isremark;
/*     */   private String iscomplete;
/*     */   private String viewtype;
/*     */   private String requeststatus;
/*     */   private String sendTime;
/*     */   private int requestlevel;
/*     */   private String ofsPushId;
/*     */   
/*     */   public String getOfsPushId() {
/*  40 */     return this.ofsPushId;
/*     */   }
/*     */   
/*     */   public void setOfsPushId(String paramString) {
/*  44 */     this.ofsPushId = paramString;
/*     */   }
/*     */   
/*     */   public int getRequestid() {
/*  48 */     return this.requestid;
/*     */   }
/*     */   
/*     */   public void setRequestid(int paramInt) {
/*  52 */     this.requestid = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getCid() {
/*  60 */     return this.cid;
/*     */   }
/*     */   
/*     */   public User getUser() {
/*  64 */     return this.user;
/*     */   }
/*     */   
/*     */   public void setUser(User paramUser) {
/*  68 */     this.user = paramUser;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setCid(int paramInt) {
/*  76 */     this.cid = paramInt;
/*     */   }
/*     */   
/*     */   public int getWorkflowid() {
/*  80 */     return this.workflowid;
/*     */   }
/*     */   
/*     */   public void setWorkflowid(int paramInt) {
/*  84 */     this.workflowid = paramInt;
/*     */   }
/*     */   
/*     */   public String getWorkflowname() {
/*  88 */     return this.workflowname;
/*     */   }
/*     */   
/*     */   public void setWorkflowname(String paramString) {
/*  92 */     this.workflowname = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getCreatorid() {
/* 100 */     return this.creatorid;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setCreatorid(int paramInt) {
/* 108 */     this.creatorid = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public User getCreator() {
/* 116 */     return this.creator;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setCreator(User paramUser) {
/* 124 */     this.creator = paramUser;
/*     */   }
/*     */   
/*     */   public String getCreatedate() {
/* 128 */     return this.createdate;
/*     */   }
/*     */   
/*     */   public void setCreatedate(String paramString) {
/* 132 */     this.createdate = paramString;
/*     */   }
/*     */   
/*     */   public String getCreatetime() {
/* 136 */     return this.createtime;
/*     */   }
/*     */   
/*     */   public void setCreatetime(String paramString) {
/* 140 */     this.createtime = paramString;
/*     */   }
/*     */   
/*     */   public int getNodeid() {
/* 144 */     return this.nodeid;
/*     */   }
/*     */   
/*     */   public void setNodeid(int paramInt) {
/* 148 */     this.nodeid = paramInt;
/*     */   }
/*     */   
/*     */   public String getNodename() {
/* 152 */     return this.nodename;
/*     */   }
/*     */   
/*     */   public void setNodename(String paramString) {
/* 156 */     this.nodename = paramString;
/*     */   }
/*     */   
/*     */   public String getRequstname() {
/* 160 */     return this.requstname;
/*     */   }
/*     */   
/*     */   public void setRequstname(String paramString) {
/* 164 */     this.requstname = paramString;
/*     */   }
/*     */   
/*     */   public String getRequestnamenew() {
/* 168 */     return this.requestnamenew;
/*     */   }
/*     */   
/*     */   public void setRequestnamenew(String paramString) {
/* 172 */     this.requestnamenew = paramString;
/*     */   }
/*     */   
/*     */   public String getReceivedate() {
/* 176 */     return this.receivedate;
/*     */   }
/*     */   
/*     */   public void setReceivedate(String paramString) {
/* 180 */     this.receivedate = paramString;
/*     */   }
/*     */   
/*     */   public String getReceivetime() {
/* 184 */     return this.receivetime;
/*     */   }
/*     */   
/*     */   public void setReceivetime(String paramString) {
/* 188 */     this.receivetime = paramString;
/*     */   }
/*     */   
/*     */   public String getOperatedate() {
/* 192 */     return this.operatedate;
/*     */   }
/*     */   
/*     */   public void setOperatedate(String paramString) {
/* 196 */     this.operatedate = paramString;
/*     */   }
/*     */   
/*     */   public String getOperatetime() {
/* 200 */     return this.operatetime;
/*     */   }
/*     */   
/*     */   public void setOperatetime(String paramString) {
/* 204 */     this.operatetime = paramString;
/*     */   }
/*     */   
/*     */   public String getIsremark() {
/* 208 */     return this.isremark;
/*     */   }
/*     */   
/*     */   public void setIsremark(String paramString) {
/* 212 */     this.isremark = paramString;
/*     */   }
/*     */   
/*     */   public String getIscomplete() {
/* 216 */     return this.iscomplete;
/*     */   }
/*     */   
/*     */   public void setIscomplete(String paramString) {
/* 220 */     this.iscomplete = paramString;
/*     */   }
/*     */   
/*     */   public String getViewtype() {
/* 224 */     return this.viewtype;
/*     */   }
/*     */   
/*     */   public void setViewtype(String paramString) {
/* 228 */     this.viewtype = paramString;
/*     */   }
/*     */   
/*     */   public String getRequeststatus() {
/* 232 */     return this.requeststatus;
/*     */   }
/*     */   
/*     */   public void setRequeststatus(String paramString) {
/* 236 */     this.requeststatus = paramString;
/*     */   }
/*     */   
/*     */   public String getSendTime() {
/* 240 */     return this.sendTime;
/*     */   }
/*     */   
/*     */   public void setSendTime(String paramString) {
/* 244 */     this.sendTime = paramString;
/*     */   }
/*     */   
/*     */   public int getRequestlevel() {
/* 248 */     return this.requestlevel;
/*     */   }
/*     */   
/*     */   public void setRequestlevel(int paramInt) {
/* 252 */     this.requestlevel = paramInt;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/todo/RequestStatusObj.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */