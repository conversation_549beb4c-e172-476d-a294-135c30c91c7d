/*    */ package weaver.workflow.request.todo;
/*    */ 
/*    */ 
/*    */ 
/*    */ public class OfsSettingObject
/*    */ {
/*  7 */   private int isuse = 0;
/*  8 */   private String showsysname = "0";
/*  9 */   private String oashortname = "";
/* 10 */   private String oafullname = "";
/* 11 */   private String showdone = "";
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public int getIsuse() {
/* 18 */     return this.isuse;
/*    */   }
/*    */   
/*    */   public void setIsuse(int paramInt) {
/* 22 */     this.isuse = paramInt;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getShowsysname() {
/* 30 */     return this.showsysname;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public void setShowsysname(String paramString) {
/* 38 */     this.showsysname = paramString;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getOashortname() {
/* 46 */     return this.oashortname;
/*    */   }
/*    */   
/*    */   public void setOashortname(String paramString) {
/* 50 */     this.oashortname = paramString;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getOafullname() {
/* 58 */     return this.oafullname;
/*    */   }
/*    */   
/*    */   public void setOafullname(String paramString) {
/* 62 */     this.oafullname = paramString;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getShowdone() {
/* 70 */     return this.showdone;
/*    */   }
/*    */   
/*    */   public void setShowdone(String paramString) {
/* 74 */     this.showdone = paramString;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/todo/OfsSettingObject.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */