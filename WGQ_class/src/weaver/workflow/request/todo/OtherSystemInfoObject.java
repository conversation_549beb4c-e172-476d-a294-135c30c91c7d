/*     */ package weaver.workflow.request.todo;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class OtherSystemInfoObject
/*     */ {
/*     */   private int sysid;
/*     */   private String syscode;
/*     */   private String sysshortname;
/*     */   private String sysfullname;
/*     */   private String pcprefixurl;
/*     */   private String appprefixurl;
/*     */   private ArrayList<OtherSystemWorkflowObject> OtherSystemWorkflowObjectList;
/*     */   
/*     */   public int getSysid() {
/*  28 */     return this.sysid;
/*     */   }
/*     */   
/*     */   public void setSysid(int paramInt) {
/*  32 */     this.sysid = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getSyscode() {
/*  40 */     return this.syscode;
/*     */   }
/*     */   
/*     */   public void setSyscode(String paramString) {
/*  44 */     this.syscode = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getSysshortname() {
/*  52 */     return this.sysshortname;
/*     */   }
/*     */   
/*     */   public void setSysshortname(String paramString) {
/*  56 */     this.sysshortname = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getSysfullname() {
/*  64 */     return this.sysfullname;
/*     */   }
/*     */   
/*     */   public void setSysfullname(String paramString) {
/*  68 */     this.sysfullname = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getPcprefixurl() {
/*  76 */     return this.pcprefixurl;
/*     */   }
/*     */   
/*     */   public void setPcprefixurl(String paramString) {
/*  80 */     this.pcprefixurl = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getAppprefixurl() {
/*  88 */     return this.appprefixurl;
/*     */   }
/*     */   
/*     */   public void setAppprefixurl(String paramString) {
/*  92 */     this.appprefixurl = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public ArrayList<OtherSystemWorkflowObject> getOtherSystemWorkflowObjectList() {
/* 100 */     return this.OtherSystemWorkflowObjectList;
/*     */   }
/*     */ 
/*     */   
/*     */   public void setOtherSystemWorkflowObjectList(ArrayList<OtherSystemWorkflowObject> paramArrayList) {
/* 105 */     this.OtherSystemWorkflowObjectList = paramArrayList;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/todo/OtherSystemInfoObject.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */