/*     */ package weaver.workflow.request.todo;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import org.apache.commons.logging.Log;
/*     */ import org.apache.commons.logging.LogFactory;
/*     */ import weaver.cache.CacheBase;
/*     */ import weaver.cache.CacheColumn;
/*     */ import weaver.cache.CacheColumnType;
/*     */ import weaver.cache.CacheItem;
/*     */ import weaver.cache.PKColumn;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.ThreadPoolUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.workflow.workflow.WorkflowVersion;
/*     */ 
/*     */ public class OfsSendInfoComInfo
/*     */   extends CacheBase {
/*  21 */   private static final Log log = LogFactory.getLog(OfsSendInfoComInfo.class);
/*     */   
/*  23 */   protected static String TABLE_NAME = "ofs_sendinfo";
/*     */   
/*  25 */   protected static String TABLE_WHERE = null;
/*     */   
/*  27 */   protected static String TABLE_ORDER = null;
/*     */   
/*     */   @PKColumn(type = CacheColumnType.NUMBER)
/*  30 */   protected static String PK_NAME = "id";
/*     */ 
/*     */   
/*     */   @CacheColumn
/*     */   protected static int id;
/*     */   
/*     */   @CacheColumn
/*     */   protected static int syscode;
/*     */   
/*     */   @CacheColumn
/*     */   protected static int sysdesc;
/*     */   
/*     */   @CacheColumn
/*     */   protected static int serverurl;
/*     */   
/*     */   @CacheColumn(isVirtual = true)
/*     */   protected static int workflowidmap;
/*     */   
/*     */   @CacheColumn(isVirtual = true)
/*     */   protected static int useridmap;
/*     */   
/*  51 */   private static StringBuffer tmpSb = new StringBuffer();
/*     */   
/*     */   private static final String ITEM_WF_SQL = "select * from ofs_sendworkflow where mainid=?";
/*     */   
/*     */   private static final String ITEM_RS_SQL = "select * from ofs_senduser where mainid=?";
/*     */   private static final String ENTRY_SQL = "select * from ofs_sendinfo where id = ?";
/*     */   
/*     */   protected void modifyCacheItem(String paramString, CacheItem paramCacheItem) {
/*  59 */     RecordSet recordSet1 = new RecordSet();
/*  60 */     RecordSet recordSet2 = new RecordSet();
/*     */     
/*  62 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  63 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/*     */     
/*  65 */     recordSet2.executeQuery("select * from ofs_sendinfo where id = ?", new Object[] { paramString });
/*  66 */     while (recordSet2.next()) {
/*  67 */       String str = recordSet2.getString("id");
/*     */       
/*  69 */       List<ArrayList<String>> list1 = (List)hashMap1.get(str);
/*  70 */       if (list1 == null) {
/*  71 */         list1 = new ArrayList();
/*  72 */         hashMap1.put(str, list1);
/*     */       } 
/*     */       
/*  75 */       recordSet1.executeQuery("select * from ofs_sendworkflow where mainid=?", new Object[] { str });
/*  76 */       ArrayList<String> arrayList1 = new ArrayList();
/*  77 */       while (recordSet1.next()) {
/*  78 */         String str1 = recordSet1.getString("workflowid");
/*     */         
/*  80 */         String str2 = WorkflowVersion.getAllVersionStringByWFIDs(str1);
/*  81 */         for (String str3 : str2.split(",")) {
/*  82 */           arrayList1.add(str3);
/*     */         }
/*     */       } 
/*  85 */       if (arrayList1.size() > 0) {
/*  86 */         list1.add(arrayList1);
/*     */       }
/*     */       
/*  89 */       List<ArrayList<String>> list2 = (List)hashMap2.get(str);
/*  90 */       if (list2 == null) {
/*  91 */         list2 = new ArrayList();
/*  92 */         hashMap2.put(str, list2);
/*     */       } 
/*  94 */       ArrayList<String> arrayList2 = InitUserid(str);
/*  95 */       if (arrayList2.size() > 0) {
/*  96 */         list2.add(arrayList2);
/*     */       }
/*     */     } 
/*     */ 
/*     */     
/* 101 */     paramCacheItem.set(workflowidmap, hashMap1);
/* 102 */     paramCacheItem.set(useridmap, hashMap2);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<ArrayList<String>> getCValFields(String paramString1, String paramString2) {
/* 115 */     Map map = null;
/* 116 */     if (Util.null2String(paramString2).equals("wf")) {
/* 117 */       map = (Map)getObjValue(workflowidmap, paramString1);
/*     */     } else {
/* 119 */       map = (Map)getObjValue(useridmap, paramString1);
/*     */     } 
/*     */     
/* 122 */     if (map == null) {
/* 123 */       return null;
/*     */     }
/* 125 */     return (List<ArrayList<String>>)map.get(paramString1);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void updateDynamicDataInputCache(String paramString) {
/* 136 */     updateCache(paramString);
/*     */   }
/*     */   
/*     */   public String getSyscode(String paramString) {
/* 140 */     return (String)getValue(syscode, paramString);
/*     */   }
/*     */   
/*     */   public String getSysdesc(String paramString) {
/* 144 */     return (String)getValue(sysdesc, paramString);
/*     */   }
/*     */   
/*     */   public String getServerUrl(String paramString) {
/* 148 */     return (String)getValue(serverurl, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public void removeCache() {
/* 154 */     super.removeCache();
/*     */   }
/*     */   
/*     */   public ArrayList<String> InitUserid(String paramString) {
/* 158 */     RecordSet recordSet1 = new RecordSet();
/* 159 */     RecordSet recordSet2 = new RecordSet();
/* 160 */     ArrayList<String> arrayList = new ArrayList();
/* 161 */     recordSet1.executeQuery("select * from ofs_senduser where mainid=?", new Object[] { paramString });
/* 162 */     while (recordSet1.next()) {
/* 163 */       String str1 = recordSet1.getString("type");
/* 164 */       String str2 = recordSet1.getString("objid");
/* 165 */       String str3 = recordSet1.getString("rolelevel");
/* 166 */       int i = Util.getIntValue(recordSet1.getString("seclevel"), 0);
/* 167 */       int j = Util.getIntValue(recordSet1.getString("seclevel1"), 100);
/* 168 */       if (str1.equals("1")) {
/* 169 */         if (!arrayList.contains(str2))
/* 170 */           arrayList.add(str2);  continue;
/*     */       } 
/* 172 */       if (str1.equals("2")) {
/* 173 */         recordSet2.executeQuery("select id from hrmresource where status in (0,1,2,3) and subcompanyid1=? and seclevel>=? and seclevel<=?", new Object[] { str2, Integer.valueOf(i), Integer.valueOf(j) });
/* 174 */         while (recordSet2.next()) {
/* 175 */           String str = recordSet2.getString(1);
/* 176 */           if (!arrayList.contains(str))
/* 177 */             arrayList.add(str); 
/*     */         }  continue;
/*     */       } 
/* 180 */       if (str1.equals("3")) {
/* 181 */         recordSet2.executeQuery("select id from hrmresource where status in (0,1,2,3) and departmentid=? and seclevel>=? and seclevel<=?", new Object[] { str2, Integer.valueOf(i), Integer.valueOf(j) });
/* 182 */         while (recordSet2.next()) {
/* 183 */           String str = recordSet2.getString(1);
/* 184 */           if (!arrayList.contains(str))
/* 185 */             arrayList.add(str); 
/*     */         }  continue;
/*     */       } 
/* 188 */       if (str1.equals("4")) {
/* 189 */         recordSet2.executeQuery("select h.id from hrmresource h,hrmrolemembers r where h.id = r.resourceid and r.roleid=? and r.rolelevel=? and h.status in (0,1,2,3) and h.seclevel>=? and h.seclevel<=?", new Object[] { str2, str3, Integer.valueOf(i), Integer.valueOf(j) });
/* 190 */         while (recordSet2.next()) {
/* 191 */           String str = recordSet2.getString(1);
/* 192 */           if (!arrayList.contains(str))
/* 193 */             arrayList.add(str); 
/*     */         }  continue;
/*     */       } 
/* 196 */       if (str1.equals("5")) {
/* 197 */         recordSet2.executeQuery("select id from hrmresource where status in (0,1,2,3) and seclevel>=? and seclevel<=?", new Object[] { Integer.valueOf(i), Integer.valueOf(j) });
/* 198 */         while (recordSet2.next()) {
/* 199 */           String str = recordSet2.getString(1);
/* 200 */           if (!arrayList.contains(str)) {
/* 201 */             arrayList.add(str);
/*     */           }
/*     */         } 
/*     */       } 
/*     */     } 
/*     */     
/* 207 */     return arrayList;
/*     */   }
/*     */   
/*     */   public void SyncCache() {
/* 211 */     ThreadPoolUtil.getThreadPool("OFSSENDINFO", "5").execute(new Runnable() {
/*     */           public void run() {}
/*     */         });
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/todo/OfsSendInfoComInfo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */