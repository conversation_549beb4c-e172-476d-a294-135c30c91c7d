/*    */ package weaver.workflow.request.todo;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class OtherSystemWorkflowObject
/*    */ {
/*    */   private int sysid;
/*    */   private int workflowid;
/*    */   private String workflowname;
/*    */   
/*    */   public int getSysid() {
/* 21 */     return this.sysid;
/*    */   }
/*    */   public void setSysid(int paramInt) {
/* 24 */     this.sysid = paramInt;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public int getWorkflowid() {
/* 32 */     return this.workflowid;
/*    */   }
/*    */   public void setWorkflowid(int paramInt) {
/* 35 */     this.workflowid = paramInt;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getWorkflowname() {
/* 43 */     return this.workflowname;
/*    */   }
/*    */   public void setWorkflowname(String paramString) {
/* 46 */     this.workflowname = paramString;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/todo/OtherSystemWorkflowObject.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */