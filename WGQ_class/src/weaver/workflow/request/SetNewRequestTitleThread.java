/*    */ package weaver.workflow.request;
/*    */ 
/*    */ import com.engine.workflow.entity.core.RequestInfoEntity;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class SetNewRequestTitleThread
/*    */   implements Runnable
/*    */ {
/*    */   private RequestInfoEntity infoEntity;
/*    */   private int userlang;
/*    */   
/*    */   public SetNewRequestTitleThread(RequestInfoEntity paramRequestInfoEntity, int paramInt) {
/* 15 */     this.infoEntity = paramRequestInfoEntity;
/* 16 */     this.userlang = paramInt;
/*    */   }
/*    */ 
/*    */   
/*    */   public void run() {
/* 21 */     if (this.infoEntity != null) {
/* 22 */       (new SetNewRequestTitle()).getAllRequestName(this.infoEntity, this.userlang);
/*    */     }
/*    */   }
/*    */   
/*    */   public int getUserlang() {
/* 27 */     return this.userlang;
/*    */   }
/*    */   
/*    */   public void setUserlang(int paramInt) {
/* 31 */     this.userlang = paramInt;
/*    */   }
/*    */   
/*    */   public RequestInfoEntity getInfoEntity() {
/* 35 */     return this.infoEntity;
/*    */   }
/*    */   
/*    */   public void setInfoEntity(RequestInfoEntity paramRequestInfoEntity) {
/* 39 */     this.infoEntity = paramRequestInfoEntity;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/SetNewRequestTitleThread.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */