/*     */ package weaver.workflow.request;
/*     */ 
/*     */ import com.engine.workflow.entity.core.RequestInfoEntity;
/*     */ import com.engine.workflow.entity.core.WorkflowBaseInfoEntity;
/*     */ import com.engine.workflow.util.WfListCusTitleUtil;
/*     */ import java.io.FileOutputStream;
/*     */ import java.util.Properties;
/*     */ import weaver.conn.ConnStatement;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.file.Prop;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.GCONST;
/*     */ import weaver.general.ThreadVarLanguage;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.workflow.workflow.WorkflowConfigComInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class SetNewRequestTitle
/*     */   extends BaseBean
/*     */ {
/*     */   public void getAllRequestName(RecordSet paramRecordSet, String paramString1, String paramString2, String paramString3, String paramString4, int paramInt1, int paramInt2, int paramInt3) {
/*  32 */     RequestInfoEntity requestInfoEntity = new RequestInfoEntity();
/*  33 */     requestInfoEntity.setRequestId(paramString1);
/*  34 */     requestInfoEntity.setRequestName(paramString2);
/*  35 */     requestInfoEntity.setWorkflowId(Util.getIntValue(paramString3));
/*  36 */     requestInfoEntity.setCurrentNodeId(paramString4);
/*  37 */     WorkflowBaseInfoEntity workflowBaseInfoEntity = new WorkflowBaseInfoEntity();
/*  38 */     workflowBaseInfoEntity.setFormId(String.valueOf(paramInt1));
/*  39 */     workflowBaseInfoEntity.setIsBill(String.valueOf(paramInt2));
/*  40 */     requestInfoEntity.setWorkflowBaseInfo(workflowBaseInfoEntity);
/*  41 */     WorkflowConfigComInfo workflowConfigComInfo = new WorkflowConfigComInfo();
/*  42 */     if ("1".equals(workflowConfigComInfo.getValue("req_async_updaterequestname"))) {
/*  43 */       WFAutoApproveThreadPoolUtil.getFixedThreadPool().execute(new SetNewRequestTitleThread(requestInfoEntity, paramInt3));
/*     */     } else {
/*  45 */       getAllRequestName(requestInfoEntity, paramInt3);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void getAllRequestName(RequestInfoEntity paramRequestInfoEntity, int paramInt) {
/*  54 */     String str1 = paramRequestInfoEntity.getRequestId();
/*  55 */     String str2 = paramRequestInfoEntity.getRequestName();
/*  56 */     String str3 = Util.null2String(Integer.valueOf(paramRequestInfoEntity.getWorkflowId()));
/*  57 */     String str4 = paramRequestInfoEntity.getCurrentNodeId();
/*  58 */     WorkflowBaseInfoEntity workflowBaseInfoEntity = paramRequestInfoEntity.getWorkflowBaseInfo();
/*  59 */     int i = Util.getIntValue(workflowBaseInfoEntity.getFormId());
/*  60 */     int j = Util.getIntValue(workflowBaseInfoEntity.getIsBill());
/*  61 */     RecordSet recordSet = new RecordSet();
/*  62 */     String str5 = str2;
/*  63 */     ConnStatement connStatement = new ConnStatement();
/*     */     try {
/*  65 */       if (null == recordSet) {
/*  66 */         recordSet = new RecordSet();
/*     */       }
/*     */ 
/*     */       
/*  70 */       RequestBaseUtil requestBaseUtil = new RequestBaseUtil();
/*  71 */       str5 = requestBaseUtil.formatRequestname(str2, str3, str1, j, i, paramInt);
/*  72 */       String str = (new WfListCusTitleUtil()).getNewRequestName(Util.getIntValue(str3), str5);
/*     */ 
/*     */       
/*  75 */       connStatement.setStatementSql("update workflow_requestbase set requestnamenew = ?,requestnamehtmlnew = ? where requestid = " + str1);
/*  76 */       connStatement.setString(1, str);
/*  77 */       connStatement.setString(2, str5);
/*  78 */       connStatement.executeUpdate();
/*  79 */       connStatement.close();
/*     */     }
/*  81 */     catch (Exception exception) {
/*  82 */       writeLog(exception);
/*     */     } finally {
/*  84 */       connStatement.close();
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void SyncRequestNewName(int paramInt1, int paramInt2, int paramInt3, int paramInt4) {
/*  96 */     RecordSet recordSet1 = new RecordSet();
/*  97 */     RecordSet recordSet2 = new RecordSet();
/*  98 */     int i = paramInt2;
/*  99 */     int j = paramInt3;
/*     */     
/*     */     try {
/* 102 */       recordSet1.executeSql("select requestid,requestname,currentnodeid from workflow_requestbase where workflowid=" + paramInt1 + " order by requestid");
/* 103 */       while (recordSet1.next()) {
/* 104 */         String str1 = recordSet1.getString("requestid");
/* 105 */         String str2 = recordSet1.getString("requestname");
/* 106 */         String str3 = recordSet1.getString("currentnodeid");
/* 107 */         getAllRequestName(recordSet2, str1 + "", str2, paramInt1 + "", str3 + "", i, j, paramInt4);
/*     */       } 
/* 109 */       recordSet1.executeSql("update workflow_base set isupdatetitle=0 where id=" + paramInt1);
/* 110 */     } catch (Exception exception) {
/* 111 */       writeLog(exception);
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   public synchronized boolean setPropValue(String paramString1, String paramString2, String paramString3) {
/* 117 */     Properties properties = Prop.loadTemplateProp(paramString1);
/*     */     try {
/* 119 */       properties.setProperty(paramString2, paramString3);
/* 120 */       FileOutputStream fileOutputStream = new FileOutputStream(GCONST.getPropertyPath() + paramString1 + ".properties");
/* 121 */       properties.store(fileOutputStream, new String(("#" + SystemEnv.getHtmlLabelName(10004326, ThreadVarLanguage.getLang()) + " 1:" + SystemEnv.getHtmlLabelName(10004327, ThreadVarLanguage.getLang()) + "").getBytes(), "ISO-8859-1"));
/* 122 */       return true;
/* 123 */     } catch (Exception exception) {
/* 124 */       return false;
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/SetNewRequestTitle.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */