/*     */ package weaver.workflow.request;
/*     */ 
/*     */ import weaver.hrm.User;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class ShareConditionInfo
/*     */ {
/*  17 */   private String method = "";
/*     */   
/*  19 */   private String docid = "";
/*     */   
/*  21 */   private String prjid = "";
/*     */   
/*  23 */   private String relatedshareid = "";
/*     */   
/*  25 */   private String rolelevel = "";
/*     */   
/*  27 */   private String seclevel = "";
/*     */   
/*  29 */   private String sharelevel = "";
/*     */   
/*  31 */   private String shareType = "";
/*     */   
/*  33 */   private String userids = "";
/*     */   
/*  35 */   private String subcompanyid = "";
/*     */   
/*  37 */   private String departmentid = "";
/*     */   
/*  39 */   private String roleid = "";
/*     */   
/*  41 */   private String forAllUser = "";
/*     */   
/*  43 */   private String crmid = "";
/*     */   
/*  45 */   private String orgGroupId = "";
/*     */   
/*  47 */   private int sharecrm = 0;
/*     */   
/*  49 */   private int usertype = 0;
/*     */   
/*  51 */   private int theusertype = 0;
/*     */   
/*  53 */   private int userid = 0;
/*     */   
/*  55 */   private int nodeid = 0;
/*     */   
/*  57 */   private int requestid = 0;
/*     */   
/*     */   private User user;
/*     */   
/*  61 */   private String billtablename = "";
/*     */   
/*  63 */   private int nextnodeid = 0;
/*     */   
/*  65 */   private int nownodeid = 0;
/*     */   
/*  67 */   private int isbill = 0;
/*  68 */   private String downloadlevel = "";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getNodeid() {
/*  76 */     return this.nodeid;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setNodeid(int paramInt) {
/*  86 */     this.nodeid = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getUserid() {
/*  95 */     return this.userid;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setUserid(int paramInt) {
/* 105 */     this.userid = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getTheusertype() {
/* 114 */     return this.theusertype;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setTheusertype(int paramInt) {
/* 124 */     this.theusertype = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getUsertype() {
/* 133 */     return this.usertype;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setUsertype(int paramInt) {
/* 143 */     this.usertype = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getSharecrm() {
/* 152 */     return this.sharecrm;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setSharecrm(int paramInt) {
/* 162 */     this.sharecrm = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCrmid() {
/* 171 */     return this.crmid;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setCrmid(String paramString) {
/* 181 */     this.crmid = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getOrgGroupId() {
/* 190 */     return this.orgGroupId;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setOrgGroupId(String paramString) {
/* 200 */     this.orgGroupId = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getForAllUser() {
/* 209 */     return this.forAllUser;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setForAllUser(String paramString) {
/* 219 */     this.forAllUser = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getPrjid() {
/* 228 */     return this.prjid;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setPrjid(String paramString) {
/* 238 */     this.prjid = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getDepartmentid() {
/* 247 */     return this.departmentid;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setDepartmentid(String paramString) {
/* 256 */     this.departmentid = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getRoleid() {
/* 265 */     return this.roleid;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setRoleid(String paramString) {
/* 274 */     this.roleid = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getShareType() {
/* 283 */     return this.shareType;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setShareType(String paramString) {
/* 293 */     this.shareType = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getSubcompanyid() {
/* 302 */     return this.subcompanyid;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setSubcompanyid(String paramString) {
/* 312 */     this.subcompanyid = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getUserids() {
/* 321 */     return this.userids;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setUserids(String paramString) {
/* 331 */     this.userids = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getDocid() {
/* 340 */     return this.docid;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setDocid(String paramString) {
/* 350 */     this.docid = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getMethod() {
/* 359 */     return this.method;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setMethod(String paramString) {
/* 369 */     this.method = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getRelatedshareid() {
/* 378 */     return this.relatedshareid;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setRelatedshareid(String paramString) {
/* 388 */     this.relatedshareid = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getRolelevel() {
/* 397 */     return this.rolelevel;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setRolelevel(String paramString) {
/* 407 */     this.rolelevel = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getSeclevel() {
/* 416 */     return this.seclevel;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setSeclevel(String paramString) {
/* 426 */     this.seclevel = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getSharelevel() {
/* 435 */     return this.sharelevel;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setSharelevel(String paramString) {
/* 445 */     this.sharelevel = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getBilltablename() {
/* 454 */     return this.billtablename;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setBilltablename(String paramString) {
/* 464 */     this.billtablename = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getIsbill() {
/* 473 */     return this.isbill;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setIsbill(int paramInt) {
/* 483 */     this.isbill = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getNextnodeid() {
/* 492 */     return this.nextnodeid;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setNextnodeid(int paramInt) {
/* 502 */     this.nextnodeid = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getNownodeid() {
/* 511 */     return this.nownodeid;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setNownodeid(int paramInt) {
/* 521 */     this.nownodeid = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getRequestid() {
/* 530 */     return this.requestid;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setRequestid(int paramInt) {
/* 540 */     this.requestid = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public User getUser() {
/* 549 */     return this.user;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setUser(User paramUser) {
/* 559 */     this.user = paramUser;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getDownloadlevel() {
/* 567 */     return this.downloadlevel;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setDownloadlevel(String paramString) {
/* 576 */     this.downloadlevel = paramString;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/ShareConditionInfo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */