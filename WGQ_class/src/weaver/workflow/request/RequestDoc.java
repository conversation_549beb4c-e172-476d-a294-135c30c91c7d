/*      */ package weaver.workflow.request;
/*      */ 
/*      */ import com.api.doc.search.service.OperateService;
/*      */ import com.api.odoc.service.RequestDocService;
/*      */ import com.api.odoc.util.ODocTHWithOpinionUtil;
/*      */ import com.api.odoc.util.OdocFileUtil;
/*      */ import com.engine.hrm.biz.HrmClassifiedProtectionBiz;
/*      */ import com.engine.hrm.biz.OrganizationShowSetBiz;
/*      */ import com.engine.workflow.biz.requestForm.RequestSecLevelBiz;
/*      */ import com.google.common.base.Strings;
/*      */ import java.io.Serializable;
/*      */ import java.io.UnsupportedEncodingException;
/*      */ import java.net.URLDecoder;
/*      */ import java.util.ArrayList;
/*      */ import java.util.HashMap;
/*      */ import java.util.List;
/*      */ import java.util.Map;
/*      */ import javax.servlet.http.HttpServletRequest;
/*      */ import org.apache.commons.lang.StringUtils;
/*      */ import weaver.conn.RecordSet;
/*      */ import weaver.cpt.capital.CapitalComInfo;
/*      */ import weaver.crm.Maint.CustomerInfoComInfo;
/*      */ import weaver.docs.docs.DocCoder;
/*      */ import weaver.docs.docs.DocComInfo;
/*      */ import weaver.docs.docs.DocManager;
/*      */ import weaver.docs.docs.DocViewer;
/*      */ import weaver.docs.senddoc.DocReceiveUnitComInfo;
/*      */ import weaver.docs.util.DocTriggerUtils;
/*      */ import weaver.file.FileUpload;
/*      */ import weaver.formmode.tree.CustomTreeUtil;
/*      */ import weaver.general.BaseBean;
/*      */ import weaver.general.StaticObj;
/*      */ import weaver.general.StringUtil;
/*      */ import weaver.general.Util;
/*      */ import weaver.hrm.User;
/*      */ import weaver.hrm.company.DepartmentComInfo;
/*      */ import weaver.hrm.company.SubCompanyComInfo;
/*      */ import weaver.hrm.companyvirtual.DepartmentVirtualComInfo;
/*      */ import weaver.hrm.companyvirtual.SubCompanyVirtualComInfo;
/*      */ import weaver.hrm.resource.ResourceComInfo;
/*      */ import weaver.interfaces.workflow.browser.Browser;
/*      */ import weaver.interfaces.workflow.browser.BrowserBean;
/*      */ import weaver.proj.Maint.ProjectInfoComInfo;
/*      */ import weaver.systeminfo.SystemEnv;
/*      */ import weaver.workflow.field.BrowserComInfo;
/*      */ import weaver.workflow.workflow.WFModeNodeFieldManager;
/*      */ import weaver.workflow.workflow.WorkflowRequestComInfo;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ public class RequestDoc
/*      */   extends BaseBean
/*      */   implements Serializable
/*      */ {
/*      */   private static final long serialVersionUID = 4299061978831668305L;
/*   68 */   private ArrayList valueAndMoudle = new ArrayList(); private User user;
/*      */   
/*      */   public RequestDoc() throws Exception {
/*   71 */     this.user = null;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setUser(User paramUser) {
/*   78 */     this.user = paramUser;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public ArrayList getDocFiled(String paramString) {
/*   87 */     ArrayList<String> arrayList = new ArrayList();
/*   88 */     RecordSet recordSet = new RecordSet();
/*   89 */     String str = "select * from workflow_createdoc where status='1' and workflowId=?";
/*   90 */     recordSet.executeQuery(str, new Object[] { paramString });
/*   91 */     recordSet.writeLog("sql=[" + str + "],workflowid=" + paramString);
/*   92 */     if (recordSet.next()) {
/*   93 */       arrayList.add(recordSet.getString("flowCodeField"));
/*   94 */       arrayList.add(recordSet.getString("flowDocField"));
/*   95 */       arrayList.add(recordSet.getString("defaultView"));
/*   96 */       arrayList.add(recordSet.getString("flowDocCatField"));
/*   97 */       arrayList.add(recordSet.getString("documentTitleField"));
/*   98 */       arrayList.add(recordSet.getString("newTextNodes"));
/*   99 */       arrayList.add(recordSet.getString("isWorkflowDraft"));
/*  100 */       arrayList.add(recordSet.getString("defaultDocType"));
/*  101 */       arrayList.add("" + Util.getIntValue(recordSet.getString("extfile2doc"), 0));
/*  102 */       arrayList.add(Util.null2String(recordSet.getString("flowattachfiled")));
/*  103 */       arrayList.add(Util.null2String(recordSet.getString("wfAttachKeep")));
/*  104 */       arrayList.add(Util.null2String(recordSet.getString("showmouldfield")));
/*      */     } 
/*  106 */     return arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean haveDocFiled(String paramString1, String paramString2) {
/*  116 */     boolean bool = false;
/*  117 */     RecordSet recordSet = new RecordSet();
/*  118 */     ArrayList<String> arrayList = getDocFiled(paramString1);
/*  119 */     if (arrayList != null && arrayList.size() > 0) {
/*  120 */       String str = "" + arrayList.get(1);
/*  121 */       WFModeNodeFieldManager wFModeNodeFieldManager = new WFModeNodeFieldManager();
/*  122 */       boolean bool1 = wFModeNodeFieldManager.getIsModeByWorkflowIdAndNodeId(Util.getIntValue(paramString1, 0), Util.getIntValue(paramString2, 0));
/*  123 */       if (StringUtils.isEmpty(str)) {
/*  124 */         return bool;
/*      */       }
/*  126 */       if (bool1) {
/*  127 */         recordSet.execute("select isview,isedit from workflow_modeview b where b.nodeid=" + paramString2 + " and fieldid=" + str);
/*      */       } else {
/*  129 */         recordSet.execute("select isview,isedit from workflow_nodeform b where b.nodeid=" + paramString2 + " and fieldid=" + str);
/*      */       } 
/*  131 */       if (recordSet.next() && (
/*  132 */         recordSet.getString(1).equals("1") || recordSet.getString(2).equals("1"))) {
/*  133 */         bool = true;
/*      */       }
/*      */     } else {
/*      */       
/*  137 */       bool = false;
/*      */     } 
/*  139 */     return bool;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean haveCreateDoc(String paramString) {
/*  148 */     boolean bool = false;
/*  149 */     int i = getRequestDocid(paramString);
/*  150 */     if (i > 0) {
/*  151 */       bool = true;
/*      */     }
/*  153 */     return bool;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public int getRequestDocid(String paramString) {
/*  162 */     int i = -1;
/*  163 */     RequestDocService requestDocService = new RequestDocService();
/*  164 */     String str = requestDocService.getOdocDocidSql(Util.getIntValue(paramString), "flowdocfield");
/*  165 */     RecordSet recordSet = new RecordSet();
/*  166 */     recordSet.executeQuery(str, new Object[0]);
/*  167 */     if (recordSet.next()) {
/*  168 */       String str1 = Util.null2String(recordSet.getString("fieldname"));
/*  169 */       String str2 = Util.null2String(recordSet.getString("tablename"));
/*  170 */       if (!"".equals(str1) && !"".equals(str2)) {
/*  171 */         recordSet.executeQuery("SELECT " + str1 + " FROM " + str2 + " WHERE REQUESTID=?", new Object[] { paramString });
/*  172 */         if (recordSet.next()) {
/*  173 */           i = Util.getIntValue(Util.null2String(recordSet.getString(str1)), -1);
/*      */         }
/*      */       } 
/*      */     } 
/*  177 */     return i;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public ArrayList getRequestToModul() {
/*  186 */     return this.valueAndMoudle;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setRequestToModul() {
/*  193 */     if (this.valueAndMoudle == null) {
/*  194 */       this.valueAndMoudle = new ArrayList();
/*      */     } else {
/*  196 */       this.valueAndMoudle.clear();
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setRequestToModul(String paramString1, String paramString2, int paramInt) throws Exception {
/*  205 */     setRequestToModul(paramString1, paramString2, paramInt, true, -1, -1);
/*      */   }
/*      */   
/*      */   public void setRequestToModul(String paramString1, String paramString2, int paramInt1, boolean paramBoolean, int paramInt2, int paramInt3, Map paramMap) throws Exception {
/*  209 */     RecordSet recordSet1 = new RecordSet();
/*  210 */     RecordSet recordSet2 = new RecordSet();
/*  211 */     ArrayList<String> arrayList = getDocFiled(paramString1);
/*      */     
/*  213 */     if (arrayList != null && arrayList.size() > 0) {
/*      */       
/*  215 */       ArrayList<String> arrayList1 = new ArrayList();
/*  216 */       ArrayList<String> arrayList2 = new ArrayList();
/*  217 */       ArrayList<String> arrayList3 = new ArrayList();
/*  218 */       ArrayList<String> arrayList4 = new ArrayList();
/*  219 */       ArrayList<String> arrayList5 = new ArrayList();
/*  220 */       ArrayList<String> arrayList6 = new ArrayList();
/*  221 */       ArrayList<String> arrayList7 = new ArrayList();
/*  222 */       ArrayList<String> arrayList8 = new ArrayList();
/*  223 */       ArrayList<String> arrayList9 = new ArrayList();
/*  224 */       ArrayList<String> arrayList10 = new ArrayList();
/*  225 */       ArrayList<String> arrayList11 = new ArrayList();
/*      */       
/*  227 */       String str1 = "";
/*      */ 
/*      */ 
/*      */       
/*  231 */       int i = 0;
/*  232 */       String str2 = "0";
/*  233 */       recordSet1.executeSql(" select formId,isBill from workflow_base where id= " + paramString1);
/*  234 */       if (recordSet1.next()) {
/*  235 */         i = Util.getIntValue(recordSet1.getString("formId"), 0);
/*  236 */         str2 = Util.null2String(recordSet1.getString("isBill"));
/*      */       } 
/*      */       
/*  239 */       if (!str2.equals("1")) {
/*  240 */         str2 = "0";
/*      */       }
/*      */       
/*  243 */       String str3 = "";
/*  244 */       int j = -1;
/*  245 */       String str4 = "" + arrayList.get(3);
/*      */       
/*  247 */       String str5 = null;
/*  248 */       String str6 = "workflow_form";
/*  249 */       if (str2.equals("1")) {
/*  250 */         recordSet1.executeSql("select tablename from workflow_bill where id = " + i);
/*  251 */         if (recordSet1.next()) {
/*  252 */           str6 = Util.null2String(recordSet1.getString("tablename"));
/*      */         }
/*      */       } 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*  277 */       if (paramBoolean || paramInt2 <= 0) {
/*      */ 
/*      */         
/*  280 */         str5 = "select * from workflow_docshow where flowid=" + paramString1 + " and ((fieldid<>-1) or(workflowContentType =2)) and (fieldid in (select a.id as fieldid from WORKFLOW_BILLFIELD a,workflow_base b where b.formid=a.billid and b.id=" + paramString1 + ") or fieldid=-1 or fieldid=-3) order by fieldId";
/*      */       }
/*      */       else {
/*      */         
/*  284 */         if (str2.equals("1")) {
/*  285 */           recordSet1.executeQuery("select fieldname,fieldhtmltype,type from workflow_billfield where id = (select distinct showmouldfield from workflow_createdoc where workflowid = ?)", new Object[] { paramString1 });
/*      */         } else {
/*  287 */           recordSet1.executeQuery("select fieldname,fieldhtmltype,type from workflow_formdict where id = (select distinct showmouldfield from workflow_createdoc where workflowid = ?)", new Object[] { paramString1 });
/*      */         } 
/*  289 */         if (recordSet1.next()) {
/*  290 */           str5 = "select " + recordSet1.getString(1) + " from " + str6 + " where requestid=" + paramString2;
/*  291 */           recordSet2 = new RecordSet();
/*  292 */           recordSet2.execute(str5);
/*  293 */           if (recordSet2.next()) {
/*  294 */             String str = Util.null2String(recordSet2.getString(1));
/*  295 */             if (paramMap != null) {
/*  296 */               str = Util.null2String(paramMap.get("field" + paramMap.get("showMouldFieldid")));
/*      */             }
/*  298 */             (new BaseBean()).writeLog("setRequestToModul  fieldValue = " + str);
/*      */             
/*  300 */             if (StringUtil.isEmpty(str)) {
/*  301 */               j = -1;
/*      */             } else {
/*  303 */               j = Util.getIntValue(str);
/*      */               
/*  305 */               int m = recordSet1.getInt("fieldhtmltype");
/*  306 */               int n = recordSet1.getInt("type");
/*  307 */               if (m == 3) {
/*  308 */                 if (n == 52 || n == 285) {
/*  309 */                   j += 100;
/*  310 */                 } else if (n == 324) {
/*  311 */                   j = -1 * j - 100;
/*      */                 } 
/*      */               }
/*      */             } 
/*      */ 
/*      */ 
/*      */             
/*  318 */             (new BaseBean()).writeLog("setRequestToModul  selectValue = " + j);
/*      */             
/*  320 */             str5 = "select * from workflow_docshow where flowId=" + paramString1 + " and ((fieldid<>-1) or(workflowContentType =2)) and selectItemId =" + j + " and docmouldid=" + paramInt2 + " order by fieldId";
/*  321 */             recordSet1.execute(str5);
/*  322 */             if (!recordSet1.next()) {
/*  323 */               str5 = "select * from workflow_docshow where flowId=" + paramString1 + " and ((fieldid<>-1) or(workflowContentType =2))  and selectItemId=-1 order by fieldId";
/*      */             }
/*      */           } 
/*      */         } else {
/*  327 */           str5 = " select * from workflow_docshow where flowId=" + paramString1 + " and ((fieldid<>-1) or(workflowContentType =2)) and selectItemId=-1 order by fieldId";
/*      */         } 
/*  329 */         (new BaseBean()).writeLog("setRequestToModul  sql = " + str5);
/*      */       } 
/*      */ 
/*      */ 
/*      */       
/*  334 */       ArrayList<String> arrayList12 = new ArrayList();
/*  335 */       String str7 = null;
/*  336 */       String str8 = null;
/*  337 */       String str9 = null;
/*  338 */       String str10 = null;
/*  339 */       String str11 = null;
/*  340 */       String str12 = null;
/*  341 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  342 */       HashMap<Object, Object> hashMap2 = null;
/*  343 */       ArrayList<HashMap<Object, Object>> arrayList13 = new ArrayList();
/*      */       
/*  345 */       recordSet1.execute(str5);
/*  346 */       while (recordSet1.next()) {
/*  347 */         arrayList1.add(recordSet1.getString("modulId"));
/*  348 */         str3 = str3 + recordSet1.getString("fieldId") + ",";
/*  349 */         arrayList2.add(recordSet1.getString("docmouldid"));
/*  350 */         arrayList12.add(recordSet1.getString("fieldId"));
/*  351 */         arrayList7.add(Util.null2String(recordSet1.getString("dateShowType")));
/*  352 */         arrayList8.add(Util.null2String(recordSet1.getString("workflowContentType")));
/*  353 */         arrayList9.add(Util.null2String(recordSet1.getString("nodeid")));
/*  354 */         arrayList11.add(Util.null2String(recordSet1.getString("selectItemId")));
/*      */       } 
/*  356 */       str3 = str3 + "-100";
/*  357 */       if (!str3.equals("")) {
/*  358 */         if (str2.equals("1")) {
/*  359 */           str5 = "select a.fieldname,a.fieldhtmltype,a.type, a.id,a.fielddbtype  from workflow_billfield a where (" + Util.getSubINClause(str3, "a.id", "in") + ") and billid=" + i + " order by id";
/*      */         } else {
/*  361 */           str5 = "select a.fieldname,a.fieldhtmltype,a.type, a.id,a.fielddbtype  from workflow_formdict a where (" + Util.getSubINClause(str3, "a.id", "in") + ") order by id";
/*      */         } 
/*      */       }
/*  364 */       recordSet1.execute(str5);
/*  365 */       int k = 0;
/*  366 */       while (recordSet1.next()) {
/*  367 */         str8 = recordSet1.getString(1);
/*  368 */         str9 = recordSet1.getString(2);
/*  369 */         str10 = recordSet1.getString(3);
/*  370 */         str11 = recordSet1.getString(4);
/*  371 */         str12 = recordSet1.getString(5);
/*      */         
/*  373 */         if (str11 != null && !str11.trim().equals("")) {
/*  374 */           hashMap1.put(str11, String.valueOf(k));
/*      */         }
/*      */         
/*  377 */         hashMap2 = new HashMap<>();
/*  378 */         hashMap2.put("tempFieldName", str8);
/*  379 */         hashMap2.put("tempFieldHtmlType", str9);
/*  380 */         hashMap2.put("tempType", str10);
/*  381 */         hashMap2.put("tempId", str11);
/*  382 */         hashMap2.put("tempFieldDbType", str12);
/*      */         
/*  384 */         arrayList13.add(hashMap2);
/*      */         
/*  386 */         k++;
/*      */       } 
/*      */       
/*  389 */       for (byte b = 0; b < arrayList12.size(); b++) {
/*  390 */         str7 = arrayList12.get(b);
/*  391 */         k = Util.getIntValue((String)hashMap1.get(str7), 0);
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */         
/*  399 */         if (arrayList13.size() > 0) {
/*  400 */           Map map = arrayList13.get(k);
/*  401 */           str8 = (String)map.get("tempFieldName");
/*  402 */           str9 = (String)map.get("tempFieldHtmlType");
/*  403 */           str10 = (String)map.get("tempType");
/*  404 */           str11 = (String)map.get("tempId");
/*  405 */           str12 = (String)map.get("tempFieldDbType");
/*      */         } else {
/*  407 */           str8 = "1";
/*  408 */           str9 = "";
/*  409 */           str10 = "";
/*  410 */           str11 = "";
/*  411 */           str12 = "";
/*      */         } 
/*      */         
/*  414 */         if ("-3".equals(str7)) {
/*  415 */           str11 = "-3";
/*      */         }
/*  417 */         if ("-10".equals(str7)) {
/*  418 */           str11 = "-10";
/*      */         }
/*      */         
/*  421 */         arrayList4.add(str9);
/*  422 */         arrayList5.add(str10);
/*  423 */         arrayList6.add(str11);
/*  424 */         arrayList10.add(str12);
/*  425 */         if (str1.equals("")) {
/*  426 */           str1 = str8;
/*      */         } else {
/*  428 */           str1 = str1 + "," + str8;
/*      */         } 
/*      */       } 
/*      */       
/*  432 */       if (!str1.equals("")) {
/*  433 */         String str = "";
/*  434 */         recordSet1.executeSql("select requestName from workflow_requestbase where requestId=" + paramString2);
/*  435 */         if (recordSet1.next()) {
/*  436 */           str = Util.null2String(recordSet1.getString("requestName"));
/*      */         }
/*  438 */         str5 = "select " + str1 + " from " + str6 + " where requestid=" + paramString2;
/*  439 */         recordSet1.execute(str5);
/*  440 */         if (recordSet1.next()) {
/*  441 */           for (byte b1 = 0; b1 < arrayList1.size(); b1++) {
/*  442 */             String str13 = arrayList4.get(b1);
/*  443 */             String str14 = arrayList5.get(b1);
/*  444 */             String str15 = arrayList6.get(b1);
/*  445 */             String str16 = arrayList7.get(b1);
/*  446 */             String str17 = arrayList8.get(b1);
/*  447 */             String str18 = arrayList9.get(b1);
/*  448 */             String str19 = arrayList2.get(b1);
/*  449 */             String str20 = arrayList11.get(b1);
/*  450 */             String str21 = arrayList10.get(b1);
/*  451 */             String str22 = arrayList1.get(b1);
/*  452 */             String str23 = "";
/*      */ 
/*      */             
/*  455 */             if (str17.equals("1") || str17.equals("")) {
/*  456 */               str23 = recordSet1.getString(b1 + 1);
/*  457 */               str23 = convertValueToFormValue(str15, str23, paramMap);
/*  458 */               if ("-3".equals(str15)) {
/*  459 */                 str23 = convertValueToFormValue("-1", str, paramMap);
/*  460 */               } else if ("-10".equals(str15)) {
/*  461 */                 Map map = (new RequestSecLevelBiz()).getSecInfoByRequestId(Util.getIntValue(paramString2), this.user);
/*  462 */                 String str24 = Util.null2String(map.get("secLevel"));
/*  463 */                 String str25 = Util.null2String(map.get("secValidity"));
/*      */                 
/*  465 */                 String str26 = (new HrmClassifiedProtectionBiz()).getResourceSecLevelShowName(str24 + "", paramInt1 + "");
/*  466 */                 str23 = ("1".equals(str16) || "".equals(str25)) ? str26 : (str26 + "★" + str25);
/*  467 */               } else if ("2".equals(str13) && "2".equals(str14)) {
/*  468 */                 str23 = recordSet1.getString(b1 + 1);
/*  469 */                 str23 = convertValueToFormValue(str15, str23, paramMap);
/*  470 */                 str23 = ODocTHWithOpinionUtil.delHtml(str23);
/*  471 */                 str23 = OdocFileUtil.richTextReplaceHtml(str23);
/*  472 */               } else if ("1".equals(str13) && ("5".equals(str14) || "4".equals(str14))) {
/*  473 */                 str23 = getValueByAmountDateShowType(str23, str16, str14);
/*  474 */               } else if (!str13.equals("3") && !str13.equals("6") && !str13.equals("5") && !str13.equals("4")) {
/*  475 */                 str23 = recordSet1.getString(b1 + 1);
/*  476 */                 str23 = convertValueToFormValue(str15, str23, paramMap);
/*  477 */               } else if ("3".equals(str13) && "2".equals(str14)) {
/*  478 */                 str23 = getValueByDateShowType(str23, str16);
/*      */               } else {
/*  480 */                 str23 = getFieldValue(str13, str14, str23, paramInt1, str15, str2, str21, Util.getIntValue(str16, -9), Util.getIntValue(paramString1, 0), str16);
/*      */               } 
/*  482 */               str23 = Util.formatMultiLang(str23, paramInt1 + "");
/*  483 */             } else if (str17.equals("2")) {
/*      */               
/*  485 */               ODocTHWithOpinionUtil oDocTHWithOpinionUtil = new ODocTHWithOpinionUtil();
/*  486 */               ArrayList arrayList14 = new ArrayList();
/*  487 */               HashMap<Object, Object> hashMap = new HashMap<>();
/*  488 */               (new BaseBean()).writeLog("oldfieldid=" + str15);
/*  489 */               (new BaseBean()).writeLog("nodeid=" + str18);
/*  490 */               str15 = "-1";
/*  491 */               (new BaseBean()).writeLog("newfieldid=" + str15);
/*  492 */               List<Map> list = oDocTHWithOpinionUtil.getWorkflowOpinionInfo(paramString2, paramString1, str15, str18, str22, str19, str20);
/*  493 */               (new BaseBean()).writeLog("list=" + list);
/*      */               
/*  495 */               boolean bool = ODocTHWithOpinionUtil.replaceOpinionBySetting(paramString1, list, str22);
/*  496 */               (new BaseBean()).writeLog("result=" + bool);
/*  497 */               if (bool) {
/*  498 */                 for (byte b2 = 0; b2 < list.size(); b2++) {
/*  499 */                   str23 = str23 + (String)((Map)list.get(b2)).get("remark") + "\n";
/*      */                 }
/*      */               } else {
/*      */                 
/*  503 */                 str23 = "";
/*      */               } 
/*      */             } 
/*  506 */             (new BaseBean()).writeLog("tempvalue=" + str23);
/*  507 */             arrayList3.add(str23);
/*      */           } 
/*      */         }
/*      */       } 
/*      */ 
/*      */       
/*  513 */       (new BaseBean()).writeLog("modullist=" + arrayList1);
/*  514 */       (new BaseBean()).writeLog("valuelist=" + arrayList3);
/*  515 */       if (arrayList1 != null && arrayList3 != null) {
/*  516 */         if (this.valueAndMoudle == null) {
/*  517 */           this.valueAndMoudle = new ArrayList();
/*      */         } else {
/*  519 */           this.valueAndMoudle.clear();
/*      */         } 
/*  521 */         this.valueAndMoudle.add(arrayList1);
/*  522 */         this.valueAndMoudle.add(arrayList3);
/*  523 */         this.valueAndMoudle.add(arrayList2);
/*      */       } 
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   public void setRequestToModul(String paramString1, String paramString2, int paramInt1, boolean paramBoolean, int paramInt2, int paramInt3) throws Exception {
/*  531 */     setRequestToModul(paramString1, paramString2, paramInt1, paramBoolean, paramInt2, paramInt3, (Map)null);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String convertValueToFormValue(String paramString1, String paramString2, Map paramMap) {
/*  543 */     String str = paramString2;
/*      */     try {
/*  545 */       if (null != paramMap) {
/*  546 */         str = (String)paramMap.get("field" + paramString1);
/*  547 */         if (null != str) {
/*  548 */           str = URLDecoder.decode(str, "UTF-8");
/*      */         }
/*      */       } 
/*  551 */     } catch (UnsupportedEncodingException unsupportedEncodingException) {
/*  552 */       writeLog("convertValueToFormValue exception:", unsupportedEncodingException);
/*      */     } 
/*  554 */     return str;
/*      */   }
/*      */ 
/*      */   
/*      */   public ArrayList getSelectItemValue(String paramString1, String paramString2) {
/*  559 */     RecordSet recordSet = new RecordSet();
/*      */     
/*  561 */     if (paramString1 == null || paramString1.trim().equals("") || paramString2 == null || paramString2.trim().equals("")) {
/*  562 */       return null;
/*      */     }
/*      */     
/*  565 */     String str1 = "";
/*  566 */     ArrayList<String> arrayList = getDocFiled(paramString1);
/*      */     
/*  568 */     if (arrayList == null || arrayList.size() == 0) {
/*  569 */       return null;
/*      */     }
/*      */ 
/*      */     
/*  573 */     int i = 0;
/*  574 */     String str2 = "0";
/*  575 */     recordSet.executeSql(" select formId,isBill from workflow_base where id= " + paramString1);
/*  576 */     if (recordSet.next()) {
/*  577 */       i = Util.getIntValue(recordSet.getString("formId"), 0);
/*  578 */       str2 = Util.null2String(recordSet.getString("isBill"));
/*      */     } 
/*  580 */     if (!str2.equals("1")) {
/*  581 */       str2 = "0";
/*      */     }
/*      */     
/*  584 */     int j = Util.getIntValue("" + arrayList.get(3), -1);
/*  585 */     String str3 = null;
/*      */     
/*  587 */     String str4 = null;
/*  588 */     String str5 = "workflow_form";
/*  589 */     if (str2.equals("1")) {
/*  590 */       recordSet.executeSql("select tablename from workflow_bill where id = " + i);
/*  591 */       if (recordSet.next()) {
/*  592 */         str5 = Util.null2String(recordSet.getString("tablename"));
/*      */       }
/*  594 */       str4 = "select a.fieldname from workflow_billfield a where a.id=" + j;
/*      */     } else {
/*  596 */       str4 = "select a.fieldname from workflow_formdict a where a.id=" + j;
/*      */     } 
/*  598 */     recordSet.executeSql(str4);
/*      */     
/*  600 */     if (recordSet.next()) {
/*  601 */       str3 = recordSet.getString(1);
/*      */     } else {
/*  603 */       return null;
/*      */     } 
/*      */     
/*  606 */     if (str3 == null || str3.trim().equals("")) {
/*  607 */       return null;
/*      */     }
/*      */     
/*  610 */     String str6 = null;
/*  611 */     str4 = "select " + str3 + " from " + str5 + " where requestid=" + paramString2;
/*  612 */     recordSet.executeSql(str4);
/*  613 */     if (recordSet.next()) {
/*  614 */       str6 = recordSet.getString(1);
/*      */     } else {
/*  616 */       return null;
/*      */     } 
/*      */     
/*  619 */     if (str6 == null || str6.trim().equals("")) {
/*  620 */       return null;
/*      */     }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  628 */     String str7 = "";
/*  629 */     String str8 = "";
/*  630 */     recordSet.executeSql("select docCategory,isAccordToSubCom from workflow_selectitem where fieldid=" + j + " and selectvalue=" + str6 + " and  isBill=" + str2);
/*  631 */     if (recordSet.next()) {
/*  632 */       str7 = Util.null2String(recordSet.getString("docCategory"));
/*  633 */       str8 = Util.null2String(recordSet.getString("isAccordToSubCom"));
/*      */     } 
/*      */     
/*  636 */     if (str8.equals("1")) {
/*  637 */       int k = 0;
/*  638 */       recordSet.executeSql("select creater from workflow_requestbase where requestId=" + paramString2);
/*  639 */       if (recordSet.next()) {
/*  640 */         k = Util.getIntValue(recordSet.getString("creater"), 0);
/*      */       }
/*      */       
/*  643 */       int m = 0;
/*      */       try {
/*  645 */         ResourceComInfo resourceComInfo = new ResourceComInfo();
/*  646 */         m = Util.getIntValue(resourceComInfo.getSubCompanyID("" + k), 0);
/*  647 */       } catch (Exception exception) {}
/*      */ 
/*      */ 
/*      */       
/*  651 */       recordSet.executeSql("SELECT docCategory FROM Workflow_SelectitemObj where fieldid=" + j + " and selectvalue=" + str6 + " and  isBill=" + str2 + " and objType='1' and objId= " + m);
/*  652 */       if (recordSet.next()) {
/*  653 */         str1 = Util.null2String(recordSet.getString("docCategory"));
/*      */       }
/*      */     } else {
/*  656 */       str1 = str7;
/*      */     } 
/*  658 */     return Util.TokenizerString(str1, ",");
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getFieldValue(String paramString1, String paramString2, String paramString3, int paramInt, String paramString4) throws Exception {
/*  671 */     return getFieldValue(paramString1, paramString2, paramString3, paramInt, paramString4, "");
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getFieldValue(String paramString1, String paramString2, String paramString3, int paramInt, String paramString4, String paramString5) throws Exception {
/*  684 */     return getFieldValue(paramString1, paramString2, paramString3, paramInt, paramString4, paramString5, "");
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getFieldValue(String paramString1, String paramString2, String paramString3, int paramInt, String paramString4, String paramString5, String paramString6, String paramString7) throws Exception {
/*  697 */     return getFieldValue(paramString1, paramString2, paramString3, paramInt, paramString4, paramString5, paramString6);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getFieldValue2(String paramString1, String paramString2, String paramString3, int paramInt, String paramString4, String paramString5, String paramString6) throws Exception {
/*  713 */     return getFieldValue(paramString1, paramString2, paramString3, paramInt, paramString4, paramString5, paramString6, -9, 0, "", true);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private String getFieldValue(String paramString1, String paramString2, String paramString3, int paramInt, String paramString4, String paramString5, String paramString6) throws Exception {
/*  727 */     return getFieldValue(paramString1, paramString2, paramString3, paramInt, paramString4, paramString5, paramString6, -9, 0, "");
/*      */   }
/*      */   private String getFieldValue(String paramString1, String paramString2, String paramString3, int paramInt1, String paramString4, String paramString5, String paramString6, int paramInt2, int paramInt3, String paramString7) throws Exception {
/*  730 */     return getFieldValue(paramString1, paramString2, paramString3, paramInt1, paramString4, paramString5, paramString6, -9, 0, "", false);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private String getFieldValue(String paramString1, String paramString2, String paramString3, int paramInt1, String paramString4, String paramString5, String paramString6, int paramInt2, int paramInt3, String paramString7, boolean paramBoolean) throws Exception {
/*  749 */     String str1 = "";
/*  750 */     String str2 = "";
/*  751 */     String str3 = OdocFileUtil.getProcessFieldType(paramString7);
/*  752 */     RecordSet recordSet = new RecordSet();
/*  753 */     if (paramString1.equals("3")) {
/*  754 */       if (paramString2.equals("2") || paramString2.equals("19") || paramString2.equals("290") || paramString2.equals("403") || paramString2.equals("402")) {
/*  755 */         str1 = paramString3;
/*  756 */       } else if (null != paramString3 && !paramString3.equals("")) {
/*  757 */         ArrayList<String> arrayList = Util.TokenizerString(paramString3, ",");
/*  758 */         if (paramString2.equals("8") || paramString2.equals("135")) {
/*      */           
/*  760 */           ProjectInfoComInfo projectInfoComInfo = new ProjectInfoComInfo();
/*  761 */           for (byte b = 0; b < arrayList.size(); b++) {
/*  762 */             str1 = str1 + projectInfoComInfo.getProjectInfoname(arrayList.get(b)) + str3;
/*      */           }
/*  764 */           str1 = OdocFileUtil.getProcessFieldValue(str1, paramString7);
/*  765 */         } else if (paramString2.equals("1") || paramString2.equals("17") || paramString2.equals("166")) {
/*      */           
/*  767 */           ResourceComInfo resourceComInfo = new ResourceComInfo();
/*  768 */           for (byte b = 0; b < arrayList.size(); b++) {
/*  769 */             str1 = str1 + resourceComInfo.getResourcename(arrayList.get(b)) + str3;
/*      */           }
/*  771 */           str1 = OdocFileUtil.getProcessFieldValue(str1, paramString7);
/*  772 */         } else if (paramString2.equals("7") || paramString2.equals("18")) {
/*      */           
/*  774 */           CustomerInfoComInfo customerInfoComInfo = new CustomerInfoComInfo();
/*  775 */           for (byte b = 0; b < arrayList.size(); b++) {
/*  776 */             str1 = str1 + customerInfoComInfo.getCustomerInfoname(arrayList.get(b)) + str3;
/*      */           }
/*  778 */           str1 = OdocFileUtil.getProcessFieldValue(str1, paramString7);
/*  779 */         } else if (paramString2.equals("4") || paramString2.equals("57") || paramString2.equals("167") || paramString2.equals("168")) {
/*  780 */           if (paramBoolean) {
/*  781 */             OrganizationShowSetBiz organizationShowSetBiz = new OrganizationShowSetBiz();
/*  782 */             str1 = organizationShowSetBiz.getDepartmentShow("workflow", paramString3);
/*      */           } else {
/*      */             
/*  785 */             DepartmentComInfo departmentComInfo = new DepartmentComInfo();
/*  786 */             DepartmentVirtualComInfo departmentVirtualComInfo = new DepartmentVirtualComInfo();
/*  787 */             for (byte b = 0; b < arrayList.size(); b++) {
/*  788 */               String str4 = "";
/*  789 */               String str5 = arrayList.get(b);
/*  790 */               if (!"".equals(str5)) {
/*  791 */                 if (Integer.parseInt(str5) < -1) {
/*  792 */                   str4 = departmentVirtualComInfo.getDepartmentname(str5);
/*      */                 } else {
/*  794 */                   str4 = departmentComInfo.getDepartmentname(str5);
/*      */                 } 
/*      */               }
/*  797 */               str1 = str1 + str4 + str3;
/*      */             } 
/*  799 */             str1 = OdocFileUtil.getProcessFieldValue(str1, paramString7);
/*      */           }
/*      */         
/*  802 */         } else if (paramString2.equals("164") || paramString2.equals("194") || paramString2.equals("170") || paramString2.equals("169")) {
/*  803 */           if (paramBoolean) {
/*  804 */             OrganizationShowSetBiz organizationShowSetBiz = new OrganizationShowSetBiz();
/*  805 */             str1 = organizationShowSetBiz.getSubcompanyShow("workflow", paramString3);
/*      */           } else {
/*      */             
/*  808 */             SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/*  809 */             SubCompanyVirtualComInfo subCompanyVirtualComInfo = new SubCompanyVirtualComInfo();
/*      */             
/*  811 */             for (byte b = 0; b < arrayList.size(); b++) {
/*  812 */               String str4 = "";
/*  813 */               String str5 = arrayList.get(b);
/*  814 */               if (!"".equals(str5)) {
/*  815 */                 if (Integer.parseInt(str5) < -1) {
/*  816 */                   str4 = subCompanyVirtualComInfo.getSubCompanyname(str5);
/*      */                 } else {
/*  818 */                   str4 = subCompanyComInfo.getSubCompanyname(str5);
/*      */                 } 
/*      */               }
/*  821 */               str1 = str1 + str4 + str3;
/*      */             } 
/*  823 */             str1 = OdocFileUtil.getProcessFieldValue(str1, paramString7);
/*      */           }
/*      */         
/*  826 */         } else if (paramString2.equals("9") || paramString2.equals("37")) {
/*      */           
/*  828 */           DocComInfo docComInfo = new DocComInfo();
/*  829 */           for (byte b = 0; b < arrayList.size(); b++) {
/*  830 */             str1 = str1 + docComInfo.getDocname(arrayList.get(b)) + str3;
/*      */           }
/*  832 */           str1 = OdocFileUtil.getProcessFieldValue(str1, paramString7);
/*  833 */         } else if (paramString2.equals("23")) {
/*      */           
/*  835 */           CapitalComInfo capitalComInfo = new CapitalComInfo();
/*  836 */           for (byte b = 0; b < arrayList.size(); b++) {
/*  837 */             str1 = str1 + capitalComInfo.getCapitalname(arrayList.get(b)) + str3;
/*      */           }
/*  839 */           str1 = OdocFileUtil.getProcessFieldValue(str1, paramString7);
/*  840 */         } else if (paramString2.equals("16")) {
/*      */           
/*  842 */           WorkflowRequestComInfo workflowRequestComInfo = new WorkflowRequestComInfo();
/*  843 */           for (byte b = 0; b < arrayList.size(); b++) {
/*  844 */             str1 = str1 + workflowRequestComInfo.getRequestName(arrayList.get(b)) + str3;
/*      */           }
/*  846 */           str1 = OdocFileUtil.getProcessFieldValue(str1, paramString7);
/*  847 */         } else if (paramString2.equals("141")) {
/*      */           
/*  849 */           ResourceConditionManager resourceConditionManager = new ResourceConditionManager();
/*  850 */           resourceConditionManager.setWorkflowid(paramInt3);
/*  851 */           str1 = str1 + resourceConditionManager.getFormShowNameOfNoLink(paramString3, paramInt1, true);
/*  852 */         } else if ("142".equals(paramString2)) {
/*  853 */           DocReceiveUnitComInfo docReceiveUnitComInfo = new DocReceiveUnitComInfo();
/*  854 */           for (byte b = 0; b < arrayList.size(); b++) {
/*  855 */             str1 = str1 + docReceiveUnitComInfo.getReceiveUnitName(arrayList.get(b)) + str3;
/*      */           }
/*  857 */           str1 = OdocFileUtil.getProcessFieldValue(str1, paramString7);
/*  858 */         } else if (paramString2.equals("161")) {
/*  859 */           str1 = "";
/*  860 */           String str = "";
/*  861 */           str2 = paramString3;
/*      */           try {
/*  863 */             Browser browser = (Browser)StaticObj.getServiceByFullname(paramString6, Browser.class);
/*  864 */             BrowserBean browserBean = browser.searchById(str2);
/*  865 */             String str4 = Util.null2String(browserBean.getDescription());
/*  866 */             String str5 = Util.null2String(browserBean.getName());
/*  867 */             str1 = str5;
/*  868 */           } catch (Exception exception) {}
/*      */         }
/*  870 */         else if (paramString2.equals("162")) {
/*  871 */           str1 = "";
/*  872 */           str2 = paramString3;
/*      */           try {
/*  874 */             Browser browser = (Browser)StaticObj.getServiceByFullname(paramString6, Browser.class);
/*  875 */             ArrayList<String> arrayList1 = Util.TokenizerString(str2, ",");
/*  876 */             for (byte b = 0; b < arrayList1.size(); b++) {
/*  877 */               String str4 = arrayList1.get(b);
/*  878 */               BrowserBean browserBean = browser.searchById(str4);
/*  879 */               String str5 = Util.null2String(browserBean.getName());
/*      */               
/*  881 */               String str6 = Util.null2String(browserBean.getDescription());
/*  882 */               str1 = str1 + str5 + " ";
/*      */             } 
/*  884 */           } catch (Exception exception) {}
/*      */         }
/*  886 */         else if (paramString2.equals("256") || paramString2.equals("257")) {
/*      */           
/*  888 */           str1 = "";
/*  889 */           str2 = paramString3;
/*  890 */           CustomTreeUtil customTreeUtil = new CustomTreeUtil();
/*      */           
/*  892 */           str1 = str1 + customTreeUtil.getTreeFieldShowName(str2, paramString6, "onlyname");
/*  893 */         } else if (paramString2.equals("226") || paramString2.equals("227")) {
/*      */           
/*  895 */           str1 = paramString3;
/*      */         }
/*      */         else {
/*      */           
/*  899 */           BrowserComInfo browserComInfo = new BrowserComInfo();
/*  900 */           String str4 = browserComInfo.getBrowsertablename(paramString2);
/*  901 */           String str5 = browserComInfo.getBrowsercolumname(paramString2);
/*  902 */           String str6 = browserComInfo.getBrowserkeycolumname(paramString2);
/*  903 */           String str7 = "";
/*  904 */           if (paramString3.indexOf(",") != -1) {
/*  905 */             str7 = "select " + str6 + "," + str5 + " from " + str4 + " where " + str6 + " in( " + paramString3 + ")";
/*      */           } else {
/*  907 */             str7 = "select " + str6 + "," + str5 + " from " + str4 + " where " + str6 + "=" + paramString3;
/*      */           } 
/*  909 */           recordSet.executeSql(str7);
/*  910 */           HashMap<Object, Object> hashMap = new HashMap<>();
/*  911 */           while (recordSet.next()) {
/*  912 */             str2 = Util.null2String(recordSet.getString(1));
/*  913 */             String str = Util.toScreen(recordSet.getString(2), paramInt1);
/*  914 */             str1 = str1 + str + " ";
/*  915 */             hashMap.put(str2, str);
/*      */           } 
/*  917 */           if (paramString2.equals("160")) {
/*  918 */             str1 = "";
/*  919 */             String[] arrayOfString = paramString3.split(",");
/*  920 */             for (String str : arrayOfString) {
/*  921 */               str1 = str1 + hashMap.get(str) + " ";
/*      */             }
/*      */           } 
/*      */         } 
/*      */       } 
/*  926 */     } else if (paramString1.equals("6")) {
/*  927 */       if (!"".equals(paramString3)) {
/*  928 */         String str = "select id,docsubject,accessorycount from docdetail where id in(" + paramString3 + ") order by id asc";
/*  929 */         recordSet.executeSql(str);
/*  930 */         while (recordSet.next()) {
/*  931 */           str1 = str1 + Util.toScreen(recordSet.getString(2), paramInt1) + str3;
/*      */         }
/*  933 */         str1 = OdocFileUtil.getProcessFieldValue(str1, paramString7);
/*      */       } 
/*  935 */     } else if (paramString1.equals("5")) {
/*  936 */       writeLog("=====fieldtype:" + paramString2 + "==showType:" + paramInt2 + "===fieldvalue:" + paramString3 + "========================");
/*      */       
/*  938 */       if (paramString2.equals("2")) {
/*      */         
/*  940 */         if ((paramInt2 == -3 || paramInt2 == -9) && !Strings.isNullOrEmpty(paramString3)) {
/*      */           
/*  942 */           String str = null;
/*  943 */           if ("1".equals(paramString5) || "0".equals(paramString5)) {
/*  944 */             str = "select * from workflow_SelectItem where selectvalue in (" + paramString3 + ") and fieldid=" + paramString4 + " and isBill=" + paramString5;
/*      */           } else {
/*  946 */             str = "select * from workflow_SelectItem where selectvalue in (" + paramString3 + ") and fieldid=" + paramString4;
/*      */           } 
/*      */           
/*  949 */           recordSet.executeSql(str);
/*  950 */           while (recordSet.next()) {
/*  951 */             if (!str1.equals("")) str1 = str1 + " "; 
/*  952 */             str1 = str1 + recordSet.getString("selectname");
/*      */           }
/*      */         
/*  955 */         } else if (paramInt2 == -2) {
/*  956 */           if (!"".equals(paramString3)) {
/*  957 */             String str = null;
/*  958 */             if ("1".equals(paramString5) || "0".equals(paramString5)) {
/*  959 */               str = "select * from workflow_SelectItem where fieldid=" + paramString4 + " and isBill=" + paramString5;
/*      */             } else {
/*  961 */               str = "select * from workflow_SelectItem where fieldid=" + paramString4;
/*      */             } 
/*      */             
/*  964 */             recordSet.executeSql(str);
/*  965 */             while (recordSet.next()) {
/*  966 */               if (!str1.equals("")) str1 = str1 + " "; 
/*  967 */               String str4 = (("," + paramString3 + ",").indexOf("," + recordSet.getString("selectvalue") + ",") >= 0) ? "√" : "□";
/*  968 */               str1 = str1 + str4 + " " + recordSet.getString("selectname");
/*      */             }
/*      */           
/*      */           } 
/*  972 */         } else if (!Strings.isNullOrEmpty(paramString3) && ("," + paramString3 + ",").indexOf("," + paramInt2 + ",") >= 0) {
/*  973 */           String str = null;
/*  974 */           if ("1".equals(paramString5) || "0".equals(paramString5)) {
/*  975 */             str = "select * from workflow_SelectItem where selectvalue in (" + paramInt2 + ") and fieldid=" + paramString4 + " and isBill=" + paramString5;
/*      */           } else {
/*  977 */             str = "select * from workflow_SelectItem where selectvalue in (" + paramInt2 + ") and fieldid=" + paramString4;
/*      */           } 
/*      */           
/*  980 */           recordSet.executeSql(str);
/*  981 */           while (recordSet.next()) {
/*  982 */             if (!str1.equals("")) str1 = str1 + " "; 
/*  983 */             str1 = str1 + recordSet.getString("selectname");
/*      */           }
/*      */         
/*      */         }
/*      */       
/*  988 */       } else if (!"".equals(paramString3)) {
/*      */         
/*  990 */         String str = null;
/*  991 */         if ("1".equals(paramString5) || "0".equals(paramString5)) {
/*  992 */           str = "select * from workflow_SelectItem where selectvalue in (" + paramString3 + ") and fieldid=" + paramString4 + " and isBill=" + paramString5;
/*      */         } else {
/*  994 */           str = "select * from workflow_SelectItem where selectvalue in (" + paramString3 + ") and fieldid=" + paramString4;
/*      */         } 
/*      */         
/*  997 */         recordSet.executeSql(str);
/*  998 */         while (recordSet.next()) {
/*  999 */           if (!str1.equals("")) str1 = str1 + " "; 
/* 1000 */           str1 = str1 + recordSet.getString("selectname");
/*      */         }
/*      */       
/*      */       } 
/* 1004 */     } else if (paramString1.equals("4")) {
/*      */       
/* 1006 */       if (paramInt2 == 2) {
/* 1007 */         if ("1".equals(paramString3)) {
/* 1008 */           str1 = "√";
/*      */         } else {
/* 1010 */           str1 = "□";
/*      */         }
/*      */       
/*      */       }
/* 1014 */       else if ("1".equals(paramString3)) {
/* 1015 */         str1 = SystemEnv.getHtmlLabelName(163, paramInt1);
/*      */       } else {
/* 1017 */         str1 = SystemEnv.getHtmlLabelName(161, paramInt1);
/*      */       } 
/*      */     } 
/*      */     
/* 1021 */     return str1.trim();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setCodeToRequest(String paramString1, String paramString2, String paramString3) {
/* 1029 */     ArrayList<String> arrayList = getDocFiled(paramString1);
/* 1030 */     RecordSet recordSet1 = new RecordSet();
/* 1031 */     RecordSet recordSet2 = new RecordSet();
/* 1032 */     if (arrayList != null && arrayList.size() > 0) {
/*      */ 
/*      */       
/* 1035 */       int i = 0;
/* 1036 */       String str1 = "0";
/* 1037 */       recordSet1.executeSql(" select formId,isBill from workflow_base where id= " + paramString1);
/* 1038 */       if (recordSet1.next()) {
/* 1039 */         i = Util.getIntValue(recordSet1.getString("formId"), 0);
/* 1040 */         str1 = Util.null2String(recordSet1.getString("isBill"));
/*      */       } 
/*      */       
/* 1043 */       if (!str1.equals("1")) {
/* 1044 */         str1 = "0";
/*      */       }
/* 1046 */       String str2 = "";
/* 1047 */       String str3 = "" + arrayList.get(0);
/*      */       
/* 1049 */       String str4 = null;
/* 1050 */       String str5 = "workflow_form";
/* 1051 */       if (str1.equals("1")) {
/* 1052 */         recordSet1.executeSql("select tablename from workflow_bill where id = " + i);
/* 1053 */         if (recordSet1.next()) {
/* 1054 */           str5 = Util.null2String(recordSet1.getString("tablename"));
/*      */         }
/* 1056 */         str4 = "select a.fieldname from workflow_billfield a where a.id=" + str3;
/*      */       } else {
/* 1058 */         str4 = "select a.fieldname from workflow_formdict a where a.id=" + str3;
/*      */       } 
/* 1060 */       recordSet1.execute(str4);
/* 1061 */       if (recordSet1.next()) {
/* 1062 */         str2 = recordSet1.getString(1);
/* 1063 */         recordSet2 = new RecordSet();
/*      */         
/* 1065 */         str4 = "update " + str5 + " set " + str2 + "='" + paramString3 + "'   where requestid=" + paramString2;
/* 1066 */         recordSet2.execute(str4);
/*      */       } 
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setDocIdToRequest(String paramString1, String paramString2, String paramString3) {
/* 1080 */     int i = Util.getIntValue(paramString1, 0);
/* 1081 */     int j = Util.getIntValue(paramString2, 0);
/* 1082 */     int k = Util.getIntValue(paramString3, 0);
/* 1083 */     if (i <= 0 || j <= 0 || k <= 0) {
/*      */       return;
/*      */     }
/*      */     
/* 1087 */     ArrayList<String> arrayList = getDocFiled(paramString1);
/* 1088 */     RecordSet recordSet1 = new RecordSet();
/* 1089 */     RecordSet recordSet2 = new RecordSet();
/* 1090 */     if (arrayList != null && arrayList.size() > 0) {
/*      */ 
/*      */       
/* 1093 */       int m = 0;
/* 1094 */       String str1 = "0";
/* 1095 */       recordSet1.executeSql(" select formId,isBill from workflow_base where id= " + paramString1);
/* 1096 */       if (recordSet1.next()) {
/* 1097 */         m = Util.getIntValue(recordSet1.getString("formId"), 0);
/* 1098 */         str1 = Util.null2String(recordSet1.getString("isBill"));
/*      */       } 
/*      */       
/* 1101 */       if (!str1.equals("1")) {
/* 1102 */         str1 = "0";
/*      */       }
/* 1104 */       String str2 = "";
/* 1105 */       String str3 = "" + arrayList.get(1);
/*      */       
/* 1107 */       String str4 = null;
/* 1108 */       String str5 = "workflow_form";
/* 1109 */       if (str1.equals("1")) {
/* 1110 */         recordSet1.executeSql("select tablename from workflow_bill where id = " + m);
/* 1111 */         if (recordSet1.next()) {
/* 1112 */           str5 = Util.null2String(recordSet1.getString("tablename"));
/*      */         }
/* 1114 */         str4 = "select a.fieldname from workflow_billfield a where a.id=" + str3;
/*      */       } else {
/* 1116 */         str4 = "select a.fieldname from workflow_formdict a where a.id=" + str3;
/*      */       } 
/* 1118 */       recordSet1.execute(str4);
/* 1119 */       if (recordSet1.next()) {
/* 1120 */         str2 = recordSet1.getString(1);
/* 1121 */         recordSet2 = new RecordSet();
/*      */         
/* 1123 */         str4 = "update " + str5 + " set " + str2 + "=" + paramString3 + "   where requestid=" + paramString2;
/* 1124 */         recordSet2.execute(str4);
/*      */       } 
/*      */ 
/*      */       
/* 1128 */       if (k > 0) {
/* 1129 */         String str = "";
/* 1130 */         recordSet1.executeSql("select docIds from workflow_requestbase where requestId=" + paramString2);
/* 1131 */         if (recordSet1.next()) {
/* 1132 */           str = Util.null2String(recordSet1.getString("docIds"));
/*      */         }
/* 1134 */         if (str.equals("")) {
/* 1135 */           str = paramString3;
/*      */         }
/* 1137 */         else if (("," + str + ",").indexOf("," + paramString3 + ",") == -1) {
/* 1138 */           str = str + "," + paramString3;
/*      */         } 
/*      */         
/* 1141 */         recordSet1.executeSql("update workflow_requestbase set docIds='" + str + "' where requestId=" + paramString2);
/*      */       } 
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void changeDocFiled(String paramString1, String paramString2, HttpServletRequest paramHttpServletRequest, int paramInt, boolean paramBoolean) throws Exception {
/* 1156 */     RecordSet recordSet1 = new RecordSet();
/* 1157 */     RecordSet recordSet2 = new RecordSet();
/* 1158 */     ArrayList<String> arrayList = getDocFiled(paramString1);
/*      */ 
/*      */     
/* 1161 */     int i = 0;
/* 1162 */     String str1 = "0";
/* 1163 */     recordSet1.executeSql(" select formId,isBill from workflow_base where id= " + paramString1);
/* 1164 */     if (recordSet1.next()) {
/* 1165 */       i = Util.getIntValue(recordSet1.getString("formId"), 0);
/* 1166 */       str1 = Util.null2String(recordSet1.getString("isBill"));
/*      */     } 
/*      */     
/* 1169 */     if (!str1.equals("1")) {
/* 1170 */       str1 = "0";
/*      */     }
/* 1172 */     DocCoder docCoder = new DocCoder();
/* 1173 */     String str2 = "";
/* 1174 */     if (arrayList != null && arrayList.size() > 0) {
/*      */       
/* 1176 */       String str3 = "workflow_form";
/* 1177 */       if (str1.equals("1")) {
/* 1178 */         recordSet1.executeSql("select tablename from workflow_bill where id = " + i);
/* 1179 */         if (recordSet1.next()) {
/* 1180 */           str3 = Util.null2String(recordSet1.getString("tablename"));
/*      */         }
/*      */       } 
/*      */       
/* 1184 */       int j = Util.getIntValue(arrayList.get(4), -1);
/* 1185 */       String str4 = "";
/* 1186 */       if (j > 0) {
/* 1187 */         if (str1.equals("1")) {
/* 1188 */           recordSet1.executeSql("select a.fieldname from workflow_billfield a where a.id=" + j);
/*      */         } else {
/* 1190 */           recordSet1.executeSql("select a.fieldname from workflow_formdict a where a.id=" + j);
/*      */         } 
/* 1192 */         if (recordSet1.next()) {
/* 1193 */           recordSet2 = new RecordSet();
/* 1194 */           recordSet2.execute("select " + recordSet1.getString(1) + " from " + str3 + " where requestid=" + paramString2);
/* 1195 */           if (recordSet2.next()) {
/* 1196 */             str4 = recordSet2.getString(1);
/* 1197 */             if (str4 == null || str4.trim().equals("")) {
/* 1198 */               str4 = "";
/*      */             }
/*      */           } 
/*      */         } 
/*      */       } 
/* 1203 */       if (j == -3) {
/* 1204 */         recordSet1.executeSql("select requestName from workflow_requestbase where requestId=" + paramString2);
/* 1205 */         if (recordSet1.next()) {
/* 1206 */           str4 = Util.null2String(recordSet1.getString("requestName"));
/*      */         }
/*      */       } 
/*      */       
/* 1210 */       String str5 = "";
/* 1211 */       if (this.user != null) {
/* 1212 */         str5 = "" + this.user.getUID();
/*      */       }
/* 1214 */       paramHttpServletRequest.getSession(true).setAttribute("docSubjectForRequestOperation", Util.toHtml2(str4));
/* 1215 */       if (!str5.equals("")) {
/* 1216 */         paramHttpServletRequest.getSession(true).setAttribute(str5 + "_" + paramString2 + "docSubjectForRequestOperation", Util.toHtml2(str4));
/*      */       }
/* 1218 */       String str6 = "" + arrayList.get(3);
/* 1219 */       String str7 = "";
/*      */       
/* 1221 */       String str8 = "";
/* 1222 */       String str9 = "";
/* 1223 */       String str10 = Util.null2String(paramHttpServletRequest.getParameter("field" + str6));
/*      */       
/* 1225 */       if (!str10.equals("")) {
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */         
/* 1231 */         String str11 = "";
/* 1232 */         String str12 = "";
/* 1233 */         recordSet1.executeSql("select docCategory,isAccordToSubCom from workflow_selectitem where fieldid=" + str6 + " and selectvalue=" + str10 + " and  isBill=" + str1);
/* 1234 */         if (recordSet1.next()) {
/* 1235 */           str11 = Util.null2String(recordSet1.getString("docCategory"));
/* 1236 */           str12 = Util.null2String(recordSet1.getString("isAccordToSubCom"));
/*      */         } 
/*      */         
/* 1239 */         if (str12.equals("1")) {
/* 1240 */           int k = 0;
/* 1241 */           recordSet1.executeSql("select creater from workflow_requestbase where requestId=" + paramString2);
/* 1242 */           if (recordSet1.next()) {
/* 1243 */             k = Util.getIntValue(recordSet1.getString("creater"), 0);
/*      */           }
/*      */           
/* 1246 */           int m = 0;
/*      */           try {
/* 1248 */             ResourceComInfo resourceComInfo = new ResourceComInfo();
/* 1249 */             m = Util.getIntValue(resourceComInfo.getSubCompanyID("" + k), 0);
/* 1250 */           } catch (Exception exception) {}
/*      */ 
/*      */           
/* 1253 */           recordSet1.executeSql("SELECT docCategory FROM Workflow_SelectitemObj where fieldid=" + str6 + " and selectvalue=" + str10 + " and  isBill=" + str1 + " and objType='1' and objId= " + m);
/* 1254 */           if (recordSet1.next()) {
/* 1255 */             str2 = Util.null2String(recordSet1.getString("docCategory"));
/*      */           }
/*      */         } else {
/* 1258 */           str2 = str11;
/*      */         } 
/* 1260 */         ArrayList<String> arrayList1 = Util.TokenizerString(str2, ",");
/* 1261 */         if (arrayList1 != null && arrayList1.size() > 0) {
/* 1262 */           String str13 = "";
/* 1263 */           String str14 = "";
/* 1264 */           String str15 = "";
/* 1265 */           if (arrayList1.size() == 1) {
/* 1266 */             str13 = "-1";
/* 1267 */             str14 = "-1";
/* 1268 */             str15 = "" + arrayList1.get(0);
/*      */           } else {
/* 1270 */             str13 = "" + arrayList1.get(0);
/* 1271 */             str14 = "" + arrayList1.get(1);
/* 1272 */             str15 = "" + arrayList1.get(2);
/*      */           } 
/* 1274 */           String str16 = "";
/*      */ 
/*      */ 
/*      */ 
/*      */           
/* 1279 */           if (!str16.equals("")) {
/* 1280 */             setCodeToRequest(paramString1, paramString2, str16);
/* 1281 */             paramHttpServletRequest.getSession(true).setAttribute("docCodeForRequestOperation", str16);
/* 1282 */             if (!str5.equals("")) {
/* 1283 */               paramHttpServletRequest.getSession(true).setAttribute(str5 + "_" + paramString2 + "docCodeForRequestOperation", str16);
/*      */             }
/*      */           } 
/* 1286 */           String str17 = "" + arrayList.get(1);
/*      */           
/* 1288 */           if (str1.equals("1")) {
/* 1289 */             str7 = "select a.fieldname from workflow_billfield a where a.id=" + str17;
/*      */           } else {
/* 1291 */             str7 = "select a.fieldname from workflow_formdict a where a.id=" + str17;
/*      */           } 
/* 1293 */           recordSet1.execute(str7);
/* 1294 */           if (recordSet1.next()) {
/* 1295 */             recordSet2 = new RecordSet();
/* 1296 */             str7 = "select " + recordSet1.getString(1) + " from " + str3 + " where requestid=" + paramString2;
/* 1297 */             recordSet2.execute(str7);
/* 1298 */             if (recordSet2.next()) {
/* 1299 */               str8 = recordSet2.getString(1);
/* 1300 */               if (!str8.equals("")) {
/* 1301 */                 setRequestToModul(paramString1, paramString2, paramInt);
/* 1302 */                 if (this.valueAndMoudle != null && this.valueAndMoudle.size() > 0) {
/* 1303 */                   docCoder.saveModulValue(this.valueAndMoudle, str8);
/*      */                 }
/*      */ 
/*      */                 
/* 1307 */                 boolean bool = false;
/* 1308 */                 String str = "";
/* 1309 */                 recordSet1.executeSql("select seccategory from DocDetail where id=" + Util.getIntValue(str8, 0));
/* 1310 */                 if (recordSet1.next()) {
/* 1311 */                   str = Util.null2String(recordSet1.getString("seccategory"));
/*      */                 }
/*      */                 
/* 1314 */                 if (!str.equals(str15)) {
/* 1315 */                   bool = true;
/*      */                 }
/* 1317 */                 if (str16.equals("")) {
/*      */                   
/* 1319 */                   if (bool) {
/* 1320 */                     recordSet1.execute("update docdetail set maincategory=" + str13 + ",subcategory=" + str14 + " ,seccategory=" + str15 + " where id=" + str8);
/*      */                   }
/*      */                 } else {
/* 1323 */                   if (str4 == null || str4.trim().equals("")) {
/* 1324 */                     str4 = str16;
/*      */                   }
/* 1326 */                   recordSet1.execute("update docdetail set docsubject='" + Util.toHtml100(str4) + "',docCode='" + str16 + "',maincategory=" + str13 + ",subcategory=" + str14 + " ,seccategory=" + str15 + " where id=" + str8);
/* 1327 */                   int k = Util.getIntValue(str8);
/* 1328 */                   if (k > 0) {
/*      */                     
/* 1330 */                     DocTriggerUtils docTriggerUtils = new DocTriggerUtils();
/* 1331 */                     docTriggerUtils.docdetail_getpinyin(k, recordSet1);
/*      */                   } 
/*      */                 } 
/*      */                 
/* 1335 */                 if (bool) {
/* 1336 */                   updateDocShareOfSecDefault(str8, str, str15);
/*      */                 }
/*      */                 
/* 1339 */                 DocComInfo docComInfo = new DocComInfo();
/* 1340 */                 docComInfo.updateDocInfoCache("" + str8);
/*      */               } 
/*      */             } 
/*      */           } 
/*      */         } 
/*      */       } else {
/* 1346 */         String str11 = "" + arrayList.get(2);
/* 1347 */         String str12 = "";
/* 1348 */         String str13 = "";
/* 1349 */         String str14 = "";
/* 1350 */         ArrayList<String> arrayList1 = Util.TokenizerString(str11, "||");
/* 1351 */         if (arrayList1 != null && arrayList1.size() > 0) {
/* 1352 */           if (arrayList1.size() == 1) {
/* 1353 */             str13 = "-1";
/* 1354 */             str14 = "-1";
/* 1355 */             str12 = "" + arrayList1.get(0);
/*      */           } else {
/* 1357 */             str13 = "" + arrayList1.get(0);
/* 1358 */             str14 = "" + arrayList1.get(1);
/* 1359 */             str12 = "" + arrayList1.get(2);
/*      */           } 
/* 1361 */           String str = "";
/* 1362 */           if (paramBoolean) {
/* 1363 */             str = docCoder.getDocCoder("" + str12);
/*      */           }
/* 1365 */           if (!str.equals("")) {
/* 1366 */             setCodeToRequest(paramString1, paramString2, str);
/* 1367 */             paramHttpServletRequest.getSession(true).setAttribute("docCodeForRequestOperation", str);
/* 1368 */             if (!str5.equals("")) {
/* 1369 */               paramHttpServletRequest.getSession(true).setAttribute(str5 + "_" + paramString2 + "docCodeForRequestOperation", str);
/*      */             }
/*      */           } 
/*      */         } 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */         
/* 1394 */         String str15 = "" + arrayList.get(1);
/* 1395 */         if (str1.equals("1")) {
/* 1396 */           str7 = "select a.fieldname from workflow_billfield a where a.id=" + str15;
/*      */         } else {
/* 1398 */           str7 = "select a.fieldname from workflow_formdict a where a.id=" + str15;
/*      */         } 
/* 1400 */         recordSet1.execute(str7);
/* 1401 */         if (recordSet1.next()) {
/* 1402 */           recordSet2 = new RecordSet();
/* 1403 */           str7 = "select " + recordSet1.getString(1) + " from " + str3 + " where requestid=" + paramString2;
/* 1404 */           recordSet2.execute(str7);
/* 1405 */           if (recordSet2.next()) {
/* 1406 */             str8 = recordSet2.getString(1);
/* 1407 */             if (!str8.equals("")) {
/* 1408 */               recordSet1.execute("update docdetail set maincategory=" + str13 + ",subcategory=" + str14 + " ,seccategory=" + str12 + " where id=" + str8);
/* 1409 */               setRequestToModul(paramString1, paramString2, paramInt);
/* 1410 */               if (this.valueAndMoudle != null && this.valueAndMoudle.size() > 0) {
/* 1411 */                 docCoder.saveModulValue(this.valueAndMoudle, str8);
/*      */               }
/*      */             } 
/*      */           } 
/*      */         } 
/*      */       } 
/*      */       
/* 1418 */       if (Util.getIntValue(str8, -1) > 0) {
/*      */         
/* 1420 */         recordSet1.executeSql("select seccategory from DocDetail where id=" + Util.getIntValue(str8, 0));
/* 1421 */         if (recordSet1.next()) {
/* 1422 */           str9 = Util.null2String(recordSet1.getString("seccategory"));
/*      */         }
/*      */         
/* 1425 */         recordSet1.executeQuery("select docid from DocDummyDetail where docid = ?", new Object[] { str8 });
/* 1426 */         if (!recordSet1.next()) {
/* 1427 */           writeLog("============虚拟目录：========docId：" + str8 + "=======seccategoryId:" + str9);
/* 1428 */           if (Util.getIntValue(str9, -1) > 0) {
/*      */             
/* 1430 */             OperateService operateService = new OperateService();
/* 1431 */             operateService.docDummyBySecCategory(this.user, Util.getIntValue(str8, -1), Util.getIntValue(str9, -1));
/*      */           } 
/*      */         } 
/*      */       } 
/*      */     } 
/*      */   }
/*      */ 
/*      */   
/*      */   public boolean haveChage(String paramString1, String paramString2, FileUpload paramFileUpload) {
/* 1440 */     return haveChage(paramString1, paramString2, (HttpServletRequest)null, paramFileUpload);
/*      */   }
/*      */   
/*      */   public boolean haveChage(String paramString1, String paramString2, HttpServletRequest paramHttpServletRequest) {
/* 1444 */     return haveChage(paramString1, paramString2, paramHttpServletRequest, (FileUpload)null);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean haveChage(String paramString1, String paramString2, HttpServletRequest paramHttpServletRequest, FileUpload paramFileUpload) {
/* 1455 */     boolean bool = false;
/*      */     
/* 1457 */     RecordSet recordSet = new RecordSet();
/* 1458 */     ArrayList<String> arrayList = getDocFiled(paramString1);
/*      */ 
/*      */     
/* 1461 */     int i = 0;
/* 1462 */     String str = "0";
/* 1463 */     recordSet.executeSql(" select formId,isBill from workflow_base where id= " + paramString1);
/* 1464 */     if (recordSet.next()) {
/* 1465 */       i = Util.getIntValue(recordSet.getString("formId"), 0);
/* 1466 */       str = Util.null2String(recordSet.getString("isBill"));
/*      */     } 
/*      */     
/* 1469 */     if (!str.equals("1")) {
/* 1470 */       str = "0";
/*      */     }
/*      */     
/* 1473 */     if ("-1".equals(paramString2)) {
/* 1474 */       bool = true;
/* 1475 */       if (arrayList != null && arrayList.size() > 0) {
/*      */         
/* 1477 */         String str1 = null;
/* 1478 */         String str2 = "" + arrayList.get(3);
/*      */         
/* 1480 */         String str3 = "";
/* 1481 */         int j = -1;
/* 1482 */         if (paramHttpServletRequest != null) {
/* 1483 */           str3 = paramHttpServletRequest.getParameter("field" + str2);
/* 1484 */           j = Util.getIntValue(paramHttpServletRequest.getParameter("docValue"), -1);
/*      */         } else {
/* 1486 */           str3 = paramFileUpload.getParameter("field" + str2);
/* 1487 */           j = Util.getIntValue(paramFileUpload.getParameter("docValue"), -1);
/*      */         } 
/*      */         
/* 1490 */         if (str2 == null || str2.trim().equals("") || str3 == null || str3.trim().equals("") || j <= 0) {
/* 1491 */           return bool;
/*      */         }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */         
/* 1501 */         String str4 = "";
/*      */         
/* 1503 */         String str5 = "";
/* 1504 */         String str6 = "";
/* 1505 */         recordSet.executeSql("select docCategory,isAccordToSubCom from workflow_selectitem where fieldid=" + str2 + " and selectvalue=" + str3 + " and  isBill=" + str);
/* 1506 */         if (recordSet.next()) {
/* 1507 */           str5 = Util.null2String(recordSet.getString("docCategory"));
/* 1508 */           str6 = Util.null2String(recordSet.getString("isAccordToSubCom"));
/*      */         } 
/*      */         
/* 1511 */         if (str6.equals("1")) {
/*      */           
/* 1513 */           int k = 0;
/*      */           
/* 1515 */           if (this.user != null) {
/* 1516 */             k = this.user.getUserSubCompany1();
/*      */           }
/*      */           
/* 1519 */           recordSet.executeSql("SELECT docCategory FROM Workflow_SelectitemObj where fieldid=" + str2 + " and selectvalue=" + str3 + " and  isBill=" + str + " and objType='1' and objId= " + k);
/* 1520 */           if (recordSet.next()) {
/* 1521 */             str1 = Util.null2String(recordSet.getString("docCategory"));
/*      */           }
/*      */         } else {
/* 1524 */           str1 = str5;
/*      */         } 
/*      */         
/* 1527 */         if (str1 == null || str1.trim().equals("")) {
/* 1528 */           return bool;
/*      */         }
/*      */ 
/*      */         
/* 1532 */         String str7 = "";
/* 1533 */         str4 = "select mainCategory,subCategory,secCategory from DocDetail where id=" + j;
/* 1534 */         recordSet.executeSql(str4);
/* 1535 */         if (recordSet.next())
/*      */         {
/*      */           
/* 1538 */           str7 = Util.null2String(recordSet.getString("mainCategory")) + "," + Util.null2String(recordSet.getString("subCategory")) + "," + Util.null2String(recordSet.getString("secCategory"));
/*      */         }
/*      */         
/* 1541 */         if (str7.equals(str1)) {
/* 1542 */           bool = false;
/*      */         }
/*      */       } 
/*      */       
/* 1546 */       return bool;
/*      */     } 
/*      */     
/* 1549 */     if (arrayList != null && arrayList.size() > 0) {
/* 1550 */       String str1 = "" + arrayList.get(3);
/* 1551 */       RecordSet recordSet1 = new RecordSet();
/* 1552 */       String str2 = null;
/* 1553 */       String str3 = "workflow_form";
/* 1554 */       if (str.equals("1")) {
/* 1555 */         recordSet.executeSql("select tablename from workflow_bill where id = " + i);
/* 1556 */         if (recordSet.next()) {
/* 1557 */           str3 = Util.null2String(recordSet.getString("tablename"));
/*      */         }
/* 1559 */         str2 = "select a.fieldname from workflow_billfield a where a.id=" + str1;
/*      */       } else {
/* 1561 */         str2 = "select a.fieldname from workflow_formdict a where a.id=" + str1;
/*      */       } 
/* 1563 */       if (!StringUtil.isEmpty(str1)) {
/* 1564 */         recordSet.execute(str2);
/* 1565 */         if (recordSet.next()) {
/* 1566 */           str2 = "select " + recordSet.getString(1) + " from " + str3 + " where requestid=" + paramString2;
/* 1567 */           recordSet1 = new RecordSet();
/* 1568 */           recordSet1.execute(str2);
/* 1569 */           if (recordSet1.next()) {
/* 1570 */             String str4 = recordSet1.getString(1);
/* 1571 */             String str5 = "";
/* 1572 */             String str6 = "";
/* 1573 */             if (paramHttpServletRequest != null) {
/* 1574 */               str5 = paramHttpServletRequest.getParameter("oldfield" + str1);
/* 1575 */               str6 = paramHttpServletRequest.getParameter("field" + str1);
/*      */             } else {
/* 1577 */               str5 = paramFileUpload.getParameter("oldfield" + str1);
/* 1578 */               str6 = paramFileUpload.getParameter("field" + str1);
/*      */             } 
/* 1580 */             if (str5 != null) {
/* 1581 */               str4 = str5;
/*      */             }
/* 1583 */             if (!str4.equals(str6)) bool = true; 
/* 1584 */             if (str6.equals("") && str4.equals("-1")) bool = false; 
/*      */           } 
/*      */         } 
/*      */       } 
/*      */     } 
/* 1589 */     return bool;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public int getViewMouldIdForDocDspExt(int paramInt1, int paramInt2) {
/* 1599 */     RecordSet recordSet = new RecordSet();
/* 1600 */     int i = 0;
/*      */ 
/*      */     
/* 1603 */     if (paramInt1 <= 0 || paramInt2 <= 0) {
/* 1604 */       return i;
/*      */     }
/*      */     
/* 1607 */     int j = -1;
/* 1608 */     int k = -1;
/* 1609 */     int m = -1;
/*      */     
/* 1611 */     recordSet.executeSql("select workflowId from workflow_requestbase where requestId=" + paramInt1);
/* 1612 */     if (recordSet.next()) {
/* 1613 */       j = Util.getIntValue(recordSet.getString("workflowId"), -1);
/*      */     }
/*      */ 
/*      */ 
/*      */     
/* 1618 */     int n = 0;
/* 1619 */     String str1 = "0";
/* 1620 */     recordSet.executeSql(" select formId,isBill from workflow_base where id= " + j);
/* 1621 */     if (recordSet.next()) {
/* 1622 */       n = Util.getIntValue(recordSet.getString("formId"), 0);
/* 1623 */       str1 = Util.null2String(recordSet.getString("isBill"));
/*      */     } 
/* 1625 */     if (!str1.equals("1")) {
/* 1626 */       str1 = "0";
/*      */     }
/*      */     
/* 1629 */     recordSet.executeSql("select flowDocCatField from workflow_createdoc where status='1' and workflowId=" + j);
/* 1630 */     if (recordSet.next()) {
/* 1631 */       k = Util.getIntValue(recordSet.getString("flowDocCatField"), -1);
/*      */     }
/* 1633 */     String str2 = null;
/* 1634 */     String str3 = "workflow_form";
/* 1635 */     if (str1.equals("1")) {
/* 1636 */       recordSet.executeSql("select tablename from workflow_bill where id = " + n);
/* 1637 */       if (recordSet.next()) {
/* 1638 */         str3 = Util.null2String(recordSet.getString("tablename"));
/*      */       }
/* 1640 */       str2 = "select a.fieldname from workflow_billfield a where a.id=" + k;
/*      */     } else {
/* 1642 */       str2 = "select a.fieldname from workflow_formdict a where a.id=" + k;
/*      */     } 
/*      */     
/* 1645 */     recordSet.execute(str2);
/* 1646 */     if (recordSet.next()) {
/* 1647 */       RecordSet recordSet1 = new RecordSet();
/* 1648 */       str2 = "select " + recordSet.getString(1) + " from " + str3 + " where requestid=" + paramInt1;
/* 1649 */       recordSet1 = new RecordSet();
/* 1650 */       recordSet1.execute(str2);
/* 1651 */       if (recordSet1.next()) {
/*      */         
/* 1653 */         m = Util.getIntValue(recordSet1.getString(1), -1);
/*      */         
/* 1655 */         str2 = "select docMouldId from workflow_docshow where flowId=" + j + " and fieldid<>-1 and selectItemId=" + m + "  and exists (select 1 from DocSecCategoryMould where mouldType in(3,7) and secCategoryId=workflow_docshow.secCategoryId and mouldId=workflow_docshow.docMouldId)   order by isDefault desc ,fieldId asc";
/* 1656 */         recordSet.execute(str2);
/* 1657 */         if (!recordSet.next())
/*      */         {
/*      */           
/* 1660 */           str2 = " select docMouldId from workflow_docshow where flowId=" + j + " and fieldid<>-1 and selectItemId=-1  and exists (select 1 from DocSecCategoryMould where mouldType in(3,7) and secCategoryId=workflow_docshow.secCategoryId and mouldId=workflow_docshow.docMouldId)   order by isDefault desc ,fieldId asc";
/*      */         }
/*      */       } 
/*      */     } else {
/*      */       
/* 1665 */       str2 = " select docMouldId from workflow_docshow where flowId=" + j + " and fieldid<>-1 and selectItemId=-1  and exists (select 1 from DocSecCategoryMould where mouldType in(3,7) and secCategoryId=workflow_docshow.secCategoryId and mouldId=workflow_docshow.docMouldId)   order by isDefault desc ,fieldId asc";
/*      */     } 
/*      */     
/* 1668 */     recordSet.executeSql(str2);
/* 1669 */     if (recordSet.next()) {
/* 1670 */       i = Util.getIntValue(recordSet.getString("docMouldId"), -1);
/*      */     }
/*      */     
/* 1673 */     return i;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getEditMouldIdAndSecCategoryIdForDocAddExt(int paramInt) {
/* 1683 */     String str1 = "0_0";
/*      */ 
/*      */     
/* 1686 */     if (paramInt <= 0) {
/* 1687 */       return str1;
/*      */     }
/*      */     
/* 1690 */     int i = 0;
/* 1691 */     int j = 0;
/*      */     
/* 1693 */     int k = -1;
/* 1694 */     int m = -1;
/* 1695 */     String str2 = "";
/* 1696 */     int n = 0;
/* 1697 */     int i1 = -1;
/* 1698 */     RecordSet recordSet = new RecordSet();
/* 1699 */     recordSet.executeSql("select workflowId from workflow_requestbase where requestId=" + paramInt);
/* 1700 */     if (recordSet.next()) {
/* 1701 */       k = Util.getIntValue(recordSet.getString("workflowId"), -1);
/*      */     }
/*      */ 
/*      */ 
/*      */ 
/*      */     
/* 1707 */     int i2 = 0;
/* 1708 */     String str3 = "0";
/* 1709 */     recordSet.executeSql(" select formId,isBill from workflow_base where id= " + k);
/* 1710 */     if (recordSet.next()) {
/* 1711 */       i2 = Util.getIntValue(recordSet.getString("formId"), 0);
/* 1712 */       str3 = Util.null2String(recordSet.getString("isBill"));
/*      */     } 
/*      */     
/* 1715 */     if (!str3.equals("1")) {
/* 1716 */       str3 = "0";
/*      */     }
/*      */     
/* 1719 */     recordSet.executeSql("select flowDocCatField,defaultView from workflow_createdoc where status='1' and workflowId=" + k);
/* 1720 */     if (recordSet.next()) {
/* 1721 */       m = Util.getIntValue(recordSet.getString("flowDocCatField"), -1);
/* 1722 */       str2 = Util.null2String(recordSet.getString("defaultView"));
/*      */     } 
/* 1724 */     ArrayList<String> arrayList = Util.TokenizerString(str2, "||");
/* 1725 */     if (arrayList != null && arrayList.size() >= 3) {
/* 1726 */       n = Util.getIntValue(arrayList.get(2));
/*      */     }
/* 1728 */     String str4 = "";
/* 1729 */     int i3 = 0;
/* 1730 */     boolean bool = false;
/* 1731 */     String str5 = null;
/* 1732 */     String str6 = "workflow_form";
/* 1733 */     if (str3.equals("1")) {
/* 1734 */       recordSet.executeSql("select tablename from workflow_bill where id = " + i2);
/* 1735 */       if (recordSet.next()) {
/* 1736 */         str6 = Util.null2String(recordSet.getString("tablename"));
/*      */       }
/* 1738 */       str5 = "select a.fieldname from workflow_billfield a where a.id=" + m;
/*      */     } else {
/* 1740 */       str5 = "select a.fieldname from workflow_formdict a where a.id=" + m;
/*      */     } 
/*      */     
/* 1743 */     recordSet.execute(str5);
/* 1744 */     if (recordSet.next()) {
/* 1745 */       RecordSet recordSet1 = new RecordSet();
/* 1746 */       str5 = "select " + recordSet.getString(1) + " from " + str6 + " where requestid=" + paramInt;
/* 1747 */       recordSet1 = new RecordSet();
/* 1748 */       recordSet1.execute(str5);
/* 1749 */       if (recordSet1.next()) {
/* 1750 */         i1 = Util.getIntValue(recordSet1.getString(1), -1);
/* 1751 */         if (i1 >= 0) {
/* 1752 */           str5 = "select docMouldId,secCategoryId from workflow_docshowedit where flowId=" + k + " and fieldid!=-1 and selectItemId=" + i1 + "  and exists (select 1 from DocSecCategoryMould where mouldType in(4,8) and secCategoryId=workflow_docshowedit.secCategoryId and mouldId=workflow_docshowedit.docMouldId and mouldId in (select mouldid from workflow_mould where visible=1 and mouldType in(3,4)  and workflowid = " + k + ")  )   order by isDefault desc ,docMouldId asc,fieldId asc";
/* 1753 */           recordSet.executeSql(str5);
/* 1754 */           if (!recordSet.next()) {
/* 1755 */             str5 = "select doccategory from workflow_selectitem where fieldId=" + m + " and isbill=" + str3 + " and selectvalue=" + i1;
/* 1756 */             recordSet.executeSql(str5);
/* 1757 */             if (recordSet.next()) {
/* 1758 */               str4 = Util.null2String(recordSet.getString("doccategory"));
/* 1759 */               ArrayList<String> arrayList1 = Util.TokenizerString(str4, ",");
/* 1760 */               if (arrayList1 != null && arrayList1.size() >= 3) {
/* 1761 */                 i3 = Util.getIntValue(arrayList1.get(2));
/*      */               }
/*      */             } 
/* 1764 */             str5 = "select mouldid as docMouldId,seccategory as secCategoryId from workflow_mould where visible=1 and mouldType in(3,4)  and workflowid = " + k + " and seccategory=" + i3 + "  order by mouldid asc";
/* 1765 */             recordSet.executeSql(str5);
/* 1766 */             if (!recordSet.next()) {
/* 1767 */               bool = true;
/*      */             }
/*      */           } 
/*      */         } else {
/* 1771 */           bool = true;
/*      */         } 
/*      */       } 
/*      */     } else {
/* 1775 */       bool = true;
/*      */     } 
/* 1777 */     if (bool) {
/* 1778 */       str5 = " select docMouldId,secCategoryId from workflow_docshowedit where flowId=" + k + " and fieldid!=-1 and selectItemId=-1  and exists (select 1 from DocSecCategoryMould where mouldType in(4,8) and secCategoryId=workflow_docshowedit.secCategoryId and mouldId=workflow_docshowedit.docMouldId and mouldId in (select mouldid from workflow_mould where visible=1 and mouldType in(3,4)  and workflowid = " + k + ") )   order by isDefault desc ,docMouldId asc,fieldId asc";
/* 1779 */       recordSet.executeSql(str5);
/* 1780 */       if (!recordSet.next()) {
/* 1781 */         str5 = "select mouldid as docMouldId,seccategory as secCategoryId from workflow_mould where visible=1 and mouldType in(3,4)  and workflowid = " + k + " and seccategory=" + n + "  order by mouldid asc";
/*      */       }
/*      */     } 
/* 1784 */     recordSet.executeSql(str5);
/* 1785 */     if (recordSet.next()) {
/* 1786 */       i = Util.getIntValue(recordSet.getString("docMouldId"), 0);
/* 1787 */       j = Util.getIntValue(recordSet.getString("secCategoryId"), 0);
/* 1788 */       str1 = i + "_" + j;
/*      */     } 
/*      */     
/* 1791 */     return str1;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getViewMouldIdAndSecCategoryIdForDocDspExt(int paramInt1, int paramInt2) {
/* 1801 */     RecordSet recordSet = new RecordSet();
/* 1802 */     String str1 = "0_0";
/*      */ 
/*      */     
/* 1805 */     if (paramInt1 <= 0 || paramInt2 <= 0) {
/* 1806 */       return str1;
/*      */     }
/*      */     
/* 1809 */     int i = 0;
/* 1810 */     int j = 0;
/*      */     
/* 1812 */     int k = -1;
/* 1813 */     int m = -1;
/* 1814 */     int n = -1;
/*      */     
/* 1816 */     recordSet.executeSql("select workflowId from workflow_requestbase where requestId=" + paramInt1);
/* 1817 */     if (recordSet.next()) {
/* 1818 */       k = Util.getIntValue(recordSet.getString("workflowId"), -1);
/*      */     }
/*      */ 
/*      */ 
/*      */     
/* 1823 */     int i1 = 0;
/* 1824 */     String str2 = "0";
/* 1825 */     recordSet.executeSql(" select formId,isBill from workflow_base where id= " + k);
/* 1826 */     if (recordSet.next()) {
/* 1827 */       i1 = Util.getIntValue(recordSet.getString("formId"), 0);
/* 1828 */       str2 = Util.null2String(recordSet.getString("isBill"));
/*      */     } 
/* 1830 */     if (!str2.equals("1")) {
/* 1831 */       str2 = "0";
/*      */     }
/*      */     
/* 1834 */     recordSet.executeSql("select flowDocCatField from workflow_createdoc where status='1' and workflowId=" + k);
/* 1835 */     if (recordSet.next()) {
/* 1836 */       m = Util.getIntValue(recordSet.getString("flowDocCatField"), -1);
/*      */     }
/* 1838 */     String str3 = null;
/* 1839 */     String str4 = "workflow_form";
/* 1840 */     if (str2.equals("1")) {
/* 1841 */       recordSet.executeSql("select tablename from workflow_bill where id = " + i1);
/* 1842 */       if (recordSet.next()) {
/* 1843 */         str4 = Util.null2String(recordSet.getString("tablename"));
/*      */       }
/* 1845 */       str3 = "select a.fieldname from workflow_billfield a where a.id=" + m;
/*      */     } else {
/* 1847 */       str3 = "select a.fieldname from workflow_formdict a where a.id=" + m;
/*      */     } 
/*      */     
/* 1850 */     recordSet.execute(str3);
/* 1851 */     if (recordSet.next()) {
/* 1852 */       RecordSet recordSet1 = new RecordSet();
/* 1853 */       str3 = "select " + recordSet.getString(1) + " from " + str4 + " where requestid=" + paramInt1;
/* 1854 */       recordSet1 = new RecordSet();
/* 1855 */       recordSet1.execute(str3);
/* 1856 */       if (recordSet1.next()) {
/*      */         
/* 1858 */         n = Util.getIntValue(recordSet1.getString(1), -1);
/* 1859 */         str3 = "select docMouldId,secCategoryId from workflow_docshow where flowId=" + k + " and fieldid<>-1 and selectItemId=" + n + "  order by isDefault desc ,docMouldId asc,fieldId asc";
/* 1860 */         recordSet.execute(str3);
/* 1861 */         if (!recordSet.next())
/*      */         {
/* 1863 */           str3 = " select docMouldId,secCategoryId from workflow_docshow where flowId=" + k + " and fieldid<>-1 and selectItemId=-1   order by isDefault desc ,docMouldId asc,fieldId asc";
/*      */         }
/*      */       } 
/*      */     } else {
/* 1867 */       str3 = " select docMouldId,secCategoryId from workflow_docshow where flowId=" + k + " and fieldid<>-1 and selectItemId=-1  order by isDefault desc ,docMouldId asc,fieldId asc";
/*      */     } 
/*      */     
/* 1870 */     recordSet.executeSql(str3);
/* 1871 */     if (recordSet.next()) {
/* 1872 */       i = Util.getIntValue(recordSet.getString("docMouldId"), 0);
/* 1873 */       j = Util.getIntValue(recordSet.getString("secCategoryId"), 0);
/* 1874 */       str1 = i + "_" + j;
/*      */     } 
/*      */     
/* 1877 */     return str1;
/*      */   }
/*      */   
/*      */   private String getValueByDateShowType(String paramString1, String paramString2) {
/* 1881 */     String str = paramString1;
/*      */ 
/*      */     
/* 1884 */     if (paramString1 == null || paramString1.trim().equals("") || paramString2 == null || paramString2
/* 1885 */       .trim().equals("") || paramString1
/* 1886 */       .length() < 10)
/*      */     {
/* 1888 */       return str;
/*      */     }
/*      */     
/* 1891 */     if ("1".equals(paramString2)) {
/* 1892 */       str = "";
/* 1893 */       String str1 = "";
/* 1894 */       int i = 0;
/* 1895 */       int j = 0;
/* 1896 */       int k = paramString1.indexOf("-");
/* 1897 */       int m = paramString1.lastIndexOf("-");
/* 1898 */       if (k < 0 || m < 0) {
/* 1899 */         return str;
/*      */       }
/* 1901 */       str1 = paramString1.substring(0, k);
/* 1902 */       i = Util.getIntValue(paramString1.substring(k + 1, m), 0);
/* 1903 */       j = Util.getIntValue(paramString1.substring(m + 1), 0);
/*      */       
/* 1905 */       char[] arrayOfChar = str1.toCharArray();
/* 1906 */       byte b = 0;
/*      */       
/* 1908 */       while (b < arrayOfChar.length) {
/* 1909 */         char c = arrayOfChar[b++];
/* 1910 */         if (c == '0') {
/* 1911 */           str = str + "○"; continue;
/* 1912 */         }  if (c == '1') {
/* 1913 */           str = str + SystemEnv.getHtmlLabelName(385821, 7); continue;
/* 1914 */         }  if (c == '2') {
/* 1915 */           str = str + SystemEnv.getHtmlLabelName(385822, 7); continue;
/* 1916 */         }  if (c == '3') {
/* 1917 */           str = str + SystemEnv.getHtmlLabelName(383911, 7); continue;
/* 1918 */         }  if (c == '4') {
/* 1919 */           str = str + SystemEnv.getHtmlLabelName(385824, 7); continue;
/* 1920 */         }  if (c == '5') {
/* 1921 */           str = str + SystemEnv.getHtmlLabelName(385825, 7); continue;
/* 1922 */         }  if (c == '6') {
/* 1923 */           str = str + SystemEnv.getHtmlLabelName(385826, 7); continue;
/* 1924 */         }  if (c == '7') {
/* 1925 */           str = str + SystemEnv.getHtmlLabelName(82514, 7); continue;
/* 1926 */         }  if (c == '8') {
/* 1927 */           str = str + SystemEnv.getHtmlLabelName(82515, 7); continue;
/* 1928 */         }  if (c == '9') {
/* 1929 */           str = str + SystemEnv.getHtmlLabelName(82516, 7);
/*      */         }
/*      */       } 
/* 1932 */       str = str + SystemEnv.getHtmlLabelName(383372, 7);
/* 1933 */       switch (i) {
/*      */         case 1:
/* 1935 */           str = str + SystemEnv.getHtmlLabelName(385821, 7);
/*      */           break;
/*      */         case 2:
/* 1938 */           str = str + SystemEnv.getHtmlLabelName(385822, 7);
/*      */           break;
/*      */         case 3:
/* 1941 */           str = str + SystemEnv.getHtmlLabelName(383911, 7);
/*      */           break;
/*      */         case 4:
/* 1944 */           str = str + SystemEnv.getHtmlLabelName(385824, 7);
/*      */           break;
/*      */         case 5:
/* 1947 */           str = str + SystemEnv.getHtmlLabelName(385825, 7);
/*      */           break;
/*      */         case 6:
/* 1950 */           str = str + SystemEnv.getHtmlLabelName(385826, 7);
/*      */           break;
/*      */         case 7:
/* 1953 */           str = str + SystemEnv.getHtmlLabelName(82514, 7);
/*      */           break;
/*      */         case 8:
/* 1956 */           str = str + SystemEnv.getHtmlLabelName(82515, 7);
/*      */           break;
/*      */         case 9:
/* 1959 */           str = str + SystemEnv.getHtmlLabelName(82516, 7);
/*      */           break;
/*      */         case 10:
/* 1962 */           str = str + SystemEnv.getHtmlLabelName(82517, 7);
/*      */           break;
/*      */         case 11:
/* 1965 */           str = str + SystemEnv.getHtmlLabelName(82518, 7);
/*      */           break;
/*      */         case 12:
/* 1968 */           str = str + SystemEnv.getHtmlLabelName(82519, 7);
/*      */           break;
/*      */       } 
/*      */ 
/*      */       
/* 1973 */       str = str + SystemEnv.getHtmlLabelName(383373, 7);
/* 1974 */       switch (j) {
/*      */         case 1:
/* 1976 */           str = str + SystemEnv.getHtmlLabelName(385821, 7);
/*      */           break;
/*      */         case 2:
/* 1979 */           str = str + SystemEnv.getHtmlLabelName(385822, 7);
/*      */           break;
/*      */         case 3:
/* 1982 */           str = str + SystemEnv.getHtmlLabelName(383911, 7);
/*      */           break;
/*      */         case 4:
/* 1985 */           str = str + SystemEnv.getHtmlLabelName(385824, 7);
/*      */           break;
/*      */         case 5:
/* 1988 */           str = str + SystemEnv.getHtmlLabelName(385825, 7);
/*      */           break;
/*      */         case 6:
/* 1991 */           str = str + SystemEnv.getHtmlLabelName(385826, 7);
/*      */           break;
/*      */         case 7:
/* 1994 */           str = str + SystemEnv.getHtmlLabelName(82514, 7);
/*      */           break;
/*      */         case 8:
/* 1997 */           str = str + SystemEnv.getHtmlLabelName(82515, 7);
/*      */           break;
/*      */         case 9:
/* 2000 */           str = str + SystemEnv.getHtmlLabelName(82516, 7);
/*      */           break;
/*      */         case 10:
/* 2003 */           str = str + SystemEnv.getHtmlLabelName(82517, 7);
/*      */           break;
/*      */         case 11:
/* 2006 */           str = str + SystemEnv.getHtmlLabelName(82518, 7);
/*      */           break;
/*      */         case 12:
/* 2009 */           str = str + SystemEnv.getHtmlLabelName(82519, 7);
/*      */           break;
/*      */         case 13:
/* 2012 */           str = str + SystemEnv.getHtmlLabelName(502962, 7);
/*      */           break;
/*      */         case 14:
/* 2015 */           str = str + SystemEnv.getHtmlLabelName(502963, 7);
/*      */           break;
/*      */         case 15:
/* 2018 */           str = str + SystemEnv.getHtmlLabelName(502964, 7);
/*      */           break;
/*      */         case 16:
/* 2021 */           str = str + SystemEnv.getHtmlLabelName(502965, 7);
/*      */           break;
/*      */         case 17:
/* 2024 */           str = str + SystemEnv.getHtmlLabelName(502966, 7);
/*      */           break;
/*      */         case 18:
/* 2027 */           str = str + SystemEnv.getHtmlLabelName(502968, 7);
/*      */           break;
/*      */         case 19:
/* 2030 */           str = str + SystemEnv.getHtmlLabelName(502969, 7);
/*      */           break;
/*      */         case 20:
/* 2033 */           str = str + SystemEnv.getHtmlLabelName(502970, 7);
/*      */           break;
/*      */         case 21:
/* 2036 */           str = str + SystemEnv.getHtmlLabelName(502971, 7);
/*      */           break;
/*      */         case 22:
/* 2039 */           str = str + SystemEnv.getHtmlLabelName(502972, 7);
/*      */           break;
/*      */         case 23:
/* 2042 */           str = str + SystemEnv.getHtmlLabelName(502973, 7);
/*      */           break;
/*      */         case 24:
/* 2045 */           str = str + SystemEnv.getHtmlLabelName(502974, 7);
/*      */           break;
/*      */         case 25:
/* 2048 */           str = str + SystemEnv.getHtmlLabelName(502976, 7);
/*      */           break;
/*      */         case 26:
/* 2051 */           str = str + SystemEnv.getHtmlLabelName(502977, 7);
/*      */           break;
/*      */         case 27:
/* 2054 */           str = str + SystemEnv.getHtmlLabelName(502978, 7);
/*      */           break;
/*      */         case 28:
/* 2057 */           str = str + SystemEnv.getHtmlLabelName(502979, 7);
/*      */           break;
/*      */         case 29:
/* 2060 */           str = str + SystemEnv.getHtmlLabelName(502980, 7);
/*      */           break;
/*      */         case 30:
/* 2063 */           str = str + SystemEnv.getHtmlLabelName(502981, 7);
/*      */           break;
/*      */         case 31:
/* 2066 */           str = str + SystemEnv.getHtmlLabelName(502982, 7);
/*      */           break;
/*      */       } 
/* 2069 */       str = str + SystemEnv.getHtmlLabelName(385820, 7);
/*      */     }
/* 2071 */     else if ("2".equals(paramString2)) {
/* 2072 */       String str1 = "";
/* 2073 */       int i = 0;
/* 2074 */       int j = 0;
/* 2075 */       int k = paramString1.indexOf("-");
/* 2076 */       int m = paramString1.lastIndexOf("-");
/* 2077 */       if (k < 0 || m < 0) {
/* 2078 */         return str;
/*      */       }
/* 2080 */       str1 = paramString1.substring(0, k);
/* 2081 */       i = Util.getIntValue(paramString1.substring(k + 1, m), 0);
/* 2082 */       j = Util.getIntValue(paramString1.substring(m + 1), 0);
/* 2083 */       str = str1 + SystemEnv.getHtmlLabelName(383372, 7) + i + SystemEnv.getHtmlLabelName(383373, 7) + j + SystemEnv.getHtmlLabelName(385820, 7);
/*      */     } 
/*      */     
/* 2086 */     return str;
/*      */   }
/*      */   
/*      */   private String getValueByAmountDateShowType(String paramString1, String paramString2, String paramString3) {
/* 2090 */     String str = paramString1;
/*      */ 
/*      */     
/* 2093 */     if (paramString1 == null || paramString1.trim().equals("") || paramString2 == null || paramString2
/* 2094 */       .trim().equals(""))
/*      */     {
/* 2096 */       return str;
/*      */     }
/* 2098 */     paramString1 = paramString1.replace(",", "");
/* 2099 */     Double double_ = Double.valueOf(Util.getDoubleValue(paramString1, 0.0D));
/* 2100 */     OdocFileUtil odocFileUtil = new OdocFileUtil();
/* 2101 */     if ("5".equals(paramString3)) {
/* 2102 */       if ("1".equals(paramString2)) {
/* 2103 */         str = odocFileUtil.digitUppercase(double_.doubleValue());
/* 2104 */       } else if ("2".equals(paramString2)) {
/* 2105 */         str = odocFileUtil.thousandsCase(double_.doubleValue());
/*      */       } 
/* 2107 */     } else if ("4".equals(paramString3)) {
/* 2108 */       if ("1".equals(paramString2)) {
/* 2109 */         str = odocFileUtil.digitUppercase(double_.doubleValue());
/* 2110 */       } else if ("2".equals(paramString2)) {
/* 2111 */         str = odocFileUtil.thousandsCase(double_.doubleValue());
/* 2112 */       } else if (paramString2 == null || paramString2.trim().equals("")) {
/*      */         
/* 2114 */         str = paramString1;
/*      */       } 
/*      */     } 
/* 2117 */     return str;
/*      */   }
/*      */ 
/*      */   
/*      */   private void updateDocShareOfSecDefault(String paramString1, String paramString2, String paramString3) {
/* 2122 */     int i = Util.getIntValue(paramString1, -1);
/* 2123 */     int j = Util.getIntValue(paramString2, -1);
/* 2124 */     int k = Util.getIntValue(paramString3, -1);
/* 2125 */     if (i <= 0 || j <= 0 || k <= 0 || j == k) {
/*      */       return;
/*      */     }
/*      */     
/*      */     try {
/* 2130 */       RecordSet recordSet = new RecordSet();
/* 2131 */       recordSet.executeSql("delete from DocShare where docid=" + i + " and isSecDefaultShare='1'");
/*      */ 
/*      */ 
/*      */       
/* 2135 */       int m = -1;
/* 2136 */       String str = null;
/* 2137 */       recordSet.executeSql("select docCreaterId,docCreaterType from DocDetail where id=" + i);
/* 2138 */       if (recordSet.next()) {
/* 2139 */         m = Util.getIntValue(recordSet.getString("docCreaterId"), -1);
/* 2140 */         str = Util.null2String(recordSet.getString("docCreaterType"));
/*      */       } 
/* 2142 */       if (m <= 0) {
/* 2143 */         m = this.user.getUID();
/*      */       }
/* 2145 */       if (str == null || str.trim().equals("")) {
/* 2146 */         str = this.user.getLogintype();
/*      */       }
/*      */       
/* 2149 */       ResourceComInfo resourceComInfo = new ResourceComInfo();
/* 2150 */       int n = Util.getIntValue(resourceComInfo.getDepartmentID("" + m));
/*      */       
/* 2152 */       DocManager docManager = new DocManager();
/* 2153 */       docManager.setId(i);
/* 2154 */       docManager.setSeccategory(k);
/* 2155 */       docManager.setDocdepartmentid(n);
/* 2156 */       docManager.setUserid(m);
/* 2157 */       docManager.setUsertype(str);
/* 2158 */       docManager.AddShareInfo();
/*      */ 
/*      */       
/* 2161 */       DocViewer docViewer = new DocViewer();
/* 2162 */       docViewer.setDocShareByDoc("" + i);
/*      */     }
/* 2164 */     catch (Exception exception) {
/* 2165 */       writeLog("ex=" + exception);
/*      */     } 
/*      */   }
/*      */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/RequestDoc.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */