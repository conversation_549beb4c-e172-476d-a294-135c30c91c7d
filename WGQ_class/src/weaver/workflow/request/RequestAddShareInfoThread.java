/*    */ package weaver.workflow.request;
/*    */ 
/*    */ import weaver.general.BaseBean;
/*    */ import weaver.general.Util;
/*    */ 
/*    */ public class RequestAddShareInfoThread
/*    */   extends BaseBean implements Runnable {
/*  8 */   private static String throwThreadWfids = null;
/*    */   
/* 10 */   private RequestAddShareInfo reqaddshareinfo = null;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public void run() {
/*    */     try {
/* 18 */       if (this.reqaddshareinfo != null) {
/* 19 */         this.reqaddshareinfo.addShareInfo(true);
/*    */       }
/* 21 */     } catch (Exception exception) {
/* 22 */       exception.printStackTrace();
/*    */     } 
/*    */   }
/*    */   
/*    */   public static boolean isThrowThread(String paramString) {
/* 27 */     if (throwThreadWfids == null) {
/* 28 */       throwThreadWfids = Util.null2String((new BaseBean()).getPropValue("workflowResourceShareMode", "throwthreadwfids"));
/*    */     }
/*    */     
/* 31 */     if (throwThreadWfids.equals("")) {
/* 32 */       return false;
/*    */     }
/*    */     
/* 35 */     if ("all".equals(throwThreadWfids) || ("," + throwThreadWfids + ",").indexOf("," + paramString + ",") != -1) {
/* 36 */       return true;
/*    */     }
/* 38 */     return false;
/*    */   }
/*    */ 
/*    */   
/*    */   public RequestAddShareInfo getReqaddshareinfo() {
/* 43 */     return this.reqaddshareinfo;
/*    */   }
/*    */ 
/*    */   
/*    */   public void setReqaddshareinfo(RequestAddShareInfo paramRequestAddShareInfo) {
/* 48 */     this.reqaddshareinfo = paramRequestAddShareInfo;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/RequestAddShareInfoThread.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */