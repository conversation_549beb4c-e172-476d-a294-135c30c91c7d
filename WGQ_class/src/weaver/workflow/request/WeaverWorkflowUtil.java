/*     */ package weaver.workflow.request;
/*     */ 
/*     */ import java.util.HashSet;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.company.DepartmentComInfo;
/*     */ import weaver.workflow.workflow.WorkflowVersion;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class WeaverWorkflowUtil
/*     */   extends BaseBean
/*     */ {
/*     */   private User user;
/*     */   private String requestid;
/*     */   private String workflowid;
/*     */   private String wftype;
/*     */   private String hrmfieldid;
/*     */   private String reqfieldid;
/*     */   private String wftypefieldid;
/*     */   private String openurl;
/*     */   
/*     */   public void setUser(User paramUser) {
/*  28 */     this.user = paramUser;
/*     */   }
/*     */   
/*     */   public void setRequestid(String paramString) {
/*  32 */     this.requestid = paramString;
/*     */   }
/*     */   
/*     */   public void setWorkflowid(String paramString) {
/*  36 */     this.workflowid = paramString;
/*     */   }
/*     */   
/*     */   public void reset() {
/*  40 */     this.user = null;
/*  41 */     this.requestid = "";
/*  42 */     this.workflowid = "";
/*  43 */     this.wftype = "";
/*  44 */     this.hrmfieldid = "";
/*  45 */     this.reqfieldid = "";
/*  46 */     this.wftypefieldid = "";
/*  47 */     this.openurl = "";
/*     */   }
/*     */   
/*     */   public boolean isShowBtn() {
/*  51 */     String str = "select * from weaver_workflow_config";
/*  52 */     RecordSet recordSet = new RecordSet();
/*  53 */     recordSet.executeSql(str);
/*  54 */     if (recordSet.next()) {
/*  55 */       this.hrmfieldid = Util.null2String(recordSet.getString("hrmfieldid"));
/*  56 */       this.reqfieldid = Util.null2String(recordSet.getString("reqfieldid"));
/*  57 */       this.wftypefieldid = Util.null2String(recordSet.getString("wftypefieldid"));
/*  58 */       this.openurl = Util.null2String(recordSet.getString("openurl"));
/*     */       
/*  60 */       String str1 = Util.null2String(recordSet.getString("scopedeptid"));
/*     */       
/*  62 */       String str2 = Util.null2String(recordSet.getString("xqpgwfids"));
/*  63 */       String str3 = Util.null2String(recordSet.getString("xqkfwfids"));
/*  64 */       String str4 = Util.null2String(recordSet.getString("bbsjwfids"));
/*  65 */       String str5 = Util.null2String(recordSet.getString("khwtwfids"));
/*     */       
/*  67 */       boolean bool = isDeptContain(str1);
/*  68 */       if (bool) {
/*  69 */         boolean bool1 = isWFContain(str2);
/*  70 */         if (bool1) {
/*  71 */           this.wftype = "0";
/*  72 */           return true;
/*     */         } 
/*     */         
/*  75 */         bool1 = isWFContain(str3);
/*  76 */         if (bool1) {
/*  77 */           this.wftype = "1";
/*  78 */           return true;
/*     */         } 
/*     */         
/*  81 */         bool1 = isWFContain(str4);
/*  82 */         if (bool1) {
/*  83 */           this.wftype = "2";
/*  84 */           return true;
/*     */         } 
/*     */         
/*  87 */         bool1 = isWFContain(str5);
/*  88 */         if (bool1) {
/*  89 */           this.wftype = "3";
/*  90 */           return true;
/*     */         } 
/*     */       } 
/*     */     } 
/*  94 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getOpenurl() {
/* 103 */     String str1 = this.openurl;
/* 104 */     String str2 = "";
/* 105 */     if (!"".equals(this.requestid)) {
/* 106 */       String str = "select creater from workflow_requestbase where requestid = " + this.requestid;
/* 107 */       RecordSet recordSet = new RecordSet();
/* 108 */       recordSet.executeSql(str);
/* 109 */       if (recordSet.next()) {
/* 110 */         str2 = Util.null2String(recordSet.getString("creater"));
/*     */       }
/*     */     } 
/*     */     
/* 114 */     if (!"".equals(this.reqfieldid)) {
/* 115 */       str1 = str1 + "&field" + this.reqfieldid + "=" + this.requestid;
/*     */     }
/*     */     
/* 118 */     if (!"".equals(this.hrmfieldid)) {
/* 119 */       str1 = str1 + "&field" + this.hrmfieldid + "=" + str2;
/*     */     }
/*     */     
/* 122 */     if (!"".equals(this.wftypefieldid)) {
/* 123 */       str1 = str1 + "&field" + this.wftypefieldid + "=" + this.wftype;
/*     */     }
/* 125 */     return str1;
/*     */   }
/*     */   
/*     */   private boolean isWFContain(String paramString) {
/* 129 */     if (!"".equals(paramString)) {
/* 130 */       String[] arrayOfString = paramString.split(",", -1);
/* 131 */       for (String str : arrayOfString) {
/* 132 */         str = Util.null2String(str);
/* 133 */         if (!"".equals(str)) {
/* 134 */           String str1 = WorkflowVersion.getVersionStringByWfid(str);
/* 135 */           if (!"".equals(this.workflowid) && (
/* 136 */             "," + str1 + ",").indexOf("," + this.workflowid + ",") != -1) {
/* 137 */             return true;
/*     */           }
/*     */         } 
/*     */       } 
/*     */     } 
/*     */     
/* 143 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private boolean isDeptContain(String paramString) {
/* 153 */     if (this.user != null) {
/* 154 */       String str1 = String.valueOf(this.user.getUserDepartment());
/* 155 */       HashSet<String> hashSet = new HashSet();
/*     */       
/* 157 */       String str2 = "";
/* 158 */       DepartmentComInfo departmentComInfo = new DepartmentComInfo();
/*     */       try {
/* 160 */         str2 = departmentComInfo.getAllSupDepartment(str1);
/* 161 */         if (!"".equals(paramString)) {
/* 162 */           String[] arrayOfString = paramString.split(",", -1);
/* 163 */           for (String str : arrayOfString) {
/* 164 */             str = Util.null2String(str);
/* 165 */             if (!"".equals(str)) {
/* 166 */               hashSet.add(str);
/*     */             }
/*     */           } 
/*     */           
/* 170 */           if (hashSet.contains(str1)) {
/* 171 */             return true;
/*     */           }
/*     */           
/* 174 */           if (!"".equals(str2)) {
/* 175 */             String[] arrayOfString1 = str2.split(",", -1);
/* 176 */             for (String str : arrayOfString1) {
/* 177 */               str = Util.null2String(str);
/* 178 */               if (!"".equals(str) && hashSet.contains(str)) {
/* 179 */                 return true;
/*     */               }
/*     */             } 
/*     */           } 
/*     */         } 
/* 184 */       } catch (Exception exception) {
/* 185 */         exception.printStackTrace();
/*     */       } 
/*     */     } 
/*     */     
/* 189 */     return false;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/WeaverWorkflowUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */