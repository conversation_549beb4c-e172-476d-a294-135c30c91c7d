/*     */ package weaver.workflow.request;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class RequestOpinionShareUserInfo
/*     */ {
/*     */   private int userid;
/*     */   private int usertype;
/*     */   private int theusertype;
/*     */   private int agentid;
/*     */   private int nodeid;
/*     */   
/*     */   public int getNodeid() {
/*  32 */     return this.nodeid;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setNodeid(int paramInt) {
/*  40 */     this.nodeid = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getAgentid() {
/*  48 */     return this.agentid;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setAgentid(int paramInt) {
/*  56 */     this.agentid = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getTheusertype() {
/*  64 */     return this.theusertype;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setTheusertype(int paramInt) {
/*  72 */     this.theusertype = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getUserid() {
/*  80 */     return this.userid;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setUserid(int paramInt) {
/*  88 */     this.userid = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getUsertype() {
/*  96 */     return this.usertype;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setUsertype(int paramInt) {
/* 104 */     this.usertype = paramInt;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/RequestOpinionShareUserInfo.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */