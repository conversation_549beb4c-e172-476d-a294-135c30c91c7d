/*    */ package weaver.workflow.request;
/*    */ 
/*    */ import java.util.ArrayList;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class WFWorkflows
/*    */ {
/*    */   int workflowid;
/*    */   String workflowname;
/*    */   ArrayList reqeustids;
/*    */   ArrayList newrequestids;
/*    */   ArrayList flagnews;
/*    */   ArrayList wfersql;
/*    */   int reqeustCount;
/*    */   int newrequestCount;
/*    */   
/*    */   public void setWfersql(ArrayList paramArrayList) {
/* 23 */     this.wfersql = paramArrayList;
/*    */   }
/*    */   
/*    */   public ArrayList getWfersql() {
/* 27 */     return this.wfersql;
/*    */   }
/*    */   
/*    */   public int getWorkflowid() {
/* 31 */     return this.workflowid;
/*    */   }
/*    */   
/*    */   public void setWorkflowid(int paramInt) {
/* 35 */     this.workflowid = paramInt;
/*    */   }
/*    */   
/*    */   public String getWorkflowname() {
/* 39 */     return this.workflowname;
/*    */   }
/*    */   
/*    */   public void setWorkflowname(String paramString) {
/* 43 */     this.workflowname = paramString;
/*    */   }
/*    */   
/*    */   public ArrayList getReqeustids() {
/* 47 */     return this.reqeustids;
/*    */   }
/*    */   
/*    */   public void setReqeustids(ArrayList paramArrayList) {
/* 51 */     this.reqeustids = paramArrayList;
/*    */   }
/*    */   
/*    */   public ArrayList getNewrequestids() {
/* 55 */     return this.newrequestids;
/*    */   }
/*    */   
/*    */   public void setNewrequestids(ArrayList paramArrayList) {
/* 59 */     this.newrequestids = paramArrayList;
/*    */   }
/*    */   
/*    */   public ArrayList getFlagnews() {
/* 63 */     return this.flagnews;
/*    */   }
/*    */   
/*    */   public void setFlagnews(ArrayList paramArrayList) {
/* 67 */     this.flagnews = paramArrayList;
/*    */   }
/*    */   
/*    */   public int getReqeustCount() {
/* 71 */     return this.reqeustCount;
/*    */   }
/*    */   
/*    */   public void setReqeustCount(int paramInt) {
/* 75 */     this.reqeustCount = paramInt;
/*    */   }
/*    */   
/*    */   public int getNewrequestCount() {
/* 79 */     return this.newrequestCount;
/*    */   }
/*    */   
/*    */   public void setNewrequestCount(int paramInt) {
/* 83 */     this.newrequestCount = paramInt;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/request/WFWorkflows.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */