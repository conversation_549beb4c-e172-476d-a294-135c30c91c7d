/*    */ package weaver.workflow.field;
/*    */ 
/*    */ import java.util.HashMap;
/*    */ import weaver.conn.RecordSet;
/*    */ 
/*    */ public class SpecialFieldInfo
/*    */ {
/*  8 */   private String sql = "";
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public HashMap getFormSpecialField() {
/* 14 */     RecordSet recordSet = new RecordSet();
/* 15 */     HashMap<Object, Object> hashMap = new HashMap<Object, Object>();
/*    */     
/* 17 */     this.sql = "select type,fieldid,displayname,linkaddress,descriptivetext from workflow_specialfield a, workflow_formdict b where a.fieldid = b.id and a.isform='1' and a.isbill='0'";
/* 18 */     recordSet.executeSql(this.sql);
/* 19 */     while (recordSet.next()) {
/* 20 */       int i = recordSet.getInt("type");
/* 21 */       String str1 = recordSet.getString("fieldid");
/* 22 */       String str2 = "";
/* 23 */       if (i == 1) {
/* 24 */         str2 = "<a href='" + recordSet.getString("linkaddress") + "' target='_blank'>" + recordSet.getString("displayname") + "</a>";
/*    */       } else {
/* 26 */         str2 = recordSet.getString("descriptivetext");
/*    */       } 
/* 28 */       hashMap.put(str1 + "_0", str2);
/*    */     } 
/*    */     
/* 31 */     this.sql = "select type,fieldid,displayname,linkaddress,descriptivetext from workflow_specialfield a, workflow_billfield b where a.fieldid = b.id and a.isform='0' and a.isbill='1'";
/* 32 */     recordSet.executeSql(this.sql);
/* 33 */     while (recordSet.next()) {
/* 34 */       int i = recordSet.getInt("type");
/* 35 */       String str1 = recordSet.getString("fieldid");
/* 36 */       String str2 = "";
/* 37 */       if (i == 1) {
/* 38 */         str2 = "<a href='" + recordSet.getString("linkaddress") + "' target='_blank'>" + recordSet.getString("displayname") + "</a>";
/*    */       } else {
/* 40 */         str2 = recordSet.getString("descriptivetext");
/*    */       } 
/* 42 */       hashMap.put(str1 + "_1", str2);
/*    */     } 
/*    */     
/* 45 */     return hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/field/SpecialFieldInfo.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */