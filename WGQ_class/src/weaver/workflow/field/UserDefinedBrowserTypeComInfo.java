/*    */ package weaver.workflow.field;
/*    */ 
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.BaseBean;
/*    */ import weaver.general.Util;
/*    */ 
/*    */ public class UserDefinedBrowserTypeComInfo
/*    */   extends BaseBean {
/*    */   public String getName(String paramString) {
/* 10 */     if (paramString != null && !"".equals(paramString)) {
/*    */       
/* 12 */       int i = paramString.indexOf(".");
/* 13 */       if (i > 0) {
/* 14 */         paramString = paramString.substring(i + 1);
/*    */       }
/*    */       
/* 17 */       RecordSet recordSet = new RecordSet();
/* 18 */       String str = "select name from (select name from datashowset where showname='" + paramString + "' union all select name from mode_browser where showname='" + paramString + "') a";
/* 19 */       recordSet.executeSql(str);
/* 20 */       if (recordSet.next()) {
/* 21 */         return Util.null2String(recordSet.getString("name"));
/*    */       }
/*    */     } 
/* 24 */     return "";
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/field/UserDefinedBrowserTypeComInfo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */