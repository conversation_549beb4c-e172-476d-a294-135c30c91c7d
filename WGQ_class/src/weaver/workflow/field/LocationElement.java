/*    */ package weaver.workflow.field;
/*    */ 
/*    */ import java.util.ArrayList;
/*    */ import java.util.Hashtable;
/*    */ import java.util.List;
/*    */ import weaver.general.BaseBean;
/*    */ import weaver.general.LocateUtil;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.User;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class LocationElement
/*    */   extends BaseBean
/*    */   implements HtmlElement
/*    */ {
/* 18 */   int preid = -10;
/* 19 */   Boolean isFirst = Boolean.valueOf(true);
/* 20 */   int index = 0;
/* 21 */   List postList = new ArrayList();
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public Hashtable getHtmlElementString(int paramInt1, String paramString1, int paramInt2, String paramString2, int paramInt3, int paramInt4, int paramInt5, String paramString3, int paramInt6, int paramInt7, int paramInt8, int paramInt9, User paramUser, Hashtable paramHashtable) {
/* 39 */     Hashtable<Object, Object> hashtable = new Hashtable<Object, Object>();
/* 40 */     int i = Util.getIntValue((String)paramHashtable.get("workflowid"));
/* 41 */     int j = Util.getIntValue((String)paramHashtable.get("requestid"), 0);
/* 42 */     String str = LocateUtil.joinLoctionsField(i, j, paramString1, "" + paramInt1, paramString3, paramUser);
/* 43 */     String[] arrayOfString = str.split("##~~weaversplit~~##");
/*    */     
/* 45 */     hashtable.put("jsStr", arrayOfString[1]);
/* 46 */     hashtable.put("inputStr", arrayOfString[0]);
/* 47 */     return hashtable;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/field/LocationElement.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */