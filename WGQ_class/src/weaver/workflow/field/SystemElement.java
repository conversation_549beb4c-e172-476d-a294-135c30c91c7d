/*     */ package weaver.workflow.field;
/*     */ 
/*     */ import com.api.browser.bean.SearchConditionOption;
/*     */ import com.engine.hrm.biz.HrmClassifiedProtectionBiz;
/*     */ import com.engine.workflow.util.GetCustomLevelUtil;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Hashtable;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.docs.category.SecCategoryComInfo;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.workflow.sysPhrase.WorkflowPhrase;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class SystemElement
/*     */   extends BaseBean
/*     */ {
/*     */   private String fieldvalue;
/*     */   private int isviewonly;
/*     */   private int isview;
/*     */   private int isedit;
/*     */   private int ismand;
/*     */   private int languageid;
/*     */   private Hashtable otherPara;
/*     */   
/*     */   public Hashtable getPrintCount() {
/*  43 */     Hashtable<Object, Object> hashtable = new Hashtable<>();
/*  44 */     String str1 = "";
/*  45 */     String str2 = "";
/*     */     try {
/*  47 */       int i = 0;
/*  48 */       int j = Util.getIntValue((String)this.otherPara.get("requestid"));
/*  49 */       RecordSet recordSet = new RecordSet();
/*  50 */       recordSet.executeSql("select count(*) as printcount from workflow_viewlog where requestid='" + j + "'");
/*  51 */       if (recordSet.next()) {
/*  52 */         i = Util.getIntValue(recordSet.getString("printcount"), 0);
/*     */       }
/*     */       
/*  55 */       str1 = str1 + "<span id=\"dycsspan\">" + i + "</span>\n";
/*  56 */       str1 = str1 + "<input type=\"hidden\" name=\"DYCS\" value=\"" + i + "\" />\n";
/*     */     }
/*  58 */     catch (Exception exception) {
/*  59 */       writeLog(exception);
/*     */     } 
/*     */     
/*  62 */     hashtable.put("jsStr", str2);
/*  63 */     hashtable.put("inputStr", str1);
/*  64 */     return hashtable;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Hashtable getSecLevel(User paramUser) {
/*  72 */     Hashtable<Object, Object> hashtable = new Hashtable<>();
/*  73 */     String str1 = "";
/*  74 */     String str2 = "";
/*     */     
/*  76 */     int i = Util.getIntValue((String)this.otherPara.get("nodeid"), 0);
/*  77 */     String str3 = "";
/*  78 */     RecordSet recordSet = new RecordSet();
/*  79 */     recordSet.executeQuery("select nodetype from workflow_flownode where nodeid = ?", new Object[] { Integer.valueOf(i) });
/*  80 */     if (recordSet.next()) {
/*  81 */       str3 = Util.null2String(recordSet.getString("nodetype"));
/*     */     }
/*     */     try {
/*  84 */       str1 = str1 + "<select class=\"Inputstyle\" " + ("0".equals(str3) ? "" : "disabled=\"disabled\"") + " id=\"seclevel\" name=\"seclevel\" style=\"margin-right:2px;margin-top:2px;margin-bottom:2px;\">\n";
/*  85 */       HrmClassifiedProtectionBiz hrmClassifiedProtectionBiz = new HrmClassifiedProtectionBiz();
/*  86 */       List<SearchConditionOption> list = hrmClassifiedProtectionBiz.getResourceOptionListByUser(paramUser);
/*  87 */       if (list != null && list.size() > 0) {
/*  88 */         for (byte b = 0; b < list.size(); b++) {
/*  89 */           SearchConditionOption searchConditionOption = list.get(b);
/*  90 */           str1 = str1 + "<option value=\"" + searchConditionOption.getKey() + "\">" + searchConditionOption.getShowname() + "</option>\n";
/*     */         } 
/*     */       }
/*  93 */       str1 = str1 + "</select>\n";
/*     */     }
/*  95 */     catch (Exception exception) {
/*  96 */       writeLog(exception);
/*     */     } 
/*     */     
/*  99 */     hashtable.put("jsStr", str2);
/* 100 */     hashtable.put("inputStr", str1);
/* 101 */     return hashtable;
/*     */   }
/*     */   
/*     */   public Hashtable getRequestName() {
/* 105 */     Hashtable<Object, Object> hashtable = new Hashtable<>();
/* 106 */     String str1 = "";
/* 107 */     String str2 = "";
/*     */     try {
/* 109 */       String str3 = "";
/* 110 */       if (this.isviewonly == 0 && this.isview == 1 && this.isedit == 1 && this.ismand == 1 && "".equals(this.fieldvalue)) {
/* 111 */         str3 = "<img src='/images/BacoError_wev8.gif' align='absmiddle'>";
/*     */       }
/* 113 */       String str4 = "";
/* 114 */       String str5 = "";
/* 115 */       ArrayList<String> arrayList1 = (ArrayList)this.otherPara.get("sqlfieldidList");
/* 116 */       ArrayList<String> arrayList2 = (ArrayList)this.otherPara.get("sqlcontentList");
/* 117 */       if (arrayList1 != null && arrayList1.size() > 0) {
/* 118 */         for (byte b = 0; b < arrayList1.size(); b++) {
/* 119 */           String str6 = Util.null2String(arrayList1.get(b)).trim();
/* 120 */           String str7 = Util.null2String(arrayList2.get(b)).trim();
/* 121 */           if (!"".equals(str7) && 
/* 122 */             str7.indexOf("$-1$") > -1) {
/* 123 */             str5 = str5 + str6 + ",";
/*     */           }
/*     */         } 
/*     */         
/* 127 */         if (str5.length() > 0) {
/* 128 */           str4 = ";fieldAttrOperate.doSqlFieldAjax(this,'" + str5.substring(0, str5.length() - 1) + "')";
/*     */         }
/*     */       } 
/* 131 */       if (this.isview == 1) {
/* 132 */         if (this.isviewonly == 0) {
/* 133 */           if (this.isedit == 1) {
/* 134 */             str1 = "<input type=\"text\" class=\"Inputstyle\" viewtype=\"" + this.ismand + "\" temptitle=\"" + SystemEnv.getHtmlLabelName(21192, this.languageid) + "\" style=\"width:90%\" id=\"requestname\" name=\"requestname\" onChange=\"checkinput2('requestname','requestnamespan',this.getAttribute('viewtype'));changeKeyword();" + str4 + "\" size=\"" + '<' + "\" maxlength=\"" + 'È' + "\" value=\"" + this.fieldvalue + "\">\n";
/* 135 */             str1 = str1 + "<span id=\"requestnamespan\">" + str3 + "</span>\n";
/*     */           } else {
/* 137 */             str1 = str1 + "<span id=\"requestnamespan\">" + this.fieldvalue + "</span>\n";
/* 138 */             str1 = str1 + "<input type=\"hidden\" name=\"requestname\" value=\"" + this.fieldvalue + "\" />\n";
/*     */           } 
/*     */         } else {
/* 141 */           str1 = str1 + "<span id=\"requestnamespan\">" + this.fieldvalue + "</span>\n";
/* 142 */           str1 = str1 + "<input type=\"hidden\" name=\"requestname\" value=\"" + this.fieldvalue + "\" />\n";
/*     */         }
/*     */       
/* 145 */       } else if (this.isviewonly == 0) {
/* 146 */         str1 = str1 + "<input type=\"hidden\" name=\"requestname\" value=\"" + this.fieldvalue + "\" />";
/*     */       }
/*     */     
/* 149 */     } catch (Exception exception) {
/* 150 */       writeLog(exception);
/*     */     } 
/* 152 */     hashtable.put("jsStr", str2);
/* 153 */     hashtable.put("inputStr", str1);
/* 154 */     return hashtable;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<Map<String, Object>> getAllLevel() {
/* 163 */     return GetCustomLevelUtil.getAllLevel(this.languageid);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getRequstLeveLName(int paramInt) {
/* 173 */     List<Map<String, Object>> list = getAllLevel();
/* 174 */     String str = "";
/* 175 */     for (Map<String, Object> map : list) {
/* 176 */       if (map.containsKey("id") && Util.null2String(map.get("id")).equals(paramInt + "")) {
/* 177 */         str = Util.null2String(map.get("customname"));
/*     */         break;
/*     */       } 
/*     */     } 
/* 181 */     return str;
/*     */   }
/*     */ 
/*     */   
/*     */   public Hashtable getRequestLevel() {
/* 186 */     Hashtable<Object, Object> hashtable = new Hashtable<>();
/* 187 */     String str1 = "";
/* 188 */     String str2 = "";
/*     */     try {
/* 190 */       String str3 = "";
/* 191 */       String str4 = "";
/* 192 */       ArrayList<String> arrayList1 = (ArrayList)this.otherPara.get("sqlfieldidList");
/* 193 */       ArrayList<String> arrayList2 = (ArrayList)this.otherPara.get("sqlcontentList");
/* 194 */       if (arrayList1 != null && arrayList1.size() > 0) {
/* 195 */         for (byte b = 0; b < arrayList1.size(); b++) {
/* 196 */           String str9 = Util.null2String(arrayList1.get(b)).trim();
/* 197 */           String str10 = Util.null2String(arrayList2.get(b)).trim();
/* 198 */           if (!"".equals(str10) && 
/* 199 */             str10.indexOf("$-2$") > -1) {
/* 200 */             str4 = str4 + str9 + ",";
/*     */           }
/*     */         } 
/*     */         
/* 204 */         if (str4.length() > 0) {
/* 205 */           str3 = " onclick=\"fieldAttrOperate.doSqlFieldAjax(this,'" + str4.substring(0, str4.length() - 1) + "')\" ";
/*     */         }
/*     */       } 
/* 208 */       int i = Util.getIntValue(this.fieldvalue, 0);
/* 209 */       String str5 = "";
/* 210 */       String str6 = "";
/* 211 */       String str7 = "";
/* 212 */       String str8 = "";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 231 */       str8 = getRequstLeveLName(i);
/* 232 */       if (this.isview == 1) {
/* 233 */         if (this.isviewonly == 0) {
/* 234 */           if (this.isedit == 1) {
/*     */             try {
/* 236 */               String str9 = "", str10 = "", str11 = "", str12 = "";
/* 237 */               List<Map<String, Object>> list = getAllLevel();
/* 238 */               for (Map<String, Object> map : list) {
/* 239 */                 str9 = "";
/* 240 */                 if (map.containsKey("isfc")) str10 = Util.null2String(map.get("isfc")); 
/* 241 */                 if (map.containsKey("id")) str11 = Util.null2String(map.get("id")); 
/* 242 */                 if (map.containsKey("customname")) str12 = Util.null2String(map.get("customname")); 
/* 243 */                 if (str11.equals(i + "")) {
/* 244 */                   str9 = " checked ";
/*     */                 }
/*     */                 
/* 247 */                 if (!"1".equals(str10)) {
/* 248 */                   str1 = str1 + "<input type=\"radio\" value=\"0\" name=\"requestlevel\" " + str9 + "" + str3 + ">" + str12 + "";
/*     */                 }
/*     */               } 
/* 251 */             } catch (Exception exception) {}
/*     */ 
/*     */           
/*     */           }
/*     */           else {
/*     */ 
/*     */             
/* 258 */             str1 = str8 + "<input type=\"hidden\" name=\"requestlevel\" value=\"" + i + "\" />\n";
/*     */           } 
/*     */         } else {
/* 261 */           str1 = str8 + "\n";
/*     */         }
/*     */       
/* 264 */       } else if (this.isviewonly == 0) {
/* 265 */         str1 = str1 + "<input type=\"hidden\" name=\"requestlevel\" value=\"" + i + "\" />";
/*     */       }
/*     */     
/* 268 */     } catch (Exception exception) {
/* 269 */       writeLog(exception);
/*     */     } 
/* 271 */     hashtable.put("jsStr", str2);
/* 272 */     hashtable.put("inputStr", str1);
/* 273 */     return hashtable;
/*     */   }
/*     */   
/*     */   public Hashtable getMessageType(int paramInt) {
/* 277 */     Hashtable<Object, Object> hashtable = new Hashtable<>();
/* 278 */     String str1 = "";
/* 279 */     String str2 = "";
/* 280 */     if (paramInt == 1) {
/*     */       try {
/* 282 */         String str3 = "";
/* 283 */         String str4 = "";
/* 284 */         ArrayList<String> arrayList1 = (ArrayList)this.otherPara.get("sqlfieldidList");
/* 285 */         ArrayList<String> arrayList2 = (ArrayList)this.otherPara.get("sqlcontentList");
/* 286 */         if (arrayList1 != null && arrayList1.size() > 0) {
/* 287 */           for (byte b = 0; b < arrayList1.size(); b++) {
/* 288 */             String str9 = Util.null2String(arrayList1.get(b)).trim();
/* 289 */             String str10 = Util.null2String(arrayList2.get(b)).trim();
/* 290 */             if (!"".equals(str10) && 
/* 291 */               str10.indexOf("$-3$") > -1) {
/* 292 */               str4 = str4 + str9 + ",";
/*     */             }
/*     */           } 
/*     */           
/* 296 */           if (str4.length() > 0) {
/* 297 */             str3 = " onclick=\"fieldAttrOperate.doSqlFieldAjax(this,'" + str4.substring(0, str4.length() - 1) + "')\" ";
/*     */           }
/*     */         } 
/* 300 */         int i = Util.getIntValue(this.fieldvalue, 0);
/* 301 */         String str5 = "";
/* 302 */         String str6 = "";
/* 303 */         String str7 = "";
/* 304 */         String str8 = "";
/* 305 */         switch (i) {
/*     */           case 0:
/* 307 */             str5 = " checked ";
/* 308 */             str8 = SystemEnv.getHtmlLabelName(17583, this.languageid);
/*     */             break;
/*     */           
/*     */           case 1:
/* 312 */             str6 = " checked ";
/* 313 */             str8 = SystemEnv.getHtmlLabelName(17584, this.languageid);
/*     */             break;
/*     */           
/*     */           case 2:
/* 317 */             str7 = " checked ";
/* 318 */             str8 = SystemEnv.getHtmlLabelName(17585, this.languageid);
/*     */             break;
/*     */         } 
/*     */         
/* 322 */         if (this.isview == 1) {
/* 323 */           if (this.isviewonly == 0) {
/* 324 */             if (this.isedit == 1) {
/* 325 */               str1 = str1 + "<input type=\"radio\" value=\"0\" name=\"messageType\" " + str5 + "" + str3 + ">" + SystemEnv.getHtmlLabelName(17583, this.languageid) + "";
/* 326 */               str1 = str1 + "<input type=\"radio\" value=\"1\" name=\"messageType\" " + str6 + "" + str3 + ">" + SystemEnv.getHtmlLabelName(17584, this.languageid) + "";
/* 327 */               str1 = str1 + "<input type=\"radio\" value=\"2\" name=\"messageType\" " + str7 + "" + str3 + ">" + SystemEnv.getHtmlLabelName(17585, this.languageid) + "";
/*     */             } else {
/* 329 */               str1 = str8 + "<input type=\"hidden\" name=\"messageType\" value=\"" + this.fieldvalue + "\" />\n";
/*     */             } 
/*     */           } else {
/* 332 */             str1 = str8 + "\n";
/*     */           }
/*     */         
/* 335 */         } else if (this.isviewonly == 0) {
/* 336 */           str1 = str1 + "<input type=\"hidden\" name=\"messageType\" value=\"" + this.fieldvalue + "\" />";
/*     */         }
/*     */       
/* 339 */       } catch (Exception exception) {
/* 340 */         writeLog(exception);
/*     */       } 
/*     */     }
/* 343 */     hashtable.put("jsStr", str2);
/* 344 */     hashtable.put("inputStr", str1);
/* 345 */     return hashtable;
/*     */   }
/*     */   
/*     */   public Hashtable getChatsType(int paramInt) {
/* 349 */     Hashtable<Object, Object> hashtable = new Hashtable<>();
/* 350 */     String str1 = "";
/* 351 */     String str2 = "";
/* 352 */     if (paramInt == 1) {
/*     */       try {
/* 354 */         String str3 = "";
/* 355 */         String str4 = "";
/* 356 */         ArrayList<String> arrayList1 = (ArrayList)this.otherPara.get("sqlfieldidList");
/* 357 */         ArrayList<String> arrayList2 = (ArrayList)this.otherPara.get("sqlcontentList");
/* 358 */         if (arrayList1 != null && arrayList1.size() > 0) {
/* 359 */           for (byte b = 0; b < arrayList1.size(); b++) {
/* 360 */             String str9 = Util.null2String(arrayList1.get(b)).trim();
/* 361 */             String str10 = Util.null2String(arrayList2.get(b)).trim();
/* 362 */             if (!"".equals(str10) && 
/* 363 */               str10.indexOf("$-5$") > -1) {
/* 364 */               str4 = str4 + str9 + ",";
/*     */             }
/*     */           } 
/*     */           
/* 368 */           if (str4.length() > 0) {
/* 369 */             str3 = " onclick=\"fieldAttrOperate.doSqlFieldAjax(this,'" + str4.substring(0, str4.length() - 1) + "')\" ";
/*     */           }
/*     */         } 
/* 372 */         int i = Util.getIntValue(this.fieldvalue, 0);
/* 373 */         String str5 = "";
/* 374 */         String str6 = "";
/* 375 */         String str7 = "";
/* 376 */         String str8 = "";
/* 377 */         switch (i) {
/*     */           case 0:
/* 379 */             str5 = " checked ";
/* 380 */             str8 = SystemEnv.getHtmlLabelName(19782, this.languageid);
/*     */             break;
/*     */           
/*     */           case 1:
/* 384 */             str6 = " checked ";
/* 385 */             str8 = SystemEnv.getHtmlLabelName(26928, this.languageid);
/*     */             break;
/*     */         } 
/*     */         
/* 389 */         if (this.isview == 1) {
/* 390 */           if (this.isviewonly == 0) {
/* 391 */             if (this.isedit == 1) {
/* 392 */               str1 = str1 + "<input type=\"radio\" value=\"0\" name=\"chatsType\" " + str5 + "" + str3 + ">" + SystemEnv.getHtmlLabelName(19782, this.languageid) + "";
/* 393 */               str1 = str1 + "<input type=\"radio\" value=\"1\" name=\"chatsType\" " + str6 + "" + str3 + ">" + SystemEnv.getHtmlLabelName(26928, this.languageid) + "";
/*     */             } else {
/* 395 */               str1 = str8 + "<input type=\"hidden\" name=\"chatsType\" value=\"" + this.fieldvalue + "\" />\n";
/*     */             } 
/*     */           } else {
/* 398 */             str1 = str8 + "\n";
/*     */           }
/*     */         
/* 401 */         } else if (this.isviewonly == 0) {
/* 402 */           str1 = str1 + "<input type=\"hidden\" name=\"chatsType\" value=\"" + this.fieldvalue + "\" />";
/*     */         }
/*     */       
/* 405 */       } catch (Exception exception) {
/* 406 */         writeLog(exception);
/*     */       } 
/*     */     }
/* 409 */     hashtable.put("jsStr", str2);
/* 410 */     hashtable.put("inputStr", str1);
/* 411 */     return hashtable;
/*     */   }
/*     */   
/*     */   public Hashtable getRemark() {
/* 415 */     Hashtable<Object, Object> hashtable = new Hashtable<>();
/* 416 */     String str1 = "";
/* 417 */     String str2 = "";
/* 418 */     boolean bool = false;
/*     */     try {
/* 420 */       int i = Util.getIntValue((String)this.otherPara.get("requestid"));
/* 421 */       String[] arrayOfString1 = (String[])this.otherPara.get("workflowPhrases");
/* 422 */       String[] arrayOfString2 = (String[])this.otherPara.get("workflowPhrasesContent");
/*     */       
/* 424 */       String str3 = Util.null2String((String)this.otherPara.get("isFormSignature"));
/* 425 */       String str4 = Util.null2String((String)this.otherPara.get("isSignDoc_edit"));
/* 426 */       String str5 = Util.null2String((String)this.otherPara.get("isannexupload_edit"));
/* 427 */       String str6 = Util.null2String((String)this.otherPara.get("isSignWorkflow_edit"));
/* 428 */       String str7 = Util.null2String((String)this.otherPara.get("annexdocCategory_edit"));
/* 429 */       String str8 = Util.null2String((String)this.otherPara.get("remarkLocation"));
/*     */       
/* 431 */       if (!"1".equals(str3)) {
/*     */         
/* 433 */         WorkflowPhrase workflowPhrase = new WorkflowPhrase();
/* 434 */         boolean bool1 = workflowPhrase.hasPrivateRight();
/* 435 */         str1 = str1 + "<script type=\"text/javascript\">";
/* 436 */         str1 = str1 + "var _hasPrivateRight = " + bool1 + ";";
/* 437 */         str1 = str1 + "</script>";
/* 438 */         str1 = str1 + "<script type=\"text/javascript\" src=\"/ueditor/custbtn/appwfPhraseBtn_wev8.js\"></script>";
/* 439 */         str1 = str1 + "<link type=\"text/css\" rel=\"stylesheet\" href=\"/js/ecology8/weaverautocomplete/css/weaverautocomplete_wev8.css\"></script>";
/* 440 */         str1 = str1 + "<script type=\"text/javascript\" src=\"/js/ecology8/weaverautocomplete/weaverautocomplete_wev8.js\"></script>";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */         
/* 454 */         String str12 = Util.null2String((String)this.otherPara.get("workflowid"));
/* 455 */         String str13 = Util.null2String((String)this.otherPara.get("isbill"));
/* 456 */         int i1 = 0;
/* 457 */         if (str13.equals("1")) {
/* 458 */           RecordSet recordSet = new RecordSet();
/* 459 */           if (!str12.equals("")) {
/* 460 */             recordSet.executeSql("select formid from workflow_base where id=" + str12);
/*     */           }
/* 462 */           if (recordSet.next()) {
/* 463 */             i1 = Util.getIntValue(recordSet.getString("formid"), 0);
/* 464 */             if (i1 > 0) {
/*     */ 
/*     */ 
/*     */ 
/*     */               
/* 469 */               bool = true;
/*     */             } else {
/* 471 */               bool = false;
/*     */             } 
/*     */           } 
/*     */         } 
/*     */ 
/*     */         
/* 477 */         if (!bool) {
/* 478 */           str1 = str1 + "<script type='text/javascript' charset='UTF-8' src='/ueditor/custbtn/applocation_wev8.js'></script>";
/* 479 */           str1 = str1 + "<script type='text/javascript' charset='UTF-8' src='/ueditor/custbtn/mapOperation.js'></script>";
/* 480 */           str1 = str1 + "<input type='hidden' name='remarkLocation' id='remarkLocation' value=''></input>";
/*     */           
/* 482 */           if (!str8.equals("")) {
/* 483 */             str1 = str1 + "<script>\njQuery(function(){\njQuery('#remarkLocation').val('" + str8 + "');\n})\n </script>\n";
/*     */           }
/*     */         } 
/*     */       } 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 491 */       if ("1".equals(str6) || "1".equals(str5) || "1".equals(str4)) {
/* 492 */         if ("1".equals(str5)) {
/* 493 */           str1 = str1 + "<script type=\"text/javascript\" src=\"/ueditor/custbtn/appwf_fileupload_wev8.js\"></script>\n";
/*     */         }
/*     */         
/* 496 */         if ("1".equals(str4)) {
/* 497 */           str1 = str1 + "<script type=\"text/javascript\" src=\"/ueditor/custbtn/appwf_doc_wev8.js\"></script>\n";
/*     */         }
/* 499 */         if ("1".equals(str6)) {
/* 500 */           str1 = str1 + "<script type=\"text/javascript\" src=\"/ueditor/custbtn/appwf_wf_wev8.js\"></script>\n";
/*     */         }
/*     */       } 
/*     */       
/* 504 */       str1 = str1 + "<table cellspacing=\"0\" cellpadding=\"0\" width=\"100%\" border=\"0\" style=\"margin-bottom:2px !important;\">\n";
/* 505 */       str1 = str1 + "<colgroup>\n";
/* 506 */       str1 = str1 + "<col width=\"20%\"></col>\n";
/* 507 */       str1 = str1 + "<col width=\"80%\"></col>\n";
/* 508 */       str1 = str1 + "</colgroup>\n";
/* 509 */       str1 = str1 + "<tbody";
/* 510 */       if ("1".equals(this.otherPara.get("isHideInput"))) {
/* 511 */         str1 = str1 + " style='display:none;'";
/*     */       }
/* 513 */       str1 = str1 + ">\n";
/* 514 */       str1 = str1 + "<tr>\n";
/*     */ 
/*     */       
/* 517 */       str1 = str1 + "<td width=\"100%\" style=\"border:1px solid rgb(204, 204, 204);padding:0px;text-align:left;\" colspan=\"2\">\n";
/*     */       
/* 519 */       str1 = str1 + "<select notBeauty=true class=\"Inputstyle\" id=\"phraseselect\" name=\"phraseselect\" style=\"display:none;margin-right:2px;margin-top:2px;margin-bottom:2px;\" onChange=\"onAddPhrase(this.value)\" onmousewheel=\"return false;\">\n";
/* 520 */       str1 = str1 + "<option value=\"\">－－" + SystemEnv.getHtmlLabelName(22409, this.languageid) + "－－</option>\n";
/* 521 */       if (arrayOfString1 != null && arrayOfString1.length > 0) {
/* 522 */         for (byte b = 0; b < arrayOfString1.length; b++) {
/* 523 */           String str = arrayOfString1[b];
/* 524 */           str1 = str1 + "<option value=\"" + arrayOfString2[b] + "\">" + str + "</option>\n";
/*     */         } 
/*     */       }
/* 527 */       str1 = str1 + "</select>\n";
/*     */       
/* 529 */       str1 = str1 + "<input type=\"hidden\" id=\"remarkText10404\" name=\"remarkText10404\" temptitle=\"" + SystemEnv.getHtmlLabelName(17614, this.languageid) + "\" value=\"\">\n";
/* 530 */       str1 = str1 + "<textarea class=\"Inputstyle\" id=\"remark\" name=\"remark\" rows=\"4\" cols=\"40\" style=\"width:100%;margin-bottom:2px;margin-right:2px;height:100px;\" temptitle=\"" + SystemEnv.getHtmlLabelName(17614, this.languageid) + "\" ";
/* 531 */       if (this.ismand == 1) {
/* 532 */         str1 = str1 + " viewtype='1' onChange=\"checkinput('remark','remarkSpan')\" ";
/*     */       }
/* 534 */       str1 = str1 + ">";
/* 535 */       str1 = str1 + Util.toHtmltextarea(Util.encodeAnd(this.fieldvalue)) + "</textarea>\n";
/* 536 */       str1 = str1 + "<script defer>\n";
/* 537 */       str1 = str1 + "function funcremark_log(){\n";
/*     */       
/* 539 */       str1 = str1 + "if (window.__htmlhasuedit == true ) {\n";
/* 540 */       str1 = str1 + "    setTimeout(function () {\n";
/* 541 */       str1 = str1 + "        funcremark_log();\n";
/* 542 */       str1 = str1 + "    }, 1000);\n";
/* 543 */       str1 = str1 + "    return;\n";
/* 544 */       str1 = str1 + "}";
/*     */ 
/*     */       
/* 547 */       str1 = str1 + "\tvar textArea_Obj=jQuery(\"#remark\").closest(\"table\").closest(\"td\");\n";
/* 548 */       str1 = str1 + "\ttextArea_Obj.attr(\"width\",textArea_Obj.width());\n";
/* 549 */       str1 = str1 + "    var _uEditor=UEUtil.initRemark('remark', null, '100%');\r\n";
/*     */       
/* 551 */       str1 = str1 + "\t_uEditor.ready(function(){\t\t\n";
/* 552 */       str1 = str1 + "\tjQuery(\".edui-for-wfannexbutton\").children(\"div\").children(\"div\").children(\"div\").children(\".edui-metro\").addClass(\"wfres_1\");\t\n";
/*     */ 
/*     */       
/* 555 */       str1 = str1 + "\tjQuery(\".edui-for-wfdocbutton\").children(\"div\").children(\"div\").children(\"div\").children(\".edui-metro\").addClass(\"wfres_2\");\t\n";
/* 556 */       str1 = str1 + "\tjQuery(\".edui-for-wfwfbutton\").children(\"div\").children(\"div\").children(\"div\").children(\".edui-metro\").addClass(\"wfres_3\");\t\t\n";
/* 557 */       if (!bool) {
/* 558 */         str1 = str1 + "if(jQuery('#remarkLocation').val() !=\"\" && jQuery('#remarkLocation').val() != null && jQuery('#remarkLocation').val() != \"null\"){\njQuery('.edui-for-wflocatebutton').children('div').children('div').children('div').children('.edui-box').removeClass('edui-metro').removeClass('wflocate1').addClass('wflocate2');}\n";
/*     */       }
/*     */       
/* 561 */       str1 = str1 + "\t});\t\t\n";
/*     */ 
/*     */       
/* 564 */       if (this.ismand == 1)
/*     */       {
/* 566 */         str1 = str1 + "    UEUtil.checkRequired(\"remark\");\n";
/*     */       }
/*     */       
/* 569 */       str1 = str1 + "}\n";
/*     */       
/* 571 */       StringBuffer stringBuffer = new StringBuffer();
/* 572 */       stringBuffer.append("").append("if(window.addEventListener){").append("\n");
/* 573 */       stringBuffer.append("\t").append("window.addEventListener(\"load\",funcremark_log,false);").append("\n");
/* 574 */       stringBuffer.append("").append("}else if(window.attachEvent){").append("\n");
/* 575 */       stringBuffer.append("\t").append("window.attachEvent(\"onload\",funcremark_log);").append("\n");
/* 576 */       stringBuffer.append("").append("}else{").append("\n");
/* 577 */       stringBuffer.append("\t").append("window.onload=funcremark_log;").append("\n");
/* 578 */       stringBuffer.append("").append("}").append("\n");
/* 579 */       str1 = str1 + stringBuffer.toString();
/* 580 */       str1 = str1 + "</script>\n";
/*     */       
/* 582 */       str1 = str1 + "<td style=\"text-align:left;\"><span id=\"remarkSpan\">\n";
/* 583 */       if (this.ismand == 1) {
/* 584 */         str1 = str1 + "<img src=\"/images/BacoError_wev8.gif\" align=absmiddle>\n";
/*     */       }
/* 586 */       str1 = str1 + "</span></td>\n";
/* 587 */       str1 = str1 + "</td>\n";
/* 588 */       str1 = str1 + "</tr>\n";
/*     */       
/* 590 */       int j = 0;
/* 591 */       int k = 0;
/* 592 */       int m = 0;
/*     */       
/* 594 */       if ("1".equals(str5) && str7 != null && !str7.equals("")) {
/* 595 */         j = Util.getIntValue(str7.substring(0, str7.indexOf(',')));
/* 596 */         k = Util.getIntValue(str7.substring(str7.indexOf(',') + 1, str7.lastIndexOf(',')));
/* 597 */         m = Util.getIntValue(str7.substring(str7.lastIndexOf(',') + 1));
/*     */       } 
/* 599 */       SecCategoryComInfo secCategoryComInfo = new SecCategoryComInfo();
/* 600 */       int n = Util.getIntValue(secCategoryComInfo.getMaxUploadFileSize("" + m), 5);
/* 601 */       if (n <= 0) {
/* 602 */         n = 5;
/*     */       }
/* 604 */       String str9 = Util.null2String((String)this.otherPara.get("annexdocids"));
/* 605 */       String str10 = Util.null2String((String)this.otherPara.get("signdocids"));
/* 606 */       String str11 = Util.null2String((String)this.otherPara.get("signworkflowids"));
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 868 */       str1 = str1 + "</tbody>\n";
/* 869 */       str1 = str1 + "</table>\n";
/*     */       
/* 871 */       str1 = str1 + "\n";
/* 872 */     } catch (Exception exception) {
/* 873 */       writeLog(exception);
/*     */     } 
/* 875 */     hashtable.put("jsStr", str2);
/* 876 */     hashtable.put("inputStr", str1);
/* 877 */     return hashtable;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static void main(String[] paramArrayOfString) {}
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getFieldvalue() {
/* 889 */     return this.fieldvalue;
/*     */   }
/*     */   
/*     */   public void setFieldvalue(String paramString) {
/* 893 */     this.fieldvalue = paramString;
/*     */   }
/*     */   
/*     */   public int getIsviewonly() {
/* 897 */     return this.isviewonly;
/*     */   }
/*     */   
/*     */   public void setIsviewonly(int paramInt) {
/* 901 */     this.isviewonly = paramInt;
/*     */   }
/*     */   
/*     */   public int getIsview() {
/* 905 */     return this.isview;
/*     */   }
/*     */   
/*     */   public void setIsview(int paramInt) {
/* 909 */     this.isview = paramInt;
/*     */   }
/*     */   
/*     */   public int getIsedit() {
/* 913 */     return this.isedit;
/*     */   }
/*     */   
/*     */   public void setIsedit(int paramInt) {
/* 917 */     this.isedit = paramInt;
/*     */   }
/*     */   
/*     */   public int getIsmand() {
/* 921 */     return this.ismand;
/*     */   }
/*     */   
/*     */   public void setIsmand(int paramInt) {
/* 925 */     this.ismand = paramInt;
/*     */   }
/*     */   
/*     */   public int getLanguageid() {
/* 929 */     return this.languageid;
/*     */   }
/*     */   
/*     */   public void setLanguageid(int paramInt) {
/* 933 */     this.languageid = paramInt;
/*     */   }
/*     */   
/*     */   public Hashtable getOtherPara() {
/* 937 */     return this.otherPara;
/*     */   }
/*     */   
/*     */   public void setOtherPara(Hashtable paramHashtable) {
/* 941 */     this.otherPara = paramHashtable;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/field/SystemElement.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */