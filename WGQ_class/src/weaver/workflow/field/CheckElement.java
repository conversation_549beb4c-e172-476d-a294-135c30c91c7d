/*     */ package weaver.workflow.field;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.Hashtable;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CheckElement
/*     */   extends BaseBean
/*     */   implements HtmlElement
/*     */ {
/*     */   public static void main(String[] paramArrayOfString) {}
/*     */   
/*     */   public Hashtable getHtmlElementString(int paramInt1, String paramString1, int paramInt2, String paramString2, int paramInt3, int paramInt4, int paramInt5, String paramString3, int paramInt6, int paramInt7, int paramInt8, int paramInt9, User paramUser, Hashtable paramHashtable) {
/*  40 */     Hashtable<Object, Object> hashtable = new Hashtable<Object, Object>();
/*  41 */     String str = "";
/*     */     
/*     */     try {
/*  44 */       String str1 = "";
/*  45 */       ArrayList<String> arrayList = (ArrayList)paramHashtable.get("sapfieldidList");
/*  46 */       if (arrayList != null && arrayList.size() > 0) {
/*  47 */         for (byte b = 0; b < arrayList.size(); b++) {
/*  48 */           String str2 = Util.null2String(arrayList.get(b));
/*  49 */           String str3 = str2.substring(0, str2.indexOf("-"));
/*  50 */           String str4 = str2.substring(str2.indexOf("-") + 1);
/*  51 */           if (("" + paramInt1).equals(str3)) {
/*  52 */             str1 = str1 + " doSAPField(" + str4 + ",this); ";
/*     */           }
/*     */         } 
/*     */       }
/*     */       
/*  57 */       int i = paramUser.getLanguage();
/*  58 */       int j = Util.getIntValue(paramString3, 0);
/*  59 */       if (paramInt4 == 0) {
/*  60 */         ArrayList arrayList1 = (ArrayList)paramHashtable.get("changefieldsadd");
/*  61 */         String str2 = "";
/*  62 */         if (paramInt6 == 1 || paramInt8 == 0) {
/*  63 */           str2 = " disabled ";
/*     */         } else {
/*  65 */           str2 = " id=\"field" + paramInt1 + "\" name=\"field" + paramInt1 + "\" ";
/*     */         } 
/*  67 */         String str3 = Util.null2String((String)paramHashtable.get("trrigerfield"));
/*  68 */         if (paramInt7 == 1) {
/*  69 */           str = str + "<input type=\"checkbox\" class=\"Inputstyle\" viewtype=\"" + paramInt9 + "\" temptitle=\"" + Util.toScreen(paramString2, i) + "\" value=\"1\" " + str2 + " ";
/*  70 */           if (str3.indexOf("field" + paramInt1) >= 0) {
/*  71 */             str = str + " onChange=\"datainput('field" + paramInt1 + "');\" ";
/*     */           }
/*  73 */           if (j == 1) {
/*  74 */             str = str + " checked ";
/*     */           }
/*  76 */           str = str + " onclick=\"" + str1 + "\" ";
/*  77 */           str = str + ">\n";
/*  78 */           if (paramInt6 == 1 || paramInt8 == 0) {
/*  79 */             str = str + "<input type=\"hidden\" id=\"field" + paramInt1 + "\" name=\"field" + paramInt1 + "\" value=\"" + j + "\" >\n";
/*     */           }
/*     */         } else {
/*     */           
/*  83 */           str = str + "<input type=\"hidden\" id=\"field" + paramInt1 + "\" name=\"field" + paramInt1 + "\" value=\"" + j + "\" >";
/*     */         } 
/*     */         
/*  86 */         if (arrayList1.indexOf("" + paramInt1) >= 0) {
/*  87 */           str = str + "<input type=\"hidden\" id=\"oldfieldview" + paramInt1 + "\" name=\"oldfieldview" + paramInt1 + "\" value=\"" + (paramInt7 + paramInt8 + paramInt9) + "\" >";
/*     */         }
/*  89 */         if (!str1.equals("")) {
/*  90 */           if (paramInt6 == 1 || paramInt8 == 0) {
/*  91 */             str = "<input class=\"btn_getsapdata\" type=\"button\" id=\"field" + paramInt1 + "\" name=\"field" + paramInt1 + "\" value=\"" + paramString2 + "\">";
/*     */           } else {
/*  93 */             str = "<input class=\"btn_getsapdata\" type=\"button\" id=\"field" + paramInt1 + "\" name=\"field" + paramInt1 + "\" value=\"" + paramString2 + "\" onclick=\"" + str1 + "\">";
/*     */           } 
/*     */         }
/*     */       } else {
/*  97 */         String str2 = Util.null2String((String)paramHashtable.get("derecorderindex"));
/*  98 */         ArrayList arrayList1 = (ArrayList)paramHashtable.get("changedefieldsadd");
/*  99 */         String str3 = "";
/* 100 */         if (paramInt6 == 1 || paramInt8 == 0) {
/* 101 */           str3 = " disabled ";
/*     */         } else {
/* 103 */           str3 = " id='field" + paramInt1 + "_" + str2 + "' name='field" + paramInt1 + "_" + str2 + "' ";
/*     */         } 
/* 105 */         str = str + "<input type='checkbox' notBeauty=true class='Inputstyle' viewtype='" + paramInt9 + "' temptitle='" + Util.toScreen(paramString2, i) + "' value='1' " + str3 + " ";
/* 106 */         if (j == 1) {
/* 107 */           str = str + " checked ";
/*     */         }
/* 109 */         str = str + ">";
/* 110 */         if (paramInt6 == 1 || paramInt8 == 0) {
/* 111 */           str = str + "<input type='hidden' id='field" + paramInt1 + "_" + str2 + "' name='field" + paramInt1 + "_" + str2 + "' value='" + j + "' >";
/*     */         }
/* 113 */         if (arrayList1.indexOf("" + paramInt1) >= 0) {
/* 114 */           str = str + "<input type='hidden' name='oldfieldview" + paramInt1 + "_" + str2 + "' value='" + (paramInt7 + paramInt8 + paramInt9) + "' />";
/*     */         }
/* 116 */         if (!str1.equals("")) {
/* 117 */           if (paramInt6 == 1 || paramInt8 == 0) {
/* 118 */             str = "<input class='btn_getsapdata' type='button' id='field" + paramInt1 + "_" + str2 + "' name='field" + paramInt1 + "_" + str2 + "' value='" + paramString2 + "'>";
/*     */           } else {
/* 120 */             str = "<input class='btn_getsapdata' type='button' id='field" + paramInt1 + "_" + str2 + "' name='field" + paramInt1 + "_" + str2 + "' value='" + paramString2 + "' onclick='" + str1 + "'>";
/*     */           } 
/*     */         }
/*     */       } 
/*     */       
/* 125 */       if ("1".equals(Util.null2String(paramHashtable.get("pdfprint")))) {
/* 126 */         str = (j == 1) ? SystemEnv.getHtmlLabelName(82677, Util.getIntValue("" + paramUser.getLanguage(), 7)) : SystemEnv.getHtmlLabelName(82676, Util.getIntValue("" + paramUser.getLanguage(), 7));
/*     */       }
/* 128 */     } catch (Exception exception) {
/* 129 */       str = "";
/* 130 */       writeLog(exception);
/*     */     } 
/* 132 */     hashtable.put("inputStr", str);
/* 133 */     return hashtable;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/field/CheckElement.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */