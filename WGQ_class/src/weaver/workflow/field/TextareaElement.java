/*     */ package weaver.workflow.field;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.Hashtable;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpSession;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.GCONST;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class TextareaElement
/*     */   extends BaseBean
/*     */   implements HtmlElement
/*     */ {
/*     */   public static void main(String[] paramArrayOfString) {}
/*     */   
/*     */   public Hashtable getHtmlElementString(int paramInt1, String paramString1, int paramInt2, String paramString2, int paramInt3, int paramInt4, int paramInt5, String paramString3, int paramInt6, int paramInt7, int paramInt8, int paramInt9, User paramUser, Hashtable paramHashtable) {
/*  49 */     Hashtable<Object, Object> hashtable = new Hashtable<>();
/*  50 */     String str1 = "";
/*  51 */     String str2 = "";
/*     */     try {
/*  53 */       String str3 = Util.null2String(GCONST.getContextPath());
/*  54 */       int i = paramUser.getLanguage();
/*  55 */       if (paramInt3 > 0) {
/*  56 */         paramInt3 /= 2;
/*     */       }
/*  58 */       String str4 = "";
/*  59 */       if (paramInt6 == 0 && paramInt7 == 1 && paramInt8 == 1 && paramInt9 == 1 && "".equals(paramString3)) {
/*  60 */         str4 = "<img src='/images/BacoError_wev8.gif' align='absmiddle'>";
/*     */       }
/*  62 */       int j = Util.getIntValue((String)paramHashtable.get("isprint"), 0);
/*  63 */       int k = Util.getIntValue((String)paramHashtable.get("fieldheight"), 4);
/*     */       
/*  65 */       String str5 = "";
/*  66 */       String str6 = "";
/*  67 */       String str7 = "";
/*  68 */       String str8 = "";
/*  69 */       String str9 = "";
/*  70 */       String str10 = "";
/*  71 */       String str11 = "";
/*  72 */       int m = Util.getIntValue((String)paramHashtable.get("version"), 0);
/*  73 */       if (m == 2) {
/*  74 */         if (paramHashtable.containsKey("_propertyChangeFormulaField_") && "y"
/*  75 */           .equals(Util.null2String(paramHashtable.get("_propertyChangeFormulaField_")))) {
/*  76 */           str5 = "formulaTrigger(this);";
/*     */         }
/*  78 */         if (paramHashtable.containsKey("_focusFormulaField_") && "y"
/*  79 */           .equals(Util.null2String(paramHashtable.get("_focusFormulaField_")))) {
/*  80 */           str6 = "formulaTrigger(this);";
/*     */         }
/*  82 */         if (paramHashtable.containsKey("_blurFormulaField_") && "y"
/*  83 */           .equals(Util.null2String(paramHashtable.get("_blurFormulaField_")))) {
/*  84 */           str7 = "formulaTrigger(this);";
/*     */         }
/*  86 */         if (paramHashtable.containsKey("_clickFormulaField_") && "y"
/*  87 */           .equals(Util.null2String(paramHashtable.get("_clickFormulaField_")))) {
/*  88 */           str8 = "formulaTrigger(this);";
/*     */         }
/*  90 */         if (paramHashtable.containsKey("_doubleClickFormulaField_") && "y"
/*  91 */           .equals(Util.null2String(paramHashtable.get("_doubleClickFormulaField_")))) {
/*  92 */           str9 = "formulaTrigger(this);";
/*     */         }
/*  94 */         if (paramHashtable.containsKey("_mouseOverFormulaField_") && "y"
/*  95 */           .equals(Util.null2String(paramHashtable.get("_mouseOverFormulaField_")))) {
/*  96 */           str10 = "formulaTrigger(this);";
/*     */         }
/*  98 */         if (paramHashtable.containsKey("_mouseOutFormulaField_") && "y"
/*  99 */           .equals(Util.null2String(paramHashtable.get("_mouseOutFormulaField_")))) {
/* 100 */           str11 = "formulaTrigger(this);";
/*     */         }
/*     */       } 
/* 103 */       if (paramInt4 == 0) {
/* 104 */         String str12 = Util.null2String((String)paramHashtable.get("trrigerfield"));
/* 105 */         ArrayList arrayList = (ArrayList)paramHashtable.get("changefieldsadd");
/* 106 */         HttpServletRequest httpServletRequest = (HttpServletRequest)paramHashtable.get("httprequest");
/* 107 */         HttpSession httpSession = null;
/*     */         try {
/* 109 */           httpSession = httpServletRequest.getSession(false);
/* 110 */         } catch (Exception exception) {}
/* 111 */         String str13 = "";
/* 112 */         String str14 = "";
/* 113 */         ArrayList<String> arrayList1 = (ArrayList)paramHashtable.get("sqlfieldidList");
/* 114 */         ArrayList<String> arrayList2 = (ArrayList)paramHashtable.get("sqlcontentList");
/* 115 */         if (arrayList1 != null && arrayList1.size() > 0) {
/* 116 */           for (byte b = 0; b < arrayList1.size(); b++) {
/* 117 */             String str15 = Util.null2String(arrayList1.get(b)).trim();
/* 118 */             String str16 = Util.null2String(arrayList2.get(b)).trim();
/* 119 */             if (!"".equals(str16) && 
/* 120 */               str16.indexOf("$" + paramInt1 + "$") > -1 && 
/* 121 */               str16.indexOf("$" + paramInt1 + "$") > -1) {
/* 122 */               str14 = str14 + str15 + ",";
/*     */             }
/*     */           } 
/*     */ 
/*     */           
/* 127 */           if (str14.length() > 0) {
/* 128 */             str13 = "fieldAttrOperate.doSqlFieldAjax(this,'" + str14.substring(0, str14.length() - 1) + "');";
/*     */           }
/*     */         } 
/* 131 */         if (paramInt7 == 1) {
/* 132 */           if (paramInt6 == 0) {
/* 133 */             if (paramInt8 == 1) {
/* 134 */               str1 = str1 + "<textarea class=\"Inputstyle\" temptype=\"" + paramInt2 + "\" viewtype=\"" + paramInt9 + "\" temptitle=\"" + Util.toScreen(paramString2, i) + "\" id=\"field" + paramInt1 + "\" name=\"field" + paramInt1 + "\" rows=\"" + k + "\" ";
/* 135 */               str1 = str1 + " onchange=\"checkinput2('field" + paramInt1 + "','field" + paramInt1 + "span',this.getAttribute('viewtype'));checkLength('field" + paramInt1 + "','" + paramInt3 + "','" + Util.toScreen(paramString2, i) + "','" + SystemEnv.getHtmlLabelName(20246, i) + "','" + SystemEnv.getHtmlLabelName(20247, i) + "');" + str5 + "\" cols=\"40\"";
/* 136 */               str1 = str1 + "  onpropertychange=\"" + str5 + str13 + "\" _listener=\"" + str13 + "\"";
/* 137 */               str1 = str1 + " oninput=\"" + str5 + "\" onfocus=\"" + str6 + "\" onclick=\"" + str8 + "\" ";
/* 138 */               str1 = str1 + " ondblclick=\"" + str9 + "\" onmouseover=\"" + str10 + "\" onmouseout=\"" + str11 + "\" ";
/* 139 */               if (str12.indexOf("field" + paramInt1) >= 0) {
/* 140 */                 str1 = str1 + " onBlur=\"datainput('field" + paramInt1 + "');" + str7 + "\" ";
/*     */               }
/* 142 */               if (paramInt2 == 2) {
/* 143 */                 str1 = str1 + " style=\"width:90%;word-break:break-all;word-wrap:break-word\">";
/*     */               } else {
/* 145 */                 str1 = str1 + " style=\"width:80%;word-break:break-all;word-wrap:break-word\">";
/*     */               } 
/* 147 */               if (paramInt2 == 2) {
/* 148 */                 str1 = str1 + Util.toHtmltextarea(Util.encodeAnd(paramString3));
/*     */               } else {
/* 150 */                 str1 = str1 + Util.toScreenToEdit(paramString3, i);
/*     */               } 
/* 152 */               str1 = str1 + "</textarea>\n";
/* 153 */               str2 = str2 + "\t$G(\"htmlfieldids\").value += \"field" + paramInt1 + ";" + Util.toScreen(paramString2, i) + ";" + paramInt2 + ",\";\n";
/* 154 */               str1 = str1 + "<span id=\"field" + paramInt1 + "span\">" + str4 + "</span>";
/* 155 */               if (paramInt2 == 2) {
/* 156 */                 str1 = str1 + "<script>\n";
/* 157 */                 str1 = str1 + "function funcField" + paramInt1 + "(){\n";
/*     */                 
/* 159 */                 str1 = str1 + "var textArea_Obj=jQuery(\"#field" + paramInt1 + "\").closest(\"td\");\n";
/* 160 */                 str1 = str1 + "textArea_Obj.attr(\"width\",textArea_Obj.width());\n";
/*     */                 
/* 162 */                 str1 = str1 + "UEUtil.initEditor('field" + paramInt1 + "')\r\n";
/* 163 */                 if (paramInt8 == 1)
/*     */                 {
/* 165 */                   str1 = str1 + "    UEUtil.checkRequired('field" + paramInt1 + "');\n";
/*     */                 }
/*     */                 
/* 168 */                 str1 = str1 + "}\n";
/*     */                 
/* 170 */                 str2 = str2 + "\twindow.__htmlhasuedit = true; \n";
/* 171 */                 str2 = str2 + "\tif (window.addEventListener){\n";
/* 172 */                 str2 = str2 + "\t    window.addEventListener(\"load\", funcField" + paramInt1 + ", false);\n";
/* 173 */                 str2 = str2 + "\t}else if (window.attachEvent){\n";
/* 174 */                 str2 = str2 + "\t    window.attachEvent(\"onload\", funcField" + paramInt1 + ");\n";
/* 175 */                 str2 = str2 + "\t}else{\n";
/* 176 */                 str2 = str2 + "\t    window.onload=funcField" + paramInt1 + ";\n";
/* 177 */                 str2 = str2 + "\t}\n";
/*     */                 
/* 179 */                 str1 = str1 + "</script>\n";
/*     */               }
/*     */             
/* 182 */             } else if (paramInt2 == 2) {
/*     */               try {
/* 184 */                 int n = Util.getIntValue((String)paramHashtable.get("requestid"), 0);
/* 185 */                 int i1 = Util.getIntValue((String)paramHashtable.get("userid"), 0);
/* 186 */                 httpSession.setAttribute("FCKEDDesc_" + n + "_" + i1 + "_" + paramInt1 + "_-1", paramString3);
/* 187 */                 str1 = str1 + "<iframe id=\"FCKiframe" + paramInt1 + "\" name=\"FCKiframe" + paramInt1 + "\" src=\"" + str3 + "/workflow/request/ShowFckEditorDesc.jsp?requestid=" + n + "&userid=" + i1 + "&fieldid=" + paramInt1 + "&rowno=-1\"  width=\"100%\" height=\"100%\" marginheight=\"0\" marginwidth=\"0\" allowTransparency=\"true\" frameborder=\"0\"></iframe>\n";
/* 188 */                 str1 = str1 + "<input type=\"hidden\" id=\"field" + paramInt1 + "\" name=\"field" + paramInt1 + "\" value=\"" + Util.toHtmltextarea(Util.encodeAnd(paramString3)) + "\" >\n";
/* 189 */                 str1 = str1 + "<span id=\"field" + paramInt1 + "span\" style=\"word-wrap:break-word;display:none\"></span>";
/* 190 */               } catch (Exception exception) {}
/*     */             } else {
/* 192 */               str1 = str1 + "<span id=\"field" + paramInt1 + "span\" style=\"word-break:break-all;word-wrap:break-word;width:99%;\">" + paramString3 + "</span>";
/* 193 */               str1 = str1 + "<input type=\"hidden\" id=\"field" + paramInt1 + "\" name=\"field" + paramInt1 + "\"   onpropertychange=\"" + str13 + "\" _listener=\"" + str13 + "\" value=\"" + Util.toHtmltextarea(Util.encodeAnd(paramString3)) + "\" >\n";
/*     */             } 
/*     */             
/* 196 */             if (arrayList.indexOf("" + paramInt1) >= 0) {
/* 197 */               str1 = str1 + "<input type=\"hidden\" id=\"oldfieldview" + paramInt1 + "\" name=\"oldfieldview" + paramInt1 + "\" value=\"" + (paramInt7 + paramInt8 + paramInt9) + "\" >";
/*     */             }
/*     */           }
/* 200 */           else if (paramInt2 == 2) {
/* 201 */             if (j == 1) {
/* 202 */               str1 = "<span id=\"field" + paramInt1 + "span\" style=\"word-break:break-all;word-wrap:break-word;width:99%;\">" + Util.null2String(paramString3) + "</span>";
/*     */             } else {
/*     */               try {
/* 205 */                 int n = Util.getIntValue((String)paramHashtable.get("requestid"), 0);
/* 206 */                 int i1 = Util.getIntValue((String)paramHashtable.get("userid"), 0);
/* 207 */                 httpSession.setAttribute("FCKEDDesc_" + n + "_" + i1 + "_" + paramInt1 + "_-1", paramString3);
/* 208 */                 str1 = str1 + "<iframe id=\"FCKiframe" + paramInt1 + "\" name=\"FCKiframe" + paramInt1 + "\" src=\"" + str3 + "/workflow/request/ShowFckEditorDesc.jsp?requestid=" + n + "&userid=" + i1 + "&fieldid=" + paramInt1 + "&rowno=-1\"  width=\"100%\" height=\"100%\" marginheight=\"0\" marginwidth=\"0\" allowTransparency=\"true\" frameborder=\"0\"></iframe>\n";
/* 209 */                 str1 = str1 + "<span id=\"field" + paramInt1 + "span\" style=\"word-break:break-all;word-wrap:break-word;width:99%;\"></span>";
/* 210 */                 str1 = str1 + "<textarea id=\"field" + paramInt1 + "\" name=\"field" + paramInt1 + "\" style=\"display:none\">" + Util.toHtmltextarea(Util.encodeAnd(paramString3)) + "</textarea>\n";
/* 211 */               } catch (Exception exception) {}
/*     */             } 
/*     */           } else {
/* 214 */             str1 = "<span id=\"field" + paramInt1 + "span\" style=\"word-break:break-all;word-wrap:break-word;width:99%;\">" + Util.null2String(paramString3) + "</span>";
/* 215 */             str1 = str1 + "<textarea id=\"field" + paramInt1 + "\" name=\"field" + paramInt1 + "\" style=\"display:none\">" + Util.toHtmltextarea(Util.encodeAnd(paramString3)) + "</textarea>\n";
/*     */           }
/*     */         
/*     */         } else {
/*     */           
/* 220 */           str1 = str1 + "<textarea id=\"field" + paramInt1 + "\" name=\"field" + paramInt1 + "\" style=\"display:none\">" + Util.toHtmltextarea(Util.encodeAnd(paramString3)) + "</textarea>\n";
/*     */         } 
/*     */       } else {
/*     */         
/* 224 */         String str12 = Util.null2String((String)paramHashtable.get("derecorderindex"));
/* 225 */         String str13 = "\"";
/* 226 */         if ("\"+rowindex+\"".equals(str12)) {
/* 227 */           str13 = "\\\"";
/*     */         }
/* 229 */         ArrayList arrayList = (ArrayList)paramHashtable.get("changedefieldsadd");
/* 230 */         String str14 = "";
/* 231 */         String str15 = "";
/* 232 */         ArrayList<String> arrayList1 = (ArrayList)paramHashtable.get("sqlfieldidList");
/* 233 */         ArrayList<String> arrayList2 = (ArrayList)paramHashtable.get("sqlcontentList");
/* 234 */         if (arrayList1 != null && arrayList1.size() > 0) {
/* 235 */           for (byte b = 0; b < arrayList1.size(); b++) {
/* 236 */             String str16 = Util.null2String(arrayList1.get(b)).trim();
/* 237 */             String str17 = Util.null2String(arrayList2.get(b)).trim();
/* 238 */             if (!"".equals(str17) && 
/* 239 */               str17.indexOf("$" + paramInt1 + "$") > -1) {
/* 240 */               str15 = str15 + str16 + ",";
/*     */             }
/*     */             
/* 243 */             if (str16.equals("" + paramInt1) && 
/* 244 */               "\"+rowindex+\"".equals(str12)) {
/* 245 */               str2 = str2 + "\t fieldAttrOperate.pageLoadInitValue('" + paramInt1 + "',rowindex);\n";
/*     */             }
/*     */           } 
/*     */           
/* 249 */           if (str15.length() > 0) {
/* 250 */             str14 = ";fieldAttrOperate.doSqlFieldAjax(this,'" + str15.substring(0, str15.length() - 1) + "')";
/*     */           }
/*     */         } 
/* 253 */         if (paramInt8 == 1 && paramInt6 == 0) {
/* 254 */           str1 = str1 + "<textarea class='Inputstyle' temptype='" + paramInt2 + "' viewtype='" + paramInt9 + "' temptitle='" + paramString2 + "' id='field" + paramInt1 + "_" + str12 + "' name='field" + paramInt1 + "_" + str12 + "' onChange=" + str13 + "checkinput2('field" + paramInt1 + "_" + str12 + "','field" + paramInt1 + "_" + str12 + "span',this.getAttribute('viewtype'));checkLength('field" + paramInt1 + "_" + str12 + "','" + paramInt3 + "','" + paramString2 + "','" + SystemEnv.getHtmlLabelName(20246, i) + "','" + SystemEnv.getHtmlLabelName(20247, i) + "')" + str14 + str13 + " cols='40' rows='" + k + "' style='word-break:break-all;word-wrap:break-word;width:70%;'>";
/* 255 */           str1 = str1 + Util.toExcelData(paramString3);
/* 256 */           str1 = str1 + "</textarea>";
/* 257 */           str1 = str1 + "<span id='field" + paramInt1 + "_" + str12 + "span'>" + str4 + "</span>";
/* 258 */           if (arrayList.indexOf("" + paramInt1) >= 0) {
/* 259 */             str1 = str1 + "<input type='hidden' id='oldfieldview" + paramInt1 + "_" + str12 + "' name='oldfieldview" + paramInt1 + "_" + str12 + "' value='" + (paramInt7 + paramInt8 + paramInt9) + "' />";
/*     */           }
/*     */         } else {
/* 262 */           str1 = str1 + "<span id='field" + paramInt1 + "_" + str12 + "span' style='word-break:break-all;word-wrap:break-word;width:99%;'>" + paramString3 + "</span><textarea class='Inputstyle' style=" + str13 + "display:none" + str13 + " id='field" + paramInt1 + "_" + str12 + "' name='field" + paramInt1 + "_" + str12 + "'>" + Util.toExcelData(paramString3) + "</textarea>";
/*     */         } 
/*     */       } 
/* 265 */     } catch (Exception exception) {
/* 266 */       str1 = "";
/* 267 */       writeLog(exception);
/*     */     } 
/* 269 */     hashtable.put("jsStr", str2);
/* 270 */     hashtable.put("inputStr", str1);
/* 271 */     return hashtable;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/field/TextareaElement.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */