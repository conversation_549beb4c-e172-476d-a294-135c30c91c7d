/*     */ package weaver.workflow.field;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Hashtable;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.workflow.request.RequestManager;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class SelectElement
/*     */   extends BaseBean
/*     */   implements HtmlElement
/*     */ {
/*     */   public static void main(String[] paramArrayOfString) {}
/*     */   
/*     */   public Hashtable getHtmlElementString(int paramInt1, String paramString1, int paramInt2, String paramString2, int paramInt3, int paramInt4, int paramInt5, String paramString3, int paramInt6, int paramInt7, int paramInt8, int paramInt9, User paramUser, Hashtable paramHashtable) {
/*  44 */     Hashtable<Object, Object> hashtable = new Hashtable<>();
/*  45 */     String str1 = "";
/*  46 */     String str2 = "";
/*  47 */     String str3 = "";
/*  48 */     String str4 = "";
/*     */     try {
/*  50 */       int i = Util.getIntValue((String)paramHashtable.get("workflowid"));
/*  51 */       int j = Util.getIntValue((String)paramHashtable.get("detailNumber"));
/*     */       
/*  53 */       int k = Util.getIntValue((String)paramHashtable.get("requestid"), 0);
/*  54 */       int m = Util.getIntValue((String)paramHashtable.get("detailRecordId"), 0);
/*  55 */       int n = Util.getIntValue((String)paramHashtable.get("languageId"), 0);
/*     */ 
/*     */       
/*  58 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  59 */       HashMap<Object, Object> hashMap2 = new HashMap<>();
/*  60 */       HashMap<Object, Object> hashMap3 = new HashMap<>();
/*  61 */       if (paramInt4 == 1) {
/*  62 */         hashMap1 = (HashMap<Object, Object>)paramHashtable.get("_isEnableFnaWfHm_FnaCommon.getIsEnableFnaWfHm_workflowid=" + i + "__requestId=" + k);
/*  63 */         if (hashMap1 == null) {
/*  64 */           hashMap1 = new HashMap<>();
/*     */         }
/*  66 */         hashMap2 = (HashMap<Object, Object>)paramHashtable.get("_isEnableFnaWfHm_fnaBudgetControl.getFnaWfFieldInfo4Expense_workflowid=" + i + "__requestId=" + k);
/*  67 */         if (hashMap2 == null) {
/*  68 */           hashMap2 = new HashMap<>();
/*     */         }
/*  70 */         hashMap3 = (HashMap<Object, Object>)paramHashtable.get("_isEnableFnaWfHm_FnaCommon.qryFnaExpenseRequestRecord_workflowid=" + i + "__requestId=" + k);
/*  71 */         if (hashMap3 == null) {
/*  72 */           hashMap3 = new HashMap<>();
/*     */         }
/*     */       } 
/*     */       
/*  76 */       int i1 = Util.getIntValue((String)paramHashtable.get("isprint"), 0);
/*  77 */       int i2 = Util.getIntValue((String)paramHashtable.get("version"), 0);
/*  78 */       String str5 = "";
/*     */       
/*  80 */       int i3 = paramUser.getLanguage();
/*  81 */       String str6 = Util.null2String((String)paramHashtable.get("trrigerfield"));
/*  82 */       ArrayList arrayList1 = (ArrayList)paramHashtable.get("selfieldsadd");
/*  83 */       ArrayList arrayList2 = (ArrayList)paramHashtable.get("changefieldsadd");
/*  84 */       int i4 = Util.getIntValue((String)paramHashtable.get("workflowid"));
/*  85 */       int i5 = Util.getIntValue((String)paramHashtable.get("nodeid"));
/*  86 */       int i6 = Util.getIntValue((String)paramHashtable.get("isbill"));
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/*  94 */       int i7 = 1;
/*  95 */       int i8 = 1;
/*  96 */       RecordSet recordSet = new RecordSet();
/*     */       
/*  98 */       if (i6 == 0) {
/*     */         
/* 100 */         if (paramInt4 == 0) {
/* 101 */           recordSet.executeQuery("select type,fieldshowtypes from workflow_formdict where id= ?", new Object[] { Integer.valueOf(paramInt1) });
/*     */         } else {
/* 103 */           recordSet.executeQuery("select type,fieldshowtypes from workflow_formdictdetail where id= ?", new Object[] { Integer.valueOf(paramInt1) });
/*     */         } 
/*     */       } else {
/* 106 */         recordSet.executeQuery("select type,fieldshowtypes from workflow_billfield where id=?", new Object[] { Integer.valueOf(paramInt1) });
/*     */       } 
/* 108 */       if (recordSet.next()) {
/* 109 */         i7 = Util.getIntValue(recordSet.getString("type"), 1);
/* 110 */         i8 = Util.getIntValue(recordSet.getString("fieldshowtypes"), 1);
/*     */       } else {
/* 112 */         i7 = paramInt2;
/*     */       } 
/*     */       
/* 115 */       int i9 = Util.getIntValue(paramString3, -1);
/* 116 */       if (i7 != 2) {
/* 117 */         if (i9 == -1) {
/* 118 */           paramString3 = "";
/*     */         } else {
/* 120 */           paramString3 = "" + i9;
/*     */         } 
/*     */       }
/* 123 */       String str7 = "";
/* 124 */       if (paramInt6 == 0 && paramInt7 == 1 && paramInt8 == 1 && paramInt9 == 1) {
/* 125 */         str7 = "<img src='/images/BacoError_wev8.gif' align='absmiddle'>";
/*     */       }
/*     */       
/* 128 */       String str8 = "";
/* 129 */       String str9 = "";
/* 130 */       String str10 = "";
/* 131 */       String str11 = "";
/* 132 */       String str12 = "";
/* 133 */       String str13 = "";
/* 134 */       String str14 = "";
/* 135 */       String str15 = "";
/* 136 */       String str16 = "";
/* 137 */       if (i2 == 2) {
/* 138 */         if (paramHashtable.containsKey("_formula") && "y"
/* 139 */           .equals(Util.null2String(paramHashtable.get("_formula")))) {
/* 140 */           str8 = "formulaTrigger(this);";
/* 141 */           str9 = "formulaTrigger(this, -1);";
/* 142 */         } else if (paramHashtable.containsKey("_propertyChangeFormulaField_") && "y"
/* 143 */           .equals(Util.null2String(paramHashtable.get("_propertyChangeFormulaField_")))) {
/* 144 */           str10 = "formulaTrigger(this);";
/*     */         } 
/* 146 */         if (paramHashtable.containsKey("_focusFormulaField_") && "y"
/* 147 */           .equals(Util.null2String(paramHashtable.get("_focusFormulaField_")))) {
/* 148 */           str11 = "formulaTrigger(this);";
/*     */         }
/* 150 */         if (paramHashtable.containsKey("_blurFormulaField_") && "y"
/* 151 */           .equals(Util.null2String(paramHashtable.get("_blurFormulaField_")))) {
/* 152 */           str12 = "formulaTrigger(this);";
/*     */         }
/* 154 */         if (paramHashtable.containsKey("_clickFormulaField_") && "y"
/* 155 */           .equals(Util.null2String(paramHashtable.get("_clickFormulaField_")))) {
/* 156 */           str13 = "formulaTrigger(this);";
/*     */         }
/* 158 */         if (paramHashtable.containsKey("_doubleClickFormulaField_") && "y"
/* 159 */           .equals(Util.null2String(paramHashtable.get("_doubleClickFormulaField_")))) {
/* 160 */           str14 = "formulaTrigger(this);";
/*     */         }
/* 162 */         if (paramHashtable.containsKey("_mouseOverFormulaField_") && "y"
/* 163 */           .equals(Util.null2String(paramHashtable.get("_mouseOverFormulaField_")))) {
/* 164 */           str15 = "formulaTrigger(this);";
/*     */         }
/* 166 */         if (paramHashtable.containsKey("_mouseOutFormulaField_") && "y"
/* 167 */           .equals(Util.null2String(paramHashtable.get("_mouseOutFormulaField_")))) {
/* 168 */           str16 = "formulaTrigger(this);";
/*     */         }
/*     */       } 
/*     */       
/* 172 */       if (paramInt4 == 0) {
/*     */         
/* 174 */         int i10 = ((Integer)paramHashtable.get("childfieldid_tmp")).intValue();
/* 175 */         int i11 = ((Integer)paramHashtable.get("firstPfieldid_tmp")).intValue();
/* 176 */         boolean bool = ((Boolean)paramHashtable.get("hasPfield")).booleanValue();
/* 177 */         String str17 = "";
/* 178 */         if (i10 != 0) {
/* 179 */           str17 = "changeChildField(this," + paramInt1 + "," + i10 + ");";
/*     */         }
/* 181 */         String str18 = "";
/* 182 */         if (paramInt6 == 1 || paramInt8 == 0) {
/* 183 */           str18 = " id=\"disfield" + paramInt1 + "\" name=\"disfield" + paramInt1 + "\" _printflag='1' disabled ";
/*     */         } else {
/* 185 */           str18 = " id=\"field" + paramInt1 + "\" name=\"field" + paramInt1 + "\" ";
/*     */         } 
/* 187 */         String str19 = "";
/* 188 */         String str20 = "";
/* 189 */         ArrayList<String> arrayList3 = (ArrayList)paramHashtable.get("sqlfieldidList");
/* 190 */         ArrayList<String> arrayList4 = (ArrayList)paramHashtable.get("sqlcontentList");
/* 191 */         if (arrayList3 != null && arrayList3.size() > 0) {
/* 192 */           for (byte b = 0; b < arrayList3.size(); b++) {
/* 193 */             String str21 = Util.null2String(arrayList3.get(b)).trim();
/* 194 */             String str22 = Util.null2String(arrayList4.get(b)).trim();
/* 195 */             if (!"".equals(str22) && 
/* 196 */               str22.indexOf("$" + paramInt1 + "$") > -1 && 
/* 197 */               str22.indexOf("$" + paramInt1 + "$") > -1) {
/* 198 */               str20 = str20 + str21 + ",";
/*     */             }
/*     */           } 
/*     */ 
/*     */           
/* 203 */           if (str20.length() > 0) {
/* 204 */             str19 = "fieldAttrOperate.doSqlFieldAjax(this,'" + str20.substring(0, str20.length() - 1) + "');";
/*     */           }
/*     */         } 
/* 207 */         if (paramInt7 == 1) {
/* 208 */           String str21 = "changeforUpload('" + paramInt1 + "'," + i4 + "," + paramUser.getLanguage() + ");";
/* 209 */           int i12 = 0;
/* 210 */           if (paramInt8 == 1) {
/* 211 */             RequestManager requestManager = new RequestManager();
/* 212 */             int i13 = 0;
/* 213 */             String str = requestManager.getUpLoadTypeForSelect(i4);
/* 214 */             if (!str.equals("")) {
/* 215 */               i13 = Util.getIntValue(str.substring(0, str.indexOf(",")), 0);
/* 216 */               i12 = Integer.valueOf(str.substring(str.indexOf(",") + 1)).intValue();
/*     */             } 
/*     */           } 
/*     */           
/* 220 */           String str22 = "";
/* 221 */           if (str6.indexOf("field" + paramInt1) >= 0) {
/* 222 */             str22 = "datainput('field" + paramInt1 + "');";
/*     */           }
/* 224 */           if (i7 == 1) {
/* 225 */             str1 = str1 + "<select notBeauty=true class=\"Inputstyle\" style=\"height:auto;\" viewtype=\"" + paramInt9 + "\" temptitle=\"" + Util.toScreen(paramString2, i3) + "\" onBlur=\"checkinput2('field" + paramInt1 + "','field" + paramInt1 + "span',this.getAttribute('viewtype'));" + str12 + "\"" + str18 + " ";
/*     */ 
/*     */             
/* 228 */             if (paramInt6 != 1 && paramInt8 != 0) {
/* 229 */               str1 = str1 + " onChange=\"";
/* 230 */               if (arrayList1.indexOf("" + paramInt1) >= 0)
/* 231 */                 str1 = str1 + "changeshowattr('" + paramInt1 + "_0',this.value,-1," + i4 + "," + i5 + ",false);"; 
/* 232 */               str1 = str1 + str17 + str21 + str22 + str19 + str9 + str10;
/* 233 */               str1 = str1 + " onfocus='" + str11 + "' onclick='" + str13 + "'";
/* 234 */               str1 = str1 + " ondblclick='" + str14 + "' onmouseover='" + str15 + "' onmouseout='" + str16 + "' ";
/*     */             }
/* 236 */             else if (arrayList1.indexOf("" + paramInt1) >= 0) {
/* 237 */               str1 = str1 + "changeshowattr('" + paramInt1 + "_0',this.value,-1," + i4 + "," + i5 + ",false);";
/*     */             } 
/* 239 */             str1 = str1 + "\"";
/*     */             
/* 241 */             str1 = str1 + ">\n";
/* 242 */             str1 = str1 + "<option value=\"\"></option>\n";
/*     */           } else {
/* 244 */             str1 = str1 + "<div class=\"radioCheckDiv\">\n";
/*     */           } 
/* 246 */           boolean bool1 = true;
/* 247 */           String str23 = "";
/* 248 */           RecordSet recordSet1 = (RecordSet)paramHashtable.get("selectitemrs");
/* 249 */           recordSet1.beforFirst();
/* 250 */           if (!bool || paramInt8 == 0 || paramInt6 == 1) {
/* 251 */             while (recordSet1.next()) {
/* 252 */               if (paramInt8 != 0 && paramInt6 != 1) {
/* 253 */                 int i13 = Util.getIntValue(recordSet1.getString("cancel"), 0);
/* 254 */                 if (i13 == 1) {
/*     */                   continue;
/*     */                 }
/*     */               } 
/* 258 */               String str24 = Util.null2String(recordSet1.getString("selectvalue"));
/* 259 */               String str25 = Util.toScreen(recordSet1.getString("selectname"), i3);
/* 260 */               String str26 = Util.null2String(recordSet1.getString("isdefault"));
/* 261 */               String str27 = "";
/*     */               
/* 263 */               if (i7 == 1) {
/* 264 */                 if (str24.equals(paramString3) || ("".equals(paramString3) && "y".equals(str26) && k <= 0)) {
/* 265 */                   bool1 = false;
/* 266 */                   str23 = str24;
/* 267 */                   str27 = " selected ";
/* 268 */                   str5 = str25;
/*     */                 } 
/* 270 */                 str1 = str1 + "<option value=\"" + str24 + "\"" + str27 + ">" + str25 + "</option>\n"; continue;
/*     */               } 
/* 272 */               if (i8 == 1) {
/* 273 */                 str1 = str1 + "<div class=\"selectItemHorizontalDiv\">";
/*     */               }
/* 275 */               if (i8 == 2) {
/* 276 */                 str1 = str1 + "<div class=\"selectItemVerticalDiv\" >";
/*     */               }
/* 278 */               if (i7 == 2) {
/* 279 */                 str1 = str1 + "<input type='checkbox' class=\"Inputstyle\" style=\"height:auto;\" viewtype=\"" + paramInt9 + "\" temptitle=\"" + Util.toScreen(paramString2, i3) + "\" onBlur=\"checkinput2('field" + paramInt1 + "','field" + paramInt1 + "span',this.getAttribute('viewtype'));\" ";
/* 280 */                 if (!paramString3.equals("")) {
/* 281 */                   if (("," + paramString3 + ",").indexOf("," + str24 + ",") >= 0) {
/* 282 */                     str1 = str1 + " checked ";
/* 283 */                     if (str23.equals("")) {
/* 284 */                       str23 = str24;
/*     */                     } else {
/* 286 */                       str23 = str23 + "," + str24;
/*     */                     } 
/* 288 */                     bool1 = false;
/*     */                   }
/*     */                 
/* 291 */                 } else if ("y".equals(str26) && k <= 0) {
/* 292 */                   str1 = str1 + " checked ";
/* 293 */                   if (str23.equals("")) {
/* 294 */                     str23 = str24;
/*     */                   } else {
/* 296 */                     str23 = str23 + "," + str24;
/*     */                   } 
/* 298 */                   bool1 = false;
/*     */                 } 
/*     */                 
/* 301 */                 str1 = str1 + " name=\"field" + paramInt1 + "_" + str24 + "\" id=\"field" + paramInt1 + "_" + str24 + "\"";
/*     */               } else {
/* 303 */                 str1 = str1 + "<input type='radio' class=\"Inputstyle\" style=\"height:auto;\" viewtype=\"" + paramInt9 + "\" temptitle=\"" + Util.toScreen(paramString2, i3) + "\" ";
/*     */                 
/* 305 */                 if (!paramString3.equals("")) {
/* 306 */                   if (paramString3.equals(str24)) {
/* 307 */                     str1 = str1 + " checked ";
/* 308 */                     str23 = str24;
/* 309 */                     bool1 = false;
/*     */                   }
/*     */                 
/* 312 */                 } else if (str23.equals("") && "y".equals(str26) && k <= 0) {
/* 313 */                   str1 = str1 + " checked ";
/* 314 */                   str23 = str24;
/* 315 */                   bool1 = false;
/*     */                 } 
/*     */                 
/* 318 */                 str1 = str1 + " name='field" + paramInt1 + "_disp' id='field" + paramInt1 + "_" + str24 + "'";
/*     */               } 
/* 320 */               if (paramInt6 == 1 || paramInt8 == 0) {
/* 321 */                 str1 = str1 + " _printflag='1' disabled ";
/*     */               }
/* 323 */               str1 = str1 + " value=\"" + str24 + "\" ";
/* 324 */               str1 = str1 + " viewtype='" + paramInt9 + "' ";
/*     */ 
/*     */               
/* 327 */               if (paramInt6 != 1 && paramInt8 != 0) {
/* 328 */                 str1 = str1 + " onclick=\"";
/* 329 */                 str1 = str1 + "selectItemCheckChange(this," + paramInt1 + ");checkinput2('field" + paramInt1 + "','field" + paramInt1 + "span',this.getAttribute('viewtype'));\"";
/*     */               } 
/*     */ 
/*     */ 
/*     */               
/* 334 */               str1 = str1 + "\"";
/* 335 */               str1 = str1 + ">\n";
/*     */               
/* 337 */               str1 = str1 + "<label for=\"field" + paramInt1 + "_" + str24 + "\" >" + str25 + "</label>\n";
/* 338 */               str1 = str1 + "</div>\n";
/*     */             } 
/*     */           } else {
/*     */             
/* 342 */             while (recordSet1.next()) {
/* 343 */               String str24 = Util.null2String(recordSet1.getString("selectvalue"));
/* 344 */               String str25 = Util.toScreen(recordSet1.getString("selectname"), i3);
/* 345 */               String str26 = Util.null2String(recordSet1.getString("isdefault"));
/* 346 */               String str27 = "";
/* 347 */               if ("".equals(paramString3)) {
/* 348 */                 if ("y".equals(str26) && k <= 0) {
/* 349 */                   bool1 = false;
/* 350 */                   str23 = str24;
/* 351 */                   str27 = " selected ";
/*     */                 } 
/*     */                 continue;
/*     */               } 
/* 355 */               if (i7 == 2 && ("," + paramString3 + ",").indexOf("," + str24 + ",") >= 0) {
/* 356 */                 bool1 = false;
/* 357 */                 str23 = str23 + "," + str24; continue;
/* 358 */               }  if (str24.equals(paramString3)) {
/* 359 */                 bool1 = false;
/* 360 */                 str23 = str24;
/* 361 */                 str27 = " selected ";
/*     */               } 
/*     */             } 
/*     */ 
/*     */             
/* 366 */             if (i7 == 2 && str23.startsWith(",")) {
/* 367 */               str23 = str23.substring(1);
/*     */             }
/* 369 */             str2 = str2 + "function doInitChildSelect" + paramInt1 + "to" + i11 + "(){";
/* 370 */             str2 = str2 + "\tdoInitChildSelect(" + paramInt1 + "," + i11 + ",\"" + str23 + "\");\n";
/* 371 */             str2 = str2 + "}";
/*     */ 
/*     */             
/* 374 */             str2 = str2 + "\tif (window.addEventListener){\n";
/* 375 */             str2 = str2 + "\t    window.addEventListener(\"load\", doInitChildSelect" + paramInt1 + "to" + i11 + ", false);\n";
/* 376 */             str2 = str2 + "\t}else if (window.attachEvent){\n";
/* 377 */             str2 = str2 + "\t    window.attachEvent(\"onload\", doInitChildSelect" + paramInt1 + "to" + i11 + ");\n";
/* 378 */             str2 = str2 + "\t}else{\n";
/* 379 */             str2 = str2 + "\t    window.onload=doInitChildSelect" + paramInt1 + "to" + i11 + ";\n";
/* 380 */             str2 = str2 + "\t}\n";
/*     */           } 
/* 382 */           if (arrayList1.indexOf("" + paramInt1) >= 0) {
/* 383 */             str2 = str2 + "function changeshowattr" + paramInt1 + "(){";
/* 384 */             str2 = str2 + "\tchangeshowattr('" + paramInt1 + "_0','" + str23 + "',-1," + i4 + "," + i5 + ",true);\n";
/* 385 */             str2 = str2 + "}";
/*     */ 
/*     */             
/* 388 */             str2 = str2 + "\tif (window.addEventListener){\n";
/* 389 */             str2 = str2 + "\t    window.addEventListener(\"load\", changeshowattr" + paramInt1 + ", false);\n";
/* 390 */             str2 = str2 + "\t}else if (window.attachEvent){\n";
/* 391 */             str2 = str2 + "\t    window.attachEvent(\"onload\", changeshowattr" + paramInt1 + ");\n";
/* 392 */             str2 = str2 + "\t}else{\n";
/* 393 */             str2 = str2 + "\t    window.onload=changeshowattr" + paramInt1 + ";\n";
/* 394 */             str2 = str2 + "\t}\n";
/*     */           } 
/*     */           
/* 397 */           if (i7 == 1) {
/* 398 */             str1 = str1 + "</select>\n";
/*     */           } else {
/* 400 */             str1 = str1 + "</div>\n";
/*     */           } 
/*     */           
/* 403 */           if (!str21.equals("")) {
/* 404 */             str1 = str1 + "<input type=\"hidden\" id=\"uploadMaxField\" name=\"uploadMaxField\" isedit=\"" + paramInt8 + "\" value=\"" + paramInt1 + "\" />\n";
/*     */           }
/* 406 */           str1 = str1 + "<span id=\"field" + paramInt1 + "span\">";
/* 407 */           if (bool1 == true) {
/* 408 */             str1 = str1 + str7;
/*     */           }
/* 410 */           str1 = str1 + "</span>\n";
/* 411 */           if (paramInt6 == 1 || paramInt8 == 0 || i7 == 2 || i7 == 3) {
/*     */             
/* 413 */             str1 = str1 + "<input type=\"hidden\" id=\"field" + paramInt1 + "\" name=\"field" + paramInt1 + "\" ";
/*     */             
/* 415 */             if (i7 == 2) {
/* 416 */               str1 = str1 + " value=\"" + str23 + "\" ";
/*     */             } else {
/* 418 */               str1 = str1 + " value=\"" + str23 + "\" ";
/*     */             } 
/* 420 */             if (paramInt6 == 1 || paramInt8 == 0) {
/* 421 */               str1 = str1 + " __disabled='1' ";
/*     */             }
/* 423 */             if (paramInt6 == 1 || paramInt8 == 0) {
/*     */               
/* 425 */               str1 = str1 + " onpropertychange=\"";
/* 426 */               if (arrayList1.indexOf("" + paramInt1) >= 0)
/* 427 */                 str1 = str1 + "changeshowattr('" + paramInt1 + "_0',this.value,-1," + i4 + "," + i5 + ",false);"; 
/* 428 */               str1 = str1 + str17 + str21 + str22 + str19 + str8 + str10 + "\"";
/* 429 */               str1 = str1 + " onfocus='" + str11 + "' onclick='" + str13 + "' onblur='" + str12 + "'";
/* 430 */               str1 = str1 + " ondblclick='" + str14 + "' onmouseover='" + str15 + "' onmouseout='" + str16 + "' ";
/* 431 */               str1 = str1 + " _listener='" + str8 + str10 + "'";
/* 432 */             } else if (i7 == 3) {
/*     */               
/* 434 */               str1 = str1 + " onpropertychange=\"";
/* 435 */               if (arrayList1.indexOf("" + paramInt1) >= 0)
/* 436 */                 str1 = str1 + "changeshowattr('" + paramInt1 + "_0',this.value,-1," + i4 + "," + i5 + ",false);"; 
/* 437 */               str1 = str1 + str17 + str21 + str22 + str19 + str8 + str10 + "\"";
/* 438 */               str1 = str1 + " onfocus='" + str11 + "' onclick='" + str13 + "' onblur='" + str12 + "'";
/* 439 */               str1 = str1 + " ondblclick='" + str14 + "' onmouseover='" + str15 + "' onmouseout='" + str16 + "' ";
/* 440 */               str1 = str1 + " _listener='" + str8 + str10 + "'";
/* 441 */               str1 = str1 + "viewtype='" + paramInt9 + "' temptitle='" + Util.toScreen(paramString2, i3) + "'  ";
/* 442 */             } else if (i7 == 2) {
/*     */               
/* 444 */               str1 = str1 + "viewtype='" + paramInt9 + "' temptitle='" + Util.toScreen(paramString2, i3) + "'";
/*     */             } 
/* 446 */             str1 = str1 + "/>\n";
/*     */           } 
/* 448 */           if (paramInt6 == 0 && paramInt8 == 1) {
/* 449 */             str1 = str1 + "<input type=\"hidden\" id=\"oldfieldview" + paramInt1 + "\" name=\"oldfieldview" + paramInt1 + "\" value=\"" + (paramInt7 + paramInt8 + paramInt9) + "\" />\n";
/*     */           }
/*     */         } else {
/*     */           
/* 453 */           str1 = str1 + "<input type=\"hidden\" id=\"field" + paramInt1 + "\" name=\"field" + paramInt1 + "\" value=\"" + paramString3 + "\" />";
/*     */         } 
/*     */       } else {
/*     */         
/* 457 */         String str17 = "";
/* 458 */         if ("dt2_fieldIdSkfs_fieldId".equals(hashMap2.get(paramInt1 + "")) || "dt3_fieldIdSkfs_fieldId".equals(hashMap2.get(paramInt1 + ""))) {
/* 459 */           str17 = "try{_fnaBorrowSkfsDtl2_onchange(this);}catch(ex1){}";
/*     */         }
/*     */         
/* 462 */         String str18 = Util.null2String((String)paramHashtable.get("trrigerdetailfield"));
/* 463 */         String str19 = Util.null2String((String)paramHashtable.get("derecorderindex"));
/* 464 */         String str20 = "\"";
/* 465 */         if ("\"+rowindex+\"".equals(str19)) {
/* 466 */           str20 = "\\\"";
/*     */         }
/* 468 */         String str21 = "";
/* 469 */         if (str18.indexOf("field" + paramInt1) >= 0) {
/* 470 */           str21 = "datainputd('field" + paramInt1 + "_" + str19 + "');";
/*     */         }
/* 472 */         String str22 = "";
/* 473 */         if (paramInt6 == 1 || paramInt8 == 0) {
/* 474 */           str22 = " id='disfield" + paramInt1 + "_" + str19 + "' name='disfield" + paramInt1 + "_" + str19 + "'  _printflag='1' disabled ";
/*     */         } else {
/* 476 */           str22 = " id='field" + paramInt1 + "_" + str19 + "' name='field" + paramInt1 + "_" + str19 + "' ";
/*     */         } 
/*     */ 
/*     */         
/* 480 */         int i10 = ((Integer)paramHashtable.get("childfieldid_tmp")).intValue();
/* 481 */         int i11 = ((Integer)paramHashtable.get("firstPfieldid_tmp")).intValue();
/* 482 */         boolean bool = ((Boolean)paramHashtable.get("hasPfield")).booleanValue();
/* 483 */         String str23 = "";
/* 484 */         if (i10 != 0) {
/* 485 */           str23 = "changeChildFieldDetail(this," + paramInt1 + "," + i10 + "," + str19 + ");";
/*     */         }
/*     */         
/* 488 */         ArrayList arrayList3 = (ArrayList)paramHashtable.get("changedefieldsadd");
/* 489 */         ArrayList arrayList4 = (ArrayList)paramHashtable.get("seldefieldsadd");
/* 490 */         String str24 = "";
/* 491 */         String str25 = "";
/* 492 */         ArrayList<String> arrayList5 = (ArrayList)paramHashtable.get("sqlfieldidList");
/* 493 */         ArrayList<String> arrayList6 = (ArrayList)paramHashtable.get("sqlcontentList");
/* 494 */         if (arrayList5 != null && arrayList5.size() > 0) {
/* 495 */           for (byte b = 0; b < arrayList5.size(); b++) {
/* 496 */             String str27 = Util.null2String(arrayList5.get(b)).trim();
/* 497 */             String str28 = Util.null2String(arrayList6.get(b)).trim();
/* 498 */             if (!"".equals(str28) && 
/* 499 */               str28.indexOf("$" + paramInt1 + "$") > -1) {
/* 500 */               str25 = str25 + str27 + ",";
/*     */             }
/*     */             
/* 503 */             if (str27.equals("" + paramInt1) && 
/* 504 */               "\"+rowindex+\"".equals(str19)) {
/* 505 */               str2 = str2 + "\t fieldAttrOperate.pageLoadInitValue('" + paramInt1 + "',rowindex);\n";
/*     */             }
/*     */           } 
/*     */           
/* 509 */           if (str25.length() > 0) {
/* 510 */             str24 = "fieldAttrOperate.doSqlFieldAjax(this,'" + str25.substring(0, str25.length() - 1) + "');";
/*     */           }
/*     */         } 
/* 513 */         if (i7 == 1) {
/* 514 */           str1 = str1 + "<select notBeauty=true class='inputstyle' viewtype='" + paramInt9 + "' style='height:auto;' temptitle='" + paramString2 + "' _detailRecordId='" + m + "' ";
/*     */           
/* 516 */           if (paramInt6 != 1 && paramInt8 != 0) {
/* 517 */             str1 = str1 + " onChange=" + str20 + str23 + "try {checkinput3(field" + paramInt1 + "_" + str19 + ",field" + paramInt1 + "_" + str19 + "span,this.getAttribute('viewtype'));} catch(_9te){}" + str9 + str10;
/* 518 */             if (arrayList4.indexOf("" + paramInt1) >= 0)
/* 519 */               str1 = str1 + "changeshowattr('" + paramInt1 + "_1',this.value," + str19 + "," + i4 + "," + i5 + ",false);"; 
/* 520 */             str1 = str1 + str21 + str24 + str17 + str20 + " ";
/* 521 */             str1 = str1 + " onfocus='" + str11 + "' onclick='" + str13 + "' onblur='" + str12 + "'";
/* 522 */             str1 = str1 + " ondblclick='" + str14 + "' onmouseover='" + str15 + "' onmouseout='" + str16 + "' ";
/*     */           } 
/*     */           
/* 525 */           str1 = str1 + str22;
/* 526 */           str1 = str1 + ">";
/* 527 */           str1 = str1 + "<option value=''></option>";
/*     */         } else {
/* 529 */           str1 = str1 + "<div class='radioCheckDiv'>\n";
/*     */         } 
/* 531 */         String str26 = "";
/* 532 */         boolean bool1 = true;
/* 533 */         RecordSet recordSet1 = (RecordSet)paramHashtable.get("selectitemrs");
/* 534 */         recordSet1.beforFirst();
/* 535 */         if (!bool || paramInt8 == 0 || paramInt6 == 1) {
/* 536 */           while (recordSet1.next()) {
/* 537 */             if (paramInt8 != 0 && paramInt6 != 1) {
/* 538 */               int i12 = Util.getIntValue(recordSet1.getString("cancel"), 0);
/* 539 */               if (i12 == 1) {
/*     */                 continue;
/*     */               }
/*     */             } 
/* 543 */             String str27 = Util.null2String(recordSet1.getString("selectvalue"));
/* 544 */             String str28 = Util.toScreen(recordSet1.getString("selectname"), i3);
/* 545 */             String str29 = Util.null2String(recordSet1.getString("isdefault"));
/*     */             
/* 547 */             if (i7 == 1) {
/* 548 */               if ("".equals(paramString3)) {
/* 549 */                 if ("y".equals(str29) && "\"+rowindex+\"".equals(str19)) {
/* 550 */                   bool1 = false;
/* 551 */                   str26 = str27;
/* 552 */                   str1 = str1 + "<option value='" + str27 + "' selected>" + str28 + "</option>";
/* 553 */                   str5 = str28; continue;
/*     */                 } 
/* 555 */                 str1 = str1 + "<option value='" + str27 + "'>" + str28 + "</option>";
/*     */                 continue;
/*     */               } 
/* 558 */               bool1 = false;
/* 559 */               str1 = str1 + "<option value='" + str27 + "'";
/* 560 */               if (str27.equals(paramString3)) {
/* 561 */                 str26 = str27;
/* 562 */                 str1 = str1 + " selected ";
/* 563 */                 str5 = str28;
/*     */               } 
/* 565 */               str1 = str1 + ">" + str28 + "</option>";
/*     */               continue;
/*     */             } 
/* 568 */             if (i8 == 1) {
/* 569 */               str1 = str1 + "<div class='selectItemHorizontalDiv'>";
/*     */             }
/* 571 */             if (i8 == 2) {
/* 572 */               str1 = str1 + "<div class='selectItemVerticalDiv' >";
/*     */             }
/* 574 */             if (i7 == 2) {
/* 575 */               str1 = str1 + "<input type='checkbox' class='Inputstyle' style='height:auto;' viewtype='" + paramInt9 + "' temptitle='" + Util.toScreen(paramString2, i3) + "' ";
/* 576 */               if (!paramString3.equals("")) {
/* 577 */                 if (("," + paramString3 + ",").indexOf("," + str27 + ",") >= 0) {
/* 578 */                   str1 = str1 + " checked ";
/* 579 */                   if (str26.equals("")) {
/* 580 */                     str26 = str27;
/*     */                   } else {
/* 582 */                     str26 = str26 + "," + str27;
/*     */                   } 
/* 584 */                   bool1 = false;
/*     */                 }
/*     */               
/* 587 */               } else if ("y".equals(str29) && "\"+rowindex+\"".equals(str19)) {
/* 588 */                 str1 = str1 + " checked ";
/* 589 */                 if (str26.equals("")) {
/* 590 */                   str26 = str27;
/*     */                 } else {
/* 592 */                   str26 = str26 + "," + str27;
/*     */                 } 
/* 594 */                 bool1 = false;
/*     */               } 
/*     */               
/* 597 */               str1 = str1 + " id='field" + paramInt1 + "_" + str19 + "_" + str27 + "' name='field" + paramInt1 + "_" + str19 + "_" + str27 + "' ";
/*     */             } else {
/* 599 */               str1 = str1 + "<input type='radio' class='Inputstyle' style='height:auto;' viewtype='" + paramInt9 + "' temptitle='" + Util.toScreen(paramString2, i3) + "' ";
/* 600 */               if (!paramString3.equals("")) {
/* 601 */                 if (paramString3.equals(str27)) {
/* 602 */                   str1 = str1 + " checked ";
/* 603 */                   if (str26.equals("")) {
/* 604 */                     str26 = str27;
/* 605 */                     bool1 = false;
/*     */                   }
/*     */                 
/*     */                 } 
/* 609 */               } else if ("y".equals(str29) && "\"+rowindex+\"".equals(str19) && 
/* 610 */                 str26.equals("")) {
/* 611 */                 str1 = str1 + " checked ";
/* 612 */                 str26 = str27;
/* 613 */                 bool1 = false;
/*     */               } 
/*     */ 
/*     */               
/* 617 */               str1 = str1 + " name='field" + paramInt1 + "_" + str19 + "_disp' id='field" + paramInt1 + "_" + str19 + "_" + str27 + "' ";
/*     */             } 
/* 619 */             if (paramInt6 == 1 || paramInt8 == 0) {
/* 620 */               str1 = str1 + " _printflag='1' disabled ";
/*     */             }
/* 622 */             str1 = str1 + " value='" + str27 + "' ";
/* 623 */             str1 = str1 + " viewtype='" + paramInt9 + "' ";
/*     */             
/* 625 */             if (paramInt6 != 1 && paramInt8 != 0) {
/* 626 */               str1 = str1 + " onclick=" + str20;
/* 627 */               str1 = str1 + "selectItemCheckChange(this,'" + paramInt1 + "_" + str19 + "');checkinput3(field" + paramInt1 + "_" + str19 + ",field" + paramInt1 + "_" + str19 + "span,this.getAttribute('viewtype'));";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */               
/* 633 */               str1 = str1 + str20 + " ";
/*     */             } 
/* 635 */             str1 = str1 + ">\n";
/*     */             
/* 637 */             str1 = str1 + "<label for='field" + paramInt1 + "_" + str19 + "_" + str27 + "' >" + str28 + "</label>\n";
/* 638 */             str1 = str1 + "</div>\n";
/*     */           } 
/*     */         } else {
/*     */           
/* 642 */           while (recordSet1.next()) {
/* 643 */             String str27 = Util.null2String(recordSet1.getString("selectvalue"));
/* 644 */             String str28 = Util.toScreen(recordSet1.getString("selectname"), i3);
/* 645 */             String str29 = Util.null2String(recordSet1.getString("isdefault"));
/*     */             
/* 647 */             String str30 = "";
/* 648 */             if ("".equals(paramString3)) {
/* 649 */               if ("y".equals(str29) && "\"+rowindex+\"".equals(str19)) {
/* 650 */                 bool1 = false;
/* 651 */                 str26 = str27;
/* 652 */                 str30 = " selected ";
/*     */               } 
/*     */               continue;
/*     */             } 
/* 656 */             if (i7 == 2 && ("," + paramString3 + ",").indexOf("," + str27 + ",") >= 0) {
/* 657 */               bool1 = false;
/* 658 */               str26 = str26 + "," + str27; continue;
/* 659 */             }  if (str27.equals(paramString3)) {
/* 660 */               bool1 = false;
/* 661 */               str26 = str27;
/* 662 */               str1 = str1 + "<option value='" + str27 + "' selected>" + str28 + "</option>";
/*     */             } 
/*     */           } 
/*     */           
/* 666 */           if (i7 == 2 && str26.startsWith(",")) {
/* 667 */             str26 = str26.substring(1);
/*     */           }
/* 669 */           if (!"\"+rowindex+\"".equals(str19)) {
/* 670 */             str2 = str2 + "function doInitDetailchildSelect" + paramInt1 + "to" + i11 + "_" + str19 + "(){";
/* 671 */             str2 = str2 + "\tdoInitDetailchildSelect(" + paramInt1 + "," + i11 + "," + str19 + ",\"" + str26 + "\");\n";
/* 672 */             str2 = str2 + "}";
/*     */ 
/*     */             
/* 675 */             str2 = str2 + "\tif (window.addEventListener){\n";
/* 676 */             str2 = str2 + "\t    window.addEventListener(\"load\", doInitDetailchildSelect" + paramInt1 + "to" + i11 + "_" + str19 + ", false);\n";
/* 677 */             str2 = str2 + "\t}else if (window.attachEvent){\n";
/* 678 */             str2 = str2 + "\t    window.attachEvent(\"onload\", doInitDetailchildSelect" + paramInt1 + "to" + i11 + "_" + str19 + ");\n";
/* 679 */             str2 = str2 + "\t}else{\n";
/* 680 */             str2 = str2 + "\t    window.onload=doInitDetailchildSelect" + paramInt1 + "to" + i11 + "_" + str19 + ";\n";
/* 681 */             str2 = str2 + "\t}\n";
/*     */             
/* 683 */             str4 = str4 + "<iframe id=\"iframe_" + i11 + "_" + paramInt1 + "_" + str19 + "\" frameborder=0 scrolling=no src=\"\"  style=\"display:none\"></iframe>";
/*     */           } else {
/* 685 */             str3 = str3 + "doInitDetailchildSelectAdd(" + paramInt1 + "," + i11 + ",rowindex,\"" + str26 + "\");";
/* 686 */             str4 = str4 + "<iframe id=\"iframe_" + i11 + "_" + paramInt1 + "\" frameborder=0 scrolling=no src=\"\"  style=\"display:none\"></iframe>";
/*     */           } 
/*     */         } 
/* 689 */         if (i7 == 1) {
/* 690 */           str1 = str1 + "</select>";
/*     */         } else {
/* 692 */           str1 = str1 + "</div>";
/*     */         } 
/* 694 */         str1 = str1 + "<span id='field" + paramInt1 + "_" + str19 + "span'>";
/* 695 */         if (bool1 == true) {
/* 696 */           str1 = str1 + str7;
/*     */         }
/* 698 */         str1 = str1 + "</span>";
/* 699 */         if (paramInt6 == 1 || paramInt8 == 0 || i7 == 2 || i7 == 3) {
/* 700 */           String str = paramString3;
/* 701 */           if ("".equals(str) && k <= 0) {
/* 702 */             str = str26;
/*     */           }
/* 704 */           str1 = str1 + "<input type='hidden' id='field" + paramInt1 + "_" + str19 + "' name='field" + paramInt1 + "_" + str19 + "' ";
/* 705 */           if (paramInt6 == 1 || paramInt8 == 0) {
/* 706 */             str1 = str1 + " __disabled='1' ";
/*     */           }
/*     */           
/* 709 */           if (i7 == 2 || i7 == 3) {
/* 710 */             str1 = str1 + " value='" + str26 + "' ";
/*     */           } else {
/* 712 */             str1 = str1 + " value='" + str26 + "' ";
/*     */           } 
/*     */           
/* 715 */           if (paramInt6 == 1 || paramInt8 == 0) {
/* 716 */             str1 = str1 + " onpropertychange=" + str20 + str23 + "try {checkinput3(field" + paramInt1 + "_" + str19 + ",field" + paramInt1 + "_" + str19 + "span,this.getAttribute('viewtype'));} catch(_8te){}";
/* 717 */             if (arrayList4.indexOf("" + paramInt1) >= 0)
/* 718 */               str1 = str1 + "changeshowattr('" + paramInt1 + "_1',this.value," + str19 + "," + i4 + "," + i5 + ",false);"; 
/* 719 */             str1 = str1 + str21 + str24 + str17 + ";" + str8 + str10 + str20 + " ";
/* 720 */             str1 = str1 + " onfocus=\"" + str11 + "\" onclick=\"" + str13 + "\" onblur=\"" + str12 + "\"";
/* 721 */             str1 = str1 + " ondblclick=\"" + str14 + "\" onmouseover=\"" + str15 + "\" onmouseout=\"" + str16 + "\" ";
/* 722 */             str1 = str1 + " _listener=" + str20 + str8 + str10 + str20 + " ";
/*     */           }
/* 724 */           else if (i7 == 2) {
/* 725 */             str1 = str1 + "viewtype='" + paramInt9 + "' temptitle='" + Util.toScreen(paramString2, i3) + "' ";
/*     */           }
/* 727 */           else if (i7 == 3) {
/* 728 */             str1 = str1 + " onpropertychange=" + str20 + str23;
/* 729 */             if (arrayList4.indexOf("" + paramInt1) >= 0)
/* 730 */               str1 = str1 + "changeshowattr('" + paramInt1 + "_1',this.value," + str19 + "," + i4 + "," + i5 + ",false);"; 
/* 731 */             str1 = str1 + str21 + str24 + str17 + ";" + str8 + str10 + str20 + " ";
/* 732 */             str1 = str1 + " onfocus=\"" + str11 + "\" onclick=\"" + str13 + "\" onblur=\"" + str12 + "\"";
/* 733 */             str1 = str1 + " ondblclick=\"" + str14 + "\" onmouseover=\"" + str15 + "\" onmouseout=\"" + str16 + "\" ";
/* 734 */             str1 = str1 + " _listener=" + str20 + str8 + str10 + str20 + " ";
/* 735 */             str1 = str1 + " viewtype='" + paramInt9 + "' temptitle='" + Util.toScreen(paramString2, i3) + "' ";
/*     */           } 
/* 737 */           str1 = str1 + " />";
/*     */         } 
/* 739 */         if (paramInt6 == 0 && paramInt8 == 1) {
/* 740 */           if (arrayList4.indexOf("" + paramInt1) >= 0) {
/* 741 */             if (!"\"+rowindex+\"".equals(str19)) {
/* 742 */               str2 = str2 + "changeshowattr('" + paramInt1 + "_1','" + str26 + "'," + str19 + "," + i4 + "," + i5 + ",true);";
/* 743 */             } else if (!"".equals(str26)) {
/* 744 */               str3 = str3 + "changeshowattr('" + paramInt1 + "_1','" + str26 + "',rowindex," + i4 + "," + i5 + ",true);";
/*     */             } 
/*     */           }
/* 747 */           str1 = str1 + "<input type='hidden' id='oldfieldview" + paramInt1 + "_" + str19 + "' name='oldfieldview" + paramInt1 + "_" + str19 + "' value='" + (paramInt7 + paramInt8 + paramInt9) + "' />";
/*     */         } 
/*     */       } 
/*     */       
/* 751 */       if (i1 == 1 && i2 == 2) {
/* 752 */         int i10 = str1.indexOf("<select");
/* 753 */         int i11 = str1.indexOf("</select>");
/* 754 */         if (i10 > -1 && i11 > -1) {
/* 755 */           str1 = str1.substring(0, i10) + "<span>" + str5 + "</span>" + str1.substring(i11 + 9);
/*     */         }
/*     */       } 
/* 758 */     } catch (Exception exception) {
/* 759 */       str1 = "";
/* 760 */       writeLog(exception);
/*     */     } 
/* 762 */     hashtable.put("jsStr", str2);
/* 763 */     hashtable.put("inputStr", str1);
/* 764 */     hashtable.put("addRowjsStr", str3);
/* 765 */     hashtable.put("hiddenElementStr", str4);
/* 766 */     return hashtable;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/field/SelectElement.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */