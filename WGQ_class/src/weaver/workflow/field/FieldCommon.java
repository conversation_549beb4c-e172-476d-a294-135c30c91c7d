/*     */ package weaver.workflow.field;
/*     */ 
/*     */ import com.google.common.base.Strings;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashSet;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class FieldCommon
/*     */   extends BaseBean
/*     */ {
/*     */   public boolean isOptionUsed(String paramString1, String paramString2, String paramString3, int paramInt) {
/*     */     try {
/*  43 */       if (Strings.isNullOrEmpty(paramString1) || Strings.isNullOrEmpty(paramString3)) return true; 
/*  44 */       RecordSet recordSet = new RecordSet();
/*  45 */       String str1 = "";
/*  46 */       String str2 = "";
/*  47 */       int i = 0;
/*  48 */       if (paramInt != 1) {
/*  49 */         if ("mainfield".equals(paramString2)) {
/*  50 */           str2 = "workflow_form";
/*  51 */           recordSet.executeQuery("select fieldname,type from workflow_formdict where id = " + paramString1, new Object[0]);
/*     */         } else {
/*  53 */           str2 = "workflow_formdetail";
/*  54 */           recordSet.executeQuery("select fieldname,type from workflow_formdictdetail where id = " + paramString1, new Object[0]);
/*     */         } 
/*  56 */         if (recordSet.next()) {
/*  57 */           str1 = recordSet.getString("fieldname");
/*  58 */           i = Util.getIntValue(recordSet.getString("type"), 1);
/*     */         } 
/*     */       } else {
/*  61 */         recordSet.executeQuery("select billid,fieldname,viewtype,detailtable,fieldhtmltype,type from workflow_billfield where id = ?", new Object[] { paramString1 });
/*  62 */         if (recordSet.next()) {
/*  63 */           str1 = recordSet.getString("fieldname");
/*  64 */           String str = recordSet.getString("detailtable");
/*  65 */           int j = Util.getIntValue(recordSet.getString("viewtype"));
/*  66 */           i = Util.getIntValue(recordSet.getString("type"), 1);
/*     */           
/*  68 */           if (j == 1 && !Strings.isNullOrEmpty(str)) {
/*  69 */             str2 = str;
/*     */           } else {
/*  71 */             int k = Util.getIntValue(recordSet.getString("billid"));
/*  72 */             recordSet.executeQuery("select tablename from workflow_bill where id = ?", new Object[] { Integer.valueOf(k) });
/*  73 */             recordSet.next();
/*  74 */             str2 = recordSet.getString("tablename");
/*     */           } 
/*  76 */           if (Strings.isNullOrEmpty(str2)) return true;
/*     */         
/*     */         } 
/*     */       } 
/*     */       
/*  81 */       String str3 = "";
/*  82 */       if (i == 2) {
/*  83 */         str3 = String.format("select %s from %s where %s is not null", new Object[] { str1, str2, str1, str1 });
/*     */       } else {
/*  85 */         str3 = String.format("select %s from %s where (%s <> '' or %s is not null)", new Object[] { str1, str2, str1, str1 });
/*     */       } 
/*     */       
/*  88 */       recordSet.executeQuery(str3, new Object[0]);
/*  89 */       HashSet<String> hashSet = new HashSet();
/*  90 */       while (recordSet.next()) {
/*  91 */         String str = recordSet.getString(1);
/*  92 */         String[] arrayOfString = str.split(",");
/*  93 */         for (String str4 : arrayOfString) {
/*  94 */           hashSet.add(str4);
/*     */         }
/*     */       } 
/*  97 */       return hashSet.contains(paramString3);
/*  98 */     } catch (Exception exception) {
/*  99 */       exception.printStackTrace();
/* 100 */       return true;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean isOptionUsed(String paramString1, String paramString2, String paramString3) {
/* 112 */     boolean bool = false;
/* 113 */     String str1 = "";
/* 114 */     RecordSet recordSet = new RecordSet();
/* 115 */     String str2 = "";
/* 116 */     String str3 = "";
/*     */     
/* 118 */     int i = 0;
/* 119 */     if ("mainfield".equals(paramString2)) {
/* 120 */       str3 = "workflow_form";
/* 121 */       recordSet.executeQuery("select fieldname,type from workflow_formdict where id = " + paramString1, new Object[0]);
/*     */     } else {
/* 123 */       str3 = "workflow_formdetail";
/* 124 */       recordSet.executeQuery("select fieldname,type from workflow_formdictdetail where id = " + paramString1, new Object[0]);
/*     */     } 
/* 126 */     if (recordSet.next()) {
/* 127 */       str2 = recordSet.getString("fieldname");
/* 128 */       i = Util.getIntValue(recordSet.getString("type"), 1);
/*     */       
/* 130 */       if (i == 1 || i == 3) {
/* 131 */         str1 = "select requestid," + str2 + " from " + str3 + " where (" + str2 + " <>'' or " + str2 + " is not null) and " + str2 + " = " + paramString3 + " and requestid in (select requestid from workflow_requestbase where deleted<>1 )";
/* 132 */         recordSet.executeQuery(str1, new Object[0]);
/* 133 */         if (recordSet.next()) bool = true; 
/*     */       } else {
/* 135 */         boolean bool1 = recordSet.getDBType().equals("oracle");
/* 136 */         boolean bool2 = recordSet.getDBType().equals("mysql");
/* 137 */         boolean bool3 = recordSet.getDBType().equals("postgresql");
/* 138 */         if (bool1) {
/* 139 */           str1 = "select requestid," + str2 + " from " + str3 + " where (" + str2 + " <>'' or " + str2 + " is not null) and ',' || " + str2 + " || ',' like '%,' || " + paramString3 + " || ',%' and requestid in (select requestid from workflow_requestbase where deleted<>1 )";
/* 140 */         } else if (bool2) {
/* 141 */           str1 = "select requestid," + str2 + " from " + str3 + " where (" + str2 + " <>'' or " + str2 + " is not null) and concat(',' , " + str2 + " , ',') like concat('%,' , " + paramString3 + " , ',%') and requestid in (select requestid from workflow_requestbase where deleted<>1 )";
/*     */         }
/* 143 */         else if (bool1) {
/* 144 */           str1 = "select requestid," + str2 + " from " + str3 + " where (" + str2 + " <>'' or " + str2 + " is not null) and ',' || " + str2 + " || ',' like '%,' || " + paramString3 + " || ',%' and requestid in (select requestid from workflow_requestbase where deleted<>1 )";
/*     */         } else {
/*     */           
/* 147 */           str1 = "select requestid," + str2 + " from " + str3 + " where (" + str2 + " <>'' or " + str2 + " is not null) and ',' + " + str2 + " + ',' like '%,' + " + paramString3 + " + ',%' and requestid in (select requestid from workflow_requestbase where deleted<>1 )";
/*     */         } 
/* 149 */         recordSet.executeQuery(str1, new Object[0]);
/* 150 */         if (recordSet.next()) bool = true;
/*     */       
/*     */       } 
/*     */     } 
/*     */ 
/*     */     
/* 156 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean isBillOptionUsed(String paramString1, String paramString2) {
/* 167 */     if (Strings.isNullOrEmpty(paramString1) || Strings.isNullOrEmpty(paramString2)) return true; 
/* 168 */     RecordSet recordSet = new RecordSet();
/* 169 */     recordSet.executeQuery("select billid,fieldname,viewtype,detailtable,fieldhtmltype,type from workflow_billfield where id = ?", new Object[] { paramString1 });
/* 170 */     if (recordSet.next()) {
/* 171 */       String str1 = "";
/* 172 */       String str2 = recordSet.getString("fieldname");
/* 173 */       String str3 = recordSet.getString("detailtable");
/* 174 */       int i = Util.getIntValue(recordSet.getString("viewtype"));
/* 175 */       String str4 = recordSet.getString("fieldhtmltype");
/* 176 */       String str5 = recordSet.getString("type");
/*     */       
/* 178 */       if (i == 1 && !Strings.isNullOrEmpty(str3)) {
/* 179 */         str1 = str3;
/*     */       } else {
/* 181 */         int j = Util.getIntValue(recordSet.getString("billid"));
/* 182 */         recordSet.executeQuery("select tablename from workflow_bill where id = ?", new Object[] { Integer.valueOf(j) });
/* 183 */         recordSet.next();
/* 184 */         str1 = recordSet.getString("tablename");
/*     */       } 
/* 186 */       if (Strings.isNullOrEmpty(str1)) return true;
/*     */       
/* 188 */       String str6 = "";
/* 189 */       if ("5".equals(str4) && "2".equals(str5)) {
/* 190 */         str6 = String.format("select distinct %s from %s where %s is not null", new Object[] { str2, str1, str2, str2 });
/*     */       } else {
/* 192 */         str6 = String.format("select distinct %s from %s where (%s <> '' or %s is not null)", new Object[] { str2, str1, str2, str2 });
/*     */       } 
/*     */       
/* 195 */       recordSet.executeQuery(str6, new Object[0]);
/* 196 */       HashSet<String> hashSet = new HashSet();
/* 197 */       while (recordSet.next()) {
/* 198 */         String str = recordSet.getString(1);
/* 199 */         String[] arrayOfString = str.split(",");
/* 200 */         for (String str7 : arrayOfString) {
/* 201 */           hashSet.add(str7);
/*     */         }
/*     */       } 
/* 204 */       return hashSet.contains(paramString2);
/*     */     } 
/* 206 */     return true;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void initNewFieldIsView(String paramString, String[] paramArrayOfString1, String[] paramArrayOfString2) {
/* 220 */     RecordSet recordSet1 = new RecordSet();
/* 221 */     RecordSet recordSet2 = new RecordSet();
/*     */     
/* 223 */     ArrayList<String> arrayList1 = new ArrayList();
/*     */     
/* 225 */     String str1 = "-1,";
/* 226 */     String str2 = "-1,";
/* 227 */     ArrayList<String> arrayList2 = new ArrayList();
/* 228 */     String str3 = "-1,";
/* 229 */     ArrayList<String> arrayList3 = new ArrayList();
/* 230 */     String str4 = "-1,";
/*     */     
/*     */     byte b;
/* 233 */     for (b = 0; b < paramArrayOfString1.length; b++) {
/*     */       
/* 235 */       str1 = str1 + paramArrayOfString1[b] + ",";
/* 236 */       arrayList2.add(paramArrayOfString1[b]);
/*     */     } 
/* 238 */     str1 = str1.substring(0, str1.length() - 1);
/*     */     
/* 240 */     recordSet1.executeSql("SELECT fieldID FROM WorkFlow_FormField WHERE formID = " + paramString + " AND fieldID IN (" + str1 + ") AND (isDetail <> '1' OR isDetail is NULL)");
/*     */ 
/*     */     
/* 243 */     while (recordSet1.next()) {
/*     */       
/* 245 */       String str = recordSet1.getString("fieldID");
/* 246 */       arrayList2.remove(str);
/*     */     } 
/*     */     
/* 249 */     recordSet1.executeSql("SELECT fieldID FROM WorkFlow_FormField WHERE formID = " + paramString + " AND fieldID NOT IN (" + str1 + ") AND (isDetail <> '1' OR isDetail is NULL)");
/*     */ 
/*     */     
/* 252 */     while (recordSet1.next())
/*     */     {
/* 254 */       str3 = str3 + recordSet1.getString("fieldID") + ",";
/*     */     }
/* 256 */     str3 = str3.substring(0, str3.length() - 1);
/*     */ 
/*     */     
/* 259 */     for (b = 0; b < paramArrayOfString2.length; b++) {
/*     */       
/* 261 */       str2 = str2 + paramArrayOfString2[b] + ",";
/* 262 */       arrayList3.add(paramArrayOfString2[b]);
/*     */     } 
/* 264 */     str2 = str2.substring(0, str2.length() - 1);
/*     */     
/* 266 */     recordSet1.executeSql("SELECT fieldID FROM WorkFlow_FormField WHERE formID = " + paramString + " AND fieldID IN (" + str2 + ") AND isDetail = '1'");
/*     */ 
/*     */     
/* 269 */     while (recordSet1.next()) {
/*     */       
/* 271 */       String str = recordSet1.getString("fieldID");
/* 272 */       arrayList3.remove(str);
/*     */     } 
/*     */     
/* 275 */     recordSet1.executeSql("SELECT fieldID FROM WorkFlow_FormField WHERE formID = " + paramString + " AND fieldID NOT IN (" + str2 + ") AND isDetail = '1'");
/*     */ 
/*     */     
/* 278 */     while (recordSet1.next())
/*     */     {
/* 280 */       str4 = str4 + recordSet1.getString("fieldID") + ",";
/*     */     }
/* 282 */     str4 = str4.substring(0, str4.length() - 1);
/*     */ 
/*     */     
/* 285 */     recordSet1.executeSql("SELECT WorkFlowFlowNode.nodeID FROM WorkFlow_Base WorkFlowBase, WorkFlow_FlowNode WorkFlowFlowNode WHERE WorkFlowBase.formID = " + paramString + " AND WorkFlowBase.isBill = '0' AND WorkFlowBase.ID = WorkFlowFlowNode.workFlowID");
/*     */ 
/*     */     
/* 288 */     while (recordSet1.next())
/*     */     {
/* 290 */       arrayList1.add(recordSet1.getString("nodeID"));
/*     */     }
/*     */     
/* 293 */     for (b = 0; b < arrayList1.size(); b++) {
/*     */       
/* 295 */       String str = arrayList1.get(b);
/*     */       
/*     */       byte b1;
/* 298 */       for (b1 = 0; b1 < arrayList2.size(); b1++)
/*     */       {
/* 300 */         recordSet2.executeSql("INSERT INTO WorkFlow_NodeForm (nodeID, fieldID, isView, isEdit, isMandatory) values (" + str + "," + arrayList2.get(b1) + ",'1','0','0')");
/*     */       }
/*     */       
/* 303 */       if (!"-1".equals(str3))
/*     */       {
/* 305 */         recordSet2.executeSql("DELETE FROM WorkFlow_NodeForm WHERE nodeID = " + str + " AND fieldID IN (" + str3 + ")");
/*     */       }
/*     */ 
/*     */ 
/*     */       
/* 310 */       for (b1 = 0; b1 < arrayList3.size(); b1++)
/*     */       {
/* 312 */         recordSet2.executeSql("INSERT INTO WorkFlow_NodeForm (nodeID, fieldID, isView, isEdit, isMandatory) values (" + str + "," + arrayList3.get(b1) + ",'1','0','0')");
/*     */       }
/*     */       
/* 315 */       if (!"-1".equals(str4))
/*     */       {
/* 317 */         recordSet2.executeSql("DELETE FROM WorkFlow_NodeForm WHERE nodeID = " + str + " AND fieldID IN (" + str4 + ")");
/*     */       }
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/field/FieldCommon.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */