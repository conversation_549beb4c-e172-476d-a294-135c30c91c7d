/*     */ package weaver.workflow.field;
/*     */ 
/*     */ import java.util.regex.Matcher;
/*     */ import java.util.regex.Pattern;
/*     */ import weaver.cache.CacheBase;
/*     */ import weaver.cache.CacheColumn;
/*     */ import weaver.cache.CacheColumnType;
/*     */ import weaver.cache.PKColumn;
/*     */ import weaver.filter.XssUtil;
/*     */ import weaver.general.Util;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class UrlComInfo
/*     */   extends CacheBase
/*     */ {
/*  26 */   protected static String TABLE_NAME = "workflow_browserurl";
/*     */   
/*  28 */   protected static String TABLE_WHERE = null;
/*     */   
/*  30 */   protected static String TABLE_ORDER = "id";
/*     */   
/*     */   @PKColumn(type = CacheColumnType.NUMBER)
/*  33 */   protected static String PK_NAME = "id";
/*     */ 
/*     */   
/*     */   @CacheColumn
/*     */   protected static int labelid;
/*     */ 
/*     */   
/*     */   @CacheColumn
/*     */   protected static int fielddbtype;
/*     */   
/*     */   @CacheColumn
/*     */   protected static int browserurl;
/*     */ 
/*     */   
/*     */   public int getUrlNum() {
/*  48 */     return size();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public boolean next(String paramString) {
/*  59 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getUrlid() {
/*  68 */     return (String)getRowValue(0);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getUrlname() {
/*  77 */     return (String)getRowValue(labelid);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getUrlname(String paramString) {
/*  88 */     return (String)getValue(labelid, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getUrldbtype(String paramString) {
/* 100 */     return (String)getValue(fielddbtype, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getUrlbrowserurl(String paramString) {
/* 111 */     String str = (String)getValue(browserurl, paramString);
/* 112 */     StringBuffer stringBuffer = new StringBuffer();
/* 113 */     if (str != null && !"".equals(str) && str.indexOf("sqlwhere=") != -1) {
/* 114 */       String str1 = "([\\?&]sqlwhere=)(.*?)(&)";
/* 115 */       Pattern pattern = Pattern.compile(str1);
/* 116 */       Matcher matcher = pattern.matcher(str);
/* 117 */       String str2 = "";
/* 118 */       if (!matcher.find()) {
/* 119 */         str1 = "([\\?&]sqlwhere=)(.*)()";
/* 120 */         pattern = Pattern.compile(str1);
/* 121 */         matcher = pattern.matcher(str);
/* 122 */         if (matcher.find()) {
/* 123 */           str2 = Util.null2String(matcher.group(2));
/*     */         }
/*     */       } else {
/* 126 */         str2 = Util.null2String(matcher.group(2));
/*     */       } 
/* 128 */       if (!str2.equals("")) {
/* 129 */         XssUtil xssUtil = new XssUtil();
/* 130 */         matcher.appendReplacement(stringBuffer, matcher.group(1) + xssUtil.put(str2) + matcher.group(3));
/* 131 */         return stringBuffer.toString();
/*     */       } 
/*     */     } 
/* 134 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void removeUrlCache() {
/* 142 */     removeCache();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String changeBroserToMulBroser(String paramString) {
/* 153 */     String str = "0";
/* 154 */     int i = Util.getIntValue(paramString, 0);
/* 155 */     switch (i) {
/*     */       case 4:
/* 157 */         str = "57";
/*     */         break;
/*     */       case 7:
/* 160 */         str = "18";
/*     */         break;
/*     */       case 8:
/* 163 */         str = "135";
/*     */         break;
/*     */       case 9:
/* 166 */         str = "37";
/*     */         break;
/*     */       case 16:
/* 169 */         str = "152";
/*     */         break;
/*     */       case 41:
/* 172 */         str = "117";
/*     */         break;
/*     */       case 167:
/* 175 */         str = "168";
/*     */         break;
/*     */       case 169:
/* 178 */         str = "170";
/*     */         break;
/*     */     } 
/* 181 */     return str;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/field/UrlComInfo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */