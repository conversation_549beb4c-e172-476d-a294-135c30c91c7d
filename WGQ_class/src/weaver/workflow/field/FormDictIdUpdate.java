/*    */ package weaver.workflow.field;
/*    */ 
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.BaseBean;
/*    */ import weaver.general.Util;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class FormDictIdUpdate
/*    */   extends BaseBean
/*    */ {
/*    */   private boolean isoracle = false;
/* 28 */   private int id = -1;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public synchronized int getFormDictNewId() {
/* 44 */     RecordSet recordSet = new RecordSet();
/*    */     
/*    */     try {
/* 47 */       recordSet.executeProc("SequenceIndex_SWFformdictid", "");
/* 48 */       if (recordSet.next())
/*    */       {
/* 50 */         this.id = Util.getIntValue(recordSet.getString(1), -1);
/*    */       
/*    */       }
/*    */     }
/* 54 */     catch (Exception exception) {
/*    */       
/* 56 */       writeLog(exception.toString() + "新建字段时无法生成新的请求ID!!!");
/* 57 */       this.id = -1;
/*    */     } 
/* 59 */     return this.id;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/field/FormDictIdUpdate.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */