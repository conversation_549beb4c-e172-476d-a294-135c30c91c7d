/*     */ package weaver.workflow.field;
/*     */ 
/*     */ import java.text.DecimalFormat;
/*     */ import java.util.regex.Pattern;
/*     */ import weaver.general.Util;
/*     */ 
/*     */ 
/*     */ 
/*     */ public class FinancialElement
/*     */ {
/*  11 */   private static String[] finAllArray = new String[] { "分", "角", "元", "十", "百", "千", "万", "十", "百", "千", "亿", "十" };
/*  12 */   private static int finPerWidth = 26;
/*  13 */   private int usepdf = 0;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String analyzeFinancialHead(String paramString) {
/*  19 */     while (paramString.indexOf("_financialhead") > -1) {
/*  20 */       int i = paramString.indexOf("_financialhead");
/*  21 */       int j = paramString.indexOf(">", i);
/*  22 */       int k = paramString.indexOf("</td>", i);
/*  23 */       String str1 = paramString.substring(0, i) + ">";
/*  24 */       String str2 = paramString.substring(k);
/*  25 */       String str3 = paramString.substring(j + 1, k);
/*  26 */       str3.replaceAll("\r\n", "").replaceAll("\r", "").replaceAll("\n", "");
/*  27 */       String str4 = paramString.substring(i, j);
/*  28 */       int m = 3;
/*  29 */       if (str4.indexOf("$[") > -1 && str4.indexOf("]$") > -1) {
/*  30 */         m = Util.getIntValue(str4.substring(str4.indexOf("$[") + 2, str4.indexOf("]$")), 3);
/*     */       }
/*  32 */       String[] arrayOfString = new String[m];
/*  33 */       System.arraycopy(finAllArray, 0, arrayOfString, 0, m);
/*  34 */       StringBuilder stringBuilder = new StringBuilder();
/*  35 */       stringBuilder.append("<div class=\"ftitlediv\">");
/*  36 */       stringBuilder.append("<div class=\"ftop\">");
/*  37 */       stringBuilder.append("<table style='width:100%;height:100%;border:0px;border-spacing:0px;'><tr><td style='height:100% !important'>");
/*  38 */       stringBuilder.append(str3);
/*  39 */       stringBuilder.append("</td></tr></table>");
/*  40 */       stringBuilder.append("</div>");
/*  41 */       stringBuilder.append("<div class=\"fbottom\">");
/*  42 */       if (this.usepdf == 1) {
/*  43 */         stringBuilder.append("<table class='fintable' width=100%><tr>");
/*  44 */         String str = (100 / m) + "%";
/*  45 */         for (int n = arrayOfString.length - 1; n >= 0; n--) {
/*  46 */           String str5 = "fborder1";
/*  47 */           if (n == 0) {
/*  48 */             str5 = "";
/*  49 */           } else if (n == 2) {
/*  50 */             str5 = "fborder2";
/*  51 */           } else if ((n - 2) % 3 == 0) {
/*  52 */             str5 = "fborder3";
/*     */           } 
/*  54 */           stringBuilder.append("<td style='width:").append(str).append("px;' ");
/*  55 */           stringBuilder.append("class='").append(str5).append("'>");
/*  56 */           stringBuilder.append(arrayOfString[n]);
/*  57 */           stringBuilder.append("</td>");
/*     */         } 
/*     */       } else {
/*  60 */         stringBuilder.append("<table class='fintable'><tr>");
/*  61 */         for (int n = arrayOfString.length - 1; n >= 0; n--) {
/*  62 */           String str = "fborder1";
/*  63 */           if (n == 0) {
/*  64 */             str = "";
/*  65 */           } else if (n == 2) {
/*  66 */             str = "fborder2";
/*  67 */           } else if ((n - 2) % 3 == 0) {
/*  68 */             str = "fborder3";
/*     */           } 
/*  70 */           stringBuilder.append("<td style='width:").append(finPerWidth).append("px;min-width:").append(finPerWidth).append("px' ");
/*  71 */           stringBuilder.append("class='").append(str).append("'>");
/*  72 */           stringBuilder.append(arrayOfString[n]);
/*  73 */           stringBuilder.append("</td>");
/*     */         } 
/*     */       } 
/*  76 */       stringBuilder.append("</tr></table>");
/*  77 */       stringBuilder.append("</div>");
/*  78 */       stringBuilder.append("</div>");
/*  79 */       paramString = str1 + stringBuilder + str2;
/*     */     } 
/*  81 */     return paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getFinancialFieldStr(String paramString1, String paramString2, String paramString3, int paramInt1, int paramInt2, int paramInt3, int paramInt4) {
/*  88 */     int i = Integer.parseInt(paramString1);
/*  89 */     String[] arrayOfString = new String[i];
/*  90 */     System.arraycopy(finAllArray, 0, arrayOfString, 0, i);
/*  91 */     StringBuilder stringBuilder = new StringBuilder();
/*  92 */     stringBuilder.append("<div class='ffielddiv' id='" + paramString2 + "_fdiv' ");
/*  93 */     if (paramInt1 == 1) {
/*  94 */       stringBuilder.append("onclick='javascript:editFinancialField(this);' ");
/*     */     }
/*  96 */     stringBuilder.append(">");
/*  97 */     if (this.usepdf == 1) {
/*  98 */       stringBuilder.append("<table class='fintable' width=100%><tr>");
/*     */     } else {
/* 100 */       stringBuilder.append("<table class='fintable'><tr>");
/* 101 */     }  char[] arrayOfChar = new char[0];
/*     */     
/* 103 */     if (paramInt3 == 1 && paramInt4 == 5) {
/* 104 */       paramString3 = paramString3.replaceAll(",", "");
/*     */     }
/* 106 */     String str1 = "(-?\\d+)(\\.\\d+)?";
/* 107 */     String str2 = "(-?\\d*)(\\.\\d+)";
/* 108 */     if (Pattern.matches(str1, paramString3) || Pattern.matches(str2, paramString3)) {
/* 109 */       arrayOfChar = getValCharArr(paramString3);
/*     */     }
/* 111 */     if (this.usepdf == 1) {
/* 112 */       String str = (100 / i) + "%";
/* 113 */       for (int j = arrayOfString.length - 1; j >= 0; j--) {
/* 114 */         String str3 = "fborder1";
/* 115 */         if (j == 0) {
/* 116 */           str3 = "";
/* 117 */         } else if (j == 2) {
/* 118 */           str3 = "fborder2";
/* 119 */         } else if ((j - 2) % 3 == 0) {
/* 120 */           str3 = "fborder3";
/*     */         } 
/* 122 */         stringBuilder.append("<td style='width:").append(str).append("' ");
/* 123 */         stringBuilder.append("class='").append(str3).append("' >");
/* 124 */         if (arrayOfChar.length == 0) {
/* 125 */           if (paramInt2 == 1 && j == 0) {
/* 126 */             stringBuilder.append("<img src='/images/BacoError_wev8.gif' align='absmiddle' />");
/*     */           }
/*     */         }
/* 129 */         else if (j < arrayOfChar.length) {
/* 130 */           stringBuilder.append(arrayOfChar[j]);
/*     */         } 
/*     */         
/* 133 */         stringBuilder.append("</td>");
/*     */       } 
/*     */     } else {
/* 136 */       for (int j = arrayOfString.length - 1; j >= 0; j--) {
/* 137 */         String str = "fborder1";
/* 138 */         if (j == 0) {
/* 139 */           str = "";
/* 140 */         } else if (j == 2) {
/* 141 */           str = "fborder2";
/* 142 */         } else if ((j - 2) % 3 == 0) {
/* 143 */           str = "fborder3";
/*     */         } 
/* 145 */         stringBuilder.append("<td style='width:").append(finPerWidth).append("px;min-width:").append(finPerWidth).append("px' ");
/* 146 */         stringBuilder.append("class='").append(str).append("' >");
/* 147 */         if (arrayOfChar.length == 0) {
/* 148 */           if (paramInt2 == 1 && j == 0) {
/* 149 */             stringBuilder.append("<img src='/images/BacoError_wev8.gif' align='absmiddle' />");
/*     */           }
/*     */         }
/* 152 */         else if (j < arrayOfChar.length) {
/* 153 */           stringBuilder.append(arrayOfChar[j]);
/*     */         } 
/*     */         
/* 156 */         stringBuilder.append("</td>");
/*     */       } 
/*     */     } 
/* 159 */     stringBuilder.append("</tr></table>");
/* 160 */     stringBuilder.append("</div>");
/* 161 */     return stringBuilder.toString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getFinancialSumStr(String paramString1, String paramString2) {
/* 168 */     int i = Integer.parseInt(paramString1);
/* 169 */     String[] arrayOfString = new String[i];
/* 170 */     System.arraycopy(finAllArray, 0, arrayOfString, 0, i);
/* 171 */     StringBuilder stringBuilder = new StringBuilder();
/* 172 */     stringBuilder.append("<div class='ffielddiv' id='" + paramString2 + "_fdiv'>");
/* 173 */     stringBuilder.append("<table class='fintable'><tr>");
/* 174 */     for (int j = arrayOfString.length - 1; j >= 0; j--) {
/* 175 */       String str = "fborder1";
/* 176 */       if (j == 0) {
/* 177 */         str = "";
/* 178 */       } else if (j == 2) {
/* 179 */         str = "fborder2";
/* 180 */       } else if ((j - 2) % 3 == 0) {
/* 181 */         str = "fborder3";
/*     */       } 
/* 183 */       stringBuilder.append("<td style='width:").append(finPerWidth).append("px;min-width:").append(finPerWidth).append("px' ");
/* 184 */       stringBuilder.append("class='").append(str).append("' >");
/* 185 */       stringBuilder.append("</td>");
/*     */     } 
/* 187 */     stringBuilder.append("</tr></table>");
/* 188 */     stringBuilder.append("</div>");
/* 189 */     return stringBuilder.toString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public char[] getValCharArr(String paramString) {
/* 197 */     DecimalFormat decimalFormat = new DecimalFormat("#.00");
/* 198 */     String str = decimalFormat.format(Double.parseDouble(paramString));
/* 199 */     int i = str.length() - 1;
/* 200 */     char[] arrayOfChar = new char[i];
/* 201 */     byte b = 0;
/* 202 */     for (int j = i; j >= 0; j--) {
/* 203 */       char c = str.charAt(j);
/* 204 */       if (c != '.') {
/* 205 */         arrayOfChar[b] = c;
/* 206 */         b++;
/*     */       } 
/* 208 */     }  return arrayOfChar;
/*     */   }
/*     */   
/*     */   public void setUsepdf(int paramInt) {
/* 212 */     this.usepdf = paramInt;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/field/FinancialElement.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */