/*     */ package weaver.workflow.field;
/*     */ 
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.company.SubCompanyComInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class SubcompanyDocCategoryManager
/*     */   extends BaseBean
/*     */ {
/*     */   private static final String subCompanyIcon = "/images/treeimages/Home_wev8.gif";
/*     */   
/*     */   public String getOrgTreeXMLByComp(int paramInt1, int paramInt2, int paramInt3) throws Exception {
/*  35 */     String str = "";
/*  36 */     SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/*  37 */     subCompanyComInfo.setTofirstRow();
/*  38 */     while (subCompanyComInfo.next()) {
/*  39 */       String str1 = subCompanyComInfo.getSupsubcomid();
/*  40 */       if (!str1.equals("0"))
/*  41 */         continue;  String str2 = subCompanyComInfo.getSubCompanyid();
/*  42 */       String str3 = subCompanyComInfo.getSubCompanyname();
/*  43 */       String str4 = SubcompanyDocCategoryUtil.getDocPath(paramInt1, paramInt2, paramInt3, Integer.parseInt(str2), "1");
/*  44 */       str3 = Util.replace(Util.replace(Util.replace(Util.replace(Util.replace(str3, "<", "&lt;", 0), ">", "&gt;", 0), "&", "&amp;", 0), "'", "&apos;", 0), "\"", "&quot;", 0);
/*  45 */       str = str + "<tree text=\"" + str3 + "\" action=\"" + str2 + "|1|" + str4 + "\" icon=\"" + "/images/treeimages/Home_wev8.gif" + "\" openIcon=\"" + "/images/treeimages/Home_wev8.gif" + "\" src=\"SubcompanyDocCategoryListTree.jsp?subCompanyId=" + str2 + "&amp;fieldId=" + paramInt1 + "&amp;isBill=" + paramInt2 + "&amp;selectValue=" + paramInt3 + "\"/>";
/*     */     } 
/*  47 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getSubCompanyTreeJSByComp(int paramInt1, int paramInt2, int paramInt3) throws Exception {
/*  61 */     String str = "";
/*  62 */     SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/*  63 */     subCompanyComInfo.setTofirstRow();
/*  64 */     while (subCompanyComInfo.next()) {
/*  65 */       String str1 = subCompanyComInfo.getSupsubcomid();
/*  66 */       if (!str1.equals("0"))
/*  67 */         continue;  String str2 = subCompanyComInfo.getSubCompanyid();
/*  68 */       String str3 = subCompanyComInfo.getSubCompanyname();
/*  69 */       String str4 = SubcompanyDocCategoryUtil.getDocPath(paramInt1, paramInt2, paramInt3, Integer.parseInt(str2), "1");
/*  70 */       str = str + "tree.add(rti = new WebFXLoadTreeItem('" + str3 + "','SubcompanyDocCategoryListTree.jsp?subCompanyId=" + str2 + "&fieldId=" + paramInt1 + "&isBill=" + paramInt2 + "&selectValue=" + paramInt3 + "','" + str2 + "|1|" + str4 + "','','" + "/images/treeimages/Home_wev8.gif" + "','" + "/images/treeimages/Home_wev8.gif" + "'));";
/*     */     } 
/*  72 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getOrgTreeXMLBySubComp(int paramInt1, int paramInt2, int paramInt3, String paramString1, String paramString2) throws Exception {
/*  89 */     String str = "";
/*  90 */     SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/*  91 */     subCompanyComInfo.setTofirstRow();
/*  92 */     while (subCompanyComInfo.next()) {
/*  93 */       String str1 = subCompanyComInfo.getSupsubcomid();
/*  94 */       if (!str1.equals(paramString1))
/*  95 */         continue;  String str2 = subCompanyComInfo.getSubCompanyid();
/*  96 */       String str3 = subCompanyComInfo.getSubCompanyname();
/*  97 */       String str4 = SubcompanyDocCategoryUtil.getDocPath(paramInt1, paramInt2, paramInt3, Integer.parseInt(str2), "1");
/*  98 */       if (!paramString2.equals("")) {
/*  99 */         str3 = Util.replace(Util.replace(Util.replace(Util.replace(Util.replace(str3, "<", "&lt;", 0), ">", "&gt;", 0), "&", "&amp;", 0), "'", "&apos;", 0), "\"", "&quot;", 0);
/* 100 */         str = str + "<tree text=\"" + str3 + "\" action=\"" + str2 + "|1|" + str4 + "\" icon=\"" + "/images/treeimages/Home_wev8.gif" + "\" openIcon=\"" + "/images/treeimages/Home_wev8.gif" + "\" src=\"SubcompanyDocCategoryListTree.jsp?subCompanyId=" + str2 + "&amp;fieldId=" + paramInt1 + "&amp;isBill=" + paramInt2 + "&amp;selectValue=" + paramInt3 + "\"/>"; continue;
/*     */       } 
/* 102 */       str3 = Util.replace(Util.replace(Util.replace(Util.replace(Util.replace(str3, "<", "&lt;", 0), ">", "&gt;", 0), "&", "&amp;", 0), "'", "&apos;", 0), "\"", "&quot;", 0);
/* 103 */       str = str + "<tree text=\"" + str3 + "\" action=\"setSubcompany('com_" + str2 + "')\" icon=\"" + "/images/treeimages/Home_wev8.gif" + "\" openIcon=\"" + "/images/treeimages/Home_wev8.gif" + "\" src=\"SubcompanyDocCategoryListTree.jsp?subCompanyId=" + str2 + "&amp;fieldId=" + paramInt1 + "&amp;isBill=" + paramInt2 + "&amp;selectValue=" + paramInt3 + "\"/>";
/*     */     } 
/*     */ 
/*     */     
/* 107 */     return str;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/field/SubcompanyDocCategoryManager.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */