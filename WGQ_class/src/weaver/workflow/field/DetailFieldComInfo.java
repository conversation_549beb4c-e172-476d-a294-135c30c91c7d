/*     */ package weaver.workflow.field;
/*     */ 
/*     */ import weaver.cache.CacheBase;
/*     */ import weaver.cache.CacheColumn;
/*     */ import weaver.cache.CacheColumnType;
/*     */ import weaver.cache.PKColumn;
/*     */ import weaver.general.Util;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class DetailFieldComInfo
/*     */   extends CacheBase
/*     */ {
/*  23 */   protected static String TABLE_NAME = "workflow_formdictdetail";
/*     */   
/*  25 */   protected static String TABLE_WHERE = null;
/*     */   
/*  27 */   protected static String TABLE_ORDER = "fieldname";
/*     */   
/*     */   @PKColumn(type = CacheColumnType.NUMBER)
/*  30 */   protected static String PK_NAME = "id";
/*     */   
/*     */   @CacheColumn
/*     */   protected static int fieldname;
/*     */   
/*     */   @CacheColumn
/*     */   protected static int fielddbtype;
/*     */   
/*     */   @CacheColumn
/*     */   protected static int fieldhtmltype;
/*     */   
/*     */   @CacheColumn
/*     */   protected static int type;
/*     */   
/*     */   @CacheColumn
/*     */   protected static int description;
/*     */   
/*     */   @CacheColumn
/*     */   protected static int imgwidth;
/*     */   @CacheColumn
/*     */   protected static int imgheight;
/*     */   
/*     */   public int getFieldNum() {
/*  53 */     return size();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public boolean next(String paramString) {
/*  64 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getFieldid() {
/*  74 */     return (String)getRowValue(0);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getFieldname() {
/*  83 */     return (String)getRowValue(fieldname);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getFieldname(String paramString) {
/*  94 */     return (String)getValue(fieldname, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getFielddbtype(String paramString) {
/* 105 */     return (String)getValue(fielddbtype, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getFieldhtmltype(String paramString) {
/* 116 */     return (String)getValue(fieldhtmltype, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getFieldType(String paramString) {
/* 127 */     return (String)getValue(type, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getFieldDesc(String paramString) {
/* 138 */     return (String)getValue(description, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getImgWidth() {
/* 147 */     return Util.getIntValue((String)getRowValue(imgwidth));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getImgWidth(String paramString) {
/* 157 */     return Util.getIntValue((String)getValue(imgwidth, paramString));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getImgHeight() {
/* 166 */     return Util.getIntValue((String)getRowValue(imgheight));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getImgHeight(String paramString) {
/* 176 */     return Util.getIntValue((String)getValue(imgheight, paramString));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void removeFieldCache() {
/* 184 */     removeCache();
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/field/DetailFieldComInfo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */