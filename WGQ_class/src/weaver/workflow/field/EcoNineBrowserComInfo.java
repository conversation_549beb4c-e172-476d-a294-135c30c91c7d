/*     */ package weaver.workflow.field;
/*     */ 
/*     */ import com.weaver.integration.util.IntegratedSapUtil;
/*     */ import java.util.Arrays;
/*     */ import java.util.List;
/*     */ import java.util.regex.Matcher;
/*     */ import java.util.regex.Pattern;
/*     */ import weaver.cache.CacheBase;
/*     */ import weaver.cache.CacheColumn;
/*     */ import weaver.cache.CacheColumnType;
/*     */ import weaver.cache.CacheItem;
/*     */ import weaver.cache.CacheMap;
/*     */ import weaver.cache.PKColumn;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.filter.XssUtil;
/*     */ import weaver.general.StaticObj;
/*     */ import weaver.general.Util;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class EcoNineBrowserComInfo
/*     */   extends CacheBase
/*     */ {
/*     */   @PKColumn(type = CacheColumnType.NUMBER)
/*  30 */   protected static String PK_NAME = "type";
/*     */ 
/*     */   
/*     */   @CacheColumn
/*     */   protected static int browserurl;
/*     */ 
/*     */   
/*     */   @CacheColumn
/*     */   protected static int fielddbtype;
/*     */ 
/*     */   
/*     */   @CacheColumn
/*     */   protected static int labelid;
/*     */   
/*     */   @CacheColumn
/*     */   protected static int tablename;
/*     */   
/*     */   @CacheColumn
/*     */   protected static int columname;
/*     */   
/*     */   @CacheColumn
/*     */   protected static int keycolumname;
/*     */   
/*     */   @CacheColumn
/*     */   protected static int linkurl;
/*     */   
/*     */   @CacheColumn
/*     */   protected static int py;
/*     */   
/*  59 */   private List<String> Browser_noSelects = Arrays.asList(new String[] { "10", "11", "64", "6", "56", "5", "3", "26", "235", "242", "243", "246", "258", "261", "264", "265", "266", "267" });
/*     */   
/*  61 */   private XssUtil xssUtil = null;
/*     */   
/*     */   public EcoNineBrowserComInfo() {
/*  64 */     this.xssUtil = new XssUtil();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public CacheMap initCache() throws Exception {
/*  72 */     CacheMap cacheMap = createCacheMap();
/*     */     
/*  74 */     RecordSet recordSet = new RecordSet();
/*     */     
/*  76 */     recordSet.executeSql(getQuerySql(recordSet.getDBType(), (String)null));
/*     */     
/*  78 */     while (recordSet.next()) {
/*  79 */       String str = Util.null2String(recordSet.getString(PK_NAME));
/*  80 */       CacheItem cacheItem = createCacheItem();
/*  81 */       parseResultSetToCacheItem(recordSet, cacheItem);
/*     */       
/*  83 */       modifyCacheItem(str, cacheItem);
/*  84 */       cacheMap.put(str, cacheItem);
/*     */     } 
/*     */     
/*  87 */     StaticObj.getInstance().putObject("PluginBrowserComInfoUpdate", "1");
/*  88 */     return cacheMap;
/*     */   }
/*     */ 
/*     */   
/*     */   protected void modifyCacheItem(String paramString, CacheItem paramCacheItem) {
/*  93 */     String str = (String)paramCacheItem.get(py);
/*  94 */     paramCacheItem.set(py, str.trim().replaceAll("[^a-zA-Z]", ""));
/*     */   }
/*     */   
/*     */   protected CacheItem initCache(String paramString) {
/*  98 */     if (paramString == null || paramString.trim().length() == 0) {
/*  99 */       return null;
/*     */     }
/* 101 */     RecordSet recordSet = new RecordSet();
/*     */     
/* 103 */     recordSet.executeQuery(getQuerySql(recordSet.getDBType(), paramString), new Object[] { paramString });
/* 104 */     if (recordSet.next()) {
/* 105 */       CacheItem cacheItem = createCacheItem();
/* 106 */       parseResultSetToCacheItem(recordSet, cacheItem);
/* 107 */       modifyCacheItem(paramString, cacheItem);
/* 108 */       return cacheItem;
/*     */     } 
/* 110 */     return null;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static String getQuerySql(String paramString1, String paramString2) {
/* 121 */     String str = "select w.* from wf_browser_config w where 1=1  and";
/*     */     
/* 123 */     if (paramString2 == null || paramString2.trim().length() == 0) {
/* 124 */       String str1 = IntegratedSapUtil.getIsOpenEcology70Sap();
/* 125 */       if ("0".equals(str1)) {
/* 126 */         str = str + "  type not in (224,225, 14, 15 ) order by type";
/*     */       } else {
/* 128 */         str = str + "  type not in (225, 14, 15 ) order by type";
/*     */       } 
/*     */     } else {
/* 131 */       str = str + "  type = ?";
/*     */     } 
/* 133 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getBrowserNum() {
/* 142 */     return size();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public boolean next(String paramString) {
/* 153 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setToFirstrow() {
/* 161 */     setTofirstRow();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getBrowserid() {
/* 170 */     return (String)getRowValue(0);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getBrowserurl() {
/* 179 */     String str = (String)getRowValue(browserurl);
/* 180 */     StringBuffer stringBuffer = new StringBuffer();
/* 181 */     if (str != null && !"".equals(str) && str.indexOf("sqlwhere=") != -1) {
/* 182 */       String str1 = "[\\?&]sqlwhere=(.*?)&";
/* 183 */       Pattern pattern = Pattern.compile(str1);
/* 184 */       Matcher matcher = pattern.matcher(str);
/* 185 */       String str2 = "";
/* 186 */       if (!matcher.find()) {
/* 187 */         str1 = "[\\?&]sqlwhere=(.*)";
/* 188 */         pattern = Pattern.compile(str1);
/* 189 */         matcher = pattern.matcher(str);
/* 190 */         if (matcher.find()) {
/* 191 */           str2 = Util.null2String(matcher.group(1));
/*     */         }
/*     */       } else {
/* 194 */         str2 = Util.null2String(matcher.group(1));
/*     */       } 
/* 196 */       if (!str2.equals("")) {
/*     */         
/* 198 */         str = str.replaceAll(str2, this.xssUtil.put(str2));
/* 199 */         return str;
/*     */       } 
/*     */     } 
/* 202 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getBrowserurl(String paramString) {
/* 213 */     String str = (String)getValue(browserurl, paramString);
/* 214 */     StringBuffer stringBuffer = new StringBuffer();
/* 215 */     if (str != null && !"".equals(str) && str.indexOf("sqlwhere=") != -1) {
/* 216 */       String str1 = "([\\?&]sqlwhere=)(.*?)(&)";
/* 217 */       Pattern pattern = Pattern.compile(str1);
/* 218 */       Matcher matcher = pattern.matcher(str);
/* 219 */       String str2 = "";
/* 220 */       if (!matcher.find()) {
/* 221 */         str1 = "([\\?&]sqlwhere=)(.*)()";
/* 222 */         pattern = Pattern.compile(str1);
/* 223 */         matcher = pattern.matcher(str);
/* 224 */         if (matcher.find()) {
/* 225 */           str2 = Util.null2String(matcher.group(2));
/*     */         }
/*     */       } else {
/* 228 */         str2 = Util.null2String(matcher.group(2));
/*     */       } 
/* 230 */       if (!str2.equals("")) {
/* 231 */         matcher.appendReplacement(stringBuffer, matcher.group(1) + this.xssUtil.put(str2) + matcher.group(3));
/* 232 */         return stringBuffer.toString();
/*     */       } 
/*     */     } 
/* 235 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getLinkurl() {
/* 244 */     return (String)getRowValue(linkurl);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getBrowserlabelid() {
/* 253 */     return (String)getRowValue(labelid);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getBrowserlabelid(String paramString) {
/* 264 */     return (String)getValue(labelid, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getLinkurl(String paramString) {
/* 275 */     return (String)getValue(linkurl, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getBrowserdbtype() {
/* 284 */     return (String)getRowValue(fielddbtype);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getBrowserdbtype(String paramString) {
/* 295 */     return (String)getValue(fielddbtype, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getBrowsertablename() {
/* 304 */     return (String)getRowValue(tablename);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getBrowsertablename(String paramString) {
/* 315 */     return (String)getValue(tablename, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getBrowsercolumname() {
/* 324 */     return (String)getRowValue(columname);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getBrowsercolumname(String paramString) {
/* 335 */     return (String)getValue(columname, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getBrowserkeycolumname() {
/* 344 */     return (String)getRowValue(keycolumname);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getBrowserkeycolumname(String paramString) {
/* 355 */     return (String)getValue(keycolumname, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getBrowserPY(int paramInt) {
/* 364 */     if (7 == paramInt) {
/* 365 */       return (String)getRowValue(py);
/*     */     }
/* 367 */     return "";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean notCanSelect() {
/* 376 */     return this.Browser_noSelects.contains(getBrowserid());
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void removeBrowserCache() {
/* 384 */     removeCache();
/* 385 */     StaticObj.getInstance().putObject("PluginBrowserComInfoUpdate", "1");
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/field/EcoNineBrowserComInfo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */