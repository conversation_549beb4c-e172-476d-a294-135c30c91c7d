/*      */ package weaver.workflow.field;
/*      */ 
/*      */ import java.util.ArrayList;
/*      */ import java.util.List;
/*      */ import weaver.conn.ConnStatement;
/*      */ import weaver.conn.RecordSet;
/*      */ import weaver.general.BaseBean;
/*      */ import weaver.general.Util;
/*      */ import weaver.systeminfo.SystemEnv;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ public class FieldManager
/*      */   extends BaseBean
/*      */ {
/*   24 */   private int languageid = 7;
/*      */ 
/*      */ 
/*      */   
/*   28 */   private int fieldid = 0;
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*   33 */   private String fieldname = "";
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*   38 */   private String istemplate = "";
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*   43 */   private String qfwws = "";
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*   48 */   private String fielddbtype = "";
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*   53 */   private String fieldhtmltype = "";
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*   58 */   private int type = 0;
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*   63 */   private int subCompanyId2 = 0;
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*   68 */   private String action = "";
/*      */ 
/*      */ 
/*      */   
/*   72 */   private String isused = "";
/*      */ 
/*      */ 
/*      */   
/*   76 */   private String description = "";
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*   81 */   private int textheight = 4;
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*   86 */   private String textheight_2 = "";
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*   91 */   private int childfieldid = 0;
/*   92 */   private int imgwidth = 0;
/*   93 */   private int imgheight = 0;
/*   94 */   private String locatetype = "";
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*   99 */   private String fieldshowtypes = "";
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void reset() {
/*  116 */     this.fieldid = 0;
/*  117 */     this.fieldname = "";
/*  118 */     this.fielddbtype = "";
/*  119 */     this.fieldhtmltype = "";
/*  120 */     this.type = 0;
/*  121 */     this.action = "";
/*  122 */     this.description = "";
/*  123 */     this.textheight = 4;
/*  124 */     this.childfieldid = 0;
/*  125 */     this.imgwidth = 0;
/*  126 */     this.imgheight = 0;
/*  127 */     this.qfwws = "";
/*      */     
/*  129 */     this.textheight_2 = "";
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setAction(String paramString) {
/*  140 */     this.action = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public int getFieldid() {
/*  150 */     return this.fieldid;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setFieldid(int paramInt) {
/*  161 */     this.fieldid = paramInt;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getFieldname() {
/*  171 */     return this.fieldname;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setFieldname(String paramString) {
/*  182 */     this.fieldname = paramString;
/*      */   }
/*      */   
/*      */   public String getIstemplate() {
/*  186 */     return this.istemplate;
/*      */   }
/*      */   
/*      */   public void setIstemplate(String paramString) {
/*  190 */     this.istemplate = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getFielddbtype() {
/*  200 */     return this.fielddbtype;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setFielddbtype(String paramString) {
/*  211 */     this.fielddbtype = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getFieldhtmltype() {
/*  221 */     return this.fieldhtmltype;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setFieldhtmltype(String paramString) {
/*  232 */     this.fieldhtmltype = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public int getType() {
/*  242 */     return this.type;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public int getSubCompanyId2() {
/*  252 */     return this.subCompanyId2;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setSubCompanyId2(int paramInt) {
/*  263 */     this.subCompanyId2 = paramInt;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setType(int paramInt) {
/*  274 */     this.type = paramInt;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setDescription(String paramString) {
/*  286 */     this.description = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getDescription() {
/*  296 */     return this.description;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setTextheight(int paramInt) {
/*  311 */     this.textheight = paramInt;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public int getTextheight() {
/*  321 */     return this.textheight;
/*      */   }
/*      */   
/*      */   public void setChildfieldid(int paramInt) {
/*  325 */     this.childfieldid = paramInt;
/*      */   }
/*      */   
/*      */   public int getChildfieldid() {
/*  329 */     return this.childfieldid;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public int getImgwidth() {
/*  337 */     return this.imgwidth;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setImgwidth(int paramInt) {
/*  345 */     this.imgwidth = (paramInt < 1) ? 0 : paramInt;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public int getImgheight() {
/*  353 */     return this.imgheight;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setImgheight(int paramInt) {
/*  361 */     this.imgheight = (paramInt < 1) ? 0 : paramInt;
/*      */   }
/*      */   
/*      */   public String getTextheight_2() {
/*  365 */     return this.textheight_2;
/*      */   }
/*      */   
/*      */   public void setTextheight_2(String paramString) {
/*  369 */     this.textheight_2 = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void getFieldInfo() throws Exception {
/*  381 */     String str = "select * from workflow_formdict where id=?";
/*      */     
/*  383 */     ConnStatement connStatement = null;
/*      */     
/*      */     try {
/*  386 */       connStatement = new ConnStatement();
/*  387 */       connStatement.setStatementSql(str);
/*  388 */       connStatement.setInt(1, this.fieldid);
/*  389 */       connStatement.executeQuery();
/*  390 */       if (!connStatement.next()) {
/*      */         
/*  392 */         connStatement.close();
/*      */         
/*      */         return;
/*      */       } 
/*  396 */       setFieldid(connStatement.getInt("id"));
/*  397 */       setFieldname(Util.null2String(connStatement.getString("fieldname")));
/*  398 */       setIstemplate(Util.null2String(connStatement.getString("istemplate")));
/*  399 */       setFielddbtype(Util.null2String(connStatement.getString("fielddbtype")));
/*  400 */       setFieldhtmltype(Util.null2String(connStatement.getString("fieldhtmltype")));
/*  401 */       setType(connStatement.getInt("type"));
/*  402 */       this.subCompanyId2 = connStatement.getInt("subcompanyid");
/*  403 */       setDescription(Util.null2String(connStatement.getString("description")));
/*  404 */       setTextheight(connStatement.getInt("textheight"));
/*  405 */       setChildfieldid(connStatement.getInt("childfieldid"));
/*  406 */       setImgwidth(connStatement.getInt("imgwidth"));
/*  407 */       setImgheight(connStatement.getInt("imgheight"));
/*  408 */       setQfwws(connStatement.getString("qfws"));
/*  409 */       setLocatetype(connStatement.getString("locatetype"));
/*      */ 
/*      */       
/*  412 */       setFieldshowtypes(Util.null2String(connStatement.getString("fieldshowtypes"), "1"));
/*      */       
/*  414 */       setTextheight_2(connStatement.getString("textheight_2"));
/*      */     }
/*  416 */     catch (Exception exception) {
/*      */       
/*  418 */       writeLog(exception);
/*  419 */       throw exception;
/*      */     } finally {
/*      */ 
/*      */       
/*      */       try {
/*      */         
/*  425 */         connStatement.close();
/*      */       }
/*  427 */       catch (Exception exception) {}
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void getDetailFieldInfo() throws Exception {
/*  441 */     String str = "select * from workflow_formdictdetail where id=?";
/*  442 */     ConnStatement connStatement = null;
/*      */ 
/*      */     
/*      */     try {
/*  446 */       connStatement = new ConnStatement();
/*  447 */       connStatement.setStatementSql(str);
/*  448 */       connStatement.setInt(1, this.fieldid);
/*  449 */       connStatement.executeQuery();
/*  450 */       if (!connStatement.next()) {
/*      */         
/*  452 */         connStatement.close();
/*      */         
/*      */         return;
/*      */       } 
/*  456 */       setFieldid(connStatement.getInt("id"));
/*  457 */       setFieldname(Util.null2String(connStatement.getString("fieldname")));
/*  458 */       setIstemplate(Util.null2String(connStatement.getString("istemplate")));
/*  459 */       setFielddbtype(Util.null2String(connStatement.getString("fielddbtype")));
/*  460 */       setFieldhtmltype(Util.null2String(connStatement.getString("fieldhtmltype")));
/*  461 */       setType(connStatement.getInt("type"));
/*  462 */       this.subCompanyId2 = connStatement.getInt("subcompanyid");
/*  463 */       setDescription(Util.null2String(connStatement.getString("description")));
/*  464 */       setTextheight(connStatement.getInt("textheight"));
/*  465 */       setChildfieldid(connStatement.getInt("childfieldid"));
/*  466 */       setQfwws(connStatement.getString("qfws"));
/*  467 */       setTextheight_2(connStatement.getString("textheight_2"));
/*  468 */       setImgwidth(Util.getIntValue("" + connStatement.getInt("imgwidth"), 50));
/*  469 */       setImgheight(Util.getIntValue("" + connStatement.getInt("imgheight"), 50));
/*      */ 
/*      */       
/*  472 */       setFieldshowtypes(Util.null2String(connStatement.getString("fieldshowtypes"), "1"));
/*      */     }
/*  474 */     catch (Exception exception) {
/*      */       
/*  476 */       writeLog(exception);
/*  477 */       throw exception;
/*      */     } finally {
/*      */ 
/*      */       
/*      */       try {
/*      */         
/*  483 */         connStatement.close();
/*      */       }
/*  485 */       catch (Exception exception) {}
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String setFieldInfo() throws Exception {
/*  498 */     String str1 = SystemEnv.getHtmlLabelName(19429, this.languageid);
/*  499 */     String str2 = "insert into workflow_formdict(fieldname,fielddbtype,fieldhtmltype,type,id,subcompanyid,description,textheight,textheight_2,childfieldid,imgwidth,imgheight,qfws,locatetype,istemplate,fieldshowtypes) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
/*  500 */     String str3 = "update workflow_formdict set fieldname=?,fielddbtype=?,fieldhtmltype=?,type=?,subcompanyid=?,description=?,textheight=?,textheight_2=?,childfieldid=?,imgwidth=?,imgheight=?,qfws=?,locatetype=?,istemplate=?,fieldshowtypes=? where id=?";
/*      */     
/*  502 */     String str4 = "select id from workflow_formdict where Upper(fieldname)=?";
/*      */     
/*  504 */     String str5 = "alter table workflow_form ";
/*  505 */     ConnStatement connStatement = null;
/*      */     
/*  507 */     String str6 = "";
/*      */     
/*      */     try {
/*  510 */       connStatement = new ConnStatement();
/*  511 */       if (this.action.equalsIgnoreCase("addfield"))
/*      */       {
/*      */ 
/*      */         
/*  515 */         FormDictIdUpdate formDictIdUpdate = new FormDictIdUpdate();
/*  516 */         int i = formDictIdUpdate.getFormDictNewId();
/*      */ 
/*      */ 
/*      */         
/*  520 */         connStatement.setStatementSql(str4);
/*  521 */         connStatement.setString(1, this.fieldname.toUpperCase());
/*  522 */         connStatement.executeQuery();
/*  523 */         if (connStatement.next())
/*      */         {
/*  525 */           return "1";
/*      */         }
/*      */         
/*  528 */         if (this.type == 161 || this.type == 162) {
/*  529 */           boolean bool = (new RecordSet()).getDBType().equals("oracle");
/*  530 */           if (bool) {
/*  531 */             str5 = str5 + "add " + this.fieldname + "  varchar2(200)";
/*      */           } else {
/*  533 */             str5 = str5 + "add " + this.fieldname + "  varchar(200)";
/*      */           } 
/*  535 */         } else if (this.type == 224 || this.type == 225) {
/*  536 */           boolean bool = (new RecordSet()).getDBType().equals("oracle");
/*  537 */           String str = "";
/*  538 */           if (this.type == 224) {
/*  539 */             str = "varchar(1000)";
/*  540 */             if (bool) {
/*  541 */               str = "varchar2(1000)";
/*      */             }
/*      */           } else {
/*  544 */             str = "varchar(4000)";
/*  545 */             if (bool) {
/*  546 */               str = "varchar2(4000)";
/*  547 */             } else if ((new RecordSet()).getDBType().equals("mysql")) {
/*  548 */               str = "text";
/*      */             } 
/*      */           } 
/*  551 */           str5 = str5 + "add " + this.fieldname + "  " + str;
/*      */         }
/*  553 */         else if (this.type == 226 || this.type == 227 || this.type == 256 || this.type == 257) {
/*  554 */           boolean bool = (new RecordSet()).getDBType().equals("oracle");
/*  555 */           String str = "";
/*  556 */           if (this.type == 226) {
/*  557 */             str = "varchar(1000)";
/*  558 */             if (bool) {
/*  559 */               str = "varchar2(1000)";
/*      */             }
/*      */           } else {
/*  562 */             str = "varchar(4000)";
/*  563 */             if (bool) {
/*  564 */               str = "varchar2(4000)";
/*  565 */             } else if ((new RecordSet()).getDBType().equals("mysql")) {
/*  566 */               str = "text";
/*      */             } 
/*      */           } 
/*  569 */           str5 = str5 + "add " + this.fieldname + "  " + str;
/*      */         }
/*  571 */         else if (this.type == 17) {
/*  572 */           boolean bool1 = (new RecordSet()).getDBType().equals("oracle");
/*  573 */           boolean bool2 = (new RecordSet()).getDBType().equals("db2");
/*  574 */           if (bool1) {
/*  575 */             str5 = str5 + "add " + this.fieldname + " clob";
/*  576 */           } else if (bool2) {
/*  577 */             str5 = str5 + "add " + this.fieldname + "  varchar(2000)";
/*      */           } else {
/*  579 */             str5 = str5 + "add " + this.fieldname + "  text";
/*      */           } 
/*      */         } else {
/*      */           
/*  583 */           str5 = str5 + "add " + this.fieldname + " " + this.fielddbtype;
/*      */         } 
/*  585 */         writeLog("cxb==================FieldManager.java========alert_table_add = " + str5);
/*  586 */         connStatement.setStatementSql(str5);
/*  587 */         connStatement.executeUpdate();
/*  588 */         writeLog("cxb==================FieldManager.java========alert_table_add finish ");
/*      */         
/*  590 */         connStatement.setStatementSql(str2);
/*  591 */         connStatement.setString(1, this.fieldname);
/*  592 */         connStatement.setString(2, this.fielddbtype);
/*  593 */         connStatement.setString(3, this.fieldhtmltype);
/*  594 */         connStatement.setInt(4, this.type);
/*  595 */         connStatement.setInt(5, i);
/*  596 */         connStatement.setInt(6, this.subCompanyId2);
/*  597 */         connStatement.setString(7, this.description);
/*  598 */         connStatement.setInt(8, this.textheight);
/*  599 */         connStatement.setString(9, this.textheight_2);
/*  600 */         connStatement.setInt(10, this.childfieldid);
/*  601 */         connStatement.setInt(11, this.imgwidth);
/*  602 */         connStatement.setInt(12, this.imgheight);
/*  603 */         connStatement.setString(13, this.qfwws);
/*  604 */         connStatement.setString(14, this.locatetype);
/*  605 */         connStatement.setString(15, this.istemplate);
/*      */         
/*  607 */         connStatement.setInt(16, Util.getIntValue(this.fieldshowtypes, 1));
/*  608 */         connStatement.executeUpdate();
/*      */       
/*      */       }
/*  611 */       else if (this.action.equalsIgnoreCase("editfield"))
/*      */       {
/*  613 */         boolean bool = fieldNameExist(getFieldid(), getFieldname(), false);
/*  614 */         if (bool) {
/*  615 */           return "1";
/*      */         }
/*  617 */         String str7 = "select * from workflow_formdict where id=?";
/*      */         
/*  619 */         connStatement.setStatementSql(str7);
/*  620 */         connStatement.setInt(1, this.fieldid);
/*  621 */         connStatement.executeQuery();
/*  622 */         String str8 = "";
/*  623 */         String str9 = "";
/*  624 */         if (connStatement.next()) {
/*      */           
/*  626 */           str8 = connStatement.getString("fieldname");
/*  627 */           str9 = connStatement.getString("fielddbtype");
/*      */         } 
/*      */ 
/*      */ 
/*      */         
/*  632 */         if (!str9.equalsIgnoreCase(this.fielddbtype) || !str8.equalsIgnoreCase(this.fieldname)) {
/*      */ 
/*      */           
/*  635 */           RecordSet recordSet = new RecordSet();
/*  636 */           boolean bool1 = recordSet.getDBType().equals("oracle");
/*      */           
/*  638 */           if (bool1) {
/*  639 */             if (!str9.equalsIgnoreCase(this.fielddbtype))
/*      */             {
/*  641 */               if (str9.toLowerCase().indexOf("varchar") > -1 && this.fielddbtype.toLowerCase().indexOf("varchar") > -1) {
/*  642 */                 String str = " alter table workflow_form modify " + str8 + " " + this.fielddbtype;
/*  643 */                 if (this.type == 161 || this.type == 162) {
/*  644 */                   str = " alter table workflow_form modify " + str8 + "  varchar2(200)";
/*      */                 }
/*  646 */                 if (this.type == 224 || this.type == 225) {
/*  647 */                   str = " alter table workflow_form modify " + str8 + "  varchar2(1000)";
/*  648 */                   if (this.type == 225) {
/*  649 */                     str = " alter table workflow_form modify " + str8 + "  varchar2(4000)";
/*      */                   }
/*      */                 } 
/*      */                 
/*  653 */                 if (this.type == 226 || this.type == 227 || this.type == 256 || this.type == 257) {
/*  654 */                   str = " alter table workflow_form modify " + str8 + "  varchar2(1000)";
/*  655 */                   if (this.type == 227 || this.type == 257) {
/*  656 */                     str = " alter table workflow_form modify " + str8 + "  varchar2(4000)";
/*      */                   }
/*      */                 } 
/*      */                 
/*  660 */                 if (!recordSet.executeSql(str)) {
/*  661 */                   throw new Exception(str1);
/*      */                 }
/*      */               } else {
/*  664 */                 String str10 = " alter table workflow_form drop column  " + str8;
/*  665 */                 String str11 = " alter table workflow_form add " + str8 + " " + this.fielddbtype;
/*  666 */                 if (this.type == 161 || this.type == 162) {
/*  667 */                   str11 = " alter table workflow_form add " + str8 + " varchar2(200)";
/*      */                 }
/*  669 */                 if (this.type == 224 || this.type == 225) {
/*  670 */                   str11 = " alter table workflow_form add " + str8 + " varchar2(1000)";
/*  671 */                   if (this.type == 225) {
/*  672 */                     str11 = " alter table workflow_form add " + str8 + " varchar2(4000)";
/*      */                   }
/*      */                 } 
/*      */                 
/*  676 */                 if (this.type == 226 || this.type == 227 || this.type == 256 || this.type == 257) {
/*  677 */                   str11 = " alter table workflow_form add " + str8 + " varchar2(1000)";
/*  678 */                   if (this.type == 227 || this.type == 257) {
/*  679 */                     str11 = " alter table workflow_form add " + str8 + " varchar2(4000)";
/*      */                   }
/*      */                 } 
/*      */                 
/*  683 */                 if (!recordSet.executeSql(str10)) {
/*  684 */                   throw new Exception("modify error");
/*      */                 }
/*  686 */                 if (!recordSet.executeSql(str11)) {
/*      */                   try {
/*  688 */                     recordSet.execute("delete from workflow_formdict where id=" + this.fieldid);
/*  689 */                   } catch (Exception exception) {}
/*  690 */                   throw new Exception(str1);
/*      */                 } 
/*      */               } 
/*      */             }
/*      */             
/*  695 */             if (!str8.equalsIgnoreCase(this.fieldname)) {
/*      */               
/*  697 */               String str = " alter table workflow_form add " + this.fieldname + " " + this.fielddbtype;
/*  698 */               if (this.type == 161 || this.type == 162)
/*  699 */                 str = " alter table workflow_form add " + this.fieldname + "  varchar2(200)"; 
/*  700 */               if (this.type == 224 || this.type == 225) {
/*  701 */                 str = " alter table workflow_form add " + this.fieldname + "  varchar2(1000)";
/*  702 */                 if (this.type == 225) {
/*  703 */                   str = " alter table workflow_form add " + this.fieldname + "  varchar2(4000)";
/*      */                 }
/*      */               } 
/*      */               
/*  707 */               if (this.type == 226 || this.type == 227 || this.type == 256 || this.type == 257) {
/*  708 */                 str = " alter table workflow_form add " + this.fieldname + "  varchar2(1000)";
/*  709 */                 if (this.type == 227 || this.type == 257) {
/*  710 */                   str = " alter table workflow_form add " + this.fieldname + "  varchar2(4000)";
/*      */                 }
/*      */               } 
/*      */               
/*  714 */               if (!recordSet.executeSql(str))
/*      */               {
/*  716 */                 throw new Exception("modify error");
/*      */               }
/*  718 */               str = " update workflow_form set " + this.fieldname + "=" + str8;
/*  719 */               if (!recordSet.executeSql(str))
/*      */               {
/*  721 */                 throw new Exception("modify error");
/*      */               }
/*  723 */               str = " alter table workflow_form drop column  " + str8;
/*  724 */               if (!recordSet.executeSql(str))
/*      */               {
/*  726 */                 throw new Exception("modify error");
/*      */               }
/*      */             } 
/*  729 */           } else if (recordSet.getDBType().equals("mysql")) {
/*  730 */             if (!str9.equalsIgnoreCase(this.fielddbtype))
/*      */             {
/*  732 */               if (str9.toLowerCase().indexOf("varchar") > -1 && this.fielddbtype.toLowerCase().indexOf("varchar") > -1) {
/*  733 */                 String str = " alter table workflow_form modify column " + str8 + " " + this.fielddbtype;
/*  734 */                 if (this.type == 161 || this.type == 162) {
/*  735 */                   str = " alter table workflow_form modify column " + str8 + "  varchar(200)";
/*      */                 }
/*  737 */                 if (this.type == 224 || this.type == 225) {
/*  738 */                   str = " alter table workflow_form modify column " + str8 + "  varchar(1000)";
/*  739 */                   if (this.type == 225) {
/*  740 */                     str = " alter table workflow_form modify column " + str8 + "  text ";
/*      */                   }
/*      */                 } 
/*      */                 
/*  744 */                 if (this.type == 226 || this.type == 227 || this.type == 256 || this.type == 257) {
/*  745 */                   str = " alter table workflow_form modify column " + str8 + "  varchar(1000)";
/*  746 */                   if (this.type == 227 || this.type == 257) {
/*  747 */                     str = " alter table workflow_form modify column " + str8 + "  text";
/*      */                   }
/*      */                 } 
/*      */                 
/*  751 */                 if (!recordSet.executeSql(str)) {
/*  752 */                   throw new Exception(str1);
/*      */                 }
/*      */               } else {
/*  755 */                 String str10 = " alter table workflow_form drop column  " + str8;
/*  756 */                 String str11 = " alter table workflow_form add " + str8 + " " + this.fielddbtype;
/*  757 */                 if (this.type == 161 || this.type == 162) {
/*  758 */                   str11 = " alter table workflow_form add " + str8 + " varchar(200)";
/*      */                 }
/*  760 */                 if (this.type == 224 || this.type == 225) {
/*  761 */                   str11 = " alter table workflow_form add " + str8 + " varchar(1000)";
/*  762 */                   if (this.type == 225) {
/*  763 */                     str11 = " alter table workflow_form add " + str8 + " text";
/*      */                   }
/*      */                 } 
/*      */                 
/*  767 */                 if (this.type == 226 || this.type == 227 || this.type == 256 || this.type == 257) {
/*  768 */                   str11 = " alter table workflow_form add " + str8 + " varchar(1000)";
/*  769 */                   if (this.type == 227 || this.type == 257) {
/*  770 */                     str11 = " alter table workflow_form add " + str8 + " text";
/*      */                   }
/*      */                 } 
/*      */                 
/*  774 */                 if (!recordSet.executeSql(str10)) {
/*  775 */                   throw new Exception("modify error");
/*      */                 }
/*  777 */                 if (!recordSet.executeSql(str11)) {
/*      */                   try {
/*  779 */                     recordSet.execute("delete from workflow_formdict where id=" + this.fieldid);
/*  780 */                   } catch (Exception exception) {}
/*  781 */                   throw new Exception(str1);
/*      */                 } 
/*      */               } 
/*      */             }
/*      */             
/*  786 */             if (!str8.equalsIgnoreCase(this.fieldname))
/*      */             {
/*  788 */               String str = " alter table workflow_form add " + this.fieldname + " " + this.fielddbtype;
/*  789 */               if (this.type == 161 || this.type == 162)
/*  790 */                 str = " alter table workflow_form add " + this.fieldname + "  varchar(200)"; 
/*  791 */               if (this.type == 224 || this.type == 225) {
/*  792 */                 str = " alter table workflow_form add " + this.fieldname + "  varchar(1000)";
/*  793 */                 if (this.type == 225) {
/*  794 */                   str = " alter table workflow_form add " + this.fieldname + "  text";
/*      */                 }
/*      */               } 
/*      */               
/*  798 */               if (this.type == 226 || this.type == 227 || this.type == 256 || this.type == 257) {
/*  799 */                 str = " alter table workflow_form add " + this.fieldname + "  varchar(1000)";
/*  800 */                 if (this.type == 227 || this.type == 257) {
/*  801 */                   str = " alter table workflow_form add " + this.fieldname + "  text";
/*      */                 }
/*      */               } 
/*      */               
/*  805 */               if (!recordSet.executeSql(str))
/*      */               {
/*  807 */                 throw new Exception("modify error");
/*      */               }
/*  809 */               str = " update workflow_form set " + this.fieldname + "=" + str8;
/*  810 */               if (!recordSet.executeSql(str))
/*      */               {
/*  812 */                 throw new Exception("modify error");
/*      */               }
/*  814 */               str = " alter table workflow_form drop column  " + str8;
/*  815 */               if (!recordSet.executeSql(str))
/*      */               {
/*  817 */                 throw new Exception("modify error");
/*      */               }
/*      */             }
/*      */           
/*  821 */           } else if (recordSet.getDBType().equals("postgresql")) {
/*      */             
/*  823 */             if (!str9.equalsIgnoreCase(this.fielddbtype))
/*      */             {
/*      */               
/*  826 */               if (str9.toLowerCase().indexOf("varchar") > -1 && this.fielddbtype.toLowerCase().indexOf("varchar") > -1) {
/*  827 */                 String str = " alter table workflow_form alter column  " + str8 + " type " + this.fielddbtype;
/*  828 */                 if (this.type == 161 || this.type == 162) {
/*  829 */                   str = " alter table workflow_form alter column  " + str8 + " type  varchar(200)";
/*      */                 }
/*  831 */                 if (this.type == 224 || this.type == 225) {
/*  832 */                   str = " alter table workflow_form alter column  " + str8 + "  type varchar(1000)";
/*  833 */                   if (this.type == 225) {
/*  834 */                     str = " alter table workflow_form alter column  " + str8 + " type  varchar(4000)";
/*      */                   }
/*      */                 } 
/*  837 */                 if (this.type == 226 || this.type == 227 || this.type == 256 || this.type == 257) {
/*  838 */                   str = " alter table workflow_form alter column  " + str8 + " type  varchar(1000)";
/*  839 */                   if (this.type == 227 || this.type == 257) {
/*  840 */                     str = " alter table workflow_form alter column  " + str8 + " type  varchar(4000)";
/*      */                   }
/*      */                 } 
/*  843 */                 if (!recordSet.executeSql(str)) {
/*  844 */                   throw new Exception(str1);
/*      */                 }
/*      */               } else {
/*  847 */                 String str10 = " alter table workflow_form drop column  " + str8;
/*  848 */                 String str11 = " alter table workflow_form add " + str8 + " " + this.fielddbtype;
/*  849 */                 if (this.type == 161 || this.type == 162) {
/*  850 */                   str11 = " alter table workflow_form add " + str8 + " varchar(200)";
/*      */                 }
/*  852 */                 if (this.type == 224 || this.type == 225) {
/*  853 */                   str11 = " alter table workflow_form add " + str8 + " varchar(1000)";
/*  854 */                   if (this.type == 225) {
/*  855 */                     str11 = " alter table workflow_form add " + str8 + " varchar(4000)";
/*      */                   }
/*      */                 } 
/*      */                 
/*  859 */                 if (this.type == 226 || this.type == 227 || this.type == 256 || this.type == 257) {
/*  860 */                   str11 = " alter table workflow_form add " + str8 + " varchar(1000)";
/*  861 */                   if (this.type == 227 || this.type == 257) {
/*  862 */                     str11 = " alter table workflow_form add " + str8 + " varchar(4000)";
/*      */                   }
/*      */                 } 
/*      */                 
/*  866 */                 if (!recordSet.executeSql(str10)) {
/*  867 */                   throw new Exception("modify error");
/*      */                 }
/*  869 */                 if (!recordSet.executeSql(str11)) {
/*      */                   try {
/*  871 */                     recordSet.execute("delete from workflow_formdict where id=" + this.fieldid);
/*  872 */                   } catch (Exception exception) {}
/*  873 */                   throw new Exception(str1);
/*      */                 } 
/*      */               } 
/*      */             }
/*  877 */             if (!str8.equalsIgnoreCase(this.fieldname))
/*      */             {
/*  879 */               String str = " alter table workflow_form add " + this.fieldname + " " + this.fielddbtype;
/*  880 */               if (this.type == 161 || this.type == 162)
/*  881 */                 str = " alter table workflow_form add " + this.fieldname + "  varchar(200)"; 
/*  882 */               if (this.type == 224 || this.type == 225) {
/*  883 */                 str = " alter table workflow_form add " + this.fieldname + "  varchar(1000)";
/*  884 */                 if (this.type == 225) {
/*  885 */                   str = " alter table workflow_form add " + this.fieldname + "  text";
/*      */                 }
/*      */               } 
/*      */               
/*  889 */               if (this.type == 226 || this.type == 227 || this.type == 256 || this.type == 257) {
/*  890 */                 str = " alter table workflow_form add " + this.fieldname + "  varchar(1000)";
/*  891 */                 if (this.type == 227 || this.type == 257) {
/*  892 */                   str = " alter table workflow_form add " + this.fieldname + "  text";
/*      */                 }
/*      */               } 
/*      */               
/*  896 */               if (!recordSet.executeSql(str))
/*      */               {
/*  898 */                 throw new Exception("modify error");
/*      */               }
/*  900 */               str = " update workflow_form set " + this.fieldname + "=" + str8;
/*  901 */               if (!recordSet.executeSql(str))
/*      */               {
/*  903 */                 throw new Exception("modify error");
/*      */               }
/*  905 */               str = " alter table workflow_form drop column  " + str8;
/*  906 */               if (!recordSet.executeSql(str))
/*      */               {
/*  908 */                 throw new Exception("modify error");
/*      */               }
/*      */             }
/*      */           
/*      */           } else {
/*      */             
/*  914 */             if (!str9.equalsIgnoreCase(this.fielddbtype))
/*      */             {
/*      */               
/*  917 */               if (str9.toLowerCase().indexOf("varchar") > -1 && this.fielddbtype.toLowerCase().indexOf("varchar") > -1) {
/*  918 */                 String str = " alter table workflow_form alter column  " + str8 + " " + this.fielddbtype;
/*  919 */                 if (this.type == 161 || this.type == 162) {
/*  920 */                   str = " alter table workflow_form alter column  " + str8 + "  varchar(200)";
/*      */                 }
/*  922 */                 if (this.type == 224 || this.type == 225) {
/*  923 */                   str = " alter table workflow_form alter column  " + str8 + "  varchar(1000)";
/*  924 */                   if (this.type == 225) {
/*  925 */                     str = " alter table workflow_form alter column  " + str8 + "  varchar(4000)";
/*      */                   }
/*      */                 } 
/*  928 */                 if (this.type == 226 || this.type == 227 || this.type == 256 || this.type == 257) {
/*  929 */                   str = " alter table workflow_form alter column  " + str8 + "  varchar(1000)";
/*  930 */                   if (this.type == 227 || this.type == 257) {
/*  931 */                     str = " alter table workflow_form alter column  " + str8 + "  varchar(4000)";
/*      */                   }
/*      */                 } 
/*  934 */                 if (!recordSet.executeSql(str)) {
/*  935 */                   throw new Exception(str1);
/*      */                 }
/*      */               } else {
/*  938 */                 String str10 = " alter table workflow_form drop column  " + str8;
/*  939 */                 String str11 = " alter table workflow_form add " + str8 + " " + this.fielddbtype;
/*  940 */                 if (this.type == 161 || this.type == 162) {
/*  941 */                   str11 = " alter table workflow_form add " + str8 + " varchar(200)";
/*      */                 }
/*  943 */                 if (this.type == 224 || this.type == 225) {
/*  944 */                   str11 = " alter table workflow_form add " + str8 + " varchar(1000)";
/*  945 */                   if (this.type == 225) {
/*  946 */                     str11 = " alter table workflow_form add " + str8 + " varchar(4000)";
/*      */                   }
/*      */                 } 
/*      */                 
/*  950 */                 if (this.type == 226 || this.type == 227 || this.type == 256 || this.type == 257) {
/*  951 */                   str11 = " alter table workflow_form add " + str8 + " varchar(1000)";
/*  952 */                   if (this.type == 227 || this.type == 257) {
/*  953 */                     str11 = " alter table workflow_form add " + str8 + " varchar(4000)";
/*      */                   }
/*      */                 } 
/*      */                 
/*  957 */                 if (!recordSet.executeSql(str10)) {
/*  958 */                   throw new Exception("modify error");
/*      */                 }
/*  960 */                 if (!recordSet.executeSql(str11)) {
/*      */                   try {
/*  962 */                     recordSet.execute("delete from workflow_formdict where id=" + this.fieldid);
/*  963 */                   } catch (Exception exception) {}
/*  964 */                   throw new Exception(str1);
/*      */                 } 
/*      */               } 
/*      */             }
/*  968 */             if (!str8.equalsIgnoreCase(this.fieldname)) {
/*  969 */               String str = "exec sp_rename 'workflow_form." + str8 + "', '" + this.fieldname + "', 'column' ";
/*  970 */               if (!recordSet.executeSql(str)) {
/*  971 */                 throw new Exception("modify error");
/*      */               }
/*      */             } 
/*      */           } 
/*      */         } 
/*      */         
/*  977 */         connStatement.setStatementSql(str3);
/*  978 */         connStatement.setString(1, this.fieldname);
/*  979 */         connStatement.setString(2, this.fielddbtype);
/*  980 */         connStatement.setString(3, this.fieldhtmltype);
/*  981 */         connStatement.setInt(4, this.type);
/*  982 */         connStatement.setInt(5, this.subCompanyId2);
/*  983 */         connStatement.setString(6, this.description);
/*  984 */         connStatement.setInt(7, this.textheight);
/*  985 */         connStatement.setString(8, this.textheight_2);
/*  986 */         connStatement.setInt(9, this.childfieldid);
/*  987 */         connStatement.setInt(10, this.imgwidth);
/*  988 */         connStatement.setInt(11, this.imgheight);
/*  989 */         connStatement.setString(12, this.qfwws);
/*  990 */         connStatement.setString(13, this.locatetype);
/*  991 */         connStatement.setString(14, this.istemplate);
/*  992 */         connStatement.setInt(15, Util.getIntValue(this.fieldshowtypes, 1));
/*  993 */         connStatement.setInt(16, this.fieldid);
/*  994 */         connStatement.executeUpdate();
/*      */       }
/*      */     
/*      */     }
/*  998 */     catch (Exception exception) {
/*      */       
/* 1000 */       str6 = "2";
/* 1001 */       writeLog(exception);
/*      */     } finally {
/*      */ 
/*      */       
/*      */       try {
/*      */         
/* 1007 */         connStatement.close();
/*      */       }
/* 1009 */       catch (Exception exception) {}
/*      */     } 
/*      */ 
/*      */     
/* 1013 */     return str6;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String setDetailFieldInfo() throws Exception {
/* 1023 */     String str1 = SystemEnv.getHtmlLabelName(19429, this.languageid);
/* 1024 */     String str2 = "insert into workflow_formdictdetail(fieldname,fielddbtype,fieldhtmltype,type,id,subcompanyid,description,textheight,textheight_2,childfieldid,qfws,imgwidth,imgheight,istemplate,fieldshowtypes) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
/* 1025 */     String str3 = "update workflow_formdictdetail set fieldname=?,fielddbtype=?,fieldhtmltype=?,type=?,subcompanyid=?,description=?,textheight=?,textheight_2=?,childfieldid=?,qfws=?,imgwidth=?,imgheight=?,istemplate=?,fieldshowtypes=? where id=?";
/* 1026 */     String str4 = "select id from workflow_formdictdetail where Upper(fieldname)=?";
/*      */     
/* 1028 */     String str5 = "alter table workflow_formdetail ";
/* 1029 */     ConnStatement connStatement = null;
/*      */     
/* 1031 */     String str6 = "";
/*      */ 
/*      */     
/*      */     try {
/* 1035 */       connStatement = new ConnStatement();
/* 1036 */       if (this.action.equalsIgnoreCase("addfield"))
/*      */       {
/*      */         
/* 1039 */         if (this.type == 161 || this.type == 162) {
/* 1040 */           boolean bool = (new RecordSet()).getDBType().equals("oracle");
/* 1041 */           if (bool) {
/* 1042 */             str5 = str5 + "add " + this.fieldname + "  varchar2(200)";
/*      */           } else {
/* 1044 */             str5 = str5 + "add " + this.fieldname + "  varchar(200)";
/*      */           } 
/* 1046 */         } else if (this.type == 224 || this.type == 225) {
/* 1047 */           boolean bool = (new RecordSet()).getDBType().equals("oracle");
/* 1048 */           String str = "varchar(1000)";
/* 1049 */           if (this.type == 225) {
/* 1050 */             str = "varchar(4000)";
/*      */           }
/* 1052 */           if (bool) {
/* 1053 */             str = "varchar2(1000)";
/* 1054 */             if (this.type == 225) {
/* 1055 */               str = "varchar2(4000)";
/*      */             }
/* 1057 */           } else if ((new RecordSet()).getDBType().equals("mysql")) {
/* 1058 */             str = "varchar(1000)";
/* 1059 */             if (this.type == 225) {
/* 1060 */               str = "text";
/*      */             }
/*      */           } 
/* 1063 */           str5 = str5 + "add " + this.fieldname + "  " + str;
/*      */         }
/* 1065 */         else if (this.type == 226 || this.type == 227 || this.type == 256 || this.type == 257) {
/* 1066 */           boolean bool = (new RecordSet()).getDBType().equals("oracle");
/* 1067 */           String str = "varchar(1000)";
/* 1068 */           if (this.type == 227 || this.type == 257) {
/* 1069 */             str = "varchar(4000)";
/*      */           }
/* 1071 */           if (bool) {
/* 1072 */             str = "varchar2(1000)";
/* 1073 */             if (this.type == 227 || this.type == 257) {
/* 1074 */               str = "varchar2(4000)";
/*      */             }
/* 1076 */           } else if ((new RecordSet()).getDBType().equals("mysql")) {
/* 1077 */             str = "varchar(1000)";
/* 1078 */             if (this.type == 227 || this.type == 257) {
/* 1079 */               str = "text";
/*      */             }
/*      */           } 
/* 1082 */           str5 = str5 + "add " + this.fieldname + "  " + str;
/* 1083 */         } else if (this.type == 17) {
/* 1084 */           boolean bool1 = (new RecordSet()).getDBType().equals("oracle");
/* 1085 */           boolean bool2 = (new RecordSet()).getDBType().equals("db2");
/* 1086 */           if (bool1) {
/* 1087 */             str5 = str5 + "add " + this.fieldname + " clob";
/* 1088 */           } else if (bool2) {
/* 1089 */             str5 = str5 + "add " + this.fieldname + "  varchar(2000)";
/*      */           } else {
/* 1091 */             str5 = str5 + "add " + this.fieldname + "  text";
/*      */           } 
/*      */         } else {
/*      */           
/* 1095 */           str5 = str5 + "add " + this.fieldname + " " + this.fielddbtype;
/* 1096 */         }  writeLog(str5);
/* 1097 */         connStatement.setStatementSql(str5);
/* 1098 */         connStatement.executeUpdate();
/*      */         
/* 1100 */         FormDictIdUpdate formDictIdUpdate = new FormDictIdUpdate();
/* 1101 */         int i = formDictIdUpdate.getFormDictNewId();
/*      */         
/* 1103 */         connStatement.setStatementSql(str4);
/* 1104 */         connStatement.setString(1, this.fieldname.toUpperCase());
/* 1105 */         connStatement.executeQuery();
/* 1106 */         if (connStatement.next()) return "1";
/*      */         
/* 1108 */         connStatement.setStatementSql(str2);
/* 1109 */         connStatement.setString(1, this.fieldname);
/* 1110 */         connStatement.setString(2, this.fielddbtype);
/* 1111 */         connStatement.setString(3, this.fieldhtmltype);
/* 1112 */         connStatement.setInt(4, this.type);
/* 1113 */         connStatement.setInt(5, i);
/* 1114 */         connStatement.setInt(6, this.subCompanyId2);
/* 1115 */         connStatement.setString(7, this.description);
/* 1116 */         connStatement.setInt(8, this.textheight);
/* 1117 */         connStatement.setString(9, this.textheight_2);
/* 1118 */         connStatement.setInt(10, this.childfieldid);
/* 1119 */         connStatement.setString(11, this.qfwws);
/* 1120 */         connStatement.setInt(12, this.imgwidth);
/* 1121 */         connStatement.setInt(13, this.imgheight);
/* 1122 */         connStatement.setString(14, this.istemplate);
/* 1123 */         connStatement.setInt(15, Util.getIntValue(this.fieldshowtypes, 1));
/* 1124 */         connStatement.executeUpdate();
/*      */       }
/* 1126 */       else if (this.action.equalsIgnoreCase("editfield"))
/*      */       {
/* 1128 */         boolean bool = fieldNameExist(getFieldid(), getFieldname(), true);
/* 1129 */         if (bool) {
/* 1130 */           return "1";
/*      */         }
/*      */         
/* 1133 */         String str7 = "select * from workflow_formdictdetail where id=?";
/*      */         
/* 1135 */         connStatement.setStatementSql(str7);
/* 1136 */         connStatement.setInt(1, this.fieldid);
/* 1137 */         connStatement.executeQuery();
/* 1138 */         String str8 = "";
/* 1139 */         String str9 = "";
/* 1140 */         if (connStatement.next()) {
/*      */           
/* 1142 */           str8 = connStatement.getString("fieldname");
/* 1143 */           str9 = connStatement.getString("fielddbtype");
/*      */         } 
/*      */ 
/*      */ 
/*      */         
/* 1148 */         if (!str9.equalsIgnoreCase(this.fielddbtype) || !str8.equalsIgnoreCase(this.fieldname)) {
/*      */ 
/*      */           
/* 1151 */           RecordSet recordSet = new RecordSet();
/* 1152 */           boolean bool1 = recordSet.getDBType().equals("oracle");
/*      */           
/* 1154 */           if (bool1) {
/*      */             
/* 1156 */             if (!str9.equalsIgnoreCase(this.fielddbtype))
/*      */             {
/*      */               
/* 1159 */               if (str9.toLowerCase().indexOf("varchar") > -1 && this.fielddbtype.toLowerCase().indexOf("varchar") > -1) {
/* 1160 */                 String str = " alter table workflow_formdetail modify " + str8 + " " + this.fielddbtype;
/* 1161 */                 if (this.type == 161 || this.type == 162) {
/* 1162 */                   str = " alter table workflow_formdetail modify " + str8 + "  varchar2(200)";
/*      */                 }
/* 1164 */                 if (this.type == 224 || this.type == 225) {
/* 1165 */                   str = " alter table workflow_formdetail modify " + str8 + "  varchar2(1000)";
/* 1166 */                   if (this.type == 225) {
/* 1167 */                     str = " alter table workflow_formdetail modify " + str8 + "  varchar2(4000)";
/*      */                   }
/*      */                 } 
/* 1170 */                 if (this.type == 226 || this.type == 227 || this.type == 256 || this.type == 257) {
/* 1171 */                   str = " alter table workflow_formdetail modify " + str8 + "  varchar2(1000)";
/* 1172 */                   if (this.type == 227 || this.type == 257) {
/* 1173 */                     str = " alter table workflow_formdetail modify " + str8 + "  varchar2(4000)";
/*      */                   }
/*      */                 } 
/* 1176 */                 if (!recordSet.executeSql(str)) {
/* 1177 */                   throw new Exception(str1);
/*      */                 }
/*      */               } else {
/* 1180 */                 String str10 = " alter table workflow_formdetail drop column  " + str8;
/* 1181 */                 String str11 = " alter table workflow_formdetail add " + str8 + " " + this.fielddbtype;
/* 1182 */                 if (this.type == 161 || this.type == 162) {
/* 1183 */                   str11 = " alter table workflow_formdetail add " + str8 + " varchar2(200)";
/*      */                 }
/* 1185 */                 if (this.type == 224 || this.type == 225) {
/* 1186 */                   str11 = " alter table workflow_formdetail add " + str8 + " varchar2(1000)";
/* 1187 */                   if (this.type == 225) {
/* 1188 */                     str11 = " alter table workflow_formdetail add " + str8 + " varchar2(4000)";
/*      */                   }
/*      */                 } 
/*      */                 
/* 1192 */                 if (this.type == 226 || this.type == 227 || this.type == 256 || this.type == 257) {
/* 1193 */                   str11 = " alter table workflow_formdetail add " + str8 + " varchar2(1000)";
/* 1194 */                   if (this.type == 227 || this.type == 257) {
/* 1195 */                     str11 = " alter table workflow_formdetail add " + str8 + " varchar2(4000)";
/*      */                   }
/*      */                 } 
/*      */                 
/* 1199 */                 if (!recordSet.executeSql(str10)) {
/* 1200 */                   throw new Exception("modify error");
/*      */                 }
/* 1202 */                 if (!recordSet.executeSql(str11)) {
/*      */                   try {
/* 1204 */                     recordSet.execute("delete from workflow_formdictdetail where id=" + this.fieldid);
/* 1205 */                   } catch (Exception exception) {}
/* 1206 */                   throw new Exception(str1);
/*      */                 } 
/*      */               } 
/*      */             }
/*      */             
/* 1211 */             if (!str8.equalsIgnoreCase(this.fieldname)) {
/*      */               
/* 1213 */               String str = " alter table workflow_formdetail add " + this.fieldname + " " + this.fielddbtype;
/* 1214 */               if (this.type == 161 || this.type == 162)
/* 1215 */                 str = " alter table workflow_formdetail add " + this.fieldname + "  varchar2(200)"; 
/* 1216 */               if (this.type == 224 || this.type == 225) {
/* 1217 */                 str = " alter table workflow_formdetail add " + this.fieldname + "  varchar2(1000)";
/* 1218 */                 if (this.type == 225) {
/* 1219 */                   str = " alter table workflow_formdetail add " + this.fieldname + "  varchar2(4000)";
/*      */                 }
/*      */               } 
/*      */               
/* 1223 */               if (this.type == 226 || this.type == 227 || this.type == 256 || this.type == 257) {
/* 1224 */                 str = " alter table workflow_formdetail add " + this.fieldname + "  varchar2(1000)";
/* 1225 */                 if (this.type == 227 || this.type == 257) {
/* 1226 */                   str = " alter table workflow_formdetail add " + this.fieldname + "  varchar2(4000)";
/*      */                 }
/*      */               } 
/*      */               
/* 1230 */               if (!recordSet.executeSql(str))
/*      */               {
/* 1232 */                 throw new Exception("modify error");
/*      */               }
/* 1234 */               str = " update workflow_formdetail set " + this.fieldname + "=" + str8;
/* 1235 */               if (!recordSet.executeSql(str))
/*      */               {
/* 1237 */                 throw new Exception("modify error");
/*      */               }
/* 1239 */               str = " alter table workflow_formdetail drop column  " + str8;
/* 1240 */               if (!recordSet.executeSql(str))
/*      */               {
/* 1242 */                 throw new Exception("modify error");
/*      */               }
/*      */             } 
/* 1245 */           } else if (recordSet.getDBType().equals("mysql")) {
/* 1246 */             if (!str9.equalsIgnoreCase(this.fielddbtype))
/*      */             {
/*      */               
/* 1249 */               if (str9.toLowerCase().indexOf("varchar") > -1 && this.fielddbtype.toLowerCase().indexOf("varchar") > -1) {
/* 1250 */                 String str = " alter table workflow_formdetail modify column " + str8 + " " + this.fielddbtype;
/* 1251 */                 if (this.type == 161 || this.type == 162) {
/* 1252 */                   str = " alter table workflow_formdetail modify column " + str8 + "  varchar(200)";
/*      */                 }
/* 1254 */                 if (this.type == 224 || this.type == 225) {
/* 1255 */                   str = " alter table workflow_formdetail modify column " + str8 + "  varchar(1000)";
/* 1256 */                   if (this.type == 225) {
/* 1257 */                     str = " alter table workflow_formdetail modify column " + str8 + "  text";
/*      */                   }
/*      */                 } 
/* 1260 */                 if (this.type == 226 || this.type == 227 || this.type == 256 || this.type == 257) {
/* 1261 */                   str = " alter table workflow_formdetail modify column " + str8 + "  varchar(1000)";
/* 1262 */                   if (this.type == 227 || this.type == 257) {
/* 1263 */                     str = " alter table workflow_formdetail modify column " + str8 + "  text";
/*      */                   }
/*      */                 } 
/* 1266 */                 if (!recordSet.executeSql(str)) {
/* 1267 */                   throw new Exception(str1);
/*      */                 }
/*      */               } else {
/* 1270 */                 String str10 = " alter table workflow_formdetail drop column  " + str8;
/* 1271 */                 String str11 = " alter table workflow_formdetail add " + str8 + " " + this.fielddbtype;
/* 1272 */                 if (this.type == 161 || this.type == 162) {
/* 1273 */                   str11 = " alter table workflow_formdetail add " + str8 + " varchar(200)";
/*      */                 }
/* 1275 */                 if (this.type == 224 || this.type == 225) {
/* 1276 */                   str11 = " alter table workflow_formdetail add " + str8 + " varchar(1000)";
/* 1277 */                   if (this.type == 225) {
/* 1278 */                     str11 = " alter table workflow_formdetail add " + str8 + " text";
/*      */                   }
/*      */                 } 
/*      */                 
/* 1282 */                 if (this.type == 226 || this.type == 227 || this.type == 256 || this.type == 257) {
/* 1283 */                   str11 = " alter table workflow_formdetail add " + str8 + " varchar(1000)";
/* 1284 */                   if (this.type == 227 || this.type == 257) {
/* 1285 */                     str11 = " alter table workflow_formdetail add " + str8 + " text";
/*      */                   }
/*      */                 } 
/*      */                 
/* 1289 */                 if (!recordSet.executeSql(str10)) {
/* 1290 */                   throw new Exception("modify error");
/*      */                 }
/* 1292 */                 if (!recordSet.executeSql(str11)) {
/*      */                   try {
/* 1294 */                     recordSet.execute("delete from workflow_formdictdetail where id=" + this.fieldid);
/* 1295 */                   } catch (Exception exception) {}
/* 1296 */                   throw new Exception(str1);
/*      */                 } 
/*      */               } 
/*      */             }
/*      */             
/* 1301 */             if (!str8.equalsIgnoreCase(this.fieldname))
/*      */             {
/* 1303 */               String str = " alter table workflow_formdetail add " + this.fieldname + " " + this.fielddbtype;
/* 1304 */               if (this.type == 161 || this.type == 162)
/* 1305 */                 str = " alter table workflow_formdetail add " + this.fieldname + "  varchar(200)"; 
/* 1306 */               if (this.type == 224 || this.type == 225) {
/* 1307 */                 str = " alter table workflow_formdetail add " + this.fieldname + "  varchar(1000)";
/* 1308 */                 if (this.type == 225) {
/* 1309 */                   str = " alter table workflow_formdetail add " + this.fieldname + "  text";
/*      */                 }
/*      */               } 
/*      */               
/* 1313 */               if (this.type == 226 || this.type == 227 || this.type == 256 || this.type == 257) {
/* 1314 */                 str = " alter table workflow_formdetail add " + this.fieldname + "  varchar(1000)";
/* 1315 */                 if (this.type == 227 || this.type == 257) {
/* 1316 */                   str = " alter table workflow_formdetail add " + this.fieldname + "  text";
/*      */                 }
/*      */               } 
/*      */               
/* 1320 */               if (!recordSet.executeSql(str))
/*      */               {
/* 1322 */                 throw new Exception("modify error");
/*      */               }
/* 1324 */               str = " update workflow_formdetail set " + this.fieldname + "=" + str8;
/* 1325 */               if (!recordSet.executeSql(str))
/*      */               {
/* 1327 */                 throw new Exception("modify error");
/*      */               }
/* 1329 */               str = " alter table workflow_formdetail drop column  " + str8;
/* 1330 */               if (!recordSet.executeSql(str))
/*      */               {
/* 1332 */                 throw new Exception("modify error");
/*      */               }
/*      */             }
/*      */           
/*      */           }
/* 1337 */           else if (recordSet.getDBType().equals("postgresql")) {
/*      */             
/* 1339 */             if (!str9.equalsIgnoreCase(this.fielddbtype))
/*      */             {
/*      */ 
/*      */               
/* 1343 */               if (str9.toLowerCase().indexOf("varchar") > -1 && this.fielddbtype.toLowerCase().indexOf("varchar") > -1) {
/* 1344 */                 String str = " alter table workflow_formdetail alter column " + str8 + " type " + this.fielddbtype;
/* 1345 */                 if (this.type == 161 || this.type == 162) {
/* 1346 */                   str = " alter table workflow_formdetail alter column " + str8 + " type varchar(200)";
/*      */                 }
/* 1348 */                 if (this.type == 224 || this.type == 225) {
/* 1349 */                   str = " alter table workflow_formdetail alter column " + str8 + " type  varchar(1000)";
/* 1350 */                   if (this.type == 225) {
/* 1351 */                     str = " alter table workflow_formdetail alter column " + str8 + "  type varchar(4000)";
/*      */                   }
/*      */                 } 
/*      */                 
/* 1355 */                 if (this.type == 226 || this.type == 227 || this.type == 256 || this.type == 257) {
/* 1356 */                   str = " alter table workflow_formdetail alter column " + str8 + "  type varchar(1000)";
/* 1357 */                   if (this.type == 227 || this.type == 257) {
/* 1358 */                     str = " alter table workflow_formdetail alter column " + str8 + " type  varchar(4000)";
/*      */                   }
/*      */                 } 
/*      */                 
/* 1362 */                 if (!recordSet.executeSql(str)) {
/* 1363 */                   throw new Exception(str1);
/*      */                 }
/*      */               } else {
/* 1366 */                 String str10 = " alter table workflow_formdetail drop column  " + str8;
/* 1367 */                 String str11 = " alter table workflow_formdetail add " + str8 + " " + this.fielddbtype;
/* 1368 */                 if (this.type == 161 || this.type == 162) {
/* 1369 */                   str11 = " alter table workflow_formdetail add " + str8 + " varchar(200)";
/*      */                 }
/* 1371 */                 if (this.type == 224 || this.type == 225) {
/* 1372 */                   str11 = " alter table workflow_formdetail add " + str8 + " varchar(1000)";
/* 1373 */                   if (this.type == 225) {
/* 1374 */                     str11 = " alter table workflow_formdetail add " + str8 + " varchar(4000)";
/*      */                   }
/*      */                 } 
/* 1377 */                 if (this.type == 226 || this.type == 227 || this.type == 256 || this.type == 257) {
/* 1378 */                   str11 = " alter table workflow_formdetail add " + str8 + " varchar(1000)";
/* 1379 */                   if (this.type == 227 || this.type == 257) {
/* 1380 */                     str11 = " alter table workflow_formdetail add " + str8 + " varchar(4000)";
/*      */                   }
/*      */                 } 
/*      */ 
/*      */                 
/* 1385 */                 if (!recordSet.executeSql(str10)) {
/* 1386 */                   throw new Exception("modify error");
/*      */                 }
/* 1388 */                 if (!recordSet.executeSql(str11)) {
/*      */                   try {
/* 1390 */                     recordSet.execute("delete from workflow_formdictdetail where id=" + this.fieldid);
/* 1391 */                   } catch (Exception exception) {}
/* 1392 */                   throw new Exception(str1);
/*      */                 } 
/*      */               } 
/*      */             }
/*      */             
/* 1397 */             if (!str8.equalsIgnoreCase(this.fieldname))
/*      */             {
/* 1399 */               String str = " alter table workflow_formdetail add " + this.fieldname + " " + this.fielddbtype;
/* 1400 */               if (this.type == 161 || this.type == 162)
/* 1401 */                 str = " alter table workflow_formdetail add " + this.fieldname + "  varchar(200)"; 
/* 1402 */               if (this.type == 224 || this.type == 225) {
/* 1403 */                 str = " alter table workflow_formdetail add " + this.fieldname + "  varchar(1000)";
/* 1404 */                 if (this.type == 225) {
/* 1405 */                   str = " alter table workflow_formdetail add " + this.fieldname + "  text";
/*      */                 }
/*      */               } 
/*      */               
/* 1409 */               if (this.type == 226 || this.type == 227 || this.type == 256 || this.type == 257) {
/* 1410 */                 str = " alter table workflow_formdetail add " + this.fieldname + "  varchar(1000)";
/* 1411 */                 if (this.type == 227 || this.type == 257) {
/* 1412 */                   str = " alter table workflow_formdetail add " + this.fieldname + "  text";
/*      */                 }
/*      */               } 
/*      */               
/* 1416 */               if (!recordSet.executeSql(str))
/*      */               {
/* 1418 */                 throw new Exception("modify error");
/*      */               }
/* 1420 */               str = " update workflow_formdetail set " + this.fieldname + "=" + str8;
/* 1421 */               if (!recordSet.executeSql(str))
/*      */               {
/* 1423 */                 throw new Exception("modify error");
/*      */               }
/* 1425 */               str = " alter table workflow_formdetail drop column  " + str8;
/* 1426 */               if (!recordSet.executeSql(str))
/*      */               {
/* 1428 */                 throw new Exception("modify error");
/*      */               }
/*      */             }
/*      */           
/*      */           } else {
/*      */             
/* 1434 */             if (!str9.equalsIgnoreCase(this.fielddbtype))
/*      */             {
/*      */ 
/*      */               
/* 1438 */               if (str9.toLowerCase().indexOf("varchar") > -1 && this.fielddbtype.toLowerCase().indexOf("varchar") > -1) {
/* 1439 */                 String str = " alter table workflow_formdetail alter column " + str8 + " " + this.fielddbtype;
/* 1440 */                 if (this.type == 161 || this.type == 162) {
/* 1441 */                   str = " alter table workflow_formdetail alter column " + str8 + "  varchar(200)";
/*      */                 }
/* 1443 */                 if (this.type == 224 || this.type == 225) {
/* 1444 */                   str = " alter table workflow_formdetail alter column " + str8 + "  varchar(1000)";
/* 1445 */                   if (this.type == 225) {
/* 1446 */                     str = " alter table workflow_formdetail alter column " + str8 + "  varchar(4000)";
/*      */                   }
/*      */                 } 
/*      */                 
/* 1450 */                 if (this.type == 226 || this.type == 227 || this.type == 256 || this.type == 257) {
/* 1451 */                   str = " alter table workflow_formdetail alter column " + str8 + "  varchar(1000)";
/* 1452 */                   if (this.type == 227 || this.type == 257) {
/* 1453 */                     str = " alter table workflow_formdetail alter column " + str8 + "  varchar(4000)";
/*      */                   }
/*      */                 } 
/*      */                 
/* 1457 */                 if (!recordSet.executeSql(str)) {
/* 1458 */                   throw new Exception(str1);
/*      */                 }
/*      */               } else {
/* 1461 */                 String str10 = " alter table workflow_formdetail drop column  " + str8;
/* 1462 */                 String str11 = " alter table workflow_formdetail add " + str8 + " " + this.fielddbtype;
/* 1463 */                 if (this.type == 161 || this.type == 162) {
/* 1464 */                   str11 = " alter table workflow_formdetail add " + str8 + " varchar(200)";
/*      */                 }
/* 1466 */                 if (this.type == 224 || this.type == 225) {
/* 1467 */                   str11 = " alter table workflow_formdetail add " + str8 + " varchar(1000)";
/* 1468 */                   if (this.type == 225) {
/* 1469 */                     str11 = " alter table workflow_formdetail add " + str8 + " varchar(4000)";
/*      */                   }
/*      */                 } 
/* 1472 */                 if (this.type == 226 || this.type == 227 || this.type == 256 || this.type == 257) {
/* 1473 */                   str11 = " alter table workflow_formdetail add " + str8 + " varchar(1000)";
/* 1474 */                   if (this.type == 227 || this.type == 257) {
/* 1475 */                     str11 = " alter table workflow_formdetail add " + str8 + " varchar(4000)";
/*      */                   }
/*      */                 } 
/*      */ 
/*      */                 
/* 1480 */                 if (!recordSet.executeSql(str10)) {
/* 1481 */                   throw new Exception("modify error");
/*      */                 }
/* 1483 */                 if (!recordSet.executeSql(str11)) {
/*      */                   try {
/* 1485 */                     recordSet.execute("delete from workflow_formdictdetail where id=" + this.fieldid);
/* 1486 */                   } catch (Exception exception) {}
/* 1487 */                   throw new Exception(str1);
/*      */                 } 
/*      */               } 
/*      */             }
/*      */             
/* 1492 */             if (!str8.equalsIgnoreCase(this.fieldname)) {
/*      */               
/* 1494 */               String str = "exec sp_rename 'workflow_formdetail." + str8 + "', '" + this.fieldname + "', 'column' ";
/* 1495 */               if (!recordSet.executeSql(str))
/*      */               {
/* 1497 */                 throw new Exception("modify error");
/*      */               }
/*      */             } 
/*      */           } 
/*      */         } 
/*      */         
/* 1503 */         connStatement.setStatementSql(str3);
/* 1504 */         connStatement.setString(1, this.fieldname);
/* 1505 */         connStatement.setString(2, this.fielddbtype);
/* 1506 */         connStatement.setString(3, this.fieldhtmltype);
/* 1507 */         connStatement.setInt(4, this.type);
/* 1508 */         connStatement.setInt(5, this.subCompanyId2);
/* 1509 */         connStatement.setString(6, this.description);
/* 1510 */         connStatement.setInt(7, this.textheight);
/* 1511 */         connStatement.setString(8, this.textheight_2);
/* 1512 */         connStatement.setInt(9, this.childfieldid);
/* 1513 */         connStatement.setString(10, this.qfwws);
/* 1514 */         connStatement.setInt(11, this.imgwidth);
/* 1515 */         connStatement.setInt(12, this.imgheight);
/* 1516 */         connStatement.setString(13, this.istemplate);
/* 1517 */         connStatement.setInt(14, Util.getIntValue(this.fieldshowtypes, 1));
/* 1518 */         connStatement.setInt(15, this.fieldid);
/* 1519 */         connStatement.executeUpdate();
/*      */       }
/*      */     
/*      */     }
/* 1523 */     catch (Exception exception) {
/*      */       
/* 1525 */       str6 = "2";
/* 1526 */       writeLog(exception);
/*      */     } finally {
/*      */ 
/*      */       
/*      */       try {
/*      */         
/* 1532 */         connStatement.close();
/*      */       }
/* 1534 */       catch (Exception exception) {}
/*      */     } 
/*      */ 
/*      */     
/* 1538 */     return str6;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private boolean fieldNameExist(int paramInt, String paramString, boolean paramBoolean) {
/* 1550 */     boolean bool = false;
/* 1551 */     String str = "select 1 from workflow_formdict where fieldname='" + paramString + "' and id <>" + paramInt;
/* 1552 */     if (paramBoolean) {
/* 1553 */       str = "select 1 from workflow_formdictdetail where fieldname='" + paramString + "' and id <>" + paramInt;
/*      */     }
/* 1555 */     RecordSet recordSet = new RecordSet();
/* 1556 */     recordSet.execute(str);
/* 1557 */     if (recordSet.getCounts() > 0) {
/* 1558 */       bool = true;
/*      */     }
/* 1560 */     return bool;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public List getAllSelectTypeField(int paramInt) {
/* 1571 */     String str1 = "";
/* 1572 */     String str2 = "";
/* 1573 */     ArrayList<String> arrayList = new ArrayList();
/* 1574 */     List list = getAvailSelectTypeField(paramInt);
/* 1575 */     String str3 = "select a.id,b.fieldlable from workflow_formdict a left join workflow_fieldlable b  on a.id = b.fieldid where a.fieldhtmltype=5 and b.formid=" + paramInt;
/* 1576 */     RecordSet recordSet = new RecordSet();
/* 1577 */     recordSet.executeSql(str3);
/* 1578 */     while (recordSet.next()) {
/*      */       
/* 1580 */       str1 = Util.null2String(recordSet.getString("id"));
/* 1581 */       str2 = Util.null2String(recordSet.getString("fieldlable"));
/* 1582 */       if (!list.contains(str1))
/*      */       {
/* 1584 */         arrayList.add(str1 + "," + str2);
/*      */       }
/*      */     } 
/* 1587 */     return arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private List getAvailSelectTypeField(int paramInt) {
/* 1599 */     String str1 = "";
/* 1600 */     ArrayList<String> arrayList = new ArrayList();
/* 1601 */     String str2 = "select a.* from workflow_selectitem a left join workflow_fieldlable b  on a.fieldid = b.fieldid  where a.docPath is null or a.docCategory is null or a.docpath='' or a.docCategory=''  and b.formid=" + paramInt;
/* 1602 */     RecordSet recordSet = new RecordSet();
/* 1603 */     recordSet.executeSql(str2);
/* 1604 */     while (recordSet.next()) {
/*      */       
/* 1606 */       str1 = Util.null2String(recordSet.getString("fieldid"));
/* 1607 */       arrayList.add(str1);
/*      */     } 
/* 1609 */     return arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getIsused() {
/* 1616 */     return this.isused;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setIsused(String paramString) {
/* 1623 */     this.isused = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setLanguageid(int paramInt) {
/* 1632 */     this.languageid = paramInt;
/*      */   }
/*      */   
/*      */   public String getQfwws() {
/* 1636 */     return this.qfwws;
/*      */   }
/*      */   
/*      */   public void setQfwws(String paramString) {
/* 1640 */     this.qfwws = paramString;
/*      */   }
/*      */   
/*      */   public String getLocatetype() {
/* 1644 */     return this.locatetype;
/*      */   }
/*      */   
/*      */   public void setLocatetype(String paramString) {
/* 1648 */     this.locatetype = paramString;
/*      */   }
/*      */   
/*      */   public String getFieldshowtypes() {
/* 1652 */     return this.fieldshowtypes;
/*      */   }
/*      */   
/*      */   public void setFieldshowtypes(String paramString) {
/* 1656 */     this.fieldshowtypes = paramString;
/*      */   }
/*      */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/field/FieldManager.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */