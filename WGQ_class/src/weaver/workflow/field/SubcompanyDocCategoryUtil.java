/*    */ package weaver.workflow.field;
/*    */ 
/*    */ import java.util.ArrayList;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.docs.category.SecCategoryComInfo;
/*    */ import weaver.general.BaseBean;
/*    */ import weaver.general.Util;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class SubcompanyDocCategoryUtil
/*    */   extends BaseBean
/*    */ {
/*    */   public static String getDocPath(int paramInt1, int paramInt2, int paramInt3, int paramInt4, String paramString) throws Exception {
/* 41 */     String str1 = "SELECT docPath,docCategory FROM Workflow_SelectitemObj WHERE fieldId=" + paramInt1 + " and isBill=" + paramInt2 + " and selectValue=" + paramInt3 + " and objId=" + paramInt4 + " AND objType='" + paramString + "'";
/* 42 */     String str2 = "";
/* 43 */     String str3 = "";
/*    */     
/* 45 */     RecordSet recordSet = new RecordSet();
/* 46 */     recordSet.executeSql(str1);
/* 47 */     if (recordSet.next()) {
/* 48 */       str2 = Util.null2String(recordSet.getString("docPath"));
/* 49 */       str3 = Util.null2String(recordSet.getString("docCategory"));
/*    */     } 
/* 51 */     SecCategoryComInfo secCategoryComInfo = new SecCategoryComInfo();
/* 52 */     ArrayList<String> arrayList = Util.TokenizerString(str3, ",");
/*    */     try {
/* 54 */       String str4 = arrayList.get(0);
/* 55 */       String str5 = arrayList.get(1);
/* 56 */       String str6 = arrayList.get(2);
/* 57 */       str2 = secCategoryComInfo.getAllParentName(str6, true);
/*    */     }
/* 59 */     catch (Exception exception) {
/* 60 */       str2 = secCategoryComInfo.getAllParentName(str3, true);
/*    */     } 
/* 62 */     return str2;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/field/SubcompanyDocCategoryUtil.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */