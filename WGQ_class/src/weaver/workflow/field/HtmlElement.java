package weaver.workflow.field;

import java.util.Hashtable;
import weaver.hrm.User;

public interface HtmlElement {
  Hashtable getHtmlElementString(int paramInt1, String paramString1, int paramInt2, String paramString2, int paramInt3, int paramInt4, int paramInt5, String paramString3, int paramInt6, int paramInt7, int paramInt8, int paramInt9, User paramUser, Hashtable paramHashtable);
}


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/field/HtmlElement.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */