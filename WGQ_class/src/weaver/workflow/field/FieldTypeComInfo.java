/*    */ package weaver.workflow.field;
/*    */ 
/*    */ import weaver.cache.CacheBase;
/*    */ import weaver.cache.CacheColumn;
/*    */ import weaver.cache.CacheColumnType;
/*    */ import weaver.cache.PKColumn;
/*    */ import weaver.general.Util;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class FieldTypeComInfo
/*    */   extends CacheBase
/*    */ {
/* 20 */   protected static String TABLE_NAME = "workflow_fieldtype";
/*    */   
/* 22 */   protected static String TABLE_WHERE = "status=1";
/*    */   
/* 24 */   protected static String TABLE_ORDER = "orderid";
/*    */   
/*    */   @PKColumn(type = CacheColumnType.NUMBER)
/* 27 */   protected static String PK_NAME = "id";
/*    */   
/*    */   @CacheColumn
/*    */   protected static int typename;
/*    */   @CacheColumn
/*    */   protected static int namelabel;
/*    */   @CacheColumn
/*    */   protected static int classname;
/*    */   @CacheColumn
/*    */   protected static int ifdetailuse;
/*    */   @CacheColumn
/*    */   protected static int orderid;
/*    */   @CacheColumn
/*    */   protected static int status;
/*    */   
/*    */   public int getFieldTypeNum() {
/* 43 */     return size();
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   @Deprecated
/*    */   public boolean next(String paramString) {
/* 54 */     return false;
/*    */   }
/*    */   
/*    */   public String getID() {
/* 58 */     return (String)getRowValue(0);
/*    */   }
/*    */   
/*    */   public int getNamelabel() {
/* 62 */     return Util.getIntValue((String)getRowValue(namelabel));
/*    */   }
/*    */   
/*    */   public int getNamelabel(String paramString) {
/* 66 */     return Util.getIntValue((String)getValue(namelabel, paramString));
/*    */   }
/*    */   
/*    */   public String getClassname() {
/* 70 */     return (String)getRowValue(classname);
/*    */   }
/*    */   
/*    */   public String getClassname(String paramString) {
/* 74 */     return (String)getValue(classname, paramString);
/*    */   }
/*    */   
/*    */   public int getIfdetailuse() {
/* 78 */     return Util.getIntValue((String)getRowValue(ifdetailuse));
/*    */   }
/*    */   
/*    */   public int getIfdetailuse(String paramString) {
/* 82 */     return Util.getIntValue((String)getValue(ifdetailuse, paramString));
/*    */   }
/*    */   
/*    */   public int getStatus() {
/* 86 */     return Util.getIntValue((String)getRowValue(status));
/*    */   }
/*    */   
/*    */   public int getStatus(String paramString) {
/* 90 */     return Util.getIntValue((String)getValue(status, paramString));
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/field/FieldTypeComInfo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */