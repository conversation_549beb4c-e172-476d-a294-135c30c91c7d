/*     */ package weaver.workflow.field;
/*     */ 
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class FieldInfo
/*     */   extends BaseBean
/*     */ {
/*     */   private RecordSet statement;
/*     */   private int nodeid;
/*     */   private int wfid;
/*     */   
/*     */   public void resetParameter() {}
/*     */   
/*     */   public void setWfid(int paramInt) {
/*  51 */     this.wfid = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setNodeid(int paramInt) {
/*  62 */     this.nodeid = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getFieldid() throws Exception {
/*  73 */     return this.statement.getInt("fieldid");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getFieldlable() throws Exception {
/*  84 */     return this.statement.getString("fieldlable");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getIsview() throws Exception {
/*  95 */     return this.statement.getString("isview");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getIsedit() throws Exception {
/* 106 */     return this.statement.getString("isedit");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getIsMandatory() throws Exception {
/* 117 */     return this.statement.getString("ismandatory");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void getFieldinfo() throws Exception {
/* 127 */     String str = "select b.fieldid,b.fieldlable,a.isview,a.isedit,a.ismandatory from workflow_nodeform a,workflow_fieldlable b,workflow_base c where c.id=" + this.wfid + " and b.formid = c.formid and a.nodeid=" + this.nodeid + " and a.fieldid = b.fieldid and b.isdefault='1'";
/* 128 */     this.statement = new RecordSet();
/*     */     
/*     */     try {
/* 131 */       this.statement.executeSql(str);
/*     */     
/*     */     }
/* 134 */     catch (Exception exception) {
/*     */       
/* 136 */       writeLog(exception);
/* 137 */       throw exception;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean next() throws Exception {
/* 149 */     return this.statement.next();
/*     */   }
/*     */   
/*     */   public void closeStatement() {}
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/field/FieldInfo.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */