/*     */ package weaver.workflow.field;
/*     */ 
/*     */ import com.api.browser.bean.BrowserValueInfo;
/*     */ import com.api.browser.service.BrowserValueInfoService;
/*     */ import com.engine.hrm.biz.HrmClassifiedProtectionBiz;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import javax.servlet.http.HttpSession;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class FieldValue
/*     */   extends BaseBean
/*     */ {
/*     */   private int lang;
/*     */   
/*     */   public int getLang() {
/*  34 */     return this.lang;
/*     */   }
/*     */   
/*     */   public void setLang(int paramInt) {
/*  38 */     this.lang = paramInt;
/*     */   }
/*     */   
/*     */   public String getFieldValue(User paramUser, int paramInt1, int paramInt2, int paramInt3, String paramString, int paramInt4) throws Exception {
/*  42 */     return getfieldValue(null, paramUser, null, null, paramInt1, paramInt2, paramInt3, paramString, paramInt4);
/*     */   }
/*     */   
/*     */   public String getFieldValue(HttpSession paramHttpSession, int paramInt1, int paramInt2, int paramInt3, String paramString, int paramInt4) throws Exception {
/*  46 */     return getfieldValue(paramHttpSession, null, null, null, paramInt1, paramInt2, paramInt3, paramString, paramInt4);
/*     */   }
/*     */   
/*     */   public String getfieldValue(HttpSession paramHttpSession, User paramUser, String paramString1, String paramString2, int paramInt1, int paramInt2, int paramInt3, String paramString3, int paramInt4) throws Exception {
/*  50 */     RecordSet recordSet = new RecordSet();
/*  51 */     String str = "";
/*     */     
/*     */     try {
/*  54 */       if (paramHttpSession != null)
/*  55 */         paramUser = (User)paramHttpSession.getAttribute("weaver_user@bean"); 
/*  56 */       int i = this.lang;
/*  57 */       if (paramUser != null) {
/*  58 */         i = paramUser.getLanguage();
/*     */       }
/*  60 */       if (paramInt2 == 3) {
/*  61 */         if (paramInt3 == 2 || paramInt3 == 19 || paramInt3 == 290) {
/*  62 */           str = str + paramString3;
/*     */         } else {
/*  64 */           String str1 = "";
/*  65 */           String str2 = "";
/*  66 */           if (paramInt4 == 0) {
/*  67 */             str2 = "select * from workflow_formdict where id=" + paramInt1;
/*     */           } else {
/*  69 */             str2 = "select * from workflow_billfield w where w.id=" + paramInt1;
/*     */           } 
/*  71 */           recordSet.executeQuery(str2, new Object[0]);
/*  72 */           if (recordSet.next()) {
/*  73 */             str1 = recordSet.getString("fielddbtype");
/*     */           }
/*  75 */           BrowserValueInfoService browserValueInfoService = new BrowserValueInfoService();
/*  76 */           HashMap<Object, Object> hashMap = new HashMap<>();
/*  77 */           hashMap.put("moduleName4Org", "workflow");
/*  78 */           List list = browserValueInfoService.getBrowserValueInfo(paramInt3, str1, paramInt1, paramString3, paramUser.getLanguage(), Util.getIntValue(paramString2), hashMap);
/*  79 */           if (list.size() > 0) {
/*  80 */             for (BrowserValueInfo browserValueInfo : list) {
/*     */               try {
/*  82 */                 BrowserValueInfo browserValueInfo1 = browserValueInfo;
/*  83 */                 str = str + (!"".equals(str) ? "," : "");
/*  84 */                 str = str + Util.null2String(browserValueInfo1.getName());
/*  85 */               } catch (Exception exception) {}
/*     */             } 
/*     */           } else {
/*  88 */             str = paramString3;
/*     */           } 
/*     */         } 
/*  91 */       } else if (paramInt2 == 4) {
/*  92 */         if (paramString3.equals("1")) {
/*  93 */           str = str + "√";
/*     */         }
/*  95 */       } else if (paramInt2 == 5) {
/*  96 */         if (paramInt3 == 2) {
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */           
/* 106 */           if (paramString3.endsWith(",")) {
/* 107 */             paramString3 = paramString3.substring(0, paramString3.length() - 1);
/*     */           }
/* 109 */           String str1 = "";
/* 110 */           if (paramString3.length() > 0) {
/* 111 */             recordSet.executeSql("select listorder,selectvalue, selectname from workflow_SelectItem where fieldid=" + paramInt1 + " and isbill = " + paramInt4 + " and " + Util.getSubINClause(paramString3, "selectvalue", "in") + " order by listorder,selectvalue ");
/* 112 */             while (recordSet.next()) {
/* 113 */               str1 = str1 + "," + recordSet.getString("selectname");
/*     */             }
/* 115 */             if (str1.startsWith(",")) {
/* 116 */               str1 = str1.substring(1);
/*     */             }
/*     */           } 
/* 119 */           str = str1;
/*     */         } else {
/*     */           
/* 122 */           recordSet.executeSql("select selectvalue,selectname from workflow_SelectItem where fieldid = " + paramInt1 + " and isbill = " + paramInt4 + " order by listorder,id");
/* 123 */           while (recordSet.next()) {
/* 124 */             String str1 = Util.null2String(recordSet.getString("selectvalue"));
/* 125 */             String str2 = Util.toScreen(recordSet.getString("selectname"), i);
/* 126 */             if (str1.equals(paramString3)) {
/* 127 */               str = str + str2;
/*     */             }
/*     */           } 
/*     */         } 
/* 131 */       } else if (paramInt2 == 6) {
/* 132 */         if (!"".equals(paramString3)) {
/* 133 */           String[] arrayOfString = Util.null2String(paramString3).split(",");
/* 134 */           for (String str1 : arrayOfString) {
/* 135 */             recordSet.executeQuery("select id,docsubject,accessorycount from docdetail where id = ? order by id asc", new Object[] { str1 });
/* 136 */             if (recordSet.next()) {
/* 137 */               str = str + recordSet.getString("docsubject") + ",";
/*     */             } else {
/*     */               
/* 140 */               str = str + str1 + ",";
/*     */             } 
/*     */           } 
/*     */         } 
/* 144 */         if (str.endsWith(",")) {
/* 145 */           str = str.substring(0, str.length() - 1);
/*     */         }
/* 147 */       } else if (paramInt1 == -10) {
/* 148 */         str = (new HrmClassifiedProtectionBiz()).getResourceSecLevelShowName(paramString3, paramUser.getLanguage() + "");
/*     */       } else {
/* 150 */         str = paramString3;
/*     */       } 
/* 152 */     } catch (Exception exception) {
/* 153 */       exception.printStackTrace();
/* 154 */       writeLog("ModifyLogCmd Exception:" + exception.getMessage());
/* 155 */       writeLog("ModifyLogCmd Exception: user:" + paramUser.getUID() + " workflowid:" + paramString1 + " requestid:" + paramString2 + " fieldId:" + paramInt1 + " fieldValue:" + paramString3 + " isBill:" + paramInt4);
/*     */     } 
/* 157 */     return str;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/field/FieldValue.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */