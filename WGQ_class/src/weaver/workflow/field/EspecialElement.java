/*    */ package weaver.workflow.field;
/*    */ 
/*    */ import java.util.HashMap;
/*    */ import java.util.Hashtable;
/*    */ import weaver.general.BaseBean;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.User;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class EspecialElement
/*    */   extends BaseBean
/*    */   implements HtmlElement
/*    */ {
/*    */   public static void main(String[] paramArrayOfString) {}
/*    */   
/*    */   public Hashtable getHtmlElementString(int paramInt1, String paramString1, int paramInt2, String paramString2, int paramInt3, int paramInt4, int paramInt5, String paramString3, int paramInt6, int paramInt7, int paramInt8, int paramInt9, User paramUser, Hashtable paramHashtable) {
/* 40 */     Hashtable<Object, Object> hashtable = new Hashtable<Object, Object>();
/* 41 */     String str1 = "";
/* 42 */     String str2 = "";
/*    */     try {
/* 44 */       if (paramInt4 == 0 && 
/* 45 */         paramInt7 == 1) {
/* 46 */         str1 = str1 + "<span id=\"field" + paramInt1 + "span\" name=\"field" + paramInt1 + "span\" style=\"word-break:break-all;word-wrap:break-word\">";
/* 47 */         int i = Util.getIntValue((String)paramHashtable.get("isbill"));
/* 48 */         SpecialFieldInfo specialFieldInfo = new SpecialFieldInfo();
/* 49 */         HashMap hashMap = specialFieldInfo.getFormSpecialField();
/* 50 */         if (i == 0) {
/* 51 */           str1 = str1 + Util.null2String((String)hashMap.get(paramInt1 + "_0"));
/*    */         } else {
/* 53 */           str1 = str1 + Util.null2String((String)hashMap.get(paramInt1 + "_1"));
/*    */         } 
/* 55 */         str1 = str1 + "</span>\n";
/*    */ 
/*    */       
/*    */       }
/*    */ 
/*    */     
/*    */     }
/* 62 */     catch (Exception exception) {
/* 63 */       str1 = "";
/* 64 */       writeLog(exception);
/*    */     } 
/* 66 */     hashtable.put("jsStr", str2);
/* 67 */     hashtable.put("inputStr", str1);
/* 68 */     return hashtable;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/field/EspecialElement.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */