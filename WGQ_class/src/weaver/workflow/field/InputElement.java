/*      */ package weaver.workflow.field;
/*      */ 
/*      */ import java.text.DecimalFormat;
/*      */ import java.util.ArrayList;
/*      */ import java.util.HashMap;
/*      */ import java.util.Hashtable;
/*      */ import org.json.JSONObject;
/*      */ import weaver.fna.general.FnaCommon;
/*      */ import weaver.general.BaseBean;
/*      */ import weaver.general.Util;
/*      */ import weaver.hrm.User;
/*      */ import weaver.systeminfo.SystemEnv;
/*      */ import weaver.workflow.exceldesign.FormatFieldValue;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ public class InputElement
/*      */   extends BaseBean
/*      */   implements HtmlElement
/*      */ {
/*      */   public static void main(String[] paramArrayOfString) {}
/*      */   
/*      */   public Hashtable getHtmlElementString(int paramInt1, String paramString1, int paramInt2, String paramString2, int paramInt3, int paramInt4, int paramInt5, String paramString3, int paramInt6, int paramInt7, int paramInt8, int paramInt9, User paramUser, Hashtable paramHashtable) {
/*   48 */     Hashtable<Object, Object> hashtable = new Hashtable<>();
/*   49 */     String str1 = "";
/*   50 */     String str2 = "";
/*      */     try {
/*   52 */       int i = Util.getIntValue((String)paramHashtable.get("workflowid"));
/*   53 */       int j = Util.getIntValue((String)paramHashtable.get("requestid"), 0);
/*   54 */       int k = Util.getIntValue((String)paramHashtable.get("detailNumber"));
/*   55 */       int m = Util.getIntValue((String)paramHashtable.get("detailRecordId"), 0);
/*   56 */       int n = Util.getIntValue((String)paramHashtable.get("languageId"), 0);
/*   57 */       if (n <= 0 && paramUser != null) {
/*   58 */         n = paramUser.getLanguage();
/*      */       }
/*   60 */       FnaCommon fnaCommon = new FnaCommon();
/*      */       
/*   62 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/*   63 */       HashMap<Object, Object> hashMap2 = new HashMap<>();
/*   64 */       HashMap<Object, Object> hashMap3 = new HashMap<>();
/*   65 */       HashMap<Object, Object> hashMap4 = new HashMap<>();
/*   66 */       HashMap<Object, Object> hashMap5 = new HashMap<>();
/*   67 */       HashMap<Object, Object> hashMap6 = new HashMap<>();
/*      */       
/*   69 */       if (paramInt2 == 1 || paramInt2 == 2 || paramInt2 == 3) {
/*   70 */         hashMap1 = (HashMap<Object, Object>)paramHashtable.get("_isEnableFnaWfHm_FnaCommon.getIsEnableFnaWfHm_workflowid=" + i + "__requestId=" + j);
/*   71 */         if (hashMap1 == null) {
/*   72 */           hashMap1 = new HashMap<>();
/*      */         }
/*   74 */         hashMap2 = (HashMap<Object, Object>)paramHashtable.get("_isEnableFnaWfHm_fnaBudgetControl.getFnaWfFieldInfo4Expense_workflowid=" + i + "__requestId=" + j);
/*   75 */         if (hashMap2 == null) {
/*   76 */           hashMap2 = new HashMap<>();
/*      */         }
/*   78 */         hashMap3 = (HashMap<Object, Object>)paramHashtable.get("_isEnableFnaWfHm_FnaCommon.qryFnaExpenseRequestRecord_workflowid=" + i + "__requestId=" + j);
/*   79 */         if (hashMap3 == null) {
/*   80 */           hashMap3 = new HashMap<>();
/*      */         }
/*   82 */         hashMap4 = (HashMap<Object, Object>)paramHashtable.get("_isEnableFnaWfHm_FnaCommon.qryFnaExpenseInfoAllRowRecordHm_workflowid=" + i + "__requestId=" + j);
/*   83 */         if (hashMap4 == null) {
/*   84 */           hashMap4 = new HashMap<>();
/*      */         }
/*   86 */         hashMap5 = (HashMap<Object, Object>)paramHashtable.get("_isEnableFnaWfHm_FnaCommon.qryFnaRepaymentRequestRecord_workflowid=" + i + "__requestId=" + j);
/*   87 */         if (hashMap5 == null) {
/*   88 */           hashMap5 = new HashMap<>();
/*      */         }
/*   90 */         hashMap6 = (HashMap<Object, Object>)paramHashtable.get("_isEnableFnaWfHm_FnaCommon.qryFnaReverseAdvanceRequestRecord_workflowid=" + i + "__requestId=" + j);
/*   91 */         if (hashMap6 == null) {
/*   92 */           hashMap6 = new HashMap<>();
/*      */         }
/*      */       } 
/*   95 */       HashMap<Object, Object> hashMap7 = null;
/*   96 */       HashMap<Object, Object> hashMap8 = null;
/*   97 */       if (m > 0) {
/*   98 */         hashMap7 = (HashMap)hashMap5.get(m + "");
/*   99 */         if (hashMap7 == null) {
/*  100 */           hashMap7 = new HashMap<>();
/*      */         }
/*  102 */         String str24 = Util.null2String((String)hashMap7.get("jklc"));
/*  103 */         String str25 = Util.null2String((String)hashMap7.get("dnxh"));
/*  104 */         hashMap8 = (HashMap)hashMap5.get("borrowRequestIdInfo_" + str24 + "_" + str25);
/*      */       } 
/*  106 */       if (hashMap7 == null) {
/*  107 */         hashMap7 = new HashMap<>();
/*      */       }
/*  109 */       if (hashMap8 == null) {
/*  110 */         hashMap8 = new HashMap<>();
/*      */       }
/*      */       
/*  113 */       HashMap<Object, Object> hashMap9 = null;
/*  114 */       HashMap<Object, Object> hashMap10 = null;
/*  115 */       if (m > 0) {
/*  116 */         hashMap9 = (HashMap)hashMap6.get(m + "");
/*  117 */         if (hashMap9 == null) {
/*  118 */           hashMap9 = new HashMap<>();
/*      */         }
/*  120 */         String str24 = Util.null2String((String)hashMap9.get("Yfklc"));
/*  121 */         String str25 = Util.null2String((String)hashMap9.get("dnxh"));
/*  122 */         hashMap10 = (HashMap)hashMap6.get("advanceRequestIdInfo_" + str24 + "_" + str25);
/*      */       } 
/*  124 */       if (hashMap9 == null) {
/*  125 */         hashMap9 = new HashMap<>();
/*      */       }
/*  127 */       if (hashMap10 == null) {
/*  128 */         hashMap10 = new HashMap<>();
/*      */       }
/*      */       
/*  131 */       int i1 = Util.getIntValue((String)paramHashtable.get("isprint"), 0);
/*  132 */       int i2 = Util.getIntValue((String)paramHashtable.get("version"), 0);
/*      */       
/*  134 */       String str3 = "";
/*  135 */       String str4 = "", str5 = "", str6 = "";
/*  136 */       FinancialElement financialElement = new FinancialElement();
/*  137 */       if (i2 == 2 && paramHashtable.containsKey("_financial")) {
/*  138 */         str3 = Util.null2String(paramHashtable.get("_financial"));
/*  139 */         if (!"".equals(str3)) {
/*  140 */           str4 = "display:none;";
/*  141 */           str5 = "showFinancialField(this);";
/*  142 */           str6 = "dynamicFinancialField(this);";
/*      */         } 
/*      */       } 
/*      */       
/*  146 */       FormatFieldValue formatFieldValue = new FormatFieldValue();
/*      */       
/*  148 */       String str7 = "", str8 = "", str9 = "", str10 = "", str11 = "", str12 = "", str13 = "", str14 = "";
/*  149 */       if (i2 == 2 && paramHashtable.containsKey("_format") && "".equals(str3)) {
/*  150 */         str7 = Util.null2String(paramHashtable.get("_format"));
/*  151 */         if (!"".equals(str7)) {
/*  152 */           str8 = formatFieldValue.FormatValue(paramString3, str7, 1, paramInt2);
/*      */           
/*  154 */           str9 = " _format=" + str7 + " ";
/*  155 */           str10 = "showFormatObj(this,1);";
/*  156 */           str11 = "showFormatObj(this,2);";
/*  157 */           str12 = "display:none;";
/*  158 */           if (paramInt4 == 0) {
/*  159 */             str13 = "width:" + ((paramInt2 == 1) ? "90%" : "85%") + ";";
/*      */           } else {
/*  161 */             str13 = "width:" + ((paramInt2 == 1) ? "90%" : "75%") + ";";
/*      */           } 
/*  163 */           JSONObject jSONObject = new JSONObject("{" + str7 + "}");
/*  164 */           int i5 = Util.getIntValue(jSONObject.getString("formatPattern"), -1);
/*  165 */           if (Util.getIntValue(jSONObject.getString("numberType"), -1) == 2 && (i5 == 1 || i5 == 4)) {
/*  166 */             str13 = str13 + "color:red !important;";
/*  167 */             str14 = str14 + "color:red !important;";
/*      */           } 
/*      */         } 
/*      */       } 
/*      */ 
/*      */       
/*  173 */       String str15 = "";
/*  174 */       String str16 = "";
/*  175 */       String str17 = "";
/*  176 */       String str18 = "";
/*  177 */       String str19 = "";
/*  178 */       String str20 = "";
/*  179 */       String str21 = "";
/*  180 */       String str22 = "";
/*  181 */       if (i2 == 2) {
/*  182 */         if (paramHashtable.containsKey("_formula") && "y".equals(Util.null2String(paramHashtable.get("_formula")))) {
/*  183 */           str15 = "formulaTrigger(this);";
/*  184 */           str10 = str10 + "formulaTrigger(this,true);";
/*  185 */         } else if (paramHashtable.containsKey("_propertyChangeFormulaField_") && "y"
/*  186 */           .equals(Util.null2String(paramHashtable.get("_propertyChangeFormulaField_")))) {
/*  187 */           str16 = "formulaTrigger(this);";
/*      */         } 
/*  189 */         if (paramHashtable.containsKey("_focusFormulaField_") && "y"
/*  190 */           .equals(Util.null2String(paramHashtable.get("_focusFormulaField_")))) {
/*  191 */           str17 = "formulaTrigger(this);";
/*      */         }
/*  193 */         if (paramHashtable.containsKey("_blurFormulaField_") && "y"
/*  194 */           .equals(Util.null2String(paramHashtable.get("_blurFormulaField_")))) {
/*  195 */           str18 = "formulaTrigger(this);";
/*      */         }
/*  197 */         if (paramHashtable.containsKey("_clickFormulaField_") && "y"
/*  198 */           .equals(Util.null2String(paramHashtable.get("_clickFormulaField_")))) {
/*  199 */           str19 = "formulaTrigger(this);";
/*      */         }
/*  201 */         if (paramHashtable.containsKey("_doubleClickFormulaField_") && "y"
/*  202 */           .equals(Util.null2String(paramHashtable.get("_doubleClickFormulaField_")))) {
/*  203 */           str20 = "formulaTrigger(this);";
/*      */         }
/*  205 */         if (paramHashtable.containsKey("_mouseOverFormulaField_") && "y"
/*  206 */           .equals(Util.null2String(paramHashtable.get("_mouseOverFormulaField_")))) {
/*  207 */           str21 = "formulaTrigger(this);";
/*      */         }
/*  209 */         if (paramHashtable.containsKey("_mouseOutFormulaField_") && "y"
/*  210 */           .equals(Util.null2String(paramHashtable.get("_mouseOutFormulaField_")))) {
/*  211 */           str22 = "formulaTrigger(this);";
/*      */         }
/*      */       } 
/*      */       
/*  215 */       int i3 = paramUser.getLanguage();
/*  216 */       String str23 = Util.null2String((String)paramHashtable.get("fielddbtype"));
/*      */       
/*  218 */       int i4 = 2;
/*  219 */       if (paramHashtable.containsKey("decimaldigits_t") && (paramInt2 == 3 || paramInt2 == 5)) {
/*  220 */         i4 = ((Integer)paramHashtable.get("decimaldigits_t")).intValue();
/*      */       }
/*  222 */       if (paramInt4 == 0) {
/*  223 */         HashMap<Object, Object> hashMap = (HashMap)paramHashtable.get("specialfield");
/*  224 */         if (hashMap == null) {
/*  225 */           hashMap = new HashMap<>();
/*      */         }
/*      */         
/*  228 */         int i5 = Util.getIntValue((String)paramHashtable.get("codeField"), 0);
/*      */         
/*  230 */         int i6 = Util.getIntValue((String)paramHashtable.get("titleFieldId"), 0);
/*  231 */         int i7 = Util.getIntValue((String)paramHashtable.get("keywordFieldId"), 0);
/*  232 */         String str24 = Util.null2String((String)paramHashtable.get("trrigerfield"));
/*  233 */         ArrayList arrayList = (ArrayList)paramHashtable.get("changefieldsadd");
/*      */         
/*  235 */         int i8 = Util.getIntValue((String)paramHashtable.get("iscreate"), 0);
/*  236 */         String str25 = Util.null2String((String)paramHashtable.get("isUse"));
/*  237 */         boolean bool = Util.null2String((String)paramHashtable.get("hasHistoryCode")).equals("true");
/*  238 */         String str26 = Util.null2String((String)paramHashtable.get("fieldCode"));
/*      */         
/*  240 */         String str27 = "";
/*  241 */         if (paramInt6 == 0 && paramInt7 == 1 && paramInt8 == 1 && paramInt9 == 1 && "".equals(paramString3)) {
/*  242 */           str27 = "<img src='/images/BacoError_wev8.gif' align='absmiddle'>";
/*      */         }
/*  244 */         String str28 = "";
/*  245 */         String str29 = "";
/*  246 */         ArrayList<String> arrayList1 = (ArrayList)paramHashtable.get("sqlfieldidList");
/*  247 */         ArrayList<String> arrayList2 = (ArrayList)paramHashtable.get("sqlcontentList");
/*  248 */         if (arrayList1 != null && arrayList1.size() > 0) {
/*  249 */           for (byte b = 0; b < arrayList1.size(); b++) {
/*  250 */             String str33 = Util.null2String(arrayList1.get(b)).trim();
/*  251 */             String str34 = Util.null2String(arrayList2.get(b)).trim();
/*  252 */             if (!"".equals(str34) && 
/*  253 */               str34.indexOf("$" + paramInt1 + "$") > -1) {
/*  254 */               str29 = str29 + str33 + ",";
/*      */             }
/*      */           } 
/*      */           
/*  258 */           if (str29.length() > 0) {
/*  259 */             str28 = "fieldAttrOperate.doSqlFieldAjax(this,'" + str29.substring(0, str29.length() - 1) + "');";
/*      */           }
/*      */         } 
/*      */         
/*  263 */         ArrayList<String> arrayList3 = (ArrayList)paramHashtable.get("attrfieldidList");
/*  264 */         ArrayList<String> arrayList4 = (ArrayList)paramHashtable.get("attrcontentList");
/*  265 */         if (arrayList3 != null && arrayList3.size() > 0) {
/*  266 */           for (byte b = 0; b < arrayList3.size(); b++) {
/*  267 */             String str33 = Util.null2String(arrayList3.get(b));
/*  268 */             String str34 = Util.null2String(arrayList4.get(b));
/*  269 */             if (str34.indexOf("$" + paramInt1 + "$") > -1) {
/*  270 */               str28 = str28 + "doMathFieldAttr" + str33 + "();";
/*      */             }
/*      */           } 
/*      */         }
/*      */         
/*  275 */         String str30 = "";
/*  276 */         ArrayList<String> arrayList5 = (ArrayList)paramHashtable.get("datefieldidList");
/*  277 */         ArrayList<String> arrayList6 = (ArrayList)paramHashtable.get("datecontentList");
/*  278 */         if (arrayList5 != null && arrayList5.size() > 0) {
/*  279 */           for (byte b = 0; b < arrayList5.size(); b++) {
/*  280 */             String str33 = Util.null2String(arrayList5.get(b)).trim();
/*  281 */             String str34 = Util.null2String(arrayList6.get(b)).trim();
/*  282 */             if (!"".equals(str34) && 
/*  283 */               str34.indexOf("$" + paramInt1 + "$") > -1) {
/*  284 */               str30 = str30 + "doFieldDate" + str33 + "(-1);";
/*      */             }
/*      */             
/*  287 */             if (str33.equals("" + paramInt1)) {
/*  288 */               str2 = str2 + "function getFieldDateAjax" + paramInt1 + "(){\n";
/*  289 */               str2 = str2 + "doFieldDate" + paramInt1 + "(-1);\n";
/*  290 */               str2 = str2 + "}\n";
/*      */ 
/*      */               
/*  293 */               str2 = str2 + "\tif (window.addEventListener){\n";
/*  294 */               str2 = str2 + "\t    window.addEventListener(\"load\", getFieldDateAjax" + paramInt1 + ", false);\n";
/*  295 */               str2 = str2 + "\t}else if (window.attachEvent){\n";
/*  296 */               str2 = str2 + "\t    window.attachEvent(\"onload\", getFieldDateAjax" + paramInt1 + ");\n";
/*  297 */               str2 = str2 + "\t}else{\n";
/*  298 */               str2 = str2 + "\t    window.onload=getFieldDateAjax" + paramInt1 + ";\n";
/*  299 */               str2 = str2 + "\t}\n";
/*      */             } 
/*      */           } 
/*  302 */           if (str30.length() > 0) {
/*  303 */             str28 = str28 + str30;
/*      */           }
/*      */         } 
/*      */         
/*  307 */         String str31 = "";
/*  308 */         if (str24.indexOf("field" + paramInt1) >= 0) {
/*  309 */           str31 = "datainput('field" + paramInt1 + "');";
/*      */         }
/*      */         
/*  312 */         ArrayList<String> arrayList7 = (ArrayList)paramHashtable.get("sapfieldidList");
/*  313 */         if (arrayList7 != null && arrayList7.size() > 0) {
/*  314 */           for (byte b = 0; b < arrayList7.size(); b++) {
/*  315 */             String str33 = Util.null2String(arrayList7.get(b));
/*  316 */             String str34 = str33.substring(0, str33.indexOf("-"));
/*  317 */             String str35 = str33.substring(str33.indexOf("-") + 1);
/*  318 */             if (("" + paramInt1).equals(str34)) {
/*  319 */               str28 = str28 + "doSAPField('" + str35 + "',this);";
/*      */             }
/*      */           } 
/*      */         }
/*      */         
/*  324 */         String str32 = " fieldtype=\"" + paramInt2 + "\" ";
/*  325 */         if (paramInt7 == 1) {
/*  326 */           if (paramInt6 == 0) {
/*  327 */             if (paramInt8 == 1) {
/*  328 */               if (paramInt2 == 1) {
/*  329 */                 if (paramInt1 != i5) {
/*  330 */                   if (i7 > 0 && i7 == paramInt1) {
/*  331 */                     str1 = str1 + "<button type=button class=\"Browser\" onclick=\"onShowKeyword(field" + paramInt1 + ".getAttribute('viewtype'))\" title=\"" + SystemEnv.getHtmlLabelName(21517, i3) + "\"></button>";
/*      */                   }
/*      */                   
/*  334 */                   String str = "width:90%;";
/*  335 */                   if (i8 == 0 && "1".equals(str25) && !bool && str26.equals("" + paramInt1)) {
/*  336 */                     str = "width:60%;";
/*      */                   }
/*  338 */                   str1 = str1 + "<input " + str32 + " datatype=\"text\" viewtype=\"" + paramInt9 + "\" type=\"text\" class=\"Inputstyle\" temptitle=\"" + Util.toScreen(paramString2, i3) + "\" id=\"field" + paramInt1 + "\" name=\"field" + paramInt1 + "\" style=\"" + str + str12 + str4 + "\" onChange=\"checkinput2('field" + paramInt1 + "','field" + paramInt1 + "span',this.getAttribute('viewtype'));checkLength('field" + paramInt1 + "','" + paramInt3 + "','" + Util.toScreen(paramString2, i3) + "','" + SystemEnv.getHtmlLabelName(20246, i3) + "','" + SystemEnv.getHtmlLabelName(20247, i3) + "');";
/*  339 */                   if (i6 > 0 && i7 > 0 && i6 == paramInt1) {
/*  340 */                     str1 = str1 + "changeKeyword();";
/*      */                   }
/*      */ 
/*      */                   
/*  344 */                   if (i8 == 0 && "1".equals(str25) && !bool && str26.equals("" + paramInt1)) {
/*  345 */                     str1 = str1 + "onChangeCode('" + paramInt9 + "');";
/*      */                   }
/*      */                   
/*  348 */                   str1 = str1 + str28 + "\"";
/*      */                   
/*  350 */                   str1 = str1 + " onBlur=\"";
/*  351 */                   if (str24.indexOf("field" + paramInt1) >= 0) {
/*  352 */                     str1 = str1 + "datainput('field" + paramInt1 + "');";
/*      */                   }
/*  354 */                   str1 = str1 + str10;
/*  355 */                   str1 = str1 + str5;
/*  356 */                   str1 = str1 + str18;
/*  357 */                   str1 = str1 + "\" ";
/*  358 */                   str1 = str1 + " " + str9 + " onpropertychange=\"" + str11 + str6 + str15 + str16 + "\" _listener=\"" + str11 + str6 + str15 + str16 + "\" ";
/*  359 */                   str1 = str1 + " onfocus=\"" + str17 + "\" onclick=\"" + str19 + "\" ";
/*  360 */                   str1 = str1 + " ondblclick=\"" + str20 + "\" onmouseover=\"" + str21 + "\" onmouseout=\"" + str22 + "\" ";
/*  361 */                   str1 = str1 + " value=\"" + Util.toScreenForWorkflow(paramString3) + "\">";
/*      */                 }
/*      */               
/*  364 */               } else if (paramInt2 == 2) {
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */                 
/*  370 */                 str1 = str1 + "<input " + str32 + " datatype=\"int\"  onafterpaste=\"if(isNaN(value))execCommand('undo')\"  style=\"width:85%;ime-mode:disabled;" + str12 + str4 + "\" viewtype=\"" + paramInt9 + "\" type=\"text\" class=\"Inputstyle\" temptitle=\"" + Util.toScreen(paramString2, i3) + "\" id=\"field" + paramInt1 + "\" name=\"field" + paramInt1 + "\" onKeyPress=\"ItemCount_KeyPress()\" ";
/*  371 */                 str1 = str1 + "onBlur=\"checkcount1(this);checkItemScale(this,'" + SystemEnv.getHtmlLabelName(31181, i3).replace("12", "9") + "',-999999999,999999999);checkinput2('field" + paramInt1 + "','field" + paramInt1 + "span',this.getAttribute('viewtype'));";
/*  372 */                 if (str24.indexOf("field" + paramInt1) >= 0) {
/*  373 */                   str1 = str1 + "datainput('field" + paramInt1 + "');";
/*      */                 }
/*  375 */                 str1 = str1 + str10;
/*  376 */                 str1 = str1 + str5;
/*  377 */                 str1 = str1 + str18;
/*  378 */                 str1 = str1 + "\" ";
/*  379 */                 str1 = str1 + " value=\"" + paramString3 + "\" " + str9 + " onchange=\"" + str28 + "\"  onpropertychange=\"" + str28 + str11 + str6 + str15 + str16 + "\"";
/*  380 */                 str1 = str1 + " onfocus=\"" + str17 + "\" onclick=\"" + str19 + "\" ";
/*  381 */                 str1 = str1 + " ondblclick=\"" + str20 + "\" onmouseover=\"" + str21 + "\" onmouseout=\"" + str22 + "\" ";
/*  382 */                 str1 = str1 + " _listener=\"" + str28 + str11 + str6 + str15 + str16 + "\">";
/*  383 */               } else if (paramInt2 == 3 || paramInt2 == 5) {
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */                 
/*  389 */                 str1 = str1 + "<input " + str32 + " datalength='" + i4 + "' datatype=\"float\"  style=\"width:85%;ime-mode:disabled;" + str12 + str4 + "\"  onafterpaste=\"if(isNaN(value))execCommand('undo')\" viewtype=\"" + paramInt9 + "\" type=\"text\" class=\"Inputstyle\" temptitle=\"" + Util.toScreen(paramString2, i3) + "\" id=\"field" + paramInt1 + "\" name=\"field" + paramInt1 + "\" onKeyPress=\"ItemDecimal_KeyPress('field" + paramInt1 + "',15," + i4 + ")\" ";
/*  390 */                 if (paramInt2 == 5) {
/*  391 */                   if (!paramString3.equals("") && i4 == 1) {
/*  392 */                     paramString3 = paramString3.substring(0, paramString3.indexOf(".") + i4 + 1);
/*      */                   }
/*  394 */                   paramString3 = Util.toDecimalDigits(paramString3, i4);
/*  395 */                   str1 = str1 + "  onfocus=\"" + str17 + "changeToNormalFormat('field" + paramInt1 + "')\" ";
/*  396 */                   str1 = str1 + "datavaluetype='5' datalength=" + i4 + " onBlur=\"checkFloat(this);";
/*      */                 } else {
/*  398 */                   paramString3 = Util.toDecimalDigits(paramString3, i4);
/*  399 */                   str1 = str1 + "  onfocus=\"" + str17 + "changeToNormalFormat('field" + paramInt1 + "')\" ";
/*  400 */                   str1 = str1 + " datalength=" + i4 + " onBlur=\"checkFloat(this);";
/*      */                 } 
/*  402 */                 str1 = str1 + "checkinput2('field" + paramInt1 + "','field" + paramInt1 + "span',this.getAttribute('viewtype'));";
/*  403 */                 if (str24.indexOf("field" + paramInt1) >= 0) {
/*  404 */                   str1 = str1 + "datainput('field" + paramInt1 + "');";
/*      */                 }
/*  406 */                 if (paramInt2 == 5) {
/*  407 */                   str1 = str1 + "changeToThousands2('field" + paramInt1 + "','" + i4 + "');";
/*      */                 }
/*  409 */                 str1 = str1 + str10;
/*  410 */                 str1 = str1 + str5;
/*  411 */                 str1 = str1 + str18;
/*  412 */                 str1 = str1 + "\"";
/*  413 */                 str1 = str1 + " value=\"" + paramString3 + "\" " + str9 + " onchange=\"" + str28 + "\" onpropertychange=\"" + str28 + str11 + str6 + str15 + str16 + "\"";
/*  414 */                 str1 = str1 + " onclick=\"" + str19 + "\" ";
/*  415 */                 str1 = str1 + " ondblclick=\"" + str20 + "\" onmouseover=\"" + str21 + "\" onmouseout=\"" + str22 + "\" ";
/*  416 */                 str1 = str1 + " _listener=\"" + str28 + str11 + str6 + str15 + str16 + "\">";
/*      */               }
/*  418 */               else if (paramInt2 == 4) {
/*  419 */                 int i9 = str23.indexOf(",");
/*  420 */                 if (i9 > -1) {
/*  421 */                   i4 = Util.getIntValue(str23.substring(i9 + 1, str23.length() - 1), 2);
/*      */                 }
/*  423 */                 paramString3 = Util.toDecimalDigits(paramString3, i4);
/*  424 */                 str1 = str1 + "<table cols=\"2\" id=\"field" + paramInt1 + "_tab\" width=\"100%\">";
/*  425 */                 str1 = str1 + "<tr><td>";
/*  426 */                 if (paramInt8 == 1) {
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */                   
/*  433 */                   str1 = str1 + "<input datatype=\"float\"  onKeyPress=\"ItemDecimal_KeyPress('field_lable" + paramInt1 + "',15," + i4 + ")\" style=\"ime-mode:disabled;width:95%\"  onafterpaste=\"if(isNaN(value))execCommand('undo')\" type=\"text\" class=\"Inputstyle\" id=\"field_lable" + paramInt1 + "\" name=\"field_lable" + paramInt1 + "\" temptitle=\"" + Util.toScreen(paramString2, i3) + "\" value=\"" + Util.milfloatFormat(paramString3) + "\" onfocus=\"FormatToNumber('" + paramInt1 + "')\" ";
/*  434 */                   if (str24.indexOf("field" + paramInt1) >= 0) {
/*  435 */                     str1 = str1 + " onBlur=\"checkFloat(this);numberToFormat('" + paramInt1 + "');checkinput2('field_lable" + paramInt1 + "','field_lable" + paramInt1 + "span',field" + paramInt1 + ".getAttribute('viewtype'));datainput('field_lable" + paramInt1 + "')\"";
/*      */                   } else {
/*  437 */                     str1 = str1 + " onBlur=\"checkFloat(this);numberToFormat('" + paramInt1 + "');checkinput2('field_lable" + paramInt1 + "','field_lable" + paramInt1 + "span',field" + paramInt1 + ".getAttribute('viewtype'))\"";
/*      */                   } 
/*  439 */                   str1 = str1 + " />";
/*  440 */                   str1 = str1 + "<span id=\"field_lable" + paramInt1 + "span\">" + str27 + "</span>";
/*  441 */                   str1 = str1 + "<span id=\"field" + paramInt1 + "span\" style=\"word-break:break-all;word-wrap:break-word\"></span>";
/*      */                   
/*  443 */                   str1 = str1 + "<input " + str32 + " datatype=\"float\" filedtype=\"4\" datalength=\"2\" viewtype=\"" + paramInt9 + "\" temptitle=\"" + Util.toScreen(paramString2, i3) + "\" type=\"hidden\" class=\"Inputstyle\" id=\"field" + paramInt1 + "\" name=\"field" + paramInt1 + "\" value=\"" + paramString3 + "\" onpropertychange=\"" + str28 + "\"  _listener=\"" + str28 + "\" />";
/*      */                 } 
/*      */               } 
/*      */               
/*  447 */               if (!"".equals(str7) && paramInt2 != 4) {
/*  448 */                 str1 = str1 + "<input type=\"text\" id=\"field" + paramInt1 + "_format\" name=\"field" + paramInt1 + "_format\" ";
/*  449 */                 str1 = str1 + "class=\"Inputstyle\" style=\"" + str13 + "\" value=\"" + str8 + "\" onfocus=\"showRealObj(this);\" readonly />";
/*      */               } 
/*  451 */               if (paramInt2 != 4) {
/*  452 */                 str1 = str1 + "<span id=\"field" + paramInt1 + "span\" style=\"word-break:break-all;word-wrap:break-word;" + str4 + "\">" + str27 + "</span>";
/*      */               }
/*  454 */               if (paramInt2 == 4) {
/*  455 */                 str1 = str1 + "</td></tr>";
/*  456 */                 str1 = str1 + "<tr><td>";
/*  457 */                 str1 = str1 + "<input type=\"text\" class=\"Inputstyle\" style=\"width:95%\" id=\"field_chinglish" + paramInt1 + "\" name=\"field_chinglish" + paramInt1 + "\" value=\"" + Util.numtochinese(paramString3) + "\" readOnly=\"true\">";
/*  458 */                 str1 = str1 + "</td></tr>";
/*  459 */                 str1 = str1 + "</table>";
/*      */               } 
/*      */               
/*  462 */               if (paramInt2 == 1 && 
/*  463 */                 paramInt1 != i5)
/*      */               {
/*  465 */                 if (i8 == 0 && "1".equals(str25) && !bool && str26.equals("" + paramInt1)) {
/*  466 */                   str1 = str1 + "<div id = 'fieldCodediv'>";
/*  467 */                   str1 = str1 + "<A href=\"#\" onclick=\"onCreateCodeAgain('" + paramInt9 + "');return false;\">" + SystemEnv.getHtmlLabelName(22784, i3) + "</a>";
/*  468 */                   str1 = str1 + "&nbsp;&nbsp;&nbsp;&nbsp";
/*  469 */                   str1 = str1 + "<A href=\"#\" onclick=\"onChooseReservedCode('" + paramInt9 + "');return false;\">" + SystemEnv.getHtmlLabelName(22785, i3) + "</a>";
/*  470 */                   str1 = str1 + "&nbsp;&nbsp;&nbsp;&nbsp";
/*  471 */                   str1 = str1 + "<A href=\"#\" onclick=\"onNewReservedCode('" + paramInt9 + "');return false;\">" + SystemEnv.getHtmlLabelName(22783, i3) + "</a>";
/*  472 */                   str1 = str1 + "</div>";
/*      */                 }
/*      */               
/*      */               }
/*      */             } else {
/*      */               
/*  478 */               str1 = str1 + "<input " + str32;
/*  479 */               if (paramInt2 == 1) {
/*  480 */                 str1 = str1 + " datatype=\"text\" ";
/*  481 */               } else if (paramInt2 == 2) {
/*  482 */                 str1 = str1 + " datatype=\"int\" ";
/*  483 */               } else if (paramInt2 == 3 || paramInt2 == 5) {
/*  484 */                 if (paramInt2 == 5 && 
/*  485 */                   !paramString3.equals("") && i4 == 1) {
/*  486 */                   paramString3 = paramString3.substring(0, paramString3.indexOf(".") + i4 + 1);
/*      */                 }
/*      */                 
/*  489 */                 str1 = str1 + " datatype=\"float\" datalength='" + i4 + "' ";
/*  490 */                 paramString3 = Util.toDecimalDigits(paramString3, i4);
/*  491 */                 if (paramInt2 == 5)
/*      */                 {
/*  493 */                   str1 = str1 + " datavaluetype='5' ";
/*      */                 }
/*  495 */               } else if (paramInt2 == 4) {
/*  496 */                 str1 = str1 + " datatype=\"float\" ";
/*      */               } 
/*  498 */               Hashtable hashtable1 = (Hashtable)paramHashtable.get("transtype_hs");
/*  499 */               int i9 = Util.getIntValue((String)hashtable1.get("transtype" + paramInt1));
/*  500 */               if (paramInt2 == 4) {
/*      */                 
/*  502 */                 if (i9 == 1 || i9 == 2) {
/*  503 */                   str1 = str1 + " type=\"hidden\" datalength=\"2\" id=\"field" + paramInt1 + "\" name=\"field" + paramInt1 + "\" value=\"" + Util.toScreenForWorkflow(paramString3) + "\"  onpropertychange=\"checkLength4Read('field" + paramInt1 + "','field" + paramInt1 + "span','" + paramInt3 + "','" + Util.toScreen(paramString2, i3) + "','" + SystemEnv.getHtmlLabelName(20246, i3) + "','" + SystemEnv.getHtmlLabelName(20247, i3) + "');numberToFormatForReadOnly('" + paramInt1 + "');" + str28 + "doformat" + paramInt1 + "();\" onchange=\"" + str28 + "doformat" + paramInt1 + "();\" _listener=\"numberToFormatForReadOnly('" + paramInt1 + "');" + str28 + "doformat" + paramInt1 + "();\" />";
/*      */                 } else {
/*  505 */                   str1 = str1 + " type=\"hidden\" datalength=\"2\" id=\"field" + paramInt1 + "\" name=\"field" + paramInt1 + "\" value=\"" + Util.toScreenForWorkflow(paramString3) + "\"  onpropertychange=\"checkLength4Read('field" + paramInt1 + "','field" + paramInt1 + "span','" + paramInt3 + "','" + Util.toScreen(paramString2, i3) + "','" + SystemEnv.getHtmlLabelName(20246, i3) + "','" + SystemEnv.getHtmlLabelName(20247, i3) + "');numberToFormatForReadOnly('" + paramInt1 + "');" + str28 + "\" onchange=\"" + str28 + "\" _listener=\"numberToFormatForReadOnly('" + paramInt1 + "');" + str28 + "\" />";
/*      */                 }
/*      */               
/*      */               }
/*  509 */               else if (i9 == 1 || i9 == 2) {
/*  510 */                 str1 = str1 + " type=\"hidden\" id=\"field" + paramInt1 + "\" name=\"field" + paramInt1 + "\" value=\"" + Util.toScreenForWorkflow(paramString3) + "\"  onpropertychange=\"checkLength4Read('field" + paramInt1 + "','field" + paramInt1 + "span','" + paramInt3 + "','" + Util.toScreen(paramString2, i3) + "','" + SystemEnv.getHtmlLabelName(20246, i3) + "','" + SystemEnv.getHtmlLabelName(20247, i3) + "');" + str28 + str11 + str6 + str15 + str16 + str31 + "doformat" + paramInt1 + "();\"  onfocus=\"" + str17 + "\" onblur=\"" + str18 + "\" onclick=\"" + str19 + "\"  ondblclick=\"" + str20 + "\" onmouseover=\"" + str21 + "\" onmouseout=\"" + str22 + "\" onchange=\"" + str28 + "doformat" + paramInt1 + "();\" " + str9 + " _listener=\"" + str28 + str11 + str6 + str15 + str16 + "doformat" + paramInt1 + "();\" />";
/*      */               
/*      */               }
/*      */               else {
/*      */                 
/*  515 */                 str1 = str1 + " type=\"hidden\" id=\"field" + paramInt1 + "\" name=\"field" + paramInt1 + "\" value=\"" + Util.toScreenForWorkflow(paramString3) + "\"  onpropertychange=\"checkLength4Read('field" + paramInt1 + "','field" + paramInt1 + "span','" + paramInt3 + "','" + Util.toScreen(paramString2, i3) + "','" + SystemEnv.getHtmlLabelName(20246, i3) + "','" + SystemEnv.getHtmlLabelName(20247, i3) + "');" + str28 + str11 + str6 + str15 + str16 + str31 + "\" onfocus=\"" + str17 + "\" onblur=\"" + str18 + "\" onclick=\"" + str19 + "\"  ondblclick=\"" + str20 + "\" onmouseover=\"" + str21 + "\" onmouseout=\"" + str22 + "\"  onchange=\"" + str28 + "\" " + str9 + " _listener=\"" + str28 + str11 + str6 + str15 + str16 + "\" />";
/*      */               } 
/*      */ 
/*      */ 
/*      */ 
/*      */               
/*  521 */               if (paramInt2 == 4) {
/*  522 */                 str1 = str1 + "<table id=\"field" + paramInt1 + "_tab\">\n";
/*  523 */                 str1 = str1 + "\t<tr><td>\n";
/*      */               } 
/*  525 */               if (paramInt2 == 4) {
/*  526 */                 if (!paramString3.equals("")) {
/*  527 */                   String[] arrayOfString = paramString3.split("\\.");
/*  528 */                   if (arrayOfString.length < 2 && i4 > 0) {
/*  529 */                     String str = "";
/*  530 */                     for (byte b = 0; b < i4; b++) {
/*  531 */                       str = str + "0";
/*      */                     }
/*  533 */                     paramString3 = paramString3 + "." + str;
/*      */                   } 
/*      */                 } 
/*  536 */                 str1 = str1 + "<span id=\"field" + paramInt1 + "span\" style=\"word-break:break-all;word-wrap:break-word\">" + Util.milfloatFormat(paramString3) + "</span>";
/*  537 */                 str1 = str1 + "\t</td></tr>\n";
/*  538 */                 str1 = str1 + "\t<tr><td>\n";
/*  539 */                 str1 = str1 + "<span id=\"field" + paramInt1 + "ncspan\">" + Util.numtochinese(paramString3) + "</span>";
/*  540 */                 str1 = str1 + "\t</td></tr>\n";
/*  541 */                 str1 = str1 + "</table>\n";
/*      */               } else {
/*  543 */                 str1 = str1 + "<span id=\"field" + paramInt1 + "span\" style=\"word-break:break-all;word-wrap:break-word;" + str12 + str4 + "\">";
/*  544 */                 if (i1 == 1 && ("fieldIdHrmInfo_fieldId"
/*  545 */                   .equals(hashMap2.get(paramInt1 + "")) || "fieldIdDepInfo_fieldId"
/*  546 */                   .equals(hashMap2.get(paramInt1 + "")) || "fieldIdSubInfo_fieldId"
/*  547 */                   .equals(hashMap2.get(paramInt1 + "")) || "fieldIdFccInfo_fieldId"
/*  548 */                   .equals(hashMap2.get(paramInt1 + "")) || "fieldIdHrmInfo2_fieldId"
/*  549 */                   .equals(hashMap2.get(paramInt1 + "")) || "fieldIdDepInfo2_fieldId"
/*  550 */                   .equals(hashMap2.get(paramInt1 + "")) || "fieldIdSubInfo2_fieldId"
/*  551 */                   .equals(hashMap2.get(paramInt1 + "")) || "fieldIdFccInfo2_fieldId"
/*  552 */                   .equals(hashMap2.get(paramInt1 + "")))) {
/*  553 */                   str1 = str1 + fnaCommon.getRemainByFnaFieldType(paramInt1, m, Util.toScreenForWorkflow(paramString3), j, k, hashMap2, hashMap3, hashMap4, n, paramHashtable);
/*      */                 
/*      */                 }
/*      */                 else {
/*      */                   
/*  558 */                   str1 = str1 + Util.toScreenForWorkflow(paramString3);
/*      */                 } 
/*  560 */                 str1 = str1 + "</span>";
/*  561 */                 if (!"".equals(str7)) {
/*  562 */                   str1 = str1 + "<span id=\"field" + paramInt1 + "span_format\" style=\"word-break:break-all;word-wrap:break-word;" + str14 + str4 + "\">" + Util.toScreenForWorkflow(str8) + "</span>";
/*      */                 }
/*      */               } 
/*      */             } 
/*  566 */             if (arrayList.indexOf("" + paramInt1) >= 0) {
/*  567 */               str1 = str1 + "<input type=\"hidden\" id=\"oldfieldview" + paramInt1 + "\" name=\"oldfieldview" + paramInt1 + "\" value=\"" + (paramInt7 + paramInt8 + paramInt9) + "\" />";
/*      */             }
/*      */           } else {
/*  570 */             String str = "";
/*  571 */             if (paramInt2 == 5) {
/*  572 */               str = " datatype=\"float\" datavaluetype='5' datalength='" + i4 + "' ";
/*  573 */               paramString3 = Util.toDecimalDigits(paramString3, i4);
/*  574 */             } else if (paramInt2 == 3) {
/*  575 */               str = " datatype=\"float\" datalength='" + i4 + "' ";
/*  576 */               paramString3 = Util.toDecimalDigits(paramString3, i4);
/*  577 */             } else if (paramInt2 == 2) {
/*  578 */               str = " datatype=\"int\" ";
/*      */             } 
/*  580 */             if (paramInt2 == 4) {
/*  581 */               str1 = str1 + "<input " + str32 + " type=\"hidden\" " + str + " id=\"field" + paramInt1 + "\" name=\"field" + paramInt1 + "\" value=\"" + paramString3 + "\"  onpropertychange=\"numberToFormatForReadOnly('" + paramInt1 + "');\"  _listener=\"numberToFormatForReadOnly('" + paramInt1 + "');\" />";
/*      */             } else {
/*  583 */               str1 = str1 + "<input " + str32 + " type=\"hidden\" " + str + " id=\"field" + paramInt1 + "\" name=\"field" + paramInt1 + "\" value=\"" + paramString3 + "\" " + str9 + " onpropertychange=\"" + str11 + "\"  _listener=\"" + str11 + "\" />";
/*      */             } 
/*  585 */             if (paramInt2 == 4) {
/*  586 */               str1 = str1 + "<table id=\"field" + paramInt1 + "_tab\">\n";
/*  587 */               str1 = str1 + "\t<tr><td>\n";
/*      */             } 
/*  589 */             if (paramInt2 == 4) {
/*  590 */               if (!paramString3.equals("")) {
/*  591 */                 String[] arrayOfString = paramString3.split("\\.");
/*  592 */                 if (arrayOfString.length < 2 && i4 > 0) {
/*  593 */                   String str33 = "";
/*  594 */                   for (byte b = 0; b < i4; b++) {
/*  595 */                     str33 = str33 + "0";
/*      */                   }
/*  597 */                   paramString3 = paramString3 + "." + str33;
/*      */                 } 
/*      */               } 
/*  600 */               str1 = str1 + "<span id=\"field" + paramInt1 + "span\" style=\"word-break:break-all;word-wrap:break-word\">" + Util.milfloatFormat(paramString3) + "</span>";
/*  601 */               str1 = str1 + "\t</td></tr>\n";
/*  602 */               str1 = str1 + "\t<tr><td>\n";
/*  603 */               str1 = str1 + "<span id=\"field" + paramInt1 + "ncspan\">" + Util.numtochinese(paramString3) + "</span>";
/*  604 */               str1 = str1 + "\t</td></tr>\n";
/*  605 */               str1 = str1 + "</table>\n";
/*      */             } else {
/*  607 */               String str33 = "";
/*  608 */               if (paramString3.matches("-*\\d+\\.?\\d*") && paramInt2 == 5) {
/*  609 */                 DecimalFormat decimalFormat = new DecimalFormat("###,###.##");
/*  610 */                 str33 = decimalFormat.format(Double.parseDouble(paramString3)) + "";
/*  611 */                 if (paramInt2 == 5) {
/*  612 */                   str33 = "" + Util.toDecimalDigits(str33, i4);
/*      */                 } else {
/*  614 */                   str33 = Util.toDecimalDigits(str33, 2);
/*      */                 } 
/*  616 */                 paramString3 = str33;
/*      */               } else {
/*  618 */                 str33 = paramString3;
/*      */               } 
/*  620 */               paramString3 = str33;
/*  621 */               str1 = str1 + "<span id=\"field" + paramInt1 + "span\" style=\"word-break:break-all;word-wrap:break-word;" + str12 + str4 + "\">";
/*  622 */               if (i1 == 1 && ("fieldIdHrmInfo_fieldId"
/*  623 */                 .equals(hashMap2.get(paramInt1 + "")) || "fieldIdDepInfo_fieldId"
/*  624 */                 .equals(hashMap2.get(paramInt1 + "")) || "fieldIdSubInfo_fieldId"
/*  625 */                 .equals(hashMap2.get(paramInt1 + "")) || "fieldIdFccInfo_fieldId"
/*  626 */                 .equals(hashMap2.get(paramInt1 + "")) || "fieldIdHrmInfo2_fieldId"
/*  627 */                 .equals(hashMap2.get(paramInt1 + "")) || "fieldIdDepInfo2_fieldId"
/*  628 */                 .equals(hashMap2.get(paramInt1 + "")) || "fieldIdSubInfo2_fieldId"
/*  629 */                 .equals(hashMap2.get(paramInt1 + "")) || "fieldIdFccInfo2_fieldId"
/*  630 */                 .equals(hashMap2.get(paramInt1 + "")))) {
/*  631 */                 str1 = str1 + fnaCommon.getRemainByFnaFieldType(paramInt1, m, Util.toScreenForWorkflow(paramString3), j, k, hashMap2, hashMap3, hashMap4, n, paramHashtable);
/*      */               
/*      */               }
/*      */               else {
/*      */                 
/*  636 */                 str1 = str1 + Util.toScreenForWorkflow(paramString3);
/*      */               } 
/*  638 */               str1 = str1 + "</span>";
/*  639 */               if (!"".equals(str7))
/*  640 */                 str1 = str1 + "<span id=\"field" + paramInt1 + "span_format\" style=\"word-break:break-all;word-wrap:break-word;" + str14 + str4 + "\">" + Util.toScreenForWorkflow(str8) + "</span>"; 
/*      */             } 
/*      */           } 
/*      */         } else {
/*  644 */           str1 = str1 + "<input " + str32 + " type=\"hidden\" datalength=\"" + i4 + "\" id=\"field" + paramInt1 + "\" name=\"field" + paramInt1 + "\" value=\"" + Util.toScreenForWorkflow(paramString3) + "\" />";
/*      */         } 
/*  646 */         if (!"".equals(str3) && paramInt2 != 4) {
/*  647 */           str1 = str1 + financialElement.getFinancialFieldStr(str3, "field" + paramInt1, paramString3, paramInt8, paramInt9, 1, paramInt2);
/*      */         }
/*      */       } else {
/*  650 */         String str24 = Util.null2String((String)paramHashtable.get("derecorderindex"));
/*  651 */         String str25 = "\"";
/*  652 */         if ("\"+rowindex+\"".equals(str24)) {
/*  653 */           str25 = "\\\"";
/*      */         }
/*  655 */         String str26 = Util.null2String((String)paramHashtable.get("trrigerdetailfield"));
/*  656 */         ArrayList arrayList = (ArrayList)paramHashtable.get("changedefieldsadd");
/*  657 */         int i5 = Util.getIntValue((String)paramHashtable.get("firstDetailFieldid"), 0);
/*  658 */         byte b = 0;
/*  659 */         if (i5 == paramInt1) {
/*  660 */           b = 10;
/*      */         }
/*  662 */         String str27 = "";
/*  663 */         if (paramInt6 == 0 && paramInt7 == 1 && paramInt8 == 1 && paramInt9 == 1 && "".equals(paramString3)) {
/*  664 */           str27 = "<img src='/images/BacoError_wev8.gif' align='absmiddle'>";
/*      */         }
/*  666 */         String str28 = "";
/*  667 */         String str29 = "";
/*  668 */         ArrayList<String> arrayList1 = (ArrayList)paramHashtable.get("sqlfieldidList");
/*  669 */         ArrayList<String> arrayList2 = (ArrayList)paramHashtable.get("sqlcontentList");
/*  670 */         if (arrayList1 != null && arrayList1.size() > 0) {
/*  671 */           for (byte b1 = 0; b1 < arrayList1.size(); b1++) {
/*  672 */             String str37 = Util.null2String(arrayList1.get(b1)).trim();
/*  673 */             String str38 = Util.null2String(arrayList2.get(b1)).trim();
/*  674 */             if (!"".equals(str38) && 
/*  675 */               str38.indexOf("$" + paramInt1 + "$") > -1) {
/*  676 */               str29 = str29 + str37 + ",";
/*      */             }
/*      */             
/*  679 */             if (str37.equals("" + paramInt1) && 
/*  680 */               "\"+rowindex+\"".equals(str24)) {
/*  681 */               str2 = str2 + "\t fieldAttrOperate.pageLoadInitValue('" + paramInt1 + "',rowindex);\n";
/*      */             }
/*      */           } 
/*      */           
/*  685 */           if (str29.length() > 0) {
/*  686 */             str28 = "fieldAttrOperate.doSqlFieldAjax(this,'" + str29.substring(0, str29.length() - 1) + "');";
/*      */           }
/*      */         } 
/*      */         
/*  690 */         String str30 = "";
/*  691 */         ArrayList<String> arrayList3 = (ArrayList)paramHashtable.get("datefieldidList");
/*  692 */         ArrayList<String> arrayList4 = (ArrayList)paramHashtable.get("datecontentList");
/*  693 */         if (arrayList3 != null && arrayList3.size() > 0) {
/*  694 */           for (byte b1 = 0; b1 < arrayList3.size(); b1++) {
/*  695 */             String str37 = Util.null2String(arrayList3.get(b1)).trim();
/*  696 */             String str38 = Util.null2String(arrayList4.get(b1)).trim();
/*  697 */             if (!"".equals(str38) && 
/*  698 */               str38.indexOf("$" + paramInt1 + "$") > -1) {
/*  699 */               str30 = str30 + "doFieldDate" + str37 + "(" + str24 + ");";
/*      */             }
/*      */             
/*  702 */             if (str37.equals("" + paramInt1)) {
/*  703 */               if ("\"+rowindex+\"".equals(str24)) {
/*  704 */                 str2 = str2 + "eval(\"doFieldDate" + paramInt1 + "(\"+rowindex+\");\");\n";
/*      */               } else {
/*  706 */                 str2 = str2 + "function getFieldDateAjaxDetail" + paramInt1 + "_" + str24 + "(){\n";
/*  707 */                 str2 = str2 + "\tdoFieldDate" + paramInt1 + "(" + str24 + ");\n";
/*  708 */                 str2 = str2 + "}\n";
/*      */ 
/*      */                 
/*  711 */                 str2 = str2 + "\tif (window.addEventListener){\n";
/*  712 */                 str2 = str2 + "\t    window.addEventListener(\"load\", getFieldDateAjaxDetail" + paramInt1 + "_" + str24 + ", false);\n";
/*  713 */                 str2 = str2 + "\t}else if (window.attachEvent){\n";
/*  714 */                 str2 = str2 + "\t    window.attachEvent(\"onload\", getFieldDateAjaxDetail" + paramInt1 + "_" + str24 + ");\n";
/*  715 */                 str2 = str2 + "\t}else{\n";
/*  716 */                 str2 = str2 + "\t    window.onload=getFieldDateAjaxDetail" + paramInt1 + "_" + str24 + ";\n";
/*  717 */                 str2 = str2 + "\t}\n";
/*      */               } 
/*      */             }
/*      */           } 
/*  721 */           if (str30.length() > 0) {
/*  722 */             str28 = str30;
/*      */           }
/*      */         } 
/*      */         
/*  726 */         ArrayList<String> arrayList5 = (ArrayList)paramHashtable.get("sapfieldidList");
/*  727 */         if (arrayList5 != null && arrayList5.size() > 0) {
/*  728 */           for (byte b1 = 0; b1 < arrayList5.size(); b1++) {
/*  729 */             String str37 = Util.null2String(arrayList5.get(b1));
/*  730 */             String str38 = str37.substring(0, str37.indexOf("-"));
/*  731 */             String str39 = str37.substring(str37.indexOf("-") + 1);
/*  732 */             if (("" + paramInt1).equals(str38)) {
/*  733 */               str28 = str28 + "doSAPField('" + str39 + "',this);";
/*      */             }
/*      */           } 
/*      */         }
/*      */         
/*  738 */         String str31 = "";
/*  739 */         if (str26.indexOf("field" + paramInt1) >= 0) {
/*  740 */           str31 = "datainputd('field" + paramInt1 + "_" + str24 + "');";
/*      */         }
/*      */         
/*  743 */         String str32 = " fieldtype='" + paramInt2 + "' ";
/*      */         
/*  745 */         String str33 = "calSum(" + paramInt5 + ");";
/*      */         
/*  747 */         String str34 = "checkinput2('field" + paramInt1 + "_" + str24 + "','field" + paramInt1 + "_" + str24 + "span',this.getAttribute('viewtype'));";
/*      */         
/*  749 */         String str35 = "";
/*  750 */         if (!"".equals(str7) && paramInt2 != 4) {
/*  751 */           str35 = str35 + "<input type='text' id='field" + paramInt1 + "_" + str24 + "_format' name='field" + paramInt1 + "_" + str24 + "_format' ";
/*  752 */           str35 = str35 + "class='Inputstyle' style='" + str13 + "' value='" + str8 + "' onfocus='showRealObj(this);' readonly />";
/*      */         } 
/*      */         
/*  755 */         String str36 = str25 + str31 + str28 + str11 + str6 + str15 + str16 + str25;
/*  756 */         if (paramInt2 == 1) {
/*  757 */           String str37 = "";
/*  758 */           if ("dt1_fieldIdJkmx_fieldId".equals(hashMap2.get(paramInt1 + "")) || "dt1_fieldIdTzmx_fieldId".equals(hashMap2.get(paramInt1 + "")) || "dt1_fieldIdXghklc_fieldId"
/*  759 */             .equals(hashMap2.get(paramInt1 + "")) || "dt1_fieldIdYfkmx_fieldId"
/*  760 */             .equals(hashMap2.get(paramInt1 + ""))) {
/*  761 */             str37 = "display:none;";
/*      */           }
/*  763 */           String str38 = "";
/*  764 */           if ("dt2_fieldIdJkdh_fieldId".equals(hashMap2.get(paramInt1 + "")) || "dt4_fieldIdYfkdh_fieldId".equals(hashMap2.get(paramInt1 + ""))) {
/*  765 */             str38 = "readonly='readonly'";
/*      */           }
/*  767 */           if (paramInt8 == 1 && paramInt6 == 0) {
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */             
/*  775 */             str1 = str1 + "<input " + str32 + " " + str38 + " class='Inputstyle' viewtype='" + paramInt9 + "' datatype='text' type='text' temptitle='" + paramString2 + "' name='field" + paramInt1 + "_" + str24 + "' id='field" + paramInt1 + "_" + str24 + "'  style='" + str37 + "width:" + ((i2 == 2) ? 90 : (85 - b)) + "%;" + str12 + str4 + "' value=" + str25 + Util.toScreenForWorkflow(paramString3) + str25 + "  onBlur='" + str10 + str5 + str18 + "' " + str9 + "  onpropertychange=" + str36 + " _listener=" + str36 + " onfocus=\\\"" + str17 + "\\\" onclick=\\\"" + str19 + "\\\"  ondblclick=\\\"" + str20 + "\\\" onmouseover=\\\"" + str21 + "\\\" onmouseout=\\\"" + str22 + "\\\"  onChange=" + str25 + str34 + "checkLength('field" + paramInt1 + "_" + str24 + "','" + paramInt3 + "','" + paramString2 + "','" + SystemEnv.getHtmlLabelName(20246, i3) + "','" + SystemEnv.getHtmlLabelName(20247, i3) + "');" + str25 + ">";
/*  776 */             str1 = str1 + str35;
/*  777 */             if ("dt1_fieldIdJkmx_fieldId".equals(hashMap2.get(paramInt1 + "")) || "dt1_fieldIdTzmx_fieldId".equals(hashMap2.get(paramInt1 + ""))) {
/*  778 */               str1 = str1 + FnaCommon.getBtnTzmx(paramInt1, str24, i3, m);
/*  779 */             } else if ("dt1_fieldIdYfkmx_fieldId".equals(hashMap2.get(paramInt1 + ""))) {
/*  780 */               str1 = str1 + FnaCommon.getBtnTzmxAdvance(paramInt1, str24, i3, m);
/*  781 */             } else if ("dt1_fieldIdXghklc_fieldId".equals(hashMap2.get(paramInt1 + ""))) {
/*  782 */               str1 = str1 + FnaCommon.getBtnXghklc(paramInt1, str24, i3, m);
/*      */             } 
/*  784 */             str1 = str1 + "<span id='field" + paramInt1 + "_" + str24 + "span' style='" + str4 + "'>";
/*  785 */             if (i1 == 1 && ("fieldIdHrmInfo_fieldId"
/*  786 */               .equals(hashMap2.get(paramInt1 + "")) || "fieldIdDepInfo_fieldId"
/*  787 */               .equals(hashMap2.get(paramInt1 + "")) || "fieldIdSubInfo_fieldId"
/*  788 */               .equals(hashMap2.get(paramInt1 + "")) || "fieldIdFccInfo_fieldId"
/*  789 */               .equals(hashMap2.get(paramInt1 + "")) || "fieldIdHrmInfo2_fieldId"
/*  790 */               .equals(hashMap2.get(paramInt1 + "")) || "fieldIdDepInfo2_fieldId"
/*  791 */               .equals(hashMap2.get(paramInt1 + "")) || "fieldIdSubInfo2_fieldId"
/*  792 */               .equals(hashMap2.get(paramInt1 + "")) || "fieldIdFccInfo2_fieldId"
/*  793 */               .equals(hashMap2.get(paramInt1 + "")))) {
/*  794 */               str1 = str1 + fnaCommon.getRemainByFnaFieldType(paramInt1, m, str27, j, k, hashMap2, hashMap3, hashMap4, n, paramHashtable);
/*      */ 
/*      */             
/*      */             }
/*  798 */             else if (!"dt1_fieldIdJkmx_fieldId".equals(hashMap2.get(paramInt1 + "")) && !"dt1_fieldIdTzmx_fieldId".equals(hashMap2.get(paramInt1 + "")) && 
/*  799 */               !"dt1_fieldIdXghklc_fieldId".equals(hashMap2.get(paramInt1 + "")) && 
/*  800 */               !"dt1_fieldIdYfkmx_fieldId".equals(hashMap2.get(paramInt1 + ""))) {
/*      */               
/*  802 */               str1 = str1 + str27;
/*      */             } 
/*  804 */             str1 = str1 + "</span>";
/*  805 */             if (arrayList.indexOf("" + paramInt1) >= 0) {
/*  806 */               str1 = str1 + "<input type='hidden' name='oldfieldview" + paramInt1 + "_" + str24 + "' value=" + str25 + "" + (paramInt7 + paramInt8 + paramInt9) + str25 + " />";
/*      */             }
/*      */           } else {
/*  809 */             if ("dt1_fieldIdJkmx_fieldId".equals(hashMap2.get(paramInt1 + "")) || "dt1_fieldIdTzmx_fieldId".equals(hashMap2.get(paramInt1 + ""))) {
/*  810 */               str1 = str1 + FnaCommon.getBtnTzmx(paramInt1, str24, i3, m);
/*  811 */             } else if ("dt1_fieldIdYfkmx_fieldId".equals(hashMap2.get(paramInt1 + ""))) {
/*  812 */               str1 = str1 + FnaCommon.getBtnTzmxAdvance(paramInt1, str24, i3, m);
/*  813 */             } else if ("dt1_fieldIdXghklc_fieldId".equals(hashMap2.get(paramInt1 + ""))) {
/*  814 */               str1 = str1 + FnaCommon.getBtnXghklc(paramInt1, str24, i3, m);
/*      */             } 
/*  816 */             str1 = str1 + "<span id='field" + paramInt1 + "_" + str24 + "span' style='" + str12 + str14 + str4 + "'>";
/*  817 */             if (i1 == 1 && ("fieldIdHrmInfo_fieldId"
/*  818 */               .equals(hashMap2.get(paramInt1 + "")) || "fieldIdDepInfo_fieldId"
/*  819 */               .equals(hashMap2.get(paramInt1 + "")) || "fieldIdSubInfo_fieldId"
/*  820 */               .equals(hashMap2.get(paramInt1 + "")) || "fieldIdFccInfo_fieldId"
/*  821 */               .equals(hashMap2.get(paramInt1 + "")) || "fieldIdHrmInfo2_fieldId"
/*  822 */               .equals(hashMap2.get(paramInt1 + "")) || "fieldIdDepInfo2_fieldId"
/*  823 */               .equals(hashMap2.get(paramInt1 + "")) || "fieldIdSubInfo2_fieldId"
/*  824 */               .equals(hashMap2.get(paramInt1 + "")) || "fieldIdFccInfo2_fieldId"
/*  825 */               .equals(hashMap2.get(paramInt1 + "")))) {
/*  826 */               str1 = str1 + fnaCommon.getRemainByFnaFieldType(paramInt1, m, Util.toScreenForWorkflow("".equals(str7) ? paramString3 : str8), j, k, hashMap2, hashMap3, hashMap4, n, paramHashtable);
/*      */ 
/*      */             
/*      */             }
/*  830 */             else if (!"dt1_fieldIdJkmx_fieldId".equals(hashMap2.get(paramInt1 + "")) && !"dt1_fieldIdTzmx_fieldId".equals(hashMap2.get(paramInt1 + "")) && 
/*  831 */               !"dt1_fieldIdXghklc_fieldId".equals(hashMap2.get(paramInt1 + "")) && 
/*  832 */               !"dt1_fieldIdYfkmx_fieldId".equals(hashMap2.get(paramInt1 + ""))) {
/*      */               
/*  834 */               str1 = str1 + Util.toScreenForWorkflow("".equals(str7) ? paramString3 : str8);
/*      */             } 
/*  836 */             str1 = str1 + "</span>";
/*  837 */             if (!"".equals(str7)) {
/*  838 */               str1 = str1 + "<span id='field" + paramInt1 + "_" + str24 + "span_format' style='" + str14 + str4 + "'>";
/*  839 */               str1 = str1 + Util.toScreenForWorkflow("".equals(str7) ? paramString3 : str8);
/*  840 */               str1 = str1 + "</span>";
/*      */             } 
/*  842 */             str1 = str1 + "<input " + str32 + " type='hidden' datatype='text' class='Inputstyle' id='field" + paramInt1 + "_" + str24 + "' name='field" + paramInt1 + "_" + str24 + "' value=" + str25 + Util.toScreenForWorkflow(paramString3) + str25 + " " + str9 + " onpropertychange=" + str36 + " _listener=" + str36 + " />";
/*      */           }
/*      */         
/*  845 */         } else if (paramInt2 == 2) {
/*  846 */           String str = "";
/*  847 */           if (("dt2_fieldIdDnxh_fieldId".equals(hashMap2.get(paramInt1 + "")) || "dt4_fieldIdDnxh_fieldId".equals(hashMap2.get(paramInt1 + ""))) && 
/*  848 */             paramInt8 == 1 && paramInt6 == 0) {
/*  849 */             str = "display:none;";
/*      */           }
/*      */           
/*  852 */           if (paramInt8 == 1 && paramInt6 == 0) {
/*      */             
/*  854 */             str1 = str1 + "<input " + str32 + " class='Inputstyle' viewtype='" + paramInt9 + "' datatype='int' temptitle='" + paramString2 + "' type='text' name='field" + paramInt1 + "_" + str24 + "' id='field" + paramInt1 + "_" + str24 + "' style='" + str + "width:75%;" + str12 + str4 + "' value='" + paramString3 + "' onKeyPress='ItemCount_KeyPress()' onBlur='" + str33 + str10 + str5 + "' " + str9 + " onpropertychange=" + str36 + " _listener=" + str36 + " onChange=" + str25 + "checkcount1(this);checkItemScale(this,'" + SystemEnv.getHtmlLabelName(31181, i3).replace("12", "9") + "',-999999999,999999999);" + str34 + str33 + str25 + ">";
/*  855 */             str1 = str1 + str35;
/*  856 */             if ("dt2_fieldIdDnxh_fieldId".equals(hashMap2.get(paramInt1 + ""))) {
/*  857 */               int i6 = Util.getIntValue((String)hashMap7.get("jklc"), 0);
/*  858 */               String str37 = Util.null2String((String)hashMap7.get("dnxhShowName"));
/*  859 */               str1 = str1 + FnaCommon.getBtnRequestDtlBrowser(i6, str37, paramInt1, str24, str4, paramInt9);
/*  860 */             } else if ("dt4_fieldIdDnxh_fieldId".equals(hashMap2.get(paramInt1 + ""))) {
/*  861 */               int i6 = Util.getIntValue((String)hashMap9.get("yfklc"), 0);
/*  862 */               String str37 = Util.null2String((String)hashMap9.get("dnxhShowName"));
/*  863 */               str1 = str1 + FnaCommon.getBtnAdvanceRequestDtlBrowser(i6, str37, paramInt1, str24, str4, paramInt9);
/*      */             } else {
/*  865 */               str1 = str1 + "<span id='field" + paramInt1 + "_" + str24 + "span' style='" + str4 + "'>" + str27 + "</span>";
/*      */             } 
/*  867 */             if (arrayList.indexOf("" + paramInt1) >= 0) {
/*  868 */               str1 = str1 + "<input type='hidden' name='oldfieldview" + paramInt1 + "_" + str24 + "' value='" + (paramInt7 + paramInt8 + paramInt9) + "' />";
/*      */             }
/*      */           } else {
/*  871 */             str1 = str1 + "<span id='field" + paramInt1 + "_" + str24 + "span' style='" + str12 + str14 + str4 + "'>";
/*  872 */             if ("dt2_fieldIdDnxh_fieldId".equals(hashMap2.get(paramInt1 + ""))) {
/*  873 */               str1 = str1 + Util.null2String((String)hashMap7.get("dnxhShowName"));
/*  874 */             } else if ("dt4_fieldIdDnxh_fieldId".equals(hashMap2.get(paramInt1 + ""))) {
/*  875 */               str1 = str1 + Util.null2String((String)hashMap9.get("dnxhShowName"));
/*      */             } else {
/*  877 */               str1 = str1 + ("".equals(str7) ? paramString3 : str8);
/*      */             } 
/*  879 */             str1 = str1 + "</span>";
/*  880 */             if (!"".equals(str7)) {
/*  881 */               str1 = str1 + "<span id='field" + paramInt1 + "_" + str24 + "span_format' style='" + str14 + str4 + "'>";
/*  882 */               str1 = str1 + ("".equals(str7) ? paramString3 : str8);
/*  883 */               str1 = str1 + "</span>";
/*      */             } 
/*  885 */             str1 = str1 + "<input " + str32 + " type='hidden' datatype='int' class='Inputstyle' id='field" + paramInt1 + "_" + str24 + "' name='field" + paramInt1 + "_" + str24 + "' value='" + paramString3 + "' " + str9 + " onChange=" + str25 + str33 + str25 + " onpropertychange=" + str36 + " _listener=" + str36 + " />";
/*      */           } 
/*  887 */         } else if (paramInt2 == 3 || paramInt2 == 5) {
/*  888 */           String str37 = "";
/*  889 */           if (paramInt2 == 3) {
/*  890 */             if (paramInt2 == 3 && ("dt2_fieldIdJkje_fieldId".equals(hashMap2.get(paramInt1 + "")) || "dt2_fieldIdYhje_fieldId"
/*  891 */               .equals(hashMap2.get(paramInt1 + "")) || "dt2_fieldIdSpzje_fieldId"
/*  892 */               .equals(hashMap2.get(paramInt1 + "")) || "dt2_fieldIdWhje_fieldId"
/*  893 */               .equals(hashMap2.get(paramInt1 + "")))) {
/*  894 */               str37 = "readonly='readonly'";
/*  895 */               if (j > 0 && m > 0) {
/*  896 */                 if ("dt2_fieldIdJkje_fieldId".equals(hashMap2.get(paramInt1 + ""))) {
/*  897 */                   paramString3 = Util.null2String((String)hashMap8.get("jkje"));
/*  898 */                 } else if ("dt2_fieldIdYhje_fieldId".equals(hashMap2.get(paramInt1 + ""))) {
/*  899 */                   paramString3 = Util.null2String((String)hashMap8.get("yhje"));
/*  900 */                 } else if ("dt2_fieldIdSpzje_fieldId".equals(hashMap2.get(paramInt1 + ""))) {
/*  901 */                   paramString3 = Util.null2String((String)hashMap8.get("spzje"));
/*  902 */                 } else if ("dt2_fieldIdWhje_fieldId".equals(hashMap2.get(paramInt1 + ""))) {
/*  903 */                   paramString3 = Util.null2String((String)hashMap8.get("whje"));
/*      */                 } 
/*      */                 try {
/*  906 */                   if (i2 == 2 && paramHashtable.containsKey("_format") && "".equals(str3) && 
/*  907 */                     !"".equals(str7)) {
/*  908 */                     str8 = formatFieldValue.FormatValue(paramString3, str7, 1, paramInt2);
/*      */                   }
/*      */                 }
/*  911 */                 catch (Exception exception) {
/*  912 */                   writeLog(exception);
/*      */                 } 
/*      */               } 
/*  915 */             } else if (paramInt2 == 3 && ("dt4_fieldIdYfkje_fieldId".equals(hashMap2.get(paramInt1 + "")) || "dt4_fieldIdYhje_fieldId"
/*  916 */               .equals(hashMap2.get(paramInt1 + "")) || "dt4_fieldIdSpzje_fieldId"
/*  917 */               .equals(hashMap2.get(paramInt1 + "")) || "dt4_fieldIdWhje_fieldId"
/*  918 */               .equals(hashMap2.get(paramInt1 + "")))) {
/*  919 */               str37 = "readonly='readonly'";
/*  920 */               if (j > 0 && m > 0) {
/*  921 */                 if ("dt4_fieldIdYfkje_fieldId".equals(hashMap2.get(paramInt1 + ""))) {
/*  922 */                   paramString3 = Util.null2String((String)hashMap10.get("Yfkje"));
/*  923 */                 } else if ("dt4_fieldIdYhje_fieldId".equals(hashMap2.get(paramInt1 + ""))) {
/*  924 */                   paramString3 = Util.null2String((String)hashMap10.get("yhje"));
/*  925 */                 } else if ("dt4_fieldIdSpzje_fieldId".equals(hashMap2.get(paramInt1 + ""))) {
/*  926 */                   paramString3 = Util.null2String((String)hashMap10.get("spzje"));
/*  927 */                 } else if ("dt4_fieldIdWhje_fieldId".equals(hashMap2.get(paramInt1 + ""))) {
/*  928 */                   paramString3 = Util.null2String((String)hashMap10.get("whje"));
/*      */                 } 
/*      */                 try {
/*  931 */                   if (i2 == 2 && paramHashtable.containsKey("_format") && "".equals(str3) && 
/*  932 */                     !"".equals(str7)) {
/*  933 */                     str8 = formatFieldValue.FormatValue(paramString3, str7, 1, paramInt2);
/*      */                   }
/*      */                 }
/*  936 */                 catch (Exception exception) {
/*  937 */                   writeLog(exception);
/*      */                 } 
/*      */               } 
/*      */             } 
/*      */           }
/*      */           
/*  943 */           String str38 = "";
/*  944 */           if (paramInt2 == 3) {
/*  945 */             paramString3 = Util.toDecimalDigits(paramString3, i4);
/*  946 */           } else if (paramInt2 == 5) {
/*  947 */             str38 = str38 + " datavaluetype='5'";
/*  948 */             paramString3 = Util.toDecimalDigits(paramString3, i4);
/*      */           } 
/*      */           
/*  951 */           str38 = str38 + " datalength='" + i4 + "' ";
/*      */           
/*  953 */           if (paramInt8 == 1 && paramInt6 == 0) {
/*  954 */             str1 = str1 + "<input " + str32 + " " + str37 + " class='Inputstyle' viewtype='" + paramInt9 + "' " + str38 + " valuetype='5' datatype='float' temptitle='" + paramString2 + "' type='text' name='field" + paramInt1 + "_" + str24 + "' id='field" + paramInt1 + "_" + str24 + "' style='width:75%;" + str12 + str4 + "' value='" + paramString3 + "' " + str9 + " onpropertychange=" + str36 + " _listener=" + str36 + " onKeyPress=" + str25 + "ItemDecimal_KeyPress(this.name, 15, " + i4 + ")" + str25 + " ";
/*  955 */             if (paramInt2 == 5) {
/*      */               
/*  957 */               str1 = str1 + " onblur='changeToThousands2(this.name," + i4 + ");" + str33 + str10 + str5 + "' ";
/*  958 */               str1 = str1 + " onfocus='changeToNormalFormat(this.name)' ";
/*  959 */               str1 = str1 + " onChange=" + str25 + "checkFloat(this);" + str34 + str33 + str25 + ">";
/*      */             } else {
/*  961 */               String str39 = "";
/*  962 */               String str40 = "";
/*  963 */               if (("dt1_fieldIdJkje_fieldId".equals(hashMap2.get(paramInt1 + "")) || "dt1_fieldIdHkje_fieldId".equals(hashMap2.get(paramInt1 + ""))) && paramInt2 == 3) {
/*  964 */                 str39 = "try{window.fnaBorrowAmountDtl1_onclick(this);}catch(ex1){}";
/*  965 */                 str40 = "try{window.fnaBorrowAmountDtl1_onFocus_onMouseover(this);}catch(ex1){}";
/*  966 */                 str1 = str1 + " _dataBaseValue='" + paramString3 + "' ";
/*  967 */                 str1 = str1 + " readonly='readonly' ";
/*  968 */               } else if ("dt1_fieldIdYfkje_fieldId".equals(hashMap2.get(paramInt1 + "")) && paramInt2 == 3) {
/*  969 */                 str39 = "try{window.fnaAdvanceAmountDtl1_onclick(this);}catch(ex1){}";
/*  970 */                 str40 = "try{window.fnaAdvanceAmountDtl1_onFocus_onMouseover(this);}catch(ex1){}";
/*  971 */                 str1 = str1 + " _dataBaseValue='" + paramString3 + "' ";
/*  972 */                 str1 = str1 + " readonly='readonly' ";
/*      */               } 
/*  974 */               str1 = str1 + " onclick='" + str39 + "' onmouseover='" + str40 + "' ";
/*  975 */               str1 = str1 + " onfocus='changeToNormalFormat(this.name);" + str40 + "' onblur='changeToThousands2(this.name," + i4 + ");" + str33 + str10 + str5 + "' ";
/*  976 */               str1 = str1 + " onChange=" + str25 + "checkFloat(this);" + str34 + str33 + str25 + ">";
/*      */             } 
/*  978 */             str1 = str1 + str35;
/*  979 */             str1 = str1 + "<span id='field" + paramInt1 + "_" + str24 + "span' style='" + str4 + "'>" + str27 + "</span>";
/*  980 */             if (arrayList.indexOf("" + paramInt1) >= 0)
/*      */             {
/*  982 */               str1 = str1 + "<input " + str38 + " type='hidden' name='oldfieldview" + paramInt1 + "_" + str24 + "' value='" + (paramInt7 + paramInt8 + paramInt9) + "' />";
/*      */             }
/*      */           } else {
/*      */             
/*  986 */             if (paramInt2 == 5) {
/*  987 */               if (!paramString3.equals("")) {
/*  988 */                 String[] arrayOfString = paramString3.split("\\.");
/*  989 */                 if (arrayOfString.length < 2 && i4 > 0) {
/*  990 */                   String str = "";
/*  991 */                   for (byte b1 = 0; b1 < i4; b1++) {
/*  992 */                     str = str + "0";
/*      */                   }
/*  994 */                   paramString3 = paramString3 + "." + str;
/*      */                 } 
/*      */               } 
/*      */             } else {
/*  998 */               String str = "";
/*  999 */               if (paramString3.matches("-*\\d+\\.?\\d*") && paramInt2 == 5) {
/* 1000 */                 DecimalFormat decimalFormat = new DecimalFormat("###,###.##");
/* 1001 */                 str = decimalFormat.format(Double.parseDouble(paramString3)) + "";
/* 1002 */                 str = Util.toDecimalDigits(str, i4);
/*      */               } else {
/*      */                 
/* 1005 */                 str = Util.toDecimalDigits(paramString3, i4);
/*      */               } 
/* 1007 */               paramString3 = str;
/*      */             } 
/* 1009 */             str1 = str1 + "<span id='field" + paramInt1 + "_" + str24 + "span' style='" + str12 + str14 + str4 + "'>" + ("".equals(str7) ? paramString3 : str8) + "</span>";
/* 1010 */             if (!"".equals(str7)) {
/* 1011 */               str1 = str1 + "<span id='field" + paramInt1 + "_" + str24 + "span_format' style='" + str14 + str4 + "'>" + ("".equals(str7) ? paramString3 : str8) + "</span>";
/*      */             }
/* 1013 */             str1 = str1 + "<input " + str32 + " type='hidden' " + str38 + " datatype='float' class='Inputstyle' id='field" + paramInt1 + "_" + str24 + "' name='field" + paramInt1 + "_" + str24 + "' value='" + paramString3 + "' " + str9 + " onChange=" + str25 + str33 + str25 + " onpropertychange=" + str36 + " _listener=" + str36 + " />";
/*      */           } 
/* 1015 */         } else if (paramInt2 == 4) {
/* 1016 */           str36 = str25 + str31 + str28 + str25;
/* 1017 */           int i6 = str23.indexOf(",");
/* 1018 */           if (i6 > -1) {
/* 1019 */             i4 = Util.getIntValue(str23.substring(i6 + 1, str23.length() - 1), 2);
/*      */           }
/* 1021 */           if (paramInt8 == 1 && paramInt6 == 0) {
/* 1022 */             str1 = str1 + "<input class='Inputstyle' temptitle='" + paramString2 + "' viewtype='" + paramInt9 + "' value='" + paramString3 + "' datatype='float' type='text' id='field_lable" + paramInt1 + "_" + str24 + "' name='field_lable" + paramInt1 + "_" + str24 + "' style='width:80%' onKeyPress=" + str25 + "ItemDecimal_KeyPress('field_lable" + paramInt1 + "_" + str24 + "',15," + i4 + ")" + str25 + "  onfocus=" + str25 + "getNumber('" + paramInt1 + "_" + str24 + "')" + str25 + " onBlur=" + str25 + "numberToChinese('" + paramInt1 + "_" + str24 + "');checkinput3(field_lable" + paramInt1 + "_" + str24 + ",field" + paramInt1 + "_" + str24 + "span,this.getAttribute('viewtype'));" + str33 + str25 + " />";
/*      */             
/* 1024 */             str1 = str1 + "<span id='field" + paramInt1 + "_" + str24 + "span'>" + str27 + "</span>";
/* 1025 */             if (arrayList.indexOf("" + paramInt1) >= 0) {
/* 1026 */               str1 = str1 + "<input type='hidden'   name='oldfieldview" + paramInt1 + "_" + str24 + "' value='" + (paramInt7 + paramInt8 + paramInt9) + "' />";
/*      */             }
/*      */           } else {
/* 1029 */             str1 = str1 + "<input class='Inputstyle'  style='width:80%' value='" + paramString3 + "' datatype='float' type='text' _printflag='1' disabled='true' id='field_lable" + paramInt1 + "_" + str24 + "' name='field_lable" + paramInt1 + "_" + str24 + "'>";
/* 1030 */             str1 = str1 + "<span id='field" + paramInt1 + "_" + str24 + "span'></span>";
/*      */           } 
/* 1032 */           str1 = str1 + "<input " + str32 + " type='hidden' datatype='float' datalength='" + i4 + "' viewtype='" + paramInt9 + "' value='" + paramString3 + "' id='field" + paramInt1 + "_" + str24 + "' name='field" + paramInt1 + "_" + str24 + "' onpropertychange=" + str36 + " _listener=" + str36 + " />";
/* 1033 */           if (!"\"+rowindex+\"".equals(str24)) {
/* 1034 */             str1 = str1 + "\n<script language=\"javascript\">";
/* 1035 */             str1 = str1 + "\ntry{";
/* 1036 */             str1 = str1 + "\n\t$G(\"field_lable" + paramInt1 + "_\"+" + str24 + ").value  = numberChangeToChinese(\"" + paramString3 + "\");";
/* 1037 */             str1 = str1 + "\n}catch(e){}";
/* 1038 */             str1 = str1 + "\n</script>";
/*      */           } 
/*      */         } 
/* 1041 */         if (!"".equals(str3) && paramInt2 != 4) {
/* 1042 */           str1 = str1 + financialElement.getFinancialFieldStr(str3, "field" + paramInt1 + "_" + str24, paramString3, paramInt8, paramInt9, 1, paramInt2);
/*      */         }
/*      */       } 
/* 1045 */     } catch (Exception exception) {
/* 1046 */       str1 = "";
/* 1047 */       writeLog(exception);
/*      */     } 
/* 1049 */     hashtable.put("jsStr", str2);
/* 1050 */     hashtable.put("inputStr", str1);
/* 1051 */     return hashtable;
/*      */   }
/*      */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/field/InputElement.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */