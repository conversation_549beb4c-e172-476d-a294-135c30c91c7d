/*     */ package weaver.workflow.field;
/*     */ 
/*     */ import weaver.cache.CacheBase;
/*     */ import weaver.cache.CacheColumn;
/*     */ import weaver.cache.CacheColumnType;
/*     */ import weaver.cache.PKColumn;
/*     */ import weaver.general.Util;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class FieldComInfo
/*     */   extends CacheBase
/*     */ {
/*  25 */   protected static String TABLE_NAME = "workflow_formdict";
/*     */   
/*  27 */   protected static String TABLE_WHERE = null;
/*     */   
/*  29 */   protected static String TABLE_ORDER = "fieldname";
/*     */   
/*     */   @PKColumn(type = CacheColumnType.NUMBER)
/*  32 */   protected static String PK_NAME = "id";
/*     */   
/*     */   @CacheColumn
/*     */   protected static int fieldname;
/*     */   
/*     */   @CacheColumn
/*     */   protected static int fielddbtype;
/*     */   
/*     */   @CacheColumn
/*     */   protected static int fieldhtmltype;
/*     */   
/*     */   @CacheColumn
/*     */   protected static int type;
/*     */   
/*     */   @CacheColumn
/*     */   protected static int description;
/*     */   
/*     */   @CacheColumn
/*     */   protected static int imgwidth;
/*     */   @CacheColumn
/*     */   protected static int imgheight;
/*     */   @CacheColumn
/*     */   protected static int textheight;
/*     */   
/*     */   public int getFieldNum() {
/*  57 */     return size();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public boolean next(String paramString) {
/*  68 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getFieldid() {
/*  77 */     return (String)getRowValue(0);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getFieldname() {
/*  86 */     return (String)getRowValue(fieldname);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getFieldname(String paramString) {
/*  97 */     return (String)getValue(fieldname, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getFielddbtype(String paramString) {
/* 108 */     return (String)getValue(fielddbtype, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getFieldhtmltype(String paramString) {
/* 119 */     return (String)getValue(fieldhtmltype, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getFieldType(String paramString) {
/* 130 */     return (String)getValue(type, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getFieldDesc(String paramString) {
/* 141 */     return (String)getValue(description, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getImgWidth() {
/* 150 */     return Util.getIntValue((String)getRowValue(imgwidth));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getImgWidth(String paramString) {
/* 160 */     return Util.getIntValue((String)getValue(imgwidth, paramString), 0);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getImgHeight() {
/* 169 */     return Util.getIntValue((String)getRowValue(imgheight));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getImgHeight(String paramString) {
/* 179 */     return Util.getIntValue((String)getValue(imgheight, paramString), 0);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getImgNumPreRow() {
/* 188 */     return Util.getIntValue((String)getRowValue(textheight));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getImgNumPreRow(String paramString) {
/* 198 */     return Util.getIntValue((String)getValue(textheight, paramString), 0);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void removeFieldCache() {
/* 206 */     removeCache();
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/field/FieldComInfo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */