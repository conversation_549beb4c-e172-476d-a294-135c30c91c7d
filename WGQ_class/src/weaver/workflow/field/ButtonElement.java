/*      */ package weaver.workflow.field;
/*      */ 
/*      */ import java.net.URLEncoder;
/*      */ import java.util.ArrayList;
/*      */ import java.util.HashMap;
/*      */ import java.util.Hashtable;
/*      */ import java.util.List;
/*      */ import javax.servlet.http.HttpServletRequest;
/*      */ import javax.servlet.http.HttpSession;
/*      */ import org.json.JSONObject;
/*      */ import weaver.common.util.taglib.BrowserTag;
/*      */ import weaver.conn.RecordSet;
/*      */ import weaver.cpt.capital.CapitalComInfo;
/*      */ import weaver.crm.Maint.CustomerInfoComInfo;
/*      */ import weaver.docs.docs.DocComInfo;
/*      */ import weaver.docs.senddoc.DocReceiveUnitComInfo;
/*      */ import weaver.fna.general.FnaCommon;
/*      */ import weaver.fna.maintenance.BudgetfeeTypeComInfo;
/*      */ import weaver.fna.maintenance.FnaSystemSetComInfo;
/*      */ import weaver.formmode.tree.CustomTreeUtil;
/*      */ import weaver.general.BaseBean;
/*      */ import weaver.general.StaticObj;
/*      */ import weaver.general.Util;
/*      */ import weaver.general.browserData.BrowserManager;
/*      */ import weaver.hrm.User;
/*      */ import weaver.hrm.attendance.domain.HrmLeaveTypeColor;
/*      */ import weaver.hrm.attendance.manager.HrmLeaveTypeColorManager;
/*      */ import weaver.hrm.company.DepartmentComInfo;
/*      */ import weaver.hrm.company.SubCompanyComInfo;
/*      */ import weaver.hrm.companyvirtual.DepartmentVirtualComInfo;
/*      */ import weaver.hrm.companyvirtual.SubCompanyVirtualComInfo;
/*      */ import weaver.hrm.job.JobTitlesComInfo;
/*      */ import weaver.hrm.resource.ResourceComInfo;
/*      */ import weaver.interfaces.workflow.browser.Browser;
/*      */ import weaver.interfaces.workflow.browser.BrowserBean;
/*      */ import weaver.proj.Maint.ProjectInfoComInfo;
/*      */ import weaver.systeminfo.SystemEnv;
/*      */ import weaver.workflow.exceldesign.FormatFieldValue;
/*      */ import weaver.workflow.request.ResourceConditionManager;
/*      */ import weaver.workflow.request.WorkflowJspBean;
/*      */ import weaver.workflow.workflow.WorkflowRequestComInfo;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ public class ButtonElement
/*      */   extends BaseBean
/*      */   implements HtmlElement
/*      */ {
/*      */   public static void main(String[] paramArrayOfString) {}
/*      */   
/*      */   public Hashtable getHtmlElementString(int paramInt1, String paramString1, int paramInt2, String paramString2, int paramInt3, int paramInt4, int paramInt5, String paramString3, int paramInt6, int paramInt7, int paramInt8, int paramInt9, User paramUser, Hashtable<String, String> paramHashtable) {
/*   71 */     Hashtable<Object, Object> hashtable = new Hashtable<>();
/*   72 */     String str1 = "";
/*   73 */     String str2 = "";
/*   74 */     String str3 = "";
/*   75 */     String str4 = "";
/*   76 */     boolean bool = false;
/*      */     try {
/*   78 */       int i = Util.getIntValue((String)paramHashtable.get("workflowid"));
/*   79 */       int j = Util.getIntValue((String)paramHashtable.get("detailNumber"));
/*   80 */       int k = Util.getIntValue((String)paramHashtable.get("requestid"), 0);
/*   81 */       int m = Util.getIntValue((String)paramHashtable.get("detailRecordId"), 0);
/*   82 */       int n = Util.getIntValue((String)paramHashtable.get("languageId"), 0);
/*      */       
/*   84 */       HashMap<Object, Object> hashMap1 = (HashMap)paramHashtable.get("_isEnableFnaWfHm_FnaCommon.getIsEnableFnaWfHm_workflowid=" + i + "__requestId=" + k);
/*   85 */       if (hashMap1 == null) {
/*   86 */         hashMap1 = new HashMap<>();
/*      */       }
/*   88 */       HashMap<Object, Object> hashMap2 = (HashMap)paramHashtable.get("_isEnableFnaWfHm_fnaBudgetControl.getFnaWfFieldInfo4Expense_workflowid=" + i + "__requestId=" + k);
/*   89 */       if (hashMap2 == null) {
/*   90 */         hashMap2 = new HashMap<>();
/*      */       }
/*   92 */       HashMap<Object, Object> hashMap3 = (HashMap)paramHashtable.get("_isEnableFnaWfHm_FnaCommon.qryFnaExpenseRequestRecord_workflowid=" + i + "__requestId=" + k);
/*   93 */       if (hashMap3 == null) {
/*   94 */         hashMap3 = new HashMap<>();
/*      */       }
/*      */       
/*   97 */       if ("fieldIdOrgId_fieldId".equals(hashMap2.get(paramInt1 + "")) || "fieldIdOrgId2_fieldId".equals(hashMap2.get(paramInt1 + ""))) {
/*   98 */         HashMap<Object, Object> hashMap = new HashMap<>();
/*   99 */         paramInt2 = FnaCommon.getOrgBtnTypeByFnaFieldType(paramInt1, paramInt2, m, k, hashMap2, hashMap3, n, paramHashtable, hashMap);
/*      */ 
/*      */ 
/*      */         
/*  103 */         if (hashMap.containsKey("newOrgIdDefValue")) {
/*  104 */           paramString3 = Util.null2String((String)hashMap.get("newOrgIdDefValue"));
/*      */         }
/*      */       } 
/*      */       
/*  108 */       int i1 = 0;
/*  109 */       if (paramHashtable.containsKey("version")) {
/*  110 */         i1 = Util.getIntValue(paramHashtable.get("version").toString(), 0);
/*      */       }
/*  112 */       int i2 = paramUser.getLanguage();
/*      */       
/*  114 */       String str5 = "", str6 = "", str7 = "", str8 = "";
/*  115 */       if (paramHashtable.containsKey("_format") && (paramInt2 == 2 || paramInt2 == 19)) {
/*  116 */         str5 = (String)paramHashtable.get("_format");
/*  117 */         if (!"".equals(str5)) {
/*  118 */           JSONObject jSONObject = new JSONObject("{" + str5 + "}");
/*  119 */           int i9 = Util.getIntValue(jSONObject.getString("numberType"), -1);
/*  120 */           if ((paramInt2 == 2 && i9 == 3) || (paramInt2 == 19 && i9 == 4)) {
/*  121 */             FormatFieldValue formatFieldValue = new FormatFieldValue();
/*      */             
/*  123 */             str6 = formatFieldValue.FormatValue(paramString3, str5, 3, paramInt2, i2);
/*      */             
/*  125 */             str7 = " _format=" + str5 + " ";
/*      */             
/*  127 */             str8 = ";showFormatObj(this,2," + i2 + ")";
/*      */           } else {
/*  129 */             str5 = "";
/*      */           } 
/*      */         } 
/*      */       } 
/*      */       
/*  134 */       if ("NULL".equals(paramString3) && 226 == paramInt2) {
/*      */         
/*  136 */         paramString3 = "";
/*  137 */       } else if ("NULL".equals(paramString3) && 227 == paramInt2) {
/*  138 */         paramString3 = "";
/*  139 */       } else if ("NULL".equals(paramString3) && 224 == paramInt2) {
/*      */         
/*  141 */         paramString3 = "";
/*  142 */       } else if ("NULL".equals(paramString3) && 225 == paramInt2) {
/*  143 */         paramString3 = "";
/*  144 */       } else if ("NULL".equals(paramString3) && (256 == paramInt2 || 257 == paramInt2)) {
/*  145 */         paramString3 = "";
/*      */       } 
/*      */ 
/*      */       
/*  149 */       BrowserComInfo browserComInfo = new BrowserComInfo();
/*  150 */       String str9 = "";
/*  151 */       if (paramInt6 == 0 && paramInt7 == 1 && paramInt8 == 1 && paramInt9 == 1 && "".equals(paramString3)) {
/*  152 */         str9 = "<img src='/images/BacoError_wev8.gif' align='absmiddle'>";
/*      */       }
/*  154 */       int i3 = Util.getIntValue((String)paramHashtable.get("iscreate"), 0);
/*  155 */       String str10 = Util.null2String((String)paramHashtable.get("fielddbtype"));
/*  156 */       int i4 = Util.getIntValue((String)paramHashtable.get("isprint"), 0);
/*  157 */       RecordSet recordSet = new RecordSet();
/*  158 */       String str11 = browserComInfo.getBrowserurl("" + paramInt2);
/*  159 */       String str12 = browserComInfo.getLinkurl("" + paramInt2);
/*  160 */       String str13 = "";
/*  161 */       String str14 = "";
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*  166 */       String str15 = "";
/*      */       
/*  168 */       String str16 = "";
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*  173 */       String str17 = "";
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*  178 */       String str18 = "";
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*  184 */       String str19 = "";
/*  185 */       String str20 = "";
/*  186 */       int i5 = -1;
/*  187 */       int i6 = 0;
/*  188 */       int i7 = 0;
/*  189 */       String str21 = Util.null2String((String)paramHashtable.get("derecorderindex"));
/*  190 */       int i8 = Util.getIntValue((String)paramHashtable.get("beagenter"), 0);
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*  196 */       String str22 = paramString3;
/*  197 */       boolean bool1 = false;
/*      */       try {
/*  199 */         HttpServletRequest httpServletRequest = (HttpServletRequest)paramHashtable.get("httprequest");
/*  200 */         HttpSession httpSession = httpServletRequest.getSession(false);
/*  201 */         i6 = Util.getIntValue((String)paramHashtable.get("requestid"), 0);
/*  202 */         i7 = Util.getIntValue(httpServletRequest.getParameter("desrequestid"), 0);
/*  203 */         int i9 = Util.getIntValue((String)paramHashtable.get("userid"), 0);
/*  204 */         str19 = Util.null2String((String)httpSession.getAttribute("requestAdd" + i6));
/*  205 */         str20 = Util.null2String((String)httpSession.getAttribute("requestAddNewNodes" + i9));
/*  206 */         i5 = Util.getIntValue((String)httpSession.getAttribute("requestFlowDocField" + i9), -1);
/*  207 */         String str24 = Util.null2String(httpServletRequest.getParameter("docfileid"));
/*  208 */         if (str19.equals("")) {
/*  209 */           str19 = Util.null2String((String)httpSession.getAttribute("requestAdd" + i9));
/*      */         }
/*  211 */         String str25 = Util.null2String((String)httpSession.getAttribute(i9 + "_" + i6 + "newdocid"));
/*  212 */         if ((paramInt2 == 37 || (paramInt2 == 9 && str19.equals("1"))) && ((str24.equals("" + paramInt1) && paramInt4 == 0) || (str24.equals("" + paramInt1 + "_" + str21) && paramInt4 == 1)) && !str25.equals("")) {
/*  213 */           if (!paramString3.equals("")) {
/*  214 */             paramString3 = paramString3 + ",";
/*      */           }
/*  216 */           if (paramInt2 == 9 && str19.equals("1")) {
/*  217 */             paramString3 = str25;
/*      */           } else {
/*  219 */             paramString3 = paramString3 + str25;
/*      */           } 
/*      */         } 
/*  222 */       } catch (Exception exception) {}
/*  223 */       if (paramInt6 == 0 && paramInt7 == 1 && paramInt8 == 1 && paramInt9 == 1 && "".equals(paramString3)) {
/*  224 */         str9 = "<img src='/images/BacoError_wev8.gif' align='absmiddle'>";
/*      */       } else {
/*  226 */         str9 = "";
/*      */       } 
/*      */       
/*  229 */       if (paramInt2 == 2 || paramInt2 == 19 || paramInt2 == 290) {
/*      */ 
/*      */ 
/*      */ 
/*      */         
/*  234 */         str13 = paramString3;
/*  235 */       } else if (!"".equals(paramString3)) {
/*  236 */         ArrayList<String> arrayList = Util.TokenizerString(paramString3, ",");
/*  237 */         if (paramInt2 == 8 || paramInt2 == 135) {
/*  238 */           ProjectInfoComInfo projectInfoComInfo = new ProjectInfoComInfo();
/*  239 */           for (byte b = 0; b < arrayList.size(); b++) {
/*  240 */             if (i4 == 0 && !str12.equals("")) {
/*  241 */               str13 = str13 + "<a href='" + str12 + arrayList.get(b) + "&f_weaver_belongto_userid=" + paramUser.getUID() + "&f_weaver_belongto_usertype=" + bool + "' target='_new'>" + projectInfoComInfo.getProjectInfoname(arrayList.get(b)) + "</a>&nbsp;";
/*      */             } else {
/*  243 */               str13 = str13 + projectInfoComInfo.getProjectInfoname(arrayList.get(b)) + "&nbsp;";
/*      */             } 
/*      */           } 
/*  246 */         } else if (paramInt2 == 17 && paramInt4 == 0 && i3 == 0) {
/*  247 */           str22 = paramString3;
/*  248 */           WorkflowJspBean workflowJspBean = new WorkflowJspBean();
/*  249 */           workflowJspBean.setRequestid(i6);
/*  250 */           StringBuffer stringBuffer = new StringBuffer(paramString3);
/*      */           
/*  252 */           String str = "&nbsp;";
/*  253 */           if (paramInt8 == 1 && paramInt6 == 0 && i1 != 0) {
/*  254 */             str = "_____";
/*      */           }
/*  256 */           boolean bool2 = (i4 == 1) ? true : false;
/*  257 */           str13 = workflowJspBean.getMultiResourceShowName(stringBuffer, str12, "" + paramInt1, i2, str, bool2);
/*      */           
/*  259 */           String[] arrayOfString = paramString3.split(",");
/*  260 */           ArrayList<String> arrayList1 = new ArrayList();
/*  261 */           for (byte b = 0; b < arrayOfString.length; b++) {
/*  262 */             if (!arrayList1.contains(arrayOfString[b])) {
/*  263 */               arrayList1.add(arrayOfString[b]);
/*      */             }
/*      */           } 
/*      */           
/*  267 */           if (paramInt8 == 0 || paramInt6 == 1) {
/*  268 */             boolean bool3 = workflowJspBean.isHasGroup();
/*  269 */             if (arrayList1.size() > 0 && bool3) {
/*  270 */               str13 = str13 + "&nbsp;<span style='color:#bfbfc0;'>（" + SystemEnv.getHtmlLabelName(18609, Util.getIntValue("" + paramUser.getLanguage(), 7)) + "" + arrayList1.size() + "" + SystemEnv.getHtmlLabelName(33423, Util.getIntValue("" + paramUser.getLanguage(), 7)) + "）</span>";
/*      */             }
/*      */           } 
/*      */           
/*  274 */           str22 = stringBuffer.toString();
/*      */           
/*  276 */           bool1 = workflowJspBean.isIsallres();
/*  277 */         } else if (paramInt2 == 1 || paramInt2 == 17 || paramInt2 == 165 || paramInt2 == 166) {
/*  278 */           ResourceComInfo resourceComInfo = new ResourceComInfo();
/*  279 */           for (byte b = 0; b < arrayList.size(); b++) {
/*  280 */             if (i4 == 0 && !str12.equals("")) {
/*  281 */               if ("/hrm/resource/HrmResource.jsp?id=".equals(str12)) {
/*  282 */                 str13 = str13 + "<a href='javaScript:openhrm(" + arrayList.get(b) + ");' onclick='pointerXY(event);'>" + resourceComInfo.getResourcename(arrayList.get(b)) + "</a>&nbsp;";
/*      */               } else {
/*  284 */                 str13 = str13 + "<a href='" + str12 + arrayList.get(b) + "&f_weaver_belongto_userid=" + paramUser.getUID() + "&f_weaver_belongto_usertype=" + bool + "' target='_new'>" + resourceComInfo.getResourcename(arrayList.get(b)) + "</a>&nbsp;";
/*      */               }
/*      */             
/*      */             } else {
/*      */               
/*  289 */               str13 = str13 + resourceComInfo.getResourcename(arrayList.get(b)) + "&nbsp;";
/*      */             } 
/*      */           } 
/*  292 */         } else if (paramInt2 == 160) {
/*  293 */           ResourceComInfo resourceComInfo = new ResourceComInfo();
/*  294 */           for (byte b = 0; b < arrayList.size(); b++) {
/*  295 */             if (i4 == 0 && !str12.equals("")) {
/*  296 */               if ("/hrm/resource/HrmResource.jsp?id=".equals(str12)) {
/*  297 */                 str13 = str13 + "<a href='javaScript:openhrm(" + arrayList.get(b) + ");' onclick='pointerXY(event);'>" + resourceComInfo.getResourcename(arrayList.get(b)) + "</a>&nbsp;";
/*      */               } else {
/*  299 */                 str13 = str13 + "<a href='" + str12 + arrayList.get(b) + "&f_weaver_belongto_userid=" + paramUser.getUID() + "&f_weaver_belongto_usertype=" + bool + "' target='_new'>" + resourceComInfo.getResourcename(arrayList.get(b)) + "</a>&nbsp;";
/*      */               } 
/*      */             } else {
/*  302 */               str13 = str13 + resourceComInfo.getResourcename(arrayList.get(b)) + "&nbsp;";
/*      */             } 
/*      */           } 
/*  305 */         } else if (paramInt2 == 142) {
/*      */ 
/*      */ 
/*      */ 
/*      */           
/*  310 */           DocReceiveUnitComInfo docReceiveUnitComInfo = new DocReceiveUnitComInfo();
/*  311 */           for (byte b = 0; b < arrayList.size(); b++) {
/*  312 */             if (i4 == 0 && !str12.equals("")) {
/*  313 */               str13 = str13 + "<a href='" + str12 + arrayList.get(b) + "&f_weaver_belongto_userid=" + paramUser.getUID() + "&f_weaver_belongto_usertype=" + bool + "' target='_new'>" + docReceiveUnitComInfo.getReceiveUnitName(arrayList.get(b)) + "</a>&nbsp;";
/*      */             } else {
/*  315 */               str13 = str13 + docReceiveUnitComInfo.getReceiveUnitName(arrayList.get(b)) + "&nbsp;";
/*      */             } 
/*      */           } 
/*  318 */         } else if (paramInt2 == 7 || paramInt2 == 18) {
/*  319 */           CustomerInfoComInfo customerInfoComInfo = new CustomerInfoComInfo();
/*  320 */           for (byte b = 0; b < arrayList.size(); b++) {
/*  321 */             if (i4 == 0 && !str12.equals("")) {
/*  322 */               str13 = str13 + "<a href='" + str12 + arrayList.get(b) + "&f_weaver_belongto_userid=" + paramUser.getUID() + "&f_weaver_belongto_usertype=" + bool + "' target='_new'>" + customerInfoComInfo.getCustomerInfoname(arrayList.get(b)) + "</a>&nbsp;";
/*      */             } else {
/*  324 */               str13 = str13 + customerInfoComInfo.getCustomerInfoname(arrayList.get(b)) + "&nbsp;";
/*      */             } 
/*      */           } 
/*  327 */         } else if (paramInt2 == 194) {
/*      */ 
/*      */ 
/*      */ 
/*      */           
/*  332 */           SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/*  333 */           for (byte b = 0; b < arrayList.size(); b++) {
/*  334 */             if (i4 == 0 && !str12.equals("")) {
/*  335 */               str13 = str13 + "<a href='" + str12 + arrayList.get(b) + "&f_weaver_belongto_userid=" + paramUser.getUID() + "&f_weaver_belongto_usertype=" + bool + "' target='_new'>" + subCompanyComInfo.getSubCompanyname(arrayList.get(b)) + "</a>&nbsp;";
/*      */             } else {
/*  337 */               str13 = str13 + subCompanyComInfo.getSubCompanyname(arrayList.get(b)) + "&nbsp;";
/*      */             } 
/*      */           } 
/*  340 */         } else if (paramInt2 == 4 || paramInt2 == 57 || paramInt2 == 167 || paramInt2 == 168) {
/*  341 */           DepartmentComInfo departmentComInfo = new DepartmentComInfo();
/*  342 */           DepartmentVirtualComInfo departmentVirtualComInfo = new DepartmentVirtualComInfo();
/*  343 */           for (byte b = 0; b < arrayList.size(); b++) {
/*  344 */             String str24 = "";
/*  345 */             String str25 = arrayList.get(b);
/*  346 */             if (!"".equals(str25)) {
/*  347 */               if (Integer.parseInt(str25) < -1) {
/*  348 */                 str24 = departmentVirtualComInfo.getDepartmentname(str25);
/*      */               } else {
/*  350 */                 str24 = departmentComInfo.getDepartmentname(str25);
/*      */               } 
/*      */             }
/*  353 */             if (i4 == 0 && !str12.equals("")) {
/*  354 */               str13 = str13 + "<a href='" + str12 + arrayList.get(b) + "&f_weaver_belongto_userid=" + paramUser.getUID() + "&f_weaver_belongto_usertype=" + bool + "' target='_new'>" + str24 + "</a>&nbsp;";
/*      */             } else {
/*  356 */               str13 = str13 + str24 + "&nbsp;";
/*      */             } 
/*      */           } 
/*  359 */         } else if (paramInt2 == 164 || paramInt2 == 194) {
/*  360 */           SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/*  361 */           SubCompanyVirtualComInfo subCompanyVirtualComInfo = new SubCompanyVirtualComInfo();
/*  362 */           for (byte b = 0; b < arrayList.size(); b++) {
/*  363 */             String str24 = "";
/*  364 */             String str25 = arrayList.get(b);
/*  365 */             if (!"".equals(str25)) {
/*  366 */               if (Integer.parseInt(str25) < -1) {
/*  367 */                 str24 = subCompanyVirtualComInfo.getSubCompanyname(str25);
/*      */               } else {
/*  369 */                 str24 = subCompanyComInfo.getSubCompanyname(str25);
/*      */               } 
/*      */             }
/*  372 */             if (i4 == 0 && !str12.equals("")) {
/*  373 */               str13 = str13 + "<a href='" + str12 + arrayList.get(b) + "&f_weaver_belongto_userid=" + paramUser.getUID() + "&f_weaver_belongto_usertype=" + bool + "' target='_new'>" + str24 + "</a>&nbsp;";
/*      */             } else {
/*  375 */               str13 = str13 + str24 + "&nbsp;";
/*      */             } 
/*      */           } 
/*  378 */         } else if (paramInt2 == 24 || paramInt2 == 278) {
/*  379 */           JobTitlesComInfo jobTitlesComInfo = new JobTitlesComInfo();
/*  380 */           String str24 = "";
/*  381 */           String str25 = "";
/*  382 */           if ("0".equals(paramString3)) {
/*  383 */             if (i3 == 1) {
/*  384 */               String str = "select jobtitle from hrmresource where id=" + i8;
/*  385 */               recordSet.executeSql(str);
/*  386 */               if (recordSet.next()) {
/*  387 */                 str24 = Util.null2String(recordSet.getString(1));
/*  388 */                 paramString3 = str24;
/*      */               } 
/*  390 */             } else if (i3 == 0) {
/*  391 */               str24 = paramString3;
/*      */             } 
/*      */           } else {
/*  394 */             str24 = paramString3;
/*      */           } 
/*      */           
/*  397 */           if (!"".equals(str24)) {
/*  398 */             ArrayList<String> arrayList1 = Util.TokenizerString(str24, ",");
/*  399 */             for (byte b = 0; b < arrayList1.size(); b++) {
/*  400 */               str25 = jobTitlesComInfo.getJobTitlesname(arrayList1.get(b));
/*  401 */               if (i4 == 0 && !str12.equals("")) {
/*  402 */                 str13 = str13 + "<a href='" + str12 + (String)arrayList1.get(b) + "&f_weaver_belongto_userid=" + paramUser.getUID() + "&f_weaver_belongto_usertype=" + bool + "' target='_new'>" + str25 + "</a>&nbsp;";
/*      */               } else {
/*  404 */                 str13 = str13 + str25 + "&nbsp;";
/*      */               }
/*      */             
/*      */             } 
/*  408 */           } else if (i4 == 0 && !str12.equals("")) {
/*  409 */             str13 = str13 + "<a href='" + str12 + str24 + "&f_weaver_belongto_userid=" + paramUser.getUID() + "&f_weaver_belongto_usertype=" + bool + "' target='_new'>" + str25 + "</a>&nbsp;";
/*      */           } else {
/*  411 */             str13 = str13 + str25 + "&nbsp;";
/*      */           }
/*      */         
/*  414 */         } else if (paramInt2 == 9 || paramInt2 == 37) {
/*  415 */           DocComInfo docComInfo = new DocComInfo();
/*  416 */           for (byte b = 0; b < arrayList.size(); b++) {
/*  417 */             if (paramInt2 == 9 && str19.equals("1") && paramInt1 == i5) {
/*  418 */               String str24 = "" + arrayList.get(b);
/*  419 */               String str25 = "0";
/*  420 */               int i9 = Util.getIntValue((String)paramHashtable.get("isremark"), -1);
/*  421 */               if (paramInt8 == 1 && i9 == 0) {
/*  422 */                 str25 = "1";
/*      */               }
/*  424 */               if (i4 == 0) {
/*  425 */                 str13 = str13 + "<a href='#' onclick='javascript:createDoc(" + paramInt1 + "," + str24 + "," + str25 + ")' >" + docComInfo.getDocname(arrayList.get(b)) + "</a><button type=button  id='createdoc' style='display:none' class=AddDocFlow onclick=createDoc(" + paramInt1 + "," + str24 + "," + str25 + ")></button>";
/*      */               } else {
/*  427 */                 str13 = str13 + "" + docComInfo.getDocname(arrayList.get(b)) + "&nbsp;";
/*      */               }
/*      */             
/*  430 */             } else if (i4 == 0 && !str12.equals("")) {
/*  431 */               str13 = str13 + "<a href='" + str12 + arrayList.get(b) + "&f_weaver_belongto_userid=" + paramUser.getUID() + "&f_weaver_belongto_usertype=" + bool + "&requestid=" + i6 + "&desrequestid=" + i7 + "' target='_blank'>" + docComInfo.getDocname(arrayList.get(b)) + "</a>&nbsp;";
/*      */             } else {
/*  433 */               str13 = str13 + docComInfo.getDocname(arrayList.get(b)) + "&nbsp;";
/*      */             }
/*      */           
/*      */           } 
/*  437 */         } else if (paramInt2 == 23) {
/*  438 */           CapitalComInfo capitalComInfo = new CapitalComInfo();
/*  439 */           for (byte b = 0; b < arrayList.size(); b++) {
/*  440 */             if (i4 == 0 && !str12.equals("")) {
/*  441 */               str13 = str13 + "<a href='" + str12 + arrayList.get(b) + "&f_weaver_belongto_userid=" + paramUser.getUID() + "&f_weaver_belongto_usertype=" + bool + "' target='_new'>" + capitalComInfo.getCapitalname(arrayList.get(b)) + "</a>&nbsp;";
/*      */             } else {
/*  443 */               str13 = str13 + capitalComInfo.getCapitalname(arrayList.get(b)) + "&nbsp;";
/*      */             } 
/*      */           } 
/*  446 */         } else if (paramInt2 == 16 || paramInt2 == 152 || paramInt2 == 171) {
/*      */           try {
/*  448 */             WorkflowRequestComInfo workflowRequestComInfo = new WorkflowRequestComInfo();
/*  449 */             HttpServletRequest httpServletRequest = (HttpServletRequest)paramHashtable.get("httprequest");
/*  450 */             HttpSession httpSession = httpServletRequest.getSession(false);
/*  451 */             for (byte b = 0; b < arrayList.size(); b++) {
/*  452 */               if (i4 == 0 && !str12.equals("")) {
/*  453 */                 int i9 = Util.getIntValue(String.valueOf(httpSession.getAttribute("slinkwfnum")));
/*  454 */                 i9++;
/*  455 */                 httpSession.setAttribute("resrequestid" + i9, "" + arrayList.get(b));
/*  456 */                 httpSession.setAttribute("slinkwfnum", "" + i9);
/*  457 */                 httpSession.setAttribute("haslinkworkflow", "1");
/*  458 */                 str15 = str15 + "<input type='hidden' name='slink" + paramInt1 + "_rq" + arrayList.get(b) + "' value='" + i9 + "' />";
/*  459 */                 str13 = str13 + "<a href='" + str12 + arrayList.get(b) + "&f_weaver_belongto_userid=" + paramUser.getUID() + "&f_weaver_belongto_usertype=" + bool + "&wflinkno=" + i9 + "' target='_new'>" + workflowRequestComInfo.getRequestName(arrayList.get(b)) + "</a>&nbsp;";
/*      */               } else {
/*  461 */                 str13 = str13 + workflowRequestComInfo.getRequestName(arrayList.get(b)) + "&nbsp;";
/*      */               } 
/*      */             } 
/*  464 */           } catch (Exception exception) {}
/*  465 */         } else if (paramInt2 == 141) {
/*  466 */           ResourceConditionManager resourceConditionManager = new ResourceConditionManager();
/*  467 */           str13 = str13 + resourceConditionManager.getFormShowName(paramString3, i2);
/*  468 */         } else if (paramInt2 == 161) {
/*      */ 
/*      */ 
/*      */ 
/*      */           
/*  473 */           str13 = "";
/*  474 */           str14 = paramString3;
/*      */ 
/*      */ 
/*      */ 
/*      */           
/*      */           try {
/*  480 */             Browser browser = (Browser)StaticObj.getServiceByFullname(str10, Browser.class);
/*  481 */             BrowserBean browserBean = browser.searchById(i6 + "^~^" + str14 + ((paramInt4 == 1) ? ("^~^" + str21) : ""));
/*  482 */             String str24 = Util.null2String(browserBean.getDescription());
/*  483 */             String str25 = Util.null2String(browserBean.getName());
/*  484 */             if (i4 == 0) {
/*      */               
/*  486 */               String str = Util.null2String(browserBean.getHref());
/*  487 */               if (str.equals("")) {
/*  488 */                 str13 = "<a title='" + Util.formatMultiLang(str24) + "'>" + Util.formatMultiLang(str25) + "</a>&nbsp;";
/*      */               }
/*  490 */               else if (isChineseCharacter(str)) {
/*  491 */                 str13 = "<a title='" + str24 + "' href='javascript:openHrefWithChinese(\"" + str + "\");'>" + str25 + "</a>&nbsp;";
/*      */               } else {
/*  493 */                 str13 = "<a title='" + Util.formatMultiLang(str24) + "' href='" + str + "' target='_blank'>" + Util.formatMultiLang(str25) + "</a>&nbsp;";
/*      */               } 
/*      */             } else {
/*      */               
/*  497 */               str13 = "" + str25 + "&nbsp;";
/*      */             } 
/*  499 */           } catch (Exception exception) {}
/*      */         }
/*  501 */         else if (paramInt2 == 162) {
/*      */ 
/*      */ 
/*      */ 
/*      */           
/*  506 */           str13 = "";
/*  507 */           str14 = paramString3;
/*      */ 
/*      */ 
/*      */ 
/*      */           
/*      */           try {
/*  513 */             Browser browser = (Browser)StaticObj.getServiceByFullname(str10, Browser.class);
/*  514 */             ArrayList<String> arrayList1 = Util.TokenizerString(str14, ",");
/*  515 */             for (byte b = 0; b < arrayList1.size(); b++) {
/*  516 */               String str24 = arrayList1.get(b);
/*  517 */               BrowserBean browserBean = browser.searchById(i6 + "^~^" + str24 + ((paramInt4 == 1) ? ("^~^" + str21) : ""));
/*  518 */               String str25 = Util.null2String(browserBean.getName());
/*  519 */               String str26 = Util.null2String(browserBean.getDescription());
/*  520 */               if (i4 == 0) {
/*      */                 
/*  522 */                 String str = Util.null2String(browserBean.getHref());
/*  523 */                 if (str.equals("")) {
/*  524 */                   str13 = str13 + "<a title='" + Util.formatMultiLang(str26) + "'>" + Util.formatMultiLang(str25) + "</a>&nbsp;";
/*      */                 }
/*  526 */                 else if (isChineseCharacter(str)) {
/*  527 */                   str13 = "<a title='" + str26 + "' href='javascript:openHrefWithChinese(\"" + str + "\");'>" + str25 + "</a>&nbsp;";
/*      */                 } else {
/*  529 */                   str13 = "<a title='" + Util.formatMultiLang(str26) + "' href='" + str + "' target='_blank'>" + Util.formatMultiLang(str25) + "</a>&nbsp;";
/*      */                 } 
/*      */               } else {
/*      */                 
/*  533 */                 str13 = str13 + "" + str25 + "&nbsp;";
/*      */               } 
/*      */             } 
/*  536 */           } catch (Exception exception) {}
/*      */         }
/*  538 */         else if (paramInt2 == 256 || paramInt2 == 257) {
/*      */ 
/*      */ 
/*      */ 
/*      */           
/*  543 */           CustomTreeUtil customTreeUtil = new CustomTreeUtil();
/*  544 */           str14 = paramString3;
/*  545 */           str13 = customTreeUtil.getTreeFieldShowName(paramString3, str10);
/*      */           try {
/*  547 */             str13 = str13.replaceAll("</a>&nbsp", "</a>,");
/*  548 */             if (str13.lastIndexOf("</a>,") != -1 && str13.lastIndexOf("</a>,") == str13.length() - 5) {
/*  549 */               str13 = str13.substring(0, str13.length() - 1);
/*      */             }
/*  551 */           } catch (Exception exception) {
/*  552 */             exception.printStackTrace();
/*      */           } 
/*  554 */         } else if (paramInt2 == 224 || paramInt2 == 225) {
/*      */ 
/*      */ 
/*      */ 
/*      */           
/*  559 */           str13 = paramString3;
/*  560 */           str14 = paramString3;
/*      */ 
/*      */ 
/*      */         
/*      */         }
/*  565 */         else if (paramInt2 == 226 || paramInt2 == 227) {
/*  566 */           str13 = paramString3;
/*  567 */           str14 = paramString3;
/*      */ 
/*      */         
/*      */         }
/*      */         else {
/*      */ 
/*      */           
/*  574 */           str16 = browserComInfo.getBrowsertablename("" + paramInt2);
/*      */ 
/*      */ 
/*      */ 
/*      */           
/*  579 */           str17 = browserComInfo.getBrowsercolumname("" + paramInt2);
/*      */ 
/*      */ 
/*      */ 
/*      */           
/*  584 */           str18 = browserComInfo.getBrowserkeycolumname("" + paramInt2);
/*      */ 
/*      */ 
/*      */           
/*      */           try {
/*  589 */             String str = "";
/*  590 */             if (paramString3.indexOf(",") != -1) {
/*  591 */               str = "select " + str18 + "," + str17 + " from " + str16 + " where " + str18 + " in ( " + paramString3 + ")";
/*      */             } else {
/*  593 */               str = "select " + str18 + "," + str17 + " from " + str16 + " where " + str18 + "=" + paramString3;
/*      */             } 
/*  595 */             (new BaseBean()).writeLog(str);
/*  596 */             recordSet.executeSql(str);
/*  597 */             while (recordSet.next()) {
/*  598 */               str14 = Util.null2String(recordSet.getString(1));
/*  599 */               String str24 = Util.toScreen(recordSet.getString(2), i2);
/*  600 */               if (paramInt2 == 22) {
/*  601 */                 int i9 = 0;
/*  602 */                 String str25 = "";
/*  603 */                 if (paramHashtable.containsKey("FnaSystemSet_enableDispalyAll") && paramHashtable.containsKey("FnaSystemSet_separator")) {
/*  604 */                   i9 = Util.getIntValue((String)paramHashtable.get("FnaSystemSet_enableDispalyAll"));
/*  605 */                   str25 = Util.null2String((String)paramHashtable.get("FnaSystemSet_separator"));
/*      */                 } else {
/*  607 */                   FnaSystemSetComInfo fnaSystemSetComInfo = new FnaSystemSetComInfo();
/*  608 */                   i9 = Util.getIntValue(fnaSystemSetComInfo.get_enableDispalyAll());
/*  609 */                   str25 = Util.null2String(fnaSystemSetComInfo.get_separator());
/*  610 */                   paramHashtable.put("FnaSystemSet_enableDispalyAll", i9 + "");
/*  611 */                   paramHashtable.put("FnaSystemSet_separator", str25);
/*      */                 } 
/*  613 */                 if (i9 == 1) {
/*  614 */                   if (paramHashtable.containsKey("FnaSystemSet_showid_FnaBudgetfeeType_level3_" + str14)) {
/*  615 */                     str24 = Util.null2String(paramHashtable.get("FnaSystemSet_showid_FnaBudgetfeeType_level3_" + str14));
/*      */                   } else {
/*  617 */                     str24 = (new BudgetfeeTypeComInfo()).getSubjectFullName(str14, str25);
/*  618 */                     paramHashtable.put("FnaSystemSet_showid_FnaBudgetfeeType_level3_" + str14, str24);
/*      */                   } 
/*      */                 }
/*      */               } 
/*  622 */               if (i4 == 0 && !str12.equals("")) {
/*  623 */                 if ("/hrm/resource/HrmResource.jsp?id=".equalsIgnoreCase(str12)) {
/*  624 */                   str13 = str13 + "<a href='javaScript:openhrm(" + str14 + ");' onclick='pointerXY(event);'>" + str24 + "</a>&nbsp"; continue;
/*  625 */                 }  if (paramInt2 == 263 || paramInt2 == 58) {
/*  626 */                   str13 = str13 + FnaCommon.escapeHtml(str24); continue;
/*      */                 } 
/*  628 */                 str13 = str13 + "<a href='" + str12 + str14 + "&f_weaver_belongto_userid=" + paramUser.getUID() + "&f_weaver_belongto_usertype=" + bool + "' target='_new'>" + FnaCommon.escapeHtml(str24) + "</a>&nbsp;";
/*      */                 continue;
/*      */               } 
/*  631 */               str13 = str13 + FnaCommon.escapeHtml(str24) + "&nbsp;";
/*      */             }
/*      */           
/*  634 */           } catch (Exception exception) {
/*  635 */             (new BaseBean()).writeLog(exception);
/*      */           } 
/*      */         } 
/*      */       } 
/*      */       
/*  640 */       if (paramInt2 == 161 || paramInt2 == 162 || paramInt2 == 224 || paramInt2 == 225 || paramInt2 == 226 || paramInt2 == 227 || paramInt2 == 256 || paramInt2 == 257) {
/*      */         
/*  642 */         str11 = str11 + "?type=" + URLEncoder.encode(str10, "UTF-8");
/*  643 */         if (paramInt2 == 256 || paramInt2 == 257) {
/*  644 */           str11 = str11 + "_" + paramInt2;
/*      */         }
/*      */       } 
/*  647 */       if (paramInt2 == 118) {
/*  648 */         str13 = "<a href='/meeting/report/MeetingRoomPlan.jsp' name='MeetingRoomPlanLink' target='_blank'>" + SystemEnv.getHtmlLabelName(2193, i2) + "</a>";
/*      */       }
/*      */ 
/*      */       
/*  652 */       if (paramInt2 == 16) {
/*  653 */         if (str11.indexOf("RequestBrowser.jsp?") > -1) {
/*  654 */           str11 = str11 + "&f_weaver_belongto_userid=" + paramUser.getUID() + "&f_weaver_belongto_usertype=" + bool;
/*      */         } else {
/*  656 */           str11 = str11 + "?f_weaver_belongto_userid=" + paramUser.getUID() + "&f_weaver_belongto_usertype=" + bool;
/*      */         } 
/*      */       }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*  682 */       if (paramInt2 == 7) {
/*  683 */         if (str11.indexOf("CustomerBrowser.jsp?") > -1) {
/*  684 */           str11 = str11 + "&f_weaver_belongto_userid=" + paramUser.getUID() + "&f_weaver_belongto_usertype=" + bool;
/*      */         } else {
/*  686 */           str11 = str11 + "?f_weaver_belongto_userid=" + paramUser.getUID() + "&f_weaver_belongto_usertype=" + bool;
/*      */         } 
/*      */       }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*  695 */       if (paramInt2 == 9) {
/*  696 */         if (str11.indexOf("DocBrowser.jsp?") > -1) {
/*  697 */           str11 = str11 + "&f_weaver_belongto_userid=" + paramUser.getUID() + "&f_weaver_belongto_usertype=" + bool;
/*      */         } else {
/*  699 */           str11 = str11 + "?f_weaver_belongto_userid=" + paramUser.getUID() + "&f_weaver_belongto_usertype=" + bool;
/*      */         } 
/*      */       }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*  708 */       if (paramInt2 == 37)
/*      */       {
/*      */ 
/*      */ 
/*      */         
/*  713 */         if (str11.indexOf("MutiDocBrowser.jsp?") > -1) {
/*  714 */           str11 = str11 + "&f_weaver_belongto_userid=" + paramUser.getUID() + "&f_weaver_belongto_usertype=" + bool;
/*      */         } else {
/*  716 */           str11 = str11 + "?f_weaver_belongto_userid=" + paramUser.getUID() + "&f_weaver_belongto_usertype=" + bool;
/*      */         } 
/*      */       }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*  728 */       if (paramInt2 == 1)
/*      */       {
/*      */ 
/*      */ 
/*      */         
/*  733 */         if (str11.indexOf("ResourceBrowser.jsp?") > -1) {
/*  734 */           str11 = str11 + "&f_weaver_belongto_userid=" + paramUser.getUID() + "&f_weaver_belongto_usertype=" + bool;
/*      */         } else {
/*  736 */           str11 = str11 + "?f_weaver_belongto_userid=" + paramUser.getUID() + "&f_weaver_belongto_usertype=" + bool;
/*      */         } 
/*      */       }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*  764 */       if (paramInt2 == 165)
/*      */       {
/*      */ 
/*      */ 
/*      */         
/*  769 */         if (str11.indexOf("ResourceBrowserByDec.jsp?") > -1) {
/*  770 */           str11 = str11 + "&f_weaver_belongto_userid=" + paramUser.getUID() + "&f_weaver_belongto_usertype=" + bool;
/*      */         } else {
/*  772 */           str11 = str11 + "?f_weaver_belongto_userid=" + paramUser.getUID() + "&f_weaver_belongto_usertype=" + bool;
/*      */         } 
/*      */       }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*  782 */       if (paramInt2 == 166)
/*      */       {
/*      */ 
/*      */ 
/*      */         
/*  787 */         if (str11.indexOf("MultiResourceBrowserByDec.jsp?") > -1) {
/*  788 */           str11 = str11 + "&f_weaver_belongto_userid=" + paramUser.getUID() + "&f_weaver_belongto_usertype=" + bool;
/*      */         } else {
/*  790 */           str11 = str11 + "?f_weaver_belongto_userid=" + paramUser.getUID() + "&f_weaver_belongto_usertype=" + bool;
/*      */         } 
/*      */       }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*  800 */       if (paramInt2 == 167)
/*      */       {
/*      */ 
/*      */ 
/*      */         
/*  805 */         if (str11.indexOf("DepartmentBrowserByDec.jsp?") > -1) {
/*  806 */           str11 = str11 + "&f_weaver_belongto_userid=" + paramUser.getUID() + "&f_weaver_belongto_usertype=" + bool;
/*      */         } else {
/*  808 */           str11 = str11 + "?f_weaver_belongto_userid=" + paramUser.getUID() + "&f_weaver_belongto_usertype=" + bool;
/*      */         } 
/*      */       }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*  818 */       if (paramInt2 == 168)
/*      */       {
/*      */ 
/*      */ 
/*      */         
/*  823 */         if (str11.indexOf("MultiDepartmentBrowserByDecOrder.jsp?") > -1) {
/*  824 */           str11 = str11 + "&f_weaver_belongto_userid=" + paramUser.getUID() + "&f_weaver_belongto_usertype=" + bool;
/*      */         } else {
/*  826 */           str11 = str11 + "?f_weaver_belongto_userid=" + paramUser.getUID() + "&f_weaver_belongto_usertype=" + bool;
/*      */         } 
/*      */       }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*  836 */       if (paramInt2 == 169)
/*      */       {
/*      */ 
/*      */ 
/*      */         
/*  841 */         if (str11.indexOf("SubcompanyBrowserByDec.jsp?") > -1) {
/*  842 */           str11 = str11 + "&f_weaver_belongto_userid=" + paramUser.getUID() + "&f_weaver_belongto_usertype=" + bool;
/*      */         } else {
/*  844 */           str11 = str11 + "?f_weaver_belongto_userid=" + paramUser.getUID() + "&f_weaver_belongto_usertype=" + bool;
/*      */         } 
/*      */       }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*  854 */       if (paramInt2 == 170)
/*      */       {
/*      */ 
/*      */ 
/*      */         
/*  859 */         if (str11.indexOf("MultiSubcompanyBrowserByDec.jsp?") > -1) {
/*  860 */           str11 = str11 + "&f_weaver_belongto_userid=" + paramUser.getUID() + "&f_weaver_belongto_usertype=" + bool;
/*      */         } else {
/*  862 */           str11 = str11 + "?f_weaver_belongto_userid=" + paramUser.getUID() + "&f_weaver_belongto_usertype=" + bool;
/*      */         } 
/*      */       }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*  872 */       BrowserTag browserTag = null;
/*  873 */       String str23 = "";
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*  879 */       if (paramInt8 == 1 && paramInt6 == 0) {
/*      */         try {
/*  881 */           str13 = str13.replaceAll("</a>&nbsp;", "</a>,");
/*  882 */           if (str13.lastIndexOf("</a>,") != -1 && str13.lastIndexOf("</a>,") == str13.length() - 5) {
/*  883 */             str13 = str13.substring(0, str13.length() - 1);
/*      */           }
/*  885 */         } catch (Exception exception) {
/*  886 */           exception.printStackTrace();
/*      */         } 
/*      */         
/*  889 */         browserTag = new BrowserTag();
/*  890 */         browserTag.setOtherPara_hs(paramHashtable);
/*  891 */         browserTag.setFieldid(paramInt1);
/*  892 */         browserTag.setViewType(String.valueOf(paramInt4));
/*  893 */         browserTag.setName("field" + paramInt1);
/*  894 */         browserTag.setBrowserValue((paramInt2 == 17) ? str22 : paramString3);
/*  895 */         browserTag.setBrowserSpanValue(str13);
/*  896 */         browserTag.setHasBrowser("true");
/*  897 */         browserTag.setIsSingle(BrowserManager.browIsSingle(String.valueOf(paramInt2)));
/*  898 */         browserTag.setHasBrowser("true");
/*  899 */         browserTag.setIsMustInput((paramInt9 == 1) ? "2" : "1");
/*  900 */         browserTag.setCompleteUrl("javascript:getajaxurl(" + paramInt2 + ")");
/*  901 */         browserTag.setLinkUrl(str12);
/*  902 */         if (paramInt2 == 161 || paramInt2 == 162 || paramInt2 == 256 || paramInt2 == 257) {
/*  903 */           browserTag.setCompleteUrl("javascript:getajaxurl(" + paramInt2 + ",'" + str10 + "')");
/*      */         }
/*  905 */         if (paramInt2 == 160) {
/*  906 */           int i9 = Util.getIntValue(paramHashtable.get("workflowid"));
/*  907 */           recordSet.execute("select a.level_n, a.level2_n from workflow_groupdetail a ,workflow_nodegroup b where a.groupid=b.id and a.type=50 and a.objid=" + paramInt1 + " and b.nodeid in (select nodeid from workflow_flownode where workflowid=" + i9 + " ) ");
/*  908 */           String str = "";
/*  909 */           int i10 = 0;
/*  910 */           if (recordSet.next()) {
/*  911 */             str = recordSet.getString(1);
/*  912 */             i10 = Util.getIntValue(recordSet.getString(2), 0);
/*  913 */             str = str + "a" + i10 + "b" + i8;
/*      */           } 
/*  915 */           browserTag.setCompleteUrl("javascript:getajaxurl(" + paramInt2 + ",'','','" + paramInt1 + "','" + str + "')");
/*      */         } 
/*      */         
/*  918 */         browserTag.setWidth("auto");
/*  919 */         browserTag.setDefaultRow("99");
/*  920 */         browserTag.setNeedHidden("false");
/*  921 */         browserTag.setOnPropertyChange("wfbrowvaluechange(this," + paramInt1 + ")");
/*      */ 
/*      */ 
/*      */         
/*  925 */         browserTag.setHasInput("true");
/*      */ 
/*      */         
/*  928 */         if (paramInt2 == 17 || paramInt2 == 1 || paramInt2 == 165 || paramInt2 == 166) {
/*  929 */           browserTag.setLinkUrl("javascript:openhrm($id$)");
/*  930 */           browserTag.setType(paramInt2 + "");
/*      */         } 
/*      */         
/*  933 */         if (paramInt2 == 17) {
/*      */           
/*  935 */           browserTag.setHasAdd("true");
/*  936 */           browserTag.setAddBtnClass("resAddGroupClass");
/*  937 */           browserTag.setAddOnClick("showrescommongroup(this, " + paramInt1 + ")");
/*  938 */           if (i3 == 0) {
/*  939 */             browserTag.setIdSplitFlag("__");
/*  940 */             browserTag.setNameSplitFlag("_____");
/*  941 */             browserTag.setBrowBtnDisabled(bool1 ? "true" : "");
/*  942 */             browserTag.setAddBtnDisabled(bool1 ? "true" : "");
/*      */           } 
/*      */         } 
/*      */       } 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*  950 */       if (paramInt4 == 0) {
/*      */ 
/*      */ 
/*      */ 
/*      */         
/*  955 */         String str24 = Util.null2String(paramHashtable.get("trrigerfield"));
/*  956 */         ArrayList arrayList1 = (ArrayList)paramHashtable.get("selfieldsadd");
/*  957 */         ArrayList arrayList2 = (ArrayList)paramHashtable.get("changefieldsadd");
/*  958 */         String str25 = "";
/*  959 */         String str26 = "";
/*  960 */         String str27 = "";
/*  961 */         ArrayList<String> arrayList3 = (ArrayList)paramHashtable.get("sqlfieldidList");
/*  962 */         ArrayList<String> arrayList4 = (ArrayList)paramHashtable.get("sqlcontentList");
/*  963 */         if (arrayList3 != null && arrayList3.size() > 0) {
/*  964 */           for (byte b = 0; b < arrayList3.size(); b++) {
/*  965 */             String str29 = Util.null2String(arrayList3.get(b)).trim();
/*  966 */             String str30 = Util.null2String(arrayList4.get(b)).trim();
/*  967 */             if (!"".equals(str30) && 
/*  968 */               str30.indexOf("$" + paramInt1 + "$") > -1 && 
/*  969 */               str30.indexOf("$" + paramInt1 + "$") > -1) {
/*  970 */               str27 = str27 + str29 + ",";
/*      */             }
/*      */           } 
/*      */ 
/*      */           
/*  975 */           if (str27.length() > 0) {
/*  976 */             str25 = "fieldAttrOperate.doSqlFieldAjax(document.getElementById('field" + paramInt1 + "'),'" + str27.substring(0, str27.length() - 1) + "');";
/*  977 */             str26 = ";fieldAttrOperate.doSqlFieldAjax(document.getElementById('field" + paramInt1 + "'),'" + str27.substring(0, str27.length() - 1) + "')";
/*      */           } 
/*      */         } 
/*      */ 
/*      */         
/*  982 */         ArrayList<String> arrayList5 = (ArrayList)paramHashtable.get("sapfieldidList");
/*  983 */         if (arrayList5 != null && arrayList5.size() > 0) {
/*  984 */           for (byte b = 0; b < arrayList5.size(); b++) {
/*  985 */             String str29 = Util.null2String(arrayList5.get(b));
/*  986 */             String str30 = str29.substring(0, str29.indexOf("-"));
/*  987 */             String str31 = str29.substring(str29.indexOf("-") + 1);
/*  988 */             if (("" + paramInt1).equals(str30)) {
/*  989 */               str25 = "doSAPField('" + str31 + "',this);";
/*  990 */               str26 = ";doSAPField('" + str31 + "',this)";
/*      */             } 
/*      */           } 
/*      */         }
/*      */         
/*  995 */         String str28 = "";
/*  996 */         ArrayList<String> arrayList6 = (ArrayList)paramHashtable.get("datefieldidList");
/*  997 */         ArrayList<String> arrayList7 = (ArrayList)paramHashtable.get("datecontentList");
/*  998 */         if (arrayList6 != null && arrayList6.size() > 0) {
/*  999 */           for (byte b = 0; b < arrayList6.size(); b++) {
/* 1000 */             String str29 = Util.null2String(arrayList6.get(b)).trim();
/* 1001 */             String str30 = Util.null2String(arrayList7.get(b)).trim();
/* 1002 */             if (!"".equals(str30) && 
/* 1003 */               str30.indexOf("$" + paramInt1 + "$") > -1) {
/* 1004 */               str28 = str28 + "doFieldDate" + str29 + "(-1);";
/*      */             }
/*      */             
/* 1007 */             if (str29.equals("" + paramInt1)) {
/* 1008 */               str2 = str2 + "function getFieldDateAjax" + paramInt1 + "(){\n";
/* 1009 */               str2 = str2 + "doFieldDate" + paramInt1 + "(-1);\n";
/* 1010 */               str2 = str2 + "}\n";
/* 1011 */               str2 = str2 + "\tif (window.addEventListener){\n";
/* 1012 */               str2 = str2 + "\t    window.addEventListener(\"load\", getFieldDateAjax" + paramInt1 + ", false);\n";
/* 1013 */               str2 = str2 + "\t}else if (window.attachEvent){\n";
/* 1014 */               str2 = str2 + "\t    window.attachEvent(\"onload\", getFieldDateAjax" + paramInt1 + ");\n";
/* 1015 */               str2 = str2 + "\t}else{\n";
/* 1016 */               str2 = str2 + "\t    window.onload=getFieldDateAjax" + paramInt1 + ";\n";
/* 1017 */               str2 = str2 + "\t}\n";
/*      */             } 
/*      */           } 
/* 1020 */           if (str28.length() > 0) {
/* 1021 */             str28 = str28.substring(0, str28.length() - 1);
/* 1022 */             str25 = str28;
/* 1023 */             str26 = str26 + ";" + str28;
/*      */           } 
/*      */         } 
/*      */ 
/*      */         
/* 1028 */         if (paramInt7 == 1) {
/* 1029 */           if (paramInt6 == 0) {
/*      */             
/* 1031 */             String str = "";
/* 1032 */             if (str24.indexOf("field" + paramInt1) >= 0) {
/* 1033 */               str = ";datainput('field" + paramInt1 + "')";
/*      */             }
/*      */             
/* 1036 */             if (paramInt8 == 1) {
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */               
/* 1042 */               if (paramInt2 == 160) {
/* 1043 */                 int i9 = Util.getIntValue(paramHashtable.get("workflowid"));
/* 1044 */                 recordSet.execute("select a.level_n, a.level2_n from workflow_groupdetail a ,workflow_nodegroup b where a.groupid=b.id and a.type=50 and a.objid=" + paramInt1 + " and b.nodeid in (select nodeid from workflow_flownode where workflowid=" + i9 + " ) ");
/* 1045 */                 String str30 = "";
/* 1046 */                 int i10 = 0;
/* 1047 */                 if (recordSet.next()) {
/* 1048 */                   str30 = recordSet.getString(1);
/* 1049 */                   i10 = Util.getIntValue(recordSet.getString(2), 0);
/* 1050 */                   str30 = str30 + "a" + i10 + "b" + i8;
/*      */                 } 
/* 1052 */                 if (i1 == 0) {
/* 1053 */                   str1 = str1 + "<button type=button  class=\"Browser\" id=\"field" + paramInt1 + "browser\" name=\"field" + paramInt1 + "browser\" onclick=\"onShowResourceRole('" + paramInt1 + "','" + str11 + "','" + str12 + "','" + paramInt2 + "',field" + paramInt1 + ".getAttribute('viewtype'),'" + str30 + "')" + str + str26 + "\" title=\"" + SystemEnv.getHtmlLabelName(20570, i2) + "\"></button>\n";
/*      */                 } else {
/* 1055 */                   str23 = "onShowResourceRole('" + paramInt1 + "','" + str11 + "','" + str12 + "','" + paramInt2 + "',field" + paramInt1 + ".getAttribute('viewtype'),'" + str30 + "')" + str26;
/* 1056 */                   browserTag.setBrowserOnClick(str23);
/* 1057 */                   str1 = str1 + browserTag.getBrowserHtml();
/*      */                 } 
/* 1059 */               } else if (paramInt2 == 161 || paramInt2 == 162) {
/* 1060 */                 if (i1 == 0) {
/* 1061 */                   str1 = str1 + "<button type=button  class=\"Browser\" id=\"field" + paramInt1 + "browser\" name=\"field" + paramInt1 + "browser\"  onclick=\"onShowBrowser2('" + paramInt1 + "','" + str11 + "','" + str12 + "','" + paramInt2 + "',field" + paramInt1 + ".getAttribute('viewtype'))" + str26 + ";";
/* 1062 */                   if (str24.indexOf("field" + paramInt1) >= 0) {
/* 1063 */                     str1 = str1 + "datainput('field" + paramInt1 + "');";
/*      */                   }
/* 1065 */                   str1 = str1 + "\"></button>\n";
/*      */                 } else {
/* 1067 */                   str23 = "onShowBrowser2('" + paramInt1 + "','" + str11 + "','" + str12 + "','" + paramInt2 + "',field" + paramInt1 + ".getAttribute('viewtype'))" + str26 + ";";
/* 1068 */                   if (str24.indexOf("field" + paramInt1) >= 0);
/*      */ 
/*      */                   
/* 1071 */                   browserTag.setBrowserOnClick(str23);
/* 1072 */                   str1 = str1 + browserTag.getBrowserHtml();
/*      */                 } 
/* 1074 */               } else if (paramInt2 == 256 || paramInt2 == 257) {
/* 1075 */                 if (i1 == 0) {
/* 1076 */                   str1 = str1 + "<button type=button  class=\"Browser\" onclick=\"onShowBrowser2('" + paramInt1 + "','" + str11 + "','" + str12 + "','" + paramInt2 + "',field" + paramInt1 + ".getAttribute('viewtype'))" + str26 + ";";
/* 1077 */                   if (str24.indexOf("field" + paramInt1) >= 0) {
/* 1078 */                     str1 = str1 + "datainput('field" + paramInt1 + "');";
/*      */                   }
/* 1080 */                   str1 = str1 + "\"></button>\n";
/*      */                 } else {
/* 1082 */                   str23 = "onShowBrowser2('" + paramInt1 + "','" + str11 + "','" + str12 + "','" + paramInt2 + "',field" + paramInt1 + ".getAttribute('viewtype'))" + str26 + ";";
/* 1083 */                   if (str24.indexOf("field" + paramInt1) >= 0);
/*      */ 
/*      */                   
/* 1086 */                   browserTag.setBrowserOnClick(str23);
/* 1087 */                   str1 = str1 + browserTag.getBrowserHtml();
/*      */                 } 
/* 1089 */               } else if (paramInt2 == 58 || paramInt2 == 263) {
/* 1090 */                 String str30 = (paramInt9 == 1) ? "2" : "1";
/* 1091 */                 String str31 = "city";
/* 1092 */                 if (paramInt2 == 263) {
/* 1093 */                   str31 = "citytwo";
/*      */                 }
/* 1095 */                 String str32 = "field" + paramInt1;
/* 1096 */                 str1 = str1 + "<div areaType=\"" + str31 + "\" areaName=\"" + str32 + "\" areaValue=\"" + str14 + "\" areaSpanValue=\"" + Util.formatMultiLang(str13) + "\"  areaMustInput=\"" + str30 + "\"  areaCallback=\"browAreaSelectCallback\"  class=\"_areaselect\" id=\"_areaselect_field" + paramInt1 + "\"></div><script> areromancedivbyid(\"_areaselect_field" + paramInt1 + "\",-1);</script>";
/* 1097 */               } else if (paramInt2 == 141) {
/* 1098 */                 if (i1 == 0) {
/* 1099 */                   str1 = str1 + "<button type=button  class=\"Browser\" id=\"field" + paramInt1 + "browser\" name=\"field" + paramInt1 + "browser\" onclick=\"onShowResourceConditionBrowser('" + paramInt1 + "','" + str11 + "','" + str12 + "','" + paramInt2 + "',field" + paramInt1 + ".getAttribute('viewtype'))" + str + str26 + "\" title=\"" + SystemEnv.getHtmlLabelName(172, i2) + "\"></button>\n";
/*      */                 } else {
/* 1101 */                   str23 = "onShowResourceConditionBrowser('" + paramInt1 + "','" + str11 + "','" + str12 + "','" + paramInt2 + "',field" + paramInt1 + ".getAttribute('viewtype'))" + str26;
/*      */                   
/* 1103 */                   browserTag.setBrowserOnClick(str23);
/* 1104 */                   str1 = str1 + browserTag.getBrowserHtml();
/*      */                 } 
/* 1106 */               } else if (paramInt2 == 34) {
/*      */ 
/*      */                 
/* 1109 */                 StringBuffer stringBuffer = (new StringBuffer()).append("<select class=\"inputstyle\" size=\"1\" viewtype=\"" + paramInt9 + "\" name=\"newLeaveType\" id=\"").append("field" + paramInt1).append("\" onchange=\"setLeaveTypeValue(this.value);checkinput2('field" + paramInt1 + "','field" + paramInt1 + "span',this.viewtype);\">").append("<option value=''></option>");
/* 1110 */                 List list = (new HrmLeaveTypeColorManager()).find("[map]field002:1");
/* 1111 */                 if (list != null) {
/* 1112 */                   for (HrmLeaveTypeColor hrmLeaveTypeColor : list) {
/* 1113 */                     stringBuffer.append("<option value='").append(hrmLeaveTypeColor.getField004()).append("' ").append(str14.equals(String.valueOf(hrmLeaveTypeColor.getField004())) ? "selected" : "").append(">").append(hrmLeaveTypeColor.getTitle(paramUser.getLanguage())).append("</option>");
/*      */                   }
/*      */                 }
/* 1116 */                 stringBuffer.append("</select>\r\n")
/* 1117 */                   .append("<span id=\"field" + paramInt1 + "span\">" + str9 + "</span>")
/* 1118 */                   .append("<script>jQuery(document).ready(function(){var newLeaveType = $GetEle('newLeaveType');jQuery(newLeaveType).selectbox('hide');jQuery(newLeaveType).autoSelect({showAll: 'true'});});function setLeaveTypeValue(vl) {jQuery(\"input[name=").append("field" + paramInt1).append("]\").val(vl);try{showVacationInfo();}catch(e){}}</script>");
/* 1119 */                 str1 = str1 + stringBuffer.toString();
/*      */               } else {
/* 1121 */                 if (paramInt2 != 9 && paramInt2 != 37 && paramInt2 != 2 && paramInt2 != 19) {
/* 1122 */                   if (i1 == 0) {
/* 1123 */                     str1 = str1 + "<button id=\"field" + paramInt1 + "browser\" name=\"field" + paramInt1 + "browser\" type=button  class=\"Browser\" ";
/* 1124 */                     str1 = str1 + "onclick=\"onShowBrowser2('" + paramInt1 + "','" + str11 + "','" + str12 + "','" + paramInt2 + "',field" + paramInt1 + ".getAttribute('viewtype'))" + str + str26 + "\"";
/* 1125 */                     str1 = str1 + " title=\"" + SystemEnv.getHtmlLabelName(172, i2) + "\"></button>";
/*      */                   } else {
/* 1127 */                     str23 = "onShowBrowser2('" + paramInt1 + "','" + str11 + "','" + str12 + "','" + paramInt2 + "',field" + paramInt1 + ".getAttribute('viewtype'))" + str26;
/* 1128 */                     browserTag.setBrowserOnClick(str23);
/* 1129 */                     str1 = str1 + browserTag.getBrowserHtml();
/*      */                   } 
/* 1131 */                 } else if (paramInt2 == 37) {
/* 1132 */                   if (i1 == 0) {
/* 1133 */                     str1 = str1 + "<button type=button  class=\"AddDocFlow\" onclick=\"onShowBrowser2('" + paramInt1 + "','" + str11 + "','" + str12 + "','" + paramInt2 + "',field" + paramInt1 + ".getAttribute('viewtype'))\">" + SystemEnv.getHtmlLabelName(611, i2) + "</button>&nbsp;&nbsp;<button type=button  class=\"AddDocFlow\" onclick=\"onNewDoc(" + paramInt1 + ")\" title=\"" + SystemEnv.getHtmlLabelName(82, i2) + "\">" + SystemEnv.getHtmlLabelName(82, i2) + "</button>";
/*      */                   } else {
/* 1135 */                     str23 = "onShowBrowser2('" + paramInt1 + "','" + str11 + "','" + str12 + "','" + paramInt2 + "',field" + paramInt1 + ".getAttribute('viewtype'))";
/* 1136 */                     browserTag.setBrowserOnClick(str23);
/* 1137 */                     browserTag.setHasAdd("true");
/* 1138 */                     browserTag.setAddOnClick("onNewDoc('" + paramInt1 + "')");
/* 1139 */                     str1 = str1 + browserTag.getBrowserHtml();
/*      */                   } 
/* 1141 */                 } else if (paramInt2 == 9 && paramInt1 == i5 && str19.equals("1")) {
/*      */                   
/* 1143 */                   if (paramString3.equals("")) {
/* 1144 */                     browserTag.setHasAdd("true");
/* 1145 */                     browserTag.setAddBtnID("createdoc");
/* 1146 */                     browserTag.setAddOnClick("createDoc('" + paramInt1 + "','','1')");
/*      */                   } else {
/* 1148 */                     browserTag.setHasAdd("true");
/* 1149 */                     browserTag.setAddBtnID("createdoc");
/* 1150 */                     browserTag.setAddOnClick("createDoc('" + paramInt1 + "','" + paramString3 + "','1')");
/* 1151 */                     browserTag.setAddBtnClass("e8_browserAdd AddDocFlowHidden");
/*      */                   } 
/* 1153 */                   if (!"1".equals(str20)) {
/* 1154 */                     if (i1 == 0) {
/* 1155 */                       str1 = str1 + "<button  id=\"field" + paramInt1 + "browser\" name=\"field" + paramInt1 + "browser\"  type=button  class=\"Browser\" ";
/* 1156 */                       if (str24.indexOf("field" + paramInt1) >= 0) {
/* 1157 */                         str1 = str1 + "onclick=\"onShowBrowser2('" + paramInt1 + "','" + str11 + "','" + str12 + "','" + paramInt2 + "',field" + paramInt1 + ".getAttribute('viewtype'));datainput('field" + paramInt1 + "')" + str26 + "\" ";
/*      */                       } else {
/* 1159 */                         str1 = str1 + "onclick=\"onShowBrowser2('" + paramInt1 + "','" + str11 + "','" + str12 + "','" + paramInt2 + "',field" + paramInt1 + ".getAttribute('viewtype'))" + str26 + "\" ";
/*      */                       } 
/* 1161 */                       str1 = str1 + " title=\"" + SystemEnv.getHtmlLabelName(172, i2) + "\"></button>";
/*      */                       
/* 1163 */                       str1 = str1 + "<span id=\"field" + paramInt1 + "span\">" + str13 + "" + str9;
/* 1164 */                       str1 = str1 + "</span>\n";
/* 1165 */                       str1 = str1 + "<span id=\"CreateNewDoc\">";
/* 1166 */                       if (str19.equals("1") && paramString3.equals("")) {
/* 1167 */                         str1 = str1 + "<button id=\"createdoc\" name=\"createdoc\"  type=button  class=\"AddDocFlow\" onclick=\"createDoc('" + paramInt1 + "','','1');\" title=\"" + SystemEnv.getHtmlLabelName(82, i2) + "\">" + SystemEnv.getHtmlLabelName(82, i2) + "</button>";
/*      */                       }
/* 1169 */                       str1 = str1 + "</span>";
/* 1170 */                       str1 = str1 + "\n";
/*      */                     } else {
/* 1172 */                       if (str24.indexOf("field" + paramInt1) >= 0) {
/* 1173 */                         str23 = "onShowBrowser2('" + paramInt1 + "','" + str11 + "','" + str12 + "','" + paramInt2 + "',field" + paramInt1 + ".getAttribute('viewtype'));datainput('field" + paramInt1 + "')" + str26;
/*      */                       } else {
/* 1175 */                         str23 = "onShowBrowser2('" + paramInt1 + "','" + str11 + "','" + str12 + "','" + paramInt2 + "',field" + paramInt1 + ".getAttribute('viewtype'))" + str26;
/*      */                       } 
/* 1177 */                       browserTag.setBrowserOnClick(str23);
/* 1178 */                       str1 = str1 + browserTag.getBrowserHtml();
/*      */                     } 
/*      */                   } else {
/*      */                     
/* 1182 */                     browserTag.setBrowBtnDisabled("none");
/* 1183 */                     browserTag.setHasInput("false");
/* 1184 */                     str1 = str1 + browserTag.getBrowserHtml();
/*      */                   }
/*      */                 
/* 1187 */                 } else if (i1 == 0) {
/* 1188 */                   str1 = str1 + "<button  id=\"field" + paramInt1 + "browser\" name=\"field" + paramInt1 + "browser\"  type=button  class=\"Browser\" ";
/* 1189 */                   if (str24.indexOf("field" + paramInt1) >= 0) {
/* 1190 */                     str1 = str1 + "onclick=\"onShowBrowser3('" + paramInt1 + "','" + str11 + "','" + str12 + "','" + paramInt2 + "',field" + paramInt1 + ".getAttribute('viewtype'));datainput('field" + paramInt1 + "','" + paramInt9 + "')" + str26 + "\" ";
/*      */                   } else {
/* 1192 */                     str1 = str1 + "onclick=\"onShowBrowser3('" + paramInt1 + "','" + str11 + "','" + str12 + "','" + paramInt2 + "',field" + paramInt1 + ".getAttribute('viewtype'))" + str26 + "\" ";
/*      */                   } 
/* 1194 */                   str1 = str1 + " title=\"" + SystemEnv.getHtmlLabelName(172, i2) + "\"></button>";
/*      */                 }
/* 1196 */                 else if (paramInt2 == 2 || paramInt2 == 19) {
/* 1197 */                   String str30 = (paramInt2 == 2) ? "calendar" : "Clock";
/* 1198 */                   str1 = str1 + "<button  id=\"field" + paramInt1 + "browser\" name=\"field" + paramInt1 + "browser\"  type=button  class=\"" + str30 + "\" ";
/* 1199 */                   if (str24.indexOf("field" + paramInt1) >= 0) {
/* 1200 */                     str1 = str1 + "onclick=\"onShowBrowser3('" + paramInt1 + "','" + str11 + "','" + str12 + "','" + paramInt2 + "',field" + paramInt1 + ".getAttribute('viewtype'));datainput('field" + paramInt1 + "','" + paramInt9 + "')" + str26 + "\" ";
/*      */                   } else {
/* 1202 */                     str1 = str1 + "onclick=\"onShowBrowser3('" + paramInt1 + "','" + str11 + "','" + str12 + "','" + paramInt2 + "',field" + paramInt1 + ".getAttribute('viewtype'))" + str26 + "\" ";
/*      */                   } 
/* 1204 */                   str1 = str1 + " title=\"" + SystemEnv.getHtmlLabelName(172, i2) + "\"></button>";
/*      */                 } else {
/* 1206 */                   if (str24.indexOf("field" + paramInt1) >= 0) {
/* 1207 */                     str23 = str23 + "onShowBrowser3('" + paramInt1 + "','" + str11 + "','" + str12 + "','" + paramInt2 + "',field" + paramInt1 + ".getAttribute('viewtype'));datainput('field" + paramInt1 + "','" + paramInt9 + "')" + str26;
/*      */                   } else {
/* 1209 */                     str23 = str23 + "onShowBrowser3('" + paramInt1 + "','" + str11 + "','" + str12 + "','" + paramInt2 + "',field" + paramInt1 + ".getAttribute('viewtype'))" + str26;
/*      */                   } 
/*      */                   
/* 1212 */                   browserTag.setBrowserOnClick(str23);
/* 1213 */                   str1 = str1 + browserTag.getBrowserHtml();
/*      */                 } 
/*      */ 
/*      */                 
/* 1217 */                 str1 = str1 + "\n";
/* 1218 */                 if (i1 != 0 && (
/* 1219 */                   paramInt2 == 2 || paramInt2 == 19)) {
/*      */ 
/*      */ 
/*      */ 
/*      */                   
/* 1224 */                   str1 = str1 + "<span id=\"field" + paramInt1 + "span\">" + ("".equals(str6) ? str13 : str6) + "" + str9;
/* 1225 */                   str1 = str1 + "</span>\n";
/*      */                 } 
/*      */               } 
/*      */ 
/*      */               
/* 1230 */               if (i1 == 0 && (paramInt2 != 9 || paramInt1 != i5 || !str19.equals("1")) && paramInt2 != 34 && paramInt2 != 58 && paramInt2 != 263) {
/* 1231 */                 str1 = str1 + "<span id=\"field" + paramInt1 + "span\">" + str13 + "" + str9;
/* 1232 */                 str1 = str1 + "</span>\n";
/*      */               } 
/* 1234 */               if (paramInt2 == 87 || paramInt2 == 184) {
/* 1235 */                 str1 = str1 + "&nbsp;&nbsp;<A href=\"/meeting/report/MeetingRoomPlan.jsp\" name='MeetingRoomPlanLink' target=\"blank\">" + SystemEnv.getHtmlLabelName(2193, i2) + "</A>\n";
/*      */               }
/* 1237 */               String str29 = "try{checkLengthbrow('field" + paramInt1 + "','field" + paramInt1 + "span','" + paramInt3 + "','" + Util.toScreen(paramString2, i2) + "','" + SystemEnv.getHtmlLabelName(20246, i2) + "','" + SystemEnv.getHtmlLabelName(20247, i2) + "',field" + paramInt1 + ".getAttribute('viewtype'))}catch(e){}" + str + str26 + str8;
/* 1238 */               str1 = str1 + "<input type=\"hidden\" viewtype=\"" + paramInt9 + "\" id=\"field" + paramInt1 + "\" name=\"field" + paramInt1 + "\" value=\"" + paramString3 + "\" __value=\"" + paramString3 + "\" " + str7 + " temptitle=\"" + Util.toScreen(paramString2, i2) + "\" onpropertychange=\"" + str29 + "\" _listener=\"" + str29 + "\" />\n";
/*      */               
/* 1240 */               str1 = str1 + "" + str15;
/* 1241 */               if (arrayList2.indexOf("" + paramInt1) >= 0) {
/* 1242 */                 str1 = str1 + "<input type=\"hidden\" id=\"oldfieldview" + paramInt1 + "\" name=\"oldfieldview" + paramInt1 + "\" value=\"" + (paramInt7 + paramInt8 + paramInt9) + "\" />";
/*      */               }
/*      */             } else {
/* 1245 */               str1 = str1 + "<span id=\"field" + paramInt1 + "span\" style=\"word-break:break-all;word-wrap:break-word\">" + ("".equals(str6) ? str13 : str6) + "</span>";
/* 1246 */               String str29 = str25 + str8 + str;
/* 1247 */               str1 = str1 + "<input type=\"hidden\" id=\"field" + paramInt1 + "\" name=\"field" + paramInt1 + "\" value=\"" + paramString3 + "\" " + str7 + " _isedit=\"0\" onpropertychange=\"" + str29 + "\" _listener=\"" + str29 + "\" />";
/*      */             } 
/* 1249 */             str1 = str1 + " ";
/*      */           } else {
/* 1251 */             str1 = str1 + "<span id=\"field" + paramInt1 + "span\" style=\"word-break:break-all;word-wrap:break-word\">" + ("".equals(str6) ? str13 : str6) + "</span>";
/* 1252 */             str1 = str1 + "<input type=\"hidden\" id=\"field" + paramInt1 + "\" name=\"field" + paramInt1 + "\" value=\"" + paramString3 + "\" />";
/*      */           }
/*      */         
/* 1255 */         } else if (paramInt6 == 0) {
/* 1256 */           str1 = str1 + "<input type=\"hidden\" id=\"field" + paramInt1 + "\" name=\"field" + paramInt1 + "\" value=\"" + paramString3 + "\" />";
/*      */         } 
/*      */       } else {
/*      */         
/* 1260 */         String str24 = "\"";
/* 1261 */         if ("\"+rowindex+\"".equals(str21)) {
/* 1262 */           str24 = "\\\"";
/*      */         }
/* 1264 */         ArrayList arrayList = (ArrayList)paramHashtable.get("changedefieldsadd");
/* 1265 */         if (paramInt3 > 0) {
/* 1266 */           paramInt3 /= 2;
/*      */         }
/* 1268 */         String str25 = Util.null2String(paramHashtable.get("trrigerdetailfield"));
/* 1269 */         String str26 = "";
/* 1270 */         String str27 = "";
/* 1271 */         String str28 = "";
/* 1272 */         ArrayList<String> arrayList1 = (ArrayList)paramHashtable.get("sqlfieldidList");
/* 1273 */         ArrayList<String> arrayList2 = (ArrayList)paramHashtable.get("sqlcontentList");
/* 1274 */         if (arrayList1 != null && arrayList1.size() > 0) {
/* 1275 */           for (byte b = 0; b < arrayList1.size(); b++) {
/* 1276 */             String str33 = Util.null2String(arrayList1.get(b)).trim();
/* 1277 */             String str34 = Util.null2String(arrayList2.get(b)).trim();
/* 1278 */             if (!"".equals(str34) && 
/* 1279 */               str34.indexOf("$" + paramInt1 + "$") > -1) {
/* 1280 */               str28 = str28 + str33 + ",";
/*      */             }
/*      */             
/* 1283 */             if (str33.equals("" + paramInt1) && 
/* 1284 */               "\"+rowindex+\"".equals(str21)) {
/* 1285 */               str2 = str2 + "\t fieldAttrOperate.pageLoadInitValue('" + paramInt1 + "',rowindex);\n";
/*      */             }
/*      */           } 
/*      */           
/* 1289 */           if (str28.length() > 0) {
/* 1290 */             str26 = "fieldAttrOperate.doSqlFieldAjax(document.getElementById('field" + paramInt1 + "_" + str21 + "'),'" + str28.substring(0, str28.length() - 1) + "');";
/* 1291 */             str27 = ";fieldAttrOperate.doSqlFieldAjax(document.getElementById('field" + paramInt1 + "_" + str21 + "'),'" + str28.substring(0, str28.length() - 1) + "')";
/*      */           } 
/*      */         } 
/*      */         
/* 1295 */         ArrayList<String> arrayList3 = (ArrayList)paramHashtable.get("sapfieldidList");
/* 1296 */         if (arrayList3 != null && arrayList3.size() > 0) {
/* 1297 */           for (byte b = 0; b < arrayList3.size(); b++) {
/* 1298 */             String str33 = Util.null2String(arrayList3.get(b));
/* 1299 */             String str34 = str33.substring(0, str33.indexOf("-"));
/* 1300 */             String str35 = str33.substring(str33.indexOf("-") + 1);
/* 1301 */             if (("" + paramInt1).equals(str34)) {
/* 1302 */               str26 = str26 + "doSAPField('" + str35 + "',this);";
/*      */             }
/*      */           } 
/*      */         }
/*      */ 
/*      */         
/* 1308 */         String str29 = "";
/* 1309 */         ArrayList<String> arrayList4 = (ArrayList)paramHashtable.get("datefieldidList");
/* 1310 */         ArrayList<String> arrayList5 = (ArrayList)paramHashtable.get("datecontentList");
/* 1311 */         if (arrayList4 != null && arrayList4.size() > 0) {
/* 1312 */           for (byte b = 0; b < arrayList4.size(); b++) {
/* 1313 */             String str33 = Util.null2String(arrayList4.get(b)).trim();
/* 1314 */             String str34 = Util.null2String(arrayList5.get(b)).trim();
/* 1315 */             if (!"".equals(str34) && 
/* 1316 */               str34.indexOf("$" + paramInt1 + "$") > -1) {
/* 1317 */               str29 = str29 + "doFieldDate" + str33 + "(" + str21 + ");";
/*      */             }
/*      */             
/* 1320 */             if (str33.equals("" + paramInt1)) {
/* 1321 */               if ("\"+rowindex+\"".equals(str21)) {
/* 1322 */                 str2 = str2 + "eval(\"doFieldDate" + paramInt1 + "(\"+rowindex+\");\");\n";
/*      */               } else {
/* 1324 */                 str2 = str2 + "function getFieldDateAjaxDetail" + paramInt1 + "_" + str21 + "(){\n";
/* 1325 */                 str2 = str2 + "\tdoFieldDate" + paramInt1 + "(" + str21 + ");\n";
/* 1326 */                 str2 = str2 + "}\n";
/* 1327 */                 str2 = str2 + "\tif (window.addEventListener){\n";
/* 1328 */                 str2 = str2 + "\t    window.addEventListener(\"load\", getFieldDateAjaxDetail" + paramInt1 + "_" + str21 + ", false);\n";
/* 1329 */                 str2 = str2 + "\t}else if (window.attachEvent){\n";
/* 1330 */                 str2 = str2 + "\t    window.attachEvent(\"onload\", getFieldDateAjaxDetail" + paramInt1 + "_" + str21 + ");\n";
/* 1331 */                 str2 = str2 + "\t}else{\n";
/* 1332 */                 str2 = str2 + "\t    window.onload=getFieldDateAjaxDetail" + paramInt1 + "_" + str21 + ";\n";
/* 1333 */                 str2 = str2 + "\t}\n";
/*      */               } 
/*      */             }
/*      */           } 
/* 1337 */           if (str29.length() > 0) {
/* 1338 */             str26 = str26 + "" + str29;
/*      */           }
/*      */         } 
/* 1341 */         String str30 = "";
/* 1342 */         String str31 = "";
/* 1343 */         if (str25.indexOf("field" + paramInt1) >= 0) {
/* 1344 */           str30 = "datainputd('field" + paramInt1 + "_" + str21 + "');";
/* 1345 */           str31 = ";datainputd('field" + paramInt1 + "_" + str21 + "')";
/*      */         } 
/* 1347 */         if (paramInt8 == 1 && paramInt6 == 0) {
/* 1348 */           String str33 = "";
/* 1349 */           String str34 = "";
/* 1350 */           if (paramInt2 == 37) {
/*      */ 
/*      */ 
/*      */ 
/*      */             
/* 1355 */             if (i1 == 0) {
/* 1356 */               str1 = str1 + "<button id='field" + paramInt1 + "_" + str21 + "browser' name='field" + paramInt1 + "_" + str21 + "browser' type=button  class='AddDocFlow' onclick=" + str24 + "onShowBrowser2('" + paramInt1 + "_" + str21 + "','" + str11 + "','" + str12 + "','" + paramInt2 + "',field" + paramInt1 + "_" + str21 + ".getAttribute('viewtype'))" + str27 + str31 + str24 + ">" + SystemEnv.getHtmlLabelName(611, i2) + "</button>&nbsp;&nbsp;<button type=button  class='AddDocFlow' onclick=" + str24 + "onNewDoc('" + paramInt1 + "_" + str21 + "')" + str24 + " title='" + SystemEnv.getHtmlLabelName(82, i2) + "'>" + SystemEnv.getHtmlLabelName(82, i2) + "</button>";
/*      */             } else {
/* 1358 */               str33 = str24 + "onShowBrowser2('" + paramInt1 + "_" + str21 + "','" + str11 + "','" + str12 + " ','" + paramInt2 + "',field" + paramInt1 + "_" + str21 + ".getAttribute('viewtype'))" + str27 + str24;
/* 1359 */               str34 = str24 + "onNewDoc('" + paramInt1 + "_" + str21 + "')" + str24;
/*      */             }
/*      */           
/* 1362 */           } else if (i1 == 0) {
/* 1363 */             str1 = str1 + "<button id='field" + paramInt1 + "_" + str21 + "browser' name='field" + paramInt1 + "_" + str21 + "browser' type=button  class='Browser' onclick=" + str24 + "onShowBrowser2('" + paramInt1 + "_" + str21 + "','" + str11 + "','" + str12 + "','" + paramInt2 + "',field" + paramInt1 + "_" + str21 + ".getAttribute('viewtype'))" + str27 + str31 + str24 + " title='" + SystemEnv.getHtmlLabelName(172, i2) + "'></button>";
/*      */           } else {
/* 1365 */             str33 = str24 + "onShowBrowser2('" + paramInt1 + "_" + str21 + "','" + str11 + "','" + str12 + "','" + paramInt2 + "',field" + paramInt1 + "_" + str21 + ".getAttribute('viewtype'))" + str27 + str24;
/*      */           } 
/*      */           
/* 1368 */           if (i1 != 0) {
/* 1369 */             if (paramInt2 == 1 || paramInt2 == 17) {
/* 1370 */               str12 = "javascript:openhrm($id$)";
/*      */             }
/* 1372 */             if (paramInt2 == 2 || paramInt2 == 19) {
/* 1373 */               String str = (paramInt2 == 2) ? "calendar" : "Clock";
/* 1374 */               str1 = str1 + "<button id='field" + paramInt1 + "_" + str21 + "browser' name='field" + paramInt1 + "_" + str21 + "browser' type=button  class='" + str + "' onclick=" + str24 + "onShowBrowser2('" + paramInt1 + "_" + str21 + "','" + str11 + "','" + str12 + "','" + paramInt2 + "',field" + paramInt1 + "_" + str21 + ".getAttribute('viewtype'))" + str27 + str31 + str24 + " title='" + SystemEnv.getHtmlLabelName(172, i2) + "'></button>";
/* 1375 */               str1 = str1 + "<span id='field" + paramInt1 + "_" + str21 + "span'>" + Util.toScreen("".equals(str6) ? str13 : str6, i2) + str9 + "</span>";
/* 1376 */             } else if (paramInt2 == 58 || paramInt2 == 263) {
/* 1377 */               String str35 = (paramInt9 == 1) ? "2" : "1";
/* 1378 */               String str36 = "city";
/* 1379 */               if (paramInt2 == 263) {
/* 1380 */                 str36 = "citytwo";
/*      */               }
/* 1382 */               String str37 = "field" + paramInt1 + "_" + str21;
/* 1383 */               str1 = str1 + "<div areaType='" + str36 + "' areaName='" + str37 + "' areaValue='" + str14 + "' areaSpanValue='" + Util.formatMultiLang(str13) + "'  areaMustInput='" + str35 + "'  areaCallback='browAreaSelectCallback'  class='_areaselect' id='_areaselect_field" + paramInt1 + "_" + str21 + "'></div>";
/* 1384 */               str4 = "areromancedivbyid(\"_areaselect_field" + paramInt1 + "_" + str21 + "\",-1);";
/* 1385 */               str3 = "<script>jQuery(function () { try {areromancedivbyid(\"_areaselect_field" + paramInt1 + "_" + str21 + "\",-1);} catch(e) {}});</script>";
/*      */             } else {
/* 1387 */               str1 = str1 + "<div><span id='field" + paramInt1 + "_" + str21 + "wrapspan'></span></div>";
/*      */               
/* 1389 */               str4 = "initDetailBrow(";
/* 1390 */               str4 = str4 + "    \"field" + paramInt1 + "_" + str21 + "\", \n";
/* 1391 */               str4 = str4 + "    \"" + paramString3 + "\", \n";
/* 1392 */               str4 = str4 + "    \"" + Util.toScreen(str13, i2).replaceAll("\\\\", "\\\\\\\\").replace("\"", "\\\"") + "\", \n";
/* 1393 */               str4 = str4 + "    \"" + str33.replaceAll("\\\\\"", "") + "\", \n";
/* 1394 */               str4 = str4 + "    " + BrowserManager.browIsSingle(paramInt2 + "") + ", \n";
/* 1395 */               str4 = str4 + "    \"" + ((paramInt9 == 1) ? "2" : "1") + "\", \n";
/* 1396 */               if (paramInt2 == 161 || paramInt2 == 162 || paramInt2 == 256 || paramInt2 == 257) {
/* 1397 */                 str4 = str4 + "    \"javascript:getajaxurl(" + paramInt2 + ",'" + str10 + "')\" , \n";
/*      */               } else {
/* 1399 */                 str4 = str4 + "    \"javascript:getajaxurl(" + paramInt2 + ")\", \n";
/*      */               } 
/* 1401 */               str4 = str4 + "    \"wfbrowvaluechange(this," + paramInt1 + ",  " + str21 + ")\", \n";
/* 1402 */               str4 = str4 + "    \"" + (!"".equals(str34) ? 1 : 0) + "\", \n";
/* 1403 */               str4 = str4 + "    \"" + (!"".equals(str34) ? str34.replaceAll("\\\\\"", "") : "") + "\", \n";
/* 1404 */               str4 = str4 + "    \"" + str12 + "\" \n";
/* 1405 */               str4 = str4 + ");\n";
/*      */               
/* 1407 */               str3 = "";
/* 1408 */               str3 = str3 + "initDetailBrow(";
/* 1409 */               str3 = str3 + "    \"field" + paramInt1 + "_" + str21 + "\", \n";
/* 1410 */               str3 = str3 + "    \"" + paramString3 + "\", \n";
/* 1411 */               str3 = str3 + "    \"" + Util.toScreen(str13, i2).replaceAll("\\\\", "\\\\\\\\").replace("\"", "\\\"") + "\", \n";
/* 1412 */               str3 = str3 + "    " + str33.replaceAll("\\\\\"", "") + ", \n";
/* 1413 */               str3 = str3 + "    " + BrowserManager.browIsSingle(paramInt2 + "") + ", \n";
/* 1414 */               str3 = str3 + "    \"" + ((paramInt9 == 1) ? "2" : "1") + "\", \n";
/* 1415 */               if (paramInt2 == 161 || paramInt2 == 162 || paramInt2 == 256 || paramInt2 == 257) {
/* 1416 */                 str3 = str3 + "    \"javascript:getajaxurl(" + paramInt2 + ",'" + str10 + "')\", \n";
/*      */               } else {
/* 1418 */                 str3 = str3 + "    \"javascript:getajaxurl(" + paramInt2 + ")\", \n";
/*      */               } 
/* 1420 */               str3 = str3 + "    \"wfbrowvaluechange(this," + paramInt1 + ",  " + str21 + ")\", \n";
/* 1421 */               str3 = str3 + "    \"" + (!"".equals(str34) ? 1 : 0) + "\", \n";
/* 1422 */               str3 = str3 + "    " + (!"".equals(str34) ? str34.replaceAll("\\\\\"", "") : "\"\"") + ", \n";
/* 1423 */               str3 = str3 + "    \"" + str12 + "\" \n";
/* 1424 */               str3 = str3 + ");\n";
/* 1425 */               str3 = "<script>jQuery(function () { try {" + str3 + "} catch(e) {}});</script>";
/*      */             } 
/*      */           } 
/*      */         } 
/* 1429 */         String str32 = str24 + str26 + str30 + str8 + str24;
/* 1430 */         str1 = str1 + "<input type='hidden' viewtype='" + paramInt9 + "' temptitle='" + paramString2 + "' id='field" + paramInt1 + "_" + str21 + "' name='field" + paramInt1 + "_" + str21 + "' value='" + paramString3 + "' " + str7 + " onpropertychange=" + str32 + " _listener=" + str32 + " />";
/* 1431 */         if (i1 == 0) {
/* 1432 */           str1 = str1 + "<span id='field" + paramInt1 + "_" + str21 + "span'>" + Util.toScreen(str13, i2) + str9;
/* 1433 */           str1 = str1 + "</span>";
/*      */         }
/* 1435 */         else if (paramInt8 != 1 || paramInt6 != 0) {
/* 1436 */           str1 = str1 + "<span id='field" + paramInt1 + "_" + str21 + "span'>" + Util.toScreen("".equals(str6) ? str13 : str6, i2) + str9;
/* 1437 */           str1 = str1 + "</span>";
/*      */         } 
/*      */         
/* 1440 */         if (paramInt2 == 87) {
/* 1441 */           str1 = str1 + "&nbsp;&nbsp;<A href='/meeting/report/MeetingRoomPlan.jsp' name='MeetingRoomPlanLink' target='_blank'>" + SystemEnv.getHtmlLabelName(2193, i2) + "</A>";
/*      */         }
/* 1443 */         if (arrayList.indexOf("" + paramInt1) >= 0) {
/* 1444 */           str1 = str1 + "<input type='hidden' name='oldfieldview" + paramInt1 + "_" + str21 + "' value='" + (paramInt7 + paramInt8 + paramInt9) + "' />";
/*      */         }
/*      */       } 
/* 1447 */     } catch (Exception exception) {
/* 1448 */       str1 = "";
/* 1449 */       writeLog(exception);
/*      */     } 
/*      */     
/* 1452 */     hashtable.put("jsStr", str2);
/* 1453 */     hashtable.put("inputStr", str1);
/* 1454 */     hashtable.put("detailinitjs", str3);
/* 1455 */     hashtable.put("detailbrowaddjs", str4);
/* 1456 */     return hashtable;
/*      */   }
/*      */ 
/*      */   
/*      */   public boolean isChineseCharacter(String paramString) {
/* 1461 */     char[] arrayOfChar = paramString.toCharArray();
/* 1462 */     for (byte b = 0; b < arrayOfChar.length; b++) {
/* 1463 */       if (arrayOfChar[b] >= '一' && arrayOfChar[b] <= '龻') {
/* 1464 */         return true;
/*      */       }
/*      */     } 
/* 1467 */     return false;
/*      */   }
/*      */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/field/ButtonElement.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */