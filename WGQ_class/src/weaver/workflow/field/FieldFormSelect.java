/*     */ package weaver.workflow.field;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class FieldFormSelect
/*     */   extends BaseBean
/*     */ {
/*  24 */   private ArrayList fieldids = new ArrayList();
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  29 */   private ArrayList isviewArray = new ArrayList();
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  34 */   private ArrayList iseditArray = new ArrayList();
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  39 */   private ArrayList ismandArray = new ArrayList();
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  44 */   private int fieldid = 0;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  49 */   private String isview = "";
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  54 */   private String isedit = "";
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  59 */   private String ismand = "";
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  64 */   private int nodeid = 0;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  69 */   private int index = 0;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void reset() {
/*  85 */     this.nodeid = 0;
/*  86 */     this.fieldids = null;
/*  87 */     this.isviewArray = null;
/*  88 */     this.iseditArray = null;
/*  89 */     this.ismandArray = null;
/*  90 */     this.index = 0;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setNodeid(int paramInt) {
/* 101 */     this.nodeid = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getNodeid() {
/* 111 */     return this.nodeid;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setFieldid(int paramInt) {
/* 121 */     this.fieldid = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getFieldid() {
/* 131 */     return this.fieldid;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getIsview() {
/* 141 */     this.index = this.fieldids.indexOf(this.fieldid + "");
/* 142 */     writeLog(this.index + "");
/* 143 */     return this.isviewArray.get(this.index);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getIsedit() {
/* 153 */     this.index = this.fieldids.indexOf(this.fieldid + "");
/* 154 */     return this.iseditArray.get(this.index);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getIsmand() {
/* 164 */     this.index = this.fieldids.indexOf(this.fieldid + "");
/* 165 */     return this.ismandArray.get(this.index);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void getFieldInfo() throws Exception {
/* 175 */     String str = "select * from workflow_nodeform where nodeid=" + this.nodeid;
/* 176 */     RecordSet recordSet = new RecordSet();
/*     */     
/*     */     try {
/* 179 */       recordSet.executeSql(str);
/* 180 */       writeLog("the sql:" + str);
/* 181 */       while (recordSet.next())
/*     */       {
/* 183 */         String str1 = recordSet.getString("fieldid");
/* 184 */         String str2 = recordSet.getString("isview");
/* 185 */         String str3 = recordSet.getString("isedit");
/* 186 */         String str4 = recordSet.getString("ismandatory");
/* 187 */         this.fieldids.add(str1);
/* 188 */         this.isviewArray.add(str2);
/* 189 */         this.iseditArray.add(str3);
/* 190 */         this.ismandArray.add(str4);
/*     */       }
/*     */     
/* 193 */     } catch (Exception exception) {
/*     */       
/* 195 */       writeLog(exception);
/* 196 */       throw exception;
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/field/FieldFormSelect.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */