/*     */ package weaver.workflow.field;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import weaver.conn.RecordSetTrans;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class WorkflowSubwfFieldManager
/*     */   extends BaseBean
/*     */ {
/*     */   public boolean hasSubWfSetting(RecordSetTrans paramRecordSetTrans, int paramInt1, int paramInt2) {
/*  33 */     return hasSubWfSetting(paramRecordSetTrans, paramInt1, 0, paramInt2);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private boolean hasSubWfSetting(RecordSetTrans paramRecordSetTrans, int paramInt1, int paramInt2, int paramInt3) {
/*  46 */     boolean bool = false;
/*     */     
/*     */     try {
/*  49 */       String str = "";
/*  50 */       StringBuffer stringBuffer = null;
/*  51 */       if (paramInt3 == 1) {
/*  52 */         stringBuffer = new StringBuffer();
/*  53 */         stringBuffer.append(" select id from workflow_base ")
/*  54 */           .append("  where isBill='1' ")
/*  55 */           .append("    and exists(select 1 from workflow_billfield  where billId=workflow_base.formId and id=").append(paramInt1).append(" )");
/*  56 */         paramRecordSetTrans.executeSql(stringBuffer.toString());
/*  57 */         while (paramRecordSetTrans.next()) {
/*  58 */           str = str + "," + paramRecordSetTrans.getString("id");
/*     */         }
/*     */       } else {
/*  61 */         stringBuffer = new StringBuffer();
/*  62 */         stringBuffer.append(" select id from workflow_base ")
/*  63 */           .append("  where 1=1 ");
/*  64 */         if (paramInt2 > 0) {
/*  65 */           stringBuffer.append("    and formid=").append(paramInt2);
/*     */         }
/*  67 */         stringBuffer.append("    and isBill='0' ")
/*  68 */           .append("    and exists(select 1 from workflow_formfield  where formId=workflow_base.formId and fieldid=").append(paramInt1).append(" )");
/*  69 */         paramRecordSetTrans.executeSql(stringBuffer.toString());
/*  70 */         while (paramRecordSetTrans.next()) {
/*  71 */           str = str + "," + paramRecordSetTrans.getString("id");
/*     */         }
/*     */       } 
/*     */       
/*  75 */       if (!str.equals("")) {
/*  76 */         str = str.substring(1);
/*     */       }
/*     */       
/*  79 */       if (str.equals("")) {
/*  80 */         return bool;
/*     */       }
/*     */       
/*  83 */       stringBuffer = new StringBuffer();
/*  84 */       stringBuffer.append(" select 1 from Workflow_SubwfSet ")
/*  85 */         .append("  where subwfCreatorFieldId=").append(paramInt1).append(" ")
/*  86 */         .append("   and mainWorkflowId in(").append(str).append(") ");
/*     */       
/*  88 */       paramRecordSetTrans.executeSql(stringBuffer.toString());
/*  89 */       if (paramRecordSetTrans.next()) {
/*  90 */         bool = true;
/*  91 */         return bool;
/*     */       } 
/*     */       
/*  94 */       stringBuffer = new StringBuffer();
/*  95 */       stringBuffer.append(" select 1 from Workflow_SubwfSetDetail  ")
/*  96 */         .append("  where mainWorkflowFieldId=").append(paramInt1).append(" ")
/*  97 */         .append("    and exists( select 1 from Workflow_SubwfSet where id=Workflow_SubwfSetDetail.subwfSetId and mainWorkflowId in(").append(str).append(")) ");
/*     */       
/*  99 */       paramRecordSetTrans.executeSql(stringBuffer.toString());
/* 100 */       if (paramRecordSetTrans.next()) {
/* 101 */         bool = true;
/* 102 */         return bool;
/*     */       } 
/*     */       
/* 105 */       stringBuffer = new StringBuffer();
/* 106 */       stringBuffer.append(" select 1 from Workflow_SubwfSetDetail  ")
/* 107 */         .append("  where subWorkflowFieldId=").append(paramInt1).append(" ")
/* 108 */         .append("    and exists( select 1 from Workflow_SubwfSet where id=Workflow_SubwfSetDetail.subwfSetId and subWorkflowId in(").append(str).append(")) ");
/*     */       
/* 110 */       paramRecordSetTrans.executeSql(stringBuffer.toString());
/* 111 */       if (paramRecordSetTrans.next()) {
/* 112 */         bool = true;
/* 113 */         return bool;
/*     */       } 
/*     */       
/* 116 */       stringBuffer = new StringBuffer();
/* 117 */       stringBuffer.append(" select 1 from Workflow_TriDiffWfDiffField ")
/* 118 */         .append(" where fieldId=").append(paramInt1).append(" ")
/* 119 */         .append("   and mainWorkflowId in(").append(str).append(") ");
/*     */       
/* 121 */       paramRecordSetTrans.executeSql(stringBuffer.toString());
/* 122 */       if (paramRecordSetTrans.next()) {
/* 123 */         bool = true;
/* 124 */         return bool;
/*     */       } 
/*     */       
/* 127 */       stringBuffer = new StringBuffer();
/* 128 */       stringBuffer.append(" select 1 from Workflow_TriDiffWfSubWf ")
/* 129 */         .append("  where subwfCreatorFieldId=").append(paramInt1).append(" ")
/* 130 */         .append("    and exists(select 1 from Workflow_TriDiffWfDiffField where id=Workflow_TriDiffWfSubWf.triDiffWfDiffFieldId and mainWorkflowId in(").append(str).append(")) ")
/* 131 */         .append("  ");
/*     */       
/* 133 */       paramRecordSetTrans.executeSql(stringBuffer.toString());
/* 134 */       if (paramRecordSetTrans.next()) {
/* 135 */         bool = true;
/* 136 */         return bool;
/*     */       } 
/*     */       
/* 139 */       stringBuffer = new StringBuffer();
/* 140 */       stringBuffer.append(" select 1 from Workflow_TriDiffWfSubWf ")
/* 141 */         .append("  where exists(select 1 from Workflow_TriDiffWfDiffField where id=Workflow_TriDiffWfSubWf.triDiffWfDiffFieldId and mainWorkflowId in(").append(str).append(")) ")
/* 142 */         .append("    and exists(select 1 from Workflow_TriDiffWfSubWfField where triDiffWfSubWfId=Workflow_TriDiffWfSubWf.id and mainWorkflowFieldId=").append(paramInt1).append(") ");
/*     */       
/* 144 */       paramRecordSetTrans.executeSql(stringBuffer.toString());
/* 145 */       if (paramRecordSetTrans.next()) {
/* 146 */         bool = true;
/* 147 */         return bool;
/*     */       } 
/*     */       
/* 150 */       stringBuffer = new StringBuffer();
/* 151 */       stringBuffer.append(" select 1 from Workflow_TriDiffWfSubWf ")
/* 152 */         .append("  where exists(select 1 from Workflow_TriDiffWfDiffField where id=Workflow_TriDiffWfSubWf.triDiffWfDiffFieldId and subWorkflowId in(").append(str).append(")) ")
/* 153 */         .append("    and exists(select 1 from Workflow_TriDiffWfSubWfField where triDiffWfSubWfId=Workflow_TriDiffWfSubWf.id and subWorkflowFieldId=").append(paramInt1).append(") ");
/*     */       
/* 155 */       paramRecordSetTrans.executeSql(stringBuffer.toString());
/* 156 */       if (paramRecordSetTrans.next()) {
/* 157 */         bool = true;
/* 158 */         return bool;
/*     */       } 
/*     */       
/* 161 */       stringBuffer = new StringBuffer();
/* 162 */       stringBuffer.append(" select 1 from Workflow_DistributionSummary where ( subfieldid = ").append(paramInt1);
/* 163 */       if (paramInt2 > 0) {
/* 164 */         stringBuffer.append(" and subformid = ").append(paramInt2);
/*     */       }
/* 166 */       stringBuffer.append(" and mainfieldid != -1)");
/* 167 */       stringBuffer.append(" or ( mainfieldid = ").append(paramInt1);
/* 168 */       if (paramInt2 > 0) {
/* 169 */         stringBuffer.append(" and mainformid = ").append(paramInt2);
/*     */       }
/* 171 */       stringBuffer.append(" and subfieldid != -1)");
/* 172 */       paramRecordSetTrans.executeSql(stringBuffer.toString());
/* 173 */       if (paramRecordSetTrans.next()) {
/* 174 */         bool = true;
/* 175 */         return bool;
/*     */       }
/*     */     
/* 178 */     } catch (Exception exception) {
/* 179 */       writeLog("e=" + exception);
/*     */     } 
/*     */     
/* 182 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean hasSubWfSettingForForm(String paramString, int paramInt1, int paramInt2) {
/* 194 */     boolean bool = false;
/* 195 */     RecordSetTrans recordSetTrans = new RecordSetTrans();
/* 196 */     recordSetTrans.setAutoCommit(false);
/*     */     try {
/* 198 */       String str = "";
/* 199 */       ArrayList<String> arrayList = new ArrayList();
/* 200 */       ArrayList arrayList1 = Util.TokenizerString(paramString, ",");
/* 201 */       recordSetTrans.executeSql("select * from workflow_formfield where formid=" + paramInt1);
/* 202 */       while (recordSetTrans.next()) {
/* 203 */         str = recordSetTrans.getString("fieldid");
/* 204 */         if (arrayList1.indexOf(str) == -1) {
/* 205 */           arrayList.add(str);
/*     */         }
/*     */       } 
/*     */       
/* 209 */       for (byte b = 0; b < arrayList.size(); b++) {
/* 210 */         if (hasSubWfSetting(recordSetTrans, Util.getIntValue(arrayList.get(b), 0), paramInt1, paramInt2)) {
/* 211 */           bool = true;
/* 212 */           return bool;
/*     */         } 
/*     */       } 
/*     */       
/* 216 */       recordSetTrans.commit();
/* 217 */     } catch (Exception exception) {
/* 218 */       recordSetTrans.rollback();
/*     */     } 
/* 220 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean hasSubWfSettingForBill(int paramInt1, int paramInt2) {
/* 231 */     boolean bool = false;
/* 232 */     RecordSetTrans recordSetTrans = new RecordSetTrans();
/* 233 */     recordSetTrans.setAutoCommit(false);
/*     */     try {
/* 235 */       if (hasSubWfSetting(recordSetTrans, paramInt1, paramInt2)) {
/* 236 */         bool = true;
/* 237 */         return bool;
/*     */       } 
/*     */       
/* 240 */       recordSetTrans.commit();
/* 241 */     } catch (Exception exception) {
/* 242 */       recordSetTrans.rollback();
/*     */     } 
/* 244 */     return bool;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/field/WorkflowSubwfFieldManager.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */