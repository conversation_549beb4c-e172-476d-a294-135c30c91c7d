/*     */ package weaver.workflow.design;
/*     */ 
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.workflow.field.BrowserComInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class WFDesignTransMethod
/*     */   extends BaseBean
/*     */ {
/*  25 */   private BrowserComInfo browserComInfo = new BrowserComInfo();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getFieldLabel(String paramString1, String paramString2) {
/*  36 */     String str1 = "";
/*  37 */     String[] arrayOfString = Util.TokenizerString2(paramString2, "+");
/*  38 */     String str2 = arrayOfString[0];
/*  39 */     int i = Util.getIntValue(arrayOfString[1]);
/*  40 */     if (i == 1) {
/*  41 */       str1 = SystemEnv.getHtmlLabelName(Util.getIntValue(paramString1), Util.getIntValue(str2, 7));
/*     */     } else {
/*  43 */       str1 = paramString1;
/*  44 */     }  return str1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getFieldViewType(String paramString1, String paramString2) {
/*  55 */     String str = "";
/*  56 */     if (paramString1.equals("1")) {
/*  57 */       str = SystemEnv.getHtmlLabelName(18021, Util.getIntValue(paramString2, 7));
/*     */     } else {
/*     */       
/*  60 */       str = SystemEnv.getHtmlLabelName(18020, Util.getIntValue(paramString2, 7));
/*     */     } 
/*  62 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getFieldType(String paramString1, String paramString2) {
/*  73 */     String str1 = "";
/*  74 */     String[] arrayOfString = Util.TokenizerString2(paramString2, "+");
/*  75 */     String str2 = arrayOfString[0];
/*  76 */     String str3 = arrayOfString[1];
/*  77 */     if (str3.equals("1")) {
/*  78 */       str1 = SystemEnv.getHtmlLabelName(21740, Util.getIntValue(str2, 7));
/*     */     }
/*  80 */     else if (str3.equals("2")) {
/*  81 */       str1 = SystemEnv.getHtmlLabelName(21740, Util.getIntValue(str2, 7));
/*     */     } 
/*     */ 
/*     */     
/*  85 */     return str1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getFieldShowType(String paramString1, String paramString2) {
/*  96 */     String str1 = "";
/*  97 */     String[] arrayOfString = Util.TokenizerString2(paramString2, "+");
/*  98 */     int i = Util.getIntValue(arrayOfString[1], 7);
/*  99 */     String str2 = arrayOfString[0];
/* 100 */     String str3 = arrayOfString[2];
/* 101 */     int j = Util.getIntValue(paramString1, 0);
/* 102 */     int k = 0;
/* 103 */     switch (j) {
/*     */       case 1:
/* 105 */         k = 688;
/*     */         break;
/*     */       case 2:
/* 108 */         k = 689;
/*     */         break;
/*     */       case 3:
/* 111 */         k = 32306;
/*     */         break;
/*     */       case 4:
/* 114 */         k = 691;
/*     */         break;
/*     */       case 5:
/* 117 */         k = 690;
/*     */         break;
/*     */       case 6:
/* 120 */         k = 17616;
/*     */         break;
/*     */       case 7:
/* 123 */         k = 21691;
/*     */         break;
/*     */       case 9:
/* 126 */         k = 125583;
/*     */         break;
/*     */       default:
/* 129 */         k = 0;
/*     */         break;
/*     */     } 
/* 132 */     if (k != 0)
/* 133 */       str1 = SystemEnv.getHtmlLabelName(k, i); 
/* 134 */     if (!paramString1.equals("4") && !paramString1.equals("9"))
/*     */     {
/*     */       
/* 137 */       if (!str2.equals("") && k != 0) {
/* 138 */         str1 = str1 + " - ";
/*     */       }
/*     */     }
/*     */     
/* 142 */     String str4 = "";
/* 143 */     String str5 = "";
/* 144 */     if (paramString1.equals("1")) {
/* 145 */       if (str2.equals("1"))
/* 146 */         str4 = SystemEnv.getHtmlLabelName(608, i); 
/* 147 */       if (str2.equals("2"))
/* 148 */         str4 = SystemEnv.getHtmlLabelName(696, i); 
/* 149 */       if (str2.equals("3"))
/* 150 */         str4 = SystemEnv.getHtmlLabelName(697, i); 
/* 151 */       if (str2.equals("4"))
/* 152 */         str4 = SystemEnv.getHtmlLabelName(18004, i); 
/* 153 */       if (str2.equals("5"))
/* 154 */         str4 = SystemEnv.getHtmlLabelName(22395, i); 
/*     */     } 
/* 156 */     if (paramString1.equals("2"))
/* 157 */       str4 = SystemEnv.getHtmlLabelName(689, i); 
/* 158 */     if (paramString1.equals("3")) {
/* 159 */       str4 = SystemEnv.getHtmlLabelName(Util.getIntValue(this.browserComInfo.getBrowserlabelid(str2)), i);
/*     */     }
/* 161 */     if (paramString1.equals("4"));
/*     */     
/* 163 */     if (paramString1.equals("5")) {
/*     */       
/* 165 */       if (str2.equals("1"))
/* 166 */         str4 = SystemEnv.getHtmlLabelName(127057, i); 
/* 167 */       if (str2.equals("2"))
/* 168 */         str4 = SystemEnv.getHtmlLabelName(127058, i); 
/* 169 */       if (str2.equals("3")) {
/* 170 */         str4 = SystemEnv.getHtmlLabelName(127059, i);
/*     */       }
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 180 */     if (paramString1.equals("6")) {
/*     */       
/* 182 */       if (str2.equals("1"))
/* 183 */         str4 = SystemEnv.getHtmlLabelName(20798, i); 
/* 184 */       if (str2.equals("2"))
/* 185 */         str4 = SystemEnv.getHtmlLabelName(20001, i); 
/*     */     } 
/* 187 */     if (paramString1.equals("7")) {
/* 188 */       if (str2.equals("1"))
/* 189 */         str4 = SystemEnv.getHtmlLabelName(21692, i); 
/* 190 */       if (str2.equals("2"))
/* 191 */         str4 = SystemEnv.getHtmlLabelName(21693, i); 
/*     */     } 
/* 193 */     str1 = str1 + str4;
/* 194 */     return str1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getSelectVal(String paramString1, String paramString2) {
/* 205 */     RecordSet recordSet = new RecordSet();
/* 206 */     String str = "";
/* 207 */     if (paramString1.equals("5")) {
/* 208 */       recordSet.executeSql("select listorder,selectvalue,selectname from workflow_SelectItem where fieldid=" + paramString2 + " order by listorder,selectvalue ");
/* 209 */       str = "[";
/*     */       
/* 211 */       str = str + "{\"label\":\"\",\"value\":\"-1\"},";
/* 212 */       while (recordSet.next()) {
/* 213 */         String str1 = recordSet.getString("selectname");
/* 214 */         str1 = str1.replace("\\", "").replace("'", "").replace("\"", "");
/* 215 */         str1 = str1.replace("<", "&lt;").replace(">", "&gt;");
/*     */         
/* 217 */         String str2 = recordSet.getString("selectvalue");
/* 218 */         str = str + "{\"label\":\"" + str1 + "\",\"value\":\"" + str2 + "\"},";
/*     */       } 
/* 220 */       if (str.contains(","))
/* 221 */         str = str.substring(0, str.lastIndexOf(",")); 
/* 222 */       str = str + "]";
/*     */     } 
/* 224 */     return str;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/design/WFDesignTransMethod.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */