/*     */ package weaver.workflow.imports.services;
/*     */ 
/*     */ import com.engine.odoc.util.OdocRequestdocUtil;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Collections;
/*     */ import java.util.Comparator;
/*     */ import java.util.HashMap;
/*     */ import java.util.Iterator;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import java.util.regex.Matcher;
/*     */ import java.util.regex.Pattern;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.workflow.cloudimports.cloudinterface.CloudInterface;
/*     */ import weaver.workflow.cloudimports.cloudinterface.DatasBean;
/*     */ import weaver.workflow.field.BrowserComInfo;
/*     */ import weaver.workflow.imports.datas.XmlBean;
/*     */ 
/*     */ 
/*     */ public class CrossSystemService
/*     */   extends BaseBean
/*     */ {
/*  25 */   private static Map<String, Boolean> existTableCache = new HashMap<>();
/*  26 */   private static Map<String, ArrayList<String>> tableColumnCache = new HashMap<>();
/*  27 */   private static Map<String, String> columnTypeCache = new HashMap<>();
/*  28 */   private static int sysVersion = 7;
/*  29 */   private static int spVersion = 0;
/*     */   
/*     */   private static boolean supportLocationField = false;
/*     */   private static boolean layoutFilterPortal = false;
/*     */   private static boolean underE81603 = false;
/*  34 */   private BrowserComInfo browserComInfo = new BrowserComInfo();
/*  35 */   private Map<String, String> specialInfoMap = new HashMap<>();
/*     */   
/*     */   static {
/*  38 */     init();
/*     */   }
/*     */   
/*     */   private static void init() {
/*  42 */     RecordSet recordSet = new RecordSet();
/*  43 */     recordSet.executeSql("select * from workflow_fieldtype where id=9");
/*  44 */     if (recordSet.next())
/*  45 */       supportLocationField = true; 
/*  46 */     recordSet.executeSql("select cversion from license");
/*  47 */     if (recordSet.next()) {
/*  48 */       String str = recordSet.getString(1).trim();
/*  49 */       sysVersion = Util.getIntValue(str.substring(0, str.indexOf(".")));
/*  50 */       if (str.indexOf("KB") > -1)
/*  51 */         spVersion = Util.getIntValue(str.substring(str.length() - 4)); 
/*  52 */       if (sysVersion == 8 && (str.indexOf("1506") > -1 || str.indexOf("1507") > -1 || str.indexOf("1508") > -1))
/*  53 */         layoutFilterPortal = true; 
/*     */     } 
/*     */     try {
/*  56 */       underE81603 = judgeTableFieldNonExist("workflow_flownode", "isSelectRejectNode");
/*  57 */     } catch (Exception exception) {}
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getDefaultSubCompanyid() {
/*  64 */     RecordSet recordSet = new RecordSet();
/*  65 */     String str1 = "select wfdetachable,wfdftsubcomid from SystemSet";
/*  66 */     if (sysVersion == 7)
/*  67 */       str1 = "select detachable,dftsubcomid from SystemSet"; 
/*  68 */     recordSet.executeSql(str1);
/*  69 */     String str2 = "";
/*  70 */     if (recordSet.next() && Util.getIntValue(recordSet.getString(1)) == 1)
/*  71 */       str2 = recordSet.getString(2); 
/*  72 */     return str2;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getTablename(String paramString) {
/*  79 */     RecordSet recordSet = new RecordSet();
/*     */     
/*  81 */     String str = "";
/*  82 */     int i = Util.getIntValue(paramString, 0);
/*  83 */     if (i == 0)
/*  84 */       return str; 
/*  85 */     if (i > 0)
/*  86 */       i -= i * 2; 
/*  87 */     recordSet.executeSql("select * from workflow_bill where id=" + i);
/*  88 */     if (recordSet.next())
/*  89 */       str = recordSet.getString("tablename"); 
/*  90 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean judgeExistTable(String paramString) throws Exception {
/* 100 */     if (existTableCache.containsKey(paramString))
/* 101 */       return ((Boolean)existTableCache.get(paramString)).booleanValue(); 
/* 102 */     boolean bool = judgeExistTableNoCache(paramString);
/* 103 */     existTableCache.put(paramString, Boolean.valueOf(bool));
/* 104 */     return bool;
/*     */   }
/*     */   
/*     */   public static boolean judgeExistTableNoCache(String paramString) throws Exception {
/* 108 */     boolean bool = false;
/* 109 */     RecordSet recordSet = new RecordSet();
/* 110 */     boolean bool1 = "oracle".equalsIgnoreCase(recordSet.getDBType());
/* 111 */     boolean bool2 = "db2".equalsIgnoreCase(recordSet.getDBType());
/*     */     try {
/* 113 */       String str = "";
/* 114 */       if (bool1) {
/* 115 */         str = "select 1 from user_tables where table_name = upper('" + paramString + "')";
/* 116 */       } else if (bool2) {
/* 117 */         str = "select 1 from SYSIBM.SYSTABLES where lower(name) = lower('" + paramString + "')";
/* 118 */       } else if (recordSet.getDBType().equals("mysql")) {
/* 119 */         str = "select 1 from information_schema.Tables where table_schema = database() and Table_Name='" + paramString + "' ";
/* 120 */       } else if (recordSet.getDBType().equals("postgresql")) {
/* 121 */         str = "select 1 from information_schema.Tables where table_schema = 'public' and lower(Table_Name)=lower('" + paramString + "') ";
/*     */       } else {
/* 123 */         str = "select 1 from sysobjects where name = '" + paramString + "' and objectproperty(id, 'IsUserTable') = 1";
/* 124 */       }  recordSet.executeSql(str);
/* 125 */       if (recordSet.next())
/* 126 */         bool = true; 
/* 127 */     } catch (Exception exception) {
/* 128 */       exception.printStackTrace();
/* 129 */       throw new Exception("判断表" + paramString + "是否存在出现异常", exception);
/*     */     } 
/* 131 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean judgeTableFieldNonExist(String paramString1, String paramString2) throws Exception {
/* 138 */     String str = getCurDBColumnType(paramString1, paramString2);
/* 139 */     return "columnNotExist".equals(str);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getCurDBColumnType(String paramString1, String paramString2) throws Exception {
/* 150 */     String str1 = paramString1 + "#" + paramString2;
/* 151 */     if (columnTypeCache.containsKey(str1))
/* 152 */       return columnTypeCache.get(str1); 
/* 153 */     String str2 = "columnNotExist";
/* 154 */     RecordSet recordSet = new RecordSet();
/* 155 */     boolean bool = "oracle".equalsIgnoreCase(recordSet.getDBType());
/*     */     try {
/* 157 */       String str = "";
/* 158 */       if (bool) {
/* 159 */         str = "select data_type from user_tab_columns where LOWER(table_name) = '" + paramString1.toLowerCase() + "' and LOWER(column_name) = '" + paramString2.toLowerCase() + "'";
/* 160 */       } else if (recordSet.getDBType().equals("mysql")) {
/* 161 */         str = "select data_type from information_schema.columns where table_schema = database() and Table_Name='" + paramString1 + "' and column_name = '" + paramString2.toLowerCase() + "'";
/* 162 */       } else if (recordSet.getDBType().equals("postgresql")) {
/* 163 */         str = "select data_type from information_schema.columns where table_schema = 'public' and Table_Name=lower('" + paramString1 + "') and column_name = lower('" + paramString2.toLowerCase() + "')";
/*     */       } else {
/* 165 */         str = "select b.name from syscolumns a inner join systypes b on a.xtype = b.xusertype where object_name(id) = '" + paramString1 + "' and a.name = '" + paramString2 + "'";
/* 166 */       }  recordSet.executeSql(str);
/* 167 */       if (recordSet.next())
/* 168 */         str2 = recordSet.getString(1).toLowerCase(); 
/* 169 */     } catch (Exception exception) {
/* 170 */       exception.printStackTrace();
/* 171 */       throw new Exception("获取表" + paramString1 + "中字段" + paramString2 + "当前库列类型出现异常", exception);
/*     */     } 
/* 173 */     columnTypeCache.put(str1, str2);
/* 174 */     return str2;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static ArrayList<String> getColumnsByTable(String paramString) throws Exception {
/* 184 */     if (tableColumnCache.containsKey(paramString))
/* 185 */       return tableColumnCache.get(paramString); 
/* 186 */     ArrayList<String> arrayList = new ArrayList();
/* 187 */     RecordSet recordSet = new RecordSet();
/* 188 */     boolean bool = "oracle".equalsIgnoreCase(recordSet.getDBType());
/*     */     try {
/* 190 */       String str = "";
/* 191 */       if (bool) {
/* 192 */         str = "select column_name from user_tab_columns where LOWER(table_name) = '" + paramString.toLowerCase() + "'";
/* 193 */       } else if (recordSet.getDBType().equals("mysql")) {
/* 194 */         str = "select column_name from information_schema.columns where table_schema = database() and Table_Name='" + paramString.toLowerCase() + "'";
/* 195 */       } else if (recordSet.getDBType().equals("postgresql")) {
/* 196 */         str = "select column_name from information_schema.columns where table_schema = 'public' and Table_Name=lower('" + paramString.toLowerCase() + "')";
/*     */       } else {
/* 198 */         str = "select name from syscolumns where object_name(id) = '" + paramString + "' and objectproperty(id, 'IsUserTable') = 1";
/* 199 */       }  recordSet.executeSql(str);
/* 200 */       while (recordSet.next()) {
/* 201 */         arrayList.add(recordSet.getString(1).toLowerCase());
/*     */       }
/* 203 */     } catch (Exception exception) {
/* 204 */       exception.printStackTrace();
/* 205 */       throw new Exception("获取表" + paramString + "对应所有列出现异常", exception);
/*     */     } 
/* 207 */     tableColumnCache.put(paramString, arrayList);
/* 208 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String convertFieldDBType(Map<String, String> paramMap, int paramInt) {
/* 215 */     String str1 = Util.null2String(paramMap.get("fieldhtmltype"));
/* 216 */     String str2 = Util.null2String(paramMap.get("type"));
/* 217 */     String str3 = Util.null2String(paramMap.get("fielddbtype"));
/* 218 */     return convertFieldDBType(str1, str2, str3, paramInt);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String convertFieldDBType(String paramString1, String paramString2, String paramString3, int paramInt) {
/* 230 */     String str = "";
/* 231 */     RecordSet recordSet = new RecordSet();
/* 232 */     boolean bool1 = "oracle".equalsIgnoreCase(recordSet.getDBType());
/* 233 */     boolean bool2 = "db2".equalsIgnoreCase(recordSet.getDBType());
/* 234 */     if (paramString1.equals("1")) {
/* 235 */       if (paramString2.equals("1")) {
/* 236 */         String str1 = "";
/* 237 */         Pattern pattern = Pattern.compile("\\d+\\)");
/* 238 */         Matcher matcher = pattern.matcher(paramString3);
/* 239 */         if (matcher.find())
/* 240 */           str1 = matcher.group(0); 
/* 241 */         if ("".equals(str1))
/* 242 */           str1 = "4000)"; 
/* 243 */         if (bool1)
/* 244 */         { str = "varchar2(" + str1; }
/* 245 */         else if (recordSet.getDBType().equals("mysql"))
/* 246 */         { str = "varchar(" + str1; }
/*     */         else
/* 248 */         { str = "varchar(" + str1; } 
/* 249 */       } else if (paramString2.equals("2")) {
/* 250 */         if (bool1)
/* 251 */         { str = "integer"; }
/*     */         else
/* 253 */         { str = "int"; } 
/* 254 */       } else if (paramString2.equals("3")) {
/* 255 */         String str1 = "";
/* 256 */         Pattern pattern = Pattern.compile("\\d+\\)");
/* 257 */         Matcher matcher = pattern.matcher(paramString3);
/* 258 */         if (matcher.find())
/* 259 */           str1 = matcher.group(0); 
/* 260 */         if ("".equals(str1))
/* 261 */           str1 = "2)"; 
/* 262 */         if (bool1)
/* 263 */         { str = "number(38," + str1; }
/*     */         else
/* 265 */         { str = "decimal(38," + str1; } 
/* 266 */       } else if (paramString2.equals("4")) {
/* 267 */         if (bool1)
/* 268 */         { str = "number(15,2)"; }
/*     */         else
/* 270 */         { str = "decimal(15,2)"; } 
/* 271 */       } else if (paramString2.equals("5")) {
/* 272 */         if (bool1)
/* 273 */         { str = "varchar2(30)"; }
/*     */         else
/* 275 */         { str = "varchar(30)"; } 
/*     */       } 
/* 277 */     } else if (paramString1.equals("2")) {
/* 278 */       if (bool1) {
/* 279 */         if (paramString2.equals("2"))
/* 280 */         { if (sysVersion <= 7 && spVersion < 1404) {
/* 281 */             str = "varchar2(4000)";
/*     */           } else {
/* 283 */             str = "clob";
/*     */           }  }
/* 285 */         else { str = "varchar2(4000)"; } 
/* 286 */       } else if (bool2) {
/* 287 */         str = "varchar(2000)";
/*     */       } else {
/* 289 */         str = "text";
/*     */       } 
/* 291 */     } else if (paramString1.equals("3")) {
/* 292 */       int i = Util.getIntValue(paramString2, 0);
/* 293 */       if (i == 161)
/* 294 */       { if (paramInt == 1)
/* 295 */           if (bool1) {
/* 296 */             str = "varchar2(1000)";
/*     */           } else {
/* 298 */             str = "varchar(1000)";
/*     */           }   }
/* 300 */       else if (i == 162 || i == 257)
/* 301 */       { if (paramInt == 1)
/* 302 */           if (bool1) {
/* 303 */             str = "clob";
/* 304 */           } else if (bool2) {
/* 305 */             str = "varchar(2000)";
/*     */           } else {
/* 307 */             str = "text";
/*     */           }   }
/* 309 */       else if (i == 118)
/* 310 */       { if (bool1)
/* 311 */         { str = "varchar2(200)"; }
/*     */         else
/* 313 */         { str = "varchar(200)"; }  }
/* 314 */       else if (i == 17)
/* 315 */       { if (bool1) {
/* 316 */           if (sysVersion <= 7 && spVersion < 1502)
/* 317 */           { str = "varchar2(4000)"; }
/*     */           else
/* 319 */           { str = "clob"; } 
/* 320 */         } else if (bool2) {
/* 321 */           str = "varchar(2000)";
/*     */         } else {
/* 323 */           str = "text";
/*     */         }  }
/* 325 */       else { str = this.browserComInfo.getBrowserdbtype(i + "");
/* 326 */         if (bool1 && "int".equalsIgnoreCase(str))
/* 327 */           str = "integer"; 
/* 328 */         if (bool1 && i == 141) {
/* 329 */           str = "clob";
/*     */         } }
/*     */       
/* 332 */       if (paramInt == 2 && (i == 161 || i == 162 || i == 224 || i == 226 || i == 256 || i == 257))
/*     */       {
/*     */         
/* 335 */         str = paramString3;
/*     */       }
/* 337 */     } else if (paramString1.equals("4")) {
/* 338 */       str = "char(1)";
/* 339 */     } else if (paramString1.equals("5")) {
/* 340 */       if (bool1) {
/* 341 */         if ("2".equals(paramString2)) {
/* 342 */           str = "varchar2(4000)";
/*     */         } else {
/* 344 */           str = "integer";
/*     */         }
/*     */       
/* 347 */       } else if ("2".equals(paramString2)) {
/* 348 */         str = "text";
/*     */       } else {
/* 350 */         str = "int";
/*     */       }
/*     */     
/* 353 */     } else if (paramString1.equals("6") || paramString1.equals("7")) {
/* 354 */       if (bool1)
/* 355 */       { str = "varchar2(4000)"; }
/* 356 */       else if (bool2)
/* 357 */       { str = "varchar(2000)"; }
/*     */       else
/* 359 */       { str = "text"; } 
/* 360 */     } else if (paramString1.equals("9")) {
/* 361 */       if (bool1) {
/* 362 */         str = "clob";
/*     */       } else {
/* 364 */         str = "text";
/*     */       } 
/* 366 */     }  writeLog("字段数据类型转换-" + paramInt + "：fieldHtmlType--" + paramString1 + " type--" + paramString2 + " oldDBType--" + paramString3 + " 转换后类型：" + str);
/* 367 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean judgeFieldSupport(Map<String, String> paramMap, String paramString) {
/* 374 */     String str = Util.null2String(paramMap.get("fieldhtmltype"));
/* 375 */     if ("3".equals(str) && 
/* 376 */       "".equals(convertFieldDBType(paramMap, 1))) {
/* 377 */       return false;
/*     */     }
/* 379 */     if ("6".equals(str) && sysVersion == 7) {
/* 380 */       paramString = paramString.toLowerCase();
/* 381 */       if ("workflow_formdictdetail".equals(paramString) || ("workflow_billfield".equals(paramString) && "1".equals(Util.null2String(paramMap.get("viewtype")))))
/* 382 */         return false; 
/*     */     } 
/* 384 */     if ("9".equals(str) && !supportLocationField) {
/* 385 */       return false;
/*     */     }
/* 387 */     return true;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void handleXmlFieldValues(String paramString, Map<String, String> paramMap1, Map<String, String> paramMap2) throws Exception {
/* 394 */     ArrayList<String> arrayList = getColumnsByTable(paramString);
/* 395 */     paramString = paramString.toLowerCase();
/*     */ 
/*     */     
/* 398 */     if ("htmllabelindex".equals(paramString) || "htmllabelinfo".equals(paramString)) {
/*     */       return;
/*     */     }
/*     */     
/* 402 */     if ("workflow_billfield".equals(paramString) || "workflow_formdict".equals(paramString) || "workflow_formdictdetail".equals(paramString)) {
/* 403 */       int i = Util.getIntValue((String)paramMap1.get("fieldhtmltype") + "");
/* 404 */       int j = Util.getIntValue((String)paramMap1.get("type") + "");
/* 405 */       if (i == 3 && (j == 165 || j == 166 || j == 167 || j == 168)) {
/* 406 */         boolean bool = paramMap1.containsKey("textheight_2");
/* 407 */         boolean bool1 = (arrayList.indexOf("textheight_2") > -1) ? true : false;
/* 408 */         if (bool && !bool1) {
/* 409 */           String str = "," + (String)paramMap1.get("textheight_2") + ",";
/* 410 */           if (str.indexOf(",2,") > -1 || str.indexOf(",20,") > -1 || str.indexOf(",21,") > -1 || str
/* 411 */             .indexOf(",22,") > -1 || str.indexOf(",23,") > -1) {
/* 412 */             str = "2";
/*     */           } else {
/* 414 */             str = "1";
/* 415 */           }  paramMap1.put("textheight", str);
/* 416 */         } else if (!bool && bool1) {
/* 417 */           paramMap1.put("textheight_2", paramMap1.get("textheight"));
/* 418 */           paramMap2.put("textheight_2", "varchar");
/*     */         } 
/*     */       } 
/*     */     } 
/* 422 */     if ("workflow_flownode".equals(paramString)) {
/* 423 */       if (paramMap1.containsKey("isselectrejectnode")) {
/* 424 */         this.specialInfoMap.put("xmlcontainnodereject", "1");
/* 425 */         if ("1".equals(Util.null2String(paramMap1.get("isrejectremind"))))
/* 426 */           this.specialInfoMap.put("node_isrejectremind", "1"); 
/* 427 */         if ("1".equals(Util.null2String(paramMap1.get("ischangrejectnode"))))
/* 428 */           this.specialInfoMap.put("node_ischangrejectnode", "1"); 
/* 429 */         if ("1".equals(Util.null2String(paramMap1.get("isselectrejectnode"))))
/* 430 */           this.specialInfoMap.put("node_isselectrejectnode", "1"); 
/*     */       } 
/* 432 */       if (paramMap1.containsKey("istakingopinions"))
/* 433 */         this.specialInfoMap.put("xmlcontaintakingopinions", "1"); 
/*     */     } 
/* 435 */     if ("workflow_nodecustomrcmenu".equals(paramString) && paramMap1.containsKey("subbackctrl")) {
/* 436 */       this.specialInfoMap.put("xmlcontainnodemenureform", "1");
/*     */     }
/* 438 */     Iterator<Map.Entry> iterator = paramMap1.entrySet().iterator();
/* 439 */     while (iterator.hasNext()) {
/* 440 */       Map.Entry entry = iterator.next();
/* 441 */       String str = (String)entry.getKey() + "";
/* 442 */       boolean bool1 = (paramString.equals("workflow_nodehtmllayout") && "filecontent".equals(str)) ? true : false;
/* 443 */       boolean bool2 = (paramString.equals("workflow_nodelink") && "linkorder".equals(str)) ? true : false;
/* 444 */       if (arrayList.indexOf(str) == -1 && !bool1 && !bool2) {
/* 445 */         iterator.remove();
/* 446 */         paramMap2.remove(str);
/* 447 */         writeLog("表" + paramString + "字段" + str + "不存在，过滤xml此信息！");
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setNodeLayoutIsactive(Map<String, String> paramMap) {
/*     */     try {
/* 458 */       RecordSet recordSet = new RecordSet();
/* 459 */       if (judgeTableFieldNonExist("workflow_nodehtmllayout", "isactive"))
/*     */         return; 
/* 461 */       String str1 = Util.null2String(paramMap.get("workflowid"));
/* 462 */       String str2 = Util.null2String(paramMap.get("nodeid"));
/* 463 */       String str3 = Util.null2String(paramMap.get("formid"));
/* 464 */       String str4 = Util.null2String(paramMap.get("type"));
/* 465 */       String str5 = " and workflowid=" + str1 + " and nodeid=" + str2 + " and formid=" + str3 + " and type=" + str4;
/* 466 */       int i = 0;
/* 467 */       recordSet.executeSql("select max(id) from workflow_nodehtmllayout where 1=1 " + str5);
/* 468 */       if (recordSet.next())
/* 469 */         i = Util.getIntValue(recordSet.getString(1)); 
/* 470 */       recordSet.executeSql("update workflow_nodehtmllayout set isactive = 0 where 1=1 " + str5);
/* 471 */       recordSet.executeSql("update workflow_nodehtmllayout set isactive = 1 where 1=1 and id=" + i + str5);
/* 472 */     } catch (Exception exception) {
/* 473 */       exception.printStackTrace();
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void sortFlowNodeLinks(List<XmlBean> paramList) {
/*     */     try {
/* 482 */       if (paramList.size() == 0)
/*     */         return; 
/* 484 */       if (!judgeTableFieldNonExist("workflow_nodelink", "linkorder"))
/*     */         return; 
/* 486 */       Collections.sort(paramList, new Comparator<XmlBean>() {
/*     */             public int compare(XmlBean param1XmlBean1, XmlBean param1XmlBean2) {
/* 488 */               int i = 0;
/* 489 */               if (param1XmlBean1.getFieldValues().containsKey("linkorder")) {
/* 490 */                 int j = Util.getIntValue((new StringBuilder()).append(param1XmlBean1.getFieldValues().get("linkorder")).append("").toString());
/* 491 */                 int k = Util.getIntValue((new StringBuilder()).append(param1XmlBean2.getFieldValues().get("linkorder")).append("").toString());
/* 492 */                 i = j - k;
/*     */               } 
/* 494 */               if (i == 0) {
/* 495 */                 int j = Util.getIntValue(param1XmlBean1.getPrimarykeyvalue());
/* 496 */                 int k = Util.getIntValue(param1XmlBean2.getPrimarykeyvalue());
/* 497 */                 i = j - k;
/*     */               } 
/* 499 */               return i;
/*     */             }
/*     */           });
/* 502 */     } catch (Exception exception) {
/* 503 */       writeLog(exception);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void specialHandleAfterImportSuccess(WorkflowDataService paramWorkflowDataService) {
/*     */     try {
/* 512 */       RecordSet recordSet = new RecordSet();
/* 513 */       String str1 = paramWorkflowDataService.getType();
/* 514 */       int i = Util.getIntValue(paramWorkflowDataService.getWorkflowid());
/* 515 */       int j = Util.getIntValue(paramWorkflowDataService.getFormid());
/* 516 */       int k = Util.getIntValue(paramWorkflowDataService.getIsbill());
/*     */ 
/*     */       
/* 519 */       writeLog("导入后workflowid:" + i + "   formid:" + j);
/*     */       
/* 521 */       String str2 = "update workflow_flownode set printflowcomment = 1 where  workflowid = ? and (printflowcomment is null or printflowcomment = '')";
/* 522 */       recordSet.executeUpdate(str2, new Object[] { Integer.valueOf(i) });
/* 523 */       if ("1".equals(str1)) {
/* 524 */         recordSet.executeSql("update workflow_base set iscust=0 where id=" + i + " and iscust is null");
/* 525 */         recordSet.executeSql("update workflow_nodelink set isreject='' where workflowid=" + i + " and isreject is null");
/*     */         
/* 527 */         if (k == 0) {
/* 528 */           recordSet.executeSql("select * from workflow_formfield where formid=" + j + " and (isdetail=0 or isdetail is null) order by fieldorder,fieldid");
/* 529 */           byte b = 0;
/* 530 */           RecordSet recordSet1 = new RecordSet();
/* 531 */           while (recordSet.next()) {
/* 532 */             recordSet1.executeSql("update workflow_formfield set fieldorder=" + b + " where formid=" + j + " and (isdetail=0 or isdetail is null) and fieldid=" + recordSet.getInt("fieldid"));
/* 533 */             b++;
/*     */           } 
/*     */         } 
/*     */         
/* 537 */         boolean bool1 = !judgeTableFieldNonExist("workflow_flownode", "isSelectRejectNode") ? true : false;
/* 538 */         boolean bool2 = this.specialInfoMap.containsKey("xmlcontainnodereject");
/* 539 */         if (bool2 && !bool1) {
/* 540 */           if ("1".equals(Util.null2String(this.specialInfoMap.get("node_isrejectremind")))) {
/* 541 */             recordSet.executeSql("update workflow_base set isRejectRemind='1' where id=" + i);
/* 542 */             if ("1".equals(Util.null2String(this.specialInfoMap.get("node_ischangrejectnode"))))
/* 543 */               recordSet.executeSql("update workflow_base set isChangRejectNode='1' where id=" + i); 
/*     */           } 
/* 545 */           if ("1".equals(Util.null2String(this.specialInfoMap.get("node_isselectrejectnode"))))
/* 546 */             recordSet.executeSql("update workflow_base set isSelectRejectNode='1' where id=" + i); 
/* 547 */         } else if (!bool2 && bool1) {
/* 548 */           recordSet.executeSql("update workflow_flownode set isRejectRemind='1' where workflowid in (select id from workflow_base where id=" + i + " and isRejectRemind='1')");
/* 549 */           recordSet.executeSql("update workflow_flownode set isChangRejectNode='1' where workflowid in (select id from workflow_base where id=" + i + " and isRejectRemind='1' and isChangRejectNode='1')");
/* 550 */           recordSet.executeSql("update workflow_flownode set isSelectRejectNode=1 where workflowid in(select id from workflow_base where id=" + i + " and isSelectRejectNode='1')");
/*     */         } 
/*     */         
/* 553 */         boolean bool3 = !judgeTableFieldNonExist("workflow_flownode", "isTakingOpinions") ? true : false;
/* 554 */         boolean bool4 = this.specialInfoMap.containsKey("xmlcontaintakingopinions");
/* 555 */         if (bool4 && !bool3) {
/* 556 */           recordSet.executeSql("update workflow_flownode set IsWaitForwardOpinion=0 where workflowid=" + i + " and IsPendingForward=1 and IsSubmitedOpinion=1 and IsWaitForwardOpinion=1");
/* 557 */         } else if (!bool4 && bool3) {
/* 558 */           recordSet.executeSql("update workflow_flownode set IsTakingOpinions=1 where workflowid=" + i + " and IsPendingForward=1 and IsWaitForwardOpinion=1");
/* 559 */           recordSet.executeSql("update workflow_flownode set IsHandleForward=1 where workflowid=" + i + " and IsPendingForward=1 and IsBeForwardModify=1");
/*     */         } 
/*     */         
/* 562 */         boolean bool5 = !judgeTableFieldNonExist("workflow_nodecustomrcmenu", "subbackctrl") ? true : false;
/* 563 */         boolean bool6 = this.specialInfoMap.containsKey("xmlcontainnodemenureform");
/* 564 */         if (!bool6 && bool5) {
/* 565 */           recordSet.executeSql("update workflow_nodecustomrcmenu set isshowinwflog=1, subbackCtrl=0, forhandbackCtrl=0, forsubbackCtrl=0, ccsubbackCtrl=0, takingOpinionsbackCtrl=0 where wfid=" + i);
/* 566 */           recordSet.executeSql("update workflow_nodecustomrcmenu set subbackCtrl = (case when (hasnoback = '1' and hasback = '1') then 2 when (hasnoback = '1' and (hasback != '1' or hasback is null)) then 1 else 0 end) where wfid=" + i);
/* 567 */           recordSet.executeSql("update workflow_nodecustomrcmenu set forhandbackCtrl = (case when (hasforhandnoback = '1' and hasforhandback = '1') then 2 when (hasforhandnoback = '1' and (hasforhandback != '1' or hasforhandback is null)) then 1 else 0 end) where wfid=" + i);
/* 568 */           recordSet.executeSql("update workflow_nodecustomrcmenu set forsubbackCtrl = (case when (hasfornoback = '1' and hasforback = '1') then 2 when (hasfornoback = '1' and (hasforback != '1' or hasforback is null)) then 1 else 0 end) where wfid=" + i);
/* 569 */           recordSet.executeSql("update workflow_nodecustomrcmenu set ccsubbackCtrl = (case when (hasccnoback = '1' and hasccback = '1') then 2 when (hasccnoback = '1' and (hasccback != '1' or hasccback is null)) then 1 else 0 end) where wfid=" + i);
/* 570 */           recordSet.executeSql("update workflow_nodecustomrcmenu set takingOpinionsbackCtrl = (case when (hastakingOpinionsnoback = '1' and hastakingOpinionsback = '1') then 2 when (hastakingOpinionsnoback = '1' and (hastakingOpinionsback != '1' or hastakingOpinionsback is null)) then 1 else 0 end) where wfid=" + i);
/* 571 */           recordSet.executeSql("update workflow_nodecustomrcmenu set submitName7 = (case when (subbackName7 is not null) then subbackName7 else submitName7 end), submitName8 = (case when (subbackName8 is not null) then subbackName8 else submitName8 end), submitName9 = (case when (subbackName9 is not null) then subbackName9 else submitName9 end) where hasback = '1' and wfid=" + i);
/* 572 */           recordSet.executeSql("update workflow_nodecustomrcmenu set subbackName7 = (case when (submitName7 is not null) then submitName7 else subbackName7 end), subbackName8 = (case when (submitName8 is not null) then submitName8 else subbackName8 end), subbackName9 = (case when (submitName9 is not null) then submitName9 else subbackName9 end) where (hasback != '1' or hasback is null) and wfid=" + i);
/* 573 */           recordSet.executeSql("update workflow_nodecustomrcmenu set forhandName7 = (case when (forhandbackName7 is not null) then forhandbackName7 else forhandName7 end), forhandName8 = (case when (forhandbackName8 is not null) then forhandbackName8 else forhandName8 end), forhandName9 = (case when (forhandbackName9 is not null) then forhandbackName9 else forhandName9 end) where hasforhandback = '1' and wfid=" + i);
/* 574 */           recordSet.executeSql("update workflow_nodecustomrcmenu set forhandbackName7 = (case when (forhandName7 is not null) then forhandName7 else forhandbackName7 end), forhandbackName8 = (case when (forhandName8 is not null) then forhandName8 else forhandbackName8 end), forhandbackName9 = (case when (forhandName9 is not null) then forhandName9 else forhandbackName9 end) where (hasforhandback != '1' or hasforhandback is null) and wfid=" + i);
/* 575 */           recordSet.executeSql("update workflow_nodecustomrcmenu set forsubName7 = (case when (forsubbackName7 is not null) then forsubbackName7 else forsubName7 end), forsubName8 = (case when (forsubbackName8 is not null) then forsubbackName8 else forsubName8 end), forsubName9 = (case when (forsubbackName9 is not null) then forsubbackName9 else forsubName9 end) where hasforback = '1' and wfid=" + i);
/* 576 */           recordSet.executeSql("update workflow_nodecustomrcmenu set forsubbackName7 = (case when (forsubName7 is not null) then forsubName7 else forsubbackName7 end), forsubbackName8 = (case when (forsubName8 is not null) then forsubName8 else forsubbackName8 end), forsubbackName9 = (case when (forsubName9 is not null) then forsubName9 else forsubbackName9 end) where (hasforback != '1' or hasforback is null) and wfid=" + i);
/* 577 */           recordSet.executeSql("update workflow_nodecustomrcmenu set ccsubName7 = (case when (ccsubbackName7 is not null) then ccsubbackName7 else ccsubName7 end), ccsubName8 = (case when (ccsubbackName8 is not null) then ccsubbackName8 else ccsubName8 end), ccsubName9 = (case when (ccsubbackName9 is not null) then ccsubbackName9 else ccsubName9 end) where hasccback = '1' and wfid=" + i);
/* 578 */           recordSet.executeSql("update workflow_nodecustomrcmenu set ccsubbackName7 = (case when (ccsubName7 is not null) then ccsubName7 else ccsubbackName7 end), ccsubbackName8 = (case when (ccsubName8 is not null) then ccsubName8 else ccsubbackName8 end), ccsubbackName9 = (case when (ccsubName9 is not null) then ccsubName9 else ccsubbackName9 end) where (hasccback != '1' or hasccback is null) and wfid=" + i);
/* 579 */           recordSet.executeSql("update workflow_nodecustomrcmenu set takingOpinionsName7 = (case when (takingOpinionsbackName7 is not null) then takingOpinionsbackName7 else takingOpinionsName7 end), takingOpinionsName8 = (case when (takingOpinionsbackName8 is not null) then takingOpinionsbackName8 else takingOpinionsName8 end), takingOpinionsName9 = (case when (takingOpinionsbackName9 is not null) then takingOpinionsbackName9 else takingOpinionsName9 end) where hastakingOpinionsback = '1' and wfid=" + i);
/* 580 */           recordSet.executeSql("update workflow_nodecustomrcmenu set takingOpinionsbackName7 = (case when (takingOpinionsName7 is not null) then takingOpinionsName7 else takingOpinionsbackName7 end), takingOpinionsbackName8 = (case when (takingOpinionsName8 is not null) then takingOpinionsName8 else takingOpinionsbackName8 end), takingOpinionsbackName9 = (case when (takingOpinionsName9 is not null) then takingOpinionsName9 else takingOpinionsbackName9 end) where (hastakingOpinionsback != '1' or hastakingOpinionsback is null) and wfid=" + i);
/*     */         } 
/*     */       } else {
/* 583 */         syncLinkNewRule(i);
/*     */       } 
/* 585 */       if (paramWorkflowDataService.isCloudImport()) {
/*     */         
/* 587 */         DatasBean datasBean = new DatasBean();
/* 588 */         datasBean.setOldworkflowid(Util.getIntValue(paramWorkflowDataService.getOldworkflowid()));
/* 589 */         datasBean.setWorkflowid(i);
/* 590 */         datasBean.setFormid(j);
/* 591 */         datasBean.setIsbill(k);
/* 592 */         datasBean.setFieldmap(paramWorkflowDataService.getFieldMap());
/* 593 */         datasBean.setNodemap(paramWorkflowDataService.getNodeMap());
/* 594 */         datasBean.setLinkmap(paramWorkflowDataService.getNodelinkMap());
/* 595 */         (new CloudInterface()).importBrowseSapAction(datasBean);
/*     */       } 
/*     */ 
/*     */       
/* 599 */       String str3 = paramWorkflowDataService.getIsComeFromOdocInit();
/* 600 */       String str4 = paramWorkflowDataService.getOdocWfType();
/* 601 */       if ("1".equals(str3)) {
/* 602 */         RecordSet recordSet1 = new RecordSet();
/*     */ 
/*     */         
/* 605 */         recordSet1.executeUpdate("update workflow_base set istemplate=1 where id=?", new Object[] { Integer.valueOf(i) });
/*     */         
/* 607 */         OdocRequestdocUtil odocRequestdocUtil = new OdocRequestdocUtil();
/* 608 */         Map map1 = paramWorkflowDataService.getFieldMap();
/* 609 */         Map map2 = paramWorkflowDataService.getNodeMap();
/* 610 */         odocRequestdocUtil.specialHandleAfterInitOdocSuccess(i, map1, map2, str4);
/*     */       }
/*     */     
/*     */     }
/* 614 */     catch (Exception exception) {
/* 615 */       writeLog("specialHandleAfterImportSuccess Exception：" + exception);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void syncLinkNewRule(int paramInt) {
/* 624 */     writeLog("syncLinkNewRule====更新newrule ====workflowid = " + paramInt);
/* 625 */     if (paramInt <= 0) {
/*     */       return;
/*     */     }
/* 628 */     RecordSet recordSet1 = new RecordSet();
/* 629 */     RecordSet recordSet2 = new RecordSet();
/* 630 */     recordSet1.executeQuery("select id from workflow_nodelink where workflowid = ?", new Object[] { Integer.valueOf(paramInt) });
/* 631 */     while (recordSet1.next()) {
/* 632 */       int i = Util.getIntValue(recordSet1.getString("id"));
/* 633 */       String str = "";
/* 634 */       recordSet2.executeQuery("select ruleid from rule_maplist where rulesrc=1 and linkid=? ", new Object[] { Integer.valueOf(i) });
/* 635 */       while (recordSet2.next()) {
/* 636 */         str = str + recordSet2.getString("ruleid") + ",";
/*     */       }
/* 638 */       if (!str.equals("")) {
/* 639 */         str = str.substring(0, str.length() - 1);
/*     */       }
/* 641 */       writeLog("syncLinkNewRule====更新newrule ====ruleIds = " + str);
/* 642 */       writeLog("syncLinkNewRule====更新newrule ====linkId = " + i);
/* 643 */       recordSet2.executeUpdate("update workflow_nodelink set newrule=? where id=? ", new Object[] { str, Integer.valueOf(i) });
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean saveRemarkDisplaySettings(String paramString) throws Exception {
/* 653 */     boolean bool = true;
/* 654 */     RecordSet recordSet = new RecordSet();
/* 655 */     boolean bool1 = judgeExistTable("workflow_remarkdisplay");
/* 656 */     if (bool1) {
/* 657 */       recordSet.executeProc("init_remarkdisplay2", paramString);
/*     */     }
/* 659 */     return bool;
/*     */   }
/*     */   
/*     */   public static boolean isLayoutFilterPortal() {
/* 663 */     return layoutFilterPortal;
/*     */   }
/*     */   
/*     */   public static boolean isUnderE81603() {
/* 667 */     return underE81603;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/imports/services/CrossSystemService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */