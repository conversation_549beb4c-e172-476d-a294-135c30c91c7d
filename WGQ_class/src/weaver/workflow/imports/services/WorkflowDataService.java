/*      */ package weaver.workflow.imports.services;
/*      */ import java.io.File;
/*      */ import java.io.InputStream;
/*      */ import java.lang.reflect.Method;
/*      */ import java.util.ArrayList;
/*      */ import java.util.Collection;
/*      */ import java.util.HashMap;
/*      */ import java.util.List;
/*      */ import java.util.Map;
/*      */ import java.util.Set;
/*      */ import org.jdom.Document;
/*      */ import org.jdom.Element;
/*      */ import org.jdom.input.SAXBuilder;
/*      */ import weaver.conn.RecordSet;
/*      */ import weaver.file.LogMan;
/*      */ import weaver.general.ThreadVarLanguage;
/*      */ import weaver.general.Util;
/*      */ import weaver.hrm.User;
/*      */ import weaver.security.util.SecurityMethodUtil;
/*      */ import weaver.systeminfo.SystemEnv;
/*      */ import weaver.workflow.field.DetailFieldComInfo;
/*      */ import weaver.workflow.field.FieldComInfo;
/*      */ import weaver.workflow.form.FormComInfo;
/*      */ import weaver.workflow.imports.datas.WorkflowData;
/*      */ import weaver.workflow.imports.datas.XmlBean;
/*      */ import weaver.workflow.imports.parses.WorkflowXml;
/*      */ import weaver.workflow.workflow.BillComInfo;
/*      */ import weaver.workflow.workflow.WorkTypeComInfo;
/*      */ import weaver.workflow.workflow.WorkflowBillComInfo;
/*      */ import weaver.workflow.workflow.WorkflowComInfo;
/*      */ 
/*      */ public class WorkflowDataService {
/*      */   private User user;
/*   34 */   private LogMan lm = LogMan.getInstance(); private String remoteAddr; private String type;
/*      */   private WorkflowXml xml;
/*      */   private Map htmlLabelMap;
/*      */   private String wfid;
/*      */   private String maintable;
/*      */   private List detailTableList;
/*      */   private Map formFields;
/*      */   private Map formDetailFields;
/*      */   private String formid;
/*      */   private String isbill;
/*      */   private String oldworkflowid;
/*      */   private String workflowid;
/*      */   private Map fieldMap;
/*      */   private Map nodeMap;
/*      */   private Map nodelinkMap;
/*      */   private Map MsgMap;
/*      */   private String exceptionMsg;
/*      */   private WorkTypeComInfo WorkTypeComInfo;
/*      */   private WorkflowComInfo WorkflowComInfo;
/*      */   private WorkflowBillComInfo WorkflowBillComInfo;
/*      */   private FormComInfo FormComInfo;
/*      */   private BillComInfo BillComInfo;
/*      */   private FieldComInfo FieldComInfo;
/*      */   private DetailFieldComInfo DetailFieldComInfo;
/*   58 */   private CrossSystemService crossSystem = new CrossSystemService();
/*      */   public static final boolean coverCloudImport = true;
/*      */   private boolean isCloudImport = false;
/*      */   private String isComeFromOdocInit;
/*      */   private String odocWfType;
/*      */   
/*      */   public WorkflowDataService() {
/*      */     try {
/*   66 */       this.WorkTypeComInfo = new WorkTypeComInfo();
/*   67 */       this.WorkflowComInfo = new WorkflowComInfo();
/*   68 */       this.WorkflowBillComInfo = new WorkflowBillComInfo();
/*   69 */       this.FormComInfo = new FormComInfo();
/*   70 */       this.BillComInfo = new BillComInfo();
/*   71 */       this.FieldComInfo = new FieldComInfo();
/*   72 */       this.DetailFieldComInfo = new DetailFieldComInfo();
/*   73 */     } catch (Exception exception) {}
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void importWorkflowByXml(String paramString) {
/*      */     try {
/*   83 */       File file = new File(paramString);
/*   84 */       this.xml = parseWorkflowXml(file);
/*   85 */     } catch (Exception exception) {
/*   86 */       this.exceptionMsg = "" + SystemEnv.getHtmlLabelName(32395, ThreadVarLanguage.getLang()) + "xml" + SystemEnv.getHtmlLabelName(10004285, ThreadVarLanguage.getLang()) + "";
/*      */       return;
/*      */     } 
/*   89 */     WorkflowData workflowData = new WorkflowData();
/*   90 */     workflowData.setUser(this.user);
/*   91 */     workflowData.setRemoteAddr(this.remoteAddr);
/*   92 */     workflowData.setType(this.type);
/*   93 */     workflowData.setCrossSystem(this.crossSystem);
/*   94 */     xmlConvertWorkflowInfo(workflowData);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void importWorkflowByXml(String paramString1, String paramString2, String paramString3) {
/*      */     try {
/*  102 */       File file = new File(paramString1);
/*  103 */       InputStream inputStream = null;
/*  104 */       inputStream = new BufferedInputStream(new FileInputStream(file));
/*  105 */       if (paramString2.equals("1")) {
/*      */         
/*      */         try {
/*  108 */           Class<?> clazz = Class.forName("weaver.file.AESCoder");
/*  109 */           Method method = clazz.getMethod("decrypt", new Class[] { InputStream.class, String.class });
/*  110 */           inputStream = (InputStream)method.invoke(null, new Object[] { inputStream, paramString3 });
/*  111 */         } catch (Exception exception) {
/*  112 */           exception.printStackTrace();
/*      */         } 
/*      */       }
/*  115 */       this.xml = parseWorkflowXml(inputStream);
/*  116 */     } catch (Exception exception) {
/*  117 */       this.exceptionMsg = "" + SystemEnv.getHtmlLabelName(32395, ThreadVarLanguage.getLang()) + "xml" + SystemEnv.getHtmlLabelName(10004285, ThreadVarLanguage.getLang()) + "";
/*      */       return;
/*      */     } 
/*  120 */     WorkflowData workflowData = new WorkflowData();
/*  121 */     workflowData.setUser(this.user);
/*  122 */     workflowData.setRemoteAddr(this.remoteAddr);
/*  123 */     workflowData.setType(this.type);
/*  124 */     workflowData.setCrossSystem(this.crossSystem);
/*  125 */     xmlConvertWorkflowInfo(workflowData);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void importWorkflowByXml(InputStream paramInputStream) {
/*      */     try {
/*  133 */       this.xml = parseWorkflowXml(paramInputStream);
/*  134 */     } catch (Exception exception) {
/*  135 */       this.exceptionMsg = "" + SystemEnv.getHtmlLabelName(32395, ThreadVarLanguage.getLang()) + "xml" + SystemEnv.getHtmlLabelName(10004285, ThreadVarLanguage.getLang()) + "";
/*      */       return;
/*      */     } 
/*  138 */     WorkflowData workflowData = new WorkflowData();
/*  139 */     workflowData.setUser(this.user);
/*  140 */     workflowData.setRemoteAddr(this.remoteAddr);
/*  141 */     workflowData.setType(this.type);
/*  142 */     workflowData.setCrossSystem(this.crossSystem);
/*  143 */     workflowData.setIsComeFromOdocInit(this.isComeFromOdocInit);
/*  144 */     workflowData.setOdocWfType(this.odocWfType);
/*  145 */     xmlConvertWorkflowInfo(workflowData);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public Map importWorkflowByCloudXml(InputStream paramInputStream, User paramUser, String paramString1, String paramString2) {
/*  152 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*      */     try {
/*  154 */       this.xml = parseWorkflowXml(paramInputStream);
/*  155 */     } catch (Exception exception) {
/*  156 */       hashMap.put("exceptionMsg", "" + SystemEnv.getHtmlLabelName(32395, ThreadVarLanguage.getLang()) + "xml" + SystemEnv.getHtmlLabelName(10004285, ThreadVarLanguage.getLang()) + "");
/*  157 */       return hashMap;
/*      */     } 
/*      */     
/*  160 */     setUser(paramUser);
/*  161 */     setType(paramString1);
/*  162 */     setRemoteAddr(paramString2);
/*  163 */     this.isCloudImport = true;
/*  164 */     WorkflowData workflowData = new WorkflowData();
/*  165 */     workflowData.setUser(paramUser);
/*  166 */     workflowData.setType(paramString1);
/*  167 */     workflowData.setRemoteAddr(paramString2);
/*  168 */     workflowData.setCrossSystem(this.crossSystem);
/*  169 */     boolean bool = xmlConvertWorkflowInfo(workflowData);
/*  170 */     if (bool) {
/*  171 */       hashMap.putAll(this.MsgMap);
/*      */     } else {
/*  173 */       hashMap.put("exceptionMsg", this.exceptionMsg);
/*  174 */     }  return hashMap;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private boolean xmlConvertWorkflowInfo(WorkflowData paramWorkflowData) {
/*  181 */     Element element1 = this.xml.getElementById("formbase");
/*  182 */     Element element2 = this.xml.getElementById("billbase");
/*      */     
/*  184 */     RecordSet recordSet1 = new RecordSet();
/*  185 */     RecordSet recordSet2 = new RecordSet();
/*  186 */     RecordSet recordSet3 = new RecordSet();
/*  187 */     RecordSet recordSet4 = new RecordSet();
/*  188 */     RecordSet recordSet5 = new RecordSet();
/*      */     
/*      */     try {
/*  191 */       List<Element> list1 = this.xml.getElementById("htmllabelindex").getChildren("htmllabelindex");
/*  192 */       for (byte b1 = 0; b1 < list1.size(); b1++) {
/*  193 */         Element element = list1.get(b1);
/*      */         
/*  195 */         paramWorkflowData.saveHtmlLabelIndex(element);
/*  196 */         List<Element> list = element.getChildren("htmllabelinfo");
/*  197 */         for (byte b = 0; b < list.size(); b++) {
/*  198 */           Element element7 = list.get(b);
/*      */           
/*  200 */           paramWorkflowData.saveHtmlLabelInfo(element7);
/*      */         } 
/*      */       } 
/*  203 */       this.htmlLabelMap = paramWorkflowData.getHtmlLabelMap();
/*      */       
/*  205 */       if (element1 != null && element1.getChild("formbase") != null) {
/*  206 */         element1 = element1.getChild("formbase");
/*  207 */         paramWorkflowData.saveFormOrBillBase(this.xml.getSingleXmlBeanByElement(element1));
/*  208 */         this.formid = paramWorkflowData.getNewformid();
/*  209 */         this.isbill = paramWorkflowData.getIsbill();
/*  210 */         this.maintable = paramWorkflowData.getMainTable();
/*      */         
/*  212 */         Element element = element1.getChild("formmode");
/*  213 */         if (element != null) {
/*  214 */           paramWorkflowData.saveFormMode(this.xml.getSingleXmlBeanByElement(element));
/*      */         }
/*  216 */         List<Element> list18 = element1.getChildren("formdict");
/*      */         
/*  218 */         for (byte b10 = 0; b10 < list18.size(); b10++) {
/*  219 */           Element element7 = list18.get(b10);
/*  220 */           paramWorkflowData.saveFormDict(element7);
/*      */         } 
/*  222 */         List<Element> list19 = element1.getChildren("formdictdetail");
/*      */         
/*  224 */         for (byte b11 = 0; b11 < list19.size(); b11++) {
/*  225 */           Element element7 = list19.get(b11);
/*  226 */           paramWorkflowData.saveFormDictDetail(element7);
/*      */         } 
/*      */         
/*  229 */         List<Element> list20 = element1.getChildren("fieldgroup");
/*  230 */         for (byte b12 = 0; b12 < list20.size(); b12++) {
/*  231 */           Element element7 = list20.get(b12);
/*  232 */           paramWorkflowData.saveFormFieldGroup(element7);
/*      */         } 
/*      */         
/*  235 */         List<Element> list21 = element1.getChildren("formfield");
/*      */         
/*  237 */         for (byte b13 = 0; b13 < list21.size(); b13++) {
/*  238 */           Element element7 = list21.get(b13);
/*  239 */           paramWorkflowData.saveFormOrBillField(element7);
/*  240 */           List<Element> list24 = element7.getChildren("selectitem");
/*  241 */           for (byte b16 = 0; b16 < list24.size(); b16++) {
/*  242 */             Element element8 = list24.get(b16);
/*  243 */             paramWorkflowData.saveSelectItem(element8);
/*      */           } 
/*  245 */           List<Element> list25 = element7.getChildren("specialfield");
/*  246 */           for (byte b17 = 0; b17 < list25.size(); b17++) {
/*  247 */             Element element8 = list25.get(b17);
/*  248 */             paramWorkflowData.saveSpecialField(element8);
/*      */           } 
/*  250 */           List<Element> list26 = element7.getChildren("selectitemobj");
/*  251 */           for (byte b18 = 0; b18 < list26.size(); b18++) {
/*  252 */             Element element8 = list26.get(b18);
/*  253 */             paramWorkflowData.saveSelectItemObj(element8);
/*      */           } 
/*      */         } 
/*      */         
/*  257 */         paramWorkflowData.saveChildField("workflow_formdict", this.formid);
/*  258 */         paramWorkflowData.saveChildField("workflow_formdictdetail", this.formid);
/*      */         
/*  260 */         this.formFields = paramWorkflowData.getFormFields();
/*  261 */         this.formDetailFields = paramWorkflowData.getFormDetailFields();
/*  262 */         List<Element> list22 = element1.getChildren("formdetailinfo");
/*  263 */         for (byte b14 = 0; b14 < list22.size(); b14++) {
/*  264 */           Element element7 = list22.get(b14);
/*  265 */           paramWorkflowData.saveFormDetailInfo(element7);
/*      */         } 
/*      */         
/*  268 */         List<Element> list23 = element1.getChildren("fieldlable");
/*  269 */         for (byte b15 = 0; b15 < list23.size(); b15++) {
/*  270 */           Element element7 = list23.get(b15);
/*  271 */           paramWorkflowData.saveFieldLable(element7);
/*      */         } 
/*  273 */         this.FormComInfo.removeFormCache();
/*  274 */       } else if (element2 != null && element2.getChild("billbase") != null) {
/*  275 */         element2 = element2.getChild("billbase");
/*  276 */         paramWorkflowData.saveFormOrBillBase(this.xml.getSingleXmlBeanByElement(element2));
/*  277 */         this.formid = paramWorkflowData.getNewformid();
/*  278 */         this.isbill = paramWorkflowData.getIsbill();
/*  279 */         this.maintable = paramWorkflowData.getMainTable();
/*      */         
/*  281 */         Element element = element2.getChild("formmode");
/*  282 */         if (element != null) {
/*  283 */           paramWorkflowData.saveFormMode(this.xml.getSingleXmlBeanByElement(element));
/*      */         }
/*      */         
/*  286 */         if (Util.getIntValue(this.formid, 0) < 0 && this.isbill.equals("1")) {
/*  287 */           List<Element> list = element2.getChildren("billdetailtable");
/*      */           
/*  289 */           for (byte b = 0; b < list.size(); b++) {
/*  290 */             Element element7 = list.get(b);
/*      */             
/*  292 */             paramWorkflowData.saveBillDetailTable(element7);
/*      */           } 
/*  294 */           this.detailTableList = paramWorkflowData.getDetailTableList();
/*      */         } 
/*      */         
/*  297 */         List<Element> list18 = element2.getChildren("fieldgroup");
/*  298 */         for (byte b10 = 0; b10 < list18.size(); b10++) {
/*  299 */           Element element7 = list18.get(b10);
/*  300 */           paramWorkflowData.saveFormFieldGroup(element7);
/*      */         } 
/*      */         
/*  303 */         ArrayList arrayList1 = paramWorkflowData.getSapListold01();
/*  304 */         ArrayList arrayList2 = paramWorkflowData.getSapListnew01();
/*  305 */         List<Element> list19 = element2.getChildren("billfield");
/*  306 */         recordSet1.execute("delete from sap_broFieldtonew where type=2");
/*  307 */         for (byte b11 = 0; b11 < list19.size(); b11++) {
/*  308 */           Element element7 = list19.get(b11);
/*      */           
/*  310 */           paramWorkflowData.saveFormOrBillField(element7);
/*      */           
/*  312 */           List<Element> list20 = element7.getChildren("selectitem");
/*  313 */           for (byte b12 = 0; b12 < list20.size(); b12++) {
/*  314 */             Element element8 = list20.get(b12);
/*      */             
/*  316 */             paramWorkflowData.saveSelectItem(element8);
/*      */           } 
/*  318 */           List<Element> list21 = element7.getChildren("specialfield");
/*  319 */           for (byte b13 = 0; b13 < list21.size(); b13++) {
/*  320 */             Element element8 = list21.get(b13);
/*      */             
/*  322 */             paramWorkflowData.saveSpecialField(element8);
/*      */           } 
/*  324 */           List<Element> list22 = element7.getChildren("selectitemobj");
/*  325 */           for (byte b14 = 0; b14 < list22.size(); b14++) {
/*  326 */             Element element8 = list22.get(b14);
/*  327 */             paramWorkflowData.saveSelectItemObj(element8);
/*      */           } 
/*      */         } 
/*      */         
/*  331 */         ArrayList arrayList3 = paramWorkflowData.getSapListold();
/*  332 */         ArrayList arrayList4 = paramWorkflowData.getSapListnew();
/*      */         
/*  334 */         if (null != arrayList3) {
/*  335 */           for (byte b = 0; b < arrayList3.size(); b++) {
/*  336 */             String str1 = (new StringBuilder()).append(arrayList3.get(b)).append("").toString();
/*  337 */             String str2 = (new StringBuilder()).append(arrayList4.get(b)).append("").toString();
/*      */ 
/*      */             
/*  340 */             String str3 = "select  id from int_BrowserbaseInfo where mark='" + str2 + "'";
/*  341 */             String str4 = "select  id from int_BrowserbaseInfo where mark='" + str1 + "'";
/*  342 */             recordSet2.execute(str3);
/*  343 */             recordSet3.execute(str4);
/*  344 */             String str5 = "";
/*  345 */             if (recordSet3.next()) {
/*  346 */               str5 = recordSet3.getString("id");
/*      */             }
/*  348 */             if (recordSet2.next()) {
/*  349 */               String str = recordSet2.getString("id");
/*      */               
/*  351 */               str3 = "Insert into sap_inparameter(baseid,sapfield,oafield,constant,ordernum,ismainfield,fromfieldid,isbill,isshow,isrdonly,orderfield,oadesc,showname) select " + str + ",sapfield,oafield,constant,ordernum,ismainfield,fromfieldid,isbill,isshow,isrdonly,orderfield,oadesc,showname  from sap_inparameter where baseid='" + str5 + "'";
/*      */               
/*  353 */               boolean bool1 = recordSet4.execute(str3);
/*      */ 
/*      */               
/*  356 */               str3 = " select  id,baseid ,comtype,name,backtable,backoper from sap_complexname where comtype=3 and  baseid='" + str5 + "' order by id";
/*  357 */               bool1 = recordSet4.execute(str3);
/*  358 */               while (recordSet4.next()) {
/*  359 */                 String str6 = recordSet4.getString("id");
/*  360 */                 String str7 = recordSet4.getString("baseid");
/*  361 */                 String str8 = recordSet4.getString("comtype");
/*  362 */                 String str9 = recordSet4.getString("name");
/*  363 */                 String str10 = recordSet4.getString("backtable");
/*  364 */                 String str11 = recordSet4.getString("backoper");
/*  365 */                 if (recordSet5.execute("Insert into sap_complexname(baseid,comtype,name,backtable,backoper) values('" + str + "','" + str8 + "','" + str9 + "','" + str10 + "','" + str11 + "') ")) {
/*  366 */                   recordSet5.execute("select  max(id) id from sap_complexname where comtype=3 and  baseid='" + str + "'");
/*  367 */                   if (recordSet5.next()) {
/*  368 */                     String str12 = recordSet5.getString("id");
/*  369 */                     str3 = "Insert into sap_instructure(baseid,nameid,sapfield,oafield,constant,ordernum,ordergroupnum,ismainfield,fromfieldid,isbill,isshow,isrdonly,orderfield,oadesc,showname) select " + str + "," + str12 + ",sapfield,oafield,constant,ordernum,ordergroupnum,ismainfield,fromfieldid,isbill,isshow,isrdonly,orderfield,oadesc,showname  from sap_instructure where baseid='" + str5 + "' and nameid='" + str6 + "'";
/*      */                     
/*  371 */                     bool1 = recordSet4.execute(str3);
/*      */                   } 
/*      */                 } 
/*      */               } 
/*      */ 
/*      */ 
/*      */               
/*  378 */               str3 = " select  id,baseid ,comtype,name,backtable,backoper from sap_complexname where comtype=1 and  baseid='" + str5 + "' order by id";
/*  379 */               bool1 = recordSet4.execute(str3);
/*  380 */               while (recordSet4.next()) {
/*  381 */                 String str6 = recordSet4.getString("id");
/*  382 */                 String str7 = recordSet4.getString("baseid");
/*  383 */                 String str8 = recordSet4.getString("comtype");
/*  384 */                 String str9 = recordSet4.getString("name");
/*  385 */                 String str10 = recordSet4.getString("backtable");
/*  386 */                 String str11 = recordSet4.getString("backoper");
/*  387 */                 if (recordSet5.execute("Insert into sap_complexname(baseid,comtype,name,backtable,backoper) values('" + str + "','" + str8 + "','" + str9 + "','" + str10 + "','" + str11 + "') ")) {
/*  388 */                   recordSet5.execute("select  max(id) id from sap_complexname where comtype=1 and  baseid='" + str + "'");
/*  389 */                   if (recordSet5.next()) {
/*  390 */                     String str12 = recordSet5.getString("id");
/*  391 */                     str3 = "Insert into sap_intable(baseid,nameid,sapfield,oafield,constant,ordernum,ordergroupnum,ismainfield,fromfieldid,isbill,oadesc,showname) select " + str + "," + str12 + ",sapfield,oafield,constant,ordernum,ordergroupnum,ismainfield,fromfieldid,isbill,oadesc,showname  from sap_intable where baseid='" + str5 + "' and nameid='" + str6 + "'";
/*      */                     
/*  393 */                     bool1 = recordSet4.execute(str3);
/*      */                   } 
/*      */                 } 
/*      */               } 
/*      */ 
/*      */               
/*  399 */               str3 = "Insert into sap_outparameter(baseid,sapfield,showname,display,ordernum,oafield,ismainfield,fromfieldid,isbill,oadesc) select " + str + ",sapfield,showname,display,ordernum,oafield,ismainfield,fromfieldid,isbill,oadesc  from sap_outparameter where baseid='" + str5 + "'";
/*      */               
/*  401 */               bool1 = recordSet4.execute(str3);
/*      */ 
/*      */               
/*  404 */               str3 = " select  id,baseid ,comtype,name,backtable,backoper from sap_complexname where comtype=4 and  baseid='" + str5 + "' order by id";
/*  405 */               bool1 = recordSet4.execute(str3);
/*  406 */               while (recordSet4.next()) {
/*  407 */                 String str6 = recordSet4.getString("id");
/*  408 */                 String str7 = recordSet4.getString("baseid");
/*  409 */                 String str8 = recordSet4.getString("comtype");
/*  410 */                 String str9 = recordSet4.getString("name");
/*  411 */                 String str10 = recordSet4.getString("backtable");
/*  412 */                 String str11 = recordSet4.getString("backoper");
/*  413 */                 if (recordSet5.execute("Insert into sap_complexname(baseid,comtype,name,backtable,backoper) values('" + str + "','" + str8 + "','" + str9 + "','" + str10 + "','" + str11 + "') ")) {
/*  414 */                   recordSet5.execute("select  max(id) id from sap_complexname where comtype=4 and  baseid='" + str + "'");
/*  415 */                   if (recordSet5.next()) {
/*  416 */                     String str12 = recordSet5.getString("id");
/*  417 */                     str3 = "Insert into sap_outstructure (baseid,nameid,sapfield,showname,display,search,ordernum,ordergroupnum,oafield,ismainfield,fromfieldid,isbill,oadesc) select " + str + "," + str12 + ",sapfield,showname,display,search,ordernum,ordergroupnum,oafield,ismainfield,fromfieldid,isbill,oadesc  from sap_outstructure where baseid='" + str5 + "' and nameid='" + str6 + "'";
/*      */                     
/*  419 */                     bool1 = recordSet4.execute(str3);
/*      */                   } 
/*      */                 } 
/*      */               } 
/*      */ 
/*      */               
/*  425 */               str3 = " select  id,baseid ,comtype,name,backtable,backoper from sap_complexname where comtype=2 and  baseid='" + str5 + "' order by id";
/*  426 */               bool1 = recordSet4.execute(str3);
/*  427 */               while (recordSet4.next()) {
/*  428 */                 String str6 = recordSet4.getString("id");
/*  429 */                 String str7 = recordSet4.getString("baseid");
/*  430 */                 String str8 = recordSet4.getString("comtype");
/*  431 */                 String str9 = recordSet4.getString("name");
/*  432 */                 String str10 = recordSet4.getString("backtable");
/*  433 */                 String str11 = recordSet4.getString("backoper");
/*  434 */                 if (recordSet5.execute("Insert into sap_complexname(baseid,comtype,name,backtable,backoper) values('" + str + "','" + str8 + "','" + str9 + "','" + str10 + "','" + str11 + "') ")) {
/*  435 */                   recordSet5.execute("select  max(id) id from sap_complexname where comtype=2 and  baseid='" + str + "'");
/*  436 */                   if (recordSet5.next()) {
/*  437 */                     String str12 = recordSet5.getString("id");
/*  438 */                     str3 = "Insert into sap_outtable (baseid,nameid,sapfield,showname,display,search,primarykey,ordernum,ordergroupnum,oafield,ismainfield,fromfieldid,isbill,oadesc,orderfield) select " + str + "," + str12 + ",sapfield,showname,display,search,primarykey,ordernum,ordergroupnum,oafield,ismainfield,fromfieldid,isbill,oadesc,orderfield  from sap_outtable where baseid='" + str5 + "' and nameid='" + str6 + "'";
/*      */                     
/*  440 */                     bool1 = recordSet4.execute(str3);
/*      */                     
/*  442 */                     str3 = " Insert into sap_outparaprocess(baseid,nameid,sapfield,oafield,constant,ordernum,ismainfield,fromfieldid,isbill,oadesc,showname )  select " + str + "," + str12 + ",sapfield,oafield,constant,ordernum,ismainfield,fromfieldid,isbill,oadesc,showname  from sap_outparaprocess where  baseid='" + str5 + "' and nameid='" + str6 + "'";
/*  443 */                     bool1 = recordSet4.execute(str3);
/*      */                   } 
/*      */                 } 
/*      */               } 
/*      */ 
/*      */               
/*  449 */               str3 = "select  id,type,resourceids,roleids,wfids,ordernum  from int_authorizeRight where baseid='" + str5 + "'";
/*  450 */               bool1 = recordSet4.execute(str3);
/*  451 */               while (recordSet4.next()) {
/*  452 */                 String str6 = recordSet4.getString("id");
/*  453 */                 String str7 = recordSet4.getString("type");
/*  454 */                 String str8 = recordSet4.getString("resourceids");
/*  455 */                 String str9 = recordSet4.getString("roleids");
/*  456 */                 String str10 = recordSet4.getString("wfids");
/*  457 */                 String str11 = recordSet4.getString("ordernum");
/*  458 */                 if (recordSet5.execute("Insert into int_authorizeRight(baseid,type,resourceids,roleids,wfids,ordernum) values('" + str + "','" + str7 + "','" + str8 + "','" + str9 + "','" + str10 + "','" + str11 + "')")) {
/*  459 */                   recordSet5.execute("select  max(id) id from int_authorizeRight");
/*  460 */                   if (recordSet5.next()) {
/*  461 */                     String str12 = recordSet5.getString("id");
/*      */                     
/*  463 */                     str3 = "insert into  int_authorizeDetaRight ( rightid,isinclude,value  ) select " + str12 + ",isinclude,value from int_authorizeDetaRight  where rightid='" + str6 + "'";
/*  464 */                     bool1 = recordSet5.execute(str3);
/*      */                   } 
/*      */                 } 
/*      */               } 
/*      */ 
/*      */               
/*  470 */               recordSet4.execute("select * from sap_broFieldtonew where type=2");
/*  471 */               while (recordSet4.next()) {
/*  472 */                 String str6 = recordSet4.getString("oldfield");
/*  473 */                 String str7 = recordSet4.getString("newfield");
/*      */                 
/*  475 */                 str3 = "update sap_inparameter set fromfieldid='" + str7 + "',isbill='1' where fromfieldid='" + str6 + "' and baseid='" + str + "' ";
/*  476 */                 bool1 = recordSet5.execute(str3);
/*      */ 
/*      */                 
/*  479 */                 str3 = "update sap_instructure set fromfieldid='" + str7 + "',isbill='1' where fromfieldid='" + str6 + "' and baseid='" + str + "' ";
/*  480 */                 bool1 = recordSet5.execute(str3);
/*      */ 
/*      */                 
/*  483 */                 str3 = "update sap_intable set fromfieldid='" + str7 + "',isbill='1' where fromfieldid='" + str6 + "' and baseid='" + str + "' ";
/*  484 */                 bool1 = recordSet5.execute(str3);
/*      */ 
/*      */                 
/*  487 */                 str3 = "update sap_outparameter set fromfieldid='" + str7 + "',isbill='1' where fromfieldid='" + str6 + "' and baseid='" + str + "' ";
/*  488 */                 bool1 = recordSet5.execute(str3);
/*      */ 
/*      */                 
/*  491 */                 str3 = "update sap_outstructure set fromfieldid='" + str7 + "',isbill='1' where fromfieldid='" + str6 + "' and baseid='" + str + "' ";
/*  492 */                 bool1 = recordSet5.execute(str3);
/*      */ 
/*      */                 
/*  495 */                 str3 = "update sap_outtable set fromfieldid='" + str7 + "',isbill='1' where fromfieldid='" + str6 + "' and baseid='" + str + "' ";
/*  496 */                 bool1 = recordSet5.execute(str3);
/*      */ 
/*      */                 
/*  499 */                 str3 = " update  sap_outparaprocess   set fromfieldid='" + str7 + "',isbill='1'   where fromfieldid='" + str6 + "' and baseid='" + str + "'";
/*  500 */                 bool1 = recordSet5.execute(str3);
/*      */               } 
/*      */             } 
/*      */           } 
/*      */         }
/*      */ 
/*      */         
/*  507 */         if (null != arrayList1) {
/*  508 */           for (byte b = 0; b < arrayList1.size(); b++) {
/*  509 */             String str1 = (new StringBuilder()).append(arrayList1.get(b)).append("").toString();
/*  510 */             String str2 = (new StringBuilder()).append(arrayList2.get(b)).append("").toString();
/*      */             
/*  512 */             String str3 = "select  id from int_BrowserbaseInfo where mark='" + str2 + "'";
/*  513 */             String str4 = "select  id from int_BrowserbaseInfo where mark='" + str1 + "'";
/*  514 */             recordSet2.execute(str3);
/*  515 */             recordSet3.execute(str4);
/*  516 */             String str5 = "";
/*  517 */             if (recordSet3.next()) {
/*  518 */               str5 = recordSet3.getString("id");
/*      */             }
/*  520 */             if (recordSet2.next()) {
/*  521 */               String str = recordSet2.getString("id");
/*      */               
/*  523 */               str3 = "Insert into sap_inparameter(baseid,sapfield,oafield,constant,ordernum,ismainfield,fromfieldid,isbill,isshow,isrdonly,orderfield,oadesc,showname) select " + str + ",sapfield,oafield,constant,ordernum,ismainfield,fromfieldid,isbill,isshow,isrdonly,orderfield,oadesc,showname  from sap_inparameter where baseid='" + str5 + "'";
/*      */               
/*  525 */               boolean bool1 = recordSet4.execute(str3);
/*      */ 
/*      */               
/*  528 */               str3 = " select  id,baseid ,comtype,name,backtable,backoper from sap_complexname where comtype=3 and  baseid='" + str5 + "' order by id";
/*  529 */               bool1 = recordSet4.execute(str3);
/*  530 */               while (recordSet4.next()) {
/*  531 */                 String str6 = recordSet4.getString("id");
/*  532 */                 String str7 = recordSet4.getString("baseid");
/*  533 */                 String str8 = recordSet4.getString("comtype");
/*  534 */                 String str9 = recordSet4.getString("name");
/*  535 */                 String str10 = recordSet4.getString("backtable");
/*  536 */                 String str11 = recordSet4.getString("backoper");
/*  537 */                 if (recordSet5.execute("Insert into sap_complexname(baseid,comtype,name,backtable,backoper) values('" + str + "','" + str8 + "','" + str9 + "','" + str10 + "','" + str11 + "') ")) {
/*  538 */                   recordSet5.execute("select  max(id) id from sap_complexname where comtype=3 and  baseid='" + str + "'");
/*  539 */                   if (recordSet5.next()) {
/*  540 */                     String str12 = recordSet5.getString("id");
/*  541 */                     str3 = "Insert into sap_instructure(baseid,nameid,sapfield,oafield,constant,ordernum,ordergroupnum,ismainfield,fromfieldid,isbill,isshow,isrdonly,orderfield,oadesc,showname) select " + str + "," + str12 + ",sapfield,oafield,constant,ordernum,ordergroupnum,ismainfield,fromfieldid,isbill,isshow,isrdonly,orderfield,oadesc,showname  from sap_instructure where baseid='" + str5 + "' and nameid='" + str6 + "'";
/*      */                     
/*  543 */                     bool1 = recordSet4.execute(str3);
/*      */                   } 
/*      */                 } 
/*      */               } 
/*      */ 
/*      */ 
/*      */               
/*  550 */               str3 = " select  id,baseid ,comtype,name,backtable,backoper from sap_complexname where comtype=1 and  baseid='" + str5 + "' order by id";
/*  551 */               bool1 = recordSet4.execute(str3);
/*  552 */               while (recordSet4.next()) {
/*  553 */                 String str6 = recordSet4.getString("id");
/*  554 */                 String str7 = recordSet4.getString("baseid");
/*  555 */                 String str8 = recordSet4.getString("comtype");
/*  556 */                 String str9 = recordSet4.getString("name");
/*  557 */                 String str10 = recordSet4.getString("backtable");
/*  558 */                 String str11 = recordSet4.getString("backoper");
/*  559 */                 if (recordSet5.execute("Insert into sap_complexname(baseid,comtype,name,backtable,backoper) values('" + str + "','" + str8 + "','" + str9 + "','" + str10 + "','" + str11 + "') ")) {
/*  560 */                   recordSet5.execute("select  max(id) id from sap_complexname where comtype=1 and  baseid='" + str + "'");
/*  561 */                   if (recordSet5.next()) {
/*  562 */                     String str12 = recordSet5.getString("id");
/*  563 */                     str3 = "Insert into sap_intable(baseid,nameid,sapfield,oafield,constant,ordernum,ordergroupnum,ismainfield,fromfieldid,isbill,oadesc,showname) select " + str + "," + str12 + ",sapfield,oafield,constant,ordernum,ordergroupnum,ismainfield,fromfieldid,isbill,oadesc,showname  from sap_intable where baseid='" + str5 + "' and nameid='" + str6 + "'";
/*      */                     
/*  565 */                     bool1 = recordSet4.execute(str3);
/*      */                   } 
/*      */                 } 
/*      */               } 
/*      */ 
/*      */               
/*  571 */               str3 = "Insert into sap_outparameter(baseid,sapfield,showname,display,ordernum,oafield,ismainfield,fromfieldid,isbill,oadesc) select " + str + ",sapfield,showname,display,ordernum,oafield,ismainfield,fromfieldid,isbill,oadesc  from sap_outparameter where baseid='" + str5 + "'";
/*      */               
/*  573 */               bool1 = recordSet4.execute(str3);
/*      */ 
/*      */               
/*  576 */               str3 = " select  id,baseid ,comtype,name,backtable,backoper from sap_complexname where comtype=4 and  baseid='" + str5 + "' order by id";
/*  577 */               bool1 = recordSet4.execute(str3);
/*  578 */               while (recordSet4.next()) {
/*  579 */                 String str6 = recordSet4.getString("id");
/*  580 */                 String str7 = recordSet4.getString("baseid");
/*  581 */                 String str8 = recordSet4.getString("comtype");
/*  582 */                 String str9 = recordSet4.getString("name");
/*  583 */                 String str10 = recordSet4.getString("backtable");
/*  584 */                 String str11 = recordSet4.getString("backoper");
/*  585 */                 if (recordSet5.execute("Insert into sap_complexname(baseid,comtype,name,backtable,backoper) values('" + str + "','" + str8 + "','" + str9 + "','" + str10 + "','" + str11 + "') ")) {
/*  586 */                   recordSet5.execute("select  max(id) id from sap_complexname where comtype=4 and  baseid='" + str + "'");
/*  587 */                   if (recordSet5.next()) {
/*  588 */                     String str12 = recordSet5.getString("id");
/*  589 */                     str3 = "Insert into sap_outstructure (baseid,nameid,sapfield,showname,display,search,ordernum,ordergroupnum,oafield,ismainfield,fromfieldid,isbill,oadesc) select " + str + "," + str12 + ",sapfield,showname,display,search,ordernum,ordergroupnum,oafield,ismainfield,fromfieldid,isbill,oadesc  from sap_outstructure where baseid='" + str5 + "' and nameid='" + str6 + "'";
/*      */                     
/*  591 */                     bool1 = recordSet4.execute(str3);
/*      */                   } 
/*      */                 } 
/*      */               } 
/*      */ 
/*      */               
/*  597 */               str3 = " select  id,baseid ,comtype,name,backtable,backoper from sap_complexname where comtype=2 and  baseid='" + str5 + "' order by id";
/*      */               
/*  599 */               bool1 = recordSet4.execute(str3);
/*  600 */               while (recordSet4.next()) {
/*  601 */                 String str6 = recordSet4.getString("id");
/*  602 */                 String str7 = recordSet4.getString("baseid");
/*  603 */                 String str8 = recordSet4.getString("comtype");
/*  604 */                 String str9 = recordSet4.getString("name");
/*  605 */                 String str10 = recordSet4.getString("backtable");
/*  606 */                 String str11 = recordSet4.getString("backoper");
/*  607 */                 if (recordSet5.execute("Insert into sap_complexname(baseid,comtype,name,backtable,backoper) values('" + str + "','" + str8 + "','" + str9 + "','" + str10 + "','" + str11 + "') ")) {
/*  608 */                   recordSet5.execute("select  max(id) id from sap_complexname where comtype=2 and  baseid='" + str + "'");
/*  609 */                   if (recordSet5.next()) {
/*  610 */                     String str12 = recordSet5.getString("id");
/*  611 */                     str3 = "Insert into sap_outtable (baseid,nameid,sapfield,showname,display,search,primarykey,ordernum,ordergroupnum,oafield,ismainfield,fromfieldid,isbill,oadesc,orderfield) select " + str + "," + str12 + ",sapfield,showname,display,search,primarykey,ordernum,ordergroupnum,oafield,ismainfield,fromfieldid,isbill,oadesc,orderfield  from sap_outtable where baseid='" + str5 + "' and nameid='" + str6 + "'";
/*      */                     
/*  613 */                     bool1 = recordSet4.execute(str3);
/*      */                     
/*  615 */                     str3 = " Insert into sap_outparaprocess(baseid,nameid,sapfield,oafield,constant,ordernum,ismainfield,fromfieldid,isbill,oadesc,showname )  select " + str + "," + str12 + ",sapfield,oafield,constant,ordernum,ismainfield,fromfieldid,isbill,oadesc,showname  from sap_outparaprocess where  baseid='" + str5 + "' and nameid='" + str6 + "'";
/*  616 */                     bool1 = recordSet4.execute(str3);
/*      */                   } 
/*      */                 } 
/*      */               } 
/*      */ 
/*      */               
/*  622 */               str3 = "select  id,type,resourceids,roleids,wfids,ordernum  from int_authorizeRight where baseid='" + str5 + "'";
/*  623 */               bool1 = recordSet4.execute(str3);
/*  624 */               while (recordSet4.next()) {
/*  625 */                 String str6 = recordSet4.getString("id");
/*  626 */                 String str7 = recordSet4.getString("type");
/*  627 */                 String str8 = recordSet4.getString("resourceids");
/*  628 */                 String str9 = recordSet4.getString("roleids");
/*  629 */                 String str10 = recordSet4.getString("wfids");
/*  630 */                 String str11 = recordSet4.getString("ordernum");
/*  631 */                 if (recordSet5.execute("Insert into int_authorizeRight(baseid,type,resourceids,roleids,wfids,ordernum) values('" + str + "','" + str7 + "','" + str8 + "','" + str9 + "','" + str10 + "','" + str11 + "')")) {
/*  632 */                   recordSet5.execute("select  max(id) id from int_authorizeRight");
/*  633 */                   if (recordSet5.next()) {
/*  634 */                     String str12 = recordSet5.getString("id");
/*      */                     
/*  636 */                     str3 = "insert into  int_authorizeDetaRight ( rightid,isinclude,value  ) select " + str12 + ",isinclude,value from int_authorizeDetaRight  where rightid='" + str6 + "'";
/*  637 */                     bool1 = recordSet5.execute(str3);
/*      */                   } 
/*      */                 } 
/*      */               } 
/*      */ 
/*      */               
/*  643 */               recordSet4.execute("select * from sap_broFieldtonew where type=2");
/*  644 */               while (recordSet4.next()) {
/*  645 */                 String str6 = recordSet4.getString("oldfield");
/*  646 */                 String str7 = recordSet4.getString("newfield");
/*      */                 
/*  648 */                 str3 = "update sap_inparameter set fromfieldid='" + str7 + "',isbill='1' where fromfieldid='" + str6 + "' and baseid='" + str + "' ";
/*  649 */                 bool1 = recordSet5.execute(str3);
/*      */ 
/*      */                 
/*  652 */                 str3 = "update sap_instructure set fromfieldid='" + str7 + "',isbill='1' where fromfieldid='" + str6 + "' and baseid='" + str + "' ";
/*  653 */                 bool1 = recordSet5.execute(str3);
/*      */ 
/*      */                 
/*  656 */                 str3 = "update sap_intable set fromfieldid='" + str7 + "',isbill='1' where fromfieldid='" + str6 + "' and baseid='" + str + "' ";
/*  657 */                 bool1 = recordSet5.execute(str3);
/*      */ 
/*      */                 
/*  660 */                 str3 = "update sap_outparameter set fromfieldid='" + str7 + "',isbill='1' where fromfieldid='" + str6 + "' and baseid='" + str + "' ";
/*  661 */                 bool1 = recordSet5.execute(str3);
/*      */ 
/*      */                 
/*  664 */                 str3 = "update sap_outstructure set fromfieldid='" + str7 + "',isbill='1' where fromfieldid='" + str6 + "' and baseid='" + str + "' ";
/*  665 */                 bool1 = recordSet5.execute(str3);
/*      */ 
/*      */                 
/*  668 */                 str3 = "update sap_outtable set fromfieldid='" + str7 + "',isbill='1' where fromfieldid='" + str6 + "' and baseid='" + str + "' ";
/*  669 */                 bool1 = recordSet5.execute(str3);
/*      */ 
/*      */                 
/*  672 */                 str3 = " update  sap_outparaprocess   set fromfieldid='" + str7 + "',isbill='1'   where fromfieldid='" + str6 + "' and baseid='" + str + "'";
/*  673 */                 bool1 = recordSet5.execute(str3);
/*      */               } 
/*      */             } 
/*      */           } 
/*      */         }
/*      */ 
/*      */         
/*  680 */         recordSet1.execute("delete from sap_broFieldtonew where type=2");
/*      */         
/*  682 */         paramWorkflowData.saveChildField("workflow_billfield", this.formid);
/*  683 */         paramWorkflowData.savePubchilchoiceid("workflow_billfield", this.formid);
/*      */         
/*  685 */         this.formFields = paramWorkflowData.getFormFields();
/*  686 */         this.formDetailFields = paramWorkflowData.getFormDetailFields();
/*      */         
/*  688 */         if (Util.getIntValue(this.formid, 0) < 0 && this.isbill.equals("1")) {
/*  689 */           List<Element> list = element2.getChildren("formdetailinfo");
/*      */           
/*  691 */           for (byte b = 0; b < list.size(); b++) {
/*  692 */             Element element7 = list.get(b);
/*      */             
/*  694 */             paramWorkflowData.saveFormDetailInfo(element7);
/*      */           } 
/*      */         } 
/*  697 */         this.BillComInfo.removeBillCache();
/*      */       } 
/*      */       
/*  700 */       List<Element> list2 = this.xml.getElementById("systemfieldgroup").getChildren("systemfieldgroup");
/*  701 */       for (byte b2 = 0; b2 < list2.size(); b2++) {
/*  702 */         Element element = list2.get(b2);
/*  703 */         paramWorkflowData.saveSystemFieldGroup(element);
/*      */       } 
/*  705 */       this.fieldMap = paramWorkflowData.getFieldMap();
/*      */       
/*  707 */       Element element3 = this.xml.getElementById("workflowtype").getChild("workflowtype");
/*  708 */       paramWorkflowData.saveWorkflowType(element3);
/*      */ 
/*      */       
/*  711 */       Element element4 = this.xml.getElementById("baseinfo").getChild("baseinfo");
/*  712 */       paramWorkflowData.saveWorkflowBase(element4);
/*      */       
/*  714 */       if ("0".equals(this.type)) {
/*      */         
/*  716 */         List list = this.xml.getElementById("settitle").getChildren("settitle");
/*  717 */         if (list != null) {
/*  718 */           for (Element element : list) {
/*  719 */             paramWorkflowData.saveSetTitle(element);
/*      */           }
/*      */         }
/*      */       } 
/*      */       
/*  724 */       this.WorkTypeComInfo.removeWorkTypeCache();
/*  725 */       this.WorkflowComInfo.removeWorkflowCache();
/*  726 */       this.WorkflowBillComInfo.removeWorkflowBillCache();
/*  727 */       this.FieldComInfo.removeFieldCache();
/*  728 */       this.DetailFieldComInfo.removeFieldCache();
/*      */       
/*  730 */       this.oldworkflowid = paramWorkflowData.getOldwfid();
/*  731 */       this.workflowid = paramWorkflowData.getNewwfid();
/*      */ 
/*      */       
/*  734 */       List list3 = this.xml.getElementById("wffileupload").getChildren("wffileupload");
/*  735 */       for (Element element7 : list3) {
/*  736 */         Element element8 = element7;
/*  737 */         paramWorkflowData.saveFileUploadSettings(element8);
/*      */       } 
/*      */ 
/*      */       
/*  741 */       Element element5 = this.xml.getElementById("communication").getChild("communication");
/*  742 */       paramWorkflowData.saveCommunicationSet(element5);
/*      */ 
/*      */       
/*  745 */       List list4 = this.xml.getElementById("workflow_textInfo").getChildren("workflow_textInfo");
/*  746 */       for (Element element7 : list4) {
/*  747 */         Element element8 = element7;
/*  748 */         paramWorkflowData.saveWorkflowTextInfo(element8);
/*      */       } 
/*      */ 
/*      */ 
/*      */       
/*  753 */       List<Element> list5 = this.xml.getElementById("flownodebase").getChildren("flownodebase");
/*  754 */       for (byte b3 = 0; b3 < list5.size(); b3++) {
/*  755 */         Element element = list5.get(b3);
/*      */         
/*  757 */         paramWorkflowData.saveWorkflowNodeBase(element);
/*      */       } 
/*  759 */       this.nodeMap = paramWorkflowData.getNodeMap();
/*      */ 
/*      */ 
/*      */       
/*  763 */       boolean bool = this.xml.isExistsElement("remarkdisplay");
/*  764 */       if (bool) {
/*  765 */         Element element = this.xml.getElementById("remarkdisplay");
/*  766 */         paramWorkflowData.saveRemarkDisplaySettings(element);
/*      */       } else {
/*  768 */         this.crossSystem.saveRemarkDisplaySettings(this.workflowid);
/*      */       } 
/*      */ 
/*      */       
/*  772 */       List list6 = this.xml.getElementById("workflow_detailfilterset").getChildren("workflow_detailfilterset");
/*  773 */       for (Element element7 : list6) {
/*  774 */         Element element8 = element7;
/*  775 */         paramWorkflowData.saveDetailFilterSet(element8);
/*      */       } 
/*      */ 
/*      */ 
/*      */       
/*  780 */       for (byte b4 = 0; b4 < list5.size(); b4++) {
/*  781 */         Element element7 = list5.get(b4);
/*      */ 
/*      */ 
/*      */         
/*  785 */         Element element8 = element7.getChild("newrulemapinfo");
/*  786 */         if (element8 != null) {
/*  787 */           paramWorkflowData.saveNewrulemapinfo(element8);
/*      */         }
/*      */         
/*  790 */         List<Element> list18 = element7.getChildren("newrulemaplist");
/*  791 */         for (byte b10 = 0; b10 < list18.size(); b10++) {
/*  792 */           Element element = list18.get(b10);
/*  793 */           paramWorkflowData.saveNewrulemaplistinfo(element);
/*      */         } 
/*      */         
/*  796 */         List<Element> list19 = element7.getChildren("newrulebase");
/*  797 */         for (byte b11 = 0; b11 < list19.size(); b11++) {
/*  798 */           Element element = list19.get(b11);
/*  799 */           paramWorkflowData.saveNewrulebaseinfo(element);
/*      */           
/*  801 */           List<Element> list39 = element.getChildren("newruleexpressions");
/*  802 */           for (byte b31 = 0; b31 < list39.size(); b31++) {
/*  803 */             Element element10 = list39.get(b31);
/*  804 */             paramWorkflowData.saveNewruleExpressioninfo(element10);
/*      */           } 
/*      */           
/*  807 */           List<Element> list40 = element.getChildren("newrulevaluebase");
/*  808 */           for (byte b32 = 0; b32 < list40.size(); b32++) {
/*  809 */             Element element10 = list40.get(b32);
/*  810 */             paramWorkflowData.saveNewruleValueBaseinfo(element10);
/*      */           } 
/*      */           
/*  813 */           List<Element> list41 = element.getChildren("newruleexpressionbase");
/*  814 */           for (byte b33 = 0; b33 < list41.size(); b33++) {
/*  815 */             Element element10 = list41.get(b33);
/*  816 */             paramWorkflowData.saveNewruleExpressionBaseinfo(element10);
/*      */           } 
/*      */           
/*  819 */           List<Element> list42 = element.getChildren("newrulevaluemap");
/*  820 */           for (byte b34 = 0; b34 < list42.size(); b34++) {
/*  821 */             Element element10 = list42.get(b34);
/*  822 */             paramWorkflowData.saveNewruleValuemapinfo(element10);
/*      */           } 
/*      */         } 
/*      */ 
/*      */         
/*  827 */         Element element9 = element7.getChild("flownode");
/*  828 */         if (element9 != null) {
/*  829 */           paramWorkflowData.saveWorkflowNode(element9);
/*      */         }
/*      */         
/*  832 */         List<Element> list20 = element7.getChildren("robotnode");
/*  833 */         for (byte b12 = 0; b12 < list20.size(); b12++) {
/*  834 */           paramWorkflowData.saveRobotNodeSet(list20.get(b12));
/*      */         }
/*      */ 
/*      */         
/*  838 */         List<Element> list21 = element7.getChildren("overtimenew");
/*  839 */         for (byte b13 = 0; b13 < list21.size(); b13++) {
/*  840 */           Element element = list21.get(b13);
/*  841 */           paramWorkflowData.saveOverTime(element);
/*  842 */           if ("0".equals(this.type)) {
/*      */             
/*  844 */             List list39 = element.getChildren("overtimerulebase");
/*  845 */             for (Element element10 : list39) {
/*  846 */               paramWorkflowData.saveRuleBase(element10, 10);
/*      */               
/*  848 */               List list42 = element10.getChildren("ruleexpressionbase");
/*  849 */               for (Element element11 : list42) {
/*  850 */                 paramWorkflowData.saveRuleExpressionbase(element11);
/*      */               }
/*      */               
/*  853 */               List list43 = element10.getChildren("ruleexpressions");
/*  854 */               for (Element element11 : list43) {
/*  855 */                 paramWorkflowData.saveRuleExpressions(element11);
/*      */               }
/*      */             } 
/*      */             
/*  859 */             List list40 = element.getChildren("overtimerulemaplist");
/*  860 */             for (Element element10 : list40) {
/*  861 */               paramWorkflowData.saveRuleMapList(element10, 10);
/*      */             }
/*      */             
/*  864 */             List list41 = element.getChildren("overtimerulemapitem");
/*  865 */             for (Element element10 : list41) {
/*  866 */               paramWorkflowData.saveRuleMapItem(element10, 10);
/*      */             }
/*      */           } 
/*      */           
/*  870 */           paramWorkflowData.updateOvertimeRule(element);
/*      */         } 
/*      */ 
/*      */         
/*  874 */         List<Element> list22 = element7.getChildren("overtimenewdetail");
/*  875 */         for (byte b14 = 0; b14 < list22.size(); b14++) {
/*  876 */           Element element = list22.get(b14);
/*  877 */           paramWorkflowData.saveOverTimeDetail(element);
/*      */           
/*  879 */           List<Element> list = element.getChildren("remindtofield");
/*  880 */           for (byte b = 0; b < list.size(); b++) {
/*  881 */             Element element10 = list.get(b);
/*  882 */             paramWorkflowData.saveOverTimeDetailToField(element10);
/*      */           } 
/*      */         } 
/*      */         
/*  886 */         List<Element> list23 = element7.getChildren("dissummary");
/*      */         
/*  888 */         for (byte b15 = 0; b15 < list23.size(); b15++) {
/*  889 */           Element element = list23.get(b15);
/*  890 */           paramWorkflowData.saveDisSummary(element);
/*      */         } 
/*  892 */         List<Element> list24 = element7.getChildren("nodeprop");
/*      */         
/*  894 */         for (byte b16 = 0; b16 < list24.size(); b16++) {
/*  895 */           Element element = list24.get(b16);
/*      */           
/*  897 */           paramWorkflowData.saveNodeProp(element);
/*      */         } 
/*  899 */         List<Element> list25 = element7.getChildren("nodemenu");
/*      */         
/*  901 */         for (byte b17 = 0; b17 < list25.size(); b17++) {
/*  902 */           Element element = list25.get(b17);
/*      */           
/*  904 */           paramWorkflowData.saveNodeMenu(element);
/*      */         } 
/*      */         
/*  907 */         List<Element> list26 = element7.getChildren("nodenewmenu");
/*  908 */         for (byte b18 = 0; b18 < list26.size(); b18++) {
/*  909 */           Element element = list26.get(b18);
/*      */           
/*  911 */           paramWorkflowData.saveNodeNewMenu(element);
/*      */         } 
/*      */         
/*  914 */         List<Element> list27 = element7.getChildren("nodeoperate");
/*      */         
/*  916 */         for (byte b19 = 0; b19 < list27.size(); b19++) {
/*  917 */           Element element = list27.get(b19);
/*      */           
/*  919 */           paramWorkflowData.saveNodeOperate(element);
/*      */         } 
/*      */         
/*  922 */         List<Element> list28 = element7.getChildren("nodemode");
/*      */         
/*  924 */         for (byte b20 = 0; b20 < list28.size(); b20++) {
/*  925 */           Element element = list28.get(b20);
/*      */           
/*  927 */           paramWorkflowData.saveNodeMode(element);
/*      */         } 
/*  929 */         List<Element> list29 = element7.getChildren("nodemodeview");
/*      */         
/*  931 */         for (byte b21 = 0; b21 < list29.size(); b21++) {
/*  932 */           Element element = list29.get(b21);
/*      */           
/*  934 */           paramWorkflowData.saveNodeModeView(element);
/*      */         } 
/*  936 */         List<Element> list30 = element7.getChildren("nodefieldattr");
/*      */         
/*  938 */         for (byte b22 = 0; b22 < list30.size(); b22++) {
/*  939 */           Element element = list30.get(b22);
/*      */           
/*  941 */           paramWorkflowData.saveNodeHtmlAttr(element);
/*      */         } 
/*  943 */         List<Element> list31 = element7.getChildren("nodehtmlview");
/*      */         
/*  945 */         for (byte b23 = 0; b23 < list31.size(); b23++) {
/*  946 */           Element element = list31.get(b23);
/*      */           
/*  948 */           paramWorkflowData.saveNodeHtmlView(element);
/*      */         } 
/*  950 */         List<Element> list32 = element7.getChildren("formulahtmllayout");
/*  951 */         for (byte b24 = 0; b24 < list32.size(); b24++) {
/*  952 */           Element element = list32.get(b24);
/*      */           
/*  954 */           paramWorkflowData.saveFormulaHtmlLayout(element);
/*      */         } 
/*  956 */         List<Element> list33 = element7.getChildren("nodeform");
/*      */         
/*  958 */         for (byte b25 = 0; b25 < list33.size(); b25++) {
/*  959 */           Element element = list33.get(b25);
/*      */           
/*  961 */           paramWorkflowData.saveNodeForm(element);
/*      */         } 
/*      */         
/*  964 */         if (this.type.equals("0")) {
/*      */           
/*  966 */           List<Element> list39 = element7.getChildren("fwlimitset");
/*  967 */           for (byte b31 = 0; b31 < list39.size(); b31++) {
/*      */             
/*  969 */             Element element = list39.get(b31);
/*  970 */             paramWorkflowData.saveFwLimitSet(element);
/*      */           } 
/*      */           
/*  973 */           List<Element> list40 = element7.getChildren("fwadvanced");
/*  974 */           for (byte b32 = 0; b32 < list40.size(); b32++) {
/*      */             
/*  976 */             Element element = list40.get(b32);
/*  977 */             paramWorkflowData.saveFwAdvanced(element);
/*      */           } 
/*      */         } 
/*      */ 
/*      */         
/*  982 */         List<Element> list34 = element7.getChildren("margin");
/*      */         
/*  984 */         for (byte b26 = 0; b26 < list34.size(); b26++) {
/*  985 */           Element element = list34.get(b26);
/*      */           
/*  987 */           paramWorkflowData.saveMargin(element);
/*      */         } 
/*      */ 
/*      */         
/*  991 */         List<Element> list35 = element7.getChildren("printset");
/*      */         
/*  993 */         for (byte b27 = 0; b27 < list35.size(); b27++) {
/*  994 */           Element element = list35.get(b27);
/*      */           
/*  996 */           paramWorkflowData.savePrintSet(element);
/*      */         } 
/*      */ 
/*      */         
/* 1000 */         List<Element> list36 = element7.getChildren("wf_flownodehtml");
/*      */         
/* 1002 */         for (byte b28 = 0; b28 < list36.size(); b28++) {
/* 1003 */           Element element = list36.get(b28);
/*      */           
/* 1005 */           paramWorkflowData.saveFlownodehtml(element);
/*      */         } 
/*      */ 
/*      */         
/* 1009 */         List<Element> list37 = element7.getChildren("nodeformgroup");
/*      */         
/* 1011 */         for (byte b29 = 0; b29 < list37.size(); b29++) {
/* 1012 */           Element element = list37.get(b29);
/*      */           
/* 1014 */           paramWorkflowData.saveNodeFormGroup(element);
/*      */         } 
/* 1016 */         List<Element> list38 = element7.getChildren("nodegroup");
/*      */         
/* 1018 */         for (byte b30 = 0; b30 < list38.size(); b30++) {
/* 1019 */           Element element = list38.get(b30);
/*      */           
/* 1021 */           paramWorkflowData.saveNodeGroup(element);
/* 1022 */           if (this.type.equals("0")) {
/*      */             
/* 1024 */             if (b4 == 0) {
/*      */               
/* 1026 */               List<Element> list39 = this.xml.getElementById("batchrulebase").getChildren("batchrulebase");
/*      */               
/* 1028 */               for (byte b31 = 0; b31 < list39.size(); b31++) {
/* 1029 */                 Element element10 = list39.get(b31);
/*      */                 
/* 1031 */                 paramWorkflowData.saveBatchRuleBase(element10);
/*      */                 
/* 1033 */                 List list40 = element10.getChildren("batchruleexpressionbase");
/* 1034 */                 for (Element element11 : list40) {
/* 1035 */                   paramWorkflowData.saveBatchRuleExpressionbase(element11);
/*      */                 }
/*      */                 
/* 1038 */                 List list41 = element10.getChildren("batchruleexpressions");
/* 1039 */                 for (Element element11 : list41) {
/* 1040 */                   paramWorkflowData.saveBatchRuleExpressions(element11);
/*      */                 }
/*      */               } 
/*      */             } 
/*      */             
/* 1045 */             List<Element> list = element.getChildren("nodegroupdetail");
/*      */             
/* 1047 */             for (byte b = 0; b < list.size(); b++) {
/* 1048 */               Element element10 = list.get(b);
/*      */               
/* 1050 */               paramWorkflowData.saveNodeGroupDetail(element10);
/*      */             } 
/*      */           } 
/*      */         } 
/*      */       } 
/*      */ 
/*      */       
/* 1057 */       List list7 = this.xml.getElementById("signInputSet").getChildren("signInputSet");
/* 1058 */       for (Element element7 : list7) {
/* 1059 */         Element element8 = element7;
/* 1060 */         paramWorkflowData.saveSignInputSet(element8);
/*      */       } 
/*      */ 
/*      */       
/* 1064 */       List list8 = this.xml.getElementById("workflow_specialAddInOperate").getChildren("workflow_specialAddInOperate");
/* 1065 */       for (Element element7 : list8) {
/* 1066 */         Element element8 = element7;
/* 1067 */         paramWorkflowData.saveNodeOperate(element8);
/*      */       } 
/*      */ 
/*      */       
/* 1071 */       paramWorkflowData.saveViewNode();
/* 1072 */       paramWorkflowData.saveRejectablenodes();
/* 1073 */       (new RequestSecLevelBiz()).workflowImportSecFieldOption(Util.getIntValue(paramWorkflowData.getNewwfid()));
/*      */ 
/*      */       
/* 1076 */       List<Element> list9 = this.xml.getElementById("flownodelink").getChildren("flownodelink");
/*      */       
/* 1078 */       ArrayList<XmlBean> arrayList = new ArrayList();
/* 1079 */       for (Element element : list9) {
/* 1080 */         arrayList.add(this.xml.getSingleXmlBeanByElement(element));
/*      */       }
/* 1082 */       this.crossSystem.sortFlowNodeLinks(arrayList);
/* 1083 */       for (XmlBean xmlBean : arrayList) {
/* 1084 */         paramWorkflowData.saveWfNodeLink(xmlBean);
/*      */       }
/* 1086 */       for (byte b5 = 0; b5 < list9.size(); b5++) {
/* 1087 */         Element element = list9.get(b5);
/* 1088 */         if ("0".equals(this.type)) {
/*      */           
/* 1090 */           List list18 = element.getChildren("rulebase");
/* 1091 */           for (Element element7 : list18) {
/* 1092 */             paramWorkflowData.saveRuleBase(element7, 1);
/*      */             
/* 1094 */             List list21 = element7.getChildren("ruleexpressionbase");
/* 1095 */             for (Element element8 : list21) {
/* 1096 */               paramWorkflowData.saveRuleExpressionbase(element8);
/*      */             }
/*      */             
/* 1099 */             List list22 = element7.getChildren("ruleexpressions");
/* 1100 */             for (Element element8 : list22) {
/* 1101 */               paramWorkflowData.saveRuleExpressions(element8);
/*      */             }
/*      */           } 
/*      */           
/* 1105 */           List list19 = element.getChildren("rulemaplist");
/* 1106 */           for (Element element7 : list19) {
/* 1107 */             paramWorkflowData.saveRuleMapList(element7, 1);
/*      */           }
/*      */           
/* 1110 */           List list20 = element.getChildren("rulemapitem");
/* 1111 */           for (Element element7 : list20) {
/* 1112 */             paramWorkflowData.saveRuleMapItem(element7, 1);
/*      */           }
/*      */         } 
/*      */         
/* 1116 */         List<Element> list = element.getChildren("nodelinkext");
/* 1117 */         for (byte b = 0; b < list.size(); b++) {
/* 1118 */           Element element7 = list.get(b);
/* 1119 */           paramWorkflowData.saveWfNodeLinkExt(element7);
/*      */         } 
/*      */       } 
/* 1122 */       if (this.type.equals("0")) {
/*      */         
/* 1124 */         List<Element> list = this.xml.getElementById("nodegroupdtlmatrix").getChildren("nodegroupdtlmatrix");
/* 1125 */         for (byte b = 0; b < list.size(); b++) {
/* 1126 */           Element element = list.get(b);
/* 1127 */           paramWorkflowData.saveNodeGroupDetailMatrix(element);
/*      */         } 
/*      */         
/* 1130 */         List list18 = this.xml.getElementById("batchrulevariablebase").getChildren("batchrulevariablebase");
/* 1131 */         for (Element element : list18) {
/* 1132 */           paramWorkflowData.saveBatchRuleVariablebase(element);
/*      */         }
/*      */         
/* 1135 */         List list19 = this.xml.getElementById("batchrulemapitem").getChildren("batchrulemapitem");
/* 1136 */         for (Element element : list19) {
/* 1137 */           paramWorkflowData.saveBatchRuleMapItem(element);
/*      */         }
/*      */         
/* 1140 */         List list20 = this.xml.getElementById("batchrulemaplist").getChildren("batchrulemaplist");
/* 1141 */         for (Element element : list20) {
/* 1142 */           paramWorkflowData.saveBatchRuleMapList(element);
/*      */         }
/*      */         
/* 1145 */         List list21 = this.xml.getElementById("workflowmatrixdetail").getChildren("workflowmatrixdetail");
/* 1146 */         for (Element element : list21) {
/* 1147 */           paramWorkflowData.saveWorkflowMatrixDetail(element);
/*      */         }
/*      */       } 
/*      */       
/* 1151 */       Element element6 = this.xml.getElementById("flowgroup");
/* 1152 */       if (null != element6) {
/* 1153 */         List<Element> list = this.xml.getElementById("flowgroup").getChildren("flowgroup");
/* 1154 */         for (byte b = 0; b < list.size(); b++) {
/* 1155 */           Element element = list.get(b);
/*      */           
/* 1157 */           paramWorkflowData.saveWorkflowGroup(element);
/*      */         } 
/*      */       } 
/*      */ 
/*      */       
/* 1162 */       List<Element> list10 = this.xml.getElementById("functionmanage").getChildren("functionmanage");
/* 1163 */       for (byte b6 = 0; b6 < list10.size(); b6++) {
/* 1164 */         Element element = list10.get(b6);
/* 1165 */         paramWorkflowData.saveFunctionManage(element);
/*      */       } 
/*      */ 
/*      */       
/* 1169 */       List<Element> list11 = this.xml.getElementById("requestwithdraw").getChildren("requestwithdraw");
/* 1170 */       for (byte b7 = 0; b7 < list11.size(); b7++) {
/* 1171 */         Element element = list11.get(b7);
/* 1172 */         paramWorkflowData.saveRequestWithdraw(element);
/*      */       } 
/*      */ 
/*      */       
/* 1176 */       List<Element> list12 = this.xml.getElementById("titleset").getChildren("titleset");
/* 1177 */       for (byte b8 = 0; b8 < list12.size(); b8++) {
/* 1178 */         Element element = list12.get(b8);
/* 1179 */         paramWorkflowData.saveTitleSet(element);
/*      */       } 
/*      */ 
/*      */       
/* 1183 */       List<Element> list13 = this.xml.getElementById("waterMark").getChildren("waterMark");
/* 1184 */       for (byte b9 = 0; b9 < list13.size(); b9++) {
/* 1185 */         Element element = list13.get(b9);
/* 1186 */         paramWorkflowData.saveWaterMark(element);
/*      */       } 
/*      */       
/* 1189 */       this.nodelinkMap = paramWorkflowData.getNodelinkMap();
/* 1190 */       if (this.type.equals("0")) {
/*      */         
/* 1192 */         List<Element> list18 = this.xml.getElementById("planset").getChildren("planset");
/* 1193 */         for (byte b10 = 0; b10 < list18.size(); b10++) {
/* 1194 */           Element element = list18.get(b10);
/* 1195 */           paramWorkflowData.savePlanSet(element);
/*      */         } 
/*      */         
/* 1198 */         List<Element> list19 = this.xml.getElementById("browsefunction").getChildren("browsefunction");
/* 1199 */         for (byte b11 = 0; b11 < list19.size(); b11++) {
/* 1200 */           Element element = list19.get(b11);
/* 1201 */           paramWorkflowData.saveRstBrowseFunction(element);
/*      */         } 
/* 1203 */         List<Element> list20 = this.xml.getElementById("browdef").getChildren("browdef");
/* 1204 */         for (byte b12 = 0; b12 < list20.size(); b12++) {
/* 1205 */           Element element = list20.get(b12);
/* 1206 */           paramWorkflowData.saveBrowDef(element);
/*      */         } 
/* 1208 */         List<Element> list21 = this.xml.getElementById("browdeffield").getChildren("browdeffield");
/* 1209 */         for (byte b13 = 0; b13 < list21.size(); b13++) {
/* 1210 */           Element element = list21.get(b13);
/* 1211 */           paramWorkflowData.saveBrowDefField(element);
/*      */         } 
/* 1213 */         List<Element> list22 = this.xml.getElementById("browdefdataranage").getChildren("browdefdataranage");
/* 1214 */         for (byte b14 = 0; b14 < list22.size(); b14++) {
/* 1215 */           Element element = list22.get(b14);
/* 1216 */           paramWorkflowData.saveBrowBdfDataRanage(element);
/*      */         } 
/* 1218 */         List<Element> list23 = this.xml.getElementById("browdeftab").getChildren("browdeftab");
/* 1219 */         for (byte b15 = 0; b15 < list23.size(); b15++) {
/* 1220 */           Element element = list23.get(b15);
/* 1221 */           paramWorkflowData.saveBrowBdfTab(element);
/*      */         } 
/* 1223 */         List<Element> list24 = this.xml.getElementById("browdefdrmatrix").getChildren("browdefdrmatrix");
/* 1224 */         for (byte b16 = 0; b16 < list24.size(); b16++) {
/* 1225 */           Element element = list24.get(b16);
/* 1226 */           paramWorkflowData.saveBrowBdfDrMatrix(element);
/*      */         } 
/* 1228 */         List<Element> list25 = this.xml.getElementById("browdefdrmatrixdetail").getChildren("browdefdrmatrixdetail");
/* 1229 */         for (byte b17 = 0; b17 < list25.size(); b17++) {
/* 1230 */           Element element = list25.get(b17);
/* 1231 */           paramWorkflowData.saveBrowBdfDrMatrixDetail(element);
/*      */         } 
/*      */         
/* 1234 */         List<Element> list26 = this.xml.getElementById("code").getChildren("code");
/* 1235 */         for (byte b18 = 0; b18 < list26.size(); b18++) {
/* 1236 */           Element element = list26.get(b18);
/* 1237 */           paramWorkflowData.saveCode(element);
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */           
/* 1247 */           List<Element> list47 = element.getChildren("shortnamesetting");
/* 1248 */           for (byte b29 = 0; b29 < list47.size(); b29++) {
/* 1249 */             Element element7 = list47.get(b29);
/*      */             
/* 1251 */             paramWorkflowData.saveShortNameSetting(element7);
/*      */           } 
/* 1253 */           List<Element> list48 = element.getChildren("supsubcomabbr");
/* 1254 */           for (byte b30 = 0; b30 < list48.size(); b30++) {
/* 1255 */             Element element7 = list48.get(b30);
/*      */             
/* 1257 */             paramWorkflowData.saveSupSubComAbbr(element7);
/*      */           } 
/* 1259 */           List<Element> list49 = element.getChildren("subcomabbr");
/* 1260 */           for (byte b31 = 0; b31 < list49.size(); b31++) {
/* 1261 */             Element element7 = list49.get(b31);
/*      */             
/* 1263 */             paramWorkflowData.saveSubComAbbr(element7);
/*      */           } 
/* 1265 */           List<Element> list50 = element.getChildren("deptabbr");
/* 1266 */           for (byte b32 = 0; b32 < list50.size(); b32++) {
/* 1267 */             Element element7 = list50.get(b32);
/*      */             
/* 1269 */             paramWorkflowData.saveSubComAbbr(element7);
/*      */           } 
/*      */         } 
/*      */ 
/*      */         
/* 1274 */         List<Element> list27 = this.xml.getElementById("coderegulate").getChildren("coderegulate");
/* 1275 */         for (byte b19 = 0; b19 < list27.size(); b19++) {
/* 1276 */           Element element = list27.get(b19);
/*      */           
/* 1278 */           paramWorkflowData.saveCodeRegulate(element);
/*      */         } 
/*      */         
/* 1281 */         List<Element> list28 = this.xml.getElementById("urgerdetail").getChildren("urgerdetail");
/* 1282 */         for (byte b20 = 0; b20 < list28.size(); b20++) {
/* 1283 */           Element element = list28.get(b20);
/* 1284 */           paramWorkflowData.saveUrgerDetail(element);
/*      */         } 
/*      */         
/* 1287 */         List<Element> list29 = this.xml.getElementById("createdoc").getChildren("createdoc");
/* 1288 */         for (byte b21 = 0; b21 < list29.size(); b21++) {
/* 1289 */           Element element = list29.get(b21);
/*      */           
/* 1291 */           paramWorkflowData.saveCreateDoc(element);
/* 1292 */           List<Element> list47 = element.getChildren("docshow");
/* 1293 */           for (byte b29 = 0; b29 < list47.size(); b29++) {
/* 1294 */             Element element7 = list47.get(b29);
/*      */             
/* 1296 */             paramWorkflowData.saveDocShow(element7);
/*      */           } 
/* 1298 */           List<Element> list48 = element.getChildren("docprop");
/* 1299 */           for (byte b30 = 0; b30 < list48.size(); b30++) {
/* 1300 */             Element element7 = list48.get(b30);
/*      */             
/* 1302 */             paramWorkflowData.saveDocProp(element7);
/* 1303 */             List<Element> list = element7.getChildren("docpropdetail");
/* 1304 */             for (byte b = 0; b < list.size(); b++) {
/* 1305 */               Element element8 = list.get(b);
/*      */               
/* 1307 */               paramWorkflowData.saveDocPropDetail(element8);
/*      */             } 
/*      */           } 
/*      */           
/* 1311 */           List<Element> list49 = element.getChildren("barcodeset");
/* 1312 */           for (byte b31 = 0; b31 < list49.size(); b31++) {
/* 1313 */             Element element7 = list49.get(b31);
/*      */             
/* 1315 */             paramWorkflowData.saveBarCodeSet(element7);
/* 1316 */             List<Element> list = element.getChildren("barcodesetdetail");
/* 1317 */             for (byte b = 0; b < list.size(); b++) {
/* 1318 */               Element element8 = list.get(b);
/*      */               
/* 1320 */               paramWorkflowData.saveBarCodeSetDetail(element8);
/*      */             } 
/*      */           } 
/*      */         } 
/*      */ 
/*      */         
/* 1326 */         List<Element> list30 = this.xml.getElementById("subwfset").getChildren("subwfset");
/* 1327 */         for (byte b22 = 0; b22 < list30.size(); b22++) {
/* 1328 */           Element element = list30.get(b22);
/* 1329 */           paramWorkflowData.saveSubwfSet(element);
/* 1330 */           List<Element> list = element.getChildren("subwfsetdetail");
/* 1331 */           for (byte b = 0; b < list.size(); b++) {
/* 1332 */             Element element7 = list.get(b);
/* 1333 */             paramWorkflowData.saveSubwfSetDetail(element7);
/*      */           } 
/*      */         } 
/*      */ 
/*      */         
/* 1338 */         List<Element> list31 = this.xml.getElementById("subdiffwfset").getChildren("subdiffwfset");
/* 1339 */         for (byte b23 = 0; b23 < list31.size(); b23++) {
/* 1340 */           Element element = list31.get(b23);
/* 1341 */           paramWorkflowData.saveSubDiffWfSet(element);
/*      */           
/* 1343 */           List<Element> list = element.getChildren("subdiffwfsetsubwf");
/* 1344 */           for (byte b = 0; b < list.size(); b++) {
/* 1345 */             Element element7 = list.get(b);
/* 1346 */             paramWorkflowData.saveTriWfDiffFieldDetail(element7);
/*      */             
/* 1348 */             List<Element> list47 = element7.getChildren("TriDiffWfSubWfField");
/* 1349 */             for (byte b29 = 0; b29 < list47.size(); b29++) {
/* 1350 */               Element element8 = list47.get(b29);
/* 1351 */               paramWorkflowData.saveTriDiffWfSubWfField(element8);
/*      */             } 
/*      */           } 
/*      */         } 
/* 1355 */         paramWorkflowData.updateFlowNode();
/*      */ 
/*      */ 
/*      */         
/* 1359 */         List list32 = this.xml.getElementById("trisubwfrulebase").getChildren("trisubwfrulebase");
/* 1360 */         for (Element element : list32) {
/* 1361 */           paramWorkflowData.saveRuleBase(element, 7);
/*      */         }
/*      */ 
/*      */         
/* 1365 */         List list33 = this.xml.getElementById("trisubwfruleexpressionbase").getChildren("trisubwfruleexpressionbase");
/* 1366 */         for (Element element : list33) {
/* 1367 */           paramWorkflowData.saveRuleExpressionbase(element);
/*      */         }
/*      */         
/* 1370 */         List list34 = this.xml.getElementById("trisubwfruleexpressions").getChildren("trisubwfruleexpressions");
/* 1371 */         for (Element element : list34) {
/* 1372 */           paramWorkflowData.saveRuleExpressions(element);
/*      */         }
/*      */         
/* 1375 */         List list35 = this.xml.getElementById("trisubwfrulemaplist").getChildren("trisubwfrulemaplist");
/* 1376 */         for (Element element : list35) {
/* 1377 */           paramWorkflowData.saveRuleMapList(element, 7);
/*      */         }
/*      */         
/* 1380 */         List list36 = this.xml.getElementById("trisubwfrulemapitem").getChildren("trisubwfrulemapitem");
/* 1381 */         for (Element element : list36) {
/* 1382 */           paramWorkflowData.saveRuleMapItem(element, 7);
/*      */         }
/*      */ 
/*      */ 
/*      */ 
/*      */         
/* 1388 */         List list37 = this.xml.getElementById("tridiffsubwfrulebase").getChildren("tridiffsubwfrulebase");
/* 1389 */         for (Element element : list37) {
/* 1390 */           paramWorkflowData.saveRuleBase(element, 8);
/*      */         }
/*      */ 
/*      */         
/* 1394 */         List list38 = this.xml.getElementById("tridiffsubwfruleexpressionbase").getChildren("tridiffsubwfruleexpressionbase");
/* 1395 */         for (Element element : list38) {
/* 1396 */           paramWorkflowData.saveRuleExpressionbase(element);
/*      */         }
/*      */         
/* 1399 */         List list39 = this.xml.getElementById("tridiffsubwfruleexpressions").getChildren("tridiffsubwfruleexpressions");
/* 1400 */         for (Element element : list39) {
/* 1401 */           paramWorkflowData.saveRuleExpressions(element);
/*      */         }
/*      */ 
/*      */         
/* 1405 */         List list40 = this.xml.getElementById("tridiffsubwfrulemaplist").getChildren("tridiffsubwfrulemaplist");
/* 1406 */         for (Element element : list40) {
/* 1407 */           paramWorkflowData.saveRuleMapList(element, 8);
/*      */         }
/*      */         
/* 1410 */         List list41 = this.xml.getElementById("tridiffsubwfrulemapitem").getChildren("tridiffsubwfrulemapitem");
/* 1411 */         for (Element element : list41) {
/* 1412 */           paramWorkflowData.saveRuleMapItem(element, 8);
/*      */         }
/* 1414 */         paramWorkflowData.replaceRuleInfo(this.workflowid, 7);
/* 1415 */         paramWorkflowData.replaceRuleInfo(this.workflowid, 8);
/*      */ 
/*      */         
/* 1418 */         List<Element> list42 = this.xml.getElementById("createtask").getChildren("createtask");
/* 1419 */         for (byte b24 = 0; b24 < list42.size(); b24++) {
/* 1420 */           Element element = list42.get(b24);
/* 1421 */           paramWorkflowData.saveCreateTask(element);
/* 1422 */           List<Element> list47 = element.getChildren("createtaskgroup");
/* 1423 */           for (byte b29 = 0; b29 < list47.size(); b29++) {
/* 1424 */             Element element7 = list47.get(b29);
/* 1425 */             paramWorkflowData.saveCreateTaskGroup(element7);
/*      */           } 
/* 1427 */           List<Element> list48 = element.getChildren("createtaskdetail");
/* 1428 */           for (byte b30 = 0; b30 < list48.size(); b30++) {
/* 1429 */             Element element7 = list48.get(b30);
/* 1430 */             paramWorkflowData.saveCreateTaskDetail(element7);
/*      */           } 
/*      */         } 
/*      */         
/* 1434 */         List<Element> list43 = this.xml.getElementById("createplan").getChildren("createplan");
/* 1435 */         for (byte b25 = 0; b25 < list43.size(); b25++) {
/* 1436 */           Element element = list43.get(b25);
/* 1437 */           paramWorkflowData.saveCreatePlan(element);
/* 1438 */           List<Element> list47 = element.getChildren("createplangroup");
/* 1439 */           for (byte b29 = 0; b29 < list47.size(); b29++) {
/* 1440 */             Element element7 = list47.get(b29);
/* 1441 */             paramWorkflowData.saveCreatePlanGroup(element7);
/*      */           } 
/* 1443 */           List<Element> list48 = element.getChildren("createplandetail");
/* 1444 */           for (byte b30 = 0; b30 < list48.size(); b30++) {
/* 1445 */             Element element7 = list48.get(b30);
/* 1446 */             paramWorkflowData.saveCreatePlanDetail(element7);
/*      */           } 
/*      */         } 
/*      */         
/* 1450 */         List<Element> list44 = this.xml.getElementById("todocprop").getChildren("todocprop");
/* 1451 */         for (byte b26 = 0; b26 < list44.size(); b26++) {
/* 1452 */           Element element = list44.get(b26);
/* 1453 */           paramWorkflowData.saveToDocProp(element);
/* 1454 */           List<Element> list = element.getChildren("todocpropdetail");
/* 1455 */           for (byte b = 0; b < list.size(); b++) {
/* 1456 */             Element element7 = list.get(b);
/* 1457 */             paramWorkflowData.saveToDocPropDetail(element7);
/*      */           } 
/*      */         } 
/*      */         
/* 1461 */         List<Element> list45 = this.xml.getElementById("viewattrlinkage").getChildren("viewattrlinkage");
/* 1462 */         for (byte b27 = 0; b27 < list45.size(); b27++) {
/* 1463 */           Element element = list45.get(b27);
/* 1464 */           paramWorkflowData.saveViewAttrLinkAge(element);
/*      */         } 
/*      */         
/* 1467 */         List<Element> list46 = this.xml.getElementById("datainputentry").getChildren("datainputentry");
/* 1468 */         for (byte b28 = 0; b28 < list46.size(); b28++) {
/* 1469 */           Element element = list46.get(b28);
/* 1470 */           paramWorkflowData.saveDataInputEntry(element);
/* 1471 */           List<Element> list = element.getChildren("datainputmain");
/* 1472 */           for (byte b = 0; b < list.size(); b++) {
/* 1473 */             Element element7 = list.get(b);
/* 1474 */             paramWorkflowData.saveDataInputMain(element7);
/*      */             
/* 1476 */             List<Element> list47 = element7.getChildren("datainputtable");
/* 1477 */             for (byte b29 = 0; b29 < list47.size(); b29++) {
/* 1478 */               Element element8 = list47.get(b29);
/* 1479 */               paramWorkflowData.saveDataInputTable(element8);
/* 1480 */               List<Element> list48 = element8.getChildren("datainputfield");
/* 1481 */               for (byte b30 = 0; b30 < list48.size(); b30++) {
/* 1482 */                 Element element9 = list48.get(b30);
/* 1483 */                 paramWorkflowData.saveDataInputField(element9);
/*      */               } 
/*      */             } 
/*      */           } 
/*      */         } 
/*      */       } 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/* 1493 */       List list14 = this.xml.getElementById("remindcontent").getChildren("remindcontent");
/* 1494 */       paramWorkflowData.saveRemindContent(list14, this.workflowid, this.oldworkflowid, this.nodeMap);
/*      */ 
/*      */       
/* 1497 */       List list15 = this.xml.getElementById("emailremindset").getChildren("emailremindset");
/* 1498 */       Map map = paramWorkflowData.saveEmailRemindSet(list15, this.workflowid, this.nodeMap);
/*      */ 
/*      */       
/* 1501 */       if (!map.isEmpty()) {
/* 1502 */         List list = this.xml.getElementById("emailremindcontent").getChildren("emailremindcontent");
/* 1503 */         paramWorkflowData.saveEmailRemindContent(list, this.workflowid, map);
/*      */       } 
/*      */ 
/*      */       
/* 1507 */       List list16 = this.xml.getElementById("emailapproveset").getChildren("emailapproveset");
/* 1508 */       paramWorkflowData.saveEmailApproveSet(list16, this.workflowid, this.oldworkflowid, this.nodeMap);
/*      */ 
/*      */       
/* 1511 */       List list17 = this.xml.getElementById("emailapprovecontent").getChildren("emailapprovecontent");
/* 1512 */       paramWorkflowData.saveEmailApproveContent(list17, this.workflowid, this.oldworkflowid, this.nodeMap);
/*      */       
/* 1514 */       if (this.type.equals("0"))
/*      */       {
/* 1516 */         paramWorkflowData.saveCreateList();
/*      */       }
/*      */ 
/*      */       
/* 1520 */       paramWorkflowData.updateLockNodes();
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/* 1527 */       this.crossSystem.specialHandleAfterImportSuccess(this);
/* 1528 */       this.MsgMap = paramWorkflowData.getMsgMap();
/* 1529 */       return true;
/* 1530 */     } catch (Exception exception) {
/* 1531 */       this.exceptionMsg = exception.getMessage();
/* 1532 */       exception.printStackTrace();
/*      */       
/* 1534 */       rollbackImportData(this.htmlLabelMap, this.maintable, this.detailTableList, this.formFields, this.formDetailFields, this.formid, this.isbill, this.workflowid);
/* 1535 */       return false;
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private WorkflowXml parseWorkflowXml(File paramFile) throws Exception {
/* 1547 */     WorkflowXml workflowXml = new WorkflowXml(this.crossSystem);
/* 1548 */     if (paramFile.exists()) {
/* 1549 */       SAXBuilder sAXBuilder = new SAXBuilder();
/* 1550 */       SecurityMethodUtil.setSaxBuilderFeature(sAXBuilder);
/* 1551 */       Document document = sAXBuilder.build(paramFile);
/* 1552 */       workflowXml.parseWorkflowXml(document);
/*      */     } 
/* 1554 */     return workflowXml;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private WorkflowXml parseWorkflowXml(InputStream paramInputStream) throws Exception {
/* 1565 */     WorkflowXml workflowXml = new WorkflowXml(this.crossSystem);
/* 1566 */     SAXBuilder sAXBuilder = new SAXBuilder();
/* 1567 */     SecurityMethodUtil.setSaxBuilderFeature(sAXBuilder);
/* 1568 */     Document document = sAXBuilder.build(paramInputStream);
/* 1569 */     workflowXml.parseWorkflowXml(document);
/* 1570 */     return workflowXml;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private void rollbackImportData(Map paramMap1, String paramString1, List<String> paramList, Map paramMap2, Map paramMap3, String paramString2, String paramString3, String paramString4) {
/* 1585 */     this.lm.writeLog("回滚导入的流程数据 开始..........");
/*      */ 
/*      */     
/* 1588 */     RecordSet recordSet = new RecordSet();
/*      */ 
/*      */     
/* 1591 */     if (null != paramMap1) {
/* 1592 */       String str1 = "";
/* 1593 */       Collection collection = paramMap1.values();
/* 1594 */       for (String str2 : collection)
/*      */       {
/* 1596 */         str1 = str1 + (str1.equals("") ? str2 : ("," + str2));
/*      */       }
/* 1598 */       if (!str1.equals(""));
/*      */     } 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/* 1604 */     String str = "";
/*      */     
/* 1606 */     paramString1 = Util.null2String(paramString1);
/*      */ 
/*      */     
/* 1609 */     if (Util.getIntValue(paramString2, 0) > 0 && paramString3.equals("1")) {
/* 1610 */       if (!paramString1.equals("") && 
/* 1611 */         null != paramMap2) {
/* 1612 */         Set set = paramMap2.keySet();
/* 1613 */         for (String str1 : set)
/*      */         {
/* 1615 */           String str2 = "alter table " + paramString1 + " drop column " + str1;
/* 1616 */           recordSet.executeSql(str2);
/*      */         }
/*      */       
/*      */       }
/*      */     
/*      */     }
/* 1622 */     else if (Util.getIntValue(paramString2, 0) > 0 && paramString3.equals("0")) {
/* 1623 */       if (null != paramMap2) {
/* 1624 */         Set set = paramMap2.keySet();
/* 1625 */         for (String str1 : set) {
/*      */           
/* 1627 */           String str2 = "alter table workflow_form drop column " + str1;
/* 1628 */           recordSet.executeSql(str2);
/*      */         } 
/*      */       } 
/* 1631 */       if (null != paramMap3) {
/* 1632 */         Set set = paramMap3.keySet();
/* 1633 */         for (String str1 : set)
/*      */         {
/* 1635 */           String str2 = "alter table workflow_formdetail drop column " + str1;
/* 1636 */           recordSet.executeSql(str2);
/*      */         }
/*      */       
/*      */       } 
/* 1640 */     } else if (!paramString1.equals("")) {
/*      */       
/* 1642 */       str = paramString1;
/* 1643 */       str = (str.length() > 30) ? str.substring(0, 30) : str;
/* 1644 */       String str1 = ((str.length() > 27) ? str.substring(0, 27) : str) + "_Id";
/* 1645 */       String str2 = ((str.length() > 24) ? str.substring(0, 24) : str) + "_Id_Tr";
/* 1646 */       recordSet.executeSql("drop table " + str);
/* 1647 */       if (recordSet.getDBType().equals("oracle")) {
/* 1648 */         recordSet.executeSql("drop sequence " + str1);
/* 1649 */         if ("jc".equalsIgnoreCase(recordSet.getOrgindbtype())) {
/* 1650 */           recordSet.executeSql("drop TRIGGER " + str2 + " on " + str);
/*      */         } else {
/* 1652 */           recordSet.executeSql("drop TRIGGER " + str2);
/*      */         } 
/* 1654 */       }  String str3 = "";
/* 1655 */       if (paramList != null) {
/* 1656 */         str = "";
/* 1657 */         str1 = "";
/* 1658 */         str2 = "";
/* 1659 */         for (byte b = 0; b < paramList.size(); b++) {
/* 1660 */           str3 = paramList.get(b);
/* 1661 */           str = str3;
/* 1662 */           str = (str.length() > 30) ? str.substring(0, 30) : str;
/* 1663 */           str1 = ((str.length() > 27) ? str.substring(0, 27) : str) + "_Id";
/* 1664 */           str2 = ((str.length() > 24) ? str.substring(0, 24) : str) + "_Id_Tr";
/* 1665 */           recordSet.executeSql("drop table " + str);
/* 1666 */           if (recordSet.getDBType().equals("oracle")) {
/* 1667 */             recordSet.executeSql("drop sequence " + str1);
/* 1668 */             if ("jc".equalsIgnoreCase(recordSet.getOrgindbtype())) {
/* 1669 */               recordSet.executeSql("drop TRIGGER " + str2 + " on " + str);
/*      */             } else {
/* 1671 */               recordSet.executeSql("drop TRIGGER " + str2);
/*      */             } 
/*      */           } 
/*      */         } 
/*      */       } 
/*      */     } 
/*      */     
/* 1678 */     paramString4 = Util.null2String(paramString4);
/* 1679 */     if (!paramString4.equals("")) {
/*      */       
/* 1681 */       recordSet.executeSql("delete from Workflow_DataInput_field where datainputid in(select id from Workflow_DataInput_main where entryid in(select id from Workflow_DataInput_entry where (workflowid=" + paramString4 + ")))");
/* 1682 */       recordSet.executeSql("delete from Workflow_DataInput_table where datainputid in(select id from Workflow_DataInput_main where entryid in(select id from Workflow_DataInput_entry where (workflowid=" + paramString4 + ")))");
/* 1683 */       recordSet.executeSql("delete from Workflow_DataInput_main where entryid in(select id from Workflow_DataInput_entry where (workflowid=" + paramString4 + "))");
/* 1684 */       recordSet.executeSql("delete from Workflow_DataInput_entry where (workflowid=" + paramString4 + ")");
/*      */       
/* 1686 */       recordSet.executeSql("delete from workflow_viewattrlinkage where (workflowid=" + paramString4 + ")");
/*      */       
/* 1688 */       recordSet.executeSql("delete from WorkflowToDocPropdetail where docpropid in(select id from WorkflowToDocProp where (workflowid=" + paramString4 + "))");
/* 1689 */       recordSet.executeSql("delete from WorkflowToDocProp where (workflowid=" + paramString4 + ")");
/*      */       
/* 1691 */       recordSet.executeSql("delete from workflow_createplandetail where createplanid in(select id from workflow_createplan where (wfid=" + paramString4 + "))");
/* 1692 */       recordSet.executeSql("delete from workflow_createplangroup where createplanid in(select id from workflow_createplan where (wfid=" + paramString4 + "))");
/* 1693 */       recordSet.executeSql("delete from workflow_createplan where (wfid=" + paramString4 + ")");
/*      */       
/* 1695 */       recordSet.executeSql("delete from workflow_createtaskdetail where createtaskid in(select id from workflow_createtask where (wfid=" + paramString4 + "))");
/* 1696 */       recordSet.executeSql("delete from workflow_createtaskgroup where createtaskid in(select id from workflow_createtask where (wfid=" + paramString4 + "))");
/* 1697 */       recordSet.executeSql("delete from workflow_createtask where (wfid=" + paramString4 + ")");
/*      */       
/* 1699 */       recordSet.executeSql("delete from Workflow_SubwfSetdetail where subwfsetid in(select id from Workflow_SubwfSet where (mainworkflowid=" + paramString4 + "))");
/* 1700 */       recordSet.executeSql("delete from Workflow_SubwfSet where (mainworkflowid=" + paramString4 + ")");
/*      */ 
/*      */       
/* 1703 */       recordSet.executeSql("delete from Workflow_TriDiffWfSubWfField where triDiffWfSubWfId in (select id from Workflow_TriDiffWfSubWf where triDiffWfDiffFieldId in(select id from Workflow_TriDiffWfDiffField where mainworkflowid=" + paramString4 + "))");
/* 1704 */       recordSet.executeSql("delete from Workflow_TriDiffWfSubWf where triDiffWfDiffFieldId in(select id from Workflow_TriDiffWfDiffField where mainworkflowid=" + paramString4 + ")");
/* 1705 */       recordSet.executeSql("delete from Workflow_TriDiffWfDiffField where mainworkflowid=" + paramString4);
/*      */ 
/*      */       
/* 1708 */       recordSet.executeSql("delete from Workflow_BarCodeSetDetail where barcodesetid in(select id from Workflow_BarCodeSet where (workflowid=" + paramString4 + "))");
/* 1709 */       recordSet.executeSql("delete from Workflow_BarCodeSet where (workflowid=" + paramString4 + ")");
/* 1710 */       recordSet.executeSql("delete from Workflow_DocPropDetail where docpropid in(select id from Workflow_DocProp where (workflowid=" + paramString4 + "))");
/* 1711 */       recordSet.executeSql("delete from Workflow_DocProp where (workflowid=" + paramString4 + ")");
/* 1712 */       recordSet.executeSql("delete from workflow_docshow where (flowid=" + paramString4 + ")");
/* 1713 */       recordSet.executeSql("delete from workflow_createdoc where (workflowid=" + paramString4 + ")");
/*      */       
/* 1715 */       recordSet.executeSql("delete from workflow_urgerdetail  where (workflowid=" + paramString4 + ")");
/*      */ 
/*      */ 
/*      */ 
/*      */       
/* 1720 */       if (Util.getIntValue(paramString2, 0) > 0 && paramString3.equals("1")) {
/* 1721 */         recordSet.executeSql("delete from workflow_deptAbbr where (workflowid=" + paramString4 + ") and isbill=" + paramString3);
/* 1722 */         recordSet.executeSql("delete from workflow_subComAbbr where (workflowid=" + paramString4 + ") and isbill=" + paramString3);
/* 1723 */         recordSet.executeSql("delete from workflow_supSubComAbbr where (workflowid=" + paramString4 + ") and isbill=" + paramString3);
/* 1724 */         recordSet.executeSql("delete from workflow_shortNameSetting where (workflowid=" + paramString4 + ") and isbill=" + paramString3);
/* 1725 */         recordSet.executeSql("delete from workflow_codeDetail  where (workflowid=" + paramString4 + ") and isbill=" + paramString3);
/* 1726 */         recordSet.executeSql("delete from workflow_code where (flowid=" + paramString4 + ") and isbill=" + paramString3);
/*      */       } else {
/* 1728 */         recordSet.executeSql("delete from workflow_deptAbbr where (workflowid=" + paramString4 + ") or (formid=" + paramString2 + ") and isbill=" + paramString3);
/* 1729 */         recordSet.executeSql("delete from workflow_subComAbbr where (workflowid=" + paramString4 + ") or (formid=" + paramString2 + ") and isbill=" + paramString3);
/* 1730 */         recordSet.executeSql("delete from workflow_supSubComAbbr where (workflowid=" + paramString4 + ") or (formid=" + paramString2 + ") and isbill=" + paramString3);
/* 1731 */         recordSet.executeSql("delete from workflow_shortNameSetting where (workflowid=" + paramString4 + ") or (formid=" + paramString2 + ") and isbill=" + paramString3);
/* 1732 */         recordSet.executeSql("delete from workflow_codeDetail  where (workflowid=" + paramString4 + ") or (mainid=" + paramString2 + ") and isbill=" + paramString3);
/* 1733 */         recordSet.executeSql("delete from workflow_code where (flowid=" + paramString4 + ") or (formid=" + paramString2 + ") and isbill=" + paramString3);
/*      */       } 
/*      */       
/* 1736 */       recordSet.executeSql("delete from workflow_titleSet where flowid=" + paramString4 + "");
/*      */       
/* 1738 */       recordSet.executeSql("delete from WorkFlowPlanSet where flowid=" + paramString4 + "");
/*      */       
/* 1740 */       recordSet.executeSql("delete from workflow_function_manage where workflowid=" + paramString4 + "");
/*      */       
/* 1742 */       recordSet.executeSql("delete from workflow_nodelink where workflowid=" + paramString4 + "");
/*      */       
/* 1744 */       recordSet.executeSql("delete from shareinnerwfcreate where workflowid = " + paramString4);
/* 1745 */       recordSet.executeSql("delete from workflow_groupdetail where groupid in(select id from workflow_nodegroup where nodeid in(select nodeid from workflow_flownode where workflowid=" + paramString4 + "))");
/* 1746 */       recordSet.executeSql("delete from workflow_nodegroup where nodeid in(select nodeid from workflow_flownode where workflowid=" + paramString4 + ")");
/* 1747 */       recordSet.executeSql("delete from workflow_NodeFormGroup where nodeid in(select nodeid from workflow_flownode where workflowid=" + paramString4 + ")");
/* 1748 */       recordSet.executeSql("delete from workflow_nodeform where nodeid in(select nodeid from workflow_flownode where workflowid=" + paramString4 + ")");
/* 1749 */       recordSet.executeSql("delete from workflow_nodehtmllayout where nodeid in(select nodeid from workflow_flownode where workflowid=" + paramString4 + ")");
/* 1750 */       recordSet.executeSql("delete from workflow_nodefieldattr where nodeid in(select nodeid from workflow_flownode where workflowid=" + paramString4 + ")");
/* 1751 */       recordSet.executeSql("delete from workflow_modeview where nodeid in(select nodeid from workflow_flownode where workflowid=" + paramString4 + ")");
/* 1752 */       recordSet.executeSql("delete from workflow_nodemode where nodeid in(select nodeid from workflow_flownode where workflowid=" + paramString4 + ")");
/* 1753 */       recordSet.executeSql("delete from workflow_addinoperate where isnode=1 and objid in(select nodeid from workflow_flownode where workflowid=" + paramString4 + ") and (workflowid=" + paramString4 + ")");
/* 1754 */       recordSet.executeSql("delete from workflow_nodecustomrcmenu  where nodeid in(select nodeid from workflow_flownode where workflowid=" + paramString4 + ") and (wfid=" + paramString4 + ")");
/* 1755 */       recordSet.executeSql("delete from workflow_CustFieldName where nodeid in(select nodeid from workflow_flownode where workflowid=" + paramString4 + ") and (workflowid=" + paramString4 + ")");
/* 1756 */       recordSet.executeSql("delete from workflow_flownode where workflowid=" + paramString4 + "");
/* 1757 */       recordSet.executeSql("delete From workflow_nodebase where id in(select nodeid from workflow_flownode where workflowid=" + paramString4 + ")");
/*      */       
/* 1759 */       String[] arrayOfString = { "workflow_nodebase" };
/* 1760 */       CacheFactory.getInstance().removeCache(arrayOfString);
/*      */       
/* 1762 */       recordSet.executeSql("delete from workflow_base where id=" + paramString4 + "");
/*      */     } 
/* 1764 */     paramString2 = Util.null2String(paramString2);
/* 1765 */     if (!paramString2.equals(""))
/*      */     {
/* 1767 */       if (Util.getIntValue(paramString2, 0) > 0 && paramString3.equals("1")) {
/* 1768 */         if (null != paramMap2) {
/* 1769 */           String str1 = "";
/* 1770 */           Collection collection = paramMap2.values();
/* 1771 */           for (String str2 : collection)
/*      */           {
/* 1773 */             str1 = str1 + (str1.equals("") ? str2 : ("," + str2));
/*      */           }
/* 1775 */           if (!str1.equals("")) {
/* 1776 */             recordSet.executeSql("delete from workflow_specialfield where fieldid in(" + str1 + ")");
/* 1777 */             recordSet.executeSql("delete from workflow_selectitem where fieldid in(" + str1 + ")");
/* 1778 */             recordSet.executeSql("delete from workflow_selectitemobj where fieldid in(" + str1 + ")");
/* 1779 */             recordSet.executeSql("delete from workflow_billfield where id in(" + str1 + ")");
/*      */           
/*      */           }
/*      */         
/*      */         }
/*      */       
/*      */       }
/* 1786 */       else if (Util.getIntValue(paramString2, 0) > 0 && paramString3.equals("0")) {
/* 1787 */         recordSet.executeSql("delete from workflow_formdetailinfo where formid=" + paramString2);
/* 1788 */         if (null != paramMap2) {
/* 1789 */           String str1 = "";
/* 1790 */           Collection collection = paramMap2.values();
/* 1791 */           for (String str2 : collection)
/*      */           {
/* 1793 */             str1 = str1 + (str1.equals("") ? str2 : ("," + str2));
/*      */           }
/* 1795 */           if (!str1.equals("")) {
/* 1796 */             recordSet.executeSql("delete from workflow_specialfield where fieldid in(" + str1 + ") and isbill=0 and isform=1 ");
/* 1797 */             recordSet.executeSql("delete from workflow_selectitem where fieldidid in(" + str1 + ") and isbill=0 and isform=1 ");
/* 1798 */             recordSet.executeSql("delete from workflow_selectitemobj where fieldidid in(" + str1 + ") ");
/* 1799 */             recordSet.executeSql("delete from workflow_formdict where id in(" + str1 + ")");
/*      */           } 
/*      */         } 
/*      */         
/* 1803 */         if (null != paramMap3) {
/* 1804 */           String str1 = "";
/* 1805 */           Collection collection = paramMap3.values();
/* 1806 */           for (String str2 : collection)
/*      */           {
/* 1808 */             str1 = str1 + (str1.equals("") ? str2 : ("," + str2));
/*      */           }
/* 1810 */           if (!str1.equals("")) {
/* 1811 */             recordSet.executeSql("delete from workflow_specialfield where fieldid in(" + str1 + ") and isbill=0 and isform=1");
/* 1812 */             recordSet.executeSql("delete from workflow_selectitem where fieldidid in(" + str1 + ") and isbill=0 and isform=1");
/* 1813 */             recordSet.executeSql("delete from workflow_selectitemobj where fieldidid in(" + str1 + ")");
/* 1814 */             recordSet.executeSql("delete from workflow_formdictdetail where id in(" + str1 + ")");
/*      */           } 
/*      */         } 
/* 1817 */         recordSet.executeSql("delete from workflow_formfield where formid=" + paramString2);
/* 1818 */         recordSet.executeSql("delete from workflow_fieldlable where formid=" + paramString2);
/* 1819 */         recordSet.executeSql("delete from workflow_formmode where formid=" + paramString2 + " and isbill=" + paramString3);
/* 1820 */         recordSet.executeSql("delete from workflow_formbase where id=" + paramString2);
/*      */       } else {
/*      */         
/* 1823 */         recordSet.executeSql("delete from Workflow_billdetailtable where billid=" + paramString2 + "");
/* 1824 */         recordSet.executeSql("delete from workflow_formdetailinfo where formid=" + paramString2 + "");
/* 1825 */         recordSet.executeSql("delete from workflow_specialfield where fieldid in(select id from workflow_billfield where billid=" + paramString2 + ")");
/* 1826 */         recordSet.executeSql("delete from workflow_selectitem where fieldid in(select id from workflow_billfield where billid=" + paramString2 + ")");
/* 1827 */         recordSet.executeSql("delete from workflow_selectitemobj where fieldid in(select id from workflow_billfield where billid=" + paramString2 + ")");
/* 1828 */         recordSet.executeSql("delete from workflow_billfield where billid=" + paramString2 + "");
/* 1829 */         recordSet.executeSql("delete from workflow_formmode where formid=" + paramString2 + " and isbill=" + paramString3);
/* 1830 */         recordSet.executeSql("delete from workflow_bill where id=" + paramString2 + "");
/*      */       } 
/*      */     }
/* 1833 */     this.lm.writeLog("回滚导入的流程数据 结束..........");
/*      */   }
/*      */   
/*      */   public User getUser() {
/* 1837 */     return this.user;
/*      */   }
/*      */   
/*      */   public void setUser(User paramUser) {
/* 1841 */     this.user = paramUser;
/*      */   }
/*      */   
/*      */   public String getRemoteAddr() {
/* 1845 */     return this.remoteAddr;
/*      */   }
/*      */   
/*      */   public void setRemoteAddr(String paramString) {
/* 1849 */     this.remoteAddr = paramString;
/*      */   }
/*      */   
/*      */   public String getExceptionMsg() {
/* 1853 */     return this.exceptionMsg;
/*      */   }
/*      */   
/*      */   public void setExceptionMsg(String paramString) {
/* 1857 */     this.exceptionMsg = paramString;
/*      */   }
/*      */   
/*      */   public String getType() {
/* 1861 */     return this.type;
/*      */   }
/*      */   
/*      */   public void setType(String paramString) {
/* 1865 */     this.type = paramString;
/*      */   }
/*      */   
/*      */   public Map getMsgMap() {
/* 1869 */     return this.MsgMap;
/*      */   }
/*      */   
/*      */   public void setMsgMap(Map paramMap) {
/* 1873 */     this.MsgMap = paramMap;
/*      */   }
/*      */   
/*      */   public String getFormid() {
/* 1877 */     return this.formid;
/*      */   }
/*      */   
/*      */   public void setFormid(String paramString) {
/* 1881 */     this.formid = paramString;
/*      */   }
/*      */   
/*      */   public String getIsbill() {
/* 1885 */     return this.isbill;
/*      */   }
/*      */   
/*      */   public void setIsbill(String paramString) {
/* 1889 */     this.isbill = paramString;
/*      */   }
/*      */   
/*      */   public String getWorkflowid() {
/* 1893 */     return this.workflowid;
/*      */   }
/*      */   
/*      */   public void setWorkflowid(String paramString) {
/* 1897 */     this.workflowid = paramString;
/*      */   }
/*      */   
/*      */   public Map getFieldMap() {
/* 1901 */     return this.fieldMap;
/*      */   }
/*      */   
/*      */   public Map getNodeMap() {
/* 1905 */     return this.nodeMap;
/*      */   }
/*      */   
/*      */   public String getOldworkflowid() {
/* 1909 */     return this.oldworkflowid;
/*      */   }
/*      */   
/*      */   public Map getNodelinkMap() {
/* 1913 */     return this.nodelinkMap;
/*      */   }
/*      */   
/*      */   public void setNodelinkMap(Map paramMap) {
/* 1917 */     this.nodelinkMap = paramMap;
/*      */   }
/*      */   
/*      */   public boolean isCloudImport() {
/* 1921 */     return this.isCloudImport;
/*      */   }
/*      */   public String getIsComeFromOdocInit() {
/* 1924 */     return this.isComeFromOdocInit;
/*      */   }
/*      */   
/*      */   public void setIsComeFromOdocInit(String paramString) {
/* 1928 */     this.isComeFromOdocInit = paramString;
/*      */   }
/*      */   public String getOdocWfType() {
/* 1931 */     return this.odocWfType;
/*      */   }
/*      */   
/*      */   public void setOdocWfType(String paramString) {
/* 1935 */     this.odocWfType = paramString;
/*      */   }
/*      */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/imports/services/WorkflowDataService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */