/*      */ package weaver.workflow.selectItem;
/*      */ import java.util.ArrayList;
/*      */ import java.util.HashMap;
/*      */ import java.util.LinkedHashMap;
/*      */ import java.util.List;
/*      */ import java.util.Map;
/*      */ import java.util.UUID;
/*      */ import javax.servlet.http.HttpServletRequest;
/*      */ import weaver.conn.RecordSet;
/*      */ import weaver.conn.RecordSetTrans;
/*      */ import weaver.general.TimeUtil;
/*      */ import weaver.general.Util;
/*      */ import weaver.hrm.User;
/*      */ import weaver.systeminfo.SystemEnv;
/*      */ 
/*      */ public class SelectItemManager {
/*   17 */   private String deleteStr = "DELETE";
/*   18 */   private String updateStr = "EDIT";
/*   19 */   private String insertStr = "ADD";
/*   20 */   private String logmoduleStr = "SELECTITEM";
/*      */ 
/*      */   
/*   23 */   private int jump_pid = 0;
/*      */   public int getJump_pid() {
/*   25 */     return this.jump_pid;
/*      */   }
/*      */   public void setJump_pid(int paramInt) {
/*   28 */     this.jump_pid = paramInt;
/*      */   }
/*      */ 
/*      */   
/*      */   public Map<String, String> getSelectItemOption(String paramString) {
/*   33 */     LinkedHashMap<Object, Object> linkedHashMap = new LinkedHashMap<>();
/*   34 */     RecordSet recordSet = new RecordSet();
/*   35 */     String str = "SELECT selectvalue,selectname FROM workflow_SelectItem WHERE (cancel IS NULL OR cancel='0' OR cancel='') and isbill=1 and fieldid=" + paramString + " order by listorder ";
/*   36 */     recordSet.executeSql(str);
/*   37 */     while (recordSet.next()) {
/*   38 */       int i = Util.getIntValue(recordSet.getString("selectvalue"), 0);
/*   39 */       String str1 = Util.null2String(recordSet.getString("selectname"));
/*   40 */       linkedHashMap.put(i + "", str1);
/*      */     } 
/*   42 */     return (Map)linkedHashMap;
/*      */   }
/*      */   
/*      */   public Map<String, String> getSelectItemOptionWithOld(String paramString) {
/*   46 */     LinkedHashMap<Object, Object> linkedHashMap = new LinkedHashMap<>();
/*   47 */     RecordSet recordSet = new RecordSet();
/*   48 */     String str = "SELECT selectvalue,selectname FROM workflow_SelectItem WHERE (cancel IS NULL OR cancel='0' OR cancel='') and isbill=0 and fieldid=" + paramString + " order by listorder ";
/*   49 */     recordSet.executeSql(str);
/*   50 */     while (recordSet.next()) {
/*   51 */       int i = Util.getIntValue(recordSet.getString("selectvalue"), 0);
/*   52 */       String str1 = Util.null2String(recordSet.getString("selectname"));
/*   53 */       linkedHashMap.put(i + "", str1);
/*      */     } 
/*   55 */     return (Map)linkedHashMap;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean hasPubChoice(int paramInt1, int paramInt2, String paramString) {
/*   67 */     boolean bool = false;
/*   68 */     int i = 0;
/*   69 */     RecordSet recordSet1 = new RecordSet();
/*   70 */     RecordSet recordSet2 = new RecordSet();
/*   71 */     String str = "";
/*   72 */     if (!paramString.equals("")) {
/*   73 */       paramString = paramString.replace(" ", "");
/*   74 */       str = str + " and detailtable='" + paramString + "' ";
/*      */     } 
/*      */ 
/*      */ 
/*      */     
/*   79 */     recordSet1.executeSql("SELECT pubchoiceId FROM workflow_billfield WHERE billid=" + paramInt1 + " AND selectItemType in ('1') AND viewtype=" + paramInt2 + str);
/*   80 */     while (recordSet1.next()) {
/*   81 */       i = Util.getIntValue(recordSet1.getString("pubchoiceId"), 0);
/*   82 */       if (i > 0) {
/*   83 */         recordSet2.executeSql("SELECT COUNT(1) as tmpcount from mode_selectitempagedetail b where mainid = " + i + " and pid!='0' and cancel!='1'");
/*   84 */         if (recordSet2.next() && 
/*   85 */           recordSet2.getInt(1) > 0) {
/*   86 */           bool = true;
/*      */           
/*      */           break;
/*      */         } 
/*      */       } 
/*      */     } 
/*      */     
/*   93 */     return bool;
/*      */   }
/*      */   
/*      */   public Map<String, String> getPubChoiceMap(String paramString) {
/*   97 */     LinkedHashMap<Object, Object> linkedHashMap = new LinkedHashMap<>();
/*   98 */     RecordSet recordSet = new RecordSet();
/*   99 */     String str = "SELECT id,name FROM mode_selectitempagedetail WHERE mainid=" + paramString + " and pid=0 AND statelev=1 and (cancel IS NULL OR cancel='0' OR cancel='') ORDER BY disorder ";
/*      */     
/*  101 */     recordSet.executeSql(str);
/*  102 */     while (recordSet.next()) {
/*  103 */       int i = Util.getIntValue(recordSet.getString("id"), 0);
/*  104 */       String str1 = Util.null2String(recordSet.getString("name"));
/*  105 */       linkedHashMap.put(i + "", str1);
/*      */     } 
/*  107 */     return (Map)linkedHashMap;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getPubchoiceName(int paramInt) {
/*  116 */     String str = "";
/*  117 */     if (paramInt > 0) {
/*  118 */       RecordSet recordSet = new RecordSet();
/*  119 */       recordSet.executeSql("SELECT selectitemname FROM mode_selectitempage WHERE id = " + paramInt + " ");
/*  120 */       if (recordSet.next()) {
/*  121 */         str = recordSet.getString("selectitemname");
/*      */       }
/*      */     } 
/*  124 */     return str;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getPubchilchoiceName(int paramInt1, int paramInt2) {
/*  134 */     String str = "";
/*  135 */     if (paramInt1 > 0) {
/*  136 */       RecordSet recordSet = new RecordSet();
/*  137 */       recordSet.executeSql("SELECT name,name1,name2 FROM mode_selectitempagedetail WHERE id = " + paramInt1 + " ");
/*  138 */       if (recordSet.next()) {
/*  139 */         String str1 = recordSet.getString("name");
/*  140 */         String str2 = recordSet.getString("name1");
/*  141 */         String str3 = recordSet.getString("name2");
/*      */         
/*  143 */         str = getSelectNameByLanguage(str1, str2, str3, paramInt2);
/*      */       } 
/*      */     } 
/*  146 */     return str;
/*      */   }
/*      */   
/*      */   public String getSelectNameByLanguage(String paramString1, String paramString2, String paramString3, int paramInt) {
/*  150 */     String str = "";
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  163 */     str = paramString1;
/*      */     
/*  165 */     return str;
/*      */   }
/*      */ 
/*      */   
/*      */   public String getPubchilchoiceFieldName(int paramInt1, int paramInt2) {
/*  170 */     String str = "";
/*  171 */     if (paramInt1 > 0) {
/*  172 */       RecordSet recordSet = new RecordSet();
/*  173 */       recordSet.executeSql("SELECT bf.id,h.labelname,h.languageid FROM workflow_billfield bf,HtmlLabelInfo h WHERE bf.fieldlabel=h.indexid AND h.languageid=" + paramInt2 + " AND id = " + paramInt1 + " ");
/*      */       
/*  175 */       if (recordSet.next()) {
/*  176 */         str = recordSet.getString("labelname");
/*      */       }
/*      */     } 
/*  179 */     return str;
/*      */   }
/*      */ 
/*      */   
/*      */   public String getFieldLable(String paramString1, String paramString2) {
/*  184 */     String str1 = "";
/*  185 */     String[] arrayOfString = Util.TokenizerString2(paramString2, "+");
/*  186 */     String str2 = arrayOfString[0] + "";
/*  187 */     String str3 = arrayOfString[1] + "";
/*      */     
/*  189 */     if (str2.equals("1")) {
/*  190 */       str1 = SystemEnv.getHtmlLabelName(Util.getIntValue(paramString1), Util.getIntValue(str3));
/*      */     } else {
/*  192 */       str1 = paramString1;
/*      */     } 
/*      */     
/*  195 */     return str1;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String canDel(String paramString) {
/*  204 */     String str = "false";
/*  205 */     RecordSet recordSet = new RecordSet();
/*  206 */     recordSet.executeSql("SELECT billid FROM workflow_billfield WHERE selectItemType = '1' and pubchoiceId =" + paramString);
/*  207 */     if (!recordSet.next()) {
/*  208 */       str = "true";
/*      */     }
/*  210 */     return str;
/*      */   }
/*      */   
/*      */   public List getCanDelList(String paramString) {
/*  214 */     ArrayList<String> arrayList = new ArrayList();
/*  215 */     arrayList.add("true");
/*  216 */     RecordSet recordSet = new RecordSet();
/*  217 */     recordSet.executeSql("SELECT billid FROM workflow_billfield WHERE selectItemType = '1' and pubchoiceId =" + paramString);
/*  218 */     if (!recordSet.next()) {
/*  219 */       arrayList.add("true");
/*      */     } else {
/*  221 */       arrayList.add("false");
/*      */     } 
/*  223 */     return arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String hasDetail(String paramString1, String paramString2) {
/*  232 */     String str = SystemEnv.getHtmlLabelName(82676, Util.getIntValue(paramString2));
/*  233 */     if ("1".equals(paramString1)) {
/*  234 */       str = SystemEnv.getHtmlLabelName(82677, Util.getIntValue(paramString2));
/*      */     }
/*      */     
/*  237 */     return str;
/*      */   }
/*      */ 
/*      */   
/*      */   public String getLinkSelectItem(String paramString1, String paramString2) {
/*  242 */     String str = "";
/*  243 */     str = "<a href=javaScript:newDialog(1," + paramString2 + ")>" + paramString1 + "</a>";
/*  244 */     return str;
/*      */   }
/*      */ 
/*      */   
/*      */   public String generateID() {
/*  249 */     UUID uUID = UUID.randomUUID();
/*  250 */     return uUID.toString().replaceAll("-", "");
/*      */   }
/*      */ 
/*      */   
/*      */   public int addSelectItem(String paramString1, String paramString2, int paramInt) {
/*  255 */     int i = 0;
/*  256 */     String str1 = TimeUtil.getCurrentDateString();
/*  257 */     String str2 = TimeUtil.getOnlyCurrentTimeString();
/*  258 */     String str3 = generateID();
/*  259 */     String str4 = "insert into mode_selectitempage(selectitemname,selectitemdesc,creater,appid,createdate,createtime,uuid,operatetime)  values('" + paramString1 + "','" + paramString2 + "','" + paramInt + "','0','" + str1 + "','" + str2 + "','" + str3 + "','" + str1 + " " + str2 + "')";
/*      */     
/*  261 */     RecordSet recordSet = new RecordSet();
/*  262 */     recordSet.executeSql(str4);
/*  263 */     recordSet.executeSql("select  id from mode_selectitempage where uuid='" + str3 + "'");
/*  264 */     recordSet.next();
/*  265 */     i = recordSet.getInt("id");
/*  266 */     return i;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public int editModeSelectItem(HttpServletRequest paramHttpServletRequest, User paramUser) {
/*  277 */     RecordSet recordSet = new RecordSet();
/*  278 */     int i = Util.getIntValue(Util.null2String(paramHttpServletRequest.getParameter("id")), 0);
/*  279 */     String str1 = "" + Util.null2String(paramHttpServletRequest.getParameter("selectitemname"));
/*  280 */     String str2 = "" + Util.null2String(paramHttpServletRequest.getParameter("selectitemdesc"));
/*  281 */     String str3 = Util.null2String(Util.null2String(paramHttpServletRequest.getParameter("statelev")));
/*  282 */     String str4 = Util.null2String(Util.null2String(paramHttpServletRequest.getParameter("delids")));
/*  283 */     int j = paramUser.getUID();
/*      */     
/*  285 */     String str5 = "";
/*  286 */     if (i < 1) {
/*  287 */       i = addSelectItem(str1, str2, j);
/*  288 */       if (i < 1) {
/*  289 */         recordSet.writeLog("同步选择框出错:" + str1 + "uid:" + j + "selectitemdesc:" + str2);
/*  290 */         return i;
/*      */       } 
/*      */     } else {
/*  293 */       String str6 = TimeUtil.getCurrentDateString();
/*  294 */       String str7 = TimeUtil.getOnlyCurrentTimeString();
/*  295 */       str5 = "update mode_selectitempage set selectitemname='" + str1 + "',selectitemdesc='" + str2 + "',operatetime='" + str6 + " " + str7 + "'  where id='" + i + "'";
/*  296 */       recordSet.executeSql(str5);
/*      */     } 
/*      */     
/*  299 */     int k = Util.getIntValue(paramHttpServletRequest.getParameter("rowno"), 0);
/*      */ 
/*      */ 
/*      */     
/*  303 */     for (byte b = 0; b < k; b++) {
/*  304 */       String str6 = Util.null2String(paramHttpServletRequest.getParameter("name_" + b));
/*  305 */       double d = Util.getDoubleValue(paramHttpServletRequest.getParameter("disorder_" + b), 1.0D);
/*  306 */       String str7 = Util.null2String(paramHttpServletRequest.getParameter("defaultvalue_" + b));
/*  307 */       String str8 = "" + Util.null2String(paramHttpServletRequest.getParameter("pathcategory_" + b));
/*  308 */       String str9 = Util.null2String(paramHttpServletRequest.getParameter("maincategory_" + b));
/*  309 */       String str10 = Util.null2String(paramHttpServletRequest.getParameter("detailid_" + b)).trim();
/*  310 */       int m = Util.getIntValue(paramHttpServletRequest.getParameter("isAccordToSubCom_" + b), 0);
/*  311 */       int n = Util.getIntValue(paramHttpServletRequest.getParameter("cancel_" + b), 0);
/*  312 */       String str11 = Util.null2String(paramHttpServletRequest.getParameter("pid_" + b));
/*  313 */       int i1 = Util.getIntValue(paramHttpServletRequest.getParameter("jump_" + b), 0);
/*      */ 
/*      */ 
/*      */       
/*  317 */       if (!str6.equals("")) {
/*  318 */         if (str10.equals("")) {
/*  319 */           String str = generateID();
/*  320 */           str5 = "insert into mode_selectitempagedetail(mainid,name,disorder,defaultvalue,pathcategory,maincategory,pid,statelev,isAccordToSubCom,cancel,uuid)  values('" + i + "','" + str6 + "','" + d + "','" + str7 + "','" + str8 + "','" + str9 + "','" + str11 + "','" + str3 + "','" + m + "','" + n + "','" + str + "')";
/*      */         
/*      */         }
/*      */         else {
/*      */           
/*  325 */           str5 = "update mode_selectitempagedetail set name ='" + str6 + "',disorder='" + d + "',defaultvalue='" + str7 + "',pathcategory='" + str8 + "',maincategory='" + str9 + "',pid='" + str11 + "',statelev='" + str3 + "',cancel='" + n + "',isAccordToSubCom='" + m + "' where id='" + str10 + "' ";
/*  326 */           if (n == 1) {
/*  327 */             ArrayList<String> arrayList = new ArrayList();
/*  328 */             arrayList = getAllSubSelectItemId(arrayList, str10, -1);
/*  329 */             String str = "";
/*  330 */             for (byte b1 = 0; b1 < arrayList.size(); b1++) {
/*  331 */               str = str + "," + (String)arrayList.get(b1);
/*      */             }
/*  333 */             if (!str.equals("")) {
/*  334 */               str = str.substring(1);
/*  335 */               String str12 = "update mode_selectitempagedetail set cancel=1 where id in (" + str + ")";
/*  336 */               recordSet.executeSql(str12);
/*      */             } 
/*      */           } 
/*      */         } 
/*      */ 
/*      */         
/*  342 */         recordSet.executeSql(str5);
/*      */ 
/*      */         
/*  345 */         if (i1 == 1) {
/*  346 */           recordSet.executeSql("select max(id) as id from mode_selectitempagedetail where mainid=" + i + " and name='" + str6 + "' and disorder='" + d + "' and pid='" + str11 + "' and statelev='" + str3 + "'");
/*      */           
/*  348 */           recordSet.next();
/*  349 */           this.jump_pid = recordSet.getInt("id");
/*      */         } 
/*      */       } 
/*      */     } 
/*      */ 
/*      */     
/*  355 */     if (!str4.equals("")) {
/*      */ 
/*      */       
/*  358 */       ArrayList<String> arrayList = new ArrayList();
/*  359 */       arrayList = getAllSubSelectItemId(arrayList, str4, -1);
/*  360 */       String str6 = "";
/*  361 */       for (byte b1 = 0; b1 < arrayList.size(); b1++) {
/*  362 */         str6 = str6 + "," + (String)arrayList.get(b1);
/*      */       }
/*  364 */       String str7 = str4 + str6;
/*  365 */       str5 = "delete from mode_selectitempagedetail where  mainid = " + i + " and  id in (" + str7 + ") ";
/*  366 */       recordSet.executeSql(str5);
/*      */     } 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  373 */     return i;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public ArrayList<String> getAllSubSelectItemId(ArrayList<String> paramArrayList, String paramString, int paramInt) {
/*  384 */     RecordSet recordSet = new RecordSet();
/*  385 */     String str1 = "select * from mode_selectitempagedetail where pid in (" + paramString + ") ";
/*  386 */     if (paramInt != -1) {
/*  387 */       str1 = str1 + " and cancel=" + paramInt;
/*      */     }
/*  389 */     recordSet.executeSql(str1);
/*  390 */     String str2 = "";
/*  391 */     while (recordSet.next()) {
/*  392 */       String str = recordSet.getString("id");
/*  393 */       if (!paramArrayList.contains(str)) {
/*  394 */         paramArrayList.add(str);
/*      */       }
/*  396 */       str2 = str2 + "," + str;
/*      */     } 
/*  398 */     if (!str2.equals("")) {
/*  399 */       str2 = str2.substring(1);
/*  400 */       return getAllSubSelectItemId(paramArrayList, str2, paramInt);
/*      */     } 
/*  402 */     return paramArrayList;
/*      */   }
/*      */   
/*      */   public String saveOrUpdate(HttpServletRequest paramHttpServletRequest, User paramUser) {
/*  406 */     int i = Util.getIntValue(paramHttpServletRequest.getParameter("id"), 0);
/*  407 */     String str1 = "" + Util.null2String(paramHttpServletRequest.getParameter("selectitemname"));
/*  408 */     String str2 = "" + Util.fromScreen3(paramHttpServletRequest.getParameter("selectitemdesc"), paramUser.getLanguage());
/*  409 */     int j = editModeSelectItem(paramHttpServletRequest, paramUser);
/*  410 */     if (i < 1) {
/*  411 */       i = j;
/*  412 */       insertLog(i, str1, this.insertStr, paramUser, paramHttpServletRequest.getRemoteAddr());
/*      */     } else {
/*  414 */       insertLog(i, str1, this.updateStr, paramUser, paramHttpServletRequest.getRemoteAddr());
/*      */     } 
/*  416 */     if (i > 0) {
/*  417 */       syncPubSelectOp(i, paramUser.getLanguage());
/*      */     }
/*  419 */     return i + "";
/*      */   }
/*      */   
/*      */   public void deleteSelectItem(String paramString1, User paramUser, String paramString2) {
/*  423 */     RecordSet recordSet = new RecordSet();
/*      */     
/*  425 */     insertMainLog(paramString1, this.deleteStr, paramUser, paramString2);
/*      */     
/*  427 */     String str = "delete from mode_selectitempage where id in(" + paramString1 + ")";
/*  428 */     recordSet.executeSql(str);
/*      */   }
/*      */   
/*      */   public String getAllStateName(int paramInt1, int paramInt2, String paramString, User paramUser) {
/*  432 */     RecordSet recordSet = new RecordSet();
/*  433 */     if (paramInt1 > 0) {
/*  434 */       String str = "select * from mode_selectitempagedetail where id = '" + paramInt1 + "' and mainid=" + paramInt2 + " order by statelev";
/*  435 */       recordSet.executeSql(str);
/*  436 */       if (recordSet.next()) {
/*  437 */         String str1 = Util.null2String(recordSet.getString("name"));
/*  438 */         String str2 = Util.null2String(recordSet.getString("statelev"));
/*  439 */         int i = Util.getIntValue(recordSet.getString("pid"));
/*      */ 
/*      */ 
/*      */ 
/*      */         
/*  444 */         paramString = "/<a style='color:blue;padding:3px;' href=\"javascript:goToState('" + i + "','" + str2 + "')\">" + str1 + "</a>" + paramString;
/*      */ 
/*      */         
/*  447 */         paramString = getAllStateName(i, paramInt2, paramString, paramUser);
/*      */       } 
/*      */     } 
/*  450 */     return paramString;
/*      */   }
/*      */   
/*      */   public String getLogType(String paramString1, String paramString2) {
/*  454 */     String str = "";
/*  455 */     if (paramString1.equals(this.insertStr)) {
/*      */       
/*  457 */       str = SystemEnv.getHtmlLabelName(1421, Util.getIntValue(paramString2));
/*  458 */     } else if (paramString1.equals(this.updateStr)) {
/*      */       
/*  460 */       str = SystemEnv.getHtmlLabelName(26473, Util.getIntValue(paramString2));
/*  461 */     } else if (paramString1.equals(this.deleteStr)) {
/*      */       
/*  463 */       str = SystemEnv.getHtmlLabelName(91, Util.getIntValue(paramString2));
/*      */     } 
/*  465 */     return str;
/*      */   }
/*      */ 
/*      */   
/*      */   public void insertMainLog(String paramString1, String paramString2, User paramUser, String paramString3) {
/*  470 */     RecordSet recordSet = new RecordSet();
/*  471 */     ArrayList<String> arrayList1 = new ArrayList();
/*  472 */     ArrayList<String> arrayList2 = new ArrayList();
/*  473 */     String str = "";
/*  474 */     str = "select id,selectitemname from mode_selectitempage where id in(" + paramString1 + ")";
/*  475 */     recordSet.executeSql(str);
/*  476 */     while (recordSet.next()) {
/*  477 */       arrayList1.add(recordSet.getString("id"));
/*  478 */       arrayList2.add(recordSet.getString("selectitemname"));
/*      */     } 
/*      */     
/*  481 */     for (byte b = 0; b < arrayList1.size(); b++) {
/*  482 */       int i = Util.getIntValue(arrayList1.get(b));
/*  483 */       String str1 = arrayList2.get(b);
/*  484 */       insertLog(i, str1, paramString2, paramUser, paramString3);
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void insertDetailLog(int paramInt, String paramString1, String paramString2, User paramUser, String paramString3) {
/*  496 */     RecordSet recordSet = new RecordSet();
/*      */ 
/*      */     
/*  499 */     ArrayList<String> arrayList1 = new ArrayList();
/*  500 */     ArrayList<String> arrayList2 = new ArrayList();
/*  501 */     String str = "";
/*  502 */     str = "select id,name as selectitemname from mode_selectitempagedetail where  mainid = " + paramInt + " and pid=0 and  id in (" + paramString1 + ")";
/*  503 */     recordSet.executeSql(str);
/*  504 */     while (recordSet.next()) {
/*  505 */       arrayList1.add(recordSet.getString("id"));
/*  506 */       arrayList2.add(recordSet.getString("selectitemname"));
/*      */     } 
/*      */     
/*  509 */     for (byte b = 0; b < arrayList1.size(); b++) {
/*  510 */       int i = Util.getIntValue(arrayList1.get(b));
/*  511 */       String str1 = arrayList2.get(b);
/*      */       
/*  513 */       insertLog(i, str1, paramString2, paramUser, paramString3);
/*      */     } 
/*      */   }
/*      */ 
/*      */   
/*      */   public void insertLog(int paramInt, String paramString1, String paramString2, User paramUser, String paramString3) {
/*  519 */     String str1 = String.valueOf(paramUser.getUID());
/*  520 */     String str2 = paramUser.getLastname();
/*  521 */     String str3 = DateUtil.getCurrentTime("yyyy-MM-dd HH:mm");
/*      */     
/*  523 */     RecordSet recordSet = new RecordSet();
/*  524 */     String str4 = "";
/*  525 */     str4 = "insert into selectItemLog(objid,selectname,logmodule,logtype,operator,operatorname,optdatetime,ipaddress) values('" + paramInt + "','" + paramString1 + "','" + this.logmoduleStr + "','" + paramString2 + "','" + str1 + "','" + str2 + "','" + str3 + "','" + paramString3 + "')";
/*      */     
/*  527 */     recordSet.executeSql(str4);
/*      */   }
/*      */ 
/*      */   
/*      */   public void setSelectOpBypubid(String paramString1, int paramInt1, String paramString2, int paramInt2, int paramInt3) {
/*  532 */     setSelectOpBypubid(paramString1, paramInt1, paramString2, paramInt2, paramInt3, 1);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setSelectOpBypubid(String paramString1, int paramInt1, String paramString2, int paramInt2, int paramInt3, int paramInt4) {
/*  545 */     RecordSetTrans recordSetTrans = new RecordSetTrans();
/*  546 */     RecordSet recordSet1 = new RecordSet();
/*  547 */     RecordSet recordSet2 = new RecordSet();
/*  548 */     recordSetTrans.setAutoCommit(false);
/*  549 */     String str = "";
/*      */     try {
/*  551 */       if (paramInt1 > 0) {
/*  552 */         int i = -1;
/*      */         
/*  554 */         if ("pg".equals(recordSetTrans.getOrgindbtype())) {
/*  555 */           recordSetTrans.executeSql("delete from workflow_SelectItem where isbill=" + paramInt2 + " and fieldid=" + paramString2 + " and (pubid not in (select id from mode_selectitempagedetail where mainid = " + paramInt1 + " and pid=0) or pubid is null)");
/*      */         } else {
/*  557 */           recordSetTrans.executeSql("delete from workflow_SelectItem where isbill=" + paramInt2 + " and fieldid=" + paramString2 + " and (pubid not in (select id from mode_selectitempagedetail where mainid = " + paramInt1 + " and pid=0) or pubid is null or pubid = '')");
/*      */         } 
/*  559 */         recordSetTrans.executeQuery("select max(selectvalue) from workflow_SelectItem where isbill=" + paramInt2 + " and fieldid=" + paramString2, new Object[0]);
/*  560 */         if (recordSetTrans.next()) {
/*  561 */           i = Util.getIntValue(Util.null2String(recordSetTrans.getString(1)), -1);
/*      */         }
/*  563 */         str = "select id,name,name1,name2,disorder,defaultvalue,pathcategory,maincategory,pid,statelev,isAccordToSubCom,cancel from mode_selectitempagedetail where mainid = " + paramInt1 + " and pid=0 and statelev = 1 order by id";
/*  564 */         recordSet1.executeSql(str);
/*  565 */         while (recordSet1.next()) {
/*  566 */           String str1 = recordSet1.getString("id");
/*  567 */           String str2 = "";
/*  568 */           String str3 = Util.null2String(recordSet1.getString("name"));
/*  569 */           String str4 = Util.null2String(recordSet1.getString("name1"));
/*  570 */           String str5 = Util.null2String(recordSet1.getString("name2"));
/*      */           
/*  572 */           str2 = getSelectNameByLanguage(str3, str4, str5, paramInt3).replaceAll("'", "''");
/*  573 */           String str6 = recordSet1.getString("disorder");
/*  574 */           String str7 = "1".equals(Util.null2String(recordSet1.getString("defaultvalue"))) ? "1" : "0";
/*  575 */           if (str7.equals("1")) str7 = "y";
/*      */           
/*  577 */           String str8 = recordSet1.getString("cancel");
/*  578 */           if ("-1".equals(str8)) str8 = "0"; 
/*  579 */           String str9 = Util.null2String(recordSet1.getString("pathcategory"));
/*  580 */           String str10 = Util.null2String(recordSet1.getString("maincategory"));
/*  581 */           String str11 = Util.null2String(recordSet1.getString("isAccordToSubCom"));
/*      */ 
/*      */           
/*  584 */           recordSet2.execute("select id from workflow_SelectItem where isbill = " + paramInt2 + " and fieldid = " + paramString2 + " and pubid = " + str1);
/*  585 */           if (recordSet2.next()) {
/*  586 */             String str12 = recordSet2.getString(1);
/*  587 */             str = "update workflow_selectitem set selectname = '" + str2 + "',listorder=" + str6 + ",isdefault='" + str7 + "',cancel='" + str8 + "',docpath='" + str9 + "',docCategory='" + str10 + "',isAccordToSubCom='" + str11 + "' where isbill = " + paramInt2 + " and fieldid = " + paramString2 + " and pubid = " + str1 + " and id = " + str12;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */             
/*  596 */             recordSetTrans.executeSql(str); continue;
/*      */           } 
/*  598 */           i++;
/*  599 */           str = "INSERT INTO workflow_selectitem(fieldid,isbill,selectvalue,selectname,listorder,isdefault,cancel,docpath,docCategory,isAccordToSubCom,pubid) VALUES (" + paramString2 + ", " + paramInt2 + ", " + i + ", '" + str2 + "', " + str6 + ", '" + str7 + "', '" + str8 + "', '" + str9 + "', '" + str10 + "', '" + str11 + "', " + str1 + ")";
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */           
/*  614 */           recordSetTrans.executeSql(str);
/*      */         } 
/*      */ 
/*      */ 
/*      */         
/*  619 */         str = "update workflow_billfield set statelev=0 where id=" + paramString2 + " and billid=" + paramString1;
/*  620 */         recordSetTrans.executeSql(str);
/*      */       } 
/*      */       
/*  623 */       recordSetTrans.commit();
/*      */       
/*  625 */       if (paramInt4 == 1) syncSelectitemObj(paramInt1, Util.getIntValue(paramString2)); 
/*  626 */     } catch (Exception exception) {
/*  627 */       recordSetTrans.rollback();
/*  628 */       exception.printStackTrace();
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public Map<String, String> existsSelectItems(int paramInt1, int paramInt2) {
/*  640 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  641 */     RecordSet recordSet = new RecordSet();
/*  642 */     int i = 0;
/*  643 */     String str = "select fieldid,pubid,selectvalue from workflow_SelectItem where pubid IN (SELECT id FROM mode_selectitempagedetail WHERE pid IN (select pubid from workflow_SelectItem  where fieldid = " + paramInt1 + ")) and fieldid!=" + paramInt2 + " order by fieldid desc ";
/*  644 */     recordSet.executeSql(str);
/*  645 */     while (recordSet.next()) {
/*  646 */       int j = recordSet.getInt("fieldid");
/*  647 */       if (!i) i = j; 
/*  648 */       if (i > 0 && i != j) {
/*      */         break;
/*      */       }
/*  651 */       int k = recordSet.getInt("pubid");
/*  652 */       int m = recordSet.getInt("selectvalue");
/*  653 */       hashMap.put(k + "", m + "");
/*      */     } 
/*  655 */     return (Map)hashMap;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setSuperSelectOp(String paramString, int paramInt1, int paramInt2, int paramInt3, int paramInt4) {
/*  667 */     RecordSet recordSet = new RecordSet();
/*  668 */     int i = -1;
/*  669 */     recordSet.execute("select mainid from mode_selectitempagedetail where id in (select pubid from workflow_SelectItem  where fieldid = " + paramInt2 + " )");
/*  670 */     if (recordSet.next())
/*  671 */       i = recordSet.getInt(1); 
/*  672 */     setSuperSelectOp(paramString, paramInt1, paramInt2, paramInt3, paramInt4, i, 1);
/*      */   }
/*      */ 
/*      */   
/*      */   public void setSuperSelectOp(String paramString, int paramInt1, int paramInt2, int paramInt3, int paramInt4, int paramInt5) {
/*  677 */     setSuperSelectOp(paramString, paramInt1, paramInt2, paramInt3, paramInt4, paramInt5, 1);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setSuperSelectOp(String paramString, int paramInt1, int paramInt2, int paramInt3, int paramInt4, int paramInt5, int paramInt6) {
/*  691 */     RecordSetTrans recordSetTrans = new RecordSetTrans();
/*  692 */     recordSetTrans.setAutoCommit(false);
/*  693 */     RecordSet recordSet1 = new RecordSet();
/*  694 */     RecordSet recordSet2 = new RecordSet();
/*  695 */     RecordSet recordSet3 = new RecordSet();
/*  696 */     String str = "";
/*      */     try {
/*  698 */       if (paramInt2 > 0 && paramInt3 > 0) {
/*      */         Map<String, String> map;
/*  700 */         HashMap<Object, Object> hashMap = new HashMap<>();
/*  701 */         if (paramInt6 == 1) {
/*  702 */           map = existsSelectItems(paramInt2, paramInt3);
/*      */         }
/*      */ 
/*      */ 
/*      */ 
/*      */         
/*  708 */         if ("pg".equals(recordSetTrans.getOrgindbtype())) {
/*  709 */           recordSetTrans.executeSql("delete from workflow_SelectItem where isbill=" + paramInt1 + " and fieldid=" + paramInt3 + " and (pubid not in (select id from mode_selectitempagedetail where mainid = " + paramInt5 + " and pid > 0) or pubid is null)");
/*      */         } else {
/*  711 */           recordSetTrans.executeSql("delete from workflow_SelectItem where isbill=" + paramInt1 + " and fieldid=" + paramInt3 + " and (pubid not in (select id from mode_selectitempagedetail where mainid = " + paramInt5 + " and pid > 0) or pubid is null or pubid = '')");
/*      */         } 
/*      */         
/*  714 */         str = "update workflow_billfield set childfieldid=" + paramInt3 + " where id=" + paramInt2 + " and billid=" + paramString;
/*  715 */         recordSetTrans.executeSql(str);
/*      */         
/*  717 */         int i = -1;
/*  718 */         int j = 1;
/*  719 */         recordSet1.execute("select max(selectvalue) from workflow_SelectItem where isbill=" + paramInt1 + " and fieldid=" + paramInt3);
/*  720 */         if (recordSet1.next()) {
/*  721 */           i = Util.getIntValue(Util.null2String(recordSet1.getString(1)), -1);
/*      */         }
/*  723 */         String str1 = "select pubid from workflow_selectitem where fieldid = " + paramInt2 + " and isbill=" + paramInt1 + " ";
/*  724 */         str = "select id,mainid,name,name1,name2,disorder,defaultvalue,pathcategory,maincategory,pid,statelev,isAccordToSubCom,cancel from mode_selectitempagedetail where pid in (" + str1 + ") order by id";
/*      */         
/*  726 */         recordSet1.executeSql(str);
/*  727 */         while (recordSet1.next()) {
/*  728 */           String str2 = recordSet1.getString("id");
/*  729 */           String str3 = "";
/*  730 */           String str4 = Util.null2String(recordSet1.getString("name"));
/*  731 */           String str5 = Util.null2String(recordSet1.getString("name1"));
/*  732 */           String str6 = Util.null2String(recordSet1.getString("name2"));
/*  733 */           str3 = getSelectNameByLanguage(str4, str5, str6, paramInt4).replaceAll("'", "''");
/*      */           
/*  735 */           String str7 = recordSet1.getString("disorder");
/*  736 */           String str8 = Util.null2String(recordSet1.getString("defaultvalue"));
/*  737 */           if (str8.equals("1")) str8 = "y";
/*      */           
/*  739 */           j = recordSet1.getInt("statelev");
/*      */           
/*  741 */           String str9 = recordSet1.getString("cancel");
/*  742 */           String str10 = Util.null2String(recordSet1.getString("pathcategory"));
/*  743 */           String str11 = Util.null2String(recordSet1.getString("maincategory"));
/*  744 */           String str12 = Util.null2String(recordSet1.getString("isAccordToSubCom"));
/*      */ 
/*      */ 
/*      */           
/*  748 */           recordSet3.execute("select id from workflow_SelectItem where isbill = " + paramInt1 + " and fieldid = " + paramInt3 + " and pubid = " + str2);
/*  749 */           if (recordSet3.next()) {
/*  750 */             String str13 = recordSet3.getString(1);
/*  751 */             str = "update workflow_selectitem set selectname = '" + str3 + "',listorder=" + str7 + ",isdefault='" + str8 + "',cancel='" + str9 + "',docpath='" + str10 + "',docCategory='" + str11 + "',isAccordToSubCom='" + str12 + "' where isbill = " + paramInt1 + " and fieldid = " + paramInt3 + " and pubid = " + str2 + " and id = " + str13;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */             
/*  760 */             recordSetTrans.executeSql(str); continue;
/*      */           } 
/*  762 */           i++;
/*  763 */           int k = -1;
/*  764 */           if (map.size() > 0) {
/*  765 */             k = Util.getIntValue(map.get(str2 + ""), -1);
/*      */           }
/*  767 */           if (k < 0) {
/*  768 */             k = i;
/*      */           }
/*  770 */           str = "INSERT INTO workflow_selectitem(fieldid,isbill,selectvalue,selectname,listorder,isdefault,cancel,docpath,docCategory,isAccordToSubCom,pubid) VALUES (" + paramInt3 + ", " + paramInt1 + ", " + k + ", '" + str3 + "', " + str7 + ", '" + str8 + "', '" + str9 + "', '" + str10 + "', '" + str11 + "', '" + str12 + "', " + str2 + ")";
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */           
/*  785 */           recordSetTrans.executeSql(str);
/*      */         } 
/*      */ 
/*      */         
/*  789 */         str = "select selectvalue,pubid from workflow_selectitem where fieldid = " + paramInt2 + " and isbill=" + paramInt1 + " order by pubid ";
/*  790 */         recordSet2.executeSql(str);
/*  791 */         while (recordSet2.next()) {
/*  792 */           String str2 = recordSet2.getString("pubid");
/*  793 */           String str3 = "";
/*  794 */           recordSetTrans.execute("select selectvalue from workflow_selectitem where pubid in (select id from mode_selectitempagedetail where pid=" + str2 + ") and isbill=" + paramInt1 + " and fieldid=" + paramInt3 + " order by listorder");
/*  795 */           while (recordSetTrans.next()) {
/*  796 */             str3 = str3 + "," + recordSetTrans.getString(1);
/*      */           }
/*  798 */           if (!"".equals(str3)) {
/*  799 */             str3 = str3.substring(1);
/*      */           }
/*  801 */           str = "update workflow_selectitem set childitemid='" + str3 + "' where fieldid = " + paramInt2 + " and isbill=" + paramInt1 + " and pubid=" + str2;
/*  802 */           recordSetTrans.executeSql(str);
/*      */         } 
/*  804 */         if (paramInt5 > 0) {
/*  805 */           recordSet1.executeSql("select pubchoiceId from workflow_billfield where id=" + paramInt2);
/*  806 */           if (recordSet1.next()) {
/*  807 */             paramInt5 = recordSet1.getInt(1);
/*      */           }
/*  809 */           str = "update workflow_billfield set pubchoiceId=" + paramInt5 + ",statelev=" + j + " where id=" + paramInt3 + " and billid=" + paramString;
/*  810 */           recordSetTrans.executeSql(str);
/*      */         } 
/*      */       } 
/*      */       
/*  814 */       recordSetTrans.commit();
/*      */       
/*  816 */       if (paramInt6 == 1) syncSelectitemObj(paramInt5, paramInt3); 
/*  817 */     } catch (Exception exception) {
/*  818 */       recordSetTrans.rollback();
/*  819 */       exception.printStackTrace();
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private void setSuperSelectOpBatch(int paramInt1, int paramInt2, int paramInt3, List<Map<String, Integer>> paramList) {
/*  831 */     if (paramList == null || paramList.size() == 0)
/*  832 */       return;  RecordSetTrans recordSetTrans = new RecordSetTrans();
/*  833 */     recordSetTrans.setAutoCommit(false);
/*  834 */     RecordSet recordSet1 = new RecordSet();
/*  835 */     RecordSet recordSet2 = new RecordSet();
/*  836 */     RecordSet recordSet3 = new RecordSet();
/*  837 */     String str1 = "";
/*      */ 
/*      */     
/*  840 */     String str2 = "delete from workflow_SelectItem where isbill=? and fieldid= ? and (pubid not in (select id from mode_selectitempagedetail where mainid = ? and pid > 0) or pubid is null or pubid = '')";
/*  841 */     String str3 = "update workflow_billfield set childfieldid= ? where id=? and billid= ? ";
/*  842 */     String str4 = "update workflow_selectitem set childitemid= ? where fieldid = ? and isbill= ? and pubid= ?";
/*  843 */     String str5 = "update workflow_billfield set pubchoiceId= ?,statelev= ? where id= ? and billid= ?";
/*  844 */     String str6 = "update workflow_selectitem set selectname = ?,listorder=?,isdefault=?,cancel=?,docpath=?,docCategory=?,isAccordToSubCom=? where isbill = ? and fieldid = ? and pubid = ? and id = ?";
/*  845 */     String str7 = "INSERT INTO workflow_selectitem(fieldid,isbill,selectvalue,selectname,listorder,isdefault,cancel,docpath,docCategory,isAccordToSubCom,pubid) VALUES(?,?,?,?,?,?,?,?,?,?,?)";
/*      */     
/*  847 */     ArrayList<ArrayList<Integer>> arrayList1 = new ArrayList();
/*  848 */     ArrayList<ArrayList<Integer>> arrayList2 = new ArrayList();
/*  849 */     ArrayList<ArrayList<String>> arrayList3 = new ArrayList();
/*  850 */     ArrayList<List<Integer>> arrayList = new ArrayList();
/*  851 */     ArrayList<ArrayList<String>> arrayList4 = new ArrayList();
/*  852 */     ArrayList<ArrayList<Integer>> arrayList5 = new ArrayList();
/*      */     
/*  854 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  855 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/*  856 */     HashMap<Object, Object> hashMap3 = new HashMap<>();
/*  857 */     String str8 = "";
/*  858 */     String str9 = "";
/*  859 */     for (byte b = 0; b < paramList.size(); b++) {
/*  860 */       Map map = paramList.get(b);
/*  861 */       int i = ((Integer)map.get("curfieldid")).intValue();
/*  862 */       int j = ((Integer)map.get("superfieldid")).intValue();
/*  863 */       str8 = str8 + "," + i;
/*  864 */       str9 = str9 + "," + j;
/*  865 */       hashMap2.put(Integer.valueOf(i), Integer.valueOf(j));
/*      */     } 
/*  867 */     if (str8.length() > 0) str8 = str8.substring(1); 
/*  868 */     if (str9.length() > 0) str9 = str9.substring(1);
/*      */     
/*  870 */     HashMap<Object, Object> hashMap4 = new HashMap<>();
/*  871 */     recordSet3.execute("select id,pubid,fieldid from workflow_SelectItem where isbill = " + paramInt1 + " and fieldid in (" + str8 + ") ");
/*  872 */     while (recordSet3.next()) {
/*  873 */       String str10 = recordSet3.getString("pubid");
/*  874 */       String str11 = recordSet3.getString("fieldid");
/*  875 */       String str12 = recordSet3.getString("id");
/*  876 */       hashMap4.put(str11 + "_" + str10, str12);
/*      */     } 
/*      */     try {
/*  879 */       for (byte b1 = 0; b1 < paramList.size(); b1++) {
/*  880 */         Map map = paramList.get(b1);
/*  881 */         int i = ((Integer)map.get("curfieldid")).intValue();
/*  882 */         int j = ((Integer)map.get("billid")).intValue();
/*  883 */         int k = ((Integer)map.get("superfieldid")).intValue();
/*      */ 
/*      */         
/*  886 */         ArrayList<Integer> arrayList6 = new ArrayList();
/*  887 */         arrayList6.add(Integer.valueOf(paramInt1));
/*  888 */         arrayList6.add(Integer.valueOf(i));
/*  889 */         arrayList6.add(Integer.valueOf(paramInt3));
/*  890 */         arrayList1.add(arrayList6);
/*      */ 
/*      */         
/*  893 */         ArrayList<Integer> arrayList7 = new ArrayList();
/*  894 */         arrayList7.add(Integer.valueOf(i));
/*  895 */         arrayList7.add(Integer.valueOf(k));
/*  896 */         arrayList7.add(Integer.valueOf(j));
/*  897 */         arrayList2.add(arrayList7);
/*      */         
/*  899 */         int m = -1;
/*  900 */         int n = 1;
/*  901 */         recordSet1.execute("select max(selectvalue) from workflow_SelectItem where isbill=" + paramInt1 + " and fieldid=" + i);
/*  902 */         if (recordSet1.next()) {
/*  903 */           m = Util.getIntValue(Util.null2String(recordSet1.getString(1)), -1);
/*      */         }
/*  905 */         String str = "select pubid from workflow_selectitem where fieldid = " + k + " and isbill=" + paramInt1 + " ";
/*  906 */         str1 = "select id,mainid,name,name1,name2,disorder,defaultvalue,pathcategory,maincategory,pid,statelev,isAccordToSubCom,cancel from mode_selectitempagedetail where pid in (" + str + ") order by id";
/*  907 */         recordSet1.executeSql(str1);
/*      */         
/*  909 */         while (recordSet1.next()) {
/*  910 */           int i1 = recordSet1.getInt("id");
/*  911 */           int i2 = recordSet1.getInt("pid");
/*  912 */           hashMap3.put(Integer.valueOf(i1), Integer.valueOf(i2));
/*      */           
/*  914 */           String str10 = "";
/*  915 */           String str11 = Util.null2String(recordSet1.getString("name"));
/*  916 */           String str12 = Util.null2String(recordSet1.getString("name1"));
/*  917 */           String str13 = Util.null2String(recordSet1.getString("name2"));
/*  918 */           str10 = getSelectNameByLanguage(str11, str12, str13, paramInt2);
/*      */           
/*  920 */           String str14 = recordSet1.getString("disorder");
/*  921 */           String str15 = Util.null2String(recordSet1.getString("defaultvalue"));
/*  922 */           if (str15.equals("1")) str15 = "y";
/*      */           
/*  924 */           n = recordSet1.getInt("statelev");
/*      */           
/*  926 */           String str16 = recordSet1.getString("cancel");
/*  927 */           String str17 = Util.null2String(recordSet1.getString("pathcategory"));
/*  928 */           String str18 = Util.null2String(recordSet1.getString("maincategory"));
/*  929 */           String str19 = Util.null2String(recordSet1.getString("isAccordToSubCom"));
/*      */           
/*  931 */           String str20 = Util.null2String((String)hashMap4.get(i + "_" + i1));
/*  932 */           if (!"".equals(str20)) {
/*  933 */             ArrayList<String> arrayList9 = new ArrayList();
/*  934 */             arrayList9.add(str10);
/*  935 */             arrayList9.add(str14);
/*  936 */             arrayList9.add(str15);
/*  937 */             arrayList9.add(str16);
/*  938 */             arrayList9.add(str17);
/*  939 */             arrayList9.add(str18);
/*  940 */             arrayList9.add(str19);
/*  941 */             arrayList9.add(Integer.valueOf(paramInt1));
/*  942 */             arrayList9.add(Integer.valueOf(i));
/*  943 */             arrayList9.add(Integer.valueOf(i1));
/*  944 */             arrayList9.add(str20);
/*  945 */             arrayList4.add(arrayList9); continue;
/*      */           } 
/*  947 */           m++;
/*  948 */           int i3 = -1;
/*  949 */           if (i3 < 0) {
/*  950 */             i3 = m;
/*      */           }
/*  952 */           ArrayList<Integer> arrayList8 = new ArrayList();
/*  953 */           arrayList8.add(Integer.valueOf(i));
/*  954 */           arrayList8.add(Integer.valueOf(paramInt1));
/*  955 */           arrayList8.add(Integer.valueOf(i3));
/*  956 */           arrayList8.add(str10);
/*  957 */           arrayList8.add(str14);
/*  958 */           arrayList8.add(str15);
/*  959 */           arrayList8.add(str16);
/*  960 */           arrayList8.add(str17);
/*  961 */           arrayList8.add(str18);
/*  962 */           arrayList8.add(str19);
/*  963 */           arrayList8.add(Integer.valueOf(i1));
/*  964 */           arrayList5.add(arrayList8);
/*      */         } 
/*      */ 
/*      */         
/*  968 */         if (paramInt3 > 0) {
/*  969 */           ArrayList<Integer> arrayList8 = new ArrayList();
/*  970 */           arrayList8.add(Integer.valueOf(n));
/*  971 */           arrayList8.add(Integer.valueOf(i));
/*  972 */           arrayList8.add(Integer.valueOf(j));
/*  973 */           hashMap1.put(Integer.valueOf(k), arrayList8);
/*      */         } 
/*      */       } 
/*      */       
/*  977 */       if (arrayList1.size() > 0) recordSetTrans.executeBatchSql(str2, arrayList1); 
/*  978 */       if (arrayList2.size() > 0) recordSetTrans.executeBatchSql(str3, arrayList2); 
/*  979 */       if (arrayList4.size() > 0) recordSetTrans.executeBatchSql(str6, arrayList4); 
/*  980 */       if (arrayList5.size() > 0) recordSetTrans.executeBatchSql(str7, arrayList5);
/*      */ 
/*      */       
/*  983 */       recordSetTrans.execute("select fieldid,selectvalue,pubid from workflow_selectitem where pubid in (select id from mode_selectitempagedetail t where exists (select 1 from workflow_selectitem t1 where t.pid = t1.pubid and t1.fieldid in (" + str9 + ") and t1.isbill=" + paramInt1 + ")) and isbill=" + paramInt1 + " and fieldid in (" + str8 + ") order by listorder");
/*      */       
/*  985 */       HashMap<Object, Object> hashMap = new HashMap<>();
/*  986 */       while (recordSetTrans.next()) {
/*  987 */         int i = recordSetTrans.getInt("fieldid");
/*  988 */         int j = recordSetTrans.getInt("pubid");
/*      */         
/*  990 */         int k = ((Integer)hashMap3.get(Integer.valueOf(j))).intValue();
/*  991 */         int m = ((Integer)hashMap2.get(Integer.valueOf(i))).intValue();
/*      */         
/*  993 */         String str10 = Util.null2String(recordSetTrans.getString("selectvalue"));
/*  994 */         if ("".equals(str10))
/*      */           continue; 
/*  996 */         String str11 = m + "_" + k;
/*  997 */         String str12 = Util.null2String((String)hashMap.get(str11));
/*  998 */         if ("".equals(str12)) {
/*  999 */           hashMap.put(str11, str10); continue;
/*      */         } 
/* 1001 */         hashMap.put(str11, str12 + "," + str10);
/*      */       } 
/*      */       
/* 1004 */       recordSetTrans.executeQuery("select fieldid,selectvalue,pubid from workflow_selectitem where fieldid in (" + str9 + ") and isbill=" + paramInt1 + " order by pubid ", new Object[0]);
/* 1005 */       while (recordSetTrans.next()) {
/* 1006 */         int i = recordSetTrans.getInt("fieldid");
/* 1007 */         String str10 = recordSetTrans.getString("pubid");
/*      */         
/* 1009 */         String str11 = Util.null2String((String)hashMap.get(i + "_" + str10));
/* 1010 */         ArrayList<String> arrayList6 = new ArrayList();
/* 1011 */         arrayList6.add(str11);
/* 1012 */         arrayList6.add(Integer.valueOf(i));
/* 1013 */         arrayList6.add(Integer.valueOf(paramInt1));
/* 1014 */         arrayList6.add(str10);
/* 1015 */         arrayList3.add(arrayList6);
/*      */       } 
/* 1017 */       if (paramInt3 > 0) {
/* 1018 */         recordSetTrans.executeQuery("select id,pubchoiceId from workflow_billfield where id in (" + str9 + ")", new Object[0]);
/* 1019 */         while (recordSetTrans.next()) {
/* 1020 */           int i = recordSetTrans.getInt("id");
/* 1021 */           paramInt3 = recordSetTrans.getInt("pubchoiceId");
/* 1022 */           List<Integer> list = (List)hashMap1.get(Integer.valueOf(i));
/* 1023 */           if (list == null)
/* 1024 */             continue;  list.add(0, Integer.valueOf(paramInt3));
/* 1025 */           arrayList.add(list);
/*      */         } 
/*      */       } 
/* 1028 */       if (arrayList3.size() > 0) recordSetTrans.executeBatchSql(str4, arrayList3); 
/* 1029 */       if (arrayList.size() > 0) recordSetTrans.executeBatchSql(str5, arrayList);
/*      */       
/* 1031 */       recordSetTrans.commit();
/* 1032 */     } catch (Exception exception) {
/* 1033 */       recordSetTrans.rollback();
/* 1034 */       exception.printStackTrace();
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public synchronized void syncPubSelectOp(int paramInt1, int paramInt2) {
/* 1043 */     RecordSet recordSet1 = new RecordSet();
/* 1044 */     RecordSet recordSet2 = new RecordSet();
/* 1045 */     String str = "";
/*      */     
/*      */     try {
/* 1048 */       str = "select id,billid,pubchoiceId,pubchilchoiceId from workflow_billfield where selectItemType = '1' and pubchoiceId = " + paramInt1 + " union all  select id, -1 as billid,pubchoiceId,pubchilchoiceId from ecme_fieldextend where selectItemType = '1' and pubchoiceId = " + paramInt1 + " order by id";
/*      */ 
/*      */       
/* 1051 */       recordSet1.executeSql(str);
/* 1052 */       while (recordSet1.next()) {
/* 1053 */         String str1 = recordSet1.getString("id");
/* 1054 */         String str2 = recordSet1.getString("billid");
/* 1055 */         setSelectOpBypubid(str2, paramInt1, str1, 1, paramInt2, 0);
/*      */       } 
/*      */       
/* 1058 */       str = "select id,billid,pubchoiceId,pubchilchoiceId from workflow_billfield where selectItemType = '2' and pubchoiceId=" + paramInt1 + " order by statelev asc";
/* 1059 */       recordSet2.executeSql(str);
/* 1060 */       ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 1061 */       while (recordSet2.next()) {
/* 1062 */         int i = Util.getIntValue(recordSet2.getString("id"), 0);
/* 1063 */         int j = recordSet1.getInt("billid");
/* 1064 */         int k = Util.getIntValue(recordSet2.getString("pubchilchoiceId"), 0);
/* 1065 */         HashMap<Object, Object> hashMap = new HashMap<>();
/* 1066 */         hashMap.put("billid", Integer.valueOf(j));
/* 1067 */         hashMap.put("superfieldid", Integer.valueOf(k));
/* 1068 */         hashMap.put("curfieldid", Integer.valueOf(i));
/* 1069 */         arrayList.add(hashMap);
/*      */       } 
/* 1071 */       setSuperSelectOpBatch(1, paramInt2, paramInt1, (List)arrayList);
/* 1072 */       syncSelectitemObj(paramInt1);
/* 1073 */     } catch (Exception exception) {
/* 1074 */       exception.printStackTrace();
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void syncSelectitemObj(int paramInt) {
/* 1083 */     RecordSet recordSet = new RecordSet();
/* 1084 */     StringBuffer stringBuffer1 = new StringBuffer();
/* 1085 */     stringBuffer1.append("delete from workflow_selectitemobj where fieldid in (");
/* 1086 */     stringBuffer1.append(" select distinct fieldid from workflow_selectitem item where");
/* 1087 */     stringBuffer1.append(" exists (select 1 from mode_selectitempagedetail d where d.id = item.pubid and d.mainid = " + paramInt + ")");
/* 1088 */     stringBuffer1.append(")");
/* 1089 */     recordSet.executeSql(stringBuffer1.toString());
/*      */     
/* 1091 */     StringBuffer stringBuffer2 = new StringBuffer();
/* 1092 */     stringBuffer2.append("insert into workflow_selectitemobj(fieldid,isbill,selectvalue,objid,objtype,docpath,doccategory)");
/* 1093 */     stringBuffer2.append(" select item.fieldid,1,item.selectvalue,itemobj.objid,itemobj.objtype,itemobj.docpath,itemobj.doccategory");
/* 1094 */     stringBuffer2.append(" from workflow_selectitem item ");
/* 1095 */     stringBuffer2.append(" left join workflow_selectitemobj itemobj");
/* 1096 */     stringBuffer2.append(" on item.pubid = itemobj.selectvalue and itemobj.fieldid = -" + paramInt);
/* 1097 */     stringBuffer2.append(" where item.isaccordtosubcom = '1' and exists (select 1 from mode_selectitempagedetail d where d.id = item.pubid and d.mainid = " + paramInt + ")");
/* 1098 */     recordSet.executeSql(stringBuffer2.toString());
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void syncSelectitemObj(int paramInt1, int paramInt2) {
/* 1108 */     RecordSet recordSet = new RecordSet();
/* 1109 */     recordSet.executeSql("delete from workflow_selectitemobj where fieldid = " + paramInt2);
/*      */     
/* 1111 */     StringBuffer stringBuffer = new StringBuffer();
/* 1112 */     stringBuffer.append("insert into workflow_selectitemobj(fieldid,isbill,selectvalue,objid,objtype,docpath,doccategory)");
/* 1113 */     stringBuffer.append(" select item.fieldid,1,item.selectvalue,itemobj.objid,itemobj.objtype,itemobj.docpath,itemobj.doccategory");
/* 1114 */     stringBuffer.append(" from workflow_selectitem item ");
/* 1115 */     stringBuffer.append(" left join workflow_selectitemobj itemobj");
/* 1116 */     stringBuffer.append(" on item.pubid = itemobj.selectvalue and itemobj.fieldid = -" + paramInt1);
/* 1117 */     stringBuffer.append(" where item.isaccordtosubcom = '1' and item.fieldid = " + paramInt2);
/* 1118 */     recordSet.executeSql(stringBuffer.toString());
/*      */   }
/*      */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/selectItem/SelectItemManager.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */