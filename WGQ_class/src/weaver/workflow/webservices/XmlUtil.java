/*     */ package weaver.workflow.webservices;
/*     */ 
/*     */ import com.thoughtworks.xstream.XStream;
/*     */ import com.thoughtworks.xstream.mapper.Mapper;
/*     */ import com.thoughtworks.xstream.security.NoTypePermission;
/*     */ import java.io.BufferedReader;
/*     */ import java.io.FileInputStream;
/*     */ import java.io.IOException;
/*     */ import java.io.InputStreamReader;
/*     */ import java.lang.reflect.Field;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.HashSet;
/*     */ import java.util.List;
/*     */ import java.util.Locale;
/*     */ import weaver.filter.XssUtil;
/*     */ import weaver.general.Util;
/*     */ 
/*     */ public class XmlUtil
/*     */ {
/*  21 */   private static XStream xs = null;
/*     */   
/*  23 */   private static XmlUtil xmlUtil = null;
/*     */   
/*     */   private static boolean isE9 = false;
/*     */   
/*     */   static {
/*  28 */     XssUtil xssUtil = new XssUtil();
/*  29 */     String version = Util.null2String(xssUtil.getEcVersion());
/*  30 */     if (version.equalsIgnoreCase("E9") || version.equalsIgnoreCase("E10"))
/*     */     {
/*  32 */       isE9 = true;
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   private XmlUtil() {
/*     */     try {
/*  40 */       List<String> list = new ArrayList();
/*  41 */       list.add("weaver.workflow.webservices.WorkflowRequestInfo");
/*  42 */       if (isE9) {
/*  43 */         list.add("weaver.workflow.webservices.WorkflowRequestTableRecord");
/*  44 */         list.add("weaver.workflow.webservices.WorkflowRequestTableField");
/*  45 */         list.add("weaver.workflow.webservices.WorkflowDetailTableInfo");
/*     */       } 
/*     */ 
/*     */       
/*  49 */       xs = alias(list);
/*  50 */     } catch (ClassNotFoundException e) {
/*  51 */       e.printStackTrace();
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static synchronized XmlUtil getInstance() {
/*  61 */     if (null == xmlUtil) {
/*  62 */       xmlUtil = new XmlUtil();
/*     */     }
/*     */     
/*  65 */     return xmlUtil;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public XStream alias(List<String> list) throws ClassNotFoundException {
/*  77 */     XStream xs = new XStream();
/*  78 */     for (int i = 0; i < list.size(); i++) {
/*     */       
/*     */       try {
/*  81 */         Object obj = Class.forName(list.get(i)).newInstance();
/*  82 */         Class<?> zz = obj.getClass();
/*  83 */         aliasAtt(xs, zz);
/*  84 */       } catch (InstantiationException e) {
/*  85 */         e.printStackTrace();
/*  86 */       } catch (IllegalAccessException e) {
/*  87 */         e.printStackTrace();
/*     */       } 
/*     */     } 
/*     */     try {
/*  91 */       XStream.setupDefaultSecurity(xs);
/*  92 */       xs.addPermission(NoTypePermission.NONE);
/*  93 */     } catch (Exception e) {
/*  94 */       e.printStackTrace();
/*     */     } 
/*  96 */     xs.allowTypesByRegExp(new String[] { "^weaver\\..*$", "^com.thoughtworks.xstream\\..*$" });
/*  97 */     xs.allowTypes(new Class[] { String[].class, String.class, Integer.class, Mapper.class, HashMap.class, ArrayList.class, HashSet.class, Mapper.Null.class });
/*  98 */     return xs;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void aliasAtt(XStream xs, Class z) {
/* 109 */     if (null == z) {
/*     */       return;
/*     */     }
/*     */ 
/*     */     
/* 114 */     xs.alias(z.getSimpleName(), z);
/* 115 */     Field[] field = z.getDeclaredFields();
/*     */     
/* 117 */     for (int i = 0; i < field.length; i++) {
/* 118 */       xs.aliasField(field[i].getName(), z, field[i].getName());
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Object xmlToObject(String str) {
/* 128 */     if (denyPermission(str)) {
/* 129 */       return null;
/*     */     }
/* 131 */     return xs.fromXML(str);
/*     */   }
/*     */   
/*     */   private boolean denyPermission(String str) {
/* 135 */     if (str == null || str.equals("")) return false; 
/* 136 */     str = str.toLowerCase(Locale.US);
/* 137 */     String[] dnsLogs = { "$$BCEL$$", ".vuleye.pw", ".burpcollaborator.", ".ceye.io", ".exeye.io", ".eu.org", ".vcap.me", ".xip.name", ".xip.io", ".sslip.io", ".nip.io", ".burpcollaborator.net", ".tu4.org", ".2xss.cc", ".bxss.me", ".godns.vip", ".dnslog.cn", ".xn--9tr.com", ".pipedream.net" };
/* 138 */     for (String dnsLog : dnsLogs) {
/* 139 */       if (str.contains(dnsLog)) {
/* 140 */         System.out.println("forbidden url for str:" + str);
/* 141 */         return true;
/*     */       } 
/*     */     } 
/* 144 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String objToXml(Object obj) {
/* 153 */     return xs.toXML(obj);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static List readFile(String filePath) {
/* 169 */     List<String> list = new ArrayList();
/*     */     
/* 171 */     FileInputStream fs = null;
/* 172 */     InputStreamReader isr = null;
/* 173 */     BufferedReader br = null;
/*     */     
/*     */     try {
/* 176 */       fs = new FileInputStream(filePath);
/*     */       
/* 178 */       isr = new InputStreamReader(fs);
/*     */       
/* 180 */       br = new BufferedReader(isr);
/*     */       
/* 182 */       String data = "";
/* 183 */       while ((data = br.readLine()) != null) {
/* 184 */         list.add(data.trim());
/*     */       }
/* 186 */     } catch (IOException e) {
/* 187 */       e.printStackTrace();
/*     */     } finally {
/*     */       try {
/* 190 */         if (br != null)
/* 191 */           br.close(); 
/* 192 */         if (isr != null) {
/* 193 */           isr.close();
/*     */         }
/* 195 */         if (fs != null) {
/* 196 */           fs.close();
/*     */         }
/* 198 */       } catch (IOException e) {
/* 199 */         e.printStackTrace();
/*     */       } 
/*     */     } 
/*     */     
/* 203 */     return list;
/*     */   }
/*     */ 
/*     */   
/*     */   public WorkflowRequestInfo getWorkflowRequestInfo() {
/* 208 */     WorkflowRequestTableField[] wrti = new WorkflowRequestTableField[16];
/* 209 */     wrti[0] = new WorkflowRequestTableField();
/* 210 */     wrti[0].setFieldName("a1");
/* 211 */     wrti[0].setFieldValue("1111");
/* 212 */     wrti[0].setView(true);
/* 213 */     wrti[0].setEdit(true);
/*     */     
/* 215 */     wrti[1] = new WorkflowRequestTableField();
/* 216 */     wrti[1].setFieldName("a2");
/* 217 */     wrti[1].setFieldValue("2222.22");
/* 218 */     wrti[1].setView(true);
/* 219 */     wrti[1].setEdit(true);
/*     */     
/* 221 */     wrti[2] = new WorkflowRequestTableField();
/* 222 */     wrti[2].setFieldName("a3");
/* 223 */     wrti[2].setFieldValue("2013-05-15");
/* 224 */     wrti[2].setView(true);
/* 225 */     wrti[2].setEdit(true);
/*     */     
/* 227 */     wrti[3] = new WorkflowRequestTableField();
/* 228 */     wrti[3].setFieldName("a4");
/* 229 */     wrti[3].setFieldValue("82");
/* 230 */     wrti[3].setView(true);
/* 231 */     wrti[3].setEdit(true);
/*     */     
/* 233 */     wrti[4] = new WorkflowRequestTableField();
/* 234 */     wrti[4].setFieldName("a5");
/* 235 */     wrti[4].setFieldValue("82,81");
/* 236 */     wrti[4].setView(true);
/* 237 */     wrti[4].setEdit(true);
/*     */     
/* 239 */     wrti[5] = new WorkflowRequestTableField();
/* 240 */     wrti[5].setFieldName("a6");
/* 241 */     wrti[5].setFieldValue("3");
/* 242 */     wrti[5].setView(true);
/* 243 */     wrti[5].setEdit(true);
/*     */     
/* 245 */     wrti[6] = new WorkflowRequestTableField();
/* 246 */     wrti[6].setFieldName("a7");
/* 247 */     wrti[6].setFieldValue("3,1");
/* 248 */     wrti[6].setView(true);
/* 249 */     wrti[6].setEdit(true);
/*     */     
/* 251 */     wrti[7] = new WorkflowRequestTableField();
/* 252 */     wrti[7].setFieldName("a8");
/* 253 */     wrti[7].setFieldValue("3");
/* 254 */     wrti[7].setView(true);
/* 255 */     wrti[7].setEdit(true);
/*     */     
/* 257 */     wrti[8] = new WorkflowRequestTableField();
/* 258 */     wrti[8].setFieldName("a9");
/* 259 */     wrti[8].setFieldValue("3,2");
/* 260 */     wrti[8].setView(true);
/* 261 */     wrti[8].setEdit(true);
/*     */     
/* 263 */     wrti[9] = new WorkflowRequestTableField();
/* 264 */     wrti[9].setFieldName("a10");
/* 265 */     wrti[9].setFieldValue("79");
/* 266 */     wrti[9].setView(true);
/* 267 */     wrti[9].setEdit(true);
/*     */     
/* 269 */     wrti[10] = new WorkflowRequestTableField();
/* 270 */     wrti[10].setFieldName("a11");
/* 271 */     wrti[10].setFieldValue("79,81");
/* 272 */     wrti[10].setView(true);
/* 273 */     wrti[10].setEdit(true);
/*     */     
/* 275 */     wrti[11] = new WorkflowRequestTableField();
/* 276 */     wrti[11].setFieldName("a12");
/* 277 */     wrti[11].setFieldType("http:附件网址.txt ,http:附件EntityService使用说明.docx ");
/* 278 */     wrti[11].setFieldValue("http://192.168.4.204:86/testfile/网址.txt,http://192.168.4.204:86/testfile/EntityService使用说明.docx");
/* 279 */     wrti[11].setView(true);
/* 280 */     wrti[11].setEdit(true);
/*     */     
/* 282 */     wrti[12] = new WorkflowRequestTableField();
/* 283 */     wrti[12].setFieldName("a13");
/* 284 */     wrti[12].setFieldType("http:附件2_wev8.jpg ,http:附件3_wev8.jpg ");
/* 285 */     wrti[12].setFieldValue("http://192.168.4.204:86/testfile/2.jpg,http://192.168.4.204:86/testfile/3_wev8.jpg");
/* 286 */     wrti[12].setView(true);
/* 287 */     wrti[12].setEdit(true);
/*     */     
/* 289 */     wrti[13] = new WorkflowRequestTableField();
/* 290 */     wrti[13].setFieldName("a14");
/* 291 */     wrti[13].setFieldValue("0");
/* 292 */     wrti[13].setView(true);
/* 293 */     wrti[13].setEdit(true);
/*     */     
/* 295 */     wrti[14] = new WorkflowRequestTableField();
/* 296 */     wrti[14].setFieldName("a15");
/* 297 */     wrti[14].setFieldValue("1");
/* 298 */     wrti[14].setView(true);
/* 299 */     wrti[14].setEdit(true);
/*     */     
/* 301 */     wrti[15] = new WorkflowRequestTableField();
/* 302 */     wrti[15].setFieldName("a16");
/* 303 */     wrti[15].setFieldValue("1111");
/* 304 */     wrti[15].setView(true);
/* 305 */     wrti[15].setEdit(true);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 311 */     WorkflowRequestTableRecord[] wrtri = new WorkflowRequestTableRecord[1];
/* 312 */     wrtri[0] = new WorkflowRequestTableRecord();
/* 313 */     wrtri[0].setWorkflowRequestTableFields(wrti);
/*     */     
/* 315 */     WorkflowMainTableInfo wmi = new WorkflowMainTableInfo();
/* 316 */     wmi.setRequestRecords(wrtri);
/*     */ 
/*     */ 
/*     */     
/* 320 */     WorkflowDetailTableInfo[] wdti = new WorkflowDetailTableInfo[2];
/*     */     
/* 322 */     wrtri = new WorkflowRequestTableRecord[2];
/*     */     
/* 324 */     wrti = new WorkflowRequestTableField[10];
/* 325 */     wrti[0] = new WorkflowRequestTableField();
/* 326 */     wrti[0].setFieldName("b1");
/* 327 */     wrti[0].setFieldValue("bbbb1");
/* 328 */     wrti[0].setView(true);
/* 329 */     wrti[0].setEdit(true);
/*     */     
/* 331 */     wrti[1] = new WorkflowRequestTableField();
/* 332 */     wrti[1].setFieldName("b2");
/* 333 */     wrti[1].setFieldValue("2222.00");
/* 334 */     wrti[1].setView(true);
/* 335 */     wrti[1].setEdit(true);
/*     */     
/* 337 */     wrti[2] = new WorkflowRequestTableField();
/* 338 */     wrti[2].setFieldName("b3");
/* 339 */     wrti[2].setFieldValue("82");
/* 340 */     wrti[2].setView(true);
/* 341 */     wrti[2].setEdit(true);
/*     */     
/* 343 */     wrti[3] = new WorkflowRequestTableField();
/* 344 */     wrti[3].setFieldName("b4");
/* 345 */     wrti[3].setFieldValue("82,34");
/* 346 */     wrti[3].setView(true);
/* 347 */     wrti[3].setEdit(true);
/*     */     
/* 349 */     wrti[4] = new WorkflowRequestTableField();
/* 350 */     wrti[4].setFieldName("b5");
/* 351 */     wrti[4].setFieldValue("3");
/* 352 */     wrti[4].setView(true);
/* 353 */     wrti[4].setEdit(true);
/*     */     
/* 355 */     wrti[5] = new WorkflowRequestTableField();
/* 356 */     wrti[5].setFieldName("b6");
/* 357 */     wrti[5].setFieldValue("82,81");
/* 358 */     wrti[5].setView(true);
/* 359 */     wrti[5].setEdit(true);
/*     */     
/* 361 */     wrti[6] = new WorkflowRequestTableField();
/* 362 */     wrti[6].setFieldName("b7");
/* 363 */     wrti[6].setFieldValue("0");
/* 364 */     wrti[6].setView(true);
/* 365 */     wrti[6].setEdit(true);
/*     */     
/* 367 */     wrti[7] = new WorkflowRequestTableField();
/* 368 */     wrti[7].setFieldName("b8");
/* 369 */     wrti[7].setFieldValue("1");
/* 370 */     wrti[7].setView(true);
/* 371 */     wrti[7].setEdit(true);
/*     */     
/* 373 */     wrti[8] = new WorkflowRequestTableField();
/* 374 */     wrti[8].setFieldName("b9");
/* 375 */     wrti[8].setFieldValue("bbbb1");
/* 376 */     wrti[8].setView(true);
/* 377 */     wrti[8].setEdit(true);
/*     */     
/* 379 */     wrti[9] = new WorkflowRequestTableField();
/* 380 */     wrti[9].setFieldName("b10");
/* 381 */     wrti[9].setFieldValue("2013-05-15");
/* 382 */     wrti[9].setView(true);
/* 383 */     wrti[9].setEdit(true);
/*     */ 
/*     */     
/* 386 */     wrtri[0] = new WorkflowRequestTableRecord();
/* 387 */     wrtri[0].setWorkflowRequestTableFields(wrti);
/*     */ 
/*     */     
/* 390 */     wrti = new WorkflowRequestTableField[10];
/* 391 */     wrti[0] = new WorkflowRequestTableField();
/* 392 */     wrti[0].setFieldName("b1");
/* 393 */     wrti[0].setFieldValue("bbbb1");
/* 394 */     wrti[0].setView(true);
/* 395 */     wrti[0].setEdit(true);
/*     */     
/* 397 */     wrti[1] = new WorkflowRequestTableField();
/* 398 */     wrti[1].setFieldName("b2");
/* 399 */     wrti[1].setFieldValue("2222.00");
/* 400 */     wrti[1].setView(true);
/* 401 */     wrti[1].setEdit(true);
/*     */     
/* 403 */     wrti[2] = new WorkflowRequestTableField();
/* 404 */     wrti[2].setFieldName("b3");
/* 405 */     wrti[2].setFieldValue("82");
/* 406 */     wrti[2].setView(true);
/* 407 */     wrti[2].setEdit(true);
/*     */     
/* 409 */     wrti[3] = new WorkflowRequestTableField();
/* 410 */     wrti[3].setFieldName("b4");
/* 411 */     wrti[3].setFieldValue("82,34");
/* 412 */     wrti[3].setView(true);
/* 413 */     wrti[3].setEdit(true);
/*     */     
/* 415 */     wrti[4] = new WorkflowRequestTableField();
/* 416 */     wrti[4].setFieldName("b5");
/* 417 */     wrti[4].setFieldValue("3");
/* 418 */     wrti[4].setView(true);
/* 419 */     wrti[4].setEdit(true);
/*     */     
/* 421 */     wrti[5] = new WorkflowRequestTableField();
/* 422 */     wrti[5].setFieldName("b6");
/* 423 */     wrti[5].setFieldValue("82,81");
/* 424 */     wrti[5].setView(true);
/* 425 */     wrti[5].setEdit(true);
/*     */     
/* 427 */     wrti[6] = new WorkflowRequestTableField();
/* 428 */     wrti[6].setFieldName("b7");
/* 429 */     wrti[6].setFieldValue("0");
/* 430 */     wrti[6].setView(true);
/* 431 */     wrti[6].setEdit(true);
/*     */     
/* 433 */     wrti[7] = new WorkflowRequestTableField();
/* 434 */     wrti[7].setFieldName("b8");
/* 435 */     wrti[7].setFieldValue("1");
/* 436 */     wrti[7].setView(true);
/* 437 */     wrti[7].setEdit(true);
/*     */     
/* 439 */     wrti[8] = new WorkflowRequestTableField();
/* 440 */     wrti[8].setFieldName("b9");
/* 441 */     wrti[8].setFieldValue("bbbb1");
/* 442 */     wrti[8].setView(true);
/* 443 */     wrti[8].setEdit(true);
/*     */     
/* 445 */     wrti[9] = new WorkflowRequestTableField();
/* 446 */     wrti[9].setFieldName("b10");
/* 447 */     wrti[9].setFieldValue("2013-05-15");
/* 448 */     wrti[9].setView(true);
/* 449 */     wrti[9].setEdit(true);
/*     */     
/* 451 */     wrtri[1] = new WorkflowRequestTableRecord();
/* 452 */     wrtri[1].setWorkflowRequestTableFields(wrti);
/*     */     
/* 454 */     wdti[0] = new WorkflowDetailTableInfo();
/* 455 */     wdti[0].setWorkflowRequestTableRecords(wrtri);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 462 */     wrtri = new WorkflowRequestTableRecord[2];
/*     */ 
/*     */     
/* 465 */     wrti = new WorkflowRequestTableField[10];
/* 466 */     wrti[0] = new WorkflowRequestTableField();
/* 467 */     wrti[0].setFieldName("c1");
/* 468 */     wrti[0].setFieldValue("bbbb1");
/* 469 */     wrti[0].setView(true);
/* 470 */     wrti[0].setEdit(true);
/*     */     
/* 472 */     wrti[1] = new WorkflowRequestTableField();
/* 473 */     wrti[1].setFieldName("c2");
/* 474 */     wrti[1].setFieldValue("2222.00");
/* 475 */     wrti[1].setView(true);
/* 476 */     wrti[1].setEdit(true);
/*     */     
/* 478 */     wrti[2] = new WorkflowRequestTableField();
/* 479 */     wrti[2].setFieldName("c3");
/* 480 */     wrti[2].setFieldValue("82");
/* 481 */     wrti[2].setView(true);
/* 482 */     wrti[2].setEdit(true);
/*     */     
/* 484 */     wrti[3] = new WorkflowRequestTableField();
/* 485 */     wrti[3].setFieldName("c4");
/* 486 */     wrti[3].setFieldValue("82,34");
/* 487 */     wrti[3].setView(true);
/* 488 */     wrti[3].setEdit(true);
/*     */     
/* 490 */     wrti[4] = new WorkflowRequestTableField();
/* 491 */     wrti[4].setFieldName("c5");
/* 492 */     wrti[4].setFieldValue("3");
/* 493 */     wrti[4].setView(true);
/* 494 */     wrti[4].setEdit(true);
/*     */     
/* 496 */     wrti[5] = new WorkflowRequestTableField();
/* 497 */     wrti[5].setFieldName("c6");
/* 498 */     wrti[5].setFieldValue("82,81");
/* 499 */     wrti[5].setView(true);
/* 500 */     wrti[5].setEdit(true);
/*     */     
/* 502 */     wrti[6] = new WorkflowRequestTableField();
/* 503 */     wrti[6].setFieldName("c7");
/* 504 */     wrti[6].setFieldValue("0");
/* 505 */     wrti[6].setView(true);
/* 506 */     wrti[6].setEdit(true);
/*     */     
/* 508 */     wrti[7] = new WorkflowRequestTableField();
/* 509 */     wrti[7].setFieldName("c8");
/* 510 */     wrti[7].setFieldValue("1");
/* 511 */     wrti[7].setView(true);
/* 512 */     wrti[7].setEdit(true);
/*     */     
/* 514 */     wrti[8] = new WorkflowRequestTableField();
/* 515 */     wrti[8].setFieldName("c9");
/* 516 */     wrti[8].setFieldValue("bbbb1");
/* 517 */     wrti[8].setView(true);
/* 518 */     wrti[8].setEdit(true);
/*     */     
/* 520 */     wrti[9] = new WorkflowRequestTableField();
/* 521 */     wrti[9].setFieldName("c10");
/* 522 */     wrti[9].setFieldValue("2013-05-15");
/* 523 */     wrti[9].setView(true);
/* 524 */     wrti[9].setEdit(true);
/*     */     
/* 526 */     wrtri[0] = new WorkflowRequestTableRecord();
/* 527 */     wrtri[0].setWorkflowRequestTableFields(wrti);
/*     */ 
/*     */     
/* 530 */     wrti = new WorkflowRequestTableField[10];
/* 531 */     wrti[0] = new WorkflowRequestTableField();
/* 532 */     wrti[0].setFieldName("c1");
/* 533 */     wrti[0].setFieldValue("bbbb1");
/* 534 */     wrti[0].setView(true);
/* 535 */     wrti[0].setEdit(true);
/*     */     
/* 537 */     wrti[1] = new WorkflowRequestTableField();
/* 538 */     wrti[1].setFieldName("c2");
/* 539 */     wrti[1].setFieldValue("2222.00");
/* 540 */     wrti[1].setView(true);
/* 541 */     wrti[1].setEdit(true);
/*     */     
/* 543 */     wrti[2] = new WorkflowRequestTableField();
/* 544 */     wrti[2].setFieldName("c3");
/* 545 */     wrti[2].setFieldValue("82");
/* 546 */     wrti[2].setView(true);
/* 547 */     wrti[2].setEdit(true);
/*     */     
/* 549 */     wrti[3] = new WorkflowRequestTableField();
/* 550 */     wrti[3].setFieldName("c4");
/* 551 */     wrti[3].setFieldValue("82,34");
/* 552 */     wrti[3].setView(true);
/* 553 */     wrti[3].setEdit(true);
/*     */     
/* 555 */     wrti[4] = new WorkflowRequestTableField();
/* 556 */     wrti[4].setFieldName("c5");
/* 557 */     wrti[4].setFieldValue("3");
/* 558 */     wrti[4].setView(true);
/* 559 */     wrti[4].setEdit(true);
/*     */     
/* 561 */     wrti[5] = new WorkflowRequestTableField();
/* 562 */     wrti[5].setFieldName("c6");
/* 563 */     wrti[5].setFieldValue("82,81");
/* 564 */     wrti[5].setView(true);
/* 565 */     wrti[5].setEdit(true);
/*     */     
/* 567 */     wrti[6] = new WorkflowRequestTableField();
/* 568 */     wrti[6].setFieldName("c7");
/* 569 */     wrti[6].setFieldValue("0");
/* 570 */     wrti[6].setView(true);
/* 571 */     wrti[6].setEdit(true);
/*     */     
/* 573 */     wrti[7] = new WorkflowRequestTableField();
/* 574 */     wrti[7].setFieldName("c8");
/* 575 */     wrti[7].setFieldValue("1");
/* 576 */     wrti[7].setView(true);
/* 577 */     wrti[7].setEdit(true);
/*     */     
/* 579 */     wrti[8] = new WorkflowRequestTableField();
/* 580 */     wrti[8].setFieldName("c9");
/* 581 */     wrti[8].setFieldValue("bbbb1");
/* 582 */     wrti[8].setView(true);
/* 583 */     wrti[8].setEdit(true);
/*     */     
/* 585 */     wrti[9] = new WorkflowRequestTableField();
/* 586 */     wrti[9].setFieldName("c10");
/* 587 */     wrti[9].setFieldValue("2013-05-15");
/* 588 */     wrti[9].setView(true);
/* 589 */     wrti[9].setEdit(true);
/*     */     
/* 591 */     wrtri[1] = new WorkflowRequestTableRecord();
/* 592 */     wrtri[1].setWorkflowRequestTableFields(wrti);
/*     */ 
/*     */     
/* 595 */     wdti[1] = new WorkflowDetailTableInfo();
/* 596 */     wdti[1].setWorkflowRequestTableRecords(wrtri);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 602 */     WorkflowBaseInfo wbi = new WorkflowBaseInfo();
/* 603 */     wbi.setWorkflowId("61");
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 608 */     WorkflowRequestInfo wri = new WorkflowRequestInfo();
/* 609 */     wri.setCreatorId("82");
/* 610 */     wri.setRequestLevel("2");
/* 611 */     wri.setRequestName("webservices接口测试流程-罗燕-2013-05-15_002");
/* 612 */     wri.setWorkflowMainTableInfo(wmi);
/* 613 */     wri.setWorkflowBaseInfo(wbi);
/* 614 */     wri.setWorkflowDetailTableInfos(wdti);
/*     */     
/* 616 */     return wri;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static void main(String[] args) {
/*     */     try {
/* 626 */       XmlUtil xmlUtil = getInstance();
/*     */       
/* 628 */       WorkflowRequestInfo wri = xmlUtil.getWorkflowRequestInfo();
/*     */ 
/*     */       
/* 631 */       String xml = xmlUtil.objToXml(wri);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 638 */       InputStreamReader reader = new InputStreamReader(new FileInputStream("/Users/<USER>/Downloads/WorkflowRequestInfo.xml"));
/*     */       
/* 640 */       BufferedReader br = new BufferedReader(reader);
/*     */       
/* 642 */       String xml2 = "";
/*     */       
/*     */       String line;
/* 645 */       while ((line = br.readLine()) != null) {
/* 646 */         xml2 = xml2 + line;
/*     */       }
/*     */       
/* 649 */       br.close();
/* 650 */       reader.close();
/*     */       
/* 652 */       WorkflowRequestInfo workflowRequestInfo1 = (WorkflowRequestInfo)xmlUtil.xmlToObject(xml2);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*     */     }
/* 659 */     catch (Exception e) {
/* 660 */       e.printStackTrace();
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/webservices/XmlUtil.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */