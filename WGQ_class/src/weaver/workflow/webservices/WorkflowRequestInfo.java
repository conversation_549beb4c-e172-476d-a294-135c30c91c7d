/*     */ package weaver.workflow.webservices;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class WorkflowRequestInfo
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 4236890406953604169L;
/*     */   private String requestId;
/*     */   private String requestName;
/*     */   private String requestLevel;
/*     */   private String messageType;
/*     */   private WorkflowBaseInfo workflowBaseInfo;
/*     */   private String currentNodeName;
/*     */   private String currentNodeId;
/*     */   private String status;
/*     */   private String creatorId;
/*     */   private String creatorName;
/*     */   private String createTime;
/*     */   private String lastOperatorName;
/*     */   private String lastOperateTime;
/*     */   private String receiveTime;
/*     */   private boolean canView;
/*     */   private boolean canEdit;
/*     */   private boolean mustInputRemark;
/*     */   private boolean needAffirmance;
/*     */   private String submitButtonName;
/*     */   private String subnobackButtonName;
/*     */   private String subbackButtonName;
/*     */   private String rejectButtonName;
/*     */   private String forwardButtonName;
/*     */   private WorkflowMainTableInfo workflowMainTableInfo;
/*     */   private WorkflowDetailTableInfo[] workflowDetailTableInfos;
/*     */   private WorkflowRequestLog[] workflowRequestLogs;
/*     */   private String[] WorkflowHtmlTemplete;
/*     */   private String[] WorkflowHtmlShow;
/*     */   private String[][] workflowPhrases;
/*     */   private String remark;
/*     */   private String isnextflow;
/*     */   private String secLevel;
/*     */   
/*     */   public String getRequestId() {
/* 184 */     return this.requestId;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setRequestId(String paramString) {
/* 191 */     this.requestId = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getRequestName() {
/* 198 */     return this.requestName;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setRequestName(String paramString) {
/* 205 */     this.requestName = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getRequestLevel() {
/* 212 */     return this.requestLevel;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setRequestLevel(String paramString) {
/* 219 */     this.requestLevel = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public WorkflowBaseInfo getWorkflowBaseInfo() {
/* 226 */     return this.workflowBaseInfo;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setWorkflowBaseInfo(WorkflowBaseInfo paramWorkflowBaseInfo) {
/* 233 */     this.workflowBaseInfo = paramWorkflowBaseInfo;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCurrentNodeName() {
/* 240 */     return this.currentNodeName;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setCurrentNodeName(String paramString) {
/* 247 */     this.currentNodeName = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCreatorId() {
/* 254 */     return this.creatorId;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setCreatorId(String paramString) {
/* 261 */     this.creatorId = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCreatorName() {
/* 268 */     return this.creatorName;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setCreatorName(String paramString) {
/* 275 */     this.creatorName = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCreateTime() {
/* 282 */     return this.createTime;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setCreateTime(String paramString) {
/* 289 */     this.createTime = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getLastOperatorName() {
/* 296 */     return this.lastOperatorName;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setLastOperatorName(String paramString) {
/* 303 */     this.lastOperatorName = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getLastOperateTime() {
/* 310 */     return this.lastOperateTime;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setLastOperateTime(String paramString) {
/* 317 */     this.lastOperateTime = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public WorkflowMainTableInfo getWorkflowMainTableInfo() {
/* 324 */     return this.workflowMainTableInfo;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setWorkflowMainTableInfo(WorkflowMainTableInfo paramWorkflowMainTableInfo) {
/* 331 */     this.workflowMainTableInfo = paramWorkflowMainTableInfo;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public WorkflowDetailTableInfo[] getWorkflowDetailTableInfos() {
/* 338 */     return this.workflowDetailTableInfos;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setWorkflowDetailTableInfos(WorkflowDetailTableInfo[] paramArrayOfWorkflowDetailTableInfo) {
/* 345 */     this.workflowDetailTableInfos = paramArrayOfWorkflowDetailTableInfo;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public WorkflowRequestLog[] getWorkflowRequestLogs() {
/* 352 */     return this.workflowRequestLogs;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setWorkflowRequestLogs(WorkflowRequestLog[] paramArrayOfWorkflowRequestLog) {
/* 359 */     this.workflowRequestLogs = paramArrayOfWorkflowRequestLog;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getMessageType() {
/* 366 */     return this.messageType;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setMessageType(String paramString) {
/* 373 */     this.messageType = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean isCanView() {
/* 380 */     return this.canView;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setCanView(boolean paramBoolean) {
/* 387 */     this.canView = paramBoolean;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean isCanEdit() {
/* 394 */     return this.canEdit;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setCanEdit(boolean paramBoolean) {
/* 401 */     this.canEdit = paramBoolean;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getSubmitButtonName() {
/* 408 */     return this.submitButtonName;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setSubmitButtonName(String paramString) {
/* 415 */     this.submitButtonName = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getRejectButtonName() {
/* 422 */     return this.rejectButtonName;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setRejectButtonName(String paramString) {
/* 429 */     this.rejectButtonName = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getForwardButtonName() {
/* 436 */     return this.forwardButtonName;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setForwardButtonName(String paramString) {
/* 443 */     this.forwardButtonName = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCurrentNodeId() {
/* 450 */     return this.currentNodeId;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setCurrentNodeId(String paramString) {
/* 457 */     this.currentNodeId = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getStatus() {
/* 464 */     return this.status;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setStatus(String paramString) {
/* 471 */     this.status = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean isMustInputRemark() {
/* 478 */     return this.mustInputRemark;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setMustInputRemark(boolean paramBoolean) {
/* 485 */     this.mustInputRemark = paramBoolean;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String[] getWorkflowHtmlTemplete() {
/* 492 */     return this.WorkflowHtmlTemplete;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setWorkflowHtmlTemplete(String[] paramArrayOfString) {
/* 499 */     this.WorkflowHtmlTemplete = paramArrayOfString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String[] getWorkflowHtmlShow() {
/* 506 */     return this.WorkflowHtmlShow;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setWorkflowHtmlShow(String[] paramArrayOfString) {
/* 513 */     this.WorkflowHtmlShow = paramArrayOfString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String[][] getWorkflowPhrases() {
/* 520 */     return this.workflowPhrases;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setWorkflowPhrases(String[][] paramArrayOfString) {
/* 527 */     this.workflowPhrases = paramArrayOfString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getReceiveTime() {
/* 534 */     return this.receiveTime;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setReceiveTime(String paramString) {
/* 541 */     this.receiveTime = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getSubnobackButtonName() {
/* 548 */     return this.subnobackButtonName;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setSubnobackButtonName(String paramString) {
/* 555 */     this.subnobackButtonName = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getSubbackButtonName() {
/* 562 */     return this.subbackButtonName;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setSubbackButtonName(String paramString) {
/* 569 */     this.subbackButtonName = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean isNeedAffirmance() {
/* 576 */     return this.needAffirmance;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setNeedAffirmance(boolean paramBoolean) {
/* 583 */     this.needAffirmance = paramBoolean;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getRemark() {
/* 590 */     return this.remark;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setRemark(String paramString) {
/* 597 */     this.remark = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getIsnextflow() {
/* 604 */     return this.isnextflow;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setIsnextflow(String paramString) {
/* 611 */     this.isnextflow = paramString;
/*     */   }
/*     */   
/*     */   public String getSecLevel() {
/* 615 */     return this.secLevel;
/*     */   }
/*     */   
/*     */   public void setSecLevel(String paramString) {
/* 619 */     this.secLevel = paramString;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/webservices/WorkflowRequestInfo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */