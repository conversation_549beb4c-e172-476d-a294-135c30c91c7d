/*    */ package weaver.workflow.webservices;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class WorkflowBaseInfo
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 9179821848679465088L;
/*    */   private String workflowId;
/*    */   private String workflowName;
/*    */   private String workflowTypeId;
/*    */   private String workflowTypeName;
/*    */   
/*    */   public String getWorkflowId() {
/* 36 */     return this.workflowId;
/*    */   }
/*    */   
/*    */   public void setWorkflowId(String paramString) {
/* 40 */     this.workflowId = paramString;
/*    */   }
/*    */   
/*    */   public String getWorkflowName() {
/* 44 */     return this.workflowName;
/*    */   }
/*    */   
/*    */   public void setWorkflowName(String paramString) {
/* 48 */     this.workflowName = paramString;
/*    */   }
/*    */   
/*    */   public String getWorkflowTypeId() {
/* 52 */     return this.workflowTypeId;
/*    */   }
/*    */   
/*    */   public void setWorkflowTypeId(String paramString) {
/* 56 */     this.workflowTypeId = paramString;
/*    */   }
/*    */   
/*    */   public String getWorkflowTypeName() {
/* 60 */     return this.workflowTypeName;
/*    */   }
/*    */   
/*    */   public void setWorkflowTypeName(String paramString) {
/* 64 */     this.workflowTypeName = paramString;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/webservices/WorkflowBaseInfo.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */