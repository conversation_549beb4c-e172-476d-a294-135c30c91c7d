/*     */ package weaver.workflow.webservices;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class WorkflowRequestLog
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 5675535868385045786L;
/*     */   private String id;
/*     */   private String nodeId;
/*     */   private String nodeName;
/*     */   private String remark;
/*     */   private String remarkSign;
/*     */   private String operatorId;
/*     */   private String operatorName;
/*     */   private String operatorSign;
/*     */   private String operateDate;
/*     */   private String operateTime;
/*     */   private String operateType;
/*     */   private String receivedPersons;
/*     */   private String annexDocHtmls;
/*     */   private String operatorDept;
/*     */   private String signDocHtmls;
/*     */   private String signWorkFlowHtmls;
/*     */   private String agentor;
/*     */   private String agentorDept;
/*     */   
/*     */   public String getNodeName() {
/* 103 */     return this.nodeName;
/*     */   }
/*     */   
/*     */   public void setNodeName(String paramString) {
/* 107 */     this.nodeName = paramString;
/*     */   }
/*     */   
/*     */   public String getRemark() {
/* 111 */     return this.remark;
/*     */   }
/*     */   
/*     */   public void setRemark(String paramString) {
/* 115 */     this.remark = paramString;
/*     */   }
/*     */   
/*     */   public String getOperatorName() {
/* 119 */     return this.operatorName;
/*     */   }
/*     */   
/*     */   public void setOperatorName(String paramString) {
/* 123 */     this.operatorName = paramString;
/*     */   }
/*     */   
/*     */   public String getOperateDate() {
/* 127 */     return this.operateDate;
/*     */   }
/*     */   
/*     */   public void setOperateDate(String paramString) {
/* 131 */     this.operateDate = paramString;
/*     */   }
/*     */   
/*     */   public String getOperateTime() {
/* 135 */     return this.operateTime;
/*     */   }
/*     */   
/*     */   public void setOperateTime(String paramString) {
/* 139 */     this.operateTime = paramString;
/*     */   }
/*     */   
/*     */   public String getOperateType() {
/* 143 */     return this.operateType;
/*     */   }
/*     */   
/*     */   public void setOperateType(String paramString) {
/* 147 */     this.operateType = paramString;
/*     */   }
/*     */   
/*     */   public String getReceivedPersons() {
/* 151 */     return this.receivedPersons;
/*     */   }
/*     */   
/*     */   public void setReceivedPersons(String paramString) {
/* 155 */     this.receivedPersons = paramString;
/*     */   }
/*     */   
/*     */   public String getOperatorDept() {
/* 159 */     return this.operatorDept;
/*     */   }
/*     */   
/*     */   public void setOperatorDept(String paramString) {
/* 163 */     this.operatorDept = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getAnnexDocHtmls() {
/* 183 */     return this.annexDocHtmls;
/*     */   }
/*     */   
/*     */   public void setAnnexDocHtmls(String paramString) {
/* 187 */     this.annexDocHtmls = paramString;
/*     */   }
/*     */   
/*     */   public String getId() {
/* 191 */     return this.id;
/*     */   }
/*     */   
/*     */   public void setId(String paramString) {
/* 195 */     this.id = paramString;
/*     */   }
/*     */   
/*     */   public String getNodeId() {
/* 199 */     return this.nodeId;
/*     */   }
/*     */   
/*     */   public void setNodeId(String paramString) {
/* 203 */     this.nodeId = paramString;
/*     */   }
/*     */   
/*     */   public String getRemarkSign() {
/* 207 */     return this.remarkSign;
/*     */   }
/*     */   
/*     */   public void setRemarkSign(String paramString) {
/* 211 */     this.remarkSign = paramString;
/*     */   }
/*     */   
/*     */   public String getOperatorSign() {
/* 215 */     return this.operatorSign;
/*     */   }
/*     */   
/*     */   public void setOperatorSign(String paramString) {
/* 219 */     this.operatorSign = paramString;
/*     */   }
/*     */   
/*     */   public String getOperatorId() {
/* 223 */     return this.operatorId;
/*     */   }
/*     */   
/*     */   public void setOperatorId(String paramString) {
/* 227 */     this.operatorId = paramString;
/*     */   }
/*     */   
/*     */   public String getSignDocHtmls() {
/* 231 */     return this.signDocHtmls;
/*     */   }
/*     */   
/*     */   public void setSignDocHtmls(String paramString) {
/* 235 */     this.signDocHtmls = paramString;
/*     */   }
/*     */   
/*     */   public String getSignWorkFlowHtmls() {
/* 239 */     return this.signWorkFlowHtmls;
/*     */   }
/*     */   
/*     */   public void setSignWorkFlowHtmls(String paramString) {
/* 243 */     this.signWorkFlowHtmls = paramString;
/*     */   }
/*     */   
/*     */   public String getAgentor() {
/* 247 */     return this.agentor;
/*     */   }
/*     */   
/*     */   public void setAgentor(String paramString) {
/* 251 */     this.agentor = paramString;
/*     */   }
/*     */   
/*     */   public String getAgentorDept() {
/* 255 */     return this.agentorDept;
/*     */   }
/*     */   
/*     */   public void setAgentorDept(String paramString) {
/* 259 */     this.agentorDept = paramString;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/webservices/WorkflowRequestLog.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */