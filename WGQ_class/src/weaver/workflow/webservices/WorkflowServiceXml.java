package weaver.workflow.webservices;

import javax.jws.WebService;

@WebService
public interface WorkflowServiceXml {
  int getToDoWorkflowRequestCount(int paramInt, String[] paramArrayOfString);
  
  int getCCWorkflowRequestCount(int paramInt, String[] paramArrayOfString);
  
  int getHendledWorkflowRequestCount(int paramInt, String[] paramArrayOfString);
  
  int getProcessedWorkflowRequestCount(int paramInt, String[] paramArrayOfString);
  
  int getMyWorkflowRequestCount(int paramInt, String[] paramArrayOfString);
  
  int getAllWorkflowRequestCount(int paramInt, String[] paramArrayOfString);
  
  WorkflowRequestLog[] getWorkflowRequestLogs(String paramString1, String paramString2, int paramInt1, int paramInt2, int paramInt3) throws Exception;
  
  String forwardWorkflowRequest(int paramInt1, String paramString1, String paramString2, int paramInt2, String paramString3);
  
  int getCreateWorkflowCount(int paramInt1, int paramInt2, String[] paramArrayOfString);
  
  int getCreateWorkflowTypeCount(int paramInt, String[] paramArrayOfString);
  
  String getLeaveDays(String paramString1, String paramString2, String paramString3, String paramString4, String paramString5);
  
  String[] getWorkflowNewFlag(String[] paramArrayOfString, String paramString);
  
  boolean deleteRequest(int paramInt1, int paramInt2);
  
  void writeWorkflowReadFlag(String paramString1, String paramString2);
  
  String submitWorkflowRequest(String paramString1, int paramInt1, int paramInt2, String paramString2, String paramString3);
  
  String doCreateWorkflowRequest(String paramString, int paramInt);
  
  String getCreateWorkflowRequestInfo(int paramInt1, int paramInt2);
  
  String getWorkflowRequest4split(int paramInt1, int paramInt2, int paramInt3, int paramInt4);
  
  String[] getCreateWorkflowList(int paramInt1, int paramInt2, int paramInt3, int paramInt4, int paramInt5, String[] paramArrayOfString);
  
  String[] getCreateWorkflowTypeList(int paramInt1, int paramInt2, int paramInt3, int paramInt4, String[] paramArrayOfString);
  
  String[] getCCWorkflowRequestList(int paramInt1, int paramInt2, int paramInt3, int paramInt4, String[] paramArrayOfString);
  
  String[] getProcessedWorkflowRequestList(int paramInt1, int paramInt2, int paramInt3, int paramInt4, String[] paramArrayOfString);
  
  String[] getMyWorkflowRequestList(int paramInt1, int paramInt2, int paramInt3, int paramInt4, String[] paramArrayOfString);
  
  String[] getHendledWorkflowRequestList(int paramInt1, int paramInt2, int paramInt3, int paramInt4, String[] paramArrayOfString);
  
  String[] getToDoWorkflowRequestList(int paramInt1, int paramInt2, int paramInt3, int paramInt4, String[] paramArrayOfString);
  
  String getWorkflowRequest(int paramInt1, int paramInt2, int paramInt3);
  
  String[] getAllWorkflowRequestList(int paramInt1, int paramInt2, int paramInt3, int paramInt4, String[] paramArrayOfString);
  
  String forward2WorkflowRequest(int paramInt1, String paramString1, String paramString2, int paramInt2, String paramString3);
  
  String givingOpinions(int paramInt1, int paramInt2, String paramString);
  
  String doForceOver(int paramInt1, int paramInt2);
  
  String getUserId(String paramString1, String paramString2);
}


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/webservices/WorkflowServiceXml.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */