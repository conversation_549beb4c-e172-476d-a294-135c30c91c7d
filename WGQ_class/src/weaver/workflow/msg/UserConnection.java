/*    */ package weaver.workflow.msg;
/*    */ 
/*    */ import java.util.Vector;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class UserConnection
/*    */ {
/*    */   protected int userid;
/*    */   protected int usertype;
/*    */   protected int languageid;
/*    */   protected Vector conns;
/*    */   protected Message nextMsg;
/*    */   
/*    */   public UserConnection(int paramInt1, String paramString, int paramInt2) {
/* 33 */     this.userid = paramInt1;
/* 34 */     this.usertype = getUserType(paramString);
/* 35 */     this.languageid = paramInt2;
/* 36 */     this.conns = new Vector();
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public static int getUserType(String paramString) {
/* 45 */     boolean bool = false;
/* 46 */     if (paramString.equals("2")) bool = true; 
/* 47 */     return bool;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/msg/UserConnection.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */