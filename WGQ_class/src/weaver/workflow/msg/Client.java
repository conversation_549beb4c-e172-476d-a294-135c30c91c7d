/*     */ package weaver.workflow.msg;
/*     */ 
/*     */ import java.applet.Applet;
/*     */ import java.io.DataOutputStream;
/*     */ import java.net.Socket;
/*     */ import java.util.ArrayList;
/*     */ import java.util.StringTokenizer;
/*     */ import netscape.javascript.JSObject;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class Client
/*     */   extends Applet
/*     */ {
/*     */   protected ClientListener cl;
/*     */   protected JSObject win;
/*     */   protected String serverUrl;
/*     */   protected int serverPort;
/*     */   protected String id;
/*     */   protected String type;
/*     */   protected String language;
/*     */   protected Socket socket;
/*     */   
/*     */   public void init() {
/*     */     try {
/*  43 */       this.cl = new ClientListener();
/*     */       
/*  45 */       this.serverUrl = getParameter("serverUrl");
/*     */       
/*  47 */       if (this.serverUrl.indexOf(":") != -1) {
/*  48 */         this.serverUrl = this.serverUrl.substring(0, this.serverUrl.indexOf(":"));
/*     */       }
/*  50 */       this.serverPort = Integer.parseInt(getParameter("serverPort"));
/*  51 */       this.id = getParameter("userId");
/*  52 */       this.type = getParameter("logintype");
/*  53 */       this.language = getParameter("languageid");
/*  54 */       this.cl.client = this;
/*     */       
/*  56 */       this.win = JSObject.getWindow(this);
/*  57 */     } catch (Exception exception) {
/*  58 */       exception.printStackTrace();
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   public void start() {
/*     */     try {
/*     */       try {
/*  66 */         this.socket.close();
/*  67 */       } catch (Exception exception) {}
/*     */       
/*  69 */       this.socket = new Socket(this.serverUrl, this.serverPort);
/*  70 */       DataOutputStream dataOutputStream = new DataOutputStream(this.socket.getOutputStream());
/*  71 */       dataOutputStream.writeUTF(this.id);
/*  72 */       dataOutputStream.writeUTF(this.type);
/*  73 */       dataOutputStream.writeUTF(this.language);
/*  74 */       this.cl.start();
/*  75 */     } catch (Exception exception) {
/*  76 */       exception.printStackTrace();
/*     */       try {
/*  78 */         this.socket.close();
/*  79 */       } catch (Exception exception1) {}
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public void stop() {
/*     */     try {
/*  87 */       this.cl.stopit();
/*  88 */       this.socket.close();
/*  89 */       System.out.println("socket closed");
/*  90 */     } catch (Exception exception) {}
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void processMessage(Message paramMessage) {
/*  99 */     String str = "";
/* 100 */     String[] arrayOfString = new String[2];
/* 101 */     if (!paramMessage.hasnewwf.equals("") && 
/* 102 */       paramMessage.isrtxmessage == 0 && paramMessage.shownewwf == 1) {
/*     */       
/* 104 */       arrayOfString = TokenizerString2(paramMessage.newrequestnames, "&", false);
/*     */       
/* 106 */       for (byte b = 0; b < arrayOfString.length; b++) {
/* 107 */         str = str + "<tr><td width='13'><img src='/images/PopupMsgDot_wev8.gif'></td><td>" + arrayOfString[b] + "</td></tr>";
/*     */       }
/* 109 */       this.win.eval("setPoppupHeight(\"" + arrayOfString.length + "\")");
/* 110 */       this.win.eval("handlenewrequest(\"" + str + "\")");
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   public static String[] TokenizerString2(String paramString1, String paramString2, boolean paramBoolean) {
/* 116 */     ArrayList<String> arrayList = TokenizerString(paramString1, paramString2, paramBoolean);
/* 117 */     int i = arrayList.size();
/* 118 */     String[] arrayOfString = new String[i];
/* 119 */     for (byte b = 0; b < i; b++) {
/* 120 */       arrayOfString[b] = arrayList.get(b);
/*     */     }
/* 122 */     return arrayOfString;
/*     */   }
/*     */   public static ArrayList TokenizerString(String paramString1, String paramString2, boolean paramBoolean) {
/* 125 */     paramString1 = null2String(paramString1);
/* 126 */     paramString2 = null2String(paramString2);
/* 127 */     ArrayList<String> arrayList = new ArrayList();
/* 128 */     StringTokenizer stringTokenizer = new StringTokenizer(paramString1, paramString2, paramBoolean);
/* 129 */     while (stringTokenizer.hasMoreTokens()) {
/* 130 */       arrayList.add(stringTokenizer.nextToken());
/*     */     }
/* 132 */     return arrayList;
/*     */   }
/*     */   public static String null2String(String paramString) {
/* 135 */     return (paramString == null) ? "" : paramString;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/msg/Client.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */