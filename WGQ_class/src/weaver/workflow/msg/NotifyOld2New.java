/*    */ package weaver.workflow.msg;
/*    */ 
/*    */ import weaver.rtx.RTXException;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class NotifyOld2New
/*    */ {
/* 22 */   private NotifyManager notify = null;
/*    */   
/*    */   public NotifyOld2New() {
/* 25 */     this.notify = new NotifyManager();
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public boolean notify(int paramInt1, Message paramMessage, int paramInt2) throws RTXException {
/* 36 */     boolean bool = true;
/* 37 */     String str = "";
/*    */     
/* 39 */     if (paramMessage.showcpt == 1) {
/* 40 */       str = SystemEnv.getHtmlLabelName(30044, paramInt2) + " " + paramMessage.cptname + SystemEnv.getHtmlLabelName(84442, paramInt2);
/*    */     }
/*    */     
/* 43 */     if (!paramMessage.hasnewwf.equals("") && 
/* 44 */       paramMessage.shownewwf == 1) {
/* 45 */       str = paramMessage.newrequestnames;
/* 46 */       str = SystemEnv.getHtmlLabelName(15031, paramInt2) + ":" + str;
/* 47 */       if (!this.notify.notify(paramInt1, str)) {
/* 48 */         bool = false;
/*    */       }
/*    */     } 
/*    */     
/* 52 */     if (!paramMessage.hasendwf.equals("")) {
/* 53 */       if (paramMessage.showendwf == 1) {
/* 54 */         str = paramMessage.endrequestnames;
/* 55 */         str = SystemEnv.getHtmlLabelName(15032, paramInt2) + ":" + str;
/* 56 */         if (!this.notify.notify(paramInt1, str)) {
/* 57 */           bool = false;
/*    */         }
/*    */       } 
/* 60 */       if (paramMessage.hassysremindwf == 1) {
/* 61 */         str = paramMessage.sysremindnames;
/* 62 */         str = SystemEnv.getHtmlLabelName(15036, paramInt2) + ":" + str;
/* 63 */         if (!this.notify.notify(paramInt1, str)) {
/* 64 */           bool = false;
/*    */         }
/*    */       } 
/*    */     } 
/* 68 */     return bool;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/msg/NotifyOld2New.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */