/*     */ package weaver.workflow.msg;
/*     */ 
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import com.cloudstore.dev.api.bean.MessageBean;
/*     */ import com.cloudstore.dev.api.bean.MessageType;
/*     */ import com.cloudstore.dev.api.util.Util_Message;
/*     */ import com.engine.common.util.ServiceUtil;
/*     */ import com.engine.msgcenter.biz.ConfigManager;
/*     */ import com.engine.msgcenter.biz.WeaMessageConfig;
/*     */ import com.engine.msgcenter.biz.WeaMessageConfigDetail;
/*     */ import com.engine.msgcenter.biz.WeaMessageTypeConfig;
/*     */ import com.engine.msgcenter.biz.WeaMessageUserConfig;
/*     */ import com.engine.workflow.biz.requestForm.RequestFlowRemindBiz;
/*     */ import com.engine.workflow.service.MsgPushService;
/*     */ import com.engine.workflow.service.impl.MsgPushServiceImpl;
/*     */ import com.google.common.base.Strings;
/*     */ import com.google.common.collect.Sets;
/*     */ import java.io.IOException;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Arrays;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.workflow.logging.Logger;
/*     */ import weaver.workflow.logging.LoggerFactory;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class MsgPushThread
/*     */   extends BaseBean
/*     */   implements Runnable
/*     */ {
/*     */   private List<MessageBean> messageBeans;
/*  40 */   private static final Logger log = LoggerFactory.getLogger(MsgPushUtil.class);
/*     */ 
/*     */   
/*     */   public MsgPushThread(List<MessageBean> paramList) {
/*  44 */     this.messageBeans = paramList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void run() {
/*     */     try {
/*  52 */       User user = new User(1);
/*  53 */       MsgPushService msgPushService = (MsgPushService)ServiceUtil.getService(MsgPushServiceImpl.class, user);
/*  54 */       this.messageBeans = msgPushService.getMessageBean(this.messageBeans);
/*  55 */       if (this.messageBeans == null || this.messageBeans.isEmpty())
/*     */         return; 
/*  57 */       updateBizState(this.messageBeans);
/*  58 */       pushToEC();
/*  59 */       pushToOuterOA();
/*  60 */     } catch (Exception exception) {
/*  61 */       writeLog("消息推送异常：", exception);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void pushToEC() throws IOException {
/*  71 */     ConfigManager configManager = new ConfigManager();
/*  72 */     for (MessageBean messageBean : this.messageBeans) {
/*     */       
/*  74 */       ArrayList<MessageBean> arrayList = new ArrayList();
/*     */       
/*  76 */       String str1 = messageBean.getDetailTitle();
/*  77 */       Map map = messageBean.getParams();
/*     */       
/*  79 */       if (Strings.isNullOrEmpty(str1) || map == null) {
/*     */         continue;
/*     */       }
/*     */ 
/*     */       
/*  84 */       HashMap<Object, Object> hashMap = new HashMap<>();
/*  85 */       String str2 = Util.null2String(map.get("detailBaseId"));
/*  86 */       MessageType messageType = messageBean.getMessageType();
/*  87 */       outlog("1、request msg info: requestId:" + messageBean.getTargetId() + ", userId:" + messageBean
/*  88 */           .getUserList());
/*  89 */       List list = configManager.loadUserConfig(messageType, messageBean.getUserList());
/*     */       
/*  91 */       Map<String, List<String>> map1 = null;
/*  92 */       if (messageType.getCode() == 217) map1 = (new RequestFlowRemindBiz()).getFlowremindUser(messageBean.getTargetId()); 
/*  93 */       for (WeaMessageConfig weaMessageConfig : list) {
/*     */         
/*  95 */         String str = Util.null2String(Integer.valueOf(weaMessageConfig.getUserid()));
/*  96 */         List list1 = weaMessageConfig.getMsgTypeConfigList();
/*  97 */         for (WeaMessageTypeConfig weaMessageTypeConfig : list1) {
/*     */ 
/*     */           
/* 100 */           if (!weaMessageTypeConfig.isEnable()) {
/* 101 */             outlog("2、push ec fail, requestId:" + messageBean.getTargetId() + ", userId:" + str + ",messageTypeCfgId:" + weaMessageTypeConfig
/* 102 */                 .getId());
/*     */             
/*     */             continue;
/*     */           } 
/*     */           
/* 107 */           if (weaMessageTypeConfig.isHasDetail()) {
/* 108 */             boolean bool1 = false;
/* 109 */             List list3 = weaMessageTypeConfig.getDetailList();
/* 110 */             for (WeaMessageConfigDetail weaMessageConfigDetail : list3) {
/* 111 */               boolean bool2 = weaMessageConfigDetail.defaultRuleCheck(str2);
/* 112 */               if (bool2) {
/* 113 */                 bool1 = true;
/*     */               }
/*     */             } 
/* 116 */             if (!bool1) {
/* 117 */               outlog("3、push ec fail, requestId:" + messageBean.getTargetId() + ", userId:" + str + ",messageTypeCfg:" + 
/* 118 */                   JSONObject.toJSONString(weaMessageTypeConfig));
/*     */               
/*     */               continue;
/*     */             } 
/*     */           } 
/*     */           
/* 124 */           WeaMessageUserConfig weaMessageUserConfig = null;
/* 125 */           List list2 = weaMessageConfig.getUserConfigList();
/* 126 */           for (WeaMessageUserConfig weaMessageUserConfig1 : list2) {
/* 127 */             if (weaMessageUserConfig1.getMsgTypeConfigId().equals(weaMessageTypeConfig.getId())) {
/* 128 */               weaMessageUserConfig = weaMessageUserConfig1;
/*     */               break;
/*     */             } 
/*     */           } 
/* 132 */           if (weaMessageUserConfig == null) {
/* 133 */             outlog("4、push ec fail,userConfig is null requestId:" + messageBean.getTargetId() + ", userId:" + str + ",messageTypeCfg:" + 
/* 134 */                 JSONObject.toJSONString(weaMessageTypeConfig));
/*     */             continue;
/*     */           } 
/* 137 */           boolean bool = true;
/* 138 */           if (messageType.getCode() == 217) {
/* 139 */             bool = checkUserConfig(messageBean.getTargetId(), str2, map1, weaMessageUserConfig, str);
/*     */           } else {
/* 141 */             bool = checkUserConfig(str2, messageType, weaMessageUserConfig);
/*     */           } 
/*     */           
/* 144 */           if (bool) {
/* 145 */             List<String> list3 = (List)hashMap.get(weaMessageTypeConfig);
/* 146 */             if (list3 == null) {
/* 147 */               list3 = new ArrayList();
/* 148 */               hashMap.put(weaMessageTypeConfig, list3);
/*     */             } 
/* 150 */             list3.add(str); continue;
/*     */           } 
/* 152 */           outlog("5、push ec fail, requestId:" + messageBean.getTargetId() + ", userId:" + str + ",userConfig:" + 
/* 153 */               JSONObject.toJSONString(weaMessageUserConfig));
/*     */         } 
/*     */       } 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 160 */       for (Map.Entry<Object, Object> entry : hashMap.entrySet()) {
/* 161 */         MessageBean messageBean1 = messageBean.clone();
/* 162 */         WeaMessageTypeConfig weaMessageTypeConfig = (WeaMessageTypeConfig)entry.getKey();
/* 163 */         messageBean1.setMessageConfig(weaMessageTypeConfig);
/* 164 */         messageBean1.setUserList(Sets.newHashSet((Iterable)entry.getValue()));
/* 165 */         messageBean1.setIs_Update(null);
/* 166 */         arrayList.add(messageBean1);
/*     */       } 
/* 168 */       Util_Message.sendMessage(arrayList);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   private void pushToOuterOA() {
/* 177 */     ArrayList<String> arrayList = new ArrayList();
/* 178 */     RecordSet recordSet = new RecordSet();
/* 179 */     for (MessageBean messageBean : this.messageBeans) {
/* 180 */       if (messageBean.getMessageType().getCode() == 31 || messageBean.getMessageType().getCode() == 217) {
/*     */         continue;
/*     */       }
/* 183 */       messageBean.setBizTitle(Util.formatMultiLang(messageBean.getBizTitle()));
/* 184 */       messageBean.setTargetName(Util.formatMultiLang(messageBean.getTargetName()));
/* 185 */       messageBean.setTitle(Util.formatMultiLang(messageBean.getTitle()));
/* 186 */       messageBean.setContext(Util.formatMultiLang(messageBean.getContext()));
/* 187 */       arrayList.add("流程ID：" + messageBean.getTargetId() + ", 流程名称：" + messageBean.getTargetName());
/* 188 */       String str1 = SystemEnv.getHtmlLabelName(messageBean.getMessageType().getLabelId(), 7);
/* 189 */       if (messageBean.getMessageType().getCode() == 39) {
/* 190 */         str1 = "流程已读提醒";
/*     */       }
/* 192 */       if (messageBean.getMessageType().getCode() == 35) {
/* 193 */         str1 = "撤销流程";
/*     */       }
/* 195 */       if (messageBean.getMessageType().getCode() == 38) {
/* 196 */         str1 = "收回流程";
/*     */       }
/* 198 */       if (messageBean.getMessageType().getCode() == 27) {
/* 199 */         str1 = "删除流程";
/*     */       }
/* 201 */       arrayList.add("消息类型：" + messageBean.getMessageType().getCode() + "--" + str1);
/* 202 */       String str2 = String.join(",", messageBean.getUserList());
/* 203 */       recordSet.executeQuery("select loginid from hrmresource where " + Util.getSubINClause(str2, "id", "in"), new Object[0]);
/* 204 */       ArrayList<String> arrayList1 = new ArrayList();
/* 205 */       while (recordSet.next()) {
/* 206 */         arrayList1.add(recordSet.getString(1));
/*     */       }
/* 208 */       arrayList.add("对应人员loginid：" + arrayList1);
/* 209 */       Map map1 = messageBean.getParams();
/* 210 */       Map map2 = (Map)map1.get("currentOperatorIds");
/* 211 */       arrayList.add("对应人员CID：" + JSONObject.toJSONString(map2));
/*     */     } 
/* 213 */     if (!arrayList.isEmpty()) {
/* 214 */       outlog("流程一次提交订阅消息 LOGSTART =============================");
/* 215 */       for (String str : arrayList) outlog(str); 
/* 216 */       outlog("流程一次提交订阅消息 LOGEND ===============================");
/*     */     } 
/* 218 */     outlog(JSONObject.toJSONString(this.messageBeans));
/* 219 */     Util_Message.publishMessage(this.messageBeans);
/*     */   }
/*     */ 
/*     */   
/*     */   private void updateBizState(List<MessageBean> paramList) throws Exception {
/* 224 */     ArrayList<MessageBean> arrayList = new ArrayList();
/* 225 */     for (MessageBean messageBean : paramList) {
/* 226 */       if (messageBean.getMessageType() != null && (messageBean.getMessageType().getCode() == MessageType.WF_COMPLETED.getCode() || messageBean
/* 227 */         .getMessageType().getCode() == MessageType.WF_STOP.getCode() || messageBean
/* 228 */         .getMessageType().getCode() == MessageType.WF_CANCEL.getCode() || messageBean
/* 229 */         .getMessageType().getCode() == MessageType.WF_DRAW_BACK.getCode() || messageBean
/* 230 */         .getMessageType().getCode() == MessageType.WF_DELETE.getCode()))
/*     */       {
/* 232 */         arrayList.add(messageBean); } 
/*     */     } 
/* 234 */     Util_Message.updateBizState(arrayList);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private boolean checkUserConfig(String paramString1, String paramString2, Map<String, List<String>> paramMap, WeaMessageUserConfig paramWeaMessageUserConfig, String paramString3) {
/* 245 */     boolean bool = false;
/*     */     
/* 247 */     String str1 = "0";
/* 248 */     String str2 = "0";
/* 249 */     String str3 = "";
/*     */     
/* 251 */     if (!paramWeaMessageUserConfig.isEnable()) return bool;
/*     */     
/* 253 */     boolean bool1 = paramWeaMessageUserConfig.isHasDetail();
/* 254 */     if (bool1) {
/* 255 */       List<WeaMessageConfigDetail> list = paramWeaMessageUserConfig.getDetailList();
/* 256 */       if (list.isEmpty()) bool = true;
/*     */       
/* 258 */       for (byte b = 0; b < list.size(); b++) {
/* 259 */         WeaMessageConfigDetail weaMessageConfigDetail = list.get(b);
/* 260 */         if (b == 0) {
/* 261 */           str1 = Util.null2String(weaMessageConfigDetail.getPath()).replaceAll(",", "");
/*     */         }
/* 263 */         if (b == 1) {
/* 264 */           str2 = Util.null2String(Integer.valueOf(weaMessageConfigDetail.getType())).replaceAll(",", "");
/* 265 */           str3 = Util.null2String(weaMessageConfigDetail.getPath());
/*     */         } 
/*     */       } 
/*     */ 
/*     */       
/* 270 */       String[] arrayOfString = str3.split(",");
/* 271 */       List<String> list1 = Arrays.asList(arrayOfString);
/* 272 */       if ("1".equals(str2)) {
/* 273 */         if (!list1.contains(paramString2)) return false; 
/* 274 */       } else if ("2".equals(str2)) {
/* 275 */         if (list1.contains(paramString2)) return false; 
/* 276 */       } else if ("0".equals(str2)) {
/*     */       
/*     */       } 
/*     */       
/* 280 */       if ("0".equals(str1) && ((List)paramMap.get("creatorPersons")).contains(paramString3)) bool = true; 
/* 281 */       if ("1".equals(str1) && ((List)paramMap.get("sendBackPersons")).contains(paramString3)) bool = true; 
/* 282 */       if ("2".equals(str1) && ((List)paramMap.get("approvalPersons")).contains(paramString3)) bool = true; 
/* 283 */       if ("3".equals(str1) && ((List)paramMap.get("handlePersons")).contains(paramString3)) bool = true; 
/* 284 */       if ("4".equals(str1) && ((List)paramMap.get("partakePersons")).contains(paramString3)) bool = true;
/*     */     
/*     */     } else {
/* 287 */       bool = true;
/*     */     } 
/* 289 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private boolean checkUserConfig(String paramString, MessageType paramMessageType, WeaMessageUserConfig paramWeaMessageUserConfig) {
/* 300 */     boolean bool = false;
/*     */     
/* 302 */     if (!paramWeaMessageUserConfig.isEnable()) return bool;
/*     */     
/* 304 */     boolean bool1 = paramWeaMessageUserConfig.isHasDetail();
/* 305 */     if (bool1) {
/*     */       
/* 307 */       boolean bool2 = false;
/* 308 */       List list1 = null;
/* 309 */       switch (paramMessageType.getCode()) {
/*     */         
/*     */         case 19:
/*     */         case 21:
/*     */         case 22:
/*     */         case 23:
/*     */         case 24:
/* 316 */           list1 = paramWeaMessageUserConfig.getInclude();
/* 317 */           bool2 = true;
/*     */           break;
/*     */       } 
/*     */       
/* 321 */       List list2 = paramWeaMessageUserConfig.getDetailList();
/* 322 */       if (list2.isEmpty()) bool = true; 
/* 323 */       for (WeaMessageConfigDetail weaMessageConfigDetail : list2) {
/*     */         
/* 325 */         String str1 = Util.null2String(Integer.valueOf(weaMessageConfigDetail.getType()));
/* 326 */         String str2 = Util.null2String(weaMessageConfigDetail.getPath());
/*     */         
/* 328 */         if ("1".equals(str1)) {
/* 329 */           String[] arrayOfString = str2.split(",");
/* 330 */           List<String> list = Arrays.asList(arrayOfString);
/*     */           
/* 332 */           if (bool2) {
/* 333 */             if (list1 != null && list1.contains(paramMessageType) && list.contains(paramString))
/* 334 */               bool = true;  continue;
/*     */           } 
/* 336 */           if (list.contains(paramString))
/* 337 */             bool = true;  continue;
/*     */         } 
/* 339 */         if ("2".equals(str1)) {
/* 340 */           String[] arrayOfString = str2.split(",");
/* 341 */           List<String> list = Arrays.asList(arrayOfString);
/*     */           
/* 343 */           if (!list.contains(paramString)) {
/* 344 */             if (bool2) {
/* 345 */               if (list1 != null && list1.contains(paramMessageType))
/* 346 */                 bool = true; 
/*     */               continue;
/*     */             } 
/* 349 */             bool = true;
/*     */           }  continue;
/*     */         } 
/* 352 */         if ("0".equals(str1)) {
/* 353 */           if (bool2) {
/* 354 */             if (list1 != null && list1.contains(paramMessageType))
/* 355 */               bool = true; 
/*     */             continue;
/*     */           } 
/* 358 */           bool = true;
/*     */         } 
/*     */       } 
/*     */     } else {
/*     */       
/* 363 */       bool = true;
/*     */     } 
/* 365 */     return bool;
/*     */   }
/*     */   
/*     */   private void outlog(String paramString) {
/* 369 */     RecordSet recordSet = new RecordSet();
/* 370 */     recordSet.executeQuery("select value from workflow_config where type = 'msg' and name ='out_log'", new Object[0]);
/* 371 */     recordSet.next();
/* 372 */     boolean bool = "1".equals(recordSet.getString(1));
/* 373 */     if (bool) log.info(paramString); 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/msg/MsgPushThread.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */