/*    */ package weaver.workflow.msg.notificationInteface;
/*    */ 
/*    */ import java.util.List;
/*    */ import java.util.Map;
/*    */ import weaver.general.BaseBean;
/*    */ import weaver.general.ThreadVarLanguage;
/*    */ import weaver.mobile.plugin.ecology.service.PushNotificationService;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class MobileNotification
/*    */   extends BaseBean
/*    */   implements NotificationInterface
/*    */ {
/* 24 */   private static PushNotificationService pns = new PushNotificationService();
/*    */   
/* 26 */   public static final String TITLE_WF_MSG = "" + SystemEnv.getHtmlLabelName(33569, ThreadVarLanguage.getLang()) + "";
/* 27 */   public static final String TITLE_NEWWF_MSG = "" + SystemEnv.getHtmlLabelName(33569, ThreadVarLanguage.getLang()) + "";
/* 28 */   public static final String TITLE_ENDWF_MSG = "" + SystemEnv.getHtmlLabelName(33569, ThreadVarLanguage.getLang()) + "";
/* 29 */   public static final String TITLE_OVERFLOWWF_MSG = "" + SystemEnv.getHtmlLabelName(33569, ThreadVarLanguage.getLang()) + "";
/* 30 */   public static final String TITLE_REJECT_MSG = "" + SystemEnv.getHtmlLabelName(33569, ThreadVarLanguage.getLang()) + "";
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public boolean sendMsg(List<Map<String, String>> paramList) throws Exception {
/* 37 */     return false;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/msg/notificationInteface/MobileNotification.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */