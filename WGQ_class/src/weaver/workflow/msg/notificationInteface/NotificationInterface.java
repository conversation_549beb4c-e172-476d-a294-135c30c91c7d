package weaver.workflow.msg.notificationInteface;

import java.util.List;
import java.util.Map;

public interface NotificationInterface {
  public static final int NOTE_TYPE_NEWWF = 0;
  
  public static final int NOTE_TYPE_ENDWF = 1;
  
  public static final int NOTE_TYPE_OVERFLOWWF = 10;
  
  public static final int NOTE_TYPE_REJECTWF = 14;
  
  boolean sendMsg(List<Map<String, String>> paramList) throws Exception;
}


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/msg/notificationInteface/NotificationInterface.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */