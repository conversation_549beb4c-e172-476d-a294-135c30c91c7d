/*    */ package weaver.workflow.msg;
/*    */ 
/*    */ import java.io.DataInputStream;
/*    */ import java.io.IOException;
/*    */ import java.net.ServerSocket;
/*    */ import java.net.Socket;
/*    */ import weaver.file.LogMan;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class Server
/*    */   extends Thread
/*    */ {
/*    */   protected int port;
/*    */   protected ServerSocket ss;
/*    */   protected UserBusinessChecker ubc;
/* 34 */   protected static LogMan log = LogMan.getInstance();
/*    */ 
/*    */ 
/*    */   
/*    */   public boolean start = true;
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public Server(int paramInt, UserBusinessChecker paramUserBusinessChecker) throws IOException {
/* 44 */     this.ubc = paramUserBusinessChecker;
/* 45 */     this.port = paramInt;
/* 46 */     this.ss = new ServerSocket(this.port);
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public void writeLog(Object paramObject) {
/* 54 */     log.writeLog(getClass().getName(), paramObject);
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public void run() {
/*    */     try {
/* 62 */       while (this.start) {
/* 63 */         System.out.println();
/* 64 */         System.out.println("server is listening at " + this.port + "...........................................");
/* 65 */         System.out.println();
/* 66 */         Socket socket = this.ss.accept();
/*    */ 
/*    */ 
/*    */ 
/*    */         
/*    */         try {
/* 72 */           DataInputStream dataInputStream = new DataInputStream(socket.getInputStream());
/* 73 */           int i = Integer.parseInt(dataInputStream.readUTF());
/* 74 */           String str = dataInputStream.readUTF();
/* 75 */           int j = Integer.parseInt(dataInputStream.readUTF());
/* 76 */           System.out.println("adding a user connection. id:" + i + " logintype: " + str + " port:" + this.port);
/* 77 */           this.ubc.addUserConnection(i, str, j, socket);
/* 78 */           System.out.println("add operation success. id:" + i + " logintype: " + str + " port:" + this.port);
/* 79 */         } catch (Exception exception) {
/* 80 */           System.out.println("add user connection error! port:" + this.port);
/* 81 */           writeLog("server " + this.port + " error message");
/* 82 */           writeLog(exception);
/*    */           try {
/* 84 */             socket.close();
/* 85 */           } catch (Exception exception1) {
/* 86 */             writeLog("server " + this.port + " error message");
/* 87 */             writeLog(exception1);
/*    */           } 
/*    */         } 
/*    */       } 
/* 91 */     } catch (Exception exception) {
/* 92 */       System.out.println("msg server crashed! port:" + this.port);
/* 93 */       writeLog("server " + this.port + " error message");
/* 94 */       writeLog(exception);
/*    */       try {
/* 96 */         this.ss.close();
/* 97 */       } catch (Exception exception1) {}
/*    */     } 
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/msg/Server.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */