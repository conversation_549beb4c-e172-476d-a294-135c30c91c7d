/*    */ package weaver.workflow.msg;
/*    */ 
/*    */ import weaver.general.Util;
/*    */ import weaver.rtx.RTXClientCom;
/*    */ import weaver.rtx.RTXConfig;
/*    */ import weaver.rtx.RTXException;
/*    */ import weaver.rtx.RTXUtil;
/*    */ import weaver.sms.SMSManager;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class NotifyManager
/*    */ {
/* 24 */   RTXClientCom rtxClient = null;
/*    */   
/* 26 */   SMSManager smsManager = null;
/*    */ 
/*    */   
/*    */   boolean isDownLineNotify = false;
/*    */ 
/*    */   
/*    */   public NotifyManager() {
/* 33 */     this.rtxClient = new RTXClientCom();
/* 34 */     this.smsManager = new SMSManager();
/*    */ 
/*    */     
/* 37 */     RTXConfig rTXConfig = new RTXConfig();
/* 38 */     String str = rTXConfig.getPorp(RTXConfig.IS_DOWN_LINE_NOTIFY);
/* 39 */     if (str != null && str.equals("true")) {
/* 40 */       this.isDownLineNotify = true;
/*    */     } else {
/* 42 */       this.isDownLineNotify = false;
/*    */     } 
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public boolean notify(int paramInt, String paramString) throws RTXException {
/* 56 */     RTXConfig rTXConfig = new RTXConfig();
/*    */ 
/*    */     
/*    */     try {
/* 60 */       String str = rTXConfig.getRtxLoginFiled(paramInt);
/* 61 */       int i = Util.getIntValue(rTXConfig.getPorp(RTXConfig.RTX_Version), 2006);
/* 62 */       if (i >= 2010) {
/* 63 */         this.rtxClient.notify(str, paramString, "2", RTXUtil.NOTIFY_TITLE);
/*    */       } else {
/* 65 */         this.rtxClient.notify(RTXUtil.user2Rtx(paramInt), paramString, "2", RTXUtil.NOTIFY_TITLE);
/*    */       }
/*    */     
/* 68 */     } catch (Exception exception) {
/* 69 */       exception.printStackTrace();
/*    */     } 
/* 71 */     return false;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/msg/NotifyManager.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */