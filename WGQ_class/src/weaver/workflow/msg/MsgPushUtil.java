/*     */ package weaver.workflow.msg;
/*     */ 
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import com.cloudstore.dev.api.bean.MessageBean;
/*     */ import com.cloudstore.dev.api.bean.MessageType;
/*     */ import com.cloudstore.dev.api.util.Util_Message;
/*     */ import com.engine.workflow.biz.workflowCore.RequestSubTableBiz;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.HashSet;
/*     */ import java.util.Iterator;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import java.util.Set;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.workflow.logging.Logger;
/*     */ import weaver.workflow.logging.LoggerFactory;
/*     */ import weaver.workflow.msg.entity.MsgEntity;
/*     */ import weaver.workflow.request.WFPathUtil;
/*     */ 
/*     */ public class MsgPushUtil
/*     */   extends BaseBean {
/*  24 */   private static final Logger log = LoggerFactory.getLogger(MsgPushUtil.class);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void pushMsg(List<MsgEntity> paramList) {
/*  31 */     if (paramList == null || paramList.isEmpty())
/*     */       return; 
/*  33 */     ArrayList<MessageBean> arrayList = new ArrayList();
/*     */     
/*  35 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/*  37 */     for (MsgEntity msgEntity : paramList) {
/*  38 */       if (msgEntity == null)
/*  39 */         continue;  if (msgEntity.getCurrentOperatorIdMap() != null) {
/*  40 */         Map map = msgEntity.getCurrentOperatorIdMap();
/*     */         
/*  42 */         Iterator<Map.Entry> iterator = map.entrySet().iterator();
/*  43 */         while (iterator.hasNext()) {
/*  44 */           Map.Entry entry = iterator.next();
/*  45 */           String str1 = (String)entry.getKey();
/*  46 */           if (!RequestSubTableBiz.isQueryRequestFromNewTable(Util.getIntValue(str1)))
/*  47 */             continue;  String str2 = (String)entry.getValue();
/*     */           
/*  49 */           List list = Util.splitString2List(str2, ",");
/*  50 */           Set set = (Set)hashMap.get(str1);
/*  51 */           if (set == null) {
/*  52 */             set = new HashSet();
/*  53 */             hashMap.put(str1, set);
/*     */           } 
/*  55 */           set.addAll(list);
/*     */         } 
/*     */       } 
/*  58 */       if (msgEntity.getcIdsMap() != null) {
/*  59 */         Map map = msgEntity.getcIdsMap();
/*  60 */         Iterator<Map.Entry> iterator = map.entrySet().iterator();
/*  61 */         while (iterator.hasNext()) {
/*  62 */           Map.Entry entry = iterator.next();
/*  63 */           String str = (String)entry.getKey();
/*  64 */           if (!RequestSubTableBiz.isQueryRequestFromNewTable(Util.getIntValue(str)))
/*  65 */             continue;  Set set1 = (Set)entry.getValue();
/*  66 */           if (set1 == null || set1.size() == 0)
/*  67 */             continue;  Set set2 = (Set)hashMap.get(str);
/*  68 */           if (set2 == null) {
/*  69 */             set2 = new HashSet();
/*  70 */             hashMap.put(str, set2);
/*     */           } 
/*  72 */           set2.addAll(set1);
/*     */         } 
/*     */       } 
/*     */       
/*  76 */       if (msgEntity.getUserId().isEmpty())
/*  77 */         continue;  arrayList.add(msgEntity.parse2MessageBean());
/*     */     } 
/*  79 */     if (hashMap.size() > 0) {
/*  80 */       log.info("~~pushMsg~~" + JSONObject.toJSONString(hashMap));
/*  81 */       RequestSubTableBiz requestSubTableBiz = new RequestSubTableBiz();
/*  82 */       requestSubTableBiz.asyncCurrentOperatorData(hashMap);
/*     */     } 
/*     */     
/*     */     try {
/*  86 */       WFPathUtil wFPathUtil = new WFPathUtil();
/*  87 */       wFPathUtil.getMsgThreadPool().execute(new MsgPushThread(arrayList));
/*  88 */     } catch (Exception exception) {
/*  89 */       writeLog("推送消息异常：", exception);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void updateBizState(List<MsgEntity> paramList) {
/*  99 */     if (paramList == null || paramList.isEmpty())
/*     */       return;  try {
/* 101 */       ArrayList<MessageBean> arrayList = new ArrayList();
/* 102 */       for (MsgEntity msgEntity : paramList) {
/* 103 */         if (msgEntity == null || 
/* 104 */           msgEntity.getUserId().isEmpty())
/* 105 */           continue;  MessageBean messageBean = msgEntity.parse2MessageBean();
/*     */         
/* 107 */         if (messageBean.getMessageType() != null && messageBean.getMessageType().getCode() != MessageType.WF_CONCERN.getCode()) {
/* 108 */           arrayList.add(messageBean);
/*     */         }
/*     */       } 
/* 111 */       Util_Message.updateBizState(arrayList);
/* 112 */     } catch (Exception exception) {}
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/msg/MsgPushUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */