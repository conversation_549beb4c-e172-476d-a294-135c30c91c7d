/*     */ package weaver.workflow.msg;
/*     */ 
/*     */ import java.io.DataInputStream;
/*     */ import java.io.DataOutputStream;
/*     */ import java.io.IOException;
/*     */ import java.io.InputStream;
/*     */ import java.io.OutputStream;
/*     */ import java.io.Serializable;
/*     */ 
/*     */ 
/*     */ 
/*     */ public class Message
/*     */   implements Serializable
/*     */ {
/*  15 */   int showcpt = 0;
/*     */   
/*  17 */   String cptname = "";
/*     */   
/*  19 */   int hascrmcontact = 0;
/*     */   
/*  21 */   String hasnewwf = "";
/*     */   
/*  23 */   int shownewwf = 0;
/*     */   
/*  25 */   String newrequestnames = "";
/*     */   
/*  27 */   String hasendwf = "";
/*     */   
/*  29 */   int showendwf = 0;
/*     */   
/*  31 */   String endrequestnames = "";
/*     */   
/*  33 */   int hassysremindwf = 0;
/*     */   
/*  35 */   String sysremindnames = "";
/*     */   
/*  37 */   int haspasstimenode = 0;
/*     */ 
/*     */   
/*  40 */   int isrtxmessage = 0;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void writeToStream(OutputStream paramOutputStream) throws IOException {
/*  47 */     DataOutputStream dataOutputStream = new DataOutputStream(paramOutputStream);
/*  48 */     dataOutputStream.writeInt(this.showcpt);
/*  49 */     dataOutputStream.writeUTF(this.cptname);
/*  50 */     dataOutputStream.writeInt(this.hascrmcontact);
/*  51 */     dataOutputStream.writeUTF(this.hasnewwf);
/*  52 */     dataOutputStream.writeInt(this.shownewwf);
/*  53 */     dataOutputStream.writeUTF(this.newrequestnames);
/*  54 */     dataOutputStream.writeUTF(this.hasendwf);
/*  55 */     dataOutputStream.writeInt(this.showendwf);
/*  56 */     dataOutputStream.writeUTF(this.endrequestnames);
/*  57 */     dataOutputStream.writeInt(this.hassysremindwf);
/*  58 */     dataOutputStream.writeUTF(this.sysremindnames);
/*  59 */     dataOutputStream.writeInt(this.haspasstimenode);
/*  60 */     dataOutputStream.writeInt(this.isrtxmessage);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void readFromStream(InputStream paramInputStream) throws IOException {
/*  68 */     DataInputStream dataInputStream = new DataInputStream(paramInputStream);
/*  69 */     this.showcpt = dataInputStream.readInt();
/*  70 */     this.cptname = dataInputStream.readUTF();
/*  71 */     this.hascrmcontact = dataInputStream.readInt();
/*  72 */     this.hasnewwf = dataInputStream.readUTF();
/*  73 */     this.shownewwf = dataInputStream.readInt();
/*  74 */     this.newrequestnames = dataInputStream.readUTF();
/*  75 */     this.hasendwf = dataInputStream.readUTF();
/*  76 */     this.showendwf = dataInputStream.readInt();
/*  77 */     this.endrequestnames = dataInputStream.readUTF();
/*  78 */     this.hassysremindwf = dataInputStream.readInt();
/*  79 */     this.sysremindnames = dataInputStream.readUTF();
/*  80 */     this.haspasstimenode = dataInputStream.readInt();
/*  81 */     this.isrtxmessage = dataInputStream.readInt();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void printMessage() {
/*  88 */     System.out.println("message content:");
/*  89 */     printInteger("showcpt", this.showcpt);
/*  90 */     printString("cptname", this.cptname);
/*  91 */     printInteger("hascrmcontact", this.hascrmcontact);
/*  92 */     printString("hasnewwf", this.hasnewwf);
/*  93 */     printInteger("shownewwf", this.shownewwf);
/*  94 */     printString("newrequestnames", this.newrequestnames);
/*  95 */     printString("hasendwf", this.hasendwf);
/*  96 */     printInteger("showendwf", this.showendwf);
/*  97 */     printString("endrequestnames", this.endrequestnames);
/*  98 */     printInteger("hassysremindwf", this.hassysremindwf);
/*  99 */     printString("sysremindnames", this.sysremindnames);
/* 100 */     printInteger("haspasstimenode", this.haspasstimenode);
/* 101 */     printInteger("isrtxmessage", this.isrtxmessage);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void printString(String paramString1, String paramString2) {
/* 110 */     if (paramString2 == null) {
/* 111 */       System.out.println(paramString1 + " is null");
/*     */     } else {
/* 113 */       System.out.println(paramString1 + " is: " + paramString2);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void printInteger(String paramString, int paramInt) {
/* 123 */     System.out.println(paramString + " is: " + paramInt);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/msg/Message.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */