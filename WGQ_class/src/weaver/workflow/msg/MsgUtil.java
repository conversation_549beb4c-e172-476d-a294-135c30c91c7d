/*    */ package weaver.workflow.msg;
/*    */ 
/*    */ import java.util.ArrayList;
/*    */ import java.util.StringTokenizer;
/*    */ import javax.servlet.http.HttpServletRequest;
/*    */ import uk.ltd.getahead.dwr.ExecutionContext;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.Util;
/*    */ import weaver.login.LicenseCheckLogin;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class MsgUtil
/*    */ {
/*    */   public String getPopupMsg(int paramInt1, int paramInt2, int paramInt3) {
/* 27 */     ExecutionContext executionContext = ExecutionContext.get();
/* 28 */     HttpServletRequest httpServletRequest = executionContext.getHttpServletRequest();
/* 29 */     if (httpServletRequest.getSession(true).getAttribute("weaver_user@bean") == null) {
/* 30 */       return "";
/*    */     }
/*    */     
/*    */     try {
/* 34 */       LicenseCheckLogin licenseCheckLogin = new LicenseCheckLogin();
/* 35 */       licenseCheckLogin.setOutLineDate(paramInt1, Util.getIpAddr(httpServletRequest));
/*    */       
/* 37 */       RecordSet recordSet = new RecordSet();
/* 38 */       Message message = new Message();
/* 39 */       String str1 = "";
/*    */       
/* 41 */       UserBusinessChecker userBusinessChecker = new UserBusinessChecker();
/* 42 */       String str2 = "select type,sum(ifPup) from SysPoppupRemindInfoNew where userid =" + paramInt1 + " and usertype='" + paramInt2 + "' and ifPup > 0 group by type";
/* 43 */       if (!recordSet.executeSql(str2))
/* 44 */         return ""; 
/* 45 */       if (recordSet.getCounts() > 0) {
/* 46 */         message = userBusinessChecker.checkit(paramInt1, paramInt2, paramInt3, recordSet, new Message());
/* 47 */         if (!message.hasnewwf.equals("")) {
/* 48 */           if (message.isrtxmessage == 0 && message.shownewwf == 1) {
/*    */             
/* 50 */             String[] arrayOfString = TokenizerString2(message.newrequestnames, "&", false);
/*    */             
/* 52 */             for (byte b = 0; b < arrayOfString.length; b++) {
/* 53 */               str1 = str1 + "<tr><td width='13'><img src='/images/PopupMsgDot_wev8.gif'></td><td>" + arrayOfString[b] + "</td></tr>";
/*    */             }
/* 55 */             str1 = str1 + "<tr><td><br><img src='/images/bwbullet_wev8.gif'></td><td><br><a id='sysRemindInfo' href='#'>&nbsp;" + SystemEnv.getHtmlLabelName(18985, paramInt3) + "</a></td></tr>";
/* 56 */             str2 = "update SysPoppupRemindInfoNew set ifPup=0 where userid = " + paramInt1 + " and usertype = '" + paramInt2 + "'";
/* 57 */             recordSet.executeSql(str2);
/* 58 */             return arrayOfString.length + "," + str1;
/* 59 */           }  return "";
/* 60 */         }  return "";
/* 61 */       }  return "";
/* 62 */     } catch (Exception exception) {
/* 63 */       return "";
/*    */     } 
/*    */   }
/*    */   public static String[] TokenizerString2(String paramString1, String paramString2, boolean paramBoolean) {
/* 67 */     ArrayList<String> arrayList = TokenizerString(paramString1, paramString2, paramBoolean);
/* 68 */     int i = arrayList.size();
/* 69 */     String[] arrayOfString = new String[i];
/* 70 */     for (byte b = 0; b < i; b++) {
/* 71 */       arrayOfString[b] = arrayList.get(b);
/*    */     }
/* 73 */     return arrayOfString;
/*    */   }
/*    */   public static ArrayList TokenizerString(String paramString1, String paramString2, boolean paramBoolean) {
/* 76 */     paramString1 = null2String(paramString1);
/* 77 */     paramString2 = null2String(paramString2);
/* 78 */     ArrayList<String> arrayList = new ArrayList();
/* 79 */     StringTokenizer stringTokenizer = new StringTokenizer(paramString1, paramString2, paramBoolean);
/* 80 */     while (stringTokenizer.hasMoreTokens()) {
/* 81 */       arrayList.add(stringTokenizer.nextToken());
/*    */     }
/* 83 */     return arrayList;
/*    */   }
/*    */   public static String null2String(String paramString) {
/* 86 */     return (paramString == null) ? "" : paramString;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/msg/MsgUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */