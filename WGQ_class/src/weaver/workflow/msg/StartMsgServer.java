/*    */ package weaver.workflow.msg;
/*    */ 
/*    */ import java.io.IOException;
/*    */ import java.util.Timer;
/*    */ import javax.servlet.ServletConfig;
/*    */ import javax.servlet.ServletException;
/*    */ import javax.servlet.http.HttpServlet;
/*    */ import javax.servlet.http.HttpServletRequest;
/*    */ import javax.servlet.http.HttpServletResponse;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class StartMsgServer
/*    */   extends HttpServlet
/*    */ {
/*    */   protected static int serverPort;
/*    */   private Server msgServer;
/*    */   private Timer timer;
/*    */   
/*    */   public static int getServerPort() {
/* 29 */     return serverPort;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public void init(ServletConfig paramServletConfig) throws ServletException {
/* 37 */     super.init(paramServletConfig);
/*    */     try {
/* 39 */       serverPort = Integer.parseInt(getInitParameter("serverPort"));
/* 40 */       if (!UserBusinessChecker.registeredServer.contains(new Integer(serverPort))) {
/* 41 */         UserBusinessChecker userBusinessChecker = new UserBusinessChecker();
/* 42 */         userBusinessChecker.msgServerPort = serverPort;
/* 43 */         this.msgServer = new Server(serverPort, userBusinessChecker);
/* 44 */         UserBusinessChecker.registeredServer.add(new Integer(serverPort));
/* 45 */         UserBusinessCheckTask userBusinessCheckTask = new UserBusinessCheckTask(userBusinessChecker);
/* 46 */         this.timer = new Timer(true);
/* 47 */         this.msgServer.start();
/* 48 */         System.out.println("message server started at " + serverPort + "!");
/* 49 */         this.timer.schedule(userBusinessCheckTask, 0L, 60000L);
/*    */       } 
/* 51 */     } catch (Exception exception) {
/* 52 */       System.out.println("init message server failed");
/* 53 */       exception.printStackTrace();
/*    */     } 
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public void doGet(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws ServletException, IOException {}
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public void doPost(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws ServletException, IOException {}
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public void destroy() {
/* 74 */     this.msgServer.start = false;
/* 75 */     this.timer.cancel();
/*    */     try {
/* 77 */       this.msgServer.ss.close();
/* 78 */     } catch (IOException iOException) {
/* 79 */       iOException.printStackTrace();
/*    */     } 
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/msg/StartMsgServer.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */