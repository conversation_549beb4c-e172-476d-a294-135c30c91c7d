/*    */ package weaver.workflow.msg;
/*    */ 
/*    */ import com.cloudstore.dev.api.bean.MessageBean;
/*    */ import com.cloudstore.dev.api.bean.MessageType;
/*    */ import com.cloudstore.dev.api.util.Util_Message;
/*    */ import com.engine.msgcenter.bean.WeaMessageConfig;
/*    */ import com.engine.msgcenter.bean.WeaMessageConfigDetail;
/*    */ import com.engine.msgcenter.util.ConfigManager;
/*    */ import com.google.common.base.Strings;
/*    */ import java.util.ArrayList;
/*    */ import java.util.Arrays;
/*    */ import java.util.List;
/*    */ import java.util.Map;
/*    */ import weaver.general.BaseBean;
/*    */ import weaver.general.Util;
/*    */ import weaver.workflow.msg.entity.MsgTargetTypeEnum;
/*    */ import weaver.workflow.request.RequestOperationMsgManager;
/*    */ 
/*    */ 
/*    */ 
/*    */ public class MsgCenterPushThread
/*    */   extends BaseBean
/*    */   implements Runnable
/*    */ {
/*    */   private List<MessageBean> messageBeans;
/*    */   
/*    */   public MsgCenterPushThread(List<MessageBean> paramList) {
/* 28 */     this.messageBeans = paramList;
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public void run() {
/*    */     try {
/* 35 */       ArrayList<MessageBean> arrayList = new ArrayList();
/*    */       
/* 37 */       if (this.messageBeans == null || this.messageBeans.isEmpty())
/*    */         return; 
/* 39 */       for (MessageBean messageBean : this.messageBeans) {
/*    */         
/* 41 */         String str1 = messageBean.getDetailTitle();
/* 42 */         if (Strings.isNullOrEmpty(str1))
/*    */           continue; 
/* 44 */         Map map = messageBean.getParams();
/* 45 */         if (map == null)
/* 46 */           continue;  String str2 = Util.null2String(map.get("detailBaseId"));
/*    */         
/* 48 */         MessageType messageType = messageBean.getMessageType();
/* 49 */         WeaMessageConfig weaMessageConfig = (new ConfigManager()).loadUserConfig(messageType, messageBean.getUserId());
/*    */ 
/*    */         
/* 52 */         boolean bool1 = weaMessageConfig.isEnable();
/* 53 */         if (!bool1) {
/*    */           continue;
/*    */         }
/*    */ 
/*    */         
/* 58 */         boolean bool2 = weaMessageConfig.isHasDetail();
/* 59 */         if (!bool2) {
/*    */           continue;
/*    */         }
/*    */         
/* 63 */         List list = weaMessageConfig.getDetailList();
/* 64 */         if (list == null) {
/*    */           continue;
/*    */         }
/*    */         
/* 68 */         for (WeaMessageConfigDetail weaMessageConfigDetail : list) {
/*    */           
/* 70 */           String str3 = Util.null2String(Integer.valueOf(weaMessageConfigDetail.getType()));
/* 71 */           if ("0".equals(str3)) {
/* 72 */             arrayList.add(messageBean);
/*    */             
/*    */             continue;
/*    */           } 
/* 76 */           String str4 = weaMessageConfigDetail.getPath();
/* 77 */           String[] arrayOfString = str4.split(";");
/* 78 */           List<String> list1 = Arrays.asList(arrayOfString);
/* 79 */           if ("1".equals(str3) && list1.contains(str2)) {
/* 80 */             arrayList.add(messageBean);
/*    */             continue;
/*    */           } 
/* 83 */           if (!"2".equals(str3) || list1.contains(str2));
/*    */         } 
/*    */       } 
/*    */ 
/*    */       
/* 88 */       RequestOperationMsgManager requestOperationMsgManager = new RequestOperationMsgManager();
/* 89 */       requestOperationMsgManager.insertMsg(this.messageBeans, MsgTargetTypeEnum.INNER);
/* 90 */       requestOperationMsgManager.updateBizState(arrayList);
/* 91 */       Util_Message.sendMessage(arrayList);
/* 92 */     } catch (Exception exception) {
/* 93 */       writeLog("消息推送异常：", exception);
/*    */     } 
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/msg/MsgCenterPushThread.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */