/*     */ package weaver.workflow.qiyuesuo.singleSeal.work;
/*     */ 
/*     */ import java.util.Enumeration;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import java.util.Set;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.workflow.qiyuesuo.singleSeal.util.SignDocUtil;
/*     */ import weaver.workflow.qiyuesuo.util.QYSLogUtil;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class SingleSealParamsWork
/*     */ {
/*  19 */   private QYSLogUtil logUtil = new QYSLogUtil(getClass().getName());
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, String> getSingleSealParams(int paramInt1, int paramInt2, User paramUser) {
/*  30 */     HashMap<Object, Object> hashMap = new HashMap<Object, Object>();
/*  31 */     hashMap.put("requestid", String.valueOf(paramInt1));
/*  32 */     hashMap.put("nodeid", String.valueOf(paramInt2));
/*  33 */     hashMap.put("userid", String.valueOf(paramUser.getUID()));
/*     */     
/*  35 */     hashMap.putAll(getWorkflowInfo(paramInt1, paramInt2, paramUser));
/*     */     
/*  37 */     int i = Util.getIntValue((String)hashMap.get("workflowid"));
/*  38 */     hashMap.putAll(getSingleSealSetting(i));
/*     */     
/*  40 */     String str1 = Util.null2String((String)hashMap.get("signNodes"));
/*  41 */     hashMap.put("isSignNode", isInNode(paramInt2, str1));
/*     */     
/*  43 */     String str2 = Util.null2String((String)hashMap.get("viewNodes"));
/*  44 */     hashMap.put("isViewNode", isInNode(paramInt2, str2));
/*     */     
/*  46 */     String str3 = Util.null2String((String)hashMap.get("mustSignNode"));
/*  47 */     hashMap.put("isMustSign", isInNode(paramInt2, str3));
/*     */     
/*  49 */     String str4 = Util.null2String((String)hashMap.get("everySignNode"));
/*  50 */     hashMap.put("isEverySign", isInNode(paramInt2, str4));
/*     */     
/*  52 */     String str5 = Util.null2String(SignDocUtil.isoffice2015());
/*  53 */     hashMap.put("office2015", str5);
/*     */     
/*  55 */     SignDocAuthority signDocAuthority = new SignDocAuthority();
/*  56 */     boolean bool1 = signDocAuthority.canSign((Map)hashMap);
/*  57 */     boolean bool2 = signDocAuthority.canView((Map)hashMap);
/*  58 */     hashMap.put("canSign", bool1 ? "1" : "0");
/*  59 */     hashMap.put("canView", bool2 ? "1" : "0");
/*     */     
/*  61 */     hashMap.put("isTransfer", isTransfer((Map)hashMap) ? "1" : "0");
/*  62 */     hashMap.put("isE9", SignDocUtil.isE9SingleSeal() ? "1" : "0");
/*  63 */     this.logUtil.info("params = " + hashMap);
/*  64 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private Map<String, String> getSingleSealSetting(int paramInt) {
/*  73 */     HashMap<Object, Object> hashMap = new HashMap<Object, Object>();
/*     */     
/*  75 */     RecordSet recordSet = new RecordSet();
/*  76 */     recordSet.executeQuery("select id,saveDocumentField,workflowid,description,documentField,useSealNode,isMustSign,isTransPdf,transServer,isSaveNewVersion,viewNode,saveDocumentField,mustSignNode,everySignNode from wf_qys_singleSealSetting where workflowid = ?", new Object[] { Integer.valueOf(paramInt) });
/*  77 */     if (recordSet.next()) {
/*  78 */       hashMap.put("signNodes", recordSet.getString("useSealNode"));
/*  79 */       hashMap.put("convertPdf", recordSet.getString("isTransPdf"));
/*  80 */       hashMap.put("convertServerType", recordSet.getString("transServer"));
/*  81 */       hashMap.put("isVersion", recordSet.getString("isSaveNewVersion"));
/*  82 */       hashMap.put("signFieldid", recordSet.getString("documentField"));
/*  83 */       hashMap.put("mustSignNode", recordSet.getString("mustSignNode"));
/*  84 */       hashMap.put("everySignNode", recordSet.getString("everySignNode"));
/*  85 */       hashMap.put("viewNodes", recordSet.getString("viewNode"));
/*  86 */       hashMap.put("saveDocumentField", recordSet.getString("saveDocumentField"));
/*     */     } 
/*  88 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private Map<String, String> getWorkflowInfo(int paramInt1, int paramInt2, User paramUser) {
/* 100 */     HashMap<Object, Object> hashMap = new HashMap<Object, Object>();
/* 101 */     RecordSet recordSet = new RecordSet();
/* 102 */     recordSet.executeQuery("select r.workflowid,b.ifVersion,b.formid,b.isbill,r.currentnodetype from workflow_requestbase r inner join workflow_base b on r.workflowid = b.id where r.requestid = ?", new Object[] { Integer.valueOf(paramInt1) });
/* 103 */     if (recordSet.next()) {
/* 104 */       hashMap.put("workflowid", recordSet.getString("workflowid"));
/* 105 */       hashMap.put("ifVersion", recordSet.getString("ifVersion"));
/* 106 */       hashMap.put("formid", recordSet.getString("formid"));
/* 107 */       hashMap.put("isbill", recordSet.getString("isbill"));
/* 108 */       hashMap.put("nodetype", recordSet.getString("currentnodetype"));
/*     */     } 
/*     */     
/* 111 */     recordSet.executeQuery("select isremark,nodeid from workflow_currentoperator where userid = ? and requestid = ?", new Object[] { Integer.valueOf(paramUser.getUID()), Integer.valueOf(paramInt1) });
/* 112 */     while (recordSet.next()) {
/* 113 */       int i = recordSet.getInt("nodeid");
/* 114 */       String str = recordSet.getString("isremark");
/* 115 */       if ("0".equals(str) && paramInt2 == i) {
/* 116 */         hashMap.put("isoperator", "1");
/*     */       }
/* 118 */       hashMap.put("isplayer", "1");
/*     */     } 
/* 120 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String isInNode(int paramInt, String paramString) {
/* 130 */     String str1 = "," + paramInt + ",";
/* 131 */     String str2 = "," + paramString + ",";
/* 132 */     return (str2.indexOf(str1) >= 0) ? "1" : "0";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setRequestParams(Map<String, String> paramMap, HttpServletRequest paramHttpServletRequest, int paramInt, User paramUser) {
/* 143 */     if (paramMap != null) {
/* 144 */       boolean bool = false;
/* 145 */       String str = Util.null2String(paramHttpServletRequest.getHeader("User-Agent"));
/* 146 */       if (str.indexOf("E-Mobile") > -1) {
/* 147 */         bool = true;
/*     */       }
/*     */       
/* 150 */       boolean bool1 = "1".equals(paramMap.get("canSign"));
/* 151 */       boolean bool2 = "1".equals(paramMap.get("canView"));
/* 152 */       bool1 = (bool1 && ((paramInt == 1 && bool == true) || paramInt != 1));
/* 153 */       paramMap.put("canSign", bool1 ? "1" : "0");
/* 154 */       paramMap.put("canView", bool2 ? "1" : "0");
/* 155 */       paramMap.put("ismobile", String.valueOf(paramInt));
/* 156 */       this.logUtil.info("setRequestParams changeParams canSign =" + bool1 + " canView =" + bool2 + " ismobile =" + paramInt);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static Map<String, String> request2Map(HttpServletRequest paramHttpServletRequest) {
/* 167 */     return request2Map(paramHttpServletRequest, null);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static Map<String, String> request2Map(HttpServletRequest paramHttpServletRequest, Set<String> paramSet) {
/* 178 */     HashMap<Object, Object> hashMap = new HashMap<Object, Object>();
/* 179 */     Enumeration<String> enumeration = paramHttpServletRequest.getParameterNames();
/* 180 */     while (enumeration.hasMoreElements()) {
/* 181 */       String str1 = enumeration.nextElement();
/* 182 */       String str2 = Util.null2String(paramHttpServletRequest.getParameter(str1));
/*     */       
/* 184 */       if (paramSet != null && !paramSet.isEmpty()) {
/* 185 */         if (paramSet.contains(str1))
/* 186 */           hashMap.put(str1, str2); 
/*     */         continue;
/*     */       } 
/* 189 */       hashMap.put(str1, str2);
/*     */     } 
/*     */     
/* 192 */     String str = paramHttpServletRequest.getHeader("User-Agent").toLowerCase();
/* 193 */     hashMap.put("agent", str);
/* 194 */     return (Map)hashMap;
/*     */   }
/*     */   
/*     */   private boolean isTransfer(Map<String, String> paramMap) {
/* 198 */     String str1 = Util.null2String(paramMap.get("signFieldid"));
/* 199 */     String str2 = Util.null2String(paramMap.get("saveDocumentField"));
/* 200 */     if (!"".equals(str2) && !"".equals(str1)) {
/* 201 */       return (("," + str1 + ",").indexOf("," + str2 + ",") < 0);
/*     */     }
/* 203 */     return false;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/qiyuesuo/singleSeal/work/SingleSealParamsWork.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */