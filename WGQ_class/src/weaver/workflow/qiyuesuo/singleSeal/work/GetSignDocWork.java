/*     */ package weaver.workflow.qiyuesuo.singleSeal.work;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Iterator;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.workflow.qiyuesuo.singleSeal.bean.SealDocInfo;
/*     */ import weaver.workflow.qiyuesuo.singleSeal.util.SignDocUtil;
/*     */ import weaver.workflow.qiyuesuo.util.QYSLogUtil;
/*     */ import weaver.workflow.qiyuesuo.util.QYSWorkflowUtil;
/*     */ 
/*     */ 
/*     */ 
/*     */ public class GetSignDocWork
/*     */   extends BaseBean
/*     */ {
/*  22 */   private QYSLogUtil logUtil = new QYSLogUtil(getClass().getName());
/*     */ 
/*     */ 
/*     */   
/*     */   public List<Map<String, String>> getSealDocid(Map<String, String> paramMap, User paramUser) {
/*  27 */     ArrayList<Map<String, String>> arrayList = new ArrayList();
/*     */     
/*  29 */     HashMap<Object, Object> hashMap1 = new HashMap<Object, Object>();
/*     */     
/*  31 */     int i = Util.getIntValue(paramMap.get("requestid"), 0);
/*  32 */     this.logUtil.info("getSealDocid requestid = " + i);
/*     */     
/*  34 */     if (!"1".equals(paramMap.get("canView"))) {
/*  35 */       return arrayList;
/*     */     }
/*  37 */     QYSWorkflowUtil qYSWorkflowUtil = new QYSWorkflowUtil(paramUser);
/*     */     
/*  39 */     String str1 = Util.null2String(paramMap.get("signFieldid"));
/*     */     
/*  41 */     HashMap<Object, Object> hashMap2 = new HashMap<Object, Object>();
/*  42 */     hashMap2.put("requestid", paramMap.get("requestid"));
/*  43 */     hashMap2.put("formid", paramMap.get("formid"));
/*  44 */     hashMap2.put("isbill", paramMap.get("isbill"));
/*  45 */     Map map1 = qYSWorkflowUtil.getRequestFieldInfo(hashMap2);
/*     */     
/*  47 */     this.logUtil.info("getSealDocid wfmap = " + map1);
/*  48 */     Map map2 = (Map)map1.get("fieldNameMap");
/*  49 */     Map map3 = (Map)map1.get("fieldValueMap");
/*  50 */     Map map4 = (Map)map1.get("fieldHtmlTypeMap");
/*     */     
/*  52 */     List list = Util.splitString2List(str1, ",");
/*  53 */     StringBuffer stringBuffer1 = new StringBuffer();
/*  54 */     StringBuffer stringBuffer2 = new StringBuffer();
/*  55 */     ArrayList<String> arrayList1 = new ArrayList();
/*     */     
/*  57 */     for (String str3 : list) {
/*  58 */       String str4 = Util.null2String(map2.get(str3));
/*  59 */       String str5 = Util.null2String(map3.get(str4));
/*  60 */       String str6 = Util.null2String(map4.get(str3));
/*  61 */       arrayList1.add(str5);
/*     */       
/*  63 */       if ("3".equals(str6)) {
/*  64 */         if (stringBuffer2.length() > 0) {
/*  65 */           stringBuffer2.append(",");
/*     */         }
/*  67 */         stringBuffer2.append(str3); continue;
/*  68 */       }  if ("6".equals(str6)) {
/*  69 */         if (stringBuffer1.length() > 0) {
/*  70 */           stringBuffer1.append(",");
/*     */         }
/*  72 */         stringBuffer1.append(str3);
/*     */       } 
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  79 */     List<Integer> list1 = SignDocUtil.tranIds(arrayList1);
/*  80 */     String str2 = Util.null2String(paramMap.get("isTransfer"));
/*  81 */     if ("1".equals(str2)) {
/*  82 */       String str3 = Util.null2String(paramMap.get("saveDocumentField"));
/*  83 */       String str4 = Util.null2String(map2.get(str3));
/*  84 */       String str5 = Util.null2String(map3.get(str4));
/*  85 */       String str6 = Util.null2String(map4.get(str3));
/*     */       
/*  87 */       stringBuffer1 = new StringBuffer();
/*  88 */       stringBuffer2 = new StringBuffer();
/*  89 */       if ("3".equals(str6)) {
/*  90 */         stringBuffer2.append(str3);
/*  91 */         paramMap.put("isextfile", "0");
/*  92 */       } else if ("6".equals(str6)) {
/*  93 */         stringBuffer1.append(str3);
/*  94 */         paramMap.put("isextfile", "1");
/*     */       } 
/*     */       
/*  97 */       CopyDocWork copyDocWork = new CopyDocWork();
/*  98 */       Map<Integer, Integer> map = copyDocWork.getTranMap(i, list1);
/*  99 */       this.logUtil.info("getSealDocid transferDocids = " + map);
/* 100 */       List list2 = SignDocUtil.tranId(str5);
/* 101 */       for (Integer integer : list1) {
/* 102 */         if (!map.containsKey(integer) || (map.containsKey(integer) && !list2.contains(map.get(integer)))) {
/*     */ 
/*     */           
/* 105 */           HashMap<Object, Object> hashMap3 = new HashMap<Object, Object>();
/* 106 */           hashMap3.put("docid", String.valueOf(integer));
/* 107 */           hashMap3.put("transfer", "1");
/* 108 */           hashMap1.put(integer, hashMap3);
/*     */           continue;
/*     */         } 
/* 111 */         HashMap<Object, Object> hashMap = new HashMap<Object, Object>();
/* 112 */         hashMap.put("docid", String.valueOf(map.get(integer)));
/* 113 */         hashMap.put("transfer", "0");
/* 114 */         hashMap1.put(integer, hashMap);
/*     */       } 
/*     */ 
/*     */ 
/*     */       
/* 119 */       for (Integer integer : list2) {
/* 120 */         boolean bool = false;
/* 121 */         for (Iterator<Integer> iterator = hashMap1.keySet().iterator(); iterator.hasNext(); ) {
/* 122 */           int j = ((Integer)iterator.next()).intValue();
/* 123 */           Map map5 = (Map)hashMap1.get(Integer.valueOf(j));
/* 124 */           if (map5 != null && 
/* 125 */             integer.intValue() == Util.getIntValue((String)map5.get("docid"))) {
/* 126 */             bool = true;
/*     */             
/*     */             break;
/*     */           } 
/*     */         } 
/* 131 */         if (!bool) {
/* 132 */           HashMap<Object, Object> hashMap = new HashMap<Object, Object>();
/* 133 */           hashMap.put("docid", String.valueOf(integer));
/* 134 */           hashMap.put("transfer", "0");
/* 135 */           hashMap1.put(integer, hashMap);
/*     */         } 
/*     */       } 
/*     */     } else {
/* 139 */       for (Integer integer : list1) {
/* 140 */         HashMap<Object, Object> hashMap = new HashMap<Object, Object>();
/* 141 */         hashMap.put("docid", String.valueOf(integer));
/* 142 */         hashMap.put("transfer", "0");
/* 143 */         hashMap1.put(integer, hashMap);
/*     */       } 
/*     */     } 
/*     */     
/* 147 */     paramMap.put("enclosureFields", stringBuffer1.toString());
/* 148 */     paramMap.put("docFields", stringBuffer2.toString());
/* 149 */     for (Integer integer : list1) {
/* 150 */       Map<String, String> map = (Map)hashMap1.get(integer);
/* 151 */       if (map != null) {
/* 152 */         arrayList.add(map);
/*     */       }
/*     */     } 
/*     */     
/* 156 */     this.logUtil.info("getSealDocid signList = " + arrayList);
/* 157 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<SealDocInfo> getSignImageFiles(List<Map<String, String>> paramList, Map<String, String> paramMap, User paramUser) {
/* 168 */     int i = Util.getIntValue(paramMap.get("convertPdf"), 0);
/* 169 */     String str = Util.null2String(paramMap.get("convertServerType"));
/* 170 */     int j = Util.getIntValue(paramMap.get("ismobile"), 0);
/* 171 */     ArrayList<SealDocInfo> arrayList = new ArrayList();
/* 172 */     ArrayList<Integer> arrayList1 = new ArrayList();
/* 173 */     RecordSet recordSet = new RecordSet();
/*     */     
/* 175 */     for (Map<String, String> map : paramList) {
/* 176 */       int k = Util.getIntValue((String)map.get("docid"));
/* 177 */       String str1 = Util.null2String((String)map.get("transfer"));
/* 178 */       if (k > 0) {
/* 179 */         Map<String, String> map1 = getNewDocid(k);
/* 180 */         int m = Util.getIntValue(map1.get("docid"));
/* 181 */         if (m <= 0) {
/*     */           continue;
/*     */         }
/* 184 */         String str2 = map1.get("doccreaterid");
/* 185 */         String str3 = map1.get("docsubject");
/*     */         
/* 187 */         recordSet.executeQuery("select id,docid,docfiletype,imagefileId,imagefilename,versionid,isextfile from DocImageFile where docid=? and docfiletype <> '1' and docfiletype <> '11' order by id,versionid desc", new Object[] { Integer.valueOf(m) });
/* 188 */         while (recordSet.next()) {
/* 189 */           int n = recordSet.getInt("id");
/* 190 */           if (arrayList1.contains(Integer.valueOf(n))) {
/*     */             continue;
/*     */           }
/* 193 */           arrayList1.add(Integer.valueOf(n));
/* 194 */           SealDocInfo sealDocInfo = new SealDocInfo();
/* 195 */           sealDocInfo.setDocfiletype(recordSet.getInt("docfiletype"));
/* 196 */           sealDocInfo.setDocid(recordSet.getInt("docid"));
/* 197 */           sealDocInfo.setImagefileid(recordSet.getInt("imagefileId"));
/* 198 */           sealDocInfo.setVersionid(recordSet.getInt("versionid"));
/* 199 */           String str4 = recordSet.getString("imagefilename");
/* 200 */           sealDocInfo.setIsextfile(recordSet.getString("isextfile"));
/* 201 */           sealDocInfo.setImagefilename(str4);
/* 202 */           String str5 = SignDocUtil.getExtendName(str4, recordSet.getString("docfiletype"));
/* 203 */           sealDocInfo.setExtendName(str5);
/* 204 */           sealDocInfo.setSignType(getSingleSignType(str5, i));
/* 205 */           sealDocInfo.setDoccreaterid(str2);
/* 206 */           sealDocInfo.setDocsubject(str3);
/* 207 */           sealDocInfo.setSignCount(getSignCount(sealDocInfo.getImagefileid()));
/* 208 */           sealDocInfo.setIsTransfer(str1);
/* 209 */           if (sealDocInfo.getSignType() != 0) {
/* 210 */             if (j != 1) {
/* 211 */               Map<String, String> map2 = getDocImg(sealDocInfo.getImagefileid(), str5, true, i, str, paramUser);
/* 212 */               sealDocInfo.setImgUrl(Util.null2String(map2.get("imgUrl")));
/* 213 */               sealDocInfo.setPageCount(Util.getIntValue(map2.get("pageCount"), 0));
/* 214 */               sealDocInfo.setLazy(Util.getIntValue(map2.get("lazy"), 0));
/*     */             } 
/* 216 */             arrayList.add(sealDocInfo);
/*     */           } 
/*     */         } 
/*     */       } 
/*     */     } 
/* 221 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, String> convertPdfAndSaveImg(int paramInt1, int paramInt2, String paramString, User paramUser) {
/* 233 */     this.logUtil.info("convertPdfAndSaveImg imagefileId = " + paramInt1 + "convertPdf = " + paramInt2 + "convertServerType = " + paramString + " userid = " + paramUser.getUID());
/* 234 */     if (paramInt2 == 1) {
/* 235 */       ConvertPdfWork convertPdfWork = new ConvertPdfWork();
/* 236 */       int i = convertPdfWork.convertPdf(String.valueOf(paramInt1), paramString);
/*     */       
/* 238 */       if (i > 0) {
/* 239 */         HashMap<Object, Object> hashMap = new HashMap<Object, Object>();
/* 240 */         hashMap.put("pdfimagefileid", String.valueOf(i));
/* 241 */         hashMap.put("encodeimagefileid", SignDocUtil.encodeImagefileId(String.valueOf(i), paramUser));
/* 242 */         Map<String, String> map = getDocImg(i, ".pdf", false, paramInt2, paramString, paramUser);
/* 243 */         hashMap.putAll(map);
/* 244 */         this.logUtil.info("convertPdfAndSaveImg infos = " + hashMap);
/* 245 */         return (Map)hashMap;
/*     */       } 
/*     */     } 
/* 248 */     this.logUtil.info("convertPdfAndSaveImg infos null");
/* 249 */     return null;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, String> getDocImg(int paramInt1, String paramString1, boolean paramBoolean, int paramInt2, String paramString2, User paramUser) {
/* 265 */     this.logUtil.info("getDocImg imagefileId = " + paramInt1 + "extendName = " + paramString1 + "isLazy = " + paramBoolean + "convertPdf = " + paramInt2 + "convertServerType = " + paramString2 + " userid = " + paramUser.getUID());
/* 266 */     PdfToImageFileWork pdfToImageFileWork = new PdfToImageFileWork();
/* 267 */     ConvertPdfWork convertPdfWork = new ConvertPdfWork();
/*     */     
/* 269 */     int i = 0;
/* 270 */     if (".pdf".equalsIgnoreCase(paramString1)) {
/* 271 */       i = paramInt1;
/*     */     }
/* 273 */     else if (paramInt2 == 1) {
/* 274 */       if (paramBoolean == true) {
/* 275 */         i = convertPdfWork.getConvertImagefileid(paramInt1);
/*     */       } else {
/* 277 */         i = convertPdfWork.convertPdf(String.valueOf(paramInt1), paramString2);
/*     */       } 
/*     */     } 
/*     */ 
/*     */     
/* 282 */     HashMap<Object, Object> hashMap = new HashMap<Object, Object>();
/* 283 */     if (i > 0) {
/* 284 */       Map<String, String> map = null;
/* 285 */       if (!paramBoolean) {
/* 286 */         map = pdfToImageFileWork.pdfToImage(i);
/*     */       } else {
/* 288 */         map = pdfToImageFileWork.getPdfImage(i);
/*     */       } 
/* 290 */       if (map != null && map.size() > 0) {
/* 291 */         hashMap.put("imgUrl", "/weaver/weaver.file.FileDownload?fileid=" + SignDocUtil.encodeImagefileId(map.get("imgImagefileid"), paramUser));
/* 292 */         hashMap.put("pageCount", map.get("pageCount"));
/* 293 */       } else if (paramBoolean == true && (".pdf".equalsIgnoreCase(paramString1) || paramInt2 == 1)) {
/* 294 */         hashMap.put("lazy", "1");
/*     */       } 
/* 296 */     } else if (paramInt2 == 1) {
/* 297 */       hashMap.put("lazy", "1");
/*     */     } 
/*     */     
/* 300 */     if (!hashMap.containsKey("imgUrl")) {
/* 301 */       hashMap.put("imgUrl", getDefaultDocImg(paramString1));
/* 302 */       hashMap.put("pageCount", "0");
/*     */     } 
/*     */     
/* 305 */     this.logUtil.info("getDocImg pdfimagefileid = " + i + "result = " + hashMap);
/* 306 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getDefaultDocImg(String paramString) {
/* 315 */     if (".pdf".equalsIgnoreCase(paramString))
/* 316 */       return "/workflow/qiyuesuo/singleSeal/img/pdf.png"; 
/* 317 */     if (".wps".equalsIgnoreCase(paramString))
/* 318 */       return "/workflow/qiyuesuo/singleSeal/img/wps.png"; 
/* 319 */     if (".docx".equalsIgnoreCase(paramString))
/* 320 */       return "/workflow/qiyuesuo/singleSeal/img/docx.png"; 
/* 321 */     if (".xls".equalsIgnoreCase(paramString))
/* 322 */       return "/workflow/qiyuesuo/singleSeal/img/xls.png"; 
/* 323 */     if (".xlsx".equalsIgnoreCase(paramString)) {
/* 324 */       return "/workflow/qiyuesuo/singleSeal/img/xlsx.png";
/*     */     }
/* 326 */     return "/workflow/qiyuesuo/singleSeal/img/doc.png";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private int getSingleSignType(String paramString, int paramInt) {
/* 338 */     if (".pdf".equalsIgnoreCase(paramString)) {
/* 339 */       return 1;
/*     */     }
/* 341 */     if (".doc".equalsIgnoreCase(paramString) || ".docx".equalsIgnoreCase(paramString) || ".wps".equalsIgnoreCase(paramString) || ".xls".equals(paramString) || ".xlsx".equals(paramString)) {
/*     */       
/* 343 */       if (paramInt == 1) {
/* 344 */         return 3;
/*     */       }
/* 346 */       return 2;
/*     */     } 
/*     */     
/* 349 */     return 0;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, String> getNewDocid(int paramInt) {
/* 358 */     RecordSet recordSet = new RecordSet();
/* 359 */     HashMap<Object, Object> hashMap = new HashMap<Object, Object>();
/* 360 */     int i = paramInt;
/* 361 */     recordSet.executeQuery("select d.doceditionid,d.doccreaterid,d.docsubject from docdetail d where id = ?", new Object[] { Integer.valueOf(paramInt) });
/* 362 */     if (recordSet.next()) {
/* 363 */       int j = recordSet.getInt("doceditionid");
/* 364 */       String str1 = recordSet.getString("doccreaterid");
/* 365 */       String str2 = recordSet.getString("docsubject");
/* 366 */       if (j > 0) {
/* 367 */         recordSet.executeQuery("select d.id,d.doceditionid,d.docedition,d.doccreaterid,d.docsubject from docdetail d where doceditionid=? order by docedition desc", new Object[] { Integer.valueOf(j) });
/* 368 */         if (recordSet.next()) {
/* 369 */           paramInt = recordSet.getInt("id");
/* 370 */           str1 = recordSet.getString("doccreaterid");
/* 371 */           str2 = recordSet.getString("docsubject");
/*     */         } 
/*     */       } 
/* 374 */       hashMap.put("docid", String.valueOf(paramInt));
/* 375 */       hashMap.put("doccreaterid", str1);
/* 376 */       hashMap.put("docsubject", str2);
/*     */     } 
/* 378 */     if (i != paramInt) {
/* 379 */       this.logUtil.info(" olddocid = " + i + " newdocid" + hashMap);
/*     */     }
/* 381 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<Integer, Integer> getSignRecord(List<Integer> paramList) {
/* 390 */     HashMap<Object, Object> hashMap = new HashMap<Object, Object>();
/* 391 */     StringBuffer stringBuffer = new StringBuffer();
/* 392 */     for (Integer integer : paramList) {
/* 393 */       if (stringBuffer.length() > 0) {
/* 394 */         stringBuffer.append(",");
/*     */       }
/* 396 */       stringBuffer.append(integer);
/*     */     } 
/* 398 */     if (stringBuffer.length() > 0) {
/* 399 */       RecordSet recordSet = new RecordSet();
/* 400 */       recordSet.executeQuery("select imagefileid,signNum from wf_qiyuesuoSingleSign where imagefileid in (" + stringBuffer.toString() + ")", new Object[0]);
/* 401 */       while (recordSet.next()) {
/* 402 */         hashMap.put(Integer.valueOf(recordSet.getInt("imagefileid")), Integer.valueOf(recordSet.getInt("signNum")));
/*     */       }
/*     */     } 
/* 405 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getSignCount(int paramInt) {
/* 414 */     RecordSet recordSet = new RecordSet();
/* 415 */     recordSet.executeQuery("select imagefileid,signNum from wf_qiyuesuoSingleSign where imagefileid = ?", new Object[] { Integer.valueOf(paramInt) });
/* 416 */     if (recordSet.next()) {
/* 417 */       return recordSet.getInt("signNum");
/*     */     }
/* 419 */     return 0;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<Integer, Integer> getSignDetail(int paramInt1, int paramInt2, int paramInt3, List<Integer> paramList) {
/* 431 */     this.logUtil.info("getSignDetail requestid = " + paramInt1 + "nodeid = " + paramInt2 + "userid = " + paramInt3 + "imagefileids = " + paramList);
/* 432 */     HashMap<Object, Object> hashMap = new HashMap<Object, Object>();
/* 433 */     StringBuffer stringBuffer = new StringBuffer();
/* 434 */     for (Integer integer : paramList) {
/* 435 */       if (stringBuffer.length() > 0) {
/* 436 */         stringBuffer.append(",");
/*     */       }
/* 438 */       stringBuffer.append(integer);
/*     */     } 
/* 440 */     if (stringBuffer.length() > 0) {
/* 441 */       RecordSet recordSet = new RecordSet();
/* 442 */       recordSet.executeQuery("select imagefileid,count(1) as signNum from wf_qiyuesuoSSdetail where isvalid = 1 and requestid = ? and nodeid = ? and userid = ? and imagefileid in (" + stringBuffer.toString() + ") group by imagefileid", new Object[] { Integer.valueOf(paramInt1), Integer.valueOf(paramInt2), Integer.valueOf(paramInt3) });
/* 443 */       while (recordSet.next()) {
/* 444 */         hashMap.put(Integer.valueOf(recordSet.getInt("imagefileid")), Integer.valueOf(recordSet.getInt("signNum")));
/*     */       }
/*     */     } 
/* 447 */     this.logUtil.info("getSignDetail results = " + hashMap);
/* 448 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<Integer, Integer> getSignDetail(int paramInt1, int paramInt2, List<Integer> paramList) {
/* 459 */     this.logUtil.info("getSignDetail requestid = " + paramInt1 + "nodeid = " + paramInt2 + "imagefileids = " + paramList);
/* 460 */     HashMap<Object, Object> hashMap = new HashMap<Object, Object>();
/* 461 */     StringBuffer stringBuffer = new StringBuffer();
/* 462 */     for (Integer integer : paramList) {
/* 463 */       if (stringBuffer.length() > 0) {
/* 464 */         stringBuffer.append(",");
/*     */       }
/* 466 */       stringBuffer.append(integer);
/*     */     } 
/* 468 */     if (stringBuffer.length() > 0) {
/* 469 */       RecordSet recordSet = new RecordSet();
/* 470 */       recordSet.executeQuery("select imagefileid,count(1) as signNum from wf_qiyuesuoSSdetail where isvalid = 1 and requestid = ? and nodeid = ? and imagefileid in (" + stringBuffer.toString() + ") group by imagefileid", new Object[] { Integer.valueOf(paramInt1), Integer.valueOf(paramInt2) });
/* 471 */       while (recordSet.next()) {
/* 472 */         hashMap.put(Integer.valueOf(recordSet.getInt("imagefileid")), Integer.valueOf(recordSet.getInt("signNum")));
/*     */       }
/*     */     } 
/* 475 */     this.logUtil.info("getSignDetail results = " + hashMap);
/* 476 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int queryImageFileidByNameUUID(String paramString) {
/* 485 */     if (!paramString.equals("")) {
/* 486 */       RecordSet recordSet = new RecordSet();
/* 487 */       recordSet.executeQuery("select imagefileid from imagefile where imagefilename = ? ", new Object[] { paramString });
/* 488 */       if (recordSet.next()) {
/* 489 */         return recordSet.getInt("imagefileid");
/*     */       }
/*     */     } 
/* 492 */     return 0;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int queryImageFileidByVersionid(int paramInt1, int paramInt2) {
/* 502 */     if (paramInt1 > 0 && paramInt2 > 0) {
/* 503 */       RecordSet recordSet = new RecordSet();
/* 504 */       recordSet.executeQuery("select imagefileid from docimagefile where docid = ? and versionid = ?", new Object[] { Integer.valueOf(paramInt1), Integer.valueOf(paramInt2) });
/* 505 */       if (recordSet.next()) {
/* 506 */         return recordSet.getInt("imagefileid");
/*     */       }
/*     */     } 
/* 509 */     return 0;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public SealDocInfo queryImageFileByid(int paramInt) {
/* 518 */     RecordSet recordSet = new RecordSet();
/* 519 */     recordSet.executeQuery("select id,docid,docfiletype,imagefileId,imagefilename,versionid,isextfile,hasUsedTemplet from DocImageFile where imagefileid=? and docfiletype <> '1' and docfiletype <> '11' order by id,versionid desc", new Object[] { Integer.valueOf(paramInt) });
/* 520 */     if (recordSet.next()) {
/* 521 */       SealDocInfo sealDocInfo = new SealDocInfo();
/* 522 */       sealDocInfo.setId(recordSet.getInt("id"));
/* 523 */       sealDocInfo.setDocfiletype(recordSet.getInt("docfiletype"));
/* 524 */       sealDocInfo.setDocid(recordSet.getInt("docid"));
/* 525 */       sealDocInfo.setImagefileid(recordSet.getInt("imagefileId"));
/* 526 */       sealDocInfo.setVersionid(recordSet.getInt("versionid"));
/* 527 */       String str1 = recordSet.getString("imagefilename");
/* 528 */       sealDocInfo.setImagefilename(str1);
/* 529 */       String str2 = SignDocUtil.getExtendName(str1, recordSet.getString("docfiletype"));
/* 530 */       sealDocInfo.setExtendName(str2);
/* 531 */       sealDocInfo.setHasUsedTemplet(recordSet.getString("hasUsedTemplet"));
/* 532 */       sealDocInfo.setIsextfile(recordSet.getString("isextfile"));
/* 533 */       return sealDocInfo;
/*     */     } 
/* 535 */     return null;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public SealDocInfo queryNewVersionImageFile(int paramInt1, int paramInt2) {
/* 545 */     RecordSet recordSet = new RecordSet();
/* 546 */     recordSet.executeQuery("select id,docid,docfiletype,imagefileId,imagefilename,versionid,isextfile,hasUsedTemplet from DocImageFile where docid=? and docfiletype <> '1' and docfiletype <> '11' order by id,versionid desc", new Object[] { Integer.valueOf(paramInt1) });
/* 547 */     ArrayList<Integer> arrayList = new ArrayList();
/* 548 */     while (recordSet.next()) {
/* 549 */       int i = recordSet.getInt("id");
/* 550 */       if (arrayList.contains(Integer.valueOf(i))) {
/*     */         continue;
/*     */       }
/* 553 */       arrayList.add(Integer.valueOf(i));
/* 554 */       int j = recordSet.getInt("imagefileId");
/* 555 */       if (j != paramInt2) {
/*     */         continue;
/*     */       }
/*     */       
/* 559 */       SealDocInfo sealDocInfo = new SealDocInfo();
/* 560 */       sealDocInfo.setId(recordSet.getInt("id"));
/* 561 */       sealDocInfo.setDocfiletype(recordSet.getInt("docfiletype"));
/* 562 */       sealDocInfo.setDocid(recordSet.getInt("docid"));
/* 563 */       sealDocInfo.setImagefileid(recordSet.getInt("imagefileId"));
/* 564 */       sealDocInfo.setVersionid(recordSet.getInt("versionid"));
/* 565 */       String str1 = recordSet.getString("imagefilename");
/* 566 */       sealDocInfo.setImagefilename(str1);
/* 567 */       String str2 = SignDocUtil.getExtendName(str1, recordSet.getString("docfiletype"));
/* 568 */       sealDocInfo.setExtendName(str2);
/* 569 */       sealDocInfo.setHasUsedTemplet(recordSet.getString("hasUsedTemplet"));
/* 570 */       sealDocInfo.setIsextfile(recordSet.getString("isextfile"));
/* 571 */       return sealDocInfo;
/*     */     } 
/* 573 */     return null;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/qiyuesuo/singleSeal/work/GetSignDocWork.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */