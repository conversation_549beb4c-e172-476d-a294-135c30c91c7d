/*     */ package weaver.workflow.qiyuesuo.singleSeal.work;
/*     */ 
/*     */ import java.awt.image.BufferedImage;
/*     */ import java.io.ByteArrayOutputStream;
/*     */ import java.io.File;
/*     */ import java.io.FileInputStream;
/*     */ import java.io.FileNotFoundException;
/*     */ import java.io.IOException;
/*     */ import java.io.InputStream;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import java.util.UUID;
/*     */ import javax.imageio.ImageIO;
/*     */ import org.apache.pdfbox.io.MemoryUsageSetting;
/*     */ import org.apache.pdfbox.pdmodel.PDDocument;
/*     */ import org.apache.pdfbox.rendering.PDFRenderer;
/*     */ import weaver.common.FileUtil;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.file.FileUpload;
/*     */ import weaver.file.ImageFileManager;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.system.SystemComInfo;
/*     */ 
/*     */ public class PdfToImageFileWork
/*     */   extends BaseBean
/*     */ {
/*     */   public Map<String, String> pdfToImage(int paramInt) {
/*  31 */     int i = -1;
/*  32 */     if (paramInt < 0) {
/*  33 */       writeLog("pdfFirstPageToImage(int imagefileId)，参数imagefileId：" + paramInt + "不合法！");
/*  34 */       return new HashMap<String, String>();
/*     */     } 
/*     */     
/*  37 */     Map<String, String> map = getPdfImage(paramInt);
/*  38 */     if (map.size() > 0) {
/*  39 */       return map;
/*     */     }
/*     */     
/*  42 */     InputStream inputStream = ImageFileManager.getInputStreamById(paramInt);
/*  43 */     if (inputStream == null) {
/*  44 */       writeLog("通过方法ImageFileManager.getInputStreamById(imagefileId)，参数imagefileId=" + paramInt + "获取的数据为null");
/*  45 */       return map;
/*     */     } 
/*     */     
/*     */     try {
/*  49 */       PDDocument pDDocument = PDDocument.load(inputStream, MemoryUsageSetting.setupTempFileOnly());
/*  50 */       PDFRenderer pDFRenderer = new PDFRenderer(pDDocument);
/*  51 */       int j = pDDocument.getNumberOfPages();
/*  52 */       SystemComInfo systemComInfo = new SystemComInfo();
/*  53 */       String str1 = FileUpload.getCreateDir(systemComInfo.getFilesystem());
/*  54 */       String str2 = UUID.randomUUID().toString();
/*  55 */       FileUtil.mkDir(str1);
/*  56 */       String str3 = str1 + File.separator + str2 + ".png";
/*  57 */       ArrayList<BufferedImage> arrayList = new ArrayList();
/*     */       
/*  59 */       if (j > 0) {
/*  60 */         BufferedImage bufferedImage = pDFRenderer.renderImageWithDPI(0, 72.0F);
/*  61 */         arrayList.add(bufferedImage);
/*     */       } 
/*     */       
/*  64 */       yPic(arrayList, str3);
/*  65 */       i = saveAsImagefile(str2, ".png", fileToBytes(str3), "");
/*  66 */       if (i > 0) {
/*  67 */         savePdfImage(paramInt, i, j);
/*  68 */         map.put("imgImagefileid", Util.null2String(Integer.valueOf(i)));
/*  69 */         map.put("pdfimagefileId", Util.null2String(Integer.valueOf(paramInt)));
/*  70 */         map.put("pageCount", Util.null2String(Integer.valueOf(j)));
/*     */       } 
/*  72 */     } catch (IOException iOException) {
/*  73 */       iOException.printStackTrace();
/*     */     } 
/*  75 */     return map;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void savePdfImage(int paramInt1, int paramInt2, int paramInt3) {
/*  85 */     RecordSet recordSet = new RecordSet();
/*  86 */     recordSet.executeQuery("select 1 from wf_qiyuesuopdfimage where pdfimagefileId = ?", new Object[] { Integer.valueOf(paramInt1) });
/*  87 */     if (recordSet.next()) {
/*  88 */       recordSet.executeUpdate("update wf_qiyuesuopdfimage set imgImagefileid = ?,pageCount = ? where pdfimagefileId = ?", new Object[] { Integer.valueOf(paramInt2), Integer.valueOf(paramInt3), Integer.valueOf(paramInt1) });
/*     */     } else {
/*  90 */       recordSet.executeUpdate("insert into wf_qiyuesuopdfimage (imgImagefileid,pageCount,pdfimagefileId) values (?,?,?)", new Object[] { Integer.valueOf(paramInt2), Integer.valueOf(paramInt3), Integer.valueOf(paramInt1) });
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, String> getPdfImage(int paramInt) {
/* 100 */     HashMap<Object, Object> hashMap = new HashMap<Object, Object>();
/* 101 */     RecordSet recordSet = new RecordSet();
/* 102 */     recordSet.executeQuery("select imgImagefileid,pageCount from wf_qiyuesuopdfimage where pdfimagefileId = ?", new Object[] { Integer.valueOf(paramInt) });
/* 103 */     if (recordSet.next()) {
/* 104 */       hashMap.put("imgImagefileid", Util.null2String(recordSet.getString("imgImagefileid")));
/* 105 */       hashMap.put("pdfimagefileId", Util.null2String(Integer.valueOf(paramInt)));
/* 106 */       hashMap.put("pageCount", Util.null2String(recordSet.getString("pageCount")));
/*     */     } 
/* 108 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void yPic(List<BufferedImage> paramList, String paramString) {
/* 117 */     if (paramList == null || paramList.size() <= 0) {
/* 118 */       writeLog("图片数组为空!");
/*     */       return;
/*     */     } 
/*     */     try {
/* 122 */       int i = 0;
/* 123 */       int j = 0;
/* 124 */       int k = 0;
/* 125 */       int m = 0;
/* 126 */       int n = paramList.size();
/* 127 */       int[] arrayOfInt = new int[n];
/* 128 */       BufferedImage bufferedImage1 = null;
/* 129 */       ArrayList<int[]> arrayList = new ArrayList();
/*     */       
/* 131 */       for (byte b1 = 0; b1 < n; b1++) {
/* 132 */         bufferedImage1 = paramList.get(b1);
/* 133 */         arrayOfInt[b1] = k = bufferedImage1.getHeight();
/* 134 */         if (b1 == 0) {
/* 135 */           j = bufferedImage1.getWidth();
/*     */         }
/* 137 */         i += k;
/* 138 */         int[] arrayOfInt1 = new int[j * k];
/* 139 */         arrayOfInt1 = bufferedImage1.getRGB(0, 0, j, k, arrayOfInt1, 0, j);
/* 140 */         arrayList.add(arrayOfInt1);
/*     */       } 
/* 142 */       k = 0;
/*     */       
/* 144 */       BufferedImage bufferedImage2 = new BufferedImage(j, i, 1);
/* 145 */       for (byte b2 = 0; b2 < n; b2++) {
/* 146 */         m = arrayOfInt[b2];
/* 147 */         if (b2 != 0) k += m; 
/* 148 */         bufferedImage2.setRGB(0, k, j, m, arrayList.get(b2), 0, j);
/*     */       } 
/* 150 */       File file = new File(paramString);
/* 151 */       ImageIO.write(bufferedImage2, "png", file);
/* 152 */     } catch (Exception exception) {
/* 153 */       writeLog("exception:", exception);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public byte[] fileToBytes(String paramString) {
/* 164 */     byte[] arrayOfByte = null;
/* 165 */     File file = new File(paramString);
/* 166 */     FileInputStream fileInputStream = null;
/* 167 */     ByteArrayOutputStream byteArrayOutputStream = null;
/*     */     try {
/* 169 */       fileInputStream = new FileInputStream(file);
/* 170 */       byteArrayOutputStream = new ByteArrayOutputStream();
/* 171 */       byte[] arrayOfByte1 = new byte[1024];
/*     */       int i;
/* 173 */       while ((i = fileInputStream.read(arrayOfByte1)) != -1) {
/* 174 */         byteArrayOutputStream.write(arrayOfByte1, 0, i);
/*     */       }
/* 176 */       arrayOfByte = byteArrayOutputStream.toByteArray();
/* 177 */     } catch (FileNotFoundException fileNotFoundException) {
/* 178 */       fileNotFoundException.printStackTrace();
/* 179 */       writeLog("异常信息为：" + fileNotFoundException.getMessage());
/* 180 */     } catch (IOException iOException) {
/* 181 */       writeLog("异常信息为：" + iOException.getMessage());
/*     */     } finally {
/*     */       try {
/* 184 */         if (null != byteArrayOutputStream) {
/* 185 */           byteArrayOutputStream.close();
/*     */         }
/* 187 */       } catch (IOException iOException) {
/* 188 */         writeLog("异常信息为：" + iOException.getMessage());
/*     */       } finally {
/*     */         try {
/* 191 */           if (null != fileInputStream) {
/* 192 */             fileInputStream.close();
/*     */           }
/* 194 */         } catch (IOException iOException) {
/* 195 */           writeLog("异常信息为：" + iOException.getMessage());
/*     */         } 
/*     */       } 
/*     */     } 
/* 199 */     return arrayOfByte;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int saveAsImagefile(String paramString1, String paramString2, byte[] paramArrayOfbyte, String paramString3) {
/* 207 */     ImageFileManager imageFileManager = new ImageFileManager();
/* 208 */     if (paramString2.startsWith(".")) {
/* 209 */       imageFileManager.setImagFileName(paramString1 + paramString2);
/*     */     } else {
/* 211 */       imageFileManager.setImagFileName(paramString1 + "." + paramString2);
/*     */     } 
/* 213 */     imageFileManager.setData(paramArrayOfbyte);
/* 214 */     imageFileManager.setComefrom(paramString3);
/* 215 */     return imageFileManager.saveImageFile();
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/qiyuesuo/singleSeal/work/PdfToImageFileWork.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */