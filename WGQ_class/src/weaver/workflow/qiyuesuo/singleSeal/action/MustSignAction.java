/*    */ package weaver.workflow.qiyuesuo.singleSeal.action;
/*    */ 
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.List;
/*    */ import java.util.Map;
/*    */ import weaver.general.BaseBean;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.User;
/*    */ import weaver.interfaces.workflow.action.Action;
/*    */ import weaver.soa.workflow.request.RequestInfo;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ import weaver.workflow.qiyuesuo.singleSeal.bean.SealDocInfo;
/*    */ import weaver.workflow.qiyuesuo.singleSeal.work.GetSignDocWork;
/*    */ import weaver.workflow.qiyuesuo.singleSeal.work.SingleSealParamsWork;
/*    */ import weaver.workflow.qiyuesuo.util.QYSLogUtil;
/*    */ 
/*    */ 
/*    */ public class MustSignAction
/*    */   extends BaseBean
/*    */   implements Action
/*    */ {
/* 23 */   private QYSLogUtil logUtil = new QYSLogUtil(getClass().getName());
/*    */ 
/*    */   
/*    */   public String execute(RequestInfo paramRequestInfo) {
/* 27 */     int i = paramRequestInfo.getRequestManager().getRequestid();
/* 28 */     int j = paramRequestInfo.getRequestManager().getNodeid();
/* 29 */     User user = paramRequestInfo.getRequestManager().getUser();
/*    */     
/* 31 */     Map<String, String> map = canSubmit(i, j, user, 1);
/* 32 */     if ("0".equals(map.get("success"))) {
/* 33 */       paramRequestInfo.getRequestManager().setMessageid("126221");
/* 34 */       paramRequestInfo.getRequestManager().setMessagecontent(Util.null2String(map.get("msg")));
/* 35 */       return "0";
/*    */     } 
/* 37 */     return "1";
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public Map<String, String> canSubmit(int paramInt1, int paramInt2, User paramUser, int paramInt3) {
/* 49 */     this.logUtil.info("canSubmit requestid = " + paramInt1 + " nodeid = " + paramInt2 + " source" + paramInt3 + " userid =" + paramUser.getUID());
/* 50 */     HashMap<Object, Object> hashMap = new HashMap<Object, Object>();
/*    */     
/* 52 */     SingleSealParamsWork singleSealParamsWork = new SingleSealParamsWork();
/* 53 */     Map<String, String> map = singleSealParamsWork.getSingleSealParams(paramInt1, paramInt2, paramUser);
/* 54 */     boolean bool1 = "1".equals(map.get("isMustSign"));
/* 55 */     boolean bool2 = "1".equals(map.get("isEverySign"));
/* 56 */     boolean bool3 = "1".equals(map.get("canSign"));
/* 57 */     StringBuffer stringBuffer = new StringBuffer();
/* 58 */     if (bool1 == true && bool3 == true) {
/*    */       Map map1;
/* 60 */       map.put("ismobile", "1");
/* 61 */       GetSignDocWork getSignDocWork = new GetSignDocWork();
/* 62 */       List list1 = getSignDocWork.getSealDocid(map, paramUser);
/* 63 */       List list2 = getSignDocWork.getSignImageFiles(list1, map, paramUser);
/* 64 */       ArrayList<Integer> arrayList = new ArrayList();
/* 65 */       for (SealDocInfo sealDocInfo : list2) {
/* 66 */         arrayList.add(Integer.valueOf(sealDocInfo.getImagefileid()));
/*    */       }
/*    */       
/* 69 */       if (bool2 == true) {
/* 70 */         map1 = getSignDocWork.getSignDetail(paramInt1, paramInt2, paramUser.getUID(), arrayList);
/*    */       } else {
/* 72 */         map1 = getSignDocWork.getSignDetail(paramInt1, paramInt2, arrayList);
/*    */       } 
/* 74 */       for (SealDocInfo sealDocInfo : list2) {
/* 75 */         int i = sealDocInfo.getImagefileid();
/* 76 */         Integer integer = (Integer)map1.get(Integer.valueOf(i));
/* 77 */         if (integer == null || integer.intValue() <= 0) {
/* 78 */           if (stringBuffer.length() > 0) {
/* 79 */             stringBuffer.append(",");
/*    */           }
/* 81 */           stringBuffer.append(sealDocInfo.getImagefilename());
/*    */         } 
/*    */       } 
/*    */     } 
/* 85 */     if (stringBuffer.length() == 0) {
/* 86 */       hashMap.put("success", "1");
/*    */     } else {
/* 88 */       hashMap.put("success", "0");
/* 89 */       hashMap.put("msg", SystemEnv.getHtmlLabelName(518409, paramUser.getLanguage()) + stringBuffer.toString());
/*    */     } 
/*    */     
/* 92 */     this.logUtil.info("canSubmit result = " + hashMap);
/* 93 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/qiyuesuo/singleSeal/action/MustSignAction.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */