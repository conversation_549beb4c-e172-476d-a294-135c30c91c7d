/*     */ package weaver.workflow.qiyuesuo.singleSeal.action;
/*     */ 
/*     */ import java.lang.reflect.Constructor;
/*     */ import java.lang.reflect.Method;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.interfaces.workflow.action.Action;
/*     */ import weaver.soa.workflow.request.RequestInfo;
/*     */ import weaver.workflow.qiyuesuo.bean.QYSResponse;
/*     */ import weaver.workflow.qiyuesuo.singleSeal.QYSSingleSealInterface;
/*     */ import weaver.workflow.qiyuesuo.util.QYSLogUtil;
/*     */ import weaver.workflow.request.RequestManager;
/*     */ 
/*     */ public class QYSSingleSignAction
/*     */   extends BaseBean
/*     */   implements Action
/*     */ {
/*  21 */   private QYSLogUtil logUtil = new QYSLogUtil(getClass().getName());
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String execute(RequestInfo paramRequestInfo) {
/*  27 */     String str1 = paramRequestInfo.getRequestid();
/*  28 */     int i = Util.getIntValue(paramRequestInfo.getWorkflowid(), 0);
/*  29 */     int j = paramRequestInfo.getObjid();
/*  30 */     int k = paramRequestInfo.getObjtype();
/*  31 */     String str2 = paramRequestInfo.getIspreadd();
/*  32 */     RequestManager requestManager = paramRequestInfo.getRequestManager();
/*     */     
/*  34 */     QYSResponse qYSResponse = new QYSResponse((new QYSSingleSealInterface()).addNewVersionSettingByWorkflowId(i));
/*  35 */     if (qYSResponse.getCode() != 0) {
/*  36 */       requestManager.setMessageid("126221");
/*  37 */       String str = qYSResponse.getMessage();
/*  38 */       if (str.equals("")) {
/*  39 */         str = "单体用印集成配置复制失败！";
/*     */       }
/*  41 */       requestManager.setMessagecontent(str);
/*  42 */       return "0";
/*     */     } 
/*     */     
/*  45 */     this.logUtil.info("--QYSSingleSignAction--start--requestid:" + str1 + "--workflowid:" + i + "--objType:" + k + "--objId:" + j + "--isPre:" + str2);
/*     */     
/*  47 */     RecordSet recordSet = new RecordSet();
/*  48 */     String str3 = "";
/*     */     
/*  50 */     String str4 = "select * from wf_qysSingleSealActionSet where workflowid=? and objId=? and objType=? and isPre=? and isused = 1 order by sourceid,id";
/*  51 */     recordSet.executeQuery(str4, new Object[] { Integer.valueOf(i), Integer.valueOf(j), Integer.valueOf(k), str2 });
/*  52 */     while (recordSet.next()) {
/*  53 */       String str = Util.null2String(recordSet.getString("actionIds"));
/*  54 */       if (!"".equals(str)) {
/*  55 */         if ("".equals(str3)) {
/*  56 */           str3 = str; continue;
/*     */         } 
/*  58 */         str3 = str3 + "," + str;
/*     */       } 
/*     */     } 
/*     */ 
/*     */     
/*  63 */     this.logUtil.info("--QYSSingleSignAction--actionIds:" + str3);
/*     */     
/*  65 */     if (!"".equals(str3)) {
/*  66 */       Map map = getQYSAction();
/*     */       
/*  68 */       String[] arrayOfString = str3.split(",");
/*  69 */       for (byte b = 0; b < arrayOfString.length; b++) {
/*  70 */         String str5 = Util.null2String(arrayOfString[b]).trim();
/*  71 */         String str6 = Util.null2String(map.get(str5));
/*     */         
/*  73 */         this.logUtil.info("--QYSSingleSignAction--id:" + str5 + "--actionClass:" + str6);
/*     */         
/*  75 */         if (!"".equals(str6)) {
/*     */ 
/*     */           
/*     */           try {
/*     */             
/*  80 */             Class<?> clazz = Class.forName(str6);
/*  81 */             Constructor<?> constructor = clazz.getConstructor(new Class[0]);
/*  82 */             Method method = clazz.getMethod("execute", new Class[] { RequestInfo.class });
/*  83 */             method.invoke(constructor.newInstance(new Object[0]), new Object[] { paramRequestInfo });
/*  84 */           } catch (Exception exception) {
/*  85 */             writeLog(exception);
/*     */           } 
/*     */           
/*  88 */           if (!"".equals(requestManager.getMessageid())) {
/*  89 */             writeLog("--QYSSingleSignAction--messageid:" + requestManager.getMessageid() + "--Messagecontent:" + requestManager.getMessagecontent());
/*  90 */             return "0";
/*     */           } 
/*     */         } 
/*     */       } 
/*     */     } 
/*     */     
/*  96 */     this.logUtil.info("--QYSSingleSignAction--end--");
/*  97 */     return "1";
/*     */   }
/*     */   
/*     */   private Map getQYSAction() {
/* 101 */     HashMap<Object, Object> hashMap = new HashMap<Object, Object>();
/*     */     
/* 103 */     RecordSet recordSet = new RecordSet();
/* 104 */     recordSet.executeQuery("select * from qysSingleSealAction ", new Object[0]);
/* 105 */     while (recordSet.next()) {
/* 106 */       hashMap.put(recordSet.getString("id"), recordSet.getString("actionClass"));
/*     */     }
/*     */     
/* 109 */     return hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/qiyuesuo/singleSeal/action/QYSSingleSignAction.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */