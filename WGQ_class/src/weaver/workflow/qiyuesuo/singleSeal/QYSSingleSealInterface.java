/*     */ package weaver.workflow.qiyuesuo.singleSeal;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Iterator;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import java.util.concurrent.locks.Lock;
/*     */ import java.util.concurrent.locks.ReentrantLock;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import weaver.conn.ConnStatement;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.workflow.action.BaseAction;
/*     */ import weaver.workflow.qiyuesuo.bean.QYSResponse;
/*     */ import weaver.workflow.qiyuesuo.util.QYSDocUtil;
/*     */ import weaver.workflow.qiyuesuo.util.QYSLogUtil;
/*     */ import weaver.workflow.qiyuesuo.util.QYSUtil;
/*     */ import weaver.workflow.qiyuesuo.util.QYSWorkflowUtil;
/*     */ import weaver.workflow.workflow.WorkflowVersion;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class QYSSingleSealInterface
/*     */   extends BaseBean
/*     */ {
/*  34 */   private static Lock addNewVersionSettingByWorkflowId = new ReentrantLock();
/*     */   private Map settingMap;
/*     */   private User user;
/*  37 */   private QYSLogUtil log = new QYSLogUtil(getClass().getName());
/*     */ 
/*     */   
/*     */   public QYSSingleSealInterface(User paramUser) {
/*  41 */     this();
/*  42 */     this.user = paramUser;
/*     */   }
/*     */   public QYSSingleSealInterface() {}
/*     */   public User getUser() {
/*  46 */     return this.user;
/*     */   }
/*     */   
/*     */   public void setUser(User paramUser) {
/*  50 */     this.user = paramUser;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void setSettingMap(int paramInt, boolean paramBoolean) {
/*  59 */     if (this.settingMap != null) {
/*     */       return;
/*     */     }
/*  62 */     if (paramInt <= 0) {
/*  63 */       this.log.error("setSettingMap--参数错误,requestid:" + paramInt);
/*     */       
/*     */       return;
/*     */     } 
/*  67 */     this.settingMap = QYSWorkflowUtil.getRequestBaseInfoById(paramInt, this.user);
/*  68 */     this.settingMap.putAll(getSettingByWorkflowid(Util.getIntValue(getStringValue("workflowid"), 0)));
/*     */     
/*  70 */     int i = Util.getIntValue(getStringValue("setid"), 0);
/*  71 */     if (i > 0) {
/*  72 */       ArrayList arrayList1 = null;
/*  73 */       if (!paramBoolean) {
/*  74 */         arrayList1 = new ArrayList();
/*     */       }
/*  76 */       this.settingMap = (new QYSWorkflowUtil(this.user)).getRequestFieldInfo(this.settingMap, arrayList1);
/*     */     } 
/*     */     
/*  79 */     Map map1 = (this.settingMap == null) ? new HashMap<Object, Object>() : ((this.settingMap.get("fieldValueMap") == null) ? new HashMap<Object, Object>() : (Map)this.settingMap.get("fieldValueMap"));
/*  80 */     Map map2 = (this.settingMap == null) ? new HashMap<Object, Object>() : ((this.settingMap.get("fieldNameMap") == null) ? new HashMap<Object, Object>() : (Map)this.settingMap.get("fieldNameMap"));
/*  81 */     String str1 = "";
/*  82 */     String str2 = "";
/*  83 */     String str3 = getStringValue("documentField");
/*  84 */     String str4 = "";
/*  85 */     ArrayList arrayList = Util.TokenizerString(str3, ",");
/*  86 */     for (byte b = 0; b < arrayList.size(); b++) {
/*  87 */       String str5 = Util.null2String(arrayList.get(b));
/*  88 */       String str6 = Util.null2String(map1.get(Util.null2String(map2.get(str5)))).trim();
/*  89 */       if (!str6.isEmpty()) {
/*     */ 
/*     */         
/*  92 */         if (str4.equals("")) {
/*  93 */           str4 = str5;
/*     */         }
/*  95 */         if (str1.isEmpty()) {
/*  96 */           str1 = str1 + str6;
/*     */         } else {
/*  98 */           str1 = str1 + "," + str6;
/*     */         } 
/*     */       } 
/* 101 */     }  new QYSDocUtil(); str2 = QYSDocUtil.getImageFileidsBydocids(str1);
/*     */     
/* 103 */     this.settingMap.put("documnetDocIds", str1);
/* 104 */     this.settingMap.put("documentImageFileids", str2);
/* 105 */     this.settingMap.put("documentFirstFiled", str4);
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public Object getSettingValue(String paramString) {
/* 111 */     return this.settingMap.get(paramString);
/*     */   }
/*     */   
/*     */   public String getStringValue(String paramString) {
/* 115 */     return Util.null2String(getSettingValue(paramString));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String doSave(HttpServletRequest paramHttpServletRequest) {
/* 125 */     return doSave(QYSUtil.request2Map(paramHttpServletRequest), false);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String doSave(Map<String, Object> paramMap, boolean paramBoolean) {
/* 135 */     QYSResponse qYSResponse = new QYSResponse();
/* 136 */     if (paramMap == null) {
/* 137 */       qYSResponse.setMessage("保存失败，参数错误，params is null");
/* 138 */       this.log.error("doSave--" + qYSResponse.getMessage());
/* 139 */       return qYSResponse.toJSONString();
/*     */     } 
/*     */     
/* 142 */     String str1 = Util.null2String(paramMap.get("src"));
/* 143 */     int i = Util.getIntValue(Util.null2String(paramMap.get("id")));
/* 144 */     int j = Util.getIntValue(Util.null2String(paramMap.get("workflowid")));
/* 145 */     String str2 = Util.null2String(paramMap.get("description"));
/* 146 */     String str3 = Util.null2String(paramMap.get("isOpenConfig"));
/* 147 */     String str4 = Util.null2String(paramMap.get("documentField"));
/* 148 */     String str5 = Util.null2String(paramMap.get("useSealNode"));
/* 149 */     String str6 = Util.null2String(paramMap.get("isSaveNewVersion"));
/* 150 */     String str7 = Util.null2String(paramMap.get("isMustSign"));
/* 151 */     String str8 = Util.null2String(paramMap.get("isTransPdf"));
/* 152 */     String str9 = Util.null2String(paramMap.get("viewNode"));
/* 153 */     String str10 = Util.null2String(paramMap.get("saveDocumentField"));
/* 154 */     String str11 = Util.null2String(paramMap.get("synButtonSetting"));
/* 155 */     String str12 = "";
/* 156 */     if (str8.equals("1")) {
/* 157 */       str12 = Util.null2String(paramMap.get("transServer"));
/*     */     }
/* 159 */     String str13 = Util.null2String(paramMap.get("useSealBtnName"));
/* 160 */     String str14 = Util.null2String(paramMap.get("viewBtnName"));
/*     */     
/* 162 */     String str15 = Util.null2String(paramMap.get("mustSignNode"));
/* 163 */     str15 = filerNodes(str15, str5);
/* 164 */     String str16 = Util.null2String(paramMap.get("everySignNode"));
/* 165 */     str16 = filerNodes(str16, str15);
/*     */     
/* 167 */     if ("add".equals(str1)) {
/* 168 */       String str = "insert into wf_qys_singleSealSetting ( workflowid, description, documentField, useSealNode, isMustSign, isTransPdf, transServer, useSealBtnName, isSaveNewVersion, viewNode, viewBtnName, saveDocumentField ,mustSignNode,everySignNode,isOpenConfig) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
/*     */ 
/*     */       
/* 171 */       ConnStatement connStatement = new ConnStatement();
/*     */       try {
/* 173 */         connStatement.setStatementSql(str);
/* 174 */         connStatement.setInt(1, j);
/* 175 */         connStatement.setString(2, str2);
/* 176 */         connStatement.setString(3, str4);
/* 177 */         connStatement.setString(4, str5);
/* 178 */         connStatement.setString(5, str7);
/* 179 */         connStatement.setString(6, str8);
/* 180 */         connStatement.setString(7, str12);
/* 181 */         connStatement.setString(8, str13);
/* 182 */         connStatement.setString(9, str6);
/* 183 */         connStatement.setString(10, str9);
/* 184 */         connStatement.setString(11, str14);
/* 185 */         connStatement.setString(12, str10);
/* 186 */         connStatement.setString(13, str15);
/* 187 */         connStatement.setString(14, str16);
/* 188 */         connStatement.setString(15, str3);
/* 189 */         connStatement.executeUpdate();
/* 190 */       } catch (Exception exception) {
/* 191 */         this.log.error(exception);
/* 192 */         qYSResponse.setMessage(exception.getLocalizedMessage());
/* 193 */         return qYSResponse.toJSONString();
/*     */       } finally {
/* 195 */         connStatement.close();
/*     */       } 
/* 197 */     } else if ("edit".equals(str1)) {
/* 198 */       String str = "update wf_qys_singleSealSetting set  description=?, documentField=?, useSealNode=?, isMustSign=?, isTransPdf=?, transServer=?, useSealBtnName=?, isSaveNewVersion=?, viewNode=?, viewBtnName=?, saveDocumentField=?,mustSignNode=?,everySignNode=?,isOpenConfig=?  where id=?";
/*     */ 
/*     */       
/* 201 */       ConnStatement connStatement = new ConnStatement();
/*     */       try {
/* 203 */         connStatement.setStatementSql(str);
/* 204 */         connStatement.setString(1, str2);
/* 205 */         connStatement.setString(2, str4);
/* 206 */         connStatement.setString(3, str5);
/* 207 */         connStatement.setString(4, str7);
/* 208 */         connStatement.setString(5, str8);
/* 209 */         connStatement.setString(6, str12);
/* 210 */         connStatement.setString(7, str13);
/* 211 */         connStatement.setString(8, str6);
/* 212 */         connStatement.setString(9, str9);
/* 213 */         connStatement.setString(10, str14);
/* 214 */         connStatement.setString(11, str10);
/* 215 */         connStatement.setString(12, str15);
/* 216 */         connStatement.setString(13, str16);
/* 217 */         connStatement.setString(14, str3);
/* 218 */         connStatement.setInt(15, i);
/* 219 */         connStatement.executeUpdate();
/* 220 */       } catch (Exception exception) {
/* 221 */         this.log.error(exception);
/* 222 */         qYSResponse.setMessage(exception.getLocalizedMessage());
/* 223 */         return qYSResponse.toJSONString();
/*     */       } finally {
/* 225 */         connStatement.close();
/*     */       } 
/*     */     } 
/*     */     
/* 229 */     saveMustSignAction(j, str15, str16);
/* 230 */     syncElectronicSetting(j, str11);
/* 231 */     qYSResponse.setCodeAndMessage(0, "");
/* 232 */     qYSResponse.put("needSetJs", Boolean.valueOf(QYSWorkflowUtil.saveCustomPage(j, paramBoolean)));
/* 233 */     return qYSResponse.toJSONString();
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public void syncElectronicSetting(int paramInt, String paramString) {
/* 239 */     if (paramInt <= 0) {
/*     */       return;
/*     */     }
/* 242 */     String str1 = "";
/* 243 */     String str2 = "";
/* 244 */     RecordSet recordSet = new RecordSet();
/* 245 */     recordSet.executeQuery("select * from wf_qys_singleSealSetting where workflowid=?", new Object[] { Integer.valueOf(paramInt) });
/* 246 */     if (recordSet.next()) {
/* 247 */       str1 = Util.null2String(recordSet.getString("useSealBtnName"));
/* 248 */       str2 = Util.null2String(recordSet.getString("viewBtnName"));
/*     */     } 
/* 250 */     if ("1".equals(paramString)) {
/* 251 */       recordSet.executeQuery("select *  from wf_qys_singleSealSetting ", new Object[0]);
/* 252 */       RecordSet recordSet1 = new RecordSet();
/* 253 */       while (recordSet.next()) {
/* 254 */         recordSet1.executeUpdate("update wf_qys_singleSealSetting set useSealBtnName=?,viewBtnName=? where workflowid=?", new Object[] { str1, str2, Util.null2String(recordSet.getString("workflowId")) });
/*     */       } 
/*     */     } 
/*     */   }
/*     */   
/*     */   private String filerNodes(String paramString1, String paramString2) {
/* 260 */     StringBuffer stringBuffer = new StringBuffer();
/* 261 */     List list1 = Util.splitString2List(paramString1, ",");
/* 262 */     List list2 = Util.splitString2List(paramString2, ",");
/* 263 */     for (String str : list1) {
/* 264 */       if (list2.contains(str)) {
/* 265 */         if (stringBuffer.length() > 0) {
/* 266 */           stringBuffer.append(",");
/*     */         }
/* 268 */         stringBuffer.append(str);
/*     */       } 
/*     */     } 
/* 271 */     return stringBuffer.toString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String doDelete(HttpServletRequest paramHttpServletRequest) {
/* 279 */     return doDelete(QYSUtil.request2Map(paramHttpServletRequest));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String doDelete(Map paramMap) {
/* 288 */     QYSResponse qYSResponse = new QYSResponse();
/* 289 */     if (paramMap == null) {
/* 290 */       qYSResponse.setMessage("删除失败，参数错误，params is null");
/* 291 */       return qYSResponse.toJSONString();
/*     */     } 
/*     */     
/* 294 */     WorkflowVersion workflowVersion = new WorkflowVersion();
/* 295 */     RecordSet recordSet = new RecordSet();
/* 296 */     ArrayList arrayList = Util.TokenizerString(Util.null2String(paramMap.get("id")), ",");
/* 297 */     if (arrayList == null || arrayList.size() <= 0) {
/* 298 */       qYSResponse.setMessage("参数无效，id为空");
/* 299 */       this.log.error("doDelete--" + qYSResponse.getMessage());
/* 300 */       return qYSResponse.toJSONString();
/*     */     } 
/*     */     
/* 303 */     String str = "";
/* 304 */     for (byte b = 0; b < arrayList.size(); b++) {
/* 305 */       int i = Util.getIntValue(Util.null2String(arrayList.get(b)));
/* 306 */       if (i > 0) {
/*     */ 
/*     */         
/* 309 */         int j = 0;
/* 310 */         recordSet.executeQuery("select * from wf_qys_singleSealSetting where id=?", new Object[] { Integer.valueOf(i) });
/* 311 */         if (recordSet.next()) {
/* 312 */           j = Util.getIntValue(recordSet.getString("workflowid"));
/*     */         }
/* 314 */         if (j > 0) {
/*     */ 
/*     */           
/* 317 */           workflowVersion.setWorkflowId(Util.null2String(Integer.valueOf(j)));
/* 318 */           String str1 = workflowVersion.getAllVersionString();
/* 319 */           str = "delete from wf_qys_singleSealSetting where " + Util.getSubINClause(str1, "workflowid", "in");
/* 320 */           boolean bool = recordSet.executeUpdate(str, new Object[0]);
/*     */           
/* 322 */           List list = Util.splitString2List(str1, ",");
/* 323 */           for (String str2 : list) {
/* 324 */             int k = Util.getIntValue(str2, -1);
/* 325 */             if (k > 0)
/* 326 */               saveMustSignAction(k, "", ""); 
/*     */           } 
/*     */         } 
/*     */       } 
/* 330 */     }  qYSResponse.setCodeAndMessage(0, "");
/* 331 */     return qYSResponse.toJSONString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static Map getSettingByWorkflowid(int paramInt) {
/* 342 */     return (paramInt <= 0) ? new HashMap<Object, Object>() : getSettingByIdOrWorkflowid(0, paramInt);
/*     */   }
/*     */   
/*     */   public static Map getSettingById(int paramInt) {
/* 346 */     return (paramInt <= 0) ? new HashMap<Object, Object>() : getSettingByIdOrWorkflowid(paramInt, 0);
/*     */   }
/*     */ 
/*     */   
/*     */   private static Map getSettingByIdOrWorkflowid(int paramInt1, int paramInt2) {
/* 351 */     HashMap<Object, Object> hashMap = new HashMap<Object, Object>();
/* 352 */     String str = "select * from wf_qys_singleSealSetting where id=? or workflowid =? order by id";
/* 353 */     RecordSet recordSet = new RecordSet();
/* 354 */     recordSet.executeQuery(str, new Object[] { Integer.valueOf(paramInt1), Integer.valueOf(paramInt2) });
/* 355 */     if (recordSet.next()) {
/* 356 */       hashMap.put("setid", Integer.valueOf(Util.getIntValue(recordSet.getString("id"), 0)));
/* 357 */       hashMap.put("workflowid", Integer.valueOf(Util.getIntValue(recordSet.getString("workflowid"), 0)));
/* 358 */       hashMap.put("description", Util.null2String(recordSet.getString("description")));
/* 359 */       hashMap.put("isOpenConfig", Util.null2String(recordSet.getString("isOpenConfig")));
/* 360 */       hashMap.put("documentField", Util.null2String(recordSet.getString("documentField")));
/* 361 */       hashMap.put("useSealNode", Util.null2String(recordSet.getString("useSealNode")));
/* 362 */       hashMap.put("isSaveNewVersion", Util.null2String(recordSet.getString("isSaveNewVersion")));
/* 363 */       hashMap.put("isMustSign", Util.null2String(recordSet.getString("isMustSign")));
/* 364 */       hashMap.put("isTransPdf", Util.null2String(recordSet.getString("isTransPdf")));
/* 365 */       hashMap.put("transServer", Util.null2String(recordSet.getString("transServer")));
/* 366 */       hashMap.put("viewNode", Util.null2String(recordSet.getString("viewNode")));
/* 367 */       hashMap.put("saveDocumentField", Util.null2String(recordSet.getString("saveDocumentField")));
/* 368 */       hashMap.put("useSealBtnName", Util.null2String(recordSet.getString("useSealBtnName")));
/* 369 */       hashMap.put("viewBtnName", Util.null2String(recordSet.getString("viewBtnName")));
/* 370 */       hashMap.put("mustSignNode", Util.null2String(recordSet.getString("mustSignNode")));
/* 371 */       hashMap.put("everySignNode", Util.null2String(recordSet.getString("everySignNode")));
/*     */     } 
/* 373 */     return hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String initSingleSealButton(int paramInt1, int paramInt2) {
/* 383 */     QYSResponse qYSResponse = new QYSResponse();
/* 384 */     if (paramInt1 <= 0 || paramInt2 <= 0) {
/* 385 */       qYSResponse.setMessage("参数错误：流程Id：" + paramInt1 + "，节点Id：" + paramInt2);
/* 386 */       this.log.error("initSingleSealButton--" + qYSResponse.getMessage());
/* 387 */       return qYSResponse.toJSONString();
/*     */     } 
/* 389 */     RecordSet recordSet = new RecordSet();
/*     */     
/* 391 */     qYSResponse = new QYSResponse((new QYSSingleSealInterface(this.user)).addNewVersionSettingByRequestId(paramInt1));
/* 392 */     if (qYSResponse.getCode() != 0) {
/* 393 */       this.log.error("initSingleSealButton--" + qYSResponse.getMessage());
/* 394 */       return qYSResponse.toJSONString();
/*     */     } 
/*     */     
/* 397 */     setSettingMap(paramInt1, true);
/* 398 */     String str1 = QYSUtil.formatMultiLang(getStringValue("useSealBtnName"), this.user.getLanguage());
/* 399 */     if (str1.equals("")) {
/* 400 */       str1 = SystemEnv.getHtmlLabelName(518633, this.user.getLanguage());
/*     */     }
/* 402 */     String str2 = QYSUtil.formatMultiLang(getStringValue("viewBtnName"), this.user.getLanguage());
/* 403 */     if (str2.equals("")) {
/* 404 */       str2 = SystemEnv.getHtmlLabelName(387479, this.user.getLanguage());
/*     */     }
/*     */     
/* 407 */     if ("0".equals(getStringValue("isOpenConfig"))) {
/* 408 */       qYSResponse.setCode(0);
/* 409 */       return qYSResponse.toJSONString();
/*     */     } 
/*     */     
/* 412 */     String str3 = getStringValue("documentField");
/* 413 */     String str4 = getStringValue("documnetDocIds");
/* 414 */     String str5 = getStringValue("documentFirstFiled");
/* 415 */     String str6 = "";
/* 416 */     String str7 = "";
/* 417 */     Map map1 = (this.settingMap.get("fieldHtmlTypeMap") == null) ? new HashMap<Object, Object>() : (Map)this.settingMap.get("fieldHtmlTypeMap");
/* 418 */     Map map2 = (this.settingMap.get("fieldTypeMap") == null) ? new HashMap<Object, Object>() : (Map)this.settingMap.get("fieldTypeMap");
/* 419 */     ArrayList arrayList1 = Util.TokenizerString(str3, ",");
/* 420 */     for (byte b = 0; b < arrayList1.size(); b++) {
/* 421 */       String str = Util.null2String(arrayList1.get(b));
/* 422 */       if (!str.isEmpty()) {
/*     */ 
/*     */         
/* 425 */         String str10 = Util.null2String(map1.get(str));
/* 426 */         String str11 = Util.null2String(map2.get(str));
/* 427 */         if (str10.equals("6") && str11.equals("1")) {
/* 428 */           str6 = str6 + (str6.equals("") ? str : ("," + str));
/*     */         }
/* 430 */         if (str10.equals("3") && (str11.equals("9") || str11.equals("37"))) {
/* 431 */           str7 = str7 + (str7.equals("") ? str : ("," + str));
/*     */         }
/*     */       } 
/*     */     } 
/*     */     
/* 436 */     String str8 = getStringValue("saveDocumentField");
/* 437 */     if (!str8.isEmpty()) {
/* 438 */       String str10 = Util.null2String(map1.get(str8));
/* 439 */       String str11 = Util.null2String(map2.get(str8));
/* 440 */       if (str10.equals("6") && str11.equals("1")) {
/* 441 */         str6 = str8;
/*     */       }
/* 443 */       if (str10.equals("3") && (str11.equals("9") || str11.equals("37"))) {
/* 444 */         str7 = str8;
/*     */       }
/*     */     } 
/*     */     
/* 448 */     ArrayList arrayList2 = Util.TokenizerString(getStringValue("useSealNode"), ",");
/* 449 */     boolean bool1 = arrayList2.contains(Util.null2String(Integer.valueOf(paramInt2)));
/*     */     
/* 451 */     ArrayList arrayList3 = Util.TokenizerString(getStringValue("viewNode"), ",");
/* 452 */     boolean bool2 = arrayList3.contains(Util.null2String(Integer.valueOf(paramInt2)));
/*     */ 
/*     */     
/* 455 */     ArrayList arrayList4 = Util.TokenizerString(getStringValue("mustSignNode"), ",");
/* 456 */     boolean bool3 = (bool1 && arrayList4.contains(Util.null2String(Integer.valueOf(paramInt2)))) ? true : false;
/*     */     
/* 458 */     ArrayList arrayList5 = Util.TokenizerString(getStringValue("everySignNode"), ",");
/* 459 */     boolean bool4 = (bool3 && arrayList5.contains(Util.null2String(Integer.valueOf(paramInt2)))) ? true : false;
/*     */     
/* 461 */     String str9 = "";
/* 462 */     recordSet.executeQuery("select isremark from workflow_currentoperator where requestid=? and userid=? and nodeid=? and islasttimes='1'", new Object[] { Integer.valueOf(paramInt1), Integer.valueOf(this.user.getUID()), Integer.valueOf(paramInt2) });
/* 463 */     if (recordSet.next()) {
/* 464 */       str9 = recordSet.getString("isremark");
/*     */     }
/*     */     
/* 467 */     qYSResponse.put("useSealBtnName", str1);
/* 468 */     qYSResponse.put("viewBtnName", str2);
/* 469 */     qYSResponse.put("documentField", str3);
/* 470 */     qYSResponse.put("isUseSeal", Boolean.valueOf(bool1));
/* 471 */     qYSResponse.put("isView", Boolean.valueOf(bool2));
/* 472 */     qYSResponse.put("documnetDocIds", str4);
/* 473 */     qYSResponse.put("documentFirstFiled", str5);
/* 474 */     qYSResponse.put("isremark", str9);
/* 475 */     qYSResponse.put("imageFileFields", str6);
/* 476 */     qYSResponse.put("docFields", str7);
/* 477 */     qYSResponse.put("saveDocumentField", str8);
/* 478 */     qYSResponse.put("isMustSign", Boolean.valueOf(bool3));
/* 479 */     qYSResponse.put("isEverySign", Boolean.valueOf(bool4));
/*     */     
/* 481 */     qYSResponse.setCodeAndMessage(0, "");
/* 482 */     return qYSResponse.toJSONString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String addNewVersionSettingByRequestId(int paramInt) {
/* 491 */     QYSResponse qYSResponse = new QYSResponse();
/* 492 */     if (paramInt <= 0) {
/* 493 */       qYSResponse.setMessage("参数错误，requestid:" + paramInt);
/* 494 */       return qYSResponse.toJSONString();
/*     */     } 
/* 496 */     int i = 0;
/* 497 */     RecordSet recordSet = new RecordSet();
/* 498 */     recordSet.executeQuery("select workflowid from workflow_requestbase where requestid=?", new Object[] { Integer.valueOf(paramInt) });
/* 499 */     if (recordSet.next()) {
/* 500 */       i = Util.getIntValue(recordSet.getString("workflowid"));
/*     */     }
/* 502 */     return addNewVersionSettingByWorkflowId(i);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String addNewVersionSettingByWorkflowId(int paramInt) {
/* 513 */     QYSResponse qYSResponse = new QYSResponse();
/* 514 */     ConnStatement connStatement = new ConnStatement();
/*     */     try {
/* 516 */       addNewVersionSettingByWorkflowId.lock();
/* 517 */       if (paramInt <= 0) {
/* 518 */         qYSResponse.setMessage("参数错误，workflowid:" + paramInt);
/* 519 */         this.log.error("addNewVersionSettingByWorkflowId--" + qYSResponse.getMessage());
/* 520 */         return qYSResponse.toJSONString();
/*     */       } 
/*     */       
/* 523 */       boolean bool = false;
/* 524 */       RecordSet recordSet = new RecordSet();
/* 525 */       String str1 = "select * from wf_qys_singleSealSetting where workflowid=?";
/* 526 */       recordSet.executeQuery(str1, new Object[] { Integer.valueOf(paramInt) });
/* 527 */       if (recordSet.next()) {
/* 528 */         bool = true;
/*     */       }
/* 530 */       if (bool) {
/* 531 */         qYSResponse.setCodeAndMessage(0, "");
/* 532 */         return qYSResponse.toJSONString();
/*     */       } 
/*     */ 
/*     */       
/* 536 */       int i = -1;
/* 537 */       WorkflowVersion workflowVersion = new WorkflowVersion(Util.null2String(Integer.valueOf(paramInt)));
/* 538 */       String str2 = workflowVersion.getAllVersionString();
/* 539 */       String str3 = workflowVersion.getActiveVersionWFID();
/* 540 */       str1 = "select * from wf_qys_singleSealSetting";
/* 541 */       str1 = str1 + " where " + Util.getSubINClause(str2, "workflowid", "in");
/* 542 */       str1 = str1 + " order by id asc";
/* 543 */       recordSet.executeQuery(str1, new Object[0]);
/* 544 */       while (recordSet.next()) {
/* 545 */         i = Util.getIntValue(Util.null2String(recordSet.getString("workflowid")));
/* 546 */         if (i == Util.getIntValue(str3)) {
/*     */           break;
/*     */         }
/*     */       } 
/* 550 */       if (i <= 0) {
/* 551 */         qYSResponse.setCodeAndMessage(0, "");
/* 552 */         return qYSResponse.toJSONString();
/*     */       } 
/* 554 */       this.log.info("addNewVersionSettingByWorkflowId--start--workflowid:" + paramInt + "--hasSettedWfid:" + i);
/* 555 */       str1 = "select * from workflow_flownode where workflowid=?";
/* 556 */       recordSet.executeQuery(str1, new Object[] { Integer.valueOf(i) });
/* 557 */       HashMap<Object, Object> hashMap = new HashMap<Object, Object>();
/* 558 */       while (recordSet.next()) {
/* 559 */         String str = Util.null2String(recordSet.getString("nodeid"));
/* 560 */         int j = QYSWorkflowUtil.getOldNodeidByNodes(str);
/* 561 */         int k = QYSWorkflowUtil.getNodeidByWfidAndOldnodeid(paramInt, j);
/* 562 */         hashMap.put(str, Util.null2String(Integer.valueOf(k)));
/*     */       } 
/*     */       
/* 565 */       Map map = getSettingByWorkflowid(i);
/* 566 */       String str4 = Util.null2String(map.get("description"));
/* 567 */       String str5 = Util.null2String(map.get("isOpenConfig"));
/* 568 */       String str6 = Util.null2String(map.get("documentField"));
/* 569 */       String str7 = Util.null2String(map.get("isSaveNewVersion"));
/* 570 */       String str8 = Util.null2String(map.get("isMustSign"));
/* 571 */       String str9 = Util.null2String(map.get("isTransPdf"));
/* 572 */       String str10 = Util.null2String(map.get("transServer"));
/* 573 */       String str11 = Util.null2String(map.get("useSealBtnName"));
/* 574 */       String str12 = Util.null2String(map.get("viewBtnName"));
/* 575 */       String str13 = Util.null2String(map.get("useSealNode"));
/* 576 */       String str14 = Util.null2String(map.get("viewNode"));
/* 577 */       String str15 = Util.null2String(map.get("mustSignNode"));
/* 578 */       String str16 = Util.null2String(map.get("everySignNode"));
/* 579 */       String str17 = Util.null2String(map.get("saveDocumentField"));
/* 580 */       String str18 = "";
/* 581 */       String str19 = "";
/* 582 */       String str20 = "";
/* 583 */       String str21 = "";
/* 584 */       String str22 = "";
/* 585 */       ArrayList arrayList1 = Util.TokenizerString(str13, ",");
/* 586 */       ArrayList arrayList2 = Util.TokenizerString(str14, ",");
/* 587 */       ArrayList arrayList3 = Util.TokenizerString(str15, ",");
/* 588 */       ArrayList arrayList4 = Util.TokenizerString(str16, ",");
/* 589 */       this.log.info("addNewVersionSettingByWorkflowId--before--userSealNode:" + str13 + "--viewNode:" + str14 + "--mustSignNode:" + str15 + "--everySignNode:" + str16); byte b;
/* 590 */       for (b = 0; b < arrayList1.size(); b++) {
/* 591 */         String str23 = Util.null2String(arrayList1.get(b));
/* 592 */         String str24 = Util.null2String((String)hashMap.get(str23));
/* 593 */         if (Util.getIntValue(str24) > 0) {
/* 594 */           str18 = str18 + (str18.equals("") ? str24 : ("," + str24));
/*     */         }
/* 596 */         str22 = str22 + (str22.equals("") ? str24 : ("," + str24));
/*     */       } 
/* 598 */       for (b = 0; b < arrayList2.size(); b++) {
/* 599 */         String str23 = Util.null2String(arrayList2.get(b));
/* 600 */         String str24 = Util.null2String((String)hashMap.get(str23));
/* 601 */         if (Util.getIntValue(str24) > 0) {
/* 602 */           str19 = str19 + (str19.equals("") ? str24 : ("," + str24));
/*     */         }
/*     */       } 
/*     */       
/* 606 */       for (b = 0; b < arrayList3.size(); b++) {
/* 607 */         String str23 = Util.null2String(arrayList3.get(b));
/* 608 */         String str24 = Util.null2String((String)hashMap.get(str23));
/* 609 */         if (Util.getIntValue(str24) > 0) {
/* 610 */           str20 = str20 + (str20.equals("") ? str24 : ("," + str24));
/*     */         }
/*     */       } 
/* 613 */       for (b = 0; b < arrayList4.size(); b++) {
/* 614 */         String str23 = Util.null2String(arrayList4.get(b));
/* 615 */         String str24 = Util.null2String((String)hashMap.get(str23));
/* 616 */         if (Util.getIntValue(str24) > 0) {
/* 617 */           str21 = str21 + (str21.equals("") ? str24 : ("," + str24));
/*     */         }
/*     */       } 
/* 620 */       this.log.info("addNewVersionSettingByWorkflowId--after--userSealNodeNew:" + str18 + "--viewNodeNew:" + str19 + "--mustSignNodeNew:" + str20 + "--everySignNodeNew:" + str21);
/*     */ 
/*     */       
/* 623 */       str1 = "insert into wf_qys_singleSealSetting(workflowid, description, documentField, useSealNode, isMustSign, isTransPdf, transServer, useSealBtnName, isSaveNewVersion, viewNode, viewBtnName, saveDocumentField,mustSignNode,everySignNode,isOpenConfig) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 628 */       connStatement.setStatementSql(str1);
/* 629 */       connStatement.setInt(1, paramInt);
/* 630 */       connStatement.setString(2, str4);
/* 631 */       connStatement.setString(3, str6);
/* 632 */       connStatement.setString(4, str18);
/* 633 */       connStatement.setString(5, str8);
/* 634 */       connStatement.setString(6, str9);
/* 635 */       connStatement.setString(7, str10);
/* 636 */       connStatement.setString(8, str11);
/* 637 */       connStatement.setString(9, str7);
/* 638 */       connStatement.setString(10, str19);
/* 639 */       connStatement.setString(11, str12);
/* 640 */       connStatement.setString(12, str17);
/* 641 */       connStatement.setString(13, str20);
/* 642 */       connStatement.setString(14, str21);
/* 643 */       connStatement.setString(15, str5);
/* 644 */       connStatement.executeUpdate();
/*     */       
/* 646 */       QYSWorkflowUtil.saveCustomPage(paramInt);
/* 647 */       saveMustSignAction(paramInt, str20, str21);
/* 648 */     } catch (Exception exception) {
/* 649 */       qYSResponse.setMessage("复制单体用印多版本配置信息失败， workflowid:" + paramInt);
/* 650 */       this.log.error("addNewVersionSettingByWorkflowId--" + qYSResponse.getMessage());
/* 651 */       this.log.error(exception);
/* 652 */       return qYSResponse.toJSONString();
/*     */     } finally {
/* 654 */       connStatement.close();
/* 655 */       addNewVersionSettingByWorkflowId.unlock();
/*     */     } 
/* 657 */     qYSResponse.setCodeAndMessage(0, "");
/* 658 */     return qYSResponse.toJSONString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void saveMustSignAction(int paramInt, String paramString1, String paramString2) {
/* 669 */     boolean bool1 = true;
/* 670 */     String str = "0";
/* 671 */     boolean bool2 = true;
/*     */     
/* 673 */     List<Integer> list1 = getSaveMustSignActionNodes(paramString1, paramString2);
/* 674 */     RecordSet recordSet = new RecordSet();
/* 675 */     List<Integer> list2 = getHasSaveMustSignActionNodes(paramInt, recordSet);
/*     */     
/* 677 */     ArrayList<Integer> arrayList1 = new ArrayList();
/* 678 */     for (Iterator<Integer> iterator1 = list2.iterator(); iterator1.hasNext(); ) { int j = ((Integer)iterator1.next()).intValue();
/* 679 */       if (!list1.contains(Integer.valueOf(j))) {
/* 680 */         arrayList1.add(Integer.valueOf(j));
/*     */       } }
/*     */     
/* 683 */     ArrayList<Integer> arrayList2 = new ArrayList();
/* 684 */     for (Iterator<Integer> iterator2 = list1.iterator(); iterator2.hasNext(); ) { int j = ((Integer)iterator2.next()).intValue();
/* 685 */       if (!list2.contains(Integer.valueOf(j))) {
/* 686 */         arrayList2.add(Integer.valueOf(j));
/*     */       } }
/*     */ 
/*     */     
/* 690 */     int i = getMustSignActionId(recordSet); Iterator<Integer> iterator3;
/* 691 */     for (iterator3 = arrayList2.iterator(); iterator3.hasNext(); ) { int j = ((Integer)iterator3.next()).intValue();
/* 692 */       saveAction(paramInt, j, bool1, str, String.valueOf(i), bool2, 1); }
/*     */ 
/*     */     
/* 695 */     for (iterator3 = arrayList1.iterator(); iterator3.hasNext(); ) { int j = ((Integer)iterator3.next()).intValue();
/* 696 */       saveAction(paramInt, j, bool1, str, "", bool2, 0); }
/*     */   
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private List<Integer> getSaveMustSignActionNodes(String paramString1, String paramString2) {
/* 707 */     List list1 = Util.splitString2List(paramString1, ",");
/* 708 */     List list2 = Util.splitString2List(paramString2, ",");
/*     */     
/* 710 */     ArrayList<Integer> arrayList = new ArrayList();
/* 711 */     for (String str : list1) {
/* 712 */       if (!list2.contains(str)) {
/* 713 */         int i = Util.getIntValue(str, 0);
/* 714 */         if (i > 0) {
/* 715 */           arrayList.add(Integer.valueOf(i));
/*     */         }
/*     */       } 
/*     */     } 
/* 719 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private int getMustSignActionId(RecordSet paramRecordSet) {
/* 728 */     paramRecordSet.executeQuery("select id from qysSingleSealAction where actionName = 'MustSignAction'", new Object[0]);
/* 729 */     if (paramRecordSet.next()) {
/* 730 */       return paramRecordSet.getInt("id");
/*     */     }
/*     */     
/* 733 */     return 0;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private List<Integer> getHasSaveMustSignActionNodes(int paramInt, RecordSet paramRecordSet) {
/* 744 */     ArrayList<Integer> arrayList = new ArrayList();
/* 745 */     paramRecordSet.executeQuery("select id,isused,actionIds,objId from wf_qysSingleSealActionSet where workflowid = ? and objType = ? and isPre = ? and sourceid = ?", new Object[] { Integer.valueOf(paramInt), Integer.valueOf(1), "0", Integer.valueOf(1) });
/* 746 */     while (paramRecordSet.next()) {
/* 747 */       arrayList.add(Integer.valueOf(paramRecordSet.getInt("objId")));
/*     */     }
/* 749 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void saveAction(int paramInt1, int paramInt2, int paramInt3, String paramString1, String paramString2, int paramInt4, int paramInt5) {
/* 763 */     RecordSet recordSet = new RecordSet();
/*     */     
/* 765 */     if ("".equals(paramString2)) {
/* 766 */       recordSet.executeUpdate("delete from wf_qysSingleSealActionSet where workflowid = ? and objId = ? and objType = ? and isPre = ? and sourceid = ?", new Object[] { Integer.valueOf(paramInt1), Integer.valueOf(paramInt2), Integer.valueOf(paramInt3), paramString1, Integer.valueOf(paramInt4) });
/*     */     } else {
/* 768 */       String str; recordSet.executeQuery("select id,isused,actionIds from wf_qysSingleSealActionSet where workflowid = ? and objId = ? and objType = ? and isPre = ? and sourceid = ?", new Object[] { Integer.valueOf(paramInt1), Integer.valueOf(paramInt2), Integer.valueOf(paramInt3), paramString1, Integer.valueOf(paramInt4) });
/*     */       
/* 770 */       if (recordSet.next()) {
/* 771 */         str = "update wf_qysSingleSealActionSet set actionIds = ?,isused = ?  where workflowid = ? and objId = ? and objType = ? and isPre = ? and sourceid = ?";
/*     */       } else {
/* 773 */         str = "insert into wf_qysSingleSealActionSet(actionIds,isused,workflowId, objId, objType, isPre, sourceid) values(?,?,?,?,?,?,?) ";
/*     */       } 
/*     */       
/* 776 */       ConnStatement connStatement = new ConnStatement();
/*     */       try {
/* 778 */         connStatement.setStatementSql(str);
/* 779 */         connStatement.setString(1, paramString2);
/* 780 */         connStatement.setInt(2, paramInt5);
/* 781 */         connStatement.setInt(3, paramInt1);
/* 782 */         connStatement.setInt(4, paramInt2);
/* 783 */         connStatement.setInt(5, paramInt3);
/* 784 */         connStatement.setString(6, paramString1);
/* 785 */         connStatement.setInt(7, paramInt4);
/* 786 */         connStatement.executeUpdate();
/* 787 */       } catch (Exception exception) {
/* 788 */         this.log.error(exception);
/*     */       } finally {
/* 790 */         connStatement.close();
/*     */       } 
/*     */     } 
/*     */ 
/*     */     
/* 795 */     boolean bool1 = true;
/*     */     
/* 797 */     boolean bool2 = false;
/* 798 */     recordSet.executeQuery("select id,isused,actionIds from wf_qysSingleSealActionSet where workflowid = ? and objId = ? and objType = ? and isPre = ?", new Object[] { Integer.valueOf(paramInt1), Integer.valueOf(paramInt2), Integer.valueOf(paramInt3), paramString1 });
/* 799 */     while (recordSet.next()) {
/* 800 */       bool1 = false;
/* 801 */       if (recordSet.getInt("isused") == 1) {
/* 802 */         bool2 = true;
/*     */       }
/*     */     } 
/* 805 */     setWorkflowAction(recordSet, paramInt1, paramInt2, paramInt3, paramString1, bool1, bool2);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String setWorkflowAction(RecordSet paramRecordSet, int paramInt1, int paramInt2, int paramInt3, String paramString, boolean paramBoolean, int paramInt4) {
/* 821 */     QYSResponse qYSResponse = new QYSResponse();
/* 822 */     if (paramInt1 <= 0) {
/* 823 */       qYSResponse.setMessage("参数错误，workflowid:" + paramInt1);
/* 824 */       this.log.error("setWorkflowAction--" + qYSResponse.getMessage());
/* 825 */       return qYSResponse.toJSONString();
/*     */     } 
/* 827 */     int i = 0;
/* 828 */     int j = 0;
/* 829 */     String str = "";
/* 830 */     if (0 == paramInt3) {
/* 831 */       j = paramInt2;
/* 832 */       str = "select * from workflowactionset where interfaceid = 'QYSSingleSignAction' and interfacetype = 3 and workflowid=?  and nodelinkid=? order by isused desc, actionorder, id ";
/*     */       
/* 834 */       paramRecordSet.executeQuery(str, new Object[] { Integer.valueOf(paramInt1), Integer.valueOf(j) });
/* 835 */     } else if (1 == paramInt3) {
/* 836 */       i = paramInt2;
/* 837 */       str = "select * from workflowactionset where interfaceid = 'QYSSingleSignAction' and interfacetype = 3 and workflowid=?  and nodeid=? and ispreoperator=? order by isused desc, actionorder, id ";
/*     */       
/* 839 */       paramRecordSet.executeQuery(str, new Object[] { Integer.valueOf(paramInt1), Integer.valueOf(i), Integer.valueOf(Util.getIntValue(paramString)) });
/*     */     } 
/* 841 */     if (paramRecordSet.getCounts() > 0) {
/* 842 */       int k = 0;
/* 843 */       String str1 = "";
/* 844 */       while (paramRecordSet.next()) {
/* 845 */         int m = Util.getIntValue(paramRecordSet.getString("id"), 0);
/* 846 */         str1 = str1 + "," + m;
/* 847 */         if (k) {
/* 848 */           k = m;
/*     */         }
/*     */       } 
/* 851 */       while (str1.startsWith(",")) {
/* 852 */         str1 = str1.substring(1);
/*     */       }
/* 854 */       if ("".equals(str1)) {
/* 855 */         str1 = "0";
/*     */       }
/*     */       
/* 858 */       if (paramBoolean == true) {
/* 859 */         ArrayList arrayList = Util.TokenizerString(str1, ",");
/* 860 */         for (byte b = 0; b < arrayList.size(); b++) {
/* 861 */           int m = Util.getIntValue(Util.null2String(arrayList.get(b)), 0);
/* 862 */           paramRecordSet.executeUpdate("delete from workflowactionset where id=?", new Object[] { Integer.valueOf(m) });
/*     */         } 
/*     */       } else {
/* 865 */         ArrayList arrayList = Util.TokenizerString(str1, ",");
/* 866 */         for (byte b = 0; b < arrayList.size(); b++) {
/* 867 */           int m = Util.getIntValue(Util.null2String(arrayList.get(b)), 0);
/* 868 */           paramRecordSet.executeUpdate("delete from workflowactionset where id=? and id != ?", new Object[] { Integer.valueOf(m), Integer.valueOf(k) });
/*     */         } 
/*     */         
/* 871 */         str = "update workflowactionset set isused=? where id=?";
/* 872 */         paramRecordSet.executeUpdate(str, new Object[] { Integer.valueOf(paramInt4), Integer.valueOf(k) });
/*     */       }
/*     */     
/* 875 */     } else if (!paramBoolean) {
/* 876 */       str = "insert into workflowactionset(workflowid, nodeid, nodelinkid, ispreoperator, interfaceid, interfacetype , actionname, actionorder, isused ) values(?,?,?,?,?,?,?,?,?) ";
/*     */ 
/*     */ 
/*     */       
/* 880 */       ConnStatement connStatement = new ConnStatement();
/*     */       try {
/* 882 */         connStatement.setStatementSql(str);
/* 883 */         connStatement.setInt(1, paramInt1);
/* 884 */         connStatement.setInt(2, i);
/* 885 */         connStatement.setInt(3, j);
/* 886 */         connStatement.setInt(4, Util.getIntValue(paramString));
/* 887 */         connStatement.setString(5, "QYSSingleSignAction");
/* 888 */         connStatement.setInt(6, 3);
/* 889 */         connStatement.setString(7, "QYSSingleSignAction");
/* 890 */         connStatement.setInt(8, 1);
/* 891 */         connStatement.setInt(9, paramInt4);
/* 892 */         connStatement.executeUpdate();
/* 893 */       } catch (Exception exception) {
/* 894 */         paramRecordSet.writeLog(exception);
/*     */       } finally {
/* 896 */         connStatement.close();
/*     */       } 
/*     */     } 
/*     */     
/* 900 */     BaseAction baseAction = new BaseAction();
/* 901 */     baseAction.checkActionOnNodeOrLink(paramInt1, i, j, Util.getIntValue(paramString), 0);
/* 902 */     qYSResponse.setCodeAndMessage(0, "");
/* 903 */     return qYSResponse.toJSONString();
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/qiyuesuo/singleSeal/QYSSingleSealInterface.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */