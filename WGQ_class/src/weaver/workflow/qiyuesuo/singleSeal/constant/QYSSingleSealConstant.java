package weaver.workflow.qiyuesuo.singleSeal.constant;

import weaver.workflow.qiyuesuo.constant.QYSConstant;

public class QYSSingleSealConstant extends QYSConstant {
  public static final String TABLE_SINGLESEAL_SETTING = "wf_qys_singleSealSetting";
  
  public static final String SINGLESEAL_PAGEID = "wf:qysSingleSealSetting";
  
  public static final String SINGLESEAL_PAGEUID = "uid:qysSingleSealSetting";
  
  public static final String TRANSPDF_YONGZHONG_KEY = "0";
  
  public static final String TRANSPDF_PROP_YONGZHONG = "doc_custom_for_weaver";
  
  public static final String YONGZHONG_PROP_CONVERT_CLIENT_OPEN = "convert_client_open";
  
  public static final String TRANSPDF_JINSHAN_KEY = "1";
  
  public static final String TRANSPDF_PROP_JINSHAN = "doc_wps_for_weaver";
  
  public static final String JINSHANG_PROP_WPS_VIEW_ONLINE = "wps_view_online";
  
  public static final String JINSHANG_PROP_WPS_DOC_CENTER = "doccenter_open";
}


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/qiyuesuo/singleSeal/constant/QYSSingleSealConstant.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */