/*     */ package weaver.workflow.qiyuesuo;
/*     */ 
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.conn.ConnStatement;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.TimeUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.workflow.qiyuesuo.bean.QYSOperateLog;
/*     */ import weaver.workflow.qiyuesuo.constant.QYSCategoryType;
/*     */ import weaver.workflow.qiyuesuo.constant.QYSLogType;
/*     */ import weaver.workflow.qiyuesuo.constant.QYSOperateType;
/*     */ import weaver.workflow.qiyuesuo.constant.QYSServerType;
/*     */ import weaver.workflow.qiyuesuo.service.QYSService;
/*     */ import weaver.workflow.qiyuesuo.service.impl.QYSServicePrivateImpl;
/*     */ import weaver.workflow.qiyuesuo.service.impl.QYSServicePublicImpl;
/*     */ import weaver.workflow.qiyuesuo.util.QYSLogUtil;
/*     */ 
/*     */ 
/*     */ public class QYSTemplateInterface
/*     */   extends BaseBean
/*     */ {
/*     */   private QYSLogUtil log;
/*     */   private int operateId;
/*     */   private QYSOperateLog operateLog;
/*     */   private QYSLogType logType;
/*     */   private User user;
/*     */   
/*     */   public void setOperateLog(QYSCategoryType paramQYSCategoryType, QYSOperateType paramQYSOperateType, int paramInt) {
/*  35 */     if (this.operateId <= 0) {
/*  36 */       this.operateId = Util.getIntValue(this.log.getOperateId(this.user, paramInt));
/*  37 */       this.operateLog = new QYSOperateLog(this.user, getLogType(), paramQYSCategoryType, paramQYSOperateType, paramInt, this.operateId);
/*     */     } else {
/*     */       return;
/*     */     } 
/*     */   }
/*     */   public void saveOperateLog(boolean paramBoolean) {
/*  43 */     this.operateLog.setSuccess(paramBoolean);
/*  44 */     this.log.saveOperateLog(this.operateLog);
/*     */   }
/*     */   public int getOperateId() {
/*  47 */     return this.operateId;
/*     */   }
/*     */   public void setOperateId(int paramInt) {
/*  50 */     this.operateId = paramInt;
/*     */   }
/*     */   public QYSOperateLog getOperateLog() {
/*  53 */     return this.operateLog;
/*     */   }
/*     */   public void setOperateLog(QYSOperateLog paramQYSOperateLog) {
/*  56 */     this.operateLog = paramQYSOperateLog;
/*     */   }
/*     */   public QYSLogType getLogType() {
/*  59 */     return this.logType;
/*     */   }
/*     */   public void setLogType(QYSLogType paramQYSLogType) {
/*  62 */     this.logType = paramQYSLogType;
/*     */   }
/*     */   public QYSTemplateInterface() {
/*  65 */     this.log = new QYSLogUtil();
/*     */   }
/*     */   public QYSTemplateInterface(User paramUser) {
/*  68 */     this();
/*  69 */     this.user = paramUser;
/*     */   }
/*     */   public QYSTemplateInterface(User paramUser, QYSLogType paramQYSLogType) {
/*  72 */     this(paramUser);
/*  73 */     this.logType = paramQYSLogType;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getTemplate() {
/*  80 */     setOperateLog(QYSCategoryType.OTHER, QYSOperateType.TEMPLATE, 0);
/*  81 */     String str1 = TimeUtil.getCurrentTimeString();
/*  82 */     this.log.info("--QYSTemplateInterface--getTemplate--start--currentDateTime:" + str1);
/*     */     
/*  84 */     String str2 = "";
/*  85 */     JSONObject jSONObject = new JSONObject();
/*  86 */     jSONObject.put("code", Integer.valueOf(1));
/*  87 */     jSONObject.put("message", str2);
/*     */     
/*  89 */     List<Map> list = getQYSServer();
/*  90 */     this.log.info("--QYSTemplateInterface--getTemplate--qysServers.size:" + list.size());
/*  91 */     if (list.isEmpty()) {
/*  92 */       str2 = "契约锁服务未配置";
/*  93 */       this.log.error("--QYSTemplateInterface--getTemplate--" + str2);
/*  94 */       jSONObject.put("message", str2);
/*  95 */       saveOperateLog(false);
/*  96 */       return jSONObject.toJSONString();
/*     */     } 
/*  98 */     String str3 = Util.null2String(getPropValue("QYS", "allPrivateServie"));
/*  99 */     boolean bool1 = false;
/* 100 */     if ("true".equals(str3)) {
/* 101 */       bool1 = true;
/*     */     }
/* 103 */     boolean bool2 = false;
/* 104 */     boolean bool3 = false;
/* 105 */     boolean bool4 = false;
/* 106 */     boolean bool5 = false;
/* 107 */     for (Map map : list) {
/* 108 */       if (map != null && "1".equals(map.get("isDefaultService")) && QYSServerType.PRIVATE.getType().equals(map.get("serverType"))) {
/*     */         
/* 110 */         bool4 = true;
/*     */         break;
/*     */       } 
/*     */     } 
/* 114 */     this.log.info("--QYSTemplateInterface--getTemplate-- existDefaultService = " + bool4);
/* 115 */     RecordSet recordSet1 = new RecordSet();
/* 116 */     ArrayList<String> arrayList = new ArrayList();
/* 117 */     for (byte b = 0; b < list.size(); b++) {
/* 118 */       HashMap hashMap = (list.get(b) == null) ? new HashMap<Object, Object>() : (HashMap)list.get(b);
/* 119 */       String str5 = Util.null2String(hashMap.get("serverType"));
/* 120 */       this.log.info("--QYSTemplateInterface--getTemplate--i:" + b + "--serverType:" + str5);
/* 121 */       boolean bool = "1".equals(hashMap.get("isDefaultService"));
/* 122 */       String str6 = (String)hashMap.get("id");
/* 123 */       if (bool1 || 
/* 124 */         !"1".equals(str5) || (
/* 125 */         bool4 ? (
/* 126 */         !bool || bool2) : 
/*     */ 
/*     */ 
/*     */         
/* 130 */         bool2)) {
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */         
/* 138 */         QYSService qYSService = getQYSService(hashMap);
/* 139 */         if (qYSService == null) {
/* 140 */           this.log.info("--QYSTemplateInterface--getTemplate--i:" + b + " ,serverId = " + str6 + " --获取契约锁服务失败！");
/*     */         }
/*     */         else {
/*     */           
/* 144 */           List<HashMap> list1 = qYSService.getTemplate(str6);
/* 145 */           if (list1 == null)
/* 146 */           { this.log.info("--QYSTemplateInterface--getTemplate--i:" + b + " ,serverId = " + str6 + " --获取合同模板失败！"); }
/*     */           else
/*     */           
/* 149 */           { bool5 = true;
/* 150 */             this.log.info("--QYSTemplateInterface--getTemplate--i:" + b + "--list.size():" + list1.size());
/*     */             
/* 152 */             ConnStatement connStatement = new ConnStatement();
/* 153 */             if ("1".equals(str5) || !bool3)
/*     */             {
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */               
/* 162 */               connStatement.close();
/*     */             }
/*     */ 
/*     */             
/* 166 */             String str7 = "insert into wf_qiyuesuoTemplate(templateId, cover, createDateTime, form, status, tenantId , title, updateDateTime, serverType, executeDateTime, templateType, word, groupId,serverId ) values(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
/*     */ 
/*     */             
/* 169 */             String str8 = " update wf_qiyuesuoTemplate set cover = ?, createDateTime = ?, form = ? , status = ? ,title=?,updateDateTime=?, executeDateTime=?,templateType=?,word=?,groupId=?,isDelete = 0,serverId=? where serverType = ? and templateid = ? and tenantId = ?";
/*     */ 
/*     */             
/* 172 */             String str9 = " select * from wf_qiyuesuoTemplate where serverType = ? and templateid = ? and tenantId = ? ";
/* 173 */             for (byte b1 = 0; b1 < list1.size(); b1++) {
/* 174 */               if (list1.get(b1) == null) {
/* 175 */                 this.log.info("--QYSTemplateInterface--getTemplate--j:" + b1 + "--记录为空--");
/*     */               }
/*     */               else {
/*     */                 
/* 179 */                 HashMap hashMap1 = list1.get(b1);
/* 180 */                 connStatement = new ConnStatement();
/*     */               } 
/*     */             } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */             
/* 227 */             this.log.info("--QYSTemplateInterface--getTemplate--i:" + b + "--end--");
/*     */             
/* 229 */             if ("1".equals(str5))
/* 230 */             { bool2 = true; }
/*     */             else
/* 232 */             { bool3 = true; }  } 
/*     */         } 
/*     */       } 
/* 235 */     }  recordSet1.executeQuery("select * from wf_qiyuesuoTemplate", new Object[0]);
/* 236 */     String str4 = " update wf_qiyuesuoTemplate set isDelete = 1 where serverType = ? and templateid = ? and tenantId = ?";
/* 237 */     RecordSet recordSet2 = new RecordSet();
/* 238 */     while (recordSet1.next()) {
/* 239 */       String str5 = Util.null2String(recordSet1.getString("templateId"));
/* 240 */       String str6 = Util.null2String(recordSet1.getString("tenantId"));
/* 241 */       String str7 = Util.null2String(recordSet1.getString("serverType"));
/* 242 */       if (!arrayList.contains(str7 + "_" + str5 + "_" + str6)) {
/* 243 */         recordSet2.executeUpdate(str4, new Object[] { str7, str5, str6 });
/*     */       }
/*     */     } 
/* 246 */     jSONObject.put("code", Integer.valueOf(0));
/* 247 */     jSONObject.put("message", "");
/* 248 */     this.log.info("--QYSTemplateInterface--getTemplate--end--");
/* 249 */     saveOperateLog(bool5);
/* 250 */     return jSONObject.toJSONString();
/*     */   }
/*     */   
/*     */   private List<Map> getQYSServer() {
/* 254 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 255 */     RecordSet recordSet = new RecordSet();
/* 256 */     recordSet.executeQuery("select * from wf_qiyuesuoServer order by id", new Object[0]);
/* 257 */     while (recordSet.next()) {
/* 258 */       HashMap<Object, Object> hashMap = new HashMap<Object, Object>();
/* 259 */       hashMap.put("id", recordSet.getString("id"));
/* 260 */       hashMap.put("serverName", recordSet.getString("serverName"));
/* 261 */       hashMap.put("serverUrl", recordSet.getString("serverUrl"));
/* 262 */       hashMap.put("accessKey", recordSet.getString("accessKey"));
/* 263 */       hashMap.put("accessSecret", recordSet.getString("accessSecret"));
/* 264 */       hashMap.put("isDefaultService", Util.null2String(recordSet.getString("isDefaultService")));
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 269 */       String str = recordSet.getString("serverType");
/* 270 */       if ("".equals(str)) {
/* 271 */         str = "1";
/*     */       }
/* 273 */       hashMap.put("serverType", str);
/* 274 */       arrayList.add(hashMap);
/*     */     } 
/* 276 */     return (List)arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private QYSService getQYSService(Map paramMap) {
/*     */     QYSServicePublicImpl qYSServicePublicImpl;
/* 285 */     QYSService qYSService = null;
/* 286 */     if (paramMap == null) {
/* 287 */       return qYSService;
/*     */     }
/*     */     
/* 290 */     String str1 = Util.null2String(paramMap.get("serverUrl"));
/* 291 */     String str2 = Util.null2String(paramMap.get("accessKey"));
/* 292 */     String str3 = Util.null2String(paramMap.get("accessSecret"));
/* 293 */     String str4 = Util.null2String(paramMap.get("serverType"));
/*     */     
/* 295 */     if ("".equals(str1) || "".equals(str2) || "".equals(str3)) {
/* 296 */       this.log.error("--QYSTemplateInterface--getQYSService--契约锁服务配置不正确--serverUrl:" + str1 + "--accessKey:" + str2 + "--accessSecret:" + str3);
/* 297 */       return qYSService;
/*     */     } 
/*     */     
/* 300 */     if ("1".equals(str4)) {
/* 301 */       QYSServicePrivateImpl qYSServicePrivateImpl = new QYSServicePrivateImpl(str1, str2, str3, this.user, 0, this.operateId);
/*     */     } else {
/* 303 */       qYSServicePublicImpl = new QYSServicePublicImpl(str1, str2, str3, this.user, 0, this.operateId);
/*     */     } 
/*     */     
/* 306 */     return (QYSService)qYSServicePublicImpl;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/qiyuesuo/QYSTemplateInterface.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */