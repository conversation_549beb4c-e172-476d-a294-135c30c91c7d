/*    */ package weaver.workflow.qiyuesuo;
/*    */ 
/*    */ import java.io.File;
/*    */ import java.util.ArrayList;
/*    */ import java.util.Calendar;
/*    */ import weaver.general.BaseBean;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.User;
/*    */ import weaver.workflow.qiyuesuo.bean.QYSOperateLog;
/*    */ import weaver.workflow.qiyuesuo.bean.QYSResponse;
/*    */ import weaver.workflow.qiyuesuo.constant.QYSCategoryType;
/*    */ import weaver.workflow.qiyuesuo.constant.QYSLogType;
/*    */ import weaver.workflow.qiyuesuo.constant.QYSOperateType;
/*    */ import weaver.workflow.qiyuesuo.util.QYSDocUtil;
/*    */ import weaver.workflow.qiyuesuo.util.QYSLogUtil;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class QYSClearTempDirInterface
/*    */   extends BaseBean
/*    */ {
/* 29 */   QYSLogUtil log = new QYSLogUtil(getClass().getName()); User user;
/*    */   private int operateId;
/*    */   
/*    */   public QYSLogType getLogType() {
/* 33 */     return (this.logType == null) ? QYSLogType.SCHEDULE : this.logType;
/*    */   } private QYSOperateLog operateLog; private QYSLogType logType;
/*    */   public QYSClearTempDirInterface() {}
/*    */   public QYSClearTempDirInterface(User paramUser) {
/* 37 */     this();
/* 38 */     this.user = paramUser;
/*    */   }
/*    */   
/*    */   public void setOperateLog(QYSCategoryType paramQYSCategoryType, QYSOperateType paramQYSOperateType, int paramInt) {
/* 42 */     if (this.operateId <= 0) {
/* 43 */       this.operateId = Util.getIntValue(this.log.getOperateId(this.user, paramInt));
/* 44 */       this.operateLog = new QYSOperateLog(this.user, getLogType(), paramQYSCategoryType, paramQYSOperateType, paramInt, this.operateId);
/*    */     } else {
/*    */       return;
/*    */     } 
/*    */   }
/*    */   
/*    */   public void saveOperateLog(boolean paramBoolean) {
/* 51 */     this.operateLog.setSuccess(paramBoolean);
/* 52 */     this.log.saveOperateLog(this.operateLog);
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String clearTempDir() {
/* 61 */     setOperateLog(QYSCategoryType.OTHER, QYSOperateType.CLEAN_TEMP_DIR, 0);
/* 62 */     QYSResponse qYSResponse = new QYSResponse();
/* 63 */     this.log.info("QYSClearTempDirInterface--clearTempDir--");
/*    */     try {
/* 65 */       String str1 = QYSDocUtil.getFileSaveBaseDir();
/* 66 */       this.log.info("QYSClearTempDirInterface--baseDir:" + str1);
/*    */       
/* 68 */       String str2 = "qiyuesuo" + File.separatorChar + "qiyuesuo";
/* 69 */       if (str1.endsWith(str2)) {
/* 70 */         Calendar calendar = Calendar.getInstance();
/*    */         
/* 72 */         String str3 = Util.add0(calendar.get(1), 4);
/* 73 */         String str4 = Util.add0(calendar.get(2) + 1, 2);
/* 74 */         String str5 = str3 + str4;
/*    */         
/* 76 */         calendar.add(2, -1);
/* 77 */         str3 = Util.add0(calendar.get(1), 4);
/* 78 */         str4 = Util.add0(calendar.get(2) + 1, 2);
/* 79 */         String str6 = str3 + str4;
/* 80 */         ArrayList<String> arrayList = new ArrayList();
/* 81 */         arrayList.add(str5);
/* 82 */         arrayList.add(str6);
/* 83 */         QYSDocUtil.deleteFile(str1, arrayList);
/*    */       } 
/* 85 */     } catch (Exception exception) {
/* 86 */       this.log.error(exception);
/* 87 */       qYSResponse.setMessage("清除临时文件夹计划任务执行失败," + exception.getLocalizedMessage());
/* 88 */       this.log.info("clearTempDir--" + qYSResponse.getMessage());
/* 89 */       saveOperateLog((qYSResponse.getCode() == 0));
/* 90 */       return qYSResponse.toJSONString();
/*    */     } 
/* 92 */     this.log.info("clearTempDir--清除临时文件夹计划任务执行成功");
/* 93 */     qYSResponse.setCodeAndMessage(0, "");
/* 94 */     saveOperateLog((qYSResponse.getCode() == 0));
/* 95 */     return qYSResponse.toJSONString();
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/qiyuesuo/QYSClearTempDirInterface.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */