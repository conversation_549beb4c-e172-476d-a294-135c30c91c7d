/*     */ package weaver.workflow.qiyuesuo.companyAuth;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import java.util.concurrent.locks.Lock;
/*     */ import java.util.concurrent.locks.ReentrantLock;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import weaver.conn.ConnStatement;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.TimeUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.workflow.qiyuesuo.QYSServerInterface;
/*     */ import weaver.workflow.qiyuesuo.bean.QYSOperateLog;
/*     */ import weaver.workflow.qiyuesuo.bean.QYSResponse;
/*     */ import weaver.workflow.qiyuesuo.constant.QYSCategoryType;
/*     */ import weaver.workflow.qiyuesuo.constant.QYSLogType;
/*     */ import weaver.workflow.qiyuesuo.constant.QYSOperateType;
/*     */ import weaver.workflow.qiyuesuo.service.QYSService;
/*     */ import weaver.workflow.qiyuesuo.service.impl.QYSServicePrivateCompanyAuthImpl;
/*     */ import weaver.workflow.qiyuesuo.service.impl.QYSServicePublicImpl;
/*     */ import weaver.workflow.qiyuesuo.util.QYSLogUtil;
/*     */ import weaver.workflow.qiyuesuo.util.QYSUtil;
/*     */ import weaver.workflow.qiyuesuo.util.QYSWorkflowUtil;
/*     */ import weaver.workflow.workflow.WorkflowVersion;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class QYSCompanyAuthInterface
/*     */   extends BaseBean
/*     */ {
/*  44 */   private static Lock addNewVersionSettingByWorkflowId = new ReentrantLock();
/*     */ 
/*     */   
/*  47 */   private QYSLogUtil log = new QYSLogUtil(getClass().getName()); private User user; private Map settingMap; private int operateId;
/*  48 */   private QYSLogType logType = QYSLogType.OTHER; private QYSOperateLog operateLog;
/*     */   public QYSCompanyAuthInterface() {}
/*     */   
/*     */   public QYSCompanyAuthInterface(User paramUser) {
/*  52 */     this();
/*  53 */     this.user = paramUser;
/*     */   }
/*     */   public QYSCompanyAuthInterface(User paramUser, QYSLogType paramQYSLogType) {
/*  56 */     this(paramUser);
/*  57 */     this.logType = paramQYSLogType;
/*     */   }
/*     */   public User getUser() {
/*  60 */     return this.user;
/*     */   }
/*     */   
/*     */   public void setUser(User paramUser) {
/*  64 */     this.user = paramUser;
/*     */   }
/*     */   
/*     */   private void setSettingMap(int paramInt) {
/*  68 */     setSettingMap(paramInt, true);
/*     */   }
/*     */   public void setOperateLog(QYSCategoryType paramQYSCategoryType, QYSOperateType paramQYSOperateType, int paramInt) {
/*  71 */     if (this.operateId <= 0) {
/*  72 */       this.operateId = Util.getIntValue(this.log.getOperateId(this.user, paramInt));
/*  73 */       this.operateLog = new QYSOperateLog(this.user, getLogType(), paramQYSCategoryType, paramQYSOperateType, paramInt, this.operateId);
/*     */     } 
/*     */   }
/*     */   public void saveOperateLog(boolean paramBoolean) {
/*  77 */     if (this.operateLog != null) {
/*  78 */       this.operateLog.setSuccess(paramBoolean);
/*  79 */       this.log.saveOperateLog(this.operateLog);
/*     */     } 
/*     */   }
/*     */   public int getOperateId() {
/*  83 */     return this.operateId;
/*     */   }
/*     */   public void setOperateId(int paramInt) {
/*  86 */     this.operateId = paramInt;
/*     */   }
/*     */   public QYSOperateLog getOperateLog() {
/*  89 */     return this.operateLog;
/*     */   }
/*     */   public void setOperateLog(QYSOperateLog paramQYSOperateLog) {
/*  92 */     this.operateLog = paramQYSOperateLog;
/*     */   }
/*     */   public QYSLogType getLogType() {
/*  95 */     return this.logType;
/*     */   }
/*     */   public void setLogType(QYSLogType paramQYSLogType) {
/*  98 */     this.logType = paramQYSLogType;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void setSettingMap(int paramInt, boolean paramBoolean) {
/* 107 */     if (this.settingMap != null) {
/*     */       return;
/*     */     }
/*     */     
/* 111 */     if (paramInt <= 0) {
/* 112 */       this.log.error("setSettingMap--参数错误，requestid:" + paramInt);
/*     */       
/*     */       return;
/*     */     } 
/* 116 */     this.settingMap = QYSWorkflowUtil.getRequestBaseInfoById(paramInt, this.user);
/* 117 */     this.settingMap.putAll(getSettingByWorkflowid(Util.getIntValue(getStringValue("workflowid"), 0)));
/*     */     
/* 119 */     int i = Util.getIntValue(getStringValue("setid"), 0);
/* 120 */     if (i > 0) {
/* 121 */       ArrayList arrayList = null;
/* 122 */       if (!paramBoolean) {
/* 123 */         arrayList = new ArrayList();
/*     */       }
/* 125 */       this.settingMap = (new QYSWorkflowUtil(this.user)).getRequestFieldInfo(this.settingMap, arrayList);
/* 126 */       if (paramBoolean) {
/* 127 */         if ("1".equals(getStringValue("serverSource"))) {
/* 128 */           Map map1 = (getSettingValue("fieldNameMap") == null) ? new HashMap<Object, Object>() : (Map)getSettingValue("fieldNameMap");
/* 129 */           Map map2 = (getSettingValue("fieldValueMap") == null) ? new HashMap<Object, Object>() : (Map)getSettingValue("fieldValueMap");
/* 130 */           int j = Util.getIntValue(Util.null2String(map2.get(Util.null2String(map1.get(getStringValue("serverField"))))), 0);
/* 131 */           if (j < 0) {
/* 132 */             j = 0;
/*     */           }
/* 134 */           this.settingMap.put("serverId", Integer.valueOf(j));
/*     */         } 
/* 136 */         this.settingMap.putAll(QYSServerInterface.getServerById(Util.getIntValue(getStringValue("serverId"), 0), this.user.getLanguage()));
/*     */       } 
/*     */     } 
/*     */   }
/*     */   
/*     */   public Object getSettingValue(String paramString) {
/* 142 */     return this.settingMap.get(paramString);
/*     */   }
/*     */   
/*     */   public String getStringValue(String paramString) {
/* 146 */     return Util.null2String(getSettingValue(paramString));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private QYSService getQYSService(int paramInt) {
/*     */     QYSServicePublicImpl qYSServicePublicImpl;
/* 156 */     if (paramInt <= 0) {
/* 157 */       return null;
/*     */     }
/*     */     
/* 160 */     setSettingMap(paramInt);
/* 161 */     String str1 = getStringValue("serverUrl");
/* 162 */     String str2 = getStringValue("accessKey");
/* 163 */     String str3 = getStringValue("accessSecret");
/* 164 */     if (str1.isEmpty() || str2.isEmpty() || str3.isEmpty()) {
/* 165 */       this.log.error("getQYSService--契约锁服务配置不完整--requestid:" + paramInt);
/* 166 */       return null;
/*     */     } 
/*     */ 
/*     */     
/* 170 */     if ("1".equals(getStringValue("serverType"))) {
/* 171 */       QYSServicePrivateCompanyAuthImpl qYSServicePrivateCompanyAuthImpl = new QYSServicePrivateCompanyAuthImpl(str1, str2, str3, this.user, this.operateId);
/*     */     } else {
/* 173 */       qYSServicePublicImpl = new QYSServicePublicImpl(str1, str2, str3, this.user, this.operateId);
/*     */     } 
/*     */     
/* 176 */     return (QYSService)qYSServicePublicImpl;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private QYSService getQYSService(String paramString1, String paramString2, String paramString3, String paramString4) {
/*     */     QYSServicePublicImpl qYSServicePublicImpl;
/* 189 */     paramString2 = Util.null2String(paramString2);
/* 190 */     if (paramString2.isEmpty()) {
/* 191 */       this.log.error("getQYSService--契约锁API服务地址为空！");
/* 192 */       return null;
/*     */     } 
/*     */     
/* 195 */     paramString3 = Util.null2String(paramString3).trim();
/* 196 */     if (paramString3.isEmpty()) {
/* 197 */       paramString3 = null;
/*     */     }
/* 199 */     paramString4 = Util.null2String(paramString4).trim();
/* 200 */     if (paramString4.isEmpty()) {
/* 201 */       paramString4 = null;
/*     */     }
/*     */     
/* 204 */     QYSServicePrivateCompanyAuthImpl qYSServicePrivateCompanyAuthImpl = null;
/* 205 */     if ("1".equals(paramString1)) {
/* 206 */       qYSServicePrivateCompanyAuthImpl = new QYSServicePrivateCompanyAuthImpl(paramString2, paramString3, paramString4, this.user, this.operateId);
/*     */     } else {
/* 208 */       qYSServicePublicImpl = new QYSServicePublicImpl(paramString2, paramString3, paramString4, this.user, this.operateId);
/*     */     } 
/*     */     
/* 211 */     return (QYSService)qYSServicePublicImpl;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static Map getSettingById(int paramInt) {
/* 221 */     return (paramInt <= 0) ? new HashMap<Object, Object>() : getSettingByIdOrWorkflowid(paramInt, 0);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static Map getSettingByWorkflowid(int paramInt) {
/* 231 */     return (paramInt <= 0) ? new HashMap<Object, Object>() : getSettingByIdOrWorkflowid(0, paramInt);
/*     */   }
/*     */   
/*     */   private static Map getSettingByIdOrWorkflowid(int paramInt1, int paramInt2) {
/* 235 */     HashMap<Object, Object> hashMap = new HashMap<Object, Object>();
/* 236 */     String str = "select * from wf_qys_companyAuthSetting where id=? or workflowid=? order by id";
/* 237 */     RecordSet recordSet = new RecordSet();
/* 238 */     recordSet.executeQuery(str, new Object[] { Integer.valueOf(paramInt1), Integer.valueOf(paramInt2) });
/* 239 */     if (recordSet.next()) {
/* 240 */       hashMap.put("setid", Integer.valueOf(Util.getIntValue(recordSet.getString("id"), 0)));
/* 241 */       hashMap.put("workflowid", Integer.valueOf(Util.getIntValue(recordSet.getString("workflowid"), 0)));
/* 242 */       hashMap.put("serverSource", recordSet.getString("serverSource"));
/* 243 */       hashMap.put("serverId", recordSet.getString("serverId"));
/* 244 */       hashMap.put("serverField", recordSet.getString("serverField"));
/* 245 */       hashMap.put("serverType", recordSet.getString("serverType"));
/* 246 */       hashMap.put("companyNameField", recordSet.getString("companyNameField"));
/* 247 */       hashMap.put("isOpenConfig", recordSet.getString("isOpenConfig"));
/* 248 */       hashMap.put("registerNoField", recordSet.getString("registerNoField"));
/* 249 */       hashMap.put("chargerField", recordSet.getString("chargerField"));
/* 250 */       hashMap.put("mobileField", recordSet.getString("mobileField"));
/* 251 */       hashMap.put("licenseField", recordSet.getString("licenseField"));
/*     */     } 
/* 253 */     return hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean hasSetting(int paramInt) {
/* 263 */     RecordSet recordSet = new RecordSet();
/* 264 */     recordSet.executeQuery("SELECT 1 FROM wf_qys_companyAuthSetting where workflowid=? ", new Object[] { Integer.valueOf(paramInt) });
/* 265 */     return recordSet.next();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String doSave(HttpServletRequest paramHttpServletRequest) {
/* 275 */     return doSave(QYSUtil.request2Map(paramHttpServletRequest));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String doSave(Map<String, Object> paramMap) {
/* 285 */     QYSResponse qYSResponse = new QYSResponse();
/* 286 */     if (paramMap == null) {
/* 287 */       qYSResponse.setMessage("保存失败！参数错误：params is null");
/* 288 */       return qYSResponse.toJSONString();
/*     */     } 
/*     */     
/* 291 */     String str1 = Util.null2String(paramMap.get("src"));
/* 292 */     int i = Util.getIntValue(Util.null2String(paramMap.get("id")), 0);
/* 293 */     int j = Util.getIntValue(Util.null2String(paramMap.get("workflowid")), 0);
/* 294 */     String str2 = Util.null2String(paramMap.get("serverSource"));
/* 295 */     boolean bool = "1".equals(str2) ? false : Util.getIntValue(Util.null2String(paramMap.get("serverId")), 0);
/* 296 */     String str3 = "1".equals(str2) ? Util.null2String(paramMap.get("serverField")) : "";
/* 297 */     String str4 = Util.null2String(paramMap.get("serverType"));
/* 298 */     String str5 = Util.null2String(paramMap.get("companyNameField"));
/* 299 */     String str6 = Util.null2String(paramMap.get("isOpenConfig"));
/* 300 */     String str7 = Util.null2String(paramMap.get("registerNoField"));
/* 301 */     String str8 = Util.null2String(paramMap.get("chargerField"));
/* 302 */     String str9 = Util.null2String(paramMap.get("mobileField"));
/* 303 */     String str10 = Util.null2String(paramMap.get("licenseField"));
/*     */     
/* 305 */     if ("add".equals(str1)) {
/* 306 */       String str = "insert into wf_qys_companyAuthSetting(workflowid, serverSource, serverId, serverField, serverType , companyNameField, registerNoField, chargerField, mobileField, licenseField, isOpenConfig ) values(?,?,?,?,?,?,?,?,?,?,?)";
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 311 */       ConnStatement connStatement = new ConnStatement();
/*     */       try {
/* 313 */         connStatement.setStatementSql(str);
/* 314 */         connStatement.setInt(1, j);
/* 315 */         connStatement.setString(2, str2);
/* 316 */         connStatement.setInt(3, bool);
/* 317 */         connStatement.setString(4, str3);
/* 318 */         connStatement.setString(5, str4);
/* 319 */         connStatement.setString(6, str5);
/* 320 */         connStatement.setString(7, str7);
/* 321 */         connStatement.setString(8, str8);
/* 322 */         connStatement.setString(9, str9);
/* 323 */         connStatement.setString(10, str10);
/* 324 */         connStatement.setString(11, str6);
/* 325 */         connStatement.executeUpdate();
/* 326 */       } catch (Exception exception) {
/* 327 */         this.log.error(exception);
/* 328 */         qYSResponse.setMessage(exception.getLocalizedMessage());
/* 329 */         return qYSResponse.toJSONString();
/*     */       } finally {
/* 331 */         connStatement.close();
/*     */       } 
/* 333 */     } else if ("edit".equals(str1)) {
/* 334 */       String str = "update wf_qys_companyAuthSetting set  serverSource=?, serverId=?, serverField=?, serverType=? , companyNameField=?, registerNoField=?, chargerField=?, mobileField=?, licenseField=?, isOpenConfig=?  where id=? ";
/*     */ 
/*     */ 
/*     */       
/* 338 */       ConnStatement connStatement = new ConnStatement();
/*     */       try {
/* 340 */         connStatement.setStatementSql(str);
/* 341 */         connStatement.setString(1, str2);
/* 342 */         connStatement.setInt(2, bool);
/* 343 */         connStatement.setString(3, str3);
/* 344 */         connStatement.setString(4, str4);
/* 345 */         connStatement.setString(5, str5);
/* 346 */         connStatement.setString(6, str7);
/* 347 */         connStatement.setString(7, str8);
/* 348 */         connStatement.setString(8, str9);
/* 349 */         connStatement.setString(9, str10);
/* 350 */         connStatement.setString(10, str6);
/* 351 */         connStatement.setInt(11, i);
/* 352 */         connStatement.executeUpdate();
/* 353 */       } catch (Exception exception) {
/* 354 */         this.log.error(exception);
/* 355 */         qYSResponse.setMessage(exception.getLocalizedMessage());
/* 356 */         return qYSResponse.toJSONString();
/*     */       } finally {
/* 358 */         connStatement.close();
/*     */       } 
/*     */     } 
/*     */ 
/*     */     
/* 363 */     qYSResponse.setCodeAndMessage(0, "");
/* 364 */     return qYSResponse.toJSONString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String doDelete(HttpServletRequest paramHttpServletRequest) {
/* 374 */     return doDelete(QYSUtil.request2Map(paramHttpServletRequest));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String doDelete(Map<String, Object> paramMap) {
/* 384 */     String[] arrayOfString = Util.null2String(paramMap.get("ids")).split(",");
/* 385 */     int[] arrayOfInt = new int[arrayOfString.length];
/* 386 */     for (byte b = 0; b < arrayOfString.length; b++) {
/* 387 */       arrayOfInt[b] = Util.getIntValue(arrayOfString[b], 0);
/*     */     }
/* 389 */     return doDelete(arrayOfInt);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String doDelete(int paramInt) {
/* 399 */     return doDelete(new int[] { paramInt });
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String doDelete(int[] paramArrayOfint) {
/* 409 */     QYSResponse qYSResponse = new QYSResponse();
/* 410 */     if (paramArrayOfint == null) {
/* 411 */       qYSResponse.setMessage("删除失败！参数错误：id is null");
/* 412 */       return qYSResponse.toJSONString();
/*     */     } 
/* 414 */     WorkflowVersion workflowVersion = new WorkflowVersion();
/* 415 */     RecordSet recordSet = new RecordSet();
/* 416 */     for (int i : paramArrayOfint) {
/* 417 */       int j = 0;
/* 418 */       String str1 = "select * from wf_qys_companyAuthSetting where id =?";
/* 419 */       recordSet.executeQuery(str1, new Object[] { Integer.valueOf(i) });
/* 420 */       if (recordSet.next()) {
/* 421 */         j = Util.getIntValue(recordSet.getString("workflowid"));
/*     */       }
/* 423 */       workflowVersion.setWorkflowId(Util.null2String(Integer.valueOf(j)));
/* 424 */       String str2 = workflowVersion.getAllVersionString();
/* 425 */       str1 = "delete from wf_qys_companyAuthSetting where " + Util.getSubINClause(str2, "workflowid", "in");
/* 426 */       recordSet.executeUpdate(str1, new Object[0]);
/*     */     } 
/*     */     
/* 429 */     qYSResponse.setCodeAndMessage(0, "");
/* 430 */     return qYSResponse.toJSONString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String sendCompanyAuthNotify(int paramInt) {
/* 440 */     setOperateLog(QYSCategoryType.COMPANYAUTH, QYSOperateType.SEND_COMPANY_AUTH_NOTIFY, paramInt);
/* 441 */     this.log.info("sendCompanyAuthNotify--start--requestid:" + paramInt);
/*     */     
/* 443 */     QYSResponse qYSResponse = new QYSResponse();
/* 444 */     if (paramInt <= 0) {
/* 445 */       qYSResponse.setMessage("参数错误，流程id：" + paramInt);
/* 446 */       this.log.error("sendCompanyAuthNotify--" + qYSResponse.getMessage());
/* 447 */       saveOperateLog((qYSResponse.getCode() == 0));
/* 448 */       return qYSResponse.toJSONString();
/*     */     } 
/*     */     
/* 451 */     QYSService qYSService = getQYSService(paramInt);
/* 452 */     if ("0".equals(getStringValue("isOpenConfig"))) {
/* 453 */       qYSResponse.setCode(0);
/* 454 */       return qYSResponse.toJSONString();
/*     */     } 
/* 456 */     if (qYSService == null) {
/* 457 */       qYSResponse.setMessage("获取契约锁服务失败，流程id：" + paramInt);
/* 458 */       this.log.error("sendCompanyAuthNotify--" + qYSResponse.getMessage());
/* 459 */       saveOperateLog((qYSResponse.getCode() == 0));
/* 460 */       return qYSResponse.toJSONString();
/*     */     } 
/*     */     
/* 463 */     String str1 = TimeUtil.getCurrentTimeString();
/* 464 */     qYSResponse = new QYSResponse(qYSService.sendCompanyAuthNotify(this.settingMap));
/* 465 */     if (0 != qYSResponse.getCode()) {
/* 466 */       this.log.error("sendCompanyAuthNotify--通知用户企业认证！" + qYSResponse.getMessage());
/* 467 */       saveOperateLog((qYSResponse.getCode() == 0));
/* 468 */       return qYSResponse.toJSONString();
/*     */     } 
/*     */     
/* 471 */     Long long_ = Long.valueOf(qYSResponse.getLongValue("companyId"));
/* 472 */     this.log.info("sendCompanyAuthNotify--companyId:" + Util.null2String(long_));
/*     */     
/* 474 */     if (long_.longValue() <= 0L) {
/* 475 */       qYSResponse.setMessage("通知用户企业认证失败，流程id：" + paramInt);
/* 476 */       this.log.error("sendCompanyAuthNotify--" + qYSResponse.getMessage());
/* 477 */       saveOperateLog((qYSResponse.getCode() == 0));
/* 478 */       return qYSResponse.toJSONString();
/*     */     } 
/*     */     
/* 481 */     String str2 = Util.null2String(qYSResponse.getString("requestJson"));
/* 482 */     String str3 = Util.null2String(qYSResponse.getString("responseJson"));
/* 483 */     String str4 = Util.null2String(qYSResponse.getString("companyName"));
/*     */     
/* 485 */     int i = this.user.getUID();
/* 486 */     int j = Util.getIntValue(Util.null2String(this.user.getLogintype()), 1) - 1;
/* 487 */     String str5 = Util.null2String(this.user.getLoginip());
/* 488 */     String str6 = "";
/* 489 */     String str7 = "";
/* 490 */     String[] arrayOfString = str1.split(" ");
/* 491 */     if (arrayOfString.length == 2) {
/* 492 */       str6 = arrayOfString[0];
/* 493 */       str7 = arrayOfString[1];
/*     */     } 
/*     */     
/* 496 */     String str8 = "insert into wf_qys_companyAuthLog (requestid, companyId, setid, serverId , userid, usertype, ipaddress, createDate, createTime, requestJson, responseJson, companyName ) values(?,?,?,?,?,?,?,?,?,?,?,?) ";
/*     */ 
/*     */     
/* 499 */     ConnStatement connStatement = new ConnStatement();
/*     */     try {
/* 501 */       connStatement.setStatementSql(str8);
/* 502 */       connStatement.setInt(1, paramInt);
/* 503 */       connStatement.setString(2, Util.null2String(long_));
/* 504 */       connStatement.setInt(3, Util.getIntValue(getStringValue("setid")));
/* 505 */       connStatement.setInt(4, Util.getIntValue(getStringValue("serverId")));
/* 506 */       connStatement.setInt(5, i);
/* 507 */       connStatement.setInt(6, j);
/* 508 */       connStatement.setString(7, str5);
/* 509 */       connStatement.setString(8, str6);
/* 510 */       connStatement.setString(9, str7);
/* 511 */       connStatement.setString(10, str2);
/* 512 */       connStatement.setString(11, str3);
/* 513 */       connStatement.setString(12, str4);
/* 514 */       connStatement.executeUpdate();
/* 515 */     } catch (Exception exception) {
/* 516 */       this.log.error(exception);
/*     */     } finally {
/* 518 */       connStatement.close();
/*     */     } 
/*     */     
/* 521 */     qYSResponse.setCodeAndMessage(0, "");
/* 522 */     this.log.info("sendCompanyAuthNotify--end--requestid:" + paramInt);
/* 523 */     saveOperateLog((qYSResponse.getCode() == 0));
/* 524 */     return qYSResponse.toJSONString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Long getCompanyIdByRequestId(int paramInt) {
/* 534 */     Long long_ = null;
/* 535 */     if (paramInt <= 0) {
/* 536 */       return long_;
/*     */     }
/*     */     
/* 539 */     RecordSet recordSet = new RecordSet();
/* 540 */     recordSet.executeQuery("select * from wf_qys_companyAuthLog where requestid=? order by id desc", new Object[] { Integer.valueOf(paramInt) });
/* 541 */     if (recordSet.next()) {
/* 542 */       String str = recordSet.getString("companyId");
/* 543 */       if (str.isEmpty()) {
/* 544 */         return long_;
/*     */       }
/*     */       
/* 547 */       long_ = Long.valueOf(str);
/*     */     } 
/* 549 */     return long_;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getRequestIdByCompanyId(Long paramLong) {
/* 559 */     int i = 0;
/* 560 */     if (paramLong == null || paramLong.longValue() <= 0L) {
/* 561 */       return i;
/*     */     }
/*     */     
/* 564 */     RecordSet recordSet = new RecordSet();
/* 565 */     recordSet.executeQuery("select * from wf_qys_companyAuthLog where companyId=? order by id desc", new Object[] { Util.null2String(paramLong) });
/* 566 */     if (recordSet.next()) {
/* 567 */       i = Util.getIntValue(recordSet.getString("requestid"), 0);
/*     */     }
/* 569 */     return i;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String deleteUnceritifiedCompany(int paramInt) {
/* 579 */     setOperateLog(QYSCategoryType.COMPANYAUTH, QYSOperateType.DELETE_UNCERITIFIED_COMPANY, paramInt);
/* 580 */     this.log.info("deleteUnceritifiedCompany--start--requestid:" + paramInt);
/*     */     
/* 582 */     QYSResponse qYSResponse = new QYSResponse();
/* 583 */     if (paramInt <= 0) {
/* 584 */       qYSResponse.setMessage("参数错误：流程Id：" + paramInt);
/* 585 */       this.log.error("deleteUnceritifiedCompany--" + qYSResponse.getMessage());
/* 586 */       saveOperateLog((qYSResponse.getCode() == 0));
/* 587 */       return qYSResponse.toJSONString();
/*     */     } 
/*     */     
/* 590 */     QYSService qYSService = getQYSService(paramInt);
/* 591 */     if ("0".equals(getStringValue("isOpenConfig"))) {
/* 592 */       qYSResponse.setCode(0);
/* 593 */       return qYSResponse.toJSONString();
/*     */     } 
/* 595 */     if (qYSService == null) {
/* 596 */       qYSResponse.setMessage("获取契约锁服务失败，流程id：" + paramInt);
/* 597 */       this.log.error("deleteUnceritifiedCompany--" + qYSResponse.getMessage());
/* 598 */       saveOperateLog((qYSResponse.getCode() == 0));
/* 599 */       return qYSResponse.toJSONString();
/*     */     } 
/*     */     
/* 602 */     Long long_ = getCompanyIdByRequestId(paramInt);
/* 603 */     if (long_ == null || long_.longValue() <= 0L) {
/* 604 */       qYSResponse.setMessage("获取公司id失败，流程id：" + paramInt);
/* 605 */       this.log.error("deleteUnceritifiedCompany--" + qYSResponse.getMessage());
/* 606 */       saveOperateLog((qYSResponse.getCode() == 0));
/* 607 */       return qYSResponse.toJSONString();
/*     */     } 
/* 609 */     this.log.info("deleteUnceritifiedCompany--companyId:" + Util.null2String(long_));
/*     */     
/* 611 */     qYSResponse = new QYSResponse(qYSService.deleteUnceritifiedCompany(long_, paramInt));
/* 612 */     if (0 != qYSResponse.getCode()) {
/* 613 */       this.log.error("deleteUnceritifiedCompany--删除未认证公司失败！" + qYSResponse.getMessage());
/* 614 */       saveOperateLog((qYSResponse.getCode() == 0));
/* 615 */       return qYSResponse.toJSONString();
/*     */     } 
/*     */     
/* 618 */     qYSResponse.setCodeAndMessage(0, "");
/* 619 */     this.log.info("deleteUnceritifiedCompany--end--requestid:" + paramInt);
/* 620 */     saveOperateLog((qYSResponse.getCode() == 0));
/* 621 */     return qYSResponse.toJSONString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String addNewVersionSettingByRequestId(int paramInt) {
/* 631 */     QYSResponse qYSResponse = new QYSResponse();
/* 632 */     if (paramInt <= 0) {
/* 633 */       qYSResponse.setMessage("参数错误，requestid:" + paramInt);
/* 634 */       this.log.error("addNewVersionSettingByRequestId--" + qYSResponse.getMessage());
/* 635 */       return qYSResponse.toJSONString();
/*     */     } 
/*     */     
/* 638 */     int i = 0;
/* 639 */     RecordSet recordSet = new RecordSet();
/* 640 */     recordSet.executeQuery("select workflowid from workflow_requestbase where requestid=?", new Object[] { Integer.valueOf(paramInt) });
/* 641 */     if (recordSet.next()) {
/* 642 */       i = Util.getIntValue(recordSet.getString("workflowid"));
/*     */     }
/* 644 */     return addNewVersionSettingByRequestId(i);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String addNewVersionSettingByWorkflowId(int paramInt) {
/* 653 */     QYSResponse qYSResponse = new QYSResponse();
/* 654 */     ConnStatement connStatement = new ConnStatement();
/*     */     try {
/* 656 */       addNewVersionSettingByWorkflowId.lock();
/* 657 */       this.log.info("addNewVersionSettingByWorkflowId--start--workflowid:" + paramInt);
/* 658 */       if (paramInt <= 0) {
/* 659 */         qYSResponse.setMessage("参数错误，workflowid:" + paramInt);
/* 660 */         this.log.error("addNewVersionSettingByWorkflowId--" + qYSResponse.getMessage());
/*     */       } 
/*     */       
/* 663 */       boolean bool1 = false;
/* 664 */       RecordSet recordSet = new RecordSet();
/* 665 */       String str1 = "select * from wf_qys_companyAuthSetting where workflowid=?";
/* 666 */       recordSet.executeQuery(str1, new Object[] { Integer.valueOf(paramInt) });
/* 667 */       if (recordSet.next()) {
/* 668 */         bool1 = true;
/*     */       }
/* 670 */       if (bool1) {
/* 671 */         qYSResponse.setCodeAndMessage(0, "");
/* 672 */         this.log.info("addNewVersionSettingByWorkflowId--流程已配置过--workflowid:" + paramInt);
/* 673 */         return qYSResponse.toJSONString();
/*     */       } 
/*     */ 
/*     */       
/* 677 */       int i = -1;
/* 678 */       WorkflowVersion workflowVersion = new WorkflowVersion(Util.null2String(Integer.valueOf(paramInt)));
/* 679 */       String str2 = workflowVersion.getAllVersionString();
/* 680 */       String str3 = workflowVersion.getActiveVersionWFID();
/* 681 */       str1 = "select * from wf_qys_companyAuthSetting";
/* 682 */       str1 = str1 + " where " + Util.getSubINClause(str2, "workflowid", "in");
/* 683 */       str1 = str1 + " order by id asc";
/* 684 */       recordSet.executeQuery(str1, new Object[0]);
/* 685 */       while (recordSet.next()) {
/* 686 */         i = Util.getIntValue(Util.null2String(recordSet.getString("workflowid")));
/* 687 */         if (i == Util.getIntValue(str3)) {
/*     */           break;
/*     */         }
/*     */       } 
/* 691 */       if (i <= 0) {
/* 692 */         qYSResponse.setCodeAndMessage(0, "");
/* 693 */         this.log.info("addNewVersionSettingByWorkflowIf--该流程的所有版本都未配置--workflowid:" + paramInt);
/* 694 */         return qYSResponse.toJSONString();
/*     */       } 
/* 696 */       Map map = getSettingByWorkflowid(i);
/* 697 */       String str4 = Util.null2String(map.get("serverSource"));
/* 698 */       boolean bool2 = "1".equals(str4) ? false : Util.getIntValue(Util.null2String(map.get("serverId")), 0);
/* 699 */       String str5 = "1".equals(str4) ? Util.null2String(map.get("serverField")) : "";
/* 700 */       String str6 = Util.null2String(map.get("serverType"));
/* 701 */       String str7 = Util.null2String(map.get("companyNameField"));
/* 702 */       String str8 = Util.null2String(map.get("isOpenConfig"));
/* 703 */       String str9 = Util.null2String(map.get("registerNoField"));
/* 704 */       String str10 = Util.null2String(map.get("chargerField"));
/* 705 */       String str11 = Util.null2String(map.get("mobileField"));
/* 706 */       String str12 = Util.null2String(map.get("licenseField"));
/*     */       
/* 708 */       str1 = "insert into wf_qys_companyAuthSetting(workflowid, serverSource, serverId, serverField, serverType , companyNameField, registerNoField, chargerField, mobileField, licenseField, isOpenConfig ) values(?,?,?,?,?,?,?,?,?,?,?)";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 714 */       connStatement.setStatementSql(str1);
/* 715 */       connStatement.setInt(1, paramInt);
/* 716 */       connStatement.setString(2, str4);
/* 717 */       connStatement.setInt(3, bool2);
/* 718 */       connStatement.setString(4, str5);
/* 719 */       connStatement.setString(5, str6);
/* 720 */       connStatement.setString(6, str7);
/* 721 */       connStatement.setString(7, str9);
/* 722 */       connStatement.setString(8, str10);
/* 723 */       connStatement.setString(9, str11);
/* 724 */       connStatement.setString(10, str12);
/* 725 */       connStatement.setString(11, str8);
/* 726 */       connStatement.executeUpdate();
/*     */     }
/* 728 */     catch (Exception exception) {
/* 729 */       qYSResponse.setMessage("复制企业认证多版本配置信息失败，worflowid:" + paramInt);
/* 730 */       this.log.error("addNewVersionSettingByWorkflowId--" + qYSResponse.getMessage());
/* 731 */       this.log.error(exception);
/* 732 */       return qYSResponse.toJSONString();
/*     */     } finally {
/* 734 */       if (connStatement != null) {
/* 735 */         connStatement.close();
/*     */       }
/* 737 */       addNewVersionSettingByWorkflowId.unlock();
/*     */     } 
/* 739 */     qYSResponse.setCodeAndMessage(0, "");
/* 740 */     this.log.info("addNewVersionSettingByWorkflowId--end--workflowid:" + paramInt);
/* 741 */     return qYSResponse.toJSONString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCompany(String paramString1, String paramString2, String paramString3, String paramString4, String paramString5) {
/* 753 */     return getCompany(paramString1, paramString2, paramString3, paramString4, paramString5, true);
/*     */   }
/*     */   
/*     */   public String getCompany(String paramString1, String paramString2, String paramString3, String paramString4, String paramString5, boolean paramBoolean) {
/* 757 */     if (paramBoolean) {
/* 758 */       setOperateLog(QYSCategoryType.OTHER, QYSOperateType.QUERY_COMPANY, 0);
/*     */     }
/* 760 */     QYSResponse qYSResponse = new QYSResponse();
/* 761 */     this.log.info("getInnerCompany--start");
/* 762 */     QYSService qYSService = getQYSService(paramString2, paramString3, paramString4, paramString5);
/* 763 */     if (qYSService == null) {
/* 764 */       qYSResponse.setMessage("获取契约锁服务失败,serverUrl:" + paramString3 + "  accessKey:" + paramString4 + "  accessSecret:" + paramString5);
/* 765 */       this.log.error("getInnerCompany--" + qYSResponse.getMessage());
/* 766 */       saveOperateLog(false);
/* 767 */       return qYSResponse.toJSONString();
/*     */     } 
/* 769 */     qYSResponse = new QYSResponse(qYSService.getCompany(paramString1));
/* 770 */     if (qYSResponse.getCode() != 0) {
/* 771 */       this.log.error("获取契约锁企业列表失败，" + qYSResponse.getMessage());
/* 772 */       saveOperateLog(false);
/* 773 */       return qYSResponse.toJSONString();
/*     */     } 
/*     */     
/* 776 */     qYSResponse.setCodeAndMessage(0, "");
/* 777 */     this.log.info("getInnerCompany--end");
/* 778 */     saveOperateLog((qYSResponse.getCode() == 0));
/* 779 */     return qYSResponse.toJSONString();
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/qiyuesuo/companyAuth/QYSCompanyAuthInterface.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */