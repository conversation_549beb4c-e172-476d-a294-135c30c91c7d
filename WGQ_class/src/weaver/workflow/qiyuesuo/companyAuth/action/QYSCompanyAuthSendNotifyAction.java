/*    */ package weaver.workflow.qiyuesuo.companyAuth.action;
/*    */ 
/*    */ import weaver.general.BaseBean;
/*    */ import weaver.general.Util;
/*    */ import weaver.interfaces.workflow.action.Action;
/*    */ import weaver.soa.workflow.request.RequestInfo;
/*    */ import weaver.workflow.qiyuesuo.bean.QYSResponse;
/*    */ import weaver.workflow.qiyuesuo.companyAuth.QYSCompanyAuthInterface;
/*    */ import weaver.workflow.qiyuesuo.constant.QYSLogType;
/*    */ import weaver.workflow.qiyuesuo.util.QYSLogUtil;
/*    */ import weaver.workflow.request.RequestManager;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class QYSCompanyAuthSendNotifyAction
/*    */   extends BaseBean
/*    */   implements Action
/*    */ {
/*    */   public String execute(RequestInfo paramRequestInfo) {
/* 22 */     int i = Util.getIntValue(paramRequestInfo.getRequestid(), 0);
/* 23 */     int j = Util.getIntValue(paramRequestInfo.getWorkflowid(), 0);
/*    */     
/* 25 */     QYSLogUtil qYSLogUtil = new QYSLogUtil(getClass().getName());
/* 26 */     qYSLogUtil.info("start--requestid:" + i);
/*    */     
/* 28 */     RequestManager requestManager = paramRequestInfo.getRequestManager();
/* 29 */     QYSCompanyAuthInterface qYSCompanyAuthInterface = new QYSCompanyAuthInterface(requestManager.getUser(), QYSLogType.ACTION);
/*    */     
/* 31 */     QYSResponse qYSResponse = new QYSResponse(qYSCompanyAuthInterface.addNewVersionSettingByWorkflowId(j));
/* 32 */     if (qYSResponse.getCode() != 0) {
/* 33 */       requestManager.setMessageid("126221");
/* 34 */       String str = qYSResponse.getMessage();
/* 35 */       if (str.equals("")) {
/* 36 */         str = "企业认证集成配置复制失败！";
/*    */       }
/* 38 */       requestManager.setMessagecontent(str);
/* 39 */       return "0";
/*    */     } 
/* 41 */     qYSResponse = new QYSResponse(qYSCompanyAuthInterface.sendCompanyAuthNotify(i));
/* 42 */     if (0 != qYSResponse.getCode()) {
/* 43 */       String str = qYSResponse.getMessage();
/* 44 */       qYSLogUtil.error("通知用户企业认证失败！" + str);
/*    */       
/* 46 */       if (str.isEmpty()) {
/* 47 */         str = "通知用户企业认证失败！";
/*    */       }
/*    */       
/* 50 */       requestManager.setMessageid("126221");
/* 51 */       requestManager.setMessagecontent(str);
/*    */     } 
/*    */     
/* 54 */     qYSLogUtil.info("end--requestid:" + i);
/* 55 */     return requestManager.getMessageid().isEmpty() ? "1" : "0";
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/qiyuesuo/companyAuth/action/QYSCompanyAuthSendNotifyAction.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */