/*    */ package weaver.workflow.qiyuesuo.companyAuth.action;
/*    */ 
/*    */ import weaver.general.BaseBean;
/*    */ import weaver.general.Util;
/*    */ import weaver.interfaces.workflow.action.Action;
/*    */ import weaver.soa.workflow.request.RequestInfo;
/*    */ import weaver.workflow.qiyuesuo.bean.QYSResponse;
/*    */ import weaver.workflow.qiyuesuo.companyAuth.QYSCompanyAuthInterface;
/*    */ import weaver.workflow.qiyuesuo.constant.QYSLogType;
/*    */ import weaver.workflow.qiyuesuo.util.QYSLogUtil;
/*    */ import weaver.workflow.request.RequestManager;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class QYSCompanyAuthDeleteAction
/*    */   extends BaseBean
/*    */   implements Action
/*    */ {
/*    */   public String execute(RequestInfo paramRequestInfo) {
/* 22 */     int i = Util.getIntValue(paramRequestInfo.getRequestid(), 0);
/* 23 */     int j = Util.getIntValue(paramRequestInfo.getWorkflowid(), 0);
/*    */     
/* 25 */     QYSLogUtil qYSLogUtil = new QYSLogUtil(getClass().getName());
/* 26 */     qYSLogUtil.info("start--requestid:" + i);
/*    */     
/* 28 */     RequestManager requestManager = paramRequestInfo.getRequestManager();
/* 29 */     QYSCompanyAuthInterface qYSCompanyAuthInterface = new QYSCompanyAuthInterface(requestManager.getUser(), QYSLogType.ACTION);
/*    */     
/* 31 */     QYSResponse qYSResponse = new QYSResponse(qYSCompanyAuthInterface.addNewVersionSettingByWorkflowId(j));
/* 32 */     if (qYSResponse.getCode() != 0) {
/* 33 */       requestManager.setMessageid("126221");
/* 34 */       String str = qYSResponse.getMessage();
/* 35 */       if (str.equals("")) {
/* 36 */         str = "企业认证集成配置复制失败！";
/*    */       }
/* 38 */       requestManager.setMessagecontent(str);
/* 39 */       return "0";
/*    */     } 
/*    */     
/* 42 */     qYSResponse = new QYSResponse(qYSCompanyAuthInterface.deleteUnceritifiedCompany(i));
/* 43 */     if (0 != qYSResponse.getCode()) {
/* 44 */       String str = qYSResponse.getMessage();
/* 45 */       qYSLogUtil.error("删除未认证公司失败！" + str);
/*    */       
/* 47 */       if (str.isEmpty()) {
/* 48 */         str = "删除未认证公司失败！";
/*    */       }
/*    */       
/* 51 */       requestManager.setMessageid("126221");
/* 52 */       requestManager.setMessagecontent(str);
/*    */     } 
/*    */     
/* 55 */     qYSLogUtil.info("end--requestid:" + i);
/* 56 */     return requestManager.getMessageid().isEmpty() ? "1" : "0";
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/qiyuesuo/companyAuth/action/QYSCompanyAuthDeleteAction.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */