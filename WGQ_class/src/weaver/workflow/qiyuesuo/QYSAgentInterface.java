/*     */ package weaver.workflow.qiyuesuo;
/*     */ 
/*     */ import com.alibaba.fastjson.JSONArray;
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import java.lang.reflect.Method;
/*     */ import java.text.SimpleDateFormat;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Arrays;
/*     */ import java.util.Date;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ import weaver.workflow.qiyuesuo.bean.QYSOperateLog;
/*     */ import weaver.workflow.qiyuesuo.bean.QYSResponse;
/*     */ import weaver.workflow.qiyuesuo.constant.QYSCategoryType;
/*     */ import weaver.workflow.qiyuesuo.constant.QYSLogType;
/*     */ import weaver.workflow.qiyuesuo.constant.QYSOperateType;
/*     */ import weaver.workflow.qiyuesuo.service.QYSAgentService;
/*     */ import weaver.workflow.qiyuesuo.service.impl.QYSAgentServicePrivateImpl;
/*     */ import weaver.workflow.qiyuesuo.util.QYSLogUtil;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class QYSAgentInterface
/*     */ {
/*     */   public static final String CONTACT_TYPE = "contactType";
/*     */   public static final String AGENTID = "agentId";
/*     */   public static final String AGENTORID = "agentorId";
/*     */   public static final String AGENTOR_MOBILE = "agentorMobile";
/*     */   public static final String AGENTOR_EMAIL = "agentorEmail";
/*     */   public static final String AGENTOR_WORKCODE = "agentorWorkCode";
/*     */   public static final String PRINCIPALID = "principalId";
/*     */   public static final String PRINCIPAL_MOBILE = "principalMobile";
/*     */   public static final String PRINCIPAL_EMAIL = "principalEmail";
/*     */   public static final String PRINCIPAL_WORKCODE = "principalWorkCode";
/*     */   public static final String START_DATE = "startdate";
/*     */   public static final String START_TIME = "starttime";
/*     */   public static final String END_DATE = "enddate";
/*     */   public static final String END_TIME = "endtime";
/*     */   public static final String WF_QIYUESUOAGENTLOG = "wf_qiyuesuoAgentLog";
/*     */   public static final String WORKFLOW_AGENTCONDITIONSET = "workflow_agentconditionset";
/*     */   private QYSOperateLog operateLog;
/*     */   private QYSLogType logType;
/*     */   private int operateId;
/*     */   private QYSLogUtil log;
/*     */   private User user;
/*     */   
/*     */   public QYSAgentInterface(User paramUser, int paramInt) {
/*  72 */     this.log = new QYSLogUtil(getClass().getName());
/*  73 */     this.logType = QYSLogType.OTHER;
/*  74 */     this.user = paramUser;
/*  75 */     this.operateId = paramInt;
/*     */   }
/*     */   
/*     */   public QYSAgentInterface(User paramUser) {
/*  79 */     this(paramUser, 0);
/*     */   }
/*     */   
/*     */   public void setOperateLog(QYSCategoryType paramQYSCategoryType, QYSOperateType paramQYSOperateType, int paramInt) {
/*  83 */     if (this.operateId <= 0) {
/*  84 */       this.operateId = Util.getIntValue(this.log.getOperateId(this.user, paramInt));
/*  85 */       this.operateLog = new QYSOperateLog(this.user, this.logType, paramQYSCategoryType, paramQYSOperateType, paramInt, this.operateId);
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   public void saveOperateLog(boolean paramBoolean) {
/*  91 */     if (this.operateLog != null) {
/*  92 */       this.operateLog.setSuccess(paramBoolean);
/*  93 */       this.log.saveOperateLog(this.operateLog);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private QYSAgentService getQYSAgentService(String paramString1, String paramString2, String paramString3, String paramString4) {
/*     */     QYSAgentServicePrivateImpl qYSAgentServicePrivateImpl;
/* 106 */     QYSAgentService qYSAgentService = null;
/* 107 */     this.log.info("getQYSAgentService--url:" + paramString1 + "--token:" + paramString2 + "--secret:" + paramString3 + "--serverType:" + paramString4 + "--operateId:" + this.operateId);
/* 108 */     if ("".equals(paramString1) || "".equals(paramString2) || "".equals(paramString3)) {
/* 109 */       return qYSAgentService;
/*     */     }
/* 111 */     if ("1".equals(paramString4)) {
/* 112 */       qYSAgentServicePrivateImpl = new QYSAgentServicePrivateImpl(paramString1, paramString2, paramString3, this.operateId, this.user);
/*     */     }
/* 114 */     return (QYSAgentService)qYSAgentServicePrivateImpl;
/*     */   }
/*     */   
/*     */   public String handleAgent(int paramInt1, int paramInt2, int paramInt3, Long paramLong, Map<String, Object> paramMap) {
/* 118 */     this.log.info("handleAgent--workflowId:" + paramInt1 + "--requestId:" + paramInt2 + "--nodeId:" + paramInt3 + "--contractId:" + paramLong);
/* 119 */     Long long_ = Long.valueOf(System.currentTimeMillis());
/*     */     
/* 121 */     QYSResponse qYSResponse = new QYSResponse(getAgentInfo(paramInt2, paramInt3));
/* 122 */     if (qYSResponse.getCode() != 0) {
/* 123 */       this.log.info("handleAgent--" + qYSResponse.getMessage());
/* 124 */       if ("".equals(qYSResponse.getMessage())) {
/* 125 */         qYSResponse.setMessage("合同代理失败，获取代理信息失败");
/*     */       }
/* 127 */       return qYSResponse.toJSONString();
/*     */     } 
/*     */     
/* 130 */     int i = Util.getIntValue(Util.null2String(qYSResponse.get("agentorId")));
/* 131 */     int j = Util.getIntValue(Util.null2String(qYSResponse.get("principalId")));
/* 132 */     this.log.info("handleAgent--agentorId:" + i + "--principalId:" + j);
/*     */ 
/*     */     
/* 135 */     if (i > 0 && j > 0) {
/*     */ 
/*     */       
/* 138 */       if (!haveAgent(paramLong, paramInt2, paramInt3, i, j).booleanValue()) {
/* 139 */         setOperateLog(QYSCategoryType.ELECTRONIC, QYSOperateType.AGENT_CONTRACT, paramInt2);
/*     */         
/* 141 */         String str1 = Util.null2String(paramMap.get("serverUrl"));
/* 142 */         String str2 = Util.null2String(paramMap.get("accessKey"));
/* 143 */         String str3 = Util.null2String(paramMap.get("accessSecret"));
/* 144 */         String str4 = Util.null2String(paramMap.get("serverType"));
/* 145 */         QYSAgentService qYSAgentService = getQYSAgentService(str1, str2, str3, str4);
/* 146 */         if (qYSAgentService == null) {
/* 147 */           this.log.info("handleAgent--service is null");
/* 148 */           if (str4.equals("1")) {
/* 149 */             qYSResponse.setMessage("合同代理失败，获取代理服务类失败");
/*     */           } else {
/* 151 */             qYSResponse.setCodeAndMessage(0, "契约锁公有云不支持合同代理功能");
/*     */           } 
/* 153 */           return qYSResponse.toJSONString();
/*     */         } 
/*     */         
/* 156 */         Map<String, Object> map = structureAgentInfo(paramInt1, j, i, paramMap);
/* 157 */         this.log.info("handleAgent--agentInfo:" + JSONObject.toJSONString(map));
/*     */         
/* 159 */         qYSResponse = new QYSResponse(qYSAgentService.agent(Arrays.asList(new Long[] { paramLong }, ), map, paramMap));
/* 160 */         if (qYSResponse.getCode() != 0) {
/* 161 */           this.log.error("handleAgent--" + qYSResponse.getMessage());
/* 162 */           saveOperateLog(false);
/* 163 */           return qYSResponse.toJSONString();
/*     */         } 
/* 165 */         Date date = new Date();
/* 166 */         String str5 = getFormatDate(date);
/* 167 */         String str6 = getFormatTime(date);
/*     */ 
/*     */         
/* 170 */         String str7 = "insert into wf_qiyuesuoAgentLog(workflowId, requestId, nodeId, contractId, groupId, agentId, agentorId, principalId, startDate, startTime, endDate, endTime, createDate, createTime, isCancelOrEnd) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
/* 171 */         RecordSet recordSet = new RecordSet();
/* 172 */         recordSet.executeUpdate(str7, new Object[] { Integer.valueOf(paramInt1), Integer.valueOf(paramInt2), Integer.valueOf(paramInt3), paramLong, Util.null2String(qYSResponse.get("groupId")), Integer.valueOf(Util.getIntValue(Util.null2String(map.get("agentId")))), Integer.valueOf(i), Integer.valueOf(j), Util.null2String(map.get("startdate")), "", Util.null2String(map.get("enddate")), "", str5, str6, Integer.valueOf(0) });
/* 173 */         saveOperateLog(true);
/*     */       } 
/*     */     } else {
/*     */       
/* 177 */       cancelAgent(paramLong, paramInt2, paramInt3, paramMap);
/*     */     } 
/* 179 */     System.out.println("handleAgent花费时间:" + (System.currentTimeMillis() - long_.longValue()));
/* 180 */     qYSResponse.setCodeAndMessage(0, "");
/* 181 */     return qYSResponse.toJSONString();
/*     */   }
/*     */   
/*     */   private String getFormatDate(Date paramDate) {
/* 185 */     String str = "";
/* 186 */     SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
/* 187 */     String[] arrayOfString = simpleDateFormat.format(paramDate).split(" ");
/* 188 */     if (arrayOfString.length == 2) {
/* 189 */       str = arrayOfString[0];
/*     */     }
/* 191 */     return str;
/*     */   }
/*     */   
/*     */   private String getFormatTime(Date paramDate) {
/* 195 */     String str = "";
/* 196 */     SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
/* 197 */     String[] arrayOfString = simpleDateFormat.format(paramDate).split(" ");
/* 198 */     if (arrayOfString.length == 2) {
/* 199 */       str = arrayOfString[1];
/*     */     }
/* 201 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private Map<String, Object> structureAgentInfo(int paramInt1, int paramInt2, int paramInt3, Map<String, Object> paramMap) {
/*     */     ResourceComInfo resourceComInfo;
/* 213 */     HashMap<Object, Object> hashMap = new HashMap<Object, Object>();
/*     */     
/*     */     try {
/* 216 */       resourceComInfo = new ResourceComInfo();
/* 217 */     } catch (Exception exception) {
/* 218 */       this.log.error(exception);
/* 219 */       return null;
/*     */     } 
/*     */     
/* 222 */     RecordSet recordSet = new RecordSet();
/* 223 */     String str1 = "";
/* 224 */     String str2 = "";
/* 225 */     String str3 = "";
/* 226 */     recordSet.executeQuery("select * from WORKFLOW_AGENTCONDITIONSET where workflowId=? and agentType=? and agentUid=? and bagentUid=? order by enddate, endtime desc", new Object[] { Integer.valueOf(paramInt1), Integer.valueOf(1), Integer.valueOf(paramInt3), Integer.valueOf(paramInt2) });
/* 227 */     if (recordSet.next()) {
/* 228 */       str1 = Util.null2String(recordSet.getString("endDate"));
/* 229 */       str2 = Util.null2String(recordSet.getString("endTime"));
/* 230 */       str3 = Util.null2String(recordSet.getString("agentId"));
/*     */     } 
/*     */     
/* 233 */     String str4 = "";
/* 234 */     String str5 = "";
/* 235 */     String str6 = Util.null2String(QYSInterface.getConfig().get("contactType"));
/* 236 */     String str7 = Util.null2String(resourceComInfo.getMobile(Util.null2String(Integer.valueOf(paramInt2))));
/* 237 */     String str8 = Util.null2String(resourceComInfo.getEmail(Util.null2String(Integer.valueOf(paramInt2))));
/* 238 */     String str9 = Util.null2String(resourceComInfo.getWorkcode(Util.null2String(Integer.valueOf(paramInt2))));
/* 239 */     String str10 = Util.null2String(resourceComInfo.getMobile(Util.null2String(Integer.valueOf(paramInt3))));
/* 240 */     String str11 = Util.null2String(resourceComInfo.getEmail(Util.null2String(Integer.valueOf(paramInt3))));
/* 241 */     String str12 = Util.null2String(resourceComInfo.getMobile(Util.null2String(Integer.valueOf(paramInt3))));
/* 242 */     hashMap.put("contactType", str6);
/* 243 */     hashMap.put("agentorId", Integer.valueOf(paramInt3));
/* 244 */     hashMap.put("agentorMobile", str10);
/* 245 */     hashMap.put("agentorEmail", str11);
/* 246 */     hashMap.put("agentorWorkCode", str12);
/* 247 */     hashMap.put("principalId", Integer.valueOf(paramInt2));
/* 248 */     hashMap.put("principalMobile", str7);
/* 249 */     hashMap.put("principalEmail", str8);
/* 250 */     hashMap.put("principalWorkCode", str9);
/* 251 */     hashMap.put("startdate", str4);
/* 252 */     hashMap.put("starttime", str5);
/* 253 */     hashMap.put("enddate", str1);
/* 254 */     hashMap.put("endtime", str2);
/* 255 */     hashMap.put("agentId", str3);
/*     */     
/* 257 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public String cancelAgent(Long paramLong, int paramInt1, int paramInt2, Map<String, Object> paramMap) {
/* 262 */     setOperateLog(QYSCategoryType.ELECTRONIC, QYSOperateType.CANCEL_AGENT, paramInt1);
/* 263 */     this.log.info("cancelAgent--contractId:" + paramLong + "--requestId:" + paramInt1 + "--nodeId:" + paramInt2 + "--principalId:" + this.user.getUID());
/* 264 */     QYSResponse qYSResponse = new QYSResponse();
/* 265 */     ArrayList<Long> arrayList = new ArrayList();
/* 266 */     ArrayList arrayList1 = new ArrayList();
/* 267 */     RecordSet recordSet = new RecordSet();
/* 268 */     String str1 = "select groupId, endTime from wf_qiyuesuoAgentLog where contractId=? and requestId=? and principalId=? and isCancelOrEnd=0";
/* 269 */     recordSet.executeQuery(str1, new Object[] { paramLong, Integer.valueOf(paramInt1), Integer.valueOf(this.user.getUID()) });
/* 270 */     while (recordSet.next()) {
/*     */       try {
/* 272 */         Long long_ = Long.valueOf(recordSet.getString("groupId"));
/* 273 */         this.log.info("cancelAgent--groupId:" + long_);
/*     */         
/* 275 */         arrayList.add(long_);
/* 276 */         arrayList1.add(Arrays.asList(new Long[] { long_ }));
/* 277 */       } catch (Exception exception) {
/* 278 */         this.log.error(exception);
/*     */       } 
/*     */     } 
/* 281 */     if (arrayList.size() <= 0) {
/* 282 */       qYSResponse.setCodeAndMessage(0, "该合同无代理记录，无需撤回！ 合同id:" + paramLong);
/* 283 */       return qYSResponse.toJSONString();
/*     */     } 
/* 285 */     this.log.info("cancelAgent--groupIds.size:" + arrayList.size());
/*     */     
/* 287 */     String str2 = Util.null2String(paramMap.get("serverUrl"));
/* 288 */     String str3 = Util.null2String(paramMap.get("accessKey"));
/* 289 */     String str4 = Util.null2String(paramMap.get("accessSecret"));
/* 290 */     String str5 = Util.null2String(paramMap.get("serverType"));
/* 291 */     QYSAgentService qYSAgentService = getQYSAgentService(str2, str3, str4, str5);
/* 292 */     if (qYSAgentService == null) {
/* 293 */       this.log.error("cancelAgent-- service is null");
/* 294 */       if (str5.equals("1")) {
/* 295 */         qYSResponse.setMessage("合同代理失败，获取代理服务类失败");
/*     */       } else {
/* 297 */         qYSResponse.setCodeAndMessage(0, "契约锁公有云不支持合同代理功能");
/*     */       } 
/* 299 */       return qYSResponse.toJSONString();
/*     */     } 
/* 301 */     qYSResponse = new QYSResponse(qYSAgentService.cancelAgent(arrayList, paramMap));
/* 302 */     if (qYSResponse.getCode() != 0) {
/* 303 */       this.log.error("cancelAgent--撤回代理失败，" + qYSResponse.getMessage());
/* 304 */       saveOperateLog(false);
/* 305 */       return qYSResponse.toJSONString();
/*     */     } 
/* 307 */     String str6 = "update wf_qiyuesuoAgentLog set isCancelOrEnd=1 where groupId=?";
/* 308 */     boolean bool = recordSet.executeBatchSql(str6, arrayList1);
/* 309 */     this.log.info("cancelAgent--end--updateResult:" + bool);
/* 310 */     saveOperateLog(true);
/* 311 */     return qYSResponse.toJSONString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getAgentInfo(int paramInt1, int paramInt2) {
/* 321 */     QYSResponse qYSResponse = new QYSResponse();
/*     */     
/*     */     try {
/* 324 */       Class<?> clazz1 = Class.forName("com.engine.common.util.ServiceUtil");
/* 325 */       Object object1 = clazz1.newInstance();
/*     */       
/* 327 */       Class<?> clazz2 = Class.forName("com.engine.workflow.publicApi.impl.WorkflowRequestInfoPAImpl");
/* 328 */       Method method1 = clazz1.getMethod("getService", new Class[] { Class.class });
/* 329 */       Object object2 = method1.invoke(object1, new Object[] { clazz2 });
/*     */       
/* 331 */       HashMap<Object, Object> hashMap = new HashMap<Object, Object>();
/* 332 */       Method method2 = clazz2.getMethod("getRequestOperatorInfo", new Class[] { User.class, int.class, Map.class });
/* 333 */       Object object3 = method2.invoke(object2, new Object[] { this.user, Integer.valueOf(paramInt1), hashMap });
/* 334 */       if (object3 == null) {
/* 335 */         qYSResponse.setMessage("代理失败，获取代理信息失败");
/* 336 */         return qYSResponse.toJSONString();
/*     */       } 
/* 338 */       JSONObject jSONObject = JSONObject.parseObject(JSONObject.toJSONString(object3));
/* 339 */       JSONArray jSONArray = JSONArray.parseArray(JSONObject.toJSONString(jSONObject.get("data")));
/* 340 */       if (jSONArray != null)
/* 341 */         for (byte b = 0; b < jSONArray.size(); b++) {
/* 342 */           JSONObject jSONObject1 = JSONObject.parseObject(JSONObject.toJSONString(jSONArray.get(b)));
/* 343 */           if (jSONObject1 != null) {
/*     */ 
/*     */             
/* 346 */             int i = Util.getIntValue(Util.null2String(jSONObject1.get("nodeid")));
/* 347 */             if (i == paramInt2) {
/*     */ 
/*     */               
/* 350 */               int j = Util.getIntValue(Util.null2String(jSONObject1.get("agenttype")));
/* 351 */               if (j == 2) {
/* 352 */                 int k = Util.getIntValue(Util.null2String(jSONObject1.get("agentorbyagentid")));
/* 353 */                 int m = Util.getIntValue(Util.null2String(jSONObject1.get("userid")));
/* 354 */                 if (k > 0 && m == this.user.getUID()) {
/* 355 */                   qYSResponse.put("principalId", Integer.valueOf(k));
/* 356 */                   qYSResponse.put("agentorId", Integer.valueOf(m));
/*     */                 } 
/*     */               } 
/*     */             } 
/*     */           } 
/*     */         }  
/* 362 */     } catch (Exception exception) {
/* 363 */       exception.printStackTrace();
/* 364 */       qYSResponse.setMessage("合同代理失败，" + exception.getMessage());
/*     */     } 
/* 366 */     qYSResponse.setCodeAndMessage(0, "");
/* 367 */     return qYSResponse.toJSONString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   Boolean haveAgent(Long paramLong, int paramInt1, int paramInt2, int paramInt3, int paramInt4) {
/* 379 */     RecordSet recordSet1 = new RecordSet();
/* 380 */     RecordSet recordSet2 = new RecordSet();
/* 381 */     String str = "select * from wf_qiyuesuoAgentLog where contractId=? and requestId=? and nodeId=? and agentorId=? and principalId=? and isCancelOrEnd=0";
/* 382 */     recordSet1.executeQuery(str, new Object[] { paramLong, Integer.valueOf(paramInt1), Integer.valueOf(paramInt2), Integer.valueOf(paramInt3), Integer.valueOf(paramInt4) });
/*     */     
/* 384 */     if (recordSet1.next()) {
/* 385 */       int i = Util.getIntValue(Util.null2String(recordSet1.getString("agentId")));
/* 386 */       recordSet2.executeQuery("select agenttype from workflow_agentconditionset where agentId=? and agentType=1", new Object[] { Integer.valueOf(i) });
/* 387 */       if (recordSet2.next()) {
/* 388 */         return Boolean.valueOf(true);
/*     */       }
/* 390 */       recordSet2.executeUpdate("update wf_qiyuesuoAgentLog set isCancelOrEnd=1 where agentId=?", new Object[] { Integer.valueOf(i) });
/* 391 */       return Boolean.valueOf(false);
/*     */     } 
/*     */     
/* 394 */     return Boolean.valueOf(false);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/qiyuesuo/QYSAgentInterface.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */