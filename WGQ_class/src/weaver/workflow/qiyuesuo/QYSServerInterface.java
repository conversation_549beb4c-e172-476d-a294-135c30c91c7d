/*     */ package weaver.workflow.qiyuesuo;
/*     */ 
/*     */ import java.net.URL;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import weaver.conn.ConnStatement;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.workflow.qiyuesuo.bean.QYSOperateLog;
/*     */ import weaver.workflow.qiyuesuo.bean.QYSResponse;
/*     */ import weaver.workflow.qiyuesuo.constant.QYSCategoryType;
/*     */ import weaver.workflow.qiyuesuo.constant.QYSLogType;
/*     */ import weaver.workflow.qiyuesuo.constant.QYSOperateType;
/*     */ import weaver.workflow.qiyuesuo.constant.QYSServerType;
/*     */ import weaver.workflow.qiyuesuo.service.QYSService;
/*     */ import weaver.workflow.qiyuesuo.service.impl.QYSServicePrivateImpl;
/*     */ import weaver.workflow.qiyuesuo.service.impl.QYSServicePublicImpl;
/*     */ import weaver.workflow.qiyuesuo.util.QYSLogUtil;
/*     */ import weaver.workflow.qiyuesuo.util.QYSPropertiesUtil;
/*     */ import weaver.workflow.qiyuesuo.util.QYSUtil;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class QYSServerInterface
/*     */   extends BaseBean
/*     */ {
/*     */   private QYSLogUtil log;
/*     */   private User user;
/*     */   private QYSOperateLog operateLog;
/*     */   private QYSLogType logType;
/*     */   private int operateId;
/*     */   private int requestid;
/*     */   
/*     */   public QYSServerInterface() {
/*  44 */     this.log = new QYSLogUtil(getClass().getName());
/*     */   }
/*     */   
/*     */   public QYSServerInterface(User paramUser) {
/*  48 */     this();
/*  49 */     this.user = paramUser;
/*     */   }
/*     */   
/*     */   public QYSServerInterface(User paramUser, QYSLogType paramQYSLogType) {
/*  53 */     this.user = paramUser;
/*  54 */     this.logType = paramQYSLogType;
/*     */   }
/*     */   
/*     */   public User getUser() {
/*  58 */     return this.user;
/*     */   }
/*     */   
/*     */   public void setUser(User paramUser) {
/*  62 */     this.user = paramUser;
/*     */   }
/*     */   
/*     */   public int getRequestid() {
/*  66 */     return this.requestid;
/*     */   }
/*     */   
/*     */   public void setRequestid(int paramInt) {
/*  70 */     this.requestid = paramInt;
/*     */   }
/*     */   
/*     */   public QYSOperateLog getOperateLog() {
/*  74 */     return this.operateLog;
/*     */   }
/*     */   
/*     */   public void setOperateLog(QYSOperateLog paramQYSOperateLog) {
/*  78 */     this.operateLog = paramQYSOperateLog;
/*     */   }
/*     */   
/*     */   public QYSLogType getLogType() {
/*  82 */     return this.logType;
/*     */   }
/*     */   public void setLogType(QYSLogType paramQYSLogType) {
/*  85 */     this.logType = paramQYSLogType;
/*     */   }
/*     */   
/*     */   public int getOperateId() {
/*  89 */     return this.operateId;
/*     */   }
/*     */   
/*     */   public void setOperateId(int paramInt) {
/*  93 */     this.operateId = paramInt;
/*     */   }
/*     */   
/*     */   public void setOperateLog(QYSCategoryType paramQYSCategoryType, QYSOperateType paramQYSOperateType, int paramInt) {
/*  97 */     if (this.operateId <= 0) {
/*  98 */       this.operateId = Util.getIntValue(this.log.getOperateId(this.user, paramInt));
/*  99 */       this.operateLog = new QYSOperateLog(this.user, getLogType(), paramQYSCategoryType, paramQYSOperateType, paramInt, this.operateId);
/*     */     } 
/*     */   }
/*     */   
/*     */   public void saveOperateLog(boolean paramBoolean) {
/* 104 */     if (this.operateLog != null) {
/* 105 */       this.operateLog.setSuccess(paramBoolean);
/* 106 */       this.log.saveOperateLog(this.operateLog);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<Map> listServerByType(QYSServerType paramQYSServerType) {
/* 117 */     return listServerByType((paramQYSServerType == null) ? "" : paramQYSServerType.getType());
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<Map> listServerByType(String paramString) {
/* 127 */     return listServerByType(paramString, (this.user == null) ? 7 : this.user.getLanguage());
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static List<Map> listServerByType(QYSServerType paramQYSServerType, int paramInt) {
/* 138 */     return listServerByType((paramQYSServerType == null) ? "" : paramQYSServerType.getType(), paramInt);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static List<Map> listServerByType(String paramString, int paramInt) {
/* 149 */     ArrayList<Map> arrayList = new ArrayList();
/* 150 */     paramString = Util.null2String(paramString).trim();
/* 151 */     RecordSet recordSet = new RecordSet();
/*     */     
/* 153 */     if (paramString.isEmpty()) {
/* 154 */       recordSet.executeQuery("select * from wf_qiyuesuoServer order by id", new Object[0]);
/*     */     } else {
/* 156 */       recordSet.executeQuery("select * from wf_qiyuesuoServer where serverType=? order by id", new Object[] { paramString });
/*     */     } 
/* 158 */     while (recordSet.next()) {
/* 159 */       arrayList.add(recordSet2Map(recordSet, paramInt));
/*     */     }
/*     */     
/* 162 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String doSave(HttpServletRequest paramHttpServletRequest) {
/* 171 */     return doSave(QYSUtil.request2Map(paramHttpServletRequest));
/*     */   }
/*     */   
/*     */   public String doSave(Map<String, Object> paramMap) {
/* 175 */     QYSResponse qYSResponse = new QYSResponse();
/* 176 */     if (paramMap == null) {
/* 177 */       qYSResponse.setMessage("契约锁服务保存失败，参数错误！params is null");
/* 178 */       return qYSResponse.toJSONString();
/*     */     } 
/*     */     
/* 181 */     RecordSet recordSet = new RecordSet();
/*     */     
/* 183 */     String str1 = Util.null2String(paramMap.get("source"));
/* 184 */     String str2 = Util.null2String(paramMap.get("src"));
/* 185 */     int i = Util.getIntValue(Util.null2String(paramMap.get("id")), 0);
/* 186 */     String str3 = Util.null2String(paramMap.get("serverName"));
/* 187 */     String str4 = Util.null2String(paramMap.get("serverUrl"));
/* 188 */     String str5 = Util.null2String(paramMap.get("accessKey"));
/* 189 */     String str6 = Util.null2String(paramMap.get("accessSecret"));
/* 190 */     String str7 = "";
/* 191 */     String str8 = Util.null2String(paramMap.get("goPage"));
/* 192 */     String str9 = Util.null2String(paramMap.get("intranetGoPage"));
/* 193 */     String str10 = Util.null2String(paramMap.get("oaIntranetGoPage"));
/* 194 */     String str11 = Util.null2String(paramMap.get("autoNetwork"));
/* 195 */     String str12 = Util.null2String(paramMap.get("networkSegmentIds"));
/* 196 */     String str13 = Util.null2String(paramMap.get("serverType"));
/* 197 */     String str14 = Util.null2String(paramMap.get("isDefaultService"));
/* 198 */     String str15 = Util.null2String(paramMap.get("hasDefaultCompany"));
/* 199 */     String str16 = "1";
/* 200 */     if ("0".equals(str13)) {
/* 201 */       str15 = "1";
/* 202 */       str16 = "";
/* 203 */       str7 = Util.null2String(paramMap.get("integratedLoginId"));
/* 204 */       str14 = "0";
/*     */     } 
/*     */ 
/*     */     
/* 208 */     if ("1".equals(str14) && "1".equals(str13)) {
/* 209 */       String str = "UPDATE wf_qiyuesuoServer SET isDefaultService = '0' ";
/* 210 */       recordSet.executeUpdate(str, new Object[0]);
/*     */     } 
/* 212 */     String str17 = "";
/* 213 */     if ("add".equals(str2)) {
/* 214 */       str17 = "insert into wf_qiyuesuoServer(serverName, serverUrl, accessKey, accessSecret, integratedLoginId, gopage, serverType , isQYSLogin ,isDefaultService, intranetGoPage, autoNetwork, networkSegmentIds, hasDefaultCompany, oaIntranetGoPage) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
/*     */ 
/*     */       
/* 217 */       ConnStatement connStatement = new ConnStatement();
/*     */       try {
/* 219 */         connStatement.setStatementSql(str17);
/* 220 */         connStatement.setString(1, str3);
/* 221 */         connStatement.setString(2, str4);
/* 222 */         connStatement.setString(3, str5);
/* 223 */         connStatement.setString(4, str6);
/* 224 */         connStatement.setString(5, str7);
/* 225 */         connStatement.setString(6, str8);
/* 226 */         connStatement.setString(7, str13);
/* 227 */         connStatement.setString(8, str16);
/* 228 */         connStatement.setString(9, str14);
/* 229 */         connStatement.setString(10, str9);
/* 230 */         connStatement.setString(11, str11);
/* 231 */         connStatement.setString(12, str12);
/* 232 */         connStatement.setString(13, str15);
/* 233 */         connStatement.setString(14, str10);
/* 234 */         connStatement.executeUpdate();
/* 235 */       } catch (Exception exception) {
/* 236 */         recordSet.writeLog(exception);
/*     */       } finally {
/* 238 */         connStatement.close();
/*     */       } 
/* 240 */       recordSet.executeQuery("select * from wf_qiyuesuoServer where serverName=? and accessKey=? and accessSecret=? order by id desc", new Object[] { str3, str5, str6 });
/* 241 */       if (recordSet.next()) {
/* 242 */         i = Util.getIntValue(Util.null2String(recordSet.getString("id")));
/*     */       }
/* 244 */     } else if ("edit".equals(str2)) {
/* 245 */       if (str1.equals("QYS")) {
/* 246 */         str17 = "update wf_qiyuesuoServer ";
/* 247 */         String str = "";
/* 248 */         ArrayList<String> arrayList = new ArrayList();
/* 249 */         if (!str6.isEmpty()) {
/* 250 */           str = str + (str.trim().startsWith("set") ? " ,accessSecret=?" : " set accessSecret=?");
/* 251 */           arrayList.add(str6);
/*     */         } 
/* 253 */         if (!str4.isEmpty()) {
/* 254 */           str = str + (str.trim().startsWith("set") ? " ,serverUrl=?" : " set serverUrl=?");
/* 255 */           arrayList.add(str4);
/*     */         } 
/* 257 */         if (!str8.isEmpty()) {
/* 258 */           str = str + (str.trim().startsWith("set") ? " ,goPage=?" : " set goPage=?");
/* 259 */           arrayList.add(str8);
/*     */         } 
/* 261 */         if (!str9.isEmpty()) {
/* 262 */           str = str + (str.trim().startsWith("set") ? " ,intranetGoPage=?" : " set intranetGoPage=?");
/* 263 */           arrayList.add(str9);
/*     */         } 
/* 265 */         if (!str10.isEmpty()) {
/* 266 */           str = str + (str.trim().startsWith("set") ? " ,oaIntranetGoPage=?" : " set oaIntranetGoPage=?");
/* 267 */           arrayList.add(str10);
/*     */         } 
/* 269 */         str = str + (str.trim().startsWith("set") ? " ,hasDefaultCompany=?" : " set hasDefaultCompany=?");
/* 270 */         arrayList.add(str15);
/* 271 */         str17 = str17 + str + " where accessKey=?";
/* 272 */         arrayList.add(str5);
/* 273 */         recordSet.executeUpdate(str17, arrayList.toArray());
/*     */       } else {
/* 275 */         str17 = "update wf_qiyuesuoServer set serverName=?, serverUrl=?, accessKey=?, accessSecret=?, integratedLoginId=?, goPage=?, serverType=?, isQYSLogin=?,isDefaultService=?, intranetGoPage=?, autoNetwork=?, networkSegmentIds=?, hasDefaultCompany=?, oaIntranetGoPage=?  where id=?";
/*     */ 
/*     */ 
/*     */         
/* 279 */         ConnStatement connStatement = new ConnStatement();
/*     */         try {
/* 281 */           connStatement.setStatementSql(str17);
/* 282 */           connStatement.setString(1, str3);
/* 283 */           connStatement.setString(2, str4);
/* 284 */           connStatement.setString(3, str5);
/* 285 */           connStatement.setString(4, str6);
/* 286 */           connStatement.setString(5, str7);
/* 287 */           connStatement.setString(6, str8);
/* 288 */           connStatement.setString(7, str13);
/* 289 */           connStatement.setString(8, str16);
/* 290 */           connStatement.setString(9, str14);
/* 291 */           connStatement.setString(10, str9);
/* 292 */           connStatement.setString(11, str11);
/* 293 */           connStatement.setString(12, str12);
/* 294 */           connStatement.setString(13, str15);
/* 295 */           connStatement.setString(14, str10);
/* 296 */           connStatement.setInt(15, i);
/* 297 */           connStatement.executeUpdate();
/* 298 */         } catch (Exception exception) {
/* 299 */           recordSet.writeLog(exception);
/*     */         } finally {
/* 301 */           connStatement.close();
/*     */         } 
/*     */       } 
/*     */     } 
/*     */     
/* 306 */     qYSResponse.put("serverId", Integer.valueOf(i));
/* 307 */     qYSResponse.setCodeAndMessage(0, "");
/* 308 */     return qYSResponse.toJSONString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String saveSyncRegulation(Map<String, Object> paramMap, QYSOperateType paramQYSOperateType) {
/* 317 */     setOperateLog(QYSCategoryType.SERVER, paramQYSOperateType, -1);
/* 318 */     QYSResponse qYSResponse = new QYSResponse();
/* 319 */     if (paramMap == null) {
/* 320 */       qYSResponse.setMessage("参数错误，params is null");
/* 321 */       this.log.info("doEditSyncData--" + qYSResponse.getMessage());
/* 322 */       saveOperateLog(false);
/* 323 */       return qYSResponse.toJSONString();
/*     */     } 
/*     */ 
/*     */     
/* 327 */     String str1 = Util.null2String(paramMap.get("syncSubcompanyId"));
/* 328 */     String str2 = Util.null2String(paramMap.get("syncDepartmentId"));
/* 329 */     String str3 = Util.null2String(paramMap.get("syncUserIds"));
/* 330 */     String str4 = Util.null2String(paramMap.get("id"));
/* 331 */     this.log.info("saveSyncRegulation--id:" + str4 + "syncSubcompanyId:" + str1 + "--syncDepartmentId:" + str2);
/* 332 */     RecordSet recordSet = new RecordSet();
/* 333 */     recordSet.executeUpdate("update wf_qiyuesuoServer set syncSubcompanyId=?, syncDepartmentId=?, syncUserIds=? where id=?", new Object[] { str1, str2, str3, str4 });
/*     */ 
/*     */     
/* 336 */     Map map = getServerById(Util.getIntValue(str4));
/* 337 */     String str5 = Util.null2String(map.get("serverType"));
/* 338 */     String str6 = Util.null2String(map.get("serverUrl"));
/* 339 */     String str7 = Util.null2String(map.get("accessKey"));
/* 340 */     String str8 = Util.null2String(map.get("accessSecret"));
/* 341 */     QYSService qYSService = getQYSService(str5, str6, str7, str8);
/* 342 */     if (qYSService == null) {
/* 343 */       qYSResponse.setMessage("保存同步规则数据失败，获取契约锁服务失败");
/* 344 */       this.log.error("saveSyncRegulation--" + qYSResponse.getMessage());
/* 345 */       saveOperateLog(false);
/* 346 */       return qYSResponse.toJSONString();
/*     */     } 
/* 348 */     qYSResponse = new QYSResponse(qYSService.saveSyncRegulation(paramMap));
/* 349 */     if (qYSResponse.getCode() != 0) {
/* 350 */       if (qYSResponse.getMessage().isEmpty()) {
/* 351 */         qYSResponse.setMessage("保存同步规则失败");
/*     */       }
/* 353 */       this.log.info("saveSyncRegulation--" + qYSResponse.getMessage());
/* 354 */       saveOperateLog(false);
/* 355 */       return qYSResponse.toJSONString();
/*     */     } 
/*     */     
/* 358 */     qYSResponse.setCodeAndMessage(0, "");
/* 359 */     saveOperateLog(true);
/* 360 */     return qYSResponse.toJSONString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getSyncRegulation(Map<String, Object> paramMap) {
/* 369 */     setOperateLog(QYSCategoryType.SERVER, QYSOperateType.GET_SYNC_REGULATION, -1);
/* 370 */     QYSResponse qYSResponse = new QYSResponse();
/* 371 */     if (paramMap == null) {
/* 372 */       qYSResponse.setMessage("参数错误，params is null");
/* 373 */       this.log.info("getSyncRegulation--" + qYSResponse.getMessage());
/* 374 */       saveOperateLog(false);
/* 375 */       return qYSResponse.toJSONString();
/*     */     } 
/* 377 */     int i = Util.getIntValue(Util.null2String(paramMap.get("id")));
/* 378 */     this.log.info("getSyncRegulation--id:" + i);
/* 379 */     Map map = getServerById(i);
/* 380 */     String str1 = Util.null2String(map.get("serverType"));
/* 381 */     String str2 = Util.null2String(map.get("serverUrl"));
/* 382 */     String str3 = Util.null2String(map.get("accessKey"));
/* 383 */     String str4 = Util.null2String(map.get("accessSecret"));
/* 384 */     QYSService qYSService = getQYSService(str1, str2, str3, str4);
/* 385 */     if (qYSService == null) {
/* 386 */       qYSResponse.setMessage("获取契约锁同步规则失败，获取契约锁服务失败");
/* 387 */       this.log.info("getSyncRegulation--" + qYSResponse.getMessage());
/* 388 */       saveOperateLog(false);
/* 389 */       return qYSResponse.toJSONString();
/*     */     } 
/*     */     
/* 392 */     qYSResponse = new QYSResponse(qYSService.getSyncRegulation(paramMap));
/* 393 */     if (qYSResponse.getCode() != 0) {
/* 394 */       if (qYSResponse.getMessage().isEmpty()) {
/* 395 */         qYSResponse.setMessage("获取契约锁同步规则失败");
/*     */       }
/* 397 */       saveOperateLog(false);
/* 398 */       return qYSResponse.toJSONString();
/*     */     } 
/* 400 */     qYSResponse.setCodeAndMessage(0, "");
/* 401 */     saveOperateLog(true);
/* 402 */     return qYSResponse.toJSONString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String syncHrm(Map<String, Object> paramMap) {
/* 411 */     setOperateLog(QYSCategoryType.SERVER, QYSOperateType.SYNC_HRM, -1);
/* 412 */     QYSResponse qYSResponse = new QYSResponse();
/* 413 */     if (paramMap == null) {
/* 414 */       qYSResponse.setMessage("同步人员失败，参数错误，params is null");
/* 415 */       this.log.error("syncHrm--" + qYSResponse.getMessage());
/* 416 */       saveOperateLog(false);
/* 417 */       return qYSResponse.toJSONString();
/*     */     } 
/* 419 */     int i = Util.getIntValue(Util.null2String(paramMap.get("id")));
/* 420 */     this.log.info("syncHrm--id:" + i);
/* 421 */     Map map = getServerById(i);
/* 422 */     String str1 = Util.null2String(map.get("serverType"));
/* 423 */     String str2 = Util.null2String(map.get("serverUrl"));
/* 424 */     String str3 = Util.null2String(map.get("accessKey"));
/* 425 */     String str4 = Util.null2String(map.get("accessSecret"));
/* 426 */     QYSService qYSService = getQYSService(str1, str2, str3, str4);
/* 427 */     if (qYSService == null) {
/* 428 */       qYSResponse.setMessage("获取契约锁同步规则失败，获取契约锁服务失败");
/* 429 */       this.log.info("syncHrm--" + qYSResponse.getMessage());
/* 430 */       saveOperateLog(false);
/* 431 */       return qYSResponse.toJSONString();
/*     */     } 
/* 433 */     qYSResponse = new QYSResponse(qYSService.syncHrm(paramMap));
/* 434 */     if (qYSResponse.getCode() != 0) {
/* 435 */       if (qYSResponse.getMessage().isEmpty()) {
/* 436 */         qYSResponse.setMessage("同步人员失败");
/*     */       }
/* 438 */       this.log.info("syncHrm--" + qYSResponse.getMessage());
/* 439 */       saveOperateLog(false);
/* 440 */       return qYSResponse.toJSONString();
/*     */     } 
/*     */     
/* 443 */     qYSResponse.setCodeAndMessage(0, "");
/* 444 */     saveOperateLog(true);
/* 445 */     return qYSResponse.toJSONString();
/*     */   }
/*     */   
/*     */   private static Map recordSet2Map(RecordSet paramRecordSet, int paramInt) {
/* 449 */     HashMap<Object, Object> hashMap = new HashMap<Object, Object>();
/* 450 */     if (paramRecordSet == null) {
/* 451 */       return hashMap;
/*     */     }
/*     */     
/* 454 */     hashMap.put("serverId", paramRecordSet.getString("id"));
/* 455 */     hashMap.put("serverName", QYSUtil.formatMultiLang(paramRecordSet.getString("serverName"), paramInt));
/* 456 */     hashMap.put("serverUrl", QYSUtil.formatMultiLang(paramRecordSet.getString("serverUrl"), paramInt));
/* 457 */     hashMap.put("accessKey", QYSUtil.formatMultiLang(paramRecordSet.getString("accessKey"), paramInt));
/* 458 */     hashMap.put("accessSecret", QYSUtil.formatMultiLang(paramRecordSet.getString("accessSecret"), paramInt));
/* 459 */     hashMap.put("integratedLoginId", QYSUtil.formatMultiLang(paramRecordSet.getString("integratedLoginId"), paramInt));
/* 460 */     hashMap.put("goPage", QYSUtil.formatMultiLang(paramRecordSet.getString("goPage"), paramInt));
/* 461 */     hashMap.put("intranetGoPage", QYSUtil.formatMultiLang(paramRecordSet.getString("intranetGoPage"), paramInt));
/* 462 */     hashMap.put("oaIntranetGoPage", QYSUtil.formatMultiLang(paramRecordSet.getString("oaIntranetGoPage"), paramInt));
/* 463 */     hashMap.put("autoNetwork", paramRecordSet.getString("autoNetwork"));
/* 464 */     hashMap.put("networkSegmentIds", paramRecordSet.getString("networkSegmentIds"));
/* 465 */     hashMap.put("serverType", paramRecordSet.getString("serverType"));
/* 466 */     hashMap.put("isQYSLogin", paramRecordSet.getString("isQYSLogin"));
/* 467 */     hashMap.put("isDefaultService", paramRecordSet.getString("isDefaultService"));
/* 468 */     hashMap.put("syncSubcompanyId", paramRecordSet.getString("syncSubcompanyId"));
/* 469 */     hashMap.put("syncDepartmentId", paramRecordSet.getString("syncDepartmentId"));
/* 470 */     hashMap.put("syncUserIds", paramRecordSet.getString("syncUserIds"));
/* 471 */     hashMap.put("hasDefaultCompany", paramRecordSet.getString("hasDefaultCompany"));
/* 472 */     return hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map getServerById(int paramInt) {
/* 482 */     return getServerById(paramInt, (this.user == null) ? 7 : this.user.getLanguage());
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static Map getServerById(int paramInt1, int paramInt2) {
/* 493 */     Map map = new HashMap<Object, Object>();
/* 494 */     if (paramInt1 <= 0) {
/* 495 */       return map;
/*     */     }
/*     */     
/* 498 */     RecordSet recordSet = new RecordSet();
/* 499 */     recordSet.executeQuery("select * from wf_qiyuesuoServer where id=? order by id", new Object[] { Integer.valueOf(paramInt1) });
/* 500 */     if (recordSet.next()) {
/* 501 */       map = recordSet2Map(recordSet, paramInt2);
/*     */     }
/*     */     
/* 504 */     return map;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public QYSService getQYSService(String paramString1, String paramString2, String paramString3, String paramString4) {
/*     */     QYSServicePublicImpl qYSServicePublicImpl;
/* 517 */     paramString2 = Util.null2String(paramString2);
/* 518 */     if (paramString2.isEmpty()) {
/* 519 */       this.log.error("getQYSService--契约锁API服务地址为空！");
/* 520 */       return null;
/*     */     } 
/*     */     
/* 523 */     paramString3 = Util.null2String(paramString3).trim();
/* 524 */     paramString4 = Util.null2String(paramString4).trim();
/*     */ 
/*     */     
/* 527 */     QYSServicePrivateImpl qYSServicePrivateImpl = null;
/* 528 */     if ("1".equals(Util.null2String(paramString1).trim())) {
/* 529 */       qYSServicePrivateImpl = new QYSServicePrivateImpl(paramString2, paramString3, paramString4, this.user, this.requestid, this.operateId);
/*     */     }
/* 531 */     else if (paramString3 != null && paramString4 != null) {
/* 532 */       qYSServicePublicImpl = new QYSServicePublicImpl(paramString2, paramString3, paramString4, this.user, this.requestid, this.operateId);
/*     */     } 
/*     */ 
/*     */     
/* 536 */     return (QYSService)qYSServicePublicImpl;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String checkServer(String paramString) {
/* 544 */     RecordSet recordSet = new RecordSet();
/* 545 */     QYSResponse qYSResponse = new QYSResponse();
/* 546 */     if (paramString == null || paramString.isEmpty()) return qYSResponse.toJSONString(); 
/* 547 */     String str1 = "";
/* 548 */     String str2 = "";
/* 549 */     String str3 = "";
/* 550 */     String str4 = "";
/* 551 */     recordSet.executeQuery("select serverType,serverUrl,accesskey,accessSecret from wf_qiyuesuoServer where id=" + paramString, new Object[0]);
/* 552 */     if (recordSet.next()) {
/* 553 */       str4 = Util.null2String(recordSet.getString("serverType"));
/* 554 */       str1 = Util.null2String(recordSet.getString("serverUrl"));
/* 555 */       str2 = Util.null2String(recordSet.getString("accessKey"));
/* 556 */       str3 = Util.null2String(recordSet.getString("accessSecret"));
/*     */     } else {
/* 558 */       return qYSResponse.toJSONString();
/*     */     } 
/* 560 */     return checkServer(str4, str1, str2, str3);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String checkServer(QYSServerType paramQYSServerType, String paramString) {
/* 570 */     return checkServer((paramQYSServerType == null) ? "" : paramQYSServerType.getType(), paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String checkServer(String paramString1, String paramString2) {
/* 581 */     return checkServer(paramString1, paramString2, (String)null, (String)null);
/*     */   }
/*     */   public String getServerVersionByUrl(Map paramMap) {
/* 584 */     QYSResponse qYSResponse = new QYSResponse();
/* 585 */     if (paramMap != null) {
/* 586 */       return checkServer(Util.null2String(paramMap.get("serverType")), Util.null2String(paramMap.get("serverUrl")), Util.null2String(paramMap.get("accessKey")), Util.null2String(paramMap.get("accessSecret")));
/*     */     }
/* 588 */     return qYSResponse.toJSONString();
/*     */   } public Map getServerMapByUrl(String paramString) {
/*     */     URL uRL;
/* 591 */     Map map = null;
/*     */     
/*     */     try {
/* 594 */       uRL = new URL(paramString);
/* 595 */     } catch (Exception exception) {
/* 596 */       this.log.error(exception.getLocalizedMessage());
/* 597 */       return map;
/*     */     } 
/* 599 */     List<Map> list = listServerByType(QYSServerType.PRIVATE);
/* 600 */     if (list.isEmpty()) {
/* 601 */       return map;
/*     */     }
/* 603 */     boolean bool = false;
/* 604 */     for (Map map1 : list) {
/* 605 */       if (map1 == null) {
/*     */         continue;
/*     */       }
/* 608 */       String[] arrayOfString = { Util.null2String(map1.get("goPage")).trim(), Util.null2String(map1.get("intranetGoPage")).trim() };
/*     */ 
/*     */ 
/*     */       
/* 612 */       for (String str : arrayOfString) {
/* 613 */         if (!str.isEmpty()) {
/*     */           URL uRL1;
/*     */ 
/*     */           
/*     */           try {
/* 618 */             uRL1 = new URL(str);
/* 619 */           } catch (Exception exception) {}
/*     */ 
/*     */           
/* 622 */           bool = (uRL.getProtocol().equalsIgnoreCase(uRL1.getProtocol()) && uRL.getHost().equalsIgnoreCase(uRL1.getHost()) && uRL.getPort() == uRL1.getPort()) ? true : false;
/*     */ 
/*     */           
/* 625 */           if (bool)
/*     */             break; 
/*     */         } 
/*     */       } 
/* 629 */       if (bool) {
/* 630 */         map = map1;
/*     */         break;
/*     */       } 
/*     */     } 
/* 634 */     return map;
/*     */   }
/*     */   public String getServerVersionByUrl(String paramString) {
/* 637 */     QYSResponse qYSResponse = new QYSResponse();
/* 638 */     Map map = getServerMapByUrl(paramString);
/* 639 */     if (map != null) {
/* 640 */       return checkServer(Util.null2String(map.get("serverType")), Util.null2String(map.get("serverUrl")), Util.null2String(map.get("accessKey")), Util.null2String(map.get("accessSecret")));
/*     */     }
/* 642 */     return qYSResponse.toJSONString();
/*     */   }
/*     */   public String isSupportNewSso(String paramString) {
/* 645 */     QYSResponse qYSResponse = new QYSResponse();
/* 646 */     Map map = getServerMapByUrl(paramString);
/* 647 */     if (map != null) {
/* 648 */       return isNewSso(Util.null2String(map.get("serverType")), Util.null2String(map.get("serverUrl")), Util.null2String(map.get("accessKey")), Util.null2String(map.get("accessSecret")));
/*     */     }
/* 650 */     return qYSResponse.toJSONString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String checkServer(QYSServerType paramQYSServerType, String paramString1, String paramString2, String paramString3) {
/* 662 */     return checkServer((paramQYSServerType == null) ? "" : paramQYSServerType.getType(), paramString1, paramString2, paramString3);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String checkServer(String paramString1, String paramString2, String paramString3, String paramString4) {
/* 675 */     paramString1 = Util.null2String(paramString1);
/* 676 */     paramString2 = Util.null2String(paramString2);
/* 677 */     this.log.info("checkServer--start--serverType:" + paramString1 + "--serverUrl:" + paramString2);
/*     */     
/* 679 */     QYSResponse qYSResponse = new QYSResponse();
/* 680 */     if (paramString2.isEmpty()) {
/* 681 */       qYSResponse.setMessage("契约锁API服务地址为空");
/* 682 */       this.log.error("checkServer--" + qYSResponse.getMessage());
/* 683 */       return qYSResponse.toJSONString();
/*     */     } 
/*     */     
/* 686 */     QYSService qYSService = getQYSService(paramString1, paramString2, paramString3, paramString4);
/* 687 */     if (qYSService == null) {
/* 688 */       qYSResponse.setMessage("获取契约锁服务失败，serverUrl：" + paramString2);
/* 689 */       this.log.error("checkServer--" + qYSResponse.getMessage());
/* 690 */       return qYSResponse.toJSONString();
/*     */     } 
/*     */     
/* 693 */     qYSResponse = new QYSResponse(qYSService.checkHealth());
/* 694 */     if (0 != qYSResponse.getCode()) {
/* 695 */       this.log.error("checkServer--契约锁服务检查失败！" + qYSResponse.getMessage());
/* 696 */       return qYSResponse.toJSONString();
/*     */     } 
/*     */     
/* 699 */     qYSResponse = new QYSResponse(qYSService.checkVersion());
/* 700 */     if (0 != qYSResponse.getCode()) {
/* 701 */       this.log.error("checkServer--校验sdk是否可用失败！" + qYSResponse.getMessage());
/* 702 */       return qYSResponse.toJSONString();
/*     */     } 
/*     */     
/* 705 */     qYSResponse.put("checkResultMessage", SystemEnv.getHtmlLabelName(qYSResponse.getBooleanValue("checkResult") ? 517635 : 517637, this.user.getLanguage()));
/* 706 */     this.log.info("checkServer--start--serverType:" + paramString1 + "--serverUrl:" + paramString2);
/* 707 */     return qYSResponse.toJSONString();
/*     */   }
/*     */   public String isNewSso(String paramString1, String paramString2, String paramString3, String paramString4) {
/* 710 */     paramString1 = Util.null2String(paramString1);
/* 711 */     paramString2 = Util.null2String(paramString2);
/* 712 */     this.log.info("isNewSso--start--serverType:" + paramString1 + "--serverUrl:" + paramString2 + "--accessKey:" + paramString3 + "--accessSecret:" + paramString4);
/* 713 */     QYSResponse qYSResponse = new QYSResponse();
/* 714 */     if (paramString2.isEmpty()) {
/* 715 */       qYSResponse.setMessage("契约锁API服务地址为空");
/* 716 */       this.log.error("isNewSso--" + qYSResponse.getMessage());
/* 717 */       return qYSResponse.toJSONString();
/*     */     } 
/* 719 */     QYSService qYSService = getQYSService(paramString1, paramString2, paramString3, paramString4);
/* 720 */     if (qYSService == null) {
/* 721 */       qYSResponse.setMessage("获取契约锁服务失败，serverUrl：" + paramString2);
/* 722 */       this.log.error("isNewSso--" + qYSResponse.getMessage());
/* 723 */       return qYSResponse.toJSONString();
/*     */     } 
/* 725 */     boolean bool = qYSService.isNewSso();
/* 726 */     this.log.info("isNewSso--isNewSso:" + bool);
/* 727 */     qYSResponse.put("isNewSso", Boolean.valueOf(bool));
/* 728 */     qYSResponse.setCodeAndMessage(0, "");
/* 729 */     return qYSResponse.toJSONString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getQYSInfo(Map<String, Object> paramMap, QYSOperateType paramQYSOperateType) {
/* 738 */     setOperateLog(QYSCategoryType.ELECTRONIC, paramQYSOperateType, 0);
/* 739 */     QYSResponse qYSResponse1 = new QYSResponse();
/* 740 */     String str1 = Util.null2String(paramMap.get("serverUrl"));
/* 741 */     String str2 = Util.null2String(paramMap.get("src"));
/* 742 */     String str3 = Util.null2String(paramMap.get("serverType"));
/* 743 */     QYSPropertiesUtil qYSPropertiesUtil = new QYSPropertiesUtil();
/* 744 */     String str4 = Util.null2String(paramMap.get("accessKey"));
/* 745 */     String str5 = Util.null2String(paramMap.get("accessSecret"));
/* 746 */     if (str2.equals("add")) {
/* 747 */       str4 = qYSPropertiesUtil.getPropValue("QYS_FIRST_ACCESS_TOKEN");
/* 748 */       str5 = qYSPropertiesUtil.getPropValue("QYS_FIRST_ACCESS_SECRET");
/* 749 */       str4 = str4.isEmpty() ? "J6WPW25eHj" : str4;
/* 750 */       str5 = str5.isEmpty() ? "qzVjMfzFD8WQ9pKyoTEFOmPgz0mT9G" : str5;
/*     */     } 
/* 752 */     String str6 = "";
/* 753 */     RecordSet recordSet = new RecordSet();
/* 754 */     recordSet.executeQuery("select oaaddress from SystemSet", new Object[0]);
/* 755 */     if (recordSet.next()) {
/* 756 */       str6 = Util.null2String(recordSet.getString("oaaddress"));
/* 757 */       if (!str6.isEmpty()) {
/* 758 */         paramMap.put("oaAddress", str6);
/*     */       }
/*     */     } 
/* 761 */     this.log.info("getQYSInfo--serverUrl:" + str1 + "--accessToken:" + str4 + "--accessSecret:" + str5);
/* 762 */     QYSService qYSService = getQYSService(str3, str1, str4, str5);
/* 763 */     if (qYSService == null) {
/* 764 */       qYSResponse1.setMessage("获取契约锁服务失败,serverUrl:" + str1 + "  accessToken:" + str4 + "  accessSecret:" + str5);
/* 765 */       this.log.info("getQYSInfo--" + qYSResponse1.getMessage());
/* 766 */       saveOperateLog((qYSResponse1.getCode() == 0));
/* 767 */       return qYSResponse1.toJSONString();
/*     */     } 
/* 769 */     QYSResponse qYSResponse2 = new QYSResponse(qYSService.checkVersion());
/* 770 */     String str7 = Util.null2String(qYSResponse2.getString("serverVersion"));
/* 771 */     byte b = 0;
/* 772 */     if (!str7.isEmpty()) {
/* 773 */       str7 = str7.replaceAll("\\.", "");
/* 774 */       if (!str7.isEmpty() && str7.length() > 3) {
/* 775 */         str7 = str7.substring(0, 3);
/*     */       }
/*     */       try {
/* 778 */         b = str7.isEmpty() ? 0 : Integer.parseInt(str7);
/* 779 */       } catch (Exception exception) {
/* 780 */         this.log.error(exception);
/*     */       } 
/*     */     } 
/* 783 */     qYSResponse1 = new QYSResponse(qYSService.getQYSInfo(paramMap));
/* 784 */     qYSResponse1.put("version", Integer.valueOf(b));
/* 785 */     if (qYSResponse1.getCode() != 0) {
/* 786 */       saveOperateLog((qYSResponse1.getCode() == 0));
/* 787 */       if (b < 'ƨ' && b != 0) {
/* 788 */         qYSResponse1.setCodeAndMessage(0, "契约锁服务版本不支持一键部署");
/* 789 */         return qYSResponse1.toJSONString();
/*     */       } 
/* 791 */       return qYSResponse1.toJSONString();
/*     */     } 
/* 793 */     qYSResponse1.setCodeAndMessage(0, "");
/* 794 */     saveOperateLog((qYSResponse1.getCode() == 0));
/* 795 */     return qYSResponse1.toJSONString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String checkVersion(String paramString1, String paramString2, String paramString3, String paramString4) {
/* 804 */     QYSResponse qYSResponse = new QYSResponse();
/* 805 */     QYSService qYSService = getQYSService(paramString1, paramString2, paramString3, paramString4);
/* 806 */     if (qYSService == null) {
/* 807 */       qYSResponse.setMessage("获取契约锁服务失败,serverUrl:" + paramString2 + "  accesskey:" + paramString3 + "  accessSecret:" + paramString4);
/* 808 */       this.log.info("checkVersion--" + qYSResponse.getMessage());
/* 809 */       return qYSResponse.toJSONString();
/*     */     } 
/* 811 */     qYSResponse = new QYSResponse(qYSService.checkVersion());
/* 812 */     if (qYSResponse.getCode() != 0) {
/* 813 */       if (qYSResponse.getMessage().isEmpty()) {
/* 814 */         qYSResponse.setMessage("版本校验失败");
/*     */       }
/* 816 */       return qYSResponse.toJSONString();
/*     */     } 
/* 818 */     qYSResponse.setCodeAndMessage(0, "");
/* 819 */     return qYSResponse.toJSONString();
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/qiyuesuo/QYSServerInterface.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */