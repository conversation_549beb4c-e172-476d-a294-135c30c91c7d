/*     */ package weaver.workflow.qiyuesuo;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.conn.ConnStatement;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.TimeUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.workflow.qiyuesuo.bean.QYSOperateLog;
/*     */ import weaver.workflow.qiyuesuo.bean.QYSResponse;
/*     */ import weaver.workflow.qiyuesuo.constant.QYSCategoryType;
/*     */ import weaver.workflow.qiyuesuo.constant.QYSLogType;
/*     */ import weaver.workflow.qiyuesuo.constant.QYSOperateType;
/*     */ import weaver.workflow.qiyuesuo.constant.QYSServerType;
/*     */ import weaver.workflow.qiyuesuo.service.QYSService;
/*     */ import weaver.workflow.qiyuesuo.service.impl.QYSServicePrivateImpl;
/*     */ import weaver.workflow.qiyuesuo.service.impl.QYSServicePublicImpl;
/*     */ import weaver.workflow.qiyuesuo.util.QYSLogUtil;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class QYSCategoryInterface
/*     */   extends BaseBean
/*     */ {
/*     */   private QYSLogUtil log;
/*     */   private int operateId;
/*     */   private QYSOperateLog operateLog;
/*     */   private User user;
/*     */   private QYSLogType logType;
/*     */   
/*     */   public void setOperateLog(QYSCategoryType paramQYSCategoryType, QYSOperateType paramQYSOperateType, int paramInt) {
/*  38 */     if (this.operateId <= 0) {
/*  39 */       this.operateId = Util.getIntValue(this.log.getOperateId(this.user, paramInt));
/*  40 */       this.operateLog = new QYSOperateLog(this.user, getLogType(), paramQYSCategoryType, paramQYSOperateType, paramInt, this.operateId);
/*     */     } else {
/*     */       return;
/*     */     } 
/*     */   }
/*     */   public void saveOperateLog(boolean paramBoolean) {
/*  46 */     this.operateLog.setSuccess(paramBoolean);
/*  47 */     this.log.saveOperateLog(this.operateLog);
/*     */   }
/*     */   public QYSCategoryInterface() {
/*  50 */     this.log = new QYSLogUtil(getClass().getName());
/*     */   }
/*     */   public QYSCategoryInterface(User paramUser) {
/*  53 */     this();
/*  54 */     this.user = paramUser;
/*     */   }
/*     */   public QYSCategoryInterface(User paramUser, QYSLogType paramQYSLogType) {
/*  57 */     this(paramUser);
/*  58 */     this.logType = paramQYSLogType;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String listCategory() {
/*  66 */     setOperateLog(QYSCategoryType.OTHER, QYSOperateType.CATEGORY, 0);
/*  67 */     String str1 = TimeUtil.getCurrentTimeString();
/*  68 */     this.log.info("listCategory--start--currentDateTime:" + str1);
/*     */     
/*  70 */     QYSResponse qYSResponse = new QYSResponse();
/*     */     
/*  72 */     List<Map> list = (new QYSServerInterface()).listServerByType("");
/*  73 */     this.log.info("listCategory--servers.size:" + list.size());
/*  74 */     if (list.isEmpty()) {
/*  75 */       qYSResponse.setMessage("契约锁服务未配置");
/*  76 */       this.log.error("listCategory--" + qYSResponse.getMessage());
/*  77 */       saveOperateLog(false);
/*  78 */       return qYSResponse.toJSONString();
/*     */     } 
/*  80 */     String str2 = Util.null2String(getPropValue("QYS", "allPrivateServie"));
/*  81 */     boolean bool1 = false;
/*  82 */     if ("true".equals(str2)) {
/*  83 */       bool1 = true;
/*     */     }
/*  85 */     boolean bool2 = false;
/*  86 */     boolean bool3 = false;
/*  87 */     boolean bool4 = false;
/*  88 */     boolean bool5 = false;
/*  89 */     for (Map map : list) {
/*  90 */       if (map != null && "1".equals(map.get("isDefaultService")) && QYSServerType.PRIVATE.getType().equals(map.get("serverType"))) {
/*     */         
/*  92 */         bool4 = true;
/*     */         break;
/*     */       } 
/*     */     } 
/*  96 */     this.log.info("listCategory existDefaultService:" + bool4);
/*  97 */     RecordSet recordSet1 = new RecordSet();
/*  98 */     ArrayList<String> arrayList = new ArrayList();
/*  99 */     for (byte b = 0; b < list.size(); b++) {
/* 100 */       Map map = list.get(b);
/* 101 */       if (map != null) {
/*     */ 
/*     */ 
/*     */         
/* 105 */         String str4 = Util.null2String(map.get("serverType"));
/* 106 */         boolean bool = "1".equals(map.get("isDefaultService"));
/* 107 */         String str5 = Util.null2String(map.get("serverId"));
/* 108 */         this.log.info("listCategory--i:" + b + "--serverType:" + str4);
/* 109 */         if (bool1 || 
/* 110 */           !QYSServerType.PRIVATE.getType().equals(str4) || (
/* 111 */           bool4 ? (
/* 112 */           !bool || bool2) : 
/*     */ 
/*     */ 
/*     */           
/* 116 */           bool2)) {
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */           
/* 124 */           QYSService qYSService = getQYSService(map);
/* 125 */           if (qYSService == null) {
/* 126 */             this.log.info("listCategory--i:" + b + " ,serverId = " + str5 + " --获取契约锁服务失败");
/*     */           }
/*     */           else {
/*     */             
/* 130 */             List list1 = qYSService.listCategory(str5);
/* 131 */             if (list1 == null)
/* 132 */             { this.log.info("listCategory--i:" + b + " ,serverId = " + str5 + " --获取业务分类失败！"); }
/*     */             else
/*     */             
/* 135 */             { bool5 = true;
/* 136 */               this.log.info("listCategory--i:" + b + "--list.size:" + list1.size());
/*     */               
/* 138 */               if (QYSServerType.PRIVATE.getType().equals(str4) || !bool3);
/*     */ 
/*     */ 
/*     */               
/* 142 */               String str6 = "insert into wf_qiyuesuoCategory (serverType, executeDateTime, categoryId, categoryName, categoryType, tenantId, createDateTime, isPrimary, status, groupId,serverId ) values (?,?,?,?,?,?,?,?,?,?,?)";
/*     */ 
/*     */               
/* 145 */               String str7 = "update wf_qiyuesuoCategory set executeDateTime=?, categoryName=?, categoryType=?, createDateTime=?, isPrimary=?, status=?, groupId=?, isdelete = 0,serverId=?  where categoryId=? and tenantId=? and serverType = ?";
/*     */ 
/*     */               
/* 148 */               String str8 = " select * from wf_qiyuesuoCategory where categoryId = ? and tenantId = ? and serverType = ? ";
/* 149 */               for (Map map1 : list1) {
/* 150 */                 if (map1 == null) {
/*     */                   continue;
/*     */                 }
/* 153 */                 String str9 = Util.null2String(map1.get("categoryId"));
/* 154 */                 String str10 = Util.null2String(map1.get("tenantId"));
/* 155 */                 String str11 = Util.null2String(map1.get("categoryType"));
/* 156 */                 arrayList.add(str4 + "_" + str10 + "_" + str9);
/* 157 */                 ConnStatement connStatement = new ConnStatement();
/* 158 */                 recordSet1.executeQuery(str8, new Object[] { str9, str10, str4 });
/*     */                 try {
/* 160 */                   if (recordSet1.next()) {
/* 161 */                     connStatement.setStatementSql(str7);
/* 162 */                     connStatement.setString(1, str1);
/* 163 */                     connStatement.setString(2, Util.null2String(map1.get("categoryName")));
/* 164 */                     connStatement.setString(3, str11);
/* 165 */                     connStatement.setString(4, Util.null2String(map1.get("createDateTime")));
/* 166 */                     connStatement.setString(5, Util.null2String(map1.get("isPrimary")));
/* 167 */                     connStatement.setInt(6, Util.getIntValue(Util.null2String(map1.get("status"))));
/* 168 */                     connStatement.setString(7, Util.null2String(map1.get("groupId")));
/* 169 */                     connStatement.setString(8, str5);
/* 170 */                     connStatement.setString(9, str9);
/* 171 */                     connStatement.setString(10, str10);
/* 172 */                     connStatement.setString(11, str4);
/* 173 */                     connStatement.executeUpdate();
/*     */                   } else {
/* 175 */                     connStatement.setStatementSql(str6);
/* 176 */                     connStatement.setString(1, str4);
/* 177 */                     connStatement.setString(2, str1);
/* 178 */                     connStatement.setString(3, str9);
/* 179 */                     connStatement.setString(4, Util.null2String(map1.get("categoryName")));
/* 180 */                     connStatement.setString(5, str11);
/* 181 */                     connStatement.setString(6, str10);
/* 182 */                     connStatement.setString(7, Util.null2String(map1.get("createDateTime")));
/* 183 */                     connStatement.setString(8, Util.null2String(map1.get("isPrimary")));
/* 184 */                     connStatement.setInt(9, Util.getIntValue(Util.null2String(map1.get("status"))));
/* 185 */                     connStatement.setString(10, Util.null2String(map1.get("groupId")));
/* 186 */                     connStatement.setString(11, str5);
/* 187 */                     connStatement.executeUpdate();
/*     */                   } 
/* 189 */                 } catch (Exception exception) {
/* 190 */                   this.log.error(exception);
/*     */                 } finally {
/* 192 */                   connStatement.close();
/*     */                 } 
/*     */               } 
/*     */               
/* 196 */               if (QYSServerType.PRIVATE.getType().equals(str4))
/* 197 */               { bool2 = true; }
/*     */               else
/* 199 */               { bool3 = true; }  } 
/*     */           } 
/*     */         } 
/*     */       } 
/* 203 */     }  recordSet1.executeQuery("select * from wf_qiyuesuoCategory", new Object[0]);
/* 204 */     String str3 = " update wf_qiyuesuoCategory set isDelete = 1 where serverType = ? and tenantId = ? and categoryId = ?";
/* 205 */     RecordSet recordSet2 = new RecordSet();
/* 206 */     while (recordSet1.next()) {
/* 207 */       String str4 = Util.null2String(recordSet1.getString("serverType"));
/* 208 */       String str5 = Util.null2String(recordSet1.getString("tenantId"));
/* 209 */       String str6 = Util.null2String(recordSet1.getString("categoryId"));
/* 210 */       if (!arrayList.contains(str4 + "_" + str5 + "_" + str6)) {
/* 211 */         recordSet2.executeUpdate(str3, new Object[] { str4, str5, str6 });
/*     */       }
/*     */     } 
/* 214 */     qYSResponse.setCodeAndMessage(0, "");
/* 215 */     this.log.info("listCategory--end");
/* 216 */     saveOperateLog(bool5);
/* 217 */     return qYSResponse.toJSONString();
/*     */   }
/*     */   private QYSService getQYSService(Map paramMap) {
/*     */     QYSServicePublicImpl qYSServicePublicImpl;
/* 221 */     if (paramMap == null) {
/* 222 */       return null;
/*     */     }
/*     */     
/* 225 */     String str1 = Util.null2String(paramMap.get("serverUrl"));
/* 226 */     String str2 = Util.null2String(paramMap.get("accessKey"));
/* 227 */     String str3 = Util.null2String(paramMap.get("accessSecret"));
/* 228 */     if (str1.isEmpty() || str2.isEmpty() || str3.isEmpty()) {
/* 229 */       this.log.error("getQYSService--契约锁服务配置不完整");
/* 230 */       return null;
/*     */     } 
/*     */ 
/*     */     
/* 234 */     if (QYSServerType.PRIVATE.getType().equals(Util.null2String(paramMap.get("serverType")))) {
/* 235 */       QYSServicePrivateImpl qYSServicePrivateImpl = new QYSServicePrivateImpl(str1, str2, str3, this.user, 0, this.operateId);
/*     */     } else {
/* 237 */       qYSServicePublicImpl = new QYSServicePublicImpl(str1, str2, str3, this.user, 0, this.operateId);
/*     */     } 
/*     */     
/* 240 */     return (QYSService)qYSServicePublicImpl;
/*     */   }
/*     */   public QYSOperateLog getOperateLog() {
/* 243 */     return this.operateLog;
/*     */   }
/*     */   public void setOperateLog(QYSOperateLog paramQYSOperateLog) {
/* 246 */     this.operateLog = paramQYSOperateLog;
/*     */   }
/*     */   public User getUser() {
/* 249 */     return this.user;
/*     */   }
/*     */   public void setUser(User paramUser) {
/* 252 */     this.user = paramUser;
/*     */   }
/*     */   public QYSLogType getLogType() {
/* 255 */     return this.logType;
/*     */   }
/*     */   public void setLogType(QYSLogType paramQYSLogType) {
/* 258 */     this.logType = paramQYSLogType;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/qiyuesuo/QYSCategoryInterface.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */