/*     */ package weaver.workflow.qiyuesuo;
/*     */ 
/*     */ import java.util.Calendar;
/*     */ import weaver.conn.RecordSetTrans;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.TimeUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.workflow.qiyuesuo.bean.QYSOperateLog;
/*     */ import weaver.workflow.qiyuesuo.bean.QYSResponse;
/*     */ import weaver.workflow.qiyuesuo.constant.QYSCategoryType;
/*     */ import weaver.workflow.qiyuesuo.constant.QYSLogType;
/*     */ import weaver.workflow.qiyuesuo.constant.QYSOperateType;
/*     */ import weaver.workflow.qiyuesuo.util.QYSLogUtil;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class QYSUserOperateLogInterface
/*     */   extends BaseBean
/*     */ {
/*     */   QYSLogUtil log;
/*     */   User user;
/*     */   private int operateId;
/*     */   private QYSOperateLog operateLog;
/*     */   private QYSLogType logType;
/*  33 */   private final int keepLogMonths = 3;
/*     */   
/*     */   public QYSUserOperateLogInterface() {
/*  36 */     this.log = new QYSLogUtil(getClass().getName());
/*     */   }
/*     */   public QYSLogType getLogType() {
/*  39 */     return (this.logType == null) ? QYSLogType.SCHEDULE : this.logType;
/*     */   }
/*     */   public QYSUserOperateLogInterface(User paramUser) {
/*  42 */     this();
/*  43 */     this.user = paramUser;
/*     */   }
/*     */   public void setOperateLog(QYSCategoryType paramQYSCategoryType, QYSOperateType paramQYSOperateType, int paramInt) {
/*  46 */     if (this.operateId <= 0) {
/*  47 */       this.operateId = Util.getIntValue(this.log.getOperateId(this.user, paramInt));
/*  48 */       this.operateLog = new QYSOperateLog(this.user, getLogType(), paramQYSCategoryType, paramQYSOperateType, paramInt, this.operateId);
/*     */     } else {
/*     */       return;
/*     */     } 
/*     */   }
/*     */   public void saveOperateLog(boolean paramBoolean) {
/*  54 */     this.operateLog.setSuccess(paramBoolean);
/*  55 */     this.log.saveOperateLog(this.operateLog);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String cleanUserOperateLog() {
/*  62 */     setOperateLog(QYSCategoryType.OTHER, QYSOperateType.CLEAN_LOG, 0);
/*  63 */     QYSResponse qYSResponse = new QYSResponse();
/*     */     
/*  65 */     int i = Util.getIntValue(Util.null2String(getPropValue("QYS", "keepLogMonths")));
/*  66 */     if (i <= 0) {
/*  67 */       i = 3;
/*     */     }
/*     */     
/*  70 */     String str1 = Util.null2String(TimeUtil.getCurrentDateString());
/*  71 */     String str2 = Util.null2String(mounthAdd(str1, -i));
/*  72 */     this.log.info("cleanUserOperateLog--keepLogMounthsProp:" + i + "--nowDate:" + str1 + "--keepDate:" + str2);
/*     */     
/*  74 */     RecordSetTrans recordSetTrans = new RecordSetTrans();
/*  75 */     recordSetTrans.setAutoCommit(false);
/*  76 */     boolean bool = true;
/*     */     try {
/*  78 */       bool = recordSetTrans.executeUpdate("delete from wf_qiyuesuoOperateLog where startDate<'" + str2 + "'", new Object[0]);
/*  79 */       bool = recordSetTrans.executeUpdate("delete from wf_qiyuesuoServiceCallLog where executeDate<'" + str2 + "'", new Object[0]);
/*  80 */       bool = recordSetTrans.executeUpdate("delete from wf_qiyuesuoSessionKey where logintime<'" + str2 + "'", new Object[0]);
/*  81 */     } catch (Exception exception) {
/*  82 */       this.log.error(exception);
/*  83 */       recordSetTrans.rollback();
/*  84 */       qYSResponse.setMessage("清除用户操作日志计划任务执行失败," + exception.getLocalizedMessage());
/*  85 */       this.log.info("cleanUserOperateLog--" + qYSResponse.getMessage());
/*  86 */       saveOperateLog((qYSResponse.getCode() == 0));
/*  87 */       return qYSResponse.toJSONString();
/*     */     } 
/*  89 */     this.log.info("cleanUserOperateLog--result:" + bool);
/*  90 */     recordSetTrans.commit();
/*     */     
/*  92 */     qYSResponse.setCodeAndMessage(0, "");
/*  93 */     saveOperateLog((qYSResponse.getCode() == 0));
/*  94 */     return qYSResponse.toJSONString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String mounthAdd(String paramString, int paramInt) {
/* 104 */     Calendar calendar = TimeUtil.getCalendar(paramString);
/* 105 */     if (calendar == null) {
/* 106 */       return null;
/*     */     }
/* 108 */     calendar.add(2, paramInt);
/* 109 */     return TimeUtil.getDateString(calendar);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/qiyuesuo/QYSUserOperateLogInterface.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */