/*     */ package weaver.workflow.html;
/*     */ 
/*     */ import java.sql.Connection;
/*     */ import java.sql.ResultSet;
/*     */ import java.sql.ResultSetMetaData;
/*     */ import java.sql.Statement;
/*     */ import java.text.SimpleDateFormat;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Date;
/*     */ import java.util.HashMap;
/*     */ import java.util.Hashtable;
/*     */ import java.util.Map;
/*     */ import java.util.regex.Matcher;
/*     */ import java.util.regex.Pattern;
/*     */ import net.sf.json.JSONArray;
/*     */ import net.sf.json.JSONObject;
/*     */ import weaver.conn.ConnStatement;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ import weaver.workflow.automatic.automaticconnect;
/*     */ import weaver.workflow.field.DetailFieldComInfo;
/*     */ import weaver.workflow.field.FieldComInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class FieldAttrManager
/*     */   extends BaseBean
/*     */ {
/*     */   public static final String DATAS_SEPARATOR = "////~~datas~~////";
/*     */   public static final String PARAM_SEPARATOR = "////~~param~~////";
/*     */   public static final String VALUE_SEPARATOR = "////~~value~~////";
/*     */   
/*     */   public Map getFieldAttrMobile(User paramUser, int paramInt1, int paramInt2, int paramInt3, int paramInt4, int paramInt5, int paramInt6, int paramInt7, String paramString) {
/*  43 */     int i = Util.getIntValue(Util.null2String(Integer.valueOf(paramUser.getLanguage())), 7);
/*  44 */     Hashtable hashtable = (new WFLayoutToHtml()).createOtherPara_mobile(paramInt5, paramInt3, paramInt2, i);
/*  45 */     return getFieldAttr(paramUser, paramInt1, paramInt2, paramInt3, paramInt4, paramInt5, paramInt6, paramInt7, paramString, hashtable, true);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map getFieldAttr(User paramUser, int paramInt1, int paramInt2, int paramInt3, int paramInt4, int paramInt5, int paramInt6, int paramInt7, String paramString, Hashtable paramHashtable, boolean paramBoolean) {
/*  53 */     ArrayList arrayList1 = (ArrayList)paramHashtable.get("fieldidList");
/*  54 */     ArrayList<String> arrayList2 = (ArrayList)paramHashtable.get("fieldtypeList");
/*  55 */     ArrayList arrayList3 = (ArrayList)paramHashtable.get("detailFieldidList");
/*  56 */     Hashtable hashtable1 = (Hashtable)paramHashtable.get("isview_hs");
/*  57 */     Hashtable hashtable2 = (Hashtable)paramHashtable.get("isedit_hs");
/*     */     
/*  59 */     StringBuffer stringBuffer1 = new StringBuffer();
/*  60 */     StringBuffer stringBuffer2 = new StringBuffer();
/*  61 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*     */     
/*  63 */     ArrayList<String> arrayList4 = new ArrayList();
/*  64 */     ArrayList<String> arrayList5 = new ArrayList();
/*  65 */     ArrayList<String> arrayList6 = new ArrayList();
/*  66 */     ArrayList<String> arrayList7 = new ArrayList();
/*  67 */     ArrayList<String> arrayList8 = new ArrayList();
/*  68 */     ArrayList<String> arrayList9 = new ArrayList();
/*  69 */     ArrayList<String> arrayList10 = new ArrayList();
/*  70 */     ArrayList<String> arrayList11 = new ArrayList();
/*  71 */     Hashtable<Object, Object> hashtable = new Hashtable<>();
/*  72 */     ArrayList<String> arrayList12 = new ArrayList();
/*  73 */     RecordSet recordSet = new RecordSet();
/*     */     try {
/*  75 */       ResourceComInfo resourceComInfo = new ResourceComInfo();
/*  76 */       int i = paramUser.getUID();
/*  77 */       int j = Util.getIntValue(resourceComInfo.getDepartmentID("" + i), 0);
/*  78 */       int k = paramUser.getUID();
/*  79 */       int m = Util.getIntValue(resourceComInfo.getDepartmentID("" + k), 0);
/*  80 */       if (paramInt6 == 0) {
/*  81 */         i = paramInt7;
/*  82 */         j = Util.getIntValue(resourceComInfo.getDepartmentID("" + i), 0);
/*     */       } 
/*  84 */       ArrayList<String> arrayList13 = new ArrayList();
/*  85 */       arrayList13.add("currentuser");
/*  86 */       arrayList13.add("currentdept");
/*  87 */       arrayList13.add("wfcreater");
/*  88 */       arrayList13.add("wfcredept");
/*  89 */       arrayList13.add("currentdate");
/*  90 */       ArrayList<String> arrayList14 = new ArrayList();
/*  91 */       arrayList14.add("" + k);
/*  92 */       arrayList14.add("" + m);
/*  93 */       arrayList14.add("" + i);
/*  94 */       arrayList14.add("" + j);
/*  95 */       arrayList14.add("" + paramString);
/*     */       
/*  97 */       recordSet.execute("select * from workflow_nodefieldattr where nodeid=" + paramInt5);
/*  98 */       while (recordSet.next()) {
/*  99 */         int n = Util.getIntValue(recordSet.getString("id"), 0);
/* 100 */         int i1 = Util.getIntValue(recordSet.getString("fieldid"), 0);
/* 101 */         if (hashtable1 == null || Util.getIntValue("" + hashtable1.get("isview" + i1)) != 1)
/*     */           continue; 
/* 103 */         String str = Util.null2String(recordSet.getString("attrcontent")).trim();
/* 104 */         int i2 = Util.getIntValue(recordSet.getString("caltype"), 0);
/* 105 */         int i3 = Util.getIntValue(recordSet.getString("othertype"), 0);
/* 106 */         int i4 = Util.getIntValue(recordSet.getString("transtype"), 0);
/* 107 */         arrayList11.add("" + i1);
/* 108 */         hashtable.put("transtype" + i1, "" + i4);
/* 109 */         if (i2 == 0)
/* 110 */           if (str.indexOf("doFieldSQL(\"") >= 0) {
/* 111 */             i2 = 1;
/* 112 */           } else if (str.indexOf("doFieldMath(\"") >= 0) {
/* 113 */             i2 = 2;
/* 114 */           } else if (str.indexOf("doFieldDate(\"") >= 0) {
/* 115 */             i2 = 3;
/* 116 */           } else if (str.indexOf("doFieldSAP(\"") >= 0) {
/* 117 */             i2 = 4;
/*     */           }  
/* 119 */         int i5 = -1;
/* 120 */         if (i2 == 1) {
/* 121 */           i5 = str.indexOf("doFieldSQL(\"");
/* 122 */           if (i5 < 0)
/* 123 */             continue;  str = analyzeSql(str, arrayList13, arrayList14);
/* 124 */           arrayList4.add("" + i1);
/* 125 */           arrayList5.add(str);
/* 126 */           str = str.substring(i5 + 12);
/* 127 */           i5 = str.lastIndexOf("\")");
/* 128 */           if (i5 > -1) {
/* 129 */             str = str.substring(0, i5);
/* 130 */             str = str.trim();
/*     */           } 
/* 132 */           str = str.trim();
/* 133 */           stringBuffer1.append("\n\t<input type=\"hidden\" id=\"fieldsql" + i1 + "\" value=\"")
/* 134 */             .append(str.replaceAll("\"", "&quot;")).append("\">"); continue;
/* 135 */         }  if (i2 == 2) {
/* 136 */           i5 = str.indexOf("doFieldMath(\"");
/* 137 */           if (i5 < 0)
/* 138 */             continue;  str = str.substring(i5 + 13);
/* 139 */           i5 = str.lastIndexOf("\")");
/* 140 */           if (i5 > -1) {
/* 141 */             str = str.substring(0, i5);
/* 142 */             str = str.trim();
/*     */           } 
/* 144 */           str = str.trim();
/* 145 */           arrayList6.add("" + i1);
/* 146 */           arrayList7.add(str); continue;
/* 147 */         }  if (i2 == 3) {
/* 148 */           i5 = str.indexOf("doFieldDate(\"");
/* 149 */           if (i5 < 0)
/* 150 */             continue;  stringBuffer1.append("\n\t<input type=\"hidden\" id=\"caltype" + i1 + "\" name=\"caltype" + i1 + "\" value=\"")
/* 151 */             .append("" + i2).append("\">");
/* 152 */           stringBuffer1.append("\n\t<input type=\"hidden\" id=\"ohtertype" + i1 + "\" name=\"ohtertype" + i1 + "\" value=\"")
/* 153 */             .append("" + i3).append("\">");
/* 154 */           str = str.substring(i5 + 13);
/* 155 */           i5 = str.lastIndexOf("\")");
/* 156 */           if (i5 > -1) {
/* 157 */             str = str.substring(0, i5);
/* 158 */             str = str.trim();
/*     */           } 
/* 160 */           str = str.trim();
/* 161 */           arrayList8.add("" + i1);
/* 162 */           arrayList9.add(str);
/* 163 */           arrayList10.add("" + i3); continue;
/* 164 */         }  if (i2 == 4) {
/* 165 */           i5 = str.indexOf("doFieldSAP(\"");
/* 166 */           if (i5 < 0)
/* 167 */             continue;  stringBuffer1.append("\n\t<input type=\"hidden\" id=\"caltype" + i1 + "\" name=\"caltype" + i1 + "\" value=\"")
/* 168 */             .append("" + i2).append("\">");
/* 169 */           stringBuffer1.append("\n\t<input type=\"hidden\" id=\"ohtertype" + i1 + "\" name=\"ohtertype" + i1 + "\" value=\"")
/* 170 */             .append("" + i3).append("\">");
/* 171 */           arrayList12.add(i1 + "-" + n);
/* 172 */           arrayList10.add("" + i3);
/*     */         } 
/*     */       } 
/* 175 */     } catch (Exception exception) {
/* 176 */       exception.printStackTrace();
/*     */     } 
/* 178 */     hashMap1.put("sqlfieldidList", arrayList4);
/* 179 */     hashMap1.put("sqlcontentList", arrayList5);
/* 180 */     hashMap1.put("attrfieldidList", arrayList6);
/* 181 */     hashMap1.put("attrcontentList", arrayList7);
/* 182 */     hashMap1.put("datefieldidList", arrayList8);
/* 183 */     hashMap1.put("datecontentList", arrayList9);
/* 184 */     hashMap1.put("transtype_hs", hashtable);
/* 185 */     hashMap1.put("sapfieldidList", arrayList12);
/*     */ 
/*     */     
/* 188 */     StringBuffer stringBuffer3 = new StringBuffer();
/* 189 */     for (byte b1 = 0; b1 < arrayList4.size(); b1++) {
/* 190 */       String str = Util.null2String(arrayList4.get(b1));
/* 191 */       if (arrayList1 != null && arrayList1.indexOf(str) > -1) {
/* 192 */         stringBuffer3.append("\t if(typeof fieldAttrOperate != 'undefined') {fieldAttrOperate.pageLoadInitValue('" + str + "', '-1');}\n");
/* 193 */       } else if (arrayList3 != null && arrayList3.indexOf(str) > -1 && 
/* 194 */         !paramBoolean) {
/* 195 */         stringBuffer3.append("\t if(typeof fieldAttrOperate != 'undefined') {fieldAttrOperate.pageLoadInitValue('" + str + "', 'AllRow');}\n");
/*     */       } 
/*     */     } 
/*     */     
/* 199 */     if (arrayList12.size() > 0) {
/* 200 */       stringBuffer2.append("function doSAPField(fieldattrid,obj){").append("\n");
/*     */       
/* 202 */       String str = Util.null2String((new BaseBean()).getPropValue("SAPTrigger", "SAPTriggerFlag"));
/* 203 */       if (str.equals("1") || str.equalsIgnoreCase("y")) {
/* 204 */         stringBuffer2.append("\tvar isdetail = 0;");
/* 205 */         stringBuffer2.append("\tvar rowindex = 0;");
/* 206 */         stringBuffer2.append("\tif(obj){\n");
/* 207 */         stringBuffer2.append("\t\tvar id = jQuery(obj).attr('id');");
/* 208 */         stringBuffer2.append("\t\tif(id.indexOf('_') > 0){");
/* 209 */         stringBuffer2.append("\t\t\tisdetail = 1;");
/* 210 */         stringBuffer2.append("\t\t\trowindex = id.substring(id.indexOf('_') + 1);");
/* 211 */         stringBuffer2.append("\t\t}");
/* 212 */         stringBuffer2.append("\t}\n");
/* 213 */         stringBuffer2.append("\t").append("jQuery.getScript('/workflow/request/GetSAPData.jsp?step=1&isdetail='+isdetail+'&rowindex='+rowindex+'&fieldattrid='+fieldattrid+'&workflowid=" + paramInt1 + "')");
/*     */       } 
/* 215 */       stringBuffer2.append("\n}");
/*     */     } 
/*     */ 
/*     */     
/* 219 */     boolean bool = recordSet.getDBType().equals("oracle"); byte b2;
/* 220 */     for (b2 = 0; b2 < arrayList6.size(); b2++) {
/* 221 */       String str1 = Util.null2String(arrayList6.get(b2));
/* 222 */       String str2 = Util.null2String(arrayList7.get(b2));
/* 223 */       stringBuffer2.append("\n");
/* 224 */       stringBuffer2.append("").append("function doMathFieldAttr" + str1 + "(){").append("\n");
/* 225 */       stringBuffer2.append("if(wf__info.onlyview){return}\n");
/* 226 */       String str3 = "";
/* 227 */       int i = str2.indexOf("$");
/* 228 */       while (i > -1) {
/* 229 */         str3 = str3 + str2.substring(0, i);
/* 230 */         str2 = str2.substring(i + 1);
/* 231 */         int n = str2.indexOf("$");
/* 232 */         String str = str2.substring(0, n);
/* 233 */         int i1 = 0;
/* 234 */         if (arrayList1 != null && arrayList2 != null && arrayList1.indexOf("" + str) > -1) {
/* 235 */           i1 = Util.getIntValue(arrayList2.get(arrayList1.indexOf("" + str)));
/*     */         }
/* 237 */         if (i1 == 5) {
/* 238 */           str3 = str3 + "($G(\"field" + str + "\").value.replace(/,/g,\"\"))*1.0";
/*     */         } else {
/* 240 */           str3 = str3 + "$G(\"field" + str + "\").value*1.0";
/*     */         } 
/* 242 */         str2 = str2.substring(n + 1);
/* 243 */         i = str2.indexOf("$");
/*     */       } 
/* 245 */       str3 = str3 + str2;
/* 246 */       if (paramInt2 == 0) {
/* 247 */         recordSet.executeSql("select fielddbtype from workflow_formdict where id=" + str1);
/*     */       } else {
/* 249 */         recordSet.executeSql("select fielddbtype from workflow_billfield where id=" + str1);
/*     */       } 
/* 251 */       int j = 2;
/*     */       try {
/* 253 */         String str = "";
/* 254 */         if (recordSet.next()) {
/* 255 */           str = Util.null2String(recordSet.getString("fielddbtype"));
/* 256 */           if (bool) {
/* 257 */             if (str.toLowerCase().indexOf("number(") > -1) {
/* 258 */               j = Util.getIntValue(str.substring(str.indexOf(",") + 1, str.indexOf(")")), 2);
/*     */             }
/* 260 */           } else if (str.toLowerCase().indexOf("decimal(") > -1) {
/* 261 */             j = Util.getIntValue(str.substring(str.indexOf(",") + 1, str.indexOf(")")), 2);
/*     */           } 
/* 263 */           if (str.toLowerCase().indexOf("int") > -1)
/* 264 */             j = 0; 
/*     */         } 
/* 266 */       } catch (Exception exception) {}
/*     */       
/* 268 */       int k = 0;
/* 269 */       if (arrayList1 != null && arrayList2 != null && arrayList1.indexOf("" + str1) > -1) {
/* 270 */         k = Util.getIntValue(arrayList2.get(arrayList1.indexOf("" + str1)));
/*     */       }
/* 272 */       int m = 0;
/* 273 */       if (hashtable2 != null) {
/* 274 */         m = Util.getIntValue((String)hashtable2.get("isedit" + str1));
/*     */       }
/* 276 */       if (k == 4) {
/* 277 */         if (m == 1) {
/* 278 */           stringBuffer2.append("").append("try{").append("\n");
/* 279 */           stringBuffer2.append("\t").append("if($G(\"field_lable" + str1 + "\")){").append("\n");
/* 280 */           stringBuffer2.append("\t\t").append("var numberTemp = ").append(str3).append(";\n");
/* 281 */           stringBuffer2.append("\t\t").append("var numberStr = toFix(numberTemp," + j + ")").append(";\n");
/* 282 */           stringBuffer2.append("\t\t").append("$G(\"field_lable" + str1 + "\").value = numberStr").append(";\n");
/* 283 */           stringBuffer2.append("\t").append("}").append("\n");
/* 284 */           stringBuffer2.append("").append("}catch(e){}").append("\n");
/* 285 */           stringBuffer2.append("").append("try{").append("\n");
/* 286 */           stringBuffer2.append("\t").append("if($G(\"field" + str1 + "\")){").append("\n");
/* 287 */           stringBuffer2.append("\t\t").append("$G(\"field" + str1 + "\").value = numberStr").append(";\n");
/* 288 */           stringBuffer2.append("\t").append("}").append("\n");
/* 289 */           stringBuffer2.append("").append("}catch(e){}").append("\n");
/* 290 */           stringBuffer2.append("").append("try{").append("\n");
/* 291 */           stringBuffer2.append("\t").append("numberToFormat('" + str1 + "');").append(";\n");
/* 292 */           stringBuffer2.append("").append("}catch(e){}").append("\n");
/* 293 */         } else if (m == 0) {
/* 294 */           stringBuffer2.append("").append("try{").append("\n");
/* 295 */           stringBuffer2.append("\t").append("if($G(\"field" + str1 + "\")){").append("\n");
/* 296 */           stringBuffer2.append("\t\t").append("var numberTemp = ").append(str3).append(";\n");
/* 297 */           stringBuffer2.append("\t\t").append("var numberStr = toFix(numberTemp," + j + ")").append(";\n");
/* 298 */           stringBuffer2.append("\t\t").append("$G(\"field" + str1 + "\").value = numberStr").append(";\n");
/* 299 */           stringBuffer2.append("\t\t").append("$G(\"field" + str1 + "span\").innerHTML = ").append("milfloatFormat(numberStr);").append(";\n");
/* 300 */           stringBuffer2.append("\t\t").append("$G(\"field" + str1 + "ncspan\").innerHTML = ").append("numberChangeToChinese(numberStr);").append(";\n");
/* 301 */           stringBuffer2.append("\t").append("}").append("\n");
/* 302 */           stringBuffer2.append("").append("}catch(e){}").append("\n");
/*     */         } 
/* 304 */         stringBuffer2.append("").append("try{").append("\n");
/* 305 */         stringBuffer2.append("\t").append("doformat" + str1 + "();").append("\n");
/* 306 */         stringBuffer2.append("").append("}catch(e){}").append("\n");
/* 307 */       } else if (k == 5) {
/* 308 */         stringBuffer2.append("").append("try{").append("\n");
/* 309 */         stringBuffer2.append("\t").append("if($G(\"field" + str1 + "\")){").append("\n");
/* 310 */         stringBuffer2.append("\t\t").append("var numberTemp = ").append(str3).append(";\n");
/* 311 */         stringBuffer2.append("\t\t").append("var numberStr = toFix(numberTemp," + j + ")").append(";\n");
/* 312 */         stringBuffer2.append("\t\t").append("$G(\"field" + str1 + "\").value = numberStr").append(";\n");
/* 313 */         stringBuffer2.append("\t").append("}else{").append("\n");
/* 314 */         stringBuffer2.append("\t\t").append("$G(\"field" + str1 + "span\").innerHTML = numberStr").append("\n");
/* 315 */         stringBuffer2.append("\t").append("}").append("\n");
/* 316 */         stringBuffer2.append("").append("}catch(e){}").append("\n");
/* 317 */         stringBuffer2.append("").append("changeToThousands('field" + str1 + "');").append("\n");
/* 318 */         stringBuffer2.append("").append("try{").append("\n");
/* 319 */         stringBuffer2.append("\t").append("if($G(\"field" + str1 + "\").type==\"hidden\"){").append("\n");
/* 320 */         stringBuffer2.append("\t\t").append("$G(\"field" + str1 + "span\").innerHTML = ").append("$G(\"field" + str1 + "\").value;").append(";\n");
/* 321 */         stringBuffer2.append("\t").append("}").append("\n");
/* 322 */         stringBuffer2.append("").append("}catch(e){}").append("\n");
/* 323 */         stringBuffer2.append("").append("try{").append("\n");
/* 324 */         stringBuffer2.append("\t").append("doformat" + str1 + "();").append("\n");
/* 325 */         stringBuffer2.append("").append("}catch(e){}").append("\n");
/*     */       } else {
/* 327 */         stringBuffer2.append("").append("try{").append("\n");
/* 328 */         stringBuffer2.append("\t").append("if($G(\"field" + str1 + "\")){").append("\n");
/* 329 */         stringBuffer2.append("\t\t").append("var numberTemp = ").append(str3).append(";\n");
/* 330 */         stringBuffer2.append("\t\t").append("var numberStr = toFix(numberTemp," + j + ")").append(";\n");
/* 331 */         stringBuffer2.append("\t\t").append("$G(\"field" + str1 + "\").value = numberStr").append(";\n");
/* 332 */         stringBuffer2.append("\t").append("}else{").append("\n");
/* 333 */         stringBuffer2.append("\t\t").append("$G(\"field" + str1 + "span\").innerHTML = numberStr").append("\n");
/* 334 */         stringBuffer2.append("\t").append("}").append("\n");
/* 335 */         stringBuffer2.append("").append("}catch(e){}").append("\n");
/* 336 */         stringBuffer2.append("").append("try{").append("\n");
/* 337 */         stringBuffer2.append("\t").append("if($G(\"field" + str1 + "\").type==\"hidden\"){").append("\n");
/* 338 */         stringBuffer2.append("\t\t").append("$G(\"field" + str1 + "span\").innerHTML = numberStr").append(";\n");
/* 339 */         stringBuffer2.append("\t").append("}").append("\n");
/* 340 */         stringBuffer2.append("").append("}catch(e){}").append("\n");
/* 341 */         stringBuffer2.append("").append("try{").append("\n");
/* 342 */         stringBuffer2.append("\t").append("doformat" + str1 + "();").append("\n");
/* 343 */         stringBuffer2.append("").append("}catch(e){}").append("\n");
/*     */       } 
/* 345 */       stringBuffer2.append("").append("}").append("\n");
/* 346 */       if (!paramBoolean) {
/* 347 */         stringBuffer3.append("\t doMathFieldAttr" + str1 + "();\n");
/*     */       }
/*     */     } 
/* 350 */     if (arrayList8.size() > 0) {
/*     */       
/* 352 */       ArrayList<String> arrayList13 = new ArrayList();
/* 353 */       ArrayList<String> arrayList14 = new ArrayList();
/* 354 */       ArrayList<String> arrayList15 = new ArrayList();
/* 355 */       Hashtable<Object, Object> hashtable3 = new Hashtable<>();
/* 356 */       String str = "";
/* 357 */       if (paramInt2 == 0) {
/* 358 */         str = "select id, fieldhtmltype, type, 0 as viewtype from workflow_formdict fd where ( (fieldhtmltype='1' and type in (2,3,4,5) ) or (fieldhtmltype='3' and type in (2, 19) ) ) and exists (select id from workflow_formfield ff where ff.fieldid=fd.id and ff.formid=" + paramInt3 + ")";
/* 359 */         str = str + " union ";
/* 360 */         str = str + "select id, fieldhtmltype, type, 1 as viewtype from workflow_formdictdetail fdd where ( (fieldhtmltype='1' and type in (2,3,4,5) ) or (fieldhtmltype='3' and type in (2, 19) ) ) and exists (select id from workflow_formfield ff where ff.fieldid=fdd.id and ff.formid=" + paramInt3 + ")";
/*     */       } else {
/* 362 */         str = "select id, fieldhtmltype, type, viewtype from workflow_billfield where ( (fieldhtmltype='1' and type in (2,3,4,5) ) or (fieldhtmltype='3' and type in (2, 19) ) ) and billid=" + paramInt3;
/*     */       } 
/* 364 */       recordSet.execute(str);
/* 365 */       while (recordSet.next()) {
/* 366 */         int i = Util.getIntValue(recordSet.getString("fieldhtmltype"), 0);
/* 367 */         int j = Util.getIntValue(recordSet.getString("viewtype"), 0);
/* 368 */         int k = Util.getIntValue(recordSet.getString("type"), 0);
/* 369 */         int m = Util.getIntValue(recordSet.getString("id"), 0);
/* 370 */         hashtable3.put("viewtype" + m, "" + j);
/* 371 */         if (i == 1) {
/* 372 */           arrayList15.add("" + m); continue;
/* 373 */         }  if (i == 3) {
/* 374 */           if (k == 2) {
/* 375 */             arrayList13.add("" + m); continue;
/* 376 */           }  if (k == 19) {
/* 377 */             arrayList14.add("" + m);
/*     */           }
/*     */         } 
/*     */       } 
/*     */       
/* 382 */       for (byte b = 0; b < arrayList8.size(); b++) {
/*     */         try {
/* 384 */           int i = Util.getIntValue(arrayList8.get(b), 0);
/* 385 */           String str1 = Util.null2String(arrayList9.get(b));
/* 386 */           String str2 = str1;
/* 387 */           int j = Util.getIntValue((String)hashtable3.get("viewtype" + i));
/* 388 */           String str3 = "";
/* 389 */           int k = Util.getIntValue(arrayList10.get(b), 0);
/* 390 */           stringBuffer2.append("").append("function doFieldDate" + i + "(detailrow_t){").append("\n");
/* 391 */           if (j == 1) {
/* 392 */             stringBuffer2.append("\t").append("var isdetail_t = \"_\"+detailrow_t;").append("\n");
/*     */           } else {
/* 394 */             stringBuffer2.append("\t").append("var isdetail_t = \"\";").append("\n");
/*     */           } 
/* 396 */           int m = str1.indexOf("$");
/* 397 */           while (m > -1) {
/* 398 */             int n = str1.indexOf("$", m + 1);
/* 399 */             String str4 = str1.substring(m + 1, n);
/* 400 */             boolean bool1 = arrayList15.contains("" + str4);
/* 401 */             boolean bool2 = arrayList13.contains("" + str4);
/* 402 */             boolean bool3 = arrayList14.contains("" + str4);
/* 403 */             String str5 = str1.substring(0, m).trim();
/* 404 */             if ("+".equals(str5)) {
/* 405 */               str3 = str3 + " + \"+\" + ";
/* 406 */             } else if ("-".equals(str5)) {
/* 407 */               str3 = str3 + " + \"-\" + ";
/*     */             } 
/*     */             
/* 410 */             if ("datetime".equals(str4)) {
/* 411 */               str3 = str3 + "  +\"$datetime$\"+  ";
/* 412 */             } else if ("currentdate".equals(str4)) {
/* 413 */               str3 = str3 + "  \"$date$" + paramString + "$date$\"  ";
/*     */             } else {
/* 415 */               String str6 = "";
/* 416 */               if (bool1 == true) {
/* 417 */                 str6 = "$input$";
/* 418 */               } else if (bool2 == true) {
/* 419 */                 str6 = "$date$";
/* 420 */               } else if (bool3 == true) {
/* 421 */                 str6 = "$time$";
/*     */               } 
/* 423 */               str3 = str3 + "\"" + str6 + "\"+document.getElementById(\"field" + str4 + "\"+isdetail_t).value+\"" + str6 + "\"";
/*     */             } 
/* 425 */             str1 = str1.substring(n + 1);
/* 426 */             m = str1.indexOf("$");
/*     */           } 
/*     */           
/* 429 */           String[] arrayOfString = str1.split("");
/* 430 */           Pattern pattern = Pattern.compile("\\d{1}");
/* 431 */           if (arrayOfString != null) {
/* 432 */             byte b3 = -1;
/* 433 */             for (byte b4 = 0; b4 < arrayOfString.length; b4++) {
/* 434 */               String str4 = arrayOfString[b4].trim();
/* 435 */               Matcher matcher = pattern.matcher(str4);
/* 436 */               if ("+".equals(str4)) {
/* 437 */                 if (b3 == 0) {
/* 438 */                   str3 = str3 + "$input$\"";
/*     */                 }
/* 440 */                 str3 = str3 + " + \"+\" + ";
/* 441 */                 b3 = 1;
/* 442 */               } else if ("-".equals(str4)) {
/* 443 */                 if (b3 == 0) {
/* 444 */                   str3 = str3 + "$input$\"";
/*     */                 }
/* 446 */                 str3 = str3 + " + \"-\" + ";
/* 447 */                 b3 = 1;
/* 448 */               } else if (matcher.find()) {
/* 449 */                 if (b3 == 1) {
/* 450 */                   str3 = str3 + "\"$input$" + str4;
/* 451 */                   b3 = 0;
/*     */                 } else {
/* 453 */                   str3 = str3 + str4;
/*     */                 } 
/*     */               } 
/*     */             } 
/* 457 */             if (b3 == 0) {
/* 458 */               str3 = str3 + "$input$\"";
/*     */             }
/*     */           } 
/* 461 */           stringBuffer2.append("\t").append("var para = \"othertype=" + k + "&datecontent=\";").append("\n");
/* 462 */           stringBuffer2.append("\t").append("var datecontent_t = " + str3 + ";").append("\n");
/* 463 */           stringBuffer2.append("\t").append("datecontent_t = datecontent_t.replace(/\\+/g, \"%2B\");").append("\n");
/* 464 */           stringBuffer2.append("\t").append("datecontent_t = datecontent_t.replace(new RegExp(\"%\",\"gm\"), \"%25\");").append("\n");
/* 465 */           stringBuffer2.append("\t").append("para = para + escape(datecontent_t);").append("\n");
/* 466 */           stringBuffer2.append("\t").append("fieldAttrOperate.doFieldDateAjax(para, \"" + i + "\", isdetail_t);").append("\n");
/* 467 */           stringBuffer2.append("").append("}").append("\n");
/* 468 */         } catch (Exception exception) {
/* 469 */           exception.printStackTrace();
/*     */         } 
/*     */       } 
/*     */     } 
/*     */     
/*     */     try {
/* 475 */       for (b2 = 0; b2 < arrayList11.size(); b2++) {
/* 476 */         String str1 = Util.null2String(arrayList11.get(b2));
/* 477 */         int i = Util.getIntValue((String)hashtable.get("transtype" + str1));
/* 478 */         String str2 = "";
/* 479 */         if (i == 1) {
/* 480 */           FieldTransInterface fieldTransInterface = (FieldTransInterface)Class.forName("weaver.workflow.html.MoneyTrans4FieldTrans").newInstance();
/* 481 */           str2 = fieldTransInterface.getTransJs(str1);
/* 482 */         } else if (i == 2) {
/* 483 */           FieldTransInterface fieldTransInterface = (FieldTransInterface)Class.forName("weaver.workflow.html.MoneySpilt4FieldTrans").newInstance();
/* 484 */           str2 = fieldTransInterface.getTransJs(str1);
/*     */         } 
/* 486 */         if (!"".equals(str2))
/* 487 */           stringBuffer2.append("\n").append(str2).append("\n"); 
/*     */       } 
/* 489 */     } catch (Exception exception) {
/* 490 */       exception.printStackTrace();
/*     */     } 
/* 492 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 493 */     if (stringBuffer3.length() > 0) {
/* 494 */       stringBuffer2.append("jQuery(document).ready(function(){\n")
/* 495 */         .append(stringBuffer3).append("});\n");
/*     */     }
/* 497 */     hashMap2.put("htmlHiddenElementsb", stringBuffer1);
/* 498 */     hashMap2.put("jsStr", stringBuffer2);
/* 499 */     hashMap2.put("otherPara_hs", hashMap1);
/* 500 */     return hashMap2;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public JSONArray parseFieldAttrRequest(User paramUser, int paramInt1, int paramInt2, int paramInt3, int paramInt4, String paramString) {
/* 508 */     JSONArray jSONArray = new JSONArray();
/* 509 */     ConnStatement connStatement = null;
/* 510 */     Connection connection = null;
/* 511 */     Statement statement = null;
/* 512 */     ResultSet resultSet = null;
/* 513 */     String str = "";
/* 514 */     automaticconnect automaticconnect = new automaticconnect();
/*     */     try {
/* 516 */       connStatement = new ConnStatement();
/* 517 */       RecordSet recordSet = new RecordSet();
/*     */       
/* 519 */       recordSet.execute("select * from workflow_nodefieldattr where nodeid=" + paramInt1);
/* 520 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 521 */       HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 522 */       while (recordSet.next()) {
/* 523 */         hashMap1.put(recordSet.getString("fieldid"), recordSet.getString("attrcontent"));
/* 524 */         hashMap2.put(recordSet.getString("fieldid"), Util.null2String(recordSet.getString("datasourceid")));
/*     */       } 
/*     */       
/* 527 */       FieldComInfo fieldComInfo = new FieldComInfo();
/* 528 */       DetailFieldComInfo detailFieldComInfo = new DetailFieldComInfo();
/* 529 */       ArrayList<String> arrayList1 = new ArrayList();
/* 530 */       ArrayList<Integer> arrayList2 = new ArrayList();
/* 531 */       ArrayList<Integer> arrayList3 = new ArrayList();
/*     */       
/* 533 */       ResourceComInfo resourceComInfo = new ResourceComInfo();
/* 534 */       int i = paramUser.getUID();
/* 535 */       int j = Util.getIntValue(resourceComInfo.getDepartmentID("" + i), 0);
/* 536 */       int k = paramUser.getUID();
/* 537 */       int m = Util.getIntValue(resourceComInfo.getDepartmentID("" + k), 0);
/* 538 */       if (paramInt4 > 0) {
/* 539 */         recordSet.executeSql("select creater from workflow_requestbase where requestid=" + paramInt4);
/* 540 */         if (recordSet.next()) {
/* 541 */           i = Util.getIntValue(recordSet.getString(1));
/* 542 */           j = Util.getIntValue(resourceComInfo.getDepartmentID("" + i), 0);
/*     */         } 
/*     */       } 
/*     */       
/* 546 */       ArrayList<String> arrayList4 = new ArrayList();
/* 547 */       ArrayList<String> arrayList5 = new ArrayList();
/*     */       
/* 549 */       arrayList4.add("currentuser");
/* 550 */       arrayList4.add("currentdept");
/* 551 */       arrayList4.add("wfcreater");
/* 552 */       arrayList4.add("wfcredept");
/* 553 */       arrayList4.add("currentdate");
/*     */       
/* 555 */       arrayList5.add("" + k);
/* 556 */       arrayList5.add("" + m);
/* 557 */       arrayList5.add("" + i);
/* 558 */       arrayList5.add("" + j);
/* 559 */       SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
/* 560 */       arrayList5.add("" + simpleDateFormat.format(new Date()));
/*     */       
/* 562 */       this; String[] arrayOfString = paramString.split("////~~datas~~////");
/* 563 */       for (String str1 : arrayOfString) {
/*     */         try {
/* 565 */           this; String[] arrayOfString1 = str1.split("////~~param~~////");
/* 566 */           if (arrayOfString1.length == 3)
/*     */           
/* 568 */           { String str2 = arrayOfString1[0];
/* 569 */             int n = Util.getIntValue(arrayOfString1[1]);
/* 570 */             String str3 = arrayOfString1[2];
/* 571 */             String str4 = "";
/* 572 */             if (hashMap1.containsKey(str2))
/* 573 */               str4 = (String)hashMap1.get(str2); 
/* 574 */             if (hashMap2.containsKey(str2)) {
/* 575 */               str = (String)hashMap2.get(str2);
/*     */             } else {
/* 577 */               str = "";
/* 578 */             }  if (!"".equals(str4))
/*     */             
/* 580 */             { String str5 = parseFieldAttrSql(str4, str3, arrayList4, arrayList5);
/* 581 */               if (!"".equals(str5))
/*     */               {
/*     */                 
/* 584 */                 if (str5.toUpperCase().indexOf("INSERT") <= -1 && str5.toUpperCase().indexOf("UPDATE") <= -1 && str5
/* 585 */                   .toUpperCase().indexOf("DROP") <= -1 && str5.toUpperCase().indexOf("TRUNCATE") <= -1 && str5
/* 586 */                   .toUpperCase().indexOf("DELETE") <= -1 && str5.toUpperCase().indexOf("EXEC") <= -1 && str5
/* 587 */                   .toUpperCase().indexOf("CALL") <= -1)
/*     */                 
/*     */                 { 
/* 590 */                   byte b = 1;
/*     */                   try {
/* 592 */                     String str8 = str5.substring(0, str5.toUpperCase().indexOf("FROM"));
/* 593 */                     if (str8.indexOf(",") > -1)
/* 594 */                       b = 2; 
/* 595 */                   } catch (Exception exception) {}
/* 596 */                   String str6 = "";
/* 597 */                   String str7 = "";
/*     */                   
/* 599 */                   if ("".equals(str)) {
/*     */                     try {
/* 601 */                       connStatement.setStatementSql(str5);
/* 602 */                       connStatement.executeQuery();
/* 603 */                       while (connStatement.next()) {
/* 604 */                         String str8 = Util.null2String(connStatement.getString(1));
/* 605 */                         str8 = str8.replaceAll("&", "&amp;").replaceAll("<", "&lt;").replaceAll(">", "&gt;");
/* 606 */                         if ("".equals(str6)) {
/* 607 */                           str6 = str8;
/*     */                         } else {
/* 609 */                           str6 = str6 + "&nbsp;" + str8;
/*     */                         } 
/* 611 */                         if (b > 1) {
/* 612 */                           String str9 = Util.null2String(connStatement.getString(2));
/* 613 */                           str9 = str9.replaceAll("&", "&amp;").replaceAll("<", "&lt;").replaceAll(">", "&gt;");
/* 614 */                           if ("".equals(str7)) {
/* 615 */                             str7 = str9; continue;
/*     */                           } 
/* 617 */                           str7 = str7 + "," + str9;
/*     */                         }
/*     */                       
/*     */                       } 
/* 621 */                     } catch (Exception exception) {}
/*     */                   } else {
/*     */                     
/* 624 */                     try { connection = automaticconnect.getConnection("datasource." + str);
/* 625 */                       statement = connection.createStatement();
/* 626 */                       resultSet = statement.executeQuery(str5);
/* 627 */                       ResultSetMetaData resultSetMetaData = resultSet.getMetaData();
/* 628 */                       while (resultSet.next()) {
/* 629 */                         String str8 = Util.null2String(resultSet.getString(1));
/* 630 */                         str8 = str8.replaceAll("&", "&amp;").replaceAll("<", "&lt;").replaceAll(">", "&gt;");
/* 631 */                         if ("".equals(str6)) {
/* 632 */                           str6 = str8;
/*     */                         } else {
/* 634 */                           str6 = str6 + "&nbsp;" + str8;
/*     */                         } 
/* 636 */                         if (b > 1) {
/* 637 */                           String str9 = Util.null2String(resultSet.getString(2));
/* 638 */                           str9 = str9.replaceAll("&", "&amp;").replaceAll("<", "&lt;").replaceAll(">", "&gt;");
/* 639 */                           if ("".equals(str7)) {
/* 640 */                             str7 = str9; continue;
/*     */                           } 
/* 642 */                           str7 = str7 + "," + str9;
/*     */                         }
/*     */                       
/*     */                       }  }
/* 646 */                     catch (Exception exception) {  }
/*     */                     finally
/* 648 */                     { if (resultSet != null)
/* 649 */                         resultSet.close(); 
/* 650 */                       if (statement != null)
/* 651 */                         statement.close(); 
/* 652 */                       if (connection != null) {
/* 653 */                         connection.close();
/*     */                       } }
/*     */                   
/*     */                   } 
/* 657 */                   int i1 = -1;
/* 658 */                   int i2 = -1;
/* 659 */                   int i3 = arrayList1.indexOf(str2);
/* 660 */                   if (i3 > -1) {
/* 661 */                     i1 = ((Integer)arrayList2.get(i3)).intValue();
/* 662 */                     i2 = ((Integer)arrayList3.get(i3)).intValue();
/*     */                   } else {
/* 664 */                     if (paramInt3 == 0) {
/* 665 */                       if (n > -1) {
/* 666 */                         i1 = Util.getIntValue(detailFieldComInfo.getFieldhtmltype("" + str2), -1);
/* 667 */                         i2 = Util.getIntValue(detailFieldComInfo.getFieldType("" + str2), -1);
/*     */                       } else {
/* 669 */                         i1 = Util.getIntValue(fieldComInfo.getFieldhtmltype("" + str2), -1);
/* 670 */                         i2 = Util.getIntValue(fieldComInfo.getFieldType("" + str2), -1);
/* 671 */                         if (i1 == -1) {
/* 672 */                           i1 = Util.getIntValue(detailFieldComInfo.getFieldhtmltype("" + str2), -1);
/* 673 */                           i2 = Util.getIntValue(detailFieldComInfo.getFieldType("" + str2), -1);
/*     */                         } 
/*     */                       } 
/*     */                     } else {
/* 677 */                       recordSet.execute("select viewtype, fieldhtmltype, type from workflow_billfield where id=" + str2);
/* 678 */                       if (recordSet.next()) {
/* 679 */                         i1 = Util.getIntValue(recordSet.getString("fieldhtmltype"), -1);
/* 680 */                         i2 = Util.getIntValue(recordSet.getString("type"), -1);
/*     */                       } 
/*     */                     } 
/* 683 */                     arrayList1.add(str2);
/* 684 */                     arrayList2.add(Integer.valueOf(i1));
/* 685 */                     arrayList3.add(Integer.valueOf(i2));
/*     */                   } 
/* 687 */                   if (i1 == 1) {
/* 688 */                     if (i2 == 3 && str6.startsWith("."))
/* 689 */                       str6 = "0" + str6; 
/* 690 */                     if (i2 == 2 || i2 == 3 || i2 == 4 || i2 == 5) {
/* 691 */                       if ("".equals(str7.trim()))
/* 692 */                         str7 = "0"; 
/* 693 */                       if ("".equals(str6.trim()))
/* 694 */                         str6 = "0"; 
/*     */                     } 
/*     */                   } 
/* 697 */                   JSONObject jSONObject = new JSONObject();
/* 698 */                   jSONObject.put("assignField", str2);
/* 699 */                   jSONObject.put("rowIndex", n + "");
/* 700 */                   jSONObject.put("name", str6);
/* 701 */                   jSONObject.put("key", str7);
/* 702 */                   jSONObject.put("htmltype", i1 + "");
/* 703 */                   jSONObject.put("type", i2 + "");
/* 704 */                   jSONArray.add(jSONObject); }  }  }  } 
/* 705 */         } catch (Exception exception) {}
/*     */       }
/*     */     
/*     */     }
/* 709 */     catch (Exception exception) {
/* 710 */       exception.printStackTrace();
/*     */     } finally {
/*     */       try {
/* 713 */         if (connStatement != null) {
/* 714 */           connStatement.close();
/* 715 */           connStatement = null;
/*     */         } 
/* 717 */         if (resultSet != null)
/* 718 */           resultSet.close(); 
/* 719 */         if (statement != null)
/* 720 */           statement.close(); 
/* 721 */         if (connection != null) {
/* 722 */           connection.close();
/*     */         }
/* 724 */       } catch (Exception exception) {}
/*     */     } 
/* 726 */     return jSONArray;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String parseFieldAttrSql(String paramString1, String paramString2, ArrayList paramArrayList1, ArrayList paramArrayList2) {
/*     */     try {
/* 734 */       ArrayList<String> arrayList1 = new ArrayList();
/* 735 */       ArrayList<String> arrayList2 = new ArrayList();
/* 736 */       arrayList1.addAll(paramArrayList1);
/* 737 */       arrayList2.addAll(paramArrayList2);
/* 738 */       this; String[] arrayOfString = paramString2.split("////~~value~~////");
/* 739 */       for (String str1 : arrayOfString) {
/* 740 */         if (!"".equals(str1) && str1.indexOf("|") != -1) {
/*     */           
/* 742 */           int j = str1.indexOf("|");
/* 743 */           arrayList1.add("" + str1.substring(0, j));
/* 744 */           arrayList2.add("" + str1.substring(j + 1));
/*     */         } 
/* 746 */       }  String str = "";
/* 747 */       int i = paramString1.indexOf("doFieldSQL(\"");
/* 748 */       if (i > -1) {
/* 749 */         paramString1 = paramString1.substring(i + 12);
/* 750 */         i = paramString1.lastIndexOf("\")");
/* 751 */         if (i > -1)
/* 752 */           paramString1 = paramString1.substring(0, i); 
/* 753 */         paramString1 = paramString1.trim();
/* 754 */         str = analyzeSql(paramString1, arrayList1, arrayList2);
/*     */       } 
/* 756 */       return str;
/* 757 */     } catch (Exception exception) {
/* 758 */       exception.printStackTrace();
/* 759 */       return "";
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static String analyzeSql(String paramString, ArrayList<String> paramArrayList1, ArrayList<String> paramArrayList2) {
/* 767 */     String str = paramString;
/*     */     try {
/* 769 */       for (byte b = 0; b < paramArrayList1.size(); b++) {
/* 770 */         String str1 = Util.null2String(paramArrayList1.get(b));
/* 771 */         String str2 = Util.null2String(paramArrayList2.get(b));
/* 772 */         str = str.replaceAll("\\$" + str1 + "\\$", str2);
/*     */       } 
/* 774 */     } catch (Exception exception) {
/* 775 */       exception.printStackTrace();
/*     */     } 
/* 777 */     return str;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/html/FieldAttrManager.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */