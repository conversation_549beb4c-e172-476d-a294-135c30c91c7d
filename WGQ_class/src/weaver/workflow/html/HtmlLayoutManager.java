/*     */ package weaver.workflow.html;
/*     */ 
/*     */ import java.io.File;
/*     */ import java.text.SimpleDateFormat;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Date;
/*     */ import java.util.Hashtable;
/*     */ import weaver.conn.ConnStatement;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.file.FileManage;
/*     */ import weaver.file.FileSecurityUtil;
/*     */ import weaver.file.FileUpload;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.GCONST;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.workflow.form.FormFieldMainManager;
/*     */ import weaver.workflow.workflow.WFNodeDtlFieldManager;
/*     */ import weaver.workflow.workflow.WFNodeFieldMainManager;
/*     */ import weaver.workflow.workflow.WFNodeFieldManager;
/*     */ 
/*     */ public class HtmlLayoutManager
/*     */   extends BaseBean {
/*     */   private User user;
/*     */   private FileUpload fu;
/*     */   
/*     */   public int doPrepPrintMode() {
/*  28 */     int i = 0;
/*     */     try {
/*  30 */       WFNodeFieldManager wFNodeFieldManager = new WFNodeFieldManager();
/*  31 */       wFNodeFieldManager.setFu(this.fu);
/*  32 */       wFNodeFieldManager.setUser(this.user);
/*  33 */       i = wFNodeFieldManager.createDefaultLayout(1, 1);
/*  34 */       int j = Util.getIntValue(this.fu.getParameter("wfid"), 0);
/*  35 */       int k = Util.getIntValue(this.fu.getParameter("nodeid"), 0);
/*  36 */       wFNodeFieldManager.setNodeModeToHtml(j, k);
/*  37 */     } catch (Exception exception) {
/*  38 */       writeLog(exception);
/*  39 */       i = -1;
/*     */     } 
/*  41 */     return i;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public int doSaveLayoutInfo() {
/*  47 */     int i = 0;
/*  48 */     ConnStatement connStatement = null;
/*     */     try {
/*  50 */       WFNodeFieldManager wFNodeFieldManager = new WFNodeFieldManager();
/*  51 */       String str1 = "";
/*  52 */       RecordSet recordSet = new RecordSet();
/*  53 */       connStatement = new ConnStatement();
/*  54 */       int j = Util.getIntValue(this.fu.getParameter("wfid"), 0);
/*  55 */       int k = Util.getIntValue(this.fu.getParameter("formid"), 0);
/*  56 */       int m = Util.getIntValue(this.fu.getParameter("nodeid"), 0);
/*  57 */       int n = Util.getIntValue(this.fu.getParameter("modeid"), 0);
/*  58 */       int i1 = Util.getIntValue(this.fu.getParameter("isbill"), -1);
/*  59 */       int i2 = Util.getIntValue(this.fu.getParameter("layouttype"), -1);
/*  60 */       int i3 = Util.getIntValue(this.fu.getParameter("isform"), -1);
/*     */ 
/*     */       
/*  63 */       String str2 = Util.htmlFilter4UTF8(Util.null2String(this.fu.getParameter("layoutname")));
/*  64 */       String str3 = Util.htmlFilter4UTF8(Util.null2String(this.fu.getParameter("layouttext")));
/*  65 */       int i4 = Util.getIntValue(Util.null2String(this.fu.getParameter("htmlParseScheme")), 0);
/*  66 */       if (str3.indexOf("<script>initFlashVideo();</script>") > -1) {
/*  67 */         str3 = str3.substring(0, str3.indexOf("<script>initFlashVideo();</script>"));
/*     */       }
/*  69 */       str3 = str3.trim();
/*  70 */       str3 = str3.replaceAll("<p>\\s*\\&nbsp;\\s*</p>", "");
/*     */       
/*  72 */       int i5 = Util.getIntValue(this.fu.getParameter("docimages_num"), 0);
/*  73 */       String[] arrayOfString1 = new String[i5];
/*  74 */       for (byte b1 = 0; b1 < i5; b1++) {
/*  75 */         arrayOfString1[b1] = "docimages_" + b1;
/*     */       }
/*  77 */       String[] arrayOfString2 = this.fu.uploadFiles(arrayOfString1);
/*  78 */       String[] arrayOfString3 = this.fu.getFileNames();
/*  79 */       int i6 = str3.indexOf(" alt=\"docimages_0\" ");
/*  80 */       if (i6 != -1) {
/*  81 */         int i8 = str3.indexOf("src", i6);
/*  82 */         if (i8 != -1) {
/*  83 */           int i9 = str3.indexOf("\"", i8);
/*  84 */           String str = str3.substring(i8, i9);
/*  85 */           str3 = Util.StringReplace(str3, str, "src=");
/*     */         } 
/*     */       } 
/*     */       
/*  89 */       String str4 = "";
/*  90 */       for (byte b2 = 0; b2 < i5; b2++) {
/*  91 */         str4 = str4 + arrayOfString2[b2] + ",";
/*     */       }
/*  93 */       Hashtable<Object, Object> hashtable = new Hashtable<>();
/*  94 */       str1 = "select imagefileid, filerealpath from imagefile where imagefileid in (" + str4 + "0)";
/*  95 */       recordSet.execute(str1);
/*  96 */       while (recordSet.next()) {
/*  97 */         int i8 = Util.getIntValue(recordSet.getString("imagefileid"), 0);
/*  98 */         String str = Util.null2String(recordSet.getString("filerealpath"));
/*  99 */         str = Util.StringReplace(str, GCONST.getRootPath(), "/");
/* 100 */         str = Util.StringReplace(str, "" + File.separatorChar, "/");
/* 101 */         hashtable.put("filerealpath" + i8, str);
/*     */       } 
/*     */ 
/*     */       
/* 105 */       String[] arrayOfString4 = this.fu.getParameters("moduleimages");
/*     */       
/* 107 */       if (arrayOfString4 != null && arrayOfString4.length > 0) {
/* 108 */         int i8 = str3.indexOf("/filesystem/htmllayoutimages");
/* 109 */         while (i8 != -1) {
/* 110 */           int i9 = str3.lastIndexOf("\"", i8);
/* 111 */           if (i8 - i9 > 1) {
/* 112 */             String str = str3.substring(0, i9 + 1);
/* 113 */             str = str + str3.substring(i8);
/* 114 */             str3 = str;
/* 115 */             i8 = str3.indexOf("/filesystem/htmllayoutimages", i8 + 1); continue;
/*     */           } 
/* 117 */           i8 = str3.indexOf("/filesystem/htmllayoutimages", i8 + 1);
/*     */         } 
/*     */       } 
/*     */ 
/*     */       
/* 122 */       for (byte b3 = 0; b3 < i5; b3++) {
/* 123 */         int i8 = str3.indexOf(" alt=\"docimages_" + b3 + "\" ");
/*     */         
/* 125 */         if (i8 != -1) {
/* 126 */           int i9 = i8;
/* 127 */           String str = str3.substring(0, i8);
/* 128 */           str = str + " alt=\"" + arrayOfString3[b3] + "\" ";
/*     */           
/* 130 */           i8 = str3.indexOf("src=\"", i8);
/* 131 */           int i10 = str3.indexOf("\"", i8 + 5);
/*     */           
/* 133 */           str = str + str3.substring(i9 + (" alt=\"docimages_" + b3 + "\" ").length(), i8);
/* 134 */           str = str + "src=\"" + Util.null2String((String)hashtable.get("filerealpath" + arrayOfString2[b3]));
/* 135 */           str = str + "\"";
/* 136 */           str = str + str3.substring(i10 + 1);
/* 137 */           str3 = str;
/*     */         } else {
/* 139 */           recordSet.execute("delete from imagefile where imagefileid=" + arrayOfString2[b3]);
/*     */         } 
/*     */       } 
/*     */ 
/*     */       
/* 144 */       recordSet.execute("delete from workflow_nodefieldattr where nodeid=" + m);
/* 145 */       str1 = "insert into workflow_nodefieldattr (fieldid, formid, isbill, nodeid, attrcontent, caltype, othertype, transtype) values (?,?,?,?,?,?,?,?)";
/* 146 */       String str5 = this.fu.getParameter("fieldids");
/* 147 */       String[] arrayOfString5 = str5.split(",");
/* 148 */       if (arrayOfString5 != null && arrayOfString5.length > 0) {
/* 149 */         for (byte b = 0; b < arrayOfString5.length; b++) {
/* 150 */           int i8 = Util.getIntValue(arrayOfString5[b], 0);
/* 151 */           if (i8 != 0) {
/* 152 */             String str = Util.null2String(this.fu.getParameter("fieldsql" + i8));
/* 153 */             int i9 = Util.getIntValue(this.fu.getParameter("caltype" + i8), 0);
/* 154 */             int i10 = Util.getIntValue(this.fu.getParameter("othertype" + i8), 0);
/* 155 */             int i11 = Util.getIntValue(this.fu.getParameter("transtype" + i8), 0);
/* 156 */             if (!"".equals(str)) {
/* 157 */               connStatement.setStatementSql(str1);
/* 158 */               connStatement.setInt(1, i8);
/* 159 */               connStatement.setInt(2, k);
/* 160 */               connStatement.setInt(3, i1);
/* 161 */               connStatement.setInt(4, m);
/* 162 */               connStatement.setString(5, str);
/* 163 */               connStatement.setInt(6, i9);
/* 164 */               connStatement.setInt(7, i10);
/* 165 */               connStatement.setInt(8, i11);
/* 166 */               connStatement.executeUpdate();
/*     */             } 
/*     */           } 
/*     */         } 
/*     */       }
/* 171 */       int i7 = Util.getIntValue(this.fu.getParameter("cssfile"), 0);
/* 172 */       String str6 = wFNodeFieldManager.createHtmlFile(m, j, i2, str3);
/* 173 */       if (i3 == 0) {
/* 174 */         SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
/* 175 */         String str = simpleDateFormat.format(new Date());
/* 176 */         if (n <= 0) {
/* 177 */           str1 = "insert into workflow_nodehtmllayout (workflowid,formid,isbill,nodeid,type,layoutname,syspath, cssfile, htmlparsescheme,operuser,opertime,isactive) values (?,?,?,?,?,?,?,?,?,?,?,1)";
/* 178 */           connStatement.setStatementSql(str1);
/* 179 */           connStatement.setInt(1, j);
/* 180 */           connStatement.setInt(2, k);
/* 181 */           connStatement.setInt(3, i1);
/* 182 */           connStatement.setInt(4, m);
/* 183 */           connStatement.setInt(5, i2);
/* 184 */           connStatement.setString(6, str2);
/* 185 */           connStatement.setString(7, str6);
/* 186 */           connStatement.setInt(8, i7);
/* 187 */           connStatement.setInt(9, i4);
/* 188 */           connStatement.setInt(10, this.user.getUID());
/* 189 */           connStatement.setString(11, str);
/*     */         } else {
/* 191 */           str1 = "update workflow_nodehtmllayout set layoutname=?, syspath=?, cssfile=?, htmlparsescheme=?,operuser=?,opertime=? where id=" + n;
/* 192 */           connStatement.setStatementSql(str1);
/* 193 */           connStatement.setString(1, str2);
/* 194 */           connStatement.setString(2, str6);
/* 195 */           connStatement.setInt(3, i7);
/* 196 */           connStatement.setInt(4, i4);
/* 197 */           connStatement.setInt(5, this.user.getUID());
/* 198 */           connStatement.setString(6, str);
/*     */         } 
/* 200 */         connStatement.executeUpdate();
/* 201 */         if (n <= 0) {
/* 202 */           recordSet.execute("select max(id) as id from workflow_nodehtmllayout where workflowid=" + j + " and nodeid=" + m + " and type=" + i2 + " and formid=" + k + " and isbill=" + i1);
/* 203 */           if (recordSet.next()) {
/* 204 */             n = Util.getIntValue(recordSet.getString("id"), 0);
/*     */           }
/*     */         } 
/*     */         
/* 208 */         if (i2 == 0) {
/* 209 */           setNodeFieldInfo2();
/*     */         }
/*     */       } 
/* 212 */       if (Util.null2String(this.fu.getParameter("curmodetype")).equals("0")) {
/* 213 */         recordSet.executeSql("update workflow_flownode set ismode='0' where workflowid=" + j + " and nodeid=" + m);
/*     */       } else {
/* 215 */         wFNodeFieldManager.setNodeModeToHtml(j, m);
/*     */       } 
/* 217 */       i = n;
/* 218 */     } catch (Exception exception) {
/* 219 */       i = -1;
/* 220 */       writeLog(exception);
/*     */     } finally {
/*     */       try {
/* 223 */         connStatement.close();
/* 224 */       } catch (Exception exception) {
/* 225 */         writeLog(exception);
/*     */       } 
/*     */     } 
/* 228 */     return i;
/*     */   }
/*     */ 
/*     */   
/*     */   public int setNodeFieldInfo2() {
/* 233 */     byte b = 0;
/*     */     try {
/* 235 */       WFNodeFieldMainManager wFNodeFieldMainManager = new WFNodeFieldMainManager();
/* 236 */       WFNodeDtlFieldManager wFNodeDtlFieldManager = new WFNodeDtlFieldManager();
/* 237 */       FormFieldMainManager formFieldMainManager = new FormFieldMainManager();
/* 238 */       RecordSet recordSet = new RecordSet();
/* 239 */       int i = Util.getIntValue(this.fu.getParameter("nodeid"), 0);
/* 240 */       int j = Util.getIntValue(this.fu.getParameter("formid"), 0);
/* 241 */       int k = Util.getIntValue(this.fu.getParameter("isbill"), 0);
/* 242 */       String str1 = Util.null2String(this.fu.getParameter("layouttext"));
/* 243 */       String str2 = "";
/* 244 */       String str3 = "";
/* 245 */       String str4 = "";
/* 246 */       int m = 0;
/*     */       
/* 248 */       m = Util.getIntValue(this.fu.getParameter("fieldattr-1"), 0);
/* 249 */       if (str1.indexOf("$field-1$") == -1) {
/* 250 */         m = 0;
/*     */       }
/* 252 */       if (m == 3) {
/* 253 */         str4 = "1";
/* 254 */         str3 = "1";
/* 255 */         str2 = "1";
/* 256 */       } else if (m == 2) {
/* 257 */         str3 = "1";
/* 258 */         str2 = "1";
/* 259 */       } else if (m == 1) {
/* 260 */         str2 = "1";
/*     */       } 
/* 262 */       wFNodeFieldMainManager.resetParameter();
/* 263 */       wFNodeFieldMainManager.setNodeid(i);
/* 264 */       wFNodeFieldMainManager.setFieldid(-1);
/* 265 */       wFNodeFieldMainManager.setIsview(str2);
/* 266 */       wFNodeFieldMainManager.setIsedit(str3);
/* 267 */       wFNodeFieldMainManager.setIsmandatory(str4);
/* 268 */       wFNodeFieldMainManager.saveWfNodeField2();
/*     */       
/* 270 */       str2 = "";
/* 271 */       str3 = "";
/* 272 */       str4 = "";
/* 273 */       m = 0;
/*     */       
/* 275 */       m = Util.getIntValue(this.fu.getParameter("fieldattr-2"), 0);
/* 276 */       if (str1.indexOf("$field-2$") == -1) {
/* 277 */         m = 0;
/*     */       }
/* 279 */       if (m == 3) {
/* 280 */         str4 = "1";
/* 281 */         str3 = "1";
/* 282 */         str2 = "1";
/* 283 */       } else if (m == 2) {
/* 284 */         str3 = "1";
/* 285 */         str2 = "1";
/* 286 */       } else if (m == 1) {
/* 287 */         str2 = "1";
/*     */       } 
/* 289 */       wFNodeFieldMainManager.resetParameter();
/* 290 */       wFNodeFieldMainManager.setNodeid(i);
/* 291 */       wFNodeFieldMainManager.setFieldid(-2);
/* 292 */       wFNodeFieldMainManager.setIsview(str2);
/* 293 */       wFNodeFieldMainManager.setIsedit(str3);
/* 294 */       wFNodeFieldMainManager.setIsmandatory(str4);
/* 295 */       wFNodeFieldMainManager.saveWfNodeField2();
/*     */       
/* 297 */       str2 = "";
/* 298 */       str3 = "";
/* 299 */       str4 = "";
/* 300 */       m = 0;
/*     */       
/* 302 */       m = Util.getIntValue(this.fu.getParameter("fieldattr-3"), 0);
/* 303 */       if (str1.indexOf("$field-3$") == -1) {
/* 304 */         m = 0;
/*     */       }
/* 306 */       if (m == 3) {
/* 307 */         str4 = "1";
/* 308 */         str3 = "1";
/* 309 */         str2 = "1";
/* 310 */       } else if (m == 2) {
/* 311 */         str3 = "1";
/* 312 */         str2 = "1";
/* 313 */       } else if (m == 1) {
/* 314 */         str2 = "1";
/*     */       } 
/* 316 */       wFNodeFieldMainManager.resetParameter();
/* 317 */       wFNodeFieldMainManager.setNodeid(i);
/* 318 */       wFNodeFieldMainManager.setFieldid(-3);
/* 319 */       wFNodeFieldMainManager.setIsview(str2);
/* 320 */       wFNodeFieldMainManager.setIsedit(str3);
/* 321 */       wFNodeFieldMainManager.setIsmandatory(str4);
/* 322 */       wFNodeFieldMainManager.saveWfNodeField2();
/*     */       
/* 324 */       str2 = "";
/* 325 */       str3 = "";
/* 326 */       str4 = "";
/* 327 */       m = 0;
/*     */       
/* 329 */       m = Util.getIntValue(this.fu.getParameter("fieldattr-5"), 0);
/* 330 */       if (str1.indexOf("$field-5$") == -1) {
/* 331 */         m = 0;
/*     */       }
/* 333 */       if (m == 3) {
/* 334 */         str4 = "1";
/* 335 */         str3 = "1";
/* 336 */         str2 = "1";
/* 337 */       } else if (m == 2) {
/* 338 */         str3 = "1";
/* 339 */         str2 = "1";
/* 340 */       } else if (m == 1) {
/* 341 */         str2 = "1";
/*     */       } 
/* 343 */       wFNodeFieldMainManager.resetParameter();
/* 344 */       wFNodeFieldMainManager.setNodeid(i);
/* 345 */       wFNodeFieldMainManager.setFieldid(-5);
/* 346 */       wFNodeFieldMainManager.setIsview(str2);
/* 347 */       wFNodeFieldMainManager.setIsedit(str3);
/* 348 */       wFNodeFieldMainManager.setIsmandatory(str4);
/* 349 */       wFNodeFieldMainManager.saveWfNodeField2();
/*     */       
/* 351 */       str2 = "";
/* 352 */       str3 = "";
/* 353 */       str4 = "";
/* 354 */       m = 0;
/*     */       
/* 356 */       m = Util.getIntValue(this.fu.getParameter("fieldattr-4"), 0);
/* 357 */       if (str1.indexOf("$field-4$") == -1) {
/* 358 */         m = 0;
/*     */       }
/* 360 */       if (m > 1) {
/* 361 */         m = 1;
/*     */       }
/* 363 */       if (m == 3) {
/* 364 */         str4 = "1";
/* 365 */         str3 = "1";
/* 366 */         str2 = "1";
/* 367 */       } else if (m == 2) {
/* 368 */         str3 = "1";
/* 369 */         str2 = "1";
/* 370 */       } else if (m == 1) {
/* 371 */         str2 = "1";
/*     */       } 
/* 373 */       wFNodeFieldMainManager.resetParameter();
/* 374 */       wFNodeFieldMainManager.setNodeid(i);
/* 375 */       wFNodeFieldMainManager.setFieldid(-4);
/* 376 */       wFNodeFieldMainManager.setIsview(str2);
/* 377 */       wFNodeFieldMainManager.setIsedit(str3);
/* 378 */       wFNodeFieldMainManager.setIsmandatory(str4);
/* 379 */       wFNodeFieldMainManager.saveWfNodeField2();
/*     */       
/* 381 */       if (k == 0) {
/* 382 */         formFieldMainManager.setFormid(j);
/* 383 */         formFieldMainManager.selectAllFormField();
/* 384 */         int n = -1;
/* 385 */         while (formFieldMainManager.next()) {
/* 386 */           str2 = "";
/* 387 */           str3 = "";
/* 388 */           str4 = "";
/* 389 */           m = 0;
/* 390 */           int i1 = formFieldMainManager.getFieldid();
/* 391 */           int i2 = formFieldMainManager.getGroupid();
/* 392 */           if (i2 == -1) i2 = 999; 
/* 393 */           String str = formFieldMainManager.getIsdetail();
/* 394 */           wFNodeFieldMainManager.resetParameter();
/* 395 */           wFNodeFieldMainManager.setNodeid(i);
/* 396 */           wFNodeFieldMainManager.setFieldid(i1);
/* 397 */           m = Util.getIntValue(this.fu.getParameter("fieldattr" + i1), 0);
/* 398 */           if (str1.indexOf("$field" + i1 + "$") == -1) {
/* 399 */             m = 0;
/*     */           }
/* 401 */           if (m == 3) {
/* 402 */             str4 = "1";
/* 403 */             str3 = "1";
/* 404 */             str2 = "1";
/* 405 */           } else if (m == 2) {
/* 406 */             str3 = "1";
/* 407 */             str2 = "1";
/* 408 */           } else if (m == 1) {
/* 409 */             str2 = "1";
/*     */           } 
/* 411 */           wFNodeFieldMainManager.setIsview(str2);
/* 412 */           wFNodeFieldMainManager.setIsedit(str3);
/* 413 */           wFNodeFieldMainManager.setIsmandatory(str4);
/* 414 */           wFNodeFieldMainManager.saveWfNodeField2();
/*     */           
/* 416 */           if (str.equals("1") && i2 > n) {
/* 417 */             n = i2;
/* 418 */             String str5 = Util.null2String(this.fu.getParameter("detailgroupattr" + i2));
/* 419 */             wFNodeDtlFieldManager.setNodeid(i);
/* 420 */             wFNodeDtlFieldManager.setGroupid(i2);
/* 421 */             wFNodeDtlFieldManager.setIsadd("0");
/* 422 */             wFNodeDtlFieldManager.setIsedit("0");
/* 423 */             wFNodeDtlFieldManager.setIsdelete("0");
/* 424 */             wFNodeDtlFieldManager.setIshide("0");
/* 425 */             wFNodeDtlFieldManager.setIsdefault("0");
/* 426 */             wFNodeDtlFieldManager.setIsneed("0");
/* 427 */             wFNodeDtlFieldManager.setIsopensapmul("0");
/* 428 */             if (str5.indexOf("1") == 0) {
/* 429 */               wFNodeDtlFieldManager.setIsadd("1");
/*     */             }
/* 431 */             if (str5.indexOf("1", 1) == 1) {
/* 432 */               wFNodeDtlFieldManager.setIsedit("1");
/*     */             }
/* 434 */             if (str5.indexOf("1", 2) == 2) {
/* 435 */               wFNodeDtlFieldManager.setIsdelete("1");
/*     */             }
/* 437 */             if (str5.indexOf("1", 3) == 3) {
/* 438 */               wFNodeDtlFieldManager.setIshide("1");
/*     */             }
/* 440 */             if (str5.indexOf("1", 4) == 4) {
/* 441 */               wFNodeDtlFieldManager.setIsdefault("1");
/*     */             }
/* 443 */             if (str5.indexOf("1", 5) == 5) {
/* 444 */               wFNodeDtlFieldManager.setIsneed("1");
/*     */             }
/* 446 */             if (str5.indexOf("1", 6) == 6) {
/* 447 */               wFNodeDtlFieldManager.setIsopensapmul("1");
/*     */             }
/* 449 */             wFNodeDtlFieldManager.setDefaultrows(getDefaultRowCount(i, i2));
/* 450 */             wFNodeDtlFieldManager.saveWfNodeDtlField();
/*     */           } 
/*     */         } 
/* 453 */       } else if (k == 1) {
/* 454 */         boolean bool = false;
/* 455 */         recordSet.executeSql("select tablename from workflow_bill where id = " + j);
/*     */         
/* 457 */         String str5 = Util.null2String(recordSet.getString("tablename"));
/* 458 */         if (recordSet.next() && (str5.equals("formtable_main_" + (j * -1)) || str5.startsWith("uf_"))) {
/* 459 */           bool = true;
/*     */         }
/*     */         
/* 462 */         str5 = "";
/* 463 */         if (bool == true) {
/* 464 */           if ("ORACLE".equalsIgnoreCase(recordSet.getDBType())) {
/* 465 */             str5 = "select * from workflow_billfield where billid = " + j + " order by viewtype,TO_NUMBER((select orderid from Workflow_billdetailtable bd where bd.billid = billid and bd.tablename = detailtable)),dsporder ";
/* 466 */           } else if (recordSet.getDBType().equals("mysql")) {
/* 467 */             str5 = "select * from workflow_billfield where billid = " + j + " order by viewtype,convert((select orderid from Workflow_billdetailtable bd where bd.billid = billid and bd.tablename = detailtable),SIGNED),dsporder ";
/*     */           }
/* 469 */           else if ("postgresql".equalsIgnoreCase(recordSet.getDBType())) {
/* 470 */             str5 = "select * from workflow_billfield where billid = " + j + " order by viewtype,TO_NUMBER((select orderid from Workflow_billdetailtable bd where bd.billid = billid and bd.tablename = detailtable)),dsporder ";
/*     */           } else {
/*     */             
/* 473 */             str5 = "select * from workflow_billfield where billid = " + j + " order by viewtype,convert(int, (select orderid from Workflow_billdetailtable bd where bd.billid = billid and bd.tablename = detailtable)),dsporder ";
/*     */           } 
/*     */         } else {
/* 476 */           str5 = "select * from workflow_billfield where billid = " + j + " order by viewtype,detailtable,dsporder ";
/*     */         } 
/* 478 */         recordSet.executeSql(str5);
/* 479 */         byte b1 = 0;
/* 480 */         String str6 = null;
/* 481 */         while (recordSet.next()) {
/* 482 */           str2 = "";
/* 483 */           str3 = "";
/* 484 */           str4 = "";
/* 485 */           m = 0;
/* 486 */           int n = recordSet.getInt("id");
/* 487 */           int i1 = recordSet.getInt("viewtype");
/* 488 */           String str = Util.null2String(recordSet.getString("detailtable"));
/* 489 */           wFNodeFieldMainManager.resetParameter();
/* 490 */           wFNodeFieldMainManager.setNodeid(i);
/* 491 */           wFNodeFieldMainManager.setFieldid(n);
/* 492 */           if (i1 == 1 && !str.equals(str6)) {
/* 493 */             str6 = str;
/* 494 */             b1++;
/*     */           } 
/* 496 */           m = Util.getIntValue(this.fu.getParameter("fieldattr" + n), 0);
/* 497 */           if (str1.indexOf("$field" + n + "$") == -1) {
/* 498 */             m = 0;
/*     */           }
/* 500 */           if (m == 3) {
/* 501 */             str4 = "1";
/* 502 */             str3 = "1";
/* 503 */             str2 = "1";
/* 504 */           } else if (m == 2) {
/* 505 */             str3 = "1";
/* 506 */             str2 = "1";
/* 507 */           } else if (m == 1) {
/* 508 */             str2 = "1";
/*     */           } 
/* 510 */           wFNodeFieldMainManager.setIsview(str2);
/* 511 */           wFNodeFieldMainManager.setIsedit(str3);
/* 512 */           wFNodeFieldMainManager.setIsmandatory(str4);
/* 513 */           wFNodeFieldMainManager.saveWfNodeField2();
/*     */         } 
/* 515 */         for (byte b2 = 0; b2 < b1; b2++) {
/* 516 */           String str = Util.null2String(this.fu.getParameter("detailgroupattr" + b2));
/* 517 */           wFNodeDtlFieldManager.setNodeid(i);
/* 518 */           wFNodeDtlFieldManager.setGroupid(b2);
/* 519 */           wFNodeDtlFieldManager.setIsadd("0");
/* 520 */           wFNodeDtlFieldManager.setIsedit("0");
/* 521 */           wFNodeDtlFieldManager.setIsdelete("0");
/* 522 */           wFNodeDtlFieldManager.setIshide("0");
/* 523 */           wFNodeDtlFieldManager.setIsdefault("0");
/* 524 */           wFNodeDtlFieldManager.setIsneed("0");
/* 525 */           wFNodeDtlFieldManager.setIsopensapmul("0");
/* 526 */           if (str.indexOf("1") == 0) {
/* 527 */             wFNodeDtlFieldManager.setIsadd("1");
/*     */           }
/* 529 */           if (str.indexOf("1", 1) == 1) {
/* 530 */             wFNodeDtlFieldManager.setIsedit("1");
/*     */           }
/* 532 */           if (str.indexOf("1", 2) == 2) {
/* 533 */             wFNodeDtlFieldManager.setIsdelete("1");
/*     */           }
/* 535 */           if (str.indexOf("1", 3) == 3) {
/* 536 */             wFNodeDtlFieldManager.setIshide("1");
/*     */           }
/* 538 */           if (str.indexOf("1", 4) == 4) {
/* 539 */             wFNodeDtlFieldManager.setIsdefault("1");
/*     */           }
/* 541 */           if (str.indexOf("1", 5) == 5) {
/* 542 */             wFNodeDtlFieldManager.setIsneed("1");
/*     */           }
/* 544 */           if (str.indexOf("1", 6) == 6) {
/* 545 */             wFNodeDtlFieldManager.setIsopensapmul("1");
/*     */           }
/*     */           
/* 548 */           wFNodeDtlFieldManager.setDefaultrows(getDefaultRowCount(i, b2));
/*     */           
/* 550 */           wFNodeDtlFieldManager.saveWfNodeDtlField();
/*     */         } 
/*     */       } 
/* 553 */       b = 1;
/* 554 */     } catch (Exception exception) {
/* 555 */       b = -1;
/* 556 */       writeLog(exception);
/*     */     } 
/* 558 */     return b;
/*     */   }
/*     */   
/*     */   public int getDefaultRowCount(int paramInt1, int paramInt2) {
/*     */     try {
/* 563 */       RecordSet recordSet = new RecordSet();
/* 564 */       recordSet.executeSql("select defaultrows from workflow_NodeFormGroup where nodeid=" + paramInt1 + " and groupid=" + paramInt2);
/* 565 */       recordSet.next();
/* 566 */       return recordSet.getInt(1);
/* 567 */     } catch (Exception exception) {
/* 568 */       return -1;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String trans2OutLayout(String paramString) {
/* 577 */     String str = "";
/*     */     try {
/* 579 */       String str1 = "";
/* 580 */       String str2 = "";
/* 581 */       str = paramString;
/* 582 */       ArrayList<String> arrayList = new ArrayList();
/* 583 */       arrayList.add("$label");
/* 584 */       arrayList.add("$field");
/* 585 */       arrayList.add("$node");
/*     */ 
/*     */       
/* 588 */       for (byte b = 0; b < arrayList.size(); b++) {
/* 589 */         String str3 = Util.null2String(arrayList.get(b));
/* 590 */         int i = str.toLowerCase().indexOf(str3);
/* 591 */         while (i > -1) {
/* 592 */           str1 = paramString.substring(0, i);
/* 593 */           str2 = paramString.substring(i + 1);
/* 594 */           int j = str1.lastIndexOf("<");
/* 595 */           int k = str2.indexOf(">");
/* 596 */           if (j > -1) {
/* 597 */             str1 = str1.substring(0, j);
/*     */           }
/* 599 */           if (k > -1) {
/* 600 */             str2 = str2.substring(k + 1);
/*     */           }
/* 602 */           paramString = str1 + "&nbsp;" + str2;
/* 603 */           str = paramString;
/* 604 */           i = str.toLowerCase().indexOf(str3);
/*     */         }
/*     */       
/*     */       } 
/* 608 */     } catch (Exception exception) {
/* 609 */       writeLog(exception);
/*     */     } 
/* 611 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String upHtmlFile(FileUpload paramFileUpload) {
/* 618 */     String str = "";
/*     */     
/*     */     try {
/* 621 */       FileManage fileManage = new FileManage();
/* 622 */       int i = Util.getIntValue(paramFileUpload.uploadFiles("htmlfile"), 0);
/*     */       
/* 624 */       String str1 = "";
/*     */       
/* 626 */       RecordSet recordSet = new RecordSet();
/* 627 */       String str2 = "";
/* 628 */       String str3 = "select filerealpath from imagefile where imagefileid = " + i;
/* 629 */       recordSet.executeSql(str3);
/* 630 */       if (recordSet.next()) {
/* 631 */         str2 = Util.null2String(recordSet.getString("filerealpath"));
/*     */       }
/*     */       
/* 634 */       if (!"".equals(str2)) {
/*     */         try {
/* 636 */           WFNodeFieldManager wFNodeFieldManager = new WFNodeFieldManager();
/* 637 */           str = wFNodeFieldManager.readHtmlFile(str2);
/*     */ 
/*     */           
/* 640 */           FileSecurityUtil.deleteFile(str2);
/* 641 */         } catch (Exception exception) {
/* 642 */           writeLog(exception);
/*     */         }
/*     */       
/*     */       }
/* 646 */     } catch (Exception exception) {
/* 647 */       writeLog(exception);
/*     */     } 
/*     */     
/* 650 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static void main(String[] paramArrayOfString) {}
/*     */ 
/*     */ 
/*     */   
/*     */   public User getUser() {
/* 660 */     return this.user;
/*     */   }
/*     */   public void setUser(User paramUser) {
/* 663 */     this.user = paramUser;
/*     */   }
/*     */   public FileUpload getFu() {
/* 666 */     return this.fu;
/*     */   }
/*     */   public void setFu(FileUpload paramFileUpload) {
/* 669 */     this.fu = paramFileUpload;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/html/HtmlLayoutManager.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */