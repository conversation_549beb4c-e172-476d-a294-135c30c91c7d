/*    */ package weaver.workflow.html;
/*    */ 
/*    */ import weaver.general.BaseBean;
/*    */ 
/*    */ public class MoneyTrans4FieldTrans
/*    */   extends BaseBean
/*    */   implements FieldTransInterface {
/*    */   public String getTransJs(String paramString) {
/*  9 */     String str = "";
/*    */     try {
/* 11 */       str = str + "function doformat" + paramString + "(){\n";
/*    */       
/* 13 */       str = str + "\tjQuery(\"[id^='field" + paramString + "']\").each(\n";
/* 14 */       str = str + "\t\tfunction(i){\n";
/* 15 */       str = str + "\t\t\tif(this.type==\"hidden\"){\n";
/* 16 */       str = str + "\t\t\t\tif(document.getElementById(this.id+\"span\")){\n";
/* 17 */       str = str + "\t\t\t\t\tdocument.getElementById(this.id+\"span\").innerHTML = numberChangeToChinese(this.value);\n";
/* 18 */       str = str + "\t\t\t\t}\n";
/* 19 */       str = str + "\t\t\t}\n";
/* 20 */       str = str + "\t\t}\n";
/* 21 */       str = str + "\t);\n";
/* 22 */       str = str + "}\n";
/*    */       
/* 24 */       str = str + "jQuery(document).ready(function(){\n";
/* 25 */       str = str + "\ttry{\n";
/* 26 */       str = str + "\tif(typeof(doMathFieldAttr" + paramString + ") == \"function\"){\n";
/* 27 */       str = str + "\tdoMathFieldAttr" + paramString + "();\n";
/* 28 */       str = str + "\t}else{doformat" + paramString + "();}\n";
/* 29 */       str = str + "\t}catch(e){}\n";
/* 30 */       str = str + "});\n";
/*    */     }
/* 32 */     catch (Exception exception) {}
/*    */ 
/*    */     
/* 35 */     return str;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/html/MoneyTrans4FieldTrans.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */