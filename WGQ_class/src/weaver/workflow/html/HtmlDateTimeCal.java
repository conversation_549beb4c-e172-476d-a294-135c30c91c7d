/*     */ package weaver.workflow.html;
/*     */ 
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.dateformat.DateTransformer;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.TimeUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.company.DepartmentComInfo;
/*     */ import weaver.hrm.report.schedulediff.HrmScheduleDiffUtil;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ 
/*     */ public class HtmlDateTimeCal extends BaseBean {
/*     */   private User user;
/*  15 */   private int trunToHour = 0;
/*     */ 
/*     */ 
/*     */   
/*     */   public void setUser(User paramUser) {
/*  20 */     this.user = paramUser;
/*     */   }
/*     */   
/*     */   public void setTrunToHour(int paramInt) {
/*  24 */     this.trunToHour = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static void main(String[] paramArrayOfString) {}
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String dohtmlDateTimeCal(String paramString, int paramInt) {
/*  39 */     String str = "";
/*     */     try {
/*  41 */       String str1 = "";
/*  42 */       String str2 = "";
/*  43 */       String str3 = "";
/*  44 */       String str4 = "";
/*  45 */       String str5 = "";
/*  46 */       String str6 = "";
/*  47 */       String str7 = "";
/*  48 */       int i = -1;
/*  49 */       byte b = 0;
/*  50 */       String str8 = "";
/*  51 */       double d = 0.0D;
/*  52 */       boolean bool1 = false;
/*  53 */       boolean bool2 = false;
/*  54 */       String str9 = paramString;
/*  55 */       int j = paramString.indexOf("$");
/*  56 */       while (j > -1) {
/*  57 */         String str10 = "";
/*  58 */         int k = paramString.indexOf("$", j + 1);
/*  59 */         String str11 = paramString.substring(j + 1, k);
/*     */         
/*  61 */         paramString = paramString.substring(k + 1);
/*  62 */         if ("datetime".equals(str11)) {
/*     */           
/*  64 */           bool1 = true;
/*     */         } else {
/*  66 */           int m = paramString.indexOf("$" + str11 + "$");
/*  67 */           String str12 = paramString.substring(0, m);
/*     */           
/*  69 */           if ("date".equalsIgnoreCase(str11)) {
/*  70 */             if ("".equals(str1)) {
/*  71 */               str1 = str12;
/*  72 */               b++;
/*     */             } else {
/*  74 */               str3 = str12;
/*  75 */               if (!bool1) {
/*  76 */                 b += 2;
/*     */               }
/*     */             } 
/*  79 */           } else if ("time".equalsIgnoreCase(str11)) {
/*  80 */             if ("".equals(str2)) {
/*  81 */               str2 = str12;
/*     */             } else {
/*  83 */               str4 = str12;
/*  84 */               if (bool1 == true) {
/*  85 */                 b += 2;
/*     */               }
/*     */             } 
/*  88 */           } else if ("input".equalsIgnoreCase(str11)) {
/*  89 */             i = Util.getIntValue(str12, 0);
/*  90 */             b += 5;
/*     */           } 
/*     */           try {
/*  93 */             if (b == 3 || b == 6 || b == 5) {
/*  94 */               if (b == 3) {
/*  95 */                 d = Util.getDoubleValue(getDateTimeCal(str1, str2, str3, str4, bool1, paramInt, str7));
/*  96 */                 str = "" + d;
/*  97 */                 i = Util.getIntValue(str);
/*  98 */                 str1 = "";
/*  99 */                 str2 = "";
/* 100 */                 str3 = "";
/* 101 */                 str3 = "";
/* 102 */               } else if (b == 6) {
/* 103 */                 str8 = getDateTimeInputCal(str1, str2, i, bool1, paramInt, str7);
/* 104 */                 str = str8;
/* 105 */               } else if (b == 5) {
/* 106 */                 if ("+".equalsIgnoreCase(str7)) {
/* 107 */                   d += i;
/*     */                 } else {
/* 109 */                   d -= i;
/*     */                 } 
/* 111 */                 str = "" + d;
/*     */               } 
/*     */               
/* 114 */               b = 0;
/*     */             } 
/* 116 */           } catch (Exception exception) {
/* 117 */             writeLog(exception);
/* 118 */             b = 0;
/*     */           } 
/* 120 */           int n = paramString.indexOf("$", m + str11.length() + 2);
/* 121 */           if (n > -1) {
/* 122 */             str10 = paramString.substring(m + str11.length() + 2, n);
/* 123 */             if (!"".equals(str10.trim())) {
/* 124 */               str7 = str10;
/*     */             }
/*     */           } 
/* 127 */           paramString = paramString.substring(m + str11.length() + 2);
/*     */         } 
/* 129 */         j = paramString.indexOf("$");
/*     */       } 
/* 131 */     } catch (Exception exception) {
/* 132 */       writeLog(exception);
/* 133 */       str = "";
/*     */     } 
/* 135 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public String dohtmlDateTimeCal(String paramString, int paramInt1, int paramInt2, int paramInt3) {
/* 141 */     String str = "";
/*     */     try {
/* 143 */       String str1 = "";
/* 144 */       String str2 = "";
/* 145 */       String str3 = "";
/* 146 */       String str4 = "";
/* 147 */       String str5 = "";
/* 148 */       String str6 = "";
/* 149 */       String str7 = "";
/* 150 */       int i = -1;
/* 151 */       byte b = 0;
/* 152 */       String str8 = "";
/* 153 */       double d = 0.0D;
/* 154 */       boolean bool1 = false;
/* 155 */       boolean bool2 = false;
/* 156 */       String str9 = paramString;
/* 157 */       int j = paramString.indexOf("$");
/* 158 */       while (j > -1) {
/* 159 */         String str10 = "";
/* 160 */         int k = paramString.indexOf("$", j + 1);
/* 161 */         String str11 = paramString.substring(j + 1, k);
/*     */         
/* 163 */         paramString = paramString.substring(k + 1);
/* 164 */         if ("datetime".equals(str11)) {
/*     */           
/* 166 */           bool1 = true;
/*     */         } else {
/* 168 */           int m = paramString.indexOf("$" + str11 + "$");
/* 169 */           String str12 = paramString.substring(0, m);
/*     */           
/* 171 */           if ("date".equalsIgnoreCase(str11)) {
/* 172 */             if ("".equals(str1)) {
/* 173 */               str1 = str12;
/* 174 */               b++;
/*     */             } else {
/* 176 */               str3 = str12;
/* 177 */               if (!bool1) {
/* 178 */                 b += 2;
/*     */               }
/*     */             } 
/* 181 */           } else if ("time".equalsIgnoreCase(str11)) {
/* 182 */             if ("".equals(str2)) {
/* 183 */               str2 = str12;
/*     */             } else {
/* 185 */               str4 = str12;
/* 186 */               if (bool1 == true) {
/* 187 */                 b += 2;
/*     */               }
/*     */             } 
/* 190 */           } else if ("input".equalsIgnoreCase(str11)) {
/* 191 */             i = Util.getIntValue(str12, 0);
/* 192 */             b += 5;
/* 193 */           } else if ("dateandtime".equals(str11)) {
/*     */             
/* 195 */             String str13 = Util.null2String((new BaseBean()).getPropValue("weaver_timezone_conversion", "timeZoneConversion")).trim();
/* 196 */             if ("1".equals(str13)) {
/* 197 */               DateTransformer dateTransformer = new DateTransformer();
/* 198 */               str12 = dateTransformer.getServerDateTime(str12);
/*     */             } 
/*     */             
/* 201 */             String[] arrayOfString = str12.split(" ");
/* 202 */             String str14 = arrayOfString[0].trim();
/* 203 */             String str15 = (arrayOfString.length > 1) ? arrayOfString[1].trim() : "";
/* 204 */             if ("".equals(str15))
/* 205 */               str15 = "00:00"; 
/* 206 */             if ("".equals(str1)) {
/* 207 */               str1 = str14;
/* 208 */               str2 = str15;
/* 209 */               b++;
/*     */             } else {
/* 211 */               str3 = str14;
/* 212 */               str4 = str15;
/* 213 */               if (bool1 == true) {
/* 214 */                 b += 2;
/*     */               }
/*     */             } 
/* 217 */             bool1 = true;
/*     */           } 
/*     */           try {
/* 220 */             if (b == 3 || b == 6 || b == 5) {
/* 221 */               if (b == 3) {
/* 222 */                 d = Util.getDoubleValue(getDateTimeCal(str1, str2, str3, str4, bool1, paramInt1, str7, paramInt2, paramInt3));
/* 223 */                 str = "" + d;
/* 224 */                 i = Util.getIntValue(str);
/* 225 */                 str1 = "";
/* 226 */                 str2 = "";
/* 227 */                 str3 = "";
/* 228 */                 str3 = "";
/* 229 */               } else if (b == 6) {
/* 230 */                 str8 = getDateTimeInputCal(str1, str2, i, bool1, paramInt1, str7);
/* 231 */                 str = str8;
/* 232 */               } else if (b == 5) {
/* 233 */                 if (this.trunToHour == 1) {
/* 234 */                   i *= 24;
/*     */                 }
/* 236 */                 if ("+".equalsIgnoreCase(str7)) {
/* 237 */                   d += i;
/*     */                 } else {
/* 239 */                   d -= i;
/*     */                 } 
/* 241 */                 str = "" + d;
/*     */               } 
/*     */               
/* 244 */               b = 0;
/*     */             } 
/* 246 */           } catch (Exception exception) {
/* 247 */             writeLog(exception);
/* 248 */             b = 0;
/*     */           } 
/* 250 */           int n = paramString.indexOf("$", m + str11.length() + 2);
/* 251 */           if (n > -1) {
/* 252 */             str10 = paramString.substring(m + str11.length() + 2, n);
/* 253 */             if (!"".equals(str10.trim())) {
/* 254 */               str7 = str10;
/*     */             }
/*     */           } 
/* 257 */           paramString = paramString.substring(m + str11.length() + 2);
/*     */         } 
/* 259 */         j = paramString.indexOf("$");
/*     */       } 
/* 261 */     } catch (Exception exception) {
/* 262 */       writeLog(exception);
/* 263 */       str = "";
/*     */     } 
/* 265 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getDateTimeCal(String paramString1, String paramString2, String paramString3, String paramString4, boolean paramBoolean, int paramInt, String paramString5) {
/* 275 */     String str = "";
/* 276 */     if ("".equals(paramString1) || "".equals(paramString3)) {
/* 277 */       return str;
/*     */     }
/*     */     try {
/* 280 */       if ("-".equals(paramString5)) {
/* 281 */         if ("".equals(paramString2) || "".equals(paramString4)) {
/* 282 */           paramBoolean = false;
/*     */         } else {
/* 284 */           paramString2 = (paramString2 + ":00").substring(0, 8);
/* 285 */           paramString4 = (paramString4 + ":00").substring(0, 8);
/*     */         } 
/* 287 */         if (paramInt == 0) {
/* 288 */           if (paramBoolean == true) {
/*     */             
/* 290 */             if (TimeUtil.timeInterval(paramString3 + " " + paramString4, paramString1 + " " + paramString2) >= 0L) {
/* 291 */               str = "" + (Util.getDoubleValue("" + TimeUtil.timeInterval(paramString3 + " " + paramString4, paramString1 + " " + paramString2)) / 3600.0D / 24.0D);
/*     */             } else {
/* 293 */               str = "" + (Util.getDoubleValue("" + TimeUtil.timeInterval(paramString3 + " " + paramString4, paramString1 + " " + paramString2)) / 3600.0D / 24.0D);
/*     */             }
/*     */           
/* 296 */           } else if (TimeUtil.dateInterval(paramString3, paramString1) >= 0L) {
/* 297 */             str = "" + (TimeUtil.dateInterval(paramString3, paramString1) + 1);
/*     */           } else {
/* 299 */             str = "" + TimeUtil.dateInterval(paramString3, paramString1);
/*     */           } 
/*     */           
/* 302 */           if (1 == this.trunToHour)
/* 303 */             str = "" + (Util.getDoubleValue(str) * 24.0D); 
/*     */         } else {
/* 305 */           HrmScheduleDiffUtil hrmScheduleDiffUtil = new HrmScheduleDiffUtil();
/* 306 */           hrmScheduleDiffUtil.setUser(this.user);
/* 307 */           ResourceComInfo resourceComInfo = new ResourceComInfo();
/* 308 */           DepartmentComInfo departmentComInfo = new DepartmentComInfo();
/* 309 */           int i = Util.getIntValue(resourceComInfo.getDepartmentID("" + this.user.getUID()));
/* 310 */           int j = Util.getIntValue(departmentComInfo.getSubcompanyid1("" + i));
/*     */           
/* 312 */           long l = 0L;
/* 313 */           if (paramBoolean == true) {
/* 314 */             l = TimeUtil.timeInterval(paramString3 + " " + paramString4, paramString1 + " " + paramString2);
/*     */           } else {
/* 316 */             l = TimeUtil.dateInterval(paramString3, paramString1);
/*     */           } 
/*     */           
/* 319 */           if (paramString4.length() == 8) {
/* 320 */             paramString4 = paramString4.substring(0, 5);
/*     */           }
/* 322 */           if (paramString2.length() == 8) {
/* 323 */             paramString2 = paramString2.substring(0, 5);
/*     */           }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */           
/* 330 */           if (l >= 0L) {
/* 331 */             String str1 = "";
/* 332 */             if (this.trunToHour == 1) {
/* 333 */               str1 = hrmScheduleDiffUtil.getTotalWorkingHours(paramString3, paramString4, paramString1, paramString2, j);
/*     */             } else {
/* 335 */               str1 = hrmScheduleDiffUtil.getTotalWorkingDays(paramString3, paramString4, paramString1, paramString2, j);
/*     */             } 
/* 337 */             if (!"".equals(str1)) {
/* 338 */               str1 = "" + Util.getFloatValue(str1);
/*     */             } else {
/* 340 */               str1 = "0";
/*     */             } 
/* 342 */             str = str1;
/*     */ 
/*     */ 
/*     */           
/*     */           }
/*     */           else {
/*     */ 
/*     */ 
/*     */             
/* 351 */             String str1 = "";
/* 352 */             if (this.trunToHour == 1) {
/* 353 */               str1 = hrmScheduleDiffUtil.getTotalWorkingHours(paramString1, paramString2, paramString3, paramString4, j);
/*     */             } else {
/* 355 */               str1 = hrmScheduleDiffUtil.getTotalWorkingDays(paramString1, paramString2, paramString3, paramString4, j);
/* 356 */             }  if (!"".equals(paramString4)) {
/* 357 */               str1 = "" + Util.getFloatValue(str1);
/*     */             } else {
/* 359 */               str1 = "" + (Util.getFloatValue(str1) - 1.0F);
/*     */             } 
/*     */             
/* 362 */             str = "-" + str1;
/*     */           } 
/*     */         } 
/*     */       } 
/*     */ 
/*     */ 
/*     */       
/* 369 */       if (!"".equals(str) && str.indexOf(".") > -1) {
/*     */         
/* 371 */         if (str.equals("6.944444444444445E-4")) {
/* 372 */           str = "0.001388888888888889";
/*     */         }
/* 374 */         str = str + "00";
/* 375 */         str = str.substring(0, str.indexOf(".") + 5);
/*     */       } 
/* 377 */     } catch (Exception exception) {
/* 378 */       writeLog(exception);
/*     */     } 
/* 380 */     return str;
/*     */   }
/*     */   
/*     */   public String getDateTimeCal(String paramString1, String paramString2, String paramString3, String paramString4, boolean paramBoolean, int paramInt1, String paramString5, int paramInt2, int paramInt3) {
/* 384 */     String str1 = "";
/* 385 */     String str2 = "";
/* 386 */     String str3 = "";
/* 387 */     String str4 = "";
/* 388 */     RecordSet recordSet = new RecordSet();
/* 389 */     if ("".equals(paramString1) || "".equals(paramString3)) {
/* 390 */       return str1;
/*     */     }
/*     */     try {
/* 393 */       if (paramInt3 == 1) {
/* 394 */         recordSet.execute("select fielddbtype,fieldhtmltype,type from workflow_billfield where id=" + paramInt2);
/* 395 */         if (recordSet.next()) {
/* 396 */           str2 = Util.null2String(recordSet.getString("fielddbtype"));
/* 397 */           str3 = Util.null2String(recordSet.getString("fieldhtmltype"));
/* 398 */           str4 = Util.null2String(recordSet.getString("type"));
/*     */         } 
/*     */       } 
/* 401 */       if (paramInt3 == 0) {
/* 402 */         recordSet.execute("select fielddbtype,fieldhtmltype,type from workflow_formdict where id=" + paramInt2);
/* 403 */         if (recordSet.next()) {
/* 404 */           str2 = Util.null2String(recordSet.getString("fielddbtype"));
/* 405 */           str3 = Util.null2String(recordSet.getString("fieldhtmltype"));
/* 406 */           str4 = Util.null2String(recordSet.getString("type"));
/*     */         } 
/* 408 */         if ("".equals(str2)) {
/* 409 */           recordSet.execute("select fielddbtype,fieldhtmltype,type from workflow_formdictdetail where id=" + paramInt2);
/* 410 */           if (recordSet.next()) {
/* 411 */             str2 = Util.null2String(recordSet.getString("fielddbtype"));
/* 412 */             str3 = Util.null2String(recordSet.getString("fieldhtmltype"));
/* 413 */             str4 = Util.null2String(recordSet.getString("type"));
/*     */           } 
/*     */         } 
/*     */       } 
/* 417 */       if (str2.indexOf("number(") > -1 || (str3.equals("1") && str4.equals("3")))
/*     */       {
/* 419 */         str2 = str2.substring(str2.indexOf(",") + 1, str2.indexOf(")"));
/*     */       }
/* 421 */       int i = Util.getIntValue(str2);
/* 422 */       if ("-".equals(paramString5)) {
/* 423 */         if ("".equals(paramString2) || "".equals(paramString4)) {
/* 424 */           paramBoolean = false;
/*     */         } else {
/* 426 */           paramString2 = (paramString2 + ":00").substring(0, 8);
/* 427 */           paramString4 = (paramString4 + ":00").substring(0, 8);
/*     */         } 
/* 429 */         if (paramInt1 == 0) {
/* 430 */           if (paramBoolean == true) {
/* 431 */             byte b = 4;
/*     */             
/* 433 */             if (TimeUtil.timeInterval(paramString3 + " " + paramString4, paramString1 + " " + paramString2) >= 0L) {
/* 434 */               str1 = "" + (Util.getDoubleValue("" + TimeUtil.timeInterval(paramString3 + " " + paramString4, paramString1 + " " + paramString2)) / 3600.0D / 24.0D);
/*     */             } else {
/* 436 */               str1 = "" + (Util.getDoubleValue("" + TimeUtil.timeInterval(paramString3 + " " + paramString4, paramString1 + " " + paramString2)) / 3600.0D / 24.0D);
/*     */             }
/*     */           
/* 439 */           } else if (TimeUtil.dateInterval(paramString3, paramString1) >= 0L) {
/* 440 */             str1 = "" + (TimeUtil.dateInterval(paramString3, paramString1) + 1);
/*     */           } else {
/* 442 */             str1 = "" + TimeUtil.dateInterval(paramString3, paramString1);
/*     */           } 
/*     */           
/* 445 */           if (this.trunToHour == 1)
/* 446 */             str1 = (Util.getDoubleValue(str1) * 24.0D) + ""; 
/*     */         } else {
/* 448 */           HrmScheduleDiffUtil hrmScheduleDiffUtil = new HrmScheduleDiffUtil();
/* 449 */           hrmScheduleDiffUtil.setUser(this.user);
/* 450 */           hrmScheduleDiffUtil.setFielddbtype2((i < 0) ? 1 : i);
/* 451 */           ResourceComInfo resourceComInfo = new ResourceComInfo();
/* 452 */           DepartmentComInfo departmentComInfo = new DepartmentComInfo();
/* 453 */           int j = Util.getIntValue(resourceComInfo.getDepartmentID("" + this.user.getUID()));
/* 454 */           int k = Util.getIntValue(departmentComInfo.getSubcompanyid1("" + j));
/*     */           
/* 456 */           long l = 0L;
/* 457 */           if (paramBoolean == true) {
/* 458 */             l = TimeUtil.timeInterval(paramString3 + " " + paramString4, paramString1 + " " + paramString2);
/*     */           } else {
/* 460 */             l = TimeUtil.dateInterval(paramString3, paramString1);
/*     */           } 
/*     */           
/* 463 */           if (paramString4.length() == 8) {
/* 464 */             paramString4 = paramString4.substring(0, 5);
/*     */           }
/* 466 */           if (paramString2.length() == 8) {
/* 467 */             paramString2 = paramString2.substring(0, 5);
/*     */           }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */           
/* 474 */           if (l >= 0L) {
/* 475 */             String str = "";
/* 476 */             if (this.trunToHour == 1) {
/* 477 */               str = hrmScheduleDiffUtil.getTotalWorkingHours(paramString3, paramString4, paramString1, paramString2, k);
/*     */             } else {
/* 479 */               str = hrmScheduleDiffUtil.getTotalWorkingDays(paramString3, paramString4, paramString1, paramString2, k);
/* 480 */             }  if (!"".equals(str)) {
/* 481 */               str = "" + Util.getFloatValue(str);
/*     */             } else {
/* 483 */               str = "0";
/*     */             } 
/* 485 */             str1 = str;
/*     */ 
/*     */ 
/*     */           
/*     */           }
/*     */           else {
/*     */ 
/*     */ 
/*     */             
/* 494 */             String str = "";
/* 495 */             if (this.trunToHour == 1) {
/* 496 */               str = hrmScheduleDiffUtil.getTotalWorkingHours(paramString1, paramString2, paramString3, paramString4, k);
/*     */             } else {
/* 498 */               str = hrmScheduleDiffUtil.getTotalWorkingDays(paramString1, paramString2, paramString3, paramString4, k);
/* 499 */             }  if (!"".equals(paramString4)) {
/* 500 */               str = "" + Util.getFloatValue(str);
/*     */             } else {
/* 502 */               str = "" + (Util.getFloatValue(str) - 1.0F);
/*     */             } 
/*     */             
/* 505 */             str1 = "-" + str;
/*     */           } 
/*     */         } 
/*     */       } 
/*     */ 
/*     */ 
/*     */       
/* 512 */       if (!"".equals(str1) && str1.indexOf(".") > -1) {
/*     */         
/* 514 */         if (str1.equals("6.944444444444445E-4")) {
/* 515 */           str1 = "0.001388888888888889";
/*     */         }
/* 517 */         str1 = str1 + "00";
/* 518 */         if (str1.indexOf("-") < 0) {
/* 519 */           str1 = Util.round(str1, i);
/*     */         } else {
/* 521 */           str1 = str1.substring(str1.indexOf("-") + 1, str1.length());
/* 522 */           str1 = Util.round(str1, i);
/* 523 */           str1 = "-" + str1;
/*     */         } 
/*     */       } 
/* 526 */     } catch (Exception exception) {
/* 527 */       writeLog(exception);
/*     */     } 
/* 529 */     return str1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getDateTimeInputCal(String paramString1, String paramString2, int paramInt1, boolean paramBoolean, int paramInt2, String paramString3) {
/* 539 */     String str = "";
/* 540 */     if ("".equals(paramString1)) {
/* 541 */       return str;
/*     */     }
/*     */     try {
/* 544 */       if (paramInt1 == 0 && paramBoolean == true) {
/* 545 */         str = paramString1 + " " + paramString2;
/* 546 */         return str;
/*     */       } 
/* 548 */       if ("-".equals(paramString3)) {
/* 549 */         paramInt1 *= -1;
/* 550 */         paramString3 = "+";
/*     */       } 
/* 552 */       if ((paramInt1 == 1 || paramInt1 == 0) && !paramBoolean) {
/* 553 */         str = paramString1;
/* 554 */         return str;
/*     */       } 
/* 556 */       if (paramInt1 >= 0) {
/* 557 */         paramInt1--;
/*     */       } else {
/* 559 */         paramInt1 = paramInt1;
/*     */       } 
/* 561 */       if ("".equals(paramString2)) {
/* 562 */         paramBoolean = false;
/*     */       }
/* 564 */       if (paramInt2 == 0) {
/* 565 */         if (!paramBoolean) {
/* 566 */           str = TimeUtil.dateAdd(paramString1, paramInt1);
/*     */         } else {
/* 568 */           str = TimeUtil.timeAdd(paramString1 + " " + (paramString2 + ":00").substring(0, 8), paramInt1 * 24 * 3600);
/*     */         } 
/*     */       } else {
/*     */         
/* 572 */         boolean bool = false;
/* 573 */         if ("-".equals(paramString3)) {
/* 574 */           bool = false;
/*     */         } else {
/* 576 */           bool = true;
/*     */         } 
/* 578 */         if (paramInt1 < 0) {
/* 579 */           paramInt1 *= -1;
/* 580 */           if (bool == true) {
/* 581 */             bool = false;
/*     */           } else {
/* 583 */             bool = true;
/*     */           } 
/*     */         } 
/*     */         
/* 587 */         str = getNextWorkDate(paramString1, paramInt1, this.user, bool);
/* 588 */         if (paramBoolean == true) {
/* 589 */           str = str + " " + paramString2;
/*     */         }
/*     */       } 
/* 592 */     } catch (Exception exception) {
/* 593 */       writeLog(exception);
/*     */     } 
/* 595 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getNextWorkDate(String paramString, int paramInt1, User paramUser, int paramInt2) {
/* 602 */     String str = "";
/*     */ 
/*     */ 
/*     */     
/*     */     try {
/* 607 */       HrmScheduleDiffUtil hrmScheduleDiffUtil = new HrmScheduleDiffUtil();
/* 608 */       hrmScheduleDiffUtil.setUser(paramUser);
/* 609 */       String str1 = paramString;
/* 610 */       byte b = 1;
/* 611 */       if (paramInt2 == 1) {
/* 612 */         b = 1;
/*     */       } else {
/* 614 */         b = -1;
/*     */       } 
/* 616 */       boolean bool = hrmScheduleDiffUtil.getIsWorkday(str1);
/* 617 */       if (!bool) {
/* 618 */         paramInt1++;
/*     */       }
/* 620 */       for (byte b1 = 0; b1 < paramInt1; ) {
/*     */         
/* 622 */         str1 = TimeUtil.dateAdd(str1, b);
/* 623 */         boolean bool1 = hrmScheduleDiffUtil.getIsWorkday(str1);
/* 624 */         if (bool1 == true) {
/* 625 */           str = str1;
/* 626 */           b1++;
/*     */         } 
/*     */       } 
/* 629 */     } catch (Exception exception) {
/* 630 */       writeLog(exception);
/*     */     } 
/*     */     
/* 633 */     return str;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/html/HtmlDateTimeCal.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */