/*      */ package weaver.workflow.exceldesign;
/*      */ import com.engine.workflow.biz.requestForm.CustomPageRangeCache;
/*      */ import java.io.File;
/*      */ import java.io.FileFilter;
/*      */ import java.io.UnsupportedEncodingException;
/*      */ import java.math.BigDecimal;
/*      */ import java.util.HashMap;
/*      */ import java.util.HashSet;
/*      */ import java.util.Iterator;
/*      */ import java.util.List;
/*      */ import java.util.Map;
/*      */ import java.util.regex.Matcher;
/*      */ import java.util.regex.Pattern;
/*      */ import org.bouncycastle.crypto.BlockCipher;
/*      */ import org.bouncycastle.crypto.CipherParameters;
/*      */ import org.bouncycastle.crypto.engines.AESFastEngine;
/*      */ import org.bouncycastle.crypto.modes.CBCBlockCipher;
/*      */ import org.bouncycastle.crypto.paddings.PaddedBufferedBlockCipher;
/*      */ import org.bouncycastle.crypto.params.KeyParameter;
/*      */ import org.bouncycastle.crypto.params.ParametersWithIV;
/*      */ import org.bouncycastle.util.encoders.Hex;
/*      */ import org.json.JSONArray;
/*      */ import org.json.JSONException;
/*      */ import org.json.JSONObject;
/*      */ import weaver.conn.RecordSet;
/*      */ import weaver.general.BaseBean;
/*      */ import weaver.general.GCONST;
/*      */ import weaver.general.ThreadVarLanguage;
/*      */ import weaver.general.Util;
/*      */ import weaver.mobile.webservices.workflow.WorkflowServiceUtil;
/*      */ import weaver.systeminfo.SystemEnv;
/*      */ import weaver.workflow.workflow.WFNodeDtlFieldManager;
/*      */ import weaver.workflow.workflow.WorkflowVersion;
/*      */ 
/*      */ public class ParseExcelLayout extends BaseBean {
/*      */   public static final String BEGMARK = "<seniordetailmark>";
/*      */   public static final String ENDMARK = "</seniordetailmark>";
/*      */   private int wfid;
/*      */   private int nodeid;
/*      */   private int formid;
/*      */   private int isbill;
/*      */   private int modeid;
/*   43 */   private int type = -1;
/*   44 */   private int requestid = -1;
/*      */   private int languageid;
/*   46 */   private String isvalid = "1";
/*      */   private boolean fullparse = true;
/*   48 */   private String detailheadrowmark = "exceldetailtitle";
/*   49 */   private String changemode = "";
/*      */   
/*      */   private int ispdf;
/*   52 */   private JSONObject etable = new JSONObject();
/*   53 */   private HashMap<String, String> formulaMap = new HashMap<>();
/*   54 */   private StringBuilder tempHtml = new StringBuilder();
/*   55 */   private StringBuilder tempCss = new StringBuilder();
/*   56 */   private StringBuilder tempScript = new StringBuilder();
/*      */   
/*   58 */   private int tabAreaLength = 0;
/*   59 */   private ExcelLayoutManager excelLayoutManager = new ExcelLayoutManager();
/*      */   
/*   61 */   private Map<String, Object> generateWfInfo = new HashMap<>();
/*      */   
/*      */   public ParseExcelLayout(HashMap<String, String> paramHashMap) {
/*   64 */     this.wfid = Util.getIntValue(paramHashMap.get("wfid"), 0);
/*   65 */     this.nodeid = Util.getIntValue(paramHashMap.get("nodeid"), 0);
/*   66 */     this.formid = Util.getIntValue(paramHashMap.get("formid"), 0);
/*   67 */     this.isbill = Util.getIntValue(paramHashMap.get("isbill"), -1);
/*   68 */     this.modeid = Util.getIntValue(paramHashMap.get("modeid"), -1);
/*   69 */     this.type = Util.getIntValue(paramHashMap.get("type"), 0);
/*   70 */     this.requestid = Util.getIntValue(paramHashMap.get("requestid"));
/*   71 */     this.languageid = Util.getIntValue(paramHashMap.get("languageid"), 7);
/*   72 */     this.changemode = Util.null2String(paramHashMap.get("changemode"));
/*   73 */     this.ispdf = Util.getIntValue(paramHashMap.get("ispdf"), 0);
/*      */   }
/*      */   
/*      */   public void analyzeLayout(String paramString1, String paramString2) {
/*      */     try {
/*   78 */       RecordSet recordSet = new RecordSet();
/*   79 */       recordSet.executeSql("SELECT isvalid FROM  workflow_base WHERE id=" + this.wfid);
/*   80 */       if (recordSet.next()) {
/*   81 */         this.isvalid = Util.null2String(recordSet.getString("isvalid"));
/*      */       }
/*   83 */       JSONObject jSONObject1 = new JSONObject(paramString1);
/*   84 */       JSONObject jSONObject2 = jSONObject1.getJSONObject("eformdesign");
/*   85 */       this.etable = jSONObject2.getJSONObject("etables");
/*   86 */       if (this.fullparse && this.ispdf == 0) {
/*      */         
/*   88 */         this.tempHtml.append("<p id=\"edesign_script_css\" style=\"display:none !important\">\n");
/*   89 */         if (this.type == 0 || this.type == 1 || this.type == 2) {
/*   90 */           this.tempHtml.append("<script type=\"text/javascript\" src=\"/workflow/exceldesign/js/format_wev8.js\"></script>\n");
/*   91 */           this.tempHtml.append("<script type=\"text/javascript\" src=\"/workflow/exceldesign/js/formula_wev8.js\"></script>\n");
/*   92 */           importDefFormula();
/*      */         } 
/*   94 */         this.tempHtml.append("<script type=\"text/javascript\" src=\"/workflow/exceldesign/js/tabpage_wev8.js\"></script>\n");
/*   95 */         this.tempHtml.append("<script type=\"text/javascript\" src=\"/workflow/exceldesign/js/wfExcelHtml_wev8.js\"></script>\n");
/*   96 */         this.tempHtml.append("<script type=\"text/javascript\" src=\"/workflow/exceldesign/js/customPublicFun_wev8.js\"></script>\n");
/*   97 */         this.tempHtml.append("</p>\n");
/*      */         
/*   99 */         if (jSONObject2.has("formula") && (this.type == 0 || this.type == 2)) {
/*  100 */           JSONObject jSONObject = jSONObject2.getJSONObject("formula");
/*  101 */           String str = jSONObject.toString();
/*  102 */           if (!"".equals(str)) {
/*  103 */             this.formulaMap = transFormulaJsonToMap(jSONObject);
/*      */             
/*  105 */             addWindowListener(jSONObject);
/*      */             
/*  107 */             str = str.replace("'", "\\'").replace("\\\"", "\\\\\"");
/*  108 */             this.tempScript.append("try{\n");
/*  109 */             this.tempScript.append("\t var globalFormula = '" + str + "';\n");
/*  110 */             this.tempScript.append("\t globalFormula = JSON.parse(globalFormula);\n");
/*  111 */             this.tempScript.append("}catch(e){}\n");
/*      */           } 
/*      */         } else {
/*  114 */           addDefaultWindowListener();
/*      */         } 
/*      */       } 
/*      */       
/*  118 */       analyzeMainTable(paramString2);
/*  119 */       if (this.fullparse) {
/*      */         
/*  121 */         if (this.requestid > 0) {
/*      */           try {
/*  123 */             ParseLinkHideAttr parseLinkHideAttr = new ParseLinkHideAttr(this.wfid, this.nodeid, this.formid, this.isbill, this.requestid);
/*  124 */             this.tempHtml = parseLinkHideAttr.adjustTemplateHide(this.tempHtml);
/*  125 */           } catch (Exception exception) {
/*  126 */             exception.printStackTrace();
/*      */           } 
/*      */         }
/*      */         
/*  130 */         this.tempHtml.append("<input type=\"hidden\" id=\"edesign_layout\" />\n");
/*  131 */         if (this.ispdf != 1) {
/*      */           
/*  133 */           this.tempScript.append("jQuery(document).ready(function(){").append("\n");
/*  134 */           this.tempScript.append("\t readyOperate.execute(" + this.type + "); \n");
/*  135 */           this.tempScript.append("});\n");
/*      */         } 
/*      */       } 
/*  138 */     } catch (Exception exception) {
/*  139 */       writeLog(exception);
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private void addWindowListener(JSONObject paramJSONObject) {
/*  148 */     Iterator<E> iterator = paramJSONObject.keys();
/*  149 */     StringBuffer stringBuffer1 = new StringBuffer();
/*  150 */     StringBuffer stringBuffer2 = new StringBuffer();
/*  151 */     StringBuffer stringBuffer3 = new StringBuffer();
/*  152 */     StringBuffer stringBuffer4 = new StringBuffer();
/*  153 */     StringBuffer stringBuffer5 = new StringBuffer();
/*  154 */     StringBuffer stringBuffer6 = new StringBuffer();
/*  155 */     StringBuffer stringBuffer7 = new StringBuffer();
/*  156 */     StringBuffer stringBuffer8 = new StringBuffer();
/*  157 */     StringBuffer stringBuffer9 = new StringBuffer();
/*  158 */     StringBuffer stringBuffer10 = new StringBuffer();
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  163 */     while (iterator.hasNext()) {
/*      */       try {
/*  165 */         String str = iterator.next().toString();
/*  166 */         JSONObject jSONObject = paramJSONObject.getJSONObject(str);
/*  167 */         if (jSONObject.has("triggers")) {
/*  168 */           String str1 = Util.null2String(jSONObject.get("triggers"));
/*      */           
/*  170 */           if (str1 != null) {
/*  171 */             String[] arrayOfString = str1.split("#_#");
/*  172 */             if (arrayOfString != null && arrayOfString.length > 0) {
/*  173 */               String str2 = arrayOfString[0];
/*  174 */               if (str2 != null) {
/*  175 */                 String[] arrayOfString1 = str2.split("##");
/*  176 */                 if (arrayOfString1 != null && arrayOfString1.length > 0) {
/*  177 */                   for (byte b = 0; b < arrayOfString1.length; b++) {
/*  178 */                     str2 = arrayOfString1[b];
/*      */                     
/*  180 */                     if ("21".equals(str2)) {
/*  181 */                       stringBuffer1.append("\t formulaTriggerByJson(" + jSONObject + ");\n");
/*      */                     
/*      */                     }
/*  184 */                     else if ("22".equals(str2)) {
/*  185 */                       stringBuffer2.append("\t formulaTriggerByJson(" + jSONObject + ");\n");
/*      */                     
/*      */                     }
/*  188 */                     else if ("23".equals(str2)) {
/*  189 */                       stringBuffer3.append("\t formulaTriggerByJson(" + jSONObject + ");\n");
/*      */                     } 
/*      */                     
/*  192 */                     if ("51".equals(str2)) {
/*  193 */                       stringBuffer4.append("\t var returnMessage = formulaTriggerByJson(" + jSONObject + ");\n");
/*      */                     }
/*      */                     
/*  196 */                     if ("52".equals(str2)) {
/*  197 */                       stringBuffer5.append("\t var returnMessage = formulaTriggerByJson(" + jSONObject + ");\n");
/*      */                     }
/*      */                     
/*  200 */                     if ("53".equals(str2)) {
/*  201 */                       stringBuffer6.append("\t var returnMessage = formulaTriggerByJson(" + jSONObject + ");\n");
/*      */                     }
/*      */                     
/*  204 */                     if ("54".equals(str2)) {
/*  205 */                       stringBuffer10.append("\t var returnMessage = formulaTriggerByJson(" + jSONObject + ");\n");
/*      */                     }
/*      */                     
/*  208 */                     if ("55".equals(str2)) {
/*  209 */                       stringBuffer8.append("\t var returnMessage = formulaTriggerByJson(" + jSONObject + ");\n");
/*      */                     }
/*      */                     
/*  212 */                     if ("56".equals(str2)) {
/*  213 */                       stringBuffer7.append("\t var returnMessage = formulaTriggerByJson(" + jSONObject + ");\n");
/*      */                     }
/*      */                     
/*  216 */                     if ("57".equals(str2)) {
/*  217 */                       stringBuffer9.append("\t var returnMessage = formulaTriggerByJson(" + jSONObject + ");\n");
/*      */                     }
/*      */                   } 
/*      */                 }
/*      */               } 
/*      */             } 
/*      */           } 
/*      */         } 
/*  225 */       } catch (Exception exception) {
/*  226 */         writeLog(exception);
/*      */       } 
/*      */     } 
/*      */     
/*  230 */     if (stringBuffer1.length() > 0) {
/*  231 */       this.tempScript.append("window.addEventListener('load',function(){").append("\n");
/*  232 */       this.tempScript.append(stringBuffer1);
/*  233 */       this.tempScript.append("});\n");
/*      */     } 
/*      */     
/*  236 */     if (stringBuffer2.length() > 0) {
/*  237 */       this.tempScript.append("window.addEventListener('mousewheel',function(){").append("\n");
/*  238 */       this.tempScript.append(stringBuffer2);
/*  239 */       this.tempScript.append("});\n");
/*      */     } 
/*      */     
/*  242 */     if (stringBuffer2.length() > 0) {
/*  243 */       this.tempScript.append("window.addEventListener('beforeunload',function(){").append("\n");
/*  244 */       this.tempScript.append(stringBuffer3);
/*  245 */       this.tempScript.append("});\n");
/*      */     } 
/*      */     
/*  248 */     this.tempScript.append("var runSubmitFormula = function(){").append("\n");
/*  249 */     if (stringBuffer4.length() > 0) {
/*  250 */       this.tempScript.append(stringBuffer4);
/*  251 */       this.tempScript.append("return returnMessage;");
/*      */     } 
/*  253 */     this.tempScript.append("};\n");
/*      */     
/*  255 */     this.tempScript.append("var runSaveFormula = function(){").append("\n");
/*  256 */     if (stringBuffer5.length() > 0) {
/*  257 */       this.tempScript.append(stringBuffer5);
/*  258 */       this.tempScript.append("return returnMessage;");
/*      */     } 
/*  260 */     this.tempScript.append("};\n");
/*      */     
/*  262 */     this.tempScript.append("var runRejectFormula = function(){").append("\n");
/*  263 */     if (stringBuffer6.length() > 0) {
/*  264 */       this.tempScript.append(stringBuffer6);
/*  265 */       this.tempScript.append("return returnMessage;");
/*      */     } 
/*  267 */     this.tempScript.append("};\n");
/*      */     
/*  269 */     this.tempScript.append("var runDoReview3Formula = function(){").append("\n");
/*  270 */     if (stringBuffer10.length() > 0) {
/*  271 */       this.tempScript.append(stringBuffer10);
/*  272 */       this.tempScript.append("return returnMessage;");
/*      */     } 
/*  274 */     this.tempScript.append("};\n");
/*      */     
/*  276 */     this.tempScript.append("var runSubmitDirectFormula = function(){").append("\n");
/*  277 */     if (stringBuffer8.length() > 0) {
/*  278 */       this.tempScript.append(stringBuffer8);
/*  279 */       this.tempScript.append("return returnMessage;");
/*      */     } 
/*  281 */     this.tempScript.append("};\n");
/*      */     
/*  283 */     this.tempScript.append("var runDoReviewFormula = function(){").append("\n");
/*  284 */     if (stringBuffer7.length() > 0) {
/*  285 */       this.tempScript.append(stringBuffer7);
/*  286 */       this.tempScript.append("return returnMessage;");
/*      */     } 
/*  288 */     this.tempScript.append("};\n");
/*      */     
/*  290 */     this.tempScript.append("var runDoReview2Formula = function(){").append("\n");
/*  291 */     if (stringBuffer9.length() > 0) {
/*  292 */       this.tempScript.append(stringBuffer9);
/*  293 */       this.tempScript.append("return returnMessage;");
/*      */     } 
/*  295 */     this.tempScript.append("};\n");
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private void addDefaultWindowListener() {
/*  305 */     this.tempScript.append("var runSubmitFormula = function(){").append("\n");
/*  306 */     this.tempScript.append("};\n");
/*      */     
/*  308 */     this.tempScript.append("var runSaveFormula = function(){").append("\n");
/*  309 */     this.tempScript.append("};\n");
/*      */     
/*  311 */     this.tempScript.append("var runRejectFormula = function(){").append("\n");
/*  312 */     this.tempScript.append("};\n");
/*      */     
/*  314 */     this.tempScript.append("var runDoReview3Formula = function(){").append("\n");
/*  315 */     this.tempScript.append("};\n");
/*      */     
/*  317 */     this.tempScript.append("var runSubmitDirectFormula = function(){").append("\n");
/*  318 */     this.tempScript.append("};\n");
/*      */     
/*  320 */     this.tempScript.append("var runDoReviewFormula = function(){").append("\n");
/*  321 */     this.tempScript.append("};\n");
/*      */     
/*  323 */     this.tempScript.append("var runDoReview2Formula = function(){").append("\n");
/*  324 */     this.tempScript.append("};\n");
/*      */   }
/*      */   
/*      */   private void importDefFormula() {
/*  328 */     this.tempHtml.append("<script type=\"text/javascript\" src=\"/workflow/exceldesign/js/formula/system_formula.js\"></script>\n");
/*  329 */     this.tempHtml.append("<script type=\"text/javascript\" src=\"/workflow/exceldesign/js/formula/db_formula_wev8.js\"></script>\n");
/*  330 */     File file = new File(GCONST.getRootPath() + "/workflow/exceldesign/js/formula/def");
/*  331 */     File[] arrayOfFile = file.listFiles(new FileFilter() {
/*      */           public boolean accept(File param1File) {
/*  333 */             String str = param1File.getName();
/*  334 */             if (str.endsWith(".js")) {
/*  335 */               return true;
/*      */             }
/*  337 */             return false;
/*      */           }
/*      */         });
/*      */     
/*  341 */     for (byte b = 0; arrayOfFile != null && b < arrayOfFile.length; b++) {
/*  342 */       this.tempHtml.append("<script type=\"text/javascript\" src=\"/workflow/exceldesign/js/formula/def/" + arrayOfFile[b].getName() + "\"></script>\n");
/*      */     }
/*      */   }
/*      */   
/*      */   private void analyzeMainTable(String paramString) throws Exception {
/*  347 */     boolean bool = WorkflowServiceUtil.isMobileParseExcelMode(this.wfid + "", this.nodeid + "", this.changemode);
/*  348 */     JSONObject jSONObject1 = this.etable.getJSONObject("emaintable");
/*  349 */     JSONObject jSONObject2 = jSONObject1.getJSONObject("rowheads");
/*  350 */     JSONObject jSONObject3 = jSONObject1.getJSONObject("colheads");
/*  351 */     JSONObject jSONObject4 = new JSONObject();
/*  352 */     if (jSONObject1.has("rowattrs"))
/*  353 */       jSONObject4 = jSONObject1.getJSONObject("rowattrs"); 
/*  354 */     boolean bool1 = (jSONObject3.toString().indexOf("%") > -1) ? true : false;
/*  355 */     int i = jSONObject2.length();
/*  356 */     int j = jSONObject3.length();
/*  357 */     if (this.type == 0 || this.type == 2) {
/*  358 */       this.tempCss.append(".excelOuterTable .td_etype_3 a{color:#123885 !important;}").append("\n");
/*  359 */       this.tempCss.append(".excelOuterTable .td_etype_3 a:hover{color:red !important;}").append("\n");
/*      */     } 
/*  361 */     this.tempHtml.append("<div class=\"excelTempDiv\">").append("\n");
/*  362 */     this.tempHtml.append("<table class=\"excelOuterTable tablefixed\">").append("\n");
/*  363 */     this.tempHtml.append("<tbody>").append("\n")
/*  364 */       .append("<tr>").append("\n")
/*  365 */       .append("<td align=\"center\">").append("\n");
/*  366 */     this.tempHtml.append("<table class=\"excelMainTable tablefixed\" style=\" ");
/*  367 */     if (this.ispdf == 1) {
/*  368 */       this.tempHtml.append(parseTableBkImage("main", jSONObject1, j));
/*  369 */       this.tempHtml.append("width:100%; ");
/*  370 */       this.tempHtml.append("\" >").append("\n");
/*      */     } else {
/*  372 */       if (this.type == 0 || this.type == 1) {
/*  373 */         if (bool1) {
/*  374 */           this.tempHtml.append("width:" + ((this.type == 0) ? 95 : 100) + "%; ");
/*      */         } else {
/*  376 */           int k = 0;
/*  377 */           for (byte b1 = 0; b1 < jSONObject3.length(); b1++) {
/*  378 */             k += Util.getIntValue(jSONObject3.get("col_" + b1).toString(), 0);
/*      */           }
/*  380 */           this.tempHtml.append("width:").append(k).append("px; ");
/*      */         } 
/*  382 */       } else if (this.type == 2) {
/*  383 */         this.tempHtml.append("width:98%; ");
/*      */       } 
/*  385 */       this.tempHtml.append("\" ");
/*  386 */       this.tempHtml.append("_hasPercent=\"").append(bool1).append("\" ");
/*  387 */       this.tempHtml.append(">").append("\n");
/*      */     } 
/*      */ 
/*      */     
/*  391 */     boolean bool2 = false;
/*  392 */     if ((this.type == 1 && bool1) || this.type == 2 || this.ispdf == 1)
/*  393 */       bool2 = true; 
/*  394 */     this.tempHtml.append("<tbody>").append("\n");
/*      */     
/*  396 */     this.tempHtml.append(parseMainTableWidth(jSONObject3, bool2));
/*      */     
/*  398 */     this.tempHtml.append(parseTableImage("main", jSONObject1, j));
/*      */     
/*  400 */     JSONArray jSONArray = jSONObject1.getJSONArray("ec");
/*  401 */     String[][] arrayOfString = buildMainCellArray("main", jSONArray, jSONObject2, jSONObject3, jSONObject4);
/*      */     
/*  403 */     String str = "";
/*  404 */     for (byte b = 0; b < i; b++) {
/*  405 */       String str1 = "", str2 = "", str3 = "";
/*  406 */       if (jSONObject4.has("row_" + b)) {
/*  407 */         Map<String, String> map = parseAttrs(jSONObject4.getJSONObject("row_" + b));
/*  408 */         str1 = map.get("_text");
/*  409 */         str2 = map.get("_class");
/*  410 */         str3 = map.get("_style");
/*      */       } 
/*  412 */       this.tempHtml.append("<tr style=\"height:").append(jSONObject2.get("row_" + b)).append("px;").append(str3).append("\" ");
/*  413 */       this.tempHtml.append("".equals(str2) ? "" : ("class=\"" + str2 + "\" ")).append(str1).append(">\n");
/*  414 */       for (byte b1 = 0; b1 < j; b1++) {
/*  415 */         str = Util.null2String(arrayOfString[b][b1]);
/*  416 */         if (!"combine_cell".equals(str))
/*      */         {
/*  418 */           if ("".equals(str))
/*  419 */           { if (this.ispdf == 1) {
/*  420 */               this.tempHtml.append("<td style=\"height:").append(jSONObject2.get("row_" + b)).append("px;\"></td>");
/*      */             } else {
/*  422 */               this.tempHtml.append("<td></td>");
/*      */             }  }
/*  424 */           else { this.tempHtml.append(str); }
/*      */            } 
/*      */       } 
/*  427 */       this.tempHtml.append("</tr>").append("\n");
/*      */     } 
/*  429 */     this.tempHtml.append("</tbody>\n").append("</table>\n");
/*  430 */     this.tempHtml.append("</td>\n").append("</tr>\n").append("</tbody>\n").append("</table>\n");
/*      */     
/*  432 */     if (!"".equals(paramString) && this.ispdf == 0 && 
/*  433 */       !"".equals(paramString)) {
/*  434 */       String str1 = decodeStr(paramString);
/*  435 */       if ("1".equals(this.changemode))
/*      */       {
/*  437 */         str1 = str1.replaceAll("<(script|Script|SCRIPT)", "<div style=\"display:none\"><_script").replaceAll("</(script|Script|SCRIPT)", "</_script");
/*      */       }
/*  439 */       this.tempHtml.append(str1).append("\n");
/*      */     } 
/*      */ 
/*      */     
/*  443 */     this.tempHtml.append("</div>\n");
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private String analyzeTabArea(JSONObject paramJSONObject) throws Exception {
/*  450 */     int i = -1;
/*  451 */     int j = 0;
/*  452 */     String str1 = "";
/*  453 */     byte b1 = 0;
/*  454 */     Iterator<E> iterator = paramJSONObject.keys();
/*  455 */     while (iterator.hasNext()) {
/*  456 */       String str3 = iterator.next().toString();
/*  457 */       String str4 = Util.null2String(paramJSONObject.get(str3));
/*  458 */       if ("style".equals(str3)) {
/*  459 */         i = Util.getIntValue(str4, -1); continue;
/*  460 */       }  if ("defshow".equals(str3)) {
/*  461 */         j = Util.getIntValue(str4, 0); continue;
/*  462 */       }  if ("areaheight".equals(str3)) {
/*  463 */         str1 = str4; continue;
/*  464 */       }  if (str3.startsWith("order_")) {
/*  465 */         b1++;
/*      */       }
/*      */     } 
/*  468 */     if (i > 0) {
/*  469 */       RecordSet recordSet = new RecordSet();
/*  470 */       recordSet.executeSql("select * from workflow_tabstyle where styleid=" + i);
/*  471 */       if (!recordSet.next())
/*  472 */         i = -1; 
/*      */     } 
/*  474 */     StringBuilder stringBuilder1 = new StringBuilder();
/*  475 */     StringBuilder stringBuilder2 = new StringBuilder();
/*      */     
/*  477 */     if (this.ispdf == 1) {
/*  478 */       stringBuilder1.append("<div class=\"tab_movebtn tab_turnleft\" style=\"display: none;\"></div>").append("\n");
/*  479 */       stringBuilder1.append("<div class=\"tab_head\" style=\"width: 100%; font-family:Microsoft YaHei;\">\n");
/*  480 */       stringBuilder1.append("<div class=\"t_area xrepeat\"  style=\"tab_4_image_bg\" tabbkpng_attr=\"1\"> ");
/*      */     } else {
/*  482 */       if (this.type != 2)
/*  483 */         stringBuilder1.append("<div class=\"tab_movebtn tab_turnleft\"></div>").append("\n"); 
/*  484 */       stringBuilder1.append("<div class=\"tab_head\"");
/*  485 */       if (this.type == 2)
/*  486 */         stringBuilder1.append(" style=\"overflow-x:auto\""); 
/*  487 */       stringBuilder1.append(">").append("\n");
/*  488 */       stringBuilder1.append("<div class=\"t_area xrepeat\"> ");
/*      */     } 
/*  490 */     for (byte b2 = 1; b2 <= b1; b2++) {
/*  491 */       String str3 = Util.null2String(paramJSONObject.get("order_" + b2));
/*  492 */       String str4 = str3.substring(0, str3.indexOf(","));
/*  493 */       String str5 = str3.substring(str3.indexOf(",") + 1);
/*      */       
/*  495 */       stringBuilder1.append("<div id=\"" + str4 + "\" ");
/*  496 */       String str6 = (b2 - 1 == j) ? "t_sel" : "t_unsel";
/*  497 */       stringBuilder1.append("class=\"" + str6 + "\">");
/*  498 */       if (this.ispdf == 1) {
/*  499 */         String str = str6.replace("t_", "");
/*  500 */         stringBuilder1.append("<div class=\"" + str6 + "_left norepeat\" style=\"tab_" + str + "_bgleft\" tabbkpng_attr=\"1\"></div>");
/*  501 */         stringBuilder1.append("<div class=\"" + str6 + "_middle xrepeat lineheight30\" style=\"tab_" + str + "_bgmiddle\" tabbkpng_attr=\"1\">");
/*  502 */         stringBuilder1.append("<span>").append(str5).append("</span>");
/*  503 */         stringBuilder1.append("</div>");
/*  504 */         stringBuilder1.append("<div class=\"" + str6 + "_right norepeat\" style=\"tab_" + str + "_bgright\" tabbkpng_attr=\"1\"></div>");
/*  505 */         stringBuilder1.append("</div>");
/*  506 */         if (b2 != b1)
/*  507 */           stringBuilder1.append("<div class=\"t_sep norepeat\" style=\"width: 1px;\"></div>"); 
/*      */       } else {
/*  509 */         stringBuilder1.append("<div class=\"" + str6 + "_left norepeat\"></div>");
/*  510 */         stringBuilder1.append("<div class=\"" + str6 + "_middle xrepeat lineheight30\">");
/*  511 */         stringBuilder1.append("<span>").append(str5).append("</span>");
/*  512 */         stringBuilder1.append("</div>");
/*  513 */         stringBuilder1.append("<div class=\"" + str6 + "_right norepeat\"></div>");
/*  514 */         stringBuilder1.append("</div>");
/*  515 */         if (b2 != b1) {
/*  516 */           stringBuilder1.append("<div class=\"t_sep norepeat\"></div>");
/*      */         }
/*      */       } 
/*  519 */       if (this.etable.has(str4)) {
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */         
/*  525 */         stringBuilder2.append("<div class=\"tab_content\" id=\"" + str4 + "_content\" ");
/*  526 */         if (b2 - 1 != j)
/*  527 */           stringBuilder2.append(" style=\"display:none\" "); 
/*  528 */         stringBuilder2.append(">");
/*  529 */         stringBuilder2.append(analyzeTab(str4));
/*  530 */         stringBuilder2.append("</div>");
/*      */       } 
/*      */     } 
/*      */     
/*  534 */     stringBuilder1.append("</div>");
/*  535 */     stringBuilder1.append("</div>");
/*  536 */     if (this.ispdf == 1)
/*  537 */       stringBuilder1.append("<div class=\"tab_movebtn tab_turnright\" style=\"display: none;\"></div>").append("\n"); 
/*  538 */     if (this.type != 2 && this.ispdf == 0)
/*  539 */       stringBuilder1.append("<div class=\"tab_movebtn tab_turnright\"></div>").append("\n"); 
/*  540 */     String str2 = "";
/*  541 */     if (this.ispdf == 0) {
/*  542 */       str2 = "tabarea_" + this.tabAreaLength;
/*      */     }
/*  544 */     this.tabAreaLength++;
/*  545 */     if (this.ispdf == 1) {
/*  546 */       str2 = "tab_area tabarea_" + this.tabAreaLength + "_0";
/*      */     }
/*  548 */     StringBuilder stringBuilder3 = new StringBuilder();
/*  549 */     stringBuilder3.append("<div class=\"").append(str2).append("\">");
/*  550 */     stringBuilder3.append("<div class=\"tab_top\">").append(stringBuilder1).append("</div>");
/*  551 */     if (this.ispdf == 1) {
/*  552 */       stringBuilder3.append("<div class=\"tab_bottom\" >");
/*  553 */       stringBuilder3.append(stringBuilder2);
/*  554 */       stringBuilder3.append("</div>");
/*  555 */       stringBuilder3.append("</div>");
/*      */     } else {
/*  557 */       stringBuilder3.append("<div class=\"tab_bottom\" style=\"");
/*  558 */       if (str1.startsWith("2,")) {
/*  559 */         String str = str1.substring(str1.indexOf(",") + 1);
/*  560 */         stringBuilder3.append("height:").append(str).append("px; overflow-y:auto;");
/*      */       } 
/*  562 */       stringBuilder3.append("\" >").append(stringBuilder2).append("</div>");
/*  563 */       stringBuilder3.append("</div>");
/*      */     } 
/*      */     
/*  566 */     if (this.ispdf == 1) str2 = "tabarea_" + this.tabAreaLength + "_0";
/*      */     
/*  568 */     this.tempScript.append("jQuery(document).ready(function(){").append("\n");
/*  569 */     if (i < 0) {
/*  570 */       this.tempScript.append("\t tabPage.front_initEvent_sysFace('" + str2 + "', '" + i + "'); ").append("\n");
/*      */     } else {
/*  572 */       JSONObject jSONObject = getCustomStyle(i);
/*  573 */       this.tempScript.append("\t tabPage.front_initEvent_cusFace('" + str2 + "', '" + jSONObject.toString() + "'); ").append("\n");
/*      */     } 
/*  575 */     this.tempScript.append("});\n");
/*      */ 
/*      */     
/*  578 */     return stringBuilder3.toString();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private String analyzeTab(String paramString) throws Exception {
/*  585 */     JSONObject jSONObject1 = this.etable.getJSONObject(paramString);
/*  586 */     JSONObject jSONObject2 = jSONObject1.getJSONObject("rowheads");
/*  587 */     JSONObject jSONObject3 = jSONObject1.getJSONObject("colheads");
/*  588 */     JSONObject jSONObject4 = new JSONObject();
/*  589 */     if (jSONObject1.has("rowattrs"))
/*  590 */       jSONObject4 = jSONObject1.getJSONObject("rowattrs"); 
/*  591 */     int i = jSONObject2.length();
/*  592 */     int j = jSONObject3.length();
/*      */     
/*  594 */     StringBuilder stringBuilder = new StringBuilder();
/*  595 */     stringBuilder.append("<table class=\"excelTabTable tablefixed\"");
/*  596 */     if (this.ispdf == 1) {
/*  597 */       stringBuilder.append(" style=\"").append(parseTableBkImage(paramString, jSONObject1, j)).append("\"");
/*      */     }
/*  599 */     stringBuilder.append(">").append("\n");
/*  600 */     stringBuilder.append("<tbody>").append("\n");
/*      */     
/*  602 */     stringBuilder.append(parseMainTableWidth(jSONObject3, true));
/*      */     
/*  604 */     stringBuilder.append(parseTableImage(paramString, jSONObject1, j));
/*      */     
/*  606 */     JSONArray jSONArray = jSONObject1.getJSONArray("ec");
/*  607 */     String[][] arrayOfString = buildMainCellArray(paramString, jSONArray, jSONObject2, jSONObject3, jSONObject4);
/*      */     
/*  609 */     String str = "";
/*  610 */     for (byte b = 0; b < i; b++) {
/*  611 */       String str1 = "", str2 = "", str3 = "";
/*  612 */       if (jSONObject4.has("row_" + b)) {
/*  613 */         Map<String, String> map = parseAttrs(jSONObject4.getJSONObject("row_" + b));
/*  614 */         str1 = map.get("_text");
/*  615 */         str2 = map.get("_class");
/*  616 */         str3 = map.get("_style");
/*      */       } 
/*  618 */       stringBuilder.append("<tr style=\"height:").append(jSONObject2.get("row_" + b)).append("px;").append(str3).append("\" ");
/*  619 */       stringBuilder.append("".equals(str2) ? "" : ("class=\"" + str2 + "\" ")).append(str1).append(">\n");
/*  620 */       for (byte b1 = 0; b1 < j; b1++) {
/*  621 */         str = Util.null2String(arrayOfString[b][b1]);
/*  622 */         if (!"combine_cell".equals(str))
/*      */         {
/*  624 */           if ("".equals(str))
/*  625 */           { if (this.ispdf == 1) {
/*  626 */               stringBuilder.append("<td style=\"height:").append(jSONObject2.get("row_" + b)).append("px;\"></td>");
/*      */             } else {
/*  628 */               stringBuilder.append("<td></td>");
/*      */             }  }
/*  630 */           else { stringBuilder.append(str); }
/*      */            } 
/*      */       } 
/*  633 */       stringBuilder.append("</tr>").append("\n");
/*      */     } 
/*  635 */     stringBuilder.append("</tbody>\n").append("</table>\n");
/*  636 */     return stringBuilder.toString();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private String[][] buildMainCellArray(String paramString, JSONArray paramJSONArray, JSONObject paramJSONObject1, JSONObject paramJSONObject2, JSONObject paramJSONObject3) throws Exception {
/*  644 */     int i = paramJSONObject1.length();
/*  645 */     int j = paramJSONObject2.length();
/*      */     
/*  647 */     String[][] arrayOfString = new String[i][j];
/*  648 */     StringBuilder stringBuilder1 = new StringBuilder();
/*  649 */     StringBuilder stringBuilder2 = new StringBuilder();
/*      */ 
/*      */     
/*  652 */     for (byte b = 0; b < paramJSONArray.length(); b++) {
/*  653 */       boolean bool; stringBuilder1.setLength(0);
/*  654 */       stringBuilder2.setLength(0);
/*  655 */       JSONObject jSONObject = paramJSONArray.getJSONObject(b);
/*  656 */       String str1 = jSONObject.getString("id");
/*  657 */       String str2 = "";
/*  658 */       String str3 = "";
/*  659 */       if (jSONObject.has("field")) str2 = jSONObject.getString("field"); 
/*  660 */       if (jSONObject.has("financial")) str3 = jSONObject.getString("financial"); 
/*  661 */       if (jSONObject.has("etype")) {
/*  662 */         bool = Util.getIntValue(jSONObject.getString("etype"), 0);
/*      */       } else {
/*  664 */         bool = true;
/*  665 */       }  int k = Util.getIntValue(str1.substring(0, str1.indexOf(",")));
/*  666 */       int m = Util.getIntValue(str1.substring(str1.indexOf(",") + 1));
/*      */       
/*  668 */       stringBuilder1.append("<td ");
/*      */ 
/*      */       
/*  671 */       try { if (!"combine_cell".equals(arrayOfString[k][m]))
/*      */         
/*      */         { 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */           
/*  679 */           int n = 1;
/*  680 */           int i1 = 1;
/*  681 */           if (jSONObject.has("rowspan"))
/*  682 */             n = Util.getIntValue(jSONObject.getString("rowspan"), 1); 
/*  683 */           if (jSONObject.has("colspan"))
/*  684 */             i1 = Util.getIntValue(jSONObject.getString("colspan"), 1); 
/*  685 */           if (n > 1 || i1 > 1) {
/*  686 */             if (n > 1)
/*  687 */               stringBuilder1.append(" rowspan=\"").append(n).append("\""); 
/*  688 */             if (i1 > 1)
/*  689 */               stringBuilder1.append(" colspan=\"").append(i1).append("\""); 
/*  690 */             if (n > i) n = i; 
/*  691 */             if (i1 > j) i1 = j; 
/*  692 */             for (byte b1 = 0; b1 < n; b1++) {
/*  693 */               for (byte b2 = 0; b2 < i1; b2++) {
/*  694 */                 if (b1 != 0 || b2 != 0) {
/*  695 */                   arrayOfString[k + b1][m + b2] = "combine_cell";
/*      */                 }
/*      */               } 
/*      */             } 
/*      */           } 
/*  700 */           String str4 = "";
/*  701 */           if (this.ispdf == 1) {
/*      */             
/*  703 */             if (paramString.indexOf("tab") > -1) {
/*  704 */               str4 = paramString + "_" + k + "_" + m;
/*      */             } else {
/*  706 */               str4 = paramString + "Td_" + k + "_" + m;
/*  707 */             }  String str = paramJSONObject1.getString("row_" + k);
/*  708 */             if (paramString.indexOf("tab") > -1) {
/*  709 */               stringBuilder1.append(" class=\"td_edesign td_etype_").append(bool).append(" ").append(str4).append(" ").append(paramString).append("_row_").append(k).append("\"").append(" style=\"height: ").append(str).append("px;\"");
/*      */             } else {
/*  711 */               stringBuilder1.append(" class=\"td_edesign td_etype_").append(bool).append(" ").append(str4).append(" main_row_").append(k).append("\"").append(" style=\"height: ").append(str).append("px;\"");
/*      */             } 
/*      */           } else {
/*      */             
/*  715 */             str4 = paramString + "Td_" + k + "_" + m;
/*  716 */             stringBuilder2.append(parseCellStyle(jSONObject, str4));
/*  717 */             if (jSONObject.has("eborder")) {
/*  718 */               stringBuilder2.append("\n").append("\t");
/*  719 */               JSONArray jSONArray = jSONObject.getJSONArray("eborder");
/*  720 */               for (byte b1 = 0; b1 < jSONArray.length(); b1++) {
/*  721 */                 JSONObject jSONObject1 = jSONArray.getJSONObject(b1);
/*  722 */                 int i2 = Util.getIntValue(jSONObject1.getString("style"));
/*  723 */                 if (bool != 7 || (!"top".equals(jSONObject1.getString("kind")) && !"bottom".equals(jSONObject1.getString("kind"))))
/*      */                 {
/*  725 */                   stringBuilder2.append(parseBorder(i2, jSONObject1.getString("kind"), jSONObject1.getString("color"))); } 
/*      */               } 
/*      */             } 
/*  728 */             stringBuilder1.append(" class=\"td_edesign td_etype_").append(bool).append(" ").append(str4).append("\"");
/*      */           } 
/*      */ 
/*      */ 
/*      */           
/*  733 */           if (bool == 2) {
/*  734 */             stringBuilder1.append(" _fieldlabel=\"").append(str2).append("\"");
/*  735 */           } else if (bool == 3) {
/*  736 */             stringBuilder1.append(" id=\"").append("field" + str2 + "_tdwrap").append("\"");
/*  737 */             stringBuilder1.append(" _fieldid=\"").append(str2).append("\"");
/*  738 */             String str = paramString.toUpperCase() + "." + getCellAttr(k, m);
/*  739 */             jSONObject.put("cellattr", str);
/*  740 */             stringBuilder1.append(" _cellattr=\"").append(str).append("\"");
/*  741 */             if (this.ispdf != 1) {
/*  742 */               if ((this.type == 0 || this.type == 2) && this.formulaMap.containsKey(str)) {
/*  743 */                 stringBuilder1.append(" _formula=\"").append(this.formulaMap.get(str)).append("\"");
/*      */               }
/*  745 */               if (this.type == 0 || this.type == 2) {
/*      */                 
/*  747 */                 if (this.formulaMap.containsKey(str2 + "$1")) {
/*  748 */                   stringBuilder1.append(" _formula=\"").append(this.formulaMap.get(str2 + "$1")).append("\"");
/*      */                 }
/*      */                 
/*  751 */                 if (this.formulaMap.containsKey(str2 + "$2")) {
/*  752 */                   stringBuilder1.append(" _formula=\"").append(this.formulaMap.get(str2 + "$2")).append("\"");
/*      */                 }
/*      */                 
/*  755 */                 if (this.formulaMap.containsKey(str2 + "$3")) {
/*  756 */                   stringBuilder1.append(" _formula=\"").append(this.formulaMap.get(str2 + "$3")).append("\"");
/*      */                 }
/*      */                 
/*  759 */                 if (this.formulaMap.containsKey(str2 + "$4")) {
/*  760 */                   stringBuilder1.append(" _formula=\"").append(this.formulaMap.get(str2 + "$4")).append("\"");
/*      */                 }
/*      */                 
/*  763 */                 if (this.formulaMap.containsKey(str2 + "$5")) {
/*  764 */                   stringBuilder1.append(" _formula=\"").append(this.formulaMap.get(str2 + "$5")).append("\"");
/*      */                 }
/*      */                 
/*  767 */                 if (this.formulaMap.containsKey(str2 + "$6")) {
/*  768 */                   stringBuilder1.append(" _formula=\"").append(this.formulaMap.get(str2 + "$6")).append("\"");
/*      */                 }
/*      */                 
/*  771 */                 if (this.formulaMap.containsKey(str2 + "$7")) {
/*  772 */                   stringBuilder1.append(" _formula=\"").append(this.formulaMap.get(str2 + "$7")).append("\"");
/*      */                 }
/*      */               } 
/*      */             } 
/*      */           } 
/*  777 */           if (str3.startsWith("1")) {
/*  778 */             stringBuilder1.append(parseFinancial(str3));
/*      */           }
/*  780 */           stringBuilder1.append(">").append("\n");
/*      */           
/*  782 */           String str5 = "", str6 = "", str7 = "";
/*  783 */           if (jSONObject.has("attrs")) {
/*  784 */             Map<String, String> map1 = parseAttrs(jSONObject.getJSONObject("attrs"));
/*  785 */             str5 = map1.get("_text");
/*  786 */             str6 = map1.get("_class");
/*  787 */             str7 = map1.get("_style");
/*      */           } 
/*  789 */           String str8 = "";
/*  790 */           if (bool == true || bool == 13 || !bool) {
/*  791 */             str8 = "etypeisText=\"1\"";
/*      */           }
/*  793 */           stringBuilder1.append("<div ").append(str5)
/*  794 */             .append("".equals(str6) ? "" : ("class=\"" + str6 + "\" "))
/*  795 */             .append("".equals(str7) ? "" : ("style=\"" + str7 + "\" ")).append(str8)
/*  796 */             .append(">").append("\n");
/*      */ 
/*      */           
/*  799 */           Map<String, String> map = parseCellText(paramString, jSONObject);
/*  800 */           stringBuilder1.append(map.get("cellText"));
/*  801 */           stringBuilder2.append(map.get("cellStyle"));
/*      */           
/*  803 */           stringBuilder1.append("</div>").append("\n");
/*  804 */           stringBuilder1.append("</td>").append("\n");
/*  805 */           arrayOfString[k][m] = stringBuilder1.toString();
/*      */           
/*  807 */           stringBuilder2.append("height:").append(paramJSONObject1.get("row_" + k)).append("px; ");
/*      */           
/*  809 */           if (this.type == 0 || this.type == 1) {
/*  810 */             float f = countTdWidth(paramJSONObject2, m, i1);
/*  811 */             if (f != -1.0F)
/*  812 */               stringBuilder2.append("width:").append(f).append("px; "); 
/*      */           } 
/*  814 */           this.tempCss.append(".").append(str4).append("{\n").append(stringBuilder2).append("}").append("\n"); }  } catch (Exception exception) { exception.printStackTrace(); }
/*      */     
/*  816 */     }  return arrayOfString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public JSONObject getCustomStyle(int paramInt) throws JSONException {
/*  823 */     JSONObject jSONObject = new JSONObject();
/*  824 */     RecordSet recordSet = new RecordSet();
/*  825 */     recordSet.executeSql("select * from workflow_tabstyle where styleid=" + paramInt);
/*  826 */     if (recordSet.next()) {
/*  827 */       jSONObject.put("image_bg", Util.null2String(recordSet.getString("image_bg")));
/*  828 */       jSONObject.put("image_sep", Util.null2String(recordSet.getString("image_sep")));
/*  829 */       jSONObject.put("image_sepwidth", Util.null2String(recordSet.getString("image_sepwidth")));
/*      */       
/*  831 */       jSONObject.put("sel_bgleft", Util.null2String(recordSet.getString("sel_bgleft")));
/*  832 */       jSONObject.put("sel_bgleftwidth", Util.null2String(recordSet.getString("sel_bgleftwidth")));
/*  833 */       jSONObject.put("sel_bgmiddle", Util.null2String(recordSet.getString("sel_bgmiddle")));
/*  834 */       jSONObject.put("sel_bgright", Util.null2String(recordSet.getString("sel_bgright")));
/*  835 */       jSONObject.put("sel_bgrightwidth", Util.null2String(recordSet.getString("sel_bgrightwidth")));
/*  836 */       jSONObject.put("sel_color", Util.null2String(recordSet.getString("sel_color")));
/*  837 */       jSONObject.put("sel_fontsize", Util.null2String(recordSet.getString("sel_fontsize")));
/*  838 */       jSONObject.put("sel_family", Util.null2String(recordSet.getString("sel_family")));
/*  839 */       jSONObject.put("sel_bold", Util.null2String(recordSet.getString("sel_bold")));
/*  840 */       jSONObject.put("sel_italic", Util.null2String(recordSet.getString("sel_italic")));
/*      */       
/*  842 */       jSONObject.put("unsel_bgleft", Util.null2String(recordSet.getString("unsel_bgleft")));
/*  843 */       jSONObject.put("unsel_bgleftwidth", Util.null2String(recordSet.getString("unsel_bgleftwidth")));
/*  844 */       jSONObject.put("unsel_bgmiddle", Util.null2String(recordSet.getString("unsel_bgmiddle")));
/*  845 */       jSONObject.put("unsel_bgright", Util.null2String(recordSet.getString("unsel_bgright")));
/*  846 */       jSONObject.put("unsel_bgrightwidth", Util.null2String(recordSet.getString("unsel_bgrightwidth")));
/*  847 */       jSONObject.put("unsel_color", Util.null2String(recordSet.getString("unsel_color")));
/*  848 */       jSONObject.put("unsel_fontsize", Util.null2String(recordSet.getString("unsel_fontsize")));
/*  849 */       jSONObject.put("unsel_family", Util.null2String(recordSet.getString("unsel_family")));
/*  850 */       jSONObject.put("unsel_bold", Util.null2String(recordSet.getString("unsel_bold")));
/*  851 */       jSONObject.put("unsel_italic", Util.null2String(recordSet.getString("unsel_italic")));
/*      */     } 
/*  853 */     return jSONObject;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private String analyzeDetail(String paramString) {
/*  860 */     String str = "";
/*  861 */     int i = Util.getIntValue(paramString.replace("detail_", "")) - 1;
/*      */     try {
/*  863 */       JSONObject jSONObject = this.etable.getJSONObject(paramString);
/*  864 */       if (jSONObject.has("edtitleinrow") && jSONObject.has("edtailinrow"))
/*  865 */       { if (jSONObject.has("seniorset") && "1".equals(jSONObject.getString("seniorset"))) {
/*  866 */           str = analyzeDetail_Senior(paramString, jSONObject);
/*      */         } else {
/*  868 */           str = analyzeDetail_Simple(paramString, jSONObject);
/*      */         }  }
/*  870 */       else { str = "<span class=\"warnInfoSpan\">" + SystemEnv.getHtmlLabelName(19325, this.languageid) + "" + (i + 1) + "" + SystemEnv.getHtmlLabelName(84102, this.languageid) + "</span>"; } 
/*  871 */     } catch (Exception exception) {
/*  872 */       str = "<span class=\"warnInfoSpan\">" + SystemEnv.getHtmlLabelName(32395, this.languageid) + "" + SystemEnv.getHtmlLabelName(19325, this.languageid) + "" + (i + 1) + "" + SystemEnv.getHtmlLabelName(32140, this.languageid) + "</span>";
/*  873 */       writeLog(exception);
/*      */     } 
/*      */     
/*  876 */     return str;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private String analyzeDetail_Senior(String paramString, JSONObject paramJSONObject) throws Exception {
/*  883 */     int i = Util.getIntValue(paramString.replace("detail_", "")) - 1;
/*  884 */     int j = Util.getIntValue(paramJSONObject.getString("edtitleinrow"));
/*  885 */     int k = Util.getIntValue(paramJSONObject.getString("edtailinrow"));
/*  886 */     JSONObject jSONObject1 = paramJSONObject.getJSONObject("rowheads");
/*  887 */     JSONObject jSONObject2 = paramJSONObject.getJSONObject("colheads");
/*  888 */     JSONObject jSONObject3 = new JSONObject();
/*  889 */     JSONObject jSONObject4 = new JSONObject();
/*  890 */     if (paramJSONObject.has("rowattrs"))
/*  891 */       jSONObject3 = paramJSONObject.getJSONObject("rowattrs"); 
/*  892 */     if (paramJSONObject.has("colattrs"))
/*  893 */       jSONObject4 = paramJSONObject.getJSONObject("colattrs"); 
/*  894 */     int m = jSONObject1.length();
/*  895 */     int n = jSONObject2.length();
/*      */     
/*  897 */     WFNodeDtlFieldManager wFNodeDtlFieldManager = new WFNodeDtlFieldManager();
/*  898 */     wFNodeDtlFieldManager.resetParameter();
/*  899 */     wFNodeDtlFieldManager.setNodeid(this.nodeid);
/*  900 */     wFNodeDtlFieldManager.setFormid(this.formid);
/*  901 */     wFNodeDtlFieldManager.setIsbill(this.isbill);
/*  902 */     wFNodeDtlFieldManager.setGroupid(i);
/*  903 */     wFNodeDtlFieldManager.selectWfNodeDtlField();
/*  904 */     boolean bool = false;
/*  905 */     if (this.type == 0 && "1".equals(wFNodeDtlFieldManager.getAllowscroll()) && jSONObject2.toString().indexOf("%") == -1 && this.ispdf == 0) {
/*  906 */       bool = true;
/*      */     }
/*  908 */     String str1 = "detailDiv_" + i;
/*  909 */     String str2 = "oTable" + i;
/*      */     
/*  911 */     StringBuilder stringBuilder = new StringBuilder();
/*  912 */     stringBuilder.append("<div id=\"").append(str1).append("\" class=\"");
/*  913 */     if (bool && this.ispdf == 0) {
/*  914 */       this.tempCss.append(".tablefixed{table-layout:fixed;}").append("\n");
/*  915 */       stringBuilder.append("excelDetailOuterDiv");
/*      */     } 
/*  917 */     if (this.ispdf == 1) {
/*  918 */       stringBuilder.append("excelDetailOuterDiv");
/*      */     }
/*  920 */     stringBuilder.append("\">").append("\n");
/*  921 */     if ((this.type == 0 || this.type == 1) && this.fullparse)
/*  922 */       stringBuilder.append("<seniordetailmark>").append("\n"); 
/*  923 */     stringBuilder.append("<table class=\"excelDetailTable\" _seniorset=\"y\" ")
/*  924 */       .append("id=\"").append(str2).append("\" ")
/*  925 */       .append("name=\"").append(str2).append("\" ")
/*  926 */       .append("style=\"");
/*  927 */     if (!bool)
/*  928 */       stringBuilder.append("width:100%;"); 
/*  929 */     stringBuilder.append(parseTableBkImage(paramString, paramJSONObject, n));
/*  930 */     stringBuilder.append("\">").append("\n");
/*  931 */     stringBuilder.append("<tbody>").append("\n");
/*      */     
/*  933 */     byte b = -1;
/*  934 */     if (bool) {
/*  935 */       double d = 0.0D;
/*  936 */       for (byte b2 = 0; b2 < n; b2++) {
/*  937 */         d += Double.parseDouble(jSONObject2.getString("col_" + b2));
/*      */       }
/*      */       
/*  940 */       this.tempScript.append("jQuery(document).ready(function(){").append("\n");
/*  941 */       this.tempScript.append("\t jQuery('table#" + str2 + "').width('" + d + "px');").append("\n");
/*  942 */       this.tempScript.append("});\n");
/*      */     } else {
/*  944 */       b = 100;
/*      */     } 
/*      */     
/*  947 */     stringBuilder.append(parseDetailTableWidth(jSONObject2, jSONObject4, "", b));
/*      */     
/*  949 */     stringBuilder.append(parseTableImage(paramString, paramJSONObject, n));
/*      */     
/*  951 */     String[][] arrayOfString = buildDetailCellArray(paramString, paramJSONObject, jSONObject1, jSONObject2, jSONObject3, jSONObject4, true);
/*      */     
/*  953 */     int i1 = Util.getIntValue((new BaseBean()).getPropValue("workflow_eDesign", "printDetailEveryPageHead"), 0);
/*  954 */     String str3 = "";
/*  955 */     for (byte b1 = 0; b1 < m; b1++) {
/*  956 */       if (this.type == 1 && i1 == 1) {
/*  957 */         if (b1 == 0)
/*  958 */           stringBuilder.append("<thead>"); 
/*  959 */         if (b1 == j)
/*  960 */           stringBuilder.append("</thead>"); 
/*      */       } 
/*  962 */       if (b1 != j && b1 != k) {
/*      */         
/*  964 */         String str4 = "", str5 = "", str6 = "";
/*  965 */         if (jSONObject3.has("row_" + b1)) {
/*  966 */           Map<String, String> map = parseAttrs(jSONObject3.getJSONObject("row_" + b1));
/*  967 */           str4 = map.get("_text");
/*  968 */           str5 = map.get("_class");
/*  969 */           str6 = map.get("_style");
/*      */         } 
/*  971 */         stringBuilder.append("<tr ");
/*  972 */         if (b1 < j) {
/*  973 */           stringBuilder.append("_target=\"headrow\" ");
/*  974 */           stringBuilder.append("class=\"").append(this.detailheadrowmark).append(" ").append(str5).append("\" ");
/*  975 */           stringBuilder.append("style=\"height:").append(jSONObject1.get("row_" + b1)).append("px;").append(str6).append("\" ");
/*  976 */           stringBuilder.append(str4);
/*  977 */         } else if (b1 > j && b1 < k) {
/*  978 */           stringBuilder.append("_target=\"datarow\" ");
/*  979 */           if (this.ispdf == 1) {
/*  980 */             stringBuilder.append("style=\"height:").append(jSONObject1.get("row_" + b1)).append("px;").append("\" ");
/*      */           }
/*  982 */         } else if (b1 > k) {
/*  983 */           stringBuilder.append("_target=\"tailrow\" ");
/*      */         } 
/*  985 */         stringBuilder.append(">").append("\n");
/*  986 */         for (byte b2 = 0; b2 < n; b2++) {
/*  987 */           str3 = Util.null2String(arrayOfString[b1][b2]);
/*  988 */           if (!"combine_cell".equals(str3))
/*      */           {
/*  990 */             if ("".equals(str3))
/*  991 */             { String str = getClassByColAttrs(jSONObject4, b2);
/*  992 */               if (this.ispdf == 1) {
/*  993 */                 stringBuilder.append("<td class=\"" + str + "\" style=\"height:").append(jSONObject1.get("row_" + b1)).append("px;\"></td>");
/*      */               } else {
/*  995 */                 stringBuilder.append("<td class=\"" + str + "\" ></td>");
/*      */               }  }
/*  997 */             else { stringBuilder.append(str3); }
/*      */              } 
/*      */         } 
/* 1000 */         stringBuilder.append("</tr>").append("\n");
/*      */       } 
/* 1002 */     }  stringBuilder.append("</tbody>").append("\n").append("</table>").append("\n");
/* 1003 */     if ((this.type == 0 || this.type == 1) && this.fullparse)
/* 1004 */       stringBuilder.append("</seniordetailmark>").append("\n"); 
/* 1005 */     stringBuilder.append("</div>\n");
/* 1006 */     return stringBuilder.toString();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private String analyzeDetail_Simple(String paramString, JSONObject paramJSONObject) throws Exception {
/* 1014 */     int i = Util.getIntValue(paramString.replace("detail_", "")) - 1;
/* 1015 */     StringBuilder stringBuilder = new StringBuilder();
/* 1016 */     int j = Util.getIntValue(paramJSONObject.getString("edtitleinrow"));
/* 1017 */     int k = Util.getIntValue(paramJSONObject.getString("edtailinrow"));
/* 1018 */     JSONObject jSONObject1 = paramJSONObject.getJSONObject("rowheads");
/* 1019 */     JSONObject jSONObject2 = paramJSONObject.getJSONObject("colheads");
/* 1020 */     JSONObject jSONObject3 = new JSONObject();
/* 1021 */     JSONObject jSONObject4 = new JSONObject();
/* 1022 */     if (paramJSONObject.has("rowattrs"))
/* 1023 */       jSONObject3 = paramJSONObject.getJSONObject("rowattrs"); 
/* 1024 */     if (paramJSONObject.has("colattrs"))
/* 1025 */       jSONObject4 = paramJSONObject.getJSONObject("colattrs"); 
/* 1026 */     int m = jSONObject1.length();
/* 1027 */     int n = jSONObject2.length();
/*      */     
/* 1029 */     WFNodeDtlFieldManager wFNodeDtlFieldManager = new WFNodeDtlFieldManager();
/* 1030 */     wFNodeDtlFieldManager.resetParameter();
/* 1031 */     wFNodeDtlFieldManager.setNodeid(this.nodeid);
/* 1032 */     wFNodeDtlFieldManager.setFormid(this.formid);
/* 1033 */     wFNodeDtlFieldManager.setIsbill(this.isbill);
/* 1034 */     wFNodeDtlFieldManager.setGroupid(i);
/* 1035 */     wFNodeDtlFieldManager.selectWfNodeDtlField();
/* 1036 */     boolean bool1 = true;
/* 1037 */     if (this.type == 1 && !"1".equals(wFNodeDtlFieldManager.getIsprintserial()))
/* 1038 */       bool1 = false; 
/* 1039 */     boolean bool2 = false;
/* 1040 */     if (this.type == 0 && "1".equals(wFNodeDtlFieldManager.getAllowscroll()) && jSONObject2.toString().indexOf("%") == -1 && this.ispdf == 0) {
/* 1041 */       bool2 = true;
/*      */     }
/* 1043 */     String str1 = "detailDiv_" + i;
/* 1044 */     String str2 = "oTable" + i;
/* 1045 */     stringBuilder.append("<div id=\"").append(str1).append("\" class=\"");
/* 1046 */     if (bool2) {
/* 1047 */       this.tempCss.append(".tablefixed{table-layout:fixed;}").append("\n");
/* 1048 */       stringBuilder.append("excelDetailOuterDiv");
/*      */     } 
/* 1050 */     if (this.ispdf == 1) {
/* 1051 */       stringBuilder.append("excelDetailOuterDiv");
/*      */     }
/* 1053 */     stringBuilder.append("\">").append("\n");
/* 1054 */     stringBuilder.append("<table class=\"excelDetailTable\" ")
/* 1055 */       .append("id=\"").append(str2).append("\" ")
/* 1056 */       .append("name=\"").append(str2).append("\" ")
/* 1057 */       .append("style=\"");
/* 1058 */     if (!bool2)
/* 1059 */       stringBuilder.append("width:100%;"); 
/* 1060 */     stringBuilder.append(parseTableBkImage(paramString, paramJSONObject, n));
/* 1061 */     stringBuilder.append("\">").append("\n");
/* 1062 */     stringBuilder.append("<tbody>").append("\n");
/*      */     
/* 1064 */     String str3 = "";
/* 1065 */     byte b = -1;
/* 1066 */     if (bool2) {
/* 1067 */       str3 = "60px";
/* 1068 */       double d = 60.0D;
/* 1069 */       for (byte b2 = 0; b2 < n; b2++) {
/* 1070 */         d += Double.parseDouble(jSONObject2.getString("col_" + b2));
/*      */       }
/*      */       
/* 1073 */       this.tempScript.append("jQuery(document).ready(function(){").append("\n");
/* 1074 */       this.tempScript.append("\t jQuery('table#" + str2 + "').width('" + d + "px');").append("\n");
/* 1075 */       this.tempScript.append("});\n");
/*      */     } else {
/* 1077 */       b = 100;
/* 1078 */       if (bool1) {
/* 1079 */         str3 = "6%";
/* 1080 */         b = 94;
/* 1081 */         for (byte b2 = 0; b2 < jSONObject2.length(); b2++) {
/* 1082 */           String str = jSONObject2.getString("col_" + b2);
/* 1083 */           if (str.indexOf("%") == -1) {
/* 1084 */             jSONObject2.put("col_" + b2, (Double.parseDouble(str) * 0.94D) + "");
/*      */           } else {
/* 1086 */             jSONObject2.put("col_" + b2, (Double.parseDouble(str.replace("%", "")) * 0.94D) + "%");
/*      */           } 
/*      */         } 
/*      */       } 
/*      */     } 
/*      */     
/* 1092 */     stringBuilder.append(parseDetailTableWidth(jSONObject2, jSONObject4, str3, b));
/*      */     
/* 1094 */     stringBuilder.append(parseTableImage(paramString, paramJSONObject, n));
/*      */     
/* 1096 */     String[][] arrayOfString = buildDetailCellArray(paramString, paramJSONObject, jSONObject1, jSONObject2, jSONObject3, jSONObject4, false);
/*      */     
/* 1098 */     int i1 = Util.getIntValue((new BaseBean()).getPropValue("workflow_eDesign", "printDetailEveryPageHead"), 0);
/* 1099 */     String str4 = "";
/* 1100 */     for (byte b1 = 0; b1 < m; b1++) {
/* 1101 */       if (this.type == 1 && i1 == 1) {
/* 1102 */         if (b1 == 0)
/* 1103 */           stringBuilder.append("<thead>"); 
/* 1104 */         if (b1 == j)
/* 1105 */           stringBuilder.append("</thead>"); 
/*      */       } 
/* 1107 */       if (b1 != j && b1 < k) {
/*      */         
/* 1109 */         String str5 = "", str6 = "", str7 = "";
/* 1110 */         if (jSONObject3.has("row_" + b1)) {
/* 1111 */           Map<String, String> map = parseAttrs(jSONObject3.getJSONObject("row_" + b1));
/* 1112 */           str5 = map.get("_text");
/* 1113 */           str6 = map.get("_class");
/* 1114 */           str7 = map.get("_style");
/*      */         } 
/* 1116 */         stringBuilder.append("<tr ");
/* 1117 */         if (b1 < j) {
/* 1118 */           stringBuilder.append("_target=\"headrow\" ");
/* 1119 */           stringBuilder.append("class=\"").append(this.detailheadrowmark).append(" ").append(str6).append("\" ");
/* 1120 */           stringBuilder.append("style=\"height:").append(jSONObject1.get("row_" + b1)).append("px;").append(str7).append("\" ");
/* 1121 */           stringBuilder.append(str5);
/* 1122 */         } else if (b1 > j && b1 < k) {
/* 1123 */           stringBuilder.append("_target=\"datarow\" ");
/*      */         } 
/* 1125 */         stringBuilder.append(">").append("\n");
/* 1126 */         if (bool1) {
/*      */           
/* 1128 */           if (this.ispdf == 1) {
/* 1129 */             stringBuilder.append("<td class=\"detail_" + i + "_" + b1 + "_0\" style=\"background-image:none !important;\">");
/* 1130 */             if (b1 == j - 1) {
/* 1131 */               stringBuilder.append("<input type=\"checkbox\" notbeauty=\"true\" name=\"check_all_record\"  title=\"" + SystemEnv.getHtmlLabelName(556, this.languageid) + "\" />").append("\n");
/* 1132 */               stringBuilder.append("<span>").append(SystemEnv.getHtmlLabelName(15486, this.languageid)).append("</span>");
/*      */             } 
/*      */           } else {
/* 1135 */             stringBuilder.append("<td class=\"detail" + i + "_" + b1 + "_0\" style=\"background-image:none !important;\">");
/* 1136 */             if (b1 == j - 1) {
/* 1137 */               stringBuilder.append("<input type=\"checkbox\" notbeauty=\"true\" name=\"check_all_record\" onclick=\"detailOperate.checkAllFun(" + i + ");\" title=\"" + SystemEnv.getHtmlLabelName(556, this.languageid) + "\" />").append("\n");
/* 1138 */               stringBuilder.append("<span>").append(SystemEnv.getHtmlLabelName(15486, this.languageid)).append("</span>");
/*      */             } 
/*      */           } 
/* 1141 */           stringBuilder.append("</td>").append("\n");
/*      */         } 
/* 1143 */         for (byte b2 = 0; b2 < n; b2++) {
/* 1144 */           str4 = Util.null2String(arrayOfString[b1][b2]);
/* 1145 */           if (!"combine_cell".equals(str4))
/*      */           {
/* 1147 */             if ("".equals(str4)) {
/* 1148 */               String str = getClassByColAttrs(jSONObject4, b2);
/* 1149 */               stringBuilder.append("<td class=\"" + str + "\"></td>");
/*      */             } else {
/* 1151 */               stringBuilder.append(str4);
/*      */             }  } 
/*      */         } 
/* 1154 */         stringBuilder.append("</tr>").append("\n");
/*      */       } 
/* 1156 */     }  stringBuilder.append("</tbody>").append("\n").append("</table>").append("\n");
/* 1157 */     stringBuilder.append("</div>\n");
/* 1158 */     return stringBuilder.toString();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private String[][] buildDetailCellArray(String paramString, JSONObject paramJSONObject1, JSONObject paramJSONObject2, JSONObject paramJSONObject3, JSONObject paramJSONObject4, JSONObject paramJSONObject5, boolean paramBoolean) throws Exception {
/* 1166 */     boolean bool = WorkflowServiceUtil.isMobileParseExcelMode(this.wfid + "", this.nodeid + "", this.changemode);
/* 1167 */     int i = Util.getIntValue(paramString.replace("detail_", "")) - 1;
/* 1168 */     int j = Util.getIntValue(paramJSONObject1.getString("edtitleinrow"));
/* 1169 */     int k = Util.getIntValue(paramJSONObject1.getString("edtailinrow"));
/* 1170 */     int m = paramJSONObject2.length();
/* 1171 */     int n = paramJSONObject3.length();
/*      */     
/* 1173 */     JSONArray jSONArray = paramJSONObject1.getJSONArray("ec");
/* 1174 */     String[][] arrayOfString = new String[m][n];
/* 1175 */     StringBuilder stringBuilder1 = new StringBuilder();
/* 1176 */     StringBuilder stringBuilder2 = new StringBuilder();
/*      */ 
/*      */     
/* 1179 */     for (byte b = 0; b < jSONArray.length(); b++) {
/* 1180 */       boolean bool1; stringBuilder1.setLength(0);
/* 1181 */       stringBuilder2.setLength(0);
/* 1182 */       JSONObject jSONObject = jSONArray.getJSONObject(b);
/* 1183 */       String str1 = jSONObject.getString("id");
/* 1184 */       String str2 = "";
/* 1185 */       String str3 = "";
/* 1186 */       if (jSONObject.has("field")) str2 = jSONObject.getString("field"); 
/* 1187 */       if (jSONObject.has("financial")) str3 = jSONObject.getString("financial"); 
/* 1188 */       if (jSONObject.has("etype")) { bool1 = Util.getIntValue(jSONObject.getString("etype"), 0); }
/* 1189 */       else { bool1 = true; }
/* 1190 */        int i1 = Util.getIntValue(str1.substring(0, str1.indexOf(",")));
/* 1191 */       int i2 = Util.getIntValue(str1.substring(str1.indexOf(",") + 1));
/* 1192 */       if (i1 != j && i1 != k)
/*      */       {
/* 1194 */         if (paramBoolean || i1 <= k) {
/*      */ 
/*      */           
/* 1197 */           String str4 = "", str5 = "", str6 = "", str7 = "";
/* 1198 */           if (jSONObject.has("attrs")) {
/* 1199 */             Map<String, String> map = parseAttrs(jSONObject.getJSONObject("attrs"), true);
/* 1200 */             str4 = map.get("_text");
/* 1201 */             str5 = map.get("_dftext");
/* 1202 */             str6 = map.get("_class");
/* 1203 */             str7 = map.get("_style");
/*      */           } 
/* 1205 */           if (!"".equals(str6))
/* 1206 */             str6 = " " + str6; 
/* 1207 */           str6 = str6 + getClassByColAttrs(paramJSONObject5, i2);
/* 1208 */           stringBuilder1.append("<td ").append(str4);
/*      */           
/* 1210 */           if (!"combine_cell".equals(arrayOfString[i1][i2]))
/*      */           
/*      */           { 
/* 1213 */             int i3 = 1;
/* 1214 */             int i4 = 1;
/* 1215 */             if (jSONObject.has("rowspan"))
/* 1216 */               i3 = Util.getIntValue(jSONObject.getString("rowspan"), 1); 
/* 1217 */             if (jSONObject.has("colspan"))
/* 1218 */               i4 = Util.getIntValue(jSONObject.getString("colspan"), 1); 
/* 1219 */             if (i3 > 1 || i4 > 1) {
/* 1220 */               if (i3 > 1)
/* 1221 */                 stringBuilder1.append(" rowspan=\"").append(i3).append("\""); 
/* 1222 */               if (i4 > 1)
/* 1223 */                 stringBuilder1.append(" colspan=\"").append(i4).append("\""); 
/* 1224 */               if (i3 > m) i3 = m; 
/* 1225 */               if (i4 > n) i4 = n; 
/* 1226 */               for (byte b1 = 0; b1 < i3; b1++) {
/* 1227 */                 for (byte b2 = 0; b2 < i4; b2++) {
/* 1228 */                   if (b1 != 0 || b2 != 0) {
/* 1229 */                     arrayOfString[i1 + b1][i2 + b2] = "combine_cell";
/*      */                   }
/*      */                 } 
/*      */               } 
/*      */             } 
/* 1234 */             String str8 = "";
/* 1235 */             if (this.ispdf == 1) {
/* 1236 */               str8 = "detail_" + (i + 1) + "_" + i1 + "_" + i2;
/*      */             } else {
/* 1238 */               str8 = "detail" + i + "_" + i1 + "_" + i2;
/*      */             } 
/* 1240 */             String str9 = str8 + " td_etype_" + bool1;
/* 1241 */             stringBuilder2.append(parseCellStyle(jSONObject, str8));
/*      */             
/* 1243 */             if (jSONObject.has("eborder")) {
/* 1244 */               stringBuilder2.append("\n").append("\t");
/* 1245 */               JSONArray jSONArray1 = jSONObject.getJSONArray("eborder");
/* 1246 */               for (byte b1 = 0; b1 < jSONArray1.length(); b1++) {
/* 1247 */                 JSONObject jSONObject1 = jSONArray1.getJSONObject(b1);
/* 1248 */                 int i5 = Util.getIntValue(jSONObject1.getString("style"));
/* 1249 */                 if (i2 != 0 || !"left".equals(jSONObject1.getString("kind")))
/*      */                 {
/* 1251 */                   if (i2 + i4 - 1 != n - 1 || !"right".equals(jSONObject1.getString("kind")))
/*      */                   {
/* 1253 */                     stringBuilder2.append(parseBorder(i5, jSONObject1.getString("kind"), jSONObject1.getString("color"))); }  } 
/*      */               } 
/*      */             } 
/* 1256 */             stringBuilder1.append(" class=\"").append(str9).append(str6).append("\"");
/* 1257 */             String str10 = paramString.toUpperCase() + "." + getCellAttr(i1, i2);
/* 1258 */             if (bool1 == 3) {
/* 1259 */               if (this.type == 0 || this.type == 1) {
/* 1260 */                 stringBuilder1.append(" ").append(str5);
/* 1261 */                 stringBuilder1.append(" _fieldclass=\"$[").append(str9).append(str6).append("]$\"");
/*      */               } 
/* 1263 */               if (this.type == 0 || this.type == 2) {
/* 1264 */                 stringBuilder1.append(" _cellattr=\"$[").append(str10).append("]$\"");
/* 1265 */                 stringBuilder1.append(" _fieldid=\"$[").append(str2).append("]$\"");
/* 1266 */                 if (this.formulaMap.containsKey(str10))
/* 1267 */                   stringBuilder1.append(" _formula=\"$[").append(this.formulaMap.get(str10)).append("]$\""); 
/* 1268 */                 if (this.formulaMap.containsKey(str2 + "$1"))
/* 1269 */                   stringBuilder1.append(" _formula=\"$[").append(this.formulaMap.get(str2 + "$1")).append("]$\""); 
/* 1270 */                 if (this.formulaMap.containsKey(str2 + "$2"))
/* 1271 */                   stringBuilder1.append(" _formula=\"$[").append(this.formulaMap.get(str2 + "$2")).append("]$\""); 
/* 1272 */                 if (this.formulaMap.containsKey(str2 + "$3"))
/* 1273 */                   stringBuilder1.append(" _formula=\"$[").append(this.formulaMap.get(str2 + "$3")).append("]$\""); 
/* 1274 */                 if (this.formulaMap.containsKey(str2 + "$4"))
/* 1275 */                   stringBuilder1.append(" _formula=\"$[").append(this.formulaMap.get(str2 + "$4")).append("]$\""); 
/* 1276 */                 if (this.formulaMap.containsKey(str2 + "$5"))
/* 1277 */                   stringBuilder1.append(" _formula=\"$[").append(this.formulaMap.get(str2 + "$5")).append("]$\""); 
/* 1278 */                 if (this.formulaMap.containsKey(str2 + "$6"))
/* 1279 */                   stringBuilder1.append(" _formula=\"$[").append(this.formulaMap.get(str2 + "$6")).append("]$\""); 
/* 1280 */                 if (this.formulaMap.containsKey(str2 + "$7"))
/* 1281 */                   stringBuilder1.append(" _formula=\"$[").append(this.formulaMap.get(str2 + "$7")).append("]$\""); 
/* 1282 */                 if (paramBoolean)
/* 1283 */                   stringBuilder1.append(" _fieldtype=\"").append(this.excelLayoutManager.getFieldType(this.formid, this.isbill, Util.getIntValue(str2), '_')).append("\""); 
/*      */               } 
/*      */             } 
/* 1286 */             if (bool1 == 2 && this.type == 2 && !"".equals(str2)) {
/* 1287 */               stringBuilder1.append(" _fieldlabel=\"").append(str2).append("\"");
/*      */             }
/* 1289 */             if (str3.startsWith("1")) {
/* 1290 */               stringBuilder1.append(parseFinancial(str3));
/*      */             }
/* 1292 */             stringBuilder1.append(">").append("\n");
/*      */ 
/*      */             
/* 1295 */             jSONObject.put("cellattr", str10);
/* 1296 */             Map<String, String> map = parseCellText(paramString, jSONObject, paramBoolean);
/* 1297 */             stringBuilder1.append(map.get("cellText"));
/* 1298 */             stringBuilder2.append(map.get("cellStyle"));
/*      */             
/* 1300 */             stringBuilder1.append("</td>").append("\n");
/* 1301 */             arrayOfString[i1][i2] = stringBuilder1.toString();
/*      */             
/* 1303 */             stringBuilder2.append("height:").append(paramJSONObject2.get("row_" + i1)).append("px;");
/* 1304 */             this.tempCss.append(".").append(str8).append("{\n").append(stringBuilder2).append(str7).append("\n").append("}").append("\n"); } 
/*      */         }  } 
/* 1306 */     }  return arrayOfString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private String analyzeMoreContent(String paramString) throws JSONException {
/* 1313 */     StringBuilder stringBuilder = new StringBuilder();
/*      */     try {
/* 1315 */       JSONObject jSONObject = this.etable.getJSONObject(paramString);
/* 1316 */       int i = Util.getIntValue(jSONObject.getString("rowcount"));
/* 1317 */       int j = Util.getIntValue(jSONObject.getString("colcount"));
/* 1318 */       String[][] arrayOfString = new String[i][j];
/* 1319 */       JSONArray jSONArray = jSONObject.getJSONArray("ec");
/*      */       
/* 1321 */       StringBuilder stringBuilder1 = new StringBuilder();
/* 1322 */       StringBuilder stringBuilder2 = new StringBuilder();
/* 1323 */       HashMap<Object, Object> hashMap = new HashMap<>();
/*      */       byte b;
/* 1325 */       for (b = 0; b < jSONArray.length(); b++) {
/* 1326 */         boolean bool; stringBuilder1.setLength(0);
/* 1327 */         stringBuilder2.setLength(0);
/* 1328 */         JSONObject jSONObject1 = jSONArray.getJSONObject(b);
/* 1329 */         String str1 = jSONObject1.getString("id");
/* 1330 */         int k = Util.getIntValue(str1.substring(0, str1.indexOf(",")));
/* 1331 */         int m = Util.getIntValue(str1.substring(str1.indexOf(",") + 1));
/*      */         
/* 1333 */         if (jSONObject1.has("etype")) { bool = Util.getIntValue(jSONObject1.getString("etype"), 0); }
/* 1334 */         else { bool = true; }
/* 1335 */          String str2 = "";
/* 1336 */         if (jSONObject1.has("field")) str2 = jSONObject1.getString("field"); 
/* 1337 */         if (bool == 14) {
/* 1338 */           if ("Y".equals(Util.null2String(jSONObject1.getString("brsign"))))
/* 1339 */             stringBuilder1.append("</br>"); 
/*      */         } else {
/*      */           String str3, str4, str5;
/* 1342 */           if (jSONObject1.has("attrs")) {
/* 1343 */             Map<String, String> map1 = parseAttrs(jSONObject1.getJSONObject("attrs"));
/* 1344 */             str3 = map1.get("_text");
/* 1345 */             str4 = map1.get("_class");
/* 1346 */             str5 = map1.get("_style");
/*      */           } else {
/* 1348 */             str3 = "";
/* 1349 */             str4 = "";
/* 1350 */             str5 = "";
/*      */           } 
/* 1352 */           String str6 = "";
/* 1353 */           if (this.ispdf == 1) {
/* 1354 */             str6 = paramString + "_" + k + "_" + m;
/*      */           } else {
/* 1356 */             str6 = paramString + "_" + k;
/*      */           } 
/* 1358 */           stringBuilder2.append(parseCellStyle(jSONObject1, str6));
/*      */           
/* 1360 */           stringBuilder1.append("<span ").append(str3)
/* 1361 */             .append(" class=\"span_mc ").append(str6).append(" ").append(str4).append("\" ");
/*      */           
/* 1363 */           if (this.ispdf == 1) {
/* 1364 */             stringBuilder1.append(" style=\"display:inline;").append(str5).append("\" ");
/*      */           } else {
/* 1366 */             stringBuilder1.append(" style=\"display:inline-block;").append(str5).append("\" ");
/*      */           } 
/* 1368 */           if (bool == 2) {
/* 1369 */             stringBuilder1.append(" _fieldlabel=\"" + str2 + "\" ");
/* 1370 */           } else if (bool == 3) {
/* 1371 */             stringBuilder1.append(" _fieldid=\"" + str2 + "\" ");
/*      */           } 
/* 1373 */           stringBuilder1.append(">").append("\n");
/* 1374 */           Map<String, String> map = parseCellText(paramString, jSONObject1);
/* 1375 */           stringBuilder1.append(map.get("cellText"));
/* 1376 */           stringBuilder2.append(map.get("cellStyle"));
/* 1377 */           stringBuilder1.append("</span>").append("\n");
/* 1378 */           this.tempCss.append(".").append(str6).append("{\n").append(stringBuilder2).append("}").append("\n");
/*      */         } 
/* 1380 */         arrayOfString[k][m] = stringBuilder1.toString();
/*      */       } 
/*      */       
/* 1383 */       for (b = 0; b < i; b++) {
/* 1384 */         for (byte b1 = 0; b1 < j; b1++) {
/* 1385 */           String str = Util.null2String(arrayOfString[b][b1]);
/* 1386 */           if (!"".equals(str))
/* 1387 */             stringBuilder.append(str); 
/*      */         } 
/*      */       } 
/* 1390 */     } catch (Exception exception) {
/* 1391 */       writeLog(exception);
/*      */     } 
/* 1393 */     return stringBuilder.toString();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private String parseMainTableWidth(JSONObject paramJSONObject, boolean paramBoolean) throws JSONException {
/* 1400 */     StringBuilder stringBuilder = new StringBuilder();
/* 1401 */     stringBuilder.append("<tr name=\"controlwidth\" style=\"height:0px !important;" + (this.fullparse ? "" : "display:none;") + "\">\n");
/* 1402 */     JSONObject jSONObject = paramJSONObject;
/* 1403 */     if (paramBoolean)
/* 1404 */       jSONObject = countPercentWidth(paramJSONObject, 100); 
/* 1405 */     for (byte b = 0; b < jSONObject.length(); b++) {
/* 1406 */       String str = jSONObject.getString("col_" + b);
/* 1407 */       if (str.indexOf("%") == -1 && str.indexOf("px") == -1)
/* 1408 */         str = str + "px"; 
/* 1409 */       if (str.indexOf("*") > -1)
/* 1410 */         str = "*"; 
/* 1411 */       stringBuilder.append("<td width=\"").append(str).append("\"").append("></td>\n");
/*      */     } 
/* 1413 */     stringBuilder.append("</tr>\n");
/* 1414 */     return stringBuilder.toString();
/*      */   }
/*      */   private String parseDetailTableWidth(JSONObject paramJSONObject1, JSONObject paramJSONObject2, String paramString, int paramInt) throws JSONException {
/* 1417 */     StringBuilder stringBuilder = new StringBuilder();
/* 1418 */     stringBuilder.append("<tr name=\"controlwidth\" style=\"height:0px !important;" + (this.fullparse ? "" : "display:none;") + "\" class=\"" + this.detailheadrowmark + "\">\n");
/* 1419 */     if (!"".equals(paramString))
/* 1420 */       stringBuilder.append("<td width=\"").append(paramString).append("\"></td>\n"); 
/* 1421 */     JSONObject jSONObject = paramJSONObject1;
/* 1422 */     if (paramInt > 0)
/* 1423 */       jSONObject = countPercentWidth(paramJSONObject1, paramInt); 
/* 1424 */     for (byte b = 0; b < jSONObject.length(); b++) {
/* 1425 */       String str1 = getClassByColAttrs(paramJSONObject2, b);
/* 1426 */       String str2 = jSONObject.getString("col_" + b);
/* 1427 */       if (str2.indexOf("%") == -1 && str2.indexOf("px") == -1)
/* 1428 */         str2 = str2 + "px"; 
/* 1429 */       if (str2.indexOf("*") > -1)
/* 1430 */         str2 = "*"; 
/* 1431 */       stringBuilder.append("<td width=\"").append(str2).append("\" ").append("".equals(str1) ? "" : ("class=\"" + str1 + "\" ")).append("></td>\n");
/*      */     } 
/* 1433 */     stringBuilder.append("</tr>\n");
/* 1434 */     return stringBuilder.toString();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private Map<String, String> parseCellText(String paramString, JSONObject paramJSONObject) throws Exception {
/* 1441 */     return parseCellText(paramString, paramJSONObject, false);
/*      */   } private Map<String, String> parseCellText(String paramString, JSONObject paramJSONObject, boolean paramBoolean) throws Exception {
/*      */     boolean bool;
/* 1444 */     StringBuilder stringBuilder1 = new StringBuilder();
/* 1445 */     StringBuilder stringBuilder2 = new StringBuilder();
/* 1446 */     RecordSet recordSet = new RecordSet();
/*      */     
/* 1448 */     if (paramJSONObject.has("etype")) { bool = Util.getIntValue(paramJSONObject.getString("etype"), 0); }
/* 1449 */     else { bool = true; }
/* 1450 */      String str1 = "", str2 = "", str3 = "", str4 = "";
/* 1451 */     if (paramJSONObject.has("evalue")) str1 = paramJSONObject.getString("evalue"); 
/* 1452 */     if (paramJSONObject.has("field")) str3 = paramJSONObject.getString("field"); 
/* 1453 */     if (paramJSONObject.has("financial")) str2 = paramJSONObject.getString("financial"); 
/* 1454 */     if (paramJSONObject.has("cellattr")) str4 = paramJSONObject.getString("cellattr"); 
/* 1455 */     if (bool == true || !bool) {
/* 1456 */       stringBuilder1.append("<span>");
/* 1457 */       str1 = str1.replaceAll("\r\n", "</br>").replaceAll("\r", "</br>").replaceAll("\n", "</br>").replaceAll(" ", "&nbsp;");
/* 1458 */       if (str1.equals("序号") && this.languageid == 8) {
/* 1459 */         str1 = "serial number";
/* 1460 */       } else if (str1.equals("合计") && this.languageid == 8) {
/* 1461 */         str1 = "Total";
/*      */       } 
/* 1463 */       stringBuilder1.append(str1);
/* 1464 */       stringBuilder1.append("</span>");
/* 1465 */     } else if (bool == 2) {
/* 1466 */       stringBuilder1.append("<span>");
/* 1467 */       String str = this.excelLayoutManager.getFieldName(Util.getIntValue(str3), this.formid, this.isbill, this.languageid);
/* 1468 */       if (this.fullparse) {
/* 1469 */         stringBuilder1.append(str);
/*      */       } else {
/* 1471 */         stringBuilder1.append("<input class=\"Label\" id=\"$label" + str3 + "$\" name=\"label" + str3 + "\" value=\"" + str + "\" />");
/* 1472 */       }  stringBuilder1.append("</span>");
/* 1473 */     } else if (bool == 3) {
/* 1474 */       stringBuilder1.append("<input ");
/* 1475 */       if (this.fullparse) {
/* 1476 */         stringBuilder1.append("type=\"hidden\" ");
/* 1477 */         if (str2.startsWith("2") || str2.startsWith("3") || str2.startsWith("4")) {
/* 1478 */           stringBuilder1.append(parseFinancial(str2));
/*      */         }
/* 1480 */         else if (paramJSONObject.has("format")) {
/* 1481 */           JSONObject jSONObject = paramJSONObject.getJSONObject("format");
/* 1482 */           if (!"".equals(jSONObject.toString().replace("{", "").replace("}", ""))) {
/* 1483 */             stringBuilder1.append("_format=\"$").append(jSONObject.toString().replace("\"", "")).append("$\" ");
/*      */           }
/*      */         } 
/*      */         
/* 1487 */         if (this.ispdf == 0) {
/* 1488 */           if (this.formulaMap.containsKey(str4)) {
/* 1489 */             stringBuilder1.append(" _formulafield_ ");
/*      */           }
/*      */           
/* 1492 */           if (this.formulaMap.containsKey(str3 + "$1")) {
/* 1493 */             stringBuilder1.append(" _propertyChangeFormulaField_ ");
/*      */           }
/*      */           
/* 1496 */           if (this.formulaMap.containsKey(str3 + "$2")) {
/* 1497 */             stringBuilder1.append(" _focusFormulaField_ ");
/*      */           }
/*      */           
/* 1500 */           if (this.formulaMap.containsKey(str3 + "$3")) {
/* 1501 */             stringBuilder1.append(" _blurFormulaField_ ");
/*      */           }
/*      */           
/* 1504 */           if (this.formulaMap.containsKey(str3 + "$4")) {
/* 1505 */             stringBuilder1.append(" _clickFormulaField_ ");
/*      */           }
/*      */           
/* 1508 */           if (this.formulaMap.containsKey(str3 + "$5")) {
/* 1509 */             stringBuilder1.append(" _doubleClickFormulaField_ ");
/*      */           }
/*      */           
/* 1512 */           if (this.formulaMap.containsKey(str3 + "$6")) {
/* 1513 */             stringBuilder1.append(" _mouseOverFormulaField_ ");
/*      */           }
/*      */           
/* 1516 */           if (this.formulaMap.containsKey(str3 + "$7")) {
/* 1517 */             stringBuilder1.append(" _mouseOutFormulaField_ ");
/*      */           }
/*      */         } 
/*      */       } else {
/* 1521 */         str1 = this.excelLayoutManager.getLayoutFieldPrefix(this.nodeid, Util.getIntValue(str3)) + str1;
/*      */       } 
/* 1523 */       stringBuilder1.append("class=\"InputStyle\" id=\"$field").append(str3).append("$\" name=\"field").append(str3).append("\" value=\"").append(str1).append("\" />");
/* 1524 */     } else if (bool == 4) {
/* 1525 */       if (Util.getIntValue(str3) == 999999999) {
/* 1526 */         stringBuilder1.append(SystemEnv.getHtmlLabelName(84441, Util.getIntValue(this.languageid)));
/*      */       }
/* 1528 */       else if (this.ispdf == 1 && Util.getIntValue(str3) == -10001) {
/* 1529 */         stringBuilder1.append("[").append(SystemEnv.getHtmlLabelName(21655, Util.getIntValue(this.languageid))).append("]");
/* 1530 */       } else if (this.ispdf == 1 && ("-1".equals(str3) || "-2".equals(str3) || "-3".equals(str3))) {
/* 1531 */         stringBuilder1.append("<input class=\"GrouplogLabelMarker\" id=\"$label" + str3 + "$\" name=\"label" + str3 + "\" type=\"hidden\"/>");
/*      */       } else {
/* 1533 */         str3 = manageNodeid(str3);
/* 1534 */         recordSet.executeSql(" select nodename from workflow_nodebase where id=" + Util.getIntValue(str3));
/* 1535 */         if (recordSet.next()) {
/* 1536 */           stringBuilder1.append(Util.formatMultiLang(recordSet.getString("nodename"), Util.null2String(Integer.valueOf(this.languageid))));
/*      */         }
/*      */       }
/*      */     
/* 1540 */     } else if (bool == 5) {
/* 1541 */       str3 = manageNodeid(str3);
/* 1542 */       stringBuilder1.append("<input class=\"InputStyle\" id=\"$node").append(str3)
/* 1543 */         .append("$\" name=\"node").append(str3).append("\" />");
/* 1544 */     } else if (bool == 6 && this.fullparse) {
/* 1545 */       String str = "";
/* 1546 */       if (paramJSONObject.has("field")) {
/* 1547 */         str = Util.null2String(paramJSONObject.get("field"));
/* 1548 */         if (!"".equals(str)) {
/* 1549 */           stringBuilder2.append("background-image:url(" + str + ") !important; ");
/* 1550 */           stringBuilder2.append("background-repeat:no-repeat !important; ");
/*      */         } 
/*      */       } 
/* 1553 */       if (this.ispdf == 1)
/* 1554 */         stringBuilder1.append("<img style=\"display:none\" tdbkimgurl_wea =\"").append(str).append("\"/>"); 
/* 1555 */       stringBuilder1.append("<span>");
/* 1556 */       stringBuilder1.append(str1.replaceAll("\r\n", "</br>").replaceAll("\r", "</br>").replaceAll("\n", "</br>").replaceAll(" ", "&nbsp;"));
/* 1557 */       stringBuilder1.append("</span>");
/* 1558 */     } else if (bool == 7) {
/* 1559 */       if (paramJSONObject.has("detail")) {
/* 1560 */         String str = paramJSONObject.getString("detail");
/* 1561 */         if (this.etable.has(str))
/* 1562 */           stringBuilder1.append(analyzeDetail(str)); 
/*      */       } 
/* 1564 */       stringBuilder2.append("padding:0px !important; ");
/* 1565 */     } else if (bool == 10 && this.type != 1 && this.ispdf == 0) {
/* 1566 */       int i = Util.getIntValue(paramString.replace("detail_", "")) - 1;
/* 1567 */       char c = (this.type == 2) ? ' ' : 'd';
/* 1568 */       if (!this.fullparse && c < ' ')
/* 1569 */         c = ' '; 
/* 1570 */       stringBuilder1.append("<div id=\"div").append(i).append("button\" class=\"detailButtonDiv\" style=\"width:" + c + "px;\">").append("\n");
/*      */       
/* 1572 */       WFNodeDtlFieldManager wFNodeDtlFieldManager = new WFNodeDtlFieldManager();
/* 1573 */       wFNodeDtlFieldManager.resetParameter();
/* 1574 */       wFNodeDtlFieldManager.setNodeid(this.nodeid);
/* 1575 */       wFNodeDtlFieldManager.setFormid(this.formid);
/* 1576 */       wFNodeDtlFieldManager.setIsbill(this.isbill);
/* 1577 */       wFNodeDtlFieldManager.setGroupid(i);
/* 1578 */       wFNodeDtlFieldManager.selectWfNodeDtlField();
/* 1579 */       if (this.fullparse) {
/* 1580 */         if ("1".equals(wFNodeDtlFieldManager.getIsopensapmul())) {
/* 1581 */           stringBuilder1.append("<button class=\"sapbtn" + ((this.type == 2) ? "_m" : "_p") + "\" type=\"button\" id=\"$sapmulbutton").append(i).append("$\" name=\"sapmulbutton").append(i)
/* 1582 */             .append("\" onclick=\"addSapRow").append(i + "(" + i + ")").append(";return false;\" title=\"SAP\"></button>\n");
/*      */         }
/* 1584 */         stringBuilder1.append("<button class=\"addbtn" + ((this.type == 2) ? "_m" : "_p") + "\" type=\"button\" id=\"$addbutton").append(i).append("$\" name=\"addbutton").append(i)
/* 1585 */           .append("\" onclick=\"addRow").append(i + "(" + i + ")").append(";return false;\" title=\"").append(SystemEnv.getHtmlLabelName(611, this.languageid)).append("\"></button>\n");
/* 1586 */         stringBuilder1.append("<button class=\"delbtn" + ((this.type == 2) ? "_m" : "_p") + "\" type=\"button\" id=\"$delbutton").append(i).append("$\" name=\"delbutton").append(i)
/* 1587 */           .append("\" onclick=\"deleteRow").append(i + "(" + i + ")").append(";return false;\" title=\"").append(SystemEnv.getHtmlLabelName(23777, this.languageid)).append("\"></button>\n");
/*      */       } else {
/* 1589 */         stringBuilder1.append("<button class=\"BtnFlow\" type=\"button\" id=\"$addbutton" + i + "$\" name=\"addbutton" + i + "\" accessKey=\"A\" onclick=\"addRow" + i + "(" + i + ");return false;\"><U>A</U>-" + SystemEnv.getHtmlLabelName(130662, ThreadVarLanguage.getLang()) + "</button>");
/* 1590 */         stringBuilder1.append("<button class=\"BtnFlow\" type=\"button\" id=\"$delbutton" + i + "$\" name=\"delbutton" + i + "\" accessKey=\"E\" onclick=\"deleteRow" + i + "(" + i + ");return false;\"><U>E</U>-" + SystemEnv.getHtmlLabelName(130663, ThreadVarLanguage.getLang()) + "</button>");
/*      */       } 
/* 1592 */       stringBuilder1.append("</div>");
/* 1593 */     } else if (bool == 11) {
/* 1594 */       if (paramJSONObject.has("fieldtype") && paramJSONObject.has("field"))
/* 1595 */         stringBuilder1.append(parseHref(Util.getIntValue(paramJSONObject.getString("fieldtype"), 1), Util.null2String(paramJSONObject.get("field")), str1)); 
/* 1596 */     } else if (bool == 12 && "main".equals(paramString) && this.fullparse) {
/* 1597 */       JSONObject jSONObject = paramJSONObject.getJSONObject("tab");
/* 1598 */       stringBuilder1.append(analyzeTabArea(jSONObject));
/* 1599 */       stringBuilder2.append("padding:6px; ");
/* 1600 */     } else if (bool == 13) {
/* 1601 */       if (paramJSONObject.has("mcpoint")) {
/* 1602 */         String str = paramJSONObject.getString("mcpoint");
/* 1603 */         if (this.etable.has(str))
/* 1604 */           stringBuilder1.append(analyzeMoreContent(str)); 
/*      */       } 
/* 1606 */     } else if (bool == 15 && this.fullparse && !"2".equals(this.isvalid)) {
/* 1607 */       if (this.ispdf == 1) {
/* 1608 */         stringBuilder1.append("<span style=\"font-family:Microsoft YaHei;\">" + SystemEnv.getHtmlLabelName(10004280, ThreadVarLanguage.getLang()) + "!</span>");
/*      */       }
/* 1610 */       else if (this.type == 2) {
/* 1611 */         stringBuilder1.append("" + SystemEnv.getHtmlLabelName(10004281, ThreadVarLanguage.getLang()) + "");
/*      */       }
/* 1613 */       else if (paramJSONObject.has("jsonparam")) {
/* 1614 */         stringBuilder1.append(parseHtml_portal(paramJSONObject.getJSONObject("jsonparam")));
/*      */       }
/*      */     
/* 1617 */     } else if (bool == 16 && this.fullparse && !"2".equals(this.isvalid)) {
/* 1618 */       if (this.ispdf == 1)
/* 1619 */       { stringBuilder1.append("<span style=\"font-family:Microsoft YaHei;\">" + SystemEnv.getHtmlLabelName(10004282, ThreadVarLanguage.getLang()) + "iframe!</span>"); }
/* 1620 */       else if (paramJSONObject.has("jsonparam"))
/* 1621 */       { stringBuilder1.append(parseHtml_iframe(paramJSONObject.getJSONObject("jsonparam"))); } 
/* 1622 */     } else if (bool == 17 && this.fullparse) {
/* 1623 */       if (paramJSONObject.has("jsonparam"))
/* 1624 */         stringBuilder1.append(parseHtml_scancode(paramJSONObject.getJSONObject("jsonparam"))); 
/* 1625 */     } else if (bool == 18 && this.fullparse) {
/* 1626 */       stringBuilder1.append("<span>");
/* 1627 */       stringBuilder1.append(this.excelLayoutManager.getFieldName(Util.getIntValue(str3), this.formid, this.isbill, this.languageid)).append("(").append(SystemEnv.getHtmlLabelName(358, this.languageid)).append(")");
/* 1628 */       stringBuilder1.append("</span>");
/* 1629 */     } else if (bool == 19 && this.fullparse) {
/* 1630 */       stringBuilder1.append("<input type=\"hidden\" id=\"$sumfield" + str3 + "$\" name=\"sumfield" + str3 + "\" value=\"" + str1 + "\" />");
/* 1631 */     } else if ((bool == 20 || bool == 21 || bool == 22) && this.fullparse) {
/* 1632 */       stringBuilder1.append("<input type=\"hidden\" name=\"detailSpecialMark\" value=\"" + bool + "\" />");
/* 1633 */     } else if (bool == 23) {
/* 1634 */       int i = Util.getIntValue(paramString.replace("detail_", "")) - 1;
/* 1635 */       stringBuilder1.append("<button class=\"BtnFlow\" type=\"button\" style=\"width:113px;height:35px;background-color:#55B1F9;border-color:#55B1F9;border-width:0px;border-radius:5px;\" ");
/* 1636 */       stringBuilder1.append("id=\"$invoiceButton" + i + "$\" name=\"invoiceButton" + i + "\"  onclick=\"invoiceScanning(" + i + ");return false;\">");
/* 1637 */       stringBuilder1.append("<span style=\"vertical-align:middle;padding-right:8px\">");
/* 1638 */       stringBuilder1.append("<img style=\"width:20px\" src=\"/workflow/exceldesign/image/controls/invoiceScanSmall.png\" />");
/* 1639 */       stringBuilder1.append("</span>");
/* 1640 */       stringBuilder1.append("<span><font color=\"white\">");
/* 1641 */       String str = SystemEnv.getHtmlLabelName(385177, this.languageid);
/* 1642 */       if (paramJSONObject.has("jsonparam") && 
/* 1643 */         paramJSONObject.getJSONObject("jsonparam").has("btn_titie")) {
/* 1644 */         str = paramJSONObject.getJSONObject("jsonparam").getString("btn_titie");
/*      */       }
/*      */       
/* 1647 */       stringBuilder1.append(str);
/* 1648 */       stringBuilder1.append("</font></span>");
/* 1649 */       stringBuilder1.append("</button>");
/* 1650 */     } else if (bool == 24 && this.ispdf == 1) {
/* 1651 */       stringBuilder1.append("<span>");
/* 1652 */       str1 = str1.replaceAll("\r\n", "</br>").replaceAll("\r", "</br>").replaceAll("\n", "</br>").replaceAll(" ", "&nbsp;");
/* 1653 */       if (str1.equals("序号") && this.languageid == 8) {
/* 1654 */         str1 = "SN";
/* 1655 */       } else if (str1.equals("合计") && this.languageid == 8) {
/* 1656 */         str1 = "Total";
/*      */       } 
/* 1658 */       stringBuilder1.append(str1);
/* 1659 */       stringBuilder1.append("</span>");
/* 1660 */     } else if (bool == 25) {
/* 1661 */       stringBuilder1.append("<span>");
/* 1662 */       stringBuilder1.append("</span>");
/* 1663 */     } else if (bool == 26) {
/* 1664 */       stringBuilder1.append("<span>");
/* 1665 */       stringBuilder1.append(SystemEnv.getHtmlLabelName(Util.getIntValue(str1), this.languageid));
/* 1666 */       stringBuilder1.append("</span>");
/* 1667 */     } else if (bool == 27) {
/* 1668 */       stringBuilder1.append("<div style=\"page-break-after:always\"></div>");
/* 1669 */     } else if (bool == 28) {
/* 1670 */       stringBuilder1.append("<span>");
/* 1671 */       stringBuilder1.append(Util.formatMultiLang(str1));
/* 1672 */       stringBuilder1.append("</span>");
/* 1673 */     } else if (bool == 29) {
/* 1674 */       String str = "";
/* 1675 */       if (str3.equals("1")) {
/* 1676 */         str = Util.null2String(this.generateWfInfo.get("workflowname"));
/* 1677 */       } else if (str3.equals("2")) {
/* 1678 */         str = Util.null2String(this.generateWfInfo.get("workflowtypename"));
/* 1679 */       } else if (str3.equals("3")) {
/* 1680 */         str = Util.null2String(this.generateWfInfo.get("workflowdesc"));
/* 1681 */       }  stringBuilder1.append("<span>");
/* 1682 */       stringBuilder1.append(Util.formatMultiLang(str, this.languageid + ""));
/* 1683 */       stringBuilder1.append("</span>");
/*      */     } 
/* 1685 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 1686 */     hashMap.put("cellText", stringBuilder1.toString() + "\n");
/* 1687 */     hashMap.put("cellStyle", stringBuilder2.toString());
/* 1688 */     return (Map)hashMap;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private Map<String, String> parseAttrs(JSONObject paramJSONObject) throws JSONException {
/* 1696 */     return parseAttrs(paramJSONObject, false);
/*      */   }
/*      */   private Map<String, String> parseAttrs(JSONObject paramJSONObject, boolean paramBoolean) throws JSONException {
/* 1699 */     StringBuilder stringBuilder1 = new StringBuilder();
/* 1700 */     StringBuilder stringBuilder2 = new StringBuilder();
/* 1701 */     String str1 = "";
/* 1702 */     String str2 = "";
/* 1703 */     Iterator<E> iterator = paramJSONObject.keys();
/* 1704 */     while (iterator.hasNext()) {
/* 1705 */       String str3 = iterator.next().toString();
/* 1706 */       String str4 = Util.null2String(paramJSONObject.get(str3));
/* 1707 */       if ("hide".equals(str3)) {
/* 1708 */         if ("y".equals(str4)) {
/* 1709 */           if (!"".equals(str2) && !str2.endsWith(";"))
/* 1710 */             str2 = str2 + ";"; 
/* 1711 */           str2 = str2 + "display:none;";
/*      */         }  continue;
/* 1713 */       }  if ("style".equals(str3)) {
/* 1714 */         str2 = str2 + str4; continue;
/* 1715 */       }  if ("class".equals(str3)) {
/* 1716 */         str1 = str4; continue;
/*      */       } 
/* 1718 */       stringBuilder1.append(str3).append("=").append("\"").append(str4).append("\" ");
/* 1719 */       if (paramBoolean) {
/* 1720 */         stringBuilder2.append("_attr").append(str3).append("=").append("\"$[").append(str4).append("]$\" ");
/*      */       }
/*      */     } 
/* 1723 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 1724 */     hashMap.put("_text", stringBuilder1.toString());
/* 1725 */     hashMap.put("_dftext", stringBuilder2.toString());
/* 1726 */     hashMap.put("_class", str1);
/* 1727 */     hashMap.put("_style", str2);
/* 1728 */     return (Map)hashMap;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private String getClassByColAttrs(JSONObject paramJSONObject, int paramInt) throws JSONException {
/* 1735 */     String str = "";
/* 1736 */     if (paramJSONObject.has("col_" + paramInt)) {
/* 1737 */       JSONObject jSONObject = paramJSONObject.getJSONObject("col_" + paramInt);
/* 1738 */       if (jSONObject.has("hide") && "y".equals(jSONObject.getString("hide")))
/* 1739 */         str = str + " detail_hide_col"; 
/* 1740 */       if (jSONObject.has("class") && !"".equals(jSONObject.getString("class")))
/* 1741 */         str = str + " " + jSONObject.getString("class"); 
/*      */     } 
/* 1743 */     return str;
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   private String parseCellStyle(JSONObject paramJSONObject, String paramString) throws JSONException {
/*      */     byte b;
/* 1750 */     StringBuilder stringBuilder1 = new StringBuilder();
/*      */     
/* 1752 */     StringBuilder stringBuilder2 = new StringBuilder();
/*      */     
/* 1754 */     boolean bool = paramString.startsWith("mc_");
/*      */     
/* 1756 */     if (paramJSONObject.has("etype")) { b = Util.getIntValue(paramJSONObject.getString("etype"), 0); }
/* 1757 */     else { b = 1; }
/* 1758 */      if (paramJSONObject.has("backgroundColor") && b != 7) {
/* 1759 */       stringBuilder1.append("background:").append(paramJSONObject.get("backgroundColor")).append("!important; ");
/*      */     }
/* 1761 */     JSONObject jSONObject = new JSONObject();
/* 1762 */     if (paramJSONObject.has("font")) {
/* 1763 */       jSONObject = paramJSONObject.getJSONObject("font");
/*      */     }
/* 1765 */     Iterator<E> iterator = jSONObject.keys();
/* 1766 */     while (iterator.hasNext()) {
/* 1767 */       String str = iterator.next().toString();
/* 1768 */       if ("text-align".equals(str)) {
/* 1769 */         if (!"".equals(jSONObject.get(str)))
/* 1770 */           stringBuilder1.append("text-align:").append(jSONObject.get(str)).append("; ");  continue;
/* 1771 */       }  if ("valign".equals(str) && !bool) {
/* 1772 */         if (!"".equals(jSONObject.get(str)))
/* 1773 */           stringBuilder1.append("vertical-align:").append(jSONObject.get(str)).append("; ");  continue;
/* 1774 */       }  if ("autoWrap".equals(str)) {
/*      */         continue;
/*      */       }
/* 1777 */       if ("italic".equals(str)) {
/* 1778 */         if ("true".equals(Util.null2String(jSONObject.get(str))))
/* 1779 */           stringBuilder2.append("font-style:italic; ");  continue;
/* 1780 */       }  if ("bold".equals(str)) {
/* 1781 */         if ("true".equals(Util.null2String(jSONObject.get(str))))
/* 1782 */           stringBuilder2.append("font-weight:bold; ");  continue;
/* 1783 */       }  if ("underline".equals(str)) {
/* 1784 */         if ("true".equals(Util.null2String(jSONObject.get(str))))
/* 1785 */           stringBuilder2.append("text-decoration:underline; ");  continue;
/* 1786 */       }  if ("deleteline".equals(str)) {
/* 1787 */         if ("true".equals(Util.null2String(jSONObject.get(str))))
/* 1788 */           stringBuilder2.append("text-decoration:line-through; ");  continue;
/* 1789 */       }  if ("font-size".equals(str)) {
/* 1790 */         if (!"".equals(jSONObject.get(str))) {
/* 1791 */           stringBuilder2.append("font-size:").append(jSONObject.get(str)).append("!important; ");
/* 1792 */           int i = Util.getIntValue(jSONObject.getString(str).trim().replace("pt", ""));
/* 1793 */           if (b == 3 && i > 9) {
/* 1794 */             int j = fontsize_compare_height(i);
/* 1795 */             this.tempCss.append(".").append(paramString).append(" input[type=\"text\"]{\n")
/* 1796 */               .append("height:" + j + "px; line-height:" + j + "px;").append("\n}\n");
/*      */           } 
/*      */         }  continue;
/* 1799 */       }  if ("font-family".equals(str)) {
/* 1800 */         if (!"".equals(jSONObject.get(str)))
/* 1801 */           stringBuilder2.append("font-family:").append(jSONObject.get(str)).append("!important; ");  continue;
/* 1802 */       }  if ("color".equals(str) && 
/* 1803 */         !"".equals(jSONObject.get(str))) {
/* 1804 */         stringBuilder2.append("color:").append(jSONObject.get(str)).append("!important; ");
/*      */       }
/*      */     } 
/* 1807 */     stringBuilder1.append("word-break:break-all; word-wrap:break-word; word-break:break-word; ");
/*      */     
/* 1809 */     if (stringBuilder1.indexOf("text-align") == -1)
/* 1810 */       stringBuilder1.append("text-align:left; "); 
/* 1811 */     if (stringBuilder1.indexOf("vertical-align") == -1)
/* 1812 */       stringBuilder1.append("vertical-align:").append(bool ? "middle" : "top").append("; "); 
/* 1813 */     if (stringBuilder2.indexOf("font-size") == -1)
/* 1814 */       stringBuilder2.append("font-size:9pt; "); 
/* 1815 */     if (stringBuilder2.indexOf("font-family") == -1) {
/* 1816 */       stringBuilder2.append("font-family:Microsoft YaHei; ");
/*      */     }
/* 1818 */     if (paramJSONObject.has("etxtindent") && !"0".equals(paramJSONObject.getString("etxtindent")) && Pattern.matches("\\d+(\\.\\d+)?", paramJSONObject.getString("etxtindent"))) {
/* 1819 */       if (stringBuilder1.indexOf("text-align:left") > -1) {
/* 1820 */         stringBuilder1.append("padding-left:").append(Double.parseDouble(paramJSONObject.getString("etxtindent")) * 8.0D).append("px; ");
/* 1821 */       } else if (stringBuilder1.indexOf("text-align:right") > -1) {
/* 1822 */         stringBuilder1.append("padding-right:").append(Double.parseDouble(paramJSONObject.getString("etxtindent")) * 8.0D).append("px; ");
/*      */       } 
/*      */     }
/* 1825 */     if (b <= 6 || b == 11 || b == 18 || b == 19 || b == 20 || b == 21 || b == 22) {
/* 1826 */       this.tempCss.append(".").append(paramString).append(" *{\n").append(stringBuilder2).append("\n}\n");
/* 1827 */       if (bool)
/* 1828 */         stringBuilder1.append(stringBuilder2); 
/*      */     } 
/* 1830 */     return stringBuilder1.toString();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private String parseTableImage(String paramString, JSONObject paramJSONObject, int paramInt) throws JSONException {
/* 1837 */     if ((paramJSONObject.has("backgroundImage") || paramJSONObject.has("floatingObjectArray")) && this.fullparse) {
/* 1838 */       StringBuilder stringBuilder = new StringBuilder();
/* 1839 */       if (paramString.startsWith("detail")) {
/* 1840 */         stringBuilder.append("<tr class=\"" + this.detailheadrowmark + "\">");
/*      */       } else {
/* 1842 */         stringBuilder.append("<tr>");
/*      */       } 
/* 1844 */       stringBuilder.append("<td colspan=\"" + paramInt + "\" style=\"position:relative;padding:0px !important;margin:0px !important;\">").append("\n");
/* 1845 */       if (paramJSONObject.has("backgroundImage") && this.ispdf == 0) {
/* 1846 */         String str = Util.null2String(paramJSONObject.get("backgroundImage"));
/*      */         
/* 1848 */         stringBuilder.append("<img src=\"").append(str).append("\" style=\"position:absolute;z-index:-100;top:0px;left:0px;\" tablebkimg_wea=\"1\"/>").append("\n");
/*      */       } 
/* 1850 */       if (paramJSONObject.has("floatingObjectArray")) {
/* 1851 */         JSONObject jSONObject = paramJSONObject.getJSONObject("floatingObjectArray");
/* 1852 */         if (jSONObject.has("floatingObjects")) {
/* 1853 */           JSONArray jSONArray = jSONObject.getJSONArray("floatingObjects");
/* 1854 */           for (byte b = 0; b < jSONArray.length(); b++) {
/* 1855 */             JSONObject jSONObject1 = jSONArray.getJSONObject(b);
/* 1856 */             if (jSONObject1.has("x") && jSONObject1.has("y") && jSONObject1.has("width") && jSONObject1.has("height") && jSONObject1.has("src")) {
/* 1857 */               String str = Util.null2String(jSONObject1.get("src"));
/* 1858 */               stringBuilder.append("<div style=\"position:absolute; z-index:99998; padding:0px; margin:0px; ")
/*      */                 
/* 1860 */                 .append("width:").append(Util.null2String(jSONObject1.get("width")) + "px").append("; ")
/* 1861 */                 .append("height:").append(Util.null2String(jSONObject1.get("height")) + "px").append("; ")
/* 1862 */                 .append("top:").append(Util.null2String(jSONObject1.get("y")) + "px").append("; ")
/* 1863 */                 .append("left:").append(Util.null2String(jSONObject1.get("x")) + "px").append("; ")
/* 1864 */                 .append("\"><img src=\"").append(str).append("\" style=\"width:100%;height:100%\" floatingimg_wea=\"1\"/></div>").append("\n");
/*      */             } 
/*      */           } 
/*      */         } 
/*      */       } 
/* 1869 */       stringBuilder.append("</td>").append("</tr>").append("\n");
/* 1870 */       return stringBuilder.toString();
/*      */     } 
/* 1872 */     return "";
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private String parseTableBkImage(String paramString, JSONObject paramJSONObject, int paramInt) throws JSONException {
/* 1880 */     if (this.ispdf == 1) {
/* 1881 */       if (paramJSONObject.has("backgroundImage") && this.fullparse) {
/* 1882 */         StringBuilder stringBuilder = new StringBuilder();
/* 1883 */         if (paramJSONObject.has("backgroundImage")) {
/* 1884 */           String str = Util.null2String(paramJSONObject.get("backgroundImage"));
/* 1885 */           if (!"".equals(str))
/*      */           {
/* 1887 */             stringBuilder.append("background-image:url(").append(str).append(");background-repeat: no-repeat;");
/*      */           }
/*      */         } 
/* 1890 */         return stringBuilder.toString();
/*      */       } 
/* 1892 */       return "";
/*      */     } 
/*      */     
/* 1895 */     return "";
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private StringBuffer parseBorder(int paramInt, String paramString1, String paramString2) {
/* 1903 */     StringBuffer stringBuffer = new StringBuffer();
/* 1904 */     switch (paramInt) {
/*      */       case 0:
/*      */         break;
/*      */       case 1:
/* 1908 */         stringBuffer.append("border-").append(paramString1).append("-width:1px; ")
/* 1909 */           .append("border-").append(paramString1).append("-style:solid; ");
/*      */         break;
/*      */       case 2:
/* 1912 */         stringBuffer.append("border-").append(paramString1).append("-width:2px; ")
/* 1913 */           .append("border-").append(paramString1).append("-style:solid; ");
/*      */         break;
/*      */       case 3:
/* 1916 */         stringBuffer.append("border-").append(paramString1).append("-width:1px; ")
/* 1917 */           .append("border-").append(paramString1).append("-style:dashed; ");
/*      */         break;
/*      */       case 5:
/* 1920 */         stringBuffer.append("border-").append(paramString1).append("-width:3px; ")
/* 1921 */           .append("border-").append(paramString1).append("-style:solid; ");
/*      */         break;
/*      */       case 6:
/* 1924 */         stringBuffer.append("border-").append(paramString1).append("-width:3px; ")
/* 1925 */           .append("border-").append(paramString1).append("-style:double; ");
/*      */         break;
/*      */       case 7:
/* 1928 */         stringBuffer.append("border-").append(paramString1).append("-width:1px; ")
/* 1929 */           .append("border-").append(paramString1).append("-style:dotted; ");
/*      */         break;
/*      */       case 8:
/* 1932 */         stringBuffer.append("border-").append(paramString1).append("-width:2px; ")
/* 1933 */           .append("border-").append(paramString1).append("-style:dashed; ");
/*      */         break;
/*      */       default:
/* 1936 */         stringBuffer.append("border-").append(paramString1).append("-width:1px; ")
/* 1937 */           .append("border-").append(paramString1).append("-style:solid; ");
/*      */         break;
/*      */     } 
/* 1940 */     if (!"".equals(paramString2))
/* 1941 */       stringBuffer.append("border-").append(paramString1).append("-color:").append(paramString2).append("; "); 
/* 1942 */     return stringBuffer;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private String parseFinancial(String paramString) {
/* 1949 */     String str = "";
/* 1950 */     int i = 3;
/* 1951 */     if (paramString.startsWith("1")) {
/* 1952 */       if (paramString.indexOf("-") > -1)
/* 1953 */         i = Util.getIntValue(paramString.substring(paramString.indexOf("-") + 1), 3); 
/* 1954 */       str = " _financialhead=\"$[" + i + "]$\" ";
/* 1955 */     } else if (paramString.startsWith("2")) {
/* 1956 */       if (paramString.indexOf("-") > -1)
/* 1957 */         i = Util.getIntValue(paramString.substring(paramString.indexOf("-") + 1), 3); 
/* 1958 */       str = " _financialfield=\"$[" + i + "]$\" ";
/* 1959 */     } else if (paramString.startsWith("3")) {
/* 1960 */       str = " _format=\"${decimals:-1,formatPattern:-1,thousands:-1,numberType:99}$\" ";
/* 1961 */     } else if (paramString.startsWith("4")) {
/* 1962 */       str = " _format=\"${decimals:2,formatPattern:2,thousands:1,numberType:2}$\" ";
/*      */     } 
/* 1964 */     return str;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private String parseHref(int paramInt, String paramString1, String paramString2) {
/* 1971 */     String str = "";
/* 1972 */     if (this.ispdf == 1) {
/* 1973 */       str = "<span style=\"text-decoration:none;\">" + paramString2 + "</span>";
/*      */     } else {
/* 1975 */       str = "<a target=\"_blank\" href=\"";
/* 1976 */       switch (paramInt) {
/*      */         case 1:
/* 1978 */           str = str + "http://";
/*      */           break;
/*      */         case 2:
/* 1981 */           str = str + "https://";
/*      */           break;
/*      */         case 3:
/* 1984 */           str = str + "ftp://";
/*      */           break;
/*      */         case 4:
/* 1987 */           str = str + "news://";
/*      */           break;
/*      */       } 
/* 1990 */       str = str + paramString1 + "\">" + paramString2 + "</a>";
/*      */     } 
/* 1992 */     return str;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String parseHtml_portal(JSONObject paramJSONObject) throws JSONException {
/* 1999 */     String str1 = paramJSONObject.has("hpid") ? paramJSONObject.getString("hpid") : "";
/* 2000 */     String str2 = paramJSONObject.has("trifields") ? paramJSONObject.getString("trifields") : "";
/* 2001 */     if ("".equals(str1))
/* 2002 */       return ""; 
/* 2003 */     StringBuilder stringBuilder = new StringBuilder();
/* 2004 */     String str3 = "/homepage/maint/HomepageForWorkflow.jsp?isSetting=false&hpid=" + str1 + "&paramfieldid=" + str2;
/* 2005 */     str3 = str3 + "&requestid=" + this.requestid + "&wfid=" + this.wfid + "&nodeid=" + this.nodeid + "&formid=" + this.formid + "&isbill=" + this.isbill + "&moduleid=" + this.modeid + "&layouttype=" + this.type;
/*      */     
/* 2007 */     if (!"".equals(str2)) {
/* 2008 */       stringBuilder.append("<input type=\"hidden\" id=\"portalInfo_").append(str1)
/* 2009 */         .append("\" _trifields=\"").append(str2).append("\" value=\"").append(str3).append("\" />");
/* 2010 */       str3 = "";
/* 2011 */       this.tempScript.append("jQuery(document).ready(function(){").append("\n");
/* 2012 */       this.tempScript.append("\t portalOperate.initEvent('" + str1 + "');").append("\n");
/* 2013 */       this.tempScript.append("});\n");
/*      */     } 
/* 2015 */     stringBuilder.append("<div class=\"portalLoading\" id=\"portalLoading_" + str1 + "\">")
/* 2016 */       .append("<span><img src=\"/images/loading2_wev8.gif\" align=\"absmiddle\" /></span>")
/* 2017 */       .append("<span>  ").append(SystemEnv.getHtmlLabelName(125516, this.languageid)).append("</span>")
/* 2018 */       .append("</div>")
/* 2019 */       .append("<iframe id=\"portalIframe_").append(str1)
/* 2020 */       .append("\" name=\"portalIframe_").append(str1)
/* 2021 */       .append("\" src=\"").append(str3)
/* 2022 */       .append("\" frameborder=\"0\" scrolling=\"auto\" ")
/* 2023 */       .append("style=\"width:100%;height:100%;display:none;\" >").append("</iframe>");
/* 2024 */     return stringBuilder.toString();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String parseHtml_iframe(JSONObject paramJSONObject) throws JSONException {
/* 2031 */     String str1 = paramJSONObject.has("set_id") ? paramJSONObject.getString("set_id") : "";
/* 2032 */     String str2 = paramJSONObject.has("set_name") ? paramJSONObject.getString("set_name") : "";
/* 2033 */     String str3 = paramJSONObject.has("set_src") ? paramJSONObject.getString("set_src") : "";
/* 2034 */     String str4 = paramJSONObject.has("set_height") ? paramJSONObject.getString("set_height") : "";
/* 2035 */     String str5 = paramJSONObject.has("set_style") ? paramJSONObject.getString("set_style") : "";
/* 2036 */     if ("".equals(str3) || "".equals(str4)) {
/* 2037 */       return "";
/*      */     }
/* 2039 */     if (str3.indexOf("$requestid$") > -1)
/* 2040 */       str3 = str3.replaceAll("\\$requestid\\$", this.requestid + ""); 
/* 2041 */     if (str3.indexOf("$workflowid$") > -1)
/* 2042 */       str3 = str3.replaceAll("\\$workflowid\\$", this.wfid + ""); 
/* 2043 */     if (str3.indexOf("$nodeid$") > -1)
/* 2044 */       str3 = str3.replaceAll("\\$nodeid\\$", this.nodeid + ""); 
/* 2045 */     if (str3.indexOf("$formid$") > -1)
/* 2046 */       str3 = str3.replaceAll("\\$formid\\$", this.formid + ""); 
/* 2047 */     boolean bool = (!str3.startsWith("/spa") && !str3.startsWith("/wui/index.html#") && !str3.startsWith("/wui/engine.html#")) ? true : false;
/* 2048 */     if (this.type == 2 && bool) {
/* 2049 */       String str8 = str3;
/* 2050 */       String str9 = "";
/* 2051 */       if (str3.indexOf("?") > 0) {
/* 2052 */         str8 = str3.substring(0, str3.indexOf("?"));
/* 2053 */         str9 = "&" + str3.substring(str3.indexOf("?") + 1);
/*      */       } 
/* 2055 */       if (!"".equals(str8) && !str8.startsWith("http://") && !str8.startsWith("https://") && 
/* 2056 */         !str8.endsWith(".com") && !str8.endsWith(".cn")) {
/*      */         
/* 2058 */         CustomPageRangeCache.appendPage(str8);
/* 2059 */         str3 = "/workflow/request/CustomPageForward.jsp?__address__=" + str8 + str9;
/*      */       } 
/*      */     } 
/*      */     
/* 2063 */     String str6 = "";
/* 2064 */     String str7 = str4;
/* 2065 */     if ("auto".equals(str4)) {
/* 2066 */       str7 = "100";
/* 2067 */       str6 = " adjustheight='y' eachcount=0 ";
/*      */     } 
/* 2069 */     StringBuilder stringBuilder = new StringBuilder();
/* 2070 */     stringBuilder.append("<div class=\"iframeLoading\" style=\"height:" + str7 + "px;line-height:" + str7 + "px;\">")
/* 2071 */       .append("<span><img src=\"/images/loading2_wev8.gif\" align=\"absmiddle\" /></span>")
/* 2072 */       .append("<span>  ").append(SystemEnv.getHtmlLabelName(125516, this.languageid)).append("</span>")
/* 2073 */       .append("</div>")
/* 2074 */       .append("<iframe src=\"" + str3 + "\"");
/* 2075 */     if (!"".equals(str1)) stringBuilder.append(" id=\"" + str1 + "\""); 
/* 2076 */     if (!"".equals(str2)) stringBuilder.append(" name=\"" + str2 + "\""); 
/* 2077 */     stringBuilder.append(" onload=\"iframeOperate.loadingOver(this);\" frameborder=\"0\" scrolling=\"auto\"")
/* 2078 */       .append(" style=\"width:100%;")
/*      */       
/* 2080 */       .append("auto".equals(str4) ? "height:0px;" : ("height:" + str4 + "px"))
/* 2081 */       .append(str5).append("\"")
/* 2082 */       .append(str6).append("></iframe>");
/* 2083 */     return stringBuilder.toString();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String parseHtml_scancode(JSONObject paramJSONObject) throws JSONException {
/* 2090 */     String str1 = paramJSONObject.has("codetype") ? paramJSONObject.getString("codetype") : "";
/* 2091 */     String str2 = paramJSONObject.has("relatefield") ? paramJSONObject.getString("relatefield") : "";
/* 2092 */     String str3 = paramJSONObject.has("scancodeData") ? paramJSONObject.getString("scancodeData") : "";
/* 2093 */     String str4 = calculateRelateValueNew(str2, str3);
/* 2094 */     String str5 = " onload=\"javascript:window.scancodeOperate.readyAdjustSize(this)\" ";
/* 2095 */     String str6 = "";
/* 2096 */     if ("1".equals(str1)) {
/* 2097 */       if ("".equals(str4))
/* 2098 */         str4 = SystemEnv.getHtmlLabelName(15863, this.languageid); 
/* 2099 */       String str = "/workflow/createQRCode?firstParam=1";
/* 2100 */       str4 = str4.replace("\"", "'");
/*      */       try {
/* 2102 */         str4 = URLEncoder.encode(str4, "UTF-8");
/* 2103 */       } catch (UnsupportedEncodingException unsupportedEncodingException) {
/* 2104 */         unsupportedEncodingException.printStackTrace();
/*      */       } 
/* 2106 */       str = str + "&msg=" + str4;
/*      */       
/* 2108 */       str6 = "<img class=\"scancode qrcodeimg\" " + str5 + " src=\"" + str + "\" />";
/* 2109 */     } else if ("2".equals(str1) && !"".equals(str4)) {
/* 2110 */       str4 = str4.replace("\"", "'");
/* 2111 */       char[] arrayOfChar = str4.toCharArray();
/* 2112 */       boolean bool = false;
/* 2113 */       for (char c : arrayOfChar) {
/* 2114 */         if (isChinese(c)) {
/* 2115 */           bool = true;
/*      */           break;
/*      */         } 
/*      */       } 
/* 2119 */       if (bool) {
/* 2120 */         str6 = "<img class=\"scancode\" src=\"/workflow/exceldesign/image/illegalBarCode_wev8.png\" />";
/*      */       } else {
/* 2122 */         String str = "/workflow/createWfBarCode?firstParam=1";
/* 2123 */         str = str + "&type=code128&qz=6&msg=" + str4;
/* 2124 */         if (paramJSONObject.has("hidetext") && ("true".equals(paramJSONObject.getString("hidetext")) || "1".equals(paramJSONObject.getString("hidetext")))) {
/* 2125 */           str = str + "&hrp=none";
/*      */         }
/* 2127 */         str6 = "<img class=\"scancode barcodeimg\" " + str5 + " src=\"" + str + "\" />";
/*      */       } 
/*      */     } 
/* 2130 */     return str6;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private String calculateRelateValueNew(String paramString1, String paramString2) {
/* 2137 */     RecordSet recordSet = new RecordSet();
/* 2138 */     String str = getTableName();
/* 2139 */     if (paramString2.equals("") && !paramString1.equals("")) {
/* 2140 */       String[] arrayOfString = paramString1.split(",");
/* 2141 */       for (String str1 : arrayOfString) {
/* 2142 */         if (str1.equals("requestid") || str1.equals("requestname")) {
/* 2143 */           paramString2 = paramString2 + '$' + str1 + '$';
/*      */         } else {
/* 2145 */           String str2 = getColumnName(str1);
/* 2146 */           paramString2 = paramString2 + '$' + str2 + '$';
/*      */         } 
/*      */       } 
/*      */     } 
/*      */     
/* 2151 */     paramString2 = paramString2.replace("$requestid$", this.requestid + "");
/* 2152 */     if (paramString2.contains("$requestname$")) {
/* 2153 */       recordSet.executeQuery("select requestname from workflow_requestbase where requestid=" + this.requestid, new Object[0]);
/* 2154 */       String str1 = "";
/* 2155 */       if (recordSet.next())
/* 2156 */         str1 = Util.null2String(recordSet.getString(1)); 
/* 2157 */       paramString2 = paramString2.replace("$requestname$", str1);
/*      */     } 
/*      */     
/* 2160 */     Pattern pattern = Pattern.compile("(\\$)([^$]+)(\\$)");
/* 2161 */     Matcher matcher = pattern.matcher(paramString2);
/* 2162 */     HashSet<String> hashSet = new HashSet();
/* 2163 */     while (matcher.find()) {
/* 2164 */       String str1 = "";
/* 2165 */       String str2 = matcher.group(2);
/*      */       
/* 2167 */       if (!hashSet.contains(str2)) {
/*      */         try {
/* 2169 */           recordSet.executeSql("select " + str2 + " from " + str + " where requestid=" + this.requestid);
/* 2170 */           if (recordSet.next())
/* 2171 */             str1 = Util.null2String(recordSet.getString(1)); 
/* 2172 */         } catch (Exception exception) {}
/* 2173 */         paramString2 = paramString2.replace("$" + str2 + "$", str1);
/*      */       } 
/* 2175 */       hashSet.add(str2);
/*      */     } 
/* 2177 */     return paramString2;
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   private String calculateRelateValue(String paramString) {
/* 2183 */     if ("".equals(paramString) || this.requestid <= 0)
/* 2184 */       return ""; 
/* 2185 */     RecordSet recordSet = new RecordSet();
/* 2186 */     String str1 = "";
/* 2187 */     String[] arrayOfString = paramString.split(",");
/* 2188 */     String str2 = getTableName();
/* 2189 */     for (String str3 : arrayOfString) {
/* 2190 */       String str4 = "";
/* 2191 */       if ("requestid".equals(str3)) {
/* 2192 */         str4 = this.requestid + "";
/* 2193 */       } else if ("requestname".equals(str3)) {
/* 2194 */         recordSet.executeSql("select requestname from workflow_requestbase where requestid=" + this.requestid);
/* 2195 */         if (recordSet.next())
/* 2196 */           str4 = Util.null2String(recordSet.getString(1)); 
/*      */       } else {
/* 2198 */         String str = getColumnName(str3);
/* 2199 */         if (!"".equals(str2) && !"".equals(str)) {
/*      */           try {
/* 2201 */             recordSet.executeSql("select " + str + " from " + str2 + " where requestid=" + this.requestid);
/* 2202 */             if (recordSet.next())
/* 2203 */               str4 = Util.null2String(recordSet.getString(1)); 
/* 2204 */           } catch (Exception exception) {}
/*      */         }
/*      */       } 
/* 2207 */       str1 = str1 + str4;
/*      */     } 
/* 2209 */     return str1;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private String getTableName() {
/* 2216 */     String str = "";
/* 2217 */     if (this.isbill == 0) {
/* 2218 */       str = "workflow_form";
/* 2219 */     } else if (this.isbill == 1) {
/* 2220 */       RecordSet recordSet = new RecordSet();
/* 2221 */       recordSet.executeSql("select tablename from workflow_bill where id=" + this.formid);
/* 2222 */       if (recordSet.next())
/* 2223 */         str = Util.null2String(recordSet.getString("tablename")); 
/*      */     } 
/* 2225 */     return str;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private String getColumnName(String paramString) {
/* 2232 */     if (Util.getIntValue(paramString) <= 0)
/* 2233 */       return ""; 
/* 2234 */     RecordSet recordSet = new RecordSet();
/* 2235 */     String str = "";
/* 2236 */     if (this.isbill == 0) {
/* 2237 */       recordSet.executeSql("select fieldname from workflow_formdict where id=" + paramString);
/* 2238 */       if (recordSet.next())
/* 2239 */         str = Util.null2String(recordSet.getString(1)); 
/* 2240 */     } else if (this.isbill == 1) {
/* 2241 */       recordSet.executeSql("select fieldname from workflow_billfield where id=" + paramString + " and billid=" + this.formid);
/* 2242 */       if (recordSet.next())
/* 2243 */         str = Util.null2String(recordSet.getString(1)); 
/*      */     } 
/* 2245 */     return str;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private static boolean isChinese(char paramChar) {
/* 2252 */     Character.UnicodeBlock unicodeBlock = Character.UnicodeBlock.of(paramChar);
/* 2253 */     if (unicodeBlock == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS || unicodeBlock == Character.UnicodeBlock.CJK_COMPATIBILITY_IDEOGRAPHS || unicodeBlock == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS_EXTENSION_A || unicodeBlock == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS_EXTENSION_B || unicodeBlock == Character.UnicodeBlock.CJK_SYMBOLS_AND_PUNCTUATION || unicodeBlock == Character.UnicodeBlock.HALFWIDTH_AND_FULLWIDTH_FORMS || unicodeBlock == Character.UnicodeBlock.GENERAL_PUNCTUATION)
/*      */     {
/*      */ 
/*      */       
/* 2257 */       return true;
/*      */     }
/* 2259 */     return false;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String decodeStr(String paramString) {
/* 2266 */     byte[] arrayOfByte1 = "WEAVER E-DESIGN.".getBytes();
/* 2267 */     byte[] arrayOfByte2 = "weaver e-design.".getBytes();
/*      */     try {
/* 2269 */       PaddedBufferedBlockCipher paddedBufferedBlockCipher = new PaddedBufferedBlockCipher((BlockCipher)new CBCBlockCipher((BlockCipher)new AESFastEngine()));
/*      */       
/* 2271 */       paddedBufferedBlockCipher.init(true, (CipherParameters)new ParametersWithIV((CipherParameters)new KeyParameter(arrayOfByte1), arrayOfByte2));
/* 2272 */       byte[] arrayOfByte3 = Hex.decode(paramString);
/* 2273 */       paddedBufferedBlockCipher.init(false, (CipherParameters)new ParametersWithIV((CipherParameters)new KeyParameter(arrayOfByte1), arrayOfByte2));
/* 2274 */       byte[] arrayOfByte4 = new byte[paddedBufferedBlockCipher.getOutputSize(arrayOfByte3.length)];
/* 2275 */       int i = paddedBufferedBlockCipher.processBytes(arrayOfByte3, 0, arrayOfByte3.length, arrayOfByte4, 0);
/* 2276 */       int j = paddedBufferedBlockCipher.doFinal(arrayOfByte4, i);
/* 2277 */       byte[] arrayOfByte5 = new byte[i + j];
/* 2278 */       System.arraycopy(arrayOfByte4, 0, arrayOfByte5, 0, arrayOfByte5.length);
/*      */       
/* 2280 */       return new String(arrayOfByte5);
/* 2281 */     } catch (Exception exception) {
/* 2282 */       exception.printStackTrace();
/*      */       
/* 2284 */       return "";
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String decodeStrByCharset(String paramString1, String paramString2) {
/* 2295 */     byte[] arrayOfByte1 = "WEAVER E-DESIGN.".getBytes();
/* 2296 */     byte[] arrayOfByte2 = "weaver e-design.".getBytes();
/*      */     try {
/* 2298 */       PaddedBufferedBlockCipher paddedBufferedBlockCipher = new PaddedBufferedBlockCipher((BlockCipher)new CBCBlockCipher((BlockCipher)new AESFastEngine()));
/*      */       
/* 2300 */       paddedBufferedBlockCipher.init(true, (CipherParameters)new ParametersWithIV((CipherParameters)new KeyParameter(arrayOfByte1), arrayOfByte2));
/* 2301 */       byte[] arrayOfByte3 = Hex.decode(paramString1);
/* 2302 */       paddedBufferedBlockCipher.init(false, (CipherParameters)new ParametersWithIV((CipherParameters)new KeyParameter(arrayOfByte1), arrayOfByte2));
/* 2303 */       byte[] arrayOfByte4 = new byte[paddedBufferedBlockCipher.getOutputSize(arrayOfByte3.length)];
/* 2304 */       int i = paddedBufferedBlockCipher.processBytes(arrayOfByte3, 0, arrayOfByte3.length, arrayOfByte4, 0);
/* 2305 */       int j = paddedBufferedBlockCipher.doFinal(arrayOfByte4, i);
/* 2306 */       byte[] arrayOfByte5 = new byte[i + j];
/* 2307 */       System.arraycopy(arrayOfByte4, 0, arrayOfByte5, 0, arrayOfByte5.length);
/*      */       
/* 2309 */       return new String(arrayOfByte5, paramString2);
/* 2310 */     } catch (Exception exception) {
/* 2311 */       exception.printStackTrace();
/*      */       
/* 2313 */       return "";
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String encodeStr(String paramString) {
/*      */     try {
/* 2324 */       byte[] arrayOfByte1 = "WEAVER E-DESIGN.".getBytes();
/* 2325 */       byte[] arrayOfByte2 = "weaver e-design.".getBytes();
/* 2326 */       PaddedBufferedBlockCipher paddedBufferedBlockCipher = new PaddedBufferedBlockCipher((BlockCipher)new CBCBlockCipher((BlockCipher)new AESFastEngine()));
/*      */       
/* 2328 */       paddedBufferedBlockCipher.init(true, (CipherParameters)new ParametersWithIV((CipherParameters)new KeyParameter(arrayOfByte1), arrayOfByte2));
/*      */ 
/*      */       
/* 2331 */       byte[] arrayOfByte3 = new byte[paddedBufferedBlockCipher.getOutputSize((paramString.getBytes()).length)];
/* 2332 */       int i = paddedBufferedBlockCipher.processBytes(paramString.getBytes(), 0, (paramString
/* 2333 */           .getBytes()).length, arrayOfByte3, 0);
/* 2334 */       int j = paddedBufferedBlockCipher.doFinal(arrayOfByte3, i);
/* 2335 */       byte[] arrayOfByte4 = new byte[i + j];
/* 2336 */       System.arraycopy(arrayOfByte3, 0, arrayOfByte4, 0, arrayOfByte4.length);
/*      */       
/* 2338 */       return new String(Hex.encode(arrayOfByte4));
/* 2339 */     } catch (Exception exception) {
/* 2340 */       exception.printStackTrace();
/*      */       
/* 2342 */       return "";
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   private HashMap<String, String> transFormulaJsonToMap(JSONObject paramJSONObject) {
/* 2349 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 2350 */     Iterator<E> iterator = paramJSONObject.keys();
/* 2351 */     while (iterator.hasNext()) {
/*      */       try {
/* 2353 */         String str1 = iterator.next().toString();
/* 2354 */         JSONObject jSONObject = paramJSONObject.getJSONObject(str1);
/* 2355 */         if (str1.contains("#new#")) {
/* 2356 */           transFormulaNewJsonToMap(jSONObject, str1, (HashMap)hashMap); continue;
/*      */         } 
/* 2358 */         String str2 = Util.null2String(jSONObject.get("destcell"));
/* 2359 */         String str3 = Util.null2String(jSONObject.get("formulatxt"));
/* 2360 */         if (!jSONObject.has("cellrange") || !jSONObject.get("cellrange").getClass().isArray())
/*      */           continue; 
/* 2362 */         JSONArray jSONArray = jSONObject.getJSONArray("cellrange");
/* 2363 */         if ("".equals(str2) || "".equals(str3) || jSONArray == null)
/*      */           continue; 
/* 2365 */         if (str3.indexOf("=") == -1)
/*      */           continue; 
/* 2367 */         for (byte b = 0; b < jSONArray.length(); b++) {
/* 2368 */           String str = jSONArray.getString(b);
/* 2369 */           if (hashMap.containsKey(str)) {
/* 2370 */             String str4 = (String)hashMap.get(str);
/* 2371 */             hashMap.put(str, str4 + "," + str1);
/*      */           } else {
/* 2373 */             hashMap.put(str, str1);
/*      */           }
/*      */         
/*      */         } 
/* 2377 */       } catch (Exception exception) {
/* 2378 */         writeLog(exception);
/*      */       } 
/*      */     } 
/* 2381 */     return (HashMap)hashMap;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private void transFormulaNewJsonToMap(JSONObject paramJSONObject, String paramString, HashMap<String, String> paramHashMap) {
/* 2389 */     String str = "";
/*      */     try {
/* 2391 */       str = Util.null2String(paramJSONObject.has("triggers") ? paramJSONObject.get("triggers") : "");
/* 2392 */     } catch (Exception exception) {
/* 2393 */       writeLog(exception);
/*      */     } 
/*      */     
/* 2396 */     if (str != null) {
/* 2397 */       String[] arrayOfString = str.split("#_#");
/* 2398 */       if (arrayOfString != null && arrayOfString.length == 2) {
/* 2399 */         String[] arrayOfString1 = arrayOfString[0].split("##");
/*      */ 
/*      */ 
/*      */         
/* 2403 */         for (byte b = 0; arrayOfString1 != null && b < arrayOfString1.length; b++) {
/* 2404 */           HashMap<Object, Object> hashMap = new HashMap<>();
/* 2405 */           String[] arrayOfString2 = arrayOfString1[b].split("#");
/* 2406 */           if (arrayOfString2 != null && arrayOfString2.length > 0 && 
/* 2407 */             arrayOfString2.length == 2) {
/* 2408 */             String str1 = arrayOfString2[0];
/* 2409 */             String str2 = arrayOfString2[1];
/* 2410 */             if (str2.indexOf(".") > -1) {
/* 2411 */               str2 = str2.substring(str2.indexOf(".") + 1);
/*      */             }
/*      */             
/* 2414 */             if ("1".equals(str1) && !hashMap.containsKey("1")) {
/* 2415 */               hashMap.put("1", str2);
/*      */             }
/*      */             
/* 2418 */             if ("2".equals(str1) && !hashMap.containsKey("2")) {
/* 2419 */               hashMap.put("2", str2);
/*      */             }
/*      */             
/* 2422 */             if ("3".equals(str1) && !hashMap.containsKey("3")) {
/* 2423 */               hashMap.put("3", str2);
/*      */             }
/*      */             
/* 2426 */             if ("4".equals(str1) && !hashMap.containsKey("4")) {
/* 2427 */               hashMap.put("4", str2);
/*      */             }
/*      */             
/* 2430 */             if ("5".equals(str1) && !hashMap.containsKey("5")) {
/* 2431 */               hashMap.put("5", str2);
/*      */             }
/*      */             
/* 2434 */             if ("6".equals(str1) && !hashMap.containsKey("6")) {
/* 2435 */               hashMap.put("6", str2);
/*      */             }
/*      */             
/* 2438 */             if ("7".equals(str1) && !hashMap.containsKey("7")) {
/* 2439 */               hashMap.put("7", str2);
/*      */             }
/*      */           } 
/*      */ 
/*      */           
/* 2444 */           if (hashMap != null && hashMap.size() > 0) {
/* 2445 */             Iterator<String> iterator = hashMap.keySet().iterator();
/*      */ 
/*      */ 
/*      */             
/* 2449 */             while (iterator.hasNext()) {
/* 2450 */               String str1 = iterator.next();
/* 2451 */               String str3 = (String)hashMap.get(str1);
/* 2452 */               String str2 = str3 + "$" + str1;
/* 2453 */               if (paramHashMap.containsKey(str2)) {
/* 2454 */                 String str4 = paramHashMap.get(str2);
/* 2455 */                 paramHashMap.put(str2, str4 + "," + paramString); continue;
/*      */               } 
/* 2457 */               paramHashMap.put(str2, paramString);
/*      */             } 
/*      */           } 
/*      */         } 
/*      */       } 
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private String getCellAttr(int paramInt1, int paramInt2) {
/* 2470 */     String str = "";
/* 2471 */     if (paramInt2 < 26) {
/* 2472 */       char c = (char)(65 + paramInt2);
/* 2473 */       str = str + String.valueOf(c);
/* 2474 */     } else if (paramInt2 >= 26 && paramInt2 < 676) {
/* 2475 */       char c1 = (char)(65 + paramInt2 / 26 - 1);
/* 2476 */       str = str + String.valueOf(c1);
/* 2477 */       char c2 = (char)(65 + paramInt2 % 26);
/* 2478 */       str = str + String.valueOf(c2);
/*      */     } 
/* 2480 */     str = str + (paramInt1 + 1);
/* 2481 */     return str;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private JSONObject countPercentWidth(JSONObject paramJSONObject, int paramInt) throws JSONException {
/* 2488 */     double d1 = 0.0D;
/* 2489 */     double d2 = 0.0D;
/* 2490 */     boolean bool = false;
/* 2491 */     for (byte b1 = 0; b1 < paramJSONObject.length(); b1++) {
/* 2492 */       String str = paramJSONObject.getString("col_" + b1);
/* 2493 */       if (str.indexOf("*") > -1) {
/* 2494 */         bool = true;
/*      */         break;
/*      */       } 
/* 2497 */       if (str.indexOf("%") > -1) {
/* 2498 */         d1 += Double.parseDouble(str.replace("%", ""));
/*      */       } else {
/* 2500 */         d2 += Double.parseDouble(str);
/*      */       } 
/*      */     } 
/* 2503 */     if (bool || d1 >= paramInt || d2 == 0.0D) {
/* 2504 */       return paramJSONObject;
/*      */     }
/* 2506 */     JSONObject jSONObject = new JSONObject();
/* 2507 */     for (byte b2 = 0; b2 < paramJSONObject.length(); b2++) {
/* 2508 */       String str = paramJSONObject.getString("col_" + b2);
/* 2509 */       if (str.indexOf("%") > -1) {
/* 2510 */         jSONObject.put("col_" + b2, str);
/*      */       } else {
/* 2512 */         double d = Float.parseFloat(str) / d2 * (paramInt - d1);
/* 2513 */         d = round(d, 2, 1);
/* 2514 */         jSONObject.put("col_" + b2, d + "%");
/*      */       } 
/*      */     } 
/* 2517 */     return jSONObject;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private float countTdWidth(JSONObject paramJSONObject, int paramInt1, int paramInt2) throws JSONException {
/* 2524 */     float f = 0.0F;
/* 2525 */     while (paramInt2 > 0) {
/* 2526 */       paramInt2--;
/* 2527 */       String str = paramJSONObject.getString("col_" + (paramInt1 + paramInt2));
/* 2528 */       if (str.indexOf("%") > -1)
/* 2529 */         return -1.0F; 
/* 2530 */       if (str.indexOf("*") > -1) {
/* 2531 */         return -1.0F;
/*      */       }
/* 2533 */       f += Float.parseFloat(str);
/*      */     } 
/*      */     
/* 2536 */     return f;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private int fontsize_compare_height(int paramInt) {
/* 2543 */     if (paramInt <= 10)
/* 2544 */       return 24; 
/* 2545 */     if (paramInt <= 12)
/* 2546 */       return 26; 
/* 2547 */     if (paramInt <= 24)
/* 2548 */       return paramInt * 2; 
/* 2549 */     if (paramInt <= 26)
/* 2550 */       return 50; 
/* 2551 */     if (paramInt <= 28)
/* 2552 */       return 52; 
/* 2553 */     if (paramInt <= 32)
/* 2554 */       return 58; 
/* 2555 */     if (paramInt <= 48) {
/* 2556 */       return 82;
/*      */     }
/* 2558 */     return 108;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private double round(double paramDouble, int paramInt1, int paramInt2) {
/* 2570 */     BigDecimal bigDecimal = new BigDecimal(paramDouble);
/* 2571 */     bigDecimal = bigDecimal.setScale(paramInt1, paramInt2);
/* 2572 */     double d = bigDecimal.doubleValue();
/* 2573 */     bigDecimal = null;
/* 2574 */     return d;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String manageNodeid(String paramString) {
/*      */     try {
/* 2582 */       RecordSet recordSet = new RecordSet();
/* 2583 */       String str = " select nodeid from workflow_flownode where workflowid=" + this.wfid + " and nodeid=" + paramString;
/* 2584 */       recordSet.executeSql(str);
/* 2585 */       if (!recordSet.next()) {
/* 2586 */         List list = WorkflowVersion.getChildrenNodeListByNodeID(paramString);
/* 2587 */         String str1 = "";
/* 2588 */         for (String str2 : list) {
/* 2589 */           str1 = str1 + str2 + ",";
/*      */         }
/* 2591 */         if (str1.endsWith(","))
/* 2592 */           str1 = str1.substring(0, str1.length() - 1); 
/* 2593 */         if (!"".equals(str1)) {
/* 2594 */           str = " select nodeid from workflow_flownode where workflowid=" + this.wfid + " and nodeid in (" + str1 + ") ";
/* 2595 */           recordSet.executeSql(str);
/* 2596 */           if (recordSet.next())
/* 2597 */             return Util.null2String(recordSet.getString("nodeid")); 
/*      */         } 
/*      */       } 
/* 2600 */     } catch (Exception exception) {
/* 2601 */       exception.printStackTrace();
/*      */     } 
/* 2603 */     return paramString;
/*      */   }
/*      */   
/*      */   public void setFullparse(boolean paramBoolean) {
/* 2607 */     this.fullparse = paramBoolean;
/* 2608 */     this.detailheadrowmark = "detailtitle";
/*      */   }
/*      */ 
/*      */   
/*      */   public StringBuilder getTempHtml() {
/* 2613 */     return this.tempHtml;
/*      */   }
/*      */   
/*      */   public StringBuilder getTempCss() {
/* 2617 */     return this.tempCss;
/*      */   }
/*      */   
/*      */   public StringBuilder getTempScript() {
/* 2621 */     return this.tempScript;
/*      */   }
/*      */   
/*      */   public void setGenerateWfInfo(Map<String, Object> paramMap) {
/* 2625 */     this.generateWfInfo = paramMap;
/*      */   }
/*      */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/exceldesign/ParseExcelLayout.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */