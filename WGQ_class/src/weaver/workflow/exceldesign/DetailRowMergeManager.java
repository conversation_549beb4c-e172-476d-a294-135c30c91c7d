/*    */ package weaver.workflow.exceldesign;
/*    */ 
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.Util;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class DetailRowMergeManager
/*    */ {
/*    */   public String getMergeInfo(int paramInt1, int paramInt2) {
/* 13 */     if (paramInt1 <= 0 || paramInt2 < 0)
/* 14 */       return ""; 
/* 15 */     String str = "";
/* 16 */     RecordSet recordSet = new RecordSet();
/* 17 */     recordSet.executeQuery("select mergefields from workflow_nodeformgroup where nodeid=? and groupid=?", new Object[] { Integer.valueOf(paramInt1), Integer.valueOf(paramInt2) });
/* 18 */     if (recordSet.next())
/* 19 */       str = Util.null2String(recordSet.getString(1)); 
/* 20 */     return str;
/*    */   }
/*    */   
/*    */   public boolean saveMergeInfo(int paramInt1, int paramInt2, String paramString) {
/* 24 */     if (paramInt1 <= 0 || paramInt2 < 0)
/* 25 */       return false; 
/* 26 */     String str1 = "select count(*) from workflow_NodeFormGroup where nodeid=? and groupid=?";
/* 27 */     String str2 = "insert into workflow_NodeFormGroup(nodeid,groupid,mergefields) values(?,?,?)";
/* 28 */     String str3 = "update workflow_NodeFormGroup set mergefields=? where nodeid=? and groupid=?";
/* 29 */     RecordSet recordSet = new RecordSet();
/* 30 */     recordSet.executeQuery(str1, new Object[] { Integer.valueOf(paramInt1), Integer.valueOf(paramInt2) });
/* 31 */     recordSet.next();
/* 32 */     boolean bool = (recordSet.getInt(1) > 0) ? true : false;
/* 33 */     boolean bool1 = false;
/* 34 */     if (bool) {
/* 35 */       bool1 = recordSet.executeUpdate(str3, new Object[] { paramString, Integer.valueOf(paramInt1), Integer.valueOf(paramInt2) });
/*    */     } else {
/* 37 */       bool1 = recordSet.executeUpdate(str2, new Object[] { Integer.valueOf(paramInt1), Integer.valueOf(paramInt2), paramString });
/* 38 */     }  return bool1;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/exceldesign/DetailRowMergeManager.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */