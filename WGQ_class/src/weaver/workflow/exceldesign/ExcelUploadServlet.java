/*     */ package weaver.workflow.exceldesign;
/*     */ 
/*     */ import java.io.DataInputStream;
/*     */ import java.io.File;
/*     */ import java.io.FileOutputStream;
/*     */ import java.io.IOException;
/*     */ import java.io.InputStream;
/*     */ import javax.servlet.ServletException;
/*     */ import javax.servlet.http.HttpServlet;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import javax.servlet.http.HttpSession;
/*     */ import weaver.file.FileType;
/*     */ import weaver.general.GCONST;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.HrmUserVarify;
/*     */ import weaver.hrm.User;
/*     */ import weaver.page.PageManager;
/*     */ import weaver.workflow.workflow.UserWFOperateLevel;
/*     */ import weaver.workflow.workflow.WfRightManager;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class ExcelUploadServlet
/*     */   extends HttpServlet
/*     */ {
/*  35 */   private PageManager pm = new PageManager();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void doGet(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws ServletException, IOException {
/*  42 */     doPost(paramHttpServletRequest, paramHttpServletResponse);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void doPost(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws ServletException, IOException {
/*  49 */     String str = Util.null2String(paramHttpServletRequest.getParameter("method"));
/*     */     
/*  51 */     if ("uploadFile".equals(str) && verifyLimits(paramHttpServletRequest, paramHttpServletResponse)) {
/*  52 */       imageUpload(paramHttpServletRequest, paramHttpServletResponse);
/*     */     }
/*     */   }
/*     */   
/*     */   private boolean verifyLimits(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/*  57 */     int i = Util.getIntValue(paramHttpServletRequest.getParameter("workflowid"), -1);
/*  58 */     User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/*  59 */     if (i < 0 || user == null) {
/*  60 */       return false;
/*     */     }
/*  62 */     WfRightManager wfRightManager = new WfRightManager();
/*  63 */     boolean bool = wfRightManager.hasPermission3(i, 0, user, 1);
/*  64 */     String str = "WorkflowManage:All";
/*  65 */     if (!HrmUserVarify.checkUserRight(str, user) && !bool) {
/*  66 */       return false;
/*     */     }
/*  68 */     HttpSession httpSession = paramHttpServletRequest.getSession();
/*  69 */     int j = Util.getIntValue(String.valueOf(httpSession.getAttribute("detachable")), 0);
/*  70 */     int k = Util.getIntValue(Util.null2String(httpSession.getAttribute(i + "subcompanyid")), -1);
/*  71 */     int m = UserWFOperateLevel.checkUserWfOperateLevel(j, k, user, bool, str);
/*  72 */     if (m < 1)
/*  73 */       return false; 
/*  74 */     return true;
/*     */   }
/*     */   
/*     */   private boolean validateFileExt(String paramString) {
/*  78 */     if (paramString == null) return false; 
/*  79 */     if (paramString.indexOf(".") != paramString.lastIndexOf(".")) {
/*  80 */       return false;
/*     */     }
/*  82 */     String[] arrayOfString = { ".jpg", ".jpeg", ".gif", ".ico", ".bmp", ".png" };
/*  83 */     if (paramString != null && arrayOfString != null) {
/*  84 */       for (byte b = 0; b < arrayOfString.length; b++) {
/*  85 */         if (paramString.toLowerCase().endsWith(arrayOfString[b].toLowerCase())) {
/*  86 */           return true;
/*     */         }
/*     */       } 
/*  89 */       return false;
/*     */     } 
/*  91 */     return false;
/*     */   }
/*     */ 
/*     */   
/*     */   private void imageUpload(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/*  96 */     String str1 = Util.null2String(paramHttpServletRequest.getParameter("savefile"));
/*  97 */     String str2 = GCONST.getRootPath() + "filesystem/exceldesign/uploadimg/";
/*  98 */     DataInputStream dataInputStream = null;
/*  99 */     FileOutputStream fileOutputStream = null;
/* 100 */     String str3 = paramHttpServletRequest.getContentType();
/*     */     try {
/* 102 */       if (str3.indexOf("multipart/form-data") >= 0) {
/* 103 */         dataInputStream = new DataInputStream((InputStream)paramHttpServletRequest.getInputStream());
/* 104 */         int i = paramHttpServletRequest.getContentLength();
/* 105 */         byte[] arrayOfByte = new byte[i];
/* 106 */         int j = 0;
/* 107 */         int k = 0;
/* 108 */         while (k < i) {
/* 109 */           j = dataInputStream.read(arrayOfByte, k, i);
/*     */           
/* 111 */           k += j;
/*     */         } 
/* 113 */         String str4 = new String(arrayOfByte);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */         
/* 119 */         int m = str3.lastIndexOf("=");
/* 120 */         String str5 = str3.substring(m + 1, str3
/* 121 */             .length());
/* 122 */         String str6 = str2 + str1;
/* 123 */         str6 = str6.replaceAll("%00", "").replaceAll("%", "");
/*     */         
/* 125 */         if (!validateFileExt(str1))
/* 126 */           return;  if (Util.isExcuteFile(str1))
/*     */           return; 
/* 128 */         int n = str4.indexOf("filename=\"");
/* 129 */         n = str4.indexOf("\n", n) + 1;
/* 130 */         n = str4.indexOf("\n", n) + 1;
/* 131 */         n = str4.indexOf("\n", n) + 1;
/* 132 */         int i1 = str4.indexOf(str5, n) - 4;
/* 133 */         int i2 = (str4.substring(0, n).getBytes()).length;
/* 134 */         File file1 = new File(str6);
/* 135 */         if (file1.exists()) {
/*     */           return;
/*     */         }
/*     */         
/* 139 */         File file2 = new File(str2);
/* 140 */         if (!file2.exists()) {
/* 141 */           file2.mkdirs();
/*     */         }
/*     */         
/* 144 */         int i3 = (str4.substring(i1, str4.length()).getBytes()).length;
/*     */         try {
/* 146 */           byte[] arrayOfByte1 = new byte[arrayOfByte.length - i2 - i3];
/*     */           
/* 148 */           System.arraycopy(arrayOfByte, i2, arrayOfByte1, 0, arrayOfByte.length - i2 - i3);
/* 149 */           String str = FileType.getFileTypeByByte(arrayOfByte1);
/* 150 */           if (validateFileExt(str)) {
/* 151 */             fileOutputStream = new FileOutputStream(str6);
/* 152 */             fileOutputStream.write(arrayOfByte, i2, arrayOfByte.length - i2 - i3);
/*     */           } else {
/* 154 */             paramHttpServletResponse.getWriter().print("file type is not valid!");
/*     */           } 
/* 156 */         } catch (Exception exception) {
/* 157 */           exception.printStackTrace();
/*     */         } finally {
/*     */           try {
/* 160 */             if (fileOutputStream != null)
/* 161 */               fileOutputStream.close(); 
/* 162 */             if (dataInputStream != null)
/* 163 */               dataInputStream.close(); 
/* 164 */           } catch (Exception exception) {
/* 165 */             exception.printStackTrace();
/*     */           }
/*     */         
/*     */         } 
/*     */       } 
/* 170 */     } catch (Exception exception) {
/* 171 */       exception.printStackTrace();
/*     */     } finally {
/*     */       try {
/* 174 */         paramHttpServletResponse.getWriter().print("SUCCESS");
/* 175 */       } catch (Exception exception) {
/* 176 */         exception.printStackTrace();
/*     */       } 
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/exceldesign/ExcelUploadServlet.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */