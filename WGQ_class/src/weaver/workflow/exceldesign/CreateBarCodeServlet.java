/*     */ package weaver.workflow.exceldesign;
/*     */ 
/*     */ import com.engine.common.util.ParamUtil;
/*     */ import java.io.ByteArrayOutputStream;
/*     */ import java.io.IOException;
/*     */ import java.util.Map;
/*     */ import javax.servlet.ServletException;
/*     */ import javax.servlet.http.HttpServlet;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import javax.xml.transform.Transformer;
/*     */ import javax.xml.transform.TransformerFactory;
/*     */ import javax.xml.transform.dom.DOMSource;
/*     */ import javax.xml.transform.stream.StreamResult;
/*     */ import org.apache.avalon.framework.configuration.Configuration;
/*     */ import org.apache.avalon.framework.configuration.DefaultConfiguration;
/*     */ import org.apache.avalon.framework.logger.ConsoleLogger;
/*     */ import org.apache.avalon.framework.logger.Logger;
/*     */ import org.krysalis.barcode4j.BarcodeGenerator;
/*     */ import org.krysalis.barcode4j.BarcodeUtil;
/*     */ import org.krysalis.barcode4j.output.CanvasProvider;
/*     */ import org.krysalis.barcode4j.output.bitmap.BitmapCanvasProvider;
/*     */ import org.krysalis.barcode4j.output.eps.EPSCanvasProvider;
/*     */ import org.krysalis.barcode4j.output.svg.SVGCanvasProvider;
/*     */ import org.krysalis.barcode4j.tools.MimeTypes;
/*     */ import org.w3c.dom.DocumentFragment;
/*     */ import weaver.security.util.XxeMethodUtil;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CreateBarCodeServlet
/*     */   extends HttpServlet
/*     */ {
/*     */   private static final long serialVersionUID = -1612710758060435089L;
/*     */   public static final String BARCODE_MSG = "msg";
/*     */   public static final String BARCODE_CODE = "code";
/*     */   public static final String BARCODE_TYPE = "type";
/*     */   public static final String BARCODE_HEIGHT = "height";
/*     */   public static final String BARCODE_MODULE_WIDTH = "mw";
/*     */   public static final String BARCODE_WIDE_FACTOR = "wf";
/*     */   public static final String BARCODE_QUIET_ZONE = "qz";
/*     */   public static final String BARCODE_HUMAN_READABLE_POS = "hrp";
/*     */   public static final String BARCODE_FORMAT = "fmt";
/*     */   public static final String BARCODE_IMAGE_RESOLUTION = "res";
/*     */   public static final String BARCODE_IMAGE_GRAYSCALE = "gray";
/*     */   public static final String BARCODE_HUMAN_READABLE_SIZE = "hrsize";
/*     */   public static final String BARCODE_HUMAN_READABLE_FONT = "hrfont";
/*     */   public static final String BARCODE_HUMAN_READABLE_PATTERN = "hrpattern";
/*  89 */   private transient Logger log = (Logger)new ConsoleLogger(1);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   protected void doGet(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws ServletException, IOException {
/*  97 */     Map<String, Object> map = ParamUtil.request2Map(paramHttpServletRequest);
/*  98 */     String str = determineFormat(map);
/*  99 */     ByteArrayOutputStream byteArrayOutputStream = getOutputStream(map);
/* 100 */     paramHttpServletResponse.setContentType(str);
/* 101 */     paramHttpServletResponse.setContentLength(byteArrayOutputStream.size());
/* 102 */     paramHttpServletResponse.getOutputStream().write(byteArrayOutputStream.toByteArray());
/* 103 */     paramHttpServletResponse.getOutputStream().flush();
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public ByteArrayOutputStream getOutputStream(Map<String, Object> paramMap) throws ServletException {
/* 109 */     String str = determineFormat(paramMap);
/* 110 */     ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream(4096);
/*     */     try {
/* 112 */       boolean bool = false;
/* 113 */       Configuration configuration = buildCfg(paramMap);
/* 114 */       String str1 = (String)paramMap.get("msg");
/* 115 */       String str2 = (String)paramMap.get("code");
/* 116 */       str1 = replaceSpecial(str1);
/* 117 */       if (str1 == null) {
/* 118 */         if (str2 != null) {
/* 119 */           str1 = str2;
/*     */         } else {
/* 121 */           str1 = "**********";
/*     */         } 
/*     */       }
/*     */       
/* 125 */       BarcodeUtil barcodeUtil = BarcodeUtil.getInstance();
/* 126 */       BarcodeGenerator barcodeGenerator = barcodeUtil.createBarcodeGenerator(configuration);
/*     */       try {
/* 128 */         if (str.equals("image/svg+xml")) {
/*     */           
/* 130 */           SVGCanvasProvider sVGCanvasProvider = new SVGCanvasProvider(false, bool);
/* 131 */           barcodeGenerator.generateBarcode((CanvasProvider)sVGCanvasProvider, str1);
/* 132 */           DocumentFragment documentFragment = sVGCanvasProvider.getDOMFragment();
/*     */ 
/*     */           
/* 135 */           TransformerFactory transformerFactory = TransformerFactory.newInstance();
/* 136 */           XxeMethodUtil.setTransformerFeature(transformerFactory);
/* 137 */           Transformer transformer = transformerFactory.newTransformer();
/* 138 */           DOMSource dOMSource = new DOMSource(documentFragment);
/* 139 */           StreamResult streamResult = new StreamResult(byteArrayOutputStream);
/* 140 */           transformer.transform(dOMSource, streamResult);
/* 141 */         } else if (str.equals("image/x-eps")) {
/* 142 */           EPSCanvasProvider ePSCanvasProvider = new EPSCanvasProvider(byteArrayOutputStream, bool);
/* 143 */           barcodeGenerator.generateBarcode((CanvasProvider)ePSCanvasProvider, str1);
/* 144 */           ePSCanvasProvider.finish();
/*     */         } else {
/* 146 */           String str3 = (String)paramMap.get("res");
/* 147 */           int i = 300;
/* 148 */           if (str3 != null) {
/* 149 */             i = Integer.parseInt(str3);
/*     */           }
/* 151 */           if (i > 2400) {
/* 152 */             throw new IllegalArgumentException("Resolutions above 2400dpi are not allowed");
/*     */           }
/*     */           
/* 155 */           if (i < 10) {
/* 156 */             throw new IllegalArgumentException("Minimum resolution must be 10dpi");
/*     */           }
/*     */           
/* 159 */           String str4 = (String)paramMap.get("gray");
/* 160 */           BitmapCanvasProvider bitmapCanvasProvider = "true".equalsIgnoreCase(str4) ? new BitmapCanvasProvider(byteArrayOutputStream, str, i, 10, true, bool) : new BitmapCanvasProvider(byteArrayOutputStream, str, i, 12, false, bool);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */           
/* 167 */           barcodeGenerator.generateBarcode((CanvasProvider)bitmapCanvasProvider, str1);
/* 168 */           bitmapCanvasProvider.finish();
/*     */         } 
/*     */       } finally {
/* 171 */         byteArrayOutputStream.close();
/*     */       } 
/* 173 */     } catch (Exception exception) {
/* 174 */       this.log.error("Error while generating barcode", exception);
/* 175 */       throw new ServletException(exception);
/* 176 */     } catch (Throwable throwable) {
/* 177 */       this.log.error("Error while generating barcode", throwable);
/* 178 */       throw new ServletException(throwable);
/*     */     } 
/* 180 */     return byteArrayOutputStream;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   protected String determineFormat(Map<String, Object> paramMap) {
/* 188 */     String str = (String)paramMap.get("fmt");
/* 189 */     str = MimeTypes.expandFormat(str);
/* 190 */     if (str == null) {
/* 191 */       str = "image/jpeg";
/*     */     }
/* 193 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   protected Configuration buildCfg(Map<String, Object> paramMap) {
/* 203 */     DefaultConfiguration defaultConfiguration1 = new DefaultConfiguration("barcode");
/*     */     
/* 205 */     String str1 = (String)paramMap.get("type");
/* 206 */     if (str1 == null) {
/* 207 */       str1 = "code128";
/*     */     }
/* 209 */     DefaultConfiguration defaultConfiguration2 = new DefaultConfiguration(str1);
/* 210 */     defaultConfiguration1.addChild((Configuration)defaultConfiguration2);
/*     */ 
/*     */     
/* 213 */     String str2 = (String)paramMap.get("height");
/* 214 */     if (str2 != null) {
/* 215 */       DefaultConfiguration defaultConfiguration = new DefaultConfiguration("height");
/* 216 */       defaultConfiguration.setValue(str2);
/* 217 */       defaultConfiguration2.addChild((Configuration)defaultConfiguration);
/*     */     } 
/* 219 */     String str3 = (String)paramMap.get("mw");
/* 220 */     if (str3 != null) {
/* 221 */       DefaultConfiguration defaultConfiguration = new DefaultConfiguration("module-width");
/* 222 */       defaultConfiguration.setValue(str3);
/* 223 */       defaultConfiguration2.addChild((Configuration)defaultConfiguration);
/*     */     } 
/* 225 */     String str4 = (String)paramMap.get("wf");
/* 226 */     if (str4 != null) {
/* 227 */       DefaultConfiguration defaultConfiguration = new DefaultConfiguration("wide-factor");
/* 228 */       defaultConfiguration.setValue(str4);
/* 229 */       defaultConfiguration2.addChild((Configuration)defaultConfiguration);
/*     */     } 
/* 231 */     String str5 = (String)paramMap.get("qz");
/* 232 */     if (str5 != null) {
/* 233 */       DefaultConfiguration defaultConfiguration = new DefaultConfiguration("quiet-zone");
/* 234 */       if (str5.startsWith("disable")) {
/* 235 */         defaultConfiguration.setAttribute("enabled", "false");
/*     */       } else {
/* 237 */         defaultConfiguration.setValue(str5);
/*     */       } 
/* 239 */       defaultConfiguration2.addChild((Configuration)defaultConfiguration);
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 245 */     String str6 = (String)paramMap.get("hrp");
/* 246 */     String str7 = (String)paramMap.get("hrpattern");
/* 247 */     String str8 = (String)paramMap.get("hrsize");
/* 248 */     String str9 = (String)paramMap.get("hrfont");
/*     */     
/* 250 */     if (str6 != null || str7 != null || str8 != null || str9 != null) {
/*     */ 
/*     */ 
/*     */       
/* 254 */       DefaultConfiguration defaultConfiguration = new DefaultConfiguration("human-readable");
/*     */ 
/*     */       
/* 257 */       if (str7 != null) {
/* 258 */         DefaultConfiguration defaultConfiguration3 = new DefaultConfiguration("pattern");
/* 259 */         defaultConfiguration3.setValue(str7);
/* 260 */         defaultConfiguration.addChild((Configuration)defaultConfiguration3);
/*     */       } 
/* 262 */       if (str8 != null) {
/* 263 */         DefaultConfiguration defaultConfiguration3 = new DefaultConfiguration("font-size");
/* 264 */         defaultConfiguration3.setValue(str8);
/* 265 */         defaultConfiguration.addChild((Configuration)defaultConfiguration3);
/*     */       } 
/* 267 */       if (str9 != null) {
/* 268 */         DefaultConfiguration defaultConfiguration3 = new DefaultConfiguration("font-name");
/* 269 */         defaultConfiguration3.setValue(str9);
/* 270 */         defaultConfiguration.addChild((Configuration)defaultConfiguration3);
/*     */       } 
/* 272 */       if (str6 != null) {
/* 273 */         DefaultConfiguration defaultConfiguration3 = new DefaultConfiguration("placement");
/* 274 */         defaultConfiguration3.setValue(str6);
/* 275 */         defaultConfiguration.addChild((Configuration)defaultConfiguration3);
/*     */       } 
/*     */       
/* 278 */       defaultConfiguration2.addChild((Configuration)defaultConfiguration);
/*     */     } 
/*     */     
/* 281 */     return (Configuration)defaultConfiguration1;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   private String replaceSpecial(String paramString) {
/* 287 */     paramString = paramString.replace("p_weaver_p", "%");
/* 288 */     paramString = paramString.replace("n_weaver_n", "#");
/* 289 */     paramString = paramString.replace("li_weaver_li", "<");
/* 290 */     paramString = paramString.replace("b_weaver_b", ">");
/* 291 */     paramString = paramString.replace("and_weaver_and", "&");
/* 292 */     paramString = paramString.replace("eq_weaver_eq", "=");
/* 293 */     paramString = paramString.replace("add_weaver_add", "+");
/* 294 */     return paramString;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/exceldesign/CreateBarCodeServlet.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */