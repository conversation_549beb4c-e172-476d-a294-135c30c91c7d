/*     */ package weaver.workflow.exceldesign;
/*     */ 
/*     */ import java.io.ByteArrayInputStream;
/*     */ import java.io.File;
/*     */ import java.io.FileOutputStream;
/*     */ import java.io.InputStream;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Enumeration;
/*     */ import java.util.Hashtable;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import weaver.file.FileManage;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.GCONST;
/*     */ import weaver.general.Util;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class JSFileUploadToPath
/*     */   extends BaseBean
/*     */ {
/*  26 */   private String filePath = "";
/*  27 */   private String fileName = "";
/*     */   
/*  29 */   private Hashtable mpdata = null;
/*  30 */   HttpServletRequest request = null;
/*  31 */   private String[] filenames = null;
/*  32 */   private ByteArrayInputStream source = null;
/*  33 */   private ArrayList filesizes = new ArrayList();
/*  34 */   private ArrayList imagewidth = new ArrayList();
/*  35 */   private ArrayList imageheight = new ArrayList();
/*     */   
/*     */   public JSFileUploadToPath(HttpServletRequest paramHttpServletRequest) {
/*  38 */     if (isMultipartData(paramHttpServletRequest)) this.mpdata = getAttachment(paramHttpServletRequest); 
/*  39 */     this.request = paramHttpServletRequest;
/*     */   }
/*     */   
/*     */   public void setFilePath(String paramString) {
/*  43 */     this.filePath = paramString;
/*     */   }
/*     */ 
/*     */   
/*     */   public Object getParameter(String paramString) {
/*  48 */     if (!isMultipartData(this.request)) return this.request.getParameter(paramString); 
/*  49 */     if (this.mpdata == null) return ""; 
/*  50 */     Object object = this.mpdata.get(paramString);
/*  51 */     if (object instanceof String[]) {
/*  52 */       String[] arrayOfString = (String[])this.mpdata.get(paramString);
/*  53 */       if (arrayOfString == null) return ""; 
/*  54 */       return arrayOfString[0];
/*  55 */     }  if (object instanceof Hashtable) {
/*  56 */       Hashtable hashtable = (Hashtable)this.mpdata.get(paramString);
/*  57 */       if (hashtable == null) return ""; 
/*  58 */       return hashtable;
/*     */     } 
/*  60 */     return null;
/*     */   }
/*     */   
/*     */   public String[] getParameters(String paramString) {
/*  64 */     if (!isMultipartData(this.request)) return this.request.getParameterValues(paramString); 
/*  65 */     if (this.mpdata == null) return null; 
/*  66 */     return (String[])this.mpdata.get(paramString);
/*     */   }
/*     */   
/*     */   public Enumeration getParameterNames() {
/*  70 */     return this.mpdata.keys();
/*     */   }
/*     */   
/*     */   public String getFileName() {
/*  74 */     return this.filenames[0];
/*     */   }
/*     */   
/*     */   public String[] getFileNames() {
/*  78 */     return this.filenames;
/*     */   }
/*     */   
/*     */   public void setFileNames(String[] paramArrayOfString) {
/*  82 */     this.filenames = paramArrayOfString;
/*     */   }
/*     */   
/*     */   public int getFileSize() {
/*  86 */     return Util.getIntValue(this.filesizes.get(0));
/*     */   }
/*     */   
/*     */   public int[] getFileSizes() {
/*  90 */     int[] arrayOfInt = new int[this.filesizes.size()];
/*  91 */     for (byte b = 0; b < this.filesizes.size(); ) { arrayOfInt[b] = Util.getIntValue((String)this.filesizes.get(b)); b++; }
/*  92 */      return arrayOfInt;
/*     */   }
/*     */   
/*     */   public void setFileSizes(ArrayList paramArrayList) {
/*  96 */     this.filesizes = paramArrayList;
/*     */   }
/*     */   
/*     */   public int getWidth() {
/* 100 */     return Util.getIntValue(this.imagewidth.get(0));
/*     */   }
/*     */   
/*     */   public int getHeight() {
/* 104 */     return Util.getIntValue(this.imageheight.get(0));
/*     */   }
/*     */   
/*     */   public int[] getWidths() {
/* 108 */     int[] arrayOfInt = new int[this.imagewidth.size()];
/* 109 */     for (byte b = 0; b < this.imagewidth.size(); ) { arrayOfInt[b] = Util.getIntValue((String)this.imagewidth.get(b)); b++; }
/* 110 */      return arrayOfInt;
/*     */   }
/*     */   public int[] getHeights() {
/* 113 */     int[] arrayOfInt = new int[this.imageheight.size()];
/* 114 */     for (byte b = 0; b < this.imageheight.size(); ) { arrayOfInt[b] = Util.getIntValue((String)this.imageheight.get(b)); b++; }
/* 115 */      return arrayOfInt;
/*     */   }
/*     */   
/*     */   public String uploadFiles(String paramString) {
/* 119 */     String[] arrayOfString1 = new String[1];
/* 120 */     arrayOfString1[0] = paramString;
/* 121 */     String[] arrayOfString2 = uploadFiles(arrayOfString1);
/* 122 */     return arrayOfString2[0];
/*     */   }
/*     */   
/*     */   public String[] uploadFiles(String[] paramArrayOfString) {
/* 126 */     if (this.mpdata == null) return null; 
/* 127 */     int i = paramArrayOfString.length;
/* 128 */     String[] arrayOfString = new String[i];
/* 129 */     this.filenames = new String[i];
/* 130 */     for (byte b = 0; b < i; b++) {
/* 131 */       Hashtable hashtable = (Hashtable)this.mpdata.get(paramArrayOfString[b]);
/* 132 */       if (hashtable != null) {
/* 133 */         this.filenames[b] = (String)hashtable.get("filename");
/* 134 */         if (this.filenames[b] != null && !this.filenames[b].equals("")) {
/* 135 */           String str1 = this.filenames[b].substring(this.filenames[b].lastIndexOf(".") + 1);
/* 136 */           String str2 = this.filenames[b].substring(0, this.filenames[b].lastIndexOf("."));
/* 137 */           arrayOfString[b] = saveFile(hashtable, str2, str1);
/*     */         } 
/*     */       } 
/*     */     } 
/* 141 */     return arrayOfString;
/*     */   }
/*     */ 
/*     */   
/*     */   private Hashtable getAttachment(HttpServletRequest paramHttpServletRequest) {
/* 146 */     if (isMultipartData(paramHttpServletRequest))
/*     */       
/* 148 */       try { MyMultiPartHelper myMultiPartHelper = new MyMultiPartHelper();
/* 149 */         String str = myMultiPartHelper.getBoundary(paramHttpServletRequest.getContentType());
/* 150 */         str = "--" + str;
/* 151 */         return myMultiPartHelper.parseMultiPart((InputStream)paramHttpServletRequest.getInputStream(), str, paramHttpServletRequest.getContentLength()); }
/* 152 */       catch (Exception exception) { writeLog(exception); return null; }
/* 153 */         return null;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   private String saveFile(Hashtable paramHashtable, String paramString1, String paramString2) {
/* 159 */     Object object = paramHashtable.get("content");
/* 160 */     String str = (String)paramHashtable.get("content-type");
/*     */     
/* 162 */     if (this.filePath.equals("")) this.filePath = GCONST.getRootPath() + "tempfile" + File.separatorChar; 
/* 163 */     FileManage.createDir(this.filePath);
/* 164 */     if (paramString2 == null) paramString2 = "xls"; 
/* 165 */     if (paramString1 == null) {
/* 166 */       this.fileName = this.filePath + "Excel" + Util.getRandom() + "." + paramString2;
/*     */     } else {
/* 168 */       this.fileName = this.filePath + paramString1 + "." + paramString2;
/*     */     } 
/*     */     
/* 171 */     File file = new File(this.fileName);
/* 172 */     FileOutputStream fileOutputStream = null;
/*     */ 
/*     */     
/*     */     try {
/* 176 */       this.source = (object instanceof String) ? new ByteArrayInputStream(((String)object).getBytes("ISO-8859-1")) : (ByteArrayInputStream)object;
/*     */     }
/* 178 */     catch (Exception exception) {
/* 179 */       this.source = (object instanceof String) ? new ByteArrayInputStream(((String)object).getBytes()) : (ByteArrayInputStream)object;
/*     */     } 
/*     */ 
/*     */     
/* 183 */     this.filesizes.add("" + this.source.available());
/*     */     
/*     */     try {
/* 186 */       if (!file.exists()) {
/* 187 */         file.createNewFile();
/*     */       }
/* 189 */       fileOutputStream = new FileOutputStream(file);
/*     */       
/* 191 */       byte[] arrayOfByte = new byte[1024];
/* 192 */       int i = 0;
/* 193 */       for (; (i = this.source.read(arrayOfByte)) > 0; fileOutputStream.write(arrayOfByte, 0, i));
/* 194 */       this.source.close();
/* 195 */       fileOutputStream.close();
/*     */     }
/* 197 */     catch (Exception exception) {
/*     */       
/* 199 */       writeLog(exception);
/*     */     } 
/*     */     
/* 202 */     return paramString1 + "." + paramString2;
/*     */   }
/*     */ 
/*     */   
/*     */   private boolean isMultipartData(HttpServletRequest paramHttpServletRequest) {
/* 207 */     return Util.null2String(paramHttpServletRequest.getContentType()).toLowerCase().startsWith("multipart/form-data");
/*     */   }
/*     */   
/*     */   public HttpServletRequest getRequest() {
/* 211 */     return this.request;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/exceldesign/JSFileUploadToPath.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */