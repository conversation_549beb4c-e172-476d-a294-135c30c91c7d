/*    */ package weaver.workflow.exceldesign;
/*    */ 
/*    */ import com.engine.common.util.ParamUtil;
/*    */ import com.google.zxing.BarcodeFormat;
/*    */ import com.google.zxing.EncodeHintType;
/*    */ import com.google.zxing.MultiFormatWriter;
/*    */ import com.google.zxing.client.j2se.MatrixToImageWriter;
/*    */ import com.google.zxing.common.BitMatrix;
/*    */ import com.google.zxing.qrcode.QRCodeWriter;
/*    */ import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;
/*    */ import java.io.ByteArrayOutputStream;
/*    */ import java.net.URLDecoder;
/*    */ import java.util.Hashtable;
/*    */ import java.util.Map;
/*    */ import javax.servlet.http.HttpServlet;
/*    */ import javax.servlet.http.HttpServletRequest;
/*    */ import javax.servlet.http.HttpServletResponse;
/*    */ import weaver.general.Util;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class CreateQRCodeServlet
/*    */   extends HttpServlet
/*    */ {
/*    */   private static final String FORMAT = "JPEG";
/*    */   
/*    */   protected void doGet(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/*    */     try {
/* 30 */       ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
/* 31 */       Map<String, Object> map = ParamUtil.request2Map(paramHttpServletRequest);
/* 32 */       byteArrayOutputStream = getOutputStream(byteArrayOutputStream, map);
/* 33 */       paramHttpServletResponse.setContentType("image/jpeg;charset=UTF-8");
/* 34 */       paramHttpServletResponse.setContentLength(byteArrayOutputStream.size());
/* 35 */       paramHttpServletResponse.getOutputStream().write(byteArrayOutputStream.toByteArray());
/* 36 */       paramHttpServletResponse.getOutputStream().flush();
/* 37 */       paramHttpServletResponse.getOutputStream().close();
/* 38 */     } catch (Exception exception) {
/* 39 */       exception.printStackTrace();
/*    */     } 
/*    */   }
/*    */   
/*    */   public ByteArrayOutputStream getOutputStream(ByteArrayOutputStream paramByteArrayOutputStream, Map<String, Object> paramMap) {
/* 44 */     String str1 = Util.null2String((String)paramMap.get("type"), "qr");
/* 45 */     String str2 = Util.null2String(paramMap.get("msg"));
/* 46 */     int i = Util.getIntValue((String)paramMap.get("width"), 200);
/* 47 */     int j = Util.getIntValue((String)paramMap.get("height"), 200);
/* 48 */     int k = Util.getIntValue((String)paramMap.get("margin"), 0);
/*    */     try {
/* 50 */       str2 = URLDecoder.decode(str2, "UTF-8");
/* 51 */       str2 = new String(str2.getBytes("UTF-8"), "ISO-8859-1");
/* 52 */       str2 = replaceSpecial(str2);
/* 53 */       Hashtable<Object, Object> hashtable = new Hashtable<>();
/* 54 */       hashtable.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.H);
/*    */       
/* 56 */       hashtable.put(EncodeHintType.MARGIN, Integer.valueOf(k));
/* 57 */       if ("qr".equals(str1)) {
/* 58 */         BitMatrix bitMatrix = (new QRCodeWriter()).encode(str2, BarcodeFormat.QR_CODE, i, j, hashtable);
/* 59 */         MatrixToImageWriter.writeToStream(bitMatrix, "JPEG", paramByteArrayOutputStream);
/* 60 */       } else if ("bar".equals(str1)) {
/* 61 */         BitMatrix bitMatrix = (new MultiFormatWriter()).encode(str2, BarcodeFormat.CODE_128, i - 20, j);
/* 62 */         MatrixToImageWriter.writeToStream(bitMatrix, "JPEG", paramByteArrayOutputStream);
/*    */       }
/*    */     
/* 65 */     } catch (Exception exception) {
/* 66 */       exception.printStackTrace();
/*    */     } 
/* 68 */     return paramByteArrayOutputStream;
/*    */   }
/*    */ 
/*    */   
/*    */   private String replaceSpecial(String paramString) {
/* 73 */     paramString = paramString.replace("p_weaver_p", "%");
/* 74 */     paramString = paramString.replace("n_weaver_n", "#");
/* 75 */     paramString = paramString.replace("li_weaver_li", "<");
/* 76 */     paramString = paramString.replace("b_weaver_b", ">");
/* 77 */     paramString = paramString.replace("and_weaver_and", "&");
/* 78 */     paramString = paramString.replace("eq_weaver_eq", "=");
/* 79 */     paramString = paramString.replace("add_weaver_add", "+");
/* 80 */     return paramString;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/exceldesign/CreateQRCodeServlet.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */