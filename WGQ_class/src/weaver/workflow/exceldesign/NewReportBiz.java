/*     */ package weaver.workflow.exceldesign;
/*     */ 
/*     */ import com.alibaba.druid.support.json.JSONUtils;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Iterator;
/*     */ import java.util.LinkedHashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.common.StringUtil;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class NewReportBiz
/*     */ {
/*     */   public static final String SYSTEM_FIELD = "systemfield";
/*     */   
/*     */   public Map<String, SearchConditionOption> getFormTableOptions(int paramInt1, int paramInt2, User paramUser) {
/*  36 */     LinkedHashMap<Object, Object> linkedHashMap = new LinkedHashMap<>();
/*  37 */     linkedHashMap.put("", new SearchConditionOption("", SystemEnv.getHtmlLabelName(332, paramUser.getLanguage())));
/*  38 */     linkedHashMap.put("systemfield", new SearchConditionOption("systemfield", SystemEnv.getHtmlLabelName(468, paramUser.getLanguage())));
/*  39 */     RecordSet recordSet = new RecordSet();
/*  40 */     if (paramInt2 == 0) {
/*  41 */       linkedHashMap.put("workflow_form", new SearchConditionOption("workflow_form", SystemEnv.getHtmlLabelName(21778, paramUser.getLanguage())));
/*  42 */       recordSet.executeQuery("select fieldid from workflow_formfield where formid = ?  and isdetail = '1' ", new Object[] { Integer.valueOf(paramInt1) });
/*  43 */       if (recordSet.next()) {
/*  44 */         linkedHashMap.put("workflow_formdetail", new SearchConditionOption("workflow_formdetail", SystemEnv.getHtmlLabelName(17463, paramUser.getLanguage())));
/*     */       }
/*     */     } else {
/*  47 */       recordSet.executeQuery("select tablename from workflow_bill where id = ?", new Object[] { Integer.valueOf(paramInt1) });
/*  48 */       if (recordSet.next()) {
/*  49 */         String str = recordSet.getString("tablename");
/*  50 */         linkedHashMap.put(str, new SearchConditionOption(str, SystemEnv.getHtmlLabelName(21778, paramUser.getLanguage())));
/*     */       } 
/*     */       
/*  53 */       recordSet.executeQuery("select tablename,orderid from workflow_billdetailtable where billid = ? order by orderid", new Object[] { Integer.valueOf(paramInt1) });
/*  54 */       while (recordSet.next()) {
/*  55 */         String str1 = recordSet.getString("tablename");
/*  56 */         String str2 = recordSet.getString("orderid");
/*  57 */         linkedHashMap.put(str1, new SearchConditionOption(str1, SystemEnv.getHtmlLabelName(17463, paramUser.getLanguage()) + str2));
/*     */       } 
/*     */     } 
/*  60 */     return (Map)linkedHashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<Integer, ReportFieldEntity> getFormFields(int paramInt1, int paramInt2, User paramUser, Map<String, SearchConditionOption> paramMap) {
/*  71 */     if (paramMap == null) paramMap = getFormTableOptions(paramInt1, paramInt2, paramUser); 
/*  72 */     Map<Integer, ReportFieldEntity> map1 = addSystemField(paramMap.get("systemfield"), paramUser);
/*  73 */     Map<Integer, ReportFieldEntity> map2 = addFormFields(paramInt1, paramInt2, paramUser, paramMap);
/*  74 */     if (map2 != null && map2.size() > 0) map1.putAll(map2); 
/*  75 */     return map1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getFieldids(ArrayList<ReportFieldEntity> paramArrayList, User paramUser) {
/*  86 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  94 */     String str1 = SystemEnv.getHtmlLabelName(468, paramUser.getLanguage());
/*     */     
/*  96 */     String str2 = SystemEnv.getHtmlLabelName(21778, paramUser.getLanguage());
/*     */     
/*  98 */     String str3 = SystemEnv.getHtmlLabelName(17463, paramUser.getLanguage());
/*  99 */     for (byte b = 0; paramArrayList != null && b < paramArrayList.size(); b++) {
/* 100 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 101 */       ReportFieldEntity reportFieldEntity = paramArrayList.get(b);
/* 102 */       String str4 = reportFieldEntity.getTabledesc();
/* 103 */       String str5 = reportFieldEntity.getLabel();
/* 104 */       String str6 = String.valueOf(reportFieldEntity.getId());
/* 105 */       if (StringUtil.isNotNull(new String[] { str4, str5 })) {
/* 106 */         hashMap.put("key", str4 + "." + str5);
/* 107 */         if (str1.equals(str4)) {
/* 108 */           hashMap.put("value", "sys." + str6);
/* 109 */         } else if (str2.equals(str4)) {
/* 110 */           hashMap.put("value", "main." + str6);
/* 111 */         } else if (str4.indexOf(str3) > -1) {
/* 112 */           String str = str4.replaceAll(str3, "");
/* 113 */           hashMap.put("value", "detail_" + str + "." + str6);
/*     */         } 
/* 115 */         arrayList.add(hashMap);
/*     */       } 
/*     */     } 
/*     */     
/* 119 */     return JSONUtils.toJSONString(arrayList).replaceAll("\"", "'");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<Integer, ReportFieldEntity> addFormFields(int paramInt1, int paramInt2, User paramUser, Map<String, SearchConditionOption> paramMap) {
/* 131 */     String str = getQueryFormFieldSQL(paramInt1, paramInt2);
/* 132 */     if ("".equals(str))
/* 133 */       return null; 
/* 134 */     if (paramMap == null) paramMap = getFormTableOptions(paramInt1, paramInt2, paramUser); 
/* 135 */     LinkedHashMap<Object, Object> linkedHashMap = new LinkedHashMap<>();
/* 136 */     RecordSet recordSet = new RecordSet();
/* 137 */     List<String> list1 = initNotGroupbyDbtype();
/* 138 */     List<String> list2 = loadNodeGroupbyFields(paramMap);
/* 139 */     recordSet.executeQuery(str, new Object[0]);
/* 140 */     while (recordSet.next()) {
/* 141 */       int i = recordSet.getInt("id");
/* 142 */       String str1 = (paramInt2 == 1) ? SystemEnv.getHtmlLabelName(recordSet.getInt("label"), paramUser.getLanguage()) : recordSet.getString("label");
/* 143 */       int j = recordSet.getInt("dsporder");
/* 144 */       int k = recordSet.getInt("viewtype");
/* 145 */       String str2 = recordSet.getString("dbtype");
/* 146 */       String str3 = Util.null2String(recordSet.getString("name")).toLowerCase();
/* 147 */       String str4 = (k == 0) ? recordSet.getString("maintablename") : recordSet.getString("detailtable");
/* 148 */       SearchConditionOption searchConditionOption = paramMap.get(str4);
/*     */       
/* 150 */       ReportFieldEntity reportFieldEntity = new ReportFieldEntity(i, str3, str1, j, str2, recordSet.getInt("httype"), recordSet.getInt("type"), k, searchConditionOption.getShowname(), searchConditionOption.getKey());
/* 151 */       reportFieldEntity.setGroupby("sqlserver".equals(recordSet.getDBType()) ? (!list2.contains(str3)) : (!list1.contains(str2)));
/* 152 */       linkedHashMap.put(Integer.valueOf(i), reportFieldEntity);
/*     */     } 
/* 154 */     return (Map)linkedHashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private List<String> loadNodeGroupbyFields(Map<String, SearchConditionOption> paramMap) {
/* 163 */     RecordSet recordSet = new RecordSet();
/* 164 */     ArrayList<String> arrayList = new ArrayList();
/* 165 */     if (paramMap == null || paramMap.size() == 0) return arrayList; 
/* 166 */     if ("sqlserver".equals(recordSet.getDBType())) {
/* 167 */       Iterator<String> iterator = paramMap.keySet().iterator();
/* 168 */       while (iterator.hasNext()) {
/* 169 */         String str = Util.null2String(iterator.next());
/* 170 */         if ("".equals(str) || "systemfield".equals(str))
/* 171 */           continue;  recordSet.executeQuery("select name from syscolumns  where id = OBJECT_ID(?) and xtype in (35,99,34)", new Object[] { str });
/* 172 */         while (recordSet.next()) {
/* 173 */           arrayList.add(Util.null2String(recordSet.getString(1)).toLowerCase());
/*     */         }
/*     */       } 
/*     */     } 
/* 177 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getQueryFormFieldSQL(int paramInt1, int paramInt2) {
/* 188 */     StringBuffer stringBuffer = new StringBuffer();
/* 189 */     if (paramInt2 == 0) {
/* 190 */       stringBuffer.append(" select workflow_formfield.fieldid      \tas id,                                             \t\n");
/* 191 */       stringBuffer.append(" \t\t   fieldname                        as name,                                            \n");
/* 192 */       stringBuffer.append("         workflow_fieldlable.fieldlable   as label,                                           \n");
/* 193 */       stringBuffer.append("         workflow_formfield.fieldorder    as dsporder,                                      \t\n");
/* 194 */       stringBuffer.append("         workflow_formdict.fielddbtype    as dbtype,                                          \n");
/* 195 */       stringBuffer.append("         workflow_formdict.fieldhtmltype  as httype,                                          \n");
/* 196 */       stringBuffer.append("         workflow_formdict.type \t\t\tas type,                                            \n");
/* 197 */       stringBuffer.append("         '0' \t\t\t\t\t\t\t\tas viewtype,                                        \n");
/* 198 */       stringBuffer.append("         '' \t\t\t\t\t\t\t\tas detailtable,                                     \n");
/* 199 */       stringBuffer.append("         'workflow_form' \t\t\t\t\tas maintablename                        \t\t\t\n");
/* 200 */       stringBuffer.append("    from workflow_formfield, workflow_formdict, workflow_fieldlable                           \n");
/* 201 */       stringBuffer.append("   where workflow_fieldlable.formid = workflow_formfield.formid                               \n");
/* 202 */       stringBuffer.append("     and workflow_fieldlable.isdefault = 1                                                    \n");
/* 203 */       stringBuffer.append("     and workflow_fieldlable.fieldid = workflow_formfield.fieldid                             \n");
/* 204 */       stringBuffer.append("     and workflow_formdict.id = workflow_formfield.fieldid                                    \n");
/* 205 */       stringBuffer.append("     and workflow_formfield.formid = " + paramInt1 + "                                           \n");
/* 206 */       stringBuffer.append("     and (workflow_formfield.isdetail <> '1' or workflow_formfield.isdetail is null)          \n");
/* 207 */       stringBuffer.append("  union  all                                                                                  \n");
/* 208 */       stringBuffer.append("  select workflow_formfield.fieldid \t\t\t\tas id,                                      \n");
/* 209 */       stringBuffer.append("         fieldname \t\t\t\t\t\t\t\tas name,                                    \n");
/* 210 */       stringBuffer.append("         workflow_fieldlable.fieldlable \t\t\tas label,                                   \n");
/* 211 */       stringBuffer.append("         workflow_formfield.fieldorder + 100 \t\tas dsporder,                              \t\n");
/* 212 */       stringBuffer.append("         workflow_formdictdetail.fielddbtype \t\tas dbtype,                                  \n");
/* 213 */       stringBuffer.append("         workflow_formdictdetail.fieldhtmltype \tas httype,                                  \n");
/* 214 */       stringBuffer.append("         workflow_formdictdetail.type \t\t\tas type,                                    \n");
/* 215 */       stringBuffer.append("         '1' \t\t\t\t\t\t\t\t\t\tas viewtype,                                \n");
/* 216 */       stringBuffer.append("         'workflow_formdetail' \t\t\t\t\tas detailtable,                             \n");
/* 217 */       stringBuffer.append("         '' \t\t\t\t\t\t\t\t\t\tas maintablename                            \n");
/* 218 */       stringBuffer.append("    from workflow_formfield, workflow_formdictdetail, workflow_fieldlable                     \n");
/* 219 */       stringBuffer.append("   where workflow_fieldlable.formid = workflow_formfield.formid                               \n");
/* 220 */       stringBuffer.append("     and workflow_fieldlable.isdefault = 1                                                    \n");
/* 221 */       stringBuffer.append("     and workflow_fieldlable.fieldid = workflow_formfield.fieldid                             \n");
/* 222 */       stringBuffer.append("     and workflow_formdictdetail.id = workflow_formfield.fieldid                              \n");
/* 223 */       stringBuffer.append("     and workflow_formfield.formid =" + paramInt1 + "                                            \n");
/* 224 */       stringBuffer.append("     and (workflow_formfield.isdetail = '1' or workflow_formfield.isdetail is not null)  \t\t\n");
/* 225 */       stringBuffer.append(" order by viewtype           \t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\n");
/* 226 */     } else if (paramInt2 == 1) {
/* 227 */       stringBuffer.append("    select wfbf.id            as id,              \n");
/* 228 */       stringBuffer.append("           wfbf.fieldname     as name,            \n");
/* 229 */       stringBuffer.append("           wfbf.fieldlabel    as label,           \n");
/* 230 */       stringBuffer.append("           wfbf.fielddbtype   as dbtype,          \n");
/* 231 */       stringBuffer.append("           wfbf.fieldhtmltype as httype,          \n");
/* 232 */       stringBuffer.append("           wfbf.type          as type,            \n");
/* 233 */       stringBuffer.append("           wfbf.dsporder      as dsporder,        \n");
/* 234 */       stringBuffer.append("           wfbf.viewtype      as viewtype,        \n");
/* 235 */       stringBuffer.append("           wfbf.detailtable   as detailtable,     \n");
/* 236 */       stringBuffer.append("           wfb.tablename   \tas maintablename    \n");
/* 237 */       stringBuffer.append("      from workflow_billfield wfbf                \n");
/* 238 */       stringBuffer.append("      left join workflow_bill wfb on wfbf.billid  = wfb.id \n");
/* 239 */       stringBuffer.append("     where wfbf.billid = " + paramInt1 + "\t\t\t\n");
/* 240 */       stringBuffer.append("     order by viewtype,detailtable,dsporder       \n");
/*     */     } 
/* 242 */     return stringBuffer.toString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private Map<Integer, ReportFieldEntity> addSystemField(SearchConditionOption paramSearchConditionOption, User paramUser) {
/* 250 */     LinkedHashMap<Object, Object> linkedHashMap = new LinkedHashMap<>();
/*     */     
/* 252 */     linkedHashMap.put(Integer.valueOf(-1), new ReportFieldEntity(-1, "requestname", SystemEnv.getHtmlLabelName(1334, paramUser.getLanguage()), -1, "varchar", 1, 1, 2, paramSearchConditionOption.getShowname(), paramSearchConditionOption.getKey(), true));
/*     */     
/* 254 */     linkedHashMap.put(Integer.valueOf(-2), new ReportFieldEntity(-2, "requestlevel", SystemEnv.getHtmlLabelName(15534, paramUser.getLanguage()), -1, "int", 5, 1, 2, paramSearchConditionOption.getShowname(), paramSearchConditionOption.getKey(), true));
/*     */     
/* 256 */     linkedHashMap.put(Integer.valueOf(-10), new ReportFieldEntity(-10, "creater", SystemEnv.getHtmlLabelName(882, paramUser.getLanguage()), -2, "int", 3, 1, 2, paramSearchConditionOption.getShowname(), paramSearchConditionOption.getKey(), true));
/*     */     
/* 258 */     linkedHashMap.put(Integer.valueOf(-11), new ReportFieldEntity(-11, "createdate", SystemEnv.getHtmlLabelName(722, paramUser.getLanguage()), -2, "varchar", 3, 2, 2, paramSearchConditionOption.getShowname(), paramSearchConditionOption.getKey(), true));
/*     */     
/* 260 */     linkedHashMap.put(Integer.valueOf(-12), new ReportFieldEntity(-12, "workflowid", SystemEnv.getHtmlLabelName(259, paramUser.getLanguage()), -2, "int", 3, -99991, 2, paramSearchConditionOption.getShowname(), paramSearchConditionOption.getKey(), true));
/*     */     
/* 262 */     linkedHashMap.put(Integer.valueOf(-13), new ReportFieldEntity(-13, "currentnodeid", SystemEnv.getHtmlLabelName(18564, paramUser.getLanguage()), -2, "int", -1, -1, 2, paramSearchConditionOption.getShowname(), paramSearchConditionOption.getKey(), true));
/*     */     
/* 264 */     linkedHashMap.put(Integer.valueOf(-14), new ReportFieldEntity(-14, "nooperator", SystemEnv.getHtmlLabelName(16354, paramUser.getLanguage()), -2, "varchar", -1, -1, 2, paramSearchConditionOption.getShowname(), paramSearchConditionOption.getKey(), false));
/*     */     
/* 266 */     linkedHashMap.put(Integer.valueOf(-15), new ReportFieldEntity(-15, "requeststatus", SystemEnv.getHtmlLabelName(19061, paramUser.getLanguage()), -2, "int", -1, -1, 2, paramSearchConditionOption.getShowname(), paramSearchConditionOption.getKey(), true));
/*     */     
/* 268 */     linkedHashMap.put(Integer.valueOf(-16), new ReportFieldEntity(-16, "filingdate", SystemEnv.getHtmlLabelName(3000, paramUser.getLanguage()), -2, "varchar", -1, -1, 2, paramSearchConditionOption.getShowname(), paramSearchConditionOption.getKey(), true));
/*     */     
/* 270 */     linkedHashMap.put(Integer.valueOf(-17), new ReportFieldEntity(-17, "signoption", SystemEnv.getHtmlLabelName(17614, paramUser.getLanguage()), -2, "long", -1, -1, 2, paramSearchConditionOption.getShowname(), paramSearchConditionOption.getKey(), false));
/*     */     
/* 272 */     linkedHashMap.put(Integer.valueOf(-18), new ReportFieldEntity(-18, "currentoperator", SystemEnv.getHtmlLabelName(20558, paramUser.getLanguage()), -1, "int", 5, 1, 2, paramSearchConditionOption.getShowname(), paramSearchConditionOption.getKey(), true));
/* 273 */     return (Map)linkedHashMap;
/*     */   }
/*     */   
/*     */   private List<String> initNotGroupbyDbtype() {
/* 277 */     ArrayList<String> arrayList = new ArrayList();
/* 278 */     arrayList.add("long");
/* 279 */     arrayList.add("clob");
/* 280 */     arrayList.add("text");
/* 281 */     return arrayList;
/*     */   }
/*     */   
/*     */   public static void main(String[] paramArrayOfString) {}
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/exceldesign/NewReportBiz.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */