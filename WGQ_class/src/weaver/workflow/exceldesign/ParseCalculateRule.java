/*     */ package weaver.workflow.exceldesign;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.regex.Matcher;
/*     */ import java.util.regex.Pattern;
/*     */ import net.sf.json.JSONObject;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class ParseCalculateRule
/*     */   extends BaseBean
/*     */ {
/*     */   public String parseRuleGroupByDetail(int paramInt1, int paramInt2) {
/*  22 */     ArrayList arrayList1 = new ArrayList();
/*  23 */     ArrayList arrayList2 = new ArrayList();
/*  24 */     ArrayList arrayList3 = new ArrayList();
/*  25 */     RecordSet recordSet1 = new RecordSet();
/*  26 */     recordSet1.executeSql("select * from workflow_formdetailinfo where formid=" + paramInt2);
/*  27 */     if (recordSet1.next()) {
/*  28 */       arrayList1 = Util.TokenizerString(recordSet1.getString("rowcalstr"), ";");
/*  29 */       arrayList2 = Util.TokenizerString(recordSet1.getString("colcalstr"), ";");
/*  30 */       arrayList3 = Util.TokenizerString(recordSet1.getString("maincalstr"), ";");
/*     */     } 
/*  32 */     RecordSet recordSet2 = new RecordSet();
/*  33 */     if (paramInt1 == 0) {
/*  34 */       recordSet2.executeSql("select distinct groupid from workflow_formfield where formid=" + paramInt2 + " and isdetail='1' order by groupid");
/*     */     } else {
/*  36 */       recordSet2.executeSql("select tablename from Workflow_billdetailtable where billid=" + paramInt2 + " order by orderid");
/*     */     } 
/*     */     
/*  39 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  40 */     byte b = 0;
/*  41 */     Pattern pattern = Pattern.compile("(detailfield_)(\\d+)");
/*  42 */     while (recordSet2.next()) {
/*  43 */       int i = b;
/*  44 */       if (paramInt1 == 0)
/*  45 */         i = Util.getIntValue(recordSet2.getString("groupid"), 0); 
/*  46 */       ArrayList<String> arrayList = new ArrayList();
/*  47 */       if (paramInt1 == 0) {
/*  48 */         recordSet1.executeSql("select fieldid from workflow_formfield where formid=" + paramInt2 + " and isdetail='1' and groupid=" + i);
/*     */       } else {
/*  50 */         recordSet1.executeSql("select id as fieldid from workflow_billfield where detailtable='" + recordSet2.getString(1) + "' ");
/*     */       } 
/*  52 */       while (recordSet1.next()) {
/*  53 */         arrayList.add(recordSet1.getString("fieldid"));
/*     */       }
/*  55 */       String str1 = "";
/*  56 */       String str2 = "";
/*  57 */       String str3 = "";
/*  58 */       for (String str : arrayList1) {
/*  59 */         Matcher matcher = pattern.matcher(str);
/*  60 */         if (matcher.find() && 
/*  61 */           arrayList.indexOf(matcher.group(2)) > -1) {
/*  62 */           str1 = str1 + str + ";";
/*     */         }
/*     */       } 
/*  65 */       for (String str : arrayList2) {
/*  66 */         Matcher matcher = pattern.matcher(str);
/*  67 */         if (matcher.find() && 
/*  68 */           arrayList.indexOf(matcher.group(2)) > -1) {
/*  69 */           str2 = str2 + str + ";";
/*     */         }
/*     */       } 
/*  72 */       for (String str : arrayList3) {
/*  73 */         Matcher matcher = pattern.matcher(str);
/*  74 */         if (matcher.find() && 
/*  75 */           arrayList.indexOf(matcher.group(2)) > -1) {
/*  76 */           str3 = str3 + str + ";";
/*     */         }
/*     */       } 
/*  79 */       if (str1.endsWith(";")) str1 = str1.substring(0, str1.length() - 1); 
/*  80 */       if (str2.endsWith(";")) str2 = str2.substring(0, str2.length() - 1); 
/*  81 */       if (str3.endsWith(";")) str3 = str3.substring(0, str3.length() - 1); 
/*  82 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  83 */       hashMap1.put("rowcal", str1);
/*  84 */       hashMap1.put("colcal", str2);
/*  85 */       hashMap1.put("maincal", str3);
/*  86 */       hashMap.put("detail_" + i, hashMap1);
/*  87 */       b++;
/*     */     } 
/*  89 */     return JSONObject.fromObject(hashMap).toString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public ArrayList<String> getFormSumFieldList(int paramInt) {
/*  98 */     ArrayList<String> arrayList = new ArrayList();
/*  99 */     RecordSet recordSet = new RecordSet();
/* 100 */     recordSet.executeSql("select colcalstr from workflow_formdetailinfo where formid=" + paramInt);
/* 101 */     if (recordSet.next())
/* 102 */       arrayList = Util.TokenizerString(recordSet.getString("colcalstr"), ";"); 
/* 103 */     return arrayList;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/exceldesign/ParseCalculateRule.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */