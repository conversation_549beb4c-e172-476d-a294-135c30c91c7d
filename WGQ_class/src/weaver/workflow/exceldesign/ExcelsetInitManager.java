/*      */ package weaver.workflow.exceldesign;
/*      */ 
/*      */ import com.engine.hrm.biz.HrmClassifiedProtectionBiz;
/*      */ import java.util.ArrayList;
/*      */ import java.util.HashMap;
/*      */ import java.util.Hashtable;
/*      */ import java.util.LinkedHashMap;
/*      */ import java.util.List;
/*      */ import java.util.Map;
/*      */ import javax.servlet.http.HttpServletRequest;
/*      */ import net.sf.json.JSONArray;
/*      */ import net.sf.json.JSONObject;
/*      */ import weaver.conn.RecordSet;
/*      */ import weaver.general.BaseBean;
/*      */ import weaver.general.Util;
/*      */ import weaver.hrm.User;
/*      */ import weaver.systeminfo.SystemEnv;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ public class ExcelsetInitManager
/*      */   extends BaseBean
/*      */ {
/*      */   private HttpServletRequest request;
/*      */   private User user;
/*      */   private Map fontmap;
/*      */   private Map fontmap_center;
/*      */   private List main_borderList;
/*      */   private List detail_borderList;
/*      */   private int excelStyle;
/*      */   private String isSys;
/*   38 */   private int main_row_height = 0;
/*   39 */   private int main_field_width = 0;
/*   40 */   private int main_field_width_select = 0;
/*   41 */   private int main_label_width = 0;
/*   42 */   private int main_label_width_select = 0;
/*   43 */   private int detail_row_height = 0;
/*   44 */   private int detail_col_width = 0;
/*   45 */   private int detail_col_width_select = 0;
/*   46 */   private String main_border = "";
/*   47 */   private String main_label_bgcolor = "";
/*   48 */   private String main_field_bgcolor = "";
/*   49 */   private String detail_border = "";
/*   50 */   private String detail_label_bgcolor = "";
/*   51 */   private String detail_field_bgcolor = "";
/*   52 */   private String main_style_base = "";
/*   53 */   private String detail_style_base = "";
/*      */   
/*      */   private boolean isFreeNodeHtml = false;
/*      */   
/*      */   private int configId;
/*      */   
/*      */   private Map<String, Object> params;
/*      */   
/*      */   private void initStyleVariable(int paramInt) {
/*   62 */     this.fontmap = new HashMap<>();
/*   63 */     this.fontmap.put("text-align", "left");
/*   64 */     this.fontmap.put("valign", "middle");
/*   65 */     this.fontmap.put("autoWrap", "true");
/*   66 */     this.fontmap_center = new HashMap<>();
/*   67 */     this.fontmap_center.put("text-align", "center");
/*   68 */     this.fontmap_center.put("valign", "middle");
/*   69 */     this.fontmap_center.put("autoWrap", "true");
/*   70 */     if (getIsSys().equals("cus")) {
/*   71 */       String str = "select * from excelStyleDec where id=" + this.excelStyle;
/*   72 */       RecordSet recordSet = new RecordSet();
/*   73 */       recordSet.executeSql(str);
/*   74 */       if (recordSet.next()) {
/*   75 */         this.main_row_height = Util.getIntValue(recordSet.getString("mainrowheight"), 0);
/*   76 */         this.main_field_width = Util.getIntValue(recordSet.getString("mainfieldwidth"), 0);
/*   77 */         this.main_field_width_select = Util.getIntValue(recordSet.getString("mainfieldwidthselect"), 0);
/*   78 */         this.main_label_width = Util.getIntValue(recordSet.getString("mainlblwidth"), 0);
/*   79 */         this.main_label_width_select = Util.getIntValue(recordSet.getString("mainlblwidthselect"), 0);
/*   80 */         this.detail_row_height = Util.getIntValue(recordSet.getString("detailrowheight"), 0);
/*   81 */         this.detail_col_width = Util.getIntValue(recordSet.getString("detailcolwidth"), 0);
/*   82 */         this.detail_col_width_select = Util.getIntValue(recordSet.getString("detailcolwidthselect"), 0);
/*   83 */         this.main_border = Util.null2String(recordSet.getString("mainborder"));
/*   84 */         this.main_label_bgcolor = Util.null2String(recordSet.getString("mainlblbgcolor"));
/*   85 */         this.main_field_bgcolor = Util.null2String(recordSet.getString("mainfieldbgcolor"));
/*   86 */         this.detail_border = Util.null2String(recordSet.getString("detailborder"));
/*   87 */         this.detail_label_bgcolor = Util.null2String(recordSet.getString("detaillblbgcolor"));
/*   88 */         this.detail_field_bgcolor = Util.null2String(recordSet.getString("detailfieldbgcolor"));
/*   89 */         this.main_borderList = new ArrayList();
/*   90 */         HashMap<Object, Object> hashMap = new HashMap<>();
/*   91 */         hashMap.put("kind", "top");
/*   92 */         hashMap.put("style", "1");
/*   93 */         hashMap.put("color", this.main_border);
/*   94 */         this.main_borderList.add(hashMap);
/*   95 */         hashMap = new HashMap<>();
/*   96 */         hashMap.put("kind", "left");
/*   97 */         hashMap.put("style", "1");
/*   98 */         hashMap.put("color", this.main_border);
/*   99 */         this.main_borderList.add(hashMap);
/*  100 */         hashMap = new HashMap<>();
/*  101 */         hashMap.put("kind", "right");
/*  102 */         hashMap.put("style", "1");
/*  103 */         hashMap.put("color", this.main_border);
/*  104 */         this.main_borderList.add(hashMap);
/*  105 */         hashMap = new HashMap<>();
/*  106 */         hashMap.put("kind", "bottom");
/*  107 */         hashMap.put("style", "1");
/*  108 */         hashMap.put("color", this.main_border);
/*  109 */         this.main_borderList.add(hashMap);
/*  110 */         this.detail_borderList = new ArrayList();
/*  111 */         hashMap = new HashMap<>();
/*  112 */         hashMap.put("kind", "top");
/*  113 */         hashMap.put("style", "1");
/*  114 */         hashMap.put("color", this.detail_border);
/*  115 */         this.detail_borderList.add(hashMap);
/*  116 */         hashMap = new HashMap<>();
/*  117 */         hashMap.put("kind", "left");
/*  118 */         hashMap.put("style", "1");
/*  119 */         hashMap.put("color", this.detail_border);
/*  120 */         this.detail_borderList.add(hashMap);
/*  121 */         hashMap = new HashMap<>();
/*  122 */         hashMap.put("kind", "right");
/*  123 */         hashMap.put("style", "1");
/*  124 */         hashMap.put("color", this.detail_border);
/*  125 */         this.detail_borderList.add(hashMap);
/*  126 */         hashMap = new HashMap<>();
/*  127 */         hashMap.put("kind", "bottom");
/*  128 */         hashMap.put("style", "1");
/*  129 */         hashMap.put("color", this.detail_border);
/*  130 */         this.detail_borderList.add(hashMap);
/*      */       } 
/*      */     } else {
/*  133 */       this.main_row_height = 30;
/*  134 */       this.detail_row_height = 30;
/*  135 */       this.detail_col_width = 120;
/*  136 */       this.main_field_width_select = 1;
/*  137 */       this.main_label_width_select = 1;
/*  138 */       this.detail_col_width_select = 1;
/*      */       
/*  140 */       this.main_label_width = 150;
/*  141 */       this.main_field_width = 300;
/*  142 */       if (paramInt == 1) {
/*  143 */         this.main_label_width = 230;
/*  144 */         this.main_field_width = 530;
/*  145 */       } else if (paramInt == 3) {
/*  146 */         this.main_label_width = 100;
/*  147 */         this.main_field_width = 260;
/*  148 */       } else if (paramInt == 4) {
/*  149 */         this.main_label_width = 85;
/*  150 */         this.main_field_width = 215;
/*      */       } 
/*  152 */       Map map = (new StyleManager()).getSingleSysStyle(this.excelStyle);
/*  153 */       this.main_border = map.get("bordercolor").toString();
/*  154 */       this.main_borderList = new ArrayList();
/*  155 */       HashMap<Object, Object> hashMap = new HashMap<>();
/*  156 */       hashMap.put("kind", "top");
/*  157 */       hashMap.put("style", "1");
/*  158 */       hashMap.put("color", this.main_border);
/*  159 */       this.main_borderList.add(hashMap);
/*  160 */       hashMap = new HashMap<>();
/*  161 */       hashMap.put("kind", "left");
/*  162 */       hashMap.put("style", "1");
/*  163 */       hashMap.put("color", this.main_border);
/*  164 */       this.main_borderList.add(hashMap);
/*  165 */       hashMap = new HashMap<>();
/*  166 */       hashMap.put("kind", "right");
/*  167 */       hashMap.put("style", "1");
/*  168 */       hashMap.put("color", this.main_border);
/*  169 */       this.main_borderList.add(hashMap);
/*  170 */       hashMap = new HashMap<>();
/*  171 */       hashMap.put("kind", "bottom");
/*  172 */       hashMap.put("style", "1");
/*  173 */       hashMap.put("color", this.main_border);
/*  174 */       this.main_borderList.add(hashMap);
/*      */       
/*  176 */       this.detail_border = this.main_border;
/*  177 */       this.detail_borderList = new ArrayList();
/*  178 */       this.detail_borderList.addAll(this.main_borderList);
/*      */       
/*  180 */       this.main_label_bgcolor = map.get("labelbgcolor").toString();
/*  181 */       this.detail_label_bgcolor = this.main_label_bgcolor;
/*      */     } 
/*  183 */     this.main_style_base = "\"borderLeft\": {\"color\": \"" + this.main_border + "\",\"style\": 1},\"borderTop\": {\"color\": \"" + this.main_border + "\",\"style\": 1}";
/*  184 */     this.main_style_base += ",\"borderRight\": {\"color\": \"" + this.main_border + "\",\"style\": 1},\"borderBottom\": {\"color\": \"" + this.main_border + "\",\"style\": 1}";
/*  185 */     this.main_style_base += ",\"locked\": true,\"vAlign\":1,\"wordWrap\":true";
/*  186 */     this.detail_style_base = "\"borderLeft\": {\"color\": \"" + this.detail_border + "\",\"style\": 1},\"borderTop\": {\"color\": \"" + this.detail_border + "\",\"style\": 1}";
/*  187 */     this.detail_style_base += ",\"borderRight\": {\"color\": \"" + this.detail_border + "\",\"style\": 1},\"borderBottom\": {\"color\": \"" + this.detail_border + "\",\"style\": 1}";
/*  188 */     this.detail_style_base += ",\"locked\": true,\"vAlign\":1,\"wordWrap\":true";
/*      */   }
/*      */   
/*      */   public int createSheetJson(int paramInt1, int paramInt2, int paramInt3) {
/*  192 */     return createSheetJson(paramInt1, paramInt2, paramInt3, 2);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public int createSheetJson(int paramInt1, int paramInt2, int paramInt3, int paramInt4) {
/*  199 */     int i = 0;
/*      */     try {
/*  201 */       if (this.request == null && this.params == null)
/*  202 */         return 0; 
/*  203 */       int j = (this.request != null) ? Util.getIntValue(this.request.getParameter("colsperrow"), 0) : Util.getIntValue(Util.null2String(this.params.get("colsperrow")), 0);
/*  204 */       if (paramInt1 == 2)
/*  205 */         j = 1; 
/*  206 */       int k = (this.request != null) ? Util.getIntValue(this.request.getParameter("wfid"), 0) : Util.getIntValue(Util.null2String(this.params.get("wfid")), 0);
/*  207 */       int m = (this.request != null) ? Util.getIntValue(this.request.getParameter("nodeid"), 0) : Util.getIntValue(Util.null2String(this.params.get("nodeid")), 0);
/*  208 */       int n = (this.request != null) ? Util.getIntValue(this.request.getParameter("formid"), 0) : Util.getIntValue(Util.null2String(this.params.get("formid")), 0);
/*  209 */       int i1 = (this.request != null) ? Util.getIntValue(this.request.getParameter("isbill"), 0) : Util.getIntValue(Util.null2String(this.params.get("isbill")), 0);
/*  210 */       boolean bool = FormTemplateManager.isFormVirtualNode(m);
/*  211 */       String[] arrayOfString = null;
/*  212 */       if (this.isFreeNodeHtml) {
/*  213 */         arrayOfString = FormTemplateManager.getFreeNodeFormParams(m, n, i1, "nf", false, this.configId);
/*      */       } else {
/*  215 */         arrayOfString = FormTemplateManager.getNodeFormParams(m, n, i1, "nf", false);
/*      */       } 
/*  217 */       RecordSet recordSet = new RecordSet();
/*      */       
/*  219 */       initStyleVariable(j);
/*  220 */       boolean bool1 = false;
/*  221 */       recordSet.executeSql("select fieldid from " + arrayOfString[0] + " where fieldid=-4 and isview=1 and " + arrayOfString[1]);
/*  222 */       if (recordSet.next())
/*  223 */         bool1 = true; 
/*  224 */       boolean bool2 = false;
/*  225 */       String str1 = "";
/*  226 */       if (bool) {
/*  227 */         str1 = (new FormTemplateManager()).getFormName(n, i1, this.user.getLanguage());
/*      */       } else {
/*  229 */         recordSet.executeSql("select id,chatsType,workflowname from workflow_base where id=" + k);
/*  230 */         if (recordSet.next()) {
/*  231 */           str1 = recordSet.getString("workflowname");
/*  232 */           if (recordSet.getInt("chatsType") == 1)
/*  233 */             bool2 = true; 
/*      */         } 
/*      */       } 
/*  236 */       str1 = str1.replace("\\", "\\\\");
/*      */       
/*  238 */       String str2 = "";
/*  239 */       String str3 = "";
/*  240 */       String str4 = "";
/*  241 */       Hashtable<Object, Object> hashtable = new Hashtable<>();
/*  242 */       if (i1 == 0) {
/*  243 */         if ("ORACLE".equalsIgnoreCase(recordSet.getDBType())) {
/*      */           
/*  245 */           str2 = "select nf.*, fd.fieldhtmltype as htmltype,fd.type, ff.isdetail, fl.fieldlable, ff.groupid, nvl(ff.fieldorder,-1) as fieldorder from " + arrayOfString[0] + " left join workflow_formfield ff on nf.fieldid=ff.fieldid and ff.formid=" + n + " left join workflow_fieldlable fl on fl.fieldid=nf.fieldid and fl.formid=" + n + " and fl.langurageid=" + this.user.getLanguage() + " left join workflow_formdict fd on nf.fieldid = fd.id where " + arrayOfString[1] + " and nf.isview=1 and nvl(ff.isdetail,0) !=1 and nf.fieldid!= -4 order by nf.orderid, fieldorder,nf.fieldid desc";
/*      */ 
/*      */           
/*  248 */           str3 = "select nf.*, fd.fieldhtmltype as htmltype,fd.type, ff.isdetail, fl.fieldlable, ff.groupid, nvl(ff.fieldorder,-1) as fieldorder from " + arrayOfString[0] + " left join workflow_formfield ff on nf.fieldid=ff.fieldid and ff.formid=" + n + " left join workflow_fieldlable fl on fl.fieldid=nf.fieldid and fl.formid=" + n + " and fl.langurageid=" + this.user.getLanguage() + " left join workflow_formdictdetail fd on nf.fieldid = fd.id where " + arrayOfString[1] + " and nf.isview=1 and nvl(ff.isdetail,0)>0 order by ff.groupid, nf.orderid, fieldorder,nf.fieldid desc";
/*      */         }
/*  250 */         else if (recordSet.getDBType().equals("mysql")) {
/*      */           
/*  252 */           str2 = "select nf.*, fd.fieldhtmltype as htmltype,fd.type, ff.isdetail, fl.fieldlable, ff.groupid, ifnull(ff.fieldorder,-1) as fieldorder from " + arrayOfString[0] + " left join workflow_formfield ff on nf.fieldid=ff.fieldid and ff.formid=" + n + " left join workflow_fieldlable fl on fl.fieldid=nf.fieldid and fl.formid=" + n + " and fl.langurageid=" + this.user.getLanguage() + " left join workflow_formdict fd on nf.fieldid = fd.id where " + arrayOfString[1] + " and nf.isview=1 and ifnull(ff.isdetail,0) !=1 and nf.fieldid!= -4 order by nf.orderid, fieldorder,nf.fieldid desc";
/*      */ 
/*      */           
/*  255 */           str3 = "select nf.*, fd.fieldhtmltype as htmltype,fd.type, ff.isdetail, fl.fieldlable, ff.groupid, ifnull(ff.fieldorder,-1) as fieldorder from " + arrayOfString[0] + " left join workflow_formfield ff on nf.fieldid=ff.fieldid and ff.formid=" + n + " left join workflow_fieldlable fl on fl.fieldid=nf.fieldid and fl.formid=" + n + " and fl.langurageid=" + this.user.getLanguage() + " left join workflow_formdictdetail fd on nf.fieldid = fd.id where " + arrayOfString[1] + " and nf.isview=1 and ifnull(ff.isdetail,0)>0 order by ff.groupid, nf.orderid, fieldorder,nf.fieldid desc";
/*      */         }
/*      */         else {
/*      */           
/*  259 */           str2 = "select nf.*, fd.fieldhtmltype as htmltype,fd.type, ff.isdetail, fl.fieldlable, ff.groupid from " + arrayOfString[0] + " left join workflow_formfield ff on nf.fieldid=ff.fieldid and ff.formid=" + n + " left join workflow_fieldlable fl on fl.fieldid=nf.fieldid and fl.formid=" + n + " and fl.langurageid=" + this.user.getLanguage() + " left join workflow_formdict fd on nf.fieldid = fd.id where " + arrayOfString[1] + " and nf.isview=1 and isnull(ff.isdetail,0) !=1  and nf.fieldid!= -4 order by ff.groupid, nf.orderid, ff.fieldorder,nf.fieldid desc";
/*      */ 
/*      */           
/*  262 */           str3 = "select nf.*, fd.fieldhtmltype as htmltype,fd.type, ff.isdetail, fl.fieldlable, ff.groupid from " + arrayOfString[0] + " left join workflow_formfield ff on nf.fieldid=ff.fieldid and ff.formid=" + n + " left join workflow_fieldlable fl on fl.fieldid=nf.fieldid and fl.formid=" + n + " and fl.langurageid=" + this.user.getLanguage() + " left join workflow_formdictdetail fd on nf.fieldid = fd.id where " + arrayOfString[1] + " and nf.isview=1 and isnull(ff.isdetail,0) >0 order by nf.orderid, ff.fieldorder,nf.fieldid desc";
/*      */         } 
/*      */         
/*  265 */         str4 = "select distinct groupid from workflow_formfield where formid=" + n + " and isdetail=1";
/*  266 */       } else if (i1 == 1) {
/*  267 */         if ("ORACLE".equalsIgnoreCase(recordSet.getDBType())) {
/*  268 */           str2 = "select nf.*, nvl(bf.viewtype,-1) as isdetail, bf.fieldhtmltype as htmltype,bf.type,bf.fieldlabel as fieldlable, detailtable as groupid, nvl(bf.dsporder,0) as dsporder from " + arrayOfString[0] + " left join workflow_billfield bf on nf.fieldid=bf.id and bf.billid=" + n + " where " + arrayOfString[1] + " and nf.isview=1 and nvl(bf.viewtype,-1) !=1 and nf.fieldid!= -4 order by nf.orderid, dsporder, isdetail, fieldid desc";
/*      */           
/*  270 */           str3 = "select nf.*, nvl(bf.viewtype,-1) as isdetail,bf.fieldhtmltype as htmltype,bf.type, bf.fieldlabel as fieldlable, detailtable as groupid, nvl(bf.dsporder,0) as dsporder from " + arrayOfString[0] + " left join workflow_billfield bf on nf.fieldid=bf.id and bf.billid=" + n + " where " + arrayOfString[1] + " and nf.isview=1 and nvl(bf.viewtype,-1) =1 order by detailtable ,nf.orderid, dsporder, isdetail, fieldid desc";
/*      */         }
/*  272 */         else if (recordSet.getDBType().equals("mysql")) {
/*  273 */           str2 = "select nf.*, ifnull(bf.viewtype,-1) as isdetail, bf.fieldhtmltype as htmltype,bf.type,bf.fieldlabel as fieldlable, detailtable as groupid, ifnull(bf.dsporder,0) as dsporder from " + arrayOfString[0] + " left join workflow_billfield bf on nf.fieldid=bf.id and bf.billid=" + n + " where " + arrayOfString[1] + " and nf.isview=1 and ifnull(bf.viewtype,-1) !=1 and nf.fieldid!= -4 order by nf.orderid, dsporder, isdetail, fieldid desc";
/*      */           
/*  275 */           str3 = "select nf.*, ifnull(bf.viewtype,-1) as isdetail,bf.fieldhtmltype as htmltype,bf.type, bf.fieldlabel as fieldlable, detailtable as groupid, ifnull(bf.dsporder,0) as dsporder from " + arrayOfString[0] + " left join workflow_billfield bf on nf.fieldid=bf.id and bf.billid=" + n + " where " + arrayOfString[1] + " and nf.isview=1 and ifnull(bf.viewtype,-1) =1 order by detailtable ,nf.orderid, dsporder, isdetail, fieldid desc";
/*      */         } else {
/*      */           
/*  278 */           str2 = "select nf.*, isnull(bf.viewtype,-1) as isdetail,bf.fieldhtmltype as htmltype,bf.type, bf.fieldlabel as fieldlable, detailtable as groupid, isnull(bf.dsporder,0) as dsporder from " + arrayOfString[0] + " left join workflow_billfield bf on nf.fieldid=bf.id and bf.billid=" + n + " where " + arrayOfString[1] + " and nf.isview=1 and isnull(bf.viewtype,-1) !=1 and nf.fieldid!= -4 order by nf.orderid, dsporder, isdetail, fieldid desc";
/*      */           
/*  280 */           str3 = "select nf.*, isnull(bf.viewtype,-1) as isdetail,bf.fieldhtmltype as htmltype,bf.type, bf.fieldlabel as fieldlable, detailtable as groupid, isnull(bf.dsporder,0) as dsporder from " + arrayOfString[0] + " left join workflow_billfield bf on nf.fieldid=bf.id and bf.billid=" + n + " where " + arrayOfString[1] + " and nf.isview=1 and isnull(bf.viewtype,-1) =1 order by detailtable, nf.orderid, dsporder, isdetail, fieldid desc";
/*      */         } 
/*      */         
/*  283 */         str4 = "select tablename from Workflow_billdetailtable where billid=" + n;
/*      */         
/*  285 */         byte b = 0;
/*  286 */         recordSet.execute("select tablename from Workflow_billdetailtable where billid=" + n + " order by orderid");
/*  287 */         while (recordSet.next()) {
/*  288 */           String str = Util.null2String(recordSet.getString("tablename"));
/*  289 */           if (!"".equals(str)) {
/*  290 */             hashtable.put(str, "" + b);
/*  291 */             b++;
/*      */           } 
/*      */         } 
/*      */       } 
/*      */       
/*  296 */       byte b1 = 1;
/*  297 */       int i2 = 0;
/*  298 */       int i3 = j * 2;
/*  299 */       ArrayList<LinkedHashMap<Object, Object>> arrayList = new ArrayList();
/*  300 */       LinkedHashMap<Object, Object> linkedHashMap1 = new LinkedHashMap<>();
/*  301 */       LinkedHashMap<Object, Object> linkedHashMap2 = new LinkedHashMap<>();
/*  302 */       LinkedHashMap<Object, Object> linkedHashMap3 = new LinkedHashMap<>();
/*  303 */       LinkedHashMap<Object, Object> linkedHashMap4 = new LinkedHashMap<>();
/*  304 */       boolean bool3 = true;
/*  305 */       recordSet.execute(str2);
/*  306 */       while (recordSet.next()) {
/*  307 */         int i7 = Util.getIntValue(recordSet.getString("fieldid"), 0);
/*  308 */         int i8 = Util.getIntValue(recordSet.getString("isview"), 0);
/*  309 */         int i9 = Util.getIntValue(recordSet.getString("isedit"), 0);
/*  310 */         int i10 = Util.getIntValue(recordSet.getString("ismandatory"), 0);
/*  311 */         boolean bool5 = (Util.getIntValue(recordSet.getString("isalonerow")) == 1) ? true : false;
/*  312 */         int i11 = Util.getIntValue(recordSet.getString("htmltype"), 0);
/*  313 */         int i12 = Util.getIntValue(recordSet.getString("type"), 0);
/*  314 */         String str7 = "";
/*  315 */         String str8 = "";
/*  316 */         if (i7 == -1) {
/*  317 */           str7 = SystemEnv.getHtmlLabelName(21192, this.user.getLanguage());
/*  318 */           str8 = "text";
/*  319 */         } else if (i7 == -2) {
/*  320 */           str7 = SystemEnv.getHtmlLabelName(15534, this.user.getLanguage());
/*  321 */           str8 = "radio";
/*  322 */         } else if (i7 == -3) {
/*  323 */           str7 = SystemEnv.getHtmlLabelName(17586, this.user.getLanguage());
/*  324 */           str8 = "radio";
/*  325 */         } else if (i7 == -5) {
/*  326 */           if (!bool2)
/*  327 */             continue;  str7 = SystemEnv.getHtmlLabelName(32812, this.user.getLanguage());
/*  328 */           str8 = "radio";
/*  329 */         } else if (i7 == -9) {
/*  330 */           str7 = SystemEnv.getHtmlLabelName(22308, this.user.getLanguage());
/*  331 */           str8 = "text";
/*  332 */         } else if (i7 == -10) {
/*  333 */           if (!HrmClassifiedProtectionBiz.isOpenClassification())
/*      */             continue; 
/*  335 */           str7 = SystemEnv.getHtmlLabelName(500520, this.user.getLanguage());
/*  336 */           str8 = "select";
/*      */         } else {
/*  338 */           if (i1 == 0) {
/*  339 */             str7 = Util.null2String(recordSet.getString("fieldlable"));
/*      */           } else {
/*  341 */             int i13 = Util.getIntValue(recordSet.getString("fieldlable"));
/*  342 */             str7 = SystemEnv.getHtmlLabelName(i13, this.user.getLanguage());
/*      */           } 
/*  344 */           if (i11 == 3) {
/*  345 */             if (i12 == 2) { str8 = "date"; }
/*  346 */             else if (i12 == 19) { str8 = "time"; }
/*  347 */             else { str8 = "browser"; } 
/*  348 */           } else if (i11 == 1) {
/*  349 */             str8 = "text";
/*  350 */           } else if (i11 == 2) {
/*  351 */             str8 = "textarea";
/*  352 */           } else if (i11 == 4) {
/*  353 */             str8 = "checkbox";
/*  354 */           } else if (i11 == 5) {
/*  355 */             str8 = "select";
/*  356 */           } else if (i11 == 6) {
/*  357 */             str8 = "affix";
/*  358 */           } else if (i11 == 7) {
/*  359 */             str8 = "link";
/*  360 */           } else if (i11 == 8) {
/*  361 */             str8 = "radio";
/*  362 */           } else if (i11 == 9) {
/*  363 */             str8 = "position";
/*      */           } 
/*  365 */         }  str7 = Util.toScreenForWorkflow(str7);
/*  366 */         byte b = 0;
/*  367 */         if (i10 == 1) {
/*  368 */           b = 3;
/*  369 */         } else if (i9 == 1) {
/*  370 */           b = 2;
/*  371 */         } else if (i8 == 1) {
/*  372 */           b = 1;
/*      */         } 
/*  374 */         String str9 = "\"backgroundImage\": \"/workflow/exceldesign/image/controls/" + str8 + b + "_wev8.png\",\"backgroundImageLayout\": 3,\"textIndent\": 3";
/*  375 */         if ((!bool3 && bool5) || i2 >= i3) {
/*  376 */           b1++;
/*  377 */           i2 = 0;
/*      */         } 
/*      */         
/*  380 */         linkedHashMap1 = new LinkedHashMap<>();
/*  381 */         linkedHashMap1.put("id", b1 + "," + i2);
/*  382 */         linkedHashMap1.put("backgroundColor", this.main_label_bgcolor);
/*  383 */         linkedHashMap1.put("etype", "2");
/*  384 */         linkedHashMap1.put("field", "" + i7);
/*  385 */         linkedHashMap1.put("font", this.fontmap);
/*  386 */         linkedHashMap1.put("etxtindent", "0.5");
/*  387 */         linkedHashMap1.put("eborder", this.main_borderList);
/*  388 */         linkedHashMap1.put("evalue", str7);
/*  389 */         arrayList.add(linkedHashMap1);
/*  390 */         linkedHashMap3 = new LinkedHashMap<>();
/*  391 */         linkedHashMap3.put("value", str7.replaceAll("\\\\", "\\\\\\\\"));
/*  392 */         linkedHashMap3.put("style", JSONObject.fromObject("{\"backColor\": \"" + this.main_label_bgcolor + "\",\"textIndent\": 0.5," + this.main_style_base + "}"));
/*  393 */         linkedHashMap2.put(b1 + "_" + i2, linkedHashMap3);
/*  394 */         i2++;
/*      */         
/*  396 */         linkedHashMap1 = new LinkedHashMap<>();
/*  397 */         linkedHashMap1.put("id", b1 + "," + i2);
/*  398 */         linkedHashMap1.put("backgroundColor", this.main_field_bgcolor);
/*  399 */         linkedHashMap1.put("etype", "3");
/*  400 */         linkedHashMap1.put("field", "" + i7);
/*  401 */         linkedHashMap1.put("fieldtype", str8);
/*  402 */         linkedHashMap1.put("font", this.fontmap);
/*  403 */         linkedHashMap1.put("etxtindent", "0.5");
/*  404 */         linkedHashMap1.put("eborder", this.main_borderList);
/*  405 */         linkedHashMap1.put("evalue", str7);
/*  406 */         linkedHashMap3 = new LinkedHashMap<>();
/*  407 */         linkedHashMap3.put("value", str7.replaceAll("\\\\", "\\\\\\\\"));
/*  408 */         linkedHashMap3.put("style", JSONObject.fromObject("{\"backColor\": \"" + this.main_field_bgcolor + "\"," + str9 + "," + this.main_style_base + "}"));
/*  409 */         linkedHashMap2.put(b1 + "_" + i2, linkedHashMap3);
/*  410 */         if (bool5) {
/*  411 */           if (j > 1) {
/*  412 */             linkedHashMap1.put("colspan", "" + (i3 - 1));
/*  413 */             linkedHashMap4.put(b1 + "", "{\"row\": " + b1 + ",\"rowCount\": 1,\"col\": " + i2 + ",\"colCount\": " + (i3 - 1) + "}");
/*      */           } 
/*  415 */           i2 += i3 - 1;
/*      */         } else {
/*  417 */           i2++;
/*      */         } 
/*  419 */         arrayList.add(linkedHashMap1);
/*  420 */         bool3 = false;
/*      */       } 
/*  422 */       byte b2 = b1;
/*  423 */       StringBuilder stringBuilder1 = new StringBuilder();
/*  424 */       for (byte b3 = 1; b3 <= b2; b3++) {
/*  425 */         LinkedHashMap<Object, Object> linkedHashMap = new LinkedHashMap<>();
/*  426 */         for (byte b = 0; b < i3; b++) {
/*  427 */           String str = b3 + "_" + b;
/*  428 */           if (linkedHashMap2.containsKey(str)) {
/*  429 */             linkedHashMap.put(b + "", linkedHashMap2.get(str));
/*      */           } else {
/*  431 */             String str7 = "";
/*  432 */             if (!linkedHashMap4.containsKey("" + b3)) {
/*  433 */               str7 = (b % 2 == 0) ? this.main_label_bgcolor : this.main_field_bgcolor;
/*  434 */               linkedHashMap1 = new LinkedHashMap<>();
/*  435 */               linkedHashMap1.put("id", b3 + "," + b);
/*  436 */               linkedHashMap1.put("backgroundColor", str7);
/*  437 */               linkedHashMap1.put("etype", "");
/*  438 */               linkedHashMap1.put("font", this.fontmap);
/*  439 */               linkedHashMap1.put("eborder", this.main_borderList);
/*  440 */               linkedHashMap1.put("evalue", "");
/*  441 */               arrayList.add(linkedHashMap1);
/*      */             } 
/*  443 */             linkedHashMap3 = new LinkedHashMap<>();
/*  444 */             linkedHashMap3.put("value", "");
/*  445 */             linkedHashMap3.put("style", JSONObject.fromObject("{\"backColor\": \"" + str7 + "\"," + this.main_style_base + "}"));
/*  446 */             linkedHashMap.put(b + "", linkedHashMap3);
/*      */           } 
/*      */         } 
/*      */         
/*  450 */         stringBuilder1.append(",\"" + b3 + "\":").append(JSONObject.fromObject(linkedHashMap).toString());
/*      */       } 
/*      */ 
/*      */ 
/*      */       
/*  455 */       LinkedHashMap<Object, Object> linkedHashMap5 = new LinkedHashMap<>();
/*  456 */       LinkedHashMap<Object, Object> linkedHashMap6 = new LinkedHashMap<>();
/*  457 */       LinkedHashMap<Object, Object> linkedHashMap7 = new LinkedHashMap<>();
/*  458 */       LinkedHashMap<Object, Object> linkedHashMap8 = new LinkedHashMap<>();
/*  459 */       LinkedHashMap<Object, Object> linkedHashMap9 = new LinkedHashMap<>();
/*  460 */       recordSet.executeSql(str3);
/*  461 */       while (recordSet.next()) {
/*  462 */         int i7 = Util.getIntValue(recordSet.getString("fieldid"), 0);
/*  463 */         int i8 = Util.getIntValue(recordSet.getString("isview"), 0);
/*  464 */         int i9 = Util.getIntValue(recordSet.getString("isedit"), 0);
/*  465 */         int i10 = Util.getIntValue(recordSet.getString("ismandatory"), 0);
/*  466 */         int i11 = Util.getIntValue(recordSet.getString("htmltype"), 0);
/*  467 */         int i12 = Util.getIntValue(recordSet.getString("type"), 0);
/*  468 */         String str = "";
/*  469 */         if (i1 == 0) {
/*  470 */           str = Util.null2String(recordSet.getString("fieldlable"));
/*      */         } else {
/*  472 */           int i13 = Util.getIntValue(recordSet.getString("fieldlable"));
/*  473 */           str = SystemEnv.getHtmlLabelName(i13, this.user.getLanguage());
/*      */         } 
/*  475 */         str = Util.toScreenForWorkflow(str);
/*  476 */         byte b = 0;
/*  477 */         if (i10 == 1) {
/*  478 */           b = 3;
/*  479 */         } else if (i9 == 1) {
/*  480 */           b = 2;
/*  481 */         } else if (i8 == 1) {
/*  482 */           b = 1;
/*  483 */         }  if (b > 0) {
/*  484 */           String str7 = "";
/*  485 */           if (i1 == 0) {
/*  486 */             str7 = "" + Util.getIntValue(recordSet.getString("groupid"), 0);
/*      */           } else {
/*  488 */             String str8 = "" + Util.null2String(recordSet.getString("groupid"));
/*  489 */             str7 = "" + Util.getIntValue((String)hashtable.get(str8), -1);
/*      */           } 
/*  491 */           linkedHashMap5.put("labelname" + i7, str);
/*  492 */           linkedHashMap6.put("fieldattr" + i7, Integer.valueOf(b));
/*  493 */           linkedHashMap8.put("htmltype" + i7, Integer.valueOf(i11));
/*  494 */           linkedHashMap9.put("type" + i7, Integer.valueOf(i12));
/*  495 */           ArrayList<String> arrayList3 = (ArrayList)linkedHashMap7.get("group" + str7);
/*  496 */           if (arrayList3 == null || arrayList3.size() == 0) {
/*  497 */             arrayList3 = new ArrayList();
/*      */           }
/*  499 */           arrayList3.add("" + i7);
/*  500 */           linkedHashMap7.put("group" + str7, arrayList3);
/*      */         } 
/*      */       } 
/*      */       
/*  504 */       recordSet.executeSql(str4);
/*  505 */       int i4 = recordSet.getCounts();
/*      */       
/*  507 */       int i5 = linkedHashMap7.size();
/*      */ 
/*      */       
/*  510 */       int i6 = b2 + i5 * 2 + 1;
/*  511 */       if (bool1)
/*  512 */         i6 += 2; 
/*  513 */       boolean bool4 = (i6 >= 20) ? (i6 + 1) : true;
/*      */ 
/*      */       
/*  516 */       LinkedHashMap<Object, Object> linkedHashMap10 = new LinkedHashMap<>();
/*  517 */       LinkedHashMap<Object, Object> linkedHashMap11 = new LinkedHashMap<>();
/*  518 */       for (byte b4 = 0; b4 < i6; b4++) {
/*  519 */         linkedHashMap11.put("row_" + b4, "" + this.main_row_height);
/*      */       }
/*  521 */       StringBuilder stringBuilder2 = new StringBuilder();
/*  522 */       stringBuilder2.append("{");
/*  523 */       stringBuilder2.append("\"main_sheet\":{");
/*  524 */       stringBuilder2.append("\"version\": \"2.0\",\"tabStripVisible\": false,\"allowDragDrop\": false,\"allowDragFill\": false,\"grayAreaBackColor\": \"white\",\"sheets\": {\"Sheet1\": {");
/*  525 */       stringBuilder2.append("\"name\":\"Sheet1\",");
/*  526 */       stringBuilder2.append("\"defaults\":{\"rowHeight\":" + this.main_row_height + ", \"colWidth\":62, \"rowHeaderColWidth\":40, \"colHeaderRowHeight\":20},");
/*  527 */       stringBuilder2.append("\"selections\": {");
/*  528 */       stringBuilder2.append("\"0\": { \"row\": 0,\"rowCount\": 1,\"col\": 0,\"colCount\": " + i3 + "}");
/*  529 */       stringBuilder2.append("},");
/*  530 */       stringBuilder2.append("\"columns\": [");
/*  531 */       String str5 = "";
/*  532 */       for (byte b5 = 0; b5 < i3; b5++) {
/*  533 */         if (b5 % 2 == 0) {
/*  534 */           if (this.main_label_width_select == 1) {
/*  535 */             linkedHashMap10.put("col_" + b5, "" + this.main_label_width);
/*  536 */             stringBuilder2.append("{\"size\":" + this.main_label_width + ",\"dirty\": true}");
/*  537 */           } else if (this.main_label_width_select == 2) {
/*  538 */             linkedHashMap10.put("col_" + b5, "" + this.main_label_width + "%");
/*  539 */             stringBuilder2.append("{\"size\":120,\"dirty\": true}");
/*  540 */             str5 = str5 + "\"" + b5 + "\":{\"value\":\"" + transColumnName(b5) + " (" + this.main_label_width + "%)\"},";
/*      */           }
/*      */         
/*  543 */         } else if (this.main_field_width_select == 1) {
/*  544 */           linkedHashMap10.put("col_" + b5, "" + this.main_field_width);
/*  545 */           stringBuilder2.append("{\"size\":" + this.main_field_width + ",\"dirty\": true}");
/*  546 */         } else if (this.main_field_width_select == 2) {
/*  547 */           linkedHashMap10.put("col_" + b5, "" + this.main_field_width + "%");
/*  548 */           stringBuilder2.append("{\"size\":150,\"dirty\": true}");
/*  549 */           str5 = str5 + "\"" + b5 + "\":{\"value\":\"" + transColumnName(b5) + " (" + this.main_field_width + "%)\"},";
/*      */         } 
/*      */         
/*  552 */         if (b5 < i3 - 1) stringBuilder2.append(","); 
/*      */       } 
/*  554 */       stringBuilder2.append("],");
/*  555 */       stringBuilder2.append("\"activeRow\": 0,\"activeCol\": 0,");
/*  556 */       stringBuilder2.append("\"gridline\": {\"color\": \"#D0D7E5\",\"showVerticalGridline\": true,\"showHorizontalGridline\": true},");
/*  557 */       stringBuilder2.append("\"allowDragDrop\": false,");
/*  558 */       stringBuilder2.append("\"allowDragFill\": false,");
/*  559 */       stringBuilder2.append("\"rowHeaderData\": {\"rowCount\": " + bool4 + ",\"defaultDataNode\": {\"style\": {\"foreColor\": \"black\"}}},");
/*  560 */       stringBuilder2.append("\"colHeaderData\": {\"colCount\": 10,");
/*  561 */       if (!"".equals(str5))
/*  562 */         stringBuilder2.append("\"dataTable\": {\"0\": {" + str5.substring(0, str5.length() - 1) + "}},"); 
/*  563 */       stringBuilder2.append("\"defaultDataNode\": {\"style\": {\"foreColor\": \"black\"}}},");
/*      */       
/*  565 */       ArrayList<JSONObject> arrayList1 = new ArrayList();
/*  566 */       arrayList1.add(JSONObject.fromObject("{\"row\": 0,\"rowCount\": 1,\"col\": 0,\"colCount\": " + i3 + "}"));
/*  567 */       for (Map.Entry<Object, Object> entry : linkedHashMap4.entrySet())
/*  568 */         arrayList1.add(JSONObject.fromObject(entry.getValue())); 
/*      */       byte b6;
/*  570 */       for (b6 = 1; b6 <= i5; b6++) {
/*  571 */         arrayList1.add(JSONObject.fromObject("{\"row\":" + (b2 + b6 * 2) + ",\"rowCount\": 1,\"col\": 0,\"colCount\": " + i3 + "}"));
/*      */       }
/*  573 */       if (bool1 && j > 1)
/*  574 */         arrayList1.add(JSONObject.fromObject("{\"row\":" + (b2 + i5 * 2 + 2) + ",\"rowCount\": 1,\"col\": 1,\"colCount\": " + (i3 - 1) + "}")); 
/*  575 */       stringBuilder2.append("\"spans\": ").append(arrayList1.toString()).append(",");
/*  576 */       stringBuilder2.append("\"data\": {");
/*  577 */       stringBuilder2.append("\"rowCount\": " + bool4 + ",");
/*  578 */       stringBuilder2.append("\"colCount\": 10,");
/*  579 */       stringBuilder2.append("\"dataTable\": {");
/*  580 */       stringBuilder2.append("\"0\":{");
/*      */       
/*  582 */       for (b6 = 0; b6 < i3; b6++) {
/*  583 */         if (b6 == 0) {
/*  584 */           stringBuilder2.append("\"" + b6 + "\":{\"value\":\"" + str1 + "\",\"style\":{\"hAlign\":1,\"vAlign\":1,\"font\":\"bold 14pt Microsoft YaHei\"}}");
/*  585 */           linkedHashMap1 = new LinkedHashMap<>();
/*  586 */           linkedHashMap1.put("id", "0,0");
/*  587 */           linkedHashMap1.put("etype", "1");
/*  588 */           linkedHashMap1.put("colspan", "" + i3);
/*  589 */           HashMap<Object, Object> hashMap = new HashMap<>();
/*  590 */           hashMap.put("text-align", "center");
/*  591 */           hashMap.put("valign", "middle");
/*  592 */           hashMap.put("bold", "true");
/*  593 */           hashMap.put("font-size", "14pt");
/*  594 */           linkedHashMap1.put("font", hashMap);
/*  595 */           linkedHashMap1.put("evalue", str1);
/*  596 */           arrayList.add(linkedHashMap1);
/*      */         } else {
/*  598 */           stringBuilder2.append("\"" + b6 + "\":{\"style\":{\"hAlign\":0,\"vAlign\":1}}");
/*  599 */         }  if (b6 < i3 - 1) stringBuilder2.append(","); 
/*      */       } 
/*  601 */       stringBuilder2.append("}");
/*      */       
/*  603 */       stringBuilder2.append(stringBuilder1);
/*      */       
/*  605 */       if (i4 > 0 && i5 > 0) {
/*  606 */         b6 = 0;
/*  607 */         byte b = 1;
/*  608 */         while (b6 < i4) {
/*  609 */           b6++;
/*  610 */           if (!linkedHashMap7.keySet().contains("group" + (b6 - 1)))
/*      */             continue; 
/*  612 */           stringBuilder2.append(",\"" + (b2 + b * 2) + "\":{");
/*  613 */           stringBuilder2.append("\"0\":{");
/*  614 */           stringBuilder2.append("\"value\": \"" + SystemEnv.getHtmlLabelName(19325, this.user.getLanguage()) + b6 + "\",");
/*  615 */           stringBuilder2.append("\"style\": {\"backColor\": \"" + this.main_label_bgcolor + "\"," + this.main_style_base + ",\"backgroundImage\": \"/workflow/exceldesign/image/shortBtn/detail/detailTable_wev8.png\",\"backgroundImageLayout\": 3,\"textIndent\": 2.5}");
/*  616 */           stringBuilder2.append("}");
/*  617 */           linkedHashMap1 = new LinkedHashMap<>();
/*  618 */           linkedHashMap1.put("id", (b2 + b * 2) + ",0");
/*  619 */           linkedHashMap1.put("etype", "7");
/*  620 */           linkedHashMap1.put("detail", "detail_" + b6);
/*  621 */           linkedHashMap1.put("backgroundColor", this.main_label_bgcolor);
/*  622 */           linkedHashMap1.put("colspan", "" + i3);
/*  623 */           linkedHashMap1.put("font", this.fontmap);
/*  624 */           linkedHashMap1.put("eborder", this.main_borderList);
/*  625 */           linkedHashMap1.put("evalue", SystemEnv.getHtmlLabelName(19325, this.user.getLanguage()) + b6);
/*  626 */           arrayList.add(linkedHashMap1);
/*      */           
/*  628 */           for (byte b7 = 1; b7 < i3; b7++) {
/*  629 */             stringBuilder2.append(",\"" + b7 + "\":{");
/*  630 */             stringBuilder2.append("\"value\": \"\",");
/*  631 */             stringBuilder2.append("\"style\": {" + this.main_style_base + "}");
/*  632 */             stringBuilder2.append("}");
/*      */           } 
/*  634 */           stringBuilder2.append("}");
/*  635 */           b++;
/*      */         } 
/*      */       } 
/*      */       
/*  639 */       if (bool1) {
/*  640 */         stringBuilder2.append(",\"" + (b2 + i5 * 2 + 2) + "\":{");
/*  641 */         stringBuilder2.append("\"0\":{");
/*  642 */         stringBuilder2.append("\"value\": \"" + SystemEnv.getHtmlLabelName(17614, this.user.getLanguage()) + "\",");
/*  643 */         stringBuilder2.append("\"style\": {\"backColor\": \"" + this.main_label_bgcolor + "\",\"textIndent\": 0.5," + this.main_style_base + "}");
/*  644 */         stringBuilder2.append("},");
/*  645 */         linkedHashMap1 = new LinkedHashMap<>();
/*  646 */         linkedHashMap1.put("id", (b2 + i5 * 2 + 2) + ",0");
/*  647 */         linkedHashMap1.put("backgroundColor", this.main_label_bgcolor);
/*  648 */         linkedHashMap1.put("etype", "2");
/*  649 */         linkedHashMap1.put("field", "-4");
/*  650 */         linkedHashMap1.put("font", this.fontmap);
/*  651 */         linkedHashMap1.put("etxtindent", "0.5");
/*  652 */         linkedHashMap1.put("eborder", this.main_borderList);
/*  653 */         linkedHashMap1.put("evalue", SystemEnv.getHtmlLabelName(17614, this.user.getLanguage()));
/*  654 */         arrayList.add(linkedHashMap1);
/*  655 */         stringBuilder2.append("\"1\":{");
/*  656 */         stringBuilder2.append("\"value\": \"" + SystemEnv.getHtmlLabelName(17614, this.user.getLanguage()) + "\",");
/*  657 */         stringBuilder2.append("\"style\": {\"backColor\": \"" + this.main_field_bgcolor + "\"," + this.main_style_base + ",\"backgroundImage\": \"/workflow/exceldesign/image/controls/textarea1_wev8.png\",\"backgroundImageLayout\": 3,\"textIndent\": 2.5}");
/*  658 */         stringBuilder2.append("}");
/*  659 */         linkedHashMap1 = new LinkedHashMap<>();
/*  660 */         linkedHashMap1.put("id", (b2 + i5 * 2 + 2) + ",1");
/*  661 */         linkedHashMap1.put("backgroundColor", this.main_field_bgcolor);
/*  662 */         linkedHashMap1.put("colspan", "" + (i3 - 1));
/*  663 */         linkedHashMap1.put("etype", "3");
/*  664 */         linkedHashMap1.put("field", "-4");
/*  665 */         linkedHashMap1.put("fieldtype", "1");
/*  666 */         linkedHashMap1.put("font", this.fontmap);
/*  667 */         linkedHashMap1.put("eborder", this.main_borderList);
/*  668 */         linkedHashMap1.put("evalue", SystemEnv.getHtmlLabelName(17614, this.user.getLanguage()));
/*  669 */         arrayList.add(linkedHashMap1);
/*      */         
/*  671 */         for (b6 = 2; b6 < i3; b6++) {
/*  672 */           stringBuilder2.append(",\"" + b6 + "\":{");
/*  673 */           stringBuilder2.append("\"value\": \"\",");
/*  674 */           stringBuilder2.append("\"style\": {" + this.main_style_base + "}");
/*  675 */           stringBuilder2.append("}");
/*      */         } 
/*  677 */         stringBuilder2.append("}");
/*      */       } 
/*  679 */       stringBuilder2.append("},");
/*  680 */       stringBuilder2.append("\"defaultDataNode\": {\"style\": {\"foreColor\": \"black\"}}");
/*  681 */       stringBuilder2.append("},");
/*  682 */       stringBuilder2.append("\"rowRangeGroup\": {\"itemsCount\": " + bool4 + "},\"colRangeGroup\": {\"itemsCount\": 10}");
/*  683 */       stringBuilder2.append("}");
/*  684 */       stringBuilder2.append("}}");
/*      */ 
/*      */ 
/*      */       
/*  688 */       ArrayList<String> arrayList2 = (new ParseCalculateRule()).getFormSumFieldList(n);
/*  689 */       LinkedHashMap<Object, Object> linkedHashMap12 = new LinkedHashMap<>();
/*  690 */       if (i5 > 0) {
/*  691 */         if (i1 == 0) {
/*  692 */           str2 = "select distinct groupid from workflow_formfield where formid=" + n + " and isdetail='1' order by groupid";
/*      */         } else {
/*  694 */           str2 = "select tablename as groupid, title from Workflow_billdetailtable where billid=" + n + " order by orderid";
/*  695 */         }  recordSet.execute(str2);
/*  696 */         byte b = 0;
/*  697 */         while (recordSet.next()) {
/*  698 */           String str7 = "";
/*  699 */           if (i1 == 0) {
/*  700 */             str7 = "" + Util.getIntValue(recordSet.getString("groupid"), 0);
/*      */           } else {
/*  702 */             str7 = "" + b;
/*      */           } 
/*  704 */           ArrayList<String> arrayList3 = (ArrayList)linkedHashMap7.get("group" + str7);
/*  705 */           if (arrayList3 == null || arrayList3.size() == 0) {
/*  706 */             b++;
/*      */             continue;
/*      */           } 
/*  709 */           boolean bool5 = false;
/*  710 */           for (String str : arrayList3) {
/*  711 */             if (arrayList2.indexOf("detailfield_" + str) > -1) {
/*  712 */               bool5 = true;
/*      */               break;
/*      */             } 
/*      */           } 
/*  716 */           stringBuilder2.append(",\"detail_" + (b + 1) + "_sheet\":{");
/*  717 */           stringBuilder2.append("\"version\": \"2.0\",\"tabStripVisible\": false,\"allowDragDrop\": false,\"allowDragFill\": false,\"grayAreaBackColor\": \"white\",\"sheets\": {\"Sheet1\": {");
/*  718 */           stringBuilder2.append("\"name\":\"Sheet1\",");
/*  719 */           stringBuilder2.append("\"defaults\":{\"rowHeight\":" + this.detail_row_height + ", \"colWidth\":62, \"rowHeaderColWidth\":40, \"colHeaderRowHeight\":20},");
/*  720 */           stringBuilder2.append("\"selections\": {");
/*  721 */           stringBuilder2.append("\"0\": { \"row\": 0,\"rowCount\": 1,\"col\": 0,\"colCount\": 1}");
/*  722 */           stringBuilder2.append("},");
/*  723 */           stringBuilder2.append("\"columns\": [");
/*      */           
/*  725 */           HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  726 */           HashMap<Object, Object> hashMap2 = new HashMap<>();
/*  727 */           HashMap<Object, Object> hashMap3 = new HashMap<>();
/*  728 */           HashMap<Object, Object> hashMap4 = new HashMap<>();
/*  729 */           ArrayList<LinkedHashMap<Object, Object>> arrayList4 = new ArrayList();
/*  730 */           ArrayList<LinkedHashMap<Object, Object>> arrayList5 = new ArrayList();
/*  731 */           ArrayList<LinkedHashMap<Object, Object>> arrayList6 = new ArrayList();
/*  732 */           ArrayList<LinkedHashMap<Object, Object>> arrayList7 = new ArrayList();
/*  733 */           ArrayList<LinkedHashMap<Object, Object>> arrayList8 = new ArrayList();
/*  734 */           HashMap<Object, Object> hashMap5 = new HashMap<>();
/*  735 */           String str8 = "";
/*  736 */           String str9 = "";
/*  737 */           int i7 = arrayList3.size() + 2;
/*  738 */           byte b7 = (arrayList3.size() > 4) ? 4 : arrayList3.size(); byte b8;
/*  739 */           for (b8 = 0; b8 < i7; b8++) {
/*      */             
/*  741 */             if (b8 == 0 || b8 == 1) {
/*  742 */               hashMap5.put("col_" + b8, "50");
/*  743 */               stringBuilder2.append("{\"size\":50,\"dirty\": true}");
/*      */             }
/*  745 */             else if (this.detail_col_width_select == 1) {
/*  746 */               hashMap5.put("col_" + b8, "" + this.detail_col_width);
/*  747 */               stringBuilder2.append("{\"size\":" + this.detail_col_width + ",\"dirty\": true}");
/*  748 */             } else if (this.detail_col_width_select == 2) {
/*  749 */               hashMap5.put("col_" + b8, "" + this.detail_col_width + "%");
/*  750 */               stringBuilder2.append("{\"size\":120,\"dirty\": true}");
/*  751 */               str8 = str8 + "\"" + b8 + "\":{\"value\":\"" + transColumnName(b8) + " (" + this.detail_col_width + "%)\"},";
/*      */             } 
/*      */             
/*  754 */             if (b8 < i7 - 1) stringBuilder2.append(",");
/*      */             
/*  756 */             LinkedHashMap<Object, Object> linkedHashMap = new LinkedHashMap<>();
/*      */             
/*  758 */             if (b8 <= i7 - b7) {
/*  759 */               String str10 = "{\"value\": \"\",\"style\": {\"borderTop\": {\"color\": \"" + this.detail_border + "\",\"style\": 1}}}";
/*  760 */               String str11 = "";
/*  761 */               if (b8 == i7 - b7) {
/*  762 */                 str10 = "{\"value\": \"\",\"style\": {\"backgroundImage\": \"/workflow/exceldesign/image/shortBtn/detail/de_btn_wev8.png\",\"backgroundImageLayout\": 3,\"borderTop\": {\"color\": \"" + this.detail_border + "\",\"style\": 1}}}";
/*  763 */                 str11 = "10";
/*  764 */                 linkedHashMap.put("colspan", b7 + "");
/*      */               } 
/*  766 */               hashMap1.put(b8 + "", JSONObject.fromObject(str10));
/*  767 */               linkedHashMap.put("id", "0," + b8);
/*  768 */               linkedHashMap.put("etype", str11);
/*  769 */               ArrayList<HashMap<Object, Object>> arrayList9 = new ArrayList();
/*  770 */               HashMap<Object, Object> hashMap = new HashMap<>();
/*  771 */               hashMap.put("kind", "top");
/*  772 */               hashMap.put("style", "1");
/*  773 */               hashMap.put("color", this.detail_border);
/*  774 */               arrayList9.add(hashMap);
/*  775 */               linkedHashMap.put("eborder", arrayList9);
/*  776 */               arrayList5.add(linkedHashMap);
/*      */             } 
/*  778 */             if (b8 == 0 || b8 == 1) {
/*  779 */               boolean bool6 = (b8 == 0) ? true : false;
/*      */               
/*  781 */               String str = "/workflow/exceldesign/image/shortBtn/detail/" + (bool6 ? "de_checkall" : "de_serialtitle") + "_wev8.png";
/*  782 */               str9 = this.detail_style_base + ",\"backColor\": \"" + this.detail_label_bgcolor + "\",\"backgroundImage\": \"" + str + "\", \"backgroundImageLayout\": 3,\"locked\": true,\"textIndent\": 2.5,\"hAlign\": 1";
/*  783 */               str9 = "{\"value\":\"" + SystemEnv.getHtmlLabelName(bool6 ? 556 : 15486, this.user.getLanguage()) + "\",\"style\":{" + str9 + "}}";
/*  784 */               hashMap2.put(b8 + "", JSONObject.fromObject(str9));
/*  785 */               linkedHashMap = new LinkedHashMap<>();
/*  786 */               linkedHashMap.put("id", "1," + b8);
/*  787 */               linkedHashMap.put("backgroundColor", this.detail_label_bgcolor);
/*  788 */               linkedHashMap.put("etype", bool6 ? "20" : "24");
/*  789 */               linkedHashMap.put("font", this.fontmap_center);
/*  790 */               linkedHashMap.put("eborder", this.detail_borderList);
/*  791 */               linkedHashMap.put("evalue", bool6 ? "" : SystemEnv.getHtmlLabelName(15486, this.user.getLanguage()));
/*  792 */               arrayList6.add(linkedHashMap);
/*      */               
/*  794 */               str = "/workflow/exceldesign/image/shortBtn/detail/" + (bool6 ? "de_checksingle" : "de_serialnum") + "_wev8.png";
/*  795 */               str9 = this.detail_style_base + ",\"backColor\": \"" + this.detail_field_bgcolor + "\",\"backgroundImage\": \"" + str + "\", \"backgroundImageLayout\": 3,\"textIndent\": 2.5,\"hAlign\": 1";
/*  796 */               str9 = "{\"value\":\"" + SystemEnv.getHtmlLabelName(bool6 ? 1426 : 15486, this.user.getLanguage()) + "\",\"style\":{" + str9 + "}}";
/*  797 */               hashMap3.put(b8 + "", JSONObject.fromObject(str9));
/*  798 */               linkedHashMap = new LinkedHashMap<>();
/*  799 */               linkedHashMap.put("id", "3," + b8);
/*  800 */               if (!this.detail_field_bgcolor.equals(""))
/*  801 */                 linkedHashMap.put("backgroundColor", this.detail_field_bgcolor); 
/*  802 */               linkedHashMap.put("etype", bool6 ? "21" : "22");
/*  803 */               linkedHashMap.put("font", this.fontmap_center);
/*  804 */               linkedHashMap.put("eborder", this.detail_borderList);
/*  805 */               arrayList7.add(linkedHashMap);
/*      */               
/*  807 */               if (bool5) {
/*  808 */                 str9 = this.detail_style_base + ",\"backColor\": \"" + this.detail_label_bgcolor + "\",\"hAlign\": 1";
/*  809 */                 str9 = "{\"value\":\"" + (bool6 ? SystemEnv.getHtmlLabelName(358, this.user.getLanguage()) : "") + "\",\"style\":{" + str9 + "}}";
/*  810 */                 hashMap4.put(b8 + "", JSONObject.fromObject(str9));
/*  811 */                 if (bool6) {
/*  812 */                   linkedHashMap = new LinkedHashMap<>();
/*  813 */                   linkedHashMap.put("id", "5," + b8);
/*  814 */                   linkedHashMap.put("colspan", "2");
/*  815 */                   linkedHashMap.put("backgroundColor", this.detail_label_bgcolor);
/*  816 */                   linkedHashMap.put("etype", "1");
/*  817 */                   linkedHashMap.put("font", this.fontmap_center);
/*  818 */                   linkedHashMap.put("eborder", this.detail_borderList);
/*  819 */                   linkedHashMap.put("evalue", SystemEnv.getHtmlLabelName(358, this.user.getLanguage()));
/*  820 */                   arrayList8.add(linkedHashMap);
/*      */                 } 
/*      */               } 
/*      */             } else {
/*  824 */               int i8 = Util.getIntValue(arrayList3.get(b8 - 2));
/*  825 */               String str10 = (String)linkedHashMap5.get("labelname" + i8);
/*  826 */               String str11 = str10.replaceAll("\\\\", "\\\\\\\\");
/*  827 */               int i9 = ((Integer)linkedHashMap6.get("fieldattr" + i8)).intValue();
/*  828 */               int i10 = ((Integer)linkedHashMap8.get("htmltype" + i8)).intValue();
/*  829 */               int i11 = ((Integer)linkedHashMap9.get("type" + i8)).intValue();
/*  830 */               String str12 = "";
/*  831 */               if (i10 == 3) {
/*  832 */                 if (i11 == 2) { str12 = "date"; }
/*  833 */                 else if (i11 == 19) { str12 = "time"; }
/*  834 */                 else { str12 = "browser"; } 
/*  835 */               } else if (i10 == 1) {
/*  836 */                 str12 = "text";
/*  837 */               } else if (i10 == 2) {
/*  838 */                 str12 = "textarea";
/*  839 */               } else if (i10 == 4) {
/*  840 */                 str12 = "checkbox";
/*  841 */               } else if (i10 == 5) {
/*  842 */                 str12 = "select";
/*  843 */               } else if (i10 == 6) {
/*  844 */                 str12 = "affix";
/*  845 */               } else if (i10 == 7) {
/*  846 */                 str12 = "link";
/*  847 */               } else if (i10 == 8) {
/*  848 */                 str12 = "radio";
/*  849 */               } else if (i10 == 9) {
/*  850 */                 str12 = "position";
/*      */               } 
/*  852 */               str9 = this.detail_style_base + ",\"backColor\": \"" + this.detail_label_bgcolor + "\",\"hAlign\": 1";
/*  853 */               str9 = "{\"value\":\"" + str11 + "\",\"style\":{" + str9 + "}}";
/*  854 */               hashMap2.put(b8 + "", JSONObject.fromObject(str9));
/*  855 */               linkedHashMap = new LinkedHashMap<>();
/*  856 */               linkedHashMap.put("id", "1," + b8);
/*  857 */               linkedHashMap.put("backgroundColor", this.detail_label_bgcolor);
/*  858 */               linkedHashMap.put("etype", "2");
/*  859 */               linkedHashMap.put("field", "" + i8);
/*  860 */               linkedHashMap.put("font", this.fontmap_center);
/*  861 */               linkedHashMap.put("eborder", this.detail_borderList);
/*  862 */               linkedHashMap.put("evalue", str10);
/*  863 */               arrayList6.add(linkedHashMap);
/*      */               
/*  865 */               str9 = this.detail_style_base + ",\"backColor\": \"" + this.detail_field_bgcolor + "\",\"backgroundImage\": \"/workflow/exceldesign/image/controls/" + str12 + i9 + "_wev8.png\", \"backgroundImageLayout\": 3,\"textIndent\": 3";
/*  866 */               str9 = "{\"value\":\"" + str11 + "\",\"style\":{" + str9 + "}}";
/*  867 */               hashMap3.put(b8 + "", JSONObject.fromObject(str9));
/*  868 */               linkedHashMap = new LinkedHashMap<>();
/*  869 */               linkedHashMap.put("id", "3," + b8);
/*  870 */               if (!this.detail_field_bgcolor.equals(""))
/*  871 */                 linkedHashMap.put("backgroundColor", this.detail_field_bgcolor); 
/*  872 */               linkedHashMap.put("etype", "3");
/*  873 */               linkedHashMap.put("field", "" + i8);
/*  874 */               linkedHashMap.put("fieldtype", str12);
/*  875 */               linkedHashMap.put("font", this.fontmap);
/*  876 */               linkedHashMap.put("etxtindent", "0.5");
/*  877 */               linkedHashMap.put("eborder", this.detail_borderList);
/*  878 */               linkedHashMap.put("evalue", str10);
/*  879 */               arrayList7.add(linkedHashMap);
/*      */               
/*  881 */               if (bool5) {
/*  882 */                 str9 = this.detail_style_base + ",\"backColor\": \"" + this.detail_label_bgcolor + "\",\"hAlign\": 1";
/*  883 */                 linkedHashMap = new LinkedHashMap<>();
/*  884 */                 linkedHashMap.put("id", "5," + b8);
/*  885 */                 linkedHashMap.put("backgroundColor", this.detail_label_bgcolor);
/*  886 */                 if (arrayList2.indexOf("detailfield_" + i8) > -1) {
/*  887 */                   str9 = str9 + ",\"backgroundImage\": \"/workflow/exceldesign/image/shortBtn/detail/detailSumField_wev8.png\", \"backgroundImageLayout\": 3,\"locked\": true,\"textIndent\": 2.5,\"foreColor\":\"#ff0000\"";
/*  888 */                   str9 = "{\"value\":\"" + str11 + "(" + SystemEnv.getHtmlLabelName(358, this.user.getLanguage()) + ")\",\"style\":{" + str9 + "}}";
/*  889 */                   linkedHashMap.put("etype", "19");
/*  890 */                   linkedHashMap.put("field", "" + i8);
/*  891 */                   linkedHashMap.put("evalue", str10 + "(" + SystemEnv.getHtmlLabelName(358, this.user.getLanguage()) + ")");
/*  892 */                   HashMap<Object, Object> hashMap = new HashMap<>();
/*  893 */                   hashMap.putAll(this.fontmap_center);
/*  894 */                   hashMap.put("color", "#ff0000");
/*  895 */                   linkedHashMap.put("font", hashMap);
/*      */                 } else {
/*  897 */                   str9 = "{\"value\":\"\",\"style\":{" + str9 + "}}";
/*  898 */                   linkedHashMap.put("etype", "");
/*  899 */                   linkedHashMap.put("font", this.fontmap_center);
/*      */                 } 
/*  901 */                 hashMap4.put(b8 + "", JSONObject.fromObject(str9));
/*  902 */                 linkedHashMap.put("eborder", this.detail_borderList);
/*  903 */                 arrayList8.add(linkedHashMap);
/*      */               } 
/*      */             } 
/*      */           } 
/*  907 */           stringBuilder2.append("],");
/*  908 */           stringBuilder2.append("\"activeRow\": 0,\"activeCol\": 0,");
/*  909 */           stringBuilder2.append("\"gridline\": {\"color\": \"#D0D7E5\",\"showVerticalGridline\": true,\"showHorizontalGridline\": true},");
/*  910 */           stringBuilder2.append("\"allowDragDrop\": false,");
/*  911 */           stringBuilder2.append("\"allowDragFill\": false,");
/*  912 */           stringBuilder2.append("\"rowHeaderData\": {\"rowCount\": 20,\"defaultDataNode\": {\"style\": {\"foreColor\": \"black\"}}},");
/*  913 */           b8 = (i7 > 10) ? i7 : 10;
/*  914 */           stringBuilder2.append("\"colHeaderData\": {\"colCount\": " + b8 + ",");
/*  915 */           if (!"".equals(str8))
/*  916 */             stringBuilder2.append("\"dataTable\": {\"0\": {" + str8.substring(0, str8.length() - 1) + "}},"); 
/*  917 */           stringBuilder2.append("\"defaultDataNode\": {\"style\": {\"foreColor\": \"black\"}}},");
/*  918 */           stringBuilder2.append("\"spans\": [{\"row\": 2,\"rowCount\": 1,\"col\": 0,\"colCount\": " + b8 + "},{\"row\": 4,\"rowCount\": 1,\"col\": 0,\"colCount\": " + b8 + "}");
/*  919 */           if (bool5)
/*  920 */             stringBuilder2.append(",{\"row\": 5,\"rowCount\": 1,\"col\": 0,\"colCount\": 2}"); 
/*  921 */           if (b7 > 1)
/*  922 */             stringBuilder2.append(",{\"row\": 0,\"rowCount\": 1,\"col\": " + (i7 - b7) + ",\"colCount\": " + b7 + "}"); 
/*  923 */           stringBuilder2.append("],");
/*  924 */           stringBuilder2.append("\"data\": {");
/*  925 */           stringBuilder2.append("\"rowCount\": 20,");
/*  926 */           stringBuilder2.append("\"colCount\": " + b8 + ",");
/*  927 */           stringBuilder2.append("\"dataTable\": {");
/*      */           
/*  929 */           JSONObject jSONObject = JSONObject.fromObject(hashMap1);
/*  930 */           stringBuilder2.append("\"0\":" + jSONObject.toString() + "");
/*  931 */           arrayList4.addAll(arrayList5);
/*      */           
/*  933 */           jSONObject = JSONObject.fromObject(hashMap2);
/*  934 */           stringBuilder2.append(",\"1\":" + jSONObject.toString() + "");
/*  935 */           arrayList4.addAll(arrayList6);
/*      */           
/*  937 */           stringBuilder2.append(",\"2\":{\"0\":{\"value\":\"" + SystemEnv.getHtmlLabelName(19572, Util.getIntValue("" + this.user.getLanguage(), 7)) + "\",\"style\":{\"backColor\": \"#eeeeee\"}}}");
/*  938 */           LinkedHashMap<Object, Object> linkedHashMap14 = new LinkedHashMap<>();
/*  939 */           linkedHashMap14.put("id", "2,0");
/*  940 */           linkedHashMap14.put("etype", "8");
/*  941 */           linkedHashMap14.put("evalue", SystemEnv.getHtmlLabelName(19572, Util.getIntValue("" + this.user.getLanguage(), 7)));
/*  942 */           arrayList4.add(linkedHashMap14);
/*      */           
/*  944 */           jSONObject = JSONObject.fromObject(hashMap3);
/*  945 */           stringBuilder2.append(",\"3\":" + jSONObject.toString() + "");
/*  946 */           arrayList4.addAll(arrayList7);
/*      */           
/*  948 */           stringBuilder2.append(",\"4\":{\"0\":{\"value\":\"" + SystemEnv.getHtmlLabelName(19573, Util.getIntValue("" + this.user.getLanguage(), 7)) + "\",\"style\":{\"backColor\": \"#eeeeee\"}}}");
/*  949 */           linkedHashMap14 = new LinkedHashMap<>();
/*  950 */           linkedHashMap14.put("id", "4,0");
/*  951 */           linkedHashMap14.put("etype", "9");
/*  952 */           linkedHashMap14.put("evalue", SystemEnv.getHtmlLabelName(19573, Util.getIntValue("" + this.user.getLanguage(), 7)));
/*  953 */           arrayList4.add(linkedHashMap14);
/*      */           
/*  955 */           if (bool5) {
/*  956 */             jSONObject = JSONObject.fromObject(hashMap4);
/*  957 */             stringBuilder2.append(",\"5\":" + jSONObject.toString() + "");
/*  958 */             arrayList4.addAll(arrayList8);
/*      */           } 
/*      */           
/*  961 */           stringBuilder2.append("},");
/*  962 */           stringBuilder2.append("\"defaultDataNode\": {\"style\": {\"foreColor\": \"black\"}}");
/*  963 */           stringBuilder2.append("},");
/*  964 */           stringBuilder2.append("\"rowRangeGroup\": {\"itemsCount\": 20},\"colRangeGroup\": {\"itemsCount\": " + b8 + "}");
/*  965 */           stringBuilder2.append("}");
/*  966 */           stringBuilder2.append("}}");
/*  967 */           HashMap<Object, Object> hashMap6 = new HashMap<>();
/*  968 */           hashMap6.put("row_0", "" + this.detail_row_height);
/*  969 */           hashMap6.put("row_1", "" + this.detail_row_height);
/*  970 */           hashMap6.put("row_2", "" + this.detail_row_height);
/*  971 */           hashMap6.put("row_3", "" + this.detail_row_height);
/*  972 */           hashMap6.put("row_4", "" + this.detail_row_height);
/*  973 */           if (bool5)
/*  974 */             hashMap6.put("row_5", "" + this.detail_row_height); 
/*  975 */           LinkedHashMap<Object, Object> linkedHashMap15 = new LinkedHashMap<>();
/*  976 */           linkedHashMap15.put("rowheads", hashMap6);
/*  977 */           linkedHashMap15.put("colheads", hashMap5);
/*  978 */           linkedHashMap15.put("ec", arrayList4);
/*  979 */           linkedHashMap15.put("edtitleinrow", "2");
/*  980 */           linkedHashMap15.put("edtailinrow", "4");
/*  981 */           linkedHashMap15.put("seniorset", "1");
/*  982 */           linkedHashMap12.put("detail_" + (b + 1), linkedHashMap15);
/*  983 */           b++;
/*      */         } 
/*      */       } 
/*      */       
/*  987 */       stringBuilder2.append("}");
/*      */ 
/*      */       
/*  990 */       LinkedHashMap<Object, Object> linkedHashMap13 = new LinkedHashMap<>();
/*  991 */       linkedHashMap13.put("wfid", "" + k);
/*  992 */       linkedHashMap13.put("nodeid", "" + m);
/*  993 */       linkedHashMap13.put("formid", "" + n);
/*  994 */       linkedHashMap13.put("isbill", "" + i1);
/*  995 */       StringBuilder stringBuilder3 = new StringBuilder();
/*  996 */       stringBuilder3.append("{");
/*  997 */       stringBuilder3.append("\"eformdesign\":{\"eattr\":" + JSONObject.fromObject(linkedHashMap13) + ",");
/*  998 */       stringBuilder3.append("\"etables\":{");
/*  999 */       stringBuilder3.append("\"emaintable\":{");
/* 1000 */       stringBuilder3.append("\"rowheads\":" + JSONObject.fromObject(linkedHashMap11) + ",");
/* 1001 */       stringBuilder3.append("\"colheads\":" + JSONObject.fromObject(linkedHashMap10) + ",");
/* 1002 */       stringBuilder3.append("\"ec\":" + JSONArray.fromObject(arrayList));
/* 1003 */       stringBuilder3.append("}");
/* 1004 */       if (i5 > 0) {
/* 1005 */         stringBuilder3.append(",");
/* 1006 */         String str = JSONObject.fromObject(linkedHashMap12).toString();
/* 1007 */         stringBuilder3.append(str.substring(1, str.lastIndexOf("}")));
/*      */       } 
/* 1009 */       stringBuilder3.append("}}}");
/*      */       
/* 1011 */       HtmlLayoutOperate htmlLayoutOperate = new HtmlLayoutOperate();
/* 1012 */       HtmlLayoutBean htmlLayoutBean = new HtmlLayoutBean();
/* 1013 */       htmlLayoutBean.setWorkflowid(k);
/* 1014 */       htmlLayoutBean.setNodeid(m);
/* 1015 */       htmlLayoutBean.setFormid(n);
/* 1016 */       htmlLayoutBean.setIsbill(i1);
/* 1017 */       htmlLayoutBean.setType(paramInt1);
/* 1018 */       htmlLayoutBean.setOperuser(this.user.getUID());
/* 1019 */       String str6 = htmlLayoutOperate.getLayoutName(m, n, i1, paramInt1, this.user.getLanguage());
/* 1020 */       htmlLayoutBean.setLayoutname(str6 + "(" + SystemEnv.getHtmlLabelName(516265, this.user.getLanguage()) + ")");
/* 1021 */       htmlLayoutBean.setHtmlparsescheme(1);
/* 1022 */       htmlLayoutBean.setVersion(2);
/* 1023 */       htmlLayoutBean.setDatajson(stringBuilder3.toString());
/* 1024 */       htmlLayoutBean.setPluginjson(stringBuilder2.toString());
/* 1025 */       if (!this.isFreeNodeHtml) {
/*      */         
/* 1027 */         RecordSet recordSet1 = new RecordSet();
/* 1028 */         String str = "select scripts from  workflow_nodehtmllayout where nodeid = ? and formid = ? and isbill = ? and type = ? and isactive = 1";
/* 1029 */         recordSet1.executeQuery(str, new Object[] { Integer.valueOf(m), Integer.valueOf(n), Integer.valueOf(i1), Integer.valueOf(paramInt1) });
/* 1030 */         if (recordSet1.next()) {
/* 1031 */           String str7 = recordSet1.getString("scripts");
/* 1032 */           htmlLayoutBean.setScripts(str7);
/*      */         } 
/*      */         
/* 1035 */         if (paramInt4 == 1) {
/*      */           
/* 1037 */           String str7 = "update workflow_nodehtmllayout set isactive = 0 where nodeid = ? and formid = ? and isbill = ? and type = ? and isactive = 1";
/* 1038 */           recordSet1.executeUpdate(str7, new Object[] { Integer.valueOf(m), Integer.valueOf(n), Integer.valueOf(i1), Integer.valueOf(paramInt1) });
/*      */         } else {
/* 1040 */           int i8 = htmlLayoutOperate.getActiveHtmlLayout(k, m, paramInt1);
/*      */           
/* 1042 */           (new ExcelFormula()).initFormula(k, n, m, i1, paramInt1, i8);
/*      */         } 
/* 1044 */         int i7 = htmlLayoutOperate.operHtmlActiveLayout(htmlLayoutBean, true, (paramInt4 != 0 && paramInt1 == 1));
/* 1045 */         i = i7;
/*      */         
/* 1047 */         (new ExcelFormula()).initFormula(k, n, m, i1, paramInt1, i7);
/*      */       } else {
/* 1049 */         int i7 = htmlLayoutOperate.operFreeNodeHtmlActiveLayout(htmlLayoutBean, true, (paramInt4 != 0 && paramInt1 == 1), this.configId);
/* 1050 */         i = i7;
/*      */       } 
/* 1052 */     } catch (Exception exception) {
/* 1053 */       writeLog(exception);
/*      */     } 
/* 1055 */     return i;
/*      */   }
/*      */   
/*      */   private String transColumnName(int paramInt) {
/* 1059 */     String str = "";
/* 1060 */     if (paramInt < 26) {
/* 1061 */       char c = (char)(65 + paramInt);
/* 1062 */       str = String.valueOf(c);
/* 1063 */     } else if (paramInt >= 26 && paramInt < 676) {
/* 1064 */       char c1 = (char)(65 + paramInt / 26 - 1);
/* 1065 */       str = str + String.valueOf(c1);
/* 1066 */       char c2 = (char)(65 + paramInt % 26);
/* 1067 */       str = str + String.valueOf(c2);
/*      */     } 
/* 1069 */     return str;
/*      */   }
/*      */   
/*      */   public HttpServletRequest getRequest() {
/* 1073 */     return this.request;
/*      */   }
/*      */   public void setRequest(HttpServletRequest paramHttpServletRequest) {
/* 1076 */     this.request = paramHttpServletRequest;
/*      */   }
/*      */   public User getUser() {
/* 1079 */     return this.user;
/*      */   }
/*      */   public void setUser(User paramUser) {
/* 1082 */     this.user = paramUser;
/*      */   }
/*      */   public int getExcelStyle() {
/* 1085 */     return this.excelStyle;
/*      */   }
/*      */   public void setExcelStyle(int paramInt) {
/* 1088 */     this.excelStyle = paramInt;
/*      */   }
/*      */   
/*      */   public boolean isFreeNodeHtml() {
/* 1092 */     return this.isFreeNodeHtml;
/*      */   }
/*      */   
/*      */   public void setFreeNodeHtml(boolean paramBoolean) {
/* 1096 */     this.isFreeNodeHtml = paramBoolean;
/*      */   }
/*      */   
/*      */   public String getIsSys() {
/* 1100 */     return this.isSys;
/*      */   }
/*      */   
/*      */   public void setIsSys(String paramString) {
/* 1104 */     this.isSys = paramString;
/*      */   }
/*      */   
/*      */   public int getConfigId() {
/* 1108 */     return this.configId;
/*      */   }
/*      */   
/*      */   public void setConfigId(int paramInt) {
/* 1112 */     this.configId = paramInt;
/*      */   }
/*      */   
/*      */   public Map<String, Object> getParams() {
/* 1116 */     return this.params;
/*      */   }
/*      */   
/*      */   public void setParams(Map<String, Object> paramMap) {
/* 1120 */     this.params = paramMap;
/*      */   }
/*      */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/exceldesign/ExcelsetInitManager.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */