/*     */ package weaver.workflow.exceldesign;
/*     */ 
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import java.text.SimpleDateFormat;
/*     */ import java.util.Date;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.conn.ConnStatement;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class FormTemplateManager
/*     */ {
/*     */   private static final int FORMVIRTUALNODEID = -12345;
/*     */   
/*     */   public static int getFORMVIRTUALNODEID() {
/*  28 */     return -12345;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean isFormVirtualNode(int paramInt) {
/*  35 */     return (paramInt == -12345);
/*     */   }
/*     */   public static boolean isFormVirtualNode(String paramString) {
/*  38 */     return paramString.equals("-12345");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String[] getNodeFormParams(int paramInt1, int paramInt2, int paramInt3, String paramString, boolean paramBoolean) {
/*  48 */     String str1 = isFormVirtualNode(paramInt1) ? " workflow_nodeform_form" : " workflow_nodeform";
/*  49 */     str1 = str1 + " " + paramString + " ";
/*     */     
/*  51 */     String str2 = paramBoolean ? " and " : " ";
/*  52 */     String str3 = "".equals(paramString) ? "" : (paramString + ".");
/*  53 */     if (isFormVirtualNode(paramInt1)) {
/*  54 */       str2 = str2 + str3 + "formid=" + paramInt2 + " and " + str3 + "isbill=" + paramInt3 + " ";
/*     */     } else {
/*  56 */       str2 = str2 + str3 + "nodeid=" + paramInt1 + " ";
/*  57 */     }  return new String[] { str1, str2 };
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static String[] getFreeNodeFormParams(int paramInt1, int paramInt2, int paramInt3, String paramString, boolean paramBoolean, int paramInt4) {
/*  63 */     String str1 = " workflow_freenode_nodeform";
/*  64 */     str1 = str1 + " " + paramString + " ";
/*     */     
/*  66 */     String str2 = paramBoolean ? " and " : " ";
/*  67 */     String str3 = "".equals(paramString) ? "" : (paramString + ".");
/*  68 */     str2 = str2 + str3 + "nodeid=" + paramInt1 + " and layoutconfigid=" + paramInt4;
/*  69 */     return new String[] { str1, str2 };
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean judgeLayoutBelForm(int paramInt) {
/*  77 */     boolean bool = false;
/*  78 */     if (paramInt > 0) {
/*  79 */       RecordSet recordSet = new RecordSet();
/*  80 */       recordSet.executeSql("select nodeid from workflow_nodehtmllayout where id=" + paramInt);
/*  81 */       if (recordSet.next() && isFormVirtualNode(recordSet.getString("nodeid")))
/*  82 */         bool = true; 
/*     */     } 
/*  84 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String transFormTemplateLink(String paramString1, String paramString2) {
/*  91 */     String[] arrayOfString = Util.splitString(paramString2, "+");
/*  92 */     int i = Util.getIntValue(arrayOfString[0]);
/*  93 */     int j = Util.getIntValue(arrayOfString[1]);
/*  94 */     int k = Util.getIntValue(arrayOfString[2], 0);
/*  95 */     int m = Util.getIntValue(arrayOfString[3], 7);
/*  96 */     String str1 = "", str2 = "", str3 = "";
/*  97 */     RecordSet recordSet = new RecordSet();
/*     */     
/*  99 */     recordSet.executeSql("select id,isprint from workflow_formmode where isbill='1' and formid=" + paramString1 + " and isprint = " + j);
/* 100 */     while (recordSet.next()) {
/* 101 */       int n = Util.getIntValue(recordSet.getString("isprint"), -1);
/* 102 */       String str = Util.null2String(recordSet.getString("id"));
/* 103 */       if (j == 0 && n == 0) {
/* 104 */         str1 = str;
/* 105 */         str2 = str; continue;
/* 106 */       }  if (j == 1 && n == 1) {
/* 107 */         str2 = str; continue;
/* 108 */       }  if (j == 2 && n == 0) {
/* 109 */         str3 = str;
/*     */       }
/*     */     } 
/* 112 */     String str4 = "";
/* 113 */     String str5 = "";
/* 114 */     if (j == 0) {
/* 115 */       str4 = SystemEnv.getHtmlLabelName(16450, m);
/* 116 */       str5 = str1;
/* 117 */     } else if (j == 1) {
/* 118 */       str4 = SystemEnv.getHtmlLabelName(128952, Util.getIntValue(m));
/* 119 */       str5 = str2;
/* 120 */     } else if (j == 2) {
/* 121 */       str4 = SystemEnv.getHtmlLabelName(125554, m);
/* 122 */       str5 = str3;
/*     */     } 
/*     */     
/* 125 */     String str6 = "";
/* 126 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 127 */     if (!"".equals(str5) && (j == 0 || j == 1)) {
/* 128 */       str4 = str4 + "（" + SystemEnv.getHtmlLabelName(18017, m) + "）";
/* 129 */       if (i > 0) {
/* 130 */         str6 = "<a href='javascript:void(0);' onclick=\"openFullWindowHaveBar('/workflow/mode/index.jsp?formid=" + paramString1 + "&isbill=1&isprint=" + j + "&modeid=" + str5 + "')\">" + str4 + "</a>";
/* 131 */         hashMap.put("modelLink", "/workflow/mode/index.jsp?formid=" + paramString1 + "&isbill=1&isprint=" + j + "&modeid=" + str5);
/* 132 */         hashMap.put("modeId", str5);
/*     */       } else {
/* 134 */         str6 = str4;
/*     */       } 
/*     */     } else {
/* 137 */       int n = getFormHtmlLayoutid(Util.getIntValue(paramString1), k, j);
/* 138 */       if (n <= 0) {
/* 139 */         str4 = SystemEnv.getHtmlLabelName(82, m);
/*     */       } else {
/* 141 */         str4 = str4 + "（" + SystemEnv.getHtmlLabelName(23682, m) + "）";
/*     */       } 
/* 143 */       if (i > 0) {
/* 144 */         this; str6 = "<a href='javascript:void(0);' onclick=\"workflowFormSetUtil.openDesignDialog('0','" + -12345 + "','" + paramString1 + "','" + k + "','" + j + "','" + n + "',false)\">" + str4 + "</a>";
/* 145 */         hashMap.put("wfId", "0");
/* 146 */         this; hashMap.put("nodeId", -12345 + "");
/* 147 */         hashMap.put("formId", paramString1 + "");
/* 148 */         hashMap.put("isBill", k + "");
/* 149 */         hashMap.put("layouttype", j + "");
/* 150 */         hashMap.put("htmllayoutid", n + "");
/*     */       } else {
/* 152 */         str6 = str4;
/*     */       } 
/*     */     } 
/* 155 */     hashMap.put("linkName", str4);
/* 156 */     hashMap.put("isModel", Boolean.valueOf((!"".equals(str5) && (j == 0 || j == 1))));
/* 157 */     hashMap.put("operatelevel", Integer.valueOf(i));
/* 158 */     return JSONObject.toJSONString(hashMap);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getFormHtmlLayoutid(int paramInt1, int paramInt2, int paramInt3) {
/* 166 */     RecordSet recordSet = new RecordSet();
/* 167 */     this; recordSet.executeSql("select id from workflow_nodehtmllayout where formid=" + paramInt1 + " and isbill=" + paramInt2 + " and type=" + paramInt3 + " and nodeid=" + -12345 + " and isactive=1");
/* 168 */     if (recordSet.next()) {
/* 169 */       return Util.getIntValue(recordSet.getString("id"));
/*     */     }
/* 171 */     return -1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getFormName(int paramInt1, int paramInt2, int paramInt3) {
/* 178 */     if (paramInt1 == 0 || paramInt2 < 0)
/* 179 */       return ""; 
/* 180 */     String str = "";
/* 181 */     if (paramInt3 < 1) paramInt3 = 7; 
/* 182 */     RecordSet recordSet = new RecordSet();
/* 183 */     if (paramInt2 == 0) {
/* 184 */       recordSet.executeSql("select formname from workflow_formbase where id=" + paramInt1);
/* 185 */       if (recordSet.next())
/* 186 */         str = recordSet.getString(1); 
/* 187 */     } else if (paramInt2 == 1) {
/* 188 */       recordSet.executeSql("select namelabel from workflow_bill where id=" + paramInt1);
/* 189 */       if (recordSet.next())
/* 190 */         str = SystemEnv.getHtmlLabelName(Util.getIntValue(recordSet.getString(1)), paramInt3); 
/*     */     } 
/* 192 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void syncFormFieldInfoToNode(int paramInt1, int paramInt2, int paramInt3) {
/* 199 */     RecordSet recordSet = new RecordSet();
/* 200 */     int i = 0;
/* 201 */     recordSet.executeQuery("select isbill from workflow_base where id=" + paramInt1, new Object[0]);
/* 202 */     if (recordSet.next())
/* 203 */       i = Util.getIntValue(recordSet.getString("isbill")); 
/* 204 */     String str1 = " formid=" + paramInt3 + " and isbill=" + i;
/*     */     
/* 206 */     String str2 = "fieldid,isview,isedit,ismandatory,orderid,isalonerow,isorder,ordertype,orderindex,isonlyshow";
/* 207 */     recordSet.executeUpdate("delete from workflow_nodeform where nodeid=" + paramInt2, new Object[0]);
/* 208 */     recordSet.executeUpdate("insert into workflow_nodeform(nodeid," + str2 + ") select " + paramInt2 + "," + str2 + " from workflow_nodeform_form where " + str1, new Object[0]);
/*     */     
/* 210 */     String str3 = "insert into workflow_nodeform(nodeid, fieldid, isview, orderid)";
/* 211 */     if (i == 0) {
/* 212 */       str3 = str3 + "select '" + paramInt2 + "',a.fieldid,1,fieldorder from workflow_formfield a where formid=" + paramInt3 + " and a.fieldid";
/*     */     } else {
/* 214 */       str3 = str3 + "select '" + paramInt2 + "',a.id,1,dsporder from workflow_billfield a where billid=" + paramInt3 + " and a.id";
/*     */     } 
/* 216 */     str3 = str3 + " not in (select fieldid from workflow_nodeform where nodeid=" + paramInt2 + ")";
/* 217 */     recordSet.executeUpdate(str3, new Object[0]);
/*     */     
/* 219 */     str2 = "groupid,isadd,isedit,isdelete,ishidenull,isdefault,isneed,isopensapmul,defaultrows,isprintserial,allowscroll,isopenpaging,isorder,ordertype,orderindex,mergefields,mobileallowscroll,mobilecardedit,iscopy,isprintthead";
/* 220 */     recordSet.executeUpdate("delete from workflow_nodeformgroup where nodeid=" + paramInt2, new Object[0]);
/* 221 */     recordSet.executeUpdate("insert into workflow_nodeformgroup(nodeid," + str2 + ") select " + paramInt2 + "," + str2 + " from workflow_nodeformgroup_form where " + str1, new Object[0]);
/* 222 */     recordSet.executeUpdate("delete from workflow_flownodehtml where nodeid=" + paramInt2, new Object[0]);
/* 223 */     recordSet.executeUpdate("insert into workflow_flownodehtml(workflowid,nodeid,colsperrow) select " + paramInt1 + "," + paramInt2 + ",colsperrow from workflow_flownodehtml_form where" + str1, new Object[0]);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void copyFormHtmlLayout(int paramInt1, int paramInt2, Map paramMap, User paramUser) {
/* 233 */     ConnStatement connStatement = null;
/* 234 */     RecordSet recordSet = new RecordSet();
/*     */     try {
/* 236 */       boolean bool = (paramInt2 > 0) ? false : true;
/* 237 */       String str1 = "";
/* 238 */       connStatement = new ConnStatement();
/* 239 */       String str2 = getFormName(paramInt1, 1, paramUser.getLanguage());
/* 240 */       String str3 = getFormName(paramInt2, bool, paramUser.getLanguage());
/* 241 */       String str4 = " formid=" + paramInt1 + " and isbill=1";
/* 242 */       String str5 = " formid=" + paramInt2 + " and isbill=" + bool;
/* 243 */       SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
/* 244 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 245 */       for (Map.Entry entry : paramMap.entrySet()) {
/* 246 */         hashMap.put(((String)entry.getKey()).replace("old", ""), entry.getValue());
/*     */       }
/*     */       
/* 249 */       this; recordSet.executeSql("delete from workflow_nodehtmllayout where " + str4 + " and nodeid=" + -12345);
/* 250 */       this; recordSet.executeSql("select * from workflow_nodehtmllayout where " + str5 + " and nodeid=" + -12345);
/* 251 */       while (recordSet.next()) {
/* 252 */         String str = recordSet.getString("datajson");
/* 253 */         if (!bool) {
/* 254 */           for (Map.Entry<Object, Object> entry : hashMap.entrySet()) {
/* 255 */             str = str.replaceAll("\"etype\":\"2\",\"field\":\"" + (String)entry.getKey() + "\"", "\"etype\":\"2\",\"field\":\"fieldmark_" + (String)entry.getKey() + "\"");
/* 256 */             str = str.replaceAll("\"etype\":\"3\",\"field\":\"" + (String)entry.getKey() + "\"", "\"etype\":\"3\",\"field\":\"fieldmark_" + (String)entry.getKey() + "\"");
/* 257 */             str = str.replaceAll("\"etype\":\"18\",\"field\":\"" + (String)entry.getKey() + "\"", "\"etype\":\"18\",\"field\":\"fieldmark_" + (String)entry.getKey() + "\"");
/* 258 */             str = str.replaceAll("\"etype\":\"19\",\"field\":\"" + (String)entry.getKey() + "\"", "\"etype\":\"19\",\"field\":\"fieldmark_" + (String)entry.getKey() + "\"");
/*     */           } 
/* 260 */           for (Map.Entry<Object, Object> entry : hashMap.entrySet()) {
/* 261 */             str = str.replaceAll("\"etype\":\"2\",\"field\":\"fieldmark_" + (String)entry.getKey() + "\"", "\"etype\":\"2\",\"field\":\"" + (String)entry.getValue() + "\"");
/* 262 */             str = str.replaceAll("\"etype\":\"3\",\"field\":\"fieldmark_" + (String)entry.getKey() + "\"", "\"etype\":\"3\",\"field\":\"" + (String)entry.getValue() + "\"");
/* 263 */             str = str.replaceAll("\"etype\":\"18\",\"field\":\"fieldmark_" + (String)entry.getKey() + "\"", "\"etype\":\"18\",\"field\":\"" + (String)entry.getValue() + "\"");
/* 264 */             str = str.replaceAll("\"etype\":\"19\",\"field\":\"fieldmark_" + (String)entry.getKey() + "\"", "\"etype\":\"19\",\"field\":\"" + (String)entry.getValue() + "\"");
/*     */           } 
/* 266 */         } else if (bool == true) {
/* 267 */           for (Map.Entry<Object, Object> entry : hashMap.entrySet()) {
/* 268 */             str = str.replaceAll("\"etype\":\"2\",\"field\":\"" + (String)entry.getKey() + "\"", "\"etype\":\"2\",\"field\":\"" + (String)entry.getValue() + "\"");
/* 269 */             str = str.replaceAll("\"etype\":\"3\",\"field\":\"" + (String)entry.getKey() + "\"", "\"etype\":\"3\",\"field\":\"" + (String)entry.getValue() + "\"");
/* 270 */             str = str.replaceAll("\"etype\":\"18\",\"field\":\"" + (String)entry.getKey() + "\"", "\"etype\":\"18\",\"field\":\"" + (String)entry.getValue() + "\"");
/* 271 */             str = str.replaceAll("\"etype\":\"19\",\"field\":\"" + (String)entry.getKey() + "\"", "\"etype\":\"19\",\"field\":\"" + (String)entry.getValue() + "\"");
/*     */           } 
/*     */         } 
/* 274 */         str1 = "insert into workflow_nodehtmllayout(workflowid,nodeid,formid,isbill,type,layoutname,htmlparsescheme,version,operuser,opertime,datajson,pluginjson,scripts,isactive) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
/*     */         
/* 276 */         connStatement.setStatementSql(str1);
/* 277 */         connStatement.setString(1, recordSet.getString("workflowid"));
/* 278 */         this; connStatement.setInt(2, -12345);
/* 279 */         connStatement.setInt(3, paramInt1);
/* 280 */         connStatement.setInt(4, 1);
/* 281 */         connStatement.setString(5, recordSet.getString("type"));
/* 282 */         connStatement.setString(6, recordSet.getString("layoutname").replace(str3, str2));
/* 283 */         connStatement.setString(7, recordSet.getString("htmlparsescheme"));
/* 284 */         connStatement.setString(8, recordSet.getString("version"));
/* 285 */         connStatement.setInt(9, paramUser.getUID());
/* 286 */         connStatement.setString(10, simpleDateFormat.format(new Date()));
/* 287 */         connStatement.setString(11, str);
/* 288 */         connStatement.setString(12, recordSet.getString("pluginjson"));
/* 289 */         connStatement.setString(13, recordSet.getString("scripts"));
/* 290 */         connStatement.setString(14, recordSet.getString("isactive"));
/* 291 */         connStatement.executeUpdate();
/*     */       } 
/*     */       
/* 294 */       recordSet.executeSql("delete from workflow_nodeform_form where " + str4);
/* 295 */       recordSet.executeSql("select * from workflow_nodeform_form where " + str5);
/* 296 */       while (recordSet.next()) {
/* 297 */         str1 = "insert into workflow_nodeform_form(formid,isbill,fieldid,isview,isedit,ismandatory,orderid,isalonerow) values(?,?,?,?,?,?,?,?)";
/* 298 */         connStatement.setStatementSql(str1);
/* 299 */         connStatement.setInt(1, paramInt1);
/* 300 */         connStatement.setInt(2, 1);
/* 301 */         int i = Util.getIntValue(recordSet.getString("fieldid"));
/* 302 */         if (i > 0)
/* 303 */           i = Util.getIntValue((String)hashMap.get(i + "")); 
/* 304 */         connStatement.setInt(3, i);
/* 305 */         connStatement.setString(4, recordSet.getString("isview"));
/* 306 */         connStatement.setString(5, recordSet.getString("isedit"));
/* 307 */         connStatement.setString(6, recordSet.getString("ismandatory"));
/* 308 */         connStatement.setFloat(7, Util.getFloatValue(recordSet.getString("orderid"), 0.0F));
/* 309 */         connStatement.setString(8, recordSet.getString("isalonerow"));
/* 310 */         connStatement.executeUpdate();
/*     */       } 
/*     */       
/* 313 */       String str6 = "groupid,isadd,isedit,isdelete,ishidenull,isdefault,isneed,isopensapmul,defaultrows,isprintserial,allowscroll";
/* 314 */       recordSet.executeSql("delete from workflow_nodeformgroup_form where " + str4);
/* 315 */       recordSet.executeSql("insert into workflow_nodeformgroup_form(formid,isbill," + str6 + ") select " + paramInt1 + ",1," + str6 + " from workflow_nodeformgroup_form where " + str5);
/* 316 */       recordSet.executeSql("delete from workflow_flownodehtml_form where " + str4);
/* 317 */       recordSet.executeSql("insert into workflow_flownodehtml_form(formid,isbill,colsperrow) select " + paramInt1 + ",1,colsperrow from workflow_flownodehtml_form where" + str5);
/* 318 */     } catch (Exception exception) {
/* 319 */       exception.printStackTrace();
/*     */     } finally {
/* 321 */       connStatement.close();
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/exceldesign/FormTemplateManager.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */