/*    */ package weaver.workflow.exceldesign;
/*    */ 
/*    */ import java.text.SimpleDateFormat;
/*    */ import java.util.Date;
/*    */ import java.util.Iterator;
/*    */ import java.util.Map;
/*    */ import javax.servlet.http.HttpServletRequest;
/*    */ import weaver.file.FileUpload;
/*    */ import weaver.general.BaseBean;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.User;
/*    */ import weaver.hrm.schedule.manager.HrmScheduleManager;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class ExcelFormulaWorkday
/*    */ {
/*    */   public String getWorkDays(HttpServletRequest paramHttpServletRequest) {
/* 23 */     FileUpload fileUpload = new FileUpload(paramHttpServletRequest);
/* 24 */     String str1 = Util.null2String(fileUpload.getParameter("beginDate"));
/* 25 */     String str2 = Util.null2String(fileUpload.getParameter("endDate"));
/* 26 */     int i = Util.getIntValue(Util.null2String(fileUpload.getParameter("userid")), -1);
/* 27 */     return getWorkDays(str1, str2, i);
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getWorkDays(String paramString1, String paramString2, int paramInt) {
/* 38 */     BaseBean baseBean = new BaseBean();
/*    */     
/* 40 */     SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
/*    */ 
/*    */     
/* 43 */     if (paramString1 == null || paramString1.length() == 0) {
/* 44 */       paramString1 = baseBean.getPropValue("scheduleWorkDayForFormula", "fromDate");
/* 45 */       paramString1 = (paramString1 == null) ? "" : paramString1.trim();
/* 46 */       if (paramString1.length() == 0) {
/* 47 */         paramString1 = simpleDateFormat.format(new Date());
/*    */       }
/*    */     } 
/*    */ 
/*    */     
/* 52 */     if (paramString2 == null || paramString2.length() == 0) {
/* 53 */       paramString2 = baseBean.getPropValue("scheduleWorkDayForFormula", "endDate");
/* 54 */       paramString2 = (paramString2 == null) ? "" : paramString2.trim();
/* 55 */       if (paramString2.length() == 0) {
/* 56 */         paramString2 = simpleDateFormat.format(new Date());
/*    */       }
/*    */     } 
/*    */     
/* 60 */     boolean bool = false;
/*    */     try {
/* 62 */       SimpleDateFormat simpleDateFormat1 = new SimpleDateFormat("yyyy-MM-dd");
/* 63 */       Date date1 = simpleDateFormat1.parse(paramString1);
/* 64 */       Date date2 = simpleDateFormat1.parse(paramString2);
/* 65 */       if (date1.compareTo(date2) > 0) {
/* 66 */         String str = paramString1;
/* 67 */         paramString1 = paramString2;
/* 68 */         paramString2 = str;
/* 69 */         bool = true;
/*    */       } 
/* 71 */     } catch (Exception exception) {
/* 72 */       baseBean.writeLog("ExcelFormulaWorkday解析日期报错", exception);
/*    */     } 
/*    */     
/* 75 */     byte b = 0;
/* 76 */     User user = new User(paramInt);
/* 77 */     HrmScheduleManager hrmScheduleManager = new HrmScheduleManager(user);
/* 78 */     Map map = hrmScheduleManager.isWorkday(paramString1, paramString2, hrmScheduleManager.getSubCompanyId());
/* 79 */     if (map != null && map.size() > 0) {
/* 80 */       Iterator<String> iterator = map.keySet().iterator();
/* 81 */       while (iterator.hasNext()) {
/* 82 */         String str = iterator.next();
/* 83 */         Boolean bool1 = (Boolean)map.get(str);
/* 84 */         if (bool1.booleanValue()) {
/* 85 */           if (bool) {
/* 86 */             b--; continue;
/*    */           } 
/* 88 */           b++;
/*    */         } 
/*    */       } 
/*    */     } 
/*    */     
/* 93 */     return b + "";
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/exceldesign/ExcelFormulaWorkday.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */