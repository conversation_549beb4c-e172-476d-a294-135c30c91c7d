/*    */ package weaver.workflow.exceldesign;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class SearchConditionOption
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 363769579655304111L;
/*    */   private String key;
/*    */   private String showname;
/*    */   private boolean selected = false;
/*    */   
/*    */   public SearchConditionOption() {}
/*    */   
/*    */   public SearchConditionOption(String paramString1, String paramString2) {
/* 21 */     this.key = paramString1;
/* 22 */     this.showname = paramString2;
/*    */   }
/*    */   
/*    */   public SearchConditionOption(String paramString1, String paramString2, boolean paramBoolean) {
/* 26 */     this.key = paramString1;
/* 27 */     this.showname = paramString2;
/* 28 */     this.selected = paramBoolean;
/*    */   }
/*    */   
/*    */   public String getKey() {
/* 32 */     return this.key;
/*    */   }
/*    */   
/*    */   public void setKey(String paramString) {
/* 36 */     this.key = paramString;
/*    */   }
/*    */   
/*    */   public String getShowname() {
/* 40 */     return this.showname;
/*    */   }
/*    */   
/*    */   public void setShowname(String paramString) {
/* 44 */     this.showname = paramString;
/*    */   }
/*    */   
/*    */   public boolean isSelected() {
/* 48 */     return this.selected;
/*    */   }
/*    */   
/*    */   public void setSelected(boolean paramBoolean) {
/* 52 */     this.selected = paramBoolean;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/exceldesign/SearchConditionOption.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */