/*     */ package weaver.workflow.exceldesign;
/*     */ 
/*     */ import com.api.workflow.service.ScriptManagerService;
/*     */ import com.engine.workflow.biz.excelDesign.ModifyLogBiz;
/*     */ import com.engine.workflow.util.SignInputSetUtil;
/*     */ import java.text.SimpleDateFormat;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Date;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import weaver.conn.ConnStatement;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.workflow.workflow.WFManager;
/*     */ import weaver.workflow.workflow.WFNodeFieldManager;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HtmlLayoutOperate
/*     */   extends BaseBean
/*     */ {
/*     */   private HttpServletRequest request;
/*     */   private User user;
/*     */   
/*     */   public String reflectNodename(String paramString) {
/*  36 */     if (FormTemplateManager.isFormVirtualNode(paramString)) return ""; 
/*  37 */     String str = "";
/*  38 */     RecordSet recordSet = new RecordSet();
/*  39 */     recordSet.executeSql("select nodename from workflow_nodebase where id=" + paramString);
/*  40 */     if (recordSet.next())
/*  41 */       str = recordSet.getString("nodename"); 
/*  42 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String reflectWorkflowname(String paramString) {
/*  49 */     if (Util.getIntValue(paramString) <= 0) return ""; 
/*  50 */     String str = "";
/*  51 */     RecordSet recordSet = new RecordSet();
/*  52 */     recordSet.executeSql("select workflowname from workflow_base where id=" + paramString);
/*  53 */     if (recordSet.next())
/*  54 */       str = recordSet.getString("workflowname"); 
/*  55 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String reflectLayoutName(String paramString1, String paramString2) {
/*  62 */     String[] arrayOfString = Util.TokenizerString2(paramString2, "+");
/*  63 */     String str = Util.null2String(arrayOfString[0]);
/*  64 */     int i = Util.getIntValue(arrayOfString[1]);
/*  65 */     if ("1".equals(str)) {
/*  66 */       return paramString1 + "（" + SystemEnv.getHtmlLabelName(678, i) + "）";
/*     */     }
/*  68 */     return paramString1 + "（" + SystemEnv.getHtmlLabelName(1477, i) + "）";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String reflectLayoutBelForm(String paramString) {
/*  75 */     String str = "0";
/*  76 */     if (FormTemplateManager.isFormVirtualNode(paramString))
/*  77 */       str = "1"; 
/*  78 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String transNodeInfo(String paramString1, String paramString2) {
/*  85 */     String[] arrayOfString = paramString2.split("\\+");
/*     */     
/*  87 */     String str = arrayOfString[0];
/*  88 */     int i = Util.getIntValue(arrayOfString[1], 7);
/*  89 */     StringBuilder stringBuilder = new StringBuilder();
/*  90 */     stringBuilder.append("<div class=\"nodeinfoDiv\">")
/*  91 */       .append("<div class=\"nodeimg nodeimg_" + paramString1 + "\" title=\"" + SystemEnv.getHtmlLabelName(515504, Util.getIntValue(i)) + "\" onclick=\"extendHistoryLayout(this);\"></div>")
/*  92 */       .append("<span class=\"nodespan nodespan_" + paramString1 + "\">" + str + "</span>")
/*  93 */       .append("</div>");
/*  94 */     return stringBuilder.toString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String transFormLayoutType(String paramString1, String paramString2) {
/* 101 */     String[] arrayOfString = paramString2.split("\\+");
/* 102 */     int i = Util.getIntValue(arrayOfString[0]);
/* 103 */     int j = Util.getIntValue(arrayOfString[1], 7);
/* 104 */     String str = "";
/* 105 */     if (i == 0) {
/* 106 */       str = SystemEnv.getHtmlLabelName(19511, Util.getIntValue(j));
/* 107 */     } else if (i == 1) {
/* 108 */       str = SystemEnv.getHtmlLabelName(128952, Util.getIntValue(j));
/* 109 */     } else if (i == 2) {
/* 110 */       str = SystemEnv.getHtmlLabelName(125554, Util.getIntValue(j));
/* 111 */     }  StringBuilder stringBuilder = new StringBuilder();
/* 112 */     stringBuilder.append("<div class=\"nodeinfoDiv\">")
/* 113 */       .append("<div class=\"nodeimg layoutimg_" + i + "\" title=\"" + SystemEnv.getHtmlLabelName(515505, Util.getIntValue(j)) + "\" onclick=\"extendHistoryLayout(this);\"></div>")
/* 114 */       .append("<span class=\"nodespan layoutspan_" + i + "\">" + str + "</span>")
/* 115 */       .append("</div>");
/* 116 */     return stringBuilder.toString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String transLayoutInfo(String paramString1, String paramString2) {
/* 123 */     String[] arrayOfString = paramString2.split("\\+");
/* 124 */     int i = Util.getIntValue(arrayOfString[0]);
/* 125 */     int j = Util.getIntValue(arrayOfString[1]);
/* 126 */     int k = Util.getIntValue(arrayOfString[2]);
/* 127 */     int m = Util.getIntValue(arrayOfString[3]);
/* 128 */     int n = Util.getIntValue(arrayOfString[4], 7);
/* 129 */     RecordSet recordSet = new RecordSet();
/* 130 */     String str = "select * from workflow_nodehtmllayout where formid=" + j + " and isbill=" + k + " and type=" + m + " and isactive=1";
/* 131 */     if (FormTemplateManager.isFormVirtualNode(paramString1)) {
/* 132 */       str = str + " and nodeid=" + paramString1;
/*     */     } else {
/* 134 */       str = str + " and ((workflowid=" + i + " and nodeid=" + paramString1 + ") or nodeid=" + FormTemplateManager.getFORMVIRTUALNODEID() + ") order by nodeid desc";
/* 135 */     }  recordSet.executeSql(str);
/* 136 */     StringBuilder stringBuilder = new StringBuilder();
/* 137 */     stringBuilder.append("<div class=\"layoutinfo_active\">")
/* 138 */       .append("<input type=\"hidden\" name=\"nodeid\" isactive=\"1\" value=\"" + paramString1 + "\" />");
/* 139 */     if (recordSet.next()) {
/* 140 */       String str1 = Util.null2String(recordSet.getString("id"));
/* 141 */       String str2 = Util.null2String(recordSet.getString("layoutname"));
/* 142 */       String str3 = Util.null2String(recordSet.getString("nodeid"));
/* 143 */       int i1 = Util.getIntValue(recordSet.getString("version"), 0);
/* 144 */       String str4 = Util.null2String(recordSet.getString("operuser"));
/* 145 */       String str5 = Util.null2String(recordSet.getString("opertime"));
/* 146 */       stringBuilder.append(getExistLayoutInfo(paramString1, str1, str2, str3, i1, str4, str5, n));
/*     */     } else {
/* 148 */       stringBuilder.append("<input type=\"hidden\" name=\"layoutid\" value=\"0\" />")
/* 149 */         .append("<input type=\"hidden\" name=\"version\" value=\"0\" />")
/* 150 */         .append("<div class=\"nonelayout\">");
/* 151 */       if (m == 0) {
/* 152 */         stringBuilder.append(SystemEnv.getHtmlLabelName(10000184, Util.getIntValue(n)));
/* 153 */       } else if (m == 1) {
/* 154 */         stringBuilder.append(SystemEnv.getHtmlLabelName(10000185, Util.getIntValue(n)));
/* 155 */       } else if (m == 2) {
/* 156 */         stringBuilder.append(SystemEnv.getHtmlLabelName(10000186, Util.getIntValue(n)));
/* 157 */       }  stringBuilder.append("</div>");
/*     */     } 
/* 159 */     stringBuilder.append("</div>");
/* 160 */     return stringBuilder.toString();
/*     */   }
/*     */   private String getExistLayoutInfo(String paramString1, String paramString2, String paramString3, String paramString4, int paramInt1, String paramString5, String paramString6, int paramInt2) {
/* 163 */     boolean bool = (!FormTemplateManager.isFormVirtualNode(paramString1) && FormTemplateManager.isFormVirtualNode(paramString4)) ? true : false;
/* 164 */     StringBuilder stringBuilder = new StringBuilder();
/* 165 */     stringBuilder.append("<input type=\"hidden\" name=\"layoutid\" value=\"" + paramString2 + "\" />")
/* 166 */       .append("<input type=\"hidden\" name=\"version\" value=\"" + paramInt1 + "\" />")
/* 167 */       .append("<input type=\"hidden\" name=\"belform\" value=\"" + (bool ? "1" : "0") + "\" />")
/* 168 */       .append("<div class=\"layoutname\"><a href=\"#\" onclick=\"editBtnClick(this)\">" + paramString3);
/* 169 */     if (paramInt1 != 2)
/* 170 */       stringBuilder.append("(" + SystemEnv.getHtmlLabelName(84089, paramInt2) + ")"); 
/* 171 */     stringBuilder.append("</a>");
/* 172 */     if (bool)
/* 173 */       stringBuilder.append("<img class=\"formlayoutimg\" src=\"/images/tooltip_wev8.png\" title=\"" + SystemEnv.getHtmlLabelName(501857, paramInt2) + "\" />"); 
/* 174 */     stringBuilder.append("</div>");
/*     */     try {
/* 176 */       ResourceComInfo resourceComInfo = new ResourceComInfo();
/* 177 */       if (!"".equals(paramString5) && !"".equals(paramString6)) {
/* 178 */         stringBuilder.append("<div class=\"operinfo\">")
/* 179 */           .append(SystemEnv.getHtmlLabelName(623, paramInt2)).append(" ")
/* 180 */           .append(resourceComInfo.getLastname(paramString5)).append(" ")
/* 181 */           .append(SystemEnv.getHtmlLabelName(82894, paramInt2)).append(" ")
/* 182 */           .append(paramString6).append(" ")
/* 183 */           .append(SystemEnv.getHtmlLabelName(84094, paramInt2))
/* 184 */           .append("</div>");
/*     */       }
/* 186 */     } catch (Exception exception) {
/* 187 */       exception.printStackTrace();
/*     */     } 
/* 189 */     return stringBuilder.toString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String transOperBtn(String paramString1, String paramString2) {
/* 211 */     String[] arrayOfString = paramString2.split("\\+");
/* 212 */     int i = Util.getIntValue(arrayOfString[4], 7);
/* 213 */     StringBuilder stringBuilder = new StringBuilder();
/* 214 */     boolean bool = FormTemplateManager.isFormVirtualNode(paramString1);
/* 215 */     stringBuilder.append("<div class=\"operarea operarea_active\">")
/* 216 */       .append("<div class=\"operbtn oper_create\" onclick=\"createBtnClick(this);\"><div class=\"operbtn_create\">" + SystemEnv.getHtmlLabelName(125, i) + "</div></div>")
/* 217 */       .append("<div class=\"operbtn oper_edit\" onclick=\"parent.checkServer(editBtnClick,this);\"><div class=\"operbtn_edit\">" + SystemEnv.getHtmlLabelName(93, i) + "</div></div>")
/* 218 */       .append("<div class=\"operbtn oper_preview\" onclick=\"previewBtnClick(this);\"><div class=\"operbtn_preview\">" + SystemEnv.getHtmlLabelName(221, i) + "</div></div>")
/* 219 */       .append("<div class=\"operbtn oper_choose\" onclick=\"chooseBtnClick(this);\"><div class=\"operbtn_choose\">" + SystemEnv.getHtmlLabelName(172, i) + "</div></div>")
/* 220 */       .append("<div class=\"operbtn oper_delete\" onclick=\"deleteBtnClick(this);\"><div class=\"operbtn_delete\">" + SystemEnv.getHtmlLabelName(91, i) + "</div></div>")
/* 221 */       .append("<div class=\"operbtn oper_excelimp\" onclick=\"excelimpBtnClick(this);\"><div class=\"operbtn_excelimp\">" + SystemEnv.getHtmlLabelName(128713, i) + "</div></div>")
/* 222 */       .append("<div class=\"operbtn oper_init\" onclick=\"initBtnClick(this);\"><div class=\"operbtn_init\">" + SystemEnv.getHtmlLabelName(125556, i) + "</div></div>");
/* 223 */     if (!bool)
/* 224 */       stringBuilder.append("<div class=\"operbtn oper_sync\" onclick=\"syncBtnClick(this);\"><div class=\"operbtn_sync\">" + SystemEnv.getHtmlLabelName(125555, i) + "</div></div>"); 
/* 225 */     stringBuilder.append("</div>");
/* 226 */     return stringBuilder.toString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List getHistoryLayout(String paramString1, String paramString2, String paramString3, String paramString4, int paramInt1, int paramInt2) {
/* 233 */     if ("".equals(paramString2)) {
/* 234 */       paramString2 = "-1";
/*     */     }
/*     */     
/* 237 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 238 */     RecordSet recordSet = new RecordSet();
/* 239 */     String str = "select * from workflow_nodehtmllayout where nodeid=" + paramString2 + " and type=" + paramInt1 + " and isactive=0";
/* 240 */     if (FormTemplateManager.isFormVirtualNode(paramString2)) {
/* 241 */       str = str + " and formid=" + paramString3 + " and isbill=" + paramString4;
/*     */     } else {
/* 243 */       str = str + " and workflowid=" + paramString1;
/* 244 */     }  recordSet.executeSql(str);
/* 245 */     while (recordSet.next()) {
/* 246 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 247 */       String str1 = Util.null2String(recordSet.getString("id"));
/* 248 */       String str2 = Util.null2String(recordSet.getString("layoutname"));
/* 249 */       String str3 = Util.null2String(recordSet.getString("nodeid"));
/* 250 */       int i = Util.getIntValue(recordSet.getString("version"), 0);
/* 251 */       String str4 = Util.null2String(recordSet.getString("operuser"));
/* 252 */       String str5 = Util.null2String(recordSet.getString("opertime"));
/*     */       
/* 254 */       StringBuilder stringBuilder = new StringBuilder();
/* 255 */       stringBuilder.append("<div class=\"layoutinfo_history\">")
/* 256 */         .append("<input type=\"hidden\" name=\"nodeid\" isactive=\"0\" value=\"" + paramString2 + "\" />")
/* 257 */         .append(getExistLayoutInfo(paramString2, str1, str2, str3, i, str4, str5, paramInt2))
/* 258 */         .append("</div>");
/* 259 */       hashMap.put("layoutInfo", stringBuilder.toString());
/*     */       
/* 261 */       stringBuilder.setLength(0);
/* 262 */       stringBuilder.append("<div class=\"operarea operarea_history\">")
/* 263 */         .append("<div class=\"operbtn_setactive\" onclick=\"setLayoutToActive(this);\" title=\"" + SystemEnv.getHtmlLabelName(503417, paramInt2) + "\"></div>")
/* 264 */         .append("<div class=\"operbtn oper_edit\" onclick=\"parent.checkServer(editBtnClick,this);\"><div class=\"operbtn_edit\">" + SystemEnv.getHtmlLabelName(93, paramInt2) + "</div></div>")
/* 265 */         .append("<div class=\"operbtn oper_preview\" onclick=\"previewBtnClick(this);\"><div class=\"operbtn_preview\">" + SystemEnv.getHtmlLabelName(221, paramInt2) + "</div></div>")
/* 266 */         .append("<div class=\"operbtn oper_delete\" onclick=\"deleteBtnClick(this);\"><div class=\"operbtn_delete\">" + SystemEnv.getHtmlLabelName(91, paramInt2) + "</div></div>")
/* 267 */         .append("</div>");
/* 268 */       hashMap.put("operArea", stringBuilder.toString());
/*     */       
/* 270 */       arrayList.add(hashMap);
/*     */     } 
/* 272 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String deleteLayout(int paramInt1, int paramInt2) {
/* 279 */     RecordSet recordSet = new RecordSet();
/*     */     
/* 281 */     (new ExcelFormula()).clearFormulaWhenDeleteLayout(paramInt1, paramInt2);
/* 282 */     recordSet.executeSql("delete from workflow_nodehtmllayout where id=" + paramInt1);
/* 283 */     return "success";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void saveLayout_all() {
/* 291 */     int i = Util.getIntValue(this.request.getParameter("wfid"), 0);
/* 292 */     int j = Util.getIntValue(this.request.getParameter("nodeid"), 0);
/* 293 */     int k = Util.getIntValue(this.request.getParameter("formid"), 0);
/* 294 */     int m = Util.getIntValue(this.request.getParameter("isbill"), 0);
/* 295 */     if (i <= 0 || j <= 0)
/*     */       return; 
/* 297 */     int n = Util.getIntValue(Util.null2String(this.request.getParameter("showhtmlid")), 0);
/* 298 */     int i1 = Util.getIntValue(Util.null2String(this.request.getParameter("printhtmlid")), 0);
/* 299 */     int i2 = Util.getIntValue(Util.null2String(this.request.getParameter("mobilehtmlid")), 0);
/* 300 */     String str1 = Util.null2String(this.request.getParameter("syncNodes"));
/* 301 */     String str2 = Util.null2String(this.request.getParameter("printsyncNodes"));
/* 302 */     String str3 = Util.null2String(this.request.getParameter("syncMNodes"));
/*     */ 
/*     */ 
/*     */     
/* 306 */     for (byte b = 0; b <= 2; b++) {
/* 307 */       int i3 = -1;
/* 308 */       String str = "";
/* 309 */       if (b == 0) {
/* 310 */         i3 = n;
/* 311 */         str = str1;
/* 312 */       } else if (b == 1) {
/* 313 */         i3 = i1;
/* 314 */         str = str2;
/* 315 */       } else if (b == 2) {
/* 316 */         i3 = i2;
/* 317 */         str = str3;
/*     */       } 
/* 319 */       saveLayout_singleType(i, j, k, m, b, i3, str, this.user.getUID(), this.user.getLanguage());
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String saveLayout_print() {
/* 327 */     int i = Util.getIntValue(this.request.getParameter("wfid"), 0);
/* 328 */     int j = Util.getIntValue(this.request.getParameter("nodeid"), 0);
/* 329 */     int k = Util.getIntValue(this.request.getParameter("formid"), 0);
/* 330 */     int m = Util.getIntValue(this.request.getParameter("isbill"), 0);
/* 331 */     int n = Util.getIntValue(Util.null2String(this.request.getParameter("printhtmlid")), 0);
/* 332 */     String str = Util.null2String(this.request.getParameter("genprintsyncNodes"));
/* 333 */     return saveLayout_singleType(i, j, k, m, 1, n, str, this.user.getUID(), this.user.getLanguage());
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void syncLayoutToNodesNew(Map<String, Object> paramMap, User paramUser) {
/* 340 */     ModifyLogBiz modifyLogBiz = new ModifyLogBiz(paramMap, paramUser);
/* 341 */     modifyLogBiz.syncmode_before(paramMap);
/* 342 */     int i = Util.getIntValue((String)paramMap.get("wfid"));
/* 343 */     int j = Util.getIntValue((String)paramMap.get("layouttype"));
/* 344 */     int k = Util.getIntValue((String)paramMap.get("from_modeid"));
/* 345 */     String str = Util.null2String((String)paramMap.get("to_nodes"));
/* 346 */     ArrayList<String> arrayList = Util.TokenizerString(str, ",");
/* 347 */     for (byte b = 0; b < arrayList.size(); b++) {
/* 348 */       int m = Util.getIntValue(arrayList.get(b));
/* 349 */       syncLayoutToNode(i, j, k, m, paramUser.getUID(), paramUser.getLanguage());
/*     */     } 
/* 351 */     modifyLogBiz.writeBizLog();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String saveLayout_choose() {
/* 358 */     int i = Util.getIntValue(this.request.getParameter("wfid"), 0);
/* 359 */     int j = Util.getIntValue(this.request.getParameter("nodeid"), 0);
/* 360 */     int k = Util.getIntValue(this.request.getParameter("formid"), 0);
/* 361 */     int m = Util.getIntValue(this.request.getParameter("isbill"), 0);
/* 362 */     int n = Util.getIntValue(this.request.getParameter("layouttype"));
/* 363 */     int i1 = Util.getIntValue(this.request.getParameter("choose_layoutid"));
/* 364 */     int i2 = Util.getIntValue(this.request.getParameter("operuser"));
/* 365 */     int i3 = Util.getIntValue(this.request.getParameter("languageid"), 7);
/* 366 */     return saveLayout_singleType(i, j, k, m, n, i1, "", i2, i3);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String saveLayout_singleType(int paramInt1, int paramInt2, int paramInt3, int paramInt4, int paramInt5, int paramInt6, String paramString, int paramInt7, int paramInt8) {
/* 373 */     boolean bool = FormTemplateManager.isFormVirtualNode(paramInt2);
/* 374 */     if (!bool && (paramInt1 <= 0 || paramInt2 <= 0 || paramInt5 < 0)) {
/* 375 */       return "fault";
/*     */     }
/* 377 */     RecordSet recordSet = new RecordSet();
/* 378 */     int i = -1;
/* 379 */     recordSet.executeSql("select id from workflow_nodehtmllayout where workflowid=" + paramInt1 + " and nodeid=" + paramInt2 + " and type=" + paramInt5 + " and isactive=1");
/* 380 */     if (recordSet.next())
/* 381 */       i = Util.getIntValue(recordSet.getString("id")); 
/* 382 */     if (paramInt6 > 0) {
/*     */       
/* 384 */       if ((paramInt5 == 0 || paramInt5 == 2) && i == -1) {
/* 385 */         recordSet.executeQuery("select id from workflow_nodehtmllayout where nodeid=? and formid=? and type=? and isactive=1", new Object[] { Integer.valueOf(FormTemplateManager.getFORMVIRTUALNODEID()), Integer.valueOf(paramInt3), Integer.valueOf(paramInt5) });
/* 386 */         if (recordSet.next())
/* 387 */           i = Util.getIntValue(recordSet.getString("id")); 
/*     */       } 
/* 389 */       if (paramInt6 != i) {
/* 390 */         int j = -1;
/* 391 */         int k = -1;
/* 392 */         recordSet.executeSql("select nodeid,type from workflow_nodehtmllayout where id=" + paramInt6);
/* 393 */         if (recordSet.next()) {
/* 394 */           j = Util.getIntValue(recordSet.getString("nodeid"));
/* 395 */           k = Util.getIntValue(recordSet.getString("type"));
/*     */         } 
/* 397 */         if (paramInt2 == j && paramInt5 == k) {
/* 398 */           setLayoutToActive(paramInt1, paramInt2, paramInt3, paramInt4, paramInt5, paramInt6);
/*     */         } else {
/* 400 */           syncLayoutToNode(paramInt1, paramInt5, paramInt6, paramInt2, paramInt7, paramInt8);
/*     */         } 
/*     */       } 
/* 403 */       if (!"".equals(paramString)) {
/* 404 */         ArrayList<String> arrayList = Util.TokenizerString(paramString, ",");
/* 405 */         for (byte b = 0; b < arrayList.size(); b++) {
/* 406 */           int j = Util.getIntValue(arrayList.get(b));
/* 407 */           syncLayoutToNode(paramInt1, paramInt5, paramInt6, j, paramInt7, paramInt8);
/*     */         }
/*     */       
/*     */       } 
/* 411 */     } else if (i > 0) {
/* 412 */       clearLayoutActiveAttr(paramInt1, paramInt2, paramInt3, paramInt4, paramInt5);
/*     */       
/* 414 */       (new ExcelFormula()).clearFormulaWhenDeleteLayout(paramInt2, paramInt5, paramInt7);
/*     */     } 
/*     */     
/* 417 */     return "success";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void syncLayoutToNodes() {
/* 424 */     int i = Util.getIntValue(this.request.getParameter("wfid"));
/* 425 */     int j = Util.getIntValue(this.request.getParameter("layouttype"));
/* 426 */     int k = Util.getIntValue(this.request.getParameter("from_modeid"));
/* 427 */     String str = Util.null2String(this.request.getParameter("to_nodes"));
/* 428 */     ArrayList<String> arrayList = Util.TokenizerString(str, ",");
/* 429 */     for (byte b = 0; b < arrayList.size(); b++) {
/* 430 */       int m = Util.getIntValue(arrayList.get(b));
/* 431 */       syncLayoutToNode(i, j, k, m, this.user.getUID(), this.user.getLanguage());
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private int syncLayoutToNode(int paramInt1, int paramInt2, int paramInt3, int paramInt4, int paramInt5, int paramInt6) {
/* 439 */     boolean bool = FormTemplateManager.isFormVirtualNode(paramInt4);
/* 440 */     if (!bool && (paramInt1 <= 0 || paramInt4 <= 0 || paramInt2 < 0 || paramInt3 <= 0))
/* 441 */       return -1; 
/* 442 */     WFNodeFieldManager wFNodeFieldManager = new WFNodeFieldManager();
/* 443 */     RecordSet recordSet = new RecordSet();
/*     */     
/* 445 */     int i = 0;
/* 446 */     int j = 0;
/* 447 */     int k = -1;
/* 448 */     HtmlLayoutBean htmlLayoutBean = new HtmlLayoutBean();
/* 449 */     htmlLayoutBean.setWorkflowid(paramInt1);
/* 450 */     htmlLayoutBean.setNodeid(paramInt4);
/* 451 */     htmlLayoutBean.setType(paramInt2);
/* 452 */     htmlLayoutBean.setOperuser(paramInt5);
/*     */     
/* 454 */     String str1 = "select formid,isbill,nodeid,layoutname,syspath,cssfile,htmlparsescheme,version,datajson,pluginjson,scripts,isfixed,initscripts from workflow_nodehtmllayout where id=" + paramInt3;
/* 455 */     recordSet.executeSql(str1);
/* 456 */     if (recordSet.next()) {
/* 457 */       i = Util.getIntValue(recordSet.getString("nodeid"), 0);
/* 458 */       j = Util.getIntValue(recordSet.getString("formid"), 0);
/* 459 */       k = Util.getIntValue(recordSet.getString("isbill"), 0);
/* 460 */       htmlLayoutBean.setFormid(j);
/* 461 */       htmlLayoutBean.setIsbill(k);
/* 462 */       htmlLayoutBean.setLayoutname(getLayoutName(paramInt4, j, k, paramInt2, paramInt6));
/* 463 */       htmlLayoutBean.setSyspath(Util.null2String(recordSet.getString("syspath")));
/* 464 */       htmlLayoutBean.setCssfile(Util.getIntValue(recordSet.getString("cssfile"), 0));
/* 465 */       htmlLayoutBean.setHtmlparsescheme(Util.getIntValue(recordSet.getString("htmlparsescheme"), 0));
/* 466 */       htmlLayoutBean.setVersion(Util.getIntValue(recordSet.getString("version"), 0));
/* 467 */       htmlLayoutBean.setDatajson(Util.null2String(recordSet.getString("datajson")));
/* 468 */       htmlLayoutBean.setPluginjson(Util.null2String(recordSet.getString("pluginjson")));
/* 469 */       htmlLayoutBean.setScripts(Util.null2String(recordSet.getString("scripts")));
/* 470 */       htmlLayoutBean.setIsFixed(Util.getIntValue(recordSet.getString("isfixed"), 0));
/* 471 */       htmlLayoutBean.setInitscripts(recordSet.getString("initscripts"));
/*     */     } else {
/* 473 */       return -1;
/*     */     } 
/* 475 */     String str2 = "";
/* 476 */     if (htmlLayoutBean.getVersion() == 0 || htmlLayoutBean.getVersion() == 1) {
/* 477 */       String str3 = "";
/* 478 */       String str4 = "";
/* 479 */       if (!"".equals(htmlLayoutBean.getSyspath())) {
/* 480 */         str3 = wFNodeFieldManager.readHtmlFile(htmlLayoutBean.getSyspath());
/*     */       }
/* 482 */       recordSet.execute("select syspath from workflow_nodehtmllayout where workflowid=" + paramInt1 + " and nodeid=" + paramInt4 + " and type=" + paramInt2 + " and isactive=1");
/* 483 */       if (recordSet.next()) {
/* 484 */         String str = Util.null2String(recordSet.getString("syspath"));
/* 485 */         if (!"".equals(str)) {
/* 486 */           str4 = wFNodeFieldManager.readHtmlFile(str);
/*     */         }
/*     */       } 
/* 489 */       String str5 = wFNodeFieldManager.doTransLayout(str3, str4);
/*     */       
/* 491 */       str2 = wFNodeFieldManager.createHtmlFile(paramInt4, paramInt1, paramInt2, str5);
/* 492 */       htmlLayoutBean.setSyspath(str2);
/*     */     } 
/*     */     
/* 495 */     int m = operHtmlActiveLayout(htmlLayoutBean);
/*     */     
/* 497 */     (new ExcelFormula()).saveAsSyncLayoutFormulaNew(paramInt1, paramInt3, htmlLayoutBean.getNodeid(), m, paramInt2, paramInt5);
/*     */     
/* 499 */     if (paramInt2 == 0 && !bool) {
/* 500 */       String str3 = "";
/* 501 */       if (paramInt2 == 0)
/* 502 */         str3 = getNoSyncFields(paramInt1); 
/* 503 */       String str4 = "".equals(str3) ? "" : (" and fieldid not in (" + str3 + ") ");
/* 504 */       int n = -1;
/* 505 */       recordSet.execute("select f.nodetype from workflow_nodebase n left join workflow_flownode f on f.nodeid=n.id where n.id=" + paramInt4);
/* 506 */       if (recordSet.next()) {
/* 507 */         n = Util.getIntValue(recordSet.getString("nodetype"), -1);
/*     */       }
/* 509 */       int i1 = -1;
/* 510 */       recordSet.executeQuery("select f.nodetype from workflow_nodehtmllayout a left join workflow_flownode f on a.nodeid = f.nodeid where a.id = ?", new Object[] { Integer.valueOf(paramInt3) });
/* 511 */       if (recordSet.next()) {
/* 512 */         i1 = Util.getIntValue(recordSet.getString("nodetype"), -1);
/*     */       }
/* 514 */       recordSet.execute("delete from workflow_nodeform where nodeid=" + paramInt4 + str4);
/* 515 */       recordSet.execute("delete from workflow_nodeformgroup where nodeid=" + paramInt4);
/* 516 */       String str5 = "fieldid,isview,isedit,ismandatory,orderid,isalonerow,isorder,ordertype,orderindex,isonlyshow";
/* 517 */       String str6 = "groupid,isadd,isedit,isdelete,ishidenull,isdefault,isneed,isopensapmul,defaultrows,isprintserial,allowscroll,isopenpaging,isorder,ordertype,orderindex,mergefields,mobileallowscroll,mobilecardedit,iscopy,isprintthead";
/* 518 */       String str7 = "groupid,ishidenull,isopensapmul,isprintserial,allowscroll,isopenpaging,isorder,ordertype,orderindex,mergefields,mobileallowscroll,mobilecardedit,isprintthead";
/* 519 */       String[] arrayOfString = FormTemplateManager.getNodeFormParams(i, j, k, "", false);
/*     */       
/* 521 */       if (n == 0 || n == 1 || n == 2) {
/* 522 */         recordSet.execute("insert into workflow_nodeform(nodeid," + str5 + ") select " + paramInt4 + "," + str5 + " from " + arrayOfString[0] + " where " + arrayOfString[1] + str4);
/* 523 */         if (n != 0) {
/* 524 */           recordSet.executeUpdate("update workflow_nodeform set isedit = '0', ismandatory = '0' where nodeid = ? and fieldid = -10", new Object[] { Integer.valueOf(paramInt4) });
/*     */         }
/*     */       } else {
/* 527 */         recordSet.execute("insert into workflow_nodeform(nodeid," + str5 + ") select " + paramInt4 + ",fieldid,isview,'0','0',orderid,isalonerow,isorder,ordertype,orderindex,'0' from " + arrayOfString[0] + " where " + arrayOfString[1] + str4);
/* 528 */       }  if (FormTemplateManager.isFormVirtualNode(i)) {
/*     */         
/* 530 */         recordSet.execute("insert into workflow_nodeformgroup(nodeid," + str6 + ") select " + paramInt4 + "," + str6 + " from workflow_nodeformgroup_form where formid=" + j + " and isbill=" + k);
/*     */       } else {
/*     */         
/* 533 */         if (n == 3) {
/* 534 */           recordSet.execute("insert into workflow_nodeformgroup(nodeid," + str7 + ") select " + paramInt4 + "," + str7 + " from workflow_nodeformgroup where nodeid=" + i);
/*     */         } else {
/* 536 */           recordSet.execute("insert into workflow_nodeformgroup(nodeid," + str6 + ") select " + paramInt4 + "," + str6 + " from workflow_nodeformgroup where nodeid=" + i);
/*     */         } 
/* 538 */         if (n != 3) {
/* 539 */           recordSet.execute("delete from workflow_nodefieldattr where nodeid=" + paramInt4);
/* 540 */           String str = "fieldid,formid,isbill,attrcontent,caltype,othertype,transtype,datasourceid";
/* 541 */           recordSet.execute("insert into workflow_nodefieldattr(nodeid," + str + ") select " + paramInt4 + "," + str + " from workflow_nodefieldattr where nodeid=" + i);
/*     */         } 
/*     */         
/* 544 */         if (i1 == 3) {
/* 545 */           recordSet.executeUpdate("update workflow_nodeformgroup set isadd=0,isedit=0,isdelete=0,isdefault=0,isneed=0,defaultrows=1,iscopy=0 where nodeid = ?", new Object[] { Integer.valueOf(paramInt4) });
/*     */         }
/*     */       } 
/*     */       
/* 549 */       setNodeModeToHtml(paramInt1, paramInt4);
/*     */     } 
/*     */ 
/*     */     
/* 553 */     if (paramInt2 == 2 && !bool) {
/* 554 */       recordSet.executeQuery("select groupid,mobileallowscroll,mobilecardedit from workflow_nodeformgroup where nodeid=?", new Object[] { Integer.valueOf(i) });
/* 555 */       RecordSet recordSet1 = new RecordSet();
/* 556 */       while (recordSet.next()) {
/* 557 */         String str = recordSet.getString("groupid");
/* 558 */         recordSet1.executeQuery("select 1 from workflow_nodeformgroup where nodeid=? and groupid=?", new Object[] { Integer.valueOf(paramInt4), str });
/*     */         
/* 560 */         if (recordSet1.next()) {
/* 561 */           String str3 = recordSet.getString("mobileallowscroll");
/* 562 */           String str4 = recordSet.getString("mobilecardedit");
/* 563 */           recordSet1.executeUpdate("update workflow_nodeformgroup set mobileallowscroll=?,mobilecardedit=? where nodeid=? and  groupid=?", new Object[] { str3, str4, Integer.valueOf(paramInt4), str });
/*     */           continue;
/*     */         } 
/* 566 */         recordSet1.executeUpdate("insert into workflow_nodeformgroup(nodeid,groupid,mobileallowscroll,mobilecardedit) select " + paramInt4 + "," + str + ",mobileallowscroll,mobilecardedit from workflow_nodeformgroup where nodeid=" + i + "and groupid=?" + str, new Object[0]);
/*     */       } 
/*     */     } 
/*     */     
/* 570 */     SignInputSetUtil.synSignInputSet(paramInt1, paramInt3, paramInt4, m);
/* 571 */     return m;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int operHtmlActiveLayout(HtmlLayoutBean paramHtmlLayoutBean) {
/* 582 */     return operHtmlActiveLayout(paramHtmlLayoutBean, true);
/*     */   }
/*     */   public int operHtmlActiveLayout(HtmlLayoutBean paramHtmlLayoutBean, boolean paramBoolean) {
/* 585 */     return operHtmlActiveLayout(paramHtmlLayoutBean, paramBoolean, false);
/*     */   }
/*     */   public int operHtmlActiveLayout(HtmlLayoutBean paramHtmlLayoutBean, boolean paramBoolean1, boolean paramBoolean2) {
/* 588 */     int i = -1;
/* 589 */     ConnStatement connStatement = null;
/*     */     
/*     */     try {
/* 592 */       if (paramHtmlLayoutBean.getVersion() == 2 && (paramHtmlLayoutBean.getDatajson().length() < 5 || paramHtmlLayoutBean.getPluginjson().length() < 5)) {
/* 593 */         throw new Exception("operHtmlActiveLayout exception--" + paramHtmlLayoutBean.getNodeid() + "--datajson--" + paramHtmlLayoutBean.getDatajson() + "--pluginjson--" + paramHtmlLayoutBean.getPluginjson());
/*     */       }
/*     */ 
/*     */       
/* 597 */       if (paramHtmlLayoutBean.getVersion() == 2 && (!paramHtmlLayoutBean.getDatajson().startsWith("{") || !paramHtmlLayoutBean.getDatajson().endsWith("}") || !paramHtmlLayoutBean.getPluginjson().startsWith("{") || !paramHtmlLayoutBean.getPluginjson().endsWith("}"))) {
/* 598 */         throw new Exception("operHtmlActiveLayout exception--" + paramHtmlLayoutBean.getNodeid() + "--datajson--" + paramHtmlLayoutBean.getDatajson() + "--pluginjson--" + paramHtmlLayoutBean.getPluginjson());
/*     */       }
/* 600 */       SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
/* 601 */       String str1 = simpleDateFormat.format(new Date());
/* 602 */       if (FormTemplateManager.isFormVirtualNode(paramHtmlLayoutBean.getNodeid())) {
/* 603 */         paramHtmlLayoutBean.setWorkflowid(0);
/*     */       }
/* 605 */       connStatement = new ConnStatement();
/* 606 */       RecordSet recordSet = new RecordSet();
/* 607 */       String str2 = "select id,scripts,initscripts from workflow_nodehtmllayout where nodeid=" + paramHtmlLayoutBean.getNodeid() + " and formid=" + paramHtmlLayoutBean.getFormid() + " and isbill=" + paramHtmlLayoutBean.getIsbill() + " and type=" + paramHtmlLayoutBean.getType() + " and isactive=1";
/* 608 */       if (!FormTemplateManager.isFormVirtualNode(paramHtmlLayoutBean.getNodeid())) {
/* 609 */         str2 = str2 + " and workflowid = " + paramHtmlLayoutBean.getWorkflowid();
/*     */       }
/* 611 */       recordSet.execute(str2);
/* 612 */       if (recordSet.next() && !paramBoolean2) {
/* 613 */         i = Util.getIntValue(recordSet.getString("id"), 0);
/* 614 */         str2 = "update workflow_nodehtmllayout set layoutname=?,syspath=?,cssfile=?,htmlparsescheme=?,version=?,operuser=?,opertime=?,datajson=?,pluginjson=?,scripts=?,initscripts=?,isFixed=? where id=?";
/*     */         
/* 616 */         connStatement.setStatementSql(str2);
/* 617 */         connStatement.setString(1, paramHtmlLayoutBean.getLayoutname());
/* 618 */         connStatement.setString(2, paramHtmlLayoutBean.getSyspath());
/* 619 */         connStatement.setInt(3, paramHtmlLayoutBean.getCssfile());
/* 620 */         connStatement.setInt(4, paramHtmlLayoutBean.getHtmlparsescheme());
/* 621 */         connStatement.setInt(5, paramHtmlLayoutBean.getVersion());
/* 622 */         connStatement.setInt(6, paramHtmlLayoutBean.getOperuser());
/* 623 */         connStatement.setString(7, str1);
/* 624 */         connStatement.setString(8, paramHtmlLayoutBean.getDatajson());
/* 625 */         connStatement.setString(9, paramHtmlLayoutBean.getPluginjson());
/* 626 */         if (paramBoolean1) {
/* 627 */           connStatement.setString(10, paramHtmlLayoutBean.getScripts());
/* 628 */           connStatement.setString(11, paramHtmlLayoutBean.getInitscripts());
/*     */         } else {
/* 630 */           connStatement.setString(10, Util.null2String(recordSet.getString("scripts")));
/* 631 */           connStatement.setString(11, Util.null2String(recordSet.getString("initscripts")));
/*     */         } 
/* 633 */         connStatement.setInt(12, paramHtmlLayoutBean.getIsFixed());
/* 634 */         connStatement.setInt(13, i);
/* 635 */         connStatement.executeUpdate();
/*     */       } else {
/* 637 */         str2 = "insert into workflow_nodehtmllayout(workflowid,formid,isbill,nodeid,type,layoutname,syspath,cssfile,htmlparsescheme,version,operuser,opertime,datajson,pluginjson,scripts,initscripts,isactive,isFixed) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
/*     */         
/* 639 */         connStatement.setStatementSql(str2);
/* 640 */         connStatement.setInt(1, paramHtmlLayoutBean.getWorkflowid());
/* 641 */         connStatement.setInt(2, paramHtmlLayoutBean.getFormid());
/* 642 */         connStatement.setInt(3, paramHtmlLayoutBean.getIsbill());
/* 643 */         connStatement.setInt(4, paramHtmlLayoutBean.getNodeid());
/* 644 */         connStatement.setInt(5, paramHtmlLayoutBean.getType());
/* 645 */         connStatement.setString(6, paramHtmlLayoutBean.getLayoutname());
/* 646 */         connStatement.setString(7, paramHtmlLayoutBean.getSyspath());
/* 647 */         connStatement.setInt(8, paramHtmlLayoutBean.getCssfile());
/* 648 */         connStatement.setInt(9, paramHtmlLayoutBean.getHtmlparsescheme());
/* 649 */         connStatement.setInt(10, paramHtmlLayoutBean.getVersion());
/* 650 */         connStatement.setInt(11, paramHtmlLayoutBean.getOperuser());
/* 651 */         connStatement.setString(12, str1);
/* 652 */         connStatement.setString(13, paramHtmlLayoutBean.getDatajson());
/* 653 */         connStatement.setString(14, paramHtmlLayoutBean.getPluginjson());
/* 654 */         connStatement.setString(15, paramHtmlLayoutBean.getScripts());
/* 655 */         connStatement.setString(16, paramHtmlLayoutBean.getInitscripts());
/* 656 */         connStatement.setInt(17, 1);
/* 657 */         connStatement.setInt(18, paramHtmlLayoutBean.getIsFixed());
/* 658 */         connStatement.executeUpdate();
/*     */         
/* 660 */         str2 = "select max(id) from workflow_nodehtmllayout where nodeid=" + paramHtmlLayoutBean.getNodeid() + " and formid=" + paramHtmlLayoutBean.getFormid() + " and isbill=" + paramHtmlLayoutBean.getIsbill() + " and type=" + paramHtmlLayoutBean.getType() + " and isactive=1";
/* 661 */         connStatement.setStatementSql(str2);
/* 662 */         connStatement.executeQuery();
/* 663 */         if (connStatement.next()) {
/* 664 */           i = Util.getIntValue(connStatement.getString(1), 0);
/*     */         }
/*     */         
/* 667 */         if (paramHtmlLayoutBean.getType() == 1) {
/* 668 */           String str = "insert into workflow_printset(workflowid,nodeid,type,modeid,isactive) values(?,?,?,?,1)";
/* 669 */           (new RecordSet()).executeUpdate(str, new Object[] { Integer.valueOf(paramHtmlLayoutBean.getWorkflowid()), Integer.valueOf(paramHtmlLayoutBean.getNodeid()), Integer.valueOf(1), Integer.valueOf(i) });
/*     */         } 
/*     */       } 
/* 672 */     } catch (Exception exception) {
/* 673 */       writeLog(exception);
/*     */     } finally {
/*     */       try {
/* 676 */         connStatement.close();
/* 677 */       } catch (Exception exception) {
/* 678 */         writeLog(exception);
/*     */       } 
/*     */     } 
/* 681 */     if (i > 0)
/* 682 */       (new ScriptManagerService()).managerScriptSplit(i, true); 
/* 683 */     return i;
/*     */   }
/*     */   
/*     */   public int operFreeNodeHtmlActiveLayout(HtmlLayoutBean paramHtmlLayoutBean, boolean paramBoolean1, boolean paramBoolean2, int paramInt) {
/* 687 */     int i = -1;
/* 688 */     ConnStatement connStatement = null;
/*     */     
/*     */     try {
/* 691 */       if (paramHtmlLayoutBean.getVersion() == 2 && paramHtmlLayoutBean.getDatajson().length() < 5) {
/* 692 */         throw new Exception("operFreeNodeHtmlActiveLayout exception--" + paramHtmlLayoutBean.getNodeid() + "--datajson--" + paramHtmlLayoutBean.getDatajson());
/*     */       }
/*     */ 
/*     */       
/* 696 */       if (paramHtmlLayoutBean.getVersion() == 2 && (!paramHtmlLayoutBean.getDatajson().startsWith("{") || !paramHtmlLayoutBean.getDatajson().endsWith("}"))) {
/* 697 */         throw new Exception("operFreeNodeHtmlActiveLayout exception--" + paramHtmlLayoutBean.getNodeid() + "--datajson--" + paramHtmlLayoutBean.getDatajson());
/*     */       }
/*     */       
/* 700 */       connStatement = new ConnStatement();
/* 701 */       RecordSet recordSet = new RecordSet();
/* 702 */       String str = "select id from workflow_freenode_htmllayout where nodeid=" + paramHtmlLayoutBean.getNodeid() + " and formid=" + paramHtmlLayoutBean.getFormid() + " and isbill=" + paramHtmlLayoutBean.getIsbill() + " and type=" + paramHtmlLayoutBean.getType() + " and layoutconfigid=" + paramInt;
/* 703 */       recordSet.execute(str);
/* 704 */       if (recordSet.next() && !paramBoolean2) {
/* 705 */         i = Util.getIntValue(recordSet.getString("id"), 0);
/* 706 */         str = "update workflow_freenode_htmllayout set layoutname=?,cssfile=?,htmlparsescheme=?,datajson=? where id=?";
/*     */         
/* 708 */         connStatement.setStatementSql(str);
/* 709 */         connStatement.setString(1, paramHtmlLayoutBean.getLayoutname());
/* 710 */         connStatement.setInt(2, paramHtmlLayoutBean.getCssfile());
/* 711 */         connStatement.setInt(3, paramHtmlLayoutBean.getHtmlparsescheme());
/* 712 */         connStatement.setString(4, paramHtmlLayoutBean.getDatajson());
/* 713 */         connStatement.setInt(5, i);
/* 714 */         connStatement.executeUpdate();
/*     */       } else {
/* 716 */         str = "insert into workflow_freenode_htmllayout(workflowid,formid,isbill,nodeid,type,layoutname,cssfile,htmlparsescheme,datajson,layoutconfigid) values (?,?,?,?,?,?,?,?,?,?)";
/*     */         
/* 718 */         connStatement.setStatementSql(str);
/* 719 */         connStatement.setInt(1, paramHtmlLayoutBean.getWorkflowid());
/* 720 */         connStatement.setInt(2, paramHtmlLayoutBean.getFormid());
/* 721 */         connStatement.setInt(3, paramHtmlLayoutBean.getIsbill());
/* 722 */         connStatement.setInt(4, paramHtmlLayoutBean.getNodeid());
/* 723 */         connStatement.setInt(5, paramHtmlLayoutBean.getType());
/* 724 */         connStatement.setString(6, paramHtmlLayoutBean.getLayoutname());
/* 725 */         connStatement.setInt(7, paramHtmlLayoutBean.getCssfile());
/* 726 */         connStatement.setInt(8, paramHtmlLayoutBean.getHtmlparsescheme());
/* 727 */         connStatement.setString(9, paramHtmlLayoutBean.getDatajson());
/* 728 */         connStatement.setInt(10, paramInt);
/* 729 */         connStatement.executeUpdate();
/*     */         
/* 731 */         str = "select max(id) from workflow_freenode_htmllayout where nodeid=" + paramHtmlLayoutBean.getNodeid() + " and formid=" + paramHtmlLayoutBean.getFormid() + " and isbill=" + paramHtmlLayoutBean.getIsbill() + " and type=" + paramHtmlLayoutBean.getType() + " and layoutconfigid=" + paramInt;
/* 732 */         connStatement.setStatementSql(str);
/* 733 */         connStatement.executeQuery();
/* 734 */         if (connStatement.next())
/* 735 */           i = Util.getIntValue(connStatement.getString(1), 0); 
/*     */       } 
/* 737 */     } catch (Exception exception) {
/* 738 */       writeLog(exception);
/*     */     } finally {
/*     */       try {
/* 741 */         connStatement.close();
/* 742 */       } catch (Exception exception) {
/* 743 */         writeLog(exception);
/*     */       } 
/*     */     } 
/* 746 */     return i;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int updateHtmlLayout(HtmlLayoutBean paramHtmlLayoutBean, int paramInt) {
/* 753 */     ConnStatement connStatement = null;
/*     */     try {
/* 755 */       SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
/* 756 */       String str1 = simpleDateFormat.format(new Date());
/* 757 */       if (FormTemplateManager.isFormVirtualNode(paramHtmlLayoutBean.getNodeid())) {
/* 758 */         paramHtmlLayoutBean.setWorkflowid(0);
/*     */       }
/* 760 */       connStatement = new ConnStatement();
/* 761 */       RecordSet recordSet = new RecordSet();
/* 762 */       String str2 = "select id from workflow_nodehtmllayout where nodeid=" + paramHtmlLayoutBean.getNodeid() + " and type=" + paramHtmlLayoutBean.getType() + " and id=" + paramInt;
/* 763 */       recordSet.execute(str2);
/* 764 */       if (recordSet.next()) {
/* 765 */         str2 = "update workflow_nodehtmllayout set layoutname=?,syspath=?,cssfile=?,htmlparsescheme=?,version=?,operuser=?,opertime=?,datajson=?,pluginjson=?,scripts=?,isFixed=? where id=?";
/*     */         
/* 767 */         connStatement.setStatementSql(str2);
/* 768 */         connStatement.setString(1, paramHtmlLayoutBean.getLayoutname());
/* 769 */         connStatement.setString(2, paramHtmlLayoutBean.getSyspath());
/* 770 */         connStatement.setInt(3, paramHtmlLayoutBean.getCssfile());
/* 771 */         connStatement.setInt(4, paramHtmlLayoutBean.getHtmlparsescheme());
/* 772 */         connStatement.setInt(5, paramHtmlLayoutBean.getVersion());
/* 773 */         connStatement.setInt(6, paramHtmlLayoutBean.getOperuser());
/* 774 */         connStatement.setString(7, str1);
/* 775 */         connStatement.setString(8, paramHtmlLayoutBean.getDatajson());
/* 776 */         connStatement.setString(9, paramHtmlLayoutBean.getPluginjson());
/* 777 */         connStatement.setString(10, paramHtmlLayoutBean.getScripts());
/* 778 */         connStatement.setInt(11, paramHtmlLayoutBean.getIsFixed());
/* 779 */         connStatement.setInt(12, paramInt);
/* 780 */         connStatement.executeUpdate();
/*     */       } else {
/* 782 */         paramInt = -1;
/*     */       } 
/* 784 */     } catch (Exception exception) {
/* 785 */       writeLog(exception);
/*     */     } finally {
/*     */       try {
/* 788 */         connStatement.close();
/* 789 */       } catch (Exception exception) {
/* 790 */         writeLog(exception);
/*     */       } 
/*     */     } 
/* 793 */     if (paramInt > 0)
/* 794 */       (new ScriptManagerService()).managerScriptSplit(paramInt, true); 
/* 795 */     return paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String getNoSyncFields(int paramInt) {
/* 802 */     String str = "";
/*     */     try {
/* 804 */       WFManager wFManager = new WFManager();
/* 805 */       wFManager.reset();
/* 806 */       wFManager.setWfid(paramInt);
/* 807 */       wFManager.getWfInfo();
/* 808 */       str = wFManager.getNosynfields();
/* 809 */     } catch (Exception exception) {}
/*     */     
/* 811 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getLayoutName(int paramInt1, int paramInt2, int paramInt3) {
/* 818 */     return getLayoutName(paramInt1, 0, -1, paramInt2, paramInt3);
/*     */   }
/*     */   public String getLayoutName(int paramInt1, int paramInt2, int paramInt3, int paramInt4, int paramInt5) {
/* 821 */     String str = "";
/* 822 */     if (FormTemplateManager.isFormVirtualNode(paramInt1)) {
/* 823 */       str = (new FormTemplateManager()).getFormName(paramInt2, paramInt3, paramInt5);
/*     */     } else {
/* 825 */       RecordSet recordSet = new RecordSet();
/* 826 */       recordSet.execute("select nodename from workflow_nodebase where id=" + paramInt1);
/* 827 */       if (recordSet.next())
/* 828 */         str = Util.null2String(recordSet.getString("nodename")); 
/*     */     } 
/* 830 */     if (paramInt4 == 0) {
/* 831 */       str = str + SystemEnv.getHtmlLabelName(16450, paramInt5);
/* 832 */     } else if (paramInt4 == 1) {
/* 833 */       str = str + SystemEnv.getHtmlLabelName(128952, Util.getIntValue(paramInt5));
/* 834 */     } else if (paramInt4 == 2) {
/* 835 */       str = str + SystemEnv.getHtmlLabelName(125554, paramInt5);
/* 836 */     }  return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String setLayoutToActive(int paramInt1, int paramInt2, int paramInt3, int paramInt4, int paramInt5, int paramInt6) {
/* 843 */     clearLayoutActiveAttr(paramInt1, paramInt2, paramInt3, paramInt4, paramInt5);
/*     */     
/* 845 */     RecordSet recordSet = new RecordSet();
/* 846 */     String str = "update workflow_nodehtmllayout set isactive=1 where nodeid=" + paramInt2 + " and type=" + paramInt5 + " and id=" + paramInt6;
/* 847 */     recordSet.executeSql(str);
/* 848 */     return "success";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void clearLayoutActiveAttr(int paramInt1, int paramInt2, int paramInt3, int paramInt4, int paramInt5) {
/* 855 */     RecordSet recordSet = new RecordSet();
/* 856 */     String str = "update workflow_nodehtmllayout set isactive=0 where nodeid=" + paramInt2 + " and type=" + paramInt5;
/* 857 */     if (FormTemplateManager.isFormVirtualNode(paramInt2)) {
/* 858 */       str = str + " and formid=" + paramInt3 + " and isbill=" + paramInt4;
/*     */     } else {
/* 860 */       str = str + " and workflowid=" + paramInt1;
/* 861 */     }  recordSet.executeSql(str);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int setNodeModeToHtml(int paramInt1, int paramInt2) {
/* 868 */     byte b = -1;
/*     */     try {
/* 870 */       RecordSet recordSet = new RecordSet();
/* 871 */       recordSet.executeSql("update workflow_flownode set ismode='2' where workflowid=" + paramInt1 + " and nodeid=" + paramInt2);
/* 872 */     } catch (Exception exception) {
/* 873 */       b = 0;
/*     */     } 
/* 875 */     return b;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getActiveHtmlLayout(int paramInt1, int paramInt2, int paramInt3) {
/* 884 */     return getActiveHtmlLayout(paramInt1, paramInt2, paramInt3, true);
/*     */   }
/*     */   public int getActiveHtmlLayout(int paramInt1, int paramInt2, int paramInt3, boolean paramBoolean) {
/* 887 */     int i = 0;
/* 888 */     int j = -1;
/* 889 */     if (paramBoolean) {
/* 890 */       RecordSet recordSet = new RecordSet();
/* 891 */       recordSet.executeSql("select formid,isbill from workflow_base where id=" + paramInt1);
/* 892 */       if (recordSet.next()) {
/* 893 */         i = Util.getIntValue(recordSet.getString("formid"));
/* 894 */         j = Util.getIntValue(recordSet.getString("isbill"));
/*     */       } 
/*     */     } 
/* 897 */     return getActiveHtmlLayout(paramInt1, paramInt2, paramInt3, paramBoolean, i, j);
/*     */   }
/*     */   public int getActiveHtmlLayout(int paramInt1, int paramInt2, int paramInt3, boolean paramBoolean, int paramInt4, int paramInt5) {
/* 900 */     if (paramInt1 < 0 || paramInt2 < 0 || paramInt3 < 0)
/* 901 */       return 0; 
/* 902 */     int i = 0;
/* 903 */     RecordSet recordSet = new RecordSet();
/* 904 */     recordSet.executeSql("select id from workflow_nodehtmllayout where workflowid=" + paramInt1 + " and nodeid=" + paramInt2 + " and type=" + paramInt3 + " and isactive=1 and formid=" + paramInt4 + " order by id desc");
/* 905 */     if (recordSet.next())
/* 906 */       i = Util.getIntValue(recordSet.getString("id"), 0); 
/* 907 */     if (paramBoolean && i < 1) {
/* 908 */       recordSet.executeSql("select id from workflow_nodehtmllayout where nodeid=" + FormTemplateManager.getFORMVIRTUALNODEID() + " and formid=" + paramInt4 + " and isbill=" + paramInt5 + " and type=" + paramInt3 + " and isactive=1");
/*     */       
/* 910 */       if (recordSet.next())
/* 911 */         i = Util.getIntValue(recordSet.getString("id"), 0); 
/*     */     } 
/* 913 */     return i;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getLayoutVersion(int paramInt) {
/* 920 */     int i = -1;
/* 921 */     if (paramInt > 0) {
/* 922 */       RecordSet recordSet = new RecordSet();
/* 923 */       recordSet.executeSql("select version from workflow_nodehtmllayout where id=" + paramInt);
/* 924 */       if (recordSet.next())
/* 925 */         i = Util.getIntValue(recordSet.getString("version")); 
/*     */     } 
/* 927 */     return i;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean judgeHaveHtmlLayout(int paramInt1, int paramInt2, int paramInt3) {
/* 934 */     if (paramInt1 > 0 && paramInt2 > 0 && paramInt3 >= 0) {
/* 935 */       RecordSet recordSet = new RecordSet();
/* 936 */       recordSet.executeSql("select id from workflow_nodehtmllayout where workflowid=" + paramInt1 + " and nodeid=" + paramInt2 + " and type=" + paramInt3 + " and isactive=1");
/* 937 */       if (recordSet.next())
/* 938 */         return true; 
/* 939 */       recordSet.executeSql("select formid,isbill from workflow_base where id=" + paramInt1);
/* 940 */       if (recordSet.next()) {
/* 941 */         int i = Util.getIntValue("formid");
/* 942 */         int j = Util.getIntValue("isbill");
/* 943 */         recordSet.executeSql("select id from workflow_nodehtmllayout where nodeid=" + FormTemplateManager.getFORMVIRTUALNODEID() + " and formid=" + i + " and isbill=" + j + " and type=" + paramInt3 + " and isactive=1");
/* 944 */         if (recordSet.next())
/* 945 */           return true; 
/*     */       } 
/*     */     } 
/* 948 */     return false;
/*     */   }
/*     */ 
/*     */   
/*     */   public HttpServletRequest getRequest() {
/* 953 */     return this.request;
/*     */   }
/*     */   
/*     */   public void setRequest(HttpServletRequest paramHttpServletRequest) {
/* 957 */     this.request = paramHttpServletRequest;
/*     */   }
/*     */   
/*     */   public User getUser() {
/* 961 */     return this.user;
/*     */   }
/*     */   
/*     */   public void setUser(User paramUser) {
/* 965 */     this.user = paramUser;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/exceldesign/HtmlLayoutOperate.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */