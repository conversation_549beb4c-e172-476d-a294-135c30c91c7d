/*     */ package weaver.workflow.exceldesign;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import org.json.JSONArray;
/*     */ import org.json.JSONException;
/*     */ import org.json.JSONObject;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class DetailOrderManager
/*     */ {
/*     */   private static final String ORDER_ASC_SHOW = " <font style='color: red;font-weight: bolder;'>↑</font>";
/*     */   private static final String ORDER_DESC_SHOW = " <font style='color: red;font-weight: bolder;'>↓</font>";
/*     */   private static final String ORDER_ASC_DB = " asc";
/*     */   private static final String ORDER_DESC_DB = " desc";
/*  37 */   private JSONObject detailFieldsJson = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getOrderDescription(String paramString1, String paramString2, String paramString3, String paramString4, String paramString5, String paramString6) {
/*  51 */     String str = "";
/*     */     
/*  53 */     List<Map<String, String>> list = new ArrayList();
/*     */     try {
/*  55 */       list = getFieldMap(paramString1, paramString2, paramString3, paramString4, paramString5, paramString6);
/*  56 */     } catch (Exception exception) {
/*  57 */       exception.printStackTrace();
/*     */     } 
/*  59 */     for (Map<String, String> map : list) {
/*  60 */       String str1 = (String)map.get("ordertype");
/*  61 */       if ("".equals(str1)) {
/*     */         break;
/*     */       }
/*  64 */       String str2 = (String)map.get("fieldname");
/*  65 */       str = str + "，" + str2;
/*  66 */       if ("0".equals(str1)) {
/*  67 */         str = str + " <font style='color: red;font-weight: bolder;'>↑</font>"; continue;
/*     */       } 
/*  69 */       str = str + " <font style='color: red;font-weight: bolder;'>↓</font>";
/*     */     } 
/*     */ 
/*     */     
/*  73 */     if (!"".equals(str))
/*  74 */       str = str.substring(1); 
/*  75 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getOrderSql(String paramString1, String paramString2, String paramString3, String paramString4, String paramString5, String paramString6, String paramString7, String paramString8, String paramString9) {
/*  93 */     String str1 = "";
/*  94 */     boolean bool = false;
/*     */     
/*  96 */     String str2 = "";
/*  97 */     if ("1".equals(paramString4)) {
/*  98 */       str2 = getTableIndex(paramString3, paramString7);
/*     */     } else {
/* 100 */       str2 = paramString6;
/*     */     } 
/* 102 */     List<Map<String, String>> list = new ArrayList();
/*     */     try {
/* 104 */       list = getFieldMap(paramString1, paramString2, paramString3, paramString4, paramString5, str2);
/* 105 */     } catch (Exception exception) {
/* 106 */       exception.printStackTrace();
/*     */     } 
/* 108 */     for (Map<String, String> map : list) {
/* 109 */       String str3 = (String)map.get("ordertype");
/* 110 */       if ("".equals(str3)) {
/*     */         continue;
/*     */       }
/* 113 */       String str4 = (String)map.get("fieldname_db");
/* 114 */       String str5 = (String)map.get("fieldid");
/* 115 */       if ("-1".equals(str5)) {
/* 116 */         bool = true;
/* 117 */         str4 = paramString9;
/*     */       } 
/* 119 */       str1 = str1 + "," + paramString8 + "." + str4;
/* 120 */       if ("0".equals(str3)) {
/* 121 */         str1 = str1 + " asc"; continue;
/*     */       } 
/* 123 */       str1 = str1 + " desc";
/*     */     } 
/*     */ 
/*     */     
/* 127 */     if (!"".equals(str1)) {
/* 128 */       str1 = str1.substring(1);
/*     */     }
/*     */     
/* 131 */     if (!bool) {
/* 132 */       if ("".equals(str1)) {
/* 133 */         str1 = paramString8 + "." + paramString9;
/*     */       } else {
/* 135 */         str1 = str1 + "," + paramString8 + "." + paramString9;
/*     */       } 
/*     */     }
/*     */     
/* 139 */     return str1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String getTableIndex(String paramString1, String paramString2) {
/* 150 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 151 */     RecordSet recordSet = new RecordSet();
/* 152 */     recordSet.executeQuery("select tablename from workflow_billdetailtable where billid=? order by orderid", new Object[] { paramString1 });
/* 153 */     byte b = 1;
/* 154 */     while (recordSet.next()) {
/* 155 */       String str1 = recordSet.getString("tablename");
/* 156 */       hashMap.put(str1, b + "");
/* 157 */       b++;
/*     */     } 
/* 159 */     String str = (String)hashMap.get(paramString2);
/* 160 */     if ("".equals(str)) {
/* 161 */       str = "-1";
/*     */     }
/* 163 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<Map<String, String>> getFieldMap(String paramString1, String paramString2, String paramString3, String paramString4, String paramString5, String paramString6) throws Exception {
/* 180 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*     */     
/* 182 */     JSONArray jSONArray = getDetailField(paramString1, paramString2, paramString3, paramString4, paramString5, paramString6);
/*     */     
/* 184 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 185 */     for (byte b = 0; b < jSONArray.length(); b++) {
/* 186 */       JSONObject jSONObject = (JSONObject)jSONArray.get(b);
/* 187 */       hashMap.put(jSONObject.getString("fieldid"), jSONObject);
/*     */     } 
/*     */     
/* 190 */     ArrayList<String> arrayList1 = new ArrayList();
/*     */ 
/*     */     
/* 193 */     RecordSet recordSet = new RecordSet();
/* 194 */     recordSet.execute("select fieldid,ordertype,nodeid from (select fieldid,ordertype,nodeid,orderindex from workflow_nodeform where nodeid = " + paramString2 + " and isorder = 1 union select -1,ordertype,nodeid,orderindex from workflow_nodeformgroup where nodeid = " + paramString2 + " and isorder = 1 and groupid = " + (Util.getIntValue(paramString6) - 1) + ") t order by orderindex");
/* 195 */     while (recordSet.next()) {
/* 196 */       String str1 = recordSet.getString(1);
/* 197 */       String str2 = Util.null2String(recordSet.getString(2));
/* 198 */       String str3 = Util.null2String(recordSet.getString(3));
/*     */       
/* 200 */       if (!"-1".equals(str1)) {
/* 201 */         JSONObject jSONObject = (JSONObject)hashMap.get(str1);
/* 202 */         if (jSONObject == null) {
/*     */           continue;
/*     */         }
/* 205 */         String str = jSONObject.getString("fielddbtype").toLowerCase();
/*     */         
/* 207 */         if (!isCanOrder(jSONObject)) {
/*     */           continue;
/*     */         }
/* 210 */         arrayList1.add(str1);
/* 211 */         HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 212 */         arrayList.add(hashMap2);
/* 213 */         hashMap2.put("fieldid", str1);
/* 214 */         hashMap2.put("fieldname", jSONObject.getString("fieldname"));
/* 215 */         hashMap2.put("fieldname_db", jSONObject.getString("fieldname_db"));
/* 216 */         hashMap2.put("ordertype", str2);
/* 217 */         hashMap2.put("fielddbtype", str); continue;
/*     */       } 
/* 219 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 220 */       arrayList.add(hashMap1);
/* 221 */       hashMap1.put("fieldid", "-1");
/* 222 */       hashMap1.put("fieldname", SystemEnv.getHtmlLabelName(384230, Util.getIntValue(paramString5)));
/* 223 */       hashMap1.put("fieldname_db", "id");
/* 224 */       hashMap1.put("ordertype", str2);
/* 225 */       hashMap1.put("fielddbtype", "integer");
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 231 */     return (List)arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private JSONArray getDetailField(String paramString1, String paramString2, String paramString3, String paramString4, String paramString5, String paramString6) throws Exception {
/* 250 */     ExcelLayoutManager excelLayoutManager = new ExcelLayoutManager();
/* 251 */     if (this.detailFieldsJson == null) {
/* 252 */       this.detailFieldsJson = excelLayoutManager.getDetailFields(Util.getIntValue(paramString3), Util.getIntValue(paramString2), Util.getIntValue(paramString4), Util.getIntValue(paramString5), true);
/*     */     }
/* 254 */     JSONArray jSONArray1 = this.detailFieldsJson.getJSONArray("detail_" + paramString6);
/*     */ 
/*     */     
/* 257 */     JSONObject jSONObject = new JSONObject();
/* 258 */     jSONObject.put("fieldid", -1);
/* 259 */     jSONObject.put("fieldname", SystemEnv.getHtmlLabelName(384230, Util.getIntValue(paramString5)));
/* 260 */     jSONObject.put("fielddbtype", "integer");
/* 261 */     jSONObject.put("fieldnamepy", "MXID");
/* 262 */     jSONObject.put("fieldtypedetail", "1");
/* 263 */     jSONObject.put("fieldtype", "1");
/* 264 */     jSONObject.put("fieldname_db", "id");
/*     */     
/* 266 */     JSONArray jSONArray2 = new JSONArray();
/* 267 */     jSONArray2.put(jSONObject);
/* 268 */     for (byte b = 0; b < jSONArray1.length(); b++) {
/* 269 */       jSONArray2.put(jSONArray1.get(b));
/*     */     }
/* 271 */     return jSONArray2;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<Map<String, String>> getUnSortFieldMap(String paramString1, String paramString2, String paramString3, String paramString4, String paramString5, String paramString6, String paramString7, String paramString8) throws Exception {
/* 289 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*     */     
/* 291 */     JSONArray jSONArray = getDetailField(paramString1, paramString2, paramString3, paramString4, paramString5, paramString6);
/*     */     
/* 293 */     ArrayList arrayList1 = Util.TokenizerString(paramString7, ",");
/*     */ 
/*     */     
/* 296 */     for (byte b = 0; b < jSONArray.length(); b++) {
/* 297 */       JSONObject jSONObject = (JSONObject)jSONArray.get(b);
/* 298 */       String str1 = jSONObject.getString("fieldid");
/* 299 */       String str2 = jSONObject.getString("fielddbtype");
/* 300 */       String str3 = jSONObject.getString("fieldname");
/* 301 */       if (isCanOrder(jSONObject))
/*     */       {
/*     */         
/* 304 */         if (!arrayList1.contains(str1)) {
/*     */ 
/*     */ 
/*     */           
/* 308 */           paramString8 = Util.null2String(paramString8).trim();
/* 309 */           if ("".equals(paramString8) || str3.indexOf(paramString8) != -1) {
/*     */ 
/*     */ 
/*     */             
/* 313 */             HashMap<Object, Object> hashMap = new HashMap<>();
/* 314 */             arrayList.add(hashMap);
/* 315 */             hashMap.put("fieldid", str1);
/* 316 */             hashMap.put("fieldname", str3);
/* 317 */             hashMap.put("fieldname_db", jSONObject.getString("fieldname_db"));
/* 318 */             hashMap.put("ordertype", "");
/* 319 */             hashMap.put("fielddbtype", str2);
/*     */           } 
/*     */         }  } 
/* 322 */     }  return (List)arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private boolean isCanOrder(JSONObject paramJSONObject) {
/* 331 */     boolean bool = true;
/*     */     
/* 333 */     String str1 = "";
/* 334 */     String str2 = "";
/* 335 */     String str3 = "";
/*     */     try {
/* 337 */       str1 = Util.null2String(paramJSONObject.getString("fielddbtype")).trim().toLowerCase();
/* 338 */       str2 = Util.null2String(paramJSONObject.getString("fieldtype"));
/* 339 */       str3 = Util.null2String(paramJSONObject.getString("fieldtypedetail"));
/* 340 */     } catch (JSONException jSONException) {
/* 341 */       jSONException.printStackTrace();
/*     */     } 
/*     */     
/* 344 */     if ("3".equals(str2) && ("161".equals(str3) || "162".equals(str3) || "256".equals(str3) || "257".equals(str3))) {
/* 345 */       bool = false;
/*     */     } else {
/* 347 */       String str = Util.null2String((new BaseBean()).getPropValue("detailOrder", "unsupporteType"));
/* 348 */       if ("".equals(str)) {
/* 349 */         if ("text".equals(str1) || "clob".equals(str1) || "long".equals(str1))
/* 350 */           bool = false; 
/*     */       } else {
/* 352 */         str = "," + str.toLowerCase() + ",";
/* 353 */         if (str.indexOf("," + str1.toLowerCase() + ",") > -1)
/* 354 */           bool = false; 
/*     */       } 
/*     */     } 
/* 357 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void saveSetting(HttpServletRequest paramHttpServletRequest) {
/* 367 */     String str1 = Util.null2String(paramHttpServletRequest.getParameter("nodeid"));
/* 368 */     String str2 = Util.null2String(paramHttpServletRequest.getParameter("wfid"));
/* 369 */     String str3 = Util.null2String(paramHttpServletRequest.getParameter("formid"));
/* 370 */     String str4 = Util.null2String(paramHttpServletRequest.getParameter("isbill"));
/* 371 */     String str5 = Util.null2String(paramHttpServletRequest.getParameter("language"));
/* 372 */     String str6 = Util.null2String(paramHttpServletRequest.getParameter("detailIndex"));
/* 373 */     RecordSet recordSet = new RecordSet();
/*     */ 
/*     */     
/*     */     try {
/* 377 */       JSONArray jSONArray = getDetailField(str2, str1, str3, str4, str5, str6);
/* 378 */       String str = "";
/* 379 */       for (byte b = 0; b < jSONArray.length(); b++) {
/* 380 */         JSONObject jSONObject = (JSONObject)jSONArray.get(b);
/* 381 */         String str7 = jSONObject.getString("fieldid");
/*     */         
/* 383 */         if (!"-1".equals(str7))
/*     */         {
/*     */           
/* 386 */           str = str + "," + str7;
/*     */         }
/*     */       } 
/* 389 */       if (!"".equals(str)) {
/* 390 */         str = str.substring(1);
/* 391 */         recordSet.execute("update workflow_nodeform set isorder = null,ordertype = null,orderindex = null where nodeid = " + str1 + " and fieldid in (" + str + ")");
/*     */       } 
/* 393 */       recordSet.execute("update workflow_nodeformgroup set isorder = null,ordertype = null,orderindex = null where nodeid = " + str1 + " and groupid = " + (Util.getIntValue(str6) - 1));
/*     */ 
/*     */       
/* 396 */       Map map = paramHttpServletRequest.getParameterMap();
/* 397 */       for (String str7 : map.keySet()) {
/* 398 */         String str8 = str7;
/* 399 */         if (str8.indexOf("ordertype_") >= 0) {
/* 400 */           String str9 = str8.split("\\_")[1];
/* 401 */           boolean bool = true;
/* 402 */           String str10 = Util.null2String(paramHttpServletRequest.getParameter("ordertype_" + str9));
/* 403 */           String str11 = Util.null2String(paramHttpServletRequest.getParameter("orderindex_" + str9));
/* 404 */           String str12 = "";
/*     */           
/* 406 */           if ("-1".equals(str9)) {
/* 407 */             str12 = "update workflow_nodeformgroup set isorder = " + bool + ",ordertype = " + str10 + ",orderindex = " + str11 + " where nodeid = " + str1 + " and groupid = " + (Util.getIntValue(str6) - 1);
/*     */           } else {
/* 409 */             str12 = "update workflow_nodeform set isorder = " + bool + ",ordertype = " + str10 + ",orderindex = " + str11 + " where nodeid = " + str1 + " and fieldid = " + str9;
/*     */           } 
/*     */           
/* 412 */           recordSet.execute(str12);
/*     */         } 
/*     */       } 
/* 415 */     } catch (Exception exception) {}
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/exceldesign/DetailOrderManager.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */