/*     */ package weaver.workflow.workflow;
/*     */ 
/*     */ import com.engine.odoc.cmd.rightMenu.util.TransferUtil;
/*     */ import java.io.BufferedReader;
/*     */ import java.io.Writer;
/*     */ import oracle.sql.CLOB;
/*     */ import weaver.conn.ConnStatement;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class WFNodePortalMainManager
/*     */   extends BaseBean
/*     */ {
/*     */   private RecordSet statement;
/*     */   private int nodeid;
/*     */   private int id;
/*     */   private int wfid;
/*     */   private int Destnodeid;
/*     */   private int linkorder;
/*     */   private String isreject;
/*     */   private String condition;
/*     */   private String conditioncn;
/*     */   private float passtime;
/*     */   private String linkname;
/*     */   private int nodepassHour;
/*     */   private int nodepassMinute;
/*     */   private String dateField;
/*     */   private String timeField;
/*     */   private String isBulidCode;
/*     */   private String isMustPass;
/*     */   private String tipsinfo;
/*     */   private int directionfrom;
/*     */   private int directionto;
/*  44 */   private int startDirection = -1;
/*  45 */   private int endDirection = -1;
/*  46 */   private String drawStyle = "";
/*     */ 
/*     */ 
/*     */   
/*     */   public void resetParameter() {
/*  51 */     this.Destnodeid = 0;
/*  52 */     this.wfid = 0;
/*  53 */     this.id = 0;
/*  54 */     this.nodeid = 0;
/*  55 */     this.isreject = "";
/*  56 */     this.condition = "";
/*  57 */     this.conditioncn = "";
/*  58 */     this.passtime = -1.0F;
/*  59 */     this.linkname = "";
/*  60 */     this.nodepassHour = 0;
/*  61 */     this.nodepassMinute = 0;
/*  62 */     this.isBulidCode = "0";
/*  63 */     this.isMustPass = "0";
/*  64 */     this.tipsinfo = "";
/*  65 */     this.directionfrom = -1;
/*  66 */     this.directionto = -1;
/*  67 */     this.dateField = "";
/*  68 */     this.timeField = "";
/*  69 */     this.startDirection = -1;
/*  70 */     this.endDirection = -1;
/*  71 */     this.drawStyle = "";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getNodepassHour() throws Exception {
/*  78 */     return this.statement.getInt("nodepasshour");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setNodepassHour(int paramInt) {
/*  85 */     this.nodepassHour = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getNodepassMinute() throws Exception {
/*  92 */     return this.statement.getInt("nodepassminute");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setNodepassMinute(int paramInt) {
/*  99 */     this.nodepassMinute = paramInt;
/*     */   }
/*     */ 
/*     */   
/*     */   public String getDateField() {
/* 104 */     return Util.null2String(this.statement.getString("dateField"));
/*     */   }
/*     */   public void setDateField(String paramString) {
/* 107 */     this.dateField = paramString;
/*     */   }
/*     */   
/*     */   public String getTimeField() {
/* 111 */     return Util.null2String(this.statement.getString("timeField"));
/*     */   }
/*     */   public void setTimeField(String paramString) {
/* 114 */     this.timeField = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setDestnodeid(int paramInt) {
/* 121 */     this.Destnodeid = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setNodeid(int paramInt) {
/* 128 */     this.nodeid = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setWfid(int paramInt) {
/* 135 */     this.wfid = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getNodeid() throws Exception {
/* 142 */     return this.statement.getInt("nodeid");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setId(int paramInt) {
/* 149 */     this.id = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getId() throws Exception {
/* 156 */     return this.statement.getInt("id");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setLinkorder(int paramInt) {
/* 163 */     this.linkorder = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getLinkorder() throws Exception {
/* 170 */     return this.statement.getInt("linkorder");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setIsreject(String paramString) {
/* 177 */     this.isreject = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setIsBulidCode(String paramString) {
/* 184 */     this.isBulidCode = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setCondition(String paramString) {
/* 191 */     this.condition = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setConditioncn(String paramString) {
/* 198 */     this.conditioncn = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setPasstime(float paramFloat) {
/* 205 */     this.passtime = paramFloat;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setLinkname(String paramString) {
/* 212 */     this.linkname = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getIsreject() throws Exception {
/* 219 */     return Util.null2String(this.statement.getString("isreject"));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getIsBulidCode() throws Exception {
/* 226 */     return Util.null2String(this.statement.getString("isBulidCode"));
/*     */   }
/*     */ 
/*     */   
/*     */   public void setStartDirection(int paramInt) {
/* 231 */     this.startDirection = paramInt;
/*     */   }
/*     */   
/*     */   public void setEndDirection(int paramInt) {
/* 235 */     this.endDirection = paramInt;
/*     */   }
/*     */   
/*     */   public String getDrawStyle() {
/* 239 */     return this.drawStyle;
/*     */   }
/*     */   
/*     */   public void setDrawStyle(String paramString) {
/* 243 */     this.drawStyle = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCondition() throws Exception {
/* 252 */     int i = getId();
/* 253 */     String str1 = "";
/* 254 */     String str2 = "";
/* 255 */     if (this.statement.getDBType().equals("mysql")) {
/* 256 */       str2 = "select `condition` from workflow_nodelink where id=" + i;
/*     */     } else {
/* 258 */       str2 = "select condition from workflow_nodelink where id=" + i;
/*     */     } 
/* 260 */     ConnStatement connStatement = new ConnStatement();
/*     */     try {
/* 262 */       connStatement.setStatementSql(str2, false);
/* 263 */       connStatement.executeQuery();
/* 264 */       if (connStatement.next()) {
/* 265 */         if (connStatement.getDBType().equals("oracle") && Util.null2String(connStatement.getOrgindbtype()).equals("oracle")) {
/*     */           
/* 267 */           CLOB cLOB = connStatement.getClob("condition");
/* 268 */           if (cLOB == null) return str1; 
/* 269 */           String str = "";
/* 270 */           StringBuffer stringBuffer = new StringBuffer("");
/* 271 */           BufferedReader bufferedReader = new BufferedReader(cLOB.getCharacterStream());
/* 272 */           for (; (str = bufferedReader.readLine()) != null; stringBuffer = stringBuffer.append(str));
/* 273 */           bufferedReader.close();
/* 274 */           str1 = stringBuffer.toString();
/*     */         } else {
/* 276 */           str1 = connStatement.getString("condition");
/*     */         } 
/*     */       }
/*     */     } finally {
/* 280 */       if (connStatement != null)
/* 281 */         connStatement.close(); 
/*     */     } 
/* 283 */     return str1.trim();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getNewrule() throws Exception {
/* 292 */     int i = getId();
/* 293 */     String str1 = "";
/* 294 */     String str2 = "SELECT newrule FROM workflow_nodelink WHERE id=" + i;
/* 295 */     ConnStatement connStatement = new ConnStatement();
/*     */     try {
/* 297 */       connStatement.setStatementSql(str2, false);
/* 298 */       connStatement.executeQuery();
/* 299 */       if (connStatement.next())
/*     */       {
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */         
/* 313 */         str1 = connStatement.getString("newrule");
/*     */       }
/*     */     } finally {
/*     */       
/* 317 */       if (connStatement != null)
/* 318 */         connStatement.close(); 
/*     */     } 
/* 320 */     return str1.trim();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getConditioncn() throws Exception {
/* 329 */     int i = getId();
/* 330 */     String str1 = "";
/* 331 */     String str2 = "select conditioncn from workflow_nodelink where id=" + i;
/* 332 */     ConnStatement connStatement = new ConnStatement();
/*     */     try {
/* 334 */       connStatement.setStatementSql(str2, false);
/* 335 */       connStatement.executeQuery();
/* 336 */       if (connStatement.next()) {
/* 337 */         if (connStatement.getDBType().equals("oracle") && Util.null2String(connStatement.getOrgindbtype()).equals("oracle")) {
/*     */           
/* 339 */           CLOB cLOB = connStatement.getClob("conditioncn");
/* 340 */           if (cLOB == null) return str1; 
/* 341 */           String str = "";
/* 342 */           StringBuffer stringBuffer = new StringBuffer("");
/* 343 */           BufferedReader bufferedReader = new BufferedReader(cLOB.getCharacterStream());
/* 344 */           for (; (str = bufferedReader.readLine()) != null; stringBuffer = stringBuffer.append(str));
/* 345 */           bufferedReader.close();
/* 346 */           str1 = stringBuffer.toString();
/*     */         } else {
/* 348 */           str1 = connStatement.getString("conditioncn");
/*     */         } 
/*     */       }
/*     */     } finally {
/* 352 */       if (connStatement != null)
/* 353 */         connStatement.close(); 
/*     */     } 
/* 355 */     return str1.trim();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public float getPasstime() throws Exception {
/* 362 */     return this.statement.getFloat("nodepasstime");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getLinkname() throws Exception {
/* 369 */     return Util.null2String(this.statement.getString("linkname"));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getDestnodeid() throws Exception {
/* 376 */     return this.statement.getInt("destnodeid");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setIsMustPass(String paramString) {
/* 383 */     this.isMustPass = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getIsMustPass() throws Exception {
/* 390 */     return Util.null2String(this.statement.getString("ismustpass"));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getTipsinfo() {
/* 399 */     return Util.null2String(this.statement.getString("tipsinfo"));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setTipsinfo(String paramString) {
/* 408 */     this.tipsinfo = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getDirectionfrom() {
/* 417 */     return this.statement.getInt("directionfrom");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setDirectionfrom(int paramInt) {
/* 426 */     this.directionfrom = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getDirectionto() {
/* 435 */     return this.statement.getInt("directionto");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setDirectionto(int paramInt) {
/* 444 */     this.directionto = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void selectWfNodePortal() throws Exception {
/* 458 */     this.statement = new RecordSet();
/* 459 */     String str = "";
/* 460 */     if (this.statement.getDBType().equals("mysql")) {
/* 461 */       str = "select id,nodeid,isreject,`condition`,conditioncn,linkname,destnodeid,nodepasstime,nodepasshour,nodepassminute,isBulidCode,ismustpass,tipsinfo,directionfrom,directionto,dateField,timeField,linkorder from workflow_nodelink where wfrequestid is null and not EXISTS(select 1 from workflow_nodebase b where workflow_nodelink.nodeid=b.id and b.IsFreeNode='1') and not EXISTS(select 1 from workflow_nodebase b where workflow_nodelink.destnodeid=b.id and b.IsFreeNode='1') and workflowid=" + this.wfid + " order by linkorder,nodeid,id";
/*     */     } else {
/* 463 */       str = "select id,nodeid,isreject,condition,conditioncn,linkname,destnodeid,nodepasstime,nodepasshour,nodepassminute,isBulidCode,ismustpass,tipsinfo,directionfrom,directionto,dateField,timeField,linkorder from workflow_nodelink where wfrequestid is null and not EXISTS(select 1 from workflow_nodebase b where workflow_nodelink.nodeid=b.id and b.IsFreeNode='1') and not EXISTS(select 1 from workflow_nodebase b where workflow_nodelink.destnodeid=b.id and b.IsFreeNode='1') and workflowid=" + this.wfid + " order by linkorder,nodeid,id";
/*     */     } 
/*     */     try {
/* 466 */       this.statement.executeSql(str);
/*     */     
/*     */     }
/* 469 */     catch (Exception exception) {
/* 470 */       writeLog(exception);
/* 471 */       throw exception;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean next() throws Exception {
/* 482 */     return this.statement.next();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void closeStatement() {}
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void updateWfNodePortal() throws Exception {
/* 499 */     String str = "update workflow_nodelink set workflowid=?,nodeid=?,isreject=?,linkname=?,destnodeid=?,nodepasstime=?,isBulidCode=?,ismustpass=?,tipsinfo=?,linkorder=? where id=?";
/* 500 */     ConnStatement connStatement = new ConnStatement();
/*     */ 
/*     */     
/* 503 */     boolean bool = (connStatement.getDBType().equals("oracle") && !Util.null2String(connStatement.getOrgindbtype()).equals("dm") && !Util.null2String(connStatement.getOrgindbtype()).equals("st") && !"jc".equalsIgnoreCase(connStatement.getOrgindbtype())) ? true : false;
/* 504 */     if (bool)
/* 505 */       str = "update workflow_nodelink set workflowid=?,nodeid=?,isreject=?,linkname=?,destnodeid=?,nodepasstime=?,isBulidCode=?,ismustpass=?,tipsinfo=?,linkorder=? where id=?"; 
/*     */     try {
/* 507 */       connStatement.setStatementSql(str);
/* 508 */       connStatement.setInt(1, this.wfid);
/* 509 */       connStatement.setInt(2, this.nodeid);
/* 510 */       connStatement.setString(3, this.isreject);
/* 511 */       connStatement.setString(4, this.linkname);
/* 512 */       connStatement.setInt(5, this.Destnodeid);
/* 513 */       connStatement.setFloat(6, this.passtime);
/*     */       
/* 515 */       connStatement.setString(7, this.isBulidCode);
/* 516 */       connStatement.setString(8, this.isMustPass);
/* 517 */       connStatement.setString(9, this.tipsinfo);
/*     */       
/* 519 */       if (!bool) {
/* 520 */         connStatement.setInt(10, this.linkorder);
/* 521 */         connStatement.setInt(11, this.id);
/* 522 */         connStatement.executeUpdate();
/*     */       
/*     */       }
/*     */       else {
/*     */ 
/*     */         
/* 528 */         connStatement.setInt(10, this.linkorder);
/* 529 */         connStatement.setInt(11, this.id);
/* 530 */         connStatement.executeUpdate();
/* 531 */         String str1 = "";
/* 532 */         if (connStatement.getDBType().equals("mysql")) {
/* 533 */           str1 = "select `condition`,conditioncn from workflow_nodelink where id = " + this.id;
/*     */         } else {
/* 535 */           str1 = "select condition,conditioncn from workflow_nodelink where id = " + this.id;
/*     */         } 
/* 537 */         connStatement.setStatementSql(str1, false);
/* 538 */         connStatement.executeQuery();
/* 539 */         connStatement.next();
/* 540 */         CLOB cLOB1 = connStatement.getClob(1);
/* 541 */         CLOB cLOB2 = connStatement.getClob(2);
/*     */         
/* 543 */         if (cLOB1 != null) {
/* 544 */           char[] arrayOfChar = this.condition.toCharArray();
/* 545 */           Writer writer = cLOB1.getCharacterOutputStream();
/* 546 */           writer.write(arrayOfChar);
/* 547 */           writer.flush();
/* 548 */           writer.close();
/*     */         } 
/* 550 */         if (cLOB2 != null) {
/* 551 */           char[] arrayOfChar = this.conditioncn.toCharArray();
/* 552 */           Writer writer = cLOB2.getCharacterOutputStream();
/* 553 */           writer.write(arrayOfChar);
/* 554 */           writer.flush();
/* 555 */           writer.close();
/*     */         } 
/*     */       } 
/* 558 */     } catch (Exception exception) {
/* 559 */       writeLog(exception);
/* 560 */       throw exception;
/*     */     } finally {
/*     */       try {
/* 563 */         connStatement.close();
/* 564 */       } catch (Exception exception) {}
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void saveWfNodePortal() throws Exception {
/* 578 */     ConnStatement connStatement = new ConnStatement();
/* 579 */     String str = "";
/* 580 */     if (connStatement.getDBType().equals("mysql")) {
/* 581 */       str = "insert into workflow_nodelink(workflowid,nodeid,isreject,linkname,destnodeid,nodepasstime,isBulidCode,ismustpass,tipsinfo,directionfrom,directionto,`condition`,conditioncn,linkorder,startDirection,endDirection,drawstyle) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
/*     */     } else {
/* 583 */       str = "insert into workflow_nodelink(workflowid,nodeid,isreject,linkname,destnodeid,nodepasstime,isBulidCode,ismustpass,tipsinfo,directionfrom,directionto,condition,conditioncn,linkorder,startDirection,endDirection,drawstyle) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
/*     */     } 
/* 585 */     boolean bool = connStatement.getDBType().equals("oracle");
/*     */     try {
/* 587 */       connStatement.setStatementSql(str);
/* 588 */       connStatement.setInt(1, this.wfid);
/* 589 */       connStatement.setInt(2, this.nodeid);
/* 590 */       connStatement.setString(3, this.isreject);
/* 591 */       connStatement.setString(4, this.linkname);
/* 592 */       connStatement.setInt(5, this.Destnodeid);
/* 593 */       connStatement.setFloat(6, this.passtime);
/* 594 */       connStatement.setString(7, this.isBulidCode);
/* 595 */       connStatement.setString(8, this.isMustPass);
/* 596 */       connStatement.setString(9, this.tipsinfo);
/* 597 */       connStatement.setInt(10, this.directionfrom);
/* 598 */       connStatement.setInt(11, this.directionto);
/* 599 */       connStatement.setString(12, this.condition);
/* 600 */       connStatement.setString(13, this.conditioncn);
/* 601 */       connStatement.setInt(14, this.linkorder);
/* 602 */       connStatement.setInt(15, this.startDirection);
/* 603 */       connStatement.setInt(16, this.endDirection);
/* 604 */       connStatement.setString(17, "".equals(this.drawStyle) ? null : this.drawStyle);
/* 605 */       int i = connStatement.executeUpdate();
/* 606 */       if (i > 0) {
/* 607 */         TransferUtil.initOdocNodelinkExtByLinkSave(this.wfid, this.nodeid, this.Destnodeid, this.linkname);
/*     */       }
/*     */     }
/* 610 */     catch (Exception exception) {
/* 611 */       writeLog(exception);
/* 612 */       throw exception;
/*     */     } finally {
/*     */       
/* 615 */       try { connStatement.close(); } catch (Exception exception) {}
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void deleteWfNodePortal() throws Exception {
/* 624 */     String str1 = "delete from workflow_nodelink where id=?";
/* 625 */     String str2 = " delete from workflow_addinoperate where objid=? and isnode = ?";
/*     */     
/* 627 */     ConnStatement connStatement = new ConnStatement();
/*     */     
/*     */     try {
/* 630 */       connStatement.setStatementSql(str2);
/* 631 */       connStatement.setInt(1, this.id);
/* 632 */       connStatement.setInt(2, 0);
/* 633 */       connStatement.executeUpdate();
/*     */       
/* 635 */       connStatement.setStatementSql(str1);
/* 636 */       connStatement.setInt(1, this.id);
/* 637 */       connStatement.executeUpdate();
/*     */ 
/*     */       
/* 640 */       RecordSet recordSet = new RecordSet();
/* 641 */       recordSet.executeUpdate("delete from rule_maplist where rulesrc = 1 and linkid = ? ", new Object[] { Integer.valueOf(this.id) });
/*     */     }
/* 643 */     catch (Exception exception) {
/* 644 */       writeLog(exception);
/* 645 */       throw exception;
/*     */     } finally {
/*     */       
/* 648 */       try { connStatement.close(); } catch (Exception exception) {}
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/workflow/WFNodePortalMainManager.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */