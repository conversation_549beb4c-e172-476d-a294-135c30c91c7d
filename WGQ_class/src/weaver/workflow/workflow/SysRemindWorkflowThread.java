/*    */ package weaver.workflow.workflow;
/*    */ 
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.User;
/*    */ import weaver.system.SysRemindWorkflow;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ class SysRemindWorkflowThread
/*    */   implements Runnable
/*    */ {
/*    */   private String title;
/*    */   private User user;
/*    */   private String subwfremindperson;
/*    */   private String emailcontent;
/*    */   private String relRequestId;
/*    */   
/*    */   public SysRemindWorkflowThread(String paramString1, User paramUser, String paramString2, String paramString3, String paramString4) {
/* 21 */     this.title = paramString1;
/* 22 */     this.user = paramUser;
/* 23 */     this.subwfremindperson = paramString2;
/* 24 */     this.emailcontent = paramString3;
/* 25 */     this.relRequestId = paramString4;
/*    */   }
/*    */   private void createRemind() throws Exception {
/* 28 */     SysRemindWorkflow sysRemindWorkflow = new SysRemindWorkflow();
/* 29 */     sysRemindWorkflow.make(this.title, 0, 0, 0, 0, this.user.getUID(), this.subwfremindperson, this.emailcontent, Util.getIntValue(this.relRequestId));
/*    */   }
/*    */   
/*    */   public void run() {
/*    */     try {
/* 34 */       createRemind();
/* 35 */     } catch (Exception exception) {
/* 36 */       exception.printStackTrace();
/*    */     } 
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/workflow/SysRemindWorkflowThread.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */