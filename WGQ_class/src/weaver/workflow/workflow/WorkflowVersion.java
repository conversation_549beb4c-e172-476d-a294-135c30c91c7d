/*      */ package weaver.workflow.workflow;
/*      */ 
/*      */ import com.engine.cpt.util.CptWorkflowSetUitl;
/*      */ import com.engine.kq.wfset.attendance.manager.HrmAttFlowVersionManager;
/*      */ import java.util.ArrayList;
/*      */ import java.util.HashMap;
/*      */ import java.util.HashSet;
/*      */ import java.util.Iterator;
/*      */ import java.util.List;
/*      */ import java.util.Map;
/*      */ import weaver.conn.RecordSet;
/*      */ import weaver.conn.RecordSetTrans;
/*      */ import weaver.fna.general.FnaCommon;
/*      */ import weaver.general.BaseBean;
/*      */ import weaver.general.Util;
/*      */ import weaver.hrm.User;
/*      */ import weaver.hrm.attendance.manager.HrmAttFlowVersionManager;
/*      */ import weaver.hrm.moduledetach.ManageDetachComInfo;
/*      */ import weaver.odoc.workflow.workflow.utils.FormSignatureConfigUtil;
/*      */ import weaver.systeminfo.SysMaintenanceLog;
/*      */ import weaver.systeminfo.SystemEnv;
/*      */ import weaver.workflow.request.WFUrgerManager;
/*      */ import weaver.workflow.request.todo.OfsSendInfoComInfo;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ public class WorkflowVersion
/*      */   extends BaseBean
/*      */ {
/*      */   private String workflowId;
/*      */   public static final String WORKFLOW_STATUS_VERSION_ACTIVE = "1";
/*      */   public static final String WORKFLOW_STATUS_TEST = "2";
/*      */   public static final String WORKFLOW_STATUS_VERSION_INACTIVE = "3";
/*      */   
/*      */   public WorkflowVersion() {}
/*      */   
/*      */   public WorkflowVersion(String paramString) {
/*   77 */     this.workflowId = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public List<Map<String, String>> getAllVersionList() {
/*   86 */     return getAllVersionList(this.workflowId);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static List<Map<String, String>> getAllVersionList(String paramString) {
/*   95 */     ArrayList<Map<String, String>> arrayList = new ArrayList();
/*      */     
/*   97 */     if ("".equals(paramString) || "0".equals(paramString)) {
/*   98 */       return arrayList;
/*      */     }
/*      */     
/*  101 */     String str1 = "0";
/*  102 */     String str2 = "0";
/*  103 */     RecordSet recordSet = new RecordSet();
/*      */     
/*  105 */     String str3 = "select isvalid, activeVersionID FROM workflow_base where id=" + paramString;
/*      */     
/*  107 */     recordSet.executeSql(str3);
/*  108 */     if (recordSet.next()) {
/*  109 */       str1 = Util.null2String(recordSet.getString("isvalid"));
/*  110 */       str2 = Util.null2String(recordSet.getString("activeVersionID"));
/*      */     } 
/*      */     
/*  113 */     String str4 = recordSet.getDBType();
/*  114 */     String str5 = "";
/*  115 */     if ("oracle".equals(str4)) {
/*  116 */       str5 = "nvl(version,1)";
/*  117 */     } else if (recordSet.getDBType().equals("mysql")) {
/*  118 */       str5 = "ifnull(version,1)";
/*      */     } else {
/*  120 */       str5 = "isnull(version,1)";
/*      */     } 
/*  122 */     if (!"3".equals(str1)) {
/*  123 */       str3 = "SELECT distinct id, version, versionDescription,formid,isbill, VersionCreater," + str5 + " FROM workflow_base WHERE activeVersionID=" + paramString + " OR id=" + paramString + " ORDER BY " + str5;
/*      */     }
/*  125 */     else if ("3".equals(str1)) {
/*  126 */       str3 = "SELECT distinct id, version, versionDescription,formid,isbill, VersionCreater," + str5 + " FROM workflow_base WHERE activeVersionID=" + str2 + " OR id=" + str2 + " ORDER BY " + str5;
/*      */     } 
/*      */ 
/*      */     
/*  130 */     recordSet.executeSql(str3);
/*  131 */     while (recordSet.next()) {
/*  132 */       String str6 = Util.null2String(recordSet.getString("id"));
/*  133 */       String str7 = Util.getIntValue(recordSet.getString("version"), 1) + "";
/*  134 */       String str8 = Util.null2String(recordSet.getString("versionDescription"));
/*  135 */       String str9 = Util.null2String(recordSet.getString("VersionCreater"));
/*  136 */       String str10 = Util.null2String(recordSet.getString("formid"));
/*  137 */       String str11 = Util.null2String(recordSet.getString("isbill"));
/*      */       
/*  139 */       HashMap<Object, Object> hashMap = new HashMap<>();
/*  140 */       hashMap.put("id", str6);
/*  141 */       hashMap.put("version", str7);
/*  142 */       hashMap.put("desc", str8);
/*  143 */       hashMap.put("creater", str9);
/*  144 */       hashMap.put("formid", str10);
/*  145 */       hashMap.put("isbill", str11);
/*  146 */       arrayList.add(hashMap);
/*      */     } 
/*      */     
/*  149 */     return arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getAllVersionString(String paramString) {
/*  159 */     String str = "";
/*      */     
/*  161 */     if (paramString == null || "".equals(paramString)) {
/*  162 */       paramString = ",";
/*      */     }
/*      */     
/*  165 */     return getVersionStringByWfid();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getAllVersionString() {
/*  189 */     return getAllVersionString(",");
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static String getAllVersionStringByWFIDs(String paramString) {
/*  199 */     StringBuffer stringBuffer = new StringBuffer();
/*      */     
/*  201 */     if (paramString == null || "".equals(paramString.trim())) {
/*  202 */       return "";
/*      */     }
/*      */     
/*  205 */     String[] arrayOfString = paramString.split(",");
/*  206 */     for (byte b = 0; b < arrayOfString.length; b++) {
/*  207 */       String str1 = arrayOfString[b].trim();
/*      */ 
/*      */ 
/*      */       
/*  211 */       String str2 = "";
/*  212 */       if (str1.indexOf("-") == -1) {
/*  213 */         str2 = getVersionStringByWfid(str1);
/*      */       } else {
/*  215 */         str2 = str1;
/*      */       } 
/*  217 */       stringBuffer.append(str2).append(",");
/*      */     } 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  223 */     if (stringBuffer.length() > 1) {
/*  224 */       stringBuffer.delete(stringBuffer.length() - 1, stringBuffer.length());
/*      */     }
/*      */     
/*  227 */     HashSet hashSet = new HashSet(Util.splitString2List(stringBuffer.toString(), ","));
/*  228 */     stringBuffer.setLength(0);
/*  229 */     for (String str : hashSet) {
/*  230 */       stringBuffer.append(str).append(",");
/*      */     }
/*  232 */     if (stringBuffer.length() > 1) {
/*  233 */       stringBuffer.delete(stringBuffer.length() - 1, stringBuffer.length());
/*      */     }
/*      */     
/*  236 */     return stringBuffer.toString();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static String getAllRelationNodeStringByNodeIDs(String paramString) {
/*  247 */     StringBuffer stringBuffer = new StringBuffer();
/*      */     
/*  249 */     if (paramString == null || "".equals(paramString.trim())) {
/*  250 */       return "";
/*      */     }
/*      */     
/*  253 */     String[] arrayOfString = paramString.split(",");
/*  254 */     for (byte b = 0; b < arrayOfString.length; b++) {
/*  255 */       String str = arrayOfString[b].trim();
/*      */       
/*  257 */       List<String> list = getRelationNodeListByNodeID(str);
/*  258 */       Iterator<String> iterator = list.iterator();
/*  259 */       while (iterator.hasNext()) {
/*  260 */         stringBuffer.append(iterator.next()).append(",");
/*      */       }
/*      */     } 
/*  263 */     if (stringBuffer.length() > 1) {
/*  264 */       stringBuffer.delete(stringBuffer.length() - 1, stringBuffer.length());
/*      */     }
/*      */     
/*  267 */     HashSet hashSet = new HashSet(Util.splitString2List(stringBuffer.toString(), ","));
/*  268 */     stringBuffer.setLength(0);
/*  269 */     for (String str : hashSet) {
/*  270 */       stringBuffer.append(str).append(",");
/*      */     }
/*  272 */     if (stringBuffer.length() > 1) {
/*  273 */       stringBuffer.delete(stringBuffer.length() - 1, stringBuffer.length());
/*      */     }
/*      */     
/*  276 */     return stringBuffer.toString();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static Map<String, String> getActiveWFNodeInfo(String paramString1, String paramString2, String paramString3) {
/*  287 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*      */     
/*  289 */     String str1 = getActiveVersionWFID(paramString1);
/*  290 */     if (paramString1.equals(str1)) {
/*  291 */       hashMap.put("id", paramString2);
/*  292 */       hashMap.put("name", paramString3);
/*  293 */       return (Map)hashMap;
/*      */     } 
/*      */     
/*  296 */     List<String> list = getRelationNodeListByNodeID(paramString2);
/*      */     
/*  298 */     RecordSet recordSet = new RecordSet();
/*  299 */     String str2 = "SELECT id, nodename from workflow_nodebase where id IN ( select nodeid from workflow_flownode where workflowid=" + str1 + ")";
/*  300 */     recordSet.executeSql(str2);
/*  301 */     while (recordSet.next()) {
/*  302 */       String str3 = recordSet.getString("id");
/*  303 */       String str4 = recordSet.getString("nodename");
/*      */       
/*  305 */       if (list.indexOf(str3) != -1) {
/*  306 */         hashMap.put("id", str3);
/*  307 */         hashMap.put("name", str4);
/*      */         
/*      */         break;
/*      */       } 
/*      */     } 
/*  312 */     return (Map)hashMap;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static Map<String, String> getActiveWFNodeInfo(String paramString1, String paramString2) {
/*  323 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*      */     
/*  325 */     String str1 = getActiveVersionWFID(paramString1);
/*      */     
/*  327 */     List<String> list = getRelationNodeListByNodeID(paramString2);
/*      */     
/*  329 */     RecordSet recordSet = new RecordSet();
/*  330 */     String str2 = "SELECT id, nodename from workflow_nodebase where id IN ( select nodeid from workflow_flownode where workflowid=" + str1 + ")";
/*  331 */     recordSet.executeSql(str2);
/*  332 */     while (recordSet.next()) {
/*  333 */       String str3 = recordSet.getString("id");
/*  334 */       String str4 = recordSet.getString("nodename");
/*      */       
/*  336 */       if (list.indexOf(str3) != -1) {
/*  337 */         hashMap.put("id", str3);
/*  338 */         hashMap.put("name", str4);
/*      */         
/*      */         break;
/*      */       } 
/*      */     } 
/*  343 */     return (Map)hashMap;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static List<String> getRelationNodeListByNodeID(String paramString) {
/*  353 */     ArrayList<String> arrayList = new ArrayList();
/*  354 */     if (paramString == null || "".equals(paramString.trim())) {
/*  355 */       return arrayList;
/*      */     }
/*      */     
/*  358 */     HashSet<String> hashSet = new HashSet();
/*      */     
/*  360 */     List<String> list1 = getParentNodeListByNodeID(paramString);
/*  361 */     List<String> list2 = getChildrenNodeListByNodeID(paramString);
/*  362 */     hashSet.add(paramString);
/*  363 */     hashSet.addAll(list1);
/*  364 */     hashSet.addAll(list2);
/*  365 */     arrayList.addAll(hashSet);
/*  366 */     return arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static List<String> getParentNodeListByNodeID(String paramString) {
/*  377 */     ArrayList<String> arrayList = new ArrayList();
/*      */     
/*  379 */     if (paramString == null || "".equals(paramString.trim())) {
/*  380 */       return arrayList;
/*      */     }
/*      */     
/*  383 */     RecordSet recordSet = new RecordSet();
/*  384 */     String str = "select nodeid, parentnodeid from workflow_versionNodeRelation where nodeid=" + paramString;
/*  385 */     recordSet.execute(str);
/*  386 */     if (recordSet.next()) {
/*  387 */       String str1 = recordSet.getString("parentnodeid");
/*      */       
/*  389 */       if (arrayList.indexOf(str1) == -1) {
/*  390 */         arrayList.addAll(getChildrenNodeListByNodeID(str1));
/*      */       }
/*  392 */       arrayList.add(str1);
/*  393 */       arrayList.addAll(getParentNodeListByNodeID(str1));
/*      */     } 
/*  395 */     return arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static List<String> getChildrenNodeListByNodeID(String paramString) {
/*  406 */     ArrayList<String> arrayList = new ArrayList();
/*      */     
/*  408 */     if (paramString == null || "".equals(paramString.trim())) {
/*  409 */       return arrayList;
/*      */     }
/*      */     
/*  412 */     RecordSet recordSet = new RecordSet();
/*  413 */     String str = "select nodeid, parentnodeid from workflow_versionNodeRelation where parentnodeid=" + paramString;
/*  414 */     recordSet.execute(str);
/*  415 */     while (recordSet.next()) {
/*  416 */       String str1 = recordSet.getString("nodeid");
/*  417 */       arrayList.add(str1);
/*  418 */       arrayList.addAll(getChildrenNodeListByNodeID(str1));
/*      */     } 
/*  420 */     return arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static String containRelationNodeId(List<String> paramList, String paramString) {
/*  431 */     if (paramList == null || paramList.size() == 0) {
/*  432 */       return paramString;
/*      */     }
/*      */     
/*  435 */     List<String> list = getRelationNodeListByNodeID(paramString);
/*      */     
/*  437 */     Iterator<String> iterator = list.iterator();
/*  438 */     while (iterator.hasNext()) {
/*  439 */       String str = iterator.next();
/*      */       
/*  441 */       int i = paramList.indexOf(str);
/*      */       
/*  443 */       if (i != -1) {
/*  444 */         return str;
/*      */       }
/*      */     } 
/*      */     
/*  448 */     return paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static String getWFNodesByParentNodeIDs(String paramString1, String paramString2) {
/*  459 */     if (paramString1 == null || "".equals(paramString1) || paramString2 == null || "".equals(paramString2)) {
/*  460 */       return null;
/*      */     }
/*      */     
/*  463 */     StringBuffer stringBuffer = new StringBuffer();
/*      */     
/*  465 */     String str = "select a.nodeid from workflow_versionNodeRelation a inner join workflow_flownode b on a.nodeid=b.nodeid where a.parentNodeid in (" + paramString2 + ") and b.workflowid=" + paramString1;
/*  466 */     RecordSet recordSet = new RecordSet();
/*  467 */     recordSet.executeSql(str);
/*  468 */     while (recordSet.next()) {
/*  469 */       String str1 = recordSet.getString("nodeid");
/*  470 */       stringBuffer.append(str1).append(",");
/*      */     } 
/*  472 */     if (stringBuffer.length() > 1) {
/*  473 */       stringBuffer.delete(stringBuffer.length() - 1, stringBuffer.length());
/*      */     }
/*      */     
/*  476 */     return stringBuffer.toString();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean isActive() {
/*  485 */     if (this.workflowId.equals(getActiveVersionWFID())) {
/*  486 */       return true;
/*      */     }
/*  488 */     return false;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getActiveVersionWFID() {
/*  497 */     return getActiveVersionWFID(this.workflowId);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static String getActiveVersionWFID(String paramString) {
/*  506 */     String str1 = paramString;
/*  507 */     String str2 = "0";
/*      */     
/*  509 */     RecordSet recordSet = new RecordSet();
/*      */     
/*  511 */     String str3 = "select isvalid FROM workflow_base where id=" + paramString;
/*      */     
/*  513 */     recordSet.executeSql(str3);
/*  514 */     if (recordSet.next()) {
/*  515 */       str2 = Util.null2String(recordSet.getString("isvalid"));
/*      */     }
/*      */     
/*  518 */     if (!"".equals(paramString.trim()) && 
/*  519 */       "3".equals(str2)) {
/*  520 */       str3 = "select activeVersionID FROM workflow_base where id=" + paramString;
/*      */       
/*  522 */       recordSet.executeSql(str3);
/*  523 */       if (recordSet.next()) {
/*  524 */         str1 = Util.null2String(recordSet.getString("activeVersionID"));
/*      */       }
/*      */     } 
/*      */ 
/*      */     
/*  529 */     return str1;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public int getLastVersionID() {
/*  538 */     int i = 1;
/*      */     
/*  540 */     String str1 = "0";
/*      */     
/*  542 */     RecordSet recordSet = new RecordSet();
/*      */     
/*  544 */     String str2 = "select isvalid FROM workflow_base where id=" + this.workflowId;
/*      */     
/*  546 */     recordSet.executeSql(str2);
/*  547 */     if (recordSet.next()) {
/*  548 */       str1 = Util.null2String(recordSet.getString("isvalid"));
/*      */     }
/*      */     
/*  551 */     if (!"3".equals(str1)) {
/*  552 */       str2 = "SELECT max(version) as version FROM workflow_base WHERE activeVersionID=" + this.workflowId + " OR id=" + this.workflowId + " ORDER BY version";
/*      */ 
/*      */ 
/*      */     
/*      */     }
/*  557 */     else if ("3"
/*  558 */       .equals(str1)) {
/*  559 */       str2 = "SELECT max(version) as version FROM workflow_base WHERE activeVersionID=(select activeVersionID FROM workflow_base where id=" + this.workflowId + ") OR id=(select activeVersionID FROM workflow_base where id=" + this.workflowId + ") ORDER BY version";
/*      */     } 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  565 */     recordSet.executeSql(str2);
/*  566 */     while (recordSet.next()) {
/*  567 */       i = Util.getIntValue(
/*  568 */           Util.null2String(recordSet.getString("version")), 1);
/*      */     }
/*      */     
/*  571 */     if (i < 1) {
/*  572 */       i = 1;
/*      */     }
/*  574 */     return i;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public int getVersionID() {
/*  583 */     return getVersionID(this.workflowId);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static int getVersionID(String paramString) {
/*  592 */     int i = 1;
/*      */     
/*  594 */     RecordSet recordSet = new RecordSet();
/*      */     
/*  596 */     String str = "select version FROM workflow_base where id=" + paramString;
/*  597 */     recordSet.executeSql(str);
/*  598 */     if (recordSet.next()) {
/*  599 */       i = Util.getIntValue(recordSet.getString("version"), 1);
/*      */     }
/*      */     
/*  602 */     return i;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getVersionDesc() {
/*  612 */     String str1 = "";
/*      */     
/*  614 */     RecordSet recordSet = new RecordSet();
/*      */     
/*  616 */     String str2 = "select versionDescription FROM workflow_base where id=" + this.workflowId;
/*      */     
/*  618 */     recordSet.executeSql(str2);
/*  619 */     if (recordSet.next()) {
/*  620 */       str1 = Util.null2String(recordSet.getString("versionDescription"));
/*      */     }
/*      */     
/*  623 */     return str1.trim();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setActiveVersion(String paramString) {
/*  632 */     String str1 = "0";
/*  633 */     RecordSet recordSet = new RecordSet();
/*      */     
/*  635 */     String str2 = "select isvalid FROM workflow_base where id=" + paramString;
/*  636 */     recordSet.executeSql(str2);
/*  637 */     if (recordSet.next()) {
/*  638 */       str1 = Util.null2String(recordSet.getString("isvalid"));
/*      */     }
/*      */ 
/*      */     
/*  642 */     RecordSetTrans recordSetTrans = new RecordSetTrans();
/*  643 */     recordSetTrans.setAutoCommit(false);
/*  644 */     if ("3".equals(str1)) {
/*      */       try {
/*  646 */         String str3 = getActiveVersionWFID(paramString);
/*  647 */         String str4 = "SELECT distinct id FROM workflow_base WHERE activeVersionID=" + str3 + " OR id=" + str3;
/*      */ 
/*      */         
/*  650 */         recordSetTrans.executeSql("update workflow_base set activeVersionID=" + paramString + " ,isvalid=3 where id in (" + str4 + ")");
/*      */         
/*  652 */         str2 = "update workflow_base set isvalid=1 where id=" + paramString;
/*      */ 
/*      */         
/*  655 */         recordSetTrans.executeSql(str2);
/*      */         
/*  657 */         WorkflowComInfo workflowComInfo = new WorkflowComInfo();
/*  658 */         workflowComInfo.removeWorkflowCache();
/*  659 */         recordSetTrans.commit();
/*  660 */       } catch (Exception exception) {
/*  661 */         recordSetTrans.rollback();
/*  662 */         writeLog("设为活动版本处理异常：" + exception);
/*      */       } 
/*      */     }
/*      */ 
/*      */     
/*  667 */     WFUrgerManager wFUrgerManager = new WFUrgerManager();
/*  668 */     recordSet.executeQuery("select wfid from workflow_versioninfo where wfversionid = ?", new Object[] { getActiveVersionWFID(paramString + "") });
/*  669 */     while (recordSet.next()) {
/*  670 */       if (recordSet.getInt("wfid") > 0) {
/*  671 */         wFUrgerManager.insertUrgerByWfid(recordSet.getInt("wfid"));
/*      */       }
/*      */     } 
/*      */     
/*  675 */     (new OfsSendInfoComInfo()).removeCache();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setActiveVersion() {
/*  684 */     setActiveVersion(this.workflowId);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setActiveVersion(String paramString1, String paramString2) {
/*  693 */     String str1 = "0";
/*  694 */     RecordSetTrans recordSetTrans = new RecordSetTrans();
/*  695 */     recordSetTrans.setAutoCommit(false);
/*  696 */     RecordSet recordSet = new RecordSet();
/*      */     
/*  698 */     String str2 = "select isvalid FROM workflow_base where id=" + paramString1;
/*  699 */     recordSet.executeSql(str2);
/*  700 */     if (recordSet.next()) {
/*  701 */       str1 = Util.null2String(recordSet.getString("isvalid"));
/*      */     }
/*      */     
/*  704 */     boolean bool = false;
/*  705 */     if ("3".equals(str1)) {
/*      */       try {
/*  707 */         String str3 = getActiveVersionWFID(paramString1);
/*  708 */         String str4 = "SELECT distinct id FROM (select * from workflow_base) w WHERE w.activeVersionID=" + str3 + " OR w.id=" + str3;
/*      */         
/*  710 */         String str5 = getActiveVersionWFID(paramString1);
/*  711 */         int i = getWorkflowStatus(str5);
/*  712 */         if (i == 3) {
/*  713 */           i = 1;
/*  714 */           writeLog("检测到workflowStatus状态为3，在此强制改为1，当前流程id为：" + paramString1 + "  当前活动版本id：" + str5);
/*      */         } 
/*  716 */         recordSetTrans.executeSql("update workflow_base set activeVersionID=" + paramString1 + " ,isvalid=3 where id in (" + str4 + ")");
/*      */         
/*  718 */         str2 = "update workflow_base set isvalid=" + i + ", versionDescription='" + paramString2 + "' where id=" + paramString1;
/*      */ 
/*      */ 
/*      */         
/*  722 */         recordSetTrans.executeSql(str2);
/*      */ 
/*      */         
/*  725 */         WorkflowComInfo workflowComInfo = new WorkflowComInfo();
/*  726 */         workflowComInfo.removeWorkflowCache();
/*      */         
/*  728 */         TestWorkflowComInfo testWorkflowComInfo = new TestWorkflowComInfo();
/*  729 */         testWorkflowComInfo.removeWorkflowCache();
/*      */ 
/*      */         
/*  732 */         WorkflowAllComInfo workflowAllComInfo = new WorkflowAllComInfo();
/*  733 */         workflowAllComInfo.updateCache(str3);
/*  734 */         workflowAllComInfo.updateCache(paramString1);
/*      */ 
/*      */         
/*  737 */         String str6 = getAllVersionStringByWFIDs(paramString1);
/*  738 */         str2 = "update mode_triggerworkflowset set workflowid=" + paramString1 + " where workflowid in (" + str6 + ")";
/*  739 */         recordSetTrans.executeSql(str2);
/*      */ 
/*      */         
/*  742 */         str2 = "update workflow_monitor_detail set workflowid=" + paramString1 + " where workflowid in (" + str6 + ")";
/*  743 */         recordSetTrans.executeSql(str2);
/*  744 */         recordSetTrans.commit();
/*  745 */         bool = true;
/*  746 */       } catch (Exception exception) {
/*  747 */         recordSetTrans.rollback();
/*  748 */         writeLog(exception);
/*      */       } 
/*      */     } else {
/*  751 */       updateVersionInfo(paramString1, paramString2);
/*      */     } 
/*  753 */     if (bool) {
/*      */       
/*  755 */       WFUrgerManager wFUrgerManager = new WFUrgerManager();
/*  756 */       recordSet.executeQuery("select wfid from workflow_versioninfo where wfversionid = ?", new Object[] { getActiveVersionWFID(paramString1 + "") });
/*  757 */       while (recordSet.next()) {
/*  758 */         if (recordSet.getInt("wfid") > 0) {
/*  759 */           wFUrgerManager.insertUrgerByWfid(recordSet.getInt("wfid"));
/*      */         }
/*      */       } 
/*      */       
/*  763 */       WorkflowVersion workflowVersion = new WorkflowVersion(paramString1);
/*  764 */       String str3 = workflowVersion.getAllVersionString();
/*  765 */       String str4 = " update workflow_agentConditionSet set workflowid = ? where workflowid in (" + str3 + ") ";
/*  766 */       recordSet.executeUpdate(str4, new Object[] { paramString1 });
/*  767 */       recordSet.executeUpdate("update workflow_agent set workflowid = ? where workflowid in (" + str3 + ") ", new Object[] { paramString1 });
/*      */     } 
/*      */     
/*  770 */     (new OfsSendInfoComInfo()).removeCache();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void updateVersionInfo(String paramString1, String paramString2) {
/*  779 */     RecordSet recordSet = new RecordSet();
/*      */     
/*  781 */     String str = "update workflow_base set versionDescription='" + paramString2 + "' where id=" + paramString1;
/*  782 */     recordSet.executeSql(str);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public Map<String, String> getVersionInfoByWfID(String paramString) {
/*  789 */     return null;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public int saveAsVersion(User paramUser, String paramString1, String paramString2) {
/*  802 */     int i = 0;
/*      */     
/*      */     try {
/*  805 */       WFManager wFManager = new WFManager();
/*  806 */       i = wFManager.setWFTemplate(Util.getIntValue(this.workflowId, 0), "3", paramUser.getUID(), paramString2);
/*  807 */     } catch (Exception exception) {
/*  808 */       writeLog("另存失败");
/*  809 */       exception.printStackTrace();
/*  810 */       writeLog("另存失败，", exception);
/*      */     } 
/*      */     
/*  813 */     if (i <= 0) {
/*  814 */       return i;
/*      */     }
/*  816 */     (new OfsSendInfoComInfo()).removeCache();
/*      */     
/*  818 */     saveAsVersionAfter(paramUser, paramString1, paramString2, i);
/*      */     
/*  820 */     return i;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void saveAsVersionAfter(User paramUser, String paramString1, String paramString2, int paramInt) {
/*  832 */     (new HrmAttFlowVersionManager()).copySet(this.workflowId, String.valueOf(paramInt));
/*      */     
/*      */     try {
/*  835 */       FnaCommon.fnaSaveAsVersion(Util.getIntValue(this.workflowId, 0), paramInt, paramString2, paramUser);
/*  836 */     } catch (Exception exception) {
/*  837 */       writeLog("另存失败--Fna:WorkflowVersion--saveAsVersion--fnaSaveAsVersion");
/*  838 */       exception.printStackTrace();
/*      */     } 
/*      */     
/*      */     try {
/*  842 */       (new HrmAttFlowVersionManager()).copySet(this.workflowId, String.valueOf(paramInt));
/*  843 */     } catch (Exception exception) {
/*  844 */       writeLog("另存失败--Hrm:WorkflowVersion--saveAsVersion--HrmAttFlowVersionManager");
/*  845 */       exception.printStackTrace();
/*      */     } 
/*      */     
/*  848 */     RecordSet recordSet = new RecordSet();
/*      */ 
/*      */     
/*  851 */     saveWorkflowVersionInfo(paramInt + "", Util.getIntValue(this.workflowId, 0) + "");
/*      */ 
/*      */     
/*  854 */     String str1 = " select wfversionid from workflow_versioninfo where wfid = ? ";
/*  855 */     recordSet.executeQuery(str1, new Object[] { paramInt + "" });
/*  856 */     ArrayList<String> arrayList1 = new ArrayList();
/*  857 */     ArrayList<String> arrayList2 = new ArrayList();
/*  858 */     while (recordSet.next()) {
/*  859 */       arrayList1.add(recordSet.getString("wfversionid"));
/*      */     }
/*  861 */     String str2 = " insert into workflow_versioninfo(wfid,wfversionid) values(?,?) ";
/*  862 */     for (String str : arrayList1) {
/*  863 */       recordSet.executeQuery(str1, new Object[] { str });
/*  864 */       if (recordSet.getCounts() < arrayList1.size()) {
/*  865 */         arrayList2.clear();
/*  866 */         arrayList2.addAll(arrayList1);
/*  867 */         while (recordSet.next()) {
/*  868 */           arrayList2.remove(recordSet.getString("wfversionid"));
/*      */         }
/*  870 */         for (String str5 : arrayList2) {
/*  871 */           recordSet.executeUpdate(str2, new Object[] { str, str5 });
/*      */         } 
/*      */       } 
/*      */     } 
/*      */ 
/*      */ 
/*      */     
/*  878 */     int i = getLastVersionID() + 1;
/*  879 */     String str3 = getActiveVersionWFID();
/*      */     
/*  881 */     if (str3.equals(this.workflowId)) {
/*  882 */       recordSet.execute("update workflow_base set activeVersionID=" + this.workflowId + " where id=" + this.workflowId);
/*      */     }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  890 */     String str4 = "update workflow_base set isvalid=3, version=" + i + ", activeVersionID=" + str3 + ", versionDescription='" + paramString1 + "', VersionCreater=" + paramUser.getUID() + " where id=" + paramInt;
/*      */ 
/*      */     
/*  893 */     recordSet.executeSql(str4);
/*      */ 
/*      */ 
/*      */     
/*  897 */     WFUrgerManager wFUrgerManager = new WFUrgerManager();
/*  898 */     recordSet.executeQuery("select wfid from workflow_versioninfo where wfversionid = ?", new Object[] { getActiveVersionWFID(this.workflowId + "") });
/*  899 */     while (recordSet.next()) {
/*  900 */       if (recordSet.getInt("wfid") > 0) {
/*  901 */         wFUrgerManager.insertUrgerByWfid(recordSet.getInt("wfid"));
/*      */       }
/*      */     } 
/*  904 */     FormSignatureConfigUtil formSignatureConfigUtil = new FormSignatureConfigUtil();
/*  905 */     formSignatureConfigUtil.saveAsNewVersion(paramUser, Util.getIntValue(this.workflowId, 0), paramInt);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static boolean hasVersion(String paramString) {
/*  915 */     List<Map<String, String>> list = getAllVersionList(paramString);
/*      */     
/*  917 */     if (list.size() > 1) {
/*  918 */       return true;
/*      */     }
/*      */     
/*  921 */     return false;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean hasVersion() {
/*  930 */     return hasVersion(this.workflowId);
/*      */   }
/*      */   
/*      */   public static String getAboutButtonName(User paramUser) {
/*  934 */     return SystemEnv.getHtmlLabelName(386340, paramUser.getLanguage());
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static boolean deleteVersion(String paramString1, int paramInt, String paramString2) throws Exception {
/*  947 */     SysMaintenanceLog sysMaintenanceLog = new SysMaintenanceLog();
/*  948 */     WFManager wFManager = new WFManager();
/*  949 */     WFMainManager wFMainManager = new WFMainManager();
/*  950 */     WorkflowComInfo workflowComInfo = new WorkflowComInfo();
/*      */     
/*  952 */     User user = new User(paramInt);
/*  953 */     wFManager.setWfid(Integer.parseInt(paramString1));
/*  954 */     wFManager.getWfInfo();
/*  955 */     int i = -1;
/*  956 */     int j = 0;
/*  957 */     WfRightManager wfRightManager = new WfRightManager();
/*  958 */     boolean bool = wfRightManager.hasPermission3(Integer.parseInt(paramString1), 0, user, 1);
/*      */     
/*  960 */     boolean bool1 = (new ManageDetachComInfo()).isUseWfManageDetach() ? true : false;
/*  961 */     if (bool1 == true) {
/*  962 */       i = wFManager.getSubCompanyId2();
/*      */     }
/*  964 */     j = UserWFOperateLevel.checkUserWfOperateLevel(bool1, i, user, bool, "WorkflowManage:All");
/*  965 */     WorkflowVersion workflowVersion = new WorkflowVersion(paramString1 + "");
/*  966 */     if ((paramString1 + "").equals(workflowVersion.getActiveVersionWFID()) || 
/*  967 */       !"true".equalsIgnoreCase(WFMainManager.getCheckBox4InActiveVersion(paramString1 + "+0")) || j <= 1) {
/*  968 */       return false;
/*      */     }
/*      */     
/*  971 */     sysMaintenanceLog.resetParameter();
/*  972 */     sysMaintenanceLog.setRelatedId((new Integer(paramString1)).intValue());
/*  973 */     wFManager.setWfid((new Integer(paramString1)).intValue());
/*  974 */     wFManager.getWfInfo();
/*  975 */     sysMaintenanceLog.setRelatedName(wFManager.getWfname());
/*  976 */     sysMaintenanceLog.setOperateType("3");
/*  977 */     sysMaintenanceLog.setOperateDesc("WrokFlow_delete");
/*  978 */     sysMaintenanceLog.setOperateItem("85");
/*  979 */     sysMaintenanceLog.setOperateUserid(paramInt);
/*  980 */     sysMaintenanceLog.setClientAddress(paramString2);
/*  981 */     sysMaintenanceLog.setIstemplate(0);
/*  982 */     sysMaintenanceLog.setSysLogInfo();
/*  983 */     wFMainManager.DeleteWf(new String[] { paramString1 });
/*      */ 
/*      */     
/*  986 */     deleteWFVersionInfo(paramString1);
/*      */     
/*  988 */     workflowComInfo.removeWorkflowCache();
/*      */ 
/*      */     
/*      */     try {
/*  992 */       RecordSet recordSet = new RecordSet();
/*  993 */       String str = "delete from mode_triggerworkflowset where workflowid=" + paramString1;
/*  994 */       recordSet.executeSql(str);
/*  995 */       str = "delete from mode_workflowtomodesetdetail where mainid in(select id from mode_workflowtomodeset where workflowid=" + paramString1 + ")";
/*  996 */       recordSet.executeSql(str);
/*  997 */       str = "delete from mode_workflowtomodesetopt where mainid in(select id from mode_workflowtomodeset where workflowid=" + paramString1 + ")";
/*  998 */       recordSet.executeSql(str);
/*  999 */       str = "delete from mode_workflowtomodeset where workflowid =" + paramString1 + "";
/* 1000 */       recordSet.executeSql(str);
/* 1001 */       str = "delete from workflow_addinoperate where workflowid =?";
/* 1002 */       recordSet.executeUpdate(str, new Object[] { paramString1 });
/* 1003 */       str = "delete from workflowactionset where workflowid =?";
/* 1004 */       recordSet.executeUpdate(str, new Object[] { paramString1 });
/* 1005 */     } catch (Exception exception) {
/* 1006 */       exception.printStackTrace();
/*      */     } 
/*      */ 
/*      */     
/* 1010 */     CptWorkflowSetUitl cptWorkflowSetUitl = new CptWorkflowSetUitl();
/* 1011 */     cptWorkflowSetUitl.delCptWorkflowSet(paramString1);
/*      */     
/* 1013 */     return true;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static int getWorkflowStatus(String paramString) {
/* 1023 */     int i = 0;
/* 1024 */     RecordSet recordSet = new RecordSet();
/*      */     
/* 1026 */     String str = "select isvalid FROM workflow_base where id=" + paramString;
/* 1027 */     recordSet.executeSql(str);
/* 1028 */     if (recordSet.next()) {
/* 1029 */       i = Util.getIntValue(recordSet.getString("isvalid"), 0);
/*      */     }
/* 1031 */     return i;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static void updateAllVersionName(String paramString1, String paramString2, int paramInt1, int paramInt2) {
/* 1041 */     String str1 = getAllVersionStringByWFIDs(paramString1);
/* 1042 */     String str2 = "update workflow_base set workflowname='" + paramString2 + "',  workflowtype='" + paramInt1 + "' ";
/*      */     
/* 1044 */     if ((new ManageDetachComInfo()).isUseWfManageDetach()) {
/* 1045 */       str2 = str2 + " ,subcompanyid =" + paramInt2;
/*      */     }
/* 1047 */     str2 = str2 + " where id in (" + str1 + ")";
/* 1048 */     RecordSet recordSet = new RecordSet();
/* 1049 */     recordSet.executeSql(str2);
/*      */   }
/*      */   
/*      */   public String getVersionStringByWfid() {
/* 1053 */     this; return getVersionStringByWfid(this.workflowId);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static String getVersionStringByWfid(String paramString) {
/* 1063 */     if ("".equals(Util.null2String(paramString))) {
/* 1064 */       paramString = "-1";
/*      */     }
/* 1066 */     String str1 = "";
/* 1067 */     String str2 = " select wfid from Workflow_VersionInfo where wfversionid = " + paramString;
/* 1068 */     String str3 = "";
/* 1069 */     RecordSet recordSet = new RecordSet();
/* 1070 */     recordSet.executeSql(str2);
/* 1071 */     while (recordSet.next()) {
/* 1072 */       str3 = recordSet.getString("wfid");
/* 1073 */       if ("".equals(str1)) {
/* 1074 */         str1 = str3; continue;
/*      */       } 
/* 1076 */       str1 = str1 + "," + str3;
/*      */     } 
/*      */     
/* 1079 */     if ("".equals(str1)) {
/* 1080 */       str1 = paramString;
/*      */     }
/* 1082 */     if (("," + str1 + ",").indexOf("," + paramString + ",") == -1) {
/* 1083 */       str1 = str1 + "," + paramString;
/*      */     }
/* 1085 */     return str1;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public List<String> getVersionListByWfid(String paramString) {
/* 1095 */     ArrayList<String> arrayList = new ArrayList();
/* 1096 */     if (!"".equals(paramString) && paramString != null) {
/* 1097 */       String str1 = " select wfid from Workflow_VersionInfo where wfversionid = " + paramString;
/* 1098 */       String str2 = "";
/* 1099 */       RecordSet recordSet = new RecordSet();
/* 1100 */       recordSet.executeSql(str1);
/* 1101 */       while (recordSet.next()) {
/* 1102 */         str2 = recordSet.getString("wfid");
/* 1103 */         arrayList.add(str2);
/*      */       } 
/*      */     } 
/* 1106 */     if (!arrayList.contains(paramString)) {
/* 1107 */       arrayList.add(paramString);
/*      */     }
/* 1109 */     return arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void saveWorkflowVersionInfo(String paramString) {
/* 1116 */     RecordSet recordSet = new RecordSet();
/* 1117 */     recordSet.executeSql("insert into Workflow_VersionInfo(wfid,wfversionid) values(" + paramString + "," + paramString + ") ");
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void saveWorkflowVersionInfo(String paramString1, String paramString2) {
/* 1124 */     RecordSet recordSet = new RecordSet();
/* 1125 */     List<String> list1 = getVersionListByWfid(paramString2);
/* 1126 */     List<String> list2 = list1; byte b;
/* 1127 */     for (b = 0; b < list1.size(); b++) {
/* 1128 */       recordSet.executeSql("insert into Workflow_VersionInfo(wfid,wfversionid) values(" + paramString1 + "," + (String)list1.get(b) + ") ");
/*      */     }
/* 1130 */     list2.add(paramString1);
/* 1131 */     for (b = 0; b < list2.size(); b++) {
/* 1132 */       recordSet.executeSql("insert into Workflow_VersionInfo(wfid,wfversionid) values(" + (String)list2.get(b) + "," + paramString1 + ") ");
/*      */     }
/*      */   }
/*      */   
/*      */   public static void deleteWFVersionInfo(String paramString) {
/* 1137 */     RecordSet recordSet = new RecordSet();
/* 1138 */     recordSet.executeSql("delete from Workflow_VersionInfo where wfid = " + paramString + " or wfversionid = " + paramString);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void relationNode(int paramInt, String paramString) {
/* 1148 */     String str = "insert into workflow_versionNodeRelation(nodeid, parentNodeid) values (" + paramInt + ", " + paramString + ")";
/* 1149 */     RecordSet recordSet = new RecordSet();
/* 1150 */     recordSet.execute(str);
/*      */   }
/*      */   
/*      */   public String getWorkflowId() {
/* 1154 */     return this.workflowId;
/*      */   }
/*      */   
/*      */   public void setWorkflowId(String paramString) {
/* 1158 */     this.workflowId = paramString;
/*      */   }
/*      */   
/*      */   private String convertOpenTextNodesInfo(Map<String, String> paramMap, String paramString) {
/* 1162 */     if (null == paramString || "".equals(paramString.trim())) {
/* 1163 */       return "";
/*      */     }
/* 1165 */     StringBuffer stringBuffer = new StringBuffer();
/* 1166 */     String[] arrayOfString = paramString.split(",");
/* 1167 */     for (byte b = 0; b < arrayOfString.length; b++) {
/* 1168 */       stringBuffer.append(paramMap.get(arrayOfString[b]));
/* 1169 */       if (b != arrayOfString.length - 1) {
/* 1170 */         stringBuffer.append(",");
/*      */       }
/*      */     } 
/* 1173 */     return stringBuffer.toString();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public int getNodeidByWfidAndOldnodeid(int paramInt1, int paramInt2) {
/* 1184 */     int i = -1;
/* 1185 */     RecordSet recordSet = new RecordSet();
/* 1186 */     List<String> list = getChildrenNodeListByNodeID(paramInt2 + "");
/* 1187 */     int j = list.size();
/* 1188 */     if (j == 0) {
/* 1189 */       return i;
/*      */     }
/*      */     
/* 1192 */     String str = "select max(nodeid) maxnodeid from workflow_flownode where nodeid in ( ";
/* 1193 */     for (byte b = 0; b < j; b++) {
/* 1194 */       if (b > 0) {
/* 1195 */         str = str + ",";
/*      */       }
/* 1197 */       str = str + "?";
/*      */     } 
/* 1199 */     str = str + " ) and workflowid = ?";
/* 1200 */     list.add(String.valueOf(paramInt1));
/*      */     
/* 1202 */     recordSet.executeQuery(str, new Object[] { list });
/* 1203 */     if (recordSet.next()) {
/* 1204 */       i = recordSet.getInt("maxnodeid");
/*      */     }
/* 1206 */     return i;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static String getActiveVersionId(String paramString) {
/* 1216 */     WorkflowComInfo workflowComInfo = new WorkflowComInfo();
/* 1217 */     List list = Util.splitString2List(paramString, ",");
/* 1218 */     StringBuffer stringBuffer = new StringBuffer();
/* 1219 */     for (String str : list) {
/* 1220 */       if ("1".equals(workflowComInfo.getIsValid(str)) || Util.getIntValue(str, 0) < 0) {
/* 1221 */         stringBuffer.append(str).append(",");
/*      */       }
/*      */     } 
/* 1224 */     if (stringBuffer.length() > 1) {
/* 1225 */       stringBuffer.delete(stringBuffer.length() - 1, stringBuffer.length());
/*      */     }
/* 1227 */     return stringBuffer.toString();
/*      */   }
/*      */ 
/*      */   
/*      */   public static String getActiveNodeid(String paramString) {
/* 1232 */     StringBuffer stringBuffer = new StringBuffer();
/* 1233 */     if (!"".equals(Util.null2String(paramString))) {
/* 1234 */       if (paramString.endsWith(","))
/* 1235 */         paramString = paramString.substring(0, paramString.length() - 1); 
/* 1236 */       String str1 = "";
/* 1237 */       RecordSet recordSet = new RecordSet();
/* 1238 */       String str2 = Util.getSubINClause(paramString, "t2.nodeid", "in");
/* 1239 */       String str3 = "select t2.nodeid from workflow_base t1,workflow_flownode t2 where t1.id=t2.workflowid and t1.isvalid='1' and " + str2;
/* 1240 */       recordSet.executeQuery(str3, new Object[0]);
/* 1241 */       while (recordSet.next()) {
/* 1242 */         str1 = Util.null2String(recordSet.getString("nodeid"));
/* 1243 */         stringBuffer.append("".equals(str1) ? "" : (str1 + ","));
/*      */       } 
/* 1245 */       if (stringBuffer.length() > 1) {
/* 1246 */         stringBuffer.delete(stringBuffer.length() - 1, stringBuffer.length());
/*      */       }
/*      */     } 
/* 1249 */     return stringBuffer.toString();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static String getAllActiveWfIds(String paramString) {
/* 1259 */     String str = "";
/* 1260 */     if (paramString != null && !"".equals(paramString)) {
/* 1261 */       RecordSet recordSet = new RecordSet();
/* 1262 */       String str1 = "select distinct (case when (activeversionid ='' or activeversionid is null) then id else activeversionid end ) as versionid   from workflow_base where 1=1 and  " + Util.getSubINClause(paramString, "id", "in");
/* 1263 */       recordSet.executeQuery(str1, new Object[0]);
/* 1264 */       StringBuilder stringBuilder = new StringBuilder();
/* 1265 */       while (recordSet.next()) {
/* 1266 */         stringBuilder.append(",").append(recordSet.getString("versionid"));
/*      */       }
/* 1268 */       if (stringBuilder.length() > 0) {
/* 1269 */         str = stringBuilder.substring(1, stringBuilder.length());
/*      */       }
/*      */     } 
/* 1272 */     return str;
/*      */   }
/*      */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/workflow/WorkflowVersion.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */