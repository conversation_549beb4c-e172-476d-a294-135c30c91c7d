/*     */ package weaver.workflow.workflow;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Iterator;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.dateformat.DateTransformer;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class WorkFlowUtil
/*     */   extends BaseBean
/*     */ {
/*     */   public boolean hasDetail(int paramInt, String paramString) {
/*  37 */     boolean bool = false;
/*  38 */     if (!paramString.equals("1") || (paramInt != 7 && paramInt != 156 && paramInt != 157 && paramInt != 158 && paramInt != 159)) {
/*     */       
/*  40 */       RecordSet recordSet = new RecordSet();
/*  41 */       if (paramString.equals("0")) {
/*  42 */         recordSet
/*  43 */           .executeSql("select count(*) from workflow_formfield  where isdetail='1' and formid=" + paramInt);
/*     */       } else {
/*     */         
/*  46 */         recordSet
/*  47 */           .executeSql("select count(*) from workflow_billfield  where viewtype=1 and billid=" + paramInt);
/*     */       } 
/*     */       
/*  50 */       if (recordSet.next() && 
/*  51 */         recordSet.getInt(1) > 0) {
/*  52 */         bool = true;
/*     */       }
/*     */     } 
/*  55 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getQueryWenbenStr(String paramString1, String paramString2, String paramString3, String paramString4, String paramString5, boolean paramBoolean1, boolean paramBoolean2) {
/*  66 */     StringBuffer stringBuffer = new StringBuffer(50);
/*  67 */     if (!paramString4.equals("")) {
/*  68 */       if (paramString3.equals("1")) {
/*  69 */         stringBuffer.append(" and (" + paramString1 + "." + paramString5 + " like '%" + paramString4 + "%' ");
/*     */       }
/*  71 */       if (paramString3.equals("2")) {
/*  72 */         stringBuffer.append(" and (" + paramString1 + "." + paramString5 + " <>'" + paramString4 + "' ");
/*     */       }
/*  74 */       if (paramString3.equals("3")) {
/*  75 */         ArrayList<String> arrayList = Util.TokenizerString(Util.StringReplace(paramString4, "　", " "), " ");
/*     */         
/*  77 */         stringBuffer.append(" and (");
/*  78 */         for (byte b = 0; b < arrayList.size(); b++) {
/*  79 */           if (b == 0) {
/*  80 */             stringBuffer.append("" + paramString1 + "." + paramString5);
/*     */           } else {
/*  82 */             stringBuffer.append(" or " + paramString1 + "." + paramString5);
/*  83 */           }  paramString4 = Util.StringReplace(Util.StringReplace(arrayList
/*  84 */                 .get(b), "+", "%"), "＋", "%");
/*  85 */           if (!paramBoolean1 && !paramBoolean2) {
/*  86 */             int i = paramString4.indexOf("[");
/*  87 */             if (i < 0)
/*  88 */               i = paramString4.indexOf("]"); 
/*  89 */             if (i < 0) {
/*  90 */               stringBuffer.append(" like '%" + paramString4 + "%' ");
/*     */             } else {
/*  92 */               stringBuffer.append(" like '%" + 
/*  93 */                   Util.StringReplace(Util.StringReplace(
/*  94 */                       Util.StringReplace(paramString4, "/", "//"), "[", "/["), "]", "/]") + "%' ESCAPE '/' ");
/*     */             }
/*     */           
/*     */           } else {
/*     */             
/*  99 */             stringBuffer.append(" like '%" + paramString4 + "%' ");
/*     */           } 
/*     */         } 
/*     */       } 
/* 103 */       if (paramString3.equals("4")) {
/* 104 */         ArrayList<String> arrayList = Util.TokenizerString(Util.StringReplace(paramString4, "　", " "), " ");
/*     */         
/* 106 */         for (byte b = 0; b < arrayList.size(); b++) {
/* 107 */           if (b == 0) {
/* 108 */             stringBuffer.append("and (" + paramString1 + "." + paramString5);
/*     */           } else {
/* 110 */             stringBuffer.append(" and " + paramString1 + "." + paramString5);
/* 111 */           }  paramString4 = Util.StringReplace(Util.StringReplace(arrayList
/* 112 */                 .get(b), "+", "%"), "＋", "%");
/* 113 */           if (!paramBoolean1 && !paramBoolean2) {
/* 114 */             int i = paramString4.indexOf("[");
/* 115 */             if (i < 0)
/* 116 */               i = paramString4.indexOf("]"); 
/* 117 */             if (i < 0) {
/* 118 */               stringBuffer.append(" not like '%" + paramString4 + "%' ");
/*     */             } else {
/* 120 */               stringBuffer.append(" not like '%" + 
/* 121 */                   Util.StringReplace(Util.StringReplace(
/* 122 */                       Util.StringReplace(paramString4, "/", "//"), "[", "/["), "]", "/]") + "%' ESCAPE '/' ");
/*     */             }
/*     */           
/*     */           } else {
/*     */             
/* 127 */             stringBuffer.append(" not like '%" + paramString4 + "%' ");
/*     */           } 
/*     */         } 
/*     */       } 
/*     */     } 
/* 132 */     return stringBuffer.toString();
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public String getQueryShuziStr(String paramString1, String paramString2, String paramString3, String paramString4, String paramString5, String paramString6, String paramString7, String paramString8) {
/* 138 */     boolean bool = (new RecordSet()).getDBType().equals("mysql");
/* 139 */     StringBuffer stringBuffer = new StringBuffer(50);
/*     */     
/* 141 */     if (paramString8.equals("5") && (!paramString5.equals("") || !paramString6.equals(""))) {
/* 142 */       stringBuffer.append("and " + paramString1 + "." + paramString7 + " is not null ");
/*     */     }
/* 144 */     if (!paramString5.equals("")) {
/* 145 */       if (paramString8.equals("5")) {
/* 146 */         stringBuffer.append("and REPLACE(" + paramString1 + "." + paramString7 + ", ',', '')");
/* 147 */         if (bool) {
/* 148 */           paramString5 = "CAST( " + paramString5 + " AS DECIMAL(65 , 4))";
/*     */         } else {
/* 150 */           paramString5 = "CAST( " + paramString5 + " AS FLOAT)";
/*     */         } 
/*     */       } else {
/* 153 */         stringBuffer.append("and (" + paramString1 + "." + paramString7);
/*     */       } 
/* 155 */       if (paramString3.equals("1"))
/* 156 */         stringBuffer.append(" >" + paramString5 + " "); 
/* 157 */       if (paramString3.equals("2"))
/* 158 */         stringBuffer.append(" >=" + paramString5 + " "); 
/* 159 */       if (paramString3.equals("3"))
/* 160 */         stringBuffer.append(" <" + paramString5 + " "); 
/* 161 */       if (paramString3.equals("4"))
/* 162 */         stringBuffer.append(" <=" + paramString5 + " "); 
/* 163 */       if (paramString3.equals("5"))
/* 164 */         stringBuffer.append(" =" + paramString5 + " "); 
/* 165 */       if (paramString3.equals("6")) {
/* 166 */         stringBuffer.append(" <>" + paramString5 + " ");
/*     */       }
/*     */     } 
/* 169 */     if (!paramString6.equals("")) {
/* 170 */       if (paramString8.equals("5")) {
/* 171 */         stringBuffer.append("and REPLACE(" + paramString1 + "." + paramString7 + ", ',', '')");
/* 172 */         if (bool) {
/* 173 */           paramString6 = "CAST( " + paramString6 + " AS DECIMAL(65 , 4))";
/*     */         } else {
/* 175 */           paramString6 = "CAST( " + paramString6 + " AS FLOAT)";
/*     */         } 
/*     */       } else {
/* 178 */         stringBuffer.append("and (" + paramString1 + "." + paramString7);
/*     */       } 
/* 180 */       if (paramString4.equals("1"))
/* 181 */         stringBuffer.append(" >" + paramString6 + " "); 
/* 182 */       if (paramString4.equals("2"))
/* 183 */         stringBuffer.append(" >=" + paramString6 + " "); 
/* 184 */       if (paramString4.equals("3"))
/* 185 */         stringBuffer.append(" <" + paramString6 + " "); 
/* 186 */       if (paramString4.equals("4"))
/* 187 */         stringBuffer.append(" <=" + paramString6 + " "); 
/* 188 */       if (paramString4.equals("5"))
/* 189 */         stringBuffer.append(" =" + paramString6 + " "); 
/* 190 */       if (paramString4.equals("6"))
/* 191 */         stringBuffer.append(" <>" + paramString6 + " "); 
/*     */     } 
/* 193 */     return stringBuffer.toString();
/*     */   }
/*     */ 
/*     */   
/*     */   public String getQueryCheckboxStr(String paramString1, String paramString2, String paramString3) {
/* 198 */     StringBuffer stringBuffer = new StringBuffer(50);
/* 199 */     stringBuffer.append("and (" + paramString1 + "." + paramString3);
/* 200 */     if (!paramString2.equals("1")) {
/* 201 */       stringBuffer.append("<>'1' ");
/*     */     } else {
/* 203 */       stringBuffer.append("='1' ");
/* 204 */     }  return stringBuffer.toString();
/*     */   }
/*     */   
/*     */   public String getQueryLiulankuangStr(String paramString1, String paramString2, String paramString3, String paramString4, boolean paramBoolean) {
/* 208 */     return getQueryLiulankuangStr(paramString1, paramString2, paramString3, paramString4, paramBoolean, ",");
/*     */   }
/*     */   public String getQueryLiulankuangStr(String paramString1, String paramString2, String paramString3, String paramString4, boolean paramBoolean, String paramString5) {
/* 211 */     RecordSet recordSet = new RecordSet();
/* 212 */     boolean bool = recordSet.getDBType().equals("mysql");
/* 213 */     StringBuffer stringBuffer = new StringBuffer(50);
/* 214 */     if (paramBoolean) {
/* 215 */       stringBuffer.append("and ('" + paramString5 + "'||" + paramString1 + "." + paramString3 + "||'" + paramString5 + "'");
/* 216 */     } else if (recordSet.getDBType().equalsIgnoreCase("postgresql")) {
/* 217 */       stringBuffer.append("and ('" + paramString5 + "'||" + paramString1 + "." + paramString3 + "||'" + paramString5 + "'");
/* 218 */     } else if (bool) {
/* 219 */       stringBuffer.append("and (concat('" + paramString5 + "' , " + paramString1 + "." + paramString3 + " , '" + paramString5 + "') ");
/*     */     } else {
/* 221 */       stringBuffer.append("and ('" + paramString5 + "'+CONVERT(varchar," + paramString1 + "." + paramString3 + ")+'" + paramString5 + "' ");
/*     */     } 
/* 223 */     if (paramString4.equals("1"))
/* 224 */       stringBuffer.append(" like '%" + paramString5 + "" + paramString2 + "" + paramString5 + "%' "); 
/* 225 */     if (paramString4.equals("2"))
/* 226 */       stringBuffer.append(" not like '%" + paramString5 + "" + paramString2 + "" + paramString5 + "%' "); 
/* 227 */     return stringBuffer.toString();
/*     */   }
/*     */ 
/*     */   
/*     */   public String getQuerySelectStr(String paramString1, String paramString2, String paramString3, String paramString4) {
/* 232 */     StringBuffer stringBuffer = new StringBuffer(50);
/* 233 */     RecordSet recordSet = new RecordSet();
/*     */     
/* 235 */     if (paramString3.equals("1") || paramString3.equals("2")) {
/* 236 */       stringBuffer.append("and (" + paramString1 + "." + paramString4);
/* 237 */       if (paramString2.equals("")) {
/* 238 */         if (paramString3.equals("1"))
/* 239 */           stringBuffer.append(" is null "); 
/* 240 */         if (paramString3.equals("2"))
/* 241 */           stringBuffer.append(" is not  null "); 
/*     */       } else {
/* 243 */         if (paramString3.equals("1"))
/* 244 */           stringBuffer.append(" =" + paramString2 + " "); 
/* 245 */         if (paramString3.equals("2")) {
/* 246 */           stringBuffer.append(" <>" + paramString2 + " ");
/*     */         }
/*     */       }
/*     */     
/* 250 */     } else if (paramString2.equals("")) {
/* 251 */       stringBuffer.append("and (" + paramString1 + "." + paramString4);
/* 252 */       if (paramString3.equals("3") || paramString3.equals("7"))
/* 253 */         stringBuffer.append(" is null "); 
/* 254 */       if (paramString3.equals("4") || paramString3.equals("8"))
/* 255 */         stringBuffer.append(" is not  null "); 
/*     */     } else {
/* 257 */       if (paramString3.equals("7") || paramString3.equals("8")) {
/* 258 */         String str = "";
/* 259 */         str = str + "and (" + paramString1 + "." + paramString4;
/* 260 */         if (paramString3.equals("7")) str = str + " in (" + paramString2 + ") "; 
/* 261 */         if (paramString3.equals("8")) str = str + " not in (" + paramString2 + ") or " + paramString1 + "." + paramString4 + " is null "; 
/* 262 */         stringBuffer.append(str);
/*     */       } 
/* 264 */       if (paramString3.equals("3") || paramString3.equals("4")) {
/* 265 */         String str = "";
/* 266 */         if ("oracle".equals(recordSet.getDBType())) {
/* 267 */           if (paramString3.equals("3")) str = str + "and ( ',' || " + paramString1 + "." + paramString4 + " || ',' like '%,' || '" + paramString2 + "' || ',%' "; 
/* 268 */           if (paramString3.equals("4")) str = str + "and ( ',' || " + paramString1 + "." + paramString4 + " || ',' not like '%,' || '" + paramString2 + "' || ',%' "; 
/* 269 */         } else if ("mysql".equals(recordSet.getDBType())) {
/* 270 */           if (paramString3.equals("3")) str = str + "and ( concat(',' , " + paramString1 + "." + paramString4 + " , ',') like concat('%,' , '" + paramString2 + "' , ',%') "; 
/* 271 */           if (paramString3.equals("4")) str = str + "and ( concat(',' , " + paramString1 + "." + paramString4 + " , ',') not like concat('%,' , '" + paramString2 + "' , ',%') ";
/*     */         
/* 273 */         } else if ("postgresql".equals(recordSet.getDBType())) {
/* 274 */           if (paramString3.equals("3")) str = str + "and ( ',' || " + paramString1 + "." + paramString4 + " || ',' like '%,' || '" + paramString2 + "' || ',%' "; 
/* 275 */           if (paramString3.equals("4")) str = str + "and ( ',' || " + paramString1 + "." + paramString4 + " || ',' not like '%,' || '" + paramString2 + "' || ',%' ";
/*     */         
/*     */         } else {
/* 278 */           if (paramString3.equals("3")) str = str + "and ( ',' + cast(" + paramString1 + "." + paramString4 + " as varchar(max)) + ',' like '%,' + '" + paramString2 + "' + ',%' "; 
/* 279 */           if (paramString3.equals("4")) str = str + "and ( ',' + cast(" + paramString1 + "." + paramString4 + " as varchar(max)) + ',' not like '%,' + '" + paramString2 + "' + ',%' "; 
/*     */         } 
/* 281 */         stringBuffer.append(str);
/*     */       } 
/*     */     } 
/*     */     
/* 285 */     return stringBuffer.toString();
/*     */   }
/*     */ 
/*     */   
/*     */   public String getQueryRenliStr(String paramString1, String paramString2, String paramString3, String paramString4) {
/* 290 */     StringBuffer stringBuffer = new StringBuffer(50);
/* 291 */     if (!paramString2.equals("")) {
/* 292 */       stringBuffer.append("and (" + paramString1 + "." + paramString4);
/* 293 */       if (paramString3.equals("1"))
/* 294 */         stringBuffer.append(" in (" + paramString2 + ") "); 
/* 295 */       if (paramString3.equals("2"))
/* 296 */         stringBuffer.append(" not in (" + paramString2 + ") "); 
/*     */     } 
/* 298 */     return stringBuffer.toString();
/*     */   }
/*     */   
/*     */   public String getQueryCustomStr(String paramString1, String paramString2, String paramString3, String paramString4) {
/* 302 */     StringBuffer stringBuffer = new StringBuffer(200);
/* 303 */     String str = "";
/* 304 */     for (String str1 : paramString2.split(",")) {
/* 305 */       if (!"".equals(str1))
/* 306 */         str = str + ",'" + str1 + "'"; 
/*     */     } 
/* 308 */     if (str.startsWith(","))
/* 309 */       str = str.substring(1); 
/* 310 */     if (!"".equals(str)) {
/* 311 */       stringBuffer.append("and (" + paramString1 + "." + paramString4);
/* 312 */       if (paramString3.equals("1"))
/* 313 */         stringBuffer.append(" in (" + str + ") "); 
/* 314 */       if (paramString3.equals("2"))
/* 315 */         stringBuffer.append(" not in (" + str + ") "); 
/*     */     } 
/* 317 */     return stringBuffer.toString();
/*     */   }
/*     */ 
/*     */   
/*     */   public String getQueryZhiweianquanStr(String paramString1, String paramString2, String paramString3, String paramString4, String paramString5, String paramString6) {
/* 322 */     StringBuffer stringBuffer = new StringBuffer(50);
/* 323 */     if (!paramString2.equals("")) {
/* 324 */       stringBuffer.append("and (" + paramString1 + "." + paramString6);
/* 325 */       if (paramString4.equals("1"))
/* 326 */         stringBuffer.append(" >" + paramString2 + " "); 
/* 327 */       if (paramString4.equals("2"))
/* 328 */         stringBuffer.append(" >=" + paramString2 + " "); 
/* 329 */       if (paramString4.equals("3"))
/* 330 */         stringBuffer.append(" <" + paramString2 + " "); 
/* 331 */       if (paramString4.equals("4"))
/* 332 */         stringBuffer.append(" <=" + paramString2 + " "); 
/* 333 */       if (paramString4.equals("5"))
/* 334 */         stringBuffer.append(" =" + paramString2 + " "); 
/* 335 */       if (paramString4.equals("6")) {
/* 336 */         stringBuffer.append(" <>" + paramString2 + " ");
/*     */       }
/*     */     } 
/* 339 */     if (!paramString3.equals("")) {
/* 340 */       stringBuffer.append(" and " + paramString1 + "." + paramString6);
/* 341 */       if (paramString5.equals("1"))
/* 342 */         stringBuffer.append(" >" + paramString3 + " "); 
/* 343 */       if (paramString5.equals("2"))
/* 344 */         stringBuffer.append(" >=" + paramString3 + " "); 
/* 345 */       if (paramString5.equals("3"))
/* 346 */         stringBuffer.append(" <" + paramString3 + " "); 
/* 347 */       if (paramString5.equals("4"))
/* 348 */         stringBuffer.append(" <=" + paramString3 + " "); 
/* 349 */       if (paramString5.equals("5"))
/* 350 */         stringBuffer.append(" =" + paramString3 + " "); 
/* 351 */       if (paramString5.equals("6"))
/* 352 */         stringBuffer.append(" <>" + paramString3 + " "); 
/*     */     } 
/* 354 */     return stringBuffer.toString();
/*     */   }
/*     */ 
/*     */   
/*     */   public String getQueryRiqiStr(String paramString1, String paramString2, String paramString3, String paramString4, String paramString5, String paramString6) {
/* 359 */     StringBuffer stringBuffer = new StringBuffer(50);
/* 360 */     if (!paramString2.equals("")) {
/* 361 */       stringBuffer.append("and (" + paramString1 + "." + paramString6);
/* 362 */       if (paramString4.equals("1"))
/* 363 */         stringBuffer.append(" >'" + paramString2 + "' "); 
/* 364 */       if (paramString4.equals("2"))
/* 365 */         stringBuffer.append(" >='" + paramString2 + "' "); 
/* 366 */       if (paramString4.equals("3"))
/* 367 */         stringBuffer.append(" <'" + paramString2 + "' "); 
/* 368 */       if (paramString4.equals("4"))
/* 369 */         stringBuffer.append(" <='" + paramString2 + "' "); 
/* 370 */       if (paramString4.equals("5"))
/* 371 */         stringBuffer.append(" ='" + paramString2 + "' "); 
/* 372 */       if (paramString4.equals("6")) {
/* 373 */         stringBuffer.append(" <>'" + paramString2 + "' ");
/*     */       }
/*     */     } 
/* 376 */     if (!paramString3.equals("")) {
/* 377 */       stringBuffer.append(" and (" + paramString1 + "." + paramString6);
/* 378 */       if (paramString5.equals("1"))
/* 379 */         stringBuffer.append(" >'" + paramString3 + "' "); 
/* 380 */       if (paramString5.equals("2"))
/* 381 */         stringBuffer.append(" >='" + paramString3 + "' "); 
/* 382 */       if (paramString5.equals("3"))
/* 383 */         stringBuffer.append(" <'" + paramString3 + "' "); 
/* 384 */       if (paramString5.equals("4"))
/* 385 */         stringBuffer.append(" <='" + paramString3 + "' "); 
/* 386 */       if (paramString5.equals("5"))
/* 387 */         stringBuffer.append(" ='" + paramString3 + "' "); 
/* 388 */       if (paramString5.equals("6"))
/* 389 */         stringBuffer.append(" <>'" + paramString3 + "' "); 
/*     */     } 
/* 391 */     return stringBuffer.toString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List getDetailTables(int paramInt, String paramString) {
/* 402 */     ArrayList<String> arrayList = new ArrayList();
/*     */ 
/*     */     
/* 405 */     if (paramString.equals("1")) {
/* 406 */       RecordSet recordSet = new RecordSet();
/*     */       
/* 408 */       recordSet
/* 409 */         .execute("select tablename,title from Workflow_billdetailtable where billid=" + paramInt + " order by orderid");
/*     */       
/* 411 */       System.out
/* 412 */         .println("查询明细名select tablename,title from Workflow_billdetailtable where billid=" + paramInt + " order by orderid");
/*     */       
/* 414 */       while (recordSet.next()) {
/* 415 */         arrayList.add(recordSet.getString("tablename"));
/*     */       }
/*     */     } 
/*     */ 
/*     */     
/* 420 */     return arrayList;
/*     */   }
/*     */ 
/*     */   
/*     */   public String getDetailKey(String paramString) {
/* 425 */     RecordSet recordSet = new RecordSet();
/* 426 */     String str = "mainid";
/* 427 */     recordSet.execute("select detailtablename from workflow_bill where id=" + paramString);
/*     */     
/* 429 */     if (recordSet.next()) {
/* 430 */       str = recordSet.getString("tablename");
/*     */     }
/* 432 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getQuerySqlCondition(String paramString1, String paramString2, String paramString3, String paramString4, String paramString5, String paramString6, String paramString7, String paramString8, String paramString9, boolean paramBoolean1, boolean paramBoolean2) {
/* 452 */     DateTransformer dateTransformer = new DateTransformer();
/* 453 */     String str1 = Util.null2String(getPropValue("weaver_timezone_conversion", "timeZoneConversion")).trim();
/* 454 */     boolean bool = false;
/* 455 */     if ("1".equals(str1))
/*     */     {
/* 457 */       bool = true;
/*     */     }
/* 459 */     String str2 = "";
/* 460 */     if ((paramString2.equals("1") && paramString3.equals("1")) || (paramString2.equals("2") && !paramString3.equals("2"))) {
/* 461 */       str2 = str2 + getQueryWenbenStr(paramString1, paramString4, paramString5, paramString7, paramString9, paramBoolean1, paramBoolean2);
/*     */     }
/* 463 */     else if (paramString2.equals("2") && paramString3.equals("2")) {
/* 464 */       if (paramString5.equals("3") && (paramBoolean1 || paramBoolean2)) {
/* 465 */         str2 = str2 + getQueryWenbenStrHtml(paramString1, paramString4, paramString5, paramString7, paramString9, paramBoolean1, paramBoolean2);
/*     */       } else {
/*     */         
/* 468 */         str2 = str2 + getQueryWenbenStr(paramString1, paramString4, paramString5, paramString7, paramString9, paramBoolean1, paramBoolean2);
/*     */       }
/*     */     
/* 471 */     } else if (paramString2.equals("1") && !paramString3.equals("1")) {
/*     */       
/* 473 */       str2 = str2 + getQueryShuziStr(paramString1, paramString4, paramString5, paramString6, paramString7, paramString8, paramString9, paramString3);
/*     */     
/*     */     }
/* 476 */     else if (paramString2.equals("4")) {
/*     */       
/* 478 */       str2 = str2 + getQueryCheckboxStr(paramString1, paramString7, paramString9);
/* 479 */     } else if (paramString2.equals("5")) {
/* 480 */       str2 = str2 + getQuerySelectStr(paramString1, paramString7, paramString5, paramString9);
/*     */     }
/* 482 */     else if (paramString2.equals("3") && (paramString3
/* 483 */       .equals("1") || paramString3.equals("9") || paramString3.equals("4") || paramString3.equals("164") || paramString3
/* 484 */       .equals("7") || paramString3.equals("8") || paramString3.equals("167") || paramString3
/* 485 */       .equals("16") || paramString3.equals("169") || paramString3.equals("165") || paramString3.equals("24"))) {
/*     */       
/* 487 */       str2 = str2 + getQueryRenliStr(paramString1, paramString7, paramString5, paramString9);
/* 488 */     } else if (paramString2.equals("3") && (paramString3.equals("161") || paramString3.equals("256"))) {
/* 489 */       str2 = str2 + getQueryCustomStr(paramString1, paramString7, paramString5, paramString9);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*     */     }
/* 497 */     else if (paramString2.equals("3") && (paramString3
/* 498 */       .equals("2") || paramString3.equals("19") || paramString3.equals("290") || paramString3.equals("402") || paramString3.equals("403"))) {
/*     */ 
/*     */       
/* 501 */       if (paramString3.equals("290") || paramString3.equals("402") || paramString3.equals("403")) {
/* 502 */         paramString5 = "2";
/* 503 */         paramString6 = "4";
/* 504 */         if (bool) {
/* 505 */           paramString7 = dateTransformer.getServerDateTime(paramString7);
/*     */         }
/*     */       } 
/* 508 */       str2 = str2 + getQueryRiqiStr(paramString1, paramString7, paramString8, paramString5, paramString6, paramString9);
/*     */     
/*     */     }
/* 511 */     else if (paramString2.equals("3") && (paramString3
/* 512 */       .equals("17") || paramString3.equals("57") || paramString3.equals("135") || paramString3
/* 513 */       .equals("152") || paramString3.equals("18") || paramString3.equals("160") || paramString3.equals("166") || paramString3
/* 514 */       .equals("168") || paramString3.equals("170") || paramString3.equals("194") || paramString3.equals("278"))) {
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 520 */       str2 = str2 + getQueryLiulankuangStr(paramString1, paramString7, paramString9, paramString5, paramBoolean1);
/*     */     }
/* 522 */     else if (paramString2.equals("3") && (paramString3
/* 523 */       .equals("141") || paramString3.equals("56") || paramString3
/* 524 */       .equals("27") || paramString3.equals("118") || paramString3
/* 525 */       .equals("65") || paramString3.equals("64") || paramString3
/* 526 */       .equals("137") || paramString3.equals("142"))) {
/*     */       
/* 528 */       if (paramString3.equals("141")) {
/* 529 */         str2 = str2 + getQueryLiulankuangStr(paramString1, paramString7, paramString9, paramString5, paramBoolean1, "~");
/*     */       } else {
/* 531 */         str2 = str2 + getQueryLiulankuangStr(paramString1, paramString7, paramString9, paramString5, paramBoolean1);
/*     */       }
/*     */     
/* 534 */     } else if (paramString2.equals("3")) {
/* 535 */       str2 = str2 + getQueryLiulankuangStr(paramString1, paramString7, paramString9, paramString5, paramBoolean1);
/*     */     }
/* 537 */     else if (paramString2.equals("6")) {
/* 538 */       str2 = str2 + getQueryLiulankuangStr(paramString1, paramString7, paramString9, paramString5, paramBoolean1);
/*     */     } 
/*     */ 
/*     */     
/* 542 */     if (paramString2.equals("1") || paramString2
/* 543 */       .equals("2") || (paramString2
/* 544 */       .equals("3") && (paramString3.equals("1") || paramString3
/* 545 */       .equals("9") || paramString3.equals("4") || paramString3
/* 546 */       .equals("7") || paramString3.equals("8") || paramString3
/* 547 */       .equals("16"))) || (paramString2
/* 548 */       .equals("3") && paramString3.equals("24")) || (paramString2
/* 549 */       .equals("3") && (paramString3.equals("2") || paramString3
/* 550 */       .equals("19") || paramString3.equals("290") || paramString3.equals("402") || paramString3.equals("403")))) {
/* 551 */       if (!paramString7.equals("")) {
/* 552 */         if (paramString3.equals("5")) {
/* 553 */           str2 = str2 + " ";
/* 554 */         } else if (!paramString3.equals("5")) {
/* 555 */           str2 = str2 + ")";
/*     */         } 
/*     */       }
/* 558 */       if (!paramString8.equals("")) {
/* 559 */         if (paramString3.equals("5")) {
/* 560 */           str2 = str2 + " ";
/* 561 */         } else if (!paramString3.equals("5")) {
/* 562 */           str2 = str2 + ")";
/*     */         } 
/*     */       }
/*     */     } else {
/* 566 */       str2 = str2 + ") ";
/* 567 */     }  return str2;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map getDetailInfo(int paramInt, String paramString, User paramUser) {
/* 579 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 580 */     RecordSet recordSet1 = new RecordSet();
/* 581 */     RecordSet recordSet2 = new RecordSet();
/* 582 */     List list = getDetailTables(paramInt, paramString);
/* 583 */     if (paramString.equals("1")) {
/* 584 */       Iterator<String> iterator = list.iterator();
/* 585 */       while (iterator.hasNext()) {
/* 586 */         String str = iterator.next();
/* 587 */         HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 588 */         ArrayList<String> arrayList1 = new ArrayList();
/* 589 */         ArrayList<String> arrayList2 = new ArrayList();
/* 590 */         ArrayList<String> arrayList3 = new ArrayList();
/* 591 */         ArrayList<String> arrayList4 = new ArrayList();
/* 592 */         ArrayList<String> arrayList5 = new ArrayList();
/* 593 */         ArrayList<String> arrayList6 = new ArrayList();
/* 594 */         ArrayList<String> arrayList7 = new ArrayList();
/*     */         
/* 596 */         recordSet2
/* 597 */           .execute("select * from workflow_billfield where viewtype='1' and billid=" + paramInt + " and detailtable='" + str + "' ORDER BY dsporder");
/*     */ 
/*     */ 
/*     */ 
/*     */         
/* 602 */         System.out
/* 603 */           .println("查询明细表字段:select * from workflow_billfield where viewtype='1' and billid=" + paramInt + " and detailtable='" + str + "' ORDER BY dsporder");
/*     */ 
/*     */ 
/*     */ 
/*     */         
/* 608 */         while (recordSet2.next()) {
/* 609 */           arrayList1.add(Util.null2String(recordSet1.getString("id")));
/* 610 */           arrayList2.add(SystemEnv.getHtmlLabelName(
/* 611 */                 Util.getIntValue(recordSet1.getString("fieldlabel")), paramUser
/* 612 */                 .getLanguage()));
/* 613 */           arrayList3.add(Util.null2String(recordSet1
/* 614 */                 .getString("fieldhtmltype")));
/* 615 */           arrayList4.add(Util.null2String(recordSet1.getString("type")));
/* 616 */           arrayList5.add(
/* 617 */               Util.null2String(recordSet1.getString("fieldname")));
/* 618 */           arrayList6.add(Util.null2String(recordSet1
/* 619 */                 .getString("fielddbtype")));
/* 620 */           arrayList7
/* 621 */             .add("" + 
/* 622 */               Util.getIntValue(recordSet1
/* 623 */                 .getString("childfieldid"), 0));
/*     */         } 
/*     */         
/* 626 */         hashMap1.put("defieldids", arrayList1);
/* 627 */         hashMap1.put("defieldlabels", arrayList2);
/* 628 */         hashMap1.put("defieldhtmltypes", arrayList3);
/* 629 */         hashMap1.put("defieldtypes", arrayList4);
/* 630 */         hashMap1.put("defieldnames", arrayList5);
/* 631 */         hashMap1.put("fieldrealtype", arrayList6);
/* 632 */         hashMap1.put("childfieldids", arrayList7);
/*     */         
/* 634 */         hashMap.put(str, hashMap1);
/*     */       } 
/*     */     } 
/*     */ 
/*     */     
/* 639 */     return hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getQueryWenbenStrHtml(String paramString1, String paramString2, String paramString3, String paramString4, String paramString5, boolean paramBoolean1, boolean paramBoolean2) {
/* 650 */     StringBuffer stringBuffer = new StringBuffer();
/* 651 */     if (!paramString4.equals("")) {
/* 652 */       ArrayList<String> arrayList = Util.TokenizerString(Util.StringReplace(paramString4, "　", " "), " ");
/*     */       
/* 654 */       stringBuffer.append(" and (");
/* 655 */       for (byte b = 0; b < arrayList.size(); b++) {
/* 656 */         paramString4 = arrayList.get(b);
/* 657 */         ArrayList<String> arrayList1 = Util.TokenizerString(paramString4, "+");
/* 658 */         if (b == 0) {
/* 659 */           stringBuffer.append("(" + paramString1 + "." + paramString5);
/*     */         } else {
/* 661 */           stringBuffer.append(" or (" + paramString1 + "." + paramString5);
/* 662 */         }  for (byte b1 = 0; b1 < arrayList1.size(); b1++) {
/* 663 */           if (b1 == 0) {
/* 664 */             stringBuffer.append(" like '%" + (String)arrayList1.get(b1) + "%' ");
/*     */           } else {
/* 666 */             stringBuffer.append(" and " + paramString1 + "." + paramString5);
/* 667 */             stringBuffer.append(" like '%" + (String)arrayList1.get(b1) + "%' ");
/*     */           } 
/* 669 */           if (b1 == arrayList1.size() - 1) stringBuffer.append(")"); 
/*     */         } 
/*     */       } 
/*     */     } 
/* 673 */     writeLog("buffer:" + stringBuffer.toString());
/* 674 */     return stringBuffer.toString();
/*     */   }
/*     */   
/*     */   public String getWorkflowFieldName(String paramString1, String paramString2) throws Exception {
/* 678 */     String str1 = "";
/* 679 */     String[] arrayOfString = Util.TokenizerString2(paramString2, "+");
/* 680 */     String str2 = arrayOfString[0];
/* 681 */     String str3 = arrayOfString[1];
/* 682 */     if (str2.equals("0")) {
/* 683 */       str1 = paramString1;
/*     */     } else {
/* 685 */       str1 = SystemEnv.getHtmlLabelName(Util.getIntValue(paramString1), Integer.parseInt(str3));
/*     */     } 
/* 687 */     return str1;
/*     */   }
/*     */   
/*     */   public String getWorkflowFieldType(String paramString1, String paramString2) throws Exception {
/* 691 */     String str = "";
/* 692 */     if ("4".equals(paramString1)) {
/* 693 */       str = SystemEnv.getHtmlLabelName(124, Integer.parseInt(paramString2));
/* 694 */     } else if ("57".equals(paramString1)) {
/* 695 */       str = SystemEnv.getHtmlLabelName(17006, Integer.parseInt(paramString2));
/* 696 */     } else if ("167".equals(paramString1)) {
/* 697 */       str = SystemEnv.getHtmlLabelName(21481, Integer.parseInt(paramString2));
/* 698 */     } else if ("168".equals(paramString1)) {
/* 699 */       str = SystemEnv.getHtmlLabelName(21482, Integer.parseInt(paramString2));
/* 700 */     } else if ("164".equals(paramString1)) {
/* 701 */       str = SystemEnv.getHtmlLabelName(141, Integer.parseInt(paramString2));
/* 702 */     } else if ("194".equals(paramString1)) {
/* 703 */       str = SystemEnv.getHtmlLabelName(25512, Integer.parseInt(paramString2));
/* 704 */     } else if ("169".equals(paramString1)) {
/* 705 */       str = SystemEnv.getHtmlLabelName(21483, Integer.parseInt(paramString2));
/* 706 */     } else if ("170".equals(paramString1)) {
/* 707 */       str = SystemEnv.getHtmlLabelName(21484, Integer.parseInt(paramString2));
/*     */     } 
/* 709 */     return str;
/*     */   }
/*     */   
/*     */   public String getWorkflowFieldPosition(String paramString1, String paramString2) throws Exception {
/* 713 */     String str = "";
/* 714 */     if ("0".equals(paramString1)) {
/* 715 */       str = SystemEnv.getHtmlLabelName(18020, Integer.parseInt(paramString2));
/*     */     } else {
/* 717 */       str = SystemEnv.getHtmlLabelName(18550, Integer.parseInt(paramString2));
/*     */     } 
/* 719 */     return str;
/*     */   }
/*     */   
/*     */   public static synchronized int getNewWorkflowGroupid() {
/* 723 */     RecordSet recordSet = new RecordSet();
/* 724 */     int i = 0;
/* 725 */     String str = "select max(id) as id from workflow_nodegroup";
/* 726 */     recordSet.execute(str);
/* 727 */     if (recordSet.next()) {
/* 728 */       i = Util.getIntValue(Util.null2String(recordSet.getString("id")), 0);
/*     */     }
/*     */     
/* 731 */     recordSet.execute("select max(groupid) as id from workflow_groupdetail");
/* 732 */     if (recordSet.next()) {
/* 733 */       int j = Util.getIntValue(Util.null2String(recordSet.getString("id")), 0);
/* 734 */       if (j > i) {
/* 735 */         i = j;
/*     */       }
/*     */     } 
/* 738 */     i++;
/* 739 */     return i;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/workflow/WorkFlowUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */