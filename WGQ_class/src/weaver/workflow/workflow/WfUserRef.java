/*     */ package weaver.workflow.workflow;
/*     */ 
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.ThreadVarLanguage;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.company.DepartmentComInfo;
/*     */ import weaver.hrm.company.SubCompanyComInfo;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class WfUserRef
/*     */ {
/*     */   public void saveUserRef(int paramInt1, String paramString1, int paramInt2, String paramString2) {
/*  18 */     RecordSet recordSet = new RecordSet();
/*     */     
/*  20 */     if (paramInt1 != 0) {
/*  21 */       String str = " select * from workflow_userref where keyid=" + paramInt1;
/*  22 */       recordSet.executeSql(str);
/*  23 */       if (recordSet.next()) {
/*  24 */         str = " update workflow_userref set name='" + paramString1 + "',usertype=" + paramInt2 + ",userids='" + paramString2 + "' where keyid=" + paramInt1;
/*  25 */         recordSet.executeSql(str);
/*     */       } 
/*     */     } else {
/*  28 */       String str = " insert into workflow_userref(name,usertype,userids) values('" + paramString1 + "'," + paramInt2 + ",'" + paramString2 + "') ";
/*  29 */       recordSet.executeSql(str);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void delUserRef(int paramInt) {
/*  37 */     if (paramInt > 0) {
/*  38 */       RecordSet recordSet = new RecordSet();
/*  39 */       recordSet.executeSql(" delete from workflow_userref where keyid=" + paramInt);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String transUserType(String paramString) {
/*  47 */     String str = "";
/*  48 */     if ("1".equals(paramString)) {
/*  49 */       str = "" + SystemEnv.getHtmlLabelName(1340, ThreadVarLanguage.getLang()) + "";
/*  50 */     } else if ("2".equals(paramString)) {
/*  51 */       str = "" + SystemEnv.getHtmlLabelName(141, ThreadVarLanguage.getLang()) + "";
/*  52 */     } else if ("3".equals(paramString)) {
/*  53 */       str = "" + SystemEnv.getHtmlLabelName(18939, ThreadVarLanguage.getLang()) + "";
/*  54 */     } else if ("4".equals(paramString)) {
/*  55 */       str = "" + SystemEnv.getHtmlLabelName(33451, ThreadVarLanguage.getLang()) + "";
/*     */     } 
/*  57 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String transNames(String paramString1, String paramString2) throws Exception {
/*  64 */     String str = "";
/*  65 */     int i = Util.getIntValue(paramString2, 0);
/*  66 */     if (i == 1) {
/*  67 */       str = "" + SystemEnv.getHtmlLabelName(1340, ThreadVarLanguage.getLang()) + "";
/*  68 */     } else if (i == 2) {
/*  69 */       str = getSubCompanyNames(paramString1);
/*  70 */     } else if (i == 3) {
/*  71 */       str = getDepartmentNames(paramString1);
/*  72 */     } else if (i == 4) {
/*  73 */       str = getUserNames(paramString1);
/*     */     } 
/*  75 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getSubCompanyNames(String paramString) throws Exception {
/*  82 */     String str = "";
/*  83 */     SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/*  84 */     String[] arrayOfString = paramString.split(",");
/*  85 */     for (String str1 : arrayOfString) {
/*  86 */       str = str + subCompanyComInfo.getSubCompanyname(str1) + ",";
/*     */     }
/*  88 */     if (str.lastIndexOf(",") > -1)
/*  89 */       str = str.substring(0, str.lastIndexOf(",")); 
/*  90 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getDepartmentNames(String paramString) throws Exception {
/*  97 */     String str = "";
/*  98 */     DepartmentComInfo departmentComInfo = new DepartmentComInfo();
/*  99 */     String[] arrayOfString = paramString.split(",");
/* 100 */     for (String str1 : arrayOfString) {
/* 101 */       str = str + departmentComInfo.getDepartmentname(str1) + ",";
/*     */     }
/* 103 */     if (str.lastIndexOf(",") > -1)
/* 104 */       str = str.substring(0, str.lastIndexOf(",")); 
/* 105 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getUserNames(String paramString) throws Exception {
/* 112 */     String str = "";
/* 113 */     ResourceComInfo resourceComInfo = new ResourceComInfo();
/* 114 */     String[] arrayOfString = paramString.split(",");
/* 115 */     for (String str1 : arrayOfString) {
/* 116 */       str = str + resourceComInfo.getResourcename(str1) + ",";
/*     */     }
/* 118 */     if (str.lastIndexOf(",") > -1)
/* 119 */       str = str.substring(0, str.lastIndexOf(",")); 
/* 120 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Boolean isExist(String paramString1, String paramString2, String paramString3) throws Exception {
/* 127 */     Boolean bool = Boolean.valueOf(false);
/* 128 */     ResourceComInfo resourceComInfo = new ResourceComInfo();
/* 129 */     RecordSet recordSet = new RecordSet();
/* 130 */     String str = "select usertype,userids from workflow_userref where name='" + paramString1 + "' ";
/* 131 */     recordSet.executeSql(str);
/* 132 */     while (recordSet.next()) {
/* 133 */       int i = Util.getIntValue(recordSet.getString("usertype"), 0);
/* 134 */       String str1 = "," + Util.null2String(recordSet.getString("userids")) + ",";
/* 135 */       if (i == 1) {
/* 136 */         bool = Boolean.valueOf(true); break;
/*     */       } 
/* 138 */       if (i == 2) {
/* 139 */         String str2 = resourceComInfo.getSubCompanyID(paramString3);
/* 140 */         if (str1.indexOf("," + str2 + ",") > -1) {
/* 141 */           bool = Boolean.valueOf(true); break;
/*     */         }  continue;
/*     */       } 
/* 144 */       if (i == 3) {
/* 145 */         String str2 = resourceComInfo.getDepartmentID(paramString3);
/* 146 */         if (str1.indexOf("," + str2 + ",") > -1) {
/* 147 */           bool = Boolean.valueOf(true); break;
/*     */         }  continue;
/*     */       } 
/* 150 */       if (i == 4 && 
/* 151 */         str1.indexOf("," + paramString3 + ",") > -1) {
/* 152 */         bool = Boolean.valueOf(true);
/*     */         
/*     */         break;
/*     */       } 
/*     */     } 
/* 157 */     return bool;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/workflow/WfUserRef.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */