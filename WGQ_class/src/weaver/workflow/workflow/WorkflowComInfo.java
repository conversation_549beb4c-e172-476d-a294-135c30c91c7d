/*     */ package weaver.workflow.workflow;
/*     */ 
/*     */ import java.util.Calendar;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import org.apache.commons.logging.Log;
/*     */ import org.apache.commons.logging.LogFactory;
/*     */ import weaver.cache.CacheBase;
/*     */ import weaver.cache.CacheColumn;
/*     */ import weaver.cache.CacheColumnType;
/*     */ import weaver.cache.CacheItem;
/*     */ import weaver.cache.PKColumn;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class WorkflowComInfo
/*     */   extends CacheBase
/*     */ {
/*  36 */   private static final Log log = LogFactory.getLog(WorkflowComInfo.class);
/*     */ 
/*     */   
/*  39 */   protected static String TABLE_NAME = "workflow_base";
/*     */   
/*  41 */   protected static String TABLE_WHERE = "isvalid='1'";
/*     */   
/*  43 */   protected static String TABLE_ORDER = "dsporder asc,workflowname,id";
/*     */   
/*     */   @PKColumn(type = CacheColumnType.NUMBER)
/*  46 */   protected static String PK_NAME = "id";
/*     */   
/*     */   @CacheColumn
/*     */   protected static int workflowname;
/*     */   
/*     */   @CacheColumn
/*     */   protected static int workflowtype;
/*     */   
/*     */   @CacheColumn
/*     */   protected static int isvalid;
/*     */   @CacheColumn
/*     */   protected static int formid;
/*     */   @CacheColumn
/*     */   protected static int isbill;
/*     */   @CacheColumn
/*     */   protected static int multiSubmit;
/*     */   @CacheColumn
/*     */   protected static int needaffirmance;
/*     */   @CacheColumn
/*     */   protected static int isremarks;
/*     */   @CacheColumn
/*     */   protected static int isimportwf;
/*     */   @CacheColumn
/*     */   protected static int isannexUpload;
/*     */   @CacheColumn
/*     */   protected static int forwardReceiveDef;
/*     */   @CacheColumn
/*     */   protected static int isWorkflowDoc;
/*     */   @CacheColumn
/*     */   protected static int officalType;
/*     */   @CacheColumn
/*     */   protected static int traceSaveSecId;
/*     */   @CacheColumn
/*     */   protected static int traceFieldId;
/*     */   @CacheColumn
/*     */   protected static int isModifyLog;
/*     */   @CacheColumn
/*     */   protected static int helpdocid;
/*     */   @CacheColumn
/*     */   protected static int isshared;
/*     */   @CacheColumn
/*     */   protected static int custompage;
/*     */   @CacheColumn
/*     */   protected static int isFree;
/*     */   @CacheColumn
/*     */   protected static int isshowchart;
/*     */   @CacheColumn
/*     */   protected static int activeversionid;
/*  94 */   private String isTemplate = "";
/*     */ 
/*     */   
/*     */   private Map<String, String> workflowCreatedocMap;
/*     */ 
/*     */   
/*     */   public String getTemplate() {
/* 101 */     return this.isTemplate;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setTemplate(String paramString) {
/* 109 */     this.isTemplate = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void reloadWorkflowInfos() {
/*     */     try {
/* 118 */       log.info("Start to reload workflow infos.");
/* 119 */       reload();
/* 120 */       log.info("Successfully reload workflow infos.");
/* 121 */     } catch (Exception exception) {
/* 122 */       log.error("Catch a exception.", exception);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getWorkflowNum() {
/* 131 */     return size();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public boolean next(String paramString) {
/* 139 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getWorkflowid() {
/* 147 */     return (String)getRowValue(0);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getWorkflowname() {
/* 155 */     return Util.formatStringIfMultilang((String)getRowValue(workflowname));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getWorkflowname(String paramString) {
/* 164 */     String str = (String)getValue(workflowname, paramString);
/*     */     
/* 166 */     if ("".equals(Util.null2String(str)) && !"".equals(paramString)) {
/* 167 */       RecordSet recordSet = new RecordSet();
/* 168 */       recordSet.executeSql("select workflowname from workflow_base where id = '" + paramString + "'");
/* 169 */       recordSet.next();
/* 170 */       str = Util.null2String(recordSet.getString("workflowname"));
/*     */     } 
/* 172 */     return Util.formatStringIfMultilang(str);
/*     */   }
/*     */   
/*     */   public String getIsremark(String paramString) {
/* 176 */     return (String)getValue(isremarks, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getNeedaffirmance() {
/* 184 */     return (String)getRowValue(needaffirmance);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getActiveversionid(String paramString) {
/* 194 */     return (String)getValue(activeversionid, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getNeedaffirmance(String paramString) {
/* 203 */     return (String)getValue(needaffirmance, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getWorkflowtype() {
/* 211 */     return (String)getRowValue(workflowtype);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getWorkflowtype(String paramString) {
/* 220 */     return (String)getValue(workflowtype, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getIsImportwf() {
/* 228 */     return (String)getRowValue(isimportwf);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getIsImportwf(String paramString) {
/* 237 */     return (String)getValue(isimportwf, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getIsValid() {
/* 245 */     return (String)getRowValue(isvalid);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getIsValid(String paramString) {
/* 254 */     return (String)getValue(isvalid, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getIsBill() {
/* 262 */     return (String)getRowValue(isbill);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getIsBill(String paramString) {
/*     */     try {
/* 275 */       return (new WorkflowAllComInfo()).getIsBill(paramString);
/* 276 */     } catch (Exception exception) {
/* 277 */       return "";
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getIsWorkflowDoc() {
/* 286 */     return (String)getRowValue(isWorkflowDoc);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getOfficalType() {
/* 294 */     return (String)getRowValue(officalType);
/*     */   }
/*     */   
/*     */   public String getTraceSaveSecId() {
/* 298 */     return (String)getRowValue(traceSaveSecId);
/*     */   }
/*     */   
/*     */   public String getTraceFieldId() {
/* 302 */     return (String)getRowValue(traceFieldId);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getFormId() {
/* 310 */     return (String)getRowValue(formid);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getFormId(String paramString) {
/*     */     try {
/* 323 */       return (new WorkflowAllComInfo()).getFormId(paramString);
/* 324 */     } catch (Exception exception) {
/* 325 */       return "";
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getIsAnnexUpload() {
/* 334 */     return (String)getRowValue(isannexUpload);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getIsAnnexUpload(String paramString) {
/* 343 */     return (String)getValue(isannexUpload, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getIsForwardReceiveDef() {
/* 351 */     return (String)getRowValue(forwardReceiveDef);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getIsForwardReceiveDef(String paramString) {
/* 360 */     return (String)getValue(forwardReceiveDef, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getIsModifyLog() {
/* 368 */     return (String)getRowValue(isModifyLog);
/*     */   }
/*     */   
/*     */   public String getIsModifyLog(String paramString) {
/* 372 */     return (String)getValue(isModifyLog, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getHelpdocid() {
/* 380 */     return (String)getRowValue(helpdocid);
/*     */   }
/*     */   
/*     */   public String getHelpdocid(String paramString) {
/* 384 */     return (String)getValue(helpdocid, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getIsshared() {
/* 392 */     return (String)getRowValue(isshared);
/*     */   }
/*     */   
/*     */   public String getIsshared(String paramString) {
/* 396 */     return (String)getValue(isshared, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCustompage() {
/* 404 */     return (String)getRowValue(custompage);
/*     */   }
/*     */   
/*     */   public String getCustompage(String paramString) {
/* 408 */     return (String)getValue(custompage, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getIsFree() {
/* 416 */     return (String)getRowValue(isFree);
/*     */   }
/*     */   
/*     */   public String getIsFree(String paramString) {
/* 420 */     return (String)getValue(isFree, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getIsshowchart() {
/* 428 */     return (String)getRowValue(isshowchart);
/*     */   }
/*     */   
/*     */   public String getIsshowchart(String paramString) {
/* 432 */     return (String)getValue(isshowchart, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getIsWorkflowDoc(String paramString) {
/* 442 */     return getIsWorkflowDoc(paramString, -1);
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public String getIsWorkflowDoc(String paramString, int paramInt) {
/* 448 */     String str = (String)getValue(isWorkflowDoc, paramString);
/* 449 */     if ("1".equals(str)) {
/* 450 */       RecordSet recordSet = new RecordSet();
/*     */ 
/*     */ 
/*     */       
/* 454 */       if (this.workflowCreatedocMap == null) {
/* 455 */         this.workflowCreatedocMap = new HashMap<>();
/* 456 */         recordSet.executeQuery("select wfstatus,flowDocField,workflowid from workflow_createdoc ", new Object[0]);
/* 457 */         while (recordSet.next()) {
/* 458 */           this.workflowCreatedocMap.put(recordSet.getString("workflowid"), recordSet.getString("wfstatus") + "," + recordSet.getString("flowDocField"));
/*     */         }
/*     */       } 
/*     */       
/* 462 */       if (this.workflowCreatedocMap.containsKey(paramString)) {
/* 463 */         String str1 = this.workflowCreatedocMap.get(paramString);
/* 464 */         List<String> list = Util.splitString2List(str1, ",");
/* 465 */         if (list.size() < 2) return ""; 
/* 466 */         int i = Util.getIntValue(list.get(0), 0);
/* 467 */         int j = Util.getIntValue(list.get(1), -1);
/* 468 */         if (paramInt == 2) {
/* 469 */           return (i == 1) ? str : "";
/*     */         }
/* 471 */         return (i == 1 && j > -1) ? str : "";
/*     */       } 
/*     */     } 
/*     */     
/* 475 */     return "";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getOfficalType(String paramString) {
/* 483 */     return (String)getValue(officalType, paramString);
/*     */   }
/*     */   
/*     */   public String getTraceSaveSecId(String paramString) {
/* 487 */     return (String)getValue(traceSaveSecId, paramString);
/*     */   }
/*     */   
/*     */   public String getTraceFieldId(String paramString) {
/* 491 */     return (String)getValue(traceFieldId, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getMultiSubmit(String paramString) {
/* 500 */     String str = (String)getValue(multiSubmit, paramString);
/* 501 */     return (str == null || "".equals(str.trim())) ? "0" : str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void removeWorkflowCache() {
/* 508 */     removeCache();
/*     */   }
/*     */ 
/*     */   
/*     */   public CacheItem initCache(String paramString) {
/* 513 */     return super.initCache(paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public String getWFTreeXMLByType(String paramString1, String paramString2) throws Exception {
/* 522 */     return "";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public String getWFTreeXMLByType(String paramString1, String paramString2, String paramString3, int paramInt) throws Exception {
/* 531 */     return "";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getDateDuringForFirst() {
/* 539 */     BaseBean baseBean = new BaseBean();
/* 540 */     int i = 0;
/* 541 */     String str = "";
/*     */     try {
/* 543 */       str = Util.null2String(baseBean.getPropValue("wfdateduring", "wfdateduring"));
/* 544 */     } catch (Exception exception) {}
/*     */     
/* 546 */     String[] arrayOfString = Util.TokenizerString2(str, ",");
/* 547 */     if (arrayOfString.length > 0) {
/* 548 */       i = Util.getIntValue(arrayOfString[0], 0);
/*     */     }
/* 550 */     if (i < 0 || i > 36) {
/* 551 */       i = 0;
/*     */     }
/* 553 */     return i;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getDateDuringSql(int paramInt) {
/* 562 */     String str = "";
/* 563 */     Calendar calendar = Calendar.getInstance();
/*     */ 
/*     */ 
/*     */     
/* 567 */     int i = calendar.get(1);
/* 568 */     int j = calendar.get(2);
/* 569 */     int k = calendar.get(5);
/* 570 */     if (paramInt > 0 && paramInt < 37) {
/* 571 */       Calendar calendar1 = Calendar.getInstance();
/* 572 */       calendar1.clear();
/* 573 */       calendar1.set(i, j, k - 30 * paramInt);
/*     */       
/* 575 */       String str1 = Util.add0(calendar1.get(1), 4) + "-" + Util.add0(calendar1.get(2) + 1, 2) + "-" + Util.add0(calendar1.get(5), 2);
/* 576 */       RecordSet recordSet = new RecordSet();
/* 577 */       String str2 = Util.null2String(getPropValue("weaver_timezone_conversion", "timeZoneConversion")).trim();
/* 578 */       if ("1".equals(str2)) {
/* 579 */         if (recordSet.getDBType().equals("oracle")) {
/* 580 */           str = str + " and receivedate||' '||receivetime>'" + str1 + " 00:00:00' ";
/* 581 */         } else if (recordSet.getDBType().equals("mysql")) {
/* 582 */           str = str + " and concat(receivedate,' ',receivetime)>'" + str1 + " 00:00:00' ";
/* 583 */         } else if (recordSet.getDBType().equals("sqlserver")) {
/* 584 */           str = str + " and receivedate+' '+receivetime>'" + str1 + " 00:00:00' ";
/*     */         }
/* 586 */         else if (recordSet.getDBType().equals("postgresql")) {
/* 587 */           str = str + " and receivedate||' '||receivetime>'" + str1 + " 00:00:00' ";
/*     */         } 
/*     */       } else {
/* 590 */         str = str + " and receivedate>'" + str1 + "' ";
/*     */       } 
/*     */     } 
/* 593 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getDocCategory(String paramString) {
/* 604 */     if (paramString == null || "".equals(paramString)) {
/* 605 */       return null;
/*     */     }
/*     */     
/* 608 */     String str1 = "select docCategory,catelogType,SelectedCateLog from workflow_base where  isvalid in('1','3') and id = " + paramString;
/* 609 */     RecordSet recordSet = new RecordSet();
/* 610 */     log.info("Following is the run SQL : \n" + str1);
/* 611 */     recordSet.executeSql(str1);
/* 612 */     String str2 = null;
/* 613 */     String str3 = null;
/* 614 */     String str4 = null;
/* 615 */     if (recordSet.next()) {
/* 616 */       str2 = Util.null2String(recordSet.getString("docCategory"));
/* 617 */       str3 = Util.null2String(recordSet.getString("catelogType"));
/* 618 */       str4 = Util.null2String(recordSet.getString("SelectedCateLog"));
/*     */     } 
/*     */     
/* 621 */     log.info("'docCategory' Value === " + str2);
/* 622 */     log.info("'catelogType' Value === " + str3);
/* 623 */     log.info("'SelectedCateLog' Value === " + str4);
/*     */     
/* 625 */     String str5 = null;
/*     */     
/* 627 */     if ("1".equals(str3)) {
/*     */       
/* 629 */       if (!"".equals(str4)) {
/* 630 */         str5 = "field" + str4;
/*     */       } else {
/* 632 */         str5 = "";
/*     */       }
/*     */     
/* 635 */     } else if ("0".equals(str3)) {
/*     */       
/* 637 */       if (!"".equals(str2) && !",,".equals(str2)) {
/* 638 */         str5 = str2;
/*     */       } else {
/* 640 */         str5 = "";
/*     */       } 
/*     */     } else {
/* 643 */       str5 = "";
/*     */     } 
/* 645 */     log.info("Following is the result value:\t" + str5);
/* 646 */     return str5;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static int[] getAnnexDocCategory(String paramString) {
/* 657 */     if (paramString == null || "".equals(paramString)) {
/* 658 */       return null;
/*     */     }
/*     */     
/* 661 */     String str1 = "select annexdoccategory from workflow_base where isvalid in('1','3') and id = " + paramString;
/* 662 */     RecordSet recordSet = new RecordSet();
/* 663 */     log.info("Following is the run SQL : \n" + str1);
/* 664 */     String str2 = null;
/* 665 */     recordSet.executeSql(str1);
/* 666 */     if (recordSet.next()) {
/* 667 */       str2 = Util.null2String(recordSet.getString("annexdoccategory"));
/*     */     }
/*     */     
/* 670 */     if (str2 != null) {
/* 671 */       str2 = str2.trim();
/*     */     } else {
/* 673 */       return null;
/*     */     } 
/* 675 */     log.info("'annexDocCategory' Value === " + str2);
/*     */ 
/*     */     
/* 678 */     if ("".equals(str2) || ",,".equals(str2)) {
/* 679 */       log.info("Return the null array.");
/* 680 */       return null;
/*     */     } 
/*     */     
/* 683 */     int[] arrayOfInt = null;
/* 684 */     String[] arrayOfString = str2.split(",");
/* 685 */     arrayOfInt = new int[arrayOfString.length];
/* 686 */     for (byte b = 0; b < arrayOfString.length; b++) {
/* 687 */       arrayOfInt[b] = Util.getIntValue(arrayOfString[b]);
/*     */     }
/* 689 */     log.info("Return Non-null array.");
/* 690 */     return arrayOfInt;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/workflow/WorkflowComInfo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */