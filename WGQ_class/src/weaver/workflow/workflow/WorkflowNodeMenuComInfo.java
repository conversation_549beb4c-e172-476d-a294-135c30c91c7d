/*    */ package weaver.workflow.workflow;
/*    */ 
/*    */ import org.apache.commons.logging.Log;
/*    */ import org.apache.commons.logging.LogFactory;
/*    */ import weaver.cache.CacheBase;
/*    */ import weaver.cache.CacheColumn;
/*    */ import weaver.cache.CacheColumnType;
/*    */ import weaver.cache.PKColumn;
/*    */ 
/*    */ 
/*    */ 
/*    */ public class WorkflowNodeMenuComInfo
/*    */   extends CacheBase
/*    */ {
/* 15 */   private static final Log log = LogFactory.getLog(WorkflowNodeMenuComInfo.class);
/*    */ 
/*    */ 
/*    */   
/* 19 */   protected static String TABLE_NAME = "workflow_nodecustomrcmenu";
/*    */   
/* 21 */   protected static String TABLE_WHERE = " 1= 1 ";
/*    */   
/* 23 */   protected static String TABLE_ORDER = "id desc,nodeid asc";
/*    */   
/*    */   @PKColumn(type = CacheColumnType.NUMBER)
/* 26 */   protected static String PK_NAME = "nodeid";
/*    */   
/*    */   @CacheColumn
/*    */   protected static int hasOvertime;
/*    */ 
/*    */   
/*    */   public String getHasovertime(String paramString) {
/* 33 */     return (String)getValue(hasOvertime, paramString);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/workflow/WorkflowNodeMenuComInfo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */