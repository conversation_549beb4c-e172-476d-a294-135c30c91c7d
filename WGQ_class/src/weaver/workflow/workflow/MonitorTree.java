/*     */ package weaver.workflow.workflow;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.List;
/*     */ import weaver.common.util.taglib.TreeNode;
/*     */ import weaver.common.util.xtree.TreeNode;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class MonitorTree
/*     */ {
/*     */   public List getMonitorTree(User paramUser, int paramInt) {
/*  31 */     ArrayList<TreeNode> arrayList = new ArrayList();
/*  32 */     RecordSet recordSet = new RecordSet();
/*  33 */     int i = paramUser.getUID();
/*  34 */     String str = "select id,typename from Workflow_MonitorType order by typeorder";
/*  35 */     if (paramInt == 1) {
/*  36 */       TreeNode treeNode1 = new TreeNode();
/*  37 */       treeNode1.setIcon("custom1");
/*  38 */       treeNode1.setLevel(new Integer(0));
/*  39 */       treeNode1.setLinktype("js");
/*  40 */       treeNode1.setLink("setGroup(1)");
/*  41 */       treeNode1.setExpand("false");
/*  42 */       treeNode1.setTitle("<font color= '#330000'>" + SystemEnv.getHtmlLabelName(18334, paramUser.getLanguage()) + "</font>");
/*  43 */       arrayList.add(treeNode1);
/*  44 */       recordSet.executeSql(str);
/*  45 */       while (recordSet.next()) {
/*  46 */         String str1 = recordSet.getString("typename");
/*  47 */         int j = recordSet.getInt("id");
/*  48 */         TreeNode treeNode2 = new TreeNode();
/*  49 */         treeNode2.setIcon("custom2");
/*  50 */         treeNode2.setTitle("<font color= '#330000'>" + str1 + "</font>");
/*  51 */         treeNode2.setLevel(new Integer(1));
/*  52 */         treeNode2.setLinktype("js");
/*  53 */         treeNode2.setLink("setMonitorType(1," + j + ")");
/*  54 */         arrayList.add(treeNode2);
/*     */       } 
/*     */     } 
/*     */ 
/*     */     
/*  59 */     TreeNode treeNode = new TreeNode();
/*  60 */     treeNode.setIcon("custom1");
/*  61 */     treeNode.setLevel(new Integer(0));
/*  62 */     treeNode.setLinktype("js");
/*  63 */     treeNode.setLink("setGroup(0)");
/*  64 */     treeNode.setExpand("false");
/*  65 */     treeNode.setTitle("<font color= '#330000'>" + SystemEnv.getHtmlLabelName(2118, paramUser.getLanguage()) + "</font>");
/*  66 */     arrayList.add(treeNode);
/*  67 */     recordSet.executeSql(str);
/*  68 */     while (recordSet.next()) {
/*  69 */       String str1 = recordSet.getString("typename");
/*  70 */       int j = recordSet.getInt("id");
/*  71 */       TreeNode treeNode1 = new TreeNode();
/*  72 */       treeNode1.setIcon("custom2");
/*  73 */       treeNode1.setTitle("<font color= '#330000'>" + str1 + "</font>");
/*  74 */       treeNode1.setLevel(new Integer(1));
/*  75 */       treeNode1.setLinktype("js");
/*  76 */       treeNode1.setLink("setMonitorType(0," + j + ")");
/*  77 */       arrayList.add(treeNode1);
/*     */     } 
/*     */     
/*  80 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public TreeNode getMonitorTypeTreeList(TreeNode paramTreeNode, String paramString1, String paramString2, String paramString3, String paramString4, int paramInt) throws Exception {
/*  94 */     RecordSet recordSet = new RecordSet();
/*  95 */     recordSet.executeSql("select id,typename from Workflow_MonitorType order by typeorder");
/*  96 */     while (recordSet.next()) {
/*  97 */       String str1 = Util.null2String(recordSet.getString("id"));
/*  98 */       if (str1.equals(""))
/*  99 */         str1 = "0"; 
/* 100 */       String str2 = Util.null2String(recordSet.getString("typename"));
/* 101 */       TreeNode treeNode = new TreeNode();
/* 102 */       treeNode.setTitle(str2);
/* 103 */       treeNode.setNodeId("workflowtype_" + str1);
/* 104 */       treeNode.setIcon("/LeftMenu/ThemeXP/folder2_wev8.gif");
/* 105 */       treeNode.setHref("/system/systemmonitor/workflow/systemMonitorStaticTab.jsp?isTemplate=" + paramString3 + "&typeid=" + str1 + "&subcompanyid=" + paramString2 + "&typename=" + str2);
/* 106 */       treeNode.setTarget("wfmainFrame");
/* 107 */       treeNode.setOncheck("check(" + treeNode.getNodeId() + ")");
/* 108 */       paramTreeNode.addTreeNode(treeNode);
/*     */     } 
/* 110 */     return paramTreeNode;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/workflow/MonitorTree.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */