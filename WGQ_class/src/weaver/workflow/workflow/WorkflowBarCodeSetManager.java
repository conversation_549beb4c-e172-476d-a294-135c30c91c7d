/*     */ package weaver.workflow.workflow;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.workflow.request.RequestDoc;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class WorkflowBarCodeSetManager
/*     */   extends BaseBean
/*     */ {
/*     */   public static final String separator = "^";
/*     */   public static final String tailed = "|";
/*     */   public static final int dataElementNum = 14;
/*     */   private WorkflowComInfo workflowComInfo;
/*     */   
/*     */   public WorkflowBarCodeSetManager() {
/*     */     try {
/*  41 */       this.workflowComInfo = new WorkflowComInfo();
/*  42 */     } catch (Exception exception) {
/*  43 */       exception.printStackTrace();
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getLabelNameByDataElementId(int paramInt1, int paramInt2) {
/*  53 */     String str = "";
/*     */     
/*  55 */     if (paramInt1 == 1) {
/*  56 */       str = SystemEnv.getHtmlLabelName(21458, paramInt2);
/*  57 */     } else if (paramInt1 == 2) {
/*  58 */       str = SystemEnv.getHtmlLabelName(21459, paramInt2);
/*  59 */     } else if (paramInt1 == 3) {
/*  60 */       str = SystemEnv.getHtmlLabelName(20216, paramInt2);
/*  61 */     } else if (paramInt1 == 4) {
/*  62 */       str = SystemEnv.getHtmlLabelName(21460, paramInt2);
/*  63 */     } else if (paramInt1 == 5) {
/*  64 */       str = SystemEnv.getHtmlLabelName(21461, paramInt2);
/*  65 */     } else if (paramInt1 == 6) {
/*  66 */       str = SystemEnv.getHtmlLabelName(21462, paramInt2);
/*  67 */     } else if (paramInt1 == 7) {
/*  68 */       str = SystemEnv.getHtmlLabelName(229, paramInt2);
/*  69 */     } else if (paramInt1 == 8) {
/*  70 */       str = SystemEnv.getHtmlLabelName(16972, paramInt2);
/*  71 */     } else if (paramInt1 == 9) {
/*  72 */       str = SystemEnv.getHtmlLabelName(15534, paramInt2);
/*  73 */     } else if (paramInt1 == 10) {
/*  74 */       str = SystemEnv.getHtmlLabelName(16983, paramInt2);
/*  75 */     } else if (paramInt1 == 11) {
/*  76 */       str = SystemEnv.getHtmlLabelName(21463, paramInt2);
/*  77 */     } else if (paramInt1 == 12) {
/*  78 */       str = SystemEnv.getHtmlLabelName(21464, paramInt2);
/*  79 */     } else if (paramInt1 == 13) {
/*  80 */       str = SystemEnv.getHtmlLabelName(21465, paramInt2);
/*  81 */     } else if (paramInt1 == 14) {
/*  82 */       str = SystemEnv.getHtmlLabelName(17037, paramInt2);
/*     */     } 
/*     */     
/*  85 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getPDF417TextValue(int paramInt1, int paramInt2, int paramInt3) {
/*  94 */     String str1 = "";
/*     */ 
/*     */     
/*  97 */     if (paramInt2 <= 0) {
/*  98 */       return str1;
/*     */     }
/*     */     
/* 101 */     if (paramInt3 <= 0) {
/* 102 */       paramInt3 = 7;
/*     */     }
/* 104 */     RecordSet recordSet = new RecordSet();
/* 105 */     int i = 0;
/* 106 */     String str2 = "";
/*     */ 
/*     */     
/* 109 */     recordSet.executeSql("select workflowId,requestName from workflow_requestbase where requestId=" + paramInt1);
/* 110 */     if (recordSet.next()) {
/* 111 */       i = Util.getIntValue(recordSet.getString("workflowId"), -1);
/* 112 */       str2 = Util.null2String(recordSet.getString("requestName"));
/*     */     } 
/*     */     
/* 115 */     int j = Util.getIntValue(this.workflowComInfo.getFormId("" + i), 0);
/* 116 */     String str3 = Util.null2String(this.workflowComInfo.getIsBill("" + i));
/* 117 */     if (!str3.equals("1")) {
/* 118 */       str3 = "0";
/*     */     }
/*     */     
/* 121 */     String str4 = "workflow_form";
/* 122 */     Object object = null;
/* 123 */     if (str3.equals("1")) {
/* 124 */       recordSet.executeSql("select tablename from workflow_bill where id = " + j);
/* 125 */       if (recordSet.next()) {
/* 126 */         str4 = Util.null2String(recordSet.getString("tablename"));
/*     */       }
/*     */     } 
/*     */     
/* 130 */     HashMap<Object, Object> hashMap1 = new HashMap<Object, Object>();
/* 131 */     int k = 0;
/* 132 */     int m = 0;
/*     */ 
/*     */     
/* 135 */     String str5 = "";
/*     */ 
/*     */     
/* 138 */     recordSet.executeSql("select * from Workflow_BarCodeSetDetail where barCodeSetId=" + paramInt2);
/* 139 */     while (recordSet.next()) {
/* 140 */       k = Util.getIntValue(recordSet.getString("dataElementId"), 0);
/* 141 */       m = Util.getIntValue(recordSet.getString("fieldId"), 0);
/*     */       
/* 143 */       hashMap1.put("" + k, "" + m);
/*     */       
/* 145 */       str5 = str5 + m + ",";
/*     */     } 
/* 147 */     str5 = str5 + "-100";
/*     */     
/* 149 */     ArrayList<String> arrayList1 = new ArrayList();
/* 150 */     ArrayList<String> arrayList2 = new ArrayList();
/* 151 */     ArrayList<String> arrayList3 = new ArrayList();
/* 152 */     ArrayList<String> arrayList4 = new ArrayList();
/*     */     
/* 154 */     String str6 = "";
/* 155 */     String str7 = null;
/*     */     
/* 157 */     if (str3.equals("1")) {
/* 158 */       recordSet.execute("select a.fieldName,a.fieldHtmlType,a.type, a.id from workflow_billfield a where a.id in (" + str5 + ") order by id");
/*     */     } else {
/* 160 */       recordSet.execute("select a.fieldName,a.fieldHtmlType,a.type, a.id from workflow_formdict a where a.id in (" + str5 + ") order by id");
/*     */     } 
/*     */     
/* 163 */     while (recordSet.next()) {
/* 164 */       arrayList1.add(Util.null2String(recordSet.getString("id")));
/* 165 */       str7 = Util.null2String(recordSet.getString("fieldName"));
/* 166 */       arrayList2.add(str7);
/* 167 */       arrayList3.add(Util.null2String(recordSet.getString("fieldHtmlType")));
/* 168 */       arrayList4.add(Util.null2String(recordSet.getString("type")));
/*     */       
/* 170 */       if (str6.equals("")) {
/* 171 */         str6 = str7; continue;
/*     */       } 
/* 173 */       str6 = str6 + "," + str7;
/*     */     } 
/*     */ 
/*     */     
/* 177 */     HashMap<Object, Object> hashMap2 = new HashMap<Object, Object>();
/* 178 */     hashMap2.put("-3", str2);
/* 179 */     String str8 = null;
/* 180 */     String str9 = null;
/* 181 */     String str10 = null;
/*     */     
/* 183 */     String str11 = null;
/* 184 */     if (!str6.equals("")) {
/* 185 */       recordSet.execute("select " + str6 + " from " + str4 + " where requestid=" + paramInt1);
/* 186 */       if (recordSet.next()) {
/* 187 */         RequestDoc requestDoc = null;
/*     */         try {
/* 189 */           requestDoc = new RequestDoc();
/* 190 */         } catch (Exception exception) {}
/*     */         
/* 192 */         for (byte b1 = 0; b1 < arrayList1.size(); b1++) {
/*     */           
/* 194 */           str8 = arrayList3.get(b1);
/* 195 */           str9 = arrayList4.get(b1);
/* 196 */           str10 = arrayList1.get(b1);
/*     */           
/* 198 */           str11 = recordSet.getString(b1 + 1);
/*     */           
/* 200 */           if (str8.equals("3") || str8.equals("6") || str8.equals("5")) {
/*     */             try {
/* 202 */               str11 = requestDoc.getFieldValue(str8, str9, str11, paramInt3, str10);
/* 203 */             } catch (Exception exception) {
/* 204 */               str11 = "";
/*     */             } 
/*     */           }
/*     */           
/* 208 */           hashMap2.put(str10, str11);
/*     */         } 
/*     */       } 
/*     */     } 
/*     */     
/* 213 */     String str12 = null;
/* 214 */     for (byte b = 1; b <= 14; b++) {
/* 215 */       str12 = "";
/* 216 */       k = b;
/*     */       
/* 218 */       m = Util.getIntValue((String)hashMap1.get("" + k), -1);
/* 219 */       str12 = Util.null2String((String)hashMap2.get("" + m));
/* 220 */       str12 = fromHtmlToBarCode(str12);
/*     */ 
/*     */       
/* 223 */       if (k == 1) {
/*     */         
/* 225 */         if (str12.trim().equals("")) {
/* 226 */           str12 = "GB0626-2005";
/*     */         }
/* 228 */       } else if (k == 5) {
/*     */         
/* 230 */         if (str12.trim().equals("")) {
/* 231 */           str12 = SystemEnv.getHtmlLabelName(557, paramInt3);
/*     */         }
/* 233 */       } else if (k == 10) {
/*     */         
/* 235 */         str12 = Util.StringReplace(str12, "-", "");
/* 236 */       } else if (k == 13) {
/*     */         
/* 238 */         str12 = Util.StringReplace(str12, "-", "");
/*     */       } 
/*     */       
/* 241 */       str1 = str1 + str12 + "^";
/*     */     } 
/*     */     
/* 244 */     str1 = str1 + "|";
/*     */     
/* 246 */     writeLog("requestId=" + paramInt1 + "#PDF417TextValue=" + str1);
/* 247 */     return str1;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   private static String fromHtmlToBarCode(String paramString) {
/* 253 */     if (paramString == null) {
/* 254 */       return "";
/*     */     }
/* 256 */     paramString = toScreen(paramString);
/*     */     
/* 258 */     paramString = Util.StringReplace(paramString, "<br>", "");
/* 259 */     paramString = Util.StringReplace(paramString, "&lt;", "<");
/* 260 */     paramString = Util.StringReplace(paramString, "&gt;", ">");
/* 261 */     paramString = Util.StringReplace(paramString, "\"", "\\\"");
/* 262 */     paramString = Util.StringReplace(paramString, "&quot;", "\\\"");
/* 263 */     paramString = Util.StringReplace(paramString, "&nbsp;", " ");
/*     */     
/* 265 */     return paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static String toScreen(String paramString) {
/* 274 */     char[] arrayOfChar = paramString.toCharArray();
/*     */     
/* 276 */     byte b = 0;
/* 277 */     StringBuffer stringBuffer = new StringBuffer();
/*     */     
/* 279 */     while (b < arrayOfChar.length) {
/* 280 */       char c = arrayOfChar[b++];
/*     */       
/* 282 */       if (c == '\r') {
/* 283 */         stringBuffer.append(""); continue;
/* 284 */       }  if (c == '\n') {
/* 285 */         stringBuffer.append(""); continue;
/*     */       } 
/* 287 */       stringBuffer.append(c);
/*     */     } 
/* 289 */     return stringBuffer.toString();
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/workflow/WorkflowBarCodeSetManager.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */