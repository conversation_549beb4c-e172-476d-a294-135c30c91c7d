/*     */ package weaver.workflow.workflow;
/*     */ 
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.company.DepartmentComInfo;
/*     */ import weaver.hrm.company.SubCompanyComInfo;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class WorkflowManage
/*     */   extends BaseBean
/*     */ {
/*     */   public User user;
/*     */   public String workflowid;
/*     */   public ResourceComInfo rcinfo;
/*     */   public SubCompanyComInfo subinfo;
/*     */   public DepartmentComInfo deptinfo;
/*     */   public Map map;
/*     */   
/*     */   public WorkflowManage() {
/*     */     try {
/*  34 */       this.rcinfo = new ResourceComInfo();
/*  35 */       this.subinfo = new SubCompanyComInfo();
/*  36 */       this.deptinfo = new DepartmentComInfo();
/*  37 */     } catch (Exception exception) {
/*  38 */       exception.printStackTrace();
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean isSubmit() {
/*  49 */     boolean bool = true;
/*     */     
/*  51 */     try { this.map = new HashMap<Object, Object>();
/*  52 */       RecordSet recordSet = new RecordSet();
/*  53 */       TimeUtilWeiw timeUtilWeiw = new TimeUtilWeiw();
/*  54 */       WFManager wFManager = new WFManager();
/*  55 */       wFManager.setWfid(Util.getIntValue(this.workflowid, 0));
/*  56 */       wFManager.getWfInfo();
/*  57 */       String str1 = wFManager.getSpecialApproval();
/*  58 */       int i = Util.getIntValue(wFManager.getFrequency(), 0);
/*  59 */       String str2 = wFManager.getCycle();
/*     */       
/*  61 */       String str3 = "";
/*  62 */       String str4 = "";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 102 */       return bool; } catch (Exception exception) { return bool; } finally { Exception exception = null; }
/*     */   
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getWorkflowCounts(String paramString1, String paramString2, int paramInt) {
/* 114 */     String str1 = this.rcinfo.getDepartmentID(this.user.getUID() + "");
/* 115 */     RecordSet recordSet = new RecordSet();
/* 116 */     int i = 0;
/* 117 */     String str2 = "0";
/* 118 */     if (!str1.equals("")) {
/* 119 */       recordSet.executeSql("select id from hrmresource where status in(0,1,2,3) and departmentid=" + str1);
/* 120 */       while (recordSet.next()) {
/* 121 */         str2 = str2 + "," + recordSet.getInt(1);
/*     */       }
/*     */     } else {
/* 124 */       str2 = "" + paramInt;
/*     */     } 
/* 126 */     StringBuffer stringBuffer = new StringBuffer();
/* 127 */     stringBuffer.append("select count(requestid) from workflow_requestbase ").append(" where workflowid = ").append(this.workflowid).append(" and deleted=0 ").append(" and createdate >='").append(paramString1).append("' ").append(" and createdate <='").append(paramString2).append("' ");
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 132 */     if (!str2.equals("0")) {
/* 133 */       stringBuffer.append(" and creater in (").append(str2).append(")");
/*     */     }
/* 135 */     recordSet.executeSql(stringBuffer.toString());
/* 136 */     if (recordSet.next())
/* 137 */       i = recordSet.getInt(1); 
/* 138 */     return i;
/*     */   }
/*     */   
/*     */   public User getUser() {
/* 142 */     return this.user;
/*     */   }
/*     */   
/*     */   public void setUser(User paramUser) {
/* 146 */     this.user = paramUser;
/*     */   }
/*     */   
/*     */   public String getWorkflowid() {
/* 150 */     return this.workflowid;
/*     */   }
/*     */   
/*     */   public void setWorkflowid(String paramString) {
/* 154 */     this.workflowid = paramString;
/*     */   }
/*     */   
/*     */   public Map getMap() {
/* 158 */     return this.map;
/*     */   }
/*     */   
/*     */   public void setMap(Map paramMap) {
/* 162 */     this.map = paramMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/workflow/WorkflowManage.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */