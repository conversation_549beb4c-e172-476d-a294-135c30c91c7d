/*     */ package weaver.workflow.workflow;
/*     */ 
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.workflow.request.wfAgentCondition;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class WFAgentManager
/*     */   extends BaseBean
/*     */ {
/*     */   private int agentId;
/*     */   private int workflowId;
/*     */   private int beagenterId;
/*     */   private int agenterId;
/*     */   private String beginDate;
/*     */   private String beginTime;
/*     */   private String endDate;
/*     */   private String endTime;
/*     */   private int isCreateAgenter;
/*  39 */   private String Procpara = "";
/*  40 */   private char flag = Util.getSeparator();
/*     */   private boolean executesuccess = true;
/*     */   
/*     */   public WFAgentManager() {
/*  44 */     this.beginDate = "";
/*  45 */     this.beginTime = "";
/*  46 */     this.endDate = "";
/*  47 */     this.endTime = "";
/*  48 */     this.isCreateAgenter = 0;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean saveAgentInfo() {
/*  55 */     wfAgentCondition wfAgentCondition = new wfAgentCondition();
/*  56 */     RecordSet recordSet = new RecordSet();
/*  57 */     recordSet.executeProc("sequenceindex_selectnextid", "workflowagentid");
/*  58 */     if (recordSet.next()) {
/*  59 */       this.agentId = Util.getIntValue(recordSet.getString(1), 0);
/*     */     }
/*  61 */     this.Procpara = "" + this.agentId + this.flag + this.workflowId + this.flag + this.beagenterId + this.flag + this.agenterId + this.flag + this.beginDate + this.flag + this.beginTime + this.flag + this.endDate + this.flag + this.endTime + this.flag + this.isCreateAgenter;
/*     */ 
/*     */     
/*  64 */     this.executesuccess = recordSet.executeProc("Workflow_Agent_Insert", this.Procpara);
/*  65 */     if (!this.executesuccess) {
/*  66 */       return false;
/*     */     }
/*  68 */     wfAgentCondition.insertAgentConditionSet("" + this.agenterId, "" + this.isCreateAgenter, "", "", "", "", "" + this.beagenterId, this.beginDate, this.beginTime, this.endDate, this.endTime, "0.00", "" + this.agentId, "", "" + this.workflowId, "", "", "", "", "", "", "", "");
/*     */     
/*  70 */     return true;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean delAIFbyWfid() {
/*  77 */     RecordSet recordSet = new RecordSet();
/*  78 */     String str = "delete from Workflow_Agent where workflowid=" + this.workflowId;
/*     */     
/*  80 */     this.executesuccess = recordSet.executeSql(str);
/*  81 */     if (!this.executesuccess) {
/*  82 */       return false;
/*     */     }
/*  84 */     recordSet.executeSql("delete workflow_agentConditionSet where workflowid=" + this.workflowId);
/*     */     
/*  86 */     return true;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean delAIFbyWfidBeagenterid() {
/*  93 */     RecordSet recordSet = new RecordSet();
/*  94 */     String str = "delete from Workflow_Agent where workflowid=" + this.workflowId + " and beagenterId=" + this.beagenterId;
/*  95 */     this.executesuccess = recordSet.executeSql(str);
/*  96 */     if (!this.executesuccess) return false;
/*     */     
/*  98 */     recordSet.executeSql("delete workflow_agentConditionSet where workflowid=" + this.workflowId + " and bagentuid=" + this.beagenterId);
/*  99 */     return true;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getAgenterId() {
/* 107 */     return this.agenterId;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setAgenterId(int paramInt) {
/* 114 */     this.agenterId = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getAgentId() {
/* 121 */     return this.agentId;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setAgentId(int paramInt) {
/* 128 */     this.agentId = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getBeagenterId() {
/* 135 */     return this.beagenterId;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setBeagenterId(int paramInt) {
/* 142 */     this.beagenterId = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getBeginDate() {
/* 149 */     return this.beginDate;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setBeginDate(String paramString) {
/* 156 */     this.beginDate = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getBeginTime() {
/* 163 */     return this.beginTime;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setBeginTime(String paramString) {
/* 170 */     this.beginTime = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getEndDate() {
/* 177 */     return this.endDate;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setEndDate(String paramString) {
/* 184 */     this.endDate = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getEndTime() {
/* 191 */     return this.endTime;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setEndTime(String paramString) {
/* 198 */     this.endTime = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getWorkflowId() {
/* 205 */     return this.workflowId;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setWorkflowId(int paramInt) {
/* 212 */     this.workflowId = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getIsCreateAgenter() {
/* 219 */     return this.isCreateAgenter;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setIsCreateAgenter(int paramInt) {
/* 226 */     this.isCreateAgenter = paramInt;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/workflow/WFAgentManager.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */