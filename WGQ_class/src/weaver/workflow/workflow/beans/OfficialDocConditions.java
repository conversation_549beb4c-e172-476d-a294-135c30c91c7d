/*     */ package weaver.workflow.workflow.beans;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class OfficialDocConditions
/*     */ {
/*  16 */   String workFlowID = "";
/*  17 */   String status = "";
/*  18 */   String workFlowCoding = "";
/*  19 */   String createDocument = "";
/*  20 */   String documentLocation = "";
/*  21 */   String mainCategoryDocument = "";
/*  22 */   String subCategoryDocument = "";
/*  23 */   String secCategoryDocument = "";
/*  24 */   String useTempletNode = "";
/*  25 */   String documentTitleField = "";
/*  26 */   String printNodes = "";
/*  27 */   String newTextNodes = "";
/*  28 */   String isCompellentMark = "";
/*  29 */   String isCancelCheck = "";
/*  30 */   String signatureNodes = "";
/*  31 */   String isWorkflowDraft = "";
/*  32 */   String defaultDocType = "";
/*  33 */   int extfile2doc = 0;
/*  34 */   String isHideTheTraces = "";
/*  35 */   String openTextDefaultNode = "";
/*  36 */   String saveTempFile = "";
/*  37 */   String cleanCopyNodes = "";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public OfficialDocConditions() {}
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public OfficialDocConditions(String paramString1, String paramString2, String paramString3, String paramString4, String paramString5, String paramString6, String paramString7, String paramString8, String paramString9, String paramString10, String paramString11, String paramString12, String paramString13, String paramString14, String paramString15, String paramString16, String paramString17, int paramInt, String paramString18, String paramString19) {
/*  79 */     this.workFlowID = paramString1;
/*  80 */     this.status = paramString2;
/*  81 */     this.workFlowCoding = paramString3;
/*  82 */     this.createDocument = paramString4;
/*  83 */     this.documentLocation = paramString5;
/*  84 */     this.mainCategoryDocument = paramString6;
/*  85 */     this.subCategoryDocument = paramString7;
/*  86 */     this.secCategoryDocument = paramString8;
/*  87 */     this.useTempletNode = paramString9;
/*  88 */     this.documentTitleField = paramString10;
/*  89 */     this.printNodes = paramString11;
/*  90 */     this.newTextNodes = paramString12;
/*  91 */     this.isCompellentMark = paramString13;
/*  92 */     this.isCancelCheck = paramString14;
/*  93 */     this.signatureNodes = paramString15;
/*  94 */     this.isWorkflowDraft = paramString16;
/*  95 */     this.defaultDocType = paramString17;
/*  96 */     this.extfile2doc = paramInt;
/*  97 */     this.isHideTheTraces = paramString18;
/*  98 */     this.cleanCopyNodes = paramString19;
/*     */   }
/*     */   
/*     */   public String getWorkFlowID() {
/* 102 */     return this.workFlowID;
/*     */   }
/*     */   public void setWorkFlowID(String paramString) {
/* 105 */     this.workFlowID = paramString;
/*     */   }
/*     */   public String getStatus() {
/* 108 */     return this.status;
/*     */   }
/*     */   public void setStatus(String paramString) {
/* 111 */     this.status = paramString;
/*     */   }
/*     */   public String getWorkFlowCoding() {
/* 114 */     return this.workFlowCoding;
/*     */   }
/*     */   public void setWorkFlowCoding(String paramString) {
/* 117 */     this.workFlowCoding = paramString;
/*     */   }
/*     */   public String getCreateDocument() {
/* 120 */     return this.createDocument;
/*     */   }
/*     */   public void setCreateDocument(String paramString) {
/* 123 */     this.createDocument = paramString;
/*     */   }
/*     */   public String getDocumentLocation() {
/* 126 */     return this.documentLocation;
/*     */   }
/*     */   public void setDocumentLocation(String paramString) {
/* 129 */     this.documentLocation = paramString;
/*     */   }
/*     */   public String getMainCategoryDocument() {
/* 132 */     return this.mainCategoryDocument;
/*     */   }
/*     */   public void setMainCategoryDocument(String paramString) {
/* 135 */     this.mainCategoryDocument = paramString;
/*     */   }
/*     */   public String getSubCategoryDocument() {
/* 138 */     return this.subCategoryDocument;
/*     */   }
/*     */   public void setSubCategoryDocument(String paramString) {
/* 141 */     this.subCategoryDocument = paramString;
/*     */   }
/*     */   public String getSecCategoryDocument() {
/* 144 */     return this.secCategoryDocument;
/*     */   }
/*     */   public void setSecCategoryDocument(String paramString) {
/* 147 */     this.secCategoryDocument = paramString;
/*     */   }
/*     */   public String getUseTempletNode() {
/* 150 */     return this.useTempletNode;
/*     */   }
/*     */   public void setUseTempletNode(String paramString) {
/* 153 */     this.useTempletNode = paramString;
/*     */   }
/*     */   public String getDocumentTitleField() {
/* 156 */     return this.documentTitleField;
/*     */   }
/*     */   public void setDocumentTitleField(String paramString) {
/* 159 */     this.documentTitleField = paramString;
/*     */   }
/*     */   public String getPrintNodes() {
/* 162 */     return this.printNodes;
/*     */   }
/*     */   public void setPrintNodes(String paramString) {
/* 165 */     this.printNodes = paramString;
/*     */   }
/*     */   public String getNewTextNodes() {
/* 168 */     return this.newTextNodes;
/*     */   }
/*     */   public void setNewTextNodes(String paramString) {
/* 171 */     this.newTextNodes = paramString;
/*     */   }
/*     */   public String getIsCompellentMark() {
/* 174 */     return this.isCompellentMark;
/*     */   }
/*     */   public void setIsCompellentMark(String paramString) {
/* 177 */     this.isCompellentMark = paramString;
/*     */   }
/*     */   public String getIsCancelCheck() {
/* 180 */     return this.isCancelCheck;
/*     */   }
/*     */   public void setIsCancelCheck(String paramString) {
/* 183 */     this.isCancelCheck = paramString;
/*     */   }
/*     */   public String getSignatureNodes() {
/* 186 */     return this.signatureNodes;
/*     */   }
/*     */   public void setSignatureNodes(String paramString) {
/* 189 */     this.signatureNodes = paramString;
/*     */   }
/*     */   public String getIsWorkflowDraft() {
/* 192 */     return this.isWorkflowDraft;
/*     */   }
/*     */   public void setIsWorkflowDraft(String paramString) {
/* 195 */     this.isWorkflowDraft = paramString;
/*     */   }
/*     */   public String getDefaultDocType() {
/* 198 */     return this.defaultDocType;
/*     */   }
/*     */   public void setDefaultDocType(String paramString) {
/* 201 */     this.defaultDocType = paramString;
/*     */   }
/*     */   public int getExtfile2doc() {
/* 204 */     return this.extfile2doc;
/*     */   }
/*     */   public void setExtfile2doc(int paramInt) {
/* 207 */     this.extfile2doc = paramInt;
/*     */   }
/*     */   public String getIsHideTheTraces() {
/* 210 */     return this.isHideTheTraces;
/*     */   }
/*     */   public void setIsHideTheTraces(String paramString) {
/* 213 */     this.isHideTheTraces = paramString;
/*     */   }
/*     */   public String getOpenTextDefaultNode() {
/* 216 */     return this.openTextDefaultNode;
/*     */   }
/*     */   public void setOpenTextDefaultNode(String paramString) {
/* 219 */     this.openTextDefaultNode = paramString;
/*     */   }
/*     */   public String getSaveTempFile() {
/* 222 */     return this.saveTempFile;
/*     */   }
/*     */   public void setSaveTempFile(String paramString) {
/* 225 */     this.saveTempFile = paramString;
/*     */   }
/*     */   public String getCleanCopyNodes() {
/* 228 */     return this.cleanCopyNodes;
/*     */   }
/*     */   public void setCleanCopyNodes(String paramString) {
/* 231 */     this.cleanCopyNodes = paramString;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/workflow/beans/OfficialDocConditions.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */