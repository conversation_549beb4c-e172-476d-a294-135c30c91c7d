/*     */ package weaver.workflow.workflow.beans.others;
/*     */ 
/*     */ import weaver.conn.RecordSet;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class InstitutionCodeInfo
/*     */ {
/*     */   private static final String EMPTY = "";
/*     */   public static final String FB = "fb";
/*     */   public static final String BM = "bm";
/*     */   public static final String ZB = "zb";
/*  19 */   private int institutionLevel = -1;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String headquartersId;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String parentInstitutionId;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String institutionId;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String institutionCode;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String institutionName;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String hasSon;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String institutionType;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public InstitutionCodeInfo() {}
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public InstitutionCodeInfo(String paramString1, String paramString2, String paramString3, String paramString4, String paramString5, String paramString6) {
/*  72 */     this.headquartersId = paramString1;
/*  73 */     this.parentInstitutionId = paramString2;
/*  74 */     this.institutionId = paramString3;
/*  75 */     this.institutionCode = paramString4;
/*  76 */     this.institutionName = paramString5;
/*  77 */     this.institutionType = paramString6;
/*  78 */     RecordSet recordSet = new RecordSet();
/*  79 */     if ("fb".equals(paramString6)) {
/*  80 */       recordSet.executeQuery("select id from HrmSubCompany WHERE supsubcomid = ? AND (canceled IS NULL OR canceled <> '1')", new Object[] { paramString3 });
/*  81 */       if (recordSet.next()) {
/*  82 */         setHasSon("1");
/*     */       } else {
/*  84 */         recordSet.executeQuery("select id from HrmDepartment WHERE subcompanyid1 = ? AND (canceled IS NULL OR canceled <> '1')", new Object[] { paramString3 });
/*  85 */         setHasSon(recordSet.next() ? "1" : "0");
/*     */       } 
/*  87 */     } else if ("bm".equals(paramString6)) {
/*  88 */       recordSet.executeQuery("select id from HrmDepartment WHERE supdepid = ? AND (canceled IS NULL OR canceled <> '1')", new Object[] { paramString3 });
/*  89 */       setHasSon(recordSet.next() ? "1" : "0");
/*  90 */     } else if ("zb".equals(paramString6)) {
/*  91 */       setHasSon("1");
/*     */     } 
/*     */   }
/*     */   
/*     */   public String getInstitutionId() {
/*  96 */     return this.institutionId;
/*     */   }
/*     */   
/*     */   public void setInstitutionId(String paramString) {
/* 100 */     this.institutionId = paramString;
/*     */   }
/*     */   
/*     */   public String getHasSon() {
/* 104 */     return this.hasSon;
/*     */   }
/*     */   
/*     */   public void setHasSon(String paramString) {
/* 108 */     this.hasSon = paramString;
/*     */   }
/*     */   
/*     */   public String getInstitutionType() {
/* 112 */     return this.institutionType;
/*     */   }
/*     */   
/*     */   public void setInstitutionType(String paramString) {
/* 116 */     this.institutionType = paramString;
/*     */   }
/*     */   
/*     */   public String getParentInstitutionId() {
/* 120 */     return this.parentInstitutionId;
/*     */   }
/*     */   
/*     */   public void setParentInstitutionId(String paramString) {
/* 124 */     this.parentInstitutionId = paramString;
/*     */   }
/*     */   
/*     */   public String getInstitutionCode() {
/* 128 */     return this.institutionCode;
/*     */   }
/*     */   
/*     */   public void setInstitutionCode(String paramString) {
/* 132 */     this.institutionCode = paramString;
/*     */   }
/*     */   
/*     */   public String getInstitutionName() {
/* 136 */     return this.institutionName;
/*     */   }
/*     */   
/*     */   public void setInstitutionName(String paramString) {
/* 140 */     this.institutionName = paramString;
/*     */   }
/*     */   
/*     */   public String getHeadquartersId() {
/* 144 */     return this.headquartersId;
/*     */   }
/*     */   
/*     */   public void setHeadquartersId(String paramString) {
/* 148 */     this.headquartersId = paramString;
/*     */   }
/*     */   
/*     */   public int getInstitutionLevel() {
/* 152 */     return this.institutionLevel;
/*     */   }
/*     */   
/*     */   public void setInstitutionLevel(int paramInt) {
/* 156 */     this.institutionLevel = paramInt;
/*     */   }
/*     */ 
/*     */   
/*     */   public String toString() {
/* 161 */     return "InstitutionCodeInfo [institutionLevel=" + this.institutionLevel + ", headquartersId=" + this.headquartersId + ", parentInstitutionId=" + this.parentInstitutionId + ", institutionId=" + this.institutionId + ", institutionCode=" + this.institutionCode + ", institutionName=" + this.institutionName + ", hasSon=" + this.hasSon + ", institutionType=" + this.institutionType + "]";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String toJSONString() {
/* 171 */     return "{institutionLevel:'" + this.institutionLevel + "',headquartersId:'" + this.headquartersId + "',parentInstitutionId:'" + this.parentInstitutionId + "',institutionId:'" + this.institutionId + "',institutionCode:'" + this.institutionCode + "',institutionName:'" + this.institutionName + "',hasSon:'" + this.hasSon + "',institutionType:'" + this.institutionType + "'}";
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/workflow/beans/others/InstitutionCodeInfo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */