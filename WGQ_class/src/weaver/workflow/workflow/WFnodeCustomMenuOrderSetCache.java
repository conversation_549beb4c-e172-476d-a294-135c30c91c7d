/*    */ package weaver.workflow.workflow;
/*    */ 
/*    */ import com.alibaba.fastjson.JSONObject;
/*    */ import java.util.HashMap;
/*    */ import java.util.LinkedList;
/*    */ import java.util.List;
/*    */ import java.util.Map;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.BaseBean;
/*    */ 
/*    */ 
/*    */ 
/*    */ public class WFnodeCustomMenuOrderSetCache
/*    */ {
/* 15 */   public static Map<String, List<Map<String, Object>>> wfNodeCMOSCache = new HashMap<>();
/*    */   
/*    */   public static void addData(String paramString1, int paramInt1, int paramInt2, String paramString2) {
/* 18 */     if (wfNodeCMOSCache.get(paramString1) == null) {
/* 19 */       LinkedList<Map<String, Object>> linkedList = new LinkedList();
/* 20 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 21 */       wfNodeCMOSCache.put(paramString1, linkedList);
/* 22 */       linkedList.add(hashMap);
/* 23 */       hashMap.put("id", Integer.valueOf(paramInt1));
/* 24 */       hashMap.put("smallId", Integer.valueOf(paramInt2));
/* 25 */       hashMap.put("isQuick", paramString2);
/*    */     } else {
/* 27 */       List<HashMap<Object, Object>> list = (List)wfNodeCMOSCache.get(paramString1);
/* 28 */       for (Map map : list) {
/* 29 */         if (String.valueOf(paramInt1).equals(map.get("id")) && String.valueOf(paramInt2).equals(map.get("smallId")) && String.valueOf(paramString2).equals(map.get("isQuick"))) {
/*    */           return;
/*    */         }
/*    */       } 
/* 33 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 34 */       list.add(hashMap);
/* 35 */       hashMap.put("id", Integer.valueOf(paramInt1));
/* 36 */       hashMap.put("smallId", Integer.valueOf(paramInt2));
/* 37 */       hashMap.put("isQuick", paramString2);
/*    */     } 
/*    */   }
/*    */   
/*    */   public static List<Map<String, Object>> initCacheByWfid(int paramInt1, int paramInt2) {
/* 42 */     String str = paramInt1 + "_" + paramInt2;
/* 43 */     synchronized (String.valueOf(str).intern()) {
/* 44 */       List<Map<String, Object>> list = wfNodeCMOSCache.get(str);
/* 45 */       (new BaseBean()).writeLog("initCacheByWfid----workflowId:" + paramInt1 + ";nodeid:" + paramInt2 + ";list:" + ((list == null) ? "null" : JSONObject.toJSONString(list)));
/* 46 */       if (list == null) {
/* 47 */         list = new LinkedList();
/* 48 */         RecordSet recordSet = new RecordSet();
/* 49 */         recordSet.executeQuery("select id,isQuick,smallId from WF_nodeCustomMenuOrderSet where workflowid = ? and nodeid = ? order by orders asc,id asc", new Object[] { Integer.valueOf(paramInt1), Integer.valueOf(paramInt2) });
/* 50 */         if (recordSet.getCounts() == 0) return list; 
/* 51 */         while (recordSet.next()) {
/* 52 */           HashMap<Object, Object> hashMap = new HashMap<>();
/* 53 */           hashMap.put("id", Integer.valueOf(recordSet.getInt("id")));
/* 54 */           hashMap.put("smallId", Integer.valueOf(recordSet.getInt("smallId")));
/* 55 */           hashMap.put("isQuick", recordSet.getString("isQuick"));
/* 56 */           list.add(hashMap);
/*    */         } 
/* 58 */         wfNodeCMOSCache.put(str, list);
/*    */       } 
/*    */       
/* 61 */       return list;
/*    */     } 
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/workflow/WFnodeCustomMenuOrderSetCache.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */