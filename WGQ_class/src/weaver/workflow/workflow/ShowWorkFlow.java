/*     */ package weaver.workflow.workflow;
/*     */ 
/*     */ import com.sun.image.codec.jpeg.JPEGCodec;
/*     */ import com.sun.image.codec.jpeg.JPEGEncodeParam;
/*     */ import com.sun.image.codec.jpeg.JPEGImageEncoder;
/*     */ import java.awt.BasicStroke;
/*     */ import java.awt.Color;
/*     */ import java.awt.Graphics2D;
/*     */ import java.awt.image.BufferedImage;
/*     */ import java.io.BufferedOutputStream;
/*     */ import java.io.IOException;
/*     */ import java.io.OutputStream;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.HashSet;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import java.util.Set;
/*     */ import javax.servlet.ServletException;
/*     */ import javax.servlet.ServletOutputStream;
/*     */ import javax.servlet.http.HttpServlet;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class ShowWorkFlow
/*     */   extends HttpServlet
/*     */ {
/*  43 */   private int nodesizex = 60;
/*  44 */   private int nodesizey = 40;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private ArrayList transNodes(List<String> paramList1, List<String> paramList2, Map<Integer, List<Integer>> paramMap, List<String> paramList3) {
/*  50 */     for (int i = paramList1.size() - 1; i >= 0; i--) {
/*  51 */       String[] arrayOfString = ((String)paramList1.get(i)).split("_");
/*  52 */       ArrayList arrayList = tansNode(arrayOfString[0], arrayOfString[2], paramList2.get(i), paramMap, paramList3);
/*     */       
/*  54 */       if (arrayList != null) {
/*     */ 
/*     */         
/*  57 */         paramList1.remove(i);
/*  58 */         for (String str : arrayList) {
/*  59 */           paramList1.add(i, str);
/*     */         }
/*     */       } 
/*     */     } 
/*  63 */     return null;
/*     */   }
/*     */ 
/*     */   
/*     */   private ArrayList tansNode(String paramString1, String paramString2, String paramString3, Map<Integer, List<Integer>> paramMap, List<String> paramList) {
/*  68 */     ArrayList<String> arrayList1 = new ArrayList();
/*  69 */     HashSet<String> hashSet = new HashSet();
/*     */     
/*  71 */     tansNode(arrayList1, hashSet, paramString1, paramString2, paramString3, paramMap, paramList);
/*     */     
/*  73 */     if (arrayList1.size() == 1) {
/*  74 */       return null;
/*     */     }
/*  76 */     arrayList1.add(paramString2);
/*     */     
/*  78 */     ArrayList<String> arrayList2 = new ArrayList();
/*     */     
/*  80 */     for (byte b = 0; b < arrayList1.size() - 1; b++) {
/*  81 */       arrayList2.add((new StringBuilder()).append(arrayList1.get(b)).append("_2_").append(arrayList1.get(b + 1)).toString());
/*     */     }
/*     */     
/*  84 */     return arrayList2;
/*     */   }
/*     */ 
/*     */   
/*     */   private void tansNode(ArrayList<String> paramArrayList, Set<String> paramSet, String paramString1, String paramString2, String paramString3, Map<Integer, List<Integer>> paramMap, List<String> paramList) {
/*  89 */     paramArrayList.add(paramString1);
/*     */     
/*  91 */     paramSet.add(paramString1);
/*     */     
/*  93 */     if (paramString1.equals(paramString2)) {
/*     */       return;
/*     */     }
/*     */     
/*  97 */     List list = paramMap.get(new Integer(paramString1));
/*     */     
/*  99 */     if (list == null || list.size() < 1) {
/*     */       return;
/*     */     }
/*     */     
/* 103 */     for (Integer integer : list) {
/*     */       
/* 105 */       if (integer.toString().equals(paramString2)) {
/*     */         continue;
/*     */       }
/*     */       
/* 109 */       if (!paramSet.contains(integer.toString()) && paramList.indexOf(integer + " " + paramString3) != -1) {
/* 110 */         tansNode(paramArrayList, paramSet, integer.toString(), paramString2, paramString3, paramMap, paramList);
/*     */       }
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void doGet(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws ServletException, IOException {
/* 123 */     paramHttpServletResponse.setContentType("text/html; charset=UTF-8");
/*     */     
/* 125 */     int i = 0;
/* 126 */     int j = 0;
/*     */     
/* 128 */     String str1 = Util.null2String(paramHttpServletRequest.getParameter("requestid"));
/* 129 */     String str2 = "-1";
/* 130 */     String str3 = "-1";
/*     */     
/* 132 */     if (str1.equals("-1")) {
/* 133 */       str2 = Util.null2String(paramHttpServletRequest.getParameter("workflowid"));
/* 134 */       str3 = Util.null2String(paramHttpServletRequest.getParameter("currentnodeid"));
/*     */     } 
/* 136 */     String str4 = "";
/* 137 */     RecordSet recordSet = new RecordSet();
/*     */     
/* 139 */     ArrayList<String> arrayList1 = new ArrayList();
/* 140 */     ArrayList<String> arrayList2 = new ArrayList();
/* 141 */     ArrayList<String> arrayList3 = new ArrayList();
/* 142 */     ArrayList<String> arrayList4 = new ArrayList();
/*     */     
/* 144 */     ArrayList<String> arrayList5 = new ArrayList();
/* 145 */     ArrayList<String> arrayList6 = new ArrayList();
/*     */     
/* 147 */     arrayList1.clear();
/* 148 */     arrayList2.clear();
/* 149 */     arrayList3.clear();
/* 150 */     arrayList4.clear();
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*     */     try {
/* 156 */       if (!str1.equals("-1")) {
/* 157 */         str4 = "select * from workflow_requestbase where requestid = ? ";
/* 158 */         recordSet.executeQuery(str4, new Object[] { str1 });
/* 159 */         if (recordSet.next()) {
/*     */           
/* 161 */           str2 = Util.null2String(recordSet.getString("workflowid"));
/* 162 */           str3 = Util.null2String(recordSet.getString("currentnodeid"));
/*     */         } 
/* 164 */         str4 = "select * from workflow_requestLog where requestid = ? ";
/* 165 */         recordSet.executeQuery(str4, new Object[] { str1 });
/* 166 */         while (recordSet.next()) {
/*     */           
/* 168 */           String str8 = Util.null2String(recordSet.getString("nodeid"));
/* 169 */           String str9 = Util.null2String(recordSet.getString("logtype"));
/* 170 */           String str10 = Util.null2String(recordSet.getString("destnodeid"));
/* 171 */           arrayList4.add(str8 + "_" + str9 + "_" + str10);
/* 172 */           arrayList6.add(recordSet.getString("operatedate") + " " + recordSet.getString("operatetime"));
/*     */         } 
/*     */         
/* 175 */         str4 = "select nodeid,operatedate,operatetime from workflow_currentoperator where requestid = ? ";
/* 176 */         recordSet.executeQuery(str4, new Object[] { str1 });
/* 177 */         while (recordSet.next()) {
/*     */           
/* 179 */           String str = Util.null2String(recordSet.getString("nodeid"));
/* 180 */           arrayList5.add(new Integer(str) + " " + recordSet.getString("operatedate") + " " + recordSet.getString("operatetime"));
/*     */         } 
/*     */       } 
/*     */ 
/*     */       
/* 185 */       str4 = "SELECT nodeid, drawxpos, drawypos, drawxpos, drawypos FROM workflow_flownode,workflow_nodebase WHERE workflow_flownode.nodeid = workflow_nodebase.id and (workflow_nodebase.IsFreeNode is null or workflow_nodebase.IsFreeNode !='1') and workflow_flownode.workflowid = ? union SELECT nodeid, drawxpos, drawypos, drawxpos, drawypos FROM workflow_flownode,workflow_nodebase WHERE workflow_flownode.nodeid = workflow_nodebase.id and workflow_nodebase.IsFreeNode='1' and workflow_flownode.workflowid = ? and workflow_nodebase.requestid=?";
/* 186 */       recordSet.executeQuery(str4, new Object[] { str2, str2, str1 });
/* 187 */       while (recordSet.next()) {
/* 188 */         arrayList1.add("" + recordSet.getInt("nodeid"));
/* 189 */         arrayList2.add("" + recordSet.getInt("drawxpos"));
/* 190 */         arrayList3.add("" + recordSet.getInt("drawypos"));
/* 191 */         int k = recordSet.getInt("drawxpos");
/* 192 */         int m = recordSet.getInt("drawypos");
/*     */         
/* 194 */         if (k > i)
/* 195 */           i = k; 
/* 196 */         if (m > j) {
/* 197 */           j = m;
/*     */         }
/*     */       } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 219 */       HashMap<Object, Object> hashMap = new HashMap<>();
/*     */       
/* 221 */       String str5 = "(select COUNT(1) from workflow_nodebase b where workflow_nodelink.nodeid=b.id and (IsFreeNode is null or IsFreeNode !='1'))>0 and (select COUNT(1) from workflow_nodebase b where workflow_nodelink.destnodeid=b.id and (IsFreeNode is null or IsFreeNode !='1'))>0 ";
/* 222 */       String str6 = "(select COUNT(1) from workflow_nodebase b where workflow_nodelink.nodeid=b.id and IsFreeNode ='1' and requestid=?) >0 ";
/* 223 */       String str7 = "(select COUNT(1) from workflow_nodebase b where workflow_nodelink.destnodeid=b.id and IsFreeNode ='1' and requestid=?)>0 ";
/* 224 */       str4 = "select * from workflow_nodelink where wfrequestid is null and workflowid =  ? and ((" + str5 + ") or (" + str6 + " or " + str7 + "))";
/* 225 */       recordSet.executeQuery(str4, new Object[] { str2, str1, str1 });
/* 226 */       while (recordSet.next()) {
/* 227 */         Integer integer = Integer.valueOf(recordSet.getInt("nodeid"));
/* 228 */         List<Integer> list = (List)hashMap.get(integer);
/* 229 */         if (list == null) {
/* 230 */           list = new ArrayList();
/* 231 */           hashMap.put(integer, list);
/*     */         } 
/* 233 */         list.add(Integer.valueOf(recordSet.getInt("destnodeid")));
/*     */         
/* 235 */         if (recordSet.getInt("x1") != -1) {
/* 236 */           if (recordSet.getInt("x1") > i)
/* 237 */             i = recordSet.getInt("x1"); 
/* 238 */           if (recordSet.getInt("y1") > j)
/* 239 */             j = recordSet.getInt("y1"); 
/*     */         } 
/* 241 */         if (recordSet.getInt("x2") != -1) {
/* 242 */           if (recordSet.getInt("x2") > i)
/* 243 */             i = recordSet.getInt("x2"); 
/* 244 */           if (recordSet.getInt("y2") > j)
/* 245 */             j = recordSet.getInt("y2"); 
/*     */         } 
/* 247 */         if (recordSet.getInt("x3") != -1) {
/* 248 */           if (recordSet.getInt("x3") > i)
/* 249 */             i = recordSet.getInt("x3"); 
/* 250 */           if (recordSet.getInt("y3") > j)
/* 251 */             j = recordSet.getInt("y3"); 
/*     */         } 
/* 253 */         if (recordSet.getInt("x4") != -1) {
/* 254 */           if (recordSet.getInt("x4") > i)
/* 255 */             i = recordSet.getInt("x4"); 
/* 256 */           if (recordSet.getInt("y4") > j)
/* 257 */             j = recordSet.getInt("y4"); 
/*     */         } 
/* 259 */         if (recordSet.getInt("x5") != -1) {
/* 260 */           if (recordSet.getInt("x5") > i)
/* 261 */             i = recordSet.getInt("x5"); 
/* 262 */           if (recordSet.getInt("y5") > j) {
/* 263 */             j = recordSet.getInt("y5");
/*     */           }
/*     */         } 
/*     */       } 
/*     */       
/* 268 */       transNodes(arrayList4, arrayList6, (Map)hashMap, arrayList5);
/*     */       
/* 270 */       ServletOutputStream servletOutputStream = paramHttpServletResponse.getOutputStream();
/* 271 */       BufferedOutputStream bufferedOutputStream = new BufferedOutputStream((OutputStream)servletOutputStream);
/*     */       
/* 273 */       BufferedImage bufferedImage = new BufferedImage(i + 50, j + 50, 1);
/*     */       
/* 275 */       JPEGEncodeParam jPEGEncodeParam = null;
/* 276 */       JPEGImageEncoder jPEGImageEncoder = JPEGCodec.createJPEGEncoder(bufferedOutputStream);
/*     */       
/* 278 */       jPEGEncodeParam = jPEGImageEncoder.getDefaultJPEGEncodeParam(bufferedImage);
/* 279 */       jPEGEncodeParam.setQuality(0.9F, true);
/* 280 */       jPEGImageEncoder.setJPEGEncodeParam(jPEGEncodeParam);
/*     */       
/* 282 */       Graphics2D graphics2D = bufferedImage.createGraphics();
/*     */       
/* 284 */       graphics2D.setColor(Color.white);
/* 285 */       graphics2D.fillRect(0, 0, i + 50, j + 50);
/*     */ 
/*     */       
/* 288 */       str5 = "(select COUNT(1) from workflow_nodebase b where workflow_nodelink.nodeid=b.id and (IsFreeNode is null or IsFreeNode !='1'))>0 and (select COUNT(1) from workflow_nodebase b where workflow_nodelink.destnodeid=b.id and (IsFreeNode is null or IsFreeNode !='1'))>0 ";
/* 289 */       str6 = "(select COUNT(1) from workflow_nodebase b where workflow_nodelink.nodeid=b.id and IsFreeNode ='1' and requestid=?)>0 ";
/* 290 */       str7 = "(select COUNT(1) from workflow_nodebase b where workflow_nodelink.destnodeid=b.id and IsFreeNode ='1' and requestid=?)>0 ";
/*     */ 
/*     */       
/* 293 */       str4 = "select * from workflow_nodelink where wfrequestid is null and workflowid =  ? and ((" + str5 + ") or (" + str6 + " or " + str7 + "))";
/* 294 */       recordSet.executeQuery(str4, new Object[] { str2, str1, str1 });
/* 295 */       while (recordSet.next()) {
/* 296 */         int k = 0, m = 0;
/*     */         
/* 298 */         int n = recordSet.getInt("nodeid");
/* 299 */         int i1 = arrayList1.indexOf("" + n);
/*     */         
/* 301 */         if (i1 != -1) {
/* 302 */           k = Util.getIntValue("" + arrayList2.get(i1));
/* 303 */           m = Util.getIntValue("" + arrayList3.get(i1));
/*     */         } 
/* 305 */         int i2 = recordSet.getInt("directionfrom");
/* 306 */         int i3 = recordSet.getInt("directionto");
/*     */         
/* 308 */         int i4 = GetPointXOfDirect(k, m, i2);
/* 309 */         int i5 = GetPointYOfDirect(k, m, i2);
/*     */         
/* 311 */         n = recordSet.getInt("destnodeid");
/* 312 */         i1 = arrayList1.indexOf("" + n);
/*     */         
/* 314 */         if (i1 != -1) {
/* 315 */           k = Util.getIntValue("" + arrayList2.get(i1));
/* 316 */           m = Util.getIntValue("" + arrayList3.get(i1));
/*     */         } 
/* 318 */         int i6 = GetPointXOfDirect(k, m, i3);
/* 319 */         int i7 = GetPointYOfDirect(k, m, i3);
/* 320 */         BasicStroke basicStroke = new BasicStroke(3.0F);
/* 321 */         graphics2D.setStroke(basicStroke);
/*     */         
/* 323 */         graphics2D.setColor(Color.black);
/* 324 */         if (Util.null2String(recordSet.getString("isreject")).equals("1")) {
/*     */           
/* 326 */           if (arrayList4.indexOf("" + recordSet.getInt("nodeid") + "_3_" + n) != -1) {
/* 327 */             graphics2D.setColor(Color.red);
/*     */           }
/*     */         }
/* 330 */         else if (arrayList4.indexOf("" + recordSet.getInt("nodeid") + "_2_" + n) != -1 || arrayList4.indexOf("" + recordSet.getInt("nodeid") + "_0_" + n) != -1 || arrayList4.indexOf("" + recordSet.getInt("nodeid") + "_i_" + n) != -1) {
/* 331 */           graphics2D.setColor(Color.red);
/*     */         } 
/*     */         
/* 334 */         int i8 = i4, i9 = i5;
/* 335 */         int i10 = i6, i11 = i7;
/*     */         
/* 337 */         if (recordSet.getInt("x1") != -1) {
/*     */ 
/*     */           
/* 340 */           drawLine(graphics2D, i4, i5, recordSet.getInt("x1"), recordSet.getInt("y1"));
/* 341 */           if (recordSet.getInt("x2") != -1) {
/*     */             
/* 343 */             drawLine(graphics2D, recordSet.getInt("x1"), recordSet.getInt("y1"), recordSet.getInt("x2"), recordSet.getInt("y2"));
/* 344 */             if (recordSet.getInt("x3") != -1) {
/*     */               
/* 346 */               drawLine(graphics2D, recordSet.getInt("x2"), recordSet.getInt("y2"), recordSet.getInt("x3"), recordSet.getInt("y3"));
/* 347 */               if (recordSet.getInt("x4") != -1) {
/*     */                 
/* 349 */                 drawLine(graphics2D, recordSet.getInt("x3"), recordSet.getInt("y3"), recordSet.getInt("x4"), recordSet.getInt("y4"));
/* 350 */                 if (recordSet.getInt("x5") != -1) {
/*     */                   
/* 352 */                   drawLine(graphics2D, recordSet.getInt("x4"), recordSet.getInt("y4"), recordSet.getInt("x5"), recordSet.getInt("y5"));
/* 353 */                   i8 = recordSet.getInt("x5"); i9 = recordSet.getInt("y5");
/*     */                   
/* 355 */                   drawLine(graphics2D, i6, i7, recordSet.getInt("x5"), recordSet.getInt("y5"));
/*     */                 }
/*     */                 else {
/*     */                   
/* 359 */                   drawLine(graphics2D, i6, i7, recordSet.getInt("x4"), recordSet.getInt("y4"));
/* 360 */                   i8 = recordSet.getInt("x4"); i9 = recordSet.getInt("y4");
/*     */                 } 
/*     */               } else {
/*     */                 
/* 364 */                 drawLine(graphics2D, i6, i7, recordSet.getInt("x3"), recordSet.getInt("y3"));
/* 365 */                 i8 = recordSet.getInt("x3"); i9 = recordSet.getInt("y3");
/*     */               } 
/*     */             } else {
/*     */               
/* 369 */               drawLine(graphics2D, i6, i7, recordSet.getInt("x2"), recordSet.getInt("y2"));
/* 370 */               i8 = recordSet.getInt("x2"); i9 = recordSet.getInt("y2");
/*     */             } 
/*     */           } else {
/*     */             
/* 374 */             drawLine(graphics2D, i6, i7, recordSet.getInt("x1"), recordSet.getInt("y1"));
/* 375 */             i8 = recordSet.getInt("x1"); i9 = recordSet.getInt("y1");
/*     */           } 
/*     */         } else {
/*     */           
/* 379 */           drawLine(graphics2D, i6, i7, i4, i5);
/*     */         } 
/*     */         
/* 382 */         int i12 = i8 + i9 - i11;
/* 383 */         int i13 = i9 - i8 + i10;
/* 384 */         int i14 = i8 - i9 + i11;
/* 385 */         int i15 = i9 + i8 - i10;
/*     */         
/* 387 */         double d = Math.sqrt(((i12 - i10) * (i12 - i10) + (i13 - i11) * (i13 - i11)));
/* 388 */         if (d != 0.0D) {
/* 389 */           d = 10.0D / d;
/* 390 */           if (i12 == i10 && i13 != i11) {
/* 391 */             i13 = i11 + 10;
/* 392 */             i12 = i12;
/*     */           }
/* 394 */           else if (i13 == i11) {
/* 395 */             i12 = i10 + 10;
/* 396 */             i13 = i13;
/*     */           } else {
/*     */             
/* 399 */             i12 = (int)(i10 + d * (i12 - i10));
/* 400 */             i13 = (int)(i11 + d * (i13 - i11));
/*     */           } 
/*     */           
/* 403 */           if (i14 == i10 && i15 != i11) {
/* 404 */             i15 = i11 + 10;
/* 405 */             i14 = i14;
/*     */           }
/* 407 */           else if (i15 == i11) {
/* 408 */             i14 = i10 + 10;
/* 409 */             i15 = i15;
/*     */           } else {
/*     */             
/* 412 */             i14 = (int)(i10 + d * (i14 - i10));
/* 413 */             i15 = (int)(i11 + d * (i15 - i11));
/*     */           } 
/*     */         } 
/*     */         
/* 417 */         drawLine(graphics2D, i10, i11, i12, i13);
/*     */         
/* 419 */         drawLine(graphics2D, i10, i11, i14, i15);
/*     */       } 
/*     */       
/* 422 */       graphics2D.dispose();
/* 423 */       jPEGImageEncoder.encode(bufferedImage);
/*     */ 
/*     */       
/* 426 */       servletOutputStream.flush();
/* 427 */       bufferedOutputStream.close();
/* 428 */       servletOutputStream.close();
/*     */ 
/*     */     
/*     */     }
/* 432 */     catch (Exception exception) {
/* 433 */       recordSet.writeLog(exception);
/*     */     } 
/*     */   }
/*     */   
/*     */   private int GetPointXOfDirect(int paramInt1, int paramInt2, int paramInt3) {
/* 438 */     switch (paramInt3) {
/*     */       
/*     */       case 0:
/*     */       case 1:
/*     */       case 2:
/*     */       case 14:
/*     */       case 15:
/* 445 */         return paramInt1 + this.nodesizex;
/*     */       case 3:
/*     */       case 13:
/* 448 */         return paramInt1 + this.nodesizex / 2;
/*     */       case 4:
/*     */       case 12:
/* 451 */         return paramInt1;
/*     */       case 5:
/*     */       case 11:
/* 454 */         return paramInt1 - this.nodesizex / 2;
/*     */       case 6:
/*     */       case 7:
/*     */       case 8:
/*     */       case 9:
/*     */       case 10:
/* 460 */         return paramInt1 - this.nodesizex;
/*     */     } 
/*     */     
/* 463 */     return paramInt1;
/*     */   }
/*     */   
/*     */   private int GetPointYOfDirect(int paramInt1, int paramInt2, int paramInt3) {
/* 467 */     switch (paramInt3) {
/*     */       
/*     */       case 2:
/*     */       case 3:
/*     */       case 4:
/*     */       case 5:
/*     */       case 6:
/* 474 */         return paramInt2 + this.nodesizey;
/*     */       case 1:
/*     */       case 7:
/* 477 */         return paramInt2 + this.nodesizey / 2;
/*     */       case 0:
/*     */       case 8:
/* 480 */         return paramInt2;
/*     */       case 9:
/*     */       case 15:
/* 483 */         return paramInt2 - this.nodesizey / 2;
/*     */       case 10:
/*     */       case 11:
/*     */       case 12:
/*     */       case 13:
/*     */       case 14:
/* 489 */         return paramInt2 - this.nodesizey;
/*     */     } 
/*     */     
/* 492 */     return paramInt2;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void drawLine(Graphics2D paramGraphics2D, int paramInt1, int paramInt2, int paramInt3, int paramInt4) {
/* 504 */     if (Math.abs(paramInt1 - paramInt3) < 2) {
/* 505 */       paramInt1 = paramInt3;
/*     */     }
/* 507 */     if (Math.abs(paramInt2 - paramInt4) < 2) {
/* 508 */       paramInt2 = paramInt4;
/*     */     }
/* 510 */     paramGraphics2D.drawLine(paramInt1, paramInt2, paramInt3, paramInt4);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/workflow/ShowWorkFlow.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */