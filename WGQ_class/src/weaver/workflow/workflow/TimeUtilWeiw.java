/*     */ package weaver.workflow.workflow;
/*     */ 
/*     */ import java.text.ParsePosition;
/*     */ import java.text.SimpleDateFormat;
/*     */ import java.util.Calendar;
/*     */ import java.util.Date;
/*     */ import java.util.GregorianCalendar;
/*     */ import weaver.general.Util;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class TimeUtilWeiw
/*     */ {
/*     */   public static String getTwoDay(String paramString1, String paramString2) {
/*  27 */     SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
/*  28 */     long l = 0L;
/*     */     try {
/*  30 */       Date date1 = simpleDateFormat.parse(paramString1);
/*  31 */       Date date2 = simpleDateFormat.parse(paramString2);
/*  32 */       l = (date1.getTime() - date2.getTime()) / 86400000L;
/*  33 */     } catch (Exception exception) {
/*  34 */       return "";
/*     */     } 
/*  36 */     return l + "";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getWeek(String paramString) {
/*  47 */     Date date = strToDate(paramString);
/*  48 */     Calendar calendar = Calendar.getInstance();
/*  49 */     calendar.setTime(date);
/*     */     
/*  51 */     return (new SimpleDateFormat("EEEE")).format(calendar.getTime());
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static Date strToDate(String paramString) {
/*  61 */     SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
/*  62 */     ParsePosition parsePosition = new ParsePosition(0);
/*  63 */     return simpleDateFormat.parse(paramString, parsePosition);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getDefaultDay() {
/*  73 */     String str1 = "";
/*  74 */     SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
/*  75 */     Calendar calendar = Calendar.getInstance();
/*     */     
/*  77 */     String str2 = Util.add0(calendar.get(1), 4) + "-" + Util.add0(calendar.get(2) + 1, 2) + "-01";
/*     */     
/*  79 */     calendar.set(5, 1);
/*  80 */     calendar.add(2, 1);
/*  81 */     calendar.add(5, -1);
/*     */     
/*  83 */     str1 = str2 + ";" + simpleDateFormat.format(calendar.getTime());
/*  84 */     return str1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getNowTime(String paramString) {
/*  94 */     Date date = new Date();
/*  95 */     SimpleDateFormat simpleDateFormat = new SimpleDateFormat(paramString);
/*  96 */     return simpleDateFormat.format(date);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private int getMondayPlus() {
/* 106 */     Calendar calendar = Calendar.getInstance();
/*     */     
/* 108 */     int i = calendar.get(7) - 1;
/* 109 */     if (i == 1) {
/* 110 */       return 0;
/*     */     }
/* 112 */     return 1 - i;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getMondayOFWeek() {
/* 122 */     int i = getMondayPlus();
/* 123 */     GregorianCalendar gregorianCalendar = new GregorianCalendar();
/* 124 */     gregorianCalendar.add(5, i);
/* 125 */     Date date = gregorianCalendar.getTime();
/* 126 */     SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
/* 127 */     return simpleDateFormat.format(date);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCurrentWeekday() {
/* 137 */     int i = getMondayPlus();
/* 138 */     GregorianCalendar gregorianCalendar = new GregorianCalendar();
/* 139 */     gregorianCalendar.add(5, i + 6);
/* 140 */     Date date = gregorianCalendar.getTime();
/* 141 */     SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
/* 142 */     return simpleDateFormat.format(date);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getThisSeasonTime(int paramInt) {
/* 153 */     int[][] arrayOfInt = { { 1, 2, 3 }, { 4, 5, 6 }, { 7, 8, 9 }, { 10, 11, 12 } };
/* 154 */     byte b = 1;
/* 155 */     if (paramInt >= 1 && paramInt <= 3) {
/* 156 */       b = 1;
/*     */     }
/* 158 */     if (paramInt >= 4 && paramInt <= 6) {
/* 159 */       b = 2;
/*     */     }
/* 161 */     if (paramInt >= 7 && paramInt <= 9) {
/* 162 */       b = 3;
/*     */     }
/* 164 */     if (paramInt >= 10 && paramInt <= 12) {
/* 165 */       b = 4;
/*     */     }
/* 167 */     int i = arrayOfInt[b - 1][0];
/* 168 */     String str1 = i + "";
/* 169 */     if (i < 10) {
/* 170 */       str1 = "0" + i;
/*     */     }
/* 172 */     int j = arrayOfInt[b - 1][2];
/* 173 */     String str2 = j + "";
/* 174 */     if (j < 10) {
/* 175 */       str2 = "0" + j;
/*     */     }
/* 177 */     Date date = new Date();
/* 178 */     SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy");
/* 179 */     String str3 = simpleDateFormat.format(date);
/* 180 */     int k = Integer.parseInt(str3);
/*     */     
/* 182 */     boolean bool = true;
/* 183 */     int m = getLastDayOfMonth(k, j);
/* 184 */     return k + "-" + str1 + "-0" + bool + ";" + k + "-" + str2 + "-" + m;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private int getLastDayOfMonth(int paramInt1, int paramInt2) {
/* 197 */     if (paramInt2 == 1 || paramInt2 == 3 || paramInt2 == 5 || paramInt2 == 7 || paramInt2 == 8 || paramInt2 == 10 || paramInt2 == 12)
/*     */     {
/* 199 */       return 31;
/*     */     }
/* 201 */     if (paramInt2 == 4 || paramInt2 == 6 || paramInt2 == 9 || paramInt2 == 11) {
/* 202 */       return 30;
/*     */     }
/* 204 */     if (paramInt2 == 2) {
/* 205 */       if (isLeapYear(paramInt1)) {
/* 206 */         return 29;
/*     */       }
/* 208 */       return 28;
/*     */     } 
/*     */     
/* 211 */     return 0;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean isLeapYear(int paramInt) {
/* 221 */     return ((paramInt % 4 == 0 && paramInt % 100 != 0) || paramInt % 400 == 0);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/workflow/TimeUtilWeiw.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */