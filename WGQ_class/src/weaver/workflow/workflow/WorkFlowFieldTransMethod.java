/*     */ package weaver.workflow.workflow;
/*     */ 
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.ThreadVarLanguage;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.systeminfo.label.LabelComInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class WorkFlowFieldTransMethod
/*     */   extends BaseBean
/*     */ {
/*     */   public void WorkFlowTransMethod() {}
/*     */   
/*     */   public String getFieldTableType(String paramString) {
/*  30 */     String str = "";
/*  31 */     if ("0".equals(paramString)) {
/*  32 */       str = "" + SystemEnv.getHtmlLabelName(21778, ThreadVarLanguage.getLang()) + "";
/*     */     } else {
/*  34 */       str = "" + SystemEnv.getHtmlLabelName(130658, ThreadVarLanguage.getLang()) + "";
/*  35 */     }  return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getFieldTableType2(String paramString) {
/*  46 */     String str = "";
/*  47 */     if ("1".equals(paramString)) {
/*  48 */       str = "" + SystemEnv.getHtmlLabelName(130658, ThreadVarLanguage.getLang()) + "";
/*     */     } else {
/*  50 */       str = "" + SystemEnv.getHtmlLabelName(21778, ThreadVarLanguage.getLang()) + "";
/*     */     } 
/*  52 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getFieldViewType(String paramString) {
/*  63 */     String str = "";
/*  64 */     switch (Integer.parseInt(paramString))
/*     */     { case 1:
/*  66 */         str = "" + SystemEnv.getHtmlLabelName(10004353, ThreadVarLanguage.getLang()) + "";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */         
/*  93 */         return str;case 2: str = "" + SystemEnv.getHtmlLabelName(689, ThreadVarLanguage.getLang()) + ""; return str;case 3: str = "" + SystemEnv.getHtmlLabelName(695, ThreadVarLanguage.getLang()) + ""; return str;case 4: str = "Check" + SystemEnv.getHtmlLabelName(10004354, ThreadVarLanguage.getLang()) + ""; return str;case 5: str = "" + SystemEnv.getHtmlLabelName(690, ThreadVarLanguage.getLang()) + ""; return str;case 6: str = "" + SystemEnv.getHtmlLabelName(17616, ThreadVarLanguage.getLang()) + ""; return str;case 7: str = "" + SystemEnv.getHtmlLabelName(21691, ThreadVarLanguage.getLang()) + ""; return str;case 9: str = "" + SystemEnv.getHtmlLabelName(125583, ThreadVarLanguage.getLang()) + ""; return str; }  str = "" + SystemEnv.getHtmlLabelName(463, ThreadVarLanguage.getLang()) + ""; return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getFieldType(String paramString1, String paramString2) {
/*     */     byte b;
/* 105 */     String str1 = "";
/* 106 */     String str2 = "";
/* 107 */     int i = 0;
/* 108 */     RecordSet recordSet = new RecordSet();
/* 109 */     LabelComInfo labelComInfo = new LabelComInfo();
/* 110 */     str2 = "select fieldhtmltype from workflow_billfield where id = " + paramString2;
/* 111 */     recordSet.execute(str2);
/* 112 */     if (recordSet.next()) {
/* 113 */       i = Util.getIntValue(recordSet.getString("fieldhtmltype"), 0);
/*     */     }
/* 115 */     switch (i) {
/*     */       case 1:
/* 117 */         if ("1".equals(paramString1)) {
/* 118 */           str1 = "" + SystemEnv.getHtmlLabelName(128895, ThreadVarLanguage.getLang()) + ""; break;
/* 119 */         }  if ("2".equals(paramString1)) {
/* 120 */           str1 = "" + SystemEnv.getHtmlLabelName(696, ThreadVarLanguage.getLang()) + ""; break;
/* 121 */         }  if ("3".equals(paramString1))
/* 122 */           str1 = "" + SystemEnv.getHtmlLabelName(386754, ThreadVarLanguage.getLang()) + ""; 
/*     */         break;
/*     */       case 2:
/* 125 */         str1 = "" + SystemEnv.getHtmlLabelName(128895, ThreadVarLanguage.getLang()) + "";
/*     */         break;
/*     */       case 3:
/* 128 */         str2 = "select labelid from workflow_browserurl where id =" + paramString1;
/* 129 */         recordSet.execute(str2);
/* 130 */         if (recordSet.next()) {
/* 131 */           str1 = labelComInfo.getLabelname(recordSet.getInt("labelid"), 7); break;
/*     */         } 
/* 133 */         str1 = "" + SystemEnv.getHtmlLabelName(463, ThreadVarLanguage.getLang()) + "";
/*     */         break;
/*     */       case 4:
/* 136 */         str1 = "Check" + SystemEnv.getHtmlLabelName(10004354, ThreadVarLanguage.getLang()) + "";
/*     */         break;
/*     */       case 5:
/* 139 */         str2 = "select id , selectname from workflow_selectitem where isbill=1 and fieldid =" + paramString2 + " order by id";
/* 140 */         recordSet.execute(str2);
/* 141 */         b = 0;
/* 142 */         while (recordSet.next()) {
/* 143 */           b++;
/* 144 */           str1 = str1 + b + ". " + recordSet.getString("selectname") + "<BR>";
/*     */         } 
/*     */         break;
/*     */       case 6:
/* 148 */         str1 = "" + SystemEnv.getHtmlLabelName(17616, ThreadVarLanguage.getLang()) + "";
/*     */         break;
/*     */       case 7:
/* 151 */         if (paramString1.equals("1"))
/* 152 */           str1 = "" + SystemEnv.getHtmlLabelName(21692, ThreadVarLanguage.getLang()) + ""; 
/* 153 */         if (paramString1.equals("2")) {
/* 154 */           str1 = "" + SystemEnv.getHtmlLabelName(21693, ThreadVarLanguage.getLang()) + "";
/*     */         }
/*     */         break;
/*     */     } 
/*     */ 
/*     */     
/* 160 */     return str1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getIsAddByUser(String paramString) {
/* 171 */     String str = "";
/* 172 */     if ("0".equals(paramString)) {
/* 173 */       str = "" + SystemEnv.getHtmlLabelName(163, ThreadVarLanguage.getLang()) + "";
/*     */     } else {
/* 175 */       str = "" + SystemEnv.getHtmlLabelName(25105, ThreadVarLanguage.getLang()) + "";
/* 176 */     }  return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCanCheckBox(String paramString) {
/* 185 */     return "0".equals(paramString) ? "true" : "false";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getNodeLinkInfo(String paramString) {
/* 193 */     String str = "";
/* 194 */     RecordSet recordSet = new RecordSet();
/* 195 */     recordSet.execute("select nodename from workflow_nodebase where id=" + paramString);
/* 196 */     if (recordSet.next()) str = recordSet.getString("nodename"); 
/* 197 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getLabelnameForBill(String paramString1, String paramString2) {
/* 205 */     String str = "";
/*     */     try {
/* 207 */       int i = Util.getIntValue(paramString1, 0);
/* 208 */       LabelComInfo labelComInfo = new LabelComInfo();
/* 209 */       String[] arrayOfString = Util.TokenizerString2(paramString2, "+");
/* 210 */       int j = Util.getIntValue(arrayOfString[0], 7);
/* 211 */       int k = Util.getIntValue(arrayOfString[1], 0);
/* 212 */       int m = Util.getIntValue(arrayOfString[2], 1);
/* 213 */       int n = Util.getIntValue(arrayOfString[3], 1);
/* 214 */       String str1 = Util.null2String(arrayOfString[4]);
/*     */       
/* 216 */       int i1 = 0;
/* 217 */       if (arrayOfString.length == 6) {
/* 218 */         i1 = Util.getIntValue(arrayOfString[5], 1);
/*     */       }
/*     */       
/* 221 */       if (!"0".equalsIgnoreCase(str1.trim())) {
/* 222 */         str1 = "";
/*     */       }
/* 224 */       if (m == 0) {
/* 225 */         str = "<a href=\"/workflow/workflow/BillManagementFieldAdd" + str1 + ".jsp?fieldid=" + k + "&src=editfield&isused=true&srcType=mainfield&billId=" + n + "\">" + labelComInfo.getLabelname(i, j) + "</a>";
/* 226 */         if (i1 == 1) {
/* 227 */           str = "<a href=\"javascript:addNewFieldDialog(0," + k + ")\">" + labelComInfo.getLabelname(i, j) + "</a>";
/*     */         }
/*     */       } else {
/* 230 */         str = labelComInfo.getLabelname(i, j);
/*     */       } 
/* 232 */     } catch (Exception exception) {}
/*     */ 
/*     */ 
/*     */     
/* 236 */     return str;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/workflow/WorkFlowFieldTransMethod.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */