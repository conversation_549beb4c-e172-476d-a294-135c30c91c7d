/*     */ package weaver.workflow.workflow;
/*     */ 
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.company.SubCompanyComInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class SubcompanyShowAttrManager
/*     */   extends BaseBean
/*     */ {
/*     */   private static final String subCompanyIcon = "/images/treeimages/Home_wev8.gif";
/*     */   
/*     */   public String getOrgTreeXMLByComp(int paramInt1, int paramInt2, int paramInt3, int paramInt4, int paramInt5) throws Exception {
/*  33 */     String str = "";
/*  34 */     SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/*  35 */     subCompanyComInfo.setTofirstRow();
/*  36 */     while (subCompanyComInfo.next()) {
/*  37 */       String str1 = subCompanyComInfo.getSupsubcomid();
/*  38 */       if (!str1.equals("0"))
/*  39 */         continue;  String str2 = subCompanyComInfo.getSubCompanyid();
/*  40 */       String str3 = subCompanyComInfo.getSubCompanyname();
/*  41 */       String str4 = SubcompanyShowAttrUtil.getObjPara(paramInt1, paramInt2, paramInt3, paramInt4, paramInt5, Integer.parseInt(str2), "1");
/*  42 */       str3 = Util.replace(Util.replace(Util.replace(Util.replace(Util.replace(str3, "<", "&lt;", 0), ">", "&gt;", 0), "&", "&amp;", 0), "'", "&apos;", 0), "\"", "&quot;", 0);
/*  43 */       str = str + "<tree text=\"" + str3 + "\" action=\"" + str2 + "|1|" + str4 + "\" icon=\"" + "/images/treeimages/Home_wev8.gif" + "\" openIcon=\"" + "/images/treeimages/Home_wev8.gif" + "\" src=\"showSubcompanyShowAttrListTree.jsp?subCompanyId=" + str2 + "&amp;workflowId=" + paramInt1 + "&amp;formId=" + paramInt2 + "&amp;isBill=" + paramInt3 + "&amp;fieldId=" + paramInt4 + "&amp;selectValue=" + paramInt5 + "\"/>";
/*     */     } 
/*  45 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getSubCompanyTreeJSByComp(int paramInt1, int paramInt2, int paramInt3, int paramInt4, int paramInt5) throws Exception {
/*  59 */     String str = "";
/*  60 */     SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/*  61 */     subCompanyComInfo.setTofirstRow();
/*  62 */     while (subCompanyComInfo.next()) {
/*  63 */       String str1 = subCompanyComInfo.getSupsubcomid();
/*  64 */       if (!str1.equals("0"))
/*  65 */         continue;  String str2 = subCompanyComInfo.getSubCompanyid();
/*  66 */       String str3 = subCompanyComInfo.getSubCompanyname();
/*  67 */       String str4 = SubcompanyShowAttrUtil.getObjPara(paramInt1, paramInt2, paramInt3, paramInt4, paramInt5, Integer.parseInt(str2), "1");
/*  68 */       str = str + "tree.add(rti = new WebFXLoadTreeItem('" + str3 + "','showSubcompanyShowAttrListTree.jsp?subCompanyId=" + str2 + "&workflowId=" + paramInt1 + "&formId=" + paramInt2 + "&isBill=" + paramInt3 + "&fieldId=" + paramInt4 + "&selectValue=" + paramInt5 + "','" + str2 + "|1|" + str4 + "','','" + "/images/treeimages/Home_wev8.gif" + "','" + "/images/treeimages/Home_wev8.gif" + "'));";
/*     */     } 
/*  70 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getOrgTreeXMLBySubComp(int paramInt1, int paramInt2, int paramInt3, int paramInt4, int paramInt5, String paramString1, String paramString2) throws Exception {
/*  87 */     String str = "";
/*  88 */     SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/*  89 */     subCompanyComInfo.setTofirstRow();
/*  90 */     while (subCompanyComInfo.next()) {
/*  91 */       String str1 = subCompanyComInfo.getSupsubcomid();
/*  92 */       if (!str1.equals(paramString1))
/*  93 */         continue;  String str2 = subCompanyComInfo.getSubCompanyid();
/*  94 */       String str3 = subCompanyComInfo.getSubCompanyname();
/*  95 */       String str4 = SubcompanyShowAttrUtil.getObjPara(paramInt1, paramInt2, paramInt3, paramInt4, paramInt5, Integer.parseInt(str2), "1");
/*  96 */       if (!paramString2.equals("")) {
/*  97 */         str3 = Util.replace(Util.replace(Util.replace(Util.replace(Util.replace(str3, "<", "&lt;", 0), ">", "&gt;", 0), "&", "&amp;", 0), "'", "&apos;", 0), "\"", "&quot;", 0);
/*  98 */         str = str + "<tree text=\"" + str3 + "\" action=\"" + str2 + "|1|" + str4 + "\" icon=\"" + "/images/treeimages/Home_wev8.gif" + "\" openIcon=\"" + "/images/treeimages/Home_wev8.gif" + "\" src=\"showSubcompanyShowAttrListTree.jsp?subCompanyId=" + str2 + "&amp;workflowId=" + paramInt1 + "&amp;formId=" + paramInt2 + "&amp;isBill=" + paramInt3 + "&amp;fieldId=" + paramInt4 + "&amp;selectValue=" + paramInt5 + "\"/>"; continue;
/*     */       } 
/* 100 */       str3 = Util.replace(Util.replace(Util.replace(Util.replace(Util.replace(str3, "<", "&lt;", 0), ">", "&gt;", 0), "&", "&amp;", 0), "'", "&apos;", 0), "\"", "&quot;", 0);
/* 101 */       str = str + "<tree text=\"" + str3 + "\" action=\"setSubcompany('com_" + str2 + "')\" icon=\"" + "/images/treeimages/Home_wev8.gif" + "\" openIcon=\"" + "/images/treeimages/Home_wev8.gif" + "\" src=\"showSubcompanyShowAttrListTree.jsp?subCompanyId=" + str2 + "&amp;workflowId=" + paramInt1 + "&amp;formId=" + paramInt2 + "&amp;isBill=" + paramInt3 + "&amp;fieldId=" + paramInt4 + "&amp;selectValue=" + paramInt5 + "\"/>";
/*     */     } 
/*     */ 
/*     */     
/* 105 */     return str;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/workflow/SubcompanyShowAttrManager.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */