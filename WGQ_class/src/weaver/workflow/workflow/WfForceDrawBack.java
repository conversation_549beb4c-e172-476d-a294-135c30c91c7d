/*      */ package weaver.workflow.workflow;
/*      */ 
/*      */ import com.engine.kq.biz.KQFlowActiontBiz;
/*      */ import com.engine.workflow.biz.freeNode.FreeNodeBiz;
/*      */ import com.engine.workflow.biz.freeNode.RequestFreeNodeFlowBiz;
/*      */ import com.engine.workflow.biz.workflowOvertime.OvertimeBiz;
/*      */ import com.engine.workflow.util.CollectionUtil;
/*      */ import java.util.ArrayList;
/*      */ import java.util.HashMap;
/*      */ import java.util.List;
/*      */ import java.util.Map;
/*      */ import javax.servlet.http.HttpServletRequest;
/*      */ import javax.servlet.http.HttpServletResponse;
/*      */ import weaver.common.StringUtil;
/*      */ import weaver.conn.RecordSet;
/*      */ import weaver.fna.general.FnaCommon;
/*      */ import weaver.general.BaseBean;
/*      */ import weaver.general.TimeUtil;
/*      */ import weaver.general.Util;
/*      */ import weaver.hrm.HrmUserVarify;
/*      */ import weaver.hrm.User;
/*      */ import weaver.hrm.attendance.manager.HrmAttVacationManager;
/*      */ import weaver.systeminfo.SystemEnv;
/*      */ import weaver.workflow.msg.PoppupRemindInfoUtil;
/*      */ import weaver.workflow.request.FlowExceptionHandle;
/*      */ import weaver.workflow.request.RequestAddShareInfo;
/*      */ import weaver.workflow.request.RequestComInfo;
/*      */ import weaver.workflow.request.RequestNodeFlow;
/*      */ import weaver.workflow.request.WFLinkInfo;
/*      */ import weaver.workflow.request.WFPathUtil;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ public class WfForceDrawBack
/*      */   extends BaseBean
/*      */ {
/*      */   RequestComInfo requestcominfo;
/*      */   
/*      */   public WfForceDrawBack() {
/*      */     try {
/*   42 */       this.requestcominfo = new RequestComInfo();
/*   43 */     } catch (Exception exception) {
/*   44 */       writeLog(exception);
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void doForceDrawBack(ArrayList paramArrayList, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse, int paramInt1, int paramInt2) {
/*   62 */     User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/*   63 */     doForceDrawBack(paramArrayList, user, paramInt1, paramInt2);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void doForceDrawBack(ArrayList<String> paramArrayList, User paramUser, int paramInt1, int paramInt2) {
/*   80 */     RecordSet recordSet1 = new RecordSet();
/*   81 */     RecordSet recordSet2 = new RecordSet();
/*   82 */     RecordSet recordSet3 = new RecordSet();
/*   83 */     RecordSet recordSet4 = new RecordSet();
/*   84 */     RecordSet recordSet5 = new RecordSet();
/*      */     
/*   86 */     String str1 = "";
/*   87 */     int i = -1;
/*   88 */     int j = -1;
/*   89 */     String str2 = "";
/*   90 */     int k = -1;
/*   91 */     int m = -1;
/*   92 */     int n = -1;
/*   93 */     int i1 = -1;
/*   94 */     int i2 = -1;
/*   95 */     int i3 = -1;
/*   96 */     int i4 = -1;
/*   97 */     int i5 = -1;
/*   98 */     int i6 = -1;
/*   99 */     int i7 = 0;
/*  100 */     int i8 = 0;
/*  101 */     int i9 = paramUser.getUID();
/*  102 */     PoppupRemindInfoUtil poppupRemindInfoUtil = new PoppupRemindInfoUtil();
/*  103 */     int i10 = paramUser.getLogintype().equals("1") ? 0 : 1;
/*  104 */     if (paramInt1 != -1) {
/*  105 */       i9 = paramInt1;
/*  106 */       i10 = paramInt2;
/*      */     } 
/*  108 */     String str3 = "";
/*  109 */     for (byte b1 = 0; b1 < paramArrayList.size(); b1++) {
/*  110 */       str1 = paramArrayList.get(b1);
/*  111 */       recordSet1.executeProc("workflow_Requestbase_SByID", str1 + "");
/*      */       
/*  113 */       if (recordSet1.next()) {
/*  114 */         i = recordSet1.getInt("currentnodeid");
/*  115 */         i1 = recordSet1.getInt("workflowid");
/*      */         
/*  117 */         i2 = Util.getIntValue(recordSet1.getString("creater"), 0);
/*  118 */         i3 = Util.getIntValue(recordSet1.getString("creatertype"), 0);
/*      */         
/*  120 */         j = Util.getIntValue(recordSet1.getString("currentnodetype"), 0);
/*      */       } 
/*      */ 
/*      */       
/*  124 */       recordSet1.executeSql(" update workflow_requestbase set dataaggregated = '' where requestid = " + str1);
/*      */       
/*  126 */       boolean bool1 = false;
/*  127 */       recordSet1.executeSql("select 1 from workflow_requestlog where (logtype='e' or logtype='i') and requestid=" + str1);
/*  128 */       if (recordSet1.getCounts() > 0) {
/*  129 */         bool1 = true;
/*      */       }
/*      */       
/*  132 */       if (paramInt1 != -1) {
/*      */         
/*  134 */         recordSet1.executeSql("select max(logid), operator,operatortype from workflow_requestlog where requestid=" + str1 + " and (logtype='2' or logtype='0' or logtype='3') and exists(select 1 from workflow_currentoperator where requestid=workflow_requestlog.requestid and userid=workflow_requestlog.operator and usertype=workflow_requestlog.operatortype and isremark='2' and preisremark='0' and operatedate is not null and operatedate>' ') group by operator,operatortype order by max(logid) desc");
/*  135 */         if (recordSet1.next() && !bool1) {
/*  136 */           i9 = Util.getIntValue(recordSet1.getString("operator"));
/*  137 */           i10 = Util.getIntValue(recordSet1.getString("operatortype"), 0);
/*      */         } else {
/*  139 */           str3 = "select userid,usertype,nodeid,groupid,groupdetailid from workflow_currentoperator where requestid = " + str1 + " and isremark = '2' and preisremark = '0'  and operatedate is not null and operatedate>' ' order by operatedate desc ,operatetime desc ";
/*      */ 
/*      */ 
/*      */ 
/*      */           
/*  144 */           if (bool1) {
/*  145 */             str3 = "select userid,usertype,nodeid,groupid,groupdetailid from workflow_currentoperator where requestid = " + str1 + " and isremark = '2' and preisremark = '0'  order by id desc ";
/*      */           }
/*      */ 
/*      */ 
/*      */ 
/*      */           
/*  151 */           recordSet1.executeSql(str3);
/*  152 */           if (recordSet1.next()) {
/*  153 */             i9 = Util.getIntValue(recordSet1.getString("userid"));
/*  154 */             i10 = Util.getIntValue(recordSet1.getString("usertype"), 0);
/*      */           } 
/*      */         } 
/*      */       } 
/*      */       
/*  159 */       if ("ORACLE".equalsIgnoreCase(recordSet1.getDBType())) {
/*  160 */         str3 = "select userid,usertype,nodeid,groupid,groupdetailid from workflow_currentoperator where requestid = " + str1 + "and isremark = '2' and (preisremark = '0' or preisremark = '7')  and userid=" + i9 + " and usertype=" + i10 + " and operatedate is not null  order by operatedate desc ,operatetime desc ";
/*      */ 
/*      */ 
/*      */       
/*      */       }
/*      */       else {
/*      */ 
/*      */ 
/*      */ 
/*      */         
/*  170 */         str3 = "select userid,usertype,nodeid,groupid,groupdetailid from workflow_currentoperator where requestid = " + str1 + " and isremark = '2' and (preisremark = '0' or preisremark = '7')  and userid=" + i9 + " and usertype=" + i10 + " and (operatedate is not null and operatedate<>'') order by operatedate desc ,operatetime desc ";
/*      */       } 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*  181 */       if (bool1) {
/*  182 */         str3 = "select userid,usertype,nodeid,groupid,groupdetailid from workflow_currentoperator where requestid = " + str1 + " and isremark = '2' and (preisremark = '0' or preisremark = '7')  and userid=" + i9 + " and usertype=" + i10 + " order by id desc ";
/*      */       }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*  193 */       recordSet1.executeSql(str3);
/*  194 */       if (recordSet1.next()) {
/*  195 */         i4 = recordSet1.getInt("nodeid");
/*  196 */         i6 = recordSet1.getInt("groupid");
/*  197 */         i9 = recordSet1.getInt("userid");
/*  198 */         i10 = recordSet1.getInt("usertype");
/*  199 */         i8 = recordSet1.getInt("groupdetailid");
/*      */       } 
/*      */       
/*  202 */       recordSet1.executeSql("select nodetype from workflow_flownode where workflowid = " + i1 + " and nodeid = " + i4);
/*  203 */       if (recordSet1.next()) {
/*  204 */         i5 = recordSet1.getInt("nodetype");
/*      */       }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*  211 */       str3 = "select * from workflow_requestlog where logtype!='7' and logtype!='9' and logtype!='1' and logtype!='a' and requestid = " + str1 + "  order by operatedate desc ,operatetime desc ";
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*  218 */       recordSet1.executeSql(str3);
/*  219 */       if (recordSet1.next()) {
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */         
/*  240 */         str3 = "update workflow_requestlog set logtype='1' where ((logtype!='0' and logtype!='2' and logtype!='3') or (logid in(select max(logid) from workflow_requestlog where (logtype='0' or logtype='2' or logtype='3') and requestid=" + str1 + " and nodeid = " + recordSet1.getString("nodeid") + " and operator = " + recordSet1.getString("operator") + " and operatortype = " + recordSet1.getString("operatortype") + " and destnodeid = " + recordSet1.getString("destnodeid") + "))) and requestid = " + str1 + " and nodeid = " + recordSet1.getString("nodeid") + " and operator = " + recordSet1.getString("operator") + " and operatortype = " + recordSet1.getString("operatortype") + " and destnodeid = " + recordSet1.getString("destnodeid");
/*      */         
/*  242 */         recordSet2.executeSql(str3);
/*      */       } 
/*      */       
/*  245 */       String str5 = "";
/*  246 */       String str6 = "";
/*  247 */       String str7 = "";
/*  248 */       String str8 = "";
/*  249 */       String str9 = "";
/*  250 */       String str10 = "";
/*  251 */       String str11 = "";
/*  252 */       String str12 = "";
/*  253 */       String str13 = "";
/*  254 */       String str14 = "";
/*  255 */       String str15 = "";
/*      */       
/*  257 */       String str16 = SystemEnv.getHtmlLabelName(18359, paramUser.getLanguage());
/*  258 */       String str17 = "";
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*  263 */       String str18 = "";
/*  264 */       String str19 = "";
/*  265 */       int i11 = 0;
/*  266 */       recordSet1.executeSql("select currentnodeid from workflow_requestbase where requestid = " + str1);
/*  267 */       while (recordSet1.next()) {
/*  268 */         str18 = Util.null2String(recordSet1.getString("currentnodeid"));
/*      */       }
/*  270 */       String str20 = "";
/*  271 */       String str21 = "";
/*  272 */       recordSet1.executeSql("select userid from workflow_currentoperator where requestid = " + str1 + " and nodeid = " + str18 + " and isremark = '0' ");
/*  273 */       while (recordSet1.next()) {
/*  274 */         str21 = Util.null2String(recordSet1.getString("userid"));
/*  275 */         if ("".equals(str20)) {
/*  276 */           str20 = str21; continue;
/*      */         } 
/*  278 */         str20 = str20 + "," + str21;
/*      */       } 
/*      */ 
/*      */       
/*  282 */       if (i4 == i) {
/*  283 */         recordSet1.execute("select * from workflow_nodegroup where nodeid=" + i);
/*  284 */         while (recordSet1.next()) {
/*      */           
/*  286 */           str15 = "select * from workflow_groupdetail where groupid = " + recordSet1.getString("id");
/*  287 */           recordSet2.execute(str15);
/*  288 */           while (recordSet2.next()) {
/*      */             
/*  290 */             i11 = recordSet2.getInt("type");
/*  291 */             str19 = Util.null2String(Integer.valueOf(recordSet2.getInt("signorder")));
/*      */           } 
/*      */         } 
/*      */         
/*  295 */         String str = " delete Workflow_SharedScope where requestid = " + str1 + " and operator in( " + str20 + ") and currentnodeid = " + str18;
/*      */ 
/*      */         
/*  298 */         if ((i11 != 5 && i11 != 50 && i11 != 42 && i11 != 51 && i11 != 6 && i11 != 31 && i11 != 32 && i11 != 7 && i11 != 38 && i11 != 40 && i11 != 41 && i11 != 17 && i11 != 18 && i11 != 36 && i11 != 37 && i11 != 19 && i11 != 39 && i11 != 15 && i11 != 8 && i11 != 33 && i11 != 9 && i11 != 10 && i11 != 47 && i11 != 34 && i11 != 11 && i11 != 12 && i11 != 48 && i11 != 13 && i11 != 35 && i11 != 14 && i11 != 44 && i11 != 45 && i11 != 46 && i11 != 16 && i11 != 43 && i11 != 49) || 
/*      */           
/*  300 */           !"1".equals(str19)) {
/*  301 */           recordSet3.executeSql(str);
/*      */         }
/*      */       } else {
/*      */         
/*  305 */         String str = " delete Workflow_SharedScope where requestid = " + str1 + " and operator in( " + str20 + ") and currentnodeid = " + str18;
/*      */ 
/*      */         
/*  308 */         recordSet3.executeSql(str);
/*      */       } 
/*      */ 
/*      */       
/*  312 */       if (i4 == i) {
/*      */         
/*  314 */         int i12 = -1;
/*  315 */         String str22 = "";
/*  316 */         recordSet1.executeSql("SELECT IsFreeNode, operators FROM workflow_nodebase WHERE id=" + i4);
/*  317 */         if (recordSet1.next()) {
/*  318 */           i12 = Util.getIntValue(Util.null2String(recordSet1.getString("IsFreeNode")));
/*  319 */           str22 = Util.null2String(recordSet1.getString("operators"));
/*      */         } 
/*      */ 
/*      */         
/*  323 */         boolean bool2 = true;
/*  324 */         recordSet1.execute("select * from workflow_nodegroup where nodeid=" + i4);
/*  325 */         while (recordSet1.next()) {
/*  326 */           str15 = "select * from workflow_groupdetail where groupid = " + recordSet1.getString("id");
/*  327 */           recordSet2.execute(str15);
/*  328 */           while (recordSet2.next()) {
/*  329 */             int i15 = recordSet2.getInt("type");
/*  330 */             int i16 = recordSet2.getInt("signorder");
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */             
/*  337 */             if (WFPathUtil.isContinuousProcessing(i15) && i16 == 2) {
/*  338 */               bool2 = false;
/*      */               
/*  340 */               String str24 = "";
/*  341 */               String str25 = "";
/*  342 */               recordSet3.execute("select * from workflow_base where id=" + i1);
/*  343 */               if (recordSet3.next()) {
/*  344 */                 m = recordSet3.getInt("isbill");
/*  345 */                 k = recordSet3.getInt("formid");
/*      */               } 
/*  347 */               if (m == 1) {
/*  348 */                 recordSet3.executeSql("select tablename from workflow_bill where id = " + k);
/*  349 */                 if (recordSet3.next())
/*  350 */                   str2 = recordSet3.getString("tablename"); 
/*      */               } 
/*  352 */               if (m == 0) {
/*  353 */                 recordSet3.executeSql("select fieldname from workflow_formdict where id =" + recordSet2.getInt("objid"));
/*      */               } else {
/*  355 */                 recordSet3.executeSql("select fieldname from workflow_billfield where id =" + recordSet2.getInt("objid"));
/*      */               } 
/*  357 */               if (recordSet3.next()) str24 = recordSet3.getString("fieldname");
/*      */ 
/*      */               
/*  360 */               if (i12 == 1 && !"".equals(str22)) {
/*  361 */                 str25 = str22;
/*  362 */               } else if (!"".equals(str24)) {
/*  363 */                 if (m == 0) {
/*  364 */                   recordSet3.executeSql("select " + str24 + " from workflow_form where requestid=" + str1);
/*      */                 } else {
/*  366 */                   recordSet3.executeSql("select " + str24 + " from " + str2 + " where id = (select billid from workflow_form where requestid=" + str1 + ")");
/*      */                 } 
/*  368 */                 if (recordSet3.next()) str25 = Util.null2String(recordSet3.getString(str24));
/*      */               
/*      */               } 
/*      */ 
/*      */ 
/*      */               
/*  374 */               if (i15 == 99) {
/*  375 */                 RecordSet recordSet = new RecordSet();
/*  376 */                 RequestNodeFlow requestNodeFlow = new RequestNodeFlow();
/*  377 */                 requestNodeFlow.setCreaterid(i2);
/*  378 */                 requestNodeFlow.setIsbill(m);
/*  379 */                 requestNodeFlow.setRequestid(Integer.valueOf(str1).intValue());
/*  380 */                 requestNodeFlow.setBilltablename(str2);
/*  381 */                 if (m == 1) {
/*  382 */                   recordSet.executeSql("select billid from workflow_form where requestid=" + str1);
/*  383 */                   if (recordSet.next()) {
/*  384 */                     n = recordSet.getInt("billid");
/*      */                   }
/*      */                 } 
/*  387 */                 requestNodeFlow.setBillid(n);
/*  388 */                 str25 = requestNodeFlow.getUseridsByMatrix(recordSet, recordSet2.getString("id"));
/*      */               } 
/*  390 */               if (drawBack_OrderManager(i1, i6, i, i4, i9, str1, str25))
/*      */                 return; 
/*      */             } 
/*      */           } 
/*      */         } 
/*  395 */         if (bool2) {
/*  396 */           FlowExceptionHandle flowExceptionHandle = new FlowExceptionHandle();
/*  397 */           String str = flowExceptionHandle.getExceptionOrderManagerOperator(Util.getIntValue(str1), i);
/*  398 */           if (!"".equals(str) && 
/*  399 */             drawBack_OrderManager(i1, i6, i, i4, i9, str1, str)) {
/*      */             return;
/*      */           }
/*      */         } 
/*      */ 
/*      */         
/*  405 */         ArrayList<String> arrayList1 = new ArrayList();
/*  406 */         ArrayList<String> arrayList2 = new ArrayList();
/*  407 */         ArrayList<String> arrayList3 = new ArrayList();
/*  408 */         ArrayList<String> arrayList4 = new ArrayList();
/*  409 */         RecordSet recordSet7 = new RecordSet();
/*  410 */         RecordSet recordSet8 = new RecordSet();
/*  411 */         RecordSet recordSet9 = new RecordSet();
/*  412 */         boolean bool3 = false;
/*  413 */         int i13 = -1;
/*  414 */         int i14 = -1;
/*  415 */         String str23 = "select userid, id,preisremark,groupdetailid,handleforwardid from workflow_currentoperator where preisremark = '0' and handleforwardid > 0 and requestid = " + str1 + " and groupid = " + i6 + " and nodeid = " + i + " and userid=" + i9 + " order by id desc";
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */         
/*  424 */         recordSet7.execute(str23);
/*  425 */         if (recordSet7.next()) {
/*  426 */           bool3 = true;
/*  427 */           i13 = Util.getIntValue(recordSet7.getString("handleforwardid"));
/*      */         } 
/*  429 */         if (bool3) {
/*  430 */           str3 = "select userid, id,preisremark,groupdetailid from workflow_currentoperator where preisremark = '0' and id <> " + i13 + " and requestid = " + str1 + " and groupid = " + i6 + " and nodeid = " + i + " order by id desc";
/*      */ 
/*      */         
/*      */         }
/*      */         else {
/*      */ 
/*      */           
/*  437 */           str3 = "select handleforwardid,userid, id,preisremark,groupdetailid from workflow_currentoperator where preisremark = '0' and requestid = " + str1 + " and groupid = " + i6 + " and nodeid = " + i + " and userid=" + i9 + "  order by id desc";
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */           
/*  445 */           recordSet8.execute(str3);
/*  446 */           if (recordSet8.next()) {
/*  447 */             int i15 = Util.getIntValue(recordSet8.getString("id"));
/*  448 */             str3 = "select handleforwardid,userid, id,preisremark,groupdetailid from workflow_currentoperator where preisremark = '0' and requestid = " + str1 + " and groupid = " + i6 + " and nodeid = " + i + " and handleforwardid=" + i15 + "  order by id desc";
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */             
/*  456 */             recordSet9.execute(str3);
/*  457 */             if (recordSet9.next()) {
/*  458 */               i14 = Util.getIntValue(recordSet9.getString("handleforwardid"));
/*      */             }
/*      */           } 
/*      */           
/*  462 */           str3 = "select userid, id,preisremark,groupdetailid from workflow_currentoperator where preisremark = '0' and requestid = " + str1 + " and groupid = " + i6 + " and nodeid = " + i + " order by id desc";
/*      */         } 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */         
/*  476 */         recordSet1.execute(str3);
/*  477 */         while (recordSet1.next()) {
/*  478 */           int i15 = Util.getIntValue(recordSet1.getString(1));
/*  479 */           int i16 = Util.getIntValue(recordSet1.getString(2));
/*  480 */           int i17 = Util.getIntValue(recordSet1.getString(3));
/*  481 */           int i18 = Util.getIntValue(recordSet1.getString(4));
/*  482 */           int i19 = arrayList2.indexOf("" + i15);
/*  483 */           if (i19 > -1) {
/*      */             continue;
/*      */           }
/*  486 */           arrayList2.add("" + i15);
/*  487 */           arrayList1.add("" + i16);
/*  488 */           arrayList3.add("" + i17);
/*  489 */           arrayList4.add("" + i18);
/*      */         } 
/*      */         
/*  492 */         for (byte b = 0; b < arrayList1.size(); b++) {
/*  493 */           int i15 = Util.getIntValue(arrayList2.get(b));
/*  494 */           int i16 = Util.getIntValue(arrayList1.get(b));
/*  495 */           int i17 = Util.getIntValue(arrayList3.get(b));
/*  496 */           int i18 = Util.getIntValue(arrayList4.get(b));
/*  497 */           if (i17 == 7) {
/*  498 */             str3 = "update workflow_currentoperator set isremark = '7', iscomplete=0,islasttimes = 1,isprocessed=null,isreminded=null,viewtype=0, operatedate = '',operatetime='' where preisremark='7' and requestid='" + str1 + "' and nodeid='" + i + "' and groupdetailid=" + i18;
/*      */           } else {
/*  500 */             str3 = "update workflow_currentoperator set isremark = '0', iscomplete=0,islasttimes = 1,isprocessed=null,isreminded=null,viewtype=0, operatedate = '',operatetime='' where id=" + i16;
/*      */           } 
/*  502 */           recordSet1.executeSql(str3);
/*  503 */           str3 = "update workflow_currentoperator set islasttimes = 0 where id<>" + i16 + " and userid=" + i15 + " and preisremark = '0' and requestid = " + str1 + " and groupid = " + i6 + " and nodeid = " + i;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */           
/*  509 */           recordSet1.executeSql(str3);
/*      */         } 
/*      */ 
/*      */         
/*  513 */         str3 = "select distinct groupid from workflow_currentoperator  where  groupdetailid>-1 and requestid = " + str1 + " and nodeid = " + i;
/*      */ 
/*      */ 
/*      */ 
/*      */         
/*  518 */         recordSet1.executeSql(str3);
/*      */         
/*  520 */         str10 = "" + recordSet1.getCounts();
/*      */         
/*  522 */         str3 = "select distinct groupid from workflow_currentoperator  where requestid = " + str1 + " and nodeid = " + i + " and isremark = '2' and groupdetailid>-1 ";
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */         
/*  528 */         recordSet1.executeSql(str3);
/*  529 */         str9 = "" + recordSet1.getCounts();
/*      */         
/*  531 */         str3 = "select operator,operatortype,operatedate,operatetime from workflow_requestlog where requestid = " + str1 + "  order by operatedate desc ,operatetime desc ";
/*      */ 
/*      */ 
/*      */         
/*  535 */         recordSet1.executeSql(str3);
/*  536 */         if (recordSet1.next()) {
/*  537 */           str5 = recordSet1.getString("operator");
/*  538 */           str6 = recordSet1.getString("operatortype");
/*  539 */           str7 = recordSet1.getString("operatedate");
/*  540 */           str8 = recordSet1.getString("operatetime");
/*      */         } 
/*  542 */         if (str5.equals("")) str5 = "" + i2; 
/*  543 */         if (str6.equals("")) str6 = "" + i3; 
/*  544 */         str3 = " update workflow_requestbase set  status = '" + str16 + "' ,passedgroups = passedgroups-1 ,totalgroups = " + str10 + " ,lastoperator = " + str5 + " ,lastoperatedate = '" + str7 + "'  ,lastoperatetime = '" + str8 + "'  ,lastoperatortype = " + str6 + " where requestid = " + str1;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */         
/*  565 */         recordSet1.executeSql(str3);
/*      */ 
/*      */         
/*  568 */         str3 = "select distinct groupid from workflow_currentoperator  where requestid = " + str1 + " and nodeid = " + i + " and isremark = '2' and userid = " + str5 + " and usertype = " + str6;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */         
/*  577 */         recordSet1.executeSql(str3);
/*  578 */         if (recordSet1.next()) {
/*  579 */           str17 = recordSet1.getString("groupid");
/*  580 */           str3 = "update workflow_currentoperator set viewtype=-2 where requestid = " + str1 + " and groupid = " + str17 + " and nodeid = " + i;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */           
/*  587 */           recordSet1.executeSql(str3);
/*      */         } 
/*      */         
/*  590 */         if (i14 > 0) {
/*  591 */           str3 = "delete from workflow_currentoperator where  preisremark = '0' and requestid = " + str1 + " and groupid = " + i6 + " and nodeid = " + i + " and handleforwardid = " + i14;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */           
/*  600 */           recordSet1.executeSql(str3);
/*      */         } 
/*      */       } else {
/*  603 */         str3 = "select  userid,usertype,wfreminduser,wfusertypes from workflow_currentoperator where requestid = " + str1 + " and nodeid = " + i + " and isremark in ('0','1','8','9','7','4') ";
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */         
/*  609 */         recordSet3.execute(str3);
/*  610 */         while (recordSet3.next()) {
/*      */ 
/*      */ 
/*      */           
/*  614 */           poppupRemindInfoUtil.updatePoppupRemindInfo(recordSet3.getInt(1), 0, "" + recordSet3.getInt(2), Integer.parseInt(str1));
/*      */           
/*  616 */           ArrayList<String> arrayList1 = Util.TokenizerString(Util.null2String(recordSet3.getString("wfreminduser")), ",");
/*  617 */           ArrayList<String> arrayList2 = Util.TokenizerString(Util.null2String(recordSet3.getString("wfusertypes")), ",");
/*  618 */           for (byte b = 0; b < arrayList1.size(); b++) {
/*  619 */             poppupRemindInfoUtil.updatePoppupRemindInfo(Util.getIntValue(arrayList1.get(b)), 10, arrayList2.get(b), Integer.parseInt(str1));
/*      */           }
/*      */         } 
/*      */ 
/*      */         
/*  624 */         str3 = "delete workflow_currentoperator where requestid = " + str1 + " and nodeid = " + i + " and isremark in ('0','1','8','9','7','4') ";
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */         
/*  630 */         recordSet1.executeSql(str3);
/*      */ 
/*      */         
/*  633 */         recordSet3.execute("select * from workflow_groupdetail where id=" + i8);
/*  634 */         recordSet3.next();
/*  635 */         int i12 = recordSet3.getInt("type");
/*  636 */         int i13 = recordSet3.getInt("signorder");
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */         
/*  644 */         if (WFPathUtil.isContinuousProcessing(i12) && i13 == 2) {
/*      */           
/*  646 */           str3 = "update workflow_currentoperator set isremark = '0', viewtype=0, operatedate = '',operatetime='' where preisremark = '0' and requestid = " + str1 + " and groupdetailid = " + i8 + " and userid=" + i9 + " and nodeid = " + i4;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */           
/*  656 */           recordSet1.executeSql(str3);
/*      */         }
/*      */         else {
/*      */           
/*  660 */           ArrayList<String> arrayList1 = new ArrayList();
/*  661 */           ArrayList<String> arrayList2 = new ArrayList();
/*  662 */           ArrayList<String> arrayList3 = new ArrayList();
/*  663 */           ArrayList<String> arrayList4 = new ArrayList();
/*      */           
/*  665 */           boolean bool2 = false;
/*  666 */           RecordSet recordSet7 = new RecordSet();
/*  667 */           RecordSet recordSet8 = new RecordSet();
/*  668 */           RecordSet recordSet9 = new RecordSet();
/*  669 */           boolean bool3 = false;
/*  670 */           int i15 = -1;
/*  671 */           byte b = -1;
/*  672 */           String str = "select userid, id,preisremark,groupdetailid,handleforwardid from workflow_currentoperator where handleforwardid > 0 and requestid = " + str1 + " and groupid = " + i6 + " and nodeid = " + i4 + " and userid=" + i9;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */           
/*  680 */           recordSet7.execute(str);
/*  681 */           if (recordSet7.next()) {
/*  682 */             bool3 = true;
/*  683 */             i15 = Util.getIntValue(recordSet7.getString("handleforwardid"));
/*      */           } 
/*  685 */           if (bool3) {
/*  686 */             str3 = "select userid, id,preisremark,groupdetailid from workflow_currentoperator where id <> " + i15 + " and requestid = " + str1 + " and groupid = " + i6 + " and nodeid = " + i4;
/*      */ 
/*      */           
/*      */           }
/*      */           else {
/*      */ 
/*      */             
/*  693 */             String str22 = "-1";
/*  694 */             String str23 = "-1";
/*  695 */             str3 = "select handleforwardid,userid, id,preisremark,groupdetailid from workflow_currentoperator where preisremark = '0' and requestid = " + str1 + " and groupid = " + i6 + " and nodeid = " + i4 + " and handleforwardid>0  order by id desc";
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */             
/*  703 */             recordSet8.execute(str3);
/*  704 */             while (recordSet8.next()) {
/*  705 */               int i16 = Util.getIntValue(recordSet8.getString("handleforwardid"));
/*  706 */               str3 = "select handleforwardid,userid, id,preisremark,groupdetailid from workflow_currentoperator where preisremark = '0' and requestid = " + str1 + " and groupid = " + i6 + " and nodeid = " + i4 + " and id=" + i16 + "  order by id desc";
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */               
/*  715 */               recordSet9.execute(str3);
/*  716 */               if (recordSet9.next()) {
/*  717 */                 str22 = Util.null2String(recordSet9.getString("userid"));
/*      */               }
/*  719 */               if ("-1".equals(str23)) {
/*  720 */                 str23 = str22; continue;
/*      */               } 
/*  722 */               str23 = str23 + "," + str22;
/*      */             } 
/*      */ 
/*      */             
/*  726 */             str3 = "select userid, id,preisremark,groupdetailid from workflow_currentoperator where requestid = " + str1 + " and groupid = " + i6 + " and nodeid = " + i4 + "and userid not in ('" + str23 + "')";
/*      */           } 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */           
/*  737 */           recordSet1.execute(str3);
/*  738 */           while (recordSet1.next()) {
/*  739 */             int i16 = Util.getIntValue(recordSet1.getString(1));
/*  740 */             int i17 = Util.getIntValue(recordSet1.getString(2));
/*  741 */             int i18 = Util.getIntValue(recordSet1.getString(3));
/*  742 */             int i19 = Util.getIntValue(recordSet1.getString(4));
/*  743 */             int i20 = arrayList2.indexOf("" + i16);
/*  744 */             if (i18 == 7 && i9 == i16) bool2 = true; 
/*  745 */             if (i20 > -1) {
/*      */               continue;
/*      */             }
/*      */ 
/*      */             
/*  750 */             arrayList2.add("" + i16);
/*  751 */             arrayList1.add("" + i17);
/*  752 */             arrayList3.add("" + i18);
/*  753 */             arrayList4.add("" + i19);
/*      */           } 
/*      */           
/*  756 */           for (byte b3 = 0; b3 < arrayList1.size(); b3++) {
/*  757 */             int i16 = Util.getIntValue(arrayList2.get(b3));
/*  758 */             int i17 = Util.getIntValue(arrayList1.get(b3));
/*  759 */             int i18 = Util.getIntValue(arrayList3.get(b3));
/*  760 */             int i19 = Util.getIntValue(arrayList4.get(b3));
/*  761 */             if (i18 == 7) {
/*  762 */               str3 = "update workflow_currentoperator set isremark = '7', iscomplete=0,islasttimes = 1,isprocessed=null,isreminded=null,viewtype=0, operatedate = '',operatetime='' where preisremark='7' and requestid='" + str1 + "' and nodeid='" + i4 + "' and groupdetailid=" + i19;
/*      */             } else {
/*  764 */               if (bool2)
/*  765 */                 continue;  String str22 = "select * from workflow_currentoperator where preisremark='1' and takisremark='2' and id = " + i17;
/*  766 */               recordSet1.executeSql(str22);
/*  767 */               if (recordSet1.next()) {
/*  768 */                 str3 = "update workflow_currentoperator set isremark = '1', viewtype=0,iscomplete=0,islasttimes =1,isprocessed=null,isreminded=null, operatedate = '',operatetime='' where ((preisremark='1' and takisremark='2')) and id=" + i17;
/*      */               } else {
/*  770 */                 str3 = "update workflow_currentoperator set isremark = '0', viewtype=0,iscomplete=0,islasttimes =1,isprocessed=null,isreminded=null, operatedate = '',operatetime='' where (preisremark='0' and (takisremark is null or takisremark=0))  and id=" + i17;
/*      */               } 
/*      */             } 
/*      */             
/*  774 */             recordSet1.executeSql(str3);
/*  775 */             str3 = "update workflow_currentoperator set islasttimes = 0 where id<>" + i17 + " and userid=" + i16 + " and requestid = " + str1 + " and groupid = " + i6 + " and nodeid = " + i4;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */             
/*  781 */             recordSet1.executeSql(str3);
/*      */             
/*      */             continue;
/*      */           } 
/*      */         } 
/*      */         
/*  787 */         recordSet1.execute("select destnodeid from workflow_nodelink where wfrequestid is null and nodeid=" + i4 + " and isreject='1'");
/*  788 */         if (recordSet1.next())
/*      */         {
/*  790 */           if (recordSet1.getInt(1) == i) {
/*      */ 
/*      */ 
/*      */ 
/*      */             
/*  795 */             recordSet4.execute("update  workflow_currentoperator set isremark='0' where  preisremark = '0' and requestid=" + str1 + "  and nodeid=" + i4 + " and groupid!=" + i6 + " and userid not in (select operator from workflow_requestlog where requestid=" + str1 + " and nodeid=" + i4 + ") ");
/*      */             
/*  797 */             recordSet4.execute("select *  from workflow_currentoperator where  requestid=" + str1 + "  and nodeid=" + i4 + " and isremark='2' and groupid!=" + i6);
/*  798 */             while (recordSet4.next()) {
/*      */               
/*  800 */               boolean bool2 = false;
/*  801 */               recordSet2.execute("select * from workflow_groupdetail where id=" + recordSet4.getInt("groupdetailid"));
/*  802 */               if (recordSet2.next()) {
/*      */                 
/*  804 */                 int i15 = recordSet2.getInt("type");
/*  805 */                 int i16 = recordSet2.getInt("signorder");
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */                 
/*  812 */                 if ((i15 == 5 || i15 == 50 || i15 == 42 || i15 == 51 || i15 == 6 || i15 == 31 || i15 == 32 || i15 == 7 || i15 == 38 || i15 == 40 || i15 == 41 || i15 == 17 || i15 == 18 || i15 == 36 || i15 == 37 || i15 == 19 || i15 == 39 || i15 == 15 || i15 == 8 || i15 == 33 || i15 == 9 || i15 == 10 || i15 == 47 || i15 == 34 || i15 == 11 || i15 == 12 || i15 == 48 || i15 == 13 || i15 == 35 || i15 == 14 || i15 == 44 || i15 == 45 || i15 == 46 || i15 == 16 || i15 == 43 || i15 == 49) && i16 == 2)
/*      */                 {
/*  814 */                   bool2 = true;
/*      */                 }
/*      */               } 
/*      */               
/*  818 */               if (!bool2) {
/*  819 */                 recordSet3.execute("update workflow_currentoperator set isremark='2' where  requestid=" + str1 + "  and nodeid=" + i4 + " and groupid=" + recordSet4.getInt("groupid"));
/*      */               }
/*      */             } 
/*      */           } 
/*      */         }
/*      */ 
/*      */         
/*  826 */         str3 = "select distinct groupid from workflow_currentoperator  where groupdetailid>-1 and requestid = " + str1 + " and nodeid = " + i4;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */         
/*  832 */         recordSet1.executeSql(str3);
/*  833 */         str10 = "" + recordSet1.getCounts();
/*      */ 
/*      */         
/*  836 */         str3 = "select distinct groupid from workflow_currentoperator  where requestid = " + str1 + " and nodeid = " + i4 + " and isremark = '2' and groupdetailid>-1";
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */         
/*  843 */         recordSet1.executeSql(str3);
/*      */         
/*  845 */         i7 = recordSet1.getCounts();
/*      */ 
/*      */ 
/*      */         
/*  849 */         recordSet1.execute("select distinct groupdetailid,groupid from workflow_currentoperator where isremark = '2' and requestid=" + str1 + "  and nodeid=" + i4);
/*      */         
/*  851 */         while (recordSet1.next()) {
/*      */           
/*  853 */           recordSet3.execute("select * from workflow_groupdetail where id=" + recordSet1.getInt("groupdetailid"));
/*  854 */           if (recordSet3.next()) {
/*  855 */             int i15 = recordSet3.getInt("type");
/*  856 */             int i16 = recordSet3.getInt("signorder");
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */             
/*  863 */             if (WFPathUtil.isContinuousProcessing(i15) && i16 == 2) {
/*      */               
/*  865 */               recordSet4.execute("select * from workflow_agentpersons where requestid=" + str1 + " and (groupdetailid=" + recordSet1.getInt("groupdetailid") + " or groupdetailid is null)");
/*  866 */               recordSet5.execute("select id from workflow_currentoperator where requestid=" + str1 + " and nodeid=" + i4 + " and isremark = '0' and groupdetailid=" + recordSet1.getInt("groupdetailid"));
/*  867 */               if ((recordSet4.next() && !recordSet4.getString("receivedPersons").equals("")) || recordSet5.next()) {
/*  868 */                 i7--;
/*      */               }
/*      */             } 
/*      */           } 
/*      */         } 
/*      */ 
/*      */         
/*  875 */         str9 = "" + i7;
/*  876 */         str3 = "select * from workflow_requestlog where requestid = " + str1 + " and nodeid <> " + i4 + "  order by operatedate desc ,operatetime desc ";
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */         
/*  883 */         recordSet1.executeSql(str3);
/*  884 */         if (recordSet1.next()) {
/*  885 */           str11 = recordSet1.getString("nodeid");
/*  886 */           str3 = "select nodetype from workflow_flownode where workflowid =" + i1 + " and nodeid =" + str11;
/*      */ 
/*      */ 
/*      */ 
/*      */           
/*  891 */           recordSet1.executeSql(str3);
/*  892 */           if (recordSet1.next()) {
/*  893 */             str12 = recordSet1.getString("nodetype");
/*      */           }
/*      */         } 
/*      */         
/*  897 */         str13 = "" + i4;
/*  898 */         str14 = "" + i5;
/*      */         
/*  900 */         str3 = "select operator,operatortype,operatedate,operatetime from workflow_requestlog where requestid = " + str1 + "  order by operatedate desc ,operatetime desc ";
/*      */ 
/*      */ 
/*      */ 
/*      */         
/*  905 */         recordSet1.executeSql(str3);
/*  906 */         if (recordSet1.next()) {
/*  907 */           str5 = recordSet1.getString("operator");
/*  908 */           str6 = recordSet1.getString("operatortype");
/*  909 */           str7 = recordSet1.getString("operatedate");
/*  910 */           str8 = recordSet1.getString("operatetime");
/*      */         } 
/*      */         
/*  913 */         if (str5.equals("")) str5 = "" + i2; 
/*  914 */         if (str6.equals("")) str6 = "" + i3; 
/*  915 */         if (str11.equals("")) str11 = str13; 
/*  916 */         if (str12.equals("")) str12 = str14; 
/*  917 */         str3 = " update workflow_requestbase set  lastnodeid = " + str11 + " ,lastnodetype = '" + str12 + "' ,currentnodeid = " + str13 + " ,currentnodetype = '" + str14 + "' ,status = '" + str16 + "' ,passedgroups = " + str9 + " ,totalgroups = " + str10 + " ,lastoperator = " + str5 + " ,lastoperatedate = '" + str7 + "'  ,lastoperatetime = '" + str8 + "'  ,lastoperatortype = " + str6 + " where requestid = " + str1;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */         
/*  947 */         recordSet1.executeSql(str3);
/*      */         
/*  949 */         WFLinkInfo wFLinkInfo = new WFLinkInfo();
/*  950 */         int i14 = wFLinkInfo.getNodeAttribute(Util.getIntValue(str13));
/*  951 */         recordSet1.executeSql("delete from workflow_nownode where nownodeid =" + i + " and requestid=" + str1);
/*  952 */         recordSet1.executeSql("insert into workflow_nownode(requestid,nownodeid,nownodetype,nownodeattribute) values(" + str1 + "," + str13 + "," + str14 + "," + i14 + ")");
/*      */ 
/*      */         
/*  955 */         str3 = "select distinct groupid from workflow_currentoperator  where requestid = " + str1 + " and nodeid = " + i4 + " and isremark = '2' and userid = " + str5 + " and usertype = " + str6;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */         
/*  965 */         recordSet1.executeSql(str3);
/*  966 */         if (recordSet1.next()) {
/*  967 */           str17 = recordSet1.getString("groupid");
/*      */           
/*  969 */           str3 = "update workflow_currentoperator set viewtype=-2 where requestid = " + str1 + " and groupid = " + str17 + " and nodeid = " + i4;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */           
/*  977 */           recordSet1.executeSql(str3);
/*      */         } 
/*  979 */         if (j == 3) {
/*  980 */           str3 = "update workflow_currentoperator set iscomplete=0 where requestid = " + str1;
/*  981 */           recordSet1.executeSql(str3);
/*  982 */           str3 = "delete from  workflow_currentoperator where isremark='4' and requestid = " + str1;
/*  983 */           recordSet1.executeSql(str3);
/*      */         } 
/*      */       } 
/*      */ 
/*      */ 
/*      */       
/*  989 */       ArrayList<String> arrayList = new ArrayList();
/*      */       
/*  991 */       recordSet3.execute("select id, userid from workflow_currentoperator where requestid=" + str1);
/*  992 */       while (recordSet3.next()) {
/*      */         
/*  994 */         String str22 = recordSet3.getString("userid");
/*  995 */         String str23 = recordSet3.getString("id");
/*  996 */         if (arrayList.indexOf(str22) > -1) {
/*      */           continue;
/*      */         }
/*      */         
/* 1000 */         recordSet1.execute("select  userid from workflow_currentoperator where requestid=" + str1 + " and islasttimes=1 and  userid=" + str22);
/* 1001 */         if (!recordSet1.next()) {
/*      */           
/* 1003 */           arrayList.add(str22);
/* 1004 */           recordSet1.execute("update  workflow_currentoperator set islasttimes=1 where requestid=" + str1 + " and userid=" + str22 + " and id=" + str23);
/*      */         } 
/*      */       } 
/*      */ 
/*      */ 
/*      */       
/* 1010 */       recordSet3.execute("select * from workflow_base where id=" + i1);
/* 1011 */       if (recordSet3.next()) {
/*      */         
/* 1013 */         m = recordSet3.getInt("isbill");
/* 1014 */         k = recordSet3.getInt("formid");
/*      */       } 
/*      */       
/* 1017 */       if (m == 1) {
/* 1018 */         recordSet3.executeSql("select tablename from workflow_bill where id = " + k);
/* 1019 */         if (recordSet3.next())
/* 1020 */           str2 = recordSet3.getString("tablename"); 
/*      */       } 
/*      */       try {
/* 1023 */         RequestAddShareInfo requestAddShareInfo = new RequestAddShareInfo();
/* 1024 */         requestAddShareInfo.setRequestid(Util.getIntValue(str1));
/* 1025 */         requestAddShareInfo.SetWorkFlowID(i1);
/* 1026 */         requestAddShareInfo.SetNowNodeID(i);
/* 1027 */         if (i4 == 0) {
/* 1028 */           requestAddShareInfo.SetNextNodeID(i);
/*      */         } else {
/* 1030 */           requestAddShareInfo.SetNextNodeID(i4);
/* 1031 */         }  requestAddShareInfo.setIsbill(m);
/* 1032 */         User user = new User();
/* 1033 */         user.setUid(-1);
/* 1034 */         user.setLogintype("1");
/* 1035 */         requestAddShareInfo.setUser(user);
/* 1036 */         requestAddShareInfo.SetIsWorkFlow(1);
/* 1037 */         requestAddShareInfo.setBillTableName(str2);
/* 1038 */         requestAddShareInfo.setHaspassnode(true);
/*      */         
/* 1040 */         requestAddShareInfo.addShareInfo();
/*      */       }
/* 1042 */       catch (Exception exception) {}
/*      */ 
/*      */ 
/*      */       
/*      */       try {
/* 1047 */         CapitalUnfreeze(str1, j, i5);
/* 1048 */       } catch (Exception exception) {
/* 1049 */         writeLog(exception);
/*      */       } 
/*      */     } 
/*      */     
/* 1053 */     RecordSet recordSet6 = new RecordSet();
/* 1054 */     HrmAttVacationManager hrmAttVacationManager = new HrmAttVacationManager();
/*      */     
/* 1056 */     String str4 = (new BaseBean()).getPropValue("WorkflowOvertimeIsNew", "isNew");
/* 1057 */     boolean bool = "1".equals(str4);
/* 1058 */     for (byte b2 = 0; b2 < paramArrayList.size(); b2++) {
/* 1059 */       str1 = paramArrayList.get(b2);
/*      */       
/* 1061 */       int i11 = 0;
/* 1062 */       int i12 = 0;
/* 1063 */       int i13 = 0;
/* 1064 */       recordSet6.executeProc("workflow_Requestbase_SByID", str1 + "");
/*      */       
/* 1066 */       if (recordSet6.next()) {
/* 1067 */         i11 = recordSet6.getInt("workflowid");
/* 1068 */         i12 = Util.getIntValue(recordSet6.getString("currentnodetype"), 0);
/*      */       } 
/*      */       
/* 1071 */       recordSet6.executeSql("select formid from workflow_base where id = " + i11);
/* 1072 */       if (recordSet6.next()) {
/* 1073 */         i13 = recordSet6.getInt("formid");
/*      */       }
/*      */       
/* 1076 */       if (i12 == 0) {
/*      */         
/* 1078 */         hrmAttVacationManager.handle(StringUtil.parseToInt(str1), i11, 0);
/*      */         try {
/* 1080 */           FnaCommon fnaCommon = new FnaCommon();
/* 1081 */           fnaCommon.doWfForceOver(Util.getIntValue(str1, 0), 0, true);
/* 1082 */         } catch (Exception exception) {
/* 1083 */           (new BaseBean()).writeLog(exception);
/*      */         } 
/*      */         
/*      */         try {
/* 1087 */           (new KQFlowActiontBiz()).delTest(Util.getIntValue(str1, 0), i11 + "", "WfForceDrawBack doForceDrawBack");
/* 1088 */         } catch (Exception exception) {
/* 1089 */           (new BaseBean()).writeLog(exception);
/*      */         } 
/*      */       } 
/* 1092 */       if (bool) {
/* 1093 */         OvertimeBiz.getInstance().addOvertimeTaskThread(Util.getIntValue(str1, 0), i11, 0);
/*      */       }
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private boolean drawBack_OrderManager(int paramInt1, int paramInt2, int paramInt3, int paramInt4, int paramInt5, String paramString1, String paramString2) {
/* 1103 */     RecordSet recordSet1 = new RecordSet();
/* 1104 */     RecordSet recordSet2 = new RecordSet();
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/* 1114 */     String str1 = "";
/* 1115 */     RecordSet recordSet3 = new RecordSet();
/* 1116 */     boolean bool = false;
/* 1117 */     int i = -1;
/* 1118 */     String str2 = "select  handleforwardid,userid,usertype,wfreminduser,wfusertypes, agentorbyagentid, agenttype, groupdetailid from workflow_currentoperator where preisremark = '0' and handleforwardid > 0 and requestid = " + paramString1 + " and groupid = " + paramInt2 + " and nodeid = " + paramInt3 + " and userid=" + paramInt5;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/* 1127 */     recordSet3.execute(str2);
/* 1128 */     if (recordSet3.next()) {
/* 1129 */       bool = true;
/* 1130 */       i = Util.getIntValue(recordSet3.getString("handleforwardid"));
/*      */     } 
/* 1132 */     if (bool) {
/* 1133 */       str1 = "select  userid,usertype,wfreminduser,wfusertypes, agentorbyagentid, agenttype, groupdetailid from workflow_currentoperator where isremark = '0' and id <> " + i + " and requestid = " + paramString1 + " and groupid = " + paramInt2 + " and nodeid = " + paramInt3;
/*      */ 
/*      */     
/*      */     }
/*      */     else {
/*      */ 
/*      */       
/* 1140 */       str1 = "select  userid,usertype,wfreminduser,wfusertypes, agentorbyagentid, agenttype, groupdetailid from workflow_currentoperator where isremark = '0' and requestid = " + paramString1 + " and groupid = " + paramInt2 + " and nodeid = " + paramInt3;
/*      */     } 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/* 1148 */     recordSet1.execute(str1);
/* 1149 */     if (recordSet1.next()) {
/* 1150 */       int j = Util.getIntValue(recordSet1.getString(1), 0);
/* 1151 */       int k = Util.getIntValue(recordSet1.getString("agenttype"), 0);
/* 1152 */       int m = Util.getIntValue(recordSet1.getString("agentorbyagentid"), 0);
/* 1153 */       int n = Util.getIntValue(recordSet1.getString("groupdetailid"), 0);
/* 1154 */       if (k == 2) {
/* 1155 */         j = m;
/*      */       }
/*      */ 
/*      */       
/* 1159 */       int i1 = paramInt5;
/*      */       
/*      */       try {
/* 1162 */         String str = "select agentorbyagentid from workflow_currentoperator ";
/* 1163 */         str = str + " where requestid=" + paramString1 + " and nodeid = " + paramInt3;
/* 1164 */         str = str + "   and userid =" + paramInt5 + " and isremark = '2' ";
/* 1165 */         str = str + "   and agenttype = '2' and groupid = " + paramInt2;
/* 1166 */         recordSet2.executeSql(str);
/* 1167 */         if (recordSet2.next()) {
/* 1168 */           i1 = recordSet2.getInt("agentorbyagentid");
/*      */         }
/*      */       }
/* 1171 */       catch (Exception exception) {}
/*      */ 
/*      */       
/* 1174 */       if ((i < 0 && paramString2.indexOf("" + i1) > -1 && paramString2.indexOf("" + j) > -1) || (i > 0 && paramString2.indexOf("" + j) > -1)) {
/*      */         
/* 1176 */         if (k == 2) {
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */           
/* 1184 */           str1 = "delete  from workflow_currentoperator  where requestid = " + paramString1 + " and groupid = " + paramInt2 + " and nodeid = " + paramInt3 + " and ((userid=" + recordSet1.getInt(1) + " and agenttype='2') or (userid=" + j + " and agenttype='1'))";
/*      */ 
/*      */ 
/*      */         
/*      */         }
/*      */         else {
/*      */ 
/*      */ 
/*      */ 
/*      */           
/* 1194 */           str1 = "delete  from workflow_currentoperator  where requestid = " + paramString1 + " and groupid = " + paramInt2 + " and nodeid = " + paramInt3 + " and userid=" + recordSet1.getInt(1);
/*      */         } 
/*      */         
/* 1197 */         PoppupRemindInfoUtil poppupRemindInfoUtil = new PoppupRemindInfoUtil();
/* 1198 */         poppupRemindInfoUtil.updatePoppupRemindInfo(recordSet1.getInt(1), 0, "" + recordSet1.getInt(2), Integer.parseInt(paramString1));
/*      */         
/* 1200 */         ArrayList<String> arrayList1 = Util.TokenizerString(Util.null2String(recordSet1.getString("wfreminduser")), ",");
/* 1201 */         ArrayList<String> arrayList2 = Util.TokenizerString(Util.null2String(recordSet1.getString("wfusertypes")), ","); int i2;
/* 1202 */         for (i2 = 0; i2 < arrayList1.size(); i2++) {
/* 1203 */           poppupRemindInfoUtil.updatePoppupRemindInfo(Util.getIntValue(arrayList1.get(i2)), 10, arrayList2.get(i2), Integer.parseInt(paramString1));
/*      */         }
/* 1205 */         recordSet2.executeSql(str1);
/*      */ 
/*      */         
/* 1208 */         str1 = "select max(id) as maxid from workflow_currentoperator where preisremark = '0' and requestid = " + paramString1 + " and groupid = " + paramInt2 + " and nodeid = " + paramInt3 + " and userid=" + paramInt5;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */         
/* 1217 */         recordSet2.executeSql(str1);
/* 1218 */         if (recordSet2.next()) {
/* 1219 */           i2 = Util.getIntValue(recordSet2.getString(1));
/* 1220 */           str1 = "update workflow_currentoperator set isremark = '0', viewtype=0, islasttimes = 1,isprocessed=null,isreminded=null,operatedate = '',operatetime='' where id= " + i2;
/* 1221 */           recordSet2.executeSql(str1);
/* 1222 */           str1 = "update workflow_currentoperator set islasttimes = 0 where id<>" + i2 + " and requestid=" + paramString1 + " and userid=" + paramInt5;
/* 1223 */           recordSet2.executeSql(str1);
/*      */         } 
/*      */ 
/*      */ 
/*      */         
/* 1228 */         recordSet2.execute("select * from workflow_agentpersons where requestid=" + paramString1 + " and groupdetailid = " + n);
/* 1229 */         i2 = 0;
/* 1230 */         if (recordSet2.next()) {
/* 1231 */           i2 = 1;
/* 1232 */           String str3 = recordSet2.getString("receivedPersons");
/* 1233 */           str3 = "" + j + "," + str3;
/*      */           
/* 1235 */           String str4 = recordSet2.getString("coadjutants");
/* 1236 */           str4 = "-1," + str4;
/* 1237 */           str1 = "update workflow_agentpersons set receivedPersons='" + str3 + "', coadjutants ='" + str4 + "' where requestid=" + paramString1 + " and groupdetailid = " + n;
/* 1238 */           recordSet2.execute(str1);
/*      */         } 
/* 1240 */         if (i2 == 0) {
/* 1241 */           recordSet2.execute("select * from workflow_agentpersons where requestid=" + paramString1);
/* 1242 */           if (recordSet2.next()) {
/* 1243 */             String str3 = recordSet2.getString("receivedPersons");
/* 1244 */             str3 = "" + j + "," + str3;
/*      */             
/* 1246 */             String str4 = recordSet2.getString("coadjutants");
/* 1247 */             str4 = "-1," + str4;
/* 1248 */             str1 = "update workflow_agentpersons set receivedPersons='" + str3 + "', coadjutants ='" + str4 + "' where requestid=" + paramString1;
/* 1249 */             recordSet2.execute(str1);
/*      */           } else {
/* 1251 */             str1 = "insert into  workflow_agentpersons (receivedPersons , requestid, groupdetailid,coadjutants) values ('" + j + "'," + paramString1 + "'," + n + ",'-1,')";
/* 1252 */             recordSet2.execute(str1);
/*      */           } 
/*      */         } 
/*      */ 
/*      */ 
/*      */         
/* 1258 */         int i3 = -1;
/* 1259 */         int i4 = -1;
/* 1260 */         String str = "";
/* 1261 */         recordSet1.execute("select * from workflow_base where id=" + paramInt1);
/* 1262 */         if (recordSet1.next()) {
/* 1263 */           i4 = recordSet1.getInt("isbill");
/* 1264 */           i3 = recordSet1.getInt("formid");
/*      */         } 
/* 1266 */         if (i4 == 1) {
/* 1267 */           recordSet1.executeSql("select tablename from workflow_bill where id = " + i3);
/* 1268 */           if (recordSet1.next()) {
/* 1269 */             str = recordSet1.getString("tablename");
/*      */           }
/*      */         } 
/*      */         try {
/* 1273 */           RequestAddShareInfo requestAddShareInfo = new RequestAddShareInfo();
/* 1274 */           requestAddShareInfo.setRequestid(Util.getIntValue(paramString1));
/* 1275 */           requestAddShareInfo.SetWorkFlowID(paramInt1);
/* 1276 */           requestAddShareInfo.SetNowNodeID(paramInt3);
/* 1277 */           if (paramInt4 == 0) {
/* 1278 */             requestAddShareInfo.SetNextNodeID(paramInt3);
/*      */           } else {
/* 1280 */             requestAddShareInfo.SetNextNodeID(paramInt4);
/* 1281 */           }  requestAddShareInfo.setIsbill(i4);
/* 1282 */           User user = new User();
/* 1283 */           user.setUid(-1);
/* 1284 */           user.setLogintype("1");
/* 1285 */           requestAddShareInfo.setUser(user);
/* 1286 */           requestAddShareInfo.SetIsWorkFlow(1);
/* 1287 */           requestAddShareInfo.setBillTableName(str);
/* 1288 */           requestAddShareInfo.setHaspassnode(true);
/*      */           
/* 1290 */           requestAddShareInfo.addShareInfo();
/* 1291 */         } catch (Exception exception) {
/* 1292 */           writeLog("ee=" + exception);
/*      */         } 
/*      */         
/* 1295 */         return true;
/*      */       } 
/*      */     } 
/*      */ 
/*      */     
/* 1300 */     return false;
/*      */   }
/*      */ 
/*      */   
/*      */   public void CapitalUnfreeze(String paramString, int paramInt1, int paramInt2) {
/* 1305 */     RecordSet recordSet1 = new RecordSet();
/* 1306 */     RecordSet recordSet2 = new RecordSet();
/* 1307 */     recordSet1.executeSql(" select r.requestid,r.workflowid,r.currentnodetype from workflow_requestbase r,workflow_base b where requestid = " + paramString + " and r.workflowid=b.id and b.formid=19 and b.isbill=1");
/* 1308 */     while (recordSet1.next()) {
/* 1309 */       String str = " select b.* from workflow_form w,bill_CptFetchDetail b where w.requestid =" + paramString + " and w.billid=b.cptfetchid";
/*      */       
/* 1311 */       recordSet2.executeSql(str);
/* 1312 */       RecordSet recordSet = new RecordSet();
/* 1313 */       while (recordSet2.next()) {
/* 1314 */         String str1 = recordSet2.getString("capitalid");
/* 1315 */         float f1 = 0.0F;
/* 1316 */         float f2 = 0.0F;
/* 1317 */         float f3 = 0.0F;
/* 1318 */         recordSet.executeSql("select number_n as old_number_n from bill_CptFetchDetail where cptfetchid = (select id from bill_CptFetchMain where requestid=" + paramString + ") and capitalid=" + str1);
/*      */         
/* 1320 */         if (recordSet.next())
/* 1321 */           f1 = recordSet.getFloat("old_number_n"); 
/* 1322 */         recordSet.executeSql("select frozennum as old_frozennum from CptCapital where id=" + str1);
/* 1323 */         if (recordSet.next())
/* 1324 */           f2 = recordSet.getFloat("old_frozennum"); 
/* 1325 */         if (paramInt2 == 0 && (paramInt1 == 1 || paramInt1 == 2)) {
/* 1326 */           f3 = f2 - f1;
/* 1327 */           recordSet.executeSql("update CptCapital set frozennum=" + f3 + " where id=" + str1); continue;
/*      */         } 
/* 1329 */         if (paramInt1 == 0 && (paramInt2 == 1 || paramInt2 == 2)) {
/* 1330 */           f3 = f2 + f1;
/* 1331 */           recordSet.executeSql("update CptCapital set frozennum=" + f3 + " where id=" + str1);
/*      */         } 
/*      */       } 
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void ForceDrawBackToCreater(ArrayList<String> paramArrayList, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse, int paramInt1, int paramInt2) {
/* 1346 */     RecordSet recordSet1 = new RecordSet();
/* 1347 */     RecordSet recordSet2 = new RecordSet();
/* 1348 */     RecordSet recordSet3 = new RecordSet();
/* 1349 */     RecordSet recordSet4 = new RecordSet();
/* 1350 */     RecordSet recordSet5 = new RecordSet();
/*      */     
/* 1352 */     String str1 = "";
/* 1353 */     User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/* 1354 */     int i = -1;
/* 1355 */     int j = 0;
/* 1356 */     String str2 = "";
/* 1357 */     int k = -1;
/* 1358 */     int m = -1;
/* 1359 */     byte b1 = -1;
/* 1360 */     int n = -1;
/* 1361 */     int i1 = -1;
/* 1362 */     int i2 = -1;
/* 1363 */     byte b2 = -1;
/* 1364 */     byte b3 = -1;
/* 1365 */     byte b4 = -1;
/* 1366 */     boolean bool1 = false;
/* 1367 */     boolean bool2 = false;
/* 1368 */     int i3 = user.getUID();
/* 1369 */     PoppupRemindInfoUtil poppupRemindInfoUtil = new PoppupRemindInfoUtil();
/* 1370 */     int i4 = user.getLogintype().equals("1") ? 0 : 1;
/* 1371 */     if (paramInt1 != -1) {
/* 1372 */       i3 = paramInt1;
/* 1373 */       i4 = paramInt2;
/*      */     } 
/* 1375 */     String str3 = "";
/* 1376 */     for (byte b5 = 0; b5 < paramArrayList.size(); b5++) {
/* 1377 */       str1 = paramArrayList.get(b5);
/* 1378 */       str3 = "select a.workflowid,a.creater,a.creatertype,b.nodeid,b.nodetype from workflow_requestbase a,workflow_flownode b where a.workflowid=b.workflowid and b.nodetype='0' and a.requestid=" + str1;
/*      */       
/* 1380 */       recordSet1.executeSql(str3);
/*      */       
/* 1382 */       if (recordSet1.next()) {
/* 1383 */         n = recordSet1.getInt("workflowid");
/* 1384 */         i1 = Util.getIntValue(recordSet1.getString("creater"), 0);
/* 1385 */         i2 = Util.getIntValue(recordSet1.getString("creatertype"), 0);
/* 1386 */         i = recordSet1.getInt("nodeid");
/* 1387 */         j = Util.getIntValue(recordSet1.getString("nodetype"), 0);
/*      */       } 
/*      */       
/* 1390 */       str3 = "select id from workflow_currentoperator where requestid=" + str1 + " and nodeid=" + i + " and userid=" + i1 + " and usertype=" + i2 + " order by id";
/*      */       
/* 1392 */       int i5 = 0;
/* 1393 */       recordSet1.executeSql(str3);
/* 1394 */       if (recordSet1.next()) {
/* 1395 */         i5 = recordSet1.getInt(1);
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */         
/* 1419 */         str3 = " update workflow_requestbase set  lastnodeid = " + i + " ,lastnodetype = '" + j + "' ,currentnodeid = " + i + " ,currentnodetype = '" + j + "' ,status = '" + SystemEnv.getHtmlLabelName(236, user.getLanguage()) + "' ,passedgroups = " + Character.MIN_VALUE + " ,totalgroups = " + Character.MIN_VALUE + " ,lastoperator = " + i3 + " ,lastoperatedate = '" + TimeUtil.getCurrentDateString() + "'  ,lastoperatetime = '" + TimeUtil.getOnlyCurrentTimeString() + "'  ,lastoperatortype = " + i4 + " where requestid = " + str1;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */         
/* 1426 */         recordSet1.executeSql(str3);
/*      */         
/* 1428 */         str3 = "update workflow_currentoperator set isremark='0',iscomplete=0, islasttimes=1,isprocessed=null,isreminded=null,viewtype=0, operatedate = '',operatetime='' where requestid =" + str1 + " and nodeid=" + i + " and userid=" + i1 + " and usertype=" + i2;
/*      */         
/* 1430 */         recordSet1.executeSql(str3);
/*      */         
/* 1432 */         str3 = "delete from workflow_requestlog where requestid=" + str1;
/*      */         
/* 1434 */         recordSet1.executeSql(str3);
/*      */         
/* 1436 */         str3 = "delete from workflow_currentoperator where requestid=" + str1 + " and id <>" + i5;
/*      */         
/* 1438 */         recordSet1.executeSql(str3);
/*      */         
/* 1440 */         poppupRemindInfoUtil.deletePoppupRemindInfo(Integer.parseInt(str1), 0);
/* 1441 */         poppupRemindInfoUtil.deletePoppupRemindInfo(Integer.parseInt(str1), 10);
/*      */         
/* 1443 */         poppupRemindInfoUtil.addPoppupRemindInfo(i1, 0, "" + i2, Util.getIntValue(str1), this.requestcominfo.getRequestname(str1 + ""));
/*      */ 
/*      */         
/* 1446 */         recordSet3.execute("select * from workflow_base where id=" + n);
/* 1447 */         if (recordSet3.next()) {
/*      */           
/* 1449 */           m = recordSet3.getInt("isbill");
/* 1450 */           k = recordSet3.getInt("formid");
/*      */         } 
/*      */         
/* 1453 */         if (m == 1) {
/* 1454 */           recordSet3.executeSql("select tablename from workflow_bill where id = " + k);
/* 1455 */           if (recordSet3.next())
/* 1456 */             str2 = recordSet3.getString("tablename"); 
/*      */         } 
/*      */         try {
/* 1459 */           RequestAddShareInfo requestAddShareInfo = new RequestAddShareInfo();
/* 1460 */           requestAddShareInfo.setRequestid(Util.getIntValue(str1));
/* 1461 */           requestAddShareInfo.SetWorkFlowID(n);
/* 1462 */           requestAddShareInfo.SetNowNodeID(i);
/* 1463 */           requestAddShareInfo.SetNextNodeID(i);
/* 1464 */           requestAddShareInfo.setIsbill(m);
/* 1465 */           requestAddShareInfo.setUser(user);
/* 1466 */           requestAddShareInfo.SetIsWorkFlow(1);
/* 1467 */           requestAddShareInfo.setBillTableName(str2);
/* 1468 */           requestAddShareInfo.setHaspassnode(false);
/*      */           
/* 1470 */           requestAddShareInfo.addShareInfo();
/*      */         } catch (Exception exception) {
/* 1472 */           exception.printStackTrace();
/*      */         } 
/*      */       } 
/*      */     } 
/* 1476 */     RecordSet recordSet6 = new RecordSet();
/* 1477 */     HrmAttVacationManager hrmAttVacationManager = new HrmAttVacationManager();
/* 1478 */     for (byte b6 = 0; b6 < paramArrayList.size(); b6++) {
/* 1479 */       str1 = paramArrayList.get(b6);
/*      */       
/* 1481 */       int i5 = 0;
/* 1482 */       int i6 = 0;
/* 1483 */       int i7 = 0;
/* 1484 */       recordSet6.executeProc("workflow_Requestbase_SByID", str1 + "");
/*      */       
/* 1486 */       if (recordSet6.next()) {
/* 1487 */         i5 = recordSet6.getInt("workflowid");
/* 1488 */         i6 = Util.getIntValue(recordSet6.getString("currentnodetype"), 0);
/*      */       } 
/*      */       
/* 1491 */       recordSet6.executeSql("select formid from workflow_base where id = " + i5);
/* 1492 */       if (recordSet6.next()) {
/* 1493 */         i7 = recordSet6.getInt("formid");
/*      */       }
/*      */       
/* 1496 */       if (i6 == 0) {
/*      */         
/* 1498 */         hrmAttVacationManager.handle(StringUtil.parseToInt(str1), i5, 1);
/*      */         try {
/* 1500 */           FnaCommon fnaCommon = new FnaCommon();
/* 1501 */           fnaCommon.doWfForceOver(Util.getIntValue(str1, 0), 0, true);
/* 1502 */         } catch (Exception exception) {
/* 1503 */           (new BaseBean()).writeLog(exception);
/*      */         } 
/*      */         
/*      */         try {
/* 1507 */           (new KQFlowActiontBiz()).delTest(Util.getIntValue(str1, 0), i5 + "", "WfForceDrawBack ForceDrawBackToCreater");
/* 1508 */         } catch (Exception exception) {
/* 1509 */           (new BaseBean()).writeLog(exception);
/*      */         } 
/*      */       } 
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean isHavePurview(int paramInt1, int paramInt2, int paramInt3, int paramInt4, int paramInt5) {
/* 1530 */     boolean bool = false;
/*      */     
/* 1532 */     RecordSet recordSet = new RecordSet();
/*      */     
/* 1534 */     int i = -1;
/* 1535 */     int j = -1;
/* 1536 */     int k = -1;
/* 1537 */     int m = -1;
/* 1538 */     String str = "";
/* 1539 */     paramInt3 = (paramInt3 == 1) ? 0 : 1;
/* 1540 */     if (paramInt4 != -1) {
/* 1541 */       paramInt2 = paramInt4;
/* 1542 */       paramInt3 = paramInt5;
/*      */     } 
/* 1544 */     boolean bool1 = false;
/* 1545 */     if (isLastOperator(paramInt1, paramInt2, paramInt3, paramInt4)) {
/* 1546 */       recordSet.executeProc("workflow_Requestbase_SByID", paramInt1 + "");
/* 1547 */       if (recordSet.next()) {
/* 1548 */         i = recordSet.getInt("workflowid");
/* 1549 */         j = recordSet.getInt("currentnodeid");
/* 1550 */         k = recordSet.getInt("currentnodetype");
/* 1551 */         m = j;
/*      */       } 
/*      */       
/* 1554 */       recordSet.executeSql("select isoverrb,isoveriv from workflow_base where id=" + i);
/* 1555 */       if (recordSet.next() && 
/* 1556 */         Util.null2String(recordSet.getString("isoverrb")).equals("1")) {
/* 1557 */         bool1 = true;
/*      */       }
/*      */ 
/*      */       
/* 1561 */       if (paramInt4 != -1) {
/* 1562 */         j = -1;
/*      */         
/* 1564 */         bool = true;
/*      */       } else {
/* 1566 */         recordSet.executeQuery("select * from workflow_function_manage where workflowid = ? and operatortype = ?", new Object[] { Integer.valueOf(i), Integer.valueOf(FreeNodeBiz.isFreeNode(j) ? -9 : j) });
/* 1567 */         if (recordSet.next()) str = recordSet.getString("retract"); 
/* 1568 */         recordSet.executeQuery("select * from workflow_currentoperator where (isremark = 0 or (isremark = 1 and takisremark = '2')) and userid = ? and requestid= ? and nodeid = ?", new Object[] { Integer.valueOf(paramInt2), Integer.valueOf(paramInt1), Integer.valueOf(j) });
/* 1569 */         boolean bool2 = false;
/* 1570 */         if (recordSet.next()) bool2 = true;
/*      */ 
/*      */ 
/*      */         
/* 1574 */         recordSet.executeQuery("select count(1) as num from workflow_requestlog where logtype <> '1' and requestid= ?", new Object[] { Integer.valueOf(paramInt1) });
/* 1575 */         if (recordSet.next() && recordSet.getInt("num") > 0) {
/* 1576 */           if (!bool1 && k == 3) {
/*      */             
/* 1578 */             bool = false;
/*      */           }
/* 1580 */           else if ("1".equals(str)) {
/* 1581 */             if (!isNextOperatorView(paramInt1, paramInt2, paramInt3, paramInt4) && !bool2) {
/* 1582 */               bool = true;
/*      */             }
/* 1584 */           } else if ("2".equals(str)) {
/* 1585 */             bool = true;
/*      */           } 
/*      */         }
/*      */       } 
/*      */     } 
/*      */ 
/*      */     
/* 1592 */     if (bool) {
/*      */       
/* 1594 */       WFLinkInfo wFLinkInfo = new WFLinkInfo();
/* 1595 */       ArrayList arrayList = wFLinkInfo.getCannotDrowBackNode(i);
/* 1596 */       if (arrayList.indexOf(Integer.valueOf(m)) > -1) {
/* 1597 */         bool = false;
/* 1598 */       } else if (RequestForceDrawBack.isOldRequest(paramInt1) != 0) {
/*      */         
/* 1600 */         bool = RequestForceDrawBack.isHavePurview(paramInt2, paramInt1, (paramInt4 != -1));
/* 1601 */       } else if (paramInt4 == -1 && RequestForceDrawBack.isOldRequest(paramInt1) == 0) {
/* 1602 */         bool = RequestForceDrawBack.isHavePurview(paramInt1, paramInt2);
/*      */       } 
/*      */     } 
/*      */ 
/*      */     
/* 1607 */     return bool;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private Map<String, String> currentOperateType(int paramInt1, int paramInt2) {
/* 1618 */     return (Map)new HashMap<>();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean isNextOperatorView(int paramInt1, int paramInt2, int paramInt3, int paramInt4) {
/* 1632 */     boolean bool = false;
/* 1633 */     RecordSet recordSet = new RecordSet();
/*      */     
/* 1635 */     int i = paramInt2;
/* 1636 */     int j = paramInt3;
/* 1637 */     String str1 = " ";
/* 1638 */     String str2 = " ";
/* 1639 */     int k = -1;
/* 1640 */     recordSet = new RecordSet();
/*      */     
/* 1642 */     int m = -1;
/* 1643 */     int n = -1;
/*      */     try {
/* 1645 */       if (paramInt4 != -1) {
/*      */         
/* 1647 */         recordSet.executeSql("select max(logid), operator,operatortype from workflow_requestlog where requestid=" + paramInt1 + " and (logtype='2' or logtype='0') and exists(select 1 from workflow_currentoperator where requestid=workflow_requestlog.requestid and userid=workflow_requestlog.operator and usertype=workflow_requestlog.operatortype and isremark='2' and preisremark='0' and operatedate is not null and operatedate>' ') group by operator,operatortype order by max(logid) desc");
/* 1648 */         if (recordSet.next()) {
/* 1649 */           int i2 = Util.getIntValue(recordSet.getString("operator"));
/* 1650 */           int i3 = Util.getIntValue(recordSet.getString("operatortype"), 0);
/* 1651 */           recordSet.executeSql("select userid,operatedate,operatetime, nodeid,takid,id from workflow_currentoperator where requestid = " + paramInt1 + " and isremark = '2'  and userid=" + i2 + " and usertype=" + i3 + " and preisremark = '0' and operatedate is not null and operatedate>' ' order by operatedate desc ,operatetime desc");
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */           
/* 1657 */           if (recordSet.next()) {
/*      */             
/* 1659 */             str1 = Util.null2String(recordSet.getString("operatedate"));
/*      */             
/* 1661 */             str2 = Util.null2String(recordSet.getString("operatetime"));
/* 1662 */             i = recordSet.getInt("userid");
/* 1663 */             k = recordSet.getInt("nodeid");
/* 1664 */             m = Util.getIntValue(recordSet.getString("takid"), -1);
/* 1665 */             n = Util.getIntValue(recordSet.getString("id"), -1);
/*      */           } 
/*      */         } else {
/* 1668 */           recordSet.executeSql("select userid,operatedate,operatetime, nodeid,takid,id from workflow_currentoperator where requestid = " + paramInt1 + " and isremark = '2'  and preisremark = '0' and operatedate is not null and operatedate>' ' order by operatedate desc ,operatetime desc");
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */           
/* 1674 */           if (recordSet.next())
/*      */           {
/* 1676 */             str1 = Util.null2String(recordSet.getString("operatedate"));
/*      */             
/* 1678 */             str2 = Util.null2String(recordSet.getString("operatetime"));
/* 1679 */             k = recordSet.getInt("nodeid");
/* 1680 */             m = Util.getIntValue(recordSet.getString("takid"), -1);
/* 1681 */             n = Util.getIntValue(recordSet.getString("id"), -1);
/*      */           }
/*      */         
/*      */         }
/*      */       
/*      */       }
/*      */       else {
/*      */         
/* 1689 */         recordSet.executeSql("select operatedate,operatetime, nodeid,takid,id from workflow_currentoperator where requestid = " + paramInt1 + " and isremark = '2'  and userid = " + i + " and usertype = " + j + " and (preisremark = '0' or takisremark ='2') and operatedate is not null and operatedate>' ' order by operatedate desc ,operatetime desc");
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */         
/* 1699 */         if (recordSet.next()) {
/*      */           
/* 1701 */           str1 = Util.null2String(recordSet.getString("operatedate"));
/*      */           
/* 1703 */           str2 = Util.null2String(recordSet.getString("operatetime"));
/* 1704 */           k = Util.getIntValue(Util.null2String(recordSet.getString("nodeid")));
/* 1705 */           m = Util.getIntValue(recordSet.getString("takid"), -1);
/* 1706 */           n = Util.getIntValue(recordSet.getString("id"), -1);
/*      */         } 
/*      */       } 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/* 1716 */       int i1 = 0;
/* 1717 */       String str3 = " select isSelectRejectNode from workflow_flownode where workflowid = (select workflowid  from workflow_requestbase where requestid = " + paramInt1 + ") and nodeid= " + k;
/* 1718 */       recordSet.executeSql(str3);
/* 1719 */       if (recordSet.next()) {
/* 1720 */         i1 = recordSet.getInt("isSelectRejectNode");
/*      */       }
/* 1722 */       String str4 = "";
/*      */       
/* 1724 */       if (i1 == 1) {
/* 1725 */         str4 = "select viewdate,viewtime from workflow_currentoperator where requestid=" + paramInt1 + " and userid!=" + i + " and (((isremark in ('0','4') or (isremark = 1 and handleforwardid in (" + n + ")))   and receivedate >= '" + str1 + "' AND receivetime >= '" + str2 + "') or id in (" + m + ")) and (viewdate>'" + str1 + "' or (viewdate='" + str1 + "' and viewtime>'" + str2 + "'))";
/*      */       
/*      */       }
/*      */       else {
/*      */ 
/*      */         
/* 1731 */         User user = User.getUser(i, paramInt3);
/* 1732 */         RequestFreeNodeFlowBiz requestFreeNodeFlowBiz = new RequestFreeNodeFlowBiz(user, paramInt1, k);
/* 1733 */         List list = FreeNodeBiz.getNextNodeIds(requestFreeNodeFlowBiz.getRootNodeGroup(), paramInt1, k);
/* 1734 */         String str = CollectionUtil.list2String(list, ",");
/* 1735 */         if ("".equals(str)) str = "-1";
/*      */         
/* 1737 */         str4 = "select viewdate,viewtime from workflow_currentoperator where requestid=" + paramInt1 + " and userid!=" + i + " and (nodeid in (select destnodeid from workflow_nodelink nl ,workflow_nodebase nd where nd.id = nl.nodeid and nodeid = " + k + " union select distinct destnodeid from workflow_penetrateLog where  requestid = " + paramInt1 + " union select distinct destnodeid from workflow_requestexception where  requestid = " + paramInt1 + " union select id as destnodeid from workflow_freenode where requestid = " + paramInt1 + " and id in (" + str + ")) or nodeid in (" + k + ")) and (((isremark in ('0','4') or (isremark = 1 and handleforwardid in (" + n + "))) and receivedate >= '" + str1 + "' AND receivetime >= '" + str2 + "') or id in (" + m + ")) and (viewdate>'" + str1 + "' or (viewdate='" + str1 + "' and viewtime>'" + str2 + "'))";
/*      */       } 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/* 1750 */       recordSet.executeSql(str4);
/* 1751 */       if (recordSet.next()) {
/* 1752 */         bool = true;
/*      */       }
/*      */     }
/* 1755 */     catch (Exception exception) {
/* 1756 */       bool = false;
/* 1757 */       writeLog(exception);
/*      */     } 
/*      */     
/* 1760 */     return bool;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean isLastOperator(int paramInt1, int paramInt2, int paramInt3, int paramInt4) {
/* 1775 */     boolean bool = false;
/* 1776 */     int i = paramInt2;
/* 1777 */     int j = paramInt3;
/* 1778 */     String str1 = " ";
/* 1779 */     String str2 = " ";
/* 1780 */     int k = -1;
/* 1781 */     int m = -1;
/* 1782 */     int n = 0;
/* 1783 */     RecordSet recordSet = new RecordSet();
/*      */     
/* 1785 */     String str3 = "select * from workflow_requestoperatelog where requestid=" + paramInt1 + " and (isinvalid is null  or isinvalid <> 1) and operatecode>0 order by id desc";
/* 1786 */     recordSet.executeQuery(str3, new Object[0]);
/* 1787 */     if (recordSet.next() && 
/* 1788 */       recordSet.getInt("operatorid") == i) {
/* 1789 */       return true;
/*      */     }
/*      */ 
/*      */ 
/*      */     
/*      */     try {
/* 1795 */       if (paramInt4 != -1) {
/* 1796 */         bool = true;
/*      */       } else {
/* 1798 */         String str = "select groupid, groupdetailid, operatedate,operatetime,nodeid from workflow_currentoperator where requestid = " + paramInt1 + " and ((isremark = '2' and preisremark = '0') or (isremark = '4' and preisremark = '0') or (isremark = '2' and preisremark = '7') or (isremark = '0' and takisremark='-2') or (isremark = '2' and takisremark ='2') or (isremark = '2' and preisremark ='1'))  and userid = " + i + " and usertype = " + j + " and operatedate is not null and operatedate>' ' order by operatedate desc ,operatetime desc";
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */         
/* 1809 */         recordSet.executeQuery(str, new Object[0]);
/* 1810 */         if (recordSet.next()) {
/* 1811 */           str1 = Util.null2String(recordSet.getString("operatedate"));
/* 1812 */           str2 = Util.null2String(recordSet.getString("operatetime"));
/* 1813 */           n = Util.getIntValue(recordSet.getString("nodeid"), 0);
/* 1814 */           k = Util.getIntValue(recordSet.getString("groupid"), 0);
/* 1815 */           m = Util.getIntValue(recordSet.getString("groupdetailid"), 0);
/*      */         } else {
/* 1817 */           return bool;
/*      */         } 
/*      */         
/* 1820 */         str = "select operatedate,operatetime from workflow_currentoperator where requestid = " + paramInt1 + " and (isremark = '2' and preisremark in('0','7')) and userid <> " + i + " and (operatedate > '" + str1 + "' or (operatedate = '" + str1 + "' and operatetime > '" + str2 + "'))";
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */         
/* 1833 */         recordSet.executeQuery(str, new Object[0]);
/* 1834 */         if (!recordSet.next()) {
/* 1835 */           bool = true;
/*      */ 
/*      */ 
/*      */ 
/*      */         
/*      */         }
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*      */       }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*      */     }
/* 1852 */     catch (Exception exception) {
/* 1853 */       bool = false;
/* 1854 */       writeLog(exception);
/*      */     } 
/*      */     
/* 1857 */     return bool;
/*      */   }
/*      */ 
/*      */   
/*      */   public boolean checkOperatorIsremark(int paramInt1, int paramInt2, int paramInt3, int paramInt4, String paramString) {
/* 1862 */     if (paramInt4 != 5 || (paramInt4 == 7 && !"2".equals(paramString))) {
/* 1863 */       return true;
/*      */     }
/* 1865 */     return false;
/*      */   }
/*      */ 
/*      */   
/*      */   public boolean checkOperatorIsremark(int paramInt1, int paramInt2, int paramInt3, int paramInt4) {
/* 1870 */     if (paramInt4 != 5 && paramInt4 != 7) {
/* 1871 */       return true;
/*      */     }
/* 1873 */     return false;
/*      */   }
/*      */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/workflow/WfForceDrawBack.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */