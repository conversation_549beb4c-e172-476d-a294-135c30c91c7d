/*     */ package weaver.workflow.workflow;
/*     */ 
/*     */ import com.engine.common.service.impl.HrmCommonServiceImpl;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class WfRightManager
/*     */ {
/*     */   public static final int OPERATION_CREATEDIR = 1;
/*     */   public static final int PERMISSIONTYPE_DEPARTMENT_SECLEVEL = 1;
/*     */   public static final int PERMISSIONTYPE_ROLE_ROLELEVEL_SECLEVEL = 2;
/*     */   public static final int PERMISSIONTYPE_SECLEVEL = 3;
/*     */   public static final int PERMISSIONTYPE_USERTYPE_SECLEVEL = 4;
/*     */   public static final int PERMISSIONTYPE_HUMANRESOURCE = 5;
/*     */   public static final int ROLELEVEL_DEPARTMENT = 0;
/*     */   public static final int ROLELEVEL_SUBCOMPANY = 1;
/*     */   public static final int ROLELEVEL_COMPANY = 2;
/*     */   
/*     */   public void grantWfPermission1(int paramInt1, int paramInt2, int paramInt3, int paramInt4, int paramInt5) {
/*  43 */     RecordSet recordSet = new RecordSet();
/*  44 */     String str = Integer.toString(paramInt1) + Util.getSeparator() + paramInt2 + Util.getSeparator() + paramInt3 + Util.getSeparator() + paramInt4 + Util.getSeparator() + paramInt5;
/*  45 */     recordSet.executeProc("Wf_Right_Insert_Type1", str);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void grantWfPermission6(int paramInt1, int paramInt2, int paramInt3, int paramInt4, int paramInt5) {
/*  57 */     RecordSet recordSet = new RecordSet();
/*  58 */     String str = Integer.toString(paramInt1) + Util.getSeparator() + paramInt2 + Util.getSeparator() + paramInt3 + Util.getSeparator() + paramInt4 + Util.getSeparator() + paramInt5;
/*  59 */     recordSet.executeProc("Wf_Right_Insert_Type6", str);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void grantWfPermission2(int paramInt1, int paramInt2, int paramInt3, int paramInt4, int paramInt5, int paramInt6) {
/*  72 */     RecordSet recordSet = new RecordSet();
/*  73 */     String str = Integer.toString(paramInt1) + Util.getSeparator() + paramInt2 + Util.getSeparator() + paramInt3 + Util.getSeparator() + paramInt4 + Util.getSeparator() + paramInt5 + Util.getSeparator() + paramInt6;
/*  74 */     recordSet.executeProc("Wf_Right_Insert_Type2", str);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void grantWfPermission3(int paramInt1, int paramInt2, int paramInt3, int paramInt4) {
/*  85 */     RecordSet recordSet = new RecordSet();
/*  86 */     String str = Integer.toString(paramInt1) + Util.getSeparator() + paramInt2 + Util.getSeparator() + paramInt3 + Util.getSeparator() + paramInt4;
/*  87 */     recordSet.executeProc("Wf_Right_Insert_Type3", str);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void grantWfPermission4(int paramInt1, int paramInt2, int paramInt3, int paramInt4, int paramInt5) {
/*  99 */     RecordSet recordSet = new RecordSet();
/* 100 */     String str = Integer.toString(paramInt1) + Util.getSeparator() + paramInt2 + Util.getSeparator() + paramInt3 + Util.getSeparator() + paramInt4 + Util.getSeparator() + paramInt5;
/* 101 */     recordSet.executeProc("Wf_Right_Insert_Type4", str);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void grantWfPermission5(int paramInt1, int paramInt2, int paramInt3, int paramInt4) {
/* 112 */     RecordSet recordSet = new RecordSet();
/* 113 */     String str = Integer.toString(paramInt1) + Util.getSeparator() + paramInt2 + Util.getSeparator() + paramInt3 + Util.getSeparator() + paramInt4;
/* 114 */     recordSet.executeProc("Wf_Right_Insert_Type5", str);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean hasPermission(int paramInt1, int paramInt2, int paramInt3, int paramInt4, int paramInt5, int paramInt6, int paramInt7, int paramInt8) {
/* 141 */     RecordSet recordSet = new RecordSet();
/*     */     
/* 143 */     recordSet.executeQuery("select templateid,istemplate from workflow_base where id = ?", new Object[] { Integer.valueOf(paramInt1) });
/* 144 */     if (recordSet.next() && 
/* 145 */       1 == recordSet.getInt(2)) {
/* 146 */       paramInt1 = recordSet.getInt(1);
/*     */     }
/*     */ 
/*     */ 
/*     */     
/* 151 */     String str1 = "";
/* 152 */     recordSet.executeQuery("select id from workflow_base where (case when activeversionid is null then id else activeversionid end) =(select (case when activeversionid is null then id else activeversionid end) from workflow_base where id = ?) ", new Object[] { Integer.valueOf(paramInt1) });
/* 153 */     while (recordSet.next()) {
/* 154 */       str1 = str1 + "," + recordSet.getString("id");
/*     */     }
/* 156 */     if ("".equals(str1)) {
/* 157 */       return false;
/*     */     }
/*     */     
/* 160 */     str1 = str1.substring(1);
/*     */     
/* 162 */     HrmCommonServiceImpl hrmCommonServiceImpl = new HrmCommonServiceImpl();
/* 163 */     String str2 = hrmCommonServiceImpl.getRoleIds(paramInt3);
/* 164 */     if ("".equals(str2)) str2 = "0"; 
/* 165 */     String str3 = "SELECT COUNT(mainid) FROM wfAccessControlList a WHERE  dirid in (" + str1 + ") AND dirtype = ? AND operationcode = ? AND ((permissiontype = 1 AND departmentid = ? AND seclevel <= ?)OR(permissiontype = 2 AND a.roleid in (" + str2 + ")) OR(permissiontype = 3 AND seclevel <= ?) OR(permissiontype = 4 AND usertype = ? AND seclevel <= ?) OR(permissiontype = 5 AND userid = ?) OR(permissiontype = 6 AND subcompanyid = ? AND seclevel <= ?)) ";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 174 */     recordSet.executeQuery(str3, new Object[] { Integer.valueOf(paramInt2), Integer.valueOf(paramInt8), Integer.valueOf(paramInt6), Integer.valueOf(paramInt5), Integer.valueOf(paramInt5), Integer.valueOf(paramInt4), Integer.valueOf(paramInt5), Integer.valueOf(paramInt3), Integer.valueOf(paramInt7), Integer.valueOf(paramInt5) });
/* 175 */     if (recordSet.next()) {
/* 176 */       return (recordSet.getInt(1) > 0);
/*     */     }
/* 178 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean hasPermission(int paramInt1, int paramInt2, int paramInt3, int paramInt4, int paramInt5, int paramInt6) {
/* 193 */     return hasPermission(paramInt1, paramInt2, getUserById(paramInt3, paramInt4, paramInt5), paramInt6);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean hasPermission(int paramInt1, int paramInt2, User paramUser, int paramInt3) {
/* 205 */     return hasPermission(paramInt1, paramInt2, paramUser.getUID(), paramUser.getType(), Integer.parseInt(paramUser.getSeclevel()), paramUser.getUserDepartment(), paramUser.getUserSubCompany1(), paramInt3);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean hasPermission3(int paramInt1, int paramInt2, User paramUser, int paramInt3) {
/* 222 */     return hasPermission(paramInt1, paramInt2, paramUser.getUID(), paramUser.getType(), Integer.parseInt(paramUser.getSeclevel()), paramUser.getUserDepartment(), paramUser.getUserSubCompany1(), paramInt3);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean hasPermission2(int paramInt1, int paramInt2, int paramInt3, int paramInt4, int paramInt5, int paramInt6, int paramInt7) {
/* 241 */     HrmCommonServiceImpl hrmCommonServiceImpl = new HrmCommonServiceImpl();
/* 242 */     String str1 = hrmCommonServiceImpl.getRoleIds(paramInt2);
/* 243 */     if ("".equals(str1)) str1 = "0"; 
/* 244 */     String str2 = "SELECT COUNT(mainid) FROM wfAccessControlList a WHERE dirtype = ? AND operationcode = ? AND ((permissiontype = 1 AND departmentid = ? AND seclevel <= ?)OR(permissiontype = 2 AND a.roleid in (" + str1 + ")) OR(permissiontype = 3 AND seclevel <= ?) OR(permissiontype = 4 AND usertype = ? AND seclevel <= ?) OR(permissiontype = 5 AND userid = ?) OR(permissiontype = 6 AND subcompanyid = ? AND seclevel <= ?)) ";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 252 */     RecordSet recordSet = new RecordSet();
/* 253 */     recordSet.executeQuery(str2, new Object[] { Integer.valueOf(paramInt1), Integer.valueOf(paramInt7), Integer.valueOf(paramInt5), Integer.valueOf(paramInt4), 
/* 254 */           Integer.valueOf(paramInt4), Integer.valueOf(paramInt3), Integer.valueOf(paramInt4), Integer.valueOf(paramInt2), Integer.valueOf(paramInt6), Integer.valueOf(paramInt4) });
/* 255 */     if (recordSet.next()) {
/* 256 */       return (recordSet.getInt(1) > 0);
/*     */     }
/* 258 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean hasPermission2(int paramInt1, User paramUser, int paramInt2) {
/* 269 */     return hasPermission2(paramInt1, paramUser.getUID(), paramUser.getType(), Integer.parseInt(paramUser.getSeclevel()), paramUser.getUserDepartment(), paramUser.getUserSubCompany1(), paramInt2);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean hasPermission2(int paramInt1, int paramInt2, int paramInt3, int paramInt4, int paramInt5) {
/* 282 */     return hasPermission2(paramInt1, getUserById(paramInt2, paramInt3, paramInt4), paramInt5);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getUserAllRoleAndRoleLevel(int paramInt) {
/* 290 */     String str = "";
/* 291 */     HrmCommonServiceImpl hrmCommonServiceImpl = new HrmCommonServiceImpl();
/* 292 */     List list = hrmCommonServiceImpl.getRoleInfo(paramInt);
/* 293 */     for (Map map1 : list) {
/* 294 */       Map map2 = map1;
/* 295 */       str = str + Util.null2String(map2.get("roleid")) + Util.null2String(map2.get("rolelevel")) + ",";
/*     */     } 
/* 297 */     if (!"".equals(str)) str = str.substring(0, str.length() - 1); 
/* 298 */     if (str.equals("")) {
/* 299 */       str = "0";
/*     */     }
/* 301 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public User getUserById(int paramInt1, int paramInt2, int paramInt3) {
/* 310 */     RecordSet recordSet = new RecordSet();
/* 311 */     User user = new User();
/* 312 */     user.setUid(paramInt1);
/* 313 */     user.setType(paramInt2);
/* 314 */     user.setSeclevel(paramInt3 + "");
/* 315 */     String str = "select departmentid,  subcompanyid1, seclevel from hrmresource where id=" + paramInt1;
/*     */     
/* 317 */     recordSet.executeSql(str);
/*     */ 
/*     */     
/* 320 */     if (recordSet.next()) {
/* 321 */       user.setUserDepartment(Util.getIntValue(recordSet.getString("departmentid"), 0));
/* 322 */       user.setUserSubCompany1(Util.getIntValue(recordSet.getString("subcompanyid1"), 0));
/*     */     } else {
/* 324 */       str = "select seclevel from HrmResourceManager where id=" + paramInt1;
/* 325 */       recordSet.executeSql(str);
/* 326 */       if (recordSet.next()) {
/* 327 */         user.setUserDepartment(0);
/* 328 */         user.setUserSubCompany1(0);
/*     */       } 
/*     */     } 
/* 331 */     return user;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void depriveDirPermission(String paramString) {
/* 339 */     String[] arrayOfString = Util.TokenizerString2(paramString, "_");
/* 340 */     if (arrayOfString.length == 2) {
/* 341 */       RecordSet recordSet = new RecordSet();
/* 342 */       if (arrayOfString[1].equals("2")) {
/* 343 */         recordSet.executeSql("delete from wfAccessControlList where roleid = " + arrayOfString[0] + " and permissiontype=2");
/*     */       } else {
/* 345 */         recordSet.executeSql("delete from wfAccessControlList where userid = " + arrayOfString[0] + " and permissiontype=" + arrayOfString[1]);
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getAllWfTypeIds(int paramInt) {
/* 358 */     RecordSet recordSet1 = new RecordSet();
/* 359 */     String str1 = "";
/* 360 */     String str2 = "";
/* 361 */     HrmCommonServiceImpl hrmCommonServiceImpl = new HrmCommonServiceImpl();
/* 362 */     List list = hrmCommonServiceImpl.getRoleInfo(paramInt);
/* 363 */     for (Map map1 : list) {
/* 364 */       Map map2 = map1;
/* 365 */       str2 = str2 + Util.null2String(map2.get("roleid")) + ",";
/*     */     } 
/*     */     
/* 368 */     if (!"".equals(str2)) str2 = str2.substring(0, str2.length() - 1); 
/* 369 */     String str3 = "select dirid from wfAccessControlList where userid = '" + paramInt + "'";
/* 370 */     if (!"".equals(str2)) str3 = str3 + "or roleid in(" + str2 + ")";
/*     */     
/* 372 */     recordSet1.execute(str3);
/* 373 */     while (recordSet1.next()) {
/* 374 */       str1 = str1 + recordSet1.getInt("dirid") + ",";
/*     */     }
/* 376 */     if (!"".equals(str1)) str1 = str1.substring(0, str1.length() - 1);
/*     */     
/* 378 */     String str4 = "";
/* 379 */     RecordSet recordSet2 = new RecordSet();
/* 380 */     if (!"".equals(str1)) {
/* 381 */       recordSet2.executeQuery("select id from workflow_base where (case when activeversionid is null then id else activeversionid end) in(select (case when activeversionid is null then id else activeversionid end) from workflow_base where id in (" + str1 + ")) ", new Object[0]);
/*     */     }
/* 383 */     while (recordSet2.next()) {
/* 384 */       str4 = str4 + "," + recordSet2.getString("id");
/*     */     }
/* 386 */     if (!"".equals(str4)) {
/* 387 */       str4 = str4.substring(1);
/*     */     }
/* 389 */     return str4;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/workflow/WfRightManager.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */