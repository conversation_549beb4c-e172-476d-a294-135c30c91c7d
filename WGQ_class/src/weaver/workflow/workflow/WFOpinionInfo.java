/*     */ package weaver.workflow.workflow;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class WFOpinionInfo
/*     */ {
/*     */   private String label_cn;
/*     */   private String label_en;
/*     */   private String label_tw;
/*     */   private String type_cn;
/*     */   private int isUse;
/*     */   private int isMust;
/*     */   private int isView;
/*     */   private int isEdit;
/*     */   private int id;
/*     */   private int orderid;
/*     */   private int nodeid;
/*     */   
/*     */   public int getNodeid() {
/*  44 */     return this.nodeid;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setNodeid(int paramInt) {
/*  52 */     this.nodeid = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getOrderid() {
/*  60 */     return this.orderid;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setOrderid(int paramInt) {
/*  68 */     this.orderid = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getId() {
/*  76 */     return this.id;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setId(int paramInt) {
/*  84 */     this.id = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getIsEdit() {
/*  92 */     return this.isEdit;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setIsEdit(int paramInt) {
/* 100 */     this.isEdit = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getIsMust() {
/* 108 */     return this.isMust;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setIsMust(int paramInt) {
/* 116 */     this.isMust = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getIsUse() {
/* 124 */     return this.isUse;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setIsUse(int paramInt) {
/* 132 */     this.isUse = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getIsView() {
/* 140 */     return this.isView;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setIsView(int paramInt) {
/* 148 */     this.isView = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getLabel_cn() {
/* 156 */     return this.label_cn;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setLabel_cn(String paramString) {
/* 164 */     this.label_cn = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getLabel_en() {
/* 172 */     return this.label_en;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setLabel_en(String paramString) {
/* 180 */     this.label_en = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getType_cn() {
/* 188 */     return this.type_cn;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setType_cn(String paramString) {
/* 196 */     this.type_cn = paramString;
/*     */   }
/*     */   
/*     */   public String getLabel_tw() {
/* 200 */     return this.label_tw;
/*     */   }
/*     */ 
/*     */   
/*     */   public void setLabel_tw(String paramString) {
/* 205 */     this.label_tw = paramString;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/workflow/WFOpinionInfo.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */