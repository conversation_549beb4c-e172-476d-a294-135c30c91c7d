/*      */ package weaver.workflow.workflow;
/*      */ 
/*      */ import com.engine.workflow.biz.DetailOrderBiz;
/*      */ import java.util.ArrayList;
/*      */ import java.util.HashMap;
/*      */ import java.util.List;
/*      */ import java.util.Map;
/*      */ import javax.servlet.http.HttpServletRequest;
/*      */ import javax.servlet.http.HttpServletResponse;
/*      */ import weaver.conn.RecordSet;
/*      */ import weaver.general.BaseBean;
/*      */ import weaver.general.Util;
/*      */ import weaver.hrm.User;
/*      */ import weaver.systeminfo.SystemEnv;
/*      */ import weaver.workflow.request.SubWorkflowTriggerService;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ public class WorkflowTriDiffWfManager
/*      */   extends BaseBean
/*      */ {
/*      */   public static List<Map<String, String>> getSubWfSetMainFieldDetails(int paramInt1, int paramInt2, int paramInt3, int paramInt4) {
/*   46 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*   47 */     RecordSet recordSet1 = new RecordSet();
/*   48 */     RecordSet recordSet2 = new RecordSet();
/*      */     
/*   50 */     String str1 = "0";
/*   51 */     String str2 = "0";
/*   52 */     recordSet1.executeSql(" select formId,isBill from workflow_base where id= " + paramInt2);
/*   53 */     if (recordSet1.next()) {
/*   54 */       str1 = recordSet1.getString(1);
/*   55 */       str2 = recordSet1.getString(2);
/*      */     } 
/*      */     
/*   58 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*      */     
/*   60 */     hashMap.put("-3", SystemEnv.getHtmlLabelName(26876, paramInt4));
/*   61 */     hashMap.put("-4", SystemEnv.getHtmlLabelName(128667, Util.getIntValue(paramInt4)));
/*   62 */     hashMap.put("-5", SystemEnv.getHtmlLabelName(17586, paramInt4));
/*      */     
/*   64 */     for (byte b = -3; b >= -5; b--) {
/*   65 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/*      */       
/*   67 */       String str = "" + b;
/*   68 */       hashMap1.put("subWorkflowFieldId", str);
/*   69 */       hashMap1.put("subWorkflowFieldName", Util.null2String((String)hashMap.get("" + b)));
/*      */ 
/*      */       
/*   72 */       recordSet2.executeSql("select id,subWorkflowFieldId,mainWorkflowFieldId,ifSplitField,isCreateDocAgain,isCreateAttachmentAgain from Workflow_TriDiffWfSubWfField where (isdetail is null or isdetail<>1) and subWorkflowFieldId=" + str + " and triDiffWfSubWfId=" + paramInt3);
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*   77 */       if (recordSet2.next()) {
/*   78 */         hashMap1.put("id", Util.null2String(recordSet2.getString("id"), ""));
/*   79 */         hashMap1.put("mainWorkflowFieldId", recordSet2.getString("mainWorkflowFieldId"));
/*   80 */         hashMap1.put("ifSplitField", recordSet2.getString("ifSplitField"));
/*   81 */         hashMap1.put("isCreateDocAgain", recordSet2.getString("isCreateDocAgain"));
/*   82 */         hashMap1.put("isCreateAttachmentAgain", recordSet2.getString("isCreateAttachmentAgain"));
/*      */       } else {
/*   84 */         hashMap1.put("id", "");
/*   85 */         hashMap1.put("mainWorkflowFieldId", "");
/*   86 */         hashMap1.put("ifSplitField", "0");
/*   87 */         hashMap1.put("isCreateDocAgain", "0");
/*   88 */         hashMap1.put("isCreateAttachmentAgain", "0");
/*      */       } 
/*      */       
/*   91 */       arrayList.add(hashMap1);
/*      */     } 
/*      */     
/*   94 */     StringBuffer stringBuffer = new StringBuffer();
/*   95 */     if ("0".equals(str2)) {
/*   96 */       stringBuffer.append(" select b.fieldid as id,c.fieldlable as name")
/*   97 */         .append(" from workflow_formfield b,workflow_fieldlable c")
/*   98 */         .append(" where c.isdefault='1' ")
/*   99 */         .append(" and c.formid = b.formid ")
/*  100 */         .append(" and c.fieldid = b.fieldid ")
/*  101 */         .append(" and (b.isdetail<>'1' or b.isdetail is null) ")
/*  102 */         .append(" and b.formid=").append(str1)
/*  103 */         .append(" order by b.fieldorder asc ");
/*      */     } else {
/*  105 */       stringBuffer.append("select wbf.id as id , wbf.fieldlabel as name")
/*  106 */         .append(" from workflow_billfield wbf")
/*  107 */         .append(" where wbf.billid=").append(str1)
/*  108 */         .append(" and (wbf.viewtype is null or wbf.viewtype<>1) ")
/*  109 */         .append(" and (wbf.fieldhtmltype != 9 or wbf.type != 1) ")
/*  110 */         .append(" order by dsporder asc ");
/*      */     } 
/*  112 */     recordSet1.executeSql(stringBuffer.toString());
/*  113 */     while (recordSet1.next()) {
/*  114 */       String str3 = recordSet1.getString(1);
/*  115 */       String str4 = "";
/*  116 */       if ("0".equals(str2)) {
/*  117 */         str4 = recordSet1.getString(2);
/*      */       } else {
/*  119 */         str4 = SystemEnv.getHtmlLabelName(recordSet1.getInt(2), paramInt4);
/*      */       } 
/*      */       
/*  122 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  123 */       hashMap1.put("subWorkflowFieldId", recordSet1.getString(1));
/*  124 */       hashMap1.put("subWorkflowFieldName", str4);
/*      */ 
/*      */       
/*  127 */       recordSet2.executeSql("select id,subWorkflowFieldId,mainWorkflowFieldId,ifSplitField,isCreateDocAgain,isCreateAttachmentAgain from Workflow_TriDiffWfSubWfField where (isdetail is null or isdetail<>1) and subWorkflowFieldId=" + str3 + " and triDiffWfSubWfId=" + paramInt3);
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*  132 */       if (recordSet2.next()) {
/*  133 */         hashMap1.put("id", Util.null2String(recordSet2.getString("id"), ""));
/*  134 */         hashMap1.put("mainWorkflowFieldId", recordSet2.getString("mainWorkflowFieldId"));
/*  135 */         hashMap1.put("ifSplitField", recordSet2.getString("ifSplitField"));
/*  136 */         hashMap1.put("isCreateDocAgain", recordSet2.getString("isCreateDocAgain"));
/*  137 */         hashMap1.put("isCreateAttachmentAgain", recordSet2.getString("isCreateAttachmentAgain"));
/*      */       } else {
/*  139 */         hashMap1.put("id", "");
/*  140 */         hashMap1.put("mainWorkflowFieldId", "");
/*  141 */         hashMap1.put("ifSplitField", "0");
/*  142 */         hashMap1.put("isCreateDocAgain", "0");
/*  143 */         hashMap1.put("isCreateAttachmentAgain", "0");
/*      */       } 
/*      */       
/*  146 */       arrayList.add(hashMap1);
/*      */     } 
/*  148 */     return (List)arrayList;
/*      */   }
/*      */ 
/*      */   
/*      */   public static List<List<Map<String, String>>> getGroupedSubWfSetDetailFieldDetails(int paramInt1, int paramInt2, int paramInt3, int paramInt4) {
/*  153 */     ArrayList<ArrayList> arrayList = new ArrayList();
/*      */     
/*  155 */     List<Map<String, String>> list = getSubWfSetDetailFieldDetails(paramInt1, paramInt2, paramInt3, paramInt4);
/*      */     
/*  157 */     String str = "";
/*  158 */     ArrayList<Map> arrayList1 = null;
/*  159 */     for (byte b = 0; b < list.size(); b++) {
/*  160 */       Map map = list.get(b);
/*      */ 
/*      */       
/*  163 */       if (!str.equals(map.get("detailGroup"))) {
/*  164 */         str = (String)map.get("detailGroup");
/*  165 */         arrayList1 = new ArrayList();
/*  166 */         arrayList.add(arrayList1);
/*      */       } 
/*      */       
/*  169 */       arrayList1.add(map);
/*      */     } 
/*      */     
/*  172 */     return (List)arrayList;
/*      */   }
/*      */ 
/*      */   
/*      */   public static List<Map<String, String>> getSubWfSetDetailFieldDetails(int paramInt1, int paramInt2, int paramInt3, int paramInt4) {
/*  177 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*  178 */     RecordSet recordSet1 = new RecordSet();
/*  179 */     RecordSet recordSet2 = new RecordSet();
/*      */     
/*  181 */     String str1 = "0";
/*  182 */     String str2 = "0";
/*  183 */     recordSet1.executeSql(" select formId,isBill from workflow_base where id= " + paramInt2);
/*  184 */     if (recordSet1.next()) {
/*  185 */       str1 = recordSet1.getString(1);
/*  186 */       str2 = recordSet1.getString(2);
/*      */     } 
/*      */     
/*  189 */     StringBuilder stringBuilder = new StringBuilder();
/*  190 */     if ("0".equals(str2)) {
/*  191 */       stringBuilder.append("select b.fieldid as id,c.fieldlable as name,b.groupid as detailtablegroup")
/*  192 */         .append(" from workflow_formfield b,workflow_fieldlable c ")
/*  193 */         .append(" where c.isdefault='1' ")
/*  194 */         .append(" and c.formid = b.formid ")
/*  195 */         .append(" and c.fieldid = b.fieldid ")
/*  196 */         .append(" and b.isdetail='1' ")
/*  197 */         .append(" and b.formid=").append(str1)
/*  198 */         .append(" order by b.groupid,b.fieldorder asc ");
/*      */     } else {
/*  200 */       stringBuilder.append("select wfbf.id as id , wfbf.fieldlabel as name,wfbf.detailtable as detailtablegroup,wbd.orderid")
/*  201 */         .append(" from workflow_billfield wfbf ")
/*  202 */         .append("      left join workflow_billdetailtable wbd on wfbf.detailtable = wbd.tablename ")
/*  203 */         .append(" where wfbf.billid=").append(str1)
/*  204 */         .append(" and wfbf.viewtype=1 ")
/*  205 */         .append(" order by wbd.orderid,wfbf.dsporder asc ");
/*      */     } 
/*      */     
/*  208 */     recordSet1.executeSql(stringBuilder.toString());
/*      */     
/*  210 */     while (recordSet1.next()) {
/*  211 */       String str3 = recordSet1.getString(1);
/*  212 */       String str4 = "";
/*  213 */       if ("0".equals(str2)) {
/*  214 */         str4 = recordSet1.getString(2);
/*      */       } else {
/*  216 */         str4 = SystemEnv.getHtmlLabelName(recordSet1.getInt(2), paramInt4);
/*      */       } 
/*      */       
/*  219 */       HashMap<Object, Object> hashMap = new HashMap<>();
/*  220 */       hashMap.put("subWorkflowFieldId", str3);
/*  221 */       hashMap.put("subWorkflowFieldName", str4);
/*  222 */       hashMap.put("detailGroup", recordSet1.getString(3));
/*      */ 
/*      */       
/*  225 */       recordSet2.executeSql("select id,subWorkflowFieldId,mainWorkflowFieldId,ifSplitField,isCreateDocAgain,isCreateAttachmentAgain from Workflow_TriDiffWfSubWfField where isdetail=1 and subWorkflowFieldId=" + str3 + " and triDiffWfSubWfId=" + paramInt3);
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*  230 */       if (recordSet2.next()) {
/*  231 */         hashMap.put("id", Util.null2String(recordSet2.getString("id"), ""));
/*  232 */         hashMap.put("mainWorkflowFieldId", recordSet2.getString("mainWorkflowFieldId"));
/*  233 */         hashMap.put("ifSplitField", recordSet2.getString("ifSplitField"));
/*  234 */         hashMap.put("isCreateDocAgain", recordSet2.getString("isCreateDocAgain"));
/*  235 */         hashMap.put("isCreateAttachmentAgain", recordSet2.getString("isCreateAttachmentAgain"));
/*      */       } else {
/*  237 */         hashMap.put("id", "");
/*  238 */         hashMap.put("mainWorkflowFieldId", "");
/*  239 */         hashMap.put("ifSplitField", "0");
/*  240 */         hashMap.put("isCreateDocAgain", "0");
/*  241 */         hashMap.put("isCreateAttachmentAgain", "0");
/*      */       } 
/*      */       
/*  244 */       arrayList.add(hashMap);
/*      */     } 
/*  246 */     return (List)arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static List<Map<String, String>> getMainWorkflowFields(int paramInt1, int paramInt2, int paramInt3) {
/*  257 */     RecordSet recordSet = new RecordSet();
/*  258 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*      */     
/*  260 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  261 */     hashMap1.put("fieldId", "");
/*  262 */     hashMap1.put("fieldName", "");
/*  263 */     hashMap1.put("fieldHtmlType", "");
/*  264 */     hashMap1.put("fieldType", "");
/*  265 */     arrayList.add(hashMap1);
/*      */     
/*  267 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/*  268 */     hashMap2.put("-2", SystemEnv.getHtmlLabelName(33569, paramInt3) + "ID");
/*  269 */     hashMap2.put("-3", SystemEnv.getHtmlLabelName(26876, paramInt3));
/*  270 */     hashMap2.put("-4", SystemEnv.getHtmlLabelName(128667, Util.getIntValue(paramInt3)));
/*  271 */     hashMap2.put("-5", SystemEnv.getHtmlLabelName(17586, paramInt3));
/*      */     
/*  273 */     for (byte b = -2; b >= -5; b--) {
/*  274 */       HashMap<Object, Object> hashMap = new HashMap<>();
/*  275 */       hashMap.put("fieldId", "" + b);
/*  276 */       hashMap.put("fieldName", Util.null2String((String)hashMap2.get("" + b)));
/*  277 */       hashMap.put("fieldHtmlType", "0");
/*  278 */       hashMap.put("fieldType", "0");
/*      */       
/*  280 */       arrayList.add(hashMap);
/*      */     } 
/*      */     
/*  283 */     String str1 = "0";
/*  284 */     String str2 = "0";
/*  285 */     recordSet.executeSql(" select formId,isBill from workflow_base where id= " + paramInt1);
/*  286 */     if (recordSet.next()) {
/*  287 */       str1 = recordSet.getString(1);
/*  288 */       str2 = recordSet.getString(2);
/*      */     } 
/*      */     
/*  291 */     StringBuffer stringBuffer = new StringBuffer();
/*  292 */     if ("0".equals(str2)) {
/*  293 */       stringBuffer.append(" select a.id as id,c.fieldlable as name,a.fieldhtmltype,a.type  ")
/*  294 */         .append("   from workflow_formdict a,workflow_formfield b,workflow_fieldlable c ")
/*  295 */         .append("  where c.isdefault='1' ")
/*  296 */         .append("    and c.formid = b.formid ")
/*  297 */         .append("    and c.fieldid = b.fieldid ")
/*  298 */         .append("    and  b.fieldid= a.id ")
/*  299 */         .append("    and (b.isdetail<>'1' or b.isdetail is null) ")
/*  300 */         .append("    and b.formid=").append(str1)
/*  301 */         .append("   order by b.fieldorder asc ");
/*      */     } else {
/*  303 */       stringBuffer.append(" select id as id , fieldlabel as name,fieldHtmlType,type  ")
/*  304 */         .append("   from workflow_billfield ")
/*  305 */         .append("  where billid=").append(str1)
/*  306 */         .append("    and (viewtype is null or viewtype<>1) ")
/*  307 */         .append("    order by dsporder asc ");
/*      */     } 
/*      */     
/*  310 */     recordSet.executeSql(stringBuffer.toString());
/*  311 */     while (recordSet.next()) {
/*  312 */       HashMap<Object, Object> hashMap = new HashMap<>();
/*      */       
/*  314 */       String str = "";
/*  315 */       if ("0".equals(str2)) {
/*  316 */         str = recordSet.getString(2);
/*      */       } else {
/*  318 */         str = SystemEnv.getHtmlLabelName(recordSet.getInt(2), paramInt3);
/*      */       } 
/*      */       
/*  321 */       hashMap.put("fieldId", recordSet.getString(1));
/*  322 */       hashMap.put("fieldName", str);
/*  323 */       hashMap.put("fieldHtmlType", recordSet.getString(3));
/*  324 */       hashMap.put("fieldType", recordSet.getString(4));
/*      */       
/*  326 */       arrayList.add(hashMap);
/*      */     } 
/*      */ 
/*      */     
/*  330 */     String str3 = "";
/*  331 */     String str4 = "";
/*  332 */     String str5 = "";
/*  333 */     String str6 = "";
/*  334 */     recordSet.executeSql("select fieldId,triggerSource,triggerSourceType,triggerSourceOrder from Workflow_TriDiffWfDiffField where id=" + paramInt2);
/*  335 */     if (recordSet.next()) {
/*  336 */       str5 = recordSet.getString("triggerSource");
/*  337 */       str3 = recordSet.getString("triggerSourceType");
/*  338 */       str4 = recordSet.getString("triggerSourceOrder");
/*  339 */       if (str3.equals("main")) {
/*  340 */         str6 = SystemEnv.getHtmlLabelName(21778, paramInt3);
/*  341 */       } else if (str3.equals("detail")) {
/*  342 */         str6 = SystemEnv.getHtmlLabelName(19325, paramInt3) + str4;
/*      */       } 
/*      */     } 
/*      */ 
/*      */     
/*  347 */     if ("detail".equals(str3)) {
/*  348 */       List<Map<String, String>> list = getDetailTableFields(str3, str5, paramInt1, str5, paramInt3);
/*      */       
/*  350 */       arrayList.addAll(list);
/*      */     } 
/*      */     
/*  353 */     return (List)arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static List<Map<String, String>> getMainWorkflowFields4e9(int paramInt1, int paramInt2, int paramInt3) {
/*  365 */     RecordSet recordSet = new RecordSet();
/*  366 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*      */     
/*  368 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  369 */     hashMap1.put("fieldId", "");
/*  370 */     hashMap1.put("fieldName", "");
/*  371 */     hashMap1.put("fieldHtmlType", "");
/*  372 */     hashMap1.put("fieldType", "");
/*  373 */     arrayList.add(hashMap1);
/*      */     
/*  375 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/*  376 */     hashMap2.put("-2", SystemEnv.getHtmlLabelName(33569, paramInt3) + "ID");
/*  377 */     hashMap2.put("-3", SystemEnv.getHtmlLabelName(26876, paramInt3));
/*  378 */     hashMap2.put("-4", SystemEnv.getHtmlLabelName(128667, Util.getIntValue(paramInt3)));
/*  379 */     hashMap2.put("-5", SystemEnv.getHtmlLabelName(17586, paramInt3));
/*      */     
/*  381 */     for (byte b = -2; b >= -5; b--) {
/*  382 */       HashMap<Object, Object> hashMap = new HashMap<>();
/*  383 */       hashMap.put("fieldId", "" + b);
/*  384 */       hashMap.put("fieldName", Util.null2String((String)hashMap2.get("" + b)));
/*  385 */       hashMap.put("fieldHtmlType", "0");
/*  386 */       hashMap.put("fieldType", "0");
/*  387 */       hashMap.put("prefix", SystemEnv.getHtmlLabelName(468, paramInt3));
/*  388 */       hashMap.put("tableorder", "-1");
/*      */       
/*  390 */       arrayList.add(hashMap);
/*      */     } 
/*      */     
/*  393 */     String str1 = "0";
/*  394 */     String str2 = "0";
/*  395 */     recordSet.executeSql(" select formId,isBill from workflow_base where id= " + paramInt1);
/*  396 */     if (recordSet.next()) {
/*  397 */       str1 = recordSet.getString(1);
/*  398 */       str2 = recordSet.getString(2);
/*      */     } 
/*      */     
/*  401 */     StringBuffer stringBuffer = new StringBuffer();
/*  402 */     if ("0".equals(str2)) {
/*  403 */       stringBuffer.append(" select a.id as id,c.fieldlable as name,a.fieldhtmltype,a.type  ")
/*  404 */         .append("   from workflow_formdict a,workflow_formfield b,workflow_fieldlable c ")
/*  405 */         .append("  where c.isdefault='1' ")
/*  406 */         .append("    and c.formid = b.formid ")
/*  407 */         .append("    and c.fieldid = b.fieldid ")
/*  408 */         .append("    and  b.fieldid= a.id ")
/*  409 */         .append("    and (b.isdetail<>'1' or b.isdetail is null) ")
/*  410 */         .append("    and b.formid=").append(str1)
/*  411 */         .append("   order by b.fieldorder asc ");
/*      */     } else {
/*  413 */       stringBuffer.append(" select id as id , fieldlabel as name,fieldHtmlType,type  ")
/*  414 */         .append("   from workflow_billfield ")
/*  415 */         .append("  where billid=").append(str1)
/*  416 */         .append("    and (viewtype is null or viewtype<>1) ")
/*  417 */         .append("    order by dsporder asc ");
/*      */     } 
/*      */     
/*  420 */     recordSet.executeSql(stringBuffer.toString());
/*  421 */     while (recordSet.next()) {
/*  422 */       HashMap<Object, Object> hashMap = new HashMap<>();
/*      */       
/*  424 */       String str = "";
/*  425 */       if ("0".equals(str2)) {
/*  426 */         str = recordSet.getString(2);
/*      */       } else {
/*  428 */         str = SystemEnv.getHtmlLabelName(recordSet.getInt(2), paramInt3);
/*      */       } 
/*      */       
/*  431 */       hashMap.put("fieldId", recordSet.getString(1));
/*  432 */       hashMap.put("fieldName", str);
/*  433 */       hashMap.put("fieldHtmlType", recordSet.getString(3));
/*  434 */       hashMap.put("fieldType", recordSet.getString(4));
/*  435 */       hashMap.put("prefix", SystemEnv.getHtmlLabelName(21778, paramInt3));
/*  436 */       hashMap.put("tableorder", "0");
/*      */       
/*  438 */       arrayList.add(hashMap);
/*      */     } 
/*      */ 
/*      */     
/*  442 */     String str3 = "";
/*  443 */     String str4 = "";
/*  444 */     String str5 = "";
/*  445 */     String str6 = "";
/*  446 */     recordSet.executeSql("select fieldId,triggerSource,triggerSourceType,triggerSourceOrder from Workflow_TriDiffWfDiffField where id=" + paramInt2);
/*  447 */     if (recordSet.next()) {
/*  448 */       str5 = recordSet.getString("triggerSource");
/*  449 */       str3 = recordSet.getString("triggerSourceType");
/*  450 */       str4 = recordSet.getString("triggerSourceOrder");
/*  451 */       if (str3.equals("main")) {
/*  452 */         str6 = SystemEnv.getHtmlLabelName(21778, paramInt3);
/*  453 */       } else if (str3.equals("detail")) {
/*  454 */         str6 = SystemEnv.getHtmlLabelName(19325, paramInt3) + str4;
/*      */       } 
/*      */     } 
/*      */ 
/*      */     
/*  459 */     if ("detail".equals(str3)) {
/*  460 */       List<Map<String, String>> list = getDetailTableFields4e9(str3, str5, paramInt1, str5, paramInt3, str4);
/*      */       
/*  462 */       arrayList.addAll(list);
/*      */     } 
/*      */     
/*  465 */     return (List)arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static List<Map<String, String>> getDetailTableFields4e9(String paramString1, String paramString2, int paramInt1, String paramString3, int paramInt2, String paramString4) {
/*  478 */     RecordSet recordSet = new RecordSet();
/*  479 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*      */ 
/*      */     
/*  482 */     String str1 = "0";
/*  483 */     String str2 = "0";
/*  484 */     recordSet.executeSql("SELECT formId,isBill FROM workflow_base WHERE id=" + paramInt1);
/*  485 */     if (recordSet.next()) {
/*  486 */       str1 = recordSet.getString(1);
/*  487 */       str2 = recordSet.getString(2);
/*      */     } 
/*      */     
/*  490 */     StringBuilder stringBuilder = new StringBuilder();
/*  491 */     if ("0".equals(str2)) {
/*  492 */       stringBuilder.append("SELECT a.id AS id,c.fieldlable AS name,a.fieldhtmltype,a.type,b.groupid AS detailtablegroup,b.groupid+1 as tableorder")
/*  493 */         .append(" FROM workflow_formdictdetail a,workflow_formfield b,workflow_fieldlable c")
/*  494 */         .append(" WHERE c.isdefault='1'")
/*  495 */         .append(" AND c.formid=b.formid")
/*  496 */         .append(" AND c.fieldid=b.fieldid")
/*  497 */         .append(" AND b.fieldid=a.id")
/*  498 */         .append(" AND b.isdetail='1'")
/*  499 */         .append(" AND b.formid=").append(str1);
/*  500 */       if (paramString3 != null && !paramString3.isEmpty()) {
/*  501 */         stringBuilder.append(" AND b.groupid=" + paramString3);
/*      */       }
/*  503 */       stringBuilder.append(" ORDER BY b.groupid,b.fieldorder ASC");
/*      */     } else {
/*      */       
/*  506 */       stringBuilder.append("SELECT a.id AS id,a.fieldlabel AS name,a.fieldHtmlType,a.type,b.id AS detailtablegroup,b.orderid as tableorder,b.tablename as tablename")
/*  507 */         .append(" FROM workflow_billfield a LEFT OUTER JOIN Workflow_billdetailtable b ON a.detailtable=b.tablename")
/*  508 */         .append(" WHERE a.billid=").append(str1)
/*  509 */         .append(" AND a.viewtype=1");
/*  510 */       if (paramString3 != null && !paramString3.isEmpty()) {
/*  511 */         stringBuilder.append(" AND b.id=").append(paramString3);
/*      */       }
/*  513 */       stringBuilder.append(" ORDER BY b.id,a.dsporder ASC");
/*      */     } 
/*      */     
/*  516 */     recordSet.executeSql(stringBuilder.toString());
/*  517 */     String str3 = "";
/*  518 */     byte b = 0;
/*      */ 
/*      */ 
/*      */     
/*  522 */     DetailOrderBiz detailOrderBiz = new DetailOrderBiz();
/*  523 */     Map map = detailOrderBiz.getDetailOrderMap(Integer.parseInt(str1));
/*      */     
/*  525 */     while (recordSet.next()) {
/*  526 */       HashMap<Object, Object> hashMap = new HashMap<>();
/*      */       
/*  528 */       String str = "";
/*  529 */       if ("0".equals(str2)) {
/*  530 */         str = recordSet.getString(2);
/*      */       } else {
/*  532 */         str = SystemEnv.getHtmlLabelName(recordSet.getInt(2), paramInt2);
/*      */       } 
/*      */       
/*  535 */       hashMap.put("fieldId", recordSet.getString(1));
/*  536 */       hashMap.put("fieldName", str);
/*  537 */       hashMap.put("fieldHtmlType", recordSet.getString(3));
/*  538 */       hashMap.put("fieldType", recordSet.getString(4));
/*  539 */       hashMap.put("detailGroup", recordSet.getString(5));
/*      */       
/*  541 */       int i = Util.getIntValue(recordSet.getString("tableorder"));
/*  542 */       if ("1".equals(str2)) {
/*  543 */         i = ((Integer)map.get(recordSet.getString("tablename"))).intValue();
/*      */       }
/*      */ 
/*      */ 
/*      */       
/*  548 */       hashMap.put("prefix", SystemEnv.getHtmlLabelName(19325, paramInt2) + i);
/*      */       
/*  550 */       hashMap.put("tableorder", recordSet.getString("tableorder"));
/*      */       
/*  552 */       if (!str3.equals(hashMap.get("detailGroup"))) {
/*  553 */         str3 = (String)hashMap.get("detailGroup");
/*  554 */         b++;
/*      */         
/*  556 */         HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  557 */         hashMap1.put("fieldId", "");
/*  558 */         hashMap1.put("fieldName", "---" + SystemEnv.getHtmlLabelName(19325, paramInt2) + "" + b + "---");
/*  559 */         hashMap1.put("fieldHtmlType", "");
/*  560 */         hashMap1.put("fieldType", "");
/*  561 */         hashMap1.put("prefix", "");
/*  562 */         arrayList.add(hashMap1);
/*      */         
/*  564 */         if ("detail".equals(paramString1) && paramString2 != null && paramString2.equals(str3)) {
/*      */           
/*  566 */           HashMap<Object, Object> hashMap2 = new HashMap<>();
/*  567 */           hashMap2.put("fieldId", SubWorkflowTriggerService.getDetailTableIdFieldId(paramString2));
/*  568 */           hashMap2.put("fieldName", SystemEnv.getHtmlLabelName(19325, paramInt2) + "id");
/*  569 */           hashMap2.put("fieldHtmlType", "");
/*  570 */           hashMap2.put("fieldType", "");
/*  571 */           hashMap2.put("prefix", SystemEnv.getHtmlLabelName(19325, paramInt2) + paramString4);
/*  572 */           hashMap.put("tableorder", paramString4);
/*  573 */           arrayList.add(hashMap2);
/*      */ 
/*      */           
/*  576 */           HashMap<Object, Object> hashMap3 = new HashMap<>();
/*  577 */           hashMap3.put("fieldId", SubWorkflowTriggerService.getDetailFieldId(paramString2));
/*  578 */           hashMap3.put("fieldName", SystemEnv.getHtmlLabelName(17463, paramInt2) + "id");
/*  579 */           hashMap3.put("fieldHtmlType", "");
/*  580 */           hashMap3.put("fieldType", "");
/*  581 */           hashMap3.put("prefix", SystemEnv.getHtmlLabelName(19325, paramInt2) + paramString4);
/*  582 */           hashMap.put("tableorder", paramString4);
/*  583 */           arrayList.add(hashMap3);
/*      */         } 
/*      */       } 
/*      */       
/*  587 */       arrayList.add(hashMap);
/*      */     } 
/*      */     
/*  590 */     return (List)arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static List<Map<String, String>> getDetailTableFields(String paramString1, String paramString2, int paramInt1, String paramString3, int paramInt2) {
/*  604 */     RecordSet recordSet = new RecordSet();
/*  605 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*      */ 
/*      */     
/*  608 */     String str1 = "0";
/*  609 */     String str2 = "0";
/*  610 */     recordSet.executeSql("SELECT formId,isBill FROM workflow_base WHERE id=" + paramInt1);
/*  611 */     if (recordSet.next()) {
/*  612 */       str1 = recordSet.getString(1);
/*  613 */       str2 = recordSet.getString(2);
/*      */     } 
/*      */     
/*  616 */     StringBuilder stringBuilder = new StringBuilder();
/*  617 */     if ("0".equals(str2)) {
/*  618 */       stringBuilder.append("SELECT a.id AS id,c.fieldlable AS name,a.fieldhtmltype,a.type,b.groupid AS detailtablegroup")
/*  619 */         .append(" FROM workflow_formdictdetail a,workflow_formfield b,workflow_fieldlable c")
/*  620 */         .append(" WHERE c.isdefault='1'")
/*  621 */         .append(" AND c.formid=b.formid")
/*  622 */         .append(" AND c.fieldid=b.fieldid")
/*  623 */         .append(" AND b.fieldid=a.id")
/*  624 */         .append(" AND b.isdetail='1'")
/*  625 */         .append(" AND b.formid=").append(str1);
/*  626 */       if (paramString3 != null && !paramString3.isEmpty()) {
/*  627 */         stringBuilder.append(" AND b.groupid=" + paramString3);
/*      */       }
/*  629 */       stringBuilder.append(" ORDER BY b.groupid,b.fieldorder ASC");
/*      */     } else {
/*  631 */       stringBuilder.append("SELECT a.id AS id,a.fieldlabel AS name,a.fieldHtmlType,a.type,b.id AS detailtablegroup")
/*  632 */         .append(" FROM workflow_billfield a LEFT OUTER JOIN Workflow_billdetailtable b ON a.detailtable=b.tablename")
/*  633 */         .append(" WHERE a.billid=").append(str1)
/*  634 */         .append(" AND a.viewtype=1");
/*  635 */       if (paramString3 != null && !paramString3.isEmpty()) {
/*  636 */         stringBuilder.append(" AND b.id=").append(paramString3);
/*      */       }
/*  638 */       stringBuilder.append(" ORDER BY b.id,a.dsporder ASC");
/*      */     } 
/*      */     
/*  641 */     recordSet.executeSql(stringBuilder.toString());
/*  642 */     String str3 = "";
/*  643 */     byte b = 0;
/*  644 */     while (recordSet.next()) {
/*  645 */       HashMap<Object, Object> hashMap = new HashMap<>();
/*      */       
/*  647 */       String str = "";
/*  648 */       if ("0".equals(str2)) {
/*  649 */         str = recordSet.getString(2);
/*      */       } else {
/*  651 */         str = SystemEnv.getHtmlLabelName(recordSet.getInt(2), paramInt2);
/*      */       } 
/*      */       
/*  654 */       hashMap.put("fieldId", recordSet.getString(1));
/*  655 */       hashMap.put("fieldName", str);
/*  656 */       hashMap.put("fieldHtmlType", recordSet.getString(3));
/*  657 */       hashMap.put("fieldType", recordSet.getString(4));
/*  658 */       hashMap.put("detailGroup", recordSet.getString(5));
/*      */       
/*  660 */       if (!str3.equals(hashMap.get("detailGroup"))) {
/*  661 */         str3 = (String)hashMap.get("detailGroup");
/*  662 */         b++;
/*      */         
/*  664 */         HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  665 */         hashMap1.put("fieldId", "");
/*  666 */         hashMap1.put("fieldName", "---" + SystemEnv.getHtmlLabelName(19325, paramInt2) + "" + b + "---");
/*  667 */         hashMap1.put("fieldHtmlType", "");
/*  668 */         hashMap1.put("fieldType", "");
/*  669 */         arrayList.add(hashMap1);
/*      */         
/*  671 */         if ("detail".equals(paramString1) && paramString2 != null && paramString2.equals(str3)) {
/*      */           
/*  673 */           HashMap<Object, Object> hashMap2 = new HashMap<>();
/*  674 */           hashMap2.put("fieldId", SubWorkflowTriggerService.getDetailTableIdFieldId(paramString2));
/*  675 */           hashMap2.put("fieldName", SystemEnv.getHtmlLabelName(19325, paramInt2) + "id");
/*  676 */           hashMap2.put("fieldHtmlType", "");
/*  677 */           hashMap2.put("fieldType", "");
/*  678 */           arrayList.add(hashMap2);
/*      */ 
/*      */           
/*  681 */           HashMap<Object, Object> hashMap3 = new HashMap<>();
/*  682 */           hashMap3.put("fieldId", SubWorkflowTriggerService.getDetailFieldId(paramString2));
/*  683 */           hashMap3.put("fieldName", SystemEnv.getHtmlLabelName(17463, paramInt2) + "id");
/*  684 */           hashMap3.put("fieldHtmlType", "");
/*  685 */           hashMap3.put("fieldType", "");
/*  686 */           arrayList.add(hashMap3);
/*      */         } 
/*      */       } 
/*      */       
/*  690 */       arrayList.add(hashMap);
/*      */     } 
/*      */     
/*  693 */     return (List)arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static List<Map<String, String>> getMainWorkflowDetailFields(String paramString1, String paramString2, int paramInt1, int paramInt2) {
/*  705 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*      */     
/*  707 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  708 */     hashMap.put("fieldId", "");
/*  709 */     hashMap.put("fieldName", "");
/*  710 */     hashMap.put("fieldHtmlType", "");
/*  711 */     hashMap.put("fieldType", "");
/*  712 */     hashMap.put("detailGroup", "");
/*  713 */     arrayList.add(hashMap);
/*      */     
/*  715 */     arrayList.addAll(getDetailTableFields(paramString1, paramString2, paramInt1, null, paramInt2));
/*      */     
/*  717 */     return (List)arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static List<Map<String, String>> getMainWorkflowDetailFields4e9(String paramString1, String paramString2, int paramInt1, int paramInt2, String paramString3) {
/*  729 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*      */     
/*  731 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  732 */     hashMap.put("fieldId", "");
/*  733 */     hashMap.put("fieldName", "");
/*  734 */     hashMap.put("fieldHtmlType", "");
/*  735 */     hashMap.put("fieldType", "");
/*  736 */     hashMap.put("detailGroup", "");
/*  737 */     arrayList.add(hashMap);
/*      */     
/*  739 */     arrayList.addAll(getDetailTableFields4e9(paramString1, paramString2, paramInt1, null, paramInt2, paramString3));
/*      */     
/*  741 */     return (List)arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   @Deprecated
/*      */   public String getTriDiffWfFieldTRString(int paramInt1, int paramInt2, int paramInt3, int paramInt4) {
/*  753 */     List<String> list = getTriDiffWfFieldTRString(paramInt1, paramInt2, paramInt3, paramInt4, true);
/*  754 */     return list.get(0);
/*      */   }
/*      */   
/*      */   @Deprecated
/*      */   public List getTriDiffWfFieldTRString(int paramInt1, int paramInt2, int paramInt3, int paramInt4, boolean paramBoolean) {
/*  759 */     String str = "";
/*  760 */     ArrayList<String> arrayList = new ArrayList();
/*  761 */     if (paramInt1 <= 0 || paramInt2 <= 0) {
/*      */       
/*  763 */       arrayList.add("");
/*  764 */       return arrayList;
/*      */     } 
/*      */     
/*      */     try {
/*  768 */       int i = 0;
/*  769 */       int j = 0;
/*  770 */       int k = 0;
/*  771 */       String str1 = "";
/*  772 */       String str2 = "";
/*      */       
/*  774 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  775 */       HashMap<Object, Object> hashMap2 = new HashMap<>();
/*  776 */       HashMap<Object, Object> hashMap3 = new HashMap<>();
/*  777 */       HashMap<Object, Object> hashMap4 = new HashMap<>();
/*      */       
/*  779 */       RecordSet recordSet = new RecordSet();
/*  780 */       recordSet.executeSql("select id,subWorkflowFieldId,mainWorkflowFieldId,isCreateDocAgain,ifSplitField from Workflow_TriDiffWfSubWfField where  (isdetail is null or isdetail<>1) and triDiffWfSubWfId=" + paramInt3);
/*  781 */       while (recordSet.next()) {
/*  782 */         i = Util.getIntValue(recordSet.getString("id"), 0);
/*  783 */         j = Util.getIntValue(recordSet.getString("subWorkflowFieldId"), 0);
/*  784 */         k = Util.getIntValue(recordSet.getString("mainWorkflowFieldId"), 0);
/*  785 */         str1 = Util.null2String(recordSet.getString("isCreateDocAgain"));
/*  786 */         str2 = Util.null2String(recordSet.getString("ifSplitField"));
/*      */         
/*  788 */         hashMap1.put("" + j, "" + i);
/*  789 */         hashMap2.put("" + j, "" + k);
/*  790 */         hashMap3.put("" + j, str1);
/*  791 */         hashMap4.put("" + j, str2);
/*      */       } 
/*      */ 
/*      */       
/*  795 */       String str3 = "0";
/*  796 */       String str4 = "0";
/*      */       
/*  798 */       recordSet.executeSql(" select formId,isBill from workflow_base where id= " + paramInt1);
/*  799 */       if (recordSet.next()) {
/*  800 */         str3 = recordSet.getString(1);
/*  801 */         str4 = recordSet.getString(2);
/*      */       } 
/*      */       
/*  804 */       int m = -1;
/*  805 */       recordSet.executeSql("select b.fieldId from Workflow_TriDiffWfSubWf a,Workflow_TriDiffWfDiffField b where a.triDiffWfDiffFieldId=b.id and a.id=" + paramInt3);
/*  806 */       if (recordSet.next()) {
/*  807 */         m = Util.getIntValue(recordSet.getString("fieldId"), -1);
/*      */       }
/*      */       
/*  810 */       StringBuffer stringBuffer = new StringBuffer();
/*  811 */       if ("0".equals(str4)) {
/*  812 */         stringBuffer.append(" select a.id as id,c.fieldlable as name,a.fieldhtmltype,a.type  ")
/*  813 */           .append("   from workflow_formdict a,workflow_formfield b,workflow_fieldlable c ")
/*  814 */           .append("  where c.isdefault='1' ")
/*  815 */           .append("    and c.formid = b.formid ")
/*  816 */           .append("    and c.fieldid = b.fieldid ")
/*  817 */           .append("    and  b.fieldid= a.id ")
/*  818 */           .append("    and (b.isdetail<>'1' or b.isdetail is null) ")
/*  819 */           .append("    and b.formid=").append(str3)
/*  820 */           .append("   order by b.fieldorder asc ");
/*      */       } else {
/*  822 */         stringBuffer.append(" select id as id , fieldlabel as name,fieldHtmlType,type  ")
/*  823 */           .append("   from workflow_billfield ")
/*  824 */           .append("  where billid=").append(str3)
/*  825 */           .append("    and (viewtype is null or viewtype<>1) ")
/*  826 */           .append("    order by dsporder asc ");
/*      */       } 
/*      */       
/*  829 */       HashMap<Object, Object> hashMap5 = new HashMap<>();
/*  830 */       ArrayList<HashMap<Object, Object>> arrayList1 = new ArrayList();
/*  831 */       HashMap<Object, Object> hashMap6 = null;
/*      */       
/*  833 */       String str5 = "";
/*  834 */       String str6 = "";
/*  835 */       String str7 = "";
/*  836 */       byte b = 0;
/*      */       
/*  838 */       HashMap<Object, Object> hashMap7 = new HashMap<>();
/*  839 */       hashMap7.put("-2", SystemEnv.getHtmlLabelName(33569, paramInt4) + "ID");
/*  840 */       hashMap7.put("-3", SystemEnv.getHtmlLabelName(26876, paramInt4));
/*  841 */       hashMap7.put("-4", SystemEnv.getHtmlLabelName(128667, Util.getIntValue(paramInt4)));
/*  842 */       hashMap7.put("-5", SystemEnv.getHtmlLabelName(17586, paramInt4));
/*      */       
/*  844 */       for (byte b1 = -2; b1 >= -5; b1--) {
/*  845 */         hashMap6 = new HashMap<>();
/*  846 */         hashMap6.put("mainWorkflowFieldId", "" + b1);
/*  847 */         hashMap6.put("mainWorkflowFieldName", Util.null2String((String)hashMap7.get("" + b1)));
/*  848 */         hashMap6.put("mainWorkflowFieldHtmlType", "0");
/*  849 */         hashMap6.put("mainWorkflowFieldType", "0");
/*  850 */         arrayList1.add(hashMap6);
/*  851 */         hashMap5.put("" + k, String.valueOf(b));
/*  852 */         b++;
/*      */       } 
/*      */       
/*  855 */       recordSet.executeSql(stringBuffer.toString());
/*  856 */       while (recordSet.next()) {
/*  857 */         hashMap6 = new HashMap<>();
/*      */         
/*  859 */         k = Util.getIntValue(recordSet.getString("id"), 0);
/*  860 */         if ("0".equals(str4)) {
/*  861 */           str5 = recordSet.getString(2);
/*      */         } else {
/*  863 */           str5 = SystemEnv.getHtmlLabelName(recordSet.getInt(2), paramInt4);
/*      */         } 
/*  865 */         str6 = recordSet.getString(3);
/*  866 */         str7 = recordSet.getString(4);
/*      */         
/*  868 */         hashMap6.put("mainWorkflowFieldId", "" + k);
/*  869 */         hashMap6.put("mainWorkflowFieldName", str5);
/*  870 */         hashMap6.put("mainWorkflowFieldHtmlType", str6);
/*  871 */         hashMap6.put("mainWorkflowFieldType", str7);
/*      */         
/*  873 */         arrayList1.add(hashMap6);
/*      */         
/*  875 */         hashMap5.put("" + k, String.valueOf(b));
/*  876 */         b++;
/*      */       } 
/*      */       
/*  879 */       String str8 = "0";
/*  880 */       String str9 = "0";
/*      */       
/*  882 */       recordSet.executeSql(" select formId,isBill from workflow_base where id= " + paramInt2);
/*  883 */       if (recordSet.next()) {
/*  884 */         str8 = recordSet.getString(1);
/*  885 */         str9 = recordSet.getString(2);
/*      */       } 
/*      */       
/*  888 */       String str10 = "";
/*  889 */       String str11 = "DataLight";
/*  890 */       String str12 = "";
/*  891 */       String str13 = "";
/*  892 */       String str14 = "";
/*  893 */       int n = 0;
/*      */       
/*  895 */       HashMap<Object, Object> hashMap8 = null;
/*  896 */       ArrayList<HashMap<Object, Object>> arrayList2 = new ArrayList();
/*      */       
/*  898 */       for (byte b2 = -3; b2 >= -5; b2--) {
/*  899 */         hashMap8 = new HashMap<>();
/*  900 */         hashMap8.put("subWorkflowFieldId", "" + b2);
/*  901 */         hashMap8.put("subWorkflowFieldName", Util.null2String((String)hashMap7.get("" + b2)));
/*  902 */         arrayList2.add(hashMap8);
/*      */       } 
/*      */       
/*  905 */       stringBuffer = new StringBuffer();
/*      */ 
/*      */       
/*  908 */       String str15 = "select a.ismode,a.showdes,a.nodeid from workflow_flownode a where a.workflowid=" + paramInt2 + " and a.nodeType=0";
/*  909 */       recordSet.execute(str15);
/*  910 */       if (recordSet.next()) {
/*  911 */         int i1 = Util.getIntValue(recordSet.getString("ismode"), 0);
/*  912 */         int i2 = Util.getIntValue(recordSet.getString("showdes"), 0);
/*  913 */         int i3 = Util.getIntValue(recordSet.getString("nodeid"), 0);
/*      */         
/*  915 */         if (i1 == 0 || (i1 == 1 && i2 == 1)) {
/*  916 */           str15 = " (select fieldid as id, ismandatory from workflow_nodeform where nodeid=" + i3 + " ) wnf ";
/*      */         } else {
/*      */           
/*  919 */           str15 = " (select fieldid as id, ismandatory from workflow_modeview where nodeid=" + i3 + " ) wnf ";
/*      */         } 
/*      */       } 
/*      */ 
/*      */       
/*  924 */       String str16 = str15;
/*      */       
/*  926 */       if ("0".equals(str9)) {
/*  927 */         stringBuffer.append(" select b.fieldid as id,c.fieldlable as name, wnf.ismandatory")
/*  928 */           .append("   from workflow_formfield b,workflow_fieldlable c left join ").append(str16)
/*  929 */           .append("   on c.fieldid=wnf.id ")
/*  930 */           .append("  where c.isdefault='1' ")
/*  931 */           .append("    and c.formid = b.formid ")
/*  932 */           .append("    and c.fieldid = b.fieldid ")
/*  933 */           .append("    and (b.isdetail<>'1' or b.isdetail is null) ")
/*  934 */           .append("    and b.formid=").append(str8)
/*  935 */           .append("   order by b.fieldorder asc ");
/*      */       } else {
/*  937 */         stringBuffer.append(" select wbf.id as id , wbf.fieldlabel as name, wnf.ismandatory ")
/*  938 */           .append("   from workflow_billfield wbf left join ").append(str16)
/*  939 */           .append("   on wbf.id=wnf.id ")
/*  940 */           .append("  where wbf.billid=").append(str8)
/*  941 */           .append("    and (wbf.viewtype is null or wbf.viewtype<>1) ")
/*  942 */           .append("    order by dsporder asc ");
/*      */       } 
/*      */       
/*  945 */       recordSet.executeSql(stringBuffer.toString());
/*      */       
/*  947 */       while (recordSet.next()) {
/*  948 */         j = Util.getIntValue(recordSet.getString(1), 0);
/*  949 */         if ("0".equals(str9)) {
/*  950 */           str10 = recordSet.getString(2);
/*      */         } else {
/*  952 */           str10 = SystemEnv.getHtmlLabelName(recordSet.getInt(2), paramInt4);
/*      */         } 
/*      */         
/*  955 */         hashMap8 = new HashMap<>();
/*  956 */         hashMap8.put("subWorkflowFieldId", "" + j);
/*  957 */         hashMap8.put("subWorkflowFieldName", str10);
/*      */         
/*  959 */         hashMap8.put("subWorkflowFieldIsmandatory", Util.getIntValue(recordSet.getString("ismandatory"), 0) + "");
/*      */         
/*  961 */         arrayList2.add(hashMap8);
/*      */       } 
/*      */       
/*  964 */       String str17 = "";
/*      */       
/*  966 */       for (b = 0; b < arrayList2.size(); b++) {
/*  967 */         Map map = arrayList2.get(b);
/*  968 */         j = Util.getIntValue((String)map.get("subWorkflowFieldId"), 0);
/*  969 */         str10 = (String)map.get("subWorkflowFieldName");
/*      */         
/*  971 */         String str18 = (String)map.get("subWorkflowFieldIsmandatory");
/*      */         
/*  973 */         String str19 = "";
/*  974 */         String str20 = "";
/*  975 */         if ("1".equals(str18)) {
/*  976 */           str20 = "<img src='/images/BacoError_wev8.gif' align='absmiddle'>";
/*      */         }
/*  978 */         str19 = "&nbsp;&nbsp;<span id='field_lable" + j + "span'>" + str20 + "</span>";
/*      */         
/*  980 */         str12 = "";
/*  981 */         str13 = "";
/*  982 */         str14 = "";
/*      */ 
/*      */         
/*  985 */         n = Util.getIntValue((String)hashMap2.get("" + j), 0);
/*      */         
/*  987 */         str12 = str12 + "<select name=mainWorkflowFieldId  id=mainWorkflowFieldId_" + b + " onChange=\"changeIsCreateDocAgain(this,'divIsCreateDocAgain_'," + m + ");checkinput2('mainWorkflowFieldId_" + b + "','field_lable" + j + "span', this.getAttribute('viewtype'));\" viewtype=\"" + str18 + "\">";
/*  988 */         str12 = str12 + "  <option value=''  ></option>";
/*      */         
/*  990 */         for (byte b3 = 0; b3 < arrayList1.size(); b3++) {
/*  991 */           Map map1 = arrayList1.get(b3);
/*      */           
/*  993 */           k = Util.getIntValue((String)map1.get("mainWorkflowFieldId"), 0);
/*  994 */           str5 = (String)map1.get("mainWorkflowFieldName");
/*  995 */           str6 = (String)map1.get("mainWorkflowFieldHtmlType");
/*  996 */           str7 = (String)map1.get("mainWorkflowFieldType");
/*      */           
/*  998 */           if (n == k) {
/*  999 */             str12 = str12 + "  <option value='" + str6 + "_" + str7 + "_" + k + "'  selected>" + str5 + "</option>";
/* 1000 */             str19 = "&nbsp;&nbsp;<span id='field_lable" + j + "span'></span>";
/*      */           } else {
/* 1002 */             str12 = str12 + "  <option value='" + str6 + "_" + str7 + "_" + k + "'>" + str5 + "</option>";
/*      */           } 
/*      */         } 
/* 1005 */         str12 = str12 + "</select>";
/*      */         
/* 1007 */         if (n <= 0) {
/*      */ 
/*      */ 
/*      */           
/* 1011 */           str13 = "<div id='divIsCreateDocAgain_" + b + "' style='display:none'><input class='inputStyle' type='checkbox' name='chkIsCreateDocAgain_" + b + "'  value='1'><span id='spandivIsCreateDocAgain_" + b + "'>" + SystemEnv.getHtmlLabelName(21577, paramInt4) + "</span></div>";
/*      */         }
/*      */         else {
/*      */           
/* 1015 */           str1 = (String)hashMap3.get("" + j);
/*      */           
/* 1017 */           str2 = (String)hashMap4.get("" + j);
/*      */           
/* 1019 */           str6 = "";
/* 1020 */           str7 = "";
/* 1021 */           String str21 = (String)hashMap5.get("" + n);
/* 1022 */           if (str21 != null && !str21.equals("")) {
/* 1023 */             Map map1 = arrayList1.get(Integer.parseInt(str21));
/* 1024 */             str6 = (String)map1.get("mainWorkflowFieldHtmlType");
/* 1025 */             str7 = (String)map1.get("mainWorkflowFieldType");
/*      */           } 
/*      */           
/* 1028 */           if (("3".equals(str6) && ("9".equals(str7) || "37".equals(str7))) || "6"
/*      */             
/* 1030 */             .equals(str6)) {
/*      */             
/* 1032 */             if ("6".equals(str6)) {
/* 1033 */               str17 = SystemEnv.getHtmlLabelName(21719, paramInt4);
/*      */             } else {
/* 1035 */               str17 = SystemEnv.getHtmlLabelName(21718, paramInt4);
/*      */             } 
/* 1037 */             if (str1 != null && str1.equals("1"))
/*      */             {
/* 1039 */               str13 = "<div id='divIsCreateDocAgain_" + b + "' style='display:'><input class='inputStyle' type='checkbox' name='chkIsCreateDocAgain_" + b + "'  value='1' checked><span id='spandivIsCreateDocAgain_" + b + "'>" + str17 + "</span></div>";
/*      */             
/*      */             }
/*      */             else
/*      */             {
/*      */               
/* 1045 */               str13 = "<div id='divIsCreateDocAgain_" + b + "' style='display:'><input class='inputStyle' type='checkbox' name='chkIsCreateDocAgain_" + b + "'  value='1'><span id='spandivIsCreateDocAgain_" + b + "'>" + str17 + "</span></div>";
/*      */ 
/*      */             
/*      */             }
/*      */ 
/*      */           
/*      */           }
/*      */           else {
/*      */ 
/*      */             
/* 1055 */             str13 = "<div id='divIsCreateDocAgain_" + b + "' style='display:none'><input class='inputStyle' type='checkbox' name='chkIsCreateDocAgain_" + b + "'  value='1'><span id='spandivIsCreateDocAgain_" + b + "'>" + SystemEnv.getHtmlLabelName(21577, paramInt4) + "</span></div>";
/*      */           } 
/*      */         } 
/*      */ 
/*      */         
/* 1060 */         if (n == m && n > 0) {
/* 1061 */           if (str2 != null && str2.equals("1"))
/*      */           {
/*      */ 
/*      */             
/* 1065 */             str14 = "<div id='divIfSplitField_" + b + "' style='display:'><input class='inputStyle' type='checkbox' name='chkIfSplitField_" + b + "'  value='1' checked>" + SystemEnv.getHtmlLabelName(19359, paramInt4) + "</div>";
/*      */           
/*      */           }
/*      */           else
/*      */           {
/* 1070 */             str14 = "<div id='divIfSplitField_" + b + "' style='display:'><input class='inputStyle' type='checkbox' name='chkIfSplitField_" + b + "'  value='1'>" + SystemEnv.getHtmlLabelName(19359, paramInt4) + "</div>";
/*      */           }
/*      */         
/*      */         }
/*      */         else {
/*      */           
/* 1076 */           str14 = "<div id='divIfSplitField_" + b + "' style='display:none'><input class='inputStyle' type='checkbox' name='chkIfSplitField_" + b + "'  value='1'>" + SystemEnv.getHtmlLabelName(19359, paramInt4) + "</div>";
/*      */         } 
/*      */ 
/*      */ 
/*      */         
/* 1081 */         i = Util.getIntValue((String)hashMap1.get("" + j), 0);
/* 1082 */         if (!paramBoolean) {
/* 1083 */           str = str + "<TR CLASS='" + str11 + "'>\n";
/* 1084 */           str = str + "\t<TD><input type='hidden' name='triDiffWfSubWfFieldId' value='" + i + "'>" + str10 + str19 + "<input type='hidden' name='subWorkflowFieldId' value='" + j + "'></TD>\n";
/* 1085 */           str = str + "\t<TD>" + str12 + "</TD>\n";
/* 1086 */           str = str + "\t<TD>" + str13 + "</TD>\n";
/* 1087 */           str = str + "\t<TD>" + str14 + "</TD>\n";
/* 1088 */           str = str + "</TR>\n";
/*      */           
/* 1090 */           if (str11.equals("DataLight")) {
/* 1091 */             str11 = "DataLight";
/*      */           } else {
/*      */             
/* 1094 */             str11 = "DataLight";
/*      */           } 
/*      */         } else {
/* 1097 */           HashMap<Object, Object> hashMap = new HashMap<>();
/* 1098 */           hashMap.put("triDiffWfSubWfFieldId", "<input type='hidden' name='triDiffWfSubWfFieldId' value='" + i + "'>" + str10 + str19 + "<input type='hidden' name='subWorkflowFieldId' value='" + j + "'>");
/* 1099 */           hashMap.put("selectMainWorkflowFieldId", str12);
/* 1100 */           hashMap.put("divIsCreateDocAgain", str13);
/* 1101 */           hashMap.put("divIfSplitField", str14);
/* 1102 */           arrayList.add(hashMap);
/*      */         } 
/*      */       } 
/*      */ 
/*      */ 
/*      */       
/* 1108 */       return arrayList;
/* 1109 */     } catch (Exception exception) {
/* 1110 */       arrayList.add("");
/* 1111 */       return arrayList;
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   @Deprecated
/*      */   public String getTriDiffWfFieldDetailTRString(int paramInt1, int paramInt2, int paramInt3, int paramInt4) {
/* 1124 */     List<String> list = getTriDiffWfFieldDetailTRString(paramInt1, paramInt2, paramInt3, paramInt4, false);
/* 1125 */     return list.get(0);
/*      */   }
/*      */   
/*      */   @Deprecated
/*      */   public List getTriDiffWfFieldDetailTRString(int paramInt1, int paramInt2, int paramInt3, int paramInt4, boolean paramBoolean) {
/* 1130 */     String str = "";
/* 1131 */     ArrayList<String> arrayList = new ArrayList();
/* 1132 */     if (paramInt1 <= 0 || paramInt2 <= 0) {
/*      */       
/* 1134 */       arrayList.add("");
/* 1135 */       return arrayList;
/*      */     } 
/*      */     
/*      */     try {
/* 1139 */       int i = 0;
/* 1140 */       int j = 0;
/* 1141 */       int k = 0;
/* 1142 */       String str1 = "";
/*      */       
/* 1144 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 1145 */       HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 1146 */       HashMap<Object, Object> hashMap3 = new HashMap<>();
/* 1147 */       RecordSet recordSet = new RecordSet();
/* 1148 */       recordSet.executeSql(" select id,subWorkflowFieldId,mainWorkflowFieldId,isCreateDocAgain from Workflow_TriDiffWfSubWfField where isdetail=1 and triDiffWfSubWfId=" + paramInt3);
/* 1149 */       while (recordSet.next()) {
/* 1150 */         i = Util.getIntValue(recordSet.getString("id"), 0);
/* 1151 */         j = Util.getIntValue(recordSet.getString("subWorkflowFieldId"), 0);
/* 1152 */         k = Util.getIntValue(recordSet.getString("mainWorkflowFieldId"), 0);
/* 1153 */         str1 = Util.null2String(recordSet.getString("isCreateDocAgain"));
/* 1154 */         hashMap1.put("" + j, "" + i);
/* 1155 */         hashMap2.put("" + j, "" + k);
/* 1156 */         hashMap3.put("" + j, str1);
/*      */       } 
/*      */ 
/*      */ 
/*      */       
/* 1161 */       String str2 = "0";
/* 1162 */       String str3 = "0";
/*      */       
/* 1164 */       recordSet.executeSql(" select formId,isBill from workflow_base where id= " + paramInt1);
/* 1165 */       if (recordSet.next()) {
/* 1166 */         str2 = recordSet.getString(1);
/* 1167 */         str3 = recordSet.getString(2);
/*      */       } 
/*      */       
/* 1170 */       StringBuffer stringBuffer = new StringBuffer();
/* 1171 */       if ("0".equals(str3)) {
/* 1172 */         stringBuffer.append(" select a.id as id,c.fieldlable as name,a.fieldhtmltype,a.type,b.groupid as detailtablegroup ")
/* 1173 */           .append("   from workflow_formdictdetail a,workflow_formfield b,workflow_fieldlable c ")
/* 1174 */           .append("  where c.isdefault='1' ")
/* 1175 */           .append("    and c.formid = b.formid ")
/* 1176 */           .append("    and c.fieldid = b.fieldid ")
/* 1177 */           .append("    and  b.fieldid= a.id ")
/* 1178 */           .append("    and b.isdetail='1' ")
/* 1179 */           .append("    and b.formid=").append(str2)
/* 1180 */           .append("   order by b.groupid,b.fieldorder asc ");
/*      */       } else {
/* 1182 */         stringBuffer.append(" select id as id , fieldlabel as name,fieldHtmlType,type,detailtable as detailtablegroup ")
/* 1183 */           .append("   from workflow_billfield ")
/* 1184 */           .append("  where billid=").append(str2)
/* 1185 */           .append("    and viewtype=1 ")
/* 1186 */           .append("    order by detailtable,dsporder asc ");
/*      */       } 
/*      */ 
/*      */       
/* 1190 */       ArrayList<HashMap<Object, Object>> arrayList1 = new ArrayList();
/* 1191 */       HashMap<Object, Object> hashMap4 = null;
/*      */       
/* 1193 */       String str4 = "";
/* 1194 */       String str5 = "";
/* 1195 */       String str6 = "";
/* 1196 */       String str7 = "";
/*      */       
/* 1198 */       recordSet.executeSql(stringBuffer.toString());
/* 1199 */       while (recordSet.next()) {
/* 1200 */         hashMap4 = new HashMap<>();
/*      */         
/* 1202 */         k = Util.getIntValue(recordSet.getString(1), 0);
/* 1203 */         if ("0".equals(str3)) {
/* 1204 */           str4 = recordSet.getString(2);
/*      */         } else {
/* 1206 */           str4 = SystemEnv.getHtmlLabelName(recordSet.getInt(2), paramInt4);
/*      */         } 
/* 1208 */         str5 = recordSet.getString(3);
/* 1209 */         str6 = recordSet.getString(4);
/* 1210 */         str7 = Util.null2String(recordSet.getString(5));
/*      */         
/* 1212 */         hashMap4.put("mainWorkflowFieldId", "" + k);
/* 1213 */         hashMap4.put("mainWorkflowFieldName", str4);
/* 1214 */         hashMap4.put("mainWorkflowFieldHtmlType", str5);
/* 1215 */         hashMap4.put("mainWorkflowFieldType", str6);
/* 1216 */         hashMap4.put("maindetailtable", str7);
/*      */         
/* 1218 */         arrayList1.add(hashMap4);
/*      */       } 
/*      */       
/* 1221 */       String str8 = "0";
/* 1222 */       String str9 = "0";
/*      */       
/* 1224 */       recordSet.executeSql(" select formId,isBill from workflow_base where id= " + paramInt2);
/* 1225 */       if (recordSet.next()) {
/* 1226 */         str8 = recordSet.getString(1);
/* 1227 */         str9 = recordSet.getString(2);
/*      */       } 
/*      */       
/* 1230 */       String str10 = "";
/* 1231 */       String str11 = "dataLight";
/* 1232 */       String str12 = "";
/* 1233 */       String str13 = "";
/* 1234 */       int m = 0;
/* 1235 */       String str14 = "";
/* 1236 */       String str15 = "";
/*      */       
/* 1238 */       HashMap<Object, Object> hashMap5 = null;
/* 1239 */       ArrayList<HashMap<Object, Object>> arrayList2 = new ArrayList();
/* 1240 */       ArrayList<ArrayList> arrayList3 = new ArrayList();
/* 1241 */       byte b1 = 0;
/* 1242 */       String str16 = null;
/* 1243 */       String str17 = "";
/* 1244 */       stringBuffer = new StringBuffer();
/* 1245 */       if ("0".equals(str9)) {
/* 1246 */         stringBuffer.append(" select b.fieldid as id,c.fieldlable as name,b.groupid as detailtablegroup ")
/* 1247 */           .append("   from workflow_formfield b,workflow_fieldlable c ")
/* 1248 */           .append("  where c.isdefault='1' ")
/* 1249 */           .append("    and c.formid = b.formid ")
/* 1250 */           .append("    and c.fieldid = b.fieldid ")
/* 1251 */           .append("    and b.isdetail='1' ")
/* 1252 */           .append("    and b.formid=").append(str8)
/* 1253 */           .append("   order by b.groupid,b.fieldorder asc ");
/*      */       } else {
/* 1255 */         stringBuffer.append(" select id as id , fieldlabel as name,detailtable as detailtablegroup  ")
/* 1256 */           .append("   from workflow_billfield ")
/* 1257 */           .append("  where billid=").append(str8)
/* 1258 */           .append("    and viewtype=1 ")
/* 1259 */           .append("    order by detailtable,dsporder asc ");
/*      */       } 
/*      */ 
/*      */       
/* 1263 */       recordSet.executeSql(stringBuffer.toString());
/*      */       
/* 1265 */       while (recordSet.next()) {
/* 1266 */         j = Util.getIntValue(recordSet.getString(1), 0);
/* 1267 */         if ("0".equals(str9)) {
/* 1268 */           str10 = recordSet.getString(2);
/*      */         } else {
/* 1270 */           str10 = SystemEnv.getHtmlLabelName(recordSet.getInt(2), paramInt4);
/*      */         } 
/* 1272 */         str17 = Util.null2String(recordSet.getString(3));
/* 1273 */         if (!str17.equals(str16)) {
/* 1274 */           if (str16 != null) {
/* 1275 */             arrayList3.add(arrayList2);
/* 1276 */             arrayList2 = new ArrayList();
/*      */           } 
/* 1278 */           str16 = str17;
/*      */         } 
/* 1280 */         hashMap5 = new HashMap<>();
/* 1281 */         hashMap5.put("subWorkflowFieldId", "" + j);
/* 1282 */         hashMap5.put("subWorkflowFieldName", str10);
/*      */         
/* 1284 */         arrayList2.add(hashMap5);
/*      */       } 
/* 1286 */       if (arrayList2.size() > 0) {
/* 1287 */         arrayList3.add(arrayList2);
/*      */       }
/*      */       
/* 1290 */       byte b2 = 0;
/* 1291 */       String str18 = "";
/* 1292 */       HashMap<Object, Object> hashMap6 = new HashMap<>();
/* 1293 */       for (byte b3 = 0; b3 < arrayList3.size(); b3++) {
/* 1294 */         List<Map> list = arrayList3.get(b3);
/* 1295 */         str11 = "DataLight";
/* 1296 */         str = str + "<tr>\n<td colSpan=4><B>" + SystemEnv.getHtmlLabelName(17463, paramInt4) + (b3 + 1) + "</B></TD></tr>\n";
/*      */ 
/*      */ 
/*      */         
/* 1300 */         str = str + "<tr class=\"header\">\n<td>" + SystemEnv.getHtmlLabelName(19357, paramInt4) + "</td>\n<td>" + SystemEnv.getHtmlLabelName(19358, paramInt4) + "</td>\n<td colSpan=2>" + SystemEnv.getHtmlLabelName(21577, paramInt4) + "</td>\n";
/*      */         
/* 1302 */         if (list != null) {
/* 1303 */           ArrayList<HashMap<Object, Object>> arrayList4 = new ArrayList();
/* 1304 */           for (b1 = 0; b1 < list.size(); b1++) {
/* 1305 */             Map map = list.get(b1);
/* 1306 */             j = Util.getIntValue((String)map.get("subWorkflowFieldId"), 0);
/* 1307 */             str10 = (String)map.get("subWorkflowFieldName");
/*      */             
/* 1309 */             str12 = "";
/* 1310 */             str13 = "";
/*      */ 
/*      */             
/* 1313 */             m = Util.getIntValue((String)hashMap2.get("" + j), 0);
/* 1314 */             str14 = "";
/* 1315 */             str15 = "";
/*      */             
/* 1317 */             str12 = str12 + "<select name=mainWorkflowDltFieldId  id=mainWorkflowDltFieldId_" + b2 + "  onChange='changeIsCreateDocAgain(this,\"divDltIsCreateDocAgain_\",0)'>";
/* 1318 */             str12 = str12 + "  <option value=''  ></option>";
/* 1319 */             String str19 = null;
/* 1320 */             byte b4 = 0;
/* 1321 */             for (byte b5 = 0; b5 < arrayList1.size(); b5++) {
/* 1322 */               Map map1 = arrayList1.get(b5);
/*      */               
/* 1324 */               k = Util.getIntValue((String)map1.get("mainWorkflowFieldId"), 0);
/* 1325 */               str4 = (String)map1.get("mainWorkflowFieldName");
/* 1326 */               str5 = (String)map1.get("mainWorkflowFieldHtmlType");
/* 1327 */               str6 = (String)map1.get("mainWorkflowFieldType");
/* 1328 */               str7 = Util.null2String((String)map1.get("maindetailtable"));
/* 1329 */               if (!str7.equals(str19)) {
/* 1330 */                 str12 = str12 + "  <option value=''  >--------" + SystemEnv.getHtmlLabelName(17463, paramInt4) + (b4 + 1) + "--------</option>";
/* 1331 */                 str19 = str7;
/* 1332 */                 b4++;
/*      */               } 
/* 1334 */               if (m == k) {
/* 1335 */                 str14 = str5;
/* 1336 */                 str15 = str6;
/* 1337 */                 str12 = str12 + "  <option value='" + str5 + "_" + str6 + "_" + k + "'  selected>" + str4 + "</option>";
/*      */               } else {
/* 1339 */                 str12 = str12 + "  <option value='" + str5 + "_" + str6 + "_" + k + "'>" + str4 + "</option>";
/*      */               } 
/*      */             } 
/* 1342 */             str12 = str12 + "</select>";
/*      */             
/* 1344 */             i = Util.getIntValue((String)hashMap1.get("" + j), 0);
/*      */             
/* 1346 */             str1 = (String)hashMap3.get("" + j);
/*      */ 
/*      */             
/* 1349 */             if (("3".equals(str14) && ("9".equals(str15) || "37".equals(str15))) || "6"
/*      */               
/* 1351 */               .equals(str14)) {
/*      */               
/* 1353 */               if ("6".equals(str5)) {
/* 1354 */                 str18 = SystemEnv.getHtmlLabelName(21719, paramInt4);
/*      */               } else {
/* 1356 */                 str18 = SystemEnv.getHtmlLabelName(21718, paramInt4);
/*      */               } 
/*      */               
/* 1359 */               if (str1 != null && str1.equals("1"))
/*      */               {
/* 1361 */                 str13 = "<div id='divDltIsCreateDocAgain_" + b2 + "' style='display:'><input class='inputStyle' type='checkbox' name='chkDltIsCreateDocAgain_" + b2 + "'  value='1' checked><span id='spandivDltIsCreateDocAgain_" + b2 + "'>" + str18 + "</span></div>";
/*      */               
/*      */               }
/*      */               else
/*      */               {
/*      */                 
/* 1367 */                 str13 = "<div id='divDltIsCreateDocAgain_" + b2 + "' style='display:'><input class='inputStyle' type='checkbox' name='chkDltIsCreateDocAgain_" + b2 + "'  value='1'><span id='spandivDltIsCreateDocAgain_" + b2 + "'>" + str18 + "</span></div>";
/*      */ 
/*      */               
/*      */               }
/*      */ 
/*      */             
/*      */             }
/*      */             else {
/*      */ 
/*      */               
/* 1377 */               str13 = "<div id='divDltIsCreateDocAgain_" + b2 + "' style='display:none'><input class='inputStyle' type='checkbox' name='chkDltIsCreateDocAgain_" + b2 + "'  value='1'><span id='spandivDltIsCreateDocAgain_" + b2 + "'>" + SystemEnv.getHtmlLabelName(21577, paramInt4) + "</span></div>";
/*      */             } 
/*      */             
/* 1380 */             if (!paramBoolean) {
/* 1381 */               str = str + "<TR CLASS='" + str11 + "'>\n";
/* 1382 */               str = str + "\t<TD><input type='hidden' name='triDiffWfDltSubWfFieldId' value='" + i + "'>" + str10 + "<input type='hidden' name='subWorkflowDltFieldId' value='" + j + "'></TD>\n";
/* 1383 */               str = str + "\t<TD>" + str12 + "</TD>\n";
/* 1384 */               str = str + "\t<TD  colSpan=2>" + str13 + "</TD>\n";
/* 1385 */               str = str + "</TR>\n";
/*      */               
/* 1387 */               if (str11.equals("DataLight")) {
/* 1388 */                 str11 = "DataLight";
/*      */               } else {
/*      */                 
/* 1391 */                 str11 = "DataLight";
/*      */               } 
/*      */             } else {
/* 1394 */               HashMap<Object, Object> hashMap = new HashMap<>();
/* 1395 */               hashMap.put("subwfDltSetDetailId", "<input type='hidden' name='triDiffWfDltSubWfFieldId' value='" + i + "'>" + str10 + "<input type='hidden' name='subWorkflowDltFieldId' value='" + j + "'>");
/* 1396 */               hashMap.put("selectMainWorkflowFieldId", str12);
/* 1397 */               hashMap.put("divIsCreateDocAgain", str13);
/* 1398 */               arrayList4.add(hashMap);
/*      */             } 
/* 1400 */             b2++;
/*      */           } 
/* 1402 */           if (paramBoolean) {
/* 1403 */             hashMap6.put("detail_" + b3, arrayList4);
/* 1404 */             arrayList.add(hashMap6);
/*      */           } 
/*      */         } 
/*      */       } 
/*      */       
/* 1409 */       if (!paramBoolean) {
/* 1410 */         arrayList.add(str);
/*      */       }
/* 1412 */       return arrayList;
/* 1413 */     } catch (Exception exception) {
/* 1414 */       arrayList.add("");
/* 1415 */       return arrayList;
/*      */     } 
/*      */   }
/*      */   
/*      */   public List<Map<String, String>> getTriDiffWfSubWfList(User paramUser, Map<String, String> paramMap, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/* 1420 */     String str1 = paramMap.get("triDiffWfDiffFieldId");
/* 1421 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 1422 */     int i = 0;
/* 1423 */     int j = 0;
/* 1424 */     String str2 = "";
/* 1425 */     int k = 0;
/*      */     
/* 1427 */     String str3 = "";
/*      */     
/* 1429 */     RecordSet recordSet = new RecordSet();
/* 1430 */     recordSet.executeSql("select id,subWorkflowId,isRead,fieldValue from Workflow_TriDiffWfSubWf where triDiffWfDiffFieldId=" + str1 + "  order by id asc");
/* 1431 */     byte b = 0;
/* 1432 */     while (recordSet.next()) {
/* 1433 */       String str = Util.null2String(recordSet.getString("fieldValue"), "-1");
/* 1434 */       if ("-1".equals(str))
/* 1435 */         continue;  HashMap<Object, Object> hashMap = new HashMap<>();
/* 1436 */       i = Util.getIntValue(recordSet.getString("id"), 0);
/* 1437 */       j = Util.getIntValue(recordSet.getString("subWorkflowId"), 0);
/* 1438 */       k = Util.getIntValue(recordSet.getString("isRead"), 0);
/*      */       
/* 1440 */       hashMap.put("id", "" + i);
/* 1441 */       hashMap.put("triDiffWfSubWfId", "" + i);
/* 1442 */       hashMap.put("isRead", "" + k);
/* 1443 */       hashMap.put("fieldValue", "" + str);
/* 1444 */       hashMap.put("subWorkflowId", "" + j);
/* 1445 */       hashMap.put("index", String.valueOf(b++));
/* 1446 */       arrayList.add(hashMap);
/*      */     } 
/*      */     
/* 1449 */     return (List)arrayList;
/*      */   }
/*      */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/workflow/WorkflowTriDiffWfManager.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */