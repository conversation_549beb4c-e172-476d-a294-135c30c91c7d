/*     */ package weaver.workflow.workflow;
/*     */ 
/*     */ import weaver.cache.CacheBase;
/*     */ import weaver.cache.CacheColumn;
/*     */ import weaver.cache.CacheColumnType;
/*     */ import weaver.cache.PKColumn;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class BillComInfo
/*     */   extends CacheBase
/*     */ {
/*  24 */   protected static String TABLE_NAME = "workflow_bill";
/*     */   
/*  26 */   protected static String TABLE_WHERE = null;
/*     */   
/*  28 */   protected static String TABLE_ORDER = null;
/*     */   
/*     */   @PKColumn(type = CacheColumnType.NUMBER)
/*  31 */   protected static String PK_NAME = "id";
/*     */ 
/*     */ 
/*     */   
/*     */   @CacheColumn
/*     */   protected static int namelabel;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getBillNum() {
/*  42 */     return size();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public boolean next(String paramString) {
/*  50 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getBillid() {
/*  59 */     return (String)getRowValue(0);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getBillLabel() {
/*  68 */     return (String)getRowValue(namelabel);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getBillLabel(String paramString) {
/*  78 */     return (String)getValue(namelabel, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void removeBillCache() {
/*  85 */     removeCache();
/*     */   }
/*     */   
/*     */   public boolean addBillCache(String paramString) {
/*  89 */     if ("".equals(paramString)) {
/*  90 */       return false;
/*     */     }
/*  92 */     addCache(paramString);
/*  93 */     return true;
/*     */   }
/*     */   
/*     */   public boolean addBillCache(String paramString, int paramInt) {
/*  97 */     if ("".equals(paramString)) {
/*  98 */       return false;
/*     */     }
/* 100 */     addCache(paramString);
/* 101 */     return true;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/workflow/BillComInfo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */