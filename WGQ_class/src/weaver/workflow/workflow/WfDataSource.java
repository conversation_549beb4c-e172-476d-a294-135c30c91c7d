/*      */ package weaver.workflow.workflow;
/*      */ 
/*      */ import java.util.ArrayList;
/*      */ import java.util.HashMap;
/*      */ import java.util.Hashtable;
/*      */ import java.util.List;
/*      */ import java.util.Map;
/*      */ import javax.servlet.http.HttpServletRequest;
/*      */ import javax.servlet.http.HttpServletResponse;
/*      */ import weaver.conn.RecordSet;
/*      */ import weaver.conn.RecordSetDataSource;
/*      */ import weaver.general.ThreadVarLanguage;
/*      */ import weaver.general.Util;
/*      */ import weaver.hrm.User;
/*      */ import weaver.servicefiles.DataSourceXML;
/*      */ import weaver.systeminfo.SystemEnv;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ public class WfDataSource
/*      */ {
/*      */   public List<Map<String, String>> getWorkflowTable(User paramUser, Map<String, String> paramMap, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/*   31 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*   32 */     RecordSet recordSet1 = new RecordSet();
/*   33 */     RecordSet recordSet2 = new RecordSet();
/*   34 */     int i = Util.getIntValue(paramMap.get("modetype"), -1);
/*   35 */     String str1 = Util.null2String(paramMap.get("tablename"));
/*      */     
/*   37 */     String str2 = Util.null2String(paramHttpServletRequest.getParameter("q"));
/*   38 */     if ("".equals(str1) && !"".equals(str2)) {
/*   39 */       str1 = str2;
/*      */     }
/*   41 */     String str3 = "";
/*   42 */     if (i == 0 || i == -1) {
/*   43 */       str3 = "select id,formname from workflow_formbase where id in (select distinct formid from workflow_base where isvalid=1)";
/*   44 */       if (!str1.equals(""))
/*   45 */         str3 = str3 + " and formname like '%" + str1 + "%'"; 
/*   46 */       recordSet1.executeSql(str3);
/*   47 */       while (recordSet1.next()) {
/*   48 */         HashMap<Object, Object> hashMap = new HashMap<>();
/*   49 */         hashMap.put("id", recordSet1.getString("id"));
/*   50 */         hashMap.put("tablelabelname", recordSet1.getString("formname") + "(" + SystemEnv.getHtmlLabelName(21778, paramUser.getLanguage()) + ")");
/*   51 */         hashMap.put("belongsTo", SystemEnv.getHtmlLabelName(2118, paramUser.getLanguage()));
/*   52 */         hashMap.put("other1", "workflow_form");
/*   53 */         hashMap.put("other2", recordSet1.getString("id"));
/*   54 */         hashMap.put("other3", "-1");
/*   55 */         arrayList.add(hashMap);
/*   56 */         byte b = 0;
/*   57 */         recordSet2.execute("select distinct groupId from workflow_formfield where formid=" + recordSet1
/*   58 */             .getString("id") + " and isdetail=1 order by groupId");
/*   59 */         while (recordSet2.next()) {
/*   60 */           b++;
/*   61 */           HashMap<Object, Object> hashMap1 = new HashMap<>();
/*   62 */           hashMap1.put("id", recordSet1.getString("id") + "_" + recordSet2.getString("groupId"));
/*   63 */           hashMap1.put("tablelabelname", recordSet1.getString("formname") + "(" + SystemEnv.getHtmlLabelName(19325, paramUser.getLanguage()) + b + ")");
/*   64 */           hashMap1.put("belongsTo", SystemEnv.getHtmlLabelName(2118, paramUser.getLanguage()));
/*   65 */           hashMap1.put("other1", "workflow_formdetail");
/*   66 */           hashMap1.put("other2", recordSet1.getString("id"));
/*   67 */           hashMap1.put("other3", recordSet2.getString("groupId"));
/*   68 */           arrayList.add(hashMap1);
/*      */         } 
/*      */       } 
/*      */     } 
/*   72 */     if (i == 0 || i == -1) {
/*   73 */       str3 = "select tablename,id,namelabel,detailtablename from workflow_bill where not exists (select 1 from ModeFormExtend where formid=id and isvirtualform=1) order by id";
/*   74 */       recordSet1.executeSql(str3);
/*   75 */       while (recordSet1.next()) {
/*   76 */         String str4 = SystemEnv.getHtmlLabelName(recordSet1.getInt("namelabel"), paramUser.getLanguage());
/*      */         
/*   78 */         if (str4 == null) {
/*      */           continue;
/*      */         }
/*   81 */         if (!str1.equals("") && 
/*   82 */           str4.indexOf(str1) < 0)
/*      */           continue; 
/*   84 */         HashMap<Object, Object> hashMap = new HashMap<>();
/*   85 */         hashMap.put("id", "0");
/*   86 */         hashMap.put("tablelabelname", str4 + "(" + SystemEnv.getHtmlLabelName(21778, paramUser.getLanguage()) + ")");
/*   87 */         hashMap.put("belongsTo", SystemEnv.getHtmlLabelName(2118, paramUser.getLanguage()));
/*   88 */         hashMap.put("other1", recordSet1.getString(1));
/*   89 */         hashMap.put("other2", recordSet1.getString("id"));
/*   90 */         hashMap.put("other3", "-1");
/*   91 */         arrayList.add(hashMap);
/*   92 */         String str5 = Util.null2String(recordSet1.getString("detailtablename"));
/*   93 */         String str6 = Util.null2String(recordSet1.getString("id"));
/*   94 */         recordSet2.execute("select tablename,orderid from Workflow_billdetailtable where tablename='" + str5 + "'");
/*   95 */         if (!str5.equals("") && !recordSet2.next()) {
/*   96 */           HashMap<Object, Object> hashMap1 = new HashMap<>();
/*   97 */           hashMap1.put("id", "0");
/*   98 */           hashMap1.put("tablelabelname", str4 + "(" + SystemEnv.getHtmlLabelName(19325, paramUser.getLanguage()) + "1)");
/*   99 */           hashMap1.put("belongsTo", SystemEnv.getHtmlLabelName(2118, paramUser.getLanguage()));
/*  100 */           hashMap1.put("other1", str5);
/*  101 */           hashMap1.put("other2", recordSet1.getString("id"));
/*  102 */           hashMap1.put("other3", recordSet2.getString("orderid"));
/*  103 */           arrayList.add(hashMap1);
/*      */         } 
/*  105 */         recordSet2.execute("select tablename,orderid from Workflow_billdetailtable where billid=" + recordSet1
/*  106 */             .getString("id") + " order by orderid");
/*  107 */         byte b = 0;
/*  108 */         while (recordSet2.next()) {
/*  109 */           b++;
/*  110 */           HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  111 */           hashMap1.put("id", "0");
/*  112 */           hashMap1.put("tablelabelname", str4 + "(" + SystemEnv.getHtmlLabelName(19325, paramUser.getLanguage()) + b + ")");
/*  113 */           hashMap1.put("belongsTo", SystemEnv.getHtmlLabelName(2118, paramUser.getLanguage()));
/*  114 */           hashMap1.put("other1", recordSet2.getString(1));
/*  115 */           hashMap1.put("other2", recordSet1.getString("id"));
/*  116 */           hashMap1.put("other3", recordSet2.getString("orderid"));
/*  117 */           arrayList.add(hashMap1);
/*      */         } 
/*      */       } 
/*      */     } 
/*  121 */     if (paramUser.getLanguage() == 7 || paramUser.getLanguage() == 9) {
/*  122 */       str3 = "select tablename,tabledesc,modetype from Sys_tabledict";
/*      */     } else {
/*  124 */       str3 = "select tablename,tabledescen,modetype from Sys_tabledict";
/*      */     } 
/*  126 */     str3 = str3 + " where 1=1 ";
/*  127 */     if (i != -1) {
/*  128 */       str3 = str3 + " and modetype=" + i;
/*      */     }
/*  130 */     if (!str1.equals("")) {
/*  131 */       if (paramUser.getLanguage() == 7 || paramUser.getLanguage() == 9) {
/*  132 */         str3 = str3 + " and tabledesc like '%" + str1 + "%'";
/*      */       } else {
/*  134 */         str3 = str3 + " and tabledescen like '%" + str1 + "%'";
/*      */       } 
/*      */     }
/*  137 */     str3 = str3 + " order by modetype";
/*  138 */     recordSet1.executeSql(str3);
/*  139 */     while (recordSet1.next()) {
/*  140 */       String str4 = Util.null2String(recordSet1.getString("modetype"));
/*  141 */       String str5 = "";
/*  142 */       if (str4.equals("1"))
/*  143 */         str5 = SystemEnv.getHtmlLabelName(179, paramUser.getLanguage()); 
/*  144 */       if (str4.equals("2"))
/*  145 */         str5 = SystemEnv.getHtmlLabelName(2115, paramUser.getLanguage()); 
/*  146 */       if (str4.equals("3"))
/*  147 */         str5 = SystemEnv.getHtmlLabelName(2113, paramUser.getLanguage()); 
/*  148 */       if (str4.equals("4"))
/*  149 */         str5 = SystemEnv.getHtmlLabelName(2114, paramUser.getLanguage()); 
/*  150 */       if (str4.equals("5"))
/*  151 */         str5 = SystemEnv.getHtmlLabelName(2116, paramUser.getLanguage()); 
/*  152 */       if (str4.equals("6"))
/*  153 */         str5 = SystemEnv.getHtmlLabelName(2117, paramUser.getLanguage()); 
/*  154 */       if (str4.equals("7"))
/*  155 */         str5 = SystemEnv.getHtmlLabelName(18442, paramUser.getLanguage()); 
/*  156 */       if (str4.equals("8"))
/*  157 */         str5 = SystemEnv.getHtmlLabelName(17629, paramUser.getLanguage()); 
/*  158 */       HashMap<Object, Object> hashMap = new HashMap<>();
/*  159 */       hashMap.put("id", "0");
/*  160 */       hashMap.put("tablelabelname", recordSet1.getString(2) + "(" + SystemEnv.getHtmlLabelName(21778, paramUser.getLanguage()) + ")");
/*  161 */       hashMap.put("belongsTo", str5);
/*  162 */       hashMap.put("other1", recordSet1.getString(1));
/*  163 */       hashMap.put("other2", "-1");
/*  164 */       hashMap.put("other3", "-1");
/*  165 */       arrayList.add(hashMap);
/*      */     } 
/*  167 */     return (List)arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public List<Map<String, String>> getWorkflowTableForE9(User paramUser, Map<String, String> paramMap, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/*  174 */     List<Map<String, String>> list = getWorkflowTable(paramUser, paramMap, paramHttpServletRequest, paramHttpServletResponse);
/*  175 */     for (Map<String, String> map : list) {
/*  176 */       map.put("id", (String)map.get("id") + "?" + Util.getRandom());
/*      */     }
/*      */     
/*  179 */     return list;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public List<Map<String, String>> getWorkflowTableForE92(User paramUser, Map<String, String> paramMap, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/*  186 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*  187 */     RecordSet recordSet1 = new RecordSet();
/*  188 */     RecordSet recordSet2 = new RecordSet();
/*  189 */     int i = Util.getIntValue(paramMap.get("modetype"), -1);
/*  190 */     int j = Util.getIntValue(paramMap.get("searchflag"), -1);
/*  191 */     String str1 = Util.null2String(paramMap.get("tablename"));
/*  192 */     String str2 = "";
/*  193 */     if (i == 0 || i == -1) {
/*  194 */       str2 = "select id,formname from workflow_formbase where id in (select distinct formid from workflow_base where isvalid=1)";
/*  195 */       if (!str1.equals(""))
/*  196 */         str2 = str2 + " and formname like '%" + str1 + "%'"; 
/*  197 */       recordSet1.executeSql(str2);
/*  198 */       while (recordSet1.next()) {
/*  199 */         HashMap<Object, Object> hashMap = new HashMap<>();
/*  200 */         hashMap.put("id", "workflow_form");
/*  201 */         hashMap.put("key", "workflow_form");
/*  202 */         hashMap.put("tablelabelname", recordSet1.getString("formname") + "(" + SystemEnv.getHtmlLabelName(21778, paramUser.getLanguage()) + ")");
/*  203 */         hashMap.put("belongsTo", SystemEnv.getHtmlLabelName(2118, paramUser.getLanguage()));
/*  204 */         hashMap.put("other1", "workflow_form");
/*  205 */         hashMap.put("other2", recordSet1.getString("id"));
/*  206 */         hashMap.put("other3", "-1");
/*  207 */         arrayList.add(hashMap);
/*  208 */         byte b = 0;
/*  209 */         recordSet2.execute("select distinct groupId from workflow_formfield where formid=" + recordSet1
/*  210 */             .getString("id") + " and isdetail=1 order by groupId");
/*  211 */         while (recordSet2.next()) {
/*  212 */           b++;
/*  213 */           HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  214 */           hashMap1.put("id", "workflow_formdetail");
/*  215 */           hashMap1.put("key", "workflow_formdetail");
/*  216 */           hashMap1.put("tablelabelname", recordSet1.getString("formname") + "(" + SystemEnv.getHtmlLabelName(19325, paramUser.getLanguage()) + b + ")");
/*  217 */           hashMap1.put("belongsTo", SystemEnv.getHtmlLabelName(2118, paramUser.getLanguage()));
/*  218 */           hashMap1.put("other1", "workflow_formdetail");
/*  219 */           hashMap1.put("other2", recordSet1.getString("id"));
/*  220 */           hashMap1.put("other3", recordSet2.getString("groupId"));
/*  221 */           arrayList.add(hashMap1);
/*      */         } 
/*      */       } 
/*      */     } 
/*  225 */     if (i == 0 || i == -1) {
/*  226 */       if (j == 1) {
/*  227 */         str2 = "select tablename,id,namelabel,detailtablename from workflow_bill a  left join ModeFormExtend c on a.id=c.formid  where  (isvirtualform is null or isvirtualform != 1)  order by id";
/*      */       
/*      */       }
/*      */       else {
/*      */         
/*  232 */         str2 = "select tablename,id,namelabel,detailtablename from workflow_bill order by id";
/*      */       } 
/*  234 */       recordSet1.executeSql(str2);
/*  235 */       while (recordSet1.next()) {
/*  236 */         String str3 = SystemEnv.getHtmlLabelName(recordSet1.getInt("namelabel"), paramUser.getLanguage());
/*      */         
/*  238 */         if (str3 == null) {
/*      */           continue;
/*      */         }
/*  241 */         if (!str1.equals("") && 
/*  242 */           str3.indexOf(str1) < 0)
/*      */           continue; 
/*  244 */         HashMap<Object, Object> hashMap = new HashMap<>();
/*  245 */         hashMap.put("id", recordSet1.getString(1));
/*  246 */         hashMap.put("key", recordSet1.getString(1));
/*  247 */         hashMap.put("tablelabelname", str3 + "(" + SystemEnv.getHtmlLabelName(21778, paramUser.getLanguage()) + ")");
/*  248 */         hashMap.put("belongsTo", SystemEnv.getHtmlLabelName(2118, paramUser.getLanguage()));
/*  249 */         hashMap.put("other1", recordSet1.getString(1));
/*  250 */         hashMap.put("other2", recordSet1.getString("id"));
/*  251 */         hashMap.put("other3", "-1");
/*  252 */         arrayList.add(hashMap);
/*  253 */         String str4 = Util.null2String(recordSet1.getString("detailtablename"));
/*  254 */         String str5 = Util.null2String(recordSet1.getString("id"));
/*  255 */         recordSet2.execute("select tablename,orderid from Workflow_billdetailtable where tablename='" + str4 + "'");
/*  256 */         if (!str4.equals("") && !recordSet2.next()) {
/*  257 */           HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  258 */           hashMap1.put("id", str4);
/*  259 */           hashMap1.put("key", Util.getRandom());
/*  260 */           hashMap1.put("tablelabelname", str3 + "(" + SystemEnv.getHtmlLabelName(19325, paramUser.getLanguage()) + "1)");
/*  261 */           hashMap1.put("belongsTo", SystemEnv.getHtmlLabelName(2118, paramUser.getLanguage()));
/*  262 */           hashMap1.put("other1", str4);
/*  263 */           hashMap1.put("other2", recordSet1.getString("id"));
/*  264 */           hashMap1.put("other3", recordSet2.getString("orderid"));
/*  265 */           arrayList.add(hashMap1);
/*      */         } 
/*  267 */         recordSet2.execute("select tablename,orderid from Workflow_billdetailtable where billid=" + recordSet1
/*  268 */             .getString("id") + " order by orderid");
/*  269 */         byte b = 0;
/*  270 */         while (recordSet2.next()) {
/*  271 */           b++;
/*  272 */           HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  273 */           hashMap1.put("id", recordSet2.getString(1));
/*  274 */           hashMap1.put("key", Util.getRandom());
/*  275 */           hashMap1.put("tablelabelname", str3 + "(" + SystemEnv.getHtmlLabelName(19325, paramUser.getLanguage()) + b + ")");
/*  276 */           hashMap1.put("belongsTo", SystemEnv.getHtmlLabelName(2118, paramUser.getLanguage()));
/*  277 */           hashMap1.put("other1", recordSet2.getString(1));
/*  278 */           hashMap1.put("other2", recordSet1.getString("id"));
/*  279 */           hashMap1.put("other3", recordSet2.getString("orderid"));
/*  280 */           arrayList.add(hashMap1);
/*      */         } 
/*      */       } 
/*      */     } 
/*  284 */     if (paramUser.getLanguage() == 7) {
/*  285 */       str2 = "select tablename,tabledesc,modetype from Sys_tabledict";
/*  286 */     } else if (paramUser.getLanguage() == 8) {
/*  287 */       str2 = "select tablename,tabledescen,modetype from Sys_tabledict";
/*      */     } 
/*  289 */     str2 = str2 + " where 1=1 ";
/*  290 */     if (i != -1) {
/*  291 */       str2 = str2 + " and modetype=" + i;
/*      */     }
/*  293 */     if (!str1.equals(""))
/*  294 */       if (paramUser.getLanguage() == 7) {
/*  295 */         str2 = str2 + " and tabledesc like '%" + str1 + "%'";
/*  296 */       } else if (paramUser.getLanguage() == 8) {
/*  297 */         str2 = str2 + " and tabledescen like '%" + str1 + "%'";
/*      */       }  
/*  299 */     str2 = str2 + " order by modetype";
/*  300 */     recordSet1.executeSql(str2);
/*  301 */     while (recordSet1.next()) {
/*  302 */       String str3 = Util.null2String(recordSet1.getString("modetype"));
/*  303 */       String str4 = "";
/*  304 */       if (str3.equals("1"))
/*  305 */         str4 = SystemEnv.getHtmlLabelName(179, paramUser.getLanguage()); 
/*  306 */       if (str3.equals("2"))
/*  307 */         str4 = SystemEnv.getHtmlLabelName(2115, paramUser.getLanguage()); 
/*  308 */       if (str3.equals("3"))
/*  309 */         str4 = SystemEnv.getHtmlLabelName(2113, paramUser.getLanguage()); 
/*  310 */       if (str3.equals("4"))
/*  311 */         str4 = SystemEnv.getHtmlLabelName(2114, paramUser.getLanguage()); 
/*  312 */       if (str3.equals("5"))
/*  313 */         str4 = SystemEnv.getHtmlLabelName(2116, paramUser.getLanguage()); 
/*  314 */       if (str3.equals("6"))
/*  315 */         str4 = SystemEnv.getHtmlLabelName(2117, paramUser.getLanguage()); 
/*  316 */       if (str3.equals("7"))
/*  317 */         str4 = SystemEnv.getHtmlLabelName(18442, paramUser.getLanguage()); 
/*  318 */       HashMap<Object, Object> hashMap = new HashMap<>();
/*  319 */       hashMap.put("id", recordSet1.getString(1));
/*  320 */       hashMap.put("key", Util.getRandom());
/*  321 */       hashMap.put("tablelabelname", recordSet1.getString(2) + "(" + SystemEnv.getHtmlLabelName(21778, paramUser.getLanguage()) + ")");
/*  322 */       hashMap.put("belongsTo", str4);
/*  323 */       hashMap.put("other1", recordSet1.getString(1));
/*  324 */       hashMap.put("other2", "-1");
/*  325 */       hashMap.put("other3", "-1");
/*  326 */       arrayList.add(hashMap);
/*      */     } 
/*      */     
/*  329 */     for (Map<String, String> map : arrayList) {
/*  330 */       map.put("randomFieldId", Util.getRandom());
/*      */     }
/*      */     
/*  333 */     return (List)arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public List<Map<String, String>> getWorkflowTableField(User paramUser, Map<String, String> paramMap, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/*  348 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*  349 */     String str1 = Util.null2String(paramMap.get("searchtablename"));
/*  350 */     String str2 = Util.null2String(paramMap.get("searchfieldname"));
/*  351 */     String str3 = Util.null2String(paramMap.get("datasourceid"));
/*  352 */     if (str1 != null) {
/*  353 */       str1 = str1.trim();
/*      */     }
/*  355 */     if (str2 != null) {
/*  356 */       str2 = str2.trim();
/*      */     }
/*  358 */     if (str3 != null) {
/*  359 */       str3 = str3.trim();
/*      */     }
/*  361 */     String str4 = paramMap.get("tablenames");
/*  362 */     ArrayList<String> arrayList1 = Util.TokenizerString(str4, ",");
/*  363 */     String str5 = "";
/*  364 */     boolean bool = false;
/*  365 */     RecordSetDataSource recordSetDataSource = new RecordSetDataSource();
/*  366 */     RecordSet recordSet1 = new RecordSet();
/*  367 */     RecordSet recordSet2 = new RecordSet();
/*  368 */     RecordSet recordSet3 = new RecordSet();
/*  369 */     for (byte b = 0; b < arrayList1.size(); b++) {
/*  370 */       String str6 = arrayList1.get(b);
/*  371 */       String str7 = "";
/*  372 */       String str8 = "";
/*  373 */       String str9 = "";
/*  374 */       String[] arrayOfString = str6.split("~");
/*  375 */       str7 = arrayOfString[0];
/*  376 */       str8 = arrayOfString[1];
/*  377 */       String str10 = "";
/*  378 */       String str11 = "";
/*  379 */       String str12 = "";
/*  380 */       if (arrayOfString.length == 4) {
/*  381 */         str10 = arrayOfString[2];
/*  382 */         str11 = arrayOfString[3];
/*      */       } 
/*  384 */       if (str3 != null && !str3.equals(""))
/*  385 */       { if (str1.equalsIgnoreCase("") || str1.equalsIgnoreCase(str8)) {
/*      */ 
/*      */           
/*  388 */           ArrayList<String> arrayList2 = recordSetDataSource.getAllColumns(str3, str8);
/*  389 */           for (byte b1 = 0; b1 < arrayList2.size(); b1++) {
/*  390 */             String str = Util.null2String(arrayList2.get(b1));
/*  391 */             if ("".equals(str2) || str.toUpperCase().indexOf(str2.toUpperCase()) != -1) {
/*      */ 
/*      */               
/*  394 */               HashMap<Object, Object> hashMap = new HashMap<>();
/*  395 */               hashMap.put("fieldname", str);
/*  396 */               hashMap.put("fieldlabel", str);
/*  397 */               hashMap.put("tablename", str8);
/*  398 */               hashMap.put("tablelabel", str8);
/*  399 */               if (str10.equals("-")) {
/*  400 */                 str12 = str8 + ".";
/*      */               } else {
/*  402 */                 str12 = str10 + ".";
/*      */               } 
/*  404 */               hashMap.put("tabfix", str12);
/*  405 */               arrayList.add(hashMap);
/*      */             } 
/*      */           } 
/*      */         }  }
/*  409 */       else { if (str10.equals("-")) {
/*  410 */           if (str11.equals("") || str11.equalsIgnoreCase("null")) {
/*  411 */             str12 = "";
/*      */           } else {
/*  413 */             str12 = str11 + ".";
/*      */           } 
/*      */         } else {
/*  416 */           str12 = str10 + ".";
/*      */         } 
/*  418 */         if (!"".equals(str7) && !"0".equals(str7)) {
/*  419 */           int i = 0;
/*  420 */           if (str8.equals("workflow_form"))
/*  421 */             str5 = "select fieldid from workflow_formfield where isdetail is null and formid=" + str7; 
/*  422 */           if (str8.equals("workflow_formdetail")) {
/*  423 */             i = Util.getIntValue(str7.substring(str7.indexOf("_") + 1, str7.length()), 0);
/*  424 */             str7 = str7.substring(0, str7.indexOf("_"));
/*  425 */             str5 = "select fieldid from workflow_formfield where groupId=" + i + " and isdetail=1 and formid=" + str7;
/*      */           } 
/*      */           
/*  428 */           recordSet1.executeSql("select formname from workflow_formbase where id=" + str7);
/*  429 */           if (recordSet1.next())
/*  430 */             str9 = recordSet1.getString("formname"); 
/*  431 */           if (str8.equals("workflow_formdetail")) {
/*  432 */             str9 = str9 + "(" + SystemEnv.getHtmlLabelName(19325, paramUser.getLanguage()) + (i + 1) + ")";
/*      */           } else {
/*  434 */             str9 = str9 + "(" + SystemEnv.getHtmlLabelName(21778, paramUser.getLanguage()) + ")";
/*      */           } 
/*  436 */           if (!str1.equals("") && !str1.equals(str7))
/*  437 */             str5 = str5 + " and 1=2"; 
/*  438 */           recordSet1.executeSql(str5);
/*  439 */           boolean bool1 = true;
/*  440 */           while (recordSet1.next()) {
/*  441 */             String str13 = recordSet1.getString("fieldid");
/*  442 */             recordSet3.executeSql("select fieldid,fieldlable from workflow_fieldlable where langurageid=" + paramUser
/*  443 */                 .getLanguage() + " and formid=" + str7 + " and fieldid=" + str13);
/*  444 */             recordSet3.next();
/*  445 */             if (!str2.equals("") && recordSet3.getString("fieldlable").indexOf(str2) < 0)
/*      */               continue; 
/*  447 */             if (bool1 && !str8.equals("workflow_formdetail") && "".equals(str2)) {
/*  448 */               bool1 = false;
/*  449 */               HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  450 */               hashMap1.put("fieldname", "requestid");
/*  451 */               hashMap1.put("fieldlabel", SystemEnv.getHtmlLabelName(648, paramUser.getLanguage()) + "ID");
/*  452 */               hashMap1.put("tablename", str8);
/*  453 */               hashMap1.put("tablelabel", str9);
/*  454 */               hashMap1.put("tabfix", str12);
/*  455 */               arrayList.add(hashMap1);
/*      */             } 
/*  457 */             String str14 = "";
/*  458 */             if (str8.equals("workflow_formdetail")) {
/*  459 */               str14 = "select fieldname from workflow_formdictdetail where id=" + str13;
/*      */             } else {
/*  461 */               str14 = "select fieldname from workflow_formdict where id=" + str13;
/*  462 */             }  recordSet2.executeSql(str14);
/*  463 */             HashMap<Object, Object> hashMap = new HashMap<>();
/*  464 */             if (recordSet2.next()) {
/*  465 */               hashMap.put("fieldname", recordSet2.getString("fieldname"));
/*      */             }
/*  467 */             hashMap.put("fieldlabel", recordSet3.getString("fieldlable"));
/*  468 */             hashMap.put("tablename", str8);
/*  469 */             hashMap.put("tablelabel", str9);
/*  470 */             hashMap.put("tabfix", str12);
/*  471 */             arrayList.add(hashMap);
/*      */           }
/*      */         
/*  474 */         } else if (str1.equals("") || str8.equals(str1)) {
/*      */           
/*  476 */           if (!str1.equals("") && str8.equals(str1)) {
/*  477 */             str8 = str1;
/*      */           }
/*  479 */           recordSet1.executeSql("select id,namelabel from workflow_bill where tablename='" + str8 + "'");
/*  480 */           boolean bool1 = true;
/*  481 */           if (recordSet1.next()) {
/*  482 */             String str = recordSet1.getString("id");
/*      */             
/*  484 */             str9 = SystemEnv.getHtmlLabelName(recordSet1.getInt("namelabel"), paramUser.getLanguage()) + "(" + SystemEnv.getHtmlLabelName(21778, paramUser.getLanguage()) + ")";
/*  485 */             recordSet3.executeSql("select fieldname,fieldlabel from workflow_billfield where viewtype=0 and billid=" + str);
/*  486 */             while (recordSet3.next()) {
/*  487 */               String str13 = recordSet3.getString("fieldname");
/*  488 */               String str14 = SystemEnv.getHtmlLabelName(recordSet3.getInt("fieldlabel"), paramUser.getLanguage());
/*  489 */               if (!str2.equals("") && str14.indexOf(str2) < 0)
/*      */                 continue; 
/*  491 */               if (bool1) {
/*  492 */                 bool1 = false;
/*      */                 
/*  494 */                 HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  495 */                 hashMap1.put("fieldname", "id");
/*  496 */                 hashMap1.put("fieldlabel", "" + SystemEnv.getHtmlLabelName(563, ThreadVarLanguage.getLang()) + "ID");
/*  497 */                 hashMap1.put("tablename", str8);
/*  498 */                 hashMap1.put("tablelabel", str9);
/*  499 */                 hashMap1.put("tabfix", str12);
/*  500 */                 arrayList.add(hashMap1);
/*      */                 
/*  502 */                 hashMap1 = new HashMap<>();
/*  503 */                 hashMap1.put("fieldname", "requestid");
/*  504 */                 hashMap1.put("fieldlabel", SystemEnv.getHtmlLabelName(648, paramUser.getLanguage()) + "ID");
/*  505 */                 hashMap1.put("tablename", str8);
/*  506 */                 hashMap1.put("tablelabel", str9);
/*  507 */                 hashMap1.put("tabfix", str12);
/*  508 */                 arrayList.add(hashMap1);
/*      */               } 
/*  510 */               HashMap<Object, Object> hashMap = new HashMap<>();
/*  511 */               hashMap.put("fieldname", str13);
/*  512 */               hashMap.put("fieldlabel", str14);
/*  513 */               hashMap.put("tablename", str8);
/*  514 */               hashMap.put("tablelabel", str9);
/*  515 */               hashMap.put("tabfix", str12);
/*  516 */               arrayList.add(hashMap);
/*      */             } 
/*      */           } 
/*  519 */           boolean bool2 = true;
/*  520 */           recordSet1.executeSql("select id,namelabel from workflow_bill where detailtablename='" + str8 + "'");
/*  521 */           recordSet3.executeSql("select tablename from Workflow_billdetailtable where tablename='" + str8 + "'");
/*  522 */           if (recordSet1.next() && !recordSet3.next()) {
/*      */             
/*  524 */             bool2 = true;
/*  525 */             String str = recordSet1.getString("id");
/*      */             
/*  527 */             str9 = SystemEnv.getHtmlLabelName(recordSet1.getInt("namelabel"), paramUser.getLanguage()) + "(" + SystemEnv.getHtmlLabelName(19325, paramUser.getLanguage()) + "1)";
/*  528 */             recordSet3.executeSql("select fieldname,fieldlabel from workflow_billfield where viewtype=1 and billid=" + str);
/*  529 */             while (recordSet3.next()) {
/*  530 */               String str13 = recordSet3.getString("fieldname");
/*  531 */               String str14 = SystemEnv.getHtmlLabelName(recordSet3.getInt("fieldlabel"), paramUser.getLanguage());
/*  532 */               if (!str2.equals("") && str14.indexOf(str2) < 0) {
/*      */                 continue;
/*      */               }
/*  535 */               if (bool2) {
/*  536 */                 bool2 = false;
/*      */                 
/*  538 */                 HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  539 */                 hashMap1.put("fieldname", "ID");
/*  540 */                 hashMap1.put("fieldlabel", SystemEnv.getHtmlLabelName(81287, Util.getIntValue("" + paramUser.getLanguage(), 7)));
/*  541 */                 hashMap1.put("tablename", str8);
/*  542 */                 hashMap1.put("tablelabel", str9);
/*  543 */                 hashMap1.put("tabfix", str12);
/*  544 */                 arrayList.add(hashMap1);
/*      */               } 
/*      */               
/*  547 */               HashMap<Object, Object> hashMap = new HashMap<>();
/*  548 */               hashMap.put("fieldname", str13);
/*  549 */               hashMap.put("fieldlabel", str14);
/*  550 */               hashMap.put("tablename", str8);
/*  551 */               hashMap.put("tablelabel", str9);
/*  552 */               hashMap.put("tabfix", str12);
/*  553 */               arrayList.add(hashMap);
/*      */             } 
/*      */           } 
/*  556 */           recordSet1.executeSql("select billid from Workflow_billdetailtable where tablename='" + str8 + "'");
/*  557 */           if (recordSet1.next()) {
/*  558 */             String str = recordSet1.getString("billid");
/*  559 */             recordSet1.executeSql("select namelabel from workflow_bill where id=" + str);
/*  560 */             if (recordSet1.next()) {
/*  561 */               str9 = SystemEnv.getHtmlLabelName(recordSet1.getInt("namelabel"), paramUser.getLanguage());
/*      */             }
/*  563 */             recordSet1.executeSql("select tablename from Workflow_billdetailtable where billid=" + str + " order by orderid");
/*      */             
/*  565 */             byte b1 = 0;
/*  566 */             while (recordSet1.next()) {
/*  567 */               b1++;
/*  568 */               String str13 = Util.null2String(recordSet1.getString("tablename"));
/*  569 */               if (str13.equals(str8)) {
/*  570 */                 str9 = str9 + "(" + SystemEnv.getHtmlLabelName(19325, paramUser.getLanguage()) + b1 + ")";
/*      */               }
/*      */             } 
/*      */             
/*  574 */             bool2 = true;
/*  575 */             recordSet3.executeSql("select fieldname,fieldlabel from workflow_billfield where viewtype=1 and detailtable='" + str8 + "' and billid=" + str);
/*      */             
/*  577 */             while (recordSet3.next()) {
/*  578 */               String str13 = recordSet3.getString("fieldname");
/*  579 */               String str14 = SystemEnv.getHtmlLabelName(recordSet3.getInt("fieldlabel"), paramUser.getLanguage());
/*  580 */               if (!str2.equals("") && str14.indexOf(str2) < 0) {
/*      */                 continue;
/*      */               }
/*  583 */               if (bool2) {
/*  584 */                 bool2 = false;
/*      */                 
/*  586 */                 HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  587 */                 hashMap1.put("fieldname", "ID");
/*  588 */                 hashMap1.put("fieldlabel", SystemEnv.getHtmlLabelName(81287, Util.getIntValue("" + paramUser.getLanguage(), 7)));
/*  589 */                 hashMap1.put("tablename", str8);
/*  590 */                 hashMap1.put("tablelabel", str9);
/*  591 */                 hashMap1.put("tabfix", str12);
/*  592 */                 arrayList.add(hashMap1);
/*      */               } 
/*  594 */               HashMap<Object, Object> hashMap = new HashMap<>();
/*  595 */               hashMap.put("fieldname", str13);
/*  596 */               hashMap.put("fieldlabel", str14);
/*  597 */               hashMap.put("tablename", str8);
/*  598 */               hashMap.put("tablelabel", str9);
/*  599 */               hashMap.put("tabfix", str12);
/*  600 */               arrayList.add(hashMap);
/*      */             } 
/*      */           } 
/*  603 */           recordSet1.executeSql("select id,tabledesc,tabledescen from Sys_tabledict where tablename='" + str8 + "'");
/*      */           
/*  605 */           if (recordSet1.next()) {
/*  606 */             String str = recordSet1.getString("id");
/*  607 */             if (paramUser.getLanguage() == 7)
/*  608 */               str9 = recordSet1.getString("tabledesc"); 
/*  609 */             if (paramUser.getLanguage() == 8)
/*  610 */               str9 = recordSet1.getString("tabledescen"); 
/*  611 */             str9 = str9 + "(" + SystemEnv.getHtmlLabelName(21778, paramUser.getLanguage()) + ")";
/*  612 */             recordSet3.executeSql("select fieldname,fielddesc,fielddescen from Sys_fielddict where tabledictid=" + str + " order by dsporder");
/*      */             
/*  614 */             while (recordSet3.next()) {
/*  615 */               String str13 = recordSet3.getString("fieldname");
/*  616 */               String str14 = "";
/*  617 */               if (paramUser.getLanguage() == 7)
/*  618 */                 str14 = recordSet3.getString("fielddesc"); 
/*  619 */               if (paramUser.getLanguage() == 8)
/*  620 */                 str14 = recordSet3.getString("fielddescen"); 
/*  621 */               if (!str2.equals("") && str14.indexOf(str2) < 0)
/*      */                 continue; 
/*  623 */               HashMap<Object, Object> hashMap = new HashMap<>();
/*  624 */               hashMap.put("fieldname", str13);
/*  625 */               hashMap.put("fieldlabel", str14);
/*  626 */               hashMap.put("tablename", str8);
/*  627 */               hashMap.put("tablelabel", str9);
/*  628 */               hashMap.put("tabfix", str12);
/*  629 */               arrayList.add(hashMap);
/*      */             } 
/*      */           } 
/*      */         }  }
/*      */     
/*      */     } 
/*  635 */     return (List)arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   public List<Map<String, String>> getWorkflowTableTriggerField(User paramUser, Map<String, String> paramMap, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/*  641 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*  642 */     int i = Util.getIntValue(paramMap.get("wfid"), 0);
/*  643 */     String str1 = Util.null2String(paramMap.get("htmltype"));
/*  644 */     String str2 = Util.null2String(paramMap.get("type"));
/*  645 */     String str3 = Util.null2String(paramMap.get("fielddbtype"));
/*  646 */     int j = Util.getIntValue(paramMap.get("tabletype"), -1);
/*  647 */     int k = Util.getIntValue(paramMap.get("ise9"), -1);
/*  648 */     String str4 = Util.null2String(paramMap.get("fieldname"));
/*  649 */     String str5 = Util.toSqlForSplitPage(paramMap.get("sqlwhere"));
/*  650 */     String str6 = Util.null2String(paramMap.get("isfrom"));
/*  651 */     RecordSet recordSet1 = new RecordSet();
/*  652 */     RecordSet recordSet2 = new RecordSet();
/*  653 */     RecordSet recordSet3 = new RecordSet();
/*      */     
/*  655 */     boolean bool = false;
/*  656 */     if ("".equals(str5) && !"".equals(Integer.valueOf(i))) {
/*  657 */       bool = false;
/*      */     } else {
/*  659 */       bool = true;
/*      */     } 
/*  661 */     String str7 = "";
/*  662 */     String str8 = "";
/*  663 */     String str9 = "";
/*  664 */     recordSet1.executeSql("select formid,isbill from workflow_base where id=" + i);
/*  665 */     if (recordSet1.next()) {
/*  666 */       str9 = recordSet1.getString("formid");
/*  667 */       str8 = recordSet1.getString("isbill");
/*      */     } 
/*      */     
/*  670 */     if (!bool) {
/*  671 */       if (str8.equals("0")) {
/*  672 */         String str = "workflow_formdict";
/*  673 */         int m = paramUser.getLanguage();
/*  674 */         if (j > 0) str = "workflow_formdictdetail"; 
/*  675 */         if (m != 7) {
/*  676 */           str7 = "select a.fieldid, b.fieldlable, a.isdetail, a.fieldorder, '' as description, a.groupid as optionkey,c.fieldhtmltype,c.type from workflow_formfield a, workflow_fieldlable b, " + str + " c  where a.formid=b.formid and a.fieldid=b.fieldid AND a.fieldid = c.id and a.formid=" + str9 + " and (  b.fieldlable !='' or b.FIELDLABLE is not null) and b.langurageid = " + m;
/*      */ 
/*      */ 
/*      */           
/*  680 */           RecordSet recordSet = new RecordSet();
/*  681 */           recordSet.executeQuery(str7, new Object[0]);
/*  682 */           if (!recordSet.next()) {
/*  683 */             m = 7;
/*      */           }
/*      */         } 
/*      */         
/*  687 */         str7 = "select a.fieldid, b.fieldlable, a.isdetail, a.fieldorder, '' as description, a.groupid as optionkey,c.fieldhtmltype,c.type from workflow_formfield a, workflow_fieldlable b, " + str + " c  where a.formid=b.formid and a.fieldid=b.fieldid AND a.fieldid = c.id and a.formid=" + str9 + " and b.langurageid = " + m;
/*      */ 
/*      */         
/*  690 */         if (j == 0)
/*  691 */           str7 = str7 + " and a.isdetail is null "; 
/*  692 */         if (j > 0) {
/*  693 */           str7 = str7 + " and a.isdetail=1 ";
/*      */         }
/*      */         
/*  696 */         if (!str4.equals("")) {
/*  697 */           str7 = str7 + " and b.fieldlable like '%" + str4 + "%' ";
/*      */         }
/*  699 */         if (k == 1) {
/*  700 */           str7 = str7 + " UNION select a.fieldid, b.fieldlable, a.isdetail, a.fieldorder, '' as description, a.groupid as optionkey,c.fieldhtmltype,c.type from workflow_formfield a, workflow_fieldlable b,  workflow_formdictdetail c  where a.formid=b.formid and a.fieldid=b.fieldid AND a.fieldid = c.id and a.formid=" + str9 + " and b.langurageid = " + m;
/*      */ 
/*      */           
/*  703 */           if (j == 0)
/*  704 */             str7 = str7 + " and a.isdetail is null "; 
/*  705 */           if (j > 0)
/*  706 */             str7 = str7 + " and a.isdetail=1 "; 
/*  707 */           if (!str4.equals("")) {
/*  708 */             str7 = str7 + " and b.fieldlable like '%" + str4 + "%' ";
/*      */           }
/*  710 */           if (recordSet1.getDBType().equals("oracle")) {
/*  711 */             str7 = str7 + " order by isdetail desc,optionkey asc,fieldorder asc ";
/*      */           } else {
/*  713 */             str7 = str7 + " order by isdetail,optionkey,fieldorder ";
/*      */           }
/*      */         
/*  716 */         } else if (recordSet1.getDBType().equals("oracle")) {
/*  717 */           str7 = str7 + " order by a.isdetail desc,optionkey asc,a.fieldorder asc ";
/*      */         } else {
/*  719 */           str7 = str7 + " order by a.isdetail,optionkey,a.fieldorder ";
/*      */         
/*      */         }
/*      */ 
/*      */       
/*      */       }
/*  725 */       else if (str8.equals("1")) {
/*  726 */         str7 = "select id as fieldid,fieldlabel,viewtype as isdetail,dsporder as fieldorder, '' as description, detailtable as optionkey,fieldhtmltype,type from workflow_billfield where billid=" + str9;
/*      */         
/*  728 */         if (j == 0)
/*  729 */           str7 = str7 + " and viewtype=0"; 
/*  730 */         if (j > 0)
/*  731 */           str7 = str7 + " and viewtype=1"; 
/*  732 */         if (!"".equals(str1)) {
/*  733 */           str7 = str7 + " and fieldhtmltype in(" + str1 + ")";
/*      */         }
/*  735 */         if (!"".equals(str2)) {
/*  736 */           str7 = str7 + " and type in(" + str2 + ")";
/*      */         }
/*  738 */         if (!"".equals(str3)) {
/*  739 */           str7 = str7 + " and fielddbtype='" + str3 + "' ";
/*      */         }
/*      */ 
/*      */         
/*  743 */         str7 = str7 + " order by viewtype,optionkey,dsporder";
/*      */       } 
/*      */     } else {
/*  746 */       str8 = paramMap.get("isbill");
/*  747 */       int m = Util.getIntValue(paramMap.get("isdetail"), 0);
/*  748 */       if ("1".equals(str8)) {
/*  749 */         if (m == 0) {
/*  750 */           str7 = "select id as fieldid, fieldlabel, 0 as isdetail, 0 as fieldorder, '' as description, '' as optionkey,fieldhtmltype,type from workflow_billfield " + str5 + " and viewtype = 0";
/*      */         } else {
/*      */           
/*  753 */           str7 = "select id as fieldid, fieldlabel, 1 as isdetail, 0 as fieldorder, '' as description, '' as optionkey,fieldhtmltype,type from workflow_billfield " + str5 + " and viewtype <> 0";
/*      */         }
/*      */       
/*      */       }
/*  757 */       else if (m == 0) {
/*  758 */         str7 = "select id as fieldid, fieldname as fieldlable, 0 as isdetail, 0 as fieldorder, description, '' as optionkey,fieldhtmltype,type from workflow_formdict " + str5;
/*      */       } else {
/*      */         
/*  761 */         str7 = "select id as fieldid, fieldname as fieldlable, 1 as isdetail, 0 as fieldorder, description, '' as optionkey,fieldhtmltype,type from workflow_formdictdetail " + str5;
/*      */       } 
/*      */ 
/*      */       
/*  765 */       str7 = str7 + " order by id";
/*      */     } 
/*  767 */     String str10 = "";
/*  768 */     String str11 = "";
/*  769 */     Hashtable<Object, Object> hashtable = new Hashtable<>();
/*  770 */     if (!bool) {
/*  771 */       if (str8.equals("0")) {
/*      */ 
/*      */         
/*  774 */         str11 = "select a.fieldid, b.fieldlable, a.isdetail, a.fieldorder from workflow_formfield a, workflow_fieldlable b  where a.isdetail is null and a.formid=b.formid and a.fieldid=b.fieldid and a.formid=" + str9 + " and b.langurageid = " + paramUser.getLanguage();
/*  775 */         if (recordSet1.getDBType().equals("oracle")) {
/*  776 */           str11 = str11 + " order by a.isdetail desc,a.fieldorder asc ";
/*      */         } else {
/*  778 */           str11 = str11 + " order by a.isdetail,a.fieldorder ";
/*      */         } 
/*  780 */       } else if (str8.equals("1")) {
/*  781 */         str11 = "select id,fieldlabel,viewtype,dsporder from workflow_billfield where viewtype=0 and billid=" + str9;
/*      */         
/*  783 */         str11 = str11 + " order by viewtype,dsporder";
/*      */       } 
/*  785 */       recordSet1.executeSql(str11);
/*  786 */       while (recordSet1.next()) {
/*  787 */         String str = "";
/*  788 */         if (str8.equals("0"))
/*  789 */           str = recordSet1.getString("fieldlable"); 
/*  790 */         if (str8.equals("1"))
/*  791 */           str = SystemEnv.getHtmlLabelName(recordSet1
/*  792 */               .getInt("fieldlabel"), paramUser.getLanguage()); 
/*  793 */         str10 = str10 + "<,option value=" + recordSet1.getString(1) + ">" + str + "</option,>";
/*      */       } 
/*      */ 
/*      */       
/*  797 */       if (str8.equals("0"))
/*  798 */       { ArrayList<String> arrayList1 = new ArrayList();
/*  799 */         str11 = "select distinct groupid from workflow_formfield where formid=" + str9;
/*  800 */         recordSet1.executeSql(str11);
/*  801 */         while (recordSet1.next()) {
/*  802 */           String str = Util.null2String(recordSet1.getString("groupid"));
/*  803 */           if (str.equals(""))
/*      */             continue; 
/*  805 */           arrayList1.add(str);
/*      */         } 
/*  807 */         for (byte b = 0; b < arrayList1.size(); b++) {
/*  808 */           String str = arrayList1.get(b);
/*  809 */           if (!str.equals("")) {
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */             
/*  815 */             str11 = "select a.fieldid, b.fieldlable, a.isdetail, a.fieldorder from workflow_formfield a, workflow_fieldlable b  where a.isdetail=1 and a.formid=b.formid and a.fieldid=b.fieldid and a.formid=" + str9 + " and b.langurageid = " + paramUser.getLanguage() + " and groupid=" + str;
/*      */ 
/*      */             
/*  818 */             if (recordSet1.getDBType().equals("oracle")) {
/*  819 */               str11 = str11 + " order by a.isdetail desc,a.fieldorder asc ";
/*      */             } else {
/*  821 */               str11 = str11 + " order by a.isdetail,a.fieldorder ";
/*      */             } 
/*  823 */             recordSet1.executeSql(str11);
/*  824 */             String str12 = "";
/*  825 */             while (recordSet1.next()) {
/*  826 */               String str13 = recordSet1.getString("fieldlable");
/*      */               
/*  828 */               str12 = str12 + "<,option value=" + recordSet1.getString(1) + ">" + str13 + "</option,>";
/*      */             } 
/*      */             
/*  831 */             hashtable.put(str, str12);
/*      */           } 
/*      */         }  }
/*  834 */       else { ArrayList<String> arrayList1 = new ArrayList();
/*  835 */         str11 = "select tablename from Workflow_billdetailtable where billid= ? order by orderid";
/*  836 */         recordSet1.executeQuery(str11, new Object[] { str9 });
/*  837 */         while (recordSet1.next()) {
/*  838 */           String str = Util.null2String(recordSet1
/*  839 */               .getString("tablename"));
/*  840 */           if (str.equals(""))
/*      */             continue; 
/*  842 */           arrayList1.add(str);
/*      */         } 
/*  844 */         for (byte b = 0; b < arrayList1.size(); b++) {
/*      */           
/*  846 */           String str = Util.null2String(arrayList1.get(b));
/*  847 */           if (!str.equals("")) {
/*      */             
/*  849 */             str11 = "select id,fieldlabel,viewtype,dsporder from workflow_billfield where viewtype=1 and billid=" + str9 + " and detailtable='" + str + "'";
/*      */ 
/*      */ 
/*      */ 
/*      */             
/*  854 */             str11 = str11 + " order by viewtype,dsporder";
/*  855 */             recordSet1.executeSql(str11);
/*  856 */             String str12 = "";
/*  857 */             while (recordSet1.next()) {
/*  858 */               String str13 = SystemEnv.getHtmlLabelName(recordSet1
/*  859 */                   .getInt("fieldlabel"), paramUser.getLanguage());
/*      */               
/*  861 */               str12 = str12 + "<,option value=" + recordSet1.getString(1) + ">" + str13 + "</option,>";
/*      */             } 
/*      */             
/*  864 */             hashtable.put(str, str12);
/*      */           } 
/*      */         }  }
/*      */     
/*  868 */     }  HashMap<Object, Object> hashMap = new HashMap<>();
/*  869 */     Map<String, Integer> map = getGroupIdMap(str8, str9, recordSet3);
/*  870 */     recordSet1.executeSql(str7);
/*  871 */     while (recordSet1.next()) {
/*  872 */       String str12 = "";
/*  873 */       String str13 = recordSet1.getString("fieldid");
/*  874 */       if (str8.equals("1")) {
/*  875 */         str12 = SystemEnv.getHtmlLabelName(recordSet1.getInt("fieldlabel"), paramUser.getLanguage());
/*  876 */         if (!str4.equals("") && str12.indexOf(str4) < 0)
/*      */           continue; 
/*      */       } else {
/*  879 */         str12 = Util.null2String(recordSet1.getString("fieldlable"));
/*      */       } 
/*  881 */       String str14 = Util.null2String(recordSet1.getString("description")).trim();
/*  882 */       if (!"".equals(str14)) {
/*  883 */         str12 = str14;
/*      */       }
/*  885 */       int m = Util.getIntValue(recordSet1.getString("fieldid"), 0);
/*      */       
/*  887 */       String str15 = "," + str10;
/*  888 */       String str16 = Util.null2String(recordSet1.getString("isdetail"));
/*  889 */       if (str16.equals("1")) {
/*  890 */         String str = Util.null2String(recordSet1.getString("optionkey"));
/*  891 */         if (!str.equals(""))
/*  892 */           str15 = "," + (String)hashtable.get(str); 
/*      */       } 
/*  894 */       str15 = str15 + ",";
/*  895 */       String str17 = "";
/*  896 */       int n = 0;
/*  897 */       if (str16.equals("1")) {
/*  898 */         String str18 = "";
/*  899 */         if (str8.equals("0")) {
/*      */           
/*  901 */           str18 = "select groupid as tablename from workflow_formfield where isdetail=1  and formid=" + str9 + " and fieldid=" + str13;
/*      */         } else {
/*      */           
/*  904 */           str18 = "select t.tablename from workflow_billfield b ,workflow_billdetailtable t where b.viewtype=1 and b.detailtable=t.tablename and b.id=" + str13;
/*      */         } 
/*      */         
/*  907 */         recordSet2.executeSql(str18);
/*  908 */         recordSet2.next();
/*      */         
/*  910 */         String str19 = recordSet2.getString("tablename");
/*  911 */         int i1 = ((Integer)map.get(str19)).intValue();
/*  912 */         if (str6.equals("1") && 
/*  913 */           j != i1 && j > 0) {
/*      */           continue;
/*      */         }
/*      */         
/*  917 */         n = i1;
/*      */         
/*  919 */         if (n < 1) {
/*  920 */           n = 1;
/*      */         }
/*  922 */         str17 = SystemEnv.getHtmlLabelName(19325, paramUser.getLanguage()) + i1;
/*      */       } else {
/*      */         
/*  925 */         str17 = SystemEnv.getHtmlLabelName(21778, paramUser.getLanguage());
/*      */       } 
/*      */       
/*  928 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  929 */       if (!recordSet1.getString("fieldhtmltype").equals("9")) {
/*  930 */         hashMap1.put("fieldid", str13);
/*  931 */         hashMap1.put("fieldname", str12);
/*  932 */         hashMap1.put("htmltype", recordSet1.getString("fieldhtmltype"));
/*  933 */         hashMap1.put("type", recordSet1.getString("type"));
/*  934 */         hashMap1.put("tabletype", str17);
/*  935 */         hashMap1.put("isdetail", str16.equals("1") ? "1" : "0");
/*  936 */         hashMap1.put("tempoption", str15);
/*  937 */         hashMap1.put("detailindex", n + "");
/*  938 */         arrayList.add(hashMap1);
/*      */       } 
/*      */     } 
/*      */ 
/*      */ 
/*      */     
/*  944 */     for (Map<String, String> map1 : arrayList) {
/*  945 */       map1.put("namefix", (String)map1.get("tabletype") + "." + (String)map1.get("fieldname"));
/*      */     }
/*  947 */     return (List)arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static int getNewGroupid(String paramString, int paramInt) {
/*  958 */     RecordSet recordSet = new RecordSet();
/*  959 */     int i = 0;
/*  960 */     String str = "";
/*      */     
/*  962 */     if (recordSet.getOrgindbtype().toLowerCase().equals("gs")) {
/*  963 */       str = "SELECT t.rid  FROM (select rid, orderid  from (SELECT row_number() over(order by orderid) as rid,tablename, orderid  FROM Workflow_billdetailtable  WHERE billid = " + paramString + " order by orderid) t1) t  WHERE t.orderid = " + paramInt;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*      */     }
/*  970 */     else if (recordSet.getDBType().equals("oracle")) {
/*  971 */       str = " SELECT t.rid FROM (select rownum as rid,orderid from (SELECT tablename, orderid FROM Workflow_billdetailtable  WHERE billid = " + paramString + " order by orderid) t1) t WHERE t.orderid=" + paramInt;
/*  972 */     } else if (recordSet.getDBType().equals("mysql")) {
/*  973 */       str = "SELECT (@rowNum := @rowNum + 1) as rid, t1.orderid       from (SELECT (@rowNum := 0) as rid,tablename, orderid             FROM Workflow_billdetailtable             WHERE billid = " + paramString + "            order by orderid) t1) ";
/*      */     
/*      */     }
/*      */     else {
/*      */ 
/*      */       
/*  979 */       str = "SELECT t.rid FROM (SELECT ROW_NUMBER() OVER (ORDER BY ORDERid) AS rid ,tablename,orderid FROM Workflow_billdetailtable WHERE billid=" + paramString + " ) t WHERE t.orderid=" + paramInt;
/*      */     } 
/*  981 */     recordSet.executeSql(str);
/*  982 */     if (recordSet.getDBType().equals("mysql")) {
/*  983 */       while (recordSet.next()) {
/*  984 */         if (paramInt == recordSet.getInt("orderid")) {
/*  985 */           i = recordSet.getInt("rid");
/*      */           break;
/*      */         } 
/*      */       } 
/*  989 */       if (i == 0) {
/*  990 */         i = paramInt;
/*      */       }
/*      */     }
/*  993 */     else if (recordSet.next()) {
/*  994 */       i = recordSet.getInt("rid");
/*      */     } else {
/*  996 */       i = paramInt;
/*      */     } 
/*      */     
/*  999 */     return i;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public List<Map<String, String>> getWorkflowTableFieldForE9(User paramUser, Map<String, String> paramMap, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/* 1006 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 1007 */     String str1 = Util.null2String(paramMap.get("searchtablename"));
/* 1008 */     String str2 = Util.null2String(paramMap.get("searchfieldname"));
/* 1009 */     String str3 = Util.null2String(paramMap.get("datasourceid"));
/* 1010 */     if (str1 != null) {
/* 1011 */       str1 = str1.trim();
/*      */     }
/* 1013 */     if (str2 != null) {
/* 1014 */       str2 = str2.trim();
/*      */     }
/* 1016 */     if (str3 != null) {
/* 1017 */       str3 = str3.trim();
/*      */     }
/* 1019 */     ArrayList arrayList1 = (new DataSourceXML()).getPointArrayList();
/* 1020 */     if (arrayList1.indexOf(str3) == -1) {
/* 1021 */       str3 = "";
/*      */     }
/*      */     
/* 1024 */     String str4 = Util.null2String(paramMap.get("tablenames")).replaceAll("maohaoTransfer", ":");
/* 1025 */     ArrayList<String> arrayList2 = Util.TokenizerString(str4, ",");
/* 1026 */     String str5 = "";
/* 1027 */     boolean bool = false;
/* 1028 */     RecordSetDataSource recordSetDataSource = new RecordSetDataSource();
/* 1029 */     RecordSet recordSet1 = new RecordSet();
/* 1030 */     RecordSet recordSet2 = new RecordSet();
/* 1031 */     RecordSet recordSet3 = new RecordSet();
/* 1032 */     for (byte b = 0; b < arrayList2.size(); b++) {
/* 1033 */       String str6 = arrayList2.get(b);
/* 1034 */       String str7 = "";
/* 1035 */       String str8 = "";
/* 1036 */       String str9 = "";
/* 1037 */       String[] arrayOfString = str6.split("~");
/* 1038 */       str7 = arrayOfString[0];
/* 1039 */       str8 = arrayOfString[1];
/* 1040 */       String str10 = "";
/* 1041 */       String str11 = "";
/* 1042 */       String str12 = "";
/* 1043 */       if (arrayOfString.length == 4) {
/* 1044 */         str10 = arrayOfString[2];
/* 1045 */         str11 = arrayOfString[3];
/*      */       } 
/*      */       
/* 1048 */       if ((!"localhost".equals(str3) && !"".equals(str3)) || 
/* 1049 */         "".equals(str7) || null == str7);
/*      */ 
/*      */ 
/*      */ 
/*      */       
/* 1054 */       if (str3 != null && !str3.equals(""))
/* 1055 */       { if (str1.equalsIgnoreCase("") || str1.equalsIgnoreCase(str8)) {
/*      */ 
/*      */           
/* 1058 */           ArrayList<String> arrayList3 = recordSetDataSource.getAllColumns(str3, str8);
/* 1059 */           for (byte b1 = 0; b1 < arrayList3.size(); b1++) {
/* 1060 */             String str = Util.null2String(arrayList3.get(b1));
/* 1061 */             if ("".equals(str2) || str.toUpperCase().indexOf(str2.toUpperCase()) != -1) {
/*      */ 
/*      */               
/* 1064 */               HashMap<Object, Object> hashMap = new HashMap<>();
/* 1065 */               hashMap.put("fieldname", str);
/* 1066 */               hashMap.put("fieldlabel", str);
/* 1067 */               hashMap.put("tablename", str8);
/* 1068 */               hashMap.put("tablelabel", str8);
/* 1069 */               if (str10.equals("-")) {
/* 1070 */                 str12 = str8 + ".";
/*      */               } else {
/* 1072 */                 str12 = str10 + ".";
/*      */               } 
/* 1074 */               hashMap.put("tabfix", str12);
/* 1075 */               hashMap.put("name", str12 + str);
/*      */               
/* 1077 */               hashMap.put("fieldnamefix", str12 + str);
/* 1078 */               arrayList.add(hashMap);
/*      */             } 
/*      */           } 
/*      */         }  }
/* 1082 */       else { if (str10.equals("-")) {
/* 1083 */           if (str11.equals("") || str11.equalsIgnoreCase("null")) {
/* 1084 */             str12 = "";
/*      */           } else {
/* 1086 */             str12 = str11 + ".";
/*      */           } 
/*      */         } else {
/* 1089 */           str12 = str10 + ".";
/*      */         } 
/* 1091 */         if (!"".equals(str7) && !"0".equals(str7)) {
/* 1092 */           int i = 0;
/* 1093 */           if (str8.equals("workflow_form"))
/* 1094 */             str5 = "select fieldid from workflow_formfield where isdetail is null and formid=" + str7; 
/* 1095 */           if (str8.equals("workflow_formdetail")) {
/* 1096 */             i = Util.getIntValue(str7.substring(str7.indexOf("_") + 1, str7.length()), 0);
/* 1097 */             str7 = str7.substring(0, str7.indexOf("_"));
/* 1098 */             str5 = "select fieldid from workflow_formfield where groupId=" + i + " and isdetail=1 and formid=" + str7;
/*      */           } 
/*      */           
/* 1101 */           recordSet1.executeSql("select formname from workflow_formbase where id=" + str7);
/* 1102 */           if (recordSet1.next())
/* 1103 */             str9 = recordSet1.getString("formname"); 
/* 1104 */           if (str8.equals("workflow_formdetail")) {
/* 1105 */             str9 = str9 + "(" + SystemEnv.getHtmlLabelName(19325, paramUser.getLanguage()) + (i + 1) + ")";
/*      */           } else {
/* 1107 */             str9 = str9 + "(" + SystemEnv.getHtmlLabelName(21778, paramUser.getLanguage()) + ")";
/*      */           } 
/* 1109 */           if (!str1.equals("") && !str1.equals(str7))
/* 1110 */             str5 = str5 + " and 1=2"; 
/* 1111 */           recordSet1.executeSql(str5);
/* 1112 */           boolean bool1 = true;
/* 1113 */           while (recordSet1.next()) {
/* 1114 */             String str13 = recordSet1.getString("fieldid");
/* 1115 */             recordSet3.executeSql("select fieldid,fieldlable from workflow_fieldlable where langurageid=" + paramUser
/* 1116 */                 .getLanguage() + " and formid=" + str7 + " and fieldid=" + str13);
/* 1117 */             recordSet3.next();
/* 1118 */             System.out.println("searchfieldname" + recordSet3.getString("fieldlable"));
/* 1119 */             if (!str2.equals("") && recordSet3.getString("fieldlable").indexOf(str2) < 0)
/*      */               continue; 
/* 1121 */             if (bool1 && !str8.equals("workflow_formdetail") && str2.equals("")) {
/* 1122 */               bool1 = false;
/* 1123 */               HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 1124 */               hashMap1.put("fieldname", "requestid");
/* 1125 */               hashMap1.put("fieldlabel", SystemEnv.getHtmlLabelName(648, paramUser.getLanguage()) + "ID");
/* 1126 */               hashMap1.put("tablename", str8);
/* 1127 */               hashMap1.put("tablelabel", str9);
/* 1128 */               hashMap1.put("tabfix", str12);
/* 1129 */               hashMap1.put("name", str12 + SystemEnv.getHtmlLabelName(648, paramUser.getLanguage()) + "ID");
/* 1130 */               hashMap1.put("fieldnamefix", str12 + "requestid");
/* 1131 */               arrayList.add(hashMap1);
/*      */             } 
/* 1133 */             String str14 = "";
/* 1134 */             if (str8.equals("workflow_formdetail")) {
/* 1135 */               str14 = "select fieldname from workflow_formdictdetail where id=" + str13;
/*      */             } else {
/* 1137 */               str14 = "select fieldname from workflow_formdict where id=" + str13;
/* 1138 */             }  recordSet2.executeSql(str14);
/* 1139 */             HashMap<Object, Object> hashMap = new HashMap<>();
/* 1140 */             String str15 = "";
/*      */             
/* 1142 */             if (recordSet2.next()) {
/* 1143 */               str15 = recordSet2.getString("fieldname");
/* 1144 */               hashMap.put("fieldname", str15);
/*      */             } 
/* 1146 */             hashMap.put("fieldlabel", recordSet3.getString("fieldlable"));
/* 1147 */             hashMap.put("tablename", str8);
/* 1148 */             hashMap.put("tablelabel", str9);
/* 1149 */             hashMap.put("tabfix", str12);
/* 1150 */             hashMap.put("name", str12 + recordSet3.getString("fieldlable"));
/* 1151 */             hashMap.put("fieldnamefix", str12 + str15);
/* 1152 */             arrayList.add(hashMap);
/*      */           }
/*      */         
/* 1155 */         } else if (str1.equals("") || str8.equals(str1)) {
/*      */           
/* 1157 */           if (!str1.equals("") && str8.equals(str1)) {
/* 1158 */             str8 = str1;
/*      */           }
/* 1160 */           recordSet1.executeSql("select id,namelabel from workflow_bill where tablename='" + str8 + "'");
/* 1161 */           boolean bool1 = true;
/* 1162 */           if (recordSet1.next()) {
/* 1163 */             String str = recordSet1.getString("id");
/*      */             
/* 1165 */             str9 = SystemEnv.getHtmlLabelName(recordSet1.getInt("namelabel"), paramUser.getLanguage()) + "(" + SystemEnv.getHtmlLabelName(21778, paramUser.getLanguage()) + ")";
/* 1166 */             recordSet3.executeSql("select fieldname,fieldlabel from workflow_billfield where viewtype=0 and (fieldhtmltype <> 9 or type <> 1) and billid=" + str);
/* 1167 */             while (recordSet3.next()) {
/* 1168 */               String str13 = recordSet3.getString("fieldname");
/* 1169 */               String str14 = SystemEnv.getHtmlLabelName(recordSet3.getInt("fieldlabel"), paramUser.getLanguage());
/* 1170 */               if (!str2.equals("") && str14.indexOf(str2) < 0)
/*      */                 continue; 
/* 1172 */               if (bool1 && str2.equals("")) {
/* 1173 */                 bool1 = false;
/*      */                 
/* 1175 */                 HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 1176 */                 hashMap1.put("fieldname", "id");
/* 1177 */                 hashMap1.put("fieldlabel", "" + SystemEnv.getHtmlLabelName(563, ThreadVarLanguage.getLang()) + "ID");
/* 1178 */                 hashMap1.put("tablename", str8);
/* 1179 */                 hashMap1.put("tablelabel", str9);
/* 1180 */                 hashMap1.put("tabfix", str12);
/* 1181 */                 hashMap1.put("name", str12 + "" + SystemEnv.getHtmlLabelName(563, ThreadVarLanguage.getLang()) + "ID");
/* 1182 */                 hashMap1.put("fieldnamefix", str12 + "id");
/* 1183 */                 arrayList.add(hashMap1);
/*      */                 
/* 1185 */                 hashMap1 = new HashMap<>();
/* 1186 */                 hashMap1.put("fieldname", "requestid");
/* 1187 */                 hashMap1.put("fieldlabel", SystemEnv.getHtmlLabelName(648, paramUser.getLanguage()) + "ID");
/* 1188 */                 hashMap1.put("tablename", str8);
/* 1189 */                 hashMap1.put("tablelabel", str9);
/* 1190 */                 hashMap1.put("tabfix", str12);
/* 1191 */                 hashMap1.put("name", str12 + SystemEnv.getHtmlLabelName(648, paramUser.getLanguage()) + "ID");
/* 1192 */                 hashMap1.put("fieldnamefix", str12 + "requestid");
/* 1193 */                 arrayList.add(hashMap1);
/*      */               } 
/* 1195 */               HashMap<Object, Object> hashMap = new HashMap<>();
/* 1196 */               hashMap.put("fieldname", str13);
/* 1197 */               hashMap.put("fieldlabel", str14);
/* 1198 */               hashMap.put("tablename", str8);
/* 1199 */               hashMap.put("tablelabel", str9);
/* 1200 */               hashMap.put("tabfix", str12);
/* 1201 */               hashMap.put("name", str12 + str14);
/* 1202 */               hashMap.put("fieldnamefix", str12 + str13);
/* 1203 */               arrayList.add(hashMap);
/*      */             } 
/*      */           } 
/* 1206 */           boolean bool2 = true;
/* 1207 */           recordSet1.executeSql("select id,namelabel from workflow_bill where detailtablename='" + str8 + "'");
/* 1208 */           recordSet3.executeSql("select tablename from Workflow_billdetailtable where tablename='" + str8 + "'");
/* 1209 */           if (recordSet1.next() && !recordSet3.next()) {
/*      */             
/* 1211 */             bool2 = true;
/* 1212 */             String str = recordSet1.getString("id");
/*      */             
/* 1214 */             str9 = SystemEnv.getHtmlLabelName(recordSet1.getInt("namelabel"), paramUser.getLanguage()) + "(" + SystemEnv.getHtmlLabelName(19325, paramUser.getLanguage()) + "1)";
/* 1215 */             recordSet3.executeSql("select fieldname,fieldlabel from workflow_billfield where viewtype=1 and billid=" + str);
/* 1216 */             while (recordSet3.next()) {
/* 1217 */               String str13 = recordSet3.getString("fieldname");
/* 1218 */               String str14 = SystemEnv.getHtmlLabelName(recordSet3.getInt("fieldlabel"), paramUser.getLanguage());
/* 1219 */               if (!str2.equals("") && str14.indexOf(str2) < 0) {
/*      */                 continue;
/*      */               }
/* 1222 */               if (bool2 && str2.equals("")) {
/* 1223 */                 bool2 = false;
/*      */                 
/* 1225 */                 HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 1226 */                 hashMap1.put("fieldname", "ID");
/* 1227 */                 hashMap1.put("fieldlabel", SystemEnv.getHtmlLabelName(81287, Util.getIntValue("" + paramUser.getLanguage(), 7)));
/* 1228 */                 hashMap1.put("tablename", str8);
/* 1229 */                 hashMap1.put("tablelabel", str9);
/* 1230 */                 hashMap1.put("tabfix", str12);
/* 1231 */                 hashMap1.put("name", str12 + SystemEnv.getHtmlLabelName(81287, Util.getIntValue("" + paramUser.getLanguage(), 7)));
/* 1232 */                 hashMap1.put("fieldnamefix", str12 + "ID");
/*      */                 
/* 1234 */                 arrayList.add(hashMap1);
/*      */               } 
/*      */               
/* 1237 */               HashMap<Object, Object> hashMap = new HashMap<>();
/* 1238 */               hashMap.put("fieldname", str13);
/* 1239 */               hashMap.put("fieldlabel", str14);
/* 1240 */               hashMap.put("tablename", str8);
/* 1241 */               hashMap.put("tablelabel", str9);
/* 1242 */               hashMap.put("tabfix", str12);
/* 1243 */               hashMap.put("name", str12 + str14);
/* 1244 */               hashMap.put("fieldnamefix", str12 + str13);
/*      */ 
/*      */               
/* 1247 */               arrayList.add(hashMap);
/*      */             } 
/*      */           } 
/* 1250 */           recordSet1.executeSql("select billid from Workflow_billdetailtable where tablename='" + str8 + "'");
/* 1251 */           if (recordSet1.next()) {
/* 1252 */             String str13 = recordSet1.getString("billid");
/* 1253 */             recordSet1.executeSql("select namelabel from workflow_bill where id=" + str13);
/* 1254 */             if (recordSet1.next()) {
/* 1255 */               str9 = SystemEnv.getHtmlLabelName(recordSet1.getInt("namelabel"), paramUser.getLanguage());
/*      */             }
/* 1257 */             recordSet1.executeSql("select tablename from Workflow_billdetailtable where billid=" + str13 + " order by orderid");
/*      */             
/* 1259 */             byte b1 = 0;
/* 1260 */             while (recordSet1.next()) {
/* 1261 */               b1++;
/* 1262 */               String str = Util.null2String(recordSet1.getString("tablename"));
/* 1263 */               if (str.equals(str8)) {
/* 1264 */                 str9 = str9 + "(" + SystemEnv.getHtmlLabelName(19325, paramUser.getLanguage()) + b1 + ")";
/*      */               }
/*      */             } 
/*      */             
/* 1268 */             String str14 = "";
/* 1269 */             recordSet3.executeQuery("select detailkeyfield from workflow_bill where id = " + str13, new Object[0]);
/* 1270 */             while (recordSet3.next()) {
/* 1271 */               str14 = Util.null2String(recordSet3.getString(1));
/*      */             }
/*      */             
/* 1274 */             bool2 = true;
/* 1275 */             recordSet3.executeSql("select fieldname,fieldlabel from workflow_billfield where viewtype=1 and detailtable='" + str8 + "' and billid=" + str13);
/*      */             
/* 1277 */             while (recordSet3.next()) {
/* 1278 */               String str16 = recordSet3.getString("fieldname");
/* 1279 */               String str17 = SystemEnv.getHtmlLabelName(recordSet3.getInt("fieldlabel"), paramUser.getLanguage());
/* 1280 */               if (!str2.equals("") && str17.indexOf(str2) < 0) {
/*      */                 continue;
/*      */               }
/* 1283 */               if (bool2 && str2.equals("")) {
/* 1284 */                 bool2 = false;
/*      */                 
/* 1286 */                 HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 1287 */                 hashMap1.put("fieldname", "ID");
/* 1288 */                 hashMap1.put("fieldlabel", SystemEnv.getHtmlLabelName(81287, Util.getIntValue("" + paramUser.getLanguage(), 7)));
/* 1289 */                 hashMap1.put("tablename", str8);
/* 1290 */                 hashMap1.put("tablelabel", str9);
/* 1291 */                 hashMap1.put("tabfix", str12);
/* 1292 */                 hashMap1.put("name", str12 + SystemEnv.getHtmlLabelName(81287, Util.getIntValue("" + paramUser.getLanguage(), 7)));
/* 1293 */                 hashMap1.put("fieldnamefix", str12 + "ID");
/* 1294 */                 arrayList.add(hashMap1);
/*      */                 
/* 1296 */                 if ("mainid".equals(str14)) {
/* 1297 */                   HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 1298 */                   hashMap2.put("fieldname", str14);
/* 1299 */                   hashMap2.put("fieldlabel", str14);
/* 1300 */                   hashMap2.put("tablename", str8);
/* 1301 */                   hashMap2.put("tablelabel", str9);
/* 1302 */                   hashMap2.put("tabfix", str12);
/* 1303 */                   hashMap2.put("name", str12 + str14);
/* 1304 */                   hashMap2.put("fieldnamefix", str12 + str14);
/* 1305 */                   arrayList.add(hashMap2);
/*      */                 } 
/*      */               } 
/*      */ 
/*      */               
/* 1310 */               HashMap<Object, Object> hashMap = new HashMap<>();
/* 1311 */               hashMap.put("fieldname", str16);
/* 1312 */               hashMap.put("fieldlabel", str17);
/* 1313 */               hashMap.put("tablename", str8);
/* 1314 */               hashMap.put("tablelabel", str9);
/* 1315 */               hashMap.put("tabfix", str12);
/* 1316 */               hashMap.put("name", str12 + str17);
/* 1317 */               hashMap.put("fieldnamefix", str12 + str16);
/* 1318 */               arrayList.add(hashMap);
/*      */             } 
/*      */             
/* 1321 */             String str15 = SystemEnv.getHtmlLabelName(503443, Util.getIntValue("" + paramUser.getLanguage(), 7));
/* 1322 */             if (!str2.equals("") && str15.contains(str2)) {
/* 1323 */               HashMap<Object, Object> hashMap = new HashMap<>();
/* 1324 */               hashMap.put("fieldname", str14);
/* 1325 */               hashMap.put("fieldlabel", SystemEnv.getHtmlLabelName(503443, Util.getIntValue("" + paramUser.getLanguage(), 7)));
/* 1326 */               hashMap.put("tablename", str8);
/* 1327 */               hashMap.put("tablelabel", str9);
/* 1328 */               hashMap.put("tabfix", str12);
/* 1329 */               hashMap.put("name", str12 + SystemEnv.getHtmlLabelName(503443, Util.getIntValue("" + paramUser.getLanguage(), 7)));
/* 1330 */               hashMap.put("fieldnamefix", str12 + str14);
/* 1331 */               arrayList.add(hashMap);
/*      */             } 
/*      */           } 
/* 1334 */           recordSet1.executeSql("select id,tabledesc,tabledescen from Sys_tabledict where tablename='" + str8 + "'");
/*      */           
/* 1336 */           if (recordSet1.next()) {
/* 1337 */             String str = recordSet1.getString("id");
/* 1338 */             if (paramUser.getLanguage() == 7 || paramUser.getLanguage() == 9) {
/* 1339 */               str9 = recordSet1.getString("tabledesc");
/*      */             } else {
/* 1341 */               str9 = recordSet1.getString("tabledescen");
/*      */             } 
/* 1343 */             List<String> list = getAllColumns(str8);
/* 1344 */             str9 = str9 + "(" + SystemEnv.getHtmlLabelName(21778, paramUser.getLanguage()) + ")";
/* 1345 */             recordSet3.executeSql("select fieldname,fielddesc,fielddescen from Sys_fielddict where tabledictid=" + str + " order by dsporder");
/*      */             
/* 1347 */             while (recordSet3.next()) {
/* 1348 */               String str13 = recordSet3.getString("fieldname").toLowerCase();
/* 1349 */               if (list.size() > 0 && list.contains(str13)) {
/* 1350 */                 list.remove(str13);
/*      */               }
/* 1352 */               String str14 = "";
/* 1353 */               if (paramUser.getLanguage() == 7 || paramUser.getLanguage() == 9) {
/* 1354 */                 str14 = recordSet3.getString("fielddesc");
/*      */               } else {
/* 1356 */                 str14 = recordSet3.getString("fielddescen");
/*      */               } 
/* 1358 */               if (!str2.equals("") && str14.indexOf(str2) < 0)
/*      */                 continue; 
/* 1360 */               HashMap<Object, Object> hashMap = new HashMap<>();
/* 1361 */               hashMap.put("fieldname", str13);
/* 1362 */               hashMap.put("fieldlabel", str14);
/* 1363 */               hashMap.put("tablename", str8);
/* 1364 */               hashMap.put("tablelabel", str9);
/* 1365 */               hashMap.put("tabfix", str12);
/* 1366 */               hashMap.put("name", str12 + str14);
/* 1367 */               hashMap.put("fieldnamefix", str12 + str13);
/* 1368 */               arrayList.add(hashMap);
/*      */             } 
/*      */ 
/*      */             
/* 1372 */             for (String str13 : list) {
/* 1373 */               HashMap<Object, Object> hashMap = new HashMap<>();
/* 1374 */               if (!str2.equals("") && str13.indexOf(str2) < 0)
/*      */                 continue; 
/* 1376 */               hashMap.put("fieldname", str13);
/* 1377 */               hashMap.put("fieldlabel", str13);
/* 1378 */               hashMap.put("tablename", str8);
/* 1379 */               hashMap.put("tablelabel", str9);
/* 1380 */               if (str10.equals("-")) {
/* 1381 */                 str12 = str8 + ".";
/*      */               } else {
/* 1383 */                 str12 = str10 + ".";
/*      */               } 
/* 1385 */               hashMap.put("tabfix", str12);
/* 1386 */               hashMap.put("name", str12 + str13);
/*      */               
/* 1388 */               hashMap.put("fieldnamefix", str12 + str13);
/* 1389 */               arrayList.add(hashMap);
/*      */             } 
/*      */           } 
/*      */         }  }
/*      */     
/*      */     } 
/* 1395 */     for (Map<String, String> map : arrayList) {
/* 1396 */       if ("".equals(map.get("tabfix"))) {
/* 1397 */         map.put("name", (String)map.get("tablelabel") + "." + (String)map.get("fieldlabel"));
/*      */       }
/*      */     } 
/* 1400 */     return (List)arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public List<String> getAllColumns(String paramString) {
/* 1410 */     RecordSet recordSet = new RecordSet();
/* 1411 */     String str1 = recordSet.getDBType();
/* 1412 */     ArrayList<String> arrayList = new ArrayList();
/* 1413 */     paramString = Util.null2String(paramString);
/* 1414 */     if (paramString.equals("")) return arrayList; 
/* 1415 */     String str2 = "";
/*      */     
/* 1417 */     if ("sqlserver".equalsIgnoreCase(str1)) {
/* 1418 */       str2 = "select c.name from sysobjects o,syscolumns c where o.id=c.id and o.name='" + paramString + "' order by c.colid";
/* 1419 */     } else if ("mysql".equalsIgnoreCase(str1)) {
/* 1420 */       str2 = "select COLUMN_NAME from information_schema.columns where table_name='" + paramString + "'  and table_schema=database() ";
/*      */     }
/* 1422 */     else if ("postgresql".equalsIgnoreCase(str1)) {
/* 1423 */       str2 = "select COLUMN_NAME from information_schema.columns where table_name=lower('" + paramString + "')  ";
/*      */     } else {
/*      */       
/* 1426 */       str2 = "select COLUMN_NAME from user_tab_columns where table_name=upper('" + paramString + "') ORDER BY COLUMN_ID";
/*      */     } 
/* 1428 */     recordSet.executeQuery(str2, new Object[0]);
/* 1429 */     while (recordSet.next()) {
/* 1430 */       String str = Util.null2String(recordSet.getString(1)).toLowerCase();
/* 1431 */       if ("uuid".equals(str) || str.indexOf("password") > -1) {
/*      */         continue;
/*      */       }
/* 1434 */       arrayList.add(str);
/*      */     } 
/* 1436 */     return arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public Map<String, Integer> getGroupIdMap(String paramString1, String paramString2, RecordSet paramRecordSet) {
/* 1449 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 1450 */     String str = "";
/* 1451 */     byte b = 1;
/* 1452 */     if (paramString1.equals("0")) {
/* 1453 */       str = "select distinct groupid as tablename from workflow_formfield where formid=" + paramString2 + " and isdetail='1' order by groupid";
/*      */     } else {
/* 1455 */       str = "select tablename from workflow_billdetailtable where billid=" + paramString2 + " order by orderid";
/*      */     } 
/* 1457 */     paramRecordSet.executeSql(str);
/* 1458 */     while (paramRecordSet.next()) {
/* 1459 */       String str1 = paramRecordSet.getString("tablename");
/* 1460 */       hashMap.put(str1, Integer.valueOf(b));
/* 1461 */       b++;
/*      */     } 
/* 1463 */     return (Map)hashMap;
/*      */   }
/*      */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/workflow/WfDataSource.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */