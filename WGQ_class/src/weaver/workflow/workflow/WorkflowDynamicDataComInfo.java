/*     */ package weaver.workflow.workflow;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.cache.CacheBase;
/*     */ import weaver.cache.CacheColumn;
/*     */ import weaver.cache.CacheItem;
/*     */ import weaver.cache.CacheMap;
/*     */ import weaver.cache.PKColumn;
/*     */ import weaver.conn.RecordSet;
/*     */ 
/*     */ public class WorkflowDynamicDataComInfo extends CacheBase {
/*  13 */   protected static String TABLE_NAME = "Workflow_DataInput_Entry";
/*     */   
/*  15 */   protected static String TABLE_WHERE = null;
/*     */   
/*  17 */   protected static String TABLE_ORDER = null;
/*     */   
/*     */   @PKColumn(type = CacheColumnType.NUMBER)
/*  20 */   protected static String PK_NAME = "workflowid";
/*     */ 
/*     */   
/*     */   @CacheColumn
/*     */   protected static int id;
/*     */ 
/*     */   
/*     */   @CacheColumn
/*     */   protected static int TriggerFieldName;
/*     */   
/*     */   @CacheColumn(isVirtual = true)
/*     */   protected static int workflowTriggerMap;
/*     */   
/*  33 */   private static StringBuffer tmpSb = new StringBuffer();
/*     */   
/*     */   private static final String ITEM_SQL = "Select DataInputID,PageFieldName from Workflow_DataInput_Field where  datainputid in (Select ID from Workflow_DataInput_Main where entryid = ?)";
/*     */   private static final String ENTRY_SQL = "select id,TriggerFieldName from Workflow_DataInput_Entry where workflowid = ?";
/*     */   
/*     */   protected CacheMap initCache() throws Exception {
/*  39 */     return createCacheMap();
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   protected void modifyCacheItem(String paramString, CacheItem paramCacheItem) {
/*  45 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/*  47 */     RecordSet recordSet1 = new RecordSet();
/*  48 */     RecordSet recordSet2 = new RecordSet();
/*  49 */     recordSet1.executeQuery("select id,TriggerFieldName from Workflow_DataInput_Entry where workflowid = ?", new Object[] { paramString });
/*  50 */     while (recordSet1.next()) {
/*  51 */       String str1 = recordSet1.getString("id");
/*  52 */       String str2 = recordSet1.getString("TriggerFieldName");
/*     */       
/*  54 */       List<String> list = (List)hashMap.get(str2);
/*  55 */       if (list == null) {
/*  56 */         list = new ArrayList();
/*  57 */         hashMap.put(str2, list);
/*     */       } 
/*     */       
/*  60 */       tmpSb.setLength(0);
/*  61 */       recordSet2.executeQuery("Select DataInputID,PageFieldName from Workflow_DataInput_Field where  datainputid in (Select ID from Workflow_DataInput_Main where entryid = ?)", new Object[] { str1 });
/*  62 */       while (recordSet2.next()) {
/*  63 */         String str = recordSet2.getString("DataInputID") + "|" + recordSet2.getString("PageFieldName");
/*  64 */         tmpSb.append(",").append(str);
/*     */       } 
/*     */ 
/*     */       
/*  68 */       String str3 = tmpSb.toString();
/*  69 */       if (str3.length() > 1) {
/*  70 */         String str = str3.substring(1);
/*  71 */         list.add(str);
/*     */       } 
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/*  77 */     paramCacheItem.set(workflowTriggerMap, hashMap);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<String> getCValFields(String paramString1, String paramString2) {
/*  90 */     Map map = (Map)getObjValue(workflowTriggerMap, paramString1);
/*  91 */     if (map == null) {
/*  92 */       return null;
/*     */     }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  99 */     return (List<String>)map.get(paramString2);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void updateDynamicDataInputCache(String paramString) {
/* 110 */     updateCache(paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void removeCache() {
/* 117 */     super.removeCache();
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/workflow/WorkflowDynamicDataComInfo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */