/*     */ package weaver.workflow.workflow;
/*     */ 
/*     */ import weaver.conn.ConnStatement;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.workflow.exceldesign.FormTemplateManager;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class WFNodeFieldMainManager
/*     */   extends BaseBean
/*     */ {
/*  34 */   private int currecord = 1; private int nodeid;
/*     */   private int formid;
/*     */   private int isbill;
/*     */   private int fieldid;
/*     */   private String isview;
/*     */   
/*     */   public void resetParameter() {
/*  41 */     this.fieldid = 0;
/*  42 */     this.nodeid = 0;
/*  43 */     this.formid = 0;
/*  44 */     this.isbill = -1;
/*  45 */     this.isview = "";
/*  46 */     this.isedit = "";
/*  47 */     this.ismandatory = "";
/*  48 */     this.isonlyshow = "";
/*  49 */     this.isalonerow = "";
/*  50 */     this.orderid = "0.00";
/*     */   }
/*     */   private String isedit; private String ismandatory; private String isonlyshow; private String isalonerow; private String orderid; private String configId;
/*     */   public String getConfigId() {
/*  54 */     return this.configId;
/*     */   }
/*     */   
/*     */   public void setConfigId(String paramString) {
/*  58 */     this.configId = paramString;
/*     */   }
/*     */   
/*     */   public void setFieldid(int paramInt) {
/*  62 */     this.fieldid = paramInt;
/*     */   }
/*     */   public int getFieldid() {
/*  65 */     return this.fieldid;
/*     */   }
/*     */   
/*     */   public void setNodeid(int paramInt) {
/*  69 */     this.nodeid = paramInt;
/*     */   }
/*     */   public int getNodeid() {
/*  72 */     return this.nodeid;
/*     */   }
/*     */   
/*     */   public void setFormid(int paramInt) {
/*  76 */     this.formid = paramInt;
/*     */   }
/*     */   
/*     */   public void setIsbill(int paramInt) {
/*  80 */     this.isbill = paramInt;
/*     */   }
/*     */   
/*     */   public void setIsview(String paramString) {
/*  84 */     this.isview = paramString;
/*     */   }
/*     */   public String getIsview() {
/*  87 */     return this.isview;
/*     */   }
/*     */   
/*     */   public void setIsedit(String paramString) {
/*  91 */     this.isedit = paramString;
/*     */   }
/*     */   public String getIsedit() {
/*  94 */     return this.isedit;
/*     */   }
/*     */   
/*     */   public void setIsmandatory(String paramString) {
/*  98 */     this.ismandatory = paramString;
/*     */   }
/*     */   public String getIsmandatory() {
/* 101 */     return this.ismandatory;
/*     */   }
/*     */   
/*     */   public String getIsonlyshow() {
/* 105 */     return this.isonlyshow;
/*     */   }
/*     */   
/*     */   public void setIsonlyshow(String paramString) {
/* 109 */     this.isonlyshow = paramString;
/*     */   }
/*     */   
/*     */   public String getIsalonerow() {
/* 113 */     return this.isalonerow;
/*     */   }
/*     */   public void setIsalonerow(String paramString) {
/* 116 */     this.isalonerow = paramString;
/*     */   }
/*     */   
/*     */   public void setOrderid(String paramString) {
/* 120 */     float f = Util.getFloatValue(paramString, 0.0F);
/* 121 */     this.orderid = "" + f;
/*     */   }
/*     */   public String getOrderid() {
/* 124 */     return this.orderid;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void selectWfNodeField() throws Exception {
/* 132 */     ConnStatement connStatement = null;
/*     */     try {
/* 134 */       connStatement = new ConnStatement();
/* 135 */       if (FormTemplateManager.isFormVirtualNode(this.nodeid)) {
/* 136 */         connStatement.setStatementSql("select * from workflow_nodeform_form where formid=? and isbill=? and fieldid=? order by orderid");
/* 137 */         connStatement.setInt(1, this.formid);
/* 138 */         connStatement.setInt(2, this.isbill);
/* 139 */         connStatement.setInt(3, this.fieldid);
/*     */       } else {
/* 141 */         connStatement.setStatementSql("select * from workflow_nodeform where nodeid=? and fieldid=? order by orderid");
/* 142 */         connStatement.setInt(1, this.nodeid);
/* 143 */         connStatement.setInt(2, this.fieldid);
/*     */       } 
/* 145 */       connStatement.executeQuery();
/* 146 */       if (connStatement.next()) {
/* 147 */         setIsview(Util.null2String(connStatement.getString("isview")));
/* 148 */         setIsedit(Util.null2String(connStatement.getString("isedit")));
/* 149 */         setIsmandatory(Util.null2String(connStatement.getString("ismandatory")));
/* 150 */         setIsonlyshow(Util.null2String(connStatement.getString("isonlyshow")));
/* 151 */         setIsalonerow(Util.null2String(connStatement.getString("isalonerow")));
/* 152 */         setOrderid(Util.null2String(connStatement.getString("orderid")));
/*     */       }
/*     */     
/* 155 */     } catch (Exception exception) {
/* 156 */       writeLog(exception);
/* 157 */       throw exception;
/*     */     } finally {
/* 159 */       connStatement.close();
/*     */     } 
/* 161 */     this.currecord = 1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean next() throws Exception {
/* 169 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void closeStatement() {}
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void saveWfNodeField() throws Exception {
/* 186 */     if (FormTemplateManager.isFormVirtualNode(this.nodeid)) {
/* 187 */       saveFormFieldAttr();
/*     */       return;
/*     */     } 
/* 190 */     String str1 = "select count(*) from workflow_nodeform where nodeid=? and fieldid=?";
/* 191 */     String str2 = "insert into workflow_nodeform(nodeid,fieldid,isview,isedit,ismandatory,isalonerow,orderid,isonlyshow) values(?,?,?,?,?,?,?,?) ";
/* 192 */     String str3 = "update workflow_nodeform set isview=?,isedit=?,ismandatory=?,isalonerow=?,orderid=?,isonlyshow=? where nodeid=? and fieldid=?";
/*     */     
/* 194 */     ConnStatement connStatement = null;
/*     */     try {
/* 196 */       connStatement = new ConnStatement();
/* 197 */       connStatement.setStatementSql(str1);
/* 198 */       connStatement.setInt(1, this.nodeid);
/* 199 */       connStatement.setInt(2, this.fieldid);
/* 200 */       connStatement.executeQuery();
/* 201 */       connStatement.next();
/* 202 */       int i = connStatement.getInt(1);
/* 203 */       if (i > 1) {
/* 204 */         RecordSet recordSet = new RecordSet();
/* 205 */         recordSet.execute("delete from workflow_nodeform where nodeid=" + this.nodeid + " and fieldid=" + this.fieldid);
/* 206 */         i = 0;
/*     */       } 
/* 208 */       if (i > 0) {
/* 209 */         connStatement.setStatementSql(str3);
/* 210 */         connStatement.setString(1, this.isview);
/* 211 */         connStatement.setString(2, this.isedit);
/* 212 */         connStatement.setString(3, this.ismandatory);
/* 213 */         connStatement.setString(4, this.isalonerow);
/* 214 */         connStatement.setFloat(5, Util.getFloatValue(this.orderid, 0.0F));
/* 215 */         connStatement.setString(6, "1".equals(this.isedit) ? this.isonlyshow : "0");
/* 216 */         connStatement.setInt(7, this.nodeid);
/* 217 */         connStatement.setInt(8, this.fieldid);
/*     */       } else {
/* 219 */         connStatement.setStatementSql(str2);
/* 220 */         connStatement.setInt(1, this.nodeid);
/* 221 */         connStatement.setInt(2, this.fieldid);
/* 222 */         connStatement.setString(3, this.isview);
/* 223 */         connStatement.setString(4, this.isedit);
/* 224 */         connStatement.setString(5, this.ismandatory);
/* 225 */         connStatement.setString(6, this.isalonerow);
/* 226 */         connStatement.setFloat(7, Util.getFloatValue(this.orderid, 0.0F));
/* 227 */         connStatement.setString(8, "1".equals(this.isedit) ? this.isonlyshow : "0");
/*     */       } 
/* 229 */       connStatement.executeUpdate();
/* 230 */     } catch (Exception exception) {
/* 231 */       writeLog(exception);
/* 232 */       throw exception;
/*     */     } finally {
/*     */       try {
/* 235 */         connStatement.close();
/* 236 */       } catch (Exception exception) {}
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void saveWfNodeField2() throws Exception {
/* 245 */     if (FormTemplateManager.isFormVirtualNode(this.nodeid)) {
/* 246 */       saveFormFieldAttr2();
/*     */       return;
/*     */     } 
/* 249 */     String str1 = "select count(*) from workflow_nodeform where nodeid=? and fieldid=?";
/* 250 */     String str2 = "insert into workflow_nodeform(nodeid,fieldid,isview,isedit,ismandatory,isonlyshow,orderid) values(?,?,?,?,?,?,?) ";
/* 251 */     String str3 = "update workflow_nodeform set isview=?,isedit=?,ismandatory=?,isonlyshow=? where nodeid=? and fieldid=?";
/*     */     
/* 253 */     ConnStatement connStatement = null;
/*     */     try {
/* 255 */       connStatement = new ConnStatement();
/* 256 */       connStatement.setStatementSql(str1);
/* 257 */       connStatement.setInt(1, this.nodeid);
/* 258 */       connStatement.setInt(2, this.fieldid);
/* 259 */       connStatement.executeQuery();
/* 260 */       connStatement.next();
/* 261 */       int i = connStatement.getInt(1);
/* 262 */       if (i > 1) {
/* 263 */         RecordSet recordSet = new RecordSet();
/* 264 */         recordSet.execute("delete from workflow_nodeform where nodeid=" + this.nodeid + " and fieldid=" + this.fieldid);
/* 265 */         i = 0;
/*     */       } 
/* 267 */       if (i > 0) {
/* 268 */         connStatement.setStatementSql(str3);
/* 269 */         connStatement.setString(1, this.isview);
/* 270 */         connStatement.setString(2, this.isedit);
/* 271 */         connStatement.setString(3, this.ismandatory);
/* 272 */         connStatement.setString(4, "1".equals(this.isedit) ? this.isonlyshow : "0");
/* 273 */         connStatement.setInt(5, this.nodeid);
/* 274 */         connStatement.setInt(6, this.fieldid);
/*     */       } else {
/* 276 */         connStatement.setStatementSql(str2);
/* 277 */         connStatement.setInt(1, this.nodeid);
/* 278 */         connStatement.setInt(2, this.fieldid);
/* 279 */         connStatement.setString(3, this.isview);
/* 280 */         connStatement.setString(4, this.isedit);
/* 281 */         connStatement.setString(5, this.ismandatory);
/* 282 */         connStatement.setString(6, "1".equals(this.isedit) ? this.isonlyshow : "0");
/* 283 */         connStatement.setFloat(7, Util.getFloatValue(this.orderid, 0.0F));
/*     */       } 
/* 285 */       connStatement.executeUpdate();
/* 286 */     } catch (Exception exception) {
/* 287 */       writeLog(exception);
/* 288 */       throw exception;
/*     */     } finally {
/*     */       try {
/* 291 */         connStatement.close();
/* 292 */       } catch (Exception exception) {}
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void deleteWfNodeField() throws Exception {
/* 302 */     String str = "delete from  workflow_nodeform where nodeid=?";
/* 303 */     ConnStatement connStatement = null;
/*     */     try {
/* 305 */       connStatement = new ConnStatement();
/* 306 */       connStatement.setStatementSql(str);
/* 307 */       connStatement.setInt(1, this.nodeid);
/* 308 */       connStatement.executeUpdate();
/* 309 */     } catch (Exception exception) {
/* 310 */       writeLog(exception);
/* 311 */       throw exception;
/*     */     } finally {
/*     */       try {
/* 314 */         connStatement.close();
/* 315 */       } catch (Exception exception) {}
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void saveFormFieldAttr() throws Exception {
/* 324 */     if (this.formid == 0 || this.isbill < 0)
/* 325 */       return;  String str1 = "select count(*) from workflow_nodeform_form where formid=? and isbill=? and fieldid=?";
/* 326 */     String str2 = "insert into workflow_nodeform_form(formid,isbill,fieldid,isview,isedit,ismandatory,isalonerow,orderid) values(?,?,?,?,?,?,?,?) ";
/* 327 */     String str3 = "update workflow_nodeform_form set isview=?,isedit=?,ismandatory=?,isalonerow=?,orderid=? where formid=? and isbill=? and fieldid=?";
/* 328 */     ConnStatement connStatement = null;
/*     */     try {
/* 330 */       connStatement = new ConnStatement();
/* 331 */       connStatement.setStatementSql(str1);
/* 332 */       connStatement.setInt(1, this.formid);
/* 333 */       connStatement.setInt(2, this.isbill);
/* 334 */       connStatement.setInt(3, this.fieldid);
/* 335 */       connStatement.executeQuery();
/* 336 */       connStatement.next();
/* 337 */       int i = connStatement.getInt(1);
/* 338 */       if (i > 1) {
/* 339 */         RecordSet recordSet = new RecordSet();
/* 340 */         recordSet.execute("delete from workflow_nodeform_form where formid=" + this.formid + " and isbill=" + this.isbill + " and fieldid=" + this.fieldid);
/* 341 */         i = 0;
/*     */       } 
/* 343 */       if (i > 0) {
/* 344 */         connStatement.setStatementSql(str3);
/* 345 */         connStatement.setString(1, this.isview);
/* 346 */         connStatement.setString(2, this.isedit);
/* 347 */         connStatement.setString(3, this.ismandatory);
/* 348 */         connStatement.setString(4, this.isalonerow);
/* 349 */         connStatement.setFloat(5, Util.getFloatValue(this.orderid, 0.0F));
/* 350 */         connStatement.setInt(6, this.formid);
/* 351 */         connStatement.setInt(7, this.isbill);
/* 352 */         connStatement.setInt(8, this.fieldid);
/*     */       } else {
/* 354 */         connStatement.setStatementSql(str2);
/* 355 */         connStatement.setInt(1, this.formid);
/* 356 */         connStatement.setInt(2, this.isbill);
/* 357 */         connStatement.setInt(3, this.fieldid);
/* 358 */         connStatement.setString(4, this.isview);
/* 359 */         connStatement.setString(5, this.isedit);
/* 360 */         connStatement.setString(6, this.ismandatory);
/* 361 */         connStatement.setString(7, this.isalonerow);
/* 362 */         connStatement.setFloat(8, Util.getFloatValue(this.orderid, 0.0F));
/*     */       } 
/* 364 */       connStatement.executeUpdate();
/* 365 */     } catch (Exception exception) {
/* 366 */       writeLog(exception);
/* 367 */       throw exception;
/*     */     } finally {
/*     */       try {
/* 370 */         connStatement.close();
/* 371 */       } catch (Exception exception) {}
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void saveFormFieldAttr2() throws Exception {
/* 380 */     if (this.formid == 0 || this.isbill < 0)
/* 381 */       return;  String str1 = "select count(*) from workflow_nodeform_form where formid=? and isbill=? and fieldid=?";
/* 382 */     String str2 = "insert into workflow_nodeform_form(formid,isbill,fieldid,isview,isedit,ismandatory,orderid) values(?,?,?,?,?,?,?) ";
/* 383 */     String str3 = "update workflow_nodeform_form set isview=?,isedit=?,ismandatory=? where formid=? and isbill=? and fieldid=?";
/* 384 */     ConnStatement connStatement = null;
/*     */     try {
/* 386 */       connStatement = new ConnStatement();
/* 387 */       connStatement.setStatementSql(str1);
/* 388 */       connStatement.setInt(1, this.formid);
/* 389 */       connStatement.setInt(2, this.isbill);
/* 390 */       connStatement.setInt(3, this.fieldid);
/* 391 */       connStatement.executeQuery();
/* 392 */       connStatement.next();
/* 393 */       int i = connStatement.getInt(1);
/* 394 */       if (i > 1) {
/* 395 */         RecordSet recordSet = new RecordSet();
/* 396 */         recordSet.execute("delete from workflow_nodeform_form where formid=" + this.formid + " and isbill=" + this.isbill + " and fieldid=" + this.fieldid);
/* 397 */         i = 0;
/*     */       } 
/* 399 */       if (i > 0) {
/* 400 */         connStatement.setStatementSql(str3);
/* 401 */         connStatement.setString(1, this.isview);
/* 402 */         connStatement.setString(2, this.isedit);
/* 403 */         connStatement.setString(3, this.ismandatory);
/* 404 */         connStatement.setInt(4, this.formid);
/* 405 */         connStatement.setInt(5, this.isbill);
/* 406 */         connStatement.setInt(6, this.fieldid);
/*     */       } else {
/* 408 */         connStatement.setStatementSql(str2);
/* 409 */         connStatement.setInt(1, this.formid);
/* 410 */         connStatement.setInt(2, this.isbill);
/* 411 */         connStatement.setInt(3, this.fieldid);
/* 412 */         connStatement.setString(4, this.isview);
/* 413 */         connStatement.setString(5, this.isedit);
/* 414 */         connStatement.setString(6, this.ismandatory);
/* 415 */         connStatement.setFloat(7, Util.getFloatValue(this.orderid, 0.0F));
/*     */       } 
/* 417 */       connStatement.executeUpdate();
/* 418 */     } catch (Exception exception) {
/* 419 */       writeLog(exception);
/* 420 */       throw exception;
/*     */     } finally {
/*     */       try {
/* 423 */         connStatement.close();
/* 424 */       } catch (Exception exception) {}
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public void saveFreeWfNodeField() throws Exception {
/* 431 */     String str1 = "select count(*) from workflow_freenode_nodeform where nodeid=? and fieldid=? ";
/* 432 */     if (Util.getIntValue(this.configId) != -1) {
/* 433 */       str1 = str1 + " and layoutconfigid=?";
/*     */     }
/* 435 */     String str2 = "insert into workflow_freenode_nodeform(nodeid,fieldid,isview,isedit,ismandatory,orderid,layoutconfigid) values(?,?,?,?,?,?,?) ";
/* 436 */     String str3 = "update workflow_freenode_nodeform set isview=?,isedit=?,ismandatory=?,orderid=? where nodeid=? and fieldid=? ";
/* 437 */     if (Util.getIntValue(this.configId) != -1) {
/* 438 */       str3 = str3 + " and layoutconfigid=?";
/*     */     }
/*     */     
/* 441 */     ConnStatement connStatement = null;
/*     */     try {
/* 443 */       connStatement = new ConnStatement();
/* 444 */       connStatement.setStatementSql(str1);
/* 445 */       connStatement.setInt(1, this.nodeid);
/* 446 */       connStatement.setInt(2, this.fieldid);
/* 447 */       if (Util.getIntValue(this.configId) != -1) {
/* 448 */         connStatement.setInt(3, this.configId);
/*     */       }
/* 450 */       connStatement.executeQuery();
/* 451 */       connStatement.next();
/* 452 */       int i = connStatement.getInt(1);
/* 453 */       if (i > 1) {
/* 454 */         RecordSet recordSet = new RecordSet();
/* 455 */         recordSet.execute("delete from workflow_freenode_nodeform where nodeid=" + this.nodeid + " and fieldid=" + this.fieldid + " and layoutconfigid = " + this.configId);
/* 456 */         i = 0;
/*     */       } 
/* 458 */       if (i > 0) {
/* 459 */         connStatement.setStatementSql(str3);
/* 460 */         connStatement.setString(1, this.isview);
/* 461 */         connStatement.setString(2, this.isedit);
/* 462 */         connStatement.setString(3, this.ismandatory);
/* 463 */         connStatement.setFloat(4, Util.getFloatValue(this.orderid, 0.0F));
/* 464 */         connStatement.setInt(5, this.nodeid);
/* 465 */         connStatement.setInt(6, this.fieldid);
/* 466 */         if (Util.getIntValue(this.configId) != -1) {
/* 467 */           connStatement.setInt(7, this.configId);
/*     */         }
/*     */       } else {
/* 470 */         connStatement.setStatementSql(str2);
/* 471 */         connStatement.setInt(1, this.nodeid);
/* 472 */         connStatement.setInt(2, this.fieldid);
/* 473 */         connStatement.setString(3, this.isview);
/* 474 */         connStatement.setString(4, this.isedit);
/* 475 */         connStatement.setString(5, this.ismandatory);
/* 476 */         connStatement.setFloat(6, Util.getFloatValue(this.orderid, 0.0F));
/* 477 */         connStatement.setString(7, this.configId);
/*     */       } 
/* 479 */       connStatement.executeUpdate();
/* 480 */     } catch (Exception exception) {
/* 481 */       writeLog(exception);
/* 482 */       throw exception;
/*     */     } finally {
/*     */       try {
/* 485 */         connStatement.close();
/* 486 */       } catch (Exception exception) {}
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/workflow/WFNodeFieldMainManager.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */