/*    */ package weaver.workflow.workflow;
/*    */ 
/*    */ import org.apache.commons.logging.Log;
/*    */ import org.apache.commons.logging.LogFactory;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.InitServer;
/*    */ import weaver.workflow.request.WFUrgerManager;
/*    */ 
/*    */ 
/*    */ 
/*    */ public class WfUrgerAutoInsertPreProcessing
/*    */   implements Runnable
/*    */ {
/*    */   public static final String GLOBAL_MOBILE_PREPROCESSING_CACHE = "global_mobile_preprocessing_cache_4789190168661429013L";
/* 15 */   private Log log = LogFactory.getLog(WfUrgerAutoInsertPreProcessing.class.getName());
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public void run() {
/* 22 */     wfUrgerAutoInsert();
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   private void wfUrgerAutoInsert() {
/* 28 */     Thread thread = InitServer.getThreadPool().get(0);
/*    */     
/* 30 */     while (thread.isAlive()) {
/* 31 */       this.log.info("流程督办自动插入数据等待中......");
/*    */       try {
/* 33 */         Thread.sleep(2000L);
/* 34 */       } catch (InterruptedException interruptedException) {
/* 35 */         interruptedException.printStackTrace();
/*    */       } 
/*    */     } 
/*    */ 
/*    */     
/* 40 */     WFUrgerManager wFUrgerManager = new WFUrgerManager();
/* 41 */     wFUrgerManager.insertUrgerByWfid(0);
/* 42 */     this.log.info("流程督办自动插入数据成功.....");
/* 43 */     RecordSet recordSet = new RecordSet();
/* 44 */     recordSet.executeSql(" update WFURGER_AUTOINSERT set isrunning = 0, dataasync = 1 ");
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/workflow/WfUrgerAutoInsertPreProcessing.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */