/*     */ package weaver.workflow.workflow;
/*     */ 
/*     */ import weaver.common.util.xtree.TreeNode;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.filter.XssUtil;
/*     */ import weaver.general.Util;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class WorkflowCodeSeqReservedTree
/*     */ {
/*     */   public TreeNode getWorkflowTypeTreeList(TreeNode paramTreeNode, String paramString1, String paramString2, String paramString3, String paramString4, int paramInt, String paramString5) throws Exception {
/*  34 */     RecordSet recordSet = new RecordSet();
/*  35 */     XssUtil xssUtil = new XssUtil();
/*  36 */     String str1 = " and (istemplate<>'1' or istemplate is null) ";
/*  37 */     if (paramString3.equals("1")) {
/*  38 */       str1 = " and istemplate='1' ";
/*     */     }
/*  40 */     String str2 = null;
/*  41 */     if (paramInt == 1) {
/*  42 */       str2 = "select id,typename from workflow_type where  exists(select 1 from workflow_base where workflowtype=workflow_type.id  " + str1 + " and subcompanyid=" + paramString2 + paramString5 + ")order by dsporder";
/*     */     } else {
/*  44 */       str2 = "select id,typename from workflow_type where  exists(select 1 from workflow_base where workflowtype=workflow_type.id  " + str1 + paramString5 + ")order by dsporder";
/*     */     } 
/*     */     
/*  47 */     recordSet.executeSql(str2);
/*  48 */     while (recordSet.next()) {
/*  49 */       String str3 = Util.null2String(recordSet.getString("id"));
/*  50 */       if (str3.equals(""))
/*  51 */         str3 = "0"; 
/*  52 */       String str4 = Util.null2String(recordSet.getString("typename"));
/*  53 */       TreeNode treeNode = new TreeNode();
/*  54 */       treeNode.setTitle(str4);
/*  55 */       treeNode.setNodeId("workflowtype_" + str3);
/*  56 */       treeNode.setIcon("/LeftMenu/ThemeXP/folder2_wev8.gif");
/*  57 */       if (!str3.equals(paramString4)) {
/*  58 */         treeNode.setNodeXmlSrc("/workflow/workflow/WorkflowCodeSeqReserved_lefttwoXML.jsp?type=" + paramString1 + "&id=" + str3 + "&nodeid=" + treeNode.getNodeId() + "&subCompanyId=" + paramString2 + "&isTemplate=" + paramString3 + "&sqlwhere=" + xssUtil.put(paramString5));
/*     */       }
/*  60 */       treeNode.setHref("/workflow/workflow/WorkflowCodeSeqReservedHelp.jsp");
/*  61 */       treeNode.setTarget("wfmainFrame");
/*  62 */       treeNode.setOncheck("check(" + treeNode.getNodeId() + ")");
/*  63 */       paramTreeNode.addTreeNode(treeNode);
/*  64 */       if (str3.equals(paramString4)) {
/*  65 */         getWorkflowTreeList(treeNode, paramString1, paramString2, paramString3, paramString4, paramInt, paramString5);
/*     */       }
/*     */     } 
/*  68 */     return paramTreeNode;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public TreeNode getWorkflowTreeList(TreeNode paramTreeNode, String paramString1, String paramString2, String paramString3, String paramString4, int paramInt, String paramString5) throws Exception {
/*  83 */     RecordSet recordSet = new RecordSet();
/*  84 */     String str1 = " and (istemplate<>'1' or istemplate is null) ";
/*  85 */     if (paramString3.equals("1")) {
/*  86 */       str1 = " and istemplate='1' ";
/*     */     }
/*  88 */     if (paramInt == 1) {
/*  89 */       recordSet.executeSql("select id,workflowname,formId,isBill from workflow_base where workflowtype=" + paramString4 + " and subCompanyId=" + paramString2 + str1 + paramString5 + " order by workflowname");
/*     */     } else {
/*  91 */       recordSet.executeSql("select id,workflowname,formId,isBill from workflow_base where workflowtype=" + paramString4 + str1 + paramString5 + " order by workflowname");
/*     */     } 
/*  93 */     String str2 = null;
/*  94 */     String str3 = null;
/*  95 */     String str4 = null;
/*  96 */     while (recordSet.next()) {
/*  97 */       str2 = Util.null2String(recordSet.getString("id"));
/*  98 */       if (str2.equals(""))
/*  99 */         str2 = "0"; 
/* 100 */       String str = Util.null2String(recordSet.getString("workflowname"));
/* 101 */       str3 = Util.null2String(recordSet.getString("formId"));
/* 102 */       str4 = Util.null2String(recordSet.getString("isBill"));
/*     */       
/* 104 */       TreeNode treeNode = new TreeNode();
/* 105 */       treeNode.setTitle(str);
/* 106 */       treeNode.setNodeId("workflow_" + str2);
/* 107 */       treeNode.setIcon("/LeftMenu/ThemeXP/page_wev8.gif");
/* 108 */       treeNode.setHref("/workflow/workflow/WorkflowCodeSeqReservedSet.jsp?subCompanyId=" + paramString2 + "&isFromSubCompanyTree=true&workflowId=" + str2 + "&formId=" + str3 + "&isBill=" + str4);
/* 109 */       treeNode.setTarget("wfmainFrame");
/* 110 */       treeNode.setOncheck("check(" + treeNode.getNodeId() + ")");
/* 111 */       paramTreeNode.addTreeNode(treeNode);
/*     */     } 
/* 113 */     return paramTreeNode;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/workflow/WorkflowCodeSeqReservedTree.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */