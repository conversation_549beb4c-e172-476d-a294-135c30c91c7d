/*      */ package weaver.workflow.workflow;
/*      */ 
/*      */ import com.api.browser.bean.SearchConditionGroup;
/*      */ import com.api.browser.bean.SearchConditionItem;
/*      */ import com.api.browser.bean.SearchConditionOption;
/*      */ import com.api.browser.util.ConditionFactory;
/*      */ import com.api.browser.util.ConditionType;
/*      */ import java.util.ArrayList;
/*      */ import java.util.HashMap;
/*      */ import java.util.List;
/*      */ import java.util.Map;
/*      */ import java.util.UUID;
/*      */ import weaver.conn.RecordSet;
/*      */ import weaver.general.BaseBean;
/*      */ import weaver.general.ThreadVarLanguage;
/*      */ import weaver.general.TimeUtil;
/*      */ import weaver.general.Util;
/*      */ import weaver.hrm.User;
/*      */ import weaver.hrm.cachecenter.bean.WorkflowAdvancedComInfo;
/*      */ import weaver.hrm.cachecenter.bean.WorkflowRecSimpleComInfo;
/*      */ import weaver.systeminfo.SystemEnv;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ public class WfFwLimitUtil
/*      */   extends BaseBean
/*      */ {
/*      */   private boolean debug = true;
/*      */   
/*      */   public String generateID() {
/*   34 */     UUID uUID = UUID.randomUUID();
/*   35 */     return uUID.toString().replaceAll("-", "");
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static boolean checkForwardflag(String paramString) {
/*   45 */     if ("1".equals(paramString) || "2".equals(paramString) || "3".equals(paramString) || "4"
/*   46 */       .equals(paramString) || "5".equals(paramString) || "6".equals(paramString)) {
/*   47 */       return true;
/*      */     }
/*   49 */     return false;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean isOpenLimit(int paramInt1, int paramInt2, String paramString) {
/*   60 */     boolean bool = false;
/*   61 */     RecordSet recordSet = new RecordSet();
/*   62 */     String str = "select isopen from workflow_FwLimitSet where isopen='1' and fwtype='" + paramString + "' and nodeid=" + paramInt2 + " and wfid=" + paramInt1;
/*   63 */     recordSet.execute(str);
/*   64 */     if (recordSet.next()) {
/*   65 */       bool = true;
/*      */     }
/*   67 */     if (this.debug) writeLog(">>>>>>>>>isOpenLimit>>>sql=" + str); 
/*   68 */     return bool;
/*      */   }
/*      */   
/*      */   public boolean choiceOperator(int paramInt1, int paramInt2, String paramString) {
/*   72 */     boolean bool = false;
/*   73 */     RecordSet recordSet = new RecordSet();
/*   74 */     String str = "select isopen from workflow_FwLimitSet where isopen='1' and fwtype='" + paramString + "' and choiceoperator='1' and nodeid=" + paramInt2 + " and wfid=" + paramInt1;
/*   75 */     recordSet.execute(str);
/*   76 */     if (recordSet.next()) {
/*   77 */       bool = true;
/*      */     }
/*   79 */     if (this.debug) writeLog(">>>>>>>>>choiceOperator>>>sql=" + str); 
/*   80 */     return bool;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void syncAllNode(int paramInt1, int paramInt2, String paramString, User paramUser) {
/*   91 */     RecordSet recordSet = new RecordSet();
/*   92 */     String str1 = "";
/*   93 */     String str2 = "select distinct f.nodeid from workflow_flownode f,workflow_nodebase n where (n.isfreenode is null or n.isfreenode !='1') and f.nodeid=n.id and f.nodeid !=" + paramInt2 + " and f.workflowid=" + paramInt1;
/*   94 */     recordSet.execute(str2);
/*   95 */     while (recordSet.next()) {
/*   96 */       int i = recordSet.getInt("nodeid");
/*   97 */       str1 = str1 + "," + i;
/*      */     } 
/*   99 */     if (!"".equals(str1)) {
/*  100 */       str1 = str1.substring(1);
/*      */     }
/*  102 */     syncAllNode(paramInt1, paramInt2, paramString, paramUser, str1);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void syncAllNode(int paramInt1, int paramInt2, String paramString1, User paramUser, String paramString2) {
/*  114 */     int i = 0;
/*  115 */     String str1 = "";
/*  116 */     String str2 = "";
/*  117 */     String str3 = "";
/*  118 */     RecordSet recordSet = new RecordSet();
/*  119 */     String str4 = "select * from workflow_FwLimitSet where fwtype='" + paramString1 + "' and nodeid=" + paramInt2 + " and wfid=" + paramInt1 + " ";
/*  120 */     recordSet.execute(str4);
/*  121 */     if (recordSet.next()) {
/*  122 */       i = recordSet.getInt("id");
/*  123 */       str1 = Util.null2String(recordSet.getString("isopen"));
/*  124 */       str2 = Util.null2String(recordSet.getString("modetype"));
/*  125 */       str3 = Util.null2o(recordSet.getString("choiceoperator"));
/*      */     } 
/*      */     
/*  128 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  129 */     hashMap.put("id", i + "");
/*  130 */     hashMap.put("isopen", str1);
/*  131 */     hashMap.put("modetype", str2);
/*  132 */     hashMap.put("choiceoperator", str3);
/*      */     
/*  134 */     if (i > 0) {
/*      */       
/*  136 */       syncNodeAdvanced((Map)hashMap, paramInt1, paramInt2, paramString1, paramUser, paramString2);
/*      */       
/*  138 */       syncNodeSimple((Map)hashMap, paramInt1, paramInt2, paramString1, paramUser, paramString2);
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void saveAsNewNode(int paramInt1, int paramInt2, int paramInt3, int paramInt4, String paramString, int paramInt5) {
/*  155 */     int i = 0;
/*  156 */     String str1 = "";
/*  157 */     String str2 = "";
/*  158 */     String str3 = "";
/*  159 */     String str4 = paramInt2 + "";
/*  160 */     RecordSet recordSet = new RecordSet();
/*  161 */     String str5 = "select * from workflow_FwLimitSet where fwtype='" + paramString + "' and nodeid=" + paramInt4 + " and wfid=" + paramInt3 + " ";
/*  162 */     recordSet.execute(str5);
/*  163 */     if (recordSet.next()) {
/*  164 */       i = recordSet.getInt("id");
/*  165 */       str1 = Util.null2String(recordSet.getString("isopen"));
/*  166 */       str2 = Util.null2String(recordSet.getString("modetype"));
/*  167 */       str3 = Util.null2String(recordSet.getString("choiceoperator"));
/*      */     } 
/*      */     try {
/*  170 */       User user = new User();
/*  171 */       user.setUid(paramInt5);
/*      */       
/*  173 */       HashMap<Object, Object> hashMap = new HashMap<>();
/*  174 */       hashMap.put("id", i + "");
/*  175 */       hashMap.put("isopen", str1);
/*  176 */       hashMap.put("modetype", str2);
/*  177 */       hashMap.put("choiceoperator", str3);
/*      */       
/*  179 */       hashMap.put("oldwfid", paramInt3 + "");
/*  180 */       hashMap.put("oldnodeid", paramInt4 + "");
/*  181 */       hashMap.put("fromNewnode", "1");
/*      */       
/*  183 */       if (i > 0)
/*      */       {
/*  185 */         syncNodeAdvanced((Map)hashMap, paramInt1, paramInt2, paramString, user, str4);
/*      */         
/*  187 */         syncNodeSimple((Map)hashMap, paramInt1, paramInt2, paramString, user, str4);
/*      */       }
/*      */     
/*  190 */     } catch (Exception exception) {
/*  191 */       writeLog(exception);
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void syncNodeAdvanced(Map<String, String> paramMap, int paramInt1, int paramInt2, String paramString1, User paramUser, String paramString2) {
/*  206 */     String str1 = TimeUtil.getCurrentDateString();
/*  207 */     String str2 = TimeUtil.getOnlyCurrentTimeString();
/*  208 */     RecordSet recordSet1 = new RecordSet();
/*  209 */     RecordSet recordSet2 = new RecordSet();
/*  210 */     String str3 = "";
/*  211 */     String str4 = "";
/*  212 */     String str5 = Util.null2String(paramMap.get("isopen"));
/*  213 */     String str6 = Util.null2String(paramMap.get("modetype"));
/*  214 */     String str7 = Util.null2String(paramMap.get("choiceoperator"));
/*      */     
/*  216 */     int i = Util.getIntValue(paramMap.get("oldwfid"), -1);
/*  217 */     int j = Util.getIntValue(paramMap.get("oldnodeid"), -1);
/*  218 */     String str8 = Util.null2String(paramMap.get("fromNewnode"));
/*      */     
/*  220 */     if (this.debug) writeLog(">>>>>>syncNodeAdvanced>>>>0>>oldwfid=" + i + " oldnodeid=" + j + " fromNewnode=" + str8); 
/*  221 */     if (i <= 0 && j <= 0) {
/*  222 */       i = paramInt1;
/*  223 */       j = paramInt2;
/*      */     } 
/*  225 */     if (this.debug) writeLog(">>>>>>syncNodeAdvanced>>>>1>>oldwfid=" + i + " oldnodeid=" + j);
/*      */     
/*  227 */     if (!"".equals(paramString2)) {
/*  228 */       str4 = " and nodeid in (" + paramString2 + ") ";
/*      */     } else {
/*  230 */       str4 = " and 1=2 ";
/*      */     } 
/*      */     
/*  233 */     if (this.debug) writeLog(">>>>>>syncNodeAdvanced>>>>>>sqlwhere=" + str4);
/*      */     
/*  235 */     if (!"1".equals(str8)) {
/*      */       
/*  237 */       str3 = "delete from workflow_FwOperAdvanced where mainid in (select id from workflow_FwAdvanced where fwtype='" + paramString1 + "' " + str4 + " and wfid=" + paramInt1 + " )";
/*  238 */       recordSet1.execute(str3);
/*      */       
/*  240 */       str3 = "delete from workflow_FwRecAdvanced where mainid in (select id from workflow_FwAdvanced where fwtype='" + paramString1 + "' " + str4 + " and wfid=" + paramInt1 + " )";
/*  241 */       recordSet1.execute(str3);
/*      */       
/*  243 */       str3 = "delete from workflow_FwAdvanced where fwtype='" + paramString1 + "' " + str4 + " and wfid=" + paramInt1 + " ";
/*  244 */       recordSet1.execute(str3);
/*      */     } 
/*      */ 
/*      */ 
/*      */     
/*  249 */     if (this.debug) writeLog(str3);
/*      */     
/*  251 */     ArrayList<Integer> arrayList1 = new ArrayList();
/*  252 */     str3 = "select id from workflow_FwAdvanced where fwtype='" + paramString1 + "' and nodeid=" + j + " and wfid=" + i + " order by id ";
/*  253 */     recordSet1.execute(str3);
/*  254 */     while (recordSet1.next()) {
/*  255 */       arrayList1.add(Integer.valueOf(recordSet1.getInt("id")));
/*      */     }
/*      */     
/*  258 */     if (this.debug) writeLog(str3);
/*      */     
/*  260 */     ArrayList<Integer> arrayList2 = new ArrayList();
/*  261 */     List<String> list = Util.splitString2List(paramString2, ","); byte b;
/*  262 */     for (b = 0; b < list.size(); b++) {
/*  263 */       int k = Util.getIntValue(list.get(b));
/*  264 */       if (k > 0) {
/*  265 */         arrayList2.add(Integer.valueOf(k));
/*      */       }
/*      */     } 
/*  268 */     if (this.debug) writeLog(">>>>>>syncNodeAdvanced>>>>>>nodeids=" + arrayList2);
/*      */     
/*  270 */     for (b = 0; b < arrayList2.size(); b++) {
/*  271 */       int k = ((Integer)arrayList2.get(b)).intValue();
/*      */       
/*  273 */       str3 = "select id from workflow_FwLimitSet where fwtype='" + paramString1 + "' and nodeid=" + k + " and wfid=" + paramInt1 + " ";
/*  274 */       recordSet2.execute(str3);
/*  275 */       if (recordSet2.next()) {
/*  276 */         str3 = "update workflow_FwLimitSet set modetype=?,isopen=?,choiceoperator=?,lastoperator=?,lastoperatedate=?,lastoperatetime=? where fwtype='" + paramString1 + "' and nodeid=" + k + " and wfid=" + paramInt1 + " ";
/*  277 */         recordSet2.executeUpdate(str3, new Object[] { str6, str5, str7, Integer.valueOf(paramUser.getUID()), str1, str2 });
/*      */       } else {
/*  279 */         str3 = "insert into workflow_FwLimitSet(wfid,nodeid,fwtype,modetype,isopen,choiceoperator,lastoperator,lastoperatedate,lastoperatetime) values (?,?,?,?,?,?,?,?,?) ";
/*  280 */         recordSet2.executeUpdate(str3, new Object[] { Integer.valueOf(paramInt1), Integer.valueOf(k), paramString1, str6, str5, str7, Integer.valueOf(paramUser.getUID()), str1, str2 });
/*      */       } 
/*      */       
/*  283 */       for (byte b1 = 0; b1 < arrayList1.size(); b1++) {
/*  284 */         int m = ((Integer)arrayList1.get(b1)).intValue();
/*      */         
/*  286 */         int n = 0;
/*  287 */         String str = generateID();
/*  288 */         str3 = "insert into workflow_FwAdvanced (wfid,nodeid,fwtype,uuid) values (" + paramInt1 + "," + k + ",'" + paramString1 + "','" + str + "')";
/*  289 */         recordSet2.execute(str3);
/*      */         
/*  291 */         str3 = "select id from workflow_FwAdvanced where uuid='" + str + "'";
/*  292 */         recordSet2.execute(str3);
/*  293 */         if (recordSet2.next()) {
/*  294 */           n = recordSet2.getInt("id");
/*      */         }
/*      */         
/*  297 */         str3 = "insert into workflow_FwOperAdvanced (mainid,objtype,objid,rolelevel,joblevel,jobobjid,relationship,seclevel,seclevelMax) ";
/*  298 */         str3 = str3 + " select " + n + ",objtype,objid,rolelevel,joblevel,jobobjid,relationship,seclevel,seclevelMax from workflow_FwOperAdvanced ";
/*  299 */         str3 = str3 + " where mainid = " + m + " order by id";
/*  300 */         recordSet2.execute(str3);
/*      */         
/*  302 */         str3 = "insert into workflow_FwRecAdvanced (mainid,objtype,objid,rolelevel,joblevel,jobobjid,relationship,seclevel,seclevelMax) ";
/*  303 */         str3 = str3 + " select " + n + ",objtype,objid,rolelevel,joblevel,jobobjid,relationship,seclevel,seclevelMax from workflow_FwRecAdvanced ";
/*  304 */         str3 = str3 + " where mainid = " + m + " order by id";
/*  305 */         recordSet2.execute(str3);
/*      */       } 
/*      */     } 
/*  308 */     WorkflowAdvancedComInfo workflowAdvancedComInfo = new WorkflowAdvancedComInfo();
/*  309 */     workflowAdvancedComInfo.removeCache();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void syncNodeSimple(Map<String, String> paramMap, int paramInt1, int paramInt2, String paramString1, User paramUser, String paramString2) {
/*  323 */     String str1 = TimeUtil.getCurrentDateString();
/*  324 */     String str2 = TimeUtil.getOnlyCurrentTimeString();
/*  325 */     RecordSet recordSet1 = new RecordSet();
/*  326 */     RecordSet recordSet2 = new RecordSet();
/*  327 */     String str3 = "";
/*  328 */     String str4 = "";
/*  329 */     int i = Util.getIntValue(paramMap.get("id"));
/*  330 */     String str5 = Util.null2String(paramMap.get("isopen"));
/*  331 */     String str6 = Util.null2String(paramMap.get("modetype"));
/*  332 */     String str7 = Util.null2String(paramMap.get("fromNewnode"));
/*  333 */     String str8 = Util.null2String(paramMap.get("choiceoperator"));
/*      */     
/*  335 */     if (!"".equals(paramString2)) {
/*  336 */       str4 = " and nodeid in (" + paramString2 + ") ";
/*      */     } else {
/*  338 */       str4 = " and 1=2 ";
/*      */     } 
/*  340 */     if (this.debug) writeLog(">>>>>>syncNodeSimple>>>>fromNewnode=" + str7 + " sqlwhere=" + str4);
/*      */     
/*  342 */     if (!"1".equals(str7)) {
/*      */       
/*  344 */       str3 = "delete from workflow_FwRecSimple where mainid in (select id from workflow_FwLimitSet where fwtype='" + paramString1 + "' " + str4 + " and wfid=" + paramInt1 + ")";
/*  345 */       recordSet1.execute(str3);
/*      */     } 
/*      */     
/*  348 */     if (this.debug) writeLog(">>>>>>syncNodeSimple>>>>sql=" + str3);
/*      */     
/*  350 */     ArrayList<Integer> arrayList = new ArrayList();
/*  351 */     List<String> list = Util.splitString2List(paramString2, ","); byte b;
/*  352 */     for (b = 0; b < list.size(); b++) {
/*  353 */       int j = Util.getIntValue(list.get(b));
/*  354 */       if (j > 0) {
/*  355 */         arrayList.add(Integer.valueOf(j));
/*      */       }
/*      */     } 
/*  358 */     if (this.debug) writeLog(">>>>>>syncNodeSimple>>>>nodeids=" + arrayList);
/*      */     
/*  360 */     for (b = 0; b < arrayList.size(); b++) {
/*  361 */       int j = ((Integer)arrayList.get(b)).intValue();
/*  362 */       int k = 0;
/*  363 */       str3 = "select id from workflow_FwLimitSet where fwtype='" + paramString1 + "' and nodeid=" + j + " and wfid=" + paramInt1 + " ";
/*  364 */       recordSet2.execute(str3);
/*  365 */       if (recordSet2.next()) {
/*  366 */         k = recordSet2.getInt("id");
/*  367 */         str3 = "update workflow_FwLimitSet set modetype=?,isopen=?,choiceoperator=?,lastoperator=?,lastoperatedate=?,lastoperatetime=? where id=" + k;
/*  368 */         recordSet2.executeUpdate(str3, new Object[] { str6, str5, str8, Integer.valueOf(paramUser.getUID()), str1, str2 });
/*      */       } else {
/*  370 */         str3 = "insert into workflow_FwLimitSet(wfid,nodeid,fwtype,modetype,isopen,choiceoperator,lastoperator,lastoperatedate,lastoperatetime) values (?,?,?,?,?,?,?,?,?) ";
/*  371 */         recordSet2.executeUpdate(str3, new Object[] { Integer.valueOf(paramInt1), Integer.valueOf(j), paramString1, str6, str5, str8, Integer.valueOf(paramUser.getUID()), str1, str2 });
/*      */       } 
/*      */       
/*  374 */       if (k <= 0) {
/*  375 */         str3 = "select id from workflow_FwLimitSet where fwtype='" + paramString1 + "' and nodeid=" + j + " and wfid=" + paramInt1 + " ";
/*  376 */         recordSet2.execute(str3);
/*  377 */         if (recordSet2.next()) {
/*  378 */           k = recordSet2.getInt("id");
/*      */         }
/*      */       } 
/*  381 */       str3 = "insert into workflow_FwRecSimple (mainid,objtype,objid,rolelevel,joblevel,jobobjid,relationship,seclevel,seclevelMax) ";
/*  382 */       str3 = str3 + " select " + k + ",objtype,objid,rolelevel,joblevel,jobobjid,relationship,seclevel,seclevelMax from workflow_FwRecSimple where mainid=" + i + " order by id";
/*  383 */       recordSet2.execute(str3);
/*      */     } 
/*  385 */     WorkflowRecSimpleComInfo workflowRecSimpleComInfo = new WorkflowRecSimpleComInfo();
/*  386 */     workflowRecSimpleComInfo.removeCache();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public Map<String, Object> saveFwLimitBaseSet(User paramUser, Map<String, Object> paramMap) {
/*  396 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  397 */     int i = Util.getIntValue(Util.null2String(paramMap.get("wfid")), 0);
/*  398 */     int j = Util.getIntValue(Util.null2String(paramMap.get("nodeid")), 0);
/*  399 */     String str1 = Util.null2String(Util.null2String(paramMap.get("fwtype")));
/*  400 */     int k = Util.getIntValue(Util.null2String(paramMap.get("modetype")), 0);
/*  401 */     String str2 = Util.null2o(Util.null2String(paramMap.get("isopen")));
/*  402 */     int m = Util.getIntValue(Util.null2String(paramMap.get("issynAll")), 0);
/*  403 */     String str3 = Util.null2o(Util.null2String(paramMap.get("choiceoperator")));
/*      */     
/*  405 */     int n = 0;
/*  406 */     String str4 = TimeUtil.getCurrentDateString();
/*  407 */     String str5 = TimeUtil.getOnlyCurrentTimeString();
/*  408 */     String str6 = "";
/*  409 */     RecordSet recordSet = new RecordSet();
/*  410 */     str6 = "select id from workflow_FwLimitSet where fwtype='" + str1 + "' and nodeid=" + j + " and wfid=" + i + " ";
/*  411 */     recordSet.execute(str6);
/*  412 */     if (recordSet.next()) {
/*  413 */       n = recordSet.getInt("id");
/*  414 */       str6 = "update workflow_FwLimitSet set modetype=?,isopen=?,choiceoperator=?,lastoperator=?,lastoperatedate=?,lastoperatetime=? where id=" + n;
/*  415 */       recordSet.executeUpdate(str6, new Object[] { Integer.valueOf(k), str2, str3, Integer.valueOf(paramUser.getUID()), str4, str5 });
/*      */     } else {
/*  417 */       str6 = "insert into workflow_FwLimitSet(wfid,nodeid,fwtype,modetype,isopen,choiceoperator,lastoperator,lastoperatedate,lastoperatetime)";
/*  418 */       str6 = str6 + " values (" + i + "," + j + ",'" + str1 + "','" + k + "','" + str2 + "','" + str3 + "'," + paramUser.getUID() + ",'" + str4 + "','" + str5 + "') ";
/*  419 */       recordSet.execute(str6);
/*      */       
/*  421 */       str6 = "select id from workflow_FwLimitSet where fwtype='" + str1 + "' and nodeid=" + j + " and wfid=" + i + " ";
/*  422 */       recordSet.execute(str6);
/*  423 */       if (recordSet.next()) {
/*  424 */         n = recordSet.getInt("id");
/*      */       }
/*      */     } 
/*      */     
/*  428 */     if (n > 0 && m == 1) {
/*  429 */       syncAllNode(i, j, str1, paramUser);
/*      */     }
/*  431 */     WorkflowRecSimpleComInfo workflowRecSimpleComInfo = new WorkflowRecSimpleComInfo();
/*  432 */     workflowRecSimpleComInfo.removeCache();
/*      */     
/*  434 */     WorkflowAdvancedComInfo workflowAdvancedComInfo = new WorkflowAdvancedComInfo();
/*  435 */     workflowAdvancedComInfo.removeCache();
/*      */     
/*  437 */     if (n > 0) {
/*  438 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  439 */       hashMap1.put("id", Integer.valueOf(n));
/*      */       
/*  441 */       hashMap.put("success", "1");
/*  442 */       hashMap.put("msg", "" + SystemEnv.getHtmlLabelName(18758, ThreadVarLanguage.getLang()) + "");
/*  443 */       hashMap.put("hidden", hashMap1);
/*      */       
/*  445 */       paramMap.put("mainid", Integer.valueOf(n));
/*  446 */       List list = new ArrayList();
/*  447 */       Map<String, Object> map = getBaseSetData(paramMap, paramUser);
/*  448 */       if (map.containsKey("conditions")) {
/*  449 */         list = (List)map.get("conditions");
/*      */       }
/*  451 */       if (map.containsKey("groupitems")) {
/*  452 */         hashMap.put("groupitems", map.get("groupitems"));
/*      */       }
/*  454 */       if (map.containsKey("hidden")) {
/*  455 */         Map<?, ?> map1 = (Map)map.get("hidden");
/*  456 */         hashMap1.putAll(map1);
/*      */       } 
/*  458 */       hashMap.put("hidden", hashMap1);
/*  459 */       hashMap.put("conditions", list);
/*      */     } else {
/*  461 */       hashMap.put("success", "0");
/*  462 */       hashMap.put("msg", "" + SystemEnv.getHtmlLabelName(22620, ThreadVarLanguage.getLang()) + "");
/*      */     } 
/*  464 */     return (Map)hashMap;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public Map<String, Object> getLimitBaseSet(int paramInt1, int paramInt2, String paramString, User paramUser) {
/*  476 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  477 */     int i = 0;
/*  478 */     int j = 0;
/*  479 */     String str1 = "0";
/*  480 */     String str2 = "0";
/*      */     
/*  482 */     String str3 = TimeUtil.getCurrentDateString();
/*  483 */     String str4 = TimeUtil.getOnlyCurrentTimeString();
/*  484 */     String str5 = "";
/*  485 */     RecordSet recordSet = new RecordSet();
/*  486 */     str5 = "select * from workflow_FwLimitSet where fwtype='" + paramString + "' and nodeid=" + paramInt2 + " and wfid=" + paramInt1 + " ";
/*  487 */     recordSet.execute(str5);
/*  488 */     if (recordSet.next()) {
/*  489 */       i = recordSet.getInt("id");
/*  490 */       j = Util.getIntValue(recordSet.getString("modetype"));
/*  491 */       str1 = Util.null2o(recordSet.getString("isopen"));
/*  492 */       str2 = Util.null2o(recordSet.getString("choiceoperator"));
/*      */     } else {
/*  494 */       str5 = "insert into workflow_FwLimitSet(wfid,nodeid,fwtype,modetype,isopen,choiceoperator,lastoperator,lastoperatedate,lastoperatetime)";
/*  495 */       str5 = str5 + " values (" + paramInt1 + "," + paramInt2 + ",'" + paramString + "','" + j + "','" + str1 + "','" + str2 + "'," + paramUser.getUID() + ",'" + str3 + "','" + str4 + "') ";
/*  496 */       recordSet.execute(str5);
/*      */       
/*  498 */       str5 = "select * from workflow_FwLimitSet where fwtype='" + paramString + "' and nodeid=" + paramInt2 + " and wfid=" + paramInt1 + " ";
/*  499 */       recordSet.execute(str5);
/*  500 */       if (recordSet.next()) {
/*  501 */         i = recordSet.getInt("id");
/*  502 */         j = Util.getIntValue(recordSet.getString("modetype"));
/*  503 */         str1 = Util.null2o(recordSet.getString("isopen"));
/*  504 */         str2 = Util.null2o(recordSet.getString("choiceoperator"));
/*      */       } 
/*      */     } 
/*  507 */     hashMap.put("mainid", Integer.valueOf(i));
/*  508 */     hashMap.put("modetype", Integer.valueOf(j));
/*  509 */     hashMap.put("isopen", str1);
/*  510 */     hashMap.put("choiceoperator", str2);
/*  511 */     return (Map)hashMap;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public Map<String, Object> saveFwLimitSet(User paramUser, Map<String, Object> paramMap) {
/*  522 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*      */     
/*  524 */     int i = Util.getIntValue(Util.null2String(paramMap.get("wfid")), 0);
/*  525 */     int j = Util.getIntValue(Util.null2String(paramMap.get("nodeid")), 0);
/*  526 */     String str1 = Util.null2String(Util.null2String(paramMap.get("fwtype")));
/*      */     
/*  528 */     int k = Util.getIntValue(Util.null2String(paramMap.get("mainid")), 0);
/*  529 */     String str2 = Util.null2o(Util.null2String(paramMap.get("modetype")));
/*  530 */     int m = Util.getIntValue(Util.null2String(paramMap.get("tabid")), 0);
/*      */     
/*  532 */     int n = Util.getIntValue(Util.null2String(paramMap.get("objtype")));
/*  533 */     String str3 = Util.null2o(Util.null2String(paramMap.get("relationship")));
/*  534 */     int i1 = Util.getIntValue(Util.null2String(paramMap.get("seclevel")), 0);
/*  535 */     int i2 = Util.getIntValue(Util.null2String(paramMap.get("seclevelto")), 100);
/*      */     
/*  537 */     String str4 = Util.null2String(paramMap.get("departmentid"));
/*      */     
/*  539 */     String str5 = Util.null2String(paramMap.get("subids"));
/*      */     
/*  541 */     String str6 = Util.null2String(paramMap.get("userid"));
/*      */     
/*  543 */     String str7 = Util.null2String(paramMap.get("roleid"));
/*  544 */     int i3 = Util.getIntValue(Util.null2String(paramMap.get("rolelevel")));
/*      */     
/*  546 */     String str8 = Util.null2String(paramMap.get("jobid"));
/*  547 */     int i4 = Util.getIntValue(Util.null2String(paramMap.get("joblevel")));
/*  548 */     String str9 = "";
/*  549 */     if (i4 == 0) {
/*  550 */       str9 = Util.null2String(Util.null2String(paramMap.get("jobtitledepartment")));
/*  551 */     } else if (i4 == 1) {
/*  552 */       str9 = Util.null2String(Util.null2String(paramMap.get("jobtitlesubcompany")));
/*      */     } 
/*      */     
/*  555 */     RecordSet recordSet = new RecordSet();
/*  556 */     String str10 = "";
/*  557 */     if ("1".equals(str2)) {
/*      */       
/*  559 */       if (k <= 0) {
/*  560 */         String str = generateID();
/*  561 */         str10 = "insert into workflow_FwAdvanced (wfid,nodeid,fwtype,uuid) values (" + i + "," + j + ",'" + str1 + "','" + str + "')";
/*  562 */         recordSet.execute(str10);
/*      */         
/*  564 */         str10 = "select id from workflow_FwAdvanced where uuid='" + str + "'";
/*  565 */         recordSet.execute(str10);
/*  566 */         if (recordSet.next()) {
/*  567 */           k = recordSet.getInt("id");
/*      */         }
/*      */       } 
/*      */     } else {
/*  571 */       str10 = "select id from workflow_FwLimitSet where fwtype='" + str1 + "' and nodeid=" + j + " and wfid=" + i + " ";
/*  572 */       recordSet.execute(str10);
/*  573 */       if (recordSet.next()) {
/*  574 */         k = recordSet.getInt("id");
/*      */       }
/*      */     } 
/*      */     
/*  578 */     if (k <= 0) {
/*  579 */       hashMap1.put("success", "0");
/*  580 */       hashMap1.put("msg", "" + SystemEnv.getHtmlLabelName(10004350, ThreadVarLanguage.getLang()) + "mainid" + SystemEnv.getHtmlLabelName(10004351, ThreadVarLanguage.getLang()) + "");
/*  581 */       return (Map)hashMap1;
/*      */     } 
/*      */     
/*  584 */     if (n <= 9 || n == 15) {
/*  585 */       if ("1".equals(str2)) {
/*  586 */         if (m == 1) {
/*  587 */           str10 = "insert into workflow_FwRecAdvanced (mainid,objtype,relationship,seclevel,seclevelMax) values (?,?,?,?,?) ";
/*  588 */           recordSet.executeUpdate(str10, new Object[] { Integer.valueOf(k), Integer.valueOf(n), str3, Integer.valueOf(i1), Integer.valueOf(i2) });
/*      */         } else {
/*  590 */           str10 = "insert into workflow_FwOperAdvanced (mainid,objtype,seclevel,seclevelMax) values (?,?,?,?) ";
/*  591 */           recordSet.executeUpdate(str10, new Object[] { Integer.valueOf(k), Integer.valueOf(n), Integer.valueOf(i1), Integer.valueOf(i2) });
/*      */         } 
/*      */       } else {
/*  594 */         str10 = "insert into workflow_FwRecSimple (mainid,objtype,relationship,seclevel,seclevelMax) values (?,?,?,?,?) ";
/*  595 */         recordSet.executeUpdate(str10, new Object[] { Integer.valueOf(k), Integer.valueOf(n), str3, Integer.valueOf(i1), Integer.valueOf(i2) });
/*      */       }
/*      */     
/*  598 */     } else if (n == 10) {
/*  599 */       if ("1".equals(str2)) {
/*  600 */         if (m == 1) {
/*  601 */           str10 = "insert into workflow_FwRecAdvanced (mainid,objtype,objid,relationship,seclevel,seclevelMax) values (?,?,?,?,?,?) ";
/*  602 */           recordSet.executeUpdate(str10, new Object[] { Integer.valueOf(k), Integer.valueOf(n), str4, str3, Integer.valueOf(i1), Integer.valueOf(i2) });
/*      */         } else {
/*  604 */           str10 = "insert into workflow_FwOperAdvanced (mainid,objtype,objid,seclevel,seclevelMax) values (?,?,?,?,?) ";
/*  605 */           recordSet.executeUpdate(str10, new Object[] { Integer.valueOf(k), Integer.valueOf(n), str4, Integer.valueOf(i1), Integer.valueOf(i2) });
/*      */         } 
/*      */       } else {
/*  608 */         str10 = "insert into workflow_FwRecSimple (mainid,objtype,objid,relationship,seclevel,seclevelMax) values (?,?,?,?,?,?) ";
/*  609 */         recordSet.executeUpdate(str10, new Object[] { Integer.valueOf(k), Integer.valueOf(n), str4, str3, Integer.valueOf(i1), Integer.valueOf(i2) });
/*      */       } 
/*  611 */     } else if (n == 11) {
/*  612 */       if ("1".equals(str2)) {
/*  613 */         if (m == 1) {
/*  614 */           str10 = "insert into workflow_FwRecAdvanced (mainid,objtype,objid,relationship,seclevel,seclevelMax) values (?,?,?,?,?,?) ";
/*  615 */           recordSet.executeUpdate(str10, new Object[] { Integer.valueOf(k), Integer.valueOf(n), str5, str3, Integer.valueOf(i1), Integer.valueOf(i2) });
/*      */         } else {
/*  617 */           str10 = "insert into workflow_FwOperAdvanced (mainid,objtype,objid,seclevel,seclevelMax) values (?,?,?,?,?) ";
/*  618 */           recordSet.executeUpdate(str10, new Object[] { Integer.valueOf(k), Integer.valueOf(n), str5, Integer.valueOf(i1), Integer.valueOf(i2) });
/*      */         } 
/*      */       } else {
/*  621 */         str10 = "insert into workflow_FwRecSimple (mainid,objtype,objid,relationship,seclevel,seclevelMax) values (?,?,?,?,?,?) ";
/*  622 */         recordSet.executeUpdate(str10, new Object[] { Integer.valueOf(k), Integer.valueOf(n), str5, str3, Integer.valueOf(i1), Integer.valueOf(i2) });
/*      */       } 
/*  624 */     } else if (n == 12) {
/*  625 */       if ("1".equals(str2)) {
/*  626 */         if (m == 1) {
/*  627 */           str10 = "insert into workflow_FwRecAdvanced (mainid,objtype,objid,relationship) values (?,?,?,?) ";
/*  628 */           recordSet.executeUpdate(str10, new Object[] { Integer.valueOf(k), Integer.valueOf(n), str6, str3 });
/*      */         } else {
/*  630 */           str10 = "insert into workflow_FwOperAdvanced (mainid,objtype,objid,relationship) values (?,?,?,?) ";
/*  631 */           recordSet.executeUpdate(str10, new Object[] { Integer.valueOf(k), Integer.valueOf(n), str6, str3 });
/*      */         } 
/*      */       } else {
/*  634 */         str10 = "insert into workflow_FwRecSimple (mainid,objtype,objid,relationship) values (?,?,?,?) ";
/*  635 */         recordSet.executeUpdate(str10, new Object[] { Integer.valueOf(k), Integer.valueOf(n), str6, str3 });
/*      */       } 
/*  637 */     } else if (n == 13) {
/*  638 */       if ("1".equals(str2)) {
/*  639 */         if (m == 1) {
/*  640 */           str10 = "insert into workflow_FwRecAdvanced (mainid,objtype,objid,rolelevel,relationship,seclevel,seclevelMax) values (?,?,?,?,?,?,?) ";
/*  641 */           recordSet.executeUpdate(str10, new Object[] { Integer.valueOf(k), Integer.valueOf(n), str7, Integer.valueOf(i3), str3, Integer.valueOf(i1), Integer.valueOf(i2) });
/*      */         } else {
/*  643 */           str10 = "insert into workflow_FwOperAdvanced (mainid,objtype,objid,rolelevel,seclevel,seclevelMax) values (?,?,?,?,?,?) ";
/*  644 */           recordSet.executeUpdate(str10, new Object[] { Integer.valueOf(k), Integer.valueOf(n), str7, Integer.valueOf(i3), Integer.valueOf(i1), Integer.valueOf(i2) });
/*      */         } 
/*      */       } else {
/*  647 */         str10 = "insert into workflow_FwRecSimple (mainid,objtype,objid,rolelevel,relationship,seclevel,seclevelMax) values (?,?,?,?,?,?,?) ";
/*  648 */         recordSet.executeUpdate(str10, new Object[] { Integer.valueOf(k), Integer.valueOf(n), str7, Integer.valueOf(i3), str3, Integer.valueOf(i1), Integer.valueOf(i2) });
/*      */       } 
/*  650 */     } else if (n == 14) {
/*  651 */       if ("1".equals(str2)) {
/*  652 */         if (m == 1) {
/*  653 */           str10 = "insert into workflow_FwRecAdvanced (mainid,objtype,objid,joblevel,jobobjid,relationship) values (?,?,?,?,?,?) ";
/*  654 */           recordSet.executeUpdate(str10, new Object[] { Integer.valueOf(k), Integer.valueOf(n), str8, Integer.valueOf(i4), str9, str3 });
/*      */         } else {
/*  656 */           str10 = "insert into workflow_FwOperAdvanced (mainid,objtype,objid,joblevel,jobobjid) values (?,?,?,?,?) ";
/*  657 */           recordSet.executeUpdate(str10, new Object[] { Integer.valueOf(k), Integer.valueOf(n), str8, Integer.valueOf(i4), str9 });
/*      */         } 
/*      */       } else {
/*  660 */         str10 = "insert into workflow_FwRecSimple (mainid,objtype,objid,joblevel,jobobjid,relationship) values (?,?,?,?,?,?) ";
/*  661 */         recordSet.executeUpdate(str10, new Object[] { Integer.valueOf(k), Integer.valueOf(n), str8, Integer.valueOf(i4), str9, str3 });
/*      */       } 
/*      */     } 
/*  664 */     saveChangeLog(i, j, str1, paramUser);
/*      */     
/*  666 */     WorkflowRecSimpleComInfo workflowRecSimpleComInfo = new WorkflowRecSimpleComInfo();
/*  667 */     workflowRecSimpleComInfo.removeCache();
/*      */     
/*  669 */     WorkflowAdvancedComInfo workflowAdvancedComInfo = new WorkflowAdvancedComInfo();
/*  670 */     workflowAdvancedComInfo.removeCache();
/*      */     
/*  672 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/*  673 */     hashMap2.put("mainid", Integer.valueOf(k));
/*  674 */     hashMap2.put("wfid", Integer.valueOf(i));
/*  675 */     hashMap2.put("nodeid", Integer.valueOf(j));
/*  676 */     hashMap2.put("fwtype", str1);
/*  677 */     hashMap2.put("modetype", str2);
/*  678 */     hashMap2.put("tabid", Integer.valueOf(m));
/*      */     
/*  680 */     hashMap1.put("success", "1");
/*  681 */     hashMap1.put("msg", "" + SystemEnv.getHtmlLabelName(18758, ThreadVarLanguage.getLang()) + "");
/*  682 */     hashMap1.put("hidden", hashMap2);
/*      */     
/*  684 */     paramMap.put("mainid", Integer.valueOf(k));
/*  685 */     List list = new ArrayList();
/*  686 */     if ("1".equals(str2)) {
/*  687 */       Map<String, Object> map = getAdvanceData(paramMap, paramUser);
/*  688 */       if (map.containsKey("conditions")) {
/*  689 */         List list1 = (List)map.get("conditions");
/*      */       }
/*      */     } else {
/*  692 */       Map<String, Object> map = getBaseSetData(paramMap, paramUser);
/*  693 */       if (map.containsKey("conditions")) {
/*  694 */         list = (List)map.get("conditions");
/*      */       }
/*  696 */       if (map.containsKey("groupitems")) {
/*  697 */         hashMap1.put("groupitems", map.get("groupitems"));
/*      */       }
/*  699 */       if (map.containsKey("hidden")) {
/*  700 */         Map<?, ?> map1 = (Map)map.get("hidden");
/*  701 */         hashMap2.putAll(map1);
/*      */       } 
/*  703 */       hashMap1.put("hidden", hashMap2);
/*      */     } 
/*  705 */     hashMap1.put("conditions", list);
/*  706 */     return (Map)hashMap1;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public Map<String, Object> deleteFwLimitSet(User paramUser, Map<String, Object> paramMap) {
/*  716 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  717 */     String str1 = Util.null2String(Util.null2String(paramMap.get("ids")));
/*  718 */     if (str1.startsWith(",")) str1 = str1.substring(1); 
/*  719 */     if (str1.endsWith(",")) str1 = str1.substring(0, str1.length() - 1);
/*      */     
/*  721 */     int i = Util.getIntValue(Util.null2String(paramMap.get("wfid")), 0);
/*  722 */     int j = Util.getIntValue(Util.null2String(paramMap.get("nodeid")), 0);
/*  723 */     String str2 = Util.null2String(Util.null2String(paramMap.get("fwtype")));
/*  724 */     String str3 = Util.null2o(Util.null2String(paramMap.get("modetype")));
/*      */ 
/*      */     
/*  727 */     int k = Util.getIntValue(Util.null2String(paramMap.get("mainid")), 0);
/*  728 */     int m = Util.getIntValue(Util.null2String(paramMap.get("tabid")), 0);
/*      */     
/*  730 */     if ("".equals(str1)) {
/*  731 */       hashMap1.put("success", "0");
/*  732 */       hashMap1.put("msg", "" + SystemEnv.getHtmlLabelName(10004352, ThreadVarLanguage.getLang()) + "ids" + SystemEnv.getHtmlLabelName(18622, ThreadVarLanguage.getLang()) + "");
/*  733 */       return (Map)hashMap1;
/*      */     } 
/*      */     
/*  736 */     RecordSet recordSet = new RecordSet();
/*  737 */     String str4 = "";
/*  738 */     if ("1".equals(str3)) {
/*  739 */       if (m == 1) {
/*  740 */         if (k < 0) {
/*  741 */           str4 = "select mainid from workflow_FwRecAdvanced where id in (" + str1 + ") ";
/*  742 */           recordSet.execute(str4);
/*  743 */           if (recordSet.next()) {
/*  744 */             k = recordSet.getInt("mainid");
/*      */           }
/*      */         } 
/*  747 */         str4 = "delete from workflow_FwRecAdvanced where id in (" + str1 + ") ";
/*  748 */         recordSet.execute(str4);
/*      */       } else {
/*  750 */         if (k < 0) {
/*  751 */           str4 = "select mainid from workflow_FwOperAdvanced where id in (" + str1 + ") ";
/*  752 */           recordSet.execute(str4);
/*  753 */           if (recordSet.next()) {
/*  754 */             k = recordSet.getInt("mainid");
/*      */           }
/*      */         } 
/*  757 */         str4 = "delete from workflow_FwOperAdvanced where id in (" + str1 + ") ";
/*  758 */         recordSet.execute(str4);
/*      */       } 
/*      */       
/*  761 */       str4 = "select id from workflow_FwRecAdvanced where mainid=" + k + " union select id from workflow_FwOperAdvanced where mainid=" + k;
/*  762 */       recordSet.execute(str4);
/*  763 */       if (!recordSet.next()) {
/*  764 */         str4 = "delete from workflow_FwAdvanced where id=" + k;
/*  765 */         recordSet.execute(str4);
/*      */       } 
/*      */     } else {
/*  768 */       str4 = "delete from workflow_FwRecSimple where id in (" + str1 + ") ";
/*  769 */       recordSet.execute(str4);
/*      */     } 
/*  771 */     saveChangeLog(i, j, str2, paramUser);
/*      */     
/*  773 */     WorkflowRecSimpleComInfo workflowRecSimpleComInfo = new WorkflowRecSimpleComInfo();
/*  774 */     workflowRecSimpleComInfo.removeCache();
/*      */     
/*  776 */     WorkflowAdvancedComInfo workflowAdvancedComInfo = new WorkflowAdvancedComInfo();
/*  777 */     workflowAdvancedComInfo.removeCache();
/*      */     
/*  779 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/*  780 */     hashMap2.put("mainid", Integer.valueOf(k));
/*  781 */     hashMap2.put("wfid", Integer.valueOf(i));
/*  782 */     hashMap2.put("nodeid", Integer.valueOf(j));
/*  783 */     hashMap2.put("fwtype", str2);
/*  784 */     hashMap2.put("modetype", str3);
/*  785 */     hashMap2.put("tabid", Integer.valueOf(m));
/*      */     
/*  787 */     hashMap1.put("success", "1");
/*  788 */     hashMap1.put("hidden", hashMap2);
/*  789 */     hashMap1.put("msg", "" + SystemEnv.getHtmlLabelName(20461, ThreadVarLanguage.getLang()) + "");
/*      */     
/*  791 */     paramMap.put("mainid", Integer.valueOf(k));
/*  792 */     List list = new ArrayList();
/*  793 */     if ("1".equals(str3)) {
/*  794 */       Map<String, Object> map = getAdvanceData(paramMap, paramUser);
/*  795 */       if (map.containsKey("conditions")) {
/*  796 */         List list1 = (List)map.get("conditions");
/*      */       }
/*      */     } else {
/*  799 */       Map<String, Object> map = getBaseSetData(paramMap, paramUser);
/*  800 */       if (map.containsKey("conditions")) {
/*  801 */         list = (List)map.get("conditions");
/*      */       }
/*  803 */       if (map.containsKey("groupitems")) {
/*  804 */         hashMap1.put("groupitems", map.get("groupitems"));
/*      */       }
/*  806 */       if (map.containsKey("hidden")) {
/*  807 */         Map<?, ?> map1 = (Map)map.get("hidden");
/*  808 */         hashMap2.putAll(map1);
/*      */       } 
/*  810 */       hashMap1.put("hidden", hashMap2);
/*      */     } 
/*  812 */     hashMap1.put("conditions", list);
/*  813 */     return (Map)hashMap1;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public Map<String, Object> deleteFwLimitSetByFwAdvanceId(User paramUser, Map<String, Object> paramMap) {
/*  823 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  824 */     String str1 = Util.null2String(Util.null2String(paramMap.get("ids")));
/*  825 */     if (str1.startsWith(",")) str1 = str1.substring(1); 
/*  826 */     if (str1.endsWith(",")) str1 = str1.substring(0, str1.length() - 1);
/*      */     
/*  828 */     int i = Util.getIntValue(Util.null2String(paramMap.get("wfid")), 0);
/*  829 */     int j = Util.getIntValue(Util.null2String(paramMap.get("nodeid")), 0);
/*  830 */     String str2 = Util.null2String(Util.null2String(paramMap.get("fwtype")));
/*  831 */     String str3 = Util.null2o(Util.null2String(paramMap.get("modetype")));
/*      */     
/*  833 */     if ("".equals(str1)) {
/*  834 */       hashMap1.put("success", "0");
/*  835 */       hashMap1.put("msg", "" + SystemEnv.getHtmlLabelName(10004352, ThreadVarLanguage.getLang()) + "ids" + SystemEnv.getHtmlLabelName(18622, ThreadVarLanguage.getLang()) + "");
/*  836 */       return (Map)hashMap1;
/*      */     } 
/*  838 */     String str4 = "";
/*  839 */     RecordSet recordSet = new RecordSet();
/*      */     
/*  841 */     str4 = "delete from workflow_FwOperAdvanced where mainid in (" + str1 + ") ";
/*  842 */     recordSet.execute(str4);
/*      */ 
/*      */     
/*  845 */     str4 = "delete from workflow_FwRecAdvanced where mainid in (" + str1 + ") ";
/*  846 */     recordSet.execute(str4);
/*      */ 
/*      */     
/*  849 */     str4 = "delete from workflow_FwAdvanced where id in (" + str1 + ") ";
/*  850 */     recordSet.execute(str4);
/*      */     
/*  852 */     saveChangeLog(i, j, str2, paramUser);
/*      */     
/*  854 */     WorkflowRecSimpleComInfo workflowRecSimpleComInfo = new WorkflowRecSimpleComInfo();
/*  855 */     workflowRecSimpleComInfo.removeCache();
/*      */     
/*  857 */     WorkflowAdvancedComInfo workflowAdvancedComInfo = new WorkflowAdvancedComInfo();
/*  858 */     workflowAdvancedComInfo.removeCache();
/*      */     
/*  860 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/*  861 */     hashMap2.put("wfid", Integer.valueOf(i));
/*  862 */     hashMap2.put("nodeid", Integer.valueOf(j));
/*  863 */     hashMap2.put("fwtype", str2);
/*  864 */     hashMap2.put("modetype", str3);
/*      */     
/*  866 */     hashMap1.put("success", "1");
/*  867 */     hashMap1.put("msg", "" + SystemEnv.getHtmlLabelName(20461, ThreadVarLanguage.getLang()) + "");
/*      */     
/*  869 */     List list = new ArrayList();
/*  870 */     Map<String, Object> map = getBaseSetData(paramMap, paramUser);
/*  871 */     if (map.containsKey("conditions")) {
/*  872 */       list = (List)map.get("conditions");
/*      */     }
/*  874 */     if (map.containsKey("groupitems")) {
/*  875 */       hashMap1.put("groupitems", map.get("groupitems"));
/*      */     }
/*  877 */     if (map.containsKey("hidden")) {
/*  878 */       Map<?, ?> map1 = (Map)map.get("hidden");
/*  879 */       hashMap2.putAll(map1);
/*      */     } 
/*  881 */     hashMap1.put("hidden", hashMap2);
/*  882 */     hashMap1.put("conditions", list);
/*  883 */     return (Map)hashMap1;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void saveChangeLog(int paramInt1, int paramInt2, String paramString, User paramUser) {
/*  894 */     String str1 = TimeUtil.getCurrentDateString();
/*  895 */     String str2 = TimeUtil.getOnlyCurrentTimeString();
/*  896 */     RecordSet recordSet = new RecordSet();
/*  897 */     String str3 = "update workflow_FwLimitSet set lastoperator=?,lastoperatedate=?,lastoperatetime=? where fwtype='" + paramString + "' and nodeid=" + paramInt2 + " and wfid=" + paramInt1 + " ";
/*  898 */     recordSet.executeUpdate(str3, new Object[] { Integer.valueOf(paramUser.getUID()), str1, str2 });
/*      */   }
/*      */ 
/*      */   
/*      */   public Map<String, Object> getBaseSetData(Map<String, Object> paramMap, User paramUser) {
/*  903 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*      */     
/*  905 */     int i = Util.getIntValue(Util.null2String(paramMap.get("wfid")), 0);
/*  906 */     int j = Util.getIntValue(Util.null2String(paramMap.get("nodeid")), 0);
/*  907 */     String str1 = Util.null2String(Util.null2String(paramMap.get("fwtype")));
/*  908 */     String str2 = Util.null2String(Util.null2String(paramMap.get("modetype")));
/*  909 */     int k = 0;
/*  910 */     String str3 = "0";
/*  911 */     String str4 = "";
/*  912 */     RecordSet recordSet = new RecordSet();
/*  913 */     WfFwLimitTransMethod wfFwLimitTransMethod = new WfFwLimitTransMethod();
/*  914 */     Map<String, Object> map = getLimitBaseSet(i, j, str1, paramUser);
/*  915 */     if ("".equals(str2)) {
/*  916 */       str2 = Util.null2o(Util.null2String(map.get("modetype")));
/*      */     }
/*  918 */     k = Util.getIntValue(Util.null2String(map.get("mainid")));
/*  919 */     str3 = Util.null2o(Util.null2String(map.get("isopen")));
/*  920 */     str4 = Util.null2o(Util.null2String(map.get("choiceoperator")));
/*      */ 
/*      */     
/*  923 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/*  924 */     hashMap2.put("wfid", Integer.valueOf(i));
/*  925 */     hashMap2.put("nodeid", Integer.valueOf(j));
/*  926 */     hashMap2.put("fwtype", str1);
/*  927 */     hashMap2.put("modetype", str2);
/*  928 */     hashMap2.put("isopen", str3);
/*  929 */     hashMap2.put("mainid", Integer.valueOf(k));
/*  930 */     hashMap2.put("modetypeTip", SystemEnv.getHtmlLabelName(386088, paramUser.getLanguage()));
/*  931 */     hashMap2.put("choiceoperator", str4);
/*  932 */     hashMap2.put("choiceoperatorTip", SystemEnv.getHtmlLabelName(501043, paramUser.getLanguage()));
/*  933 */     if ("6".equals(str1)) {
/*  934 */       hashMap2.put("choiceoperatorTip", SystemEnv.getHtmlLabelName(521995, paramUser.getLanguage()));
/*      */     }
/*      */     
/*  937 */     ArrayList<ArrayList<HashMap<Object, Object>>> arrayList = new ArrayList();
/*  938 */     ArrayList<HashMap<Object, Object>> arrayList1 = new ArrayList();
/*  939 */     HashMap<Object, Object> hashMap3 = new HashMap<>();
/*  940 */     if ("1".equals(str2)) {
/*      */       
/*  942 */       hashMap3.put("title", SystemEnv.getHtmlLabelName(99, paramUser.getLanguage()));
/*  943 */       hashMap3.put("dataIndex", "id1");
/*  944 */       hashMap3.put("key", "id1");
/*  945 */       arrayList1.add(hashMap3);
/*      */       
/*  947 */       hashMap3 = new HashMap<>();
/*  948 */       hashMap3.put("title", SystemEnv.getHtmlLabelName(386137, paramUser.getLanguage()));
/*  949 */       hashMap3.put("dataIndex", "id2");
/*  950 */       hashMap3.put("key", "id2");
/*  951 */       arrayList1.add(hashMap3);
/*  952 */       arrayList.add(arrayList1);
/*      */       
/*  954 */       arrayList1 = new ArrayList<>();
/*  955 */       String str = "select * from workflow_FwAdvanced where fwtype='" + str1 + "' and nodeid=" + j + " and wfid=" + i + " ORDER BY id";
/*  956 */       recordSet.execute(str);
/*  957 */       while (recordSet.next()) {
/*  958 */         String str5 = recordSet.getString("id");
/*  959 */         String str6 = wfFwLimitTransMethod.getOperatorShow(str5, paramUser.getLanguage() + "");
/*  960 */         String str7 = wfFwLimitTransMethod.getRecLimitShow(str5, paramUser.getLanguage() + "");
/*      */         
/*  962 */         hashMap3 = new HashMap<>();
/*  963 */         hashMap3.put("key", str5);
/*  964 */         hashMap3.put("id1", str6);
/*  965 */         hashMap3.put("id2", str7);
/*  966 */         arrayList1.add(hashMap3);
/*      */       } 
/*  968 */       arrayList.add(arrayList1);
/*      */     } else {
/*  970 */       hashMap3 = new HashMap<>();
/*  971 */       hashMap3.put("title", SystemEnv.getHtmlLabelName(386135, paramUser.getLanguage()));
/*  972 */       hashMap3.put("dataIndex", "objtype");
/*  973 */       hashMap3.put("key", "objtype");
/*  974 */       arrayList1.add(hashMap3);
/*      */       
/*  976 */       hashMap3 = new HashMap<>();
/*  977 */       hashMap3.put("title", SystemEnv.getHtmlLabelName(345, paramUser.getLanguage()));
/*  978 */       hashMap3.put("dataIndex", "objid");
/*  979 */       hashMap3.put("key", "objid");
/*  980 */       arrayList1.add(hashMap3);
/*      */       
/*  982 */       hashMap3 = new HashMap<>();
/*  983 */       hashMap3.put("title", SystemEnv.getHtmlLabelName(683, paramUser.getLanguage()));
/*  984 */       hashMap3.put("dataIndex", "seclevel");
/*  985 */       hashMap3.put("key", "seclevel");
/*  986 */       arrayList1.add(hashMap3);
/*      */       
/*  988 */       hashMap3 = new HashMap<>();
/*  989 */       hashMap3.put("title", SystemEnv.getHtmlLabelName(386136, paramUser.getLanguage()));
/*  990 */       hashMap3.put("dataIndex", "relationship");
/*  991 */       hashMap3.put("key", "relationship");
/*  992 */       arrayList1.add(hashMap3);
/*  993 */       arrayList.add(arrayList1);
/*      */       
/*  995 */       arrayList1 = new ArrayList<>();
/*  996 */       String str = "select * from workflow_FwRecSimple where mainid=" + k + " ORDER BY id";
/*  997 */       recordSet.execute(str);
/*  998 */       while (recordSet.next()) {
/*  999 */         String str5 = recordSet.getString("id");
/* 1000 */         String str6 = recordSet.getString("objtype");
/* 1001 */         String str7 = recordSet.getString("objid");
/* 1002 */         String str8 = recordSet.getString("seclevel");
/* 1003 */         String str9 = recordSet.getString("seclevelMax");
/* 1004 */         String str10 = recordSet.getString("relationship");
/* 1005 */         String str11 = recordSet.getString("rolelevel");
/* 1006 */         String str12 = recordSet.getString("joblevel");
/* 1007 */         String str13 = recordSet.getString("jobobjid");
/*      */         
/* 1009 */         String str14 = str6 + "+" + str11 + "+" + str12 + "+" + str13 + "+" + paramUser.getLanguage();
/* 1010 */         String str15 = wfFwLimitTransMethod.getObjTypeShow(str6, paramUser.getLanguage() + "");
/* 1011 */         String str16 = wfFwLimitTransMethod.getObjIdsShow(str7, str14);
/* 1012 */         String str17 = wfFwLimitTransMethod.getSecLevelShow(Util.getIntValue(str6), str8, str9);
/* 1013 */         String str18 = wfFwLimitTransMethod.getRelationshipShow(str10, paramUser.getLanguage() + "");
/*      */         
/* 1015 */         hashMap3 = new HashMap<>();
/* 1016 */         hashMap3.put("key", str5);
/* 1017 */         hashMap3.put("objtype", str15);
/* 1018 */         hashMap3.put("objid", str16);
/* 1019 */         hashMap3.put("seclevel", str17);
/* 1020 */         hashMap3.put("relationship", str18);
/* 1021 */         arrayList1.add(hashMap3);
/*      */       } 
/* 1023 */       arrayList.add(arrayList1);
/*      */     } 
/*      */     
/* 1026 */     ConditionFactory conditionFactory = new ConditionFactory(paramUser);
/* 1027 */     ArrayList<SearchConditionItem> arrayList2 = new ArrayList();
/* 1028 */     SearchConditionItem searchConditionItem1 = conditionFactory.createCondition(ConditionType.CHECKBOX, 21738, "issynAll");
/* 1029 */     searchConditionItem1.setColSpan(8);
/* 1030 */     searchConditionItem1.setFieldcol(16);
/* 1031 */     searchConditionItem1.setLabelcol(8);
/* 1032 */     if (!"6".equals(str1)) {
/* 1033 */       arrayList2.add(searchConditionItem1);
/*      */     }
/* 1035 */     searchConditionItem1 = conditionFactory.createCondition(ConditionType.SWITCH, 386133, "isopen");
/* 1036 */     searchConditionItem1.setValue(str3);
/* 1037 */     searchConditionItem1.setColSpan(8);
/* 1038 */     searchConditionItem1.setFieldcol(16);
/* 1039 */     searchConditionItem1.setLabelcol(8);
/* 1040 */     arrayList2.add(searchConditionItem1);
/*      */ 
/*      */     
/* 1043 */     if ("1".equals(str1) || "2".equals(str1) || "4".equals(str1) || "6".equals(str1)) {
/* 1044 */       int m = 500745;
/* 1045 */       if ("2".equals(str1)) m = 501045; 
/* 1046 */       if ("4".equals(str1)) m = 504414; 
/* 1047 */       if ("6".equals(str1)) m = 521994; 
/* 1048 */       searchConditionItem1 = conditionFactory.createCondition(ConditionType.SWITCH, m, "choiceoperator");
/* 1049 */       searchConditionItem1.setValue(str4);
/* 1050 */       searchConditionItem1.setColSpan(8);
/* 1051 */       searchConditionItem1.setFieldcol(16);
/* 1052 */       searchConditionItem1.setLabelcol(8);
/* 1053 */       arrayList2.add(searchConditionItem1);
/*      */     } 
/*      */     
/* 1056 */     ArrayList<SearchConditionOption> arrayList3 = new ArrayList();
/* 1057 */     arrayList3.add(new SearchConditionOption("0", SystemEnv.getHtmlLabelName(18016, paramUser.getLanguage()), !"1".equals(str2)));
/* 1058 */     arrayList3.add(new SearchConditionOption("1", SystemEnv.getHtmlLabelName(19048, paramUser.getLanguage()), "1".equals(str2)));
/* 1059 */     SearchConditionItem searchConditionItem2 = conditionFactory.createCondition(ConditionType.SELECT, 384810, "modetype", arrayList3);
/* 1060 */     searchConditionItem2.setDetailtype(3);
/* 1061 */     searchConditionItem2.setColSpan(8);
/* 1062 */     searchConditionItem2.setFieldcol(16);
/* 1063 */     searchConditionItem2.setLabelcol(8);
/* 1064 */     arrayList2.add(searchConditionItem2);
/*      */     
/* 1066 */     hashMap1.put("hidden", hashMap2);
/* 1067 */     hashMap1.put("groupitems", new SearchConditionGroup(SystemEnv.getHtmlLabelName(387260, paramUser.getLanguage()), true, arrayList2));
/* 1068 */     hashMap1.put("conditions", arrayList);
/* 1069 */     hashMap1.put("success", "1");
/* 1070 */     return (Map)hashMap1;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public Map<String, Object> getAdvanceData(Map<String, Object> paramMap, User paramUser) {
/* 1080 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*      */     
/* 1082 */     int i = Util.getIntValue(Util.null2String(paramMap.get("wfid")), 0);
/* 1083 */     int j = Util.getIntValue(Util.null2String(paramMap.get("nodeid")), 0);
/* 1084 */     String str1 = Util.null2String(Util.null2String(paramMap.get("fwtype")));
/*      */     
/* 1086 */     int k = Util.getIntValue(Util.null2String(paramMap.get("mainid")));
/*      */     
/* 1088 */     ArrayList<ArrayList<HashMap<Object, Object>>> arrayList = new ArrayList();
/* 1089 */     ArrayList<HashMap<Object, Object>> arrayList1 = new ArrayList();
/* 1090 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 1091 */     WfFwLimitTransMethod wfFwLimitTransMethod = new WfFwLimitTransMethod();
/*      */ 
/*      */     
/* 1094 */     HashMap<Object, Object> hashMap3 = new HashMap<>();
/* 1095 */     hashMap3.put("wfid", Integer.valueOf(i));
/* 1096 */     hashMap3.put("nodeid", Integer.valueOf(j));
/* 1097 */     hashMap3.put("fwtype", str1);
/* 1098 */     hashMap3.put("mainid", Integer.valueOf(k));
/*      */ 
/*      */     
/* 1101 */     hashMap2 = new HashMap<>();
/* 1102 */     hashMap2.put("title", SystemEnv.getHtmlLabelName(130470, paramUser.getLanguage()));
/* 1103 */     hashMap2.put("dataIndex", "objtype");
/* 1104 */     hashMap2.put("key", "objtype");
/* 1105 */     arrayList1.add(hashMap2);
/*      */     
/* 1107 */     hashMap2 = new HashMap<>();
/* 1108 */     hashMap2.put("title", SystemEnv.getHtmlLabelName(345, paramUser.getLanguage()));
/* 1109 */     hashMap2.put("dataIndex", "objid");
/* 1110 */     hashMap2.put("key", "objid");
/* 1111 */     arrayList1.add(hashMap2);
/*      */     
/* 1113 */     hashMap2 = new HashMap<>();
/* 1114 */     hashMap2.put("title", SystemEnv.getHtmlLabelName(683, paramUser.getLanguage()));
/* 1115 */     hashMap2.put("dataIndex", "seclevel");
/* 1116 */     hashMap2.put("key", "seclevel");
/* 1117 */     arrayList1.add(hashMap2);
/* 1118 */     arrayList.add(arrayList1);
/*      */     
/* 1120 */     arrayList1 = new ArrayList<>();
/* 1121 */     String str2 = "select * from workflow_FwOperAdvanced where mainid = " + k + " ORDER BY id";
/* 1122 */     RecordSet recordSet = new RecordSet();
/* 1123 */     recordSet.execute(str2);
/* 1124 */     while (recordSet.next()) {
/* 1125 */       int m = recordSet.getInt("id");
/* 1126 */       String str3 = recordSet.getString("objtype");
/* 1127 */       String str4 = recordSet.getString("objid");
/* 1128 */       String str5 = recordSet.getString("rolelevel");
/* 1129 */       String str6 = recordSet.getString("joblevel");
/* 1130 */       String str7 = recordSet.getString("jobobjid");
/* 1131 */       String str8 = recordSet.getString("seclevel");
/* 1132 */       String str9 = recordSet.getString("seclevelMax");
/*      */       
/* 1134 */       String str10 = wfFwLimitTransMethod.getObjTypeShow(str3, paramUser.getLanguage() + "");
/* 1135 */       String str11 = str3 + "+" + str5 + "+" + str6 + "+" + str7 + "+" + paramUser.getLanguage();
/* 1136 */       String str12 = wfFwLimitTransMethod.getObjIdsShow(str4, str11);
/* 1137 */       String str13 = wfFwLimitTransMethod.getSecLevelShow(Util.getIntValue(str3), str8, str9);
/*      */       
/* 1139 */       hashMap2 = new HashMap<>();
/* 1140 */       hashMap2.put("key", m + "");
/* 1141 */       hashMap2.put("objtype", str10);
/* 1142 */       hashMap2.put("objid", str12);
/* 1143 */       hashMap2.put("seclevel", str13);
/* 1144 */       arrayList1.add(hashMap2);
/*      */     } 
/* 1146 */     arrayList.add(arrayList1);
/*      */ 
/*      */     
/* 1149 */     arrayList1 = new ArrayList<>();
/* 1150 */     hashMap2 = new HashMap<>();
/* 1151 */     hashMap2.put("title", SystemEnv.getHtmlLabelName(386135, paramUser.getLanguage()));
/* 1152 */     hashMap2.put("dataIndex", "objtype");
/* 1153 */     hashMap2.put("key", "objtype");
/* 1154 */     arrayList1.add(hashMap2);
/*      */     
/* 1156 */     hashMap2 = new HashMap<>();
/* 1157 */     hashMap2.put("title", SystemEnv.getHtmlLabelName(345, paramUser.getLanguage()));
/* 1158 */     hashMap2.put("dataIndex", "objid");
/* 1159 */     hashMap2.put("key", "objid");
/* 1160 */     arrayList1.add(hashMap2);
/*      */     
/* 1162 */     hashMap2 = new HashMap<>();
/* 1163 */     hashMap2.put("title", SystemEnv.getHtmlLabelName(683, paramUser.getLanguage()));
/* 1164 */     hashMap2.put("dataIndex", "seclevel");
/* 1165 */     hashMap2.put("key", "seclevel");
/* 1166 */     arrayList1.add(hashMap2);
/*      */     
/* 1168 */     hashMap2 = new HashMap<>();
/* 1169 */     hashMap2.put("title", SystemEnv.getHtmlLabelName(386136, paramUser.getLanguage()));
/* 1170 */     hashMap2.put("dataIndex", "relationship");
/* 1171 */     hashMap2.put("key", "relationship");
/* 1172 */     arrayList1.add(hashMap2);
/* 1173 */     arrayList.add(arrayList1);
/*      */     
/* 1175 */     arrayList1 = new ArrayList<>();
/* 1176 */     str2 = "select * from workflow_FwRecAdvanced where mainid = " + k + " ORDER BY id";
/* 1177 */     recordSet.execute(str2);
/* 1178 */     while (recordSet.next()) {
/* 1179 */       int m = recordSet.getInt("id");
/* 1180 */       String str3 = recordSet.getString("objtype");
/* 1181 */       String str4 = recordSet.getString("objid");
/* 1182 */       String str5 = recordSet.getString("rolelevel");
/* 1183 */       String str6 = recordSet.getString("joblevel");
/* 1184 */       String str7 = recordSet.getString("jobobjid");
/* 1185 */       String str8 = recordSet.getString("seclevel");
/* 1186 */       String str9 = recordSet.getString("seclevelMax");
/* 1187 */       String str10 = recordSet.getString("relationship");
/*      */       
/* 1189 */       String str11 = wfFwLimitTransMethod.getObjTypeShow(str3, paramUser.getLanguage() + "");
/* 1190 */       String str12 = str3 + "+" + str5 + "+" + str6 + "+" + str7 + "+" + paramUser.getLanguage();
/* 1191 */       String str13 = wfFwLimitTransMethod.getObjIdsShow(str4, str12);
/* 1192 */       String str14 = wfFwLimitTransMethod.getSecLevelShow(Util.getIntValue(str3), str8, str9);
/* 1193 */       String str15 = wfFwLimitTransMethod.getRelationshipShow(str10, paramUser.getLanguage() + "");
/*      */       
/* 1195 */       hashMap2 = new HashMap<>();
/* 1196 */       hashMap2.put("key", m + "");
/* 1197 */       hashMap2.put("objtype", str11);
/* 1198 */       hashMap2.put("objid", str13);
/* 1199 */       hashMap2.put("seclevel", str14);
/* 1200 */       hashMap2.put("relationship", str15);
/* 1201 */       arrayList1.add(hashMap2);
/*      */     } 
/* 1203 */     arrayList.add(arrayList1);
/* 1204 */     hashMap1.put("conditions", arrayList);
/* 1205 */     hashMap1.put("hidden", hashMap3);
/* 1206 */     hashMap1.put("success", "1");
/* 1207 */     return (Map)hashMap1;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public Map<String, Object> canChoiceOperator(Map<String, Object> paramMap, User paramUser) {
/* 1217 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 1218 */     int i = Util.getIntValue(Util.null2String(paramMap.get("wfid")), 0);
/* 1219 */     int j = Util.getIntValue(Util.null2String(paramMap.get("nodeid")), 0);
/* 1220 */     String str1 = Util.null2String(Util.null2String(paramMap.get("fwtype")));
/* 1221 */     String str2 = choiceOperator(i, j, str1) ? "1" : "0";
/* 1222 */     hashMap.put("choiceoperator", str2);
/* 1223 */     hashMap.put("success", "1");
/* 1224 */     return (Map)hashMap;
/*      */   }
/*      */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/workflow/WfFwLimitUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */