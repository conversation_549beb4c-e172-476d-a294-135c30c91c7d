/*     */ package weaver.workflow.workflow;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashSet;
/*     */ import java.util.List;
/*     */ import weaver.cache.CacheBase;
/*     */ import weaver.cache.CacheColumn;
/*     */ import weaver.cache.CacheColumnType;
/*     */ import weaver.cache.PKColumn;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.company.SubCompanyComInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class WorkTypeComInfo
/*     */   extends CacheBase
/*     */ {
/*  32 */   protected static String TABLE_NAME = "workflow_type";
/*     */   
/*  34 */   protected static String TABLE_WHERE = null;
/*     */   
/*  36 */   protected static String TABLE_ORDER = "dsporder";
/*     */   
/*     */   @PKColumn(type = CacheColumnType.NUMBER)
/*  39 */   protected static String PK_NAME = "id";
/*     */   
/*     */   @CacheColumn
/*     */   protected static int typename;
/*     */   
/*     */   @CacheColumn
/*     */   protected static int typedesc;
/*     */   
/*     */   @CacheColumn
/*     */   protected static int dsporder;
/*     */   
/*     */   private static final String wftypeCloseIcon = "/images/treeimages/folderclose_wev8.gif";
/*     */   
/*     */   private static final String wftypeOpenIcon = "/images/treeimages/open_wev8.gif";
/*     */ 
/*     */   
/*     */   public int getWorkTypeNum() {
/*  56 */     return size();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public boolean next(String paramString) {
/*  64 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getWorkTypeid() {
/*  73 */     return (String)getRowValue(0);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getWorkTypename() {
/*  82 */     String str = (String)getRowValue(typename);
/*  83 */     str = Util.formatStringIfMultilang(str);
/*  84 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getWorkTypename(String paramString) {
/*  95 */     String str = (String)getValue(typename, paramString);
/*  96 */     str = Util.formatStringIfMultilang(str);
/*  97 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getWorkTypedesc() {
/* 106 */     return (String)getRowValue(typedesc);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getWorkTypedesc(String paramString) {
/* 117 */     return (String)getValue(typedesc, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getWorkDsporder() {
/* 126 */     return (String)getRowValue(dsporder);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getWorkDsporder(String paramString) {
/* 137 */     return (String)getValue(dsporder, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void removeWorkTypeCache() {
/* 144 */     removeCache();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getWFTypeInfo(String paramString) throws Exception {
/* 155 */     String str = "";
/* 156 */     RecordSet recordSet = new RecordSet();
/* 157 */     recordSet
/* 158 */       .execute("select b.workflowtype,max(a.isview) isview,max(a.isintervenor) isintervenor,max(a.isdelete) isdelete,max(a.isForceDrawBack) isForceDrawBack,max(a.isForceOver) isForceOver,max(a.issooperator) issooperator from workflow_monitor_bound a,workflow_base b where a.workflowid=b.id and a.monitorhrmid=" + paramString + " group by b.workflowtype");
/*     */     
/* 160 */     ArrayList<String> arrayList1 = new ArrayList();
/* 161 */     ArrayList<String> arrayList2 = new ArrayList();
/* 162 */     ArrayList<String> arrayList3 = new ArrayList();
/* 163 */     ArrayList<String> arrayList4 = new ArrayList();
/* 164 */     ArrayList<String> arrayList5 = new ArrayList();
/* 165 */     ArrayList<String> arrayList6 = new ArrayList();
/* 166 */     ArrayList<String> arrayList7 = new ArrayList();
/* 167 */     while (recordSet.next()) {
/* 168 */       arrayList1.add(recordSet.getString("workflowtype"));
/* 169 */       arrayList2.add(Util.getIntValue(recordSet.getString("isview"), 0) + "");
/* 170 */       arrayList3.add(Util.getIntValue(recordSet.getString("isintervenor"), 0) + "");
/* 171 */       arrayList4.add(Util.getIntValue(recordSet.getString("isdelete"), 0) + "");
/* 172 */       arrayList5.add(Util.getIntValue(recordSet.getString("isForceDrawBack"), 0) + "");
/* 173 */       arrayList6.add(Util.getIntValue(recordSet.getString("isForceOver"), 0) + "");
/* 174 */       arrayList7.add(Util.getIntValue(recordSet.getString("issooperator"), 0) + "");
/*     */     } 
/*     */     
/* 177 */     recordSet.executeSql("select id,typename from workflow_type order by dsporder");
/* 178 */     while (recordSet.next()) {
/* 179 */       String str1 = recordSet.getString(1);
/* 180 */       String str2 = Util.null2String(recordSet.getString(2)).replaceAll("'", "\\\\'");
/* 181 */       String str3 = "0";
/* 182 */       String str4 = "0";
/* 183 */       String str5 = "0";
/* 184 */       String str6 = "0";
/* 185 */       String str7 = "0";
/* 186 */       String str8 = "0";
/* 187 */       String str9 = "0";
/* 188 */       int i = arrayList1.indexOf(str1);
/* 189 */       if (i != -1) {
/* 190 */         str3 = "1";
/* 191 */         str4 = arrayList2.get(i);
/* 192 */         str5 = arrayList3.get(i);
/* 193 */         str6 = arrayList4.get(i);
/* 194 */         str7 = arrayList5.get(i);
/* 195 */         str8 = arrayList6.get(i);
/* 196 */         str9 = arrayList7.get(i);
/*     */       } 
/* 198 */       str = str + "tree.add(rti = new WebFXLoadTreeItem('" + str2 + "','WorkflowListTree.jsp?workflowType=" + str1 + "&userid=" + paramString + "','wftype|" + str1 + "|" + str3 + "|" + str4 + "|" + str5 + "|" + str6 + "|" + str7 + "|" + str8 + "|" + str9 + "','','" + "/images/treeimages/folderclose_wev8.gif" + "','" + "/images/treeimages/open_wev8.gif" + "'));";
/*     */     } 
/*     */     
/* 201 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getWFTypeInfo(String paramString1, String paramString2, int paramInt1, int paramInt2, int paramInt3) throws Exception {
/* 212 */     String str1 = "";
/* 213 */     RecordSet recordSet = new RecordSet();
/* 214 */     String str2 = "select b.workflowtype,max(a.isview) isview,max(a.isintervenor) isintervenor,max(a.isdelete) isdelete,max(a.isForceDrawBack) isForceDrawBack,max(a.isForceOver) isForceOver,max(a.issooperator) issooperator from workflow_monitor_bound a,workflow_base b where a.workflowid=b.id and a.monitorhrmid=" + paramString1 + " and (isvalid='1' or isvalid='2') ";
/*     */     
/* 216 */     if (paramInt3 == 1)
/* 217 */       if (recordSet.getDBType().equals("oracle")) {
/* 218 */         str2 = str2 + " and nvl(b.subcompanyid,0) = " + paramInt2;
/* 219 */       } else if (recordSet.getDBType().equals("mysql")) {
/* 220 */         str2 = str2 + " and ifnull(b.subcompanyid,0) = " + paramInt2;
/*     */       } else {
/* 222 */         str2 = str2 + " and isnull(b.subcompanyid,0) = " + paramInt2;
/*     */       }  
/* 224 */     if (recordSet.getDBType().equals("oracle")) {
/* 225 */       str2 = str2 + " and nvl(a.monitortype,0) = " + paramInt1;
/* 226 */     } else if (recordSet.getDBType().equals("mysql")) {
/* 227 */       str2 = str2 + " and ifnull(a.monitortype,0) = " + paramInt1;
/*     */     } else {
/* 229 */       str2 = str2 + " and isnull(a.monitortype,0) = " + paramInt1;
/*     */     } 
/* 231 */     str2 = str2 + " group by b.workflowtype";
/*     */     
/* 233 */     recordSet.executeSql(str2);
/* 234 */     ArrayList<String> arrayList1 = new ArrayList();
/* 235 */     ArrayList<String> arrayList2 = new ArrayList();
/* 236 */     ArrayList<String> arrayList3 = new ArrayList();
/* 237 */     ArrayList<String> arrayList4 = new ArrayList();
/* 238 */     ArrayList<String> arrayList5 = new ArrayList();
/* 239 */     ArrayList<String> arrayList6 = new ArrayList();
/* 240 */     ArrayList<String> arrayList7 = new ArrayList();
/* 241 */     while (recordSet.next()) {
/* 242 */       int i = arrayList1.indexOf(recordSet.getString("workflowtype"));
/*     */       
/* 244 */       if (i > -1) {
/* 245 */         if (Util.getIntValue(arrayList2.get(i), 0) != 1) {
/* 246 */           arrayList2.set(i, Util.getIntValue(recordSet.getString("isview"), 0) + "");
/*     */         }
/* 248 */         if (Util.getIntValue(arrayList3.get(i), 0) != 1) {
/* 249 */           arrayList3.set(i, Util.getIntValue(recordSet.getString("isintervenor"), 0) + "");
/*     */         }
/* 251 */         if (Util.getIntValue(arrayList4.get(i), 0) != 1) {
/* 252 */           arrayList4.set(i, Util.getIntValue(recordSet.getString("isdelete"), 0) + "");
/*     */         }
/* 254 */         if (Util.getIntValue(arrayList5.get(i), 0) != 1) {
/* 255 */           arrayList5.set(i, Util.getIntValue(recordSet.getString("isForceDrawBack"), 0) + "");
/*     */         }
/* 257 */         if (Util.getIntValue(arrayList6.get(i), 0) != 1) {
/* 258 */           arrayList6.set(i, Util.getIntValue(recordSet.getString("isForceOver"), 0) + "");
/*     */         }
/* 260 */         if (Util.getIntValue(arrayList7.get(i), 0) != 1)
/* 261 */           arrayList7.set(i, Util.getIntValue(recordSet.getString("issooperator"), 0) + ""); 
/*     */         continue;
/*     */       } 
/* 264 */       arrayList1.add(recordSet.getString("workflowtype"));
/* 265 */       arrayList2.add(Util.getIntValue(recordSet.getString("isview"), 0) + "");
/* 266 */       arrayList3.add(Util.getIntValue(recordSet.getString("isintervenor"), 0) + "");
/* 267 */       arrayList4.add(Util.getIntValue(recordSet.getString("isdelete"), 0) + "");
/* 268 */       arrayList5.add(Util.getIntValue(recordSet.getString("isForceDrawBack"), 0) + "");
/* 269 */       arrayList6.add(Util.getIntValue(recordSet.getString("isForceOver"), 0) + "");
/* 270 */       arrayList7.add(Util.getIntValue(recordSet.getString("issooperator"), 0) + "");
/*     */     } 
/*     */ 
/*     */     
/* 274 */     recordSet.executeSql("select id,typename from workflow_type order by dsporder");
/* 275 */     while (recordSet.next()) {
/* 276 */       String str3 = recordSet.getString(1);
/* 277 */       String str4 = Util.null2String(recordSet.getString(2)).replaceAll("'", "\\\\'");
/* 278 */       String str5 = "0";
/* 279 */       String str6 = "0";
/* 280 */       String str7 = "0";
/* 281 */       String str8 = "0";
/* 282 */       String str9 = "0";
/* 283 */       String str10 = "0";
/* 284 */       String str11 = "0";
/* 285 */       int i = arrayList1.indexOf(str3);
/* 286 */       if (i != -1) {
/* 287 */         str5 = "1";
/* 288 */         str6 = arrayList2.get(i);
/* 289 */         str7 = arrayList3.get(i);
/* 290 */         str8 = arrayList4.get(i);
/* 291 */         str9 = arrayList5.get(i);
/* 292 */         str10 = arrayList6.get(i);
/* 293 */         str11 = arrayList7.get(i);
/*     */       } 
/* 295 */       str1 = str1 + "tree.add(rti = new WebFXLoadTreeItem('" + str4 + "','WorkflowListTree.jsp?workflowType=" + str3 + "&userid=" + paramString1 + "&detachable=" + paramInt3 + "&subcompanyid=" + paramInt2 + "&typeid=" + paramInt1 + "','wftype|" + str3 + "|" + str5 + "|" + str6 + "|" + str7 + "|" + str8 + "|" + str9 + "|" + str10 + "|" + str11 + "','','" + "/images/treeimages/folderclose_wev8.gif" + "','" + "/images/treeimages/open_wev8.gif" + "'));";
/*     */     } 
/*     */     
/* 298 */     return str1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getMMTypeInfo(String paramString1, String paramString2, int paramInt1, int paramInt2, int paramInt3, String paramString3) throws Exception {
/* 309 */     String str1 = "";
/*     */     
/* 311 */     RecordSet recordSet = new RecordSet();
/* 312 */     String str2 = "select b.workflowtype,b.workflowname,max(a.isview) isview,max(a.isintervenor) isintervenor,max(a.isdelete) isdelete,max(a.isForceDrawBack) isForceDrawBack,max(a.isForceOver) isForceOver,max(a.issooperator) issooperator from workflow_monitor_bound a,workflow_base b where a.workflowid=b.id and a.monitorhrmid=" + paramString1 + " and (isvalid='1' or isvalid='2') ";
/*     */     
/* 314 */     if (paramInt3 == 1)
/* 315 */       if (recordSet.getDBType().equals("oracle")) {
/* 316 */         str2 = str2 + " and nvl(b.subcompanyid,0) = " + paramInt2;
/* 317 */       } else if (recordSet.getDBType().equals("mysql")) {
/* 318 */         str2 = str2 + " and ifnull(b.subcompanyid,0) = " + paramInt2;
/*     */       } else {
/* 320 */         str2 = str2 + " and isnull(b.subcompanyid,0) = " + paramInt2;
/*     */       }  
/* 322 */     if (recordSet.getDBType().equals("oracle")) {
/* 323 */       str2 = str2 + " and nvl(a.monitortype,0) = " + paramInt1;
/* 324 */     } else if (recordSet.getDBType().equals("mysql")) {
/* 325 */       str2 = str2 + " and ifnull(a.monitortype,0) = " + paramInt1;
/*     */     } else {
/* 327 */       str2 = str2 + " and isnull(a.monitortype,0) = " + paramInt1;
/*     */     } 
/* 329 */     str2 = str2 + " group by b.workflowtype,b.workflowname";
/*     */     
/* 331 */     recordSet.executeSql(str2);
/* 332 */     ArrayList<String> arrayList1 = new ArrayList();
/* 333 */     ArrayList<String> arrayList2 = new ArrayList();
/* 334 */     ArrayList<String> arrayList3 = new ArrayList();
/* 335 */     ArrayList<String> arrayList4 = new ArrayList();
/* 336 */     ArrayList<String> arrayList5 = new ArrayList();
/* 337 */     ArrayList<String> arrayList6 = new ArrayList();
/* 338 */     ArrayList<String> arrayList7 = new ArrayList();
/* 339 */     ArrayList<String> arrayList8 = new ArrayList();
/* 340 */     while (recordSet.next()) {
/* 341 */       int i = arrayList1.indexOf(recordSet.getString("workflowtype"));
/*     */       
/* 343 */       if (i > -1) {
/* 344 */         if (Util.getIntValue(arrayList3.get(i), 0) != 1) {
/* 345 */           arrayList3.set(i, Util.getIntValue(recordSet.getString("isview"), 0) + "");
/*     */         }
/* 347 */         if (Util.getIntValue(arrayList4.get(i), 0) != 1) {
/* 348 */           arrayList4.set(i, Util.getIntValue(recordSet.getString("isintervenor"), 0) + "");
/*     */         }
/* 350 */         if (Util.getIntValue(arrayList5.get(i), 0) != 1) {
/* 351 */           arrayList5.set(i, Util.getIntValue(recordSet.getString("isdelete"), 0) + "");
/*     */         }
/* 353 */         if (Util.getIntValue(arrayList6.get(i), 0) != 1) {
/* 354 */           arrayList6.set(i, Util.getIntValue(recordSet.getString("isForceDrawBack"), 0) + "");
/*     */         }
/* 356 */         if (Util.getIntValue(arrayList7.get(i), 0) != 1) {
/* 357 */           arrayList7.set(i, Util.getIntValue(recordSet.getString("isForceOver"), 0) + "");
/*     */         }
/* 359 */         if (Util.getIntValue(arrayList8.get(i), 0) != 1)
/* 360 */           arrayList8.set(i, Util.getIntValue(recordSet.getString("issooperator"), 0) + ""); 
/*     */         continue;
/*     */       } 
/* 363 */       arrayList2.add(recordSet.getString("workflowname"));
/* 364 */       arrayList1.add(recordSet.getString("workflowtype"));
/* 365 */       arrayList3.add(Util.getIntValue(recordSet.getString("isview"), 0) + "");
/* 366 */       arrayList4.add(Util.getIntValue(recordSet.getString("isintervenor"), 0) + "");
/* 367 */       arrayList5.add(Util.getIntValue(recordSet.getString("isdelete"), 0) + "");
/* 368 */       arrayList6.add(Util.getIntValue(recordSet.getString("isForceDrawBack"), 0) + "");
/* 369 */       arrayList7.add(Util.getIntValue(recordSet.getString("isForceOver"), 0) + "");
/* 370 */       arrayList8.add(Util.getIntValue(recordSet.getString("issooperator"), 0) + "");
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/* 375 */     if (!paramString3.equals("1")) {
/* 376 */       recordSet.executeSql("select id,typename from workflow_type order by dsporder");
/* 377 */       while (recordSet.next()) {
/* 378 */         String str3 = recordSet.getString(1);
/* 379 */         String str4 = Util.null2String(recordSet.getString(2)).replaceAll("'", "\\\\'");
/* 380 */         String str5 = "0";
/* 381 */         String str6 = "0";
/* 382 */         String str7 = "0";
/* 383 */         String str8 = "0";
/* 384 */         String str9 = "0";
/* 385 */         String str10 = "0";
/* 386 */         String str11 = "0";
/* 387 */         int i = arrayList1.indexOf(str3);
/* 388 */         if (i != -1) {
/* 389 */           str5 = "1";
/* 390 */           str6 = arrayList3.get(i);
/* 391 */           str7 = arrayList4.get(i);
/* 392 */           str8 = arrayList5.get(i);
/* 393 */           str9 = arrayList6.get(i);
/* 394 */           str10 = arrayList7.get(i);
/* 395 */           str11 = arrayList8.get(i);
/*     */         } 
/* 397 */         str1 = str1 + "tree.add(rti = new WebFXLoadTreeItem('" + str4 + "','WorkflowListTree.jsp?workflowType=" + str3 + "&userid=" + paramString1 + "&detachable=" + paramInt3 + "&subcompanyid=" + paramInt2 + "&typeid=" + paramInt1 + "','wftype|" + str3 + "|" + str5 + "|" + str6 + "|" + str7 + "|" + str8 + "|" + str9 + "|" + str10 + "|" + str11 + "','','" + "/images/treeimages/folderclose_wev8.gif" + "','" + "/images/treeimages/open_wev8.gif" + "'));";
/*     */       }
/*     */     
/*     */     } else {
/*     */       
/* 402 */       for (byte b = 0; b < arrayList1.size(); b++) {
/* 403 */         String str3 = arrayList1.get(b);
/* 404 */         String str4 = arrayList2.get(b);
/* 405 */         String str5 = "0";
/* 406 */         String str6 = "0";
/* 407 */         String str7 = "0";
/* 408 */         String str8 = "0";
/* 409 */         String str9 = "0";
/* 410 */         String str10 = "0";
/* 411 */         String str11 = "0";
/* 412 */         int i = arrayList1.indexOf(str3);
/* 413 */         if (i != -1) {
/* 414 */           str5 = "1";
/* 415 */           str6 = arrayList3.get(i);
/* 416 */           str7 = arrayList4.get(i);
/* 417 */           str8 = arrayList5.get(i);
/* 418 */           str9 = arrayList6.get(i);
/* 419 */           str10 = arrayList7.get(i);
/* 420 */           str11 = arrayList8.get(i);
/*     */         } 
/* 422 */         str1 = str1 + "tree.add(rti = new WebFXLoadTreeItem('" + str4 + "','WorkflowListTree.jsp?workflowType=" + str3 + "&userid=" + paramString1 + "&detachable=" + paramInt3 + "&subcompanyid=" + paramInt2 + "&typeid=" + paramInt1 + "','wftype|" + str3 + "|" + str5 + "|" + str6 + "|" + str7 + "|" + str8 + "|" + str9 + "|" + str10 + "|" + str11 + "','','" + "/images/treeimages/folderclose_wev8.gif" + "','" + "/images/treeimages/open_wev8.gif" + "'));";
/*     */       } 
/*     */     } 
/*     */     
/* 426 */     return str1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getMMTypeInfo2(String paramString1, String paramString2, int paramInt1, int paramInt2, int paramInt3, String paramString3) throws Exception {
/* 436 */     SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/* 437 */     String str1 = subCompanyComInfo.getRightSubCompany(Util.getIntValue(paramString2), "WorkflowMonitor:All", 0);
/* 438 */     String str2 = "";
/* 439 */     RecordSet recordSet = new RecordSet();
/* 440 */     String str3 = "select b.workflowtype,max(a.isview) isview,max(a.isintervenor) isintervenor,max(a.isdelete) isdelete,max(a.isForceDrawBack) isForceDrawBack,max(a.isForceOver) isForceOver,max(a.issooperator) issooperator from workflow_monitor_detail a,workflow_base b where a.workflowid=b.id and a.infoid=" + paramString1 + " and (isvalid='1' or isvalid='2') ";
/*     */     
/* 442 */     if (paramInt3 == 1)
/* 443 */       if (recordSet.getDBType().equals("oracle")) {
/*     */         
/* 445 */         str3 = str3 + " and (" + Util.getSubINClause(str1, "nvl(b.subcompanyid,0)", "in") + ")";
/* 446 */       } else if (recordSet.getDBType().equals("mysql")) {
/* 447 */         str3 = str3 + " and (" + Util.getSubINClause(str1, "ifnull(b.subcompanyid,0)", "in") + ")";
/*     */       } else {
/*     */         
/* 450 */         str3 = str3 + " and (" + Util.getSubINClause(str1, "isnull(b.subcompanyid,0)", "in") + ")";
/*     */       }  
/* 452 */     if (recordSet.getDBType().equals("oracle")) {
/* 453 */       str3 = str3 + " and nvl(a.monitortype,0) = " + paramInt1;
/* 454 */     } else if (recordSet.getDBType().equals("mysql")) {
/* 455 */       str3 = str3 + " and ifnull(a.monitortype,0) = " + paramInt1;
/*     */     } else {
/* 457 */       str3 = str3 + " and isnull(a.monitortype,0) = " + paramInt1;
/*     */     } 
/* 459 */     str3 = str3 + " group by b.workflowtype,b.workflowname";
/*     */     
/* 461 */     recordSet.executeSql(str3);
/* 462 */     ArrayList<String> arrayList1 = new ArrayList();
/* 463 */     ArrayList<String> arrayList2 = new ArrayList();
/* 464 */     ArrayList<String> arrayList3 = new ArrayList();
/* 465 */     ArrayList<String> arrayList4 = new ArrayList();
/* 466 */     ArrayList<String> arrayList5 = new ArrayList();
/* 467 */     ArrayList<String> arrayList6 = new ArrayList();
/* 468 */     ArrayList<String> arrayList7 = new ArrayList();
/* 469 */     ArrayList<String> arrayList8 = new ArrayList();
/* 470 */     while (recordSet.next()) {
/* 471 */       int i = arrayList1.indexOf(recordSet.getString("workflowtype"));
/*     */       
/* 473 */       if (i > -1) {
/* 474 */         if (Util.getIntValue(arrayList3.get(i), 0) != 1) {
/* 475 */           arrayList3.set(i, Util.getIntValue(recordSet.getString("isview"), 0) + "");
/*     */         }
/*     */         
/* 478 */         if (Util.getIntValue(arrayList4.get(i), 0) != 1) {
/* 479 */           arrayList4.set(i, Util.getIntValue(recordSet.getString("isintervenor"), 0) + "");
/*     */         }
/* 481 */         if (Util.getIntValue(arrayList5.get(i), 0) != 1) {
/* 482 */           arrayList5.set(i, Util.getIntValue(recordSet.getString("isdelete"), 0) + "");
/*     */         }
/* 484 */         if (Util.getIntValue(arrayList6.get(i), 0) != 1) {
/* 485 */           arrayList6.set(i, Util.getIntValue(recordSet.getString("isForceDrawBack"), 0) + "");
/*     */         }
/* 487 */         if (Util.getIntValue(arrayList7.get(i), 0) != 1) {
/* 488 */           arrayList7.set(i, Util.getIntValue(recordSet.getString("isForceOver"), 0) + "");
/*     */         }
/* 490 */         if (Util.getIntValue(arrayList8.get(i), 0) != 1)
/* 491 */           arrayList8.set(i, Util.getIntValue(recordSet.getString("issooperator"), 0) + ""); 
/*     */         continue;
/*     */       } 
/* 494 */       arrayList2.add(recordSet.getString("workflowname"));
/* 495 */       arrayList1.add(recordSet.getString("workflowtype"));
/* 496 */       arrayList3.add(Util.getIntValue(recordSet.getString("isview"), 0) + "");
/* 497 */       arrayList4.add(Util.getIntValue(recordSet.getString("isintervenor"), 0) + "");
/* 498 */       arrayList5.add(Util.getIntValue(recordSet.getString("isdelete"), 0) + "");
/* 499 */       arrayList6.add(Util.getIntValue(recordSet.getString("isForceDrawBack"), 0) + "");
/* 500 */       arrayList7.add(Util.getIntValue(recordSet.getString("isForceOver"), 0) + "");
/* 501 */       arrayList8.add(Util.getIntValue(recordSet.getString("issooperator"), 0) + "");
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/* 506 */     if (!paramString3.equals("1")) {
/* 507 */       if (paramInt3 == 1) {
/* 508 */         String str = "select id,typedesc from workflow_type a where exists (select 1 from workflow_base b where a.id = b.workflowtype and (b.isvalid='1' or b.isvalid='2')";
/*     */         
/* 510 */         if (str1.length() > 0) {
/* 511 */           if ("oracle".equalsIgnoreCase(recordSet.getDBType())) {
/*     */             
/* 513 */             str = str + " and (" + Util.getSubINClause(str1, "nvl(subcompanyid,-1)", "in") + ") )";
/* 514 */           } else if (recordSet.getDBType().equals("mysql")) {
/* 515 */             str = str + " and (" + Util.getSubINClause(str1, "ifnull(subcompanyid,-1)", "in") + ") )";
/*     */           } else {
/*     */             
/* 518 */             str = str + " and (" + Util.getSubINClause(str1, "isnull(subcompanyid,-1)", "in") + ") )";
/*     */           } 
/*     */         } else {
/* 521 */           str = str + " and 1 = 2 ";
/*     */         } 
/* 523 */         recordSet.executeQuery(str, new Object[0]);
/* 524 */         HashSet<String> hashSet = new HashSet();
/* 525 */         while (recordSet.next()) {
/* 526 */           hashSet.add(recordSet.getString(1));
/*     */         }
/* 528 */         recordSet.executeSql("select id,typename from workflow_type order by dsporder");
/* 529 */         while (recordSet.next()) {
/* 530 */           String str4 = recordSet.getString(1);
/* 531 */           if (!hashSet.contains(str4)) {
/*     */             continue;
/*     */           }
/* 534 */           String str5 = Util.null2String(recordSet.getString(2)).replaceAll("'", "\\\\'");
/* 535 */           String str6 = "0";
/* 536 */           String str7 = "0";
/* 537 */           String str8 = "0";
/* 538 */           String str9 = "0";
/* 539 */           String str10 = "0";
/* 540 */           String str11 = "0";
/* 541 */           String str12 = "0";
/* 542 */           int i = arrayList1.indexOf(str4);
/* 543 */           if (i != -1) {
/* 544 */             str6 = "1";
/* 545 */             str7 = arrayList3.get(i);
/* 546 */             str8 = arrayList4.get(i);
/* 547 */             str9 = arrayList5.get(i);
/* 548 */             str10 = arrayList6.get(i);
/* 549 */             str11 = arrayList7.get(i);
/* 550 */             str12 = arrayList8.get(i);
/*     */           } 
/* 552 */           str2 = str2 + "tree.add(rti = new WebFXLoadTreeItem('" + str5 + "','WorkflowListTree.jsp?workflowType=" + str4 + "&infoid=" + paramString1 + "&detachable=" + paramInt3 + "&subcompanyid=" + paramInt2 + "&typeid=" + paramInt1 + "','wftype|" + str4 + "|" + str6 + "|" + str7 + "|" + str8 + "|" + str9 + "|" + str10 + "|" + str11 + "|" + str12 + "','','" + "/images/treeimages/folderclose_wev8.gif" + "','" + "/images/treeimages/open_wev8.gif" + "'));";
/*     */         } 
/*     */       } else {
/*     */         
/* 556 */         recordSet.executeSql("select id,typename from workflow_type order by dsporder");
/* 557 */         while (recordSet.next()) {
/* 558 */           String str4 = recordSet.getString(1);
/* 559 */           String str5 = Util.null2String(recordSet.getString(2)).replaceAll("'", "\\\\'");
/* 560 */           String str6 = "0";
/* 561 */           String str7 = "0";
/* 562 */           String str8 = "0";
/* 563 */           String str9 = "0";
/* 564 */           String str10 = "0";
/* 565 */           String str11 = "0";
/* 566 */           String str12 = "0";
/* 567 */           int i = arrayList1.indexOf(str4);
/* 568 */           if (i != -1) {
/* 569 */             str6 = "1";
/* 570 */             str7 = arrayList3.get(i);
/* 571 */             str8 = arrayList4.get(i);
/* 572 */             str9 = arrayList5.get(i);
/* 573 */             str10 = arrayList6.get(i);
/* 574 */             str11 = arrayList7.get(i);
/* 575 */             str12 = arrayList8.get(i);
/*     */           } 
/* 577 */           str2 = str2 + "tree.add(rti = new WebFXLoadTreeItem('" + str5 + "','WorkflowListTree.jsp?workflowType=" + str4 + "&infoid=" + paramString1 + "&detachable=" + paramInt3 + "&subcompanyid=" + paramInt2 + "&typeid=" + paramInt1 + "','wftype|" + str4 + "|" + str6 + "|" + str7 + "|" + str8 + "|" + str9 + "|" + str10 + "|" + str11 + "|" + str12 + "','','" + "/images/treeimages/folderclose_wev8.gif" + "','" + "/images/treeimages/open_wev8.gif" + "'));";
/*     */         } 
/*     */       } 
/*     */     } else {
/*     */       
/* 582 */       for (byte b = 0; b < arrayList1.size(); b++) {
/* 583 */         String str4 = arrayList1.get(b);
/* 584 */         String str5 = arrayList2.get(b);
/* 585 */         String str6 = "0";
/* 586 */         String str7 = "0";
/* 587 */         String str8 = "0";
/* 588 */         String str9 = "0";
/* 589 */         String str10 = "0";
/* 590 */         String str11 = "0";
/* 591 */         String str12 = "0";
/* 592 */         int i = arrayList1.indexOf(str4);
/* 593 */         if (i != -1) {
/* 594 */           str6 = "1";
/* 595 */           str7 = arrayList3.get(i);
/* 596 */           str8 = arrayList4.get(i);
/* 597 */           str9 = arrayList5.get(i);
/* 598 */           str10 = arrayList6.get(i);
/* 599 */           str11 = arrayList7.get(i);
/* 600 */           str12 = arrayList8.get(i);
/*     */         } 
/* 602 */         str2 = str2 + "tree.add(rti = new WebFXLoadTreeItem('" + str5 + "','WorkflowListTree.jsp?workflowType=" + str4 + "&infoid=" + paramString1 + "&detachable=" + paramInt3 + "&subcompanyid=" + paramInt2 + "&typeid=" + paramInt1 + "','wftype|" + str4 + "|" + str6 + "|" + str7 + "|" + str8 + "|" + str9 + "|" + str10 + "|" + str11 + "|" + str12 + "','','" + "/images/treeimages/folderclose_wev8.gif" + "','" + "/images/treeimages/open_wev8.gif" + "'));";
/*     */       } 
/*     */     } 
/*     */     
/* 606 */     return str2;
/*     */   }
/*     */   
/*     */   public String getCanDelType(String paramString) {
/* 610 */     String str = "true";
/* 611 */     RecordSet recordSet = new RecordSet();
/* 612 */     recordSet.executeSql("SELECT DISTINCT workflowtype FROM workflow_base where workflowtype='" + paramString + "'");
/* 613 */     if (recordSet.next()) {
/* 614 */       str = "false";
/*     */     }
/* 616 */     return str;
/*     */   }
/*     */   
/*     */   public List getCanDelTypeList(String paramString) {
/* 620 */     ArrayList<String> arrayList = new ArrayList();
/* 621 */     arrayList.add("true");
/* 622 */     RecordSet recordSet = new RecordSet();
/* 623 */     recordSet.executeSql("SELECT DISTINCT workflowtype FROM workflow_base where workflowtype='" + paramString + "'");
/* 624 */     if (recordSet.next()) {
/* 625 */       arrayList.add("false");
/*     */     } else {
/* 627 */       arrayList.add("true");
/*     */     } 
/* 629 */     return arrayList;
/*     */   }
/*     */   
/*     */   public String getLinkType(String paramString1, String paramString2) {
/* 633 */     String str = "";
/* 634 */     str = "<a href=javaScript:newDialog(1," + paramString2 + ")>" + paramString1 + "</a>";
/* 635 */     return str;
/*     */   }
/*     */   
/*     */   public boolean hasWorkflowDoc(String paramString) {
/* 639 */     return hasWorkflowDoc(paramString, 1);
/*     */   }
/*     */   
/*     */   public boolean hasWorkflowDoc(String paramString, int paramInt) {
/* 643 */     if (paramInt != 1 && paramInt != 2)
/* 644 */       return false; 
/* 645 */     String str = "";
/* 646 */     if (paramInt == 1) {
/* 647 */       str = "1,3";
/*     */     } else {
/* 649 */       str = "2";
/*     */     } 
/* 651 */     RecordSet recordSet = new RecordSet();
/* 652 */     if (paramInt == 2) {
/* 653 */       recordSet.execute("select count(id) from workflow_base where isvalid=1 and id in(select workflowid from workflow_createdoc where status=1) and isWorkflowDoc=1 and workflowtype=" + paramString + " and officalType in (" + str + ")");
/*     */     } else {
/* 655 */       recordSet.execute("select count(id) from workflow_base where isvalid=1 and id in(select workflowid from workflow_createdoc where status=1 and flowDocField>0 and wfstatus=1) and isWorkflowDoc=1 and workflowtype=" + paramString + " and officalType in (" + str + ")");
/*     */     } 
/* 657 */     if (recordSet.next()) {
/* 658 */       return (recordSet.getInt(1) > 0);
/*     */     }
/* 660 */     return false;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/workflow/WorkTypeComInfo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */