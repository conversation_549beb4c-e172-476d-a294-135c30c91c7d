/*      */ package weaver.workflow.workflow;
/*      */ 
/*      */ import java.util.ArrayList;
/*      */ import javax.servlet.http.HttpServletRequest;
/*      */ import weaver.conn.RecordSet;
/*      */ import weaver.general.GCONST;
/*      */ import weaver.general.OrderProperties;
/*      */ import weaver.general.Util;
/*      */ import weaver.systeminfo.SystemEnv;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ public class WfLinkageInfo
/*      */ {
/*      */   private int workflowid;
/*      */   private int langurageid;
/*      */   private int formid;
/*      */   private int isbill;
/*      */   private int fieldid;
/*      */   private String searchfieldname;
/*      */   private String viewtype;
/*      */   
/*      */   public int getWorkflowid() {
/*   33 */     return this.workflowid;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setWorkflowid(int paramInt) {
/*   42 */     this.workflowid = paramInt;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public int getLangurageid() {
/*   51 */     return this.langurageid;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setLangurageid(int paramInt) {
/*   60 */     this.langurageid = paramInt;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public int getFormid() {
/*   69 */     return this.formid;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setFormid(int paramInt) {
/*   78 */     this.formid = paramInt;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public int getIsbill() {
/*   87 */     return this.isbill;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setIsbill(int paramInt) {
/*   96 */     this.isbill = paramInt;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getSearchfieldname() {
/*  105 */     return this.searchfieldname;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setSearchfieldname(String paramString) {
/*  114 */     this.searchfieldname = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getViewtype() {
/*  123 */     return this.viewtype;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setViewtype(String paramString) {
/*  132 */     this.viewtype = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public int getFieldid() {
/*  141 */     return this.fieldid;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setFieldid(int paramInt) {
/*  150 */     this.fieldid = paramInt;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public WfLinkageInfo() {
/*  157 */     this.workflowid = 0;
/*  158 */     this.formid = 0;
/*  159 */     this.isbill = 0;
/*  160 */     this.fieldid = 0;
/*  161 */     this.langurageid = 7;
/*  162 */     this.searchfieldname = "";
/*  163 */     this.viewtype = "";
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void init(int paramInt1, int paramInt2) {
/*  173 */     this.workflowid = paramInt1;
/*  174 */     this.langurageid = paramInt2;
/*  175 */     RecordSet recordSet = new RecordSet();
/*  176 */     String str = "select formid,isbill from workflow_base where id=" + paramInt1;
/*  177 */     recordSet.executeSql(str);
/*  178 */     if (recordSet.next()) {
/*  179 */       this.formid = Util.getIntValue(recordSet.getString("formid"));
/*  180 */       this.isbill = Util.getIntValue(recordSet.getString("isbill"), 0);
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public ArrayList[] getSelectFieldByEdit(int paramInt) {
/*  191 */     ArrayList[] arrayOfArrayList = new ArrayList[3];
/*  192 */     arrayOfArrayList[0] = new ArrayList();
/*  193 */     arrayOfArrayList[1] = new ArrayList();
/*  194 */     arrayOfArrayList[2] = new ArrayList();
/*  195 */     RecordSet recordSet = new RecordSet();
/*  196 */     if (paramInt == -1) {
/*  197 */       String str = "";
/*  198 */       if (this.isbill == 1) {
/*  199 */         str = " select a.id as fieldid,a.fieldlabel as fieldname,a.viewtype as isdetail from workflow_billfield a  where a.fieldhtmltype='5' and a.viewtype=0 and a.billid=" + this.formid + " order by a.viewtype,a.dsporder ";
/*      */       } else {
/*      */         
/*  202 */         str = "select a.fieldid,e.fieldlable as fieldname,a.isdetail,a.fieldorder from workflow_formfield a,workflow_formdict d,workflow_fieldlable e where d.id=a.fieldid and a.fieldid=e.fieldid and a.formid=e.formid and (a.isdetail is null or a.isdetail!='1')  and d.fieldhtmltype='5' and e.langurageid=" + this.langurageid + " and a.formid=" + this.formid;
/*      */ 
/*      */ 
/*      */         
/*  206 */         if ("oracle".equals(recordSet.getDBType())) {
/*  207 */           str = str + " order by isdetail desc,fieldorder";
/*      */         } else {
/*  209 */           str = str + " order by isdetail,fieldorder";
/*      */         } 
/*      */       } 
/*      */       
/*  213 */       recordSet.executeSql(str);
/*  214 */       while (recordSet.next()) {
/*  215 */         String str1 = Util.null2String(recordSet.getString("fieldid"));
/*  216 */         String str2 = Util.null2String(recordSet.getString("fieldname"));
/*  217 */         int i = Util.getIntValue(recordSet.getString("isdetail"), 0);
/*  218 */         if (this.isbill == 1) {
/*  219 */           str2 = SystemEnv.getHtmlLabelName(Util.getIntValue(str2), this.langurageid);
/*      */         }
/*  221 */         if (i == 1) {
/*  222 */           str2 = str2 + "(" + SystemEnv.getHtmlLabelName(17463, this.langurageid) + ")";
/*      */         }
/*  224 */         arrayOfArrayList[0].add(str1);
/*  225 */         arrayOfArrayList[1].add(str2);
/*  226 */         arrayOfArrayList[2].add(i + "");
/*      */       } 
/*      */     } else {
/*  229 */       String str1 = "select ismode,showdes,printdes from workflow_flownode where workflowid=" + this.workflowid + " and nodeid=" + paramInt;
/*  230 */       int i = 0;
/*  231 */       boolean bool = false;
/*  232 */       recordSet.executeSql(str1);
/*  233 */       if (recordSet.next()) {
/*  234 */         String str = Util.null2String(recordSet.getString("ismode"));
/*  235 */         int j = Util.getIntValue(Util.null2String(recordSet.getString("showdes")), 0);
/*  236 */         if (str.equals("1") && j != 1) {
/*  237 */           recordSet.executeSql("select id from workflow_nodemode where isprint='0' and workflowid=" + this.workflowid + " and nodeid=" + paramInt);
/*  238 */           if (recordSet.next()) {
/*  239 */             i = recordSet.getInt("id");
/*      */           } else {
/*  241 */             recordSet.executeSql("select id from workflow_formmode where isprint='0' and formid=" + this.formid + " and isbill='" + this.isbill + "'");
/*  242 */             if (recordSet.next()) {
/*  243 */               i = recordSet.getInt("id");
/*  244 */               bool = true;
/*      */             } 
/*      */           } 
/*      */         } 
/*      */       } 
/*      */       
/*  250 */       OrderProperties orderProperties = new OrderProperties();
/*  251 */       orderProperties.load(GCONST.getPropertyPath() + "workflow_allow_readonlyshowattr.properties");
/*  252 */       String str2 = "isedit='1'";
/*  253 */       if ("1".equals(orderProperties.get("allowreadonlyshowattr"))) {
/*  254 */         str2 = "isview='1'";
/*      */       }
/*  256 */       if (this.isbill == 1) {
/*      */         
/*  258 */         String str = "b.isedit='1'";
/*  259 */         if ("1".equals(orderProperties.get("allowreadonlyshowattr"))) {
/*  260 */           str = " (b.isedit='1' or (b.isview='1' and a.viewtype=0))";
/*      */         }
/*  262 */         if (i > 0) {
/*  263 */           if (bool == true) {
/*  264 */             str1 = "select a.id as fieldid,a.fieldlabel as fieldname,a.viewtype as isdetail from workflow_billfield a,workflow_modeview b where a.id=b.fieldid a.billid=b.formid and b.isbill=1 and a.fieldhtmltype='5' and a.type<>2 and " + str + " and b.nodeid=0 and a.billid=" + this.formid + " order by a.viewtype,a.dsporder";
/*      */           } else {
/*      */             
/*  267 */             str1 = "select a.id as fieldid,a.fieldlabel as fieldname,a.viewtype as isdetail from workflow_billfield a,workflow_modeview b where a.id=b.fieldid and b.formid=a.billid and b.isbill=1 and a.fieldhtmltype='5' and a.type<>2 and " + str + " and b.nodeid=" + paramInt + " and a.billid=" + this.formid + " order by a.viewtype,a.dsporder";
/*      */           } 
/*      */         } else {
/*      */           
/*  271 */           str1 = "select a.id as fieldid,a.fieldlabel as fieldname,a.viewtype as isdetail from workflow_billfield a,workflow_nodeform b where a.id=b.fieldid and a.fieldhtmltype='5' and a.type<>2 and " + str + " and b.nodeid=" + paramInt + " and a.billid=" + this.formid + " order by a.viewtype,a.dsporder";
/*      */         } 
/*      */       } else {
/*      */         
/*  275 */         if (i > 0) {
/*  276 */           if (bool == true) {
/*  277 */             str1 = "select a.fieldid,e.fieldlable as fieldname,a.isdetail,a.fieldorder from workflow_formfield a,workflow_modeview b,workflow_formdict d,workflow_fieldlable e where a.fieldid=b.fieldid and d.id=a.fieldid and a.fieldid=e.fieldid and a.formid=e.formid and (a.isdetail is null or a.isdetail!='1') and b.isbill=0  and d.fieldhtmltype='5' and d.type<>2 and a.formid=b.formid and b." + str2 + " and e.langurageid=" + this.langurageid + " and b.nodeid=0 and a.formid=" + this.formid + " union  select a.fieldid,e.fieldlable as fieldname,a.isdetail,a.fieldorder from workflow_formfield a,workflow_modeview b,workflow_formdictdetail d,workflow_fieldlable e where a.fieldid=b.fieldid and d.id=a.fieldid and a.fieldid=e.fieldid and a.formid=e.formid and a.isdetail='1' and b.isbill=0  and d.fieldhtmltype='5' and d.type<>2 and a.formid=b.formid and b.isedit='1' and e.langurageid=" + this.langurageid + " and b.nodeid=0 and a.formid=" + this.formid;
/*      */ 
/*      */           
/*      */           }
/*      */           else {
/*      */ 
/*      */             
/*  284 */             str1 = "select a.fieldid,e.fieldlable as fieldname,a.isdetail,a.fieldorder from workflow_formfield a,workflow_modeview b,workflow_formdict d,workflow_fieldlable e where a.fieldid=b.fieldid and d.id=a.fieldid and a.fieldid=e.fieldid and a.formid=e.formid and (a.isdetail is null or a.isdetail!='1') and b.isbill=0  and d.fieldhtmltype='5' and d.type<>2 and a.formid=b.formid and b." + str2 + " and e.langurageid=" + this.langurageid + " and b.nodeid=" + paramInt + " and a.formid=" + this.formid + " union  select a.fieldid,e.fieldlable as fieldname,a.isdetail,a.fieldorder from workflow_formfield a,workflow_modeview b,workflow_formdictdetail d,workflow_fieldlable e where a.fieldid=b.fieldid and d.id=a.fieldid and a.fieldid=e.fieldid and a.formid=e.formid and a.isdetail='1' and b.isbill=0  and d.fieldhtmltype='5' and d.type<>2 and a.formid=b.formid and b.isedit='1' and e.langurageid=" + this.langurageid + " and b.nodeid=" + paramInt + " and a.formid=" + this.formid;
/*      */           
/*      */           }
/*      */ 
/*      */         
/*      */         }
/*      */         else {
/*      */           
/*  292 */           str1 = "select a.fieldid,e.fieldlable as fieldname,a.isdetail,a.fieldorder from workflow_formfield a,workflow_nodeform b,workflow_formdict d,workflow_fieldlable e where a.fieldid=b.fieldid and d.id=a.fieldid and a.fieldid=e.fieldid and a.formid=e.formid and (a.isdetail is null or a.isdetail!='1')  and d.fieldhtmltype='5' and d.type<>2 and b." + str2 + " and e.langurageid=" + this.langurageid + " and b.nodeid=" + paramInt + " and a.formid=" + this.formid + " union  select a.fieldid,e.fieldlable as fieldname,a.isdetail,a.fieldorder from workflow_formfield a,workflow_nodeform b,workflow_formdictdetail d,workflow_fieldlable e where a.fieldid=b.fieldid and d.id=a.fieldid and a.fieldid=e.fieldid and a.formid=e.formid and a.isdetail='1'  and d.fieldhtmltype='5' and d.type<>2 and b.isedit='1' and e.langurageid=" + this.langurageid + " and b.nodeid=" + paramInt + " and a.formid=" + this.formid;
/*      */         } 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */         
/*  299 */         if ("oracle".equals(recordSet.getDBType())) {
/*  300 */           str1 = str1 + " order by isdetail desc,fieldorder";
/*      */         } else {
/*  302 */           str1 = str1 + " order by isdetail,fieldorder";
/*      */         } 
/*      */       } 
/*      */       
/*  306 */       recordSet.executeSql(str1);
/*  307 */       while (recordSet.next()) {
/*  308 */         String str3 = Util.null2String(recordSet.getString("fieldid"));
/*  309 */         String str4 = Util.null2String(recordSet.getString("fieldname"));
/*  310 */         int j = Util.getIntValue(recordSet.getString("isdetail"), 0);
/*  311 */         if (this.isbill == 1) {
/*  312 */           str4 = SystemEnv.getHtmlLabelName(Util.getIntValue(str4), this.langurageid);
/*      */         }
/*  314 */         if (j == 1) {
/*  315 */           str4 = str4 + "(" + SystemEnv.getHtmlLabelName(17463, this.langurageid) + ")";
/*      */         }
/*  317 */         arrayOfArrayList[0].add(str3);
/*  318 */         arrayOfArrayList[1].add(str4);
/*  319 */         arrayOfArrayList[2].add(j + "");
/*      */       } 
/*      */     } 
/*  322 */     return arrayOfArrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public ArrayList[] getSelectFieldItem(int paramInt) {
/*  332 */     ArrayList[] arrayOfArrayList = new ArrayList[2];
/*  333 */     arrayOfArrayList[0] = new ArrayList();
/*  334 */     arrayOfArrayList[1] = new ArrayList();
/*  335 */     RecordSet recordSet = new RecordSet();
/*  336 */     String str = "select selectvalue,selectname from workflow_selectitem where fieldid=" + paramInt + " and isbill=" + this.isbill + " and (cancel<>'1' or cancel is null) order by listorder,selectvalue";
/*  337 */     recordSet.executeSql(str);
/*  338 */     while (recordSet.next()) {
/*  339 */       arrayOfArrayList[0].add(recordSet.getString("selectvalue"));
/*  340 */       arrayOfArrayList[1].add(Util.toHtmlMode(recordSet.getString("selectname")));
/*      */     } 
/*  342 */     return arrayOfArrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public ArrayList[] getFieldsByView(int paramInt) {
/*  352 */     ArrayList[] arrayOfArrayList = new ArrayList[3];
/*  353 */     arrayOfArrayList[0] = new ArrayList();
/*  354 */     arrayOfArrayList[1] = new ArrayList();
/*  355 */     arrayOfArrayList[2] = new ArrayList();
/*  356 */     RecordSet recordSet = new RecordSet();
/*  357 */     String str1 = "";
/*  358 */     String str2 = "select ismode,showdes,printdes from workflow_flownode where workflowid=" + this.workflowid + " and nodeid=" + paramInt;
/*  359 */     int i = 0;
/*  360 */     boolean bool = false;
/*  361 */     recordSet.executeSql(str2);
/*  362 */     if (recordSet.next()) {
/*  363 */       String str = Util.null2String(recordSet.getString("ismode"));
/*  364 */       int j = Util.getIntValue(Util.null2String(recordSet.getString("showdes")), 0);
/*  365 */       if (str.equals("1") && j != 1) {
/*  366 */         recordSet.executeSql("select id from workflow_nodemode where isprint='0' and workflowid=" + this.workflowid + " and nodeid=" + paramInt);
/*  367 */         if (recordSet.next()) {
/*  368 */           i = recordSet.getInt("id");
/*      */         } else {
/*  370 */           recordSet.executeSql("select id from workflow_formmode where isprint='0' and formid=" + this.formid + " and isbill='" + this.isbill + "'");
/*  371 */           if (recordSet.next()) {
/*  372 */             i = recordSet.getInt("id");
/*  373 */             bool = true;
/*      */           } 
/*      */         } 
/*      */       } 
/*      */     } 
/*  378 */     if (this.isbill == 1) {
/*  379 */       if (i > 0) {
/*  380 */         if (bool == true) {
/*  381 */           str2 = "select a.id as fieldid,a.fieldlabel as fieldname,a.viewtype as isdetail from workflow_billfield a,workflow_modeview b where a.id=b.fieldid a.billid=b.formid and b.isbill=1 and a.fieldhtmltype!='6' and b.isview='1' and b.nodeid=0 and a.billid=" + this.formid;
/*      */         } else {
/*      */           
/*  384 */           str2 = "select a.id as fieldid,a.fieldlabel as fieldname,a.viewtype as isdetail from workflow_billfield a,workflow_modeview b where a.id=b.fieldid and b.formid=a.billid and b.isbill=1 and a.fieldhtmltype!='6' and b.isview='1' and b.nodeid=" + paramInt + " and a.billid=" + this.formid;
/*      */         } 
/*      */       } else {
/*      */         
/*  388 */         str2 = "select a.id as fieldid,a.fieldlabel as fieldname,a.viewtype as isdetail from workflow_billfield a,workflow_nodeform b where a.id=b.fieldid and b.isview='1' and a.fieldhtmltype!='6' and b.nodeid=" + paramInt + " and a.billid=" + this.formid;
/*      */       } 
/*      */       
/*  391 */       if (this.viewtype == null || this.viewtype.equals("")) {
/*  392 */         str2 = str2 + " order by a.viewtype,a.dsporder";
/*  393 */       } else if (this.viewtype.equals("0")) {
/*  394 */         str2 = str2 + " and (a.viewtype is null or a.viewtype!='1') order by a.viewtype,a.dsporder";
/*      */       } else {
/*  396 */         recordSet.executeSql("select detailtable from workflow_billfield where billid=" + this.formid + " and id=" + this.fieldid);
/*  397 */         if (recordSet.next()) {
/*  398 */           str1 = Util.null2String(recordSet.getString("detailtable"));
/*      */         }
/*  400 */         if (str1.equals("")) {
/*  401 */           str2 = str2 + " and a.viewtype='1' and (a.detailtable is null or a.detailtable='') order by a.viewtype,a.dsporder";
/*      */         } else {
/*  403 */           str2 = str2 + " and a.viewtype='1' and a.detailtable='" + str1 + "' order by a.viewtype,a.dsporder";
/*      */         }
/*      */       
/*      */       } 
/*  407 */     } else if (i > 0) {
/*  408 */       if (bool == true) {
/*  409 */         if (this.viewtype == null || this.viewtype.equals("")) {
/*  410 */           str2 = "select a.fieldid,e.fieldlable as fieldname,a.isdetail,a.fieldorder from workflow_formfield a,workflow_modeview b,workflow_formdict d,workflow_fieldlable e where a.fieldid=b.fieldid and d.id=a.fieldid and a.fieldid=e.fieldid and a.formid=e.formid and (a.isdetail is null or a.isdetail!='1') and b.isbill=0  and d.fieldhtmltype!='6' and b.isview='1' and a.formid=b.formid and e.langurageid=" + this.langurageid + " and b.nodeid=0 and a.formid=" + this.formid + " union  select a.fieldid,e.fieldlable as fieldname,a.isdetail,a.fieldorder from workflow_formfield a,workflow_modeview b,workflow_formdictdetail d,workflow_fieldlable e where a.fieldid=b.fieldid and d.id=a.fieldid and a.fieldid=e.fieldid and a.formid=e.formid and a.isdetail='1' and b.isbill=0  and d.fieldhtmltype!='6' and b.isview='1' and a.formid=b.formid and e.langurageid=" + this.langurageid + " and b.nodeid=0 and a.formid=" + this.formid;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */           
/*  416 */           if ("oracle".equals(recordSet.getDBType())) {
/*  417 */             str2 = str2 + " order by isdetail desc,fieldorder";
/*      */           } else {
/*  419 */             str2 = str2 + " order by isdetail,fieldorder";
/*      */           } 
/*  421 */         } else if (this.viewtype.equals("0")) {
/*  422 */           str2 = "select a.fieldid,e.fieldlable as fieldname,a.isdetail,a.fieldorder from workflow_formfield a,workflow_modeview b,workflow_formdict d,workflow_fieldlable e where a.fieldid=b.fieldid and d.id=a.fieldid and a.fieldid=e.fieldid and a.formid=e.formid and (a.isdetail is null or a.isdetail!='1') and b.isbill=0  and d.fieldhtmltype!='6' and b.isview='1' and a.formid=b.formid and e.langurageid=" + this.langurageid + " and b.nodeid=0 and a.formid=" + this.formid + " order by a.isdetail,a.fieldorder";
/*      */         }
/*      */         else {
/*      */           
/*  426 */           recordSet.executeSql("select groupid from workflow_formfield where isdetail='1' and formid=" + this.formid + " and fieldid=" + this.fieldid);
/*  427 */           if (recordSet.next()) {
/*  428 */             str1 = Util.null2String(recordSet.getString("groupid"));
/*      */           }
/*  430 */           if (str1.equals("")) {
/*  431 */             str2 = " select a.fieldid,e.fieldlable as fieldname,a.isdetail,a.fieldorder from workflow_formfield a,workflow_modeview b,workflow_formdictdetail d,workflow_fieldlable e where a.fieldid=b.fieldid and d.id=a.fieldid and a.fieldid=e.fieldid and a.formid=e.formid and a.isdetail='1' and b.isbill=0  and d.fieldhtmltype!='6' and b.isview='1' and a.formid=b.formid and e.langurageid=" + this.langurageid + " and b.nodeid=0 and a.formid=" + this.formid + " and (a.groupid is null or a.groupid='') order by a.isdetail,a.fieldorder";
/*      */           }
/*      */           else {
/*      */             
/*  435 */             str2 = " select a.fieldid,e.fieldlable as fieldname,a.isdetail,a.fieldorder from workflow_formfield a,workflow_modeview b,workflow_formdictdetail d,workflow_fieldlable e where a.fieldid=b.fieldid and d.id=a.fieldid and a.fieldid=e.fieldid and a.formid=e.formid and a.isdetail='1' and b.isbill=0  and d.fieldhtmltype!='6' and b.isview='1' and a.formid=b.formid and e.langurageid=" + this.langurageid + " and b.nodeid=0 and a.formid=" + this.formid + " and a.groupid=" + str1 + " order by a.isdetail,a.fieldorder";
/*      */           }
/*      */         
/*      */         }
/*      */       
/*      */       }
/*  441 */       else if (this.viewtype == null || this.viewtype.equals("")) {
/*  442 */         str2 = "select a.fieldid,e.fieldlable as fieldname,a.isdetail,a.fieldorder from workflow_formfield a,workflow_modeview b,workflow_formdict d,workflow_fieldlable e where a.fieldid=b.fieldid and d.id=a.fieldid and a.fieldid=e.fieldid and a.formid=e.formid and (a.isdetail is null or a.isdetail!='1') and b.isbill=0  and d.fieldhtmltype!='6' and b.isview='1' and a.formid=b.formid and e.langurageid=" + this.langurageid + " and b.nodeid=" + paramInt + " and a.formid=" + this.formid + " union  select a.fieldid,e.fieldlable as fieldname,a.isdetail,a.fieldorder from workflow_formfield a,workflow_modeview b,workflow_formdictdetail d,workflow_fieldlable e where a.fieldid=b.fieldid and d.id=a.fieldid and a.fieldid=e.fieldid and a.formid=e.formid and a.isdetail='1' and b.isbill=0  and d.fieldhtmltype!='6' and b.isview='1' and a.formid=b.formid and e.langurageid=" + this.langurageid + " and b.nodeid=" + paramInt + " and a.formid=" + this.formid;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */         
/*  448 */         if ("oracle".equals(recordSet.getDBType())) {
/*  449 */           str2 = str2 + " order by isdetail desc,fieldorder";
/*      */         } else {
/*  451 */           str2 = str2 + " order by isdetail,fieldorder";
/*      */         } 
/*  453 */       } else if (this.viewtype.equals("0")) {
/*  454 */         str2 = "select a.fieldid,e.fieldlable as fieldname,a.isdetail,a.fieldorder from workflow_formfield a,workflow_modeview b,workflow_formdict d,workflow_fieldlable e where a.fieldid=b.fieldid and d.id=a.fieldid and a.fieldid=e.fieldid and a.formid=e.formid and (a.isdetail is null or a.isdetail!='1') and b.isbill=0  and d.fieldhtmltype!='6' and b.isview='1' and a.formid=b.formid and e.langurageid=" + this.langurageid + " and b.nodeid=" + paramInt + " and a.formid=" + this.formid + " order by a.isdetail,a.fieldorder";
/*      */       }
/*      */       else {
/*      */         
/*  458 */         recordSet.executeSql("select groupid from workflow_formfield where isdetail='1' and formid=" + this.formid + " and fieldid=" + this.fieldid);
/*  459 */         if (recordSet.next()) {
/*  460 */           str1 = Util.null2String(recordSet.getString("groupid"));
/*      */         }
/*  462 */         if (str1.equals("")) {
/*  463 */           str2 = " select a.fieldid,e.fieldlable as fieldname,a.isdetail,a.fieldorder from workflow_formfield a,workflow_modeview b,workflow_formdictdetail d,workflow_fieldlable e where a.fieldid=b.fieldid and d.id=a.fieldid and a.fieldid=e.fieldid and a.formid=e.formid and a.isdetail='1' and b.isbill=0  and d.fieldhtmltype!='6' and b.isview='1' and a.formid=b.formid and e.langurageid=" + this.langurageid + " and b.nodeid=" + paramInt + " and a.formid=" + this.formid + " and (a.groupid is null or a.groupid='') order by a.isdetail,a.fieldorder";
/*      */         }
/*      */         else {
/*      */           
/*  467 */           str2 = " select a.fieldid,e.fieldlable as fieldname,a.isdetail,a.fieldorder from workflow_formfield a,workflow_modeview b,workflow_formdictdetail d,workflow_fieldlable e where a.fieldid=b.fieldid and d.id=a.fieldid and a.fieldid=e.fieldid and a.formid=e.formid and a.isdetail='1' and b.isbill=0  and d.fieldhtmltype!='6' and b.isview='1' and a.formid=b.formid and e.langurageid=" + this.langurageid + " and b.nodeid=" + paramInt + " and a.formid=" + this.formid + " and a.groupid=" + str1 + " order by a.isdetail,a.fieldorder";
/*      */         
/*      */         }
/*      */       
/*      */       }
/*      */     
/*      */     }
/*  474 */     else if (this.viewtype == null || this.viewtype.equals("")) {
/*  475 */       str2 = "select a.fieldid,e.fieldlable as fieldname,a.isdetail,a.fieldorder from workflow_formfield a,workflow_nodeform b,workflow_formdict d,workflow_fieldlable e where a.fieldid=b.fieldid and d.id=a.fieldid and a.fieldid=e.fieldid and a.formid=e.formid and (a.isdetail is null or a.isdetail!='1')  and d.fieldhtmltype!='6' and b.isview='1' and e.langurageid=" + this.langurageid + " and b.nodeid=" + paramInt + " and a.formid=" + this.formid + " union  select a.fieldid,e.fieldlable as fieldname,a.isdetail,a.fieldorder from workflow_formfield a,workflow_nodeform b,workflow_formdictdetail d,workflow_fieldlable e where a.fieldid=b.fieldid and d.id=a.fieldid and a.fieldid=e.fieldid and a.formid=e.formid and a.isdetail='1'  and d.fieldhtmltype!='6' and b.isview='1' and e.langurageid=" + this.langurageid + " and b.nodeid=" + paramInt + " and a.formid=" + this.formid;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*  481 */       if ("oracle".equals(recordSet.getDBType())) {
/*  482 */         str2 = str2 + " order by isdetail desc,fieldorder";
/*      */       } else {
/*  484 */         str2 = str2 + " order by isdetail,fieldorder";
/*      */       } 
/*  486 */     } else if (this.viewtype.equals("0")) {
/*  487 */       str2 = "select a.fieldid,e.fieldlable as fieldname,a.isdetail,a.fieldorder from workflow_formfield a,workflow_nodeform b,workflow_formdict d,workflow_fieldlable e where a.fieldid=b.fieldid and d.id=a.fieldid and a.fieldid=e.fieldid and a.formid=e.formid and (a.isdetail is null or a.isdetail!='1')  and d.fieldhtmltype!='6' and b.isview='1' and e.langurageid=" + this.langurageid + " and b.nodeid=" + paramInt + " and a.formid=" + this.formid + " order by a.isdetail,a.fieldorder";
/*      */     }
/*      */     else {
/*      */       
/*  491 */       recordSet.executeSql("select groupid from workflow_formfield where isdetail='1' and formid=" + this.formid + " and fieldid=" + this.fieldid);
/*  492 */       if (recordSet.next()) {
/*  493 */         str1 = Util.null2String(recordSet.getString("groupid"));
/*      */       }
/*  495 */       if (str1.equals("")) {
/*  496 */         str2 = " select a.fieldid,e.fieldlable as fieldname,a.isdetail,a.fieldorder from workflow_formfield a,workflow_nodeform b,workflow_formdictdetail d,workflow_fieldlable e where a.fieldid=b.fieldid and d.id=a.fieldid and a.fieldid=e.fieldid and a.formid=e.formid and a.isdetail='1'  and d.fieldhtmltype!='6' and b.isview='1' and e.langurageid=" + this.langurageid + " and b.nodeid=" + paramInt + " and a.formid=" + this.formid + "and (a.groupid is null or a.groupid='')  order by a.isdetail,a.fieldorder";
/*      */       }
/*      */       else {
/*      */         
/*  500 */         str2 = " select a.fieldid,e.fieldlable as fieldname,a.isdetail,a.fieldorder from workflow_formfield a,workflow_nodeform b,workflow_formdictdetail d,workflow_fieldlable e where a.fieldid=b.fieldid and d.id=a.fieldid and a.fieldid=e.fieldid and a.formid=e.formid and a.isdetail='1'  and d.fieldhtmltype!='6' and b.isview='1' and e.langurageid=" + this.langurageid + " and b.nodeid=" + paramInt + " and a.formid=" + this.formid + " and a.groupid=" + str1 + " order by a.isdetail,a.fieldorder";
/*      */       } 
/*      */     } 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  508 */     recordSet.executeSql(str2);
/*  509 */     while (recordSet.next()) {
/*  510 */       String str3 = Util.null2String(recordSet.getString("fieldid"));
/*  511 */       String str4 = Util.null2String(recordSet.getString("fieldname"));
/*  512 */       int j = Util.getIntValue(recordSet.getString("isdetail"), 0);
/*  513 */       if (this.isbill == 1) {
/*  514 */         str4 = SystemEnv.getHtmlLabelName(Util.getIntValue(str4), this.langurageid);
/*      */       }
/*  516 */       if (j == 1) {
/*  517 */         str4 = str4 + "(" + SystemEnv.getHtmlLabelName(17463, this.langurageid) + ")";
/*      */       }
/*  519 */       if (this.searchfieldname != null && !this.searchfieldname.equals("")) {
/*  520 */         if (str4.indexOf(this.searchfieldname) != -1) {
/*  521 */           arrayOfArrayList[0].add(str3);
/*  522 */           arrayOfArrayList[1].add(str4);
/*  523 */           arrayOfArrayList[2].add(j + "");
/*      */         }  continue;
/*      */       } 
/*  526 */       arrayOfArrayList[0].add(str3);
/*  527 */       arrayOfArrayList[1].add(str4);
/*  528 */       arrayOfArrayList[2].add(j + "");
/*      */     } 
/*      */     
/*  531 */     return arrayOfArrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public ArrayList[] getFieldsByEdit(int paramInt) {
/*  541 */     ArrayList[] arrayOfArrayList = new ArrayList[3];
/*  542 */     arrayOfArrayList[0] = new ArrayList();
/*  543 */     arrayOfArrayList[1] = new ArrayList();
/*  544 */     arrayOfArrayList[2] = new ArrayList();
/*  545 */     RecordSet recordSet = new RecordSet();
/*  546 */     String str = "";
/*      */     
/*  548 */     if (paramInt == -1) {
/*  549 */       String str1 = "";
/*  550 */       if (this.isbill == 1) {
/*  551 */         str1 = " select a.id as fieldid,a.fieldlabel as fieldname,a.viewtype as isdetail from workflow_billfield a  where a.fieldhtmltype!='7' and a.billid=" + this.formid;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */         
/*  557 */         str1 = str1 + " and (a.viewtype is null or a.viewtype!='1') order by a.viewtype,a.dsporder";
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*      */       }
/*      */       else {
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */         
/*  584 */         str1 = "select a.fieldid,e.fieldlable as fieldname,a.isdetail,a.fieldorder from workflow_formfield a,workflow_formdict d,workflow_fieldlable e where d.id=a.fieldid and a.fieldid=e.fieldid and a.formid=e.formid and (a.isdetail is null or a.isdetail!='1')  and d.fieldhtmltype!='7' and e.langurageid=" + this.langurageid + " and a.formid=" + this.formid + " order by a.isdetail,a.fieldorder";
/*      */       } 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*  604 */       recordSet.executeSql(str1);
/*  605 */       while (recordSet.next()) {
/*  606 */         String str2 = Util.null2String(recordSet.getString("fieldid"));
/*  607 */         String str3 = Util.null2String(recordSet.getString("fieldname"));
/*  608 */         int i = Util.getIntValue(recordSet.getString("isdetail"), 0);
/*  609 */         if (this.isbill == 1) {
/*  610 */           str3 = SystemEnv.getHtmlLabelName(Util.getIntValue(str3), this.langurageid);
/*      */         }
/*  612 */         if (i == 1) {
/*  613 */           str3 = str3 + "(" + SystemEnv.getHtmlLabelName(17463, this.langurageid) + ")";
/*      */         }
/*  615 */         if (this.searchfieldname != null && !this.searchfieldname.equals("")) {
/*  616 */           if (str3.indexOf(this.searchfieldname) != -1) {
/*  617 */             arrayOfArrayList[0].add(str2);
/*  618 */             arrayOfArrayList[1].add(str3);
/*  619 */             arrayOfArrayList[2].add(i + "");
/*      */           }  continue;
/*      */         } 
/*  622 */         arrayOfArrayList[0].add(str2);
/*  623 */         arrayOfArrayList[1].add(str3);
/*  624 */         arrayOfArrayList[2].add(i + "");
/*      */       } 
/*      */     } else {
/*      */       
/*  628 */       String str1 = "select ismode,showdes,printdes from workflow_flownode where workflowid=" + this.workflowid + " and nodeid=" + paramInt;
/*  629 */       int i = 0;
/*  630 */       boolean bool = false;
/*  631 */       recordSet.executeSql(str1);
/*  632 */       if (recordSet.next()) {
/*  633 */         String str2 = Util.null2String(recordSet.getString("ismode"));
/*  634 */         int j = Util.getIntValue(Util.null2String(recordSet.getString("showdes")), 0);
/*  635 */         if (str2.equals("1") && j != 1) {
/*  636 */           recordSet.executeSql("select id from workflow_nodemode where isprint='0' and workflowid=" + this.workflowid + " and nodeid=" + paramInt);
/*  637 */           if (recordSet.next()) {
/*  638 */             i = recordSet.getInt("id");
/*      */           } else {
/*  640 */             recordSet.executeSql("select id from workflow_formmode where isprint='0' and formid=" + this.formid + " and isbill='" + this.isbill + "'");
/*  641 */             if (recordSet.next()) {
/*  642 */               i = recordSet.getInt("id");
/*  643 */               bool = true;
/*      */             } 
/*      */           } 
/*      */         } 
/*      */       } 
/*  648 */       if (this.isbill == 1) {
/*  649 */         if (i > 0) {
/*  650 */           if (bool == true) {
/*  651 */             str1 = "select a.id as fieldid,a.fieldlabel as fieldname,a.viewtype as isdetail from workflow_billfield a,workflow_modeview b where a.id=b.fieldid a.billid=b.formid and b.isbill=1 and b.isedit='1' and a.fieldhtmltype!='7' and b.nodeid=0 and a.billid=" + this.formid;
/*      */           } else {
/*      */             
/*  654 */             str1 = "select a.id as fieldid,a.fieldlabel as fieldname,a.viewtype as isdetail from workflow_billfield a,workflow_modeview b where a.id=b.fieldid and b.formid=a.billid and b.isbill=1 and b.isedit='1' and a.fieldhtmltype!='7' and b.nodeid=" + paramInt + " and a.billid=" + this.formid;
/*      */           } 
/*      */         } else {
/*      */           
/*  658 */           str1 = "select a.id as fieldid,a.fieldlabel as fieldname,a.viewtype as isdetail from workflow_billfield a,workflow_nodeform b where a.id=b.fieldid and b.isedit='1' and a.fieldhtmltype!='7' and b.nodeid=" + paramInt + " and a.billid=" + this.formid;
/*      */         } 
/*      */         
/*  661 */         if (this.viewtype == null || this.viewtype.equals("")) {
/*  662 */           str1 = str1 + " order by a.viewtype,a.dsporder";
/*  663 */         } else if (this.viewtype.equals("0")) {
/*  664 */           str1 = str1 + " and (a.viewtype is null or a.viewtype!='1') order by a.viewtype,a.dsporder";
/*      */         } else {
/*  666 */           recordSet.executeSql("select detailtable from workflow_billfield where billid=" + this.formid + " and id=" + this.fieldid);
/*  667 */           if (recordSet.next()) {
/*  668 */             str = Util.null2String(recordSet.getString("detailtable"));
/*      */           }
/*  670 */           if (str.equals("")) {
/*  671 */             str1 = str1 + " and a.viewtype='1' and (a.detailtable is null or a.detailtable='') order by a.viewtype,a.dsporder";
/*      */           } else {
/*  673 */             str1 = str1 + " and a.viewtype='1' and a.detailtable='" + str + "' order by a.viewtype,a.dsporder";
/*      */           }
/*      */         
/*      */         } 
/*  677 */       } else if (i > 0) {
/*  678 */         if (bool == true) {
/*  679 */           if (this.viewtype == null || this.viewtype.equals("")) {
/*  680 */             str1 = "select a.fieldid,e.fieldlable as fieldname,a.isdetail,a.fieldorder from workflow_formfield a,workflow_modeview b,workflow_formdict d,workflow_fieldlable e where a.fieldid=b.fieldid and d.id=a.fieldid and a.fieldid=e.fieldid and a.formid=e.formid and (a.isdetail is null or a.isdetail!='1') and b.isbill=0  and d.fieldhtmltype!='7' and b.isedit='1' and a.formid=b.formid and e.langurageid=" + this.langurageid + " and b.nodeid=0 and a.formid=" + this.formid + " union  select a.fieldid,e.fieldlable as fieldname,a.isdetail,a.fieldorder from workflow_formfield a,workflow_modeview b,workflow_formdictdetail d,workflow_fieldlable e where a.fieldid=b.fieldid and d.id=a.fieldid and a.fieldid=e.fieldid and a.formid=e.formid and a.isdetail='1' and b.isbill=0  and d.fieldhtmltype!='" + '\007' + "' and b.isedit='1' and a.formid=b.formid and e.langurageid=" + this.langurageid + " and b.nodeid=0 and a.formid=" + this.formid;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */             
/*  686 */             if ("oracle".equals(recordSet.getDBType())) {
/*  687 */               str1 = str1 + " order by isdetail desc,fieldorder";
/*      */             } else {
/*  689 */               str1 = str1 + " order by isdetail,fieldorder";
/*      */             } 
/*  691 */           } else if (this.viewtype.equals("0")) {
/*  692 */             str1 = "select a.fieldid,e.fieldlable as fieldname,a.isdetail,a.fieldorder from workflow_formfield a,workflow_modeview b,workflow_formdict d,workflow_fieldlable e where a.fieldid=b.fieldid and d.id=a.fieldid and a.fieldid=e.fieldid and a.formid=e.formid and (a.isdetail is null or a.isdetail!='1') and b.isbill=0  and d.fieldhtmltype!='7' and b.isedit='1' and a.formid=b.formid and e.langurageid=" + this.langurageid + " and b.nodeid=0 and a.formid=" + this.formid + " order by a.isdetail,a.fieldorder";
/*      */           }
/*      */           else {
/*      */             
/*  696 */             recordSet.executeSql("select groupid from workflow_formfield where isdetail='1' and formid=" + this.formid + " and fieldid=" + this.fieldid);
/*  697 */             if (recordSet.next()) {
/*  698 */               str = Util.null2String(recordSet.getString("groupid"));
/*      */             }
/*  700 */             if (str.equals("")) {
/*  701 */               str1 = " select a.fieldid,e.fieldlable as fieldname,a.isdetail,a.fieldorder from workflow_formfield a,workflow_modeview b,workflow_formdictdetail d,workflow_fieldlable e where a.fieldid=b.fieldid and d.id=a.fieldid and a.fieldid=e.fieldid and a.formid=e.formid and a.isdetail='1' and b.isbill=0  and d.fieldhtmltype!='7' and b.isedit='1' and a.formid=b.formid and e.langurageid=" + this.langurageid + " and b.nodeid=0 and a.formid=" + this.formid + " and (a.groupid is null or a.groupid='') order by a.isdetail,a.fieldorder";
/*      */             }
/*      */             else {
/*      */               
/*  705 */               str1 = " select a.fieldid,e.fieldlable as fieldname,a.isdetail,a.fieldorder from workflow_formfield a,workflow_modeview b,workflow_formdictdetail d,workflow_fieldlable e where a.fieldid=b.fieldid and d.id=a.fieldid and a.fieldid=e.fieldid and a.formid=e.formid and a.isdetail='1' and b.isbill=0  and d.fieldhtmltype!='7' and b.isedit='1' and a.formid=b.formid and e.langurageid=" + this.langurageid + " and b.nodeid=0 and a.formid=" + this.formid + " and a.groupid=" + str + " order by a.isdetail,a.fieldorder";
/*      */             }
/*      */           
/*      */           }
/*      */         
/*      */         }
/*  711 */         else if (this.viewtype == null || this.viewtype.equals("")) {
/*  712 */           str1 = "select a.fieldid,e.fieldlable as fieldname,a.isdetail,a.fieldorder from workflow_formfield a,workflow_modeview b,workflow_formdict d,workflow_fieldlable e where a.fieldid=b.fieldid and d.id=a.fieldid and a.fieldid=e.fieldid and a.formid=e.formid and (a.isdetail is null or a.isdetail!='1') and b.isbill=0  and d.fieldhtmltype!='7' and b.isedit='1' and a.formid=b.formid and e.langurageid=" + this.langurageid + " and b.nodeid=" + paramInt + " and a.formid=" + this.formid + " union  select a.fieldid,e.fieldlable as fieldname,a.isdetail,a.fieldorder from workflow_formfield a,workflow_modeview b,workflow_formdictdetail d,workflow_fieldlable e where a.fieldid=b.fieldid and d.id=a.fieldid and a.fieldid=e.fieldid and a.formid=e.formid and a.isdetail='1' and b.isbill=0  and d.fieldhtmltype!='" + '\007' + "' and b.isedit='1' and a.formid=b.formid and e.langurageid=" + this.langurageid + " and b.nodeid=" + paramInt + " and a.formid=" + this.formid;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */           
/*  718 */           if ("oracle".equals(recordSet.getDBType())) {
/*  719 */             str1 = str1 + " order by isdetail desc,fieldorder";
/*      */           } else {
/*  721 */             str1 = str1 + " order by isdetail,fieldorder";
/*      */           } 
/*  723 */         } else if (this.viewtype.equals("0")) {
/*  724 */           str1 = "select a.fieldid,e.fieldlable as fieldname,a.isdetail,a.fieldorder from workflow_formfield a,workflow_modeview b,workflow_formdict d,workflow_fieldlable e where a.fieldid=b.fieldid and d.id=a.fieldid and a.fieldid=e.fieldid and a.formid=e.formid and (a.isdetail is null or a.isdetail!='1') and b.isbill=0  and d.fieldhtmltype!='7' and b.isedit='1' and a.formid=b.formid and e.langurageid=" + this.langurageid + " and b.nodeid=" + paramInt + " and a.formid=" + this.formid + " order by a.isdetail,a.fieldorder";
/*      */         }
/*      */         else {
/*      */           
/*  728 */           recordSet.executeSql("select groupid from workflow_formfield where isdetail='1' and formid=" + this.formid + " and fieldid=" + this.fieldid);
/*  729 */           if (recordSet.next()) {
/*  730 */             str = Util.null2String(recordSet.getString("groupid"));
/*      */           }
/*  732 */           if (str.equals("")) {
/*  733 */             str1 = " select a.fieldid,e.fieldlable as fieldname,a.isdetail,a.fieldorder from workflow_formfield a,workflow_modeview b,workflow_formdictdetail d,workflow_fieldlable e where a.fieldid=b.fieldid and d.id=a.fieldid and a.fieldid=e.fieldid and a.formid=e.formid and a.isdetail='1' and b.isbill=0  and d.fieldhtmltype!='7' and b.isedit='1' and a.formid=b.formid and e.langurageid=" + this.langurageid + " and b.nodeid=" + paramInt + " and a.formid=" + this.formid + " and (a.groupid is null or a.groupid='') order by a.isdetail,a.fieldorder";
/*      */           }
/*      */           else {
/*      */             
/*  737 */             str1 = " select a.fieldid,e.fieldlable as fieldname,a.isdetail,a.fieldorder from workflow_formfield a,workflow_modeview b,workflow_formdictdetail d,workflow_fieldlable e where a.fieldid=b.fieldid and d.id=a.fieldid and a.fieldid=e.fieldid and a.formid=e.formid and a.isdetail='1' and b.isbill=0  and d.fieldhtmltype!='7' and b.isedit='1' and a.formid=b.formid and e.langurageid=" + this.langurageid + " and b.nodeid=" + paramInt + " and a.formid=" + this.formid + " and a.groupid=" + str + " order by a.isdetail,a.fieldorder";
/*      */           
/*      */           }
/*      */         
/*      */         }
/*      */       
/*      */       }
/*  744 */       else if (this.viewtype == null || this.viewtype.equals("")) {
/*  745 */         str1 = "select a.fieldid,e.fieldlable as fieldname,a.isdetail,a.fieldorder from workflow_formfield a,workflow_nodeform b,workflow_formdict d,workflow_fieldlable e where a.fieldid=b.fieldid and d.id=a.fieldid and a.fieldid=e.fieldid and a.formid=e.formid and (a.isdetail is null or a.isdetail!='1')  and d.fieldhtmltype!='7' and b.isedit='1' and e.langurageid=" + this.langurageid + " and b.nodeid=" + paramInt + " and a.formid=" + this.formid + " union  select a.fieldid,e.fieldlable as fieldname,a.isdetail,a.fieldorder from workflow_formfield a,workflow_nodeform b,workflow_formdictdetail d,workflow_fieldlable e where a.fieldid=b.fieldid and d.id=a.fieldid and a.fieldid=e.fieldid and a.formid=e.formid and a.isdetail='1'  and d.fieldhtmltype!='" + '\007' + "' and b.isedit='1' and e.langurageid=" + this.langurageid + " and b.nodeid=" + paramInt + " and a.formid=" + this.formid;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */         
/*  751 */         if ("oracle".equals(recordSet.getDBType())) {
/*  752 */           str1 = str1 + " order by isdetail desc,fieldorder";
/*      */         } else {
/*  754 */           str1 = str1 + " order by isdetail,fieldorder";
/*      */         } 
/*  756 */       } else if (this.viewtype.equals("0")) {
/*  757 */         str1 = "select a.fieldid,e.fieldlable as fieldname,a.isdetail,a.fieldorder from workflow_formfield a,workflow_nodeform b,workflow_formdict d,workflow_fieldlable e where a.fieldid=b.fieldid and d.id=a.fieldid and a.fieldid=e.fieldid and a.formid=e.formid and (a.isdetail is null or a.isdetail!='1')  and d.fieldhtmltype!='7' and b.isedit='1' and e.langurageid=" + this.langurageid + " and b.nodeid=" + paramInt + " and a.formid=" + this.formid + " order by a.isdetail,a.fieldorder";
/*      */       }
/*      */       else {
/*      */         
/*  761 */         recordSet.executeSql("select groupid from workflow_formfield where isdetail='1' and formid=" + this.formid + " and fieldid=" + this.fieldid);
/*  762 */         if (recordSet.next()) {
/*  763 */           str = Util.null2String(recordSet.getString("groupid"));
/*      */         }
/*  765 */         if (str.equals("")) {
/*  766 */           str1 = " select a.fieldid,e.fieldlable as fieldname,a.isdetail,a.fieldorder from workflow_formfield a,workflow_nodeform b,workflow_formdictdetail d,workflow_fieldlable e where a.fieldid=b.fieldid and d.id=a.fieldid and a.fieldid=e.fieldid and a.formid=e.formid and a.isdetail='1'  and d.fieldhtmltype!='7' and b.isedit='1' and e.langurageid=" + this.langurageid + " and b.nodeid=" + paramInt + " and a.formid=" + this.formid + "and (a.groupid is null or a.groupid='')  order by a.isdetail,a.fieldorder";
/*      */         }
/*      */         else {
/*      */           
/*  770 */           str1 = " select a.fieldid,e.fieldlable as fieldname,a.isdetail,a.fieldorder from workflow_formfield a,workflow_nodeform b,workflow_formdictdetail d,workflow_fieldlable e where a.fieldid=b.fieldid and d.id=a.fieldid and a.fieldid=e.fieldid and a.formid=e.formid and a.isdetail='1'  and d.fieldhtmltype!='7' and b.isedit='1' and e.langurageid=" + this.langurageid + " and b.nodeid=" + paramInt + " and a.formid=" + this.formid + " and a.groupid=" + str + " order by a.isdetail,a.fieldorder";
/*      */         } 
/*      */       } 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*  778 */       recordSet.executeSql(str1);
/*  779 */       while (recordSet.next()) {
/*  780 */         String str2 = Util.null2String(recordSet.getString("fieldid"));
/*  781 */         String str3 = Util.null2String(recordSet.getString("fieldname"));
/*  782 */         int j = Util.getIntValue(recordSet.getString("isdetail"), 0);
/*  783 */         if (this.isbill == 1) {
/*  784 */           str3 = SystemEnv.getHtmlLabelName(Util.getIntValue(str3), this.langurageid);
/*      */         }
/*  786 */         if (j == 1) {
/*  787 */           str3 = str3 + "(" + SystemEnv.getHtmlLabelName(17463, this.langurageid) + ")";
/*      */         }
/*  789 */         if (this.searchfieldname != null && !this.searchfieldname.equals("")) {
/*  790 */           if (str3.indexOf(this.searchfieldname) != -1) {
/*  791 */             arrayOfArrayList[0].add(str2);
/*  792 */             arrayOfArrayList[1].add(str3);
/*  793 */             arrayOfArrayList[2].add(j + "");
/*      */           }  continue;
/*      */         } 
/*  796 */         arrayOfArrayList[0].add(str2);
/*  797 */         arrayOfArrayList[1].add(str3);
/*  798 */         arrayOfArrayList[2].add(j + "");
/*      */       } 
/*      */     } 
/*      */     
/*  802 */     return arrayOfArrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getFieldnames(int paramInt) {
/*  812 */     String str1 = "";
/*  813 */     RecordSet recordSet = new RecordSet();
/*  814 */     String str2 = "";
/*  815 */     int i = 0;
/*  816 */     if (this.isbill == 1) {
/*  817 */       str2 = "select fieldlabel as fieldname,viewtype as isdetail from workflow_billfield where id=" + paramInt;
/*      */     } else {
/*  819 */       str2 = "select b.fieldlable as fieldname,a.isdetail from workflow_formfield a,workflow_fieldlable b where a.fieldid=b.fieldid and a.formid=b.formid and b.fieldid=" + paramInt + " and b.formid=" + this.formid;
/*      */     } 
/*  821 */     recordSet.executeSql(str2);
/*  822 */     if (recordSet.next()) {
/*  823 */       str1 = Util.null2String(recordSet.getString("fieldname"));
/*  824 */       i = Util.getIntValue(recordSet.getString("isdetail"), 0);
/*      */     } 
/*  826 */     if (this.isbill == 1) str1 = SystemEnv.getHtmlLabelName(Util.getIntValue(str1), this.langurageid); 
/*  827 */     if (i == 1) str1 = str1 + "(" + SystemEnv.getHtmlLabelName(17463, this.langurageid) + ")"; 
/*  828 */     return str1;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean DeleteLinkageBywfid(int paramInt) {
/*  838 */     boolean bool = false;
/*  839 */     RecordSet recordSet = new RecordSet();
/*  840 */     bool = recordSet.executeSql("delete from workflow_viewattrlinkage where workflowid=" + paramInt);
/*  841 */     return bool;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean InsertLinkage(HttpServletRequest paramHttpServletRequest, int paramInt) {
/*  852 */     boolean bool = true;
/*  853 */     RecordSet recordSet = new RecordSet();
/*  854 */     if (paramHttpServletRequest != null && paramInt > 0) {
/*  855 */       int i = Util.getIntValue(paramHttpServletRequest.getParameter("linkage_rownum"));
/*  856 */       int j = Util.getIntValue(paramHttpServletRequest.getParameter("linkage_indexnum"));
/*  857 */       String str = "";
/*  858 */       if (i > 0)
/*  859 */         for (byte b = 0; b < j; b++) {
/*  860 */           int k = Util.getIntValue(paramHttpServletRequest.getParameter("nodeid_" + b), 0);
/*  861 */           String str1 = Util.null2String(paramHttpServletRequest.getParameter("selectfieldid_" + b));
/*  862 */           String str2 = Util.null2String(paramHttpServletRequest.getParameter("selectfieldvalue_" + b));
/*  863 */           String str3 = Util.null2String(paramHttpServletRequest.getParameter("changefieldids_" + b));
/*  864 */           int m = Util.getIntValue(paramHttpServletRequest.getParameter("viewattr_" + b), 2);
/*  865 */           if (k > 0 && !str1.equals("") && !str2.equals("")) {
/*  866 */             str = "insert into workflow_viewattrlinkage(workflowid,nodeid,selectfieldid,selectfieldvalue,changefieldids,viewattr) values(" + paramInt + "," + k + ",'" + str1 + "','" + str2 + "','" + str3 + "','" + m + "')";
/*      */             
/*  868 */             boolean bool1 = recordSet.executeSql(str);
/*  869 */             if (bool) bool = bool1; 
/*  870 */           } else if (k == -1) {
/*  871 */             str = "insert into workflow_viewattrlinkage(workflowid,nodeid,selectfieldid,selectfieldvalue,changefieldids,viewattr) values(" + paramInt + ",-1,'" + str1 + "','" + str2 + "','" + str3 + "','" + m + "')";
/*      */             
/*  873 */             boolean bool1 = recordSet.executeSql(str);
/*  874 */             if (bool) bool = bool1;
/*      */           
/*      */           } 
/*      */         }  
/*      */     } else {
/*  879 */       bool = false;
/*      */     } 
/*  881 */     return bool;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean LinkageSave(HttpServletRequest paramHttpServletRequest, int paramInt) {
/*  892 */     boolean bool = DeleteLinkageBywfid(paramInt);
/*  893 */     if (bool) bool = InsertLinkage(paramHttpServletRequest, paramInt); 
/*  894 */     return bool;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public ArrayList getSelectField(int paramInt1, int paramInt2, int paramInt3) {
/*  906 */     ArrayList<String> arrayList = new ArrayList();
/*  907 */     RecordSet recordSet = new RecordSet();
/*  908 */     String str = "select distinct selectfieldid from workflow_viewattrlinkage where workflowid=" + paramInt1 + " and (nodeid=" + paramInt2 + " or nodeid = -1)";
/*  909 */     recordSet.executeSql(str);
/*  910 */     while (recordSet.next()) {
/*  911 */       String str1 = recordSet.getString("selectfieldid");
/*  912 */       if (paramInt3 == 1) {
/*  913 */         if (str1.indexOf("_1") > 0) arrayList.add(str1.substring(0, str1.indexOf("_")));  continue;
/*      */       } 
/*  915 */       if (str1.indexOf("_0") > 0) arrayList.add(str1.substring(0, str1.indexOf("_")));
/*      */     
/*      */     } 
/*  918 */     return arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public ArrayList getChangeField(int paramInt1, int paramInt2, int paramInt3) {
/*  930 */     ArrayList<String> arrayList = new ArrayList();
/*  931 */     RecordSet recordSet = new RecordSet();
/*  932 */     String str = "select distinct changefieldids from workflow_viewattrlinkage where workflowid=" + paramInt1 + " and (nodeid=" + paramInt2 + " or nodeid = -1)";
/*  933 */     recordSet.executeSql(str);
/*  934 */     while (recordSet.next()) {
/*  935 */       ArrayList<String> arrayList1 = Util.TokenizerString(recordSet.getString("changefieldids"), ",");
/*  936 */       for (byte b = 0; b < arrayList1.size(); b++) {
/*  937 */         String str1 = arrayList1.get(b);
/*  938 */         String str2 = str1.substring(0, str1.indexOf("_"));
/*  939 */         if (paramInt3 == 1)
/*  940 */         { if (str1.indexOf("_1") > 0 && arrayList.indexOf(str2) == -1) arrayList.add(str2);
/*      */            }
/*  942 */         else if (str1.indexOf("_0") > 0 && arrayList.indexOf(str2) == -1) { arrayList.add(str2); }
/*      */       
/*      */       } 
/*      */     } 
/*  946 */     return arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getChangeFieldByselectvalue(int paramInt1, int paramInt2, String paramString1, String paramString2) {
/*  959 */     String str = "";
/*  960 */     ArrayList<String> arrayList1 = Util.TokenizerString(paramString1, ",");
/*  961 */     ArrayList<String> arrayList2 = Util.TokenizerString(paramString2, ",");
/*  962 */     for (byte b = 0; b < arrayList1.size() && b < arrayList2.size(); b++) {
/*  963 */       String str1 = Util.null2String(arrayList1.get(b));
/*  964 */       String str2 = Util.null2String(arrayList2.get(b));
/*  965 */       if (!str.equals("")) { str = str + "+" + getChangeFieldByselectvalueSingle(paramInt1, paramInt2, str1, str2); }
/*  966 */       else { str = getChangeFieldByselectvalueSingle(paramInt1, paramInt2, str1, str2); }
/*      */     
/*  968 */     }  return str;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getChangeFieldByselectvalueSingle(int paramInt1, int paramInt2, String paramString1, String paramString2) {
/*  981 */     String str1 = "";
/*  982 */     String str2 = "";
/*  983 */     ArrayList<String> arrayList = new ArrayList();
/*  984 */     RecordSet recordSet = new RecordSet();
/*  985 */     if (paramString2 == null || paramString2.equals("")) paramString2 = " "; 
/*  986 */     String str3 = "select distinct changefieldids,viewattr from workflow_viewattrlinkage where workflowid=" + paramInt1 + " and (nodeid=" + paramInt2 + " or nodeid = -1) and selectfieldid='" + Util.null2String(paramString1) + "' and selectfieldvalue='" + Util.null2String(paramString2) + "' order by viewattr";
/*  987 */     recordSet.executeSql(str3);
/*  988 */     while (recordSet.next()) {
/*  989 */       String str5 = Util.null2String(recordSet.getString("changefieldids"));
/*  990 */       String str6 = Util.null2String(recordSet.getString("viewattr"));
/*  991 */       str2 = str2 + "," + str5;
/*  992 */       if (str1.equals("")) {
/*  993 */         str1 = str5 + "$" + str6; continue;
/*      */       } 
/*  995 */       str1 = str1 + "&" + str5 + "$" + str6;
/*      */     } 
/*      */     
/*  998 */     String str4 = "";
/*      */     
/* 1000 */     str3 = "select distinct changefieldids from workflow_viewattrlinkage where workflowid=" + paramInt1 + " and (nodeid=" + paramInt2 + " or nodeid = -1) and selectfieldid='" + Util.null2String(paramString1) + "' and selectfieldvalue!='" + Util.null2String(paramString2) + "'";
/* 1001 */     recordSet.executeSql(str3);
/* 1002 */     while (recordSet.next()) {
/* 1003 */       str4 = str4 + Util.null2String(recordSet.getString("changefieldids")) + ",";
/*      */     }
/* 1005 */     arrayList = Util.TokenizerString(str4, ",");
/* 1006 */     if (str1.equals("")) {
/* 1007 */       str1 = str4 + "$-1";
/*      */     } else {
/* 1009 */       String str = "";
/* 1010 */       for (byte b = 0; b < arrayList.size(); b++) {
/* 1011 */         if ((str2 + ",").indexOf("," + arrayList.get(b) + ",") == -1) {
/* 1012 */           str = str + "," + arrayList.get(b);
/*      */         }
/*      */       } 
/* 1015 */       if (!str.equals("")) {
/* 1016 */         if (str.startsWith(",")) str = str.substring(1);
/*      */         
/* 1018 */         str1 = str + "$-1&" + str1;
/*      */       } 
/*      */     } 
/* 1021 */     return str1;
/*      */   }
/*      */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/workflow/WfLinkageInfo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */