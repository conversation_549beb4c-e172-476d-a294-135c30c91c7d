/*     */ package weaver.workflow.workflow;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.List;
/*     */ import weaver.common.util.taglib.TreeNode;
/*     */ import weaver.common.util.xtree.TreeNode;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class WorkFlowTree
/*     */ {
/*     */   public List getWrokflowTree(User paramUser, int paramInt) {
/*  33 */     ArrayList<TreeNode> arrayList = new ArrayList();
/*  34 */     RecordSet recordSet = new RecordSet();
/*  35 */     int i = paramUser.getUID();
/*  36 */     String str = "select id,typename from  workflow_type";
/*  37 */     if (paramInt == 1) {
/*  38 */       TreeNode treeNode1 = new TreeNode();
/*  39 */       treeNode1.setIcon("custom1");
/*  40 */       treeNode1.setLevel(new Integer(0));
/*  41 */       treeNode1.setLinktype("js");
/*  42 */       treeNode1.setLink("setGroup(1)");
/*  43 */       treeNode1.setExpand("false");
/*  44 */       treeNode1.setTitle("&nbsp;" + SystemEnv.getHtmlLabelName(18334, paramUser.getLanguage()));
/*  45 */       arrayList.add(treeNode1);
/*  46 */       recordSet.executeSql(str);
/*  47 */       while (recordSet.next()) {
/*  48 */         String str1 = Util.formatMultiLang(recordSet.getString("typename"));
/*  49 */         int j = recordSet.getInt("id");
/*  50 */         TreeNode treeNode2 = new TreeNode();
/*  51 */         treeNode2.setIcon("custom2");
/*  52 */         treeNode2.setTitle(str1);
/*  53 */         treeNode2.setLevel(new Integer(1));
/*  54 */         treeNode2.setLinktype("js");
/*  55 */         treeNode2.setLink("setWorkflowType(1," + j + ")");
/*  56 */         arrayList.add(treeNode2);
/*     */       } 
/*     */     } 
/*     */ 
/*     */     
/*  61 */     TreeNode treeNode = new TreeNode();
/*  62 */     treeNode.setIcon("custom1");
/*  63 */     treeNode.setLevel(new Integer(0));
/*  64 */     treeNode.setLinktype("js");
/*  65 */     treeNode.setLink("setGroup(0)");
/*  66 */     treeNode.setExpand("false");
/*  67 */     treeNode.setTitle("&nbsp;" + SystemEnv.getHtmlLabelName(2118, paramUser.getLanguage()));
/*  68 */     arrayList.add(treeNode);
/*  69 */     recordSet.executeSql(str);
/*  70 */     while (recordSet.next()) {
/*  71 */       String str1 = Util.formatMultiLang(recordSet.getString("typename"));
/*  72 */       int j = recordSet.getInt("id");
/*  73 */       TreeNode treeNode1 = new TreeNode();
/*  74 */       treeNode1.setIcon("custom2");
/*  75 */       treeNode1.setTitle(str1);
/*  76 */       treeNode1.setLevel(new Integer(1));
/*  77 */       treeNode1.setLinktype("js");
/*  78 */       treeNode1.setLink("setWorkflowType(0," + j + ")");
/*  79 */       arrayList.add(treeNode1);
/*     */     } 
/*     */     
/*  82 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public TreeNode getWorkflowTypeTreeList(TreeNode paramTreeNode, String paramString1, String paramString2, String paramString3, String paramString4, int paramInt) throws Exception {
/*  97 */     return getWorkflowTypeTreeList(paramTreeNode, paramString1, paramString2, paramString3, paramString4, paramInt, "0", "", paramString2, "", "");
/*     */   }
/*     */   
/*     */   public TreeNode getWorkflowTypeTreeList(TreeNode paramTreeNode, String paramString1, String paramString2, String paramString3, String paramString4, int paramInt, String paramString5, String paramString6, String paramString7, String paramString8, String paramString9) throws Exception {
/* 101 */     RecordSet recordSet = new RecordSet();
/* 102 */     StringBuffer stringBuffer = new StringBuffer();
/* 103 */     stringBuffer.append("select id,typename from workflow_type a ");
/* 104 */     if ("".equals(paramString1)) {
/* 105 */       stringBuffer.append(" where exists (select 1 from workflow_base b where  a.id = b.workflowtype and workflowname like '%" + paramString8 + "%'");
/* 106 */       stringBuffer.append(" and (isvalid<>3 or isvalid is null)");
/* 107 */       if (paramString3.equals("1")) {
/* 108 */         stringBuffer.append(" and istemplate='1'");
/*     */       } else {
/* 110 */         stringBuffer.append(" and (istemplate is null or istemplate<>'1') ");
/*     */       } 
/* 112 */       if (paramString5.equals("1")) {
/* 113 */         stringBuffer.append(" and isWorkflowDoc = 1");
/*     */       }
/*     */ 
/*     */       
/* 117 */       if (paramInt == 1) {
/* 118 */         if ("-1".equals(paramString2)) {
/* 119 */           paramString2 = paramString9;
/*     */         }
/* 121 */         else if (("," + paramString9 + ",").indexOf("," + paramString2 + ",") < 0) {
/* 122 */           paramString2 = "-1";
/*     */         } 
/*     */         
/* 125 */         String str = Util.getSubINClause(paramString2, "subcompanyid", "IN");
/* 126 */         if (!"".equals(paramString6) && !"".equals(paramString2)) {
/* 127 */           if (paramString3.equals("1")) {
/* 128 */             stringBuffer.append(" and ( templateid in (" + paramString6 + ") or " + str + ")");
/*     */           } else {
/* 130 */             stringBuffer.append(" and ( id in (" + paramString6 + ") or " + str + ")");
/*     */           } 
/* 132 */         } else if (!"".equals(paramString6)) {
/* 133 */           if (paramString3.equals("1")) {
/* 134 */             stringBuffer.append(" and templateid in (").append(paramString6).append(")");
/*     */           } else {
/* 136 */             stringBuffer.append(" and id in (").append(paramString6).append(")");
/*     */           }
/*     */         
/*     */         } else {
/*     */           
/* 141 */           stringBuffer.append(" and " + str);
/*     */         }
/*     */       
/*     */       }
/* 145 */       else if (!"".equals(paramString6)) {
/* 146 */         if (paramString3.equals("1")) {
/* 147 */           stringBuffer.append(" and templateid in (" + paramString6 + ")");
/*     */         } else {
/* 149 */           stringBuffer.append(" and id in (" + paramString6 + ")");
/*     */         } 
/*     */       } 
/*     */       
/* 153 */       stringBuffer.append(" )");
/*     */     } 
/*     */     
/* 156 */     if ("multiworkflowtype".equals(paramString1) && paramInt == 1) {
/* 157 */       String str = Util.getSubINClause(paramString2, "subcompanyid", "IN");
/* 158 */       stringBuffer.append(" where exists (select 1 from workflow_base b where  a.id = b.workflowtype");
/* 159 */       stringBuffer.append(" and " + str + ")");
/*     */     } 
/* 161 */     if (stringBuffer.toString().indexOf("where") > 0) {
/* 162 */       stringBuffer.append("  order by dsporder asc,id asc");
/*     */     } else {
/* 164 */       if (!"".equals(paramString8)) {
/* 165 */         stringBuffer.append(" where  exists (select 1 from workflow_base b where  a.id = b.workflowtype and workflowname like '%" + paramString8 + "%' and (isvalid<>3 or isvalid is null)");
/* 166 */         if (paramString3.equals("1")) {
/* 167 */           stringBuffer.append(" and istemplate='1'");
/*     */         } else {
/* 169 */           stringBuffer.append(" and (istemplate is null or istemplate<>'1') ");
/*     */         } 
/* 171 */         stringBuffer.append(")");
/*     */       } 
/* 173 */       stringBuffer.append(" order by dsporder asc,id asc");
/*     */     } 
/*     */     
/* 176 */     recordSet.executeSql(stringBuffer.toString());
/* 177 */     while (recordSet.next()) {
/* 178 */       String str1 = Util.null2String(recordSet.getString("id"));
/* 179 */       if (str1.equals(""))
/* 180 */         str1 = "0"; 
/* 181 */       String str2 = Util.formatMultiLang(Util.null2String(recordSet.getString("typename")));
/* 182 */       TreeNode treeNode = new TreeNode();
/* 183 */       treeNode.setTitle(str2);
/* 184 */       treeNode.setNodeId("workflowtype_" + str1);
/* 185 */       treeNode.setIcon("/LeftMenu/ThemeXP/folder2_wev8.gif");
/* 186 */       if ("multiworkflowtype".equals(paramString1)) {
/* 187 */         treeNode.setHref("javascript:setWorkflowType('" + treeNode.getNodeId() + "')");
/* 188 */         treeNode.setTarget("_self");
/*     */       } else {
/* 190 */         treeNode.setNodeXmlSrc("/workflow/workflow/WorkflowXML.jsp?isWorkflowDoc=" + paramString5 + "&type=" + paramString1 + "&id=" + str1 + "&nodeid=" + treeNode.getNodeId() + "&subCompanyId=" + paramString7 + "&isTemplate=" + paramString3 + "&workflowIds=" + paramString6 + "&searchStr=" + paramString8);
/* 191 */         treeNode.setHref("/workflow/workflow/managewfTab.jsp?isWorkflowDoc=" + paramString5 + "&isTemplate=" + paramString3 + "&typeid=" + str1 + "&subCompanyId=" + paramString7);
/* 192 */         treeNode.setTarget("wfmainFrame");
/* 193 */         treeNode.setOncheck("check(" + treeNode.getNodeId() + ")");
/*     */       } 
/* 195 */       paramTreeNode.addTreeNode(treeNode);
/* 196 */       if (str1.equals(paramString4));
/*     */     } 
/*     */ 
/*     */     
/* 200 */     return paramTreeNode;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public TreeNode getWorkflowTreeList(TreeNode paramTreeNode, String paramString1, String paramString2, String paramString3, String paramString4, int paramInt) throws Exception {
/* 214 */     return getWorkflowTreeList(paramTreeNode, paramString1, paramString2, paramString3, paramString4, paramInt, "0", "", "", "");
/*     */   }
/*     */   public TreeNode getWorkflowTreeList(TreeNode paramTreeNode, String paramString1, String paramString2, String paramString3, String paramString4, int paramInt, String paramString5, String paramString6, String paramString7, String paramString8) throws Exception {
/* 217 */     RecordSet recordSet = new RecordSet();
/* 218 */     WorkflowVersion workflowVersion = new WorkflowVersion();
/*     */     
/* 220 */     StringBuffer stringBuffer = new StringBuffer();
/* 221 */     stringBuffer.append("select id,workflowname from workflow_base where (isvalid<>3 or isvalid is null)");
/* 222 */     stringBuffer.append(" and workflowname like '%" + paramString7 + "%' ");
/* 223 */     if (paramString3.equals("1")) {
/* 224 */       stringBuffer.append(" and istemplate='1'");
/*     */     } else {
/* 226 */       stringBuffer.append(" and (istemplate is null or istemplate<>'1') ");
/*     */     } 
/* 228 */     if (paramString5.equals("1")) {
/* 229 */       stringBuffer.append(" and isWorkflowDoc = 1");
/*     */     }
/* 231 */     if (!"".equals(paramString4)) {
/* 232 */       stringBuffer.append(" and workflowtype = ").append(paramString4);
/*     */     }
/*     */     
/* 235 */     if (paramInt == 1) {
/* 236 */       if ("-1".equals(paramString2)) {
/* 237 */         paramString2 = paramString8;
/*     */       }
/* 239 */       else if (("," + paramString8 + ",").indexOf("," + paramString2 + ",") < 0) {
/* 240 */         paramString2 = "-1";
/*     */       } 
/*     */       
/* 243 */       String str = Util.getSubINClause(paramString2, "subcompanyid", "IN");
/* 244 */       if (!"".equals(paramString6) && !"".equals(paramString2)) {
/* 245 */         if (paramString3.equals("1")) {
/* 246 */           stringBuffer.append(" and (templateid in (");
/*     */         } else {
/* 248 */           stringBuffer.append(" and (id in (");
/*     */         } 
/* 250 */         stringBuffer.append(paramString6).append(") or " + str + ")");
/* 251 */       } else if (!"".equals(paramString6)) {
/* 252 */         if (paramString3.equals("1")) {
/* 253 */           stringBuffer.append(" and templateid in (");
/*     */         } else {
/* 255 */           stringBuffer.append(" and id in (");
/*     */         } 
/* 257 */         stringBuffer.append(paramString6).append(")");
/*     */       } else {
/*     */         
/* 260 */         stringBuffer.append(" and " + str);
/*     */       }
/*     */     
/*     */     }
/* 264 */     else if (!"".equals(paramString6)) {
/* 265 */       if (paramString3.equals("1")) {
/* 266 */         stringBuffer.append(" and templateid in (");
/*     */       } else {
/* 268 */         stringBuffer.append(" and id in (");
/*     */       } 
/* 270 */       stringBuffer.append(paramString6).append(")");
/*     */     } 
/*     */     
/* 273 */     stringBuffer.append(" order by dsporder asc,workflowname asc");
/* 274 */     recordSet.executeSql(stringBuffer.toString());
/* 275 */     while (recordSet.next()) {
/* 276 */       String str1 = Util.null2String(recordSet.getString("id"));
/* 277 */       if (str1.equals(""))
/* 278 */         str1 = "0"; 
/* 279 */       int i = WorkflowVersion.getVersionID(str1);
/* 280 */       String str2 = Util.formatMultiLang(Util.null2String(recordSet.getString("workflowname")));
/* 281 */       TreeNode treeNode = new TreeNode();
/* 282 */       treeNode.setTitle(str2);
/* 283 */       treeNode.setNodeId("workflow_" + str1);
/* 284 */       treeNode.setIcon("/LeftMenu/ThemeXP/page_wev8.gif");
/*     */       
/* 286 */       if ("1".equals(paramString5)) {
/* 287 */         treeNode.setHref("/docs/tabs/DocCommonTab.jsp?wfid=" + str1 + "&_fromURL=49&isWorkflowDoc=" + paramString5);
/*     */       }
/* 289 */       else if ("1".equals(paramString3)) {
/* 290 */         treeNode.setHref("/workflow/workflow/addwf.jsp?src=editwf&wfid=" + str1 + "&isTemplate=" + paramString3);
/*     */       } else {
/* 292 */         treeNode.setHref("/workflow/workflow/addwf.jsp?src=editwf&wfid=" + str1 + "&isTemplate=" + paramString3 + "&versionid_toXtree=" + i);
/*     */       } 
/*     */       
/* 295 */       treeNode.setTarget("wfmainFrame");
/* 296 */       treeNode.setOncheck("check(" + treeNode.getNodeId() + ")");
/* 297 */       paramTreeNode.addTreeNode(treeNode);
/*     */     } 
/* 299 */     return paramTreeNode;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/workflow/WorkFlowTree.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */