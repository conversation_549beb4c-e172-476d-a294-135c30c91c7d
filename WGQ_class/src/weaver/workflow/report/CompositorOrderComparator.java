/*    */ package weaver.workflow.report;
/*    */ 
/*    */ import java.util.Comparator;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class CompositorOrderComparator
/*    */   implements Comparator
/*    */ {
/*    */   public int compare(Object paramObject1, Object paramObject2) {
/* 30 */     int i = ((ReportCompositorOrderBean)paramObject1).getCompositorOrder();
/*    */ 
/*    */     
/* 33 */     int j = ((ReportCompositorOrderBean)paramObject2).getCompositorOrder();
/*    */ 
/*    */     
/* 36 */     return (new Integer(i)).compareTo(new Integer(j));
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/report/CompositorOrderComparator.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */