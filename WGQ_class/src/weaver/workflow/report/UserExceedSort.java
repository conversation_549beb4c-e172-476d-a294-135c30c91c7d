/*     */ package weaver.workflow.report;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.List;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.company.DepartmentComInfo;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class UserExceedSort
/*     */   extends BaseBean
/*     */ {
/*  36 */   private DepartmentComInfo departmentcominfo = new DepartmentComInfo();
/*  37 */   private ResourceComInfo resourcecominfo = new ResourceComInfo();
/*  38 */   private ReportShare MDCompanyNameInfo = new ReportShare();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List getUserSort(String paramString) throws Exception {
/*  48 */     RecordSet recordSet = new RecordSet();
/*  49 */     ArrayList<String> arrayList1 = new ArrayList();
/*  50 */     ArrayList<String> arrayList2 = new ArrayList();
/*  51 */     ArrayList<String> arrayList3 = new ArrayList();
/*  52 */     ArrayList<String> arrayList4 = new ArrayList();
/*  53 */     ArrayList<String> arrayList5 = new ArrayList();
/*  54 */     ArrayList<String> arrayList6 = new ArrayList();
/*  55 */     ArrayList<String> arrayList7 = new ArrayList();
/*  56 */     ArrayList<ArrayList<String>> arrayList = new ArrayList();
/*  57 */     char c = Util.getSeparator();
/*  58 */     byte b = 1;
/*  59 */     if (recordSet.getDBType().equals("oracle")) {
/*  60 */       String str = "select userid,count(distinct workflow_requestbase.requestid) as counts, (select count(requestid) from workflow_requestbase b where exists (select 1 from workflow_currentoperator a where a.requestid=b.requestid  and a.userid=workflow_currentoperator.userid )  and b.status is not null ) as countall, to_number(count(distinct workflow_requestbase.requestid)*100)/to_number((select count(requestid) from workflow_requestbase b  where exists (select 1 from workflow_currentoperator  a where a.requestid=b.requestid  and a.userid=workflow_currentoperator.userid ) and b.status is not null ) ) as percents from workflow_currentoperator,workflow_requestbase where  workflow_currentoperator.requestid=workflow_requestbase.requestid and (workflow_currentoperator.isprocessed = '2' or workflow_currentoperator.isprocessed = '3') and workflow_requestbase.status is not null and exists (select 1 from workflow_nodelink where workflowid=workflow_requestbase.workflowid and (workflow_currentoperator.usertype = 0) and exists (select 1 from hrmresource where hrmresource.id=workflow_currentoperator.userid and hrmresource.status in (0,1,2,3)) and (to_number(NVL(nodepasshour,0))>0 or to_number(nvl(nodepassminute,0))>0))" + paramString + " group by userid order by percents desc,userid";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/*  80 */       recordSet.executeSql(str);
/*  81 */     } else if (recordSet.getDBType().equals("mysql")) {
/*  82 */       String str = "select userid,count(distinct workflow_requestbase.requestid) as counts, (select count(distinct b.requestid) from workflow_requestbase b , workflow_currentoperator a where a.requestid=b.requestid  and a.userid=workflow_currentoperator.userid   and b.status is not null ) as countall, CONVERT(count(distinct workflow_requestbase.requestid)*100,SIGNED)/CONVERT((select count(distinct b.requestid) from workflow_requestbase b  ,workflow_currentoperator  a where a.requestid=b.requestid  and a.userid=workflow_currentoperator.userid  and b.status is not null ),SIGNED) as percents from workflow_currentoperator,workflow_requestbase where  workflow_currentoperator.requestid=workflow_requestbase.requestid and (workflow_currentoperator.isprocessed = '2' or workflow_currentoperator.isprocessed = '3') and workflow_requestbase.status is not null and exists (select 1 from workflow_nodelink where workflowid=workflow_requestbase.workflowid and (workflow_currentoperator.usertype = 0) and exists (select 1 from hrmresource where hrmresource.id=workflow_currentoperator.userid and hrmresource.status in (0,1,2,3)) and (CONVERT(ifnull(nodepasshour,0),SIGNED)>0 or CONVERT(ifnull(nodepassminute,0),SIGNED)>0))" + paramString + " group by userid order by percents desc,userid";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 102 */       recordSet.executeSql(str);
/*     */     }
/* 104 */     else if (recordSet.getDBType().equals("postgresql")) {
/* 105 */       String str = "select userid,count(distinct workflow_requestbase.requestid) as counts, (select count(requestid) from workflow_requestbase b where exists (select 1 from workflow_currentoperator a where a.requestid=b.requestid  and a.userid=workflow_currentoperator.userid )  and b.status is not null ) as countall, to_number(count(distinct workflow_requestbase.requestid)*100)/to_number((select count(requestid) from workflow_requestbase b  where exists (select 1 from workflow_currentoperator  a where a.requestid=b.requestid  and a.userid=workflow_currentoperator.userid ) and b.status is not null ) ) as percents from workflow_currentoperator,workflow_requestbase where  workflow_currentoperator.requestid=workflow_requestbase.requestid and (workflow_currentoperator.isprocessed = '2' or workflow_currentoperator.isprocessed = '3') and workflow_requestbase.status is not null and exists (select 1 from workflow_nodelink where workflowid=workflow_requestbase.workflowid and (workflow_currentoperator.usertype = 0) and exists (select 1 from hrmresource where hrmresource.id=workflow_currentoperator.userid and hrmresource.status in (0,1,2,3)) and (to_number(isnull(nodepasshour,0))>0 or to_number(isnull(nodepassminute,0))>0))" + paramString + " group by userid order by percents desc,userid";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 125 */       recordSet.executeSql(str);
/*     */     } else {
/*     */       
/* 128 */       recordSet.executeProc("MostExceedPerson_Get", paramString);
/*     */     } 
/* 130 */     while (recordSet.next()) {
/* 131 */       int i = 1;
/* 132 */       int j = 1;
/* 133 */       String str1 = recordSet.getString(1);
/* 134 */       String str2 = recordSet.getString(2);
/* 135 */       String str3 = recordSet.getString(3);
/* 136 */       String str4 = recordSet.getString(4);
/* 137 */       String str5 = this.resourcecominfo.getDepartmentID(str1);
/* 138 */       String str6 = this.departmentcominfo.getSubcompanyid1(str5); int k;
/* 139 */       for (k = arrayList4.size() - 1; k >= 0; k--) {
/*     */         
/* 141 */         String str7 = "" + arrayList4.get(k);
/* 142 */         String str8 = str7.substring(str7.indexOf("$") + 1);
/* 143 */         if (str8.equals(str5)) {
/*     */           
/* 145 */           i = Util.getIntValue(str7.substring(0, str7.indexOf("$")), 1) + 1;
/*     */           
/*     */           break;
/*     */         } 
/*     */       } 
/*     */       
/* 151 */       for (k = arrayList5.size() - 1; k >= 0; k--) {
/*     */         
/* 153 */         String str7 = "" + arrayList5.get(k);
/* 154 */         String str8 = str7.substring(str7.indexOf("$") + 1);
/* 155 */         if (str8.equals(str6)) {
/*     */           
/* 157 */           j = Util.getIntValue(str7.substring(0, str7.indexOf("$")), 1) + 1;
/*     */ 
/*     */           
/*     */           break;
/*     */         } 
/*     */       } 
/*     */       
/* 164 */       arrayList4.add("" + i + "$" + str5);
/* 165 */       arrayList5.add("" + j + "$" + str6);
/* 166 */       arrayList6.add(str1);
/* 167 */       arrayList7.add(str2);
/* 168 */       arrayList1.add("" + b);
/* 169 */       arrayList2.add("" + str3);
/* 170 */       arrayList3.add("" + str4);
/* 171 */       b++;
/*     */     } 
/* 173 */     arrayList.add(arrayList1);
/* 174 */     arrayList.add(arrayList6);
/* 175 */     arrayList.add(arrayList7);
/* 176 */     arrayList.add(arrayList4);
/* 177 */     arrayList.add(arrayList5);
/* 178 */     arrayList.add(arrayList2);
/* 179 */     arrayList.add(arrayList3);
/* 180 */     return arrayList;
/*     */   }
/*     */   
/*     */   public String getPercents(String paramString) {
/* 184 */     return Util.round(paramString, 2) + "%";
/*     */   }
/*     */   
/*     */   public String getDepartmentSort(String paramString1, String paramString2) {
/* 188 */     StringBuilder stringBuilder = new StringBuilder();
/* 189 */     RecordSet recordSet = new RecordSet();
/*     */     
/* 191 */     stringBuilder.append("select id from ").append(Util.toSqlForSplitPage(paramString2)).append(" order by percents desc");
/* 192 */     recordSet.executeSql(stringBuilder.toString());
/* 193 */     String str = this.resourcecominfo.getDepartmentID(paramString1);
/* 194 */     long l = 0L;
/* 195 */     while (recordSet.next()) {
/* 196 */       String str1 = recordSet.getString(1);
/* 197 */       String str2 = this.resourcecominfo.getDepartmentID(str1);
/* 198 */       if (str.equals(str2)) {
/* 199 */         l++;
/* 200 */         if (paramString1.equals(str1)) {
/*     */           break;
/*     */         }
/*     */       } 
/*     */     } 
/* 205 */     return String.valueOf(l);
/*     */   }
/*     */   
/*     */   public String getSubCompanySort(String paramString1, String paramString2) {
/* 209 */     StringBuilder stringBuilder = new StringBuilder();
/* 210 */     stringBuilder.append("select id from ").append(Util.toSqlForSplitPage(paramString2)).append(" order by percents desc");
/* 211 */     RecordSet recordSet = new RecordSet();
/*     */     
/* 213 */     recordSet.executeSql(stringBuilder.toString());
/* 214 */     String str1 = this.resourcecominfo.getDepartmentID(paramString1);
/* 215 */     String str2 = this.departmentcominfo.getSubcompanyid1(str1);
/* 216 */     long l = 0L;
/* 217 */     while (recordSet.next()) {
/* 218 */       String str3 = recordSet.getString(1);
/* 219 */       String str4 = this.resourcecominfo.getDepartmentID(str3);
/* 220 */       String str5 = this.departmentcominfo.getSubcompanyid1(str4);
/* 221 */       if (str2.equals(str5)) {
/* 222 */         l++;
/* 223 */         if (paramString1.equals(str3)) {
/*     */           break;
/*     */         }
/*     */       } 
/*     */     } 
/* 228 */     return String.valueOf(l);
/*     */   }
/*     */   
/*     */   public String getFromSQL(String paramString) {
/* 232 */     StringBuilder stringBuilder = new StringBuilder();
/* 233 */     RecordSet recordSet = new RecordSet();
/*     */     
/* 235 */     if (recordSet.getDBType().equals("oracle")) {
/* 236 */       stringBuilder.append("(select userid as id");
/* 237 */       stringBuilder.append(", count(distinct workflow_requestbase.requestid) counts");
/* 238 */       stringBuilder.append(", (select count(requestid) from workflow_requestbase b");
/* 239 */       stringBuilder.append("   where exists (select 1 from workflow_currentoperator a");
/* 240 */       stringBuilder.append("    where a.requestid=b.requestid");
/* 241 */       stringBuilder.append("    and a.userid=workflow_currentoperator.userid)");
/* 242 */       stringBuilder.append("   and b.status is not null )  countall");
/* 243 */       stringBuilder.append(", to_number(count(distinct workflow_requestbase.requestid)*100)/to_number((select count(requestid) from workflow_requestbase b");
/* 244 */       stringBuilder.append("  where exists (select 1 from workflow_currentoperator a");
/* 245 */       stringBuilder.append("   where a.requestid=b.requestid");
/* 246 */       stringBuilder.append("   and a.userid=workflow_currentoperator.userid)");
/* 247 */       stringBuilder.append("  and b.status is not null))  percents");
/* 248 */       stringBuilder.append(" from workflow_currentoperator,workflow_requestbase");
/* 249 */       stringBuilder.append(" where workflow_currentoperator.requestid=workflow_requestbase.requestid");
/* 250 */       stringBuilder.append("  and (workflow_currentoperator.isprocessed = '2' or workflow_currentoperator.isprocessed = '3')");
/* 251 */       stringBuilder.append("  and workflow_requestbase.status is not null");
/* 252 */       stringBuilder.append("  and exists (select 1 from workflow_nodelink where workflowid=workflow_requestbase.workflowid");
/* 253 */       stringBuilder.append("   and (workflow_currentoperator.usertype = 0) and exists (select 1 from hrmresource where hrmresource.id=workflow_currentoperator.userid and hrmresource.status in (0,1,2,3))");
/* 254 */       stringBuilder.append("  and (to_number(NVL(nodepasshour,0))>0 or to_number(nvl(nodepassminute,0))>0))");
/* 255 */       stringBuilder.append(" ").append(paramString);
/* 256 */       stringBuilder.append(" group by userid)  temptab");
/* 257 */     } else if (recordSet.getDBType().equals("mysql")) {
/* 258 */       stringBuilder.append("(select userid as id");
/* 259 */       stringBuilder.append(", count(distinct workflow_requestbase.requestid) counts");
/* 260 */       stringBuilder.append(", (select count(distinct b.requestid) from workflow_requestbase b");
/* 261 */       stringBuilder.append("   ,workflow_currentoperator a");
/* 262 */       stringBuilder.append("    where a.requestid=b.requestid");
/* 263 */       stringBuilder.append("    and a.userid=workflow_currentoperator.userid");
/* 264 */       stringBuilder.append("   and b.status is not null )  countall");
/* 265 */       stringBuilder.append(", CONVERT(count(distinct workflow_requestbase.requestid)*100,SIGNED)/CONVERT((select count(distinct b.requestid) from workflow_requestbase b");
/* 266 */       stringBuilder.append("  , workflow_currentoperator a");
/* 267 */       stringBuilder.append("   where a.requestid=b.requestid");
/* 268 */       stringBuilder.append("   and a.userid=workflow_currentoperator.userid");
/* 269 */       stringBuilder.append("  and b.status is not null),SIGNED)  percents");
/* 270 */       stringBuilder.append(" from workflow_currentoperator,workflow_requestbase");
/* 271 */       stringBuilder.append(" where workflow_currentoperator.requestid=workflow_requestbase.requestid");
/* 272 */       stringBuilder.append("  and (workflow_currentoperator.isprocessed = '2' or workflow_currentoperator.isprocessed = '3')");
/* 273 */       stringBuilder.append("  and workflow_requestbase.status is not null");
/* 274 */       stringBuilder.append("  and exists (select 1 from workflow_nodelink where workflowid=workflow_requestbase.workflowid");
/* 275 */       stringBuilder.append("   and (workflow_currentoperator.usertype = 0) and exists (select 1 from hrmresource where hrmresource.id=workflow_currentoperator.userid and hrmresource.status in (0,1,2,3))");
/* 276 */       stringBuilder.append("  and (CONVERT(ifnull(nodepasshour,0),SIGNED)>0 or CONVERT(ifnull(nodepassminute,0),SIGNED)>0))");
/* 277 */       stringBuilder.append(" ").append(paramString);
/* 278 */       stringBuilder.append(" group by userid)  temptab");
/*     */     
/*     */     }
/* 281 */     else if (recordSet.getDBType().equals("postgresql")) {
/* 282 */       stringBuilder.append("(select userid as id");
/* 283 */       stringBuilder.append(", count(distinct workflow_requestbase.requestid) counts");
/* 284 */       stringBuilder.append(", (select count(requestid) from workflow_requestbase b");
/* 285 */       stringBuilder.append("   where exists (select 1 from workflow_currentoperator a");
/* 286 */       stringBuilder.append("    where a.requestid=b.requestid");
/* 287 */       stringBuilder.append("    and a.userid=workflow_currentoperator.userid)");
/* 288 */       stringBuilder.append("   and b.status is not null )  countall");
/* 289 */       stringBuilder.append(", to_number(count(distinct workflow_requestbase.requestid)*100)/to_number((select count(requestid) from workflow_requestbase b");
/* 290 */       stringBuilder.append("  where exists (select 1 from workflow_currentoperator a");
/* 291 */       stringBuilder.append("   where a.requestid=b.requestid");
/* 292 */       stringBuilder.append("   and a.userid=workflow_currentoperator.userid)");
/* 293 */       stringBuilder.append("  and b.status is not null))  percents");
/* 294 */       stringBuilder.append(" from workflow_currentoperator,workflow_requestbase");
/* 295 */       stringBuilder.append(" where workflow_currentoperator.requestid=workflow_requestbase.requestid");
/* 296 */       stringBuilder.append("  and (workflow_currentoperator.isprocessed = '2' or workflow_currentoperator.isprocessed = '3')");
/* 297 */       stringBuilder.append("  and workflow_requestbase.status is not null");
/* 298 */       stringBuilder.append("  and exists (select 1 from workflow_nodelink where workflowid=workflow_requestbase.workflowid");
/* 299 */       stringBuilder.append("   and (workflow_currentoperator.usertype = 0) and exists (select 1 from hrmresource where hrmresource.id=workflow_currentoperator.userid and hrmresource.status in (0,1,2,3))");
/* 300 */       stringBuilder.append("  and (to_number(isnull(nodepasshour,0))>0 or to_number(isnull(nodepassminute,0))>0))");
/* 301 */       stringBuilder.append(" ").append(paramString);
/* 302 */       stringBuilder.append(" group by userid)  temptab");
/*     */     } else {
/*     */       
/* 305 */       stringBuilder.append("(select top 100 percent userid as id");
/* 306 */       stringBuilder.append(", count(distinct workflow_requestbase.requestid) as counts");
/* 307 */       stringBuilder.append(", (select count(requestid) from workflow_requestbase b");
/* 308 */       stringBuilder.append("   where exists (select 1 from workflow_currentoperator a");
/* 309 */       stringBuilder.append("    where a.requestid=b.requestid");
/* 310 */       stringBuilder.append("    and a.userid=workflow_currentoperator.userid)");
/* 311 */       stringBuilder.append("   and b.status is not null and b.status!='' ) as countall");
/* 312 */       stringBuilder.append(", convert(float,count(distinct workflow_requestbase.requestid)*100)/convert(float,(select count(requestid) from workflow_requestbase b");
/* 313 */       stringBuilder.append("  where exists (select 1 from workflow_currentoperator a");
/* 314 */       stringBuilder.append("   where a.requestid=b.requestid");
/* 315 */       stringBuilder.append("   and a.userid=workflow_currentoperator.userid)");
/* 316 */       stringBuilder.append("  and b.status is not null and b.status!='')) as percents");
/* 317 */       stringBuilder.append(" from workflow_currentoperator,workflow_requestbase");
/* 318 */       stringBuilder.append(" where workflow_currentoperator.requestid=workflow_requestbase.requestid");
/* 319 */       stringBuilder.append("  and (workflow_currentoperator.isprocessed = '2' or workflow_currentoperator.isprocessed = '3')");
/* 320 */       stringBuilder.append("  and workflow_requestbase.status is not null and workflow_requestbase.status!=''");
/* 321 */       stringBuilder.append("  and exists (select 1 from workflow_nodelink where workflowid=workflow_requestbase.workflowid");
/* 322 */       stringBuilder.append("   and (workflow_currentoperator.usertype = 0) and exists (select 1 from hrmresource where hrmresource.id=workflow_currentoperator.userid and hrmresource.status in (0,1,2,3))");
/* 323 */       stringBuilder.append("  and (convert(float,nodepasshour)>0 or convert(float,nodepassminute)>0))");
/* 324 */       stringBuilder.append(" and isremark<>4");
/* 325 */       stringBuilder.append(" ").append(paramString);
/* 326 */       stringBuilder.append(" group by userid) as temptab");
/*     */     } 
/*     */     
/* 329 */     return stringBuilder.toString();
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/report/UserExceedSort.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */