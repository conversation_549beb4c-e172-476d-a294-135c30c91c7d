/*     */ package weaver.workflow.report;
/*     */ 
/*     */ import java.text.DecimalFormat;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Arrays;
/*     */ import java.util.Comparator;
/*     */ import java.util.Hashtable;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import java.util.Set;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.ThreadVarLanguage;
/*     */ import weaver.general.TimeUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.workflow.workflow.WorkTypeComInfo;
/*     */ import weaver.workflow.workflow.WorkflowComInfo;
/*     */ import weaver.workflow.workflow.WorkflowVersion;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class MostExceedFlow
/*     */   extends BaseBean
/*     */ {
/*     */   public List getMostExceedSort(String paramString) throws Exception {
/*  46 */     String str1 = "";
/*  47 */     RecordSet recordSet1 = new RecordSet();
/*  48 */     RecordSet recordSet2 = new RecordSet();
/*  49 */     if (recordSet1.getDBType().equals("oracle")) {
/*  50 */       str1 = "select requestid, workflowid, nodeid, userid, isremark, (select requestname from workflow_requestbase t2 where t2.requestid = t1.requestid) as requestname, (operatedate||' '||operatetime) as operatedate, (receivedate||' '||receivetime) as receivedate from workflow_currentoperator t1 where (isprocessed = '2' or isprocessed = '3') " + paramString + "group by workflowid,requestid,nodeid,userid,operatedate,operatetime,receivedate,receivetime,isremark order by requestid desc";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*     */     }
/*  60 */     else if (recordSet1.getDBType().equals("mysql")) {
/*  61 */       str1 = "select requestid, workflowid, nodeid, userid, isremark, (select requestname from workflow_requestbase t2 where t2.requestid = t1.requestid) as requestname, (select destnodeid from workflow_requestlog where requestid=t1.requestid and nodeid=t1.nodeid and operator=t1.userid and logtype in('0','2') and operatedate+operatetime>=t1.receivedate+t1.receivetime order by operatedate+operatetime asc limit 1) as destnodeid, concat(operatedate,' ',operatetime) as operatedate, concat(receivedate,' ',receivetime) as receivedate from workflow_currentoperator t1 where (isprocessed = '2' or isprocessed = '3') " + paramString + "group by workflowid,requestid,nodeid,userid,operatedate,operatetime,receivedate,receivetime,isremark order by requestid desc";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*     */     }
/*  73 */     else if (recordSet1.getDBType().equals("postgresql")) {
/*  74 */       str1 = "select requestid, workflowid, nodeid, userid, isremark, (select requestname from workflow_requestbase t2 where t2.requestid = t1.requestid) as requestname, (select destnodeid from workflow_requestlog where requestid=t1.requestid and nodeid=t1.nodeid and operator=t1.userid and logtype in('0','2') and operatedate+operatetime>=t1.receivedate+t1.receivetime order by operatedate+operatetime asc limit 1) as destnodeid, concat(operatedate,' ',operatetime) as operatedate, concat(receivedate,' ',receivetime) as receivedate from workflow_currentoperator t1 where (isprocessed = '2' or isprocessed = '3') " + paramString + "group by workflowid,requestid,nodeid,userid,operatedate,operatetime,receivedate,receivetime,isremark order by requestid desc";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*     */     }
/*     */     else {
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/*  87 */       str1 = "select requestid, workflowid, nodeid, userid, isremark, (select requestname from workflow_requestbase t2 where t2.requestid = t1.requestid) as requestname, (select top 1 destnodeid from workflow_requestlog where requestid=t1.requestid and nodeid=t1.nodeid and operator=t1.userid and logtype in('0','2') and operatedate+operatetime>=t1.receivedate+t1.receivetime order by operatedate+operatetime asc) as destnodeid, (operatedate+' '+operatetime) as operatedate, (receivedate+' '+receivetime) as receivedate from workflow_currentoperator t1 where (isprocessed = '2' or isprocessed = '3') " + paramString + "group by workflowid,requestid,nodeid,userid,operatedate,operatetime,receivedate,receivetime,isremark order by requestid desc";
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  99 */     String str2 = "";
/* 100 */     Hashtable<Object, Object> hashtable = new Hashtable<>();
/*     */     
/* 102 */     recordSet1.executeSql(str1);
/* 103 */     while (recordSet1.next()) {
/* 104 */       String str3 = Util.null2String(recordSet1.getString("requestid"));
/* 105 */       String str4 = Util.null2String(recordSet1.getString("workflowid"));
/* 106 */       String str5 = Util.null2String(recordSet1.getString("requestname"));
/* 107 */       String str6 = Util.null2String(recordSet1.getString("nodeid"));
/* 108 */       String str7 = Util.null2String(recordSet1.getString("userid"));
/* 109 */       String str8 = Util.null2String(recordSet1.getString("isremark"));
/* 110 */       String str9 = Util.null2String(recordSet1.getString("receivedate"));
/* 111 */       String str10 = Util.null2String(recordSet1.getString("operatedate"));
/*     */       
/* 113 */       String str11 = "";
/* 114 */       if (recordSet2.getDBType().equals("oracle")) {
/* 115 */         recordSet2.executeSql("select destnodeid from workflow_requestlog where rownum=1 and requestid=" + str3 + " and nodeid=" + str6 + " and operator=" + str7 + " and logtype in ('0', '2') and (operatedate || operatetime >= '" + str9 + "') order by operatedate||operatetime asc");
/* 116 */         if (recordSet2.next()) str11 = Util.null2String(recordSet2.getString("destnodeid")); 
/*     */       } else {
/* 118 */         str11 = Util.null2String(recordSet1.getString("destnodeid"));
/*     */       } 
/*     */       
/* 121 */       str2 = str3 + "$" + str4 + "$" + str5 + "$" + str9;
/* 122 */       if ("".equals(str10) || "0".equals(str8)) str10 = TimeUtil.getCurrentTimeString(); 
/* 123 */       float f1 = (float)TimeUtil.timeInterval(str9, str10);
/* 124 */       float f2 = getEeceedOverTime(str4, str6, str11);
/* 125 */       float f3 = 0.0F;
/* 126 */       DecimalFormat decimalFormat = new DecimalFormat("0.00");
/* 127 */       if (f1 - f2 < 0.0F) {
/* 128 */         f3 = f1;
/*     */       } else {
/* 130 */         f3 = f1 - f2;
/*     */       } 
/* 132 */       if (hashtable.containsKey(str2)) {
/* 133 */         String str12 = "";
/* 134 */         String str13 = (String)hashtable.get(str2);
/* 135 */         str12 = decimalFormat.format(((Util.getFloatValue(str13) * 3600.0F + f3) / 3600.0F));
/* 136 */         hashtable.put(str2, str12); continue;
/*     */       } 
/* 138 */       hashtable.put(str2, decimalFormat.format((f3 / 3600.0F)));
/*     */     } 
/*     */ 
/*     */     
/* 142 */     ArrayList<ArrayList<String>> arrayList = new ArrayList();
/* 143 */     ArrayList<String> arrayList1 = new ArrayList();
/* 144 */     ArrayList<String> arrayList2 = new ArrayList();
/* 145 */     ArrayList<String> arrayList3 = new ArrayList();
/*     */     
/* 147 */     Map.Entry[] arrayOfEntry = getSortedHashtableByValue(hashtable);
/* 148 */     for (int i = arrayOfEntry.length; i > 0; i--) {
/* 149 */       String str3 = arrayOfEntry[i - 1].getKey().toString();
/* 150 */       String str4 = arrayOfEntry[i - 1].getValue().toString();
/* 151 */       String[] arrayOfString = Util.TokenizerString2(str3, "$");
/* 152 */       arrayList2.add(arrayOfString[1]);
/* 153 */       arrayList1.add(arrayOfString[2]);
/* 154 */       arrayList3.add(str4);
/*     */     } 
/* 156 */     arrayList.add(arrayList1);
/* 157 */     arrayList.add(arrayList2);
/* 158 */     arrayList.add(arrayList3);
/* 159 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public float getEeceedOverTime(String paramString1, String paramString2, String paramString3) {
/* 170 */     float f1 = 0.0F;
/* 171 */     float f2 = 0.0F;
/* 172 */     float f3 = 0.0F;
/* 173 */     RecordSet recordSet = new RecordSet();
/* 174 */     String str = "";
/* 175 */     if ("".equals(paramString3)) {
/* 176 */       str = "select nodepasshour,nodepassminute from workflow_nodelink where workflowid = " + paramString1 + " and nodeid = " + paramString2 + " and isreject='' and (nodepasshour>0 or nodepassminute>0)";
/*     */     } else {
/* 178 */       str = "select nodepasshour,nodepassminute from workflow_nodelink where workflowid = " + paramString1 + " and nodeid = " + paramString2 + " and destnodeid = " + paramString3 + " and isreject='' and (nodepasshour>0 or nodepassminute>0)";
/*     */     } 
/* 180 */     recordSet.executeSql(str);
/* 181 */     while (recordSet.next()) {
/* 182 */       f2 = Util.getFloatValue(recordSet.getString("nodepasshour"));
/* 183 */       f3 = Util.getFloatValue(recordSet.getString("nodepassminute"));
/* 184 */       f1 = f2 * 3600.0F + f3 * 60.0F;
/*     */     } 
/* 186 */     return f1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static Map.Entry[] getSortedHashtableByValue(Hashtable paramHashtable) {
/* 195 */     Set set = paramHashtable.entrySet();
/* 196 */     Map.Entry[] arrayOfEntry = (Map.Entry[])set.toArray((Object[])new Map.Entry[set.size()]);
/* 197 */     Arrays.sort(arrayOfEntry, new Comparator<Map.Entry>() {
/*     */           public int compare(Object param1Object1, Object param1Object2) {
/* 199 */             double d1 = Double.parseDouble((String)((Map.Entry)param1Object1).getValue());
/* 200 */             double d2 = Double.parseDouble((String)((Map.Entry)param1Object2).getValue());
/* 201 */             return (new Double(d1)).compareTo(new Double(d2));
/*     */           }
/*     */         });
/* 204 */     return arrayOfEntry;
/*     */   }
/*     */   
/*     */   public String getFlowTypeName(String paramString) throws Exception {
/* 208 */     WorkTypeComInfo workTypeComInfo = new WorkTypeComInfo();
/* 209 */     WorkflowComInfo workflowComInfo = new WorkflowComInfo();
/* 210 */     paramString = WorkflowVersion.getActiveVersionWFID(paramString);
/* 211 */     return workTypeComInfo.getWorkTypename(workflowComInfo.getWorkflowtype(paramString));
/*     */   }
/*     */   
/*     */   public String getFlowName(String paramString) throws Exception {
/* 215 */     WorkflowComInfo workflowComInfo = new WorkflowComInfo();
/* 216 */     return workflowComInfo.getWorkflowname(paramString);
/*     */   }
/*     */   
/*     */   public String getOverTimeString(String paramString1, String paramString2) {
/* 220 */     String[] arrayOfString = paramString2.split("\\+");
/* 221 */     String str1 = arrayOfString[0].trim();
/* 222 */     String str2 = arrayOfString[1].trim();
/* 223 */     String str3 = arrayOfString[2].trim();
/* 224 */     String str4 = arrayOfString[3].trim();
/* 225 */     String str5 = arrayOfString[4].trim();
/* 226 */     String str6 = arrayOfString[5].trim();
/* 227 */     if ("".equals(str1) || "0".equals(str2)) str1 = TimeUtil.getCurrentTimeString(); 
/* 228 */     float f1 = (float)TimeUtil.timeInterval(str3, str1);
/* 229 */     float f2 = getEeceedOverTime(str4, str5, str6);
/* 230 */     float f3 = 0.0F;
/* 231 */     DecimalFormat decimalFormat = new DecimalFormat("0.00");
/* 232 */     if (f1 - f2 < 0.0F) {
/* 233 */       f3 = f1;
/*     */     } else {
/* 235 */       f3 = f1 - f2;
/*     */     } 
/* 237 */     return getSpendsString(decimalFormat.format((f3 / 3600.0F)));
/*     */   }
/*     */   
/*     */   public String getSpendsString(String paramString) {
/* 241 */     double d = Util.getDoubleValue(paramString);
/* 242 */     if (d <= 0.0D) {
/* 243 */       return "0" + SystemEnv.getHtmlLabelName(15049, ThreadVarLanguage.getLang()) + "";
/*     */     }
/* 245 */     int i = (int)d;
/* 246 */     int j = (int)((d - i) * 60.0D + 0.5D);
/* 247 */     StringBuilder stringBuilder = new StringBuilder();
/* 248 */     if (i >= 24) {
/* 249 */       stringBuilder.append(i / 24).append("" + SystemEnv.getHtmlLabelName(383378, ThreadVarLanguage.getLang()) + "");
/*     */     }
/* 251 */     if (i > 0) {
/* 252 */       stringBuilder.append(i % 24).append("" + SystemEnv.getHtmlLabelName(391, ThreadVarLanguage.getLang()) + "");
/*     */     }
/* 254 */     stringBuilder.append(j).append("" + SystemEnv.getHtmlLabelName(15049, ThreadVarLanguage.getLang()) + "");
/* 255 */     return stringBuilder.toString();
/*     */   }
/*     */   
/*     */   public String getFromSQL(String paramString) {
/* 259 */     StringBuilder stringBuilder = new StringBuilder();
/* 260 */     RecordSet recordSet = new RecordSet();
/* 261 */     if (recordSet.getDBType().equals("oracle")) {
/* 262 */       stringBuilder.append("(select requestid as id");
/* 263 */       stringBuilder.append(", workflowid");
/* 264 */       stringBuilder.append(", nodeid");
/* 265 */       stringBuilder.append(", userid");
/* 266 */       stringBuilder.append(", isremark");
/* 267 */       stringBuilder.append(", (select requestname from workflow_requestbase t2 where t2.requestid = t1.requestid)  requestname");
/* 268 */       stringBuilder.append(", (select destnodeid from workflow_requestlog where rownum=1 and requestid=t1.requestid and nodeid=t1.nodeid and operator=t1.userid and logtype in ('0', '2') and (operatedate || operatetime >= receivedate || receivetime) )  destnodeid");
/* 269 */       stringBuilder.append(", (operatedate||' '||operatetime) as operatedate");
/* 270 */       stringBuilder.append(", (receivedate||' '||receivetime) as receivedate");
/* 271 */       stringBuilder.append(" from workflow_currentoperator t1");
/* 272 */       stringBuilder.append(" where (isprocessed = '2' or isprocessed = '3') ").append(paramString);
/* 273 */       stringBuilder.append(" group by workflowid,requestid,nodeid,userid,operatedate,operatetime,receivedate,receivetime,isremark order by operatedate||operatetime asc )  temptab");
/* 274 */     } else if (recordSet.getDBType().equals("mysql")) {
/* 275 */       stringBuilder.append("(select requestid as id");
/* 276 */       stringBuilder.append(", workflowid");
/* 277 */       stringBuilder.append(", nodeid");
/* 278 */       stringBuilder.append(", userid");
/* 279 */       stringBuilder.append(", isremark");
/* 280 */       stringBuilder.append(", (select requestname from workflow_requestbase t2 where t2.requestid = t1.requestid) as requestname");
/* 281 */       stringBuilder.append(", (select destnodeid from workflow_requestlog where requestid=t1.requestid and nodeid=t1.nodeid and operator=t1.userid and logtype in('0','2') and operatedate+operatetime>=t1.receivedate+t1.receivetime order by operatedate+operatetime asc limit 1) as destnodeid");
/* 282 */       stringBuilder.append(", concat(operatedate,' ',operatetime) as operatedate");
/* 283 */       stringBuilder.append(", concat(receivedate,' ',receivetime) as receivedate");
/* 284 */       stringBuilder.append(" from workflow_currentoperator t1");
/* 285 */       stringBuilder.append(" where (isprocessed = '2' or isprocessed = '3') ").append(paramString);
/* 286 */       stringBuilder.append(" group by workflowid,requestid,nodeid,userid,operatedate,operatetime,receivedate,receivetime,isremark) as temptab");
/*     */     }
/* 288 */     else if (recordSet.getDBType().equals("postgresql")) {
/* 289 */       stringBuilder.append("(select requestid as id");
/* 290 */       stringBuilder.append(", workflowid");
/* 291 */       stringBuilder.append(", nodeid");
/* 292 */       stringBuilder.append(", userid");
/* 293 */       stringBuilder.append(", isremark");
/* 294 */       stringBuilder.append(", (select requestname from workflow_requestbase t2 where t2.requestid = t1.requestid) as requestname");
/* 295 */       stringBuilder.append(", (select destnodeid from workflow_requestlog where requestid=t1.requestid and nodeid=t1.nodeid and operator=t1.userid and logtype in('0','2') and operatedate+operatetime>=t1.receivedate+t1.receivetime order by operatedate+operatetime asc limit 1) as destnodeid");
/* 296 */       stringBuilder.append(", concat(operatedate,' ',operatetime) as operatedate");
/* 297 */       stringBuilder.append(", concat(receivedate,' ',receivetime) as receivedate");
/* 298 */       stringBuilder.append(" from workflow_currentoperator t1");
/* 299 */       stringBuilder.append(" where (isprocessed = '2' or isprocessed = '3') ").append(paramString);
/* 300 */       stringBuilder.append(" group by workflowid,requestid,nodeid,userid,operatedate,operatetime,receivedate,receivetime,isremark) as temptab");
/*     */     } else {
/*     */       
/* 303 */       stringBuilder.append("(select requestid as id");
/* 304 */       stringBuilder.append(", workflowid");
/* 305 */       stringBuilder.append(", nodeid");
/* 306 */       stringBuilder.append(", userid");
/* 307 */       stringBuilder.append(", isremark");
/* 308 */       stringBuilder.append(", (select requestname from workflow_requestbase t2 where t2.requestid = t1.requestid) as requestname");
/* 309 */       stringBuilder.append(", (select top 1 destnodeid from workflow_requestlog where requestid=t1.requestid and nodeid=t1.nodeid and operator=t1.userid and logtype in('0','2') and operatedate+operatetime>=t1.receivedate+t1.receivetime order by operatedate+operatetime asc) as destnodeid");
/* 310 */       stringBuilder.append(", (operatedate+' '+operatetime) as operatedate");
/* 311 */       stringBuilder.append(", (receivedate+' '+receivetime) as receivedate");
/* 312 */       stringBuilder.append(" from workflow_currentoperator t1");
/* 313 */       stringBuilder.append(" where (isprocessed = '2' or isprocessed = '3') ").append(paramString);
/* 314 */       stringBuilder.append(" group by workflowid,requestid,nodeid,userid,operatedate,operatetime,receivedate,receivetime,isremark) as temptab");
/*     */     } 
/*     */     
/* 317 */     return stringBuilder.toString();
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/report/MostExceedFlow.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */