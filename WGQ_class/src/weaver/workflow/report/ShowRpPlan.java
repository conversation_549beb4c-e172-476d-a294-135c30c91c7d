/*     */ package weaver.workflow.report;
/*     */ 
/*     */ import java.awt.Color;
/*     */ import java.awt.Graphics2D;
/*     */ import java.awt.image.BufferedImage;
/*     */ import java.io.BufferedOutputStream;
/*     */ import java.io.IOException;
/*     */ import java.io.OutputStream;
/*     */ import java.util.ArrayList;
/*     */ import javax.servlet.ServletException;
/*     */ import javax.servlet.ServletOutputStream;
/*     */ import javax.servlet.http.HttpServlet;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.ThreadVarLanguage;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class ShowRpPlan
/*     */   extends HttpServlet
/*     */ {
/*  30 */   private int width = 320;
/*  31 */   private int height = 320;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void doGet(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws ServletException, IOException {
/*  42 */     RecordSet recordSet = new RecordSet();
/*  43 */     String str1 = Util.null2String(paramHttpServletRequest.getParameter("resourceid"));
/*  44 */     String str2 = Util.null2String(paramHttpServletRequest.getParameter("accepterid"));
/*  45 */     String str3 = Util.null2String(paramHttpServletRequest.getParameter("startdate"));
/*  46 */     String str4 = Util.null2String(paramHttpServletRequest.getParameter("enddate"));
/*     */ 
/*     */ 
/*     */     
/*     */     try {
/*  51 */       ServletOutputStream servletOutputStream = paramHttpServletResponse.getOutputStream();
/*  52 */       BufferedOutputStream bufferedOutputStream = new BufferedOutputStream((OutputStream)servletOutputStream);
/*     */       
/*  54 */       BufferedImage bufferedImage = new BufferedImage(this.width, this.height, 1);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/*  63 */       Graphics2D graphics2D = bufferedImage.createGraphics();
/*     */       
/*  65 */       graphics2D.setColor(Color.white);
/*  66 */       graphics2D.fillRect(0, 0, this.width, this.height);
/*     */       
/*  68 */       byte b1 = 10;
/*  69 */       char c = 'Ĭ';
/*  70 */       byte b2 = 10;
/*     */ 
/*     */ 
/*     */ 
/*     */       
/*  75 */       byte b3 = 0;
/*  76 */       ArrayList<String> arrayList1 = new ArrayList();
/*  77 */       ArrayList<String> arrayList2 = new ArrayList();
/*  78 */       ArrayList<Color> arrayList = new ArrayList();
/*     */       
/*  80 */       arrayList.add(Color.pink);
/*  81 */       arrayList.add(Color.cyan);
/*  82 */       arrayList.add(Color.yellow);
/*  83 */       arrayList.add(Color.green);
/*     */       
/*  85 */       String str = "select * from bill_HrmTime where resourceid != accepterid and enddate >='" + str3 + "' and enddate <='" + str4 + "'";
/*  86 */       if (!str1.equals("0"))
/*  87 */         str = str + " and resourceid=" + str1; 
/*  88 */       if (!str2.equals("0")) {
/*  89 */         str = str + " and accepterid=" + str2;
/*     */       }
/*     */       
/*  92 */       byte b4 = 0;
/*  93 */       byte b5 = 0;
/*  94 */       byte b6 = 0;
/*  95 */       byte b7 = 0;
/*     */       
/*  97 */       recordSet.executeSql(str);
/*  98 */       while (recordSet.next()) {
/*  99 */         String str5 = Util.null2String(recordSet.getString("customizestr1"));
/* 100 */         String str6 = Util.null2String(recordSet.getString("enddate"));
/* 101 */         if (str5.equals("")) {
/* 102 */           b4++;
/* 103 */         } else if (str6.compareTo(str5) > 0) {
/* 104 */           b5++;
/* 105 */         } else if (str6.compareTo(str5) == 0) {
/* 106 */           b6++;
/* 107 */         } else if (str6.compareTo(str5) < 0) {
/* 108 */           b7++;
/* 109 */         }  b3++;
/*     */       } 
/* 111 */       if (b3 != 0) {
/* 112 */         arrayList2.add("" + (b4 * 360 / b3));
/* 113 */         arrayList2.add("" + (b5 * 360 / b3));
/* 114 */         arrayList2.add("" + (b6 * 360 / b3));
/* 115 */         arrayList2.add("" + (b7 * 360 / b3));
/*     */         
/* 117 */         arrayList1.add("" + Util.fromScreen("" + SystemEnv.getHtmlLabelName(732, ThreadVarLanguage.getLang()) + "", 7) + "(" + b4 + ")");
/* 118 */         arrayList1.add("" + Util.fromScreen("" + SystemEnv.getHtmlLabelName(10004287, ThreadVarLanguage.getLang()) + "", 7) + "(" + b5 + ")");
/* 119 */         arrayList1.add("" + Util.fromScreen("" + SystemEnv.getHtmlLabelName(10004288, ThreadVarLanguage.getLang()) + "", 7) + "(" + b6 + ")");
/* 120 */         arrayList1.add("" + Util.fromScreen("" + SystemEnv.getHtmlLabelName(10004289, ThreadVarLanguage.getLang()) + "", 7) + "(" + b7 + ")");
/*     */       } 
/*     */       
/* 123 */       int i = 0;
/* 124 */       for (byte b8 = 0; b8 < arrayList1.size(); b8++) {
/* 125 */         int j = Util.getIntValue("" + arrayList2.get(b8), 0);
/* 126 */         if (j != 0) {
/*     */           
/* 128 */           graphics2D.setColor(arrayList.get(b8));
/* 129 */           graphics2D.fillArc(b1, b2, c, c, i, j);
/*     */           
/* 131 */           int k = (int)(Math.cos(Math.toRadians(((j + i + i) / 2))) * (2 * c / 5));
/* 132 */           int m = (int)(Math.sin(Math.toRadians(((j + i + i) / 2))) * (2 * c / 5));
/*     */           
/* 134 */           graphics2D.setColor(Color.black);
/* 135 */           graphics2D.drawString("" + arrayList1.get(b8), c / 2 + k - 10, c / 2 - m);
/* 136 */           i += j;
/*     */         } 
/*     */       } 
/*     */       
/* 140 */       graphics2D.dispose();
/*     */ 
/*     */ 
/*     */       
/* 144 */       servletOutputStream.flush();
/* 145 */       bufferedOutputStream.close();
/* 146 */       servletOutputStream.close();
/*     */ 
/*     */     
/*     */     }
/* 150 */     catch (Exception exception) {}
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/report/ShowRpPlan.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */