/*     */ package weaver.workflow.report;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.Collections;
/*     */ import weaver.cache.CacheBase;
/*     */ import weaver.cache.CacheColumn;
/*     */ import weaver.cache.CacheColumnType;
/*     */ import weaver.cache.PKColumn;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class ReportComInfo
/*     */   extends CacheBase
/*     */ {
/*  29 */   protected static String TABLE_NAME = "Workflow_Report";
/*     */   
/*  31 */   protected static String TABLE_WHERE = null;
/*     */   
/*  33 */   protected static String TABLE_ORDER = null;
/*     */   
/*     */   @PKColumn(type = CacheColumnType.NUMBER)
/*  36 */   protected static String PK_NAME = "id";
/*     */ 
/*     */   
/*     */   @CacheColumn
/*     */   protected static int reportname;
/*     */   
/*     */   @CacheColumn
/*     */   protected static int reporttype;
/*     */   
/*     */   @CacheColumn
/*     */   protected static int reportwfid;
/*     */   
/*     */   @CacheColumn
/*     */   protected static int formid;
/*     */   
/*     */   @CacheColumn
/*     */   protected static int isbill;
/*     */ 
/*     */   
/*     */   public int getReportTypeNum() {
/*  56 */     return size();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public boolean next(String paramString) {
/*  66 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getReportid() {
/*  74 */     return (String)getRowValue(0);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getReportTypeid() {
/*  83 */     return (String)getRowValue(reporttype);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getReportname() {
/*  92 */     return (String)getRowValue(reportname);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getReportname(String paramString) {
/* 101 */     return (String)getValue(reportname, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getReportTypeid(String paramString) {
/* 110 */     return (String)getValue(reporttype, paramString);
/*     */   }
/*     */   
/*     */   public String getReportWfid() {
/* 114 */     return (String)getRowValue(reportwfid);
/*     */   }
/*     */   
/*     */   public String getReportWfid(String paramString) {
/* 118 */     return (String)getValue(reportwfid, paramString);
/*     */   }
/*     */   
/*     */   public String getFormid() {
/* 122 */     return (String)getRowValue(formid);
/*     */   }
/*     */   
/*     */   public String getFormid(String paramString) {
/* 126 */     return (String)getValue(formid, paramString);
/*     */   }
/*     */   
/*     */   public String getIsBill() {
/* 130 */     return (String)getRowValue(isbill);
/*     */   }
/*     */   
/*     */   public String getIsBill(String paramString) {
/* 134 */     return (String)getValue(isbill, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void removeReportTypeCache() {
/* 141 */     removeCache();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCompositorOrderByStrs(ArrayList<?> paramArrayList) {
/* 151 */     String str1 = "";
/* 152 */     String str2 = "";
/* 153 */     ArrayList<?> arrayList = new ArrayList();
/* 154 */     ReportCompositorOrderBean reportCompositorOrderBean = new ReportCompositorOrderBean();
/* 155 */     if (paramArrayList.size() > 0) {
/* 156 */       Collections.sort(paramArrayList, new CompositorOrderComparator());
/* 157 */       arrayList = paramArrayList;
/* 158 */       for (byte b = 0; b < arrayList.size(); b++) {
/* 159 */         reportCompositorOrderBean = (ReportCompositorOrderBean)arrayList.get(b);
/* 160 */         str2 = "d".equals(reportCompositorOrderBean.getOrderType()) ? "desc" : "asc";
/* 161 */         str1 = str1 + reportCompositorOrderBean.getSqlFlag() + "." + reportCompositorOrderBean.getFieldName() + " " + str2 + ",";
/*     */       } 
/* 163 */       return " order by " + str1.trim().substring(0, str1.length() - 1);
/*     */     } 
/* 165 */     return "";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCompositorListByStrs(ArrayList<?> paramArrayList) {
/* 175 */     String str1 = "";
/* 176 */     String str2 = "";
/* 177 */     ArrayList<?> arrayList = new ArrayList();
/* 178 */     ReportCompositorListBean reportCompositorListBean = new ReportCompositorListBean();
/* 179 */     if (paramArrayList.size() > 0) {
/* 180 */       String str3 = "";
/* 181 */       Collections.sort(paramArrayList, new CompositorListComparator());
/* 182 */       arrayList = paramArrayList;
/*     */       
/* 184 */       boolean bool = false;
/* 185 */       RecordSet recordSet = new RecordSet();
/* 186 */       String str4 = Util.null2String(recordSet.getDBType());
/* 187 */       if (str4.equals("oracle") || str4.equals("db2")) {
/* 188 */         bool = true;
/*     */       }
/*     */       
/* 191 */       for (byte b = 0; b < arrayList.size(); b++) {
/* 192 */         reportCompositorListBean = (ReportCompositorListBean)arrayList.get(b);
/* 193 */         str3 = "-17".equals(reportCompositorListBean.getHtmlType()) ? "l" : reportCompositorListBean.getSqlFlag();
/* 194 */         if (bool) {
/* 195 */           if (reportCompositorListBean.getFieldName().indexOf(" as ") > -1) {
/* 196 */             str1 = str1 + reportCompositorListBean.getSqlFlag() + "." + reportCompositorListBean.getFieldName() + ",";
/*     */           } else {
/* 198 */             str1 = str1 + reportCompositorListBean.getSqlFlag() + "." + reportCompositorListBean.getFieldName() + " as " + str3 + "_" + reportCompositorListBean.getFieldName() + ",";
/*     */           }
/*     */         
/* 201 */         } else if (reportCompositorListBean.getFieldName().indexOf(" as ") > -1) {
/* 202 */           str1 = str1 + reportCompositorListBean.getSqlFlag() + "." + reportCompositorListBean.getFieldName() + ",";
/*     */         } else {
/* 204 */           str1 = str1 + reportCompositorListBean.getSqlFlag() + "." + reportCompositorListBean.getFieldName() + " as " + str3 + "_" + reportCompositorListBean.getFieldName() + ",";
/*     */         } 
/*     */       } 
/*     */       
/* 208 */       return str1.trim().substring(0, str1.length() - 1);
/*     */     } 
/* 210 */     return "*";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public ArrayList getCompositorList(ArrayList<?> paramArrayList) {
/* 220 */     ArrayList<?> arrayList = new ArrayList();
/* 221 */     if (paramArrayList.size() > 0) {
/* 222 */       Collections.sort(paramArrayList, new CompositorListComparator());
/* 223 */       arrayList = paramArrayList;
/*     */     } 
/* 225 */     return arrayList;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/report/ReportComInfo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */