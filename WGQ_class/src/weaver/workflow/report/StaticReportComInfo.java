/*     */ package weaver.workflow.report;
/*     */ 
/*     */ import weaver.cache.CacheBase;
/*     */ import weaver.cache.CacheColumn;
/*     */ import weaver.cache.CacheColumnType;
/*     */ import weaver.cache.PKColumn;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class StaticReportComInfo
/*     */   extends CacheBase
/*     */ {
/*  24 */   protected static String TABLE_NAME = "workflow_StaticRpbase";
/*     */   
/*  26 */   protected static String TABLE_WHERE = null;
/*     */   
/*  28 */   protected static String TABLE_ORDER = "id";
/*     */   
/*     */   @PKColumn(type = CacheColumnType.NUMBER)
/*  31 */   protected static String PK_NAME = "id";
/*     */   
/*     */   @CacheColumn
/*     */   protected static int name;
/*     */   
/*     */   @CacheColumn
/*     */   protected static int description;
/*     */   
/*     */   @CacheColumn
/*     */   protected static int pagename;
/*     */   
/*     */   @CacheColumn
/*     */   protected static int module;
/*     */   
/*     */   @CacheColumn
/*     */   protected static int reportid;
/*     */ 
/*     */   
/*     */   public int getReportTypeNum() {
/*  50 */     return size();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public boolean next(String paramString) {
/*  60 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getId() {
/*  69 */     return (String)getRowValue(0);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getName() {
/*  78 */     return (String)getRowValue(name);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getName(String paramString) {
/*  89 */     return (String)getValue(name, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getDescription() {
/*  98 */     return (String)getRowValue(description);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getDescription(String paramString) {
/* 109 */     return (String)getValue(description, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getPagename() {
/* 118 */     return (String)getRowValue(pagename);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getPagename(String paramString) {
/* 129 */     return (String)getValue(pagename, paramString);
/*     */   }
/*     */   
/*     */   public String getModule() {
/* 133 */     return (String)getRowValue(module);
/*     */   }
/*     */   
/*     */   public String getModule(String paramString) {
/* 137 */     return (String)getValue(module, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getReportid() {
/* 146 */     return (String)getRowValue(reportid);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getReportid(String paramString) {
/* 156 */     return (String)getValue(reportid, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void removeStaticReportCache() {
/* 163 */     removeCache();
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/report/StaticReportComInfo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */