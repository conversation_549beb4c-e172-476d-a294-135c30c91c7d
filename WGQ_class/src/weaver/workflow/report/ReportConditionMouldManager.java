/*    */ package weaver.workflow.report;
/*    */ 
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.BaseBean;
/*    */ import weaver.general.Util;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class ReportConditionMouldManager
/*    */   extends BaseBean
/*    */ {
/* 26 */   private int mouldId = -1;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public synchronized int getNextMouldId() {
/* 40 */     RecordSet recordSet = new RecordSet();
/*    */     
/*    */     try {
/* 43 */       recordSet.executeProc("SequenceIndex_SelectNextID", "WorkflowRptCondMouldId");
/* 44 */       if (recordSet.next())
/*    */       {
/* 46 */         this.mouldId = Util.getIntValue(recordSet.getString(1), 0);
/*    */       }
/*    */     }
/* 49 */     catch (Exception exception) {
/*    */       
/* 51 */       writeLog(exception.toString() + "新建报表条件模板时无法生成新的模板ID!!!");
/* 52 */       this.mouldId = -1;
/*    */     } 
/*    */     
/* 55 */     return this.mouldId;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/report/ReportConditionMouldManager.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */