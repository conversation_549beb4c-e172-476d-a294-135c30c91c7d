/*     */ package weaver.workflow.report;
/*     */ 
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.conn.RecordSetTrans;
/*     */ import weaver.general.BaseBean;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class RequestDeleteLog
/*     */   extends BaseBean
/*     */ {
/*     */   private String requestId;
/*     */   private String requestName;
/*     */   private String operateUserId;
/*     */   private String operateDate;
/*     */   private String operateTime;
/*     */   private String workflowId;
/*     */   private String clientAddress;
/*     */   private String operateUserType;
/*     */   
/*     */   public boolean save() {
/*  23 */     return save(new RecordSet());
/*     */   }
/*     */   
/*     */   public boolean save(RecordSet paramRecordSet) {
/*  27 */     return paramRecordSet.executeSql(createSQL());
/*     */   }
/*     */   
/*     */   public boolean save(RecordSetTrans paramRecordSetTrans) throws Exception {
/*  31 */     return paramRecordSetTrans.executeSql(createSQL());
/*     */   }
/*     */   
/*     */   public String getRequestId() {
/*  35 */     return this.requestId;
/*     */   }
/*     */   
/*     */   public void setRequestId(String paramString) {
/*  39 */     this.requestId = paramString;
/*     */   }
/*     */   
/*     */   public String getRequestName() {
/*  43 */     return this.requestName;
/*     */   }
/*     */   
/*     */   public void setRequestName(String paramString) {
/*  47 */     this.requestName = paramString;
/*     */   }
/*     */   
/*     */   public String getOperateUserId() {
/*  51 */     return this.operateUserId;
/*     */   }
/*     */   
/*     */   public void setOperateUserId(String paramString) {
/*  55 */     this.operateUserId = paramString;
/*     */   }
/*     */   
/*     */   public void setOperateUserType(String paramString) {
/*  59 */     this.operateUserType = paramString;
/*     */   }
/*     */   
/*     */   public String getOperateUserType() {
/*  63 */     return this.operateUserType;
/*     */   }
/*     */   
/*     */   public String getOperateDate() {
/*  67 */     return this.operateDate;
/*     */   }
/*     */   
/*     */   public void setOperateDate(String paramString) {
/*  71 */     this.operateDate = paramString;
/*     */   }
/*     */   
/*     */   public String getOperateTime() {
/*  75 */     return this.operateTime;
/*     */   }
/*     */   
/*     */   public void setOperateTime(String paramString) {
/*  79 */     this.operateTime = paramString;
/*     */   }
/*     */   
/*     */   public String getWorkflowId() {
/*  83 */     return this.workflowId;
/*     */   }
/*     */   
/*     */   public void setWorkflowId(String paramString) {
/*  87 */     this.workflowId = paramString;
/*     */   }
/*     */   
/*     */   public String getClientAddress() {
/*  91 */     return this.clientAddress;
/*     */   }
/*     */   
/*     */   public void setClientAddress(String paramString) {
/*  95 */     this.clientAddress = paramString;
/*     */   }
/*     */   
/*     */   private String createSQL() {
/*  99 */     StringBuilder stringBuilder = new StringBuilder();
/* 100 */     stringBuilder.append("INSERT INTO workflow_requestdeletelog(request_id,request_name,operate_userid,operate_usertype,operate_date,operate_time,workflow_id,client_address) VALUES(");
/* 101 */     stringBuilder.append("'").append(transSQL(getRequestId())).append("'");
/* 102 */     stringBuilder.append(",'").append(transSQL(getRequestName())).append("'");
/* 103 */     stringBuilder.append(",'").append(transSQL(getOperateUserId())).append("'");
/* 104 */     stringBuilder.append(",'").append(transSQL(getOperateUserType())).append("'");
/* 105 */     stringBuilder.append(",'").append(transSQL(getOperateDate())).append("'");
/* 106 */     stringBuilder.append(",'").append(transSQL(getOperateTime())).append("'");
/* 107 */     stringBuilder.append(",'").append(transSQL(getWorkflowId())).append("'");
/* 108 */     stringBuilder.append(",'").append(transSQL(getClientAddress())).append("'");
/* 109 */     stringBuilder.append(")");
/* 110 */     return stringBuilder.toString();
/*     */   }
/*     */   
/*     */   private String transSQL(String paramString) {
/* 114 */     if (paramString == null) {
/* 115 */       return "";
/*     */     }
/* 117 */     return paramString.replace("'", "''");
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/report/RequestDeleteLog.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */