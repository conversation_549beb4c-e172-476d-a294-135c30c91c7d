/*     */ package weaver.workflow.report;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.List;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.company.DepartmentComInfo;
/*     */ import weaver.hrm.company.SubCompanyComInfo;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class UserPendingSort
/*     */   extends BaseBean
/*     */ {
/*  34 */   private DepartmentComInfo departmentcominfo = new DepartmentComInfo();
/*  35 */   private ResourceComInfo resourcecominfo = new ResourceComInfo();
/*  36 */   private ReportShare MDCompanyNameInfo = new ReportShare();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List getUserSort(String paramString) throws Exception {
/*  46 */     RecordSet recordSet = new RecordSet();
/*  47 */     ArrayList<String> arrayList1 = new ArrayList();
/*  48 */     ArrayList<String> arrayList2 = new ArrayList();
/*  49 */     ArrayList<String> arrayList3 = new ArrayList();
/*  50 */     ArrayList<String> arrayList4 = new ArrayList();
/*  51 */     ArrayList<String> arrayList5 = new ArrayList();
/*  52 */     ArrayList<ArrayList<String>> arrayList = new ArrayList();
/*  53 */     byte b = 1;
/*  54 */     if (recordSet.getDBType().equals("oracle") || recordSet.getDBType().equals("mysql") || recordSet.getDBType().equals("postgresql")) {
/*  55 */       String str = "SELECT userid, COUNT(requestid) AS Expr1 FROM workflow_currentoperator WHERE workflowtype > 0 and (isremark IN ('0', '1', '5','8','9','7')) AND (islasttimes = 1) AND (usertype = 0)  and exists (select 1 from hrmresource where hrmresource.id=workflow_currentoperator.userid and hrmresource.status in (0,1,2,3) ) AND EXISTS (SELECT 1 FROM workflow_base WHERE id = workflow_currentoperator.workflowid AND (isvalid='1' or isvalid='3'))  and exists (select 1 from workflow_requestbase where requestid = workflow_currentoperator.requestid and (deleted=0 or deleted is null)) " + paramString + " GROUP BY userid ORDER BY COUNT(requestid) desc,userid asc";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/*  63 */       recordSet.executeSql(str);
/*     */     } else {
/*  65 */       recordSet.executeProc("WorkFlowPending_Get", paramString);
/*     */     } 
/*  67 */     while (recordSet.next()) {
/*  68 */       int i = 1;
/*  69 */       int j = 1;
/*  70 */       String str1 = recordSet.getString(1);
/*  71 */       String str2 = recordSet.getString(2);
/*  72 */       String str3 = this.resourcecominfo.getDepartmentID(str1);
/*  73 */       String str4 = this.departmentcominfo.getSubcompanyid1(str3); int k;
/*  74 */       for (k = arrayList2.size() - 1; k >= 0; k--) {
/*     */         
/*  76 */         String str5 = "" + arrayList2.get(k);
/*  77 */         String str6 = str5.substring(str5.indexOf("$") + 1);
/*  78 */         if (str6.equals(str3)) {
/*     */           
/*  80 */           i = Util.getIntValue(str5.substring(0, str5.indexOf("$")), 1) + 1;
/*     */           
/*     */           break;
/*     */         } 
/*     */       } 
/*     */       
/*  86 */       for (k = arrayList3.size() - 1; k >= 0; k--) {
/*     */         
/*  88 */         String str5 = "" + arrayList3.get(k);
/*  89 */         String str6 = str5.substring(str5.indexOf("$") + 1);
/*  90 */         if (str6.equals(str4)) {
/*     */           
/*  92 */           j = Util.getIntValue(str5.substring(0, str5.indexOf("$")), 1) + 1;
/*     */           
/*     */           break;
/*     */         } 
/*     */       } 
/*     */       
/*  98 */       arrayList2.add("" + i + "$" + str3);
/*  99 */       arrayList3.add("" + j + "$" + str4);
/* 100 */       arrayList4.add(str1);
/* 101 */       arrayList5.add(str2);
/* 102 */       arrayList1.add("" + b);
/* 103 */       b++;
/*     */     } 
/* 105 */     arrayList.add(arrayList1);
/* 106 */     arrayList.add(arrayList4);
/* 107 */     arrayList.add(arrayList5);
/* 108 */     arrayList.add(arrayList2);
/* 109 */     arrayList.add(arrayList3);
/* 110 */     return arrayList;
/*     */   }
/*     */   
/*     */   public String getDepartmentName(String paramString) {
/* 114 */     return this.departmentcominfo.getDepartmentname(this.resourcecominfo.getDepartmentID(paramString));
/*     */   }
/*     */   
/*     */   public String getSubCompanyName(String paramString) throws Exception {
/* 118 */     SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/* 119 */     return subCompanyComInfo.getSubCompanyname(this.resourcecominfo.getSubCompanyID(paramString));
/*     */   }
/*     */   
/*     */   public String getDepartmentSort(String paramString1, String paramString2) {
/* 123 */     StringBuilder stringBuilder = new StringBuilder();
/* 124 */     RecordSet recordSet = new RecordSet();
/* 125 */     stringBuilder.append("select id from ").append(Util.toSqlForSplitPage(paramString2)).append(" order by counts desc");
/* 126 */     recordSet.executeSql(stringBuilder.toString());
/* 127 */     String str = this.resourcecominfo.getDepartmentID(paramString1);
/* 128 */     long l = 0L;
/* 129 */     while (recordSet.next()) {
/* 130 */       String str1 = recordSet.getString(1);
/* 131 */       String str2 = this.resourcecominfo.getDepartmentID(str1);
/* 132 */       if (str.equals(str2)) {
/* 133 */         l++;
/* 134 */         if (paramString1.equals(str1)) {
/*     */           break;
/*     */         }
/*     */       } 
/*     */     } 
/* 139 */     return String.valueOf(l);
/*     */   }
/*     */   
/*     */   public String getSubCompanySort(String paramString1, String paramString2) {
/* 143 */     StringBuilder stringBuilder = new StringBuilder();
/* 144 */     stringBuilder.append("select id from ").append(Util.toSqlForSplitPage(paramString2)).append(" order by counts desc");
/* 145 */     RecordSet recordSet = new RecordSet();
/* 146 */     recordSet.executeSql(stringBuilder.toString());
/* 147 */     String str1 = this.resourcecominfo.getDepartmentID(paramString1);
/* 148 */     String str2 = this.departmentcominfo.getSubcompanyid1(str1);
/* 149 */     long l = 0L;
/* 150 */     while (recordSet.next()) {
/* 151 */       String str3 = recordSet.getString(1);
/* 152 */       String str4 = this.resourcecominfo.getDepartmentID(str3);
/* 153 */       String str5 = this.departmentcominfo.getSubcompanyid1(str4);
/* 154 */       if (str2.equals(str5)) {
/* 155 */         l++;
/* 156 */         if (paramString1.equals(str3)) {
/*     */           break;
/*     */         }
/*     */       } 
/*     */     } 
/* 161 */     return String.valueOf(l);
/*     */   }
/*     */   
/*     */   public String getFromSQL(String paramString) {
/* 165 */     StringBuilder stringBuilder = new StringBuilder();
/* 166 */     RecordSet recordSet = new RecordSet();
/* 167 */     if (recordSet.getDBType().equals("oracle")) {
/* 168 */       stringBuilder.append("(SELECT userid as id");
/* 169 */       stringBuilder.append(", COUNT(requestid) AS counts");
/* 170 */       stringBuilder.append(" FROM workflow_currentoperator");
/* 171 */       stringBuilder.append(" WHERE workflowtype > 0 and (isremark IN ('0', '1', '5','8','9','7'))");
/* 172 */       stringBuilder.append(" AND (islasttimes = 1) AND (usertype = 0)");
/* 173 */       stringBuilder.append(" and exists (select 1 from hrmresource where hrmresource.id=workflow_currentoperator.userid and hrmresource.status in (0,1,2,3))");
/* 174 */       stringBuilder.append(" AND EXISTS (SELECT 1 FROM workflow_base WHERE id = workflow_currentoperator.workflowid AND (isvalid='1' or isvalid='3'))");
/* 175 */       stringBuilder.append(" and exists (select 1 from workflow_requestbase where requestid = workflow_currentoperator.requestid and (deleted=0 or deleted is null)) ");
/* 176 */       stringBuilder.append(paramString);
/* 177 */       stringBuilder.append(" GROUP BY userid)  temptab");
/* 178 */     } else if (recordSet.getDBType().equals("mysql")) {
/* 179 */       stringBuilder.append("(SELECT userid as id");
/* 180 */       stringBuilder.append(", COUNT(requestid) AS counts");
/* 181 */       stringBuilder.append(" FROM workflow_currentoperator");
/* 182 */       stringBuilder.append(" WHERE workflowtype > 0 and (isremark IN ('0', '1', '5','8','9','7'))");
/* 183 */       stringBuilder.append(" AND (islasttimes = 1) AND (usertype = 0)");
/* 184 */       stringBuilder.append(" and exists (select 1 from hrmresource where hrmresource.id=workflow_currentoperator.userid and hrmresource.status in (0,1,2,3))");
/* 185 */       stringBuilder.append(" AND EXISTS (SELECT 1 FROM workflow_base WHERE id = workflow_currentoperator.workflowid AND (isvalid='1' or isvalid='3'))");
/* 186 */       stringBuilder.append(" and exists (select 1 from workflow_requestbase where requestid = workflow_currentoperator.requestid and (deleted=0 or deleted is null)) ");
/* 187 */       stringBuilder.append(paramString);
/* 188 */       stringBuilder.append(" GROUP BY userid)  temptab");
/*     */     }
/* 190 */     else if (recordSet.getDBType().equals("postgresql")) {
/* 191 */       stringBuilder.append("(SELECT userid as id");
/* 192 */       stringBuilder.append(", COUNT(requestid) AS counts");
/* 193 */       stringBuilder.append(" FROM workflow_currentoperator");
/* 194 */       stringBuilder.append(" WHERE workflowtype > 0 and (isremark IN ('0', '1', '5','8','9','7'))");
/* 195 */       stringBuilder.append(" AND (islasttimes = 1) AND (usertype = 0)");
/* 196 */       stringBuilder.append(" and exists (select 1 from hrmresource where hrmresource.id=workflow_currentoperator.userid and hrmresource.status in (0,1,2,3))");
/* 197 */       stringBuilder.append(" AND EXISTS (SELECT 1 FROM workflow_base WHERE id = workflow_currentoperator.workflowid AND (isvalid='1' or isvalid='3'))");
/* 198 */       stringBuilder.append(" and exists (select 1 from workflow_requestbase where requestid = workflow_currentoperator.requestid and (deleted=0 or deleted is null)) ");
/* 199 */       stringBuilder.append(paramString);
/* 200 */       stringBuilder.append(" GROUP BY userid)  temptab");
/*     */     } else {
/*     */       
/* 203 */       stringBuilder.append("(SELECT TOP 100 PERCENT userid as id");
/* 204 */       stringBuilder.append(", COUNT(requestid) AS counts");
/* 205 */       stringBuilder.append(" FROM  workflow_currentoperator");
/* 206 */       stringBuilder.append(" WHERE workflowtype > 0 and isremark IN ('0', '1', '5','8','9','7')");
/* 207 */       stringBuilder.append(" AND islasttimes = 1 AND usertype = '0'");
/* 208 */       stringBuilder.append(" and exists (select 1 from hrmresource where hrmresource.id=workflow_currentoperator.userid and hrmresource.status in (0,1,2,3))");
/* 209 */       stringBuilder.append(" and exists (select 1 from workflow_requestbase where requestid = workflow_currentoperator.requestid and (deleted=0 or deleted is null))");
/* 210 */       stringBuilder.append(" AND EXISTS (SELECT 1 FROM workflow_base WHERE id = workflow_currentoperator.workflowid AND (isvalid='1' or isvalid='3')) ");
/* 211 */       stringBuilder.append(paramString);
/* 212 */       stringBuilder.append(" GROUP BY userid) as temptab");
/*     */     } 
/*     */     
/* 215 */     return stringBuilder.toString();
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/report/UserPendingSort.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */