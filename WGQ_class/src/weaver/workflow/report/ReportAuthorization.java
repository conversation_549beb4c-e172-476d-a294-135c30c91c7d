/*      */ package weaver.workflow.report;
/*      */ 
/*      */ import cn.hutool.core.util.ObjectUtil;
/*      */ import com.engine.common.service.impl.HrmCommonServiceImpl;
/*      */ import java.util.ArrayList;
/*      */ import java.util.Collections;
/*      */ import java.util.HashMap;
/*      */ import java.util.List;
/*      */ import java.util.Map;
/*      */ import javax.servlet.http.HttpServlet;
/*      */ import javax.servlet.http.HttpServletRequest;
/*      */ import weaver.conn.RecordSet;
/*      */ import weaver.general.Util;
/*      */ import weaver.hrm.User;
/*      */ import weaver.hrm.company.DepartmentComInfo;
/*      */ import weaver.hrm.companyvirtual.DepartmentVirtualComInfo;
/*      */ import weaver.hrm.companyvirtual.ResourceVirtualComInfo;
/*      */ import weaver.hrm.resource.ResourceComInfo;
/*      */ import weaver.workflow.workflow.WorkflowVersion;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ public class ReportAuthorization
/*      */   extends HttpServlet
/*      */ {
/*   32 */   private ReportShare reportShare = null;
/*   33 */   private ResourceComInfo rscominfo = null;
/*   34 */   private ResourceVirtualComInfo rsvcominfo = null;
/*   35 */   private DepartmentComInfo deptcominfo = null;
/*   36 */   private DepartmentVirtualComInfo deptvcominfo = null;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean checkUserReportPrivileges(String paramString1, String paramString2, User paramUser) throws Exception {
/*   43 */     boolean bool = false;
/*   44 */     RecordSet recordSet = new RecordSet();
/*   45 */     if (checkReportByUserId(paramString1, paramString2, paramUser)) {
/*   46 */       String str1 = "";
/*   47 */       String str2 = "";
/*   48 */       recordSet.executeSql("SELECT reportwfid FROM Workflow_Report WHERE id = " + paramString1);
/*   49 */       while (recordSet.next()) {
/*   50 */         str2 = recordSet.getString("reportwfid");
/*      */       }
/*      */ 
/*      */       
/*   54 */       String str3 = "";
/*   55 */       if (str2.indexOf(",") > -1) {
/*   56 */         String[] arrayOfString = Util.TokenizerString2(str2, ",");
/*   57 */         for (byte b = 0; b < arrayOfString.length; b++) {
/*   58 */           if ("".equals(str3)) {
/*   59 */             str3 = WorkflowVersion.getAllVersionStringByWFIDs(arrayOfString[b]);
/*      */           } else {
/*   61 */             str3 = str3 + "," + WorkflowVersion.getAllVersionStringByWFIDs(arrayOfString[b]);
/*      */           } 
/*      */         } 
/*      */       } else {
/*   65 */         str3 = WorkflowVersion.getAllVersionStringByWFIDs(str2);
/*      */       } 
/*      */       
/*   68 */       recordSet.executeSql("SELECT workflowid FROM workflow_requestbase WHERE requestid = '" + paramString2 + "'");
/*   69 */       while (recordSet.next()) {
/*   70 */         str1 = recordSet.getString("workflowid");
/*      */       }
/*      */       
/*   73 */       if (!str2.equals("") && !str1.equals("")) {
/*   74 */         String str4 = "," + str3 + ",";
/*   75 */         String str5 = "," + str1 + ",";
/*   76 */         if (str4.indexOf(str5) > -1) {
/*   77 */           bool = true;
/*      */         }
/*      */       } 
/*      */     } 
/*      */     
/*   82 */     return bool;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean checkReportPrivilegesByRequest(String paramString, int paramInt, User paramUser) throws Exception {
/*   91 */     boolean bool = false;
/*   92 */     RecordSet recordSet = new RecordSet();
/*      */ 
/*      */     
/*   95 */     if (checkReportByUserId(paramString, String.valueOf(paramInt), paramUser)) {
/*   96 */       String str1 = "";
/*   97 */       String str2 = "";
/*   98 */       recordSet.executeSql("SELECT reportwfid FROM Workflow_Report WHERE id = " + paramString);
/*   99 */       while (recordSet.next()) {
/*  100 */         str2 = recordSet.getString("reportwfid");
/*      */       }
/*      */ 
/*      */       
/*  104 */       String str3 = "";
/*  105 */       if (str2.indexOf(",") > -1) {
/*  106 */         String[] arrayOfString = Util.TokenizerString2(str2, ",");
/*  107 */         for (byte b = 0; b < arrayOfString.length; b++) {
/*  108 */           if ("".equals(str3)) {
/*  109 */             str3 = WorkflowVersion.getAllVersionStringByWFIDs(arrayOfString[b]);
/*      */           } else {
/*  111 */             str3 = str3 + "," + WorkflowVersion.getAllVersionStringByWFIDs(arrayOfString[b]);
/*      */           } 
/*      */         } 
/*      */       } else {
/*  115 */         str3 = WorkflowVersion.getAllVersionStringByWFIDs(str2);
/*      */       } 
/*      */       
/*  118 */       recordSet.executeSql("SELECT workflowid FROM workflow_requestbase WHERE requestid = '" + paramInt + "'");
/*  119 */       while (recordSet.next()) {
/*  120 */         str1 = recordSet.getString("workflowid");
/*      */       }
/*      */ 
/*      */       
/*  124 */       if (!str2.equals("") && !str1.equals("")) {
/*  125 */         String str4 = "," + str3 + ",";
/*  126 */         String str5 = "," + str1 + ",";
/*  127 */         if (str4.indexOf(str5) > -1) {
/*  128 */           bool = true;
/*      */         }
/*      */       } 
/*      */     } 
/*      */     
/*  133 */     return bool;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean checkReportPrivilegesByRequest(HttpServletRequest paramHttpServletRequest, User paramUser) throws Exception {
/*  142 */     boolean bool = false;
/*  143 */     RecordSet recordSet = new RecordSet();
/*  144 */     String str = Util.null2String(paramHttpServletRequest.getParameter("reportid"));
/*  145 */     int i = Util.getIntValue(paramHttpServletRequest.getParameter("requestid"), 0);
/*  146 */     if (checkReportByUserId(str, String.valueOf(i), paramUser)) {
/*  147 */       String str1 = "";
/*  148 */       String str2 = "";
/*  149 */       recordSet.executeSql("SELECT reportwfid FROM Workflow_Report WHERE id = " + str);
/*  150 */       while (recordSet.next()) {
/*  151 */         str2 = recordSet.getString("reportwfid");
/*      */       }
/*      */ 
/*      */       
/*  155 */       String str3 = "";
/*  156 */       if (str2.indexOf(",") > -1) {
/*  157 */         String[] arrayOfString = Util.TokenizerString2(str2, ",");
/*  158 */         for (byte b = 0; b < arrayOfString.length; b++) {
/*  159 */           if ("".equals(str3)) {
/*  160 */             str3 = WorkflowVersion.getAllVersionStringByWFIDs(arrayOfString[b]);
/*      */           } else {
/*  162 */             str3 = str3 + "," + WorkflowVersion.getAllVersionStringByWFIDs(arrayOfString[b]);
/*      */           } 
/*      */         } 
/*      */       } else {
/*  166 */         str3 = WorkflowVersion.getAllVersionStringByWFIDs(str2);
/*      */       } 
/*      */       
/*  169 */       recordSet.executeSql("SELECT workflowid FROM workflow_requestbase WHERE requestid = '" + i + "'");
/*  170 */       while (recordSet.next()) {
/*  171 */         str1 = recordSet.getString("workflowid");
/*      */       }
/*      */ 
/*      */       
/*  175 */       if (!str2.equals("") && !str1.equals("")) {
/*  176 */         String str4 = "," + str3 + ",";
/*  177 */         String str5 = "," + str1 + ",";
/*  178 */         if (str4.indexOf(str5) > -1) {
/*  179 */           bool = true;
/*      */         }
/*      */       } 
/*      */     } 
/*      */     
/*  184 */     return bool;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean checkReportByUserId(String paramString1, String paramString2, User paramUser) throws Exception {
/*  193 */     boolean bool = false;
/*  194 */     RecordSet recordSet = new RecordSet();
/*  195 */     this.reportShare = new ReportShare();
/*  196 */     this.rscominfo = new ResourceComInfo();
/*  197 */     this.rsvcominfo = new ResourceVirtualComInfo();
/*  198 */     this.deptcominfo = new DepartmentComInfo();
/*  199 */     this.deptvcominfo = new DepartmentVirtualComInfo();
/*  200 */     int i = -1;
/*  201 */     int j = -1;
/*  202 */     int k = -1;
/*  203 */     int m = -1;
/*  204 */     int n = -1;
/*  205 */     int i1 = -1;
/*  206 */     String str1 = String.valueOf(paramUser.getUID());
/*  207 */     String str2 = "";
/*  208 */     String str3 = "";
/*  209 */     String str4 = "";
/*  210 */     String str5 = "";
/*  211 */     String str6 = "";
/*  212 */     String str7 = "";
/*  213 */     String str8 = "";
/*  214 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*      */     
/*  216 */     str8 = this.rscominfo.getJobTitle(str1);
/*  217 */     str4 = this.rscominfo.getDepartmentID(str1);
/*  218 */     str5 = this.rscominfo.getSubCompanyID(str1);
/*  219 */     str6 = this.rsvcominfo.getDepartmentids(str1);
/*  220 */     str7 = this.rsvcominfo.getSubcompanyids(str1);
/*      */     
/*  222 */     String str9 = "";
/*  223 */     String str10 = "";
/*  224 */     if (!"".equals(str6)) {
/*  225 */       str9 = str4 + "," + str6;
/*      */     }
/*  227 */     if (!"".equals(str7)) {
/*  228 */       str10 = str5 + "," + str7;
/*      */     }
/*      */     
/*  231 */     HrmCommonServiceImpl hrmCommonServiceImpl = new HrmCommonServiceImpl();
/*  232 */     List list = hrmCommonServiceImpl.getRoleInfo(paramUser.getUID());
/*      */     
/*  234 */     if (Util.getIntValue(paramString1) < 0) {
/*  235 */       recordSet.executeSql("SELECT shareType,userid,departmentid,subcompanyid,roleid,rolelevel,allowlook,sharelevel,mutidepartmentid,seclevel,seclevel2 FROM WorkflowReportShare WHERE (reportid=" + paramString1 + " or reportid=0)");
/*      */     } else {
/*  237 */       recordSet.executeSql("SELECT shareType,userid,departmentid,subcompanyid,roleid,rolelevel,allowlook,sharelevel,mutidepartmentid,seclevel,seclevel2 FROM WorkflowReportShare WHERE reportid=" + paramString1);
/*      */     } 
/*      */     
/*  240 */     while (recordSet.next()) {
/*  241 */       String str15 = Util.null2String(recordSet.getString("shareType"));
/*  242 */       String str16 = Util.null2String(recordSet.getString("userid"));
/*  243 */       String str17 = Util.null2String(recordSet.getString("departmentid"));
/*  244 */       String str18 = Util.null2String(recordSet.getString("subcompanyid"));
/*  245 */       String str19 = Util.null2String(recordSet.getString("roleid"));
/*  246 */       int i2 = recordSet.getInt("rolelevel");
/*  247 */       String str20 = Util.null2String(recordSet.getString("allowlook"));
/*  248 */       String str21 = Util.null2String(recordSet.getString("sharelevel"));
/*  249 */       String str22 = Util.null2String(recordSet.getString("mutidepartmentid"));
/*  250 */       int i3 = Util.getIntValue(recordSet.getString("seclevel"), 0);
/*  251 */       if (str15.equals("1") && !str16.equals("") && str20.equals("1") && ((
/*  252 */         str16.startsWith(",") && str16.indexOf("," + str1 + ",") > -1) || str16.equals(str1))) {
/*  253 */         hashMap.put(str21 + "#" + str22, str22);
/*      */       }
/*      */       
/*  256 */       if (str15.equals("2") && !str18.equals("") && str20.equals("1")) {
/*  257 */         if ((str18.startsWith(",") && str18.indexOf("," + str5 + ",") > -1) || str18.equals(str5)) {
/*  258 */           hashMap.put(str21 + "#" + str22, str22);
/*      */         }
/*  260 */         else if (!"".equals(str7)) {
/*  261 */           if (str7.indexOf(",") > -1) {
/*  262 */             String[] arrayOfString = Util.TokenizerString2(str7, ",");
/*  263 */             for (byte b = 0; b < arrayOfString.length; b++) {
/*  264 */               if ((str18.startsWith(",") && str18.indexOf("," + arrayOfString[b] + ",") > -1) || str18.equals(arrayOfString[b])) {
/*  265 */                 hashMap.put(str21 + "#" + str22, str22);
/*      */               }
/*      */             }
/*      */           
/*  269 */           } else if ((str18.startsWith(",") && str18.indexOf("," + str7 + ",") > -1) || str18.equals(str7)) {
/*  270 */             hashMap.put(str21 + "#" + str22, str22);
/*      */           } 
/*      */         } 
/*      */       }
/*      */ 
/*      */       
/*  276 */       if (str15.equals("3") && !str17.equals("") && str20.equals("1")) {
/*  277 */         if ((str17.startsWith(",") && str17.indexOf("," + str4 + ",") > -1) || str17.equals(str4)) {
/*  278 */           hashMap.put(str21 + "#" + str22, str22);
/*      */         }
/*  280 */         else if (!"".equals(str6)) {
/*  281 */           if (str6.indexOf(",") > -1) {
/*  282 */             String[] arrayOfString = Util.TokenizerString2(str6, ",");
/*  283 */             for (byte b = 0; b < arrayOfString.length; b++) {
/*  284 */               if ((str17.startsWith(",") && str17.indexOf("," + arrayOfString[b] + ",") > -1) || str17.equals(arrayOfString[b])) {
/*  285 */                 hashMap.put(str21 + "#" + str22, str22);
/*      */               }
/*      */             }
/*      */           
/*  289 */           } else if ((str17.startsWith(",") && str17.indexOf("," + str6 + ",") > -1) || str17.equals(str6)) {
/*  290 */             hashMap.put(str21 + "#" + str22, str22);
/*      */           } 
/*      */         } 
/*      */       }
/*      */ 
/*      */       
/*  296 */       if (str15.equals("4") && !str19.equals("") && !str19.equals("0") && list != null && list.size() > 0 && str20.equals("1")) {
/*  297 */         for (Map map1 : list) {
/*  298 */           Map map2 = map1;
/*  299 */           String str = Util.null2String(map2.get("roleid"));
/*  300 */           int i4 = Util.getIntValue(Util.null2String(map2.get("rolelevel")));
/*  301 */           if (str.equals(str19) && i4 >= i2) {
/*  302 */             hashMap.put(str21 + "#" + str22, str22);
/*      */           }
/*      */         } 
/*      */       }
/*      */       
/*  307 */       if (str15.equals("5") && str20.equals("1")) {
/*  308 */         hashMap.put(str21 + "#" + str22, str22);
/*      */       }
/*      */       
/*  311 */       if (str15.equals("6") && !str16.equals("") && str20.equals("1") && ((
/*  312 */         str16.startsWith(",") && str16.indexOf("," + str8 + ",") > -1) || str16.equals(str8))) {
/*  313 */         if (i3 == 0) {
/*  314 */           if ((str17.startsWith(",") && str17.indexOf("," + str4 + ",") > -1) || str17.equals(str4)) {
/*  315 */             hashMap.put(str21 + "#" + str22, str22); continue;
/*      */           } 
/*  317 */           if (!"".equals(str6)) {
/*  318 */             if (str6.indexOf(",") > -1) {
/*  319 */               String[] arrayOfString = Util.TokenizerString2(str6, ",");
/*  320 */               for (byte b = 0; b < arrayOfString.length; b++) {
/*  321 */                 if ((str17.startsWith(",") && str17.indexOf("," + arrayOfString[b] + ",") > -1) || str17.equals(arrayOfString[b]))
/*  322 */                   hashMap.put(str21 + "#" + str22, str22); 
/*      */               } 
/*      */               continue;
/*      */             } 
/*  326 */             if ((str17.startsWith(",") && str17.indexOf("," + str6 + ",") > -1) || str17.equals(str6)) {
/*  327 */               hashMap.put(str21 + "#" + str22, str22);
/*      */             }
/*      */           } 
/*      */           continue;
/*      */         } 
/*  332 */         if (i3 == 1) {
/*  333 */           if ((str17.startsWith(",") && str17.indexOf("," + str5 + ",") > -1) || str17.equals(str5)) {
/*  334 */             hashMap.put(str21 + "#" + str22, str22); continue;
/*      */           } 
/*  336 */           if (!"".equals(str7)) {
/*  337 */             if (str7.indexOf(",") > -1) {
/*  338 */               String[] arrayOfString = Util.TokenizerString2(str7, ",");
/*  339 */               for (byte b = 0; b < arrayOfString.length; b++) {
/*  340 */                 if ((str17.startsWith(",") && str17.indexOf("," + arrayOfString[b] + ",") > -1) || str17.equals(arrayOfString[b]))
/*  341 */                   hashMap.put(str21 + "#" + str22, str22); 
/*      */               } 
/*      */               continue;
/*      */             } 
/*  345 */             if ((str17.startsWith(",") && str17.indexOf("," + str7 + ",") > -1) || str17.equals(str7)) {
/*  346 */               hashMap.put(str21 + "#" + str22, str22);
/*      */             }
/*      */           } 
/*      */           
/*      */           continue;
/*      */         } 
/*  352 */         hashMap.put(str21 + "#" + str22, str22);
/*      */       } 
/*      */     } 
/*      */ 
/*      */ 
/*      */     
/*  358 */     String str11 = "";
/*  359 */     String str12 = "";
/*  360 */     if (!hashMap.isEmpty()) {
/*  361 */       for (String str : hashMap.keySet()) {
/*  362 */         String[] arrayOfString = Util.TokenizerString2(str, "#");
/*  363 */         if (arrayOfString[0].equals("9")) {
/*  364 */           m = Integer.parseInt(arrayOfString[0]);
/*  365 */           str2 = str2 + (String)hashMap.get(str) + ","; continue;
/*  366 */         }  if (arrayOfString[0].equals("5")) {
/*  367 */           i1 = Integer.parseInt(arrayOfString[0]);
/*  368 */           str3 = str3 + (String)hashMap.get(str) + ","; continue;
/*      */         } 
/*  370 */         if (arrayOfString[0].equals("3")) {
/*  371 */           k = Integer.parseInt(arrayOfString[0]); continue;
/*  372 */         }  if (arrayOfString[0].equals("4")) {
/*  373 */           n = Integer.parseInt(arrayOfString[0]); continue;
/*  374 */         }  if (arrayOfString[0].equals("2")) {
/*  375 */           i = Integer.parseInt(arrayOfString[0]); continue;
/*      */         } 
/*  377 */         j = Integer.parseInt(arrayOfString[0]);
/*  378 */         if (j == 0) {
/*  379 */           if (str11.equals("")) {
/*  380 */             str11 = "" + str9;
/*      */           } else {
/*  382 */             str11 = str11 + "," + str9;
/*      */           } 
/*      */         }
/*  385 */         if (j == 1) {
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */           
/*  393 */           if (str12.equals("")) {
/*  394 */             str12 = str10; continue;
/*      */           } 
/*  396 */           str12 = str12 + "," + str10;
/*      */         } 
/*      */       } 
/*      */     }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  405 */     if (!"".equals(str2) && 
/*  406 */       str2.endsWith(",")) {
/*  407 */       str2 = str2.substring(0, str2.length() - 1);
/*      */     }
/*      */     
/*  410 */     if (!"".equals(str3) && 
/*  411 */       str3.endsWith(",")) {
/*  412 */       str3 = str3.substring(0, str3.length() - 1);
/*      */     }
/*      */ 
/*      */     
/*  416 */     if (i == -1 && j == -1 && k == -1 && m == -1 && n == -1 && i1 == -1) {
/*  417 */       return false;
/*      */     }
/*      */ 
/*      */     
/*  421 */     if (k == 3) {
/*      */       
/*  423 */       String str15 = "";
/*  424 */       if (!"".equals(str4) && !"".equals(DepartmentComInfo.getAllChildDepartId(str4, ""))) {
/*  425 */         str15 = str15 + "," + DepartmentComInfo.getAllChildDepartId(str4, "");
/*      */       }
/*  427 */       if (str11.equals("")) {
/*  428 */         str11 = str15;
/*      */       } else {
/*  430 */         str11 = str11 + "," + str15;
/*      */       } 
/*      */       
/*  433 */       String str16 = "";
/*  434 */       if (!"".equals(str6)) {
/*  435 */         if (str6.indexOf(",") > -1) {
/*  436 */           String[] arrayOfString = Util.TokenizerString2(str6, ",");
/*  437 */           for (byte b = 0; b < arrayOfString.length; b++) {
/*  438 */             if (!"".equals(DepartmentVirtualComInfo.getAllChildDepartId(arrayOfString[b], ""))) {
/*  439 */               if ("".equals(str16)) {
/*  440 */                 str16 = DepartmentVirtualComInfo.getAllChildDepartId(arrayOfString[b], "");
/*      */               } else {
/*  442 */                 str16 = str16 + "," + DepartmentVirtualComInfo.getAllChildDepartId(arrayOfString[b], "");
/*      */               }
/*      */             
/*      */             }
/*      */           } 
/*  447 */         } else if (!"".equals(DepartmentVirtualComInfo.getAllChildDepartId(str6, ""))) {
/*  448 */           str16 = str16 + "," + DepartmentVirtualComInfo.getAllChildDepartId(str6, "");
/*      */         } 
/*      */       }
/*      */       
/*  452 */       if (!"".equals(str16)) {
/*  453 */         str11 = str11 + "," + str16;
/*      */       }
/*  455 */       while (str11.indexOf(",,") > -1) {
/*  456 */         str11 = str11.replaceAll(",,", ",");
/*      */       }
/*  458 */       if (str11.indexOf(",") == 0) {
/*  459 */         str11 = str11.substring(1, str11.length());
/*      */       }
/*  461 */       if (str11.endsWith(",")) {
/*  462 */         str11 = str11.substring(0, str11.length() - 1);
/*      */       }
/*      */     } 
/*      */     
/*  466 */     if (m == 9) {
/*  467 */       if (str11.equals("")) { str11 = str2; }
/*  468 */       else { str11 = str11 + "," + str2; }
/*      */     
/*      */     }
/*  471 */     if (i1 == 5) {
/*  472 */       if (str12.equals("")) { str12 = str3; }
/*  473 */       else { str12 = str12 + "," + str3; }
/*      */     
/*      */     }
/*  476 */     if (str11.equals("")) str11 = "-100"; 
/*  477 */     if (str12.equals("")) str12 = "-100"; 
/*  478 */     String str13 = "";
/*  479 */     if (n == 4)
/*      */     {
/*  481 */       str13 = " or c.userid= " + str1;
/*      */     }
/*  483 */     String str14 = "";
/*  484 */     if (recordSet.getDBType().equals("oracle")) {
/*      */       
/*  486 */       str14 = str14 + " and nvl(r.currentstatus,-1) = -1 ";
/*  487 */     } else if (recordSet.getDBType().equals("mysql")) {
/*  488 */       str14 = str14 + " and ifnull(r.currentstatus,-1) = -1 ";
/*      */     }
/*      */     else {
/*      */       
/*  492 */       str14 = str14 + " and isnull(r.currentstatus,-1) = -1 ";
/*      */     } 
/*  494 */     StringBuffer stringBuffer = new StringBuffer();
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  530 */     stringBuffer.append("select distinct c.requestid from workflow_report b ");
/*  531 */     stringBuffer.append(" inner join (select distinct b.formid,b.id,r.requestid,");
/*  532 */     if (!"".equals(str13)) stringBuffer.append("c.userid,"); 
/*  533 */     stringBuffer.append(" r.creater FROM workflow_requestbase r,workflow_base b,workflow_currentoperator c ");
/*  534 */     stringBuffer.append(" where r.workflowid=b.id and r.requestid = c.requestid and r.requestid = ").append(paramString2).append(str14).append(" ) c on b.formid = c.formid ");
/*  535 */     stringBuffer.append(" where b.id = ").append(paramString1);
/*      */     
/*  537 */     if (i != 2) {
/*  538 */       stringBuffer.append(" and (c.creater=1 or ");
/*  539 */       stringBuffer.append(" exists (select e.id from hrmresource e inner join hrmdepartmentallview f on e.departmentid=f.id ");
/*  540 */       stringBuffer.append(" inner join hrmsubcompanyallview h on e.subcompanyid1 = h.id ");
/*  541 */       stringBuffer.append(" where c.creater=e.id ");
/*  542 */       stringBuffer.append("and (f.id in(").append(str11).append(") ").append(str13).append(" or h.id in(").append(str12).append("))");
/*  543 */       stringBuffer.append("))");
/*      */     } 
/*  545 */     recordSet.writeLog("report Authorizate sql------------->:" + stringBuffer.toString());
/*  546 */     recordSet.executeQuery(stringBuffer.toString(), new Object[0]);
/*  547 */     if (recordSet.next()) {
/*  548 */       bool = true;
/*      */     }
/*  550 */     return bool;
/*      */   }
/*      */   
/*      */   public String getReportIdByUserId(User paramUser) throws Exception {
/*  554 */     RecordSet recordSet = new RecordSet();
/*  555 */     this.rscominfo = new ResourceComInfo();
/*  556 */     this.rsvcominfo = new ResourceVirtualComInfo();
/*  557 */     String str1 = String.valueOf(paramUser.getUID());
/*      */     
/*  559 */     String str2 = "";
/*  560 */     String str3 = "";
/*  561 */     String str4 = "";
/*  562 */     String str5 = "";
/*  563 */     String str6 = ",";
/*  564 */     String str7 = "";
/*  565 */     String str8 = "";
/*  566 */     int i = 0;
/*      */     
/*  568 */     str7 = this.rscominfo.getJobTitle(str1);
/*  569 */     str2 = this.rscominfo.getDepartmentID(str1);
/*  570 */     str3 = this.rscominfo.getSubCompanyID(str1);
/*  571 */     str4 = this.rsvcominfo.getDepartmentids(str1);
/*  572 */     str5 = this.rsvcominfo.getSubcompanyids(str1);
/*  573 */     i = Util.getIntValue(this.rscominfo.getSeclevel(str1), 0);
/*      */     
/*  575 */     HrmCommonServiceImpl hrmCommonServiceImpl = new HrmCommonServiceImpl();
/*  576 */     List list = hrmCommonServiceImpl.getRoleInfo(paramUser.getUID());
/*      */     
/*  578 */     recordSet.executeSql("SELECT reportid,shareType,userid,departmentid,subcompanyid,roleid,rolelevel,seclevel,seclevel2 FROM WorkflowReportShare");
/*      */     
/*  580 */     while (recordSet.next()) {
/*  581 */       String str9 = Util.null2String(recordSet.getString("reportid"));
/*  582 */       String str10 = Util.null2String(recordSet.getString("shareType"));
/*  583 */       String str11 = Util.null2String(recordSet.getString("userid"));
/*  584 */       String str12 = Util.null2String(recordSet.getString("departmentid"));
/*  585 */       String str13 = Util.null2String(recordSet.getString("subcompanyid"));
/*  586 */       String str14 = Util.null2String(recordSet.getString("roleid"));
/*  587 */       int j = Util.getIntValue(recordSet.getString("rolelevel"));
/*  588 */       int k = Util.getIntValue(recordSet.getString("seclevel"), 0);
/*  589 */       int m = Util.getIntValue(recordSet.getString("seclevel2"), 100);
/*  590 */       if ((i < k || i > m) && !str10.equals("6") && !str10.equals("1")) {
/*      */         continue;
/*      */       }
/*      */       
/*  594 */       if (str10.equals("1") && !str11.equals("") && ((
/*  595 */         str11.startsWith(",") && str11.indexOf("," + str1 + ",") > -1) || str11.equals(str1))) {
/*  596 */         str8 = str8 + str9 + ",";
/*      */       }
/*      */       
/*  599 */       if (str10.equals("2") && !str13.equals("")) {
/*  600 */         if ((str13.startsWith(",") && str13.indexOf("," + str3 + ",") > -1) || str13.equals(str3)) {
/*  601 */           str8 = str8 + str9 + ",";
/*      */         }
/*  603 */         else if (!"".equals(str5)) {
/*  604 */           if (str5.indexOf(",") > -1) {
/*  605 */             String[] arrayOfString = Util.TokenizerString2(str5, ",");
/*  606 */             for (byte b = 0; b < arrayOfString.length; b++) {
/*  607 */               if ((str13.startsWith(",") && str13.indexOf("," + arrayOfString[b] + ",") > -1) || str13.equals(arrayOfString[b])) {
/*  608 */                 str8 = str8 + str9 + ",";
/*      */               }
/*      */             }
/*      */           
/*  612 */           } else if ((str13.startsWith(",") && str13.indexOf("," + str5 + ",") > -1) || str13.equals(str5)) {
/*  613 */             str8 = str8 + str9 + ",";
/*      */           } 
/*      */         } 
/*      */       }
/*      */ 
/*      */       
/*  619 */       if (str10.equals("3") && !str12.equals("")) {
/*  620 */         if ((str12.startsWith(",") && str12.indexOf("," + str2 + ",") > -1) || str12.equals(str2)) {
/*  621 */           str8 = str8 + str9 + ",";
/*      */         }
/*  623 */         else if (!"".equals(str4)) {
/*  624 */           if (str4.indexOf(",") > -1) {
/*  625 */             String[] arrayOfString = Util.TokenizerString2(str4, ",");
/*  626 */             for (byte b = 0; b < arrayOfString.length; b++) {
/*  627 */               if ((str12.startsWith(",") && str12.indexOf("," + arrayOfString[b] + ",") > -1) || str12.equals(arrayOfString[b])) {
/*  628 */                 str8 = str8 + str9 + ",";
/*      */               }
/*      */             }
/*      */           
/*  632 */           } else if ((str12.startsWith(",") && str12.indexOf("," + str4 + ",") > -1) || str12.equals(str4)) {
/*  633 */             str8 = str8 + str9 + ",";
/*      */           } 
/*      */         } 
/*      */       }
/*      */ 
/*      */       
/*  639 */       if (str10.equals("4") && !str14.equals("") && !str14.equals("0") && list != null && list.size() > 0) {
/*  640 */         for (Map map1 : list) {
/*  641 */           Map map2 = map1;
/*  642 */           String str = Util.null2String(map2.get("roleid"));
/*  643 */           int n = Util.getIntValue(Util.null2String(map2.get("rolelevel")));
/*  644 */           if (str.equals(str14) && n >= j) {
/*  645 */             str8 = str8 + str9 + ",";
/*      */           }
/*      */         } 
/*      */       }
/*  649 */       if (str10.equals("5")) {
/*  650 */         str8 = str8 + str9 + ",";
/*      */       }
/*      */       
/*  653 */       if (str10.equals("6") && !str11.equals("") && ((
/*  654 */         str11.startsWith(",") && str11.indexOf("," + str7 + ",") > -1) || str11.equals(str7))) {
/*  655 */         if (k == 0) {
/*  656 */           if ((str12.startsWith(",") && str12.indexOf("," + str2 + ",") > -1) || str12.equals(str2)) {
/*  657 */             str8 = str8 + str9 + ","; continue;
/*      */           } 
/*  659 */           if (!"".equals(str4)) {
/*  660 */             if (str4.indexOf(",") > -1) {
/*  661 */               String[] arrayOfString = Util.TokenizerString2(str4, ",");
/*  662 */               for (byte b = 0; b < arrayOfString.length; b++) {
/*  663 */                 if ((str12.startsWith(",") && str12.indexOf("," + arrayOfString[b] + ",") > -1) || str12.equals(arrayOfString[b]))
/*  664 */                   str8 = str8 + str9 + ","; 
/*      */               } 
/*      */               continue;
/*      */             } 
/*  668 */             if ((str12.startsWith(",") && str12.indexOf("," + str4 + ",") > -1) || str12.equals(str4)) {
/*  669 */               str8 = str8 + str9 + ",";
/*      */             }
/*      */           } 
/*      */           continue;
/*      */         } 
/*  674 */         if (k == 1) {
/*  675 */           if ((str12.startsWith(",") && str12.indexOf("," + str3 + ",") > -1) || str12.equals(str3)) {
/*  676 */             str8 = str8 + str9 + ","; continue;
/*      */           } 
/*  678 */           if (!"".equals(str5)) {
/*  679 */             if (str5.indexOf(",") > -1) {
/*  680 */               String[] arrayOfString = Util.TokenizerString2(str5, ",");
/*  681 */               for (byte b = 0; b < arrayOfString.length; b++) {
/*  682 */                 if ((str12.startsWith(",") && str12.indexOf("," + arrayOfString[b] + ",") > -1) || str12.equals(arrayOfString[b]))
/*  683 */                   str8 = str8 + str9 + ","; 
/*      */               } 
/*      */               continue;
/*      */             } 
/*  687 */             if ((str12.startsWith(",") && str12.indexOf("," + str5 + ",") > -1) || str12.equals(str5)) {
/*  688 */               str8 = str8 + str9 + ",";
/*      */             }
/*      */           } 
/*      */           
/*      */           continue;
/*      */         } 
/*  694 */         str8 = str8 + str9 + ",";
/*      */       } 
/*      */     } 
/*      */ 
/*      */ 
/*      */     
/*  700 */     return str8;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getUserRights(String paramString, User paramUser) throws Exception {
/*  712 */     RecordSet recordSet = new RecordSet();
/*  713 */     this.deptcominfo = new DepartmentComInfo();
/*  714 */     this.reportShare = new ReportShare();
/*  715 */     this.rscominfo = new ResourceComInfo();
/*  716 */     this.rsvcominfo = new ResourceVirtualComInfo();
/*  717 */     this.deptcominfo = new DepartmentComInfo();
/*  718 */     this.deptvcominfo = new DepartmentVirtualComInfo();
/*  719 */     int i = -1;
/*  720 */     int j = -1;
/*  721 */     int k = -1;
/*  722 */     int m = -1;
/*  723 */     int n = -1;
/*  724 */     String str1 = String.valueOf(paramUser.getUID());
/*  725 */     String str2 = "";
/*  726 */     String str3 = "";
/*  727 */     String str4 = "";
/*  728 */     String str5 = "";
/*  729 */     String str6 = "";
/*  730 */     String str7 = "";
/*  731 */     String str8 = "";
/*  732 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  733 */     int i1 = 0;
/*      */     
/*  735 */     str8 = this.rscominfo.getJobTitle(str1);
/*  736 */     str4 = this.rscominfo.getDepartmentID(str1);
/*  737 */     str5 = this.rscominfo.getSubCompanyID(str1);
/*  738 */     str6 = this.rsvcominfo.getDepartmentids(str1);
/*  739 */     str7 = this.rsvcominfo.getSubcompanyids(str1);
/*      */     
/*  741 */     i1 = Util.getIntValue(this.rscominfo.getSeclevel(str1), 0);
/*  742 */     String str9 = "";
/*  743 */     String str10 = "";
/*  744 */     if (!"".equals(str6)) {
/*  745 */       str9 = str4 + "," + str6;
/*      */     }
/*  747 */     if (!"".equals(str7)) {
/*  748 */       str10 = str5 + "," + str7;
/*      */     }
/*      */     
/*  751 */     HrmCommonServiceImpl hrmCommonServiceImpl = new HrmCommonServiceImpl();
/*  752 */     List list = hrmCommonServiceImpl.getRoleInfo(paramUser.getUID());
/*      */     
/*  754 */     recordSet.executeSql("select shareType,userid,departmentid,subcompanyid,roleid,rolelevel,allowlook,sharelevel,mutidepartmentid,seclevel,seclevel2 from WorkflowReportShare where reportid=" + paramString + " or reportid=0 ");
/*  755 */     while (recordSet.next()) {
/*  756 */       String str13 = Util.null2String(recordSet.getString("shareType"));
/*  757 */       String str14 = Util.null2String(recordSet.getString("userid"));
/*  758 */       String str15 = Util.null2String(recordSet.getString("departmentid"));
/*  759 */       String str16 = Util.null2String(recordSet.getString("subcompanyid"));
/*  760 */       String str17 = Util.null2String(recordSet.getString("roleid"));
/*  761 */       int i2 = recordSet.getInt("rolelevel");
/*  762 */       String str18 = Util.null2String(recordSet.getString("sharelevel"));
/*  763 */       String str19 = Util.null2String(recordSet.getString("mutidepartmentid"));
/*      */       
/*  765 */       if (paramUser.isAdmin() && (
/*  766 */         ObjectUtil.equal(str18, "0") || ObjectUtil.equal(str18, "1") || ObjectUtil.equal(str18, "3"))) {
/*      */         continue;
/*      */       }
/*      */ 
/*      */       
/*  771 */       int i3 = Util.getIntValue(recordSet.getString("seclevel"), -999);
/*  772 */       int i4 = Util.getIntValue(recordSet.getString("seclevel2"), 999);
/*  773 */       if (i1 < i3 || i1 > i4) {
/*      */         continue;
/*      */       }
/*      */       
/*  777 */       if (str13.equals("1") && !str14.equals("") && ((
/*  778 */         str14.startsWith(",") && str14.indexOf("," + str1 + ",") > -1) || str14.equals(str1))) {
/*  779 */         hashMap.put(str18 + "#" + str19, str19);
/*      */       }
/*      */       
/*  782 */       if (str13.equals("2") && !str16.equals("")) {
/*  783 */         if ((str16.startsWith(",") && str16.indexOf("," + str5 + ",") > -1) || str16.equals(str5)) {
/*  784 */           hashMap.put(str18 + "#" + str19, str19);
/*      */         }
/*  786 */         else if (!"".equals(str7)) {
/*  787 */           if (str7.indexOf(",") > -1) {
/*  788 */             String[] arrayOfString = Util.TokenizerString2(str7, ",");
/*  789 */             for (byte b = 0; b < arrayOfString.length; b++) {
/*  790 */               if ((str16.startsWith(",") && str16.indexOf("," + arrayOfString[b] + ",") > -1) || str16.equals(arrayOfString[b])) {
/*  791 */                 hashMap.put(str18 + "#" + str19, str19);
/*      */               }
/*      */             }
/*      */           
/*  795 */           } else if ((str16.startsWith(",") && str16.indexOf("," + str7 + ",") > -1) || str16.equals(str7)) {
/*  796 */             hashMap.put(str18 + "#" + str19, str19);
/*      */           } 
/*      */         } 
/*      */       }
/*      */ 
/*      */       
/*  802 */       if (str13.equals("3") && !str15.equals("")) {
/*  803 */         if ((str15.startsWith(",") && str15.indexOf("," + str4 + ",") > -1) || str15.equals(str4)) {
/*  804 */           hashMap.put(str18 + "#" + str19, str19);
/*      */         }
/*  806 */         else if (!"".equals(str6)) {
/*  807 */           if (str6.indexOf(",") > -1) {
/*  808 */             String[] arrayOfString = Util.TokenizerString2(str6, ",");
/*  809 */             for (byte b = 0; b < arrayOfString.length; b++) {
/*  810 */               if ((str15.startsWith(",") && str15.indexOf("," + arrayOfString[b] + ",") > -1) || str15.equals(arrayOfString[b])) {
/*  811 */                 hashMap.put(str18 + "#" + str19, str19);
/*      */               }
/*      */             }
/*      */           
/*  815 */           } else if ((str15.startsWith(",") && str15.indexOf("," + str6 + ",") > -1) || str15.equals(str6)) {
/*  816 */             hashMap.put(str18 + "#" + str19, str19);
/*      */           } 
/*      */         } 
/*      */       }
/*      */ 
/*      */       
/*  822 */       if (str13.equals("4") && !str17.equals("") && !str17.equals("0") && list != null && list.size() > 0) {
/*  823 */         for (Map map1 : list) {
/*  824 */           Map map2 = map1;
/*  825 */           String str = Util.null2String(map2.get("roleid"));
/*  826 */           int i5 = Util.getIntValue(Util.null2String(map2.get("rolelevel")));
/*  827 */           if (str.equals(str17) && i5 >= i2) {
/*  828 */             hashMap.put(str18 + "#" + str19, str19);
/*      */           }
/*      */         } 
/*      */       }
/*  832 */       if (str13.equals("5")) {
/*  833 */         hashMap.put(str18 + "#" + str19, str19);
/*      */       }
/*      */       
/*  836 */       if (str13.equals("6") && !str14.equals("") && ((
/*  837 */         str14.startsWith(",") && str14.indexOf("," + str8 + ",") > -1) || str14.equals(str8))) {
/*  838 */         if (i3 == 0) {
/*  839 */           if ((str15.startsWith(",") && str15.indexOf("," + str4 + ",") > -1) || str15.equals(str4)) {
/*  840 */             hashMap.put(str18 + "#" + str19, str19); continue;
/*      */           } 
/*  842 */           if (!"".equals(str6)) {
/*  843 */             if (str6.indexOf(",") > -1) {
/*  844 */               String[] arrayOfString = Util.TokenizerString2(str6, ",");
/*  845 */               for (byte b = 0; b < arrayOfString.length; b++) {
/*  846 */                 if ((str15.startsWith(",") && str15.indexOf("," + arrayOfString[b] + ",") > -1) || str15.equals(arrayOfString[b]))
/*  847 */                   hashMap.put(str18 + "#" + str19, str19); 
/*      */               } 
/*      */               continue;
/*      */             } 
/*  851 */             if ((str15.startsWith(",") && str15.indexOf("," + str6 + ",") > -1) || str15.equals(str6)) {
/*  852 */               hashMap.put(str18 + "#" + str19, str19);
/*      */             }
/*      */           } 
/*      */           continue;
/*      */         } 
/*  857 */         if (i3 == 1) {
/*  858 */           if ((str15.startsWith(",") && str15.indexOf("," + str5 + ",") > -1) || str15.equals(str5)) {
/*  859 */             hashMap.put(str18 + "#" + str19, str19); continue;
/*      */           } 
/*  861 */           if (!"".equals(str7)) {
/*  862 */             if (str7.indexOf(",") > -1) {
/*  863 */               String[] arrayOfString = Util.TokenizerString2(str7, ",");
/*  864 */               for (byte b = 0; b < arrayOfString.length; b++) {
/*  865 */                 if ((str15.startsWith(",") && str15.indexOf("," + arrayOfString[b] + ",") > -1) || str15.equals(arrayOfString[b]))
/*  866 */                   hashMap.put(str18 + "#" + str19, str19); 
/*      */               } 
/*      */               continue;
/*      */             } 
/*  870 */             if ((str15.startsWith(",") && str15.indexOf("," + str7 + ",") > -1) || str15.equals(str7)) {
/*  871 */               hashMap.put(str18 + "#" + str19, str19);
/*      */             }
/*      */           } 
/*      */           
/*      */           continue;
/*      */         } 
/*  877 */         hashMap.put(str18 + "#" + str19, str19);
/*      */       } 
/*      */     } 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  884 */     String str11 = "-100";
/*  885 */     String str12 = "";
/*  886 */     if (!hashMap.isEmpty()) {
/*  887 */       for (String str : hashMap.keySet()) {
/*  888 */         String[] arrayOfString = Util.TokenizerString2(str, "#");
/*  889 */         if (arrayOfString[0].equals("9")) {
/*  890 */           m = Integer.parseInt(arrayOfString[0]);
/*  891 */           str2 = str2 + (String)hashMap.get(str) + ","; continue;
/*  892 */         }  if (arrayOfString[0].equals("4")) {
/*  893 */           n = Integer.parseInt(arrayOfString[0]);
/*  894 */           str3 = str3 + (String)hashMap.get(str) + ","; continue;
/*      */         } 
/*  896 */         if (arrayOfString[0].equals("3")) {
/*  897 */           k = Integer.parseInt(arrayOfString[0]); continue;
/*  898 */         }  if (arrayOfString[0].equals("2")) {
/*  899 */           i = Integer.parseInt(arrayOfString[0]); continue;
/*      */         } 
/*  901 */         j = Integer.parseInt(arrayOfString[0]);
/*  902 */         if (j == 0) {
/*  903 */           if (str11.equals("-100")) {
/*  904 */             str11 = "" + str9;
/*      */           } else {
/*  906 */             str11 = str11 + "," + str9;
/*      */           } 
/*      */         }
/*  909 */         if (j == 1) {
/*  910 */           String str13 = "";
/*  911 */           recordSet.executeSql("select id from HrmDepartmentAllView where subcompanyid1 in (" + str10 + ") ");
/*  912 */           while (recordSet.next()) {
/*  913 */             String str14 = Util.null2String(recordSet.getString("id"));
/*  914 */             if ("".equals(str13)) {
/*  915 */               str13 = str14; continue;
/*      */             } 
/*  917 */             str13 = str13 + "," + str14;
/*      */           } 
/*      */           
/*  920 */           if (!"".equals(str13)) {
/*  921 */             str11 = str11 + "," + str13;
/*      */           }
/*      */         } 
/*      */       } 
/*      */     }
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  930 */     if (!"".equals(str2) && 
/*  931 */       str2.endsWith(",")) {
/*  932 */       str2 = str2.substring(0, str2.length() - 1);
/*      */     }
/*      */     
/*  935 */     if (!"".equals(str3) && 
/*  936 */       str3.endsWith(",")) {
/*  937 */       str3 = str3.substring(0, str3.length() - 1);
/*      */     }
/*      */ 
/*      */     
/*  941 */     if (k == 3) {
/*      */       
/*  943 */       String str13 = "";
/*  944 */       if (!"".equals(str4) && !"".equals(DepartmentComInfo.getAllChildDepartId(str4, ""))) {
/*  945 */         str13 = str13 + "," + DepartmentComInfo.getAllChildDepartId(str4, "");
/*      */       }
/*  947 */       if (str11.equals("")) {
/*  948 */         str11 = str13;
/*      */       } else {
/*  950 */         str11 = str11 + "," + str13;
/*      */       } 
/*      */       
/*  953 */       String str14 = "";
/*  954 */       if (!"".equals(str6)) {
/*  955 */         if (str6.indexOf(",") > -1) {
/*  956 */           String[] arrayOfString = Util.TokenizerString2(str6, ",");
/*  957 */           for (byte b = 0; b < arrayOfString.length; b++) {
/*  958 */             if (!"".equals(DepartmentVirtualComInfo.getAllChildDepartId(arrayOfString[b], ""))) {
/*  959 */               if ("".equals(str14)) {
/*  960 */                 str14 = DepartmentVirtualComInfo.getAllChildDepartId(arrayOfString[b], "");
/*      */               } else {
/*  962 */                 str14 = str14 + "," + DepartmentVirtualComInfo.getAllChildDepartId(arrayOfString[b], "");
/*      */               }
/*      */             
/*      */             }
/*      */           } 
/*  967 */         } else if (!"".equals(DepartmentVirtualComInfo.getAllChildDepartId(str6, ""))) {
/*  968 */           str14 = str14 + "," + DepartmentVirtualComInfo.getAllChildDepartId(str6, "");
/*      */         } 
/*      */       }
/*      */       
/*  972 */       if (!"".equals(str14)) {
/*  973 */         str11 = str11 + "," + str14;
/*      */       }
/*      */       
/*  976 */       while (str11.indexOf(",,") > -1) {
/*  977 */         str11 = str11.replaceAll(",,", ",");
/*      */       }
/*  979 */       if (str11.indexOf(",") == 0) {
/*  980 */         str11 = str11.substring(1, str11.length());
/*      */       }
/*  982 */       if (str11.endsWith(",")) {
/*  983 */         str11 = str11.substring(0, str11.length() - 1);
/*      */       }
/*      */     } 
/*      */ 
/*      */     
/*  988 */     if (m == 9) {
/*  989 */       if (str11.equals("")) { str11 = str2; }
/*  990 */       else { str11 = str11 + "," + str2; }
/*      */     
/*      */     }
/*  993 */     if (n == 4) {
/*  994 */       String str = "";
/*  995 */       recordSet.executeSql("select id from HrmDepartmentAllView where subcompanyid1 in (" + str3 + ") ");
/*  996 */       while (recordSet.next()) {
/*  997 */         String str13 = Util.null2String(recordSet.getString("id"));
/*  998 */         if ("".equals(str)) {
/*  999 */           str = str13; continue;
/*      */         } 
/* 1001 */         str = str + "," + str13;
/*      */       } 
/*      */       
/* 1004 */       if (!"".equals(str)) {
/* 1005 */         str11 = str11 + "," + str;
/*      */       }
/*      */     } 
/*      */     
/* 1009 */     if (i == 2) {
/* 1010 */       str11 = "";
/*      */     }
/*      */     
/* 1013 */     if (!"".equals(str12)) {
/* 1014 */       if (str11.equals("-100")) {
/* 1015 */         str11 = "" + str12;
/*      */       } else {
/* 1017 */         str11 = str11 + "," + str12;
/*      */       } 
/*      */     }
/* 1020 */     return str11;
/*      */   }
/*      */   
/*      */   public boolean checkFlowReport(String paramString1, String paramString2, User paramUser) {
/* 1024 */     boolean bool = false;
/* 1025 */     RecordSet recordSet = new RecordSet();
/*      */     
/*      */     try {
/* 1028 */       this.rscominfo = new ResourceComInfo();
/* 1029 */       this.rsvcominfo = new ResourceVirtualComInfo();
/* 1030 */     } catch (Exception exception) {
/* 1031 */       exception.printStackTrace();
/*      */     } 
/*      */     
/* 1034 */     String str1 = String.valueOf(paramUser.getUID());
/* 1035 */     String str2 = "";
/* 1036 */     String str3 = "";
/* 1037 */     String str4 = "";
/* 1038 */     String str5 = "";
/* 1039 */     String str6 = "";
/*      */     
/* 1041 */     str6 = this.rscominfo.getJobTitle(str1);
/* 1042 */     str2 = this.rscominfo.getDepartmentID(str1);
/* 1043 */     str3 = this.rscominfo.getSubCompanyID(str1);
/* 1044 */     str4 = this.rsvcominfo.getDepartmentids(str1);
/* 1045 */     str5 = this.rsvcominfo.getSubcompanyids(str1);
/*      */     
/* 1047 */     HrmCommonServiceImpl hrmCommonServiceImpl = new HrmCommonServiceImpl();
/* 1048 */     List list = hrmCommonServiceImpl.getRoleInfo(paramUser.getUID());
/*      */     
/* 1050 */     recordSet.executeSql("select shareType,userid,departmentid,subcompanyid,roleid,rolelevel,seclevel,seclevel2 from WorkflowReportShare where (reportid=" + paramString1 + " or reportid=0) and allowlook = 1 ");
/* 1051 */     while (recordSet.next()) {
/* 1052 */       String str7 = Util.null2String(recordSet.getString("shareType"));
/* 1053 */       String str8 = Util.null2String(recordSet.getString("userid"));
/* 1054 */       String str9 = Util.null2String(recordSet.getString("departmentid"));
/* 1055 */       String str10 = Util.null2String(recordSet.getString("subcompanyid"));
/* 1056 */       String str11 = Util.null2String(recordSet.getString("roleid"));
/* 1057 */       int i = recordSet.getInt("rolelevel");
/* 1058 */       int j = Util.getIntValue(recordSet.getString("seclevel"), 0);
/* 1059 */       int k = Util.getIntValue(recordSet.getString("seclevel2"), 0);
/* 1060 */       if (str7.equals("1") && !str8.equals("") && ((
/* 1061 */         str8.startsWith(",") && str8.indexOf("," + str1 + ",") > -1) || str8.equals(str1))) {
/* 1062 */         bool = true;
/*      */       }
/*      */       
/* 1065 */       if (str7.equals("2") && !str10.equals("")) {
/* 1066 */         if ((str10.startsWith(",") && str10.indexOf("," + str3 + ",") > -1) || str10.equals(str3)) {
/* 1067 */           bool = true;
/*      */         }
/* 1069 */         else if (!"".equals(str5)) {
/* 1070 */           if (str5.indexOf(",") > -1) {
/* 1071 */             String[] arrayOfString = Util.TokenizerString2(str5, ",");
/* 1072 */             for (byte b = 0; b < arrayOfString.length; b++) {
/* 1073 */               if ((str10.startsWith(",") && str10.indexOf("," + arrayOfString[b] + ",") > -1) || str10.equals(arrayOfString[b])) {
/* 1074 */                 bool = true;
/*      */               }
/*      */             }
/*      */           
/* 1078 */           } else if ((str10.startsWith(",") && str10.indexOf("," + str5 + ",") > -1) || str10.equals(str5)) {
/* 1079 */             bool = true;
/*      */           } 
/*      */         } 
/*      */       }
/*      */ 
/*      */       
/* 1085 */       if (str7.equals("3") && !str9.equals("")) {
/* 1086 */         if ((str9.startsWith(",") && str9.indexOf("," + str2 + ",") > -1) || str9.equals(str2)) {
/* 1087 */           bool = true;
/*      */         }
/* 1089 */         else if (!"".equals(str4)) {
/* 1090 */           if (str4.indexOf(",") > -1) {
/* 1091 */             String[] arrayOfString = Util.TokenizerString2(str4, ",");
/* 1092 */             for (byte b = 0; b < arrayOfString.length; b++) {
/* 1093 */               if ((str9.startsWith(",") && str9.indexOf("," + arrayOfString[b] + ",") > -1) || str9.equals(arrayOfString[b])) {
/* 1094 */                 bool = true;
/*      */               }
/*      */             }
/*      */           
/* 1098 */           } else if ((str9.startsWith(",") && str9.indexOf("," + str4 + ",") > -1) || str9.equals(str4)) {
/* 1099 */             bool = true;
/*      */           } 
/*      */         } 
/*      */       }
/*      */ 
/*      */       
/* 1105 */       if (str7.equals("4") && !str11.equals("") && !str11.equals("0") && list != null && list.size() > 0) {
/* 1106 */         for (Map map1 : list) {
/* 1107 */           Map map2 = map1;
/* 1108 */           String str = Util.null2String(map2.get("roleid"));
/* 1109 */           int m = Util.getIntValue(Util.null2String(map2.get("rolelevel")));
/* 1110 */           if (str.equals(str11) && m >= i) {
/* 1111 */             bool = true;
/*      */           }
/*      */         } 
/*      */       }
/* 1115 */       if (str7.equals("5")) {
/* 1116 */         bool = true;
/*      */       }
/*      */       
/* 1119 */       if (str7.equals("6") && !str8.equals("") && ((
/* 1120 */         str8.startsWith(",") && str8.indexOf("," + str6 + ",") > -1) || str8.equals(str6))) {
/* 1121 */         if (j == 0) {
/* 1122 */           if ((str9.startsWith(",") && str9.indexOf("," + str2 + ",") > -1) || str9.equals(str2)) {
/* 1123 */             bool = true; continue;
/*      */           } 
/* 1125 */           if (!"".equals(str4)) {
/* 1126 */             if (str4.indexOf(",") > -1) {
/* 1127 */               String[] arrayOfString = Util.TokenizerString2(str4, ",");
/* 1128 */               for (byte b = 0; b < arrayOfString.length; b++) {
/* 1129 */                 if ((str9.startsWith(",") && str9.indexOf("," + arrayOfString[b] + ",") > -1) || str9.equals(arrayOfString[b]))
/* 1130 */                   bool = true; 
/*      */               } 
/*      */               continue;
/*      */             } 
/* 1134 */             if ((str9.startsWith(",") && str9.indexOf("," + str4 + ",") > -1) || str9.equals(str4)) {
/* 1135 */               bool = true;
/*      */             }
/*      */           } 
/*      */           continue;
/*      */         } 
/* 1140 */         if (j == 1) {
/* 1141 */           if ((str9.startsWith(",") && str9.indexOf("," + str3 + ",") > -1) || str9.equals(str3)) {
/* 1142 */             bool = true; continue;
/*      */           } 
/* 1144 */           if (!"".equals(str5)) {
/* 1145 */             if (str5.indexOf(",") > -1) {
/* 1146 */               String[] arrayOfString = Util.TokenizerString2(str5, ",");
/* 1147 */               for (byte b = 0; b < arrayOfString.length; b++) {
/* 1148 */                 if ((str9.startsWith(",") && str9.indexOf("," + arrayOfString[b] + ",") > -1) || str9.equals(arrayOfString[b]))
/* 1149 */                   bool = true; 
/*      */               } 
/*      */               continue;
/*      */             } 
/* 1153 */             if ((str9.startsWith(",") && str9.indexOf("," + str5 + ",") > -1) || str9.equals(str5)) {
/* 1154 */               bool = true;
/*      */             }
/*      */           } 
/*      */           
/*      */           continue;
/*      */         } 
/* 1160 */         bool = true;
/*      */       } 
/*      */     } 
/*      */ 
/*      */ 
/*      */     
/* 1166 */     return bool;
/*      */   }
/*      */   
/*      */   public boolean checkFlowReportByRequest(HttpServletRequest paramHttpServletRequest, User paramUser) {
/* 1170 */     String str = Util.null2String(paramHttpServletRequest.getParameter("reportid"));
/* 1171 */     int i = Util.getIntValue(paramHttpServletRequest.getParameter("requestid"), 0);
/* 1172 */     return checkFlowReportByRequest(str, i, paramUser);
/*      */   }
/*      */   
/*      */   public boolean checkFlowReportByRequest(String paramString, int paramInt, User paramUser) {
/* 1176 */     RecordSet recordSet = new RecordSet();
/* 1177 */     boolean bool1 = false;
/*      */     
/*      */     try {
/* 1180 */       this.rscominfo = new ResourceComInfo();
/* 1181 */       this.rsvcominfo = new ResourceVirtualComInfo();
/* 1182 */     } catch (Exception exception) {
/* 1183 */       exception.printStackTrace();
/*      */     } 
/*      */     
/* 1186 */     String str1 = String.valueOf(paramUser.getUID());
/* 1187 */     String str2 = "";
/* 1188 */     String str3 = "";
/* 1189 */     String str4 = "";
/* 1190 */     String str5 = "";
/* 1191 */     String str6 = "";
/* 1192 */     str6 = this.rscominfo.getJobTitle(str1);
/* 1193 */     str2 = this.rscominfo.getDepartmentID(str1);
/* 1194 */     str3 = this.rscominfo.getSubCompanyID(str1);
/* 1195 */     str4 = this.rsvcominfo.getDepartmentids(str1);
/* 1196 */     str5 = this.rsvcominfo.getSubcompanyids(str1);
/*      */     
/* 1198 */     HrmCommonServiceImpl hrmCommonServiceImpl = new HrmCommonServiceImpl();
/* 1199 */     List list = hrmCommonServiceImpl.getRoleInfo(paramUser.getUID());
/*      */     
/* 1201 */     boolean bool2 = false;
/*      */     try {
/* 1203 */       bool2 = checkFlowreportByUserId(paramString, String.valueOf(paramInt), paramUser);
/* 1204 */     } catch (Exception exception) {
/* 1205 */       exception.printStackTrace();
/*      */     } 
/*      */     
/* 1208 */     recordSet.executeSql("select shareType,sharelevel,userid,departmentid,subcompanyid,roleid,rolelevel,seclevel,seclevel2 from WorkflowReportShare where (reportid=" + paramString + " or reportid=0) and allowlook = 1 ");
/* 1209 */     while (recordSet.next()) {
/* 1210 */       String str7 = Util.null2String(recordSet.getString("shareType"));
/* 1211 */       String str8 = Util.null2String(recordSet.getString("userid"));
/* 1212 */       String str9 = Util.null2String(recordSet.getString("departmentid"));
/* 1213 */       String str10 = Util.null2String(recordSet.getString("subcompanyid"));
/* 1214 */       String str11 = Util.null2String(recordSet.getString("roleid"));
/* 1215 */       int i = recordSet.getInt("rolelevel");
/* 1216 */       int j = recordSet.getInt("sharelevel");
/* 1217 */       int k = Util.getIntValue(recordSet.getString("seclevel"), 0);
/* 1218 */       int m = Util.getIntValue(recordSet.getString("seclevel2"), 0);
/* 1219 */       if (str7.equals("1") && !str8.equals("") && ((
/* 1220 */         str8.startsWith(",") && str8.indexOf("," + str1 + ",") > -1) || str8.equals(str1))) {
/* 1221 */         bool1 = bool2;
/*      */       }
/*      */       
/* 1224 */       if (str7.equals("2") && !str10.equals("")) {
/* 1225 */         if ((str10.startsWith(",") && str10.indexOf("," + str3 + ",") > -1) || str10.equals(str3)) {
/* 1226 */           bool1 = true;
/*      */         }
/* 1228 */         else if (!"".equals(str5)) {
/* 1229 */           if (str5.indexOf(",") > -1) {
/* 1230 */             String[] arrayOfString = Util.TokenizerString2(str5, ",");
/* 1231 */             for (byte b = 0; b < arrayOfString.length; b++) {
/* 1232 */               if ((str10.startsWith(",") && str10.indexOf("," + arrayOfString[b] + ",") > -1) || str10.equals(arrayOfString[b])) {
/* 1233 */                 bool1 = bool2;
/*      */               }
/*      */             }
/*      */           
/* 1237 */           } else if ((str10.startsWith(",") && str10.indexOf("," + str5 + ",") > -1) || str10.equals(str5)) {
/* 1238 */             bool1 = bool2;
/*      */           } 
/*      */         } 
/*      */       }
/*      */ 
/*      */       
/* 1244 */       if (str7.equals("3") && !str9.equals("")) {
/* 1245 */         if ((str9.startsWith(",") && str9.indexOf("," + str2 + ",") > -1) || str9.equals(str2)) {
/* 1246 */           bool1 = bool2;
/*      */         }
/* 1248 */         else if (!"".equals(str4)) {
/* 1249 */           if (str4.indexOf(",") > -1) {
/* 1250 */             String[] arrayOfString = Util.TokenizerString2(str4, ",");
/* 1251 */             for (byte b = 0; b < arrayOfString.length; b++) {
/* 1252 */               if ((str9.startsWith(",") && str9.indexOf("," + arrayOfString[b] + ",") > -1) || str9.equals(arrayOfString[b])) {
/* 1253 */                 bool1 = bool2;
/*      */               }
/*      */             }
/*      */           
/* 1257 */           } else if ((str9.startsWith(",") && str9.indexOf("," + str4 + ",") > -1) || str9.equals(str4)) {
/* 1258 */             bool1 = bool2;
/*      */           } 
/*      */         } 
/*      */       }
/*      */ 
/*      */       
/* 1264 */       if (str7.equals("4") && !str11.equals("") && !str11.equals("0") && list != null && list.size() > 0) {
/* 1265 */         for (Map map1 : list) {
/* 1266 */           Map map2 = map1;
/* 1267 */           String str = Util.null2String(map2.get("roleid"));
/* 1268 */           int n = Util.getIntValue(Util.null2String(map2.get("rolelevel")));
/* 1269 */           if (str.equals(str11) && n >= i) {
/* 1270 */             bool1 = bool2;
/*      */           }
/*      */         } 
/*      */       }
/*      */       
/* 1275 */       if (str7.equals("5")) {
/* 1276 */         bool1 = bool2;
/*      */       }
/*      */       
/* 1279 */       if (str7.equals("6") && !str8.equals("") && ((
/* 1280 */         str8.startsWith(",") && str8.indexOf("," + str6 + ",") > -1) || str8.equals(str6))) {
/* 1281 */         if (k == 0) {
/* 1282 */           if ((str9.startsWith(",") && str9.indexOf("," + str2 + ",") > -1) || str9.equals(str2)) {
/* 1283 */             bool1 = bool2; continue;
/*      */           } 
/* 1285 */           if (!"".equals(str4)) {
/* 1286 */             if (str4.indexOf(",") > -1) {
/* 1287 */               String[] arrayOfString = Util.TokenizerString2(str4, ",");
/* 1288 */               for (byte b = 0; b < arrayOfString.length; b++) {
/* 1289 */                 if ((str9.startsWith(",") && str9.indexOf("," + arrayOfString[b] + ",") > -1) || str9.equals(arrayOfString[b]))
/* 1290 */                   bool1 = bool2; 
/*      */               } 
/*      */               continue;
/*      */             } 
/* 1294 */             if ((str9.startsWith(",") && str9.indexOf("," + str4 + ",") > -1) || str9.equals(str4)) {
/* 1295 */               bool1 = bool2;
/*      */             }
/*      */           } 
/*      */           continue;
/*      */         } 
/* 1300 */         if (k == 1) {
/* 1301 */           if ((str9.startsWith(",") && str9.indexOf("," + str3 + ",") > -1) || str9.equals(str3)) {
/* 1302 */             bool1 = bool2; continue;
/*      */           } 
/* 1304 */           if (!"".equals(str5)) {
/* 1305 */             if (str5.indexOf(",") > -1) {
/* 1306 */               String[] arrayOfString = Util.TokenizerString2(str5, ",");
/* 1307 */               for (byte b = 0; b < arrayOfString.length; b++) {
/* 1308 */                 if ((str9.startsWith(",") && str9.indexOf("," + arrayOfString[b] + ",") > -1) || str9.equals(arrayOfString[b]))
/* 1309 */                   bool1 = bool2; 
/*      */               } 
/*      */               continue;
/*      */             } 
/* 1313 */             if ((str9.startsWith(",") && str9.indexOf("," + str5 + ",") > -1) || str9.equals(str5)) {
/* 1314 */               bool1 = bool2;
/*      */             }
/*      */           } 
/*      */           
/*      */           continue;
/*      */         } 
/* 1320 */         bool1 = bool2;
/*      */       } 
/*      */     } 
/*      */ 
/*      */     
/* 1325 */     return bool1;
/*      */   }
/*      */   
/*      */   public int getMaxShareLevel(String paramString, User paramUser) {
/* 1329 */     RecordSet recordSet = new RecordSet();
/*      */     
/*      */     try {
/* 1332 */       this.rscominfo = new ResourceComInfo();
/* 1333 */       this.rsvcominfo = new ResourceVirtualComInfo();
/* 1334 */     } catch (Exception exception) {
/* 1335 */       exception.printStackTrace();
/*      */     } 
/* 1337 */     int i = -1;
/* 1338 */     String str1 = "";
/* 1339 */     String str2 = "";
/* 1340 */     String str3 = "";
/* 1341 */     String str4 = "";
/* 1342 */     String str5 = String.valueOf(paramUser.getUID());
/* 1343 */     ArrayList<String> arrayList = new ArrayList();
/* 1344 */     String str6 = "";
/*      */     
/* 1346 */     str6 = this.rscominfo.getJobTitle(str5);
/* 1347 */     str1 = this.rscominfo.getDepartmentID(str5);
/* 1348 */     str2 = this.rscominfo.getSubCompanyID(str5);
/* 1349 */     str3 = this.rsvcominfo.getDepartmentids(str5);
/* 1350 */     str4 = this.rsvcominfo.getSubcompanyids(str5);
/*      */     
/* 1352 */     HrmCommonServiceImpl hrmCommonServiceImpl = new HrmCommonServiceImpl();
/* 1353 */     List list = hrmCommonServiceImpl.getRoleInfo(paramUser.getUID());
/*      */ 
/*      */     
/* 1356 */     recordSet.executeSql("select shareType,userid,departmentid,subcompanyid,roleid,rolelevel,sharelevel,seclevel,seclevel2 from WorkflowReportShare where reportid=" + paramString);
/* 1357 */     while (recordSet.next()) {
/* 1358 */       String str7 = Util.null2String(recordSet.getString("shareType"));
/* 1359 */       String str8 = Util.null2String(recordSet.getString("userid"));
/* 1360 */       String str9 = Util.null2String(recordSet.getString("departmentid"));
/* 1361 */       String str10 = Util.null2String(recordSet.getString("subcompanyid"));
/* 1362 */       String str11 = Util.null2String(recordSet.getString("roleid"));
/* 1363 */       int j = recordSet.getInt("rolelevel");
/* 1364 */       int k = Util.getIntValue(recordSet.getString("seclevel"), 0);
/* 1365 */       int m = Util.getIntValue(recordSet.getString("seclevel2"), 0);
/* 1366 */       String str12 = Util.null2String(recordSet.getString("sharelevel"));
/* 1367 */       if (str7.equals("1") && !str8.equals("") && ((
/* 1368 */         str8.startsWith(",") && str8.indexOf("," + str5 + ",") > -1) || str8.equals(str5))) {
/* 1369 */         arrayList.add(str12);
/*      */       }
/*      */       
/* 1372 */       if (str7.equals("2") && !str10.equals("")) {
/* 1373 */         if ((str10.startsWith(",") && str10.indexOf("," + str2 + ",") > -1) || str10.equals(str2)) {
/* 1374 */           arrayList.add(str12);
/*      */         }
/* 1376 */         else if (!"".equals(str4)) {
/* 1377 */           if (str4.indexOf(",") > -1) {
/* 1378 */             String[] arrayOfString = Util.TokenizerString2(str4, ",");
/* 1379 */             for (byte b = 0; b < arrayOfString.length; b++) {
/* 1380 */               if ((str10.startsWith(",") && str10.indexOf("," + arrayOfString[b] + ",") > -1) || str10.equals(arrayOfString[b])) {
/* 1381 */                 arrayList.add(str12);
/*      */               }
/*      */             }
/*      */           
/* 1385 */           } else if ((str10.startsWith(",") && str10.indexOf("," + str4 + ",") > -1) || str10.equals(str4)) {
/* 1386 */             arrayList.add(str12);
/*      */           } 
/*      */         } 
/*      */       }
/*      */ 
/*      */       
/* 1392 */       if (str7.equals("3") && !str9.equals("")) {
/* 1393 */         if ((str9.startsWith(",") && str9.indexOf("," + str1 + ",") > -1) || str9.equals(str1)) {
/* 1394 */           arrayList.add(str12);
/*      */         }
/* 1396 */         else if (!"".equals(str3)) {
/* 1397 */           if (str3.indexOf(",") > -1) {
/* 1398 */             String[] arrayOfString = Util.TokenizerString2(str3, ",");
/* 1399 */             for (byte b = 0; b < arrayOfString.length; b++) {
/* 1400 */               if ((str9.startsWith(",") && str9.indexOf("," + arrayOfString[b] + ",") > -1) || str9.equals(arrayOfString[b])) {
/* 1401 */                 arrayList.add(str12);
/*      */               }
/*      */             }
/*      */           
/* 1405 */           } else if ((str9.startsWith(",") && str9.indexOf("," + str3 + ",") > -1) || str9.equals(str3)) {
/* 1406 */             arrayList.add(str12);
/*      */           } 
/*      */         } 
/*      */       }
/*      */ 
/*      */       
/* 1412 */       if (str7.equals("4") && !str11.equals("") && !str11.equals("0") && list != null && list.size() > 0) {
/* 1413 */         for (Map map1 : list) {
/* 1414 */           Map map2 = map1;
/* 1415 */           String str = Util.null2String(map2.get("roleid"));
/* 1416 */           int n = Util.getIntValue(Util.null2String(map2.get("rolelevel")));
/* 1417 */           if (str.equals(str11) && n >= j) {
/* 1418 */             arrayList.add(str12);
/*      */           }
/*      */         } 
/*      */       }
/* 1422 */       if (str7.equals("5")) {
/* 1423 */         arrayList.add(str12);
/*      */       }
/*      */       
/* 1426 */       if (str7.equals("6") && !str8.equals("") && ((
/* 1427 */         str8.startsWith(",") && str8.indexOf("," + str6 + ",") > -1) || str8.equals(str6))) {
/* 1428 */         if (k == 0) {
/* 1429 */           if ((str9.startsWith(",") && str9.indexOf("," + str1 + ",") > -1) || str9.equals(str1)) {
/* 1430 */             arrayList.add(str12); continue;
/*      */           } 
/* 1432 */           if (!"".equals(str3)) {
/* 1433 */             if (str3.indexOf(",") > -1) {
/* 1434 */               String[] arrayOfString = Util.TokenizerString2(str3, ",");
/* 1435 */               for (byte b = 0; b < arrayOfString.length; b++) {
/* 1436 */                 if ((str9.startsWith(",") && str9.indexOf("," + arrayOfString[b] + ",") > -1) || str9.equals(arrayOfString[b]))
/* 1437 */                   arrayList.add(str12); 
/*      */               } 
/*      */               continue;
/*      */             } 
/* 1441 */             if ((str9.startsWith(",") && str9.indexOf("," + str3 + ",") > -1) || str9.equals(str3)) {
/* 1442 */               arrayList.add(str12);
/*      */             }
/*      */           } 
/*      */           continue;
/*      */         } 
/* 1447 */         if (k == 1) {
/* 1448 */           if ((str9.startsWith(",") && str9.indexOf("," + str2 + ",") > -1) || str9.equals(str2)) {
/* 1449 */             arrayList.add(str12); continue;
/*      */           } 
/* 1451 */           if (!"".equals(str4)) {
/* 1452 */             if (str4.indexOf(",") > -1) {
/* 1453 */               String[] arrayOfString = Util.TokenizerString2(str4, ",");
/* 1454 */               for (byte b = 0; b < arrayOfString.length; b++) {
/* 1455 */                 if ((str9.startsWith(",") && str9.indexOf("," + arrayOfString[b] + ",") > -1) || str9.equals(arrayOfString[b]))
/* 1456 */                   arrayList.add(str12); 
/*      */               } 
/*      */               continue;
/*      */             } 
/* 1460 */             if ((str9.startsWith(",") && str9.indexOf("," + str4 + ",") > -1) || str9.equals(str4)) {
/* 1461 */               arrayList.add(str12);
/*      */             }
/*      */           } 
/*      */           
/*      */           continue;
/*      */         } 
/* 1467 */         arrayList.add(str12);
/*      */       } 
/*      */     } 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/* 1474 */     if (arrayList.size() > 0) {
/* 1475 */       i = Integer.parseInt(Collections.<String>max(arrayList));
/*      */     }
/* 1477 */     return i;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean checkFlowreportByUserId(String paramString1, String paramString2, User paramUser) throws Exception {
/* 1486 */     boolean bool = false;
/* 1487 */     RecordSet recordSet = new RecordSet();
/* 1488 */     this.reportShare = new ReportShare();
/* 1489 */     this.rscominfo = new ResourceComInfo();
/* 1490 */     this.rsvcominfo = new ResourceVirtualComInfo();
/* 1491 */     this.deptcominfo = new DepartmentComInfo();
/* 1492 */     this.deptvcominfo = new DepartmentVirtualComInfo();
/* 1493 */     int i = -1;
/* 1494 */     int j = -1;
/* 1495 */     int k = -1;
/* 1496 */     int m = -1;
/* 1497 */     int n = -1;
/* 1498 */     byte b = -1;
/* 1499 */     String str1 = String.valueOf(paramUser.getUID());
/* 1500 */     String str2 = "";
/* 1501 */     String str3 = "";
/* 1502 */     String str4 = "";
/* 1503 */     String str5 = "";
/* 1504 */     String str6 = "";
/* 1505 */     String str7 = "";
/* 1506 */     String str8 = "";
/* 1507 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*      */     
/* 1509 */     str8 = this.rscominfo.getJobTitle(str1);
/* 1510 */     str4 = this.rscominfo.getDepartmentID(str1);
/* 1511 */     str5 = this.rscominfo.getSubCompanyID(str1);
/* 1512 */     str6 = this.rsvcominfo.getDepartmentids(str1);
/* 1513 */     str7 = this.rsvcominfo.getSubcompanyids(str1);
/*      */     
/* 1515 */     String str9 = "";
/* 1516 */     String str10 = "";
/* 1517 */     if (!"".equals(str6)) {
/* 1518 */       str9 = str4 + "," + str6;
/*      */     }
/* 1520 */     if (!"".equals(str7)) {
/* 1521 */       str10 = str5 + "," + str7;
/*      */     }
/*      */     
/* 1524 */     HrmCommonServiceImpl hrmCommonServiceImpl = new HrmCommonServiceImpl();
/* 1525 */     List list = hrmCommonServiceImpl.getRoleInfo(paramUser.getUID());
/*      */     
/* 1527 */     recordSet.executeSql("SELECT shareType,userid,departmentid,subcompanyid,roleid,rolelevel,allowlook,sharelevel,mutidepartmentid,seclevel,seclevel2 FROM WorkflowReportShare WHERE (reportid=" + paramString1 + " or reportid=0)");
/*      */     
/* 1529 */     while (recordSet.next()) {
/* 1530 */       String str16 = Util.null2String(recordSet.getString("shareType"));
/* 1531 */       String str17 = Util.null2String(recordSet.getString("userid"));
/* 1532 */       String str18 = Util.null2String(recordSet.getString("departmentid"));
/* 1533 */       String str19 = Util.null2String(recordSet.getString("subcompanyid"));
/* 1534 */       String str20 = Util.null2String(recordSet.getString("roleid"));
/* 1535 */       int i1 = recordSet.getInt("rolelevel");
/* 1536 */       String str21 = Util.null2String(recordSet.getString("allowlook"));
/* 1537 */       String str22 = Util.null2String(recordSet.getString("sharelevel"));
/* 1538 */       String str23 = Util.null2String(recordSet.getString("mutidepartmentid"));
/* 1539 */       int i2 = Util.getIntValue(recordSet.getString("seclevel"), 0);
/* 1540 */       if (str16.equals("1") && !str17.equals("") && str21.equals("1") && ((
/* 1541 */         str17.startsWith(",") && str17.indexOf("," + str1 + ",") > -1) || str17.equals(str1))) {
/* 1542 */         hashMap.put(str22 + "#" + str23, str23);
/*      */       }
/*      */       
/* 1545 */       if (str16.equals("2") && !str19.equals("") && str21.equals("1")) {
/* 1546 */         if ((str19.startsWith(",") && str19.indexOf("," + str5 + ",") > -1) || str19.equals(str5)) {
/* 1547 */           hashMap.put(str22 + "#" + str23, str23);
/*      */         }
/* 1549 */         else if (!"".equals(str7)) {
/* 1550 */           if (str7.indexOf(",") > -1) {
/* 1551 */             String[] arrayOfString = Util.TokenizerString2(str7, ",");
/* 1552 */             for (byte b1 = 0; b1 < arrayOfString.length; b1++) {
/* 1553 */               if ((str19.startsWith(",") && str19.indexOf("," + arrayOfString[b1] + ",") > -1) || str19.equals(arrayOfString[b1])) {
/* 1554 */                 hashMap.put(str22 + "#" + str23, str23);
/*      */               }
/*      */             }
/*      */           
/* 1558 */           } else if ((str19.startsWith(",") && str19.indexOf("," + str7 + ",") > -1) || str19.equals(str7)) {
/* 1559 */             hashMap.put(str22 + "#" + str23, str23);
/*      */           } 
/*      */         } 
/*      */       }
/*      */ 
/*      */       
/* 1565 */       if (str16.equals("3") && !str18.equals("") && str21.equals("1")) {
/* 1566 */         if ((str18.startsWith(",") && str18.indexOf("," + str4 + ",") > -1) || str18.equals(str4)) {
/* 1567 */           hashMap.put(str22 + "#" + str23, str23);
/*      */         }
/* 1569 */         else if (!"".equals(str6)) {
/* 1570 */           if (str6.indexOf(",") > -1) {
/* 1571 */             String[] arrayOfString = Util.TokenizerString2(str6, ",");
/* 1572 */             for (byte b1 = 0; b1 < arrayOfString.length; b1++) {
/* 1573 */               if ((str18.startsWith(",") && str18.indexOf("," + arrayOfString[b1] + ",") > -1) || str18.equals(arrayOfString[b1])) {
/* 1574 */                 hashMap.put(str22 + "#" + str23, str23);
/*      */               }
/*      */             }
/*      */           
/* 1578 */           } else if ((str18.startsWith(",") && str18.indexOf("," + str6 + ",") > -1) || str18.equals(str6)) {
/* 1579 */             hashMap.put(str22 + "#" + str23, str23);
/*      */           } 
/*      */         } 
/*      */       }
/*      */ 
/*      */       
/* 1585 */       if (str16.equals("4") && !str20.equals("") && !str20.equals("0") && list != null && list.size() > 0 && str21.equals("1")) {
/* 1586 */         for (Map map1 : list) {
/* 1587 */           Map map2 = map1;
/* 1588 */           String str = Util.null2String(map2.get("roleid"));
/* 1589 */           int i3 = Util.getIntValue(Util.null2String(map2.get("rolelevel")));
/* 1590 */           if (str.equals(str20) && i3 >= i1) {
/* 1591 */             hashMap.put(str22 + "#" + str23, str23);
/*      */           }
/*      */         } 
/*      */       }
/*      */       
/* 1596 */       if (str16.equals("5") && str21.equals("1")) {
/* 1597 */         hashMap.put(str22 + "#" + str23, str23);
/*      */       }
/*      */       
/* 1600 */       if (str16.equals("6") && !str17.equals("") && str21.equals("1") && ((
/* 1601 */         str17.startsWith(",") && str17.indexOf("," + str8 + ",") > -1) || str17.equals(str8))) {
/* 1602 */         if (i2 == 0) {
/* 1603 */           if ((str18.startsWith(",") && str18.indexOf("," + str4 + ",") > -1) || str18.equals(str4)) {
/* 1604 */             hashMap.put(str22 + "#" + str23, str23); continue;
/*      */           } 
/* 1606 */           if (!"".equals(str6)) {
/* 1607 */             if (str6.indexOf(",") > -1) {
/* 1608 */               String[] arrayOfString = Util.TokenizerString2(str6, ",");
/* 1609 */               for (byte b1 = 0; b1 < arrayOfString.length; b1++) {
/* 1610 */                 if ((str18.startsWith(",") && str18.indexOf("," + arrayOfString[b1] + ",") > -1) || str18.equals(arrayOfString[b1]))
/* 1611 */                   hashMap.put(str22 + "#" + str23, str23); 
/*      */               } 
/*      */               continue;
/*      */             } 
/* 1615 */             if ((str18.startsWith(",") && str18.indexOf("," + str6 + ",") > -1) || str18.equals(str6)) {
/* 1616 */               hashMap.put(str22 + "#" + str23, str23);
/*      */             }
/*      */           } 
/*      */           continue;
/*      */         } 
/* 1621 */         if (i2 == 1) {
/* 1622 */           if ((str18.startsWith(",") && str18.indexOf("," + str5 + ",") > -1) || str18.equals(str5)) {
/* 1623 */             hashMap.put(str22 + "#" + str23, str23); continue;
/*      */           } 
/* 1625 */           if (!"".equals(str7)) {
/* 1626 */             if (str7.indexOf(",") > -1) {
/* 1627 */               String[] arrayOfString = Util.TokenizerString2(str7, ",");
/* 1628 */               for (byte b1 = 0; b1 < arrayOfString.length; b1++) {
/* 1629 */                 if ((str18.startsWith(",") && str18.indexOf("," + arrayOfString[b1] + ",") > -1) || str18.equals(arrayOfString[b1]))
/* 1630 */                   hashMap.put(str22 + "#" + str23, str23); 
/*      */               } 
/*      */               continue;
/*      */             } 
/* 1634 */             if ((str18.startsWith(",") && str18.indexOf("," + str7 + ",") > -1) || str18.equals(str7)) {
/* 1635 */               hashMap.put(str22 + "#" + str23, str23);
/*      */             }
/*      */           } 
/*      */           
/*      */           continue;
/*      */         } 
/* 1641 */         hashMap.put(str22 + "#" + str23, str23);
/*      */       } 
/*      */     } 
/*      */ 
/*      */ 
/*      */     
/* 1647 */     String str11 = "";
/* 1648 */     String str12 = "";
/* 1649 */     if (!hashMap.isEmpty()) {
/* 1650 */       for (String str : hashMap.keySet()) {
/* 1651 */         String[] arrayOfString = Util.TokenizerString2(str, "#");
/* 1652 */         if (arrayOfString[0].equals("9")) {
/* 1653 */           m = Integer.parseInt(arrayOfString[0]);
/* 1654 */           str2 = str2 + (String)hashMap.get(str) + ","; continue;
/* 1655 */         }  if (arrayOfString[0].equals("4")) {
/* 1656 */           n = Integer.parseInt(arrayOfString[0]);
/* 1657 */           str3 = str3 + (String)hashMap.get(str) + ","; continue;
/*      */         } 
/* 1659 */         if (arrayOfString[0].equals("3")) {
/* 1660 */           k = Integer.parseInt(arrayOfString[0]); continue;
/* 1661 */         }  if (arrayOfString[0].equals("2")) {
/* 1662 */           i = Integer.parseInt(arrayOfString[0]); continue;
/*      */         } 
/* 1664 */         j = Integer.parseInt(arrayOfString[0]);
/* 1665 */         if (j == 0) {
/* 1666 */           if (str11.equals("")) {
/* 1667 */             str11 = "" + str9;
/*      */           } else {
/* 1669 */             str11 = str11 + "," + str9;
/*      */           } 
/*      */         }
/* 1672 */         if (j == 1) {
/* 1673 */           if (str12.equals("")) {
/* 1674 */             str12 = str10; continue;
/*      */           } 
/* 1676 */           str12 = str12 + "," + str10;
/*      */         } 
/*      */       } 
/*      */     }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/* 1685 */     if (!"".equals(str2) && 
/* 1686 */       str2.endsWith(",")) {
/* 1687 */       str2 = str2.substring(0, str2.length() - 1);
/*      */     }
/*      */     
/* 1690 */     if (!"".equals(str3) && 
/* 1691 */       str3.endsWith(",")) {
/* 1692 */       str3 = str3.substring(0, str3.length() - 1);
/*      */     }
/*      */ 
/*      */ 
/*      */     
/* 1697 */     if (i == -1 && j == -1 && k == -1 && m == -1 && n == -1 && b == -1) {
/* 1698 */       return false;
/*      */     }
/*      */ 
/*      */     
/* 1702 */     if (k == 3) {
/*      */       
/* 1704 */       String str16 = "";
/* 1705 */       if (!"".equals(str4) && !"".equals(DepartmentComInfo.getAllChildDepartId(str4, ""))) {
/* 1706 */         str16 = str16 + "," + DepartmentComInfo.getAllChildDepartId(str4, "");
/*      */       }
/* 1708 */       if (str11.equals("")) {
/* 1709 */         str11 = str16;
/*      */       } else {
/* 1711 */         str11 = str11 + "," + str16;
/*      */       } 
/*      */       
/* 1714 */       String str17 = "";
/* 1715 */       if (!"".equals(str6)) {
/* 1716 */         if (str6.indexOf(",") > -1) {
/* 1717 */           String[] arrayOfString = Util.TokenizerString2(str6, ",");
/* 1718 */           for (byte b1 = 0; b1 < arrayOfString.length; b1++) {
/* 1719 */             if (!"".equals(DepartmentVirtualComInfo.getAllChildDepartId(arrayOfString[b1], ""))) {
/* 1720 */               if ("".equals(str17)) {
/* 1721 */                 str17 = DepartmentVirtualComInfo.getAllChildDepartId(arrayOfString[b1], "");
/*      */               } else {
/* 1723 */                 str17 = str17 + "," + DepartmentVirtualComInfo.getAllChildDepartId(arrayOfString[b1], "");
/*      */               }
/*      */             
/*      */             }
/*      */           } 
/* 1728 */         } else if (!"".equals(DepartmentVirtualComInfo.getAllChildDepartId(str6, ""))) {
/* 1729 */           str17 = str17 + "," + DepartmentVirtualComInfo.getAllChildDepartId(str6, "");
/*      */         } 
/*      */       }
/*      */       
/* 1733 */       if (!"".equals(str17)) {
/* 1734 */         str11 = str11 + "," + str17;
/*      */       }
/* 1736 */       while (str11.indexOf(",,") > -1) {
/* 1737 */         str11 = str11.replaceAll(",,", ",");
/*      */       }
/* 1739 */       if (str11.indexOf(",") == 0) {
/* 1740 */         str11 = str11.substring(1, str11.length());
/*      */       }
/* 1742 */       if (str11.endsWith(",")) {
/* 1743 */         str11 = str11.substring(0, str11.length() - 1);
/*      */       }
/*      */     } 
/*      */     
/* 1747 */     if (m == 9) {
/* 1748 */       if (str11.equals("")) { str11 = str2; }
/* 1749 */       else { str11 = str11 + "," + str2; }
/*      */     
/*      */     }
/* 1752 */     if (n == 4) {
/* 1753 */       if (str12.equals("")) { str12 = str3; }
/* 1754 */       else { str12 = str12 + "," + str3; }
/*      */     
/*      */     }
/* 1757 */     if (str11.equals("")) str11 = "-100"; 
/* 1758 */     if (str12.equals("")) str12 = "-100"; 
/* 1759 */     String str13 = "";
/*      */     
/* 1761 */     String str14 = "";
/* 1762 */     if (recordSet.getDBType().equals("oracle")) {
/*      */       
/* 1764 */       str14 = str14 + " and nvl(r.currentstatus,-1) = -1 ";
/* 1765 */     } else if (recordSet.getDBType().equals("mysql")) {
/* 1766 */       str14 = str14 + " and ifnull(r.currentstatus,-1) = -1 ";
/*      */     } else {
/*      */       
/* 1769 */       str14 = str14 + " and isnull(r.currentstatus,-1) = -1 ";
/*      */     } 
/*      */     
/* 1772 */     String str15 = "";
/* 1773 */     if (!str13.equals("")) {
/* 1774 */       str15 = "c.userid,";
/*      */     }
/* 1776 */     StringBuffer stringBuffer = new StringBuffer();
/* 1777 */     stringBuffer.append(" SELECT DISTINCT c.requestid FROM (SELECT DISTINCT ")
/* 1778 */       .append(" b.formid,b.id,r.requestid, ");
/* 1779 */     if (!str15.equals("")) {
/* 1780 */       stringBuffer.append(str15);
/*      */     }
/* 1782 */     stringBuffer.append(" c.userid FROM workflow_requestbase r,workflow_base b,workflow_currentoperator c ");
/* 1783 */     stringBuffer.append(" WHERE r.workflowid=b.id AND r.requestid = c.requestid and r.requestid = " + paramString2 + str14 + " ) c  INNER JOIN hrmresource e ");
/*      */     
/* 1785 */     if (recordSet.getOrgindbtype().equalsIgnoreCase("gs") || recordSet.getDBType().equals("mysql")) {
/* 1786 */       stringBuffer.append(" INNER JOIN HrmDepartmentAllView f ON e.departmentid=f.id and (c.userid=e.id or c.userid=1)");
/*      */     } else {
/* 1788 */       stringBuffer.append(" INNER JOIN HrmDepartmentAllView f ON e.departmentid=f.id ON (c.userid=e.id or c.userid=1)");
/*      */     } 
/*      */     
/* 1791 */     stringBuffer.append(" INNER JOIN HrmSubCompanyAllView  h ON e.subcompanyid1 = h.id ");
/* 1792 */     if (i != 2) {
/* 1793 */       stringBuffer.append(" and (f.id in(" + str11 + ") " + str13)
/* 1794 */         .append(" OR h.id IN(" + str12 + ") ) ");
/*      */     }
/* 1796 */     recordSet.executeSql(stringBuffer.toString());
/* 1797 */     recordSet.writeLog(" flowReport Authorization  user:" + paramUser.getLastname() + ", SQL:" + stringBuffer.toString());
/* 1798 */     if (recordSet.next()) {
/* 1799 */       bool = true;
/*      */     }
/* 1801 */     return bool;
/*      */   }
/*      */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/report/ReportAuthorization.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */