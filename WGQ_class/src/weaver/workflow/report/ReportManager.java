/*     */ package weaver.workflow.report;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.List;
/*     */ import weaver.common.util.xtree.TreeNode;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.systemright.CheckSubCompanyRight;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class ReportManager
/*     */ {
/*     */   public TreeNode getReportTypeList(TreeNode paramTreeNode, String paramString1, String paramString2, String paramString3, int paramInt1, int paramInt2) throws Exception {
/*  35 */     RecordSet recordSet = new RecordSet();
/*  36 */     StringBuffer stringBuffer = new StringBuffer();
/*  37 */     if (paramInt1 == 1) {
/*  38 */       stringBuffer.append("select id,typename from Workflow_ReportType ");
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/*  51 */       stringBuffer.append(" order by typeorder");
/*  52 */       recordSet.executeSql(stringBuffer.toString());
/*  53 */       while (recordSet.next()) {
/*  54 */         String str1 = Util.null2String(recordSet.getString("id"));
/*  55 */         if (str1.equals(""))
/*  56 */           str1 = "0"; 
/*  57 */         String str2 = Util.null2String(recordSet.getString("typename"));
/*  58 */         TreeNode treeNode = new TreeNode();
/*  59 */         treeNode.setTitle(str2);
/*  60 */         treeNode.setNodeId("reportType_" + str1);
/*  61 */         treeNode.setIcon("/LeftMenu/ThemeXP/folder1_wev8.gif");
/*  62 */         treeNode.setHref("/workflow/report/ReportManageTab.jsp?otype=" + str1 + "&subcompanyid=" + paramString2);
/*     */         
/*  64 */         treeNode.setTarget("wfmainFrame");
/*     */ 
/*     */         
/*  67 */         paramTreeNode.addTreeNode(treeNode);
/*     */       } 
/*     */     } else {
/*  70 */       stringBuffer.append("select id,typename from Workflow_ReportType order by typeorder");
/*  71 */       recordSet.executeSql(stringBuffer.toString());
/*  72 */       while (recordSet.next()) {
/*  73 */         String str1 = Util.null2String(recordSet.getString("id"));
/*  74 */         if (str1.equals(""))
/*  75 */           str1 = "0"; 
/*  76 */         String str2 = Util.null2String(recordSet.getString("typename"));
/*  77 */         TreeNode treeNode = new TreeNode();
/*  78 */         treeNode.setTitle(str2);
/*  79 */         treeNode.setNodeId("reportType_" + str1);
/*  80 */         treeNode.setIcon("/LeftMenu/ThemeXP/folder1_wev8.gif");
/*  81 */         treeNode.setHref("/workflow/report/ReportManageTab.jsp?otype=" + str1 + "&typename=" + str2);
/*     */         
/*  83 */         treeNode.setTarget("wfmainFrame");
/*     */ 
/*     */         
/*  86 */         paramTreeNode.addTreeNode(treeNode);
/*     */       } 
/*     */     } 
/*     */     
/*  90 */     return paramTreeNode;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getCheckBox(String paramString) {
/*  99 */     String[] arrayOfString = Util.TokenizerString2(paramString, "+");
/* 100 */     int i = Util.getIntValue(arrayOfString[0], 0);
/* 101 */     int j = Util.getIntValue(arrayOfString[1], 0);
/* 102 */     int k = Util.getIntValue(arrayOfString[2], 0);
/*     */     
/* 104 */     if (k == 1 && j != 1) {
/*     */       try {
/* 106 */         CheckSubCompanyRight checkSubCompanyRight = new CheckSubCompanyRight();
/* 107 */         int m = checkSubCompanyRight.ChkComRightByUserRightCompanyId(j, "WorkflowReportManage:All", i);
/* 108 */         return (m > 1) ? "true" : "false";
/* 109 */       } catch (Exception exception) {
/* 110 */         exception.printStackTrace();
/*     */       } 
/*     */     }
/* 113 */     return "true";
/*     */   }
/*     */ 
/*     */   
/*     */   public static List<String> getCanDelTypeList(String paramString1, String paramString2) {
/* 118 */     ArrayList<String> arrayList = new ArrayList();
/* 119 */     String[] arrayOfString = Util.TokenizerString2(paramString2, "+");
/* 120 */     int i = Util.getIntValue(arrayOfString[0], 0);
/* 121 */     int j = Util.getIntValue(arrayOfString[1], 0);
/* 122 */     int k = Util.getIntValue(arrayOfString[2], 0);
/* 123 */     if (k == 1 && j != 1) {
/*     */       
/*     */       try {
/* 126 */         CheckSubCompanyRight checkSubCompanyRight = new CheckSubCompanyRight();
/* 127 */         int m = checkSubCompanyRight.ChkComRightByUserRightCompanyId(j, "WorkflowReportManage:All", i);
/* 128 */         arrayList.add("true");
/* 129 */         arrayList.add("true");
/* 130 */         arrayList.add((m > 1) ? "true" : "false");
/* 131 */       } catch (Exception exception) {
/* 132 */         exception.printStackTrace();
/*     */       } 
/*     */     } else {
/*     */       
/* 136 */       arrayList.add("true");
/* 137 */       arrayList.add("true");
/* 138 */       arrayList.add("true");
/*     */     } 
/*     */     
/* 141 */     return arrayList;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/report/ReportManager.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */