/*    */ package weaver.workflow.report;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class ReportCompositorOrderBean
/*    */   implements Serializable
/*    */ {
/*    */   private int compositorOrder;
/*    */   private String orderType;
/*    */   private String fieldName;
/*    */   private String sqlFlag;
/*    */   
/*    */   public void setCompositorOrder(int paramInt) {
/* 28 */     this.compositorOrder = paramInt;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public int getCompositorOrder() {
/* 35 */     return this.compositorOrder;
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public void setOrderType(String paramString) {
/* 41 */     this.orderType = paramString;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getOrderType() {
/* 48 */     return this.orderType;
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public void setFieldName(String paramString) {
/* 54 */     this.fieldName = paramString;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getFieldName() {
/* 61 */     return this.fieldName;
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public void setSqlFlag(String paramString) {
/* 67 */     this.sqlFlag = paramString;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getSqlFlag() {
/* 74 */     return this.sqlFlag;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/report/ReportCompositorOrderBean.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */