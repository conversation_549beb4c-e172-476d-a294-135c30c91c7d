/*    */ package weaver.workflow.report;
/*    */ import weaver.general.ThreadVarLanguage;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ import weaver.workflow.workflow.WorkTypeComInfo;
/*    */ import weaver.workflow.workflow.WorkflowComInfo;
/*    */ 
/*    */ public class MostSpendTimeSort extends BaseBean {
/*    */   public String getFlowTypeName(String paramString) throws Exception {
/*  9 */     WorkTypeComInfo workTypeComInfo = new WorkTypeComInfo();
/* 10 */     WorkflowComInfo workflowComInfo = new WorkflowComInfo();
/* 11 */     paramString = WorkflowVersion.getActiveVersionWFID(paramString);
/* 12 */     return workTypeComInfo.getWorkTypename(workflowComInfo.getWorkflowtype(paramString));
/*    */   }
/*    */   
/*    */   public String getFlowName(String paramString) throws Exception {
/* 16 */     WorkflowComInfo workflowComInfo = new WorkflowComInfo();
/* 17 */     return workflowComInfo.getWorkflowname(paramString);
/*    */   }
/*    */   
/*    */   public String getSpendsString(String paramString) {
/* 21 */     double d = Util.getDoubleValue(paramString);
/* 22 */     if (d <= 0.0D) {
/* 23 */       return "0" + SystemEnv.getHtmlLabelName(15049, ThreadVarLanguage.getLang()) + "";
/*    */     }
/* 25 */     int i = (int)d;
/* 26 */     int j = (int)((d - i) * 60.0D + 0.5D);
/* 27 */     StringBuilder stringBuilder = new StringBuilder();
/* 28 */     if (i >= 24) {
/* 29 */       stringBuilder.append(i / 24).append("" + SystemEnv.getHtmlLabelName(383378, ThreadVarLanguage.getLang()) + "");
/*    */     }
/* 31 */     if (i > 0) {
/* 32 */       stringBuilder.append(i % 24).append("" + SystemEnv.getHtmlLabelName(391, ThreadVarLanguage.getLang()) + "");
/*    */     }
/* 34 */     stringBuilder.append(j).append("" + SystemEnv.getHtmlLabelName(15049, ThreadVarLanguage.getLang()) + "");
/* 35 */     return stringBuilder.toString();
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/report/MostSpendTimeSort.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */