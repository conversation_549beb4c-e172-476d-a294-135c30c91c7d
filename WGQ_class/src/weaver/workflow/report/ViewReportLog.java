/*     */ package weaver.workflow.report;
/*     */ 
/*     */ import com.google.common.base.Strings;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.workflow.workflow.WorkflowComInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ public class ViewReportLog
/*     */ {
/*     */   public String getViewReportDateTime(String paramString1, String paramString2) {
/*  15 */     return paramString1 + " " + paramString2;
/*     */   }
/*     */   
/*     */   public String getLogType(String paramString1, String paramString2) {
/*  19 */     int i = Util.getIntValue(paramString2, 7);
/*  20 */     String str = "";
/*  21 */     switch (paramString1) {
/*     */       case "1":
/*  23 */         str = SystemEnv.getHtmlLabelName(86, i);
/*     */         break;
/*     */       case "2":
/*  26 */         str = SystemEnv.getHtmlLabelName(615, i);
/*     */         break;
/*     */       case "3":
/*  29 */         str = SystemEnv.getHtmlLabelName(236, i);
/*     */         break;
/*     */       case "4":
/*  32 */         str = SystemEnv.getHtmlLabelName(244, i);
/*     */         break;
/*     */       case "5":
/*  35 */         str = SystemEnv.getHtmlLabelName(91, i);
/*     */         break;
/*     */       case "6":
/*  38 */         str = SystemEnv.getHtmlLabelName(737, i);
/*     */         break;
/*     */       case "7":
/*  41 */         str = SystemEnv.getHtmlLabelName(6011, i);
/*     */         break;
/*     */       case "9":
/*  44 */         str = SystemEnv.getHtmlLabelName(1006, i);
/*     */         break;
/*     */       case "0":
/*  47 */         str = SystemEnv.getHtmlLabelName(142, i);
/*     */         break;
/*     */       case "a":
/*  50 */         str = SystemEnv.getHtmlLabelName(503278, i);
/*     */         break;
/*     */       case "b":
/*  53 */         str = SystemEnv.getHtmlLabelName(388499, i);
/*     */         break;
/*     */       case "c":
/*  56 */         str = SystemEnv.getHtmlLabelName(382667, i);
/*     */         break;
/*     */       case "e":
/*  59 */         str = SystemEnv.getHtmlLabelName(18360, i);
/*     */         break;
/*     */       case "h":
/*  62 */         str = SystemEnv.getHtmlLabelName(132319, i);
/*     */         break;
/*     */       case "t":
/*  65 */         str = SystemEnv.getHtmlLabelName(2084, i);
/*     */         break;
/*     */       case "s":
/*  68 */         str = SystemEnv.getHtmlLabelName(21223, i);
/*     */         break;
/*     */       case "y":
/*  71 */         str = SystemEnv.getHtmlLabelName(511515, i);
/*     */         break;
/*     */       case "z":
/*  74 */         str = SystemEnv.getHtmlLabelName(507272, i);
/*     */         break;
/*     */       case "r":
/*  77 */         str = SystemEnv.getHtmlLabelName(517785, i);
/*     */         break;
/*     */       case "i":
/*  80 */         str = SystemEnv.getHtmlLabelName(18913, i);
/*     */         break;
/*     */     } 
/*     */     
/*  84 */     return str;
/*     */   }
/*     */   
/*     */   public String getViewReportWorkflowName(String paramString) {
/*  88 */     String str = "";
/*     */     try {
/*  90 */       str = (new WorkflowComInfo()).getWorkflowname(paramString);
/*  91 */     } catch (Exception exception) {
/*     */       
/*  93 */       exception.printStackTrace();
/*     */     } 
/*  95 */     return str;
/*     */   }
/*     */   
/*     */   public String getViewReportCreater(String paramString) {
/*  99 */     String str1 = "";
/*     */     try {
/* 101 */       str1 = (new ResourceComInfo()).getResourcename(paramString);
/* 102 */     } catch (Exception exception) {
/*     */       
/* 104 */       exception.printStackTrace();
/*     */     } 
/* 106 */     String str2 = "";
/* 107 */     str2 = "<a href='javascript:openhrm(" + paramString + ");'>" + str1 + "</a>";
/*     */     
/* 109 */     return str2;
/*     */   }
/*     */   
/*     */   public String getViewReportViewer(String paramString) {
/* 113 */     String str1 = "";
/*     */     try {
/* 115 */       str1 = (new ResourceComInfo()).getResourcename(paramString);
/* 116 */     } catch (Exception exception) {
/*     */       
/* 118 */       exception.printStackTrace();
/*     */     } 
/* 120 */     String str2 = "";
/* 121 */     str2 = "<a href='javascript:openhrm(" + paramString + ");' onclick='pointerXY(event);' title='" + str1 + "'>" + str1 + "</a>";
/*     */     
/* 123 */     return str2;
/*     */   }
/*     */   
/*     */   public String getViewReportRequest(String paramString1, String paramString2) {
/* 127 */     String str = "";
/* 128 */     str = "<a href='/workflow/request/ViewRequest.jsp?requestid=" + paramString2 + "&ismonitor=1' target='_blank'>" + paramString1 + "</a>";
/*     */     
/* 130 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getSubRequest(String paramString1, String paramString2) {
/* 140 */     String str = "0";
/* 141 */     RecordSet recordSet = new RecordSet();
/* 142 */     recordSet.executeQuery("select count(1)  as ssum from wf_subReq_detail_log where mainLogId = " + paramString1 + " and logtype=1", new Object[0]);
/* 143 */     if (recordSet.next()) {
/* 144 */       int i = Util.getIntValue(recordSet.getString("ssum"), 0);
/* 145 */       if (i != 0) str = "<a style='color: #00a9ff;' href='javascript:;' onclick='subRequestLogUtil.openSubRequest(" + paramString1 + ")' >" + i + "</a>"; 
/*     */     } 
/* 147 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getNodeInfo(String paramString1, String paramString2) {
/* 158 */     String[] arrayOfString = Util.TokenizerString2(paramString2, "+");
/* 159 */     String str1 = arrayOfString[0];
/* 160 */     String str2 = arrayOfString[1];
/* 161 */     int i = Util.getIntValue(arrayOfString[2]);
/* 162 */     if ("2".equals(str2)) return paramString1; 
/* 163 */     String str3 = SystemEnv.getHtmlLabelName(386806, i);
/* 164 */     if ("2".equals(str1)) {
/* 165 */       str3 = SystemEnv.getHtmlLabelName(386807, i);
/*     */     }
/* 167 */     return String.format("%s: %s", new Object[] { str3, paramString1 });
/*     */   }
/*     */   public String getTriggertype(String paramString1, String paramString2) {
/* 170 */     String[] arrayOfString = Util.TokenizerString2(paramString2, "+");
/* 171 */     int i = Util.getIntValue(arrayOfString[0]);
/* 172 */     String str = SystemEnv.getHtmlLabelName(533115, i);
/* 173 */     if ("1".equals(paramString1)) str = SystemEnv.getHtmlLabelName(533116, i); 
/* 174 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getDetailLabel(String paramString1, String paramString2) {
/* 184 */     String[] arrayOfString = Util.TokenizerString2(paramString2, "+");
/* 185 */     String str1 = arrayOfString[0];
/* 186 */     int i = Util.getIntValue(arrayOfString[1]);
/*     */     
/* 188 */     String str2 = SystemEnv.getHtmlLabelName(Util.getIntValue(paramString1), i);
/* 189 */     return "<a style='color: #00a9ff;' href='javascript:;' onclick='subRequestLogUtil.openDetail(" + str1 + ")' >" + str2 + "</a>";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getSubRequestDetail(String paramString1, String paramString2) {
/* 199 */     String[] arrayOfString = Util.TokenizerString2(paramString2, "+");
/* 200 */     String str1 = arrayOfString[0];
/* 201 */     String str2 = arrayOfString[1];
/* 202 */     String str3 = arrayOfString[2];
/*     */     
/* 204 */     paramString1 = Strings.isNullOrEmpty(paramString1) ? str2 : paramString1;
/* 205 */     if ("-1".equals(str3)) {
/* 206 */       return "<a href='/workflow/request/ViewRequest.jsp?requestid=" + str1 + "&ismonitor=1' target='_blank'>" + paramString1 + "</a>";
/*     */     }
/* 208 */     return paramString1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getSubRequestCreater(String paramString1, String paramString2) {
/* 218 */     String[] arrayOfString = Util.TokenizerString2(paramString2, "+");
/* 219 */     String str = arrayOfString[0];
/* 220 */     return "<a href=\"javaScript:openhrm(" + str + ");\" onclick=\"pointerXY(event);\">" + paramString1 + "</a>";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getSubRequestStatus(String paramString1, String paramString2) {
/* 230 */     String[] arrayOfString = Util.TokenizerString2(paramString2, "+");
/* 231 */     int i = Util.getIntValue(arrayOfString[0]);
/* 232 */     if ("-1".equals(paramString1)) paramString1 = "225"; 
/* 233 */     return SystemEnv.getHtmlLabelName(Util.getIntValue(paramString1), i);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/report/ViewReportLog.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */