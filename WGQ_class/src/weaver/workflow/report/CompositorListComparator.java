/*    */ package weaver.workflow.report;
/*    */ 
/*    */ import java.util.Comparator;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class CompositorListComparator
/*    */   implements Comparator
/*    */ {
/*    */   public int compare(Object paramObject1, Object paramObject2) {
/* 30 */     double d1 = ((ReportCompositorListBean)paramObject1).getCompositorList();
/*    */ 
/*    */     
/* 33 */     double d2 = ((ReportCompositorListBean)paramObject2).getCompositorList();
/*    */ 
/*    */     
/* 36 */     return (new Double(d1)).compareTo(new Double(d2));
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/report/CompositorListComparator.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */