/*     */ package weaver.workflow.report;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class ReportCompositorListBean
/*     */   implements Serializable
/*     */ {
/*     */   private double compositorList;
/*     */   private String fieldName;
/*     */   private String fieldId;
/*     */   private String colName;
/*     */   private String sqlFlag;
/*     */   private String isDetail;
/*     */   private String htmlType;
/*     */   private String types;
/*     */   private String isstat;
/*     */   private String dbOrder;
/*     */   private String fieldWidth;
/*     */   
/*     */   public String getFieldWidth() {
/*  35 */     return this.fieldWidth;
/*     */   }
/*     */   public void setFieldWidth(String paramString) {
/*  38 */     this.fieldWidth = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public void setCompositorList(double paramDouble) {
/*  44 */     this.compositorList = paramDouble;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public double getCompositorList() {
/*  51 */     return this.compositorList;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public void setFieldName(String paramString) {
/*  57 */     this.fieldName = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getFieldName() {
/*  64 */     return this.fieldName;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public void setIsDetail(String paramString) {
/*  70 */     this.isDetail = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getIsDetail() {
/*  77 */     return this.isDetail;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public void setHtmlType(String paramString) {
/*  83 */     this.htmlType = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getHtmlType() {
/*  90 */     return this.htmlType;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public void setIsstat(String paramString) {
/*  96 */     this.isstat = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getIsstat() {
/* 103 */     return this.isstat;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public void setDbOrder(String paramString) {
/* 109 */     this.dbOrder = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getDbOrder() {
/* 116 */     return this.dbOrder;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public void setTypes(String paramString) {
/* 122 */     this.types = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getTypes() {
/* 129 */     return this.types;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public void setColName(String paramString) {
/* 135 */     this.colName = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getColName() {
/* 142 */     return this.colName;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public void setSqlFlag(String paramString) {
/* 148 */     this.sqlFlag = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getSqlFlag() {
/* 155 */     return this.sqlFlag;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public void setFieldId(String paramString) {
/* 161 */     this.fieldId = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getFieldId() {
/* 168 */     return this.fieldId;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/report/ReportCompositorListBean.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */