/*     */ package weaver.workflow.report;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.List;
/*     */ import weaver.cache.CacheBase;
/*     */ import weaver.cache.CacheColumn;
/*     */ import weaver.cache.CacheColumnType;
/*     */ import weaver.cache.PKColumn;
/*     */ import weaver.conn.RecordSet;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class ReportTypeComInfo
/*     */   extends CacheBase
/*     */ {
/*  28 */   protected static String TABLE_NAME = "Workflow_ReportType";
/*     */   
/*  30 */   protected static String TABLE_WHERE = null;
/*     */   
/*  32 */   protected static String TABLE_ORDER = "typeorder";
/*     */   
/*     */   @PKColumn(type = CacheColumnType.NUMBER)
/*  35 */   protected static String PK_NAME = "id";
/*     */ 
/*     */   
/*     */   @CacheColumn
/*     */   protected static int typename;
/*     */ 
/*     */ 
/*     */   
/*     */   public int getReportTypeNum() {
/*  44 */     return size();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getReportTypeid() {
/*  53 */     return (String)getRowValue(0);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getReportTypename() {
/*  62 */     return (String)getRowValue(typename);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getReportTypename(String paramString) {
/*  71 */     return (String)getValue(typename, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void removeReportTypeCache() {
/*  78 */     removeCache();
/*     */   }
/*     */   
/*     */   public String getCanDelType(String paramString) {
/*  82 */     String str = "false";
/*  83 */     RecordSet recordSet = new RecordSet();
/*  84 */     recordSet.executeSql("SELECT count(*) FROM Workflow_Report where reporttype=" + paramString);
/*  85 */     int i = 0;
/*  86 */     if (recordSet.next()) {
/*  87 */       i = recordSet.getInt(1);
/*     */     }
/*  89 */     if (i == 0)
/*  90 */       str = "true"; 
/*  91 */     return str;
/*     */   }
/*     */   
/*     */   public List getCanDelTypeList(String paramString) {
/*  95 */     ArrayList<String> arrayList = new ArrayList();
/*  96 */     arrayList.add("true");
/*  97 */     RecordSet recordSet = new RecordSet();
/*  98 */     recordSet.executeSql("SELECT count(*) FROM Workflow_Report where reporttype=" + paramString);
/*  99 */     int i = 0;
/* 100 */     if (recordSet.next()) {
/* 101 */       i = recordSet.getInt(1);
/*     */     }
/* 103 */     if (i > 0) {
/* 104 */       arrayList.add("false");
/*     */     } else {
/* 106 */       arrayList.add("true");
/* 107 */     }  return arrayList;
/*     */   }
/*     */   
/*     */   public String getLinkType(String paramString1, String paramString2) {
/* 111 */     String str = "";
/* 112 */     str = "<a href='javascript:newDialog(1," + paramString2 + ");'>" + paramString1 + "</a>";
/* 113 */     return str;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/report/ReportTypeComInfo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */