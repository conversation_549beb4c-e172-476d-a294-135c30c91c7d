/*     */ package weaver.workflow.report;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.company.DepartmentComInfo;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class UserShareRights
/*     */   extends BaseBean
/*     */ {
/*  33 */   private DepartmentComInfo departmentcominfo = new DepartmentComInfo();
/*  34 */   private ResourceComInfo resourcecominfo = new ResourceComInfo();
/*  35 */   private ReportShare MDCompanyNameInfo = new ReportShare();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getUserRights(String paramString, User paramUser) throws Exception {
/*  48 */     setWfReportShareRight(paramString, "" + paramUser.getUID());
/*     */     
/*  50 */     int i = -1;
/*  51 */     int j = -1;
/*  52 */     int k = -1;
/*  53 */     String str1 = "";
/*  54 */     String str2 = "-100";
/*  55 */     RecordSet recordSet = new RecordSet();
/*     */     
/*  57 */     recordSet.executeSql("select sharelevel,mutidepartmentid from WorkflowReportShareDetail where userid=" + paramUser.getUID() + " and usertype=1 and (reportid=" + paramString + " or reportid=0) order by sharelevel");
/*  58 */     while (recordSet.next()) {
/*  59 */       int m = Util.getIntValue(recordSet.getString("sharelevel"), 0);
/*  60 */       if (m == 9 || m == 4) {
/*  61 */         k = m;
/*  62 */         str1 = Util.null2String(recordSet.getString("mutidepartmentid"));
/*  63 */         if (str1.length() > 1 && str1.indexOf(",") == 0) str1 = str1.substring(1, str1.length());  continue;
/*     */       } 
/*  65 */       if (m == 3) {
/*  66 */         j = m; continue;
/*     */       } 
/*  68 */       if (i != -1) {
/*  69 */         if (i < m)
/*  70 */           i = m; 
/*     */         continue;
/*     */       } 
/*  73 */       i = m;
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/*  78 */     if (i == 0) {
/*  79 */       str2 = "" + (new Integer(paramUser.getUserDepartment())).toString();
/*     */     }
/*  81 */     if (i == 1) {
/*  82 */       while (this.departmentcominfo.next()) {
/*  83 */         String str3 = this.departmentcominfo.getSubcompanyid1();
/*  84 */         if (!("" + paramUser.getUserSubCompany1()).equals(str3))
/*  85 */           continue;  String str4 = "" + this.departmentcominfo.getDepartmentid();
/*  86 */         if (str2.equals("")) { str2 = str4; continue; }
/*  87 */          str2 = str2 + "," + str4;
/*     */       } 
/*     */     }
/*     */ 
/*     */     
/*  92 */     if (j == 3) {
/*  93 */       String str = (new Integer(paramUser.getUserDepartment())).toString();
/*  94 */       ArrayList<String> arrayList = this.MDCompanyNameInfo.GetChildDepartment(str);
/*  95 */       for (byte b = 0; b < arrayList.size(); b++) {
/*  96 */         String str3 = arrayList.get(b);
/*  97 */         if (Util.getIntValue(str3) > 0) {
/*  98 */           if (str2.equals("")) { str2 = str3; }
/*     */           else
/* 100 */           { String str4 = "," + str2;
/* 101 */             if (str4.indexOf("," + str3 + ",") < 0) str2 = str2 + "," + str3;
/*     */              }
/*     */         
/*     */         }
/*     */       } 
/*     */     } 
/* 107 */     if ((k == 9 || k == 4) && i != 2)
/* 108 */       if (str2.equals("")) { str2 = str1; }
/* 109 */       else { str2 = str2 + "," + str1; }
/*     */        
/* 111 */     if (i == 2) {
/* 112 */       str2 = "";
/*     */     }
/* 114 */     return str2;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setWfReportShareRight(String paramString1, String paramString2) {
/* 123 */     RecordSet recordSet = new RecordSet();
/*     */     
/* 125 */     recordSet.executeSql("select * from WorkflowReportShareDetail where userid=" + paramString2 + " and usertype=1 and reportid=" + paramString1 + " order by sharelevel");
/* 126 */     if (!recordSet.next())
/*     */       try {
/* 128 */         this.MDCompanyNameInfo.setReportShareByReport(paramString1);
/* 129 */       } catch (Exception exception) {
/* 130 */         exception.printStackTrace();
/*     */       }  
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/report/UserShareRights.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */