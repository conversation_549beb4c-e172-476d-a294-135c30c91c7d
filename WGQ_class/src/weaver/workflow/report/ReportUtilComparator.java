/*    */ package weaver.workflow.report;
/*    */ 
/*    */ import java.util.Comparator;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class ReportUtilComparator
/*    */   implements Comparator
/*    */ {
/*    */   public int compare(Object paramObject1, Object paramObject2) {
/* 26 */     double d1 = Double.parseDouble((String)paramObject1);
/* 27 */     double d2 = Double.parseDouble((String)paramObject2);
/*    */     
/* 29 */     if (d1 > d2) {
/* 30 */       return 1;
/*    */     }
/* 32 */     return -1;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/report/ReportUtilComparator.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */