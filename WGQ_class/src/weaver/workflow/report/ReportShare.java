/*     */ package weaver.workflow.report;
/*     */ 
/*     */ import com.engine.common.service.impl.HrmCommonServiceImpl;
/*     */ import java.util.ArrayList;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.check.JobComInfo;
/*     */ import weaver.hrm.company.DepartmentComInfo;
/*     */ import weaver.hrm.company.SubCompanyComInfo;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class ReportShare
/*     */   extends BaseBean
/*     */ {
/*     */   private ResourceComInfo resourcecominfo;
/*     */   private DepartmentComInfo departmentcominfo;
/*  31 */   private ArrayList chiledeptlist = new ArrayList();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setReportShareByReport(String paramString) throws Exception {
/*  40 */     if (!"".equals(paramString) && paramString != null) {
/*  41 */       RecordSet recordSet = new RecordSet();
/*     */       
/*  43 */       String str1 = "";
/*  44 */       String str2 = "";
/*  45 */       String str3 = "";
/*  46 */       recordSet.executeSql("select userid,departmentid,subcompanyid from WorkflowReportShare WHERE reportid =  " + paramString);
/*  47 */       while (recordSet.next()) {
/*  48 */         String str6 = Util.null2String(recordSet.getString("userid"));
/*  49 */         String str7 = Util.null2String(recordSet.getString("departmentid"));
/*  50 */         String str8 = Util.null2String(recordSet.getString("subcompanyid"));
/*  51 */         if (!"".equals(str6) && !"0".equals(str6)) {
/*  52 */           if (str6.startsWith(",") && str6.endsWith(",")) {
/*  53 */             str6 = str6.substring(1, str6.length() - 1);
/*     */           }
/*  55 */           str1 = str1 + str6 + ",";
/*     */         } 
/*  57 */         if (!"".equals(str7) && !"0".equals(str7)) {
/*  58 */           if (str7.startsWith(",") && str7.endsWith(",")) {
/*  59 */             str7 = str7.substring(1, str7.length() - 1);
/*     */           }
/*  61 */           str2 = str2 + str7 + ",";
/*     */         } 
/*  63 */         if (!"".equals(str8) && !"0".equals(str8)) {
/*  64 */           if (str8.startsWith(",") && str8.endsWith(",")) {
/*  65 */             str8 = str8.substring(1, str8.length() - 1);
/*     */           }
/*  67 */           str3 = str3 + str8 + ",";
/*     */         } 
/*     */       } 
/*     */       
/*  71 */       ArrayList<String> arrayList1 = new ArrayList();
/*  72 */       ArrayList<String> arrayList2 = new ArrayList();
/*  73 */       ArrayList<String> arrayList3 = new ArrayList();
/*  74 */       ArrayList<String> arrayList4 = new ArrayList();
/*  75 */       ArrayList<String> arrayList5 = new ArrayList();
/*     */       
/*  77 */       char c = Util.getSeparator();
/*     */ 
/*     */ 
/*     */ 
/*     */       
/*  82 */       recordSet.executeProc("WkfReportShareDetail_DBId", paramString);
/*     */ 
/*     */ 
/*     */       
/*  86 */       String str4 = "";
/*  87 */       HrmCommonServiceImpl hrmCommonServiceImpl = new HrmCommonServiceImpl();
/*  88 */       String str5 = hrmCommonServiceImpl.getHrmRoleMemeberSqlByRoleIds("10");
/*  89 */       recordSet.executeSql(str5);
/*  90 */       while (recordSet.next()) {
/*  91 */         String str6 = Util.null2String(recordSet.getString("resourceid"));
/*  92 */         String str7 = Util.null2String(recordSet.getString("rolelevel"));
/*  93 */         arrayList1.add(str6 + "_1");
/*  94 */         arrayList2.add(str6);
/*  95 */         arrayList3.add("1");
/*  96 */         arrayList4.add(str7);
/*  97 */         arrayList5.add("");
/*     */       } 
/*     */ 
/*     */ 
/*     */       
/* 102 */       if ((!"".equals(str1) && !"0".equals(str1)) || (!"".equals(str2) && !"0".equals(str2)) || (!"".equals(str3) && !"0".equals(str3)) || Integer.parseInt(paramString) <= 0) {
/*     */         
/* 104 */         str4 = "select distinct t1.id, t2.sharelevel, t2.mutidepartmentid \t  from HrmResource t1, WorkflowReportShare t2 \t where ((t2.foralluser = 1 and t2.seclevel <= t1.seclevel) ";
/*     */ 
/*     */         
/* 107 */         if (!"".equals(str1) && !"0".equals(str1)) {
/* 108 */           str1 = str1.substring(0, str1.length() - 1);
/* 109 */           str4 = str4 + " or (t1.id in (" + str1 + ") and t2.seclevel <= t1.seclevel) ";
/*     */         } 
/* 111 */         if (!"".equals(str2) && !"0".equals(str2)) {
/* 112 */           str2 = str2.substring(0, str2.length() - 1);
/* 113 */           str4 = str4 + " or (t1.departmentid in (" + str2 + ") and t2.seclevel <= t1.seclevel)  ";
/*     */         } 
/* 115 */         if (!"".equals(str3) && !"0".equals(str3)) {
/* 116 */           str3 = str3.substring(0, str3.length() - 1);
/* 117 */           str4 = str4 + " or (t1.subcompanyid1 in (" + str1 + ") and t2.seclevel <= t1.seclevel) ";
/*     */         } 
/* 119 */         str5 = hrmCommonServiceImpl.getHrmRoleMemeberSqlByRoleIds(null);
/* 120 */         str4 = str4 + " )\t   and t1.id <> 0 \t   and t2.reportid = " + paramString + "\tunion \tselect distinct t3.resourceid as id, t2.sharelevel, t2.mutidepartmentid \t  from WorkflowReportShare t2, (" + str5 + ") t3 \t where (t3.roleid = t2.roleid and t2.roleid != 0 and \t       t3.rolelevel >= t2.rolelevel) \t   and t3.resourceid <> 0 \t   and t2.reportid = " + paramString;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */         
/* 130 */         recordSet.executeSql(str4);
/* 131 */         while (recordSet.next()) {
/* 132 */           String str6 = Util.null2String(recordSet.getString(1));
/* 133 */           String str7 = Util.null2String(recordSet.getString(2));
/* 134 */           String str8 = Util.null2String(recordSet.getString(3));
/*     */ 
/*     */           
/* 137 */           if (str7.equals("3")) {
/*     */             
/* 139 */             int j = arrayList1.indexOf(str6 + "_2");
/*     */             
/* 141 */             if (j != -1) {
/*     */ 
/*     */               
/* 144 */               String str = arrayList4.get(j);
/* 145 */               if (str.compareTo(str7) < 0 && !"2".equals(str))
/* 146 */                 arrayList4.set(j, str7);  continue;
/*     */             } 
/* 148 */             arrayList1.add(str6 + "_2");
/* 149 */             arrayList2.add(str6);
/* 150 */             arrayList3.add("1");
/* 151 */             arrayList4.add(str7);
/* 152 */             arrayList5.add("");
/*     */             continue;
/*     */           } 
/* 155 */           if (str7.equals("9") || str7.equals("5")) {
/*     */             
/* 157 */             int j = arrayList1.indexOf(str6 + "_3");
/*     */             
/* 159 */             if (j != -1) {
/* 160 */               String str = arrayList5.get(j);
/* 161 */               arrayList5.set(j, str + "," + str8); continue;
/*     */             } 
/* 163 */             arrayList1.add(str6 + "_3");
/* 164 */             arrayList2.add(str6);
/* 165 */             arrayList3.add("1");
/* 166 */             arrayList4.add(str7);
/* 167 */             arrayList5.add(str8);
/*     */             continue;
/*     */           } 
/* 170 */           if (str7.equals("4")) {
/*     */             
/* 172 */             int j = arrayList1.indexOf(str6 + "_4");
/* 173 */             int k = arrayList1.indexOf(str6 + "_2");
/*     */             
/* 175 */             if (j != -1 && k == -1) {
/* 176 */               String str = arrayList5.get(j);
/* 177 */               arrayList5.set(j, str + str8); continue;
/* 178 */             }  if (k == -1) {
/* 179 */               arrayList1.add(str6 + "_4");
/* 180 */               arrayList2.add(str6);
/* 181 */               arrayList3.add("1");
/* 182 */               arrayList4.add(str7);
/* 183 */               arrayList5.add(str8);
/*     */             }  continue;
/* 185 */           }  if (str7.equals("2")) {
/*     */             
/* 187 */             int j = arrayList1.indexOf(str6 + "_2");
/* 188 */             int k = arrayList1.indexOf(str6 + "_4");
/*     */             
/* 190 */             if (j != -1) {
/* 191 */               String str = arrayList5.get(j);
/* 192 */               arrayList5.set(j, str + str8); continue;
/* 193 */             }  if (k != -1) {
/* 194 */               arrayList1.set(k, str6 + "_2");
/* 195 */               arrayList2.set(k, str6);
/* 196 */               arrayList3.set(k, "1");
/* 197 */               arrayList4.set(k, str7);
/* 198 */               arrayList5.set(k, str8); continue;
/*     */             } 
/* 200 */             arrayList1.add(str6 + "_2");
/* 201 */             arrayList2.add(str6);
/* 202 */             arrayList3.add("1");
/* 203 */             arrayList4.add(str7);
/* 204 */             arrayList5.add(str8);
/*     */             continue;
/*     */           } 
/* 207 */           int i = arrayList1.indexOf(str6 + "_1");
/*     */           
/* 209 */           if (i != -1) {
/* 210 */             if (((String)arrayList4.get(i)).compareTo(str7) < 0)
/* 211 */               arrayList4.set(i, str7); 
/*     */             continue;
/*     */           } 
/* 214 */           arrayList1.add(str6 + "_1");
/* 215 */           arrayList2.add(str6);
/* 216 */           arrayList3.add("1");
/* 217 */           arrayList4.add(str7);
/* 218 */           arrayList5.add("");
/*     */         } 
/*     */       } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 227 */       for (byte b = 0; b < arrayList1.size(); b++) {
/*     */         
/* 229 */         String str = paramString + c + (String)arrayList2.get(b) + c + (String)arrayList3.get(b) + c + (String)arrayList4.get(b) + c + (String)arrayList5.get(b);
/*     */         
/* 231 */         recordSet.executeProc("WkfReportShareDetail_Insert", "" + str);
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setRptRolByReport(String paramString) throws Exception {
/* 244 */     RecordSet recordSet = new RecordSet();
/* 245 */     ArrayList<String> arrayList1 = new ArrayList();
/* 246 */     ArrayList<String> arrayList2 = new ArrayList();
/* 247 */     ArrayList<String> arrayList3 = new ArrayList();
/* 248 */     ArrayList<String> arrayList4 = new ArrayList();
/* 249 */     ArrayList<String> arrayList5 = new ArrayList();
/* 250 */     char c = Util.getSeparator();
/*     */     
/* 252 */     recordSet.executeProc("WkfReportShareDetail_DBId", paramString);
/*     */     
/* 254 */     HrmCommonServiceImpl hrmCommonServiceImpl = new HrmCommonServiceImpl();
/* 255 */     String str1 = hrmCommonServiceImpl.getHrmRoleMemeberSqlByRoleIds("10");
/* 256 */     String str2 = " ";
/* 257 */     recordSet.executeSql(str1);
/* 258 */     while (recordSet.next()) {
/* 259 */       String str3 = Util.null2String(recordSet.getString("resourceid"));
/* 260 */       String str4 = Util.null2String(recordSet.getString("rolelevel"));
/* 261 */       arrayList1.add(str3 + "_1");
/* 262 */       arrayList2.add(str3);
/* 263 */       arrayList3.add("1");
/* 264 */       arrayList4.add(str4);
/* 265 */       arrayList5.add("");
/*     */     } 
/* 267 */     str1 = hrmCommonServiceImpl.getHrmRoleMemeberSqlByRoleIds(null);
/*     */     
/* 269 */     str2 = str2 + "\tselect distinct t3.resourceid as id, t2.sharelevel, t2.mutidepartmentid \t  from WorkflowReportShare t2, (" + str1 + ") t3 \t where (t3.roleid = t2.roleid and t2.roleid != 0 and \t       t3.rolelevel >= t2.rolelevel) \t   and t3.resourceid <> 0 \t   and t2.reportid = " + paramString;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 275 */     recordSet.executeSql(str2);
/* 276 */     while (recordSet.next()) {
/* 277 */       String str3 = Util.null2String(recordSet.getString(1));
/* 278 */       String str4 = Util.null2String(recordSet.getString(2));
/* 279 */       String str5 = Util.null2String(recordSet.getString(3));
/* 280 */       if (str4.equals("3")) {
/*     */         
/* 282 */         int j = arrayList1.indexOf(str3 + "_2");
/*     */         
/* 284 */         if (j != -1) {
/*     */ 
/*     */           
/* 287 */           String str = arrayList4.get(j);
/* 288 */           if (str.compareTo(str4) < 0 && !"2".equals(str))
/* 289 */             arrayList4.set(j, str4);  continue;
/*     */         } 
/* 291 */         arrayList1.add(str3 + "_2");
/* 292 */         arrayList2.add(str3);
/* 293 */         arrayList3.add("1");
/* 294 */         arrayList4.add(str4);
/* 295 */         arrayList5.add("");
/*     */         continue;
/*     */       } 
/* 298 */       if (str4.equals("9") || str4.equals("4")) {
/*     */         
/* 300 */         int j = arrayList1.indexOf(str3 + "_3");
/*     */         
/* 302 */         if (j != -1) {
/* 303 */           String str = arrayList5.get(j);
/* 304 */           arrayList5.set(j, str + str5); continue;
/*     */         } 
/* 306 */         arrayList1.add(str3 + "_3");
/* 307 */         arrayList2.add(str3);
/* 308 */         arrayList3.add("1");
/* 309 */         arrayList4.add(str4);
/* 310 */         arrayList5.add(str5); continue;
/*     */       } 
/* 312 */       if (str4.equals("2")) {
/*     */         
/* 314 */         int j = arrayList1.indexOf(str3 + "_2");
/* 315 */         int k = arrayList1.indexOf(str3 + "_4");
/*     */         
/* 317 */         if (j != -1) {
/* 318 */           String str = arrayList5.get(j);
/* 319 */           arrayList5.set(j, str + str5); continue;
/* 320 */         }  if (k != -1) {
/* 321 */           arrayList1.set(k, str3 + "_2");
/* 322 */           arrayList2.set(k, str3);
/* 323 */           arrayList3.set(k, "1");
/* 324 */           arrayList4.set(k, str4);
/* 325 */           arrayList5.set(k, str5); continue;
/*     */         } 
/* 327 */         arrayList1.add(str3 + "_2");
/* 328 */         arrayList2.add(str3);
/* 329 */         arrayList3.add("1");
/* 330 */         arrayList4.add(str4);
/* 331 */         arrayList5.add(str5);
/*     */         continue;
/*     */       } 
/* 334 */       int i = arrayList1.indexOf(str3 + "_1");
/*     */       
/* 336 */       if (i != -1) {
/* 337 */         if (((String)arrayList4.get(i)).compareTo(str4) < 0)
/* 338 */           arrayList4.set(i, str4); 
/*     */         continue;
/*     */       } 
/* 341 */       arrayList1.add(str3 + "_1");
/* 342 */       arrayList2.add(str3);
/* 343 */       arrayList3.add("1");
/* 344 */       arrayList4.add(str4);
/* 345 */       arrayList5.add("");
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/* 350 */     for (byte b = 0; b < arrayList1.size(); b++) {
/* 351 */       String str = paramString + c + (String)arrayList2.get(b) + c + (String)arrayList3.get(b) + c + (String)arrayList4.get(b) + c + (String)arrayList5.get(b);
/* 352 */       recordSet.executeProc("WkfReportShareDetail_Insert", "" + str);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setReportShareByHrm(String paramString) throws Exception {
/* 364 */     RecordSet recordSet = new RecordSet();
/* 365 */     ArrayList<String> arrayList1 = new ArrayList();
/* 366 */     ArrayList<String> arrayList2 = new ArrayList();
/* 367 */     ArrayList<String> arrayList3 = new ArrayList();
/*     */     
/* 369 */     this.resourcecominfo = new ResourceComInfo();
/* 370 */     this.departmentcominfo = new DepartmentComInfo();
/*     */     
/* 372 */     char c = Util.getSeparator();
/*     */ 
/*     */     
/* 375 */     recordSet.executeProc("WkfReportShareDetail_DByUId", paramString);
/* 376 */     this.resourcecominfo.removeResourceCache();
/* 377 */     this.resourcecominfo = new ResourceComInfo();
/* 378 */     String str1 = this.resourcecominfo.getDepartmentID(paramString);
/* 379 */     String str2 = this.departmentcominfo.getSubcompanyid1(str1);
/* 380 */     String str3 = this.resourcecominfo.getSeclevel(paramString);
/*     */ 
/*     */     
/* 383 */     HrmCommonServiceImpl hrmCommonServiceImpl = new HrmCommonServiceImpl();
/* 384 */     String str4 = hrmCommonServiceImpl.getHrmRoleMemeberSqlByRoleIds("10");
/*     */     
/* 386 */     String str5 = " select distinct t1.id , t2.rolelevel from Workflow_Report  t1, (" + str4 + ")  t2  where t2.resourceid= " + paramString;
/*     */ 
/*     */     
/* 389 */     recordSet.executeSql(str5);
/* 390 */     while (recordSet.next()) {
/* 391 */       String str6 = Util.null2String(recordSet.getString(1));
/* 392 */       String str7 = Util.null2String(recordSet.getString(2));
/* 393 */       int i = arrayList1.indexOf(str6);
/* 394 */       if (i != -1) {
/* 395 */         if (((String)arrayList2.get(i)).compareTo(str7) < 0)
/* 396 */           arrayList2.set(i, str7); 
/*     */         continue;
/*     */       } 
/* 399 */       arrayList1.add(str6);
/* 400 */       arrayList2.add(str7);
/* 401 */       arrayList3.add("");
/*     */     } 
/*     */     
/* 404 */     str4 = hrmCommonServiceImpl.getHrmRoleMemeberSqlByRoleIds(null);
/*     */     
/* 406 */     str5 = " select distinct t2.reportid , t2.sharelevel,t2.mutidepartmentid from WorkflowReportShare  t2,  (" + str4 + ")  t3  where  ( (t2.foralluser=1 and t2.seclevel<=" + str3 + ")  or ( t2.userid='" + paramString + "' or t2.userid like '%," + paramString + ",%' ) or ((t2.departmentid='" + str1 + "' or t2.departmentid like '%," + str1 + ",%') and t2.departmentid <> 0 and t2.seclevel<=" + str3 + ")  or ( t3.resourceid=" + paramString + " and t3.roleid=t2.roleid and t3.rolelevel>=t2.rolelevel )) ";
/*     */ 
/*     */ 
/*     */     
/* 410 */     recordSet.executeSql(str5);
/* 411 */     while (recordSet.next()) {
/* 412 */       String str6 = Util.null2String(recordSet.getString(1));
/* 413 */       String str7 = Util.null2String(recordSet.getString(2));
/* 414 */       String str8 = Util.null2String(recordSet.getString(3));
/* 415 */       int i = arrayList1.indexOf(str6);
/* 416 */       if (i != -1) {
/* 417 */         if (((String)arrayList2.get(i)).compareTo(str7) < 0)
/* 418 */           arrayList2.set(i, str7); 
/*     */         continue;
/*     */       } 
/* 421 */       arrayList1.add(str6);
/* 422 */       arrayList2.add(str7);
/* 423 */       arrayList3.add(str8);
/*     */     } 
/*     */ 
/*     */     
/* 427 */     for (byte b = 0; b < arrayList1.size(); b++) {
/*     */       
/* 429 */       String str = (String)arrayList1.get(b) + c + paramString + c + "1" + c + (String)arrayList2.get(b) + c + (String)arrayList3.get(b);
/*     */       
/* 431 */       recordSet.executeProc("WkfReportShareDetail_Insert", "" + str);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public ArrayList GetChildDepartment(String paramString) {
/* 441 */     RecordSet recordSet = new RecordSet();
/* 442 */     recordSet.executeSql("Select * from HrmDepartment where supdepid =" + paramString);
/* 443 */     while (recordSet.next()) {
/* 444 */       GetSubDeptment(recordSet.getString("id"));
/*     */     }
/* 446 */     return this.chiledeptlist;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void GetSubDeptment(String paramString) {
/* 454 */     if (this.chiledeptlist.indexOf(paramString) < 0)
/* 455 */       this.chiledeptlist.add(paramString); 
/* 456 */     RecordSet recordSet = new RecordSet();
/* 457 */     recordSet.executeSql("Select * from HrmDepartment where supdepid =" + paramString);
/* 458 */     while (recordSet.next()) {
/* 459 */       String str = recordSet.getString("id");
/* 460 */       if (this.chiledeptlist.indexOf(str) < 0) {
/* 461 */         this.chiledeptlist.add(str);
/* 462 */         GetSubDeptment(str);
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void SetNewHrmReportShare(String paramString1, String paramString2, String paramString3, String paramString4) {
/* 474 */     RecordSet recordSet1 = new RecordSet();
/* 475 */     RecordSet recordSet2 = new RecordSet();
/* 476 */     char c = Util.getSeparator();
/* 477 */     String str1 = "," + paramString1 + ",";
/* 478 */     String str2 = "," + paramString2 + ",";
/* 479 */     String str3 = "," + paramString3 + ",";
/* 480 */     checkHrmReportShare(paramString1, paramString2, paramString3, paramString4);
/*     */ 
/*     */     
/* 483 */     recordSet1.executeSql("select reportid,seclevel,sharelevel,mutidepartmentid from WorkflowReportShare where sharetype=1 and (userid like '%" + str1 + "%' or userid = '" + paramString1 + "')");
/* 484 */     while (recordSet1.next()) {
/* 485 */       String str5 = recordSet1.getString("reportid");
/* 486 */       int i = recordSet1.getInt("sharelevel");
/* 487 */       String str6 = recordSet1.getString("mutidepartmentid");
/* 488 */       recordSet2.executeSql("select userid from WorkflowReportShareDetail where reportid='" + str5 + "' and userid ='" + paramString1 + "'");
/* 489 */       if (recordSet2.getCounts() == 0) {
/* 490 */         String str = str5 + c + paramString1 + c + "1" + c + i + c + str6;
/* 491 */         recordSet2.executeProc("WkfReportShareDetail_Insert", "" + str);
/*     */       } 
/*     */     } 
/*     */ 
/*     */     
/* 496 */     recordSet1.executeSql("select reportid,seclevel,mutidepartmentid,sharelevel from WorkflowReportShare where sharetype=2 and (departmentid like '%" + str2 + "%' or departmentid = '" + paramString2 + "') and seclevel <= " + paramString4);
/* 497 */     while (recordSet1.next()) {
/* 498 */       String str5 = recordSet1.getString("reportid");
/* 499 */       String str6 = recordSet1.getString("mutidepartmentid");
/* 500 */       int i = recordSet1.getInt("seclevel");
/* 501 */       int j = recordSet1.getInt("sharelevel");
/* 502 */       recordSet2.executeSql("select userid from WorkflowReportShareDetail where reportid='" + str5 + "' and userid ='" + paramString1 + "'");
/* 503 */       if (recordSet2.getCounts() == 0 && 
/* 504 */         Util.getIntValue(paramString4) >= i) {
/* 505 */         String str = str5 + c + paramString1 + c + "1" + c + j + c + str6;
/* 506 */         recordSet2.executeProc("WkfReportShareDetail_Insert", "" + str);
/*     */       } 
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/* 512 */     recordSet1.executeSql("select reportid,seclevel,mutidepartmentid,sharelevel from WorkflowReportShare where sharetype=3 and (subcompanyid like '%" + str3 + "%' or subcompanyid = '" + paramString3 + "') and seclevel <= " + paramString4);
/* 513 */     while (recordSet1.next()) {
/* 514 */       String str5 = recordSet1.getString("reportid");
/* 515 */       String str6 = recordSet1.getString("mutidepartmentid");
/* 516 */       int i = recordSet1.getInt("seclevel");
/* 517 */       int j = recordSet1.getInt("sharelevel");
/* 518 */       recordSet2.executeSql("select userid from WorkflowReportShareDetail where reportid='" + str5 + "' and userid ='" + paramString1 + "'");
/* 519 */       if (recordSet2.getCounts() == 0 && 
/* 520 */         Util.getIntValue(paramString4) >= i) {
/* 521 */         String str = str5 + c + paramString1 + c + "1" + c + j + c + str6;
/* 522 */         recordSet2.executeProc("WkfReportShareDetail_Insert", "" + str);
/*     */       } 
/*     */     } 
/*     */     
/* 526 */     HrmCommonServiceImpl hrmCommonServiceImpl = new HrmCommonServiceImpl();
/* 527 */     String str4 = hrmCommonServiceImpl.getRoleIds(Util.getIntValue(paramString1));
/* 528 */     if ("".equals(str4)) str4 = "0";
/*     */ 
/*     */     
/* 531 */     recordSet1.executeSql("select reportid,seclevel,sharelevel,mutidepartmentid from WorkflowReportShare where sharetype=4 and roleid in (" + str4 + ") and seclevel <= " + paramString4);
/* 532 */     while (recordSet1.next()) {
/* 533 */       int i = recordSet1.getInt("seclevel");
/* 534 */       String str5 = recordSet1.getString("mutidepartmentid");
/* 535 */       String str6 = recordSet1.getString("reportid");
/* 536 */       int j = recordSet1.getInt("sharelevel");
/* 537 */       recordSet2.executeSql("select userid from WorkflowReportShareDetail where reportid='" + str6 + "' and userid ='" + paramString1 + "'");
/* 538 */       if (recordSet2.getCounts() == 0 && 
/* 539 */         Util.getIntValue(paramString4) >= i) {
/* 540 */         String str = str6 + c + paramString1 + c + "1" + c + j + c + str5;
/* 541 */         recordSet2.executeProc("WkfReportShareDetail_Insert", "" + str);
/*     */       } 
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/* 547 */     recordSet1.executeSql("select reportid,seclevel,sharelevel,mutidepartmentid from WorkflowReportShare where sharetype=5 and seclevel <= " + paramString4);
/* 548 */     while (recordSet1.next()) {
/* 549 */       int i = recordSet1.getInt("seclevel");
/* 550 */       String str5 = recordSet1.getString("mutidepartmentid");
/* 551 */       String str6 = recordSet1.getString("reportid");
/* 552 */       int j = recordSet1.getInt("sharelevel");
/* 553 */       recordSet2.executeSql("select userid from WorkflowReportShareDetail where reportid='" + str6 + "' and userid ='" + paramString1 + "'");
/* 554 */       if (recordSet2.getCounts() == 0 && 
/* 555 */         Util.getIntValue(paramString4) >= i) {
/* 556 */         String str = str6 + c + paramString1 + c + "1" + c + j + c + str5;
/* 557 */         recordSet2.executeProc("WkfReportShareDetail_Insert", "" + str);
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void checkHrmReportShare(String paramString1, String paramString2, String paramString3, String paramString4) {
/* 570 */     String str1 = "," + paramString1 + ",";
/* 571 */     String str2 = "," + paramString2 + ",";
/* 572 */     String str3 = "," + paramString3 + ",";
/*     */     
/* 574 */     RecordSet recordSet1 = new RecordSet();
/* 575 */     RecordSet recordSet2 = new RecordSet();
/* 576 */     boolean bool = false;
/* 577 */     recordSet1.executeSql("select * from WorkflowReportShare where (reportid in (select reportid from WorkflowReportShareDetail where userid=" + paramString1 + ") or userid like '%" + str1 + "%') and sharetype in (2,3,4,5)");
/* 578 */     while (recordSet1.next()) {
/* 579 */       String str4 = recordSet1.getString("sharetype");
/* 580 */       String str5 = recordSet1.getString("reportid");
/* 581 */       int i = recordSet1.getInt("seclevel");
/* 582 */       String str6 = recordSet1.getString("departmentid");
/* 583 */       String str7 = recordSet1.getString("subcompanyid");
/* 584 */       String str8 = recordSet1.getString("roleid");
/* 585 */       String str9 = recordSet1.getString("rolelevel");
/*     */       
/* 587 */       if ("2".equals(str4) && str6.indexOf(str2) > -1 && Util.getIntValue(paramString4) < i) {
/* 588 */         bool = true;
/*     */       }
/*     */       
/* 591 */       if ("3".equals(str4) && str7.indexOf(str3) > -1 && Util.getIntValue(paramString4) < i) {
/* 592 */         bool = true;
/*     */       }
/*     */       
/* 595 */       if ("4".equals(str4) && Util.getIntValue(paramString4) < i) {
/* 596 */         HrmCommonServiceImpl hrmCommonServiceImpl = new HrmCommonServiceImpl();
/* 597 */         String str = hrmCommonServiceImpl.getRoleSql(paramString1);
/* 598 */         recordSet2.executeSql(str + " t where roleid=" + str8 + " and rolelevel=" + str9);
/* 599 */         if (recordSet2.next()) {
/* 600 */           bool = true;
/*     */         }
/*     */       } 
/*     */       
/* 604 */       if ("5".equals(str4) && Util.getIntValue(paramString4) < i) {
/* 605 */         recordSet2.executeSql("select reportid from WorkflowReportShareDetail where userid=" + paramString1 + " and reportid=" + str5);
/* 606 */         if (!recordSet2.next()) {
/* 607 */           bool = true;
/*     */         }
/*     */       } 
/*     */       
/* 611 */       if (bool) {
/* 612 */         recordSet2.executeSql("delete from WorkflowReportShareDetail where userid=" + paramString1 + " and reportid=" + str5);
/*     */       }
/*     */     } 
/*     */   }
/*     */   
/*     */   public String getReportName(String paramString1, String paramString2) {
/* 618 */     String str = "";
/* 619 */     int i = Util.getIntValue(paramString2, 7);
/* 620 */     switch (Util.getIntValue(paramString1)) {
/*     */       
/*     */       case 0:
/* 623 */         str = SystemEnv.getHtmlLabelName(332, i);
/*     */         break;
/*     */       case -1:
/* 626 */         str = SystemEnv.getHtmlLabelName(388253, i);
/*     */         break;
/*     */       case -2:
/* 629 */         str = SystemEnv.getHtmlLabelName(388257, i);
/*     */         break;
/*     */       case -3:
/* 632 */         str = SystemEnv.getHtmlLabelName(388254, i);
/*     */         break;
/*     */       case -4:
/* 635 */         str = SystemEnv.getHtmlLabelName(388258, i);
/*     */         break;
/*     */       case -5:
/* 638 */         str = SystemEnv.getHtmlLabelName(19031, i);
/*     */         break;
/*     */       case -6:
/* 641 */         str = SystemEnv.getHtmlLabelName(19032, i);
/*     */         break;
/*     */       case -7:
/* 644 */         str = SystemEnv.getHtmlLabelName(19034, i);
/*     */         break;
/*     */       case -8:
/* 647 */         str = SystemEnv.getHtmlLabelName(19035, i);
/*     */         break;
/*     */       case -9:
/* 650 */         str = SystemEnv.getHtmlLabelName(388255, i);
/*     */         break;
/*     */       case -10:
/* 653 */         str = SystemEnv.getHtmlLabelName(388259, i);
/*     */         break;
/*     */       case -11:
/* 656 */         str = SystemEnv.getHtmlLabelName(21899, i);
/*     */         break;
/*     */     } 
/* 659 */     return str;
/*     */   }
/*     */   
/*     */   public String getShareTypeName(String paramString1, String paramString2) {
/* 663 */     String str = "";
/* 664 */     int i = Util.getIntValue(paramString2, 7);
/* 665 */     switch (Util.getIntValue(paramString1)) {
/*     */       
/*     */       case 1:
/* 668 */         str = SystemEnv.getHtmlLabelName(179, i);
/*     */         break;
/*     */       case 2:
/* 671 */         str = SystemEnv.getHtmlLabelName(141, i);
/*     */         break;
/*     */       case 3:
/* 674 */         str = SystemEnv.getHtmlLabelName(124, i);
/*     */         break;
/*     */       case 4:
/* 677 */         str = SystemEnv.getHtmlLabelName(122, i);
/*     */         break;
/*     */       case 5:
/* 680 */         str = SystemEnv.getHtmlLabelName(1340, i);
/*     */         break;
/*     */       case 6:
/* 683 */         str = SystemEnv.getHtmlLabelName(6086, i);
/*     */         break;
/*     */     } 
/* 686 */     return str;
/*     */   }
/*     */   public String getSeclevel(String paramString1, String paramString2) throws Exception {
/* 689 */     String[] arrayOfString = Util.TokenizerString2(paramString2, "+");
/* 690 */     String str1 = arrayOfString[1];
/* 691 */     String str2 = "";
/* 692 */     if (!str1.equals("6") && !"1".equals(str1)) {
/* 693 */       if (!paramString1.equals("")) {
/* 694 */         str2 = str2 + paramString1;
/* 695 */         if (!arrayOfString[0].equals("") && !arrayOfString[0].equals(" ")) {
/* 696 */           str2 = str2 + "-" + arrayOfString[0];
/*     */         } else {
/* 698 */           str2 = str2 + "-100";
/*     */         } 
/* 700 */       } else if (!arrayOfString[0].equals("")) {
/* 701 */         str2 = str2 + arrayOfString[0];
/*     */       } 
/*     */     }
/* 704 */     return str2;
/*     */   }
/*     */   public String getShareObj(String paramString1, String paramString2) throws Exception {
/* 707 */     ResourceComInfo resourceComInfo = new ResourceComInfo();
/* 708 */     SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/* 709 */     DepartmentComInfo departmentComInfo = new DepartmentComInfo();
/* 710 */     JobComInfo jobComInfo = new JobComInfo();
/* 711 */     RecordSet recordSet = new RecordSet();
/* 712 */     String str1 = "";
/* 713 */     String[] arrayOfString = Util.TokenizerString2(paramString2, "+");
/* 714 */     String str2 = Util.null2String(arrayOfString[0]);
/* 715 */     String str3 = Util.null2String(arrayOfString[1]);
/* 716 */     String str4 = Util.null2String(arrayOfString[2]);
/* 717 */     String str5 = Util.null2String(arrayOfString[3]);
/* 718 */     int i = Util.getIntValue(arrayOfString[4]);
/* 719 */     int j = Util.getIntValue(arrayOfString[5], 7);
/* 720 */     String str6 = Util.null2String(arrayOfString[6]);
/* 721 */     if ("1".equals(paramString1)) {
/* 722 */       String[] arrayOfString1 = Util.TokenizerString2(str2, ",");
/* 723 */       for (byte b = 0; b < arrayOfString1.length; b++) {
/* 724 */         str1 = str1 + "<a href='/hrm/resource/HrmResource.jsp?id=" + arrayOfString1[b] + "' target='_blank'>" + Util.toScreen(resourceComInfo.getResourcename(arrayOfString1[b]), j) + "</a>,";
/*     */       }
/* 726 */     } else if ("3".equals(paramString1)) {
/* 727 */       String[] arrayOfString1 = Util.TokenizerString2(str4, ",");
/* 728 */       for (byte b = 0; b < arrayOfString1.length; b++) {
/* 729 */         str1 = str1 + "<a href='/hrm/company/HrmDepartmentDsp.jsp?id=" + arrayOfString1[b] + "' target='_blank'>" + Util.toScreen(departmentComInfo.getDepartmentname(arrayOfString1[b]), j) + "</a>,";
/*     */       }
/* 731 */     } else if ("2".equals(paramString1)) {
/* 732 */       String[] arrayOfString1 = Util.TokenizerString2(str3, ",");
/* 733 */       for (byte b = 0; b < arrayOfString1.length; b++) {
/* 734 */         str1 = str1 + "<a href='/hrm/company/HrmSubCompanyDsp.jsp?id=" + arrayOfString1[b] + "' target='_blank'>" + Util.toScreen(subCompanyComInfo.getSubCompanyname(arrayOfString1[b]), j) + "</a>,";
/*     */       }
/* 736 */     } else if ("4".equals(paramString1)) {
/*     */ 
/*     */       
/* 739 */       if (null != str5 && !str5.trim().equals("")) {
/* 740 */         recordSet.executeSql("select rolesmark from hrmroles where id in (" + str5 + ")");
/* 741 */         while (recordSet.next()) {
/* 742 */           str1 = str1 + Util.toScreen(recordSet.getString(1), j);
/* 743 */           if (i == 0)
/* 744 */             str1 = str1 + "/" + SystemEnv.getHtmlLabelName(124, j) + ","; 
/* 745 */           if (i == 1)
/* 746 */             str1 = str1 + "/" + SystemEnv.getHtmlLabelName(141, j) + ","; 
/* 747 */           if (i == 2) {
/* 748 */             str1 = str1 + "/" + SystemEnv.getHtmlLabelName(140, j) + ",";
/*     */           }
/*     */         } 
/*     */       } 
/* 752 */     } else if (!"5".equals(paramString1) && 
/* 753 */       "6".equals(paramString1)) {
/* 754 */       String str = "";
/* 755 */       String[] arrayOfString1 = Util.TokenizerString2(str2, ",");
/* 756 */       for (byte b = 0; b < arrayOfString1.length; b++) {
/* 757 */         str1 = str1 + jobComInfo.getJobName(arrayOfString1[b]);
/*     */       }
/*     */       
/* 760 */       if (str6.equals("0")) {
/* 761 */         str = "/" + SystemEnv.getHtmlLabelName(19438, j) + "(";
/* 762 */         String[] arrayOfString2 = Util.TokenizerString2(str4, ",");
/* 763 */         for (byte b1 = 0; b1 < arrayOfString2.length; b1++) {
/* 764 */           str = str + "<a href='/hrm/company/HrmDepartmentDsp.jsp?id=" + arrayOfString2[b1] + "' target='_blank'>" + Util.toScreen(departmentComInfo.getDepartmentname(arrayOfString2[b1]), j) + "</a>,";
/*     */         }
/* 766 */         str = str.substring(0, str.length() - 1);
/* 767 */         str1 = str1 + str + "),";
/* 768 */       } else if (str6.equals("1")) {
/* 769 */         str = "/" + SystemEnv.getHtmlLabelName(19437, j) + "(";
/* 770 */         String[] arrayOfString2 = Util.TokenizerString2(str4, ",");
/* 771 */         for (byte b1 = 0; b1 < arrayOfString2.length; b1++) {
/* 772 */           str = str + "<a href='/hrm/company/HrmSubCompanyDsp.jsp?id=" + arrayOfString2[b1] + "' target='_blank'>" + Util.toScreen(subCompanyComInfo.getSubCompanyname(arrayOfString2[b1]), j) + "</a>,";
/*     */         }
/* 774 */         str = str.substring(0, str.length() - 1);
/* 775 */         str1 = str1 + str + "),";
/*     */       } else {
/* 777 */         str = "/" + SystemEnv.getHtmlLabelName(140, j);
/* 778 */         str1 = str1 + str + ",";
/*     */       } 
/*     */     } 
/*     */     
/* 782 */     if (str1.length() > 0) {
/* 783 */       return str1.substring(0, str1.length() - 1);
/*     */     }
/* 785 */     return str1;
/*     */   }
/*     */   
/*     */   public String getShareLevel(String paramString1, String paramString2) throws Exception {
/* 789 */     String str1 = "";
/* 790 */     String[] arrayOfString = Util.TokenizerString2(paramString2, "+");
/* 791 */     int i = Util.getIntValue(paramString1);
/* 792 */     String str2 = Util.null2String(arrayOfString[0]);
/* 793 */     int j = Util.getIntValue(arrayOfString[1], 7);
/* 794 */     DepartmentComInfo departmentComInfo = new DepartmentComInfo();
/* 795 */     SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/* 796 */     if (i == 0) str1 = SystemEnv.getHtmlLabelName(18511, j); 
/* 797 */     if (i == 1) str1 = SystemEnv.getHtmlLabelName(18512, j); 
/* 798 */     if (i == 2) str1 = SystemEnv.getHtmlLabelName(140, j); 
/* 799 */     if (i == 3) str1 = SystemEnv.getHtmlLabelName(130507, j); 
/* 800 */     if (i == 4) str1 = SystemEnv.getHtmlLabelName(6087, j); 
/* 801 */     if (i == 5) {
/* 802 */       String str = "";
/* 803 */       ArrayList<String> arrayList = Util.TokenizerString(str2, ",");
/* 804 */       for (byte b = 0; b < arrayList.size(); b++) {
/* 805 */         int k = Util.getIntValue(arrayList.get(b));
/*     */ 
/*     */         
/* 808 */         str = str + subCompanyComInfo.getSubCompanyname("" + k) + ",";
/*     */       } 
/*     */       
/* 811 */       if (str.length() > 0) {
/* 812 */         str = str.substring(0, str.length() - 1);
/*     */       }
/* 814 */       str1 = SystemEnv.getHtmlLabelName(25512, j) + "(" + str + ")";
/*     */     } 
/* 816 */     if (i == 9) {
/* 817 */       String str = "";
/* 818 */       ArrayList<String> arrayList = Util.TokenizerString(str2, ",");
/* 819 */       for (byte b = 0; b < arrayList.size(); b++) {
/* 820 */         int k = Util.getIntValue(arrayList.get(b));
/*     */         
/* 822 */         str = str + departmentComInfo.getDepartmentname("" + k) + ",";
/*     */       } 
/* 824 */       if (str.length() > 0) {
/* 825 */         str = str.substring(0, str.length() - 1);
/*     */       }
/* 827 */       str1 = SystemEnv.getHtmlLabelName(17006, j) + "(" + str + ")";
/*     */     } 
/* 829 */     return str1;
/*     */   }
/*     */   
/*     */   public String getFlowReportLevel(String paramString1, String paramString2) throws Exception {
/* 833 */     String str1 = "";
/* 834 */     String[] arrayOfString = Util.TokenizerString2(paramString2, "+");
/* 835 */     int i = Util.getIntValue(paramString1);
/* 836 */     String str2 = Util.null2String(arrayOfString[0]);
/* 837 */     int j = Util.getIntValue(arrayOfString[1], 7);
/* 838 */     DepartmentComInfo departmentComInfo = new DepartmentComInfo();
/* 839 */     SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/* 840 */     if (i == 0) str1 = SystemEnv.getHtmlLabelName(18511, j); 
/* 841 */     if (i == 1) str1 = SystemEnv.getHtmlLabelName(18512, j); 
/* 842 */     if (i == 2) str1 = SystemEnv.getHtmlLabelName(140, j); 
/* 843 */     if (i == 3) str1 = SystemEnv.getHtmlLabelName(130507, j); 
/* 844 */     if (i == 4) {
/* 845 */       String str = "";
/* 846 */       ArrayList<String> arrayList = Util.TokenizerString(str2, ",");
/* 847 */       for (byte b = 0; b < arrayList.size(); b++) {
/* 848 */         int k = Util.getIntValue(arrayList.get(b));
/*     */         
/* 850 */         if (k > 0) {
/* 851 */           str = str + subCompanyComInfo.getSubCompanyname("" + k) + ",";
/*     */         }
/*     */       } 
/* 854 */       if (str.length() > 0) {
/* 855 */         str = str.substring(0, str.length() - 1);
/*     */       }
/* 857 */       str1 = SystemEnv.getHtmlLabelName(25512, j) + "(" + str + ")";
/*     */     } 
/* 859 */     if (i == 9) {
/* 860 */       String str = "";
/* 861 */       ArrayList<String> arrayList = Util.TokenizerString(str2, ",");
/* 862 */       for (byte b = 0; b < arrayList.size(); b++) {
/* 863 */         int k = Util.getIntValue(arrayList.get(b));
/* 864 */         if (k > 0)
/* 865 */           str = str + departmentComInfo.getDepartmentname("" + k) + ","; 
/*     */       } 
/* 867 */       if (str.length() > 0) {
/* 868 */         str = str.substring(0, str.length() - 1);
/*     */       }
/* 870 */       str1 = SystemEnv.getHtmlLabelName(17006, j) + "(" + str + ")";
/*     */     } 
/* 872 */     return str1;
/*     */   }
/*     */   
/*     */   public String getAllowLook(String paramString1, String paramString2) throws Exception {
/* 876 */     String str = "";
/* 877 */     String[] arrayOfString = Util.TokenizerString2(paramString2, "+");
/* 878 */     int i = Util.getIntValue(arrayOfString[0], 7);
/* 879 */     int j = Util.getIntValue(paramString1);
/* 880 */     if (j == 1) {
/* 881 */       str = SystemEnv.getHtmlLabelName(115, i);
/*     */     } else {
/* 883 */       str = SystemEnv.getHtmlLabelName(126638, i);
/*     */     } 
/* 885 */     return str;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/report/ReportShare.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */