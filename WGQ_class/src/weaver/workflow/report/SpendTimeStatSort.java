/*    */ package weaver.workflow.report;
/*    */ import weaver.general.ThreadVarLanguage;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ import weaver.workflow.workflow.WorkTypeComInfo;
/*    */ import weaver.workflow.workflow.WorkflowComInfo;
/*    */ 
/*    */ public class SpendTimeStatSort extends BaseBean {
/*    */   public String getFlowTypeName(String paramString) throws Exception {
/*  9 */     WorkTypeComInfo workTypeComInfo = new WorkTypeComInfo();
/* 10 */     WorkflowComInfo workflowComInfo = new WorkflowComInfo();
/* 11 */     return workTypeComInfo.getWorkTypename(workflowComInfo.getWorkflowtype(WorkflowVersion.getActiveVersionWFID(paramString)));
/*    */   }
/*    */   
/*    */   public String getFlowName(String paramString) throws Exception {
/* 15 */     WorkflowComInfo workflowComInfo = new WorkflowComInfo();
/* 16 */     return workflowComInfo.getWorkflowname(paramString);
/*    */   }
/*    */   
/*    */   public String getSpendsString(String paramString) {
/* 20 */     double d = Util.getDoubleValue(paramString);
/* 21 */     if (d <= 0.0D) {
/* 22 */       return "0" + SystemEnv.getHtmlLabelName(15049, ThreadVarLanguage.getLang()) + "";
/*    */     }
/* 24 */     int i = (int)d;
/* 25 */     int j = (int)((d - i) * 60.0D + 0.5D);
/* 26 */     StringBuilder stringBuilder = new StringBuilder();
/* 27 */     if (i >= 24) {
/* 28 */       stringBuilder.append(i / 24).append("" + SystemEnv.getHtmlLabelName(383378, ThreadVarLanguage.getLang()) + "");
/*    */     }
/* 30 */     if (i > 0) {
/* 31 */       stringBuilder.append(i % 24).append("" + SystemEnv.getHtmlLabelName(391, ThreadVarLanguage.getLang()) + "");
/*    */     }
/* 33 */     stringBuilder.append(j).append("" + SystemEnv.getHtmlLabelName(15049, ThreadVarLanguage.getLang()) + "");
/* 34 */     return stringBuilder.toString();
/*    */   }
/*    */   
/*    */   public long getMinutes(int paramInt1, int paramInt2) {
/* 38 */     if (paramInt2 < 0) {
/* 39 */       return -1L;
/*    */     }
/* 41 */     if (paramInt2 == 0) {
/* 42 */       return 0L;
/*    */     }
/* 44 */     switch (paramInt1) {
/*    */       
/*    */       case 1:
/* 47 */         return (paramInt2 * 60);
/*    */       
/*    */       case 2:
/* 50 */         return (paramInt2 * 1440);
/*    */     } 
/*    */ 
/*    */     
/* 54 */     return paramInt2;
/*    */   }
/*    */ 
/*    */   
/*    */   public String getSQLTypeString(int paramInt) {
/* 59 */     switch (paramInt) {
/*    */       
/*    */       case 1:
/* 62 */         return ">";
/*    */       
/*    */       case 2:
/* 65 */         return ">=";
/*    */       
/*    */       case 3:
/* 68 */         return "<";
/*    */       
/*    */       case 4:
/* 71 */         return "<=";
/*    */       
/*    */       case 5:
/* 74 */         return "=";
/*    */     } 
/*    */ 
/*    */     
/* 78 */     return "<>";
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/report/SpendTimeStatSort.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */