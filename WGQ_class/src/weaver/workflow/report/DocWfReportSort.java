/*    */ package weaver.workflow.report;
/*    */ 
/*    */ import weaver.general.BaseBean;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.company.DepartmentComInfo;
/*    */ import weaver.hrm.company.SubCompanyComInfo;
/*    */ import weaver.hrm.resource.ResourceComInfo;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class DocWfReportSort
/*    */   extends BaseBean
/*    */ {
/* 15 */   private ResourceComInfo resourceComInfo = new ResourceComInfo();
/* 16 */   private DepartmentComInfo departmentComInfo = new DepartmentComInfo();
/* 17 */   private SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/*    */ 
/*    */   
/*    */   public String getPercentString(String paramString1, String paramString2) {
/* 21 */     int i = Util.getIntValue(paramString2, 0);
/* 22 */     if (i == 0) {
/* 23 */       return "0.00%";
/*    */     }
/* 25 */     int j = Util.getIntValue(paramString1, 0);
/* 26 */     String str = Double.toString(j * 100.0D / i);
/* 27 */     return Util.round(str, 2) + "%";
/*    */   }
/*    */   
/*    */   public String getNumString(String paramString) {
/* 31 */     return String.valueOf(Util.getIntValue(paramString, 0));
/*    */   }
/*    */   
/*    */   public String replaceSql(String paramString) {
/* 35 */     paramString = Util.StringReplace(paramString, "&", "&amp;");
/* 36 */     paramString = Util.StringReplace(paramString, "<", "&lt;");
/* 37 */     paramString = Util.StringReplace(paramString, ">", "&gt;");
/* 38 */     return paramString;
/*    */   }
/*    */   
/*    */   public String getObjName(String paramString1, String paramString2) {
/* 42 */     int i = Util.getIntValue(paramString2, 2);
/* 43 */     switch (i) {
/*    */       case 1:
/* 45 */         return this.resourceComInfo.getLastname(paramString1);
/*    */       case 3:
/* 47 */         return this.subCompanyComInfo.getSubCompanyname(paramString1);
/*    */     } 
/*    */     
/* 50 */     return this.departmentComInfo.getDepartmentname(paramString1);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/report/DocWfReportSort.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */