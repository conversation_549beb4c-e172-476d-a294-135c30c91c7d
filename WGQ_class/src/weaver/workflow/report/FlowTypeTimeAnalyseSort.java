/*    */ package weaver.workflow.report;
/*    */ import weaver.general.BaseBean;
/*    */ import weaver.general.ThreadVarLanguage;
/*    */ import weaver.general.Util;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ public class FlowTypeTimeAnalyseSort extends BaseBean {
/*    */   public String getSpendsString(String paramString) {
/*  9 */     double d = Util.getDoubleValue(paramString);
/* 10 */     if (d <= 0.0D) {
/* 11 */       return "0" + SystemEnv.getHtmlLabelName(15049, ThreadVarLanguage.getLang()) + "";
/*    */     }
/* 13 */     int i = (int)d;
/* 14 */     int j = (int)((d - i) * 60.0D + 0.5D);
/* 15 */     StringBuilder stringBuilder = new StringBuilder();
/* 16 */     if (i >= 24) {
/* 17 */       stringBuilder.append(i / 24).append("" + SystemEnv.getHtmlLabelName(383378, ThreadVarLanguage.getLang()) + "");
/*    */     }
/* 19 */     if (i > 0) {
/* 20 */       stringBuilder.append(i % 24).append("" + SystemEnv.getHtmlLabelName(391, ThreadVarLanguage.getLang()) + "");
/*    */     }
/* 22 */     stringBuilder.append(j).append("" + SystemEnv.getHtmlLabelName(15049, ThreadVarLanguage.getLang()) + "");
/* 23 */     return stringBuilder.toString();
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/report/FlowTypeTimeAnalyseSort.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */