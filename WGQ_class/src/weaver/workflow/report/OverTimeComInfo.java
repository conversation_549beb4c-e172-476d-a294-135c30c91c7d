/*     */ package weaver.workflow.report;
/*     */ 
/*     */ import weaver.cache.CacheBase;
/*     */ import weaver.cache.CacheColumn;
/*     */ import weaver.cache.CacheColumnType;
/*     */ import weaver.cache.CacheItem;
/*     */ import weaver.cache.CacheMap;
/*     */ import weaver.cache.PKColumn;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.StaticObj;
/*     */ import weaver.general.Util;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class OverTimeComInfo
/*     */   extends CacheBase
/*     */ {
/*     */   @PKColumn(type = CacheColumnType.NUMBER)
/*  31 */   protected static String PK_NAME = "nodeid";
/*     */   @CacheColumn
/*     */   protected static int nodepasshour;
/*     */   @CacheColumn
/*     */   protected static int nodepassminute;
/*     */   @CacheColumn
/*     */   protected static int workflowid;
/*     */   
/*     */   public CacheMap initCache() throws Exception {
/*  40 */     CacheMap cacheMap = createCacheMap();
/*     */     
/*  42 */     RecordSet recordSet = new RecordSet();
/*     */ 
/*     */     
/*  45 */     String str = "select workflow_nodeLink.workflowid,nodeid,max(nodepasshour) as nodepasshour ,max(nodepassminute) as nodepassminute from workflow_nodeLink where wfrequestid is null or wfrequestid<=0 group by workflow_nodeLink.workflowid,nodeid";
/*  46 */     recordSet.executeSql(str);
/*  47 */     while (recordSet.next()) {
/*  48 */       String str1 = Util.null2String(recordSet.getString(PK_NAME));
/*  49 */       CacheItem cacheItem = createCacheItem();
/*  50 */       parseResultSetToCacheItem(recordSet, cacheItem);
/*     */ 
/*     */ 
/*     */       
/*  54 */       modifyCacheItem(str1, cacheItem);
/*  55 */       cacheMap.put(str1, cacheItem);
/*     */     } 
/*     */     
/*  58 */     StaticObj.getInstance().putObject("PluginOverTimeComInfoUpdate", "1");
/*  59 */     return cacheMap;
/*     */   }
/*     */ 
/*     */   
/*     */   protected void modifyCacheItem(String paramString, CacheItem paramCacheItem) {
/*  64 */     String str1 = (String)paramCacheItem.get(nodepasshour);
/*  65 */     String str2 = (String)paramCacheItem.get(nodepassminute);
/*     */     
/*  67 */     paramCacheItem.set(nodepasshour, "-1".equals(str1) ? "0" : str1);
/*  68 */     paramCacheItem.set(nodepassminute, "-1".equals(str2) ? "0" : str2);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getTimeNum() {
/*  77 */     return size();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public boolean next(String paramString) {
/*  89 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getNodeid() {
/*  98 */     return (String)getRowValue(0);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getWorkFlowids() {
/* 107 */     return (String)getRowValue(workflowid);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getNodeid(String paramString) {
/* 118 */     return (String)getValue(0, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getWorkFlowids(String paramString) {
/* 129 */     return (String)getValue(workflowid, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getOverHour(String paramString) {
/* 140 */     return (String)getValue(nodepasshour, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getOverTime(String paramString) {
/* 151 */     return (String)getValue(nodepassminute, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void removeBrowserCache() {
/* 158 */     removeCache();
/*     */   }
/*     */ 
/*     */   
/*     */   public CacheItem initCache(String paramString) {
/* 163 */     RecordSet recordSet = new RecordSet();
/* 164 */     recordSet.executeSql("select workflow_nodeLink.workflowid,nodeid,max(nodepasshour) nodepasshour ,max(nodepassminute)  nodepassminute from workflow_nodeLink  where nodeid='" + paramString + "' group by workflow_nodeLink.workflowid,nodeid  ");
/* 165 */     if (recordSet.next()) {
/* 166 */       CacheItem cacheItem = createCacheItem();
/* 167 */       parseResultSetToCacheItem(recordSet, cacheItem);
/* 168 */       modifyCacheItem(paramString, cacheItem);
/* 169 */       return cacheItem;
/*     */     } 
/* 171 */     return null;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean addTimeInfoCache(String paramString) {
/* 182 */     addCache(paramString);
/* 183 */     StaticObj.getInstance().putObject("PluginOverTimeComInfoUpdate", "1");
/* 184 */     return true;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void updateTimeInfoCache(String paramString) {
/* 195 */     updateCache(paramString);
/* 196 */     StaticObj.getInstance().putObject("PluginOverTimeComInfoUpdate", "1");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void deleteDocInfoCache(String paramString) {
/* 207 */     deleteCache(paramString);
/* 208 */     StaticObj.getInstance().putObject("PluginOverTimeComInfoUpdate", "1");
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/report/OverTimeComInfo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */