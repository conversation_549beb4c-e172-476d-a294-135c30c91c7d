/*     */ package weaver.workflow.transfer;
/*     */ 
/*     */ import com.engine.workflow.constant.node.OperatorDBType;
/*     */ import java.util.ArrayList;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.conn.RecordSetTrans;
/*     */ import weaver.general.ThreadVarLanguage;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.check.JobComInfo;
/*     */ import weaver.hrm.company.DepartmentComInfo;
/*     */ import weaver.hrm.company.SubCompanyComInfo;
/*     */ import weaver.hrm.companyvirtual.DepartmentVirtualComInfo;
/*     */ import weaver.hrm.companyvirtual.SubCompanyVirtualComInfo;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ import weaver.hrm.roles.RolesComInfo;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ public class PermissionTransferMgr
/*     */ {
/*  21 */   private String groupDetailids = "0, 0";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean transfer(String paramString, int paramInt) {
/*  30 */     RecordSetTrans recordSetTrans = new RecordSetTrans();
/*  31 */     recordSetTrans.setAutoCommit(false);
/*     */     
/*  33 */     if (paramString == null || "".equals(paramString)) {
/*  34 */       return false;
/*     */     }
/*     */     try {
/*  37 */       String str = "update workflow_groupdetail set objid=" + paramInt + " where id in (" + paramString + ")";
/*  38 */       recordSetTrans.executeSql(str);
/*  39 */       recordSetTrans.commit();
/*  40 */       return true;
/*  41 */     } catch (Exception exception) {
/*  42 */       recordSetTrans.rollback();
/*  43 */       exception.printStackTrace();
/*     */       
/*  45 */       return false;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getNodeType(String paramString) {
/*  52 */     RecordSet recordSet = new RecordSet();
/*  53 */     String str = "";
/*  54 */     recordSet.executeSql("select nodetype,nodeid  from workflow_flownode t where t.nodeid in(select nodeid from workflow_nodegroup where id in(select groupid from workflow_groupdetail where id in('" + paramString + "') ))");
/*  55 */     if (recordSet.next()) {
/*  56 */       str = Util.null2String(recordSet.getString("nodetype"));
/*     */     }
/*     */     
/*  59 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean transfer(String paramString1, int paramInt, String paramString2, String paramString3) {
/*  69 */     RecordSetTrans recordSetTrans = new RecordSetTrans();
/*  70 */     RecordSet recordSet1 = new RecordSet();
/*  71 */     RecordSet recordSet2 = new RecordSet();
/*  72 */     recordSetTrans.setAutoCommit(false);
/*  73 */     recordSet1.writeLog("=替换权限===");
/*  74 */     if (paramString1 == null || "".equals(paramString1)) {
/*  75 */       return false;
/*     */     }
/*     */     try {
/*  78 */       String str = "";
/*  79 */       if (paramString2.equals("1")) {
/*     */         
/*  81 */         ArrayList<String> arrayList = Util.TokenizerString(paramString1, ",");
/*     */         
/*  83 */         for (byte b = 0; b < arrayList.size(); b++) {
/*  84 */           if (getNodeType("" + arrayList.get(b)).equals("0")) {
/*  85 */             str = "update workflow_groupdetail set objid=" + paramInt + " where id in (" + arrayList.get(b) + ")";
/*  86 */             recordSet1.writeLog("=a===sql:" + str);
/*  87 */             recordSet1.executeSql(str);
/*     */             
/*  89 */             str = "update Workflow_HrmOperator set objid=" + paramInt + " where groupdetailid in (" + arrayList.get(b) + ") and objid='" + paramString3 + "'";
/*  90 */             recordSet1.executeSql(str);
/*  91 */             recordSet1.writeLog("=b===sql:" + str);
/*     */           }
/*     */           else {
/*     */             
/*  95 */             str = "update Workflow_HrmOperator set objid=" + paramInt + " where groupdetailid in (" + arrayList.get(b) + ") and objid='" + paramString3 + "'";
/*  96 */             recordSet1.executeSql(str);
/*     */           }
/*     */         
/*     */         } 
/* 100 */       } else if (paramString2.equals("58")) {
/* 101 */         ArrayList<String> arrayList = Util.TokenizerString(paramString1, ",");
/* 102 */         for (byte b = 0; b < arrayList.size(); b++) {
/* 103 */           if (getNodeType("" + arrayList.get(b)).equals("0")) {
/* 104 */             str = "update workflow_groupdetail set jobobj='" + paramInt + "' where id in (" + arrayList.get(b) + ")";
/*     */           } else {
/* 106 */             String str1 = "";
/* 107 */             recordSet2.executeSql("select jobobj from workflow_groupdetail where id =" + arrayList.get(b));
/* 108 */             while (recordSet2.next()) {
/* 109 */               str1 = recordSet2.getString("jobobj");
/*     */             }
/* 111 */             ArrayList<String> arrayList1 = Util.TokenizerString(str1, ",");
/* 112 */             int i = arrayList1.indexOf(paramString3);
/* 113 */             arrayList1.set(i, paramInt + "");
/* 114 */             str1 = "";
/* 115 */             for (byte b1 = 0; b1 < arrayList1.size(); b1++) {
/* 116 */               if ("".equals(str1)) {
/* 117 */                 str1 = arrayList1.get(b1);
/*     */               } else {
/* 119 */                 str1 = str1 + "," + (String)arrayList1.get(b1);
/*     */               } 
/*     */             } 
/* 122 */             str = "update workflow_groupdetail set jobobj='" + str1 + "' where id in (" + arrayList.get(b) + ")";
/*     */           } 
/* 124 */           recordSet1.executeSql(str);
/*     */         } 
/*     */       } else {
/* 127 */         str = "update workflow_groupdetail set objid=" + paramInt + " where id in (" + paramString1 + ")";
/*     */       } 
/* 129 */       recordSetTrans.executeSql(str);
/* 130 */       recordSetTrans.commit();
/* 131 */       return true;
/* 132 */     } catch (Exception exception) {
/* 133 */       recordSetTrans.rollback();
/* 134 */       exception.printStackTrace();
/*     */       
/* 136 */       return false;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean copy(String paramString, int paramInt) {
/* 146 */     RecordSetTrans recordSetTrans = new RecordSetTrans();
/* 147 */     recordSetTrans.setAutoCommit(false);
/*     */     
/* 149 */     if (paramString == null || "".equals(paramString)) {
/* 150 */       return false;
/*     */     }
/*     */     
/*     */     try {
/* 154 */       String str = "insert INTO workflow_groupdetail(groupid,type,objid,level_n,level2_n,orders,signorder,CONDITIONS,CONDITIONCN,IsCoadjutant,signtype,issyscoadjutant,issubmitdesc,ispending,isforward,ismodify,coadjutants,coadjutantcn,deptField, subcompanyField,bhxj) SELECT groupid,type," + paramInt + ",level_n,level2_n,orders,signorder,CONDITIONS,CONDITIONCN,IsCoadjutant,signtype,issyscoadjutant,issubmitdesc,ispending,isforward,ismodify,coadjutants,coadjutantcn,deptField, subcompanyField ,bhxjfrom workflow_groupdetail where id in (" + paramString + ")";
/*     */ 
/*     */       
/* 157 */       recordSetTrans.executeSql(str);
/* 158 */       recordSetTrans.commit();
/* 159 */       return true;
/* 160 */     } catch (Exception exception) {
/* 161 */       recordSetTrans.rollback();
/* 162 */       exception.printStackTrace();
/*     */       
/* 164 */       return false;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean copy(String paramString1, int paramInt, String paramString2, String paramString3) {
/* 175 */     RecordSetTrans recordSetTrans = new RecordSetTrans();
/* 176 */     RecordSet recordSet1 = new RecordSet();
/* 177 */     RecordSet recordSet2 = new RecordSet();
/* 178 */     RecordSet recordSet3 = new RecordSet();
/* 179 */     recordSetTrans.setAutoCommit(false);
/* 180 */     if (paramString1 == null || "".equals(paramString1)) {
/* 181 */       return false;
/*     */     }
/*     */     
/*     */     try {
/* 185 */       String str = "";
/* 186 */       if (paramString2.equals("1")) {
/* 187 */         String str1 = " SELECT groupid,type," + paramInt + ",level_n,level2_n,orders,signorder,CONDITIONS,CONDITIONCN,IsCoadjutant,signtype,issyscoadjutant,issubmitdesc,ispending,isforward,ismodify,coadjutants,coadjutantcn,deptField, subcompanyField,bhxj from workflow_groupdetail where id in (" + paramString1 + ")";
/*     */         
/* 189 */         recordSet1.executeSql(str1);
/* 190 */         while (recordSet1.next()) {
/* 191 */           String str2 = Util.null2String(recordSet1.getString("groupid"));
/* 192 */           String str3 = Util.null2String(recordSet1.getString("type"));
/* 193 */           int i = paramInt;
/* 194 */           String str4 = Util.null2String(recordSet1.getString("level_n"));
/* 195 */           String str5 = Util.null2String(recordSet1.getString("level2_n"));
/* 196 */           String str6 = Util.null2String(recordSet1.getString("orders"));
/* 197 */           String str7 = Util.null2String(recordSet1.getString("signorder"));
/* 198 */           String str8 = Util.null2String(recordSet1.getString("CONDITIONS"));
/* 199 */           String str9 = Util.null2String(recordSet1.getString("CONDITIONCN"));
/* 200 */           str9 = str9.replace("'", "''");
/* 201 */           String str10 = Util.null2String(recordSet1.getString("IsCoadjutant"));
/* 202 */           String str11 = Util.null2String(recordSet1.getString("signtype"));
/* 203 */           String str12 = Util.null2String(recordSet1.getString("issyscoadjutant"));
/* 204 */           String str13 = Util.null2String(recordSet1.getString("issubmitdesc"));
/* 205 */           String str14 = Util.null2String(recordSet1.getString("ispending"));
/* 206 */           String str15 = Util.null2String(recordSet1.getString("isforward"));
/* 207 */           String str16 = Util.null2String(recordSet1.getString("ismodify"));
/* 208 */           String str17 = Util.null2String(recordSet1.getString("coadjutants"));
/* 209 */           String str18 = Util.null2String(recordSet1.getString("coadjutantcn"));
/* 210 */           String str19 = Util.null2String(recordSet1.getString("deptField"));
/* 211 */           String str20 = Util.null2String(recordSet1.getString("subcompanyField"));
/* 212 */           String str21 = Util.null2String(recordSet1.getString("bhxj"));
/*     */           
/* 214 */           String str22 = "insert INTO workflow_groupdetail(groupid,type,objid,level_n,level2_n,orders,signorder,CONDITIONS,CONDITIONCN,IsCoadjutant,signtype,issyscoadjutant,issubmitdesc,ispending,isforward,ismodify,coadjutants,coadjutantcn,deptField, subcompanyField,bhxj)";
/* 215 */           str22 = str22 + "values('" + str2 + "','" + str3 + "','" + i + "','" + str4 + "','" + str5 + "','" + str6 + "','" + str7 + "','" + str8 + "','" + str9 + "','" + str10 + "','" + str11 + "','" + str12 + "','" + str13 + "','" + str14 + "','" + str15 + "','" + str16 + "','" + str17 + "','" + str18 + "','" + str19 + "','" + str20 + "','" + str21 + "')";
/* 216 */           recordSet2.executeSql(str22);
/*     */ 
/*     */           
/* 219 */           int j = -1;
/* 220 */           recordSet3.executeSql("select max(id) as id  from workflow_groupdetail  ");
/* 221 */           if (recordSet3.next()) {
/* 222 */             j = Util.getIntValue(recordSet3.getString("id"), 0);
/*     */           }
/* 224 */           if (j > 0) {
/* 225 */             recordSet3.executeSql("insert into Workflow_HrmOperator(type,objid,groupid,groupdetailid,orders)values('3','" + paramInt + "','" + str2 + "','" + j + "','" + str6 + "')");
/*     */           }
/*     */         }
/*     */       
/* 229 */       } else if (paramString2.equals("58")) {
/* 230 */         str = "insert INTO workflow_groupdetail(groupid,type,objid,level_n,level2_n,orders,signorder,CONDITIONS,CONDITIONCN,IsCoadjutant,signtype,issyscoadjutant,issubmitdesc,ispending,isforward,ismodify,coadjutants,coadjutantcn,deptField, subcompanyField,jobobj,jobfield) SELECT groupid,type,objid,level_n,level2_n,orders,signorder,CONDITIONS,CONDITIONCN,IsCoadjutant,signtype,issyscoadjutant,issubmitdesc,ispending,isforward,ismodify,coadjutants,coadjutantcn,deptField, subcompanyField," + paramInt + ",jobfield from workflow_groupdetail where id in (" + paramString1 + ")";
/*     */ 
/*     */         
/* 233 */         recordSetTrans.executeSql(str);
/* 234 */         recordSetTrans.commit();
/*     */       } else {
/* 236 */         str = "insert INTO workflow_groupdetail(groupid,type,objid,level_n,level2_n,orders,signorder,CONDITIONS,CONDITIONCN,IsCoadjutant,signtype,issyscoadjutant,issubmitdesc,ispending,isforward,ismodify,coadjutants,coadjutantcn,deptField, subcompanyField) SELECT groupid,type," + paramInt + ",level_n,level2_n,orders,signorder,CONDITIONS,CONDITIONCN,IsCoadjutant,signtype,issyscoadjutant,issubmitdesc,ispending,isforward,ismodify,coadjutants,coadjutantcn,deptField, subcompanyField from workflow_groupdetail where id in (" + paramString1 + ")";
/*     */ 
/*     */         
/* 239 */         recordSetTrans.executeSql(str);
/* 240 */         recordSetTrans.commit();
/*     */       } 
/* 242 */       return true;
/* 243 */     } catch (Exception exception) {
/* 244 */       recordSetTrans.rollback();
/* 245 */       exception.printStackTrace();
/*     */       
/* 247 */       return false;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean delete(String paramString) {
/* 256 */     RecordSetTrans recordSetTrans = new RecordSetTrans();
/* 257 */     recordSetTrans.setAutoCommit(false);
/*     */     
/* 259 */     if (paramString == null || "".equals(paramString)) {
/* 260 */       return false;
/*     */     }
/*     */     
/*     */     try {
/* 264 */       String str = "delete from workflow_groupdetail where id in (" + paramString + ")";
/* 265 */       recordSetTrans.executeSql(str);
/* 266 */       recordSetTrans.commit();
/* 267 */       return true;
/* 268 */     } catch (Exception exception) {
/* 269 */       recordSetTrans.rollback();
/* 270 */       exception.printStackTrace();
/*     */       
/* 272 */       return false;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean deletegroupdetail(String paramString) {
/* 280 */     RecordSet recordSet = new RecordSet();
/* 281 */     recordSet.writeLog("===deletegroupdetail===select 1 from Workflow_HrmOperator where groupdetailid='" + paramString + "'");
/* 282 */     recordSet.executeSql("select 1 from Workflow_HrmOperator where groupdetailid='" + paramString + "'");
/* 283 */     if (recordSet.next()) {
/* 284 */       return true;
/*     */     }
/*     */     
/* 287 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean delete(String paramString1, String paramString2, String paramString3) {
/* 297 */     RecordSetTrans recordSetTrans = new RecordSetTrans();
/* 298 */     RecordSet recordSet1 = new RecordSet();
/* 299 */     RecordSet recordSet2 = new RecordSet();
/* 300 */     RecordSet recordSet3 = new RecordSet();
/* 301 */     recordSetTrans.setAutoCommit(false);
/* 302 */     if (paramString1 == null || "".equals(paramString1)) {
/* 303 */       return false;
/*     */     }
/* 305 */     recordSet2.writeLog("==============删除权限====================");
/*     */     try {
/* 307 */       String str = "";
/* 308 */       if (paramString2.equals("1")) {
/* 309 */         recordSet2.writeLog("==============删除权限====================groupDetailids:" + paramString1);
/* 310 */         ArrayList<String> arrayList = Util.TokenizerString(paramString1, ",");
/* 311 */         for (byte b = 0; b < arrayList.size(); b++) {
/*     */ 
/*     */           
/* 314 */           recordSet2.writeLog("======delete from Workflow_HrmOperator  where groupdetailid='" + arrayList.get(b) + "' and objid='" + paramString3 + "'");
/* 315 */           recordSet1.executeSql("delete from Workflow_HrmOperator  where groupdetailid='" + arrayList.get(b) + "' and objid='" + paramString3 + "'");
/*     */ 
/*     */           
/* 318 */           if (!deletegroupdetail("" + arrayList.get(b))) {
/* 319 */             str = "delete from workflow_groupdetail where id in (" + arrayList.get(b) + ")";
/* 320 */             recordSet2.writeLog("======sql:" + str);
/* 321 */             recordSet2.executeSql(str);
/*     */           } 
/*     */         } 
/* 324 */       } else if (paramString2.equals("58")) {
/* 325 */         ArrayList<String> arrayList = Util.TokenizerString(paramString1, ",");
/* 326 */         for (byte b = 0; b < arrayList.size(); b++) {
/* 327 */           if (getNodeType("" + arrayList.get(b)).equals("0")) {
/* 328 */             str = "delete from workflow_groupdetail where id in (" + arrayList.get(b) + ")";
/*     */           } else {
/* 330 */             String str1 = "";
/* 331 */             recordSet3.executeSql("select jobobj from workflow_groupdetail where id =" + arrayList.get(b));
/* 332 */             while (recordSet3.next()) {
/* 333 */               str1 = recordSet3.getString("jobobj");
/*     */             }
/* 335 */             ArrayList<String> arrayList1 = Util.TokenizerString(str1, ",");
/* 336 */             int i = arrayList1.indexOf(paramString3);
/* 337 */             arrayList1.remove(i);
/* 338 */             str1 = "";
/* 339 */             for (byte b1 = 0; b1 < arrayList1.size(); b1++) {
/* 340 */               if ("".equals(str1)) {
/* 341 */                 str1 = arrayList1.get(b1);
/*     */               } else {
/* 343 */                 str1 = str1 + "," + (String)arrayList1.get(b1);
/*     */               } 
/*     */             } 
/* 346 */             if (!"".equals(str1)) {
/* 347 */               str = "update workflow_groupdetail set jobobj='" + str1 + "' where id in (" + arrayList.get(b) + ")";
/*     */             } else {
/* 349 */               str = "delete from workflow_groupdetail where id in (" + arrayList.get(b) + ")";
/*     */             } 
/*     */           } 
/* 352 */           recordSetTrans.executeSql(str);
/*     */         } 
/* 354 */         recordSetTrans.commit();
/*     */       } else {
/* 356 */         str = "delete from workflow_groupdetail where id in (" + paramString1 + ")";
/* 357 */         recordSetTrans.executeSql(str);
/* 358 */         recordSetTrans.commit();
/*     */       } 
/*     */       
/* 361 */       return true;
/* 362 */     } catch (Exception exception) {
/* 363 */       recordSetTrans.rollback();
/* 364 */       exception.printStackTrace();
/*     */       
/* 366 */       return false;
/*     */     } 
/*     */   }
/*     */   
/*     */   public String getWfTypeNameByTypeID(String paramString) {
/* 371 */     String str = "SELECT typename FROM workflow_type WHERE id=" + paramString;
/*     */     
/* 373 */     RecordSet recordSet = new RecordSet();
/* 374 */     recordSet.executeSql(str);
/* 375 */     if (recordSet.next()) {
/* 376 */       return Util.null2String(recordSet.getString("typename"));
/*     */     }
/* 378 */     return "";
/*     */   }
/*     */   
/*     */   public String getWorkflowNameWithVersion(String paramString1, String paramString2) {
/* 382 */     String[] arrayOfString = Util.TokenizerString2(paramString2, "+");
/* 383 */     String str1 = arrayOfString[0];
/* 384 */     String str2 = arrayOfString[1];
/* 385 */     String str3 = arrayOfString[2];
/* 386 */     if ("3".equals(str1)) {
/* 387 */       str2 = "".equals(Util.null2String(str2).trim()) ? "1" : str2;
/* 388 */       paramString1 = paramString1 + "<span style = \"color:gray\">【" + SystemEnv.getHtmlLabelName(18500, Util.getIntValue(str3)) + "V" + str2 + "】</span>";
/*     */     } 
/* 390 */     return paramString1;
/*     */   }
/*     */   
/*     */   public String getNodeTypeDescByTypeID(String paramString) {
/* 394 */     if ("0".equals(paramString))
/* 395 */       return "" + SystemEnv.getHtmlLabelName(10000122, ThreadVarLanguage.getLang()) + ""; 
/* 396 */     if ("1".equals(paramString))
/* 397 */       return "" + SystemEnv.getHtmlLabelName(504446, ThreadVarLanguage.getLang()) + ""; 
/* 398 */     if ("2".equals(paramString))
/* 399 */       return "" + SystemEnv.getHtmlLabelName(10000907, ThreadVarLanguage.getLang()) + ""; 
/* 400 */     if ("3".equals(paramString)) {
/* 401 */       return "" + SystemEnv.getHtmlLabelName(10004334, ThreadVarLanguage.getLang()) + "";
/*     */     }
/* 403 */     return "";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getWorkflowName(String paramString1, String paramString2) {
/* 413 */     String str = "";
/* 414 */     SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/* 415 */     if (!"".equals(paramString2)) {
/* 416 */       str = paramString1 + "(" + SystemEnv.getHtmlLabelName(19799, ThreadVarLanguage.getLang()) + ":" + subCompanyComInfo.getSubCompanyname(paramString2) + ")";
/*     */     } else {
/* 418 */       str = paramString1;
/*     */     } 
/* 420 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getHrmOperator(String paramString) {
/* 429 */     RecordSet recordSet = new RecordSet();
/* 430 */     String str = "";
/*     */     
/*     */     try {
/* 433 */       ResourceComInfo resourceComInfo = new ResourceComInfo();
/* 434 */       recordSet.executeSql("select * from Workflow_HrmOperator where groupdetailid='" + paramString + "'");
/* 435 */       while (recordSet.next()) {
/* 436 */         if (str.equals("")) {
/* 437 */           str = Util.null2String(resourceComInfo.getLastname(recordSet.getString("objid"))); continue;
/*     */         } 
/* 439 */         str = str + "," + Util.null2String(resourceComInfo.getLastname(recordSet.getString("objid")));
/*     */       }
/*     */     
/* 442 */     } catch (Exception exception) {
/*     */       
/* 444 */       exception.printStackTrace();
/*     */     } 
/* 446 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getHrmOperator(String paramString1, String paramString2) {
/* 456 */     RecordSet recordSet = new RecordSet();
/* 457 */     String str = "";
/*     */     
/*     */     try {
/* 460 */       ResourceComInfo resourceComInfo = new ResourceComInfo();
/* 461 */       recordSet.executeSql("select * from Workflow_HrmOperator where groupdetailid='" + paramString1 + "' and objid='" + paramString2 + "'");
/* 462 */       while (recordSet.next()) {
/* 463 */         if (str.equals("")) {
/* 464 */           str = Util.null2String(resourceComInfo.getLastname(recordSet.getString("objid"))); continue;
/*     */         } 
/* 466 */         str = str + "," + Util.null2String(resourceComInfo.getLastname(recordSet.getString("objid")));
/*     */       }
/*     */     
/* 469 */     } catch (Exception exception) {
/*     */       
/* 471 */       exception.printStackTrace();
/*     */     } 
/* 473 */     return str;
/*     */   }
/*     */   
/*     */   public String getPermissionDetail(String paramString) throws Exception {
/* 477 */     String str = "SELECT * FROM workflow_groupdetail WHERE id=" + paramString;
/* 478 */     RecordSet recordSet = new RecordSet();
/* 479 */     recordSet.executeSql(str);
/* 480 */     if (recordSet.next()) {
/* 481 */       String str1 = Util.null2String(recordSet.getString("type"));
/* 482 */       String str2 = Util.null2String(recordSet.getString("id"));
/* 483 */       int i = Util.getIntValue(recordSet.getString("objid"), 0);
/* 484 */       String str3 = Util.null2String(recordSet.getString("level_n"));
/* 485 */       String str4 = Util.null2String(recordSet.getString("level2_n"));
/* 486 */       String str5 = Util.null2String(recordSet.getString("signtype"));
/* 487 */       int j = Util.getIntValue(recordSet.getString("signorder"), 0);
/* 488 */       int k = Util.getIntValue(recordSet.getString("bhxj"), 0);
/* 489 */       String str6 = Util.null2String(recordSet.getString("jobobj"));
/* 490 */       String str7 = Util.null2String(recordSet.getString("jobfield"));
/*     */       
/* 492 */       String str8 = "";
/* 493 */       String str9 = "";
/*     */       
/* 495 */       if ("3".equals(str1)) {
/* 496 */         this; str9 = getHrmOperator(str2);
/* 497 */         str8 = "" + SystemEnv.getHtmlLabelName(386760, ThreadVarLanguage.getLang()) + "";
/* 498 */       } else if ("1".equals(str1)) {
/* 499 */         DepartmentComInfo departmentComInfo = new DepartmentComInfo();
/* 500 */         DepartmentVirtualComInfo departmentVirtualComInfo = new DepartmentVirtualComInfo();
/* 501 */         if (i < 0) {
/* 502 */           str9 = departmentVirtualComInfo.getDepartmentname(i + "");
/*     */         } else {
/* 504 */           str9 = departmentComInfo.getDepartmentname(i + "");
/* 505 */         }  str9 = str9 + "[" + getDepartmentLevelName(k, ThreadVarLanguage.getLang()) + "]";
/* 506 */         str8 = "" + SystemEnv.getHtmlLabelName(527043, ThreadVarLanguage.getLang()) + "";
/* 507 */       } else if ("2".equals(str1)) {
/* 508 */         RolesComInfo rolesComInfo = new RolesComInfo();
/* 509 */         str9 = rolesComInfo.getRolesRemark(i + "");
/* 510 */         str8 = "" + SystemEnv.getHtmlLabelName(122, ThreadVarLanguage.getLang()) + "";
/* 511 */       } else if ("30".equals(str1)) {
/* 512 */         SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/* 513 */         SubCompanyVirtualComInfo subCompanyVirtualComInfo = new SubCompanyVirtualComInfo();
/* 514 */         if (i < 0) {
/* 515 */           str9 = subCompanyVirtualComInfo.getSubCompanyname(i + "");
/*     */         } else {
/* 517 */           str9 = subCompanyComInfo.getSubCompanyname(i + "");
/* 518 */         }  str9 = str9 + "[" + getSubcompanyLevelName(k, ThreadVarLanguage.getLang()) + "]";
/* 519 */         str8 = "" + SystemEnv.getHtmlLabelName(141, ThreadVarLanguage.getLang()) + "";
/* 520 */       } else if ("58".equals(str1)) {
/* 521 */         JobComInfo jobComInfo = new JobComInfo();
/* 522 */         String[] arrayOfString = Util.TokenizerString2(str6, ",");
/* 523 */         for (byte b = 0; b < arrayOfString.length; b++) {
/* 524 */           if ("".equals(str9)) {
/* 525 */             str9 = jobComInfo.getJobName(arrayOfString[b]);
/*     */           } else {
/* 527 */             str9 = str9 + "," + jobComInfo.getJobName(arrayOfString[b]);
/*     */           } 
/*     */         } 
/* 530 */         if (str3.equals("0") || str3.equals(OperatorDBType.GENERAL_WF_CREATE_THIS_DEPARTMENT.getDbType())) {
/* 531 */           str9 = str9 + "[" + getDepartmentLevelName(k, ThreadVarLanguage.getLang()) + "]";
/*     */         }
/* 533 */         if (str3.equals("1") || str3.equals(OperatorDBType.GENERAL_WF_CREATE_THIS_SUBCOMPANY.getDbType())) {
/* 534 */           str9 = str9 + "[" + getSubcompanyLevelName(k, ThreadVarLanguage.getLang()) + "]";
/*     */         }
/*     */         
/* 537 */         str8 = "" + SystemEnv.getHtmlLabelName(6086, ThreadVarLanguage.getLang()) + "";
/*     */       } 
/*     */ 
/*     */       
/* 541 */       if (getNodeType(str2).equals("0")) {
/* 542 */         if ("3".equals(str1) || "4".equals(str1)) {
/* 543 */           str8 = str8 + "";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */         
/*     */         }
/* 552 */         else if (j == 1) {
/* 553 */           str8 = str8 + "(" + SystemEnv.getHtmlLabelName(353, ThreadVarLanguage.getLang()) + ")";
/*     */         } else {
/* 555 */           str8 = str8 + "(" + SystemEnv.getHtmlLabelName(21473, ThreadVarLanguage.getLang()) + ")";
/*     */         
/*     */         }
/*     */       
/*     */       }
/* 560 */       else if ("3".equals(str1) || "1".equals(str1) || "2".equals(str1) || "30".equals(str1) || "4".equals(str1) || "58".equals(str1)) {
/* 561 */         if (j == 0) {
/* 562 */           str8 = str8 + "(" + SystemEnv.getHtmlLabelName(15556, ThreadVarLanguage.getLang()) + ")";
/* 563 */         } else if (j == 1) {
/* 564 */           str8 = str8 + "(" + SystemEnv.getHtmlLabelName(15557, ThreadVarLanguage.getLang()) + ")";
/* 565 */         } else if (j == 2) {
/* 566 */           str8 = str8 + "(" + SystemEnv.getHtmlLabelName(15558, ThreadVarLanguage.getLang()) + ")";
/* 567 */         } else if (j == 3) {
/* 568 */           str8 = str8 + "(" + SystemEnv.getHtmlLabelName(25165, ThreadVarLanguage.getLang()) + ") ";
/* 569 */         } else if (j == 4) {
/* 570 */           str8 = str8 + "(" + SystemEnv.getHtmlLabelName(10004335, ThreadVarLanguage.getLang()) + ") ";
/*     */         }
/*     */       
/* 573 */       } else if (j == 1) {
/* 574 */         str8 = str8 + "(" + SystemEnv.getHtmlLabelName(353, ThreadVarLanguage.getLang()) + ")";
/*     */       } else {
/* 576 */         str8 = str8 + "(" + SystemEnv.getHtmlLabelName(21473, ThreadVarLanguage.getLang()) + ")";
/*     */       } 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 582 */       return SystemEnv.getHtmlLabelName(523649, ThreadVarLanguage.getLang()) + "：" + str8 + "&nbsp;&nbsp;" + 
/*     */         
/* 584 */         SystemEnv.getHtmlLabelName(10004336, ThreadVarLanguage.getLang()) + "" + str9;
/*     */     } 
/*     */     
/* 587 */     return "";
/*     */   }
/*     */   
/*     */   public String getDepartmentLevelName(int paramInt1, int paramInt2) {
/* 591 */     if (paramInt1 == 0)
/* 592 */       return SystemEnv.getHtmlLabelName(21837, paramInt2); 
/* 593 */     if (paramInt1 == 1)
/* 594 */       return SystemEnv.getHtmlLabelName(126607, paramInt2); 
/* 595 */     if (paramInt1 == 2) {
/* 596 */       return SystemEnv.getHtmlLabelName(126608, paramInt2);
/*     */     }
/* 598 */     return SystemEnv.getHtmlLabelName(21837, paramInt2);
/*     */   }
/*     */   
/*     */   public String getSubcompanyLevelName(int paramInt1, int paramInt2) {
/* 602 */     if (paramInt1 == 0)
/* 603 */       return SystemEnv.getHtmlLabelName(30792, paramInt2); 
/* 604 */     if (paramInt1 == 1)
/* 605 */       return SystemEnv.getHtmlLabelName(19436, paramInt2); 
/* 606 */     if (paramInt1 == 2) {
/* 607 */       return SystemEnv.getHtmlLabelName(27189, paramInt2);
/*     */     }
/* 609 */     return SystemEnv.getHtmlLabelName(30792, paramInt2);
/*     */   }
/*     */   public String getGroupDetailids() {
/* 612 */     return this.groupDetailids;
/*     */   }
/*     */   
/*     */   public void setGroupDetailids(String paramString) {
/* 616 */     this.groupDetailids = paramString;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/transfer/PermissionTransferMgr.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */