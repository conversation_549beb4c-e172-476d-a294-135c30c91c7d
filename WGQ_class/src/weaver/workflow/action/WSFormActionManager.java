/*     */ package weaver.workflow.action;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import weaver.conn.ConnStatement;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.TimeUtil;
/*     */ import weaver.general.Util;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class WSFormActionManager
/*     */   extends BaseBean
/*     */ {
/*     */   private String operate;
/*     */   private int actionid;
/*     */   private String actionname;
/*     */   private int formid;
/*     */   private int isbill;
/*     */   private String wsurl;
/*     */   private String wsnamespace;
/*     */   private String wsoperation;
/*     */   private String xmltext;
/*     */   private int rettype;
/*     */   private String retstr;
/*     */   private String inpara;
/*     */   private String webservicefrom;
/*  78 */   private String custominterface = "";
/*     */ 
/*     */ 
/*     */   
/*     */   private int isnewsap;
/*     */ 
/*     */ 
/*     */   
/*     */   private String subcompanyid;
/*     */ 
/*     */ 
/*     */   
/*     */   public WSFormActionManager() {
/*  91 */     this.operate = "";
/*  92 */     this.actionid = 0;
/*  93 */     this.actionname = "";
/*  94 */     this.formid = 0;
/*  95 */     this.isbill = 0;
/*  96 */     this.wsurl = "";
/*  97 */     this.wsnamespace = "";
/*  98 */     this.wsoperation = "";
/*  99 */     this.xmltext = "";
/* 100 */     this.rettype = 0;
/* 101 */     this.retstr = "";
/* 102 */     this.inpara = "";
/* 103 */     this.webservicefrom = "";
/* 104 */     this.custominterface = "";
/* 105 */     this.isnewsap = 0;
/* 106 */     this.subcompanyid = "";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getSubcompanyid() {
/* 114 */     return this.subcompanyid;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setSubcompanyid(String paramString) {
/* 122 */     this.subcompanyid = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getIsnewsap() {
/* 129 */     return this.isnewsap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setIsnewsap(int paramInt) {
/* 137 */     this.isnewsap = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getInpara() {
/* 144 */     return this.inpara;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setInpara(String paramString) {
/* 152 */     this.inpara = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getOperate() {
/* 159 */     return this.operate;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setOperate(String paramString) {
/* 166 */     this.operate = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getActionid() {
/* 173 */     return this.actionid;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setActionid(int paramInt) {
/* 180 */     this.actionid = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getActionname() {
/* 187 */     return this.actionname;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setActionname(String paramString) {
/* 194 */     this.actionname = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getFormid() {
/* 202 */     return this.formid;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setFormid(int paramInt) {
/* 210 */     this.formid = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getIsbill() {
/* 218 */     return this.isbill;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setIsbill(int paramInt) {
/* 226 */     this.isbill = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getWsurl() {
/* 233 */     return this.wsurl;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setWsurl(String paramString) {
/* 240 */     this.wsurl = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getWsoperation() {
/* 247 */     return this.wsoperation;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setWsoperation(String paramString) {
/* 254 */     this.wsoperation = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getXmltext() {
/* 261 */     return this.xmltext;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setXmltext(String paramString) {
/* 268 */     this.xmltext = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getRettype() {
/* 275 */     return this.rettype;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setRettype(int paramInt) {
/* 282 */     this.rettype = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getRetstr() {
/* 289 */     return this.retstr;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setRetstr(String paramString) {
/* 296 */     this.retstr = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int doSaveWsAction() {
/* 305 */     ConnStatement connStatement = null;
/*     */     try {
/* 307 */       connStatement = new ConnStatement();
/* 308 */       String str1 = "";
/* 309 */       String str2 = TimeUtil.getCurrentTimeString();
/* 310 */       String str3 = str2.substring(0, 10);
/* 311 */       String str4 = str2.substring(11);
/* 312 */       if (this.actionid <= 0) {
/* 313 */         str1 = "insert into wsformactionset(actionname, formid, isbill, wsurl, wsoperation, xmltext, rettype, retstr, inpara,webservicefrom,custominterface,wsnamespace,CreateDate,CreateTime,ModifyDate,ModifyTime,subcompanyid) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
/* 314 */         connStatement.setStatementSql(str1);
/* 315 */         connStatement.setString(1, this.actionname);
/* 316 */         connStatement.setInt(2, this.formid);
/* 317 */         connStatement.setInt(3, this.isbill);
/* 318 */         connStatement.setString(4, this.wsurl);
/* 319 */         connStatement.setString(5, this.wsoperation);
/* 320 */         connStatement.setString(6, this.xmltext);
/* 321 */         connStatement.setInt(7, this.rettype);
/* 322 */         connStatement.setString(8, this.retstr);
/* 323 */         connStatement.setString(9, this.inpara);
/* 324 */         connStatement.setInt(10, Util.getIntValue(this.webservicefrom, 1));
/* 325 */         connStatement.setInt(11, Util.getIntValue(this.custominterface, 1));
/* 326 */         connStatement.setString(12, this.wsnamespace);
/*     */         
/* 328 */         connStatement.setString(13, str3);
/* 329 */         connStatement.setString(14, str4);
/* 330 */         connStatement.setString(15, str3);
/* 331 */         connStatement.setString(16, str4);
/* 332 */         connStatement.setString(17, this.subcompanyid);
/* 333 */         connStatement.executeUpdate();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */         
/* 340 */         str1 = "select max(id) as maxid from wsformactionset where formid=? and isbill=?";
/* 341 */         connStatement.setStatementSql(str1);
/* 342 */         connStatement.setInt(1, this.formid);
/* 343 */         connStatement.setInt(2, this.isbill);
/* 344 */         connStatement.executeQuery();
/* 345 */         while (connStatement.next()) {
/* 346 */           this.actionid = Util.getIntValue(connStatement.getString("maxid"), 0);
/*     */         }
/*     */       } else {
/* 349 */         str1 = "update wsformactionset set actionname=? ,wsurl=?, wsoperation=?, xmltext=?, rettype=?, retstr=?, inpara=?, webservicefrom=?, custominterface=?,wsnamespace=?,ModifyDate=?,ModifyTime=?,subcompanyid=? where id=?";
/* 350 */         connStatement.setStatementSql(str1);
/* 351 */         connStatement.setString(1, this.actionname);
/* 352 */         connStatement.setString(2, this.wsurl);
/* 353 */         connStatement.setString(3, this.wsoperation);
/* 354 */         connStatement.setString(4, this.xmltext);
/* 355 */         connStatement.setInt(5, this.rettype);
/* 356 */         connStatement.setString(6, this.retstr);
/* 357 */         connStatement.setString(7, this.inpara);
/* 358 */         connStatement.setInt(8, Util.getIntValue(this.webservicefrom, 1));
/* 359 */         connStatement.setString(9, this.custominterface);
/* 360 */         connStatement.setString(10, this.wsnamespace);
/*     */         
/* 362 */         connStatement.setString(11, str3);
/* 363 */         connStatement.setString(12, str4);
/* 364 */         connStatement.setInt(13, this.subcompanyid);
/* 365 */         connStatement.setInt(14, this.actionid);
/*     */         
/* 367 */         connStatement.executeUpdate();
/*     */       } 
/* 369 */     } catch (Exception exception) {
/* 370 */       writeLog(exception);
/* 371 */       this.actionid = -1;
/*     */     } finally {
/*     */       try {
/* 374 */         connStatement.close();
/* 375 */       } catch (Exception exception) {}
/*     */     } 
/*     */ 
/*     */     
/* 379 */     return this.actionid;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int doDeleteWsAction() {
/* 387 */     byte b = -1;
/*     */     try {
/* 389 */       RecordSet recordSet = new RecordSet();
/* 390 */       String str = "delete from wsformactionset where id=" + this.actionid;
/* 391 */       recordSet.execute(str);
/* 392 */       b = 1;
/* 393 */     } catch (Exception exception) {
/* 394 */       writeLog(exception);
/* 395 */       b = 0;
/*     */     } 
/* 397 */     return b;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public ArrayList doSelectWsAction(int paramInt1, int paramInt2) {
/* 407 */     ArrayList<ArrayList<String>> arrayList = new ArrayList();
/*     */     try {
/* 409 */       RecordSet recordSet = new RecordSet();
/* 410 */       String str = "select * from wsformactionset where ";
/* 411 */       if (this.actionid > 0) {
/* 412 */         str = str + " id=" + this.actionid;
/*     */       } else {
/* 414 */         str = str + " formid=" + paramInt1 + " and isbill=" + paramInt2;
/*     */       } 
/* 416 */       recordSet.execute(str);
/* 417 */       while (recordSet.next()) {
/* 418 */         int i = Util.getIntValue(recordSet.getString("id"));
/* 419 */         String str1 = Util.null2String(recordSet.getString("actionname"));
/* 420 */         String str2 = Util.null2String(recordSet.getString("wsurl"));
/* 421 */         String str3 = Util.null2String(recordSet.getString("wsoperation"));
/* 422 */         String str4 = Util.null2String(recordSet.getString("xmltext"));
/* 423 */         int j = Util.getIntValue(recordSet.getString("rettype"));
/* 424 */         String str5 = Util.null2String(recordSet.getString("retstr"));
/* 425 */         String str6 = Util.null2String(recordSet.getString("inpara"));
/* 426 */         String str7 = Util.null2String(recordSet.getString("webservicefrom"));
/* 427 */         String str8 = Util.null2String(recordSet.getString("custominterface"));
/* 428 */         String str9 = Util.null2String(recordSet.getString("wsnamespace"));
/* 429 */         String str10 = Util.null2String(recordSet.getString("subcompanyid"));
/* 430 */         ArrayList<String> arrayList1 = new ArrayList();
/* 431 */         arrayList1.add("" + i);
/* 432 */         arrayList1.add(str1);
/* 433 */         arrayList1.add(str2);
/* 434 */         arrayList1.add(str3);
/* 435 */         arrayList1.add(str4);
/* 436 */         arrayList1.add("" + j);
/* 437 */         arrayList1.add(str5);
/* 438 */         arrayList1.add(str6);
/* 439 */         arrayList1.add(str7);
/* 440 */         arrayList1.add(str8);
/* 441 */         arrayList1.add(str9);
/* 442 */         arrayList1.add(str10);
/*     */         
/* 444 */         arrayList.add(arrayList1);
/*     */       } 
/* 446 */     } catch (Exception exception) {
/* 447 */       writeLog(exception);
/*     */     } 
/* 449 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static void main(String[] paramArrayOfString) {}
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getWebservicefrom() {
/* 466 */     return this.webservicefrom;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setWebservicefrom(String paramString) {
/* 474 */     this.webservicefrom = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCustominterface() {
/* 482 */     return this.custominterface;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setCustominterface(String paramString) {
/* 490 */     this.custominterface = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getWsnamespace() {
/* 497 */     return this.wsnamespace;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setWsnamespace(String paramString) {
/* 504 */     this.wsnamespace = paramString;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/action/WSFormActionManager.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */