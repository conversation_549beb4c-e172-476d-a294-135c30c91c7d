/*      */ package weaver.workflow.action;
/*      */ 
/*      */ import java.io.ByteArrayInputStream;
/*      */ import java.io.InputStream;
/*      */ import java.io.Writer;
/*      */ import java.sql.SQLException;
/*      */ import java.util.ArrayList;
/*      */ import java.util.HashMap;
/*      */ import java.util.Iterator;
/*      */ import java.util.List;
/*      */ import java.util.Map;
/*      */ import oracle.sql.CLOB;
/*      */ import org.dom4j.Document;
/*      */ import org.dom4j.Element;
/*      */ import org.dom4j.io.SAXReader;
/*      */ import weaver.conn.ConnStatement;
/*      */ import weaver.conn.RecordSet;
/*      */ import weaver.conn.RecordSetTrans;
/*      */ import weaver.general.ThreadVarLanguage;
/*      */ import weaver.general.Util;
/*      */ import weaver.hrm.User;
/*      */ import weaver.security.util.SecurityMethodUtil;
/*      */ import weaver.systeminfo.SystemEnv;
/*      */ import weaver.workflow.field.UrlComInfo;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ public class ESBReturnRuleBusiness
/*      */ {
/*   38 */   private int ruleid = -1;
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*   43 */   private User user = null;
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*   48 */   private ESBReturnRuleExpressions expressions = null;
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*   53 */   private List<Map<String, String>> formlist = null;
/*      */ 
/*      */ 
/*      */   
/*      */   public static final String RULE_ID_KEY = "ruleids";
/*      */ 
/*      */ 
/*      */   
/*      */   public static final String RULE_DESC_KEY = "ruledescs";
/*      */ 
/*      */ 
/*      */   
/*      */   public static List<ESBReturnRuleBean> getAllRuleList() {
/*   66 */     ArrayList<ESBReturnRuleBean> arrayList = new ArrayList();
/*   67 */     RecordSet recordSet = new RecordSet();
/*   68 */     recordSet.execute("select * from esbreturnrule where rulesrc=3");
/*   69 */     while (recordSet.next()) {
/*   70 */       ESBReturnRuleBean eSBReturnRuleBean = new ESBReturnRuleBean();
/*   71 */       eSBReturnRuleBean.setId(Util.getIntValue(recordSet.getString("id")));
/*   72 */       eSBReturnRuleBean.setFormid(Util.null2String(recordSet.getString("formid")));
/*   73 */       eSBReturnRuleBean.setLinkid(Util.null2String(recordSet.getString("linkid")));
/*   74 */       eSBReturnRuleBean.setRulesrc(Util.null2String(recordSet.getString("rulesrc")));
/*   75 */       arrayList.add(eSBReturnRuleBean);
/*      */     } 
/*   77 */     return arrayList;
/*      */   }
/*      */   
/*      */   public static ESBReturnRuleBean getRuleBean(int paramInt) {
/*   81 */     RecordSet recordSet = new RecordSet();
/*   82 */     recordSet.execute("select * from esbreturnrule where id=" + paramInt);
/*   83 */     if (recordSet.next()) {
/*   84 */       ESBReturnRuleBean eSBReturnRuleBean = new ESBReturnRuleBean();
/*   85 */       eSBReturnRuleBean.setId(Util.getIntValue(recordSet.getString("id")));
/*   86 */       eSBReturnRuleBean.setFormid(Util.null2String(recordSet.getString("formid")));
/*   87 */       eSBReturnRuleBean.setLinkid(Util.null2String(recordSet.getString("linkid")));
/*   88 */       eSBReturnRuleBean.setRulesrc(Util.null2String(recordSet.getString("rulesrc")));
/*   89 */       eSBReturnRuleBean.setRulename(Util.null2String(recordSet.getString("rulename")));
/*   90 */       eSBReturnRuleBean.setRuledesc(Util.null2String(recordSet.getString("ruledesc")));
/*   91 */       return eSBReturnRuleBean;
/*      */     } 
/*   93 */     return null;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void newRule(ESBReturnRuleBean paramESBReturnRuleBean, RecordSet paramRecordSet) throws Exception {
/*  113 */     if (paramRecordSet.getDBType().equals("oracle") && Util.null2String(paramRecordSet.getOrgindbtype()).equals("oracle")) {
/*      */       
/*  115 */       String str = "insert into esbreturnrule(setid, esbid, version, rulename, condition) values(?,?,?,?,empty_clob())";
/*  116 */       paramRecordSet.executeUpdate(str, new Object[] { Integer.valueOf(paramESBReturnRuleBean.getSetid()), paramESBReturnRuleBean.getEsbid(), Integer.valueOf(paramESBReturnRuleBean.getVersion()), paramESBReturnRuleBean.getRulename() });
/*  117 */     } else if (paramRecordSet.getDBType().equals("mysql")) {
/*  118 */       String str = "insert into esbreturnrule(setid, esbid, version, rulename,`condition`) values(?,?,?,?,?)";
/*  119 */       paramRecordSet.executeUpdate(str, new Object[] { Integer.valueOf(paramESBReturnRuleBean.getSetid()), paramESBReturnRuleBean.getEsbid(), Integer.valueOf(paramESBReturnRuleBean.getVersion()), paramESBReturnRuleBean.getRulename(), paramESBReturnRuleBean.getCondition() });
/*      */     } else {
/*      */       
/*  122 */       String str = "insert into esbreturnrule(setid, esbid, version, rulename, condition) values(?,?,?,?,?)";
/*  123 */       paramRecordSet.executeUpdate(str, new Object[] { Integer.valueOf(paramESBReturnRuleBean.getSetid()), paramESBReturnRuleBean.getEsbid(), Integer.valueOf(paramESBReturnRuleBean.getVersion()), paramESBReturnRuleBean.getRulename(), paramESBReturnRuleBean.getCondition() });
/*      */     } 
/*      */     
/*  126 */     saveClob(paramESBReturnRuleBean.getCondition());
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void updateRule(int paramInt, ESBReturnRuleBean paramESBReturnRuleBean, RecordSetTrans paramRecordSetTrans) throws Exception {
/*  137 */     RecordSet recordSet = new RecordSet();
/*  138 */     String str = "";
/*      */ 
/*      */ 
/*      */     
/*  142 */     str = "update esbreturnrule set  rulename='" + paramESBReturnRuleBean.getRulename();
/*  143 */     if (paramRecordSetTrans.getDBType().equals("oracle") && Util.null2String(paramRecordSetTrans.getOrgindbtype()).equals("oracle")) {
/*  144 */       str = str + "', condition= empty_clob() where id=" + paramInt;
/*  145 */       recordSet.execute(str);
/*  146 */     } else if (paramRecordSetTrans.getDBType().equals("mysql")) {
/*      */ 
/*      */       
/*  149 */       str = str + "', `condition`=? where id=" + paramInt;
/*      */       
/*  151 */       recordSet.executeQuery(str, new Object[] { paramESBReturnRuleBean.getCondit() });
/*      */     } else {
/*      */       
/*  154 */       str = str + "', condition=? where id=" + paramInt;
/*      */ 
/*      */       
/*  157 */       recordSet.executeQuery(str, new Object[] { paramESBReturnRuleBean.getCondit() });
/*      */     } 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  181 */     if (paramRecordSetTrans.getDBType().equals("oracle") && !Util.null2String(paramRecordSetTrans.getOrgindbtype()).equals("dm") && 
/*  182 */       !Util.null2String(paramRecordSetTrans.getOrgindbtype()).equals("st") && !"jc".equalsIgnoreCase(paramRecordSetTrans.getOrgindbtype())) {
/*  183 */       ConnStatement connStatement = null;
/*      */       try {
/*  185 */         String str1 = "select condition from esbreturnrule where id = " + paramInt + " for update";
/*  186 */         if (!"".equals(paramESBReturnRuleBean.getCondit())) {
/*  187 */           connStatement = new ConnStatement();
/*  188 */           connStatement.setStatementSql(str1, false);
/*  189 */           connStatement.executeQuery();
/*  190 */           if (connStatement.next()) {
/*  191 */             CLOB cLOB = connStatement.getClob(1);
/*  192 */             char[] arrayOfChar = paramESBReturnRuleBean.getCondit().toCharArray();
/*  193 */             Writer writer = cLOB.getCharacterOutputStream();
/*  194 */             writer.write(arrayOfChar);
/*  195 */             writer.flush();
/*  196 */             writer.close();
/*      */           } 
/*      */         } 
/*  199 */       } catch (Exception exception) {
/*  200 */         exception.printStackTrace();
/*      */       } finally {
/*  202 */         if (connStatement != null) connStatement.close();
/*      */       
/*      */       } 
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static String newRule(ESBReturnRuleBean paramESBReturnRuleBean) throws Exception {
/*  215 */     String str1 = "";
/*  216 */     RecordSet recordSet = new RecordSet();
/*      */ 
/*      */ 
/*      */     
/*  220 */     String str2 = "insert into esbreturnrule(rulesrc, formid,isbill,linkid,condit,rulename,ruledesc) values('" + paramESBReturnRuleBean.getRulesrc() + "', '" + paramESBReturnRuleBean.getFormid() + "', '" + paramESBReturnRuleBean.getIsbill() + "', '";
/*  221 */     if (recordSet.getDBType().equals("oracle") && Util.null2String(recordSet.getOrgindbtype()).equals("oracle")) {
/*  222 */       str2 = str2 + paramESBReturnRuleBean.getLinkid() + "', empty_clob() , '";
/*      */     } else {
/*      */       
/*  225 */       str2 = str2 + paramESBReturnRuleBean.getLinkid() + "', '" + paramESBReturnRuleBean.getCondit() + "', '";
/*      */     } 
/*      */     
/*  228 */     str2 = str2 + paramESBReturnRuleBean.getRulename() + "', '" + paramESBReturnRuleBean.getRuledesc() + "')";
/*  229 */     recordSet.executeSql(str2);
/*  230 */     str2 = "select max(id) as id from esbreturnrule";
/*  231 */     recordSet.executeSql(str2);
/*  232 */     if (recordSet.next()) {
/*  233 */       str1 = recordSet.getString("id");
/*      */     }
/*      */     
/*  236 */     if (recordSet.getDBType().equals("oracle") && !Util.null2String(recordSet.getOrgindbtype()).equals("dm") && 
/*  237 */       !Util.null2String(recordSet.getOrgindbtype()).equals("st") && !"jc".equalsIgnoreCase(recordSet.getOrgindbtype())) {
/*  238 */       ConnStatement connStatement = null;
/*      */       try {
/*  240 */         String str = "select condition from esbreturnrule where id = " + str1 + " for update";
/*  241 */         if (!"".equals(paramESBReturnRuleBean.getCondit())) {
/*  242 */           connStatement = new ConnStatement();
/*  243 */           connStatement.setStatementSql(str, false);
/*  244 */           connStatement.executeQuery();
/*  245 */           if (connStatement.next()) {
/*  246 */             CLOB cLOB = connStatement.getClob(1);
/*  247 */             char[] arrayOfChar = paramESBReturnRuleBean.getCondit().toCharArray();
/*  248 */             Writer writer = cLOB.getCharacterOutputStream();
/*  249 */             writer.write(arrayOfChar);
/*  250 */             writer.flush();
/*  251 */             writer.close();
/*      */           } 
/*      */         } 
/*  254 */       } catch (Exception exception) {
/*  255 */         exception.printStackTrace();
/*      */       } finally {
/*  257 */         if (connStatement != null) connStatement.close();
/*      */       
/*      */       } 
/*      */     } 
/*  261 */     return str1;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static void updateRule(int paramInt, ESBReturnRuleBean paramESBReturnRuleBean) throws Exception {
/*  271 */     RecordSet recordSet = new RecordSet();
/*  272 */     String str = "";
/*  273 */     if (paramESBReturnRuleBean.getRulesrc().equals("3")) {
/*      */ 
/*      */ 
/*      */       
/*  277 */       str = "update esbreturnrule set rulesrc='" + paramESBReturnRuleBean.getRulesrc() + "', rulename='" + paramESBReturnRuleBean.getRulename() + "', ruledesc='" + paramESBReturnRuleBean.getRuledesc() + "' where id=" + paramInt;
/*      */ 
/*      */ 
/*      */     
/*      */     }
/*      */     else {
/*      */ 
/*      */ 
/*      */       
/*  286 */       str = "update esbreturnrule set rulesrc='" + paramESBReturnRuleBean.getRulesrc() + "', formid='" + paramESBReturnRuleBean.getFormid() + "', isbill='" + paramESBReturnRuleBean.getIsbill() + "', linkid='" + paramESBReturnRuleBean.getLinkid() + "', rulename='" + paramESBReturnRuleBean.getRulename() + "', ruledesc='" + paramESBReturnRuleBean.getRuledesc();
/*  287 */       if (recordSet.getDBType().equals("oracle") && Util.null2String(recordSet.getOrgindbtype()).equals("oracle")) {
/*  288 */         str = str + "', condit= empty_clob()  where id=" + paramInt;
/*      */       } else {
/*  290 */         str = str + "', condit='" + paramESBReturnRuleBean.getCondit() + "' where id=" + paramInt;
/*      */       } 
/*      */     } 
/*      */     
/*  294 */     recordSet.executeSql(str);
/*      */     
/*  296 */     if (recordSet.getDBType().equals("oracle") && !Util.null2String(recordSet.getOrgindbtype()).equals("dm") && !Util.null2String(recordSet.getOrgindbtype()).equals("st") && 
/*  297 */       !paramESBReturnRuleBean.getRulesrc().equals("3") && !"jc".equalsIgnoreCase(recordSet.getOrgindbtype())) {
/*  298 */       ConnStatement connStatement = null;
/*      */       try {
/*  300 */         String str1 = "select condit from esbreturnrule where id = " + paramInt + " for update";
/*  301 */         if (!"".equals(paramESBReturnRuleBean.getCondit())) {
/*  302 */           connStatement = new ConnStatement();
/*  303 */           connStatement.setStatementSql(str1, false);
/*  304 */           connStatement.executeQuery();
/*  305 */           if (connStatement.next()) {
/*  306 */             CLOB cLOB = connStatement.getClob(1);
/*  307 */             char[] arrayOfChar = paramESBReturnRuleBean.getCondit().toCharArray();
/*  308 */             Writer writer = cLOB.getCharacterOutputStream();
/*  309 */             writer.write(arrayOfChar);
/*  310 */             writer.flush();
/*  311 */             writer.close();
/*      */           } 
/*      */         } 
/*  314 */       } catch (Exception exception) {
/*  315 */         exception.printStackTrace();
/*      */       } finally {
/*  317 */         if (connStatement != null) connStatement.close();
/*      */       
/*      */       } 
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*  326 */   public static int expIndex = 1;
/*      */ 
/*      */ 
/*      */   
/*  330 */   public static int rlindex = 1;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private ESBReturnRuleExpressions stream2Exps(InputStream paramInputStream) throws Exception {
/*  338 */     SAXReader sAXReader = new SAXReader();
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  344 */     SecurityMethodUtil.setReaderFeature(sAXReader);
/*  345 */     Document document = null;
/*      */     try {
/*  347 */       document = sAXReader.read(paramInputStream);
/*      */       
/*  349 */       Element element = document.getRootElement();
/*      */       
/*  351 */       return parserXml(element);
/*      */     
/*      */     }
/*  354 */     catch (Exception exception) {
/*  355 */       exception.printStackTrace();
/*      */       
/*  357 */       return null;
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private ESBReturnRuleExpressions parserXml(Element paramElement) {
/*  367 */     if (paramElement == null) return null;
/*      */     
/*  369 */     ESBReturnRuleExpressions eSBReturnRuleExpressions = new ESBReturnRuleExpressions();
/*  370 */     eSBReturnRuleExpressions.setRelation(Util.getIntValue(paramElement.attributeValue("relation"), 0));
/*  371 */     eSBReturnRuleExpressions.setRuleid(this.ruleid);
/*      */     
/*  373 */     List list = paramElement.elements();
/*      */     
/*  375 */     Iterator<Element> iterator = list.iterator();
/*  376 */     while (iterator.hasNext()) {
/*  377 */       Element element = iterator.next();
/*      */       
/*  379 */       String str = element.getName();
/*      */       
/*  381 */       if ("expressions".equals(str)) {
/*  382 */         eSBReturnRuleExpressions.getChildrens().add(parserXml(element)); continue;
/*  383 */       }  if ("expression".equals(str)) {
/*  384 */         String str1 = Util.null2String(element.attributeValue("id"));
/*  385 */         int i = Util.getIntValue(element.attributeValue("datafield"));
/*  386 */         String str2 = Util.null2String(element.attributeValue("datafieldlabel"));
/*  387 */         String str3 = Util.null2String(element.attributeValue("htmltype"));
/*  388 */         String str4 = Util.null2String(element.attributeValue("fieldtype"));
/*  389 */         String str5 = Util.null2String(element.attributeValue("dbtype"));
/*  390 */         int j = Util.getIntValue(element.attributeValue("typehrm"), -1);
/*  391 */         int k = Util.getIntValue(element.attributeValue("compareoption1"), -1);
/*  392 */         int m = Util.getIntValue(element.attributeValue("compareoption2"), -1);
/*  393 */         String str6 = Util.null2String(element.attributeValue("browservalue"));
/*  394 */         String str7 = Util.null2String(element.attributeValue("browserspantext"));
/*  395 */         String str8 = Util.null2String(element.attributeValue("browserspanlabel"));
/*  396 */         String str9 = Util.null2String(element.attributeValue("textvalue1"));
/*  397 */         int n = Util.getIntValue(element.attributeValue("selectvalue1"), -1);
/*  398 */         String str10 = Util.null2String(element.attributeValue("checkboxvalue1"));
/*  399 */         String str11 = Util.null2String(element.attributeValue("textvalue2"));
/*  400 */         String str12 = Util.null2String(element.attributeValue("paramtype"));
/*  401 */         String str13 = Util.null2String(element.attributeValue("valuetype"));
/*  402 */         String str14 = Util.null2String(element.attributeValue("valuevarid"));
/*  403 */         String str15 = Util.null2String(element.attributeValue("redius"));
/*  404 */         String str16 = Util.null2String(element.attributeValue("nodeId"));
/*  405 */         String str17 = Util.null2String(element.attributeValue("meetCondition"));
/*  406 */         String str18 = Util.null2String(element.attributeValue("jingdu"));
/*  407 */         String str19 = Util.null2String(element.attributeValue("weidu"));
/*      */         
/*  409 */         ESBReturnRuleExpressionBean eSBReturnRuleExpressionBean = new ESBReturnRuleExpressionBean();
/*  410 */         eSBReturnRuleExpressionBean.setRuleid(this.ruleid);
/*  411 */         eSBReturnRuleExpressionBean.setId(Util.getIntValue(str1, -1));
/*  412 */         eSBReturnRuleExpressionBean.setDatafield(i);
/*  413 */         eSBReturnRuleExpressionBean.setDatafieldlabel(str2);
/*  414 */         eSBReturnRuleExpressionBean.setHtmltype(str3);
/*  415 */         eSBReturnRuleExpressionBean.setFieldtype(str4);
/*  416 */         eSBReturnRuleExpressionBean.setDbtype(str5);
/*  417 */         eSBReturnRuleExpressionBean.setTypehrm(j);
/*  418 */         eSBReturnRuleExpressionBean.setCompareoption1(k);
/*  419 */         eSBReturnRuleExpressionBean.setCompareoption2(m);
/*  420 */         eSBReturnRuleExpressionBean.setBrowservalue(str6);
/*  421 */         eSBReturnRuleExpressionBean.setBrowserspantext(str7);
/*  422 */         eSBReturnRuleExpressionBean.setBrowserspanlabel(str8);
/*  423 */         eSBReturnRuleExpressionBean.setTextvalue1(str9);
/*  424 */         eSBReturnRuleExpressionBean.setElementtext1(str9);
/*  425 */         eSBReturnRuleExpressionBean.setSelectvalue1(n);
/*  426 */         eSBReturnRuleExpressionBean.setCheckboxvalue1(str10);
/*  427 */         eSBReturnRuleExpressionBean.setTextvalue2(str11);
/*  428 */         eSBReturnRuleExpressionBean.setParamtype(Util.getIntValue(str12, -1));
/*  429 */         eSBReturnRuleExpressionBean.setValuetype(Util.getIntValue(str13, -1));
/*  430 */         eSBReturnRuleExpressionBean.setValuevarid(Util.getIntValue(str14, -1));
/*  431 */         eSBReturnRuleExpressionBean.setRedius(str15);
/*  432 */         eSBReturnRuleExpressionBean.setNodeId(str16);
/*  433 */         eSBReturnRuleExpressionBean.setMeetCondition(str17);
/*  434 */         eSBReturnRuleExpressionBean.setJingdu(str18);
/*  435 */         eSBReturnRuleExpressionBean.setWeidu(str19);
/*  436 */         if (str3.equals("9")) {
/*  437 */           eSBReturnRuleExpressionBean.setElementvalue1(str7);
/*      */         }
/*  439 */         eSBReturnRuleExpressions.getChildrens().add(eSBReturnRuleExpressionBean);
/*      */       } 
/*      */     } 
/*  442 */     return eSBReturnRuleExpressions;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String persistenceRule2db(String paramString, ESBReturnRuleBean paramESBReturnRuleBean) throws Exception {
/*  453 */     setId(paramESBReturnRuleBean.getId());
/*  454 */     RecordSet recordSet = new RecordSet();
/*      */     
/*  456 */     String str1 = "delete from esbreturnrule where id = " + this.ruleid;
/*  457 */     recordSet.executeSql(str1);
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  463 */     newRule(paramESBReturnRuleBean, recordSet);
/*  464 */     int i = getruleid(recordSet);
/*  465 */     if (i > 0) {
/*  466 */       setId(i);
/*      */     } else {
/*  468 */       return "-1";
/*      */     } 
/*  470 */     paramString = paramString.replace("&", "&amp;");
/*  471 */     ESBReturnRuleExpressions eSBReturnRuleExpressions = stream2Exps(new ByteArrayInputStream(paramString.getBytes("UTF-8")));
/*  472 */     String str2 = "delete from esbreturnrule_expressions where ruleid=" + this.ruleid;
/*  473 */     recordSet.executeSql(str2);
/*  474 */     str2 = "delete from esbreturnrule_expressionbase where ruleid=" + this.ruleid;
/*  475 */     recordSet.executeSql(str2);
/*  476 */     str2 = "delete from esbreturnrule_variablebase where ruleid=" + this.ruleid;
/*  477 */     recordSet.executeSql(str2);
/*      */     try {
/*  479 */       eSBReturnRuleExpressions.persistence2db(recordSet, paramESBReturnRuleBean.getRulesrc());
/*  480 */     } catch (Exception exception) {
/*  481 */       exception.printStackTrace();
/*  482 */       return "-1";
/*      */     } 
/*      */     
/*  485 */     return getId() + "";
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static synchronized int getruleid(RecordSet paramRecordSet) throws Exception {
/*  495 */     int i = 0;
/*      */     
/*  497 */     paramRecordSet.executeSql("select max(id) as id from esbreturnrule");
/*  498 */     if (paramRecordSet.next()) {
/*  499 */       i = Util.getIntValue(paramRecordSet.getString("id"), 0);
/*      */     }
/*  501 */     return i;
/*      */   }
/*      */   
/*      */   public String getRuleName() {
/*  505 */     RecordSet recordSet = new RecordSet();
/*  506 */     recordSet.executeSql("select rulename from esbreturnrule where id=" + this.ruleid);
/*  507 */     if (recordSet.next()) {
/*  508 */       return Util.null2String(recordSet.getString("rulename"));
/*      */     }
/*  510 */     return null;
/*      */   }
/*      */ 
/*      */   
/*      */   public String getCondit() {
/*  515 */     RecordSet recordSet = new RecordSet();
/*  516 */     recordSet.executeSql("select condit from esbreturnrule where id=" + this.ruleid);
/*  517 */     if (recordSet.next()) {
/*  518 */       return Util.null2String(recordSet.getString("condit"));
/*      */     }
/*  520 */     return null;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static ESBReturnRuleExpressions getExpressionsByRuleid(int paramInt) {
/*  529 */     RecordSet recordSet = new RecordSet();
/*  530 */     recordSet.executeSql("select id from esbreturnrule_expressions where ruleid=" + paramInt + " ORDER BY id desc");
/*  531 */     if (recordSet.next()) {
/*  532 */       return getExpressions(Util.getIntValue(recordSet.getString("id")));
/*      */     }
/*  534 */     return null;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static ESBReturnRuleExpressions getExpressionsByAll(int paramInt) {
/*  543 */     RecordSet recordSet = new RecordSet();
/*  544 */     recordSet.executeSql("select id from esbreturnrule_expressions where ruleid=" + paramInt + " ORDER BY id desc");
/*  545 */     if (recordSet.next()) {
/*  546 */       return getExpressions(Util.getIntValue(recordSet.getString("id")), paramInt);
/*      */     }
/*  548 */     return null;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private static ESBReturnRuleExpressions getExpressions(int paramInt1, int paramInt2) {
/*  559 */     ESBReturnRuleExpressions eSBReturnRuleExpressions = ESBReturnRuleExpressions.getExpressions(paramInt1);
/*  560 */     if (eSBReturnRuleExpressions != null)
/*      */     {
/*  562 */       if (!"".equals(eSBReturnRuleExpressions.getExpids())) {
/*  563 */         ArrayList<String> arrayList = Util.TokenizerString(eSBReturnRuleExpressions.getExpids(), ",");
/*  564 */         for (byte b = 0; b < arrayList.size(); b++) {
/*  565 */           eSBReturnRuleExpressions.getChildrens().add(getExpressions(Integer.parseInt(arrayList.get(b)), paramInt2));
/*      */         }
/*      */       } else {
/*  568 */         ESBReturnRuleExpressionBean eSBReturnRuleExpressionBean = ESBReturnRuleExpressionBean.getExpressionBean(eSBReturnRuleExpressions.getExpbaseid());
/*  569 */         eSBReturnRuleExpressionBean.setRuleid(paramInt2);
/*  570 */         eSBReturnRuleExpressions.getChildrens().add(eSBReturnRuleExpressionBean);
/*      */       } 
/*      */     }
/*  573 */     return eSBReturnRuleExpressions;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private static ESBReturnRuleExpressions getExpressions(int paramInt) {
/*  583 */     ESBReturnRuleExpressions eSBReturnRuleExpressions = ESBReturnRuleExpressions.getExpressions(paramInt);
/*  584 */     if (eSBReturnRuleExpressions != null)
/*      */     {
/*  586 */       if (!"".equals(eSBReturnRuleExpressions.getExpids())) {
/*  587 */         ArrayList<String> arrayList = Util.TokenizerString(eSBReturnRuleExpressions.getExpids(), ",");
/*  588 */         for (byte b = 0; b < arrayList.size(); b++) {
/*  589 */           eSBReturnRuleExpressions.getChildrens().add(getExpressions(Integer.parseInt(arrayList.get(b))));
/*      */         }
/*      */       } else {
/*  592 */         ESBReturnRuleExpressionBean eSBReturnRuleExpressionBean = ESBReturnRuleExpressionBean.getExpressionBean(eSBReturnRuleExpressions.getExpbaseid());
/*  593 */         eSBReturnRuleExpressions.getChildrens().add(eSBReturnRuleExpressionBean);
/*      */       } 
/*      */     }
/*  596 */     return eSBReturnRuleExpressions;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String ruleToString() {
/*  604 */     if (this.expressions == null) {
/*  605 */       this.expressions = getExpressionsByRuleid(this.ruleid);
/*      */     }
/*  607 */     if (this.expressions != null) {
/*  608 */       return this.expressions.toString();
/*      */     }
/*  610 */     return "";
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String ruleToString(String paramString) {
/*  620 */     if (this.expressions == null) {
/*  621 */       this.expressions = getExpressionsByRuleid(Integer.parseInt(paramString));
/*      */     }
/*  623 */     if (this.expressions != null) {
/*  624 */       return this.expressions.toString();
/*      */     }
/*  626 */     return "";
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static String[] getRuleHtmlByRuleId(int paramInt, User paramUser) {
/*  636 */     RecordSet recordSet = new RecordSet();
/*  637 */     recordSet.executeSql("select id from esbreturnrule_expressions where ruleid=" + paramInt + " ORDER BY id desc");
/*  638 */     if (recordSet.next()) {
/*  639 */       expIndex = 1;
/*  640 */       rlindex = 1;
/*  641 */       return getRuleHtmlByExpID(Util.getIntValue(recordSet.getString("id")), paramUser, true);
/*      */     } 
/*  643 */     return new String[] { "", "" };
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private static String[] getRuleHtmlByExpID(int paramInt, User paramUser, boolean paramBoolean) {
/*  655 */     StringBuffer stringBuffer1 = new StringBuffer();
/*      */     
/*  657 */     StringBuffer stringBuffer2 = new StringBuffer();
/*      */     
/*  659 */     ESBReturnRuleExpressions eSBReturnRuleExpressions = ESBReturnRuleExpressions.getExpressions(paramInt);
/*  660 */     if (eSBReturnRuleExpressions != null)
/*      */     {
/*  662 */       if (!"".equals(eSBReturnRuleExpressions.getExpids())) {
/*  663 */         int i = getRlindex();
/*  664 */         if (paramBoolean) {
/*  665 */           stringBuffer1.append("<div id=\"mainBlock\" class=\"relationblock\" key='" + i + "'>");
/*  666 */           stringBuffer1.append("<div class=\"verticalblock\" onmouseover=\"relatmouseover(this)\" title=\"" + SystemEnv.getHtmlLabelName(83945, Util.getIntValue("" + paramUser.getLanguage(), 7)) + "\" onmouseout=\"relatmouseout(this)\" ondblclick=\"switchRelationEditMode(event,this);\">" + ((eSBReturnRuleExpressions.getRelation() == 0) ? "&nbsp;OR&nbsp;" : "&nbsp;AND&nbsp;") + "</div><div class=\"relationStyle outermoststyle\" ><div class=\"relationStyleTop\"></div><div class=\"relationStyleCenter\"></div><div class=\"relationStyleBottom\"></div></div>");
/*      */           
/*  668 */           stringBuffer2.append("<expressions id=\"mainExpression\" type='expressions' relation='" + eSBReturnRuleExpressions.getRelation() + "' rotindex='" + i + "'>");
/*      */         } else {
/*  670 */           stringBuffer1.append("<div class=\"relationblock displayspan\" onclick=\"switchSelected(event, this)\" key='" + i + "'>");
/*  671 */           stringBuffer1.append("<div class=\"verticalblock\" title=\"" + SystemEnv.getHtmlLabelName(83945, Util.getIntValue("" + paramUser.getLanguage(), 7)) + "\" onmouseover=\"relatmouseover(this)\" onmouseout=\"relatmouseout(this)\" ondblclick=\"switchRelationEditMode(event,this);\">" + ((eSBReturnRuleExpressions.getRelation() == 0) ? "&nbsp;OR&nbsp;" : "&nbsp;AND&nbsp;") + "</div><div class=\"relationStyle\" ><div class=\"relationStyleTop\"></div><div class=\"relationStyleCenter\"></div><div class=\"relationStyleBottom\"></div></div>");
/*      */           
/*  673 */           stringBuffer2.append("<expressions type='expressions' relation='" + eSBReturnRuleExpressions.getRelation() + "' rotindex='" + i + "'>");
/*      */         } 
/*      */         
/*  676 */         ArrayList<String> arrayList = Util.TokenizerString(eSBReturnRuleExpressions.getExpids(), ",");
/*  677 */         for (byte b = 0; b < arrayList.size(); b++) {
/*  678 */           String[] arrayOfString = getRuleHtmlByExpID(Integer.parseInt(arrayList.get(b)), paramUser, false);
/*  679 */           stringBuffer1.append("<div class=\"relationItem\">");
/*  680 */           stringBuffer1.append(arrayOfString[0]);
/*  681 */           stringBuffer1.append("</div>");
/*      */           
/*  683 */           stringBuffer2.append(arrayOfString[1]);
/*      */         } 
/*  685 */         stringBuffer1.append("</div>");
/*  686 */         stringBuffer2.append("</expressions>");
/*      */       } else {
/*  688 */         ESBReturnRuleExpressionBean eSBReturnRuleExpressionBean = ESBReturnRuleExpressionBean.getExpressionBean(eSBReturnRuleExpressions.getExpbaseid());
/*  689 */         if (eSBReturnRuleExpressionBean != null) {
/*  690 */           String str = "";
/*  691 */           str = getExpStr(eSBReturnRuleExpressionBean, paramUser, "hidden");
/*      */           
/*  693 */           if (str.length() > 0) {
/*      */             
/*  695 */             String str1 = "";
/*  696 */             str1 = getExpStr(eSBReturnRuleExpressionBean, paramUser, "view");
/*  697 */             int i = getExpIndex();
/*  698 */             String str2 = "<input name='child' type='hidden' value='" + str + "' expindex='" + i + "'>";
/*  699 */             String str3 = "<span class='displayspan' key='" + i + "' onclick='switchSelected(event, this)' ondblclick='switchEditMode(this);'>" + str1.replace("\\\\", "\\") + "</span>";
/*  700 */             stringBuffer1.append(str3);
/*  701 */             stringBuffer2.append(str2);
/*      */           } 
/*      */         } 
/*      */       } 
/*      */     }
/*  706 */     return new String[] { stringBuffer1.toString(), stringBuffer2.toString() };
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static String getExpStr(ESBReturnRuleExpressionBean paramESBReturnRuleExpressionBean, User paramUser, String paramString) {
/*  717 */     String str = "";
/*  718 */     if (paramESBReturnRuleExpressionBean != null) {
/*      */       
/*  720 */       String str1 = paramESBReturnRuleExpressionBean.getHtmltype();
/*  721 */       String str2 = paramESBReturnRuleExpressionBean.getFieldtype();
/*  722 */       String str3 = "";
/*  723 */       String str4 = "";
/*  724 */       String str5 = "";
/*  725 */       String str6 = "";
/*  726 */       String str7 = "";
/*  727 */       String str8 = "";
/*  728 */       String str9 = "";
/*  729 */       String str10 = "";
/*  730 */       String str11 = "";
/*  731 */       String str12 = "";
/*      */       
/*  733 */       if (paramString.equals("hidden")) {
/*      */         
/*  735 */         str = "{";
/*  736 */         str = str + "\"datafield\":\"" + paramESBReturnRuleExpressionBean.getDatafield() + "\",";
/*  737 */         str = str + "\"datafieldlabel\":\"" + paramESBReturnRuleExpressionBean.getDatafieldlabel() + "\",";
/*  738 */         str = str + "\"type4hrm\":\"" + paramESBReturnRuleExpressionBean.getTypehrm() + "\",";
/*  739 */         str = str + "\"htmltype\":\"" + paramESBReturnRuleExpressionBean.getHtmltype() + "\",";
/*  740 */         str = str + "\"fieldtype\":\"" + paramESBReturnRuleExpressionBean.getFieldtype() + "\",";
/*  741 */         str = str + "\"dbtype\":\"" + paramESBReturnRuleExpressionBean.getDbtype() + "\",";
/*  742 */         str = str + "\"paramtype\":\"" + paramESBReturnRuleExpressionBean.getParamtype() + "\",";
/*  743 */         str = str + "\"valuetype\":\"" + paramESBReturnRuleExpressionBean.getValuetype() + "\",";
/*  744 */         str = str + "\"jingdu\":\"" + paramESBReturnRuleExpressionBean.getJingdu() + "\",";
/*  745 */         str = str + "\"weidu\":\"" + paramESBReturnRuleExpressionBean.getWeidu() + "\",";
/*  746 */         str = str + "\"redius\":\"" + paramESBReturnRuleExpressionBean.getRedius() + "\",";
/*  747 */         str = str + "\"meetCondition\":\"" + paramESBReturnRuleExpressionBean.getMeetCondition() + "\",";
/*  748 */         str = str + "\"nodeId\":\"" + paramESBReturnRuleExpressionBean.getNodeId() + "\",";
/*  749 */         if (paramESBReturnRuleExpressionBean.getValuetype() == 3) {
/*  750 */           str = str + "\"valuevarid\":\"" + paramESBReturnRuleExpressionBean.getElementvalue1() + "\",";
/*      */         } else {
/*  752 */           str = str + "\"valuevarid\":\"-1\",";
/*  753 */         }  str = str + "\"compareoption1\":\"" + paramESBReturnRuleExpressionBean.getCompareoption1() + "\",";
/*  754 */         str = str + "\"compareoptionlabel1\":\"" + getCompareLabel(paramESBReturnRuleExpressionBean.getCompareoption1(), paramUser.getLanguage()) + "\",";
/*  755 */         str = str + "\"compareoption2\":\"" + paramESBReturnRuleExpressionBean.getCompareoption2() + "\",";
/*  756 */         str = str + "\"compareoptionlabel2\":\"" + getCompareLabel(paramESBReturnRuleExpressionBean.getCompareoption2(), paramUser.getLanguage()) + "\",";
/*      */       } 
/*      */       
/*  759 */       str3 = paramESBReturnRuleExpressionBean.getElementvalue1();
/*  760 */       str4 = paramESBReturnRuleExpressionBean.getElementtext1();
/*  761 */       if (str4.length() > 0)
/*      */       {
/*  763 */         if (str4.indexOf("__") >= 0) {
/*      */ 
/*      */           
/*  766 */           String[] arrayOfString = str4.split("__");
/*  767 */           for (byte b = 0; b < arrayOfString.length; b++)
/*  768 */             str5 = str5 + arrayOfString[b] + ","; 
/*  769 */           str5 = str5.substring(0, str5.lastIndexOf(","));
/*      */         } else {
/*      */           
/*  772 */           str5 = str4;
/*      */         }  } 
/*  774 */       if (paramString.equals("view")) {
/*  775 */         str = paramESBReturnRuleExpressionBean.getDatafieldlabel() + " " + getCompareLabel(paramESBReturnRuleExpressionBean.getCompareoption1(), paramUser.getLanguage()) + " '" + str5 + "'";
/*      */       }
/*  777 */       if (paramString.equals("hidden")) {
/*  778 */         str = str + "\"browservalue\":\"" + str3 + "\",";
/*  779 */         str = str + "\"browserspantext\":\"" + str4 + "\",";
/*  780 */         str = str + "\"browserspanlabel\":\"" + str5 + "\",";
/*  781 */         str = str + "\"textvalue1\":\"" + str4 + "\",";
/*  782 */         str = str + "\"selectvalue1\":\"" + str7 + "\",";
/*  783 */         str = str + "\"selecttext1\":\"" + str12 + "\",";
/*  784 */         str = str + "\"selectlabel1\":\"" + str8 + "\",";
/*  785 */         if (str1.equals("5") && str2.equals("2"))
/*  786 */           str = str + "\"selectval\":" + str11 + ","; 
/*  787 */         str = str + "\"checkboxvalue1\":\"" + str9 + "\",";
/*  788 */         str = str + "\"textvalue2\":\"" + str10 + "\"";
/*  789 */         str = str + "}";
/*      */       } 
/*      */     } 
/*  792 */     return str;
/*      */   }
/*      */   
/*      */   public static String getNodeNameById(String paramString) {
/*  796 */     String str = "";
/*  797 */     if (paramString != null && !paramString.equals("")) {
/*  798 */       RecordSet recordSet = new RecordSet();
/*  799 */       String str1 = "select nodename from workflow_nodebase where id=" + paramString;
/*  800 */       recordSet.executeSql(str1);
/*  801 */       if (recordSet.next()) {
/*  802 */         str = Util.null2String(recordSet.getString("nodename"));
/*      */       }
/*      */     } 
/*  805 */     return str;
/*      */   }
/*      */   
/*      */   public static String getMeetConditionLabel(String paramString, int paramInt) {
/*  809 */     String str = "";
/*  810 */     if (paramString != null && !paramString.equals("")) {
/*  811 */       switch (Integer.parseInt(paramString)) {
/*      */         case 1:
/*  813 */           str = SystemEnv.getHtmlLabelName(125878, paramInt);
/*      */           break;
/*      */         case 2:
/*  816 */           str = SystemEnv.getHtmlLabelName(125879, paramInt);
/*      */           break;
/*      */       } 
/*      */     }
/*  820 */     return str;
/*      */   }
/*      */   public static String getRediusLabel(String paramString, int paramInt) {
/*  823 */     String str = "";
/*  824 */     if (paramString != null && !paramString.equals("")) {
/*  825 */       switch (Integer.parseInt(paramString)) {
/*      */         case 1:
/*  827 */           str = "50" + SystemEnv.getHtmlLabelName(125675, paramInt);
/*      */           break;
/*      */         case 2:
/*  830 */           str = "300" + SystemEnv.getHtmlLabelName(125675, paramInt);
/*      */           break;
/*      */         case 3:
/*  833 */           str = "500" + SystemEnv.getHtmlLabelName(125675, paramInt);
/*      */           break;
/*      */         case 4:
/*  836 */           str = "1000" + SystemEnv.getHtmlLabelName(125675, paramInt);
/*      */           break;
/*      */       } 
/*      */     }
/*  840 */     return str;
/*      */   }
/*      */ 
/*      */   
/*      */   public static String getCompareLabel(int paramInt1, int paramInt2) {
/*  845 */     String str = "";
/*  846 */     if (paramInt1 > 0)
/*      */     {
/*  848 */       switch (paramInt1) {
/*      */         
/*      */         case 1:
/*  851 */           str = SystemEnv.getHtmlLabelName(15508, paramInt2);
/*      */           break;
/*      */         case 2:
/*  854 */           str = SystemEnv.getHtmlLabelName(325, paramInt2);
/*      */           break;
/*      */         case 3:
/*  857 */           str = SystemEnv.getHtmlLabelName(15509, paramInt2);
/*      */           break;
/*      */         case 4:
/*  860 */           str = SystemEnv.getHtmlLabelName(326, paramInt2);
/*      */           break;
/*      */         case 5:
/*  863 */           str = SystemEnv.getHtmlLabelName(327, paramInt2);
/*      */           break;
/*      */         case 6:
/*  866 */           str = SystemEnv.getHtmlLabelName(15506, paramInt2);
/*      */           break;
/*      */         case 7:
/*  869 */           str = SystemEnv.getHtmlLabelName(353, paramInt2);
/*      */           break;
/*      */         case 8:
/*  872 */           str = SystemEnv.getHtmlLabelName(21473, paramInt2);
/*      */           break;
/*      */         case 9:
/*  875 */           str = SystemEnv.getHtmlLabelName(346, paramInt2);
/*      */           break;
/*      */         case 10:
/*  878 */           str = SystemEnv.getHtmlLabelName(15507, paramInt2);
/*      */           break;
/*      */         case 11:
/*  881 */           str = SystemEnv.getHtmlLabelName(82763, paramInt2);
/*      */           break;
/*      */         case 12:
/*  884 */           str = SystemEnv.getHtmlLabelName(82764, paramInt2);
/*      */           break;
/*      */       } 
/*      */     
/*      */     }
/*  889 */     return str;
/*      */   }
/*      */   
/*      */   public List<Map<String, String>> getAllVar() {
/*  893 */     return getAllVarByRule(this.ruleid);
/*      */   }
/*      */   
/*      */   public static List<Map<String, String>> getAllVarByRule(int paramInt) {
/*  897 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*      */     
/*  899 */     RecordSet recordSet = new RecordSet();
/*  900 */     recordSet.executeSql("select rv.id, rv.name, rv.fieldtype, rv.htmltype from rule_variablebase rv  where rv.ruleid=" + paramInt);
/*      */     
/*  902 */     while (recordSet.next()) {
/*  903 */       HashMap<Object, Object> hashMap = new HashMap<>();
/*  904 */       hashMap.put("id", recordSet.getString("id"));
/*  905 */       hashMap.put("name", recordSet.getString("name"));
/*  906 */       hashMap.put("fieldtype", recordSet.getString("fieldtype"));
/*  907 */       hashMap.put("htmltype", recordSet.getString("htmltype"));
/*  908 */       arrayList.add(hashMap);
/*      */     } 
/*      */     
/*  911 */     return (List)arrayList;
/*      */   }
/*      */   
/*      */   public List<Map<String, String>> getFormMappingList(int paramInt1, int paramInt2, int paramInt3, int paramInt4) {
/*  915 */     return getFormMappingList(paramInt1, paramInt2, paramInt3, paramInt4, 0, 0);
/*      */   }
/*      */   
/*      */   public List<Map<String, String>> getFormMappingList(int paramInt1, int paramInt2, int paramInt3, int paramInt4, int paramInt5, int paramInt6) {
/*  919 */     return getFormMappingList(paramInt1, paramInt2, paramInt3, paramInt4, paramInt5, paramInt6, 0);
/*      */   }
/*      */   
/*      */   public List<Map<String, String>> getFormMappingList(int paramInt1, int paramInt2, int paramInt3, int paramInt4, int paramInt5, int paramInt6, int paramInt7) {
/*  923 */     if (this.formlist == null) {
/*  924 */       this.formlist = new ArrayList<>();
/*  925 */       UrlComInfo urlComInfo = new UrlComInfo();
/*  926 */       RecordSet recordSet = new RecordSet();
/*  927 */       String str1 = "";
/*      */       
/*  929 */       if (paramInt2 == 0) {
/*  930 */         str1 = " select a.fieldid as id,fieldname as name,c.fieldlable as label,b.fieldhtmltype as htmltype,b.type as type,b.fielddbtype as dbtype, fieldorder, '0' as isdetail  from workflow_formfield a,workflow_formdict b,workflow_fieldlable c  where c.formid = a.formid and c.isdefault = '1' and b.fieldhtmltype!=6 and c.fieldid =a.fieldid and b.id = a.fieldid and (a.isdetail<>'1' or a.isdetail is null) and a.formid=" + paramInt1 + " ";
/*      */       
/*      */       }
/*  933 */       else if (paramInt2 == 1) {
/*  934 */         str1 = " select id as id,fieldname as name,fieldlabel as label,fieldhtmltype as htmltype,type as type, fielddbtype as dbtype, dsporder as fieldorder, 0 as isdetail  from workflow_billfield  where (viewtype is null or viewtype!=1) and billid = " + paramInt1 + " and fieldhtmltype!=6 ";
/*      */       } 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*  940 */       String str2 = SystemEnv.getHtmlLabelName(19325, this.user.getLanguage()) + (paramInt7 + 1);
/*  941 */       if (paramInt7 > 0) {
/*      */         
/*  943 */         paramInt7 = getDetailTableId(paramInt7, 1);
/*      */         
/*  945 */         StringBuffer stringBuffer = new StringBuffer();
/*  946 */         if (paramInt2 == 1) {
/*  947 */           String str = "";
/*  948 */           if (paramInt2 == 1) {
/*  949 */             recordSet.executeSql("select tablename, orderid from workflow_billdetailtable where id=" + paramInt7);
/*  950 */             if (recordSet.next()) {
/*  951 */               str = Util.null2String(recordSet.getString("tablename"));
/*  952 */               str2 = SystemEnv.getHtmlLabelName(19325, this.user.getLanguage()) + Util.getIntValue(recordSet.getString("orderid"), 1);
/*      */             } 
/*      */           } 
/*      */           
/*  956 */           stringBuffer.append(" select id as id,fieldname as name,fieldlabel as label,fieldhtmltype as htmltype,type as type, fielddbtype as dbtype ");
/*  957 */           stringBuffer.append(" , dsporder as fieldorder, viewtype as isdetail");
/*  958 */           stringBuffer.append(" from workflow_billfield ");
/*  959 */           stringBuffer.append(" where billid=").append(paramInt1);
/*  960 */           stringBuffer.append(" and fieldhtmltype!=6 and detailtable='").append(str).append("'");
/*      */         } else {
/*      */           
/*  963 */           stringBuffer.append(" select a.fieldid as id, ");
/*  964 */           stringBuffer.append("       fieldname as name, ");
/*  965 */           stringBuffer.append("       c.fieldlable as label, ");
/*  966 */           stringBuffer.append("       b.fieldhtmltype as htmltype, ");
/*  967 */           stringBuffer.append("       b.type as type, ");
/*  968 */           stringBuffer.append("       b.fielddbtype as dbtype, ");
/*  969 */           stringBuffer.append("       fieldorder, ");
/*  970 */           stringBuffer.append("       isdetail ");
/*  971 */           stringBuffer.append(" from workflow_formfield a, workflow_formdictdetail b, workflow_fieldlable c ");
/*  972 */           stringBuffer.append(" where  c.formid = a.formid ");
/*  973 */           stringBuffer.append("       and c.isdefault = '1' ");
/*  974 */           stringBuffer.append("       and b.fieldhtmltype != 6 ");
/*  975 */           stringBuffer.append("       and c.fieldid = a.fieldid ");
/*  976 */           stringBuffer.append("       and b.id = a.fieldid ");
/*  977 */           stringBuffer.append("       and a.isdetail = '1' ");
/*  978 */           stringBuffer.append("       and a.formid=").append(paramInt1);
/*  979 */           stringBuffer.append("       and a.groupid = ").append(paramInt7);
/*      */         } 
/*      */         
/*  982 */         str1 = "(" + str1 + ") union all (" + stringBuffer.toString() + ")";
/*      */         
/*  984 */         str2 = str2 + ".";
/*      */       } 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*  996 */       str1 = str1 + " order by isdetail, fieldorder ";
/*  997 */       recordSet.executeSql(str1);
/*  998 */       while (recordSet.next()) {
/*  999 */         String str3 = recordSet.getString("id");
/* 1000 */         String str4 = recordSet.getString("htmltype");
/* 1001 */         String str5 = recordSet.getString("type");
/* 1002 */         String str6 = recordSet.getString("dbtype");
/* 1003 */         String str7 = recordSet.getString("name");
/* 1004 */         String str8 = recordSet.getString("label");
/* 1005 */         String str9 = Util.null2String(recordSet.getString("isdetail"));
/*      */         
/* 1007 */         if (paramInt2 == 1) {
/* 1008 */           str8 = SystemEnv.getHtmlLabelName(Util.getIntValue(str8), this.user.getLanguage());
/*      */         }
/* 1010 */         str8 = Util.toScreen(str8, this.user.getLanguage());
/*      */         
/* 1012 */         if ("1".equals(str9)) {
/* 1013 */           str8 = str2 + str8;
/*      */         }
/*      */         
/* 1016 */         if ((str4.equals("3") && (urlComInfo.getUrlbrowserurl(str5).equals("") || str5.equals("141"))) || 
/* 1017 */           str4.equals("7") || (str4.equals("2") && str5.equals("2")))
/*      */           continue; 
/* 1019 */         HashMap<Object, Object> hashMap = new HashMap<>();
/* 1020 */         hashMap.put("id", str3);
/* 1021 */         hashMap.put("htmltype", str4);
/* 1022 */         hashMap.put("type", str5);
/* 1023 */         hashMap.put("dbtype", str6);
/* 1024 */         hashMap.put("name", str7);
/* 1025 */         hashMap.put("label", str8);
/* 1026 */         hashMap.put("isdetail", str9);
/*      */         
/* 1028 */         this.formlist.add(hashMap);
/*      */       } 
/*      */     } 
/*      */     
/* 1032 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*      */     
/* 1034 */     Iterator<Map<String, String>> iterator = this.formlist.iterator();
/* 1035 */     while (iterator.hasNext()) {
/* 1036 */       Map<?, ?> map = iterator.next();
/* 1037 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 1038 */       hashMap.putAll(map);
/*      */       
/* 1040 */       String str1 = (String)hashMap.get("htmltype");
/* 1041 */       String str2 = (String)hashMap.get("type");
/*      */       
/* 1043 */       if (paramInt3 == 1 && paramInt4 == 1) {
/* 1044 */         if ((str1.equals("1") && str2.equals("1")) || str1.equals("2"))
/* 1045 */           arrayList.add(hashMap); 
/*      */         continue;
/*      */       } 
/* 1048 */       if (paramInt3 == 1 && (paramInt4 == 2 || paramInt4 == 3)) {
/* 1049 */         if (str1.equals("1") && str2.equals(paramInt4 + "")) {
/* 1050 */           arrayList.add(hashMap);
/*      */         }
/*      */         
/* 1053 */         if (paramInt3 == 1 && paramInt4 == 2 && str1.equals("3") && str2.equals("1")) {
/* 1054 */           hashMap.put("label", (String)hashMap.get("label") + "(" + SystemEnv.getHtmlLabelName(683, this.user.getLanguage()) + ")");
/* 1055 */           arrayList.add(hashMap);
/*      */         } 
/*      */         continue;
/*      */       } 
/* 1059 */       if (paramInt3 == 3) {
/*      */ 
/*      */         
/* 1062 */         if (str1.equals("3") && str2.equals(String.valueOf(paramInt4)))
/* 1063 */           arrayList.add(hashMap);  continue;
/*      */       } 
/* 1065 */       if (paramInt3 == 9) {
/* 1066 */         if (str1.equals("9"))
/* 1067 */           arrayList.add(hashMap); 
/*      */         continue;
/*      */       } 
/* 1070 */       if (paramInt5 == 1 && paramInt6 == 2 && str1.equals("3") && str2.equals("1")) {
/* 1071 */         hashMap.put("label", (String)hashMap.get("label") + "(" + SystemEnv.getHtmlLabelName(683, this.user.getLanguage()) + ")");
/*      */       }
/* 1073 */       arrayList.add(hashMap);
/*      */     } 
/*      */ 
/*      */     
/* 1077 */     return (List)arrayList;
/*      */   }
/*      */   
/*      */   public List<String> getRuleMappingOperate(String paramString) {
/* 1081 */     ArrayList<String> arrayList = new ArrayList();
/* 1082 */     arrayList.add("true");
/* 1083 */     return arrayList;
/*      */   }
/*      */   public String getMapResultCheckBox(String paramString) {
/* 1086 */     return "true";
/*      */   }
/*      */   
/*      */   public List<String> getNewsOperate(String paramString1, String paramString2) {
/* 1090 */     ArrayList<String> arrayList = new ArrayList();
/* 1091 */     if (paramString2.equals("1") || paramString2.equals("2") || paramString2.equals("4") || paramString2.equals("6") || paramString2.equals("7") || paramString2.equals("8")) {
/*      */       
/* 1093 */       arrayList.add("false");
/* 1094 */       arrayList.add("true");
/* 1095 */       arrayList.add("false");
/* 1096 */       arrayList.add("false");
/*      */     } else {
/*      */       
/* 1099 */       arrayList.add("true");
/* 1100 */       arrayList.add("true");
/* 1101 */       String str = getRuleCheckbox(paramString1 + "+" + paramString2);
/* 1102 */       arrayList.add(str);
/* 1103 */       arrayList.add(str.equals("true") ? "false" : "true");
/*      */     } 
/*      */     
/* 1106 */     return arrayList;
/*      */   }
/*      */   
/*      */   public String getRuleCheckbox(String paramString) {
/* 1110 */     String[] arrayOfString = Util.TokenizerString2(paramString, "+");
/* 1111 */     String str1 = Util.null2String(arrayOfString[1]);
/* 1112 */     String str2 = Util.null2String(arrayOfString[0]);
/* 1113 */     if (str1.equals("1") || str1.equals("2") || str1.equals("4") || str1.equals("6") || str1.equals("7") || str1.equals("8"))
/*      */     {
/* 1115 */       return "false";
/*      */     }
/*      */     
/* 1118 */     RecordSet recordSet1 = new RecordSet();
/* 1119 */     RecordSet recordSet2 = new RecordSet();
/* 1120 */     recordSet1.executeSql("select wfid from esbreturnrule_maplist where ruleid=" + str2);
/* 1121 */     int i = 0;
/* 1122 */     int j = 0;
/* 1123 */     while (recordSet1.next()) {
/* 1124 */       i = Util.getIntValue(recordSet1.getString("wfid"));
/* 1125 */       if (i > 0) {
/* 1126 */         recordSet2.executeSql("select count(1) as count from workflow_base where id=" + i);
/* 1127 */         if (recordSet2.next()) {
/* 1128 */           j = Util.getIntValue(recordSet2.getString("count"));
/* 1129 */           if (j > 0) {
/* 1130 */             return "false";
/*      */           }
/*      */         } 
/*      */       } 
/*      */     } 
/* 1135 */     return "true";
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   public String getwfname(String paramString) {
/* 1141 */     String str1 = "";
/* 1142 */     RecordSet recordSet = new RecordSet();
/* 1143 */     String str2 = "select workflowname from workflow_base where id=" + paramString;
/* 1144 */     recordSet.executeSql(str2);
/* 1145 */     if (recordSet.next()) {
/* 1146 */       str1 = recordSet.getString("workflowname");
/*      */     }
/* 1148 */     return str1;
/*      */   }
/*      */ 
/*      */   
/*      */   public String getquoteposition(String paramString1, String paramString2) {
/* 1153 */     String[] arrayOfString = Util.TokenizerString2(paramString2, "+");
/* 1154 */     String str1 = Util.null2String(arrayOfString[0]);
/* 1155 */     String str2 = Util.null2String(arrayOfString[1]);
/* 1156 */     String str3 = Util.null2String(arrayOfString[2]);
/* 1157 */     int i = Util.getIntValue(arrayOfString[3], 7);
/* 1158 */     String str4 = "";
/* 1159 */     if (str3.equals("4")) {
/* 1160 */       str4 = SystemEnv.getHtmlLabelName(383589, i);
/* 1161 */     } else if (str3.equals("1")) {
/*      */       
/* 1163 */       RecordSet recordSet = new RecordSet();
/* 1164 */       recordSet.executeSql(" select linkname from workflow_nodelink where id=" + str1);
/* 1165 */       if (recordSet.next()) {
/* 1166 */         str4 = SystemEnv.getHtmlLabelName(15587, i) + ":" + recordSet.getString("linkname");
/*      */       }
/* 1168 */     } else if (str3.equals("2")) {
/*      */       
/* 1170 */       RecordSet recordSet = new RecordSet();
/* 1171 */       recordSet.executeSql(" select t4.nodename,t3.workflowid,t1.groupname from workflow_nodegroup t1,workflow_groupdetail t2,workflow_flownode t3,workflow_nodebase t4  where t1.id = t2.groupid and t1.nodeid=t3.nodeid and t3.nodeid=t4.id  and t2.id=" + str1);
/*      */ 
/*      */       
/* 1174 */       if (recordSet.next()) {
/* 1175 */         str4 = SystemEnv.getHtmlLabelName(15070, i) + ":" + recordSet.getString("nodename") + " " + SystemEnv.getHtmlLabelName(15545, i) + ":" + recordSet.getString("groupname");
/*      */       }
/* 1177 */     } else if (str3.equals("6")) {
/*      */       
/* 1179 */       str4 = SystemEnv.getHtmlLabelName(82586, i);
/*      */     }
/* 1181 */     else if (str3.equals("5")) {
/*      */       
/* 1183 */       str4 = SystemEnv.getHtmlLabelName(124849, i);
/* 1184 */     } else if (str3.equals("7") || str3.equals("8")) {
/* 1185 */       str4 = SystemEnv.getHtmlLabelName(126298, i);
/*      */     } 
/* 1187 */     return str4;
/*      */   }
/*      */ 
/*      */   
/*      */   public String getwftypename(String paramString) {
/* 1192 */     String str1 = "";
/* 1193 */     RecordSet recordSet = new RecordSet();
/* 1194 */     String str2 = "select t2.typename from workflow_base t1, workflow_type t2 where t1.workflowtype=t2.id and t1.id=" + paramString;
/* 1195 */     recordSet.executeSql(str2);
/* 1196 */     if (recordSet.next()) {
/* 1197 */       str1 = recordSet.getString("typename");
/*      */     }
/* 1199 */     return str1;
/*      */   }
/*      */   
/*      */   public static int getExpIndex() {
/* 1203 */     return expIndex++;
/*      */   }
/*      */   
/*      */   public static int getRlindex() {
/* 1207 */     return rlindex++;
/*      */   }
/*      */   
/*      */   public String getBelongName(String paramString1, String paramString2) {
/* 1211 */     if ("0".equals(paramString2)) {
/* 1212 */       String str = "select workflowname from workflow_base where id=(select workflowid from workflow_nodelink where id=" + paramString1 + ")";
/* 1213 */       RecordSet recordSet = new RecordSet();
/* 1214 */       recordSet.execute(str);
/* 1215 */       if (recordSet.next()) {
/* 1216 */         return recordSet.getString("workflowname");
/*      */       }
/*      */     } 
/* 1219 */     return "";
/*      */   }
/*      */   
/*      */   public String getBelongTypeName(String paramString) {
/* 1223 */     if ("0".equals(paramString)) {
/* 1224 */       return "" + SystemEnv.getHtmlLabelName(33413, ThreadVarLanguage.getLang()) + "";
/*      */     }
/* 1226 */     return "" + SystemEnv.getHtmlLabelName(125506, ThreadVarLanguage.getLang()) + "";
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static boolean deleteRuleMapping(int paramInt) {
/* 1236 */     RecordSet recordSet = new RecordSet();
/* 1237 */     int i = 0;
/* 1238 */     int j = 0;
/* 1239 */     int k = 0;
/* 1240 */     int m = 0;
/* 1241 */     recordSet.executeSql("SELECT * from esbreturnrule_maplist where id=" + paramInt);
/* 1242 */     if (recordSet.next()) {
/* 1243 */       i = Util.getIntValue(recordSet.getString("ruleid"));
/* 1244 */       j = Util.getIntValue(recordSet.getString("linkid"));
/* 1245 */       k = Util.getIntValue(recordSet.getString("rulesrc"));
/* 1246 */       m = Util.getIntValue(recordSet.getString("nm"));
/*      */     } 
/* 1248 */     if (i != 0 && j != 0 && k != 0) {
/* 1249 */       if (m == 1) {
/*      */         
/* 1251 */         recordSet.executeSql("delete from esbreturnrule_maplist where id=" + paramInt);
/* 1252 */         recordSet.executeSql("delete from esbreturnrule_mapitem where ruleid=" + i + " and linkid=" + j + " and rulesrc=" + k);
/* 1253 */         String str = "";
/* 1254 */         recordSet.executeSql("SELECT ruleid from esbreturnrule_maplist where linkid=" + j + " and rulesrc=" + k + " and rowidenty=0");
/* 1255 */         byte b = 1;
/* 1256 */         while (recordSet.next()) {
/*      */           
/* 1258 */           if (recordSet.getCounts() > b) {
/* 1259 */             str = str + recordSet.getString("ruleid") + ",";
/*      */           } else {
/* 1261 */             str = str + recordSet.getString("ruleid");
/* 1262 */           }  b++;
/*      */         } 
/* 1264 */         if (k == 1) {
/*      */           
/* 1266 */           recordSet.executeSql("update workflow_nodelink set newrule='" + str + "' where id=" + j);
/* 1267 */           recordSet.executeSql("select condition,newrule from workflow_nodelink where id=" + j);
/* 1268 */           if (recordSet.first()) {
/*      */             
/* 1270 */             String str1 = Util.null2String(recordSet.getString("condition"));
/* 1271 */             String str2 = Util.null2String(recordSet.getString("newrule"));
/* 1272 */             if (str1.equals("") && str2.equals("")) {
/* 1273 */               return false;
/*      */             }
/*      */           } 
/* 1276 */         } else if (k == 4) {
/* 1277 */           recordSet.executeSql("update workflow_urgerdetail set conditions='" + str + "', conditioncn='' where id=" + j);
/* 1278 */         } else if (k == 2) {
/* 1279 */           recordSet.executeSql("update workflow_groupdetail set conditions='" + str + "', conditioncn='' where id=" + j);
/* 1280 */         } else if (k == 7 || k == 8) {
/* 1281 */           Map<String, String> map = getRuleInfoByRIds(k, j + "");
/* 1282 */           String str1 = map.get("ruleids");
/* 1283 */           String str2 = map.get("ruledescs");
/*      */           
/* 1285 */           if (k == 7) {
/* 1286 */             recordSet.executeUpdate("update Workflow_SubwfSet set condition=?, conditioncn=? where id=" + j, new Object[] { str1, str2 });
/* 1287 */           } else if (k == 8) {
/* 1288 */             recordSet.executeUpdate("update Workflow_TriDiffWfDiffField set condition=?, conditioncn=? where id=" + j, new Object[] { str1, str2 });
/*      */           } 
/*      */         } 
/* 1291 */         if (str.equals(""))
/* 1292 */           return false; 
/*      */       } else {
/* 1294 */         deleteRule(i);
/*      */       } 
/*      */     }
/*      */     
/* 1298 */     return true;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static boolean deleteRuleOld(int paramInt) {
/* 1308 */     RecordSet recordSet = new RecordSet();
/*      */     
/* 1310 */     String str1 = "";
/* 1311 */     String str2 = "";
/* 1312 */     recordSet.executeSql("SELECT linkid,rulesrc from esbreturnrule where id=" + paramInt);
/* 1313 */     if (recordSet.next()) {
/*      */       
/* 1315 */       str1 = recordSet.getString("linkid");
/* 1316 */       str2 = recordSet.getString("rulesrc");
/*      */     } 
/* 1318 */     recordSet.executeSql("delete from esbreturnrule where id=" + paramInt);
/* 1319 */     recordSet.executeSql("delete from esbreturnrule_maplist where ruleid=" + paramInt);
/* 1320 */     if (str2.equals("-1")) {
/*      */       
/* 1322 */       recordSet.executeSql("update workflow_nodelink set condition='',conditioncn='' where id=" + str1);
/* 1323 */       recordSet.executeSql("select condition,newrule from workflow_nodelink where id=" + str1);
/* 1324 */       if (recordSet.first()) {
/*      */         
/* 1326 */         String str3 = Util.null2String(recordSet.getString("condition"));
/* 1327 */         String str4 = Util.null2String(recordSet.getString("newrule"));
/* 1328 */         if (str3.equals("") && str4.equals(""))
/* 1329 */           return false; 
/*      */       } 
/*      */     } 
/* 1332 */     return true;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static boolean deleteRule(int paramInt) {
/* 1341 */     RecordSet recordSet = new RecordSet();
/* 1342 */     recordSet.executeSql("delete from esbreturnrule_expressionbase where ruleid=" + paramInt);
/* 1343 */     recordSet.executeSql("delete from esbreturnrule_expressions where ruleid=" + paramInt);
/* 1344 */     recordSet.executeSql("delete from esbreturnrule where id=" + paramInt);
/* 1345 */     recordSet.executeSql("delete from esbreturnrule_variablebase where ruleid=" + paramInt);
/* 1346 */     String str1 = "";
/* 1347 */     String str2 = "";
/* 1348 */     String str3 = "";
/* 1349 */     recordSet.executeSql("SELECT linkid,rulesrc from esbreturnrule_maplist where ruleid=" + paramInt);
/* 1350 */     if (recordSet.next()) {
/*      */       
/* 1352 */       str2 = recordSet.getString("linkid");
/* 1353 */       str3 = recordSet.getString("rulesrc");
/*      */     } 
/* 1355 */     recordSet.executeSql("delete from esbreturnrule_maplist where ruleid=" + paramInt);
/* 1356 */     recordSet.executeSql("delete from esbreturnrule_mapitem where ruleid=" + paramInt);
/* 1357 */     byte b = 1;
/* 1358 */     while (recordSet.next()) {
/*      */       
/* 1360 */       if (recordSet.getCounts() > b) {
/* 1361 */         str1 = str1 + recordSet.getString("ruleid") + ",";
/*      */       } else {
/* 1363 */         str1 = str1 + recordSet.getString("ruleid");
/* 1364 */       }  b++;
/*      */     } 
/*      */     
/* 1367 */     if (str1.equals(""))
/* 1368 */       return false; 
/* 1369 */     return true;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean deleteTempRule() {
/* 1377 */     RecordSet recordSet = new RecordSet();
/* 1378 */     recordSet.executeSql("select id from esbreturnrule where linkid = -2 or linkid = -4");
/* 1379 */     recordSet.first();
/* 1380 */     int i = Util.getIntValue(recordSet.getString("id"));
/* 1381 */     recordSet.executeSql("delete from esbreturnrule_expressionbase where ruleid=" + i);
/* 1382 */     recordSet.executeSql("delete from esbreturnrule_expressions where ruleid=" + i);
/* 1383 */     recordSet.executeSql("delete from esbreturnrule where id=" + i);
/* 1384 */     return true;
/*      */   }
/*      */ 
/*      */   
/*      */   public boolean getExpressionResult() {
/* 1389 */     boolean bool = false;
/*      */     
/* 1391 */     String str = "";
/*      */     
/* 1393 */     return bool;
/*      */   }
/*      */   
/*      */   public int getId() {
/* 1397 */     return this.ruleid;
/*      */   }
/*      */   
/*      */   public void setId(int paramInt) {
/* 1401 */     this.ruleid = paramInt;
/*      */   }
/*      */   
/*      */   public int getRuleid() {
/* 1405 */     return this.ruleid;
/*      */   }
/*      */   
/*      */   public void setRuleid(int paramInt) {
/* 1409 */     this.ruleid = paramInt;
/*      */   }
/*      */   
/*      */   public User getUser() {
/* 1413 */     return this.user;
/*      */   }
/*      */   
/*      */   public void setUser(User paramUser) {
/* 1417 */     this.user = paramUser;
/*      */   }
/*      */ 
/*      */   
/*      */   public String getNM(String paramString1, String paramString2) {
/* 1422 */     if (paramString1.equals("0"))
/* 1423 */       return SystemEnv.getHtmlLabelName(82, Util.getIntValue(paramString2, 7)); 
/* 1424 */     if (paramString1.equals("1")) {
/* 1425 */       return SystemEnv.getHtmlLabelName(19422, Util.getIntValue(paramString2, 7));
/*      */     }
/* 1427 */     return SystemEnv.getHtmlLabelName(84447, Util.getIntValue(paramString2, 7));
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String addLocateInfo(String paramString1, String paramString2) {
/* 1437 */     return paramString1;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public List<String> getRuleMappingOperate(String paramString1, String paramString2) {
/* 1444 */     ArrayList<String> arrayList = new ArrayList();
/* 1445 */     String str1 = "false";
/* 1446 */     String str2 = "false";
/* 1447 */     String str3 = "false";
/* 1448 */     String str4 = "false";
/* 1449 */     String str5 = "false";
/* 1450 */     if (paramString2.equals("0")) {
/*      */       
/* 1452 */       str2 = "true";
/* 1453 */       str3 = "true";
/* 1454 */     } else if (paramString2.equals("1")) {
/*      */       
/* 1456 */       str1 = "true";
/* 1457 */       str4 = "true";
/*      */     } else {
/*      */       
/* 1460 */       str5 = "true";
/*      */     } 
/* 1462 */     arrayList.add(str4);
/* 1463 */     arrayList.add(str3);
/* 1464 */     arrayList.add(str1);
/* 1465 */     arrayList.add(str2);
/* 1466 */     arrayList.add(str5);
/* 1467 */     return arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static void saveClob(String paramString) {
/* 1475 */     RecordSet recordSet = new RecordSet();
/* 1476 */     String str = "";
/* 1477 */     if (recordSet.getDBType().equals("oracle")) {
/* 1478 */       ConnStatement connStatement = null;
/*      */       try {
/* 1480 */         recordSet.executeSql("select max(id) as id from esbreturnrule");
/* 1481 */         if (recordSet.next()) {
/* 1482 */           str = Util.null2String(recordSet.getString("id"));
/*      */         }
/* 1484 */         String str1 = "select condition from esbreturnrule where id = " + str + " for update";
/* 1485 */         if (!"".equals(paramString)) {
/* 1486 */           connStatement = new ConnStatement();
/* 1487 */           connStatement.setStatementSql(str1, false);
/* 1488 */           connStatement.executeQuery();
/* 1489 */           if (connStatement.next()) {
/* 1490 */             CLOB cLOB = connStatement.getClob(1);
/* 1491 */             if (cLOB != null) {
/* 1492 */               char[] arrayOfChar = paramString.toCharArray();
/* 1493 */               Writer writer = cLOB.getCharacterOutputStream();
/* 1494 */               writer.write(arrayOfChar);
/* 1495 */               writer.flush();
/* 1496 */               writer.close();
/*      */             } 
/*      */           } 
/*      */         } 
/* 1500 */       } catch (Exception exception) {
/* 1501 */         exception.printStackTrace();
/*      */       } finally {
/* 1503 */         if (connStatement != null) connStatement.close();
/*      */       
/*      */       } 
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private void updateWFOperatorBatch(ESBReturnRuleBean paramESBReturnRuleBean) {
/* 1513 */     if (!paramESBReturnRuleBean.getRulesrc().equals("3")) {
/*      */       return;
/*      */     }
/*      */     
/* 1517 */     ConnStatement connStatement = null;
/*      */     
/*      */     try {
/* 1520 */       connStatement = new ConnStatement();
/* 1521 */       String str = "SELECT wfid, linkid FROM esbreturnrule_maplist WHERE ruleid=" + this.ruleid + " AND rulesrc=2 AND nm=1";
/* 1522 */       RecordSet recordSet = new RecordSet();
/* 1523 */       recordSet.executeSql(str);
/* 1524 */       while (recordSet.next()) {
/* 1525 */         String str1 = Util.null2String(recordSet.getString("linkid"));
/*      */         
/* 1527 */         String str2 = "";
/*      */         
/* 1529 */         int i = 1;
/* 1530 */         RecordSet recordSet1 = new RecordSet();
/* 1531 */         recordSet1.executeSql("SELECT conditions, ruleRelationship FROM workflow_groupdetail WHERE id=" + str1);
/* 1532 */         if (!recordSet1.next()) {
/*      */           continue;
/*      */         }
/*      */         
/* 1536 */         str2 = Util.null2String(recordSet1.getString("conditions"));
/* 1537 */         i = Util.getIntValue(Util.null2String(recordSet1.getString("ruleRelationship")), 1);
/*      */         
/* 1539 */         if ("".equals(str2)) {
/*      */           continue;
/*      */         }
/*      */         
/* 1543 */         StringBuffer stringBuffer = new StringBuffer();
/* 1544 */         recordSet1.executeSql("SELECT condit FROM esbreturnrule WHERE id IN (" + str2 + ")");
/* 1545 */         while (recordSet1.next()) {
/* 1546 */           if (stringBuffer.length() > 0) {
/* 1547 */             stringBuffer.append((i == 1) ? " AND " : " OR ");
/*      */           }
/*      */           
/* 1550 */           stringBuffer.append(Util.null2String(recordSet1.getString("condit")));
/*      */         } 
/*      */         
/* 1553 */         String str3 = "update workflow_groupdetail set conditioncn=? where id=?";
/*      */         
/* 1555 */         connStatement.setStatementSql(str3);
/* 1556 */         connStatement.setString(1, stringBuffer.toString());
/* 1557 */         connStatement.setInt(2, Util.getIntValue(str1));
/* 1558 */         connStatement.executeUpdate();
/*      */       }
/*      */     
/* 1561 */     } catch (SQLException sQLException) {
/* 1562 */       sQLException.printStackTrace();
/* 1563 */     } catch (Exception exception) {
/* 1564 */       exception.printStackTrace();
/*      */     } finally {
/*      */       try {
/* 1567 */         connStatement.close();
/* 1568 */       } catch (Exception exception) {}
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static Map<String, String> getRuleInfoByRIds(int paramInt, String paramString) {
/* 1583 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 1584 */     RecordSet recordSet = new RecordSet();
/* 1585 */     StringBuffer stringBuffer1 = new StringBuffer();
/* 1586 */     StringBuffer stringBuffer2 = new StringBuffer();
/*      */     
/* 1588 */     int i = 1;
/* 1589 */     if (paramInt == 7) {
/* 1590 */       recordSet.executeSql(" select ruleRelationship from Workflow_SubwfSet where id=" + paramString);
/* 1591 */     } else if (paramInt == 8) {
/* 1592 */       recordSet.executeSql(" select ruleRelationship from Workflow_TriDiffWfDiffField where id=" + paramString);
/*      */     } 
/* 1594 */     if (recordSet.next()) {
/* 1595 */       i = Util.getIntValue(Util.null2String(recordSet.getString("ruleRelationship")), 1);
/*      */     }
/*      */     
/* 1598 */     String str = (i == 1) ? " AND " : " OR ";
/*      */     
/* 1600 */     recordSet.executeSql(" select t1.id, t1.condit from esbreturnrule t1, esbreturnrule_maplist t2 where t1.id = t2.ruleid and t2.linkid=" + paramString + " and t2.rulesrc=" + paramInt + " and t2.rowidenty=0");
/* 1601 */     while (recordSet.next()) {
/* 1602 */       stringBuffer1.append(Util.null2String(recordSet.getString("id"))).append(",");
/* 1603 */       stringBuffer2.append(Util.null2String(recordSet.getString("condit"))).append(str);
/*      */     } 
/*      */     
/* 1606 */     if (stringBuffer1.length() > 0) {
/* 1607 */       stringBuffer1.delete(stringBuffer1.length() - 1, stringBuffer1.length());
/* 1608 */       stringBuffer2.delete(stringBuffer2.length() - str.length(), stringBuffer2.length());
/*      */     } 
/*      */     
/* 1611 */     hashMap.put("ruleids", stringBuffer1.toString());
/* 1612 */     hashMap.put("ruledescs", stringBuffer2.toString());
/*      */     
/* 1614 */     return (Map)hashMap;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static int getDetailTableId(int paramInt1, int paramInt2) {
/* 1687 */     if (paramInt2 == 0) {
/* 1688 */       return (paramInt1 + 9) * 10 - 1;
/*      */     }
/* 1690 */     return (paramInt1 + 1) / 10 - 9;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static String copyRulesByRuleids(String paramString, int paramInt1, int paramInt2, int paramInt3, int paramInt4, List<String> paramList1, List<String> paramList2) throws Exception {
/* 1708 */     if (paramString == null || "".equals(paramString.trim())) {
/* 1709 */       return "";
/*      */     }
/*      */     
/* 1712 */     ArrayList<String> arrayList = new ArrayList();
/*      */     
/* 1714 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 1715 */     RecordSet recordSet1 = new RecordSet();
/* 1716 */     RecordSet recordSet2 = new RecordSet();
/*      */     
/* 1718 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 1719 */     HashMap<Object, Object> hashMap3 = new HashMap<>();
/*      */     
/* 1721 */     String str1 = "select * from esbreturnrule where id in (" + paramString + ") order by id";
/* 1722 */     ConnStatement connStatement = null;
/*      */     try {
/* 1724 */       connStatement = new ConnStatement();
/* 1725 */       connStatement.setStatementSql(str1, false);
/* 1726 */       connStatement.executeQuery();
/* 1727 */       while (connStatement.next()) {
/* 1728 */         String str = connStatement.getString("condit");
/* 1729 */         int i = connStatement.getInt("id");
/* 1730 */         int j = connStatement.getInt("rulesrc");
/* 1731 */         int k = connStatement.getInt("formid");
/*      */         
/* 1733 */         int m = -1;
/* 1734 */         str1 = "insert into esbreturnrule(formid,linkid,rulesrc,isbill,rulename,ruledesc,condit) values(?,?,?,?,?,?,?)";
/*      */         
/* 1736 */         if (k != -1 && j != -1) {
/* 1737 */           if (!arrayList.contains(String.valueOf(i))) {
/* 1738 */             ConnStatement connStatement1 = null;
/*      */             try {
/* 1740 */               connStatement1 = new ConnStatement();
/* 1741 */               connStatement1.setStatementSql(str1);
/* 1742 */               connStatement1.setInt(1, connStatement.getInt("formid"));
/* 1743 */               connStatement1.setInt(2, paramInt3);
/* 1744 */               connStatement1.setInt(3, connStatement.getInt("rulesrc"));
/* 1745 */               connStatement1.setInt(4, connStatement.getInt("isbill"));
/* 1746 */               connStatement1.setString(5, connStatement.getString("rulename"));
/* 1747 */               connStatement1.setString(6, connStatement.getString("ruledesc"));
/*      */               
/* 1749 */               connStatement1.setString(7, connStatement.getString("condit"));
/* 1750 */               connStatement1.executeUpdate();
/*      */               
/* 1752 */               str1 = "select max(id) as maxid from esbreturnrule";
/* 1753 */               connStatement1.setStatementSql(str1);
/* 1754 */               connStatement1.executeQuery();
/* 1755 */               if (connStatement1.next()) {
/* 1756 */                 m = connStatement1.getInt("maxid");
/*      */               }
/* 1758 */               hashMap1.put(i + "", m + "");
/* 1759 */               arrayList.add(i + "");
/* 1760 */             } catch (Exception exception) {
/* 1761 */               exception.printStackTrace();
/*      */             } finally {
/* 1763 */               connStatement1.close();
/*      */             } 
/*      */           } 
/*      */           
/* 1767 */           int n = 0;
/* 1768 */           recordSet1.executeSql("select max(id) as id from esbreturnrule_expressionbase");
/* 1769 */           if (recordSet1.next()) {
/* 1770 */             n = Util.getIntValue(recordSet1.getString("id"), 0);
/*      */           }
/* 1772 */           n++;
/*      */           
/* 1774 */           str1 = "select * from esbreturnrule_expressionbase where ruleid = " + i + " order by id ";
/* 1775 */           recordSet1.executeSql(str1);
/* 1776 */           while (recordSet1.next()) {
/* 1777 */             int i2 = recordSet1.getInt("id");
/* 1778 */             int i3 = recordSet1.getInt("ruleid");
/* 1779 */             int i4 = recordSet1.getInt("datafield");
/* 1780 */             String str3 = recordSet1.getString("datafieldtext");
/* 1781 */             int i5 = recordSet1.getInt("compareoption1");
/* 1782 */             int i6 = recordSet1.getInt("compareoption2");
/* 1783 */             int i7 = recordSet1.getInt("htmltype");
/* 1784 */             int i8 = recordSet1.getInt("typehrm");
/* 1785 */             String str4 = recordSet1.getString("fieldtype");
/* 1786 */             int i9 = recordSet1.getInt("valuetype");
/* 1787 */             int i10 = recordSet1.getInt("paramtype");
/* 1788 */             String str5 = recordSet1.getString("elementvalue1");
/* 1789 */             String str6 = recordSet1.getString("elementlabel1");
/* 1790 */             String str7 = recordSet1.getString("elementvalue2");
/* 1791 */             String str8 = recordSet1.getString("dbtype");
/* 1792 */             int i11 = recordSet1.getInt("nodeid");
/* 1793 */             int i12 = recordSet1.getInt("meetCondition");
/* 1794 */             int i13 = recordSet1.getInt("redius");
/* 1795 */             String str9 = recordSet1.getString("jingdu");
/* 1796 */             String str10 = recordSet1.getString("weidu");
/* 1797 */             int i14 = paramList1.indexOf("" + i11);
/* 1798 */             if (i14 != -1) {
/* 1799 */               i11 = Util.getIntValue(paramList2.get(i14));
/*      */             }
/* 1801 */             recordSet2.executeSql("insert into esbreturnrule_expressionbase(id,ruleid,datafield,datafieldtext,compareoption1,compareoption2,htmltype,typehrm,fieldtype,valuetype,paramtype,elementvalue1,elementlabel1,elementvalue2,dbtype,nodeid,meetcondition,redius,jingdu,weidu) values (" + n + "," + m + "," + i4 + ",'" + str3 + "'," + i5 + "," + i6 + "," + i7 + "," + i8 + ",'" + str4 + "'," + i9 + "," + i10 + ",'" + str5 + "','" + str6 + "','" + str7 + "','" + str8 + "'," + i11 + "," + i12 + "," + i13 + ",'" + str9 + "','" + str10 + "')");
/*      */ 
/*      */ 
/*      */             
/* 1805 */             hashMap2.put(i2 + "", n + "");
/* 1806 */             n++;
/*      */           } 
/*      */ 
/*      */           
/* 1810 */           int i1 = 0;
/* 1811 */           recordSet1.executeSql("select max(id) as id from esbreturnrule_expressions");
/* 1812 */           if (recordSet1.next()) {
/* 1813 */             i1 = Util.getIntValue(recordSet1.getString("id"), 0);
/*      */           }
/* 1815 */           i1++;
/* 1816 */           str1 = "select id,ruleid,relation,expids,expbaseid from esbreturnrule_expressions where ruleid = " + i + " order by id asc";
/* 1817 */           recordSet1.executeSql(str1);
/* 1818 */           while (recordSet1.next()) {
/* 1819 */             int i2 = recordSet1.getInt("id");
/* 1820 */             int i3 = recordSet1.getInt("ruleid");
/* 1821 */             String str3 = recordSet1.getString("relation");
/* 1822 */             String str4 = Util.null2String(recordSet1.getString("expids"));
/* 1823 */             String str5 = recordSet1.getString("expbaseid");
/* 1824 */             if (hashMap2.get(String.valueOf(str5)) != null) {
/* 1825 */               str5 = (String)hashMap2.get(str5);
/*      */             }
/*      */             
/* 1828 */             hashMap3.put(i2 + "", i1 + "");
/* 1829 */             recordSet2.executeSql("insert into esbreturnrule_expressions(id,ruleid,relation,expids,expbaseid) values (" + i1 + "," + m + ",'" + str3 + "','" + str4 + "','" + str5 + "')");
/* 1830 */             i1++;
/*      */           } 
/*      */           
/* 1833 */           str1 = "select id,ruleid,relation,expids,expbaseid from esbreturnrule_expressions where ruleid = " + m;
/* 1834 */           recordSet1.executeSql(str1);
/* 1835 */           while (recordSet1.next()) {
/* 1836 */             int i2 = recordSet1.getInt("id");
/* 1837 */             String str3 = Util.null2String(recordSet1.getString("expids"));
/* 1838 */             String str4 = "";
/* 1839 */             if (!"".equals(str3)) {
/* 1840 */               StringBuilder stringBuilder = new StringBuilder();
/* 1841 */               for (String str5 : str3.split(",")) {
/* 1842 */                 str5 = (hashMap3.get(str5) != null) ? ((String)hashMap3.get(str5)).toString() : "";
/* 1843 */                 if (!str5.isEmpty()) {
/* 1844 */                   stringBuilder.append(str5).append(",");
/*      */                 }
/*      */               } 
/* 1847 */               if (stringBuilder.length() > 0) {
/* 1848 */                 stringBuilder.setLength(stringBuilder.length() - 1);
/* 1849 */                 str4 = stringBuilder.toString();
/*      */               } 
/*      */             } 
/* 1852 */             recordSet2.executeSql("update esbreturnrule_expressions set expids='" + str4 + "' where id = " + i2);
/*      */           } 
/* 1854 */           str1 = "select id,wfid,linkid,ruleid,isused,rulesrc,nm,rowidenty, detailid from esbreturnrule_maplist where ruleid = " + i + " and linkid = " + paramInt2 + " and nm = 0 and rulesrc=" + j;
/* 1855 */           recordSet1.executeSql(str1);
/* 1856 */           while (recordSet1.next()) {
/* 1857 */             int i2 = recordSet1.getInt("id");
/* 1858 */             int i3 = recordSet1.getInt("wfid");
/* 1859 */             int i4 = recordSet1.getInt("linkid");
/* 1860 */             int i5 = recordSet1.getInt("ruleid");
/* 1861 */             int i6 = recordSet1.getInt("isused");
/* 1862 */             int i7 = recordSet1.getInt("rulesrc");
/* 1863 */             int i8 = recordSet1.getInt("nm");
/* 1864 */             int i9 = recordSet1.getInt("rowidenty");
/* 1865 */             int i10 = recordSet1.getInt("detailid");
/*      */             
/* 1867 */             str1 = "insert into esbreturnrule_maplist(wfid,ruleid,linkid,isused,rulesrc,nm,rowidenty, detailid) values( " + paramInt1 + "," + m + "," + paramInt3 + "," + i6 + "," + i7 + "," + i8 + "," + i9 + ", " + i10 + " )";
/* 1868 */             recordSet2.executeSql(str1);
/*      */           }  continue;
/*      */         } 
/* 1871 */         str1 = "select id,ruleid,rulesrc,linkid,rulevarid,formfieldid,rowidenty,nodeid,meetcondition from esbreturnrule_mapitem where ruleid = " + i + " and linkid = " + paramInt2 + " and rulesrc = " + paramInt4;
/* 1872 */         recordSet1.executeSql(str1);
/* 1873 */         while (recordSet1.next()) {
/* 1874 */           int n = recordSet1.getInt("id");
/* 1875 */           int i1 = recordSet1.getInt("ruleid");
/* 1876 */           int i2 = recordSet1.getInt("rulesrc");
/* 1877 */           int i3 = recordSet1.getInt("linkid");
/* 1878 */           int i4 = recordSet1.getInt("rulevarid");
/* 1879 */           int i5 = recordSet1.getInt("formfieldid");
/* 1880 */           int i6 = recordSet1.getInt("rowidenty");
/* 1881 */           int i7 = recordSet1.getInt("nodeid");
/* 1882 */           int i8 = recordSet1.getInt("meetcondition");
/* 1883 */           int i9 = paramList1.indexOf("" + i7);
/* 1884 */           if (i9 != -1) {
/* 1885 */             i7 = Util.getIntValue(paramList2.get(i9));
/*      */           }
/*      */ 
/*      */ 
/*      */ 
/*      */           
/* 1891 */           recordSet2.executeSql("insert into esbreturnrule_mapitem(ruleid,rulesrc,linkid,rulevarid,formfieldid,rowidenty,nodeid,meetcondition) values (" + i + "," + i2 + "," + paramInt3 + "," + i4 + "," + i5 + "," + i6 + "," + i7 + "," + i8 + ")");
/*      */         } 
/*      */ 
/*      */ 
/*      */         
/* 1896 */         str1 = "select id,wfid,linkid,ruleid,isused,rulesrc,nm,rowidenty from esbreturnrule_maplist where ruleid = " + i + " and linkid = " + paramInt2 + " and nm = 1 and rulesrc = " + paramInt4;
/* 1897 */         recordSet1.executeSql(str1);
/* 1898 */         while (recordSet1.next()) {
/* 1899 */           int n = recordSet1.getInt("id");
/* 1900 */           int i1 = recordSet1.getInt("wfid");
/* 1901 */           int i2 = recordSet1.getInt("linkid");
/* 1902 */           int i3 = recordSet1.getInt("ruleid");
/* 1903 */           int i4 = recordSet1.getInt("isused");
/* 1904 */           int i5 = recordSet1.getInt("rulesrc");
/* 1905 */           int i6 = recordSet1.getInt("nm");
/* 1906 */           int i7 = recordSet1.getInt("rowidenty");
/*      */           
/* 1908 */           str1 = "insert into esbreturnrule_maplist(wfid,ruleid,linkid,isused,rulesrc,nm,rowidenty) values( " + paramInt1 + "," + i + "," + paramInt3 + "," + i4 + "," + i5 + "," + i6 + "," + i7 + " )";
/* 1909 */           recordSet2.executeSql(str1);
/*      */         }
/*      */       
/*      */       }
/*      */     
/* 1914 */     } catch (Exception exception) {
/* 1915 */       exception.printStackTrace();
/*      */     } finally {
/* 1917 */       connStatement.close();
/*      */     } 
/*      */     
/* 1920 */     String str2 = "";
/* 1921 */     if (!"".equals(paramString)) {
/* 1922 */       if (paramString.indexOf(",") > -1) {
/* 1923 */         String str3 = "";
/* 1924 */         String str4 = "";
/* 1925 */         String[] arrayOfString = Util.TokenizerString2(paramString, ",");
/* 1926 */         for (byte b = 0; b < arrayOfString.length; b++) {
/* 1927 */           str4 = Util.null2String((String)hashMap1.get(arrayOfString[b]));
/* 1928 */           if (!"".equals(str4)) {
/* 1929 */             if ("".equals(str3)) {
/* 1930 */               str3 = str4;
/*      */             } else {
/* 1932 */               str3 = str3 + "," + str4;
/*      */             }
/*      */           
/* 1935 */           } else if ("".equals(str3)) {
/* 1936 */             str3 = arrayOfString[b];
/*      */           } else {
/* 1938 */             str3 = str3 + "," + arrayOfString[b];
/*      */           } 
/*      */         } 
/*      */         
/* 1942 */         str2 = str3;
/*      */       } else {
/* 1944 */         str2 = paramString;
/* 1945 */         String str = Util.null2String((String)hashMap1.get(paramString));
/* 1946 */         if (!"".equals(str)) {
/* 1947 */           str2 = str;
/*      */         }
/*      */       } 
/*      */     }
/*      */     
/* 1952 */     return str2;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static String getConditionCn(int paramInt1, int paramInt2, User paramUser) {
/* 1965 */     if (paramInt2 != 1 && paramInt2 != 2) return "";
/*      */     
/* 1967 */     String str1 = "";
/* 1968 */     String str2 = "";
/* 1969 */     int i = 1;
/* 1970 */     RecordSet recordSet = new RecordSet();
/*      */     
/* 1972 */     String str3 = "";
/* 1973 */     if (paramInt2 == 1) {
/* 1974 */       str3 = "select newrule conditions, ruleRelationship, conditioncn from workflow_nodelink where id = " + paramInt1;
/* 1975 */     } else if (paramInt2 == 2) {
/* 1976 */       str3 = " select conditions, ruleRelationship, conditioncn from workflow_groupdetail where id = " + paramInt1;
/*      */     } 
/* 1978 */     recordSet.executeSql(str3);
/* 1979 */     if (recordSet.next()) {
/* 1980 */       i = Util.getIntValue(Util.null2String(recordSet.getString("ruleRelationship")), 1);
/* 1981 */       str1 = Util.null2String(recordSet.getString("conditions"));
/* 1982 */       str2 = Util.null2String(recordSet.getString("conditioncn"));
/*      */     } 
/*      */     
/* 1985 */     if ("".equals(str1)) {
/* 1986 */       return "";
/*      */     }
/*      */     
/* 1989 */     if (str1.indexOf(".") >= 0 || str1.indexOf("(") >= 0) {
/* 1990 */       return str2;
/*      */     }
/*      */     
/* 1993 */     StringBuffer stringBuffer = new StringBuffer();
/* 1994 */     recordSet.executeSql("SELECT id FROM esbreturnrule WHERE id IN (" + str1 + ") order by id desc");
/* 1995 */     while (recordSet.next()) {
/* 1996 */       if (stringBuffer.length() > 0) {
/* 1997 */         stringBuffer.append((i == 1) ? " AND " : " OR ");
/*      */       }
/* 1999 */       int j = Util.getIntValue(recordSet.getString("id"));
/* 2000 */       stringBuffer.append(getExpressionsDisplayStringByRuleId(j, paramUser));
/*      */     } 
/* 2002 */     return stringBuffer.toString();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static String getConditionCnGz(int paramInt1, int paramInt2, User paramUser) {
/* 2014 */     if (paramInt2 != 1 && paramInt2 != 2) return "";
/*      */     
/* 2016 */     String str1 = "";
/* 2017 */     String str2 = "";
/* 2018 */     int i = 1;
/* 2019 */     RecordSet recordSet = new RecordSet();
/*      */     
/* 2021 */     String str3 = "";
/* 2022 */     if (paramInt2 == 1) {
/* 2023 */       str3 = "select newrule conditions, ruleRelationship, conditioncn from workflow_nodelink where id = " + paramInt1;
/* 2024 */     } else if (paramInt2 == 2) {
/* 2025 */       str3 = " select conditions, ruleRelationship, conditioncn from workflow_groupdetail where id = " + paramInt1;
/*      */     } 
/* 2027 */     recordSet.executeSql(str3);
/* 2028 */     if (recordSet.next()) {
/* 2029 */       i = Util.getIntValue(Util.null2String(recordSet.getString("ruleRelationship")), 1);
/* 2030 */       str1 = Util.null2String(recordSet.getString("conditions"));
/* 2031 */       str2 = Util.null2String(recordSet.getString("conditioncn"));
/*      */     } 
/*      */     
/* 2034 */     if ("".equals(str1) && "".equals(str2)) {
/* 2035 */       return "";
/*      */     }
/* 2037 */     boolean bool1 = false;
/* 2038 */     boolean bool2 = false;
/* 2039 */     StringBuffer stringBuffer1 = new StringBuffer();
/*      */     
/* 2041 */     if (str2.indexOf(".") >= 0 || str2.indexOf("(") >= 0) {
/* 2042 */       bool1 = true;
/* 2043 */       stringBuffer1.append(str2);
/*      */     } 
/*      */ 
/*      */     
/* 2047 */     StringBuffer stringBuffer2 = new StringBuffer();
/* 2048 */     if (!"".equals(str1)) {
/* 2049 */       recordSet.executeSql("SELECT id FROM esbreturnrule WHERE id IN (" + str1 + ") order by id desc");
/* 2050 */       while (recordSet.next()) {
/* 2051 */         bool2 = true;
/* 2052 */         if (stringBuffer2.length() > 0) {
/* 2053 */           stringBuffer2.append((i == 1) ? " AND " : " OR ");
/*      */         }
/* 2055 */         int j = Util.getIntValue(recordSet.getString("id"));
/* 2056 */         stringBuffer2.append(getExpressionsDisplayStringByRuleId(j, paramUser));
/*      */       } 
/*      */     } 
/*      */ 
/*      */     
/* 2061 */     if (bool1 && !bool2)
/* 2062 */       return stringBuffer1.toString(); 
/* 2063 */     if (!bool1 && bool2)
/*      */     {
/* 2065 */       return stringBuffer2.toString(); } 
/* 2066 */     if (bool1 && bool2) {
/* 2067 */       if (i == 1) {
/* 2068 */         stringBuffer1.append(" AND ").append(stringBuffer2);
/*      */       } else {
/* 2070 */         stringBuffer1.append(" OR ").append(stringBuffer2);
/*      */       } 
/* 2072 */       return stringBuffer1.toString();
/*      */     } 
/* 2074 */     return "";
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   public String getExpressionsDisplayStringByRuleId(String paramString1, String paramString2) {
/* 2080 */     return getExpressionsDisplayStringByRuleId(Util.getIntValue(paramString1), new User(Util.getIntValue(paramString2))).replace("\\\\", "\\");
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static String getExpressionsDisplayStringByRuleId(int paramInt, User paramUser) {
/* 2089 */     RecordSet recordSet = new RecordSet();
/* 2090 */     recordSet.executeSql("select id from esbreturnrule_expressions where ruleid=" + paramInt + " ORDER BY id desc");
/* 2091 */     if (recordSet.next()) {
/* 2092 */       ESBReturnRuleExpressions eSBReturnRuleExpressions = ESBReturnRuleExpressions.getExpressions(Util.getIntValue(recordSet.getString("id")));
/* 2093 */       if (!"".equals(eSBReturnRuleExpressions.getExpids()) && Util.splitString2List(eSBReturnRuleExpressions.getExpids(), ",").size() > 1) {
/* 2094 */         return "(" + getExpressionsDisplayString(eSBReturnRuleExpressions, paramUser) + ")";
/*      */       }
/* 2096 */       return getExpressionsDisplayString(eSBReturnRuleExpressions, paramUser);
/*      */     } 
/*      */     
/* 2099 */     if (recordSet.getDBType().equals("mysql")) {
/* 2100 */       recordSet.executeSql("select `condition` from esbreturnrule where id=" + paramInt);
/*      */     } else {
/* 2102 */       recordSet.executeSql("select condition from esbreturnrule where id=" + paramInt);
/*      */     } 
/* 2104 */     if (recordSet.next()) {
/* 2105 */       return Util.null2String(recordSet.getString(1));
/*      */     }
/*      */     
/* 2108 */     return "";
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private static String getExpressionsDisplayString(ESBReturnRuleExpressions paramESBReturnRuleExpressions, User paramUser) {
/* 2119 */     if (paramESBReturnRuleExpressions == null) return "";
/*      */     
/* 2121 */     StringBuffer stringBuffer = new StringBuffer();
/*      */     
/* 2123 */     String str = paramESBReturnRuleExpressions.getExpids();
/*      */     
/* 2125 */     if (!"".equals(str)) {
/*      */       
/* 2127 */       int i = paramESBReturnRuleExpressions.getRelation();
/* 2128 */       String str1 = "&nbsp;AND&nbsp;";
/* 2129 */       if (i == 0) {
/* 2130 */         str1 = "&nbsp;OR&nbsp;";
/*      */       }
/*      */       
/* 2133 */       List<String> list = Util.splitString2List(str, ",");
/* 2134 */       for (byte b = 0; b < list.size(); b++) {
/* 2135 */         ESBReturnRuleExpressions eSBReturnRuleExpressions = ESBReturnRuleExpressions.getExpressions(Util.getIntValue(list.get(b)));
/* 2136 */         String str2 = getExpressionsDisplayString(eSBReturnRuleExpressions, paramUser);
/*      */         
/* 2138 */         if (b != 0) {
/* 2139 */           stringBuffer.append(str1);
/*      */         }
/*      */         
/* 2142 */         if (!"".equals(eSBReturnRuleExpressions.getExpids()) || list.size() > 1) {
/* 2143 */           stringBuffer.append("(" + str2 + ")");
/*      */         } else {
/* 2145 */           stringBuffer.append(str2);
/*      */         } 
/*      */       } 
/*      */     } else {
/* 2149 */       ESBReturnRuleExpressionBean eSBReturnRuleExpressionBean = ESBReturnRuleExpressionBean.getExpressionBean(paramESBReturnRuleExpressions.getExpbaseid());
/* 2150 */       if (eSBReturnRuleExpressionBean == null) return "";
/*      */       
/* 2152 */       stringBuffer.append(getExpessionDisplayString(eSBReturnRuleExpressionBean, paramUser));
/*      */     } 
/*      */     
/* 2155 */     return stringBuffer.toString();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static String getExpessionDisplayString(ESBReturnRuleExpressionBean paramESBReturnRuleExpressionBean, User paramUser) {
/* 2165 */     String str = "";
/* 2166 */     if (paramESBReturnRuleExpressionBean != null)
/*      */     {
/* 2168 */       str = getDataFieldLabel(paramESBReturnRuleExpressionBean, paramUser) + " " + getCompareLabel(paramESBReturnRuleExpressionBean.getCompareoption1(), paramUser.getLanguage()) + " '" + paramESBReturnRuleExpressionBean.getElementtext1() + "'";
/*      */     }
/* 2170 */     return str.replace("\\\\", "\\");
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private static String getDataFieldLabel(ESBReturnRuleExpressionBean paramESBReturnRuleExpressionBean, User paramUser) {
/* 2177 */     RecordSet recordSet = new RecordSet();
/* 2178 */     String str = "";
/* 2179 */     if (paramESBReturnRuleExpressionBean != null) {
/* 2180 */       str = paramESBReturnRuleExpressionBean.getDatafieldlabel();
/*      */     }
/* 2182 */     return str;
/*      */   }
/*      */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/action/ESBReturnRuleBusiness.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */