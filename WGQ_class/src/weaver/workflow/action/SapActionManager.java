/*     */ package weaver.workflow.action;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import weaver.conn.ConnStatement;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class SapActionManager
/*     */   extends BaseBean
/*     */ {
/*     */   private int actionid;
/*     */   private String actionname;
/*     */   private int workflowid;
/*     */   private int nodeid;
/*     */   private int nodelinkid;
/*     */   private int ispreoperator;
/*     */   private int actionorder;
/*     */   private String sapoperation;
/*     */   private ArrayList inparaList;
/*     */   private ArrayList outparaList;
/*     */   private HttpServletRequest request;
/*     */   private int isnewsap;
/*     */   
/*     */   public int getIsnewsap() {
/*  32 */     return this.isnewsap;
/*     */   }
/*     */   public void setIsnewsap(int paramInt) {
/*  35 */     this.isnewsap = paramInt;
/*     */   }
/*     */   public void setRequest(HttpServletRequest paramHttpServletRequest) {
/*  38 */     this.request = paramHttpServletRequest;
/*     */   }
/*     */   public ArrayList getInparaList() {
/*  41 */     return this.inparaList;
/*     */   }
/*     */   public void setInparaList(ArrayList paramArrayList) {
/*  44 */     this.inparaList = paramArrayList;
/*     */   }
/*     */   public ArrayList getOutparaList() {
/*  47 */     return this.outparaList;
/*     */   }
/*     */   public void setOutparaList(ArrayList paramArrayList) {
/*  50 */     this.outparaList = paramArrayList;
/*     */   }
/*     */   public int getActionid() {
/*  53 */     return this.actionid;
/*     */   }
/*     */   public void setActionid(int paramInt) {
/*  56 */     this.actionid = paramInt;
/*     */   }
/*     */   public String getActionname() {
/*  59 */     return this.actionname;
/*     */   }
/*     */   public void setActionname(String paramString) {
/*  62 */     this.actionname = paramString;
/*     */   }
/*     */   public int getWorkflowid() {
/*  65 */     return this.workflowid;
/*     */   }
/*     */   public void setWorkflowid(int paramInt) {
/*  68 */     this.workflowid = paramInt;
/*     */   }
/*     */   public int getNodeid() {
/*  71 */     return this.nodeid;
/*     */   }
/*     */   public void setNodeid(int paramInt) {
/*  74 */     this.nodeid = paramInt;
/*     */   }
/*     */   public int getNodelinkid() {
/*  77 */     return this.nodelinkid;
/*     */   }
/*     */   public void setNodelinkid(int paramInt) {
/*  80 */     this.nodelinkid = paramInt;
/*     */   }
/*     */   public int getIspreoperator() {
/*  83 */     return this.ispreoperator;
/*     */   }
/*     */   public void setIspreoperator(int paramInt) {
/*  86 */     this.ispreoperator = paramInt;
/*     */   }
/*     */   public int getActionorder() {
/*  89 */     return this.actionorder;
/*     */   }
/*     */   public void setActionorder(int paramInt) {
/*  92 */     this.actionorder = paramInt;
/*     */   }
/*     */   public String getSapoperation() {
/*  95 */     return this.sapoperation;
/*     */   }
/*     */   public void setSapoperation(String paramString) {
/*  98 */     this.sapoperation = paramString;
/*     */   }
/*     */   
/*     */   public SapActionManager() {
/* 102 */     this.actionid = 0;
/* 103 */     this.actionname = "";
/* 104 */     this.workflowid = 0;
/* 105 */     this.nodeid = 0;
/* 106 */     this.nodelinkid = 0;
/* 107 */     this.ispreoperator = 0;
/* 108 */     this.actionorder = 0;
/* 109 */     this.sapoperation = "";
/* 110 */     this.inparaList = new ArrayList();
/* 111 */     this.outparaList = new ArrayList();
/* 112 */     this.isnewsap = 0;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public ArrayList getSapActionSetList(int paramInt1, int paramInt2, int paramInt3, int paramInt4) {
/* 119 */     ArrayList<ArrayList<String>> arrayList = new ArrayList();
/*     */     try {
/* 121 */       RecordSet recordSet = new RecordSet();
/* 122 */       String str = "select * from sapactionset where ";
/* 123 */       if (paramInt2 > 0) {
/* 124 */         if (paramInt4 != 1) {
/* 125 */           paramInt4 = 0;
/*     */         }
/* 127 */         str = str + " workflowid=" + paramInt1 + " and nodeid=" + paramInt2 + " and ispreoperator=" + paramInt4;
/*     */       } else {
/* 129 */         str = str + " workflowid=" + paramInt1 + " and nodelinkid=" + paramInt3;
/*     */       } 
/* 131 */       recordSet.execute(str);
/* 132 */       while (recordSet.next()) {
/* 133 */         int i = Util.getIntValue(recordSet.getString("id"));
/* 134 */         String str1 = Util.null2String(recordSet.getString("actionname"));
/* 135 */         int j = Util.getIntValue(recordSet.getString("actionorder"));
/* 136 */         String str2 = Util.null2String(recordSet.getString("sapoperation"));
/* 137 */         ArrayList<String> arrayList1 = new ArrayList();
/* 138 */         arrayList1.add("" + i);
/* 139 */         arrayList1.add(str1);
/* 140 */         arrayList1.add("" + j);
/* 141 */         arrayList1.add(str2);
/*     */         
/* 143 */         arrayList.add(arrayList1);
/*     */       } 
/* 145 */     } catch (Exception exception) {
/* 146 */       writeLog(exception);
/*     */     } 
/* 148 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int doSaveAspAction() {
/* 155 */     if ("0".equals(this.isnewsap + "")) {
/*     */       
/* 157 */       ConnStatement connStatement = null;
/* 158 */       writeLog("-------------------------------------- doSaveAspAction ---- start ------------------------------------");
/*     */       try {
/* 160 */         connStatement = new ConnStatement();
/* 161 */         String str1 = "";
/*     */         
/* 163 */         writeLog("doSaveAspAction\tthis.actionid:" + this.actionid);
/* 164 */         if (this.actionid <= 0) {
/* 165 */           str1 = "insert into sapactionset(actionname, workflowid, nodeid, nodelinkid, ispreoperator, actionorder, sapoperation) values (?,?,?,?,?,?,?)";
/* 166 */           connStatement.setStatementSql(str1);
/* 167 */           connStatement.setString(1, this.actionname);
/* 168 */           connStatement.setInt(2, this.workflowid);
/* 169 */           connStatement.setInt(3, this.nodeid);
/* 170 */           connStatement.setInt(4, this.nodelinkid);
/* 171 */           connStatement.setInt(5, this.ispreoperator);
/* 172 */           connStatement.setInt(6, this.actionorder);
/* 173 */           connStatement.setString(7, this.sapoperation);
/* 174 */           connStatement.executeUpdate();
/* 175 */           str1 = "insert into sapactionset(actionname, workflowid, nodeid, nodelinkid, ispreoperator, actionorder, sapoperation) values ('" + this.actionname + "','" + this.workflowid + "','" + this.nodeid + "','" + this.nodelinkid + "','" + this.ispreoperator + "','" + this.actionorder + "','" + this.sapoperation + "')";
/* 176 */           writeLog("doSaveAspAction\tnew sql:" + str1);
/*     */           
/* 178 */           str1 = "select max(id) as maxid from sapactionset where workflowid=" + this.workflowid + " and nodeid=" + this.nodeid + " and nodelinkid=" + this.nodelinkid + " and ispreoperator=" + this.ispreoperator;
/* 179 */           connStatement.setStatementSql(str1);
/* 180 */           connStatement.executeQuery();
/* 181 */           writeLog("doSaveAspAction\tnew sql:" + str1);
/* 182 */           while (connStatement.next()) {
/* 183 */             this.actionid = Util.getIntValue(connStatement.getString("maxid"), 0);
/* 184 */             writeLog("doSaveAspAction\tstatement\tnew actionid:" + this.actionid);
/*     */           } 
/*     */           
/* 187 */           writeLog("doSaveAspAction\tnew actionid:" + this.actionid);
/*     */         } else {
/* 189 */           str1 = "update sapactionset set actionname=?, actionorder=?, sapoperation=? where id=?";
/* 190 */           connStatement.setStatementSql(str1);
/* 191 */           connStatement.setString(1, this.actionname);
/* 192 */           connStatement.setInt(2, this.actionorder);
/* 193 */           connStatement.setString(3, this.sapoperation);
/* 194 */           connStatement.setInt(4, this.actionid);
/* 195 */           connStatement.executeUpdate();
/* 196 */           str1 = "update sapactionset set actionname='" + this.actionname + "', actionorder='" + this.actionorder + "', sapoperation='" + this.sapoperation + "' where id='" + this.actionid + "'";
/* 197 */           writeLog("doSaveAspAction\tupdate sql:" + str1);
/*     */         } 
/*     */ 
/*     */         
/* 201 */         RecordSet recordSet = new RecordSet();
/* 202 */         str1 = "delete from sapactionsetdetail where mainid=" + this.actionid;
/* 203 */         recordSet.execute(str1);
/*     */         
/* 205 */         writeLog("保存明细：");
/* 206 */         String str2 = Util.null2String(this.request.getParameter("submitdtlid0"));
/* 207 */         String[] arrayOfString1 = str2.split(",");
/* 208 */         writeLog("submitdtlid0:" + str2 + "\t\tsubmitdids0.length:" + arrayOfString1.length);
/* 209 */         if (arrayOfString1 != null)
/* 210 */           for (byte b = 0; b < arrayOfString1.length; b++) {
/* 211 */             String str = Util.null2String(arrayOfString1[b]);
/* 212 */             writeLog("submitdid:" + str);
/* 213 */             if (!"".equals(str)) {
/* 214 */               int i = Util.getIntValue(this.request.getParameter("paratype0_" + str), -1);
/* 215 */               writeLog("paratype_t:" + i);
/* 216 */               if (i >= 0) {
/*     */ 
/*     */                 
/* 219 */                 String str4 = Util.null2String(this.request.getParameter("paraname0_" + str));
/* 220 */                 String str5 = Util.null2String(this.request.getParameter("paratext0_" + str));
/* 221 */                 str1 = "insert into sapactionsetdetail (mainid, type, paratype, paraname, paratext) values (?,?,?,?,?)";
/* 222 */                 connStatement.setStatementSql(str1);
/* 223 */                 connStatement.setInt(1, this.actionid);
/* 224 */                 connStatement.setInt(2, 0);
/* 225 */                 connStatement.setInt(3, i);
/* 226 */                 connStatement.setString(4, str4);
/* 227 */                 connStatement.setString(5, str5);
/* 228 */                 connStatement.executeUpdate();
/* 229 */                 str1 = "insert into sapactionsetdetail (mainid, type, paratype, paraname, paratext) values ('" + this.actionid + "','" + Character.MIN_VALUE + "','" + i + "','" + str4 + "','" + str5 + "')";
/* 230 */                 writeLog("insert into sql:" + str1);
/*     */               } 
/*     */             } 
/*     */           }  
/* 234 */         String str3 = Util.null2String(this.request.getParameter("submitdtlid1"));
/* 235 */         String[] arrayOfString2 = str3.split(",");
/* 236 */         writeLog("submitdtlid1:" + str3 + "\t\tsubmitdids1.length:" + arrayOfString2.length);
/* 237 */         if (arrayOfString2 != null) {
/* 238 */           for (byte b = 0; b < arrayOfString2.length; b++) {
/* 239 */             String str = Util.null2String(arrayOfString2[b]);
/* 240 */             if (!"".equals(str)) {
/* 241 */               int i = Util.getIntValue(this.request.getParameter("paratype1_" + str), -1);
/* 242 */               writeLog("paratype_t:" + i);
/* 243 */               if (i >= 0)
/*     */               {
/*     */                 
/* 246 */                 String str4 = Util.null2String(this.request.getParameter("paraname1_" + str));
/* 247 */                 String str5 = Util.null2String(this.request.getParameter("paratext1_" + str));
/* 248 */                 str1 = "insert into sapactionsetdetail (mainid, type, paratype, paraname, paratext) values (?,?,?,?,?)";
/* 249 */                 connStatement.setStatementSql(str1);
/* 250 */                 connStatement.setInt(1, this.actionid);
/* 251 */                 connStatement.setInt(2, 1);
/* 252 */                 connStatement.setInt(3, i);
/* 253 */                 connStatement.setString(4, str4);
/* 254 */                 connStatement.setString(5, str5);
/* 255 */                 connStatement.executeUpdate();
/* 256 */                 str1 = "insert into sapactionsetdetail (mainid, type, paratype, paraname, paratext) values ('" + this.actionid + "','" + '\001' + "','" + i + "','" + str4 + "','" + str5 + "')";
/* 257 */                 writeLog("insert into sql:" + str1);
/*     */               }
/*     */             
/*     */             }
/*     */           
/*     */           }
/*     */         
/*     */         }
/* 265 */       } catch (Exception exception) {
/* 266 */         this.actionid = -1;
/* 267 */         writeLog(exception);
/*     */       } finally {
/*     */         try {
/* 270 */           connStatement.close();
/* 271 */         } catch (Exception exception) {
/* 272 */           writeLog(exception);
/*     */         } 
/*     */       } 
/*     */     } 
/*     */ 
/*     */     
/* 278 */     BaseAction baseAction = new BaseAction();
/* 279 */     baseAction.checkActionOnNodeOrLink(this.workflowid, this.nodeid, this.nodelinkid, this.ispreoperator, this.isnewsap);
/*     */     
/* 281 */     writeLog("-------------------------------------- doSaveAspAction ---- end ------------------------------------");
/* 282 */     return this.actionid;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int doDeleteSapAction() {
/* 289 */     byte b = -1;
/*     */     try {
/* 291 */       RecordSet recordSet = new RecordSet();
/* 292 */       String str = "delete from sapactionset where id=" + this.actionid;
/* 293 */       recordSet.execute(str);
/* 294 */       str = "delete from sapactionsetdetail where mainid=" + this.actionid;
/* 295 */       recordSet.execute(str);
/* 296 */       BaseAction baseAction = new BaseAction();
/* 297 */       baseAction.checkActionOnNodeOrLink(this.workflowid, this.nodeid, this.nodelinkid, this.ispreoperator, this.isnewsap);
/* 298 */       b = 1;
/* 299 */     } catch (Exception exception) {
/* 300 */       writeLog(exception);
/* 301 */       b = 0;
/*     */     } 
/* 303 */     return b;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public void getSapActionSetById() {
/*     */     try {
/* 310 */       RecordSet recordSet = new RecordSet();
/* 311 */       String str = "select * from sapactionset where id=" + this.actionid;
/* 312 */       recordSet.execute(str);
/* 313 */       writeLog("getSapActionSetById:" + str);
/* 314 */       if (recordSet.next()) {
/* 315 */         this.actionname = Util.null2String(recordSet.getString("actionname"));
/* 316 */         this.actionorder = Util.getIntValue(recordSet.getString("actionorder"));
/* 317 */         this.sapoperation = Util.null2String(recordSet.getString("sapoperation"));
/*     */       } 
/* 319 */       writeLog("actionname:" + this.actionname + "\t\tactionorder:" + this.actionorder + "\t\tsapoperation:" + this.sapoperation);
/*     */       
/* 321 */       this.inparaList = new ArrayList();
/* 322 */       this.outparaList = new ArrayList();
/* 323 */       str = "select * from sapactionsetdetail where mainid=" + this.actionid + " order by id";
/* 324 */       recordSet.execute(str);
/* 325 */       writeLog("getSapActionSetById:" + str);
/* 326 */       while (recordSet.next()) {
/* 327 */         int i = Util.getIntValue(recordSet.getString("type"), 0);
/* 328 */         int j = Util.getIntValue(recordSet.getString("paratype"), 0);
/* 329 */         String str1 = Util.null2String(recordSet.getString("paraname"));
/* 330 */         String str2 = Util.null2String(recordSet.getString("paratext"));
/* 331 */         writeLog("type_t:" + i + "\t\tparatype_t:" + j + "\tparaname_t:" + str1 + "\tparatext_t:" + str2);
/*     */         
/* 333 */         ArrayList<String> arrayList = new ArrayList();
/* 334 */         arrayList.add("" + j);
/* 335 */         arrayList.add(str1);
/* 336 */         arrayList.add(str2);
/* 337 */         if (i == 0) {
/* 338 */           this.inparaList.add(arrayList); continue;
/* 339 */         }  if (i == 1) {
/* 340 */           this.outparaList.add(arrayList);
/*     */         }
/*     */       } 
/* 343 */     } catch (Exception exception) {
/* 344 */       writeLog(exception);
/*     */     } 
/*     */   }
/*     */   
/*     */   public static void main(String[] paramArrayOfString) {}
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/action/SapActionManager.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */