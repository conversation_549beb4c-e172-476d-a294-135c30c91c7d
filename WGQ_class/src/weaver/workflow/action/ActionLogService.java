/*     */ package weaver.workflow.action;
/*     */ 
/*     */ import java.util.concurrent.ExecutorService;
/*     */ import java.util.concurrent.Executors;
/*     */ import weaver.conn.ConnStatement;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.TimeUtil;
/*     */ import weaver.integration.logging.Logger;
/*     */ import weaver.integration.logging.LoggerFactory;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class ActionLogService
/*     */ {
/*  27 */   private Logger newlog = LoggerFactory.getLogger(ActionLogService.class);
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  32 */   private ActionLogDAO dao = new ActionLogDAO();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int save(String paramString1, String paramString2, String paramString3) {
/*  44 */     String str1 = TimeUtil.getCurrentTimeString();
/*  45 */     String str2 = str1.substring(0, 10);
/*  46 */     String str3 = str1.substring(11);
/*  47 */     String str4 = "";
/*     */     
/*  49 */     if ("0".equals(paramString2) || "1".equals(paramString2) || "3".equals(paramString2) || "5".equals(paramString2) || "6".equals(paramString2)) {
/*  50 */       if ("3".equals(paramString2)) {
/*  51 */         paramString1 = paramString1.replace("action.", "");
/*  52 */         int i = getActionDBid(paramString1);
/*  53 */         paramString1 = "" + i;
/*  54 */       } else if ("5".equals(paramString2)) {
/*  55 */         int i = (new ESBAction()).getActionDBidByName(paramString1);
/*  56 */         paramString1 = "" + i;
/*  57 */       } else if ("6".equals(paramString2)) {
/*  58 */         int i = (new ESBNewAction()).getActionDBidByName(paramString1);
/*  59 */         paramString1 = "" + i;
/*     */       } 
/*  61 */       str4 = getActionShowName(paramString1, paramString2);
/*     */     } 
/*  63 */     RecordSet recordSet = new RecordSet();
/*  64 */     String str5 = this.dao.insert(paramString1, paramString2, str2, str3, paramString3, str4);
/*     */     
/*  66 */     boolean bool = false;
/*     */ 
/*     */     
/*  69 */     bool = recordSet.executeSql(str5);
/*  70 */     this.newlog.error("insertSql=" + str5);
/*     */     
/*  72 */     recordSet.executeSql(this.dao.getid(paramString1, paramString2, str2, str3));
/*  73 */     recordSet.next();
/*  74 */     return recordSet.getInt(1);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int saveNew(String paramString1, String paramString2, String paramString3, String paramString4, String paramString5, String paramString6, String paramString7, int paramInt) {
/*  97 */     String str1 = "";
/*     */     
/*  99 */     if ("0".equals(paramString2) || "1".equals(paramString2) || "3".equals(paramString2) || "5".equals(paramString2) || "6".equals(paramString2)) {
/* 100 */       if ("3".equals(paramString2)) {
/* 101 */         paramString1 = paramString1.replace("action.", "");
/* 102 */         int i = getActionDBid(paramString1);
/* 103 */         paramString1 = "" + i;
/* 104 */       } else if ("5".equals(paramString2)) {
/* 105 */         int i = (new ESBAction()).getActionDBidByName(paramString1);
/* 106 */         paramString1 = "" + i;
/* 107 */       } else if ("6".equals(paramString2)) {
/* 108 */         int i = (new ESBNewAction()).getActionDBidByName(paramString1);
/* 109 */         paramString1 = "" + i;
/*     */       } 
/* 111 */       str1 = getActionShowName(paramString1, paramString2);
/*     */     } 
/* 113 */     RecordSet recordSet = new RecordSet();
/* 114 */     String str2 = this.dao.insertNew(paramString1, paramString2, paramString4, paramString5, paramString3, str1, paramString6, paramString7, paramInt);
/*     */     
/* 116 */     boolean bool = false;
/*     */     
/* 118 */     this.newlog.info("insertSql=" + str2);
/* 119 */     bool = recordSet.executeSql(str2);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 125 */     return 0;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean updateFinishTime(int paramInt) {
/* 134 */     String str1 = TimeUtil.getCurrentTimeString();
/* 135 */     String str2 = str1.substring(0, 10);
/* 136 */     String str3 = str1.substring(11);
/*     */     
/* 138 */     RecordSet recordSet = new RecordSet();
/* 139 */     String str4 = this.dao.updateFinishTime(paramInt, str2, str3);
/*     */     
/* 141 */     boolean bool = false;
/* 142 */     bool = recordSet.executeSql(str4);
/* 143 */     this.newlog.error("updateFinishTimeSql=" + str4);
/*     */ 
/*     */     
/* 146 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean updateExecResultFail(int paramInt) {
/* 154 */     RecordSet recordSet = new RecordSet();
/* 155 */     String str = this.dao.updateExecResultFail(paramInt);
/* 156 */     boolean bool = false;
/* 157 */     bool = recordSet.executeSql(str);
/* 158 */     this.newlog.error("updateExecResultSql=" + str);
/* 159 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getActionShowName(String paramString1, String paramString2) {
/* 169 */     String str1 = "";
/* 170 */     String str2 = "";
/* 171 */     String str3 = "";
/* 172 */     if ("0".equals(paramString2)) {
/* 173 */       str1 = "dmlactionname";
/* 174 */       str2 = "formactionset";
/* 175 */     } else if ("1".equals(paramString2)) {
/* 176 */       str1 = "actionname";
/* 177 */       str2 = "wsformactionset";
/*     */     }
/* 179 */     else if ("3".equals(paramString2)) {
/* 180 */       str1 = "actionshowname";
/* 181 */       str2 = "actionsetting";
/*     */     }
/* 183 */     else if ("5".equals(paramString2)) {
/* 184 */       str1 = "actionname";
/* 185 */       str2 = "esbformactionset";
/*     */     }
/* 187 */     else if ("6".equals(paramString2)) {
/* 188 */       str1 = "actionname";
/* 189 */       str2 = "esb_actionset";
/*     */     } 
/*     */     
/* 192 */     String str4 = "select " + str1 + " from " + str2 + " where id=?";
/* 193 */     ConnStatement connStatement = null;
/*     */     
/*     */     try {
/* 196 */       connStatement = new ConnStatement();
/* 197 */       connStatement.setStatementSql(str4);
/* 198 */       connStatement.setString(1, paramString1);
/*     */       
/* 200 */       connStatement.executeQuery();
/* 201 */       if (connStatement.next())
/*     */       {
/* 203 */         str3 = connStatement.getString(str1);
/*     */       }
/*     */     }
/* 206 */     catch (Exception exception) {
/*     */       
/* 208 */       this.newlog.error("getActionShowName error!", exception);
/*     */     }
/*     */     finally {
/*     */       
/* 212 */       if (null != connStatement)
/*     */       {
/* 214 */         connStatement.close();
/*     */       }
/*     */     } 
/* 217 */     return str3;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void threadSaveLog(String paramString1, String paramString2, String paramString3, String paramString4, String paramString5, String paramString6, String paramString7, int paramInt) {
/* 232 */     ExecutorService executorService = Executors.newFixedThreadPool(3);
/*     */     try {
/* 234 */       ActionLogSaveRunnable actionLogSaveRunnable = new ActionLogSaveRunnable();
/* 235 */       actionLogSaveRunnable.setActionid(paramString1);
/* 236 */       actionLogSaveRunnable.setActiontype(paramString2);
/* 237 */       actionLogSaveRunnable.setClientip(paramString3);
/* 238 */       actionLogSaveRunnable.setCreateDate(paramString4);
/* 239 */       actionLogSaveRunnable.setCreateTime(paramString5);
/* 240 */       actionLogSaveRunnable.setModifyDate(paramString6);
/* 241 */       actionLogSaveRunnable.setModifyTime(paramString7);
/* 242 */       actionLogSaveRunnable.setExecresult(paramInt);
/* 243 */       executorService.submit(actionLogSaveRunnable);
/* 244 */     } catch (Exception exception) {
/* 245 */       this.newlog.error("threadSaveLog error!", exception);
/*     */     } finally {
/* 247 */       executorService.shutdown();
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getActionDBid(String paramString) {
/* 259 */     String str = "select id from actionsetting where actionname=?";
/* 260 */     int i = 0;
/* 261 */     ConnStatement connStatement = null;
/*     */     
/*     */     try {
/* 264 */       connStatement = new ConnStatement();
/* 265 */       connStatement.setStatementSql(str);
/* 266 */       connStatement.setString(1, paramString);
/*     */       
/* 268 */       connStatement.executeQuery();
/*     */       
/* 270 */       if (connStatement.next())
/*     */       {
/* 272 */         i = connStatement.getInt("id");
/*     */       }
/*     */     }
/* 275 */     catch (Exception exception) {
/*     */       
/* 277 */       this.newlog.error(exception);
/*     */     }
/*     */     finally {
/*     */       
/* 281 */       if (null != connStatement)
/*     */       {
/* 283 */         connStatement.close();
/*     */       }
/*     */     } 
/* 286 */     return i;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/action/ActionLogService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */