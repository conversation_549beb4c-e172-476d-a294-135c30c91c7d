/*     */ package weaver.workflow.action;
/*     */ 
/*     */ import weaver.integration.logging.Logger;
/*     */ import weaver.integration.logging.LoggerFactory;
/*     */ 
/*     */ 
/*     */ 
/*     */ public class ActionLogSaveRunnable
/*     */   implements Runnable
/*     */ {
/*     */   private String actionid;
/*     */   private String actiontype;
/*     */   private String clientip;
/*     */   private String createDate;
/*     */   private String createTime;
/*     */   private String modifyDate;
/*     */   private String modifyTime;
/*     */   private int execresult;
/*     */   
/*     */   public String getActionid() {
/*  21 */     return this.actionid;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setActionid(String paramString) {
/*  29 */     this.actionid = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getActiontype() {
/*  37 */     return this.actiontype;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setActiontype(String paramString) {
/*  45 */     this.actiontype = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getClientip() {
/*  53 */     return this.clientip;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setClientip(String paramString) {
/*  61 */     this.clientip = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCreateDate() {
/*  69 */     return this.createDate;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setCreateDate(String paramString) {
/*  77 */     this.createDate = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCreateTime() {
/*  85 */     return this.createTime;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setCreateTime(String paramString) {
/*  93 */     this.createTime = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getModifyDate() {
/* 101 */     return this.modifyDate;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setModifyDate(String paramString) {
/* 109 */     this.modifyDate = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getModifyTime() {
/* 117 */     return this.modifyTime;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setModifyTime(String paramString) {
/* 125 */     this.modifyTime = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getExecresult() {
/* 162 */     return this.execresult;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setExecresult(int paramInt) {
/* 170 */     this.execresult = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/* 181 */   private Logger newlog = LoggerFactory.getLogger(ActionLogSaveRunnable.class);
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void run() {
/* 187 */     ActionLogService actionLogService = new ActionLogService();
/* 188 */     actionLogService.saveNew(this.actionid, this.actiontype, this.clientip, this.createDate, this.createTime, this.modifyDate, this.modifyTime, this.execresult);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/action/ActionLogSaveRunnable.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */