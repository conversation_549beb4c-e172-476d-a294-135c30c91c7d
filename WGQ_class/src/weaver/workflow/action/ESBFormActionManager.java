/*     */ package weaver.workflow.action;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import weaver.conn.ConnStatement;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.TimeUtil;
/*     */ import weaver.general.Util;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class ESBFormActionManager
/*     */   extends BaseBean
/*     */ {
/*  85 */   private String operate = "";
/*  86 */   private int actionid = 0;
/*  87 */   private String actionname = "";
/*  88 */   private int formid = 0;
/*  89 */   private int isbill = 0; private String esbid;
/*  90 */   private String xmltext = ""; private String esbname;
/*  91 */   private int rettype = 0;
/*  92 */   private String retstr = "";
/*  93 */   private String inpara = ""; private int version;
/*  94 */   private String subcompanyid = "";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getSubcompanyid() {
/* 102 */     return this.subcompanyid;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setSubcompanyid(String paramString) {
/* 109 */     this.subcompanyid = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getInpara() {
/* 117 */     return this.inpara;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setInpara(String paramString) {
/* 124 */     this.inpara = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getOperate() {
/* 131 */     return this.operate;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setOperate(String paramString) {
/* 138 */     this.operate = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getActionid() {
/* 145 */     return this.actionid;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setActionid(int paramInt) {
/* 152 */     this.actionid = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getActionname() {
/* 159 */     return this.actionname;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setActionname(String paramString) {
/* 166 */     this.actionname = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getFormid() {
/* 174 */     return this.formid;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setFormid(int paramInt) {
/* 182 */     this.formid = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getIsbill() {
/* 190 */     return this.isbill;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setIsbill(int paramInt) {
/* 198 */     this.isbill = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getEsbid() {
/* 205 */     return this.esbid;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setEsbid(String paramString) {
/* 212 */     this.esbid = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getEsbName() {
/* 219 */     return this.esbname;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setEsbName(String paramString) {
/* 226 */     this.esbname = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getXmltext() {
/* 234 */     return this.xmltext;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setXmltext(String paramString) {
/* 241 */     this.xmltext = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getRettype() {
/* 248 */     return this.rettype;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setRettype(int paramInt) {
/* 255 */     this.rettype = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getRetstr() {
/* 262 */     return this.retstr;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setRetstr(String paramString) {
/* 269 */     this.retstr = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public ArrayList doSelectESBAction(int paramInt, String paramString) {
/* 279 */     ArrayList<ArrayList<String>> arrayList = new ArrayList();
/*     */     try {
/* 281 */       RecordSet recordSet = new RecordSet();
/* 282 */       String str = "select * from esbformactionset where ";
/* 283 */       if (this.actionid > 0) {
/* 284 */         str = str + " id=" + this.actionid;
/*     */       } else {
/* 286 */         str = str + " formid=" + paramInt + " and isbill=" + paramString;
/*     */       } 
/* 288 */       recordSet.execute(str);
/* 289 */       while (recordSet.next()) {
/* 290 */         int i = Util.getIntValue(recordSet.getString("id"));
/* 291 */         String str1 = Util.null2String(recordSet.getString("actionname"));
/* 292 */         String str2 = Util.null2String(recordSet.getString("esbid"));
/* 293 */         String str3 = Util.null2String(recordSet.getString("esbname"));
/* 294 */         int j = Util.getIntValue(recordSet.getString("version"));
/*     */         
/* 296 */         ArrayList<String> arrayList1 = new ArrayList();
/* 297 */         arrayList1.add("" + i);
/* 298 */         arrayList1.add(str1);
/* 299 */         arrayList1.add(str2);
/* 300 */         arrayList1.add(str3);
/* 301 */         arrayList1.add("" + j);
/*     */         
/* 303 */         arrayList.add(arrayList1);
/*     */       } 
/* 305 */     } catch (Exception exception) {
/* 306 */       writeLog(exception);
/*     */     } 
/* 308 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int doDeleteESBAction() {
/* 316 */     byte b = -1;
/*     */     try {
/* 318 */       RecordSet recordSet = new RecordSet();
/* 319 */       String str1 = "delete from esbformactionset where id=" + this.actionid;
/* 320 */       recordSet.execute(str1);
/* 321 */       String str2 = "delete from esbmethodparamvalue where actionid=" + this.actionid;
/* 322 */       recordSet.execute(str2);
/* 323 */       b = 1;
/* 324 */     } catch (Exception exception) {
/* 325 */       writeLog(exception);
/* 326 */       b = 0;
/*     */     } 
/* 328 */     return b;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int doSaveESBAction() {
/* 336 */     ConnStatement connStatement = null;
/*     */     try {
/* 338 */       connStatement = new ConnStatement();
/* 339 */       String str1 = "";
/* 340 */       String str2 = TimeUtil.getCurrentTimeString();
/* 341 */       String str3 = str2.substring(0, 10);
/* 342 */       String str4 = str2.substring(11);
/* 343 */       if (this.actionid <= 0) {
/* 344 */         str1 = "insert into esbformactionset(actionname, formid, isbill, esbid, esbname, version,subcompanyid,CreateDate,CreateTime,ModifyDate,ModifyTime) values(?,?,?,?,?,?,?,?,?,?,?)";
/* 345 */         connStatement.setStatementSql(str1);
/* 346 */         byte b = 1;
/* 347 */         connStatement.setString(b++, this.actionname);
/* 348 */         connStatement.setInt(b++, this.formid);
/* 349 */         connStatement.setInt(b++, this.isbill);
/* 350 */         connStatement.setString(b++, this.esbid);
/* 351 */         connStatement.setString(b++, this.esbname);
/* 352 */         connStatement.setInt(b++, this.version);
/* 353 */         connStatement.setInt(b++, this.subcompanyid);
/* 354 */         connStatement.setString(b++, str3);
/* 355 */         connStatement.setString(b++, str4);
/* 356 */         connStatement.setString(b++, str3);
/* 357 */         connStatement.setString(b++, str4);
/* 358 */         connStatement.executeUpdate();
/*     */         
/* 360 */         str1 = "select max(id) as maxid from esbformactionset where formid=? and isbill=?";
/* 361 */         connStatement.setStatementSql(str1);
/* 362 */         connStatement.setInt(1, this.formid);
/* 363 */         connStatement.setInt(2, this.isbill);
/* 364 */         connStatement.executeQuery();
/* 365 */         while (connStatement.next()) {
/* 366 */           this.actionid = Util.getIntValue(connStatement.getString("maxid"), 0);
/*     */         }
/*     */       } else {
/* 369 */         str1 = "update esbformactionset set actionname=? ,esbid=?, esbname=?, version=?,subcompanyid=?, ModifyDate=?,ModifyTime=? where id=?";
/* 370 */         connStatement.setStatementSql(str1);
/* 371 */         byte b = 1;
/* 372 */         connStatement.setString(b++, this.actionname);
/* 373 */         connStatement.setString(b++, this.esbid);
/* 374 */         connStatement.setString(b++, this.esbname);
/* 375 */         connStatement.setInt(b++, this.version);
/* 376 */         connStatement.setInt(b++, this.subcompanyid);
/*     */         
/* 378 */         connStatement.setString(b++, str3);
/* 379 */         connStatement.setString(b++, str4);
/*     */         
/* 381 */         connStatement.setInt(b++, this.actionid);
/* 382 */         connStatement.executeUpdate();
/*     */       } 
/* 384 */     } catch (Exception exception) {
/* 385 */       writeLog(exception);
/* 386 */       this.actionid = -1;
/*     */     } finally {
/*     */       try {
/* 389 */         connStatement.close();
/* 390 */       } catch (Exception exception) {}
/*     */     } 
/*     */ 
/*     */     
/* 394 */     return this.actionid;
/*     */   }
/*     */   
/*     */   public int getVersion() {
/* 398 */     return this.version;
/*     */   }
/*     */   
/*     */   public void setVersion(int paramInt) {
/* 402 */     this.version = paramInt;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/action/ESBFormActionManager.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */