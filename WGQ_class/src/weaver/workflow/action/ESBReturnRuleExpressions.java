/*     */ package weaver.workflow.action;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class ESBReturnRuleExpressions
/*     */ {
/*  29 */   private int id = -1;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  34 */   private int ruleid = 0;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  39 */   private int relation = -1;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  44 */   private String expids = "";
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  49 */   private int expbaseid = -1;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String relationRuleID;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  59 */   private ESBReturnRuleExpressions parent = null;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  64 */   private List<Object> childrens = new ArrayList();
/*     */ 
/*     */   
/*     */   public static final int RELATION_GREATER = 0;
/*     */ 
/*     */   
/*     */   public static final int RELATION_GREATERANDEQUAL = 1;
/*     */ 
/*     */   
/*     */   public static final int RELATION_LESS = 2;
/*     */ 
/*     */   
/*     */   public static final int RELATION_LESSANDEQUAL = 3;
/*     */ 
/*     */   
/*     */   public static final int RELATION_EQUAL = 4;
/*     */   
/*     */   public static final int RELATION_NOTEQUAL = 5;
/*     */   
/*     */   public static final int RELATION_CONTAIN = 6;
/*     */   
/*     */   public static final int RELATION_NOTCONTAIN = 7;
/*     */ 
/*     */   
/*     */   public int persistence2db(RecordSet paramRecordSet, String paramString) throws Exception {
/*  89 */     ArrayList<String> arrayList = new ArrayList(); int i;
/*  90 */     for (i = 0; i < this.childrens.size(); i++) {
/*  91 */       Object object = this.childrens.get(i);
/*     */       
/*  93 */       if (object instanceof ESBReturnRuleExpressions) {
/*  94 */         int j = ((ESBReturnRuleExpressions)object).persistence2db(paramRecordSet, paramString);
/*  95 */         arrayList.add(String.valueOf(j));
/*     */       } else {
/*  97 */         int j = ((ESBReturnRuleExpressionBean)object).persistence2db(paramRecordSet, paramString);
/*  98 */         arrayList.add(String.valueOf(j));
/*     */       } 
/*     */     } 
/* 101 */     i = getdbid(paramRecordSet);
/* 102 */     String str1 = listToString(arrayList);
/*     */     
/* 104 */     String str2 = "insert into esbreturnrule_expressions(id, ruleid, relation, expids) values (" + i + ", " + this.ruleid + ", " + this.relation + ", '" + str1 + "')";
/* 105 */     paramRecordSet.executeSql(str2);
/*     */     
/* 107 */     return i;
/*     */   }
/*     */ 
/*     */   
/*     */   public static ESBReturnRuleExpressions getExpressions(int paramInt) {
/* 112 */     ESBReturnRuleExpressions eSBReturnRuleExpressions = null;
/*     */     
/* 114 */     RecordSet recordSet = new RecordSet();
/* 115 */     recordSet.executeSql("select * from esbreturnrule_expressions where id=" + paramInt);
/* 116 */     if (recordSet.next()) {
/* 117 */       eSBReturnRuleExpressions = new ESBReturnRuleExpressions();
/* 118 */       eSBReturnRuleExpressions.setExpids(Util.null2String(recordSet.getString("expids")));
/* 119 */       eSBReturnRuleExpressions.setExpbaseid(Util.getIntValue(recordSet.getString("expbaseid")));
/* 120 */       eSBReturnRuleExpressions.setRelation(Util.getIntValue(recordSet.getString("relation")));
/*     */     } 
/* 122 */     return eSBReturnRuleExpressions;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String toIKExpression() {
/* 130 */     String str = "";
/* 131 */     for (byte b = 0; b < this.childrens.size(); b++) {
/* 132 */       Object object = this.childrens.get(b);
/* 133 */       if (b != 0) {
/* 134 */         str = str + getRelation4IK();
/*     */       }
/*     */       
/* 137 */       if (object instanceof ESBReturnRuleExpressions) {
/* 138 */         str = str + "(" + ((ESBReturnRuleExpressions)object).toIKExpression() + ")";
/*     */       } else {
/* 140 */         str = str + ((ESBReturnRuleExpressionBean)object).toIKExpression2();
/*     */       } 
/*     */     } 
/* 143 */     return str;
/*     */   }
/*     */ 
/*     */   
/*     */   public String getExpressionFieldids() {
/* 148 */     String str = "";
/* 149 */     for (byte b = 0; b < this.childrens.size(); b++) {
/* 150 */       Object object = this.childrens.get(b);
/* 151 */       if (object instanceof ESBReturnRuleExpressions) {
/* 152 */         str = str + ((ESBReturnRuleExpressions)object).getExpressionFieldids();
/* 153 */         if (!str.equals("")) {
/* 154 */           str = str + ",";
/*     */         }
/*     */       } else {
/*     */         
/* 158 */         str = str + ((ESBReturnRuleExpressionBean)object).getExpressionFieldids();
/* 159 */         if (!str.equals(""))
/* 160 */           str = str + ","; 
/*     */       } 
/*     */     } 
/* 163 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, String> toIKExpressionMap() {
/* 172 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 173 */     hashMap.put("exp", toIKExpression());
/* 174 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static synchronized int getdbid(RecordSet paramRecordSet) throws Exception {
/* 184 */     int i = 0;
/*     */     
/* 186 */     paramRecordSet.executeSql("select max(id) as id from esbreturnrule_expressions");
/* 187 */     if (paramRecordSet.next()) {
/* 188 */       i = Util.getIntValue(paramRecordSet.getString("id"), 0);
/*     */     }
/*     */     
/* 191 */     return i + 1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String listToString(List paramList) {
/* 200 */     StringBuilder stringBuilder = new StringBuilder();
/* 201 */     if (paramList != null && paramList.size() > 0) {
/* 202 */       for (byte b = 0; b < paramList.size(); b++) {
/* 203 */         if (b < paramList.size() - 1) {
/* 204 */           stringBuilder.append((new StringBuilder()).append(paramList.get(b)).append(",").toString());
/*     */         } else {
/* 206 */           stringBuilder.append(paramList.get(b));
/*     */         } 
/*     */       } 
/*     */     }
/* 210 */     return stringBuilder.toString();
/*     */   }
/*     */   
/*     */   public int getId() {
/* 214 */     return this.id;
/*     */   }
/*     */   
/*     */   public void setId(int paramInt) {
/* 218 */     this.id = paramInt;
/*     */   }
/*     */   
/*     */   public int getRelation() {
/* 222 */     return this.relation;
/*     */   }
/*     */   
/*     */   public String getRelationString() {
/* 226 */     if (this.relation == 0) {
/* 227 */       return " OR ";
/*     */     }
/* 229 */     return " AND ";
/*     */   }
/*     */   
/*     */   public String getRelation4IK() {
/* 233 */     if (this.relation == 0) {
/* 234 */       return " || ";
/*     */     }
/* 236 */     return " && ";
/*     */   }
/*     */ 
/*     */   
/*     */   public void setRelation(int paramInt) {
/* 241 */     this.relation = paramInt;
/*     */   }
/*     */   
/*     */   public String getRelationRuleID() {
/* 245 */     return this.relationRuleID;
/*     */   }
/*     */   
/*     */   public void setRelationRuleID(String paramString) {
/* 249 */     this.relationRuleID = paramString;
/*     */   }
/*     */   
/*     */   public List<Object> getChildrens() {
/* 253 */     return this.childrens;
/*     */   }
/*     */   
/*     */   public void setChildrens(List<Object> paramList) {
/* 257 */     this.childrens = paramList;
/*     */   }
/*     */   
/*     */   public ESBReturnRuleExpressions getParent() {
/* 261 */     return this.parent;
/*     */   }
/*     */   
/*     */   public void setParent(ESBReturnRuleExpressions paramESBReturnRuleExpressions) {
/* 265 */     this.parent = paramESBReturnRuleExpressions;
/*     */   }
/*     */   
/*     */   public int getRuleid() {
/* 269 */     return this.ruleid;
/*     */   }
/*     */   
/*     */   public void setRuleid(int paramInt) {
/* 273 */     this.ruleid = paramInt;
/*     */   }
/*     */   
/*     */   public String getExpids() {
/* 277 */     return this.expids;
/*     */   }
/*     */   
/*     */   public void setExpids(String paramString) {
/* 281 */     this.expids = paramString;
/*     */   }
/*     */   
/*     */   public int getExpbaseid() {
/* 285 */     return this.expbaseid;
/*     */   }
/*     */   
/*     */   public void setExpbaseid(int paramInt) {
/* 289 */     this.expbaseid = paramInt;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/action/ESBReturnRuleExpressions.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */