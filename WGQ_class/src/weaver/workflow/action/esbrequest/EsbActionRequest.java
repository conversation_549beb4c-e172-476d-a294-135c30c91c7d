/*     */ package weaver.workflow.action.esbrequest;
/*     */ 
/*     */ import com.alibaba.fastjson.JSONArray;
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import com.api.integration.util.TreeBean;
/*     */ import com.engine.integration.bean.esbActionBase.EsbActionRequestBean;
/*     */ import com.weaver.esb.server.enums.ParamType;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class EsbActionRequest
/*     */ {
/*     */   private RequestStrategy strategy;
/*     */   
/*     */   public JSONObject getRequestParamsFormmode(List<TreeBean<EsbActionRequestBean>> paramList, String paramString, int paramInt) {
/*  33 */     EsbActionRequestBean esbActionRequestBean = new EsbActionRequestBean();
/*  34 */     esbActionRequestBean.setAssignType(paramString);
/*  35 */     JSONArray jSONArray = this.strategy.getDetailData(esbActionRequestBean);
/*     */     
/*  37 */     JSONObject jSONObject = jSONArray.getJSONObject(paramInt);
/*     */     
/*  39 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  40 */     hashMap.put(esbActionRequestBean.getAssignType(), jSONObject);
/*  41 */     return getRequestParams(paramList, (Map)hashMap);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public JSONObject getRequestParams(List<TreeBean<EsbActionRequestBean>> paramList, Map<String, JSONObject> paramMap) {
/*  48 */     JSONObject jSONObject = new JSONObject(true);
/*     */ 
/*     */ 
/*     */     
/*  52 */     if (paramMap == null) {
/*  53 */       paramMap = new HashMap<>();
/*     */     }
/*     */     
/*  56 */     for (TreeBean<EsbActionRequestBean> treeBean : paramList) {
/*  57 */       EsbActionRequestBean esbActionRequestBean = (EsbActionRequestBean)treeBean.getCurrentNode();
/*  58 */       List<TreeBean<EsbActionRequestBean>> list = treeBean.getChildNode();
/*     */       
/*  60 */       if (list == null || list.isEmpty()) {
/*     */         
/*  62 */         if (esbActionRequestBean.isArrs()) {
/*  63 */           JSONArray jSONArray = new JSONArray();
/*  64 */           jSONObject.put(esbActionRequestBean.getParamName(), jSONArray);
/*     */           
/*     */           continue;
/*     */         } 
/*  68 */         if (ParamType.JSON.hasEquals(esbActionRequestBean.getParamType()) || ParamType.XML.hasEquals(esbActionRequestBean.getParamType())) {
/*  69 */           JSONObject jSONObject1 = new JSONObject();
/*  70 */           jSONObject.put(esbActionRequestBean.getParamName(), jSONObject1); continue;
/*     */         } 
/*  72 */         String str = esbActionRequestBean.getAssignType();
/*  73 */         Object object = "";
/*  74 */         if (this.strategy.isDeatilType(str)) {
/*     */           
/*  76 */           JSONObject jSONObject1 = paramMap.get(str);
/*  77 */           if (jSONObject1 != null) {
/*  78 */             String str1 = this.strategy.getFiedName(esbActionRequestBean.getAssignValue());
/*  79 */             Object object1 = jSONObject1.get(str1);
/*     */           } 
/*     */         } else {
/*     */           
/*  83 */           object = this.strategy.getValue(esbActionRequestBean);
/*     */         } 
/*     */         
/*  86 */         jSONObject.put(esbActionRequestBean.getParamName(), object);
/*     */         continue;
/*     */       } 
/*  89 */       if (esbActionRequestBean.isArrs()) {
/*  90 */         jSONObject.put(esbActionRequestBean.getParamName(), getDetailAssignValue(esbActionRequestBean, list, paramMap)); continue;
/*     */       } 
/*  92 */       jSONObject.put(esbActionRequestBean.getParamName(), getRequestParams(list, paramMap));
/*     */     } 
/*     */ 
/*     */     
/*  96 */     return jSONObject;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public JSONArray getDetailAssignValue(EsbActionRequestBean paramEsbActionRequestBean, List<TreeBean<EsbActionRequestBean>> paramList, Map<String, JSONObject> paramMap) {
/* 106 */     JSONArray jSONArray1 = new JSONArray();
/*     */     
/* 108 */     JSONArray jSONArray2 = this.strategy.getDetailData(paramEsbActionRequestBean);
/* 109 */     if (jSONArray2 == null || jSONArray2.size() <= 0) {
/* 110 */       return jSONArray1;
/*     */     }
/*     */     
/* 113 */     for (byte b = 0; b < jSONArray2.size(); b++) {
/* 114 */       JSONObject jSONObject1 = jSONArray2.getJSONObject(b);
/*     */ 
/*     */       
/* 117 */       if (paramMap == null) {
/* 118 */         paramMap = new HashMap<>();
/*     */       }
/* 120 */       paramMap.put(paramEsbActionRequestBean.getAssignType(), jSONObject1);
/*     */       
/* 122 */       JSONObject jSONObject2 = getRequestParams(paramList, paramMap);
/*     */       
/* 124 */       jSONArray1.add(jSONObject2);
/*     */     } 
/* 126 */     return jSONArray1;
/*     */   }
/*     */   
/*     */   public RequestStrategy getStrategy() {
/* 130 */     return this.strategy;
/*     */   }
/*     */   
/*     */   public void setStrategy(RequestStrategy paramRequestStrategy) {
/* 134 */     this.strategy = paramRequestStrategy;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/action/esbrequest/EsbActionRequest.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */