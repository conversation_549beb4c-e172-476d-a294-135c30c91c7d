package weaver.workflow.action.esbrequest;

import com.alibaba.fastjson.JSONArray;
import com.engine.integration.bean.esbActionBase.EsbActionRequestBean;

public interface RequestStrategy {
  boolean isDeatilType(String paramString);
  
  Object getValue(EsbActionRequestBean paramEsbActionRequestBean);
  
  JSONArray getDetailData(EsbActionRequestBean paramEsbActionRequestBean);
  
  String getFiedName(String paramString);
}


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/action/esbrequest/RequestStrategy.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */