/*     */ package weaver.workflow.action;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class ESBReturnAssignBean
/*     */   implements Comparable
/*     */ {
/*     */   private int id;
/*     */   private String fieldName;
/*     */   private String fieldShowName;
/*     */   private String fieldType;
/*     */   private String operation;
/*     */   private String returnParam;
/*     */   private int transType;
/*     */   private String extraInfo;
/*     */   private Integer showOrder;
/*     */   private String dbfieldName;
/*     */   private String value;
/*     */   
/*     */   public String getFieldType() {
/*  75 */     return this.fieldType;
/*     */   }
/*     */   
/*     */   public void setFieldType(String paramString) {
/*  79 */     this.fieldType = paramString;
/*     */   }
/*     */   
/*     */   public int getTransType() {
/*  83 */     return this.transType;
/*     */   }
/*     */   
/*     */   public void setTransType(int paramInt) {
/*  87 */     this.transType = paramInt;
/*     */   }
/*     */   
/*     */   public String getExtraInfo() {
/*  91 */     return this.extraInfo;
/*     */   }
/*     */   
/*     */   public void setExtraInfo(String paramString) {
/*  95 */     this.extraInfo = paramString;
/*     */   }
/*     */   
/*     */   public Integer getShowOrder() {
/*  99 */     return this.showOrder;
/*     */   }
/*     */   
/*     */   public void setShowOrder(Integer paramInteger) {
/* 103 */     this.showOrder = paramInteger;
/*     */   }
/*     */   
/*     */   public int getId() {
/* 107 */     return this.id;
/*     */   }
/*     */   
/*     */   public void setId(int paramInt) {
/* 111 */     this.id = paramInt;
/*     */   }
/*     */   
/*     */   public String getFieldName() {
/* 115 */     return this.fieldName;
/*     */   }
/*     */   
/*     */   public void setFieldName(String paramString) {
/* 119 */     this.fieldName = paramString;
/*     */   }
/*     */   
/*     */   public String getOperation() {
/* 123 */     return this.operation;
/*     */   }
/*     */   
/*     */   public void setOperation(String paramString) {
/* 127 */     this.operation = paramString;
/*     */   }
/*     */   
/*     */   public String getReturnParam() {
/* 131 */     return this.returnParam;
/*     */   }
/*     */   
/*     */   public void setReturnParam(String paramString) {
/* 135 */     this.returnParam = paramString;
/*     */   }
/*     */ 
/*     */   
/*     */   public String getFieldShowName() {
/* 140 */     return this.fieldShowName;
/*     */   }
/*     */   
/*     */   public void setFieldShowName(String paramString) {
/* 144 */     this.fieldShowName = paramString;
/*     */   }
/*     */   
/*     */   public String getDbfieldName() {
/* 148 */     return this.dbfieldName;
/*     */   }
/*     */   
/*     */   public void setDbfieldName(String paramString) {
/* 152 */     this.dbfieldName = paramString;
/*     */   }
/*     */   
/*     */   public String getValue() {
/* 156 */     return this.value;
/*     */   }
/*     */   
/*     */   public void setValue(String paramString) {
/* 160 */     this.value = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int compareTo(Object paramObject) {
/* 170 */     if (paramObject instanceof ESBReturnAssignBean) {
/* 171 */       ESBReturnAssignBean eSBReturnAssignBean = (ESBReturnAssignBean)paramObject;
/* 172 */       return getShowOrder().compareTo(eSBReturnAssignBean.getShowOrder());
/*     */     } 
/* 174 */     throw new ClassCastException("不能转换为ESBReturnAssignBean类型的对象...");
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/action/ESBReturnAssignBean.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */