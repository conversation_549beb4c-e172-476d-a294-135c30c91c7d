/*     */ package weaver.workflow.action;
/*     */ 
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Iterator;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import org.wltea.expression.ExpressionEvaluator;
/*     */ import org.wltea.expression.datameta.Variable;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class ESBReturnRuleInterface
/*     */   extends BaseBean
/*     */ {
/*     */   private int actionid;
/*     */   
/*     */   public ESBReturnRuleInterface(int paramInt) {
/*  38 */     this.actionid = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getActionid() {
/*  45 */     return this.actionid;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setActionid(int paramInt) {
/*  53 */     this.actionid = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean check(String paramString) {
/*  62 */     Integer integer = Integer.valueOf(1);
/*  63 */     ArrayList<String> arrayList = new ArrayList();
/*     */     
/*  65 */     RecordSet recordSet = new RecordSet();
/*     */     
/*  67 */     String str = "select returnRuleRelationShip from esbformactionset where id=" + this.actionid;
/*  68 */     recordSet.execute(str);
/*  69 */     if (recordSet.next()) {
/*  70 */       integer = Integer.valueOf(Util.getIntValue(recordSet.getString("returnRuleRelationShip"), 1));
/*     */     }
/*     */     
/*  73 */     str = "select id from esbreturnrule where setid=" + this.actionid;
/*  74 */     recordSet.execute(str);
/*  75 */     while (recordSet.next()) {
/*  76 */       String str1 = recordSet.getString("id");
/*  77 */       if (str1 != null && !"".equals(str1)) {
/*  78 */         arrayList.add(str1);
/*     */       }
/*     */     } 
/*     */ 
/*     */     
/*  83 */     JSONObject jSONObject = JSONObject.parseObject(paramString);
/*  84 */     ArrayList<Variable> arrayList1 = new ArrayList();
/*  85 */     parseReturnValue(jSONObject, arrayList1, "");
/*     */     
/*  87 */     for (byte b = 0; b < arrayList.size(); b++) {
/*  88 */       String str1 = arrayList.get(b);
/*     */ 
/*     */       
/*  91 */       ESBReturnRuleExpressions eSBReturnRuleExpressions = ESBReturnRuleBusiness.getExpressionsByAll(Integer.parseInt(str1));
/*     */       
/*  93 */       HashMap<Object, Object> hashMap = new HashMap<>();
/*  94 */       String str2 = "";
/*  95 */       if (eSBReturnRuleExpressions != null) {
/*  96 */         Map<String, String> map = eSBReturnRuleExpressions.toIKExpressionMap();
/*  97 */         str2 = map.get("exp");
/*     */       } 
/*     */ 
/*     */       
/* 101 */       Boolean bool = Boolean.valueOf(false);
/*     */       try {
/* 103 */         Object object = ExpressionEvaluator.evaluate(str2, arrayList1);
/* 104 */       } catch (Exception exception) {
/*     */         
/* 106 */         StringBuffer stringBuffer = new StringBuffer();
/* 107 */         for (byte b1 = 0; b1 < arrayList1.size(); b1++) {
/* 108 */           Variable variable = arrayList1.get(b1);
/* 109 */           stringBuffer.append(variable.getVariableName()).append(":").append(variable.getStringValue());
/* 110 */           if (b1 < arrayList1.size() - 1) {
/* 111 */             stringBuffer.append(", ");
/*     */           }
/*     */         } 
/* 114 */         writeLog("ESB返回值条件异常， 条件：[" + str2.toString() + "], 值：[" + stringBuffer.toString() + "]");
/* 115 */         bool = Boolean.valueOf(false);
/*     */       } 
/*     */ 
/*     */       
/* 119 */       boolean bool1 = "true".equals(bool.toString());
/*     */       
/* 121 */       if (integer.intValue() == 1) {
/* 122 */         if (!bool1) {
/* 123 */           return false;
/*     */         }
/*     */       }
/* 126 */       else if (bool1) {
/* 127 */         return true;
/*     */       } 
/*     */     } 
/*     */ 
/*     */     
/* 132 */     return (integer.intValue() == 1);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void parseReturnValue(Object paramObject, List<Variable> paramList, String paramString) {
/* 143 */     if (paramObject instanceof JSONObject) {
/* 144 */       JSONObject jSONObject = (JSONObject)paramObject;
/* 145 */       Iterator<String> iterator = jSONObject.keySet().iterator();
/* 146 */       while (iterator.hasNext()) {
/* 147 */         String str1 = iterator.next();
/* 148 */         Object object = jSONObject.get(str1);
/* 149 */         String str2 = "";
/* 150 */         if ("".equals(paramString)) {
/* 151 */           str2 = str1;
/*     */         } else {
/* 153 */           str2 = paramString + "_" + str1;
/*     */         } 
/* 155 */         if (object instanceof JSONObject) {
/*     */           
/* 157 */           parseReturnValue(object, paramList, str2); continue;
/*     */         } 
/* 159 */         paramList.add(Variable.createVariable(str2, object));
/*     */       } 
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/action/ESBReturnRuleInterface.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */