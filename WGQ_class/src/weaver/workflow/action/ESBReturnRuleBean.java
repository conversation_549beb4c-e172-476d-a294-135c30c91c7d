/*     */ package weaver.workflow.action;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class ESBReturnRuleBean
/*     */ {
/*  22 */   private int id = -1;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  27 */   private String rulesrc = "";
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  32 */   private String formid = "";
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  37 */   private String linkid = "";
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  42 */   private String isbill = "";
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  47 */   private String rulename = "";
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  52 */   private String ruledesc = "";
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  57 */   private String condit = "";
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  62 */   private int setid = -1;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  67 */   private String esbid = "";
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  72 */   private String condition = "";
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  77 */   private int version = -1;
/*     */ 
/*     */   
/*     */   public int getVersion() {
/*  81 */     return this.version;
/*     */   }
/*     */   
/*     */   public void setVersion(int paramInt) {
/*  85 */     this.version = paramInt;
/*     */   }
/*     */   
/*     */   public int getId() {
/*  89 */     return this.id;
/*     */   }
/*     */   
/*     */   public void setId(int paramInt) {
/*  93 */     this.id = paramInt;
/*     */   }
/*     */   
/*     */   public String getRulesrc() {
/*  97 */     return this.rulesrc;
/*     */   }
/*     */   
/*     */   public void setRulesrc(String paramString) {
/* 101 */     this.rulesrc = paramString;
/*     */   }
/*     */   
/*     */   public String getFormid() {
/* 105 */     return this.formid;
/*     */   }
/*     */   
/*     */   public void setFormid(String paramString) {
/* 109 */     this.formid = paramString;
/*     */   }
/*     */   
/*     */   public String getLinkid() {
/* 113 */     return this.linkid;
/*     */   }
/*     */   
/*     */   public void setLinkid(String paramString) {
/* 117 */     this.linkid = paramString;
/*     */   }
/*     */   
/*     */   public String getRulename() {
/* 121 */     return this.rulename;
/*     */   }
/*     */   
/*     */   public void setRulename(String paramString) {
/* 125 */     this.rulename = paramString;
/*     */   }
/*     */   
/*     */   public String getRuledesc() {
/* 129 */     return this.ruledesc;
/*     */   }
/*     */   
/*     */   public void setRuledesc(String paramString) {
/* 133 */     this.ruledesc = paramString;
/*     */   }
/*     */   
/*     */   public String getIsbill() {
/* 137 */     return this.isbill;
/*     */   }
/*     */   
/*     */   public void setIsbill(String paramString) {
/* 141 */     this.isbill = paramString;
/*     */   }
/*     */   
/*     */   public String getCondit() {
/* 145 */     return this.condit;
/*     */   }
/*     */   
/*     */   public void setCondit(String paramString) {
/* 149 */     this.condit = paramString;
/*     */   }
/*     */   
/*     */   public int getSetid() {
/* 153 */     return this.setid;
/*     */   }
/*     */   
/*     */   public void setSetid(int paramInt) {
/* 157 */     this.setid = paramInt;
/*     */   }
/*     */   
/*     */   public String getCondition() {
/* 161 */     return this.condition;
/*     */   }
/*     */   
/*     */   public void setCondition(String paramString) {
/* 165 */     this.condition = paramString;
/*     */   }
/*     */   
/*     */   public String getEsbid() {
/* 169 */     return this.esbid;
/*     */   }
/*     */   
/*     */   public void setEsbid(String paramString) {
/* 173 */     this.esbid = paramString;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/action/ESBReturnRuleBean.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */