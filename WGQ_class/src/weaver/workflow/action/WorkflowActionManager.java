/*     */ package weaver.workflow.action;
/*     */ import java.util.ArrayList;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import weaver.conn.ConnStatement;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ 
/*     */ public class WorkflowActionManager extends BaseBean {
/*     */   private int isTriggerReject;
/*     */   private String operate;
/*     */   private int actionid;
/*     */   private String actionname;
/*     */   private int isused;
/*     */   private int workflowid;
/*     */   private int nodeid;
/*     */   
/*     */   public int getIsTriggerReject() {
/*  18 */     return this.isTriggerReject;
/*     */   } private int nodelinkid; private int ispreoperator; private int actionorder; private String interfaceid; private int interfacetype; private int isnewsap; private int drawbackflag;
/*     */   public void setIsTriggerReject(int paramInt) {
/*  21 */     this.isTriggerReject = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public WorkflowActionManager() {
/*  38 */     this.operate = "";
/*  39 */     this.actionid = 0;
/*  40 */     this.isused = 0;
/*  41 */     this.actionname = "";
/*  42 */     this.workflowid = 0;
/*  43 */     this.nodeid = 0;
/*  44 */     this.nodelinkid = 0;
/*  45 */     this.ispreoperator = 0;
/*  46 */     this.actionorder = 0;
/*  47 */     this.interfaceid = "";
/*  48 */     this.interfacetype = 0;
/*  49 */     this.isnewsap = 0;
/*  50 */     this.drawbackflag = 0;
/*     */   }
/*     */   public String getOperate() {
/*  53 */     return this.operate;
/*     */   }
/*     */   public void setOperate(String paramString) {
/*  56 */     this.operate = paramString;
/*     */   }
/*     */   public int getActionid() {
/*  59 */     return this.actionid;
/*     */   }
/*     */   public void setActionid(int paramInt) {
/*  62 */     this.actionid = paramInt;
/*     */   }
/*     */   public String getActionname() {
/*  65 */     return this.actionname;
/*     */   }
/*     */   public void setActionname(String paramString) {
/*  68 */     this.actionname = paramString;
/*     */   }
/*     */   public int getWorkflowid() {
/*  71 */     return this.workflowid;
/*     */   }
/*     */   public void setWorkflowid(int paramInt) {
/*  74 */     this.workflowid = paramInt;
/*     */   }
/*     */   public int getNodeid() {
/*  77 */     return this.nodeid;
/*     */   }
/*     */   public void setNodeid(int paramInt) {
/*  80 */     this.nodeid = paramInt;
/*     */   }
/*     */   public int getNodelinkid() {
/*  83 */     return this.nodelinkid;
/*     */   }
/*     */   public void setNodelinkid(int paramInt) {
/*  86 */     this.nodelinkid = paramInt;
/*     */   }
/*     */   public int getIspreoperator() {
/*  89 */     return this.ispreoperator;
/*     */   }
/*     */   public void setIspreoperator(int paramInt) {
/*  92 */     this.ispreoperator = paramInt;
/*     */   }
/*     */   public int getActionorder() {
/*  95 */     return this.actionorder;
/*     */   }
/*     */   public void setActionorder(int paramInt) {
/*  98 */     this.actionorder = paramInt;
/*     */   }
/*     */   public int getDrawbackflag() {
/* 101 */     return this.drawbackflag;
/*     */   }
/*     */   public void setDrawbackflag(int paramInt) {
/* 104 */     this.drawbackflag = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void saveAction(HttpServletRequest paramHttpServletRequest) {
/* 112 */     int i = Util.getIntValue(paramHttpServletRequest.getParameter("workflowid"), 0);
/* 113 */     String[] arrayOfString1 = paramHttpServletRequest.getParameterValues("isused");
/* 114 */     String[] arrayOfString2 = paramHttpServletRequest.getParameterValues("actionid");
/* 115 */     String[] arrayOfString3 = paramHttpServletRequest.getParameterValues("type");
/* 116 */     String[] arrayOfString4 = paramHttpServletRequest.getParameterValues("actionnodeid");
/*     */     
/* 118 */     String[] arrayOfString5 = paramHttpServletRequest.getParameterValues("actionispreoperator");
/*     */     
/* 120 */     String[] arrayOfString6 = paramHttpServletRequest.getParameterValues("actionnodelinkid");
/* 121 */     String[] arrayOfString7 = paramHttpServletRequest.getParameterValues("actionname");
/* 122 */     String[] arrayOfString8 = paramHttpServletRequest.getParameterValues("actionorder");
/* 123 */     String[] arrayOfString9 = paramHttpServletRequest.getParameterValues("interfaceid");
/* 124 */     String[] arrayOfString10 = paramHttpServletRequest.getParameterValues("interfacetype");
/* 125 */     String str = "0";
/* 126 */     int j = 0;
/* 127 */     int k = 0;
/* 128 */     int m = 0;
/*     */     
/* 130 */     WorkflowActionManager workflowActionManager = new WorkflowActionManager();
/* 131 */     if (null != arrayOfString2 && arrayOfString2.length > 0)
/*     */     {
/* 133 */       for (byte b = 0; b < arrayOfString2.length; b++) {
/*     */         
/* 135 */         int n = Util.getIntValue(arrayOfString2[b], 0);
/* 136 */         j = Util.getIntValue(arrayOfString4[b], 0);
/* 137 */         k = Util.getIntValue(arrayOfString5[b], 0);
/* 138 */         m = Util.getIntValue(arrayOfString6[b], 0);
/* 139 */         String str1 = Util.null2String(arrayOfString7[b]);
/* 140 */         int i1 = Util.getIntValue(arrayOfString8[b], 0);
/* 141 */         String str2 = Util.null2String(arrayOfString9[b]);
/* 142 */         int i2 = Util.getIntValue(arrayOfString10[b], 0);
/* 143 */         int i3 = Util.getIntValue(arrayOfString1[b], 0);
/*     */ 
/*     */         
/* 146 */         workflowActionManager.setActionid(n);
/* 147 */         workflowActionManager.setWorkflowid(i);
/* 148 */         workflowActionManager.setNodeid(j);
/* 149 */         workflowActionManager.setActionorder(i1);
/* 150 */         workflowActionManager.setNodelinkid(m);
/* 151 */         workflowActionManager.setIspreoperator(k);
/* 152 */         workflowActionManager.setActionname(str1);
/* 153 */         workflowActionManager.setInterfaceid(str2);
/* 154 */         workflowActionManager.setInterfacetype(i2);
/* 155 */         workflowActionManager.setIsused(i3);
/*     */         
/* 157 */         n = workflowActionManager.doSaveWsAction();
/* 158 */         if (n > 0) {
/* 159 */           str = str + ("".equals(str) ? ("" + n) : ("," + n));
/*     */         }
/*     */       } 
/*     */     }
/*     */ 
/*     */     
/* 165 */     if (!"".equals(str)) {
/*     */       
/* 167 */       j = Util.getIntValue(paramHttpServletRequest.getParameter("nodeid"), 0);
/* 168 */       m = Util.getIntValue(paramHttpServletRequest.getParameter("linkid"), 0);
/* 169 */       k = Util.getIntValue(paramHttpServletRequest.getParameter("ispreoperator"), 0);
/*     */       
/* 171 */       workflowActionManager.setWorkflowid(i);
/* 172 */       workflowActionManager.setNodeid(j);
/* 173 */       workflowActionManager.setNodelinkid(m);
/* 174 */       workflowActionManager.setIspreoperator(k);
/*     */ 
/*     */       
/* 177 */       workflowActionManager.doDeleteWsActionsNoExists(str);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void saveActionNoDelete(HttpServletRequest paramHttpServletRequest) {
/* 186 */     int i = Util.getIntValue(paramHttpServletRequest.getParameter("workflowid"), 0);
/* 187 */     String[] arrayOfString1 = paramHttpServletRequest.getParameterValues("isused");
/* 188 */     String[] arrayOfString2 = paramHttpServletRequest.getParameterValues("actionid");
/* 189 */     String[] arrayOfString3 = paramHttpServletRequest.getParameterValues("type");
/* 190 */     String[] arrayOfString4 = paramHttpServletRequest.getParameterValues("actionnodeid");
/*     */     
/* 192 */     String[] arrayOfString5 = paramHttpServletRequest.getParameterValues("actionispreoperator");
/*     */     
/* 194 */     String[] arrayOfString6 = paramHttpServletRequest.getParameterValues("actionnodelinkid");
/* 195 */     String[] arrayOfString7 = paramHttpServletRequest.getParameterValues("actionname");
/* 196 */     String[] arrayOfString8 = paramHttpServletRequest.getParameterValues("actionorder");
/* 197 */     String[] arrayOfString9 = paramHttpServletRequest.getParameterValues("interfaceid");
/* 198 */     String[] arrayOfString10 = paramHttpServletRequest.getParameterValues("interfacetype");
/* 199 */     String str = "0";
/* 200 */     int j = 0;
/* 201 */     int k = 0;
/* 202 */     int m = 0;
/*     */     
/* 204 */     WorkflowActionManager workflowActionManager = new WorkflowActionManager();
/* 205 */     if (null != arrayOfString2 && arrayOfString2.length > 0)
/*     */     {
/* 207 */       for (byte b = 0; b < arrayOfString2.length; b++) {
/*     */         
/* 209 */         int n = Util.getIntValue(arrayOfString2[b], 0);
/* 210 */         j = Util.getIntValue(arrayOfString4[b], 0);
/* 211 */         k = Util.getIntValue(arrayOfString5[b], 0);
/* 212 */         m = Util.getIntValue(arrayOfString6[b], 0);
/* 213 */         String str1 = Util.null2String(arrayOfString7[b]);
/* 214 */         int i1 = Util.getIntValue(arrayOfString8[b], 0);
/* 215 */         String str2 = Util.null2String(arrayOfString9[b]);
/* 216 */         int i2 = Util.getIntValue(arrayOfString10[b], 0);
/* 217 */         int i3 = Util.getIntValue(arrayOfString1[b], 0);
/*     */ 
/*     */         
/* 220 */         workflowActionManager.setActionid(n);
/* 221 */         workflowActionManager.setWorkflowid(i);
/* 222 */         workflowActionManager.setNodeid(j);
/* 223 */         workflowActionManager.setActionorder(i1);
/* 224 */         workflowActionManager.setNodelinkid(m);
/* 225 */         workflowActionManager.setIspreoperator(k);
/* 226 */         workflowActionManager.setActionname(str1);
/* 227 */         workflowActionManager.setInterfaceid(str2);
/* 228 */         workflowActionManager.setInterfacetype(i2);
/* 229 */         workflowActionManager.setIsused(i3);
/*     */         
/* 231 */         n = workflowActionManager.doSaveWsAction();
/*     */       } 
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int doSaveWsAction() {
/* 242 */     ConnStatement connStatement = null;
/*     */     try {
/* 244 */       connStatement = new ConnStatement();
/* 245 */       String str = "";
/* 246 */       if (this.actionid <= 0) {
/* 247 */         str = "insert into workflowactionset(actionname, workflowid, nodeid, nodelinkid, ispreoperator, actionorder, interfaceid,interfacetype,isused,drawbackflag) values(?,?,?,?,?,?,?,?,?,?)";
/* 248 */         connStatement.setStatementSql(str);
/* 249 */         connStatement.setString(1, this.actionname);
/* 250 */         connStatement.setInt(2, this.workflowid);
/* 251 */         connStatement.setInt(3, this.nodeid);
/* 252 */         connStatement.setInt(4, this.nodelinkid);
/* 253 */         connStatement.setInt(5, this.ispreoperator);
/* 254 */         connStatement.setInt(6, this.actionorder);
/* 255 */         connStatement.setString(7, this.interfaceid);
/* 256 */         connStatement.setInt(8, this.interfacetype);
/* 257 */         connStatement.setInt(9, this.isused);
/* 258 */         connStatement.setInt(10, this.drawbackflag);
/* 259 */         connStatement.executeUpdate();
/*     */         
/* 261 */         str = "select max(id) as maxid from workflowactionset where workflowid=" + this.workflowid + " and nodeid=" + this.nodeid + " and nodelinkid=" + this.nodelinkid + " and ispreoperator=" + this.ispreoperator;
/* 262 */         connStatement.setStatementSql(str);
/* 263 */         connStatement.executeQuery();
/* 264 */         if (connStatement.next()) {
/* 265 */           this.actionid = Util.getIntValue(connStatement.getString("maxid"), 0);
/*     */         }
/*     */       } else {
/* 268 */         str = "update workflowactionset set actionname=?, workflowid=?, nodeid=?, nodelinkid=?, ispreoperator=?, actionorder=?, interfaceid=?, interfacetype=?,isused=?,drawbackflag = ? where id=?";
/* 269 */         connStatement.setStatementSql(str);
/* 270 */         connStatement.setString(1, this.actionname);
/* 271 */         connStatement.setInt(2, this.workflowid);
/* 272 */         connStatement.setInt(3, this.nodeid);
/* 273 */         connStatement.setInt(4, this.nodelinkid);
/* 274 */         connStatement.setInt(5, this.ispreoperator);
/* 275 */         connStatement.setInt(6, this.actionorder);
/* 276 */         connStatement.setString(7, this.interfaceid);
/* 277 */         connStatement.setInt(8, this.interfacetype);
/* 278 */         connStatement.setInt(9, this.isused);
/* 279 */         connStatement.setInt(10, this.drawbackflag);
/* 280 */         connStatement.setInt(11, this.actionid);
/* 281 */         connStatement.executeUpdate();
/*     */         
/* 283 */         if (this.interfacetype == 4 && this.actionname.indexOf("browser.") > -1) {
/* 284 */           String str1 = "update int_BrowserbaseInfo set w_enable = " + this.isused + " ,w_actionorder = " + this.actionorder + " where mark = '" + this.actionname + "'";
/* 285 */           setIsnewsap(1);
/* 286 */           RecordSet recordSet = new RecordSet();
/* 287 */           recordSet.execute(str1);
/*     */         } 
/*     */         
/* 290 */         if (this.interfaceid.equals("WorkflowToMode")) {
/* 291 */           str = "update mode_workflowtomodeset set isenable = " + this.isused + " where actionid=" + this.actionid;
/* 292 */           RecordSet recordSet = new RecordSet();
/* 293 */           recordSet.execute(str);
/*     */         } 
/*     */       } 
/*     */       
/*     */       try {
/* 298 */         connStatement.close();
/* 299 */       } catch (Exception exception) {}
/*     */ 
/*     */       
/* 302 */       BaseAction baseAction = new BaseAction();
/* 303 */       baseAction.setIsTriggerReject(this.isTriggerReject);
/* 304 */       baseAction.setWorkflowActionManager(this);
/* 305 */       baseAction.checkActionOnNodeOrLink(this.workflowid, this.nodeid, this.nodelinkid, this.ispreoperator, this.isnewsap);
/* 306 */     } catch (Exception exception) {
/* 307 */       writeLog(exception);
/* 308 */       this.actionid = -1;
/*     */     } finally {
/*     */       try {
/* 311 */         connStatement.close();
/* 312 */       } catch (Exception exception) {}
/*     */     } 
/*     */ 
/*     */     
/* 316 */     return this.actionid;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int doDeleteWsAction() {
/* 324 */     byte b = -1;
/*     */     try {
/* 326 */       RecordSet recordSet = new RecordSet();
/* 327 */       if (this.actionid > 0) {
/* 328 */         setActionid(this.actionid);
/* 329 */         String str = "delete from workflowactionset where id=" + this.actionid;
/* 330 */         recordSet.execute(str);
/* 331 */         BaseAction baseAction = new BaseAction();
/* 332 */         baseAction.setIsTriggerReject(this.isTriggerReject);
/* 333 */         baseAction.checkActionOnNodeOrLink(this.workflowid, this.nodeid, this.nodelinkid, this.ispreoperator, this.isnewsap);
/* 334 */         b = 1;
/*     */       }
/*     */     
/* 337 */     } catch (Exception exception) {
/* 338 */       writeLog(exception);
/* 339 */       b = 0;
/*     */     } 
/* 341 */     return b;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int doDeleteWsActionsNoExists(String paramString) {
/* 350 */     byte b = -1;
/*     */     try {
/* 352 */       RecordSet recordSet1 = new RecordSet();
/* 353 */       RecordSet recordSet2 = new RecordSet();
/* 354 */       if (!"".equals(paramString) && this.workflowid > 0) {
/*     */         
/* 356 */         String str = "select id from workflowactionset where id not in(" + paramString + ") and workflowid=" + this.workflowid;
/* 357 */         if (this.nodeid > 0) {
/* 358 */           if (this.ispreoperator != 1) {
/* 359 */             this.ispreoperator = 0;
/*     */           }
/* 361 */           str = str + " and nodeid=" + this.nodeid + " and ispreoperator=" + this.ispreoperator;
/* 362 */         } else if (this.nodelinkid > 0) {
/* 363 */           str = str + " and nodelinkid=" + this.nodelinkid;
/*     */         } 
/* 365 */         recordSet1.execute(str);
/* 366 */         while (recordSet1.next()) {
/*     */           
/* 368 */           int i = recordSet1.getInt(1);
/* 369 */           setActionid(i);
/* 370 */           ArrayList<ArrayList> arrayList = doSelectWsAction(Util.getIntValue("" + this.workflowid), this.nodeid, this.nodelinkid, this.ispreoperator);
/* 371 */           if (arrayList.size() > 0) {
/* 372 */             ArrayList<String> arrayList1 = arrayList.get(0);
/* 373 */             i = Util.getIntValue(arrayList1.get(0));
/* 374 */             this.actionname = Util.null2String(arrayList1.get(1));
/* 375 */             this.actionorder = Util.getIntValue(arrayList1.get(2), 0);
/* 376 */             this.interfaceid = Util.null2String(arrayList1.get(3));
/* 377 */             this.interfacetype = Util.getIntValue(arrayList1.get(4));
/*     */             
/* 379 */             this.workflowid = Util.getIntValue(arrayList1.get(5));
/* 380 */             this.nodeid = Util.getIntValue(Util.null2String(arrayList1.get(6)), 0);
/*     */             
/* 382 */             this.ispreoperator = Util.getIntValue(Util.null2String(arrayList1.get(7)), 0);
/*     */             
/* 384 */             this.nodelinkid = Util.getIntValue(Util.null2String(arrayList1.get(8)), 0);
/*     */           } 
/*     */           
/* 387 */           str = "update int_BrowserbaseInfo set w_enable =0 where mark =(select actionname from workflowactionset where id = " + this.actionid + ")";
/* 388 */           recordSet2.execute(str);
/* 389 */           str = "delete from workflowactionset where id=" + this.actionid;
/* 390 */           recordSet2.execute(str);
/*     */           
/* 392 */           str = "update mode_workflowtomodeset set isenable = 0 where actionid = " + this.actionid;
/* 393 */           recordSet2.execute(str);
/* 394 */           BaseAction baseAction = new BaseAction();
/* 395 */           baseAction.setIsTriggerReject(this.isTriggerReject);
/* 396 */           baseAction.checkActionOnNodeOrLink(this.workflowid, this.nodeid, this.nodelinkid, this.ispreoperator, this.isnewsap);
/* 397 */           b = 1;
/*     */         } 
/*     */       } 
/* 400 */     } catch (Exception exception) {
/* 401 */       writeLog(exception);
/* 402 */       b = 0;
/*     */     } 
/* 404 */     return b;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int doDeleteWsAction(int paramInt) {
/* 411 */     byte b = -1;
/*     */     try {
/* 413 */       RecordSet recordSet = new RecordSet();
/* 414 */       setActionid(paramInt);
/* 415 */       ArrayList<ArrayList> arrayList = doSelectWsAction(Util.getIntValue("" + this.workflowid), this.nodeid, this.nodelinkid, this.ispreoperator);
/* 416 */       if (arrayList.size() > 0) {
/* 417 */         ArrayList<String> arrayList1 = arrayList.get(0);
/* 418 */         paramInt = Util.getIntValue(arrayList1.get(0));
/* 419 */         this.actionname = Util.null2String(arrayList1.get(1));
/* 420 */         this.actionorder = Util.getIntValue(arrayList1.get(2), 0);
/* 421 */         this.interfaceid = Util.null2String(arrayList1.get(3));
/* 422 */         this.interfacetype = Util.getIntValue(arrayList1.get(4));
/*     */         
/* 424 */         this.workflowid = Util.getIntValue(arrayList1.get(5));
/* 425 */         this.nodeid = Util.getIntValue(Util.null2String(arrayList1.get(6)), 0);
/*     */         
/* 427 */         this.ispreoperator = Util.getIntValue(Util.null2String(arrayList1.get(7)), 0);
/*     */         
/* 429 */         this.nodelinkid = Util.getIntValue(Util.null2String(arrayList1.get(8)), 0);
/*     */       } 
/* 431 */       String str = "delete from workflowactionset where id=" + this.actionid;
/* 432 */       recordSet.execute(str);
/* 433 */       BaseAction baseAction = new BaseAction();
/* 434 */       baseAction.setIsTriggerReject(this.isTriggerReject);
/* 435 */       baseAction.checkActionOnNodeOrLink(this.workflowid, this.nodeid, this.nodelinkid, this.ispreoperator, this.isnewsap);
/* 436 */       b = 1;
/* 437 */     } catch (Exception exception) {
/* 438 */       writeLog(exception);
/* 439 */       b = 0;
/*     */     } 
/* 441 */     return b;
/*     */   }
/*     */   
/*     */   public void doDeleteByWorkflowid(int paramInt) {
/* 445 */     ArrayList arrayList = new ArrayList();
/* 446 */     BaseAction baseAction = new BaseAction();
/* 447 */     baseAction.setIsTriggerReject(this.isTriggerReject);
/*     */     try {
/* 449 */       RecordSet recordSet = new RecordSet();
/* 450 */       String str = "select * from workflowactionset where ";
/* 451 */       if (paramInt > 0) {
/* 452 */         str = str + " workflowid=" + paramInt;
/*     */         
/* 454 */         recordSet.execute(str);
/* 455 */         while (recordSet.next()) {
/* 456 */           int i = Util.getIntValue(recordSet.getString("id"));
/* 457 */           String str1 = Util.null2String(recordSet.getString("actionname"));
/* 458 */           int j = Util.getIntValue(recordSet.getString("actionorder"));
/* 459 */           String str2 = Util.null2String(recordSet.getString("interfaceid"));
/* 460 */           String str3 = Util.null2String(recordSet.getString("interfacetype"));
/* 461 */           int k = Util.getIntValue(Util.null2String(recordSet.getString("workflowid")));
/* 462 */           int m = Util.getIntValue(Util.null2String(recordSet.getString("nodeid")));
/* 463 */           int n = Util.getIntValue(Util.null2String(recordSet.getString("ispreoperator")));
/* 464 */           int i1 = Util.getIntValue(Util.null2String(recordSet.getString("nodelinkid")));
/*     */           
/* 466 */           baseAction.checkActionOnNodeOrLink(k, m, i1, n, this.isnewsap);
/*     */         } 
/* 468 */         str = "delete from workflowactionset where workflowid=" + paramInt;
/* 469 */         recordSet.executeSql(str);
/*     */       } 
/* 471 */     } catch (Exception exception) {
/* 472 */       writeLog(exception);
/*     */     } 
/*     */   }
/*     */   public ArrayList doSelectWsAction(int paramInt1, int paramInt2, int paramInt3, int paramInt4) {
/* 476 */     ArrayList<ArrayList<String>> arrayList = new ArrayList();
/*     */     try {
/* 478 */       RecordSet recordSet = new RecordSet();
/* 479 */       String str = "select * from workflowactionset where ";
/* 480 */       if (this.actionid > 0) {
/* 481 */         str = str + " id=" + this.actionid;
/*     */       }
/* 483 */       else if (paramInt2 > 0) {
/* 484 */         if (paramInt4 != 1) {
/* 485 */           paramInt4 = 0;
/*     */         }
/* 487 */         str = str + " workflowid=" + paramInt1 + " and nodeid=" + paramInt2 + " and ispreoperator=" + paramInt4;
/* 488 */       } else if (paramInt3 > 0) {
/* 489 */         str = str + " workflowid=" + paramInt1 + " and nodelinkid=" + paramInt3;
/*     */       } 
/*     */       
/* 492 */       recordSet.execute(str);
/* 493 */       while (recordSet.next()) {
/* 494 */         int i = Util.getIntValue(recordSet.getString("id"));
/* 495 */         String str1 = Util.null2String(recordSet.getString("actionname"));
/* 496 */         int j = Util.getIntValue(recordSet.getString("actionorder"));
/* 497 */         String str2 = Util.null2String(recordSet.getString("interfaceid"));
/* 498 */         String str3 = Util.null2String(recordSet.getString("interfacetype"));
/* 499 */         String str4 = Util.null2String(recordSet.getString("workflowid"));
/* 500 */         String str5 = Util.null2String(recordSet.getString("nodeid"));
/* 501 */         String str6 = Util.null2String(recordSet.getString("ispreoperator"));
/* 502 */         String str7 = Util.null2String(recordSet.getString("nodelinkid"));
/* 503 */         String str8 = Util.null2String(recordSet.getString("isused"));
/*     */         
/* 505 */         ArrayList<String> arrayList1 = new ArrayList();
/* 506 */         arrayList1.add("" + i);
/* 507 */         arrayList1.add(str1);
/* 508 */         arrayList1.add("" + j);
/* 509 */         arrayList1.add(str2);
/* 510 */         arrayList1.add(str3);
/* 511 */         arrayList1.add(str4);
/* 512 */         arrayList1.add(str5);
/* 513 */         arrayList1.add(str6);
/* 514 */         arrayList1.add(str7);
/* 515 */         arrayList1.add(str8);
/*     */         
/* 517 */         arrayList.add(arrayList1);
/*     */       } 
/* 519 */     } catch (Exception exception) {
/* 520 */       writeLog(exception);
/*     */     } 
/* 522 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void doDeleteWsActionByInterface(int paramInt1, int paramInt2, int paramInt3, int paramInt4, int paramInt5, String paramString) {
/* 534 */     ArrayList arrayList = new ArrayList();
/*     */     try {
/* 536 */       RecordSet recordSet = new RecordSet();
/* 537 */       String str = "select * from workflowactionset where ";
/* 538 */       if (this.actionid > 0) {
/* 539 */         str = str + " id=" + this.actionid;
/*     */       }
/* 541 */       else if (paramInt2 > 0) {
/* 542 */         if (paramInt4 != 1) {
/* 543 */           paramInt4 = 0;
/*     */         }
/* 545 */         str = str + " workflowid=" + paramInt1 + " and nodeid=" + paramInt2 + " and ispreoperator=" + paramInt4;
/* 546 */       } else if (paramInt3 > 0) {
/* 547 */         str = str + " workflowid=" + paramInt1 + " and nodelinkid=" + paramInt3;
/*     */       } 
/*     */       
/* 550 */       str = str + " and interfacetype=" + paramInt5 + " and interfaceid='" + paramString + "' ";
/*     */       
/* 552 */       recordSet.execute(str);
/* 553 */       while (recordSet.next()) {
/* 554 */         int i = Util.getIntValue(recordSet.getString("id"));
/* 555 */         setWorkflowid(paramInt1);
/* 556 */         setNodeid(paramInt2);
/* 557 */         setNodelinkid(paramInt3);
/* 558 */         setIspreoperator(paramInt4);
/* 559 */         doDeleteWsAction(i);
/*     */       } 
/* 561 */     } catch (Exception exception) {
/* 562 */       writeLog(exception);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static void main(String[] paramArrayOfString) {}
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getInterfaceid() {
/* 575 */     return this.interfaceid;
/*     */   }
/*     */   
/*     */   public void setInterfaceid(String paramString) {
/* 579 */     this.interfaceid = paramString;
/*     */   }
/*     */   
/*     */   public int getIsnewsap() {
/* 583 */     return this.isnewsap;
/*     */   }
/*     */   
/*     */   public void setIsnewsap(int paramInt) {
/* 587 */     this.isnewsap = paramInt;
/*     */   }
/*     */   
/*     */   public int getInterfacetype() {
/* 591 */     return this.interfacetype;
/*     */   }
/*     */   
/*     */   public void setInterfacetype(int paramInt) {
/* 595 */     this.interfacetype = paramInt;
/*     */   }
/*     */   public int getIsused() {
/* 598 */     return this.isused;
/*     */   }
/*     */   public void setIsused(int paramInt) {
/* 601 */     this.isused = paramInt;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/action/WorkflowActionManager.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */