/*     */ package weaver.workflow.action;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import weaver.conn.ConnStatement;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ 
/*     */ 
/*     */ public class WSActionManager
/*     */   extends BaseBean
/*     */ {
/*     */   private String operate;
/*     */   private int actionid;
/*     */   private String actionname;
/*     */   private int workflowid;
/*     */   private int nodeid;
/*     */   private int nodelinkid;
/*     */   private int ispreoperator;
/*     */   private int actionorder;
/*     */   private String wsurl;
/*     */   private String wsoperation;
/*     */   private String xmltext;
/*     */   private int rettype;
/*     */   private String retstr;
/*     */   private String inpara;
/*     */   private String webservicefrom;
/*     */   private int isnewsap;
/*  29 */   private String custominterface = "";
/*     */   
/*     */   public WSActionManager() {
/*  32 */     this.operate = "";
/*  33 */     this.actionid = 0;
/*  34 */     this.actionname = "";
/*  35 */     this.workflowid = 0;
/*  36 */     this.nodeid = 0;
/*  37 */     this.nodelinkid = 0;
/*  38 */     this.ispreoperator = 0;
/*  39 */     this.actionorder = 0;
/*  40 */     this.wsurl = "";
/*  41 */     this.wsoperation = "";
/*  42 */     this.xmltext = "";
/*  43 */     this.rettype = 0;
/*  44 */     this.retstr = "";
/*  45 */     this.inpara = "";
/*  46 */     this.webservicefrom = "";
/*  47 */     this.custominterface = "";
/*  48 */     this.isnewsap = 0;
/*     */   }
/*     */   public int getIsnewsap() {
/*  51 */     return this.isnewsap;
/*     */   }
/*     */   public void setIsnewsap(int paramInt) {
/*  54 */     this.isnewsap = paramInt;
/*     */   }
/*     */   public String getInpara() {
/*  57 */     return this.inpara;
/*     */   }
/*     */   public void setInpara(String paramString) {
/*  60 */     this.inpara = paramString;
/*     */   }
/*     */   public String getOperate() {
/*  63 */     return this.operate;
/*     */   }
/*     */   public void setOperate(String paramString) {
/*  66 */     this.operate = paramString;
/*     */   }
/*     */   public int getActionid() {
/*  69 */     return this.actionid;
/*     */   }
/*     */   public void setActionid(int paramInt) {
/*  72 */     this.actionid = paramInt;
/*     */   }
/*     */   public String getActionname() {
/*  75 */     return this.actionname;
/*     */   }
/*     */   public void setActionname(String paramString) {
/*  78 */     this.actionname = paramString;
/*     */   }
/*     */   public int getWorkflowid() {
/*  81 */     return this.workflowid;
/*     */   }
/*     */   public void setWorkflowid(int paramInt) {
/*  84 */     this.workflowid = paramInt;
/*     */   }
/*     */   public int getNodeid() {
/*  87 */     return this.nodeid;
/*     */   }
/*     */   public void setNodeid(int paramInt) {
/*  90 */     this.nodeid = paramInt;
/*     */   }
/*     */   public int getNodelinkid() {
/*  93 */     return this.nodelinkid;
/*     */   }
/*     */   public void setNodelinkid(int paramInt) {
/*  96 */     this.nodelinkid = paramInt;
/*     */   }
/*     */   public int getIspreoperator() {
/*  99 */     return this.ispreoperator;
/*     */   }
/*     */   public void setIspreoperator(int paramInt) {
/* 102 */     this.ispreoperator = paramInt;
/*     */   }
/*     */   public int getActionorder() {
/* 105 */     return this.actionorder;
/*     */   }
/*     */   public void setActionorder(int paramInt) {
/* 108 */     this.actionorder = paramInt;
/*     */   }
/*     */   public String getWsurl() {
/* 111 */     return this.wsurl;
/*     */   }
/*     */   public void setWsurl(String paramString) {
/* 114 */     this.wsurl = paramString;
/*     */   }
/*     */   public String getWsoperation() {
/* 117 */     return this.wsoperation;
/*     */   }
/*     */   public void setWsoperation(String paramString) {
/* 120 */     this.wsoperation = paramString;
/*     */   }
/*     */   public String getXmltext() {
/* 123 */     return this.xmltext;
/*     */   }
/*     */   public void setXmltext(String paramString) {
/* 126 */     this.xmltext = paramString;
/*     */   }
/*     */   public int getRettype() {
/* 129 */     return this.rettype;
/*     */   }
/*     */   public void setRettype(int paramInt) {
/* 132 */     this.rettype = paramInt;
/*     */   }
/*     */   public String getRetstr() {
/* 135 */     return this.retstr;
/*     */   }
/*     */   public void setRetstr(String paramString) {
/* 138 */     this.retstr = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int doSaveWsAction() {
/* 146 */     ConnStatement connStatement = null;
/*     */     try {
/* 148 */       connStatement = new ConnStatement();
/* 149 */       String str = "";
/* 150 */       if (this.actionid <= 0) {
/* 151 */         str = "insert into wsactionset(actionname, workflowid, nodeid, nodelinkid, ispreoperator, actionorder, wsurl, wsoperation, xmltext, rettype, retstr, inpara,webservicefrom,custominterface) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
/* 152 */         connStatement.setStatementSql(str);
/* 153 */         connStatement.setString(1, this.actionname);
/* 154 */         connStatement.setInt(2, this.workflowid);
/* 155 */         connStatement.setInt(3, this.nodeid);
/* 156 */         connStatement.setInt(4, this.nodelinkid);
/* 157 */         connStatement.setInt(5, this.ispreoperator);
/* 158 */         connStatement.setInt(6, this.actionorder);
/* 159 */         connStatement.setString(7, this.wsurl);
/* 160 */         connStatement.setString(8, this.wsoperation);
/* 161 */         connStatement.setString(9, this.xmltext);
/* 162 */         connStatement.setInt(10, this.rettype);
/* 163 */         connStatement.setString(11, this.retstr);
/* 164 */         connStatement.setString(12, this.inpara);
/* 165 */         connStatement.setInt(13, Util.getIntValue(this.webservicefrom, 1));
/* 166 */         connStatement.setInt(14, Util.getIntValue(this.custominterface, 1));
/* 167 */         connStatement.executeUpdate();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */         
/* 174 */         str = "select max(id) as maxid from wsactionset where workflowid=? and nodeid=? and nodelinkid=? and ispreoperator=? ";
/* 175 */         connStatement.setStatementSql(str);
/* 176 */         connStatement.setInt(1, this.workflowid);
/* 177 */         connStatement.setInt(2, this.nodeid);
/* 178 */         connStatement.setInt(3, this.nodelinkid);
/* 179 */         connStatement.setInt(4, this.ispreoperator);
/* 180 */         connStatement.executeQuery();
/* 181 */         while (connStatement.next()) {
/* 182 */           this.actionid = Util.getIntValue(connStatement.getString("maxid"), 0);
/*     */         }
/*     */       } else {
/* 185 */         str = "update wsactionset set actionname=?, actionorder=?, wsurl=?, wsoperation=?, xmltext=?, rettype=?, retstr=?, inpara=?, webservicefrom=?, custominterface=? where id=?";
/* 186 */         connStatement.setStatementSql(str);
/* 187 */         connStatement.setString(1, this.actionname);
/* 188 */         connStatement.setInt(2, this.actionorder);
/* 189 */         connStatement.setString(3, this.wsurl);
/* 190 */         connStatement.setString(4, this.wsoperation);
/* 191 */         connStatement.setString(5, this.xmltext);
/* 192 */         connStatement.setInt(6, this.rettype);
/* 193 */         connStatement.setString(7, this.retstr);
/* 194 */         connStatement.setString(8, this.inpara);
/* 195 */         connStatement.setInt(9, Util.getIntValue(this.webservicefrom, 1));
/* 196 */         connStatement.setString(10, this.custominterface);
/* 197 */         connStatement.setInt(11, this.actionid);
/* 198 */         connStatement.executeUpdate();
/*     */       } 
/*     */       try {
/* 201 */         connStatement.close();
/* 202 */       } catch (Exception exception) {}
/*     */ 
/*     */       
/* 205 */       BaseAction baseAction = new BaseAction();
/* 206 */       baseAction.checkActionOnNodeOrLink(this.workflowid, this.nodeid, this.nodelinkid, this.ispreoperator, this.isnewsap);
/* 207 */     } catch (Exception exception) {
/* 208 */       writeLog(exception);
/* 209 */       this.actionid = -1;
/*     */     } finally {
/*     */       try {
/* 212 */         connStatement.close();
/* 213 */       } catch (Exception exception) {}
/*     */     } 
/*     */ 
/*     */     
/* 217 */     return this.actionid;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int doDeleteWsAction() {
/* 225 */     byte b = -1;
/*     */     try {
/* 227 */       RecordSet recordSet = new RecordSet();
/* 228 */       String str = "delete from wsactionset where id=" + this.actionid;
/* 229 */       recordSet.execute(str);
/* 230 */       BaseAction baseAction = new BaseAction();
/* 231 */       baseAction.checkActionOnNodeOrLink(this.workflowid, this.nodeid, this.nodelinkid, this.ispreoperator, this.isnewsap);
/* 232 */       b = 1;
/* 233 */     } catch (Exception exception) {
/* 234 */       writeLog(exception);
/* 235 */       b = 0;
/*     */     } 
/* 237 */     return b;
/*     */   }
/*     */   
/*     */   public ArrayList doSelectWsAction(int paramInt1, int paramInt2, int paramInt3, int paramInt4) {
/* 241 */     ArrayList<ArrayList<String>> arrayList = new ArrayList();
/*     */     try {
/* 243 */       RecordSet recordSet = new RecordSet();
/* 244 */       String str = "select * from wsactionset where ";
/* 245 */       if (this.actionid > 0) {
/* 246 */         str = str + " id=" + this.actionid;
/*     */       }
/* 248 */       else if (paramInt2 > 0) {
/* 249 */         if (paramInt4 != 1) {
/* 250 */           paramInt4 = 0;
/*     */         }
/* 252 */         str = str + " workflowid=" + paramInt1 + " and nodeid=" + paramInt2 + " and ispreoperator=" + paramInt4;
/*     */       } else {
/* 254 */         str = str + " workflowid=" + paramInt1 + " and nodelinkid=" + paramInt3;
/*     */       } 
/*     */       
/* 257 */       recordSet.execute(str);
/* 258 */       while (recordSet.next()) {
/* 259 */         int i = Util.getIntValue(recordSet.getString("id"));
/* 260 */         String str1 = Util.null2String(recordSet.getString("actionname"));
/* 261 */         int j = Util.getIntValue(recordSet.getString("actionorder"));
/* 262 */         String str2 = Util.null2String(recordSet.getString("wsurl"));
/* 263 */         String str3 = Util.null2String(recordSet.getString("wsoperation"));
/* 264 */         String str4 = Util.null2String(recordSet.getString("xmltext"));
/* 265 */         int k = Util.getIntValue(recordSet.getString("rettype"));
/* 266 */         String str5 = Util.null2String(recordSet.getString("retstr"));
/* 267 */         String str6 = Util.null2String(recordSet.getString("inpara"));
/* 268 */         String str7 = Util.null2String(recordSet.getString("webservicefrom"));
/* 269 */         String str8 = Util.null2String(recordSet.getString("custominterface"));
/*     */         
/* 271 */         ArrayList<String> arrayList1 = new ArrayList();
/* 272 */         arrayList1.add("" + i);
/* 273 */         arrayList1.add(str1);
/* 274 */         arrayList1.add("" + j);
/* 275 */         arrayList1.add(str2);
/* 276 */         arrayList1.add(str3);
/* 277 */         arrayList1.add(str4);
/* 278 */         arrayList1.add("" + k);
/* 279 */         arrayList1.add(str5);
/* 280 */         arrayList1.add(str6);
/* 281 */         arrayList1.add(str7);
/* 282 */         arrayList1.add(str8);
/*     */         
/* 284 */         arrayList.add(arrayList1);
/*     */       } 
/* 286 */     } catch (Exception exception) {
/* 287 */       writeLog(exception);
/*     */     } 
/* 289 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static void main(String[] paramArrayOfString) {}
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getWebservicefrom() {
/* 301 */     return this.webservicefrom;
/*     */   }
/*     */   
/*     */   public void setWebservicefrom(String paramString) {
/* 305 */     this.webservicefrom = paramString;
/*     */   }
/*     */   
/*     */   public String getCustominterface() {
/* 309 */     return this.custominterface;
/*     */   }
/*     */   
/*     */   public void setCustominterface(String paramString) {
/* 313 */     this.custominterface = paramString;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/action/WSActionManager.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */