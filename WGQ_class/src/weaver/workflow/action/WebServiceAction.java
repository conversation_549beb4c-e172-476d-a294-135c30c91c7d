/*     */ package weaver.workflow.action;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Iterator;
/*     */ import java.util.Map;
/*     */ import java.util.Set;
/*     */ import java.util.regex.Matcher;
/*     */ import java.util.regex.Pattern;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.crm.Maint.CustomerInfoComInfo;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.SoapService;
/*     */ import weaver.general.StaticObj;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ import weaver.integration.logging.Logger;
/*     */ import weaver.integration.logging.LoggerFactory;
/*     */ import weaver.interfaces.workflow.action.Action;
/*     */ import weaver.soa.workflow.request.Cell;
/*     */ import weaver.soa.workflow.request.DetailTable;
/*     */ import weaver.soa.workflow.request.Property;
/*     */ import weaver.soa.workflow.request.RequestInfo;
/*     */ import weaver.soa.workflow.request.Row;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.workflow.field.SpecialFieldInfo;
/*     */ import weaver.workflow.request.RequestManager;
/*     */ import weaver.workflow.workflow.GetShowCondition;
/*     */ import weaver.workflow.workflow.WorkflowComInfo;
/*     */ import weaver.wsclient.bean.MethodBean;
/*     */ import weaver.wsclient.util.WSDLFacade;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class WebServiceAction
/*     */   extends BaseBean
/*     */   implements Action
/*     */ {
/*  40 */   private Logger newlog = LoggerFactory.getLogger(WebServiceAction.class);
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private int actionid;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private int workflowid;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private int nodeid;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private int nodelinkid;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private int ispreoperator;
/*     */ 
/*     */ 
/*     */   
/*     */   private String isNewMark;
/*     */ 
/*     */ 
/*     */   
/*     */   private String baseid;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getBaseid() {
/*  79 */     return this.baseid;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setBaseid(String paramString) {
/*  87 */     this.baseid = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getIsNewMark() {
/*  95 */     return this.isNewMark;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setIsNewMark(String paramString) {
/* 103 */     this.isNewMark = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getActionid() {
/* 111 */     return this.actionid;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setActionid(int paramInt) {
/* 119 */     this.actionid = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getWorkflowid() {
/* 127 */     return this.workflowid;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setWorkflowid(int paramInt) {
/* 135 */     this.workflowid = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getNodeid() {
/* 143 */     return this.nodeid;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setNodeid(int paramInt) {
/* 151 */     this.nodeid = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getNodelinkid() {
/* 159 */     return this.nodelinkid;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setNodelinkid(int paramInt) {
/* 167 */     this.nodelinkid = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getIspreoperator() {
/* 175 */     return this.ispreoperator;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setIspreoperator(int paramInt) {
/* 183 */     this.ispreoperator = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String execute(RequestInfo paramRequestInfo) {
/* 191 */     String str = "";
/*     */     try {
/* 193 */       String str1 = "select * from wsformactionset where id=" + this.actionid;
/* 194 */       RecordSet recordSet = new RecordSet();
/* 195 */       recordSet.execute(str1);
/* 196 */       if (recordSet.next()) {
/* 197 */         String str2 = Util.null2String(recordSet.getString("wsurl"));
/* 198 */         WSDLFacade wSDLFacade = new WSDLFacade();
/* 199 */         str2 = wSDLFacade.getWebserviceUrlFromDB(str2);
/* 200 */         String str3 = Util.null2String(recordSet.getString("wsoperation"));
/* 201 */         String str4 = Util.null2String(recordSet.getString("xmltext"));
/* 202 */         int i = Util.getIntValue(recordSet.getString("rettype"));
/* 203 */         String str5 = Util.null2String(recordSet.getString("retstr"));
/* 204 */         String str6 = Util.null2String(recordSet.getString("inpara"));
/* 205 */         String str7 = Util.null2String(recordSet.getString("webservicefrom"));
/* 206 */         String str8 = Util.null2String(recordSet.getString("custominterface"));
/* 207 */         String str9 = Util.null2String(recordSet.getString("wsnamespace"));
/*     */         
/* 209 */         if ("0".equals(str7)) {
/*     */           
/*     */           try {
/* 212 */             if (!str8.equals("")) {
/* 213 */               Action action = (Action)StaticObj.getServiceByFullname(str8, Action.class);
/* 214 */               str = action.execute(paramRequestInfo);
/* 215 */               if (!str.equals("1")) {
/* 216 */                 throw new Exception(str);
/*     */               }
/*     */             } 
/* 219 */           } catch (Exception exception) {
/* 220 */             this.newlog.error("报错", exception);
/* 221 */             throw new Exception("workflow interface action error:" + exception.getMessage());
/*     */           
/*     */           }
/*     */         
/*     */         }
/*     */         else {
/*     */           
/* 228 */           MethodBean methodBean = wSDLFacade.getWSMethodFromDB(str3);
/* 229 */           Map<String, Map<String, String>> map = wSDLFacade.getWSMethodParamValueFromDB(str3, "5", "" + this.actionid);
/* 230 */           str3 = methodBean.getMethodname();
/* 231 */           String str10 = methodBean.getMethodreturntype();
/* 232 */           Map<String, String> map1 = (Map)map.get("value");
/*     */           
/* 234 */           String str11 = analyzeXmltext(str4, paramRequestInfo);
/* 235 */           if (null != map1 && map1.size() > 0) {
/*     */             
/* 237 */             Set set = map1.keySet();
/* 238 */             for (Iterator<String> iterator = set.iterator(); iterator.hasNext(); ) {
/*     */               
/* 240 */               String str13 = Util.null2String(iterator.next());
/*     */               
/* 242 */               if (!"".equals(str13)) {
/*     */                 
/* 244 */                 String str14 = Util.null2String((String)map1.get(str13));
/* 245 */                 str14 = getFieldValue(str14, paramRequestInfo);
/* 246 */                 map1.put(str13, str14);
/*     */               } 
/*     */             } 
/* 249 */             if (map1.containsKey(str6))
/*     */             {
/* 251 */               map1.put(str6, str11);
/*     */             }
/*     */           } 
/* 254 */           map.put("value", map1);
/* 255 */           String str12 = doAxis(str2, str9, str3, "", "", map, str10);
/* 256 */           boolean bool = doRetOperate(str12, i, str5, paramRequestInfo);
/* 257 */           if (bool == true) {
/* 258 */             str = "1";
/*     */           } else {
/* 260 */             str = "errmsg:" + str12;
/* 261 */             this.newlog.error("WebService Error! requestid = " + paramRequestInfo.getRequestid());
/*     */           }
/*     */         
/*     */         } 
/*     */       } 
/* 266 */     } catch (Exception exception) {
/* 267 */       str = "";
/* 268 */       exception.printStackTrace();
/* 269 */       this.newlog.error("报错", exception);
/*     */     } 
/* 271 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String analyzeXmltext(String paramString, RequestInfo paramRequestInfo) {
/* 281 */     String str = "";
/*     */     try {
/* 283 */       String str1 = "";
/* 284 */       RecordSet recordSet = new RecordSet();
/* 285 */       WorkflowComInfo workflowComInfo = new WorkflowComInfo();
/* 286 */       int i = Util.getIntValue(paramRequestInfo.getRequestid());
/* 287 */       GetShowCondition getShowCondition = new GetShowCondition();
/* 288 */       int j = 7;
/*     */       try {
/* 290 */         j = paramRequestInfo.getRequestManager().getUser().getLanguage();
/* 291 */       } catch (Exception exception) {}
/*     */       
/* 293 */       str = changeSysField(paramString, paramRequestInfo);
/*     */       
/* 295 */       int k = Util.getIntValue(workflowComInfo.getFormId("" + this.workflowid), 0);
/* 296 */       int m = Util.getIntValue(workflowComInfo.getIsBill("" + this.workflowid), 0);
/* 297 */       String str2 = "";
/* 298 */       if (m == 0) {
/* 299 */         str1 = "select fd.id, fd.fieldname, fd.fieldhtmltype, fd.type, fd.fielddbtype from workflow_formdict fd left join workflow_formfield ff on ff.fieldid=fd.id where ff.formid=" + k + " order by fd.id";
/* 300 */         str2 = "workflow_form";
/*     */       } else {
/* 302 */         str1 = "select bf.id, bf.fieldname, bf.fieldhtmltype, bf.type, bf.fielddbtype from workflow_billfield bf where (viewtype=0 or viewtype is null) and billid=" + k + " order by bf.dsporder";
/* 303 */         recordSet.execute("select tablename from workflow_bill where id=" + k);
/* 304 */         if (recordSet.next()) {
/* 305 */           str2 = Util.null2String(recordSet.getString("tablename"));
/*     */         }
/*     */       } 
/* 308 */       ArrayList<String> arrayList1 = new ArrayList();
/* 309 */       ArrayList<String> arrayList2 = new ArrayList();
/* 310 */       ArrayList<String> arrayList3 = new ArrayList();
/* 311 */       ArrayList<String> arrayList4 = new ArrayList();
/* 312 */       ArrayList<String> arrayList5 = new ArrayList();
/* 313 */       recordSet.execute(str1);
/* 314 */       while (recordSet.next()) {
/* 315 */         int n = Util.getIntValue(recordSet.getString("id"), 0);
/* 316 */         String str3 = Util.null2String(recordSet.getString("fieldname"));
/* 317 */         int i1 = Util.getIntValue(recordSet.getString("fieldhtmltype"), 0);
/* 318 */         int i2 = Util.getIntValue(recordSet.getString("type"), 0);
/* 319 */         String str4 = Util.null2String(recordSet.getString("fielddbtype"));
/* 320 */         if ("".equals(str3) || n <= 0) {
/*     */           continue;
/*     */         }
/* 323 */         arrayList1.add("" + n);
/* 324 */         arrayList2.add(str3);
/* 325 */         arrayList3.add("" + i1);
/* 326 */         arrayList4.add("" + i2);
/* 327 */         arrayList5.add("" + str4);
/*     */       } 
/* 329 */       str1 = "select * from " + str2 + " where requestid=" + i;
/* 330 */       recordSet.execute(str1);
/* 331 */       if (recordSet.next()) {
/* 332 */         for (byte b = 0; b < arrayList1.size(); b++) {
/* 333 */           int n = Util.getIntValue(arrayList1.get(b), 0);
/* 334 */           String str3 = Util.null2String(arrayList2.get(b));
/* 335 */           int i1 = Util.getIntValue(arrayList3.get(b), 0);
/* 336 */           int i2 = Util.getIntValue(arrayList4.get(b), 0);
/* 337 */           String str4 = Util.null2String(arrayList5.get(b));
/* 338 */           int i3 = str.indexOf("$field" + n + "$");
/* 339 */           if (i3 >= 0) {
/* 340 */             String str5 = Util.null2String(recordSet.getString(str3));
/* 341 */             String str6 = "";
/* 342 */             if (i1 == 5) {
/* 343 */               str6 = getShowCondition.getShowCN("" + i1, "" + n, str5, "" + m, str4);
/* 344 */             } else if (i1 == 3) {
/* 345 */               str6 = getShowCondition.getShowCN("" + i1, "" + i2, str5, "" + m, str4);
/* 346 */             } else if (i1 == 4) {
/* 347 */               if ("1".equals(str5)) {
/* 348 */                 str6 = SystemEnv.getHtmlLabelName(1426, j);
/*     */               } else {
/* 350 */                 str6 = SystemEnv.getHtmlLabelName(22906, j);
/*     */               } 
/* 352 */             } else if (i1 == 6) {
/* 353 */               str6 = getShowCondition.getShowCN("3", "37", str5, "" + m, "varchar(4000)");
/* 354 */             } else if (i1 == 7) {
/* 355 */               SpecialFieldInfo specialFieldInfo = new SpecialFieldInfo();
/* 356 */               HashMap hashMap = specialFieldInfo.getFormSpecialField();
/* 357 */               if (m == 0) {
/* 358 */                 str6 = str6 + Util.null2String((String)hashMap.get(n + "_0"));
/*     */               } else {
/* 360 */                 str6 = str6 + Util.null2String((String)hashMap.get(n + "_1"));
/*     */               } 
/* 362 */             } else if (i1 == 1 || i1 == 2) {
/* 363 */               str6 = str5;
/*     */             } 
/* 365 */             str6 = replaceSpecialWord4Ws(str6);
/* 366 */             if (str6.indexOf("\\") > -1) {
/* 367 */               str6 = str6.replace("\\", "\\\\");
/*     */             }
/* 369 */             str = str.replaceAll("\\$field" + n + "\\$", str6);
/*     */           } 
/*     */         } 
/*     */       }
/*     */ 
/*     */       
/* 375 */       byte b1 = 0;
/* 376 */       ArrayList<String> arrayList6 = new ArrayList();
/* 377 */       ArrayList<String> arrayList7 = new ArrayList();
/* 378 */       if (m == 0) {
/* 379 */         str1 = "select distinct groupid from workflow_formfield where formid=" + k + " and isdetail='1' order by groupid";
/*     */       } else {
/* 381 */         str1 = "select tablename as groupid from Workflow_billdetailtable where billid=" + k + " order by orderid";
/*     */       } 
/* 383 */       recordSet.execute(str1);
/* 384 */       while (recordSet.next()) {
/* 385 */         String str3 = "";
/* 386 */         if (m == 0) {
/* 387 */           str3 = "" + Util.getIntValue(recordSet.getString("groupid"), 0);
/*     */         } else {
/* 389 */           str3 = Util.null2String(recordSet.getString("groupid"));
/*     */         } 
/* 391 */         b1++;
/* 392 */         arrayList6.add("" + b1);
/* 393 */         arrayList7.add(str3);
/*     */       } 
/* 395 */       for (byte b2 = 0; b2 < arrayList6.size(); b2++) {
/* 396 */         String str3 = Util.null2String(arrayList7.get(b2));
/* 397 */         int n = Util.getIntValue(arrayList6.get(b2));
/* 398 */         int i1 = str.indexOf("$grouphead" + n + "$");
/* 399 */         int i2 = str.indexOf("$grouptail" + n + "$");
/* 400 */         while (i1 >= 0 && i2 >= 0 && i1 < i2) {
/* 401 */           String str4 = "";
/* 402 */           String str5 = "";
/*     */           try {
/* 404 */             str4 = "";
/* 405 */             str5 = "";
/* 406 */             str4 = str.substring(0, i1);
/* 407 */             String str6 = str.substring(i1 + ("$grouptail" + n + "$").length(), i2);
/* 408 */             str5 = str.substring(i2 + ("$grouptail" + n + "$").length());
/* 409 */             ArrayList<String> arrayList8 = new ArrayList();
/* 410 */             ArrayList<String> arrayList9 = new ArrayList();
/* 411 */             ArrayList<String> arrayList10 = new ArrayList();
/* 412 */             ArrayList<String> arrayList11 = new ArrayList();
/* 413 */             ArrayList<String> arrayList12 = new ArrayList();
/* 414 */             if (m == 0) {
/* 415 */               str1 = "select fd.id, fd.fieldname, fd.fieldhtmltype, fd.type, fd.fielddbtype from workflow_formdictdetail fd left join workflow_formfield ff on ff.fieldid=fd.id where ff.groupid=" + str3 + " and ff.formid=" + k + " order by fd.id";
/*     */             } else {
/* 417 */               str1 = "select bf.id, bf.fieldname, bf.fieldhtmltype, bf.type, bf.fielddbtype from workflow_billfield bf where viewtype=1 and billid=" + k + " and detailtable='" + str3 + "' order by bf.dsporder";
/*     */             } 
/* 419 */             RecordSet recordSet1 = new RecordSet();
/* 420 */             recordSet1.execute(str1);
/* 421 */             while (recordSet1.next()) {
/* 422 */               int i3 = Util.getIntValue(recordSet1.getString("id"), 0);
/* 423 */               String str8 = Util.null2String(recordSet1.getString("fieldname"));
/* 424 */               int i4 = Util.getIntValue(recordSet1.getString("fieldhtmltype"), 0);
/* 425 */               int i5 = Util.getIntValue(recordSet1.getString("type"), 0);
/* 426 */               String str9 = Util.null2String(recordSet1.getString("fielddbtype"));
/* 427 */               if ("".equals(str8) || i3 <= 0) {
/*     */                 continue;
/*     */               }
/* 430 */               arrayList8.add("" + i3);
/* 431 */               arrayList9.add(str8);
/* 432 */               arrayList10.add("" + i4);
/* 433 */               arrayList11.add("" + i5);
/* 434 */               arrayList12.add(str9);
/*     */             } 
/*     */ 
/*     */ 
/*     */             
/* 439 */             if (m == 0) {
/* 440 */               str1 = "select * from workflow_formdetail where requestid=" + i + " and groupid=" + str3;
/*     */             } else {
/* 442 */               String str8 = "";
/* 443 */               str1 = "select detailkeyfield from workflow_bill where id=" + k;
/* 444 */               recordSet.execute(str1);
/* 445 */               if (recordSet.next()) {
/* 446 */                 str8 = Util.null2String(recordSet.getString("detailkeyfield"));
/* 447 */                 if ("".equals(str8)) {
/* 448 */                   str = str4 + str5;
/* 449 */                   i1 = str.indexOf("$grouphead" + n + "$");
/* 450 */                   i2 = str.indexOf("$grouptail" + n + "$");
/*     */                   continue;
/*     */                 } 
/*     */               } 
/* 454 */               str1 = "select * from " + str3 + " where " + str8 + "=(select id from " + str2 + " where requestid=" + i + ")";
/*     */             } 
/* 456 */             recordSet.execute(str1);
/* 457 */             String str7 = "";
/* 458 */             while (recordSet.next()) {
/* 459 */               String str8 = str6;
/* 460 */               for (byte b = 0; b < arrayList8.size(); b++) {
/* 461 */                 int i3 = Util.getIntValue(arrayList8.get(b), 0);
/* 462 */                 String str9 = Util.null2String(arrayList9.get(b));
/* 463 */                 int i4 = Util.getIntValue(arrayList10.get(b), 0);
/* 464 */                 int i5 = Util.getIntValue(arrayList11.get(b), 0);
/* 465 */                 String str10 = Util.null2String(arrayList12.get(b));
/* 466 */                 int i6 = str.indexOf("$field" + i3 + "$");
/* 467 */                 if (i6 >= 0) {
/* 468 */                   String str11 = Util.null2String(recordSet.getString(str9));
/* 469 */                   String str12 = "";
/* 470 */                   if (i4 == 5) {
/* 471 */                     str12 = getShowCondition.getShowCN("" + i4, "" + i3, str11, "" + m, str10);
/* 472 */                   } else if (i4 == 3) {
/* 473 */                     str12 = getShowCondition.getShowCN("" + i4, "" + i5, str11, "" + m, str10);
/* 474 */                   } else if (i4 == 4) {
/* 475 */                     if ("1".equals(str11)) {
/* 476 */                       str12 = SystemEnv.getHtmlLabelName(1426, j);
/*     */                     } else {
/* 478 */                       str12 = SystemEnv.getHtmlLabelName(22906, j);
/*     */                     } 
/* 480 */                   } else if (i4 == 6) {
/* 481 */                     str12 = getShowCondition.getShowCN("3", "37", str11, "" + m, "varchar(4000)");
/* 482 */                   } else if (i4 == 7) {
/* 483 */                     SpecialFieldInfo specialFieldInfo = new SpecialFieldInfo();
/* 484 */                     HashMap hashMap = specialFieldInfo.getFormSpecialField();
/* 485 */                     if (m == 0) {
/* 486 */                       str12 = str12 + Util.null2String((String)hashMap.get(i3 + "_0"));
/*     */                     } else {
/* 488 */                       str12 = str12 + Util.null2String((String)hashMap.get(i3 + "_1"));
/*     */                     } 
/* 490 */                   } else if (i4 == 1 || i4 == 2) {
/* 491 */                     str12 = str11;
/*     */                   } 
/* 493 */                   str12 = replaceSpecialWord4Ws(str12);
/* 494 */                   if (str12.indexOf("\\") > -1) {
/* 495 */                     str12 = str12.replace("\\", "\\\\");
/*     */                   }
/* 497 */                   str8 = str8.replaceAll("\\$field" + i3 + "\\$", str12);
/*     */                 } 
/*     */               } 
/* 500 */               str7 = str7 + str8;
/*     */             } 
/* 502 */             str = str4 + str7 + str5;
/* 503 */             i1 = str.indexOf("$grouphead" + n + "$");
/* 504 */             i2 = str.indexOf("$grouptail" + n + "$");
/* 505 */           } catch (Exception exception) {
/* 506 */             str = str4 + str5;
/* 507 */             i1 = str.indexOf("$grouphead" + n + "$");
/* 508 */             i2 = str.indexOf("$grouptail" + n + "$");
/*     */           }
/*     */         
/*     */         } 
/*     */       } 
/* 513 */     } catch (Exception exception) {
/* 514 */       this.newlog.error("报错", exception);
/* 515 */       str = "";
/*     */     } 
/* 517 */     this.newlog.error(str);
/* 518 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String replaceString(String paramString1, String paramString2, String paramString3) {
/* 529 */     if (paramString1 == null) return null; 
/* 530 */     if (paramString2 == null || paramString2.equals("") || paramString3 == null) return paramString1; 
/* 531 */     String str = paramString1;
/* 532 */     while (str.indexOf(paramString2) >= 0) {
/* 533 */       int i = str.indexOf(paramString2);
/* 534 */       str = str.substring(0, i) + paramString3 + str.substring(i + paramString2.length());
/*     */     } 
/* 536 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private boolean hasWorkflowFormFieldParams(String paramString) {
/* 545 */     Pattern pattern = Pattern.compile("(\\$[a-zA-Z][a-zA-Z0-9_]*\\$)");
/* 546 */     Matcher matcher = pattern.matcher(paramString);
/* 547 */     return matcher.find();
/*     */   }
/*     */   private String fieldToLowerCase(String paramString) {
/* 550 */     String str = "\\$(.*?)\\$";
/* 551 */     ArrayList<String> arrayList = new ArrayList();
/* 552 */     Pattern pattern = Pattern.compile(str);
/* 553 */     Matcher matcher = pattern.matcher(paramString);
/* 554 */     while (matcher.find()) {
/* 555 */       byte b = 1;
/* 556 */       arrayList.add(matcher.group(b));
/* 557 */       b++;
/*     */     } 
/* 559 */     for (String str1 : arrayList) {
/* 560 */       if (!str1.equals(str1.toLowerCase()))
/* 561 */         paramString = replaceString(paramString, str1, str1.toLowerCase()); 
/*     */     } 
/* 563 */     return paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getFieldValue(String paramString, RequestInfo paramRequestInfo) {
/* 574 */     paramString = fieldToLowerCase(paramString);
/* 575 */     String str = paramString;
/*     */     
/* 577 */     if (!hasWorkflowFormFieldParams(paramString)) {
/* 578 */       return str;
/*     */     }
/*     */     try {
/* 581 */       RequestManager requestManager = paramRequestInfo.getRequestManager();
/* 582 */       ResourceComInfo resourceComInfo = new ResourceComInfo();
/* 583 */       CustomerInfoComInfo customerInfoComInfo = new CustomerInfoComInfo();
/* 584 */       WorkflowComInfo workflowComInfo = new WorkflowComInfo();
/* 585 */       String str1 = "";
/* 586 */       RecordSet recordSet = new RecordSet();
/*     */       
/* 588 */       if (str.indexOf("$requestname$") >= 0) {
/* 589 */         str = replaceString(str, "$requestname$", requestManager.getRequestname());
/*     */       }
/* 591 */       if (str.indexOf("$requestid$") >= 0)
/*     */       {
/*     */         
/* 594 */         str = replaceString(str, "$requestid$", "" + requestManager.getRequestid());
/*     */       }
/*     */       
/* 597 */       if (str.indexOf("$creater$") >= 0) {
/*     */         
/* 599 */         String str2 = "";
/* 600 */         int i = requestManager.getCreatertype();
/* 601 */         if (i == 0) {
/* 602 */           str2 = resourceComInfo.getLastname("" + requestManager.getCreater());
/* 603 */         } else if (i == 1) {
/* 604 */           str2 = customerInfoComInfo.getCustomerInfoname("" + requestManager.getCreater());
/*     */         } 
/*     */         
/* 607 */         str = replaceString(str, "$creater$", "" + requestManager.getCreater());
/*     */       } 
/*     */       
/* 610 */       if (str.indexOf("$createdate$") >= 0)
/*     */       {
/*     */         
/* 613 */         str = replaceString(str, "$createdate$", "" + requestManager.getCreatedate());
/*     */       }
/*     */       
/* 616 */       if (str.indexOf("$createtime$") >= 0)
/*     */       {
/*     */         
/* 619 */         str = replaceString(str, "$createtime$", "" + requestManager.getCreatetime());
/*     */       }
/*     */       
/* 622 */       if (str.indexOf("$workflowname$") >= 0)
/*     */       {
/*     */         
/* 625 */         str = replaceString(str, "$workflowname$", "" + workflowComInfo.getWorkflowname("" + requestManager.getWorkflowid()));
/*     */       }
/*     */ 
/*     */       
/* 629 */       if (str.indexOf("$currentuse$") >= 0) {
/*     */         
/* 631 */         String str2 = "";
/* 632 */         int i = requestManager.getUserId();
/* 633 */         int j = requestManager.getUserType();
/* 634 */         if (j == 0) {
/* 635 */           str2 = resourceComInfo.getLastname("" + i);
/* 636 */         } else if (j == 1) {
/* 637 */           str2 = customerInfoComInfo.getCustomerInfoname("" + i);
/*     */         } 
/*     */         
/* 640 */         str = replaceString(str, "$currentuse$", str2);
/*     */       } 
/*     */       
/* 643 */       if (str.indexOf("$currentnode$") >= 0) {
/*     */         
/* 645 */         String str2 = "";
/* 646 */         int i = requestManager.getNodeid();
/* 647 */         str1 = "select nodename from workflow_nodebase where id=" + i;
/* 648 */         recordSet.execute(str1);
/* 649 */         if (recordSet.next()) {
/* 650 */           str2 = Util.null2String(recordSet.getString("nodename"));
/*     */         }
/*     */         
/* 653 */         str = replaceString(str, "$currentnode$", str2);
/*     */       } 
/* 655 */       Property[] arrayOfProperty = paramRequestInfo.getMainTableInfo().getProperty();
/* 656 */       for (byte b = 0; b < arrayOfProperty.length; b++) {
/* 657 */         String str2 = arrayOfProperty[b].getName().toLowerCase();
/* 658 */         String str3 = Util.null2String(arrayOfProperty[b].getValue());
/*     */         
/* 660 */         if (str.indexOf("$" + str2 + "$") >= 0)
/*     */         {
/* 662 */           str = replaceString(str, "$" + str2 + "$", str3);
/*     */         }
/*     */       } 
/* 665 */       DetailTable[] arrayOfDetailTable = paramRequestInfo.getDetailTableInfo().getDetailTable();
/* 666 */       if (arrayOfDetailTable.length > 0) {
/* 667 */         for (byte b1 = 0; b1 < arrayOfDetailTable.length; b1++) {
/* 668 */           DetailTable detailTable = arrayOfDetailTable[b1];
/* 669 */           Row[] arrayOfRow = detailTable.getRow();
/* 670 */           String[] arrayOfString = new String[2];
/* 671 */           ArrayList arrayList = new ArrayList();
/* 672 */           for (byte b2 = 0; b2 < arrayOfRow.length; b2++) {
/* 673 */             Row row = arrayOfRow[b2];
/* 674 */             Cell[] arrayOfCell = row.getCell();
/* 675 */             for (byte b3 = 0; b3 < arrayOfCell.length; b3++) {
/* 676 */               Cell cell = arrayOfCell[b3];
/* 677 */               String str2 = cell.getName().toLowerCase();
/* 678 */               String str3 = cell.getValue();
/* 679 */               if (str.indexOf("$detail_" + str2 + "$") >= 0) {
/* 680 */                 str = replaceString(str, "$detail_" + str2 + "$", str3);
/*     */               }
/*     */             } 
/*     */           } 
/*     */         } 
/*     */       }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 694 */       if (str == null || str.contains("$detail_")) {
/* 695 */         str = "";
/*     */       }
/* 697 */     } catch (Exception exception) {
/* 698 */       str = paramString;
/* 699 */       if (str == null || str.contains("$detail_")) {
/* 700 */         str = "";
/*     */       }
/* 702 */       this.newlog.error("报错", exception);
/*     */     } 
/* 704 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String changeSysField(String paramString, RequestInfo paramRequestInfo) {
/* 714 */     String str = paramString;
/*     */     try {
/* 716 */       RequestManager requestManager = paramRequestInfo.getRequestManager();
/* 717 */       ResourceComInfo resourceComInfo = new ResourceComInfo();
/* 718 */       CustomerInfoComInfo customerInfoComInfo = new CustomerInfoComInfo();
/* 719 */       WorkflowComInfo workflowComInfo = new WorkflowComInfo();
/* 720 */       String str1 = "";
/* 721 */       RecordSet recordSet = new RecordSet();
/*     */ 
/*     */       
/* 724 */       ArrayList<String> arrayList1 = new ArrayList();
/* 725 */       ArrayList<String> arrayList2 = new ArrayList();
/*     */       
/* 727 */       arrayList1.add("\\$requestname\\$");
/* 728 */       arrayList2.add(requestManager.getRequestname());
/*     */       
/* 730 */       arrayList1.add("\\$requestid\\$");
/* 731 */       arrayList2.add("" + requestManager.getRequestid());
/*     */       
/* 733 */       arrayList1.add("\\$creater\\$");
/* 734 */       String str2 = "";
/* 735 */       int i = requestManager.getCreatertype();
/* 736 */       if (i == 0) {
/* 737 */         str2 = resourceComInfo.getLastname("" + requestManager.getCreater());
/* 738 */       } else if (i == 1) {
/* 739 */         str2 = customerInfoComInfo.getCustomerInfoname("" + requestManager.getCreater());
/*     */       } 
/* 741 */       arrayList2.add(str2);
/*     */       
/* 743 */       arrayList1.add("\\$createdate\\$");
/* 744 */       arrayList2.add(requestManager.getCreatedate());
/*     */       
/* 746 */       arrayList1.add("\\$createtime\\$");
/* 747 */       arrayList2.add(requestManager.getCreatetime());
/*     */       
/* 749 */       arrayList1.add("\\$workflowname\\$");
/* 750 */       arrayList2.add(workflowComInfo.getWorkflowname("" + requestManager.getWorkflowid()));
/*     */       
/* 752 */       arrayList1.add("\\$currentuse\\$");
/* 753 */       String str3 = "";
/* 754 */       int j = requestManager.getUserId();
/* 755 */       int k = requestManager.getUserType();
/* 756 */       if (k == 0) {
/* 757 */         str3 = resourceComInfo.getLastname("" + j);
/* 758 */       } else if (k == 1) {
/* 759 */         str3 = customerInfoComInfo.getCustomerInfoname("" + j);
/*     */       } 
/* 761 */       arrayList2.add(str3);
/*     */       
/* 763 */       arrayList1.add("\\$currentnode\\$");
/* 764 */       String str4 = "";
/* 765 */       int m = requestManager.getNodeid();
/* 766 */       str1 = "select nodename from workflow_nodebase where id=" + m;
/* 767 */       recordSet.execute(str1);
/* 768 */       if (recordSet.next()) {
/* 769 */         str4 = Util.null2String(recordSet.getString("nodename"));
/*     */       }
/* 771 */       arrayList2.add(str4);
/*     */       
/* 773 */       for (byte b = 0; b < arrayList1.size(); b++) {
/* 774 */         String str5 = Util.null2String(arrayList1.get(b));
/* 775 */         String str6 = Util.null2String(arrayList2.get(b));
/* 776 */         str = str.replaceAll(str5, str6);
/*     */       } 
/* 778 */     } catch (Exception exception) {
/* 779 */       str = paramString;
/* 780 */       this.newlog.error("报错", exception);
/*     */     } 
/* 782 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean doRetOperate(String paramString1, int paramInt, String paramString2, RequestInfo paramRequestInfo) {
/* 794 */     boolean bool = false;
/*     */     try {
/* 796 */       if (paramInt == 0) {
/* 797 */         if ("".equals(paramString2.trim()) || paramString1.trim().equalsIgnoreCase(paramString2.trim())) {
/* 798 */           bool = true;
/*     */         }
/* 800 */       } else if (paramInt == 1) {
/* 801 */         paramString2 = changeSysField(paramString2, paramRequestInfo);
/* 802 */         paramString2 = analyzeXmltext(paramString2, paramRequestInfo);
/* 803 */         paramString2 = paramString2.replaceAll("\\$retstr\\$", paramString1);
/* 804 */         RecordSet recordSet = new RecordSet();
/* 805 */         bool = recordSet.execute(paramString2);
/*     */       } 
/* 807 */     } catch (Exception exception) {
/* 808 */       this.newlog.error("报错", exception);
/* 809 */       bool = false;
/*     */     } 
/* 811 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String doAxis(String paramString1, String paramString2, String paramString3, String paramString4, String paramString5, Map paramMap, String paramString6) {
/* 826 */     String str = "";
/* 827 */     if (paramString1 == null || "".equals(paramString1) || paramString3 == null || "".equals(paramString3)) {
/* 828 */       this.newlog.error("参数有误!");
/* 829 */       return str;
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*     */     try {
/* 863 */       str = SoapService.serviceSend(paramString1, paramString2, paramString3, paramMap, paramString6);
/*     */     
/*     */     }
/* 866 */     catch (Exception exception) {
/* 867 */       this.newlog.error("报错", exception);
/* 868 */       this.newlog.error("Service 获取Call对象失败!");
/*     */     } 
/* 870 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String replaceSpecialWord4Ws(String paramString) {
/* 879 */     String str = Util.null2String(paramString);
/* 880 */     str = str.replaceAll("&", "&amp;");
/* 881 */     str = str.replaceAll(">", "&gt;");
/* 882 */     str = str.replaceAll("<", "&lt;");
/* 883 */     return str;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/action/WebServiceAction.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */