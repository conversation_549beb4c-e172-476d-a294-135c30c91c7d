/*     */ package weaver.workflow.action;
/*     */ 
/*     */ import com.alibaba.fastjson.JSONArray;
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import com.alibaba.fastjson.JSONPath;
/*     */ import com.api.integration.util.JavaUtil;
/*     */ import com.weaver.esb.client.EsbClient;
/*     */ import com.weaver.esb.spi.EsbService;
/*     */ import com.weaver.file.Prop;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import net.sf.json.JSONObject;
/*     */ import org.apache.commons.lang3.StringUtils;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.integration.logging.Logger;
/*     */ import weaver.integration.logging.LoggerFactory;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.workflow.dmlaction.commands.bases.FieldBase;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class ActionDataSource
/*     */ {
/*  40 */   private static Logger log = LoggerFactory.getLogger(ActionDataSource.class);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<Map<String, String>> getActionTable(User paramUser, Map<String, String> paramMap, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/*  52 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*  53 */     RecordSet recordSet1 = new RecordSet();
/*  54 */     RecordSet recordSet2 = new RecordSet();
/*     */     
/*  56 */     int i = Util.getIntValue(paramMap.get("actiontype"), -1);
/*  57 */     String str1 = Util.null2String(paramMap.get("formid"));
/*  58 */     String str2 = Util.null2String(paramMap.get("actionname"));
/*  59 */     String str3 = "";
/*  60 */     if (i == 1 || i == -1) {
/*  61 */       String str = "";
/*  62 */       if (!"".equals(str2)) {
/*  63 */         str = str + " where dmlactionname like '%" + str2 + "%'";
/*     */       }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/*  70 */       str3 = "select * from (select d.id,      d.dmlactionname as actionname,d.typename,      d.formid,      d.isbill,      d.datasourceid,      '1' as fromtype,      '" + SystemEnv.getHtmlLabelName(82986, Util.getIntValue("" + paramUser.getLanguage(), 7)) + "' as fromtypename from formactionset d " + str + ") r where r.formid=" + str1 + " order by id";
/*     */ 
/*     */ 
/*     */       
/*  74 */       recordSet1.executeSql(str3);
/*  75 */       while (recordSet1.next()) {
/*  76 */         HashMap<Object, Object> hashMap = new HashMap<>();
/*  77 */         hashMap.put("id", recordSet1.getString("id"));
/*  78 */         hashMap.put("actionname", recordSet1.getString("actionname"));
/*  79 */         hashMap.put("fromtypename", recordSet1.getString("fromtypename"));
/*  80 */         hashMap.put("fromtype", recordSet1.getString("fromtype"));
/*  81 */         arrayList.add(hashMap);
/*     */       } 
/*     */     } 
/*  84 */     if (i == 2 || i == -1) {
/*  85 */       String str = "";
/*  86 */       if (!"".equals(str2)) {
/*  87 */         str = str + " where actionname like '%" + str2 + "%'";
/*     */       }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/*  94 */       str3 = "select * from (select s.id,       s.actionname,s.typename,       s.formid,       s.isbill,       '' as datasourceid,       '2' as fromtype,      case when webservicefrom=1 then '" + SystemEnv.getHtmlLabelName(82987, Util.getIntValue("" + paramUser.getLanguage(), 7)) + "' else '" + SystemEnv.getHtmlLabelName(82988, Util.getIntValue("" + paramUser.getLanguage(), 7)) + "' end as fromtypename  from wsformactionset s " + str + ") r where r.formid=" + str1 + " order by id";
/*     */ 
/*     */ 
/*     */       
/*  98 */       recordSet1.executeSql(str3);
/*  99 */       while (recordSet1.next()) {
/* 100 */         HashMap<Object, Object> hashMap = new HashMap<>();
/* 101 */         hashMap.put("id", recordSet1.getString("id"));
/* 102 */         hashMap.put("actionname", recordSet1.getString("actionname"));
/* 103 */         hashMap.put("fromtypename", recordSet1.getString("fromtypename"));
/* 104 */         hashMap.put("fromtype", recordSet1.getString("fromtype"));
/* 105 */         arrayList.add(hashMap);
/*     */       } 
/*     */     } 
/* 108 */     if (i == 3 || i == -1) {
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 137 */       String str = "";
/* 138 */       if (!"".equals(str2)) {
/* 139 */         str = str + " where actionshowname like '%" + str2 + "%'";
/*     */       }
/*     */ 
/*     */       
/* 143 */       str3 = "select * from (select d.actionname as id,      d.actionshowname as actionname,      '3' as fromtype,      '" + SystemEnv.getHtmlLabelName(82988, Util.getIntValue("" + paramUser.getLanguage(), 7)) + "' as fromtypename from actionsetting d " + str + ") r order by id";
/*     */ 
/*     */ 
/*     */       
/* 147 */       recordSet1.executeSql(str3);
/* 148 */       while (recordSet1.next()) {
/* 149 */         HashMap<Object, Object> hashMap = new HashMap<>();
/* 150 */         hashMap.put("id", recordSet1.getString("id"));
/* 151 */         hashMap.put("actionname", recordSet1.getString("actionname"));
/* 152 */         hashMap.put("fromtypename", recordSet1.getString("fromtypename"));
/* 153 */         hashMap.put("fromtype", recordSet1.getString("fromtype"));
/* 154 */         hashMap.put("uuid", "3_" + recordSet1.getString("id"));
/* 155 */         arrayList.add(hashMap);
/*     */       } 
/*     */     } 
/* 158 */     if (i == 5 || i == -1) {
/* 159 */       String str = "";
/* 160 */       if (!"".equals(str2)) {
/* 161 */         str = str + " where actionname like '%" + str2 + "%'";
/*     */       }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 168 */       str3 = "select * from (select s.actionname as id,       s.actionname,'' as typename,       s.formid,       s.isbill,       '' as datasourceid,       '5' as fromtype,      '" + SystemEnv.getHtmlLabelName(381878, Util.getIntValue("" + paramUser.getLanguage(), 7)) + "' as fromtypename  from esbformactionset s " + str + ") r where r.formid=" + str1 + " order by id";
/*     */ 
/*     */ 
/*     */       
/* 172 */       recordSet1.executeSql(str3);
/* 173 */       while (recordSet1.next()) {
/* 174 */         HashMap<Object, Object> hashMap = new HashMap<>();
/* 175 */         hashMap.put("id", recordSet1.getString("id"));
/* 176 */         hashMap.put("actionname", recordSet1.getString("actionname"));
/* 177 */         hashMap.put("fromtypename", recordSet1.getString("fromtypename"));
/* 178 */         hashMap.put("fromtype", recordSet1.getString("fromtype"));
/* 179 */         hashMap.put("uuid", "5_" + recordSet1.getString("id"));
/* 180 */         arrayList.add(hashMap);
/*     */       } 
/*     */     } 
/* 183 */     if (i == 6 || i == -1) {
/* 184 */       String str = "";
/* 185 */       if (!"".equals(str2)) {
/* 186 */         str = str + " where actionname like '%" + str2 + "%'";
/*     */       }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 193 */       str3 = "select * from (select s.actionname as id,       s.actionname,'' as typename,       s.formid,       s.isbill,       '' as datasourceid,       '6' as fromtype,      '" + SystemEnv.getHtmlLabelName(381878, Util.getIntValue("" + paramUser.getLanguage(), 7)) + "' as fromtypename  from esb_actionset s " + str + ") r where r.formid=" + str1 + " order by id";
/*     */ 
/*     */ 
/*     */       
/* 197 */       recordSet1.executeSql(str3);
/* 198 */       while (recordSet1.next()) {
/* 199 */         HashMap<Object, Object> hashMap = new HashMap<>();
/* 200 */         hashMap.put("id", recordSet1.getString("id"));
/* 201 */         hashMap.put("actionname", recordSet1.getString("actionname"));
/* 202 */         hashMap.put("fromtypename", recordSet1.getString("fromtypename"));
/* 203 */         hashMap.put("fromtype", recordSet1.getString("fromtype"));
/* 204 */         hashMap.put("uuid", "6_" + recordSet1.getString("id"));
/* 205 */         arrayList.add(hashMap);
/*     */       } 
/*     */     } 
/* 208 */     return (List)arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<Map<String, String>> getDMLFormField(User paramUser, Map<String, String> paramMap, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/* 221 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 222 */     String str1 = Util.null2String(paramMap.get("formid"));
/* 223 */     String str2 = Util.null2String(paramMap.get("isbill"));
/* 224 */     String str3 = Util.null2String(paramMap.get("fieldname")).trim();
/* 225 */     String str4 = Util.null2String(paramMap.get("fieldtype"));
/* 226 */     String str5 = Util.null2String(paramMap.get("issearch"));
/* 227 */     String str6 = Util.null2String(paramMap.get("showdetail"));
/* 228 */     int i = Util.getIntValue(Util.null2String(paramMap.get("tableid")), 0);
/*     */     
/* 230 */     FieldBase fieldBase = new FieldBase();
/* 231 */     RecordSet recordSet = new RecordSet();
/*     */     
/* 233 */     ArrayList arrayList1 = new ArrayList();
/* 234 */     Map map1 = new HashMap<>();
/*     */     
/* 236 */     Map map2 = new HashMap<>();
/*     */     
/* 238 */     Map map3 = new HashMap<>();
/*     */ 
/*     */     
/* 241 */     Map map4 = new HashMap<>();
/*     */     
/* 243 */     Map map5 = new HashMap<>();
/*     */     
/* 245 */     fieldBase.setShowdetail(str6);
/* 246 */     fieldBase.setDetailTableId(i);
/*     */ 
/*     */     
/* 249 */     fieldBase.getFormTableFields(paramUser, recordSet, Util.getIntValue(str1, 0), Util.getIntValue(str2, 0), 0);
/*     */     
/* 251 */     List<String> list1 = fieldBase.getFieldList();
/* 252 */     if (null != list1 && list1.size() > 0) {
/*     */ 
/*     */       
/* 255 */       map1 = fieldBase.getFieldDBTypeMap();
/*     */       
/* 257 */       map2 = fieldBase.getFieldLabelMap();
/*     */       
/* 259 */       map3 = fieldBase.getFieldNameMap();
/*     */       
/* 261 */       map4 = fieldBase.getFieldHtmlTypeMap();
/*     */       
/* 263 */       map5 = fieldBase.getFieldTypeMap();
/*     */     } 
/* 265 */     List<String> list2 = new ArrayList();
/* 266 */     if ("1".equals(str5)) {
/*     */       
/* 268 */       if (null != list1 && list1.size() > 0)
/*     */       {
/* 270 */         for (byte b = 0; b < list1.size(); b++) {
/*     */           
/* 272 */           String str7 = list1.get(b);
/* 273 */           String str8 = Util.null2String((String)map3.get(str7));
/* 274 */           String str9 = Util.null2String((String)map1.get(str7));
/* 275 */           String str10 = Util.null2String((String)map2.get(str7));
/* 276 */           String str11 = "";
/* 277 */           if (Util.getIntValue(str7, 0) > 0 || Util.getIntValue(str7, 0) == -2 || Util.getIntValue(str7, 0) == -17) {
/*     */             
/* 279 */             if (!"".equals(str4)) {
/*     */               
/* 281 */               if ("1".equals(str4))
/*     */               {
/* 283 */                 if (list2.indexOf(str7) == -1) {
/* 284 */                   list2.add(str7);
/*     */                 
/*     */                 }
/*     */               }
/*     */             }
/* 289 */             else if (list2.indexOf(str7) == -1) {
/* 290 */               list2.add(str7);
/*     */             }
/*     */           
/* 293 */           } else if (Util.getIntValue(str7, 0) < 0 && Util.getIntValue(str7, 0) > -11) {
/*     */             
/* 295 */             if (!"".equals(str4)) {
/*     */               
/* 297 */               if ("2".equals(str4))
/*     */               {
/* 299 */                 if (list2.indexOf(str7) == -1) {
/* 300 */                   list2.add(str7);
/*     */                 
/*     */                 }
/*     */               }
/*     */             }
/* 305 */             else if (list2.indexOf(str7) == -1) {
/* 306 */               list2.add(str7);
/*     */             }
/*     */           
/* 309 */           } else if (Util.getIntValue(str7, 0) < -10) {
/*     */             
/* 311 */             if (!"".equals(str4)) {
/*     */               
/* 313 */               if ("3".equals(str4))
/*     */               {
/* 315 */                 if (list2.indexOf(str7) == -1) {
/* 316 */                   list2.add(str7);
/*     */                 
/*     */                 }
/*     */               }
/*     */             }
/* 321 */             else if (list2.indexOf(str7) == -1) {
/* 322 */               list2.add(str7);
/*     */             } 
/*     */           } 
/* 325 */           if (!"".equals(str3))
/*     */           {
/*     */             
/* 328 */             if (str10.indexOf(str3) > -1) {
/*     */               
/* 330 */               if (list2.indexOf(str7) == -1) {
/* 331 */                 list2.add(str7);
/*     */               }
/*     */             } else {
/* 334 */               list2.remove(str7);
/*     */             } 
/*     */           }
/*     */         } 
/*     */       }
/*     */     } else {
/*     */       
/* 341 */       list2 = list1;
/*     */     } 
/* 343 */     if (null != list2 && list2.size() > 0) {
/*     */       
/* 345 */       boolean bool = true;
/* 346 */       for (byte b = 0; b < list2.size(); b++) {
/*     */         
/* 348 */         String str7 = list2.get(b);
/* 349 */         String str8 = Util.null2String((String)map3.get(str7));
/* 350 */         String str9 = Util.null2String((String)map1.get(str7));
/* 351 */         String str10 = Util.null2String((String)map2.get(str7));
/* 352 */         String str11 = Util.null2String((String)map4.get(str7));
/* 353 */         String str12 = Util.null2String((String)map5.get(str7));
/* 354 */         String str13 = "";
/* 355 */         if (Util.getIntValue(str7, 0) > 0 || Util.getIntValue(str7, 0) == -2 || Util.getIntValue(str7, 0) == -17) {
/*     */           
/* 357 */           if (!"".equals(str4) && !"1".equals(str4)) {
/*     */             continue;
/*     */           }
/*     */ 
/*     */           
/* 362 */           str13 = SystemEnv.getHtmlLabelName(83678, Util.getIntValue("" + paramUser.getLanguage(), 7));
/*     */         }
/* 364 */         else if (Util.getIntValue(str7, 0) < 0 && Util.getIntValue(str7, 0) > -11) {
/*     */           
/* 366 */           if (!"".equals(str4) && !"2".equals(str4)) {
/*     */             continue;
/*     */           }
/*     */           
/* 370 */           str13 = SystemEnv.getHtmlLabelName(83679, Util.getIntValue("" + paramUser.getLanguage(), 7));
/*     */         }
/* 372 */         else if (Util.getIntValue(str7, 0) < -10) {
/*     */           
/* 374 */           if (!"".equals(str4) && !"3".equals(str4)) {
/*     */             continue;
/*     */           }
/*     */           
/* 378 */           str13 = SystemEnv.getHtmlLabelName(83680, Util.getIntValue("" + paramUser.getLanguage(), 7));
/*     */         } 
/*     */ 
/*     */ 
/*     */ 
/*     */         
/* 384 */         HashMap<Object, Object> hashMap1 = new HashMap<>();
/*     */         
/* 386 */         HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 387 */         hashMap2.put("fieldid", str7);
/* 388 */         hashMap2.put("labelname", str10);
/* 389 */         hashMap2.put("fieldname", str8);
/* 390 */         hashMap2.put("fielddbtype", str9);
/* 391 */         hashMap2.put("fieldhtmltype", str11);
/* 392 */         hashMap2.put("fieldtype", str12);
/* 393 */         JSONObject jSONObject = JSONObject.fromObject(hashMap2);
/* 394 */         hashMap1.put("bean", jSONObject.toString());
/*     */         
/* 396 */         hashMap1.put("id", str7);
/* 397 */         hashMap1.put("jfieldlabel", str10);
/* 398 */         hashMap1.put("jfieldname", str8);
/* 399 */         hashMap1.put("jfielddbtype", str9);
/* 400 */         hashMap1.put("fielddesc", str13);
/* 401 */         arrayList.add(hashMap1); continue;
/*     */       } 
/*     */     } 
/* 404 */     return (List)arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<Map<String, String>> getESBFormField(User paramUser, Map<String, String> paramMap, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/*     */     Map<String, String> map4, map5;
/* 418 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 419 */     String str1 = Util.null2String(paramMap.get("formid"));
/* 420 */     String str2 = Util.null2String(paramMap.get("isbill"));
/* 421 */     String str3 = Util.null2String(paramMap.get("fieldname")).trim();
/* 422 */     String str4 = Util.null2String(paramMap.get("fieldtype"));
/* 423 */     String str5 = Util.null2String(paramMap.get("issearch"));
/* 424 */     String str6 = Util.null2String(paramMap.get("showdetail"));
/* 425 */     String str7 = Util.null2String(paramMap.get("tableInfo"));
/* 426 */     int i = Util.getIntValue(Util.null2String(paramMap.get("tableid")), 0);
/*     */     
/* 428 */     ESBFieldBase eSBFieldBase = new ESBFieldBase();
/* 429 */     RecordSet recordSet = new RecordSet();
/*     */     
/* 431 */     ArrayList arrayList1 = new ArrayList();
/* 432 */     Map map1 = new HashMap<>();
/*     */     
/* 434 */     Map map2 = new HashMap<>();
/*     */     
/* 436 */     Map map3 = new HashMap<>();
/*     */ 
/*     */     
/* 439 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*     */     
/* 441 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/*     */     
/* 443 */     eSBFieldBase.setShowdetail(str6);
/* 444 */     eSBFieldBase.setDetailTableId(i);
/*     */ 
/*     */     
/* 447 */     eSBFieldBase.getFormTableFields(paramUser, recordSet, Util.getIntValue(str1, 0), Util.getIntValue(str2, 0), 0, str7);
/*     */     
/* 449 */     List<String> list1 = eSBFieldBase.getFieldList();
/* 450 */     if (null != list1 && list1.size() > 0) {
/*     */ 
/*     */       
/* 453 */       map1 = eSBFieldBase.getFieldDBTypeMap();
/*     */       
/* 455 */       map2 = eSBFieldBase.getFieldLabelMap();
/*     */       
/* 457 */       map3 = eSBFieldBase.getFieldNameMap();
/*     */       
/* 459 */       map4 = eSBFieldBase.getFieldHtmlTypeMap();
/*     */       
/* 461 */       map5 = eSBFieldBase.getFieldTypeMap();
/*     */     } 
/* 463 */     List<String> list2 = new ArrayList();
/* 464 */     if ("1".equals(str5)) {
/*     */       
/* 466 */       if (null != list1 && list1.size() > 0)
/*     */       {
/* 468 */         for (byte b = 0; b < list1.size(); b++) {
/*     */           
/* 470 */           String str8 = list1.get(b);
/* 471 */           String str9 = Util.null2String((String)map3.get(str8));
/* 472 */           String str10 = Util.null2String((String)map1.get(str8));
/* 473 */           String str11 = Util.null2String((String)map2.get(str8));
/* 474 */           String str12 = "";
/* 475 */           if (Util.getIntValue(str8, 0) > 0 || Util.getIntValue(str8, 0) == -2 || Util.getIntValue(str8, 0) == -17) {
/*     */             
/* 477 */             if (!"".equals(str4)) {
/*     */               
/* 479 */               if ("1".equals(str4))
/*     */               {
/* 481 */                 if (list2.indexOf(str8) == -1) {
/* 482 */                   list2.add(str8);
/*     */                 
/*     */                 }
/*     */               }
/*     */             }
/* 487 */             else if (list2.indexOf(str8) == -1) {
/* 488 */               list2.add(str8);
/*     */             }
/*     */           
/* 491 */           } else if (Util.getIntValue(str8, 0) < 0 && Util.getIntValue(str8, 0) > -11) {
/*     */             
/* 493 */             if (!"".equals(str4)) {
/*     */               
/* 495 */               if ("2".equals(str4))
/*     */               {
/* 497 */                 if (list2.indexOf(str8) == -1) {
/* 498 */                   list2.add(str8);
/*     */                 
/*     */                 }
/*     */               }
/*     */             }
/* 503 */             else if (list2.indexOf(str8) == -1) {
/* 504 */               list2.add(str8);
/*     */             }
/*     */           
/* 507 */           } else if (Util.getIntValue(str8, 0) < -10) {
/*     */             
/* 509 */             if (!"".equals(str4)) {
/*     */               
/* 511 */               if ("3".equals(str4))
/*     */               {
/* 513 */                 if (list2.indexOf(str8) == -1) {
/* 514 */                   list2.add(str8);
/*     */                 
/*     */                 }
/*     */               }
/*     */             }
/* 519 */             else if (list2.indexOf(str8) == -1) {
/* 520 */               list2.add(str8);
/*     */             } 
/*     */           } 
/* 523 */           if (!"".equals(str3))
/*     */           {
/*     */             
/* 526 */             if (str11.indexOf(str3) > -1) {
/*     */               
/* 528 */               if (list2.indexOf(str8) == -1) {
/* 529 */                 list2.add(str8);
/*     */               }
/*     */             } else {
/* 532 */               list2.remove(str8);
/*     */             } 
/*     */           }
/*     */         } 
/*     */       }
/*     */     } else {
/*     */       
/* 539 */       list2 = list1;
/*     */     } 
/* 541 */     if (null != list2 && list2.size() > 0) {
/*     */       
/* 543 */       boolean bool = true;
/* 544 */       for (byte b = 0; b < list2.size(); b++) {
/*     */         
/* 546 */         String str8 = list2.get(b);
/* 547 */         String str9 = Util.null2String((String)map3.get(str8));
/* 548 */         String str10 = Util.null2String((String)map1.get(str8));
/* 549 */         String str11 = Util.null2String((String)map2.get(str8));
/* 550 */         String str12 = Util.null2String(map4.get(str8));
/* 551 */         String str13 = Util.null2String(map5.get(str8));
/* 552 */         String str14 = "";
/* 553 */         if (Util.getIntValue(str8, 0) > 0 || Util.getIntValue(str8, 0) == -2 || Util.getIntValue(str8, 0) == -17) {
/*     */           
/* 555 */           if (!"".equals(str4) && !"1".equals(str4)) {
/*     */             continue;
/*     */           }
/*     */ 
/*     */           
/* 560 */           str14 = SystemEnv.getHtmlLabelName(83678, Util.getIntValue("" + paramUser.getLanguage(), 7));
/*     */         }
/* 562 */         else if (Util.getIntValue(str8, 0) < 0 && Util.getIntValue(str8, 0) > -11) {
/*     */           
/* 564 */           if (!"".equals(str4) && !"2".equals(str4)) {
/*     */             continue;
/*     */           }
/*     */           
/* 568 */           str14 = SystemEnv.getHtmlLabelName(83679, Util.getIntValue("" + paramUser.getLanguage(), 7));
/*     */         }
/* 570 */         else if (Util.getIntValue(str8, 0) < -10) {
/*     */           
/* 572 */           if (!"".equals(str4) && !"3".equals(str4)) {
/*     */             continue;
/*     */           }
/*     */           
/* 576 */           str14 = SystemEnv.getHtmlLabelName(83680, Util.getIntValue("" + paramUser.getLanguage(), 7));
/*     */         } 
/*     */ 
/*     */ 
/*     */ 
/*     */         
/* 582 */         HashMap<Object, Object> hashMap3 = new HashMap<>();
/*     */         
/* 584 */         HashMap<Object, Object> hashMap4 = new HashMap<>();
/* 585 */         hashMap4.put("fieldid", str8);
/* 586 */         hashMap4.put("labelname", str11);
/* 587 */         hashMap4.put("fieldname", str9);
/* 588 */         hashMap4.put("fielddbtype", str10);
/* 589 */         hashMap4.put("fieldhtmltype", str12);
/* 590 */         hashMap4.put("fieldtype", str13);
/* 591 */         JSONObject jSONObject = JSONObject.fromObject(hashMap4);
/* 592 */         hashMap3.put("bean", jSONObject.toString());
/*     */         
/* 594 */         hashMap3.put("id", str8);
/* 595 */         hashMap3.put("jfieldlabel", str11);
/* 596 */         hashMap3.put("jfieldname", str9);
/* 597 */         hashMap3.put("jfielddbtype", str10);
/* 598 */         hashMap3.put("fielddesc", str14);
/* 599 */         arrayList.add(hashMap3); continue;
/*     */       } 
/*     */     } 
/* 602 */     return (List)arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<Map<String, String>> getESBNodeOperater(User paramUser, Map<String, String> paramMap, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/* 615 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*     */ 
/*     */     
/* 618 */     String str1 = Util.null2String(paramMap.get("type"));
/*     */     
/* 620 */     String str2 = "";
/* 621 */     String str3 = "";
/* 622 */     String str4 = "";
/* 623 */     String str5 = "";
/* 624 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/* 626 */     String str6 = "";
/* 627 */     if ("1".equals(str1)) {
/*     */       
/* 629 */       str6 = SystemEnv.getHtmlLabelName(517189, Util.getIntValue("" + paramUser.getLanguage(), 7));
/*     */     } else {
/*     */       
/* 632 */       str6 = SystemEnv.getHtmlLabelName(83680, Util.getIntValue("" + paramUser.getLanguage(), 7));
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/* 637 */     str2 = "-11";
/* 638 */     str3 = "s.lastname";
/* 639 */     str4 = "varchar";
/* 640 */     if ("1".equals(str1)) {
/*     */       
/* 642 */       str5 = SystemEnv.getHtmlLabelName(518804, Util.getIntValue("" + paramUser.getLanguage(), 7));
/*     */     } else {
/*     */       
/* 645 */       str5 = SystemEnv.getHtmlLabelName(32678, Util.getIntValue("" + paramUser.getLanguage(), 7));
/*     */     } 
/*     */ 
/*     */     
/* 649 */     hashMap.put("fieldid", str2);
/* 650 */     hashMap.put("labelname", str5);
/* 651 */     hashMap.put("fieldname", str3);
/* 652 */     hashMap.put("fielddbtype", str4);
/* 653 */     hashMap.put("fielddesc", str6);
/* 654 */     arrayList.add(hashMap);
/*     */     
/* 656 */     str2 = "-12";
/* 657 */     str3 = "s.userid";
/* 658 */     str4 = "int";
/* 659 */     str5 = SystemEnv.getHtmlLabelName(32679, Util.getIntValue("" + paramUser.getLanguage(), 7));
/*     */     
/* 661 */     hashMap = new HashMap<>();
/* 662 */     hashMap.put("fieldid", str2);
/* 663 */     hashMap.put("labelname", str5);
/* 664 */     hashMap.put("fieldname", str3);
/* 665 */     hashMap.put("fielddbtype", str4);
/* 666 */     hashMap.put("fielddesc", str6);
/* 667 */     arrayList.add(hashMap);
/*     */     
/* 669 */     str2 = "-13";
/* 670 */     str3 = "s.usersubcompany";
/* 671 */     str4 = "int";
/* 672 */     if ("1".equals(str1)) {
/*     */       
/* 674 */       str5 = SystemEnv.getHtmlLabelName(385899, Util.getIntValue("" + paramUser.getLanguage(), 7));
/*     */     } else {
/*     */       
/* 677 */       str5 = SystemEnv.getHtmlLabelName(32680, Util.getIntValue("" + paramUser.getLanguage(), 7));
/*     */     } 
/*     */ 
/*     */     
/* 681 */     hashMap = new HashMap<>();
/* 682 */     hashMap.put("fieldid", str2);
/* 683 */     hashMap.put("labelname", str5);
/* 684 */     hashMap.put("fieldname", str3);
/* 685 */     hashMap.put("fielddbtype", str4);
/* 686 */     hashMap.put("fielddesc", str6);
/* 687 */     arrayList.add(hashMap);
/*     */     
/* 689 */     str2 = "-14";
/* 690 */     str3 = "s.userdepartment";
/* 691 */     str4 = "int";
/* 692 */     if ("1".equals(str1)) {
/*     */       
/* 694 */       str5 = SystemEnv.getHtmlLabelName(385898, Util.getIntValue("" + paramUser.getLanguage(), 7));
/*     */     } else {
/*     */       
/* 697 */       str5 = SystemEnv.getHtmlLabelName(32681, Util.getIntValue("" + paramUser.getLanguage(), 7));
/*     */     } 
/*     */     
/* 700 */     hashMap = new HashMap<>();
/* 701 */     hashMap.put("fieldid", str2);
/* 702 */     hashMap.put("labelname", str5);
/* 703 */     hashMap.put("fieldname", str3);
/* 704 */     hashMap.put("fielddbtype", str4);
/* 705 */     hashMap.put("fielddesc", str6);
/* 706 */     arrayList.add(hashMap);
/*     */     
/* 708 */     str2 = "-15";
/* 709 */     str3 = "s.managerid";
/* 710 */     str4 = "int";
/* 711 */     if ("1".equals(str1)) {
/*     */       
/* 713 */       str5 = SystemEnv.getHtmlLabelName(515013, Util.getIntValue("" + paramUser.getLanguage(), 7));
/*     */     } else {
/*     */       
/* 716 */       str5 = SystemEnv.getHtmlLabelName(32682, Util.getIntValue("" + paramUser.getLanguage(), 7));
/*     */     } 
/*     */     
/* 719 */     hashMap = new HashMap<>();
/* 720 */     hashMap.put("fieldid", str2);
/* 721 */     hashMap.put("labelname", str5);
/* 722 */     hashMap.put("fieldname", str3);
/* 723 */     hashMap.put("fielddbtype", str4);
/* 724 */     hashMap.put("fielddesc", str6);
/* 725 */     arrayList.add(hashMap);
/*     */     
/* 727 */     str2 = "-16";
/* 728 */     str3 = "s.managerstr";
/* 729 */     str4 = "varchar";
/* 730 */     if ("1".equals(str1)) {
/*     */       
/* 732 */       str5 = SystemEnv.getHtmlLabelName(515014, Util.getIntValue("" + paramUser.getLanguage(), 7));
/*     */     } else {
/*     */       
/* 735 */       str5 = SystemEnv.getHtmlLabelName(32683, Util.getIntValue("" + paramUser.getLanguage(), 7));
/*     */     } 
/*     */     
/* 738 */     hashMap = new HashMap<>();
/* 739 */     hashMap.put("fieldid", str2);
/* 740 */     hashMap.put("labelname", str5);
/* 741 */     hashMap.put("fieldname", str3);
/* 742 */     hashMap.put("fielddbtype", str4);
/* 743 */     hashMap.put("fielddesc", str6);
/* 744 */     arrayList.add(hashMap);
/* 745 */     return (List)arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<Map<String, String>> getESBRequestData(User paramUser, Map<String, String> paramMap, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/* 758 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 759 */     String str1 = "";
/* 760 */     String str2 = "";
/* 761 */     String str3 = "";
/* 762 */     String str4 = "";
/* 763 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 764 */     String str5 = SystemEnv.getHtmlLabelName(83679, Util.getIntValue("" + paramUser.getLanguage(), 7));
/*     */     
/* 766 */     str1 = "-1";
/* 767 */     str2 = "requestid";
/* 768 */     str3 = "int";
/* 769 */     str4 = SystemEnv.getHtmlLabelName(28610, paramUser.getLanguage());
/*     */     
/* 771 */     hashMap.put("fieldid", str1);
/* 772 */     hashMap.put("labelname", str4);
/* 773 */     hashMap.put("fieldname", str2);
/* 774 */     hashMap.put("fielddbtype", str3);
/* 775 */     hashMap.put("fielddesc", str5);
/* 776 */     arrayList.add(hashMap);
/*     */     
/* 778 */     str1 = "-3";
/* 779 */     str2 = "s.workflowid";
/* 780 */     str3 = "int";
/* 781 */     str4 = SystemEnv.getHtmlLabelName(32672, Util.getIntValue("" + paramUser.getLanguage(), 7));
/*     */     
/* 783 */     hashMap = new HashMap<>();
/* 784 */     hashMap.put("fieldid", str1);
/* 785 */     hashMap.put("labelname", str4);
/* 786 */     hashMap.put("fieldname", str2);
/* 787 */     hashMap.put("fielddbtype", str3);
/* 788 */     hashMap.put("fielddesc", str5);
/* 789 */     arrayList.add(hashMap);
/*     */     
/* 791 */     str1 = "-4";
/* 792 */     str2 = "s.formid";
/* 793 */     str3 = "int";
/* 794 */     str4 = SystemEnv.getHtmlLabelName(32673, Util.getIntValue("" + paramUser.getLanguage(), 7));
/*     */     
/* 796 */     hashMap = new HashMap<>();
/* 797 */     hashMap.put("fieldid", str1);
/* 798 */     hashMap.put("labelname", str4);
/* 799 */     hashMap.put("fieldname", str2);
/* 800 */     hashMap.put("fielddbtype", str3);
/* 801 */     hashMap.put("fielddesc", str5);
/* 802 */     arrayList.add(hashMap);
/*     */     
/* 804 */     str1 = "-5";
/* 805 */     str2 = "s.nodeid";
/* 806 */     str3 = "int";
/* 807 */     str4 = SystemEnv.getHtmlLabelName(32674, Util.getIntValue("" + paramUser.getLanguage(), 7));
/*     */     
/* 809 */     hashMap = new HashMap<>();
/* 810 */     hashMap.put("fieldid", str1);
/* 811 */     hashMap.put("labelname", str4);
/* 812 */     hashMap.put("fieldname", str2);
/* 813 */     hashMap.put("fielddbtype", str3);
/* 814 */     hashMap.put("fielddesc", str5);
/* 815 */     arrayList.add(hashMap);
/*     */     
/* 817 */     str1 = "-6";
/* 818 */     str2 = "s.nodetype";
/* 819 */     str3 = "int";
/* 820 */     str4 = SystemEnv.getHtmlLabelName(32675, Util.getIntValue("" + paramUser.getLanguage(), 7));
/*     */     
/* 822 */     hashMap = new HashMap<>();
/* 823 */     hashMap.put("fieldid", str1);
/* 824 */     hashMap.put("labelname", str4);
/* 825 */     hashMap.put("fieldname", str2);
/* 826 */     hashMap.put("fielddbtype", str3);
/* 827 */     hashMap.put("fielddesc", str5);
/* 828 */     arrayList.add(hashMap);
/*     */     
/* 830 */     str1 = "-7";
/* 831 */     str2 = "s.requestname";
/* 832 */     str3 = "varchar";
/* 833 */     str4 = SystemEnv.getHtmlLabelName(1334, Util.getIntValue("" + paramUser.getLanguage(), 7));
/*     */     
/* 835 */     hashMap = new HashMap<>();
/* 836 */     hashMap.put("fieldid", str1);
/* 837 */     hashMap.put("labelname", str4);
/* 838 */     hashMap.put("fieldname", str2);
/* 839 */     hashMap.put("fielddbtype", str3);
/* 840 */     hashMap.put("fielddesc", str5);
/* 841 */     arrayList.add(hashMap);
/*     */     
/* 843 */     str1 = "-8";
/* 844 */     str2 = "s.remark";
/* 845 */     str3 = "varchar";
/* 846 */     str4 = SystemEnv.getHtmlLabelName(17614, Util.getIntValue("" + paramUser.getLanguage(), 7));
/*     */     
/* 848 */     hashMap = new HashMap<>();
/* 849 */     hashMap.put("fieldid", str1);
/* 850 */     hashMap.put("labelname", str4);
/* 851 */     hashMap.put("fieldname", str2);
/* 852 */     hashMap.put("fielddbtype", str3);
/* 853 */     hashMap.put("fielddesc", str5);
/* 854 */     arrayList.add(hashMap);
/*     */     
/* 856 */     str1 = "-9";
/* 857 */     str2 = "s.creater";
/* 858 */     str3 = "int";
/* 859 */     str4 = SystemEnv.getHtmlLabelName(32676, Util.getIntValue("" + paramUser.getLanguage(), 7));
/*     */     
/* 861 */     hashMap = new HashMap<>();
/* 862 */     hashMap.put("fieldid", str1);
/* 863 */     hashMap.put("labelname", str4);
/* 864 */     hashMap.put("fieldname", str2);
/* 865 */     hashMap.put("fielddbtype", str3);
/* 866 */     hashMap.put("fielddesc", str5);
/* 867 */     arrayList.add(hashMap);
/*     */     
/* 869 */     str1 = "-10";
/* 870 */     str2 = "s.createrdepartment";
/* 871 */     str3 = "int";
/* 872 */     str4 = SystemEnv.getHtmlLabelName(32677, Util.getIntValue("" + paramUser.getLanguage(), 7));
/*     */     
/* 874 */     hashMap = new HashMap<>();
/* 875 */     hashMap.put("fieldid", str1);
/* 876 */     hashMap.put("labelname", str4);
/* 877 */     hashMap.put("fieldname", str2);
/* 878 */     hashMap.put("fielddbtype", str3);
/* 879 */     hashMap.put("fielddesc", str5);
/* 880 */     arrayList.add(hashMap);
/*     */     
/* 882 */     return (List)arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<Map<String, String>> getESBActionEventData(User paramUser, Map<String, String> paramMap, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/* 895 */     ArrayList<Map<String, String>> arrayList = new ArrayList();
/* 896 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 897 */     Prop prop = Prop.getInstance();
/* 898 */     String str1 = Prop.getPropValue("esb", "format");
/* 899 */     if ("xml".equalsIgnoreCase(str1)) {
/*     */       
/* 901 */       String str = SystemEnv.getHtmlLabelName(511359, paramUser.getLanguage());
/* 902 */       Map<String, String> map = getTips(str);
/* 903 */       arrayList.add(map);
/* 904 */       return arrayList;
/*     */     } 
/*     */     
/* 907 */     String str2 = Util.null2String(paramMap.get("eventId"));
/* 908 */     String str3 = Util.null2String(paramMap.get("eventName"));
/* 909 */     EsbService esbService = EsbClient.getService();
/* 910 */     String str4 = esbService.getList();
/* 911 */     Integer integer = Integer.valueOf(-1);
/*     */     try {
/* 913 */       integer = (Integer)JSONPath.read(str4, "$.code");
/* 914 */     } catch (Exception exception) {
/* 915 */       log.error(JavaUtil.getExceptionDetail(exception));
/* 916 */       integer = Integer.valueOf(-1);
/*     */     } 
/*     */     
/* 919 */     if (integer.intValue() == -1)
/*     */     
/* 921 */     { String str = SystemEnv.getHtmlLabelName(511301, paramUser.getLanguage());
/* 922 */       Map<String, String> map = getTips(str);
/* 923 */       arrayList.add(map); }
/* 924 */     else if (integer.intValue() == 100)
/* 925 */     { Object object = JSONPath.read(str4, "$.data");
/* 926 */       if (object instanceof JSONArray) {
/* 927 */         JSONArray jSONArray = (JSONArray)object;
/* 928 */         for (byte b = 0; b < jSONArray.size(); b++) {
/* 929 */           JSONObject jSONObject = jSONArray.getJSONObject(b);
/* 930 */           String str5 = jSONObject.getString("eventId");
/* 931 */           String str6 = jSONObject.getString("eventName");
/*     */           
/* 933 */           if (!StringUtils.isNotEmpty(str2) || str5.contains(str2))
/*     */           {
/*     */             
/* 936 */             if (!StringUtils.isNotEmpty(str3) || str6.contains(str3)) {
/*     */ 
/*     */ 
/*     */               
/* 940 */               hashMap = new HashMap<>();
/* 941 */               hashMap.put("eventId", str5);
/* 942 */               hashMap.put("eventName", str6);
/* 943 */               hashMap.put("eventStatus", jSONObject.getString("eventStatus"));
/* 944 */               hashMap.put("eventType", jSONObject.getString("eventType"));
/* 945 */               hashMap.put("productName", jSONObject.getString("productName"));
/* 946 */               hashMap.put("moduleName", jSONObject.getString("moduleName"));
/* 947 */               arrayList.add(hashMap);
/*     */             }  } 
/*     */         } 
/*     */       }  }
/* 951 */     else { String str = (String)JSONPath.read(str4, "$.msg");
/* 952 */       Map<String, String> map = getTips(str);
/* 953 */       arrayList.add(map); }
/*     */     
/* 955 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private Map<String, String> getTips(String paramString) {
/* 964 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 965 */     hashMap.put("eventId", "");
/* 966 */     hashMap.put("eventName", "");
/* 967 */     hashMap.put("eventStatus", "");
/* 968 */     hashMap.put("eventType", paramString);
/* 969 */     hashMap.put("productCode", "");
/* 970 */     hashMap.put("moduleCode", "");
/* 971 */     hashMap.put("resId", "");
/* 972 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/action/ActionDataSource.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */