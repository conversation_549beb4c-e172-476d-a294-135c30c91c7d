/*     */ package weaver.workflow.action;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.List;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.conn.RecordSetTrans;
/*     */ import weaver.general.Util;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class ESBReturnRuleExpressionBean
/*     */ {
/*  25 */   private int id = -1;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  30 */   private int ruleid = -1;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  35 */   private int datafield = 0;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  40 */   private String datafieldlabel = null;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  45 */   private String htmltype = null;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  50 */   private String fieldtype = null;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  55 */   private int typehrm = -1;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  60 */   private int compareoption1 = -1;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  65 */   private int compareoption2 = -1;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  70 */   private String browservalue = null;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  75 */   private String browserspantext = null;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  80 */   private String browserspanlabel = null;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  85 */   private int selectvalue1 = -1;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  90 */   private String textvalue1 = null;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  95 */   private String textvalue2 = null;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/* 100 */   private String checkboxvalue1 = null;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/* 105 */   private int paramtype = -1;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/* 110 */   private int valuetype = -1;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/* 115 */   private String dbtype = "";
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private boolean isMapitem;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String linkid;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private int rulesrc;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/* 135 */   private String elementvalue1 = null;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/* 140 */   private String elementtext1 = null;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/* 145 */   private String elementvalue2 = null;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/* 150 */   private int valuevarid = -1;
/*     */   
/* 152 */   private String redius = null;
/* 153 */   private String nodeId = null;
/* 154 */   private String meetCondition = null;
/*     */ 
/*     */ 
/*     */   
/* 158 */   private String jingdu = null;
/*     */ 
/*     */ 
/*     */   
/* 162 */   private String weidu = null;
/*     */ 
/*     */   
/*     */   public static final int TYPE_STRING = 0;
/*     */ 
/*     */   
/*     */   public static final int TYPE_INT = 1;
/*     */ 
/*     */   
/*     */   public static final int TYPE_FLOAT = 2;
/*     */ 
/*     */   
/*     */   public static final int TYPE_MAP = 3;
/*     */ 
/*     */   
/*     */   public static final int RELATION_GREATER = 0;
/*     */ 
/*     */   
/*     */   public static final int RELATION_GREATERANDEQUAL = 1;
/*     */   
/*     */   public static final int RELATION_LESS = 2;
/*     */   
/*     */   public static final int RELATION_LESSANDEQUAL = 3;
/*     */   
/*     */   public static final int RELATION_EQUAL = 4;
/*     */   
/*     */   public static final int RELATION_NOTEQUAL = 5;
/*     */   
/*     */   public static final int RELATION_CONTAIN = 6;
/*     */   
/*     */   public static final int RELATION_NOTCONTAIN = 7;
/*     */   
/*     */   public static final String VARINFOSPLIT = "#";
/*     */ 
/*     */   
/*     */   public int persistence2db(RecordSet paramRecordSet, String paramString) throws Exception {
/* 198 */     String str = null;
/* 199 */     setElementvalue1();
/* 200 */     if (this.id > 0) {
/*     */       
/* 202 */       str = "update esbreturnrule_expressionbase set  datafield =" + this.datafield + "  datafieldtext ='" + this.datafieldlabel + "'  typehrm ='" + this.typehrm + "'  compareoption1=" + this.compareoption1 + " compareoption2=" + this.compareoption2 + " htmltype=" + this.htmltype + " fieldtype='" + this.fieldtype + "'  dbtype='" + this.dbtype + "'  paramtype='" + this.paramtype + "'  valuetype='" + this.valuetype + "'  elementvalue1='" + this.elementvalue1 + "'  elementlabel1='" + this.elementtext1 + "'  elementvalue2='" + this.elementvalue2 + "'  where id=" + this.id;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*     */     }
/*     */     else {
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 219 */       this.id = getdbid(paramRecordSet);
/* 220 */       str = "insert into esbreturnrule_expressionbase(id, ruleid, datafield, datafieldtext, typehrm, compareoption1, compareoption2, htmltype, fieldtype,dbtype, paramtype, valuetype, elementvalue1,elementlabel1,elementvalue2,redius,nodeid,meetcondition,jingdu,weidu) values(";
/* 221 */       str = str + this.id + ", " + this.ruleid + ", " + this.datafield + ", '" + this.datafieldlabel + "', '" + this.typehrm + "', " + this.compareoption1 + ", " + this.compareoption2 + ", -1, '', '', '" + this.paramtype + "', '" + this.valuetype + "', '" + this.elementvalue1 + "', '" + this.elementtext1 + "', '" + this.elementvalue2 + "', '-1', '-1', '-1', '', '')";
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 251 */     paramRecordSet.executeSql(str);
/* 252 */     int i = ESBReturnRuleExpressions.getdbid(paramRecordSet);
/* 253 */     str = "insert into esbreturnrule_expressions(id, ruleid, relation, expbaseid) values (" + i + ", " + this.ruleid + ", " + -1 + ", " + this.id + ")";
/*     */     
/* 255 */     paramRecordSet.executeSql(str);
/* 256 */     return i;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static synchronized int getdbid(RecordSet paramRecordSet) throws Exception {
/* 267 */     int i = 0;
/*     */     
/* 269 */     paramRecordSet.executeSql("select max(id) as id from esbreturnrule_expressionbase");
/* 270 */     if (paramRecordSet.next()) {
/* 271 */       i = Util.getIntValue(paramRecordSet.getString("id"), 0);
/*     */     }
/*     */     
/* 274 */     return i + 1;
/*     */   }
/*     */   
/*     */   private int getvardbid(RecordSetTrans paramRecordSetTrans) throws Exception {
/* 278 */     int i = 0;
/*     */     
/* 280 */     paramRecordSetTrans.executeSql("select max(id) as id from esbreturnrule_variablebase");
/* 281 */     if (paramRecordSetTrans.next()) {
/* 282 */       i = Util.getIntValue(paramRecordSetTrans.getString("id"), 0);
/*     */     }
/* 284 */     return i + 1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getExpressionFieldids() {
/* 295 */     String str = "#" + this.htmltype + "#" + this.fieldtype + "#" + this.compareoption1 + "#" + this.valuetype + "#" + getFormFieldId(this.datafield);
/* 296 */     if (this.fieldtype.equals("_level")) {
/* 297 */       return "level_" + this.datafield + str;
/*     */     }
/* 299 */     if (this.valuetype == 3) {
/* 300 */       return getFormFieldId(this.datafield) + str + "," + getFormFieldId(Util.getIntValue(this.elementvalue1, -1)) + str;
/*     */     }
/* 302 */     return getFormFieldId(this.datafield) + str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String toIKExpression2() {
/* 313 */     String str = "";
/*     */ 
/*     */ 
/*     */     
/* 317 */     this.datafieldlabel = this.datafieldlabel.replace(".", "_");
/* 318 */     str = this.datafieldlabel + " " + getRelation4IK() + "\"" + this.elementtext1 + "\"";
/*     */     
/* 320 */     return str;
/*     */   }
/*     */   
/*     */   private int getFormFieldId(int paramInt) {
/* 324 */     if (isMapitem()) {
/* 325 */       String str = "SELECT formfieldid FROM esbreturnrule_mapitem WHERE ruleid='" + getRuleid() + "' AND rulesrc='" + getRulesrc() + "' AND linkid='" + getLinkid() + "' AND rulevarid='" + paramInt + "'";
/* 326 */       RecordSet recordSet = new RecordSet();
/* 327 */       recordSet.executeSql(str);
/* 328 */       if (recordSet.next()) {
/* 329 */         paramInt = Util.getIntValue(recordSet.getString("formfieldid"), 0);
/*     */       }
/*     */     } 
/* 332 */     return paramInt;
/*     */   }
/*     */   private List getCondition() {
/* 335 */     ArrayList<String> arrayList = new ArrayList();
/* 336 */     String str = "SELECT nodeid,meetCondition FROM esbreturnrule_mapitem WHERE ruleid='" + getRuleid() + "' AND rulesrc='" + getRulesrc() + "' AND linkid='" + getLinkid() + "'";
/* 337 */     RecordSet recordSet = new RecordSet();
/* 338 */     recordSet.executeSql(str);
/* 339 */     if (recordSet.next()) {
/* 340 */       arrayList.add(Util.null2String(recordSet.getString("nodeid")));
/* 341 */       arrayList.add(Util.null2String(recordSet.getString("meetCondition")));
/*     */     } 
/* 343 */     return arrayList;
/*     */   }
/*     */ 
/*     */   
/*     */   private String getRelation4IK() {
/* 348 */     if (getCompareoption1() == 1)
/* 349 */       return ">"; 
/* 350 */     if (getCompareoption1() == 2)
/* 351 */       return ">="; 
/* 352 */     if (getCompareoption1() == 3)
/* 353 */       return "<"; 
/* 354 */     if (getCompareoption1() == 4)
/* 355 */       return "<="; 
/* 356 */     if (getCompareoption1() == 5)
/* 357 */       return "=="; 
/* 358 */     if (getCompareoption1() == 6)
/* 359 */       return "!="; 
/* 360 */     if (getCompareoption1() == 9)
/* 361 */       return "$CONTAINS"; 
/* 362 */     if (getCompareoption1() == 10) {
/* 363 */       return "$NOTCONTAINS";
/*     */     }
/* 365 */     return "";
/*     */   }
/*     */ 
/*     */   
/*     */   private String getRelation4IK(int paramInt) {
/* 370 */     if (paramInt == 1) {
/*     */       
/* 372 */       if ((this.htmltype.equals("1") && this.fieldtype.equals("1")) || this.htmltype.equals("2"))
/*     */       
/* 374 */       { if (getCompareoption1() == 5)
/* 375 */           return "=="; 
/* 376 */         if (getCompareoption1() == 6)
/* 377 */           return "!="; 
/* 378 */         if (getCompareoption1() == 9)
/* 379 */           return "$CONTAINS"; 
/* 380 */         if (getCompareoption1() == 10)
/* 381 */           return "$NOTCONTAINS";  }
/* 382 */       else if ((this.htmltype.equals("1") && !this.fieldtype.equals("1")) || (this.htmltype.equals("3") && (this.fieldtype.equals("2") || this.fieldtype.equals("19") || this.fieldtype.equals("_level"))))
/*     */       
/* 384 */       { if (getCompareoption1() == 1)
/* 385 */           return ">"; 
/* 386 */         if (getCompareoption1() == 2)
/* 387 */           return ">="; 
/* 388 */         if (getCompareoption1() == 3)
/* 389 */           return "<"; 
/* 390 */         if (getCompareoption1() == 4)
/* 391 */           return "<="; 
/* 392 */         if (getCompareoption1() == 5)
/* 393 */           return "=="; 
/* 394 */         if (getCompareoption1() == 6)
/* 395 */           return "!=";  }
/* 396 */       else { if (this.htmltype.equals("4"))
/*     */         {
/* 398 */           return "=="; } 
/* 399 */         if (this.htmltype.equals("5")) {
/*     */           
/* 401 */           if (getCompareoption1() == 5)
/* 402 */             return "=="; 
/* 403 */           if (getCompareoption1() == 6)
/* 404 */             return "!="; 
/* 405 */           if (getCompareoption1() == 7)
/* 406 */             return "$BROWSERINCLUDINGLOWER"; 
/* 407 */           if (getCompareoption1() == 8)
/* 408 */             return "$NOTBROWSERINCLUDINGLOWER"; 
/* 409 */           if (getCompareoption1() == 9)
/* 410 */             return "$BROWSERCONTAINS"; 
/* 411 */           if (getCompareoption1() == 10)
/* 412 */             return "$NOTBROWSERCONTAINS"; 
/* 413 */         } else if (this.htmltype.equals("3")) {
/*     */           
/* 415 */           if (getCompareoption1() == 7 || getCompareoption1() == 9)
/*     */           {
/* 417 */             return "$BROWSERCONTAINS";
/*     */           }
/* 419 */           if (getCompareoption1() == 8 || getCompareoption1() == 10)
/*     */           {
/* 421 */             return "$NOTBROWSERCONTAINS";
/*     */           }
/*     */           
/* 424 */           if (getCompareoption1() == 11)
/*     */           {
/* 426 */             return "$BROWSERINCLUDINGLOWER";
/*     */           }
/* 428 */           if (getCompareoption1() == 12)
/*     */           {
/* 430 */             return "$NOTBROWSERINCLUDINGLOWER";
/*     */           }
/* 432 */         } else if (this.htmltype.equals("9")) {
/* 433 */           if (getCompareoption1() == 7)
/* 434 */             return "$INLOCATION"; 
/* 435 */           if (getCompareoption1() == 8) {
/* 436 */             return "$NOTINLOCATION";
/*     */           }
/*     */         }  }
/*     */     
/*     */     } else {
/*     */       
/* 442 */       if (getCompareoption2() == 1)
/* 443 */         return ">"; 
/* 444 */       if (getCompareoption2() == 2)
/* 445 */         return ">="; 
/* 446 */       if (getCompareoption2() == 3)
/* 447 */         return "<"; 
/* 448 */       if (getCompareoption2() == 4)
/* 449 */         return "<="; 
/* 450 */       if (getCompareoption2() == 5)
/* 451 */         return "=="; 
/* 452 */       if (getCompareoption2() == 6)
/* 453 */         return "!="; 
/*     */     } 
/* 455 */     return "";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static ESBReturnRuleExpressionBean getExpressionBean(int paramInt) {
/* 464 */     ESBReturnRuleExpressionBean eSBReturnRuleExpressionBean = null;
/* 465 */     RecordSet recordSet = new RecordSet();
/* 466 */     byte b = -1;
/* 467 */     String str = "select * from esbreturnrule_expressionbase where id=" + paramInt;
/* 468 */     recordSet.executeSql(str);
/* 469 */     if (recordSet.next()) {
/* 470 */       eSBReturnRuleExpressionBean = new ESBReturnRuleExpressionBean();
/* 471 */       eSBReturnRuleExpressionBean.setId(Util.getIntValue(recordSet.getString("id")));
/* 472 */       eSBReturnRuleExpressionBean.setDatafield(Util.getIntValue(recordSet.getString("datafield")));
/* 473 */       eSBReturnRuleExpressionBean.setDatafieldlabel(Util.null2String(recordSet.getString("datafieldtext")));
/* 474 */       eSBReturnRuleExpressionBean.setTypehrm(Util.getIntValue(recordSet.getString("typehrm"), -1));
/* 475 */       eSBReturnRuleExpressionBean.setHtmltype(Util.null2String(recordSet.getString("htmltype")));
/* 476 */       eSBReturnRuleExpressionBean.setFieldtype(Util.null2String(recordSet.getString("fieldtype")));
/* 477 */       eSBReturnRuleExpressionBean.setDbtype(Util.null2String(recordSet.getString("dbtype")));
/* 478 */       eSBReturnRuleExpressionBean.setCompareoption1(Util.getIntValue(recordSet.getString("compareoption1"), -1));
/* 479 */       eSBReturnRuleExpressionBean.setCompareoption2(Util.getIntValue(recordSet.getString("compareoption2"), -1));
/* 480 */       eSBReturnRuleExpressionBean.setElementtext1(Util.null2String(recordSet.getString("elementlabel1")));
/* 481 */       eSBReturnRuleExpressionBean.setElementvalue1(Util.null2String(recordSet.getString("elementvalue1")));
/* 482 */       eSBReturnRuleExpressionBean.setElementvalue2(Util.null2String(recordSet.getString("elementvalue2")));
/* 483 */       eSBReturnRuleExpressionBean.setParamtype(Util.getIntValue(recordSet.getString("paramtype"), -1));
/* 484 */       eSBReturnRuleExpressionBean.setValuetype(Util.getIntValue(recordSet.getString("valuetype"), -1));
/* 485 */       eSBReturnRuleExpressionBean.setRedius(Util.null2String(recordSet.getString("redius")));
/* 486 */       eSBReturnRuleExpressionBean.setNodeId(Util.null2String(recordSet.getString("nodeId")));
/* 487 */       eSBReturnRuleExpressionBean.setMeetCondition(Util.null2String(recordSet.getString("meetCondition")));
/* 488 */       eSBReturnRuleExpressionBean.setJingdu(Util.null2String(recordSet.getString("jingdu")));
/* 489 */       eSBReturnRuleExpressionBean.setWeidu(Util.null2String(recordSet.getString("weidu")));
/* 490 */       eSBReturnRuleExpressionBean.setRulesrc(Util.getIntValue("" + b, -1));
/*     */     } 
/* 492 */     return eSBReturnRuleExpressionBean;
/*     */   }
/*     */   
/*     */   public int getId() {
/* 496 */     return this.id;
/*     */   }
/*     */   
/*     */   public void setId(int paramInt) {
/* 500 */     this.id = paramInt;
/*     */   }
/*     */   
/*     */   public int getRuleid() {
/* 504 */     return this.ruleid;
/*     */   }
/*     */   
/*     */   public void setRuleid(int paramInt) {
/* 508 */     this.ruleid = paramInt;
/*     */   }
/*     */   
/*     */   public int getDatafield() {
/* 512 */     return this.datafield;
/*     */   }
/*     */   
/*     */   public void setDatafield(int paramInt) {
/* 516 */     this.datafield = paramInt;
/*     */   }
/*     */   
/*     */   public String getDatafieldlabel() {
/* 520 */     return this.datafieldlabel;
/*     */   }
/*     */   
/*     */   public void setDatafieldlabel(String paramString) {
/* 524 */     this.datafieldlabel = paramString;
/*     */   }
/*     */   
/*     */   public String getHtmltype() {
/* 528 */     return this.htmltype;
/*     */   }
/*     */   
/*     */   public void setHtmltype(String paramString) {
/* 532 */     this.htmltype = paramString;
/*     */   }
/*     */   
/*     */   public String getFieldtype() {
/* 536 */     return this.fieldtype;
/*     */   }
/*     */   
/*     */   public void setFieldtype(String paramString) {
/* 540 */     this.fieldtype = paramString;
/*     */   }
/*     */   
/*     */   public int getTypehrm() {
/* 544 */     return this.typehrm;
/*     */   }
/*     */   
/*     */   public void setTypehrm(int paramInt) {
/* 548 */     this.typehrm = paramInt;
/*     */   }
/*     */   
/*     */   public int getCompareoption1() {
/* 552 */     return this.compareoption1;
/*     */   }
/*     */   
/*     */   public void setCompareoption1(int paramInt) {
/* 556 */     this.compareoption1 = paramInt;
/*     */   }
/*     */   
/*     */   public int getCompareoption2() {
/* 560 */     return this.compareoption2;
/*     */   }
/*     */   
/*     */   public void setCompareoption2(int paramInt) {
/* 564 */     this.compareoption2 = paramInt;
/*     */   }
/*     */   
/*     */   public String getBrowservalue() {
/* 568 */     return this.browservalue;
/*     */   }
/*     */   
/*     */   public void setBrowservalue(String paramString) {
/* 572 */     this.browservalue = paramString;
/*     */   }
/*     */   
/*     */   public String getBrowserspantext() {
/* 576 */     return this.browserspantext;
/*     */   }
/*     */   
/*     */   public void setBrowserspantext(String paramString) {
/* 580 */     this.browserspantext = paramString;
/*     */   }
/*     */   
/*     */   public String getBrowserspanlabel() {
/* 584 */     return this.browserspanlabel;
/*     */   }
/*     */   
/*     */   public void setBrowserspanlabel(String paramString) {
/* 588 */     this.browserspanlabel = paramString;
/*     */   }
/*     */   
/*     */   public int getSelectvalue1() {
/* 592 */     return this.selectvalue1;
/*     */   }
/*     */   
/*     */   public void setSelectvalue1(int paramInt) {
/* 596 */     this.selectvalue1 = paramInt;
/*     */   }
/*     */   
/*     */   public String getTextvalue1() {
/* 600 */     return this.textvalue1;
/*     */   }
/*     */   
/*     */   public void setTextvalue1(String paramString) {
/* 604 */     this.textvalue1 = paramString;
/*     */   }
/*     */   
/*     */   public String getTextvalue2() {
/* 608 */     return this.textvalue2;
/*     */   }
/*     */   
/*     */   public void setTextvalue2(String paramString) {
/* 612 */     this.textvalue2 = paramString;
/*     */   }
/*     */   
/*     */   public String getCheckboxvalue1() {
/* 616 */     return this.checkboxvalue1;
/*     */   }
/*     */   
/*     */   public void setCheckboxvalue1(String paramString) {
/* 620 */     this.checkboxvalue1 = paramString;
/*     */   }
/*     */   
/*     */   public String getElementvalue1() {
/* 624 */     return this.elementvalue1;
/*     */   }
/*     */   
/*     */   public void setElementvalue1() {
/* 628 */     if ((this.htmltype.equals("1") && this.fieldtype.equals("1")) || this.htmltype.equals("2")) {
/*     */       
/* 630 */       this.elementvalue1 = this.textvalue1;
/* 631 */       this.compareoption2 = -1;
/* 632 */     } else if ((this.htmltype.equals("1") && !this.fieldtype.equals("1")) || (this.htmltype.equals("3") && (this.fieldtype.equals("2") || this.fieldtype.equals("19") || this.fieldtype.equals("_level")))) {
/*     */       
/* 634 */       this.elementvalue1 = this.textvalue1;
/* 635 */       setElementvalue2(this.textvalue2);
/* 636 */     } else if (this.htmltype.equals("4")) {
/*     */       
/* 638 */       this.elementvalue1 = this.checkboxvalue1;
/* 639 */       this.compareoption2 = -1;
/* 640 */     } else if (this.htmltype.equals("5")) {
/*     */ 
/*     */       
/* 643 */       if (this.fieldtype.equals("2")) {
/* 644 */         this.elementvalue1 = this.selectvalue1 + "";
/* 645 */         this.compareoption2 = -1;
/*     */       } else {
/*     */         
/* 648 */         String str = this.browservalue;
/* 649 */         if (str.startsWith(",")) str = str.substring(1); 
/* 650 */         this.elementvalue1 = str;
/* 651 */         setElementtext1(str);
/* 652 */         this.compareoption2 = -1;
/*     */       } 
/* 654 */     } else if (this.htmltype.equals("3")) {
/*     */       
/* 656 */       this.elementvalue1 = this.browservalue;
/* 657 */       setElementtext1(this.browserspantext);
/* 658 */       this.compareoption2 = -1;
/*     */     } 
/* 660 */     if (this.valuetype == 3) {
/*     */       
/* 662 */       this.elementvalue1 = this.valuevarid + "";
/* 663 */       this.elementtext1 = this.textvalue1 + "";
/* 664 */       this.compareoption2 = -1;
/* 665 */     } else if (this.valuetype == 4) {
/*     */       
/* 667 */       this.elementvalue1 = this.browservalue;
/* 668 */       setElementtext1(this.browserspantext);
/* 669 */       this.compareoption2 = -1;
/*     */     } 
/*     */   }
/*     */   
/*     */   public void setElementvalue1(String paramString) {
/* 674 */     this.elementvalue1 = paramString;
/*     */   }
/*     */   
/*     */   public String getElementtext1() {
/* 678 */     return this.elementtext1;
/*     */   }
/*     */   
/*     */   public void setElementtext1(String paramString) {
/* 682 */     this.elementtext1 = paramString;
/*     */   }
/*     */   
/*     */   public String getElementvalue2() {
/* 686 */     return this.elementvalue2;
/*     */   }
/*     */   
/*     */   public void setElementvalue2(String paramString) {
/* 690 */     this.elementvalue2 = paramString;
/*     */   }
/*     */   
/*     */   public int getParamtype() {
/* 694 */     return this.paramtype;
/*     */   }
/*     */   
/*     */   public void setParamtype(int paramInt) {
/* 698 */     this.paramtype = paramInt;
/*     */   }
/*     */   
/*     */   public int getValuetype() {
/* 702 */     return this.valuetype;
/*     */   }
/*     */   
/*     */   public void setValuetype(int paramInt) {
/* 706 */     this.valuetype = paramInt;
/*     */   }
/*     */   
/*     */   public String getDbtype() {
/* 710 */     return this.dbtype;
/*     */   }
/*     */   
/*     */   public void setDbtype(String paramString) {
/* 714 */     this.dbtype = paramString;
/*     */   }
/*     */   
/*     */   public int getValuevarid() {
/* 718 */     return this.valuevarid;
/*     */   }
/*     */   
/*     */   public void setValuevarid(int paramInt) {
/* 722 */     this.valuevarid = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean isMapitem() {
/* 730 */     return this.isMapitem;
/*     */   }
/*     */   
/*     */   public void setMapitem(boolean paramBoolean) {
/* 734 */     this.isMapitem = paramBoolean;
/*     */   }
/*     */   
/*     */   public String getLinkid() {
/* 738 */     return this.linkid;
/*     */   }
/*     */   
/*     */   public void setLinkid(String paramString) {
/* 742 */     this.linkid = paramString;
/*     */   }
/*     */   
/*     */   public int getRulesrc() {
/* 746 */     return this.rulesrc;
/*     */   }
/*     */   
/*     */   public void setRulesrc(int paramInt) {
/* 750 */     this.rulesrc = paramInt;
/*     */   }
/*     */   
/*     */   public String getRedius() {
/* 754 */     return this.redius;
/*     */   }
/*     */   
/*     */   public void setRedius(String paramString) {
/* 758 */     this.redius = paramString;
/*     */   }
/*     */   
/*     */   public String getNodeId() {
/* 762 */     return this.nodeId;
/*     */   }
/*     */   
/*     */   public void setNodeId(String paramString) {
/* 766 */     this.nodeId = paramString;
/*     */   }
/*     */   
/*     */   public String getMeetCondition() {
/* 770 */     return this.meetCondition;
/*     */   }
/*     */   
/*     */   public void setMeetCondition(String paramString) {
/* 774 */     this.meetCondition = paramString;
/*     */   }
/*     */   
/*     */   public String getJingdu() {
/* 778 */     return this.jingdu;
/*     */   }
/*     */   
/*     */   public void setJingdu(String paramString) {
/* 782 */     this.jingdu = paramString;
/*     */   }
/*     */   
/*     */   public String getWeidu() {
/* 786 */     return this.weidu;
/*     */   }
/*     */   
/*     */   public void setWeidu(String paramString) {
/* 790 */     this.weidu = paramString;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/action/ESBReturnRuleExpressionBean.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */