/*    */ package weaver.workflow.logging;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class LoggerFactory
/*    */ {
/*    */   private static final String loggerName = "workflow";
/*    */   
/*    */   public static Logger getLogger(String paramString1, String paramString2) {
/* 12 */     if ("".equals(paramString1))
/* 13 */       paramString1 = "workflow"; 
/* 14 */     Log4JLogger log4JLogger = new Log4JLogger();
/* 15 */     log4JLogger.setClassname(paramString2);
/* 16 */     log4JLogger.init(paramString1);
/* 17 */     return log4JLogger;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public static Logger getLogger(Class paramClass) {
/* 26 */     return getLogger("workflow", paramClass.getCanonicalName());
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public static Logger getLogger(String paramString) {
/* 35 */     return getLogger("workflow", paramString);
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public static Logger getLogger() {
/* 43 */     String str = Thread.currentThread().getStackTrace()[2].getClassName();
/* 44 */     return getLogger("workflow", str);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workflow/logging/LoggerFactory.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */