/*    */ package weaver.cpc.util;
/*    */ 
/*    */ import org.json.JSONArray;
/*    */ import org.json.JSONObject;
/*    */ import weaver.general.BaseBean;
/*    */ import weaver.hrm.User;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class CpcDetailColUtil
/*    */   extends BaseBean
/*    */ {
/*    */   public JSONArray getDetailColumnConf(String paramString, User paramUser) {
/* 32 */     JSONArray jSONArray = new JSONArray();
/* 33 */     JSONObject jSONObject = new JSONObject();
/*    */ 
/*    */ 
/*    */     
/*    */     try {
/* 38 */       if ("CpcShareHolder".equalsIgnoreCase(paramString))
/*    */       {
/* 40 */         jSONObject = new JSONObject();
/* 41 */         jSONObject.put("width", "10%");
/* 42 */         jSONObject.put("colname", SystemEnv.getHtmlLabelName(27336, paramUser.getLanguage()));
/* 43 */         jSONObject.put("itemhtml", "<span class='browser' completeurl='/data.jsp?type=23&isdata=1' browserurl='/systeminfo/BrowserMain.jsp?url=/cpt/capital/CapitalBrowser.jsp?sqlwhere=where isdata=1' name='capitalid' isMustInput='2' hasInput='true'  isSingle='true' _callback='loadinfo' ></span>");
/* 44 */         jSONArray.put(jSONObject);
/*    */         
/* 46 */         jSONObject = new JSONObject();
/* 47 */         jSONObject.put("width", "10%");
/* 48 */         jSONObject.put("colname", SystemEnv.getHtmlLabelName(31001, paramUser.getLanguage()));
/* 49 */         jSONObject.put("itemhtml", "<input type='text' name='capitalspec' id='capitalspec' /><span class='mustinput'></span>");
/* 50 */         jSONArray.put(jSONObject);
/*    */ 
/*    */         
/* 53 */         jSONObject = new JSONObject();
/* 54 */         jSONObject.put("width", "10%");
/* 55 */         jSONObject.put("colname", SystemEnv.getHtmlLabelName(31040, paramUser.getLanguage()));
/* 56 */         jSONObject.put("itemhtml", "<input type='text' name='price' id='price' onkeyup=\"value=value.replace(/[^\\d\\.]/g,'')\"  /><span class='mustinput'></span>");
/* 57 */         jSONArray.put(jSONObject);
/*    */         
/* 59 */         jSONObject = new JSONObject();
/* 60 */         jSONObject.put("width", "10%");
/* 61 */         jSONObject.put("colname", SystemEnv.getHtmlLabelName(406, paramUser.getLanguage()));
/* 62 */         jSONObject.put("itemhtml", "<input type='text' name='capitalnum' id='capitalnum' onkeyup=\"value=value.replace(/[^\\d\\.]/g,'')\" /><span class='mustinput'></span>");
/* 63 */         jSONArray.put(jSONObject);
/*    */         
/* 65 */         jSONObject = new JSONObject();
/* 66 */         jSONObject.put("width", "10%");
/* 67 */         jSONObject.put("colname", SystemEnv.getHtmlLabelName(31108, paramUser.getLanguage()));
/* 68 */         jSONObject.put("itemhtml", "<input type='text' name='invoice' id='invoice' />");
/* 69 */         jSONArray.put(jSONObject);
/*    */         
/* 71 */         jSONObject = new JSONObject();
/* 72 */         jSONObject.put("width", "10%");
/* 73 */         jSONObject.put("colname", SystemEnv.getHtmlLabelName(31109, paramUser.getLanguage()));
/* 74 */         jSONObject.put("itemhtml", "<input type='text' name='location' id='location' />");
/* 75 */         jSONArray.put(jSONObject);
/*    */       
/*    */       }
/*    */ 
/*    */     
/*    */     }
/* 81 */     catch (Exception exception) {
/* 82 */       exception.printStackTrace();
/* 83 */       writeLog(exception.getMessage());
/*    */     } 
/*    */     
/* 86 */     return jSONArray;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/cpc/util/CpcDetailColUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */