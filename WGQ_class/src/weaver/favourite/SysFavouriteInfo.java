/*     */ package weaver.favourite;
/*     */ 
/*     */ import java.io.UnsupportedEncodingException;
/*     */ import java.text.SimpleDateFormat;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Date;
/*     */ import java.util.HashMap;
/*     */ import java.util.StringTokenizer;
/*     */ import org.json.JSONArray;
/*     */ import org.json.JSONException;
/*     */ import org.json.JSONObject;
/*     */ import weaver.conn.ConnStatement;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.dateformat.DateTransformer;
/*     */ import weaver.general.ThreadVarLanguage;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ public class SysFavouriteInfo {
/*  20 */   private String favouriteid = "";
/*  21 */   private String sysfavouriteid = "";
/*  22 */   private String searchtitle = "";
/*  23 */   private String pagename = "";
/*  24 */   private String url = "";
/*  25 */   private String importlevel = "";
/*  26 */   private String adddate = "";
/*  27 */   private String id = "";
/*  28 */   private String type = "";
/*  29 */   private int userid = -1;
/*  30 */   private int favouriteObjId = 0;
/*     */   private JSONArray body;
/*     */   private JSONArray columns;
/*  33 */   private int start = 0;
/*  34 */   private int limit = 0;
/*  35 */   private int count = -1;
/*     */ 
/*     */   
/*     */   public String querySysFavourites() throws Exception {
/*  39 */     RecordSet recordSet = new RecordSet();
/*  40 */     String str1 = "";
/*  41 */     this.favouriteid = getFavouriteid();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  49 */     str1 = "select top " + getLimit() + " *  from sysfavourite a, sysfavourite_favourite b  where a.resourceid =" + getUserid() + " and a.id = b.sysfavouriteid  and b.favouriteid =" + this.favouriteid + " and a.id not in (select top " + getStart() + " a.id  from sysfavourite a, sysfavourite_favourite b  where a.resourceid =" + getUserid() + " and a.id = b.sysfavouriteid  and b.favouriteid =" + this.favouriteid + " order by a.importlevel desc,a.adddate desc,a.id desc)  order by a.importlevel desc,a.adddate desc,a.id desc";
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  54 */     String str2 = recordSet.getDBType();
/*  55 */     if (str2.equals("oracle")) {
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/*  66 */       str1 = "select *  from sysfavourite a, sysfavourite_favourite b  where a.resourceid =" + getUserid() + " and a.id = b.sysfavouriteid  and b.favouriteid =" + this.favouriteid + " and a.id not in (select r.* from (select a.id  from sysfavourite a, sysfavourite_favourite b  where a.resourceid =" + getUserid() + " and a.id = b.sysfavouriteid  and b.favouriteid =" + this.favouriteid + " order by a.importlevel desc,a.adddate desc,a.id desc) r where rownum<=" + getStart() + " )  and rownum<=" + getLimit() + " order by a.importlevel desc,a.adddate desc,a.id desc";
/*  67 */     } else if ("mysql".equals(str2)) {
/*  68 */       int i = getLimit() - getStart();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/*  74 */       str1 = "select *  from sysfavourite a, sysfavourite_favourite b  where a.resourceid =" + getUserid() + " and a.id = b.sysfavouriteid  and b.favouriteid =" + this.favouriteid + " order by a.importlevel desc,a.adddate desc,a.id desc limit " + getStart() + "," + i;
/*  75 */     } else if ("postgresql".equals(str2)) {
/*  76 */       int i = getLimit() - getStart();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/*  82 */       str1 = "select *  from sysfavourite a, sysfavourite_favourite b  where a.resourceid =" + getUserid() + " and a.id = b.sysfavouriteid  and b.favouriteid =" + this.favouriteid + " order by a.importlevel desc,a.adddate desc,a.id desc limit " + i + " offset " + getStart();
/*     */     } 
/*  84 */     recordSet.execute(str1);
/*     */ 
/*     */     
/*  87 */     while (recordSet.next()) {
/*     */ 
/*     */       
/*     */       try {
/*  91 */         String str = Util.null2String(recordSet.getString("adddate"));
/*     */         
/*  93 */         DateTransformer dateTransformer = new DateTransformer();
/*  94 */         str = dateTransformer.getLocaleDateTime(str);
/*     */         
/*  96 */         setJsonStringBody(recordSet.getString("id"), recordSet.getString("pagename"), str, recordSet
/*  97 */             .getString("url"), recordSet
/*  98 */             .getString("importlevel"), recordSet.getString("favouritetype"));
/*     */       }
/* 100 */       catch (JSONException jSONException) {
/*     */         
/* 102 */         jSONException.printStackTrace();
/*     */       } 
/*     */     } 
/*     */     
/* 106 */     return getJsonString(this.columns);
/*     */   }
/*     */   
/*     */   public String searchSysFavourites() throws Exception {
/* 110 */     RecordSet recordSet = new RecordSet();
/* 111 */     String str = "";
/* 112 */     this.searchtitle = getSearchtitle();
/*     */     
/* 114 */     str = "select * from sysfavourite a \twhere a.resourceid=" + getUserid() + "\t  and a.pagename like '%" + this.searchtitle + "%' \t  order by importlevel desc,adddate desc,id desc";
/*     */ 
/*     */     
/* 117 */     recordSet.execute(str);
/*     */ 
/*     */     
/* 120 */     while (recordSet.next()) {
/*     */ 
/*     */       
/*     */       try {
/* 124 */         String str1 = Util.null2String(recordSet.getString("adddate"));
/*     */         
/* 126 */         DateTransformer dateTransformer = new DateTransformer();
/* 127 */         str1 = dateTransformer.getLocaleDateTime(str1);
/*     */         
/* 129 */         setJsonStringBody(recordSet.getString("id"), recordSet.getString("pagename"), str1, recordSet
/* 130 */             .getString("url"), recordSet
/* 131 */             .getString("importlevel"), recordSet.getString("favouritetype"));
/*     */       }
/* 133 */       catch (JSONException jSONException) {
/*     */         
/* 135 */         jSONException.printStackTrace();
/*     */       } 
/*     */     } 
/*     */     
/* 139 */     return getJsonString(this.columns);
/*     */   }
/*     */   
/*     */   public String getSysFavouriteUrl(String paramString) {
/* 143 */     RecordSet recordSet = new RecordSet();
/* 144 */     String str = "select * from sysfavourite a \twhere a.id = " + paramString + "\t  order by importlevel desc,adddate desc,id desc";
/*     */ 
/*     */     
/* 147 */     recordSet.execute(str);
/*     */ 
/*     */     
/* 150 */     if (recordSet.next())
/*     */     {
/* 152 */       return "<a href='javascript:void(0)' onclick=\"javascript:openFullWindowForLong('" + recordSet.getString("url") + "');\">" + recordSet.getString("pagename") + "</a>";
/*     */     }
/* 154 */     return "";
/*     */   }
/*     */   
/*     */   public String getFavouriteName(String paramString1, String paramString2) {
/* 158 */     String str = "";
/* 159 */     int i = Util.getIntValue(paramString2, 7);
/* 160 */     if ("-1".equals(paramString1)) {
/* 161 */       str = SystemEnv.getHtmlLabelName(18030, i);
/*     */     } else {
/* 163 */       String str1 = "select favouritename from favourite where id = " + paramString1;
/* 164 */       RecordSet recordSet = new RecordSet();
/* 165 */       recordSet.executeSql(str1);
/* 166 */       if (recordSet.next()) {
/* 167 */         str = recordSet.getString("favouritename");
/*     */       }
/*     */     } 
/* 170 */     return str;
/*     */   }
/*     */ 
/*     */   
/*     */   public String getFavouriteType(String paramString1, String paramString2) {
/* 175 */     int i = Util.getIntValue(paramString2, 7);
/* 176 */     String str = "";
/* 177 */     if (paramString1.equals("1")) {
/*     */ 
/*     */       
/* 180 */       str = SystemEnv.getHtmlLabelName(30041, i);
/* 181 */       return "<img title=\"" + str + "\" src=\"/images_face/ecologyFace_2/LeftMenuIcon/MyDoc_wev8.png\"/>";
/*     */     } 
/* 183 */     if (paramString1.equals("2")) {
/*     */ 
/*     */       
/* 186 */       str = SystemEnv.getHtmlLabelName(33569, i);
/* 187 */       return "<img title=\"" + str + "\" src=\"/images_face/ecologyFace_2/LeftMenuIcon/MyWorkflow_wev8.png\"/>";
/*     */     } 
/* 189 */     if (paramString1.equals("3")) {
/*     */ 
/*     */       
/* 192 */       str = SystemEnv.getHtmlLabelName(30046, i);
/* 193 */       return "<img title=\"" + str + "\" src=\"/images_face/ecologyFace_2/LeftMenuIcon/MyProject_wev8.png\"/>";
/*     */     } 
/* 195 */     if (paramString1.equals("4")) {
/*     */ 
/*     */       
/* 198 */       str = SystemEnv.getHtmlLabelName(30043, i);
/* 199 */       return "<img title=\"" + str + "\" src=\"/images_face/ecologyFace_2/LeftMenuIcon/MyCRM_wev8.png\"/>";
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/* 204 */     str = SystemEnv.getHtmlLabelName(25740, i);
/* 205 */     return "<img title=\"" + str + "\" src=\"/images/filetypeicons/html_wev8.png\"/>";
/*     */   }
/*     */ 
/*     */   
/*     */   public String getImportLevel2(String paramString) {
/* 210 */     if (paramString.equals("1"))
/*     */     {
/* 212 */       return "<span style='vertical-align:middle'>" + SystemEnv.getHtmlLabelName(154, ThreadVarLanguage.getLang()) + "</span>";
/*     */     }
/* 214 */     if (paramString.equals("2"))
/*     */     {
/* 216 */       return "<span style='vertical-align:middle'>" + SystemEnv.getHtmlLabelName(22241, ThreadVarLanguage.getLang()) + "</span>";
/*     */     }
/*     */ 
/*     */     
/* 220 */     return "<span style='vertical-align:middle'>" + SystemEnv.getHtmlLabelName(25397, ThreadVarLanguage.getLang()) + "</span>";
/*     */   }
/*     */ 
/*     */   
/*     */   private int querySysFavouritesCount() {
/* 225 */     RecordSet recordSet = new RecordSet();
/* 226 */     int i = -1;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 232 */     String str = "select count(*)   from sysfavourite_favourite a, sysfavourite b  where a.favouriteid =" + getFavouriteid() + "   and a.sysfavouriteid = b.id    and a.resourceid = b.resourceid    and a.resourceid =" + getUserid();
/* 233 */     recordSet.execute(str);
/*     */ 
/*     */     
/* 236 */     while (recordSet.next())
/*     */     {
/* 238 */       i = recordSet.getInt(1);
/*     */     }
/* 240 */     return i;
/*     */   }
/*     */ 
/*     */   
/*     */   public String deleteFavourites() throws Exception {
/* 245 */     RecordSet recordSet = new RecordSet();
/* 246 */     String str = "";
/* 247 */     this.sysfavouriteid = getSysfavouriteid();
/*     */     
/* 249 */     int i = getUserid();
/* 250 */     str = "delete from sysfavourite_favourite where sysfavouriteid in(" + this.sysfavouriteid + ") and resourceid = " + i;
/*     */     
/* 252 */     boolean bool = recordSet.execute(str);
/*     */     
/* 254 */     if (bool) {
/*     */       
/* 256 */       str = "delete from sysfavourite where id in(" + this.sysfavouriteid + ") and resourceid = " + i;
/* 257 */       bool = recordSet.execute(str);
/*     */     } 
/* 259 */     if (bool)
/*     */     {
/* 261 */       setJsonStringBody(this.sysfavouriteid, "", "", "", "", "");
/*     */     }
/* 263 */     return getJsonString(this.columns);
/*     */   }
/*     */ 
/*     */   
/*     */   public String editFavourites() throws Exception {
/* 268 */     int i = getUserid();
/*     */     
/* 270 */     String str = "update sysfavourite set pagename=?,importlevel=" + getImportlevel() + ",favouritetype=" + getType() + " where id=" + getSysfavouriteid() + " and resourceid = " + i;
/*     */ 
/*     */     
/* 273 */     ConnStatement connStatement = new ConnStatement();
/*     */     
/* 275 */     try { connStatement.setStatementSql(str);
/* 276 */       connStatement.setString(1, getPagename());
/* 277 */       int j = connStatement.executeUpdate();
/* 278 */       setJsonStringBody(getSysfavouriteid(), getPagename(), "", "", getImportlevel(), getType()); }
/* 279 */     catch (Exception exception) {  }
/*     */     finally
/* 281 */     { connStatement.close(); }
/*     */     
/* 283 */     return getJsonString(this.columns);
/*     */   }
/*     */ 
/*     */   
/*     */   public String saveFavourites(String paramString) throws Exception {
/* 288 */     RecordSet recordSet = new RecordSet();
/*     */     
/*     */     try {
/* 291 */       JSONObject jSONObject = new JSONObject(paramString);
/* 292 */       JSONArray jSONArray = jSONObject.getJSONArray("databody");
/* 293 */       String str = "";
/* 294 */       for (byte b = 0; b < jSONArray.length(); b++) {
/*     */         
/* 296 */         JSONObject jSONObject1 = jSONArray.getJSONObject(b);
/*     */         
/* 298 */         String str1 = (new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")).format(new Date());
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */         
/* 305 */         char c = Util.getSeparator();
/* 306 */         String str2 = jSONObject1.getString("pagename");
/*     */         
/* 308 */         String str3 = getRealyLink(jSONObject1.getString("linkid"), 
/* 309 */             getType());
/*     */ 
/*     */ 
/*     */         
/* 313 */         String str4 = String.valueOf(getUserid()) + c + str1 + c + str2 + c + str3 + c + getImportlevel() + c + getType();
/*     */         
/* 315 */         recordSet.executeProc("SysFavourite_Insert", str4);
/* 316 */         if (recordSet.next())
/*     */         {
/* 318 */           str = recordSet.getString(1);
/*     */         }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */         
/* 325 */         String str5 = "insert into sysfavourite_favourite(favouriteid,sysfavouriteid,resourceid) values(" + getFavouriteid() + "," + str + "," + getUserid() + ")";
/* 326 */         setJsonStringBody(str, str2, str1, str3, 
/* 327 */             getImportlevel(), getType());
/* 328 */         recordSet.execute(str5);
/*     */       } 
/*     */       
/* 331 */       return getJsonString(this.columns);
/*     */     }
/* 333 */     catch (Exception exception) {
/*     */       
/* 335 */       exception.printStackTrace();
/*     */       
/* 337 */       return "";
/*     */     } 
/*     */   }
/*     */   
/*     */   public String saveFavouritesFromPage() throws Exception {
/* 342 */     RecordSet recordSet = new RecordSet();
/*     */     
/*     */     try {
/* 345 */       String str1 = getFavouriteid();
/* 346 */       StringTokenizer stringTokenizer = new StringTokenizer(str1, ",");
/* 347 */       String str2 = "";
/* 348 */       while (stringTokenizer.hasMoreTokens()) {
/*     */ 
/*     */         
/* 351 */         String str3 = (new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")).format(new Date());
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */         
/* 358 */         char c = Util.getSeparator();
/* 359 */         String str4 = getPagename();
/*     */         
/* 361 */         String str5 = getUrl();
/*     */         
/* 363 */         String str6 = getFavTypeFromUrl(str5);
/* 364 */         if ("".equals(str6)) {
/* 365 */           str6 = getType();
/*     */         }
/*     */         
/* 368 */         int i = getFavobjIdFromUrl(str5);
/* 369 */         if (i == 0) {
/* 370 */           i = this.favouriteObjId;
/*     */         }
/*     */ 
/*     */ 
/*     */ 
/*     */         
/* 376 */         String str7 = String.valueOf(getUserid()) + c + str3 + c + str4 + c + str5 + c + getImportlevel() + c + str6;
/*     */         
/* 378 */         recordSet.executeProc("SysFavourite_Insert", str7);
/* 379 */         if (recordSet.next())
/*     */         {
/* 381 */           str2 = recordSet.getString(1);
/*     */         }
/* 383 */         String str8 = stringTokenizer.nextToken().toString();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */         
/* 390 */         String str9 = "insert into sysfavourite_favourite(favouriteid,sysfavouriteid,resourceid) values(" + str8 + "," + str2 + "," + getUserid() + ")";
/* 391 */         recordSet.execute(str9);
/*     */         
/* 393 */         recordSet.execute("update SysFavourite set favouriteObjId=" + i + " where id=" + str2);
/*     */       } 
/*     */       
/* 396 */       return "true";
/*     */     }
/* 398 */     catch (Exception exception) {
/*     */       
/* 400 */       exception.printStackTrace();
/*     */       
/* 402 */       return "";
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String getFavTypeFromUrl(String paramString) {
/* 411 */     String str = "";
/* 412 */     if (!"".equals(paramString)) {
/* 413 */       if (paramString.indexOf("/workflow/request/ManageRequestNoForm.jsp") != -1 || paramString
/* 414 */         .indexOf("/workflow/request/ManageRequestNoFormBill") != -1 || paramString
/* 415 */         .indexOf("/workflow/request/ManageRequestNoFormMode") != -1 || paramString
/* 416 */         .indexOf("/workflow/request/ViewRequest.jsp") != -1) {
/* 417 */         str = "2";
/*     */       }
/*     */       
/* 420 */       if (paramString.indexOf("/docs/docs/DocDspExt.jsp") != -1 || paramString
/* 421 */         .indexOf("/docs/docs/DocEditExt.jsp") != -1 || paramString
/* 422 */         .indexOf("/docs/docs/DocDsp.jsp") != -1 || paramString
/* 423 */         .indexOf("/docs/docs/DocEdit.jsp") != -1) {
/* 424 */         str = "1";
/*     */       }
/*     */       
/* 427 */       if (paramString.indexOf("/CRM/data/ViewCustomer.jsp") != -1) {
/* 428 */         str = "4";
/*     */       }
/*     */       
/* 431 */       if (paramString.indexOf("/proj/data/ProjTab.jsp") != -1 || paramString.indexOf("/proj/data/ViewProject.jsp") != -1) {
/* 432 */         str = "3";
/*     */       }
/*     */     } 
/* 435 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private int getFavobjIdFromUrl(String paramString) {
/* 444 */     int i = 0;
/* 445 */     if (!"".equals(paramString)) {
/* 446 */       if (paramString.indexOf("/workflow/request/ManageRequestNoForm.jsp") != -1 || paramString
/* 447 */         .indexOf("/workflow/request/ManageRequestNoFormBill") != -1 || paramString
/* 448 */         .indexOf("/workflow/request/ManageRequestNoFormMode") != -1 || paramString
/* 449 */         .indexOf("/workflow/request/ViewRequest.jsp") != -1) {
/*     */ 
/*     */         
/* 452 */         paramString = paramString.replaceFirst("\\?", "&");
/* 453 */         HashMap<Object, Object> hashMap = new HashMap<>();
/*     */         try {
/* 455 */           RequestUtil.parseParameters(hashMap, paramString, "utf-8");
/* 456 */           if (hashMap.containsKey("requestid")) {
/* 457 */             String[] arrayOfString = (String[])hashMap.get("requestid");
/* 458 */             if (arrayOfString != null && arrayOfString.length > 0) {
/* 459 */               i = Util.getIntValue(arrayOfString[0], -1);
/*     */             }
/*     */           } 
/* 462 */         } catch (UnsupportedEncodingException unsupportedEncodingException) {
/* 463 */           unsupportedEncodingException.printStackTrace();
/*     */         } 
/*     */       } 
/*     */       
/* 467 */       if (paramString.indexOf("/docs/docs/DocDspExt.jsp") != -1 || paramString
/* 468 */         .indexOf("/docs/docs/DocEditExt.jsp") != -1 || paramString
/* 469 */         .indexOf("/docs/docs/DocDsp.jsp") != -1 || paramString
/* 470 */         .indexOf("/docs/docs/DocEdit.jsp") != -1) {
/*     */ 
/*     */         
/* 473 */         paramString = paramString.replaceFirst("\\?", "&");
/* 474 */         HashMap<Object, Object> hashMap = new HashMap<>();
/*     */         try {
/* 476 */           RequestUtil.parseParameters(hashMap, paramString, "utf-8");
/* 477 */           if (hashMap.containsKey("id")) {
/* 478 */             String[] arrayOfString = (String[])hashMap.get("id");
/* 479 */             if (arrayOfString != null && arrayOfString.length > 0) {
/* 480 */               i = Util.getIntValue(arrayOfString[0], -1);
/*     */             }
/*     */           } 
/* 483 */         } catch (UnsupportedEncodingException unsupportedEncodingException) {
/* 484 */           unsupportedEncodingException.printStackTrace();
/*     */         } 
/*     */       } 
/*     */       
/* 488 */       if (paramString.indexOf("/CRM/data/ViewCustomer.jsp") != -1) {
/*     */         
/* 490 */         paramString = paramString.replaceFirst("\\?", "&");
/* 491 */         HashMap<Object, Object> hashMap = new HashMap<>();
/*     */         try {
/* 493 */           RequestUtil.parseParameters(hashMap, paramString, "utf-8");
/* 494 */           if (hashMap.containsKey("CustomerID")) {
/* 495 */             String[] arrayOfString = (String[])hashMap.get("CustomerID");
/* 496 */             if (arrayOfString != null && arrayOfString.length > 0) {
/* 497 */               i = Util.getIntValue(arrayOfString[0], -1);
/*     */             }
/*     */           } 
/* 500 */         } catch (UnsupportedEncodingException unsupportedEncodingException) {
/* 501 */           unsupportedEncodingException.printStackTrace();
/*     */         } 
/*     */       } 
/*     */       
/* 505 */       if (paramString.indexOf("/proj/data/ProjTab.jsp") != -1 || paramString.indexOf("/proj/data/ViewProject.jsp") != -1) {
/*     */         
/* 507 */         paramString = paramString.replaceFirst("\\?", "&");
/* 508 */         HashMap<Object, Object> hashMap = new HashMap<>();
/*     */         try {
/* 510 */           RequestUtil.parseParameters(hashMap, paramString, "utf-8");
/* 511 */           if (hashMap.containsKey("ProjID")) {
/* 512 */             String[] arrayOfString = (String[])hashMap.get("ProjID");
/* 513 */             if (arrayOfString != null && arrayOfString.length > 0) {
/* 514 */               i = Util.getIntValue(arrayOfString[0], -1);
/*     */             }
/*     */           } 
/* 517 */         } catch (UnsupportedEncodingException unsupportedEncodingException) {
/* 518 */           unsupportedEncodingException.printStackTrace();
/*     */         } 
/*     */       } 
/*     */     } 
/* 522 */     return i;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public String appendFavourites() {
/* 528 */     RecordSet recordSet = new RecordSet();
/* 529 */     String str1 = getSysfavouriteid();
/* 530 */     String str2 = getFavouriteid();
/* 531 */     StringTokenizer stringTokenizer1 = new StringTokenizer(str1, ",");
/* 532 */     ArrayList<String> arrayList = new ArrayList();
/* 533 */     while (stringTokenizer1.hasMoreTokens()) {
/*     */       
/* 535 */       String str = stringTokenizer1.nextToken();
/* 536 */       arrayList.add(str);
/*     */     } 
/* 538 */     StringTokenizer stringTokenizer2 = new StringTokenizer(str2, ",");
/* 539 */     boolean bool = false;
/* 540 */     while (stringTokenizer2.hasMoreTokens()) {
/*     */       
/* 542 */       String str = stringTokenizer2.nextToken();
/* 543 */       for (String str3 : arrayList) {
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */         
/* 549 */         String str4 = "select b.id  from sysfavourite_favourite a, sysfavourite b, sysfavourite c  where a.favouriteid =" + str + "   and a.resourceid=" + getUserid() + "   and a.sysfavouriteid = b.id    and c.id =" + str3 + "   and c.url = b.url";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */         
/* 558 */         String str5 = (new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")).format(new Date());
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */         
/* 565 */         char c = Util.getSeparator();
/*     */ 
/*     */ 
/*     */ 
/*     */         
/* 570 */         String str6 = "select resourceid, adddate, pagename, url, importlevel,favouritetype from sysfavourite where id = " + str3;
/* 571 */         recordSet.executeSql(str6);
/* 572 */         boolean bool1 = false;
/* 573 */         String str7 = "";
/* 574 */         String str8 = "";
/* 575 */         String str9 = "";
/* 576 */         String str10 = "";
/* 577 */         if (recordSet.next()) {
/* 578 */           bool1 = true;
/* 579 */           str7 = recordSet.getString("pagename");
/* 580 */           str8 = recordSet.getString("url");
/* 581 */           str9 = recordSet.getString("importlevel");
/* 582 */           str10 = recordSet.getString("favouritetype");
/*     */         } 
/*     */         
/* 585 */         String str11 = getUserid() + "" + c + str5 + c + str7 + c + str8 + c + str9 + c + str10;
/* 586 */         String str12 = "";
/*     */         
/* 588 */         if (bool1) {
/* 589 */           recordSet.executeProc("SysFavourite_Insert", str11);
/*     */         }
/*     */         
/* 592 */         if (recordSet.next())
/*     */         {
/* 594 */           str12 = recordSet.getString(1);
/*     */         }
/* 596 */         if (!"".equals(str12)) {
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */           
/* 603 */           String str13 = "insert into sysfavourite_favourite(favouriteid,sysfavouriteid,resourceid) values(" + str + "," + str12 + "," + getUserid() + ")";
/*     */           
/* 605 */           bool = recordSet.execute(str13);
/*     */         } 
/*     */       } 
/*     */     } 
/*     */     
/* 610 */     if (bool) {
/* 611 */       return "true";
/*     */     }
/* 613 */     return "false";
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   private void setJsonStringBody(String paramString1, String paramString2, String paramString3, String paramString4, String paramString5, String paramString6) throws Exception {
/* 619 */     JSONObject jSONObject = new JSONObject();
/* 620 */     jSONObject.put("id", paramString1);
/* 621 */     jSONObject.put("title", paramString2);
/* 622 */     jSONObject.put("adddate", paramString3);
/* 623 */     jSONObject.put("link", paramString4);
/* 624 */     jSONObject.put("importlevel", paramString5);
/* 625 */     jSONObject.put("favouritetype", paramString6);
/* 626 */     this.columns.put(jSONObject);
/*     */   }
/*     */ 
/*     */   
/*     */   private String getRealyLink(String paramString1, String paramString2) {
/* 631 */     if ("1".equals(paramString2))
/*     */     {
/* 633 */       return "/docs/docs/DocDsp.jsp?id=" + paramString1;
/*     */     }
/* 635 */     if ("2".equals(paramString2))
/*     */     {
/* 637 */       return "/workflow/request/ViewRequest.jsp?requestid=" + paramString1;
/*     */     }
/* 639 */     if ("3".equals(paramString2))
/*     */     {
/* 641 */       return "/proj/data/ViewProject.jsp?ProjID=" + paramString1;
/*     */     }
/* 643 */     if ("4".equals(paramString2))
/*     */     {
/* 645 */       return "/CRM/data/ViewCustomer.jsp?CustomerID=" + paramString1;
/*     */     }
/* 647 */     return "";
/*     */   }
/*     */ 
/*     */   
/*     */   private String getJsonString(JSONArray paramJSONArray) throws Exception {
/* 652 */     if (!"".equals(getFavouriteid()) && null != getFavouriteid())
/*     */     {
/* 654 */       this.count = querySysFavouritesCount();
/*     */     }
/* 656 */     JSONObject jSONObject = new JSONObject();
/* 657 */     jSONObject.put("total", this.count);
/* 658 */     jSONObject.put("databody", paramJSONArray);
/* 659 */     return jSONObject.toString();
/*     */   }
/*     */ 
/*     */   
/*     */   public JSONArray getBody() {
/* 664 */     return this.body;
/*     */   }
/*     */ 
/*     */   
/*     */   public void setBody(JSONArray paramJSONArray) {
/* 669 */     this.body = paramJSONArray;
/*     */   }
/*     */ 
/*     */   
/*     */   public String getFavouriteid() {
/* 674 */     return this.favouriteid;
/*     */   }
/*     */ 
/*     */   
/*     */   public void setFavouriteid(String paramString) {
/* 679 */     this.favouriteid = paramString;
/*     */   }
/*     */ 
/*     */   
/*     */   public String getPagename() {
/* 684 */     return this.pagename;
/*     */   }
/*     */ 
/*     */   
/*     */   public void setPagename(String paramString) {
/* 689 */     this.pagename = paramString;
/*     */   }
/*     */ 
/*     */   
/*     */   public String getUrl() {
/* 694 */     return this.url;
/*     */   }
/*     */ 
/*     */   
/*     */   public void setUrl(String paramString) {
/* 699 */     this.url = paramString;
/*     */   }
/*     */ 
/*     */   
/*     */   public String getImportlevel() {
/* 704 */     return this.importlevel;
/*     */   }
/*     */ 
/*     */   
/*     */   public void setImportlevel(String paramString) {
/* 709 */     this.importlevel = paramString;
/*     */   }
/*     */ 
/*     */   
/*     */   public String getAdddate() {
/* 714 */     return this.adddate;
/*     */   }
/*     */ 
/*     */   
/*     */   public void setAdddate(String paramString) {
/* 719 */     this.adddate = paramString;
/*     */   }
/*     */ 
/*     */   
/*     */   public String getId() {
/* 724 */     return this.id;
/*     */   }
/*     */ 
/*     */   
/*     */   public void setId(String paramString) {
/* 729 */     this.id = paramString;
/*     */   }
/*     */ 
/*     */   
/*     */   public int getUserid() {
/* 734 */     return this.userid;
/*     */   }
/*     */ 
/*     */   
/*     */   public void setUserid(int paramInt) {
/* 739 */     this.userid = paramInt;
/*     */   }
/*     */ 
/*     */   
/*     */   public int getFavouriteObjId() {
/* 744 */     return this.favouriteObjId;
/*     */   }
/*     */ 
/*     */   
/*     */   public void setFavouriteObjId(int paramInt) {
/* 749 */     this.favouriteObjId = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public SysFavouriteInfo() {
/* 755 */     this.columns = new JSONArray();
/*     */   }
/*     */   
/*     */   public static String getFavouriteTypeImage(String paramString) {
/* 759 */     if ("1".equals(paramString))
/*     */     {
/* 761 */       return "/images_face/ecologyFace_2/LeftMenuIcon/MyDoc_wev8.png";
/*     */     }
/* 763 */     if ("2".equals(paramString))
/*     */     {
/* 765 */       return "/images_face/ecologyFace_2/LeftMenuIcon/MyWorkflow_wev8.png";
/*     */     }
/* 767 */     if ("3".equals(paramString))
/*     */     {
/* 769 */       return "/images_face/ecologyFace_2/LeftMenuIcon/MyProject_wev8.png";
/*     */     }
/* 771 */     if ("4".equals(paramString))
/*     */     {
/* 773 */       return "/images_face/ecologyFace_2/LeftMenuIcon/MyCRM_wev8.png";
/*     */     }
/*     */ 
/*     */     
/* 777 */     return "/images/filetypeicons/html_wev8.png";
/*     */   }
/*     */ 
/*     */   
/*     */   public String getType() {
/* 782 */     return this.type;
/*     */   }
/*     */ 
/*     */   
/*     */   public void setType(String paramString) {
/* 787 */     this.type = paramString;
/*     */   }
/*     */ 
/*     */   
/*     */   public String getSysfavouriteid() {
/* 792 */     return this.sysfavouriteid;
/*     */   }
/*     */ 
/*     */   
/*     */   public void setSysfavouriteid(String paramString) {
/* 797 */     this.sysfavouriteid = paramString;
/*     */   }
/*     */ 
/*     */   
/*     */   public int getStart() {
/* 802 */     return this.start;
/*     */   }
/*     */ 
/*     */   
/*     */   public void setStart(int paramInt) {
/* 807 */     this.start = paramInt;
/*     */   }
/*     */ 
/*     */   
/*     */   public int getLimit() {
/* 812 */     return this.limit;
/*     */   }
/*     */ 
/*     */   
/*     */   public void setLimit(int paramInt) {
/* 817 */     this.limit = paramInt;
/*     */   }
/*     */ 
/*     */   
/*     */   public int getCount() {
/* 822 */     return this.count;
/*     */   }
/*     */ 
/*     */   
/*     */   public void setCount(int paramInt) {
/* 827 */     this.count = paramInt;
/*     */   }
/*     */   
/*     */   public String getSearchtitle() {
/* 831 */     return this.searchtitle;
/*     */   }
/*     */   
/*     */   public void setSearchtitle(String paramString) {
/* 835 */     this.searchtitle = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String substring(String paramString, int paramInt) {
/* 848 */     int i = paramString.length();
/* 849 */     int j = 0;
/* 850 */     StringBuffer stringBuffer = new StringBuffer();
/*     */     try {
/* 852 */       for (byte b = 0; b < i; ) {
/* 853 */         String str = String.valueOf(paramString.charAt(b));
/* 854 */         byte[] arrayOfByte = str.getBytes("GBK");
/* 855 */         j += arrayOfByte.length;
/* 856 */         if (j <= paramInt) {
/* 857 */           stringBuffer.append(str);
/*     */           
/*     */           b++;
/*     */         } 
/*     */       } 
/* 862 */     } catch (Exception exception) {}
/*     */     
/* 864 */     return stringBuffer.toString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static int getLength(String paramString) {
/* 873 */     int i = 0;
/* 874 */     int j = paramString.length();
/*     */     try {
/* 876 */       for (byte b = 0; b < j; b++) {
/* 877 */         String str = String.valueOf(paramString.charAt(b));
/* 878 */         byte[] arrayOfByte = str.getBytes("GBK");
/* 879 */         i += arrayOfByte.length;
/*     */       } 
/* 881 */     } catch (Exception exception) {}
/*     */     
/* 883 */     return i;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/favourite/SysFavouriteInfo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */