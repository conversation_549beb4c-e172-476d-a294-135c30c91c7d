/*     */ package weaver.favourite;
/*     */ 
/*     */ import java.text.SimpleDateFormat;
/*     */ import java.util.Date;
/*     */ import org.json.JSONArray;
/*     */ import org.json.JSONException;
/*     */ import org.json.JSONObject;
/*     */ import weaver.conn.ConnStatement;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.ThreadVarLanguage;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class FavouriteTabInfo
/*     */ {
/*     */   private String userid;
/*  20 */   private int userlanguage = 0;
/*     */   private String tabid;
/*     */   private String tabname;
/*     */   private String tabdesc;
/*     */   private String taborder;
/*     */   private String adddata;
/*     */   private JSONArray body;
/*     */   private JSONArray columns;
/*     */   private int favouriteid;
/*     */   private String favouriteAlias;
/*     */   private int favouritePageSize;
/*     */   private int favouriteTitleSize;
/*     */   private int showFavouriteTitle;
/*     */   private int showFavouriteLevel;
/*     */   private int favourite_tabid;
/*     */   private int sysfavouriteid;
/*  36 */   private String type = "";
/*  37 */   private String importlevel = "";
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getType() {
/*  43 */     return this.type;
/*     */   }
/*     */   
/*     */   public void setType(String paramString) {
/*  47 */     this.type = paramString;
/*     */   }
/*     */   
/*     */   public String getImportlevel() {
/*  51 */     return this.importlevel;
/*     */   }
/*     */   
/*     */   public void setImportlevel(String paramString) {
/*  55 */     this.importlevel = paramString;
/*     */   }
/*     */   
/*     */   public int getFavourite_tabid() {
/*  59 */     return this.favourite_tabid;
/*     */   }
/*     */   
/*     */   public void setFavourite_tabid(int paramInt) {
/*  63 */     this.favourite_tabid = paramInt;
/*     */   }
/*     */   
/*     */   public void setFavouritePageSize(int paramInt) {
/*  67 */     this.favouritePageSize = paramInt;
/*     */   }
/*     */ 
/*     */   
/*     */   public FavouriteTabInfo() {
/*  72 */     this.columns = new JSONArray();
/*     */   }
/*     */   
/*     */   public String getUserid() {
/*  76 */     return this.userid;
/*     */   }
/*     */   
/*     */   public void setUserid(String paramString) {
/*  80 */     this.userid = paramString;
/*     */   }
/*     */   
/*     */   public String getTabid() {
/*  84 */     return this.tabid;
/*     */   }
/*     */   
/*     */   public void setTabid(String paramString) {
/*  88 */     this.tabid = paramString;
/*     */   }
/*     */   
/*     */   public String getTabname() {
/*  92 */     return this.tabname;
/*     */   }
/*     */   
/*     */   public void setTabname(String paramString) {
/*  96 */     this.tabname = paramString;
/*     */   }
/*     */   
/*     */   public String getAdddata() {
/* 100 */     return this.adddata;
/*     */   }
/*     */   
/*     */   public void setAdddata(String paramString) {
/* 104 */     this.adddata = paramString;
/*     */   }
/*     */   
/*     */   public JSONArray getBody() {
/* 108 */     return this.body;
/*     */   }
/*     */   
/*     */   public void setBody(JSONArray paramJSONArray) {
/* 112 */     this.body = paramJSONArray;
/*     */   }
/*     */   
/*     */   public JSONArray getColumns() {
/* 116 */     return this.columns;
/*     */   }
/*     */   
/*     */   public void setColumns(JSONArray paramJSONArray) {
/* 120 */     this.columns = paramJSONArray;
/*     */   }
/*     */   
/*     */   public int getFavouriteid() {
/* 124 */     return this.favouriteid;
/*     */   }
/*     */   
/*     */   public void setFavouriteid(int paramInt) {
/* 128 */     this.favouriteid = paramInt;
/*     */   }
/*     */   
/*     */   public int getFavouritePageSize() {
/* 132 */     return this.favouritePageSize;
/*     */   }
/*     */   
/*     */   public void setFavouriteDisplaySize(int paramInt) {
/* 136 */     this.favouritePageSize = paramInt;
/*     */   }
/*     */   
/*     */   public String getFavouriteAlias() {
/* 140 */     return this.favouriteAlias;
/*     */   }
/*     */   
/*     */   public void setFavouriteAlias(String paramString) {
/* 144 */     this.favouriteAlias = paramString;
/*     */   }
/*     */   
/*     */   public int getFavouriteTitleSize() {
/* 148 */     return this.favouriteTitleSize;
/*     */   }
/*     */   
/*     */   public void setFavouriteTitleSize(int paramInt) {
/* 152 */     this.favouriteTitleSize = paramInt;
/*     */   }
/*     */   
/*     */   public int getShowFavouriteTitle() {
/* 156 */     return this.showFavouriteTitle;
/*     */   }
/*     */   
/*     */   public void setShowFavouriteTitle(int paramInt) {
/* 160 */     this.showFavouriteTitle = paramInt;
/*     */   }
/*     */   
/*     */   public int getShowFavouriteLevel() {
/* 164 */     return this.showFavouriteLevel;
/*     */   }
/*     */   
/*     */   public void setShowFavouriteLevel(int paramInt) {
/* 168 */     this.showFavouriteLevel = paramInt;
/*     */   }
/*     */   
/*     */   public String addFavouriteTab() throws Exception {
/* 172 */     RecordSet recordSet = new RecordSet();
/* 173 */     String str1 = getUserid();
/* 174 */     String str2 = getTabname();
/* 175 */     String str3 = getTabdesc();
/* 176 */     String str4 = getTaborder();
/* 177 */     String str5 = "";
/* 178 */     String str6 = (new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")).format(new Date());
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 185 */     char c = Util.getSeparator();
/* 186 */     String str7 = str1 + c + str6 + c + str2 + c + str3 + c + str4;
/* 187 */     recordSet.executeProc("FavouriteTab_Insert", str7);
/* 188 */     while (recordSet.next())
/*     */     {
/* 190 */       str5 = recordSet.getString(1);
/*     */     }
/* 192 */     if (str5 != "")
/*     */     {
/* 194 */       setJsonStringBody(str5, str2, str3, str4, "");
/*     */     }
/* 196 */     return getJsonString(this.columns);
/*     */   }
/*     */   
/*     */   public String editFavouriteTab() throws Exception {
/* 200 */     String str1 = getUserid();
/* 201 */     String str2 = getTabid();
/* 202 */     String str3 = getTabname();
/* 203 */     String str4 = getTabdesc();
/* 204 */     String str5 = getTaborder();
/* 205 */     boolean bool = false;
/* 206 */     String str6 = "update favouritetab set tabname=?,tabdesc=?,displayorder=" + str5 + " where id=" + str2 + "and resourceid=" + str1;
/*     */     
/* 208 */     ConnStatement connStatement = new ConnStatement();
/*     */     
/* 210 */     try { connStatement.setStatementSql(str6);
/* 211 */       connStatement.setString(1, str3);
/* 212 */       connStatement.setString(2, str4);
/* 213 */       int i = connStatement.executeUpdate();
/* 214 */       setJsonStringBody(str2, str3, str4, str5, ""); }
/* 215 */     catch (Exception exception) {  }
/*     */     finally
/* 217 */     { connStatement.close(); }
/*     */     
/* 219 */     return getJsonString(this.columns);
/*     */   }
/*     */   
/*     */   public String deleteFavouriteTab() throws Exception {
/* 223 */     RecordSet recordSet = new RecordSet();
/* 224 */     String str1 = getUserid();
/* 225 */     String str2 = getTabid();
/* 226 */     boolean bool = false;
/* 227 */     String str3 = "delete from favouritetab where id=" + str2 + "and resourceid=" + str1;
/*     */     
/* 229 */     bool = recordSet.execute(str3);
/* 230 */     String str4 = "delete from favourite_tab where tabid=" + str2;
/* 231 */     bool = recordSet.execute(str4);
/* 232 */     if (bool)
/*     */     {
/* 234 */       setJsonStringBody(str2, "", "", "", "");
/*     */     }
/* 236 */     return getJsonString(this.columns);
/*     */   }
/*     */   
/*     */   public String setFavouriteTabElement() throws Exception {
/* 240 */     boolean bool = false;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 247 */     String str = "update favourite_tab    set favouritealias     = ?,        favouritepagesize  = " + getFavouritePageSize() + ",        favouritetitlesize = " + getFavouriteTitleSize() + ",        showfavouritetitle = " + getShowFavouriteTitle() + ",        showfavouritelevel = " + getShowFavouriteLevel() + " where id =" + getFavourite_tabid();
/* 248 */     ConnStatement connStatement = new ConnStatement();
/*     */     
/* 250 */     try { connStatement.setStatementSql(str);
/* 251 */       connStatement.setString(1, getFavouriteAlias());
/* 252 */       int i = connStatement.executeUpdate(); }
/* 253 */     catch (Exception exception) {  }
/*     */     finally
/* 255 */     { connStatement.close(); }
/*     */     
/* 257 */     return getElementTableData("" + this.favouriteid);
/*     */   }
/*     */   
/*     */   public String deleteSysFaAndReloadElement() {
/* 261 */     RecordSet recordSet = new RecordSet();
/* 262 */     String str1 = "";
/* 263 */     this.sysfavouriteid = getSysfavouriteid();
/*     */     
/* 265 */     str1 = "delete from sysfavourite_favourite where sysfavouriteid=" + this.sysfavouriteid;
/*     */     
/* 267 */     boolean bool = recordSet.execute(str1);
/*     */     
/* 269 */     if (bool) {
/*     */       
/* 271 */       str1 = "delete from sysfavourite where id=" + this.sysfavouriteid;
/* 272 */       bool = recordSet.execute(str1);
/*     */     } 
/* 274 */     String str2 = "";
/*     */ 
/*     */     
/* 277 */     str2 = "select a.id,a.favouriteid,a.favouritealias,a.favouritepagesize,a.favouritetitlesize,a.showfavouritetitle,a.showfavouritelevel,a.position \tfrom favourite_tab a where a.id=" + getFavourite_tabid();
/*     */     
/* 279 */     recordSet.execute(str2);
/* 280 */     while (recordSet.next()) {
/*     */       
/* 282 */       setFavourite_tabid(recordSet.getInt(1));
/* 283 */       setFavouriteid(recordSet.getInt(2));
/* 284 */       setFavouriteAlias(recordSet.getString(3));
/* 285 */       setFavouritePageSize(recordSet.getInt(4));
/* 286 */       setFavouriteTitleSize(recordSet.getInt(5));
/* 287 */       setShowFavouriteTitle(recordSet.getInt(6));
/* 288 */       setShowFavouriteLevel(recordSet.getInt(7));
/*     */     } 
/* 290 */     return getElementTableData("" + getFavouriteid());
/*     */   }
/*     */   
/*     */   public String saveFavouriteTabPosition(String paramString) {
/* 294 */     RecordSet recordSet = new RecordSet();
/*     */     
/*     */     try {
/* 297 */       JSONObject jSONObject = new JSONObject(paramString);
/* 298 */       JSONArray jSONArray = jSONObject.getJSONArray("databody");
/* 299 */       String str = "";
/* 300 */       for (byte b = 0; b < jSONArray.length(); b++) {
/*     */         
/* 302 */         JSONObject jSONObject1 = jSONArray.getJSONObject(b);
/* 303 */         String str1 = jSONObject1.getString("favourite_tabid");
/* 304 */         String str2 = jSONObject1.getString("position");
/* 305 */         if (!"".equals(str1)) {
/*     */           
/* 307 */           String str3 = "update favourite_tab set position=" + str2 + " where id=" + str1;
/*     */           
/* 309 */           recordSet.execute(str3);
/*     */         } 
/*     */       } 
/*     */       
/* 313 */       return "";
/*     */     }
/* 315 */     catch (Exception exception) {
/*     */       
/* 317 */       exception.printStackTrace();
/*     */       
/* 319 */       return "";
/*     */     } 
/*     */   }
/*     */   public String flashFavouriteTabElement() throws Exception {
/* 323 */     RecordSet recordSet = new RecordSet();
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 328 */     String str1 = "select top " + getFavouritePageSize() + " a.id,a.pagename,a.url,a.importlevel,a.favouritetype from sysfavourite a,sysfavourite_favourite b  where a.id=b.sysfavouriteid  and b.favouriteid=" + getFavouriteid() + " and a.importlevel>=" + getShowFavouriteLevel() + " and b.resourceid=" + getUserid() + " order by a.importlevel desc,a.adddate desc,a.id desc";
/*     */     
/* 330 */     String str2 = recordSet.getDBType();
/* 331 */     if (str2.equals("oracle")) {
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 336 */       str1 = "select a.id,a.pagename,a.url,a.importlevel,a.favouritetype from sysfavourite a,sysfavourite_favourite b  where a.id=b.sysfavouriteid  and b.favouriteid=" + getFavouriteid() + " and a.importlevel>=" + getShowFavouriteLevel() + " and rownum<=" + getFavouritePageSize() + " and b.resourceid=" + getUserid() + " order by a.importlevel desc,a.adddate desc,a.id desc";
/*     */     }
/* 338 */     else if ("mysql".equals(str2)) {
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 345 */       str1 = "select a.id,a.pagename,a.url,a.importlevel,a.favouritetype from sysfavourite a,sysfavourite_favourite b  where a.id=b.sysfavouriteid  and b.favouriteid=" + getFavouriteid() + " and a.importlevel>=" + getShowFavouriteLevel() + " and b.resourceid=" + getUserid() + " order by a.importlevel desc,a.adddate desc,a.id desc limit 0," + getFavouritePageSize();
/* 346 */     } else if ("postgresql".equals(str2)) {
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 353 */       str1 = "select a.id,a.pagename,a.url,a.importlevel,a.favouritetype from sysfavourite a,sysfavourite_favourite b  where a.id=b.sysfavouriteid  and b.favouriteid=" + getFavouriteid() + " and a.importlevel>=" + getShowFavouriteLevel() + " and b.resourceid=" + getUserid() + " order by a.importlevel desc,a.adddate desc,a.id desc limit " + getFavouritePageSize() + "  offset 0";
/*     */     } 
/* 355 */     recordSet.execute(str1);
/* 356 */     while (recordSet.next()) {
/*     */       
/* 358 */       String str3 = recordSet.getString(1);
/* 359 */       String str4 = recordSet.getString(2);
/* 360 */       String str5 = recordSet.getString(3);
/* 361 */       String str6 = "" + recordSet.getInt(4);
/* 362 */       String str7 = "" + recordSet.getInt(5);
/* 363 */       setJsonStringBody(str3, str4, str5, str6, str7);
/*     */     } 
/* 365 */     return getJsonString(this.columns);
/*     */   }
/*     */   
/*     */   public String deleteFavouriteTabElement() throws Exception {
/* 369 */     RecordSet recordSet = new RecordSet();
/* 370 */     String str1 = "" + getFavourite_tabid();
/* 371 */     boolean bool = false;
/* 372 */     String str2 = "delete from favourite_tab where id=" + str1;
/* 373 */     bool = recordSet.execute(str2);
/* 374 */     if (bool)
/*     */     {
/* 376 */       setJsonStringBody(str1, "", "", "", "");
/*     */     }
/* 378 */     return getJsonString(this.columns);
/*     */   }
/*     */   
/*     */   public String addSysFavouriteFromTabs(String paramString) {
/* 382 */     RecordSet recordSet = new RecordSet();
/* 383 */     String str = "";
/*     */     
/*     */     try {
/* 386 */       JSONObject jSONObject = new JSONObject(paramString);
/* 387 */       JSONArray jSONArray = jSONObject.getJSONArray("databody");
/* 388 */       String str1 = "";
/* 389 */       for (byte b = 0; b < jSONArray.length(); b++) {
/*     */         
/* 391 */         JSONObject jSONObject1 = jSONArray.getJSONObject(b);
/*     */         
/* 393 */         String str2 = (new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")).format(new Date());
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */         
/* 400 */         char c = Util.getSeparator();
/* 401 */         String str3 = jSONObject1.getString("pagename");
/*     */         
/* 403 */         String str4 = getRealyLink(jSONObject1.getString("linkid"), 
/* 404 */             getType());
/*     */ 
/*     */ 
/*     */         
/* 408 */         String str5 = String.valueOf(getUserid()) + c + str2 + c + str3 + c + str4 + c + getImportlevel() + c + getType();
/*     */         
/* 410 */         recordSet.executeProc("SysFavourite_Insert", str5);
/* 411 */         if (recordSet.next())
/*     */         {
/* 413 */           str1 = recordSet.getString(1);
/*     */         }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */         
/* 420 */         String str6 = "insert into sysfavourite_favourite(favouriteid,sysfavouriteid,resourceid) values(" + getFavouriteid() + "," + str1 + "," + getUserid() + ")";
/* 421 */         setJsonStringBody(str1, str3, str4, getImportlevel(), getType());
/* 422 */         recordSet.execute(str6);
/*     */       } 
/*     */       
/* 425 */       return getJsonString(this.columns);
/*     */     }
/* 427 */     catch (Exception exception) {
/*     */       
/* 429 */       exception.printStackTrace();
/*     */       
/* 431 */       return "";
/*     */     } 
/*     */   }
/*     */   public String queryFavouriteTabs() throws Exception {
/* 435 */     RecordSet recordSet = new RecordSet();
/* 436 */     String str = "select * from favouritetab where resourceid=" + getUserid() + " order by displayorder,adddate desc ";
/*     */     
/* 438 */     recordSet.execute(str);
/* 439 */     while (recordSet.next()) {
/*     */ 
/*     */       
/*     */       try {
/* 443 */         setJsonStringBody(recordSet.getString("id"), recordSet.getString("tabname"), recordSet.getString("tabdesc"), recordSet.getString("displayorder"), "");
/*     */       }
/* 445 */       catch (JSONException jSONException) {
/*     */         
/* 447 */         jSONException.printStackTrace();
/*     */       } 
/*     */     } 
/* 450 */     return getJsonString(this.columns);
/*     */   }
/*     */   
/*     */   public String addFavouriteTabElement() {
/* 454 */     RecordSet recordSet = new RecordSet();
/* 455 */     String str1 = "" + getFavouriteid();
/* 456 */     String str2 = "";
/* 457 */     if ("-1".equals(str1)) {
/*     */       
/* 459 */       this.favouriteAlias = "" + SystemEnv.getHtmlLabelName(18030, ThreadVarLanguage.getLang()) + "";
/*     */     }
/*     */     else {
/*     */       
/* 463 */       String str = "select * from favourite where id=" + str1;
/*     */       
/* 465 */       recordSet.execute(str);
/* 466 */       while (recordSet.next())
/*     */       {
/* 468 */         this.favouriteAlias = recordSet.getString(4);
/*     */       }
/*     */     } 
/* 471 */     if (!"".equals(this.favouriteAlias)) {
/*     */       
/* 473 */       String str3 = (new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")).format(new Date());
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 480 */       char c = Util.getSeparator();
/*     */ 
/*     */ 
/*     */       
/* 484 */       String str4 = str1 + c + getTabid() + c + this.favouriteAlias + c + '\n' + c + '\031' + c + '\001' + c + '\001' + c + getUserid();
/*     */       
/* 486 */       recordSet.executeProc("Favourite_Tab_Insert", str4);
/* 487 */       if (recordSet.next()) {
/*     */         
/* 489 */         this.favourite_tabid = recordSet.getInt(1);
/*     */         
/* 491 */         str2 = getElementTableBody(str1, this.favourite_tabid, this.favouriteAlias);
/*     */       } 
/*     */     } 
/*     */     
/* 495 */     return str2;
/*     */   }
/*     */   
/*     */   private String getElementTableBody(String paramString1, int paramInt, String paramString2) {
/* 499 */     StringBuffer stringBuffer = new StringBuffer();
/* 500 */     stringBuffer.append("<TABLE class=ElementTable id=_elementTable_" + paramInt + "\n cellSpacing=0 cellPadding=0 width=\"100%\" name=\"tblE\" \n ebaseid=\"6\" eid=\"" + paramInt + "\"> \n<div id=\"favouriteid_" + paramString1 + "\" style=\"display:none;\"></div><TBODY> \n");
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 507 */     stringBuffer.append(getElementTableTitle("" + paramInt, paramString2));
/* 508 */     stringBuffer.append("<TR> \n <TD colSpan=2> \n");
/* 509 */     stringBuffer.append(getElementTableSetDiv("" + paramInt, paramString2));
/* 510 */     stringBuffer.append(getElementTableAddDiv("" + paramInt, paramString2));
/* 511 */     stringBuffer.append(getElementTableData("" + paramString1));
/* 512 */     stringBuffer.append("</TD></TR></TBODY></TABLE>");
/*     */     
/* 514 */     return stringBuffer.toString();
/*     */   }
/*     */   
/*     */   private String getElementTableTitle(String paramString1, String paramString2) {
/* 518 */     return "<TR \n style=\"BACKGROUND: url(/images/homepage/style/style1/headBg_wev8.gif);\">\n <TD vAlign=center align=left>\n &nbsp;\n <IMG title=" + 
/*     */ 
/*     */ 
/*     */       
/* 522 */       SystemEnv.getHtmlLabelName(19652, getUserlanguage()) + " style=\"\"\n\tonclick=\"showDivEcontent('" + paramString1 + "');\" \n \theight=16 src=\"/images/homepage/element/2_wev8.gif\" width=16\n \talign=absMiddle border=0>\n &nbsp;\n <FONT id=_etitlecolor color=#000000><B><SPAN\n \t\tid=spanEtitle" + paramString1 + ">" + paramString2 + "</SPAN>\n </B> </FONT>\n </TD>\n <TD align=right>\n &nbsp;&nbsp;\n <IMG\n \tonmouseover=\"this.style.filter='progid:DXImageTransform.Microsoft.Alpha(opacity=70)'\"\n \ttitle=" + 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 535 */       SystemEnv.getHtmlLabelName(611, getUserlanguage()) + "\n \tstyle=\"FILTER: progid : DXImageTransform . Microsoft . Alpha(opacity = 30);\"\n \tonclick=\"onSysFavouritesAdd('_divSysFavouriteAdd_" + paramString1 + "')\"\n \tonmouseout=\"this.style.filter='progid:DXImageTransform.Microsoft.Alpha(opacity=30)'\"\n \tsrc=\"/images/btnDocExpand_wev8.gif\">\n &nbsp;\n <IMG\n \tonmouseover=\"this.style.filter='progid:DXImageTransform.Microsoft.Alpha(opacity=70)'\"\n \ttitle=" + 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 543 */       SystemEnv.getHtmlLabelName(22250, getUserlanguage()) + "\n \tstyle=\"FILTER: progid : DXImageTransform . Microsoft . Alpha(opacity = 30);\"\n \tonclick=\"onESetting(_divESetting_" + paramString1 + ")\"\n \tonmouseout=\"this.style.filter='progid:DXImageTransform.Microsoft.Alpha(opacity=30)'\"\n \tsrc=\"/images/homepage/style/style1/setting1_wev8.gif\">\n &nbsp;\n <IMG\n \tonmouseover=\"this.style.filter='progid:DXImageTransform.Microsoft.Alpha(opacity=70)'\"\n \ttitle=" + 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 551 */       SystemEnv.getHtmlLabelName(91, getUserlanguage()) + "\n \tstyle=\"FILTER: progid : DXImageTransform.Microsoft.Alpha(opacity = 30);\"\n \tonclick=onDel(" + paramString1 + ")\n onmouseout=\"this.style.filter='progid:DXImageTransform.Microsoft.Alpha(opacity=30)'\"\n \tsrc=\"/images/homepage/style/style1/close1_wev8.gif\">\n &nbsp;\n <A onclick=\"javascript:openFavouriteTab('" + this.favouriteid + "','" + paramString2 + "');\" \n \thref=\"javascript:void(0);\"><IMG\n \t\tonmouseover=\"this.style.filter='progid:DXImageTransform.Microsoft.Alpha(opacity=70)'\"\n \t\ttitle=" + 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 560 */       SystemEnv.getHtmlLabelName(17499, getUserlanguage()) + "\n \t\tstyle=\"FILTER: progid : DXImageTransform . Microsoft . Alpha(opacity = 30);\"\n \t\tonmouseout=\"this.style.filter='progid:DXImageTransform.Microsoft.Alpha(opacity=30)'\"\n \t\tsrc=\"/images/homepage/style/style1/more1_wev8.gif\" border=0>\n </A>&nbsp;\n </TD>\n </TR>";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String getElementTableSetDiv(String paramString1, String paramString2) {
/* 571 */     return "<DIV id='_divESetting_" + paramString1 + "' \nstyle=\"display: none\"> \n<TABLE class=viewForm bgColor=#ffffff valign=\"top\"> \n\t<TBODY> \n\t\t<TR vAlign=top> \n\t\t\t<TD width=\"20%\"> \n\t\t\t\t&nbsp;" + 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 577 */       SystemEnv.getHtmlLabelName(19491, getUserlanguage()) + " \n\t\t\t</TD> \n\t\t\t<TD class=field width=\"80%\"> \n\t\t\t\t<INPUT class=inputStyle id=_eTitel_" + paramString1 + " \n\t\t\t\t\tstyle=\"WIDTH: 98%\" value=" + paramString2 + "> \n\t\t\t</TD> \n\t\t</TR> \n\t\t<TR vAlign=top style='height:1px;'> \n\t\t\t<TD class=line colSpan=2></TD> \n\t\t</TR> \n\t\t<TR vAlign=top> \n\t\t\t<TD> \n\t\t\t\t&nbsp;" + 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 589 */       SystemEnv.getHtmlLabelName(19493, getUserlanguage()) + " \n\t\t\t</TD> \n\t\t\t<TD class=field> \n\t\t\t\t<INPUT class=inputStyle id=_ePerpage_" + paramString1 + " \n\t\t\t\t\tstyle=\"WIDTH: 98%\" value=10> \n\t\t\t</TD> \n\t\t</TR> \n\t\t<TR vAlign=top style='height:1px;'> \n\t\t\t<TD class=line colSpan=2></TD> \n\t\t</TR> \n\t\t<TR vAlign=top> \n\t\t\t<TD> \n\t\t\t\t&nbsp;" + 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 601 */       SystemEnv.getHtmlLabelName(19495, getUserlanguage()) + " \n\t\t\t</TD> \n\t\t\t<TD class=field> \n\t\t\t\t<INPUT type=checkbox CHECKED value=4 name=_chkTitleField_" + paramString1 + "> \n\t\t\t\t" + 
/*     */ 
/*     */ 
/*     */       
/* 605 */       SystemEnv.getHtmlLabelName(229, getUserlanguage()) + " &nbsp;" + SystemEnv.getHtmlLabelName(19524, getUserlanguage()) + ": \n\t\t\t\t<INPUT class=inputstyle title=" + 
/* 606 */       SystemEnv.getHtmlLabelName(19524, getUserlanguage()) + " style=\"WIDTH: 24px\" \n\t\t\t\t\tvalue=25 name=_wordcount_" + paramString1 + " \n\t\t\t\t\tbasefield=\"4\"> \n\t\t\t\t&nbsp; \n\t\t\t\t<BR> \n\t\t\t\t<INPUT type=checkbox CHECKED value=4 name=_chkImportLevelField_" + paramString1 + "> \n\t\t\t\t" + 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 612 */       SystemEnv.getHtmlLabelName(18178, getUserlanguage()) + "\n\t\t\t</TD> \n\t\t</TR> \n\t\t<TR vAlign=top style='height:1px;'> \n\t\t\t<TD class=line colSpan=2></TD> \n\t\t</TR> \n\t\t<TR vAlign=top> \n\t\t\t<TD></TD> \n\t\t\t<TD> \n\t\t\t\t<A \n\t\t\t\t\thref=\"javascript:onUseSetting('" + paramString1 + "','" + this.favouriteid + "');\">" + 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 622 */       SystemEnv.getHtmlLabelName(19565, getUserlanguage()) + "</A> \n\t\t\t\t&nbsp;&nbsp;&nbsp; \n\t\t\t\t<A \n\t\t\t\t\thref=\"javascript:onNoUseSetting('" + paramString1 + "')\">" + 
/*     */ 
/*     */       
/* 625 */       SystemEnv.getHtmlLabelName(19566, getUserlanguage()) + "</A> \n\t\t\t</TD> \n\t\t<TR vAlign=top style='height:1px;'> \n\t\t\t<TD class=line colSpan=2></TD> \n\t\t</TR> \n\t</TBODY> \n</TABLE> \n</DIV>";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String getElementTableAddDiv(String paramString1, String paramString2) {
/* 637 */     return "<DIV id='_divSysFavouriteAdd_" + paramString1 + "' \n\tstyle=\"display: none;\"> \n\t<TABLE class=viewForm bgColor=#ffffff valign=\"top\"> \n\t\t<TBODY> \n\t\t\t<TR vAlign=top> \n\t\t\t\t<TD> \n\t\t\t\t\t&nbsp;" + 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 643 */       SystemEnv.getHtmlLabelName(22255, getUserlanguage()) + "： \n\t\t\t\t</TD> \n\t\t\t\t<TD class=field width=\"80%\"> \n\t\t\t\t\t<A onclick=\"javascript:addSysFavourites('" + this.favouriteid + "','" + paramString1 + "',1)\" \n\t\t\t\t\t\thref=\"javascript:void(0);\">" + 
/*     */ 
/*     */ 
/*     */       
/* 647 */       SystemEnv.getHtmlLabelName(22243, getUserlanguage()) + "</A> \n\t\t\t\t\t&nbsp;&nbsp; \n\t\t\t\t\t<A onclick=\"javascript:addSysFavourites('" + this.favouriteid + "','" + paramString1 + "',2)\" \n\t\t\t\t\t\thref=\"javascript:void(0);\">" + 
/*     */ 
/*     */       
/* 650 */       SystemEnv.getHtmlLabelName(22244, getUserlanguage()) + "</A> \n\t\t\t\t\t\t&nbsp;&nbsp; \n\t\t\t\t\t<A onclick=\"javascript:addSysFavourites('" + this.favouriteid + "','" + paramString1 + "',3)\" \n\t\t\t\t\t\thref=\"javascript:void(0);\">" + 
/*     */ 
/*     */       
/* 653 */       SystemEnv.getHtmlLabelName(22245, getUserlanguage()) + "</A> \n\t\t\t\t\t\t&nbsp;&nbsp; \n\t\t\t\t\t<A onclick=\"javascript:addSysFavourites('" + this.favouriteid + "','" + paramString1 + "',4)\" \n\t\t\t\t\t\thref=\"javascript:void(0);\">" + 
/*     */ 
/*     */       
/* 656 */       SystemEnv.getHtmlLabelName(21313, getUserlanguage()) + "</A> \n\t\t\t\t</TD> \n\t\t\t</TR> \n\t\t\t<TR vAlign=top style='height:1px;'> \n\t\t\t\t<TD class=line colSpan=2></TD> \n\t\t\t</TR> \n\t\t</TBODY> \n\t</TABLE> \n</DIV>";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String getElementTableData(String paramString) {
/* 669 */     RecordSet recordSet = new RecordSet();
/* 670 */     StringBuffer stringBuffer = new StringBuffer("<DIV id='_divEcontent_" + this.favourite_tabid + "' \nstyle=\"OVERFLOW: auto; WIDTH: 100%\"> \n<TABLE class=Econtent id='favouritemaintable' \n\tstyle=\"COLOR: #000000\" width=\"100%\"> \n\t<TBODY> \n\t\t<TR> \n\t\t\t<TD width=1></TD> \n\t\t\t<TD width=*> \n\t\t\t\t<TABLE width=\"100%\" id=\"favouritetabdatatable_" + paramString + "\"> \n\t\t\t\t\t<TBODY> \n");
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 680 */     int i = getFavouritePageSize();
/* 681 */     if (i == 0)
/*     */     {
/* 683 */       i = 10;
/*     */     }
/*     */ 
/*     */ 
/*     */     
/* 688 */     String str1 = "select top " + i + " a.id,a.pagename,a.url,a.importlevel,a.favouritetype from sysfavourite a,sysfavourite_favourite b  where a.id=b.sysfavouriteid  and b.favouriteid=" + paramString + " and a.importlevel>=" + getShowFavouriteLevel() + " and b.resourceid=" + getUserid() + " order by a.importlevel desc,a.adddate desc,a.id desc";
/* 689 */     String str2 = recordSet.getDBType();
/* 690 */     if (str2.equals("oracle")) {
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 695 */       str1 = "select a.id,a.pagename,a.url,a.importlevel,a.favouritetype from sysfavourite a,sysfavourite_favourite b  where a.id=b.sysfavouriteid  and b.favouriteid=" + paramString + " and a.importlevel>=" + getShowFavouriteLevel() + " and rownum<=" + i + " and b.resourceid=" + getUserid() + " order by a.importlevel desc,a.adddate desc,a.id desc";
/* 696 */     } else if ("mysql".equals(str2)) {
/*     */ 
/*     */ 
/*     */       
/* 700 */       str1 = "select a.id,a.pagename,a.url,a.importlevel,a.favouritetype from sysfavourite a,sysfavourite_favourite b  where a.id=b.sysfavouriteid  and b.favouriteid=" + paramString + " and a.importlevel>=" + getShowFavouriteLevel() + " and b.resourceid=" + getUserid() + " order by a.importlevel desc,a.adddate desc,a.id desc limit 0," + i;
/*     */     }
/* 702 */     else if ("postgresql".equals(str2)) {
/*     */ 
/*     */ 
/*     */       
/* 706 */       str1 = "select a.id,a.pagename,a.url,a.importlevel,a.favouritetype from sysfavourite a,sysfavourite_favourite b  where a.id=b.sysfavouriteid  and b.favouriteid=" + paramString + " and a.importlevel>=" + getShowFavouriteLevel() + " and b.resourceid=" + getUserid() + " order by a.importlevel desc,a.adddate desc,a.id desc limit " + i + " offset 0";
/*     */     } 
/*     */     
/* 709 */     recordSet.execute(str1);
/* 710 */     while (recordSet.next()) {
/*     */       
/* 712 */       String str3 = recordSet.getString(1);
/* 713 */       String str4 = recordSet.getString(2);
/* 714 */       String str5 = recordSet.getString(3);
/* 715 */       int j = recordSet.getInt(4);
/* 716 */       int k = recordSet.getInt(5);
/* 717 */       String str6 = getImportName(j);
/* 718 */       stringBuffer.append(getElementTableDataDetail(str3, str4, str5, str6, k));
/*     */     } 
/* 720 */     stringBuffer.append("\t\t\t\t\t</TBODY> \n\t\t\t\t</TABLE> \n\t\t\t</TD> \n\t\t</TR> \n\t</TBODY> \n</TABLE> \n</DIV>");
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 727 */     return stringBuffer.toString();
/*     */   }
/*     */   
/*     */   private String getElementTableDataDetail(String paramString1, String paramString2, String paramString3, String paramString4, int paramInt) {
/* 731 */     String str1 = paramString2;
/* 732 */     int i = getFavouriteTitleSize();
/* 733 */     if (i == 0)
/*     */     {
/* 735 */       i = 25;
/*     */     }
/* 737 */     if (paramString2.length() > i) {
/*     */       
/* 739 */       paramString2 = paramString2.substring(0, i);
/* 740 */       paramString2 = paramString2 + "...";
/*     */     } 
/* 742 */     str1 = str1.replaceAll("&nbsp", "＆nbsp");
/* 743 */     str1 = Util.toHtml5(str1);
/* 744 */     paramString2 = paramString2.replaceAll("&nbsp", "＆nbsp");
/* 745 */     paramString2 = Util.toHtml5(paramString2);
/*     */     
/* 747 */     String str2 = "<TR height=18 id=\"sysFavouriteTr_" + paramString1 + "\"> \n\t<TD width=8> \n\t\t<IMG \n\t\t\tsrc=\"/images/homepage/style/style1/esymbol_wev8.gif\" \n\t\t\tname=esymbol> \n\t\t<div id=\"sysFavouriteTypeDiv_" + paramString1 + "\" style=\"display:none;\"> \n\t\t\t<span id=\"sysFavouriteType_" + paramString1 + "\">" + paramInt + "</span> \n\t\t</div> \n\t</TD> \n\t<TD width=* title=\"" + paramString2 + "\"> \n\t<input type='hidden' id='sysFavouriteHiddenTitle_" + paramString1 + "' value='" + str1 + "'> \n";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 759 */     int j = getShowFavouriteTitle();
/* 760 */     if (j != -1)
/*     */     {
/* 762 */       str2 = str2 + "\t\t<A \n\t\t\thref=\"javascript:openFullWindowForXtable('" + paramString3 + "')\"><span id=\"sysFavouriteTitle_" + paramString1 + "\">" + paramString2 + "</span></A> \n";
/*     */     }
/*     */ 
/*     */ 
/*     */     
/* 767 */     str2 = str2 + "\t</TD> \n";
/* 768 */     str2 = str2 + "\t<TD width=76> \n";
/* 769 */     int k = getShowFavouriteLevel();
/* 770 */     String str3 = "display:";
/* 771 */     if (k != -1) {
/*     */       
/* 773 */       str3 = str3 + "block;";
/*     */     }
/*     */     else {
/*     */       
/* 777 */       str3 = str3 + "none;";
/*     */     } 
/* 779 */     str2 = str2 + "<div id=\"sysFavouriteImportnameDiv_" + paramString1 + "\" style='" + str3 + "'><span id=\"sysFavouriteImportname_" + paramString1 + "\">" + paramString4 + "</span></div> \n";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 785 */     str2 = str2 + "\t</TD> \n\t<TD width=70> \n\t\t<A onclick=javascript:showEditWindow(" + paramString1 + ")\n\t\t\thref=\"javascript:void(0);\">" + SystemEnv.getHtmlLabelName(93, getUserlanguage()) + "</A>&nbsp;&nbsp;&nbsp;\n\t\t<A onclick=\"javascript:deleteSysFavourite('" + paramString1 + "','" + this.favouriteid + "','" + this.favourite_tabid + "');\"\n\t\t\thref=\"javascript:void(0);\">" + SystemEnv.getHtmlLabelName(91, getUserlanguage()) + "</A> \n\t</TD> \n</TR> \n<TR \n\tstyle=\"BACKGROUND: url(/images/homepage/style/style1/esparatorimg_wev8.gif)\" \n\theight=1> \n\t<TD colSpan=5></TD> \n</TR>";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 793 */     return str2;
/*     */   }
/*     */   
/*     */   private void setJsonStringBody(String paramString1, String paramString2, String paramString3, String paramString4, String paramString5) throws Exception {
/* 797 */     JSONObject jSONObject = new JSONObject();
/* 798 */     jSONObject.put("id", paramString1);
/* 799 */     jSONObject.put("title", paramString2);
/* 800 */     jSONObject.put("desc", paramString3);
/* 801 */     jSONObject.put("order", paramString4);
/* 802 */     jSONObject.put("type", paramString5);
/* 803 */     this.columns.put(jSONObject);
/*     */   }
/*     */   
/*     */   private String getJsonString(JSONArray paramJSONArray) throws Exception {
/* 807 */     JSONObject jSONObject = new JSONObject();
/* 808 */     jSONObject.put("total", 10);
/* 809 */     jSONObject.put("databody", paramJSONArray);
/* 810 */     return jSONObject.toString();
/*     */   }
/*     */   
/*     */   private String getImportName(int paramInt) {
/* 814 */     String str = "";
/* 815 */     if (1 == paramInt) {
/*     */       
/* 817 */       str = SystemEnv.getHtmlLabelName(154, getUserlanguage());
/*     */     }
/* 819 */     else if (2 == paramInt) {
/*     */       
/* 821 */       str = SystemEnv.getHtmlLabelName(22241, getUserlanguage());
/*     */     }
/*     */     else {
/*     */       
/* 825 */       str = SystemEnv.getHtmlLabelName(15533, getUserlanguage());
/*     */     } 
/* 827 */     return str;
/*     */   }
/*     */   
/*     */   private String getRealyLink(String paramString1, String paramString2) {
/* 831 */     if ("1".equals(paramString2))
/*     */     {
/* 833 */       return "/docs/docs/DocDsp.jsp?id=" + paramString1;
/*     */     }
/* 835 */     if ("2".equals(paramString2))
/*     */     {
/* 837 */       return "/workflow/request/ViewRequest.jsp?requestid=" + paramString1;
/*     */     }
/* 839 */     if ("3".equals(paramString2))
/*     */     {
/* 841 */       return "/proj/data/ViewProject.jsp?ProjID=" + paramString1;
/*     */     }
/* 843 */     if ("4".equals(paramString2))
/*     */     {
/* 845 */       return "/CRM/data/ViewCustomer.jsp?CustomerID=" + paramString1;
/*     */     }
/* 847 */     return "";
/*     */   }
/*     */   
/*     */   public String getTabdesc() {
/* 851 */     return this.tabdesc;
/*     */   }
/*     */   
/*     */   public void setTabdesc(String paramString) {
/* 855 */     this.tabdesc = paramString;
/*     */   }
/*     */   
/*     */   public String getTaborder() {
/* 859 */     return this.taborder;
/*     */   }
/*     */   
/*     */   public void setTaborder(String paramString) {
/* 863 */     this.taborder = paramString;
/*     */   }
/*     */   
/*     */   public int getUserlanguage() {
/* 867 */     return this.userlanguage;
/*     */   }
/*     */   
/*     */   public void setUserlanguage(int paramInt) {
/* 871 */     this.userlanguage = paramInt;
/*     */   }
/*     */   
/*     */   public int getSysfavouriteid() {
/* 875 */     return this.sysfavouriteid;
/*     */   }
/*     */   
/*     */   public void setSysfavouriteid(int paramInt) {
/* 879 */     this.sysfavouriteid = paramInt;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/favourite/FavouriteTabInfo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */