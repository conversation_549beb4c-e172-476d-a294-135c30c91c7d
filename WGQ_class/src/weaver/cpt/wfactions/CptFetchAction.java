/*     */ package weaver.cpt.wfactions;
/*     */ 
/*     */ import com.engine.cpt.util.CptRightShareUitl;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import org.json.JSONArray;
/*     */ import org.json.JSONObject;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.cpt.capital.CapitalComInfo;
/*     */ import weaver.cpt.capital.CptShare;
/*     */ import weaver.cpt.util.CptWfUtil;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ import weaver.interfaces.workflow.action.Action;
/*     */ import weaver.soa.workflow.request.Cell;
/*     */ import weaver.soa.workflow.request.DetailTable;
/*     */ import weaver.soa.workflow.request.Property;
/*     */ import weaver.soa.workflow.request.RequestInfo;
/*     */ import weaver.soa.workflow.request.Row;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CptFetchAction
/*     */   implements Action, Runnable
/*     */ {
/*  34 */   private static BaseBean baseBean = new BaseBean();
/*  35 */   private static Object lock = new Object();
/*  36 */   private CptWfUtil cptWfUtil = new CptWfUtil();
/*  37 */   private RequestInfo request = null;
/*  38 */   private JSONObject wfObject = null;
/*  39 */   private User user = null;
/*     */   
/*     */   public String execute(RequestInfo paramRequestInfo) {
/*  42 */     this.request = paramRequestInfo;
/*  43 */     this.user = paramRequestInfo.getRequestManager().getUser();
/*     */     try {
/*  45 */       this.wfObject = this.cptWfUtil.getCptwfInfo(paramRequestInfo.getWorkflowid());
/*  46 */       if (this.wfObject.getInt("zctype") != this.wfObject.getInt("sltype") || "".equals(this.wfObject.getString("zcname")) || "".equals(this.wfObject.getString("slname"))) {
/*  47 */         paramRequestInfo.getRequestManager().setMessageid("20088");
/*  48 */         paramRequestInfo.getRequestManager().setMessagecontent(SystemEnv.getHtmlLabelName(125188, this.user.getLanguage()));
/*  49 */         return "0";
/*     */       } 
/*  51 */       if ("1".equals(this.wfObject.getString("isasync"))) {
/*  52 */         (new Thread(this)).start();
/*     */       } else {
/*  54 */         doAction(paramRequestInfo);
/*     */       } 
/*  56 */     } catch (Exception exception) {
/*  57 */       exception.printStackTrace();
/*  58 */       baseBean.writeLog(exception.getMessage());
/*     */     } 
/*  60 */     return "1";
/*     */   }
/*     */   
/*     */   public void run() {
/*  64 */     doAction(this.request);
/*     */   }
/*     */   
/*     */   private String doAction(RequestInfo paramRequestInfo) {
/*  68 */     synchronized (lock) {
/*  69 */       baseBean.writeLog("tagtag run action :" + getClass() + ",requestid:" + paramRequestInfo.getRequestid());
/*  70 */       CptShare cptShare = new CptShare();
/*  71 */       CapitalComInfo capitalComInfo = null;
/*  72 */       ResourceComInfo resourceComInfo = null;
/*     */       try {
/*  74 */         capitalComInfo = new CapitalComInfo();
/*  75 */         resourceComInfo = new ResourceComInfo();
/*  76 */         RecordSet recordSet1 = new RecordSet();
/*  77 */         RecordSet recordSet2 = new RecordSet();
/*  78 */         String str1 = paramRequestInfo.getRequestid();
/*  79 */         RecordSet recordSet3 = new RecordSet();
/*  80 */         recordSet3.executeSql("select 1 from  CptUseLog where userequest ='" + str1 + "'");
/*  81 */         if (recordSet3.next()) {
/*  82 */           baseBean.writeLog("tagtag duplicate run action :" + getClass() + ",requestid:" + paramRequestInfo.getRequestid());
/*  83 */           return "1";
/*     */         } 
/*     */ 
/*     */         
/*  87 */         Map map1 = this.cptWfUtil.getcusField(paramRequestInfo.getWorkflowid(), "0");
/*  88 */         Map map2 = new HashMap<>();
/*  89 */         HashMap<Object, Object> hashMap = new HashMap<>();
/*  90 */         JSONArray jSONArray1 = new JSONArray();
/*     */         
/*  92 */         String str2 = "";
/*  93 */         String str3 = "";
/*  94 */         String str4 = "";
/*  95 */         String str5 = "";
/*  96 */         String str6 = "";
/*  97 */         String str7 = "";
/*  98 */         String str8 = "";
/*     */ 
/*     */         
/* 101 */         JSONArray jSONArray2 = new JSONArray();
/* 102 */         Property[] arrayOfProperty = paramRequestInfo.getMainTableInfo().getProperty(); byte b;
/* 103 */         for (b = 0; b < arrayOfProperty.length; b++) {
/* 104 */           String str9 = arrayOfProperty[b].getName();
/* 105 */           String str10 = Util.null2String(arrayOfProperty[b].getValue());
/* 106 */           if (this.wfObject.getInt("sqrtype") == 0 && str9.equalsIgnoreCase(this.wfObject.getString("sqrname"))) {
/* 107 */             str2 = str10;
/* 108 */             str4 = resourceComInfo.getDepartmentID(str2);
/*     */           } 
/* 110 */           if (this.wfObject.getInt("zctype") == 0 && str9.equalsIgnoreCase(this.wfObject.getString("zcname"))) {
/* 111 */             str3 = str10;
/*     */           }
/* 113 */           if (this.wfObject.getInt("sltype") == 0 && str9.equalsIgnoreCase(this.wfObject.getString("slname"))) {
/* 114 */             str6 = str10;
/*     */           }
/* 116 */           if (this.wfObject.getInt("rqtype") == 0 && str9.equalsIgnoreCase(this.wfObject.getString("rqname"))) {
/* 117 */             str5 = str10;
/*     */           }
/* 119 */           if (this.wfObject.getInt("cfddtype") == 0 && str9.equalsIgnoreCase(this.wfObject.getString("cfddname"))) {
/* 120 */             str7 = str10;
/*     */           }
/* 122 */           if (this.wfObject.getInt("bztype") == 0 && str9.equalsIgnoreCase(this.wfObject.getString("bzname"))) {
/* 123 */             str8 = str10;
/*     */           }
/*     */ 
/*     */           
/* 127 */           for (String str : map1.keySet()) {
/* 128 */             JSONObject jSONObject = (JSONObject)map1.get(str);
/* 129 */             if (str9.equalsIgnoreCase(Util.null2String(jSONObject.get("fieldname")))) {
/* 130 */               hashMap.put(str, str10);
/*     */             }
/*     */           } 
/*     */         } 
/* 134 */         if (this.wfObject.getInt("zctype") == 0 && this.wfObject.getInt("sltype") == 0) {
/* 135 */           JSONObject jSONObject1 = new JSONObject();
/* 136 */           jSONObject1.put("sqr", str2);
/* 137 */           jSONObject1.put("sqbm", str4);
/* 138 */           jSONObject1.put("zc", str3);
/* 139 */           jSONObject1.put("sl", str6);
/* 140 */           jSONObject1.put("rq", str5);
/* 141 */           jSONObject1.put("cfdd", str7);
/* 142 */           jSONObject1.put("bz", str8);
/* 143 */           jSONArray2.put(jSONObject1);
/*     */ 
/*     */           
/* 146 */           JSONObject jSONObject2 = new JSONObject();
/* 147 */           for (String str9 : hashMap.keySet()) {
/* 148 */             String str10 = (String)hashMap.get(str9);
/* 149 */             jSONObject2.put(str9, str10);
/*     */           } 
/* 151 */           jSONArray1.put(jSONObject2);
/*     */         }
/* 153 */         else if (this.wfObject.getInt("zctype") == this.wfObject.getInt("sltype")) {
/* 154 */           DetailTable[] arrayOfDetailTable = paramRequestInfo.getDetailTableInfo().getDetailTable();
/* 155 */           String str = Util.null2String(Integer.valueOf(this.wfObject.getInt("zctype")));
/*     */           
/* 157 */           map2 = this.cptWfUtil.getcusField(paramRequestInfo.getWorkflowid(), str);
/* 158 */           DetailTable detailTable = null;
/* 159 */           for (DetailTable detailTable1 : arrayOfDetailTable) {
/* 160 */             String str9 = detailTable1.getTableDBName();
/* 161 */             if (!"".equals(str9)) {
/* 162 */               String str10 = str9.substring(str9.length() - 1);
/* 163 */               if (str10.equals(str)) {
/* 164 */                 detailTable = detailTable1;
/*     */                 break;
/*     */               } 
/*     */             } 
/*     */           } 
/* 169 */           if (detailTable != null) {
/* 170 */             Row[] arrayOfRow = detailTable.getRow();
/* 171 */             for (byte b1 = 0; b1 < arrayOfRow.length; b1++) {
/* 172 */               Row row = arrayOfRow[b1];
/* 173 */               Cell[] arrayOfCell = row.getCell();
/*     */               
/* 175 */               HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 176 */               for (byte b2 = 0; b2 < arrayOfCell.length; b2++) {
/* 177 */                 Cell cell = arrayOfCell[b2];
/* 178 */                 String str9 = cell.getName().toLowerCase();
/* 179 */                 String str10 = Util.null2String(cell.getValue());
/* 180 */                 if (str9.equalsIgnoreCase(this.wfObject.getString("sqrname")) && this.wfObject.getInt("zctype") == this.wfObject.getInt("sqrtype")) {
/* 181 */                   str2 = str10;
/* 182 */                   str4 = resourceComInfo.getDepartmentID(str2);
/*     */                 } 
/* 184 */                 if (str9.equalsIgnoreCase(this.wfObject.getString("zcname"))) {
/* 185 */                   str3 = str10;
/*     */                 }
/* 187 */                 if (str9.equalsIgnoreCase(this.wfObject.getString("slname"))) {
/* 188 */                   str6 = str10;
/*     */                 }
/* 190 */                 if (str9.equalsIgnoreCase(this.wfObject.getString("rqname")) && this.wfObject.getInt("zctype") == this.wfObject.getInt("rqtype")) {
/* 191 */                   str5 = str10;
/*     */                 }
/* 193 */                 if (str9.equalsIgnoreCase(this.wfObject.getString("cfddname")) && this.wfObject.getInt("zctype") == this.wfObject.getInt("cfddtype")) {
/* 194 */                   str7 = str10;
/*     */                 }
/* 196 */                 if (str9.equalsIgnoreCase(this.wfObject.getString("bzname")) && this.wfObject.getInt("zctype") == this.wfObject.getInt("bztype")) {
/* 197 */                   str8 = str10;
/*     */                 }
/*     */ 
/*     */                 
/* 201 */                 for (String str11 : map2.keySet()) {
/* 202 */                   JSONObject jSONObject = (JSONObject)map2.get(str11);
/* 203 */                   if (str9.equalsIgnoreCase(Util.null2String(jSONObject.get("fieldname")))) {
/* 204 */                     hashMap1.put(str11, str10);
/*     */                   }
/*     */                 } 
/*     */               } 
/* 208 */               JSONObject jSONObject1 = new JSONObject();
/* 209 */               jSONObject1.put("sqr", str2);
/* 210 */               jSONObject1.put("sqbm", str4);
/* 211 */               jSONObject1.put("zc", str3);
/* 212 */               jSONObject1.put("sl", str6);
/* 213 */               jSONObject1.put("rq", str5);
/* 214 */               jSONObject1.put("cfdd", str7);
/* 215 */               jSONObject1.put("bz", str8);
/* 216 */               jSONArray2.put(jSONObject1);
/*     */ 
/*     */               
/* 219 */               JSONObject jSONObject2 = new JSONObject();
/* 220 */               for (String str9 : hashMap1.keySet()) {
/* 221 */                 String str10 = (String)hashMap1.get(str9);
/* 222 */                 jSONObject2.put(str9, str10);
/*     */               } 
/* 224 */               jSONArray1.put(jSONObject2);
/*     */             } 
/*     */           } 
/*     */         } 
/*     */         
/* 229 */         for (b = 0; b < jSONArray2.length(); b++) {
/* 230 */           JSONObject jSONObject = jSONArray2.getJSONObject(b);
/* 231 */           char c = Util.getSeparator();
/* 232 */           String str9 = "";
/* 233 */           String str10 = "";
/* 234 */           String str11 = jSONObject.getString("zc");
/* 235 */           String str12 = jSONObject.getString("sl");
/* 236 */           String str13 = jSONObject.getString("rq");
/* 237 */           str4 = jSONObject.getString("sqbm");
/* 238 */           str2 = jSONObject.getString("sqr");
/* 239 */           String str14 = jSONObject.getString("cfdd");
/* 240 */           String str15 = "";
/* 241 */           str8 = jSONObject.getString("bz");
/* 242 */           if (Util.getIntValue(str11, 0) > 0 && Util.getDoubleValue(str12, 0.0D) > 0.0D) {
/*     */ 
/*     */ 
/*     */             
/* 246 */             recordSet1.execute(" select * from CptCapital where id=" + str11);
/* 247 */             if (recordSet1.next()) {
/* 248 */               str10 = recordSet1.getString("sptcount");
/* 249 */               str15 = recordSet1.getString("location");
/*     */             } 
/* 251 */             if ("".equals(str14)) {
/* 252 */               str14 = str15;
/*     */             }
/*     */             
/* 255 */             recordSet1.executeQuery("select stateid from  cptcapital where id=?", new Object[] { str11 });
/* 256 */             String str = recordSet1.next() ? recordSet1.getString("stateid") : "0";
/* 257 */             if (str.equals("1"))
/*     */             
/*     */             { 
/*     */               
/* 261 */               if (str10.equals("1")) {
/* 262 */                 str9 = str11;
/* 263 */                 str9 = str9 + c + str13;
/* 264 */                 str9 = str9 + c + str4;
/* 265 */                 str9 = str9 + c + str2;
/* 266 */                 str9 = str9 + c + "1";
/* 267 */                 str9 = str9 + c + str1;
/* 268 */                 str9 = str9 + c + "";
/* 269 */                 str9 = str9 + c + "0";
/* 270 */                 str9 = str9 + c + "2";
/* 271 */                 str9 = str9 + c + str8;
/* 272 */                 str9 = str9 + c + str14;
/* 273 */                 str9 = str9 + c + str10;
/* 274 */                 recordSet1.executeProc("CptUseLogUse_Insert2", str9);
/*     */               } else {
/* 276 */                 str9 = str11;
/* 277 */                 str9 = str9 + c + str13;
/* 278 */                 str9 = str9 + c + str4;
/* 279 */                 str9 = str9 + c + str2;
/* 280 */                 str9 = str9 + c + str12;
/* 281 */                 str9 = str9 + c + str1;
/* 282 */                 str9 = str9 + c + "";
/* 283 */                 str9 = str9 + c + "0";
/* 284 */                 str9 = str9 + c + "2";
/* 285 */                 str9 = str9 + c + str8;
/* 286 */                 str9 = str9 + c + str14;
/* 287 */                 str9 = str9 + c + "0";
/* 288 */                 recordSet1.executeProc("CptUseLogUse_Insert2", str9);
/* 289 */                 recordSet1.next();
/* 290 */                 String str17 = recordSet1.getString(1);
/*     */                 
/* 292 */                 if (str17.equals("-1")) {
/* 293 */                   paramRequestInfo.getRequestManager().setMessageid("20088");
/* 294 */                   paramRequestInfo.getRequestManager().setMessagecontent(capitalComInfo.getCapitalname(str11) + " " + SystemEnv.getHtmlLabelName(503127, this.user.getLanguage()));
/* 295 */                   return "0";
/*     */                 } 
/*     */               } 
/* 298 */               String str16 = "";
/* 299 */               if (!str14.equals("")) {
/* 300 */                 str16 = "update CptCapital set location='" + str14 + "' where id=" + str11;
/*     */               }
/* 302 */               recordSet2.execute(str16);
/* 303 */               if ("1".equals(str10)) {
/* 304 */                 str16 = "update CptCapital set deprestartdate='" + str13 + "' where id=" + str11;
/* 305 */                 recordSet2.execute(str16);
/*     */               } 
/* 307 */               if (!str8.equals("")) {
/* 308 */                 recordSet2.execute("update CptCapital set remark='" + str8 + "' where id=" + str11);
/*     */               }
/* 310 */               recordSet2.executeProc("HrmInfoStatus_UpdateCapital", "" + str2);
/* 311 */               cptShare.setCptShareByCpt(str11);
/*     */ 
/*     */               
/* 314 */               this.cptWfUtil.updateCptFieldOfWF(map1, hashMap, map2, jSONArray1.getJSONObject(b), str11, this.user.getUID());
/*     */ 
/*     */               
/* 317 */               CptRightShareUitl.editCapitalResetRight(str11);
/*     */               
/* 319 */               capitalComInfo.updateCapitalCache(str11); } 
/*     */           } 
/*     */         } 
/* 322 */       } catch (Exception exception) {
/* 323 */         exception.printStackTrace();
/* 324 */         baseBean.writeLog(exception.getMessage());
/*     */       } 
/*     */       
/* 327 */       Thread thread = new Thread() {
/*     */           public void run() {
/*     */             try {
/* 330 */               sleep(15000L);
/* 331 */             } catch (InterruptedException interruptedException) {
/* 332 */               interruptedException.printStackTrace();
/*     */             } 
/* 334 */             RecordSet recordSet = new RecordSet();
/* 335 */             recordSet.execute("update cptcapital set frozennum=0 where frozennum>0");
/* 336 */             CptWfUtil cptWfUtil = new CptWfUtil();
/* 337 */             cptWfUtil.DoFrozenCpt_new();
/*     */           }
/*     */         };
/* 340 */       thread.start();
/*     */       
/* 342 */       return "1";
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/cpt/wfactions/CptFetchAction.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */