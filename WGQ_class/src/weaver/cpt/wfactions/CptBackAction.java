/*     */ package weaver.cpt.wfactions;
/*     */ 
/*     */ import com.engine.cpt.util.CptRightShareUitl;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import org.json.JSONArray;
/*     */ import org.json.JSONObject;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.cpt.capital.CapitalComInfo;
/*     */ import weaver.cpt.capital.CptShare;
/*     */ import weaver.cpt.util.CptWfUtil;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ import weaver.interfaces.workflow.action.Action;
/*     */ import weaver.soa.workflow.request.Cell;
/*     */ import weaver.soa.workflow.request.DetailTable;
/*     */ import weaver.soa.workflow.request.Property;
/*     */ import weaver.soa.workflow.request.RequestInfo;
/*     */ import weaver.soa.workflow.request.Row;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CptBackAction
/*     */   implements Action, Runnable
/*     */ {
/*  34 */   private static BaseBean baseBean = new BaseBean();
/*  35 */   private static Object lock = new Object();
/*  36 */   private CptWfUtil cptWfUtil = new CptWfUtil();
/*  37 */   private RequestInfo request = null;
/*  38 */   private JSONObject wfObject = null;
/*  39 */   private User user = null;
/*     */   
/*     */   public String execute(RequestInfo paramRequestInfo) {
/*  42 */     this.request = paramRequestInfo;
/*  43 */     this.user = paramRequestInfo.getRequestManager().getUser();
/*     */     try {
/*  45 */       this.wfObject = this.cptWfUtil.getCptwfInfo(paramRequestInfo.getWorkflowid());
/*  46 */       if (this.wfObject.getInt("zctype") != this.wfObject.getInt("sltype") || "".equals(this.wfObject.getString("zcname")) || "".equals(this.wfObject.getString("slname"))) {
/*  47 */         paramRequestInfo.getRequestManager().setMessageid("20088");
/*  48 */         paramRequestInfo.getRequestManager().setMessagecontent(SystemEnv.getHtmlLabelName(125188, this.user.getLanguage()));
/*  49 */         return "0";
/*     */       } 
/*  51 */       if ("1".equals(this.wfObject.getString("isasync"))) {
/*  52 */         (new Thread(this)).start();
/*     */       } else {
/*  54 */         doAction(paramRequestInfo);
/*     */       } 
/*  56 */     } catch (Exception exception) {
/*  57 */       exception.printStackTrace();
/*  58 */       baseBean.writeLog(exception.getMessage());
/*     */     } 
/*  60 */     return "1";
/*     */   }
/*     */   
/*     */   public void run() {
/*  64 */     doAction(this.request);
/*     */   }
/*     */   
/*     */   private String doAction(RequestInfo paramRequestInfo) {
/*  68 */     synchronized (lock) {
/*  69 */       baseBean.writeLog("tagtag run action :" + getClass() + ",requestid:" + paramRequestInfo.getRequestid());
/*  70 */       CptShare cptShare = new CptShare();
/*  71 */       CapitalComInfo capitalComInfo = null;
/*  72 */       ResourceComInfo resourceComInfo = null;
/*     */       try {
/*  74 */         capitalComInfo = new CapitalComInfo();
/*  75 */         resourceComInfo = new ResourceComInfo();
/*     */         
/*  77 */         RecordSet recordSet1 = new RecordSet();
/*  78 */         RecordSet recordSet2 = new RecordSet();
/*  79 */         char c = Util.getSeparator();
/*  80 */         String str1 = paramRequestInfo.getRequestid();
/*     */ 
/*     */         
/*  83 */         Map map1 = this.cptWfUtil.getcusField(paramRequestInfo.getWorkflowid(), "0");
/*  84 */         Map map2 = new HashMap<>();
/*  85 */         HashMap<Object, Object> hashMap = new HashMap<>();
/*  86 */         JSONArray jSONArray1 = new JSONArray();
/*     */         
/*  88 */         String str2 = "";
/*  89 */         String str3 = "";
/*  90 */         String str4 = "";
/*  91 */         String str5 = "";
/*  92 */         String str6 = "";
/*  93 */         String str7 = "";
/*  94 */         String str8 = "";
/*     */ 
/*     */         
/*  97 */         JSONArray jSONArray2 = new JSONArray();
/*  98 */         Property[] arrayOfProperty = paramRequestInfo.getMainTableInfo().getProperty(); byte b;
/*  99 */         for (b = 0; b < arrayOfProperty.length; b++) {
/* 100 */           String str9 = arrayOfProperty[b].getName();
/* 101 */           String str10 = Util.null2String(arrayOfProperty[b].getValue());
/* 102 */           if (this.wfObject.getInt("sqrtype") == 0 && str9.equalsIgnoreCase(this.wfObject.getString("sqrname"))) {
/* 103 */             str2 = str10;
/* 104 */             str4 = resourceComInfo.getDepartmentID(str2);
/*     */           } 
/* 106 */           if (this.wfObject.getInt("zctype") == 0 && str9.equalsIgnoreCase(this.wfObject.getString("zcname"))) {
/* 107 */             str3 = str10;
/*     */           }
/* 109 */           if (this.wfObject.getInt("sltype") == 0 && str9.equalsIgnoreCase(this.wfObject.getString("slname"))) {
/* 110 */             str6 = str10;
/*     */           }
/* 112 */           if (this.wfObject.getInt("rqtype") == 0 && str9.equalsIgnoreCase(this.wfObject.getString("rqname"))) {
/* 113 */             str5 = str10;
/*     */           }
/* 115 */           if (this.wfObject.getInt("cfddtype") == 0 && str9.equalsIgnoreCase(this.wfObject.getString("cfddname"))) {
/* 116 */             str7 = str10;
/*     */           }
/* 118 */           if (this.wfObject.getInt("bztype") == 0 && str9.equalsIgnoreCase(this.wfObject.getString("bzname"))) {
/* 119 */             str8 = str10;
/*     */           }
/*     */ 
/*     */           
/* 123 */           for (String str : map1.keySet()) {
/* 124 */             JSONObject jSONObject = (JSONObject)map1.get(str);
/* 125 */             if (str9.equalsIgnoreCase(Util.null2String(jSONObject.get("fieldname")))) {
/* 126 */               hashMap.put(str, str10);
/*     */             }
/*     */           } 
/*     */         } 
/*     */         
/* 131 */         if (this.wfObject.getInt("zctype") == 0 && this.wfObject.getInt("sltype") == 0) {
/* 132 */           JSONObject jSONObject1 = new JSONObject();
/* 133 */           jSONObject1.put("sqr", str2);
/* 134 */           jSONObject1.put("sqbm", str4);
/* 135 */           jSONObject1.put("zc", str3);
/* 136 */           jSONObject1.put("sl", str6);
/* 137 */           jSONObject1.put("rq", str5);
/* 138 */           jSONObject1.put("cfdd", str7);
/* 139 */           jSONObject1.put("bz", str8);
/* 140 */           jSONArray2.put(jSONObject1);
/*     */ 
/*     */           
/* 143 */           JSONObject jSONObject2 = new JSONObject();
/* 144 */           for (String str9 : hashMap.keySet()) {
/* 145 */             String str10 = (String)hashMap.get(str9);
/* 146 */             jSONObject2.put(str9, str10);
/*     */           } 
/* 148 */           jSONArray1.put(jSONObject2);
/*     */         }
/* 150 */         else if (this.wfObject.getInt("zctype") == this.wfObject.getInt("sltype")) {
/* 151 */           DetailTable[] arrayOfDetailTable = paramRequestInfo.getDetailTableInfo().getDetailTable();
/* 152 */           String str = Util.null2String(Integer.valueOf(this.wfObject.getInt("zctype")));
/*     */           
/* 154 */           map2 = this.cptWfUtil.getcusField(paramRequestInfo.getWorkflowid(), str);
/* 155 */           DetailTable detailTable = null;
/* 156 */           for (DetailTable detailTable1 : arrayOfDetailTable) {
/* 157 */             String str9 = detailTable1.getTableDBName();
/* 158 */             if (!"".equals(str9)) {
/* 159 */               String str10 = str9.substring(str9.length() - 1);
/* 160 */               if (str10.equals(str)) {
/* 161 */                 detailTable = detailTable1;
/*     */                 break;
/*     */               } 
/*     */             } 
/*     */           } 
/* 166 */           if (detailTable != null) {
/* 167 */             Row[] arrayOfRow = detailTable.getRow();
/* 168 */             for (byte b1 = 0; b1 < arrayOfRow.length; b1++) {
/* 169 */               Row row = arrayOfRow[b1];
/* 170 */               Cell[] arrayOfCell = row.getCell();
/*     */               
/* 172 */               HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 173 */               for (byte b2 = 0; b2 < arrayOfCell.length; b2++) {
/* 174 */                 Cell cell = arrayOfCell[b2];
/* 175 */                 String str9 = cell.getName().toLowerCase();
/* 176 */                 String str10 = Util.null2String(cell.getValue());
/* 177 */                 if (str9.equalsIgnoreCase(this.wfObject.getString("sqrname")) && this.wfObject.getInt("zctype") == this.wfObject.getInt("sqrtype")) {
/* 178 */                   str2 = str10;
/* 179 */                   str4 = resourceComInfo.getDepartmentID(str2);
/*     */                 } 
/* 181 */                 if (str9.equalsIgnoreCase(this.wfObject.getString("zcname"))) {
/* 182 */                   str3 = str10;
/*     */                 }
/* 184 */                 if (str9.equalsIgnoreCase(this.wfObject.getString("slname"))) {
/* 185 */                   str6 = str10;
/*     */                 }
/* 187 */                 if (str9.equalsIgnoreCase(this.wfObject.getString("rqname")) && this.wfObject.getInt("zctype") == this.wfObject.getInt("rqtype")) {
/* 188 */                   str5 = str10;
/*     */                 }
/* 190 */                 if (str9.equalsIgnoreCase(this.wfObject.getString("cfddname")) && this.wfObject.getInt("zctype") == this.wfObject.getInt("cfddtype")) {
/* 191 */                   str7 = str10;
/*     */                 }
/* 193 */                 if (str9.equalsIgnoreCase(this.wfObject.getString("bzname")) && this.wfObject.getInt("zctype") == this.wfObject.getInt("bztype")) {
/* 194 */                   str8 = str10;
/*     */                 }
/*     */ 
/*     */                 
/* 198 */                 for (String str11 : map2.keySet()) {
/* 199 */                   JSONObject jSONObject = (JSONObject)map2.get(str11);
/* 200 */                   if (str9.equalsIgnoreCase(Util.null2String(jSONObject.get("fieldname")))) {
/* 201 */                     hashMap1.put(str11, str10);
/*     */                   }
/*     */                 } 
/*     */               } 
/* 205 */               JSONObject jSONObject1 = new JSONObject();
/* 206 */               jSONObject1.put("sqr", str2);
/* 207 */               jSONObject1.put("sqbm", str4);
/* 208 */               jSONObject1.put("zc", str3);
/* 209 */               jSONObject1.put("sl", str6);
/* 210 */               jSONObject1.put("rq", str5);
/* 211 */               jSONObject1.put("cfdd", str7);
/* 212 */               jSONObject1.put("bz", str8);
/*     */               
/* 214 */               jSONArray2.put(jSONObject1);
/*     */ 
/*     */               
/* 217 */               JSONObject jSONObject2 = new JSONObject();
/* 218 */               for (String str9 : hashMap1.keySet()) {
/* 219 */                 String str10 = (String)hashMap1.get(str9);
/* 220 */                 jSONObject2.put(str9, str10);
/*     */               } 
/* 222 */               jSONArray1.put(jSONObject2);
/*     */             } 
/*     */           } 
/*     */         } 
/*     */ 
/*     */         
/* 228 */         for (b = 0; b < jSONArray2.length(); b++) {
/* 229 */           JSONObject jSONObject = jSONArray2.getJSONObject(b);
/*     */           
/* 231 */           String str9 = "";
/* 232 */           String str10 = "";
/* 233 */           String str11 = jSONObject.getString("zc");
/* 234 */           String str12 = jSONObject.getString("rq");
/* 235 */           String str13 = jSONObject.getString("sl");
/* 236 */           str4 = jSONObject.getString("sqbm");
/* 237 */           str2 = jSONObject.getString("sqr");
/* 238 */           String str14 = jSONObject.getString("cfdd");
/* 239 */           str8 = jSONObject.getString("bz");
/*     */           
/* 241 */           if (Util.getIntValue(str11, 0) > 0 && Util.getDoubleValue(str13, 0.0D) > 0.0D) {
/*     */ 
/*     */ 
/*     */             
/* 245 */             String str15 = "";
/* 246 */             str15 = "select sptcount,resourceid,departmentid,deprestartdate,costcenterid,stateid from CptCapital where id=" + str11;
/* 247 */             recordSet1.execute(str15);
/* 248 */             recordSet1.next();
/* 249 */             str10 = recordSet1.getString("sptcount");
/*     */             
/* 251 */             String str16 = Util.null2String(recordSet1.getString("resourceid"));
/* 252 */             String str17 = Util.null2String(recordSet1.getString("deprestartdate"));
/* 253 */             String str18 = Util.null2String(recordSet1.getString("departmentid"));
/* 254 */             String str19 = Util.null2String(recordSet1.getString("costcenterid"));
/* 255 */             String str20 = Util.null2String(recordSet1.getString("stateid"));
/*     */ 
/*     */             
/* 258 */             String str21 = "1";
/* 259 */             if (!"".equals(str16) && !"0".equals(str16) && str20.equals("4")) {
/* 260 */               str21 = "0";
/*     */             }
/* 262 */             if ("1".equals(str10)) {
/* 263 */               str21 = "1";
/*     */             }
/* 265 */             str9 = str11;
/* 266 */             str9 = str9 + c + str12;
/* 267 */             str9 = str9 + c + "" + str4;
/* 268 */             str9 = str9 + c + "" + str2;
/* 269 */             str9 = str9 + c + str21;
/* 270 */             str9 = str9 + c + str14;
/* 271 */             str9 = str9 + c + str1;
/* 272 */             str9 = str9 + c + "";
/* 273 */             str9 = str9 + c + "0";
/* 274 */             str9 = str9 + c + "1";
/* 275 */             str9 = str9 + c + str8;
/* 276 */             str9 = str9 + c + "0";
/* 277 */             str9 = str9 + c + str10;
/* 278 */             recordSet2.executeProc("CptUseLogBack_Insert", str9);
/*     */             
/* 280 */             if (!"".equals(str16) && !"0".equals(str16) && str20.equals("4")) {
/* 281 */               String str = "2";
/* 282 */               recordSet2.execute("select usestatus from cptuselog where capitalid='" + str11 + "' order by id desc");
/* 283 */               int i = recordSet2.getCounts();
/* 284 */               while (i >= 1) {
/* 285 */                 recordSet2.next();
/* 286 */                 if ("2".equals(recordSet2.getString(1)) || "3".equals(recordSet2.getString(1))) {
/* 287 */                   str = Util.null2String(recordSet2.getString("usestatus"));
/*     */                   break;
/*     */                 } 
/* 290 */                 i--;
/*     */               } 
/* 292 */               if ("".equals(str19)) {
/* 293 */                 recordSet2.execute("update CptCapital set resourceid = '" + str16 + "',departmentid = '" + str18 + "',deprestartdate='" + str17 + "',costcenterid = null ,stateid = '" + str + "' where id = " + str11);
/*     */               } else {
/* 295 */                 recordSet2.execute("update CptCapital set resourceid = '" + str16 + "',departmentid = '" + str18 + "',deprestartdate='" + str17 + "',costcenterid = '" + str19 + "',stateid = '" + str + "' where id = " + str11);
/*     */               } 
/*     */             } 
/*     */             
/* 299 */             cptShare.setCptShareByCpt(str11);
/* 300 */             if (str20.equals("2") || str20.equals("3")) {
/* 301 */               recordSet2.execute("Update CptCapital Set deprestartdate = null where id = " + str11);
/*     */             }
/* 303 */             if (!str14.equals("")) {
/* 304 */               recordSet2.execute("update CptCapital set location='" + str14 + "' where id=" + str11);
/*     */             }
/* 306 */             if (!"".equals(str8)) {
/* 307 */               recordSet2.execute("update CptCapital set remark='" + str8 + "' where id = " + str11);
/*     */             }
/*     */             
/* 310 */             this.cptWfUtil.updateCptFieldOfWF(map1, hashMap, map2, jSONArray1.getJSONObject(b), str11, this.user.getUID());
/*     */             
/* 312 */             CptRightShareUitl.editCapitalResetRight(str11);
/*     */           } 
/* 314 */         }  capitalComInfo.removeCapitalCache();
/* 315 */       } catch (Exception exception) {
/* 316 */         exception.printStackTrace();
/* 317 */         baseBean.writeLog(exception.getMessage());
/*     */       } 
/*     */       
/* 320 */       Thread thread = new Thread() {
/*     */           public void run() {
/*     */             try {
/* 323 */               sleep(15000L);
/* 324 */             } catch (InterruptedException interruptedException) {
/* 325 */               interruptedException.printStackTrace();
/*     */             } 
/* 327 */             RecordSet recordSet = new RecordSet();
/* 328 */             recordSet.execute("update cptcapital set frozennum=0 where frozennum>0");
/* 329 */             CptWfUtil cptWfUtil = new CptWfUtil();
/* 330 */             cptWfUtil.DoFrozenCpt_new();
/*     */           }
/*     */         };
/* 333 */       thread.start();
/*     */       
/* 335 */       return "1";
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/cpt/wfactions/CptBackAction.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */