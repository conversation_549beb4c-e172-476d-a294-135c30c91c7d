/*     */ package weaver.cpt.wfactions;
/*     */ 
/*     */ import com.engine.cpt.util.CptRightShareUitl;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import org.json.JSONArray;
/*     */ import org.json.JSONObject;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.cpt.capital.CapitalComInfo;
/*     */ import weaver.cpt.util.CptWfUtil;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ import weaver.interfaces.workflow.action.Action;
/*     */ import weaver.soa.workflow.request.Cell;
/*     */ import weaver.soa.workflow.request.DetailTable;
/*     */ import weaver.soa.workflow.request.Property;
/*     */ import weaver.soa.workflow.request.RequestInfo;
/*     */ import weaver.soa.workflow.request.Row;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CptMendAction
/*     */   implements Action, Runnable
/*     */ {
/*  35 */   private static BaseBean baseBean = new BaseBean();
/*  36 */   private static Object lock = new Object();
/*  37 */   private CptWfUtil cptWfUtil = new CptWfUtil();
/*  38 */   private RequestInfo request = null;
/*  39 */   private JSONObject wfObject = null;
/*  40 */   private User user = null;
/*     */   
/*     */   public String execute(RequestInfo paramRequestInfo) {
/*  43 */     this.request = paramRequestInfo;
/*  44 */     this.user = paramRequestInfo.getRequestManager().getUser();
/*     */     try {
/*  46 */       this.wfObject = this.cptWfUtil.getCptwfInfo(paramRequestInfo.getWorkflowid());
/*  47 */       if (this.wfObject.getInt("zctype") != this.wfObject.getInt("sltype") || "".equals(this.wfObject.getString("zcname")) || "".equals(this.wfObject.getString("slname"))) {
/*  48 */         paramRequestInfo.getRequestManager().setMessageid("20088");
/*  49 */         paramRequestInfo.getRequestManager().setMessagecontent(SystemEnv.getHtmlLabelName(125188, this.user.getLanguage()));
/*  50 */         return "0";
/*     */       } 
/*     */       
/*  53 */       if ("1".equals(this.wfObject.getString("isasync"))) {
/*  54 */         (new Thread(this)).start();
/*     */       } else {
/*  56 */         doAction(paramRequestInfo);
/*     */       } 
/*  58 */     } catch (Exception exception) {
/*  59 */       exception.printStackTrace();
/*  60 */       baseBean.writeLog(exception.getMessage());
/*     */     } 
/*  62 */     return "1";
/*     */   }
/*     */   
/*     */   public void run() {
/*  66 */     doAction(this.request);
/*     */   }
/*     */   
/*     */   private String doAction(RequestInfo paramRequestInfo) {
/*  70 */     synchronized (lock) {
/*  71 */       baseBean.writeLog("tagtag run action :" + getClass() + ",requestid:" + paramRequestInfo.getRequestid());
/*  72 */       CapitalComInfo capitalComInfo = null;
/*  73 */       ResourceComInfo resourceComInfo = null;
/*     */       try {
/*  75 */         capitalComInfo = new CapitalComInfo();
/*  76 */         resourceComInfo = new ResourceComInfo();
/*     */         
/*  78 */         RecordSet recordSet = new RecordSet();
/*  79 */         char c = Util.getSeparator();
/*  80 */         String str1 = paramRequestInfo.getRequestid();
/*     */         
/*  82 */         Map map1 = this.cptWfUtil.getcusField(paramRequestInfo.getWorkflowid(), "0");
/*  83 */         Map map2 = new HashMap<>();
/*  84 */         HashMap<Object, Object> hashMap = new HashMap<>();
/*  85 */         JSONArray jSONArray1 = new JSONArray();
/*     */         
/*  87 */         String str2 = "";
/*  88 */         String str3 = "";
/*  89 */         String str4 = "";
/*  90 */         String str5 = "";
/*  91 */         String str6 = "";
/*  92 */         String str7 = "";
/*  93 */         String str8 = "";
/*  94 */         String str9 = "";
/*  95 */         String str10 = "";
/*  96 */         String str11 = "";
/*     */ 
/*     */         
/*  99 */         JSONArray jSONArray2 = new JSONArray();
/* 100 */         Property[] arrayOfProperty = paramRequestInfo.getMainTableInfo().getProperty(); byte b;
/* 101 */         for (b = 0; b < arrayOfProperty.length; b++) {
/* 102 */           String str12 = arrayOfProperty[b].getName();
/* 103 */           String str13 = Util.null2String(arrayOfProperty[b].getValue());
/* 104 */           if (this.wfObject.getInt("sqrtype") == 0 && str12.equalsIgnoreCase(this.wfObject.getString("sqrname"))) {
/* 105 */             str2 = str13;
/* 106 */             str4 = resourceComInfo.getDepartmentID(str2);
/*     */           } 
/* 108 */           if (this.wfObject.getInt("zctype") == 0 && str12.equalsIgnoreCase(this.wfObject.getString("zcname"))) {
/* 109 */             str3 = str13;
/*     */           }
/* 111 */           if (this.wfObject.getInt("sltype") == 0 && str12.equalsIgnoreCase(this.wfObject.getString("slname"))) {
/* 112 */             str6 = str13;
/*     */           }
/* 114 */           if (this.wfObject.getInt("rqtype") == 0 && str12.equalsIgnoreCase(this.wfObject.getString("rqname"))) {
/* 115 */             str5 = str13;
/*     */           }
/* 117 */           if (this.wfObject.getInt("cfddtype") == 0 && str12.equalsIgnoreCase(this.wfObject.getString("cfddname"))) {
/* 118 */             str7 = str13;
/*     */           }
/* 120 */           if (this.wfObject.getInt("bztype") == 0 && str12.equalsIgnoreCase(this.wfObject.getString("bzname"))) {
/* 121 */             str8 = str13;
/*     */           }
/* 123 */           if (this.wfObject.getInt("jgtype") == 0 && str12.equalsIgnoreCase(this.wfObject.getString("jgname"))) {
/* 124 */             str9 = str13;
/*     */           }
/* 126 */           if (this.wfObject.getInt("wxqxtype") == 0 && str12.equalsIgnoreCase(this.wfObject.getString("wxqxname"))) {
/* 127 */             str10 = str13;
/*     */           }
/* 129 */           if (this.wfObject.getInt("wxdwtype") == 0 && str12.equalsIgnoreCase(this.wfObject.getString("wxdwname"))) {
/* 130 */             str11 = str13;
/*     */           }
/*     */           
/* 133 */           for (String str : map1.keySet()) {
/* 134 */             JSONObject jSONObject = (JSONObject)map1.get(str);
/* 135 */             if (str12.equalsIgnoreCase(Util.null2String(jSONObject.get("fieldname")))) {
/* 136 */               hashMap.put(str, str13);
/*     */             }
/*     */           } 
/*     */         } 
/*     */         
/* 141 */         if (this.wfObject.getInt("zctype") == 0 && this.wfObject.getInt("sltype") == 0) {
/* 142 */           JSONObject jSONObject1 = new JSONObject();
/* 143 */           jSONObject1.put("sqr", str2);
/* 144 */           jSONObject1.put("sqbm", str4);
/* 145 */           jSONObject1.put("zc", str3);
/* 146 */           jSONObject1.put("sl", str6);
/* 147 */           jSONObject1.put("rq", str5);
/* 148 */           jSONObject1.put("cfdd", str7);
/* 149 */           jSONObject1.put("bz", str8);
/* 150 */           jSONObject1.put("jg", str9);
/* 151 */           jSONObject1.put("wxqx", str10);
/* 152 */           jSONObject1.put("wxdw", str11);
/* 153 */           jSONArray2.put(jSONObject1);
/*     */ 
/*     */           
/* 156 */           JSONObject jSONObject2 = new JSONObject();
/* 157 */           for (String str12 : hashMap.keySet()) {
/* 158 */             String str13 = (String)hashMap.get(str12);
/* 159 */             jSONObject2.put(str12, str13);
/*     */           } 
/* 161 */           jSONArray1.put(jSONObject2);
/* 162 */         } else if (this.wfObject.getInt("zctype") == this.wfObject.getInt("sltype")) {
/* 163 */           DetailTable[] arrayOfDetailTable = paramRequestInfo.getDetailTableInfo().getDetailTable();
/* 164 */           String str = Util.null2String(Integer.valueOf(this.wfObject.getInt("zctype")));
/*     */           
/* 166 */           map2 = this.cptWfUtil.getcusField(paramRequestInfo.getWorkflowid(), str);
/* 167 */           DetailTable detailTable = null;
/* 168 */           for (DetailTable detailTable1 : arrayOfDetailTable) {
/* 169 */             String str12 = detailTable1.getTableDBName();
/* 170 */             if (!"".equals(str12)) {
/* 171 */               String str13 = str12.substring(str12.length() - 1);
/* 172 */               if (str13.equals(str)) {
/* 173 */                 detailTable = detailTable1;
/*     */                 break;
/*     */               } 
/*     */             } 
/*     */           } 
/* 178 */           if (detailTable != null) {
/* 179 */             Row[] arrayOfRow = detailTable.getRow();
/* 180 */             for (byte b1 = 0; b1 < arrayOfRow.length; b1++) {
/* 181 */               Row row = arrayOfRow[b1];
/* 182 */               Cell[] arrayOfCell = row.getCell();
/*     */               
/* 184 */               HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 185 */               for (byte b2 = 0; b2 < arrayOfCell.length; b2++) {
/* 186 */                 Cell cell = arrayOfCell[b2];
/* 187 */                 String str12 = cell.getName().toLowerCase();
/* 188 */                 String str13 = Util.null2String(cell.getValue());
/* 189 */                 if (str12.equalsIgnoreCase(this.wfObject.getString("sqrname")) && this.wfObject.getInt("zctype") == this.wfObject.getInt("sqrtype")) {
/* 190 */                   str2 = str13;
/* 191 */                   str4 = resourceComInfo.getDepartmentID(str2);
/*     */                 } 
/* 193 */                 if (str12.equalsIgnoreCase(this.wfObject.getString("zcname"))) {
/* 194 */                   str3 = str13;
/*     */                 }
/* 196 */                 if (str12.equalsIgnoreCase(this.wfObject.getString("slname"))) {
/* 197 */                   str6 = str13;
/*     */                 }
/* 199 */                 if (str12.equalsIgnoreCase(this.wfObject.getString("rqname")) && this.wfObject.getInt("zctype") == this.wfObject.getInt("rqtype")) {
/* 200 */                   str5 = str13;
/*     */                 }
/* 202 */                 if (str12.equalsIgnoreCase(this.wfObject.getString("cfddname")) && this.wfObject.getInt("zctype") == this.wfObject.getInt("cfddtype")) {
/* 203 */                   str7 = str13;
/*     */                 }
/* 205 */                 if (str12.equalsIgnoreCase(this.wfObject.getString("bzname")) && this.wfObject.getInt("zctype") == this.wfObject.getInt("bztype")) {
/* 206 */                   str8 = str13;
/*     */                 }
/* 208 */                 if (str12.equalsIgnoreCase(this.wfObject.getString("jgname")) && this.wfObject.getInt("zctype") == this.wfObject.getInt("jgtype")) {
/* 209 */                   str9 = str13;
/*     */                 }
/* 211 */                 if (str12.equalsIgnoreCase(this.wfObject.getString("wxqxname")) && this.wfObject.getInt("zctype") == this.wfObject.getInt("wxqxtype")) {
/* 212 */                   str10 = str13;
/*     */                 }
/* 214 */                 if (str12.equalsIgnoreCase(this.wfObject.getString("wxdwname")) && this.wfObject.getInt("zctype") == this.wfObject.getInt("wxdwtype")) {
/* 215 */                   str11 = str13;
/*     */                 }
/*     */                 
/* 218 */                 for (String str14 : map2.keySet()) {
/* 219 */                   JSONObject jSONObject = (JSONObject)map2.get(str14);
/* 220 */                   if (str12.equalsIgnoreCase(Util.null2String(jSONObject.get("fieldname")))) {
/* 221 */                     hashMap1.put(str14, str13);
/*     */                   }
/*     */                 } 
/*     */               } 
/* 225 */               JSONObject jSONObject1 = new JSONObject();
/* 226 */               jSONObject1.put("sqr", str2);
/* 227 */               jSONObject1.put("sqbm", str4);
/* 228 */               jSONObject1.put("zc", str3);
/* 229 */               jSONObject1.put("sl", str6);
/* 230 */               jSONObject1.put("rq", str5);
/* 231 */               jSONObject1.put("cfdd", str7);
/* 232 */               jSONObject1.put("bz", str8);
/* 233 */               jSONObject1.put("jg", str9);
/* 234 */               jSONObject1.put("wxqx", str10);
/* 235 */               jSONObject1.put("wxdw", str11);
/* 236 */               jSONArray2.put(jSONObject1);
/*     */ 
/*     */               
/* 239 */               JSONObject jSONObject2 = new JSONObject();
/* 240 */               for (String str12 : hashMap1.keySet()) {
/* 241 */                 String str13 = (String)hashMap1.get(str12);
/* 242 */                 jSONObject2.put(str12, str13);
/*     */               } 
/* 244 */               jSONArray1.put(jSONObject2);
/*     */             } 
/*     */           } 
/*     */         } 
/*     */ 
/*     */         
/* 250 */         for (b = 0; b < jSONArray2.length(); b++) {
/* 251 */           JSONObject jSONObject = jSONArray2.getJSONObject(b);
/* 252 */           String str12 = "";
/* 253 */           String str13 = jSONObject.getString("zc");
/* 254 */           String str14 = jSONObject.getString("sl");
/* 255 */           String str15 = jSONObject.getString("rq");
/* 256 */           str4 = jSONObject.getString("sqbm");
/* 257 */           str2 = jSONObject.getString("sqr");
/* 258 */           String str16 = jSONObject.getString("cfdd");
/* 259 */           str8 = jSONObject.getString("bz");
/* 260 */           str9 = jSONObject.getString("jg");
/* 261 */           str10 = jSONObject.getString("wxqx");
/* 262 */           str11 = jSONObject.getString("wxdw");
/*     */           
/* 264 */           if (Util.getIntValue(str13, 0) > 0 && Util.getDoubleValue(str14, 0.0D) > 0.0D) {
/*     */ 
/*     */             
/* 267 */             str12 = str13;
/* 268 */             str12 = str12 + c + str15;
/* 269 */             str12 = str12 + c + str4;
/* 270 */             str12 = str12 + c + str2;
/* 271 */             str12 = str12 + c + "1";
/* 272 */             str12 = str12 + c + str16;
/* 273 */             str12 = str12 + c + str1;
/* 274 */             str12 = str12 + c + str11;
/* 275 */             str12 = str12 + c + "" + Util.getDoubleValue(str9, 0.0D);
/* 276 */             str12 = str12 + c + "4";
/* 277 */             str12 = str12 + c + str8;
/* 278 */             str12 = str12 + c + str2;
/* 279 */             str12 = str12 + c + str10;
/* 280 */             recordSet.executeProc("CptUseLogMend_Insert", str12);
/*     */             
/* 282 */             if (!str8.equals("")) {
/* 283 */               recordSet.execute("update CptCapital set remark='" + str8 + "' where id=" + str13);
/*     */             }
/*     */             
/* 286 */             this.cptWfUtil.updateCptFieldOfWF(map1, hashMap, map2, jSONArray1.getJSONObject(b), str13, this.user.getUID());
/*     */             
/* 288 */             CptRightShareUitl.editCapitalResetRight(str13);
/*     */           } 
/* 290 */         }  capitalComInfo.removeCapitalCache();
/* 291 */       } catch (Exception exception) {
/* 292 */         exception.printStackTrace();
/* 293 */         baseBean.writeLog(exception.getMessage());
/*     */       } 
/*     */       
/* 296 */       Thread thread = new Thread() {
/*     */           public void run() {
/*     */             try {
/* 299 */               sleep(15000L);
/* 300 */             } catch (InterruptedException interruptedException) {
/* 301 */               interruptedException.printStackTrace();
/*     */             } 
/* 303 */             RecordSet recordSet = new RecordSet();
/* 304 */             recordSet.execute("update cptcapital set frozennum=0 where frozennum>0");
/* 305 */             CptWfUtil cptWfUtil = new CptWfUtil();
/* 306 */             cptWfUtil.DoFrozenCpt_new();
/*     */           }
/*     */         };
/* 309 */       thread.start();
/*     */       
/* 311 */       return "1";
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/cpt/wfactions/CptMendAction.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */