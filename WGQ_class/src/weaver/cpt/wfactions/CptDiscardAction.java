/*     */ package weaver.cpt.wfactions;
/*     */ 
/*     */ import com.engine.cpt.util.CptRightShareUitl;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import org.json.JSONArray;
/*     */ import org.json.JSONObject;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.cpt.capital.CapitalComInfo;
/*     */ import weaver.cpt.capital.CptShare;
/*     */ import weaver.cpt.util.CptDwrUtil;
/*     */ import weaver.cpt.util.CptWfUtil;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ import weaver.interfaces.workflow.action.Action;
/*     */ import weaver.soa.workflow.request.Cell;
/*     */ import weaver.soa.workflow.request.DetailTable;
/*     */ import weaver.soa.workflow.request.Property;
/*     */ import weaver.soa.workflow.request.RequestInfo;
/*     */ import weaver.soa.workflow.request.Row;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CptDiscardAction
/*     */   implements Action, Runnable
/*     */ {
/*  37 */   private static BaseBean baseBean = new BaseBean();
/*  38 */   private static Object lock = new Object();
/*  39 */   private CptWfUtil cptWfUtil = new CptWfUtil();
/*  40 */   private RequestInfo request = null;
/*  41 */   private JSONObject wfObject = null;
/*  42 */   private User user = null;
/*     */   
/*     */   public String execute(RequestInfo paramRequestInfo) {
/*  45 */     this.request = paramRequestInfo;
/*  46 */     this.user = paramRequestInfo.getRequestManager().getUser();
/*     */     try {
/*  48 */       this.wfObject = this.cptWfUtil.getCptwfInfo(paramRequestInfo.getWorkflowid());
/*  49 */       if (this.wfObject.getInt("zctype") != this.wfObject.getInt("sltype") || "".equals(this.wfObject.getString("zcname")) || "".equals(this.wfObject.getString("slname"))) {
/*  50 */         paramRequestInfo.getRequestManager().setMessageid("20088");
/*  51 */         paramRequestInfo.getRequestManager().setMessagecontent(SystemEnv.getHtmlLabelName(125188, this.user.getLanguage()));
/*  52 */         return "0";
/*     */       } 
/*     */       
/*  55 */       if ("1".equals(this.wfObject.getString("isasync"))) {
/*  56 */         (new Thread(this)).start();
/*     */       } else {
/*  58 */         doAction(paramRequestInfo);
/*     */       } 
/*  60 */     } catch (Exception exception) {
/*  61 */       exception.printStackTrace();
/*  62 */       baseBean.writeLog(exception.getMessage());
/*     */     } 
/*  64 */     return "1";
/*     */   }
/*     */   
/*     */   public void run() {
/*  68 */     doAction(this.request);
/*     */   }
/*     */   
/*     */   private String doAction(RequestInfo paramRequestInfo) {
/*  72 */     synchronized (lock) {
/*  73 */       baseBean.writeLog("tagtag run action :" + getClass() + ",requestid:" + paramRequestInfo.getRequestid());
/*  74 */       CptShare cptShare = new CptShare();
/*  75 */       CapitalComInfo capitalComInfo = null;
/*  76 */       ResourceComInfo resourceComInfo = null;
/*     */       try {
/*  78 */         capitalComInfo = new CapitalComInfo();
/*  79 */         resourceComInfo = new ResourceComInfo();
/*     */         
/*  81 */         RecordSet recordSet = new RecordSet();
/*  82 */         char c = Util.getSeparator();
/*  83 */         String str1 = paramRequestInfo.getRequestid();
/*     */         
/*  85 */         Map map1 = this.cptWfUtil.getcusField(paramRequestInfo.getWorkflowid(), "0");
/*  86 */         Map map2 = new HashMap<>();
/*  87 */         HashMap<Object, Object> hashMap = new HashMap<>();
/*  88 */         JSONArray jSONArray1 = new JSONArray();
/*     */         
/*  90 */         String str2 = "";
/*  91 */         String str3 = "";
/*  92 */         String str4 = "";
/*  93 */         String str5 = "";
/*  94 */         String str6 = "";
/*  95 */         String str7 = "";
/*  96 */         String str8 = "";
/*  97 */         String str9 = "";
/*     */ 
/*     */         
/* 100 */         JSONArray jSONArray2 = new JSONArray();
/*     */         
/* 102 */         Property[] arrayOfProperty = paramRequestInfo.getMainTableInfo().getProperty(); byte b;
/* 103 */         for (b = 0; b < arrayOfProperty.length; b++) {
/* 104 */           String str10 = arrayOfProperty[b].getName();
/* 105 */           String str11 = Util.null2String(arrayOfProperty[b].getValue());
/* 106 */           if (this.wfObject.getInt("sqrtype") == 0 && str10.equalsIgnoreCase(this.wfObject.getString("sqrname"))) {
/* 107 */             str2 = str11;
/* 108 */             str4 = resourceComInfo.getDepartmentID(str2);
/*     */           } 
/* 110 */           if (this.wfObject.getInt("zctype") == 0 && str10.equalsIgnoreCase(this.wfObject.getString("zcname"))) {
/* 111 */             str3 = str11;
/*     */           }
/* 113 */           if (this.wfObject.getInt("sltype") == 0 && str10.equalsIgnoreCase(this.wfObject.getString("slname"))) {
/* 114 */             str6 = str11;
/*     */           }
/* 116 */           if (this.wfObject.getInt("rqtype") == 0 && str10.equalsIgnoreCase(this.wfObject.getString("rqname"))) {
/* 117 */             str5 = str11;
/*     */           }
/* 119 */           if (this.wfObject.getInt("cfddtype") == 0 && str10.equalsIgnoreCase(this.wfObject.getString("cfddname"))) {
/* 120 */             str7 = str11;
/*     */           }
/* 122 */           if (this.wfObject.getInt("bztype") == 0 && str10.equalsIgnoreCase(this.wfObject.getString("bzname"))) {
/* 123 */             str8 = str11;
/*     */           }
/* 125 */           if (this.wfObject.getInt("jgtype") == 0 && str10.equalsIgnoreCase(this.wfObject.getString("jgname"))) {
/* 126 */             str9 = str11;
/*     */           }
/*     */           
/* 129 */           for (String str : map1.keySet()) {
/* 130 */             JSONObject jSONObject = (JSONObject)map1.get(str);
/* 131 */             if (str10.equalsIgnoreCase(Util.null2String(jSONObject.get("fieldname")))) {
/* 132 */               hashMap.put(str, str11);
/*     */             }
/*     */           } 
/*     */         } 
/*     */         
/* 137 */         if (this.wfObject.getInt("zctype") == 0 && this.wfObject.getInt("sltype") == 0) {
/* 138 */           JSONObject jSONObject1 = new JSONObject();
/* 139 */           jSONObject1.put("sqr", str2);
/* 140 */           jSONObject1.put("sqbm", str4);
/* 141 */           jSONObject1.put("zc", str3);
/* 142 */           jSONObject1.put("sl", str6);
/* 143 */           jSONObject1.put("rq", str5);
/* 144 */           jSONObject1.put("cfdd", str7);
/* 145 */           jSONObject1.put("bz", str8);
/* 146 */           jSONObject1.put("jg", str9);
/* 147 */           jSONArray2.put(jSONObject1);
/*     */ 
/*     */           
/* 150 */           JSONObject jSONObject2 = new JSONObject();
/* 151 */           for (String str10 : hashMap.keySet()) {
/* 152 */             String str11 = (String)hashMap.get(str10);
/* 153 */             jSONObject2.put(str10, str11);
/*     */           } 
/* 155 */           jSONArray1.put(jSONObject2);
/*     */         }
/* 157 */         else if (this.wfObject.getInt("zctype") == this.wfObject.getInt("sltype")) {
/* 158 */           DetailTable[] arrayOfDetailTable = paramRequestInfo.getDetailTableInfo().getDetailTable();
/* 159 */           String str = Util.null2String(Integer.valueOf(this.wfObject.getInt("zctype")));
/*     */           
/* 161 */           map2 = this.cptWfUtil.getcusField(paramRequestInfo.getWorkflowid(), str);
/* 162 */           DetailTable detailTable = null;
/* 163 */           for (DetailTable detailTable1 : arrayOfDetailTable) {
/* 164 */             String str10 = detailTable1.getTableDBName();
/* 165 */             if (!"".equals(str10)) {
/* 166 */               String str11 = str10.substring(str10.length() - 1);
/* 167 */               if (str11.equals(str)) {
/* 168 */                 detailTable = detailTable1;
/*     */                 break;
/*     */               } 
/*     */             } 
/*     */           } 
/* 173 */           if (detailTable != null) {
/* 174 */             Row[] arrayOfRow = detailTable.getRow();
/* 175 */             for (byte b1 = 0; b1 < arrayOfRow.length; b1++) {
/* 176 */               Row row = arrayOfRow[b1];
/* 177 */               Cell[] arrayOfCell = row.getCell();
/*     */               
/* 179 */               HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 180 */               for (byte b2 = 0; b2 < arrayOfCell.length; b2++) {
/* 181 */                 Cell cell = arrayOfCell[b2];
/* 182 */                 String str10 = cell.getName().toLowerCase();
/* 183 */                 String str11 = Util.null2String(cell.getValue());
/* 184 */                 if (str10.equalsIgnoreCase(this.wfObject.getString("sqrname")) && this.wfObject.getInt("zctype") == this.wfObject.getInt("sqrtype")) {
/* 185 */                   str2 = str11;
/* 186 */                   str4 = resourceComInfo.getDepartmentID(str2);
/*     */                 } 
/* 188 */                 if (str10.equalsIgnoreCase(this.wfObject.getString("zcname"))) {
/* 189 */                   str3 = str11;
/*     */                 }
/* 191 */                 if (str10.equalsIgnoreCase(this.wfObject.getString("slname"))) {
/* 192 */                   str6 = str11;
/*     */                 }
/* 194 */                 if (str10.equalsIgnoreCase(this.wfObject.getString("rqname")) && this.wfObject.getInt("zctype") == this.wfObject.getInt("rqtype")) {
/* 195 */                   str5 = str11;
/*     */                 }
/* 197 */                 if (str10.equalsIgnoreCase(this.wfObject.getString("cfddname")) && this.wfObject.getInt("zctype") == this.wfObject.getInt("cfddtype")) {
/* 198 */                   str7 = str11;
/*     */                 }
/* 200 */                 if (str10.equalsIgnoreCase(this.wfObject.getString("bzname")) && this.wfObject.getInt("zctype") == this.wfObject.getInt("bztype")) {
/* 201 */                   str8 = str11;
/*     */                 }
/* 203 */                 if (str10.equalsIgnoreCase(this.wfObject.getString("jgname")) && this.wfObject.getInt("zctype") == this.wfObject.getInt("jgtype")) {
/* 204 */                   str9 = str11;
/*     */                 }
/*     */                 
/* 207 */                 for (String str12 : map2.keySet()) {
/* 208 */                   JSONObject jSONObject = (JSONObject)map2.get(str12);
/* 209 */                   if (str10.equalsIgnoreCase(Util.null2String(jSONObject.get("fieldname")))) {
/* 210 */                     hashMap1.put(str12, str11);
/*     */                   }
/*     */                 } 
/*     */               } 
/* 214 */               JSONObject jSONObject1 = new JSONObject();
/* 215 */               jSONObject1.put("sqr", str2);
/* 216 */               jSONObject1.put("sqbm", str4);
/* 217 */               jSONObject1.put("zc", str3);
/* 218 */               jSONObject1.put("sl", str6);
/* 219 */               jSONObject1.put("rq", str5);
/* 220 */               jSONObject1.put("cfdd", str7);
/* 221 */               jSONObject1.put("bz", str8);
/* 222 */               jSONObject1.put("jg", str9);
/* 223 */               jSONArray2.put(jSONObject1);
/*     */ 
/*     */               
/* 226 */               JSONObject jSONObject2 = new JSONObject();
/* 227 */               for (String str10 : hashMap1.keySet()) {
/* 228 */                 String str11 = (String)hashMap1.get(str10);
/* 229 */                 jSONObject2.put(str10, str11);
/*     */               } 
/* 231 */               jSONArray1.put(jSONObject2);
/*     */             } 
/*     */           } 
/*     */         } 
/*     */ 
/*     */         
/* 237 */         for (b = 0; b < jSONArray2.length(); b++) {
/* 238 */           JSONObject jSONObject = jSONArray2.getJSONObject(b);
/* 239 */           String str10 = "";
/* 240 */           String str11 = "";
/* 241 */           String str12 = jSONObject.getString("zc");
/* 242 */           String str13 = jSONObject.getString("sl");
/* 243 */           String str14 = jSONObject.getString("rq");
/* 244 */           str4 = jSONObject.getString("sqbm");
/* 245 */           str2 = jSONObject.getString("sqr");
/* 246 */           String str15 = jSONObject.getString("cfdd");
/* 247 */           str8 = jSONObject.getString("bz");
/* 248 */           str9 = jSONObject.getString("jg");
/*     */           
/* 250 */           if (Util.getIntValue(str12, 0) > 0 && Util.getDoubleValue(str13, 0.0D) > 0.0D)
/*     */           
/*     */           { 
/* 253 */             str11 = Util.null2String((String)(new CptDwrUtil()).getCptInfoMap(str12).get("sptcount"));
/* 254 */             if ("".equals(str11)) {
/* 255 */               str11 = "0";
/*     */             }
/* 257 */             if (str11.equals("1")) {
/* 258 */               str10 = str12;
/* 259 */               str10 = str10 + c + str14;
/* 260 */               str10 = str10 + c + str4;
/* 261 */               str10 = str10 + c + str2;
/* 262 */               str10 = str10 + c + "1";
/* 263 */               str10 = str10 + c + str15;
/* 264 */               str10 = str10 + c + str1;
/* 265 */               str10 = str10 + c + "";
/* 266 */               str10 = str10 + c + "" + Util.getDoubleValue(str9, 0.0D);
/* 267 */               str10 = str10 + c + "5";
/* 268 */               str10 = str10 + c + str8;
/* 269 */               str10 = str10 + c + str11;
/* 270 */               recordSet.executeProc("CptUseLogDiscard_Insert", str10);
/*     */             } else {
/* 272 */               str10 = str12;
/* 273 */               str10 = str10 + c + str14;
/* 274 */               str10 = str10 + c + str4;
/* 275 */               str10 = str10 + c + str2;
/* 276 */               str10 = str10 + c + "" + Util.getDoubleValue(str13, 0.0D);
/* 277 */               str10 = str10 + c + str15;
/* 278 */               str10 = str10 + c + str1;
/* 279 */               str10 = str10 + c + "";
/* 280 */               str10 = str10 + c + "" + Util.getDoubleValue(str9, 0.0D);
/* 281 */               str10 = str10 + c + "5";
/* 282 */               str10 = str10 + c + str8;
/* 283 */               str10 = str10 + c + str11;
/*     */               
/* 285 */               recordSet.executeProc("CptUseLogDiscard_Insert", str10);
/* 286 */               recordSet.next();
/* 287 */               String str = recordSet.getString(1);
/*     */               
/* 289 */               if (str.equals("-1")) {
/* 290 */                 paramRequestInfo.getRequestManager().setMessageid("20088");
/* 291 */                 paramRequestInfo.getRequestManager().setMessagecontent(capitalComInfo.getCapitalname(str12) + " " + SystemEnv.getHtmlLabelName(503149, this.user.getLanguage()));
/* 292 */                 return "0";
/*     */               } 
/*     */             } 
/* 295 */             if (!str15.equals("")) {
/* 296 */               recordSet.execute("update CptCapital set location='" + str15 + "' where id=" + str12);
/*     */             }
/* 298 */             if (!str8.equals("")) {
/* 299 */               recordSet.execute("update CptCapital set remark='" + str8 + "' where id=" + str12);
/*     */             }
/*     */             
/* 302 */             this.cptWfUtil.updateCptFieldOfWF(map1, hashMap, map2, jSONArray1.getJSONObject(b), str12, this.user.getUID());
/*     */ 
/*     */             
/* 305 */             CptRightShareUitl.editCapitalResetRight(str12);
/*     */             
/* 307 */             cptShare.setCptShareByCpt(str12);
/*     */             
/* 309 */             capitalComInfo.updateCapitalCache(str12); } 
/*     */         } 
/* 311 */       } catch (Exception exception) {
/* 312 */         exception.printStackTrace();
/* 313 */         baseBean.writeLog(exception.getMessage());
/*     */       } 
/*     */       
/* 316 */       Thread thread = new Thread() {
/*     */           public void run() {
/*     */             try {
/* 319 */               sleep(15000L);
/* 320 */             } catch (InterruptedException interruptedException) {
/* 321 */               interruptedException.printStackTrace();
/*     */             } 
/* 323 */             RecordSet recordSet = new RecordSet();
/* 324 */             recordSet.execute("update cptcapital set frozennum=0 where frozennum>0");
/* 325 */             CptWfUtil cptWfUtil = new CptWfUtil();
/* 326 */             cptWfUtil.DoFrozenCpt_new();
/*     */           }
/*     */         };
/* 329 */       thread.start();
/*     */       
/* 331 */       return "1";
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/cpt/wfactions/CptDiscardAction.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */