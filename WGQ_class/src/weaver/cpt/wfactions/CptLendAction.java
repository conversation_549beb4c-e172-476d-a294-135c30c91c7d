/*     */ package weaver.cpt.wfactions;
/*     */ 
/*     */ import com.engine.cpt.util.CptRightShareUitl;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import org.json.JSONArray;
/*     */ import org.json.JSONObject;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.cpt.capital.CapitalComInfo;
/*     */ import weaver.cpt.capital.CptShare;
/*     */ import weaver.cpt.util.CptWfUtil;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ import weaver.interfaces.workflow.action.Action;
/*     */ import weaver.soa.workflow.request.Cell;
/*     */ import weaver.soa.workflow.request.DetailTable;
/*     */ import weaver.soa.workflow.request.Property;
/*     */ import weaver.soa.workflow.request.RequestInfo;
/*     */ import weaver.soa.workflow.request.Row;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CptLendAction
/*     */   implements Action, Runnable
/*     */ {
/*  34 */   private static BaseBean baseBean = new BaseBean();
/*  35 */   private static Object lock = new Object();
/*  36 */   private CptWfUtil cptWfUtil = new CptWfUtil();
/*  37 */   private RequestInfo request = null;
/*  38 */   private JSONObject wfObject = null;
/*  39 */   private User user = null;
/*     */   
/*     */   public String execute(RequestInfo paramRequestInfo) {
/*  42 */     this.request = paramRequestInfo;
/*  43 */     this.user = paramRequestInfo.getRequestManager().getUser();
/*     */     try {
/*  45 */       this.wfObject = this.cptWfUtil.getCptwfInfo(paramRequestInfo.getWorkflowid());
/*  46 */       if (this.wfObject.getInt("zctype") != this.wfObject.getInt("sltype") || "".equals(this.wfObject.getString("zcname")) || "".equals(this.wfObject.getString("slname"))) {
/*  47 */         paramRequestInfo.getRequestManager().setMessageid("20088");
/*  48 */         paramRequestInfo.getRequestManager().setMessagecontent(SystemEnv.getHtmlLabelName(125188, this.user.getLanguage()));
/*  49 */         return "0";
/*     */       } 
/*  51 */       if ("1".equals(this.wfObject.getString("isasync"))) {
/*  52 */         (new Thread(this)).start();
/*     */       } else {
/*  54 */         doAction(paramRequestInfo);
/*     */       } 
/*  56 */     } catch (Exception exception) {
/*  57 */       exception.printStackTrace();
/*  58 */       baseBean.writeLog(exception.getMessage());
/*     */     } 
/*  60 */     return "1";
/*     */   }
/*     */   
/*     */   public void run() {
/*  64 */     doAction(this.request);
/*     */   }
/*     */   
/*     */   private String doAction(RequestInfo paramRequestInfo) {
/*  68 */     synchronized (lock) {
/*  69 */       baseBean.writeLog("tagtag run action :" + getClass() + ",requestid:" + paramRequestInfo.getRequestid());
/*  70 */       CptShare cptShare = new CptShare();
/*  71 */       CapitalComInfo capitalComInfo = null;
/*  72 */       ResourceComInfo resourceComInfo = null;
/*     */       try {
/*  74 */         capitalComInfo = new CapitalComInfo();
/*  75 */         resourceComInfo = new ResourceComInfo();
/*     */         
/*  77 */         RecordSet recordSet1 = new RecordSet();
/*  78 */         String str1 = paramRequestInfo.getRequestid();
/*  79 */         RecordSet recordSet2 = new RecordSet();
/*  80 */         recordSet2.executeSql("select 1 from  CptUseLog where userequest ='" + str1 + "'");
/*  81 */         if (recordSet2.next()) {
/*  82 */           baseBean.writeLog("tagtag duplicate run action :" + getClass() + ",requestid:" + paramRequestInfo.getRequestid());
/*  83 */           return "1";
/*     */         } 
/*     */         
/*  86 */         Map map1 = this.cptWfUtil.getcusField(paramRequestInfo.getWorkflowid(), "0");
/*  87 */         Map map2 = new HashMap<>();
/*  88 */         HashMap<Object, Object> hashMap = new HashMap<>();
/*  89 */         JSONArray jSONArray1 = new JSONArray();
/*     */         
/*  91 */         String str2 = "";
/*  92 */         String str3 = "";
/*  93 */         String str4 = "";
/*  94 */         String str5 = "";
/*  95 */         String str6 = "";
/*  96 */         String str7 = "";
/*  97 */         String str8 = "";
/*     */ 
/*     */         
/* 100 */         JSONArray jSONArray2 = new JSONArray();
/*     */         
/* 102 */         Property[] arrayOfProperty = paramRequestInfo.getMainTableInfo().getProperty(); byte b;
/* 103 */         for (b = 0; b < arrayOfProperty.length; b++) {
/* 104 */           String str9 = arrayOfProperty[b].getName();
/* 105 */           String str10 = Util.null2String(arrayOfProperty[b].getValue());
/*     */           
/* 107 */           if (this.wfObject.getInt("sqrtype") == 0 && str9.equalsIgnoreCase(this.wfObject.getString("sqrname"))) {
/* 108 */             str2 = str10;
/* 109 */             str4 = resourceComInfo.getDepartmentID(str2);
/*     */           } 
/* 111 */           if (this.wfObject.getInt("zctype") == 0 && str9.equalsIgnoreCase(this.wfObject.getString("zcname"))) {
/* 112 */             str3 = str10;
/*     */           }
/* 114 */           if (this.wfObject.getInt("sltype") == 0 && str9.equalsIgnoreCase(this.wfObject.getString("slname"))) {
/* 115 */             str6 = str10;
/*     */           }
/* 117 */           if (this.wfObject.getInt("rqtype") == 0 && str9.equalsIgnoreCase(this.wfObject.getString("rqname"))) {
/* 118 */             str5 = str10;
/*     */           }
/* 120 */           if (this.wfObject.getInt("cfddtype") == 0 && str9.equalsIgnoreCase(this.wfObject.getString("cfddname"))) {
/* 121 */             str7 = str10;
/*     */           }
/* 123 */           if (this.wfObject.getInt("bztype") == 0 && str9.equalsIgnoreCase(this.wfObject.getString("bzname"))) {
/* 124 */             str8 = str10;
/*     */           }
/*     */           
/* 127 */           for (String str : map1.keySet()) {
/* 128 */             JSONObject jSONObject = (JSONObject)map1.get(str);
/* 129 */             if (str9.equalsIgnoreCase(Util.null2String(jSONObject.get("fieldname")))) {
/* 130 */               hashMap.put(str, str10);
/*     */             }
/*     */           } 
/*     */         } 
/*     */         
/* 135 */         if (this.wfObject.getInt("zctype") == 0 && this.wfObject.getInt("sltype") == 0) {
/* 136 */           JSONObject jSONObject1 = new JSONObject();
/* 137 */           jSONObject1.put("sqr", str2);
/* 138 */           jSONObject1.put("sqbm", str4);
/* 139 */           jSONObject1.put("zc", str3);
/* 140 */           jSONObject1.put("sl", str6);
/* 141 */           jSONObject1.put("rq", str5);
/* 142 */           jSONObject1.put("cfdd", str7);
/* 143 */           jSONObject1.put("bz", str8);
/* 144 */           jSONArray2.put(jSONObject1);
/*     */ 
/*     */           
/* 147 */           JSONObject jSONObject2 = new JSONObject();
/* 148 */           for (String str9 : hashMap.keySet()) {
/* 149 */             String str10 = (String)hashMap.get(str9);
/* 150 */             jSONObject2.put(str9, str10);
/*     */           } 
/* 152 */           jSONArray1.put(jSONObject2);
/* 153 */         } else if (this.wfObject.getInt("zctype") == this.wfObject.getInt("sltype")) {
/* 154 */           DetailTable[] arrayOfDetailTable = paramRequestInfo.getDetailTableInfo().getDetailTable();
/* 155 */           String str = Util.null2String(Integer.valueOf(this.wfObject.getInt("zctype")));
/*     */           
/* 157 */           map2 = this.cptWfUtil.getcusField(paramRequestInfo.getWorkflowid(), str);
/* 158 */           DetailTable detailTable = null;
/* 159 */           for (DetailTable detailTable1 : arrayOfDetailTable) {
/* 160 */             String str9 = detailTable1.getTableDBName();
/* 161 */             if (!"".equals(str9)) {
/* 162 */               String str10 = str9.substring(str9.length() - 1);
/* 163 */               if (str10.equals(str)) {
/* 164 */                 detailTable = detailTable1;
/*     */                 break;
/*     */               } 
/*     */             } 
/*     */           } 
/* 169 */           if (detailTable != null) {
/* 170 */             Row[] arrayOfRow = detailTable.getRow();
/*     */             
/* 172 */             for (byte b1 = 0; b1 < arrayOfRow.length; b1++) {
/* 173 */               Row row = arrayOfRow[b1];
/* 174 */               Cell[] arrayOfCell = row.getCell();
/*     */               
/* 176 */               HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 177 */               for (byte b2 = 0; b2 < arrayOfCell.length; b2++) {
/* 178 */                 Cell cell = arrayOfCell[b2];
/* 179 */                 String str9 = cell.getName().toLowerCase();
/* 180 */                 String str10 = Util.null2String(cell.getValue());
/*     */                 
/* 182 */                 if (str9.equalsIgnoreCase(this.wfObject.getString("sqrname")) && this.wfObject.getInt("zctype") == this.wfObject.getInt("sqrtype")) {
/* 183 */                   str2 = str10;
/* 184 */                   str4 = resourceComInfo.getDepartmentID(str2);
/*     */                 } 
/* 186 */                 if (str9.equalsIgnoreCase(this.wfObject.getString("zcname"))) {
/* 187 */                   str3 = str10;
/*     */                 }
/* 189 */                 if (str9.equalsIgnoreCase(this.wfObject.getString("slname"))) {
/* 190 */                   str6 = str10;
/*     */                 }
/* 192 */                 if (str9.equalsIgnoreCase(this.wfObject.getString("rqname")) && this.wfObject.getInt("zctype") == this.wfObject.getInt("rqtype")) {
/* 193 */                   str5 = str10;
/*     */                 }
/* 195 */                 if (str9.equalsIgnoreCase(this.wfObject.getString("cfddname")) && this.wfObject.getInt("zctype") == this.wfObject.getInt("cfddtype")) {
/* 196 */                   str7 = str10;
/*     */                 }
/* 198 */                 if (str9.equalsIgnoreCase(this.wfObject.getString("bzname")) && this.wfObject.getInt("zctype") == this.wfObject.getInt("bztype")) {
/* 199 */                   str8 = str10;
/*     */                 }
/*     */                 
/* 202 */                 for (String str11 : map2.keySet()) {
/* 203 */                   JSONObject jSONObject = (JSONObject)map2.get(str11);
/* 204 */                   if (str9.equalsIgnoreCase(Util.null2String(jSONObject.get("fieldname")))) {
/* 205 */                     hashMap1.put(str11, str10);
/*     */                   }
/*     */                 } 
/*     */               } 
/* 209 */               JSONObject jSONObject1 = new JSONObject();
/* 210 */               jSONObject1.put("sqr", str2);
/* 211 */               jSONObject1.put("sqbm", str4);
/* 212 */               jSONObject1.put("zc", str3);
/* 213 */               jSONObject1.put("sl", str6);
/* 214 */               jSONObject1.put("rq", str5);
/* 215 */               jSONObject1.put("cfdd", str7);
/* 216 */               jSONObject1.put("bz", str8);
/* 217 */               jSONArray2.put(jSONObject1);
/*     */ 
/*     */               
/* 220 */               JSONObject jSONObject2 = new JSONObject();
/* 221 */               for (String str9 : hashMap1.keySet()) {
/* 222 */                 String str10 = (String)hashMap1.get(str9);
/* 223 */                 jSONObject2.put(str9, str10);
/*     */               } 
/* 225 */               jSONArray1.put(jSONObject2);
/*     */             } 
/*     */           } 
/*     */         } 
/*     */         
/* 230 */         for (b = 0; b < jSONArray2.length(); b++) {
/* 231 */           JSONObject jSONObject = jSONArray2.getJSONObject(b);
/* 232 */           String str9 = jSONObject.getString("zc");
/* 233 */           String str10 = jSONObject.getString("sl");
/* 234 */           String str11 = jSONObject.getString("rq");
/* 235 */           str4 = jSONObject.getString("sqbm");
/* 236 */           str2 = jSONObject.getString("sqr");
/* 237 */           String str12 = jSONObject.getString("cfdd");
/* 238 */           str8 = jSONObject.getString("bz");
/* 239 */           if (Util.getIntValue(str9, 0) > 0 && Util.getDoubleValue(str10, 0.0D) > 0.0D) {
/*     */ 
/*     */ 
/*     */             
/* 243 */             recordSet1.executeQuery("select stateid from  cptcapital where id=?", new Object[] { str9 });
/* 244 */             String str = recordSet1.next() ? recordSet1.getString("stateid") : "0";
/* 245 */             if (str.equals("1"))
/*     */             
/*     */             { 
/*     */               
/* 249 */               recordSet1.executeUpdate("insert into CptUseLog(capitalid ,usedate,usedeptid ,useresourceid ,usecount,useaddress ,userequest ,maintaincompany ,fee ,usestatus ,remark) values(?,?,?,?,?,?,?,?,?,?,?)", new Object[] { str9, str11, str4, str2, 
/* 250 */                     Integer.valueOf(1), str12, str1, "", Integer.valueOf(0), "3", str8 });
/* 251 */               recordSet1.executeUpdate("update CptCapital set departmentid=?,resourceid=?,stateid=3,deprestartdate=? where id=?", new Object[] { str4, str2, str11, str9 });
/*     */               
/* 253 */               if (!"".equals(str12)) {
/* 254 */                 recordSet1.execute("update CptCapital set location='" + str12 + "' where id=" + str9);
/*     */               }
/* 256 */               if (!"".equals(str8)) {
/* 257 */                 recordSet1.execute("update CptCapital set remark='" + str8 + "' where id=" + str9);
/*     */               }
/*     */               
/* 260 */               this.cptWfUtil.updateCptFieldOfWF(map1, hashMap, map2, jSONArray1.getJSONObject(b), str9, this.user.getUID());
/*     */ 
/*     */               
/* 263 */               cptShare.setCptShareByCpt(str9);
/*     */               
/* 265 */               CptRightShareUitl.editCapitalResetRight(str9); } 
/*     */           } 
/* 267 */         }  capitalComInfo.removeCapitalCache();
/* 268 */       } catch (Exception exception) {
/* 269 */         exception.printStackTrace();
/* 270 */         baseBean.writeLog(exception.getMessage());
/*     */       } 
/*     */       
/* 273 */       Thread thread = new Thread() {
/*     */           public void run() {
/*     */             try {
/* 276 */               sleep(15000L);
/* 277 */             } catch (InterruptedException interruptedException) {
/* 278 */               interruptedException.printStackTrace();
/*     */             } 
/* 280 */             RecordSet recordSet = new RecordSet();
/* 281 */             recordSet.execute("update cptcapital set frozennum=0 where frozennum>0");
/* 282 */             CptWfUtil cptWfUtil = new CptWfUtil();
/* 283 */             cptWfUtil.DoFrozenCpt_new();
/*     */           }
/*     */         };
/* 286 */       thread.start();
/*     */       
/* 288 */       return "1";
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/cpt/wfactions/CptLendAction.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */