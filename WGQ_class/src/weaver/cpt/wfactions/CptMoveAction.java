/*     */ package weaver.cpt.wfactions;
/*     */ 
/*     */ import com.engine.cpt.util.CapitalTransMethod;
/*     */ import com.engine.cpt.util.CptRightShareUitl;
/*     */ import java.math.BigDecimal;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import org.json.JSONArray;
/*     */ import org.json.JSONObject;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.cpt.capital.CapitalComInfo;
/*     */ import weaver.cpt.capital.CptShare;
/*     */ import weaver.cpt.util.CptWfUtil;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ import weaver.interfaces.workflow.action.Action;
/*     */ import weaver.soa.workflow.request.Cell;
/*     */ import weaver.soa.workflow.request.DetailTable;
/*     */ import weaver.soa.workflow.request.Property;
/*     */ import weaver.soa.workflow.request.RequestInfo;
/*     */ import weaver.soa.workflow.request.Row;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CptMoveAction
/*     */   implements Action, Runnable
/*     */ {
/*  36 */   private static BaseBean baseBean = new BaseBean();
/*  37 */   private static Object lock = new Object();
/*  38 */   private CptWfUtil cptWfUtil = new CptWfUtil();
/*  39 */   private RequestInfo request = null;
/*  40 */   private JSONObject wfObject = null;
/*  41 */   private CptShare CptShare = new CptShare();
/*  42 */   private User user = null;
/*     */   
/*     */   public String execute(RequestInfo paramRequestInfo) {
/*  45 */     this.request = paramRequestInfo;
/*  46 */     this.user = paramRequestInfo.getRequestManager().getUser();
/*     */     try {
/*  48 */       this.wfObject = this.cptWfUtil.getCptwfInfo(paramRequestInfo.getWorkflowid());
/*  49 */       if (this.wfObject.getInt("zctype") != this.wfObject.getInt("sltype") || "".equals(this.wfObject.getString("zcname")) || "".equals(this.wfObject.getString("slname"))) {
/*  50 */         paramRequestInfo.getRequestManager().setMessageid("20088");
/*  51 */         paramRequestInfo.getRequestManager().setMessagecontent(SystemEnv.getHtmlLabelName(125188, this.user.getLanguage()));
/*  52 */         return "0";
/*     */       } 
/*  54 */       CapitalTransMethod capitalTransMethod = new CapitalTransMethod();
/*  55 */       if (capitalTransMethod.IsWareHouseOpen() && "".equals(this.wfObject.getString("rkckname"))) {
/*  56 */         paramRequestInfo.getRequestManager().setMessageid("20088");
/*  57 */         paramRequestInfo.getRequestManager().setMessagecontent(SystemEnv.getHtmlLabelName(517193, this.user.getLanguage()));
/*  58 */         return "0";
/*     */       } 
/*  60 */       if ("1".equals(this.wfObject.getString("isasync"))) {
/*  61 */         (new Thread(this)).start();
/*     */       } else {
/*  63 */         return doAction(paramRequestInfo);
/*     */       } 
/*  65 */     } catch (Exception exception) {
/*  66 */       exception.printStackTrace();
/*  67 */       baseBean.writeLog(exception.getMessage());
/*     */     } 
/*  69 */     return "1";
/*     */   }
/*     */   
/*     */   public void run() {
/*  73 */     doAction(this.request);
/*     */   }
/*     */   
/*     */   private String doAction(RequestInfo paramRequestInfo) {
/*  77 */     synchronized (lock) {
/*  78 */       baseBean.writeLog("tagtag run action :" + getClass() + ",requestid:" + paramRequestInfo.getRequestid());
/*  79 */       CapitalComInfo capitalComInfo = null;
/*  80 */       ResourceComInfo resourceComInfo = null;
/*     */       try {
/*  82 */         capitalComInfo = new CapitalComInfo();
/*  83 */         resourceComInfo = new ResourceComInfo();
/*  84 */         CapitalTransMethod capitalTransMethod = new CapitalTransMethod();
/*  85 */         boolean bool = capitalTransMethod.IsWareHouseOpen();
/*  86 */         RecordSet recordSet = new RecordSet();
/*  87 */         String str1 = paramRequestInfo.getRequestid();
/*     */         
/*  89 */         Map map1 = this.cptWfUtil.getcusField(paramRequestInfo.getWorkflowid(), "0");
/*  90 */         Map map2 = new HashMap<>();
/*  91 */         HashMap<Object, Object> hashMap = new HashMap<>();
/*  92 */         JSONArray jSONArray1 = new JSONArray();
/*     */         
/*  94 */         String str2 = "";
/*  95 */         String str3 = "";
/*  96 */         String str4 = "";
/*  97 */         String str5 = "";
/*  98 */         String str6 = "";
/*  99 */         String str7 = "";
/* 100 */         String str8 = "";
/* 101 */         String str9 = "";
/*     */ 
/*     */         
/* 104 */         JSONArray jSONArray2 = new JSONArray();
/* 105 */         Property[] arrayOfProperty = paramRequestInfo.getMainTableInfo().getProperty(); byte b;
/* 106 */         for (b = 0; b < arrayOfProperty.length; b++) {
/* 107 */           String str10 = arrayOfProperty[b].getName();
/* 108 */           String str11 = Util.null2String(arrayOfProperty[b].getValue());
/* 109 */           if (this.wfObject.getInt("sqrtype") == 0 && str10.equalsIgnoreCase(this.wfObject.getString("sqrname"))) {
/* 110 */             str2 = str11;
/* 111 */             str4 = resourceComInfo.getDepartmentID(str2);
/*     */           } 
/* 113 */           if (this.wfObject.getInt("zctype") == 0 && str10.equalsIgnoreCase(this.wfObject.getString("zcname"))) {
/* 114 */             str3 = str11;
/*     */           }
/* 116 */           if (this.wfObject.getInt("sltype") == 0 && str10.equalsIgnoreCase(this.wfObject.getString("slname"))) {
/* 117 */             str6 = str11;
/*     */           }
/* 119 */           if (this.wfObject.getInt("rqtype") == 0 && str10.equalsIgnoreCase(this.wfObject.getString("rqname"))) {
/* 120 */             str5 = str11;
/*     */           }
/* 122 */           if (this.wfObject.getInt("cfddtype") == 0 && str10.equalsIgnoreCase(this.wfObject.getString("cfddname"))) {
/* 123 */             str7 = str11;
/*     */           }
/* 125 */           if (this.wfObject.getInt("bztype") == 0 && str10.equalsIgnoreCase(this.wfObject.getString("bzname"))) {
/* 126 */             str8 = str11;
/*     */           }
/* 128 */           if (this.wfObject.getInt("rkcktype") == 0 && str10.equalsIgnoreCase(this.wfObject.getString("rkckname"))) {
/* 129 */             if (bool && str11.equalsIgnoreCase("")) {
/* 130 */               paramRequestInfo.getRequestManager().setMessageid("20088");
/* 131 */               paramRequestInfo.getRequestManager().setMessagecontent(SystemEnv.getHtmlLabelName(515444, this.user.getLanguage()));
/* 132 */               return "0";
/*     */             } 
/* 134 */             str9 = str11;
/*     */           } 
/*     */           
/* 137 */           for (String str : map1.keySet()) {
/* 138 */             JSONObject jSONObject = (JSONObject)map1.get(str);
/* 139 */             if (str10.equalsIgnoreCase(Util.null2String(jSONObject.get("fieldname")))) {
/* 140 */               hashMap.put(str, str11);
/*     */             }
/*     */           } 
/*     */         } 
/*     */         
/* 145 */         if (this.wfObject.getInt("zctype") == 0 && this.wfObject.getInt("sltype") == 0) {
/* 146 */           JSONObject jSONObject1 = new JSONObject();
/* 147 */           jSONObject1.put("sqr", str2);
/* 148 */           jSONObject1.put("sqbm", str4);
/* 149 */           jSONObject1.put("zc", str3);
/* 150 */           jSONObject1.put("sl", str6);
/* 151 */           jSONObject1.put("rq", str5);
/* 152 */           jSONObject1.put("cfdd", str7);
/* 153 */           jSONObject1.put("bz", str8);
/* 154 */           jSONObject1.put("rkck", str9);
/* 155 */           jSONArray2.put(jSONObject1);
/*     */ 
/*     */           
/* 158 */           JSONObject jSONObject2 = new JSONObject();
/* 159 */           for (String str10 : hashMap.keySet()) {
/* 160 */             String str11 = (String)hashMap.get(str10);
/* 161 */             jSONObject2.put(str10, str11);
/*     */           } 
/* 163 */           jSONArray1.put(jSONObject2);
/* 164 */         } else if (this.wfObject.getInt("zctype") == this.wfObject.getInt("sltype")) {
/* 165 */           DetailTable[] arrayOfDetailTable = paramRequestInfo.getDetailTableInfo().getDetailTable();
/* 166 */           String str = Util.null2String(Integer.valueOf(this.wfObject.getInt("zctype")));
/*     */           
/* 168 */           map2 = this.cptWfUtil.getcusField(paramRequestInfo.getWorkflowid(), str);
/* 169 */           DetailTable detailTable = null;
/* 170 */           for (DetailTable detailTable1 : arrayOfDetailTable) {
/* 171 */             String str10 = detailTable1.getTableDBName();
/* 172 */             if (!"".equals(str10)) {
/* 173 */               String str11 = str10.substring(str10.length() - 1);
/* 174 */               if (str11.equals(str)) {
/* 175 */                 detailTable = detailTable1;
/*     */                 break;
/*     */               } 
/*     */             } 
/*     */           } 
/* 180 */           if (detailTable != null) {
/* 181 */             Row[] arrayOfRow = detailTable.getRow();
/*     */             
/* 183 */             for (byte b1 = 0; b1 < arrayOfRow.length; b1++) {
/* 184 */               Row row = arrayOfRow[b1];
/* 185 */               Cell[] arrayOfCell = row.getCell();
/*     */               
/* 187 */               HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 188 */               for (byte b2 = 0; b2 < arrayOfCell.length; b2++) {
/* 189 */                 Cell cell = arrayOfCell[b2];
/* 190 */                 String str10 = cell.getName().toLowerCase();
/* 191 */                 String str11 = Util.null2String(cell.getValue());
/* 192 */                 if (str10.equalsIgnoreCase(this.wfObject.getString("sqrname")) && this.wfObject.getInt("zctype") == this.wfObject.getInt("sqrtype")) {
/* 193 */                   str2 = str11;
/* 194 */                   str4 = resourceComInfo.getDepartmentID(str2);
/*     */                 } 
/* 196 */                 if (str10.equalsIgnoreCase(this.wfObject.getString("zcname"))) {
/* 197 */                   str3 = str11;
/*     */                 }
/* 199 */                 if (str10.equalsIgnoreCase(this.wfObject.getString("slname"))) {
/* 200 */                   str6 = str11;
/*     */                 }
/* 202 */                 if (str10.equalsIgnoreCase(this.wfObject.getString("rqname")) && this.wfObject.getInt("zctype") == this.wfObject.getInt("rqtype")) {
/* 203 */                   str5 = str11;
/*     */                 }
/* 205 */                 if (str10.equalsIgnoreCase(this.wfObject.getString("cfddname")) && this.wfObject.getInt("zctype") == this.wfObject.getInt("cfddtype")) {
/* 206 */                   str7 = str11;
/*     */                 }
/* 208 */                 if (str10.equalsIgnoreCase(this.wfObject.getString("bzname")) && this.wfObject.getInt("zctype") == this.wfObject.getInt("bztype")) {
/* 209 */                   str8 = str11;
/*     */                 }
/* 211 */                 if (this.wfObject.getInt("rkcktype") != 0 && str10.equalsIgnoreCase(this.wfObject.getString("rkckname"))) {
/* 212 */                   if (bool && str11.equalsIgnoreCase("")) {
/* 213 */                     paramRequestInfo.getRequestManager().setMessageid("20088");
/* 214 */                     paramRequestInfo.getRequestManager().setMessagecontent(SystemEnv.getHtmlLabelName(515444, this.user.getLanguage()));
/* 215 */                     return "0";
/*     */                   } 
/* 217 */                   str9 = str11;
/*     */                 } 
/*     */ 
/*     */                 
/* 221 */                 for (String str12 : map2.keySet()) {
/* 222 */                   JSONObject jSONObject = (JSONObject)map2.get(str12);
/* 223 */                   if (str10.equalsIgnoreCase(Util.null2String(jSONObject.get("fieldname")))) {
/* 224 */                     hashMap1.put(str12, str11);
/*     */                   }
/*     */                 } 
/*     */               } 
/* 228 */               JSONObject jSONObject1 = new JSONObject();
/* 229 */               jSONObject1.put("sqr", str2);
/* 230 */               jSONObject1.put("sqbm", str4);
/* 231 */               jSONObject1.put("zc", str3);
/* 232 */               jSONObject1.put("sl", str6);
/* 233 */               jSONObject1.put("rq", str5);
/* 234 */               jSONObject1.put("cfdd", str7);
/* 235 */               jSONObject1.put("bz", str8);
/* 236 */               jSONObject1.put("rkck", str9);
/* 237 */               jSONArray2.put(jSONObject1);
/*     */ 
/*     */               
/* 240 */               JSONObject jSONObject2 = new JSONObject();
/* 241 */               for (String str10 : hashMap1.keySet()) {
/* 242 */                 String str11 = (String)hashMap1.get(str10);
/* 243 */                 jSONObject2.put(str10, str11);
/*     */               } 
/* 245 */               jSONArray1.put(jSONObject2);
/*     */             } 
/*     */           } 
/*     */         } 
/*     */ 
/*     */         
/* 251 */         for (b = 0; b < jSONArray2.length(); b++) {
/* 252 */           JSONObject jSONObject = jSONArray2.getJSONObject(b);
/* 253 */           char c = Util.getSeparator();
/* 254 */           String str10 = "";
/* 255 */           String str11 = jSONObject.getString("zc");
/* 256 */           String str12 = jSONObject.getString("sl");
/* 257 */           str4 = jSONObject.getString("sqbm");
/* 258 */           str2 = jSONObject.getString("sqr");
/* 259 */           String str13 = jSONObject.getString("cfdd");
/* 260 */           str8 = jSONObject.getString("bz");
/* 261 */           String str14 = jSONObject.getString("rkck");
/* 262 */           String str15 = capitalComInfo.getSptCount(str11);
/* 263 */           String str16 = "";
/* 264 */           String str17 = "";
/* 265 */           String str18 = "";
/* 266 */           String str19 = "";
/*     */           
/* 268 */           this.cptWfUtil.updateCptFieldOfWF(map1, hashMap, map2, jSONArray1.getJSONObject(b), str11, this.user.getUID());
/* 269 */           recordSet.executeQuery("select datatype,warehouse,blongdepartment,startprice from CptCapital where id=?", new Object[] { str11 });
/* 270 */           if (recordSet.next()) {
/* 271 */             str16 = Util.null2String(recordSet.getString("datatype"));
/* 272 */             str17 = Util.null2String(recordSet.getString("warehouse"));
/* 273 */             str18 = Util.null2String(recordSet.getString("blongdepartment"));
/* 274 */             str19 = Util.null2String(recordSet.getString("CptCapital", "startprice", true, true));
/*     */           } 
/* 276 */           if (Util.getIntValue(str11, 0) > 0 && Util.getDoubleValue(str12, 0.0D) > 0.0D) {
/*     */ 
/*     */ 
/*     */             
/* 280 */             if (str15.equalsIgnoreCase("0")) {
/* 281 */               String str = "";
/* 282 */               if (bool) {
/* 283 */                 recordSet.executeQuery("select id from CptCapital where datatype=? and blongdepartment=? and warehouse=? and id !=?", new Object[] { str16, str18, str14, str11 });
/* 284 */                 if (recordSet.next()) {
/* 285 */                   str = recordSet.getString("id");
/*     */                   
/* 287 */                   recordSet.executeUpdate("update CptCapital set capitalnum=capitalnum+" + str12 + ",startprice=(capitalnum*startprice+" + (new BigDecimal(str12))
/* 288 */                       .setScale(2, 4).multiply((new BigDecimal(str19)).setScale(2, 4)) + ")/(capitalnum+" + str12 + ") where id = " + recordSet
/* 289 */                       .getString("id"), new Object[0]);
/* 290 */                   recordSet.executeUpdate("update CptCapital set capitalnum=capitalnum-" + str12 + " where id=?", new Object[] { str11 });
/* 291 */                 } else if (!str14.equals(str17)) {
/* 292 */                   str = capitalTransMethod.copyNewCpt(str11, str12, str14);
/* 293 */                   if (!str.equalsIgnoreCase("")) {
/* 294 */                     recordSet.executeUpdate("update CptCapital set capitalnum=capitalnum-" + str12 + " where id=?", new Object[] { str11 });
/*     */                   }
/*     */                 } 
/* 297 */                 recordSet.executeUpdate("insert INTO CptUseLog (capitalid, usedate, usedeptid, useresourceid, usecount, useaddress, usestatus, remark, olddeptid, warehouseid) values (?,?,?,?,?,?,?,?,?,?)", new Object[] { str11, str5, Integer.valueOf(Util.getIntValue(str4, 0)), Integer.valueOf(Util.getIntValue(str2, 0)), str12, str13, Integer.valueOf(-5), str8, Integer.valueOf(Util.getIntValue(capitalComInfo.getDepartmentid(str11), 0)), str14 });
/* 298 */                 recordSet.executeUpdate("insert INTO CptUseLog (capitalid, usedate, usedeptid, useresourceid, usecount, useaddress, usestatus, remark, olddeptid, warehouseid) values (?,?,?,?,?,?,?,?,?,?)", new Object[] { str, str5, Integer.valueOf(Util.getIntValue(str4, 0)), Integer.valueOf(Util.getIntValue(str2, 0)), str12, str13, Integer.valueOf(-4), str8, Integer.valueOf(Util.getIntValue(capitalComInfo.getDepartmentid(str11), 0)), str14 });
/*     */               } 
/* 300 */             } else if (str15.equalsIgnoreCase("1")) {
/* 301 */               str10 = str11;
/* 302 */               str10 = str10 + c + str5;
/* 303 */               str10 = str10 + c + str4;
/* 304 */               str10 = str10 + c + str2;
/* 305 */               str10 = str10 + c + str1;
/* 306 */               str10 = str10 + c + "1";
/* 307 */               str10 = str10 + c + str13;
/* 308 */               str10 = str10 + c + "-4";
/* 309 */               str10 = str10 + c + str8;
/* 310 */               str10 = str10 + c + capitalComInfo.getDepartmentid(str11);
/* 311 */               recordSet.executeProc("Capital_Adjust2", str10);
/* 312 */               recordSet.execute("update cptcapital set location='" + str13 + "' where id='" + str11 + "' ");
/* 313 */               recordSet.executeProc("HrmInfoStatus_UpdateCapital", "" + str2);
/*     */               
/* 315 */               if (!str8.equals("")) {
/* 316 */                 recordSet.execute("update CptCapital set remark='" + str8 + "' where id=" + str11);
/*     */               }
/* 318 */               if (!str14.equalsIgnoreCase("")) {
/* 319 */                 recordSet.executeUpdate("update cptcapital set warehouse=? where id=?", new Object[] { str14, str11 });
/* 320 */                 recordSet.executeQuery("select max(id) as id from CptUseLog", new Object[0]);
/* 321 */                 if (recordSet.next()) {
/* 322 */                   String str = Util.null2String(recordSet.getString("id"));
/* 323 */                   recordSet.executeUpdate("update CptUseLog set warehouseid=? where id=?", new Object[] { str14, str });
/*     */                 } 
/*     */               } 
/*     */             } 
/* 327 */             this.CptShare.setCptShareByCpt(str11);
/*     */             
/* 329 */             if (bool) {
/* 330 */               this.CptShare.freshenCptShareByWareHouse(str11);
/*     */             }
/*     */             
/* 333 */             CptRightShareUitl.editCapitalResetRight(str11);
/*     */           } 
/* 335 */         }  capitalComInfo.removeCapitalCache();
/* 336 */       } catch (Exception exception) {
/* 337 */         exception.printStackTrace();
/* 338 */         baseBean.writeLog(exception.getMessage());
/*     */       } 
/*     */       
/* 341 */       Thread thread = new Thread() {
/*     */           public void run() {
/*     */             try {
/* 344 */               sleep(15000L);
/* 345 */             } catch (InterruptedException interruptedException) {
/* 346 */               interruptedException.printStackTrace();
/*     */             } 
/* 348 */             RecordSet recordSet = new RecordSet();
/* 349 */             recordSet.execute("update cptcapital set frozennum=0 where frozennum>0");
/* 350 */             CptWfUtil cptWfUtil = new CptWfUtil();
/* 351 */             cptWfUtil.DoFrozenCpt_new();
/*     */           }
/*     */         };
/* 354 */       thread.start();
/*     */       
/* 356 */       return "1";
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/cpt/wfactions/CptMoveAction.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */