/*     */ package weaver.cpt.wfactions;
/*     */ 
/*     */ import com.engine.cpt.util.CptRightShareUitl;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import org.json.JSONArray;
/*     */ import org.json.JSONObject;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.cpt.capital.CapitalComInfo;
/*     */ import weaver.cpt.capital.CptShare;
/*     */ import weaver.cpt.util.CptDwrUtil;
/*     */ import weaver.cpt.util.CptWfUtil;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ import weaver.interfaces.workflow.action.Action;
/*     */ import weaver.soa.workflow.request.Cell;
/*     */ import weaver.soa.workflow.request.DetailTable;
/*     */ import weaver.soa.workflow.request.Property;
/*     */ import weaver.soa.workflow.request.RequestInfo;
/*     */ import weaver.soa.workflow.request.Row;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CptLossAction
/*     */   implements Action, Runnable
/*     */ {
/*  35 */   private static BaseBean baseBean = new BaseBean();
/*  36 */   private static Object lock = new Object();
/*  37 */   private CptWfUtil cptWfUtil = new CptWfUtil();
/*  38 */   private RequestInfo request = null;
/*  39 */   private JSONObject wfObject = null;
/*  40 */   private User user = null;
/*     */   
/*     */   public String execute(RequestInfo paramRequestInfo) {
/*  43 */     this.request = paramRequestInfo;
/*  44 */     this.user = paramRequestInfo.getRequestManager().getUser();
/*     */     try {
/*  46 */       this.wfObject = this.cptWfUtil.getCptwfInfo(paramRequestInfo.getWorkflowid());
/*  47 */       if (this.wfObject.getInt("zctype") != this.wfObject.getInt("sltype") || "".equals(this.wfObject.getString("zcname")) || "".equals(this.wfObject.getString("slname"))) {
/*  48 */         paramRequestInfo.getRequestManager().setMessageid("20088");
/*  49 */         paramRequestInfo.getRequestManager().setMessagecontent(SystemEnv.getHtmlLabelName(125188, this.user.getLanguage()));
/*  50 */         return "0";
/*     */       } 
/*     */       
/*  53 */       if ("1".equals(this.wfObject.getString("isasync"))) {
/*  54 */         (new Thread(this)).start();
/*     */       } else {
/*  56 */         doAction(paramRequestInfo);
/*     */       } 
/*  58 */     } catch (Exception exception) {
/*  59 */       exception.printStackTrace();
/*  60 */       baseBean.writeLog(exception.getMessage());
/*     */     } 
/*  62 */     return "1";
/*     */   }
/*     */   
/*     */   public void run() {
/*  66 */     doAction(this.request);
/*     */   }
/*     */   
/*     */   private String doAction(RequestInfo paramRequestInfo) {
/*  70 */     synchronized (lock) {
/*  71 */       baseBean.writeLog("tagtag run action :" + getClass() + ",requestid:" + paramRequestInfo.getRequestid());
/*  72 */       CptShare cptShare = new CptShare();
/*  73 */       CapitalComInfo capitalComInfo = null;
/*  74 */       ResourceComInfo resourceComInfo = null;
/*     */       try {
/*  76 */         capitalComInfo = new CapitalComInfo();
/*  77 */         resourceComInfo = new ResourceComInfo();
/*     */         
/*  79 */         RecordSet recordSet = new RecordSet();
/*  80 */         char c = Util.getSeparator();
/*  81 */         String str1 = paramRequestInfo.getRequestid();
/*     */         
/*  83 */         Map map1 = this.cptWfUtil.getcusField(paramRequestInfo.getWorkflowid(), "0");
/*  84 */         Map map2 = new HashMap<>();
/*  85 */         HashMap<Object, Object> hashMap = new HashMap<>();
/*  86 */         JSONArray jSONArray1 = new JSONArray();
/*     */         
/*  88 */         String str2 = "";
/*  89 */         String str3 = "";
/*  90 */         String str4 = "";
/*  91 */         String str5 = "";
/*  92 */         String str6 = "";
/*  93 */         String str7 = "";
/*  94 */         String str8 = "";
/*  95 */         String str9 = "";
/*     */ 
/*     */         
/*  98 */         JSONArray jSONArray2 = new JSONArray();
/*  99 */         Property[] arrayOfProperty = paramRequestInfo.getMainTableInfo().getProperty(); byte b;
/* 100 */         for (b = 0; b < arrayOfProperty.length; b++) {
/* 101 */           String str10 = arrayOfProperty[b].getName();
/* 102 */           String str11 = Util.null2String(arrayOfProperty[b].getValue());
/*     */           
/* 104 */           if (this.wfObject.getInt("sqrtype") == 0 && str10.equalsIgnoreCase(this.wfObject.getString("sqrname"))) {
/* 105 */             str2 = str11;
/* 106 */             str4 = resourceComInfo.getDepartmentID(str2);
/*     */           } 
/* 108 */           if (this.wfObject.getInt("zctype") == 0 && str10.equalsIgnoreCase(this.wfObject.getString("zcname"))) {
/* 109 */             str3 = str11;
/*     */           }
/* 111 */           if (this.wfObject.getInt("sltype") == 0 && str10.equalsIgnoreCase(this.wfObject.getString("slname"))) {
/* 112 */             str6 = str11;
/*     */           }
/* 114 */           if (this.wfObject.getInt("rqtype") == 0 && str10.equalsIgnoreCase(this.wfObject.getString("rqname"))) {
/* 115 */             str5 = str11;
/*     */           }
/* 117 */           if (this.wfObject.getInt("cfddtype") == 0 && str10.equalsIgnoreCase(this.wfObject.getString("cfddname"))) {
/* 118 */             str7 = str11;
/*     */           }
/* 120 */           if (this.wfObject.getInt("bztype") == 0 && str10.equalsIgnoreCase(this.wfObject.getString("bzname"))) {
/* 121 */             str8 = str11;
/*     */           }
/* 123 */           if (this.wfObject.getInt("jgtype") == 0 && str10.equalsIgnoreCase(this.wfObject.getString("jgname"))) {
/* 124 */             str9 = str11;
/*     */           }
/*     */           
/* 127 */           for (String str : map1.keySet()) {
/* 128 */             JSONObject jSONObject = (JSONObject)map1.get(str);
/* 129 */             if (str10.equalsIgnoreCase(Util.null2String(jSONObject.get("fieldname")))) {
/* 130 */               hashMap.put(str, str11);
/*     */             }
/*     */           } 
/*     */         } 
/*     */         
/* 135 */         if (this.wfObject.getInt("zctype") == 0 && this.wfObject.getInt("sltype") == 0) {
/* 136 */           JSONObject jSONObject1 = new JSONObject();
/* 137 */           jSONObject1.put("sqr", str2);
/* 138 */           jSONObject1.put("sqbm", str4);
/* 139 */           jSONObject1.put("zc", str3);
/* 140 */           jSONObject1.put("sl", str6);
/* 141 */           jSONObject1.put("rq", str5);
/* 142 */           jSONObject1.put("cfdd", str7);
/* 143 */           jSONObject1.put("bz", str8);
/* 144 */           jSONObject1.put("jg", str9);
/* 145 */           jSONArray2.put(jSONObject1);
/*     */ 
/*     */           
/* 148 */           JSONObject jSONObject2 = new JSONObject();
/* 149 */           for (String str10 : hashMap.keySet()) {
/* 150 */             String str11 = (String)hashMap.get(str10);
/* 151 */             jSONObject2.put(str10, str11);
/*     */           } 
/* 153 */           jSONArray1.put(jSONObject2);
/* 154 */         } else if (this.wfObject.getInt("zctype") == this.wfObject.getInt("sltype")) {
/* 155 */           DetailTable[] arrayOfDetailTable = paramRequestInfo.getDetailTableInfo().getDetailTable();
/* 156 */           String str = Util.null2String(Integer.valueOf(this.wfObject.getInt("zctype")));
/*     */           
/* 158 */           map2 = this.cptWfUtil.getcusField(paramRequestInfo.getWorkflowid(), str);
/* 159 */           DetailTable detailTable = null;
/* 160 */           for (DetailTable detailTable1 : arrayOfDetailTable) {
/* 161 */             String str10 = detailTable1.getTableDBName();
/* 162 */             if (!"".equals(str10)) {
/* 163 */               String str11 = str10.substring(str10.length() - 1);
/* 164 */               if (str11.equals(str)) {
/* 165 */                 detailTable = detailTable1;
/*     */                 break;
/*     */               } 
/*     */             } 
/*     */           } 
/* 170 */           if (detailTable != null) {
/* 171 */             Row[] arrayOfRow = detailTable.getRow();
/* 172 */             for (byte b1 = 0; b1 < arrayOfRow.length; b1++) {
/* 173 */               Row row = arrayOfRow[b1];
/* 174 */               Cell[] arrayOfCell = row.getCell();
/*     */               
/* 176 */               HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 177 */               for (byte b2 = 0; b2 < arrayOfCell.length; b2++) {
/* 178 */                 Cell cell = arrayOfCell[b2];
/* 179 */                 String str10 = cell.getName().toLowerCase();
/* 180 */                 String str11 = Util.null2String(cell.getValue());
/* 181 */                 if (str10.equalsIgnoreCase(this.wfObject.getString("sqrname")) && this.wfObject.getInt("zctype") == this.wfObject.getInt("sqrtype")) {
/* 182 */                   str2 = str11;
/* 183 */                   str4 = resourceComInfo.getDepartmentID(str2);
/*     */                 } 
/* 185 */                 if (str10.equalsIgnoreCase(this.wfObject.getString("zcname"))) {
/* 186 */                   str3 = str11;
/*     */                 }
/* 188 */                 if (str10.equalsIgnoreCase(this.wfObject.getString("slname"))) {
/* 189 */                   str6 = str11;
/*     */                 }
/* 191 */                 if (str10.equalsIgnoreCase(this.wfObject.getString("rqname")) && this.wfObject.getInt("zctype") == this.wfObject.getInt("rqtype")) {
/* 192 */                   str5 = str11;
/*     */                 }
/* 194 */                 if (str10.equalsIgnoreCase(this.wfObject.getString("cfddname")) && this.wfObject.getInt("zctype") == this.wfObject.getInt("cfddtype")) {
/* 195 */                   str7 = str11;
/*     */                 }
/* 197 */                 if (str10.equalsIgnoreCase(this.wfObject.getString("bzname")) && this.wfObject.getInt("zctype") == this.wfObject.getInt("bztype")) {
/* 198 */                   str8 = str11;
/*     */                 }
/* 200 */                 if (str10.equalsIgnoreCase(this.wfObject.getString("jgname")) && this.wfObject.getInt("zctype") == this.wfObject.getInt("jgtype")) {
/* 201 */                   str9 = str11;
/*     */                 }
/*     */                 
/* 204 */                 for (String str12 : map2.keySet()) {
/* 205 */                   JSONObject jSONObject = (JSONObject)map2.get(str12);
/* 206 */                   if (str10.equalsIgnoreCase(Util.null2String(jSONObject.get("fieldname")))) {
/* 207 */                     hashMap1.put(str12, str11);
/*     */                   }
/*     */                 } 
/*     */               } 
/* 211 */               JSONObject jSONObject1 = new JSONObject();
/* 212 */               jSONObject1.put("sqr", str2);
/* 213 */               jSONObject1.put("sqbm", str4);
/* 214 */               jSONObject1.put("zc", str3);
/* 215 */               jSONObject1.put("sl", str6);
/* 216 */               jSONObject1.put("rq", str5);
/* 217 */               jSONObject1.put("cfdd", str7);
/* 218 */               jSONObject1.put("bz", str8);
/* 219 */               jSONObject1.put("jg", str9);
/* 220 */               jSONArray2.put(jSONObject1);
/*     */ 
/*     */               
/* 223 */               JSONObject jSONObject2 = new JSONObject();
/* 224 */               for (String str10 : hashMap1.keySet()) {
/* 225 */                 String str11 = (String)hashMap1.get(str10);
/* 226 */                 jSONObject2.put(str10, str11);
/*     */               } 
/* 228 */               jSONArray1.put(jSONObject2);
/*     */             } 
/*     */           } 
/*     */         } 
/*     */ 
/*     */         
/* 234 */         for (b = 0; b < jSONArray2.length(); b++) {
/* 235 */           JSONObject jSONObject = jSONArray2.getJSONObject(b);
/* 236 */           String str10 = "";
/* 237 */           String str11 = "";
/* 238 */           String str12 = jSONObject.getString("zc");
/* 239 */           String str13 = jSONObject.getString("sl");
/* 240 */           String str14 = jSONObject.getString("rq");
/* 241 */           str4 = jSONObject.getString("sqbm");
/* 242 */           str2 = jSONObject.getString("sqr");
/* 243 */           String str15 = jSONObject.getString("cfdd");
/* 244 */           str8 = jSONObject.getString("bz");
/* 245 */           str9 = jSONObject.getString("jg");
/*     */           
/* 247 */           if (Util.getIntValue(str12, 0) > 0 && Util.getDoubleValue(str13, 0.0D) > 0.0D)
/*     */           
/*     */           { 
/* 250 */             str11 = Util.null2String((String)(new CptDwrUtil()).getCptInfoMap(str12).get("sptcount"));
/* 251 */             if ("".equals(str11)) {
/* 252 */               str11 = "0";
/*     */             }
/* 254 */             if (str11.equals("1")) {
/* 255 */               str10 = str12;
/* 256 */               str10 = str10 + c + str14;
/* 257 */               str10 = str10 + c + str4;
/* 258 */               str10 = str10 + c + str2;
/* 259 */               str10 = str10 + c + "1";
/* 260 */               str10 = str10 + c + str15;
/* 261 */               str10 = str10 + c + str1;
/* 262 */               str10 = str10 + c + "";
/* 263 */               str10 = str10 + c + "" + Util.getDoubleValue(str9, 0.0D);
/* 264 */               str10 = str10 + c + "-7";
/* 265 */               str10 = str10 + c + str8;
/* 266 */               str10 = str10 + c + "0";
/* 267 */               str10 = str10 + c + str11;
/* 268 */               recordSet.executeProc("CptUseLogLoss_Insert", str10);
/*     */               
/* 270 */               cptShare.setCptShareByCpt(str12);
/*     */             } else {
/* 272 */               str11 = "0";
/* 273 */               str10 = str12;
/* 274 */               str10 = str10 + c + str14;
/* 275 */               str10 = str10 + c + str4;
/* 276 */               str10 = str10 + c + str2;
/* 277 */               str10 = str10 + c + "" + Util.getDoubleValue(str13, 0.0D);
/* 278 */               str10 = str10 + c + str15;
/* 279 */               str10 = str10 + c + str1;
/* 280 */               str10 = str10 + c + "";
/* 281 */               str10 = str10 + c + "" + Util.getDoubleValue(str9, 0.0D);
/* 282 */               str10 = str10 + c + "-7";
/* 283 */               str10 = str10 + c + str8;
/* 284 */               str10 = str10 + c + "0";
/* 285 */               str10 = str10 + c + str11;
/* 286 */               recordSet.executeProc("CptUseLogLoss_Insert", str10);
/* 287 */               recordSet.next();
/* 288 */               String str = recordSet.getString(1);
/*     */               
/* 290 */               if (str.equals("-1")) {
/* 291 */                 paramRequestInfo.getRequestManager().setMessageid("20088");
/* 292 */                 paramRequestInfo.getRequestManager().setMessagecontent(capitalComInfo.getCapitalname(str12) + " " + SystemEnv.getHtmlLabelName(503149, this.user.getLanguage()));
/*     */               } 
/*     */             } 
/* 295 */             if (!str15.equals("")) {
/* 296 */               recordSet.execute("update CptCapital set location='" + str15 + "' where id=" + str12);
/*     */             }
/* 298 */             if (!str8.equals("")) {
/* 299 */               recordSet.execute("update CptCapital set remark='" + str8 + "' where id=" + str12);
/*     */             }
/*     */             
/* 302 */             this.cptWfUtil.updateCptFieldOfWF(map1, hashMap, map2, jSONArray1.getJSONObject(b), str12, this.user.getUID());
/*     */             
/* 304 */             CptRightShareUitl.editCapitalResetRight(str12);
/*     */             
/* 306 */             capitalComInfo.updateCapitalCache(str12); } 
/*     */         } 
/* 308 */       } catch (Exception exception) {
/* 309 */         exception.printStackTrace();
/* 310 */         baseBean.writeLog(exception.getMessage());
/*     */       } 
/*     */       
/* 313 */       Thread thread = new Thread() {
/*     */           public void run() {
/*     */             try {
/* 316 */               sleep(15000L);
/* 317 */             } catch (InterruptedException interruptedException) {
/* 318 */               interruptedException.printStackTrace();
/*     */             } 
/* 320 */             RecordSet recordSet = new RecordSet();
/* 321 */             recordSet.execute("update cptcapital set frozennum=0 where frozennum>0");
/* 322 */             CptWfUtil cptWfUtil = new CptWfUtil();
/* 323 */             cptWfUtil.DoFrozenCpt_new();
/*     */           }
/*     */         };
/* 326 */       thread.start();
/*     */       
/* 328 */       return "1";
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/cpt/wfactions/CptLossAction.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */