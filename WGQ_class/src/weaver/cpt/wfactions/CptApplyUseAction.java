/*     */ package weaver.cpt.wfactions;
/*     */ 
/*     */ import com.api.cpt.util.CptCommonUtil;
/*     */ import com.engine.cpt.util.CapitalTransMethod;
/*     */ import com.engine.cpt.util.CptRightShareUitl;
/*     */ import java.math.BigDecimal;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import org.json.JSONArray;
/*     */ import org.json.JSONObject;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.cpt.ExcelToDB.CapitalExcelToDB;
/*     */ import weaver.cpt.capital.CapitalComInfo;
/*     */ import weaver.cpt.capital.CptShare;
/*     */ import weaver.cpt.maintenance.CapitalAssortmentComInfo;
/*     */ import weaver.cpt.util.CptFieldManager;
/*     */ import weaver.cpt.util.CptWfUtil;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.TimeUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.company.DepartmentComInfo;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ import weaver.interfaces.workflow.action.Action;
/*     */ import weaver.soa.workflow.request.Cell;
/*     */ import weaver.soa.workflow.request.DetailTable;
/*     */ import weaver.soa.workflow.request.Property;
/*     */ import weaver.soa.workflow.request.RequestInfo;
/*     */ import weaver.soa.workflow.request.Row;
/*     */ import weaver.system.code.CodeBuild;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CptApplyUseAction
/*     */   implements Action, Runnable
/*     */ {
/*  41 */   private static BaseBean baseBean = new BaseBean();
/*  42 */   private static Object lock = new Object();
/*  43 */   private CptWfUtil cptWfUtil = new CptWfUtil();
/*  44 */   private RequestInfo request = null;
/*  45 */   private JSONObject wfObject = null;
/*  46 */   private User user = null;
/*  47 */   private CptCommonUtil cptCommonUtil = new CptCommonUtil();
/*     */ 
/*     */   
/*     */   public void run() {
/*  51 */     doAction(this.request);
/*     */   }
/*     */ 
/*     */   
/*     */   public String execute(RequestInfo paramRequestInfo) {
/*  56 */     this.request = paramRequestInfo;
/*  57 */     this.user = paramRequestInfo.getRequestManager().getUser();
/*     */     try {
/*  59 */       this.wfObject = this.cptWfUtil.getCptwfInfo(paramRequestInfo.getWorkflowid());
/*  60 */       if (this.wfObject.getInt("zczltype") != this.wfObject.getInt("sltype") || "".equals(this.wfObject.getString("zczlname")) || "".equals(this.wfObject.getString("slname"))) {
/*  61 */         paramRequestInfo.getRequestManager().setMessageid("20088");
/*  62 */         paramRequestInfo.getRequestManager().setMessagecontent(SystemEnv.getHtmlLabelName(125188, this.user.getLanguage()));
/*  63 */         return "0";
/*     */       } 
/*  65 */       CapitalTransMethod capitalTransMethod = new CapitalTransMethod();
/*  66 */       if (capitalTransMethod.IsWareHouseOpen() && "".equals(this.wfObject.getString("rkckname"))) {
/*  67 */         paramRequestInfo.getRequestManager().setMessageid("20088");
/*  68 */         paramRequestInfo.getRequestManager().setMessagecontent(SystemEnv.getHtmlLabelName(517193, this.user.getLanguage()));
/*  69 */         return "0";
/*     */       } 
/*  71 */       if ("1".equals(this.wfObject.getString("isasync"))) {
/*  72 */         (new Thread(this)).start();
/*     */       } else {
/*  74 */         return doAction(paramRequestInfo);
/*     */       } 
/*  76 */     } catch (Exception exception) {
/*  77 */       exception.printStackTrace();
/*  78 */       baseBean.writeLog(exception.getMessage());
/*     */     } 
/*  80 */     return "1";
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   private String doAction(RequestInfo paramRequestInfo) {
/*  86 */     synchronized (lock) {
/*  87 */       baseBean.writeLog("tagtag run action :" + getClass() + ",requestid:" + paramRequestInfo.getRequestid());
/*  88 */       CapitalAssortmentComInfo capitalAssortmentComInfo = new CapitalAssortmentComInfo();
/*  89 */       CptShare cptShare = new CptShare();
/*  90 */       CapitalComInfo capitalComInfo = null;
/*  91 */       DepartmentComInfo departmentComInfo = null;
/*  92 */       ResourceComInfo resourceComInfo = null;
/*  93 */       CapitalTransMethod capitalTransMethod = new CapitalTransMethod();
/*  94 */       boolean bool = capitalTransMethod.IsWareHouseOpen();
/*     */       try {
/*  96 */         capitalComInfo = new CapitalComInfo();
/*  97 */         departmentComInfo = new DepartmentComInfo();
/*  98 */         resourceComInfo = new ResourceComInfo();
/*  99 */         char c = Util.getSeparator();
/* 100 */         String str1 = paramRequestInfo.getRequestid();
/*     */ 
/*     */         
/* 103 */         Map map1 = this.cptWfUtil.getcusField(paramRequestInfo.getWorkflowid(), "0");
/* 104 */         Map map2 = new HashMap<>();
/* 105 */         HashMap<Object, Object> hashMap = new HashMap<>();
/* 106 */         JSONArray jSONArray1 = new JSONArray();
/*     */         
/* 108 */         String str2 = TimeUtil.getCurrentDateString();
/* 109 */         String str3 = TimeUtil.getOnlyCurrentTimeString();
/* 110 */         String str4 = "";
/* 111 */         String str5 = "";
/* 112 */         String str6 = "";
/* 113 */         String str7 = "";
/* 114 */         String str8 = "";
/* 115 */         String str9 = "";
/* 116 */         String str10 = "";
/* 117 */         String str11 = "";
/* 118 */         String str12 = "";
/* 119 */         String str13 = "";
/* 120 */         String str14 = "";
/* 121 */         String str15 = "";
/* 122 */         String str16 = "";
/* 123 */         String str17 = "";
/* 124 */         String str18 = "";
/* 125 */         String str19 = "";
/* 126 */         String str20 = "";
/* 127 */         boolean bool1 = false;
/*     */         
/* 129 */         JSONArray jSONArray2 = new JSONArray();
/*     */         
/* 131 */         Property[] arrayOfProperty = paramRequestInfo.getMainTableInfo().getProperty();
/* 132 */         for (byte b1 = 0; b1 < arrayOfProperty.length; b1++) {
/* 133 */           String str45 = arrayOfProperty[b1].getName();
/* 134 */           String str46 = Util.null2String(arrayOfProperty[b1].getValue());
/* 135 */           if (this.wfObject.getInt("sqrtype") == 0 && str45.equalsIgnoreCase(this.wfObject.getString("sqrname"))) {
/* 136 */             str4 = str46;
/*     */           }
/* 138 */           if (this.wfObject.getInt("sltype") == 0 && str45.equalsIgnoreCase(this.wfObject.getString("slname"))) {
/* 139 */             str10 = str46;
/*     */           }
/* 141 */           if (this.wfObject.getInt("rqtype") == 0 && str45.equalsIgnoreCase(this.wfObject.getString("rqname"))) {
/* 142 */             str7 = str46;
/*     */           }
/* 144 */           if (this.wfObject.getInt("cfddtype") == 0 && str45.equalsIgnoreCase(this.wfObject.getString("cfddname"))) {
/* 145 */             str11 = str46;
/*     */           }
/* 147 */           if (this.wfObject.getInt("bztype") == 0 && str45.equalsIgnoreCase(this.wfObject.getString("bzname"))) {
/* 148 */             str12 = str46;
/*     */           }
/* 150 */           if (this.wfObject.getInt("zczltype") == 0 && str45.equalsIgnoreCase(this.wfObject.getString("zczlname"))) {
/* 151 */             str6 = str46;
/*     */           }
/* 153 */           if (this.wfObject.getInt("ggxhtype") == 0 && str45.equalsIgnoreCase(this.wfObject.getString("ggxhname"))) {
/* 154 */             str13 = str46;
/*     */           }
/* 156 */           if (this.wfObject.getInt("jgtype") == 0 && str45.equalsIgnoreCase(this.wfObject.getString("jgname"))) {
/* 157 */             str14 = str46;
/*     */           }
/* 159 */           if (this.wfObject.getInt("zcmctype") == 0 && str45.equalsIgnoreCase(this.wfObject.getString("zcmcname"))) {
/* 160 */             str18 = str46;
/*     */           }
/* 162 */           if (this.wfObject.getInt("zcbhtype") == 0 && str45.equalsIgnoreCase(this.wfObject.getString("zcbhname"))) {
/* 163 */             str19 = str46;
/*     */           }
/* 165 */           if (this.wfObject.getInt("ssbmtype") == 0 && str45.equalsIgnoreCase(this.wfObject.getString("ssbmname"))) {
/* 166 */             str5 = str46;
/* 167 */             bool1 = true;
/*     */           } 
/*     */           
/* 170 */           if (this.wfObject.getInt("lysltype") == 0 && str45.equalsIgnoreCase(this.wfObject.getString("lyslname"))) {
/* 171 */             str15 = str46;
/*     */           }
/* 173 */           if (this.wfObject.getInt("lysqrtype") == 0 && str45.equalsIgnoreCase(this.wfObject.getString("lysqrname"))) {
/* 174 */             str16 = str46;
/* 175 */             str17 = resourceComInfo.getDepartmentID(str16);
/*     */           } 
/* 177 */           if (this.wfObject.getInt("rkcktype") == 0 && str45.equalsIgnoreCase(this.wfObject.getString("rkckname"))) {
/* 178 */             if (bool && str46.equalsIgnoreCase("")) {
/* 179 */               paramRequestInfo.getRequestManager().setMessageid("20088");
/* 180 */               paramRequestInfo.getRequestManager().setMessagecontent(SystemEnv.getHtmlLabelName(515444, this.user.getLanguage()));
/* 181 */               return "0";
/*     */             } 
/* 183 */             str20 = str46;
/*     */           } 
/*     */ 
/*     */           
/* 187 */           for (String str : map1.keySet()) {
/* 188 */             JSONObject jSONObject = (JSONObject)map1.get(str);
/* 189 */             if (str45.equalsIgnoreCase(Util.null2String(jSONObject.get("fieldname")))) {
/* 190 */               hashMap.put(str, str46);
/* 191 */               if (str.equals("selectdate")) str8 = str46; 
/* 192 */               if (str.equals("stockindate")) str9 = str46; 
/*     */             } 
/*     */           } 
/*     */         } 
/* 196 */         if (this.wfObject.getInt("zczltype") == 0 && this.wfObject.getInt("sltype") == 0 && this.wfObject.getInt("lysltype") == 0) {
/* 197 */           JSONObject jSONObject1 = new JSONObject();
/* 198 */           if ("".equals(str4)) {
/* 199 */             str4 = "0";
/*     */           }
/* 201 */           jSONObject1.put("sqr", str4);
/* 202 */           if ("".equals(str5)) {
/* 203 */             str5 = resourceComInfo.getDepartmentID(str4);
/*     */           }
/* 205 */           jSONObject1.put("sqbm", str5);
/* 206 */           jSONObject1.put("sl", str10);
/* 207 */           jSONObject1.put("rq", str7);
/* 208 */           jSONObject1.put("cfdd", str11);
/* 209 */           jSONObject1.put("bz", str12);
/* 210 */           jSONObject1.put("zczl", str6);
/* 211 */           jSONObject1.put("ggxh", str13);
/* 212 */           jSONObject1.put("jg", str14);
/* 213 */           jSONObject1.put("lysl", str15);
/* 214 */           jSONObject1.put("lysqr", str16);
/* 215 */           jSONObject1.put("lysqrbm", str17);
/* 216 */           jSONObject1.put("zcmc", str18);
/* 217 */           jSONObject1.put("zcbh", str19);
/* 218 */           jSONObject1.put("rkck", str20);
/* 219 */           jSONArray2.put(jSONObject1);
/*     */ 
/*     */           
/* 222 */           JSONObject jSONObject2 = new JSONObject();
/* 223 */           for (String str45 : hashMap.keySet()) {
/* 224 */             String str46 = (String)hashMap.get(str45);
/* 225 */             jSONObject2.put(str45, str46);
/*     */           } 
/* 227 */           jSONArray1.put(jSONObject2);
/*     */         }
/* 229 */         else if (this.wfObject.getInt("zczltype") == this.wfObject.getInt("sltype") && this.wfObject.getInt("sltype") == this.wfObject.getInt("lysltype")) {
/*     */           
/* 231 */           DetailTable[] arrayOfDetailTable = paramRequestInfo.getDetailTableInfo().getDetailTable();
/* 232 */           String str = Util.null2String(Integer.valueOf(this.wfObject.getInt("zczltype")));
/*     */           
/* 234 */           map2 = this.cptWfUtil.getcusField(paramRequestInfo.getWorkflowid(), str);
/* 235 */           DetailTable detailTable = null;
/* 236 */           for (DetailTable detailTable1 : arrayOfDetailTable) {
/* 237 */             String str45 = detailTable1.getTableDBName();
/* 238 */             if (!"".equals(str45)) {
/* 239 */               String str46 = str45.substring(str45.length() - 1);
/* 240 */               if (str46.equals(str)) {
/* 241 */                 detailTable = detailTable1;
/*     */                 break;
/*     */               } 
/*     */             } 
/*     */           } 
/* 246 */           if (detailTable != null) {
/* 247 */             Row[] arrayOfRow = detailTable.getRow();
/* 248 */             for (byte b = 0; b < arrayOfRow.length; b++) {
/* 249 */               Row row = arrayOfRow[b];
/* 250 */               Cell[] arrayOfCell = row.getCell();
/*     */ 
/*     */               
/* 253 */               HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 254 */               boolean bool3 = false;
/* 255 */               for (byte b3 = 0; b3 < arrayOfCell.length; b3++) {
/* 256 */                 Cell cell = arrayOfCell[b3];
/* 257 */                 String str45 = cell.getName().toLowerCase();
/* 258 */                 String str46 = Util.null2String(cell.getValue());
/* 259 */                 if (str45.equalsIgnoreCase(this.wfObject.getString("sqrname")) && this.wfObject.getInt("zczltype") == this.wfObject.getInt("sqrtype")) {
/* 260 */                   str4 = str46;
/*     */                 }
/* 262 */                 if (str45.equalsIgnoreCase(this.wfObject.getString("slname"))) {
/* 263 */                   str10 = str46;
/*     */                 }
/* 265 */                 if (str45.equalsIgnoreCase(this.wfObject.getString("rqname")) && this.wfObject.getInt("zczltype") == this.wfObject.getInt("rqtype")) {
/* 266 */                   str7 = str46;
/*     */                 }
/* 268 */                 if (str45.equalsIgnoreCase(this.wfObject.getString("cfddname")) && this.wfObject.getInt("zczltype") == this.wfObject.getInt("cfddtype")) {
/* 269 */                   str11 = str46;
/*     */                 }
/* 271 */                 if (str45.equalsIgnoreCase(this.wfObject.getString("bzname")) && this.wfObject.getInt("zczltype") == this.wfObject.getInt("bztype")) {
/* 272 */                   str12 = str46;
/*     */                 }
/* 274 */                 if (str45.equalsIgnoreCase(this.wfObject.getString("zczlname"))) {
/* 275 */                   str6 = str46;
/*     */                 }
/* 277 */                 if (str45.equalsIgnoreCase(this.wfObject.getString("ggxhname")) && this.wfObject.getInt("zczltype") == this.wfObject.getInt("ggxhtype")) {
/* 278 */                   str13 = str46;
/*     */                 }
/* 280 */                 if (str45.equalsIgnoreCase(this.wfObject.getString("jgname")) && this.wfObject.getInt("zczltype") == this.wfObject.getInt("jgtype")) {
/* 281 */                   str14 = str46;
/*     */                 }
/* 283 */                 if (str45.equalsIgnoreCase(this.wfObject.getString("zcmcname"))) {
/* 284 */                   str18 = str46;
/*     */                 }
/* 286 */                 if (str45.equalsIgnoreCase(this.wfObject.getString("zcbhname"))) {
/* 287 */                   str19 = str46;
/*     */                 }
/* 289 */                 if (!bool1 && str45.equalsIgnoreCase(this.wfObject.getString("ssbmname"))) {
/* 290 */                   if (!"".equals(str46)) {
/* 291 */                     str5 = str46;
/* 292 */                     bool3 = true;
/*     */                   } else {
/* 294 */                     str5 = "";
/*     */                   } 
/*     */                 }
/* 297 */                 if (str45.equalsIgnoreCase(this.wfObject.getString("lyslname"))) {
/* 298 */                   str15 = str46;
/*     */                 }
/* 300 */                 if (str45.equalsIgnoreCase(this.wfObject.getString("lysqrname"))) {
/* 301 */                   str16 = str46;
/* 302 */                   str17 = resourceComInfo.getDepartmentID(str16);
/*     */                 } 
/* 304 */                 if (this.wfObject.getInt("rkcktype") != 0 && str45.equalsIgnoreCase(this.wfObject.getString("rkckname"))) {
/* 305 */                   if (bool && str46.equalsIgnoreCase("")) {
/* 306 */                     paramRequestInfo.getRequestManager().setMessageid("20088");
/* 307 */                     paramRequestInfo.getRequestManager().setMessagecontent(SystemEnv.getHtmlLabelName(515444, this.user.getLanguage()));
/* 308 */                     return "0";
/*     */                   } 
/* 310 */                   str20 = str46;
/*     */                 } 
/*     */ 
/*     */                 
/* 314 */                 for (String str47 : map2.keySet()) {
/* 315 */                   JSONObject jSONObject = (JSONObject)map2.get(str47);
/* 316 */                   if (str45.equalsIgnoreCase(Util.null2String(jSONObject.get("fieldname")))) {
/* 317 */                     hashMap1.put(str47, str46);
/*     */                   }
/*     */                 } 
/*     */               } 
/* 321 */               JSONObject jSONObject1 = new JSONObject();
/* 322 */               if ("".equals(str4)) {
/* 323 */                 str4 = "0";
/*     */               }
/* 325 */               jSONObject1.put("sqr", str4);
/*     */               
/* 327 */               if (!bool1 && !bool3) {
/* 328 */                 str5 = "";
/*     */               }
/*     */               
/* 331 */               if ("".equals(str5)) {
/* 332 */                 str5 = resourceComInfo.getDepartmentID(str4);
/*     */               }
/* 334 */               jSONObject1.put("sqbm", str5);
/* 335 */               jSONObject1.put("sl", str10);
/* 336 */               jSONObject1.put("rq", str7);
/* 337 */               jSONObject1.put("cfdd", str11);
/* 338 */               jSONObject1.put("bz", str12);
/* 339 */               jSONObject1.put("zczl", str6);
/* 340 */               jSONObject1.put("ggxh", str13);
/* 341 */               jSONObject1.put("jg", str14);
/* 342 */               jSONObject1.put("lysl", str15);
/* 343 */               jSONObject1.put("lysqr", str16);
/* 344 */               jSONObject1.put("lysqrbm", str17);
/* 345 */               jSONObject1.put("zcmc", str18);
/* 346 */               jSONObject1.put("zcbh", str19);
/* 347 */               jSONObject1.put("rkck", str20);
/* 348 */               jSONArray2.put(jSONObject1);
/*     */ 
/*     */               
/* 351 */               JSONObject jSONObject2 = new JSONObject();
/* 352 */               for (String str45 : hashMap1.keySet()) {
/* 353 */                 String str46 = (String)hashMap1.get(str45);
/* 354 */                 jSONObject2.put(str45, str46);
/*     */               } 
/* 356 */               jSONArray1.put(jSONObject2);
/*     */             } 
/*     */           } 
/*     */         } 
/*     */ 
/*     */         
/* 362 */         RecordSet recordSet1 = new RecordSet();
/* 363 */         RecordSet recordSet2 = new RecordSet();
/* 364 */         RecordSet recordSet3 = new RecordSet();
/* 365 */         CodeBuild codeBuild = new CodeBuild();
/* 366 */         String str21 = "";
/* 367 */         String str22 = "1";
/* 368 */         String str23 = "";
/* 369 */         String str24 = "";
/* 370 */         String str25 = "";
/* 371 */         String str26 = "";
/* 372 */         String str27 = "";
/* 373 */         String str28 = "";
/* 374 */         String str29 = "";
/* 375 */         String str30 = "";
/*     */         
/* 377 */         String str31 = "";
/* 378 */         String str32 = "";
/* 379 */         String str33 = "";
/* 380 */         String str34 = "";
/* 381 */         String str35 = "";
/* 382 */         String str36 = "";
/* 383 */         String str37 = "";
/* 384 */         String str38 = "";
/* 385 */         String str39 = "";
/* 386 */         String str40 = "";
/* 387 */         String str41 = "";
/* 388 */         String str42 = "";
/* 389 */         String str43 = "";
/* 390 */         String str44 = "";
/* 391 */         boolean bool2 = false;
/*     */         
/* 393 */         recordSet1.executeQuery("select * from cptcode where isuse=2", new Object[0]);
/* 394 */         if (recordSet1.next()) {
/* 395 */           bool2 = true;
/*     */         }
/*     */ 
/*     */         
/* 399 */         if (bool2) {
/* 400 */           String str = "";
/* 401 */           for (byte b = 0; b < jSONArray2.length(); b++) {
/* 402 */             JSONObject jSONObject = (JSONObject)jSONArray2.get(b);
/* 403 */             str43 = jSONObject.getString("zcbh");
/* 404 */             str5 = jSONObject.getString("sqbm");
/* 405 */             str23 = jSONObject.getString("zczl");
/* 406 */             str31 = jSONObject.getString("sl");
/* 407 */             recordSet1.executeProc("CptCapital_SelectByID", str23);
/* 408 */             if (recordSet1.next()) {
/* 409 */               str33 = recordSet1.getString("sptcount");
/*     */             }
/* 411 */             boolean bool3 = CapitalExcelToDB.checkmarkstr(str43, str5, str23, str33, str31);
/* 412 */             if (bool3) {
/* 413 */               str = str + str43 + ",";
/*     */             }
/*     */           } 
/* 416 */           if (!str.equals("")) {
/* 417 */             str = str.substring(0, str.length() - 1);
/* 418 */             paramRequestInfo.getRequestManager().setMessageid("20088");
/* 419 */             paramRequestInfo.getRequestManager().setMessagecontent(SystemEnv.getHtmlLabelName(502700, this.user.getLanguage()) + str);
/* 420 */             return "0";
/*     */           } 
/*     */         } 
/*     */         
/* 424 */         for (byte b2 = 0; b2 < jSONArray2.length(); b2++) {
/* 425 */           JSONObject jSONObject = (JSONObject)jSONArray2.get(b2);
/* 426 */           String str45 = jSONObject.getString("zczl");
/* 427 */           String str46 = jSONObject.getString("jg");
/* 428 */           String str47 = jSONObject.getString("sl");
/* 429 */           String str48 = jSONObject.getString("lysl");
/* 430 */           String str49 = jSONObject.getString("lysqr");
/* 431 */           String str50 = jSONObject.getString("lysqrbm");
/* 432 */           String str51 = Util.null2String(jSONObject.getString("rq"));
/* 433 */           if ("".equals(str51)) {
/* 434 */             str51 = TimeUtil.getCurrentDateString();
/*     */           }
/*     */           
/* 437 */           str5 = jSONObject.getString("sqbm");
/* 438 */           str4 = jSONObject.getString("sqr");
/* 439 */           String str52 = jSONObject.getString("cfdd");
/* 440 */           String str53 = jSONObject.getString("ggxh");
/* 441 */           str12 = jSONObject.getString("bz");
/* 442 */           str42 = jSONObject.getString("zcmc");
/* 443 */           str43 = jSONObject.getString("zcbh");
/* 444 */           str44 = jSONObject.getString("rkck");
/*     */           
/* 446 */           if (Util.getIntValue(str45, 0) > 0 && Util.getDoubleValue(str47, 0.0D) > 0.0D) {
/*     */ 
/*     */ 
/*     */             
/* 450 */             if (Util.getDoubleValue(str46, 0.0D) > Util.getDoubleValue("999999999999999")) {
/* 451 */               paramRequestInfo.getRequestManager().setMessageid("20088");
/* 452 */               paramRequestInfo.getRequestManager().setMessagecontent(SystemEnv.getHtmlLabelName(520529, this.user.getLanguage()));
/* 453 */               return "0";
/*     */             } 
/* 455 */             if (Util.getDoubleValue(str47, 0.0D) > Util.getDoubleValue("999999999999999")) {
/* 456 */               paramRequestInfo.getRequestManager().setMessageid("20088");
/* 457 */               paramRequestInfo.getRequestManager().setMessagecontent(SystemEnv.getHtmlLabelName(520528, this.user.getLanguage()));
/* 458 */               return "0";
/*     */             } 
/* 460 */             if (Util.getDoubleValue(str48, 0.0D) > Util.getDoubleValue("999999999999999")) {
/* 461 */               paramRequestInfo.getRequestManager().setMessageid("20088");
/* 462 */               paramRequestInfo.getRequestManager().setMessagecontent(SystemEnv.getHtmlLabelName(520528, this.user.getLanguage()));
/* 463 */               return "0";
/*     */             } 
/*     */ 
/*     */             
/* 467 */             String str54 = "";
/* 468 */             String str55 = "";
/* 469 */             JSONObject jSONObject1 = (JSONObject)jSONArray1.get(b2);
/* 470 */             if (jSONObject1.has("selectdate")) {
/* 471 */               str54 = jSONObject1.getString("selectdate");
/*     */             } else {
/* 473 */               str54 = str8;
/*     */             } 
/* 475 */             if (jSONObject1.has("stockindate")) {
/* 476 */               str55 = jSONObject1.getString("stockindate");
/*     */             } else {
/* 478 */               str55 = str9;
/*     */             } 
/*     */             
/* 481 */             str23 = str45;
/* 482 */             str31 = str47;
/* 483 */             BigDecimal bigDecimal = new BigDecimal("" + Util.getDoubleValue(str46, 0.0D));
/* 484 */             bigDecimal = bigDecimal.setScale(2, 4);
/*     */             
/* 486 */             recordSet1.executeProc("CptCapital_SelectByID", str23);
/* 487 */             if (recordSet1.next()) {
/* 488 */               str24 = recordSet1.getString("mark");
/* 489 */               str33 = recordSet1.getString("sptcount");
/* 490 */               str36 = recordSet1.getString("capitalgroupid");
/* 491 */               str38 = recordSet1.getString("capitaltypeid");
/* 492 */               if (str53.equals("")) {
/* 493 */                 str53 = recordSet1.getString("capitalspec");
/*     */               }
/* 495 */               if (str46.equals("")) {
/* 496 */                 bigDecimal = new BigDecimal(Util.getDoubleValue(recordSet1.getString("CptCapital", "startprice", true, true), 0.0D));
/* 497 */                 bigDecimal = bigDecimal.setScale(2, 4);
/*     */               } 
/* 499 */               str41 = recordSet1.getString("customerid");
/*     */ 
/*     */ 
/*     */ 
/*     */               
/* 504 */               str35 = bigDecimal.multiply(new BigDecimal(str31)).toString();
/*     */               
/* 506 */               String str = str36;
/* 507 */               byte b = 10;
/* 508 */               while (b > 0 && 
/* 509 */                 !capitalAssortmentComInfo.getSupAssortmentId(str).equals("0")) {
/*     */ 
/*     */                 
/* 512 */                 str = capitalAssortmentComInfo.getSupAssortmentId(str);
/* 513 */                 b--;
/*     */               } 
/*     */               
/* 516 */               if (bigDecimal.compareTo(new BigDecimal("2000")) == 1) {
/* 517 */                 str37 = "1";
/*     */               } else {
/* 519 */                 str37 = "2";
/*     */               } 
/* 521 */               str39 = departmentComInfo.getSubcompanyid1(str5);
/* 522 */               str40 = str5;
/*     */ 
/*     */               
/* 525 */               if (bool && !str44.equalsIgnoreCase("")) {
/* 526 */                 recordSet1.executeQuery("select * from CptCapital where datatype=? and blongdepartment=? and warehouse=?", new Object[] { str23, str5, str44 });
/*     */               } else {
/* 528 */                 recordSet1.executeProc("CptCapital_SelectByDataType", str23 + c + str5);
/*     */               } 
/* 530 */               if (!str33.equals("1") && recordSet1.next()) {
/* 531 */                 str24 = recordSet1.getString("mark");
/* 532 */               } else if (!str33.equals("1")) {
/* 533 */                 String str56 = "";
/* 534 */                 str56 = codeBuild.getCurrentCapitalCodeIsOver(departmentComInfo.getSubcompanyid1(str5), str5, str36, str38, str54, str55, str23, 1);
/*     */                 
/* 536 */                 if ("yes".equals(str56)) {
/* 537 */                   RecordSet recordSet = new RecordSet();
/* 538 */                   recordSet.execute("select name from cptcapital where isdata = 1 and id = " + str23);
/* 539 */                   recordSet.next();
/* 540 */                   String str57 = Util.null2String(recordSet.getString(1));
/* 541 */                   paramRequestInfo.getRequestManager().setMessageid("20088");
/* 542 */                   paramRequestInfo.getRequestManager().setMessagecontent(str57 + " " + SystemEnv.getHtmlLabelName(386045, this.user.getLanguage()));
/* 543 */                   return "0";
/*     */                 } 
/* 545 */                 if (bool2 && !"".equals(str43)) {
/* 546 */                   str24 = str43;
/*     */                 } else {
/* 548 */                   str24 = codeBuild.getCurrentCapitalCode(departmentComInfo.getSubcompanyid1(str5), str5, str36, str38, str54, str55, str23);
/*     */                 } 
/*     */               } 
/*     */ 
/*     */ 
/*     */               
/* 554 */               str32 = str51;
/* 555 */               str32 = str32 + c + str5;
/* 556 */               str32 = str32 + c + str4;
/* 557 */               str32 = str32 + c + str4;
/* 558 */               str32 = str32 + c + str31;
/* 559 */               str32 = str32 + c + str52;
/* 560 */               str32 = str32 + c + str1;
/* 561 */               str32 = str32 + c + "";
/* 562 */               str32 = str32 + c + str35;
/* 563 */               str32 = str32 + c + str22;
/* 564 */               str32 = str32 + c + str12;
/* 565 */               str32 = str32 + c + str24;
/* 566 */               str32 = str32 + c + str23;
/* 567 */               str32 = str32 + c + str26;
/* 568 */               str32 = str32 + c + str27;
/* 569 */               str32 = str32 + c + str28;
/* 570 */               str32 = str32 + c + str29;
/* 571 */               str32 = str32 + c + str30;
/* 572 */               str32 = str32 + c + str4;
/* 573 */               str32 = str32 + c + str2;
/* 574 */               str32 = str32 + c + str3;
/*     */               
/* 576 */               if (str33.equals("1")) {
/*     */ 
/*     */                 
/* 579 */                 String str56 = "";
/*     */                 
/* 581 */                 int i = (int)Util.getDoubleValue(str47, 0.0D);
/* 582 */                 str56 = codeBuild.getCurrentCapitalCodeIsOver(departmentComInfo.getSubcompanyid1(str5), str5, str36, str38, str54, str55, str23, i);
/* 583 */                 if ("yes".equals(str56)) {
/* 584 */                   RecordSet recordSet = new RecordSet();
/* 585 */                   recordSet.execute("select name from cptcapital where isdata = 1 and id = " + str23);
/* 586 */                   recordSet.next();
/* 587 */                   String str57 = Util.null2String(recordSet.getString(1));
/* 588 */                   paramRequestInfo.getRequestManager().setMessageid("20088");
/* 589 */                   paramRequestInfo.getRequestManager().setMessagecontent(str57 + " " + 
/* 590 */                       SystemEnv.getHtmlLabelName(386045, this.user.getLanguage()));
/* 591 */                   return "0";
/*     */                 } 
/*     */                 
/* 594 */                 for (byte b3 = 0; b3 < (int)Util.getDoubleValue(str47, 0.0D); b3++) {
/*     */                   
/* 596 */                   if (bool2 && !"".equals(str43)) {
/* 597 */                     if ((int)Util.getDoubleValue(str47, 0.0D) > 1) {
/* 598 */                       str24 = str43 + "_" + (b3 + 1);
/*     */                     } else {
/* 600 */                       str24 = str43;
/*     */                     } 
/*     */                   } else {
/* 603 */                     str24 = codeBuild.getCurrentCapitalCode(departmentComInfo.getSubcompanyid1(str5), str5, str36, str38, str54, str55, str23);
/*     */                   } 
/* 605 */                   str32 = str51;
/* 606 */                   str32 = str32 + c + str5;
/* 607 */                   str32 = str32 + c + str4;
/* 608 */                   str32 = str32 + c + str4;
/* 609 */                   str32 = str32 + c + "1";
/* 610 */                   str32 = str32 + c + str52;
/* 611 */                   str32 = str32 + c + str1;
/* 612 */                   str32 = str32 + c + "";
/* 613 */                   str32 = str32 + c + "" + bigDecimal;
/* 614 */                   str32 = str32 + c + str22;
/* 615 */                   str32 = str32 + c + str12;
/* 616 */                   str32 = str32 + c + str24;
/* 617 */                   str32 = str32 + c + str23;
/* 618 */                   str32 = str32 + c + str26;
/* 619 */                   str32 = str32 + c + str27;
/* 620 */                   str32 = str32 + c + str28;
/* 621 */                   str32 = str32 + c + str29;
/* 622 */                   str32 = str32 + c + str30;
/* 623 */                   str32 = str32 + c + str4;
/* 624 */                   str32 = str32 + c + str2;
/* 625 */                   str32 = str32 + c + str3;
/*     */                   
/* 627 */                   str21 = str23;
/* 628 */                   str21 = str21 + c + str41;
/* 629 */                   str21 = str21 + c + "" + bigDecimal;
/* 630 */                   str21 = str21 + c + str53;
/* 631 */                   str21 = str21 + c + str52;
/* 632 */                   str21 = str21 + c + "";
/* 633 */                   str21 = str21 + c + "";
/* 634 */                   str21 = str21 + c + "";
/* 635 */                   recordSet1.executeProc("CptCapital_Duplicate", str21);
/* 636 */                   recordSet1.next();
/* 637 */                   str34 = recordSet1.getString(1);
/*     */ 
/*     */                   
/* 640 */                   capitalTransMethod.saveWareHouse(Util.getIntValue(str34), str44);
/*     */                   
/* 642 */                   str32 = str34 + c + str32;
/* 643 */                   str32 = str32 + c + "" + bigDecimal;
/* 644 */                   str32 = str32 + c + str41;
/* 645 */                   str32 = str32 + c + str37;
/* 646 */                   str32 = str32 + c + str25;
/*     */                   
/* 648 */                   recordSet1.executeProc("CptUseLogInStock_Insert", str32);
/*     */ 
/*     */                   
/* 651 */                   CptFieldManager cptFieldManager = new CptFieldManager();
/* 652 */                   cptFieldManager.updateCptDefinedField(str23, str34);
/*     */ 
/*     */                   
/* 655 */                   recordSet3.execute("select max(id) as id from CptUseLog where capitalid = " + str34);
/* 656 */                   if (recordSet3.next()) {
/* 657 */                     String str60 = recordSet3.getString("id");
/* 658 */                     recordSet3.execute("update CptUseLog set useresourceid = " + str4 + ",usedeptid=" + str5 + " where id = " + str60);
/*     */                   } 
/*     */                   
/* 661 */                   recordSet1.execute("update cptcapital set createrid='" + str4 + "',departmentid=null,relatewfid='" + str1 + "',olddepartment = " + str5 + ",blongsubcompany=" + (
/* 662 */                       "".equals(str39) ? null : str39) + ", blongdepartment=" + str40 + ",contractno='' where id = " + str34);
/*     */ 
/*     */ 
/*     */                   
/* 666 */                   if (!"".equals(str42)) {
/* 667 */                     recordSet1.executeUpdate("update cptcapital set name=? where id=?", new Object[] { str42, str34 });
/*     */                   }
/* 669 */                   if (!"".equals(str12)) {
/* 670 */                     recordSet1.execute("update cptcapital set remark='" + str12 + "' where id = " + str34);
/*     */                   }
/* 672 */                   String str57 = "select * from cptcapitalparts where cptid = " + str23;
/* 673 */                   recordSet2.execute(str57);
/* 674 */                   while (recordSet2.next()) {
/*     */ 
/*     */ 
/*     */                     
/* 678 */                     str57 = "insert into cptcapitalparts (cptid,partsname,partsspec,partssum,partsweight,partssize) select " + str34 + ",partsname,partsspec,partssum,partsweight,partssize from cptcapitalparts where id = " + recordSet2.getString("id");
/* 679 */                     recordSet3.execute(str57);
/*     */                   } 
/* 681 */                   str57 = "select * from cptcapitalequipment where cptid = " + str23;
/* 682 */                   recordSet2.execute(str57);
/* 683 */                   while (recordSet2.next()) {
/*     */ 
/*     */ 
/*     */                     
/* 687 */                     str57 = "insert into cptcapitalequipment (cptid,equipmentname,equipmentspec,equipmentsum,equipmentpower,equipmentvoltage) select " + str34 + ",equipmentname,equipmentspec,equipmentsum,equipmentpower,equipmentvoltage from cptcapitalequipment where id = " + recordSet2.getString("id");
/* 688 */                     recordSet3.execute(str57);
/*     */                   } 
/*     */ 
/*     */ 
/*     */                   
/* 693 */                   this.cptWfUtil.updateCptFieldOfWF(map1, hashMap, map2, (JSONObject)jSONArray1.get(b2), str34, this.user.getUID(), Boolean.valueOf(false));
/*     */                   
/* 695 */                   cptShare.setCptShareByCpt(str34);
/*     */                   
/* 697 */                   if (bool) {
/* 698 */                     cptShare.freshenCptShareByWareHouse(str34);
/*     */                   }
/*     */ 
/*     */                   
/* 702 */                   recordSet3.execute("select name from cptcapital where id=" + str34);
/* 703 */                   recordSet3.next();
/* 704 */                   String str58 = recordSet3.getString("name");
/* 705 */                   String str59 = Util.null2String(this.cptCommonUtil.getPinYin(str58, 7).get("pinyin"));
/* 706 */                   recordSet3.execute("update cptcapital set ecology_pinyin_search='" + str59 + "' where id=" + str34);
/*     */ 
/*     */                   
/* 709 */                   if (Util.getIntValue(str49, 0) > 0 && Util.getDoubleValue(str48, 0.0D) > 0.0D && Util.getDoubleValue(str48, 0.0D) > b3) {
/* 710 */                     RecordSet recordSet4 = new RecordSet();
/* 711 */                     RecordSet recordSet5 = new RecordSet();
/* 712 */                     char c1 = Util.getSeparator();
/* 713 */                     recordSet4.execute(" select * from CptCapital where id=" + str34);
/* 714 */                     String str60 = "";
/* 715 */                     if (recordSet4.next()) {
/* 716 */                       str33 = recordSet4.getString("sptcount");
/* 717 */                       str60 = recordSet4.getString("location");
/*     */                     } 
/* 719 */                     if ("".equals(str52)) {
/* 720 */                       str52 = str60;
/*     */                     }
/* 722 */                     str32 = str34;
/* 723 */                     str32 = str32 + c1 + str51;
/* 724 */                     str32 = str32 + c1 + str50;
/* 725 */                     str32 = str32 + c1 + str49;
/* 726 */                     str32 = str32 + c1 + "1";
/* 727 */                     str32 = str32 + c1 + str1;
/* 728 */                     str32 = str32 + c1 + "";
/* 729 */                     str32 = str32 + c1 + "0";
/* 730 */                     str32 = str32 + c1 + "2";
/* 731 */                     str32 = str32 + c1 + str12;
/* 732 */                     str32 = str32 + c1 + str52;
/* 733 */                     str32 = str32 + c1 + str33;
/* 734 */                     recordSet4.executeProc("CptUseLogUse_Insert2", str32);
/*     */ 
/*     */                     
/* 737 */                     String str61 = "";
/* 738 */                     boolean bool3 = "oracle".equalsIgnoreCase(recordSet4.getDBType());
/* 739 */                     if (!bool3) {
/* 740 */                       if (!str52.equals("")) {
/* 741 */                         str61 = "update CptCapital set deprestartdate='" + str51 + "',location='" + str52 + "' where id=" + str34 + " and (deprestartdate is null or deprestartdate='')";
/*     */                       } else {
/* 743 */                         str61 = "update CptCapital set deprestartdate='" + str51 + "' where id=" + str34 + " and (deprestartdate is null or deprestartdate='')";
/*     */                       }
/*     */                     
/* 746 */                     } else if (!str52.equals("")) {
/* 747 */                       str61 = "update CptCapital set deprestartdate='" + str51 + "',location='" + str52 + "' where id=" + str34 + " and deprestartdate is null";
/*     */                     } else {
/* 749 */                       str61 = "update CptCapital set deprestartdate='" + str51 + "' where id=" + str34 + " and deprestartdate is null";
/*     */                     } 
/*     */                     
/* 752 */                     recordSet5.execute(str61);
/* 753 */                     if (!str12.equals("")) {
/* 754 */                       recordSet5.execute("update CptCapital set remark='" + str12 + "' where id=" + str34);
/*     */                     }
/* 756 */                     recordSet5.executeProc("HrmInfoStatus_UpdateCapital", "" + str49);
/*     */ 
/*     */                     
/* 759 */                     this.cptWfUtil.updateCptFieldOfWF(map1, hashMap, map2, jSONArray1.getJSONObject(b2), str34, this.user.getUID(), Boolean.valueOf(false));
/*     */                     
/* 761 */                     cptShare.setCptShareByCpt(str34);
/*     */                   } 
/*     */                   
/* 764 */                   CptRightShareUitl.addCapitalRight(str, str34);
/*     */ 
/*     */                   
/* 767 */                   capitalComInfo.updateCapitalCache(str34);
/*     */                 } 
/*     */               } else {
/*     */                 
/* 771 */                 boolean bool3 = false;
/*     */                 
/* 773 */                 if (bool && !str44.equalsIgnoreCase("")) {
/* 774 */                   recordSet1.executeQuery("select * from CptCapital where datatype=? and blongdepartment=? and warehouse=?", new Object[] { str23, str5, str44 });
/*     */                 } else {
/* 776 */                   recordSet1.executeProc("CptCapital_SelectByDataType", str23 + c + str5);
/*     */                 } 
/* 778 */                 if (recordSet1.next()) {
/* 779 */                   bool3 = true;
/*     */ 
/*     */                   
/* 782 */                   str34 = recordSet1.getString("id");
/* 783 */                   BigDecimal bigDecimal1 = new BigDecimal(recordSet1.getString("CptCapital", "startprice", true, true));
/* 784 */                   BigDecimal bigDecimal2 = new BigDecimal(recordSet1.getString("capitalnum"));
/* 785 */                   bigDecimal = bigDecimal.multiply(new BigDecimal(str31));
/* 786 */                   bigDecimal = bigDecimal.add(bigDecimal1.multiply(bigDecimal2));
/* 787 */                   bigDecimal = bigDecimal.divide(bigDecimal2.add(new BigDecimal(str31)), 2, 0);
/*     */ 
/*     */                   
/* 790 */                   str32 = str34 + c + str32;
/* 791 */                   str32 = str32 + c + "" + bigDecimal;
/* 792 */                   str32 = str32 + c + "";
/* 793 */                   str32 = str32 + c + str37;
/* 794 */                   str32 = str32 + c + str25;
/*     */ 
/*     */                   
/* 797 */                   recordSet1.executeUpdate("INSERT INTO CptUseLog(capitalid,usedate,usedeptid,useresourceid,usecount,useaddress,usestatus,fee,userequest,remark) values(?,?,?,?,?,?,?,?,?,?)", new Object[] {
/* 798 */                         Integer.valueOf(Util.getIntValue(str34)), str51, Integer.valueOf(Util.getIntValue(str5, 0)), Integer.valueOf(Util.getIntValue(str4, 0)), Float.valueOf(Util.getFloatValue(str31, 0.0F)), str52, str22, Float.valueOf(Util.getFloatValue(str35)), str1, str12
/*     */                       });
/*     */                   
/* 801 */                   BigDecimal bigDecimal3 = bigDecimal2.add(new BigDecimal(str31));
/* 802 */                   recordSet1.execute("update cptcapital set  createrid='" + str4 + "',departmentid=null,startprice = '" + bigDecimal + "',capitalnum='" + bigDecimal3 + "' where id='" + str34 + "'");
/* 803 */                   cptShare.setCptShareByCpt(str34);
/*     */ 
/*     */                   
/* 806 */                   capitalComInfo.updateCapitalCache(str34);
/*     */                 }
/*     */                 else {
/*     */                   
/* 810 */                   str21 = str23;
/* 811 */                   str21 = str21 + c + str41;
/* 812 */                   str21 = str21 + c + "" + bigDecimal;
/* 813 */                   str21 = str21 + c + str53;
/* 814 */                   str21 = str21 + c + str52;
/* 815 */                   str21 = str21 + c + "";
/* 816 */                   str21 = str21 + c + "";
/* 817 */                   str21 = str21 + c + "";
/*     */                   
/* 819 */                   recordSet1.executeProc("CptCapital_Duplicate", str21);
/* 820 */                   recordSet1.next();
/* 821 */                   str34 = recordSet1.getString(1);
/*     */ 
/*     */                   
/* 824 */                   capitalTransMethod.saveWareHouse(Util.getIntValue(str34), str44);
/*     */                   
/* 826 */                   str32 = str34 + c + str32;
/* 827 */                   str32 = str32 + c + "" + bigDecimal;
/* 828 */                   str32 = str32 + c + str41;
/* 829 */                   str32 = str32 + c + str37;
/* 830 */                   str32 = str32 + c + str25;
/*     */ 
/*     */                   
/* 833 */                   recordSet1.executeProc("CptUseLogInStock_Insert", str32);
/*     */ 
/*     */                   
/* 836 */                   CptFieldManager cptFieldManager = new CptFieldManager();
/* 837 */                   cptFieldManager.updateCptDefinedField(str23, str34);
/*     */                   
/* 839 */                   recordSet1.execute("update cptcapital set createrid='" + str4 + "',departmentid=null,relatewfid='" + str1 + "',olddepartment = " + str5 + ",blongsubcompany=" + (
/* 840 */                       "".equals(str39) ? null : str39) + ", blongdepartment=" + str40 + ",contractno='',remark='" + str12 + "' where id = " + str34);
/*     */ 
/*     */ 
/*     */ 
/*     */                   
/* 845 */                   if (!"".equals(str42)) {
/* 846 */                     recordSet1.executeUpdate("update cptcapital set name=? where id=?", new Object[] { str42, str34 });
/*     */                   }
/*     */                   
/* 849 */                   String str56 = "select * from cptcapitalparts where cptid = " + str23;
/* 850 */                   recordSet2.execute(str56);
/* 851 */                   while (recordSet2.next()) {
/*     */ 
/*     */ 
/*     */                     
/* 855 */                     str56 = "insert into cptcapitalparts (cptid,partsname,partsspec,partssum,partsweight,partssize) select " + str34 + ",partsname,partsspec,partssum,partsweight,partssize from cptcapitalparts where id = " + recordSet2.getString("id");
/* 856 */                     recordSet3.execute(str56);
/*     */                   } 
/* 858 */                   str56 = "select * from cptcapitalequipment where cptid = " + str23;
/* 859 */                   recordSet2.execute(str56);
/* 860 */                   while (recordSet2.next()) {
/*     */ 
/*     */ 
/*     */                     
/* 864 */                     str56 = "insert into cptcapitalequipment (cptid,equipmentname,equipmentspec,equipmentsum,equipmentpower,equipmentvoltage) select " + str34 + ",equipmentname,equipmentspec,equipmentsum,equipmentpower,equipmentvoltage from cptcapitalequipment where id = " + recordSet2.getString("id");
/* 865 */                     recordSet3.execute(str56);
/*     */                   } 
/*     */ 
/*     */ 
/*     */                   
/* 870 */                   this.cptWfUtil.updateCptFieldOfWF(map1, hashMap, map2, (JSONObject)jSONArray1.get(b2), str34, this.user.getUID(), Boolean.valueOf(false));
/* 871 */                   cptShare.setCptShareByCpt(str34);
/*     */                   
/* 873 */                   if (bool) {
/* 874 */                     cptShare.freshenCptShareByWareHouse(str34);
/*     */                   }
/*     */ 
/*     */                   
/* 878 */                   recordSet3.execute("select name from cptcapital where id=" + str34);
/* 879 */                   recordSet3.next();
/* 880 */                   String str57 = recordSet3.getString("name");
/* 881 */                   String str58 = Util.null2String(this.cptCommonUtil.getPinYin(str57, 7).get("pinyin"));
/* 882 */                   recordSet3.execute("update cptcapital set ecology_pinyin_search='" + str58 + "' where id=" + str34);
/*     */                 } 
/* 884 */                 if (!bool3 || Util.getDoubleValue(str48, 0.0D) > 0.0D)
/*     */                 {
/* 886 */                   this.cptWfUtil.updateCptFieldOfWF(map1, hashMap, map2, jSONArray1.getJSONObject(b2), str34, this.user.getUID(), Boolean.valueOf(false));
/*     */                 }
/*     */ 
/*     */                 
/* 890 */                 CptRightShareUitl.addCapitalRight(str, str34);
/*     */                 
/* 892 */                 if (Util.getDoubleValue(str48, 0.0D) > 0.0D && Util.getIntValue(str49, 0) > 0) {
/*     */ 
/*     */ 
/*     */                   
/* 896 */                   RecordSet recordSet4 = new RecordSet();
/* 897 */                   RecordSet recordSet5 = new RecordSet();
/* 898 */                   char c1 = Util.getSeparator();
/* 899 */                   recordSet4.execute(" select sptcount,location,name from CptCapital where id=" + str34);
/* 900 */                   String str56 = "";
/* 901 */                   String str57 = "";
/* 902 */                   if (recordSet4.next()) {
/* 903 */                     str33 = recordSet4.getString("sptcount");
/* 904 */                     str56 = recordSet4.getString("location");
/* 905 */                     str57 = recordSet4.getString("name");
/*     */                   } 
/* 907 */                   if ("".equals(str52)) {
/* 908 */                     str52 = str56;
/*     */                   }
/* 910 */                   str32 = str34;
/* 911 */                   str32 = str32 + c1 + str51;
/* 912 */                   str32 = str32 + c1 + str50;
/* 913 */                   str32 = str32 + c1 + str49;
/* 914 */                   str32 = str32 + c1 + str48;
/* 915 */                   str32 = str32 + c1 + str1;
/* 916 */                   str32 = str32 + c1 + "";
/* 917 */                   str32 = str32 + c1 + "0";
/* 918 */                   str32 = str32 + c1 + "2";
/* 919 */                   str32 = str32 + c1 + str12;
/* 920 */                   str32 = str32 + c1 + str52;
/* 921 */                   str32 = str32 + c1 + "0";
/* 922 */                   recordSet4.executeProc("CptUseLogUse_Insert2", str32);
/* 923 */                   recordSet4.next();
/* 924 */                   String str58 = recordSet4.getString(1);
/*     */                   
/* 926 */                   if (str58.equals("-1")) {
/* 927 */                     paramRequestInfo.getRequestManager().setMessageid("20088");
/* 928 */                     paramRequestInfo.getRequestManager().setMessagecontent(str57 + SystemEnv.getHtmlLabelName(503127, this.user.getLanguage()));
/* 929 */                     return "0";
/*     */                   } 
/*     */                   
/* 932 */                   String str59 = "";
/* 933 */                   boolean bool4 = "oracle".equalsIgnoreCase(recordSet4.getDBType());
/* 934 */                   if (!bool4) {
/* 935 */                     if (!str52.equals("")) {
/* 936 */                       str59 = "update CptCapital set deprestartdate='" + str51 + "',location='" + str52 + "' where id=" + str34 + " and (deprestartdate is null or deprestartdate='')";
/*     */                     } else {
/* 938 */                       str59 = "update CptCapital set deprestartdate='" + str51 + "' where id=" + str34 + " and (deprestartdate is null or deprestartdate='')";
/*     */                     }
/*     */                   
/* 941 */                   } else if (!str52.equals("")) {
/* 942 */                     str59 = "update CptCapital set deprestartdate='" + str51 + "',location='" + str52 + "' where id=" + str34 + " and deprestartdate is null";
/*     */                   } else {
/* 944 */                     str59 = "update CptCapital set deprestartdate='" + str51 + "' where id=" + str34 + " and deprestartdate is null";
/*     */                   } 
/*     */                   
/* 947 */                   recordSet5.execute(str59);
/* 948 */                   if (!str12.equals("")) {
/* 949 */                     recordSet5.execute("update CptCapital set remark='" + str12 + "' where id=" + str34);
/*     */                   }
/* 951 */                   recordSet5.executeProc("HrmInfoStatus_UpdateCapital", "" + str49);
/*     */                   
/* 953 */                   cptShare.setCptShareByCpt(str34);
/*     */ 
/*     */                   
/* 956 */                   capitalComInfo.updateCapitalCache(str34);
/*     */                 } 
/*     */               } 
/*     */             } 
/*     */           } 
/* 961 */         }  try { capitalComInfo.removeCapitalCache(); }
/* 962 */         catch (Exception exception)
/* 963 */         { exception.printStackTrace(); }
/*     */       
/* 965 */       } catch (Exception exception) {
/* 966 */         exception.printStackTrace();
/* 967 */         baseBean.writeLog(exception.getMessage());
/*     */       } 
/* 969 */       return "1";
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/cpt/wfactions/CptApplyUseAction.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */