/*     */ package weaver.cpt.wfactions;
/*     */ 
/*     */ import com.engine.cpt.util.CapitalTransMethod;
/*     */ import com.engine.cpt.util.CptRightShareUitl;
/*     */ import com.weaver.formmodel.util.DateHelper;
/*     */ import java.math.BigDecimal;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import org.json.JSONArray;
/*     */ import org.json.JSONObject;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.cpt.capital.CapitalComInfo;
/*     */ import weaver.cpt.capital.CptShare;
/*     */ import weaver.cpt.maintenance.CapitalAssortmentComInfo;
/*     */ import weaver.cpt.maintenance.CapitalTypeComInfo;
/*     */ import weaver.cpt.util.CptUtil;
/*     */ import weaver.cpt.util.CptWfUtil;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.company.DepartmentComInfo;
/*     */ import weaver.hrm.company.SubCompanyComInfo;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ import weaver.interfaces.workflow.action.Action;
/*     */ import weaver.soa.workflow.request.Cell;
/*     */ import weaver.soa.workflow.request.DetailTable;
/*     */ import weaver.soa.workflow.request.Property;
/*     */ import weaver.soa.workflow.request.RequestInfo;
/*     */ import weaver.soa.workflow.request.Row;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CptChangeAction
/*     */   implements Action, Runnable
/*     */ {
/*  40 */   private static BaseBean baseBean = new BaseBean();
/*  41 */   private static Object lock = new Object();
/*  42 */   private CptWfUtil cptWfUtil = new CptWfUtil();
/*  43 */   private RequestInfo request = null;
/*  44 */   private JSONObject wfObject = null;
/*  45 */   private User user = null;
/*     */   
/*     */   public String execute(RequestInfo paramRequestInfo) {
/*  48 */     this.request = paramRequestInfo;
/*  49 */     this.user = paramRequestInfo.getRequestManager().getUser();
/*     */     try {
/*  51 */       this.wfObject = this.cptWfUtil.getCptwfInfo(paramRequestInfo.getWorkflowid());
/*     */       
/*  53 */       if ("".equals(this.wfObject.getString("zcname"))) {
/*  54 */         paramRequestInfo.getRequestManager().setMessageid("20088");
/*  55 */         paramRequestInfo.getRequestManager().setMessagecontent(SystemEnv.getHtmlLabelName(125188, this.user.getLanguage()));
/*  56 */         return "0";
/*     */       } 
/*     */       
/*  59 */       if ("1".equals(this.wfObject.getString("isasync"))) {
/*  60 */         (new Thread(this)).start();
/*     */       } else {
/*  62 */         return doAction(paramRequestInfo);
/*     */       } 
/*  64 */     } catch (Exception exception) {
/*  65 */       exception.printStackTrace();
/*  66 */       baseBean.writeLog(exception.getMessage());
/*     */     } 
/*  68 */     return "1";
/*     */   }
/*     */   
/*     */   public void run() {
/*  72 */     doAction(this.request);
/*     */   }
/*     */   
/*     */   private String doAction(RequestInfo paramRequestInfo) {
/*  76 */     synchronized (lock) {
/*  77 */       baseBean.writeLog("tagtag run action :" + getClass() + ",requestid:" + paramRequestInfo.getRequestid());
/*  78 */       CptShare cptShare = new CptShare();
/*  79 */       CapitalComInfo capitalComInfo = null;
/*  80 */       ResourceComInfo resourceComInfo = null;
/*  81 */       DepartmentComInfo departmentComInfo = null;
/*  82 */       CapitalAssortmentComInfo capitalAssortmentComInfo = null;
/*  83 */       CapitalTypeComInfo capitalTypeComInfo = null;
/*  84 */       SubCompanyComInfo subCompanyComInfo = null;
/*  85 */       CapitalTransMethod capitalTransMethod = new CapitalTransMethod();
/*  86 */       boolean bool = capitalTransMethod.IsWareHouseOpen();
/*     */       try {
/*  88 */         capitalComInfo = new CapitalComInfo();
/*  89 */         resourceComInfo = new ResourceComInfo();
/*  90 */         departmentComInfo = new DepartmentComInfo();
/*  91 */         capitalAssortmentComInfo = new CapitalAssortmentComInfo();
/*  92 */         capitalTypeComInfo = new CapitalTypeComInfo();
/*  93 */         subCompanyComInfo = new SubCompanyComInfo();
/*     */         
/*  95 */         RecordSet recordSet = new RecordSet();
/*  96 */         String str1 = paramRequestInfo.getRequestid();
/*     */         
/*  98 */         Map map1 = this.cptWfUtil.getcusField(paramRequestInfo.getWorkflowid(), "0");
/*  99 */         Map map2 = new HashMap<>();
/* 100 */         HashMap<Object, Object> hashMap = new HashMap<>();
/* 101 */         JSONArray jSONArray1 = new JSONArray();
/*     */         
/* 103 */         String str2 = "";
/* 104 */         String str3 = "";
/* 105 */         String str4 = "";
/* 106 */         String str5 = "";
/* 107 */         String str6 = "";
/* 108 */         String str7 = "";
/* 109 */         String str8 = "";
/* 110 */         String str9 = "";
/* 111 */         String str10 = "";
/* 112 */         String str11 = "";
/* 113 */         String str12 = "";
/* 114 */         String str13 = "";
/* 115 */         String str14 = "";
/* 116 */         String str15 = "";
/* 117 */         String str16 = "";
/*     */ 
/*     */         
/* 120 */         JSONArray jSONArray2 = new JSONArray();
/* 121 */         Property[] arrayOfProperty = paramRequestInfo.getMainTableInfo().getProperty(); byte b;
/* 122 */         for (b = 0; b < arrayOfProperty.length; b++) {
/* 123 */           String str17 = arrayOfProperty[b].getName();
/* 124 */           String str18 = Util.null2String(arrayOfProperty[b].getValue());
/*     */           
/* 126 */           if (this.wfObject.getInt("sqrtype") == 0 && str17.equalsIgnoreCase(this.wfObject.getString("sqrname"))) {
/* 127 */             str5 = str18;
/* 128 */             str6 = resourceComInfo.getDepartmentID(str5);
/*     */           } 
/* 130 */           if (this.wfObject.getInt("zctype") == 0 && str17.equalsIgnoreCase(this.wfObject.getString("zcname"))) {
/* 131 */             str2 = str18;
/*     */           }
/* 133 */           if (this.wfObject.getInt("zcztype") == 0 && str17.equalsIgnoreCase(this.wfObject.getString("zczname"))) {
/* 134 */             str4 = str18;
/*     */           }
/* 136 */           if (this.wfObject.getInt("sltype") == 0 && str17.equalsIgnoreCase(this.wfObject.getString("slname"))) {
/* 137 */             str12 = str18;
/*     */           }
/* 139 */           if (this.wfObject.getInt("cptnotype") == 0 && str17.equalsIgnoreCase(this.wfObject.getString("cptnoname"))) {
/* 140 */             str3 = str18;
/*     */           }
/* 142 */           if (this.wfObject.getInt("cfddtype") == 0 && str17.equalsIgnoreCase(this.wfObject.getString("cfddname"))) {
/* 143 */             str10 = str18;
/*     */           }
/* 145 */           if (this.wfObject.getInt("zclxtype") == 0 && str17.equalsIgnoreCase(this.wfObject.getString("zclxname"))) {
/* 146 */             str7 = str18;
/*     */           }
/* 148 */           if (this.wfObject.getInt("ggxhtype") == 0 && str17.equalsIgnoreCase(this.wfObject.getString("ggxhname"))) {
/* 149 */             str8 = str18;
/*     */           }
/* 151 */           if (this.wfObject.getInt("rkrqtype") == 0 && str17.equalsIgnoreCase(this.wfObject.getString("rkrqname"))) {
/* 152 */             str9 = str18;
/*     */           }
/* 154 */           if (this.wfObject.getInt("jgtype") == 0 && str17.equalsIgnoreCase(this.wfObject.getString("jgname"))) {
/* 155 */             str11 = str18;
/*     */           }
/* 157 */           if (this.wfObject.getInt("ssbmtype") == 0 && str17.equalsIgnoreCase(this.wfObject.getString("ssbmname"))) {
/* 158 */             str13 = str18;
/* 159 */             str14 = departmentComInfo.getSubcompanyid1(str13);
/*     */           } 
/* 161 */           if (this.wfObject.getInt("bztype") == 0 && str17.equalsIgnoreCase(this.wfObject.getString("bzname"))) {
/* 162 */             str15 = str18;
/*     */           }
/* 164 */           if (this.wfObject.getInt("rkcktype") == 0 && str17.equalsIgnoreCase(this.wfObject.getString("rkckname"))) {
/* 165 */             str16 = str18;
/*     */           }
/*     */ 
/*     */           
/* 169 */           for (String str : map1.keySet()) {
/* 170 */             JSONObject jSONObject = (JSONObject)map1.get(str);
/* 171 */             if (str17.equalsIgnoreCase(Util.null2String(jSONObject.get("fieldname")))) {
/* 172 */               hashMap.put(str, str18);
/*     */             }
/*     */           } 
/*     */         } 
/*     */         
/* 177 */         if (this.wfObject.getInt("zctype") == 0) {
/* 178 */           JSONObject jSONObject1 = new JSONObject();
/* 179 */           jSONObject1.put("sqr", str5);
/* 180 */           jSONObject1.put("sqbm", str6);
/* 181 */           jSONObject1.put("zc", str2);
/* 182 */           jSONObject1.put("zcz", str4);
/* 183 */           jSONObject1.put("sl", str12);
/* 184 */           jSONObject1.put("cptno", str3);
/* 185 */           jSONObject1.put("cfdd", str10);
/* 186 */           jSONObject1.put("zclx", str7);
/* 187 */           jSONObject1.put("ggxh", str8);
/* 188 */           jSONObject1.put("rkrq", str9);
/* 189 */           jSONObject1.put("jg", str11);
/* 190 */           jSONObject1.put("ssbm", str13);
/* 191 */           jSONObject1.put("ssfb", str14);
/* 192 */           jSONObject1.put("bz", str15);
/* 193 */           jSONObject1.put("rkck", str16);
/* 194 */           jSONArray2.put(jSONObject1);
/*     */ 
/*     */           
/* 197 */           JSONObject jSONObject2 = new JSONObject();
/* 198 */           for (String str17 : hashMap.keySet()) {
/* 199 */             String str18 = (String)hashMap.get(str17);
/* 200 */             jSONObject2.put(str17, str18);
/*     */           } 
/* 202 */           jSONArray1.put(jSONObject2);
/*     */         } else {
/*     */           
/* 205 */           DetailTable[] arrayOfDetailTable = paramRequestInfo.getDetailTableInfo().getDetailTable();
/* 206 */           String str = Util.null2String(Integer.valueOf(this.wfObject.getInt("zctype")));
/*     */           
/* 208 */           map2 = this.cptWfUtil.getcusField(paramRequestInfo.getWorkflowid(), str);
/* 209 */           DetailTable detailTable = null;
/* 210 */           for (DetailTable detailTable1 : arrayOfDetailTable) {
/* 211 */             String str17 = detailTable1.getTableDBName();
/* 212 */             if (!"".equals(str17)) {
/* 213 */               String str18 = str17.substring(str17.length() - 1);
/* 214 */               if (str18.equals(str)) {
/* 215 */                 detailTable = detailTable1;
/*     */                 break;
/*     */               } 
/*     */             } 
/*     */           } 
/* 220 */           if (detailTable != null) {
/* 221 */             Row[] arrayOfRow = detailTable.getRow();
/* 222 */             for (byte b1 = 0; b1 < arrayOfRow.length; b1++) {
/* 223 */               Row row = arrayOfRow[b1];
/* 224 */               Cell[] arrayOfCell = row.getCell();
/*     */               
/* 226 */               HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 227 */               for (byte b2 = 0; b2 < arrayOfCell.length; b2++) {
/* 228 */                 Cell cell = arrayOfCell[b2];
/* 229 */                 String str17 = cell.getName().toLowerCase();
/* 230 */                 String str18 = Util.null2String(cell.getValue());
/* 231 */                 if (str17.equalsIgnoreCase(this.wfObject.getString("sqrname")) && this.wfObject.getInt("zctype") == this.wfObject.getInt("sqrtype")) {
/* 232 */                   str5 = str18;
/* 233 */                   str6 = resourceComInfo.getDepartmentID(str5);
/*     */                 } 
/* 235 */                 if (str17.equalsIgnoreCase(this.wfObject.getString("zcname"))) {
/* 236 */                   str2 = str18;
/*     */                 }
/* 238 */                 if (str17.equalsIgnoreCase(this.wfObject.getString("zczname")) && this.wfObject.getInt("zctype") == this.wfObject.getInt("zcztype")) {
/* 239 */                   str4 = str18;
/*     */                 }
/* 241 */                 if (str17.equalsIgnoreCase(this.wfObject.getString("slname"))) {
/* 242 */                   str12 = str18;
/*     */                 }
/* 244 */                 if (str17.equalsIgnoreCase(this.wfObject.getString("cptnoname")) && this.wfObject.getInt("zctype") == this.wfObject.getInt("cptnotype")) {
/* 245 */                   str3 = str18;
/*     */                 }
/* 247 */                 if (str17.equalsIgnoreCase(this.wfObject.getString("cfddname")) && this.wfObject.getInt("zctype") == this.wfObject.getInt("cfddtype")) {
/* 248 */                   str10 = str18;
/*     */                 }
/* 250 */                 if (str17.equalsIgnoreCase(this.wfObject.getString("zclxname")) && this.wfObject.getInt("zctype") == this.wfObject.getInt("zclxtype")) {
/* 251 */                   str7 = str18;
/*     */                 }
/* 253 */                 if (str17.equalsIgnoreCase(this.wfObject.getString("ggxhname")) && this.wfObject.getInt("zctype") == this.wfObject.getInt("ggxhtype")) {
/* 254 */                   str8 = str18;
/*     */                 }
/* 256 */                 if (str17.equalsIgnoreCase(this.wfObject.getString("rkrqname")) && this.wfObject.getInt("zctype") == this.wfObject.getInt("rkrqtype")) {
/* 257 */                   str9 = str18;
/*     */                 }
/* 259 */                 if (str17.equalsIgnoreCase(this.wfObject.getString("jgname")) && this.wfObject.getInt("zctype") == this.wfObject.getInt("jgtype")) {
/* 260 */                   str11 = str18;
/*     */                 }
/* 262 */                 if (str17.equalsIgnoreCase(this.wfObject.getString("ssbmname")) && this.wfObject.getInt("zctype") == this.wfObject.getInt("ssbmtype")) {
/* 263 */                   str13 = str18;
/* 264 */                   str14 = departmentComInfo.getSubcompanyid1(str13);
/*     */                 } 
/* 266 */                 if (str17.equalsIgnoreCase(this.wfObject.getString("bzname")) && this.wfObject.getInt("zctype") == this.wfObject.getInt("bztype")) {
/* 267 */                   str15 = str18;
/*     */                 }
/* 269 */                 if (str17.equalsIgnoreCase(this.wfObject.getString("rkckname")) && this.wfObject.getInt("zctype") == this.wfObject.getInt("rkcktype")) {
/* 270 */                   str16 = str18;
/*     */                 }
/*     */                 
/* 273 */                 for (String str19 : map2.keySet()) {
/* 274 */                   JSONObject jSONObject = (JSONObject)map2.get(str19);
/* 275 */                   if (str17.equalsIgnoreCase(Util.null2String(jSONObject.get("fieldname")))) {
/* 276 */                     hashMap1.put(str19, str18);
/*     */                   }
/*     */                 } 
/*     */               } 
/* 280 */               JSONObject jSONObject1 = new JSONObject();
/* 281 */               jSONObject1.put("sqr", str5);
/* 282 */               jSONObject1.put("sqbm", str6);
/* 283 */               jSONObject1.put("zc", str2);
/* 284 */               jSONObject1.put("zcz", str4);
/* 285 */               jSONObject1.put("sl", str12);
/* 286 */               jSONObject1.put("cptno", str3);
/* 287 */               jSONObject1.put("cfdd", str10);
/* 288 */               jSONObject1.put("zclx", str7);
/* 289 */               jSONObject1.put("ggxh", str8);
/* 290 */               jSONObject1.put("rkrq", str9);
/* 291 */               jSONObject1.put("jg", str11);
/* 292 */               jSONObject1.put("ssbm", str13);
/* 293 */               jSONObject1.put("ssfb", str14);
/* 294 */               jSONObject1.put("bz", str15);
/* 295 */               jSONObject1.put("rkck", str16);
/*     */               
/* 297 */               jSONArray2.put(jSONObject1);
/*     */               
/* 299 */               JSONObject jSONObject2 = new JSONObject();
/* 300 */               for (String str17 : hashMap1.keySet()) {
/* 301 */                 String str18 = (String)hashMap1.get(str17);
/* 302 */                 jSONObject2.put(str17, str18);
/*     */               } 
/* 304 */               jSONArray1.put(jSONObject2);
/*     */             } 
/*     */           } 
/*     */         } 
/*     */         
/* 309 */         for (b = 0; b < jSONArray2.length(); b++) {
/* 310 */           JSONObject jSONObject = jSONArray2.getJSONObject(b);
/* 311 */           str5 = jSONObject.getString("sqr");
/* 312 */           str6 = jSONObject.getString("sqbm");
/* 313 */           str2 = jSONObject.getString("zc");
/* 314 */           str4 = jSONObject.getString("zcz");
/* 315 */           String str17 = jSONObject.getString("sl");
/* 316 */           double d1 = Util.getDoubleValue(str17, 0.0D);
/* 317 */           recordSet.execute("select name,sptcount,stateid,resourceid,departmentid,capitalgroupid,capitalnum,frozennum,mark,location,capitaltypeid,capitalspec,StockInDate,startprice,blongsubcompany,blongdepartment,warehouse from cptcapital where id='" + str2 + "' and isdata=2");
/*     */           
/* 319 */           recordSet.next();
/* 320 */           String str18 = recordSet.getString("name");
/* 321 */           String str19 = recordSet.getString("sptcount");
/* 322 */           String str20 = Util.null2String(recordSet.getString("stateid"), "1");
/* 323 */           String str21 = recordSet.getString("resourceid");
/* 324 */           String str22 = recordSet.getString("departmentid");
/* 325 */           String str23 = recordSet.getString("capitalgroupid");
/* 326 */           double d2 = Util.getDoubleValue(recordSet.getString("capitalnum"), 0.0D);
/* 327 */           double d3 = Util.getDoubleValue(recordSet.getString("frozennum"), 0.0D);
/* 328 */           String str24 = recordSet.getString("mark");
/* 329 */           String str25 = recordSet.getString("location");
/* 330 */           String str26 = recordSet.getString("capitaltypeid");
/* 331 */           String str27 = recordSet.getString("capitalspec");
/* 332 */           String str28 = recordSet.getString("StockInDate");
/* 333 */           double d4 = Util.getDoubleValue(recordSet.getString("startprice"), 0.0D);
/* 334 */           String str29 = (new BigDecimal(d4 + "")).setScale(2, 4).toString();
/* 335 */           String str30 = recordSet.getString("blongsubcompany");
/* 336 */           String str31 = recordSet.getString("blongdepartment");
/* 337 */           String str32 = recordSet.getString("warehouse");
/* 338 */           String str33 = recordSet.getString("datatype");
/* 339 */           if ("1".equals(str19)) {
/* 340 */             d1 = 1.0D;
/*     */           }
/* 342 */           if (d1 != 0.0D && d1 != d2 && d1 < d3) {
/* 343 */             paramRequestInfo.getRequestManager().setMessageid("20088");
/* 344 */             paramRequestInfo.getRequestManager().setMessagecontent(SystemEnv.getHtmlLabelName(503146, this.user.getLanguage()) + str18 + SystemEnv.getHtmlLabelName(503147, this.user.getLanguage()) + d3);
/* 345 */             return "0";
/*     */           } 
/* 347 */           String str34 = "";
/* 348 */           char c = Util.getSeparator();
/* 349 */           String str35 = DateHelper.getCurrentDate();
/* 350 */           int i = paramRequestInfo.getRequestManager().getUserId();
/* 351 */           str3 = jSONObject.getString("cptno");
/* 352 */           str10 = jSONObject.getString("cfdd");
/* 353 */           str7 = jSONObject.getString("zclx");
/* 354 */           str8 = jSONObject.getString("ggxh");
/* 355 */           str9 = jSONObject.getString("rkrq");
/* 356 */           double d5 = Util.getDoubleValue(jSONObject.getString("jg"), 0.0D);
/* 357 */           String str36 = (new BigDecimal(d5 + "")).setScale(2, 4).toString();
/* 358 */           str13 = jSONObject.getString("ssbm");
/* 359 */           str14 = jSONObject.getString("ssfb");
/* 360 */           str15 = jSONObject.getString("bz");
/* 361 */           String str37 = jSONObject.getString("rkck");
/*     */           
/* 363 */           String str38 = "update cptcapital set ";
/* 364 */           if (!"".equals(str5) && "1".equals(str19) && !"1".equals(str20) && !"5".equals(str20) && !"-7".equals(str20)) {
/* 365 */             str38 = str38 + "resourceid='" + str5 + "',departmentid='" + str6 + "',";
/* 366 */             if (!str5.equals(str21)) {
/* 367 */               str34 = str2;
/* 368 */               str34 = str34 + c + "resourceid";
/* 369 */               str34 = str34 + c + resourceComInfo.getResourcename(str21);
/* 370 */               str34 = str34 + c + resourceComInfo.getResourcename(str5);
/* 371 */               str34 = str34 + c + "" + i;
/* 372 */               str34 = str34 + c + str35;
/* 373 */               recordSet.executeProc("CptCapitalModify_Insert", str34);
/*     */             } 
/* 375 */             if (!str6.equals(str22)) {
/* 376 */               str34 = str2;
/* 377 */               str34 = str34 + c + "departmentid";
/* 378 */               str34 = str34 + c + departmentComInfo.getDepartmentName(str22);
/* 379 */               str34 = str34 + c + departmentComInfo.getDepartmentName(str6);
/* 380 */               str34 = str34 + c + "" + i;
/* 381 */               str34 = str34 + c + str35;
/* 382 */               recordSet.executeProc("CptCapitalModify_Insert", str34);
/*     */             } 
/*     */           } 
/* 385 */           if (!"".equals(str4)) {
/* 386 */             str38 = str38 + "capitalgroupid='" + str4 + "',";
/* 387 */             if (!str4.equals(str23)) {
/* 388 */               str34 = str2;
/* 389 */               str34 = str34 + c + "capitalgroupid";
/* 390 */               str34 = str34 + c + capitalAssortmentComInfo.getAssortmentName(str23);
/* 391 */               str34 = str34 + c + capitalAssortmentComInfo.getAssortmentName(str4);
/* 392 */               str34 = str34 + c + "" + i;
/* 393 */               str34 = str34 + c + str35;
/* 394 */               recordSet.executeProc("CptCapitalModify_Insert", str34);
/*     */             } 
/*     */           } 
/* 397 */           if (d1 >= 0.0D && !str17.equalsIgnoreCase("")) {
/* 398 */             str38 = str38 + "capitalnum='" + d1 + "',";
/* 399 */             if (d1 != d2) {
/* 400 */               str34 = str2;
/* 401 */               str34 = str34 + c + "capitalnum";
/* 402 */               str34 = str34 + c + "" + d2;
/* 403 */               str34 = str34 + c + "" + d1;
/* 404 */               str34 = str34 + c + "" + i;
/* 405 */               str34 = str34 + c + str35;
/* 406 */               recordSet.executeProc("CptCapitalModify_Insert", str34);
/*     */             } else {
/* 408 */               d1 = d2;
/*     */             } 
/*     */           } 
/* 411 */           if (!"".equals(str3) && !CptUtil.checkmarkstr(str3)) {
/* 412 */             str38 = str38 + "mark='" + str3 + "',";
/* 413 */             if (!str3.equals(str24)) {
/* 414 */               str34 = str2;
/* 415 */               str34 = str34 + c + "mark";
/* 416 */               str34 = str34 + c + str24;
/* 417 */               str34 = str34 + c + str3;
/* 418 */               str34 = str34 + c + "" + i;
/* 419 */               str34 = str34 + c + str35;
/* 420 */               recordSet.executeProc("CptCapitalModify_Insert", str34);
/*     */             } 
/*     */           } 
/* 423 */           if (!"".equals(str10)) {
/* 424 */             str38 = str38 + "location='" + str10 + "',";
/* 425 */             if (!str10.equals(str25)) {
/* 426 */               str34 = str2;
/* 427 */               str34 = str34 + c + "location";
/* 428 */               str34 = str34 + c + str25;
/* 429 */               str34 = str34 + c + str10;
/* 430 */               str34 = str34 + c + "" + i;
/* 431 */               str34 = str34 + c + str35;
/* 432 */               recordSet.executeProc("CptCapitalModify_Insert", str34);
/*     */             } 
/*     */           } 
/* 435 */           if (!"".equals(str7)) {
/* 436 */             str38 = str38 + "capitaltypeid='" + str7 + "',";
/* 437 */             if (!str7.equals(str26)) {
/* 438 */               str34 = str2;
/* 439 */               str34 = str34 + c + "capitaltypeid";
/* 440 */               str34 = str34 + c + capitalTypeComInfo.getCapitalTypename(str26);
/* 441 */               str34 = str34 + c + capitalTypeComInfo.getCapitalTypename(str7);
/* 442 */               str34 = str34 + c + "" + i;
/* 443 */               str34 = str34 + c + str35;
/* 444 */               recordSet.executeProc("CptCapitalModify_Insert", str34);
/*     */             } 
/*     */           } 
/* 447 */           if (!"".equals(str8)) {
/* 448 */             str38 = str38 + "capitalspec='" + str8 + "',";
/* 449 */             if (!str8.equals(str27)) {
/* 450 */               str34 = str2;
/* 451 */               str34 = str34 + c + "capitalspec";
/* 452 */               str34 = str34 + c + str27;
/* 453 */               str34 = str34 + c + str8;
/* 454 */               str34 = str34 + c + "" + i;
/* 455 */               str34 = str34 + c + str35;
/* 456 */               recordSet.executeProc("CptCapitalModify_Insert", str34);
/*     */             } 
/*     */           } 
/* 459 */           if (!"".equals(str9)) {
/* 460 */             str38 = str38 + "StockInDate='" + str9 + "',";
/* 461 */             if (!str9.equals(str28)) {
/* 462 */               str34 = str2;
/* 463 */               str34 = str34 + c + "stockindate";
/* 464 */               str34 = str34 + c + str28;
/* 465 */               str34 = str34 + c + str9;
/* 466 */               str34 = str34 + c + "" + i;
/* 467 */               str34 = str34 + c + str35;
/* 468 */               recordSet.executeProc("CptCapitalModify_Insert", str34);
/*     */             } 
/*     */           } 
/* 471 */           if (!str36.equals("0.00")) {
/* 472 */             str38 = str38 + "startprice='" + str36 + "',";
/* 473 */             if (!str36.equals(str29)) {
/* 474 */               str34 = str2;
/* 475 */               str34 = str34 + c + "startprice";
/* 476 */               str34 = str34 + c + "" + str29;
/* 477 */               str34 = str34 + c + "" + str36;
/* 478 */               str34 = str34 + c + "" + i;
/* 479 */               str34 = str34 + c + str35;
/* 480 */               recordSet.executeProc("CptCapitalModify_Insert", str34);
/*     */             } else {
/* 482 */               str36 = str29;
/*     */             } 
/*     */           } 
/* 485 */           if (!"".equals(str14)) {
/* 486 */             str38 = str38 + "blongsubcompany='" + str14 + "',";
/* 487 */             if (!str14.equals(str30)) {
/* 488 */               str34 = str2;
/* 489 */               str34 = str34 + c + "blongsubcompany";
/* 490 */               str34 = str34 + c + subCompanyComInfo.getSubCompanyname(str30);
/* 491 */               str34 = str34 + c + subCompanyComInfo.getSubCompanyname(str14);
/* 492 */               str34 = str34 + c + "" + i;
/* 493 */               str34 = str34 + c + str35;
/* 494 */               recordSet.executeProc("CptCapitalModify_Insert", str34);
/*     */             } 
/* 496 */             if (!str13.equals(str31)) {
/* 497 */               if (!bool && str19.equalsIgnoreCase("0")) {
/* 498 */                 recordSet.executeQuery("select * from CptCapital where datatype=? and blongdepartment=?", new Object[] { str33, str13 });
/* 499 */                 if (recordSet.next()) {
/*     */                   
/* 501 */                   recordSet.executeUpdate("update CptCapital set capitalnum=capitalnum+" + d1 + ",startprice=(capitalnum*startprice+" + (new BigDecimal(d1))
/* 502 */                       .setScale(2, 4).multiply((new BigDecimal(str36)).setScale(2, 4)) + ")/(capitalnum+" + d1 + ") where id = " + recordSet
/* 503 */                       .getString("id"), new Object[0]);
/* 504 */                   recordSet.executeUpdate("delete from CptCapital where id=?", new Object[] { str2 });
/*     */                 } else {
/* 506 */                   str38 = str38 + "blongdepartment='" + str13 + "',";
/*     */                 } 
/*     */               } else {
/* 509 */                 str38 = str38 + "blongdepartment='" + str13 + "',";
/*     */               } 
/* 511 */               str34 = str2;
/* 512 */               str34 = str34 + c + "blongdepartment";
/* 513 */               str34 = str34 + c + departmentComInfo.getDepartmentName(str31);
/* 514 */               str34 = str34 + c + departmentComInfo.getDepartmentName(str13);
/* 515 */               str34 = str34 + c + "" + i;
/* 516 */               str34 = str34 + c + str35;
/* 517 */               recordSet.executeProc("CptCapitalModify_Insert", str34);
/*     */             } else {
/* 519 */               str13 = str31;
/*     */             } 
/*     */           } 
/* 522 */           if (bool && !"".equals(str37) && 
/* 523 */             !str37.equals(str32)) {
/* 524 */             if (str19.equalsIgnoreCase("0")) {
/* 525 */               recordSet.executeQuery("select * from CptCapital where datatype=? and blongdepartment=? and warehouse=?", new Object[] { str33, str13, str37 });
/* 526 */               if (recordSet.next()) {
/*     */                 
/* 528 */                 recordSet.executeUpdate("update CptCapital set capitalnum=capitalnum+" + d1 + ",startprice=(capitalnum*startprice+" + (new BigDecimal(d1))
/* 529 */                     .setScale(2, 4).multiply((new BigDecimal(str36)).setScale(2, 4)) + ")/(capitalnum+" + d1 + ") where id = " + recordSet
/* 530 */                     .getString("id"), new Object[0]);
/* 531 */                 recordSet.executeUpdate("delete from CptCapital where id=?", new Object[] { str2 });
/*     */               } else {
/* 533 */                 str38 = str38 + "warehouse='" + str37 + "',";
/*     */               } 
/*     */             } else {
/* 536 */               str38 = str38 + "warehouse='" + str37 + "',";
/*     */             } 
/* 538 */             str34 = str2;
/* 539 */             str34 = str34 + c + "warehouse";
/* 540 */             str34 = str34 + c + capitalTransMethod.getWareHouseName(str32);
/* 541 */             str34 = str34 + c + capitalTransMethod.getWareHouseName(str37);
/* 542 */             str34 = str34 + c + "" + i;
/* 543 */             str34 = str34 + c + str35;
/* 544 */             recordSet.executeProc("CptCapitalModify_Insert", str34);
/*     */           } 
/*     */           
/* 547 */           str38 = str38 + "name=name where id='" + str2 + "'";
/* 548 */           recordSet.execute(str38);
/* 549 */           if (!"".equals(str15)) {
/* 550 */             recordSet.execute("update CptCapital set remark='" + str15 + "' where id = " + str2);
/*     */           }
/* 552 */           str38 = "insert into CptUseLog(capitalid,usedate,userequest,useresourceid,usestatus,remark,resourceid,usecount) values(";
/* 553 */           if (d1 >= 0.0D && !str17.equalsIgnoreCase("")) {
/* 554 */             str38 = str38 + "'" + str2 + "','" + str35 + "','" + str1 + "','-1','7','" + str15 + "','" + i + "','" + d1 + "'";
/*     */           } else {
/* 556 */             str38 = str38 + "'" + str2 + "','" + str35 + "','" + str1 + "','-1','7','" + str15 + "','" + i + "','" + d2 + "'";
/*     */           } 
/* 558 */           str38 = str38 + ")";
/* 559 */           recordSet.execute(str38);
/*     */           
/* 561 */           if (!"".equals(str4) && 
/* 562 */             !str4.equals(str23)) {
/* 563 */             CptRightShareUitl.freshenCptShareByCapitalgroup(str2, str23);
/*     */           }
/*     */ 
/*     */           
/* 567 */           if (!"".equals(str5) && "1".equals(str19) && !"1".equals(str20) && !"5".equals(str20) && !"-7".equals(str20) && 
/* 568 */             !str5.equals(str21)) {
/* 569 */             cptShare.freshenCptShareByResource(str2);
/*     */           }
/*     */ 
/*     */           
/* 573 */           if (!"".equals(str37) && 
/* 574 */             !str37.equals(str32)) {
/* 575 */             cptShare.freshenCptShareByWareHouse(str2);
/*     */           }
/*     */ 
/*     */           
/* 579 */           this.cptWfUtil.updateCptFieldOfWF(map1, hashMap, map2, jSONArray1.getJSONObject(b), str2, this.user.getUID());
/*     */ 
/*     */           
/* 582 */           CptRightShareUitl.editCapitalResetRight(str2);
/* 583 */           capitalComInfo.deleteCapitalCache(str2);
/*     */         } 
/* 585 */         capitalComInfo.removeCapitalCache();
/* 586 */       } catch (Exception exception) {
/* 587 */         exception.printStackTrace();
/* 588 */         baseBean.writeLog(exception.getMessage());
/*     */       } 
/*     */       
/* 591 */       Thread thread = new Thread() {
/*     */           public void run() {
/*     */             try {
/* 594 */               sleep(15000L);
/* 595 */             } catch (InterruptedException interruptedException) {
/* 596 */               interruptedException.printStackTrace();
/*     */             } 
/* 598 */             RecordSet recordSet = new RecordSet();
/* 599 */             recordSet.execute("update cptcapital set frozennum=0 where frozennum>0");
/* 600 */             CptWfUtil cptWfUtil = new CptWfUtil();
/* 601 */             cptWfUtil.DoFrozenCpt_new();
/*     */           }
/*     */         };
/* 604 */       thread.start();
/*     */       
/* 606 */       return "1";
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/cpt/wfactions/CptChangeAction.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */