/*    */ package weaver.cpt.job;
/*    */ 
/*    */ import com.cloudstore.dev.api.bean.MessageBean;
/*    */ import com.cloudstore.dev.api.bean.MessageType;
/*    */ import com.cloudstore.dev.api.util.Util_Message;
/*    */ import com.engine.msgcenter.biz.ConfigManager;
/*    */ import com.engine.msgcenter.biz.WeaMessageTypeConfig;
/*    */ import com.google.common.collect.Sets;
/*    */ import java.io.IOException;
/*    */ import java.text.DecimalFormat;
/*    */ import java.util.Map;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.BaseBean;
/*    */ import weaver.general.GCONST;
/*    */ import weaver.general.LabelUtil;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.User;
/*    */ import weaver.interfaces.schedule.BaseCronJob;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class CptLowInventoryRemindJob
/*    */   extends BaseCronJob
/*    */ {
/* 31 */   private BaseBean bb = new BaseBean();
/*    */   
/*    */   public void setCronExpr(String paramString) {
/* 34 */     this.cronExpr = paramString;
/*    */   }
/*    */   
/*    */   private String cronExpr;
/*    */   
/*    */   public String getCronExpr() {
/* 40 */     return this.cronExpr;
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public void execute() {
/* 46 */     this.bb.writeLog("run " + getClass().getName() + " job...");
/*    */     try {
/* 48 */       generateReminder();
/* 49 */     } catch (Exception exception) {
/* 50 */       exception.printStackTrace();
/* 51 */       this.bb.writeLog(exception.getMessage());
/*    */     } 
/*    */   }
/*    */ 
/*    */   
/*    */   public static synchronized void generateReminder() {
/* 57 */     RecordSet recordSet = new RecordSet();
/* 58 */     ConfigManager configManager = new ConfigManager();
/* 59 */     DecimalFormat decimalFormat = new DecimalFormat("0.00");
/* 60 */     String str1 = "";
/* 61 */     String str2 = "";
/* 62 */     String str3 = "";
/*    */     
/* 64 */     String str4 = " select t1.id,t1.name,t2.name as dataname,t1.datatype,t2.resourceid,t1.capitalnum,t1.alertnum from cptcapital t1 left outer join cptcapital t2 on t2.id=t1.datatype  where t1.isdata='2' and (t1.sptcount!=1 or t1.sptcount is null) ";
/*    */     
/* 66 */     recordSet.executeQuery(str4, new Object[0]);
/* 67 */     while (recordSet.next()) {
/* 68 */       str3 = recordSet.getString("resourceid");
/*    */       
/*    */       try {
/* 71 */         Util_Message.delMessage(str3, MessageType.STOCK_LOW_WARNING);
/* 72 */       } catch (Exception exception) {
/* 73 */         exception.printStackTrace();
/*    */       } 
/*    */     } 
/*    */     
/* 77 */     String str5 = " select t1.id,t1.name,t2.name as dataname,t1.datatype,t2.resourceid,t1.capitalnum,t1.alertnum from cptcapital t1 left outer join cptcapital t2 on t2.id=t1.datatype  where t1.isdata='2' and (t1.sptcount!=1 or t1.sptcount is null) and t1.alertnum>0 and t1.capitalnum<=t1.alertnum ";
/*    */     
/* 79 */     recordSet.executeQuery(str5, new Object[0]);
/* 80 */     while (recordSet.next()) {
/* 81 */       String str = "";
/* 82 */       str1 = recordSet.getString("id");
/* 83 */       str2 = recordSet.getString("name");
/* 84 */       str3 = recordSet.getString("resourceid");
/* 85 */       User user = new User(Util.getIntValue(str3));
/*    */       
/* 87 */       str = str + LabelUtil.getMultiLangLabel("1331") + "：" + decimalFormat.format(Double.valueOf(recordSet.getString("capitalnum"))) + "</br>";
/* 88 */       str = str + LabelUtil.getMultiLangLabel("15294") + "：" + decimalFormat.format(Double.valueOf(recordSet.getString("alertnum")));
/* 89 */       Map map = configManager.defaultRuleCheckConfig(MessageType.STOCK_LOW_WARNING, Util.getIntValue(str3), null);
/* 90 */       for (Map.Entry entry : map.entrySet()) {
/* 91 */         MessageBean messageBean = null;
/*    */         try {
/* 93 */           messageBean = Util_Message.createMessage(MessageType.STOCK_LOW_WARNING, Util.getIntValue(str3), str2, "387532", str, GCONST.getContextPath() + "/spa/cpt/static/index.html#/main/cpt/InventoryRemind?cptid=" + str1, "", 0);
/* 94 */           WeaMessageTypeConfig weaMessageTypeConfig = (WeaMessageTypeConfig)entry.getKey();
/* 95 */           messageBean.setMessageConfig(weaMessageTypeConfig);
/* 96 */           messageBean.setUserList(Sets.newHashSet((Iterable)entry.getValue()));
/* 97 */           Util_Message.sendAndpublishMessage(messageBean);
/* 98 */         } catch (IOException iOException) {
/* 99 */           iOException.printStackTrace();
/*    */         } 
/*    */       } 
/*    */     } 
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/cpt/job/CptLowInventoryRemindJob.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */