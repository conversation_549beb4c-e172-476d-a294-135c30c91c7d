package weaver.cpt.maintenance;

import java.util.EmptyStackException;

class IIlIII extends Calculate.IIllIl {
  IIlIII(char param<PERSON>har, int paramInt) {
    super(paramChar, paramInt);
  }
  
  void _$1() {
    try {
      Double double_1 = Calculate.access$0().pop();
      Double double_2 = Calculate.access$0().pop();
      Calculate.access$0().push(new Double(double_2.doubleValue() * double_1.doubleValue()));
    } catch (EmptyStackException emptyStackException) {
      Calculate._$1 = -1;
    } 
  }
}


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/cpt/maintenance/IlIIIlllIIIIlIII.class
 * Java compiler version: 1 (45.3)
 * JD-Core Version:       1.1.3
 */