/*     */ package weaver.cpt.maintenance;
/*     */ 
/*     */ import java.util.EmptyStackException;
/*     */ import java.util.Stack;
/*     */ import weaver.general.BaseBean;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class Calculate
/*     */   extends BaseBean
/*     */ {
/*  26 */   static int error = 0;
/*     */ 
/*     */   
/*     */   private static class _Operator
/*     */   {
/*     */     final char oper;
/*     */     
/*     */     final int priority;
/*     */     
/*     */     void operation() {}
/*     */     
/*     */     _Operator(char param1Char, int param1Int) {
/*  38 */       this.oper = param1Char;
/*  39 */       this.priority = param1Int;
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   private static class _Add
/*     */     extends _Operator
/*     */   {
/*     */     void operation() {
/*     */       try {
/*  50 */         Double double_1 = Calculate.digitStack.pop();
/*  51 */         Double double_2 = Calculate.digitStack.pop();
/*  52 */         Calculate.digitStack.push(new Double(double_2.doubleValue() + double_1.doubleValue()));
/*     */       }
/*  54 */       catch (EmptyStackException emptyStackException) {
/*  55 */         Calculate.error = -1;
/*     */       } 
/*     */     }
/*     */     
/*     */     _Add(char param1Char, int param1Int) {
/*  60 */       super(param1Char, param1Int);
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   private static class _Subtract
/*     */     extends _Operator
/*     */   {
/*     */     void operation() {
/*     */       try {
/*  71 */         Double double_1 = Calculate.digitStack.pop();
/*  72 */         Double double_2 = Calculate.digitStack.pop();
/*  73 */         Calculate.digitStack.push(new Double(double_2.doubleValue() - double_1.doubleValue()));
/*     */       }
/*  75 */       catch (EmptyStackException emptyStackException) {
/*  76 */         Calculate.error = -1;
/*     */       } 
/*     */     }
/*     */     
/*     */     _Subtract(char param1Char, int param1Int) {
/*  81 */       super(param1Char, param1Int);
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   private static class _Multiply
/*     */     extends _Operator
/*     */   {
/*     */     void operation() {
/*     */       try {
/*  92 */         Double double_1 = Calculate.digitStack.pop();
/*  93 */         Double double_2 = Calculate.digitStack.pop();
/*  94 */         Calculate.digitStack.push(new Double(double_2.doubleValue() * double_1.doubleValue()));
/*     */       }
/*  96 */       catch (EmptyStackException emptyStackException) {
/*  97 */         Calculate.error = -1;
/*     */       } 
/*     */     }
/*     */     
/*     */     _Multiply(char param1Char, int param1Int) {
/* 102 */       super(param1Char, param1Int);
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   private static class _Divide
/*     */     extends _Operator
/*     */   {
/*     */     void operation() {
/*     */       try {
/* 113 */         Double double_1 = Calculate.digitStack.pop();
/* 114 */         Double double_2 = Calculate.digitStack.pop();
/* 115 */         Calculate.digitStack.push(new Double(double_2.doubleValue() / double_1.doubleValue()));
/*     */       }
/* 117 */       catch (EmptyStackException emptyStackException) {
/* 118 */         Calculate.error = -1;
/*     */       } 
/*     */     }
/*     */     
/*     */     _Divide(char param1Char, int param1Int) {
/* 123 */       super(param1Char, param1Int);
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   private static class _Power
/*     */     extends _Operator
/*     */   {
/*     */     void operation() {
/*     */       try {
/* 134 */         Double double_1 = Calculate.digitStack.pop();
/* 135 */         Double double_2 = Calculate.digitStack.pop();
/* 136 */         Calculate.digitStack.push(new Double(Math.pow(double_2.doubleValue(), double_1.doubleValue())));
/*     */       }
/* 138 */       catch (EmptyStackException emptyStackException) {
/* 139 */         Calculate.error = -1;
/*     */       } 
/*     */     }
/*     */     
/*     */     _Power(char param1Char, int param1Int) {
/* 144 */       super(param1Char, param1Int);
/*     */     } }
/*     */   
/* 147 */   static _Operator _LEFTPARE = new _Operator('(', 0);
/* 148 */   static _Operator _RIGHTPARE = new _Operator(')', 0);
/* 149 */   static _Operator _ADD = new _Add('+', 1);
/* 150 */   static _Operator _SUB = new _Subtract('-', 1);
/* 151 */   static _Operator _MUL = new _Multiply('*', 2);
/* 152 */   static _Operator _DIV = new _Divide('/', 2);
/* 153 */   static _Operator _POW = new _Power('^', 3);
/*     */ 
/*     */   
/* 156 */   private static Stack operStack = new Stack();
/* 157 */   private static Stack digitStack = new Stack();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private double cal(String paramString) {
/* 170 */     StringBuffer stringBuffer1 = new StringBuffer(paramString.trim());
/* 171 */     StringBuffer stringBuffer2 = new StringBuffer();
/*     */ 
/*     */     
/* 174 */     char c = 'a';
/* 175 */     Double double_ = new Double(0.0D);
/*     */     
/*     */     byte b;
/*     */     
/* 179 */     for (b = 0; b < stringBuffer1.length(); b++) {
/* 180 */       char c1 = stringBuffer1.charAt(b);
/* 181 */       if (b != 0) {
/* 182 */         c = stringBuffer1.charAt(b - 1);
/*     */       }
/* 184 */       if (b == 0 && (c1 == _SUB.oper || c1 == _ADD.oper)) {
/* 185 */         stringBuffer1.insert(0, '0');
/*     */       }
/* 187 */       else if (c == _LEFTPARE.oper && (c1 == _SUB.oper || c1 == _ADD.oper)) {
/* 188 */         stringBuffer1.insert(b, '0');
/*     */       } 
/*     */     } 
/*     */     
/* 192 */     for (b = 0; b < stringBuffer1.length(); b++) {
/*     */       
/* 194 */       char c1 = stringBuffer1.charAt(b);
/*     */       
/* 196 */       if (c1 == _LEFTPARE.oper) {
/*     */         
/* 198 */         if (b != 0 && stringBuffer1.charAt(b - 1) >= '0' && stringBuffer1.charAt(b - 1) <= '9') {
/* 199 */           error = -1;
/* 200 */           return 0.0D;
/*     */         } 
/* 202 */         operStack.push(new _Operator('(', 0));
/*     */       }
/* 204 */       else if (c1 == _RIGHTPARE.oper) {
/*     */         _Operator _Operator1;
/* 206 */         if (b < stringBuffer1.length() - 1 && stringBuffer1.charAt(b + 1) >= '0' && stringBuffer1.charAt(b + 1) <= '9') {
/* 207 */           error = -1;
/* 208 */           return 0.0D;
/*     */         } 
/*     */         
/* 211 */         if (stringBuffer2.length() != 0) {
/* 212 */           digitStack.push(new Double(stringBuffer2.toString()));
/* 213 */           stringBuffer2 = stringBuffer2.delete(0, stringBuffer2.length());
/*     */         } 
/*     */         
/*     */         do {
/* 217 */           _Operator1 = operStack.pop();
/* 218 */           _Operator1.operation();
/* 219 */         } while (_Operator1.oper != _LEFTPARE.oper);
/*     */       
/*     */       }
/* 222 */       else if (c1 == _ADD.oper) {
/*     */         
/* 224 */         if (stringBuffer2.length() != 0) {
/* 225 */           digitStack.push(new Double(stringBuffer2.toString()));
/* 226 */           stringBuffer2 = stringBuffer2.delete(0, stringBuffer2.length());
/*     */         } 
/*     */         
/* 229 */         if (operStack.empty()) {
/* 230 */           operStack.push(new _Add('+', 1));
/*     */         }
/*     */         else {
/*     */           
/* 234 */           _Operator _Operator1 = operStack.peek();
/* 235 */           if (_Operator1.priority >= _ADD.priority) {
/* 236 */             _Operator1 = operStack.pop();
/* 237 */             _Operator1.operation();
/* 238 */             operStack.push(new _Add('+', 1));
/*     */           } else {
/*     */             
/* 241 */             operStack.push(new _Add('+', 1));
/*     */           }
/*     */         
/*     */         } 
/* 245 */       } else if (c1 == _SUB.oper) {
/*     */         
/* 247 */         if (stringBuffer2.length() != 0) {
/* 248 */           digitStack.push(new Double(stringBuffer2.toString()));
/* 249 */           stringBuffer2 = stringBuffer2.delete(0, stringBuffer2.length());
/*     */         } 
/*     */         
/* 252 */         if (operStack.empty()) {
/* 253 */           operStack.push(new _Subtract('-', 1));
/*     */         }
/*     */         else {
/*     */           
/* 257 */           _Operator _Operator1 = operStack.peek();
/* 258 */           if (_Operator1.priority >= _SUB.priority) {
/* 259 */             _Operator1 = operStack.pop();
/* 260 */             _Operator1.operation();
/* 261 */             operStack.push(new _Subtract('-', 1));
/*     */           } else {
/*     */             
/* 264 */             operStack.push(new _Subtract('-', 1));
/*     */           }
/*     */         
/*     */         } 
/* 268 */       } else if (c1 == _MUL.oper) {
/*     */         
/* 270 */         if (stringBuffer2.length() != 0) {
/* 271 */           digitStack.push(new Double(stringBuffer2.toString()));
/* 272 */           stringBuffer2 = stringBuffer2.delete(0, stringBuffer2.length());
/*     */         } 
/*     */         
/* 275 */         if (operStack.empty()) {
/* 276 */           operStack.push(new _Multiply('*', 2));
/*     */         }
/*     */         else {
/*     */           
/* 280 */           _Operator _Operator1 = operStack.peek();
/* 281 */           if (_Operator1.priority >= _MUL.priority) {
/* 282 */             _Operator1 = operStack.pop();
/* 283 */             _Operator1.operation();
/* 284 */             operStack.push(new _Multiply('*', 2));
/*     */           } else {
/*     */             
/* 287 */             operStack.push(new _Multiply('*', 2));
/*     */           }
/*     */         
/*     */         } 
/* 291 */       } else if (c1 == _DIV.oper) {
/*     */         
/* 293 */         if (stringBuffer2.length() != 0) {
/* 294 */           digitStack.push(new Double(stringBuffer2.toString()));
/* 295 */           stringBuffer2 = stringBuffer2.delete(0, stringBuffer2.length());
/*     */         } 
/*     */         
/* 298 */         if (operStack.empty()) {
/* 299 */           operStack.push(new _Divide('/', 2));
/*     */         }
/*     */         else {
/*     */           
/* 303 */           _Operator _Operator1 = operStack.peek();
/* 304 */           if (_Operator1.priority >= _MUL.priority) {
/* 305 */             _Operator1 = operStack.pop();
/* 306 */             _Operator1.operation();
/* 307 */             operStack.push(new _Divide('/', 2));
/*     */           } else {
/*     */             
/* 310 */             operStack.push(new _Divide('/', 2));
/*     */           }
/*     */         
/*     */         } 
/* 314 */       } else if (c1 == _POW.oper) {
/*     */         
/* 316 */         if (stringBuffer2.length() != 0) {
/* 317 */           digitStack.push(new Double(stringBuffer2.toString()));
/* 318 */           stringBuffer2 = stringBuffer2.delete(0, stringBuffer2.length());
/*     */         } 
/*     */         
/* 321 */         if (operStack.empty()) {
/* 322 */           operStack.push(new _Power('^', 2));
/*     */         }
/*     */         else {
/*     */           
/* 326 */           _Operator _Operator1 = operStack.peek();
/* 327 */           if (_Operator1.priority >= _MUL.priority) {
/* 328 */             _Operator1 = operStack.pop();
/* 329 */             _Operator1.operation();
/* 330 */             operStack.push(new _Power('^', 2));
/*     */           } else {
/*     */             
/* 333 */             operStack.push(new _Power('^', 2));
/*     */           } 
/*     */         } 
/*     */       } else {
/*     */         
/* 338 */         stringBuffer2.append(c1);
/*     */       } 
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 346 */     if (stringBuffer2.length() != 0) {
/* 347 */       digitStack.push(new Double(stringBuffer2.toString()));
/* 348 */       stringBuffer2 = stringBuffer2.delete(0, stringBuffer2.length());
/*     */     } 
/* 350 */     while (!operStack.empty()) {
/* 351 */       _Operator _Operator1 = operStack.pop();
/* 352 */       if (_Operator1.oper == _LEFTPARE.oper) {
/* 353 */         error = -1;
/*     */       }
/* 355 */       _Operator1.operation();
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 362 */     double_ = digitStack.pop();
/*     */     
/* 364 */     return double_.doubleValue();
/*     */   }
/*     */   
/*     */   public double calculate(String paramString) {
/* 368 */     double d = 0.0D;
/*     */     try {
/* 370 */       d = cal(paramString);
/*     */     }
/* 372 */     catch (Exception exception) {
/* 373 */       error = -1;
/*     */     } 
/* 375 */     if (error == -1)
/*     */     {
/*     */       
/* 378 */       return -1.0D;
/*     */     }
/* 380 */     return d;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void clearAll() {
/* 388 */     error = 0;
/* 389 */     operStack.removeAllElements();
/* 390 */     digitStack.removeAllElements();
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/cpt/maintenance/Calculate.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */