/*    */ package weaver.cpt.maintenance;
/*    */ 
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.BaseBean;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class DepreRatioCal
/*    */   extends BaseBean
/*    */ {
/* 18 */   private Calculate calcu = new Calculate();
/* 19 */   private double startunit = 0.0D;
/* 20 */   private double endunit = 0.0D;
/* 21 */   private String deprefunc = "";
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public void setDepre(String paramString) throws Exception {
/* 28 */     RecordSet recordSet = new RecordSet();
/*    */     try {
/* 30 */       recordSet.executeProc("CptDepreMethod1_SelectByID", paramString);
/* 31 */       while (recordSet.next()) {
/* 32 */         this.startunit = recordSet.getDouble("startunit");
/* 33 */         this.endunit = recordSet.getDouble("endunit");
/* 34 */         this.deprefunc = recordSet.getString("deprefunc");
/*    */       }
/*    */     
/* 37 */     } catch (Exception exception) {
/* 38 */       writeLog(exception);
/* 39 */       throw exception;
/*    */     } 
/*    */   }
/*    */ 
/*    */   
/*    */   public void clearDepre() {
/* 45 */     this.startunit = 0.0D;
/* 46 */     this.endunit = 0.0D;
/* 47 */     this.deprefunc = "";
/*    */   }
/*    */ 
/*    */   
/*    */   private double getDepre(double paramDouble) throws Exception {
/*    */     try {
/* 53 */       return 1.0D * this.startunit - getFuncValue("" + paramDouble, this.deprefunc) / getFuncValue("1", this.deprefunc) * (this.startunit - this.endunit);
/*    */     }
/* 55 */     catch (Exception exception) {
/* 56 */       writeLog(exception);
/* 57 */       throw exception;
/*    */     } 
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   private double getFuncValue(String paramString1, String paramString2) throws Exception {
/* 64 */     double d = 0.0D;
/* 65 */     StringBuffer stringBuffer = new StringBuffer(paramString2);
/* 66 */     for (byte b = 0; b < stringBuffer.length(); b++) {
/* 67 */       if (stringBuffer.charAt(b) == 't') {
/* 68 */         stringBuffer.replace(b, b + 1, "(" + paramString1 + ")");
/*    */       }
/*    */     } 
/* 71 */     d = this.calcu.calculate(stringBuffer.toString());
/* 72 */     this.calcu.clearAll();
/* 73 */     return d;
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public float getDepreRatio(double paramDouble) throws Exception {
/*    */     try {
/* 80 */       return (float)getDepre(paramDouble);
/*    */     }
/* 82 */     catch (Exception exception) {
/* 83 */       writeLog(exception);
/* 84 */       throw exception;
/*    */     } 
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/cpt/maintenance/DepreRatioCal.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */