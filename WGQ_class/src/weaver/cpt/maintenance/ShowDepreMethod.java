/*     */ package weaver.cpt.maintenance;
/*     */ 
/*     */ import com.sun.image.codec.jpeg.JPEGCodec;
/*     */ import com.sun.image.codec.jpeg.JPEGEncodeParam;
/*     */ import com.sun.image.codec.jpeg.JPEGImageEncoder;
/*     */ import java.awt.BasicStroke;
/*     */ import java.awt.Color;
/*     */ import java.awt.Graphics2D;
/*     */ import java.awt.image.BufferedImage;
/*     */ import java.io.BufferedOutputStream;
/*     */ import java.io.IOException;
/*     */ import java.io.OutputStream;
/*     */ import javax.servlet.ServletException;
/*     */ import javax.servlet.ServletOutputStream;
/*     */ import javax.servlet.http.HttpServlet;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.general.Util;
/*     */ 
/*     */ 
/*     */ 
/*     */ public class ShowDepreMethod
/*     */   extends HttpServlet
/*     */ {
/*  25 */   private int width = 350;
/*  26 */   private int height = 250;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void doGet(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws ServletException, IOException {
/*  33 */     String str = Util.null2String(paramHttpServletRequest.getParameter("depreid"));
/*     */     
/*     */     try {
/*  36 */       DepreRatioCal depreRatioCal = new DepreRatioCal();
/*  37 */       depreRatioCal.setDepre(str);
/*     */       
/*  39 */       ServletOutputStream servletOutputStream = paramHttpServletResponse.getOutputStream();
/*  40 */       BufferedOutputStream bufferedOutputStream = new BufferedOutputStream((OutputStream)servletOutputStream);
/*     */       
/*  42 */       BufferedImage bufferedImage = new BufferedImage(this.width, this.height, 1);
/*     */       
/*  44 */       JPEGEncodeParam jPEGEncodeParam = null;
/*  45 */       JPEGImageEncoder jPEGImageEncoder = JPEGCodec.createJPEGEncoder(bufferedOutputStream);
/*     */       
/*  47 */       jPEGEncodeParam = jPEGImageEncoder.getDefaultJPEGEncodeParam(bufferedImage);
/*  48 */       jPEGEncodeParam.setQuality(0.9F, true);
/*  49 */       jPEGImageEncoder.setJPEGEncodeParam(jPEGEncodeParam);
/*     */       
/*  51 */       Graphics2D graphics2D = bufferedImage.createGraphics();
/*     */       
/*  53 */       graphics2D.setColor(Color.white);
/*  54 */       graphics2D.fillRect(0, 0, this.width, this.height);
/*     */       
/*  56 */       byte b1 = 30;
/*  57 */       char c1 = 'Ü';
/*  58 */       char c2 = 'ŀ';
/*  59 */       byte b2 = 0;
/*     */       
/*  61 */       BasicStroke basicStroke = new BasicStroke(3.0F);
/*  62 */       graphics2D.setStroke(basicStroke);
/*     */       
/*  64 */       graphics2D.setColor(Color.black);
/*  65 */       graphics2D.drawLine(b1, c1, b1, b2);
/*  66 */       graphics2D.drawLine(b1, c1, c2, c1);
/*     */       
/*  68 */       drawArrow(b1, c1, b1, b2, graphics2D);
/*  69 */       graphics2D.drawString("X", b1 + 10, b2 + 10);
/*  70 */       drawArrow(b1, c1, c2, c1, graphics2D);
/*  71 */       graphics2D.drawString("Y", c2 + 10, c1 + 5);
/*     */ 
/*     */       
/*  74 */       int i = 0;
/*  75 */       int j = (int)(200.0F * depreRatioCal.getDepreRatio(i / 10.0D));
/*     */       byte b3;
/*  77 */       for (b3 = 0; b3 < 13; b3 += 2) {
/*  78 */         int k = b3 * 20;
/*  79 */         graphics2D.drawString("" + (b3 / 10.0D), k + 30, c1 + 15);
/*     */       } 
/*     */       
/*  82 */       for (b3 = 0; b3 <= 10; b3++) {
/*  83 */         int k = b3 * 20;
/*  84 */         graphics2D.drawString("" + (b3 / 10.0D), 10, c1 - k);
/*     */       } 
/*     */       
/*  87 */       basicStroke = new BasicStroke(2.0F);
/*  88 */       graphics2D.setStroke(basicStroke);
/*     */       
/*  90 */       graphics2D.setColor(Color.red);
/*  91 */       for (b3 = 0; b3 <= 10; b3++) {
/*  92 */         int k = b3 * 20;
/*  93 */         int m = (int)(200.0F * depreRatioCal.getDepreRatio(b3 / 10.0D));
/*  94 */         graphics2D.drawLine(i + 30, c1 - j, k + 30, c1 - m);
/*     */         
/*  96 */         i = k;
/*  97 */         j = m;
/*     */       } 
/*  99 */       graphics2D.drawLine(i + 30, c1 - j, i + 50 + 30, c1 - j);
/*     */       
/* 101 */       graphics2D.dispose();
/* 102 */       jPEGImageEncoder.encode(bufferedImage);
/*     */ 
/*     */       
/* 105 */       servletOutputStream.flush();
/* 106 */       bufferedOutputStream.close();
/* 107 */       servletOutputStream.close();
/*     */ 
/*     */     
/*     */     }
/* 111 */     catch (Exception exception) {}
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void drawArrow(int paramInt1, int paramInt2, int paramInt3, int paramInt4, Graphics2D paramGraphics2D) {
/* 124 */     int i = paramInt1 + paramInt2 - paramInt4;
/* 125 */     int j = paramInt2 - paramInt1 + paramInt3;
/* 126 */     int k = paramInt1 - paramInt2 + paramInt4;
/* 127 */     int m = paramInt2 + paramInt1 - paramInt3;
/*     */     
/* 129 */     double d = Math.sqrt(((i - paramInt3) * (i - paramInt3) + (j - paramInt4) * (j - paramInt4)));
/* 130 */     if (d != 0.0D) {
/* 131 */       d = 10.0D / d;
/* 132 */       if (i == paramInt3 && j != paramInt4) {
/* 133 */         j = paramInt4 + 10;
/* 134 */         i = i;
/*     */       }
/* 136 */       else if (j == paramInt4) {
/* 137 */         i = paramInt3 + 10;
/* 138 */         j = j;
/*     */       } else {
/*     */         
/* 141 */         i = (int)(paramInt3 + d * (i - paramInt3));
/* 142 */         j = (int)(paramInt4 + d * (j - paramInt4));
/*     */       } 
/*     */       
/* 145 */       if (k == paramInt3 && m != paramInt4) {
/* 146 */         m = paramInt4 + 10;
/* 147 */         k = k;
/*     */       }
/* 149 */       else if (m == paramInt4) {
/* 150 */         k = paramInt3 + 10;
/* 151 */         m = m;
/*     */       } else {
/*     */         
/* 154 */         k = (int)(paramInt3 + d * (k - paramInt3));
/* 155 */         m = (int)(paramInt4 + d * (m - paramInt4));
/*     */       } 
/*     */     } 
/* 158 */     paramGraphics2D.drawLine(paramInt3, paramInt4, i, j);
/* 159 */     paramGraphics2D.drawLine(paramInt3, paramInt4, k, m);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/cpt/maintenance/ShowDepreMethod.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */