/*    */ package weaver.cpt.maintenance;
/*    */ 
/*    */ import weaver.cache.CacheBase;
/*    */ import weaver.cache.CacheColumn;
/*    */ import weaver.cache.CacheColumnType;
/*    */ import weaver.cache.PKColumn;
/*    */ 
/*    */ 
/*    */ 
/*    */ public class DepreMethodComInfo
/*    */   extends CacheBase
/*    */ {
/* 13 */   protected static String TABLE_NAME = "CptDepreMethod1";
/*    */   
/* 15 */   protected static String TABLE_WHERE = null;
/*    */   
/* 17 */   protected static String TABLE_ORDER = "depretype ASC";
/*    */   
/*    */   @PKColumn(type = CacheColumnType.NUMBER)
/* 20 */   protected static String PK_NAME = "id";
/*    */ 
/*    */   
/*    */   @CacheColumn
/*    */   protected static int id;
/*    */ 
/*    */   
/*    */   @CacheColumn
/*    */   protected static int name;
/*    */ 
/*    */ 
/*    */   
/*    */   public int getDepreMethodNum() {
/* 33 */     return size();
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public boolean next() {
/* 41 */     return super.next();
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public boolean next(String paramString) {
/* 50 */     setTofirstRow();
/* 51 */     return false;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public void setTofirstRow() {
/* 58 */     super.setTofirstRow();
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getDepreMethodid() {
/* 66 */     return (String)getRowValue(id);
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getDepreMethodname() {
/* 74 */     return (String)getRowValue(name);
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getDepreMethodname(String paramString) {
/* 84 */     return (String)getValue(name, paramString);
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public void removeDepreMethodCache() {
/* 91 */     removeCache();
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/cpt/maintenance/DepreMethodComInfo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */