/*     */ package weaver.cpt.maintenance;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.Hashtable;
/*     */ import weaver.general.BaseBean;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CapitalAssortmentList
/*     */   extends BaseBean
/*     */ {
/*     */   private String selectedid;
/*  19 */   private int currentindex = -1;
/*  20 */   private int recordercount = 0;
/*     */   
/*     */   private ArrayList assortmentsteps;
/*     */   private ArrayList assortmentids;
/*     */   private ArrayList assortmentnames;
/*     */   private ArrayList assortmentimages;
/*     */   private ArrayList capitalcounts;
/*     */   private ArrayList subassortmentcounts;
/*     */   private ArrayList supassortmentids;
/*     */   private Hashtable allCapitalAssortmentInfo;
/*  30 */   private CapitalAssortmentComInfo cominfo = new CapitalAssortmentComInfo();
/*     */   
/*     */   public CapitalAssortmentList() {
/*  33 */     this.selectedid = "";
/*  34 */     this.assortmentsteps = new ArrayList();
/*  35 */     this.assortmentids = new ArrayList();
/*  36 */     this.assortmentnames = new ArrayList();
/*  37 */     this.assortmentimages = new ArrayList();
/*  38 */     this.subassortmentcounts = new ArrayList();
/*  39 */     this.supassortmentids = new ArrayList();
/*  40 */     this.capitalcounts = new ArrayList();
/*  41 */     this.allCapitalAssortmentInfo = new Hashtable<Object, Object>();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void initCapitalAssortmentList(String paramString) {
/*  49 */     this.selectedid = paramString;
/*  50 */     while (this.cominfo.next()) {
/*  51 */       String str1 = this.cominfo.getAssortmentId();
/*  52 */       String str2 = this.cominfo.getSupAssortmentId();
/*     */       
/*  54 */       ArrayList<String> arrayList = (this.allCapitalAssortmentInfo.get(str2) == null) ? new ArrayList() : (ArrayList)this.allCapitalAssortmentInfo.get(str2);
/*  55 */       arrayList.add(str1);
/*  56 */       this.allCapitalAssortmentInfo.put(str2, arrayList);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setCapitalAssortmentList(String paramString) {
/*  67 */     this.assortmentsteps.clear();
/*  68 */     this.assortmentids.clear();
/*  69 */     this.assortmentnames.clear();
/*  70 */     this.assortmentimages.clear();
/*  71 */     this.capitalcounts.clear();
/*  72 */     this.subassortmentcounts.clear();
/*  73 */     this.supassortmentids.clear();
/*  74 */     this.currentindex = -1;
/*  75 */     setCapitalAssortmentListInfo(paramString, 1);
/*  76 */     this.recordercount = this.assortmentids.size();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setCapitalAssortmentList2(String paramString) {
/*  85 */     this.assortmentsteps.clear();
/*  86 */     this.assortmentids.clear();
/*  87 */     this.assortmentnames.clear();
/*  88 */     this.assortmentimages.clear();
/*  89 */     this.capitalcounts.clear();
/*  90 */     this.subassortmentcounts.clear();
/*  91 */     this.supassortmentids.clear();
/*  92 */     this.currentindex = -1;
/*  93 */     setCapitalAssortmentListInfo2(paramString, 1);
/*  94 */     this.recordercount = this.assortmentids.size();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getAssortmentNum() {
/* 103 */     return this.recordercount;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean next() {
/* 117 */     if (this.currentindex + 1 < this.recordercount) {
/* 118 */       this.currentindex++;
/* 119 */       return true;
/*     */     } 
/*     */     
/* 122 */     this.currentindex = -1;
/* 123 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean back() {
/* 132 */     if (this.currentindex - 1 >= -1) {
/* 133 */       this.currentindex--;
/* 134 */       return true;
/*     */     } 
/*     */     
/* 137 */     this.currentindex = -1;
/* 138 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getAssortmentId() {
/* 147 */     return this.assortmentids.get(this.currentindex);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getAssortmentName() {
/* 155 */     return ((String)this.assortmentnames.get(this.currentindex)).trim();
/*     */   }
/*     */   
/*     */   public String getAssortmentStep() {
/* 159 */     return ((String)this.assortmentsteps.get(this.currentindex)).trim();
/*     */   }
/*     */   
/*     */   public String getAssortmentImage() {
/* 163 */     return ((String)this.assortmentimages.get(this.currentindex)).trim();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCapitalCount() {
/* 171 */     return ((String)this.capitalcounts.get(this.currentindex)).trim();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getSubAssortmentCount() {
/* 179 */     return ((String)this.subassortmentcounts.get(this.currentindex)).trim();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getSupAssortmentId() {
/* 187 */     return ((String)this.supassortmentids.get(this.currentindex)).trim();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void setCapitalAssortmentListInfo(String paramString, int paramInt) {
/* 196 */     boolean bool = false;
/*     */     
/* 198 */     if (!paramString.equals("0")) {
/* 199 */       this.assortmentsteps.add("" + paramInt);
/* 200 */       this.assortmentids.add(paramString);
/* 201 */       this.assortmentnames.add(this.cominfo.getAssortmentName(paramString));
/* 202 */       this.capitalcounts.add(this.cominfo.getCapitalCount(paramString));
/* 203 */       this.subassortmentcounts.add(this.cominfo.getSubAssortmentCount(paramString));
/* 204 */       this.supassortmentids.add(this.cominfo.getSupAssortmentId(paramString));
/* 205 */       if (this.cominfo.getSubAssortmentCount(paramString).equals("0")) { this.assortmentimages.add("0"); }
/*     */       
/* 207 */       else if (this.selectedid.indexOf(paramString + "|") != 0 && this.selectedid.indexOf("|" + paramString + "|") <= 0) { this.assortmentimages.add("1"); }
/*     */       else
/* 209 */       { this.assortmentimages.add("2");
/* 210 */         bool = true; }
/*     */     
/*     */     } 
/*     */ 
/*     */     
/* 215 */     ArrayList<String> arrayList = (ArrayList)this.allCapitalAssortmentInfo.get(paramString);
/*     */     
/* 217 */     if (arrayList != null) {
/* 218 */       for (byte b = 0; b < arrayList.size(); b++) {
/* 219 */         String str = arrayList.get(b);
/* 220 */         if (paramString.equals("0")) {
/* 221 */           setCapitalAssortmentListInfo(str, paramInt);
/*     */         }
/* 223 */         else if (bool) {
/*     */           
/* 225 */           setCapitalAssortmentListInfo(str, paramInt + 1);
/*     */         } 
/*     */       } 
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void setCapitalAssortmentListInfo2(String paramString, int paramInt) {
/* 237 */     boolean bool = false;
/*     */     
/* 239 */     if (!paramString.equals("0")) {
/* 240 */       this.assortmentsteps.add("" + paramInt);
/* 241 */       this.assortmentids.add(paramString);
/* 242 */       this.assortmentnames.add(this.cominfo.getAssortmentName(paramString));
/* 243 */       this.capitalcounts.add(this.cominfo.getCapitalCount(paramString));
/* 244 */       this.subassortmentcounts.add(this.cominfo.getSubAssortmentCount(paramString));
/* 245 */       this.supassortmentids.add(this.cominfo.getSupAssortmentId(paramString));
/* 246 */       if (!this.cominfo.getSubAssortmentCount(paramString).equals("0")) {
/* 247 */         bool = true;
/*     */       }
/*     */     } 
/*     */     
/* 251 */     ArrayList<String> arrayList = (ArrayList)this.allCapitalAssortmentInfo.get(paramString);
/*     */     
/* 253 */     if (arrayList != null)
/* 254 */       for (byte b = 0; b < arrayList.size(); b++) {
/* 255 */         String str = arrayList.get(b);
/* 256 */         if (paramString.equals("0")) {
/* 257 */           setCapitalAssortmentListInfo2(str, paramInt);
/*     */         }
/* 259 */         else if (bool) {
/*     */           
/* 261 */           setCapitalAssortmentListInfo2(str, paramInt + 1);
/*     */         } 
/*     */       }  
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/cpt/maintenance/CapitalAssortmentList.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */