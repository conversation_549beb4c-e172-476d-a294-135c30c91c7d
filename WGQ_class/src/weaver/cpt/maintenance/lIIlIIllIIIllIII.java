package weaver.cpt.maintenance;

import java.util.EmptyStackException;

class IllIII extends Calculate.IIllIl {
  IllIII(char paramChar, int paramInt) {
    super(paramChar, paramInt);
  }
  
  void _$1() {
    try {
      Double double_1 = Calculate.access$0().pop();
      Double double_2 = Calculate.access$0().pop();
      Calculate.access$0().push(new Double(Math.pow(double_2.doubleValue(), double_1.doubleValue())));
    } catch (EmptyStackException emptyStackException) {
      Calculate._$1 = -1;
    } 
  }
}


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/cpt/maintenance/lIIlIIllIIIllIII.class
 * Java compiler version: 1 (45.3)
 * JD-Core Version:       1.1.3
 */