/*     */ package weaver.cpt.maintenance;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import weaver.cache.CacheBase;
/*     */ import weaver.cache.CacheColumn;
/*     */ import weaver.cache.CacheColumnType;
/*     */ import weaver.cache.PKColumn;
/*     */ import weaver.common.util.xtree.TreeNode;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.cpt.util.CommonShareManager;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.HrmUserVarify;
/*     */ import weaver.hrm.User;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CapitalAssortmentComInfo
/*     */   extends CacheBase
/*     */ {
/*  28 */   protected static String TABLE_NAME = "CptCapitalAssortment";
/*     */   
/*  30 */   protected static String TABLE_WHERE = null;
/*     */   
/*  32 */   protected static String TABLE_ORDER = "id ASC";
/*     */   
/*     */   @PKColumn(type = CacheColumnType.NUMBER)
/*  35 */   protected static String PK_NAME = "id";
/*     */   
/*     */   @CacheColumn
/*     */   protected static int assortmentname;
/*     */   
/*     */   @CacheColumn
/*     */   protected static int assortmentmark;
/*     */   @CacheColumn
/*     */   protected static int supassortmentid;
/*     */   @CacheColumn
/*     */   protected static int supassortmentstr;
/*     */   @CacheColumn
/*     */   protected static int subassortmentcount;
/*     */   @CacheColumn
/*     */   protected static int assortmentremark;
/*     */   @CacheColumn
/*     */   protected static int capitalcount;
/*     */   @CacheColumn
/*     */   protected static int subcompanyid1;
/*  54 */   private CommonShareManager commonShareManager = new CommonShareManager();
/*     */   
/*     */   public String getCapitaldata2Count(String paramString1, User paramUser, String paramString2) {
/*  57 */     this.commonShareManager.setAliasTableName("t2");
/*  58 */     String str = "select COUNT(1) as cnt from cptcapital t1 where t1.isdata=2 and t1.capitalgroupid=" + paramString1 + " " + this.commonShareManager.getAssortmentSqlWhere(paramUser) + " ";
/*  59 */     if (!"".equals(Util.null2String(paramString2))) {
/*  60 */       str = str + paramString2;
/*     */     }
/*  62 */     RecordSet recordSet = new RecordSet();
/*     */     
/*  64 */     recordSet.executeSql(str);
/*  65 */     if (recordSet.next()) {
/*  66 */       return "" + Util.getIntValue(recordSet.getString("cnt"), 0);
/*     */     }
/*     */     
/*  69 */     return "0";
/*     */   }
/*     */   
/*     */   public String getCapitaldata2CountForDetachable(String paramString1, User paramUser, String paramString2, String paramString3) {
/*  73 */     RecordSet recordSet1 = new RecordSet();
/*  74 */     RecordSet recordSet2 = new RecordSet();
/*  75 */     int i = Util.getIntValue(paramString3, 0);
/*     */     
/*  77 */     recordSet1.execute("select cptdetachable from SystemSet");
/*  78 */     int j = 0;
/*  79 */     if (recordSet1.next()) {
/*  80 */       j = recordSet1.getInt("cptdetachable");
/*     */     }
/*  82 */     int k = paramUser.getUID();
/*  83 */     int m = paramUser.getUserSubCompany1();
/*  84 */     byte b = 2;
/*  85 */     String str1 = "";
/*  86 */     if (HrmUserVarify.checkUserRight("Capital:Maintenance", paramUser)) {
/*  87 */       str1 = "Capital:Maintenance";
/*     */     }
/*  89 */     String str2 = "";
/*  90 */     String str3 = "";
/*  91 */     ArrayList<String> arrayList = new ArrayList();
/*     */     
/*  93 */     if (j == 1 && k != 1) {
/*  94 */       String str = "";
/*  95 */       recordSet2.executeProc("HrmRoleSR_SeByURId", "" + k + b + str1);
/*  96 */       while (recordSet2.next()) {
/*  97 */         str2 = recordSet2.getString("subcompanyid");
/*  98 */         str = str + ", " + str2;
/*  99 */         arrayList.add(str2);
/*     */       } 
/* 101 */       if (!"".equals(str) && i == 0) {
/* 102 */         str = str.substring(1);
/* 103 */         str3 = str3 + " and blongsubcompany in (" + str + ") ";
/* 104 */       } else if (i > 0 && arrayList.contains(i + "")) {
/* 105 */         str3 = str3 + " and blongsubcompany in (" + i + ") ";
/*     */       } else {
/* 107 */         str3 = str3 + " and blongsubcompany in (99999) ";
/*     */       } 
/* 109 */     } else if (j == 1 && k == 1 && i > 0) {
/* 110 */       str3 = str3 + " and blongsubcompany in (" + i + ") ";
/*     */     } 
/* 112 */     this.commonShareManager.setAliasTableName("t2");
/* 113 */     String str4 = "select COUNT(1) as cnt from cptcapital t1 where t1.isdata=2 " + str3;
/* 114 */     if (!paramUser.getLoginid().equalsIgnoreCase("sysadmin")) {
/* 115 */       str4 = str4 + this.commonShareManager.getAssortmentSqlWhere(paramUser);
/*     */     }
/* 117 */     if (!"0".equals(paramString1)) {
/* 118 */       str4 = str4 + " and t1.capitalgroupid=" + paramString1;
/*     */     }
/* 120 */     if (!"".equals(Util.null2String(paramString2))) {
/* 121 */       str4 = str4 + paramString2;
/*     */     }
/* 123 */     recordSet1.execute(str4);
/* 124 */     if (recordSet1.next()) {
/* 125 */       return "" + Util.getIntValue(recordSet1.getString("cnt"), 0);
/*     */     }
/*     */     
/* 128 */     return "0";
/*     */   }
/*     */   
/* 131 */   private static int rootassortmentnum = 0;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getCapitalAssortmentNum() {
/* 139 */     return size();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setTofirstRow() {
/* 147 */     super.setTofirstRow();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean next() {
/* 155 */     return super.next();
/*     */   }
/*     */   
/*     */   public int getRootAssortmentNum() {
/* 159 */     CapitalAssortmentComInfo capitalAssortmentComInfo = new CapitalAssortmentComInfo();
/* 160 */     capitalAssortmentComInfo.setTofirstRow();
/* 161 */     while (capitalAssortmentComInfo.next()) {
/* 162 */       if (capitalAssortmentComInfo.getSupAssortmentId().equals("0")) {
/* 163 */         rootassortmentnum++;
/*     */       }
/*     */     } 
/*     */     
/* 167 */     return rootassortmentnum;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getAssortmentId() {
/* 176 */     return (String)getRowValue(0);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getAssortmentName() {
/* 184 */     return (String)getRowValue(assortmentname);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getAssortmentMark() {
/* 192 */     return (String)getRowValue(assortmentmark);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getSupAssortmentId() {
/* 200 */     return (String)getRowValue(supassortmentid);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getSupAssortmentStr() {
/* 208 */     return (String)getRowValue(supassortmentstr);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getSubAssortmentCount() {
/* 216 */     return (String)getRowValue(subassortmentcount);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getAssortmentRemark() {
/* 224 */     return (String)getRowValue(assortmentremark);
/*     */   }
/*     */   
/*     */   public String getCapitalData1Count(String paramString) {
/* 228 */     RecordSet recordSet = new RecordSet();
/* 229 */     String str = "select count(id) as countnum from cptcapital where isdata=1 and capitalgroupid=" + paramString;
/* 230 */     recordSet.executeSql(str);
/* 231 */     if (recordSet.next()) {
/* 232 */       String str1 = Util.getIntValue(recordSet.getString("countnum"), 0) + "";
/* 233 */       return str1.trim();
/*     */     } 
/* 235 */     return "0";
/*     */   }
/*     */   
/*     */   public String getCapitaldata1CountForDetachable(String paramString1, User paramUser, String paramString2) {
/* 239 */     RecordSet recordSet1 = new RecordSet();
/* 240 */     RecordSet recordSet2 = new RecordSet();
/* 241 */     int i = Util.getIntValue(paramString2, 0);
/*     */     
/* 243 */     recordSet1.execute("select cptdetachable from SystemSet");
/* 244 */     int j = 0;
/* 245 */     if (recordSet1.next()) {
/* 246 */       j = recordSet1.getInt("cptdetachable");
/*     */     }
/* 248 */     int k = paramUser.getUID();
/* 249 */     int m = paramUser.getUserSubCompany1();
/* 250 */     byte b = 2;
/* 251 */     String str1 = "";
/* 252 */     if (HrmUserVarify.checkUserRight("Capital:Maintenance", paramUser)) {
/* 253 */       str1 = "Capital:Maintenance";
/*     */     }
/* 255 */     String str2 = "";
/* 256 */     String str3 = "";
/* 257 */     ArrayList<String> arrayList = new ArrayList();
/* 258 */     if (j == 1 && k != 1) {
/* 259 */       String str = "";
/* 260 */       recordSet2.executeProc("HrmRoleSR_SeByURId", "" + k + b + str1);
/* 261 */       while (recordSet2.next()) {
/* 262 */         str2 = recordSet2.getString("subcompanyid");
/* 263 */         str = str + ", " + str2;
/* 264 */         arrayList.add(str2);
/*     */       } 
/* 266 */       if (!"".equals(str) && i == 0) {
/* 267 */         str = str.substring(1);
/* 268 */         str3 = str3 + " and blongsubcompany in (" + str + ") ";
/* 269 */       } else if (i > 0 && arrayList.contains(i + "")) {
/* 270 */         str3 = str3 + " and blongsubcompany in (" + i + ") ";
/*     */       } else {
/* 272 */         str3 = str3 + " and blongsubcompany in (99999) ";
/*     */       } 
/* 274 */     } else if (j == 1 && k == 1 && i > 0) {
/* 275 */       str3 = str3 + " and blongsubcompany in (" + i + ") ";
/*     */     } 
/* 277 */     String str4 = "select count(id) as countnum from cptcapital where isdata=1 " + str3;
/* 278 */     if (!"0".equals(paramString1)) {
/* 279 */       str4 = str4 + " and capitalgroupid=" + paramString1;
/*     */     }
/* 281 */     recordSet1.executeSql(str4);
/* 282 */     if (recordSet1.next()) {
/* 283 */       String str = Util.getIntValue(recordSet1.getString("countnum"), 0) + "";
/* 284 */       return str.trim();
/*     */     } 
/* 286 */     return "0";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCapitalCount() {
/* 294 */     RecordSet recordSet = new RecordSet();
/* 295 */     String str1 = "select count(id) as countnum from cptcapital where isdata=1 and capitalgroupid=" + getAssortmentId();
/* 296 */     recordSet.executeSql(str1);
/* 297 */     recordSet.next();
/* 298 */     String str2 = Util.getIntValue(recordSet.getString("countnum"), 0) + "";
/* 299 */     return str2.trim();
/*     */   }
/*     */ 
/*     */   
/*     */   public String getCapitalData2Count() {
/* 304 */     return getCapitalData2Count(getAssortmentId());
/*     */   }
/*     */   
/*     */   public String getCapitalData2Count(String paramString) {
/* 308 */     String str = "select t.isdata,COUNT(1) as cnt from cptcapital t where  t.capitalgroupid=" + paramString + " group by t.isdata ";
/* 309 */     RecordSet recordSet = new RecordSet();
/* 310 */     recordSet.executeSql(str);
/* 311 */     String[] arrayOfString = { "", "" };
/* 312 */     while (recordSet.next()) {
/* 313 */       String str1 = Util.null2String(recordSet.getString("isdata"));
/* 314 */       if ("1".equals(str1)) {
/* 315 */         arrayOfString[0] = Util.null2String(recordSet.getString("cnt")); continue;
/* 316 */       }  if ("2".equals(str1)) {
/* 317 */         arrayOfString[1] = Util.null2String(recordSet.getString("cnt"));
/*     */       }
/*     */     } 
/* 320 */     return arrayOfString[1];
/*     */   }
/*     */ 
/*     */   
/*     */   public String getSubcompanyid1() {
/* 325 */     return (String)getRowValue(subcompanyid1);
/*     */   }
/*     */   
/*     */   public String getSubcompanyid1(String paramString) {
/* 329 */     return "".equals(getValue(subcompanyid1, paramString)) ? "0" : (String)getValue(subcompanyid1, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getAssortmentName(String paramString) {
/* 341 */     return (String)getValue(assortmentname, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getAssortmentMark(String paramString) {
/* 351 */     return (String)getValue(assortmentmark, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getSupAssortmentId(String paramString) {
/* 361 */     return (String)getValue(supassortmentid, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getSupAssortmentStr(String paramString) {
/* 371 */     return (String)getValue(supassortmentstr, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getSubAssortmentCount(String paramString) {
/* 381 */     return (String)getValue(subassortmentcount, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getassortmentRemark(String paramString) {
/* 391 */     return (String)getValue(assortmentremark, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCapitalCount(String paramString) {
/* 401 */     return (String)getValue(capitalcount, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void removeCapitalAssortmentCache() {
/* 409 */     removeCache();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public TreeNode getTreeList(TreeNode paramTreeNode, String paramString1, int paramInt1, int paramInt2, String paramString2, String paramString3, String paramString4) throws Exception {
/* 425 */     int i = paramInt1;
/* 426 */     CapitalAssortmentComInfo capitalAssortmentComInfo = new CapitalAssortmentComInfo();
/* 427 */     capitalAssortmentComInfo.setTofirstRow();
/* 428 */     i++;
/* 429 */     while (capitalAssortmentComInfo.next() && i < paramInt2) {
/* 430 */       String str1 = capitalAssortmentComInfo.getSupAssortmentId();
/* 431 */       if (str1.equals(""))
/* 432 */         str1 = "0"; 
/* 433 */       if (!str1.equals(paramString1))
/*     */         continue; 
/* 435 */       String str2 = capitalAssortmentComInfo.getAssortmentId();
/* 436 */       String str3 = capitalAssortmentComInfo.getAssortmentName();
/* 437 */       String str4 = capitalAssortmentComInfo.getCapitalCount();
/* 438 */       TreeNode treeNode = new TreeNode();
/* 439 */       if ("y".equals(paramString4.toLowerCase()) && Integer.parseInt(str4) > 0) {
/* 440 */         treeNode.setTitle(str3 + " (" + str4 + ")");
/*     */       } else {
/* 442 */         treeNode.setTitle(str3);
/* 443 */       }  treeNode.setNodeId("com_" + str2);
/* 444 */       treeNode.setIcon("/images/treeimages/Home_wev8.gif");
/* 445 */       if ("checksingle".equals(paramString2.toLowerCase())) {
/* 446 */         if ("y".equals(paramString3.toLowerCase())) {
/* 447 */           if (!hasChild(str2)) {
/* 448 */             treeNode.setRadio("Y");
/* 449 */             treeNode.setOncheck("check('" + str2 + "','" + str3 + "')");
/*     */           } 
/*     */         } else {
/* 452 */           treeNode.setRadio("Y");
/* 453 */           treeNode.setOncheck("check('" + str2 + "','" + str3 + "')");
/*     */         } 
/*     */       }
/* 456 */       if (i == paramInt2 - 1 && 
/* 457 */         hasChild(str2)) {
/* 458 */         treeNode.setNodeXmlSrc("/cpt/maintenance/CptAssortmentTreeXML.jsp?id=" + str2 + "&checktype=" + paramString2 + "&onlyendnode=" + paramString3 + "&showcptcount=" + paramString4);
/*     */       }
/* 460 */       treeNode.setHref("javascript:onClick('" + str2 + "')");
/* 461 */       treeNode.setTarget("_self");
/* 462 */       paramTreeNode.addTreeNode(treeNode);
/* 463 */       getTreeList(treeNode, str2, i, paramInt2, paramString2, paramString3, paramString4);
/*     */     } 
/* 465 */     return paramTreeNode;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public TreeNode getTreeListById(TreeNode paramTreeNode, String paramString1, String paramString2, String paramString3, String paramString4) throws Exception {
/* 477 */     CapitalAssortmentComInfo capitalAssortmentComInfo = new CapitalAssortmentComInfo();
/* 478 */     ArrayList<String> arrayList = new ArrayList();
/* 479 */     String str = capitalAssortmentComInfo.getSupAssortmentId(paramString1);
/* 480 */     byte b = 0;
/* 481 */     while (!str.equals("0") && b < '✏') {
/* 482 */       arrayList.add(str);
/* 483 */       str = capitalAssortmentComInfo.getSupAssortmentId(str);
/* 484 */       if (str.equals("")) str = "0"; 
/* 485 */       b++;
/*     */     } 
/* 487 */     TreeNode treeNode = paramTreeNode;
/* 488 */     arrayList.add("0");
/* 489 */     for (int i = arrayList.size() - 1; i >= 0; i--) {
/* 490 */       capitalAssortmentComInfo.setTofirstRow();
/* 491 */       TreeNode treeNode1 = null;
/* 492 */       while (capitalAssortmentComInfo.next()) {
/* 493 */         String str1 = capitalAssortmentComInfo.getSupAssortmentId();
/* 494 */         if (str1.equals(""))
/* 495 */           str1 = "0"; 
/* 496 */         if (!str1.equals(arrayList.get(i)))
/* 497 */           continue;  String str2 = capitalAssortmentComInfo.getAssortmentId();
/* 498 */         String str3 = capitalAssortmentComInfo.getAssortmentName();
/* 499 */         String str4 = capitalAssortmentComInfo.getCapitalCount();
/* 500 */         TreeNode treeNode2 = new TreeNode();
/* 501 */         if ("y".equals(paramString4.toLowerCase()) && Integer.parseInt(str4) > 0) {
/* 502 */           treeNode2.setTitle(str3 + " (" + str4 + ")");
/*     */         } else {
/* 504 */           treeNode2.setTitle(str3);
/* 505 */         }  treeNode2.setNodeId("com_" + str2);
/* 506 */         treeNode2.setIcon("/images/treeimages/Home_wev8.gif");
/* 507 */         treeNode2.setHref("javascript:onClick('" + str2 + "')");
/* 508 */         treeNode2.setTarget("_self");
/* 509 */         if ("checksingle".equals(paramString2.toLowerCase())) {
/* 510 */           if ("y".equals(paramString3.toLowerCase())) {
/* 511 */             if (!hasChild(str2)) {
/* 512 */               treeNode2.setRadio("Y");
/* 513 */               treeNode2.setOncheck("check('" + str2 + "','" + str3 + "')");
/*     */             } 
/*     */           } else {
/* 516 */             treeNode2.setRadio("Y");
/* 517 */             treeNode2.setOncheck("check('" + str2 + "','" + str3 + "')");
/*     */           } 
/*     */         }
/* 520 */         if (hasChild(str2)) {
/* 521 */           treeNode2.setNodeXmlSrc("/cpt/maintenance/CptAssortmentTreeXML.jsp?id=" + str2 + "&checktype=" + paramString2 + "&onlyendnode=" + paramString3 + "&showcptcount=" + paramString4);
/*     */         }
/* 523 */         if (i > 0 && 
/* 524 */           str2.equals(arrayList.get(i - 1)))
/* 525 */           treeNode1 = treeNode2; 
/* 526 */         treeNode.addTreeNode(treeNode2);
/*     */       } 
/* 528 */       if (treeNode1 != null)
/* 529 */         treeNode = treeNode1; 
/*     */     } 
/* 531 */     return paramTreeNode;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private boolean hasChild(String paramString) throws Exception {
/* 539 */     CapitalAssortmentComInfo capitalAssortmentComInfo = new CapitalAssortmentComInfo();
/* 540 */     capitalAssortmentComInfo.setTofirstRow();
/* 541 */     while (capitalAssortmentComInfo.next()) {
/* 542 */       if (capitalAssortmentComInfo.getSupAssortmentId().equals(paramString)) return true; 
/*     */     } 
/* 544 */     return false;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/cpt/maintenance/CapitalAssortmentComInfo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */