/*     */ package weaver.cpt.maintenance;
/*     */ 
/*     */ import weaver.cache.CacheBase;
/*     */ import weaver.cache.CacheColumn;
/*     */ import weaver.cache.CacheColumnType;
/*     */ import weaver.cache.PKColumn;
/*     */ 
/*     */ 
/*     */ public class CapitalGroupComInfo
/*     */   extends CacheBase
/*     */ {
/*  12 */   protected static String TABLE_NAME = "CptCapitalGroup";
/*     */   
/*  14 */   protected static String TABLE_WHERE = null;
/*     */   
/*  16 */   protected static String TABLE_ORDER = "id ASC";
/*     */   
/*     */   @PKColumn(type = CacheColumnType.NUMBER)
/*  19 */   protected static String PK_NAME = "id";
/*     */ 
/*     */   
/*     */   @CacheColumn
/*     */   protected static int name;
/*     */ 
/*     */   
/*     */   @CacheColumn
/*     */   protected static int description;
/*     */   
/*     */   @CacheColumn
/*     */   protected static int parentid;
/*     */ 
/*     */   
/*     */   public int getCapitalGroupNum() {
/*  34 */     return size();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean next() {
/*  42 */     return super.next();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean next(String paramString) {
/*  51 */     setTofirstRow();
/*  52 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setTofirstRow() {
/*  59 */     super.setTofirstRow();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCapitalGroupid() {
/*  67 */     return (String)getRowValue(0);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCapitalGroupname() {
/*  75 */     return (String)getRowValue(name);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCapitalGroupname(String paramString) {
/*  85 */     return (String)getValue(name, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCapitalGroupdescription() {
/*  93 */     return (String)getRowValue(description);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCapitalGroupdescription(String paramString) {
/* 103 */     return (String)getValue(description, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCapitalGroupParentid() {
/* 111 */     return (String)getRowValue(parentid);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCapitalGroupParentid(String paramString) {
/* 121 */     return (String)getValue(parentid, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void removeCapitalGroupCache() {
/* 128 */     removeCache();
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/cpt/maintenance/CapitalGroupComInfo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */