/*      */ package weaver.cpt.ExcelToDB;
/*      */ import com.api.cpt.util.CptCommonUtil;
/*      */ import com.api.cpt.util.CptMaintenanceUtil;
/*      */ import com.engine.cpt.util.CapitalTransMethod;
/*      */ import com.engine.cpt.util.CptRightShareUitl;
/*      */ import com.weaver.formmodel.util.StringHelper;
/*      */ import java.io.FileInputStream;
/*      */ import java.math.BigDecimal;
/*      */ import java.text.DateFormat;
/*      */ import java.text.DecimalFormat;
/*      */ import java.text.SimpleDateFormat;
/*      */ import java.util.ArrayList;
/*      */ import java.util.Calendar;
/*      */ import java.util.HashMap;
/*      */ import java.util.HashSet;
/*      */ import java.util.Map;
/*      */ import java.util.Set;
/*      */ import java.util.Vector;
/*      */ import org.apache.poi.hssf.usermodel.HSSFCell;
/*      */ import org.apache.poi.hssf.usermodel.HSSFDateUtil;
/*      */ import org.apache.poi.hssf.usermodel.HSSFRow;
/*      */ import org.apache.poi.hssf.usermodel.HSSFSheet;
/*      */ import org.apache.poi.hssf.usermodel.HSSFWorkbook;
/*      */ import org.apache.poi.poifs.filesystem.POIFSFileSystem;
/*      */ import org.apache.poi.ss.usermodel.Cell;
/*      */ import org.apache.poi.ss.usermodel.CellType;
/*      */ import weaver.conn.ConnStatement;
/*      */ import weaver.conn.RecordSet;
/*      */ import weaver.cpt.capital.CapitalComInfo;
/*      */ import weaver.cpt.capital.CptShare;
/*      */ import weaver.cpt.maintenance.CapitalTypeComInfo;
/*      */ import weaver.cpt.util.CptFieldComInfo;
/*      */ import weaver.crm.Maint.CustomerInfoComInfo;
/*      */ import weaver.fna.maintenance.CurrencyComInfo;
/*      */ import weaver.formmode.browser.FormModeBrowserUtil;
/*      */ import weaver.general.TimeUtil;
/*      */ import weaver.general.Util;
/*      */ import weaver.hrm.company.DepartmentComInfo;
/*      */ import weaver.hrm.company.SubCompanyComInfo;
/*      */ import weaver.interfaces.workflow.browser.BaseBrowser;
/*      */ import weaver.lgc.maintenance.AssetUnitComInfo;
/*      */ import weaver.systeminfo.SysMaintenanceLog;
/*      */ import weaver.systeminfo.SystemEnv;
/*      */ import weaver.systeminfo.language.LanguageComInfo;
/*      */ import weaver.workflow.field.BrowserComInfo;
/*      */ 
/*      */ public class CapitalExcelToUP extends BaseBean {
/*   48 */   private Vector<String> msg1 = new Vector<>();
/*   49 */   private Vector<String> msg2 = new Vector<>();
/*   50 */   private Vector<String> msg3 = new Vector<>();
/*   51 */   private ArrayList<String> crmidlist = new ArrayList<>();
/*   52 */   private ArrayList<String> crmnamelist = new ArrayList<>();
/*      */   private String msgType;
/*      */   private String upCondition;
/*      */   private String conditionName;
/*   56 */   private int userLanguageid = 7;
/*   57 */   private CptCommonUtil cptCommonUtil = new CptCommonUtil();
/*      */   
/*   59 */   private HashMap<String, String> fullSubcompanyMap = null;
/*   60 */   private HashMap<String, String> fullDepartmentMap = null;
/*   61 */   private HashMap<String, String> fullHrmMap = null;
/*   62 */   private ArrayList<String> impFieldIds = null;
/*      */   
/*   64 */   private CptFieldComInfo CptFieldComInfo = new CptFieldComInfo();
/*   65 */   private CapitalComInfo capitalComInfo = new CapitalComInfo();
/*   66 */   private DepartmentComInfo departmentComInfo = new DepartmentComInfo();
/*      */   
/*      */   public Vector<String> getMsg1() {
/*   69 */     return this.msg1;
/*      */   }
/*      */   
/*      */   public Vector<String> getMsg2() {
/*   73 */     return this.msg2;
/*      */   }
/*      */   
/*      */   public Vector<String> getMsg3() {
/*   77 */     return this.msg3;
/*      */   }
/*      */   
/*      */   public String getMsgType() {
/*   81 */     return this.msgType;
/*      */   }
/*      */   
/*      */   public void setMsgType(String paramString) {
/*   85 */     this.msgType = paramString;
/*      */   }
/*      */   
/*   88 */   private int totalCount = 0;
/*   89 */   private int successCount = 0;
/*   90 */   private String logidTemp = "";
/*      */   
/*      */   public int getSuccessCount() {
/*   93 */     return this.successCount;
/*      */   }
/*      */   
/*      */   public void setLogdetailFlag(String paramString) {
/*   97 */     this.logidTemp = paramString;
/*      */   }
/*      */   
/*      */   public int getTotalCount() {
/*  101 */     return this.totalCount;
/*      */   }
/*      */   
/*      */   public void setTotalCount(int paramInt) {
/*  105 */     this.totalCount = paramInt;
/*      */   }
/*      */   
/*      */   public int getUserLanguageid() {
/*  109 */     return this.userLanguageid;
/*      */   }
/*      */   public void setUserLanguageid(int paramInt) {
/*  112 */     this.userLanguageid = paramInt;
/*      */   }
/*      */ 
/*      */   
/*      */   public void setUpCondition(String paramString) {
/*  117 */     this.upCondition = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private String getCellValue(HSSFCell paramHSSFCell, int paramInt, String paramString1, String paramString2) {
/*  129 */     String str = "";
/*      */     
/*  131 */     try { switch (paramHSSFCell.getCellType()) {
/*      */         case NUMERIC:
/*  133 */           if (HSSFDateUtil.isCellDateFormatted((Cell)paramHSSFCell)) {
/*  134 */             str = DateFormat.getDateInstance().format(paramHSSFCell.getDateCellValue()).toString().trim(); break;
/*      */           } 
/*  136 */           str = String.valueOf(paramHSSFCell.getNumericCellValue());
/*      */           break;
/*      */         
/*      */         case STRING:
/*  140 */           str = paramHSSFCell.getStringCellValue().trim();
/*  141 */           str = str.replaceAll("'", "＇");
/*      */           break;
/*      */         
/*      */         case FORMULA:
/*  145 */           str = DateFormat.getDateInstance().format(paramHSSFCell.getDateCellValue()).toString().trim();
/*      */           break;
/*      */       } 
/*      */       
/*      */        }
/*  150 */     catch (NullPointerException nullPointerException) {  }
/*  151 */     catch (Exception exception)
/*  152 */     { writeLog(exception);
/*  153 */       exception.printStackTrace(); }
/*      */     
/*  155 */     if (paramInt == 1 && str.equals("")) {
/*  156 */       this.msg1.add(paramString1);
/*  157 */       this.msg2.add(paramString2);
/*  158 */       this.msg3.add(SystemEnv.getHtmlLabelName(384148, this.userLanguageid));
/*      */     } 
/*  160 */     str = Util.null2String(str).trim();
/*  161 */     return str;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private String getDateCellValue(HSSFCell paramHSSFCell, int paramInt, String paramString1, String paramString2) {
/*  171 */     String str = ""; 
/*      */     try { double d;
/*  173 */       switch (paramHSSFCell.getCellType()) {
/*      */         case NUMERIC:
/*  175 */           if (HSSFDateUtil.isCellDateFormatted((Cell)paramHSSFCell)) {
/*  176 */             str = DateFormat.getDateInstance().format(paramHSSFCell.getDateCellValue()).toString().trim();
/*  177 */             str = TimeUtil.getDateString(TimeUtil.getString2Date(str, "yyyy-MM-dd")); break;
/*      */           } 
/*  179 */           d = paramHSSFCell.getNumericCellValue();
/*  180 */           str = TimeUtil.getDateString(HSSFDateUtil.getJavaDate(d));
/*      */           break;
/*      */         
/*      */         case STRING:
/*  184 */           str = paramHSSFCell.getStringCellValue().trim();
/*  185 */           str = TimeUtil.getDateString(TimeUtil.getString2Date(str, "yyyy-MM-dd"));
/*      */           break;
/*      */         case FORMULA:
/*  188 */           str = DateFormat.getDateInstance().format(paramHSSFCell.getDateCellValue()).toString().trim();
/*      */           break;
/*      */       } 
/*      */       
/*      */        }
/*  193 */     catch (NullPointerException nullPointerException) {  }
/*  194 */     catch (Exception exception)
/*  195 */     { writeLog(exception);
/*  196 */       exception.printStackTrace(); }
/*      */     
/*  198 */     if (paramInt == 1 && str.equals("")) {
/*  199 */       this.msg1.add(paramString1);
/*  200 */       this.msg2.add(paramString2);
/*  201 */       this.msg3.add(SystemEnv.getHtmlLabelName(384148, this.userLanguageid));
/*      */     } 
/*  203 */     return str;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void ExcelToDB(String paramString1, int paramInt1, int paramInt2, int paramInt3, String paramString2, String paramString3) {
/*  216 */     setUpCondition(paramString3);
/*  217 */     setUserLanguageid(paramInt3);
/*      */     try {
/*  219 */       FileInputStream fileInputStream = new FileInputStream(paramString1);
/*  220 */       POIFSFileSystem pOIFSFileSystem = new POIFSFileSystem(fileInputStream);
/*  221 */       HSSFWorkbook hSSFWorkbook = new HSSFWorkbook(pOIFSFileSystem);
/*  222 */       fileInputStream.close();
/*      */       
/*  224 */       if (!validExcelTitle(hSSFWorkbook, paramInt1, Integer.parseInt(paramString3))) {
/*      */         return;
/*      */       }
/*  227 */     } catch (Exception exception) {
/*  228 */       exception.printStackTrace();
/*      */       
/*      */       return;
/*      */     } 
/*  232 */     Calendar calendar1 = Calendar.getInstance();
/*      */ 
/*      */     
/*  235 */     String str1 = Util.add0(calendar1.get(1), 4) + "-" + Util.add0(calendar1.get(2) + 1, 2) + "-" + Util.add0(calendar1.get(5), 2);
/*  236 */     Calendar calendar2 = Calendar.getInstance();
/*      */ 
/*      */     
/*  239 */     String str2 = Util.add0(calendar2.getTime().getHours(), 2) + ":" + Util.add0(calendar2.getTime().getMinutes(), 2) + ":" + Util.add0(calendar2.getTime().getSeconds(), 2);
/*  240 */     getCrmInfo();
/*      */     
/*  242 */     this.fullSubcompanyMap = getSubMap();
/*  243 */     this.fullDepartmentMap = getDeptMap();
/*  244 */     this.fullHrmMap = getHrmMap();
/*  245 */     ExcelToDB1(paramString1, paramInt1, paramInt2, paramInt3, str1, str2, paramString2);
/*      */     
/*      */     try {
/*  248 */       CapitalComInfo capitalComInfo = new CapitalComInfo();
/*  249 */       capitalComInfo.removeCapitalCache();
/*  250 */     } catch (Exception exception) {
/*  251 */       writeLog(exception);
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private boolean validExcelTitle(HSSFWorkbook paramHSSFWorkbook, int paramInt1, int paramInt2) {
/*      */     try {
/*  262 */       CptFieldComInfo cptFieldComInfo = new CptFieldComInfo();
/*      */       
/*  264 */       int[][] arrayOfInt = { { 0, 0, 0 }, { 0, 518480, 518479 }, { 0, 82306, 518478 } };
/*  265 */       String str = SystemEnv.getHtmlLabelName(arrayOfInt[paramInt1][paramInt2], this.userLanguageid).trim().toLowerCase();
/*  266 */       this.conditionName = str;
/*      */       
/*  268 */       HSSFSheet hSSFSheet = paramHSSFWorkbook.getSheetAt(0);
/*  269 */       ArrayList<String> arrayList = cptFieldComInfo.getUpdateFieldList("" + paramInt1);
/*  270 */       arrayList.add("-1");
/*  271 */       this.impFieldIds = new ArrayList<>();
/*      */ 
/*      */       
/*  274 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  275 */       HashMap<Object, Object> hashMap2 = new HashMap<>();
/*  276 */       for (byte b1 = 0; b1 < arrayList.size(); b1++) {
/*  277 */         String str1 = Util.null2String(arrayList.get(b1));
/*  278 */         String str2 = cptFieldComInfo.getFieldhtmltype(str1);
/*  279 */         String str3 = cptFieldComInfo.getLabel(str1);
/*  280 */         String str4 = cptFieldComInfo.getFieldname(str1).toLowerCase();
/*      */ 
/*      */         
/*  283 */         if (paramInt1 == 1 && str4.equals("resourceid")) {
/*  284 */           str3 = "1507";
/*      */         }
/*  286 */         str3 = SystemEnv.getHtmlLabelName(Util.getIntValue(str3), this.userLanguageid).trim().toLowerCase();
/*  287 */         str3 = Util.formatMultiLang(str3, String.valueOf(this.userLanguageid)).toLowerCase();
/*      */         
/*  289 */         String str5 = str3 + "_" + str4;
/*      */         
/*  291 */         hashMap1.put(str1, str3);
/*  292 */         if (!str2.equals("7")) {
/*      */           Set<String> set;
/*      */           
/*  295 */           if (hashMap2.containsKey(str3)) {
/*  296 */             set = (Set)hashMap2.get(str3);
/*      */           } else {
/*  298 */             set = new HashSet();
/*      */           } 
/*  300 */           set.add(str1);
/*  301 */           hashMap2.put(str3, set);
/*      */           
/*  303 */           if (hashMap2.containsKey(str5)) {
/*  304 */             set = (Set<String>)hashMap2.get(str5);
/*      */           } else {
/*  306 */             set = new HashSet<>();
/*      */           } 
/*  308 */           set.add(str1);
/*  309 */           hashMap2.put(str5, set);
/*      */         } 
/*      */       } 
/*  312 */       HashMap<Object, Object> hashMap3 = new HashMap<>();
/*  313 */       HSSFRow hSSFRow = hSSFSheet.getRow(0);
/*  314 */       short s = hSSFRow.getLastCellNum();
/*  315 */       if (s < 2 || arrayList.size() + 1 < s) {
/*      */         
/*  317 */         this.msgType = "e6";
/*  318 */         return false;
/*      */       } 
/*  320 */       HashMap<Object, Object> hashMap4 = new HashMap<>();
/*  321 */       for (byte b2 = 0; b2 < s; b2++) {
/*      */         
/*  323 */         String str1 = Util.null2String(POIUtil.getValue((Sheet)hSSFSheet, 0, b2)).trim().toLowerCase();
/*  324 */         if (b2 == 0) {
/*  325 */           if (!str1.equals(str)) {
/*  326 */             this.msg1.add("1");
/*  327 */             this.msg2.add("" + (b2 + 1));
/*  328 */             this.msg3.add(SystemEnv.getHtmlLabelName(518537, this.userLanguageid) + str);
/*      */           } 
/*      */         } else {
/*      */           
/*  332 */           if (!hashMap2.containsKey(str1)) {
/*      */             
/*  334 */             String str3 = SystemEnv.getHtmlLabelName(520847, this.userLanguageid);
/*      */             
/*  336 */             str3 = str3.replace("{colIndex}", String.valueOf(b2 + 1)).replace("{content}", str1);
/*  337 */             this.msg1.add("1");
/*  338 */             this.msg2.add("");
/*  339 */             this.msg3.add(str3);
/*      */             
/*      */             break;
/*      */           } 
/*  343 */           Set<String> set = (Set)hashMap2.get(str1);
/*  344 */           if (set.size() > 1) {
/*      */             
/*  346 */             String str3 = SystemEnv.getHtmlLabelName(520848, this.userLanguageid);
/*      */ 
/*      */             
/*  349 */             str3 = str3.replace("{colIndex}", String.valueOf(b2 + 1)).replace("{content}", str1).replace("{fieldCount}", String.valueOf(set.size()));
/*  350 */             this.msg1.add("1");
/*  351 */             this.msg2.add("");
/*  352 */             this.msg3.add(str3);
/*      */             
/*      */             break;
/*      */           } 
/*  356 */           String str2 = set.iterator().next();
/*  357 */           if (!hashMap3.containsKey(str2)) {
/*  358 */             hashMap3.put(str2, str1);
/*      */           }
/*      */           
/*  361 */           if (hashMap4.containsKey(str2)) {
/*  362 */             int j = ((Integer)hashMap4.get(str2)).intValue();
/*      */             
/*  364 */             String str3 = SystemEnv.getHtmlLabelName(520849, this.userLanguageid);
/*      */ 
/*      */ 
/*      */             
/*  368 */             str3 = str3.replace("{colIndex1}", String.valueOf(j)).replace("{colName1}", (CharSequence)hashMap3.get(str2)).replace("{colIndex2}", String.valueOf(b2 + 1)).replace("{colName2}", str1);
/*      */             
/*  370 */             this.msg1.add("1");
/*  371 */             this.msg2.add("");
/*  372 */             this.msg3.add(str3);
/*      */             break;
/*      */           } 
/*  375 */           int i = b2 + 1;
/*  376 */           hashMap4.put(str2, Integer.valueOf(i));
/*      */           
/*  378 */           this.impFieldIds.add(str2);
/*      */         } 
/*      */       } 
/*  381 */     } catch (Exception exception) {
/*  382 */       exception.printStackTrace();
/*      */     } 
/*  384 */     if (this.msg3.size() > 0) {
/*  385 */       this.msgType = "e7";
/*  386 */       return false;
/*  387 */     }  return true;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private void ExcelToDB1(String paramString1, int paramInt1, int paramInt2, int paramInt3, String paramString2, String paramString3, String paramString4) {
/*  399 */     ConnStatement connStatement = new ConnStatement();
/*  400 */     RecordSet recordSet = new RecordSet();
/*      */     try {
/*  402 */       FileInputStream fileInputStream = new FileInputStream(paramString1);
/*  403 */       POIFSFileSystem pOIFSFileSystem = new POIFSFileSystem(fileInputStream);
/*  404 */       HSSFWorkbook hSSFWorkbook = new HSSFWorkbook(pOIFSFileSystem);
/*  405 */       HSSFSheet hSSFSheet = hSSFWorkbook.getSheetAt(0);
/*  406 */       fileInputStream.close();
/*      */       
/*  408 */       int i = hSSFSheet.getLastRowNum();
/*      */       
/*  410 */       boolean bool = validateData(hSSFWorkbook, paramInt1, paramInt3);
/*  411 */       if (!bool) {
/*      */         return;
/*      */       }
/*  414 */       this.totalCount = i;
/*  415 */       HSSFRow hSSFRow = null;
/*      */       
/*  417 */       HashMap<Object, Object> hashMap = new HashMap<>();
/*  418 */       RecordSet recordSet1 = new RecordSet();
/*      */       
/*  420 */       recordSet1.executeSql("select * from hrmsubcompany where canceled !=1 or canceled is null");
/*  421 */       while (recordSet1.next()) {
/*  422 */         hashMap.put(recordSet1.getString("subcompanyname"), recordSet1.getString("id"));
/*      */       }
/*      */       
/*  425 */       HashMap<String, String> hashMap1 = getBooleanMap();
/*  426 */       HashMap<String, HashMap<String, String>> hashMap2 = getCusBrowserMap();
/*  427 */       CapitalComInfo capitalComInfo = new CapitalComInfo();
/*  428 */       CapitalTransMethod capitalTransMethod = new CapitalTransMethod();
/*  429 */       CptShare cptShare = new CptShare();
/*      */       
/*  431 */       for (byte b = 1; b < i + 1; b++) {
/*  432 */         hSSFRow = hSSFSheet.getRow(b);
/*  433 */         if (hSSFRow == null)
/*  434 */           break;  String str1 = Util.null2String(getCellValue(hSSFRow.getCell(0), 0, (b + 1) + "", "1"));
/*      */         
/*  436 */         ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*      */         
/*  438 */         String str2 = "";
/*  439 */         ArrayList<String> arrayList1 = new ArrayList();
/*      */         
/*  441 */         if (this.impFieldIds != null && this.impFieldIds.size() > 0) {
/*  442 */           char c; for (c = '\001'; c <= this.impFieldIds.size(); c++) {
/*  443 */             HashMap<Object, Object> hashMap3 = new HashMap<>();
/*  444 */             String str3 = this.impFieldIds.get(c - 1);
/*  445 */             String str4 = this.CptFieldComInfo.getFieldname(str3);
/*  446 */             int j = Integer.parseInt(this.CptFieldComInfo.getFieldhtmltype(str3));
/*  447 */             String str5 = this.CptFieldComInfo.getFielddbtype(str3);
/*  448 */             int k = Integer.parseInt(this.CptFieldComInfo.getFieldType(str3));
/*  449 */             int m = Integer.parseInt(this.CptFieldComInfo.getLabel(str3));
/*      */             
/*  451 */             if (j != 6 && j != 7) {
/*      */ 
/*      */               
/*  454 */               String str = Util.null2String(getCellValue(hSSFRow.getCell((short)c), 0, (b + 1) + "", "" + (c + 1)));
/*      */               
/*  456 */               if (!"".equals(str)) {
/*      */ 
/*      */ 
/*      */                 
/*  460 */                 if (!"null".equalsIgnoreCase(str)) {
/*      */                   
/*  462 */                   if ("isinner".equals(str4)) {
/*  463 */                     if (str.equals(SystemEnv.getHtmlLabelName(15298, paramInt3))) {
/*  464 */                       str = "0";
/*  465 */                     } else if (str.equals(SystemEnv.getHtmlLabelName(15299, paramInt3))) {
/*  466 */                       str = "1";
/*      */                     } 
/*  468 */                   } else if ("attribute".equals(str4)) {
/*  469 */                     if (str.equals(SystemEnv.getHtmlLabelName(1366, paramInt3))) {
/*  470 */                       str = "0";
/*  471 */                     } else if (str.equals(SystemEnv.getHtmlLabelName(1368, paramInt3))) {
/*  472 */                       str = "2";
/*  473 */                     } else if (str.equals(SystemEnv.getHtmlLabelName(1369, paramInt3))) {
/*  474 */                       str = "3";
/*  475 */                     } else if (str.equals(SystemEnv.getHtmlLabelName(60, paramInt3))) {
/*  476 */                       str = "4";
/*  477 */                     } else if (str.equals(SystemEnv.getHtmlLabelName(1370, paramInt3))) {
/*  478 */                       str = "5";
/*  479 */                     } else if (str.equals(SystemEnv.getHtmlLabelName(811, paramInt3))) {
/*  480 */                       str = "6";
/*      */                     } else {
/*  482 */                       str = "1";
/*      */                     } 
/*  484 */                   } else if (j == 1) {
/*  485 */                     if (k == 2) {
/*  486 */                       if (!"".equals(str)) {
/*  487 */                         str = (int)Util.getDoubleValue(str, 0.0D) + "";
/*      */                       }
/*  489 */                     } else if ((k == 3 || k == 4 || k == 5) && 
/*  490 */                       !"".equals(str)) {
/*  491 */                       str = (new BigDecimal(str)).toString();
/*      */                     }
/*      */                   
/*  494 */                   } else if (j == 3 && k != 2 && k != 19 && k != 402 && k != 403) {
/*  495 */                     if (str5.startsWith("int")) {
/*  496 */                       if (hashMap2.containsKey("" + k)) {
/*  497 */                         if (Util.getDoubleValue(str, 0.0D) > 0.0D) {
/*  498 */                           str = "" + (int)Util.getDoubleValue(str, 0.0D);
/*      */                         }
/*  500 */                         str = "" + Util.getIntValue((String)((HashMap)hashMap2.get("" + k)).get(str), 0);
/*      */                       } 
/*  502 */                     } else if (str5.startsWith("browser")) {
/*  503 */                       if (hashMap2.containsKey(str5)) {
/*  504 */                         String[] arrayOfString = Util.TokenizerString2(str, ",");
/*  505 */                         String str6 = "";
/*  506 */                         for (String str7 : arrayOfString) {
/*  507 */                           String str8 = Util.null2String((String)((HashMap)hashMap2.get(str5)).get(str7), "");
/*  508 */                           if (!"".equals(str8)) {
/*  509 */                             str6 = str6 + "" + str8 + ",";
/*      */                           }
/*      */                         } 
/*  512 */                         if (!"".equals(str6)) {
/*  513 */                           str = str6.substring(0, str6.length() - 1);
/*      */                         }
/*      */                       }
/*      */                     
/*  517 */                     } else if (hashMap2.containsKey("" + k)) {
/*  518 */                       String[] arrayOfString = Util.TokenizerString2(str, ",");
/*  519 */                       String str6 = "";
/*  520 */                       for (String str7 : arrayOfString) {
/*  521 */                         String str8 = Util.null2String((String)((HashMap)hashMap2.get(str5)).get(str7), "");
/*  522 */                         if (!"".equals(str8)) {
/*  523 */                           str6 = str6 + "" + str8 + ",";
/*      */                         }
/*      */                       } 
/*  526 */                       if (!"".equals(str6)) {
/*  527 */                         str = str6.substring(0, str6.length() - 1);
/*      */                       }
/*      */                     }
/*      */                   
/*  531 */                   } else if (j == 3 && k == 19) {
/*  532 */                     if (str != "") {
/*  533 */                       str = checkTime(str, b + 1, c + 1, false);
/*      */                     }
/*  535 */                   } else if (j == 3 && (k == 2 || k == 402 || k == 403)) {
/*  536 */                     if (!"".equals(str)) {
/*  537 */                       str = checkDate(str, b + 1, c + 1, false, k);
/*      */                     }
/*  539 */                   } else if (j == 4) {
/*  540 */                     if (hashMap1.containsKey(str)) {
/*  541 */                       str = hashMap1.get(str);
/*      */                     }
/*  543 */                   } else if (j == 5) {
/*      */                     
/*  545 */                     String str6 = "";
/*  546 */                     String str7 = "";
/*  547 */                     recordSet.executeQuery("select iscommon,cid from cptDefineField where fieldname=?", new Object[] { str4 });
/*  548 */                     if (recordSet.next()) {
/*  549 */                       str6 = Util.null2String(recordSet.getString("iscommon"));
/*  550 */                       str7 = Util.null2String(recordSet.getString("cid"));
/*      */                     } 
/*  552 */                     if (str6.equals("1")) {
/*  553 */                       recordSet.executeQuery("select name from mode_selectitempagedetail where statelev =1 and cancel <> 1 and mainid=? order by id asc", new Object[] { str7 });
/*  554 */                       byte b1 = 0;
/*  555 */                       while (recordSet.next()) {
/*  556 */                         String str8 = Util.formatMultiLang(Util.null2String(recordSet.getString("name")), this.userLanguageid + "");
/*  557 */                         if (str8.equals(str)) {
/*  558 */                           str = b1 + "";
/*      */                           break;
/*      */                         } 
/*  561 */                         b1++;
/*      */                       } 
/*      */                     } else {
/*      */                       
/*  565 */                       recordSet.executeSql("select selectvalue from cpt_selectitem where fieldid=" + str3 + " and selectname='" + str + "' ");
/*  566 */                       recordSet.next();
/*  567 */                       str = Util.null2String(recordSet.getString("selectvalue"));
/*      */                     } 
/*      */                   } 
/*      */                 } else {
/*      */                   
/*  572 */                   str = "";
/*      */                 } 
/*      */                 
/*  575 */                 hashMap3.put("fieldid", str3);
/*  576 */                 hashMap3.put("fieldname", str4);
/*  577 */                 hashMap3.put("fieldvalue", str);
/*  578 */                 hashMap3.put("fieldlabel", "" + m);
/*  579 */                 hashMap3.put("type", "" + k);
/*  580 */                 hashMap3.put("fieldhtmltype", "" + j);
/*  581 */                 arrayList.add(hashMap3);
/*      */                 
/*  583 */                 if ("oracle".equalsIgnoreCase(recordSet1.getDBType())) {
/*  584 */                   if (str5.toUpperCase().indexOf("INT") >= 0) {
/*  585 */                     if ("5".equals(Integer.valueOf(j))) {
/*  586 */                       if (!Util.null2String(str).equals("")) {
/*  587 */                         str2 = str2 + str4 + " = " + Util.getIntValue(str) + ",";
/*      */                       } else {
/*  589 */                         str2 = str2 + str4 + " = NULL,";
/*      */                       }
/*      */                     
/*  592 */                     } else if (!Util.null2String(str).equals("")) {
/*  593 */                       str2 = str2 + str4 + " = " + Util.getIntValue(str) + ",";
/*      */                     } else {
/*  595 */                       str2 = str2 + str4 + " = NULL,";
/*      */                     }
/*      */                   
/*  598 */                   } else if (str5.toUpperCase().indexOf("NUMBER") >= 0 || str5.toUpperCase().indexOf("FLOAT") >= 0 || str5.toUpperCase().indexOf("DECIMAL") >= 0) {
/*  599 */                     int n = str5.indexOf(",");
/*  600 */                     int i1 = 2;
/*  601 */                     if (n > -1) {
/*  602 */                       i1 = Util.getIntValue(str5.substring(n + 1, str5.length() - 1).trim(), 2);
/*      */                     } else {
/*  604 */                       i1 = 2;
/*      */                     } 
/*  606 */                     if (!Util.null2String(str).equals("")) {
/*  607 */                       str2 = str2 + str4 + " = " + Util.getPointValue2(str, i1) + ",";
/*      */                     } else {
/*      */                       
/*  610 */                       str2 = str2 + str4 + " = NULL,";
/*      */                     } 
/*  612 */                   } else if (j != 6) {
/*      */ 
/*      */                     
/*  615 */                     String str6 = "";
/*  616 */                     if (j == 3 && (k == 161 || k == 162)) {
/*  617 */                       str6 = Util.null2String(str);
/*  618 */                       str6 = str6.trim();
/*      */                     }
/*  620 */                     else if (j == 2 && k == 2) {
/*  621 */                       str6 = Util.toHtml100(str);
/*  622 */                       str6 = StringHelper.convertSpecialChar2Html(str6);
/*  623 */                     } else if (j == 1 && k == 1) {
/*  624 */                       str6 = StringHelper.convertSpecialChar2Html(str);
/*  625 */                       str6 = Util.toHtmlForWorkflow(str6);
/*  626 */                     } else if (j == 2 && k == 1) {
/*  627 */                       str6 = Util.StringReplace(str, " ", "&nbsp;");
/*  628 */                       str6 = StringHelper.convertSpecialChar2Html(str6);
/*  629 */                       str6 = Util.toHtmlForWorkflowForMode(str6);
/*      */                     } else {
/*  631 */                       str6 = Util.StringReplace(Util.toHtml10(str), " ", "&nbsp;");
/*  632 */                       str6 = StringHelper.convertSpecialChar2Html(str6);
/*  633 */                       str6 = Util.toHtmlForWorkflow(str6);
/*      */                     } 
/*      */                     
/*  636 */                     str6 = Util.StringReplace(str6, "weaver2017", "+");
/*      */                     
/*  638 */                     if (2 == j || FormModeBrowserUtil.isMultiBrowser("" + j, "" + k)) {
/*  639 */                       str2 = str2 + str4 + " = ?,";
/*  640 */                       arrayList1.add(str6);
/*      */                     } else {
/*  642 */                       str2 = str2 + str4 + " = '" + str6 + "',";
/*      */                     }
/*      */                   
/*      */                   } 
/*  646 */                 } else if (str5.toUpperCase().indexOf("INT") >= 0) {
/*  647 */                   if ("5".equals(Integer.valueOf(j))) {
/*  648 */                     if (!"".equals(str)) {
/*  649 */                       str2 = str2 + str4 + " = " + Util.getIntValue(str) + ",";
/*      */                     } else {
/*  651 */                       str2 = str2 + str4 + " = NULL,";
/*      */                     }
/*      */                   
/*  654 */                   } else if (!"".equals(str)) {
/*  655 */                     str2 = str2 + str4 + " = " + Util.getIntValue(str) + ",";
/*      */                   } else {
/*  657 */                     str2 = str2 + str4 + " = NULL,";
/*      */                   }
/*      */                 
/*  660 */                 } else if (str5.toUpperCase().indexOf("DECIMAL") >= 0 || str5.toUpperCase().indexOf("FLOAT") >= 0) {
/*  661 */                   int n = str5.indexOf(",");
/*  662 */                   int i1 = 2;
/*  663 */                   if (n > -1) {
/*  664 */                     i1 = Util.getIntValue(str5.substring(n + 1, str5.length() - 1).trim(), 2);
/*      */                   } else {
/*  666 */                     i1 = 2;
/*      */                   } 
/*  668 */                   if (!"".equals(str)) {
/*  669 */                     str2 = str2 + str4 + " = " + Util.getPointValue2(str, i1) + ",";
/*      */                   } else {
/*  671 */                     str2 = str2 + str4 + " = NULL,";
/*      */                   } 
/*  673 */                 } else if (j != 6) {
/*      */ 
/*      */                   
/*  676 */                   String str6 = "";
/*  677 */                   if (j == 2 && k == 2) {
/*  678 */                     str6 = StringHelper.convertSpecialChar2Html(str);
/*  679 */                   } else if (j == 1 && k == 1) {
/*  680 */                     str6 = StringHelper.convertSpecialChar2Html(str);
/*  681 */                     str6 = Util.toHtmlForWorkflow(str6);
/*  682 */                   } else if (j == 2 && k == 1) {
/*  683 */                     str6 = Util.StringReplace(str, " ", "&nbsp;");
/*  684 */                     str6 = StringHelper.convertSpecialChar2Html(str6);
/*  685 */                     str6 = Util.toHtmlForWorkflowForMode(str6);
/*  686 */                   } else if (j == 4 && k == 1) {
/*  687 */                     str6 = Util.StringReplace(str, " ", "&nbsp;");
/*  688 */                     str6 = Util.toHtmlForWorkflow(str6);
/*  689 */                     if (str6.equals("")) {
/*  690 */                       str6 = "0";
/*      */                     }
/*      */                   } else {
/*  693 */                     str6 = Util.StringReplace(Util.toHtml10(str), " ", "&nbsp;");
/*  694 */                     str6 = StringHelper.convertSpecialChar2Html(str6);
/*  695 */                     str6 = Util.toHtmlForWorkflow(str6);
/*      */                   } 
/*  697 */                   str6 = Util.StringReplace(str6, "weaver2017", "+");
/*      */                   
/*  699 */                   if (2 == j || FormModeBrowserUtil.isMultiBrowser("" + j, "" + k)) {
/*  700 */                     str2 = str2 + str4 + " = ?,";
/*  701 */                     arrayList1.add(str6);
/*      */                   } else {
/*  703 */                     str2 = str2 + str4 + " = '" + str6 + "',";
/*      */                   } 
/*      */                 } 
/*      */               } 
/*      */             } 
/*  708 */           }  c = Util.getSeparator();
/*  709 */           CurrencyComInfo currencyComInfo = new CurrencyComInfo();
/*  710 */           CapitalTypeComInfo capitalTypeComInfo = new CapitalTypeComInfo();
/*  711 */           AssetUnitComInfo assetUnitComInfo = new AssetUnitComInfo();
/*  712 */           CustomerInfoComInfo customerInfoComInfo = new CustomerInfoComInfo();
/*  713 */           SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/*  714 */           DepartmentComInfo departmentComInfo = this.departmentComInfo;
/*  715 */           if (!str2.equals("")) {
/*  716 */             RecordSet recordSet2 = new RecordSet();
/*  717 */             String str3 = "";
/*  718 */             if (this.upCondition.equals("1")) {
/*  719 */               str3 = "select * from cptcapital where mark = '" + str1 + "' and isdata=" + paramInt1;
/*      */             } else {
/*  721 */               str3 = "select * from cptcapital where id = '" + str1 + "'";
/*      */             } 
/*  723 */             recordSet2.execute(str3);
/*  724 */             recordSet2.next();
/*  725 */             String str4 = recordSet2.getString("id");
/*  726 */             String str5 = "";
/*  727 */             str5 = str2.substring(0, str2.length() - 1);
/*  728 */             str5 = " update cptcapital set  " + str5 + " where id = " + str4;
/*  729 */             Object[] arrayOfObject = new Object[arrayList1.size()];
/*  730 */             for (byte b1 = 0; b1 < arrayList1.size(); b1++) {
/*  731 */               arrayOfObject[b1] = arrayList1.get(b1);
/*      */             }
/*      */ 
/*      */             
/*  735 */             String str6 = "";
/*  736 */             for (Map<Object, Object> map : arrayList) {
/*  737 */               String str9 = Util.null2String((String)map.get("fieldname"));
/*  738 */               String str10 = Util.null2String((String)map.get("fieldvalue"));
/*  739 */               String str11 = Util.null2String((String)map.get("fieldid"));
/*  740 */               String str12 = Util.null2String((String)map.get("type"));
/*  741 */               String str13 = Util.null2String((String)map.get("fieldhtmltype"));
/*  742 */               String str14 = recordSet2.getString("cptcapital", str9, true, true);
/*      */               
/*  744 */               if (!recordSet2.getString("sptcount").equals("1") && ("depreyear".equals(str9) || "deprerate".equals(str9))) {
/*      */                 continue;
/*      */               }
/*      */               
/*  748 */               if (!str14.equals(str10)) {
/*      */                 
/*  750 */                 if ("currencyid".equals(str9)) {
/*  751 */                   str14 = Util.toScreen(currencyComInfo.getCurrencyname(str14), paramInt3);
/*  752 */                   str10 = Util.toScreen(currencyComInfo.getCurrencyname(str10), paramInt3);
/*  753 */                 } else if ("capitaltypeid".equals(str9)) {
/*  754 */                   str14 = Util.toScreen(capitalTypeComInfo.getCapitalTypename(str14), paramInt3);
/*  755 */                   str10 = Util.toScreen(capitalTypeComInfo.getCapitalTypename(str10), paramInt3);
/*  756 */                 } else if ("unitid".equals(str9)) {
/*  757 */                   str14 = Util.toScreen(assetUnitComInfo.getAssetUnitname(str14), paramInt3);
/*  758 */                   str10 = Util.toScreen(assetUnitComInfo.getAssetUnitname(str10), paramInt3);
/*  759 */                 } else if ("replacecapitalid".equals(str9)) {
/*  760 */                   str14 = Util.toScreen(capitalComInfo.getCapitalname(str14), paramInt3);
/*  761 */                   str10 = Util.toScreen(capitalComInfo.getCapitalname(str10), paramInt3);
/*  762 */                 } else if ("customerid".equals(str9)) {
/*  763 */                   str14 = Util.toScreen(customerInfoComInfo.getCustomerInfoname(str14), paramInt3);
/*  764 */                   str10 = Util.toScreen(customerInfoComInfo.getCustomerInfoname(str10), paramInt3);
/*  765 */                 } else if ("blongsubcompany".equals(str9)) {
/*  766 */                   str14 = Util.toScreen(subCompanyComInfo.getSubCompanyname(str14), paramInt3);
/*  767 */                   str10 = Util.toScreen(subCompanyComInfo.getSubCompanyname(str10), paramInt3);
/*  768 */                 } else if ("blongdepartment".equals(str9)) {
/*      */                   
/*  770 */                   String str15 = this.departmentComInfo.getSubcompanyid1(str10);
/*  771 */                   String str16 = recordSet2.getString("blongsubcompany");
/*  772 */                   if (!str16.equals(str15)) {
/*  773 */                     str6 = str4;
/*  774 */                     str6 = str6 + c + "blongsubcompany";
/*  775 */                     str6 = str6 + c + Util.toScreen(subCompanyComInfo.getSubCompanyname(str16), paramInt3);
/*  776 */                     str6 = str6 + c + Util.toScreen(subCompanyComInfo.getSubCompanyname(str15), paramInt3);
/*  777 */                     str6 = str6 + c + "" + paramInt2;
/*  778 */                     str6 = str6 + c + paramString2;
/*  779 */                     recordSet1.executeProc("CptCapitalModify_Insert", str6);
/*  780 */                     recordSet1.executeUpdate("update cptcapital  set blongsubcompany = ? where id = ?", new Object[] { str15, str4 });
/*      */                   } 
/*      */                   
/*  783 */                   str14 = Util.toScreen(departmentComInfo.getDepartmentname(str14), paramInt3);
/*  784 */                   str10 = Util.toScreen(departmentComInfo.getDepartmentname(str10), paramInt3);
/*      */                 }
/*  786 */                 else if ("isinner".equals(str9)) {
/*  787 */                   String str15 = "";
/*  788 */                   String str16 = "";
/*  789 */                   if (str14.equals("0")) {
/*  790 */                     str15 = SystemEnv.getHtmlLabelName(15298, paramInt3);
/*  791 */                   } else if (str14.equals("1")) {
/*  792 */                     str15 = SystemEnv.getHtmlLabelName(15299, paramInt3);
/*      */                   } 
/*  794 */                   if (str10.equals("0")) {
/*  795 */                     str16 = SystemEnv.getHtmlLabelName(15298, paramInt3);
/*  796 */                   } else if (str10.equals("1")) {
/*  797 */                     str16 = SystemEnv.getHtmlLabelName(15299, paramInt3);
/*  798 */                   }  str14 = str15;
/*  799 */                   str10 = str16;
/*  800 */                 } else if ("attribute".equals(str9)) {
/*  801 */                   str14 = CptMaintenanceUtil.getAttributeName(str14);
/*  802 */                   str10 = CptMaintenanceUtil.getAttributeName(str10);
/*      */                 } else {
/*  804 */                   if ("warehouse".equals(str9)) {
/*      */                     
/*  806 */                     capitalTransMethod.saveWareHouse(Integer.parseInt(str4), str10);
/*  807 */                     cptShare.freshenCptShareByWareHouse(str4 + "");
/*      */                   } 
/*  809 */                   str14 = capitalTransMethod.getFieldValueByType(str14, Util.getIntValue(this.CptFieldComInfo.getFieldhtmltype(str11)), Util.getIntValue(this.CptFieldComInfo.getFieldType(str11)), this.CptFieldComInfo.getFielddbtype(str11), str11);
/*  810 */                   str10 = capitalTransMethod.getFieldValueByType(str10, Util.getIntValue(this.CptFieldComInfo.getFieldhtmltype(str11)), Util.getIntValue(this.CptFieldComInfo.getFieldType(str11)), this.CptFieldComInfo.getFielddbtype(str11), str11);
/*      */                 } 
/*  812 */                 if (str13.equals("2") && str12.equals("2") && 
/*  813 */                   str10.endsWith("\n") && str10.substring(0, str10.lastIndexOf("\n")).equals(str14)) {
/*      */                   continue;
/*      */                 }
/*      */ 
/*      */                 
/*  818 */                 str6 = str4;
/*  819 */                 str6 = str6 + c + str9;
/*  820 */                 str6 = str6 + c + str14;
/*  821 */                 str6 = str6 + c + str10;
/*  822 */                 str6 = str6 + c + "" + paramInt2;
/*  823 */                 str6 = str6 + c + paramString2;
/*  824 */                 recordSet1.executeProc("CptCapitalModify_Insert", str6);
/*      */               } 
/*      */             } 
/*  827 */             recordSet1.executeUpdate(str5, arrayOfObject);
/*      */             
/*  829 */             if (this.upCondition.equals("1")) {
/*  830 */               recordSet1.executeQuery("select name from cptcapital where mark = '" + str1 + "'", new Object[0]);
/*      */             } else {
/*  832 */               recordSet1.executeQuery("select name from cptcapital where id   = '" + str1 + "'", new Object[0]);
/*      */             } 
/*  834 */             recordSet1.next();
/*  835 */             saveLogDetail(str4, recordSet1.getString("name"), recordSet2.getString("mark"), "0.0", recordSet2.getString("sptcount"), recordSet2.getString("stateid"));
/*      */ 
/*      */             
/*  838 */             recordSet1.execute("select name from cptcapital where id=" + str4);
/*  839 */             recordSet1.next();
/*  840 */             String str7 = recordSet1.getString("name");
/*  841 */             String str8 = Util.null2String(this.cptCommonUtil.getPinYin(str7, 7).get("pinyin"));
/*  842 */             recordSet1.execute("update cptcapital set ecology_pinyin_search='" + str8 + "' where id=" + str4);
/*      */             
/*  844 */             capitalComInfo.updateCapitalCache(str4);
/*      */             
/*  846 */             if (paramInt1 == 1) {
/*  847 */               SysMaintenanceLog sysMaintenanceLog = new SysMaintenanceLog();
/*  848 */               sysMaintenanceLog.resetParameter();
/*  849 */               sysMaintenanceLog.setRelatedName(recordSet1.getString("name"));
/*  850 */               sysMaintenanceLog.setOperateUserid(paramInt2);
/*  851 */               sysMaintenanceLog.setOperateItem("51");
/*  852 */               sysMaintenanceLog.setClientAddress(paramString4);
/*  853 */               sysMaintenanceLog.setRelatedId(Util.getIntValue(str4));
/*  854 */               sysMaintenanceLog.setOperateType("2");
/*  855 */               sysMaintenanceLog.setOperateDesc("CptCapital_ImpUpdate,");
/*  856 */               sysMaintenanceLog.setSysLogInfo();
/*      */             } else {
/*  858 */               CptRightShareUitl.editCapitalResetRight(str4);
/*      */             } 
/*      */           } 
/*      */         } 
/*  862 */         this.successCount++;
/*      */       } 
/*  864 */     } catch (Exception exception) {
/*  865 */       this.msgType = "e1";
/*  866 */       writeLog(exception);
/*  867 */       exception.printStackTrace();
/*      */     } finally {
/*  869 */       connStatement.close();
/*      */     } 
/*      */   }
/*      */   
/*      */   private boolean isDate(String paramString) {
/*      */     try {
/*  875 */       SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
/*  876 */       simpleDateFormat.parse(paramString);
/*  877 */     } catch (Exception exception) {
/*  878 */       return false;
/*      */     } 
/*  880 */     return true;
/*      */   }
/*      */ 
/*      */   
/*      */   private String getCellValue(HSSFCell paramHSSFCell, int paramInt1, int paramInt2, int paramInt3) {
/*  885 */     String str = "";
/*  886 */     if (paramHSSFCell == null && paramInt1 == 1) {
/*  887 */       this.msg1.add("" + paramInt2);
/*  888 */       this.msg2.add("" + paramInt3);
/*  889 */       this.msg3.add(" ");
/*  890 */       return "";
/*  891 */     }  if (paramHSSFCell == null)
/*  892 */       return ""; 
/*      */     try {
/*      */       BigDecimal bigDecimal;
/*  895 */       switch (paramHSSFCell.getCellType()) {
/*      */         case NUMERIC:
/*  897 */           if (HSSFDateUtil.isCellDateFormatted((Cell)paramHSSFCell)) {
/*  898 */             str = DateFormat.getDateInstance().format(paramHSSFCell.getDateCellValue()).toString().trim(); break;
/*      */           } 
/*  900 */           str = String.valueOf(paramHSSFCell.getNumericCellValue());
/*  901 */           bigDecimal = new BigDecimal(str);
/*  902 */           str = bigDecimal.toPlainString();
/*      */           break;
/*      */ 
/*      */         
/*      */         case STRING:
/*  907 */           str = paramHSSFCell.getStringCellValue().trim();
/*  908 */           str = str.replaceAll("'", "＇");
/*      */           break;
/*      */         case FORMULA:
/*  911 */           str = DateFormat.getDateInstance().format(paramHSSFCell.getDateCellValue()).toString().trim();
/*      */           break;
/*      */       } 
/*      */ 
/*      */     
/*  916 */     } catch (Exception exception) {
/*  917 */       exception.printStackTrace();
/*      */     } 
/*  919 */     if (paramInt1 == 1 && str.equals("")) {
/*  920 */       this.msg1.add("" + paramInt2);
/*  921 */       this.msg2.add("" + paramInt3);
/*  922 */       this.msg3.add(" ");
/*      */     } 
/*  924 */     str = Util.null2String(str).trim();
/*      */     
/*  926 */     return str;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private String getCellValue_S(HSSFCell paramHSSFCell, int paramInt, String paramString1, String paramString2) {
/*  936 */     String str = ""; try {
/*      */       DecimalFormat decimalFormat;
/*  938 */       switch (paramHSSFCell.getCellType()) {
/*      */         case NUMERIC:
/*  940 */           if (HSSFDateUtil.isCellDateFormatted((Cell)paramHSSFCell)) {
/*  941 */             str = DateFormat.getDateInstance().format(paramHSSFCell.getDateCellValue()).toString().trim();
/*      */             break;
/*      */           } 
/*  944 */           decimalFormat = new DecimalFormat("0");
/*  945 */           str = decimalFormat.format(paramHSSFCell.getNumericCellValue());
/*      */           
/*  947 */           if (str.indexOf(".") > 0) {
/*  948 */             str = str.substring(0, str.indexOf("."));
/*      */           }
/*      */           break;
/*      */         
/*      */         case STRING:
/*  953 */           str = paramHSSFCell.getStringCellValue().trim();
/*  954 */           str = str.replaceAll("'", "＇");
/*      */           break;
/*      */         
/*      */         case FORMULA:
/*  958 */           str = DateFormat.getDateInstance().format(paramHSSFCell.getDateCellValue()).toString().trim();
/*      */           break;
/*      */       } 
/*      */ 
/*      */     
/*  963 */     } catch (NullPointerException nullPointerException) {
/*      */ 
/*      */     
/*  966 */     } catch (Exception exception) {
/*  967 */       writeLog(exception);
/*  968 */       exception.printStackTrace();
/*      */     } 
/*  970 */     if (paramInt == 1 && str.trim().equals("")) {
/*  971 */       this.msg1.add(paramString1);
/*  972 */       this.msg2.add(paramString2);
/*  973 */       this.msg3.add(SystemEnv.getHtmlLabelName(129512, getUserLanguageid()));
/*      */     } 
/*  975 */     str = Util.null2String(str).trim();
/*  976 */     return str;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private String getCellValue(HSSFCell paramHSSFCell, int paramInt1, int paramInt2, int paramInt3, HashMap<String, String> paramHashMap, int paramInt4) {
/*  990 */     String str = Util.null2String(getCellValue(paramHSSFCell, paramInt1, paramInt2, paramInt3));
/*      */     
/*  992 */     if (!str.startsWith("0") && Util.getDoubleValue(str, -1.0D) > -1.0D && 
/*  993 */       str.indexOf(".") > -1) {
/*  994 */       str = str.substring(0, str.indexOf("."));
/*      */     }
/*      */ 
/*      */     
/*  998 */     if (paramInt4 == 1 && paramInt1 == 1 && !"".equals(str) && Util.getIntValue(str, 0) <= 0) {
/*  999 */       this.msg1.add("" + paramInt2);
/* 1000 */       this.msg2.add("" + paramInt3);
/* 1001 */       this.msg3.add(" ");
/* 1002 */       return "";
/* 1003 */     }  if (paramInt4 == 2 && paramInt1 == 1 && !"".equals(str) && !isDate(str)) {
/* 1004 */       this.msg1.add("" + paramInt2);
/* 1005 */       this.msg2.add("" + paramInt3);
/* 1006 */       this.msg3.add(" ");
/* 1007 */       return "";
/* 1008 */     }  if (paramInt4 == 3 && paramInt1 == 1 && !"".equals(str) && Util.getDoubleValue(str, 0.0D) <= 0.0D) {
/* 1009 */       this.msg1.add("" + paramInt2);
/* 1010 */       this.msg2.add("" + paramInt3);
/* 1011 */       this.msg3.add(" ");
/* 1012 */       return "";
/*      */     } 
/* 1014 */     if (!"".equals(str) && 
/* 1015 */       paramHashMap != null) {
/* 1016 */       if (paramHashMap.containsKey(str)) {
/* 1017 */         str = "" + (int)Util.getDoubleValue(paramHashMap.get(str), -1.0D);
/*      */       } else {
/* 1019 */         str = "";
/* 1020 */         if (paramInt1 == 1) {
/* 1021 */           this.msg1.add("" + paramInt2);
/* 1022 */           this.msg2.add("" + paramInt3);
/* 1023 */           this.msg3.add(" ");
/* 1024 */           return "";
/*      */         } 
/*      */       } 
/*      */     }
/*      */ 
/*      */ 
/*      */ 
/*      */     
/* 1032 */     return str;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private HashMap<String, String> getCptData1MarkMap(int paramInt1, int paramInt2) {
/* 1042 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 1043 */     RecordSet recordSet = new RecordSet();
/* 1044 */     String str = "select mark,id from cptcapital where isdata='" + paramInt1 + "'";
/* 1045 */     recordSet.executeSql(str);
/* 1046 */     while (recordSet.next()) {
/* 1047 */       hashMap.put(recordSet.getString("cptcapital", paramInt2, true, true), recordSet.getString("id"));
/*      */     }
/* 1049 */     return (HashMap)hashMap;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private HashMap<String, String> getCptStateMap() {
/* 1058 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 1059 */     RecordSet recordSet = new RecordSet();
/* 1060 */     String str = "select * from CptCapitalState order by id ";
/* 1061 */     LanguageComInfo languageComInfo = null;
/*      */     try {
/* 1063 */       languageComInfo = new LanguageComInfo();
/* 1064 */     } catch (Exception exception) {
/* 1065 */       exception.printStackTrace();
/*      */     } 
/* 1067 */     recordSet.executeQuery(str, new Object[0]);
/* 1068 */     while (recordSet.next()) {
/* 1069 */       String str1 = Util.null2String(recordSet.getString("name"));
/* 1070 */       while (languageComInfo.next()) {
/* 1071 */         hashMap.put(Util.formatMultiLang(str1, languageComInfo.getLanguageid()), recordSet.getString("id"));
/*      */       }
/*      */     } 
/* 1074 */     return (HashMap)hashMap;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private HashMap<String, String> getDeptMap() {
/* 1083 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 1084 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 1085 */     HashMap<Object, Object> hashMap3 = new HashMap<>();
/* 1086 */     LanguageComInfo languageComInfo = null;
/*      */     try {
/* 1088 */       languageComInfo = new LanguageComInfo();
/* 1089 */     } catch (Exception exception) {
/* 1090 */       exception.printStackTrace();
/*      */     } 
/* 1092 */     RecordSet recordSet = new RecordSet();
/* 1093 */     String str = "select * from HrmDepartment where id not in (select id from HrmDepartment where canceled=1) order by subcompanyid1,supdepid ";
/* 1094 */     recordSet.executeQuery(str, new Object[0]);
/* 1095 */     while (recordSet.next()) {
/* 1096 */       String str1 = Util.null2String(recordSet.getString("departmentname"));
/* 1097 */       while (languageComInfo.next()) {
/* 1098 */         hashMap1.put(Util.formatMultiLang(str1, languageComInfo.getLanguageid()), recordSet.getString("id"));
/* 1099 */         hashMap3.put(recordSet.getString("subcompanyid1") + "_____" + Util.formatMultiLang(str1, languageComInfo.getLanguageid()), recordSet.getString("id"));
/*      */       } 
/* 1101 */       hashMap2.put(recordSet.getString("id"), recordSet.getString("id"));
/*      */     } 
/* 1103 */     hashMap1.putAll(getAllpathDepartmentMapByCache());
/* 1104 */     hashMap1.putAll(hashMap3);
/* 1105 */     hashMap1.putAll(hashMap2);
/* 1106 */     return (HashMap)hashMap1;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private HashMap<String, String> getSubMap() {
/* 1115 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 1116 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 1117 */     LanguageComInfo languageComInfo = null;
/*      */     try {
/* 1119 */       languageComInfo = new LanguageComInfo();
/* 1120 */     } catch (Exception exception) {
/* 1121 */       exception.printStackTrace();
/*      */     } 
/* 1123 */     RecordSet recordSet = new RecordSet();
/* 1124 */     String str = "select * from HrmSubCompany where id not in (select id from HrmSubCompany where canceled=1) order by supsubcomid ";
/* 1125 */     recordSet.executeQuery(str, new Object[0]);
/* 1126 */     while (recordSet.next()) {
/* 1127 */       String str1 = Util.null2String(recordSet.getString("subcompanydesc"));
/* 1128 */       while (languageComInfo.next()) {
/* 1129 */         hashMap1.put(Util.formatMultiLang(str1, languageComInfo.getLanguageid()), recordSet.getString("id"));
/*      */       }
/* 1131 */       hashMap2.put(recordSet.getString("id"), recordSet.getString("id"));
/*      */     } 
/* 1133 */     hashMap1.putAll(getAllpathSubcompanyMapByCache());
/* 1134 */     hashMap1.putAll(hashMap2);
/* 1135 */     return (HashMap)hashMap1;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private HashMap<String, String> getHrmMap() {
/* 1144 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 1145 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 1146 */     LanguageComInfo languageComInfo = null;
/*      */     try {
/* 1148 */       languageComInfo = new LanguageComInfo();
/* 1149 */     } catch (Exception exception) {
/* 1150 */       exception.printStackTrace();
/*      */     } 
/* 1152 */     RecordSet recordSet = new RecordSet();
/* 1153 */     String str = "select id,lastname from hrmresource where status<4 order by id ";
/* 1154 */     recordSet.executeQuery(str, new Object[0]);
/* 1155 */     while (recordSet.next()) {
/* 1156 */       String str1 = Util.null2String(recordSet.getString("lastname"));
/* 1157 */       while (languageComInfo.next()) {
/* 1158 */         hashMap1.put(Util.formatMultiLang(str1, languageComInfo.getLanguageid()), recordSet.getString("id"));
/*      */       }
/* 1160 */       hashMap2.put(recordSet.getString("id"), recordSet.getString("id"));
/*      */     } 
/* 1162 */     hashMap1.putAll(hashMap2);
/* 1163 */     return (HashMap)hashMap1;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private HashMap<String, String> getCustomBrowser(String paramString1, String paramString2, String paramString3, String paramString4) {
/* 1170 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 1171 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 1172 */     RecordSet recordSet = new RecordSet();
/* 1173 */     if ("".equals(paramString2)) {
/* 1174 */       recordSet.executeSql(paramString1);
/*      */     } else {
/* 1176 */       recordSet.executeSqlWithDataSource(paramString1, paramString2);
/*      */     } 
/* 1178 */     while (recordSet.next()) {
/* 1179 */       hashMap1.put(recordSet.getString(paramString4), recordSet.getString(paramString3));
/* 1180 */       hashMap2.put(recordSet.getString(paramString3), recordSet.getString(paramString3));
/*      */     } 
/* 1182 */     hashMap1.putAll(hashMap2);
/* 1183 */     return (HashMap)hashMap1;
/*      */   }
/*      */   
/*      */   private HashMap<String, String> getBooleanMap() {
/* 1187 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 1188 */     hashMap.put(SystemEnv.getHtmlLabelName(163, 7), "1");
/* 1189 */     hashMap.put("1", "1");
/* 1190 */     hashMap.put("yes", "1");
/* 1191 */     hashMap.put("YES", "1");
/* 1192 */     hashMap.put("true", "1");
/* 1193 */     hashMap.put("TRUE", "1");
/* 1194 */     hashMap.put(SystemEnv.getHtmlLabelName(30587, 7), "0");
/* 1195 */     hashMap.put("0", "0");
/* 1196 */     hashMap.put("no", "0");
/* 1197 */     hashMap.put("NO", "0");
/* 1198 */     hashMap.put("false", "0");
/* 1199 */     hashMap.put("FALSE", "0");
/* 1200 */     return (HashMap)hashMap;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private String getAllSupDeptids(String paramString) throws Exception {
/* 1211 */     return getAllSupDeptids(paramString, new DepartmentComInfo(), 1);
/*      */   }
/*      */   
/*      */   private String getAllSupDeptids(String paramString, DepartmentComInfo paramDepartmentComInfo, int paramInt) throws Exception {
/* 1215 */     String str1 = "";
/* 1216 */     if (paramDepartmentComInfo == null) {
/* 1217 */       paramDepartmentComInfo = new DepartmentComInfo();
/*      */     }
/* 1219 */     String str2 = paramDepartmentComInfo.getDepartmentsupdepid(paramString);
/* 1220 */     if (str2 == null || str2.equals("") || str2.equals("0") || str2.equals(paramString) || paramInt > 100)
/* 1221 */       return str1; 
/* 1222 */     str1 = str1 + str2 + ",";
/* 1223 */     paramInt++;
/* 1224 */     str1 = str1 + getAllSupDeptids(str2, paramDepartmentComInfo, paramInt);
/* 1225 */     return str1;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public HashMap<String, String> getAllpathDepartmentMapByCache() {
/* 1234 */     HashMap<String, String> hashMap = getAllpathSubcompanyMap2ByCache();
/* 1235 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*      */     try {
/* 1237 */       DepartmentComInfo departmentComInfo = new DepartmentComInfo();
/* 1238 */       departmentComInfo.setTofirstRow();
/* 1239 */       int i = 100000;
/* 1240 */       LanguageComInfo languageComInfo = null;
/*      */       try {
/* 1242 */         languageComInfo = new LanguageComInfo();
/* 1243 */       } catch (Exception exception) {
/* 1244 */         exception.printStackTrace();
/*      */       } 
/* 1246 */       while (departmentComInfo.next() && i-- > 0) {
/* 1247 */         String str1 = departmentComInfo.getSubcompanyid1();
/* 1248 */         String str2 = departmentComInfo.getDepartmentid();
/* 1249 */         if (!departmentComInfo.getDeparmentcanceled(str2).equals("1")) {
/* 1250 */           String str3 = departmentComInfo.getDepartmentname();
/* 1251 */           String str4 = getAllSupDeptids(str2);
/* 1252 */           String str5 = "";
/* 1253 */           while (languageComInfo.next()) {
/* 1254 */             if (!"".equals(str4)) {
/* 1255 */               String[] arrayOfString = Util.TokenizerString2(str4, ",");
/* 1256 */               for (int j = arrayOfString.length - 1; j >= 0; j--) {
/* 1257 */                 str5 = str5 + Util.formatMultiLang(departmentComInfo.getDepartmentname(arrayOfString[j]), languageComInfo.getLanguageid()) + ">";
/*      */               }
/* 1259 */               str5 = str5 + Util.formatMultiLang(str3, languageComInfo.getLanguageid());
/*      */             } else {
/* 1261 */               str5 = Util.formatMultiLang(str3, languageComInfo.getLanguageid());
/*      */             } 
/* 1263 */             if (hashMap.containsKey(str1)) {
/* 1264 */               String str = (String)hashMap.get(str1) + ">" + str5;
/* 1265 */               hashMap1.put(str, str2);
/*      */             } 
/* 1267 */             hashMap1.put(str5, str2);
/*      */           } 
/*      */         } 
/*      */       } 
/* 1271 */     } catch (Exception exception) {
/* 1272 */       exception.printStackTrace();
/*      */     } 
/* 1274 */     return (HashMap)hashMap1;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public HashMap<String, String> getAllpathSubcompanyMapByCache() {
/* 1283 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*      */     try {
/* 1285 */       SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/* 1286 */       subCompanyComInfo.setTofirstRow();
/* 1287 */       int i = 100000;
/* 1288 */       LanguageComInfo languageComInfo = null;
/*      */       try {
/* 1290 */         languageComInfo = new LanguageComInfo();
/* 1291 */       } catch (Exception exception) {
/* 1292 */         exception.printStackTrace();
/*      */       } 
/* 1294 */       while (subCompanyComInfo.next() && i-- > 0) {
/* 1295 */         String str = subCompanyComInfo.getSubCompanyid();
/* 1296 */         if (!subCompanyComInfo.getCompanyiscanceled(str).equals("1")) {
/* 1297 */           String str1 = subCompanyComInfo.getSubCompanydesc();
/* 1298 */           String str2 = subCompanyComInfo.getAllSupCompany(str);
/* 1299 */           String str3 = "";
/* 1300 */           while (languageComInfo.next()) {
/* 1301 */             if (!"".equals(str2)) {
/* 1302 */               String[] arrayOfString = Util.TokenizerString2(str2, ",");
/* 1303 */               for (int j = arrayOfString.length - 1; j >= 0; j--) {
/* 1304 */                 str3 = str3 + Util.formatMultiLang(subCompanyComInfo.getSubCompanydesc(arrayOfString[j]), languageComInfo.getLanguageid()) + ">";
/*      */               }
/* 1306 */               str3 = str3 + Util.formatMultiLang(str1, languageComInfo.getLanguageid());
/*      */             } else {
/* 1308 */               str3 = Util.formatMultiLang(str1, languageComInfo.getLanguageid());
/*      */             } 
/* 1310 */             hashMap.put(str3, str);
/*      */           } 
/*      */         } 
/*      */       } 
/* 1314 */     } catch (Exception exception) {
/* 1315 */       exception.printStackTrace();
/*      */     } 
/*      */     
/* 1318 */     return (HashMap)hashMap;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private HashMap<String, String> getAllpathSubcompanyMap2ByCache() {
/* 1327 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*      */     try {
/* 1329 */       SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/* 1330 */       subCompanyComInfo.setTofirstRow();
/* 1331 */       int i = 100000;
/* 1332 */       LanguageComInfo languageComInfo = null;
/*      */       try {
/* 1334 */         languageComInfo = new LanguageComInfo();
/* 1335 */       } catch (Exception exception) {
/* 1336 */         exception.printStackTrace();
/*      */       } 
/* 1338 */       while (subCompanyComInfo.next() && i-- > 0) {
/* 1339 */         String str = subCompanyComInfo.getSubCompanyid();
/* 1340 */         if (!subCompanyComInfo.getCompanyiscanceled(str).equals("1")) {
/* 1341 */           String str1 = subCompanyComInfo.getSubCompanydesc();
/* 1342 */           String str2 = subCompanyComInfo.getAllSupCompany(str);
/* 1343 */           String str3 = "";
/* 1344 */           while (languageComInfo.next()) {
/* 1345 */             if (!"".equals(str2)) {
/* 1346 */               String[] arrayOfString = Util.TokenizerString2(str2, ",");
/* 1347 */               for (int j = arrayOfString.length - 1; j >= 0; j--) {
/* 1348 */                 str3 = str3 + Util.formatMultiLang(subCompanyComInfo.getSubCompanydesc(arrayOfString[j]), languageComInfo.getLanguageid()) + ">";
/*      */               }
/* 1350 */               str3 = str3 + Util.formatMultiLang(str1, languageComInfo.getLanguageid());
/*      */             } else {
/* 1352 */               str3 = Util.formatMultiLang(str1, languageComInfo.getLanguageid());
/*      */             } 
/* 1354 */             hashMap.put(str, str3);
/*      */           } 
/*      */         } 
/*      */       } 
/* 1358 */     } catch (Exception exception) {
/* 1359 */       exception.printStackTrace();
/*      */     } 
/*      */     
/* 1362 */     return (HashMap)hashMap;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public HashMap<String, HashMap<String, String>> getCusBrowserMap() {
/* 1372 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*      */     try {
/* 1374 */       BrowserComInfo browserComInfo = new BrowserComInfo();
/* 1375 */       String str = "select type,fielddbtype from cptDefineField where fieldhtmltype=3 and isopen='1' ";
/* 1376 */       RecordSet recordSet1 = new RecordSet();
/* 1377 */       RecordSet recordSet2 = new RecordSet();
/* 1378 */       LanguageComInfo languageComInfo = new LanguageComInfo();
/* 1379 */       recordSet1.executeSql(str);
/* 1380 */       while (recordSet1.next()) {
/* 1381 */         String str1 = recordSet1.getString("type");
/* 1382 */         String str2 = recordSet1.getString("fielddbtype");
/* 1383 */         if ("2".equals(str1) || "19".equals(str1) || hashMap.containsKey(str1)) {
/*      */           continue;
/*      */         }
/* 1386 */         if ("164".equals(str1) || "194".equals(str1) || "169".equals(str1) || "170".equals(str1)) {
/*      */           
/* 1388 */           if (this.fullSubcompanyMap != null) {
/* 1389 */             hashMap.put(str1, this.fullSubcompanyMap); continue;
/*      */           } 
/* 1391 */           hashMap.put(str1, getSubMap());
/*      */           continue;
/*      */         } 
/* 1394 */         if ("4".equals(str1) || "57".equals(str1) || "167".equals(str1) || "168".equals(str1)) {
/*      */           
/* 1396 */           if (this.fullDepartmentMap != null) {
/* 1397 */             hashMap.put(str1, this.fullDepartmentMap); continue;
/*      */           } 
/* 1399 */           hashMap.put(str1, getDeptMap());
/*      */           continue;
/*      */         } 
/* 1402 */         if ("1".equals(str1) || "17".equals(str1) || "165".equals(str1) || "166".equals(str1)) {
/*      */           
/* 1404 */           if (this.fullHrmMap != null) {
/* 1405 */             hashMap.put(str1, this.fullHrmMap); continue;
/*      */           } 
/* 1407 */           hashMap.put(str1, getHrmMap());
/*      */           continue;
/*      */         } 
/* 1410 */         if ("161".equals(str1) || "162".equals(str1)) {
/* 1411 */           BaseBrowser baseBrowser = (BaseBrowser)StaticObj.getServiceByFullname(str2, Browser.class);
/* 1412 */           String str7 = baseBrowser.getFrom();
/* 1413 */           String str8 = baseBrowser.getSearch();
/* 1414 */           String str9 = "";
/*      */           try {
/* 1416 */             str9 = baseBrowser.getDs().getDatasourcename();
/* 1417 */           } catch (Exception exception) {}
/* 1418 */           if ("2".equals(str7)) {
/* 1419 */             String str17 = baseBrowser.getKeyfield();
/* 1420 */             String str18 = baseBrowser.getNamefield();
/* 1421 */             if (!hashMap.keySet().contains(str2))
/* 1422 */               hashMap.put(str2, getCustomBrowser(str8, str9, str17, str18)); 
/*      */             continue;
/*      */           } 
/* 1425 */           String str10 = str2.split("\\.")[1];
/* 1426 */           recordSet2.execute("select searchbyid,searchbyname from mode_browser where showname='" + str10 + "'");
/* 1427 */           recordSet2.next();
/* 1428 */           String str11 = recordSet2.getString(1).toLowerCase();
/* 1429 */           String str12 = recordSet2.getString(2).toLowerCase();
/*      */           
/* 1431 */           String str13 = str11.split("where")[1].trim();
/* 1432 */           String str14 = str12.split("where")[1].trim();
/*      */           
/* 1434 */           String str15 = str13.split("=")[0].trim();
/* 1435 */           String str16 = str14.split("like")[0].trim();
/*      */           
/* 1437 */           if (!hashMap.keySet().contains(str2)) {
/* 1438 */             hashMap.put(str2, getCustomBrowser(str8, str9, str15, str16));
/*      */           }
/*      */           
/*      */           continue;
/*      */         } 
/* 1443 */         String str3 = browserComInfo.getBrowsertablename(str1);
/* 1444 */         String str4 = browserComInfo.getBrowsercolumname(str1);
/* 1445 */         String str5 = browserComInfo.getBrowserkeycolumname(str1);
/* 1446 */         String str6 = "select " + str5 + "," + str4 + " from " + str3 + "";
/* 1447 */         if (str1.equals("23")) {
/* 1448 */           str6 = str6 + " where isdata=2";
/* 1449 */         } else if (str1.equals("179")) {
/* 1450 */           str6 = str6 + " where isdata=1 and (cancelled is null or cancelled = 0)";
/*      */         } 
/* 1452 */         recordSet2.executeSql(str6);
/* 1453 */         HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 1454 */         HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 1455 */         while (recordSet2.next()) {
/* 1456 */           hashMap1.put(Util.formatMultiLang(recordSet2.getString(str4), languageComInfo.getLanguageid()), recordSet2.getString(str5));
/* 1457 */           hashMap2.put(recordSet2.getString(str5), recordSet2.getString(str5));
/*      */         } 
/* 1459 */         hashMap1.putAll(hashMap2);
/* 1460 */         hashMap.put(str1, hashMap1);
/*      */       } 
/* 1462 */     } catch (Exception exception) {
/* 1463 */       exception.printStackTrace();
/* 1464 */       writeLog(exception.getMessage());
/* 1465 */       return (HashMap)hashMap;
/*      */     } 
/*      */     
/* 1468 */     return (HashMap)hashMap;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private boolean validateData(HSSFWorkbook paramHSSFWorkbook, int paramInt1, int paramInt2) {
/* 1480 */     RecordSet recordSet = new RecordSet();
/* 1481 */     HSSFSheet hSSFSheet = paramHSSFWorkbook.getSheetAt(0);
/* 1482 */     int i = hSSFSheet.getLastRowNum();
/* 1483 */     if (i <= 0) {
/* 1484 */       this.msgType = "e4";
/* 1485 */       return false;
/*      */     } 
/* 1487 */     HashSet<String> hashSet = new HashSet();
/* 1488 */     HashMap<String, String> hashMap1 = getCptData1MarkMap(paramInt1, Integer.parseInt(this.upCondition));
/* 1489 */     HashMap<String, String> hashMap2 = getBooleanMap();
/* 1490 */     HashMap<String, HashMap<String, String>> hashMap = getCusBrowserMap();
/* 1491 */     HSSFRow hSSFRow = null;
/*      */     try {
/* 1493 */       for (byte b = 1; b < i + 1; b++) {
/* 1494 */         hSSFRow = hSSFSheet.getRow(b);
/* 1495 */         if (hSSFRow == null)
/* 1496 */           break;  String str = getCellValue(hSSFRow.getCell(0), 0, b + 1, 1, (HashMap<String, String>)null, 0);
/* 1497 */         if (!"".equals(str)) {
/* 1498 */           if (hashMap1.containsKey(str)) {
/* 1499 */             if (!hashSet.contains(str)) {
/* 1500 */               hashSet.add(str);
/*      */             } else {
/* 1502 */               this.msg1.add("" + (b + 1));
/* 1503 */               this.msg2.add("1");
/* 1504 */               this.msg3.add(this.conditionName + SystemEnv.getHtmlLabelName(18082, paramInt2));
/*      */             } 
/*      */           } else {
/* 1507 */             this.msg1.add("" + (b + 1));
/* 1508 */             this.msg2.add("1");
/* 1509 */             this.msg3.add(this.conditionName + SystemEnv.getHtmlLabelName(384354, paramInt2));
/*      */           } 
/*      */         } else {
/* 1512 */           this.msg1.add("" + (b + 1));
/* 1513 */           this.msg2.add("1");
/* 1514 */           this.msg3.add(SystemEnv.getHtmlLabelName(129512, paramInt2) + "!");
/*      */         } 
/*      */ 
/*      */         
/* 1518 */         if (this.impFieldIds != null && this.impFieldIds.size() > 0)
/* 1519 */           for (byte b1 = 1; b1 <= this.impFieldIds.size(); b1++) {
/* 1520 */             String str1 = this.impFieldIds.get(b1 - 1);
/* 1521 */             String str2 = this.CptFieldComInfo.getFieldname(str1);
/* 1522 */             int j = Integer.parseInt(this.CptFieldComInfo.getFieldhtmltype(str1));
/* 1523 */             String str3 = this.CptFieldComInfo.getFielddbtype(str1);
/* 1524 */             int k = Integer.parseInt(this.CptFieldComInfo.getFieldType(str1));
/* 1525 */             String str4 = this.CptFieldComInfo.getIsmand(str1);
/*      */             
/* 1527 */             if (j != 6 && j != 7) {
/*      */ 
/*      */               
/* 1530 */               String str5 = Util.null2String(getCellValue(hSSFRow.getCell((short)b1), 0, (b + 1) + "", "" + (b1 + 1)));
/*      */               
/* 1532 */               if (!"".equalsIgnoreCase(str5))
/*      */               {
/*      */                 
/* 1535 */                 if ("null".equalsIgnoreCase(str5)) {
/* 1536 */                   if ("1".equals(str4)) {
/* 1537 */                     this.msg1.add((b + 1) + "");
/* 1538 */                     this.msg2.add((b1 + 1) + "");
/* 1539 */                     this.msg3.add(SystemEnv.getHtmlLabelName(129512, paramInt2) + "!");
/*      */                   
/*      */                   }
/*      */                 
/*      */                 }
/* 1544 */                 else if ("isinner".equals(str2)) {
/* 1545 */                   if (!str5.equals("0") && !str5.equals("1") && !str5.equals(SystemEnv.getHtmlLabelName(15298, paramInt2)) && 
/* 1546 */                     !str5.equals(SystemEnv.getHtmlLabelName(15299, paramInt2)))
/*      */                   {
/* 1548 */                     this.msg1.add((b + 1) + "");
/* 1549 */                     this.msg2.add((b1 + 1) + "");
/* 1550 */                     this.msg3.add(SystemEnv.getHtmlLabelName(518536, paramInt2));
/*      */                   }
/*      */                 
/* 1553 */                 } else if ("attribute".equals(str2)) {
/* 1554 */                   if (!str5.equals(SystemEnv.getHtmlLabelName(1366, paramInt2)) && 
/* 1555 */                     !str5.equals(SystemEnv.getHtmlLabelName(1368, paramInt2)) && 
/* 1556 */                     !str5.equals(SystemEnv.getHtmlLabelName(1369, paramInt2)) && 
/* 1557 */                     !str5.equals(SystemEnv.getHtmlLabelName(60, paramInt2)) && 
/* 1558 */                     !str5.equals(SystemEnv.getHtmlLabelName(1370, paramInt2)) && 
/* 1559 */                     !str5.equals(SystemEnv.getHtmlLabelName(811, paramInt2)) && 
/* 1560 */                     !str5.equals(SystemEnv.getHtmlLabelName(811, paramInt2)) && 
/* 1561 */                     !str5.equals(SystemEnv.getHtmlLabelName(1367, paramInt2)))
/*      */                   {
/* 1563 */                     this.msg1.add((b + 1) + "");
/* 1564 */                     this.msg2.add((b1 + 1) + "");
/* 1565 */                     this.msg3.add(SystemEnv.getHtmlLabelName(518536, paramInt2));
/*      */                   }
/*      */                 
/* 1568 */                 } else if ("attribute".equals(str2)) {
/* 1569 */                   if (!str5.equals(SystemEnv.getHtmlLabelName(1366, paramInt2)) && 
/* 1570 */                     !str5.equals(SystemEnv.getHtmlLabelName(1368, paramInt2)) && 
/* 1571 */                     !str5.equals(SystemEnv.getHtmlLabelName(1369, paramInt2)) && 
/* 1572 */                     !str5.equals(SystemEnv.getHtmlLabelName(60, paramInt2)) && 
/* 1573 */                     !str5.equals(SystemEnv.getHtmlLabelName(1370, paramInt2)) && 
/* 1574 */                     !str5.equals(SystemEnv.getHtmlLabelName(811, paramInt2)) && 
/* 1575 */                     !str5.equals(SystemEnv.getHtmlLabelName(811, paramInt2)) && 
/* 1576 */                     !str5.equals(SystemEnv.getHtmlLabelName(1367, paramInt2)))
/*      */                   {
/* 1578 */                     this.msg1.add((b + 1) + "");
/* 1579 */                     this.msg2.add((b1 + 1) + "");
/* 1580 */                     this.msg3.add(SystemEnv.getHtmlLabelName(518536, paramInt2));
/*      */                   
/*      */                   }
/*      */                 
/*      */                 }
/* 1585 */                 else if (j == 1) {
/* 1586 */                   if (k == 2) {
/* 1587 */                     if (!"".equals(str5)) {
/*      */                       try {
/* 1589 */                         Double.parseDouble(str5);
/* 1590 */                       } catch (NumberFormatException numberFormatException) {
/* 1591 */                         this.msg1.add((b + 1) + "");
/* 1592 */                         this.msg2.add((b1 + 1) + "");
/* 1593 */                         this.msg3.add(SystemEnv.getHtmlLabelName(386061, paramInt2));
/*      */                       }
/*      */                     
/*      */                     }
/* 1597 */                   } else if ((k == 3 || k == 4 || k == 5) && 
/* 1598 */                     !"".equals(str5)) {
/*      */                     try {
/* 1600 */                       Double.parseDouble(str5);
/* 1601 */                     } catch (NumberFormatException numberFormatException) {
/* 1602 */                       this.msg1.add((b + 1) + "");
/* 1603 */                       this.msg2.add((b1 + 1) + "");
/* 1604 */                       this.msg3.add(SystemEnv.getHtmlLabelName(386061, paramInt2));
/*      */                     }
/*      */                   
/*      */                   }
/*      */                 
/* 1609 */                 } else if (j == 3 && k != 2 && k != 19 && k != 402 && k != 403) {
/* 1610 */                   if (str3.startsWith("int")) {
/* 1611 */                     if (hashMap.containsKey("" + k)) {
/* 1612 */                       if (Util.getDoubleValue(str5, 0.0D) > 0.0D) {
/* 1613 */                         str5 = "" + (int)Util.getDoubleValue(str5, 0.0D);
/*      */                       }
/* 1615 */                       str5 = "" + Util.getIntValue((String)((HashMap)hashMap.get("" + k)).get(str5), 0);
/*      */                     } else {
/* 1617 */                       str5 = "0";
/*      */                     } 
/* 1619 */                     if (Util.getIntValue(str5) == 0) {
/* 1620 */                       this.msg1.add((b + 1) + "");
/* 1621 */                       this.msg2.add((b1 + 1) + "");
/* 1622 */                       this.msg3.add(SystemEnv.getHtmlLabelName(518536, paramInt2));
/*      */                     } 
/* 1624 */                   } else if (str3.startsWith("browser")) {
/* 1625 */                     if (hashMap.containsKey(str3)) {
/* 1626 */                       String[] arrayOfString = Util.TokenizerString2(str5, ",");
/* 1627 */                       String str6 = "";
/* 1628 */                       for (String str7 : arrayOfString) {
/* 1629 */                         String str8 = Util.null2String((String)((HashMap)hashMap.get(str3)).get(str7), "");
/* 1630 */                         if (!"".equals(str8)) {
/* 1631 */                           str6 = str6 + "" + str8 + ",";
/*      */                         } else {
/* 1633 */                           this.msg1.add((b + 1) + "");
/* 1634 */                           this.msg2.add((b1 + 1) + "");
/* 1635 */                           this.msg3.add(SystemEnv.getHtmlLabelName(518536, paramInt2));
/*      */                           
/*      */                           break;
/*      */                         } 
/*      */                       } 
/*      */                     } 
/* 1641 */                   } else if (hashMap.containsKey("" + k)) {
/* 1642 */                     String[] arrayOfString = Util.TokenizerString2(str5, ",");
/* 1643 */                     String str6 = "";
/* 1644 */                     for (String str7 : arrayOfString) {
/* 1645 */                       String str8 = Util.null2String((String)((HashMap)hashMap.get(str3)).get(str7), "");
/* 1646 */                       if (!"".equals(str8)) {
/* 1647 */                         str6 = str6 + "" + str8 + ",";
/*      */                       }
/*      */                     } 
/* 1650 */                     if (str6 == "") {
/* 1651 */                       this.msg1.add((b + 1) + "");
/* 1652 */                       this.msg2.add((b1 + 1) + "");
/* 1653 */                       this.msg3.add(SystemEnv.getHtmlLabelName(518536, paramInt2));
/*      */                     }
/*      */                   
/*      */                   } 
/* 1657 */                 } else if (j == 3) {
/* 1658 */                   if (k == 2 || k == 402 || k == 403) {
/* 1659 */                     checkDate(str5, b + 1, b1 + 1, true, k);
/* 1660 */                   } else if (k == 19) {
/* 1661 */                     checkTime(str5, b + 1, b1 + 1, true);
/*      */                   } 
/* 1663 */                 } else if (j == 4) {
/* 1664 */                   if (!hashMap2.containsKey(str5)) {
/* 1665 */                     this.msg1.add((b + 1) + "");
/* 1666 */                     this.msg2.add((b1 + 1) + "");
/* 1667 */                     this.msg3.add(SystemEnv.getHtmlLabelName(518536, paramInt2));
/*      */                   }
/*      */                 
/* 1670 */                 } else if (j == 5) {
/*      */                   
/* 1672 */                   String str6 = "";
/* 1673 */                   String str7 = "";
/* 1674 */                   recordSet.executeQuery("select iscommon,cid from cptDefineField where fieldname=?", new Object[] { str2 });
/* 1675 */                   if (recordSet.next()) {
/* 1676 */                     str6 = Util.null2String(recordSet.getString("iscommon"));
/* 1677 */                     str7 = Util.null2String(recordSet.getString("cid"));
/*      */                   } 
/* 1679 */                   if (str6.equals("1")) {
/* 1680 */                     recordSet.executeQuery("select name from mode_selectitempagedetail where statelev =1 and cancel <> 1 and mainid=? order by id asc", new Object[] { str7 });
/* 1681 */                     boolean bool = true;
/* 1682 */                     byte b2 = 0;
/* 1683 */                     while (recordSet.next()) {
/* 1684 */                       String str8 = Util.formatMultiLang(Util.null2String(recordSet.getString("name")), this.userLanguageid + "");
/* 1685 */                       if (str8.equals(str5)) {
/* 1686 */                         bool = false;
/*      */                         break;
/*      */                       } 
/* 1689 */                       b2++;
/*      */                     } 
/*      */                     
/* 1692 */                     if (bool) {
/* 1693 */                       this.msg1.add((b + 1) + "");
/* 1694 */                       this.msg2.add((b1 + 1) + "");
/* 1695 */                       this.msg3.add(SystemEnv.getHtmlLabelName(518536, paramInt2));
/*      */                     } 
/*      */                   } else {
/*      */                     
/* 1699 */                     recordSet.executeSql("select selectvalue from cpt_selectitem where fieldid='" + str1 + "' and selectname='" + str5 + "' ");
/* 1700 */                     if (!recordSet.next()) {
/* 1701 */                       this.msg1.add((b + 1) + "");
/* 1702 */                       this.msg2.add((b1 + 1) + "");
/* 1703 */                       this.msg3.add(SystemEnv.getHtmlLabelName(518536, paramInt2));
/*      */                     } 
/*      */                   } 
/*      */                 } 
/*      */               }
/*      */             } 
/*      */           }  
/*      */       } 
/* 1711 */     } catch (Exception exception) {
/* 1712 */       exception.printStackTrace();
/*      */     } 
/* 1714 */     if (this.msg1.size() > 0) {
/* 1715 */       this.msgType = "e2";
/* 1716 */       return false;
/*      */     } 
/*      */     
/* 1719 */     return true;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private void getCrmInfo() {
/* 1727 */     this.crmidlist.clear();
/* 1728 */     this.crmnamelist.clear();
/* 1729 */     ConnStatement connStatement = new ConnStatement();
/*      */     try {
/* 1731 */       connStatement.setStatementSql("select id,name from CRM_CustomerInfo where deleted=0 and type=2");
/* 1732 */       connStatement.executeQuery();
/* 1733 */       while (connStatement.next()) {
/* 1734 */         this.crmidlist.add(connStatement.getString("id"));
/* 1735 */         this.crmnamelist.add(Util.null2String(connStatement.getString("name")).trim());
/*      */       } 
/* 1737 */     } catch (Exception exception) {
/* 1738 */       writeLog(exception);
/*      */     } finally {
/* 1740 */       connStatement.close();
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private String checkDate(String paramString, int paramInt1, int paramInt2, boolean paramBoolean, int paramInt3) {
/* 1751 */     paramString = paramString.replaceAll("/", "-");
/* 1752 */     paramString = paramString.replaceAll("\\.", "-");
/* 1753 */     String[] arrayOfString = paramString.split("-");
/* 1754 */     if (paramInt3 == 402 && arrayOfString.length == 1) {
/* 1755 */       String str = arrayOfString[0];
/*      */       try {
/* 1757 */         if (str.length() == 4) {
/* 1758 */           paramString = str;
/* 1759 */           SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy");
/* 1760 */           simpleDateFormat.setLenient(false);
/* 1761 */           simpleDateFormat.parse(paramString);
/* 1762 */           return paramString;
/*      */         } 
/* 1764 */       } catch (Exception exception) {
/* 1765 */         if (paramBoolean) {
/* 1766 */           this.msg1.add(paramInt1 + "");
/* 1767 */           this.msg2.add(paramInt2 + "");
/* 1768 */           this.msg3.add(SystemEnv.getHtmlLabelName(130208, this.userLanguageid));
/*      */         } 
/* 1770 */         return "";
/*      */       } 
/* 1772 */     } else if (paramInt3 == 403 && arrayOfString.length == 2) {
/* 1773 */       String str1 = arrayOfString[0];
/* 1774 */       String str2 = arrayOfString[1];
/*      */       try {
/* 1776 */         if (str1.length() == 4 && 1 <= Integer.valueOf(str2).intValue() && Integer.valueOf(str2).intValue() <= 12) {
/* 1777 */           if (str2.length() == 1) {
/* 1778 */             str2 = "0" + str2;
/*      */           }
/*      */           
/* 1781 */           paramString = str1 + "-" + str2;
/* 1782 */           SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM");
/* 1783 */           simpleDateFormat.setLenient(false);
/* 1784 */           simpleDateFormat.parse(paramString);
/* 1785 */           return paramString;
/*      */         } 
/* 1787 */       } catch (Exception exception) {
/* 1788 */         if (paramBoolean) {
/* 1789 */           this.msg1.add(paramInt1 + "");
/* 1790 */           this.msg2.add(paramInt2 + "");
/* 1791 */           this.msg3.add(SystemEnv.getHtmlLabelName(130208, this.userLanguageid));
/*      */         } 
/* 1793 */         return "";
/*      */       } 
/* 1795 */     }  if (paramInt3 == 2 && arrayOfString.length == 3) {
/* 1796 */       String str1 = arrayOfString[0];
/* 1797 */       String str2 = arrayOfString[1];
/* 1798 */       String str3 = arrayOfString[2];
/*      */       try {
/* 1800 */         if (str1.length() == 4 && 1 <= Integer.valueOf(str2).intValue() && Integer.valueOf(str2).intValue() <= 12 && 
/* 1801 */           1 <= Integer.valueOf(str3).intValue() && Integer.valueOf(str3).intValue() <= 31) {
/* 1802 */           if (str2.length() == 1) {
/* 1803 */             str2 = "0" + str2;
/*      */           }
/* 1805 */           if (str3.length() == 1) {
/* 1806 */             str3 = "0" + str3;
/*      */           }
/* 1808 */           paramString = str1 + "-" + str2 + "-" + str3;
/* 1809 */           SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
/* 1810 */           simpleDateFormat.setLenient(false);
/* 1811 */           simpleDateFormat.parse(paramString);
/* 1812 */           return paramString;
/*      */         }
/*      */       
/* 1815 */       } catch (Exception exception) {
/* 1816 */         if (paramBoolean) {
/* 1817 */           this.msg1.add(paramInt1 + "");
/* 1818 */           this.msg2.add(paramInt2 + "");
/* 1819 */           this.msg3.add(SystemEnv.getHtmlLabelName(130208, this.userLanguageid));
/*      */         } 
/* 1821 */         return "";
/*      */       } 
/*      */     } 
/* 1824 */     if (paramBoolean) {
/* 1825 */       this.msg1.add(paramInt1 + "");
/* 1826 */       this.msg2.add(paramInt2 + "");
/* 1827 */       this.msg3.add(SystemEnv.getHtmlLabelName(130208, this.userLanguageid));
/*      */     } 
/* 1829 */     return "";
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private String checkTime(String paramString, int paramInt1, int paramInt2, boolean paramBoolean) {
/* 1837 */     String[] arrayOfString = paramString.split(":");
/* 1838 */     if (arrayOfString.length == 2) {
/* 1839 */       String str1 = arrayOfString[0];
/* 1840 */       String str2 = arrayOfString[1];
/*      */       try {
/* 1842 */         if (0 <= Integer.valueOf(str1).intValue() && Integer.valueOf(str1).intValue() <= 23 && 
/* 1843 */           0 <= Integer.valueOf(str2).intValue() && Integer.valueOf(str2).intValue() <= 59) {
/* 1844 */           if (str1.length() == 1) {
/* 1845 */             str1 = "0" + str1;
/*      */           }
/* 1847 */           if (str2.length() == 1) {
/* 1848 */             str2 = "0" + str2;
/*      */           }
/* 1850 */           paramString = str1 + ":" + str2;
/* 1851 */           return paramString;
/*      */         }
/*      */       
/* 1854 */       } catch (Exception exception) {
/* 1855 */         if (paramBoolean) {
/* 1856 */           this.msg1.add(paramInt1 + "");
/* 1857 */           this.msg2.add(paramInt2 + "");
/* 1858 */           this.msg3.add(SystemEnv.getHtmlLabelName(24944, this.userLanguageid));
/*      */         } 
/* 1860 */         return "";
/*      */       } 
/*      */     } 
/* 1863 */     if (paramBoolean) {
/* 1864 */       this.msg1.add(paramInt1 + "");
/* 1865 */       this.msg2.add(paramInt2 + "");
/* 1866 */       this.msg3.add(SystemEnv.getHtmlLabelName(24944, this.userLanguageid));
/*      */     } 
/* 1868 */     return "";
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private void saveLogDetail(String paramString1, String paramString2, String paramString3, String paramString4, String paramString5, String paramString6) {
/* 1876 */     String str = "insert into CPT_ToDBLogDetail(logid,cptid,cptname,cptmark,num,sptcount,stateid,operationtype) values(?,?,?,?,?,?,?,2)";
/* 1877 */     RecordSet recordSet = new RecordSet();
/* 1878 */     recordSet.executeUpdate(str, new Object[] { this.logidTemp, paramString1, paramString2, paramString3, paramString4, paramString5, paramString6.equals("") ? "0" : paramString6 });
/*      */   }
/*      */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/cpt/ExcelToDB/CapitalExcelToUP.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */