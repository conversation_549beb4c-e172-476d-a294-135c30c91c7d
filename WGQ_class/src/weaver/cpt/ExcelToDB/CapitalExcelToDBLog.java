/*     */ package weaver.cpt.ExcelToDB;
/*     */ 
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CapitalExcelToDBLog
/*     */ {
/*     */   public String getImportType(String paramString1, String paramString2) {
/*  14 */     String str = "";
/*  15 */     if (paramString1.equals("1")) {
/*     */       
/*  17 */       str = SystemEnv.getHtmlLabelName(611, Util.getIntValue(paramString2));
/*  18 */     } else if (paramString1.equals("2")) {
/*  19 */       str = SystemEnv.getHtmlLabelName(17744, Util.getIntValue(paramString2));
/*     */     } 
/*     */     
/*  22 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getState(String paramString1, String paramString2) {
/*  29 */     String str = "";
/*  30 */     if (paramString1.equals("1")) {
/*  31 */       str = SystemEnv.getHtmlLabelName(1375, Util.getIntValue(paramString2));
/*  32 */     } else if (paramString1.equals("2")) {
/*  33 */       str = SystemEnv.getHtmlLabelName(1378, Util.getIntValue(paramString2));
/*  34 */     } else if (paramString1.equals("3")) {
/*  35 */       str = SystemEnv.getHtmlLabelName(1379, Util.getIntValue(paramString2));
/*  36 */     } else if (paramString1.equals("4")) {
/*  37 */       str = SystemEnv.getHtmlLabelName(1382, Util.getIntValue(paramString2));
/*  38 */     } else if (paramString1.equals("5")) {
/*  39 */       str = SystemEnv.getHtmlLabelName(1386, Util.getIntValue(paramString2));
/*  40 */     } else if (paramString1.equals("-7")) {
/*  41 */       str = SystemEnv.getHtmlLabelName(1385, Util.getIntValue(paramString2));
/*     */     } 
/*     */     
/*  44 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String isDeleted(String paramString1, String paramString2) {
/*  51 */     String str = "";
/*  52 */     if (paramString1.equals("1")) {
/*  53 */       str = SystemEnv.getHtmlLabelName(18967, Util.getIntValue(paramString2));
/*     */     }
/*     */ 
/*     */     
/*  57 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getSptcount(String paramString1, String paramString2) {
/*  64 */     String str = "";
/*  65 */     if (paramString1.equals("1")) {
/*  66 */       str = SystemEnv.getHtmlLabelName(163, Util.getIntValue(paramString2));
/*     */     } else {
/*  68 */       str = SystemEnv.getHtmlLabelName(161, Util.getIntValue(paramString2));
/*     */     } 
/*  70 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getResult(String paramString1, String paramString2) {
/*  77 */     String str = "";
/*  78 */     String[] arrayOfString = paramString1.split(",");
/*  79 */     str = SystemEnv.getHtmlLabelName(32935, Util.getIntValue(paramString2)) + arrayOfString[0] + SystemEnv.getHtmlLabelName(18256, Util.getIntValue(paramString2)) + "，" + SystemEnv.getHtmlLabelName(15242, Util.getIntValue(paramString2)) + arrayOfString[1] + SystemEnv.getHtmlLabelName(18256, Util.getIntValue(paramString2)) + "，" + SystemEnv.getHtmlLabelName(498, Util.getIntValue(paramString2)) + arrayOfString[2] + SystemEnv.getHtmlLabelName(18256, Util.getIntValue(paramString2));
/*  80 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getHrmCardLink(String paramString) {
/*  87 */     ResourceComInfo resourceComInfo = null;
/*     */     try {
/*  89 */       resourceComInfo = new ResourceComInfo();
/*  90 */     } catch (Exception exception) {
/*  91 */       exception.printStackTrace();
/*     */     } 
/*  93 */     String str = resourceComInfo.getLastname(paramString);
/*  94 */     return "<a href=\"javaScript:openhrm(" + paramString + ");\" onclick=\"pointerXY(event);\" title=\"" + str + "\" >" + str + "</a>";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCapitalNameLink(String paramString1, String paramString2) {
/* 102 */     String[] arrayOfString = paramString2.split("\\+");
/* 103 */     String str = "";
/* 104 */     if (arrayOfString[2].equals("1")) {
/* 105 */       str = "<a href=\"javascript:openFullWindowForXtable('/spa/cpt/index.html#/main/cpt/cptcard1?capitalid=" + paramString1 + "')\" >" + arrayOfString[0] + "</a>";
/*     */     } else {
/* 107 */       str = "<a href=\"javascript:openFullWindowForXtable('/spa/cpt/index.html#/main/cpt/cptcard?capitalid=" + paramString1 + "')\" >" + arrayOfString[0] + "</a>";
/*     */     } 
/* 109 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String logRight(User paramUser) throws Exception {
/* 117 */     ResourceComInfo resourceComInfo = new ResourceComInfo();
/* 118 */     String str1 = Util.null2String(Integer.valueOf(paramUser.getUID()));
/* 119 */     String str2 = "";
/* 120 */     String str3 = "";
/* 121 */     if (str1.equals("1")) {
/* 122 */       return str2;
/*     */     }
/* 124 */     str3 = resourceComInfo.getUnderliningByUserId(str1);
/* 125 */     if (!str3.equals("")) {
/* 126 */       str3 = str3 + "," + str1;
/*     */     } else {
/* 128 */       str3 = str1;
/*     */     } 
/* 130 */     str2 = "and submiter in (" + str3 + ")";
/*     */     
/* 132 */     return str2;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/cpt/ExcelToDB/CapitalExcelToDBLog.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */