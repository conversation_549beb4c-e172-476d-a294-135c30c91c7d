/*      */ package weaver.cpt.search;
/*      */ 
/*      */ import java.util.HashMap;
/*      */ import weaver.conn.RecordSet;
/*      */ import weaver.cpt.util.CommonShareManager;
/*      */ import weaver.filter.XssUtil;
/*      */ import weaver.formmode.cuspage.cpt.Cpt4modeUtil;
/*      */ import weaver.general.BaseBean;
/*      */ import weaver.general.GCONST;
/*      */ import weaver.general.Util;
/*      */ import weaver.hrm.User;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ public class CptSearchComInfo
/*      */   extends BaseBean
/*      */ {
/*   19 */   private HashMap<String, String> cusFieldInfo = new HashMap<>();
/*      */   public HashMap<String, String> getCusFieldInfo() {
/*   21 */     return this.cusFieldInfo;
/*      */   }
/*      */   public void setCusFieldInfo(HashMap<String, String> paramHashMap) {
/*   24 */     this.cusFieldInfo = paramHashMap;
/*      */   }
/*   26 */   private String cusSql = "";
/*      */   public String getCusSql() {
/*   28 */     return this.cusSql;
/*      */   }
/*      */   public void setCusSql(String paramString) {
/*   31 */     this.cusSql = paramString;
/*      */   }
/*      */   
/*   34 */   private String mark = "";
/*   35 */   private String name = "";
/*   36 */   private String startdate = "";
/*   37 */   private String startdate1 = "";
/*   38 */   private String enddate = "";
/*   39 */   private String enddate1 = "";
/*   40 */   private String seclevel = "";
/*   41 */   private String seclevel1 = "";
/*   42 */   private String subcompanyid = "";
/*   43 */   private String capblsubid = "";
/*      */   
/*   45 */   private String departmentid = "";
/*   46 */   private String costcenterid = "";
/*   47 */   private String resourceid = "";
/*   48 */   private String currencyid = "";
/*   49 */   private String capitalcost = "";
/*   50 */   private String capitalcost1 = "";
/*   51 */   private String startprice = "";
/*   52 */   private String startprice1 = "";
/*   53 */   private String depreendprice = "";
/*   54 */   private String depreendprice1 = "";
/*   55 */   private String capitalspec = "";
/*   56 */   private String capitallevel = "";
/*   57 */   private String manufacturer = "";
/*   58 */   private String manudate = "";
/*   59 */   private String manudate1 = "";
/*   60 */   private String capitaltypeid = "";
/*   61 */   private String capitalgroupid = "";
/*   62 */   private String capitalgroupid1 = "";
/*   63 */   private String unitid = "";
/*   64 */   private String capitalnum = "";
/*   65 */   private String capitalnum1 = "";
/*   66 */   private String currentnum = "";
/*   67 */   private String currentnum1 = "";
/*   68 */   private String replacecapitalid = "";
/*   69 */   private String version = "";
/*   70 */   private String itemid = "";
/*   71 */   private String depremethod1 = "";
/*   72 */   private String depremethod2 = "";
/*   73 */   private String deprestartdate = "";
/*   74 */   private String deprestartdate1 = "";
/*   75 */   private String depreenddate = "";
/*   76 */   private String depreenddate1 = "";
/*   77 */   private String customerid = "";
/*   78 */   private String attribute = "";
/*   79 */   private String stateid = "";
/*   80 */   private String location = "";
/*   81 */   private String isdata = "";
/*   82 */   private String counttype = "";
/*   83 */   private String isinner = "";
/*   84 */   private String stockindate = "";
/*   85 */   private String stockindate1 = "";
/*      */   
/*   87 */   private String blongsubcompany = "";
/*      */ 
/*      */   
/*   90 */   private String capitalsql = "";
/*   91 */   private String orderby = "";
/*      */ 
/*      */   
/*   94 */   private String fnamark = "";
/*   95 */   private String barcode = "";
/*   96 */   private String blongdepartment = "";
/*   97 */   private String sptcount = "";
/*   98 */   private String relatewfid = "";
/*   99 */   private String SelectDate = "";
/*  100 */   private String SelectDate1 = "";
/*  101 */   private String contractno = "";
/*  102 */   private String Invoice = "";
/*  103 */   private String depreyear = "";
/*  104 */   private String depreyear1 = "";
/*  105 */   private String deprerate = "";
/*  106 */   private String deprerate1 = "";
/*  107 */   private String issupervision = "";
/*  108 */   private String amountpay = "";
/*  109 */   private String amountpay1 = "";
/*  110 */   private String purchasestate = "";
/*  111 */   private String alertnum = "";
/*      */   
/*  113 */   private String datefield1 = "";
/*  114 */   private String datefield11 = "";
/*  115 */   private String datefield2 = "";
/*  116 */   private String datefield22 = "";
/*  117 */   private String datefield3 = "";
/*  118 */   private String datefield33 = "";
/*  119 */   private String datefield4 = "";
/*  120 */   private String datefield44 = "";
/*  121 */   private String datefield5 = "";
/*  122 */   private String datefield55 = "";
/*      */   
/*  124 */   private String numberfield1 = "";
/*  125 */   private String numberfield11 = "";
/*  126 */   private String numberfield2 = "";
/*  127 */   private String numberfield22 = "";
/*  128 */   private String numberfield3 = "";
/*  129 */   private String numberfield33 = "";
/*  130 */   private String numberfield4 = "";
/*  131 */   private String numberfield44 = "";
/*  132 */   private String numberfield5 = "";
/*  133 */   private String numberfield55 = "";
/*      */   
/*  135 */   private String textfield1 = "";
/*  136 */   private String textfield2 = "";
/*  137 */   private String textfield3 = "";
/*  138 */   private String textfield4 = "";
/*  139 */   private String textfield5 = "";
/*      */   
/*  141 */   private String tinyintfield1 = "";
/*  142 */   private String tinyintfield2 = "";
/*  143 */   private String tinyintfield3 = "";
/*  144 */   private String tinyintfield4 = "";
/*  145 */   private String tinyintfield5 = "";
/*      */   
/*  147 */   private String docff01name = "";
/*  148 */   private String docff02name = "";
/*  149 */   private String docff03name = "";
/*  150 */   private String docff04name = "";
/*  151 */   private String docff05name = "";
/*      */   
/*  153 */   private String depff01name = "";
/*  154 */   private String depff02name = "";
/*  155 */   private String depff03name = "";
/*  156 */   private String depff04name = "";
/*  157 */   private String depff05name = "";
/*      */   
/*  159 */   private String crmff01name = "";
/*  160 */   private String crmff02name = "";
/*  161 */   private String crmff03name = "";
/*  162 */   private String crmff04name = "";
/*  163 */   private String crmff05name = "";
/*      */   
/*  165 */   private String reqff01name = "";
/*  166 */   private String reqff02name = "";
/*  167 */   private String reqff03name = "";
/*  168 */   private String reqff04name = "";
/*  169 */   private String reqff05name = "";
/*      */ 
/*      */   
/*  172 */   private CommonShareManager commonShareManager = new CommonShareManager();
/*      */   
/*      */   public String getCrmff01name() {
/*  175 */     return this.crmff01name;
/*      */   }
/*      */   
/*      */   public void setCrmff01name(String paramString) {
/*  179 */     this.crmff01name = paramString;
/*      */   }
/*      */   
/*      */   public String getCrmff02name() {
/*  183 */     return this.crmff02name;
/*      */   }
/*      */   
/*      */   public void setCrmff02name(String paramString) {
/*  187 */     this.crmff02name = paramString;
/*      */   }
/*      */   
/*      */   public String getCrmff03name() {
/*  191 */     return this.crmff03name;
/*      */   }
/*      */   
/*      */   public void setCrmff03name(String paramString) {
/*  195 */     this.crmff03name = paramString;
/*      */   }
/*      */   
/*      */   public String getCrmff04name() {
/*  199 */     return this.crmff04name;
/*      */   }
/*      */   
/*      */   public void setCrmff04name(String paramString) {
/*  203 */     this.crmff04name = paramString;
/*      */   }
/*      */   
/*      */   public String getCrmff05name() {
/*  207 */     return this.crmff05name;
/*      */   }
/*      */   
/*      */   public void setCrmff05name(String paramString) {
/*  211 */     this.crmff05name = paramString;
/*      */   }
/*      */   
/*      */   public String getDatefield1() {
/*  215 */     return this.datefield1;
/*      */   }
/*      */   
/*      */   public void setDatefield1(String paramString) {
/*  219 */     this.datefield1 = paramString;
/*      */   }
/*      */   
/*      */   public String getDatefield11() {
/*  223 */     return this.datefield11;
/*      */   }
/*      */   
/*      */   public void setDatefield11(String paramString) {
/*  227 */     this.datefield11 = paramString;
/*      */   }
/*      */   
/*      */   public String getDatefield2() {
/*  231 */     return this.datefield2;
/*      */   }
/*      */   
/*      */   public void setDatefield2(String paramString) {
/*  235 */     this.datefield2 = paramString;
/*      */   }
/*      */   
/*      */   public String getDatefield22() {
/*  239 */     return this.datefield22;
/*      */   }
/*      */   
/*      */   public void setDatefield22(String paramString) {
/*  243 */     this.datefield22 = paramString;
/*      */   }
/*      */   
/*      */   public String getDatefield3() {
/*  247 */     return this.datefield3;
/*      */   }
/*      */   
/*      */   public void setDatefield3(String paramString) {
/*  251 */     this.datefield3 = paramString;
/*      */   }
/*      */   
/*      */   public String getDatefield33() {
/*  255 */     return this.datefield33;
/*      */   }
/*      */   
/*      */   public void setDatefield33(String paramString) {
/*  259 */     this.datefield33 = paramString;
/*      */   }
/*      */   
/*      */   public String getDatefield4() {
/*  263 */     return this.datefield4;
/*      */   }
/*      */   
/*      */   public void setDatefield4(String paramString) {
/*  267 */     this.datefield4 = paramString;
/*      */   }
/*      */   
/*      */   public String getDatefield44() {
/*  271 */     return this.datefield44;
/*      */   }
/*      */   
/*      */   public void setDatefield44(String paramString) {
/*  275 */     this.datefield44 = paramString;
/*      */   }
/*      */   
/*      */   public String getDatefield5() {
/*  279 */     return this.datefield5;
/*      */   }
/*      */   
/*      */   public void setDatefield5(String paramString) {
/*  283 */     this.datefield5 = paramString;
/*      */   }
/*      */   
/*      */   public String getDatefield55() {
/*  287 */     return this.datefield55;
/*      */   }
/*      */   
/*      */   public void setDatefield55(String paramString) {
/*  291 */     this.datefield55 = paramString;
/*      */   }
/*      */   
/*      */   public String getDepff01name() {
/*  295 */     return this.depff01name;
/*      */   }
/*      */   
/*      */   public void setDepff01name(String paramString) {
/*  299 */     this.depff01name = paramString;
/*      */   }
/*      */   
/*      */   public String getDepff02name() {
/*  303 */     return this.depff02name;
/*      */   }
/*      */   
/*      */   public void setDepff02name(String paramString) {
/*  307 */     this.depff02name = paramString;
/*      */   }
/*      */   
/*      */   public String getDepff03name() {
/*  311 */     return this.depff03name;
/*      */   }
/*      */   
/*      */   public void setDepff03name(String paramString) {
/*  315 */     this.depff03name = paramString;
/*      */   }
/*      */   
/*      */   public String getDepff04name() {
/*  319 */     return this.depff04name;
/*      */   }
/*      */   
/*      */   public void setDepff04name(String paramString) {
/*  323 */     this.depff04name = paramString;
/*      */   }
/*      */   
/*      */   public String getDepff05name() {
/*  327 */     return this.depff05name;
/*      */   }
/*      */   
/*      */   public void setDepff05name(String paramString) {
/*  331 */     this.depff05name = paramString;
/*      */   }
/*      */   
/*      */   public String getDocff01name() {
/*  335 */     return this.docff01name;
/*      */   }
/*      */   
/*      */   public void setDocff01name(String paramString) {
/*  339 */     this.docff01name = paramString;
/*      */   }
/*      */   
/*      */   public String getDocff02name() {
/*  343 */     return this.docff02name;
/*      */   }
/*      */   
/*      */   public void setDocff02name(String paramString) {
/*  347 */     this.docff02name = paramString;
/*      */   }
/*      */   
/*      */   public String getDocff03name() {
/*  351 */     return this.docff03name;
/*      */   }
/*      */   
/*      */   public void setDocff03name(String paramString) {
/*  355 */     this.docff03name = paramString;
/*      */   }
/*      */   
/*      */   public String getDocff04name() {
/*  359 */     return this.docff04name;
/*      */   }
/*      */   
/*      */   public void setDocff04name(String paramString) {
/*  363 */     this.docff04name = paramString;
/*      */   }
/*      */   
/*      */   public String getDocff05name() {
/*  367 */     return this.docff05name;
/*      */   }
/*      */   
/*      */   public void setDocff05name(String paramString) {
/*  371 */     this.docff05name = paramString;
/*      */   }
/*      */   
/*      */   public String getNumberfield1() {
/*  375 */     return this.numberfield1;
/*      */   }
/*      */   
/*      */   public void setNumberfield1(String paramString) {
/*  379 */     this.numberfield1 = paramString;
/*      */   }
/*      */   
/*      */   public String getNumberfield11() {
/*  383 */     return this.numberfield11;
/*      */   }
/*      */   
/*      */   public void setNumberfield11(String paramString) {
/*  387 */     this.numberfield11 = paramString;
/*      */   }
/*      */   
/*      */   public String getNumberfield2() {
/*  391 */     return this.numberfield2;
/*      */   }
/*      */   
/*      */   public void setNumberfield2(String paramString) {
/*  395 */     this.numberfield2 = paramString;
/*      */   }
/*      */   
/*      */   public String getNumberfield22() {
/*  399 */     return this.numberfield22;
/*      */   }
/*      */   
/*      */   public void setNumberfield22(String paramString) {
/*  403 */     this.numberfield22 = paramString;
/*      */   }
/*      */   
/*      */   public String getNumberfield3() {
/*  407 */     return this.numberfield3;
/*      */   }
/*      */   
/*      */   public void setNumberfield3(String paramString) {
/*  411 */     this.numberfield3 = paramString;
/*      */   }
/*      */   
/*      */   public String getNumberfield33() {
/*  415 */     return this.numberfield33;
/*      */   }
/*      */   
/*      */   public void setNumberfield33(String paramString) {
/*  419 */     this.numberfield33 = paramString;
/*      */   }
/*      */   
/*      */   public String getNumberfield4() {
/*  423 */     return this.numberfield4;
/*      */   }
/*      */   
/*      */   public void setNumberfield4(String paramString) {
/*  427 */     this.numberfield4 = paramString;
/*      */   }
/*      */   
/*      */   public String getNumberfield44() {
/*  431 */     return this.numberfield44;
/*      */   }
/*      */   
/*      */   public void setNumberfield44(String paramString) {
/*  435 */     this.numberfield44 = paramString;
/*      */   }
/*      */   
/*      */   public String getNumberfield5() {
/*  439 */     return this.numberfield5;
/*      */   }
/*      */   
/*      */   public void setNumberfield5(String paramString) {
/*  443 */     this.numberfield5 = paramString;
/*      */   }
/*      */   
/*      */   public String getNumberfield55() {
/*  447 */     return this.numberfield55;
/*      */   }
/*      */   
/*      */   public void setNumberfield55(String paramString) {
/*  451 */     this.numberfield55 = paramString;
/*      */   }
/*      */   
/*      */   public String getReqff01name() {
/*  455 */     return this.reqff01name;
/*      */   }
/*      */   
/*      */   public void setReqff01name(String paramString) {
/*  459 */     this.reqff01name = paramString;
/*      */   }
/*      */   
/*      */   public String getReqff02name() {
/*  463 */     return this.reqff02name;
/*      */   }
/*      */   
/*      */   public void setReqff02name(String paramString) {
/*  467 */     this.reqff02name = paramString;
/*      */   }
/*      */   
/*      */   public String getReqff03name() {
/*  471 */     return this.reqff03name;
/*      */   }
/*      */   
/*      */   public void setReqff03name(String paramString) {
/*  475 */     this.reqff03name = paramString;
/*      */   }
/*      */   
/*      */   public String getReqff04name() {
/*  479 */     return this.reqff04name;
/*      */   }
/*      */   
/*      */   public void setReqff04name(String paramString) {
/*  483 */     this.reqff04name = paramString;
/*      */   }
/*      */   
/*      */   public String getReqff05name() {
/*  487 */     return this.reqff05name;
/*      */   }
/*      */   
/*      */   public void setReqff05name(String paramString) {
/*  491 */     this.reqff05name = paramString;
/*      */   }
/*      */   
/*      */   public String getTextfield1() {
/*  495 */     return this.textfield1;
/*      */   }
/*      */   
/*      */   public void setTextfield1(String paramString) {
/*  499 */     this.textfield1 = paramString;
/*      */   }
/*      */   
/*      */   public String getTextfield2() {
/*  503 */     return this.textfield2;
/*      */   }
/*      */   
/*      */   public void setTextfield2(String paramString) {
/*  507 */     this.textfield2 = paramString;
/*      */   }
/*      */   
/*      */   public String getTextfield3() {
/*  511 */     return this.textfield3;
/*      */   }
/*      */   
/*      */   public void setTextfield3(String paramString) {
/*  515 */     this.textfield3 = paramString;
/*      */   }
/*      */   
/*      */   public String getTextfield4() {
/*  519 */     return this.textfield4;
/*      */   }
/*      */   
/*      */   public void setTextfield4(String paramString) {
/*  523 */     this.textfield4 = paramString;
/*      */   }
/*      */   
/*      */   public String getTextfield5() {
/*  527 */     return this.textfield5;
/*      */   }
/*      */   
/*      */   public void setTextfield5(String paramString) {
/*  531 */     this.textfield5 = paramString;
/*      */   }
/*      */   
/*      */   public String getTinyintfield1() {
/*  535 */     return this.tinyintfield1;
/*      */   }
/*      */   
/*      */   public void setTinyintfield1(String paramString) {
/*  539 */     this.tinyintfield1 = paramString;
/*      */   }
/*      */   
/*      */   public String getTinyintfield2() {
/*  543 */     return this.tinyintfield2;
/*      */   }
/*      */   
/*      */   public void setTinyintfield2(String paramString) {
/*  547 */     this.tinyintfield2 = paramString;
/*      */   }
/*      */   
/*      */   public String getTinyintfield3() {
/*  551 */     return this.tinyintfield3;
/*      */   }
/*      */   
/*      */   public void setTinyintfield3(String paramString) {
/*  555 */     this.tinyintfield3 = paramString;
/*      */   }
/*      */   
/*      */   public String getTinyintfield4() {
/*  559 */     return this.tinyintfield4;
/*      */   }
/*      */   
/*      */   public void setTinyintfield4(String paramString) {
/*  563 */     this.tinyintfield4 = paramString;
/*      */   }
/*      */   
/*      */   public String getTinyintfield5() {
/*  567 */     return this.tinyintfield5;
/*      */   }
/*      */   
/*      */   public void setTinyintfield5(String paramString) {
/*  571 */     this.tinyintfield5 = paramString;
/*      */   }
/*      */   
/*      */   public String getAmountpay() {
/*  575 */     return this.amountpay;
/*      */   }
/*      */   
/*      */   public void setAmountpay(String paramString) {
/*  579 */     this.amountpay = paramString;
/*      */   }
/*      */   
/*      */   public String getAmountpay1() {
/*  583 */     return this.amountpay1;
/*      */   }
/*      */   
/*      */   public void setAmountpay1(String paramString) {
/*  587 */     this.amountpay1 = paramString;
/*      */   }
/*      */   
/*      */   public String getBarcode() {
/*  591 */     return this.barcode;
/*      */   }
/*      */   
/*      */   public void setBarcode(String paramString) {
/*  595 */     this.barcode = paramString;
/*      */   }
/*      */   
/*      */   public String getBlongdepartment() {
/*  599 */     return this.blongdepartment;
/*      */   }
/*      */   
/*      */   public void setBlongdepartment(String paramString) {
/*  603 */     this.blongdepartment = paramString;
/*      */   }
/*      */   
/*      */   public String getContractno() {
/*  607 */     return this.contractno;
/*      */   }
/*      */   
/*      */   public void setContractno(String paramString) {
/*  611 */     this.contractno = paramString;
/*      */   }
/*      */   
/*      */   public String getDeprerate() {
/*  615 */     return this.deprerate;
/*      */   }
/*      */   
/*      */   public void setDeprerate(String paramString) {
/*  619 */     this.deprerate = paramString;
/*      */   }
/*      */   
/*      */   public String getDeprerate1() {
/*  623 */     return this.deprerate1;
/*      */   }
/*      */   
/*      */   public void setDeprerate1(String paramString) {
/*  627 */     this.deprerate1 = paramString;
/*      */   }
/*      */   
/*      */   public String getDepreyear() {
/*  631 */     return this.depreyear;
/*      */   }
/*      */   
/*      */   public void setDepreyear(String paramString) {
/*  635 */     this.depreyear = paramString;
/*      */   }
/*      */   
/*      */   public String getDepreyear1() {
/*  639 */     return this.depreyear1;
/*      */   }
/*      */   
/*      */   public void setDepreyear1(String paramString) {
/*  643 */     this.depreyear1 = paramString;
/*      */   }
/*      */   
/*      */   public String getFnamark() {
/*  647 */     return this.fnamark;
/*      */   }
/*      */   
/*      */   public void setFnamark(String paramString) {
/*  651 */     this.fnamark = paramString;
/*      */   }
/*      */   
/*      */   public String getInvoice() {
/*  655 */     return this.Invoice;
/*      */   }
/*      */   
/*      */   public void setInvoice(String paramString) {
/*  659 */     this.Invoice = paramString;
/*      */   }
/*      */   
/*      */   public String getIssupervision() {
/*  663 */     return this.issupervision;
/*      */   }
/*      */   
/*      */   public void setIssupervision(String paramString) {
/*  667 */     this.issupervision = paramString;
/*      */   }
/*      */   
/*      */   public String getPurchasestate() {
/*  671 */     return this.purchasestate;
/*      */   }
/*      */   
/*      */   public void setPurchasestate(String paramString) {
/*  675 */     this.purchasestate = paramString;
/*      */   }
/*      */   
/*      */   public String getRelatewfid() {
/*  679 */     return this.relatewfid;
/*      */   }
/*      */   
/*      */   public void setRelatewfid(String paramString) {
/*  683 */     this.relatewfid = paramString;
/*      */   }
/*      */   
/*      */   public String getSelectDate() {
/*  687 */     return this.SelectDate;
/*      */   }
/*      */   
/*      */   public void setSelectDate(String paramString) {
/*  691 */     this.SelectDate = paramString;
/*      */   }
/*      */   
/*      */   public String getSelectDate1() {
/*  695 */     return this.SelectDate1;
/*      */   }
/*      */   
/*      */   public void setSelectDate1(String paramString) {
/*  699 */     this.SelectDate1 = paramString;
/*      */   }
/*      */   
/*      */   public String getSptcount() {
/*  703 */     return this.sptcount;
/*      */   }
/*      */   
/*      */   public void setSptcount(String paramString) {
/*  707 */     this.sptcount = paramString;
/*      */   }
/*      */   
/*      */   public void setAlertnum(String paramString) {
/*  711 */     this.alertnum = paramString;
/*      */   }
/*      */   
/*      */   public String getAlertnum() {
/*  715 */     return this.alertnum;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void resetSearchInfo() {
/*  724 */     this.cusFieldInfo = new HashMap<>();
/*  725 */     this.cusSql = "";
/*  726 */     this.mark = "";
/*  727 */     this.name = "";
/*  728 */     this.startdate = "";
/*  729 */     this.startdate1 = "";
/*  730 */     this.enddate = "";
/*  731 */     this.enddate1 = "";
/*  732 */     this.seclevel = "";
/*  733 */     this.seclevel1 = "";
/*  734 */     this.subcompanyid = "";
/*  735 */     this.departmentid = "";
/*  736 */     this.costcenterid = "";
/*  737 */     this.resourceid = "";
/*  738 */     this.currencyid = "";
/*  739 */     this.capitalcost = "";
/*  740 */     this.capitalcost1 = "";
/*  741 */     this.startprice = "";
/*  742 */     this.startprice1 = "";
/*  743 */     this.depreendprice = "";
/*  744 */     this.depreendprice1 = "";
/*  745 */     this.capitalspec = "";
/*  746 */     this.capitallevel = "";
/*  747 */     this.manufacturer = "";
/*  748 */     this.manudate = "";
/*  749 */     this.manudate1 = "";
/*  750 */     this.capitaltypeid = "";
/*  751 */     this.capitalgroupid = "";
/*  752 */     this.unitid = "";
/*  753 */     this.capitalnum = "";
/*  754 */     this.capitalnum1 = "";
/*  755 */     this.currentnum = "";
/*  756 */     this.currentnum1 = "";
/*  757 */     this.replacecapitalid = "";
/*  758 */     this.version = "";
/*  759 */     this.itemid = "";
/*  760 */     this.depremethod1 = "";
/*  761 */     this.depremethod2 = "";
/*  762 */     this.deprestartdate = "";
/*  763 */     this.deprestartdate1 = "";
/*  764 */     this.depreenddate = "";
/*  765 */     this.depreenddate1 = "";
/*  766 */     this.customerid = "";
/*  767 */     this.attribute = "";
/*  768 */     this.stateid = "";
/*  769 */     this.location = "";
/*  770 */     this.isdata = "";
/*  771 */     this.counttype = "";
/*  772 */     this.isinner = "";
/*  773 */     this.stockindate = "";
/*  774 */     this.stockindate1 = "";
/*  775 */     this.blongsubcompany = "";
/*  776 */     this.capblsubid = "";
/*      */     
/*  778 */     this.fnamark = "";
/*  779 */     this.barcode = "";
/*  780 */     this.blongdepartment = "";
/*  781 */     this.sptcount = "";
/*  782 */     this.relatewfid = "";
/*  783 */     this.SelectDate = "";
/*  784 */     this.SelectDate1 = "";
/*  785 */     this.contractno = "";
/*  786 */     this.Invoice = "";
/*  787 */     this.depreyear = "";
/*  788 */     this.depreyear1 = "";
/*  789 */     this.deprerate = "";
/*  790 */     this.deprerate1 = "";
/*  791 */     this.issupervision = "";
/*  792 */     this.amountpay = "";
/*  793 */     this.amountpay1 = "";
/*  794 */     this.purchasestate = "";
/*  795 */     this.alertnum = "";
/*      */     
/*  797 */     this.datefield1 = "";
/*  798 */     this.datefield11 = "";
/*  799 */     this.datefield2 = "";
/*  800 */     this.datefield22 = "";
/*  801 */     this.datefield3 = "";
/*  802 */     this.datefield33 = "";
/*  803 */     this.datefield4 = "";
/*  804 */     this.datefield44 = "";
/*  805 */     this.datefield5 = "";
/*  806 */     this.datefield55 = "";
/*      */     
/*  808 */     this.numberfield1 = "";
/*  809 */     this.numberfield11 = "";
/*  810 */     this.numberfield2 = "";
/*  811 */     this.numberfield22 = "";
/*  812 */     this.numberfield3 = "";
/*  813 */     this.numberfield33 = "";
/*  814 */     this.numberfield4 = "";
/*  815 */     this.numberfield44 = "";
/*  816 */     this.numberfield5 = "";
/*  817 */     this.numberfield55 = "";
/*      */     
/*  819 */     this.textfield1 = "";
/*  820 */     this.textfield2 = "";
/*  821 */     this.textfield3 = "";
/*  822 */     this.textfield4 = "";
/*  823 */     this.textfield5 = "";
/*      */     
/*  825 */     this.tinyintfield1 = "";
/*  826 */     this.tinyintfield2 = "";
/*  827 */     this.tinyintfield3 = "";
/*  828 */     this.tinyintfield4 = "";
/*  829 */     this.tinyintfield5 = "";
/*      */     
/*  831 */     this.docff01name = "";
/*  832 */     this.docff02name = "";
/*  833 */     this.docff03name = "";
/*  834 */     this.docff04name = "";
/*  835 */     this.docff05name = "";
/*      */     
/*  837 */     this.depff01name = "";
/*  838 */     this.depff02name = "";
/*  839 */     this.depff03name = "";
/*  840 */     this.depff04name = "";
/*  841 */     this.depff05name = "";
/*      */     
/*  843 */     this.crmff01name = "";
/*  844 */     this.crmff02name = "";
/*  845 */     this.crmff03name = "";
/*  846 */     this.crmff04name = "";
/*  847 */     this.crmff05name = "";
/*      */     
/*  849 */     this.reqff01name = "";
/*  850 */     this.reqff02name = "";
/*  851 */     this.reqff03name = "";
/*  852 */     this.reqff04name = "";
/*  853 */     this.reqff05name = "";
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setCapitalsql(String paramString) {
/*  862 */     this.capitalsql = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getCapitalsql() {
/*  870 */     return this.capitalsql;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setOrderby(String paramString) {
/*  878 */     this.orderby = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getOrderby() {
/*  886 */     return this.orderby;
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   public String getBlongsubcompany() {
/*  892 */     return this.blongsubcompany;
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   public void setBlongsubcompany(String paramString) {
/*  898 */     this.blongsubcompany = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   public String getCapblsubid() {
/*  904 */     return this.capblsubid;
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   public void setCapblsubid(String paramString) {
/*  910 */     this.capblsubid = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setMark(String paramString) {
/*  918 */     this.mark = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getMark() {
/*  926 */     return this.mark;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setName(String paramString) {
/*  934 */     this.name = paramString.trim();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getName() {
/*  942 */     return this.name;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setStartdate(String paramString) {
/*  950 */     this.startdate = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getStartdate() {
/*  958 */     return this.startdate;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setStartdate1(String paramString) {
/*  966 */     this.startdate1 = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getStartdate1() {
/*  974 */     return this.startdate1;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setEnddate(String paramString) {
/*  982 */     this.enddate = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getEnddate() {
/*  990 */     return this.enddate;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setEnddate1(String paramString) {
/*  998 */     this.enddate1 = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getEnddate1() {
/* 1006 */     return this.enddate1;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setSeclevel(String paramString) {
/* 1014 */     this.seclevel = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getSeclevel() {
/* 1022 */     return this.seclevel;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setSeclevel1(String paramString) {
/* 1030 */     this.seclevel1 = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getSeclevel1() {
/* 1038 */     return this.seclevel1;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setSubcompanyid(String paramString) {
/* 1046 */     this.subcompanyid = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getSubcompanyid() {
/* 1054 */     return this.subcompanyid;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setDepartmentid(String paramString) {
/* 1062 */     this.departmentid = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getDepartmentid() {
/* 1070 */     return this.departmentid;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setCostcenterid(String paramString) {
/* 1078 */     this.costcenterid = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getCostcenterid() {
/* 1086 */     return this.costcenterid;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setResourceid(String paramString) {
/* 1094 */     this.resourceid = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getResourceid() {
/* 1102 */     return this.resourceid;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setCurrencyid(String paramString) {
/* 1110 */     this.currencyid = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getCurrencyid() {
/* 1118 */     return this.currencyid;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setCapitalcost(String paramString) {
/* 1126 */     this.capitalcost = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getCapitalcost() {
/* 1134 */     return this.capitalcost;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setCapitalcost1(String paramString) {
/* 1142 */     this.capitalcost1 = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getCapitalcost1() {
/* 1150 */     return this.capitalcost1;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setStartprice(String paramString) {
/* 1158 */     this.startprice = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getStartprice() {
/* 1166 */     return this.startprice;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setStartprice1(String paramString) {
/* 1174 */     this.startprice1 = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getStartprice1() {
/* 1182 */     return this.startprice1;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setDepreendprice(String paramString) {
/* 1190 */     this.depreendprice = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getDepreendprice() {
/* 1198 */     return this.depreendprice;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setDepreendprice1(String paramString) {
/* 1206 */     this.depreendprice1 = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getDepreendprice1() {
/* 1214 */     return this.depreendprice1;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setCapitalspec(String paramString) {
/* 1222 */     this.capitalspec = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getCapitalspec() {
/* 1230 */     return this.capitalspec;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setCapitallevel(String paramString) {
/* 1238 */     this.capitallevel = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getCapitallevel() {
/* 1246 */     return this.capitallevel;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setManufacturer(String paramString) {
/* 1254 */     this.manufacturer = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getManufacturer() {
/* 1262 */     return this.manufacturer;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setManudate(String paramString) {
/* 1270 */     this.manudate = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getManudate() {
/* 1278 */     return this.manudate;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setManudate1(String paramString) {
/* 1286 */     this.manudate1 = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getManudate1() {
/* 1294 */     return this.manudate1;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setCapitaltypeid(String paramString) {
/* 1302 */     this.capitaltypeid = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getCapitaltypeid() {
/* 1310 */     return this.capitaltypeid;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setCapitalgroupid(String paramString) {
/* 1318 */     this.capitalgroupid = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getCapitalgroupid() {
/* 1326 */     return this.capitalgroupid;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setCapitalgroupid1(String paramString) {
/* 1333 */     this.capitalgroupid1 = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getCapitalgroupid1() {
/* 1341 */     return this.capitalgroupid1;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setUnitid(String paramString) {
/* 1349 */     this.unitid = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getUnitid() {
/* 1357 */     return this.unitid;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setCapitalnum(String paramString) {
/* 1365 */     this.capitalnum = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getCapitalnum() {
/* 1373 */     return this.capitalnum;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setCapitalnum1(String paramString) {
/* 1381 */     this.capitalnum1 = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getCapitalnum1() {
/* 1389 */     return this.capitalnum1;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setCurrentnum(String paramString) {
/* 1397 */     this.currentnum = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getCurrentnum() {
/* 1405 */     return this.currentnum;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setCurrentnum1(String paramString) {
/* 1413 */     this.currentnum1 = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getCurrentnum1() {
/* 1421 */     return this.currentnum1;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setReplacecapitalid(String paramString) {
/* 1429 */     this.replacecapitalid = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getReplacecapitalid() {
/* 1437 */     return this.replacecapitalid;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setVersion(String paramString) {
/* 1445 */     this.version = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getVersion() {
/* 1453 */     return this.version;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setItemid(String paramString) {
/* 1461 */     this.itemid = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getItemid() {
/* 1469 */     return this.itemid;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setDepremethod1(String paramString) {
/* 1477 */     this.depremethod1 = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getDepremethod1() {
/* 1485 */     return this.depremethod1;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setDepremethod2(String paramString) {
/* 1493 */     this.depremethod2 = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getDepremethod2() {
/* 1501 */     return this.depremethod2;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setDeprestartdate(String paramString) {
/* 1509 */     this.deprestartdate = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getDeprestartdate() {
/* 1517 */     return this.deprestartdate;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setDeprestartdate1(String paramString) {
/* 1525 */     this.deprestartdate1 = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getDeprestartdate1() {
/* 1533 */     return this.deprestartdate1;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setDepreenddate(String paramString) {
/* 1541 */     this.depreenddate = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getDepreenddate() {
/* 1549 */     return this.depreenddate;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setDepreenddate1(String paramString) {
/* 1557 */     this.depreenddate1 = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getDepreenddate1() {
/* 1565 */     return this.depreenddate1;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setCustomerid(String paramString) {
/* 1573 */     this.customerid = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getCustomerid() {
/* 1581 */     return this.customerid;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setAttribute(String paramString) {
/* 1589 */     this.attribute = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getAttribute() {
/* 1597 */     return this.attribute;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setStateid(String paramString) {
/* 1605 */     this.stateid = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getStateid() {
/* 1613 */     return this.stateid;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setLocation(String paramString) {
/* 1621 */     this.location = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getLocation() {
/* 1629 */     return this.location;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setIsData(String paramString) {
/* 1637 */     this.isdata = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getIsData() {
/* 1645 */     return this.isdata;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setCountType(String paramString) {
/* 1653 */     this.counttype = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getCountType() {
/* 1661 */     return this.counttype;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setIsInner(String paramString) {
/* 1669 */     this.isinner = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getIsInner() {
/* 1677 */     return this.isinner;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getStockindate() {
/* 1685 */     return this.stockindate;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setStockindate(String paramString) {
/* 1693 */     this.stockindate = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getStockindate1() {
/* 1701 */     return this.stockindate1;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setStockindate1(String paramString) {
/* 1709 */     this.stockindate1 = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String FormatSQLSearch() {
/* 1717 */     String str1 = "";
/* 1718 */     boolean bool = false;
/* 1719 */     if (!this.isdata.equals("")) {
/* 1720 */       if (!bool) {
/* 1721 */         bool = true;
/* 1722 */         str1 = " where isdata = '" + this.isdata + "' ";
/*      */       } else {
/* 1724 */         str1 = str1 + " and isdata = '" + this.isdata + "' ";
/*      */       } 
/*      */     }
/*      */     
/* 1728 */     if (!this.mark.equals("")) {
/* 1729 */       if (!bool) {
/* 1730 */         bool = true;
/* 1731 */         str1 = " where mark like '%" + this.mark + "%' ";
/*      */       } else {
/* 1733 */         str1 = str1 + " and mark like '%" + this.mark + "%' ";
/*      */       } 
/*      */     }
/*      */     
/* 1737 */     if (!this.blongsubcompany.equals("")) {
/* 1738 */       if (!bool) {
/* 1739 */         bool = true;
/* 1740 */         str1 = " where blongsubcompany in (" + this.blongsubcompany + ") ";
/*      */       } else {
/* 1742 */         str1 = str1 + " and blongsubcompany  in (" + this.blongsubcompany + ") ";
/*      */       } 
/*      */     }
/*      */     
/* 1746 */     if (!this.capblsubid.equals("") && !this.capblsubid.equals("-1")) {
/* 1747 */       if (!bool) {
/* 1748 */         bool = true;
/* 1749 */         str1 = " where blongsubcompany =" + this.capblsubid + " ";
/*      */       } else {
/* 1751 */         str1 = str1 + " and blongsubcompany =" + this.capblsubid + " ";
/*      */       } 
/*      */     }
/* 1754 */     if (!this.name.equals("")) {
/* 1755 */       if (!bool) {
/* 1756 */         bool = true;
/* 1757 */         str1 = " where name like '%" + this.name + "%' ";
/*      */       } else {
/* 1759 */         str1 = str1 + " and name like '%" + this.name + "%' ";
/*      */       } 
/*      */     }
/*      */     
/* 1763 */     if (!this.startdate.equals("")) {
/* 1764 */       if (!bool) {
/* 1765 */         bool = true;
/* 1766 */         str1 = " where startdate >= '" + this.startdate + "' ";
/*      */       } else {
/* 1768 */         str1 = str1 + " and startdate >= '" + this.startdate + "' ";
/*      */       } 
/*      */     }
/*      */     
/* 1772 */     if (!this.startdate1.equals("")) {
/* 1773 */       if (!bool) {
/* 1774 */         bool = true;
/* 1775 */         str1 = " where startdate <= '" + this.startdate1 + "' ";
/*      */       } else {
/* 1777 */         str1 = str1 + " and startdate <= '" + this.startdate1 + "' ";
/*      */       } 
/*      */     }
/*      */     
/* 1781 */     if (!this.enddate.equals("")) {
/* 1782 */       if (!bool) {
/* 1783 */         bool = true;
/* 1784 */         str1 = " where enddate >= '" + this.enddate + "' ";
/*      */       } else {
/* 1786 */         str1 = str1 + " and enddate >= '" + this.enddate + "' ";
/*      */       } 
/*      */     }
/*      */     
/* 1790 */     if (!this.enddate1.equals("")) {
/* 1791 */       if (!bool) {
/* 1792 */         bool = true;
/* 1793 */         str1 = " where enddate <= '" + this.enddate1 + "' and (enddate<>'' or enddate is not null) ";
/*      */       } else {
/* 1795 */         str1 = str1 + " and enddate <= '" + this.enddate1 + "' and (enddate<>'' or enddate is not null) ";
/*      */       } 
/*      */     }
/*      */     
/* 1799 */     if (!this.seclevel.equals("")) {
/* 1800 */       if (!bool) {
/* 1801 */         bool = true;
/* 1802 */         str1 = " where seclevel >= " + this.seclevel;
/*      */       } else {
/* 1804 */         str1 = str1 + " and seclevel >= " + this.seclevel;
/*      */       } 
/*      */     }
/*      */     
/* 1808 */     if (!this.seclevel1.equals("")) {
/* 1809 */       if (!bool) {
/* 1810 */         bool = true;
/* 1811 */         str1 = " where seclevel <= " + this.seclevel1;
/*      */       } else {
/* 1813 */         str1 = str1 + " and seclevel <= " + this.seclevel1;
/*      */       } 
/*      */     }
/*      */     
/* 1817 */     if (!this.subcompanyid.equals("")) {
/* 1818 */       if (this.isdata.equals("1")) {
/* 1819 */         if (!bool) {
/* 1820 */           bool = true;
/* 1821 */           str1 = " where blongsubcompany =" + this.subcompanyid + " ";
/*      */         } else {
/* 1823 */           str1 = str1 + " and blongsubcompany =" + this.subcompanyid + " ";
/*      */         }
/*      */       
/* 1826 */       } else if (!this.departmentid.equals("")) {
/* 1827 */         if (!bool) {
/* 1828 */           bool = true;
/* 1829 */           str1 = " where departmentid in(select id from HrmDepartment where subcompanyid1 in(" + this.subcompanyid + "))";
/*      */         } else {
/* 1831 */           str1 = str1 + " and departmentid in(select id from HrmDepartment where subcompanyid1 in(" + this.subcompanyid + "))";
/*      */         } 
/*      */       } 
/*      */     }
/*      */ 
/*      */     
/* 1837 */     if (!this.departmentid.equals("")) {
/* 1838 */       if (!bool) {
/* 1839 */         bool = true;
/* 1840 */         str1 = " where departmentid = " + this.departmentid;
/*      */       } else {
/* 1842 */         str1 = str1 + " and departmentid = " + this.departmentid;
/*      */       } 
/*      */     }
/*      */     
/* 1846 */     if (!this.costcenterid.equals("")) {
/* 1847 */       if (!bool) {
/* 1848 */         bool = true;
/* 1849 */         str1 = " where costcenterid = " + this.costcenterid;
/*      */       } else {
/* 1851 */         str1 = str1 + " and costcenterid = " + this.costcenterid;
/*      */       } 
/*      */     }
/*      */     
/* 1855 */     if (!this.resourceid.equals("")) {
/* 1856 */       if (!bool) {
/* 1857 */         bool = true;
/* 1858 */         str1 = " where resourceid in( " + (new CommonShareManager()).getContainsSubuserids(this.resourceid) + ") ";
/*      */       } else {
/* 1860 */         str1 = str1 + " and resourceid in( " + (new CommonShareManager()).getContainsSubuserids(this.resourceid) + ") ";
/*      */       } 
/*      */     }
/*      */     
/* 1864 */     if (!this.currencyid.equals("")) {
/* 1865 */       if (!bool) {
/* 1866 */         bool = true;
/* 1867 */         str1 = " where currencyid = " + this.currencyid;
/*      */       } else {
/* 1869 */         str1 = str1 + " and currencyid = " + this.currencyid;
/*      */       } 
/*      */     }
/*      */     
/* 1873 */     if (!this.capitalcost.equals("")) {
/* 1874 */       if (!bool) {
/* 1875 */         bool = true;
/* 1876 */         str1 = " where capitalcost >= " + this.capitalcost;
/*      */       } else {
/* 1878 */         str1 = str1 + " and capitalcost >= " + this.capitalcost;
/*      */       } 
/*      */     }
/*      */     
/* 1882 */     if (!this.capitalcost1.equals("")) {
/* 1883 */       if (!bool) {
/* 1884 */         bool = true;
/* 1885 */         str1 = " where capitalcost <= " + this.capitalcost1;
/*      */       } else {
/* 1887 */         str1 = str1 + " and capitalcost <= " + this.capitalcost1;
/*      */       } 
/*      */     }
/*      */     
/* 1891 */     if (!this.startprice.equals("")) {
/* 1892 */       if (!bool) {
/* 1893 */         bool = true;
/* 1894 */         str1 = " where startprice >= " + this.startprice;
/*      */       } else {
/* 1896 */         str1 = str1 + " and startprice >= " + this.startprice;
/*      */       } 
/*      */     }
/*      */     
/* 1900 */     if (!this.startprice1.equals("")) {
/* 1901 */       if (!bool) {
/* 1902 */         bool = true;
/* 1903 */         str1 = " where startprice <= " + this.startprice1;
/*      */       } else {
/* 1905 */         str1 = str1 + " and startprice <= " + this.startprice1;
/*      */       } 
/*      */     }
/*      */     
/* 1909 */     if (!this.depreendprice.equals("")) {
/* 1910 */       if (!bool) {
/* 1911 */         bool = true;
/* 1912 */         str1 = " where depreendprice >= " + this.depreendprice;
/*      */       } else {
/* 1914 */         str1 = str1 + " and depreendprice >= " + this.depreendprice;
/*      */       } 
/*      */     }
/*      */     
/* 1918 */     if (!this.depreendprice1.equals("")) {
/* 1919 */       if (!bool) {
/* 1920 */         bool = true;
/* 1921 */         str1 = " where depreendprice <= " + this.depreendprice1;
/*      */       } else {
/* 1923 */         str1 = str1 + " and depreendprice <= " + this.depreendprice1;
/*      */       } 
/*      */     }
/*      */     
/* 1927 */     if (!this.capitalspec.equals("")) {
/* 1928 */       if (!bool) {
/* 1929 */         bool = true;
/* 1930 */         str1 = " where capitalspec like '%" + this.capitalspec + "%' ";
/*      */       } else {
/* 1932 */         str1 = str1 + " and capitalspec like '%" + this.capitalspec + "%' ";
/*      */       } 
/*      */     }
/*      */     
/* 1936 */     if (!this.capitallevel.equals("")) {
/* 1937 */       if (!bool) {
/* 1938 */         bool = true;
/* 1939 */         str1 = " where capitallevel like '%" + this.capitallevel + "%' ";
/*      */       } else {
/* 1941 */         str1 = str1 + " and capitallevel like '%" + this.capitallevel + "%' ";
/*      */       } 
/*      */     }
/*      */     
/* 1945 */     if (!this.manufacturer.equals("")) {
/* 1946 */       if (!bool) {
/* 1947 */         bool = true;
/* 1948 */         str1 = " where manufacturer like '%" + this.manufacturer + "%' ";
/*      */       } else {
/* 1950 */         str1 = str1 + " and manufacturer like '%" + this.manufacturer + "%' ";
/*      */       } 
/*      */     }
/*      */     
/* 1954 */     if (!this.manudate.equals("")) {
/* 1955 */       if (!bool) {
/* 1956 */         bool = true;
/* 1957 */         str1 = " where manudate >='" + this.manudate + "' ";
/*      */       } else {
/* 1959 */         str1 = str1 + " and manudate >='" + this.manudate + "' ";
/*      */       } 
/*      */     }
/*      */     
/* 1963 */     if (!this.manudate1.equals("")) {
/* 1964 */       if (!bool) {
/* 1965 */         bool = true;
/* 1966 */         str1 = " where manudate <='" + this.manudate1 + "' ";
/*      */       } else {
/* 1968 */         str1 = str1 + " and manudate <='" + this.manudate1 + "' ";
/*      */       } 
/*      */     }
/*      */     
/* 1972 */     if (!this.capitaltypeid.equals("")) {
/* 1973 */       if (!bool) {
/* 1974 */         bool = true;
/* 1975 */         str1 = " where capitaltypeid =" + this.capitaltypeid;
/*      */       } else {
/* 1977 */         str1 = str1 + " and capitaltypeid =" + this.capitaltypeid;
/*      */       } 
/*      */     }
/*      */     
/* 1981 */     if (!this.capitalgroupid.equals("")) {
/* 1982 */       if (!bool) {
/* 1983 */         bool = true;
/* 1984 */         str1 = " where (capitalgroupid =" + this.capitalgroupid + " or capitalgroupid in (select id from CptCapitalAssortment where supassortmentstr like '%|" + this.capitalgroupid + "|%' ))";
/*      */       } else {
/* 1986 */         str1 = str1 + " and (capitalgroupid =" + this.capitalgroupid + " or capitalgroupid in (select id from CptCapitalAssortment where supassortmentstr like '%|" + this.capitalgroupid + "|%' )) ";
/*      */       } 
/* 1988 */       this.capitalgroupid1 = this.capitalgroupid;
/* 1989 */     } else if (!this.capitalgroupid1.equals("") && "".equals(this.capitalgroupid)) {
/* 1990 */       if (!bool) {
/* 1991 */         bool = true;
/* 1992 */         str1 = " where (capitalgroupid =" + this.capitalgroupid1 + " or capitalgroupid in (select id from CptCapitalAssortment where supassortmentstr like '%|" + this.capitalgroupid1 + "|%' ))";
/*      */       } else {
/* 1994 */         str1 = str1 + " and (capitalgroupid =" + this.capitalgroupid1 + " or capitalgroupid in (select id from CptCapitalAssortment where supassortmentstr like '%|" + this.capitalgroupid1 + "|%' )) ";
/*      */       } 
/*      */     } 
/*      */     
/* 1998 */     if (!this.unitid.equals("")) {
/* 1999 */       if (!bool) {
/* 2000 */         bool = true;
/* 2001 */         str1 = " where unitid =" + this.unitid;
/*      */       } else {
/* 2003 */         str1 = str1 + " and unitid =" + this.unitid;
/*      */       } 
/*      */     }
/*      */     
/* 2007 */     if (!this.capitalnum.equals("")) {
/* 2008 */       if (!bool) {
/* 2009 */         bool = true;
/* 2010 */         str1 = " where capitalnum >=" + this.capitalnum;
/*      */       } else {
/* 2012 */         str1 = str1 + " and capitalnum >=" + this.capitalnum;
/*      */       } 
/*      */     }
/*      */     
/* 2016 */     if (!this.capitalnum1.equals("")) {
/* 2017 */       if (!bool) {
/* 2018 */         bool = true;
/* 2019 */         str1 = " where capitalnum <=" + this.capitalnum1;
/*      */       } else {
/* 2021 */         str1 = str1 + " and capitalnum <=" + this.capitalnum1;
/*      */       } 
/*      */     }
/*      */     
/* 2025 */     if (!this.currentnum.equals("")) {
/* 2026 */       if (!bool) {
/* 2027 */         bool = true;
/* 2028 */         str1 = " where currentnum >=" + this.currentnum;
/*      */       } else {
/* 2030 */         str1 = str1 + " and currentnum >=" + this.currentnum;
/*      */       } 
/*      */     }
/*      */     
/* 2034 */     if (!this.currentnum1.equals("")) {
/* 2035 */       if (!bool) {
/* 2036 */         bool = true;
/* 2037 */         str1 = " where currentnum <=" + this.currentnum1;
/*      */       } else {
/* 2039 */         str1 = str1 + " and currentnum <=" + this.currentnum1;
/*      */       } 
/*      */     }
/*      */     
/* 2043 */     if (!this.replacecapitalid.equals("")) {
/* 2044 */       if (!bool) {
/* 2045 */         bool = true;
/* 2046 */         str1 = " where replacecapitalid =" + this.replacecapitalid;
/*      */       } else {
/* 2048 */         str1 = str1 + " and replacecapitalid =" + this.replacecapitalid;
/*      */       } 
/*      */     }
/*      */     
/* 2052 */     if (!this.version.equals("")) {
/* 2053 */       if (!bool) {
/* 2054 */         bool = true;
/* 2055 */         str1 = " where version like '%" + this.version + "%' ";
/*      */       } else {
/* 2057 */         str1 = str1 + " and version like '%" + this.version + "%' ";
/*      */       } 
/*      */     }
/*      */     
/* 2061 */     if (!this.itemid.equals("")) {
/* 2062 */       if (!bool) {
/* 2063 */         bool = true;
/* 2064 */         str1 = " where itemid =" + this.itemid;
/*      */       } else {
/* 2066 */         str1 = str1 + " and itemid =" + this.itemid;
/*      */       } 
/*      */     }
/*      */     
/* 2070 */     if (!this.depremethod1.equals("")) {
/* 2071 */       if (!bool) {
/* 2072 */         bool = true;
/* 2073 */         str1 = " where depremethod1 =" + this.depremethod1;
/*      */       } else {
/* 2075 */         str1 = str1 + " and depremethod1 =" + this.depremethod1;
/*      */       } 
/*      */     }
/*      */     
/* 2079 */     if (!this.depremethod2.equals("")) {
/* 2080 */       if (!bool) {
/* 2081 */         bool = true;
/* 2082 */         str1 = " where depremethod2 =" + this.depremethod2;
/*      */       } else {
/* 2084 */         str1 = str1 + " and depremethod2 =" + this.depremethod2;
/*      */       } 
/*      */     }
/*      */     
/* 2088 */     if (!this.deprestartdate.equals("")) {
/* 2089 */       if (!bool) {
/* 2090 */         bool = true;
/* 2091 */         str1 = " where deprestartdate >='" + this.deprestartdate + "' ";
/*      */       } else {
/* 2093 */         str1 = str1 + " and deprestartdate >='" + this.deprestartdate + "' ";
/*      */       } 
/*      */     }
/*      */     
/* 2097 */     if (!this.deprestartdate1.equals("")) {
/* 2098 */       if (!bool) {
/* 2099 */         bool = true;
/* 2100 */         str1 = " where deprestartdate <='" + this.deprestartdate1 + "' ";
/*      */       } else {
/* 2102 */         str1 = str1 + " and deprestartdate <='" + this.deprestartdate1 + "' ";
/*      */       } 
/*      */     }
/*      */     
/* 2106 */     if (!this.depreenddate.equals("")) {
/* 2107 */       if (!bool) {
/* 2108 */         bool = true;
/* 2109 */         str1 = " where depreenddate >='" + this.depreenddate + "' ";
/*      */       } else {
/* 2111 */         str1 = str1 + " and depreenddate >='" + this.depreenddate + "' ";
/*      */       } 
/*      */     }
/*      */     
/* 2115 */     if (!this.depreenddate1.equals("")) {
/* 2116 */       if (!bool) {
/* 2117 */         bool = true;
/* 2118 */         str1 = " where depreenddate <='" + this.depreenddate1 + "' ";
/*      */       } else {
/* 2120 */         str1 = str1 + " and depreenddate <='" + this.depreenddate1 + "' ";
/*      */       } 
/*      */     }
/*      */     
/* 2124 */     if (!this.customerid.equals("")) {
/* 2125 */       if (!bool) {
/* 2126 */         bool = true;
/* 2127 */         str1 = " where customerid =" + this.customerid;
/*      */       } else {
/* 2129 */         str1 = str1 + " and customerid =" + this.customerid;
/*      */       } 
/*      */     }
/*      */     
/* 2133 */     if (!this.attribute.equals("")) {
/* 2134 */       if (!bool) {
/* 2135 */         bool = true;
/* 2136 */         str1 = " where attribute =" + this.attribute;
/*      */       } else {
/* 2138 */         str1 = str1 + " and attribute =" + this.attribute;
/*      */       } 
/*      */     }
/*      */     
/* 2142 */     if (!this.stateid.equals("")) {
/* 2143 */       if (!bool) {
/* 2144 */         bool = true;
/* 2145 */         str1 = " where stateid =" + this.stateid;
/*      */       } else {
/* 2147 */         str1 = str1 + " and stateid =" + this.stateid;
/*      */       } 
/*      */     }
/*      */     
/* 2151 */     if (!this.location.equals("")) {
/* 2152 */       if (!bool) {
/* 2153 */         bool = true;
/* 2154 */         str1 = " where location like '%" + this.location + "%' ";
/*      */       } else {
/* 2156 */         str1 = str1 + " and location like '%" + this.location + "%' ";
/*      */       } 
/*      */     }
/*      */ 
/*      */     
/* 2161 */     if (!this.counttype.equals("")) {
/* 2162 */       if (this.counttype.equals("3")) {
/* 2163 */         if (!bool) {
/* 2164 */           bool = true;
/* 2165 */           str1 = " where (counttype = '1' or counttype = '2') ";
/*      */         } else {
/* 2167 */           str1 = str1 + " and (counttype = '1' or counttype = '2') ";
/*      */         }
/*      */       
/*      */       }
/* 2171 */       else if (!bool) {
/* 2172 */         bool = true;
/* 2173 */         str1 = " where counttype = '" + this.counttype + "' ";
/*      */       } else {
/* 2175 */         str1 = str1 + " and counttype = '" + this.counttype + "' ";
/*      */       } 
/*      */     }
/*      */ 
/*      */     
/* 2180 */     if (!this.isinner.equals("")) {
/* 2181 */       if (!bool) {
/* 2182 */         bool = true;
/* 2183 */         str1 = " where isinner = '" + this.isinner + "' ";
/*      */       } else {
/* 2185 */         str1 = str1 + " and isinner = '" + this.isinner + "' ";
/*      */       } 
/*      */     }
/*      */     
/* 2189 */     if (!this.stockindate.equals("")) {
/* 2190 */       if (!bool) {
/* 2191 */         bool = true;
/* 2192 */         str1 = " where stockindate >= '" + this.stockindate + "' ";
/*      */       } else {
/* 2194 */         str1 = str1 + " and stockindate >= '" + this.stockindate + "' ";
/*      */       } 
/*      */     }
/*      */     
/* 2198 */     if (!this.stockindate1.equals("")) {
/* 2199 */       if (!bool) {
/* 2200 */         bool = true;
/* 2201 */         str1 = " where stockindate <= '" + this.stockindate1 + "' ";
/*      */       } else {
/* 2203 */         str1 = str1 + " and stockindate <= '" + this.stockindate1 + "' ";
/*      */       } 
/*      */     }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/* 2214 */     if (!bool) {
/* 2215 */       bool = true;
/* 2216 */       str1 = " where 1=1 ";
/*      */     } else {
/* 2218 */       str1 = str1 + " and 1=1 ";
/*      */     } 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/* 2224 */     if (!this.fnamark.equals("")) {
/* 2225 */       str1 = str1 + " and fnamark like '%" + this.fnamark + "%' ";
/*      */     }
/*      */ 
/*      */     
/* 2229 */     if (!this.barcode.equals("")) {
/* 2230 */       str1 = str1 + " and barcode like '%" + this.barcode + "%' ";
/*      */     }
/*      */ 
/*      */     
/* 2234 */     if (!this.blongdepartment.equals("") && !this.blongdepartment.equals("0")) {
/* 2235 */       str1 = str1 + " and blongdepartment = '" + this.blongdepartment + "' ";
/*      */     }
/*      */ 
/*      */     
/* 2239 */     if (this.sptcount.equals("1")) {
/* 2240 */       str1 = str1 + " and sptcount = '" + this.sptcount + "' ";
/* 2241 */     } else if (this.sptcount.equals("0")) {
/*      */       
/* 2243 */       str1 = str1 + " and ( sptcount is null or  sptcount = '' or sptcount != '1' ) ";
/*      */     } 
/*      */ 
/*      */     
/* 2247 */     if (!this.relatewfid.equals("") && !this.relatewfid.equals("0")) {
/* 2248 */       str1 = str1 + " and fnamark like '%" + this.fnamark + "%' ";
/*      */     }
/*      */ 
/*      */     
/* 2252 */     if (!this.SelectDate.equals("")) {
/* 2253 */       str1 = str1 + " and SelectDate >= '" + this.SelectDate + "' ";
/*      */     }
/*      */ 
/*      */     
/* 2257 */     if (!this.SelectDate1.equals("")) {
/* 2258 */       str1 = str1 + " and SelectDate <= '" + this.SelectDate1 + "' ";
/*      */     }
/*      */ 
/*      */     
/* 2262 */     if (!this.contractno.equals("")) {
/* 2263 */       str1 = str1 + " and contractno like '%" + this.contractno + "%' ";
/*      */     }
/*      */ 
/*      */     
/* 2267 */     if (!this.Invoice.equals("")) {
/* 2268 */       str1 = str1 + " and Invoice like '%" + this.Invoice + "%' ";
/*      */     }
/*      */ 
/*      */     
/* 2272 */     if (!this.depreyear.equals("")) {
/* 2273 */       str1 = str1 + " and depreyear >= " + this.depreyear + " ";
/*      */     }
/*      */ 
/*      */     
/* 2277 */     if (!this.depreyear1.equals("")) {
/* 2278 */       str1 = str1 + " and depreyear <= " + this.depreyear1 + " ";
/*      */     }
/*      */ 
/*      */     
/* 2282 */     if (!this.deprerate.equals("")) {
/* 2283 */       str1 = str1 + " and deprerate >= " + this.deprerate + " ";
/*      */     }
/*      */ 
/*      */     
/* 2287 */     if (!this.deprerate1.equals("")) {
/* 2288 */       str1 = str1 + " and deprerate <= " + this.deprerate1 + " ";
/*      */     }
/*      */ 
/*      */     
/* 2292 */     if (!this.issupervision.equals("0") && !this.issupervision.equals("")) {
/* 2293 */       str1 = str1 + " and issupervision = '" + this.issupervision + "' ";
/*      */     }
/*      */ 
/*      */     
/* 2297 */     if (!this.amountpay.equals("")) {
/* 2298 */       str1 = str1 + " and amountpay >= " + this.amountpay + " ";
/*      */     }
/*      */ 
/*      */     
/* 2302 */     if (!this.amountpay1.equals("")) {
/* 2303 */       str1 = str1 + " and amountpay <= " + this.amountpay1 + " ";
/*      */     }
/*      */ 
/*      */     
/* 2307 */     if (!this.purchasestate.equals("") && !this.purchasestate.equals("0")) {
/* 2308 */       str1 = str1 + " and purchasestate = '" + this.purchasestate + "' ";
/*      */     }
/*      */ 
/*      */     
/* 2312 */     if (!this.alertnum.equals("")) {
/* 2313 */       str1 = str1 + " and alertnum = " + this.alertnum + "";
/*      */     }
/*      */ 
/*      */     
/* 2317 */     if (!this.datefield1.equals("")) {
/* 2318 */       str1 = str1 + " and datefield1 >= '" + this.datefield1 + "' ";
/*      */     }
/*      */ 
/*      */     
/* 2322 */     if (!this.datefield11.equals("")) {
/* 2323 */       str1 = str1 + " and datefield1 <= '" + this.datefield11 + "' ";
/*      */     }
/*      */     
/* 2326 */     if (!this.datefield2.equals("")) {
/* 2327 */       str1 = str1 + " and datefield2 >= '" + this.datefield2 + "' ";
/*      */     }
/*      */ 
/*      */     
/* 2331 */     if (!this.datefield22.equals("")) {
/* 2332 */       str1 = str1 + " and datefield2 <= '" + this.datefield22 + "' ";
/*      */     }
/*      */ 
/*      */     
/* 2336 */     if (!this.datefield3.equals("")) {
/* 2337 */       str1 = str1 + " and datefield3 >= '" + this.datefield3 + "' ";
/*      */     }
/*      */ 
/*      */     
/* 2341 */     if (!this.datefield33.equals("")) {
/* 2342 */       str1 = str1 + " and datefield3 <= '" + this.datefield33 + "' ";
/*      */     }
/*      */ 
/*      */     
/* 2346 */     if (!this.datefield4.equals("")) {
/* 2347 */       str1 = str1 + " and datefield4 >= '" + this.datefield4 + "' ";
/*      */     }
/*      */ 
/*      */     
/* 2351 */     if (!this.datefield44.equals("")) {
/* 2352 */       str1 = str1 + " and datefield4 <= '" + this.datefield44 + "' ";
/*      */     }
/*      */ 
/*      */     
/* 2356 */     if (!this.datefield5.equals("")) {
/* 2357 */       str1 = str1 + " and datefield5 >= '" + this.datefield5 + "' ";
/*      */     }
/*      */ 
/*      */     
/* 2361 */     if (!this.datefield55.equals("")) {
/* 2362 */       str1 = str1 + " and datefield5 <= '" + this.datefield55 + "' ";
/*      */     }
/*      */ 
/*      */ 
/*      */     
/* 2367 */     if (!this.numberfield1.equals("")) {
/* 2368 */       str1 = str1 + " and numberfield1 >= " + this.numberfield1 + " ";
/*      */     }
/*      */     
/* 2371 */     if (!this.numberfield11.equals("")) {
/* 2372 */       str1 = str1 + " and numberfield1 <= " + this.numberfield11 + " ";
/*      */     }
/*      */ 
/*      */     
/* 2376 */     if (!this.numberfield2.equals("")) {
/* 2377 */       str1 = str1 + " and numberfield2 >= " + this.numberfield2 + " ";
/*      */     }
/*      */     
/* 2380 */     if (!this.numberfield22.equals("")) {
/* 2381 */       str1 = str1 + " and numberfield2 <= " + this.numberfield22 + " ";
/*      */     }
/*      */ 
/*      */     
/* 2385 */     if (!this.numberfield3.equals("")) {
/* 2386 */       str1 = str1 + " and numberfield3 >= " + this.numberfield3 + " ";
/*      */     }
/*      */     
/* 2389 */     if (!this.numberfield33.equals("")) {
/* 2390 */       str1 = str1 + " and numberfield3 <= " + this.numberfield33 + " ";
/*      */     }
/*      */ 
/*      */     
/* 2394 */     if (!this.numberfield4.equals("")) {
/* 2395 */       str1 = str1 + " and numberfield4 >= " + this.numberfield4 + " ";
/*      */     }
/*      */     
/* 2398 */     if (!this.numberfield44.equals("")) {
/* 2399 */       str1 = str1 + " and numberfield4 <= " + this.numberfield44 + " ";
/*      */     }
/*      */ 
/*      */     
/* 2403 */     if (!this.numberfield5.equals("")) {
/* 2404 */       str1 = str1 + " and numberfield5 >= " + this.numberfield5 + " ";
/*      */     }
/*      */     
/* 2407 */     if (!this.numberfield55.equals("")) {
/* 2408 */       str1 = str1 + " and numberfield5 <= " + this.numberfield55 + " ";
/*      */     }
/*      */ 
/*      */ 
/*      */     
/* 2413 */     if (!this.textfield1.equals("")) {
/* 2414 */       str1 = str1 + " and textfield1 like '%" + this.textfield1 + "%' ";
/*      */     }
/*      */ 
/*      */     
/* 2418 */     if (!this.textfield2.equals("")) {
/* 2419 */       str1 = str1 + " and textfield2 like '%" + this.textfield2 + "%' ";
/*      */     }
/*      */ 
/*      */     
/* 2423 */     if (!this.textfield3.equals("")) {
/* 2424 */       str1 = str1 + " and textfield3 like '%" + this.textfield3 + "%' ";
/*      */     }
/*      */ 
/*      */     
/* 2428 */     if (!this.textfield4.equals("")) {
/* 2429 */       str1 = str1 + " and textfield4 like '%" + this.textfield4 + "%' ";
/*      */     }
/*      */ 
/*      */     
/* 2433 */     if (!this.textfield5.equals("")) {
/* 2434 */       str1 = str1 + " and textfield5 like '%" + this.textfield5 + "%' ";
/*      */     }
/*      */ 
/*      */ 
/*      */     
/* 2439 */     if (this.tinyintfield1.equals("1")) {
/* 2440 */       str1 = str1 + " and tinyintfield1 = '" + this.tinyintfield1 + "' ";
/*      */     }
/*      */ 
/*      */     
/* 2444 */     if (this.tinyintfield2.equals("1")) {
/* 2445 */       str1 = str1 + " and tinyintfield2 = '" + this.tinyintfield2 + "' ";
/*      */     }
/*      */ 
/*      */     
/* 2449 */     if (this.tinyintfield3.equals("1")) {
/* 2450 */       str1 = str1 + " and tinyintfield3 = '" + this.tinyintfield3 + "' ";
/*      */     }
/*      */ 
/*      */     
/* 2454 */     if (this.tinyintfield4.equals("1")) {
/* 2455 */       str1 = str1 + " and tinyintfield4 = '" + this.tinyintfield4 + "' ";
/*      */     }
/*      */ 
/*      */     
/* 2459 */     if (this.tinyintfield5.equals("1")) {
/* 2460 */       str1 = str1 + " and tinyintfield5 = '" + this.tinyintfield5 + "' ";
/*      */     }
/*      */ 
/*      */ 
/*      */     
/* 2465 */     if (!this.docff01name.equals("")) {
/* 2466 */       str1 = str1 + " and docff01name like '%" + this.docff01name + "%' ";
/*      */     }
/*      */ 
/*      */     
/* 2470 */     if (!this.docff02name.equals("")) {
/* 2471 */       str1 = str1 + " and docff02name like '%" + this.docff02name + "%' ";
/*      */     }
/*      */ 
/*      */     
/* 2475 */     if (!this.docff03name.equals("")) {
/* 2476 */       str1 = str1 + " and docff03name like '%" + this.docff03name + "%' ";
/*      */     }
/*      */ 
/*      */     
/* 2480 */     if (!this.docff04name.equals("")) {
/* 2481 */       str1 = str1 + " and docff04name like '%" + this.docff04name + "%' ";
/*      */     }
/*      */ 
/*      */     
/* 2485 */     if (!this.docff05name.equals("")) {
/* 2486 */       str1 = str1 + " and docff05name like '%" + this.docff05name + "%' ";
/*      */     }
/*      */ 
/*      */ 
/*      */     
/* 2491 */     if (!this.depff01name.equals("")) {
/* 2492 */       str1 = str1 + " and depff01name like '%" + this.depff01name + "%' ";
/*      */     }
/*      */ 
/*      */     
/* 2496 */     if (!this.depff02name.equals("")) {
/* 2497 */       str1 = str1 + " and depff02name like '%" + this.depff02name + "%' ";
/*      */     }
/*      */ 
/*      */     
/* 2501 */     if (!this.depff03name.equals("")) {
/* 2502 */       str1 = str1 + " and depff03name like '%" + this.depff03name + "%' ";
/*      */     }
/*      */ 
/*      */     
/* 2506 */     if (!this.depff04name.equals("")) {
/* 2507 */       str1 = str1 + " and depff04name like '%" + this.depff04name + "%' ";
/*      */     }
/*      */ 
/*      */     
/* 2511 */     if (!this.depff05name.equals("")) {
/* 2512 */       str1 = str1 + " and depff05name like '%" + this.depff05name + "%' ";
/*      */     }
/*      */ 
/*      */ 
/*      */     
/* 2517 */     if (!this.crmff01name.equals("")) {
/* 2518 */       str1 = str1 + " and crmff01name like '%" + this.crmff01name + "%' ";
/*      */     }
/*      */ 
/*      */     
/* 2522 */     if (!this.crmff02name.equals("")) {
/* 2523 */       str1 = str1 + " and crmff02name like '%" + this.crmff02name + "%' ";
/*      */     }
/*      */ 
/*      */     
/* 2527 */     if (!this.crmff03name.equals("")) {
/* 2528 */       str1 = str1 + " and crmff03name like '%" + this.crmff03name + "%' ";
/*      */     }
/*      */ 
/*      */     
/* 2532 */     if (!this.crmff04name.equals("")) {
/* 2533 */       str1 = str1 + " and crmff04name like '%" + this.crmff04name + "%' ";
/*      */     }
/*      */ 
/*      */     
/* 2537 */     if (!this.crmff05name.equals("")) {
/* 2538 */       str1 = str1 + " and crmff05name like '%" + this.crmff05name + "%' ";
/*      */     }
/*      */ 
/*      */ 
/*      */     
/* 2543 */     if (!this.reqff01name.equals("")) {
/* 2544 */       str1 = str1 + " and reqff01name like '%" + this.reqff01name + "%' ";
/*      */     }
/*      */ 
/*      */     
/* 2548 */     if (!this.reqff02name.equals("")) {
/* 2549 */       str1 = str1 + " and reqff02name like '%" + this.reqff02name + "%' ";
/*      */     }
/*      */ 
/*      */     
/* 2553 */     if (!this.reqff03name.equals("")) {
/* 2554 */       str1 = str1 + " and reqff03name like '%" + this.reqff03name + "%' ";
/*      */     }
/*      */ 
/*      */     
/* 2558 */     if (!this.reqff04name.equals("")) {
/* 2559 */       str1 = str1 + " and reqff04name like '%" + this.reqff04name + "%' ";
/*      */     }
/*      */ 
/*      */     
/* 2563 */     if (!this.reqff05name.equals("")) {
/* 2564 */       str1 = str1 + " and reqff05name like '%" + this.reqff05name + "%' ";
/*      */     }
/*      */ 
/*      */     
/* 2568 */     if (!"".equals(this.cusSql)) {
/* 2569 */       str1 = str1 + this.cusSql;
/*      */     }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/* 2580 */     if (!this.capitalsql.equals("")) str1 = str1 + " and " + this.capitalsql + " ";
/*      */     
/* 2582 */     String str2 = "";
/* 2583 */     if (!this.orderby.equals("")) str2 = " order by " + this.orderby;
/*      */     
/* 2585 */     return str1 + str2;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String[] getCptCount4Hrm(String paramString, User paramUser) {
/* 2595 */     if ("".equals(Util.null2String(paramString)) || paramUser == null) {
/* 2596 */       return new String[] { "", "" };
/*      */     }
/* 2598 */     CommonShareManager commonShareManager = new CommonShareManager();
/* 2599 */     String str = "";
/* 2600 */     String[] arrayOfString = new String[2];
/* 2601 */     if (Cpt4modeUtil.isUse()) {
/* 2602 */       arrayOfString[0] = "/formmode/search/CustomSearchBySimple.jsp?customid=" + Cpt4modeUtil.getSearchid("wdzc") + "&resourceid=" + paramString + "&from=hrmResourceBase&mymodetype=wdzc&sqlwhere=" + (new XssUtil()).put("where resourceid in(" + this.commonShareManager.getContainsSubuserids("" + paramString) + ")");
/* 2603 */       str = " select  count(id) as cnt FROM uf_cptcapital t1 where t1.isdata=2 and t1.sptcount='0' and t1.resourceid in (" + this.commonShareManager.getContainsSubuserids("" + paramString) + ") and  t1.stateid<>0  ";
/*      */     } else {
/*      */       
/* 2606 */       arrayOfString[0] = GCONST.getContextPath() + "/spa/cpt/index.html#/main/cpt/mycapital";
/* 2607 */       str = " select  count(id) as cnt FROM cptcapital t1 where t1.isdata=2 and t1.sptcount='1' and t1.resourceid in (" + this.commonShareManager.getContainsSubuserids("" + paramString) + ") and  t1.stateid<>1 " + commonShareManager.getAssortmentSqlWhere(paramUser);
/*      */     } 
/*      */     
/* 2610 */     RecordSet recordSet = new RecordSet();
/* 2611 */     recordSet.executeSql(str);
/* 2612 */     if (recordSet.next()) {
/* 2613 */       arrayOfString[1] = "" + recordSet.getInt("cnt");
/*      */     } else {
/* 2615 */       arrayOfString[1] = "0";
/*      */     } 
/*      */     
/* 2618 */     return arrayOfString;
/*      */   }
/*      */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/cpt/search/CptSearchComInfo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */