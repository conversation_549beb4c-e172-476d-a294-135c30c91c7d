/*    */ package weaver.cpt.search;
/*    */ 
/*    */ import weaver.general.BaseBean;
/*    */ import weaver.hrm.company.DepartmentComInfo;
/*    */ import weaver.hrm.resource.ResourceComInfo;
/*    */ 
/*    */ public class CapitalSearch
/*    */   extends BaseBean {
/*    */   public String getDepartmentName(String paramString) {
/* 10 */     String str = "";
/*    */     try {
/* 12 */       ResourceComInfo resourceComInfo = new ResourceComInfo();
/* 13 */       DepartmentComInfo departmentComInfo = new DepartmentComInfo();
/* 14 */       String str1 = resourceComInfo.getDepartmentID(paramString);
/* 15 */       String str2 = departmentComInfo.getDepartmentname(str1);
/* 16 */       str = str + "<a href=javaScript:openFullWindowHaveBar('/hrm/company/HrmDepartmentDsp.jsp?id=" + str1 + "') >" + str2 + "</a> ";
/* 17 */     } catch (Exception exception) {
/* 18 */       writeLog(exception);
/*    */     } 
/* 20 */     return str;
/*    */   }
/*    */   
/*    */   public String getDepartmentNameNoHref(String paramString) {
/* 24 */     String str = "";
/*    */     try {
/* 26 */       ResourceComInfo resourceComInfo = new ResourceComInfo();
/* 27 */       DepartmentComInfo departmentComInfo = new DepartmentComInfo();
/* 28 */       String str1 = resourceComInfo.getDepartmentID(paramString);
/* 29 */       String str2 = departmentComInfo.getDepartmentname(str1);
/* 30 */       str = str + str2;
/* 31 */     } catch (Exception exception) {
/* 32 */       writeLog(exception);
/*    */     } 
/* 34 */     return str;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/cpt/search/CapitalSearch.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */