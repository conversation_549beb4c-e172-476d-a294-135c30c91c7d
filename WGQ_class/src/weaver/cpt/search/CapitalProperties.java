/*     */ package weaver.cpt.search;
/*     */ 
/*     */ import weaver.crm.Maint.CustomerInfoComInfo;
/*     */ import weaver.docs.docs.DocComInfo;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.company.DepartmentComInfo;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.workflow.workflow.WorkflowRequestComInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CapitalProperties
/*     */   extends BaseBean
/*     */ {
/*     */   public String getIssupervision(String paramString1, String paramString2) {
/*  22 */     String str = "";
/*  23 */     if (paramString1.equals("2")) {
/*  24 */       str = SystemEnv.getHtmlLabelName(163, Util.getIntValue(paramString2));
/*     */     } else {
/*  26 */       str = SystemEnv.getHtmlLabelName(161, Util.getIntValue(paramString2));
/*     */     } 
/*     */     
/*  29 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getPurchasestate(String paramString1, String paramString2) {
/*  38 */     String str = "";
/*     */     
/*  40 */     if (paramString1.equals("1")) { str = SystemEnv.getHtmlLabelName(22334, Util.getIntValue(paramString2)); }
/*  41 */     else if (paramString1.equals("2")) { str = SystemEnv.getHtmlLabelName(22335, Util.getIntValue(paramString2)); }
/*  42 */     else if (paramString1.equals("3")) { str = SystemEnv.getHtmlLabelName(22336, Util.getIntValue(paramString2)); }
/*  43 */     else if (paramString1.equals("4")) { str = SystemEnv.getHtmlLabelName(22337, Util.getIntValue(paramString2)); }
/*     */     
/*  45 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getIsinner(String paramString1, String paramString2) {
/*  55 */     String str = "";
/*  56 */     if (paramString1.equals("0")) { str = SystemEnv.getHtmlLabelName(15298, Util.getIntValue(paramString2)); }
/*  57 */     else if (paramString1.equals("1")) { str = SystemEnv.getHtmlLabelName(15299, Util.getIntValue(paramString2)); }
/*  58 */      return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCheckboxStatus(String paramString1, String paramString2) {
/*  67 */     String str = "";
/*     */     
/*  69 */     if (paramString1.equals("1")) {
/*  70 */       str = SystemEnv.getHtmlLabelName(163, Util.getIntValue(paramString2));
/*     */     } else {
/*  72 */       str = SystemEnv.getHtmlLabelName(161, Util.getIntValue(paramString2));
/*     */     } 
/*     */     
/*  75 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getAttribute(String paramString1, String paramString2) {
/*  85 */     String str = "";
/*     */     
/*  87 */     if (paramString1.equals("0")) {
/*  88 */       str = SystemEnv.getHtmlLabelName(1366, Util.getIntValue(paramString2));
/*     */     }
/*  90 */     else if (paramString1.equals("1")) {
/*  91 */       str = SystemEnv.getHtmlLabelName(1367, Util.getIntValue(paramString2));
/*     */     }
/*  93 */     else if (paramString1.equals("2")) {
/*  94 */       str = SystemEnv.getHtmlLabelName(1368, Util.getIntValue(paramString2));
/*     */     }
/*  96 */     else if (paramString1.equals("3")) {
/*  97 */       str = SystemEnv.getHtmlLabelName(1369, Util.getIntValue(paramString2));
/*     */     }
/*  99 */     else if (paramString1.equals("4")) {
/* 100 */       str = SystemEnv.getHtmlLabelName(60, Util.getIntValue(paramString2));
/*     */     }
/* 102 */     else if (paramString1.equals("5")) {
/* 103 */       str = SystemEnv.getHtmlLabelName(1370, Util.getIntValue(paramString2));
/*     */     }
/* 105 */     else if (paramString1.equals("6")) {
/* 106 */       str = SystemEnv.getHtmlLabelName(811, Util.getIntValue(paramString2));
/*     */     } 
/*     */     
/* 109 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCrmName(String paramString) {
/* 118 */     String str = "";
/* 119 */     String[] arrayOfString = Util.TokenizerString2(paramString, ",");
/* 120 */     CustomerInfoComInfo customerInfoComInfo = null;
/*     */     try {
/* 122 */       customerInfoComInfo = new CustomerInfoComInfo();
/*     */       
/* 124 */       if (arrayOfString != null) {
/* 125 */         for (byte b = 0; b < arrayOfString.length; b++)
/*     */         {
/* 127 */           str = str + "<a href=javaScript:openFullWindowHaveBar('/CRM/data/ViewCustomer.jsp?CustomerID=" + arrayOfString[b] + "') >" + customerInfoComInfo.getCustomerInfoname(arrayOfString[b]) + "</a> ";
/*     */         }
/*     */       }
/* 130 */     } catch (Exception exception) {
/* 131 */       writeLog(exception);
/*     */     } 
/* 133 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getDocName(String paramString) {
/* 142 */     String str = "";
/* 143 */     DocComInfo docComInfo = null;
/*     */     try {
/* 145 */       docComInfo = new DocComInfo();
/* 146 */       String[] arrayOfString = Util.TokenizerString2(paramString, ",");
/* 147 */       if (arrayOfString != null) {
/* 148 */         for (byte b = 0; b < arrayOfString.length; b++)
/*     */         {
/* 150 */           str = str + "<a href=javaScript:openFullWindowHaveBar('/docs/docs/DocDsp.jsp?id=" + arrayOfString[b] + "') >" + docComInfo.getDocname(arrayOfString[b]) + "</a> ";
/*     */         }
/*     */       }
/* 153 */     } catch (Exception exception) {
/* 154 */       writeLog(exception);
/*     */     } 
/* 156 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getRequestName(String paramString) {
/* 165 */     String str = "";
/* 166 */     WorkflowRequestComInfo workflowRequestComInfo = null;
/*     */     try {
/* 168 */       workflowRequestComInfo = new WorkflowRequestComInfo();
/* 169 */       String[] arrayOfString = Util.TokenizerString2(paramString, ",");
/* 170 */       if (arrayOfString != null) {
/* 171 */         for (byte b = 0; b < arrayOfString.length; b++)
/*     */         {
/* 173 */           str = str + "<a href=javaScript:openFullWindowHaveBar('/workflow/request/ViewRequest.jsp?requestid=" + arrayOfString[b] + "') >" + workflowRequestComInfo.getRequestName(arrayOfString[b]) + "</a> ";
/*     */         }
/*     */       }
/* 176 */     } catch (Exception exception) {
/* 177 */       writeLog(exception);
/*     */     } 
/* 179 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getDepartmentName(String paramString) {
/* 188 */     String str = "";
/* 189 */     DepartmentComInfo departmentComInfo = null;
/*     */     try {
/* 191 */       departmentComInfo = new DepartmentComInfo();
/* 192 */       String[] arrayOfString = Util.TokenizerString2(paramString, ",");
/* 193 */       if (arrayOfString != null) {
/* 194 */         for (byte b = 0; b < arrayOfString.length; b++)
/*     */         {
/* 196 */           str = str + "<a href=javaScript:openFullWindowHaveBar('/hrm/company/HrmDepartmentDsp.jsp?id=" + arrayOfString[b] + "') >" + departmentComInfo.getDepartmentname(arrayOfString[b]) + "</a> ";
/*     */         }
/*     */       }
/* 199 */     } catch (Exception exception) {
/* 200 */       writeLog(exception);
/*     */     } 
/*     */     
/* 203 */     return str;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/cpt/search/CapitalProperties.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */