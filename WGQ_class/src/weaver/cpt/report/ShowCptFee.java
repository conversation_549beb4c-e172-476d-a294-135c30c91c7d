/*     */ package weaver.cpt.report;
/*     */ 
/*     */ import com.sun.image.codec.jpeg.JPEGCodec;
/*     */ import com.sun.image.codec.jpeg.JPEGEncodeParam;
/*     */ import com.sun.image.codec.jpeg.JPEGImageEncoder;
/*     */ import java.awt.BasicStroke;
/*     */ import java.awt.Color;
/*     */ import java.awt.Font;
/*     */ import java.awt.Graphics2D;
/*     */ import java.awt.image.BufferedImage;
/*     */ import java.io.BufferedOutputStream;
/*     */ import java.io.IOException;
/*     */ import java.io.OutputStream;
/*     */ import java.util.ArrayList;
/*     */ import javax.servlet.ServletException;
/*     */ import javax.servlet.ServletOutputStream;
/*     */ import javax.servlet.http.HttpServlet;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.company.DepartmentComInfo;
/*     */ 
/*     */ public class ShowCptFee
/*     */   extends HttpServlet {
/*  26 */   private int width = 700;
/*  27 */   private int height = 470;
/*     */ 
/*     */ 
/*     */   
/*     */   public void doGet(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws ServletException, IOException {
/*  32 */     RecordSet recordSet = new RecordSet();
/*  33 */     String str1 = Util.null2String(paramHttpServletRequest.getParameter("startdate"));
/*  34 */     String str2 = Util.null2String(paramHttpServletRequest.getParameter("enddate"));
/*     */ 
/*     */     
/*     */     try {
/*  38 */       ServletOutputStream servletOutputStream = paramHttpServletResponse.getOutputStream();
/*  39 */       BufferedOutputStream bufferedOutputStream = new BufferedOutputStream((OutputStream)servletOutputStream);
/*     */       
/*  41 */       BufferedImage bufferedImage = new BufferedImage(this.width, this.height, 1);
/*     */       
/*  43 */       JPEGEncodeParam jPEGEncodeParam = null;
/*  44 */       JPEGImageEncoder jPEGImageEncoder = JPEGCodec.createJPEGEncoder(bufferedOutputStream);
/*     */       
/*  46 */       jPEGEncodeParam = jPEGImageEncoder.getDefaultJPEGEncodeParam(bufferedImage);
/*  47 */       jPEGEncodeParam.setQuality(0.9F, true);
/*  48 */       jPEGImageEncoder.setJPEGEncodeParam(jPEGEncodeParam);
/*     */       
/*  50 */       Graphics2D graphics2D = bufferedImage.createGraphics();
/*     */       
/*  52 */       graphics2D.setColor(Color.white);
/*  53 */       graphics2D.fillRect(0, 0, this.width, this.height);
/*     */       
/*  55 */       byte b1 = 50;
/*  56 */       char c1 = 'ǂ';
/*  57 */       char c2 = 'ʨ';
/*  58 */       byte b2 = 0;
/*     */       
/*  60 */       BasicStroke basicStroke = new BasicStroke(3.0F);
/*  61 */       graphics2D.setStroke(basicStroke);
/*     */       
/*  63 */       graphics2D.setColor(Color.black);
/*  64 */       graphics2D.drawLine(b1, c1, b1, b2);
/*  65 */       graphics2D.drawLine(b1, c1, c2, c1);
/*     */       
/*  67 */       drawArrow(b1, c1, b1, b2, graphics2D);
/*  68 */       graphics2D.drawString("X", b1 + 10, b2 + 10);
/*  69 */       drawArrow(b1, c1, c2, c1, graphics2D);
/*  70 */       graphics2D.drawString("Y", c2 + 10, c1 + 5);
/*     */ 
/*     */       
/*  73 */       DepartmentComInfo departmentComInfo = new DepartmentComInfo();
/*  74 */       int i = departmentComInfo.getCompanyNum();
/*     */       
/*  76 */       ArrayList<String> arrayList1 = new ArrayList();
/*  77 */       ArrayList<String> arrayList2 = new ArrayList();
/*  78 */       ArrayList<String> arrayList3 = new ArrayList();
/*     */       
/*  80 */       float f = 0.0F;
/*  81 */       String str3 = " select * from bill_CptPlanMain ";
/*  82 */       recordSet.executeSql(str3);
/*  83 */       while (recordSet.next()) {
/*  84 */         if (Util.getFloatValue(recordSet.getString("totalamount"), 0.0F) > f)
/*  85 */           f = Util.getFloatValue(recordSet.getString("totalamount"), 0.0F); 
/*  86 */         arrayList1.add(recordSet.getString("departmentid"));
/*  87 */         arrayList2.add(recordSet.getString("totalamount"));
/*  88 */         arrayList3.add("");
/*     */       } 
/*     */       
/*  91 */       str3 = "select departmentid,sum(totalamount) as totalamount from bill_CptAdjustMain where realizedate >='" + str1 + "' and realizedate <='" + str2 + "' group by departmentid";
/*  92 */       recordSet.executeSql(str3);
/*  93 */       while (recordSet.next()) {
/*  94 */         String str = recordSet.getString("departmentid");
/*  95 */         float f1 = Util.getFloatValue(recordSet.getString("totalamount"), 0.0F);
/*  96 */         int n = arrayList1.indexOf(str);
/*  97 */         if (n != -1) {
/*  98 */           float f2 = Util.getFloatValue("" + arrayList3.get(n), 0.0F) + f1;
/*  99 */           arrayList3.add(n, "" + f2);
/*     */         } 
/*     */       } 
/* 102 */       str3 = "select departmentid,sum(totalamount) as totalamount from bill_CptFetchMain where realizedate >='" + str1 + "' and realizedate <='" + str2 + "' group by departmentid";
/* 103 */       recordSet.executeSql(str3);
/* 104 */       while (recordSet.next()) {
/* 105 */         String str = recordSet.getString("departmentid");
/* 106 */         float f1 = Util.getFloatValue(recordSet.getString("totalamount"), 0.0F);
/* 107 */         int n = arrayList1.indexOf(str);
/* 108 */         if (n != -1) {
/* 109 */           float f2 = Util.getFloatValue("" + arrayList3.get(n), 0.0F) + f1;
/* 110 */           arrayList3.add(n, "" + f2);
/*     */         } 
/*     */       } 
/* 113 */       str3 = "select a.departmentid as departmentid,sum(b.inamount) as totalamount from bill_CptStockInMain a,bill_CptStockInDetail b where a.id = b.cptstockinid and realizedate >='" + str1 + "' and realizedate <='" + str2 + "' group by a.departmentid";
/* 114 */       recordSet.executeSql(str3);
/* 115 */       while (recordSet.next()) {
/* 116 */         String str = recordSet.getString("departmentid");
/* 117 */         float f1 = Util.getFloatValue(recordSet.getString("totalamount"), 0.0F);
/* 118 */         int n = arrayList1.indexOf(str);
/* 119 */         if (n != -1) {
/* 120 */           float f2 = Util.getFloatValue("" + arrayList3.get(n), 0.0F) + f1;
/* 121 */           arrayList3.add(n, "" + f2);
/*     */         } 
/*     */       } 
/*     */       int j;
/* 125 */       for (j = 0; j < arrayList1.size(); j++) {
/* 126 */         float f1 = Util.getFloatValue("" + arrayList3.get(j), 0.0F);
/* 127 */         if (f1 > f) {
/* 128 */           f = f1;
/*     */         }
/*     */       } 
/* 131 */       j = c2 / 3 * i;
/* 132 */       int k = b1 + j;
/*     */ 
/*     */       
/* 135 */       Font font1 = graphics2D.getFont();
/* 136 */       String str4 = font1.getFontName();
/* 137 */       Font font2 = new Font("幼圆", 0, 9);
/* 138 */       graphics2D.setFont(font2);
/*     */       
/* 140 */       int m = (int)(5000.0F / f * c1); byte b3;
/* 141 */       for (b3 = 0; b3 < f / 5000.0F; b3++) {
/* 142 */         int n = b3 * 20;
/* 143 */         graphics2D.drawString("" + (5000 * b3), 10, c1 - m * b3);
/*     */       } 
/*     */ 
/*     */       
/* 147 */       for (b3 = 0; b3 < arrayList1.size(); b3++) {
/* 148 */         float f1 = Util.getFloatValue("" + arrayList3.get(b3), 0.0F);
/* 149 */         float f2 = Util.getFloatValue("" + arrayList2.get(b3), 0.0F);
/*     */         
/* 151 */         int n = (int)(f2 / f * (c1 - 20));
/* 152 */         int i1 = (int)(f1 / f * (c1 - 20));
/* 153 */         graphics2D.setColor(Color.red);
/* 154 */         graphics2D.fill3DRect(k, c1 - n, j, n, true);
/* 155 */         graphics2D.setColor(Color.black);
/* 156 */         graphics2D.drawString("" + f2, k, c1 - n - 5);
/*     */         
/* 158 */         graphics2D.setColor(Color.green);
/* 159 */         graphics2D.fill3DRect(k + j, c1 - i1, j, i1, true);
/* 160 */         graphics2D.setColor(Color.black);
/* 161 */         graphics2D.drawString("" + f1, k + j, c1 - i1 - 5);
/*     */         
/* 163 */         graphics2D.setColor(Color.black);
/* 164 */         graphics2D.drawString(Util.fromScreen2(departmentComInfo.getDepartmentname("" + arrayList1.get(b3)), 7), k, c1 + 17);
/* 165 */         k += 3 * j;
/*     */       } 
/*     */       
/* 168 */       graphics2D.dispose();
/* 169 */       jPEGImageEncoder.encode(bufferedImage);
/*     */ 
/*     */       
/* 172 */       servletOutputStream.flush();
/* 173 */       bufferedOutputStream.close();
/* 174 */       servletOutputStream.close();
/*     */ 
/*     */     
/*     */     }
/* 178 */     catch (Exception exception) {}
/*     */   }
/*     */ 
/*     */   
/*     */   private void drawArrow(int paramInt1, int paramInt2, int paramInt3, int paramInt4, Graphics2D paramGraphics2D) {
/* 183 */     int i = paramInt1 + paramInt2 - paramInt4;
/* 184 */     int j = paramInt2 - paramInt1 + paramInt3;
/* 185 */     int k = paramInt1 - paramInt2 + paramInt4;
/* 186 */     int m = paramInt2 + paramInt1 - paramInt3;
/*     */     
/* 188 */     double d = Math.sqrt(((i - paramInt3) * (i - paramInt3) + (j - paramInt4) * (j - paramInt4)));
/* 189 */     if (d != 0.0D) {
/* 190 */       d = 10.0D / d;
/* 191 */       if (i == paramInt3 && j != paramInt4) {
/* 192 */         j = paramInt4 + 10;
/* 193 */         i = i;
/*     */       }
/* 195 */       else if (j == paramInt4) {
/* 196 */         i = paramInt3 + 10;
/* 197 */         j = j;
/*     */       } else {
/*     */         
/* 200 */         i = (int)(paramInt3 + d * (i - paramInt3));
/* 201 */         j = (int)(paramInt4 + d * (j - paramInt4));
/*     */       } 
/*     */       
/* 204 */       if (k == paramInt3 && m != paramInt4) {
/* 205 */         m = paramInt4 + 10;
/* 206 */         k = k;
/*     */       }
/* 208 */       else if (m == paramInt4) {
/* 209 */         k = paramInt3 + 10;
/* 210 */         m = m;
/*     */       } else {
/*     */         
/* 213 */         k = (int)(paramInt3 + d * (k - paramInt3));
/* 214 */         m = (int)(paramInt4 + d * (m - paramInt4));
/*     */       } 
/*     */     } 
/* 217 */     paramGraphics2D.drawLine(paramInt3, paramInt4, i, j);
/* 218 */     paramGraphics2D.drawLine(paramInt3, paramInt4, k, m);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/cpt/report/ShowCptFee.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */