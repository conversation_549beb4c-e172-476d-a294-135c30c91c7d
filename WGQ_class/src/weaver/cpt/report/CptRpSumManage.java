/*     */ package weaver.cpt.report;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CptRpSumManage
/*     */   extends BaseBean
/*     */ {
/*     */   private String optional;
/*     */   private String id;
/*  22 */   private int currentid = -1;
/*  23 */   private int countid = 0;
/*     */ 
/*     */ 
/*     */   
/*  27 */   private ArrayList resultids = new ArrayList();
/*  28 */   private ArrayList resultcounts = new ArrayList();
/*  29 */   private ArrayList resultpercents = new ArrayList();
/*  30 */   private ArrayList imagepercents = new ArrayList();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getResultID() {
/*  42 */     return this.resultids.get(this.currentid);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getResultCount() {
/*  50 */     return this.resultcounts.get(this.currentid);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getResultPercent() {
/*  58 */     return this.resultpercents.get(this.currentid);
/*     */   }
/*     */   
/*     */   public String getImagePercent() {
/*  62 */     return this.imagepercents.get(this.currentid);
/*     */   }
/*     */ 
/*     */   
/*     */   public void setOptional(String paramString) {
/*  67 */     this.optional = paramString;
/*     */   }
/*     */   
/*     */   public void setID(String paramString) {
/*  71 */     this.id = paramString;
/*     */   }
/*     */ 
/*     */   
/*     */   public boolean next() {
/*  76 */     if (this.currentid + 1 < this.countid) {
/*  77 */       this.currentid++;
/*  78 */       return true;
/*     */     } 
/*  80 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void getRpResult() throws Exception {
/*  88 */     String str = "";
/*  89 */     int i = 0;
/*  90 */     int j = 0;
/*     */     
/*  92 */     if (this.optional.equals("capitalgroupid")) str = "CptCapital_SSumByCapitalgroup"; 
/*  93 */     if (this.optional.equals("capitaltypeid")) str = "CptCapital_SSumByCapitaltypeid"; 
/*  94 */     if (this.optional.equals("resourceid")) str = "CptCapital_SSumByResourceid"; 
/*  95 */     if (this.optional.equals("departmentid")) str = "CptCapital_SSumByDepartmentid"; 
/*  96 */     if (this.optional.equals("stateid")) str = "CptCapital_SelectSumByStateid"; 
/*  97 */     if (this.optional.equals("customerid")) str = "CptCapital_SSumByCustomerid";
/*     */     
/*  99 */     RecordSet recordSet = new RecordSet();
/*     */     
/*     */     try {
/* 102 */       recordSet.executeProc(str, "");
/* 103 */       while (recordSet.next()) {
/* 104 */         this.resultids.add(recordSet.getString("resultid"));
/* 105 */         int k = recordSet.getInt("resultcount");
/* 106 */         i += k;
/* 107 */         if (!j) j = k; 
/* 108 */         this.resultcounts.add("" + k);
/*     */       } 
/* 110 */       if (i == 0 || j == 0)
/* 111 */         return;  this.countid = this.resultids.size();
/*     */       
/* 113 */       for (byte b = 0; b < this.countid; b++) {
/* 114 */         int k = (int)((Util.getFloatValue(this.resultcounts.get(b)) / j) * 100.0D);
/* 115 */         int m = (int)((Util.getFloatValue(this.resultcounts.get(b)) / i) * 100.0D);
/* 116 */         this.resultpercents.add("" + m + "%");
/* 117 */         this.imagepercents.add("" + k + "%");
/*     */       }
/*     */     
/*     */     }
/* 121 */     catch (Exception exception) {
/* 122 */       writeLog(exception);
/* 123 */       throw exception;
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/cpt/report/CptRpSumManage.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */