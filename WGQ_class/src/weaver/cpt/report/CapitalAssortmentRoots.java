/*    */ package weaver.cpt.report;
/*    */ 
/*    */ import java.util.ArrayList;
/*    */ import java.util.Hashtable;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.cpt.maintenance.CapitalAssortmentComInfo;
/*    */ import weaver.general.BaseBean;
/*    */ import weaver.general.Util;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class CapitalAssortmentRoots
/*    */   extends BaseBean
/*    */ {
/*    */   private ArrayList leafids;
/*    */   private Hashtable allRootAssortmentInfo;
/* 23 */   private CapitalAssortmentComInfo cominfo = new CapitalAssortmentComInfo();
/*    */   
/*    */   public CapitalAssortmentRoots() throws Exception {
/* 26 */     this.leafids = new ArrayList();
/* 27 */     this.allRootAssortmentInfo = new Hashtable<Object, Object>();
/*    */   }
/*    */ 
/*    */   
/*    */   public void setRootAssortmentList() throws Exception {
/* 32 */     this.leafids.clear();
/* 33 */     this.allRootAssortmentInfo.clear();
/* 34 */     this.cominfo.removeCapitalAssortmentCache();
/*    */     
/* 36 */     RecordSet recordSet = new RecordSet();
/*    */     try {
/* 38 */       recordSet.executeProc("CptCapitalAssortment_SLeaf", "");
/* 39 */       while (recordSet.next()) {
/* 40 */         this.leafids.add(Util.null2String(recordSet.getString("id")));
/*    */       }
/*    */     }
/* 43 */     catch (Exception exception) {
/* 44 */       writeLog(exception);
/* 45 */       throw exception;
/*    */     } 
/*    */ 
/*    */     
/* 49 */     for (byte b = 0; b < this.leafids.size(); b++) {
/* 50 */       String str = this.leafids.get(b);
/* 51 */       while (!this.cominfo.getSupAssortmentId(str).equals("0")) {
/* 52 */         str = this.cominfo.getSupAssortmentId(str);
/*    */       }
/* 54 */       this.allRootAssortmentInfo.put(this.leafids.get(b), str);
/*    */     } 
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public String getRootId(String paramString) {
/* 61 */     return (String)this.allRootAssortmentInfo.get(paramString);
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public ArrayList getAllLeafIds(String paramString) {
/* 67 */     ArrayList arrayList = new ArrayList();
/* 68 */     for (byte b = 0; b < this.leafids.size(); b++) {
/* 69 */       if (getRootId(this.leafids.get(b)).equals(paramString)) {
/* 70 */         arrayList.add(this.leafids.get(b));
/*    */       }
/*    */     } 
/*    */     
/* 74 */     return arrayList;
/*    */   }
/*    */ 
/*    */   
/*    */   public String getRootCapitalNum(String paramString) {
/* 79 */     ArrayList<String> arrayList = getAllLeafIds(paramString);
/*    */     
/* 81 */     int i = 0;
/* 82 */     if (arrayList.size() == 0)
/*    */     {
/* 84 */       return this.cominfo.getCapitalCount(paramString);
/*    */     }
/*    */     
/* 87 */     for (byte b = 0; b < arrayList.size(); b++) {
/* 88 */       i += Util.getIntValue(this.cominfo.getCapitalCount(arrayList.get(b)), 0);
/*    */     }
/*    */ 
/*    */     
/* 92 */     return "" + i;
/*    */   }
/*    */ 
/*    */   
/*    */   public String getLeafCapitalNum(String paramString) {
/* 97 */     return this.cominfo.getCapitalCount(paramString);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/cpt/report/CapitalAssortmentRoots.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */