/*     */ package weaver.cpt.report;
/*     */ 
/*     */ import com.sun.image.codec.jpeg.JPEGCodec;
/*     */ import com.sun.image.codec.jpeg.JPEGEncodeParam;
/*     */ import com.sun.image.codec.jpeg.JPEGImageEncoder;
/*     */ import java.awt.BasicStroke;
/*     */ import java.awt.Color;
/*     */ import java.awt.Font;
/*     */ import java.awt.Graphics2D;
/*     */ import java.awt.image.BufferedImage;
/*     */ import java.io.BufferedOutputStream;
/*     */ import java.io.IOException;
/*     */ import java.io.OutputStream;
/*     */ import java.util.ArrayList;
/*     */ import javax.servlet.ServletException;
/*     */ import javax.servlet.ServletOutputStream;
/*     */ import javax.servlet.http.HttpServlet;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ 
/*     */ public class ShowCptDepartFee
/*     */   extends HttpServlet
/*     */ {
/*  26 */   private int width = 700;
/*  27 */   private int height = 470;
/*     */ 
/*     */ 
/*     */   
/*     */   public void doGet(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws ServletException, IOException {
/*  32 */     RecordSet recordSet1 = new RecordSet();
/*  33 */     RecordSet recordSet2 = new RecordSet();
/*  34 */     String str1 = Util.null2String(paramHttpServletRequest.getParameter("startdate"));
/*  35 */     String str2 = Util.null2String(paramHttpServletRequest.getParameter("enddate"));
/*  36 */     String str3 = Util.null2String(paramHttpServletRequest.getParameter("departmentid"));
/*     */ 
/*     */     
/*     */     try {
/*  40 */       ServletOutputStream servletOutputStream = paramHttpServletResponse.getOutputStream();
/*  41 */       BufferedOutputStream bufferedOutputStream = new BufferedOutputStream((OutputStream)servletOutputStream);
/*     */       
/*  43 */       BufferedImage bufferedImage = new BufferedImage(this.width, this.height, 1);
/*     */       
/*  45 */       JPEGEncodeParam jPEGEncodeParam = null;
/*  46 */       JPEGImageEncoder jPEGImageEncoder = JPEGCodec.createJPEGEncoder(bufferedOutputStream);
/*     */       
/*  48 */       jPEGEncodeParam = jPEGImageEncoder.getDefaultJPEGEncodeParam(bufferedImage);
/*  49 */       jPEGEncodeParam.setQuality(0.9F, true);
/*  50 */       jPEGImageEncoder.setJPEGEncodeParam(jPEGEncodeParam);
/*     */       
/*  52 */       Graphics2D graphics2D = bufferedImage.createGraphics();
/*     */       
/*  54 */       graphics2D.setColor(Color.white);
/*  55 */       graphics2D.fillRect(0, 0, this.width, this.height);
/*     */       
/*  57 */       byte b1 = 50;
/*  58 */       char c1 = 'ǂ';
/*  59 */       char c2 = 'ʨ';
/*  60 */       byte b2 = 0;
/*     */       
/*  62 */       BasicStroke basicStroke = new BasicStroke(3.0F);
/*  63 */       graphics2D.setStroke(basicStroke);
/*     */       
/*  65 */       graphics2D.setColor(Color.black);
/*  66 */       graphics2D.drawLine(b1, c1, b1, b2);
/*  67 */       graphics2D.drawLine(b1, c1, c2, c1);
/*     */       
/*  69 */       drawArrow(b1, c1, b1, b2, graphics2D);
/*  70 */       graphics2D.drawString("X", b1 + 10, b2 + 10);
/*  71 */       drawArrow(b1, c1, c2, c1, graphics2D);
/*  72 */       graphics2D.drawString("Y", c2 + 10, c1 + 5);
/*     */ 
/*     */       
/*  75 */       float f1 = 0.0F;
/*  76 */       String str4 = " select totalamount from bill_CptPlanMain where departmentid = " + str3;
/*  77 */       recordSet1.executeSql(str4);
/*  78 */       if (recordSet1.next()) {
/*  79 */         f1 = recordSet1.getFloat(1);
/*     */       }
/*     */       
/*  82 */       int i = 0;
/*  83 */       str4 = " select datediff(Month,'" + str1 + "','" + str2 + "')";
/*  84 */       recordSet1.executeSql(str4);
/*  85 */       if (recordSet1.next()) {
/*  86 */         i = recordSet1.getInt(1) + 1;
/*     */       }
/*  88 */       ArrayList<String> arrayList1 = new ArrayList();
/*  89 */       ArrayList<String> arrayList2 = new ArrayList();
/*     */       
/*  91 */       int j = Util.getIntValue(str1.substring(5, 7), 0);
/*     */       
/*  93 */       for (byte b3 = 0; b3 < i; b3++) {
/*  94 */         arrayList1.add("" + (j + b3));
/*  95 */         arrayList2.add("");
/*     */       } 
/*     */       
/*  98 */       float f2 = 0.0F;
/*     */       
/* 100 */       str4 = "select totalamount,realizedate from bill_CptAdjustMain where departmentid =" + str3 + " and realizedate >='" + str1 + "' and realizedate <='" + str2 + "'";
/* 101 */       recordSet1.executeSql(str4);
/* 102 */       while (recordSet1.next()) {
/* 103 */         String str = recordSet1.getString("realizedate");
/* 104 */         float f = Util.getFloatValue(recordSet1.getString("totalamount"), 0.0F);
/* 105 */         str4 = " select datediff(Month,'" + str1 + "','" + str + "')";
/* 106 */         recordSet2.executeSql(str4);
/* 107 */         if (recordSet2.next()) {
/* 108 */           int i2 = recordSet2.getInt(1);
/* 109 */           float f4 = Util.getFloatValue("" + arrayList2.get(i2), 0.0F) + f;
/* 110 */           arrayList2.add(i2, "" + f4);
/*     */         } 
/*     */       } 
/* 113 */       str4 = "select totalamount,realizedate from bill_CptFetchMain where departmentid = " + str3 + " and realizedate >='" + str1 + "' and realizedate <='" + str2 + "'";
/* 114 */       recordSet1.executeSql(str4);
/* 115 */       while (recordSet1.next()) {
/* 116 */         String str = recordSet1.getString("realizedate");
/* 117 */         float f = Util.getFloatValue(recordSet1.getString("totalamount"), 0.0F);
/* 118 */         str4 = " select datediff(Month,'" + str1 + "','" + str + "')";
/* 119 */         recordSet2.executeSql(str4);
/* 120 */         if (recordSet2.next()) {
/* 121 */           int i2 = recordSet2.getInt(1);
/* 122 */           float f4 = Util.getFloatValue("" + arrayList2.get(i2), 0.0F) + f;
/* 123 */           arrayList2.add(i2, "" + f4);
/*     */         } 
/*     */       } 
/* 126 */       str4 = "select realizedate,b.inamount as totalamount from bill_CptStockInMain a,bill_CptStockInDetail b where a.id = b.cptstockinid and  departmentid = " + str3 + " and realizedate >='" + str1 + "' and realizedate <='" + str2 + "'";
/* 127 */       recordSet1.executeSql(str4);
/* 128 */       while (recordSet1.next()) {
/* 129 */         String str = recordSet1.getString("realizedate");
/* 130 */         float f = Util.getFloatValue(recordSet1.getString("totalamount"), 0.0F);
/* 131 */         str4 = " select datediff(Month,'" + str1 + "','" + str + "')";
/* 132 */         recordSet2.executeSql(str4);
/* 133 */         if (recordSet2.next()) {
/* 134 */           int i2 = recordSet2.getInt(1);
/* 135 */           float f4 = Util.getFloatValue("" + arrayList2.get(i2), 0.0F) + f;
/* 136 */           arrayList2.add(i2, "" + f4);
/*     */         } 
/*     */       } 
/*     */       int k;
/* 140 */       for (k = 0; k < arrayList1.size(); k++) {
/* 141 */         float f = Util.getFloatValue("" + arrayList2.get(k), 0.0F);
/* 142 */         f2 += f;
/*     */       } 
/* 144 */       if (f1 > f2) {
/* 145 */         f2 = f1;
/*     */       }
/* 147 */       k = (c2 - 20) / (i + 1);
/* 148 */       int m = b1;
/*     */       
/* 150 */       Font font1 = graphics2D.getFont();
/* 151 */       String str5 = font1.getFontName();
/* 152 */       Font font2 = new Font("幼圆", 0, 9);
/* 153 */       graphics2D.setFont(font2);
/*     */       
/* 155 */       int n = (int)(5000.0F / f2 * c1);
/* 156 */       for (byte b4 = 0; b4 < f2 / 5000.0F; b4++) {
/* 157 */         int i2 = b4 * 20;
/* 158 */         graphics2D.drawString("" + (5000 * b4), 10, c1 - n * b4);
/*     */       } 
/*     */       
/* 161 */       float f3 = 0.0F; int i1;
/* 162 */       for (i1 = 0; i1 < arrayList1.size(); i1++) {
/* 163 */         float f = Util.getFloatValue("" + arrayList2.get(i1), 0.0F);
/* 164 */         f3 += f;
/* 165 */         int i2 = (int)(f3 / f2 * (c1 - 20));
/*     */         
/* 167 */         graphics2D.setColor(Color.green);
/* 168 */         graphics2D.fill3DRect(m, c1 - i2, k, i2, true);
/* 169 */         graphics2D.setColor(Color.black);
/* 170 */         graphics2D.drawString("" + f3, m + 5, c1 - i2 - 5);
/*     */         
/* 172 */         graphics2D.drawString("" + arrayList1.get(i1), m + k / 3, c1 + 17);
/* 173 */         m += k;
/*     */       } 
/*     */       
/* 176 */       i1 = (int)(f1 / f2 * (c1 - 20));
/* 177 */       graphics2D.setColor(Color.red);
/* 178 */       graphics2D.drawLine(b1, c1 - i1, c2, c1 - i1);
/* 179 */       graphics2D.setColor(Color.black);
/* 180 */       graphics2D.drawString("" + f1, c2 - 20, c1 - i1 - 5);
/*     */       
/* 182 */       graphics2D.dispose();
/* 183 */       jPEGImageEncoder.encode(bufferedImage);
/*     */ 
/*     */       
/* 186 */       servletOutputStream.flush();
/* 187 */       bufferedOutputStream.close();
/* 188 */       servletOutputStream.close();
/*     */ 
/*     */     
/*     */     }
/* 192 */     catch (Exception exception) {}
/*     */   }
/*     */ 
/*     */   
/*     */   private void drawArrow(int paramInt1, int paramInt2, int paramInt3, int paramInt4, Graphics2D paramGraphics2D) {
/* 197 */     int i = paramInt1 + paramInt2 - paramInt4;
/* 198 */     int j = paramInt2 - paramInt1 + paramInt3;
/* 199 */     int k = paramInt1 - paramInt2 + paramInt4;
/* 200 */     int m = paramInt2 + paramInt1 - paramInt3;
/*     */     
/* 202 */     double d = Math.sqrt(((i - paramInt3) * (i - paramInt3) + (j - paramInt4) * (j - paramInt4)));
/* 203 */     if (d != 0.0D) {
/* 204 */       d = 10.0D / d;
/* 205 */       if (i == paramInt3 && j != paramInt4) {
/* 206 */         j = paramInt4 + 10;
/* 207 */         i = i;
/*     */       }
/* 209 */       else if (j == paramInt4) {
/* 210 */         i = paramInt3 + 10;
/* 211 */         j = j;
/*     */       } else {
/*     */         
/* 214 */         i = (int)(paramInt3 + d * (i - paramInt3));
/* 215 */         j = (int)(paramInt4 + d * (j - paramInt4));
/*     */       } 
/*     */       
/* 218 */       if (k == paramInt3 && m != paramInt4) {
/* 219 */         m = paramInt4 + 10;
/* 220 */         k = k;
/*     */       }
/* 222 */       else if (m == paramInt4) {
/* 223 */         k = paramInt3 + 10;
/* 224 */         m = m;
/*     */       } else {
/*     */         
/* 227 */         k = (int)(paramInt3 + d * (k - paramInt3));
/* 228 */         m = (int)(paramInt4 + d * (m - paramInt4));
/*     */       } 
/*     */     } 
/* 231 */     paramGraphics2D.drawLine(paramInt3, paramInt4, i, j);
/* 232 */     paramGraphics2D.drawLine(paramInt3, paramInt4, k, m);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/cpt/report/ShowCptDepartFee.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */