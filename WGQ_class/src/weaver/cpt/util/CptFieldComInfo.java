/*     */ package weaver.cpt.util;
/*     */ 
/*     */ import java.text.DecimalFormat;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.TreeMap;
/*     */ import org.json.JSONException;
/*     */ import org.json.JSONObject;
/*     */ import weaver.cache.CacheBase;
/*     */ import weaver.cache.CacheColumn;
/*     */ import weaver.cache.CacheColumnType;
/*     */ import weaver.cache.PKColumn;
/*     */ import weaver.cpt.util.html.HtmlUtil;
/*     */ import weaver.general.Util;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CptFieldComInfo
/*     */   extends CacheBase
/*     */ {
/*  28 */   protected static String TABLE_NAME = "CptDefineField";
/*     */   
/*  30 */   protected static String TABLE_WHERE = null;
/*     */   
/*  32 */   protected static String TABLE_ORDER = "groupid ASC, dsporder ASC";
/*     */   
/*     */   @PKColumn(type = CacheColumnType.NUMBER)
/*  35 */   protected static String PK_NAME = "id";
/*     */   
/*     */   @CacheColumn
/*     */   protected static int fieldname;
/*     */   
/*     */   @CacheColumn
/*     */   protected static int fielddbtype;
/*     */   @CacheColumn
/*     */   protected static int fieldhtmltype;
/*     */   @CacheColumn
/*     */   protected static int type;
/*     */   @CacheColumn
/*     */   protected static int imgwidth;
/*     */   @CacheColumn
/*     */   protected static int imgheight;
/*     */   @CacheColumn
/*     */   protected static int fieldlabel;
/*     */   @CacheColumn
/*     */   protected static int viewtype;
/*     */   @CacheColumn
/*     */   protected static int fromuser;
/*     */   @CacheColumn
/*     */   protected static int textheight;
/*     */   @CacheColumn
/*     */   protected static int dsporder;
/*     */   @CacheColumn
/*     */   protected static int isopen;
/*     */   @CacheColumn
/*     */   protected static int ismand;
/*     */   @CacheColumn
/*     */   protected static int isused;
/*     */   @CacheColumn
/*     */   protected static int issystem;
/*     */   @CacheColumn
/*     */   protected static int allowhide;
/*     */   @CacheColumn
/*     */   protected static int groupid;
/*  72 */   private DecimalFormat df = new DecimalFormat("00000.00");
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setTofirstRow() {
/*  80 */     super.setTofirstRow();
/*     */   }
/*     */   
/*     */   public ArrayList getField_ids() {
/*  84 */     if (this.field_ids == null) {
/*  85 */       this.field_ids = new ArrayList();
/*  86 */       CptFieldComInfo cptFieldComInfo = new CptFieldComInfo();
/*  87 */       cptFieldComInfo.setTofirstRow();
/*  88 */       while (cptFieldComInfo.next()) {
/*  89 */         this.field_ids.add(cptFieldComInfo.getFieldid());
/*     */       }
/*     */     } 
/*  92 */     return this.field_ids;
/*     */   }
/*     */   
/*     */   public void setField_ids(ArrayList paramArrayList) {
/*  96 */     this.field_ids = paramArrayList;
/*     */   }
/*     */   
/*  99 */   private ArrayList field_ids = null;
/*     */   
/*     */   public ArrayList getField_names() {
/* 102 */     if (this.field_names == null) {
/* 103 */       this.field_names = new ArrayList();
/* 104 */       CptFieldComInfo cptFieldComInfo = new CptFieldComInfo();
/* 105 */       cptFieldComInfo.setTofirstRow();
/* 106 */       while (cptFieldComInfo.next()) {
/* 107 */         this.field_names.add(cptFieldComInfo.getFieldname());
/*     */       }
/*     */     } 
/* 110 */     return this.field_names;
/*     */   }
/*     */   
/*     */   public void setField_names(ArrayList paramArrayList) {
/* 114 */     this.field_names = paramArrayList;
/*     */   }
/*     */   
/*     */   public ArrayList getField_htmltypes() {
/* 118 */     if (this.field_htmltypes == null) {
/* 119 */       this.field_htmltypes = new ArrayList();
/* 120 */       CptFieldComInfo cptFieldComInfo = new CptFieldComInfo();
/* 121 */       cptFieldComInfo.setTofirstRow();
/* 122 */       while (cptFieldComInfo.next()) {
/* 123 */         this.field_htmltypes.add(cptFieldComInfo.getFieldhtmltype());
/*     */       }
/*     */     } 
/* 126 */     return this.field_htmltypes;
/*     */   }
/*     */   
/*     */   public void setField_htmltypes(ArrayList paramArrayList) {
/* 130 */     this.field_htmltypes = paramArrayList;
/*     */   }
/*     */   
/* 133 */   private ArrayList field_names = null;
/* 134 */   private ArrayList field_htmltypes = null;
/*     */   
/*     */   public ArrayList getField_types() {
/* 137 */     if (this.field_types == null) {
/* 138 */       this.field_types = new ArrayList();
/* 139 */       CptFieldComInfo cptFieldComInfo = new CptFieldComInfo();
/* 140 */       cptFieldComInfo.setTofirstRow();
/* 141 */       while (cptFieldComInfo.next()) {
/* 142 */         this.field_types.add(cptFieldComInfo.getFieldType());
/*     */       }
/*     */     } 
/* 145 */     return this.field_types;
/*     */   }
/*     */   
/*     */   public void setField_types(ArrayList paramArrayList) {
/* 149 */     this.field_types = paramArrayList;
/*     */   }
/*     */   
/* 152 */   private ArrayList field_types = null;
/*     */   
/* 154 */   private TreeMap<String, JSONObject> usedFieldMap = null;
/* 155 */   private TreeMap<String, JSONObject> mandFieldMap = null;
/* 156 */   private TreeMap<String, JSONObject> openFieldMap = null;
/* 157 */   private TreeMap<String, JSONObject> openFieldMapAll = null;
/* 158 */   private TreeMap<String, JSONObject> openSysFieldMap = null;
/* 159 */   private HashMap<String, String> updateFieldMap = null;
/* 160 */   private TreeMap<String, TreeMap<String, JSONObject>> groupFieldMap = null;
/*     */ 
/*     */   
/* 163 */   private String mandfieldStr = ",";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getFieldNum() {
/* 172 */     return size();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean next() {
/* 182 */     return super.next();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean next(String paramString) {
/* 193 */     setTofirstRow();
/* 194 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getFieldid() {
/* 205 */     return (String)getRowValue(0);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getFieldname() {
/* 215 */     return (String)getRowValue(fieldname);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getFieldname(String paramString) {
/* 227 */     return (String)getValue(fieldname, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getFielddbtype(String paramString) {
/* 239 */     return (String)getValue(fielddbtype, paramString);
/*     */   }
/*     */   
/*     */   public String getFielddbtype() {
/* 243 */     return (String)getRowValue(fielddbtype);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getFieldhtmltype(String paramString) {
/* 255 */     return (String)getValue(fieldhtmltype, paramString);
/*     */   }
/*     */   
/*     */   public String getFieldhtmltype() {
/* 259 */     return (String)getRowValue(fieldhtmltype);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getFieldType(String paramString) {
/* 271 */     return (String)getValue(type, paramString);
/*     */   }
/*     */   
/*     */   public String getFieldType() {
/* 275 */     return (String)getRowValue(type);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getImgWidth() {
/* 285 */     return Util.getIntValue((String)getRowValue(imgwidth));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getImgWidth(String paramString) {
/* 293 */     return Util.getIntValue((String)getValue(imgwidth, paramString), 0);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getImgHeight() {
/* 302 */     return Util.getIntValue((String)getRowValue(imgheight));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getImgHeight(String paramString) {
/* 311 */     return Util.getIntValue((String)getValue(imgheight, paramString), 0);
/*     */   }
/*     */ 
/*     */   
/*     */   public String getLabel() {
/* 316 */     return (String)getRowValue(fieldlabel);
/*     */   }
/*     */   public String getLabel(String paramString) {
/* 319 */     return (String)getValue(fieldlabel, paramString);
/*     */   }
/*     */   public String getViewtype() {
/* 322 */     return (String)getRowValue(viewtype);
/*     */   }
/*     */   public String getViewtype(String paramString) {
/* 325 */     return (String)getValue(viewtype, paramString);
/*     */   }
/*     */   public String getFromuser() {
/* 328 */     return (String)getRowValue(fromuser);
/*     */   }
/*     */   public String getFromuser(String paramString) {
/* 331 */     return (String)getValue(fromuser, paramString);
/*     */   }
/*     */   public String getTextheight() {
/* 334 */     return (String)getRowValue(textheight);
/*     */   }
/*     */   public String getTextheight(String paramString) {
/* 337 */     return (String)getValue(textheight, paramString);
/*     */   }
/*     */   public String getDsporder() {
/* 340 */     return (String)getRowValue(dsporder);
/*     */   }
/*     */   public String getDsporder(String paramString) {
/* 343 */     return (String)getValue(dsporder, paramString);
/*     */   }
/*     */   public String getIsopen() {
/* 346 */     return (String)getRowValue(isopen);
/*     */   }
/*     */   public String getIsopen(String paramString) {
/* 349 */     return (String)getValue(isopen, paramString);
/*     */   }
/*     */   public String getIsmand() {
/* 352 */     return (String)getRowValue(ismand);
/*     */   }
/*     */   public String getIsmand(String paramString) {
/* 355 */     return (String)getValue(ismand, paramString);
/*     */   }
/*     */   public String getIsused() {
/* 358 */     return (String)getRowValue(isused);
/*     */   }
/*     */   public String getIsused(String paramString) {
/* 361 */     return (String)getValue(isused, paramString);
/*     */   }
/*     */ 
/*     */   
/*     */   public String getIssystem() {
/* 366 */     return Util.getIntValue((String)getRowValue(issystem), 0) + "";
/*     */   }
/*     */   public String getIssystem(String paramString) {
/* 369 */     return Util.getIntValue((String)getValue(issystem, paramString), 0) + "";
/*     */   }
/*     */   public String getAllowhide() {
/* 372 */     return (String)getRowValue(allowhide);
/*     */   }
/*     */   public String getAllowhide(String paramString) {
/* 375 */     return (String)getValue(allowhide, paramString);
/*     */   }
/*     */   public String getGroupid() {
/* 378 */     return (String)getRowValue(groupid);
/*     */   }
/*     */   public String getGroupid(String paramString) {
/* 381 */     return (String)getValue(groupid, paramString);
/*     */   }
/*     */   public String getFieldkind() {
/* 384 */     return "1";
/*     */   }
/*     */ 
/*     */   
/*     */   public void removeFieldCache() {
/* 389 */     removeCache();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public TreeMap<String, JSONObject> getUsedFieldMap() {
/* 396 */     if (this.usedFieldMap == null) {
/* 397 */       this.usedFieldMap = new TreeMap<>();
/* 398 */       CptFieldComInfo cptFieldComInfo = new CptFieldComInfo();
/* 399 */       cptFieldComInfo.setTofirstRow();
/* 400 */       while (cptFieldComInfo.next()) {
/* 401 */         String str = cptFieldComInfo.getIsused();
/* 402 */         if ("1".equals(str)) {
/* 403 */           JSONObject jSONObject = new JSONObject();
/*     */           try {
/* 405 */             jSONObject.put("id", cptFieldComInfo.getFieldid());
/* 406 */             jSONObject.put("fieldname", cptFieldComInfo.getFieldname());
/* 407 */             jSONObject.put("fielddbtype", cptFieldComInfo.getFielddbtype());
/* 408 */             jSONObject.put("fieldhtmltype", cptFieldComInfo.getFieldhtmltype());
/* 409 */             jSONObject.put("type", cptFieldComInfo.getFieldType());
/* 410 */             jSONObject.put("imgwidth", cptFieldComInfo.getImgWidth());
/* 411 */             jSONObject.put("imgheight", cptFieldComInfo.getImgHeight());
/* 412 */             jSONObject.put("fieldlabel", cptFieldComInfo.getLabel());
/* 413 */             jSONObject.put("viewtype", cptFieldComInfo.getViewtype());
/* 414 */             jSONObject.put("fromuser", cptFieldComInfo.getFromuser());
/* 415 */             jSONObject.put("textheight", cptFieldComInfo.getTextheight());
/* 416 */             jSONObject.put("dsporder", cptFieldComInfo.getDsporder());
/* 417 */             jSONObject.put("isopen", cptFieldComInfo.getIsopen());
/* 418 */             jSONObject.put("ismand", cptFieldComInfo.getIsmand());
/* 419 */             jSONObject.put("isused", cptFieldComInfo.getIsused());
/* 420 */             jSONObject.put("issystem", cptFieldComInfo.getIssystem());
/* 421 */             jSONObject.put("allowhide", cptFieldComInfo.getAllowhide());
/* 422 */             jSONObject.put("groupid", cptFieldComInfo.getGroupid());
/* 423 */             jSONObject.put("fieldkind", cptFieldComInfo.getFieldkind());
/* 424 */             jSONObject.put("eleclazzname", HtmlUtil.getHtmlClassName(cptFieldComInfo.getFieldhtmltype()));
/* 425 */             if ("5".equals(cptFieldComInfo.getFieldhtmltype())) {
/* 426 */               jSONObject.put("seltype", "cpt");
/*     */             }
/* 428 */           } catch (JSONException jSONException) {
/* 429 */             writeLog(jSONException);
/*     */           } 
/* 431 */           this.usedFieldMap.put(this.df.format(Util.getDoubleValue(cptFieldComInfo.getDsporder())), jSONObject);
/*     */         } 
/*     */       } 
/*     */     } 
/* 435 */     return this.usedFieldMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public TreeMap<String, JSONObject> getOpenFieldMap() {
/* 442 */     if (this.openFieldMap == null || this.openFieldMapAll == null || this.openSysFieldMap == null) {
/* 443 */       this.openFieldMap = new TreeMap<>();
/* 444 */       this.openFieldMapAll = new TreeMap<>();
/* 445 */       this.openSysFieldMap = new TreeMap<>();
/* 446 */       CptFieldComInfo cptFieldComInfo = new CptFieldComInfo();
/* 447 */       cptFieldComInfo.setTofirstRow();
/* 448 */       while (cptFieldComInfo.next()) {
/* 449 */         String str = cptFieldComInfo.getIsopen();
/* 450 */         if ("1".equals(str)) {
/* 451 */           JSONObject jSONObject = new JSONObject();
/*     */           try {
/* 453 */             jSONObject.put("id", cptFieldComInfo.getFieldid());
/* 454 */             jSONObject.put("fieldname", cptFieldComInfo.getFieldname());
/* 455 */             jSONObject.put("fielddbtype", cptFieldComInfo.getFielddbtype());
/* 456 */             jSONObject.put("fieldhtmltype", cptFieldComInfo.getFieldhtmltype());
/* 457 */             jSONObject.put("type", cptFieldComInfo.getFieldType());
/* 458 */             jSONObject.put("imgwidth", cptFieldComInfo.getImgWidth());
/* 459 */             jSONObject.put("imgheight", cptFieldComInfo.getImgHeight());
/* 460 */             jSONObject.put("fieldlabel", cptFieldComInfo.getLabel());
/* 461 */             jSONObject.put("viewtype", cptFieldComInfo.getViewtype());
/* 462 */             jSONObject.put("fromuser", cptFieldComInfo.getFromuser());
/* 463 */             jSONObject.put("textheight", cptFieldComInfo.getTextheight());
/* 464 */             jSONObject.put("dsporder", cptFieldComInfo.getDsporder());
/* 465 */             jSONObject.put("isopen", cptFieldComInfo.getIsopen());
/* 466 */             jSONObject.put("ismand", cptFieldComInfo.getIsmand());
/* 467 */             jSONObject.put("isused", cptFieldComInfo.getIsused());
/* 468 */             jSONObject.put("issystem", cptFieldComInfo.getIssystem());
/* 469 */             jSONObject.put("allowhide", cptFieldComInfo.getAllowhide());
/* 470 */             jSONObject.put("groupid", cptFieldComInfo.getGroupid());
/* 471 */             jSONObject.put("fieldkind", cptFieldComInfo.getFieldkind());
/* 472 */             jSONObject.put("eleclazzname", HtmlUtil.getHtmlClassName(cptFieldComInfo.getFieldhtmltype()));
/* 473 */             if ("5".equals(cptFieldComInfo.getFieldhtmltype())) {
/* 474 */               jSONObject.put("seltype", "cpt");
/*     */             }
/* 476 */           } catch (JSONException jSONException) {
/* 477 */             writeLog(jSONException);
/*     */           } 
/* 479 */           String str1 = this.df.format(Util.getDoubleValue(cptFieldComInfo.getDsporder()));
/* 480 */           if ("warehouse".equals(cptFieldComInfo.getFieldname().toLowerCase())) {
/* 481 */             str1 = str1 + "_1";
/*     */           }
/* 483 */           this.openFieldMapAll.put(str1, jSONObject);
/* 484 */           if ("1".equals(cptFieldComInfo.getIssystem())) {
/* 485 */             this.openSysFieldMap.put(str1, jSONObject); continue;
/*     */           } 
/* 487 */           this.openFieldMap.put(str1, jSONObject);
/*     */         } 
/*     */       } 
/*     */     } 
/*     */     
/* 492 */     return this.openFieldMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public TreeMap<String, JSONObject> getOpenFieldMap(String paramString) {
/* 500 */     getOpenFieldMap();
/* 501 */     if ("all".equalsIgnoreCase(paramString))
/* 502 */       return this.openFieldMapAll; 
/* 503 */     if ("sys".equalsIgnoreCase(paramString)) {
/* 504 */       return this.openSysFieldMap;
/*     */     }
/* 506 */     return this.openFieldMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public TreeMap<String, TreeMap<String, JSONObject>> getGroupFieldMap() {
/* 514 */     if (this.groupFieldMap == null) {
/* 515 */       this.groupFieldMap = new TreeMap<>();
/* 516 */       CptFieldComInfo cptFieldComInfo = new CptFieldComInfo();
/* 517 */       cptFieldComInfo.setTofirstRow();
/* 518 */       while (cptFieldComInfo.next()) {
/* 519 */         String str = cptFieldComInfo.getIsopen();
/* 520 */         if ("1".equals(str)) {
/* 521 */           JSONObject jSONObject = new JSONObject();
/*     */           try {
/* 523 */             jSONObject.put("id", cptFieldComInfo.getFieldid());
/* 524 */             jSONObject.put("fieldname", cptFieldComInfo.getFieldname());
/* 525 */             jSONObject.put("fielddbtype", cptFieldComInfo.getFielddbtype());
/* 526 */             jSONObject.put("fieldhtmltype", cptFieldComInfo.getFieldhtmltype());
/* 527 */             jSONObject.put("type", cptFieldComInfo.getFieldType());
/* 528 */             jSONObject.put("imgwidth", cptFieldComInfo.getImgWidth());
/* 529 */             jSONObject.put("imgheight", cptFieldComInfo.getImgHeight());
/* 530 */             jSONObject.put("fieldlabel", cptFieldComInfo.getLabel());
/* 531 */             jSONObject.put("viewtype", cptFieldComInfo.getViewtype());
/* 532 */             jSONObject.put("fromuser", cptFieldComInfo.getFromuser());
/* 533 */             jSONObject.put("textheight", cptFieldComInfo.getTextheight());
/* 534 */             jSONObject.put("dsporder", cptFieldComInfo.getDsporder());
/* 535 */             jSONObject.put("isopen", cptFieldComInfo.getIsopen());
/* 536 */             jSONObject.put("ismand", cptFieldComInfo.getIsmand());
/* 537 */             jSONObject.put("isused", cptFieldComInfo.getIsused());
/* 538 */             jSONObject.put("issystem", cptFieldComInfo.getIssystem());
/* 539 */             jSONObject.put("allowhide", cptFieldComInfo.getAllowhide());
/* 540 */             jSONObject.put("groupid", cptFieldComInfo.getGroupid());
/* 541 */             jSONObject.put("fieldkind", cptFieldComInfo.getFieldkind());
/* 542 */             jSONObject.put("eleclazzname", HtmlUtil.getHtmlClassName(cptFieldComInfo.getFieldhtmltype()));
/* 543 */             if ("5".equals(cptFieldComInfo.getFieldhtmltype())) {
/* 544 */               jSONObject.put("seltype", "cpt");
/*     */             }
/* 546 */           } catch (JSONException jSONException) {
/* 547 */             writeLog(jSONException);
/*     */           } 
/*     */           
/* 550 */           String str1 = this.df.format(Util.getDoubleValue(cptFieldComInfo.getDsporder()));
/* 551 */           if ("warehouse".equals(cptFieldComInfo.getFieldname().toLowerCase())) {
/* 552 */             str1 = str1 + "_1";
/*     */           }
/* 554 */           TreeMap<String, JSONObject> treeMap = this.groupFieldMap.get(cptFieldComInfo.getGroupid());
/* 555 */           if (treeMap != null) {
/* 556 */             treeMap.put(str1, jSONObject); continue;
/*     */           } 
/* 558 */           treeMap = new TreeMap<>();
/* 559 */           treeMap.put(str1, jSONObject);
/* 560 */           this.groupFieldMap.put(cptFieldComInfo.getGroupid(), treeMap);
/*     */         } 
/*     */       } 
/*     */     } 
/*     */     
/* 565 */     return this.groupFieldMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public TreeMap<String, JSONObject> getMandFieldMap() {
/* 573 */     if (this.mandFieldMap == null) {
/* 574 */       this.mandFieldMap = new TreeMap<>();
/* 575 */       CptFieldComInfo cptFieldComInfo = new CptFieldComInfo();
/* 576 */       cptFieldComInfo.setTofirstRow();
/* 577 */       while (cptFieldComInfo.next()) {
/* 578 */         if (!"1".equals(cptFieldComInfo.getIssystem()) && "1".equals(cptFieldComInfo.getIsmand())) {
/* 579 */           JSONObject jSONObject = new JSONObject();
/*     */           try {
/* 581 */             jSONObject.put("id", cptFieldComInfo.getFieldid());
/* 582 */             jSONObject.put("fieldname", cptFieldComInfo.getFieldname());
/* 583 */             jSONObject.put("fielddbtype", cptFieldComInfo.getFielddbtype());
/* 584 */             jSONObject.put("fieldhtmltype", cptFieldComInfo.getFieldhtmltype());
/* 585 */             jSONObject.put("type", cptFieldComInfo.getFieldType());
/* 586 */             jSONObject.put("imgwidth", cptFieldComInfo.getImgWidth());
/* 587 */             jSONObject.put("imgheight", cptFieldComInfo.getImgHeight());
/* 588 */             jSONObject.put("fieldlabel", cptFieldComInfo.getLabel());
/* 589 */             jSONObject.put("viewtype", cptFieldComInfo.getViewtype());
/* 590 */             jSONObject.put("fromuser", cptFieldComInfo.getFromuser());
/* 591 */             jSONObject.put("textheight", cptFieldComInfo.getTextheight());
/* 592 */             jSONObject.put("dsporder", cptFieldComInfo.getDsporder());
/* 593 */             jSONObject.put("isopen", cptFieldComInfo.getIsopen());
/* 594 */             jSONObject.put("ismand", cptFieldComInfo.getIsmand());
/* 595 */             jSONObject.put("isused", cptFieldComInfo.getIsused());
/* 596 */             jSONObject.put("issystem", cptFieldComInfo.getIssystem());
/* 597 */             jSONObject.put("allowhide", cptFieldComInfo.getAllowhide());
/* 598 */             jSONObject.put("groupid", cptFieldComInfo.getGroupid());
/* 599 */             jSONObject.put("fieldkind", cptFieldComInfo.getFieldkind());
/* 600 */             jSONObject.put("eleclazzname", HtmlUtil.getHtmlClassName(cptFieldComInfo.getFieldhtmltype()));
/* 601 */             if ("5".equals(cptFieldComInfo.getFieldhtmltype())) {
/* 602 */               jSONObject.put("seltype", "cpt");
/*     */             }
/* 604 */           } catch (JSONException jSONException) {
/* 605 */             writeLog(jSONException);
/*     */           } 
/* 607 */           String str = this.df.format(Util.getDoubleValue(cptFieldComInfo.getDsporder()));
/* 608 */           if ("warehouse".equals(cptFieldComInfo.getFieldname().toLowerCase())) {
/* 609 */             str = str + "_1";
/*     */           }
/* 611 */           this.mandFieldMap.put(str, jSONObject);
/*     */         } 
/*     */       } 
/*     */     } 
/* 615 */     return this.mandFieldMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getMandFieldStr() {
/* 622 */     CptFieldComInfo cptFieldComInfo = new CptFieldComInfo();
/* 623 */     cptFieldComInfo.setTofirstRow();
/* 624 */     while (cptFieldComInfo.next()) {
/* 625 */       if ("1".equals(cptFieldComInfo.getIssystem()) && "1".equals(cptFieldComInfo.getIsopen()) && "1".equals(cptFieldComInfo.getIsmand())) {
/* 626 */         this.mandfieldStr += cptFieldComInfo.getFieldname() + ","; continue;
/* 627 */       }  if (!"1".equals(cptFieldComInfo.getIssystem()) && "1".equals(cptFieldComInfo.getIsopen()) && "1".equals(cptFieldComInfo.getIsmand())) {
/* 628 */         this.mandfieldStr += "field" + cptFieldComInfo.getFieldid() + ",";
/*     */       }
/*     */     } 
/* 631 */     return this.mandfieldStr;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public ArrayList<String> getUpdateFieldList(String paramString) {
/* 638 */     ArrayList<String> arrayList1 = new ArrayList();
/* 639 */     ArrayList<String> arrayList2 = new ArrayList();
/* 640 */     String str = "";
/* 641 */     if (paramString.equals("1")) {
/* 642 */       str = str + ",name,mark,deprestartdate,departmentid,capitalgroupid,barcode,fnamark,stateid,blongdepartment,sptcount,capitalnum,isinner,startdate,enddate,manudate,stockindate,selectdate,location,contractno,invoice,alertnum,warehouse,";
/*     */     } else {
/* 644 */       str = str + ",name,mark,deprestartdate,departmentid,resourceid,capitalgroupid,stateid,blongsubcompany,sptcount,capitalnum,alertnum,warehouse,";
/*     */     } 
/* 646 */     if (this.updateFieldMap == null) {
/* 647 */       this.updateFieldMap = new HashMap<>();
/* 648 */       CptFieldComInfo cptFieldComInfo = new CptFieldComInfo();
/* 649 */       cptFieldComInfo.setTofirstRow();
/* 650 */       while (cptFieldComInfo.next()) {
/* 651 */         if ("1".equals(cptFieldComInfo.getIsopen()) && str.indexOf("," + cptFieldComInfo.getFieldname() + ",") == -1) {
/* 652 */           if ("1".equals(cptFieldComInfo.getIssystem())) { arrayList1.add(cptFieldComInfo.getFieldid()); continue; }
/* 653 */            arrayList2.add(cptFieldComInfo.getFieldid());
/*     */         } 
/*     */       } 
/* 656 */       arrayList1.addAll(arrayList2);
/*     */     } 
/* 658 */     return arrayList1;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/cpt/util/CptFieldComInfo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */