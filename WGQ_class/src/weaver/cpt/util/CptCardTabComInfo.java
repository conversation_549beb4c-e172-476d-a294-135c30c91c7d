/*     */ package weaver.cpt.util;
/*     */ 
/*     */ import java.text.DecimalFormat;
/*     */ import java.util.TreeMap;
/*     */ import org.json.JSONException;
/*     */ import org.json.JSONObject;
/*     */ import weaver.cache.CacheBase;
/*     */ import weaver.cache.CacheColumn;
/*     */ import weaver.cache.CacheColumnType;
/*     */ import weaver.cache.PKColumn;
/*     */ import weaver.general.Util;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CptCardTabComInfo
/*     */   extends CacheBase
/*     */ {
/*  25 */   protected static String TABLE_NAME = "cpt_cptcardtab";
/*     */   
/*  27 */   protected static String TABLE_WHERE = null;
/*     */   
/*  29 */   protected static String TABLE_ORDER = "dsporder ASC";
/*     */   
/*     */   @PKColumn(type = CacheColumnType.NUMBER)
/*  32 */   protected static String PK_NAME = "id";
/*     */   
/*     */   @CacheColumn
/*     */   protected static int groupname;
/*     */   
/*     */   @CacheColumn
/*     */   protected static int grouplabel;
/*     */   @CacheColumn
/*     */   protected static int dsporder;
/*     */   @CacheColumn
/*     */   protected static int isopen;
/*     */   @CacheColumn
/*     */   protected static int ismand;
/*     */   @CacheColumn
/*     */   protected static int isused;
/*     */   @CacheColumn
/*     */   protected static int issystem;
/*     */   @CacheColumn
/*     */   protected static int linkurl;
/*  51 */   private DecimalFormat df = new DecimalFormat("00000.00");
/*  52 */   private TreeMap<String, JSONObject> usedFieldMap = null;
/*  53 */   private TreeMap<String, JSONObject> mandFieldMap = null;
/*  54 */   private TreeMap<String, JSONObject> openFieldMap = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getGroupNum() {
/*  63 */     return size();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean next() {
/*  73 */     return super.next();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean next(String paramString) {
/*  84 */     setTofirstRow();
/*  85 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getGroupid() {
/*  96 */     return (String)getRowValue(0);
/*     */   }
/*     */ 
/*     */   
/*     */   public String getGroupName() {
/* 101 */     return (String)getRowValue(groupname);
/*     */   }
/*     */ 
/*     */   
/*     */   public String getGroupName(String paramString) {
/* 106 */     return (String)getValue(groupname, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public String getLabel() {
/* 112 */     return (String)getRowValue(grouplabel);
/*     */   }
/*     */   public String getLabel(String paramString) {
/* 115 */     return (String)getValue(grouplabel, paramString);
/*     */   }
/*     */   public String getDsporder() {
/* 118 */     return (String)getRowValue(dsporder);
/*     */   }
/*     */   public String getDsporder(String paramString) {
/* 121 */     return (String)getValue(dsporder, paramString);
/*     */   }
/*     */   public String getIsopen() {
/* 124 */     return (String)getRowValue(isopen);
/*     */   }
/*     */   public String getIsopen(String paramString) {
/* 127 */     return (String)getValue(isopen, paramString);
/*     */   }
/*     */   public String getIsmand() {
/* 130 */     return (String)getRowValue(ismand);
/*     */   }
/*     */   public String getIsmand(String paramString) {
/* 133 */     return (String)getValue(ismand, paramString);
/*     */   }
/*     */   public String getIsused() {
/* 136 */     return (String)getRowValue(isused);
/*     */   }
/*     */   public String getIsused(String paramString) {
/* 139 */     return (String)getValue(isused, paramString);
/*     */   }
/*     */   public String getIsSystem() {
/* 142 */     return (String)getRowValue(issystem);
/*     */   }
/*     */   public String getIsSystem(String paramString) {
/* 145 */     return (String)getValue(issystem, paramString);
/*     */   }
/*     */   public String getLinkurl() {
/* 148 */     return (String)getRowValue(linkurl);
/*     */   }
/*     */   public String getLinkurl(String paramString) {
/* 151 */     return (String)getValue(linkurl, paramString);
/*     */   }
/*     */   
/*     */   public void removeCache() {
/* 155 */     super.removeCache();
/*     */   }
/*     */   public void setTofirstRow() {
/* 158 */     super.setTofirstRow();
/*     */   }
/*     */   
/*     */   public TreeMap<String, JSONObject> getUsedFieldMap() {
/* 162 */     if (this.usedFieldMap == null) {
/* 163 */       this.usedFieldMap = new TreeMap<>();
/* 164 */       CptCardTabComInfo cptCardTabComInfo = new CptCardTabComInfo();
/* 165 */       cptCardTabComInfo.setTofirstRow();
/* 166 */       while (cptCardTabComInfo.next()) {
/* 167 */         String str = cptCardTabComInfo.getIsused();
/* 168 */         if ("1".equals(str)) {
/* 169 */           JSONObject jSONObject = new JSONObject();
/*     */           try {
/* 171 */             jSONObject.put("id", cptCardTabComInfo.getGroupid());
/* 172 */             jSONObject.put("fieldname", cptCardTabComInfo.getGroupName());
/* 173 */             jSONObject.put("fieldlabel", cptCardTabComInfo.getLabel());
/* 174 */             jSONObject.put("dsporder", cptCardTabComInfo.getDsporder());
/* 175 */             jSONObject.put("isopen", cptCardTabComInfo.getIsopen());
/* 176 */             jSONObject.put("ismand", cptCardTabComInfo.getIsmand());
/* 177 */             jSONObject.put("isused", cptCardTabComInfo.getIsused());
/* 178 */             jSONObject.put("issystem", cptCardTabComInfo.getIsSystem());
/* 179 */             jSONObject.put("linkurl", cptCardTabComInfo.getLinkurl());
/* 180 */           } catch (JSONException jSONException) {
/* 181 */             writeLog(jSONException);
/*     */           } 
/* 183 */           this.usedFieldMap.put(this.df.format(Util.getDoubleValue(cptCardTabComInfo.getDsporder())), jSONObject);
/*     */         } 
/*     */       } 
/*     */     } 
/* 187 */     return this.usedFieldMap;
/*     */   }
/*     */   public TreeMap<String, JSONObject> getOpenFieldMap() {
/* 190 */     if (this.openFieldMap == null) {
/* 191 */       this.openFieldMap = new TreeMap<>();
/* 192 */       CptCardTabComInfo cptCardTabComInfo = new CptCardTabComInfo();
/* 193 */       cptCardTabComInfo.setTofirstRow();
/* 194 */       while (cptCardTabComInfo.next()) {
/* 195 */         String str = cptCardTabComInfo.getIsopen();
/* 196 */         if ("1".equals(str)) {
/* 197 */           JSONObject jSONObject = new JSONObject();
/*     */           try {
/* 199 */             jSONObject.put("id", cptCardTabComInfo.getGroupid());
/* 200 */             jSONObject.put("fieldname", cptCardTabComInfo.getGroupName());
/* 201 */             jSONObject.put("fieldlabel", cptCardTabComInfo.getLabel());
/* 202 */             jSONObject.put("dsporder", cptCardTabComInfo.getDsporder());
/* 203 */             jSONObject.put("isopen", cptCardTabComInfo.getIsopen());
/* 204 */             jSONObject.put("ismand", cptCardTabComInfo.getIsmand());
/* 205 */             jSONObject.put("isused", cptCardTabComInfo.getIsused());
/* 206 */             jSONObject.put("issystem", cptCardTabComInfo.getIsSystem());
/* 207 */             jSONObject.put("linkurl", cptCardTabComInfo.getLinkurl());
/* 208 */           } catch (JSONException jSONException) {
/* 209 */             writeLog(jSONException);
/*     */           } 
/* 211 */           this.openFieldMap.put(this.df.format(Util.getDoubleValue(cptCardTabComInfo.getDsporder())), jSONObject);
/*     */         } 
/*     */       } 
/*     */     } 
/* 215 */     return this.openFieldMap;
/*     */   }
/*     */   public TreeMap<String, JSONObject> getMandFieldMap() {
/* 218 */     if (this.mandFieldMap == null) {
/* 219 */       this.mandFieldMap = new TreeMap<>();
/* 220 */       CptCardTabComInfo cptCardTabComInfo = new CptCardTabComInfo();
/* 221 */       cptCardTabComInfo.setTofirstRow();
/* 222 */       while (cptCardTabComInfo.next()) {
/* 223 */         String str = cptCardTabComInfo.getIsmand();
/* 224 */         if ("1".equals(str)) {
/* 225 */           JSONObject jSONObject = new JSONObject();
/*     */           try {
/* 227 */             jSONObject.put("id", cptCardTabComInfo.getGroupid());
/* 228 */             jSONObject.put("fieldname", cptCardTabComInfo.getGroupName());
/* 229 */             jSONObject.put("fieldlabel", cptCardTabComInfo.getLabel());
/* 230 */             jSONObject.put("dsporder", cptCardTabComInfo.getDsporder());
/* 231 */             jSONObject.put("isopen", cptCardTabComInfo.getIsopen());
/* 232 */             jSONObject.put("ismand", cptCardTabComInfo.getIsmand());
/* 233 */             jSONObject.put("isused", cptCardTabComInfo.getIsused());
/* 234 */             jSONObject.put("issystem", cptCardTabComInfo.getIsSystem());
/* 235 */             jSONObject.put("linkurl", cptCardTabComInfo.getLinkurl());
/* 236 */           } catch (JSONException jSONException) {
/* 237 */             writeLog(jSONException);
/*     */           } 
/* 239 */           this.mandFieldMap.put(this.df.format(Util.getDoubleValue(cptCardTabComInfo.getDsporder())), jSONObject);
/*     */         } 
/*     */       } 
/*     */     } 
/* 243 */     return this.mandFieldMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/cpt/util/CptCardTabComInfo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */