/*     */ package weaver.cpt.util;
/*     */ 
/*     */ import java.util.HashMap;
/*     */ import java.util.TreeMap;
/*     */ import org.json.JSONException;
/*     */ import org.json.JSONObject;
/*     */ import weaver.cache.CacheBase;
/*     */ import weaver.cache.CacheColumn;
/*     */ import weaver.cache.CacheColumnType;
/*     */ import weaver.cache.PKColumn;
/*     */ import weaver.cpt.util.html.HtmlUtil;
/*     */ import weaver.general.Util;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CptDetailFieldComInfo
/*     */   extends CacheBase
/*     */ {
/*  26 */   protected static String TABLE_NAME = "cptdetailfield";
/*     */   
/*  28 */   protected static String TABLE_WHERE = null;
/*     */   
/*  30 */   protected static String TABLE_ORDER = "detailtable ASC, dsporder ASC";
/*     */   
/*     */   @PKColumn(type = CacheColumnType.NUMBER)
/*  33 */   protected static String PK_NAME = "id";
/*     */   
/*     */   @CacheColumn
/*     */   protected static int field_id;
/*     */   
/*     */   @CacheColumn
/*     */   protected static int field_name;
/*     */   @CacheColumn
/*     */   protected static int field_dbtype;
/*     */   @CacheColumn
/*     */   protected static int field_htmltype;
/*     */   @CacheColumn
/*     */   protected static int field_type;
/*     */   @CacheColumn
/*     */   protected static int imgwidth;
/*     */   @CacheColumn
/*     */   protected static int imgheight;
/*     */   @CacheColumn
/*     */   protected static int label;
/*     */   @CacheColumn
/*     */   protected static int viewtype;
/*     */   @CacheColumn
/*     */   protected static int fromuser;
/*     */   @CacheColumn
/*     */   protected static int textheight;
/*     */   @CacheColumn
/*     */   protected static int dsporder;
/*     */   @CacheColumn
/*     */   protected static int isopen;
/*     */   @CacheColumn
/*     */   protected static int ismand;
/*     */   @CacheColumn
/*     */   protected static int isused;
/*     */   @CacheColumn
/*     */   protected static int detailtable;
/*     */   @CacheColumn
/*     */   protected static int otherpara;
/*  70 */   private TreeMap<String, JSONObject> usedFieldMap = null;
/*  71 */   private TreeMap<String, JSONObject> mandFieldMap = null;
/*  72 */   private TreeMap<String, JSONObject> openFieldMap = null;
/*     */   
/*  74 */   private HashMap<String, TreeMap<String, JSONObject>> detailTypeOpenFieldMap = null;
/*  75 */   private HashMap<String, TreeMap<String, JSONObject>> detailTypeMandFieldMap = null;
/*  76 */   private HashMap<String, TreeMap<String, JSONObject>> detailTypeUsedFieldMap = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getFieldNum() {
/*  85 */     return size();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean next() {
/*  95 */     return super.next();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean next(String paramString) {
/* 106 */     setTofirstRow();
/* 107 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getFieldid() {
/* 118 */     return (String)getRowValue(field_id);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getFieldname() {
/* 128 */     return (String)getRowValue(field_name);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getFieldname(String paramString) {
/* 140 */     return (String)getValue(field_name, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getFielddbtype(String paramString) {
/* 152 */     return (String)getValue(field_dbtype, paramString);
/*     */   }
/*     */   
/*     */   public String getFielddbtype() {
/* 156 */     return (String)getRowValue(field_dbtype);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getFieldhtmltype(String paramString) {
/* 168 */     return (String)getValue(field_htmltype, paramString);
/*     */   }
/*     */   
/*     */   public String getFieldhtmltype() {
/* 172 */     return (String)getRowValue(field_htmltype);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getFieldType(String paramString) {
/* 184 */     return (String)getValue(field_type, paramString);
/*     */   }
/*     */   
/*     */   public String getFieldType() {
/* 188 */     return (String)getRowValue(field_type);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getImgWidth() {
/* 198 */     return Util.getIntValue((String)getRowValue(imgwidth));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getImgWidth(String paramString) {
/* 206 */     return Util.getIntValue((String)getValue(imgwidth, paramString), 0);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getImgHeight() {
/* 215 */     return Util.getIntValue((String)getRowValue(imgheight));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getImgHeight(String paramString) {
/* 224 */     return Util.getIntValue((String)getValue(imgheight, paramString), 0);
/*     */   }
/*     */ 
/*     */   
/*     */   public String getLabel() {
/* 229 */     return (String)getRowValue(label);
/*     */   }
/*     */   public String getLabel(String paramString) {
/* 232 */     return (String)getValue(label, paramString);
/*     */   }
/*     */   public String getViewtype() {
/* 235 */     return (String)getRowValue(viewtype);
/*     */   }
/*     */   public String getViewtype(String paramString) {
/* 238 */     return (String)getValue(viewtype, paramString);
/*     */   }
/*     */   public String getFromuser() {
/* 241 */     return (String)getRowValue(fromuser);
/*     */   }
/*     */   public String getFromuser(String paramString) {
/* 244 */     return (String)getValue(fromuser, paramString);
/*     */   }
/*     */   public String getTextheight() {
/* 247 */     return (String)getRowValue(textheight);
/*     */   }
/*     */   public String getTextheight(String paramString) {
/* 250 */     return (String)getValue(textheight, paramString);
/*     */   }
/*     */   public String getDsporder() {
/* 253 */     return (String)getRowValue(dsporder);
/*     */   }
/*     */   public String getDsporder(String paramString) {
/* 256 */     return (String)getValue(dsporder, paramString);
/*     */   }
/*     */   public String getIsopen() {
/* 259 */     return (String)getRowValue(isopen);
/*     */   }
/*     */   public String getIsopen(String paramString) {
/* 262 */     return (String)getValue(isopen, paramString);
/*     */   }
/*     */   public String getIsmand() {
/* 265 */     return (String)getRowValue(ismand);
/*     */   }
/*     */   public String getIsmand(String paramString) {
/* 268 */     return (String)getValue(ismand, paramString);
/*     */   }
/*     */   public String getIsused() {
/* 271 */     return (String)getRowValue(isused);
/*     */   }
/*     */   public String getIsused(String paramString) {
/* 274 */     return (String)getValue(isused, paramString);
/*     */   }
/*     */   
/*     */   public String getOtherPara() {
/* 278 */     return (String)getRowValue(otherpara);
/*     */   }
/*     */   public String getOtherPara(String paramString) {
/* 281 */     return (String)getValue(otherpara, paramString);
/*     */   }
/*     */   
/*     */   public String getDetailtable() {
/* 285 */     return ((String)getRowValue(detailtable)).toUpperCase();
/*     */   }
/*     */   
/*     */   public void removeFieldCache() {
/* 289 */     removeCache();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public TreeMap<String, JSONObject> getUsedFieldMap() {
/* 297 */     if (this.usedFieldMap == null) {
/* 298 */       this.usedFieldMap = new TreeMap<>();
/* 299 */       CptDetailFieldComInfo cptDetailFieldComInfo = new CptDetailFieldComInfo();
/* 300 */       cptDetailFieldComInfo.setTofirstRow();
/* 301 */       while (cptDetailFieldComInfo.next()) {
/* 302 */         String str = cptDetailFieldComInfo.getIsused();
/* 303 */         if ("1".equals(str)) {
/* 304 */           JSONObject jSONObject = new JSONObject();
/*     */           try {
/* 306 */             jSONObject.put("id", cptDetailFieldComInfo.getFieldid());
/* 307 */             jSONObject.put("fieldname", cptDetailFieldComInfo.getFieldname());
/* 308 */             jSONObject.put("fielddbtype", cptDetailFieldComInfo.getFielddbtype());
/* 309 */             jSONObject.put("fieldhtmltype", cptDetailFieldComInfo.getFieldhtmltype());
/* 310 */             jSONObject.put("type", cptDetailFieldComInfo.getFieldType());
/* 311 */             jSONObject.put("imgwidth", cptDetailFieldComInfo.getImgWidth());
/* 312 */             jSONObject.put("imgheight", cptDetailFieldComInfo.getImgHeight());
/* 313 */             jSONObject.put("fieldlabel", cptDetailFieldComInfo.getLabel());
/* 314 */             jSONObject.put("viewtype", cptDetailFieldComInfo.getViewtype());
/* 315 */             jSONObject.put("fromuser", cptDetailFieldComInfo.getFromuser());
/* 316 */             jSONObject.put("textheight", cptDetailFieldComInfo.getTextheight());
/* 317 */             jSONObject.put("dsporder", cptDetailFieldComInfo.getDsporder());
/* 318 */             jSONObject.put("isopen", cptDetailFieldComInfo.getIsopen());
/* 319 */             jSONObject.put("ismand", cptDetailFieldComInfo.getIsmand());
/* 320 */             jSONObject.put("isused", cptDetailFieldComInfo.getIsused());
/* 321 */             jSONObject.put("detailtable", cptDetailFieldComInfo.getDetailtable());
/* 322 */             jSONObject.put("otherpara", cptDetailFieldComInfo.getOtherPara());
/* 323 */             jSONObject.put("eleclazzname", HtmlUtil.getHtmlClassName(cptDetailFieldComInfo.getFieldhtmltype()));
/* 324 */           } catch (JSONException jSONException) {
/* 325 */             writeLog(jSONException);
/*     */           } 
/* 327 */           this.usedFieldMap.put(cptDetailFieldComInfo.getDsporder(), jSONObject);
/*     */         } 
/*     */       } 
/*     */     } 
/* 331 */     return this.usedFieldMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public TreeMap<String, JSONObject> getOpenFieldMap() {
/* 338 */     if (this.openFieldMap == null) {
/* 339 */       this.openFieldMap = new TreeMap<>();
/* 340 */       CptDetailFieldComInfo cptDetailFieldComInfo = new CptDetailFieldComInfo();
/* 341 */       cptDetailFieldComInfo.setTofirstRow();
/* 342 */       while (cptDetailFieldComInfo.next()) {
/* 343 */         String str = cptDetailFieldComInfo.getIsopen();
/* 344 */         if ("1".equals(str)) {
/* 345 */           JSONObject jSONObject = new JSONObject();
/*     */           try {
/* 347 */             jSONObject.put("id", cptDetailFieldComInfo.getFieldid());
/* 348 */             jSONObject.put("fieldname", cptDetailFieldComInfo.getFieldname());
/* 349 */             jSONObject.put("fielddbtype", cptDetailFieldComInfo.getFielddbtype());
/* 350 */             jSONObject.put("fieldhtmltype", cptDetailFieldComInfo.getFieldhtmltype());
/* 351 */             jSONObject.put("type", cptDetailFieldComInfo.getFieldType());
/* 352 */             jSONObject.put("imgwidth", cptDetailFieldComInfo.getImgWidth());
/* 353 */             jSONObject.put("imgheight", cptDetailFieldComInfo.getImgHeight());
/* 354 */             jSONObject.put("fieldlabel", cptDetailFieldComInfo.getLabel());
/* 355 */             jSONObject.put("viewtype", cptDetailFieldComInfo.getViewtype());
/* 356 */             jSONObject.put("fromuser", cptDetailFieldComInfo.getFromuser());
/* 357 */             jSONObject.put("textheight", cptDetailFieldComInfo.getTextheight());
/* 358 */             jSONObject.put("dsporder", cptDetailFieldComInfo.getDsporder());
/* 359 */             jSONObject.put("isopen", cptDetailFieldComInfo.getIsopen());
/* 360 */             jSONObject.put("ismand", cptDetailFieldComInfo.getIsmand());
/* 361 */             jSONObject.put("isused", cptDetailFieldComInfo.getIsused());
/* 362 */             jSONObject.put("detailtable", cptDetailFieldComInfo.getDetailtable());
/* 363 */             jSONObject.put("otherpara", cptDetailFieldComInfo.getOtherPara());
/* 364 */             jSONObject.put("eleclazzname", HtmlUtil.getHtmlClassName(cptDetailFieldComInfo.getFieldhtmltype()));
/* 365 */           } catch (JSONException jSONException) {
/* 366 */             writeLog(jSONException);
/*     */           } 
/* 368 */           this.openFieldMap.put(cptDetailFieldComInfo.getDsporder(), jSONObject);
/*     */         } 
/*     */       } 
/*     */     } 
/* 372 */     return this.openFieldMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public TreeMap<String, JSONObject> getMandFieldMap() {
/* 379 */     if (this.mandFieldMap == null) {
/* 380 */       this.mandFieldMap = new TreeMap<>();
/* 381 */       CptDetailFieldComInfo cptDetailFieldComInfo = new CptDetailFieldComInfo();
/* 382 */       cptDetailFieldComInfo.setTofirstRow();
/* 383 */       while (cptDetailFieldComInfo.next()) {
/* 384 */         String str = cptDetailFieldComInfo.getIsmand();
/* 385 */         if ("1".equals(str)) {
/* 386 */           JSONObject jSONObject = new JSONObject();
/*     */           try {
/* 388 */             jSONObject.put("id", cptDetailFieldComInfo.getFieldid());
/* 389 */             jSONObject.put("fieldname", cptDetailFieldComInfo.getFieldname());
/* 390 */             jSONObject.put("fielddbtype", cptDetailFieldComInfo.getFielddbtype());
/* 391 */             jSONObject.put("fieldhtmltype", cptDetailFieldComInfo.getFieldhtmltype());
/* 392 */             jSONObject.put("type", cptDetailFieldComInfo.getFieldType());
/* 393 */             jSONObject.put("imgwidth", cptDetailFieldComInfo.getImgWidth());
/* 394 */             jSONObject.put("imgheight", cptDetailFieldComInfo.getImgHeight());
/* 395 */             jSONObject.put("fieldlabel", cptDetailFieldComInfo.getLabel());
/* 396 */             jSONObject.put("viewtype", cptDetailFieldComInfo.getViewtype());
/* 397 */             jSONObject.put("fromuser", cptDetailFieldComInfo.getFromuser());
/* 398 */             jSONObject.put("textheight", cptDetailFieldComInfo.getTextheight());
/* 399 */             jSONObject.put("dsporder", cptDetailFieldComInfo.getDsporder());
/* 400 */             jSONObject.put("isopen", cptDetailFieldComInfo.getIsopen());
/* 401 */             jSONObject.put("ismand", cptDetailFieldComInfo.getIsmand());
/* 402 */             jSONObject.put("isused", cptDetailFieldComInfo.getIsused());
/* 403 */             jSONObject.put("detailtable", cptDetailFieldComInfo.getDetailtable());
/* 404 */             jSONObject.put("otherpara", cptDetailFieldComInfo.getOtherPara());
/* 405 */             jSONObject.put("eleclazzname", HtmlUtil.getHtmlClassName(cptDetailFieldComInfo.getFieldhtmltype()));
/* 406 */           } catch (JSONException jSONException) {
/* 407 */             writeLog(jSONException);
/*     */           } 
/* 409 */           this.mandFieldMap.put(cptDetailFieldComInfo.getDsporder(), jSONObject);
/*     */         } 
/*     */       } 
/*     */     } 
/* 413 */     return this.mandFieldMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public TreeMap<String, JSONObject> getUsedFieldMap(String paramString) {
/* 418 */     if (this.detailTypeUsedFieldMap == null) {
/* 419 */       this.detailTypeUsedFieldMap = new HashMap<>();
/* 420 */       String str = "1crazydream";
/* 421 */       TreeMap<Object, Object> treeMap = new TreeMap<>();
/* 422 */       CptDetailFieldComInfo cptDetailFieldComInfo = new CptDetailFieldComInfo();
/* 423 */       cptDetailFieldComInfo.setTofirstRow();
/* 424 */       while (cptDetailFieldComInfo.next()) {
/* 425 */         JSONObject jSONObject = new JSONObject();
/*     */         try {
/* 427 */           jSONObject.put("id", cptDetailFieldComInfo.getFieldid());
/* 428 */           jSONObject.put("fieldname", cptDetailFieldComInfo.getFieldname());
/* 429 */           jSONObject.put("fielddbtype", cptDetailFieldComInfo.getFielddbtype());
/* 430 */           jSONObject.put("fieldhtmltype", cptDetailFieldComInfo.getFieldhtmltype());
/* 431 */           jSONObject.put("type", cptDetailFieldComInfo.getFieldType());
/* 432 */           jSONObject.put("imgwidth", cptDetailFieldComInfo.getImgWidth());
/* 433 */           jSONObject.put("imgheight", cptDetailFieldComInfo.getImgHeight());
/* 434 */           jSONObject.put("fieldlabel", cptDetailFieldComInfo.getLabel());
/* 435 */           jSONObject.put("viewtype", cptDetailFieldComInfo.getViewtype());
/* 436 */           jSONObject.put("fromuser", cptDetailFieldComInfo.getFromuser());
/* 437 */           jSONObject.put("textheight", cptDetailFieldComInfo.getTextheight());
/* 438 */           jSONObject.put("dsporder", cptDetailFieldComInfo.getDsporder());
/* 439 */           jSONObject.put("isopen", cptDetailFieldComInfo.getIsopen());
/* 440 */           jSONObject.put("ismand", cptDetailFieldComInfo.getIsmand());
/* 441 */           jSONObject.put("isused", cptDetailFieldComInfo.getIsused());
/* 442 */           jSONObject.put("detailtable", cptDetailFieldComInfo.getDetailtable());
/* 443 */           jSONObject.put("otherpara", cptDetailFieldComInfo.getOtherPara());
/* 444 */           jSONObject.put("eleclazzname", HtmlUtil.getHtmlClassName(cptDetailFieldComInfo.getFieldhtmltype()));
/* 445 */         } catch (JSONException jSONException) {
/* 446 */           writeLog(jSONException);
/*     */         } 
/* 448 */         if (!str.equals(cptDetailFieldComInfo.getDetailtable())) {
/* 449 */           this.detailTypeUsedFieldMap.put(str, treeMap);
/*     */           
/* 451 */           treeMap.clear();
/* 452 */           str = cptDetailFieldComInfo.getDetailtable();
/*     */         } 
/* 454 */         if ("1".equals(cptDetailFieldComInfo.getIsused())) {
/* 455 */           treeMap.put(cptDetailFieldComInfo.getDsporder(), jSONObject);
/*     */         }
/*     */       } 
/* 458 */       this.detailTypeUsedFieldMap.put(str, treeMap);
/*     */     } 
/* 460 */     return this.detailTypeUsedFieldMap.get(paramString);
/*     */   }
/*     */   public TreeMap<String, JSONObject> getOpenFieldMap(String paramString) {
/* 463 */     if (this.detailTypeOpenFieldMap == null) {
/* 464 */       this.detailTypeOpenFieldMap = new HashMap<>();
/* 465 */       String str = "1crazydream";
/* 466 */       TreeMap<Object, Object> treeMap = new TreeMap<>();
/* 467 */       CptDetailFieldComInfo cptDetailFieldComInfo = new CptDetailFieldComInfo();
/* 468 */       cptDetailFieldComInfo.setTofirstRow();
/* 469 */       while (cptDetailFieldComInfo.next()) {
/* 470 */         JSONObject jSONObject = new JSONObject();
/*     */         try {
/* 472 */           jSONObject.put("id", cptDetailFieldComInfo.getFieldid());
/* 473 */           jSONObject.put("fieldname", cptDetailFieldComInfo.getFieldname());
/* 474 */           jSONObject.put("fielddbtype", cptDetailFieldComInfo.getFielddbtype());
/* 475 */           jSONObject.put("fieldhtmltype", cptDetailFieldComInfo.getFieldhtmltype());
/* 476 */           jSONObject.put("type", cptDetailFieldComInfo.getFieldType());
/* 477 */           jSONObject.put("imgwidth", cptDetailFieldComInfo.getImgWidth());
/* 478 */           jSONObject.put("imgheight", cptDetailFieldComInfo.getImgHeight());
/* 479 */           jSONObject.put("fieldlabel", cptDetailFieldComInfo.getLabel());
/* 480 */           jSONObject.put("viewtype", cptDetailFieldComInfo.getViewtype());
/* 481 */           jSONObject.put("fromuser", cptDetailFieldComInfo.getFromuser());
/* 482 */           jSONObject.put("textheight", cptDetailFieldComInfo.getTextheight());
/* 483 */           jSONObject.put("dsporder", cptDetailFieldComInfo.getDsporder());
/* 484 */           jSONObject.put("isopen", cptDetailFieldComInfo.getIsopen());
/* 485 */           jSONObject.put("ismand", cptDetailFieldComInfo.getIsmand());
/* 486 */           jSONObject.put("isused", cptDetailFieldComInfo.getIsused());
/* 487 */           jSONObject.put("detailtable", cptDetailFieldComInfo.getDetailtable());
/* 488 */           jSONObject.put("otherpara", cptDetailFieldComInfo.getOtherPara());
/* 489 */           jSONObject.put("eleclazzname", HtmlUtil.getHtmlClassName(cptDetailFieldComInfo.getFieldhtmltype()));
/* 490 */         } catch (JSONException jSONException) {
/* 491 */           writeLog(jSONException);
/*     */         } 
/* 493 */         if (!str.equals(cptDetailFieldComInfo.getDetailtable())) {
/* 494 */           this.detailTypeOpenFieldMap.put(str, treeMap);
/*     */           
/* 496 */           treeMap.clear();
/* 497 */           str = cptDetailFieldComInfo.getDetailtable();
/*     */         } 
/* 499 */         if ("1".equals(cptDetailFieldComInfo.getIsopen())) {
/* 500 */           treeMap.put(cptDetailFieldComInfo.getDsporder(), jSONObject);
/*     */         }
/*     */       } 
/* 503 */       this.detailTypeOpenFieldMap.put(str, treeMap);
/*     */     } 
/* 505 */     return this.detailTypeOpenFieldMap.get(paramString);
/*     */   }
/*     */   public TreeMap<String, JSONObject> getMandFieldMap(String paramString) {
/* 508 */     if (this.detailTypeMandFieldMap == null) {
/* 509 */       this.detailTypeMandFieldMap = new HashMap<>();
/* 510 */       String str = "1crazydream";
/* 511 */       TreeMap<Object, Object> treeMap = new TreeMap<>();
/* 512 */       CptDetailFieldComInfo cptDetailFieldComInfo = new CptDetailFieldComInfo();
/* 513 */       cptDetailFieldComInfo.setTofirstRow();
/* 514 */       while (cptDetailFieldComInfo.next()) {
/* 515 */         JSONObject jSONObject = new JSONObject();
/*     */         try {
/* 517 */           jSONObject.put("id", cptDetailFieldComInfo.getFieldid());
/* 518 */           jSONObject.put("fieldname", cptDetailFieldComInfo.getFieldname());
/* 519 */           jSONObject.put("fielddbtype", cptDetailFieldComInfo.getFielddbtype());
/* 520 */           jSONObject.put("fieldhtmltype", cptDetailFieldComInfo.getFieldhtmltype());
/* 521 */           jSONObject.put("type", cptDetailFieldComInfo.getFieldType());
/* 522 */           jSONObject.put("imgwidth", cptDetailFieldComInfo.getImgWidth());
/* 523 */           jSONObject.put("imgheight", cptDetailFieldComInfo.getImgHeight());
/* 524 */           jSONObject.put("fieldlabel", cptDetailFieldComInfo.getLabel());
/* 525 */           jSONObject.put("viewtype", cptDetailFieldComInfo.getViewtype());
/* 526 */           jSONObject.put("fromuser", cptDetailFieldComInfo.getFromuser());
/* 527 */           jSONObject.put("textheight", cptDetailFieldComInfo.getTextheight());
/* 528 */           jSONObject.put("dsporder", cptDetailFieldComInfo.getDsporder());
/* 529 */           jSONObject.put("isopen", cptDetailFieldComInfo.getIsopen());
/* 530 */           jSONObject.put("ismand", cptDetailFieldComInfo.getIsmand());
/* 531 */           jSONObject.put("isused", cptDetailFieldComInfo.getIsused());
/* 532 */           jSONObject.put("detailtable", cptDetailFieldComInfo.getDetailtable());
/* 533 */           jSONObject.put("otherpara", cptDetailFieldComInfo.getOtherPara());
/* 534 */           jSONObject.put("eleclazzname", HtmlUtil.getHtmlClassName(cptDetailFieldComInfo.getFieldhtmltype()));
/* 535 */         } catch (JSONException jSONException) {
/* 536 */           writeLog(jSONException);
/*     */         } 
/* 538 */         if (!str.equals(cptDetailFieldComInfo.getDetailtable())) {
/* 539 */           this.detailTypeMandFieldMap.put(str, treeMap);
/*     */           
/* 541 */           treeMap.clear();
/* 542 */           str = cptDetailFieldComInfo.getDetailtable();
/*     */         } 
/* 544 */         if ("1".equals(cptDetailFieldComInfo.getIsmand())) {
/* 545 */           treeMap.put(cptDetailFieldComInfo.getDsporder(), jSONObject);
/*     */         }
/*     */       } 
/* 548 */       this.detailTypeMandFieldMap.put(str, treeMap);
/*     */     } 
/* 550 */     return this.detailTypeMandFieldMap.get(paramString);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/cpt/util/CptDetailFieldComInfo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */