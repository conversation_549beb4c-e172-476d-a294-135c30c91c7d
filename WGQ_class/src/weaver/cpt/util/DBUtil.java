/*     */ package weaver.cpt.util;
/*     */ 
/*     */ import java.sql.CallableStatement;
/*     */ import java.sql.PreparedStatement;
/*     */ import java.sql.SQLException;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Arrays;
/*     */ import java.util.List;
/*     */ import weaver.conn.ConnectionPool;
/*     */ import weaver.conn.WeaverConnection;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class DBUtil
/*     */   extends BaseBean
/*     */ {
/*     */   private WeaverConnection conn;
/*  28 */   private ConnectionPool pool = ConnectionPool.getInstance();
/*     */   
/*     */   private String dbType;
/*     */   
/*     */   private boolean getConnection(String paramString) {
/*  33 */     if (paramString != null) {
/*  34 */       this.conn = this.pool.getConnection(paramString);
/*     */     } else {
/*  36 */       this.conn = this.pool.getConnection();
/*     */     } 
/*     */     
/*  39 */     if (this.conn != null) {
/*  40 */       return true;
/*     */     }
/*  42 */     return false;
/*     */   }
/*     */ 
/*     */   
/*     */   public void executeSqlBatch(String paramString, List<String> paramList) {
/*  47 */     executeSqlBatch(paramString, paramList, null);
/*     */   }
/*     */   
/*     */   public void executeSqlBatch(String paramString1, List<String> paramList, String paramString2) {
/*  51 */     if (paramList == null || paramList.size() == 0 || !getConnection(paramString2)) {
/*     */       return;
/*     */     }
/*     */     
/*  55 */     this.dbType = this.conn.getDBType();
/*  56 */     PreparedStatement preparedStatement = null;
/*  57 */     List<String> list = null;
/*     */     try {
/*  59 */       this.conn.setAutoCommit(false);
/*  60 */       preparedStatement = this.conn.prepareStatement(paramString1);
/*  61 */       byte b = 0;
/*  62 */       for (String str : paramList) {
/*  63 */         list = Arrays.asList(str.split(Util.getSeparator() + ""));
/*     */         
/*  65 */         for (byte b1 = 0; b1 < list.size(); b1++) {
/*  66 */           preparedStatement.setString(b1 + 1, list.get(b1));
/*     */         }
/*  68 */         preparedStatement.addBatch();
/*  69 */         b++;
/*     */         
/*  71 */         if (b == 'ᎈ') {
/*  72 */           long l1 = System.currentTimeMillis();
/*  73 */           preparedStatement.executeBatch();
/*  74 */           this.conn.commit();
/*  75 */           preparedStatement.clearBatch();
/*  76 */           b = 0;
/*  77 */           long l2 = System.currentTimeMillis();
/*     */         } 
/*     */       } 
/*     */ 
/*     */       
/*  82 */       if (b != 0) {
/*  83 */         preparedStatement.executeBatch();
/*  84 */         this.conn.commit();
/*     */       } 
/*  86 */       this.conn.setAutoCommit(true);
/*  87 */     } catch (SQLException sQLException) {
/*  88 */       writeLog(">>>>执行过程出错");
/*  89 */       writeLog(sQLException);
/*     */       try {
/*  91 */         this.conn.rollback();
/*  92 */       } catch (SQLException sQLException1) {
/*  93 */         writeLog(">>>>rollback出错");
/*  94 */         writeLog(sQLException1);
/*     */       } 
/*     */     } finally {
/*     */       try {
/*  98 */         if (preparedStatement != null) {
/*  99 */           preparedStatement.close();
/*     */         }
/*     */       }
/* 102 */       catch (SQLException sQLException) {
/* 103 */         writeLog(">>>>关闭stmt出错");
/* 104 */         writeLog(sQLException);
/*     */       } 
/*     */       try {
/* 107 */         if (this.conn != null && !this.conn.isClosed()) {
/* 108 */           this.conn.close();
/*     */         }
/*     */       }
/* 111 */       catch (SQLException sQLException) {
/* 112 */         writeLog(">>>>关闭conn出错");
/* 113 */         writeLog(sQLException);
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void executeProcBatch(String paramString, List<String> paramList) {
/* 125 */     executeProcBatch(paramString, paramList, (String)null);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void executeProcBatch(String paramString1, List<String> paramList, String paramString2) {
/* 135 */     if (paramList == null || paramList.size() == 0 || !getConnection(paramString2)) {
/*     */       return;
/*     */     }
/*     */     
/* 139 */     String str = paramList.get(0);
/* 140 */     ArrayList<String> arrayList = new ArrayList();
/* 141 */     for (String str1 : str.split(Util.getSeparator() + "")) {
/* 142 */       arrayList.add(str1);
/*     */     }
/*     */     
/* 145 */     if (str.endsWith(Util.getSeparator() + "")) {
/* 146 */       arrayList.add("");
/*     */     }
/*     */     
/* 149 */     int i = arrayList.size();
/*     */     
/* 151 */     StringBuilder stringBuilder = new StringBuilder("{call " + paramString1 + "(");
/* 152 */     for (byte b = 0; b < i; b++) {
/* 153 */       stringBuilder.append("?,");
/*     */     }
/* 155 */     this.dbType = this.conn.getDBType();
/* 156 */     if ("sqlserver".equals(this.dbType)) {
/* 157 */       stringBuilder.append("?,");
/* 158 */       stringBuilder.append("?,");
/* 159 */     } else if (!"oracle".equals(this.dbType)) {
/*     */ 
/*     */ 
/*     */       
/* 163 */       if (!"db2".equals(this.dbType))
/*     */       {
/* 165 */         if (!"mysql".equals(this.dbType)) {
/*     */ 
/*     */           
/* 168 */           writeLog(">>>>dbType = " + this.dbType);
/*     */           return;
/*     */         }  } 
/*     */     } 
/* 172 */     stringBuilder.delete(stringBuilder.length() - 1, stringBuilder.length());
/* 173 */     stringBuilder.append(")}");
/*     */ 
/*     */ 
/*     */     
/* 177 */     CallableStatement callableStatement = null;
/*     */     try {
/* 179 */       callableStatement = this.conn.prepareCall(stringBuilder.toString());
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 186 */       this.conn.setAutoCommit(false);
/*     */       
/* 188 */       byte b1 = 0;
/* 189 */       for (String str1 : paramList) {
/* 190 */         arrayList = new ArrayList<>();
/* 191 */         for (String str2 : str1.split(Util.getSeparator() + "")) {
/* 192 */           arrayList.add(str2);
/*     */         }
/*     */         
/* 195 */         if (str1.endsWith(Util.getSeparator() + "")) {
/* 196 */           arrayList.add("");
/*     */         }
/* 198 */         if (arrayList.size() != i) {
/* 199 */           writeLog(">>>>errorList=" + arrayList);
/*     */         }
/*     */ 
/*     */         
/* 203 */         for (byte b2 = 0; b2 < i; b2++) {
/* 204 */           callableStatement.setString(b2 + 1, arrayList.get(b2));
/*     */         }
/*     */ 
/*     */         
/* 208 */         if ("sqlserver".equals(this.dbType)) {
/* 209 */           callableStatement.setString(i + 1, (String)null);
/* 210 */           callableStatement.setString(i + 2, (String)null);
/* 211 */         } else if (!"oracle".equals(this.dbType)) {
/*     */ 
/*     */ 
/*     */           
/* 215 */           if (!"db2".equals(this.dbType))
/*     */           {
/* 217 */             if (!"mysql".equals(this.dbType)) {
/*     */ 
/*     */               
/* 220 */               writeLog(">>>>dbType = " + this.dbType);
/*     */               return;
/*     */             }  } 
/*     */         } 
/* 224 */         callableStatement.addBatch();
/* 225 */         b1++;
/*     */         
/* 227 */         if (b1 == 'ᎈ') {
/* 228 */           long l1 = System.currentTimeMillis();
/* 229 */           callableStatement.executeBatch();
/* 230 */           this.conn.commit();
/* 231 */           callableStatement.clearBatch();
/* 232 */           b1 = 0;
/* 233 */           long l2 = System.currentTimeMillis();
/*     */         } 
/*     */       } 
/*     */ 
/*     */       
/* 238 */       callableStatement.executeBatch();
/* 239 */       this.conn.commit();
/* 240 */       callableStatement.clearBatch();
/* 241 */       this.conn.setAutoCommit(true);
/*     */     }
/* 243 */     catch (SQLException sQLException) {
/* 244 */       writeLog(">>>>执行过程出错");
/* 245 */       writeLog(sQLException);
/*     */       try {
/* 247 */         this.conn.rollback();
/* 248 */       } catch (SQLException sQLException1) {
/* 249 */         writeLog(">>>>rollback出错");
/* 250 */         writeLog(sQLException1);
/*     */       } 
/*     */     } finally {
/*     */       try {
/* 254 */         if (callableStatement != null) {
/* 255 */           callableStatement.close();
/*     */         }
/*     */       }
/* 258 */       catch (SQLException sQLException) {
/* 259 */         writeLog(">>>>关闭stmt出错");
/* 260 */         writeLog(sQLException);
/*     */       } 
/*     */       try {
/* 263 */         if (this.conn != null && !this.conn.isClosed()) {
/* 264 */           this.conn.close();
/*     */         }
/*     */       }
/* 267 */       catch (SQLException sQLException) {
/* 268 */         writeLog(">>>>关闭conn出错");
/* 269 */         writeLog(sQLException);
/*     */       } 
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/cpt/util/DBUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */