/*      */ package weaver.cpt.util;
/*      */ 
/*      */ import com.engine.common.service.impl.HrmCommonServiceImpl;
/*      */ import com.engine.cpt.util.CptRightShareUitl;
/*      */ import java.util.ArrayList;
/*      */ import java.util.HashSet;
/*      */ import net.sf.json.JSONObject;
/*      */ import weaver.conn.RecordSet;
/*      */ import weaver.general.BaseBean;
/*      */ import weaver.general.Util;
/*      */ import weaver.hrm.User;
/*      */ import weaver.hrm.companyvirtual.ResourceVirtualComInfo;
/*      */ import weaver.hrm.resource.ResourceComInfo;
/*      */ import weaver.proj.Maint.ProjectInfoComInfo;
/*      */ import weaver.proj.util.SQLUtil;
/*      */ import weaver.systeminfo.setting.HrmUserSettingComInfo;
/*      */ import weaver.systeminfo.systemright.CheckUserRight;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ public class CommonShareManager
/*      */ {
/*   31 */   private ResourceVirtualComInfo resourceVirtualComInfo = null;
/*   32 */   private HrmUserSettingComInfo hrmUserSettingComInfo = null;
/*      */   private ResourceComInfo rci;
/*   34 */   private ProjectInfoComInfo projectInfoComInfo = null;
/*   35 */   private String aliasTableName = "";
/*      */   
/*      */   public String getAliasTableName() {
/*   38 */     return this.aliasTableName;
/*      */   }
/*      */   
/*      */   public void setAliasTableName(String paramString) {
/*   42 */     this.aliasTableName = paramString;
/*      */   }
/*      */   
/*      */   public CommonShareManager() {
/*      */     try {
/*   47 */       this.resourceVirtualComInfo = new ResourceVirtualComInfo();
/*   48 */       this.hrmUserSettingComInfo = new HrmUserSettingComInfo();
/*   49 */       this.rci = new ResourceComInfo();
/*   50 */       this.projectInfoComInfo = new ProjectInfoComInfo();
/*   51 */     } catch (Exception exception) {
/*   52 */       (new BaseBean()).writeLog(exception.getMessage());
/*   53 */       exception.printStackTrace();
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getTableName(String paramString1, String paramString2) {
/*   63 */     String str = "";
/*   64 */     if (paramString1.equalsIgnoreCase("prj")) {
/*   65 */       if (paramString2.equalsIgnoreCase("inner")) { str = "Prj_ShareInfo"; }
/*   66 */       else { str = "Prj_ShareInfo"; } 
/*   67 */     } else if (paramString1.equalsIgnoreCase("prjtsk")) {
/*   68 */       if (paramString2.equalsIgnoreCase("inner")) { str = "Prj_TaskShareInfo"; }
/*   69 */       else { str = "Prj_TaskShareInfo"; } 
/*   70 */     } else if (paramString1.equalsIgnoreCase("cpt")) {
/*   71 */       if (paramString2.equalsIgnoreCase("inner")) { str = "CptCapitalShareInfo"; }
/*   72 */       else { str = "CptCapitalShareInfo"; }
/*      */     
/*   74 */     }  return str;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getShareWhereByUser(String paramString, User paramUser) {
/*   83 */     String str1 = "";
/*      */     
/*   85 */     int i = paramUser.getUID();
/*      */     
/*   87 */     int j = Util.getIntValue(paramUser.getLogintype());
/*   88 */     int k = Util.getIntValue(paramUser.getSeclevel());
/*   89 */     int m = paramUser.getUserDepartment();
/*   90 */     int n = paramUser.getUserSubCompany1();
/*   91 */     int i1 = paramUser.getType();
/*   92 */     String str2 = paramUser.getJobtitle();
/*   93 */     if ("".equals(str2)) str2 = "0"; 
/*   94 */     boolean bool = paramUser.isMergeFlag();
/*      */     
/*   96 */     str1 = getSqlWhere(paramString, "" + i, "" + j, "" + i1, "" + m, "" + n, "" + k, bool, str2);
/*      */ 
/*      */     
/*   99 */     return str1;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getTypeShareWhereByUser(String paramString, User paramUser) {
/*  108 */     int i = paramUser.getUID();
/*  109 */     int j = Util.getIntValue(paramUser.getLogintype());
/*  110 */     int k = Util.getIntValue(paramUser.getSeclevel());
/*  111 */     int m = paramUser.getUserDepartment();
/*  112 */     int n = paramUser.getUserSubCompany1();
/*  113 */     int i1 = paramUser.getType();
/*  114 */     String str = paramUser.getJobtitle();
/*  115 */     if ("".equals(str)) str = "0"; 
/*  116 */     boolean bool = paramUser.isMergeFlag();
/*      */     
/*  118 */     return getTypeSqlWhere(paramString, "" + i, "" + j, "" + i1, "" + m, "" + n, "" + k, bool, str);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getPrjShareWhereByUser(User paramUser) {
/*  129 */     RecordSet recordSet = new RecordSet();
/*  130 */     if (paramUser == null) {
/*  131 */       return " (1=2) ";
/*      */     }
/*  133 */     int i = paramUser.getUserDepartment();
/*  134 */     int j = paramUser.getUserSubCompany1();
/*      */     
/*  136 */     String str1 = " ( ";
/*      */ 
/*      */     
/*  139 */     str1 = str1 + " ( t1.manager=" + paramUser.getUID() + " ) ";
/*      */     
/*  141 */     str1 = str1 + " or exists (select 1 from Prj_Members t3 where t3.relateditemid=t1.id and t3.userid='" + paramUser.getUID() + "') ";
/*      */     
/*  143 */     String str2 = "( select id from hrmresource where managerstr like '%," + paramUser.getUID() + ",%' UNION ALL select " + paramUser.getUID() + ")";
/*  144 */     String str3 = "( select id from HrmResourcevirtual where managerstr like '%," + paramUser.getUID() + ",%' UNION ALL select " + paramUser.getUID() + ")";
/*  145 */     if ("oracle".equalsIgnoreCase(recordSet.getDBType())) {
/*  146 */       str2 = "( select id from hrmresource where managerstr like '%," + paramUser.getUID() + ",%' UNION ALL select " + paramUser.getUID() + " from dual)";
/*  147 */       str3 = "( select id from HrmResourcevirtual where managerstr like '%," + paramUser.getUID() + ",%' UNION ALL select " + paramUser.getUID() + " from dual)";
/*  148 */     } else if ("mysql".equalsIgnoreCase(recordSet.getDBType())) {
/*  149 */       str2 = "( select id from hrmresource where managerstr like '%," + paramUser.getUID() + ",%' UNION ALL select " + paramUser.getUID() + " from dual)";
/*  150 */       str3 = "( select id from HrmResourcevirtual where managerstr like '%," + paramUser.getUID() + ",%' UNION ALL select " + paramUser.getUID() + " from dual)";
/*      */     } 
/*  152 */     str1 = str1 + "  or exists ( select 1 from " + str2 + " t2 where t2.id =t1.manager ) ";
/*      */     
/*  154 */     str1 = str1 + "  or exists ( select 1 from " + str3 + " t2 where t2.id =t1.manager ) ";
/*      */     
/*  156 */     str1 = str1 + "  or t1.prjtype in  (select relateditemid from Prj_T_ShareInfo t2 where  (" + getTypeShareWhereByUser("prj", paramUser) + ") ) ";
/*      */     
/*  158 */     str1 = str1 + "  or exists ( select 1 from Prj_ShareInfo t2 where t2.relateditemid =t1.id and (" + getShareWhereByUser("prj", paramUser) + ") ) ";
/*      */     
/*  160 */     str1 = str1 + "  or exists ( select 1 from hrmrolemembers t3 where t3.roleid=9 and t3.resourceid=" + paramUser.getUID() + " and ( t3.rolelevel>=2 or ( t3.rolelevel=1 and t1.subcompanyid1=" + j + " ) or  ( t3.rolelevel=0 and t1.department=" + i + " ) )  ) ";
/*      */     
/*  162 */     str1 = str1 + " ) ";
/*      */ 
/*      */ 
/*      */     
/*  166 */     return str1;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getPrjShareWhereByUserCanEdit(User paramUser) {
/*  175 */     RecordSet recordSet = new RecordSet();
/*  176 */     if (paramUser == null) {
/*  177 */       return " (1=2) ";
/*      */     }
/*  179 */     int i = paramUser.getUserDepartment();
/*  180 */     int j = paramUser.getUserSubCompany1();
/*      */     
/*  182 */     String str1 = " ( ";
/*      */ 
/*      */     
/*  185 */     str1 = str1 + " ( t1.manager=" + paramUser.getUID() + " ) ";
/*      */     
/*  187 */     String str2 = "( select id from hrmresource where managerstr like '%," + paramUser.getUID() + ",%' UNION ALL select " + paramUser.getUID() + ")";
/*  188 */     String str3 = "( select id from HrmResourcevirtual where managerstr like '%," + paramUser.getUID() + ",%' UNION ALL select " + paramUser.getUID() + ")";
/*  189 */     if ("oracle".equalsIgnoreCase(recordSet.getDBType())) {
/*  190 */       str2 = "( select id from hrmresource where managerstr like '%," + paramUser.getUID() + ",%' UNION ALL select " + paramUser.getUID() + " from dual)";
/*  191 */       str3 = "( select id from HrmResourcevirtual where managerstr like '%," + paramUser.getUID() + ",%' UNION ALL select " + paramUser.getUID() + " from dual)";
/*  192 */     } else if ("mysql".equalsIgnoreCase(recordSet.getDBType())) {
/*  193 */       str2 = "( select id from hrmresource where managerstr like '%," + paramUser.getUID() + ",%' UNION ALL select " + paramUser.getUID() + " from dual)";
/*  194 */       str3 = "( select id from HrmResourcevirtual where managerstr like '%," + paramUser.getUID() + ",%' UNION ALL select " + paramUser.getUID() + " from dual)";
/*      */     } 
/*  196 */     str1 = str1 + "  or exists ( select 1 from " + str2 + " t2 where t2.id =t1.manager ) ";
/*      */     
/*  198 */     str1 = str1 + "  or exists ( select 1 from " + str3 + " t2 where t2.id =t1.manager ) ";
/*      */     
/*  200 */     str1 = str1 + "  or t1.prjtype in  (select relateditemid from Prj_T_ShareInfo t2 where t2.sharelevel=2 and (" + getTypeShareWhereByUser("prj", paramUser) + ") ) ";
/*      */     
/*  202 */     str1 = str1 + "  or exists ( select 1 from Prj_ShareInfo t2 where t2.relateditemid =t1.id and t2.sharelevel=2 and (" + getShareWhereByUser("prj", paramUser) + ") ) ";
/*      */     
/*  204 */     str1 = str1 + "  or exists ( select 1 from hrmrolemembers t3 where t3.roleid=9 and t3.resourceid=" + paramUser.getUID() + " and ( t3.rolelevel>=2 or ( t3.rolelevel=1 and t1.subcompanyid1=" + j + " ) or  ( t3.rolelevel=0 and t1.department=" + i + " ) )  ) ";
/*      */     
/*  206 */     str1 = str1 + " ) ";
/*  207 */     return str1;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getPrjTskShareWhereByUser(User paramUser, String paramString) {
/*  217 */     if (paramUser == null) {
/*  218 */       return " (1=2) ";
/*      */     }
/*  220 */     paramString = !"".equals(Util.null2String(paramString)) ? paramString : "tt1";
/*      */     
/*  222 */     String str = " ( ";
/*      */ 
/*      */     
/*  225 */     RecordSet recordSet = new RecordSet();
/*  226 */     if (recordSet.getDBType().equals("oracle")) {
/*  227 */       str = str + " ','||" + paramString + ".hrmid||',' like '%," + paramUser.getUID() + ",%' ";
/*  228 */     } else if (recordSet.getDBType().equals("mysql")) {
/*  229 */       str = str + " concat(','," + paramString + ".hrmid,',') like '%," + paramUser.getUID() + ",%' ";
/*      */     }
/*  231 */     else if (recordSet.getDBType().equals("postgresql")) {
/*  232 */       str = str + " ','||" + paramString + ".hrmid||',' like '%," + paramUser.getUID() + ",%' ";
/*      */     } else {
/*      */       
/*  235 */       str = str + " ','+" + paramString + ".hrmid+',' like '%," + paramUser.getUID() + ",%' ";
/*      */     } 
/*      */     
/*  238 */     str = str + "  or  ( " + paramString + ".prjid=t1.id and ( " + getPrjShareWhereByUser(paramUser) + " ) ) ";
/*      */     
/*  240 */     str = str + "  or exists ( select 1 from Prj_TaskShareInfo t2 where t2.relateditemid =" + paramString + ".id and (" + getShareWhereByUser("prjtsk", paramUser) + ") ) ";
/*      */     
/*  242 */     str = str + " ) ";
/*  243 */     return str;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getPrjTskShareWhereByUserCanEdit(User paramUser, String paramString) {
/*  253 */     if (paramUser == null) {
/*  254 */       return " (1=2) ";
/*      */     }
/*  256 */     paramString = !"".equals(Util.null2String(paramString)) ? paramString : "tt1";
/*  257 */     String str = " ( ";
/*      */     
/*  259 */     str = str + SQLUtil.filteSql((new RecordSet()).getDBType(), "','+" + paramString + ".hrmid+',' ") + " like '%," + paramUser.getUID() + ",%' ";
/*      */     
/*  261 */     str = str + "  or  ( " + paramString + ".prjid=t1.id and ( " + getPrjShareWhereByUserCanEdit(paramUser) + " ) ) ";
/*      */     
/*  263 */     str = str + "  or exists ( select 1 from Prj_TaskShareInfo t2 where t2.relateditemid =" + paramString + ".id and t2.sharelevel=2 and (" + getShareWhereByUser("prjtsk", paramUser) + ") ) ";
/*      */     
/*  265 */     str = str + " ) ";
/*  266 */     return str;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public HashSet<String> getPrjCanviewUsers(String paramString) {
/*  275 */     if ("".equals(Util.null2String(paramString))) {
/*  276 */       return new HashSet<>();
/*      */     }
/*  278 */     String str1 = this.projectInfoComInfo.getProjectInfomanager(paramString);
/*  279 */     HashSet<String> hashSet = new HashSet();
/*      */     
/*  281 */     RecordSet recordSet = new RecordSet();
/*  282 */     String str2 = "getparents(" + str1 + ")";
/*  283 */     String str3 = "getparents_v(" + str1 + ")";
/*  284 */     if ("oracle".equalsIgnoreCase(recordSet.getDBType()) && !"jc".equals(recordSet.getOrgindbtype())) {
/*  285 */       str2 = "table(getparents(" + str1 + "))";
/*  286 */       str3 = "table(getparents_v(" + str1 + "))";
/*  287 */     } else if ("mysql".equalsIgnoreCase(recordSet.getDBType()) || "jc".equals(recordSet.getOrgindbtype()) || "postgresql".equals(recordSet.getOrgindbtype())) {
/*  288 */       str2 = "(SELECT ID FROM HRMRESOURCE R,(select " + str2 + " f_id) t WHERE FIND_IN_SET(ID,f_id))";
/*  289 */       str3 = "(SELECT ID FROM HRMRESOURCE R,(select " + str3 + " f_id) t WHERE FIND_IN_SET(ID,f_id))";
/*      */     } 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  305 */     String str4 = " select t2.userid from Prj_ProjectInfo t1,Prj_Members t2 where t2.relateditemid=t1.id and t1.id=" + paramString + " union  select t2.id from " + str2 + " t2  union  select t2.id from " + str3 + " t2  union  select distinct t1.id from HrmResource t1 ,  Prj_ShareInfo  t2 where  t1.id <> 0 and t2.relateditemid = " + paramString + " and ( (t2.foralluser=1 and t2.seclevel<=t1.seclevel)  or ( t2.userid= t1.id ) or (t2.departmentid=t1.departmentid and t2.seclevel<=t1.seclevel) or (t2.subcompanyid=t1.subcompanyid1 and t2.seclevel<=t1.seclevel) )  union  select resourceid from hrmrolemembers  t1, hrmresource  t2 where roleid=9 and t1.resourceid=t2.id and (rolelevel=2 or (rolelevel=0 and t2.departmentid=" + this.rci.getDepartmentID(str1) + " ) or (t1.rolelevel=1 and t2.subcompanyid1=" + this.rci.getSubCompanyID(str1) + "  )) ";
/*      */     
/*  307 */     recordSet.executeSql(str4);
/*      */ 
/*      */ 
/*      */     
/*  311 */     while (recordSet.next()) {
/*  312 */       hashSet.add(recordSet.getString(1));
/*      */     }
/*      */     
/*  315 */     return hashSet;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getPrjPermissionType(String paramString, User paramUser) {
/*  324 */     if ("".equals(Util.null2String(paramString)) || paramUser == null) {
/*  325 */       return "0";
/*      */     }
/*  327 */     String str1 = this.projectInfoComInfo.getProjectInfomanager(paramString);
/*  328 */     String str2 = this.projectInfoComInfo.getProjectInfoprjtype(paramString);
/*      */     
/*  330 */     if (paramUser.getUID() == Util.getIntValue(str1)) {
/*  331 */       return "2.5";
/*      */     }
/*  333 */     RecordSet recordSet = new RecordSet();
/*      */     
/*  335 */     String str3 = "( select id from hrmresource where managerstr like '%," + paramUser.getUID() + ",%' UNION ALL select " + paramUser.getUID() + ")";
/*  336 */     String str4 = "( select id from HrmResourcevirtual where managerstr like '%," + paramUser.getUID() + ",%' UNION ALL select " + paramUser.getUID() + ")";
/*  337 */     if ("oracle".equalsIgnoreCase(recordSet.getDBType())) {
/*  338 */       str3 = "( select id from hrmresource where managerstr like '%," + paramUser.getUID() + ",%' UNION ALL select " + paramUser.getUID() + " from dual)";
/*  339 */       str4 = "( select id from HrmResourcevirtual where managerstr like '%," + paramUser.getUID() + ",%' UNION ALL select " + paramUser.getUID() + " from dual)";
/*  340 */     } else if ("mysql".equalsIgnoreCase(recordSet.getDBType())) {
/*  341 */       str3 = "( select id from hrmresource where managerstr like '%," + paramUser.getUID() + ",%' UNION ALL select " + paramUser.getUID() + " from dual)";
/*  342 */       str4 = "( select id from HrmResourcevirtual where managerstr like '%," + paramUser.getUID() + ",%' UNION ALL select " + paramUser.getUID() + " from dual)";
/*      */     } 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  364 */     String str5 = "select max(ptype) from ( select 0.5 as ptype from Prj_ProjectInfo t1,Prj_Members t2 where t2.relateditemid=t1.id and t1.id=" + paramString + " and t2.userid=" + paramUser.getUID() + " union  select 3 from " + str3 + " t2 where t2.id='" + str1 + "' union  select 3 from " + str4 + " t2 where t2.id='" + str1 + "' union " + getTypeSharLevel("prj", str2, paramUser).replace("max(sharelevel)", "max(sharelevel) as ptype") + " union " + getSharLevel("prj", paramString, paramUser).replace("max(sharelevel)", "max(sharelevel) as ptype") + " union  select 4 as ptype from hrmrolemembers  t1, hrmresource  t2 where t2.id=" + paramUser.getUID() + " and roleid=9 and t1.resourceid=t2.id and (rolelevel=2 or (rolelevel=0 and t2.departmentid=" + this.rci.getDepartmentID(str1) + " ) or (t1.rolelevel=1 and t2.subcompanyid1=" + this.rci.getSubCompanyID(str1) + "  ))  union  select 4 as ptype from hrmrolemembers  t1, hrmresourcemanager  t2 where t2.id=" + paramUser.getUID() + " and roleid=9 and t1.resourceid=t2.id   ) pt ";
/*      */ 
/*      */ 
/*      */     
/*  368 */     recordSet.execute(str5);
/*  369 */     if (recordSet.next()) {
/*  370 */       return recordSet.getString(1);
/*      */     }
/*  372 */     return "0";
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getPrjTskPermissionType(String paramString, User paramUser) {
/*  383 */     if ("".equals(Util.null2String(paramString)) || paramUser == null) {
/*  384 */       return "0.0";
/*      */     }
/*  386 */     RecordSet recordSet = new RecordSet();
/*  387 */     String str1 = "select t1.*,t2.subject as parentname from Prj_TaskProcess t1 left outer join Prj_TaskProcess t2 on t1.parentid=t2.id where t1.id=" + paramString;
/*  388 */     recordSet.execute(str1);
/*      */     
/*  390 */     String str2 = "";
/*  391 */     String str3 = "";
/*  392 */     if (recordSet.next()) {
/*  393 */       str2 = Util.null2String(recordSet.getString("hrmid"));
/*  394 */       str3 = Util.null2String(recordSet.getString("prjid"));
/*      */     } 
/*  396 */     String str4 = this.projectInfoComInfo.getProjectInfomanager(str3);
/*  397 */     String str5 = this.projectInfoComInfo.getProjectInfoprjtype(str3);
/*      */     
/*  399 */     if (("," + str2 + ",").indexOf("," + paramUser.getUID() + ",") > -1)
/*  400 */       return "5"; 
/*  401 */     if (paramUser.getUID() == Util.getIntValue(str4)) {
/*  402 */       return "2.5";
/*      */     }
/*      */ 
/*      */     
/*  406 */     String str6 = "( select id from hrmresource where managerstr like '%," + paramUser.getUID() + ",%' UNION ALL select " + paramUser.getUID() + ")";
/*  407 */     String str7 = "( select id from HrmResourcevirtual where managerstr like '%," + paramUser.getUID() + ",%' UNION ALL select " + paramUser.getUID() + ")";
/*  408 */     if ("oracle".equalsIgnoreCase(recordSet.getDBType())) {
/*  409 */       str6 = "( select id from hrmresource where managerstr like '%," + paramUser.getUID() + ",%' UNION ALL select " + paramUser.getUID() + " from dual)";
/*  410 */       str7 = "( select id from HrmResourcevirtual where managerstr like '%," + paramUser.getUID() + ",%' UNION ALL select " + paramUser.getUID() + " from dual)";
/*  411 */     } else if ("mysql".equalsIgnoreCase(recordSet.getDBType())) {
/*  412 */       str6 = "( select id from hrmresource where managerstr like '%," + paramUser.getUID() + ",%' UNION ALL select " + paramUser.getUID() + " from dual)";
/*  413 */       str7 = "( select id from HrmResourcevirtual where managerstr like '%," + paramUser.getUID() + ",%' UNION ALL select " + paramUser.getUID() + " from dual)";
/*      */     } 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  438 */     str1 = "select max(ptype) from ( select 0.5 as ptype from Prj_ProjectInfo t1,Prj_Members t2 where t2.relateditemid=t1.id and t1.id=" + str3 + " and t2.userid=" + paramUser.getUID() + " union  select 2.1 from " + str6 + " t2 where t2.id='" + str4 + "' union  select 2.1 from " + str7 + " t2 where t2.id='" + str4 + "' union " + getTypeSharLevel("prj", str5, paramUser).replace("max(sharelevel)", "max(sharelevel) as ptype") + " union " + getSharLevel("prj", str3, paramUser).replace("max(sharelevel)", "max(sharelevel) as ptype") + " union  select 2.2 as ptype from hrmrolemembers  t1, hrmresource  t2 where t2.id=" + paramUser.getUID() + " and roleid=9 and t1.resourceid=t2.id and (rolelevel=2 or (rolelevel=0 and t2.departmentid=" + this.rci.getDepartmentID(str4) + " ) or (t1.rolelevel=1 and t2.subcompanyid1=" + this.rci.getSubCompanyID(str4) + "  ))  union  select 2.2 as ptype from hrmrolemembers  t1, hrmresourcemanager  t2 where t2.id=" + paramUser.getUID() + " and roleid=9 and t1.resourceid=t2.id   union  select ( case ptype1 when 2 then 2.3 when 1 then 0.8 else 0.0 end ) as ptype from  (" + getSharLevel("prjtsk", paramString, paramUser).replace("max(sharelevel)", "max(sharelevel) as ptype1  ") + ") tshare  ) pt ";
/*      */ 
/*      */     
/*  441 */     recordSet.execute(str1);
/*  442 */     if (recordSet.next()) {
/*  443 */       return recordSet.getString(1);
/*      */     }
/*  445 */     return "0";
/*      */   }
/*      */ 
/*      */   
/*      */   public String getSharLevel(String paramString1, String paramString2, User paramUser) {
/*  450 */     String str1 = "" + paramUser.getUID();
/*  451 */     String str2 = "" + paramUser.getLogintype();
/*  452 */     String str3 = "1";
/*  453 */     String str4 = "" + paramUser.getUserDepartment();
/*  454 */     String str5 = "" + paramUser.getUserSubCompany1();
/*  455 */     String str6 = "" + paramUser.getSeclevel();
/*  456 */     String str7 = paramUser.getJobtitle();
/*  457 */     if ("".equals(str7)) str7 = "0"; 
/*  458 */     boolean bool = paramUser.isMergeFlag();
/*  459 */     String str8 = "";
/*      */ 
/*      */     
/*  462 */     if ("cpt".equalsIgnoreCase(paramString1)) {
/*  463 */       String str9 = CptRightShareUitl.getTopCptgroupid(paramString2);
/*  464 */       str8 = getSharLevel(paramString1, str1, str2, str3, str4, str5, str6, paramString2, bool, str7).replace("max(sharelevel)", "max(sharelevel) as sharelevel");
/*  465 */       String str10 = getTypeSharLevel("cpt", str9, paramUser).replace("max(sharelevel)", "max(sharelevel) as sharelevel");
/*  466 */       str8 = "select max(sharelevel) from (" + str8 + " union " + str10 + ")t";
/*      */     } else {
/*  468 */       str8 = getSharLevel(paramString1, str1, str2, str3, str4, str5, str6, paramString2, bool, str7);
/*      */     } 
/*  470 */     return str8;
/*      */   }
/*      */ 
/*      */   
/*      */   public String getSharLevel(String paramString1, String paramString2, String paramString3, String paramString4, String paramString5, String paramString6, String paramString7, String paramString8, boolean paramBoolean, String paramString9) {
/*  475 */     String str1 = "";
/*  476 */     String str2 = "";
/*  477 */     if (paramString3.equals("1")) { str2 = "inner"; } else { str2 = "outer"; }
/*  478 */      if ("prj".equalsIgnoreCase(paramString1) || "prjtsk".equalsIgnoreCase(paramString1) || "cpt".equalsIgnoreCase(paramString1)) {
/*  479 */       String str = getTableName(paramString1, str2);
/*  480 */       str1 = "select max(sharelevel) from " + str + " t2 where  t2.relateditemid=" + paramString8 + " and " + getSqlWhere(paramString1, paramString2, paramString3, paramString4, paramString5, paramString6, paramString7, paramBoolean, paramString9) + "  ";
/*      */     } 
/*  482 */     return str1;
/*      */   }
/*      */   
/*      */   public String getTypeSharLevel(String paramString1, String paramString2, User paramUser) {
/*  486 */     String str1 = "" + paramUser.getUID();
/*  487 */     String str2 = "" + paramUser.getLogintype();
/*  488 */     String str3 = "1";
/*  489 */     String str4 = "" + paramUser.getUserDepartment();
/*  490 */     String str5 = "" + paramUser.getUserSubCompany1();
/*  491 */     String str6 = "" + paramUser.getSeclevel();
/*  492 */     String str7 = paramUser.getJobtitle();
/*  493 */     if ("".equals(str7)) str7 = "0"; 
/*  494 */     boolean bool = paramUser.isMergeFlag();
/*  495 */     return getTypeSharLevel(paramString1, str1, str2, str3, str4, str5, str6, paramString2, bool, str7);
/*      */   }
/*      */   
/*      */   public String getTypeSharLevel(String paramString1, String paramString2, String paramString3, String paramString4, String paramString5, String paramString6, String paramString7, String paramString8, boolean paramBoolean, String paramString9) {
/*  499 */     String str = "";
/*  500 */     if ("cpt".equalsIgnoreCase(paramString1)) {
/*  501 */       String str1 = "CptAssortmentShare";
/*  502 */       str = "select max(sharelevel) from " + str1 + " t2 where  t2.assortmentid=" + paramString8 + " and (" + getTypeSqlWhere(paramString1, paramString2, paramString3, paramString4, paramString5, paramString6, paramString7, paramBoolean, paramString9) + ") ";
/*      */     } else {
/*  504 */       String str1 = "prj_t_shareinfo";
/*  505 */       str = "select max(sharelevel) from " + str1 + " t2 where  t2.relateditemid=" + paramString8 + " and (" + getTypeSqlWhere(paramString1, paramString2, paramString3, paramString4, paramString5, paramString6, paramString7, paramBoolean, paramString9) + ") ";
/*      */     } 
/*  507 */     return str;
/*      */   }
/*      */ 
/*      */   
/*      */   public String getSqlWhere(String paramString1, String paramString2, String paramString3, String paramString4, String paramString5, String paramString6, String paramString7, boolean paramBoolean, String paramString8) {
/*  512 */     JSONObject jSONObject = getVirtualIds(paramString2);
/*  513 */     boolean bool = !"".equals(this.aliasTableName) ? true : false;
/*  514 */     String str1 = bool ? (this.aliasTableName + ".") : "t2.";
/*  515 */     String str2 = "";
/*  516 */     RecordSet recordSet = new RecordSet();
/*      */     
/*  518 */     HrmCommonServiceImpl hrmCommonServiceImpl = new HrmCommonServiceImpl();
/*  519 */     String str3 = hrmCommonServiceImpl.getCptRoleMembersSql(str1 + "roleid", str1 + "rolelevel");
/*      */     
/*  521 */     if ("prj".equalsIgnoreCase(paramString1) || "prjtsk".equalsIgnoreCase(paramString1)) {
/*  522 */       str2 = "(";
/*  523 */       if (paramString3.equals("1")) {
/*  524 */         str2 = str2 + "(" + str1 + "sharetype=1 and " + str1 + "userid=" + paramString2 + ") or ";
/*  525 */         str2 = str2 + "(" + str1 + "sharetype=6 and " + str1 + "userid=" + paramString2 + ") or ";
/*  526 */         str2 = str2 + "(" + str1 + "sharetype=5 and " + str1 + "subcompanyid=" + paramString6 + " and " + str1 + "seclevel<=" + paramString7 + " and seclevelmax>=" + paramString7 + " )  or (" + str1 + "sharetype=2 and " + str1 + "departmentid=" + paramString5 + " and " + str1 + "seclevel<=" + paramString7 + " and seclevelmax>=" + paramString7 + " ) or ";
/*      */         
/*  528 */         str2 = str2 + " (" + str1 + "sharetype=3 and " + str1 + "seclevel<=" + paramString7 + " and " + str1 + "seclevelmax>=" + paramString7 + " and " + paramString2 + " in (" + str3 + ") ) or ";
/*  529 */         str2 = str2 + " (" + str1 + "sharetype=4 and " + str1 + "foralluser=1 and " + str1 + "seclevel<=" + paramString7 + " and seclevelmax>=" + paramString7 + " ) ";
/*      */         
/*  531 */         String str = "";
/*  532 */         if (recordSet.getDBType().equals("oracle") || recordSet.getDBType().equals("postgresql")) {
/*  533 */           str = str + " ( " + str1 + "joblevel=1 AND ','||" + str1 + "scopeid||',' like '%," + paramString5 + ",%') or (" + str1 + "joblevel=2 AND ','||" + str1 + "scopeid||',' like '%," + paramString6 + ",%') ";
/*      */         }
/*  535 */         else if (recordSet.getDBType().equals("mysql")) {
/*  536 */           str = str + " ( " + str1 + "joblevel=1 AND concat(','," + str1 + "scopeid,',') like '%," + paramString5 + ",%') or (" + str1 + "joblevel=2 AND concat(','," + str1 + "scopeid,',') like '%," + paramString6 + ",%') ";
/*      */         } else {
/*      */           
/*  539 */           str = str + " ( " + str1 + "joblevel=1 AND ','+" + str1 + "scopeid+',' like '%," + paramString5 + ",%') or (" + str1 + "joblevel=2 AND ','+" + str1 + "scopeid+',' like '%," + paramString6 + ",%') ";
/*      */         } 
/*      */ 
/*      */ 
/*      */         
/*  544 */         if (jSONObject.containsKey("subids")) {
/*  545 */           String[] arrayOfString = Util.TokenizerString2(jSONObject.getString("subids"), ",");
/*  546 */           for (byte b = 0; b < arrayOfString.length; b++) {
/*  547 */             if (!"".equals(arrayOfString[b])) {
/*  548 */               if (recordSet.getDBType().equals("oracle") || recordSet.getDBType().equals("postgresql")) {
/*  549 */                 str = str + " or (" + str1 + "joblevel=2 AND ','||" + str1 + "scopeid||',' like '%," + arrayOfString[b] + ",%')";
/*  550 */               } else if (recordSet.getDBType().equals("mysql")) {
/*  551 */                 str = str + " or (" + str1 + "joblevel=2 AND concat(','," + str1 + "scopeid,',') like '%," + arrayOfString[b] + ",%')";
/*      */               } else {
/*  553 */                 str = str + " or (" + str1 + "joblevel=2 AND ','+" + str1 + "scopeid+',' like '%," + arrayOfString[b] + ",%')";
/*      */               } 
/*      */             }
/*      */           } 
/*      */         } 
/*  558 */         if (jSONObject.containsKey("deptids")) {
/*  559 */           String[] arrayOfString = Util.TokenizerString2(jSONObject.getString("deptids"), ",");
/*  560 */           for (byte b = 0; b < arrayOfString.length; b++) {
/*  561 */             if (!"".equals(arrayOfString[b])) {
/*  562 */               if (recordSet.getDBType().equals("oracle") || recordSet.getDBType().equals("postgresql")) {
/*  563 */                 str = str + " or (" + str1 + "joblevel=1 AND ','||" + str1 + "scopeid||',' like '%," + arrayOfString[b] + ",%')";
/*  564 */               } else if (recordSet.getDBType().equals("mysql")) {
/*  565 */                 str = str + " or (" + str1 + "joblevel=1 AND concat(','," + str1 + "scopeid,',') like '%," + arrayOfString[b] + ",%')";
/*      */               } else {
/*  567 */                 str = str + " or (" + str1 + "joblevel=1 AND ','+" + str1 + "scopeid+',' like '%," + arrayOfString[b] + ",%')";
/*      */               } 
/*      */             }
/*      */           } 
/*      */         } 
/*      */         
/*  573 */         str2 = str2 + " or (" + str1 + "sharetype=11 and " + str1 + "jobtitleid=" + paramString8 + " and (" + str1 + "joblevel=0 OR " + str + ") ) ";
/*      */         
/*  575 */         if (jSONObject.containsKey("subids")) {
/*  576 */           str2 = str2 + " or (" + str1 + "sharetype=5 and " + str1 + "subcompanyid in (" + jSONObject.getString("subids") + ") and " + str1 + "seclevel<=" + paramString7 + " and seclevelmax>=" + paramString7 + ")  ";
/*      */         }
/*  578 */         if (jSONObject.containsKey("deptids")) {
/*  579 */           str2 = str2 + " or (" + str1 + "sharetype=2 and " + str1 + "departmentid in (" + jSONObject.getString("deptids") + ") and " + str1 + "seclevel<=" + paramString7 + " and seclevelmax>=" + paramString7 + ")  ";
/*      */         }
/*      */       } else {
/*      */         
/*  583 */         str2 = str2 + "(" + str1 + "sharetype=9 and " + str1 + "crmid=" + paramString2 + ") or\t(" + str1 + "sharetype=10 and " + str1 + "crmid=" + paramString4 + " and " + str1 + "seclevel<=" + paramString7 + " and seclevelmax>=" + paramString7 + " )";
/*      */       } 
/*      */       
/*  586 */       str2 = str2 + ")";
/*  587 */     } else if ("cpt".equalsIgnoreCase(paramString1)) {
/*  588 */       str2 = "(";
/*  589 */       if (paramString3.equals("1")) {
/*  590 */         boolean bool1 = isShowsub(paramString2);
/*  591 */         ArrayList arrayList = new ArrayList();
/*  592 */         int i = Util.getIntValue(paramString7);
/*  593 */         if (bool1 && paramBoolean) {
/*  594 */           arrayList = (ArrayList)User.getBelongtoUsersByUserId(paramString2);
/*      */         }
/*  596 */         str2 = str2 + "(" + str1 + "sharetype=1 and " + str1 + "userid in (" + paramString2 + ((bool1 && paramBoolean) ? ("," + User.getBelongtoidsByUserId(paramString2)) : "") + ") ) or ";
/*  597 */         str2 = str2 + "(" + str1 + "sharetype=6 and " + str1 + "userid in (" + paramString2 + ((bool1 && paramBoolean) ? ("," + User.getBelongtoidsByUserId(paramString2)) : "") + ") ) or ";
/*  598 */         str2 = str2 + "(" + str1 + "sharetype=7 and " + str1 + "userid in (" + paramString2 + ((bool1 && paramBoolean) ? ("," + User.getBelongtoidsByUserId(paramString2)) : "") + ") ) or ";
/*  599 */         str2 = str2 + "(" + str1 + "sharetype=8 and " + str1 + "userid in (" + paramString2 + ((bool1 && paramBoolean) ? ("," + User.getBelongtoidsByUserId(paramString2)) : "") + ") ) or ";
/*  600 */         str2 = str2 + "(" + str1 + "sharetype=5 and (" + str1 + "subcompanyid=" + paramString6 + " and " + str1 + "seclevel<=" + paramString7 + " and seclevelmax>=" + paramString7 + ") ) or";
/*      */         
/*  602 */         String str = "";
/*  603 */         if (recordSet.getDBType().equals("oracle") || recordSet.getDBType().equals("postgresql")) {
/*  604 */           str = str + " ( " + str1 + "joblevel=1 AND ','||" + str1 + "scopeid||',' like '%," + paramString5 + ",%') or (" + str1 + "joblevel=2 AND ','||" + str1 + "scopeid||',' like '%," + paramString6 + ",%') ";
/*      */         }
/*  606 */         else if (recordSet.getDBType().equals("mysql")) {
/*  607 */           str = str + " ( " + str1 + "joblevel=1 AND concat(','," + str1 + "scopeid,',') like '%," + paramString5 + ",%') or (" + str1 + "joblevel=2 AND concat(','," + str1 + "scopeid,',') like '%," + paramString6 + ",%') ";
/*      */         } else {
/*      */           
/*  610 */           str = str + " ( " + str1 + "joblevel=1 AND ','+" + str1 + "scopeid+',' like '%," + paramString5 + ",%') or (" + str1 + "joblevel=2 AND ','+" + str1 + "scopeid+',' like '%," + paramString6 + ",%') ";
/*      */         } 
/*      */ 
/*      */         
/*  614 */         if (jSONObject.containsKey("subids")) {
/*  615 */           String[] arrayOfString = Util.TokenizerString2(jSONObject.getString("subids"), ",");
/*  616 */           for (byte b = 0; b < arrayOfString.length; b++) {
/*  617 */             if (!"".equals(arrayOfString[b])) {
/*  618 */               if (recordSet.getDBType().equals("oracle") || recordSet.getDBType().equals("postgresql")) {
/*  619 */                 str = str + " or (" + str1 + "joblevel=2 AND ','||" + str1 + "scopeid||',' like '%," + arrayOfString[b] + ",%')";
/*  620 */               } else if (recordSet.getDBType().equals("mysql")) {
/*  621 */                 str = str + " or (" + str1 + "joblevel=2 AND concat(','," + str1 + "scopeid,',') like '%," + arrayOfString[b] + ",%')";
/*      */               } else {
/*  623 */                 str = str + " or (" + str1 + "joblevel=2 AND ','+" + str1 + "scopeid+',' like '%," + arrayOfString[b] + ",%')";
/*      */               } 
/*      */             }
/*      */           } 
/*      */         } 
/*  628 */         if (jSONObject.containsKey("deptids")) {
/*  629 */           String[] arrayOfString = Util.TokenizerString2(jSONObject.getString("deptids"), ",");
/*  630 */           for (byte b = 0; b < arrayOfString.length; b++) {
/*  631 */             if (!"".equals(arrayOfString[b])) {
/*  632 */               if (recordSet.getDBType().equals("oracle") || recordSet.getDBType().equals("postgresql")) {
/*  633 */                 str = str + " or (" + str1 + "joblevel=1 AND ','||" + str1 + "scopeid||',' like '%," + arrayOfString[b] + ",%')";
/*  634 */               } else if (recordSet.getDBType().equals("mysql")) {
/*  635 */                 str = str + " or (" + str1 + "joblevel=1 AND concat(','," + str1 + "scopeid,',') like '%," + arrayOfString[b] + ",%')";
/*      */               } else {
/*  637 */                 str = str + " or (" + str1 + "joblevel=1 AND ','+" + str1 + "scopeid+',' like '%," + arrayOfString[b] + ",%')";
/*      */               } 
/*      */             }
/*      */           } 
/*      */         } 
/*  642 */         str2 = str2 + " (" + str1 + "sharetype=11 and " + str1 + "jobtitleid=" + paramString8 + " and (" + str1 + "joblevel=0 OR " + str + ") ) ";
/*      */         
/*  644 */         if (bool1 && paramBoolean) {
/*  645 */           for (User user : arrayList) {
/*  646 */             str2 = str2 + " or (" + str1 + "subcompanyid=" + user.getUserSubCompany1() + " and " + str1 + "seclevel<=" + user.getSeclevel() + " and seclevelmax>=" + user.getSeclevel() + ") ";
/*  647 */             if (Util.getIntValue(user.getSeclevel(), 0) < i) {
/*  648 */               i = Util.getIntValue(user.getSeclevel(), 0);
/*      */             }
/*      */           } 
/*      */         }
/*  652 */         str2 = str2 + "  or (" + str1 + "sharetype=2 and ((" + str1 + "departmentid=" + paramString5 + " and " + str1 + "seclevel<=" + paramString7 + " and " + str1 + "seclevelmax>=" + paramString7 + ")";
/*  653 */         if (bool1 && paramBoolean) {
/*  654 */           for (User user : arrayList) {
/*  655 */             str2 = str2 + " or (" + str1 + "departmentid=" + user.getUserDepartment() + " and " + str1 + "seclevel<=" + user.getSeclevel() + " and seclevelmax>=" + user.getSeclevel() + ") ";
/*      */           }
/*      */         }
/*  658 */         str2 = str2 + " )) or ";
/*  659 */         str2 = str2 + " (" + str1 + "sharetype=3 and (( " + str1 + "seclevel<=" + paramString7 + " and " + str1 + "seclevelmax>=" + paramString7 + " and " + paramString2 + " in (" + str3 + ")) ";
/*  660 */         if (bool1 && paramBoolean) {
/*  661 */           for (User user : arrayList) {
/*  662 */             str2 = str2 + " or ( " + str1 + "seclevel<=" + user.getSeclevel() + " and " + str1 + "seclevelmax>=" + user.getSeclevel() + " and " + user.getUID() + " in (" + str3 + ")) ";
/*      */           }
/*      */         }
/*  665 */         str2 = str2 + " )) or ";
/*  666 */         str2 = str2 + " (" + str1 + "sharetype=4 and " + str1 + "foralluser=1 and " + str1 + "seclevel<=" + (bool1 ? ("" + i) : paramString7) + " and seclevelmax>=" + (bool1 ? ("" + i) : paramString7) + ") ";
/*      */ 
/*      */         
/*  669 */         if (jSONObject.containsKey("subids")) {
/*  670 */           str2 = str2 + " or (" + str1 + "sharetype=5 and " + str1 + "subcompanyid in (" + jSONObject.getString("subids") + ") and " + str1 + "seclevel<=" + paramString7 + " and seclevelmax>=" + paramString7 + ")  ";
/*      */         }
/*  672 */         if (jSONObject.containsKey("deptids")) {
/*  673 */           str2 = str2 + " or (" + str1 + "sharetype=2 and " + str1 + "departmentid in (" + jSONObject.getString("deptids") + ") and " + str1 + "seclevel<=" + paramString7 + " and seclevelmax>=" + paramString7 + ")  ";
/*      */         }
/*      */         
/*  676 */         if (bool1 && paramBoolean) {
/*  677 */           for (User user : arrayList) {
/*  678 */             JSONObject jSONObject1 = getVirtualIds("" + user.getUID());
/*  679 */             if (jSONObject1.containsKey("subids")) {
/*  680 */               str2 = str2 + " or (" + str1 + "sharetype=5 and " + str1 + "subcompanyid in (" + jSONObject1.getString("subids") + ") and " + str1 + "seclevel<=" + user.getSeclevel() + " and seclevelmax>=" + user.getSeclevel() + ")  ";
/*      */             }
/*  682 */             if (jSONObject1.containsKey("deptids")) {
/*  683 */               str2 = str2 + " or (" + str1 + "sharetype=2 and " + str1 + "departmentid in (" + jSONObject1.getString("deptids") + ") and " + str1 + "seclevel<=" + user.getSeclevel() + " and seclevelmax>=" + user.getSeclevel() + ")  ";
/*      */             }
/*      */           } 
/*      */         }
/*      */       } else {
/*      */         
/*  689 */         str2 = str2 + "(" + str1 + "sharetype=9 and " + str1 + "crmid=" + paramString2 + ") or\t(" + str1 + "sharetype=10 and " + str1 + "crmid=" + paramString4 + " and " + str1 + "seclevel<=" + paramString7 + " and seclevelmax>=" + paramString7 + ")";
/*      */       } 
/*      */       
/*  692 */       str2 = str2 + ")";
/*      */     } 
/*      */ 
/*      */     
/*  696 */     return str2;
/*      */   }
/*      */ 
/*      */   
/*      */   public String getTypeSqlWhere(String paramString1, String paramString2, String paramString3, String paramString4, String paramString5, String paramString6, String paramString7, boolean paramBoolean, String paramString8) {
/*  701 */     JSONObject jSONObject = getVirtualIds(paramString2);
/*  702 */     boolean bool = !"".equals(this.aliasTableName) ? true : false;
/*  703 */     String str1 = bool ? (this.aliasTableName + ".") : "t2.";
/*  704 */     String str2 = "";
/*  705 */     RecordSet recordSet = new RecordSet();
/*      */     
/*  707 */     HrmCommonServiceImpl hrmCommonServiceImpl = new HrmCommonServiceImpl();
/*  708 */     String str3 = hrmCommonServiceImpl.getCptRoleMembersSql(str1 + "roleid", str1 + "rolelevel");
/*      */     
/*  710 */     if ("prj".equalsIgnoreCase(paramString1) || "prjtsk".equalsIgnoreCase(paramString1)) {
/*  711 */       if (paramString3.equals("1")) {
/*  712 */         str2 = str2 + "(" + str1 + "sharetype=1 and " + str1 + "userid=" + paramString2 + ") or ";
/*  713 */         str2 = str2 + "(" + str1 + "sharetype=6 and " + str1 + "userid=" + paramString2 + ") or ";
/*  714 */         str2 = str2 + "(" + str1 + "sharetype=5 and " + str1 + "subcompanyid=" + paramString6 + " and " + str1 + "seclevel<=" + paramString7 + " and seclevelmax>=" + paramString7 + " )  or (" + str1 + "sharetype=2 and " + str1 + "departmentid=" + paramString5 + " and " + str1 + "seclevel<=" + paramString7 + " and seclevelmax>=" + paramString7 + " ) or ";
/*      */         
/*  716 */         str2 = str2 + " (" + str1 + "sharetype=3 and " + str1 + "seclevel<=" + paramString7 + " and " + str1 + "seclevelmax>=" + paramString7 + "  and " + paramString2 + " in (" + str3 + ") ) or ";
/*  717 */         str2 = str2 + " (" + str1 + "sharetype=4 and " + str1 + "foralluser=1 and " + str1 + "seclevel<=" + paramString7 + " and seclevelmax>=" + paramString7 + " ) ";
/*      */         
/*  719 */         String str = "";
/*  720 */         if (recordSet.getDBType().equals("oracle")) {
/*  721 */           str = str + " ( " + str1 + "joblevel=1 AND ','||" + str1 + "scopeid||',' like '%," + paramString5 + ",%') or (" + str1 + "joblevel=2 AND ','||" + str1 + "scopeid||',' like '%," + paramString6 + ",%') ";
/*      */         }
/*  723 */         else if (recordSet.getDBType().equals("mysql")) {
/*  724 */           str = str + " ( " + str1 + "joblevel=1 AND concat(','," + str1 + "scopeid,',') like '%," + paramString5 + ",%') or (" + str1 + "joblevel=2 AND concat(','," + str1 + "scopeid,',') like '%," + paramString6 + ",%') ";
/*      */         
/*      */         }
/*  727 */         else if (recordSet.getDBType().equals("postgresql")) {
/*  728 */           str = str + " ( " + str1 + "joblevel=1 AND ','||" + str1 + "scopeid||',' like '%," + paramString5 + ",%') or (" + str1 + "joblevel=2 AND ','||" + str1 + "scopeid||',' like '%," + paramString6 + ",%') ";
/*      */         }
/*      */         else {
/*      */           
/*  732 */           str = str + " ( " + str1 + "joblevel=1 AND ','+" + str1 + "scopeid+',' like '%," + paramString5 + ",%') or (" + str1 + "joblevel=2 AND ','+" + str1 + "scopeid+',' like '%," + paramString6 + ",%') ";
/*      */         } 
/*      */ 
/*      */ 
/*      */         
/*  737 */         if (jSONObject.containsKey("subids")) {
/*  738 */           String[] arrayOfString = Util.TokenizerString2(jSONObject.getString("subids"), ",");
/*  739 */           for (byte b = 0; b < arrayOfString.length; b++) {
/*  740 */             if (!"".equals(arrayOfString[b])) {
/*  741 */               if (recordSet.getDBType().equals("oracle")) {
/*  742 */                 str = str + " or (" + str1 + "joblevel=2 AND ','||" + str1 + "scopeid||',' like '%," + arrayOfString[b] + ",%')";
/*  743 */               } else if (recordSet.getDBType().equals("mysql")) {
/*  744 */                 str = str + " or (" + str1 + "joblevel=2 AND concat(','," + str1 + "scopeid,',') like '%," + arrayOfString[b] + ",%')";
/*      */               }
/*  746 */               else if (recordSet.getDBType().equals("postgresql")) {
/*  747 */                 str = str + " or (" + str1 + "joblevel=2 AND ','||" + str1 + "scopeid||',' like '%," + arrayOfString[b] + ",%')";
/*      */               } else {
/*      */                 
/*  750 */                 str = str + " or (" + str1 + "joblevel=2 AND ','+" + str1 + "scopeid+',' like '%," + arrayOfString[b] + ",%')";
/*      */               } 
/*      */             }
/*      */           } 
/*      */         } 
/*  755 */         if (jSONObject.containsKey("deptids")) {
/*  756 */           String[] arrayOfString = Util.TokenizerString2(jSONObject.getString("deptids"), ",");
/*  757 */           for (byte b = 0; b < arrayOfString.length; b++) {
/*  758 */             if (!"".equals(arrayOfString[b])) {
/*  759 */               if (recordSet.getDBType().equals("oracle")) {
/*  760 */                 str = str + " or (" + str1 + "joblevel=1 AND ','||" + str1 + "scopeid||',' like '%," + arrayOfString[b] + ",%')";
/*  761 */               } else if (recordSet.getDBType().equals("mysql")) {
/*  762 */                 str = str + " or (" + str1 + "joblevel=1 AND concat(','," + str1 + "scopeid,',') like '%," + arrayOfString[b] + ",%')";
/*      */               }
/*  764 */               else if (recordSet.getDBType().equals("postgresql")) {
/*  765 */                 str = str + " or (" + str1 + "joblevel=1 AND ','||" + str1 + "scopeid||',' like '%," + arrayOfString[b] + ",%')";
/*      */               } else {
/*      */                 
/*  768 */                 str = str + " or (" + str1 + "joblevel=1 AND ','+" + str1 + "scopeid+',' like '%," + arrayOfString[b] + ",%')";
/*      */               } 
/*      */             }
/*      */           } 
/*      */         } 
/*      */         
/*  774 */         str2 = str2 + " or (" + str1 + "sharetype=11 and " + str1 + "jobtitleid=" + paramString8 + " and (" + str1 + "joblevel=0 OR " + str + ") ) ";
/*      */         
/*  776 */         if (jSONObject.containsKey("subids")) {
/*  777 */           str2 = str2 + " or (" + str1 + "sharetype=5 and " + str1 + "subcompanyid in (" + jSONObject.getString("subids") + ") and " + str1 + "seclevel<=" + paramString7 + " and seclevelmax>=" + paramString7 + ")  ";
/*      */         }
/*  779 */         if (jSONObject.containsKey("deptids")) {
/*  780 */           str2 = str2 + " or (" + str1 + "sharetype=2 and " + str1 + "departmentid in (" + jSONObject.getString("deptids") + ") and " + str1 + "seclevel<=" + paramString7 + " and seclevelmax>=" + paramString7 + ")  ";
/*      */         }
/*      */       } else {
/*      */         
/*  784 */         str2 = str2 + "(" + str1 + "sharetype=9 and " + str1 + "crmid=" + paramString2 + ") or\t(" + str1 + "sharetype=10 and " + str1 + "crmid=" + paramString4 + " and " + str1 + "seclevel<=" + paramString7 + " and seclevelmax>=" + paramString7 + " )";
/*      */       }
/*      */     
/*  787 */     } else if ("cpt".equalsIgnoreCase(paramString1)) {
/*  788 */       if (paramString3.equals("1")) {
/*  789 */         boolean bool1 = isShowsub(paramString2);
/*  790 */         ArrayList arrayList = new ArrayList();
/*  791 */         int i = Util.getIntValue(paramString7);
/*  792 */         if (bool1 && paramBoolean) {
/*  793 */           arrayList = (ArrayList)User.getBelongtoUsersByUserId(paramString2);
/*      */         }
/*  795 */         str2 = str2 + "(" + str1 + "sharetype=1 and " + str1 + "userid in (" + paramString2 + ((bool1 && paramBoolean) ? ("," + User.getBelongtoidsByUserId(paramString2)) : "") + ") ) or ";
/*  796 */         str2 = str2 + "(" + str1 + "sharetype=5 and (" + str1 + "subcompanyid=" + paramString6 + " and " + str1 + "seclevel<=" + paramString7 + " and seclevelmax>=" + paramString7 + ") ) or";
/*      */         
/*  798 */         String str = "";
/*  799 */         if (recordSet.getDBType().equals("oracle")) {
/*  800 */           str = str + " ( " + str1 + "joblevel=1 AND ','||" + str1 + "scopeid||',' like '%," + paramString5 + ",%') or (" + str1 + "joblevel=2 AND ','||" + str1 + "scopeid||',' like '%," + paramString6 + ",%') ";
/*      */         
/*      */         }
/*  803 */         else if (recordSet.getDBType().equals("mysql")) {
/*  804 */           str = str + " ( " + str1 + "joblevel=1 AND concat(','," + str1 + "scopeid,',') like '%," + paramString5 + ",%') or (" + str1 + "joblevel=2 AND concat(','," + str1 + "scopeid,',') like '%," + paramString6 + ",%') ";
/*      */         
/*      */         }
/*  807 */         else if (recordSet.getDBType().equals("postgresql")) {
/*  808 */           str = str + " ( " + str1 + "joblevel=1 AND ','||" + str1 + "scopeid||',' like '%," + paramString5 + ",%') or (" + str1 + "joblevel=2 AND ','||" + str1 + "scopeid||',' like '%," + paramString6 + ",%') ";
/*      */         } else {
/*      */           
/*  811 */           str = str + " ( " + str1 + "joblevel=1 AND ','+" + str1 + "scopeid+',' like '%," + paramString5 + ",%') or (" + str1 + "joblevel=2 AND ','+" + str1 + "scopeid+',' like '%," + paramString6 + ",%') ";
/*      */         } 
/*      */ 
/*      */         
/*  815 */         if (jSONObject.containsKey("subids")) {
/*  816 */           String[] arrayOfString = Util.TokenizerString2(jSONObject.getString("subids"), ",");
/*  817 */           for (byte b = 0; b < arrayOfString.length; b++) {
/*  818 */             if (!"".equals(arrayOfString[b])) {
/*  819 */               if (recordSet.getDBType().equals("oracle")) {
/*  820 */                 str = str + " or (" + str1 + "joblevel=2 AND ','||" + str1 + "scopeid||',' like '%," + arrayOfString[b] + ",%')";
/*  821 */               } else if (recordSet.getDBType().equals("mysql")) {
/*  822 */                 str = str + " or (" + str1 + "joblevel=2 AND concat(','," + str1 + "scopeid,',') like '%," + arrayOfString[b] + ",%')";
/*      */               }
/*  824 */               else if (recordSet.getDBType().equals("postgresql")) {
/*  825 */                 str = str + " or (" + str1 + "joblevel=2 AND ','||" + str1 + "scopeid||',' like '%," + arrayOfString[b] + ",%')";
/*      */               } else {
/*      */                 
/*  828 */                 str = str + " or (" + str1 + "joblevel=2 AND ','+" + str1 + "scopeid+',' like '%," + arrayOfString[b] + ",%')";
/*      */               } 
/*      */             }
/*      */           } 
/*      */         } 
/*  833 */         if (jSONObject.containsKey("deptids")) {
/*  834 */           String[] arrayOfString = Util.TokenizerString2(jSONObject.getString("deptids"), ",");
/*  835 */           for (byte b = 0; b < arrayOfString.length; b++) {
/*  836 */             if (!"".equals(arrayOfString[b])) {
/*  837 */               if (recordSet.getDBType().equals("oracle")) {
/*  838 */                 str = str + " or (" + str1 + "joblevel=1 AND ','||" + str1 + "scopeid||',' like '%," + arrayOfString[b] + ",%')";
/*  839 */               } else if (recordSet.getDBType().equals("mysql")) {
/*  840 */                 str = str + " or (" + str1 + "joblevel=1 AND concat(','," + str1 + "scopeid,',') like '%," + arrayOfString[b] + ",%')";
/*      */               }
/*  842 */               else if (recordSet.getDBType().equals("postgresql")) {
/*  843 */                 str = str + " or (" + str1 + "joblevel=1 AND ','||" + str1 + "scopeid||',' like '%," + arrayOfString[b] + ",%')";
/*      */               } else {
/*      */                 
/*  846 */                 str = str + " or (" + str1 + "joblevel=1 AND ','+" + str1 + "scopeid+',' like '%," + arrayOfString[b] + ",%')";
/*      */               } 
/*      */             }
/*      */           } 
/*      */         } 
/*  851 */         str2 = str2 + " (" + str1 + "sharetype=11 and " + str1 + "jobtitleid=" + paramString8 + " and (" + str1 + "joblevel=0 OR " + str + ") ) ";
/*      */         
/*  853 */         if (bool1 && paramBoolean) {
/*  854 */           for (User user : arrayList) {
/*  855 */             str2 = str2 + " or (" + str1 + "subcompanyid=" + user.getUserSubCompany1() + " and " + str1 + "seclevel<=" + user.getSeclevel() + " and seclevelmax>=" + user.getSeclevel() + ") ";
/*  856 */             if (Util.getIntValue(user.getSeclevel(), 0) < i) {
/*  857 */               i = Util.getIntValue(user.getSeclevel(), 0);
/*      */             }
/*      */           } 
/*      */         }
/*  861 */         str2 = str2 + "  or (" + str1 + "sharetype=2 and ((" + str1 + "departmentid=" + paramString5 + " and " + str1 + "seclevel<=" + paramString7 + " and seclevelmax>=" + paramString7 + ")";
/*  862 */         if (bool1 && paramBoolean) {
/*  863 */           for (User user : arrayList) {
/*  864 */             str2 = str2 + " or (" + str1 + "departmentid=" + user.getUserDepartment() + " and " + str1 + "seclevel<=" + user.getSeclevel() + " and seclevelmax>=" + user.getSeclevel() + ") ";
/*      */           }
/*      */         }
/*  867 */         str2 = str2 + " )) or ";
/*      */         
/*  869 */         str2 = str2 + " (" + str1 + "sharetype=3 and (( " + str1 + "seclevel<=" + paramString7 + " and " + str1 + "seclevelmax>=" + paramString7 + " and " + paramString2 + " in (" + str3 + ")) ";
/*  870 */         if (bool1 && paramBoolean) {
/*  871 */           for (User user : arrayList) {
/*  872 */             str2 = str2 + " or ( " + str1 + "seclevel<=" + user.getSeclevel() + " and " + str1 + "seclevelmax>=" + user.getSeclevel() + " and " + user.getUID() + " in (" + str3 + ")) ";
/*      */           }
/*      */         }
/*  875 */         str2 = str2 + " )) or ";
/*  876 */         str2 = str2 + " (" + str1 + "sharetype=4 and " + str1 + "foralluser=1 and " + str1 + "seclevel<=" + (bool1 ? ("" + i) : paramString7) + " and seclevelmax>=" + (bool1 ? ("" + i) : paramString7) + ") ";
/*      */ 
/*      */         
/*  879 */         if (jSONObject.containsKey("subids")) {
/*  880 */           str2 = str2 + " or (" + str1 + "sharetype=5 and " + str1 + "subcompanyid in (" + jSONObject.getString("subids") + ") and " + str1 + "seclevel<=" + paramString7 + " and seclevelmax>=" + paramString7 + ")  ";
/*      */         }
/*  882 */         if (jSONObject.containsKey("deptids")) {
/*  883 */           str2 = str2 + " or (" + str1 + "sharetype=2 and " + str1 + "departmentid in (" + jSONObject.getString("deptids") + ") and " + str1 + "seclevel<=" + paramString7 + " and seclevelmax>=" + paramString7 + ")  ";
/*      */         }
/*      */         
/*  886 */         if (bool1 && paramBoolean) {
/*  887 */           for (User user : arrayList) {
/*  888 */             JSONObject jSONObject1 = getVirtualIds("" + user.getUID());
/*  889 */             if (jSONObject1.containsKey("subids")) {
/*  890 */               str2 = str2 + " or (" + str1 + "sharetype=5 and " + str1 + "subcompanyid in (" + jSONObject1.getString("subids") + ") and " + str1 + "seclevel<=" + user.getSeclevel() + " and seclevelmax>=" + user.getSeclevel() + ")  ";
/*      */             }
/*  892 */             if (jSONObject1.containsKey("deptids")) {
/*  893 */               str2 = str2 + " or (" + str1 + "sharetype=2 and " + str1 + "departmentid in (" + jSONObject1.getString("deptids") + ") and " + str1 + "seclevel<=" + user.getSeclevel() + " and seclevelmax>=" + user.getSeclevel() + ")  ";
/*      */             }
/*      */           } 
/*      */         }
/*      */       } else {
/*      */         
/*  899 */         str2 = str2 + "(" + str1 + "sharetype=9 and " + str1 + "crmid=" + paramString2 + ") or\t(" + str1 + "sharetype=10 and " + str1 + "crmid=" + paramString4 + " and " + str1 + "seclevel<=" + paramString7 + " and seclevelmax>=" + paramString7 + ")";
/*      */       } 
/*      */     } 
/*      */ 
/*      */ 
/*      */     
/*  905 */     return str2;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getAssortmentSqlWhere(User paramUser) {
/*  914 */     String str1 = "";
/*  915 */     RecordSet recordSet = new RecordSet();
/*  916 */     String str2 = "";
/*  917 */     if (recordSet.getDBType().equals("oracle")) {
/*  918 */       str2 = "(a.id=b.id or a.supassortmentstr LIKE '0|'||b.id||'|%')";
/*  919 */     } else if (recordSet.getDBType().equals("mysql")) {
/*  920 */       str2 = "(a.id=b.id or a.supassortmentstr LIKE concat('0|',b.id,'|%'))";
/*      */     }
/*  922 */     else if (recordSet.getDBType().equals("postgresql")) {
/*  923 */       str2 = "(a.id=b.id or a.supassortmentstr LIKE '0|'||b.id||'|%')";
/*      */     } else {
/*      */       
/*  926 */       str2 = "(a.id=b.id or a.supassortmentstr LIKE '0|'+CONVERT(VARCHAR(10), b.id) +'|%')";
/*      */     } 
/*      */ 
/*      */     
/*  930 */     str1 = str1 + " and (exists(select 1 from CptCapitalShareInfo t2 where t2.relateditemid=t1.id and (" + getShareWhereByUser("cpt", paramUser) + ")) or t1.capitalgroupid in (SELECT DISTINCT a.id FROM CptCapitalAssortment a ,CptCapitalAssortment b WHERE " + str2 + " AND b.id in (select assortmentid from CptAssortmentShare t2 where 1=1 and (" + getTypeShareWhereByUser("cpt", paramUser) + "))))";
/*      */ 
/*      */     
/*  933 */     return str1;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean shareIfExists(String paramString1, String paramString2) {
/*  943 */     boolean bool = false;
/*  944 */     if (paramString1 == null || paramString2 == null) {
/*  945 */       return false;
/*      */     }
/*  947 */     RecordSet recordSet = new RecordSet();
/*  948 */     String[] arrayOfString = Util.TokenizerString2(paramString2, "|");
/*  949 */     if (arrayOfString != null && arrayOfString.length >= 6) {
/*  950 */       int i = Util.getIntValue(arrayOfString[0], 0);
/*  951 */       int j = Util.getIntValue(arrayOfString[1], 0);
/*  952 */       int k = Util.getIntValue(arrayOfString[2], 0);
/*  953 */       int m = Util.getIntValue(arrayOfString[3], 0);
/*  954 */       int n = Util.getIntValue(arrayOfString[4], 0);
/*  955 */       String str1 = Util.null2String(arrayOfString[5], "0");
/*      */ 
/*      */       
/*  958 */       String str2 = "relateditemid";
/*  959 */       if ("CptAssortmentShare".equalsIgnoreCase(paramString1) || "uf4mode_CptAssortmentShare".equalsIgnoreCase(paramString1)) {
/*  960 */         str2 = "assortmentid";
/*      */       }
/*      */       
/*  963 */       String str3 = " select 1 from " + paramString1 + " where " + str2 + "=" + i + " ";
/*  964 */       if (arrayOfString.length >= 9) {
/*  965 */         int i1 = Util.getIntValue(arrayOfString[6], 100);
/*  966 */         int i2 = Util.getIntValue(arrayOfString[7], 0);
/*  967 */         String str = Util.null2String(arrayOfString[8], "0");
/*  968 */         if (k == 1) {
/*  969 */           str3 = str3 + " and sharetype=1 and sharelevel=" + j + " and userid in (" + str1 + ") ";
/*  970 */         } else if (k == 2) {
/*  971 */           str3 = str3 + " and sharetype=2 and sharelevel=" + j + " and ( seclevel=" + m + " and seclevelMax=" + i1 + " ) and departmentid in (" + str1 + ") ";
/*  972 */         } else if (k == 3) {
/*  973 */           str3 = str3 + " and sharetype=3 and sharelevel=" + j + " and ( seclevel=" + m + " and seclevelMax=" + i1 + " ) and rolelevel>=" + n + " and roleid in (" + str1 + ") ";
/*  974 */         } else if (k == 4) {
/*  975 */           str3 = str3 + " and sharetype=4 and sharelevel=" + j + " and ( seclevel=" + m + " and seclevelMax=" + i1 + " ) ";
/*  976 */         } else if (k == 5) {
/*  977 */           str3 = str3 + " and sharetype=5 and sharelevel=" + j + " and ( seclevel=" + m + " and seclevelMax=" + i1 + " ) and subcompanyid in (" + str1 + ") ";
/*  978 */         } else if (k == 11) {
/*  979 */           String[] arrayOfString1 = Util.TokenizerString2(str, ",");
/*  980 */           String str4 = "";
/*  981 */           if (!"0".equals(Integer.valueOf(i2))) {
/*  982 */             str4 = " and ( ";
/*  983 */             for (byte b = 0; b < arrayOfString1.length; b++) {
/*  984 */               if (recordSet.getDBType().equals("oracle") || recordSet.getDBType().equals("postgresql")) {
/*  985 */                 str4 = str4 + " ','||scopeid||',' like '%," + arrayOfString1[b] + ",%'";
/*  986 */               } else if (recordSet.getDBType().equals("mysql")) {
/*  987 */                 str4 = str4 + " concat(',',scopeid,',') like '%," + arrayOfString1[b] + ",%'";
/*      */               } else {
/*  989 */                 str4 = str4 + " ','+scopeid+',' like '%," + arrayOfString1[b] + ",%'";
/*      */               } 
/*  991 */               if (b != arrayOfString1.length - 1) {
/*  992 */                 str4 = str4 + " or ";
/*      */               }
/*      */             } 
/*  995 */             str4 = str4 + " ) ";
/*      */           } 
/*      */           
/*  998 */           str3 = str3 + " and sharetype=11 and sharelevel=" + j + " and jobtitleid in (" + str1 + ") and joblevel = " + i2 + str4;
/*      */         }
/*      */       
/* 1001 */       } else if (k == 1) {
/* 1002 */         str3 = str3 + " and sharetype=1 and sharelevel=" + j + " and userid in (" + str1 + ") ";
/* 1003 */       } else if (k == 2) {
/* 1004 */         str3 = str3 + " and sharetype=2 and sharelevel=" + j + " and seclevel<=" + m + " and departmentid in (" + str1 + ") ";
/* 1005 */       } else if (k == 3) {
/* 1006 */         str3 = str3 + " and sharetype=3 and sharelevel=" + j + " and seclevel<=" + m + " and rolelevel>=" + n + " and roleid=" + str1 + " ";
/* 1007 */       } else if (k == 4) {
/* 1008 */         str3 = str3 + " and sharetype=4 and sharelevel=" + j + " and seclevel<=" + m + " ";
/* 1009 */       } else if (k == 5) {
/* 1010 */         str3 = str3 + " and sharetype=5 and sharelevel=" + j + " and seclevel<=" + m + " and subcompanyid in (" + str1 + ") ";
/*      */       } 
/*      */ 
/*      */       
/* 1014 */       recordSet.executeSql(str3);
/* 1015 */       if (recordSet.next()) {
/* 1016 */         bool = true;
/*      */       }
/*      */     } 
/* 1019 */     return bool;
/*      */   }
/*      */   
/*      */   public String getContainsSubuserids(String paramString) {
/* 1023 */     if (isShowsub(paramString)) {
/* 1024 */       return paramString + "," + User.getBelongtoidsByUserId(paramString);
/*      */     }
/* 1026 */     return paramString;
/*      */   }
/*      */   
/*      */   private boolean isShowsub(String paramString) {
/* 1030 */     User user = new User(Util.getIntValue(paramString));
/* 1031 */     return isShowsub(user);
/*      */   }
/*      */   private boolean isShowsub(User paramUser) {
/* 1034 */     return (!"1".equals(paramUser.getAccount_type()) && "1".equals(this.hrmUserSettingComInfo.getBelongtoshowByUserId("" + paramUser.getUID())) && !"".equals(paramUser.getBelongtoids()));
/*      */   }
/*      */ 
/*      */   
/*      */   public int getDataFuncPermissionUID(User paramUser, String paramString1, String paramString2, String paramString3) {
/* 1039 */     boolean bool = paramUser.isMergeFlag();
/* 1040 */     boolean bool1 = false;
/* 1041 */     if ("".equals(Util.null2String(paramString1))) {
/* 1042 */       bool1 = true;
/*      */     }
/* 1044 */     if ("cpt_cptshare".equalsIgnoreCase(paramString2)) {
/* 1045 */       CapitalTransUtil capitalTransUtil = new CapitalTransUtil();
/* 1046 */       CheckUserRight checkUserRight = new CheckUserRight();
/* 1047 */       if (capitalTransUtil.canEdit(paramString3, paramUser) && (bool1 || checkUserRight.checkUserRight(paramString1, paramUser))) {
/* 1048 */         return paramUser.getUID();
/*      */       }
/* 1050 */       String str = paramUser.getBelongtoids();
/* 1051 */       if (!"".equals(Util.null2String(str))) {
/* 1052 */         String[] arrayOfString = Util.TokenizerString2(str, ",");
/* 1053 */         for (byte b = 0; b < arrayOfString.length; b++) {
/* 1054 */           User user = new User(Util.getIntValue(arrayOfString[b], 0));
/* 1055 */           user.setMergeFlag(bool);
/* 1056 */           if (capitalTransUtil.canEdit(paramString3, user) && (bool1 || checkUserRight.checkUserRight(paramString1, user))) {
/* 1057 */             return user.getUID();
/*      */           }
/*      */         } 
/*      */       } 
/*      */     } 
/*      */ 
/*      */ 
/*      */     
/* 1065 */     return paramUser.getUID();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getPrjFilterids(String paramString, User paramUser) {
/* 1074 */     return getShareids("prj", paramString, paramUser);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getCptFilterids(String paramString, User paramUser) {
/* 1083 */     return getShareids("cpt", paramString, paramUser);
/*      */   }
/*      */   
/*      */   private String getShareids(String paramString1, String paramString2, User paramUser) {
/* 1087 */     if (paramUser != null && !"".equals(Util.null2String(paramString2))) {
/* 1088 */       String[] arrayOfString = Util.TokenizerString2(paramString2, ",");
/* 1089 */       String str = "";
/* 1090 */       for (byte b = 0; b < arrayOfString.length; b++) {
/* 1091 */         str = str + Util.getIntValue(arrayOfString[b]) + ",";
/*      */       }
/* 1093 */       if (str.length() > 0) {
/* 1094 */         if (str.endsWith(",")) {
/* 1095 */           str = str.substring(0, str.length() - 1);
/*      */         }
/* 1097 */         String str1 = "";
/* 1098 */         String str2 = "";
/* 1099 */         RecordSet recordSet = new RecordSet();
/* 1100 */         if ("cpt".equalsIgnoreCase(paramString1)) {
/* 1101 */           str1 = " select t1.id from CptCapital  t1 where t1.id in(" + str + ") " + getAssortmentSqlWhere(paramUser);
/* 1102 */         } else if ("prj".equalsIgnoreCase(paramString1)) {
/* 1103 */           str1 = " select t1.id from Prj_ProjectInfo  t1 where t1.id in(" + str + ") and ( " + getPrjShareWhereByUser(paramUser) + " ) ";
/*      */         } 
/* 1105 */         recordSet.executeSql(str1);
/* 1106 */         while (recordSet.next()) {
/* 1107 */           str2 = str2 + recordSet.getString("id") + ",";
/*      */         }
/* 1109 */         if (str2.endsWith(",")) {
/* 1110 */           str2 = str2.substring(0, str2.length() - 1);
/*      */         }
/* 1112 */         return str2;
/*      */       } 
/*      */     } 
/* 1115 */     return "";
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private JSONObject getVirtualIds(String paramString) {
/* 1124 */     JSONObject jSONObject = new JSONObject();
/*      */     try {
/* 1126 */       String str = this.resourceVirtualComInfo.getSubcompanyids(paramString);
/* 1127 */       if (!"".equals(Util.null2String(str))) {
/* 1128 */         jSONObject.put("subids", str);
/*      */       }
/* 1130 */       str = this.resourceVirtualComInfo.getDepartmentids(paramString);
/* 1131 */       if (!"".equals(Util.null2String(str))) {
/* 1132 */         jSONObject.put("deptids", str);
/*      */       }
/* 1134 */       str = this.resourceVirtualComInfo.getManagerstrs(paramString);
/* 1135 */       if (!"".equals(Util.null2String(str))) {
/* 1136 */         jSONObject.put("managers", str);
/*      */       }
/* 1138 */     } catch (Exception exception) {
/* 1139 */       return jSONObject;
/*      */     } 
/*      */ 
/*      */     
/* 1143 */     return jSONObject;
/*      */   }
/*      */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/cpt/util/CommonShareManager.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */