/*      */ package weaver.cpt.util;
/*      */ 
/*      */ import com.engine.cpt.util.CapitalTransMethod;
/*      */ import com.weaver.formmodel.util.DateHelper;
/*      */ import com.weaver.formmodel.util.StringHelper;
/*      */ import java.util.ArrayList;
/*      */ import java.util.HashMap;
/*      */ import java.util.HashSet;
/*      */ import java.util.Iterator;
/*      */ import java.util.Map;
/*      */ import org.json.JSONException;
/*      */ import org.json.JSONObject;
/*      */ import weaver.conn.RecordSet;
/*      */ import weaver.formmode.browser.FormModeBrowserUtil;
/*      */ import weaver.general.Util;
/*      */ import weaver.proj.util.CodeUtil;
/*      */ import weaver.workflow.request.RequestManager;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ public class CptWfUtil
/*      */   implements Runnable
/*      */ {
/*   30 */   private String opType = "";
/*   31 */   private String delreqids = "";
/*   32 */   private RequestManager RequestManager = null;
/*      */   
/*      */   private boolean is4Mode = false;
/*      */   
/*      */   public CptWfUtil() {}
/*      */   
/*      */   public CptWfUtil(String paramString1, String paramString2) {
/*   39 */     this.delreqids = paramString1;
/*   40 */     this.opType = paramString2;
/*      */   }
/*      */   public CptWfUtil(RequestManager paramRequestManager, String paramString) {
/*   43 */     this.RequestManager = paramRequestManager;
/*   44 */     this.opType = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public HashMap<String, String> getCptFrozenWorkflow(String paramString) {
/*   53 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*   54 */     RecordSet recordSet1 = new RecordSet();
/*   55 */     String str1 = recordSet1.getDBType();
/*   56 */     String str2 = "";
/*   57 */     if ("sqlserver".equalsIgnoreCase(str1)) {
/*   58 */       str2 = "isnull";
/*   59 */     } else if ("oracle".equalsIgnoreCase(str1)) {
/*   60 */       str2 = "nvl";
/*   61 */     } else if ("mysql".equalsIgnoreCase(str1)) {
/*   62 */       str2 = "ifnull";
/*      */     }
/*   64 */     else if ("postgresql".equalsIgnoreCase(str1)) {
/*   65 */       str2 = "isnull";
/*      */     } 
/*   67 */     RecordSet recordSet2 = new RecordSet();
/*   68 */     StringBuffer stringBuffer1 = new StringBuffer("");
/*   69 */     String str3 = "substring";
/*   70 */     if ("sqlserver".equalsIgnoreCase(str1)) {
/*   71 */       str3 = "substring";
/*   72 */     } else if ("oracle".equalsIgnoreCase(str1)) {
/*   73 */       str3 = "substr";
/*   74 */     } else if ("mysql".equalsIgnoreCase(str1)) {
/*   75 */       str3 = "substring";
/*      */     } 
/*   77 */     String str4 = "select t2.wfid,t1.id,t1.formid,t2.zc,t3.fieldname as zc_fieldname," + str3 + "(REVERSE(Rtrim(t3.detailtable)),1,1) as zc_viewtype,t2.sl,t4.fieldname as sl_fieldname," + str3 + "(REVERSE(Rtrim(t4.detailtable)),1,1) as sl_viewtype  from workflow_base t1,cpt_cptwfconf t2 join workflow_billfield t3 on t3.id=t2.zc  join workflow_billfield t4 on t4.id=t2.sl where t2.wfid=t1.id  and t2.wftype in('fetch','mend','move','lend','back','discard','loss') ";
/*      */     
/*   79 */     recordSet2.execute(str4);
/*   80 */     ArrayList arrayList = new ArrayList();
/*   81 */     while (recordSet2.next()) {
/*   82 */       int i = Util.getIntValue(recordSet2.getString("formid"), 0);
/*   83 */       if (i >= 0) {
/*      */         continue;
/*      */       }
/*   86 */       int j = Util.getIntValue(recordSet2.getString("wfid"), 0);
/*   87 */       String str5 = recordSet2.getString("zc_fieldname");
/*   88 */       int k = Util.getIntValue(recordSet2.getString("zc_viewtype"), 0);
/*   89 */       String str6 = recordSet2.getString("sl_fieldname");
/*   90 */       int m = Util.getIntValue(recordSet2.getString("sl_viewtype"), 0);
/*      */ 
/*      */       
/*   93 */       if (k != m) {
/*      */         continue;
/*      */       }
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*  100 */       String str7 = "formtable_main_" + -i;
/*  101 */       recordSet1.execute("select tablename from workflow_bill where id=" + i);
/*  102 */       while (recordSet1.next()) {
/*  103 */         str7 = recordSet1.getString("tablename");
/*      */       }
/*  105 */       String str8 = "";
/*  106 */       if (k == 0 && m == 0) {
/*  107 */         str8 = " select m.requestid," + str2 + "(m." + str6 + ",1) as num from " + str7 + " m where m." + str5;
/*  108 */       } else if (k == m) {
/*  109 */         str8 = " select m.requestid," + str2 + "(d." + str6 + ",1) as num from " + str7 + " m," + str7 + "_dt" + k + " d where d.mainid=m.id and d." + str5;
/*      */       } 
/*      */       
/*  112 */       stringBuffer1.append(" union all ").append("\n");
/*  113 */       stringBuffer1.append(str8 + "=" + paramString + " and exists(select 1 from workflow_requestbase r where r.workflowid=" + j + " and r.requestid=m.requestid and r.currentnodetype>0 and r.currentnodetype<3 ) ").append("\n");
/*      */     } 
/*  115 */     StringBuffer stringBuffer2 = new StringBuffer();
/*  116 */     if ("sqlserver".equalsIgnoreCase(str1)) {
/*  117 */       stringBuffer2.append("  ").append("\n")
/*  118 */         .append(" select m.requestid,d.number_n as num from bill_cptfetchmain m,bill_cptfetchdetail d where d.cptfetchid=m.id and d.capitalid=" + paramString + " and exists(select 1 from workflow_requestbase r where r.requestid=m.requestid and r.currentnodetype>0 and r.currentnodetype<3) ").append("\n")
/*  119 */         .append(" union all ").append("\n")
/*  120 */         .append(" select m.requestid,d.number_n as num from bill_CptAdjustMain m,bill_CptAdjustDetail d where d.cptadjustid=m.id and d.capitalid=" + paramString + " and exists(select 1 from workflow_requestbase r where r.requestid=m.requestid and r.currentnodetype>0 and r.currentnodetype<3) ").append("\n")
/*  121 */         .append(" union all ").append("\n")
/*  122 */         .append(" select m.requestid,d.numbers as num from bill_Discard m,bill_discard_detail d where d.mainid=m.id and d.capitalid=" + paramString + " and exists(select 1 from workflow_requestbase r where r.requestid=m.requestid and r.currentnodetype>0 and r.currentnodetype<3) ").append("\n")
/*  123 */         .append(" union all ").append("\n")
/*  124 */         .append(" select m.requestid,m.losscount as num from bill_cptloss m where m.losscpt=" + paramString + " and exists(select 1 from workflow_requestbase r where r.requestid=m.requestid and r.currentnodetype>0 and r.currentnodetype<3) ").append("\n")
/*  125 */         .append(" union all ").append("\n")
/*  126 */         .append(" select m.requestid,1 as num from bill_mendCpt m where m.cptMend=" + paramString + " and exists(select 1 from workflow_requestbase r where r.requestid=m.requestid and r.currentnodetype>0 and r.currentnodetype<3) ").append("\n")
/*  127 */         .append(" union all ").append("\n")
/*  128 */         .append(" select m.requestid,1 as num from bill_returncpt m where m.returnCpt=" + paramString + " and exists(select 1 from workflow_requestbase r where r.requestid=m.requestid and r.currentnodetype>0 and r.currentnodetype<3) ").append("\n")
/*  129 */         .append(" union all ").append("\n")
/*  130 */         .append("  select l.requestid,1 as num from bill_cptlend l where l.lendCpt=" + paramString + " and EXISTS (select 1 from workflow_requestbase r where r.requestid=l.requestid and r.currentnodetype>0 and r.currentnodetype<3) ").append("\n")
/*  131 */         .append(stringBuffer1.toString()).append("\n");
/*      */     
/*      */     }
/*  134 */     else if ("oracle".equalsIgnoreCase(str1)) {
/*  135 */       stringBuffer2.append("  ").append("\n")
/*  136 */         .append(" select m.requestid,d.number_n as num from bill_cptfetchmain m,bill_cptfetchdetail d where d.cptfetchid=m.id and d.capitalid=" + paramString + " and exists(select 1 from workflow_requestbase r where r.requestid=m.requestid and r.currentnodetype>0 and r.currentnodetype<3) ").append("\n")
/*  137 */         .append(" union all ").append("\n")
/*  138 */         .append(" select m.requestid,d.number_n as num from bill_CptAdjustMain m,bill_CptAdjustDetail d where d.cptadjustid=m.id and d.capitalid=" + paramString + " and exists(select 1 from workflow_requestbase r where r.requestid=m.requestid and r.currentnodetype>0 and r.currentnodetype<3) ").append("\n")
/*  139 */         .append(" union all ").append("\n")
/*  140 */         .append(" select m.requestid,d.numbers as num from bill_Discard m,bill_discard_detail d where d.mainid=m.id and d.capitalid=" + paramString + " and exists(select 1 from workflow_requestbase r where r.requestid=m.requestid and r.currentnodetype>0 and r.currentnodetype<3) ").append("\n")
/*  141 */         .append(" union all ").append("\n")
/*  142 */         .append(" select m.requestid,m.losscount as num from bill_cptloss m where m.losscpt=" + paramString + " and exists(select 1 from workflow_requestbase r where r.requestid=m.requestid and r.currentnodetype>0 and r.currentnodetype<3) ").append("\n")
/*  143 */         .append(" union all ").append("\n")
/*  144 */         .append(" select m.requestid,1 as num from bill_mendCpt m where m.cptMend=" + paramString + " and exists(select 1 from workflow_requestbase r where r.requestid=m.requestid and r.currentnodetype>0 and r.currentnodetype<3) ").append("\n")
/*  145 */         .append(" union all ").append("\n")
/*  146 */         .append(" select m.requestid,1 as num from bill_returncpt m where m.returnCpt=" + paramString + " and exists(select 1 from workflow_requestbase r where r.requestid=m.requestid and r.currentnodetype>0 and r.currentnodetype<3) ").append("\n")
/*  147 */         .append(" union all ").append("\n")
/*  148 */         .append(" select l.requestid,1 as num from bill_cptlend l where l.lendCpt=" + paramString + " and exists(select 1 from workflow_requestbase r where r.requestid=l.requestid and r.currentnodetype>0 and r.currentnodetype<3) ").append("\n")
/*  149 */         .append(stringBuffer1.toString()).append("\n");
/*      */ 
/*      */     
/*      */     }
/*  153 */     else if ("postgresql".equalsIgnoreCase(str1)) {
/*  154 */       stringBuffer2.append("  ").append("\n")
/*  155 */         .append(" select m.requestid,d.number_n as num from bill_cptfetchmain m,bill_cptfetchdetail d where d.cptfetchid=m.id and d.capitalid=" + paramString + " and exists(select 1 from workflow_requestbase r where r.requestid=m.requestid and r.currentnodetype>0 and r.currentnodetype<3) ").append("\n")
/*  156 */         .append(" union all ").append("\n")
/*  157 */         .append(" select m.requestid,d.number_n as num from bill_CptAdjustMain m,bill_CptAdjustDetail d where d.cptadjustid=m.id and d.capitalid=" + paramString + " and exists(select 1 from workflow_requestbase r where r.requestid=m.requestid and r.currentnodetype>0 and r.currentnodetype<3) ").append("\n")
/*  158 */         .append(" union all ").append("\n")
/*  159 */         .append(" select m.requestid,d.numbers as num from bill_Discard m,bill_discard_detail d where d.mainid=m.id and d.capitalid=" + paramString + " and exists(select 1 from workflow_requestbase r where r.requestid=m.requestid and r.currentnodetype>0 and r.currentnodetype<3) ").append("\n")
/*  160 */         .append(" union all ").append("\n")
/*  161 */         .append(" select m.requestid,m.losscount as num from bill_cptloss m where m.losscpt=" + paramString + " and exists(select 1 from workflow_requestbase r where r.requestid=m.requestid and r.currentnodetype>0 and r.currentnodetype<3) ").append("\n")
/*  162 */         .append(" union all ").append("\n")
/*  163 */         .append(" select m.requestid,1 as num from bill_mendCpt m where m.cptMend=" + paramString + " and exists(select 1 from workflow_requestbase r where r.requestid=m.requestid and r.currentnodetype>0 and r.currentnodetype<3) ").append("\n")
/*  164 */         .append(" union all ").append("\n")
/*  165 */         .append(" select m.requestid,1 as num from bill_returncpt m where m.returnCpt=" + paramString + " and exists(select 1 from workflow_requestbase r where r.requestid=m.requestid and r.currentnodetype>0 and r.currentnodetype<3) ").append("\n")
/*  166 */         .append(" union all ").append("\n")
/*  167 */         .append(" select l.requestid,1 as num from bill_cptlend l where l.lendCpt=" + paramString + " and exists(select 1 from workflow_requestbase r where r.requestid=l.requestid and r.currentnodetype>0 and r.currentnodetype<3) ").append("\n")
/*  168 */         .append(stringBuffer1.toString()).append("\n");
/*      */ 
/*      */     
/*      */     }
/*  172 */     else if ("mysql".equalsIgnoreCase(str1)) {
/*  173 */       stringBuffer2.append("  ").append("\n")
/*  174 */         .append(" select m.requestid,d.number_n as num from bill_cptfetchmain m,bill_cptfetchdetail d where d.cptfetchid=m.id and d.capitalid=" + paramString + " and exists(select 1 from workflow_requestbase r where r.requestid=m.requestid and r.currentnodetype>0 and r.currentnodetype<3) ").append("\n")
/*  175 */         .append(" union all ").append("\n")
/*  176 */         .append(" select m.requestid,d.number_n as num from bill_CptAdjustMain m,bill_CptAdjustDetail d where d.cptadjustid=m.id and d.capitalid=" + paramString + " and exists(select 1 from workflow_requestbase r where r.requestid=m.requestid and r.currentnodetype>0 and r.currentnodetype<3) ").append("\n")
/*  177 */         .append(" union all ").append("\n")
/*  178 */         .append(" select m.requestid,d.numbers as num from bill_Discard m,bill_discard_detail d where d.mainid=m.id and d.capitalid=" + paramString + " and exists(select 1 from workflow_requestbase r where r.requestid=m.requestid and r.currentnodetype>0 and r.currentnodetype<3) ").append("\n")
/*  179 */         .append(" union all ").append("\n")
/*  180 */         .append(" select m.requestid,m.losscount as num from bill_cptloss m where m.losscpt=" + paramString + " and exists(select 1 from workflow_requestbase r where r.requestid=m.requestid and r.currentnodetype>0 and r.currentnodetype<3) ").append("\n")
/*  181 */         .append(" union all ").append("\n")
/*  182 */         .append(" select m.requestid,1 as num from bill_mendCpt m where m.cptMend=" + paramString + " and exists(select 1 from workflow_requestbase r where r.requestid=m.requestid and r.currentnodetype>0 and r.currentnodetype<3) ").append("\n")
/*  183 */         .append(" union all ").append("\n")
/*  184 */         .append(" select m.requestid,1 as num from bill_returncpt m where m.returnCpt=" + paramString + " and exists(select 1 from workflow_requestbase r where r.requestid=m.requestid and r.currentnodetype>0 and r.currentnodetype<3) ").append("\n")
/*  185 */         .append(" union all ").append("\n")
/*  186 */         .append(" select l.requestid,1 as num from bill_cptlend l where l.lendCpt=" + paramString + " and exists(select 1 from workflow_requestbase r where r.requestid=l.requestid and r.currentnodetype>0 and r.currentnodetype<3) ").append("\n")
/*  187 */         .append(stringBuffer1.toString()).append("\n");
/*      */     } 
/*      */ 
/*      */ 
/*      */     
/*  192 */     recordSet1.execute(stringBuffer2.toString());
/*  193 */     while (recordSet1.next()) {
/*  194 */       String str = Util.null2String(recordSet1.getString("requestid"));
/*  195 */       float f = Util.getFloatValue(recordSet1.getString("num"));
/*  196 */       if (hashMap.containsKey(str)) {
/*  197 */         f = Util.getFloatValue((String)hashMap.get(str)) + f;
/*      */       }
/*  199 */       hashMap.put(recordSet1.getString("requestid"), "" + f);
/*      */     } 
/*  201 */     return (HashMap)hashMap;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void regenTrgCptFrozennumUpdate() {
/*  209 */     RecordSet recordSet1 = new RecordSet();
/*  210 */     String str1 = recordSet1.getDBType();
/*  211 */     String str2 = "";
/*  212 */     if ("sqlserver".equalsIgnoreCase(str1)) {
/*  213 */       str2 = "isnull";
/*  214 */     } else if ("oracle".equalsIgnoreCase(str1)) {
/*  215 */       str2 = "nvl";
/*  216 */     } else if ("mysql".equalsIgnoreCase(str1)) {
/*  217 */       str2 = "ifnull";
/*      */     }
/*  219 */     else if ("postgresql".equalsIgnoreCase(str1)) {
/*  220 */       str2 = "isnull";
/*      */     } 
/*  222 */     recordSet1.setChecksql(false);
/*  223 */     RecordSet recordSet2 = new RecordSet();
/*  224 */     StringBuffer stringBuffer1 = new StringBuffer("");
/*  225 */     String str3 = "substring";
/*  226 */     if ("sqlserver".equalsIgnoreCase(str1)) {
/*  227 */       str3 = "substring";
/*  228 */     } else if ("oracle".equalsIgnoreCase(str1)) {
/*  229 */       str3 = "substr";
/*  230 */     } else if ("mysql".equalsIgnoreCase(str1)) {
/*  231 */       str3 = "substring";
/*      */     } 
/*  233 */     String str4 = "select t1.id,t1.formid,t2.zc,t3.fieldname as zc_fieldname," + str3 + "(REVERSE(Rtrim(t3.detailtable)),1,1) as zc_viewtype,t2.sl,t4.fieldname as sl_fieldname," + str3 + "(REVERSE(Rtrim(t4.detailtable)),1,1) as sl_viewtype  from workflow_base t1,cpt_cptwfconf t2 join workflow_billfield t3 on t3.id=t2.zc  join workflow_billfield t4 on t4.id=t2.sl where t2.wfid=t1.id  and t2.wftype in('fetch','mend','move','lend','back','discard','loss') ";
/*      */     
/*  235 */     recordSet2.execute(str4);
/*  236 */     ArrayList<String> arrayList = new ArrayList();
/*  237 */     while (recordSet2.next()) {
/*  238 */       int i = Util.getIntValue(recordSet2.getString("formid"), 0);
/*  239 */       if (i >= 0) {
/*      */         continue;
/*      */       }
/*  242 */       String str5 = recordSet2.getString("zc_fieldname");
/*  243 */       int j = Util.getIntValue(recordSet2.getString("zc_viewtype"), 0);
/*  244 */       String str6 = recordSet2.getString("sl_fieldname");
/*  245 */       int k = Util.getIntValue(recordSet2.getString("sl_viewtype"), 0);
/*      */ 
/*      */       
/*  248 */       if (j != k) {
/*      */         continue;
/*      */       }
/*  251 */       if (arrayList.contains("" + i + "_" + str5 + "_" + str6 + "_" + j)) {
/*      */         continue;
/*      */       }
/*  254 */       arrayList.add("" + i + "_" + str5 + "_" + str6 + "_" + j);
/*  255 */       String str7 = "formtable_main_" + -i;
/*  256 */       recordSet1.execute("select tablename from workflow_bill where id=" + i);
/*  257 */       while (recordSet1.next()) {
/*  258 */         str7 = recordSet1.getString("tablename");
/*      */       }
/*  260 */       String str8 = "";
/*  261 */       if (j == 0 && k == 0) {
/*  262 */         str8 = " select " + str2 + "(m." + str6 + ",1) as num from " + str7 + " m where m." + str5;
/*  263 */       } else if (j == k) {
/*  264 */         str8 = " select " + str2 + "(d." + str6 + ",1) as num from " + str7 + " m," + str7 + "_dt" + j + " d where d.mainid=m.id and d." + str5;
/*      */       } 
/*      */       
/*  267 */       stringBuffer1.append(" union all ").append("\n");
/*  268 */       if ("sqlserver".equalsIgnoreCase(str1)) {
/*  269 */         stringBuffer1.append(str8 + "=@cptid and exists(select 1 from workflow_requestbase r left join cpt_cptwfconf b on r.workflowid=b.wfid where r.requestid=m.requestid and r.currentnodetype>0 and r.currentnodetype<3 and b.wftype in('fetch','mend','move','lend','back','discard','loss')) ").append("\n"); continue;
/*  270 */       }  if ("oracle".equalsIgnoreCase(str1)) {
/*  271 */         stringBuffer1.append(str8 + "=i_cptid and exists(select 1 from workflow_requestbase r left join cpt_cptwfconf b on r.workflowid=b.wfid where r.requestid=m.requestid and r.currentnodetype>0 and r.currentnodetype<3 and b.wftype in('fetch','mend','move','lend','back','discard','loss')) ").append("\n"); continue;
/*  272 */       }  if ("mysql".equalsIgnoreCase(str1)) {
/*  273 */         stringBuffer1.append(str8 + "=i_cptid and exists(select 1 from workflow_requestbase r left join cpt_cptwfconf b on r.workflowid=b.wfid where r.requestid=m.requestid and r.currentnodetype>0 and r.currentnodetype<3 and b.wftype in('fetch','mend','move','lend','back','discard','loss')) ").append("\n"); continue;
/*  274 */       }  if ("postgresql".equalsIgnoreCase(str1)) {
/*  275 */         stringBuffer1.append(str8 + "=i_cptid and exists(select 1 from workflow_requestbase r left join cpt_cptwfconf b on r.workflowid=b.wfid where r.requestid=m.requestid and r.currentnodetype>0 and r.currentnodetype<3 and b.wftype in('fetch','mend','move','lend','back','discard','loss')) ").append("\n");
/*      */       }
/*      */     } 
/*      */     
/*  279 */     StringBuffer stringBuffer2 = new StringBuffer();
/*  280 */     if ("sqlserver".equalsIgnoreCase(str1)) {
/*  281 */       recordSet1.execute("drop trigger trg_cptfrozennum_update");
/*  282 */       stringBuffer2.append(" create trigger trg_cptfrozennum_update ").append("\n")
/*  283 */         .append(" on cptcapital ").append("\n")
/*  284 */         .append(" for update ").append("\n")
/*  285 */         .append(" as ").append("\n")
/*  286 */         .append(" if update(frozennum) ").append("\n")
/*  287 */         .append(" begin ").append("\n")
/*  288 */         .append(" declare @cptid int; ").append("\n")
/*  289 */         .append(" declare @isdata int; ").append("\n")
/*  290 */         .append(" declare @newfrozennum decimal(15,2); ").append("\n")
/*  291 */         .append(" declare @oldfrozennum decimal(15,2); ").append("\n")
/*  292 */         .append(" declare my_cursor cursor for select inserted.id,inserted.isdata,inserted.frozennum,deleted.frozennum from inserted,deleted where inserted.id=deleted.id; ").append("\n")
/*  293 */         .append(" open my_cursor ").append("\n")
/*  294 */         .append(" fetch next from my_cursor into @cptid,@isdata,@newfrozennum,@oldfrozennum; ").append("\n")
/*  295 */         .append(" while @@fetch_status=0 ").append("\n")
/*  296 */         .append(" begin ").append("\n")
/*  297 */         .append(" if @isdata!=2 fetch next from my_cursor into @cptid,@isdata,@newfrozennum,@oldfrozennum; ").append("\n")
/*  298 */         .append(" select  @newfrozennum=sum(t.num)  from ").append("\n")
/*  299 */         .append(" ( ").append("\n")
/*  300 */         .append(" select d.number_n as num from bill_cptfetchmain m,bill_cptfetchdetail d where d.cptfetchid=m.id and d.capitalid=@cptid and exists(select 1 from workflow_requestbase r where r.requestid=m.requestid and r.currentnodetype>0 and r.currentnodetype<3) ").append("\n")
/*  301 */         .append(" union all ").append("\n")
/*  302 */         .append(" select d.number_n as num from bill_CptAdjustMain m,bill_CptAdjustDetail d where d.cptadjustid=m.id and d.capitalid=@cptid and exists(select 1 from workflow_requestbase r where r.requestid=m.requestid and r.currentnodetype>0 and r.currentnodetype<3) ").append("\n")
/*  303 */         .append(" union all ").append("\n")
/*  304 */         .append(" select d.numbers as num from bill_Discard m,bill_discard_detail d where d.mainid=m.id and d.capitalid=@cptid and exists(select 1 from workflow_requestbase r where r.requestid=m.requestid and r.currentnodetype>0 and r.currentnodetype<3) ").append("\n")
/*  305 */         .append(" union all ").append("\n")
/*  306 */         .append(" select m.losscount as num from bill_cptloss m where m.losscpt=@cptid and exists(select 1 from workflow_requestbase r where r.requestid=m.requestid and r.currentnodetype>0 and r.currentnodetype<3) ").append("\n")
/*  307 */         .append(" union all ").append("\n")
/*  308 */         .append(" select 1 as num from bill_mendCpt m where m.cptMend=@cptid and exists(select 1 from workflow_requestbase r where r.requestid=m.requestid and r.currentnodetype>0 and r.currentnodetype<3) ").append("\n")
/*  309 */         .append(" union all ").append("\n")
/*  310 */         .append(" select 1 as num from bill_returncpt m where m.returnCpt=@cptid and exists(select 1 from workflow_requestbase r where r.requestid=m.requestid and r.currentnodetype>0 and r.currentnodetype<3) ").append("\n")
/*  311 */         .append(" union all ").append("\n")
/*  312 */         .append("  select 1 as num from bill_cptlend l where l.lendCpt=@cptid and EXISTS (select 1 from workflow_requestbase r where r.requestid=l.requestid and r.currentnodetype>0 and r.currentnodetype<3) ").append("\n")
/*  313 */         .append(stringBuffer1.toString()).append("\n")
/*  314 */         .append(" ) t ; ").append("\n")
/*  315 */         .append(" update cptcapital set frozennum=@newfrozennum where id=@cptid; ").append("\n")
/*  316 */         .append(" fetch next from my_cursor into @cptid,@isdata,@newfrozennum,@oldfrozennum; ").append("\n")
/*  317 */         .append(" end ").append("\n")
/*  318 */         .append(" CLOSE my_cursor; ").append("\n")
/*  319 */         .append(" DEALLOCATE my_cursor; ").append("\n")
/*  320 */         .append(" end ").append("\n");
/*      */     }
/*  322 */     else if ("oracle".equalsIgnoreCase(str1)) {
/*  323 */       stringBuffer2.append(" CREATE OR REPLACE TRIGGER trg_cptfrozennum_update ").append("\n")
/*  324 */         .append(" before UPDATE OF frozennum ON cptcapital ").append("\n")
/*  325 */         .append(" FOR EACH ROW ").append("\n")
/*  326 */         .append(" declare ").append("\n")
/*  327 */         .append(" i_cptid int; ").append("\n")
/*  328 */         .append(" i_isdata int; ").append("\n")
/*  329 */         .append(" i_oldfrozennum number(15,2); ").append("\n")
/*  330 */         .append(" i_newfrozennum number(15,2); ").append("\n")
/*  331 */         .append(" begin ").append("\n")
/*  332 */         .append(" i_cptid:=:new.id; ").append("\n")
/*  333 */         .append(" i_isdata:=:new.isdata; ").append("\n")
/*  334 */         .append(" i_oldfrozennum:=:old.frozennum; ").append("\n")
/*  335 */         .append(" i_newfrozennum:=:new.frozennum; ").append("\n")
/*  336 */         .append(" if i_isdata!=2 THEN RETURN; end if; ").append("\n")
/*  337 */         .append(" select  sum(t.num) into :new.frozennum  from ").append("\n")
/*  338 */         .append(" ( ").append("\n")
/*  339 */         .append(" select d.number_n as num from bill_cptfetchmain m,bill_cptfetchdetail d where d.cptfetchid=m.id and d.capitalid=i_cptid and exists(select 1 from workflow_requestbase r where r.requestid=m.requestid and r.currentnodetype>0 and r.currentnodetype<3) ").append("\n")
/*  340 */         .append(" union all ").append("\n")
/*  341 */         .append(" select d.number_n as num from bill_CptAdjustMain m,bill_CptAdjustDetail d where d.cptadjustid=m.id and d.capitalid=i_cptid and exists(select 1 from workflow_requestbase r where r.requestid=m.requestid and r.currentnodetype>0 and r.currentnodetype<3) ").append("\n")
/*  342 */         .append(" union all ").append("\n")
/*  343 */         .append(" select d.numbers as num from bill_Discard m,bill_discard_detail d where d.mainid=m.id and d.capitalid=i_cptid and exists(select 1 from workflow_requestbase r where r.requestid=m.requestid and r.currentnodetype>0 and r.currentnodetype<3) ").append("\n")
/*  344 */         .append(" union all ").append("\n")
/*  345 */         .append(" select m.losscount as num from bill_cptloss m where m.losscpt=i_cptid and exists(select 1 from workflow_requestbase r where r.requestid=m.requestid and r.currentnodetype>0 and r.currentnodetype<3) ").append("\n")
/*  346 */         .append(" union all ").append("\n")
/*  347 */         .append(" select 1 as num from bill_mendCpt m where m.cptMend=i_cptid and exists(select 1 from workflow_requestbase r where r.requestid=m.requestid and r.currentnodetype>0 and r.currentnodetype<3) ").append("\n")
/*  348 */         .append(" union all ").append("\n")
/*  349 */         .append(" select 1 as num from bill_returncpt m where m.returnCpt=i_cptid and exists(select 1 from workflow_requestbase r where r.requestid=m.requestid and r.currentnodetype>0 and r.currentnodetype<3) ").append("\n")
/*  350 */         .append(" union all ").append("\n")
/*  351 */         .append(" select 1 as num from bill_cptlend l where l.lendCpt=i_cptid and exists(select 1 from workflow_requestbase r where r.requestid=l.requestid and r.currentnodetype>0 and r.currentnodetype<3) ").append("\n")
/*  352 */         .append(stringBuffer1.toString()).append("\n")
/*  353 */         .append(" ) t ; ").append("\n")
/*  354 */         .append(" end; ").append("\n");
/*      */     }
/*  356 */     else if ("mysql".equalsIgnoreCase(str1)) {
/*  357 */       recordSet1.execute("DROP TRIGGER IF EXISTS TRG_CPTFROZENNUM_UPDATE;");
/*  358 */       stringBuffer2.append(" CREATE TRIGGER TRG_CPTFROZENNUM_UPDATE").append("\n")
/*  359 */         .append(" before UPDATE ON cptcapital").append("\n")
/*  360 */         .append(" FOR EACH ROW").append("\n")
/*  361 */         .append(" label_at_start:").append("\n")
/*  362 */         .append("  begin").append("\n")
/*  363 */         .append("  declare i_cptid INT;").append("\n")
/*  364 */         .append(" declare i_isdata INT;").append("\n")
/*  365 */         .append(" declare i_oldfrozennum DECIMAL(15,2);").append("\n")
/*  366 */         .append(" declare i_newfrozennum DECIMAL(15,2);").append("\n")
/*  367 */         .append(" set i_cptid = NEW.id;").append("\n")
/*  368 */         .append(" set i_isdata= NEW.isdata;").append("\n")
/*  369 */         .append(" set i_oldfrozennum = OLD.frozennum;").append("\n")
/*  370 */         .append(" set i_newfrozennum = NEW.frozennum;").append("\n")
/*  371 */         .append(" IF i_isdata!=2 ").append("\n")
/*  372 */         .append(" THEN LEAVE label_at_start;").append("\n")
/*  373 */         .append(" END IF;").append("\n")
/*  374 */         .append(" select  sum(t.num) into i_newfrozennum  from ").append("\n")
/*  375 */         .append(" (").append("\n")
/*  376 */         .append(" select d.number_n as num from bill_cptfetchmain m,bill_cptfetchdetail d where d.cptfetchid=m.id and d.capitalid=i_cptid and exists(select 1 from workflow_requestbase r where r.requestid=m.requestid and r.currentnodetype>0 and r.currentnodetype<3)").append("\n")
/*  377 */         .append(" union all ").append("\n")
/*  378 */         .append(" select d.number_n as num from bill_CptAdjustMain m,bill_CptAdjustDetail d where d.cptadjustid=m.id and d.capitalid=i_cptid and exists(select 1 from workflow_requestbase r where r.requestid=m.requestid and r.currentnodetype>0 and r.currentnodetype<3)").append("\n")
/*  379 */         .append(" union all ").append("\n")
/*  380 */         .append(" select d.numbers as num from bill_Discard m,bill_discard_detail d where d.mainid=m.id and d.capitalid=i_cptid and exists(select 1 from workflow_requestbase r where r.requestid=m.requestid and r.currentnodetype>0 and r.currentnodetype<3)").append("\n")
/*  381 */         .append(" union all ").append("\n")
/*  382 */         .append(" select m.losscount as num from bill_cptloss m where m.losscpt=i_cptid and exists(select 1 from workflow_requestbase r where r.requestid=m.requestid and r.currentnodetype>0 and r.currentnodetype<3)").append("\n")
/*  383 */         .append(" union all ").append("\n")
/*  384 */         .append(" select 1 as num from bill_mendCpt m where m.cptMend=i_cptid and exists(select 1 from workflow_requestbase r where r.requestid=m.requestid and r.currentnodetype>0 and r.currentnodetype<3)").append("\n")
/*  385 */         .append(" union all ").append("\n")
/*  386 */         .append(" select 1 as num from bill_returncpt m where m.returnCpt=i_cptid and exists(select 1 from workflow_requestbase r where r.requestid=m.requestid and r.currentnodetype>0 and r.currentnodetype<3)").append("\n")
/*  387 */         .append(" union all ").append("\n")
/*  388 */         .append(" select 1 as num from bill_cptlend l where l.lendCpt=i_cptid and exists(select 1 from workflow_requestbase r where r.requestid=l.requestid and r.currentnodetype>0 and r.currentnodetype<3)").append("\n")
/*  389 */         .append(stringBuffer1.toString()).append("\n")
/*  390 */         .append(" ) t ; ").append("\n")
/*  391 */         .append(" SET new.frozennum = i_newfrozennum; ").append("\n")
/*  392 */         .append(" end; ").append("\n");
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public JSONObject getCptwfInfo4mode(String paramString) throws JSONException {
/*  401 */     this.is4Mode = true;
/*  402 */     return getCptwfInfo(paramString);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public JSONObject getCptwfInfo(String paramString) throws JSONException {
/*  411 */     JSONObject jSONObject = new JSONObject();
/*  412 */     RecordSet recordSet = new RecordSet();
/*  413 */     String str1 = recordSet.getDBType();
/*  414 */     String str2 = "substring";
/*  415 */     if ("sqlserver".equalsIgnoreCase(str1)) {
/*  416 */       str2 = "substring";
/*  417 */     } else if ("oracle".equalsIgnoreCase(str1)) {
/*  418 */       str2 = "substr";
/*      */     } 
/*  420 */     String str3 = " select t1.id,t1.wftype,t1.wfid,t1.isasync,t1.actname  ,t1.sqr,t2.fieldname as sqrname," + str2 + "(REVERSE(Rtrim(t2.detailtable)),1,1) as sqrtype  ,t1.zczl,t3.fieldname as zczlname," + str2 + "(REVERSE(Rtrim(t3.detailtable)),1,1) as zczltype  ,t1.zc,t4.fieldname as zcname," + str2 + "(REVERSE(Rtrim(t4.detailtable)),1,1) as zctype  ,t1.sl,t5.fieldname as slname," + str2 + "(REVERSE(Rtrim(t5.detailtable)),1,1) as sltype  ,t1.zcz,t6.fieldname as zczname," + str2 + "(REVERSE(Rtrim(t6.detailtable)),1,1) as zcztype  ,t1.jg,t7.fieldname as jgname," + str2 + "(REVERSE(Rtrim(t7.detailtable)),1,1) as jgtype  ,t1.rq,t8.fieldname as rqname," + str2 + "(REVERSE(Rtrim(t8.detailtable)),1,1) as rqtype  ,t1.ggxh,t9.fieldname as ggxhname," + str2 + "(REVERSE(Rtrim(t9.detailtable)),1,1) as ggxhtype  ,t1.cfdd,t10.fieldname as cfddname," + str2 + "(REVERSE(Rtrim(t10.detailtable)),1,1) as cfddtype  ,t1.bz,t11.fieldname as bzname," + str2 + "(REVERSE(Rtrim(t11.detailtable)),1,1) as bztype  ,t1.wxqx,t12.fieldname as wxqxname," + str2 + "(REVERSE(Rtrim(t12.detailtable)),1,1) as wxqxtype  ,t1.wxdw,t13.fieldname as wxdwname," + str2 + "(REVERSE(Rtrim(t13.detailtable)),1,1) as wxdwtype  ,t1.cptno,t14.fieldname as cptnoname," + str2 + "(REVERSE(Rtrim(t14.detailtable)),1,1) as cptnotype  ,t1.zclx,t15.fieldname as zclxname," + str2 + "(REVERSE(Rtrim(t15.detailtable)),1,1) as zclxtype  ,t1.rkrq,t16.fieldname as rkrqname," + str2 + "(REVERSE(Rtrim(t16.detailtable)),1,1) as rkrqtype  ,t1.ssbm,t17.fieldname as ssbmname," + str2 + "(REVERSE(Rtrim(t17.detailtable)),1,1) as ssbmtype  ,t1.lysl,t18.fieldname as lyslname," + str2 + "(REVERSE(Rtrim(t18.detailtable)),1,1) as lysltype  ,t1.lysqr,t19.fieldname as lysqrname," + str2 + "(REVERSE(Rtrim(t19.detailtable)),1,1) as lysqrtype  ,t1.zcmc,t20.fieldname as zcmcname," + str2 + "(REVERSE(Rtrim(t20.detailtable)),1,1) as zcmctype  ,t1.zcbh,t21.fieldname as zcbhname," + str2 + "(REVERSE(Rtrim(t21.detailtable)),1,1) as zcbhtype  ,t1.rkck,t22.fieldname as rkckname," + str2 + "(REVERSE(Rtrim(t22.detailtable)),1,1) as rkcktype  from cpt_cptwfconf t1  left outer join workflow_billfield t2 on t2.id=t1.sqr  left outer join workflow_billfield t3 on t3.id=t1.zczl  left outer join workflow_billfield t4 on t4.id=t1.zc  left outer join workflow_billfield t5 on t5.id=t1.sl  left outer join workflow_billfield t6 on t6.id=t1.zcz  left outer join workflow_billfield t7 on t7.id=t1.jg  left outer join workflow_billfield t8 on t8.id=t1.rq  left outer join workflow_billfield t9 on t9.id=t1.ggxh  left outer join workflow_billfield t10 on t10.id=t1.cfdd  left outer join workflow_billfield t11 on t11.id=t1.bz  left outer join workflow_billfield t12 on t12.id=t1.wxqx  left outer join workflow_billfield t13 on t13.id=t1.wxdw  left outer join workflow_billfield t14 on t14.id=t1.cptno  left outer join workflow_billfield t15 on t15.id=t1.zclx  left outer join workflow_billfield t16 on t16.id=t1.rkrq  left outer join workflow_billfield t17 on t17.id=t1.ssbm  left outer join workflow_billfield t18 on t18.id=t1.lysl  left outer join workflow_billfield t19 on t19.id=t1.lysqr  left outer join workflow_billfield t20 on t20.id=t1.zcmc  left outer join workflow_billfield t21 on t21.id=t1.zcbh  left outer join workflow_billfield t22 on t22.id=t1.rkck  where t1.wfid=" + paramString;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  466 */     recordSet.writeLog("getCptwfInfo searchsql:" + str3);
/*  467 */     recordSet.execute(str3);
/*  468 */     if (recordSet.next()) {
/*  469 */       int i = 0;
/*  470 */       HashSet<String> hashSet = new HashSet();
/*      */       
/*  472 */       jSONObject.put("id", recordSet.getString("id"));
/*  473 */       jSONObject.put("wftype", recordSet.getString("wftype"));
/*  474 */       jSONObject.put("wfid", recordSet.getString("wfid"));
/*  475 */       jSONObject.put("isasync", recordSet.getString("isasync"));
/*  476 */       jSONObject.put("actname", recordSet.getString("actname"));
/*  477 */       jSONObject.put("sqr", recordSet.getString("sqr"));
/*  478 */       jSONObject.put("sqrname", recordSet.getString("sqrname"));
/*  479 */       i = Util.getIntValue(recordSet.getString("sqrtype"), 0);
/*  480 */       hashSet.add("" + i);
/*  481 */       jSONObject.put("sqrtype", i);
/*  482 */       jSONObject.put("zczl", recordSet.getString("zczl"));
/*  483 */       jSONObject.put("zczlname", recordSet.getString("zczlname"));
/*  484 */       i = Util.getIntValue(recordSet.getString("zczltype"), 0);
/*  485 */       hashSet.add("" + i);
/*  486 */       jSONObject.put("zczltype", i);
/*  487 */       jSONObject.put("zc", recordSet.getString("zc"));
/*  488 */       jSONObject.put("zcname", recordSet.getString("zcname"));
/*  489 */       i = Util.getIntValue(recordSet.getString("zctype"), 0);
/*  490 */       hashSet.add("" + i);
/*  491 */       jSONObject.put("zctype", i);
/*  492 */       jSONObject.put("sl", recordSet.getString("sl"));
/*  493 */       jSONObject.put("slname", recordSet.getString("slname"));
/*  494 */       i = Util.getIntValue(recordSet.getString("sltype"), 0);
/*  495 */       hashSet.add("" + i);
/*  496 */       jSONObject.put("sltype", i);
/*  497 */       jSONObject.put("zcz", recordSet.getString("zcz"));
/*  498 */       jSONObject.put("zczname", recordSet.getString("zczname"));
/*  499 */       i = Util.getIntValue(recordSet.getString("zcztype"), 0);
/*  500 */       hashSet.add("" + i);
/*  501 */       jSONObject.put("zcztype", i);
/*  502 */       jSONObject.put("jg", recordSet.getString("jg"));
/*  503 */       jSONObject.put("jgname", recordSet.getString("jgname"));
/*  504 */       i = Util.getIntValue(recordSet.getString("jgtype"), 0);
/*  505 */       hashSet.add("" + i);
/*  506 */       jSONObject.put("jgtype", i);
/*  507 */       jSONObject.put("rq", recordSet.getString("rq"));
/*  508 */       jSONObject.put("rqname", recordSet.getString("rqname"));
/*  509 */       i = Util.getIntValue(recordSet.getString("rqtype"), 0);
/*  510 */       hashSet.add("" + i);
/*  511 */       jSONObject.put("rqtype", i);
/*  512 */       jSONObject.put("ggxh", recordSet.getString("ggxh"));
/*  513 */       jSONObject.put("ggxhname", recordSet.getString("ggxhname"));
/*  514 */       i = Util.getIntValue(recordSet.getString("ggxhtype"), 0);
/*  515 */       hashSet.add("" + i);
/*  516 */       jSONObject.put("ggxhtype", i);
/*  517 */       jSONObject.put("cfdd", recordSet.getString("cfdd"));
/*  518 */       jSONObject.put("cfddname", recordSet.getString("cfddname"));
/*  519 */       i = Util.getIntValue(recordSet.getString("cfddtype"), 0);
/*  520 */       hashSet.add("" + i);
/*  521 */       jSONObject.put("cfddtype", i);
/*  522 */       jSONObject.put("bz", recordSet.getString("bz"));
/*  523 */       jSONObject.put("bzname", recordSet.getString("bzname"));
/*  524 */       i = Util.getIntValue(recordSet.getString("bztype"), 0);
/*  525 */       hashSet.add("" + i);
/*  526 */       jSONObject.put("bztype", i);
/*  527 */       jSONObject.put("wxqx", recordSet.getString("wxqx"));
/*  528 */       jSONObject.put("wxqxname", recordSet.getString("wxqxname"));
/*  529 */       i = Util.getIntValue(recordSet.getString("wxqxtype"), 0);
/*  530 */       hashSet.add("" + i);
/*  531 */       jSONObject.put("wxqxtype", i);
/*  532 */       jSONObject.put("wxdw", recordSet.getString("wxdw"));
/*  533 */       jSONObject.put("wxdwname", recordSet.getString("wxdwname"));
/*  534 */       i = Util.getIntValue(recordSet.getString("wxdwtype"), 0);
/*  535 */       hashSet.add("" + i);
/*  536 */       jSONObject.put("wxdwtype", i);
/*      */       
/*  538 */       i = Util.getIntValue(recordSet.getString("cptnotype"), 0);
/*  539 */       hashSet.add("" + i);
/*  540 */       jSONObject.put("cptnotype", i);
/*  541 */       jSONObject.put("cptno", recordSet.getString("cptno"));
/*  542 */       jSONObject.put("cptnoname", recordSet.getString("cptnoname"));
/*      */       
/*  544 */       i = Util.getIntValue(recordSet.getString("zclxtype"), 0);
/*  545 */       hashSet.add("" + i);
/*  546 */       jSONObject.put("zclxtype", i);
/*  547 */       jSONObject.put("zclx", recordSet.getString("zclx"));
/*  548 */       jSONObject.put("zclxname", recordSet.getString("zclxname"));
/*      */       
/*  550 */       i = Util.getIntValue(recordSet.getString("rkrqtype"), 0);
/*  551 */       hashSet.add("" + i);
/*  552 */       jSONObject.put("rkrqtype", i);
/*  553 */       jSONObject.put("rkrq", recordSet.getString("rkrq"));
/*  554 */       jSONObject.put("rkrqname", recordSet.getString("rkrqname"));
/*      */       
/*  556 */       i = Util.getIntValue(recordSet.getString("ssbmtype"), 0);
/*  557 */       hashSet.add("" + i);
/*  558 */       jSONObject.put("ssbmtype", i);
/*  559 */       jSONObject.put("ssbm", recordSet.getString("ssbm"));
/*  560 */       jSONObject.put("ssbmname", recordSet.getString("ssbmname"));
/*      */       
/*  562 */       i = Util.getIntValue(recordSet.getString("lysltype"), 0);
/*  563 */       hashSet.add("" + i);
/*  564 */       jSONObject.put("lysltype", i);
/*  565 */       jSONObject.put("lysl", recordSet.getString("lysl"));
/*  566 */       jSONObject.put("lyslname", recordSet.getString("lyslname"));
/*      */       
/*  568 */       i = Util.getIntValue(recordSet.getString("lysqrtype"), 0);
/*  569 */       hashSet.add("" + i);
/*  570 */       jSONObject.put("lysqrtype", i);
/*  571 */       jSONObject.put("lysqr", recordSet.getString("lysqr"));
/*  572 */       jSONObject.put("lysqrname", recordSet.getString("lysqrname"));
/*      */       
/*  574 */       i = Util.getIntValue(recordSet.getString("zcmctype"), 0);
/*  575 */       hashSet.add("" + i);
/*  576 */       jSONObject.put("zcmctype", i);
/*  577 */       jSONObject.put("zcmc", recordSet.getString("zcmc"));
/*  578 */       jSONObject.put("zcmcname", recordSet.getString("zcmcname"));
/*      */       
/*  580 */       i = Util.getIntValue(recordSet.getString("zcbhtype"), 0);
/*  581 */       hashSet.add("" + i);
/*  582 */       jSONObject.put("zcbhtype", i);
/*  583 */       jSONObject.put("zcbh", recordSet.getString("zcbh"));
/*  584 */       jSONObject.put("zcbhname", recordSet.getString("zcbhname"));
/*      */       
/*  586 */       i = Util.getIntValue(recordSet.getString("rkcktype"), 0);
/*  587 */       hashSet.add("" + i);
/*  588 */       jSONObject.put("rkcktype", i);
/*  589 */       jSONObject.put("rkck", recordSet.getString("rkck"));
/*  590 */       jSONObject.put("rkckname", recordSet.getString("rkckname"));
/*      */       
/*  592 */       jSONObject.put("hasdt1", hashSet.contains("1"));
/*  593 */       jSONObject.put("hasdt2", hashSet.contains("2"));
/*  594 */       jSONObject.put("hasdt3", hashSet.contains("3"));
/*  595 */       jSONObject.put("hasdt4", hashSet.contains("4"));
/*      */     } 
/*  597 */     return jSONObject;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getWftype(String paramString) {
/*  605 */     String str = "";
/*  606 */     RecordSet recordSet = new RecordSet();
/*  607 */     if ("mysql".equals(recordSet.getDBType()) || "sqlserver".equals(recordSet.getDBType())) {
/*  608 */       recordSet.execute("select wftype  from cpt_cptwfconf where wfid=" + paramString + " and actname != '' and actname is not null");
/*      */     } else {
/*  610 */       recordSet.execute("select wftype  from cpt_cptwfconf where wfid=" + paramString + " and actname is not null");
/*      */     } 
/*  612 */     if (recordSet.next()) {
/*  613 */       str = Util.null2String(recordSet.getString("wftype"));
/*      */     }
/*  615 */     return str;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getAllCptWftype(String paramString) {
/*  624 */     String str = "";
/*  625 */     RecordSet recordSet = new RecordSet();
/*  626 */     recordSet.execute("select wftype from cpt_cptwfconf where wfid=" + paramString);
/*  627 */     if (recordSet.next()) {
/*  628 */       str = Util.null2String(recordSet.getString("wftype"));
/*      */     }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  635 */     return str;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public Map<String, JSONObject> getcusField(String paramString1, String paramString2) {
/*  645 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  646 */     RecordSet recordSet = new RecordSet();
/*  647 */     String str1 = "substring";
/*  648 */     if ("sqlserver".equalsIgnoreCase(recordSet.getDBType())) {
/*  649 */       str1 = "substring";
/*  650 */     } else if ("oracle".equalsIgnoreCase(recordSet.getDBType())) {
/*  651 */       str1 = "substr";
/*  652 */     } else if ("mysql".equalsIgnoreCase(recordSet.getDBType())) {
/*  653 */       str1 = "substring";
/*      */     } 
/*  655 */     String str2 = "SELECT a.fieldname,c.detailtable,c.viewtype,c.fieldname AS wfname,c.type,c.fieldhtmltype,c.fielddbtype FROM cpt_cptwffieldmap a LEFT JOIN cpt_cptwfconf b ON a.mainid = b.id LEFT JOIN workflow_billfield c ON a.fieldid = c.id WHERE b.wfid=" + paramString1;
/*  656 */     if ("0".equals(paramString2)) {
/*  657 */       str2 = str2 + " and c.viewtype=0";
/*      */     } else {
/*  659 */       str2 = str2 + " and " + str1 + "(REVERSE(Rtrim(c.detailtable)),1,1)='" + paramString2 + "'";
/*      */     } 
/*  661 */     recordSet.execute(str2);
/*  662 */     while (recordSet.next()) {
/*  663 */       String str3 = Util.null2String(recordSet.getString("fieldname"));
/*  664 */       String str4 = Util.null2String(recordSet.getString("type"));
/*  665 */       String str5 = Util.null2String(recordSet.getString("fieldhtmltype"));
/*  666 */       String str6 = Util.null2String(recordSet.getString("fielddbtype"));
/*  667 */       String str7 = Util.null2String(recordSet.getString("wfname"));
/*  668 */       JSONObject jSONObject = new JSONObject();
/*      */       try {
/*  670 */         jSONObject.put("type", str4);
/*  671 */         jSONObject.put("fieldhtmltype", str5);
/*  672 */         jSONObject.put("fielddbtype", str6);
/*  673 */         jSONObject.put("fieldname", str7);
/*  674 */         hashMap.put(str3, jSONObject);
/*  675 */       } catch (JSONException jSONException) {
/*  676 */         jSONException.printStackTrace();
/*      */       } 
/*      */     } 
/*  679 */     return (Map)hashMap;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void updateCptFieldOfWF(Map<String, JSONObject> paramMap1, Map<String, String> paramMap, Map<String, JSONObject> paramMap2, JSONObject paramJSONObject, String paramString, int paramInt) {
/*  691 */     if ("".equals(Util.null2String(paramString))) {
/*      */       return;
/*      */     }
/*  694 */     CodeUtil codeUtil = new CodeUtil();
/*  695 */     String str = codeUtil.getCptData2CodeUse();
/*      */     try {
/*  697 */       RecordSet recordSet = new RecordSet();
/*  698 */       String str1 = "";
/*  699 */       ArrayList<String> arrayList = new ArrayList();
/*      */ 
/*      */       
/*  702 */       if (paramMap1 != null && paramMap1.size() > 0) {
/*  703 */         Iterator<Map.Entry> iterator = paramMap1.entrySet().iterator();
/*  704 */         RecordSet recordSet1 = new RecordSet();
/*  705 */         RecordSet recordSet2 = new RecordSet();
/*  706 */         CapitalTransMethod capitalTransMethod = new CapitalTransMethod();
/*  707 */         char c = Util.getSeparator();
/*  708 */         String str2 = DateHelper.getCurrentDate();
/*  709 */         recordSet1.executeQuery("select * from CptCapital where id=? and isdata=2", new Object[] { paramString });
/*  710 */         if (!recordSet1.next()) {
/*      */           return;
/*      */         }
/*  713 */         while (iterator.hasNext()) {
/*  714 */           Map.Entry entry = iterator.next();
/*  715 */           String str3 = (String)entry.getKey();
/*  716 */           JSONObject jSONObject = (JSONObject)entry.getValue();
/*  717 */           int i = Util.getIntValue(jSONObject.getString("type"));
/*  718 */           int j = Util.getIntValue(jSONObject.getString("fieldhtmltype"));
/*  719 */           String str4 = Util.null2String(jSONObject.getString("fielddbtype"));
/*      */           
/*  721 */           String str5 = str3;
/*  722 */           String str6 = Util.null2String(paramMap.get(str5));
/*      */           
/*  724 */           if ("".equals(str6)) {
/*      */             continue;
/*      */           }
/*  727 */           if ("mark".equals(str5) && "1".equals(str)) {
/*      */             continue;
/*      */           }
/*      */           
/*  731 */           String str7 = Util.null2String(recordSet1.getString(str5));
/*  732 */           String str8 = "";
/*  733 */           String str9 = "";
/*  734 */           if (!str6.equals(str7)) {
/*  735 */             recordSet.executeQuery("select id from cptDefineField where fieldname=?", new Object[] { str5 });
/*  736 */             if (recordSet.next()) {
/*  737 */               str8 = Util.null2String(recordSet.getString("id"));
/*      */             }
/*  739 */             str9 = paramString;
/*  740 */             str9 = str9 + c + str5;
/*  741 */             str9 = str9 + c + capitalTransMethod.getFieldValueByType(str7, j, i, str4, str8);
/*  742 */             str9 = str9 + c + capitalTransMethod.getFieldValueByType(str6, j, i, str4, str8);
/*  743 */             str9 = str9 + c + "" + paramInt;
/*  744 */             str9 = str9 + c + str2;
/*  745 */             recordSet.executeProc("CptCapitalModify_Insert", str9);
/*      */           } 
/*      */ 
/*      */           
/*  749 */           recordSet.execute("select sptcount from CptCapital where isdata = 2 and id =" + paramString);
/*  750 */           int k = 0;
/*  751 */           if (recordSet.next()) {
/*  752 */             k = recordSet.getInt("sptcount");
/*      */           }
/*  754 */           if ("alertnum".equals(str5) && k == 1) {
/*      */             continue;
/*      */           }
/*      */           
/*  758 */           if ("oracle".equalsIgnoreCase(recordSet.getDBType())) {
/*  759 */             if (str4.toUpperCase().indexOf("INT") >= 0) {
/*  760 */               if (!Util.null2String(str6).equals("")) {
/*  761 */                 str1 = str1 + str5 + " = " + Util.getIntValue(str6) + ","; continue;
/*      */               } 
/*  763 */               str1 = str1 + str5 + " = NULL,"; continue;
/*      */             } 
/*  765 */             if (str4.toUpperCase().indexOf("NUMBER") >= 0 || str4.toUpperCase().indexOf("FLOAT") >= 0 || str4.toUpperCase().indexOf("DECIMAL") >= 0) {
/*  766 */               int m = str4.indexOf(",");
/*  767 */               int n = 2;
/*  768 */               if (m > -1) {
/*  769 */                 n = Util.getIntValue(str4.substring(m + 1, str4.length() - 1).trim(), 2);
/*      */               } else {
/*  771 */                 n = 2;
/*      */               } 
/*  773 */               if (!Util.null2String(str6).equals("")) {
/*  774 */                 str1 = str1 + str5 + " = " + Util.getPointValue2(str6, n) + ",";
/*      */                 continue;
/*      */               } 
/*  777 */               str1 = str1 + str5 + " = NULL,";
/*      */               continue;
/*      */             } 
/*  780 */             String str11 = "";
/*  781 */             if (j == 3 && (i == 161 || i == 162)) {
/*  782 */               str11 = Util.null2String(str6);
/*  783 */               str11 = str11.trim();
/*      */             }
/*  785 */             else if (j == 2 && i == 2) {
/*  786 */               str11 = Util.toHtml100(str6);
/*  787 */               str11 = StringHelper.convertSpecialChar2Html(str11);
/*  788 */             } else if (j == 1 && i == 1) {
/*  789 */               str11 = StringHelper.convertSpecialChar2Html(str6);
/*  790 */               str11 = Util.toHtmlForWorkflow(str11);
/*  791 */             } else if (j == 2 && i == 1) {
/*  792 */               str11 = Util.StringReplace(str6, " ", "&nbsp;");
/*  793 */               str11 = StringHelper.convertSpecialChar2Html(str11);
/*  794 */               str11 = Util.toHtmlForWorkflowForMode(str11);
/*      */             } else {
/*  796 */               str11 = Util.StringReplace(Util.toHtml10(str6), " ", "&nbsp;");
/*  797 */               str11 = StringHelper.convertSpecialChar2Html(str11);
/*  798 */               str11 = Util.toHtmlForWorkflow(str11);
/*      */             } 
/*      */             
/*  801 */             str11 = Util.StringReplace(str11, "weaver2017", "+");
/*      */             
/*  803 */             if (2 == j || FormModeBrowserUtil.isMultiBrowser("" + j, "" + i)) {
/*  804 */               str1 = str1 + str5 + " = ?,";
/*  805 */               arrayList.add(str11); continue;
/*      */             } 
/*  807 */             str1 = str1 + str5 + " = '" + str11 + "',";
/*      */             
/*      */             continue;
/*      */           } 
/*      */           
/*  812 */           if (str4.toUpperCase().indexOf("INT") >= 0) {
/*  813 */             if (!"".equals(str6)) {
/*  814 */               str1 = str1 + str5 + " = " + Util.getIntValue(str6) + ","; continue;
/*      */             } 
/*  816 */             str1 = str1 + str5 + " = NULL,"; continue;
/*      */           } 
/*  818 */           if (str4.toUpperCase().indexOf("DECIMAL") >= 0 || str4.toUpperCase().indexOf("FLOAT") >= 0) {
/*  819 */             int m = str4.indexOf(",");
/*  820 */             int n = 2;
/*  821 */             if (m > -1) {
/*  822 */               n = Util.getIntValue(str4.substring(m + 1, str4.length() - 1).trim(), 2);
/*      */             } else {
/*  824 */               n = 2;
/*      */             } 
/*  826 */             if (!"".equals(str6)) {
/*  827 */               str1 = str1 + str5 + " = " + Util.getPointValue2(str6, n) + ","; continue;
/*      */             } 
/*  829 */             str1 = str1 + str5 + " = NULL,";
/*      */             continue;
/*      */           } 
/*  832 */           String str10 = "";
/*  833 */           if (j == 2 && i == 2) {
/*      */             
/*  835 */             str10 = StringHelper.convertSpecialChar2Html(str6);
/*  836 */             str10 = Util.toHtml100(str10);
/*  837 */           } else if (j == 1 && i == 1) {
/*  838 */             str10 = StringHelper.convertSpecialChar2Html(str6);
/*  839 */             str10 = Util.toHtmlForWorkflow(str10);
/*  840 */           } else if (j == 2 && i == 1) {
/*  841 */             str10 = Util.StringReplace(str6, " ", "&nbsp;");
/*  842 */             str10 = StringHelper.convertSpecialChar2Html(str10);
/*  843 */             str10 = Util.toHtmlForWorkflowForMode(str10);
/*  844 */           } else if (j == 4 && i == 1) {
/*  845 */             str10 = Util.StringReplace(str6, " ", "&nbsp;");
/*  846 */             str10 = Util.toHtmlForWorkflow(str10);
/*  847 */             if (str10.equals("")) {
/*  848 */               str10 = "0";
/*      */             }
/*      */           } else {
/*  851 */             str10 = Util.StringReplace(Util.toHtml10(str6), " ", "&nbsp;");
/*  852 */             str10 = StringHelper.convertSpecialChar2Html(str10);
/*  853 */             str10 = Util.toHtmlForWorkflow(str10);
/*      */           } 
/*  855 */           str10 = Util.StringReplace(str10, "weaver2017", "+");
/*      */           
/*  857 */           if (2 == j || FormModeBrowserUtil.isMultiBrowser("" + j, "" + i)) {
/*  858 */             str1 = str1 + str5 + " = ?,";
/*  859 */             arrayList.add(str10); continue;
/*      */           } 
/*  861 */           str1 = str1 + str5 + " = '" + str10 + "',";
/*      */         } 
/*      */       } 
/*      */ 
/*      */ 
/*      */       
/*  867 */       if (paramMap2 != null && paramMap2.size() > 0) {
/*  868 */         Iterator<Map.Entry> iterator = paramMap2.entrySet().iterator();
/*  869 */         RecordSet recordSet1 = new RecordSet();
/*  870 */         RecordSet recordSet2 = new RecordSet();
/*  871 */         CapitalTransMethod capitalTransMethod = new CapitalTransMethod();
/*  872 */         char c = Util.getSeparator();
/*  873 */         String str2 = DateHelper.getCurrentDate();
/*  874 */         recordSet1.executeQuery("select * from CptCapital where id=? and isdata=2", new Object[] { paramString });
/*  875 */         if (!recordSet1.next()) {
/*      */           return;
/*      */         }
/*  878 */         while (iterator.hasNext()) {
/*  879 */           Map.Entry entry = iterator.next();
/*  880 */           String str3 = (String)entry.getKey();
/*  881 */           JSONObject jSONObject = (JSONObject)entry.getValue();
/*  882 */           int i = Util.getIntValue(jSONObject.getString("type"));
/*  883 */           int j = Util.getIntValue(jSONObject.getString("fieldhtmltype"));
/*  884 */           String str4 = Util.null2String(jSONObject.getString("fielddbtype"));
/*      */           
/*  886 */           String str5 = str3;
/*  887 */           String str6 = Util.null2String(paramJSONObject.get(str5));
/*      */           
/*  889 */           if ("".equals(str6)) {
/*      */             continue;
/*      */           }
/*  892 */           if ("mark".equals(str5) && "1".equals(str)) {
/*      */             continue;
/*      */           }
/*      */ 
/*      */           
/*  897 */           recordSet.execute("select sptcount from CptCapital where isdata = 2 and id =" + paramString);
/*  898 */           int k = 0;
/*  899 */           if (recordSet.next()) {
/*  900 */             k = recordSet.getInt("sptcount");
/*      */           }
/*  902 */           if ("alertnum".equals(str5) && k == 1) {
/*      */             continue;
/*      */           }
/*      */           
/*  906 */           String str7 = Util.null2String(recordSet1.getString(str5));
/*  907 */           String str8 = "";
/*  908 */           String str9 = "";
/*  909 */           if (!str6.equals(str7)) {
/*  910 */             recordSet.executeQuery("select id from cptDefineField where fieldname=?", new Object[] { str5 });
/*  911 */             if (recordSet.next()) {
/*  912 */               str8 = Util.null2String(recordSet.getString("id"));
/*      */             }
/*  914 */             str9 = paramString;
/*  915 */             str9 = str9 + c + str5;
/*  916 */             str9 = str9 + c + capitalTransMethod.getFieldValueByType(str7, j, i, str4, str8);
/*  917 */             str9 = str9 + c + capitalTransMethod.getFieldValueByType(str6, j, i, str4, str8);
/*  918 */             str9 = str9 + c + "" + paramInt;
/*  919 */             str9 = str9 + c + str2;
/*  920 */             recordSet.executeProc("CptCapitalModify_Insert", str9);
/*      */           } 
/*      */           
/*  923 */           if ("oracle".equalsIgnoreCase(recordSet.getDBType())) {
/*  924 */             if (str4.toUpperCase().indexOf("INT") >= 0) {
/*  925 */               if (!Util.null2String(str6).equals("")) {
/*  926 */                 str1 = str1 + str5 + " = " + Util.getIntValue(str6) + ","; continue;
/*      */               } 
/*  928 */               str1 = str1 + str5 + " = NULL,"; continue;
/*      */             } 
/*  930 */             if (str4.toUpperCase().indexOf("NUMBER") >= 0 || str4.toUpperCase().indexOf("FLOAT") >= 0 || str4.toUpperCase().indexOf("DECIMAL") >= 0) {
/*  931 */               int m = str4.indexOf(",");
/*  932 */               int n = 2;
/*  933 */               if (m > -1) {
/*  934 */                 n = Util.getIntValue(str4.substring(m + 1, str4.length() - 1).trim(), 2);
/*      */               } else {
/*  936 */                 n = 2;
/*      */               } 
/*  938 */               if (!Util.null2String(str6).equals("")) {
/*  939 */                 str1 = str1 + str5 + " = " + Util.getPointValue2(str6, n) + ",";
/*      */                 continue;
/*      */               } 
/*  942 */               str1 = str1 + str5 + " = NULL,";
/*      */               continue;
/*      */             } 
/*  945 */             String str11 = "";
/*  946 */             if (j == 3 && (i == 161 || i == 162)) {
/*  947 */               str11 = Util.null2String(str6);
/*  948 */               str11 = str11.trim();
/*      */             }
/*  950 */             else if (j == 2 && i == 2) {
/*  951 */               str11 = Util.toHtml100(str6);
/*  952 */               str11 = StringHelper.convertSpecialChar2Html(str11);
/*  953 */             } else if (j == 1 && i == 1) {
/*  954 */               str11 = StringHelper.convertSpecialChar2Html(str6);
/*  955 */               str11 = Util.toHtmlForWorkflow(str11);
/*  956 */             } else if (j == 2 && i == 1) {
/*  957 */               str11 = Util.StringReplace(str6, " ", "&nbsp;");
/*  958 */               str11 = StringHelper.convertSpecialChar2Html(str11);
/*  959 */               str11 = Util.toHtmlForWorkflowForMode(str11);
/*      */             } else {
/*  961 */               str11 = Util.StringReplace(Util.toHtml10(str6), " ", "&nbsp;");
/*  962 */               str11 = StringHelper.convertSpecialChar2Html(str11);
/*  963 */               str11 = Util.toHtmlForWorkflow(str11);
/*      */             } 
/*      */             
/*  966 */             str11 = Util.StringReplace(str11, "weaver2017", "+");
/*      */             
/*  968 */             if (2 == j || FormModeBrowserUtil.isMultiBrowser("" + j, "" + i)) {
/*  969 */               str1 = str1 + str5 + " = ?,";
/*  970 */               arrayList.add(str11); continue;
/*      */             } 
/*  972 */             str1 = str1 + str5 + " = '" + str11 + "',";
/*      */             
/*      */             continue;
/*      */           } 
/*      */           
/*  977 */           if (str4.toUpperCase().indexOf("INT") >= 0) {
/*  978 */             if (!"".equals(str6)) {
/*  979 */               str1 = str1 + str5 + " = " + Util.getIntValue(str6) + ","; continue;
/*      */             } 
/*  981 */             str1 = str1 + str5 + " = NULL,"; continue;
/*      */           } 
/*  983 */           if (str4.toUpperCase().indexOf("DECIMAL") >= 0 || str4.toUpperCase().indexOf("FLOAT") >= 0) {
/*  984 */             int m = str4.indexOf(",");
/*  985 */             int n = 2;
/*  986 */             if (m > -1) {
/*  987 */               n = Util.getIntValue(str4.substring(m + 1, str4.length() - 1).trim(), 2);
/*      */             } else {
/*  989 */               n = 2;
/*      */             } 
/*  991 */             if (!"".equals(str6)) {
/*  992 */               str1 = str1 + str5 + " = " + Util.getPointValue2(str6, n) + ","; continue;
/*      */             } 
/*  994 */             str1 = str1 + str5 + " = NULL,";
/*      */             continue;
/*      */           } 
/*  997 */           String str10 = "";
/*  998 */           if (j == 2 && i == 2) {
/*  999 */             str10 = StringHelper.convertSpecialChar2Html(str6);
/* 1000 */             str10 = Util.toHtml100(str10);
/* 1001 */           } else if (j == 1 && i == 1) {
/* 1002 */             str10 = StringHelper.convertSpecialChar2Html(str6);
/* 1003 */             str10 = Util.toHtmlForWorkflow(str10);
/* 1004 */           } else if (j == 2 && i == 1) {
/* 1005 */             str10 = Util.StringReplace(str6, " ", "&nbsp;");
/* 1006 */             str10 = StringHelper.convertSpecialChar2Html(str10);
/* 1007 */             str10 = Util.toHtmlForWorkflowForMode(str10);
/* 1008 */           } else if (j == 4 && i == 1) {
/* 1009 */             str10 = Util.StringReplace(str6, " ", "&nbsp;");
/* 1010 */             str10 = Util.toHtmlForWorkflow(str10);
/* 1011 */             if (str10.equals("")) {
/* 1012 */               str10 = "0";
/*      */             }
/*      */           } else {
/* 1015 */             str10 = Util.StringReplace(Util.toHtml10(str6), " ", "&nbsp;");
/* 1016 */             str10 = StringHelper.convertSpecialChar2Html(str10);
/* 1017 */             str10 = Util.toHtmlForWorkflow(str10);
/*      */           } 
/* 1019 */           str10 = Util.StringReplace(str10, "weaver2017", "+");
/*      */           
/* 1021 */           if (2 == j || FormModeBrowserUtil.isMultiBrowser("" + j, "" + i)) {
/* 1022 */             str1 = str1 + str5 + " = ?,";
/* 1023 */             arrayList.add(str10); continue;
/*      */           } 
/* 1025 */           str1 = str1 + str5 + " = '" + str10 + "',";
/*      */         } 
/*      */       } 
/*      */ 
/*      */ 
/*      */       
/* 1031 */       if (!str1.equals("")) {
/* 1032 */         str1 = str1.substring(0, str1.length() - 1);
/* 1033 */         str1 = "update cptcapital set  " + str1 + " where id = " + paramString;
/* 1034 */         Object[] arrayOfObject = new Object[arrayList.size()];
/* 1035 */         for (byte b = 0; b < arrayList.size(); b++) {
/* 1036 */           arrayOfObject[b] = arrayList.get(b);
/*      */         }
/*      */         
/* 1039 */         recordSet.executeSql(str1, false, arrayOfObject);
/*      */       } 
/* 1041 */     } catch (Exception exception) {
/* 1042 */       exception.printStackTrace();
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void updateCptFieldOfWF(Map<String, JSONObject> paramMap1, Map<String, String> paramMap, Map<String, JSONObject> paramMap2, JSONObject paramJSONObject, String paramString, int paramInt, Boolean paramBoolean) {
/* 1055 */     if ("".equals(Util.null2String(paramString))) {
/*      */       return;
/*      */     }
/* 1058 */     CodeUtil codeUtil = new CodeUtil();
/* 1059 */     String str = codeUtil.getCptData2CodeUse();
/*      */     try {
/* 1061 */       RecordSet recordSet = new RecordSet();
/* 1062 */       String str1 = "";
/* 1063 */       ArrayList<String> arrayList = new ArrayList();
/*      */ 
/*      */       
/* 1066 */       if (paramMap1 != null && paramMap1.size() > 0) {
/* 1067 */         Iterator<Map.Entry> iterator = paramMap1.entrySet().iterator();
/* 1068 */         RecordSet recordSet1 = new RecordSet();
/* 1069 */         RecordSet recordSet2 = new RecordSet();
/* 1070 */         CapitalTransMethod capitalTransMethod = new CapitalTransMethod();
/* 1071 */         char c = Util.getSeparator();
/* 1072 */         String str2 = DateHelper.getCurrentDate();
/* 1073 */         recordSet1.executeQuery("select * from CptCapital where id=? and isdata=2", new Object[] { paramString });
/* 1074 */         if (!recordSet1.next()) {
/*      */           return;
/*      */         }
/* 1077 */         while (iterator.hasNext()) {
/* 1078 */           Map.Entry entry = iterator.next();
/* 1079 */           String str3 = (String)entry.getKey();
/* 1080 */           JSONObject jSONObject = (JSONObject)entry.getValue();
/* 1081 */           int i = Util.getIntValue(jSONObject.getString("type"));
/* 1082 */           int j = Util.getIntValue(jSONObject.getString("fieldhtmltype"));
/* 1083 */           String str4 = Util.null2String(jSONObject.getString("fielddbtype"));
/*      */           
/* 1085 */           String str5 = str3;
/* 1086 */           String str6 = Util.null2String(paramMap.get(str5));
/*      */           
/* 1088 */           if ("".equals(str6)) {
/*      */             continue;
/*      */           }
/* 1091 */           if ("mark".equals(str5) && "1".equals(str)) {
/*      */             continue;
/*      */           }
/*      */           
/* 1095 */           String str7 = Util.null2String(recordSet1.getString(str5));
/* 1096 */           String str8 = "";
/* 1097 */           String str9 = "";
/* 1098 */           if (paramBoolean.booleanValue() && !str6.equals(str7)) {
/* 1099 */             recordSet.executeQuery("select id from cptDefineField where fieldname=?", new Object[] { str5 });
/* 1100 */             if (recordSet.next()) {
/* 1101 */               str8 = Util.null2String(recordSet.getString("id"));
/*      */             }
/* 1103 */             str9 = paramString;
/* 1104 */             str9 = str9 + c + str5;
/* 1105 */             str9 = str9 + c + capitalTransMethod.getFieldValueByType(str7, j, i, str4, str8);
/* 1106 */             str9 = str9 + c + capitalTransMethod.getFieldValueByType(str6, j, i, str4, str8);
/* 1107 */             str9 = str9 + c + "" + paramInt;
/* 1108 */             str9 = str9 + c + str2;
/* 1109 */             recordSet.executeProc("CptCapitalModify_Insert", str9);
/*      */           } 
/*      */ 
/*      */           
/* 1113 */           recordSet.execute("select sptcount from CptCapital where isdata = 2 and id =" + paramString);
/* 1114 */           int k = 0;
/* 1115 */           if (recordSet.next()) {
/* 1116 */             k = recordSet.getInt("sptcount");
/*      */           }
/* 1118 */           if ("alertnum".equals(str5) && k == 1) {
/*      */             continue;
/*      */           }
/*      */           
/* 1122 */           if ("oracle".equalsIgnoreCase(recordSet.getDBType())) {
/* 1123 */             if (str4.toUpperCase().indexOf("INT") >= 0) {
/* 1124 */               if (!Util.null2String(str6).equals("")) {
/* 1125 */                 str1 = str1 + str5 + " = " + Util.getIntValue(str6) + ","; continue;
/*      */               } 
/* 1127 */               str1 = str1 + str5 + " = NULL,"; continue;
/*      */             } 
/* 1129 */             if (str4.toUpperCase().indexOf("NUMBER") >= 0 || str4.toUpperCase().indexOf("FLOAT") >= 0 || str4.toUpperCase().indexOf("DECIMAL") >= 0) {
/* 1130 */               int m = str4.indexOf(",");
/* 1131 */               int n = 2;
/* 1132 */               if (m > -1) {
/* 1133 */                 n = Util.getIntValue(str4.substring(m + 1, str4.length() - 1).trim(), 2);
/*      */               } else {
/* 1135 */                 n = 2;
/*      */               } 
/* 1137 */               if (!Util.null2String(str6).equals("")) {
/* 1138 */                 str1 = str1 + str5 + " = " + Util.getPointValue2(str6, n) + ",";
/*      */                 continue;
/*      */               } 
/* 1141 */               str1 = str1 + str5 + " = NULL,";
/*      */               continue;
/*      */             } 
/* 1144 */             String str11 = "";
/* 1145 */             if (j == 3 && (i == 161 || i == 162)) {
/* 1146 */               str11 = Util.null2String(str6);
/* 1147 */               str11 = str11.trim();
/*      */             }
/* 1149 */             else if (j == 2 && i == 2) {
/* 1150 */               str11 = Util.toHtml100(str6);
/* 1151 */               str11 = StringHelper.convertSpecialChar2Html(str11);
/* 1152 */             } else if (j == 1 && i == 1) {
/* 1153 */               str11 = StringHelper.convertSpecialChar2Html(str6);
/* 1154 */               str11 = Util.toHtmlForWorkflow(str11);
/* 1155 */             } else if (j == 2 && i == 1) {
/* 1156 */               str11 = Util.StringReplace(str6, " ", "&nbsp;");
/* 1157 */               str11 = StringHelper.convertSpecialChar2Html(str11);
/* 1158 */               str11 = Util.toHtmlForWorkflowForMode(str11);
/*      */             } else {
/* 1160 */               str11 = Util.StringReplace(Util.toHtml10(str6), " ", "&nbsp;");
/* 1161 */               str11 = StringHelper.convertSpecialChar2Html(str11);
/* 1162 */               str11 = Util.toHtmlForWorkflow(str11);
/*      */             } 
/*      */             
/* 1165 */             str11 = Util.StringReplace(str11, "weaver2017", "+");
/*      */             
/* 1167 */             if (2 == j || FormModeBrowserUtil.isMultiBrowser("" + j, "" + i)) {
/* 1168 */               str1 = str1 + str5 + " = ?,";
/* 1169 */               arrayList.add(str11); continue;
/*      */             } 
/* 1171 */             str1 = str1 + str5 + " = '" + str11 + "',";
/*      */             
/*      */             continue;
/*      */           } 
/*      */           
/* 1176 */           if (str4.toUpperCase().indexOf("INT") >= 0) {
/* 1177 */             if (!"".equals(str6)) {
/* 1178 */               str1 = str1 + str5 + " = " + Util.getIntValue(str6) + ","; continue;
/*      */             } 
/* 1180 */             str1 = str1 + str5 + " = NULL,"; continue;
/*      */           } 
/* 1182 */           if (str4.toUpperCase().indexOf("DECIMAL") >= 0 || str4.toUpperCase().indexOf("FLOAT") >= 0) {
/* 1183 */             int m = str4.indexOf(",");
/* 1184 */             int n = 2;
/* 1185 */             if (m > -1) {
/* 1186 */               n = Util.getIntValue(str4.substring(m + 1, str4.length() - 1).trim(), 2);
/*      */             } else {
/* 1188 */               n = 2;
/*      */             } 
/* 1190 */             if (!"".equals(str6)) {
/* 1191 */               str1 = str1 + str5 + " = " + Util.getPointValue2(str6, n) + ","; continue;
/*      */             } 
/* 1193 */             str1 = str1 + str5 + " = NULL,";
/*      */             continue;
/*      */           } 
/* 1196 */           String str10 = "";
/* 1197 */           if (j == 2 && i == 2) {
/*      */             
/* 1199 */             str10 = StringHelper.convertSpecialChar2Html(str6);
/* 1200 */             str10 = Util.toHtml100(str10);
/* 1201 */           } else if (j == 1 && i == 1) {
/* 1202 */             str10 = StringHelper.convertSpecialChar2Html(str6);
/* 1203 */             str10 = Util.toHtmlForWorkflow(str10);
/* 1204 */           } else if (j == 2 && i == 1) {
/* 1205 */             str10 = Util.StringReplace(str6, " ", "&nbsp;");
/* 1206 */             str10 = StringHelper.convertSpecialChar2Html(str10);
/* 1207 */             str10 = Util.toHtmlForWorkflowForMode(str10);
/* 1208 */           } else if (j == 4 && i == 1) {
/* 1209 */             str10 = Util.StringReplace(str6, " ", "&nbsp;");
/* 1210 */             str10 = Util.toHtmlForWorkflow(str10);
/* 1211 */             if (str10.equals("")) {
/* 1212 */               str10 = "0";
/*      */             }
/*      */           } else {
/* 1215 */             str10 = Util.StringReplace(Util.toHtml10(str6), " ", "&nbsp;");
/* 1216 */             str10 = StringHelper.convertSpecialChar2Html(str10);
/* 1217 */             str10 = Util.toHtmlForWorkflow(str10);
/*      */           } 
/* 1219 */           str10 = Util.StringReplace(str10, "weaver2017", "+");
/*      */           
/* 1221 */           if (2 == j || FormModeBrowserUtil.isMultiBrowser("" + j, "" + i)) {
/* 1222 */             str1 = str1 + str5 + " = ?,";
/* 1223 */             arrayList.add(str10); continue;
/*      */           } 
/* 1225 */           str1 = str1 + str5 + " = '" + str10 + "',";
/*      */         } 
/*      */       } 
/*      */ 
/*      */ 
/*      */       
/* 1231 */       if (paramMap2 != null && paramMap2.size() > 0) {
/* 1232 */         Iterator<Map.Entry> iterator = paramMap2.entrySet().iterator();
/* 1233 */         RecordSet recordSet1 = new RecordSet();
/* 1234 */         RecordSet recordSet2 = new RecordSet();
/* 1235 */         CapitalTransMethod capitalTransMethod = new CapitalTransMethod();
/* 1236 */         char c = Util.getSeparator();
/* 1237 */         String str2 = DateHelper.getCurrentDate();
/* 1238 */         recordSet1.executeQuery("select * from CptCapital where id=? and isdata=2", new Object[] { paramString });
/* 1239 */         if (!recordSet1.next()) {
/*      */           return;
/*      */         }
/* 1242 */         while (iterator.hasNext()) {
/* 1243 */           Map.Entry entry = iterator.next();
/* 1244 */           String str3 = (String)entry.getKey();
/* 1245 */           JSONObject jSONObject = (JSONObject)entry.getValue();
/* 1246 */           int i = Util.getIntValue(jSONObject.getString("type"));
/* 1247 */           int j = Util.getIntValue(jSONObject.getString("fieldhtmltype"));
/* 1248 */           String str4 = Util.null2String(jSONObject.getString("fielddbtype"));
/*      */           
/* 1250 */           String str5 = str3;
/* 1251 */           String str6 = Util.null2String(paramJSONObject.get(str5));
/*      */           
/* 1253 */           if ("".equals(str6)) {
/*      */             continue;
/*      */           }
/* 1256 */           if ("mark".equals(str5) && "1".equals(str)) {
/*      */             continue;
/*      */           }
/*      */ 
/*      */           
/* 1261 */           recordSet.execute("select sptcount from CptCapital where isdata = 2 and id =" + paramString);
/* 1262 */           int k = 0;
/* 1263 */           if (recordSet.next()) {
/* 1264 */             k = recordSet.getInt("sptcount");
/*      */           }
/* 1266 */           if ("alertnum".equals(str5) && k == 1) {
/*      */             continue;
/*      */           }
/*      */           
/* 1270 */           String str7 = Util.null2String(recordSet1.getString(str5));
/* 1271 */           String str8 = "";
/* 1272 */           String str9 = "";
/* 1273 */           if (!str6.equals(str7)) {
/* 1274 */             recordSet.executeQuery("select id from cptDefineField where fieldname=?", new Object[] { str5 });
/* 1275 */             if (recordSet.next()) {
/* 1276 */               str8 = Util.null2String(recordSet.getString("id"));
/*      */             }
/* 1278 */             str9 = paramString;
/* 1279 */             str9 = str9 + c + str5;
/* 1280 */             str9 = str9 + c + capitalTransMethod.getFieldValueByType(str7, j, i, str4, str8);
/* 1281 */             str9 = str9 + c + capitalTransMethod.getFieldValueByType(str6, j, i, str4, str8);
/* 1282 */             str9 = str9 + c + "" + paramInt;
/* 1283 */             str9 = str9 + c + str2;
/* 1284 */             recordSet.executeProc("CptCapitalModify_Insert", str9);
/*      */           } 
/*      */           
/* 1287 */           if ("oracle".equalsIgnoreCase(recordSet.getDBType())) {
/* 1288 */             if (str4.toUpperCase().indexOf("INT") >= 0) {
/* 1289 */               if (!Util.null2String(str6).equals("")) {
/* 1290 */                 str1 = str1 + str5 + " = " + Util.getIntValue(str6) + ","; continue;
/*      */               } 
/* 1292 */               str1 = str1 + str5 + " = NULL,"; continue;
/*      */             } 
/* 1294 */             if (str4.toUpperCase().indexOf("NUMBER") >= 0 || str4.toUpperCase().indexOf("FLOAT") >= 0 || str4.toUpperCase().indexOf("DECIMAL") >= 0) {
/* 1295 */               int m = str4.indexOf(",");
/* 1296 */               int n = 2;
/* 1297 */               if (m > -1) {
/* 1298 */                 n = Util.getIntValue(str4.substring(m + 1, str4.length() - 1).trim(), 2);
/*      */               } else {
/* 1300 */                 n = 2;
/*      */               } 
/* 1302 */               if (!Util.null2String(str6).equals("")) {
/* 1303 */                 str1 = str1 + str5 + " = " + Util.getPointValue2(str6, n) + ",";
/*      */                 continue;
/*      */               } 
/* 1306 */               str1 = str1 + str5 + " = NULL,";
/*      */               continue;
/*      */             } 
/* 1309 */             String str11 = "";
/* 1310 */             if (j == 3 && (i == 161 || i == 162)) {
/* 1311 */               str11 = Util.null2String(str6);
/* 1312 */               str11 = str11.trim();
/*      */             }
/* 1314 */             else if (j == 2 && i == 2) {
/* 1315 */               str11 = Util.toHtml100(str6);
/* 1316 */               str11 = StringHelper.convertSpecialChar2Html(str11);
/* 1317 */             } else if (j == 1 && i == 1) {
/* 1318 */               str11 = StringHelper.convertSpecialChar2Html(str6);
/* 1319 */               str11 = Util.toHtmlForWorkflow(str11);
/* 1320 */             } else if (j == 2 && i == 1) {
/* 1321 */               str11 = Util.StringReplace(str6, " ", "&nbsp;");
/* 1322 */               str11 = StringHelper.convertSpecialChar2Html(str11);
/* 1323 */               str11 = Util.toHtmlForWorkflowForMode(str11);
/*      */             } else {
/* 1325 */               str11 = Util.StringReplace(Util.toHtml10(str6), " ", "&nbsp;");
/* 1326 */               str11 = StringHelper.convertSpecialChar2Html(str11);
/* 1327 */               str11 = Util.toHtmlForWorkflow(str11);
/*      */             } 
/*      */             
/* 1330 */             str11 = Util.StringReplace(str11, "weaver2017", "+");
/*      */             
/* 1332 */             if (2 == j || FormModeBrowserUtil.isMultiBrowser("" + j, "" + i)) {
/* 1333 */               str1 = str1 + str5 + " = ?,";
/* 1334 */               arrayList.add(str11); continue;
/*      */             } 
/* 1336 */             str1 = str1 + str5 + " = '" + str11 + "',";
/*      */             
/*      */             continue;
/*      */           } 
/*      */           
/* 1341 */           if (str4.toUpperCase().indexOf("INT") >= 0) {
/* 1342 */             if (!"".equals(str6)) {
/* 1343 */               str1 = str1 + str5 + " = " + Util.getIntValue(str6) + ","; continue;
/*      */             } 
/* 1345 */             str1 = str1 + str5 + " = NULL,"; continue;
/*      */           } 
/* 1347 */           if (str4.toUpperCase().indexOf("DECIMAL") >= 0 || str4.toUpperCase().indexOf("FLOAT") >= 0) {
/* 1348 */             int m = str4.indexOf(",");
/* 1349 */             int n = 2;
/* 1350 */             if (m > -1) {
/* 1351 */               n = Util.getIntValue(str4.substring(m + 1, str4.length() - 1).trim(), 2);
/*      */             } else {
/* 1353 */               n = 2;
/*      */             } 
/* 1355 */             if (!"".equals(str6)) {
/* 1356 */               str1 = str1 + str5 + " = " + Util.getPointValue2(str6, n) + ","; continue;
/*      */             } 
/* 1358 */             str1 = str1 + str5 + " = NULL,";
/*      */             continue;
/*      */           } 
/* 1361 */           String str10 = "";
/* 1362 */           if (j == 2 && i == 2) {
/* 1363 */             str10 = StringHelper.convertSpecialChar2Html(str6);
/* 1364 */             str10 = Util.toHtml100(str10);
/* 1365 */           } else if (j == 1 && i == 1) {
/* 1366 */             str10 = StringHelper.convertSpecialChar2Html(str6);
/* 1367 */             str10 = Util.toHtmlForWorkflow(str10);
/* 1368 */           } else if (j == 2 && i == 1) {
/* 1369 */             str10 = Util.StringReplace(str6, " ", "&nbsp;");
/* 1370 */             str10 = StringHelper.convertSpecialChar2Html(str10);
/* 1371 */             str10 = Util.toHtmlForWorkflowForMode(str10);
/* 1372 */           } else if (j == 4 && i == 1) {
/* 1373 */             str10 = Util.StringReplace(str6, " ", "&nbsp;");
/* 1374 */             str10 = Util.toHtmlForWorkflow(str10);
/* 1375 */             if (str10.equals("")) {
/* 1376 */               str10 = "0";
/*      */             }
/*      */           } else {
/* 1379 */             str10 = Util.StringReplace(Util.toHtml10(str6), " ", "&nbsp;");
/* 1380 */             str10 = StringHelper.convertSpecialChar2Html(str10);
/* 1381 */             str10 = Util.toHtmlForWorkflow(str10);
/*      */           } 
/* 1383 */           str10 = Util.StringReplace(str10, "weaver2017", "+");
/*      */           
/* 1385 */           if (2 == j || FormModeBrowserUtil.isMultiBrowser("" + j, "" + i)) {
/* 1386 */             str1 = str1 + str5 + " = ?,";
/* 1387 */             arrayList.add(str10); continue;
/*      */           } 
/* 1389 */           str1 = str1 + str5 + " = '" + str10 + "',";
/*      */         } 
/*      */       } 
/*      */ 
/*      */ 
/*      */       
/* 1395 */       if (!str1.equals("")) {
/* 1396 */         str1 = str1.substring(0, str1.length() - 1);
/* 1397 */         str1 = "update cptcapital set  " + str1 + " where id = " + paramString;
/* 1398 */         Object[] arrayOfObject = new Object[arrayList.size()];
/* 1399 */         for (byte b = 0; b < arrayList.size(); b++) {
/* 1400 */           arrayOfObject[b] = arrayList.get(b);
/*      */         }
/*      */         
/* 1403 */         recordSet.executeSql(str1, false, arrayOfObject);
/*      */       } 
/* 1405 */     } catch (Exception exception) {
/* 1406 */       exception.printStackTrace();
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void releaseFrozenCptnum(String paramString) {
/* 1415 */     RecordSet recordSet = new RecordSet();
/* 1416 */     recordSet.execute("update cptcapital set frozennum=0 where isdata='2' and frozennum>0");
/* 1417 */     DoFrozenCpt_new();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void doFreezeCptnum(RequestManager paramRequestManager) {
/* 1424 */     if (paramRequestManager != null) {
/*      */       try {
/* 1426 */         String str = paramRequestManager.getSrc();
/* 1427 */         if ("submit".equalsIgnoreCase(str) || "delete"
/* 1428 */           .equalsIgnoreCase(str) || "reject"
/* 1429 */           .equalsIgnoreCase(str) || "intervenor"
/* 1430 */           .equalsIgnoreCase(str))
/*      */         {
/* 1432 */           RecordSet recordSet = new RecordSet();
/*      */           
/* 1434 */           int i = paramRequestManager.getWorkflowid();
/* 1435 */           int j = paramRequestManager.getFormid();
/* 1436 */           String str1 = getWftype("" + i);
/* 1437 */           if (!"".equals(str1) && !"apply".equalsIgnoreCase(str1) && !"applyuse".equalsIgnoreCase(str1))
/*      */           {
/* 1439 */             recordSet.execute("update cptcapital set frozennum=0 where isdata='2' and frozennum>0");
/* 1440 */             DoFrozenCpt_new();
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */           
/*      */           }
/* 1469 */           else if (j == 18 || j == 19 || j == 201 || j == 220 || j == 221 || j == 222 || j == 224)
/*      */           {
/* 1471 */             recordSet.execute("update cptcapital set frozennum=0 where isdata='2' and frozennum>0");
/* 1472 */             DoFrozenCpt_new();
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */           
/*      */           }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */         
/*      */         }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*      */       }
/* 1499 */       catch (Exception exception) {}
/*      */     }
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   public void run() {
/* 1506 */     if ("releaseFrozenCptnum".equalsIgnoreCase(this.opType)) {
/* 1507 */       releaseFrozenCptnum(this.delreqids);
/* 1508 */     } else if ("freezeCptnum".equalsIgnoreCase(this.opType)) {
/* 1509 */       doFreezeCptnum(this.RequestManager);
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public synchronized void DoFrozenCpt_new() {
/* 1518 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 1519 */     final RecordSet rs = new RecordSet();
/* 1520 */     final RecordSet rs1 = new RecordSet();
/* 1521 */     String str1 = recordSet1.getDBType();
/* 1522 */     String str2 = "";
/* 1523 */     if ("sqlserver".equalsIgnoreCase(str1)) {
/* 1524 */       str2 = "isnull";
/* 1525 */     } else if ("oracle".equalsIgnoreCase(str1)) {
/* 1526 */       str2 = "nvl";
/* 1527 */     } else if ("mysql".equalsIgnoreCase(str1)) {
/* 1528 */       str2 = "ifnull";
/*      */     }
/* 1530 */     else if ("postgresql".equalsIgnoreCase(str1)) {
/* 1531 */       str2 = "isnull";
/*      */     } 
/* 1533 */     RecordSet recordSet3 = new RecordSet();
/* 1534 */     StringBuffer stringBuffer1 = new StringBuffer("");
/* 1535 */     String str3 = "substring";
/* 1536 */     if ("sqlserver".equalsIgnoreCase(str1)) {
/* 1537 */       str3 = "substring";
/* 1538 */     } else if ("oracle".equalsIgnoreCase(str1)) {
/* 1539 */       str3 = "substr";
/* 1540 */     } else if ("mysql".equalsIgnoreCase(str1)) {
/* 1541 */       str3 = "substring";
/*      */     } 
/* 1543 */     String str4 = "select t2.wfid,t1.id,t1.formid,t2.zc,t3.fieldname as zc_fieldname," + str3 + "(REVERSE(Rtrim(t3.detailtable)),1,1) as zc_viewtype,t2.sl,t4.fieldname as sl_fieldname," + str3 + "(REVERSE(Rtrim(t4.detailtable)),1,1) as sl_viewtype  from workflow_base t1,cpt_cptwfconf t2 join workflow_billfield t3 on t3.id=t2.zc  join workflow_billfield t4 on t4.id=t2.sl where t2.wfid=t1.id  and t2.wftype in('fetch','mend','move','lend','back','discard','loss') ";
/*      */     
/* 1545 */     recordSet3.execute(str4);
/* 1546 */     ArrayList arrayList = new ArrayList();
/* 1547 */     while (recordSet3.next()) {
/* 1548 */       int i = Util.getIntValue(recordSet3.getString("formid"), 0);
/* 1549 */       if (i >= 0) {
/*      */         continue;
/*      */       }
/* 1552 */       int j = Util.getIntValue(recordSet3.getString("wfid"), 0);
/* 1553 */       String str5 = recordSet3.getString("zc_fieldname");
/* 1554 */       int k = Util.getIntValue(recordSet3.getString("zc_viewtype"), 0);
/* 1555 */       String str6 = recordSet3.getString("sl_fieldname");
/* 1556 */       int m = Util.getIntValue(recordSet3.getString("sl_viewtype"), 0);
/*      */       
/* 1558 */       if (k != m) {
/*      */         continue;
/*      */       }
/*      */ 
/*      */ 
/*      */ 
/*      */       
/* 1565 */       String str7 = "formtable_main_" + -i;
/* 1566 */       recordSet1.execute("select tablename from workflow_bill where id=" + i);
/* 1567 */       while (recordSet1.next()) {
/* 1568 */         str7 = recordSet1.getString("tablename");
/*      */       }
/* 1570 */       String str8 = "";
/* 1571 */       String str9 = "m." + str5;
/* 1572 */       String str10 = "d." + str5;
/* 1573 */       if (k == 0 && m == 0) {
/* 1574 */         str8 = " select m.requestid," + str2 + "(m." + str6 + ",1) as num," + str9 + " as capitalid from " + str7 + " m where ";
/* 1575 */       } else if (k == m) {
/* 1576 */         str8 = " select m.requestid," + str2 + "(d." + str6 + ",1) as num," + str10 + " as capitalid from " + str7 + " m," + str7 + "_dt" + k + " d where d.mainid=m.id and ";
/*      */       } 
/* 1578 */       stringBuffer1.append(" union all ").append("\n");
/* 1579 */       stringBuffer1.append(str8 + " exists(select 1 from workflow_requestbase r where r.workflowid=" + j + " and r.requestid=m.requestid and r.currentnodetype>0 and r.currentnodetype<3 ) ").append("\n");
/*      */     } 
/* 1581 */     final StringBuffer sb = new StringBuffer();
/* 1582 */     if ("sqlserver".equalsIgnoreCase(str1)) {
/* 1583 */       stringBuffer2.append("  ").append("\n")
/* 1584 */         .append(" select m.requestid,d.number_n as num,d.capitalid from bill_cptfetchmain m,bill_cptfetchdetail d where d.cptfetchid=m.id and  exists(select 1 from workflow_requestbase r where r.requestid=m.requestid and r.currentnodetype>0 and r.currentnodetype<3) ").append("\n")
/* 1585 */         .append(" union all ").append("\n")
/* 1586 */         .append(" select m.requestid,d.number_n as num ,d.capitalid from bill_CptAdjustMain m,bill_CptAdjustDetail d where d.cptadjustid=m.id and  exists(select 1 from workflow_requestbase r where r.requestid=m.requestid and r.currentnodetype>0 and r.currentnodetype<3) ").append("\n")
/* 1587 */         .append(" union all ").append("\n")
/* 1588 */         .append(" select m.requestid,d.numbers as num,d.capitalid from bill_Discard m,bill_discard_detail d where d.mainid=m.id and  exists(select 1 from workflow_requestbase r where r.requestid=m.requestid and r.currentnodetype>0 and r.currentnodetype<3) ").append("\n")
/* 1589 */         .append(" union all ").append("\n")
/* 1590 */         .append(" select m.requestid,m.losscount as num ,m.losscpt as capitalid from bill_cptloss m where  exists(select 1 from workflow_requestbase r where r.requestid=m.requestid and r.currentnodetype>0 and r.currentnodetype<3) ").append("\n")
/* 1591 */         .append(" union all ").append("\n")
/* 1592 */         .append(" select m.requestid,1 as num ,m.cptMend as capitalid from bill_mendCpt m where exists(select 1 from workflow_requestbase r where r.requestid=m.requestid and r.currentnodetype>0 and r.currentnodetype<3) ").append("\n")
/* 1593 */         .append(" union all ").append("\n")
/* 1594 */         .append(" select m.requestid,1 as num,m.returnCpt as capitalid from bill_returncpt m where exists(select 1 from workflow_requestbase r where r.requestid=m.requestid and r.currentnodetype>0 and r.currentnodetype<3) ").append("\n")
/* 1595 */         .append(" union all ").append("\n")
/* 1596 */         .append("  select l.requestid,1 as num ,l.lendCpt as capitalid from bill_cptlend l where EXISTS (select 1 from workflow_requestbase r where r.requestid=l.requestid and r.currentnodetype>0 and r.currentnodetype<3) ").append("\n")
/* 1597 */         .append(stringBuffer1.toString()).append("\n");
/*      */     
/*      */     }
/* 1600 */     else if ("oracle".equalsIgnoreCase(str1)) {
/* 1601 */       stringBuffer2.append("  ").append("\n")
/* 1602 */         .append(" select m.requestid,d.number_n as num,d.capitalid from bill_cptfetchmain m,bill_cptfetchdetail d where d.cptfetchid=m.id  and exists(select 1 from workflow_requestbase r where r.requestid=m.requestid and r.currentnodetype>0 and r.currentnodetype<3) ").append("\n")
/* 1603 */         .append(" union all ").append("\n")
/* 1604 */         .append(" select m.requestid,d.number_n as num,d.capitalid from bill_CptAdjustMain m,bill_CptAdjustDetail d where d.cptadjustid=m.id  and exists(select 1 from workflow_requestbase r where r.requestid=m.requestid and r.currentnodetype>0 and r.currentnodetype<3) ").append("\n")
/* 1605 */         .append(" union all ").append("\n")
/* 1606 */         .append(" select m.requestid,d.numbers as num,d.capitalid from bill_Discard m,bill_discard_detail d where d.mainid=m.id and exists(select 1 from workflow_requestbase r where r.requestid=m.requestid and r.currentnodetype>0 and r.currentnodetype<3) ").append("\n")
/* 1607 */         .append(" union all ").append("\n")
/* 1608 */         .append(" select m.requestid,m.losscount as num,m.losscpt as capitalid from bill_cptloss m where  exists(select 1 from workflow_requestbase r where r.requestid=m.requestid and r.currentnodetype>0 and r.currentnodetype<3) ").append("\n")
/* 1609 */         .append(" union all ").append("\n")
/* 1610 */         .append(" select m.requestid,1 as num,m.cptMend as capitalid from bill_mendCpt m where exists(select 1 from workflow_requestbase r where r.requestid=m.requestid and r.currentnodetype>0 and r.currentnodetype<3) ").append("\n")
/* 1611 */         .append(" union all ").append("\n")
/* 1612 */         .append(" select m.requestid,1 as num,m.returnCpt as capitalid from bill_returncpt m where exists(select 1 from workflow_requestbase r where r.requestid=m.requestid and r.currentnodetype>0 and r.currentnodetype<3) ").append("\n")
/* 1613 */         .append(" union all ").append("\n")
/* 1614 */         .append(" select l.requestid,1 as num,l.lendCpt as capitalid from bill_cptlend l where exists(select 1 from workflow_requestbase r where r.requestid=l.requestid and r.currentnodetype>0 and r.currentnodetype<3) ").append("\n")
/* 1615 */         .append(stringBuffer1.toString()).append("\n");
/*      */ 
/*      */     
/*      */     }
/* 1619 */     else if ("postgresql".equalsIgnoreCase(str1)) {
/* 1620 */       stringBuffer2.append("  ").append("\n")
/* 1621 */         .append(" select m.requestid,d.number_n as num,d.capitalid from bill_cptfetchmain m,bill_cptfetchdetail d where d.cptfetchid=m.id  and exists(select 1 from workflow_requestbase r where r.requestid=m.requestid and r.currentnodetype>0 and r.currentnodetype<3) ").append("\n")
/* 1622 */         .append(" union all ").append("\n")
/* 1623 */         .append(" select m.requestid,d.number_n as num,d.capitalid from bill_CptAdjustMain m,bill_CptAdjustDetail d where d.cptadjustid=m.id  and exists(select 1 from workflow_requestbase r where r.requestid=m.requestid and r.currentnodetype>0 and r.currentnodetype<3) ").append("\n")
/* 1624 */         .append(" union all ").append("\n")
/* 1625 */         .append(" select m.requestid,d.numbers as num,d.capitalid from bill_Discard m,bill_discard_detail d where d.mainid=m.id and exists(select 1 from workflow_requestbase r where r.requestid=m.requestid and r.currentnodetype>0 and r.currentnodetype<3) ").append("\n")
/* 1626 */         .append(" union all ").append("\n")
/* 1627 */         .append(" select m.requestid,m.losscount as num,m.losscpt as capitalid from bill_cptloss m where  exists(select 1 from workflow_requestbase r where r.requestid=m.requestid and r.currentnodetype>0 and r.currentnodetype<3) ").append("\n")
/* 1628 */         .append(" union all ").append("\n")
/* 1629 */         .append(" select m.requestid,1 as num,m.cptMend as capitalid from bill_mendCpt m where exists(select 1 from workflow_requestbase r where r.requestid=m.requestid and r.currentnodetype>0 and r.currentnodetype<3) ").append("\n")
/* 1630 */         .append(" union all ").append("\n")
/* 1631 */         .append(" select m.requestid,1 as num,m.returnCpt as capitalid from bill_returncpt m where exists(select 1 from workflow_requestbase r where r.requestid=m.requestid and r.currentnodetype>0 and r.currentnodetype<3) ").append("\n")
/* 1632 */         .append(" union all ").append("\n")
/* 1633 */         .append(" select l.requestid,1 as num,l.lendCpt as capitalid from bill_cptlend l where exists(select 1 from workflow_requestbase r where r.requestid=l.requestid and r.currentnodetype>0 and r.currentnodetype<3) ").append("\n")
/* 1634 */         .append(stringBuffer1.toString()).append("\n");
/*      */ 
/*      */     
/*      */     }
/* 1638 */     else if ("mysql".equalsIgnoreCase(str1)) {
/* 1639 */       stringBuffer2.append("  ").append("\n")
/* 1640 */         .append(" select m.requestid,d.number_n as num,d.capitalid from bill_cptfetchmain m,bill_cptfetchdetail d where d.cptfetchid=m.id and  exists(select 1 from workflow_requestbase r where r.requestid=m.requestid and r.currentnodetype>0 and r.currentnodetype<3) ").append("\n")
/* 1641 */         .append(" union all ").append("\n")
/* 1642 */         .append(" select m.requestid,d.number_n as num,d.capitalid from bill_CptAdjustMain m,bill_CptAdjustDetail d where d.cptadjustid=m.id and exists(select 1 from workflow_requestbase r where r.requestid=m.requestid and r.currentnodetype>0 and r.currentnodetype<3) ").append("\n")
/* 1643 */         .append(" union all ").append("\n")
/* 1644 */         .append(" select m.requestid,d.numbers as num,d.capitalid from bill_Discard m,bill_discard_detail d where d.mainid=m.id and  exists(select 1 from workflow_requestbase r where r.requestid=m.requestid and r.currentnodetype>0 and r.currentnodetype<3) ").append("\n")
/* 1645 */         .append(" union all ").append("\n")
/* 1646 */         .append(" select m.requestid,m.losscount as num,m.losscpt as capitalid from bill_cptloss m where  exists(select 1 from workflow_requestbase r where r.requestid=m.requestid and r.currentnodetype>0 and r.currentnodetype<3) ").append("\n")
/* 1647 */         .append(" union all ").append("\n")
/* 1648 */         .append(" select m.requestid,1 as num,m.cptMend as capitalid from bill_mendCpt m where exists(select 1 from workflow_requestbase r where r.requestid=m.requestid and r.currentnodetype>0 and r.currentnodetype<3) ").append("\n")
/* 1649 */         .append(" union all ").append("\n")
/* 1650 */         .append(" select m.requestid,1 as num,m.returnCpt as capitalid from bill_returncpt m where exists(select 1 from workflow_requestbase r where r.requestid=m.requestid and r.currentnodetype>0 and r.currentnodetype<3) ").append("\n")
/* 1651 */         .append(" union all ").append("\n")
/* 1652 */         .append(" select l.requestid,1 as num,l.lendCpt as capitalid from bill_cptlend l where exists(select 1 from workflow_requestbase r where r.requestid=l.requestid and r.currentnodetype>0 and r.currentnodetype<3) ").append("\n")
/* 1653 */         .append(stringBuffer1.toString()).append("\n");
/*      */     
/*      */     }
/* 1656 */     else if ("postgresql".equalsIgnoreCase(str1)) {
/* 1657 */       stringBuffer2.append("  ").append("\n")
/* 1658 */         .append(" select m.requestid,d.number_n as num,d.capitalid from bill_cptfetchmain m,bill_cptfetchdetail d where d.cptfetchid=m.id and  exists(select 1 from workflow_requestbase r where r.requestid=m.requestid and r.currentnodetype>0 and r.currentnodetype<3) ").append("\n")
/* 1659 */         .append(" union all ").append("\n")
/* 1660 */         .append(" select m.requestid,d.number_n as num,d.capitalid from bill_CptAdjustMain m,bill_CptAdjustDetail d where d.cptadjustid=m.id and exists(select 1 from workflow_requestbase r where r.requestid=m.requestid and r.currentnodetype>0 and r.currentnodetype<3) ").append("\n")
/* 1661 */         .append(" union all ").append("\n")
/* 1662 */         .append(" select m.requestid,d.numbers as num,d.capitalid from bill_Discard m,bill_discard_detail d where d.mainid=m.id and  exists(select 1 from workflow_requestbase r where r.requestid=m.requestid and r.currentnodetype>0 and r.currentnodetype<3) ").append("\n")
/* 1663 */         .append(" union all ").append("\n")
/* 1664 */         .append(" select m.requestid,m.losscount as num,m.losscpt as capitalid from bill_cptloss m where  exists(select 1 from workflow_requestbase r where r.requestid=m.requestid and r.currentnodetype>0 and r.currentnodetype<3) ").append("\n")
/* 1665 */         .append(" union all ").append("\n")
/* 1666 */         .append(" select m.requestid,1 as num,m.cptMend as capitalid from bill_mendCpt m where exists(select 1 from workflow_requestbase r where r.requestid=m.requestid and r.currentnodetype>0 and r.currentnodetype<3) ").append("\n")
/* 1667 */         .append(" union all ").append("\n")
/* 1668 */         .append(" select m.requestid,1 as num,m.returnCpt as capitalid from bill_returncpt m where exists(select 1 from workflow_requestbase r where r.requestid=m.requestid and r.currentnodetype>0 and r.currentnodetype<3) ").append("\n")
/* 1669 */         .append(" union all ").append("\n")
/* 1670 */         .append(" select l.requestid,1 as num,l.lendCpt as capitalid from bill_cptlend l where exists(select 1 from workflow_requestbase r where r.requestid=l.requestid and r.currentnodetype>0 and r.currentnodetype<3) ").append("\n")
/* 1671 */         .append(stringBuffer1.toString()).append("\n");
/*      */     } 
/*      */ 
/*      */ 
/*      */     
/* 1676 */     Thread thread = new Thread() {
/*      */         public void run() {
/*      */           try {
/* 1679 */             sleep(1000L);
/* 1680 */           } catch (InterruptedException interruptedException) {
/* 1681 */             interruptedException.printStackTrace();
/*      */           } 
/* 1683 */           String str = "select capitalid,sum(num) as frozennum from (" + sb.toString() + " ) t group by capitalid";
/*      */           
/* 1685 */           rs.execute(str);
/* 1686 */           while (rs.next()) {
/* 1687 */             String str1 = rs.getString("capitalid");
/* 1688 */             String str2 = rs.getString("frozennum");
/* 1689 */             if (!"".equals(str1)) {
/* 1690 */               rs1.execute("update cptcapital set frozennum=" + str2 + " where id=" + str1);
/*      */             }
/*      */           } 
/*      */         }
/*      */       };
/* 1695 */     thread.start();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static void releaseNumber(int paramInt) {
/* 1705 */     int i = 0;
/* 1706 */     RecordSet recordSet = new RecordSet();
/* 1707 */     recordSet.executeQuery("select formid from workflow_base where id=?", new Object[] { Integer.valueOf(paramInt) });
/* 1708 */     if (recordSet.next()) {
/* 1709 */       i = recordSet.getInt("formid");
/*      */     }
/*      */     
/* 1712 */     CptWfUtil cptWfUtil = new CptWfUtil();
/* 1713 */     String str = cptWfUtil.getWftype("" + paramInt);
/* 1714 */     if (!"".equals(str) && !"apply".equalsIgnoreCase(str) && !"applyuse".equalsIgnoreCase(str)) {
/* 1715 */       recordSet.execute("update CptCapital set frozennum = 0 where isdata='2' and  frozennum > 0");
/* 1716 */       CptWfUtil cptWfUtil1 = new CptWfUtil();
/* 1717 */       cptWfUtil1.DoFrozenCpt_new();
/* 1718 */     } else if (i == 18 || i == 19 || i == 201 || i == 220 || i == 221 || i == 222 || i == 224) {
/* 1719 */       recordSet.execute("update CptCapital set frozennum = 0 where isdata='2' and  frozennum > 0");
/* 1720 */       CptWfUtil cptWfUtil1 = new CptWfUtil();
/* 1721 */       cptWfUtil1.DoFrozenCpt_new();
/*      */     } 
/*      */   }
/*      */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/cpt/util/CptWfUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */