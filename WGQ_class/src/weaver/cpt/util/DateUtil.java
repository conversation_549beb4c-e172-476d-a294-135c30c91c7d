/*    */ package weaver.cpt.util;
/*    */ 
/*    */ import java.text.SimpleDateFormat;
/*    */ import java.util.Calendar;
/*    */ import java.util.Date;
/*    */ import java.util.GregorianCalendar;
/*    */ import java.util.Locale;
/*    */ 
/*    */ 
/*    */ public class DateUtil
/*    */ {
/* 12 */   private static SimpleDateFormat datetimeFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.US);
/*    */ 
/*    */ 
/*    */   
/* 16 */   private static SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd", Locale.US);
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public static String getCurrentQuarterStartTime() {
/* 22 */     Calendar calendar = Calendar.getInstance();
/* 23 */     int i = calendar.get(2) + 1;
/* 24 */     Date date = null;
/*    */     try {
/* 26 */       if (i >= 1 && i <= 3) {
/* 27 */         calendar.set(2, 0);
/* 28 */       } else if (i >= 4 && i <= 6) {
/* 29 */         calendar.set(2, 3);
/* 30 */       } else if (i >= 7 && i <= 9) {
/* 31 */         calendar.set(2, 6);
/* 32 */       } else if (i >= 10 && i <= 12) {
/* 33 */         calendar.set(2, 9);
/* 34 */       }  calendar.set(5, 1);
/* 35 */       date = datetimeFormat.parse(dateFormat.format(calendar.getTime()) + " 00:00");
/* 36 */     } catch (Exception exception) {
/* 37 */       exception.printStackTrace();
/*    */     } 
/* 39 */     return dateFormat.format(date);
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public static String getCurrentQuarterEndTime() {
/* 45 */     Calendar calendar = Calendar.getInstance();
/* 46 */     int i = calendar.get(2) + 1;
/* 47 */     Date date = null;
/*    */     try {
/* 49 */       if (i >= 1 && i <= 3) {
/* 50 */         calendar.set(2, 2);
/* 51 */         calendar.set(5, 31);
/* 52 */       } else if (i >= 4 && i <= 6) {
/* 53 */         calendar.set(2, 5);
/* 54 */         calendar.set(5, 30);
/* 55 */       } else if (i >= 7 && i <= 9) {
/* 56 */         calendar.set(2, 8);
/* 57 */         calendar.set(5, 30);
/* 58 */       } else if (i >= 10 && i <= 12) {
/* 59 */         calendar.set(2, 11);
/* 60 */         calendar.set(5, 31);
/*    */       } 
/* 62 */       date = datetimeFormat.parse(dateFormat.format(calendar.getTime()) + " 23:59");
/* 63 */     } catch (Exception exception) {
/* 64 */       exception.printStackTrace();
/*    */     } 
/* 66 */     return dateFormat.format(date);
/*    */   }
/*    */   
/*    */   public static String getLastDayOfWeek() {
/* 70 */     Date date = new Date();
/* 71 */     GregorianCalendar gregorianCalendar = new GregorianCalendar();
/* 72 */     gregorianCalendar.setFirstDayOfWeek(2);
/* 73 */     gregorianCalendar.setTime(date);
/* 74 */     gregorianCalendar.set(7, gregorianCalendar.getFirstDayOfWeek() + 5);
/* 75 */     return dateFormat.format(gregorianCalendar.getTime());
/*    */   }
/*    */   
/*    */   public static String getFirstDayOfWeek() {
/* 79 */     Date date = new Date();
/* 80 */     GregorianCalendar gregorianCalendar = new GregorianCalendar();
/* 81 */     gregorianCalendar.setFirstDayOfWeek(1);
/* 82 */     gregorianCalendar.setTime(date);
/* 83 */     gregorianCalendar.set(7, gregorianCalendar.getFirstDayOfWeek());
/* 84 */     return dateFormat.format(gregorianCalendar.getTime());
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/cpt/util/DateUtil.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */