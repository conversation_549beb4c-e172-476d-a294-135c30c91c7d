/*     */ package weaver.cpt.util;
/*     */ 
/*     */ import java.io.File;
/*     */ import java.io.FileWriter;
/*     */ import java.io.IOException;
/*     */ import java.util.Properties;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.conn.RecordSetTrans;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.GCONST;
/*     */ import weaver.general.Util;
/*     */ import weaver.proj.util.FieldUtil;
/*     */ import weaver.proj.util.LabelUtil;
/*     */ import weaver.proj.util.PrjFieldManager;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CptInitManager
/*     */   extends BaseBean
/*     */   implements Runnable
/*     */ {
/*     */   public synchronized void run() {
/*  31 */     writeLog(getClass().getName() + " is running start...");
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  36 */     boolean bool1 = "1".equals(getPropValue("module", "cpt.status"));
/*  37 */     if (bool1) {
/*  38 */       FileWriter fileWriter = null;
/*  39 */       FieldUtil fieldUtil = new FieldUtil();
/*     */       
/*     */       try {
/*  42 */         boolean bool3 = "0".equals(getPropValue("weaver_initoldfields", "changeSelectValue"));
/*  43 */         if (!bool3) {
/*  44 */           fieldUtil.changeSelectValue();
/*     */           
/*  46 */           String str = GCONST.getPropertyPath();
/*  47 */           Properties properties = LoadTemplateProp("weaver_initoldfields");
/*  48 */           properties.setProperty("cptselect", "0");
/*  49 */           properties.setProperty("changeSelectValue", "0");
/*  50 */           fileWriter = new FileWriter(new File(str + "weaver_initoldfields.properties"));
/*  51 */           properties.store(fileWriter, "");
/*  52 */           fileWriter.flush();
/*  53 */           fileWriter.close();
/*     */         } 
/*     */         
/*  56 */         boolean bool4 = "1".equals(getPropValue("weaver_initmodule", "cpt"));
/*  57 */         if (bool4) {
/*     */           
/*  59 */           writeLog(" init cpt ...");
/*  60 */           RecordSet recordSet1 = new RecordSet();
/*  61 */           RecordSet recordSet2 = new RecordSet();
/*     */ 
/*     */           
/*  64 */           String str1 = "select id,resourceid,createrid,stateid from cptcapital where isdata=2 and sptcount=1 and stateid<>1 and (resourceid is not null or resourceid!='') ";
/*  65 */           recordSet1.executeSql(str1);
/*  66 */           while (recordSet1.next()) {
/*  67 */             int i = recordSet1.getInt("id");
/*  68 */             int j = recordSet1.getInt("resourceid");
/*  69 */             int k = recordSet1.getInt("createrid");
/*  70 */             if (j > 0) {
/*  71 */               recordSet2.executeSql("INSERT INTO CptCapitalShareInfo(relateditemid,sharetype,sharelevel,userid,isdefault) VALUES (" + i + ",'6','1','" + j + "','1')");
/*     */             }
/*  73 */             if (k > 0) {
/*  74 */               recordSet2.executeSql("INSERT INTO CptCapitalShareInfo(relateditemid,sharetype,sharelevel,userid,isdefault) VALUES (" + i + ",'7','2','" + k + "','1')");
/*     */             }
/*     */           } 
/*     */ 
/*     */           
/*  79 */           boolean bool = "1".equals(getPropValue("weaver_initoldfields", "cpt"));
/*  80 */           if (bool) {
/*  81 */             fieldUtil.initOldFields("cpt");
/*     */           }
/*     */ 
/*     */ 
/*     */           
/*  86 */           byte b = 1;
/*  87 */           str1 = "select * from cptDefineField order by dsporder ";
/*  88 */           recordSet1.execute(str1);
/*  89 */           while (recordSet1.next()) {
/*  90 */             recordSet2.executeSql("update cptDefineField set dsporder=" + b + " where id=" + recordSet1.getInt("id"));
/*  91 */             b++;
/*     */           } 
/*     */ 
/*     */           
/*  95 */           String str2 = GCONST.getPropertyPath();
/*  96 */           Properties properties = LoadTemplateProp("weaver_initoldfields");
/*  97 */           properties.setProperty("cpt", "0");
/*  98 */           fileWriter = new FileWriter(new File(str2 + "weaver_initoldfields.properties"));
/*  99 */           properties.store(fileWriter, "");
/* 100 */           fileWriter.flush();
/* 101 */           fileWriter.close();
/*     */           
/* 103 */           properties = LoadTemplateProp("weaver_initmodule");
/* 104 */           properties.setProperty("cpt", "0");
/* 105 */           fileWriter = new FileWriter(new File(str2 + "weaver_initmodule.properties"));
/* 106 */           properties.store(fileWriter, "");
/* 107 */           fileWriter.flush();
/* 108 */           fileWriter.close();
/*     */         } 
/* 110 */       } catch (Exception exception) {
/*     */         
/* 112 */         writeLog(exception.getMessage());
/*     */       } finally {
/*     */         try {
/* 115 */           if (fileWriter != null) {
/* 116 */             fileWriter.close();
/*     */           }
/* 118 */         } catch (IOException iOException) {
/*     */           
/* 120 */           iOException.printStackTrace();
/*     */         } 
/*     */       } 
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 166 */     boolean bool2 = "1".equals(getPropValue("module", "proj.status"));
/* 167 */     if (bool2) {
/* 168 */       boolean bool3 = "1".equals(getPropValue("weaver_initmodule", "proj"));
/* 169 */       if (bool3) {
/* 170 */         FileWriter fileWriter = null;
/*     */ 
/*     */         
/*     */         try {
/* 174 */           writeLog(" init proj ...");
/*     */           
/* 176 */           FieldUtil fieldUtil = new FieldUtil();
/* 177 */           RecordSet recordSet1 = new RecordSet();
/* 178 */           RecordSet recordSet2 = new RecordSet();
/* 179 */           RecordSet recordSet3 = new RecordSet();
/*     */ 
/*     */           
/* 182 */           initOldPrjtypeFieldLabels();
/*     */ 
/*     */ 
/*     */ 
/*     */           
/* 187 */           boolean bool = "1".equals(getPropValue("weaver_initoldfields", "proj"));
/* 188 */           if (bool) {
/* 189 */             fieldUtil.initOldFields("proj");
/*     */           }
/*     */ 
/*     */           
/* 193 */           byte b = 1;
/* 194 */           String str1 = "select * from prjDefineField where prjtype=-1 order by dsporder ";
/* 195 */           recordSet1.execute(str1);
/* 196 */           while (recordSet1.next()) {
/* 197 */             recordSet2.executeSql("update prjDefineField set dsporder=" + b + " where id=" + recordSet1.getInt("id"));
/* 198 */             b++;
/*     */           } 
/*     */ 
/*     */           
/* 202 */           PrjFieldManager prjFieldManager = new PrjFieldManager();
/* 203 */           str1 = "select * from Prj_ProjectType ";
/* 204 */           recordSet1.executeSql(str1);
/* 205 */           while (recordSet1.next()) {
/* 206 */             prjFieldManager.syncDefinedFields(Util.getIntValue(recordSet1.getString("id"), 0));
/*     */           }
/*     */ 
/*     */ 
/*     */           
/* 211 */           String str2 = GCONST.getPropertyPath();
/* 212 */           Properties properties = LoadTemplateProp("weaver_initoldfields");
/* 213 */           properties.setProperty("proj", "0");
/* 214 */           fileWriter = new FileWriter(new File(str2 + "weaver_initoldfields.properties"));
/* 215 */           properties.store(fileWriter, "");
/* 216 */           fileWriter.flush();
/* 217 */           fileWriter.close();
/*     */           
/* 219 */           properties = LoadTemplateProp("weaver_initmodule");
/* 220 */           properties.setProperty("proj", "0");
/* 221 */           fileWriter = new FileWriter(new File(str2 + "weaver_initmodule.properties"));
/* 222 */           properties.store(fileWriter, "");
/* 223 */           fileWriter.flush();
/* 224 */           fileWriter.close();
/*     */ 
/*     */         
/*     */         }
/* 228 */         catch (Exception exception) {
/*     */           
/* 230 */           writeLog(exception.getMessage());
/*     */         } finally {
/*     */           try {
/* 233 */             if (fileWriter != null) {
/* 234 */               fileWriter.close();
/*     */             }
/* 236 */           } catch (IOException iOException) {
/*     */             
/* 238 */             iOException.printStackTrace();
/*     */           } 
/*     */         } 
/*     */       } 
/*     */ 
/*     */ 
/*     */       
/* 245 */       boolean bool4 = "1".equals(getPropValue("weaver_initmodule", "updateprojtype"));
/* 246 */       if (bool4) {
/* 247 */         FileWriter fileWriter = null;
/*     */         
/*     */         try {
/* 250 */           RecordSet recordSet = new RecordSet();
/*     */           
/* 252 */           recordSet.executeSql(" SELECT DISTINCT prjtype FROM prjDefineField WHERE prjtype <> -1 ");
/* 253 */           PrjFieldManager prjFieldManager = new PrjFieldManager();
/* 254 */           while (recordSet.next()) {
/* 255 */             int i = recordSet.getInt("prjtype");
/* 256 */             prjFieldManager.syncDefinedFields(i);
/*     */           } 
/*     */ 
/*     */           
/* 260 */           String str1 = GCONST.getPropertyPath();
/* 261 */           Properties properties = LoadTemplateProp("weaver_initmodule");
/* 262 */           properties.setProperty("updateprojtype", "0");
/* 263 */           fileWriter = new FileWriter(new File(str1 + "weaver_initmodule.properties"));
/* 264 */           properties.store(fileWriter, "");
/* 265 */           fileWriter.flush();
/* 266 */           fileWriter.close();
/*     */         }
/* 268 */         catch (Exception exception) {
/* 269 */           writeLog(exception.getMessage());
/*     */         } finally {
/*     */           try {
/* 272 */             if (fileWriter != null) {
/* 273 */               fileWriter.close();
/*     */             }
/* 275 */           } catch (IOException iOException) {
/* 276 */             iOException.printStackTrace();
/*     */           } 
/*     */         } 
/*     */       } 
/*     */ 
/*     */       
/* 282 */       boolean bool5 = "1".equals(getPropValue("weaver_initmodule", "cusupdate"));
/* 283 */       if (bool5) {
/* 284 */         FileWriter fileWriter = null;
/*     */         
/* 286 */         RecordSet recordSet = new RecordSet();
/* 287 */         boolean bool6 = recordSet.getDBType().equals("oracle");
/* 288 */         boolean bool7 = recordSet.getDBType().equals("mysql");
/* 289 */         boolean bool8 = recordSet.getDBType().equals("db2");
/*     */         
/* 291 */         RecordSetTrans recordSetTrans = new RecordSetTrans();
/* 292 */         recordSetTrans.setAutoCommit(false);
/*     */         try {
/* 294 */           recordSet.execute("select distinct t2.id,t2.fieldname,t2.fielddbtype,t2.fieldhtmltype,t2.type from cus_formfield t1, cus_formdict t2 where t1.scope='ProjCustomField'  and t1.fieldid=t2.id order by t2.id");
/* 295 */           String str1 = "scope,scopeid,id,";
/* 296 */           while (recordSet.next()) {
/* 297 */             String str3 = recordSet.getString("fieldname");
/* 298 */             int i = recordSet.getInt("fieldhtmltype");
/* 299 */             int j = recordSet.getInt("type");
/* 300 */             String str4 = recordSet.getString("fielddbtype");
/* 301 */             if (i == 3) {
/* 302 */               if (j == 161 || j == 162) {
/* 303 */                 if (j == 161)
/* 304 */                 { if (bool6) { str4 = "varchar2(1000)"; }
/* 305 */                   else if (bool7) { str4 = "varchar(1000)"; }
/* 306 */                   else if (bool8) { str4 = "varchar(1000)"; }
/* 307 */                   else { str4 = "varchar(1000)"; }
/*     */                    }
/* 309 */                 else if (bool6) { str4 = "varchar2(4000)"; }
/* 310 */                 else if (bool7) { str4 = "varchar(2000)"; }
/* 311 */                 else if (bool8) { str4 = "varchar(2000)"; }
/* 312 */                 else { str4 = "text"; }
/*     */               
/*     */               }
/* 315 */               if (j == 256 || j == 257) {
/* 316 */                 if (j == 256)
/* 317 */                 { if (bool6) { str4 = "varchar2(1000)"; }
/* 318 */                   else if (bool7) { str4 = "varchar(1000)"; }
/* 319 */                   else if (bool8) { str4 = "varchar(1000)"; }
/* 320 */                   else { str4 = "varchar(1000)"; }
/*     */                    }
/* 322 */                 else if (bool6) { str4 = "varchar2(4000)"; }
/* 323 */                 else if (bool7) { str4 = "varchar(2000)"; }
/* 324 */                 else if (bool8) { str4 = "varchar(2000)"; }
/* 325 */                 else { str4 = "varchar(4000)"; }
/*     */               
/*     */               }
/* 328 */               if (j == 224 || j == 225) {
/* 329 */                 if (j == 224)
/* 330 */                 { if (bool6) { str4 = "varchar2(1000)"; }
/* 331 */                   else if (bool7) { str4 = "varchar(1000)"; }
/* 332 */                   else if (bool8) { str4 = "varchar(1000)"; }
/* 333 */                   else { str4 = "varchar(1000)"; }
/*     */                    }
/* 335 */                 else if (bool6) { str4 = "varchar2(4000)"; }
/* 336 */                 else if (bool7) { str4 = "varchar(2000)"; }
/* 337 */                 else if (bool8) { str4 = "varchar(2000)"; }
/* 338 */                 else { str4 = "text"; }
/*     */               
/*     */               }
/*     */             } 
/* 342 */             str1 = str1 + str3 + ",";
/* 343 */             recordSetTrans.executeSql("alter table prj_fielddata add " + str3 + " " + str4);
/*     */           } 
/* 345 */           str1 = str1.substring(0, str1.length() - 1);
/* 346 */           str1 = "insert into prj_fielddata(" + str1 + ") select " + str1 + " from cus_fielddata where scope='ProjCustomFieldReal' or scope='ProjCustomField' order by seqorder";
/* 347 */           recordSetTrans.executeSql(str1);
/*     */           
/* 349 */           String str2 = GCONST.getPropertyPath();
/* 350 */           Properties properties = LoadTemplateProp("weaver_initmodule");
/* 351 */           properties.setProperty("cusupdate", "0");
/* 352 */           fileWriter = new FileWriter(new File(str2 + "weaver_initmodule.properties"));
/* 353 */           properties.store(fileWriter, "");
/* 354 */           fileWriter.flush();
/* 355 */           fileWriter.close();
/* 356 */           recordSetTrans.commit();
/* 357 */         } catch (Exception exception) {
/* 358 */           recordSetTrans.rollback();
/* 359 */           writeLog(exception.getMessage());
/*     */         } finally {
/*     */           try {
/* 362 */             if (fileWriter != null) {
/* 363 */               fileWriter.close();
/*     */             }
/* 365 */           } catch (IOException iOException) {
/* 366 */             iOException.printStackTrace();
/*     */           } 
/*     */         } 
/*     */       } 
/*     */ 
/*     */       
/* 372 */       String str = getPropValue("weaver_initmodule", "stageinit");
/* 373 */       if ("".equals(str) || "1".equals(str)) {
/* 374 */         writeLog("init prj_taskprocess stage is running begin...");
/* 375 */         FileWriter fileWriter = null;
/* 376 */         RecordSet recordSet = new RecordSet();
/* 377 */         RecordSetTrans recordSetTrans = new RecordSetTrans();
/* 378 */         recordSetTrans.setAutoCommit(false);
/*     */         try {
/* 380 */           recordSet.execute("select id from Prj_ProjectInfo where id not in(select distinct prjid from prj_projectstage)");
/* 381 */           while (recordSet.next()) {
/* 382 */             String str2 = Util.null2String(recordSet.getString("id"));
/*     */             
/* 384 */             String str3 = "insert into prj_projectstage(prjid,name,dsporder,status) values(" + str2 + ",'" + SystemEnv.getHtmlLabelName(26797, 7) + "',1,0)";
/* 385 */             recordSetTrans.execute(str3);
/* 386 */             str3 = "select max(id) stageid from prj_projectstage where prjid = " + str2;
/* 387 */             recordSetTrans.execute(str3);
/* 388 */             int i = 0;
/* 389 */             if (recordSetTrans.next()) {
/* 390 */               i = Util.getIntValue(recordSetTrans.getString("stageid"));
/*     */             }
/* 392 */             str3 = "update Prj_TaskProcess set stageid=" + i + ",stageorder=dsporder where prjid=" + str2;
/* 393 */             recordSetTrans.execute(str3);
/*     */           } 
/* 395 */           recordSetTrans.execute("update Prj_TaskInfo set stageid = (select stageid from Prj_TaskProcess where Prj_TaskInfo.taskid = Prj_TaskProcess.id) where exists (select 1 from Prj_TaskProcess where Prj_TaskInfo.taskid = Prj_TaskProcess.id)");
/*     */ 
/*     */           
/* 398 */           String str1 = GCONST.getPropertyPath();
/* 399 */           Properties properties = LoadTemplateProp("weaver_initmodule");
/* 400 */           if ("".equals(str)) {
/* 401 */             properties.put("stageinit", "0");
/*     */           } else {
/* 403 */             properties.setProperty("stageinit", "0");
/*     */           } 
/* 405 */           fileWriter = new FileWriter(new File(str1 + "weaver_initmodule.properties"));
/* 406 */           properties.store(fileWriter, "");
/* 407 */           fileWriter.flush();
/* 408 */           fileWriter.close();
/* 409 */           recordSetTrans.commit();
/* 410 */         } catch (Exception exception) {
/* 411 */           recordSetTrans.rollback();
/* 412 */           writeLog(exception.getMessage());
/*     */         } finally {
/*     */           try {
/* 415 */             if (fileWriter != null) {
/* 416 */               fileWriter.close();
/*     */             }
/* 418 */           } catch (IOException iOException) {
/* 419 */             iOException.printStackTrace();
/*     */           } 
/*     */         } 
/* 422 */         writeLog("init prj_taskprocess stage is running end...");
/*     */       } 
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 430 */     writeLog(getClass().getName() + " is running end...");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static void initOldPrjtypeFieldLabels() {
/* 437 */     LabelUtil labelUtil = new LabelUtil();
/* 438 */     RecordSet recordSet1 = new RecordSet();
/* 439 */     RecordSet recordSet2 = new RecordSet();
/*     */     try {
/* 441 */       recordSet1.executeSql(" select fieldid, scopeid, fieldlable from cus_formfield where scope='ProjCustomField' ");
/* 442 */       while (recordSet1.next()) {
/* 443 */         String str1 = recordSet1.getString("fieldid");
/* 444 */         String str2 = recordSet1.getString("scopeid");
/* 445 */         String str3 = recordSet1.getString("fieldlable");
/*     */         
/* 447 */         int i = labelUtil.getLabelId(str3, 7);
/* 448 */         recordSet2.executeSql("update cus_formfield set fieldlable=" + i + " where scope='ProjCustomField' and fieldid=" + str1 + " and scopeid=" + str2);
/*     */       } 
/* 450 */     } catch (Exception exception) {
/* 451 */       recordSet1.writeLog(exception);
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/cpt/util/CptInitManager.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */