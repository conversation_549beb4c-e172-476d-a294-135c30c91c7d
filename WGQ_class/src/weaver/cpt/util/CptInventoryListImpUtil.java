/*     */ package weaver.cpt.util;
/*     */ import java.io.FileInputStream;
/*     */ import java.text.DateFormat;
/*     */ import java.util.HashMap;
/*     */ import java.util.HashSet;
/*     */ import java.util.Vector;
/*     */ import org.apache.poi.hssf.usermodel.HSSFCell;
/*     */ import org.apache.poi.hssf.usermodel.HSSFRow;
/*     */ import org.apache.poi.hssf.usermodel.HSSFSheet;
/*     */ import org.apache.poi.hssf.usermodel.HSSFWorkbook;
/*     */ import org.apache.poi.poifs.filesystem.POIFSFileSystem;
/*     */ import org.apache.poi.ss.usermodel.Cell;
/*     */ import org.apache.poi.ss.usermodel.CellType;
/*     */ import org.json.JSONArray;
/*     */ import org.json.JSONObject;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.systeminfo.language.LanguageComInfo;
/*     */ 
/*     */ public class CptInventoryListImpUtil {
/*  24 */   private HashMap<String, String> fullHrmMap = null;
/*  25 */   private int planId = 0;
/*  26 */   private int languageId = 7;
/*     */   
/*     */   public void ExcelToDB(String paramString, int paramInt, User paramUser, FileUpload paramFileUpload) {
/*  29 */     this.logBean.writeLog("tagtag start CptInventoryListImpUtil...");
/*  30 */     this.planId = paramInt;
/*  31 */     this.languageId = paramUser.getLanguage();
/*  32 */     FileInputStream fileInputStream = null;
/*  33 */     HSSFWorkbook hSSFWorkbook = null;
/*     */     try {
/*  35 */       fileInputStream = new FileInputStream(paramString);
/*  36 */       POIFSFileSystem pOIFSFileSystem = new POIFSFileSystem(fileInputStream);
/*  37 */       hSSFWorkbook = new HSSFWorkbook(pOIFSFileSystem);
/*  38 */       if (hSSFWorkbook == null) {
/*  39 */         this.msgType = "e1";
/*     */         
/*     */         return;
/*     */       } 
/*  43 */     } catch (Exception exception) {
/*     */       
/*  45 */       exception.printStackTrace();
/*  46 */       this.logBean.writeLog(exception.getMessage());
/*  47 */       this.msgType = "e1";
/*     */       return;
/*     */     } finally {
/*     */       try {
/*  51 */         if (fileInputStream != null) {
/*  52 */           fileInputStream.close();
/*     */         }
/*  54 */       } catch (Exception exception) {}
/*     */     } 
/*     */     
/*  57 */     this.fullHrmMap = getHrmMap();
/*  58 */     boolean bool = validateData(hSSFWorkbook, paramUser);
/*  59 */     if (!bool) {
/*     */       return;
/*     */     }
/*  62 */     ExcelToDB1(this.jsonArray);
/*     */     
/*  64 */     this.logBean.writeLog("tagtag end CptInventoryListImpUtil...");
/*     */   }
/*     */ 
/*     */   
/*     */   private boolean validateData(HSSFWorkbook paramHSSFWorkbook, User paramUser) {
/*  69 */     HSSFSheet hSSFSheet = paramHSSFWorkbook.getSheetAt(0);
/*  70 */     int i = hSSFSheet.getLastRowNum();
/*  71 */     HSSFRow hSSFRow = hSSFSheet.getRow(0);
/*  72 */     short s = hSSFRow.getLastCellNum();
/*     */     
/*  74 */     if (i <= 0) {
/*     */       
/*  76 */       this.msgType = "e4";
/*  77 */       return false;
/*     */     } 
/*  79 */     if (s != 4) {
/*     */       
/*  81 */       this.msgType = "e6";
/*  82 */       return false;
/*     */     } 
/*     */     
/*  85 */     this.jsonArray = new JSONArray();
/*     */     
/*  87 */     HashMap<String, String> hashMap1 = null;
/*  88 */     if (this.fullHrmMap != null) {
/*  89 */       hashMap1 = this.fullHrmMap;
/*     */     } else {
/*  91 */       hashMap1 = getHrmMap();
/*     */     } 
/*  93 */     HashMap<String, String> hashMap2 = getAllCptCodeMap();
/*  94 */     HashSet<String> hashSet1 = getInventoryCptSet();
/*  95 */     HashSet<String> hashSet2 = getInventoryListSet(2);
/*     */     
/*  97 */     for (byte b = 1; b < i + 1; b++) {
/*  98 */       hSSFRow = hSSFSheet.getRow(b);
/*  99 */       JSONObject jSONObject1 = new JSONObject();
/* 100 */       JSONObject jSONObject2 = new JSONObject();
/* 101 */       String str1 = getCellValue(hSSFRow.getCell(0), 1, b + 1, 1);
/* 102 */       if (!str1.equals("") && hashSet2.contains(str1)) {
/* 103 */         this.msg1.add("" + (b + 1));
/* 104 */         this.msg2.add("1");
/* 105 */         this.msg3.add(SystemEnv.getHtmlLabelName(510476, paramUser.getLanguage()));
/*     */       } 
/* 107 */       String str2 = getCellValue(hSSFRow.getCell(1), 1, b + 1, 2);
/* 108 */       String str3 = getCellValue(hSSFRow.getCell(2), 1, b + 1, 3);
/* 109 */       if (!str3.equals("")) {
/* 110 */         if (Util.getDoubleValue(str3, -1.0D) > -1.0D) {
/* 111 */           str3 = (int)Util.getDoubleValue(str3) + "";
/*     */         }
/* 113 */         if (hashMap1.containsKey(str3)) {
/* 114 */           str3 = hashMap1.get(str3);
/*     */         } else {
/* 116 */           this.msg1.add("" + (b + 1));
/* 117 */           this.msg2.add("3");
/* 118 */           this.msg3.add(SystemEnv.getHtmlLabelName(506225, paramUser.getLanguage()));
/*     */         } 
/*     */       } 
/*     */       
/* 122 */       String str4 = getCellValue(hSSFRow.getCell(3), 0, b + 1, 4);
/* 123 */       String str5 = "";
/*     */       try {
/* 125 */         if (!str4.equals("")) {
/* 126 */           String[] arrayOfString = Util.TokenizerString2(str4, ",");
/* 127 */           String str6 = "";
/* 128 */           String str7 = "";
/* 129 */           for (String str : arrayOfString) {
/*     */             
/* 131 */             if (hashMap2.containsKey(str)) {
/* 132 */               if (hashSet1.contains(str)) {
/* 133 */                 str7 = str7 + str + ",";
/*     */               } else {
/* 135 */                 str5 = str5 + (String)hashMap2.get(str) + ",";
/* 136 */                 hashSet1.add(str);
/*     */               } 
/*     */             } else {
/* 139 */               str6 = str6 + str + ",";
/*     */             } 
/*     */           } 
/* 142 */           str5 = str5.equals("") ? "" : str5.substring(0, str5.length() - 1);
/*     */           
/* 144 */           String str8 = str6.equals("") ? "" : (str6.substring(0, str6.length() - 1) + SystemEnv.getHtmlLabelName(510477, paramUser.getLanguage()));
/* 145 */           str8 = str8 + (str7.equals("") ? "" : (str7.substring(0, str7.length() - 1) + " " + SystemEnv.getHtmlLabelName(510767, paramUser.getLanguage())));
/* 146 */           if (!str8.equals("")) {
/* 147 */             this.msg1.add("" + (b + 1));
/* 148 */             this.msg2.add("4");
/* 149 */             this.msg3.add(str8);
/*     */           } 
/*     */         } 
/* 152 */       } catch (Exception exception) {
/* 153 */         this.msg1.add("" + (b + 1));
/* 154 */         this.msg2.add("4");
/* 155 */         this.msg3.add(" ");
/*     */       } 
/*     */       
/*     */       try {
/* 159 */         jSONObject1.put("listmark", str1);
/* 160 */         jSONObject1.put("listname", str2);
/* 161 */         jSONObject1.put("counter", str3);
/* 162 */         jSONObject1.put("cptmarks", str5);
/*     */       }
/* 164 */       catch (Exception exception) {
/* 165 */         exception.printStackTrace();
/*     */       } 
/* 167 */       this.jsonArray.put(jSONObject1);
/* 168 */       this.jsoncusArray.put(jSONObject2);
/*     */     } 
/* 170 */     if (this.msg1.size() > 0) {
/* 171 */       this.msgType = "e2";
/* 172 */       return false;
/*     */     } 
/* 174 */     return true;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void ExcelToDB1(JSONArray paramJSONArray) {
/* 182 */     if (paramJSONArray != null && paramJSONArray.length() > 0) {
/*     */       try {
/* 184 */         RecordSet recordSet = new RecordSet();
/* 185 */         HashSet<String> hashSet = getInventoryListSet(1);
/* 186 */         HashMap<String, String> hashMap = getAllCptCodeMap();
/* 187 */         String[] arrayOfString = null;
/*     */         
/* 189 */         for (byte b = 0; b < paramJSONArray.length(); b++) {
/* 190 */           JSONObject jSONObject = (JSONObject)paramJSONArray.get(b);
/* 191 */           String str1 = Util.null2String(jSONObject.getString("listmark"));
/* 192 */           String str2 = Util.null2String(jSONObject.getString("listname"));
/* 193 */           String str3 = Util.null2String(jSONObject.getString("counter"));
/* 194 */           String str4 = Util.null2String(jSONObject.getString("cptmarks"));
/* 195 */           if (!hashSet.contains(str1)) {
/* 196 */             recordSet.execute("insert into cpt_inventory_planlist(mainid,name,mark,description,countuser,planliststate,totalnum,uncountnum,countednum) values (" + this.planId + ",'" + str2 + "','" + str1 + "','','" + str3 + "',0,0,0,0)");
/*     */             
/* 198 */             hashSet.add(str1);
/*     */           } 
/* 200 */           recordSet.executeQuery("select id,totalnum,uncountnum from  cpt_inventory_planlist where mark=?", new Object[] { str1 });
/* 201 */           if (recordSet.next()) {
/* 202 */             int i = recordSet.getInt("id");
/* 203 */             double d1 = recordSet.getDouble("totalnum");
/* 204 */             double d2 = recordSet.getDouble("uncountnum");
/* 205 */             if (!str4.equals("")) {
/* 206 */               arrayOfString = Util.TokenizerString2(str4, ",");
/* 207 */               for (String str : arrayOfString) {
/* 208 */                 recordSet.execute("insert into cpt_inventory_detail (planid,mainid,cptid,detailstate,cptname) values (" + this.planId + "," + i + "," + str + ",0, '" + (String)hashMap
/* 209 */                     .get(str + "_name") + "')");
/* 210 */                 d1++;
/* 211 */                 d2++;
/*     */               } 
/* 213 */               recordSet.executeUpdate("update  cpt_inventory_planlist set totalnum=?,uncountnum=? where mark=?", new Object[] { Double.valueOf(d1), Double.valueOf(d2), str1 });
/*     */             } 
/*     */           } 
/*     */         } 
/* 217 */       } catch (Exception exception) {
/* 218 */         this.logBean.writeLog("tagtag inventoryList import exception:" + exception.getMessage());
/*     */       } 
/*     */     }
/*     */   }
/*     */   
/* 223 */   private JSONArray jsonArray = new JSONArray();
/* 224 */   private JSONArray jsoncusArray = new JSONArray();
/* 225 */   private BaseBean logBean = new BaseBean();
/*     */   private String msgType;
/*     */   
/*     */   public String getMsgType() {
/* 229 */     return this.msgType;
/*     */   }
/*     */   
/* 232 */   private Vector msg1 = new Vector();
/* 233 */   private Vector msg2 = new Vector();
/* 234 */   private Vector msg3 = new Vector();
/*     */   public Vector getMsg1() {
/* 236 */     return this.msg1;
/*     */   }
/*     */   
/*     */   public Vector getMsg2() {
/* 240 */     return this.msg2;
/*     */   }
/*     */   
/*     */   public Vector getMsg3() {
/* 244 */     return this.msg3;
/*     */   }
/*     */   
/*     */   private String getCellValue(HSSFCell paramHSSFCell, int paramInt1, int paramInt2, int paramInt3) {
/* 248 */     String str = "";
/* 249 */     if (paramHSSFCell == null && paramInt1 == 1) {
/* 250 */       this.msg1.add("" + paramInt2);
/* 251 */       this.msg2.add("" + paramInt3);
/* 252 */       this.msg3.add("" + SystemEnv.getHtmlLabelName(384148, this.languageId));
/* 253 */       return "";
/* 254 */     }  if (paramHSSFCell == null) {
/* 255 */       return "";
/*     */     }
/*     */     try {
/* 258 */       switch (paramHSSFCell.getCellType()) {
/*     */         
/*     */         case NUMERIC:
/* 261 */           if (HSSFDateUtil.isCellDateFormatted((Cell)paramHSSFCell)) {
/* 262 */             str = DateFormat.getDateInstance().format(paramHSSFCell.getDateCellValue()).toString().trim(); break;
/*     */           } 
/* 264 */           str = String.valueOf(paramHSSFCell.getNumericCellValue());
/*     */           break;
/*     */         
/*     */         case STRING:
/* 268 */           str = paramHSSFCell.getStringCellValue().trim();
/*     */           break;
/*     */         case FORMULA:
/* 271 */           str = DateFormat.getDateInstance().format(paramHSSFCell.getDateCellValue()).toString().trim();
/*     */           break;
/*     */       } 
/*     */ 
/*     */     
/* 276 */     } catch (Exception exception) {
/* 277 */       exception.printStackTrace();
/*     */     } 
/* 279 */     if (paramInt1 == 1 && str.equals("")) {
/* 280 */       this.msg1.add("" + paramInt2);
/* 281 */       this.msg2.add("" + paramInt3);
/* 282 */       this.msg3.add("" + SystemEnv.getHtmlLabelName(384148, this.languageId));
/*     */     } 
/* 284 */     str = Util.null2String(str).trim();
/* 285 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private HashMap<String, String> getHrmMap() {
/* 295 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 296 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 297 */     LanguageComInfo languageComInfo = null;
/*     */     try {
/* 299 */       languageComInfo = new LanguageComInfo();
/* 300 */     } catch (Exception exception) {
/* 301 */       exception.printStackTrace();
/*     */     } 
/* 303 */     RecordSet recordSet = new RecordSet();
/* 304 */     String str = "select id,lastname from hrmresource where status<4 order by id ";
/* 305 */     recordSet.executeQuery(str, new Object[0]);
/* 306 */     while (recordSet.next()) {
/* 307 */       String str1 = Util.null2String(recordSet.getString("lastname"));
/* 308 */       while (languageComInfo.next()) {
/* 309 */         hashMap1.put(Util.formatMultiLang(str1, languageComInfo.getLanguageid()), recordSet.getString("id"));
/*     */       }
/* 311 */       hashMap2.put(recordSet.getString("id"), recordSet.getString("id"));
/*     */     } 
/* 313 */     hashMap1.putAll(hashMap2);
/* 314 */     return (HashMap)hashMap1;
/*     */   }
/*     */   private HashSet<String> getInventoryCptSet() {
/* 317 */     HashSet<String> hashSet = new HashSet();
/* 318 */     RecordSet recordSet = new RecordSet();
/* 319 */     String str = "";
/* 320 */     str = "select t1.cptid,t2.mark from cpt_inventory_detail t1 left join  cptcapital t2 on t1.cptid=t2.id where planid=?";
/* 321 */     recordSet.executeQuery(str, new Object[] { Integer.valueOf(this.planId) });
/*     */     
/* 323 */     while (recordSet.next()) {
/* 324 */       String str1 = Util.null2String(recordSet.getString("mark"));
/* 325 */       hashSet.add(str1);
/*     */     } 
/* 327 */     return hashSet;
/*     */   }
/*     */   private HashMap<String, String> getAllCptCodeMap() {
/* 330 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 331 */     RecordSet recordSet = new RecordSet();
/* 332 */     String str = "";
/* 333 */     str = "select id,mark,name from cptcapital where isdata=2 ";
/* 334 */     recordSet.executeQuery(str, new Object[0]);
/* 335 */     while (recordSet.next()) {
/* 336 */       String str1 = Util.null2String(recordSet.getString("id"));
/* 337 */       String str2 = Util.null2String(recordSet.getString("mark"));
/* 338 */       String str3 = Util.null2String(recordSet.getString("name"));
/* 339 */       hashMap.put(str2, str1);
/* 340 */       hashMap.put(str1 + "_name", str3);
/*     */     } 
/* 342 */     return (HashMap)hashMap;
/*     */   }
/*     */   
/*     */   private HashSet<String> getInventoryListSet(int paramInt) {
/* 346 */     HashSet<String> hashSet = new HashSet();
/* 347 */     RecordSet recordSet = new RecordSet();
/* 348 */     String str = "";
/* 349 */     if (paramInt == 1) {
/* 350 */       str = "select id,mark from cpt_inventory_planlist where mainid =?";
/*     */     } else {
/* 352 */       str = "select id,mark from cpt_inventory_planlist where mainid !=?";
/*     */     } 
/* 354 */     recordSet.executeQuery(str, new Object[] { Integer.valueOf(this.planId) });
/*     */     
/* 356 */     while (recordSet.next()) {
/* 357 */       String str1 = Util.null2String(recordSet.getString("mark"));
/* 358 */       hashSet.add(str1);
/*     */     } 
/* 360 */     return hashSet;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/cpt/util/CptInventoryListImpUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */