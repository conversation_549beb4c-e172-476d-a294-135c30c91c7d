/*     */ package weaver.cpt.util;
/*     */ 
/*     */ import org.json.JSONArray;
/*     */ import org.json.JSONObject;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.TimeUtil;
/*     */ import weaver.hrm.User;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CptDetailColumnUtil
/*     */   extends BaseBean
/*     */ {
/*  20 */   private CptFieldManager cptFieldManager = new CptFieldManager();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public JSONArray getDetailColumnConf(String paramString, User paramUser) {
/*  28 */     JSONArray jSONArray = new JSONArray();
/*  29 */     JSONObject jSONObject = new JSONObject();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*     */     try {
/*  54 */       if ("CptStockInDetail".equalsIgnoreCase(paramString)) {
/*     */         
/*  56 */         jSONObject = new JSONObject();
/*  57 */         jSONObject.put("width", "10%");
/*  58 */         jSONObject.put("colname", SystemEnv.getHtmlLabelName(1509, paramUser.getLanguage()));
/*  59 */         jSONObject.put("itemhtml", "<span class='browser' completeurl='/data.jsp?type=23&isdata=1&inculdeNumZero=1' browserurl='/systeminfo/BrowserMain.jsp?url=/cpt/capital/CapitalBrowser.jsp?sqlwhere=where isdata=1&inculdeNumZero=1' name='capitalid' isMustInput='2' hasInput='true'  isSingle='true' _callback='loadinfo' ></span>");
/*  60 */         jSONArray.put(jSONObject);
/*     */         
/*  62 */         jSONObject = new JSONObject();
/*  63 */         jSONObject.put("width", "10%");
/*  64 */         jSONObject.put("colname", SystemEnv.getHtmlLabelName(904, paramUser.getLanguage()));
/*  65 */         jSONObject.put("itemhtml", "<input type='text' name='capitalspec' id='capitalspec' /><span class='mustinput'></span>");
/*  66 */         jSONArray.put(jSONObject);
/*     */ 
/*     */         
/*  69 */         jSONObject = new JSONObject();
/*  70 */         jSONObject.put("width", "10%");
/*  71 */         jSONObject.put("colname", SystemEnv.getHtmlLabelName(1330, paramUser.getLanguage()));
/*  72 */         jSONObject.put("itemhtml", "<input type='text' name='price' maxLength='10' id='price' onkeyup=\"value=value.replace(/[^\\d\\.]/g,'')\"  /><span class='mustinput'></span>");
/*  73 */         jSONArray.put(jSONObject);
/*     */         
/*  75 */         jSONObject = new JSONObject();
/*  76 */         jSONObject.put("width", "10%");
/*  77 */         jSONObject.put("colname", SystemEnv.getHtmlLabelName(1331, paramUser.getLanguage()));
/*  78 */         jSONObject.put("itemhtml", "<input type='text' name='capitalnum' maxLength='7' id='capitalnum' onkeyup=\"value=value.replace(/[^\\d\\.]/g,'')\" /><span class='mustinput'></span>");
/*  79 */         jSONArray.put(jSONObject);
/*     */         
/*  81 */         jSONObject = new JSONObject();
/*  82 */         jSONObject.put("width", "10%");
/*  83 */         jSONObject.put("colname", SystemEnv.getHtmlLabelName(900, paramUser.getLanguage()));
/*  84 */         jSONObject.put("itemhtml", "<input type='text' name='invoice' id='invoice' />");
/*  85 */         jSONArray.put(jSONObject);
/*     */         
/*  87 */         jSONObject = new JSONObject();
/*  88 */         jSONObject.put("width", "10%");
/*  89 */         jSONObject.put("colname", SystemEnv.getHtmlLabelName(1387, paramUser.getLanguage()));
/*  90 */         jSONObject.put("itemhtml", "<input type='text' name='location' id='location' />");
/*  91 */         jSONArray.put(jSONObject);
/*     */       
/*     */       }
/*  94 */       else if ("CptUse".equalsIgnoreCase(paramString)) {
/*     */         
/*  96 */         jSONObject = new JSONObject();
/*  97 */         jSONObject.put("width", "15%");
/*  98 */         jSONObject.put("colname", SystemEnv.getHtmlLabelName(1412, paramUser.getLanguage()));
/*  99 */         jSONObject.put("itemhtml", "<input type='hidden' name='StockInDate' value='" + TimeUtil.getCurrentDateString() + "'   class='wuiDate' style='width:90px!important;'  _span='StockInDate_span' _button='StockInDate_btn' _callback='' >");
/* 100 */         jSONArray.put(jSONObject);
/*     */         
/* 102 */         jSONObject = new JSONObject();
/* 103 */         jSONObject.put("width", "15%");
/* 104 */         jSONObject.put("colname", SystemEnv.getHtmlLabelName(15312, paramUser.getLanguage()));
/* 105 */         jSONObject.put("itemhtml", "<span class='browser' completeurl='/data.jsp?type=23&cptstateid=1&cptuse=1&inculdeNumZero=0' browserurl='/systeminfo/BrowserMain.jsp?url=/cpt/capital/CapitalBrowser.jsp?sqlwhere=where isdata=2&cptstateid=1&cptuse=1&inculdeNumZero=0' name='capitalid' hasInput='true' isMustInput='2' isSingle='true'  _callback='loadinfo'  ></span>");
/* 106 */         jSONArray.put(jSONObject);
/*     */         
/* 108 */         jSONObject = new JSONObject();
/* 109 */         jSONObject.put("width", "15%");
/* 110 */         jSONObject.put("colname", SystemEnv.getHtmlLabelName(368, paramUser.getLanguage()));
/* 111 */         jSONObject.put("itemhtml", "<span class='browser' completeurl='/data.jsp' browserurl='/systeminfo/BrowserMain.jsp?url=/hrm/resource/ResourceBrowser.jsp' name='hrmid' hasInput='true' isMustInput='2' isSingle='true'></span>");
/* 112 */         jSONArray.put(jSONObject);
/*     */         
/* 114 */         jSONObject = new JSONObject();
/* 115 */         jSONObject.put("width", "10%");
/* 116 */         jSONObject.put("colname", SystemEnv.getHtmlLabelName(904, paramUser.getLanguage()));
/* 117 */         jSONObject.put("itemhtml", "<span id='capitalspec_span' name='capitalspec_span'></span>");
/* 118 */         jSONArray.put(jSONObject);
/*     */         
/* 120 */         jSONObject = new JSONObject();
/* 121 */         jSONObject.put("width", "5%");
/* 122 */         jSONObject.put("colname", SystemEnv.getHtmlLabelName(714, paramUser.getLanguage()));
/* 123 */         jSONObject.put("itemhtml", "<span id='code_span' name='code_span'></span>");
/* 124 */         jSONArray.put(jSONObject);
/*     */         
/* 126 */         jSONObject = new JSONObject();
/* 127 */         jSONObject.put("width", "10%");
/* 128 */         jSONObject.put("colname", SystemEnv.getHtmlLabelName(1446, paramUser.getLanguage()));
/* 129 */         jSONObject.put("itemhtml", "<span id='capitalcount_span' name='capitalcount_span'></span>");
/* 130 */         jSONArray.put(jSONObject);
/*     */         
/* 132 */         jSONObject = new JSONObject();
/* 133 */         jSONObject.put("width", "8%");
/* 134 */         jSONObject.put("colname", SystemEnv.getHtmlLabelName(15313, paramUser.getLanguage()));
/* 135 */         jSONObject.put("itemhtml", "<input type='text' name='capitalnum' id='capitalnum' size='5' maxlength='5' onkeyup=\"value=value.replace(/[^\\d\\.]/g,'')\" /><span class='mustinput'></span>");
/* 136 */         jSONArray.put(jSONObject);
/*     */         
/* 138 */         jSONObject = new JSONObject();
/* 139 */         jSONObject.put("width", "8%");
/* 140 */         jSONObject.put("colname", SystemEnv.getHtmlLabelName(1387, paramUser.getLanguage()));
/* 141 */         jSONObject.put("itemhtml", "<input type='text' name='location' id='location' size='10' maxlength='10' />");
/* 142 */         jSONArray.put(jSONObject);
/*     */         
/* 144 */         jSONObject = new JSONObject();
/* 145 */         jSONObject.put("width", "10%");
/* 146 */         jSONObject.put("colname", SystemEnv.getHtmlLabelName(454, paramUser.getLanguage()));
/* 147 */         jSONObject.put("itemhtml", "<input type='text' name='remark' id='remark' />");
/* 148 */         jSONArray.put(jSONObject);
/*     */       }
/* 150 */       else if ("CptMove".equalsIgnoreCase(paramString)) {
/*     */         
/* 152 */         jSONObject = new JSONObject();
/* 153 */         jSONObject.put("width", "15%");
/* 154 */         jSONObject.put("colname", SystemEnv.getHtmlLabelName(15310, paramUser.getLanguage()));
/* 155 */         jSONObject.put("itemhtml", "<span class='browser' completeurl='/data.jsp' browserurl='/systeminfo/BrowserMain.jsp?url=/hrm/resource/ResourceBrowser.jsp' name='hrmid' hasInput='true' isMustInput='2' isSingle='true' _callback=''></span>");
/* 156 */         jSONArray.put(jSONObject);
/*     */         
/* 158 */         jSONObject = new JSONObject();
/* 159 */         jSONObject.put("width", "15%");
/* 160 */         jSONObject.put("colname", SystemEnv.getHtmlLabelName(15311, paramUser.getLanguage()));
/* 161 */         jSONObject.put("itemhtml", "<span class='browser' completeurl='/data.jsp?type=4' browserurl='/systeminfo/BrowserMain.jsp?url=/hrm/company/DepartmentBrowser.jsp' name='CptDept_to' isMustInput='2' hasInput='true' isSingle='true' _callback=''></span>");
/* 162 */         jSONArray.put(jSONObject);
/*     */ 
/*     */         
/* 165 */         jSONObject = new JSONObject();
/* 166 */         jSONObject.put("width", "15%");
/* 167 */         jSONObject.put("colname", SystemEnv.getHtmlLabelName(15309, paramUser.getLanguage()));
/* 168 */         jSONObject.put("itemhtml", "<span class='browser' completeurl='/data.jsp?type=23&cptstateid=2&inculdeNumZero=0' browserurl='/systeminfo/BrowserMain.jsp?url=/cpt/capital/CapitalBrowser.jsp?sqlwhere=where isdata=2&cptstateid=2&inculdeNumZero=0' name='capitalid' hasInput='true' isMustInput='2' isSingle='true' _callback='loadinfo'></span>");
/* 169 */         jSONArray.put(jSONObject);
/*     */         
/* 171 */         jSONObject = new JSONObject();
/* 172 */         jSONObject.put("width", "10%");
/* 173 */         jSONObject.put("colname", SystemEnv.getHtmlLabelName(503077, paramUser.getLanguage()));
/* 174 */         jSONObject.put("itemhtml", "<span id='curdept_span' name='curdept_span'></span>");
/* 175 */         jSONArray.put(jSONObject);
/*     */         
/* 177 */         jSONObject = new JSONObject();
/* 178 */         jSONObject.put("width", "10%");
/* 179 */         jSONObject.put("colname", SystemEnv.getHtmlLabelName(503035, paramUser.getLanguage()));
/* 180 */         jSONObject.put("itemhtml", "<span id='curresource_span' name='curresource_span'></span>");
/* 181 */         jSONArray.put(jSONObject);
/*     */         
/* 183 */         jSONObject = new JSONObject();
/* 184 */         jSONObject.put("width", "10%");
/* 185 */         jSONObject.put("colname", SystemEnv.getHtmlLabelName(904, paramUser.getLanguage()));
/* 186 */         jSONObject.put("itemhtml", "<span id='capitalspec_span' name='capitalspec_span'></span>");
/* 187 */         jSONArray.put(jSONObject);
/*     */         
/* 189 */         jSONObject = new JSONObject();
/* 190 */         jSONObject.put("width", "10%");
/* 191 */         jSONObject.put("colname", SystemEnv.getHtmlLabelName(1387, paramUser.getLanguage()));
/* 192 */         jSONObject.put("itemhtml", "<input type='text' name='location' id='location' size='10' maxlength='10' />");
/* 193 */         jSONArray.put(jSONObject);
/*     */         
/* 195 */         jSONObject = new JSONObject();
/* 196 */         jSONObject.put("width", "30%");
/* 197 */         jSONObject.put("colname", SystemEnv.getHtmlLabelName(454, paramUser.getLanguage()));
/* 198 */         jSONObject.put("itemhtml", "<input type='text' name='remark' id='remark' />");
/* 199 */         jSONArray.put(jSONObject);
/*     */       }
/* 201 */       else if ("CptLend".equalsIgnoreCase(paramString)) {
/*     */         
/* 203 */         jSONObject = new JSONObject();
/* 204 */         jSONObject.put("width", "15%");
/* 205 */         jSONObject.put("colname", SystemEnv.getHtmlLabelName(503028, paramUser.getLanguage()));
/* 206 */         jSONObject.put("itemhtml", "<span class='browser' completeurl='/data.jsp' browserurl='/systeminfo/BrowserMain.jsp?url=/hrm/resource/ResourceBrowser.jsp' name='hrmid' hasInput='true' isMustInput='2' isSingle='true' _callback=''></span>");
/* 207 */         jSONArray.put(jSONObject);
/*     */         
/* 209 */         jSONObject = new JSONObject();
/* 210 */         jSONObject.put("width", "15%");
/* 211 */         jSONObject.put("colname", SystemEnv.getHtmlLabelName(1404, paramUser.getLanguage()));
/* 212 */         jSONObject.put("itemhtml", "<input name='StockInDate' type='hidden' value='" + TimeUtil.getCurrentDateString() + "'   class='wuiDate' style='width:90px!important;'  _span='StockInDate_span' _button='StockInDate_btn' _callback='' >");
/* 213 */         jSONArray.put(jSONObject);
/*     */         
/* 215 */         jSONObject = new JSONObject();
/* 216 */         jSONObject.put("width", "15%");
/* 217 */         jSONObject.put("colname", SystemEnv.getHtmlLabelName(503027, paramUser.getLanguage()));
/* 218 */         jSONObject.put("itemhtml", "<span class='browser' completeurl='/data.jsp?type=23&cptstateid=1&cptsptcount=1&inculdeNumZero=0' browserurl='/systeminfo/BrowserMain.jsp?url=/cpt/capital/CapitalBrowser.jsp?sqlwhere=where isdata=2&cptstateid=1&cptsptcount=1&inculdeNumZero=0' name='capitalid' hasInput='true' isMustInput='2' isSingle='true' _callback='loadinfo'></span>");
/* 219 */         jSONArray.put(jSONObject);
/*     */         
/* 221 */         jSONObject = new JSONObject();
/* 222 */         jSONObject.put("width", "10%");
/* 223 */         jSONObject.put("colname", SystemEnv.getHtmlLabelName(15393, paramUser.getLanguage()));
/* 224 */         jSONObject.put("itemhtml", "<span id='curdept_span' name='curdept_span'></span>");
/* 225 */         jSONArray.put(jSONObject);
/*     */         
/* 227 */         jSONObject = new JSONObject();
/* 228 */         jSONObject.put("width", "10%");
/* 229 */         jSONObject.put("colname", SystemEnv.getHtmlLabelName(503035, paramUser.getLanguage()));
/* 230 */         jSONObject.put("itemhtml", "<span id='curresource_span' name='curresource_span'></span>");
/* 231 */         jSONArray.put(jSONObject);
/*     */         
/* 233 */         jSONObject = new JSONObject();
/* 234 */         jSONObject.put("width", "10%");
/* 235 */         jSONObject.put("colname", SystemEnv.getHtmlLabelName(904, paramUser.getLanguage()));
/* 236 */         jSONObject.put("itemhtml", "<span id='capitalspec_span' name='capitalspec_span'></span>");
/* 237 */         jSONArray.put(jSONObject);
/*     */         
/* 239 */         jSONObject = new JSONObject();
/* 240 */         jSONObject.put("width", "10%");
/* 241 */         jSONObject.put("colname", SystemEnv.getHtmlLabelName(1387, paramUser.getLanguage()));
/* 242 */         jSONObject.put("itemhtml", "<input type='text' name='location' id='location' size='10' maxlength='10' />");
/* 243 */         jSONArray.put(jSONObject);
/*     */         
/* 245 */         jSONObject = new JSONObject();
/* 246 */         jSONObject.put("width", "30%");
/* 247 */         jSONObject.put("colname", SystemEnv.getHtmlLabelName(454, paramUser.getLanguage()));
/* 248 */         jSONObject.put("itemhtml", "<input type='text' name='remark' id='remark' />");
/* 249 */         jSONArray.put(jSONObject);
/*     */       }
/* 251 */       else if ("CptLoss".equalsIgnoreCase(paramString)) {
/*     */         
/* 253 */         jSONObject = new JSONObject();
/* 254 */         jSONObject.put("width", "15%");
/* 255 */         jSONObject.put("colname", SystemEnv.getHtmlLabelName(17482, paramUser.getLanguage()));
/* 256 */         jSONObject.put("itemhtml", "<span class='browser' completeurl='/data.jsp' browserurl='/systeminfo/BrowserMain.jsp?url=/hrm/resource/ResourceBrowser.jsp' name='operator' hasInput='true' isMustInput='1' isSingle='true' _callback=''></span>");
/* 257 */         jSONArray.put(jSONObject);
/*     */         
/* 259 */         jSONObject = new JSONObject();
/* 260 */         jSONObject.put("width", "15%");
/* 261 */         jSONObject.put("colname", SystemEnv.getHtmlLabelName(1406, paramUser.getLanguage()));
/* 262 */         jSONObject.put("itemhtml", "<input name='StockInDate' type='hidden' value='" + TimeUtil.getCurrentDateString() + "'   class='wuiDate' style='width:90px!important;'  _span='StockInDate_span' _button='StockInDate_btn' _callback='' >");
/* 263 */         jSONArray.put(jSONObject);
/*     */         
/* 265 */         jSONObject = new JSONObject();
/* 266 */         jSONObject.put("width", "15%");
/* 267 */         jSONObject.put("colname", SystemEnv.getHtmlLabelName(503031, paramUser.getLanguage()));
/* 268 */         jSONObject.put("itemhtml", "<span class='browser' completeurl='/data.jsp?type=23&cptstateid=1,2,3,4&inculdeNumZero=0' browserurl='/systeminfo/BrowserMain.jsp?url=/cpt/capital/CapitalBrowser.jsp?cptstateid=0,1,2,3,4&inculdeNumZero=0' name='capitalid' hasInput='true' isMustInput='2' isSingle='true' _callback='loadinfo'></span>");
/* 269 */         jSONArray.put(jSONObject);
/*     */         
/* 271 */         jSONObject = new JSONObject();
/* 272 */         jSONObject.put("width", "10%");
/* 273 */         jSONObject.put("colname", SystemEnv.getHtmlLabelName(15393, paramUser.getLanguage()));
/* 274 */         jSONObject.put("itemhtml", "<span id='curdept_span' name='curdept_span'></span>");
/* 275 */         jSONArray.put(jSONObject);
/*     */         
/* 277 */         jSONObject = new JSONObject();
/* 278 */         jSONObject.put("width", "10%");
/* 279 */         jSONObject.put("colname", SystemEnv.getHtmlLabelName(904, paramUser.getLanguage()));
/* 280 */         jSONObject.put("itemhtml", "<span id='capitalspec_span' name='capitalspec_span'></span>");
/* 281 */         jSONArray.put(jSONObject);
/*     */ 
/*     */         
/* 284 */         jSONObject = new JSONObject();
/* 285 */         jSONObject.put("width", "10%");
/* 286 */         jSONObject.put("colname", SystemEnv.getHtmlLabelName(1451, paramUser.getLanguage()));
/* 287 */         jSONObject.put("itemhtml", "<span id='capitalcount_span' name='capitalcount_span'></span>");
/* 288 */         jSONArray.put(jSONObject);
/*     */         
/* 290 */         jSONObject = new JSONObject();
/* 291 */         jSONObject.put("width", "8%");
/* 292 */         jSONObject.put("colname", SystemEnv.getHtmlLabelName(503032, paramUser.getLanguage()));
/* 293 */         jSONObject.put("itemhtml", "<input type='text' name='capitalnum' id='capitalnum' size='5' maxlength='5' onkeyup=\"value=value.replace(/[^\\d\\.]/g,'')\" /><span class='mustinput'></span>");
/* 294 */         jSONArray.put(jSONObject);
/*     */         
/* 296 */         jSONObject = new JSONObject();
/* 297 */         jSONObject.put("width", "8%");
/* 298 */         jSONObject.put("colname", SystemEnv.getHtmlLabelName(1393, paramUser.getLanguage()));
/* 299 */         jSONObject.put("itemhtml", "<input type='text' name='cost' id='cost' size='10' maxlength='10' onkeyup=\"value=value.replace(/[^\\d\\.]/g,'')\" />");
/* 300 */         jSONArray.put(jSONObject);
/*     */         
/* 302 */         jSONObject = new JSONObject();
/* 303 */         jSONObject.put("width", "10%");
/* 304 */         jSONObject.put("colname", SystemEnv.getHtmlLabelName(454, paramUser.getLanguage()));
/* 305 */         jSONObject.put("itemhtml", "<input type='text' name='remark' id='remark' />");
/* 306 */         jSONArray.put(jSONObject);
/*     */       }
/* 308 */       else if ("CptDiscard".equalsIgnoreCase(paramString)) {
/*     */         
/* 310 */         jSONObject = new JSONObject();
/* 311 */         jSONObject.put("width", "15%");
/* 312 */         jSONObject.put("colname", SystemEnv.getHtmlLabelName(17482, paramUser.getLanguage()));
/* 313 */         jSONObject.put("itemhtml", "<span class='browser' completeurl='/data.jsp' browserurl='/systeminfo/BrowserMain.jsp?url=/hrm/resource/ResourceBrowser.jsp' name='operator' hasInput='true' isMustInput='1' isSingle='true' _callback=''></span>");
/* 314 */         jSONArray.put(jSONObject);
/*     */         
/* 316 */         jSONObject = new JSONObject();
/* 317 */         jSONObject.put("width", "15%");
/* 318 */         jSONObject.put("colname", SystemEnv.getHtmlLabelName(1392, paramUser.getLanguage()));
/* 319 */         jSONObject.put("itemhtml", "<input name='StockInDate' type='hidden' value='" + TimeUtil.getCurrentDateString() + "'   class='wuiDate' style='width:90px!important;'  _span='StockInDate_span' _button='StockInDate_btn' _callback='' >");
/* 320 */         jSONArray.put(jSONObject);
/*     */         
/* 322 */         jSONObject = new JSONObject();
/* 323 */         jSONObject.put("width", "15%");
/* 324 */         jSONObject.put("colname", SystemEnv.getHtmlLabelName(21545, paramUser.getLanguage()));
/* 325 */         jSONObject.put("itemhtml", "<span class='browser' completeurl='/data.jsp?type=23&cptstateid=1,2,3,4&inculdeNumZero=0' browserurl='/systeminfo/BrowserMain.jsp?url=/cpt/capital/CapitalBrowser.jsp?sqlwhere=where isdata=2&cptstateid=1,2,3,4&inculdeNumZero=0' name='capitalid' hasInput='true' isMustInput='2' isSingle='true' _callback='loadinfo'></span>");
/* 326 */         jSONArray.put(jSONObject);
/*     */         
/* 328 */         jSONObject = new JSONObject();
/* 329 */         jSONObject.put("width", "8%");
/* 330 */         jSONObject.put("colname", SystemEnv.getHtmlLabelName(15393, paramUser.getLanguage()));
/* 331 */         jSONObject.put("itemhtml", "<span id='curdept_span' name='curdept_span'></span>");
/* 332 */         jSONArray.put(jSONObject);
/*     */         
/* 334 */         jSONObject = new JSONObject();
/* 335 */         jSONObject.put("width", "8%");
/* 336 */         jSONObject.put("colname", SystemEnv.getHtmlLabelName(904, paramUser.getLanguage()));
/* 337 */         jSONObject.put("itemhtml", "<span id='capitalspec_span' name='capitalspec_span'></span>");
/* 338 */         jSONArray.put(jSONObject);
/*     */ 
/*     */         
/* 341 */         jSONObject = new JSONObject();
/* 342 */         jSONObject.put("width", "8%");
/* 343 */         jSONObject.put("colname", SystemEnv.getHtmlLabelName(1451, paramUser.getLanguage()));
/* 344 */         jSONObject.put("itemhtml", "<span id='capitalcount_span' name='capitalcount_span'></span>");
/* 345 */         jSONArray.put(jSONObject);
/*     */         
/* 347 */         jSONObject = new JSONObject();
/* 348 */         jSONObject.put("width", "8%");
/* 349 */         jSONObject.put("colname", SystemEnv.getHtmlLabelName(17273, paramUser.getLanguage()));
/* 350 */         jSONObject.put("itemhtml", "<input type='text' name='capitalnum' id='capitalnum' size='5' maxlength='5' onkeyup=\"value=value.replace(/[^\\d\\.]/g,'')\" /><span class='mustinput'></span>");
/* 351 */         jSONArray.put(jSONObject);
/*     */         
/* 353 */         jSONObject = new JSONObject();
/* 354 */         jSONObject.put("width", "8%");
/* 355 */         jSONObject.put("colname", SystemEnv.getHtmlLabelName(1393, paramUser.getLanguage()));
/* 356 */         jSONObject.put("itemhtml", "<input type='text' name='cost' id='cost' size='10' maxlength='10' onkeyup=\"value=value.replace(/[^\\d\\.]/g,'')\" />");
/* 357 */         jSONArray.put(jSONObject);
/*     */         
/* 359 */         jSONObject = new JSONObject();
/* 360 */         jSONObject.put("width", "10%");
/* 361 */         jSONObject.put("colname", SystemEnv.getHtmlLabelName(454, paramUser.getLanguage()));
/* 362 */         jSONObject.put("itemhtml", "<input type='text' name='remark' id='remark' />");
/* 363 */         jSONArray.put(jSONObject);
/*     */       }
/* 365 */       else if ("CptMend".equalsIgnoreCase(paramString)) {
/*     */ 
/*     */         
/* 368 */         jSONObject = new JSONObject();
/* 369 */         jSONObject.put("width", "8%");
/* 370 */         jSONObject.put("colname", SystemEnv.getHtmlLabelName(1047, paramUser.getLanguage()));
/* 371 */         jSONObject.put("itemhtml", "<span class='browser'  completeurl='/data.jsp' browserurl='/systeminfo/BrowserMain.jsp?url=/hrm/resource/ResourceBrowser.jsp' name='operator' hasInput='true' isMustInput='1' isSingle='true' _callback=''></span>");
/* 372 */         jSONArray.put(jSONObject);
/*     */         
/* 374 */         jSONObject = new JSONObject();
/* 375 */         jSONObject.put("width", "8%");
/* 376 */         jSONObject.put("colname", SystemEnv.getHtmlLabelName(1409, paramUser.getLanguage()));
/* 377 */         jSONObject.put("itemhtml", "<input name='menddate' type='hidden' value='" + TimeUtil.getCurrentDateString() + "'   class='wuiDate' style='width:90px!important;'  _span='menddate_span' _button='menddate_btn' _callback='' >");
/* 378 */         jSONArray.put(jSONObject);
/*     */         
/* 380 */         jSONObject = new JSONObject();
/* 381 */         jSONObject.put("width", "8%");
/* 382 */         jSONObject.put("colname", SystemEnv.getHtmlLabelName(22457, paramUser.getLanguage()));
/* 383 */         jSONObject.put("itemhtml", "<input name='mendperioddate' type='hidden' value=''   class='wuiDate' style='width:90px!important;'  _span='mendperioddate_span' _button='mendperioddate_btn' _callback='' >");
/* 384 */         jSONArray.put(jSONObject);
/*     */         
/* 386 */         jSONObject = new JSONObject();
/* 387 */         jSONObject.put("width", "8%");
/* 388 */         jSONObject.put("colname", SystemEnv.getHtmlLabelName(1399, paramUser.getLanguage()));
/* 389 */         jSONObject.put("itemhtml", "<span class='browser' completeurl='/data.jsp?type=7' browserurl='/systeminfo/BrowserMain.jsp?url=/CRM/data/CustomerBrowser.jsp?type=2' name='maintaincompany' hasInput='true' isMustInput='1' isSingle='true' _callback=''></span>");
/* 390 */         jSONArray.put(jSONObject);
/*     */         
/* 392 */         jSONObject = new JSONObject();
/* 393 */         jSONObject.put("width", "8%");
/* 394 */         jSONObject.put("colname", SystemEnv.getHtmlLabelName(33016, paramUser.getLanguage()));
/* 395 */         jSONObject.put("itemhtml", "<span class='browser' completeurl='/data.jsp?type=23&cptstateid=1,2,3&cptsptcount=1&inculdeNumZero=0' browserurl='/systeminfo/BrowserMain.jsp?url=/cpt/capital/CapitalBrowser.jsp?sqlwhere=where isdata=2&cptstateid=1,2,3&cptsptcount=1&inculdeNumZero=0' name='capitalid' hasInput='true' isMustInput='2' isSingle='true' _callback='loadinfo'></span>");
/* 396 */         jSONArray.put(jSONObject);
/*     */         
/* 398 */         jSONObject = new JSONObject();
/* 399 */         jSONObject.put("width", "8%");
/* 400 */         jSONObject.put("colname", SystemEnv.getHtmlLabelName(15393, paramUser.getLanguage()));
/* 401 */         jSONObject.put("itemhtml", "<span  id='curdept_span' name='curdept_span'></span>");
/* 402 */         jSONArray.put(jSONObject);
/*     */         
/* 404 */         jSONObject = new JSONObject();
/* 405 */         jSONObject.put("width", "8%");
/* 406 */         jSONObject.put("colname", SystemEnv.getHtmlLabelName(904, paramUser.getLanguage()));
/* 407 */         jSONObject.put("itemhtml", "<span  id='capitalspec_span' name='capitalspec_span'></span>");
/* 408 */         jSONArray.put(jSONObject);
/*     */         
/* 410 */         jSONObject = new JSONObject();
/* 411 */         jSONObject.put("width", "8%");
/* 412 */         jSONObject.put("colname", SystemEnv.getHtmlLabelName(602, paramUser.getLanguage()));
/* 413 */         jSONObject.put("itemhtml", "<span  id='status_span' name='status_span'></span>");
/* 414 */         jSONArray.put(jSONObject);
/*     */         
/* 416 */         jSONObject = new JSONObject();
/* 417 */         jSONObject.put("width", "6%");
/* 418 */         jSONObject.put("colname", SystemEnv.getHtmlLabelName(1393, paramUser.getLanguage()));
/* 419 */         jSONObject.put("itemhtml", "<input type='text' style='width:50px!important;' name='cost' id='cost' size='10' maxlength='10' onkeyup=\"value=value.replace(/[^\\d\\.]/g,'')\" />");
/* 420 */         jSONArray.put(jSONObject);
/*     */         
/* 422 */         jSONObject = new JSONObject();
/* 423 */         jSONObject.put("width", "6%");
/* 424 */         jSONObject.put("colname", SystemEnv.getHtmlLabelName(454, paramUser.getLanguage()));
/* 425 */         jSONObject.put("itemhtml", "<input type='text' style='width:50px!importan;' name='remark' id='remark' />");
/* 426 */         jSONArray.put(jSONObject);
/*     */       }
/* 428 */       else if ("CptBack".equalsIgnoreCase(paramString)) {
/*     */         
/* 430 */         jSONObject = new JSONObject();
/* 431 */         jSONObject.put("width", "6%");
/* 432 */         jSONObject.put("colname", SystemEnv.getHtmlLabelName(1413, paramUser.getLanguage()));
/* 433 */         jSONObject.put("itemhtml", "<input name='StockInDate' type='hidden' value='" + TimeUtil.getCurrentDateString() + "'   class='wuiDate' style='width:90px!important;'  _span='menddate_span' _button='menddate_btn' _callback='' >");
/* 434 */         jSONArray.put(jSONObject);
/*     */         
/* 436 */         jSONObject = new JSONObject();
/* 437 */         jSONObject.put("width", "15%");
/* 438 */         jSONObject.put("colname", SystemEnv.getHtmlLabelName(503034, paramUser.getLanguage()));
/* 439 */         jSONObject.put("itemhtml", "<span class='browser' completeurl='/data.jsp?type=23&cptstateid=2,3,4&inculdeNumZero=0' browserurl='/systeminfo/BrowserMain.jsp?url=/cpt/capital/CapitalBrowser.jsp?sqlwhere=where isdata=2&cptstateid=2,3,4&inculdeNumZero=0' name='capitalid' hasInput='true' isMustInput='2' isSingle='true' _callback='loadinfo'></span>");
/* 440 */         jSONArray.put(jSONObject);
/*     */         
/* 442 */         jSONObject = new JSONObject();
/* 443 */         jSONObject.put("width", "8%");
/* 444 */         jSONObject.put("colname", SystemEnv.getHtmlLabelName(15393, paramUser.getLanguage()));
/* 445 */         jSONObject.put("itemhtml", "<span id='curdept_span' name='curdept_span'></span>");
/* 446 */         jSONArray.put(jSONObject);
/*     */         
/* 448 */         jSONObject = new JSONObject();
/* 449 */         jSONObject.put("width", "8%");
/* 450 */         jSONObject.put("colname", SystemEnv.getHtmlLabelName(904, paramUser.getLanguage()));
/* 451 */         jSONObject.put("itemhtml", "<span id='capitalspec_span' name='capitalspec_span'></span>");
/* 452 */         jSONArray.put(jSONObject);
/*     */         
/* 454 */         jSONObject = new JSONObject();
/* 455 */         jSONObject.put("width", "8%");
/* 456 */         jSONObject.put("colname", SystemEnv.getHtmlLabelName(602, paramUser.getLanguage()));
/* 457 */         jSONObject.put("itemhtml", "<span id='status_span' name='status_span'></span>");
/* 458 */         jSONArray.put(jSONObject);
/*     */         
/* 460 */         jSONObject = new JSONObject();
/* 461 */         jSONObject.put("width", "8%");
/* 462 */         jSONObject.put("colname", SystemEnv.getHtmlLabelName(503035, paramUser.getLanguage()));
/* 463 */         jSONObject.put("itemhtml", "<span id='curresource_span' name='curresource_span'></span>");
/* 464 */         jSONArray.put(jSONObject);
/*     */         
/* 466 */         jSONObject = new JSONObject();
/* 467 */         jSONObject.put("width", "10%");
/* 468 */         jSONObject.put("colname", SystemEnv.getHtmlLabelName(454, paramUser.getLanguage()));
/* 469 */         jSONObject.put("itemhtml", "<input type='text' name='remark' id='remark' />");
/* 470 */         jSONArray.put(jSONObject);
/*     */       }
/* 472 */       else if ("CptChange".equalsIgnoreCase(paramString)) {
/*     */         
/* 474 */         jSONObject = new JSONObject();
/* 475 */         jSONObject.put("width", "12%");
/* 476 */         jSONObject.put("colname", SystemEnv.getHtmlLabelName(535, paramUser.getLanguage()));
/* 477 */         jSONObject.put("itemhtml", "<span class='browser' completeurl='/data.jsp?type=23&inculdeNumZero=0' browserurl='/systeminfo/BrowserMain.jsp?url=/cpt/capital/CapitalBrowser.jsp?sqlwhere=where isdata=2&inculdeNumZero=0' name='capitalid' hasInput='true' isMustInput='2' isSingle='true' _callback='loadinfo'></span>");
/* 478 */         jSONArray.put(jSONObject);
/*     */         
/* 480 */         jSONObject = new JSONObject();
/* 481 */         jSONObject.put("width", "8%");
/* 482 */         jSONObject.put("colname", SystemEnv.getHtmlLabelName(714, paramUser.getLanguage()));
/* 483 */         jSONObject.put("itemhtml", "<input type='text' name='mark' id='mark' style='width:80px'/>");
/* 484 */         jSONArray.put(jSONObject);
/*     */         
/* 486 */         jSONObject = new JSONObject();
/* 487 */         jSONObject.put("width", "10%");
/* 488 */         jSONObject.put("colname", SystemEnv.getHtmlLabelName(831, paramUser.getLanguage()));
/* 489 */         jSONObject.put("itemhtml", "<span class='browser' completeurl='/data.jsp?type=25' browserurl='/systeminfo/BrowserMain.jsp?url=/cpt/maintenance/CptAssortmentBrowser.jsp' id='capitalgroupid' name='capitalgroupid' hasInput='true' isMustInput='1' isSingle='true'></span>");
/* 490 */         jSONArray.put(jSONObject);
/*     */         
/* 492 */         jSONObject = new JSONObject();
/* 493 */         jSONObject.put("width", "8%");
/* 494 */         jSONObject.put("colname", SystemEnv.getHtmlLabelName(1508, paramUser.getLanguage()));
/* 495 */         jSONObject.put("itemhtml", "<span class='browser' completeurl='/data.jsp?type=1' browserurl='/systeminfo/BrowserMain.jsp?url=/hrm/resource/ResourceBrowser.jsp' id='resourceid' name='resourceid' hasInput='true' isMustInput='1' isSingle='true'></span>");
/* 496 */         jSONArray.put(jSONObject);
/*     */         
/* 498 */         jSONObject = new JSONObject();
/* 499 */         jSONObject.put("width", "10%");
/* 500 */         jSONObject.put("colname", SystemEnv.getHtmlLabelName(904, paramUser.getLanguage()));
/* 501 */         jSONObject.put("itemhtml", "<input type='text' name='capitalspec' id='capitalspec' style='width:80px'/>");
/* 502 */         jSONArray.put(jSONObject);
/*     */         
/* 504 */         jSONObject = new JSONObject();
/* 505 */         jSONObject.put("width", "8%");
/* 506 */         jSONObject.put("colname", SystemEnv.getHtmlLabelName(753, paramUser.getLanguage()));
/* 507 */         jSONObject.put("itemhtml", "<input id='stockindate' name='stockindate' type='hidden' value='' class='wuiDate' style='width:90px!important;'  _span='menddate_span' _button='menddate_btn' _callback='' >");
/* 508 */         jSONArray.put(jSONObject);
/*     */         
/* 510 */         jSONObject = new JSONObject();
/* 511 */         jSONObject.put("width", "10%");
/* 512 */         jSONObject.put("colname", SystemEnv.getHtmlLabelName(1387, paramUser.getLanguage()));
/* 513 */         jSONObject.put("itemhtml", "<input type='text' name='location' id='location' style='width:80px'/>");
/* 514 */         jSONArray.put(jSONObject);
/*     */         
/* 516 */         jSONObject = new JSONObject();
/* 517 */         jSONObject.put("width", "6%");
/* 518 */         jSONObject.put("colname", SystemEnv.getHtmlLabelName(726, paramUser.getLanguage()));
/* 519 */         jSONObject.put("itemhtml", "<input type='text' id='startprice' name='startprice' size=8 onKeyPress=ItemNum_KeyPress() onBlur=checknumber(this.name) value=''>");
/* 520 */         jSONArray.put(jSONObject);
/*     */         
/* 522 */         jSONObject = new JSONObject();
/* 523 */         jSONObject.put("width", "6%");
/* 524 */         jSONObject.put("colname", SystemEnv.getHtmlLabelName(1331, paramUser.getLanguage()));
/* 525 */         jSONObject.put("itemhtml", "<input type='text' id='capitalnum' name='capitalnum' size=8 onKeyPress=ItemNum_KeyPress() onBlur=checknumber_change(this.name) value=''>");
/* 526 */         jSONArray.put(jSONObject);
/*     */         
/* 528 */         jSONObject = new JSONObject();
/* 529 */         jSONObject.put("width", "8%");
/* 530 */         jSONObject.put("colname", SystemEnv.getHtmlLabelName(15393, paramUser.getLanguage()));
/* 531 */         jSONObject.put("itemhtml", "<span class='browser' completeurl='/data.jsp?type=4' browserurl='/systeminfo/BrowserMain.jsp?url=/hrm/company/DepartmentBrowser.jsp' id='blongdepartment' name='blongdepartment' hasInput='true' isMustInput='1' isSingle='true'></span>");
/* 532 */         jSONArray.put(jSONObject);
/*     */         
/* 534 */         jSONObject = new JSONObject();
/* 535 */         jSONObject.put("width", "12%");
/* 536 */         jSONObject.put("colname", SystemEnv.getHtmlLabelName(503036, paramUser.getLanguage()));
/* 537 */         jSONObject.put("itemhtml", "<input type='text' name='remark' id='remark' style='width:100px'/>");
/*     */         
/* 539 */         jSONArray.put(jSONObject);
/* 540 */       } else if ("CptDefineField".equalsIgnoreCase(paramString)) {
/* 541 */         jSONObject = new JSONObject();
/* 542 */         jSONObject.put("width", "10%");
/* 543 */         jSONObject.put("colname", SystemEnv.getHtmlLabelName(124937, paramUser.getLanguage()));
/* 544 */         jSONObject.put("itemhtml", "<input type='text' name='itemDspName' id='itemDspName' size='15' maxlength='30' onblur=\"checkKey(this);checkinput_char_num(this.name);\" /><span class='mustinput'></span>");
/* 545 */         jSONArray.put(jSONObject);
/*     */         
/* 547 */         jSONObject = new JSONObject();
/* 548 */         jSONObject.put("width", "10%");
/* 549 */         jSONObject.put("colname", SystemEnv.getHtmlLabelName(15456, paramUser.getLanguage()));
/* 550 */         jSONObject.put("itemhtml", "<input type='text' name='itemFieldName' id='itemFieldName' onblur=\"checkKey(this);\" /><span class='mustinput'></span>");
/* 551 */         jSONArray.put(jSONObject);
/*     */         
/* 553 */         jSONObject = new JSONObject();
/* 554 */         jSONObject.put("width", "50%");
/* 555 */         jSONObject.put("colname", SystemEnv.getHtmlLabelName(686, paramUser.getLanguage()));
/* 556 */         jSONObject.put("itemhtml", "" + this.cptFieldManager.getItemFieldTypeSelectForAddMainRow2(paramUser));
/* 557 */         jSONArray.put(jSONObject);
/*     */         
/* 559 */         jSONObject = new JSONObject();
/* 560 */         jSONObject.put("width", "10%");
/* 561 */         jSONObject.put("colname", SystemEnv.getHtmlLabelName(18095, paramUser.getLanguage()));
/* 562 */         jSONObject.put("itemhtml", "<input type='checkbox'  name='isopen' id='isopen' value='1' checked>");
/* 563 */         jSONArray.put(jSONObject);
/*     */         
/* 565 */         jSONObject = new JSONObject();
/* 566 */         jSONObject.put("width", "10%");
/* 567 */         jSONObject.put("colname", SystemEnv.getHtmlLabelName(18019, paramUser.getLanguage()));
/* 568 */         jSONObject.put("itemhtml", "<input type='checkbox'  onchange='' name='ismand' value='1' >");
/* 569 */         jSONArray.put(jSONObject);
/*     */         
/* 571 */         jSONObject = new JSONObject();
/* 572 */         jSONObject.put("width", "10%");
/* 573 */         jSONObject.put("colname", SystemEnv.getHtmlLabelName(88, paramUser.getLanguage()));
/* 574 */         jSONObject.put("itemhtml", "<input type='text' size=10 maxlength=7 name='itemDspOrder' value=''  onKeyPress='ItemNum_KeyPress(this.name)' onchange='checknumber(this.name);checkDigit(this.name,15,2)' style='text-align:right;' />");
/* 575 */         jSONArray.put(jSONObject);
/*     */       }
/*     */     
/*     */     }
/* 579 */     catch (Exception exception) {
/* 580 */       exception.printStackTrace();
/* 581 */       writeLog(exception.getMessage());
/*     */     } 
/*     */     
/* 584 */     return jSONArray;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/cpt/util/CptDetailColumnUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */