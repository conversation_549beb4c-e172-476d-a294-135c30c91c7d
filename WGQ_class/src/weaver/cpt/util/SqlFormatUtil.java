/*    */ package weaver.cpt.util;
/*    */ 
/*    */ import java.util.HashMap;
/*    */ import java.util.List;
/*    */ import weaver.common.util.string.StringUtil;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.BaseBean;
/*    */ 
/*    */ 
/*    */ 
/*    */ public class SqlFormatUtil
/*    */   extends BaseBean
/*    */ {
/*    */   public static String formatSqlForMySql(String paramString1, String paramString2, List<String> paramList1, List<String> paramList2, String paramString3) {
/* 15 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 16 */     String str = "";
/* 17 */     RecordSet recordSet = new RecordSet();
/* 18 */     recordSet.executeSql("select column_name,data_type from information_schema.columns where lower(table_name)=lower('" + paramString2 + "')");
/* 19 */     while (recordSet.next()) {
/* 20 */       hashMap.put(recordSet.getString("column_name"), recordSet.getString("data_type"));
/*    */     }
/* 22 */     if ("insert".equals(paramString1)) {
/* 23 */       str = str + "insert into " + paramString2 + " set ";
/* 24 */       String str1 = "";
/* 25 */       String str2 = "";
/* 26 */       for (byte b = 0; b < paramList1.size(); b++) {
/* 27 */         if (b == 0) {
/* 28 */           str1 = str1 + "(";
/* 29 */           str2 = str2 + "(";
/*    */         } 
/* 31 */         String str3 = ((String)paramList1.get(b)).trim();
/* 32 */         String str4 = StringUtil.isNullOrEmpty(paramList2.get(b)) ? "" : ((String)paramList2.get(b)).trim();
/* 33 */         String str5 = (String)hashMap.get(str3);
/* 34 */         if ("int".equals(str5)) {
/* 35 */           if (StringUtil.isNotNullAndEmpty(str4) && !"null".equals(str4)) {
/* 36 */             str4 = "'" + str4 + "'";
/*    */           } else {
/* 38 */             str4 = "null";
/*    */           } 
/*    */         }
/*    */         
/* 42 */         if (b == paramList1.size() - 1) {
/* 43 */           str1 = str1 + str3 + ")";
/* 44 */           str2 = str2 + str4 + ")";
/*    */         } else {
/* 46 */           str1 = str1 + str3 + ",";
/* 47 */           str2 = str2 + str4 + ",";
/*    */         } 
/*    */       } 
/* 50 */       str = str + str1 + "values" + str2;
/* 51 */     } else if ("update".equals(paramString1)) {
/* 52 */       str = str + "update  " + paramString2 + " set ";
/* 53 */       for (byte b = 0; b < paramList1.size(); b++) {
/*    */         
/* 55 */         String str1 = ((String)paramList1.get(b)).trim();
/* 56 */         String str2 = StringUtil.isNullOrEmpty(paramList2.get(b)) ? "" : ((String)paramList2.get(b)).trim();
/* 57 */         String str3 = (String)hashMap.get(str1);
/* 58 */         if ("int".equals(str3)) {
/* 59 */           if (StringUtil.isNotNullAndEmpty(str2) && !"null".equals(str2)) {
/* 60 */             str = str + str1 + "='" + str2 + "'";
/*    */           } else {
/* 62 */             str = str + str1 + "=null";
/*    */           } 
/*    */         } else {
/* 65 */           str = str + str1 + "='" + str2 + "'";
/*    */         } 
/*    */         
/* 68 */         if (b != paramList1.size() - 1) {
/* 69 */           str = str + ",";
/*    */         }
/*    */       } 
/*    */     } 
/* 73 */     if (StringUtil.isNotNullAndEmpty(paramString3)) {
/* 74 */       str = str + " where " + paramString3;
/*    */     }
/* 76 */     return str;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/cpt/util/SqlFormatUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */