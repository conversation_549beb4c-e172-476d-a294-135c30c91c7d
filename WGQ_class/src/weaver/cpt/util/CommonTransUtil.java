/*     */ package weaver.cpt.util;
/*     */ 
/*     */ import net.sf.json.JSONObject;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.crm.Maint.CustomerInfoComInfo;
/*     */ import weaver.docs.docs.DocComInfo;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.company.DepartmentComInfo;
/*     */ import weaver.hrm.company.SubCompanyComInfo;
/*     */ import weaver.hrm.job.JobTitlesComInfo;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ import weaver.hrm.roles.RolesComInfo;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.workflow.request.RequestComInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CommonTransUtil
/*     */   extends BaseBean
/*     */ {
/*  25 */   private static ResourceComInfo resourceComInfo = null;
/*  26 */   private static DepartmentComInfo departmentComInfo = null;
/*  27 */   private static SubCompanyComInfo subCompanyComInfo = null;
/*  28 */   private static RolesComInfo rolesComInfo = null;
/*  29 */   private static RequestComInfo requestComInfo = null;
/*  30 */   private static CustomerInfoComInfo customerInfoComInfo = null;
/*  31 */   private static DocComInfo docComInfo = null;
/*  32 */   private static JobTitlesComInfo jobCominfo = null;
/*     */   public CommonTransUtil() {
/*     */     try {
/*  35 */       resourceComInfo = new ResourceComInfo();
/*  36 */       departmentComInfo = new DepartmentComInfo();
/*  37 */       subCompanyComInfo = new SubCompanyComInfo();
/*  38 */       rolesComInfo = new RolesComInfo();
/*  39 */       requestComInfo = new RequestComInfo();
/*  40 */       customerInfoComInfo = new CustomerInfoComInfo();
/*  41 */       docComInfo = new DocComInfo();
/*  42 */       jobCominfo = new JobTitlesComInfo();
/*  43 */     } catch (Exception exception) {
/*  44 */       exception.printStackTrace();
/*  45 */       writeLog(exception.getMessage());
/*     */     } 
/*     */   }
/*     */   
/*     */   public String onDetailEdit(String paramString1, String paramString2) {
/*  50 */     return "<a href=\"javascript:onDetailEdit(" + paramString2 + ")\">" + paramString1 + "</a>";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getSeclevel(String paramString1, String paramString2) {
/*  61 */     String[] arrayOfString = Util.TokenizerString2(Util.null2String(paramString2), "+");
/*  62 */     if (arrayOfString.length > 1 && ("1".equals(arrayOfString[1]) || "11".equals(arrayOfString[1]) || "9".equals(arrayOfString[1]) || "6".equals(arrayOfString[1]) || "7".equals(arrayOfString[1]))) {
/*  63 */       return "";
/*     */     }
/*     */     
/*  66 */     return paramString1 + " - " + arrayOfString[0];
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getShareTypeName(String paramString1, String paramString2) {
/*  78 */     if ("".equals(Util.null2String(paramString1))) {
/*  79 */       return "";
/*     */     }
/*  81 */     JSONObject jSONObject = JSONObject.fromObject(paramString2);
/*     */     
/*  83 */     int i = Util.getIntValue(paramString1, 0);
/*     */     
/*  85 */     int j = Util.getIntValue(jSONObject.getString("languageid"), 7);
/*  86 */     String str = "";
/*  87 */     switch (i) {
/*     */       case 1:
/*  89 */         str = SystemEnv.getHtmlLabelName(179, j);
/*     */         break;
/*     */       case 2:
/*  92 */         str = SystemEnv.getHtmlLabelName(124, j);
/*     */         break;
/*     */       case 3:
/*  95 */         str = SystemEnv.getHtmlLabelName(122, j);
/*     */         break;
/*     */       case 4:
/*  98 */         str = SystemEnv.getHtmlLabelName(1340, j);
/*     */         break;
/*     */       case 5:
/* 101 */         str = SystemEnv.getHtmlLabelName(141, j);
/*     */         break;
/*     */       case 9:
/* 104 */         str = SystemEnv.getHtmlLabelName(136, j);
/*     */         break;
/*     */       case 11:
/* 107 */         str = SystemEnv.getHtmlLabelName(6086, j);
/*     */         break;
/*     */     } 
/*     */ 
/*     */     
/* 112 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getIsDefaultShareName(String paramString1, String paramString2) {
/* 123 */     JSONObject jSONObject = JSONObject.fromObject(paramString2);
/*     */     
/* 125 */     int i = Util.getIntValue(paramString1, 0);
/*     */     
/* 127 */     int j = Util.getIntValue(jSONObject.getString("languageid"), 7);
/* 128 */     String str = SystemEnv.getHtmlLabelName(18574, j);
/* 129 */     switch (i)
/*     */     { case 1:
/* 131 */         str = SystemEnv.getHtmlLabelName(15059, j);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */         
/* 139 */         return str; }  str = SystemEnv.getHtmlLabelName(18574, j); return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getShareLevelName(String paramString1, String paramString2) {
/* 150 */     if ("".equals(Util.null2String(paramString1))) {
/* 151 */       return "";
/*     */     }
/* 153 */     JSONObject jSONObject = JSONObject.fromObject(paramString2);
/*     */     
/* 155 */     int i = Util.getIntValue(paramString1, 0);
/*     */     
/* 157 */     int j = Util.getIntValue(jSONObject.getString("languageid"), 7);
/* 158 */     String str = "";
/* 159 */     switch (i) {
/*     */       case 1:
/* 161 */         str = SystemEnv.getHtmlLabelName(367, j);
/*     */         break;
/*     */       case 2:
/* 164 */         str = SystemEnv.getHtmlLabelName(93, j);
/*     */         break;
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 171 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getShareObjectName(String paramString1, String paramString2) {
/* 182 */     if ("".equals(Util.null2String(paramString1))) {
/* 183 */       return "";
/*     */     }
/* 185 */     JSONObject jSONObject = JSONObject.fromObject(paramString2);
/*     */ 
/*     */     
/* 188 */     int i = Util.getIntValue(jSONObject.getString("languageid"), 7);
/* 189 */     String str1 = Util.null2String(jSONObject.getString("sharetablename"));
/*     */     
/* 191 */     String str2 = "";
/* 192 */     String str3 = "select * from " + str1 + " where id=" + paramString1;
/* 193 */     RecordSet recordSet = new RecordSet();
/* 194 */     recordSet.executeSql(str3);
/* 195 */     if (recordSet.next()) {
/* 196 */       String str4, str5; int j = recordSet.getInt("sharetype");
/* 197 */       switch (j) {
/*     */         
/*     */         case 1:
/*     */         case 6:
/*     */         case 7:
/* 202 */           str2 = "<a href=\"javascript:openhrm(" + recordSet.getString("userid") + ")\" onclick=\"pointerXY(event);\" >" + Util.toScreen(resourceComInfo.getResourcename(recordSet
/* 203 */                 .getString("userid")), i) + "</a>";
/*     */           break;
/*     */         
/*     */         case 2:
/* 207 */           str2 = "<a href=\"javascript:openFullWindowForXtable('/hrm/company/HrmDepartmentDsp.jsp?id=" + recordSet.getString("departmentid") + "')\" >" + Util.toScreen(departmentComInfo.getDepartmentname(recordSet
/* 208 */                 .getString("departmentid")), i) + "</a>";
/*     */           break;
/*     */         
/*     */         case 3:
/* 212 */           str2 = Util.toScreen(rolesComInfo
/* 213 */               .getRoleLevelName("" + Util.getIntValue(recordSet.getString("rolelevel"), 0), "" + i), i);
/* 214 */           str2 = str2 + " / " + Util.toScreen(rolesComInfo
/* 215 */               .getRolesRemark(recordSet.getString("roleid")), i);
/*     */           break;
/*     */         
/*     */         case 9:
/* 219 */           str2 = Util.toScreen("<a href=\"javascript:openFullWindowForXtable('/CRM/data/ViewCustomer.jsp?CustomerID=" + recordSet
/* 220 */               .getString("crmid") + "')\" >" + customerInfoComInfo.getCustomerInfoname(recordSet.getString("crmid")) + "</a>", i);
/*     */           break;
/*     */         
/*     */         case 4:
/* 224 */           str2 = "";
/*     */           break;
/*     */         case 5:
/* 227 */           str2 = Util.toScreen("<a href=\"javascript:openFullWindowForXtable('/hrm/company/HrmSubCompanyDsp.jsp?id=" + recordSet
/* 228 */               .getString("subcompanyid") + "')\" >" + subCompanyComInfo.getSubCompanyname(recordSet.getString("subcompanyid")) + "</a>", i);
/*     */           break;
/*     */         
/*     */         case 11:
/* 232 */           str4 = recordSet.getString("joblevel");
/* 233 */           str5 = "";
/* 234 */           if ("0".equals(str4)) {
/* 235 */             str5 = SystemEnv.getHtmlLabelName(140, i);
/*     */           } else {
/* 237 */             String str = recordSet.getString("scopeid");
/* 238 */             if ("1".equals(str4)) {
/* 239 */               str5 = SystemEnv.getHtmlLabelName(19438, i);
/* 240 */               str5 = str5 + "(" + departmentComInfo.getDepartmentNames(str) + ")";
/* 241 */             } else if ("2".equals(str4)) {
/* 242 */               str5 = SystemEnv.getHtmlLabelName(19437, i);
/*     */               
/* 244 */               String str6 = "";
/* 245 */               if (str.indexOf(",") != -1) {
/* 246 */                 String[] arrayOfString = str.split(",");
/* 247 */                 for (byte b = 0; b < arrayOfString.length; b++) {
/* 248 */                   str6 = str6 + subCompanyComInfo.getSubCompanyname(arrayOfString[b]) + ",";
/*     */                 }
/*     */               } else {
/* 251 */                 str6 = subCompanyComInfo.getSubCompanyname(str);
/*     */               } 
/* 253 */               if (!"".equals(str6)) str6 = str6.substring(0, str6.length() - 1); 
/* 254 */               str5 = str5 + "(" + str6 + ")";
/*     */             } 
/*     */           } 
/* 257 */           str2 = Util.toScreen(jobCominfo.getJobTitlesname(recordSet.getString("jobtitleid")) + "/" + str5, i);
/*     */           break;
/*     */ 
/*     */         
/*     */         case 8:
/* 262 */           str2 = "<a href=\"javascript:openhrm(" + recordSet.getString("userid") + ")\" onclick=\"pointerXY(event);\" >" + Util.toScreen(resourceComInfo.getResourcename(recordSet
/* 263 */                 .getString("userid")), i) + "</a>";
/*     */           break;
/*     */       } 
/*     */ 
/*     */ 
/*     */     
/*     */     } 
/* 270 */     return str2;
/*     */   }
/*     */   
/*     */   public String getDocNames(String paramString) {
/* 274 */     if ("".equals(Util.null2String(paramString))) {
/* 275 */       return "";
/*     */     }
/*     */     
/*     */     try {
/* 279 */       String str = "";
/* 280 */       String[] arrayOfString = Util.TokenizerString2(paramString, ",");
/* 281 */       for (byte b = 0; b < arrayOfString.length; b++) {
/* 282 */         str = str + docComInfo.getDocname(arrayOfString[b]) + " ";
/*     */       }
/* 284 */       return str;
/* 285 */     } catch (Exception exception) {
/* 286 */       return "";
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public String getDepNames(String paramString) {
/* 293 */     if ("".equals(Util.null2String(paramString))) {
/* 294 */       return "";
/*     */     }
/*     */     
/*     */     try {
/* 298 */       String str = "";
/* 299 */       String[] arrayOfString = Util.TokenizerString2(paramString, ",");
/* 300 */       for (byte b = 0; b < arrayOfString.length; b++) {
/* 301 */         str = str + departmentComInfo.getDepartmentname(arrayOfString[b]) + " ";
/*     */       }
/*     */       
/* 304 */       return str;
/* 305 */     } catch (Exception exception) {
/* 306 */       return "";
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCrmNames(String paramString) {
/* 313 */     if ("".equals(Util.null2String(paramString))) {
/* 314 */       return "";
/*     */     }
/*     */     
/*     */     try {
/* 318 */       String str = "";
/* 319 */       String[] arrayOfString = Util.TokenizerString2(paramString, ",");
/* 320 */       for (byte b = 0; b < arrayOfString.length; b++) {
/* 321 */         str = str + customerInfoComInfo.getCustomerInfoname(arrayOfString[b]) + " ";
/*     */       }
/*     */       
/* 324 */       return str;
/* 325 */     } catch (Exception exception) {
/* 326 */       return "";
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public String getReqNames(String paramString) {
/* 333 */     if ("".equals(Util.null2String(paramString))) {
/* 334 */       return "";
/*     */     }
/*     */     
/*     */     try {
/* 338 */       String str = "";
/* 339 */       String[] arrayOfString = Util.TokenizerString2(paramString, ",");
/* 340 */       for (byte b = 0; b < arrayOfString.length; b++) {
/* 341 */         str = str + requestComInfo.getRequestname(arrayOfString[b]) + " ";
/*     */       }
/* 343 */       return str;
/* 344 */     } catch (Exception exception) {
/* 345 */       return "";
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public String getHrmNamesWithCard(String paramString) {
/* 352 */     if ("".equals(Util.null2String(paramString))) {
/* 353 */       return "";
/*     */     }
/*     */     
/*     */     try {
/* 357 */       String str = "";
/* 358 */       String[] arrayOfString = Util.TokenizerString2(paramString, ",");
/* 359 */       for (byte b = 0; b < arrayOfString.length; b++) {
/* 360 */         str = str + "<a href=\"javaScript:openhrm(" + arrayOfString[b] + ");\" onclick=\"pointerXY(event);\">" + resourceComInfo.getResourcename(arrayOfString[b]) + "</a> ";
/*     */       }
/* 362 */       return str;
/* 363 */     } catch (Exception exception) {
/* 364 */       return "";
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static void main(String[] paramArrayOfString) {
/* 371 */     User user = new User();
/* 372 */     user.getLanguage();
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/cpt/util/CommonTransUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */