/*     */ package weaver.cpt.util;
/*     */ 
/*     */ import weaver.cache.CacheBase;
/*     */ import weaver.cache.CacheColumn;
/*     */ import weaver.cache.CacheColumnType;
/*     */ import weaver.cache.PKColumn;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CptWfConfComInfo
/*     */   extends CacheBase
/*     */ {
/*  18 */   protected static String TABLE_NAME = "cpt_cptwfconf";
/*     */   
/*  20 */   protected static String TABLE_WHERE = null;
/*     */   
/*  22 */   protected static String TABLE_ORDER = "id desc";
/*     */   
/*     */   @PKColumn(type = CacheColumnType.NUMBER)
/*  25 */   protected static String PK_NAME = "id";
/*     */   
/*     */   @CacheColumn
/*     */   protected static int wftype;
/*     */   
/*     */   @CacheColumn
/*     */   protected static int wfid;
/*     */   
/*     */   @CacheColumn
/*     */   protected static int sqr;
/*     */   
/*     */   @CacheColumn
/*     */   protected static int zczl;
/*     */   
/*     */   @CacheColumn
/*     */   protected static int zc;
/*     */   
/*     */   @CacheColumn
/*     */   protected static int sl;
/*     */   
/*     */   @CacheColumn
/*     */   protected static int jg;
/*     */   @CacheColumn
/*     */   protected static int rq;
/*     */   @CacheColumn
/*     */   protected static int ggxh;
/*     */   @CacheColumn
/*     */   protected static int cfdd;
/*     */   @CacheColumn
/*     */   protected static int bz;
/*     */   @CacheColumn
/*     */   protected static int wxqx;
/*     */   @CacheColumn
/*     */   protected static int wxdw;
/*     */   @CacheColumn
/*     */   protected static int isasync;
/*     */   @CacheColumn
/*     */   protected static int actname;
/*     */   
/*     */   public int getGroupNum() {
/*  65 */     return size();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean next() {
/*  75 */     return super.next();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean next(String paramString) {
/*  86 */     setTofirstRow();
/*  87 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public String getId() {
/*  93 */     return (String)getRowValue(0);
/*     */   }
/*     */   
/*     */   public String getWftype() {
/*  97 */     return (String)getRowValue(wftype);
/*     */   }
/*     */   public String getWftype(String paramString) {
/* 100 */     return (String)getValue(wftype, paramString);
/*     */   }
/*     */   public String getWfid() {
/* 103 */     return (String)getRowValue(wfid);
/*     */   }
/*     */   public String getWfid(String paramString) {
/* 106 */     return (String)getValue(wfid, paramString);
/*     */   }
/*     */   public String getSqr() {
/* 109 */     return (String)getRowValue(sqr);
/*     */   }
/*     */   public String getSqr(String paramString) {
/* 112 */     return (String)getValue(sqr, paramString);
/*     */   }
/*     */   public String getZczl() {
/* 115 */     return (String)getRowValue(zczl);
/*     */   }
/*     */   public String getZczl(String paramString) {
/* 118 */     return (String)getValue(zczl, paramString);
/*     */   }
/*     */   public String getZc() {
/* 121 */     return (String)getRowValue(zc);
/*     */   }
/*     */   public String getZc(String paramString) {
/* 124 */     return (String)getValue(zc, paramString);
/*     */   }
/*     */   public String getJg() {
/* 127 */     return (String)getRowValue(jg);
/*     */   }
/*     */   public String getJg(String paramString) {
/* 130 */     return (String)getValue(jg, paramString);
/*     */   }
/*     */   public String getRq() {
/* 133 */     return (String)getRowValue(rq);
/*     */   }
/*     */   public String getRq(String paramString) {
/* 136 */     return (String)getValue(rq, paramString);
/*     */   }
/*     */   public String getGgxh() {
/* 139 */     return (String)getRowValue(ggxh);
/*     */   }
/*     */   public String getGgxh(String paramString) {
/* 142 */     return (String)getValue(ggxh, paramString);
/*     */   }
/*     */   public String getCfdd() {
/* 145 */     return (String)getRowValue(cfdd);
/*     */   }
/*     */   public String getCfdd(String paramString) {
/* 148 */     return (String)getValue(cfdd, paramString);
/*     */   }
/*     */   public String getBz() {
/* 151 */     return (String)getRowValue(bz);
/*     */   }
/*     */   public String getBz(String paramString) {
/* 154 */     return (String)getValue(bz, paramString);
/*     */   }
/*     */   public String getWxqx() {
/* 157 */     return (String)getRowValue(wxqx);
/*     */   }
/*     */   public String getWxqx(String paramString) {
/* 160 */     return (String)getValue(wxqx, paramString);
/*     */   }
/*     */   public String getWxdw() {
/* 163 */     return (String)getRowValue(wxdw);
/*     */   }
/*     */   public String getWxdw(String paramString) {
/* 166 */     return (String)getValue(wxdw, paramString);
/*     */   }
/*     */   public String getIsasync() {
/* 169 */     return (String)getRowValue(isasync);
/*     */   }
/*     */   public String getIsasync(String paramString) {
/* 172 */     return (String)getValue(isasync, paramString);
/*     */   }
/*     */   public String getActname() {
/* 175 */     return (String)getRowValue(actname);
/*     */   }
/*     */   public String getActname(String paramString) {
/* 178 */     return (String)getValue(actname, paramString);
/*     */   }
/*     */   public String getSl() {
/* 181 */     return (String)getRowValue(sl);
/*     */   }
/*     */   public String getSl(String paramString) {
/* 184 */     return (String)getValue(sl, paramString);
/*     */   }
/*     */   
/*     */   public void removeCache() {
/* 188 */     super.removeCache();
/*     */   }
/*     */   public void setTofirstRow() {
/* 191 */     super.setTofirstRow();
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/cpt/util/CptWfConfComInfo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */