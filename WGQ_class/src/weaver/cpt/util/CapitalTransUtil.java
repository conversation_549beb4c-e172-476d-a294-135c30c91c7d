/*     */ package weaver.cpt.util;
/*     */ 
/*     */ import com.api.prj.util.ProjectTransMethod;
/*     */ import com.engine.common.biz.EncryptConfigBiz;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Arrays;
/*     */ import net.sf.json.JSONObject;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.cpt.capital.CapitalComInfo;
/*     */ import weaver.cpt.maintenance.CapitalAssortmentComInfo;
/*     */ import weaver.cpt.maintenance.CapitalStateComInfo;
/*     */ import weaver.crm.Maint.CustomerInfoComInfo;
/*     */ import weaver.docs.docs.DocComInfo;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.HrmUserVarify;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.company.DepartmentComInfo;
/*     */ import weaver.hrm.company.SubCompanyComInfo;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ import weaver.hrm.roles.RolesComInfo;
/*     */ import weaver.lgc.maintenance.AssetUnitComInfo;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.workflow.request.RequestComInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CapitalTransUtil
/*     */   extends BaseBean
/*     */ {
/*  34 */   private static ResourceComInfo resourceComInfo = null;
/*  35 */   private static DepartmentComInfo departmentComInfo = null;
/*  36 */   private static SubCompanyComInfo subCompanyComInfo = null;
/*  37 */   private static RolesComInfo rolesComInfo = null;
/*  38 */   private static RequestComInfo requestComInfo = null;
/*  39 */   private static CustomerInfoComInfo customerInfoComInfo = null;
/*  40 */   private static DocComInfo docComInfo = null;
/*  41 */   private static CapitalAssortmentComInfo capitalAssortmentComInfo = null;
/*  42 */   private static AssetUnitComInfo assetUnitComInfo = null;
/*  43 */   private static CapitalStateComInfo capitalStateComInfo = null;
/*  44 */   private static CapitalComInfo capitalComInfo = null;
/*     */ 
/*     */   
/*  47 */   private CommonShareManager commonShareManager = new CommonShareManager();
/*     */   public CapitalTransUtil() {
/*     */     try {
/*  50 */       resourceComInfo = new ResourceComInfo();
/*  51 */       departmentComInfo = new DepartmentComInfo();
/*  52 */       subCompanyComInfo = new SubCompanyComInfo();
/*  53 */       rolesComInfo = new RolesComInfo();
/*  54 */       requestComInfo = new RequestComInfo();
/*  55 */       customerInfoComInfo = new CustomerInfoComInfo();
/*  56 */       docComInfo = new DocComInfo();
/*  57 */       capitalAssortmentComInfo = new CapitalAssortmentComInfo();
/*  58 */       assetUnitComInfo = new AssetUnitComInfo();
/*  59 */       capitalStateComInfo = new CapitalStateComInfo();
/*  60 */       capitalComInfo = new CapitalComInfo();
/*  61 */     } catch (Exception exception) {
/*  62 */       exception.printStackTrace();
/*  63 */       writeLog(exception.getMessage());
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public ArrayList getStockinHistoryOperates(String paramString1, String paramString2) {
/*  74 */     ArrayList<String> arrayList = new ArrayList(Arrays.asList((Object[])new String[] { "true", "true", "true", "true" }));
/*  75 */     if ("1".equals(paramString2)) {
/*  76 */       arrayList.set(1, "false");
/*  77 */       arrayList.set(2, "false");
/*  78 */     } else if ("-1".equals(paramString2)) {
/*  79 */       arrayList.set(1, "false");
/*  80 */     } else if ("-2".equals(paramString2)) {
/*  81 */       arrayList.set(1, "false");
/*  82 */       arrayList.set(2, "false");
/*     */     } else {
/*  84 */       arrayList.set(2, "false");
/*  85 */       arrayList.set(3, "false");
/*     */     } 
/*  87 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public ArrayList getOperates(String paramString1, String paramString2) throws Exception {
/*  98 */     if ("".equals(Util.null2String(paramString1)) || "".equals(Util.null2String(paramString2))) {
/*  99 */       ArrayList<String> arrayList1 = new ArrayList();
/* 100 */       for (byte b = 0; b < 20; b++) {
/* 101 */         arrayList1.add("false");
/*     */       }
/* 103 */       return arrayList1;
/*     */     } 
/* 105 */     ArrayList<String> arrayList = new ArrayList();
/* 106 */     JSONObject jSONObject = JSONObject.fromObject(paramString2);
/* 107 */     if (jSONObject != null) {
/* 108 */       String str = (String)jSONObject.get("operator_val");
/* 109 */       if (!"".equals(Util.null2String(str))) {
/* 110 */         arrayList = Util.TokenizerString(str, "_");
/*     */       } else {
/* 112 */         String str1 = jSONObject.getString("operatortype");
/* 113 */         int i = jSONObject.getInt("operator_num");
/* 114 */         for (byte b = 0; b < i; b++) {
/* 115 */           arrayList.add("true");
/*     */         }
/*     */         
/* 118 */         User user = new User();
/* 119 */         user.setUid(jSONObject.getInt("userid"));
/* 120 */         user.setLoginid(resourceComInfo.getLoginID(jSONObject.getString("userid")));
/* 121 */         user.setLogintype(jSONObject.getString("usertype"));
/* 122 */         user.setLanguage(jSONObject.getInt("languageid"));
/*     */         
/* 124 */         String str2 = capitalComInfo.getResourceid(paramString1);
/* 125 */         String str3 = capitalComInfo.getCptStateId(paramString1);
/* 126 */         String str4 = capitalComInfo.getIsData(paramString1);
/*     */         
/* 128 */         if ("cpt_cptassortment".equalsIgnoreCase(str1)) {
/* 129 */           boolean bool1 = (Util.getIntValue(capitalAssortmentComInfo.getSubAssortmentCount(paramString1), 0) <= 0) ? true : false;
/* 130 */           boolean bool2 = (Util.getIntValue(capitalAssortmentComInfo.getCapitalCount(paramString1 + ""), 0) <= 0) ? true : false;
/* 131 */           boolean bool = jSONObject.getBoolean("isroot");
/*     */           
/* 133 */           boolean bool3 = false;
/*     */           
/* 135 */           RecordSet recordSet = new RecordSet();
/* 136 */           recordSet.executeSql("select * from cptcapital where  capitalgroupid=" + paramString1);
/* 137 */           if (!recordSet.next()) {
/* 138 */             bool3 = true;
/*     */           }
/* 140 */           arrayList.set(1, "" + ((bool1 && bool3) ? 1 : 0));
/* 141 */           arrayList.set(3, "" + bool3);
/* 142 */           arrayList.set(4, "" + bool);
/* 143 */         } else if ("cpt_cpttype".equalsIgnoreCase(str1)) {
/* 144 */           boolean bool = true;
/* 145 */           String str5 = "select 1 from CptCapitalType where id=" + paramString1 + " and exists( select 1 from cptcapital where CptCapitalType.id=cptcapital.capitaltypeid)";
/* 146 */           RecordSet recordSet = new RecordSet();
/* 147 */           recordSet.executeSql(str5);
/* 148 */           if (recordSet.next()) {
/* 149 */             bool = false;
/*     */           } else {
/* 151 */             bool = HrmUserVarify.checkUserRight("CptCapitalTypeEdit:Delete", user);
/*     */           } 
/* 153 */           arrayList.set(1, "" + bool);
/* 154 */         } else if ("cpt_cptdata1".equalsIgnoreCase(str1)) {
/* 155 */           boolean bool = true;
/* 156 */           String str5 = "select * from cptcapital where datatype = " + paramString1;
/* 157 */           RecordSet recordSet = new RecordSet();
/* 158 */           recordSet.executeSql(str5);
/* 159 */           if (recordSet.next()) bool = false; 
/* 160 */           str5 = "select * from cptstockinmain t1 , cptstockindetail t2 where t1.id = t2.cptstockinid and ischecked = 0 and cpttype = " + paramString1;
/* 161 */           recordSet.executeSql(str5);
/* 162 */           if (recordSet.next()) bool = false; 
/* 163 */           arrayList.set(1, "" + bool);
/*     */         }
/* 165 */         else if ("cpt_mycptlist".equalsIgnoreCase(str1)) {
/*     */ 
/*     */           
/* 168 */           if (!HrmUserVarify.checkUserRight("CptCapital:Return", user) || (!str3.equals("2") && !str3.equals("3") && !str3.equals("4"))) {
/* 169 */             arrayList.set(0, "false");
/*     */           }
/*     */           
/* 172 */           if (!HrmUserVarify.checkUserRight("CptCapital:Mend", user) || str3.equals("4") || str3.equals("5")) {
/* 173 */             arrayList.set(1, "false");
/*     */           }
/*     */           
/* 176 */           if (!HrmUserVarify.checkUserRight("CptCapitalEdit:Edit", user) && !HrmUserVarify.checkUserRight("CptCapital:modify", user) && !canEdit(paramString1, user)) {
/* 177 */             arrayList.set(2, "false");
/*     */           }
/* 179 */           if (!HrmUserVarify.checkUserRight("CptCapital:FlowView", user)) {
/* 180 */             arrayList.set(4, "false");
/*     */           }
/* 182 */           if (!canEdit(paramString1, user)) {
/* 183 */             arrayList.set(5, "false");
/*     */           }
/* 185 */           if (!HrmUserVarify.checkUserRight("CptCapitalEdit:Delete", user)) {
/* 186 */             arrayList.set(6, "false");
/*     */           }
/*     */         }
/* 189 */         else if ("cpt_qrydata2list".equalsIgnoreCase(str1)) {
/*     */           
/* 191 */           arrayList.set(0, "false");
/* 192 */           arrayList.set(1, "false");
/*     */           
/* 194 */           if (!HrmUserVarify.checkUserRight("CptCapitalEdit:Edit", user) && !HrmUserVarify.checkUserRight("CptCapital:modify", user) && !canEdit(paramString1, user)) {
/* 195 */             arrayList.set(2, "false");
/*     */           }
/* 197 */           arrayList.set(3, "true");
/* 198 */           if (!HrmUserVarify.checkUserRight("CptCapital:FlowView", user)) {
/* 199 */             arrayList.set(4, "false");
/*     */           }
/* 201 */           if (!canEdit(paramString1, user)) {
/* 202 */             arrayList.set(5, "false");
/*     */           }
/* 204 */           if (!HrmUserVarify.checkUserRight("CptCapitalEdit:Delete", user)) {
/* 205 */             arrayList.set(6, "false");
/*     */           }
/* 207 */           arrayList.set(7, "false");
/*     */         }
/* 209 */         else if ("cpt_qrydata1list".equalsIgnoreCase(str1)) {
/* 210 */           arrayList.set(0, "false");
/* 211 */           arrayList.set(1, "false");
/* 212 */           arrayList.set(2, "false");
/* 213 */           arrayList.set(3, "false");
/* 214 */           arrayList.set(4, "false");
/* 215 */           arrayList.set(5, "false");
/* 216 */           arrayList.set(6, "false");
/* 217 */           arrayList.set(7, "false");
/*     */           
/* 219 */           boolean bool = HrmUserVarify.checkUserRight("Capital:Maintenance", user);
/* 220 */           arrayList.set(2, "" + bool);
/*     */           
/* 222 */           arrayList.set(6, "" + canDeleteData1(user, paramString1));
/*     */         } 
/*     */       } 
/*     */     } 
/*     */ 
/*     */     
/* 228 */     return arrayList;
/*     */   }
/*     */   
/*     */   public boolean canDeleteData1(User paramUser, String paramString) {
/* 232 */     boolean bool = false;
/* 233 */     if (HrmUserVarify.checkUserRight("Capital:Maintenance", paramUser)) {
/* 234 */       String str = "select * from cptcapital where datatype = " + paramString;
/* 235 */       RecordSet recordSet = new RecordSet();
/* 236 */       recordSet.executeSql(str);
/* 237 */       if (!recordSet.next()) {
/* 238 */         str = "select * from cptstockinmain t1 , cptstockindetail t2 where t1.id = t2.cptstockinid and ischecked = 0 and cpttype = " + paramString;
/* 239 */         recordSet.executeSql(str);
/* 240 */         if (!recordSet.next()) {
/* 241 */           bool = true;
/*     */         }
/*     */       } 
/*     */     } 
/*     */ 
/*     */     
/* 247 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean canEdit(String paramString, User paramUser) {
/* 254 */     paramUser.setSeclevel(resourceComInfo.getSeclevel("" + paramUser.getUID()));
/*     */     
/* 256 */     String str = this.commonShareManager.getSharLevel("cpt", paramString, paramUser);
/* 257 */     RecordSet recordSet = new RecordSet();
/* 258 */     recordSet.executeSql(str);
/* 259 */     boolean bool = false;
/* 260 */     if (recordSet.next()) {
/* 261 */       int i = recordSet.getInt(1);
/* 262 */       if (i >= 2) {
/* 263 */         bool = true;
/*     */       }
/*     */     } 
/*     */     
/* 267 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getBrowserRetInfo(String paramString1, String paramString2) {
/* 278 */     if ("".equals(Util.null2String(paramString1))) {
/* 279 */       return "";
/*     */     }
/*     */     
/* 282 */     String str = "";
/* 283 */     RecordSet recordSet = new RecordSet();
/* 284 */     recordSet.executeSql("select * from cptcapital where id=" + paramString1);
/* 285 */     if (recordSet.next()) {
/* 286 */       String str1 = "";
/* 287 */       String str2 = "";
/* 288 */       String str3 = "";
/* 289 */       String str4 = "";
/* 290 */       String str5 = "";
/* 291 */       String str6 = "";
/* 292 */       String str7 = "";
/* 293 */       String str8 = "";
/* 294 */       double d = 0.0D;
/* 295 */       String str9 = "";
/* 296 */       String str10 = "";
/* 297 */       String str11 = "";
/* 298 */       String str12 = "";
/* 299 */       String str13 = "";
/* 300 */       String str14 = "";
/* 301 */       str1 = recordSet.getString("blongsubcompany");
/* 302 */       str2 = recordSet.getString("id");
/* 303 */       str3 = recordSet.getString("capitalgroupid");
/* 304 */       str4 = capitalAssortmentComInfo.getSupAssortmentId(str3);
/* 305 */       str5 = str4;
/* 306 */       str6 = recordSet.getString("departmentid");
/* 307 */       str7 = recordSet.getString("stateid");
/* 308 */       str8 = "";
/*     */       
/* 310 */       str9 = Util.null2String(recordSet.getString("resourceid"));
/* 311 */       str9 = resourceComInfo.getLastname(str9);
/*     */       
/* 313 */       str10 = Util.null2String(recordSet.getString("capitalspec"));
/* 314 */       str12 = Util.null2String(recordSet.getString("startprice"));
/* 315 */       str11 = assetUnitComInfo.getAssetUnitname(recordSet.getString("unitid"));
/* 316 */       str13 = Util.null2String(recordSet.getString("location"));
/* 317 */       str14 = Util.null2String(recordSet.getString("sptcount"));
/*     */       
/* 319 */       if (paramString2.equals("1")) {
/* 320 */         d = Util.getDoubleValue(recordSet.getString("capitalnum"), 0.0D);
/* 321 */         double d1 = Util.getDoubleValue(recordSet.getString("frozennum"), 0.0D);
/* 322 */         if (d1 < 0.0D) d1 = 0.0D; 
/* 323 */         d -= d1;
/*     */       } 
/*     */ 
/*     */       
/* 327 */       if ("0".equals(paramString2)) {
/* 328 */         str = "<A HREF=# >" + str2 + "_#_" + Util.null2String(recordSet.getString("name")) + "_#_" + str4 + "_#_" + str5 + "_#_" + str9 + "_#_" + str10 + "_#_" + str12 + "_#_" + str11 + "_#_" + Util.null2String(recordSet.getString("mark")) + "_#__#__#__#_</A>";
/*     */       } else {
/* 330 */         str = "<A HREF=# >" + str2 + "_#_" + Util.null2String(recordSet.getString("name")) + "_#_" + str4 + "_#_" + str5 + "_#_" + str9 + "_#_" + Util.null2String(recordSet.getString("fnamark")) + "_#_" + Util.null2String(capitalStateComInfo.getCapitalStatename(str7)) + "_#_" + String.valueOf(d) + "_#_" + Util.null2String(recordSet.getString("mark")) + "_#_" + str10 + "_#_" + str13 + "_#_" + str11 + "_#_" + str14 + "</A>";
/*     */       } 
/*     */     } 
/*     */ 
/*     */     
/* 335 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCanBatchInstock(String paramString) {
/* 345 */     return "true";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCanEditDefinedField(String paramString) {
/* 353 */     return "false";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCanDelCptCapitalData1(String paramString) {
/* 363 */     boolean bool = true;
/* 364 */     String str = "select * from cptcapital where datatype = " + paramString;
/* 365 */     RecordSet recordSet = new RecordSet();
/* 366 */     recordSet.executeSql(str);
/* 367 */     if (recordSet.next()) bool = false; 
/* 368 */     str = "select * from cptstockinmain t1 , cptstockindetail t2 where t1.id = t2.cptstockinid and ischecked = 0 and cpttype = " + paramString;
/* 369 */     recordSet.executeSql(str);
/* 370 */     if (recordSet.next()) bool = false;
/*     */ 
/*     */     
/* 373 */     return "" + bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCanDelCptType(String paramString) {
/* 383 */     boolean bool = true;
/*     */     
/* 385 */     String str = "select 1 from CptCapitalType where id=" + paramString + " and exists( select 1 from cptcapital where CptCapitalType.id=cptcapital.capitaltypeid)";
/* 386 */     RecordSet recordSet = new RecordSet();
/* 387 */     recordSet.executeSql(str);
/* 388 */     if (recordSet.next()) {
/* 389 */       bool = false;
/*     */     }
/*     */     
/* 392 */     return "" + bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCanDelCptAssortment(String paramString) {
/* 403 */     boolean bool1 = (Util.getIntValue(capitalAssortmentComInfo.getSubAssortmentCount(paramString), 0) <= 0) ? true : false;
/* 404 */     boolean bool2 = false;
/*     */     
/* 406 */     RecordSet recordSet = new RecordSet();
/* 407 */     recordSet.executeSql("select * from cptcapital where  capitalgroupid=" + paramString);
/* 408 */     if (!recordSet.next()) {
/* 409 */       bool2 = true;
/*     */     }
/*     */     
/* 412 */     if (bool1 && bool2) {
/* 413 */       return "true";
/*     */     }
/* 415 */     return "false";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCanDelCptAssortmentShare(String paramString) {
/* 427 */     return "true";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCanDelShareInfo(String paramString) {
/* 436 */     if ("1".equals(Util.null2String(paramString))) {
/* 437 */       return "true";
/*     */     }
/* 439 */     return "false";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public ArrayList getCanDelPrjShare(String paramString1, String paramString2) throws Exception {
/* 451 */     ArrayList<String> arrayList = new ArrayList();
/* 452 */     if ("1".equals(paramString2)) {
/* 453 */       arrayList.add("true");
/*     */     } else {
/* 455 */       arrayList.add("false");
/*     */     } 
/* 457 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getShareTypeName(String paramString1, String paramString2) {
/* 467 */     if ("".equals(Util.null2String(paramString1))) {
/* 468 */       return "";
/*     */     }
/* 470 */     JSONObject jSONObject = JSONObject.fromObject(paramString2);
/*     */     
/* 472 */     int i = Util.getIntValue(paramString1, 0);
/*     */     
/* 474 */     int j = Util.getIntValue(jSONObject.getString("languageid"), 7);
/* 475 */     String str = "";
/* 476 */     switch (i) {
/*     */       case 1:
/* 478 */         str = SystemEnv.getHtmlLabelName(179, j);
/*     */         break;
/*     */       case 2:
/* 481 */         str = SystemEnv.getHtmlLabelName(124, j);
/*     */         break;
/*     */       case 3:
/* 484 */         str = SystemEnv.getHtmlLabelName(122, j);
/*     */         break;
/*     */       case 4:
/* 487 */         str = SystemEnv.getHtmlLabelName(1340, j);
/*     */         break;
/*     */       case 5:
/* 490 */         str = SystemEnv.getHtmlLabelName(141, j);
/*     */         break;
/*     */       case 6:
/* 493 */         str = SystemEnv.getHtmlLabelName(1508, j);
/*     */         break;
/*     */       case 7:
/* 496 */         str = SystemEnv.getHtmlLabelName(882, j);
/*     */         break;
/*     */       case 8:
/* 499 */         str = SystemEnv.getHtmlLabelName(82769, j);
/*     */         break;
/*     */       case 11:
/* 502 */         str = SystemEnv.getHtmlLabelName(6086, j);
/*     */         break;
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/* 508 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getIsDefaultShareName(String paramString1, String paramString2) {
/* 517 */     if ("".equals(Util.null2String(paramString1))) {
/* 518 */       return "";
/*     */     }
/* 520 */     JSONObject jSONObject = JSONObject.fromObject(paramString2);
/*     */     
/* 522 */     int i = Util.getIntValue(paramString1, 0);
/*     */     
/* 524 */     int j = Util.getIntValue(jSONObject.getString("languageid"), 7);
/* 525 */     String str = "";
/* 526 */     switch (i) {
/*     */       case 1:
/* 528 */         str = SystemEnv.getHtmlLabelName(15059, j);
/*     */         break;
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 535 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getShareLevelName(String paramString1, String paramString2) {
/* 546 */     if ("".equals(Util.null2String(paramString1))) {
/* 547 */       return "";
/*     */     }
/* 549 */     JSONObject jSONObject = JSONObject.fromObject(paramString2);
/*     */     
/* 551 */     int i = Util.getIntValue(paramString1, 0);
/*     */     
/* 553 */     int j = Util.getIntValue(jSONObject.getString("languageid"), 7);
/* 554 */     String str = "";
/* 555 */     switch (i) {
/*     */       case 1:
/* 557 */         str = SystemEnv.getHtmlLabelName(367, j);
/*     */         break;
/*     */       case 2:
/* 560 */         str = SystemEnv.getHtmlLabelName(93, j);
/*     */         break;
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 567 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getShareObjectName(String paramString1, String paramString2) {
/* 578 */     if ("".equals(Util.null2String(paramString1))) {
/* 579 */       return "";
/*     */     }
/* 581 */     JSONObject jSONObject = JSONObject.fromObject(paramString2);
/*     */ 
/*     */     
/* 584 */     int i = Util.getIntValue(jSONObject.getString("languageid"), 7);
/* 585 */     String str1 = "";
/* 586 */     String str2 = "select * from CptAssortmentShare where id=" + paramString1;
/* 587 */     RecordSet recordSet = new RecordSet();
/* 588 */     recordSet.executeSql(str2);
/* 589 */     if (recordSet.next()) {
/* 590 */       int j = recordSet.getInt("sharetype");
/* 591 */       switch (j) {
/*     */ 
/*     */ 
/*     */         
/*     */         case 1:
/* 596 */           str1 = "<a href='/hrm/resource/HrmResource.jsp?id=" + recordSet.getString("userid") + "' target='_blank'>" + Util.toScreen(resourceComInfo.getResourcename(recordSet
/* 597 */                 .getString("userid")), i) + "</a>";
/*     */           break;
/*     */ 
/*     */ 
/*     */         
/*     */         case 2:
/* 603 */           str1 = "<a href='/hrm/company/HrmDepartmentDsp.jsp?id=" + recordSet.getString("departmentid") + "' target='_blank'>" + Util.toScreen(departmentComInfo.getDepartmentname(recordSet
/* 604 */                 .getString("departmentid")), i) + "</a>";
/*     */           break;
/*     */         
/*     */         case 3:
/* 608 */           str1 = Util.toScreen(rolesComInfo
/* 609 */               .getRolesname(recordSet.getString("roleid")), i);
/*     */           break;
/*     */         
/*     */         case 4:
/* 613 */           str1 = "";
/*     */           break;
/*     */         case 5:
/* 616 */           str1 = Util.toScreen(subCompanyComInfo
/* 617 */               .getSubCompanyname(recordSet.getString("subcompanyid")), i);
/*     */           break;
/*     */       } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*     */     } 
/* 626 */     return str1;
/*     */   }
/*     */   
/*     */   public String getDocNames(String paramString) {
/* 630 */     if ("".equals(Util.null2String(paramString))) {
/* 631 */       return "";
/*     */     }
/*     */     
/*     */     try {
/* 635 */       String str = "";
/* 636 */       String[] arrayOfString = Util.TokenizerString2(paramString, ",");
/* 637 */       for (byte b = 0; b < arrayOfString.length; b++) {
/* 638 */         str = str + docComInfo.getDocname(arrayOfString[b]) + " ";
/*     */       }
/* 640 */       return str;
/* 641 */     } catch (Exception exception) {
/* 642 */       return "";
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public String getDepNames(String paramString) {
/* 649 */     if ("".equals(Util.null2String(paramString))) {
/* 650 */       return "";
/*     */     }
/*     */     
/*     */     try {
/* 654 */       String str = "";
/* 655 */       String[] arrayOfString = Util.TokenizerString2(paramString, ",");
/* 656 */       for (byte b = 0; b < arrayOfString.length; b++) {
/* 657 */         str = str + departmentComInfo.getDepartmentname(arrayOfString[b]) + " ";
/*     */       }
/*     */       
/* 660 */       return str;
/* 661 */     } catch (Exception exception) {
/* 662 */       return "";
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCrmNames(String paramString) {
/* 669 */     if ("".equals(Util.null2String(paramString))) {
/* 670 */       return "";
/*     */     }
/*     */     
/*     */     try {
/* 674 */       String str = "";
/* 675 */       String[] arrayOfString = Util.TokenizerString2(paramString, ",");
/* 676 */       for (byte b = 0; b < arrayOfString.length; b++) {
/* 677 */         str = str + customerInfoComInfo.getCustomerInfoname(arrayOfString[b]) + " ";
/*     */       }
/*     */       
/* 680 */       return str;
/* 681 */     } catch (Exception exception) {
/* 682 */       return "";
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public String getReqNames(String paramString) {
/* 689 */     if ("".equals(Util.null2String(paramString))) {
/* 690 */       return "";
/*     */     }
/*     */     
/*     */     try {
/* 694 */       String str = "";
/* 695 */       String[] arrayOfString = Util.TokenizerString2(paramString, ",");
/* 696 */       for (byte b = 0; b < arrayOfString.length; b++) {
/* 697 */         str = str + requestComInfo.getRequestname(arrayOfString[b]) + " ";
/*     */       }
/* 699 */       return str;
/* 700 */     } catch (Exception exception) {
/* 701 */       return "";
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   public String getSeclevel(String paramString1, String paramString2) {
/* 707 */     String str = "select * from CptAssortmentShare where id=" + paramString2;
/* 708 */     RecordSet recordSet = new RecordSet();
/* 709 */     recordSet.executeSql(str);
/* 710 */     int i = 0;
/* 711 */     if (recordSet.next()) {
/* 712 */       int j = recordSet.getInt("sharetype");
/* 713 */       i = recordSet.getInt("Seclevel");
/* 714 */       if (j == 1) {
/* 715 */         return "";
/*     */       }
/*     */     } 
/* 718 */     return i + "";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCapitalInstockState(String paramString1, String paramString2) {
/* 727 */     int i = Util.getIntValue(paramString1, 0);
/* 728 */     String str = "82682";
/* 729 */     if (i == 1) {
/* 730 */       str = "82684";
/* 731 */     } else if (i == -1) {
/* 732 */       str = "27774";
/* 733 */     } else if (i == -2) {
/* 734 */       str = "18661";
/*     */     } 
/* 736 */     return SystemEnv.getHtmlLabelNames(str, paramString2);
/*     */   }
/*     */ 
/*     */   
/*     */   public String getCapitalInstockViewDetail(String paramString) {
/* 741 */     return "<a href=\"javascript:onViewDetail(" + paramString + ")\">" + paramString + "</a>";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCanDelCptFromAssort(String paramString) {
/* 752 */     String str1 = "false";
/* 753 */     RecordSet recordSet = new RecordSet();
/* 754 */     String[] arrayOfString = paramString.split("\\+");
/* 755 */     recordSet.executeSql("select isdata from cptcapital where id = " + arrayOfString[0]);
/* 756 */     recordSet.next();
/* 757 */     String str2 = recordSet.getString("isdata");
/* 758 */     if ("1".equals(str2)) {
/* 759 */       str1 = getCanDelCptCapitalData1(arrayOfString[0]);
/*     */     } else {
/*     */       
/* 762 */       User user = new User();
/* 763 */       user.setUid(Integer.parseInt(arrayOfString[1]));
/* 764 */       user.setLogintype("1");
/* 765 */       user.setLoginid(resourceComInfo.getLoginID(arrayOfString[1]));
/* 766 */       user.setLanguage(Integer.parseInt(arrayOfString[2]));
/* 767 */       if (HrmUserVarify.checkUserRight("CptCapitalEdit:Delete", user)) {
/* 768 */         str1 = "true";
/*     */       }
/*     */     } 
/* 771 */     return str1;
/*     */   }
/*     */   
/*     */   public String getWarningnum(String paramString1, String paramString2) {
/* 775 */     ProjectTransMethod projectTransMethod = new ProjectTransMethod();
/* 776 */     String str = EncryptConfigBiz.getDecryptData(paramString1);
/* 777 */     paramString2 = ProjectTransMethod.getDecryptStrByfieldname("alertnum", "CptCapital", paramString2);
/* 778 */     if (Util.getDoubleValue(paramString2, 0.0D) >= Util.getDoubleValue(str, 0.0D)) {
/* 779 */       return "<span style='color:red;'>" + paramString1 + "</span>";
/*     */     }
/* 781 */     return paramString1;
/*     */   }
/*     */   public String getAlertnumInput(String paramString1, String paramString2) {
/* 784 */     ProjectTransMethod projectTransMethod = new ProjectTransMethod();
/* 785 */     paramString1 = ProjectTransMethod.getDecryptStrByfieldname("alertnum", "CptCapital", paramString1);
/* 786 */     return paramString1;
/*     */   }
/*     */   
/*     */   public static void main(String[] paramArrayOfString) {
/* 790 */     User user = new User();
/* 791 */     user.getLanguage();
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/cpt/util/CapitalTransUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */