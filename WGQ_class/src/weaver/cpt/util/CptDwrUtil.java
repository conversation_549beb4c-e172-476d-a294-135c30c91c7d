/*     */ package weaver.cpt.util;
/*     */ 
/*     */ import java.util.HashMap;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.cpt.maintenance.CapitalAssortmentComInfo;
/*     */ import weaver.cpt.maintenance.CapitalStateComInfo;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.company.DepartmentComInfo;
/*     */ import weaver.hrm.company.SubCompanyComInfo;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CptDwrUtil
/*     */   extends BaseBean
/*     */ {
/*  23 */   private SubCompanyComInfo subCompanyComInfo = null;
/*  24 */   private DepartmentComInfo departmentComInfo = null;
/*  25 */   private ResourceComInfo resourceComInfo = null;
/*  26 */   private CapitalAssortmentComInfo capitalAssortmentComInfo = null;
/*  27 */   private CapitalStateComInfo capitalStateComInfo = null;
/*     */ 
/*     */   
/*     */   public CptDwrUtil() {
/*     */     try {
/*  32 */       this.subCompanyComInfo = new SubCompanyComInfo();
/*  33 */       this.departmentComInfo = new DepartmentComInfo();
/*  34 */       this.resourceComInfo = new ResourceComInfo();
/*  35 */       this.capitalAssortmentComInfo = new CapitalAssortmentComInfo();
/*  36 */       this.capitalStateComInfo = new CapitalStateComInfo();
/*  37 */     } catch (Exception exception) {
/*  38 */       writeLog(exception.getMessage());
/*  39 */       exception.printStackTrace();
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public HashMap<String, String> getCptInfoMap(String paramString) {
/*  52 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  53 */     RecordSet recordSet = new RecordSet();
/*  54 */     recordSet.executeQuery("select t1.*,t2.unitname from cptcapital t1 left outer join LgcAssetUnit t2 on t2.id=t1.unitid where  t1.id= ?", new Object[] { paramString });
/*  55 */     if (recordSet.next()) {
/*  56 */       hashMap.put("id", Util.null2String(recordSet.getString("id")));
/*  57 */       hashMap.put("sptcount", Util.null2String(recordSet.getString("sptcount")));
/*  58 */       hashMap.put("mark", Util.null2String(recordSet.getString("mark")));
/*  59 */       hashMap.put("capitalgroupid", Util.null2String(recordSet.getString("capitalgroupid")));
/*  60 */       hashMap.put("capitalgroupname_", getBrowserName(this.capitalAssortmentComInfo.getAssortmentName(Util.null2String(recordSet.getString("capitalgroupid"))), Util.null2String(recordSet.getString("capitalgroupid"))));
/*  61 */       hashMap.put("capitalspec", Util.null2String(recordSet.getString("capitalspec")));
/*  62 */       hashMap.put("name", Util.null2String(recordSet.getString("name")));
/*  63 */       hashMap.put("startprice", Util.null2String(recordSet.getString("startprice")));
/*  64 */       hashMap.put("unitid", Util.null2String(recordSet.getString("unitid")));
/*  65 */       hashMap.put("unitname", Util.null2String(recordSet.getString("unitname")));
/*  66 */       hashMap.put("location", Util.null2String(recordSet.getString("location")));
/*  67 */       hashMap.put("stockindate", Util.null2String(recordSet.getString("stockindate")));
/*  68 */       hashMap.put("selectdate", Util.null2String(recordSet.getString("selectdate")));
/*  69 */       hashMap.put("stateid", Util.null2String(recordSet.getString("stateid")));
/*  70 */       hashMap.put("statename", this.capitalStateComInfo.getCapitalStatename(Util.null2String(recordSet.getString("stateid"))));
/*  71 */       hashMap.put("blongsubcompanyid", Util.null2String(recordSet.getString("blongsubcompany")));
/*  72 */       hashMap.put("blongsubcompanyname", this.subCompanyComInfo.getSubCompanyname(Util.null2String(recordSet.getString("blongsubcompany"))));
/*  73 */       hashMap.put("blongdepartmentid", Util.null2String(recordSet.getString("blongdepartment")));
/*  74 */       hashMap.put("blongdepartmentname", this.departmentComInfo.getDepartmentname(Util.null2String(recordSet.getString("blongdepartment"))));
/*  75 */       hashMap.put("blongdepartmentname_", getBrowserName(this.departmentComInfo.getDepartmentname(Util.null2String(recordSet.getString("blongdepartment"))), Util.null2String(recordSet.getString("blongdepartment"))));
/*  76 */       hashMap.put("resourceid", Util.null2String(recordSet.getString("resourceid")));
/*  77 */       hashMap.put("resourcename", this.resourceComInfo.getResourcename(Util.null2String(recordSet.getString("resourceid"))));
/*  78 */       hashMap.put("resourcename_", getBrowserName(this.resourceComInfo.getResourcename(Util.null2String(recordSet.getString("resourceid"))), Util.null2String(recordSet.getString("resourceid"))));
/*     */       
/*  80 */       hashMap.put("location", Util.null2String(recordSet.getString("location")));
/*  81 */       hashMap.put("remark", Util.null2String(recordSet.getString("remark")));
/*     */       
/*  83 */       double d1 = Util.getDoubleValue(recordSet.getString("capitalnum"), 0.0D);
/*  84 */       double d2 = Util.getDoubleValue(recordSet.getString("frozennum"), 0.0D);
/*  85 */       if (d2 < 0.0D) {
/*  86 */         d2 = 0.0D;
/*     */       }
/*  88 */       double d3 = d1 - d2;
/*  89 */       if (d3 < 0.0D) d3 = 0.0D; 
/*  90 */       hashMap.put("capitalnum", "" + d1);
/*  91 */       hashMap.put("frozennum", "" + d2);
/*  92 */       hashMap.put("availablenum", "" + d3);
/*     */     } 
/*     */     
/*  95 */     return (HashMap)hashMap;
/*     */   }
/*     */   
/*     */   public String getBrowserName(String paramString1, String paramString2) {
/*  99 */     return paramString1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCptWfNodeItem(String paramString) {
/* 108 */     if (Util.getIntValue(paramString) <= 0) {
/* 109 */       return "";
/*     */     }
/* 111 */     StringBuffer stringBuffer = new StringBuffer();
/*     */     
/* 113 */     RecordSet recordSet = new RecordSet();
/* 114 */     recordSet.executeQuery("select b.id as triggerNodeId,a.nodeType as triggerNodeType,b.nodeName as triggerNodeName from workflow_flownode a,workflow_nodebase b where (b.IsFreeNode is null or b.IsFreeNode!='1') and a.nodeId=b.id and  a.workFlowId= ? order by a.nodeType,a.nodeId  ", new Object[] { paramString });
/* 115 */     while (recordSet.next()) {
/* 116 */       int i = recordSet.getInt("triggerNodeId");
/* 117 */       int j = recordSet.getInt("triggerNodeType");
/* 118 */       String str = Util.null2String(recordSet.getString("triggerNodeName"));
/* 119 */       stringBuffer.append("<option value='" + i + "' nodetype='" + j + "' >" + str + "</option>");
/*     */     } 
/* 121 */     return stringBuffer.toString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCptWfLinkItem(String paramString) {
/* 129 */     if (Util.getIntValue(paramString) <= 0) {
/* 130 */       return "";
/*     */     }
/* 132 */     StringBuffer stringBuffer = new StringBuffer();
/*     */     
/* 134 */     String str = "select id,nodeid,isreject,condition,conditioncn,linkname,destnodeid,nodepasstime,nodepasshour,nodepassminute,isBulidCode,ismustpass,tipsinfo,directionfrom,directionto from workflow_nodelink where wfrequestid is null and not EXISTS(select 1 from workflow_nodebase b where workflow_nodelink.nodeid=b.id and b.IsFreeNode='1') and not EXISTS(select 1 from workflow_nodebase b where workflow_nodelink.destnodeid=b.id and b.IsFreeNode='1') and workflowid=? order by nodeid,id";
/* 135 */     RecordSet recordSet = new RecordSet();
/* 136 */     recordSet.executeQuery(str, new Object[] { paramString });
/* 137 */     while (recordSet.next()) {
/* 138 */       int i = recordSet.getInt("id");
/* 139 */       String str1 = Util.null2String(recordSet.getString("linkname"));
/* 140 */       stringBuffer.append("<option value='" + i + "'>" + str1 + "</option>");
/*     */     } 
/*     */     
/* 143 */     return stringBuffer.toString();
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/cpt/util/CptDwrUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */