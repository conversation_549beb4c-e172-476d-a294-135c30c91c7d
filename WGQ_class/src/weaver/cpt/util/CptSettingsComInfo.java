/*     */ package weaver.cpt.util;
/*     */ 
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import weaver.cache.CacheBase;
/*     */ import weaver.cache.CacheColumn;
/*     */ import weaver.cache.CacheColumnType;
/*     */ import weaver.cache.PKColumn;
/*     */ import weaver.general.Util;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CptSettingsComInfo
/*     */   extends CacheBase
/*     */ {
/*  19 */   protected static String TABLE_NAME = "cpt_barcodesettings";
/*     */   
/*  21 */   protected static String TABLE_WHERE = null;
/*     */   
/*  23 */   protected static String TABLE_ORDER = "id desc";
/*     */   
/*     */   @PKColumn(type = CacheColumnType.NUMBER)
/*  26 */   protected static String PK_NAME = "id";
/*     */   
/*     */   @CacheColumn
/*     */   protected static int subcompanyid;
/*     */   
/*     */   @CacheColumn
/*     */   protected static int departmentid;
/*     */   
/*     */   @CacheColumn
/*     */   protected static int userid;
/*     */   
/*     */   @CacheColumn
/*     */   protected static int isopen;
/*     */   @CacheColumn
/*     */   protected static int barType;
/*     */   @CacheColumn
/*     */   protected static int code;
/*     */   @CacheColumn
/*     */   protected static int width;
/*     */   @CacheColumn
/*     */   protected static int height;
/*     */   @CacheColumn
/*     */   protected static int st;
/*     */   @CacheColumn
/*     */   protected static int textFont;
/*     */   @CacheColumn
/*     */   protected static int fontColor;
/*     */   @CacheColumn
/*     */   protected static int barColor;
/*     */   @CacheColumn
/*     */   protected static int backColor;
/*     */   @CacheColumn
/*     */   protected static int rotate;
/*     */   @CacheColumn
/*     */   protected static int barHeightCM;
/*     */   @CacheColumn
/*     */   protected static int x;
/*     */   @CacheColumn
/*     */   protected static int n;
/*     */   @CacheColumn
/*     */   protected static int leftMarginCM;
/*     */   @CacheColumn
/*     */   protected static int topMarginCM;
/*     */   @CacheColumn
/*     */   protected static int checkCharacter;
/*     */   @CacheColumn
/*     */   protected static int checkCharacterInText;
/*     */   @CacheColumn
/*     */   protected static int Code128Set;
/*     */   @CacheColumn
/*     */   protected static int UPCESytem;
/*     */   @CacheColumn
/*     */   protected static int isopen2;
/*     */   @CacheColumn
/*     */   protected static int barType2;
/*     */   @CacheColumn
/*     */   protected static int width2;
/*     */   @CacheColumn
/*     */   protected static int height2;
/*     */   @CacheColumn
/*     */   protected static int st2;
/*     */   @CacheColumn
/*     */   protected static int textFont2;
/*     */   @CacheColumn
/*     */   protected static int content2type;
/*     */   @CacheColumn
/*     */   protected static int content2;
/*     */   @CacheColumn
/*     */   protected static int link2;
/*     */   @CacheColumn
/*     */   protected static int userfilter;
/*     */   @CacheColumn
/*     */   protected static int usehrm;
/*     */   @CacheColumn
/*     */   protected static int usehrms;
/*     */   
/*     */   public int getGroupNum() {
/* 103 */     return size();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean next() {
/* 111 */     return super.next();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean next(String paramString) {
/* 120 */     setTofirstRow();
/* 121 */     return false;
/*     */   }
/*     */   
/*     */   public String getId() {
/* 125 */     return (String)getRowValue(0);
/*     */   }
/*     */   
/*     */   public String getSubcompanyid() {
/* 129 */     return (String)getRowValue(subcompanyid);
/*     */   }
/*     */   
/*     */   public String getSubcompanyid(String paramString) {
/* 133 */     return (String)getValue(subcompanyid, paramString);
/*     */   }
/*     */   
/*     */   public String getDepartmentid() {
/* 137 */     return (String)getRowValue(departmentid);
/*     */   }
/*     */   
/*     */   public String getDepartmentid(String paramString) {
/* 141 */     return (String)getValue(departmentid, paramString);
/*     */   }
/*     */   
/*     */   public String getUserid() {
/* 145 */     return (String)getRowValue(userid);
/*     */   }
/*     */   
/*     */   public String getUserid(String paramString) {
/* 149 */     return (String)getValue(userid, paramString);
/*     */   }
/*     */   
/*     */   public String getIsopen() {
/* 153 */     return (String)getRowValue(isopen);
/*     */   }
/*     */   
/*     */   public String getIsopen(String paramString) {
/* 157 */     return (String)getValue(isopen, paramString);
/*     */   }
/*     */   
/*     */   public String getBarType() {
/* 161 */     return (String)getRowValue(barType);
/*     */   }
/*     */   
/*     */   public String getBarType(String paramString) {
/* 165 */     return (String)getValue(barType, paramString);
/*     */   }
/*     */   
/*     */   public String getCode() {
/* 169 */     return (String)getRowValue(code);
/*     */   }
/*     */   
/*     */   public String getCode(String paramString) {
/* 173 */     return (String)getValue(code, paramString);
/*     */   }
/*     */   
/*     */   public String getWidth() {
/* 177 */     return (String)getRowValue(width);
/*     */   }
/*     */   
/*     */   public String getWidth(String paramString) {
/* 181 */     return (String)getValue(width, paramString);
/*     */   }
/*     */   
/*     */   public String getHeight() {
/* 185 */     return (String)getRowValue(height);
/*     */   }
/*     */   
/*     */   public String getHeight(String paramString) {
/* 189 */     return (String)getValue(height, paramString);
/*     */   }
/*     */   
/*     */   public String getSt() {
/* 193 */     return (String)getRowValue(st);
/*     */   }
/*     */   
/*     */   public String getSt(String paramString) {
/* 197 */     return (String)getValue(st, paramString);
/*     */   }
/*     */   
/*     */   public String getTextFont() {
/* 201 */     return (String)getRowValue(textFont);
/*     */   }
/*     */   
/*     */   public String getTextFont(String paramString) {
/* 205 */     return (String)getValue(textFont, paramString);
/*     */   }
/*     */   
/*     */   public String getFontColor() {
/* 209 */     return (String)getRowValue(fontColor);
/*     */   }
/*     */   
/*     */   public String getFontColor(String paramString) {
/* 213 */     return (String)getValue(fontColor, paramString);
/*     */   }
/*     */   
/*     */   public String getBarColor() {
/* 217 */     return (String)getRowValue(barColor);
/*     */   }
/*     */   
/*     */   public String getBarColor(String paramString) {
/* 221 */     return (String)getValue(barColor, paramString);
/*     */   }
/*     */   
/*     */   public String getBackColor() {
/* 225 */     return (String)getRowValue(backColor);
/*     */   }
/*     */   
/*     */   public String getBackColor(String paramString) {
/* 229 */     return (String)getValue(backColor, paramString);
/*     */   }
/*     */   
/*     */   public String getRotate() {
/* 233 */     return (String)getRowValue(rotate);
/*     */   }
/*     */   
/*     */   public String getRotate(String paramString) {
/* 237 */     return (String)getValue(rotate, paramString);
/*     */   }
/*     */   
/*     */   public String getBarHeightCM() {
/* 241 */     return (String)getRowValue(barHeightCM);
/*     */   }
/*     */   
/*     */   public String getBarHeightCM(String paramString) {
/* 245 */     return (String)getValue(barHeightCM, paramString);
/*     */   }
/*     */   
/*     */   public String getX() {
/* 249 */     return (String)getRowValue(x);
/*     */   }
/*     */   
/*     */   public String getX(String paramString) {
/* 253 */     return (String)getValue(x, paramString);
/*     */   }
/*     */   
/*     */   public String getN() {
/* 257 */     return (String)getRowValue(n);
/*     */   }
/*     */   
/*     */   public String getN(String paramString) {
/* 261 */     return (String)getValue(n, paramString);
/*     */   }
/*     */   
/*     */   public String getLeftMarginCM() {
/* 265 */     return (String)getRowValue(leftMarginCM);
/*     */   }
/*     */   
/*     */   public String getLeftMarginCM(String paramString) {
/* 269 */     return (String)getValue(leftMarginCM, paramString);
/*     */   }
/*     */   
/*     */   public String getTopMarginCM() {
/* 273 */     return (String)getRowValue(topMarginCM);
/*     */   }
/*     */   
/*     */   public String getTopMarginCM(String paramString) {
/* 277 */     return (String)getValue(topMarginCM, paramString);
/*     */   }
/*     */   
/*     */   public String getCheckCharacter() {
/* 281 */     return (String)getRowValue(checkCharacter);
/*     */   }
/*     */   
/*     */   public String getCheckCharacter(String paramString) {
/* 285 */     return (String)getValue(checkCharacter, paramString);
/*     */   }
/*     */   
/*     */   public String getCheckCharacterInText() {
/* 289 */     return (String)getRowValue(checkCharacterInText);
/*     */   }
/*     */   
/*     */   public String getCheckCharacterInText(String paramString) {
/* 293 */     return (String)getValue(checkCharacterInText, paramString);
/*     */   }
/*     */   
/*     */   public String getCode128Set() {
/* 297 */     return (String)getRowValue(Code128Set);
/*     */   }
/*     */   
/*     */   public String getCode128Set(String paramString) {
/* 301 */     return (String)getValue(Code128Set, paramString);
/*     */   }
/*     */   
/*     */   public String getUPCESytem() {
/* 305 */     return (String)getRowValue(UPCESytem);
/*     */   }
/*     */   
/*     */   public String getUPCESytem(String paramString) {
/* 309 */     return (String)getValue(UPCESytem, paramString);
/*     */   }
/*     */   
/*     */   public String getLink2() {
/* 313 */     return (String)getRowValue(link2);
/*     */   }
/*     */   
/*     */   public String getLink2(String paramString) {
/* 317 */     return (String)getValue(link2, paramString);
/*     */   }
/*     */   
/*     */   public String getIsopen2() {
/* 321 */     return (String)getRowValue(isopen2);
/*     */   }
/*     */   
/*     */   public String getIsopen2(String paramString) {
/* 325 */     return (String)getValue(isopen2, paramString);
/*     */   }
/*     */   
/*     */   public String getBarType2() {
/* 329 */     return (String)getRowValue(barType2);
/*     */   }
/*     */   
/*     */   public String getBarType2(String paramString) {
/* 333 */     return (String)getValue(barType2, paramString);
/*     */   }
/*     */   
/*     */   public String getWidth2() {
/* 337 */     return (String)getRowValue(width2);
/*     */   }
/*     */   
/*     */   public String getWidth2(String paramString) {
/* 341 */     return (String)getValue(width2, paramString);
/*     */   }
/*     */   
/*     */   public String getHeight2() {
/* 345 */     return (String)getRowValue(height2);
/*     */   }
/*     */   
/*     */   public String getHeight2(String paramString) {
/* 349 */     return (String)getValue(height2, paramString);
/*     */   }
/*     */   
/*     */   public String getSt2() {
/* 353 */     return (String)getRowValue(st2);
/*     */   }
/*     */   
/*     */   public String getSt2(String paramString) {
/* 357 */     return (String)getValue(st2, paramString);
/*     */   }
/*     */   
/*     */   public String getTextFont2() {
/* 361 */     return (String)getRowValue(textFont2);
/*     */   }
/*     */   
/*     */   public String getTextFont2(String paramString) {
/* 365 */     return (String)getValue(textFont2, paramString);
/*     */   }
/*     */   
/*     */   public String getContent2type() {
/* 369 */     return (String)getRowValue(content2type);
/*     */   }
/*     */   
/*     */   public String getContent2type(String paramString) {
/* 373 */     return (String)getValue(content2type, paramString);
/*     */   }
/*     */   
/*     */   public String getContent2() {
/* 377 */     return (String)getRowValue(content2);
/*     */   }
/*     */   
/*     */   public String getContent2(String paramString) {
/* 381 */     return (String)getValue(content2, paramString);
/*     */   }
/*     */   public String getUserfilter() {
/* 384 */     return (String)getRowValue(userfilter);
/*     */   } public String getUserfilter(String paramString) {
/* 386 */     return (String)getValue(userfilter, paramString);
/*     */   } public String getUsehrm() {
/* 388 */     return (String)getRowValue(usehrm);
/*     */   } public String getUsehrm(String paramString) {
/* 390 */     return (String)getValue(usehrm, paramString);
/*     */   } public String getUsehrms() {
/* 392 */     return (String)getRowValue(usehrms);
/*     */   } public String getUsehrmss(String paramString) {
/* 394 */     return (String)getValue(usehrms, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getBarcodeImageStr(HttpServletRequest paramHttpServletRequest, String paramString1, String paramString2, String paramString3) {
/* 404 */     CptSettingsComInfo cptSettingsComInfo = new CptSettingsComInfo();
/* 405 */     cptSettingsComInfo.setTofirstRow();
/* 406 */     String str = "";
/* 407 */     if (cptSettingsComInfo.next()) {
/* 408 */       if ("1".equals(cptSettingsComInfo.getIsopen())) {
/* 409 */         if ("".equals(Util.null2String(paramString1))) {
/* 410 */           paramString1 = paramString2;
/*     */         }
/* 412 */         StringBuffer stringBuffer = new StringBuffer();
/* 413 */         stringBuffer.append("<img src='");
/* 414 */         stringBuffer.append(paramHttpServletRequest.getContextPath()).append("/CreateCptBarCode?code=").append(paramString1);
/* 415 */         stringBuffer.append("&barType=").append(cptSettingsComInfo.getBarType());
/* 416 */         if ((int)Util.getDoubleValue(cptSettingsComInfo.getWidth(), 0.0D) > 0) {
/* 417 */           stringBuffer.append("&width=").append((int)Util.getDoubleValue(cptSettingsComInfo.getWidth(), 0.0D));
/*     */         }
/* 419 */         if ((int)Util.getDoubleValue(cptSettingsComInfo.getHeight(), 0.0D) > 0) {
/* 420 */           stringBuffer.append("&height=").append((int)Util.getDoubleValue(cptSettingsComInfo.getHeight(), 0.0D));
/*     */         }
/* 422 */         stringBuffer.append("&st=").append(cptSettingsComInfo.getSt());
/* 423 */         stringBuffer.append("&textFont=").append(cptSettingsComInfo.getTextFont());
/* 424 */         stringBuffer.append("&fontColor=").append(cptSettingsComInfo.getFontColor());
/* 425 */         stringBuffer.append("&barColor=").append(cptSettingsComInfo.getBarColor());
/* 426 */         stringBuffer.append("&backColor=").append(cptSettingsComInfo.getBackColor());
/* 427 */         stringBuffer.append("&rotate=").append(cptSettingsComInfo.getRotate());
/* 428 */         stringBuffer.append("&barHeightCM=").append(cptSettingsComInfo.getBarHeightCM());
/* 429 */         stringBuffer.append("&x=").append(cptSettingsComInfo.getX());
/* 430 */         stringBuffer.append("&n=").append(cptSettingsComInfo.getN());
/* 431 */         stringBuffer.append("&leftMarginCM=").append(cptSettingsComInfo.getLeftMarginCM());
/* 432 */         stringBuffer.append("&topMarginCM=").append(cptSettingsComInfo.getTopMarginCM());
/* 433 */         stringBuffer.append("&checkCharacter=").append(cptSettingsComInfo.getCheckCharacter());
/* 434 */         stringBuffer.append("&checkCharacterInText=").append(cptSettingsComInfo.getCheckCharacterInText());
/* 435 */         stringBuffer.append("&Code128Set=").append(cptSettingsComInfo.getCode128Set());
/* 436 */         stringBuffer.append("&UPCESytem=").append(cptSettingsComInfo.getUPCESytem());
/* 437 */         stringBuffer.append("&capitalid=").append(paramString3);
/* 438 */         stringBuffer.append("' />");
/* 439 */         str = stringBuffer.toString();
/*     */       } else {
/* 441 */         str = paramString1;
/*     */       } 
/*     */     }
/* 444 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getBarcodeImageStr2(HttpServletRequest paramHttpServletRequest, String paramString1, String paramString2, String paramString3) {
/* 457 */     CptSettingsComInfo cptSettingsComInfo = new CptSettingsComInfo();
/* 458 */     cptSettingsComInfo.setTofirstRow();
/* 459 */     String str = "";
/* 460 */     if (cptSettingsComInfo.next()) {
/* 461 */       if ("1".equals(cptSettingsComInfo.getIsopen2())) {
/* 462 */         if ("".equals(Util.null2String(paramString1))) {
/* 463 */           paramString1 = paramString2;
/*     */         }
/* 465 */         StringBuffer stringBuffer = new StringBuffer();
/* 466 */         stringBuffer.append("<img src='");
/* 467 */         stringBuffer.append(paramHttpServletRequest.getContextPath()).append("/CreateCptBarCode?code=").append(paramString1);
/* 468 */         stringBuffer.append("&barType=").append("-1");
/* 469 */         if ((int)Util.getDoubleValue(cptSettingsComInfo.getWidth2(), 0.0D) > 100) {
/* 470 */           stringBuffer.append("&width=").append((int)Util.getDoubleValue(cptSettingsComInfo.getWidth2(), 0.0D));
/*     */         }
/* 472 */         if ((int)Util.getDoubleValue(cptSettingsComInfo.getHeight2(), 0.0D) > 100) {
/* 473 */           stringBuffer.append("&height=").append((int)Util.getDoubleValue(cptSettingsComInfo.getHeight2(), 0.0D));
/*     */         }
/* 475 */         stringBuffer.append("&st=").append(cptSettingsComInfo.getSt2());
/* 476 */         stringBuffer.append("&capitalid=").append(paramString3);
/* 477 */         stringBuffer.append("' />");
/* 478 */         str = stringBuffer.toString();
/*     */       } else {
/* 480 */         str = paramString1;
/*     */       } 
/*     */     }
/* 483 */     return str;
/*     */   }
/*     */   
/*     */   public void removeCache() {
/* 487 */     super.removeCache();
/*     */   }
/*     */   public void setTofirstRow() {
/* 490 */     super.setTofirstRow();
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/cpt/util/CptSettingsComInfo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */