/*    */ package weaver.cpt.util;
/*    */ 
/*    */ import javax.servlet.http.HttpServletRequest;
/*    */ import org.json.JSONObject;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.User;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public abstract class OAuth
/*    */ {
/*    */   public static boolean onlyView(User paramUser, String paramString, HttpServletRequest paramHttpServletRequest, JSONObject paramJSONObject) {
/* 23 */     if (paramUser == null || paramHttpServletRequest == null || "".equals(Util.null2String(paramString))) {
/* 24 */       return false;
/*    */     }
/* 26 */     String str = (paramHttpServletRequest.getParameter("authFlag") != null) ? Util.null2String(paramHttpServletRequest.getParameter("authFlag")) : ((paramHttpServletRequest.getAttribute("authFlag") != null) ? Util.null2String(paramHttpServletRequest.getAttribute("authFlag")) : "");
/*    */ 
/*    */ 
/*    */     
/* 30 */     if (!"".equals(str) && ("cpt".equalsIgnoreCase(paramString) || "prj".equalsIgnoreCase(paramString))) {
/* 31 */       String str1 = Util.null2String(getAuthClazz(str));
/* 32 */       if (!"".equals(str1)) {
/*    */         
/*    */         try {
/* 35 */           OAuth oAuth = (OAuth)Class.forName(str1).newInstance();
/* 36 */           return oAuth.rightCheck(paramUser, paramString, paramHttpServletRequest, paramJSONObject);
/* 37 */         } catch (Exception exception) {
/* 38 */           exception.printStackTrace();
/* 39 */           return false;
/*    */         } 
/*    */       }
/*    */     } else {
/* 43 */       return false;
/*    */     } 
/* 45 */     return false;
/*    */   }
/*    */   
/*    */   private static String getAuthClazz(String paramString) {
/* 49 */     RecordSet recordSet = new RecordSet();
/* 50 */     recordSet.executeSql("select auth_clazz_ from cpt_oauth where auth_flag_='" + paramString + "' ");
/* 51 */     if (recordSet.next()) {
/* 52 */       return recordSet.getString("auth_clazz_");
/*    */     }
/* 54 */     return "";
/*    */   }
/*    */   
/*    */   public abstract boolean rightCheck(User paramUser, String paramString, HttpServletRequest paramHttpServletRequest, JSONObject paramJSONObject);
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/cpt/util/OAuth.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */