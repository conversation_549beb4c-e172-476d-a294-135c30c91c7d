/*     */ package weaver.cpt.util;
/*     */ 
/*     */ import com.api.cpt.util.FieldInfoManager;
/*     */ import java.util.ArrayList;
/*     */ import java.util.regex.Matcher;
/*     */ import java.util.regex.Pattern;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.workflow.field.BrowserComInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class PrintUtil
/*     */ {
/*     */   public String parse(String paramString1, User paramUser, String paramString2, HttpServletRequest paramHttpServletRequest) {
/*  23 */     StringBuilder stringBuilder = new StringBuilder("");
/*  24 */     ArrayList<String> arrayList = Util.TokenizerString(paramString2, ",");
/*  25 */     int i = arrayList.size();
/*  26 */     if (i > 0) {
/*  27 */       CptFieldComInfo cptFieldComInfo = new CptFieldComInfo();
/*  28 */       BrowserComInfo browserComInfo = new BrowserComInfo();
/*  29 */       ArrayList<String> arrayList1 = cptFieldComInfo.getField_names();
/*  30 */       ArrayList<String> arrayList2 = cptFieldComInfo.getField_htmltypes();
/*  31 */       ArrayList<String> arrayList3 = cptFieldComInfo.getField_types();
/*  32 */       ArrayList<String> arrayList4 = cptFieldComInfo.getField_ids();
/*     */       
/*  34 */       RecordSet recordSet1 = new RecordSet();
/*  35 */       RecordSet recordSet2 = new RecordSet();
/*  36 */       RecordSet recordSet3 = new RecordSet();
/*  37 */       int j = 1;
/*  38 */       int k = 1;
/*  39 */       String str = "";
/*  40 */       recordSet3.executeSql("select * from CPT_PRINT_SET where id=-1");
/*  41 */       if (recordSet3.next()) {
/*  42 */         str = recordSet3.getString("forcepage");
/*  43 */         k = recordSet3.getInt("row1");
/*  44 */         j = recordSet3.getInt("col");
/*     */       } 
/*  46 */       if (k < 1) k = 1; 
/*  47 */       if (j < 1) j = 1; 
/*  48 */       int m = 0;
/*  49 */       if ((m = i % k * j) > 0) {
/*     */         
/*  51 */         int n = k * j - m;
/*  52 */         for (byte b1 = 0; b1 < n; b1++) {
/*  53 */           arrayList.add("");
/*     */         }
/*     */       } 
/*  56 */       stringBuilder.append("<table><tr>");
/*  57 */       byte b = 0;
/*  58 */       for (String str1 : arrayList) {
/*     */         
/*  60 */         b++;
/*     */         
/*  62 */         stringBuilder.append("<td><div style=''>");
/*  63 */         if (Util.getIntValue(str1) > 0) {
/*  64 */           stringBuilder.append(getContents2(str1, paramString1, paramUser, browserComInfo, arrayList1, arrayList2, arrayList3, arrayList4, recordSet1, recordSet2, paramHttpServletRequest));
/*     */         }
/*  66 */         stringBuilder.append("</div></td>");
/*     */         
/*  68 */         if (b % j == 0) {
/*  69 */           stringBuilder.append("</tr><tr>");
/*     */         }
/*  71 */         if (b == j * k) {
/*  72 */           stringBuilder.append("</table>");
/*     */         }
/*     */         
/*  75 */         if ("1".equals(str) && b % j * k == 0 && i > j * k) {
/*  76 */           stringBuilder.append("<P style='width:0px;height:0px;page-break-before:always'>&nbsp;</P>");
/*     */         }
/*     */         
/*  79 */         if (b % j * k == 0) {
/*  80 */           stringBuilder.append("<table><tr>");
/*     */         }
/*     */       } 
/*     */       
/*  84 */       if (b == j * k) {
/*  85 */         stringBuilder.append("</table>");
/*     */       }
/*     */     } 
/*  88 */     return stringBuilder.toString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String getContents2(String paramString1, String paramString2, User paramUser, BrowserComInfo paramBrowserComInfo, ArrayList<String> paramArrayList1, ArrayList<String> paramArrayList2, ArrayList<String> paramArrayList3, ArrayList<String> paramArrayList4, RecordSet paramRecordSet1, RecordSet paramRecordSet2, HttpServletRequest paramHttpServletRequest) {
/* 109 */     paramRecordSet1.isReturnDecryptData(true);
/* 110 */     paramRecordSet1.executeSql("select * from cptcapital where id=" + paramString1);
/* 111 */     if (paramRecordSet1.next()) {
/* 112 */       Pattern pattern = Pattern.compile("#\\[[_0-9a-zA-Z-]{1,}\\]");
/* 113 */       Matcher matcher = pattern.matcher(paramString2);
/* 114 */       while (matcher.find()) {
/* 115 */         String str = matcher.group();
/* 116 */         if ("#[1d-barcode]".equals(str)) {
/* 117 */           String str1 = paramRecordSet1.getString("mark");
/* 118 */           String str2 = paramRecordSet1.getString("barcode");
/* 119 */           if ("".equals(Util.null2String(str2))) {
/* 120 */             str2 = str1;
/*     */           }
/* 122 */           String str3 = CptSettingsComInfo.getBarcodeImageStr(paramHttpServletRequest, str2, str1, paramString1);
/* 123 */           paramString2 = paramString2.replace(str, str3); continue;
/* 124 */         }  if ("#[2d-barcode]".equals(str)) {
/* 125 */           String str1 = CptSettingsComInfo.getBarcodeImageStr2(paramHttpServletRequest, "", "", paramString1);
/* 126 */           paramString2 = paramString2.replace(str, str1); continue;
/*     */         } 
/* 128 */         if (str != null && str.length() > 3) {
/* 129 */           String str1 = str.substring(2, str.length() - 1);
/* 130 */           int i = paramArrayList1.indexOf(str1);
/* 131 */           if (i > -1) {
/* 132 */             int j = Util.getIntValue(paramArrayList2.get(i), 1);
/* 133 */             int k = Util.getIntValue(paramArrayList3.get(i), 0);
/* 134 */             int m = Util.getIntValue(paramArrayList4.get(i), 0);
/* 135 */             String str2 = paramRecordSet1.getString("cptcapital", str1, true, true);
/* 136 */             ArrayList<E> arrayList = Util.TokenizerString(str2, ",");
/*     */             
/* 138 */             String str3 = "";
/* 139 */             if (j == 3) {
/* 140 */               if (k == 2 || k == 19 || k == 226 || k == 227) {
/* 141 */                 str3 = str2;
/* 142 */               } else if (k == 161 || k == 162) {
/* 143 */                 FieldInfoManager fieldInfoManager = new FieldInfoManager();
/* 144 */                 str3 = fieldInfoManager.getDefinedSingle2Multi_print(m, null, str2);
/*     */               } else {
/* 146 */                 String str4 = paramBrowserComInfo.getBrowsertablename(k + "");
/* 147 */                 String str5 = paramBrowserComInfo.getBrowsercolumname(k + "");
/* 148 */                 String str6 = paramBrowserComInfo.getBrowserkeycolumname(k + "");
/* 149 */                 for (byte b = 0; b < arrayList.size(); b++) {
/* 150 */                   int n = Util.getIntValue(arrayList.get(b).toString(), 0);
/* 151 */                   String str7 = "select " + str5 + " from " + str4 + " where " + str6 + "=" + n;
/* 152 */                   paramRecordSet2.executeSql(str7);
/* 153 */                   paramRecordSet2.next();
/* 154 */                   String str8 = paramRecordSet2.getString(1);
/* 155 */                   str3 = str3 + str8 + ",";
/*     */                 } 
/*     */               } 
/* 158 */             } else if (j == 4) {
/* 159 */               if ("1".equals(str2)) {
/* 160 */                 str3 = SystemEnv.getHtmlLabelName(163, paramUser.getLanguage());
/*     */               } else {
/* 162 */                 str3 = SystemEnv.getHtmlLabelName(161, paramUser.getLanguage());
/*     */               } 
/* 164 */             } else if (j == 5) {
/*     */               
/* 166 */               String str4 = "";
/* 167 */               String str5 = "";
/* 168 */               paramRecordSet2.executeQuery("select iscommon,cid from cptDefineField where fieldname=?", new Object[] { str1 });
/* 169 */               if (paramRecordSet2.next()) {
/* 170 */                 str4 = Util.null2String(paramRecordSet2.getString("iscommon"));
/* 171 */                 str5 = Util.null2String(paramRecordSet2.getString("cid"));
/*     */               } 
/* 173 */               if (str4.equals("1")) {
/* 174 */                 byte b = 0;
/* 175 */                 paramRecordSet2.executeQuery("select name from mode_selectitempagedetail where statelev =1 and cancel <> 1 and mainid=? order by id asc", new Object[] { str5 });
/* 176 */                 while (paramRecordSet2.next()) {
/* 177 */                   if (str2.equalsIgnoreCase(b + "")) {
/* 178 */                     str3 = Util.null2String(paramRecordSet2.getString("name"));
/*     */                     break;
/*     */                   } 
/* 181 */                   b++;
/*     */                 } 
/*     */               } else {
/*     */                 
/* 185 */                 paramRecordSet2.executeSql("select selectvalue,selectname from cpt_SelectItem where fieldid = " + m + "  order by listorder,id");
/*     */                 
/* 187 */                 while (paramRecordSet2.next()) {
/* 188 */                   String str6 = Util.null2String(paramRecordSet2
/* 189 */                       .getString("selectvalue"));
/* 190 */                   String str7 = Util.toScreen(paramRecordSet2
/* 191 */                       .getString("selectname"), paramUser.getLanguage());
/* 192 */                   if (str6.equals(str2)) {
/* 193 */                     str3 = str3 + str7;
/*     */                   }
/*     */                 } 
/*     */               } 
/*     */             } else {
/* 198 */               str3 = str2;
/*     */             } 
/* 200 */             if (str3.endsWith(",")) {
/* 201 */               str3 = str3.substring(0, str3.length() - 1);
/*     */             }
/* 203 */             paramString2 = paramString2.replace(str, str3);
/*     */           } 
/*     */         } 
/*     */       } 
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 212 */     return paramString2;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/cpt/util/PrintUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */