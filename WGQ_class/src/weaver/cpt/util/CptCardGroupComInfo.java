/*     */ package weaver.cpt.util;
/*     */ 
/*     */ import java.text.DecimalFormat;
/*     */ import java.util.TreeMap;
/*     */ import org.json.JSONException;
/*     */ import org.json.JSONObject;
/*     */ import weaver.cache.CacheBase;
/*     */ import weaver.cache.CacheColumn;
/*     */ import weaver.cache.CacheColumnType;
/*     */ import weaver.cache.PKColumn;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CptCardGroupComInfo
/*     */   extends CacheBase
/*     */ {
/*  26 */   protected static String TABLE_NAME = "cpt_cptcardgroup";
/*     */   
/*  28 */   protected static String TABLE_WHERE = null;
/*     */   
/*  30 */   protected static String TABLE_ORDER = "dsporder ASC";
/*     */   
/*     */   @PKColumn(type = CacheColumnType.NUMBER)
/*  33 */   protected static String PK_NAME = "id";
/*     */   
/*     */   @CacheColumn
/*     */   protected static int groupname;
/*     */   
/*     */   @CacheColumn
/*     */   protected static int grouplabel;
/*     */   @CacheColumn
/*     */   protected static int dsporder;
/*     */   @CacheColumn
/*     */   protected static int isopen;
/*     */   @CacheColumn
/*     */   protected static int ismand;
/*     */   @CacheColumn
/*     */   protected static int isused;
/*     */   @CacheColumn
/*     */   protected static int issystem;
/*  50 */   private DecimalFormat df = new DecimalFormat("00000.00");
/*  51 */   private TreeMap<String, JSONObject> usedFieldMap = null;
/*  52 */   private TreeMap<String, JSONObject> mandFieldMap = null;
/*  53 */   private TreeMap<String, JSONObject> openFieldMap = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getGroupNum() {
/*  62 */     return size();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean next() {
/*  72 */     return super.next();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean next(String paramString) {
/*  83 */     setTofirstRow();
/*  84 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getGroupid() {
/*  95 */     return (String)getRowValue(0);
/*     */   }
/*     */ 
/*     */   
/*     */   public String getGroupName() {
/* 100 */     return (String)getRowValue(groupname);
/*     */   }
/*     */ 
/*     */   
/*     */   public String getGroupName(String paramString) {
/* 105 */     return (String)getValue(groupname, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public String getLabel() {
/* 111 */     return (String)getRowValue(grouplabel);
/*     */   }
/*     */   public String getLabel(String paramString) {
/* 114 */     return (String)getValue(grouplabel, paramString);
/*     */   }
/*     */   public String getDsporder() {
/* 117 */     return (String)getRowValue(dsporder);
/*     */   }
/*     */   public String getDsporder(String paramString) {
/* 120 */     return (String)getValue(dsporder, paramString);
/*     */   }
/*     */   public String getIsopen() {
/* 123 */     return (String)getRowValue(isopen);
/*     */   }
/*     */   public String getIsopen(String paramString) {
/* 126 */     return (String)getValue(isopen, paramString);
/*     */   }
/*     */   public String getIsmand() {
/* 129 */     return (String)getRowValue(ismand);
/*     */   }
/*     */   public String getIsmand(String paramString) {
/* 132 */     return (String)getValue(ismand, paramString);
/*     */   }
/*     */   public String getIsused() {
/* 135 */     return (String)getRowValue(isused);
/*     */   }
/*     */   public String getIsused(String paramString) {
/* 138 */     return (String)getValue(isused, paramString);
/*     */   }
/*     */   public String getIsSystem() {
/* 141 */     return (String)getRowValue(issystem);
/*     */   }
/*     */   public String getIsSystem(String paramString) {
/* 144 */     return (String)getValue(issystem, paramString);
/*     */   }
/*     */   
/*     */   public void removeCache() {
/* 148 */     super.removeCache();
/*     */   }
/*     */   public void setTofirstRow() {
/* 151 */     super.setTofirstRow();
/*     */   }
/*     */   
/*     */   public TreeMap<String, JSONObject> getUsedFieldMap() {
/* 155 */     if (this.usedFieldMap == null) {
/* 156 */       this.usedFieldMap = new TreeMap<>();
/* 157 */       CptCardGroupComInfo cptCardGroupComInfo = new CptCardGroupComInfo();
/* 158 */       cptCardGroupComInfo.setTofirstRow();
/* 159 */       while (cptCardGroupComInfo.next()) {
/* 160 */         String str = cptCardGroupComInfo.getIsused();
/* 161 */         if ("1".equals(str)) {
/* 162 */           JSONObject jSONObject = new JSONObject();
/*     */           try {
/* 164 */             jSONObject.put("id", cptCardGroupComInfo.getGroupid());
/* 165 */             jSONObject.put("fieldname", cptCardGroupComInfo.getGroupName());
/* 166 */             jSONObject.put("fieldlabel", cptCardGroupComInfo.getLabel());
/* 167 */             jSONObject.put("dsporder", cptCardGroupComInfo.getDsporder());
/* 168 */             jSONObject.put("isopen", cptCardGroupComInfo.getIsopen());
/* 169 */             jSONObject.put("ismand", cptCardGroupComInfo.getIsmand());
/* 170 */             jSONObject.put("isused", cptCardGroupComInfo.getIsused());
/* 171 */             jSONObject.put("issystem", cptCardGroupComInfo.getIsSystem());
/* 172 */           } catch (JSONException jSONException) {
/* 173 */             writeLog(jSONException);
/*     */           } 
/* 175 */           this.usedFieldMap.put(this.df.format(Util.getDoubleValue(cptCardGroupComInfo.getDsporder())), jSONObject);
/*     */         } 
/*     */       } 
/*     */     } 
/* 179 */     return this.usedFieldMap;
/*     */   }
/*     */   public TreeMap<String, JSONObject> getOpenFieldMap() {
/* 182 */     if (this.openFieldMap == null) {
/* 183 */       this.openFieldMap = new TreeMap<>();
/* 184 */       CptCardGroupComInfo cptCardGroupComInfo = new CptCardGroupComInfo();
/* 185 */       cptCardGroupComInfo.setTofirstRow();
/* 186 */       while (cptCardGroupComInfo.next()) {
/* 187 */         String str = cptCardGroupComInfo.getIsopen();
/* 188 */         if ("1".equals(str)) {
/* 189 */           JSONObject jSONObject = new JSONObject();
/*     */           try {
/* 191 */             jSONObject.put("id", cptCardGroupComInfo.getGroupid());
/* 192 */             jSONObject.put("fieldname", cptCardGroupComInfo.getGroupName());
/* 193 */             jSONObject.put("fieldlabel", cptCardGroupComInfo.getLabel());
/* 194 */             jSONObject.put("dsporder", cptCardGroupComInfo.getDsporder());
/* 195 */             jSONObject.put("isopen", cptCardGroupComInfo.getIsopen());
/* 196 */             jSONObject.put("ismand", cptCardGroupComInfo.getIsmand());
/* 197 */             jSONObject.put("isused", cptCardGroupComInfo.getIsused());
/* 198 */             jSONObject.put("issystem", cptCardGroupComInfo.getIsSystem());
/* 199 */           } catch (JSONException jSONException) {
/* 200 */             writeLog(jSONException);
/*     */           } 
/* 202 */           this.openFieldMap.put(this.df.format(Util.getDoubleValue(cptCardGroupComInfo.getDsporder())), jSONObject);
/*     */         } 
/*     */       } 
/*     */     } 
/* 206 */     return this.openFieldMap;
/*     */   }
/*     */   public TreeMap<String, JSONObject> getMandFieldMap() {
/* 209 */     if (this.mandFieldMap == null) {
/* 210 */       this.mandFieldMap = new TreeMap<>();
/* 211 */       CptCardGroupComInfo cptCardGroupComInfo = new CptCardGroupComInfo();
/* 212 */       cptCardGroupComInfo.setTofirstRow();
/* 213 */       while (cptCardGroupComInfo.next()) {
/* 214 */         String str = cptCardGroupComInfo.getIsmand();
/* 215 */         if ("1".equals(str)) {
/* 216 */           JSONObject jSONObject = new JSONObject();
/*     */           try {
/* 218 */             jSONObject.put("id", cptCardGroupComInfo.getGroupid());
/* 219 */             jSONObject.put("fieldname", cptCardGroupComInfo.getGroupName());
/* 220 */             jSONObject.put("fieldlabel", cptCardGroupComInfo.getLabel());
/* 221 */             jSONObject.put("dsporder", cptCardGroupComInfo.getDsporder());
/* 222 */             jSONObject.put("isopen", cptCardGroupComInfo.getIsopen());
/* 223 */             jSONObject.put("ismand", cptCardGroupComInfo.getIsmand());
/* 224 */             jSONObject.put("isused", cptCardGroupComInfo.getIsused());
/* 225 */             jSONObject.put("issystem", cptCardGroupComInfo.getIsSystem());
/* 226 */           } catch (JSONException jSONException) {
/* 227 */             writeLog(jSONException);
/*     */           } 
/* 229 */           this.mandFieldMap.put(this.df.format(Util.getDoubleValue(cptCardGroupComInfo.getDsporder())), jSONObject);
/*     */         } 
/*     */       } 
/*     */     } 
/* 233 */     return this.mandFieldMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean existsFields(String paramString) {
/* 241 */     RecordSet recordSet = new RecordSet();
/* 242 */     recordSet.executeSql("select 1 from cpt_cptcardgroup t1 where t1.id=" + paramString + " and exists(select 1 from cptDefineField t2 where t2.groupid=t1.id ) ");
/* 243 */     if (recordSet.next()) {
/* 244 */       return true;
/*     */     }
/* 246 */     return false;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/cpt/util/CptCardGroupComInfo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */