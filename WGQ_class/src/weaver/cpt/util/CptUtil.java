/*    */ package weaver.cpt.util;
/*    */ 
/*    */ import weaver.conn.RecordSet;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class CptUtil
/*    */ {
/*    */   public static boolean checkmarkstr(String paramString) {
/* 14 */     RecordSet recordSet = new RecordSet();
/* 15 */     recordSet.executeSql("select * from cptcapital where mark='" + paramString + "' and isdata=2");
/* 16 */     if (!recordSet.next()) {
/* 17 */       return false;
/*    */     }
/* 19 */     return true;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/cpt/util/CptUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */