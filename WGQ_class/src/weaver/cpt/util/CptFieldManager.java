/*      */ package weaver.cpt.util;
/*      */ 
/*      */ import com.weaver.formmodel.util.StringHelper;
/*      */ import java.util.ArrayList;
/*      */ import java.util.Iterator;
/*      */ import java.util.Map;
/*      */ import java.util.TreeMap;
/*      */ import javax.servlet.http.HttpSession;
/*      */ import org.json.JSONObject;
/*      */ import weaver.conn.RecordSet;
/*      */ import weaver.conn.RecordSetTrans;
/*      */ import weaver.cpt.capital.CapitalComInfo;
/*      */ import weaver.file.FileUpload;
/*      */ import weaver.formmode.browser.FormModeBrowserUtil;
/*      */ import weaver.general.BaseBean;
/*      */ import weaver.general.Util;
/*      */ import weaver.hrm.User;
/*      */ import weaver.hrm.resource.ResourceComInfo;
/*      */ import weaver.systeminfo.SystemEnv;
/*      */ import weaver.workflow.field.BrowserComInfo;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ public class CptFieldManager
/*      */   extends BaseBean
/*      */ {
/*      */   public void updateCusfieldValue(String paramString, FileUpload paramFileUpload, User paramUser) {
/*   43 */     if ("".equals(Util.null2String(paramString)) || paramFileUpload == null || paramUser == null) {
/*      */       return;
/*      */     }
/*      */     try {
/*   47 */       CptFieldComInfo cptFieldComInfo = new CptFieldComInfo();
/*   48 */       TreeMap<String, JSONObject> treeMap = cptFieldComInfo.getOpenFieldMap();
/*      */       
/*   50 */       if (treeMap != null && treeMap.size() > 0) {
/*   51 */         RecordSet recordSet = new RecordSet();
/*   52 */         Iterator<Map.Entry> iterator = treeMap.entrySet().iterator();
/*   53 */         String str = "";
/*      */         
/*   55 */         ArrayList<String> arrayList = new ArrayList();
/*   56 */         while (iterator.hasNext()) {
/*   57 */           Map.Entry entry = iterator.next();
/*   58 */           JSONObject jSONObject = (JSONObject)entry.getValue();
/*   59 */           int i = Util.getIntValue(jSONObject.getString("type"));
/*   60 */           int j = Util.getIntValue(jSONObject.getString("fieldhtmltype"));
/*   61 */           String str1 = Util.null2String(jSONObject.getString("fielddbtype"));
/*   62 */           String str2 = jSONObject.getString("fieldname");
/*   63 */           String str3 = Util.null2String(paramFileUpload.getParameter("field" + jSONObject.getString("id")));
/*      */           
/*   65 */           if ("oracle".equalsIgnoreCase(recordSet.getDBType())) {
/*   66 */             if (str1.toUpperCase().indexOf("INT") >= 0) {
/*   67 */               if ("5".equals(Integer.valueOf(j))) {
/*   68 */                 if (!Util.null2String(str3).equals("")) {
/*   69 */                   str = str + str2 + " = " + Util.getIntValue(str3) + ","; continue;
/*      */                 } 
/*   71 */                 str = str + str2 + " = NULL,";
/*      */                 continue;
/*      */               } 
/*   74 */               if (!Util.null2String(str3).equals("")) {
/*   75 */                 str = str + str2 + " = " + Util.getIntValue(str3) + ","; continue;
/*      */               } 
/*   77 */               str = str + str2 + " = NULL,";
/*      */               continue;
/*      */             } 
/*   80 */             if (str1.toUpperCase().indexOf("NUMBER") >= 0 || str1.toUpperCase().indexOf("FLOAT") >= 0 || str1.toUpperCase().indexOf("DECIMAL") >= 0) {
/*   81 */               int k = str1.indexOf(",");
/*   82 */               int m = 2;
/*   83 */               if (k > -1) {
/*   84 */                 m = Util.getIntValue(str1.substring(k + 1, str1.length() - 1).trim(), 2);
/*      */               } else {
/*   86 */                 m = 2;
/*      */               } 
/*   88 */               if (!Util.null2String(str3).equals("")) {
/*   89 */                 str = str + str2 + " = " + Util.getPointValue2(str3, m) + ",";
/*      */                 continue;
/*      */               } 
/*   92 */               str = str + str2 + " = NULL,"; continue;
/*      */             } 
/*   94 */             if (j == 6) {
/*      */               continue;
/*      */             }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */             
/*  124 */             String str5 = "";
/*  125 */             if (j == 3 && (i == 161 || i == 162)) {
/*  126 */               str5 = Util.null2String(str3);
/*  127 */               str5 = str5.trim();
/*      */             }
/*  129 */             else if (j == 2 && i == 2) {
/*  130 */               str5 = Util.toHtml100(str3);
/*  131 */               str5 = StringHelper.convertSpecialChar2Html(str3);
/*  132 */             } else if (j == 1 && i == 1) {
/*  133 */               str5 = StringHelper.convertSpecialChar2Html(str3);
/*  134 */               str5 = Util.toHtmlForWorkflow(str5);
/*  135 */             } else if (j == 2 && i == 1) {
/*  136 */               str5 = Util.StringReplace(str3, " ", "&nbsp;");
/*  137 */               str5 = StringHelper.convertSpecialChar2Html(str5);
/*      */               
/*  139 */               str5 = Util.toHtmlForWorkflowForMode(str5);
/*      */             } else {
/*  141 */               str5 = Util.StringReplace(Util.toHtml10(str3), " ", "&nbsp;");
/*  142 */               str5 = StringHelper.convertSpecialChar2Html(str5);
/*  143 */               str5 = Util.toHtmlForWorkflow(str5);
/*      */             } 
/*      */             
/*  146 */             str5 = Util.StringReplace(str5, "weaver2017", "+");
/*      */             
/*  148 */             if (2 == j || FormModeBrowserUtil.isMultiBrowser("" + j, "" + i)) {
/*  149 */               str = str + str2 + " = ?,";
/*  150 */               arrayList.add(str5); continue;
/*      */             } 
/*  152 */             str = str + str2 + " = '" + str5 + "',";
/*      */             
/*      */             continue;
/*      */           } 
/*      */           
/*  157 */           if (str1.toUpperCase().indexOf("INT") >= 0) {
/*  158 */             if ("5".equals(Integer.valueOf(j))) {
/*  159 */               if (!"".equals(str3)) {
/*  160 */                 str = str + str2 + " = " + Util.getIntValue(str3) + ","; continue;
/*      */               } 
/*  162 */               str = str + str2 + " = NULL,";
/*      */               continue;
/*      */             } 
/*  165 */             if (!"".equals(str3)) {
/*  166 */               str = str + str2 + " = " + Util.getIntValue(str3) + ","; continue;
/*      */             } 
/*  168 */             str = str + str2 + " = NULL,";
/*      */             continue;
/*      */           } 
/*  171 */           if (str1.toUpperCase().indexOf("DECIMAL") >= 0 || str1.toUpperCase().indexOf("FLOAT") >= 0) {
/*  172 */             int k = str1.indexOf(",");
/*  173 */             int m = 2;
/*  174 */             if (k > -1) {
/*  175 */               m = Util.getIntValue(str1.substring(k + 1, str1.length() - 1).trim(), 2);
/*      */             } else {
/*  177 */               m = 2;
/*      */             } 
/*  179 */             if (!"".equals(str3)) {
/*  180 */               str = str + str2 + " = " + Util.getPointValue2(str3, m) + ","; continue;
/*      */             } 
/*  182 */             str = str + str2 + " = NULL,";
/*      */             continue;
/*      */           } 
/*  185 */           if (j == 6) {
/*  186 */             String str5 = "";
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */             
/*      */             continue;
/*      */           } 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */           
/*  217 */           String str4 = "";
/*  218 */           if (j == 2 && i == 2) {
/*  219 */             str4 = Util.toHtml100(str3);
/*  220 */             str4 = StringHelper.convertSpecialChar2Html(str3);
/*  221 */           } else if (j == 1 && i == 1) {
/*  222 */             str4 = StringHelper.convertSpecialChar2Html(str3);
/*  223 */             str4 = Util.toHtmlForWorkflow(str4);
/*  224 */           } else if (j == 2 && i == 1) {
/*  225 */             str4 = Util.StringReplace(str3, " ", "&nbsp;");
/*  226 */             str4 = StringHelper.convertSpecialChar2Html(str4);
/*      */             
/*  228 */             str4 = Util.toHtmlForWorkflowForMode(str4);
/*  229 */           } else if (j == 4 && i == 1) {
/*  230 */             str4 = Util.StringReplace(str3, " ", "&nbsp;");
/*  231 */             str4 = Util.toHtmlForWorkflow(str4);
/*  232 */             if (str4.equals("")) {
/*  233 */               str4 = "0";
/*      */             }
/*      */           } else {
/*  236 */             str4 = Util.StringReplace(Util.toHtml10(str3), " ", "&nbsp;");
/*  237 */             str4 = StringHelper.convertSpecialChar2Html(str4);
/*  238 */             str4 = Util.toHtmlForWorkflow(str4);
/*      */           } 
/*  240 */           str4 = Util.StringReplace(str4, "weaver2017", "+");
/*      */           
/*  242 */           if (2 == j || FormModeBrowserUtil.isMultiBrowser("" + j, "" + i)) {
/*  243 */             str = str + str2 + " = ?,";
/*  244 */             arrayList.add(str4); continue;
/*      */           } 
/*  246 */           str = str + str2 + " = '" + str4 + "',";
/*      */         } 
/*      */ 
/*      */ 
/*      */ 
/*      */         
/*  252 */         if (!str.equals("")) {
/*  253 */           str = str.substring(0, str.length() - 1);
/*  254 */           str = " update cptcapital set  " + str + " where id = " + paramString;
/*  255 */           Object[] arrayOfObject = new Object[arrayList.size()];
/*  256 */           for (byte b = 0; b < arrayList.size(); b++) {
/*  257 */             arrayOfObject[b] = arrayList.get(b);
/*      */           }
/*  259 */           recordSet.executeSql(str, false, arrayOfObject);
/*      */         }
/*      */       
/*      */       } 
/*  263 */     } catch (Exception exception) {
/*  264 */       exception.printStackTrace();
/*  265 */       writeLog(exception.getMessage());
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void updateCusfieldValueNew(String paramString, FileUpload paramFileUpload, User paramUser) {
/*  276 */     if ("".equals(Util.null2String(paramString)) || paramFileUpload == null || paramUser == null) {
/*      */       return;
/*      */     }
/*      */     try {
/*  280 */       CptFieldComInfo cptFieldComInfo = new CptFieldComInfo();
/*  281 */       TreeMap<String, JSONObject> treeMap = cptFieldComInfo.getOpenFieldMap();
/*      */       
/*  283 */       if (treeMap != null && treeMap.size() > 0) {
/*  284 */         String str1 = "cptcapital";
/*  285 */         String str2 = "update";
/*  286 */         ArrayList<String> arrayList1 = new ArrayList();
/*  287 */         ArrayList<String> arrayList2 = new ArrayList();
/*  288 */         String str3 = "";
/*      */ 
/*      */         
/*  291 */         StringBuilder stringBuilder = new StringBuilder("update cptcapital set  ");
/*  292 */         RecordSet recordSet = new RecordSet();
/*  293 */         Iterator<Map.Entry> iterator = treeMap.entrySet().iterator();
/*  294 */         while (iterator.hasNext()) {
/*  295 */           Map.Entry entry = iterator.next();
/*  296 */           String str4 = (String)entry.getKey();
/*  297 */           JSONObject jSONObject = (JSONObject)entry.getValue();
/*  298 */           int i = Util.getIntValue(jSONObject.getString("type"));
/*  299 */           int j = Util.getIntValue(jSONObject.getString("fieldhtmltype"));
/*  300 */           String str5 = jSONObject.getString("fieldname");
/*  301 */           String str6 = Util.null2String(paramFileUpload.getParameter("field" + jSONObject.getString("id")));
/*  302 */           if ((i == 2 || i == 3 || i == 4 || i == 5) && j == 1 && "".equals(str6)) {
/*  303 */             recordSet.executeSql("select " + str5 + " from cptcapital where id=" + paramString);
/*  304 */             if (recordSet.next()) {
/*  305 */               String str = recordSet.getString(str5);
/*  306 */               if (!"".equals(str)) {
/*  307 */                 arrayList1.add(str5);
/*  308 */                 arrayList2.add("null");
/*      */               } 
/*      */             } 
/*      */             
/*      */             continue;
/*      */           } 
/*  314 */           arrayList1.add(str5);
/*  315 */           arrayList2.add(str6);
/*      */         } 
/*      */         
/*  318 */         str3 = " id=" + paramString;
/*      */ 
/*      */         
/*  321 */         recordSet.executeSql(SqlFormatUtil.formatSqlForMySql(str2, str1, arrayList1, arrayList2, str3));
/*      */       }
/*      */     
/*  324 */     } catch (Exception exception) {
/*  325 */       exception.printStackTrace();
/*  326 */       writeLog(exception.getMessage());
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static boolean isFieldUsed(String paramString) {
/*  337 */     boolean bool = false;
/*  338 */     RecordSet recordSet = new RecordSet();
/*  339 */     String str = "";
/*  340 */     if ("oracle".equalsIgnoreCase(recordSet.getDBType())) {
/*  341 */       str = "select 1 from dual where exists(select 1 from cptcapital where " + paramString + " is not null )";
/*  342 */     } else if ("mysql".equalsIgnoreCase(recordSet.getDBType())) {
/*  343 */       str = "select 1 from dual where exists(select 1 from cptcapital where " + paramString + " is not null and " + paramString + " != '' )";
/*      */     }
/*  345 */     else if ("postgresql".equalsIgnoreCase(recordSet.getDBType())) {
/*  346 */       str = "select 1  where exists(select 1 from cptcapital where " + paramString + " is not null )";
/*      */     } else {
/*      */       
/*  349 */       str = "select 1  where exists(select 1 from cptcapital where convert(varchar(200)," + paramString + ") !='' and " + paramString + " is not null )";
/*      */     } 
/*      */     
/*  352 */     recordSet.execute(str);
/*  353 */     if (recordSet.next()) {
/*  354 */       bool = true;
/*      */     }
/*      */ 
/*      */     
/*  358 */     return bool;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*  364 */   private String action = "";
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void reset() {
/*  379 */     this.action = "";
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setAction(String paramString) {
/*  390 */     this.action = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public synchronized int getNewIndexId(RecordSetTrans paramRecordSetTrans) {
/*  399 */     int i = -1;
/*      */     try {
/*  401 */       paramRecordSetTrans
/*  402 */         .executeSql("select min(id) as id from HtmlLabelIndex");
/*  403 */       if (paramRecordSetTrans.next()) {
/*  404 */         i = paramRecordSetTrans.getInt("id") - 1;
/*  405 */         if (i > -2)
/*  406 */           i = -2; 
/*      */       } 
/*  408 */     } catch (Exception exception) {
/*  409 */       i = -1;
/*      */     } 
/*  411 */     return i;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   @Deprecated
/*      */   public String getItemFieldTypeSelectForAddMainRow(User paramUser) {
/*  424 */     String str1 = ",161,162,224,226,256,257,";
/*  425 */     String str2 = "";
/*      */     
/*  427 */     if (paramUser == null) {
/*  428 */       return str2;
/*      */     }
/*  430 */     BrowserComInfo browserComInfo = new BrowserComInfo();
/*  431 */     String str3 = "1";
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  457 */     str2 = str2 + "<select class='InputStyle' style='width:100px!important;' name='itemFieldType_\" + rowindex + \"'  onChange='onChangItemFieldType(\"  + rowindex +  \")'><option value='1' " + getSelectedForItemFieldType(str3, "1") + ">" + SystemEnv.getHtmlLabelName(688, paramUser.getLanguage()) + "</option><option value='2' " + getSelectedForItemFieldType(str3, "2") + ">" + SystemEnv.getHtmlLabelName(689, paramUser.getLanguage()) + "</option><option value='3' " + getSelectedForItemFieldType(str3, "3") + ">" + SystemEnv.getHtmlLabelName(695, paramUser.getLanguage()) + "</option><option value='4' " + getSelectedForItemFieldType(str3, "4") + ">" + SystemEnv.getHtmlLabelName(691, paramUser.getLanguage()) + "</option><option value='5' " + getSelectedForItemFieldType(str3, "5") + ">" + SystemEnv.getHtmlLabelName(690, paramUser.getLanguage()) + "</option></select>";
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  469 */     String str4 = "style='display:none'";
/*  470 */     String str5 = "style='display:none'";
/*  471 */     String str6 = "style='display:none'";
/*  472 */     String str7 = "style='display:none'";
/*  473 */     String str8 = "style='display:none'";
/*  474 */     String str9 = "style='display:none'";
/*  475 */     String str10 = "style='display:none'";
/*  476 */     String str11 = "style='display:none'";
/*  477 */     String str12 = "style='display:none'";
/*      */     
/*  479 */     if ("1".equals(str3)) {
/*  480 */       str4 = "style='display:inline'";
/*  481 */     } else if ("2".equals(str3)) {
/*  482 */       str5 = "style='display:inline'";
/*  483 */     } else if ("3".equals(str3)) {
/*  484 */       str6 = "style='display:inline'";
/*  485 */     } else if (!"4".equals(str3)) {
/*  486 */       if ("5".equals(str3)) {
/*  487 */         str7 = "style='display:inline'";
/*  488 */       } else if ("6".equals(str3)) {
/*  489 */         str8 = "style='display:inline'";
/*  490 */       } else if ("7".equals(str3)) {
/*  491 */         str10 = "style='display:inline'";
/*  492 */         str11 = "style='display:block'";
/*      */       } 
/*      */     } 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  505 */     str2 = str2 + "<div id=div1_\" + rowindex + \" " + str4 + " > " + SystemEnv.getHtmlLabelName(63, paramUser.getLanguage()) + "<select class='InputStyle' style='width:100px!important;' name='documentType_\" + rowindex + \"'  onChange='onChangType(\"  + rowindex +  \")'><option value='1'>" + SystemEnv.getHtmlLabelName(608, paramUser.getLanguage()) + "</option><option value='2'>" + SystemEnv.getHtmlLabelName(696, paramUser.getLanguage()) + "</option><option value='3'>" + SystemEnv.getHtmlLabelName(697, paramUser.getLanguage()) + "</option></select></div>";
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  514 */     str2 = str2 + "<div id='div1_1_\" + rowindex + \"' " + str4 + " > " + SystemEnv.getHtmlLabelName(698, paramUser.getLanguage()) + " <input class='InputStyle' style='width:100px!important;' type='text' size=3 maxlength=3 id='itemFieldScale1_\" + rowindex + \"' name='itemFieldScale1_\" + rowindex + \"' onKeyPress='ItemPlusCount_KeyPress()' onblur='checkPlusnumber1(this);checklength(itemFieldScale1_\" + rowindex + \",itemFieldScale1span_\" + rowindex + \");checkcount1(itemFieldScale1_\" + rowindex + \")' style='text-align:right;'><span id=itemFieldScale1span_\" + rowindex + \"><IMG src='/images/BacoError_wev8.gif' align=absMiddle></span></div>";
/*      */ 
/*      */ 
/*      */     
/*  518 */     str2 = str2 + "<div id='div1_3_\" + rowindex + \"' style='display:none'> " + SystemEnv.getHtmlLabelName(15212, paramUser.getLanguage()) + "<select id='decimaldigits_\" + rowindex + \"' name='decimaldigits_\" + rowindex + \"' style='width:100px!important;'><option value='1' >1</option><option value='2' selected>2</option><option value='3' >3</option><option value='4' >4</option></select></div>";
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  531 */     str2 = str2 + "<div id=div2_\" + rowindex + \" " + str5 + " > " + SystemEnv.getHtmlLabelName(207, paramUser.getLanguage()) + " <input class='InputStyle' style='width:100px!important;' type='text' size=4 maxlength=2 value=4 id=textheight_\" + rowindex + \" name='textheight_\" + rowindex + \"' onKeyPress='ItemPlusCount_KeyPress()' onblur='checkPlusnumber1(this);checkcount1(textheight_\" + rowindex + \")' style='text-align:right;'>" + SystemEnv.getHtmlLabelName(222, paramUser.getLanguage()) + SystemEnv.getHtmlLabelName(15449, paramUser.getLanguage()) + " <input type='checkbox' value='2' name='htmledit_\" + rowindex + \"' id='htmledit_\" + rowindex + \"' onclick='onfirmhtml(\"  + rowindex +  \")'></div>";
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  536 */     str2 = str2 + "<div id=div3_\" + rowindex + \" " + str6 + " > " + SystemEnv.getHtmlLabelName(63, paramUser.getLanguage()) + "<select class='InputStyle' style='width:100px!important;' name='broswerType_\" + rowindex + \"' onChange='onChangBroswerType(\"  + rowindex +  \")'>";
/*      */     
/*  538 */     while (browserComInfo.next()) {
/*  539 */       if (browserComInfo.getBrowserurl().equals("")) {
/*      */         continue;
/*      */       }
/*  542 */       if (str1.indexOf("," + browserComInfo.getBrowserid() + ",") > -1) {
/*      */         continue;
/*      */       }
/*      */ 
/*      */ 
/*      */       
/*  548 */       str2 = str2 + "<option value='" + browserComInfo.getBrowserid() + "'>" + SystemEnv.getHtmlLabelName(Util.getIntValue(browserComInfo
/*  549 */             .getBrowserlabelid(), 0), paramUser
/*  550 */           .getLanguage()) + "</option>";
/*      */     } 
/*  552 */     str2 = str2 + "</select></div>";
/*  553 */     str2 = str2 + "<div id=div3_0_\" + rowindex + \" " + str6 + " > <span><IMG src='/images/BacoError_wev8.gif' align=absMiddle></span>";
/*      */ 
/*      */ 
/*      */     
/*  557 */     str2 = str2 + "</div>";
/*  558 */     str2 = str2 + "<div id=div3_1_\" + rowindex + \" " + str6 + " > <select class='InputStyle' style='width:100px!important;' name='definebroswerType_\" + rowindex + \"' onChange='div3_0_show(\"+rowindex+\")'>";
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  568 */     str2 = str2 + "</select></div>";
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  577 */     str2 = str2 + "<div id=div3_2_\" + rowindex + \" " + str6 + " > " + SystemEnv.getHtmlLabelName(19340, paramUser.getLanguage()) + "<select class='InputStyle' style='width:100px!important;' name='decentralizationbroswerType_\" + rowindex + \"'><option value='1' selected>" + SystemEnv.getHtmlLabelName(18916, paramUser.getLanguage()) + "</option><option value='2'>" + SystemEnv.getHtmlLabelName(18919, paramUser.getLanguage()) + "</option>";
/*      */     
/*  579 */     str2 = str2 + "</select></div>";
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  586 */     str2 = str2 + "<div id=div5_\" + rowindex + \" " + str7 + " > <input type='button' class=addbtn id=btnaddRow name=btnaddRow onclick='addoTableRow(\"  + rowindex +  \")'><input type='button' class=delbtn id=btnsubmitClear name=btnsubmitClear onclick='submitClear(\"  + rowindex +  \")'><span style='display:none;'>" + SystemEnv.getHtmlLabelName(22662, paramUser.getLanguage()) + "&nbsp;</span><BUTTON style='display:none;' id='showChildFieldBotton' class=Browser onClick=\\\"onShowChildField(childfieldidSpan_\" + rowindex + \",childfieldid_\" + rowindex + \",'_\" + rowindex + \"')\\\"></BUTTON><span style='display:none;' id='childfieldidSpan_\" + rowindex + \"'></span><input type='hidden' value='' name='childfieldid_\" + rowindex + \"' id='childfieldid_\" + rowindex + \"'></div>";
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  613 */     str2 = str2 + "<div id=div5_5_\" + rowindex + \" " + str7 + " > <table class='ViewForm' id='choiceTable_\" + rowindex + \"' cols=6 border=0><col width=20%><col width=40%><col width=40%><col width=0%><col width=0%><col width=0%><tr><td>" + SystemEnv.getHtmlLabelName(1426, paramUser.getLanguage()) + "</td><td>" + SystemEnv.getHtmlLabelName(15442, paramUser.getLanguage()) + "</td><td>" + SystemEnv.getHtmlLabelName(338, paramUser.getLanguage()) + "</td><td>" + SystemEnv.getHtmlLabelName(19206, paramUser.getLanguage()) + "</td><td style='display:none;'>" + SystemEnv.getHtmlLabelName(19207, paramUser.getLanguage()) + "</td><td style='display:none;'>" + SystemEnv.getHtmlLabelName(22663, paramUser.getLanguage()) + "</td></tr><input type='hidden' value='0' name='choiceRows_\" + rowindex + \"' id='choiceRows_\" + rowindex + \"'></table></div>";
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  625 */     str2 = str2 + "<div id=div6_\" + rowindex + \" " + str8 + " > " + SystemEnv.getHtmlLabelName(63, paramUser.getLanguage()) + "<select class='InputStyle' style='width:100px!important;' name='uploadtype_\" + rowindex + \"'  onChange='onuploadtype(this, \"  + rowindex +  \")'><option value='1'>" + SystemEnv.getHtmlLabelName(20798, paramUser.getLanguage()) + "</option><option value='2'>" + SystemEnv.getHtmlLabelName(20001, paramUser.getLanguage()) + "</option></select></div>";
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  634 */     str2 = str2 + "<div id=div6_1_\" + rowindex + \" " + str9 + " > " + SystemEnv.getHtmlLabelName(24030, paramUser.getLanguage()) + "<input class=InputStyle style='width:100px!important;' type=text name=strlength_\" + rowindex + \" size=6 value=5 maxlength=3 onKeyPress='ItemPlusCount_KeyPress()' onBlur='checkPlusnumber1(this)'>" + SystemEnv.getHtmlLabelName(22924, paramUser.getLanguage()) + "<input class=InputStyle style='width:100px!important;' type=text name=imgwidth_\" + rowindex + \" size=6 value=100 maxlength=4 onKeyPress='ItemPlusCount_KeyPress()' onBlur='checkPlusnumber1(this)'>" + SystemEnv.getHtmlLabelName(22925, paramUser.getLanguage()) + "<input class=InputStyle style='width:100px!important;' type=text name=imgheight_\" + rowindex + \" size=6 value=100 maxlength=4 onKeyPress='ItemPlusCount_KeyPress()' onBlur='checkPlusnumber1(this)'></div>";
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  645 */     str2 = str2 + "<div id=div7_\" + rowindex + \" " + str10 + " > " + SystemEnv.getHtmlLabelName(63, paramUser.getLanguage()) + "<select class='InputStyle' style='width:100px!important;' name='specialfield_\" + rowindex + \"'  onChange='specialtype(this, \"  + rowindex +  \")'><option value='1'>" + SystemEnv.getHtmlLabelName(21692, paramUser.getLanguage()) + "</option><option value='2'>" + SystemEnv.getHtmlLabelName(21693, paramUser.getLanguage()) + "</option></select></div>";
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  657 */     str2 = str2 + "<div id=div7_1_\" + rowindex + \" " + str11 + " > <table width=100% class='ViewForm' border=0><tr><td width=50%>" + SystemEnv.getHtmlLabelName(606, paramUser.getLanguage()) + "　　<input class=InputStyle style='width:100px!important;' type=text name=displayname_\" + rowindex + \" size=25 maxlength=1000></td></tr><tr><td width=100%>" + SystemEnv.getHtmlLabelName(16208, paramUser.getLanguage()) + "　<input class=InputStyle style='width:100px!important;' type=text size=25 name=linkaddress_\" + rowindex + \" maxlength=1000><br>" + SystemEnv.getHtmlLabelName(18391, paramUser.getLanguage()) + "</td></tr></table></div>";
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  663 */     str2 = str2 + "<div id=div7_2_\" + rowindex + \" " + str12 + " > <table width=100% class='ViewForm' border=0><tr><td width=12%>" + SystemEnv.getHtmlLabelName(21693, paramUser.getLanguage()) + "</td><td>　<textarea class='InputStyle' style='width:88%;height:100px' name=descriptivetext_\" + rowindex + \"></textarea></td></tr></table></div>";
/*      */ 
/*      */ 
/*      */     
/*  667 */     return str2;
/*      */   }
/*      */ 
/*      */   
/*      */   public String getItemFieldTypeSelectForAddMainRow2(User paramUser) {
/*  672 */     String str1 = "";
/*      */     
/*  674 */     if (paramUser == null) {
/*  675 */       return str1;
/*      */     }
/*  677 */     BrowserComInfo browserComInfo = new BrowserComInfo();
/*  678 */     String str2 = "1";
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  714 */     str1 = str1 + "<select class='InputStyle' name='itemFieldType' id='itemFieldType'  onChange='onChangItemFieldType(this.name)'><option value='1' " + getSelectedForItemFieldType(str2, "1") + ">" + SystemEnv.getHtmlLabelName(688, paramUser.getLanguage()) + "</option><option value='2' " + getSelectedForItemFieldType(str2, "2") + ">" + SystemEnv.getHtmlLabelName(689, paramUser.getLanguage()) + "</option><option value='3' " + getSelectedForItemFieldType(str2, "3") + ">" + SystemEnv.getHtmlLabelName(695, paramUser.getLanguage()) + "</option><option value='4' " + getSelectedForItemFieldType(str2, "4") + ">" + SystemEnv.getHtmlLabelName(691, paramUser.getLanguage()) + "</option><option value='5' " + getSelectedForItemFieldType(str2, "5") + ">" + SystemEnv.getHtmlLabelName(690, paramUser.getLanguage()) + "</option><option value='6' " + getSelectedForItemFieldType(str2, "6") + ">" + SystemEnv.getHtmlLabelName(17616, paramUser.getLanguage()) + "</option><option value='7' " + getSelectedForItemFieldType(str2, "7") + ">" + SystemEnv.getHtmlLabelName(21691, paramUser.getLanguage()) + "</option></select>";
/*      */     
/*  716 */     String str3 = "style='display:none'";
/*  717 */     String str4 = "style='display:none'";
/*  718 */     String str5 = "style='display:none'";
/*  719 */     String str6 = "style='display:none'";
/*  720 */     String str7 = "style='display:none'";
/*  721 */     String str8 = "style='display:none'";
/*  722 */     String str9 = "style='display:none'";
/*  723 */     String str10 = "style='display:none'";
/*  724 */     String str11 = "style='display:none'";
/*      */     
/*  726 */     if ("1".equals(str2)) {
/*  727 */       str3 = "style='display:inline'";
/*  728 */     } else if ("2".equals(str2)) {
/*  729 */       str4 = "style='display:inline'";
/*  730 */     } else if ("3".equals(str2)) {
/*  731 */       str5 = "style='display:inline'";
/*  732 */     } else if (!"4".equals(str2)) {
/*  733 */       if ("5".equals(str2)) {
/*  734 */         str6 = "style='display:inline'";
/*  735 */       } else if ("6".equals(str2)) {
/*  736 */         str7 = "style='display:inline'";
/*  737 */       } else if ("7".equals(str2)) {
/*  738 */         str9 = "style='display:inline'";
/*  739 */         str10 = "style='display:block'";
/*      */       } 
/*      */     } 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  756 */     str1 = str1 + "<div id=div1 " + str3 + " > " + SystemEnv.getHtmlLabelName(63, paramUser.getLanguage()) + "<select class='InputStyle' name='documentType' id='documentType'  onChange='onChangType(this.name)'><option value='1'>" + SystemEnv.getHtmlLabelName(608, paramUser.getLanguage()) + "</option><option value='2'>" + SystemEnv.getHtmlLabelName(696, paramUser.getLanguage()) + "</option><option value='3'>" + SystemEnv.getHtmlLabelName(697, paramUser.getLanguage()) + "</option><option value='4'>" + SystemEnv.getHtmlLabelName(18004, paramUser.getLanguage()) + "</option><option value='5'>" + SystemEnv.getHtmlLabelName(22395, paramUser.getLanguage()) + "</option></select></div>";
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  761 */     str1 = str1 + "<div id='div1_1' " + str3 + " > " + SystemEnv.getHtmlLabelName(698, paramUser.getLanguage()) + " <input class='InputStyle' type='text' size=3 maxlength=3 id='itemFieldScale1' name='itemFieldScale1' onKeyPress='ItemPlusCount_KeyPress()' onblur='checkPlusnumber1(this);checklength(itemFieldScale1,itemFieldScale1span);checkcount1(itemFieldScale1)' style='text-align:right;'><span id=itemFieldScale1span><IMG src='/images/BacoError_wev8.gif' align=absMiddle></span></div>";
/*      */ 
/*      */ 
/*      */     
/*  765 */     str1 = str1 + "<div id='div1_3' style='display:none'> " + SystemEnv.getHtmlLabelName(15212, paramUser.getLanguage()) + "<select id='decimaldigits' name='decimaldigits'><option value='1' >1</option><option value='2' selected>2</option><option value='3' >3</option><option value='4' >4</option></select></div>";
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  778 */     str1 = str1 + "<div id=div2 " + str4 + " > " + SystemEnv.getHtmlLabelName(207, paramUser.getLanguage()) + " <input class='InputStyle' type='text' size=4 maxlength=2 value=4 id=textheight name='textheight' onKeyPress='ItemPlusCount_KeyPress()' onblur='checkPlusnumber1(this);checkcount1(textheight)' style='text-align:right;'>" + SystemEnv.getHtmlLabelName(222, paramUser.getLanguage()) + SystemEnv.getHtmlLabelName(15449, paramUser.getLanguage()) + " <input type='checkbox' value='2' name='htmledit' id='htmledit' onclick='onfirmhtml(this)'></div>";
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  783 */     str1 = str1 + "<div id=div3 " + str5 + " > " + SystemEnv.getHtmlLabelName(63, paramUser.getLanguage()) + "<select class='InputStyle' name='broswerType' id='broswerType' onChange='onChangBroswerType(this.name)'>";
/*      */     
/*  785 */     while (browserComInfo.next()) {
/*  786 */       if (browserComInfo.getBrowserurl().equals("")) {
/*      */         continue;
/*      */       }
/*      */ 
/*      */ 
/*      */       
/*  792 */       str1 = str1 + "<option value='" + browserComInfo.getBrowserid() + "'>" + SystemEnv.getHtmlLabelName(Util.getIntValue(browserComInfo
/*  793 */             .getBrowserlabelid(), 0), paramUser
/*  794 */           .getLanguage()) + "</option>";
/*      */     } 
/*  796 */     str1 = str1 + "</select></div>";
/*  797 */     str1 = str1 + "<div id=div3_0 " + str5 + " > <span><IMG src='/images/BacoError_wev8.gif' align=absMiddle></span>";
/*      */ 
/*      */ 
/*      */     
/*  801 */     str1 = str1 + "</div>";
/*  802 */     str1 = str1 + "<div id=div3_1 " + str5 + " > <select class='InputStyle' name='definebroswerType' onChange='div3_0_show(this)'>";
/*      */ 
/*      */ 
/*      */     
/*  806 */     str1 = str1 + "</select></div>";
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  815 */     str1 = str1 + "<div id=div3_2 " + str5 + " > " + SystemEnv.getHtmlLabelName(19340, paramUser.getLanguage()) + "<select class='InputStyle' name='decentralizationbroswerType'><option value='1' selected>" + SystemEnv.getHtmlLabelName(18916, paramUser.getLanguage()) + "</option><option value='2'>" + SystemEnv.getHtmlLabelName(18919, paramUser.getLanguage()) + "</option>";
/*      */     
/*  817 */     str1 = str1 + "</select></div>";
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  828 */     str1 = str1 + "<div id=div5 " + str6 + " > <BUTTON class=addbtn id=btnaddRow name=btnaddRow onclick='addoTableRow(this)'></BUTTON><BUTTON class=delbtn id=btnsubmitClear name=btnsubmitClear onclick='submitClear(this)'></BUTTON><span style='display:none;'>" + SystemEnv.getHtmlLabelName(22662, paramUser.getLanguage()) + "&nbsp;</span><BUTTON style='display:none;' id='showChildFieldBotton' class=Browser onClick=\\\"onShowChildField(childfieldidSpan,childfieldid,'')\\\"></BUTTON><span style='display:none;' id='childfieldidSpan'></span><input type='hidden' value='' name='childfieldid' id='childfieldid'></div>";
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  855 */     str1 = str1 + "<div id=div5_5 " + str6 + " > <table class='ViewForm' id='choiceTable' cols=6 border=0><col width=20%><col width=40%><col width=40%><col width=0%><col width=0%><col width=0%><tr><td>" + SystemEnv.getHtmlLabelName(1426, paramUser.getLanguage()) + "</td><td>" + SystemEnv.getHtmlLabelName(15442, paramUser.getLanguage()) + "</td><td>" + SystemEnv.getHtmlLabelName(338, paramUser.getLanguage()) + "</td><td>" + SystemEnv.getHtmlLabelName(19206, paramUser.getLanguage()) + "</td><td style='display:none;'>" + SystemEnv.getHtmlLabelName(19207, paramUser.getLanguage()) + "</td><td style='display:none;'>" + SystemEnv.getHtmlLabelName(22663, paramUser.getLanguage()) + "</td></tr><input type='hidden' value='0' name='choiceRows' id='choiceRows'></table></div>";
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  867 */     str1 = str1 + "<div id=div6 " + str7 + " > " + SystemEnv.getHtmlLabelName(63, paramUser.getLanguage()) + "<select class='InputStyle' name='uploadtype'  onChange='onuploadtype(this)'><option value='1'>" + SystemEnv.getHtmlLabelName(20798, paramUser.getLanguage()) + "</option><option value='2'>" + SystemEnv.getHtmlLabelName(20001, paramUser.getLanguage()) + "</option></select></div>";
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  876 */     str1 = str1 + "<div id=div6_1 " + str8 + " > " + SystemEnv.getHtmlLabelName(24030, paramUser.getLanguage()) + "<input class=InputStyle  type=text name=strlength size=6 value=5 maxlength=3 onKeyPress='ItemPlusCount_KeyPress()' onBlur='checkPlusnumber1(this)'>" + SystemEnv.getHtmlLabelName(22924, paramUser.getLanguage()) + "<input class=InputStyle  type=text name=imgwidth size=6 value=100 maxlength=4 onKeyPress='ItemPlusCount_KeyPress()' onBlur='checkPlusnumber1(this)'>" + SystemEnv.getHtmlLabelName(22925, paramUser.getLanguage()) + "<input class=InputStyle  type=text name=imgheight size=6 value=100 maxlength=4 onKeyPress='ItemPlusCount_KeyPress()' onBlur='checkPlusnumber1(this)'></div>";
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  887 */     str1 = str1 + "<div id=div7 " + str9 + " > " + SystemEnv.getHtmlLabelName(63, paramUser.getLanguage()) + "<select class='InputStyle' name='specialfield'  onChange='specialtype(this)'><option value='1'>" + SystemEnv.getHtmlLabelName(21692, paramUser.getLanguage()) + "</option><option value='2'>" + SystemEnv.getHtmlLabelName(21693, paramUser.getLanguage()) + "</option></select></div>";
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  899 */     str1 = str1 + "<div id=div7_1 " + str10 + " > <table width=100% class='ViewForm' border=0><tr><td width=50%>" + SystemEnv.getHtmlLabelName(606, paramUser.getLanguage()) + "　　<input class=InputStyle type=text name=displayname size=25 maxlength=1000></td></tr><tr><td width=100%>" + SystemEnv.getHtmlLabelName(16208, paramUser.getLanguage()) + "　<input class=InputStyle type=text size=25 name=linkaddress maxlength=1000><br>" + SystemEnv.getHtmlLabelName(18391, paramUser.getLanguage()) + "</td></tr></table></div>";
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  905 */     str1 = str1 + "<div id=div7_2 " + str11 + " > <table width=100% class='ViewForm' border=0><tr><td width=12%>" + SystemEnv.getHtmlLabelName(21693, paramUser.getLanguage()) + "</td><td>　<textarea class='inputstyle' style='width:88%;height:100px' name=descriptivetext></textarea></td></tr></table></div>";
/*      */ 
/*      */ 
/*      */     
/*  909 */     return str1;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private String getSelectedForItemFieldType(String paramString1, String paramString2) {
/*  925 */     String str = "";
/*      */     
/*  927 */     if (paramString1 == null || paramString2 == null) {
/*  928 */       return str;
/*      */     }
/*      */     
/*  931 */     if (paramString1.equals(paramString2)) {
/*  932 */       str = "selected";
/*      */     }
/*      */     
/*  935 */     return str;
/*      */   }
/*      */   
/*      */   public String getFieldvalue(User paramUser, int paramInt1, int paramInt2, int paramInt3, String paramString, int paramInt4) throws Exception {
/*  939 */     return getFieldvalue((HttpSession)null, paramUser, (String)null, (String)null, paramInt1, paramInt2, paramInt3, paramString, paramInt4, false);
/*      */   }
/*      */ 
/*      */   
/*      */   public String getFieldvalue(User paramUser, int paramInt1, int paramInt2, int paramInt3, String paramString, int paramInt4, boolean paramBoolean) throws Exception {
/*  944 */     return getFieldvalue((HttpSession)null, paramUser, (String)null, (String)null, paramInt1, paramInt2, paramInt3, paramString, paramInt4, paramBoolean);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getFieldvalue(HttpSession paramHttpSession, int paramInt1, int paramInt2, int paramInt3, String paramString, int paramInt4) throws Exception {
/*  951 */     return getFieldvalue(paramHttpSession, (User)null, (String)null, (String)null, paramInt1, paramInt2, paramInt3, paramString, paramInt4, false);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getFieldvalue(HttpSession paramHttpSession, User paramUser, String paramString1, String paramString2, int paramInt1, int paramInt2, int paramInt3, String paramString3, int paramInt4, boolean paramBoolean) throws Exception {
/*  960 */     RecordSet recordSet = new RecordSet();
/*  961 */     String str = "";
/*      */     
/*  963 */     if (paramInt2 == 3) {
/*  964 */       ArrayList<String> arrayList = Util.TokenizerString(paramString3, ",");
/*  965 */       BrowserComInfo browserComInfo = new BrowserComInfo();
/*  966 */       String str1 = Util.null2String(browserComInfo.getLinkurl(paramInt3 + ""));
/*  967 */       if (paramInt3 == 2 || paramInt3 == 19 || paramInt3 == 226 || paramInt3 == 227) {
/*  968 */         str = paramString3;
/*  969 */       } else if (paramInt3 == 1 || paramInt3 == 17) {
/*  970 */         for (byte b = 0; b < arrayList.size(); b++) {
/*  971 */           if (paramBoolean)
/*      */           {
/*  973 */             str = str + "<a href=\"javascript:openhrm(" + arrayList.get(b) + ")\" onclick=\"pointerXY(event);\" >" + (new ResourceComInfo()).getResourcename(arrayList.get(b)) + "</a>&nbsp;";
/*      */           }
/*      */           else
/*      */           {
/*  977 */             str = str + (new ResourceComInfo()).getResourcename(arrayList.get(b)) + ",";
/*      */           }
/*      */         
/*      */         } 
/*  981 */       } else if (paramInt3 == 23 || paramInt3 == 26 || paramInt3 == 3) {
/*  982 */         if ("0".equals(Util.null2String(paramString3))) {
/*  983 */           return "";
/*      */         }
/*  985 */         CptSettingsComInfo cptSettingsComInfo = new CptSettingsComInfo();
/*  986 */         cptSettingsComInfo.setTofirstRow();
/*  987 */         cptSettingsComInfo.next();
/*  988 */         CapitalComInfo capitalComInfo = new CapitalComInfo();
/*  989 */         for (byte b = 0; b < arrayList.size(); b++) {
/*  990 */           if (paramBoolean) {
/*      */             
/*  992 */             str = str + "<a href=\"javascript:openFullWindowForXtable('" + str1 + arrayList.get(b) + "')\" onclick=\"pointerXY(event);\" >" + capitalComInfo.getCapitalname(arrayList.get(b)) + "</a>";
/*      */             
/*  994 */             if ("1".equals(cptSettingsComInfo.getIsopen2())) {
/*  995 */               str = str + "&nbsp;<a href='/CreateCptBarCode?barType=-1&capitalid=" + arrayList.get(b) + "' onclick=\"return hs.expand(this)\"><img src=\"/cpt/img/qrcode2_wev8.png\" onmouseover=\"this.src='/cpt/img/qrcode3_wev8.png'\" onmouseout=\"this.src='/cpt/img/qrcode2_wev8.png'\" width=\"16\" height=\"16\" style=\"vertical-align:middle;cursor: pointer;\"></a>";
/*      */             }
/*  997 */             str = str + "&nbsp;";
/*      */           } else {
/*      */             
/* 1000 */             str = str + capitalComInfo.getCapitalname(arrayList.get(b)) + ",";
/*      */           } 
/*      */         } 
/*      */       } else {
/*      */         
/* 1005 */         String str2 = browserComInfo.getBrowsertablename(paramInt3 + "");
/* 1006 */         String str3 = browserComInfo.getBrowsercolumname(paramInt3 + "");
/* 1007 */         String str4 = browserComInfo.getBrowserkeycolumname(paramInt3 + "");
/* 1008 */         for (byte b = 0; b < arrayList.size(); b++) {
/* 1009 */           int i = Util.getIntValue(arrayList.get(b).toString(), 0);
/* 1010 */           String str5 = "select " + str3 + " from " + str2 + " where " + str4 + "=" + i;
/* 1011 */           recordSet.executeSql(str5);
/* 1012 */           recordSet.next();
/* 1013 */           String str6 = recordSet.getString(1);
/* 1014 */           if (paramBoolean) {
/*      */             
/* 1016 */             if (paramInt3 == 258 || paramInt3 == 58 || paramInt3 == 263) {
/* 1017 */               str = str + Util.toScreen(str6, paramUser.getLanguage());
/*      */             } else {
/* 1019 */               if (!str1.equals("")) {
/* 1020 */                 str = str + "<a href=\"javascript:openFullWindowForXtable('" + str1 + i + "')\">";
/*      */               }
/* 1022 */               str = str + Util.toScreen(str6, paramUser.getLanguage());
/* 1023 */               if (!str1.equals("")) {
/* 1024 */                 str = str + "</a>&nbsp;";
/*      */               }
/*      */             } 
/*      */           } else {
/* 1028 */             str = str + str6 + ",";
/*      */           } 
/*      */         } 
/*      */       } 
/* 1032 */       if (str.endsWith(",")) {
/* 1033 */         str = str.substring(0, str.length() - 1);
/*      */       }
/* 1035 */     } else if (paramInt2 == 4) {
/*      */ 
/*      */ 
/*      */ 
/*      */       
/* 1040 */       if ("1".equals(paramString3)) {
/* 1041 */         str = "<span style='color:red;'>" + SystemEnv.getHtmlLabelName(163, paramUser.getLanguage()) + "</span>";
/*      */       } else {
/* 1043 */         str = SystemEnv.getHtmlLabelName(161, paramUser.getLanguage());
/*      */       } 
/* 1045 */     } else if (paramInt2 == 5) {
/*      */       
/* 1047 */       recordSet.executeSql("select selectvalue,selectname from cpt_SelectItem where fieldid = " + paramInt1 + "  order by listorder,id");
/*      */       
/* 1049 */       while (recordSet.next()) {
/* 1050 */         String str1 = Util.null2String(recordSet
/* 1051 */             .getString("selectvalue"));
/* 1052 */         if (paramHttpSession != null)
/* 1053 */           paramUser = (User)paramHttpSession.getAttribute("weaver_user@bean"); 
/* 1054 */         String str2 = Util.toScreen(recordSet
/* 1055 */             .getString("selectname"), paramUser.getLanguage());
/* 1056 */         if (str1.equals(paramString3)) {
/* 1057 */           str = str + str2;
/*      */         }
/*      */       } 
/* 1060 */     } else if (paramInt2 == 6) {
/* 1061 */       recordSet.executeSql("select id,docsubject,accessorycount from docdetail where id in(" + paramString3 + ") order by id asc");
/*      */       
/* 1063 */       while (recordSet.next()) {
/* 1064 */         str = str + recordSet.getString("docsubject") + ",";
/*      */       }
/* 1066 */       if (str.endsWith(",")) {
/* 1067 */         str = str.substring(0, str.length() - 1);
/*      */       }
/* 1069 */     } else if (paramInt2 == 2 && paramInt3 == 1) {
/* 1070 */       str = Util.toHtml(paramString3);
/*      */     } else {
/* 1072 */       str = paramString3;
/*      */     } 
/* 1074 */     return str;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static synchronized void syncFields() {
/* 1081 */     RecordSet recordSet1 = new RecordSet();
/* 1082 */     RecordSet recordSet2 = new RecordSet();
/* 1083 */     char c = '✐';
/*      */     
/* 1085 */     String str = "delete from  cpt_browdef where ( fieldid in(select id from cptDefineField where issystem is null and isopen!='1' )  or not exists(select 1 from cptDefineField t where t.id=cpt_browdef.fieldid)  )";
/*      */ 
/*      */     
/* 1088 */     recordSet1.executeSql(str);
/* 1089 */     str = " INSERT INTO cpt_browdef( fieldid,displayorder )  select t1.id,-9999 from cptDefineField t1 where t1.isopen='1' and (t1.fieldhtmltype!=2 or (t1.fieldhtmltype=2 and t1.type=1 )) and t1.fieldhtmltype!=6 and t1.fieldhtmltype!=7 and not exists(select 1 from cpt_browdef t2 where  t2.fieldid=t1.id ) ";
/*      */ 
/*      */     
/* 1092 */     recordSet1.executeSql(str);
/* 1093 */     str = "select * from cpt_browdef where displayorder=-9999";
/* 1094 */     recordSet1.executeSql(str);
/* 1095 */     while (recordSet1.next()) {
/* 1096 */       recordSet2.executeSql("update cpt_browdef set displayorder=" + ++c + " where fieldid=" + recordSet1.getInt("fieldid"));
/*      */     }
/*      */ 
/*      */     
/* 1100 */     str = "delete from  CptSearchDefinition where ( fieldname in(select fieldname from cptDefineField where (issystem is null and isopen!='1') )  or not exists (select 1 from cptDefineField t where lower(t.fieldname)=lower(CptSearchDefinition.fieldname ))  )";
/*      */ 
/*      */     
/* 1103 */     recordSet1.executeSql(str);
/*      */ 
/*      */     
/* 1106 */     str = " INSERT INTO CptSearchDefinition( fieldname,isconditionstitle,istitle,isconditions,isseniorconditions,mouldid,displayorder,searchtype )  select t1.fieldname,1,0,1,0,-1,-9999,'1' from cptDefineField t1 where t1.isopen='1' and (t1.fieldhtmltype!=2 or (t1.fieldhtmltype=2 and t1.type=1 )) and t1.fieldhtmltype!=6 and t1.fieldhtmltype!=7 and not exists(select 1 from CptSearchDefinition t2 where t2.searchtype='1' and  lower(t2.fieldname)=lower(t1.fieldname) ) ";
/*      */ 
/*      */     
/* 1109 */     recordSet1.executeSql(str);
/* 1110 */     str = "select * from CptSearchDefinition where displayorder=-9999 and searchtype='1'";
/* 1111 */     c = '✐';
/* 1112 */     recordSet1.executeSql(str);
/* 1113 */     while (recordSet1.next()) {
/* 1114 */       recordSet2.executeSql("update CptSearchDefinition set displayorder=" + ++c + " where fieldname='" + recordSet1.getString("fieldname") + "' ");
/*      */     }
/*      */ 
/*      */     
/* 1118 */     str = " INSERT INTO CptSearchDefinition( fieldname,isconditionstitle,istitle,isconditions,isseniorconditions,mouldid,displayorder,searchtype )  select t1.fieldname,1,0,1,0,-1,-9999,'2' from cptDefineField t1 where t1.isopen='1' and (t1.fieldhtmltype!=2 or (t1.fieldhtmltype=2 and t1.type=1 )) and t1.fieldhtmltype!=6 and t1.fieldhtmltype!=7 and not exists(select 1 from CptSearchDefinition t2 where t2.searchtype='2' and lower(t2.fieldname)=lower(t1.fieldname) ) ";
/*      */ 
/*      */     
/* 1121 */     recordSet1.executeSql(str);
/* 1122 */     str = "select * from CptSearchDefinition where displayorder=-9999 and searchtype='2'";
/* 1123 */     c = '✐';
/* 1124 */     recordSet1.executeSql(str);
/* 1125 */     while (recordSet1.next()) {
/* 1126 */       recordSet2.executeSql("update CptSearchDefinition set displayorder=" + ++c + " where fieldname='" + recordSet1.getString("fieldname") + "' ");
/*      */     }
/*      */ 
/*      */     
/* 1130 */     str = " INSERT INTO CptSearchDefinition( fieldname,isconditionstitle,istitle,isconditions,isseniorconditions,mouldid,displayorder,searchtype )  select t1.fieldname,1,0,1,0,-1,-9999,'3' from cptDefineField t1 where t1.isopen='1' and (t1.fieldhtmltype!=2 or (t1.fieldhtmltype=2 and t1.type=1 )) and t1.fieldhtmltype!=6 and t1.fieldhtmltype!=7 and not exists(select 1 from CptSearchDefinition t2 where t2.searchtype='3' and  lower(t2.fieldname)=lower(t1.fieldname) ) ";
/*      */ 
/*      */     
/* 1133 */     recordSet1.executeSql(str);
/* 1134 */     str = "select * from CptSearchDefinition where displayorder=-9999 and searchtype='3'";
/* 1135 */     c = '✐';
/* 1136 */     recordSet1.executeSql(str);
/* 1137 */     while (recordSet1.next()) {
/* 1138 */       recordSet2.executeSql("update CptSearchDefinition set displayorder=" + ++c + " where fieldname='" + recordSet1.getString("fieldname") + "' ");
/*      */     }
/*      */ 
/*      */     
/* 1142 */     str = " INSERT INTO CptSearchDefinition( fieldname,isconditionstitle,istitle,isconditions,isseniorconditions,mouldid,displayorder,searchtype )  select t1.fieldname,1,0,1,0,-1,-9999,'4' from cptDefineField t1 where t1.isopen='1' and (t1.fieldhtmltype!=2 or (t1.fieldhtmltype=2 and t1.type=1 )) and t1.fieldhtmltype!=6 and t1.fieldhtmltype!=7 and not exists(select 1 from CptSearchDefinition t2 where t2.searchtype='4' and  lower(t2.fieldname)=lower(t1.fieldname) ) ";
/*      */ 
/*      */     
/* 1145 */     recordSet1.executeSql(str);
/* 1146 */     str = "select * from CptSearchDefinition where displayorder=-9999 and searchtype='4'";
/* 1147 */     c = '✐';
/* 1148 */     recordSet1.executeSql(str);
/* 1149 */     while (recordSet1.next()) {
/* 1150 */       recordSet2.executeSql("update CptSearchDefinition set displayorder=" + ++c + " where fieldname='" + recordSet1.getString("fieldname") + "' ");
/*      */     }
/*      */ 
/*      */     
/* 1154 */     str = " INSERT INTO CptSearchDefinition( fieldname,isconditionstitle,istitle,isconditions,isseniorconditions,mouldid,displayorder,searchtype )  select t1.fieldname,1,0,1,0,-1,-9999,'5' from cptDefineField t1 where t1.isopen='1' and (t1.fieldhtmltype!=2 or (t1.fieldhtmltype=2 and t1.type=1 )) and t1.fieldhtmltype!=6 and t1.fieldhtmltype!=7 and not exists(select 1 from CptSearchDefinition t2 where t2.searchtype='5' and  lower(t2.fieldname)=lower(t1.fieldname) ) ";
/*      */ 
/*      */     
/* 1157 */     recordSet1.executeSql(str);
/* 1158 */     str = "select * from CptSearchDefinition where displayorder=-9999 and searchtype='5'";
/* 1159 */     c = '✐';
/* 1160 */     recordSet1.executeSql(str);
/* 1161 */     while (recordSet1.next()) {
/* 1162 */       recordSet2.executeSql("update CptSearchDefinition set displayorder=" + ++c + " where fieldname='" + recordSet1.getString("fieldname") + "' ");
/*      */     }
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getBrowserFieldvalue(String paramString1, String paramString2) throws Exception {
/* 1173 */     String[] arrayOfString = Util.TokenizerString2(paramString2, "+");
/* 1174 */     if (arrayOfString != null && arrayOfString.length >= 4) {
/* 1175 */       return getFieldvalue((HttpSession)null, new User(Util.getIntValue(arrayOfString[0])), (String)null, (String)null, Util.getIntValue(arrayOfString[1], 0), Util.getIntValue(arrayOfString[2]), Util.getIntValue(arrayOfString[3]), paramString1, 0, true);
/*      */     }
/* 1177 */     return "";
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getBrowserFieldvalue_new(String paramString1, String paramString2) throws Exception {
/* 1190 */     String[] arrayOfString = Util.TokenizerString2(paramString2, "+");
/* 1191 */     if (arrayOfString != null && arrayOfString.length >= 4) {
/* 1192 */       return getFieldvalue_new((HttpSession)null, Util.getIntValue(arrayOfString[0], 7), (String)null, (String)null, Util.getIntValue(arrayOfString[1], 0), Util.getIntValue(arrayOfString[2]), Util.getIntValue(arrayOfString[3]), paramString1, 0, true);
/*      */     }
/* 1194 */     return "";
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getFieldvalue_new(HttpSession paramHttpSession, int paramInt1, String paramString1, String paramString2, int paramInt2, int paramInt3, int paramInt4, String paramString3, int paramInt5, boolean paramBoolean) throws Exception {
/* 1202 */     RecordSet recordSet = new RecordSet();
/* 1203 */     String str = "";
/*      */     
/* 1205 */     if (paramInt3 == 3) {
/* 1206 */       ArrayList<String> arrayList = Util.TokenizerString(paramString3, ",");
/* 1207 */       BrowserComInfo browserComInfo = new BrowserComInfo();
/* 1208 */       String str1 = Util.null2String(browserComInfo.getLinkurl(paramInt4 + ""));
/* 1209 */       if (paramInt4 == 2 || paramInt4 == 19 || paramInt4 == 226 || paramInt4 == 227) {
/* 1210 */         str = paramString3;
/* 1211 */       } else if (paramInt4 == 1 || paramInt4 == 17) {
/* 1212 */         for (byte b = 0; b < arrayList.size(); b++) {
/* 1213 */           if (paramBoolean)
/*      */           {
/* 1215 */             str = str + "<a href=\"javascript:openhrm(" + arrayList.get(b) + ")\" onclick=\"pointerXY(event);\" >" + (new ResourceComInfo()).getResourcename(arrayList.get(b)) + "</a>&nbsp;";
/*      */           }
/*      */           else
/*      */           {
/* 1219 */             str = str + (new ResourceComInfo()).getResourcename(arrayList.get(b)) + ",";
/*      */           }
/*      */         
/*      */         } 
/* 1223 */       } else if (paramInt4 == 23 || paramInt4 == 26 || paramInt4 == 3) {
/* 1224 */         if ("0".equals(Util.null2String(paramString3))) {
/* 1225 */           return "";
/*      */         }
/* 1227 */         CptSettingsComInfo cptSettingsComInfo = new CptSettingsComInfo();
/* 1228 */         cptSettingsComInfo.setTofirstRow();
/* 1229 */         cptSettingsComInfo.next();
/* 1230 */         CapitalComInfo capitalComInfo = new CapitalComInfo();
/* 1231 */         for (byte b = 0; b < arrayList.size(); b++) {
/* 1232 */           if (paramBoolean) {
/*      */             
/* 1234 */             str = str + "<a href=\"javascript:openFullWindowForXtable('" + str1 + arrayList.get(b) + "')\" onclick=\"pointerXY(event);\" >" + capitalComInfo.getCapitalname(arrayList.get(b)) + "</a>";
/*      */             
/* 1236 */             if ("1".equals(cptSettingsComInfo.getIsopen2())) {
/* 1237 */               str = str + "&nbsp;<a href='/CreateCptBarCode?barType=-1&capitalid=" + arrayList.get(b) + "' onclick=\"return hs.expand(this)\"><img src=\"/cpt/img/qrcode2_wev8.png\" onmouseover=\"this.src='/cpt/img/qrcode3_wev8.png'\" onmouseout=\"this.src='/cpt/img/qrcode2_wev8.png'\" width=\"16\" height=\"16\" style=\"vertical-align:middle;cursor: pointer;\"></a>";
/*      */             }
/* 1239 */             str = str + "&nbsp;";
/*      */           } else {
/*      */             
/* 1242 */             str = str + capitalComInfo.getCapitalname(arrayList.get(b)) + ",";
/*      */           } 
/*      */         } 
/*      */       } else {
/*      */         
/* 1247 */         String str2 = browserComInfo.getBrowsertablename(paramInt4 + "");
/* 1248 */         String str3 = browserComInfo.getBrowsercolumname(paramInt4 + "");
/* 1249 */         String str4 = browserComInfo.getBrowserkeycolumname(paramInt4 + "");
/* 1250 */         for (byte b = 0; b < arrayList.size(); b++) {
/* 1251 */           int i = Util.getIntValue(arrayList.get(b).toString(), 0);
/* 1252 */           String str5 = "select " + str3 + " from " + str2 + " where " + str4 + "=" + i;
/* 1253 */           recordSet.executeSql(str5);
/* 1254 */           recordSet.next();
/* 1255 */           String str6 = recordSet.getString(1);
/* 1256 */           if (paramBoolean) {
/*      */             
/* 1258 */             if (paramInt4 == 258 || paramInt4 == 58 || paramInt4 == 263) {
/* 1259 */               str = str + Util.toScreen(str6, paramInt1);
/*      */             } else {
/* 1261 */               if (!str1.equals("")) {
/* 1262 */                 str = str + "<a href=\"javascript:openFullWindowForXtable('" + str1 + i + "')\">";
/*      */               }
/* 1264 */               str = str + Util.toScreen(str6, paramInt1);
/* 1265 */               if (!str1.equals("")) {
/* 1266 */                 str = str + "</a>&nbsp;";
/*      */               }
/*      */             } 
/*      */           } else {
/* 1270 */             str = str + str6 + ",";
/*      */           } 
/*      */         } 
/*      */       } 
/* 1274 */       if (str.endsWith(",")) {
/* 1275 */         str = str.substring(0, str.length() - 1);
/*      */       }
/* 1277 */     } else if (paramInt3 == 4) {
/*      */ 
/*      */ 
/*      */ 
/*      */       
/* 1282 */       if ("1".equals(paramString3)) {
/* 1283 */         str = "<span style='color:red;'>" + SystemEnv.getHtmlLabelName(163, paramInt1) + "</span>";
/*      */       } else {
/* 1285 */         str = SystemEnv.getHtmlLabelName(161, paramInt1);
/*      */       } 
/* 1287 */     } else if (paramInt3 == 5) {
/*      */       
/* 1289 */       recordSet.executeSql("select selectvalue,selectname from cpt_SelectItem where fieldid = " + paramInt2 + "  order by listorder,id");
/*      */       
/* 1291 */       while (recordSet.next()) {
/* 1292 */         String str1 = Util.null2String(recordSet.getString("selectvalue"));
/* 1293 */         String str2 = Util.toScreen(recordSet
/* 1294 */             .getString("selectname"), paramInt1);
/* 1295 */         if (str1.equals(paramString3)) {
/* 1296 */           str = str + str2;
/*      */         }
/*      */       } 
/* 1299 */     } else if (paramInt3 == 6) {
/* 1300 */       recordSet.executeSql("select id,docsubject,accessorycount from docdetail where id in(" + paramString3 + ") order by id asc");
/*      */       
/* 1302 */       while (recordSet.next()) {
/* 1303 */         str = str + recordSet.getString("docsubject") + ",";
/*      */       }
/* 1305 */       if (str.endsWith(",")) {
/* 1306 */         str = str.substring(0, str.length() - 1);
/*      */       }
/* 1308 */     } else if (paramInt3 == 2 && paramInt4 == 1) {
/* 1309 */       str = Util.toHtml(paramString3);
/*      */     } else {
/* 1311 */       str = paramString3;
/*      */     } 
/* 1313 */     return str;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void updateCptDefinedField(String paramString1, String paramString2) {
/* 1322 */     if ("".equals(Util.null2String(paramString1)) || "".equals(Util.null2String(paramString2))) {
/*      */       return;
/*      */     }
/*      */     try {
/* 1326 */       RecordSet recordSet1 = new RecordSet();
/* 1327 */       RecordSet recordSet2 = new RecordSet();
/* 1328 */       recordSet2.isReturnDecryptData(true);
/* 1329 */       CptFieldComInfo cptFieldComInfo = new CptFieldComInfo();
/* 1330 */       TreeMap<String, JSONObject> treeMap = cptFieldComInfo.getOpenFieldMap();
/* 1331 */       recordSet2.execute("select * from cptcapital where id=" + paramString1);
/* 1332 */       if (recordSet2.next() && 
/* 1333 */         treeMap != null && treeMap.size() > 0) {
/* 1334 */         Iterator<Map.Entry> iterator = treeMap.entrySet().iterator();
/* 1335 */         String str = "";
/*      */         
/* 1337 */         ArrayList<String> arrayList = new ArrayList();
/* 1338 */         while (iterator.hasNext()) {
/* 1339 */           Map.Entry entry = iterator.next();
/* 1340 */           String str1 = (String)entry.getKey();
/* 1341 */           JSONObject jSONObject = (JSONObject)entry.getValue();
/* 1342 */           int i = Util.getIntValue(jSONObject.getString("type"));
/* 1343 */           int j = Util.getIntValue(jSONObject.getString("fieldhtmltype"));
/* 1344 */           String str2 = Util.null2String(jSONObject.getString("fielddbtype"));
/* 1345 */           String str3 = jSONObject.getString("fieldname");
/* 1346 */           String str4 = Util.null2String(recordSet2.getString(str3));
/*      */           
/* 1348 */           if ("oracle".equalsIgnoreCase(recordSet1.getDBType())) {
/* 1349 */             if (str2.toUpperCase().indexOf("INT") >= 0) {
/* 1350 */               if (!Util.null2String(str4).equals("")) {
/* 1351 */                 str = str + str3 + " = " + Util.getIntValue(str4) + ","; continue;
/*      */               } 
/* 1353 */               str = str + str3 + " = NULL,"; continue;
/*      */             } 
/* 1355 */             if (str2.toUpperCase().indexOf("NUMBER") >= 0 || str2.toUpperCase().indexOf("FLOAT") >= 0 || str2.toUpperCase().indexOf("DECIMAL") >= 0) {
/* 1356 */               int k = str2.indexOf(",");
/* 1357 */               int m = 2;
/* 1358 */               if (k > -1) {
/* 1359 */                 m = Util.getIntValue(str2.substring(k + 1, str2.length() - 1).trim(), 2);
/*      */               } else {
/* 1361 */                 m = 2;
/*      */               } 
/* 1363 */               if (!Util.null2String(str4).equals("")) {
/* 1364 */                 str = str + str3 + " = " + Util.getPointValue2(str4, m) + ",";
/*      */                 continue;
/*      */               } 
/* 1367 */               str = str + str3 + " = NULL,";
/*      */               continue;
/*      */             } 
/* 1370 */             String str6 = "";
/* 1371 */             if (j == 3 && (i == 161 || i == 162)) {
/* 1372 */               str6 = Util.null2String(str4);
/* 1373 */               str6 = str6.trim();
/*      */             }
/* 1375 */             else if (j == 2 && i == 2) {
/* 1376 */               str6 = Util.toHtml100(str4);
/* 1377 */               str6 = StringHelper.convertSpecialChar2Html(str6);
/* 1378 */             } else if (j == 1 && i == 1) {
/* 1379 */               str6 = StringHelper.convertSpecialChar2Html(str4);
/* 1380 */               str6 = Util.toHtmlForWorkflow(str6);
/* 1381 */             } else if (j == 2 && i == 1) {
/* 1382 */               str6 = Util.StringReplace(str4, " ", "&nbsp;");
/* 1383 */               str6 = StringHelper.convertSpecialChar2Html(str6);
/* 1384 */               str6 = Util.toHtmlForWorkflowForMode(str6);
/*      */             } else {
/* 1386 */               str6 = Util.StringReplace(Util.toHtml10(str4), " ", "&nbsp;");
/* 1387 */               str6 = StringHelper.convertSpecialChar2Html(str6);
/* 1388 */               str6 = Util.toHtmlForWorkflow(str6);
/*      */             } 
/*      */             
/* 1391 */             str6 = Util.StringReplace(str6, "weaver2017", "+");
/*      */             
/* 1393 */             if (2 == j || FormModeBrowserUtil.isMultiBrowser("" + j, "" + i)) {
/* 1394 */               str = str + str3 + " = ?,";
/* 1395 */               arrayList.add(str6); continue;
/*      */             } 
/* 1397 */             str = str + str3 + " = '" + str6 + "',";
/*      */             
/*      */             continue;
/*      */           } 
/*      */           
/* 1402 */           if (str2.toUpperCase().indexOf("INT") >= 0) {
/* 1403 */             if (!"".equals(str4)) {
/* 1404 */               str = str + str3 + " = " + Util.getIntValue(str4) + ","; continue;
/*      */             } 
/* 1406 */             str = str + str3 + " = NULL,"; continue;
/*      */           } 
/* 1408 */           if (str2.toUpperCase().indexOf("DECIMAL") >= 0 || str2.toUpperCase().indexOf("FLOAT") >= 0) {
/* 1409 */             int k = str2.indexOf(",");
/* 1410 */             int m = 2;
/* 1411 */             if (k > -1) {
/* 1412 */               m = Util.getIntValue(str2.substring(k + 1, str2.length() - 1).trim(), 2);
/*      */             } else {
/* 1414 */               m = 2;
/*      */             } 
/* 1416 */             if (!"".equals(str4)) {
/* 1417 */               str = str + str3 + " = " + Util.getPointValue2(str4, m) + ","; continue;
/*      */             } 
/* 1419 */             str = str + str3 + " = NULL,";
/*      */             continue;
/*      */           } 
/* 1422 */           String str5 = "";
/* 1423 */           if (j == 2 && i == 2) {
/*      */             
/* 1425 */             str5 = StringHelper.convertSpecialChar2Html(str4);
/* 1426 */             str5 = Util.toHtml100(str5);
/* 1427 */           } else if (j == 1 && i == 1) {
/* 1428 */             str5 = StringHelper.convertSpecialChar2Html(str4);
/* 1429 */             str5 = Util.toHtmlForWorkflow(str5);
/* 1430 */           } else if (j == 2 && i == 1) {
/* 1431 */             str5 = Util.StringReplace(str4, " ", "&nbsp;");
/* 1432 */             str5 = StringHelper.convertSpecialChar2Html(str5);
/* 1433 */             str5 = Util.toHtmlForWorkflowForMode(str5);
/* 1434 */           } else if (j == 4 && i == 1) {
/* 1435 */             str5 = Util.StringReplace(str4, " ", "&nbsp;");
/* 1436 */             str5 = Util.toHtmlForWorkflow(str5);
/* 1437 */             if (str5.equals("")) {
/* 1438 */               str5 = "0";
/*      */             }
/*      */           } else {
/* 1441 */             str5 = Util.StringReplace(Util.toHtml10(str4), " ", "&nbsp;");
/* 1442 */             str5 = StringHelper.convertSpecialChar2Html(str5);
/* 1443 */             str5 = Util.toHtmlForWorkflow(str5);
/*      */           } 
/* 1445 */           str5 = Util.StringReplace(str5, "weaver2017", "+");
/*      */           
/* 1447 */           if (2 == j || FormModeBrowserUtil.isMultiBrowser("" + j, "" + i)) {
/* 1448 */             str = str + str3 + " = ?,";
/* 1449 */             arrayList.add(str5); continue;
/*      */           } 
/* 1451 */           str = str + str3 + " = '" + str5 + "',";
/*      */         } 
/*      */ 
/*      */ 
/*      */ 
/*      */         
/* 1457 */         if (!str.equals("")) {
/* 1458 */           str = str.substring(0, str.length() - 1);
/* 1459 */           str = " update cptcapital set  " + str + " where id = " + paramString2;
/*      */           
/* 1461 */           Object[] arrayOfObject = new Object[arrayList.size()];
/* 1462 */           for (byte b = 0; b < arrayList.size(); b++) {
/* 1463 */             arrayOfObject[b] = arrayList.get(b);
/*      */           }
/* 1465 */           recordSet1.executeSql(str, false, arrayOfObject);
/*      */         }
/*      */       
/*      */       } 
/* 1469 */     } catch (Exception exception) {
/* 1470 */       exception.printStackTrace();
/* 1471 */       writeLog(exception.getMessage());
/*      */     } 
/*      */   }
/*      */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/cpt/util/CptFieldManager.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */