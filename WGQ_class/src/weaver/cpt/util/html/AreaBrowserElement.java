/*    */ package weaver.cpt.util.html;
/*    */ 
/*    */ import org.json.JSONObject;
/*    */ import weaver.general.BaseBean;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.User;
/*    */ import weaver.hrm.definedfield.HrmFieldManager;
/*    */ 
/*    */ public class AreaBrowserElement
/*    */   extends BaseBean
/*    */   implements HtmlElement
/*    */ {
/* 13 */   private HrmFieldManager hrmFieldManager = new HrmFieldManager();
/*    */   
/*    */   public String getHtmlElementString(String paramString, JSONObject paramJSONObject, User paramUser) {
/* 16 */     String str = "";
/* 17 */     if (paramJSONObject == null || paramUser == null) {
/* 18 */       return "";
/*    */     }
/*    */     try {
/* 21 */       int i = paramUser.getLanguage();
/* 22 */       String str1 = paramJSONObject.getString("id");
/* 23 */       String str2 = paramJSONObject.getString("fieldname");
/* 24 */       String str3 = paramJSONObject.getString("fieldlabel");
/* 25 */       int j = paramJSONObject.getInt("fieldhtmltype");
/* 26 */       int k = paramJSONObject.getInt("ismand");
/* 27 */       int m = paramJSONObject.getInt("type");
/* 28 */       int n = paramJSONObject.getInt("issystem");
/* 29 */       String str4 = (1 == n) ? str2 : ("field" + str1);
/* 30 */       if (paramJSONObject.getString("fieldkind") != null && "2".equals(paramJSONObject.getString("fieldkind"))) {
/* 31 */         str4 = "customfield" + str1;
/*    */       }
/* 33 */       String str5 = "";
/*    */       try {
/* 35 */         str5 = paramJSONObject.getString("dmlurl");
/* 36 */       } catch (Exception exception) {}
/*    */       
/* 38 */       String str6 = this.hrmFieldManager.getFieldvalue(paramUser, str5, Util.getIntValue(str1), j, m, paramString, 0);
/*    */       
/* 40 */       String str7 = "";
/*    */       
/* 42 */       if (m == 258) {
/* 43 */         str7 = "country";
/* 44 */       } else if (m == 58) {
/* 45 */         str7 = "city";
/* 46 */       } else if (m == 263) {
/* 47 */         str7 = "citytwo";
/*    */       } 
/* 49 */       str = "<div areaType=\"" + str7 + "\" areaName=\"" + str4 + "\" areaValue=\"" + paramString + "\" areaSpanValue=\"" + str6 + "\"  areaMustInput=\"" + ((k == 1) ? "2" : "1") + "\"  areaCallback=\"\" id=\"_areaselect_" + str4 + "\" class=\"_areaselect\" ></div>";
/* 50 */       str = str + "<SCRIPT language=\"javascript\" src=\"/hrm/area/browser/areabrowser_wev8.js\"></script>";
/* 51 */       str = str + "<LINK href=\"/hrm/area/browser/areabrowser.css\" type=text/css rel=STYLESHEET>";
/* 52 */       str = str + "<script language=\"javascript\">";
/* 53 */       str = str + "areromancedivbyid(\"_areaselect_" + str4 + "\")";
/* 54 */       str = str + "</script>";
/* 55 */     } catch (Exception exception) {
/* 56 */       exception.printStackTrace();
/* 57 */       writeLog(exception.getMessage());
/*    */     } 
/*    */ 
/*    */     
/* 61 */     return str;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/cpt/util/html/AreaBrowserElement.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */