/*     */ package weaver.cpt.util.html;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import org.json.JSONObject;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.cpt.util.CptFieldManager;
/*     */ import weaver.formmode.tree.CustomTreeUtil;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.StaticObj;
/*     */ import weaver.general.Util;
/*     */ import weaver.general.browserData.BrowserManager;
/*     */ import weaver.hrm.User;
/*     */ import weaver.interfaces.workflow.browser.Browser;
/*     */ import weaver.interfaces.workflow.browser.BrowserBean;
/*     */ import weaver.proj.util.PrjFieldManager;
/*     */ import weaver.workflow.field.BrowserComInfo;
/*     */ 
/*     */ public class BrowserElement
/*     */   extends BaseBean
/*     */   implements HtmlElement {
/*  21 */   private CptFieldManager cptFieldManager = new CptFieldManager();
/*     */   
/*  23 */   private PrjFieldManager prjFieldManager = new PrjFieldManager();
/*     */ 
/*     */ 
/*     */   
/*     */   public String getHtmlElementString(String paramString, JSONObject paramJSONObject, User paramUser) {
/*  28 */     String str = "";
/*  29 */     if (paramJSONObject == null || paramUser == null) {
/*  30 */       return "";
/*     */     }
/*     */     try {
/*  33 */       int i = paramUser.getLanguage();
/*  34 */       String str1 = paramJSONObject.getString("id");
/*  35 */       String str2 = paramJSONObject.getString("fieldname");
/*  36 */       String str3 = paramJSONObject.getString("fieldlabel");
/*  37 */       String str4 = paramJSONObject.getString("fielddbtype");
/*  38 */       int j = paramJSONObject.getInt("fieldhtmltype");
/*  39 */       int k = paramJSONObject.getInt("ismand");
/*  40 */       int m = paramJSONObject.getInt("type");
/*  41 */       if (m == 258 || m == 58 || m == 263) {
/*  42 */         AreaBrowserElement areaBrowserElement = new AreaBrowserElement();
/*  43 */         return areaBrowserElement.getHtmlElementString(paramString, paramJSONObject, paramUser);
/*     */       } 
/*  45 */       int n = paramJSONObject.getInt("issystem");
/*  46 */       String str5 = (1 == n) ? str2 : ("field" + str1);
/*  47 */       if (paramJSONObject.getString("fieldkind") != null && "2".equals(paramJSONObject.getString("fieldkind"))) {
/*  48 */         str5 = "customfield" + str1;
/*     */       }
/*  50 */       byte b = 2;
/*     */       
/*  52 */       String str6 = "";
/*  53 */       String str7 = "";
/*     */       
/*  55 */       String str8 = "";
/*  56 */       if (k == 1 && "".equals(paramString)) {
/*  57 */         str8 = "<img src='/images/BacoError_wev8.gif' align='absmiddle'>";
/*     */       }
/*     */       
/*  60 */       boolean bool = false;
/*     */       
/*  62 */       String str9 = "";
/*  63 */       char c = 'ὀ';
/*     */ 
/*     */       
/*  66 */       if ("NULL".equals(paramString) && 226 == m) {
/*     */         
/*  68 */         paramString = "";
/*  69 */       } else if ("NULL".equals(paramString) && 227 == m) {
/*  70 */         paramString = "";
/*  71 */       } else if ("NULL".equals(paramString) && 224 == m) {
/*     */         
/*  73 */         paramString = "";
/*  74 */       } else if ("NULL".equals(paramString) && 225 == m) {
/*  75 */         paramString = "";
/*     */       } 
/*     */       
/*  78 */       BrowserComInfo browserComInfo = new BrowserComInfo();
/*  79 */       boolean bool1 = false;
/*  80 */       boolean bool2 = false;
/*  81 */       RecordSet recordSet = new RecordSet();
/*  82 */       String str10 = Util.null2String(browserComInfo.getBrowserurl("" + m));
/*  83 */       String str11 = "?";
/*  84 */       if (str10.indexOf("systeminfo/BrowserMain.jsp?url=/cpt/capital/CapitalBrowser.jsp?") > -1) {
/*  85 */         str11 = "&";
/*     */       }
/*  87 */       if (m == 37) {
/*  88 */         str10 = str10 + str11 + "documentids=#id#";
/*  89 */       } else if (m == 161 || m == 162) {
/*  90 */         str10 = str10 + str11 + "type=" + str4;
/*     */       } else {
/*  92 */         str10 = str10 + str11 + "resourceids=#id#";
/*     */       } 
/*  94 */       String str12 = browserComInfo.getLinkurl("" + m);
/*  95 */       String str13 = "";
/*  96 */       String str14 = "";
/*  97 */       String str15 = "";
/*     */       
/*  99 */       String str16 = "";
/* 100 */       String str17 = "";
/* 101 */       String str18 = "";
/* 102 */       String str19 = "";
/* 103 */       boolean bool3 = false;
/* 104 */       boolean bool4 = false;
/*     */ 
/*     */       
/* 107 */       StringBuilder stringBuilder = new StringBuilder();
/*     */       
/* 109 */       if (m == 2 || m == 19) {
/* 110 */         str13 = paramString;
/* 111 */         if (m == 2) {
/* 112 */           if (k == 1) {
/* 113 */             str8 = " _isrequired='yes' ";
/*     */           }
/* 115 */           stringBuilder.append("<input type='hidden'  name='" + str5 + "' value='" + paramString + "' " + str8 + "  class='wuiDate' _callback='' />");
/* 116 */           return stringBuilder.toString();
/*     */         } 
/* 118 */         if (m == 19)
/*     */         {
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */           
/* 139 */           stringBuilder.append("")
/* 140 */             .append("<button class = Clock type='button' onclick =\"onShowTime(" + str5 + "span," + str5 + ")\"></button>")
/* 141 */             .append("<span id ='" + str5 + "span'>" + (!"".equals(paramString) ? paramString : str8) + "</span>")
/* 142 */             .append("<input class=inputstyle type = hidden name ='" + str5 + "' value ='" + paramString + "' >")
/* 143 */             .append("");
/*     */           
/* 145 */           return stringBuilder.toString();
/*     */         }
/*     */       
/* 148 */       } else if (m == 161) {
/* 149 */         str13 = "";
/*     */         try {
/* 151 */           Browser browser = (Browser)StaticObj.getServiceByFullname(str4, Browser.class);
/* 152 */           BrowserBean browserBean = browser.searchById(paramString);
/* 153 */           String str20 = Util.null2String(browserBean.getName());
/* 154 */           str20 = str20.replaceAll("<", "&lt;");
/* 155 */           str20 = str20.replaceAll(">", "&gt;");
/* 156 */           str13 = str20;
/* 157 */         } catch (Exception exception) {}
/*     */       }
/* 159 */       else if (m == 162) {
/* 160 */         str13 = "";
/* 161 */         String str20 = ",";
/*     */         try {
/* 163 */           Browser browser = (Browser)StaticObj.getServiceByFullname(str4, Browser.class);
/* 164 */           ArrayList<String> arrayList = Util.TokenizerString(paramString, ",");
/* 165 */           for (byte b1 = 0; b1 < arrayList.size(); b1++) {
/* 166 */             String str21 = arrayList.get(b1);
/* 167 */             BrowserBean browserBean = browser.searchById(str21);
/* 168 */             String str22 = Util.null2String(browserBean.getName());
/* 169 */             str22 = str22.replaceAll("<", "&lt;");
/* 170 */             str22 = str22.replaceAll(">", "&gt;");
/*     */             
/* 172 */             str13 = str13 + str22 + str20;
/*     */           } 
/* 174 */         } catch (Exception exception) {}
/*     */         
/* 176 */         if (str13.endsWith(",")) {
/* 177 */           str13 = str13.substring(0, str13.length() - 1);
/*     */         }
/* 179 */       } else if (m == 256 || m == 257) {
/* 180 */         str10 = browserComInfo.getBrowserurl("" + m) + "?type=" + str4 + "_" + m;
/* 181 */         CustomTreeUtil customTreeUtil = new CustomTreeUtil();
/* 182 */         str13 = customTreeUtil.getTreeFieldShowName(paramString, str4);
/*     */       } else {
/* 184 */         str13 = this.cptFieldManager.getFieldvalue(paramUser, Util.getIntValue(str1), j, m, paramString, 0);
/*     */       } 
/*     */       
/* 187 */       bool = "true".equalsIgnoreCase(BrowserManager.browIsSingle("" + m));
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 193 */       if (m == 7 && n == 1) {
/* 194 */         str10 = str10 + "&sqlwhere=where t1.type=2";
/*     */       }
/* 196 */       stringBuilder.append("<span class='browser' id='" + str5 + "_span'></span>")
/* 197 */         .append("<script type='text/javascript'>")
/* 198 */         .append("$('#" + str5 + "_span').e8Browser({")
/* 199 */         .append("name:'" + str5 + "',")
/* 200 */         .append("viewType:'0',")
/* 201 */         .append("browserValue:'" + paramString + "',")
/* 202 */         .append("isMustInput:'" + ((k == 1) ? "2" : "1") + "',")
/* 203 */         .append("browserSpanValue:'" + str13 + "',")
/* 204 */         .append("hasInput:'true',")
/* 205 */         .append("linkUrl:'" + str12 + "',")
/* 206 */         .append("completeUrl:'/data.jsp?type=" + m + "',")
/* 207 */         .append("browserUrl:'" + str10 + "',")
/* 208 */         .append("width:'80%',")
/* 209 */         .append("hasAdd:false,")
/* 210 */         .append("isSingle:" + bool)
/* 211 */         .append("});")
/* 212 */         .append("</script>")
/* 213 */         .append("");
/*     */ 
/*     */       
/* 216 */       str = str + stringBuilder.toString();
/*     */     }
/* 218 */     catch (Exception exception) {
/* 219 */       exception.printStackTrace();
/* 220 */       writeLog(exception.getMessage());
/*     */     } 
/*     */ 
/*     */     
/* 224 */     return str;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/cpt/util/html/BrowserElement.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */