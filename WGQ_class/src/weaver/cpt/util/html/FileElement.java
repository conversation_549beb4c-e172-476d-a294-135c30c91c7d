/*     */ package weaver.cpt.util.html;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.Hashtable;
/*     */ import org.json.JSONObject;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.docs.category.SecCategoryComInfo;
/*     */ import weaver.docs.docs.DocImageManager;
/*     */ import weaver.general.AttachFileUtil;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.workflow.request.RequestManager;
/*     */ 
/*     */ 
/*     */ 
/*     */ public class FileElement
/*     */   extends BaseBean
/*     */   implements HtmlElement
/*     */ {
/*     */   public String getHtmlElementString(String paramString, JSONObject paramJSONObject, User paramUser) {
/*  23 */     String str = "";
/*  24 */     if (paramJSONObject == null || paramUser == null) {
/*  25 */       return "";
/*     */     }
/*     */     try {
/*  28 */       int i = paramUser.getLanguage();
/*  29 */       String str1 = paramJSONObject.getString("id");
/*  30 */       String str2 = paramJSONObject.getString("fieldname");
/*  31 */       String str3 = paramJSONObject.getString("fieldlabel");
/*  32 */       String str4 = paramJSONObject.getString("fielddbtype");
/*  33 */       int j = paramJSONObject.getInt("ismand");
/*  34 */       int k = paramJSONObject.getInt("type");
/*     */       
/*  36 */       byte b1 = 2;
/*     */       
/*  38 */       String str5 = "";
/*  39 */       String str6 = "";
/*     */       
/*  41 */       String str7 = "";
/*  42 */       if (j == 1 && "".equals(paramString)) {
/*  43 */         str7 = "<img src='/images/BacoError_wev8.gif' align='absmiddle'>";
/*     */       }
/*     */ 
/*     */ 
/*     */       
/*  48 */       int m = Util.getIntValue(paramJSONObject.getString("imgwidth"), 0);
/*  49 */       int n = Util.getIntValue(paramJSONObject.getString("imgheight"), 0);
/*  50 */       int i1 = Util.getIntValue(paramJSONObject.getString("textheight"), 0);
/*  51 */       RecordSet recordSet = new RecordSet();
/*  52 */       String str8 = "";
/*  53 */       DocImageManager docImageManager = new DocImageManager();
/*  54 */       SecCategoryComInfo secCategoryComInfo = new SecCategoryComInfo();
/*  55 */       AttachFileUtil attachFileUtil = new AttachFileUtil();
/*  56 */       String str9 = "";
/*  57 */       String str10 = "1";
/*     */ 
/*     */       
/*  60 */       String str11 = "18,73,88";
/*  61 */       boolean bool1 = false;
/*  62 */       byte b2 = 5;
/*     */       
/*  64 */       if (b2 <= 0) {
/*  65 */         b2 = 5;
/*     */       }
/*  67 */       boolean bool2 = false;
/*  68 */       String str12 = "";
/*  69 */       boolean bool3 = true;
/*  70 */       ArrayList<String> arrayList = new ArrayList();
/*  71 */       String str13 = "0";
/*  72 */       String str14 = "0";
/*  73 */       String str15 = "";
/*     */ 
/*     */       
/*  76 */       str = str + "<table cols=\"3\" id=\"field" + str1 + "_tab\">\n";
/*  77 */       str = str + "<tbody>\n";
/*  78 */       str = str + "<col width=\"50%\">\n";
/*  79 */       str = str + "<col width=\"25%\">\n";
/*  80 */       str = str + "<col width=\"25%\">\n";
/*  81 */       if ("-2".equals(paramString)) {
/*  82 */         str = str + "<tr>\n";
/*  83 */         str = str + "<td colSpan=\"3\"><font color=\"red\">\n";
/*  84 */         str = str + "" + SystemEnv.getHtmlLabelName(21710, i) + "</font>\n";
/*  85 */         str = str + "</td>\n";
/*  86 */         str = str + "</tr>\n";
/*     */       }
/*  88 */       else if (!paramString.equals("")) {
/*  89 */         str8 = "select id,docsubject,accessorycount,SecCategory from docdetail where id in(" + paramString + ") order by id asc";
/*  90 */         recordSet.executeSql(str8);
/*  91 */         int i2 = recordSet.getCounts();
/*  92 */         byte b = -1;
/*  93 */         int i3 = i1;
/*  94 */         boolean bool = false;
/*  95 */         while (recordSet.next()) {
/*  96 */           bool = false;
/*  97 */           b++;
/*  98 */           String str21 = Util.null2String(recordSet.getString(1));
/*  99 */           String str22 = Util.toScreen(recordSet.getString(2), i);
/* 100 */           int i4 = recordSet.getInt(3);
/* 101 */           String str23 = Util.null2String(recordSet.getString(4));
/* 102 */           docImageManager.resetParameter();
/* 103 */           docImageManager.setDocid(Integer.parseInt(str21));
/* 104 */           docImageManager.selectDocImageInfo();
/*     */           
/* 106 */           String str24 = "";
/* 107 */           long l = 0L;
/* 108 */           String str25 = "";
/* 109 */           String str26 = "";
/* 110 */           int i5 = 0;
/*     */           
/* 112 */           if (docImageManager.next()) {
/*     */             
/* 114 */             str24 = docImageManager.getImagefileid();
/* 115 */             l = docImageManager.getImageFileSize(Util.getIntValue(str24));
/* 116 */             str25 = docImageManager.getImagefilename();
/* 117 */             str26 = str25.substring(str25.lastIndexOf(".") + 1).toLowerCase();
/* 118 */             i5 = docImageManager.getVersionId();
/*     */           } 
/* 120 */           if (i4 > 1) {
/* 121 */             str26 = "htm";
/*     */           }
/* 123 */           boolean bool5 = secCategoryComInfo.getNoDownload(str23).equals("1") ? true : false;
/* 124 */           if (k == 2) {
/* 125 */             if (b == 0) {
/* 126 */               bool = true;
/* 127 */               if (!"1".equals(str9) && !bool5 && i2 > 1 && b == 0) {
/* 128 */                 str = str + "<button type=button  class=\"btnFlowd\" accessKey=\"1\" onclick=\"addDocReadTag('" + str21 + "');downloadsBatch('" + paramString + "','" + str13 + "')\">\n";
/* 129 */                 str = str + "&nbsp;&nbsp;&nbsp;" + SystemEnv.getHtmlLabelName(10000008, paramUser.getLanguage()) + "\n";
/* 130 */                 str = str + "</button>\n";
/*     */               } 
/*     */               
/* 133 */               str = str + "<tr>\n";
/* 134 */               str = str + "<td colSpan=3>\n";
/* 135 */               str = str + "<table cellspacing=\"0\" cellpadding=\"0\">\n";
/* 136 */               str = str + "<tr>\n";
/*     */             } 
/* 138 */             if (i3 > 0 && b >= i3) {
/* 139 */               i3 += i1;
/* 140 */               bool = true;
/* 141 */               str = str + "</tr>\n";
/* 142 */               str = str + "<tr>\n";
/*     */             } 
/* 144 */             str = str + "<input type=\"hidden\" id=\"field" + str1 + "_del_" + b + "\" name=\"field" + str1 + "_del_" + b + "\" value=\"0\" >\n";
/* 145 */             str = str + "<input type=\"hidden\" id=\"field" + str1 + "_id_" + b + "\" name=\"field" + str1 + "_id_" + b + "\" value=\"" + str21 + "\" >\n";
/* 146 */             str = str + "<td ";
/* 147 */             if (!bool) {
/* 148 */               str = str + "style=\"padding-left:15\"";
/*     */             }
/* 150 */             str = str + ">\n";
/* 151 */             str = str + "<table>\n";
/* 152 */             str = str + "<tr>\n";
/* 153 */             str = str + "<td colspan=2 align=\"center\"><img src=\"/weaver/weaver.file.FileDownload?fileid=" + str24 + "&requestid=" + str13 + "\" style=\"cursor:hand\" alt=\"" + str25 + "\"";
/* 154 */             if (m > 0) {
/* 155 */               str = str + " width=" + m;
/*     */             }
/* 157 */             if (n > 0) {
/* 158 */               str = str + " height=" + n;
/*     */             }
/* 160 */             str = str + " onclick=\"addDocReadTag('" + str21 + "');openAccessory('" + str24 + "')\">\n";
/* 161 */             str = str + "</td>\n";
/* 162 */             str = str + "</tr>\n";
/* 163 */             str = str + "<tr>\n";
/* 164 */             if (str10.equals("1")) {
/* 165 */               str = str + "<td align=\"center\"><nobr>\n";
/* 166 */               str = str + "<a href=\"#\" style=\"text-decoration:underline\" onmouseover=\"this.style.color='blue'\" onclick='onChangeSharetype(\"span" + str1 + "_id_" + b + "\",\"field" + str1 + "_del_" + b + "\",\"" + j + "\",oUpload" + str1 + ");return false;'>[<span style=\"cursor:hand;color:black;\">" + SystemEnv.getHtmlLabelName(91, i) + "</span>]</a>\n";
/* 167 */               str = str + "<span id=\"span" + str1 + "_id_" + b + "\" name=\"span" + str1 + "_id_" + b + "\" style=\"visibility:hidden\"><b><font COLOR=\"#FF0033\">√</font></b><span></td>\n";
/*     */             } 
/* 169 */             if (!bool5) {
/* 170 */               str = str + "<td align=\"center\"><nobr>\n";
/* 171 */               str = str + "<a href=\"#\" style=\"text-decoration:underline\" onmouseover=\"this.style.color='blue'\" onclick=\"addDocReadTag('" + str21 + "');downloads('" + str24 + "');return false;\">[<span style=\"cursor:hand;color:black;\">" + SystemEnv.getHtmlLabelName(258, i) + "</span>]</a>\n";
/* 172 */               str = str + "</td>\n";
/*     */             } 
/* 174 */             str = str + "</tr>\n";
/* 175 */             str = str + "</table>\n";
/* 176 */             str = str + "</td>\n"; continue;
/*     */           } 
/* 178 */           String str27 = AttachFileUtil.getImgStrbyExtendName(str26, 20);
/*     */           
/* 180 */           str = str + "<tr>\n";
/* 181 */           str = str + "<td>\n";
/* 182 */           str = str + "<input type=\"hidden\" id=\"field" + str1 + "_del_" + b + "\" name=\"field" + str1 + "_del_" + b + "\" value=\"0\" >\n";
/* 183 */           str = str + str27 + "\n";
/*     */           
/* 185 */           if (i4 == 1 && (Util.isExt(str26) || str26.equalsIgnoreCase("pdf"))) {
/* 186 */             str = str + "<a style=\"cursor:hand\" onclick=\"addDocReadTag('" + str21 + "');openDocExt('" + str21 + "','" + i5 + "','" + str24 + "',1)\">" + str25 + "</a>&nbsp;\n";
/*     */           } else {
/* 188 */             str = str + "<a style=\"cursor:hand\" onclick=\"addDocReadTag('" + str21 + "');openAccessory('" + str24 + "')\">" + str25 + "</a>&nbsp;\n";
/*     */           } 
/* 190 */           str = str + "<input type=\"hidden\" id=\"field" + str1 + "_id_" + b + "\" name=\"field" + str1 + "_id_" + b + "\" value=\"" + str21 + "\">\n";
/* 191 */           str = str + "</td>\n";
/* 192 */           if (str10.equals("1")) {
/* 193 */             str = str + "<td>\n";
/* 194 */             str = str + "<button type=button  class=\"btnFlow\" accessKey=\"1\" onclick=\"onChangeSharetype('span" + str1 + "_id_" + b + "','field" + str1 + "_del_" + b + "','" + j + "',oUpload" + str1 + ")\"><u>" + b + "</u>-" + SystemEnv.getHtmlLabelName(91, i) + "</button>\n";
/* 195 */             str = str + "<span id=\"span" + str1 + "_id_" + b + "\" name=\"span" + str1 + "_id_" + b + "\" style=\"visibility:hidden\">\n";
/* 196 */             str = str + "<b><font color=\"#FF0033\">√</font></b>";
/* 197 */             str = str + "</span>\n";
/* 198 */             str = str + "</td>\n";
/*     */           } 
/* 200 */           if (i4 == 1 && ((!str26.equalsIgnoreCase("xls") && !str26.equalsIgnoreCase("doc") && !str26.equalsIgnoreCase("ppt") && !str26.equalsIgnoreCase("xlsx") && !str26.equalsIgnoreCase("docx") && !str26.equalsIgnoreCase("pptx") && !str26.equalsIgnoreCase("pdf") && !str26.equalsIgnoreCase("pdfx")) || !bool5)) {
/* 201 */             str = str + "<td>\n";
/* 202 */             str = str + "<span id=\"selectDownload\">\n";
/* 203 */             if ((!str26.equalsIgnoreCase("xls") && !str26.equalsIgnoreCase("doc") && !str26.equalsIgnoreCase("ppt") && !str26.equalsIgnoreCase("xlsx") && !str26.equalsIgnoreCase("docx") && !str26.equalsIgnoreCase("pptx") && !str26.equalsIgnoreCase("pdf") && !str26.equalsIgnoreCase("pdfx")) || !bool5) {
/* 204 */               str = str + "<button type=button  class=\"btnFlowd\" accessKey=\"1\" onclick=\"addDocReadTag('" + str21 + "');downloads('" + str24 + "')\">\n";
/* 205 */               str = str + "<u>" + b + "</u>-" + SystemEnv.getHtmlLabelName(258, i) + "\t\t(" + (l / 1000L) + "K)\n";
/* 206 */               str = str + "</button>\n";
/* 207 */               if (!"1".equals(str9) && i2 > 1 && !bool5 && b == 0) {
/* 208 */                 str = str + "<button type=button  class=\"btnFlowd\" accessKey=\"1\" onclick=\"addDocReadTag('" + str21 + "');downloadsBatch('" + paramString + "','" + str13 + "')\">\n";
/* 209 */                 str = str + "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;" + SystemEnv.getHtmlLabelName(32405, paramUser.getLanguage()) + "\n";
/* 210 */                 str = str + "</button>\n";
/*     */               } 
/*     */             } 
/* 213 */             str = str + "</span>\n";
/* 214 */             str = str + "</td>\n";
/*     */           } 
/* 216 */           str = str + "</tr>\n";
/*     */         } 
/*     */         
/* 219 */         if (k == 2 && b > -1) {
/* 220 */           str = str + "</tr>\n</table>\n</td>\n</tr>\n";
/*     */         }
/* 222 */         str = str + "<input type=\"hidden\" id=\"field" + str1 + "_idnum\" name=\"field" + str1 + "_idnum\" value=\"" + (b + 1) + "\">\n";
/* 223 */         str = str + "<input type=\"hidden\" id=\"field" + str1 + "_idnum_1\" name=\"field" + str1 + "_idnum_1\" value=\"" + (b + 1) + "\">\n";
/*     */       } 
/*     */ 
/*     */       
/* 227 */       str = str + "<tr>\n";
/* 228 */       str = str + "<td colspan=3>\n";
/* 229 */       String str16 = "";
/* 230 */       String str17 = "";
/* 231 */       String str18 = "";
/* 232 */       if (str11 != null && !str11.equals("")) {
/* 233 */         str16 = str11.substring(0, str11.indexOf(','));
/* 234 */         str17 = str11.substring(str11.indexOf(',') + 1, str11.lastIndexOf(','));
/* 235 */         str18 = str11.substring(str11.lastIndexOf(',') + 1, str11.length());
/*     */       } 
/* 237 */       String str19 = "*.*";
/* 238 */       String str20 = "All Files";
/* 239 */       if (k == 2) {
/* 240 */         str19 = (new BaseBean()).getPropValue("PicFileTypes", "PicFileTypes");
/* 241 */         str20 = "Images Files";
/*     */       } 
/* 243 */       boolean bool4 = true;
/* 244 */       if (!bool2) {
/* 245 */         if ("".equals(str16) && "".equals(str17) && "".equals(str18)) {
/* 246 */           bool4 = false;
/* 247 */           str = str + "<font color=\"red\">" + SystemEnv.getHtmlLabelName(83157, i) + "!</font>\n";
/*     */         } 
/* 249 */       } else if (!bool3) {
/* 250 */         bool4 = false;
/* 251 */         str = str + "<font color=\"red\">" + SystemEnv.getHtmlLabelName(83157, i) + "!</font>\n";
/*     */       } 
/* 253 */       if (bool4) {
/* 254 */         arrayList.add("" + str1);
/* 255 */         str15 = str15 + "var oUpload" + str1 + ";\n";
/* 256 */         str15 = str15 + "function fileupload" + str1 + "() {\n";
/* 257 */         str15 = str15 + " var settings = {\n";
/* 258 */         str15 = str15 + "flash_url:\"/js/swfupload/swfupload.swf\",\n";
/* 259 */         str15 = str15 + "upload_url:\"/docs/docupload/MultiDocUploadByWorkflow.jsp\",\n";
/* 260 */         str15 = str15 + "post_params:{\n";
/* 261 */         str15 = str15 + "\t\"mainId\":\"" + str16 + "\",\n";
/* 262 */         str15 = str15 + "\t\"subId\":\"" + str17 + "\",\n";
/* 263 */         str15 = str15 + "\t\"secId\":\"" + str18 + "\",\n";
/* 264 */         str15 = str15 + "\t\"userid\":\"" + paramUser.getUID() + "\",\n";
/* 265 */         str15 = str15 + "\t\"logintype\":\"" + paramUser.getLogintype() + "\",\n";
/* 266 */         str15 = str15 + "\t\"workflowid\":\"" + str14 + "\"\n";
/* 267 */         str15 = str15 + "},\n";
/* 268 */         str15 = str15 + "file_size_limit :\"" + b2 + " MB\",\n";
/* 269 */         str15 = str15 + "file_types : \"" + str19 + "\",\n";
/* 270 */         str15 = str15 + "file_types_description : \"" + str20 + "\",\n";
/* 271 */         str15 = str15 + "file_upload_limit : 100,\n";
/* 272 */         str15 = str15 + "file_queue_limit : 0,\n";
/* 273 */         str15 = str15 + "custom_settings : { \n";
/* 274 */         str15 = str15 + "\tprogressTarget : \"fsUploadProgress" + str1 + "\",\n";
/* 275 */         str15 = str15 + "\tcancelButtonId : \"btnCancel" + str1 + "\",\n";
/* 276 */         str15 = str15 + "\tuploadspan : \"field" + str1 + "span\",\n";
/* 277 */         str15 = str15 + "\tuploadfiedid : \"field" + str1 + "\"\n";
/* 278 */         str15 = str15 + "},\n";
/* 279 */         str15 = str15 + "debug: false,\n";
/* 280 */         str15 = str15 + "button_image_url : \"/js/swfupload/add_wev8.png\",\n";
/* 281 */         str15 = str15 + "button_placeholder_id : \"spanButtonPlaceHolder" + str1 + "\",\n";
/* 282 */         str15 = str15 + "button_width: 100,\n";
/* 283 */         str15 = str15 + "button_height: 18,\n";
/* 284 */         str15 = str15 + "button_text : '<span class=\"button\">" + SystemEnv.getHtmlLabelName(21406, i) + "</span>',\n";
/* 285 */         str15 = str15 + "button_text_style : '.button { font-family: Helvetica, Arial, sans-serif; font-size: 12pt; } .buttonSmall { font-size: 10pt; }',\n";
/* 286 */         str15 = str15 + "button_text_top_padding: 0,\n";
/* 287 */         str15 = str15 + "button_text_left_padding: 18,\n";
/* 288 */         str15 = str15 + "button_window_mode: SWFUpload.WINDOW_MODE.TRANSPARENT,\n";
/* 289 */         str15 = str15 + "button_cursor: SWFUpload.CURSOR.HAND,\n";
/* 290 */         str15 = str15 + "file_queued_handler : fileQueued,\n";
/* 291 */         str15 = str15 + "file_queue_error_handler : fileQueueError,\n";
/* 292 */         str15 = str15 + "file_dialog_complete_handler : fileDialogComplete_1,\n";
/* 293 */         str15 = str15 + "upload_start_handler : uploadStart,\n";
/* 294 */         str15 = str15 + "upload_progress_handler : uploadProgress,\n";
/* 295 */         str15 = str15 + "upload_error_handler : uploadError,\n";
/* 296 */         str15 = str15 + "upload_success_handler : uploadSuccess_1,\n";
/* 297 */         str15 = str15 + "upload_complete_handler : uploadComplete_1,\n";
/* 298 */         str15 = str15 + "queue_complete_handler : queueComplete\n";
/* 299 */         str15 = str15 + "};\n";
/* 300 */         str15 = str15 + "try {\n";
/* 301 */         str15 = str15 + "\toUpload" + str1 + "=new SWFUpload(settings);\n";
/* 302 */         str15 = str15 + "} catch(e) {\n";
/* 303 */         str15 = str15 + "\talert(e)\n";
/* 304 */         str15 = str15 + "}\n";
/* 305 */         str15 = str15 + "}\n";
/*     */ 
/*     */ 
/*     */         
/* 309 */         str15 = str15 + "\tif (window.addEventListener){\n";
/* 310 */         str15 = str15 + "\t    window.addEventListener(\"load\", fileupload" + str1 + ", false);\n";
/* 311 */         str15 = str15 + "\t}else if (window.attachEvent){\n";
/* 312 */         str15 = str15 + "\t    window.attachEvent(\"onload\", fileupload" + str1 + ");\n";
/* 313 */         str15 = str15 + "\t}else{\n";
/* 314 */         str15 = str15 + "\t    window.onload=fileupload" + str1 + ";\n";
/* 315 */         str15 = str15 + "\t}\n";
/*     */         
/* 317 */         str = str + "<TABLE class=\"ViewForm\">\n";
/* 318 */         str = str + "<tr>\n";
/* 319 */         str = str + "<td colspan=2>\n";
/* 320 */         str = str + "<div>\n";
/* 321 */         str = str + "<span>\n";
/* 322 */         str = str + "<span id=\"spanButtonPlaceHolder" + str1 + "\"></span>\n";
/* 323 */         str = str + "</span>\n";
/* 324 */         str = str + "&nbsp;&nbsp;<span style=\"color:#262626;cursor:hand;TEXT-DECORATION:none\" disabled onclick=\"oUpload" + str1 + ".cancelQueue();showmustinput(oUpload" + str1 + ");\" id=\"btnCancel" + str1 + "\">\n";
/* 325 */         str = str + "<span><img src=\"/js/swfupload/delete_wev8.gif\" border=0></span>\n";
/* 326 */         str = str + "<span style=\"height:19px\"><font style=\"margin:0 0 0 -1\">" + SystemEnv.getHtmlLabelName(21407, i) + "</font></span>\n";
/* 327 */         str = str + "</span><span id=\"uploadspan\">(" + SystemEnv.getHtmlLabelName(18976, i) + b2 + SystemEnv.getHtmlLabelName(18977, i) + ")</span>\n";
/* 328 */         str = str + "<span id=\"field" + str1 + "span\">\n";
/* 329 */         if (j == 1 && paramString.equals("")) {
/* 330 */           str = str + "<img src='/images/BacoError_wev8.gif' align=absMiddle>\n";
/*     */         }
/* 332 */         str = str + "</span>\n";
/* 333 */         str = str + "</div>\n";
/* 334 */         str = str + "<input  class=InputStyle  type=hidden size=60 name=\"field" + str1 + "\" id=\"field" + str1 + "\" temptitle=\"" + Util.toScreen(str3, i) + "\"  viewtype=" + j + " value=\"" + paramString + "\">\n";
/* 335 */         str = str + "</td>\n";
/* 336 */         str = str + "</tr>\n";
/* 337 */         str = str + "<tr>\n";
/* 338 */         str = str + "<td colspan=2>\n";
/* 339 */         str = str + "<div class=\"fieldset flash\" id=\"fsUploadProgress" + str1 + "\">\n";
/* 340 */         str = str + "</div>\n";
/* 341 */         str = str + "<div id=\"divStatus" + str1 + "\"></div>\n";
/* 342 */         str = str + "</td>\n";
/* 343 */         str = str + "</tr>\n";
/* 344 */         str = str + "</TABLE>\n";
/*     */       } 
/* 346 */       str = str + "<input type=\"hidden\" id=\"mainId\" name=\"mainId\" value=\"" + str16 + "\">\n";
/* 347 */       str = str + "<input type=\"hidden\" id=\"subId\" name=\"subId\" value=\"" + str17 + "\">\n";
/* 348 */       str = str + "<input type=\"hidden\" id=\"secId\" name=\"secId\" value=\"" + str18 + "\">\n";
/* 349 */       str = str + "</td>\n";
/* 350 */       str = str + "</tr>\n";
/* 351 */       str = str + "</TABLE>\n";
/*     */       
/* 353 */       str = str + "<script type=\"text/javascript\">" + str15 + "</script>";
/*     */     }
/* 355 */     catch (Exception exception) {
/* 356 */       exception.printStackTrace();
/* 357 */       writeLog(exception.getMessage());
/*     */     } 
/*     */ 
/*     */     
/* 361 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public Hashtable getHtmlElementString(int paramInt1, String paramString1, int paramInt2, String paramString2, int paramInt3, int paramInt4, int paramInt5, String paramString3, int paramInt6, int paramInt7, int paramInt8, int paramInt9, User paramUser, Hashtable<String, ArrayList<String>> paramHashtable) {
/* 369 */     Hashtable<Object, Object> hashtable = new Hashtable<>();
/* 370 */     String str1 = "";
/* 371 */     String str2 = "";
/* 372 */     ArrayList<String> arrayList = null;
/* 373 */     arrayList = (ArrayList)paramHashtable.get("uploadfieldids");
/* 374 */     ArrayList arrayList1 = (ArrayList)paramHashtable.get("changefieldsadd");
/* 375 */     if (arrayList == null)
/* 376 */       arrayList = new ArrayList(); 
/*     */     try {
/* 378 */       int i = paramUser.getLanguage();
/* 379 */       String str3 = "";
/* 380 */       RecordSet recordSet = new RecordSet();
/* 381 */       DocImageManager docImageManager = new DocImageManager();
/* 382 */       SecCategoryComInfo secCategoryComInfo = new SecCategoryComInfo();
/* 383 */       AttachFileUtil attachFileUtil = new AttachFileUtil();
/* 384 */       RequestManager requestManager = new RequestManager();
/* 385 */       int j = Util.getIntValue((String)paramHashtable.get("workflowid"));
/* 386 */       String str4 = "";
/* 387 */       String str5 = "select forbidAttDownload from workflow_base where id=" + j;
/* 388 */       recordSet.execute(str5);
/* 389 */       if (recordSet.next()) {
/* 390 */         str4 = recordSet.getString("forbidAttDownload");
/*     */       }
/* 392 */       int k = Util.getIntValue((String)paramHashtable.get("requestid"));
/* 393 */       String str6 = Util.null2String((String)paramHashtable.get("nodetype"));
/* 394 */       String str7 = Util.null2String((String)paramHashtable.get("canDelAcc"));
/* 395 */       int m = Util.getIntValue((String)paramHashtable.get("fieldimgwidth" + paramInt1), 0);
/* 396 */       int n = Util.getIntValue((String)paramHashtable.get("fieldimgheight" + paramInt1), 0);
/* 397 */       int i1 = Util.getIntValue((String)paramHashtable.get("fieldimgnum" + paramInt1), 0);
/* 398 */       int i2 = Util.getIntValue((String)paramHashtable.get("isprint"), 0);
/*     */       
/* 400 */       String str8 = Util.null2String((String)paramHashtable.get("docCategory"));
/* 401 */       int i3 = Util.getIntValue((String)paramHashtable.get("maxUploadImageSize"), -1);
/* 402 */       int i4 = 5;
/* 403 */       if (i3 > 0) {
/* 404 */         i4 = i3;
/*     */       } else {
/* 406 */         if (str8.equals("")) {
/* 407 */           str3 = "select docCategory from workflow_base where id=" + j;
/* 408 */           recordSet.execute(str3);
/* 409 */           if (recordSet.next()) {
/* 410 */             str8 = recordSet.getString("docCategory");
/*     */           }
/*     */         } 
/* 413 */         int i6 = Util.getIntValue(str8.substring(str8.lastIndexOf(",") + 1), -1);
/* 414 */         i4 = Util.getIntValue(secCategoryComInfo.getMaxUploadFileSize("" + i6), 5);
/*     */       } 
/* 416 */       if (i4 <= 0) {
/* 417 */         i4 = 5;
/*     */       }
/* 419 */       int i5 = 0;
/* 420 */       String str9 = "";
/* 421 */       String str10 = requestManager.getUpLoadTypeForSelect(j);
/* 422 */       if (!str10.equals("")) {
/* 423 */         str9 = str10.substring(0, str10.indexOf(","));
/* 424 */         i5 = Integer.valueOf(str10.substring(str10.indexOf(",") + 1)).intValue();
/*     */       } 
/* 426 */       boolean bool = requestManager.hasUsedType(j);
/* 427 */       if (str9.equals("") || str9.equals("0")) {
/* 428 */         bool = false;
/*     */       }
/* 430 */       String str11 = "";
/* 431 */       if (paramInt6 == 0 && paramInt7 == 1 && paramInt8 == 1 && paramInt9 == 1 && "".equals(paramString3)) {
/* 432 */         str11 = "<img src='/images/BacoError_wev8.gif' align='absmiddle'>";
/*     */       }
/* 434 */       if (paramInt4 == 0) {
/* 435 */         if (paramInt7 == 1) {
/*     */           
/* 437 */           if (paramInt8 == 1 && paramInt6 == 0) {
/* 438 */             str1 = str1 + "<table cols=\"3\" id=\"field" + paramInt1 + "_tab\">\n";
/* 439 */             str1 = str1 + "<tbody>\n";
/* 440 */             str1 = str1 + "<col width=\"50%\">\n";
/* 441 */             str1 = str1 + "<col width=\"25%\">\n";
/* 442 */             str1 = str1 + "<col width=\"25%\">\n";
/* 443 */             if ("-2".equals(paramString3)) {
/* 444 */               str1 = str1 + "<tr>\n";
/* 445 */               str1 = str1 + "<td colSpan=\"3\"><font color=\"red\">\n";
/* 446 */               str1 = str1 + "" + SystemEnv.getHtmlLabelName(21710, i) + "</font>\n";
/* 447 */               str1 = str1 + "</td>\n";
/* 448 */               str1 = str1 + "</tr>\n";
/*     */             }
/* 450 */             else if (!paramString3.equals("")) {
/* 451 */               str3 = "select id,docsubject,accessorycount,SecCategory from docdetail where id in(" + paramString3 + ") order by id asc";
/* 452 */               recordSet.executeSql(str3);
/* 453 */               int i6 = recordSet.getCounts();
/* 454 */               byte b = -1;
/* 455 */               int i7 = i1;
/* 456 */               boolean bool2 = false;
/* 457 */               while (recordSet.next()) {
/* 458 */                 bool2 = false;
/* 459 */                 b++;
/* 460 */                 String str17 = Util.null2String(recordSet.getString(1));
/* 461 */                 String str18 = Util.toScreen(recordSet.getString(2), i);
/* 462 */                 int i8 = recordSet.getInt(3);
/* 463 */                 String str19 = Util.null2String(recordSet.getString(4));
/* 464 */                 docImageManager.resetParameter();
/* 465 */                 docImageManager.setDocid(Integer.parseInt(str17));
/* 466 */                 docImageManager.selectDocImageInfo();
/*     */                 
/* 468 */                 String str20 = "";
/* 469 */                 long l = 0L;
/* 470 */                 String str21 = "";
/* 471 */                 String str22 = "";
/* 472 */                 int i9 = 0;
/*     */                 
/* 474 */                 if (docImageManager.next()) {
/*     */                   
/* 476 */                   str20 = docImageManager.getImagefileid();
/* 477 */                   l = docImageManager.getImageFileSize(Util.getIntValue(str20));
/* 478 */                   str21 = docImageManager.getImagefilename();
/* 479 */                   str22 = str21.substring(str21.lastIndexOf(".") + 1).toLowerCase();
/* 480 */                   i9 = docImageManager.getVersionId();
/*     */                 } 
/* 482 */                 if (i8 > 1) {
/* 483 */                   str22 = "htm";
/*     */                 }
/* 485 */                 boolean bool3 = secCategoryComInfo.getNoDownload(str19).equals("1") ? true : false;
/* 486 */                 if (paramInt2 == 2) {
/* 487 */                   if (b == 0) {
/* 488 */                     bool2 = true;
/* 489 */                     if (!"1".equals(str4) && !bool3 && i6 > 1 && b == 0) {
/* 490 */                       str1 = str1 + "<button type=button  class=\"btnFlowd\" accessKey=\"1\" onclick=\"addDocReadTag('" + str17 + "');downloadsBatch('" + paramString3 + "','" + k + "')\">\n";
/* 491 */                       str1 = str1 + "&nbsp;&nbsp;&nbsp;" + SystemEnv.getHtmlLabelName(10000008, paramUser.getLanguage()) + "\n";
/* 492 */                       str1 = str1 + "</button>\n";
/*     */                     } 
/*     */                     
/* 495 */                     str1 = str1 + "<tr>\n";
/* 496 */                     str1 = str1 + "<td colSpan=3>\n";
/* 497 */                     str1 = str1 + "<table cellspacing=\"0\" cellpadding=\"0\">\n";
/* 498 */                     str1 = str1 + "<tr>\n";
/*     */                   } 
/* 500 */                   if (i7 > 0 && b >= i7) {
/* 501 */                     i7 += i1;
/* 502 */                     bool2 = true;
/* 503 */                     str1 = str1 + "</tr>\n";
/* 504 */                     str1 = str1 + "<tr>\n";
/*     */                   } 
/* 506 */                   str1 = str1 + "<input type=\"hidden\" id=\"field" + paramInt1 + "_del_" + b + "\" name=\"field" + paramInt1 + "_del_" + b + "\" value=\"0\" >\n";
/* 507 */                   str1 = str1 + "<input type=\"hidden\" id=\"field" + paramInt1 + "_id_" + b + "\" name=\"field" + paramInt1 + "_id_" + b + "\" value=\"" + str17 + "\" >\n";
/* 508 */                   str1 = str1 + "<td ";
/* 509 */                   if (!bool2) {
/* 510 */                     str1 = str1 + "style=\"padding-left:15\"";
/*     */                   }
/* 512 */                   str1 = str1 + ">\n";
/* 513 */                   str1 = str1 + "<table>\n";
/* 514 */                   str1 = str1 + "<tr>\n";
/* 515 */                   str1 = str1 + "<td colspan=2 align=\"center\"><img src=\"/weaver/weaver.file.FileDownload?fileid=" + str20 + "&requestid=" + k + "\" style=\"cursor:hand\" alt=\"" + str21 + "\"";
/* 516 */                   if (m > 0) {
/* 517 */                     str1 = str1 + " width=" + m;
/*     */                   }
/* 519 */                   if (n > 0) {
/* 520 */                     str1 = str1 + " height=" + n;
/*     */                   }
/* 522 */                   str1 = str1 + " onclick=\"addDocReadTag('" + str17 + "');openAccessory('" + str20 + "')\">\n";
/* 523 */                   str1 = str1 + "</td>\n";
/* 524 */                   str1 = str1 + "</tr>\n";
/* 525 */                   str1 = str1 + "<tr>\n";
/* 526 */                   if (!str7.equals("1") || (str7.equals("1") && str6.equals("0"))) {
/* 527 */                     str1 = str1 + "<td align=\"center\"><nobr>\n";
/* 528 */                     str1 = str1 + "<a href=\"#\" style=\"text-decoration:underline\" onmouseover=\"this.style.color='blue'\" onclick='onChangeSharetype(\"span" + paramInt1 + "_id_" + b + "\",\"field" + paramInt1 + "_del_" + b + "\",\"" + paramInt9 + "\",oUpload" + paramInt1 + ");return false;'>[<span style=\"cursor:hand;color:black;\">" + SystemEnv.getHtmlLabelName(91, i) + "</span>]</a>\n";
/* 529 */                     str1 = str1 + "<span id=\"span" + paramInt1 + "_id_" + b + "\" name=\"span" + paramInt1 + "_id_" + b + "\" style=\"visibility:hidden\"><b><font COLOR=\"#FF0033\">√</font></b><span></td>\n";
/*     */                   } 
/* 531 */                   if (!bool3) {
/* 532 */                     str1 = str1 + "<td align=\"center\"><nobr>\n";
/* 533 */                     str1 = str1 + "<a href=\"#\" style=\"text-decoration:underline\" onmouseover=\"this.style.color='blue'\" onclick=\"addDocReadTag('" + str17 + "');downloads('" + str20 + "');return false;\">[<span style=\"cursor:hand;color:black;\">" + SystemEnv.getHtmlLabelName(258, i) + "</span>]</a>\n";
/* 534 */                     str1 = str1 + "</td>\n";
/*     */                   } 
/* 536 */                   str1 = str1 + "</tr>\n";
/* 537 */                   str1 = str1 + "</table>\n";
/* 538 */                   str1 = str1 + "</td>\n"; continue;
/*     */                 } 
/* 540 */                 String str23 = AttachFileUtil.getImgStrbyExtendName(str22, 20);
/*     */                 
/* 542 */                 str1 = str1 + "<tr>\n";
/* 543 */                 str1 = str1 + "<td>\n";
/* 544 */                 str1 = str1 + "<input type=\"hidden\" id=\"field" + paramInt1 + "_del_" + b + "\" name=\"field" + paramInt1 + "_del_" + b + "\" value=\"0\" >\n";
/* 545 */                 str1 = str1 + str23 + "\n";
/*     */                 
/* 547 */                 if (i8 == 1 && (Util.isExt(str22) || str22.equalsIgnoreCase("pdf"))) {
/* 548 */                   str1 = str1 + "<a style=\"cursor:hand\" onclick=\"addDocReadTag('" + str17 + "');openDocExt('" + str17 + "','" + i9 + "','" + str20 + "',1)\">" + str21 + "</a>&nbsp;\n";
/*     */                 } else {
/* 550 */                   str1 = str1 + "<a style=\"cursor:hand\" onclick=\"addDocReadTag('" + str17 + "');openAccessory('" + str20 + "')\">" + str21 + "</a>&nbsp;\n";
/*     */                 } 
/* 552 */                 str1 = str1 + "<input type=\"hidden\" id=\"field" + paramInt1 + "_id_" + b + "\" name=\"field" + paramInt1 + "_id_" + b + "\" value=\"" + str17 + "\">\n";
/* 553 */                 str1 = str1 + "</td>\n";
/* 554 */                 if (!str7.equals("1") || (str7.equals("1") && str6.equals("0"))) {
/* 555 */                   str1 = str1 + "<td>\n";
/* 556 */                   str1 = str1 + "<button type=button  class=\"btnFlow\" accessKey=\"1\" onclick=\"onChangeSharetype('span" + paramInt1 + "_id_" + b + "','field" + paramInt1 + "_del_" + b + "','" + paramInt9 + "',oUpload" + paramInt1 + ")\"><u>" + b + "</u>-" + SystemEnv.getHtmlLabelName(91, i) + "</button>\n";
/* 557 */                   str1 = str1 + "<span id=\"span" + paramInt1 + "_id_" + b + "\" name=\"span" + paramInt1 + "_id_" + b + "\" style=\"visibility:hidden\">\n";
/* 558 */                   str1 = str1 + "<b><font color=\"#FF0033\">√</font></b>";
/* 559 */                   str1 = str1 + "</span>\n";
/* 560 */                   str1 = str1 + "</td>\n";
/*     */                 } 
/* 562 */                 if (i8 == 1 && ((!str22.equalsIgnoreCase("xls") && !str22.equalsIgnoreCase("doc") && !str22.equalsIgnoreCase("ppt") && !str22.equalsIgnoreCase("xlsx") && !str22.equalsIgnoreCase("docx") && !str22.equalsIgnoreCase("pptx") && !str22.equalsIgnoreCase("pdf") && !str22.equalsIgnoreCase("pdfx")) || !bool3)) {
/* 563 */                   str1 = str1 + "<td>\n";
/* 564 */                   str1 = str1 + "<span id=\"selectDownload\">\n";
/* 565 */                   if ((!str22.equalsIgnoreCase("xls") && !str22.equalsIgnoreCase("doc") && !str22.equalsIgnoreCase("ppt") && !str22.equalsIgnoreCase("xlsx") && !str22.equalsIgnoreCase("docx") && !str22.equalsIgnoreCase("pptx") && !str22.equalsIgnoreCase("pdf") && !str22.equalsIgnoreCase("pdfx")) || !bool3) {
/* 566 */                     str1 = str1 + "<button type=button  class=\"btnFlowd\" accessKey=\"1\" onclick=\"addDocReadTag('" + str17 + "');downloads('" + str20 + "')\">\n";
/* 567 */                     str1 = str1 + "<u>" + b + "</u>-" + SystemEnv.getHtmlLabelName(258, i) + "\t\t(" + (l / 1000L) + "K)\n";
/* 568 */                     str1 = str1 + "</button>\n";
/* 569 */                     if (!"1".equals(str4) && i6 > 1 && !bool3 && b == 0) {
/* 570 */                       str1 = str1 + "<button type=button  class=\"btnFlowd\" accessKey=\"1\" onclick=\"addDocReadTag('" + str17 + "');downloadsBatch('" + paramString3 + "','" + k + "')\">\n";
/* 571 */                       str1 = str1 + "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;" + SystemEnv.getHtmlLabelName(32405, paramUser.getLanguage()) + "\n";
/* 572 */                       str1 = str1 + "</button>\n";
/*     */                     } 
/*     */                   } 
/* 575 */                   str1 = str1 + "</span>\n";
/* 576 */                   str1 = str1 + "</td>\n";
/*     */                 } 
/* 578 */                 str1 = str1 + "</tr>\n";
/*     */               } 
/*     */               
/* 581 */               if (paramInt2 == 2 && b > -1) {
/* 582 */                 str1 = str1 + "</tr>\n</table>\n</td>\n</tr>\n";
/*     */               }
/* 584 */               str1 = str1 + "<input type=\"hidden\" id=\"field" + paramInt1 + "_idnum\" name=\"field" + paramInt1 + "_idnum\" value=\"" + (b + 1) + "\">\n";
/* 585 */               str1 = str1 + "<input type=\"hidden\" id=\"field" + paramInt1 + "_idnum_1\" name=\"field" + paramInt1 + "_idnum_1\" value=\"" + (b + 1) + "\">\n";
/*     */             } 
/*     */             
/* 588 */             str1 = str1 + "<tr>\n";
/* 589 */             str1 = str1 + "<td colspan=3>\n";
/* 590 */             String str12 = "";
/* 591 */             String str13 = "";
/* 592 */             String str14 = "";
/* 593 */             if (str8 != null && !str8.equals("")) {
/* 594 */               str12 = str8.substring(0, str8.indexOf(','));
/* 595 */               str13 = str8.substring(str8.indexOf(',') + 1, str8.lastIndexOf(','));
/* 596 */               str14 = str8.substring(str8.lastIndexOf(',') + 1, str8.length());
/*     */             } 
/* 598 */             String str15 = "*.*";
/* 599 */             String str16 = "All Files";
/* 600 */             if (paramInt2 == 2) {
/* 601 */               str15 = (new BaseBean()).getPropValue("PicFileTypes", "PicFileTypes");
/* 602 */               str16 = "Images Files";
/*     */             } 
/* 604 */             boolean bool1 = true;
/* 605 */             if (i5 == 0) {
/* 606 */               if ("".equals(str12) && "".equals(str13) && "".equals(str14)) {
/* 607 */                 bool1 = false;
/* 608 */                 str1 = str1 + "<font color=\"red\">" + SystemEnv.getHtmlLabelName(83157, i) + "!</font>\n";
/*     */               } 
/* 610 */             } else if (!bool) {
/* 611 */               bool1 = false;
/* 612 */               str1 = str1 + "<font color=\"red\">" + SystemEnv.getHtmlLabelName(83157, i) + "!</font>\n";
/*     */             } 
/* 614 */             if (bool1) {
/* 615 */               arrayList.add("" + paramInt1);
/* 616 */               str2 = str2 + "var oUpload" + paramInt1 + ";\n";
/* 617 */               str2 = str2 + "function fileupload" + paramInt1 + "() {\n";
/* 618 */               str2 = str2 + " var settings = {\n";
/* 619 */               str2 = str2 + "flash_url:\"/js/swfupload/swfupload.swf\",\n";
/* 620 */               str2 = str2 + "upload_url:\"/docs/docupload/MultiDocUploadByWorkflow.jsp\",\n";
/* 621 */               str2 = str2 + "post_params:{\n";
/* 622 */               str2 = str2 + "\t\"mainId\":\"" + str12 + "\",\n";
/* 623 */               str2 = str2 + "\t\"subId\":\"" + str13 + "\",\n";
/* 624 */               str2 = str2 + "\t\"secId\":\"" + str14 + "\",\n";
/* 625 */               str2 = str2 + "\t\"userid\":\"" + paramUser.getUID() + "\",\n";
/* 626 */               str2 = str2 + "\t\"logintype\":\"" + paramUser.getLogintype() + "\",\n";
/* 627 */               str2 = str2 + "\t\"workflowid\":\"" + j + "\"\n";
/* 628 */               str2 = str2 + "},\n";
/* 629 */               str2 = str2 + "file_size_limit :\"" + i4 + " MB\",\n";
/* 630 */               str2 = str2 + "file_types : \"" + str15 + "\",\n";
/* 631 */               str2 = str2 + "file_types_description : \"" + str16 + "\",\n";
/* 632 */               str2 = str2 + "file_upload_limit : 100,\n";
/* 633 */               str2 = str2 + "file_queue_limit : 0,\n";
/* 634 */               str2 = str2 + "custom_settings : { \n";
/* 635 */               str2 = str2 + "\tprogressTarget : \"fsUploadProgress" + paramInt1 + "\",\n";
/* 636 */               str2 = str2 + "\tcancelButtonId : \"btnCancel" + paramInt1 + "\",\n";
/* 637 */               str2 = str2 + "\tuploadspan : \"field" + paramInt1 + "span\",\n";
/* 638 */               str2 = str2 + "\tuploadfiedid : \"field" + paramInt1 + "\"\n";
/* 639 */               str2 = str2 + "},\n";
/* 640 */               str2 = str2 + "debug: false,\n";
/* 641 */               str2 = str2 + "button_image_url : \"/js/swfupload/add_wev8.png\",\n";
/* 642 */               str2 = str2 + "button_placeholder_id : \"spanButtonPlaceHolder" + paramInt1 + "\",\n";
/* 643 */               str2 = str2 + "button_width: 100,\n";
/* 644 */               str2 = str2 + "button_height: 18,\n";
/* 645 */               str2 = str2 + "button_text : '<span class=\"button\">" + SystemEnv.getHtmlLabelName(21406, i) + "</span>',\n";
/* 646 */               str2 = str2 + "button_text_style : '.button { font-family: Helvetica, Arial, sans-serif; font-size: 12pt; } .buttonSmall { font-size: 10pt; }',\n";
/* 647 */               str2 = str2 + "button_text_top_padding: 0,\n";
/* 648 */               str2 = str2 + "button_text_left_padding: 18,\n";
/* 649 */               str2 = str2 + "button_window_mode: SWFUpload.WINDOW_MODE.TRANSPARENT,\n";
/* 650 */               str2 = str2 + "button_cursor: SWFUpload.CURSOR.HAND,\n";
/* 651 */               str2 = str2 + "file_queued_handler : fileQueued,\n";
/* 652 */               str2 = str2 + "file_queue_error_handler : fileQueueError,\n";
/* 653 */               str2 = str2 + "file_dialog_complete_handler : fileDialogComplete_1,\n";
/* 654 */               str2 = str2 + "upload_start_handler : uploadStart,\n";
/* 655 */               str2 = str2 + "upload_progress_handler : uploadProgress,\n";
/* 656 */               str2 = str2 + "upload_error_handler : uploadError,\n";
/* 657 */               str2 = str2 + "upload_success_handler : uploadSuccess_1,\n";
/* 658 */               str2 = str2 + "upload_complete_handler : uploadComplete_1,\n";
/* 659 */               str2 = str2 + "queue_complete_handler : queueComplete\n";
/* 660 */               str2 = str2 + "};\n";
/* 661 */               str2 = str2 + "try {\n";
/* 662 */               str2 = str2 + "\toUpload" + paramInt1 + "=new SWFUpload(settings);\n";
/* 663 */               str2 = str2 + "} catch(e) {\n";
/* 664 */               str2 = str2 + "\talert(e)\n";
/* 665 */               str2 = str2 + "}\n";
/* 666 */               str2 = str2 + "}\n";
/*     */ 
/*     */ 
/*     */               
/* 670 */               str2 = str2 + "\tif (window.addEventListener){\n";
/* 671 */               str2 = str2 + "\t    window.addEventListener(\"load\", fileupload" + paramInt1 + ", false);\n";
/* 672 */               str2 = str2 + "\t}else if (window.attachEvent){\n";
/* 673 */               str2 = str2 + "\t    window.attachEvent(\"onload\", fileupload" + paramInt1 + ");\n";
/* 674 */               str2 = str2 + "\t}else{\n";
/* 675 */               str2 = str2 + "\t    window.onload=fileupload" + paramInt1 + ";\n";
/* 676 */               str2 = str2 + "\t}\n";
/*     */               
/* 678 */               str1 = str1 + "<TABLE class=\"ViewForm\">\n";
/* 679 */               str1 = str1 + "<tr>\n";
/* 680 */               str1 = str1 + "<td colspan=2>\n";
/* 681 */               str1 = str1 + "<div>\n";
/* 682 */               str1 = str1 + "<span>\n";
/* 683 */               str1 = str1 + "<span id=\"spanButtonPlaceHolder" + paramInt1 + "\"></span>\n";
/* 684 */               str1 = str1 + "</span>\n";
/* 685 */               str1 = str1 + "&nbsp;&nbsp;<span style=\"color:#262626;cursor:hand;TEXT-DECORATION:none\" disabled onclick=\"oUpload" + paramInt1 + ".cancelQueue();showmustinput(oUpload" + paramInt1 + ");\" id=\"btnCancel" + paramInt1 + "\">\n";
/* 686 */               str1 = str1 + "<span><img src=\"/js/swfupload/delete_wev8.gif\" border=0></span>\n";
/* 687 */               str1 = str1 + "<span style=\"height:19px\"><font style=\"margin:0 0 0 -1\">" + SystemEnv.getHtmlLabelName(21407, i) + "</font></span>\n";
/* 688 */               str1 = str1 + "</span><span id=\"uploadspan\">(" + SystemEnv.getHtmlLabelName(18976, i) + i4 + SystemEnv.getHtmlLabelName(18977, i) + ")</span>\n";
/* 689 */               str1 = str1 + "<span id=\"field" + paramInt1 + "span\">\n";
/* 690 */               if (paramInt9 == 1 && paramString3.equals("")) {
/* 691 */                 str1 = str1 + "<img src='/images/BacoError_wev8.gif' align=absMiddle>\n";
/*     */               }
/* 693 */               str1 = str1 + "</span>\n";
/* 694 */               str1 = str1 + "</div>\n";
/* 695 */               str1 = str1 + "<input  class=InputStyle  type=hidden size=60 name=\"field" + paramInt1 + "\" id=\"field" + paramInt1 + "\" temptitle=\"" + Util.toScreen(paramString2, i) + "\"  viewtype=" + paramInt9 + " value=\"" + paramString3 + "\">\n";
/* 696 */               str1 = str1 + "</td>\n";
/* 697 */               str1 = str1 + "</tr>\n";
/* 698 */               str1 = str1 + "<tr>\n";
/* 699 */               str1 = str1 + "<td colspan=2>\n";
/* 700 */               str1 = str1 + "<div class=\"fieldset flash\" id=\"fsUploadProgress" + paramInt1 + "\">\n";
/* 701 */               str1 = str1 + "</div>\n";
/* 702 */               str1 = str1 + "<div id=\"divStatus" + paramInt1 + "\"></div>\n";
/* 703 */               str1 = str1 + "</td>\n";
/* 704 */               str1 = str1 + "</tr>\n";
/* 705 */               str1 = str1 + "</TABLE>\n";
/*     */             } 
/* 707 */             str1 = str1 + "<input type=\"hidden\" id=\"mainId\" name=\"mainId\" value=\"" + str12 + "\">\n";
/* 708 */             str1 = str1 + "<input type=\"hidden\" id=\"subId\" name=\"subId\" value=\"" + str13 + "\">\n";
/* 709 */             str1 = str1 + "<input type=\"hidden\" id=\"secId\" name=\"secId\" value=\"" + str14 + "\">\n";
/* 710 */             str1 = str1 + "</td>\n";
/* 711 */             str1 = str1 + "</tr>\n";
/* 712 */             str1 = str1 + "</TABLE>\n";
/*     */           } else {
/* 714 */             str1 = str1 + "<table cols=\"3\" id=\"field" + paramInt1 + "_tab\">\n";
/* 715 */             str1 = str1 + "<tbody>\n";
/* 716 */             str1 = str1 + "<col width=\"50%\">\n";
/* 717 */             str1 = str1 + "<col width=\"25%\">\n";
/* 718 */             str1 = str1 + "<col width=\"25%\">\n";
/* 719 */             if ("-2".equals(paramString3)) {
/* 720 */               str1 = str1 + "<tr>\n";
/* 721 */               str1 = str1 + "<td colSpan=\"3\"><font color=\"red\">\n";
/* 722 */               str1 = str1 + "" + SystemEnv.getHtmlLabelName(21710, i) + "</font>\n";
/* 723 */               str1 = str1 + "</td>\n";
/* 724 */               str1 = str1 + "</tr>\n";
/*     */             }
/* 726 */             else if (!paramString3.equals("")) {
/* 727 */               str3 = "select id,docsubject,accessorycount,SecCategory from docdetail where id in(" + paramString3 + ") order by id asc";
/* 728 */               byte b = -1;
/* 729 */               int i6 = i1;
/* 730 */               boolean bool1 = false;
/* 731 */               recordSet.executeSql(str3);
/* 732 */               int i7 = recordSet.getCounts();
/* 733 */               while (recordSet.next()) {
/* 734 */                 bool1 = false;
/* 735 */                 b++;
/* 736 */                 String str12 = Util.null2String(recordSet.getString(1));
/* 737 */                 String str13 = Util.toScreen(recordSet.getString(2), i);
/* 738 */                 int i8 = recordSet.getInt(3);
/* 739 */                 String str14 = Util.null2String(recordSet.getString(4));
/* 740 */                 docImageManager.resetParameter();
/* 741 */                 docImageManager.setDocid(Integer.parseInt(str12));
/* 742 */                 docImageManager.selectDocImageInfo();
/*     */                 
/* 744 */                 String str15 = "";
/* 745 */                 long l = 0L;
/* 746 */                 String str16 = "";
/* 747 */                 String str17 = "";
/* 748 */                 int i9 = 0;
/*     */                 
/* 750 */                 if (docImageManager.next()) {
/* 751 */                   str15 = docImageManager.getImagefileid();
/* 752 */                   l = docImageManager.getImageFileSize(Util.getIntValue(str15));
/* 753 */                   str16 = docImageManager.getImagefilename();
/* 754 */                   str17 = str16.substring(str16.lastIndexOf(".") + 1).toLowerCase();
/* 755 */                   i9 = docImageManager.getVersionId();
/*     */                 } 
/* 757 */                 if (i8 > 1) {
/* 758 */                   str17 = "htm";
/*     */                 }
/* 760 */                 String str18 = AttachFileUtil.getImgStrbyExtendName(str17, 20);
/* 761 */                 boolean bool2 = secCategoryComInfo.getNoDownload(str14).equals("1") ? true : false;
/* 762 */                 if (i2 == 1) {
/* 763 */                   bool2 = false;
/*     */                 }
/* 765 */                 if (paramInt2 == 2) {
/* 766 */                   if (b == 0) {
/* 767 */                     bool1 = true;
/*     */                     
/* 769 */                     if (!"1".equals(str4) && !bool2 && i7 > 1 && b == 0) {
/*     */ 
/*     */                       
/* 772 */                       str1 = str1 + "<button type=button  class=\"btnFlowd\" accessKey=\"1\" onclick=\"addDocReadTag('" + str12 + "');top.location='/weaver/weaver.file.FileDownload?fieldvalue=" + paramString3 + "&download=1&downloadBatch=1&requestid=" + k + "'\">\n";
/* 773 */                       str1 = str1 + "&nbsp;&nbsp;&nbsp;" + SystemEnv.getHtmlLabelName(10000008, paramUser.getLanguage()) + "\n";
/* 774 */                       str1 = str1 + "</button>\n";
/*     */                     } 
/*     */                     
/* 777 */                     str1 = str1 + "<tr>\n";
/* 778 */                     str1 = str1 + "<td colSpan=3>\n";
/* 779 */                     str1 = str1 + "<table cellspacing=\"0\" cellpadding=\"0\">\n";
/* 780 */                     str1 = str1 + "<tr>\n";
/*     */                   } 
/* 782 */                   if (i6 > 0 && b >= i6) {
/* 783 */                     i6 += i1;
/* 784 */                     bool1 = true;
/* 785 */                     str1 = str1 + "</tr>\n";
/* 786 */                     str1 = str1 + "<tr>\n";
/*     */                   } 
/* 788 */                   str1 = str1 + "<input type=\"hidden\" id=\"field" + paramInt1 + "_del_" + b + "\" name=\"field" + paramInt1 + "_del_" + b + "\" value=\"0\" >\n";
/* 789 */                   str1 = str1 + "<input type=\"hidden\" id=\"field" + paramInt1 + "_id_" + b + "\" name=\"field" + paramInt1 + "_id_" + b + "\" value=\"" + str12 + "\" >\n";
/* 790 */                   str1 = str1 + "<td ";
/* 791 */                   if (!bool1) {
/* 792 */                     str1 = str1 + "style=\"padding-left:15\"";
/*     */                   }
/* 794 */                   str1 = str1 + ">\n";
/* 795 */                   str1 = str1 + "<table>\n";
/* 796 */                   str1 = str1 + "<tr>\n";
/* 797 */                   str1 = str1 + "<td colspan=2 align=\"center\"><img src=\"/weaver/weaver.file.FileDownload?fileid=" + str15 + "&requestid=" + k + "\" style=\"cursor:hand\" alt=\"" + str16 + "\"";
/* 798 */                   if (m > 0)
/* 799 */                     str1 = str1 + " width=" + m; 
/* 800 */                   if (n > 0)
/* 801 */                     str1 = str1 + " height=" + n; 
/* 802 */                   str1 = str1 + " onclick=\"addDocReadTag('" + str12 + "');openAccessory('" + str15 + "')\">\n";
/* 803 */                   str1 = str1 + "</td>\n";
/* 804 */                   str1 = str1 + "</tr>\n";
/* 805 */                   str1 = str1 + "<tr>\n";
/* 806 */                   if (!bool2 && paramInt6 == 0) {
/* 807 */                     str1 = str1 + "<td align=\"center\"><nobr>\n";
/* 808 */                     str1 = str1 + "<a href=\"#\" style=\"text-decoration:underline\" onmouseover=\"this.style.color='blue'\" onclick=\"addDocReadTag('" + str12 + "');top.location='/weaver/weaver.file.FileDownload?fileid=" + str15 + "&download=1&requestid=" + k + "';return false;\">[<span style=\"cursor:hand;color:black;\">" + SystemEnv.getHtmlLabelName(258, i) + "</span>]</a>\n";
/* 809 */                     str1 = str1 + "</td>\n";
/*     */                   } 
/* 811 */                   str1 = str1 + "</tr>\n";
/* 812 */                   str1 = str1 + "</table>\n";
/* 813 */                   str1 = str1 + "</td>\n"; continue;
/*     */                 } 
/* 815 */                 str1 = str1 + "<tr>\n";
/* 816 */                 str1 = str1 + "<td colspan=\"3\">\n";
/* 817 */                 str1 = str1 + str18;
/* 818 */                 if (i8 == 1 && (Util.isExt(str17) || str17.equalsIgnoreCase("pdf"))) {
/* 819 */                   str1 = str1 + "<a style=\"cursor:hand\" onclick=\"addDocReadTag('" + str12 + "');openDocExt('" + str12 + "','" + i9 + "','" + str15 + "',0)\">" + str16 + "</a>&nbsp;\n";
/*     */                 } else {
/* 821 */                   str1 = str1 + "<a style=\"cursor:hand\" onclick=\"addDocReadTag('" + str12 + "');openAccessory('" + str15 + "')\">" + str16 + "</a>&nbsp;\n";
/*     */                 } 
/* 823 */                 if (paramInt6 == 0) {
/* 824 */                   str1 = str1 + "<input type=\"hidden\" id=\"field" + paramInt1 + "_id_" + b + "\" name=\"field" + paramInt1 + "_id_" + b + "\" value=\"" + str12 + "\">\n";
/*     */                 }
/* 826 */                 if ((!str17.equalsIgnoreCase("xls") && !str17.equalsIgnoreCase("doc") && !str17.equalsIgnoreCase("ppt") && !str17.equalsIgnoreCase("xlsx") && !str17.equalsIgnoreCase("docx") && !str17.equalsIgnoreCase("pptx") && !str17.equalsIgnoreCase("pdf") && !str17.equalsIgnoreCase("pdfx")) || !bool2) {
/* 827 */                   str1 = str1 + "<span id=\"selectDownload\">\n";
/* 828 */                   str1 = str1 + "<button type=button  class=\"btnFlowd\" accessKey=\"1\" onclick=\"addDocReadTag('" + str12 + "');top.location='/weaver/weaver.file.FileDownload?fileid=" + str15 + "&download=1&requestid=" + k + "'\">\n";
/* 829 */                   str1 = str1 + "<u>" + b + "</u>-" + SystemEnv.getHtmlLabelName(258, i) + "\t(" + (l / 1000L) + "K)\n";
/* 830 */                   str1 = str1 + "</button>\n";
/* 831 */                   str1 = str1 + "</span>\n";
/* 832 */                   if (!"1".equals(str4) && i7 > 1 && !bool2 && b == 0) {
/* 833 */                     str1 = str1 + "<button type=button  class=\"btnFlowd\" accessKey=\"1\" onclick=\"addDocReadTag('" + str12 + "');top.location='/weaver/weaver.file.FileDownload?fieldvalue=" + paramString3 + "&download=1&downloadBatch=1&requestid=" + k + "'\">\n";
/* 834 */                     str1 = str1 + "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;" + SystemEnv.getHtmlLabelName(32405, paramUser.getLanguage()) + "\n";
/* 835 */                     str1 = str1 + "</button>\n";
/*     */                   } 
/*     */                 } 
/* 838 */                 str1 = str1 + "</td>\n";
/* 839 */                 str1 = str1 + "</tr>\n";
/*     */               } 
/*     */               
/* 842 */               if (paramInt2 == 2 && b > -1) {
/* 843 */                 str1 = str1 + "</tr></table></td></tr>\n";
/*     */               }
/*     */               
/* 846 */               str1 = str1 + "<input type=\"hidden\" id=\"field" + paramInt1 + "_idnum\" name=\"field" + paramInt1 + "_idnum\" value=\"" + (b + 1) + "\">\n";
/* 847 */               str1 = str1 + "<input type=hidden name=\"field" + paramInt1 + "\" value=\"" + paramString3 + "\">\n";
/*     */             } 
/*     */ 
/*     */             
/* 851 */             str1 = str1 + "</tbody>\n";
/* 852 */             str1 = str1 + "</table>\n";
/*     */           }
/*     */         
/* 855 */         } else if (paramInt6 == 0 && 
/* 856 */           !paramString3.equals("") && !paramString3.equals("-2")) {
/* 857 */           String[] arrayOfString = Util.TokenizerString2(paramString3, ",");
/* 858 */           byte b = -1;
/* 859 */           for (byte b1 = 0; b1 < arrayOfString.length; b1++) {
/* 860 */             b++;
/* 861 */             String str = Util.null2String("" + arrayOfString[b1]);
/* 862 */             str1 = "<input type=\"hidden\" id=\"field" + paramInt1 + "_id_" + b + "\" name=\"field" + paramInt1 + "_id_" + b + "\" value=\"" + str + "\">\n";
/*     */           } 
/* 864 */           str1 = "<input type=\"hidden\" id=\"field" + paramInt1 + "_idnum\" name=\"field" + paramInt1 + "_idnum\" value=\"" + b + '\001' + "\">\n";
/*     */         } 
/*     */ 
/*     */         
/* 868 */         if (arrayList1.indexOf("" + paramInt1) >= 0) {
/* 869 */           str1 = str1 + "<input type=\"hidden\" id=\"oldfieldview" + paramInt1 + "\" name=\"oldfieldview" + paramInt1 + "\" value=\"" + (paramInt7 + paramInt8 + paramInt9) + "\" >";
/*     */         }
/*     */       }
/*     */     
/*     */     }
/* 874 */     catch (Exception exception) {
/* 875 */       str1 = "";
/* 876 */       writeLog(exception);
/*     */     } 
/* 878 */     hashtable.put("jsStr", str2);
/* 879 */     hashtable.put("inputStr", str1);
/* 880 */     paramHashtable.put("uploadfieldids", arrayList);
/* 881 */     return hashtable;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/cpt/util/html/FileElement.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */