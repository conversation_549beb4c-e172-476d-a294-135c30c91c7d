/*     */ package weaver.cpt.util.html;
/*     */ 
/*     */ import org.json.JSONObject;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class InputElement
/*     */   extends BaseBean
/*     */   implements HtmlElement
/*     */ {
/*     */   public String getHtmlElementString(String paramString, JSONObject paramJSONObject, User paramUser) {
/*  22 */     String str = "";
/*  23 */     if (paramJSONObject == null || paramUser == null) {
/*  24 */       return "";
/*     */     }
/*     */     try {
/*  27 */       int i = paramUser.getLanguage();
/*  28 */       String str1 = paramJSONObject.getString("id");
/*  29 */       String str2 = paramJSONObject.getString("fieldname");
/*  30 */       String str3 = paramJSONObject.getString("fieldlabel");
/*  31 */       String str4 = paramJSONObject.getString("fielddbtype");
/*  32 */       int j = paramJSONObject.getInt("ismand");
/*  33 */       int k = paramJSONObject.getInt("type");
/*  34 */       int m = paramJSONObject.getInt("issystem");
/*  35 */       String str5 = (1 == m) ? str2 : ("field" + str1);
/*  36 */       String str6 = "0";
/*  37 */       if (paramJSONObject.getString("fieldkind") != null && "2".equals(paramJSONObject.getString("fieldkind"))) {
/*  38 */         str5 = "customfield" + str1;
/*  39 */         str6 = "1";
/*     */       } 
/*     */       
/*  42 */       int n = 2;
/*     */       
/*  44 */       String str7 = "";
/*  45 */       String str8 = "";
/*     */       
/*  47 */       String str9 = "";
/*  48 */       if (j == 1 && "".equals(paramString)) {
/*  49 */         str9 = "<img src='/images/BacoError_wev8.gif' align='absmiddle'>";
/*     */       }
/*  51 */       if (k == 1) {
/*  52 */         int i1 = Util.getIntValue(str4.substring(str4.indexOf("(") + 1, str4.indexOf(")")), 30);
/*  53 */         str = "<input datatype=\"text\" class=\"InputStyle\"  size=\"30\" maxLength=\"" + i1 + "\" alt='" + SystemEnv.getHtmlLabelName(20246, i) + i1 + "(" + SystemEnv.getHtmlLabelName(124962, i) + ")!" + "'  value=\"" + paramString + "\" id=\"" + str5 + "\" name=\"" + str5 + "\" " + ((j == 1) ? ("onblur=\"checkinput('" + str5 + "','" + str5 + "span',this.getAttribute('viewtype'));checkMaxLength(this);\"") : "checkMaxLength(this);") + " />";
/*  54 */       } else if (k == 2) {
/*  55 */         str = str + "<input datatype=\"int\"  onafterpaste=\"if(isNaN(value))execCommand('undo')\"  style=\"ime-mode:disabled\" viewtype=\"" + j + "\" type=\"text\" class=\"InputStyle\" id=\"" + str5 + "\" name=\"" + str5 + "\" style=\"width:70%\" onKeyPress=\"ItemCount_KeyPress()\" ";
/*  56 */         str = str + " onBlur=\"checkcount1(this);checkItemScale(this,'" + SystemEnv.getHtmlLabelName(31181, i).replace("12", "9") + "',-999999999,999999999);checkinput2('" + str5 + "','" + str5 + "span',this.getAttribute('viewtype'))\" ";
/*  57 */         str = str + " value=\"" + paramString + "\"  onchange=\"" + str7 + "\"  onpropertychange=\"" + str7 + "\"   _listener=\"" + str7 + "\">";
/*  58 */       } else if (k == 3 || k == 5) {
/*  59 */         if (k == 3) {
/*  60 */           int i1 = str4.indexOf(",");
/*  61 */           if (i1 > -1) {
/*  62 */             n = Util.getIntValue(str4.substring(i1 + 1, str4.length() - 1), 2);
/*     */           } else {
/*  64 */             n = 2;
/*     */           } 
/*     */         } 
/*     */         
/*  68 */         if (k == 5) {
/*  69 */           n = Util.getIntValue(paramJSONObject.getString("qfws"), 2);
/*  70 */           if (!paramString.equals("") && n == 1) {
/*  71 */             paramString = paramString.substring(0, paramString.indexOf(".") + n + 1);
/*     */           }
/*  73 */           paramString = Util.toDecimalDigits(paramString, n);
/*  74 */           str = str + "<input datalength='" + n + "' datatype=\"float\"  style=\"ime-mode:disabled\"  onafterpaste=\"if(isNaN(value))execCommand('undo')\" viewtype=\"" + j + "\" type=\"text\" class=\"InputStyle\" id=\"" + str5 + "\" name=\"" + str5 + "\" style=\"width:70%\" onKeyPress=\"ItemDecimal_KeyPress('" + str5 + "',15," + n + ")\" ";
/*  75 */           str = str + "  onfocus=\"changeToNormalFormat('" + str5 + "')\" ";
/*  76 */           str = str + "datavaluetype='5' datalength=" + n + " onBlur=\"";
/*     */         } else {
/*  78 */           str = str + "<input datalength='" + n + "' datatype=\"float\"  style=\"ime-mode:disabled\"  onafterpaste=\"if(isNaN(value))execCommand('undo')\" viewtype=\"" + j + "\" type=\"text\" class=\"InputStyle\" id=\"" + str5 + "\" name=\"" + str5 + "\" style=\"width:70%\" onKeyPress=\"ItemDecimal_KeyPress('" + str5 + "',15," + n + ")\" ";
/*  79 */           paramString = Util.toDecimalDigits(paramString, n);
/*  80 */           str = str + " datalength=" + n + " onBlur=\"checkFloat(this);";
/*     */         } 
/*     */         
/*  83 */         str = str + "checkinput2('" + str5 + "','" + str5 + "span',this.getAttribute('viewtype'));";
/*  84 */         if (k == 5) {
/*  85 */           str = str + "changeToThousands2('" + str5 + "','" + n + "');";
/*     */         }
/*  87 */         str = str + "\" ";
/*  88 */         str = str + " value=\"" + paramString + "\"  onchange=\"" + str7 + "\"  onpropertychange=\"" + str7 + "\" _listener=\"" + str7 + "\">";
/*  89 */       } else if (k == 4) {
/*  90 */         int i1 = str4.indexOf(",");
/*  91 */         if (i1 > -1) {
/*  92 */           n = Util.getIntValue(str4.substring(i1 + 1, str4.length() - 1), 2);
/*     */         }
/*     */         
/*  95 */         str = str + "<table cols=\"2\" id=\"" + str5 + "_tab\" width=\"100%\">";
/*  96 */         str = str + "<tr><td>";
/*     */         
/*  98 */         str = str + "<input datatype=\"float\"  onKeyPress=\"ItemDecimal_KeyPress('field_lable" + str1 + "',15," + n + ")\" style=\"ime-mode:disabled;width:70%\"  onafterpaste=\"if(isNaN(value))execCommand('undo')\" type=\"text\" class=\"InputStyle\" id=\"field_lable" + str1 + "\" name=\"field_lable" + str1 + "\" onfocus=\"FormatToNumber('" + str1 + "'," + str6 + ")\" ";
/*  99 */         str = str + " onBlur=\"checkFloat(this);numberToFormat('" + str1 + "'," + str6 + ");checkinput2('field_lable" + str1 + "','field_lable" + str1 + "span'," + str5 + ".getAttribute('viewtype'))\"";
/* 100 */         str = str + " onpropertychange=\"" + str8 + "\">";
/* 101 */         str = str + "<span id=\"field_lable" + str1 + "span\">" + str9 + "</span>";
/* 102 */         str = str + "<span id=\"" + str5 + "span\" style=\"word-break:break-all;word-wrap:break-word\"></span>";
/*     */         
/* 104 */         str = str + "<input datatype=\"float\" filedtype=\"4\" datalength=\"2\" viewtype=\"" + j + "\" type=\"hidden\" class=\"InputStyle\" id=\"" + str5 + "\" name=\"" + str5 + "\" value=\"" + paramString + "\" >";
/*     */       } 
/*     */       
/* 107 */       if (k != 4) {
/* 108 */         str = str + "<span id=\"" + str5 + "span\" style=\"word-break:break-all;word-wrap:break-word\">" + str9 + "</span>";
/*     */       }
/* 110 */       if (k == 4) {
/* 111 */         str = str + "</td></tr>";
/* 112 */         str = str + "<tr><td>";
/* 113 */         str = str + "<input type=\"text\" class=\"InputStyle\" style=\"width:70%\" id=\"field_chinglish" + str1 + "\" name=\"field_chinglish" + str1 + "\" readOnly=\"true\">";
/* 114 */         str = str + "</td></tr>";
/* 115 */         str = str + "</table>";
/* 116 */         if (!"".equals(paramString)) {
/* 117 */           str = str + "<script language=\"javascript\">";
/* 118 */           str = str + "$G(\"field_lable\"+" + str1 + ").value = milfloatFormat(floatFormat(" + paramString + "));";
/* 119 */           str = str + "$G(\"field_chinglish\"+" + str1 + ").value = numberChangeToChinese(" + paramString + ");";
/* 120 */           str = str + "</script>";
/*     */         }
/*     */       
/*     */       } 
/* 124 */     } catch (Exception exception) {
/* 125 */       exception.printStackTrace();
/* 126 */       writeLog(exception.getMessage());
/*     */     } 
/*     */ 
/*     */     
/* 130 */     return str;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/cpt/util/html/InputElement.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */