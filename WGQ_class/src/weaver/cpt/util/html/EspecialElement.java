/*     */ package weaver.cpt.util.html;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Hashtable;
/*     */ import org.json.JSONObject;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.workflow.field.SpecialFieldInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class EspecialElement
/*     */   extends BaseBean
/*     */   implements HtmlElement
/*     */ {
/*     */   public String getHtmlElementString(String paramString, JSONObject paramJSONObject, User paramUser) {
/*  22 */     String str = "";
/*  23 */     if (paramJSONObject == null || paramUser == null) {
/*  24 */       return "";
/*     */     }
/*     */     try {
/*  27 */       int i = paramUser.getLanguage();
/*  28 */       String str1 = paramJSONObject.getString("id");
/*  29 */       String str2 = paramJSONObject.getString("fieldname");
/*  30 */       String str3 = paramJSONObject.getString("fieldlabel");
/*  31 */       String str4 = paramJSONObject.getString("fielddbtype");
/*  32 */       int j = paramJSONObject.getInt("ismand");
/*  33 */       int k = paramJSONObject.getInt("type");
/*     */       
/*  35 */       byte b = 2;
/*     */       
/*  37 */       String str5 = "";
/*  38 */       String str6 = "";
/*     */       
/*  40 */       String str7 = "";
/*     */       
/*  42 */       String str8 = "select fieldid,displayname,linkaddress,descriptivetext from cpt_specialfield a where a.fieldid ='" + str1 + "'";
/*  43 */       RecordSet recordSet = new RecordSet();
/*  44 */       recordSet.executeSql(str8);
/*  45 */       if (recordSet.next()) {
/*  46 */         String str9 = "";
/*  47 */         if (k == 1) {
/*  48 */           str = "<a href='" + recordSet.getString("linkaddress") + "' target='_blank'>" + recordSet.getString("displayname") + "</a>";
/*     */         } else {
/*  50 */           str = Util.null2String(recordSet.getString("descriptivetext"));
/*     */         }
/*     */       
/*     */       } 
/*  54 */     } catch (Exception exception) {
/*  55 */       exception.printStackTrace();
/*  56 */       writeLog(exception.getMessage());
/*     */     } 
/*     */ 
/*     */     
/*  60 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public Hashtable getHtmlElementString(int paramInt1, String paramString1, int paramInt2, String paramString2, int paramInt3, int paramInt4, int paramInt5, String paramString3, int paramInt6, int paramInt7, int paramInt8, int paramInt9, User paramUser, Hashtable paramHashtable) {
/*  67 */     Hashtable<Object, Object> hashtable = new Hashtable<Object, Object>();
/*  68 */     String str1 = "";
/*  69 */     String str2 = "";
/*     */     try {
/*  71 */       ArrayList<String> arrayList1 = (ArrayList)paramHashtable.get("sqlfieldidList");
/*  72 */       ArrayList<String> arrayList2 = (ArrayList)paramHashtable.get("sqlcontentList");
/*  73 */       if (arrayList1 != null && arrayList1.size() > 0) {
/*  74 */         for (byte b = 0; b < arrayList1.size(); b++) {
/*  75 */           String str3 = Util.null2String(arrayList1.get(b)).trim();
/*  76 */           String str4 = Util.null2String(arrayList2.get(b)).trim();
/*  77 */           if (str3.equals("" + paramInt1)) {
/*  78 */             str2 = str2 + "function getFieldValueAjax" + paramInt1 + "(){" + "\n";
/*  79 */             str2 = str2 + "initFieldValue(\"" + paramInt1 + "\");" + "\n";
/*  80 */             str2 = str2 + "}\n";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */             
/*  87 */             str2 = str2 + "\tif (window.addEventListener){\n";
/*  88 */             str2 = str2 + "\t    window.addEventListener(\"load\", getFieldValueAjax" + paramInt1 + ", false);" + "\n";
/*  89 */             str2 = str2 + "\t}else if (window.attachEvent){\n";
/*  90 */             str2 = str2 + "\t    window.attachEvent(\"onload\", getFieldValueAjax" + paramInt1 + ");" + "\n";
/*  91 */             str2 = str2 + "\t}else{\n";
/*  92 */             str2 = str2 + "\t    window.onload=getFieldValueAjax" + paramInt1 + ";" + "\n";
/*  93 */             str2 = str2 + "\t}\n";
/*     */           } 
/*     */         } 
/*     */       }
/*  97 */       if (paramInt4 == 0 && 
/*  98 */         paramInt7 == 1) {
/*  99 */         str1 = str1 + "<span id=\"field" + paramInt1 + "span\" name=\"field" + paramInt1 + "span\" style=\"word-break:break-all;word-wrap:break-word\">";
/* 100 */         int i = Util.getIntValue((String)paramHashtable.get("isbill"));
/* 101 */         SpecialFieldInfo specialFieldInfo = new SpecialFieldInfo();
/* 102 */         HashMap hashMap = specialFieldInfo.getFormSpecialField();
/* 103 */         if (i == 0) {
/* 104 */           str1 = str1 + Util.null2String((String)hashMap.get(paramInt1 + "_0"));
/*     */         } else {
/* 106 */           str1 = str1 + Util.null2String((String)hashMap.get(paramInt1 + "_1"));
/*     */         } 
/* 108 */         str1 = str1 + "</span>\n";
/*     */ 
/*     */       
/*     */       }
/*     */ 
/*     */     
/*     */     }
/* 115 */     catch (Exception exception) {
/* 116 */       str1 = "";
/* 117 */       writeLog(exception);
/*     */     } 
/* 119 */     hashtable.put("jsStr", str2);
/* 120 */     hashtable.put("inputStr", str1);
/* 121 */     return hashtable;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/cpt/util/html/EspecialElement.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */