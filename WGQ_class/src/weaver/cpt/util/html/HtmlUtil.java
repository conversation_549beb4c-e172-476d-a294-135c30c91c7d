/*    */ package weaver.cpt.util.html;
/*    */ 
/*    */ import org.json.JSONException;
/*    */ import org.json.JSONObject;
/*    */ import weaver.hrm.User;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class HtmlUtil
/*    */ {
/*    */   public static String getHtmlClassName(String paramString) {
/* 17 */     return paramString.equals("1") ? "weaver.cpt.util.html.InputElement" : (paramString.equals("2") ? "weaver.cpt.util.html.TextareaElement" : (paramString.equals("3") ? "weaver.cpt.util.html.BrowserElement" : (paramString.equals("4") ? "weaver.cpt.util.html.CheckElement" : (paramString.equals("5") ? "weaver.cpt.util.html.SelectElement" : (paramString.equals("6") ? "weaver.cpt.util.html.FileElement" : (paramString.equals("7") ? "weaver.cpt.util.html.EspecialElement" : "weaver.cpt.util.html.InputElement"))))));
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getHtmlInputString(String paramString, JSONObject paramJSONObject, User paramUser) throws InstantiationException, IllegalAccessException, ClassNotFoundException, JSONException {
/* 29 */     return ((HtmlElement)Class.forName(paramJSONObject.getString("eleclazzname")).newInstance()).getHtmlElementString(paramString, paramJSONObject, paramUser);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/cpt/util/html/HtmlUtil.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */