/*    */ package weaver.cpt.util.html;
/*    */ 
/*    */ import org.json.JSONObject;
/*    */ import weaver.general.BaseBean;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.User;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class CheckElement
/*    */   extends BaseBean
/*    */   implements HtmlElement
/*    */ {
/*    */   public String getHtmlElementString(String paramString, JSONObject paramJSONObject, User paramUser) {
/* 20 */     String str = "";
/* 21 */     if (paramJSONObject == null || paramUser == null) {
/* 22 */       return "";
/*    */     }
/*    */     try {
/* 25 */       int i = paramUser.getLanguage();
/* 26 */       String str1 = paramJSONObject.getString("id");
/* 27 */       String str2 = paramJSONObject.getString("fieldname");
/* 28 */       String str3 = paramJSONObject.getString("fieldlabel");
/* 29 */       String str4 = paramJSONObject.getString("fielddbtype");
/* 30 */       int j = paramJSONObject.getInt("ismand");
/* 31 */       int k = paramJSONObject.getInt("type");
/* 32 */       int m = paramJSONObject.getInt("issystem");
/* 33 */       String str5 = (1 == m) ? str2 : ("field" + str1);
/* 34 */       if (paramJSONObject.getString("fieldkind") != null && "2".equals(paramJSONObject.getString("fieldkind"))) {
/* 35 */         str5 = "customfield" + str1;
/*    */       }
/* 37 */       byte b = 2;
/* 38 */       int n = Util.getIntValue(paramString, 0);
/*    */       
/* 40 */       String str6 = "";
/* 41 */       String str7 = "";
/*    */       
/* 43 */       String str8 = "";
/* 44 */       if (j == 1 && "".equals(paramString)) {
/* 45 */         str8 = "<img src='/images/BacoError_wev8.gif' align='absmiddle'>";
/*    */       }
/* 47 */       str = str + "<input type=\"checkbox\" id=\"" + str5 + "\" name=\"" + str5 + "\" class=\"InputStyle\" viewtype=\"" + j + "\" temptitle=\"" + Util.toScreen(SystemEnv.getHtmlLabelName(Util.getIntValue(str3), i), i) + "\" value=\"1\" ";
/* 48 */       if (n == 1) {
/* 49 */         str = str + " checked ";
/*    */       }
/* 51 */       str = str + " onclick=\"" + str6 + "\" ";
/* 52 */       str = str + ">\n";
/*    */     }
/* 54 */     catch (Exception exception) {
/* 55 */       exception.printStackTrace();
/* 56 */       writeLog(exception.getMessage());
/*    */     } 
/*    */ 
/*    */     
/* 60 */     return str;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/cpt/util/html/CheckElement.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */