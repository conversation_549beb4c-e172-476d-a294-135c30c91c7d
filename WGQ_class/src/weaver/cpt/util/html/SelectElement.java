/*     */ package weaver.cpt.util.html;
/*     */ 
/*     */ import org.json.JSONObject;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class SelectElement
/*     */   extends BaseBean
/*     */   implements HtmlElement
/*     */ {
/*     */   public String getHtmlElementString(String paramString, JSONObject paramJSONObject, User paramUser) {
/*  22 */     String str = "";
/*  23 */     if (paramJSONObject == null || paramUser == null) {
/*  24 */       return "";
/*     */     }
/*     */     try {
/*  27 */       int i = paramUser.getLanguage();
/*  28 */       String str1 = paramJSONObject.getString("id");
/*  29 */       String str2 = paramJSONObject.getString("fieldname");
/*  30 */       String str3 = paramJSONObject.getString("fieldlabel");
/*  31 */       String str4 = paramJSONObject.getString("fielddbtype");
/*  32 */       int j = paramJSONObject.getInt("ismand");
/*  33 */       int k = paramJSONObject.getInt("type");
/*  34 */       int m = paramJSONObject.getInt("issystem");
/*  35 */       String str5 = (m == 1) ? str2 : ("field" + str1);
/*  36 */       if (paramJSONObject.getString("fieldkind") != null && "2".equals(paramJSONObject.getString("fieldkind"))) {
/*  37 */         str5 = "customfield" + str1;
/*     */       }
/*  39 */       String str6 = paramJSONObject.getString("seltype");
/*     */       
/*  41 */       byte b = 2;
/*     */       
/*  43 */       String str7 = "";
/*  44 */       String str8 = "";
/*  45 */       String str9 = "";
/*  46 */       String str10 = "";
/*  47 */       if (j == 1 && "".equals(paramString)) {
/*  48 */         str10 = "<img src='/images/BacoError_wev8.gif' align='absmiddle'>";
/*     */       }
/*     */       
/*  51 */       str = str + "<select class=\"InputStyle\" name=\"" + str5 + "\" viewtype=\"" + j + "\" onBlur=\"checkinput2('" + str5 + "','" + str5 + "span',this.getAttribute('viewtype'));\"" + str9 + " ";
/*  52 */       str = str + ">\n";
/*  53 */       str = str + "<option value=\"\"></option>\n";
/*  54 */       boolean bool = true;
/*  55 */       String str11 = "";
/*  56 */       RecordSet recordSet = new RecordSet();
/*  57 */       char c = Util.getSeparator();
/*     */       
/*  59 */       if ("cpt".equalsIgnoreCase(str6)) {
/*  60 */         recordSet.executeProc("cpt_selectitembyid_new", "" + str1 + c + '\001');
/*  61 */       } else if ("prj".equalsIgnoreCase(str6)) {
/*  62 */         recordSet.executeProc("prj_selectitembyid_new", "" + getRealSelectFieldId4prj(str1) + c + '\001');
/*  63 */       } else if ("prjtype".equalsIgnoreCase(str6)) {
/*  64 */         recordSet.executeSql("select * from cus_selectitem where fieldid='" + str1.replace("prjtype_", "") + "' and cancel='0' order by fieldorder ");
/*  65 */       } else if ("prjtsk".equalsIgnoreCase(str6)) {
/*  66 */         recordSet.executeProc("prjtsk_selectitembyid_new", "" + str1 + c + '\001');
/*     */       } 
/*     */       
/*  69 */       while (recordSet.next()) {
/*  70 */         String str12 = Util.null2String(recordSet.getString("selectvalue"));
/*  71 */         String str13 = Util.toScreen(recordSet.getString("selectname"), i);
/*  72 */         String str14 = Util.null2String(recordSet.getString("selectlabel"));
/*  73 */         if (!"".equals(str14)) {
/*  74 */           str13 = SystemEnv.getHtmlLabelNames(str14, i);
/*     */         }
/*  76 */         String str15 = "prjtype".equals(str6) ? Util.null2String(recordSet.getString("prj_isdefault")) : Util.null2String(recordSet.getString("isdefault"));
/*  77 */         String str16 = "";
/*  78 */         if ("".equals(paramString)) {
/*  79 */           if ("y".equals(str15)) {
/*  80 */             bool = false;
/*  81 */             str11 = str12;
/*  82 */             str16 = " selected ";
/*     */           }
/*     */         
/*  85 */         } else if (str12.equals(paramString)) {
/*  86 */           bool = false;
/*  87 */           str11 = str12;
/*  88 */           str16 = " selected ";
/*     */         } 
/*     */         
/*  91 */         str = str + "<option value=\"" + str12 + "\"" + str16 + ">" + str13 + "</option>\n";
/*     */       } 
/*     */       
/*  94 */       str = str + "</select>\n";
/*  95 */       str = str + "<span id=\"" + str5 + "span\">";
/*  96 */       if (bool == true) {
/*  97 */         str = str + str10;
/*     */       }
/*  99 */       str = str + "</span>\n";
/*     */     }
/* 101 */     catch (Exception exception) {
/* 102 */       exception.printStackTrace();
/* 103 */       writeLog(exception.getMessage());
/*     */     } 
/*     */ 
/*     */     
/* 107 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String getRealSelectFieldId4prj(String paramString) {
/* 116 */     RecordSet recordSet = new RecordSet();
/* 117 */     String str = "select id from prjDefineField where prjtype=-1 and fieldname in(select fieldname from prjDefineField where id='" + paramString + "')";
/* 118 */     recordSet.executeSql(str);
/* 119 */     recordSet.next();
/* 120 */     return Util.null2String(recordSet.getString(1));
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/cpt/util/html/SelectElement.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */