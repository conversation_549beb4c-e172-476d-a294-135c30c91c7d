/*    */ package weaver.cpt.util.html;
/*    */ 
/*    */ import org.json.JSONObject;
/*    */ import weaver.general.BaseBean;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.User;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ public class TextareaElement
/*    */   extends BaseBean
/*    */   implements HtmlElement
/*    */ {
/*    */   public String getHtmlElementString(String paramString, JSONObject paramJSONObject, User paramUser) {
/* 16 */     String str = "";
/* 17 */     if (paramJSONObject == null || paramUser == null) {
/* 18 */       return "";
/*    */     }
/*    */     try {
/* 21 */       int i = paramUser.getLanguage();
/* 22 */       String str1 = paramJSONObject.getString("id");
/* 23 */       String str2 = paramJSONObject.getString("fieldname");
/* 24 */       String str3 = paramJSONObject.getString("fieldlabel");
/* 25 */       String str4 = paramJSONObject.getString("fielddbtype");
/* 26 */       int j = paramJSONObject.getInt("ismand");
/* 27 */       int k = paramJSONObject.getInt("type");
/* 28 */       int m = paramJSONObject.getInt("issystem");
/* 29 */       String str5 = (1 == m) ? str2 : ("field" + str1);
/* 30 */       if (paramJSONObject.getString("fieldkind") != null && "2".equals(paramJSONObject.getString("fieldkind"))) {
/* 31 */         str5 = "customfield" + str1;
/*    */       }
/* 33 */       int n = Util.getIntValue(paramJSONObject.getString("textheight"), 4);
/* 34 */       char c = 'ὀ';
/*    */       
/* 36 */       byte b = 2;
/*    */       
/* 38 */       String str6 = "";
/* 39 */       String str7 = "";
/*    */       
/* 41 */       String str8 = "";
/* 42 */       if (j == 1 && "".equals(paramString)) {
/* 43 */         str8 = "<img src='/images/BacoError_wev8.gif' align='absmiddle'>";
/*    */       }
/*    */ 
/*    */       
/* 47 */       str = str + "<textarea class=\"InputStyle\" temptype=\"" + k + "\" viewtype=\"" + j + "\" temptitle=\"" + Util.toScreen(SystemEnv.getHtmlLabelNames(str3, i), i) + "\" id=\"" + str5 + "\" name=\"" + str5 + "\" rows=\"" + n + "\" ";
/* 48 */       str = str + " onchange=\"checkinput2('" + str5 + "','" + str5 + "span',this.getAttribute('viewtype'));checkLengthfortext('" + str5 + "','" + c + "','" + Util.toScreen(SystemEnv.getHtmlLabelNames(str3, i), i) + "','" + SystemEnv.getHtmlLabelName(20246, i) + "','" + SystemEnv.getHtmlLabelName(20247, i) + "')" + str6 + "\" cols=\"40\"";
/* 49 */       if (k == 2) {
/* 50 */         str = str + " style=\"width:90%;word-break:break-all;word-wrap:break-word\">";
/*    */       } else {
/* 52 */         str = str + " style=\"width:80%;word-break:break-all;word-wrap:break-word\">";
/*    */       } 
/* 54 */       if (k == 2) {
/* 55 */         str = str + Util.toHtmltextarea(Util.encodeAnd(paramString));
/*    */       } else {
/* 57 */         str = str + Util.toScreenToEdit(paramString, i);
/*    */       } 
/* 59 */       str = str + "</textarea>\n";
/* 60 */       str = str + "<span id=\"" + str5 + "span\">" + str8 + "</span>";
/* 61 */       if (k == 2) {
/* 62 */         int i1 = n * 10;
/* 63 */         if (i1 < 200) {
/* 64 */           i1 = 200;
/*    */         }
/* 66 */         str = str + "<script>\n";
/* 67 */         str = str + "function funcField" + str1 + "(){" + "\n";
/* 68 */         str = str + "\tFCKEditorExt.initEditor('frmain','" + str5 + "'," + i + ",FCKEditorExt.NO_IMAGE, " + i1 + ");" + "\n";
/* 69 */         str = str + "\tFCKEditorExt.checkText('" + str5 + "span','" + str5 + "');" + "\n";
/* 70 */         str = str + "\tFCKEditorExt.toolbarExpand(false,\"" + str5 + "\");" + "\n";
/* 71 */         str = str + "}\n";
/*    */ 
/*    */         
/* 74 */         str = str + "\tif (window.addEventListener){\n";
/* 75 */         str = str + "\t    window.addEventListener(\"load\", funcField" + str1 + ", false);" + "\n";
/* 76 */         str = str + "\t}else if (window.attachEvent){\n";
/* 77 */         str = str + "\t    window.attachEvent(\"onload\", funcField" + str1 + ");" + "\n";
/* 78 */         str = str + "\t}else{\n";
/* 79 */         str = str + "\t    window.onload=funcField" + str1 + ";" + "\n";
/* 80 */         str = str + "\t}\n";
/*    */         
/* 82 */         str = str + "</script>\n";
/*    */       }
/*    */     
/* 85 */     } catch (Exception exception) {
/* 86 */       exception.printStackTrace();
/* 87 */       writeLog(exception.getMessage());
/*    */     } 
/*    */ 
/*    */     
/* 91 */     return str;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/cpt/util/html/TextareaElement.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */