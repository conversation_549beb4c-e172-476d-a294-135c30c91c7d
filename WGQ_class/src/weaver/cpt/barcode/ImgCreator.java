/*    */ package weaver.cpt.barcode;
/*    */ 
/*    */ import java.awt.Graphics;
/*    */ import java.awt.Image;
/*    */ import java.awt.image.BufferedImage;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class ImgCreator
/*    */ {
/*    */   private Image img;
/*    */   public Graphics g;
/*    */   
/*    */   public Image getImage(int paramInt1, int paramInt2) {
/* 24 */     int i = (paramInt2 <= paramInt1) ? paramInt1 : paramInt2;
/* 25 */     this.img = new BufferedImage(i, i, 13);
/* 26 */     this.g = ((BufferedImage)this.img).createGraphics();
/* 27 */     return this.img;
/*    */   }
/*    */ 
/*    */   
/*    */   public Graphics getGraphics() {
/* 32 */     return this.g;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/cpt/barcode/ImgCreator.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */