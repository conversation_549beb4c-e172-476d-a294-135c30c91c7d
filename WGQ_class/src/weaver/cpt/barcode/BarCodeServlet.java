/*     */ package weaver.cpt.barcode;
/*     */ 
/*     */ import com.google.zxing.BarcodeFormat;
/*     */ import com.google.zxing.EncodeHintType;
/*     */ import com.google.zxing.MultiFormatWriter;
/*     */ import com.google.zxing.client.j2se.MatrixToImageWriter;
/*     */ import com.google.zxing.common.BitMatrix;
/*     */ import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;
/*     */ import java.awt.BasicStroke;
/*     */ import java.awt.Color;
/*     */ import java.awt.Font;
/*     */ import java.awt.Graphics2D;
/*     */ import java.awt.image.BufferedImage;
/*     */ import java.awt.image.ImageObserver;
/*     */ import java.io.BufferedOutputStream;
/*     */ import java.io.ByteArrayOutputStream;
/*     */ import java.io.File;
/*     */ import java.io.FileOutputStream;
/*     */ import java.io.IOException;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Hashtable;
/*     */ import java.util.StringTokenizer;
/*     */ import java.util.regex.Matcher;
/*     */ import java.util.regex.Pattern;
/*     */ import javax.imageio.ImageIO;
/*     */ import javax.servlet.ServletException;
/*     */ import javax.servlet.ServletOutputStream;
/*     */ import javax.servlet.http.HttpServlet;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.cpt.util.CptFieldComInfo;
/*     */ import weaver.cpt.util.CptSettingsComInfo;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.workflow.field.BrowserComInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class BarCodeServlet
/*     */   extends HttpServlet
/*     */ {
/*     */   public BarCode barcode;
/*     */   private static final String FORMAT = "jpeg";
/*     */   
/*     */   public void init() throws ServletException {}
/*     */   
/*     */   private BarCode getChart(HttpServletRequest paramHttpServletRequest) {
/*  60 */     if (this.barcode == null)
/*  61 */       this.barcode = new BarCode(); 
/*     */     try {
/*  63 */       setParameter("barType", paramHttpServletRequest.getParameter("barType"));
/*  64 */       setParameter("barType2", paramHttpServletRequest.getParameter("barType2"));
/*  65 */       if (paramHttpServletRequest.getParameter("width") != null && paramHttpServletRequest.getParameter("height") != null) {
/*  66 */         setParameter("width", paramHttpServletRequest.getParameter("width"));
/*  67 */         setParameter("width2", paramHttpServletRequest.getParameter("width2"));
/*  68 */         setParameter("height", paramHttpServletRequest.getParameter("height"));
/*  69 */         setParameter("height2", paramHttpServletRequest.getParameter("height2"));
/*  70 */         setParameter("autoSize", "n");
/*     */       } 
/*  72 */       setParameter("code", paramHttpServletRequest.getParameter("code"));
/*  73 */       setParameter("st", paramHttpServletRequest.getParameter("st"));
/*  74 */       setParameter("st2", paramHttpServletRequest.getParameter("st2"));
/*  75 */       setParameter("textFont", paramHttpServletRequest.getParameter("textFont"));
/*  76 */       setParameter("textFont2", paramHttpServletRequest.getParameter("textFont2"));
/*  77 */       setParameter("content2", paramHttpServletRequest.getParameter("content2"));
/*  78 */       setParameter("fontColor", paramHttpServletRequest.getParameter("fontColor"));
/*  79 */       setParameter("barColor", paramHttpServletRequest.getParameter("barColor"));
/*  80 */       setParameter("backColor", paramHttpServletRequest.getParameter("backColor"));
/*  81 */       setParameter("rotate", paramHttpServletRequest.getParameter("rotate"));
/*  82 */       setParameter("barHeightCM", paramHttpServletRequest.getParameter("barHeightCM"));
/*  83 */       setParameter("x", paramHttpServletRequest.getParameter("x"));
/*  84 */       setParameter("n", paramHttpServletRequest.getParameter("n"));
/*  85 */       setParameter("leftMarginCM", paramHttpServletRequest.getParameter("leftMarginCM"));
/*  86 */       setParameter("topMarginCM", paramHttpServletRequest.getParameter("topMarginCM"));
/*  87 */       setParameter("checkCharacter", paramHttpServletRequest.getParameter("checkCharacter"));
/*  88 */       setParameter("checkCharacterInText", paramHttpServletRequest.getParameter("checkCharacterInText"));
/*  89 */       setParameter("Code128Set", paramHttpServletRequest.getParameter("Code128Set"));
/*  90 */       setParameter("UPCESytem", paramHttpServletRequest.getParameter("UPCESytem"));
/*     */     }
/*  92 */     catch (Exception exception) {
/*  93 */       exception.printStackTrace();
/*  94 */       this.barcode.code = "Parameter Error";
/*     */     } 
/*  96 */     return this.barcode;
/*     */   }
/*     */   
/*     */   public void setParameter(String paramString1, String paramString2) {
/* 100 */     if (paramString2 != null)
/* 101 */       if (paramString1.equals("code")) {
/* 102 */         this.barcode.code = paramString2;
/* 103 */       } else if (paramString1.equals("width")) {
/* 104 */         this.barcode.width = (new Integer(paramString2)).intValue();
/* 105 */       } else if (paramString1.equals("height")) {
/* 106 */         this.barcode.height = (new Integer(paramString2)).intValue();
/* 107 */       } else if (paramString1.equals("autoSize")) {
/* 108 */         this.barcode.autoSize = paramString2.equalsIgnoreCase("y");
/* 109 */       } else if (paramString1.equals("st")) {
/* 110 */         this.barcode.showText = paramString2.equalsIgnoreCase("y");
/* 111 */       } else if (paramString1.equals("textFont")) {
/* 112 */         this.barcode.textFont = convertFont(paramString2);
/* 113 */       } else if (paramString1.equals("fontColor")) {
/* 114 */         this.barcode.fontColor = convertColor(paramString2);
/* 115 */       } else if (paramString1.equals("barColor")) {
/* 116 */         this.barcode.barColor = convertColor(paramString2);
/* 117 */       } else if (paramString1.equals("backColor")) {
/* 118 */         this.barcode.backColor = convertColor(paramString2);
/* 119 */       } else if (paramString1.equals("rotate")) {
/* 120 */         this.barcode.rotate = (new Integer(paramString2)).intValue();
/* 121 */       } else if (paramString1.equals("barHeightCM")) {
/* 122 */         this.barcode.barHeightCM = (new Double(paramString2)).doubleValue();
/* 123 */       } else if (paramString1.equals("x")) {
/* 124 */         this.barcode.X = (new Double(paramString2)).doubleValue();
/* 125 */       } else if (paramString1.equals("n")) {
/* 126 */         this.barcode.N = (new Double(paramString2)).doubleValue();
/* 127 */       } else if (paramString1.equals("leftMarginCM")) {
/* 128 */         this.barcode.leftMarginCM = (new Double(paramString2)).doubleValue();
/* 129 */       } else if (paramString1.equals("topMarginCM")) {
/* 130 */         this.barcode.topMarginCM = (new Double(paramString2)).doubleValue();
/* 131 */       } else if (paramString1.equals("checkCharacter")) {
/* 132 */         this.barcode.checkCharacter = paramString2.equalsIgnoreCase("y");
/* 133 */       } else if (paramString1.equals("checkCharacterInText")) {
/* 134 */         this.barcode.checkCharacterInText = paramString2.equalsIgnoreCase("y");
/* 135 */       } else if (paramString1.equals("Code128Set")) {
/* 136 */         this.barcode.Code128Set = paramString2.charAt(0);
/* 137 */       } else if (paramString1.equals("UPCESytem")) {
/* 138 */         this.barcode.UPCESytem = paramString2.charAt(0);
/* 139 */       } else if (paramString1.equals("barType")) {
/* 140 */         if (paramString2.equalsIgnoreCase("CODE39")) {
/* 141 */           this.barcode.barType = 0;
/* 142 */         } else if (paramString2.equalsIgnoreCase("CODE39EXT")) {
/* 143 */           this.barcode.barType = 1;
/* 144 */         } else if (paramString2.equalsIgnoreCase("INTERLEAVED25")) {
/* 145 */           this.barcode.barType = 2;
/* 146 */         } else if (paramString2.equalsIgnoreCase("CODE11")) {
/* 147 */           this.barcode.barType = 3;
/* 148 */         } else if (paramString2.equalsIgnoreCase("CODABAR")) {
/* 149 */           this.barcode.barType = 4;
/* 150 */         } else if (paramString2.equalsIgnoreCase("MSI")) {
/* 151 */           this.barcode.barType = 5;
/* 152 */         } else if (paramString2.equalsIgnoreCase("UPCA")) {
/* 153 */           this.barcode.barType = 6;
/* 154 */         } else if (paramString2.equalsIgnoreCase("IND25")) {
/* 155 */           this.barcode.barType = 7;
/* 156 */         } else if (paramString2.equalsIgnoreCase("MAT25")) {
/* 157 */           this.barcode.barType = 8;
/* 158 */         } else if (paramString2.equalsIgnoreCase("CODE93")) {
/* 159 */           this.barcode.barType = 9;
/* 160 */         } else if (paramString2.equalsIgnoreCase("EAN13")) {
/* 161 */           this.barcode.barType = 10;
/* 162 */         } else if (paramString2.equalsIgnoreCase("EAN8")) {
/* 163 */           this.barcode.barType = 11;
/* 164 */         } else if (paramString2.equalsIgnoreCase("UPCE")) {
/* 165 */           this.barcode.barType = 12;
/* 166 */         } else if (paramString2.equalsIgnoreCase("CODE128")) {
/* 167 */           this.barcode.barType = 13;
/* 168 */         } else if (paramString2.equalsIgnoreCase("CODE93EXT")) {
/* 169 */           this.barcode.barType = 14;
/* 170 */         } else if (paramString2.equalsIgnoreCase("POSTNET")) {
/* 171 */           this.barcode.barType = 15;
/* 172 */         } else if (paramString2.equalsIgnoreCase("PLANET")) {
/* 173 */           this.barcode.barType = 16;
/* 174 */         } else if (paramString2.equalsIgnoreCase("UCC128")) {
/* 175 */           this.barcode.barType = 17;
/*     */         } else {
/* 177 */           this.barcode.barType = -1;
/* 178 */           this.barcode.barTypeStr = paramString2;
/*     */         } 
/*     */       }  
/*     */   }
/*     */   
/*     */   public synchronized void doGet(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws ServletException, IOException {
/* 184 */     paramHttpServletResponse.setContentType("image/jpeg");
/* 185 */     ServletOutputStream servletOutputStream = paramHttpServletResponse.getOutputStream();
/* 186 */     paramHttpServletResponse.setHeader("Pragma", "no-cache");
/* 187 */     paramHttpServletResponse.setHeader("Cache-Control", "no-cache");
/* 188 */     paramHttpServletResponse.setDateHeader("Expires", 0L);
/* 189 */     BufferedImage bufferedImage = null;
/* 190 */     String str = Util.null2String(paramHttpServletRequest.getParameter("capitalid"));
/* 191 */     User user = new User();
/* 192 */     if (paramHttpServletRequest.getSession() != null) {
/* 193 */       user = (User)paramHttpServletRequest.getSession().getAttribute("weaver_user@bean");
/*     */     }
/*     */     try {
/* 196 */       BarCode barCode = getChart(paramHttpServletRequest);
/* 197 */       barCode.setSize(barCode.width, barCode.height);
/* 198 */       Object object = null;
/* 199 */       if (barCode.barType == -1) {
/*     */         
/* 201 */         CptSettingsComInfo cptSettingsComInfo = new CptSettingsComInfo();
/* 202 */         cptSettingsComInfo.setTofirstRow();
/* 203 */         cptSettingsComInfo.next();
/* 204 */         String str1 = cptSettingsComInfo.getIsopen2();
/* 205 */         String str2 = cptSettingsComInfo.getContent2type();
/* 206 */         String str3 = cptSettingsComInfo.getContent2();
/* 207 */         String str4 = cptSettingsComInfo.getLink2();
/* 208 */         String str5 = "";
/* 209 */         String str6 = getServletContext().getRealPath("/");
/* 210 */         String str7 = getServletInfo();
/* 211 */         String str8 = paramHttpServletRequest.getServerName();
/* 212 */         int i = paramHttpServletRequest.getServerPort();
/* 213 */         if ("1".equals(str1)) {
/* 214 */           if ("link".equals(str2)) {
/* 215 */             if (str4.indexOf("http://") == -1 && str4.indexOf("https://") == -1) {
/* 216 */               str4 = paramHttpServletRequest.getScheme() + "://" + str8 + ":" + i + "" + str4;
/*     */             }
/* 218 */             String str9 = "?";
/* 219 */             if (str4.indexOf("?") > -1) {
/* 220 */               str9 = "&";
/*     */             } else {
/* 222 */               str9 = "?";
/*     */             } 
/* 224 */             str4 = str4 + str9 + "id=" + str + "&capitalid=" + str;
/* 225 */             if (Util.getIntValue(str) > 0) {
/* 226 */               str4 = getContents2(str, str4, user);
/*     */             }
/* 228 */             str5 = str4;
/*     */           } else {
/* 230 */             if (Util.getIntValue(str) > 0) {
/* 231 */               str3 = getContents2(str, str3, user);
/*     */             }
/*     */             
/* 234 */             str5 = str3;
/*     */           } 
/*     */         }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */         
/* 242 */         BarcodeFormat barcodeFormat = BarcodeFormat.QR_CODE;
/* 243 */         if ("QR_CODE".equalsIgnoreCase(barCode.barTypeStr)) {
/* 244 */           barcodeFormat = BarcodeFormat.QR_CODE;
/* 245 */         } else if ("PDF_417".equalsIgnoreCase(barCode.barTypeStr)) {
/* 246 */           barcodeFormat = BarcodeFormat.PDF_417;
/* 247 */         } else if ("ITF".equalsIgnoreCase(barCode.barTypeStr)) {
/* 248 */           barcodeFormat = BarcodeFormat.ITF;
/*     */         } 
/* 250 */         int j = Util.getIntValue("" + cptSettingsComInfo.getWidth2(), 100);
/* 251 */         int k = Util.getIntValue("" + cptSettingsComInfo.getHeight2(), 100);
/*     */ 
/*     */ 
/*     */ 
/*     */         
/* 256 */         int m = Util.getIntValue("" + barCode.leftMarginCM, 0);
/* 257 */         str5 = new String(str5.getBytes("UTF-8"), "ISO-8859-1");
/*     */         
/* 259 */         Hashtable<Object, Object> hashtable = new Hashtable<>();
/* 260 */         hashtable.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.H);
/* 261 */         hashtable.put(EncodeHintType.MARGIN, Integer.valueOf(m));
/*     */ 
/*     */         
/* 264 */         BitMatrix bitMatrix = (new MultiFormatWriter()).encode(str5, barcodeFormat, j, k, hashtable);
/*     */         
/* 266 */         bitMatrix = deleteWhite(bitMatrix);
/*     */         
/* 268 */         File file = new File(str6 + "tmpqrcodeimg.jpg");
/* 269 */         if (!file.exists()) {
/* 270 */           file.createNewFile();
/*     */         }
/* 272 */         MatrixToImageWriter.writeToFile(bitMatrix, "JPEG", file);
/*     */         
/* 274 */         bufferedImage = ImageIO.read(file);
/* 275 */         BufferedImage bufferedImage1 = new BufferedImage(j, k, 1);
/* 276 */         bufferedImage1.getGraphics().drawImage(bufferedImage, 0, 0, j, k, null);
/* 277 */         BufferedOutputStream bufferedOutputStream = new BufferedOutputStream(new FileOutputStream(str6 + "tmpqrcodeimg.jpg"));
/* 278 */         ImageIO.write(bufferedImage1, "JPEG", bufferedOutputStream);
/* 279 */         bufferedOutputStream.close();
/* 280 */         file = new File(str6 + "tmpqrcodeimg.jpg");
/* 281 */         if (!file.exists()) {
/* 282 */           file.createNewFile();
/*     */         }
/*     */         
/* 285 */         bufferedImage = ImageIO.read(file);
/*     */         
/* 287 */         if ("y".equals(cptSettingsComInfo.getSt2())) {
/*     */           
/* 289 */           String str9 = cptSettingsComInfo.getTextFont2();
/* 290 */           File file1 = new File(str6 + str9);
/* 291 */           if (!file1.exists()) {
/* 292 */             file1 = new File(str9);
/* 293 */             if (!file1.exists()) {
/* 294 */               file1 = new File(str6 + "js/tabs/images/nav/mnav6_wev8.png");
/*     */             }
/*     */           } 
/*     */           
/* 298 */           BufferedImage bufferedImage2 = ImageIO.read(file1);
/* 299 */           Graphics2D graphics2D = bufferedImage.createGraphics();
/*     */           
/* 301 */           int n = (bufferedImage2.getWidth((ImageObserver)null) > bufferedImage.getWidth() * 2 / 10) ? (bufferedImage.getWidth() * 2 / 10) : bufferedImage2.getWidth((ImageObserver)null);
/* 302 */           int i1 = (bufferedImage2.getHeight((ImageObserver)null) > bufferedImage.getHeight() * 2 / 10) ? (bufferedImage.getHeight() * 2 / 10) : bufferedImage2.getHeight((ImageObserver)null);
/*     */ 
/*     */           
/* 305 */           int i2 = (bufferedImage.getWidth() - n) / 2;
/* 306 */           int i3 = (bufferedImage.getHeight() - i1) / 2;
/*     */           
/* 308 */           graphics2D.drawImage(bufferedImage2, i2, i3, n, i1, null);
/* 309 */           graphics2D.drawRoundRect(i2, i3, n, i1, 15, 15);
/*     */           
/* 311 */           graphics2D.setStroke(new BasicStroke(2.0F));
/*     */           
/* 313 */           graphics2D.setColor(Color.WHITE);
/* 314 */           graphics2D.drawRect(i2, i3, n, i1);
/* 315 */           graphics2D.dispose();
/* 316 */           bufferedImage2.flush();
/* 317 */           bufferedImage.flush();
/*     */         } 
/*     */ 
/*     */ 
/*     */         
/* 322 */         ImageIO.write(bufferedImage, "JPEG", file);
/*     */       } else {
/*     */         
/* 325 */         if (barCode.autoSize) {
/* 326 */           BufferedImage bufferedImage1 = new BufferedImage((barCode.getSize()).width, (barCode.getSize()).height, 13);
/* 327 */           Graphics2D graphics2D1 = bufferedImage1.createGraphics();
/* 328 */           barCode.paint(graphics2D1);
/* 329 */           barCode.invalidate();
/* 330 */           graphics2D1.dispose();
/*     */         } 
/* 332 */         bufferedImage = new BufferedImage((barCode.getSize()).width, (barCode.getSize()).height, 1);
/* 333 */         Graphics2D graphics2D = bufferedImage.createGraphics();
/* 334 */         barCode.paint(graphics2D);
/*     */       } 
/*     */ 
/*     */ 
/*     */       
/* 339 */       ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
/* 340 */       ImageIO.write(bufferedImage, "jpeg", byteArrayOutputStream);
/* 341 */       paramHttpServletResponse.setContentType("image/jpeg;charset=UTF-8");
/* 342 */       paramHttpServletResponse.setHeader("Cache-Control", "no-store");
/* 343 */       paramHttpServletResponse.setDateHeader("Expires", 0L);
/* 344 */       paramHttpServletResponse.setContentLength(byteArrayOutputStream.size());
/* 345 */       paramHttpServletResponse.getOutputStream().write(byteArrayOutputStream.toByteArray());
/* 346 */       paramHttpServletResponse.getOutputStream().flush();
/* 347 */       paramHttpServletResponse.getOutputStream().close();
/*     */     }
/* 349 */     catch (Exception exception) {
/* 350 */       exception.printStackTrace();
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   public void doPost(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws ServletException {
/*     */     try {
/* 357 */       doGet(paramHttpServletRequest, paramHttpServletResponse);
/* 358 */     } catch (Exception exception) {
/* 359 */       exception.printStackTrace();
/*     */     } 
/*     */   }
/*     */   
/*     */   private Font convertFont(String paramString) {
/* 364 */     StringTokenizer stringTokenizer = new StringTokenizer(paramString, "|");
/* 365 */     String str1 = stringTokenizer.nextToken();
/* 366 */     String str2 = stringTokenizer.nextToken();
/* 367 */     String str3 = stringTokenizer.nextToken();
/* 368 */     byte b = -1;
/* 369 */     if (str2.trim().toUpperCase().equals("PLAIN")) {
/* 370 */       b = 0;
/* 371 */     } else if (str2.trim().toUpperCase().equals("BOLD")) {
/* 372 */       b = 1;
/* 373 */     } else if (str2.trim().toUpperCase().equals("ITALIC")) {
/* 374 */       b = 2;
/* 375 */     }  return new Font(str1, b, (new Integer(str3)).intValue());
/*     */   }
/*     */   
/*     */   private Color convertColor(String paramString) {
/* 379 */     Color color = null;
/* 380 */     if (paramString.trim().toUpperCase().equals("RED")) {
/* 381 */       color = Color.red;
/* 382 */     } else if (paramString.trim().toUpperCase().equals("BLACK")) {
/* 383 */       color = Color.black;
/* 384 */     } else if (paramString.trim().toUpperCase().equals("BLUE")) {
/* 385 */       color = Color.blue;
/* 386 */     } else if (paramString.trim().toUpperCase().equals("CYAN")) {
/* 387 */       color = Color.cyan;
/* 388 */     } else if (paramString.trim().toUpperCase().equals("DARKGRAY")) {
/* 389 */       color = Color.darkGray;
/* 390 */     } else if (paramString.trim().toUpperCase().equals("GRAY")) {
/* 391 */       color = Color.gray;
/* 392 */     } else if (paramString.trim().toUpperCase().equals("GREEN")) {
/* 393 */       color = Color.green;
/* 394 */     } else if (paramString.trim().toUpperCase().equals("LIGHTGRAY")) {
/* 395 */       color = Color.lightGray;
/* 396 */     } else if (paramString.trim().toUpperCase().equals("MAGENTA")) {
/* 397 */       color = Color.magenta;
/* 398 */     } else if (paramString.trim().toUpperCase().equals("ORANGE")) {
/* 399 */       color = Color.orange;
/* 400 */     } else if (paramString.trim().toUpperCase().equals("PINK")) {
/* 401 */       color = Color.pink;
/* 402 */     } else if (paramString.trim().toUpperCase().equals("WHITE")) {
/* 403 */       color = Color.white;
/* 404 */     } else if (paramString.trim().toUpperCase().equals("YELLOW")) {
/* 405 */       color = Color.yellow;
/* 406 */     }  return color;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String getContents2(String paramString1, String paramString2, User paramUser) {
/* 416 */     CptFieldComInfo cptFieldComInfo = new CptFieldComInfo();
/* 417 */     BrowserComInfo browserComInfo = new BrowserComInfo();
/* 418 */     ArrayList<String> arrayList1 = cptFieldComInfo.getField_names();
/* 419 */     arrayList1.add("id");
/* 420 */     ArrayList<String> arrayList2 = cptFieldComInfo.getField_htmltypes();
/* 421 */     arrayList2.add("1");
/* 422 */     ArrayList<String> arrayList3 = cptFieldComInfo.getField_types();
/* 423 */     arrayList3.add("1");
/* 424 */     ArrayList<String> arrayList4 = cptFieldComInfo.getField_ids();
/* 425 */     arrayList4.add("-1");
/*     */     
/* 427 */     RecordSet recordSet1 = new RecordSet();
/* 428 */     RecordSet recordSet2 = new RecordSet();
/* 429 */     recordSet1.executeSql("select * from cptcapital where id=" + paramString1);
/* 430 */     if (recordSet1.next()) {
/* 431 */       Pattern pattern = Pattern.compile("#<\\D[_0-9a-zA-Z]{0,}>");
/* 432 */       Matcher matcher = pattern.matcher(paramString2);
/* 433 */       while (matcher.find()) {
/* 434 */         String str = matcher.group();
/* 435 */         if (str != null && str.length() > 3) {
/* 436 */           String str1 = str.substring(2, str.length() - 1);
/* 437 */           int i = arrayList1.indexOf(str1);
/* 438 */           if (i > -1) {
/* 439 */             int j = Util.getIntValue(arrayList2.get(i), 1);
/* 440 */             int k = Util.getIntValue(arrayList3.get(i), 0);
/* 441 */             int m = Util.getIntValue(arrayList4.get(i), 0);
/* 442 */             String str2 = recordSet1.getString("cptcapital", str1, true, true);
/* 443 */             ArrayList<E> arrayList = Util.TokenizerString(str2, ",");
/*     */             
/* 445 */             String str3 = "";
/* 446 */             if (j == 3) {
/* 447 */               if (k == 2 || k == 19 || k == 226 || k == 227) {
/* 448 */                 str3 = str2;
/*     */               } else {
/*     */                 
/* 451 */                 String str4 = browserComInfo.getBrowsertablename(k + "");
/* 452 */                 String str5 = browserComInfo.getBrowsercolumname(k + "");
/* 453 */                 String str6 = browserComInfo.getBrowserkeycolumname(k + "");
/* 454 */                 for (byte b = 0; b < arrayList.size(); b++) {
/* 455 */                   int n = Util.getIntValue(arrayList.get(b).toString(), 0);
/* 456 */                   String str7 = "select " + str5 + " from " + str4 + " where " + str6 + "=" + n;
/* 457 */                   recordSet2.executeSql(str7);
/* 458 */                   recordSet2.next();
/* 459 */                   String str8 = recordSet2.getString(1);
/* 460 */                   str3 = str3 + str8 + ",";
/*     */                 } 
/*     */               } 
/* 463 */             } else if (j == 4) {
/* 464 */               if ("1".equals(str2)) {
/* 465 */                 str3 = SystemEnv.getHtmlLabelName(163, paramUser.getLanguage());
/*     */               } else {
/* 467 */                 str3 = SystemEnv.getHtmlLabelName(161, paramUser.getLanguage());
/*     */               } 
/* 469 */             } else if (j == 5) {
/*     */ 
/*     */               
/* 472 */               String str4 = "";
/* 473 */               String str5 = "";
/* 474 */               recordSet2.executeQuery("select iscommon,cid from cptDefineField where fieldname=?", new Object[] { str1 });
/* 475 */               if (recordSet2.next()) {
/* 476 */                 str4 = Util.null2String(recordSet2.getString("iscommon"));
/* 477 */                 str5 = Util.null2String(recordSet2.getString("cid"));
/*     */               } 
/* 479 */               if (str4.equals("1")) {
/* 480 */                 byte b = 0;
/* 481 */                 recordSet1.executeQuery("select name from mode_selectitempagedetail where statelev =1 and cancel <> 1 and mainid=? order by id asc", new Object[] { str5 });
/* 482 */                 while (recordSet1.next()) {
/* 483 */                   if (str2.equalsIgnoreCase(b + "")) {
/* 484 */                     str3 = Util.null2String(recordSet1.getString("name"));
/*     */                     break;
/*     */                   } 
/* 487 */                   b++;
/*     */                 } 
/*     */               } else {
/*     */                 
/* 491 */                 recordSet2.executeSql("select selectvalue,selectname from cpt_SelectItem where fieldid = " + m + "  order by listorder,id");
/*     */                 
/* 493 */                 while (recordSet2.next()) {
/* 494 */                   String str6 = Util.null2String(recordSet2
/* 495 */                       .getString("selectvalue"));
/* 496 */                   String str7 = Util.toScreen(recordSet2
/* 497 */                       .getString("selectname"), paramUser.getLanguage());
/* 498 */                   if (str6.equals(str2)) {
/* 499 */                     str3 = str3 + str7;
/*     */                   }
/*     */                 } 
/*     */               } 
/*     */             } else {
/* 504 */               str3 = str2;
/*     */             } 
/* 506 */             if (str3.endsWith(",")) {
/* 507 */               str3 = str3.substring(0, str3.length() - 1);
/*     */             }
/* 509 */             paramString2 = paramString2.replaceFirst(str, str3);
/*     */           } 
/*     */         } 
/*     */       } 
/*     */     } 
/*     */ 
/*     */     
/* 516 */     return paramString2;
/*     */   }
/*     */   
/*     */   private static BitMatrix deleteWhite(BitMatrix paramBitMatrix) {
/* 520 */     int[] arrayOfInt = paramBitMatrix.getEnclosingRectangle();
/* 521 */     int i = arrayOfInt[2] + 1;
/* 522 */     int j = arrayOfInt[3] + 1;
/*     */     
/* 524 */     BitMatrix bitMatrix = new BitMatrix(i, j);
/* 525 */     bitMatrix.clear();
/* 526 */     for (byte b = 0; b < i; b++) {
/* 527 */       for (byte b1 = 0; b1 < j; b1++) {
/* 528 */         if (paramBitMatrix.get(b + arrayOfInt[0], b1 + arrayOfInt[1]))
/* 529 */           bitMatrix.set(b, b1); 
/*     */       } 
/*     */     } 
/* 532 */     return bitMatrix;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/cpt/barcode/BarCodeServlet.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */