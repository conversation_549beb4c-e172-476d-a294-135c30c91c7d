/*     */ package weaver.cpt.barcode;
/*     */ 
/*     */ import java.applet.Applet;
/*     */ import java.awt.BorderLayout;
/*     */ import java.awt.Color;
/*     */ import java.awt.Font;
/*     */ import java.util.StringTokenizer;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class BarCodeApplet
/*     */   extends Applet
/*     */ {
/*     */   public BarCode barcode;
/*     */   
/*     */   public BarCodeApplet() {
/*  20 */     this.barcode = null;
/*  21 */     setLayout(new BorderLayout());
/*     */   }
/*     */   
/*     */   public void start() {
/*  25 */     this.barcode.autoSize = false;
/*  26 */     this.barcode.paint(getGraphics());
/*     */   }
/*     */   
/*     */   public void refresh() {
/*  30 */     this.barcode.paint(this.barcode.getGraphics());
/*  31 */     paintAll(getGraphics());
/*     */   }
/*     */   
/*     */   public void init() {
/*  35 */     if (this.barcode == null)
/*  36 */       this.barcode = new BarCode(); 
/*  37 */     add("Center", this.barcode);
/*  38 */     initParameter();
/*     */   }
/*     */   
/*     */   private void initParameter() {
/*     */     try {
/*  43 */       setParameter("barType", getParameter("barType"));
/*  44 */       setParameter("code", getParameter("barcode"));
/*  45 */       setParameter("st", getParameter("st"));
/*  46 */       setParameter("textFont", getParameter("textFont"));
/*  47 */       setParameter("fontColor", getParameter("fontColor"));
/*  48 */       setParameter("barColor", getParameter("barColor"));
/*  49 */       setParameter("backColor", getParameter("backColor"));
/*  50 */       setParameter("rotate", getParameter("rotate"));
/*  51 */       setParameter("barHeightCM", getParameter("barHeightCM"));
/*  52 */       setParameter("x", getParameter("x"));
/*  53 */       setParameter("n", getParameter("n"));
/*  54 */       setParameter("leftMarginCM", getParameter("leftMarginCM"));
/*  55 */       setParameter("topMarginCM", getParameter("topMarginCM"));
/*  56 */       setParameter("checkCharacter", getParameter("checkCharacter"));
/*  57 */       setParameter("checkCharacterInText", getParameter("checkCharacterInText"));
/*  58 */       setParameter("Code128Set", getParameter("Code128Set"));
/*  59 */       setParameter("UPCESytem", getParameter("UPCESytem"));
/*  60 */     } catch (Exception exception) {
/*  61 */       exception.printStackTrace();
/*  62 */       this.barcode.code = "Parameter Error";
/*     */     } 
/*     */   }
/*     */   
/*     */   public void setParameter(String paramString1, String paramString2) {
/*  67 */     if (paramString2 != null)
/*  68 */       if (paramString1.equals("code")) {
/*  69 */         this.barcode.code = paramString2;
/*  70 */       } else if (paramString1.equals("st")) {
/*  71 */         this.barcode.showText = paramString2.equalsIgnoreCase("y");
/*  72 */       } else if (paramString1.equals("textFont")) {
/*  73 */         this.barcode.textFont = convertFont(paramString2);
/*  74 */       } else if (paramString1.equals("fontColor")) {
/*  75 */         this.barcode.fontColor = convertColor(paramString2);
/*  76 */       } else if (paramString1.equals("barColor")) {
/*  77 */         this.barcode.barColor = convertColor(paramString2);
/*  78 */       } else if (paramString1.equals("backColor")) {
/*  79 */         this.barcode.backColor = convertColor(paramString2);
/*  80 */       } else if (paramString1.equals("rotate")) {
/*  81 */         this.barcode.rotate = (new Integer(paramString2)).intValue();
/*  82 */       } else if (paramString1.equals("barHeightCM")) {
/*  83 */         this.barcode.barHeightCM = (new Double(paramString2)).doubleValue();
/*  84 */       } else if (paramString1.equals("x")) {
/*  85 */         this.barcode.X = (new Double(paramString2)).doubleValue();
/*  86 */       } else if (paramString1.equals("n")) {
/*  87 */         this.barcode.N = (new Double(paramString2)).doubleValue();
/*  88 */       } else if (paramString1.equals("leftMarginCM")) {
/*  89 */         this.barcode.leftMarginCM = (new Double(paramString2)).doubleValue();
/*  90 */       } else if (paramString1.equals("topMarginCM")) {
/*  91 */         this.barcode.topMarginCM = (new Double(paramString2)).doubleValue();
/*  92 */       } else if (paramString1.equals("checkCharacter")) {
/*  93 */         this.barcode.checkCharacter = paramString2.equalsIgnoreCase("y");
/*  94 */       } else if (paramString1.equals("checkCharacterInText")) {
/*  95 */         this.barcode.checkCharacterInText = paramString2.equalsIgnoreCase("y");
/*  96 */       } else if (paramString1.equals("Code128Set")) {
/*  97 */         this.barcode.Code128Set = paramString2.charAt(0);
/*  98 */       } else if (paramString1.equals("UPCESytem")) {
/*  99 */         this.barcode.UPCESytem = paramString2.charAt(0);
/* 100 */       } else if (paramString1.equals("barType")) {
/* 101 */         if (paramString2.equalsIgnoreCase("CODE39")) {
/* 102 */           this.barcode.barType = 0;
/* 103 */         } else if (paramString2.equalsIgnoreCase("CODE39EXT")) {
/* 104 */           this.barcode.barType = 1;
/* 105 */         } else if (paramString2.equalsIgnoreCase("INTERLEAVED25")) {
/* 106 */           this.barcode.barType = 2;
/* 107 */         } else if (paramString2.equalsIgnoreCase("CODE11")) {
/* 108 */           this.barcode.barType = 3;
/* 109 */         } else if (paramString2.equalsIgnoreCase("CODABAR")) {
/* 110 */           this.barcode.barType = 4;
/* 111 */         } else if (paramString2.equalsIgnoreCase("MSI")) {
/* 112 */           this.barcode.barType = 5;
/* 113 */         } else if (paramString2.equalsIgnoreCase("UPCA")) {
/* 114 */           this.barcode.barType = 6;
/* 115 */         } else if (paramString2.equalsIgnoreCase("IND25")) {
/* 116 */           this.barcode.barType = 7;
/* 117 */         } else if (paramString2.equalsIgnoreCase("MAT25")) {
/* 118 */           this.barcode.barType = 8;
/* 119 */         } else if (paramString2.equalsIgnoreCase("CODE93")) {
/* 120 */           this.barcode.barType = 9;
/* 121 */         } else if (paramString2.equalsIgnoreCase("EAN13")) {
/* 122 */           this.barcode.barType = 10;
/* 123 */         } else if (paramString2.equalsIgnoreCase("EAN8")) {
/* 124 */           this.barcode.barType = 11;
/* 125 */         } else if (paramString2.equalsIgnoreCase("UPCE")) {
/* 126 */           this.barcode.barType = 12;
/* 127 */         } else if (paramString2.equalsIgnoreCase("CODE128")) {
/* 128 */           this.barcode.barType = 13;
/* 129 */         } else if (paramString2.equalsIgnoreCase("CODE93EXT")) {
/* 130 */           this.barcode.barType = 14;
/* 131 */         } else if (paramString2.equalsIgnoreCase("POSTNET")) {
/* 132 */           this.barcode.barType = 15;
/* 133 */         } else if (paramString2.equalsIgnoreCase("PLANET")) {
/* 134 */           this.barcode.barType = 16;
/* 135 */         } else if (paramString2.equalsIgnoreCase("UCC128")) {
/* 136 */           this.barcode.barType = 17;
/*     */         } 
/*     */       }  
/*     */   } private Font convertFont(String paramString) {
/* 140 */     StringTokenizer stringTokenizer = new StringTokenizer(paramString, "|");
/* 141 */     String str1 = stringTokenizer.nextToken();
/* 142 */     String str2 = stringTokenizer.nextToken();
/* 143 */     String str3 = stringTokenizer.nextToken();
/* 144 */     byte b = -1;
/* 145 */     if (str2.trim().toUpperCase().equals("PLAIN")) {
/* 146 */       b = 0;
/* 147 */     } else if (str2.trim().toUpperCase().equals("BOLD")) {
/* 148 */       b = 1;
/* 149 */     } else if (str2.trim().toUpperCase().equals("ITALIC")) {
/* 150 */       b = 2;
/* 151 */     }  return new Font(str1, b, (new Integer(str3)).intValue());
/*     */   }
/*     */   
/*     */   private Color convertColor(String paramString) {
/* 155 */     Color color = null;
/* 156 */     if (paramString.trim().toUpperCase().equals("RED")) {
/* 157 */       color = Color.red;
/* 158 */     } else if (paramString.trim().toUpperCase().equals("BLACK")) {
/* 159 */       color = Color.black;
/* 160 */     } else if (paramString.trim().toUpperCase().equals("BLUE")) {
/* 161 */       color = Color.blue;
/* 162 */     } else if (paramString.trim().toUpperCase().equals("CYAN")) {
/* 163 */       color = Color.cyan;
/* 164 */     } else if (paramString.trim().toUpperCase().equals("DARKGRAY")) {
/* 165 */       color = Color.darkGray;
/* 166 */     } else if (paramString.trim().toUpperCase().equals("GRAY")) {
/* 167 */       color = Color.gray;
/* 168 */     } else if (paramString.trim().toUpperCase().equals("GREEN")) {
/* 169 */       color = Color.green;
/* 170 */     } else if (paramString.trim().toUpperCase().equals("LIGHTGRAY")) {
/* 171 */       color = Color.lightGray;
/* 172 */     } else if (paramString.trim().toUpperCase().equals("MAGENTA")) {
/* 173 */       color = Color.magenta;
/* 174 */     } else if (paramString.trim().toUpperCase().equals("ORANGE")) {
/* 175 */       color = Color.orange;
/* 176 */     } else if (paramString.trim().toUpperCase().equals("PINK")) {
/* 177 */       color = Color.pink;
/* 178 */     } else if (paramString.trim().toUpperCase().equals("WHITE")) {
/* 179 */       color = Color.white;
/* 180 */     } else if (paramString.trim().toUpperCase().equals("YELLOW")) {
/* 181 */       color = Color.yellow;
/* 182 */     }  return color;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/cpt/barcode/BarCodeApplet.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */