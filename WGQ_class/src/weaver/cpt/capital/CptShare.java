/*     */ package weaver.cpt.capital;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.cpt.util.DBUtil;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.company.DepartmentComInfo;
/*     */ import weaver.hrm.resource.AllSubordinate;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CptShare
/*     */   extends BaseBean
/*     */ {
/*     */   private AllSubordinate allsubordinates;
/*     */   private ResourceComInfo resourcecominfo;
/*     */   private DepartmentComInfo departmentcominfo;
/*  20 */   char flag = Util.getSeparator();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setCptShare(String paramString1, String paramString2, String paramString3, int paramInt) {}
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setCptShareByCpt(String paramString) throws Exception {
/*  38 */     if (Util.getIntValue(paramString, 0) <= 0) {
/*     */       return;
/*     */     }
/*  41 */     RecordSet recordSet = new RecordSet();
/*     */     
/*  43 */     int i = 0;
/*  44 */     int j = 0;
/*  45 */     int k = 0;
/*  46 */     int m = 1;
/*     */     
/*  48 */     recordSet.execute("select a.sptcount,a.resourceid ,a.createrid, a.departmentid,a.blongdepartment,a.blongsubcompany,b.createrid as createrid1 from CptCapital a LEFT JOIN CptCapital b ON a.datatype=b.id where a.id=" + paramString);
/*  49 */     if (recordSet.next()) {
/*     */       
/*  51 */       m = Util.getIntValue(recordSet.getString("sptcount"), 0);
/*  52 */       j = Util.getIntValue(recordSet.getString("createrid"), 0);
/*  53 */       i = Util.getIntValue(recordSet.getString("resourceid"), 0);
/*  54 */       k = Util.getIntValue(recordSet.getString("createrid1"), 0);
/*     */     } 
/*     */     
/*  57 */     recordSet.execute("select * from CptCapitalShareInfo where relateditemid=" + paramString + " and sharetype=7 and userid=" + j);
/*  58 */     String str = "";
/*  59 */     if (!recordSet.next()) {
/*  60 */       str = "insert into CptCapitalShareInfo(relateditemid,sharetype,sharelevel,userid,isdefault) values(" + paramString + ",7,2," + j + ",1) ";
/*  61 */       recordSet.execute(str);
/*     */     } 
/*     */     
/*  64 */     recordSet.execute("select * from CptCapitalShareInfo where relateditemid=" + paramString + " and sharetype=1 and userid=" + k);
/*  65 */     if (!recordSet.next()) {
/*  66 */       str = "insert into CptCapitalShareInfo(relateditemid,sharetype,sharelevel,userid,isdefault) values(" + paramString + ",1,1," + k + ",1) ";
/*  67 */       recordSet.execute(str);
/*     */     } 
/*     */     
/*  70 */     if (m == 1) {
/*     */       
/*  72 */       recordSet.execute("delete from CptCapitalShareInfo where relateditemid=" + paramString + " and sharetype in(6)");
/*  73 */       if (i > 0) {
/*  74 */         str = "insert into CptCapitalShareInfo(relateditemid,sharetype,sharelevel,userid,isdefault) values(" + paramString + ",6,1," + i + ",1) ";
/*  75 */         recordSet.execute(str);
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setCptShareByHrm(String paramString) throws Exception {
/*  89 */     RecordSet recordSet = new RecordSet();
/*  90 */     ArrayList<String> arrayList1 = new ArrayList();
/*  91 */     ArrayList<String> arrayList2 = new ArrayList();
/*  92 */     this.resourcecominfo = new ResourceComInfo();
/*  93 */     this.departmentcominfo = new DepartmentComInfo();
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  98 */     recordSet.execute("Select id from CptCapital where resourceid=" + paramString);
/*  99 */     while (recordSet.next()) {
/* 100 */       arrayList1.add(Util.null2String(recordSet.getString("id")));
/* 101 */       arrayList2.add("2");
/*     */     } 
/*     */ 
/*     */     
/* 105 */     String str1 = "";
/* 106 */     this.allsubordinates = new AllSubordinate();
/* 107 */     this.allsubordinates.getAll(paramString);
/* 108 */     while (this.allsubordinates.next()) {
/* 109 */       String str = this.allsubordinates.getSubordinateID();
/* 110 */       if (str1.equals("")) { str1 = str1 + str; continue; }
/* 111 */        str1 = str1 + "," + str;
/*     */     } 
/*     */     
/* 114 */     if (!str1.equals("")) {
/* 115 */       recordSet.execute("Select distinct id from CptCapital where resourceid in (" + str1 + ")");
/* 116 */       while (recordSet.next()) {
/* 117 */         if (arrayList1.indexOf(Util.null2String(recordSet.getString("id"))) >= 0)
/* 118 */           continue;  arrayList1.add(Util.null2String(recordSet.getString("id")));
/* 119 */         arrayList2.add("1");
/*     */       } 
/*     */     } 
/* 122 */     String str2 = this.resourcecominfo.getDepartmentID(paramString);
/* 123 */     String str3 = this.departmentcominfo.getSubcompanyid1(str2);
/* 124 */     String str4 = this.resourcecominfo.getSeclevel(paramString);
/*     */     
/* 126 */     if (str2.equals("")) str2 = "0"; 
/* 127 */     if (str3.equals("")) str3 = "0"; 
/* 128 */     if (str4.equals("")) str4 = "0";
/*     */   
/*     */   }
/*     */   
/*     */   public void InitDetail() {
/* 133 */     RecordSet recordSet = new RecordSet();
/*     */     
/* 135 */     recordSet.execute("delete from CptCapitalShareInfo");
/* 136 */     recordSet.execute("delete from CptShareDetail");
/*     */     
/* 138 */     recordSet.execute("SELECT id, resourceid FROM CptCapital WHERE (resourceid <>'' and resourceid is not null) AND (resourceid <> 0)");
/* 139 */     while (recordSet.next()) {
/*     */       try {
/* 141 */         setCptShareByCpt(recordSet.getString("id"));
/* 142 */       } catch (Exception exception) {}
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void SetAssortShare(String paramString) {
/* 151 */     String str1 = "";
/* 152 */     RecordSet recordSet1 = new RecordSet();
/* 153 */     RecordSet recordSet2 = new RecordSet();
/*     */ 
/*     */     
/* 156 */     ArrayList<String> arrayList1 = new ArrayList();
/* 157 */     recordSet1.execute("select distinct * from CptAssortmentShare where assortmentid=" + paramString);
/* 158 */     while (recordSet1.next()) {
/* 159 */       String str4 = recordSet1.getString("sharetype");
/* 160 */       String str5 = recordSet1.getString("seclevel");
/* 161 */       String str6 = recordSet1.getString("seclevelMax");
/* 162 */       String str7 = recordSet1.getString("rolelevel");
/* 163 */       String str8 = recordSet1.getString("sharelevel");
/* 164 */       String str9 = recordSet1.getString("userid");
/* 165 */       String str10 = recordSet1.getString("departmentid");
/* 166 */       String str11 = recordSet1.getString("roleid");
/* 167 */       String str12 = recordSet1.getString("foralluser");
/* 168 */       String str13 = recordSet1.getString("subcompanyid");
/* 169 */       String str14 = recordSet1.getString("jobtitleid");
/* 170 */       String str15 = recordSet1.getString("joblevel");
/* 171 */       String str16 = recordSet1.getString("scopeid");
/*     */       
/* 173 */       String str17 = str4;
/* 174 */       str17 = str17 + this.flag + str5;
/* 175 */       str17 = str17 + this.flag + str7;
/* 176 */       str17 = str17 + this.flag + str8;
/* 177 */       str17 = str17 + this.flag + str9;
/* 178 */       str17 = str17 + this.flag + str10;
/* 179 */       str17 = str17 + this.flag + str11;
/* 180 */       str17 = str17 + this.flag + str12;
/* 181 */       str17 = str17 + this.flag + paramString;
/* 182 */       str17 = str17 + this.flag + str13;
/* 183 */       str17 = str17 + this.flag + str6;
/* 184 */       str17 = str17 + this.flag + str14;
/* 185 */       str17 = str17 + this.flag + str15;
/* 186 */       str17 = str17 + this.flag + str16;
/*     */       
/* 188 */       arrayList1.add(str17);
/*     */     } 
/*     */     
/* 191 */     ArrayList<String> arrayList2 = new ArrayList();
/*     */     
/* 193 */     String str2 = "select t1.id from CptCapital t1 where t1.isdata='2' and exists (select t2.id from CptCapitalAssortment t2 where ( t2.id=t1.capitalgroupid and  ( t2.supassortmentstr like '%|" + paramString + "|%' or t2.id = " + paramString + " ) ) )";
/*     */ 
/*     */     
/* 196 */     String str3 = "delete from CptCapitalShareInfo where sharefrom=" + paramString;
/* 197 */     str3 = str3 + " and exists( select 1 from  (" + str2 + ") t3 where t3.id=CptCapitalShareInfo.relateditemid ) ";
/* 198 */     recordSet1.execute(str3);
/*     */     
/* 200 */     boolean bool1 = "oracle".equalsIgnoreCase((new RecordSet()).getDBType());
/* 201 */     boolean bool2 = "mysql".equalsIgnoreCase((new RecordSet()).getDBType());
/* 202 */     boolean bool3 = "postgresql".equalsIgnoreCase((new RecordSet()).getDBType());
/* 203 */     recordSet1.execute(str2);
/* 204 */     while (recordSet1.next()) {
/* 205 */       str1 = recordSet1.getString("id");
/* 206 */       for (byte b = 0; b < arrayList1.size(); b++) {
/*     */         
/* 208 */         String str = arrayList1.get(b);
/* 209 */         str = str1 + this.flag + str;
/*     */         
/* 211 */         if (bool1 || bool2 || bool3) {
/* 212 */           recordSet2.executeProc("CptAstShareInfo_Insert_dft", str);
/*     */         } else {
/* 214 */           arrayList2.add(str);
/*     */         } 
/*     */       } 
/*     */     } 
/*     */ 
/*     */     
/* 220 */     if ((!bool1 && !bool2) || !bool3) {
/* 221 */       (new DBUtil()).executeProcBatch("CptAstShareInfo_Insert_dft", arrayList2);
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public void freshenCptShareByCapitalgroup(String paramString) {
/* 228 */     RecordSet recordSet = new RecordSet();
/* 229 */     recordSet.execute("select capitalgroupid from cptcapital where id=" + paramString);
/* 230 */     String str = "";
/* 231 */     if (recordSet.next()) {
/* 232 */       String str1 = recordSet.getString("capitalgroupid");
/* 233 */       str = getRootGroupid(str1);
/*     */     } 
/* 235 */     recordSet.execute("delete from  CptCapitalShareInfo where relateditemid=" + paramString + " and sharefrom is not null");
/* 236 */     ArrayList<String> arrayList1 = new ArrayList();
/* 237 */     ArrayList<String> arrayList2 = new ArrayList();
/* 238 */     ArrayList<String> arrayList3 = new ArrayList();
/* 239 */     ArrayList<String> arrayList4 = new ArrayList();
/* 240 */     ArrayList<String> arrayList5 = new ArrayList();
/* 241 */     ArrayList<String> arrayList6 = new ArrayList();
/* 242 */     ArrayList<String> arrayList7 = new ArrayList();
/* 243 */     ArrayList<String> arrayList8 = new ArrayList();
/* 244 */     ArrayList<String> arrayList9 = new ArrayList();
/* 245 */     ArrayList<String> arrayList10 = new ArrayList();
/* 246 */     ArrayList<String> arrayList11 = new ArrayList();
/* 247 */     ArrayList<String> arrayList12 = new ArrayList();
/* 248 */     ArrayList<String> arrayList13 = new ArrayList();
/* 249 */     recordSet.execute("select * from CptAssortmentShare where assortmentid=" + str);
/* 250 */     while (recordSet.next()) {
/* 251 */       arrayList1.add(recordSet.getString("sharetype"));
/* 252 */       arrayList2.add(recordSet.getString("seclevel"));
/* 253 */       arrayList3.add(recordSet.getString("rolelevel"));
/* 254 */       arrayList4.add(recordSet.getString("sharelevel"));
/* 255 */       arrayList5.add(recordSet.getString("userid"));
/* 256 */       arrayList6.add(recordSet.getString("departmentid"));
/* 257 */       arrayList7.add(recordSet.getString("roleid"));
/* 258 */       arrayList8.add(recordSet.getString("foralluser"));
/* 259 */       arrayList9.add(recordSet.getString("subcompanyid"));
/* 260 */       arrayList10.add(recordSet.getString("seclevelMax"));
/* 261 */       arrayList11.add(recordSet.getString("jobtitleid"));
/* 262 */       arrayList12.add(recordSet.getString("joblevel"));
/* 263 */       arrayList13.add(recordSet.getString("scopeid"));
/*     */     } 
/*     */ 
/*     */     
/* 267 */     for (byte b = 0; b < arrayList1.size(); b++) {
/* 268 */       recordSet.execute("INSERT INTO CptCapitalShareInfo(relateditemid, sharetype, seclevel, rolelevel, sharelevel, userid, departmentid, roleid, foralluser,subcompanyid,seclevelMax,jobtitleid,joblevel,scopeid,isdefault,sharefrom)  VALUES ( " + paramString + ", " + 
/*     */           
/* 270 */           Util.getIntValue(arrayList1.get(b), 0) + ", " + 
/* 271 */           Util.getIntValue(arrayList2.get(b), 0) + ", " + Util.getIntValue(arrayList3.get(b), 0) + ", " + Util.getIntValue(arrayList4.get(b), 1) + "," + 
/* 272 */           Util.getIntValue(arrayList5.get(b), 0) + ", " + Util.getIntValue(arrayList6.get(b), 0) + ", " + Util.getIntValue(arrayList7.get(b), 0) + "," + 
/* 273 */           Util.getIntValue(arrayList8.get(b), 0) + "," + Util.getIntValue(arrayList9.get(b), 0) + "," + Util.getIntValue(arrayList10.get(b), 0) + "," + 
/* 274 */           Util.getIntValue(arrayList11.get(b), 0) + "," + Util.getIntValue(arrayList12.get(b), 0) + "," + Util.getIntValue(arrayList13.get(b), 0) + ",1," + Util.getIntValue(str) + ")");
/*     */     }
/*     */   }
/*     */   
/*     */   public void freshenCptShareByResource(String paramString) {
/* 279 */     RecordSet recordSet = new RecordSet();
/* 280 */     recordSet.execute("select resourceid from cptcapital where id=" + paramString);
/* 281 */     String str = "";
/* 282 */     if (recordSet.next()) {
/* 283 */       str = Util.null2String(recordSet.getString("resourceid"), "0");
/*     */     }
/* 285 */     recordSet.execute("DELETE FROM CptCapitalShareInfo WHERE relateditemid=" + paramString + " AND sharetype=6");
/*     */     
/* 287 */     if (!str.equals("0")) {
/* 288 */       recordSet.execute("INSERT INTO CptCapitalShareInfo(relateditemid, sharetype, seclevel, rolelevel, sharelevel, userid, departmentid, roleid, foralluser, subcompanyid, isdefault)  VALUES ( " + paramString + ", 6, 0, 0, 1," + str + ", 0, 0, 0, 0, 1)");
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public String getRootGroupid(String paramString) {
/* 295 */     RecordSet recordSet = new RecordSet();
/* 296 */     String str = "";
/* 297 */     recordSet.execute("select id,supassortmentid from CptCapitalAssortment where id=" + paramString);
/* 298 */     if (recordSet.next()) {
/* 299 */       paramString = recordSet.getString("id");
/* 300 */       String str1 = recordSet.getString("supassortmentid");
/* 301 */       if ("0".equals(str1)) {
/* 302 */         str = paramString;
/*     */       } else {
/* 304 */         str = getRootGroupid(str1);
/*     */       } 
/*     */     } 
/* 307 */     return str;
/*     */   }
/*     */ 
/*     */   
/*     */   public void setCptShareByWareHouse(String paramString) {
/* 312 */     if (Util.getIntValue(paramString, 0) <= 0) {
/*     */       return;
/*     */     }
/* 315 */     RecordSet recordSet1 = new RecordSet();
/*     */     
/* 317 */     String str1 = "delete from CptCapitalShareInfo where relatedtshareid=-1 and warehouse=?";
/* 318 */     recordSet1.executeUpdate(str1, new Object[] { paramString });
/* 319 */     RecordSet recordSet2 = new RecordSet();
/* 320 */     String str2 = "";
/* 321 */     String str3 = "";
/* 322 */     recordSet1.executeQuery("select manager,subcompanyid from CptCapitalWareHouse where id=?", new Object[] { paramString });
/* 323 */     if (recordSet1.next()) {
/* 324 */       str2 = Util.null2String(recordSet1.getString("manager"));
/* 325 */       str3 = Util.null2String(recordSet1.getString("subcompanyid"));
/*     */     } 
/* 327 */     String[] arrayOfString1 = str2.split(",");
/* 328 */     String[] arrayOfString2 = str3.split(",");
/* 329 */     recordSet2.executeQuery("select id from CptCapital where warehouse=? and isdata=2", new Object[] { paramString });
/* 330 */     while (recordSet2.next()) {
/* 331 */       String str = Util.null2String(recordSet2.getString("id"));
/*     */       
/* 333 */       for (String str4 : arrayOfString1) {
/* 334 */         String str5 = "insert into CptCapitalShareInfo(relateditemid,sharetype,sharelevel,userid,isdefault,warehouse,relatedtshareid) values(" + str + ",1,2," + str4 + ",1," + paramString + ",-1) ";
/* 335 */         recordSet1.executeUpdate(str5, new Object[0]);
/*     */       } 
/*     */       
/* 338 */       for (String str4 : arrayOfString2) {
/* 339 */         String str5 = "insert into CptCapitalShareInfo(relateditemid,sharetype,sharelevel,subcompanyid,isdefault,warehouse,relatedtshareid,seclevel,seclevelMax) values(" + str + ",5,1," + str4 + ",1," + paramString + ",-1,0,99999) ";
/* 340 */         recordSet1.executeUpdate(str5, new Object[0]);
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   public void setWareHouseShare(String paramString) {
/* 347 */     String str1 = "";
/* 348 */     RecordSet recordSet1 = new RecordSet();
/* 349 */     RecordSet recordSet2 = new RecordSet();
/* 350 */     RecordSet recordSet3 = new RecordSet();
/*     */     
/* 352 */     String str2 = "select t1.id from CptCapital t1 where t1.isdata=2 and exists (select t2.id from CptCapitalWareHouse t2 where ( t2.id=t1.warehouse and t2.id = " + paramString + " ) )";
/*     */     
/* 354 */     String str3 = "delete from CptCapitalShareInfo where (relatedtshareid!=-1 or relatedtshareid is null) and warehouse=" + paramString;
/* 355 */     str3 = str3 + " and exists( select 1 from  (" + str2 + ") t3 where t3.id=CptCapitalShareInfo.relateditemid ) ";
/* 356 */     recordSet1.execute(str3);
/* 357 */     recordSet1.execute(str2);
/* 358 */     while (recordSet1.next()) {
/* 359 */       str1 = recordSet1.getString("id");
/*     */       
/* 361 */       recordSet2.execute("select distinct * from CptWareHouseShare where warehouseid=" + paramString);
/* 362 */       while (recordSet2.next()) {
/* 363 */         String str4 = recordSet2.getString("sharetype");
/* 364 */         String str5 = recordSet2.getString("seclevel");
/* 365 */         String str6 = recordSet2.getString("seclevelMax");
/* 366 */         String str7 = recordSet2.getString("rolelevel");
/* 367 */         String str8 = recordSet2.getString("sharelevel");
/* 368 */         String str9 = recordSet2.getString("userid");
/* 369 */         String str10 = recordSet2.getString("departmentid");
/* 370 */         String str11 = recordSet2.getString("roleid");
/* 371 */         String str12 = recordSet2.getString("foralluser");
/* 372 */         String str13 = recordSet2.getString("subcompanyid");
/* 373 */         String str14 = recordSet2.getString("jobtitleid");
/* 374 */         String str15 = recordSet2.getString("joblevel");
/* 375 */         String str16 = recordSet2.getString("scopeid");
/* 376 */         recordSet3.executeUpdate("INSERT INTO CptCapitalShareInfo(relateditemid, sharetype, seclevel, rolelevel, sharelevel, userid, departmentid, roleid, foralluser, subcompanyid, isdefault, seclevelMax, jobtitleid, joblevel, scopeid, warehouse)  VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)", new Object[] { str1, str4, str5, str7, str8, str9, str10, str11, str12, str13, 
/* 377 */               Integer.valueOf(1), str6, str14, str15, str16, paramString });
/*     */       } 
/*     */     } 
/*     */   }
/*     */   
/*     */   public void freshenCptShareByWareHouse(String paramString) {
/* 383 */     RecordSet recordSet = new RecordSet();
/* 384 */     recordSet.execute("select warehouse from cptcapital where id=" + paramString);
/* 385 */     String str1 = "";
/* 386 */     if (recordSet.next()) {
/* 387 */       str1 = recordSet.getString("warehouse");
/*     */     }
/*     */ 
/*     */     
/* 391 */     recordSet.execute("delete from CptCapitalShareInfo where relateditemid=" + paramString + " and warehouse is not null");
/*     */ 
/*     */     
/* 394 */     String str2 = "";
/* 395 */     String str3 = "";
/* 396 */     recordSet.executeQuery("select manager,subcompanyid from CptCapitalWareHouse where id=?", new Object[] { str1 });
/* 397 */     if (recordSet.next()) {
/* 398 */       str2 = Util.null2String(recordSet.getString("manager"));
/* 399 */       str3 = Util.null2String(recordSet.getString("subcompanyid"));
/*     */     } 
/* 401 */     String[] arrayOfString1 = str2.split(",");
/* 402 */     String[] arrayOfString2 = str3.split(",");
/*     */     
/* 404 */     for (String str4 : arrayOfString1) {
/* 405 */       String str5 = "insert into CptCapitalShareInfo(relateditemid,sharetype,sharelevel,userid,isdefault,warehouse,relatedtshareid) values(?,1,2,?,1,?,-1) ";
/* 406 */       recordSet.executeUpdate(str5, new Object[] { paramString, str4, str1 });
/*     */     } 
/*     */     
/* 409 */     for (String str4 : arrayOfString2) {
/* 410 */       String str5 = "insert into CptCapitalShareInfo(relateditemid,sharetype,sharelevel,subcompanyid,isdefault,seclevel,seclevelMax,warehouse,relatedtshareid) values(?,5,1,?,1,0,99999,?,-1) ";
/* 411 */       recordSet.executeUpdate(str5, new Object[] { paramString, str4, str1 });
/*     */     } 
/*     */ 
/*     */     
/* 415 */     ArrayList<String> arrayList1 = new ArrayList();
/* 416 */     ArrayList<String> arrayList2 = new ArrayList();
/* 417 */     ArrayList<String> arrayList3 = new ArrayList();
/* 418 */     ArrayList<String> arrayList4 = new ArrayList();
/* 419 */     ArrayList<String> arrayList5 = new ArrayList();
/* 420 */     ArrayList<String> arrayList6 = new ArrayList();
/* 421 */     ArrayList<String> arrayList7 = new ArrayList();
/* 422 */     ArrayList<String> arrayList8 = new ArrayList();
/* 423 */     ArrayList<String> arrayList9 = new ArrayList();
/* 424 */     ArrayList<String> arrayList10 = new ArrayList();
/* 425 */     ArrayList<String> arrayList11 = new ArrayList();
/* 426 */     ArrayList<String> arrayList12 = new ArrayList();
/* 427 */     ArrayList<String> arrayList13 = new ArrayList();
/* 428 */     recordSet.executeQuery("select * from CptWareHouseShare where warehouseid=?", new Object[] { str1 });
/* 429 */     while (recordSet.next()) {
/* 430 */       arrayList1.add(recordSet.getString("sharetype"));
/* 431 */       arrayList2.add(recordSet.getString("seclevel"));
/* 432 */       arrayList3.add(recordSet.getString("rolelevel"));
/* 433 */       arrayList4.add(recordSet.getString("sharelevel"));
/* 434 */       arrayList5.add(recordSet.getString("userid"));
/* 435 */       arrayList6.add(recordSet.getString("departmentid"));
/* 436 */       arrayList7.add(recordSet.getString("roleid"));
/* 437 */       arrayList8.add(recordSet.getString("foralluser"));
/* 438 */       arrayList9.add(recordSet.getString("subcompanyid"));
/* 439 */       arrayList10.add(recordSet.getString("seclevelMax"));
/* 440 */       arrayList11.add(recordSet.getString("jobtitleid"));
/* 441 */       arrayList12.add(recordSet.getString("joblevel"));
/* 442 */       arrayList13.add(recordSet.getString("scopeid"));
/*     */     } 
/*     */     
/* 445 */     for (byte b = 0; b < arrayList1.size(); b++)
/* 446 */       recordSet.execute("INSERT INTO CptCapitalShareInfo(relateditemid, sharetype, seclevel, rolelevel, sharelevel, userid, departmentid, roleid, foralluser,subcompanyid,seclevelMax,jobtitleid,joblevel,scopeid,isdefault,warehouse)  VALUES ( " + paramString + ", " + 
/*     */           
/* 448 */           Util.getIntValue(arrayList1.get(b), 0) + ", " + 
/* 449 */           Util.getIntValue(arrayList2.get(b), 0) + ", " + Util.getIntValue(arrayList3.get(b), 0) + ", " + Util.getIntValue(arrayList4.get(b), 1) + "," + 
/* 450 */           Util.getIntValue(arrayList5.get(b), 0) + ", " + Util.getIntValue(arrayList6.get(b), 0) + ", " + Util.getIntValue(arrayList7.get(b), 0) + "," + 
/* 451 */           Util.getIntValue(arrayList8.get(b)) + "," + Util.getIntValue(arrayList9.get(b)) + "," + Util.getIntValue(arrayList10.get(b)) + "," + 
/* 452 */           Util.getIntValue(arrayList11.get(b)) + "," + Util.getIntValue(arrayList12.get(b)) + "," + (String)arrayList13.get(b) + ",1," + Util.getIntValue(str1) + ")"); 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/cpt/capital/CptShare.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */