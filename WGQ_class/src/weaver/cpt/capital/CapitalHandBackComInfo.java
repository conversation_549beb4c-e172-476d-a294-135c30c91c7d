/*    */ package weaver.cpt.capital;
/*    */ 
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.BaseBean;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.resource.ResourceComInfo;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class CapitalHandBackComInfo
/*    */   extends BaseBean
/*    */ {
/* 20 */   String managerID = "";
/* 21 */   String resourceID = "";
/* 22 */   String toDepartmentID = "";
/* 23 */   String fromDepartmentID = "";
/* 24 */   String relateReq = "";
/* 25 */   String returnDate = "";
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public void setManagerID(String paramString) {
/* 32 */     this.managerID = paramString;
/*    */   }
/*    */   
/*    */   public void setResourceID(String paramString) {
/* 36 */     this.resourceID = paramString;
/*    */   }
/*    */   
/*    */   public void setToDepartmentID(String paramString) {
/* 40 */     this.toDepartmentID = paramString;
/*    */   }
/*    */   
/*    */   public void setFromDepartmentID(String paramString) {
/* 44 */     this.fromDepartmentID = paramString;
/*    */   }
/*    */   
/*    */   public void setRelateReq(String paramString) {
/* 48 */     this.relateReq = paramString;
/*    */   }
/*    */   
/*    */   public void setReturnDate(String paramString) {
/* 52 */     this.returnDate = paramString;
/*    */   }
/*    */   
/*    */   private void resetCapitalHandBackComInfo() throws Exception {
/* 56 */     this.managerID = "";
/* 57 */     this.resourceID = "";
/* 58 */     this.toDepartmentID = "";
/* 59 */     this.fromDepartmentID = "";
/* 60 */     this.relateReq = "";
/* 61 */     this.returnDate = "";
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public void handBack() throws Exception {
/* 67 */     RecordSet recordSet = new RecordSet();
/*    */     try {
/* 69 */       char c = Util.getSeparator();
/* 70 */       recordSet.executeProc("CptCapital_HandBackSelect", this.resourceID + c + this.managerID);
/* 71 */       while (recordSet.next())
/*    */       {
/* 73 */         ResourceComInfo resourceComInfo = new ResourceComInfo();
/* 74 */         String str1 = recordSet.getString("id");
/* 75 */         String str2 = recordSet.getString("sptcount");
/* 76 */         String str3 = resourceComInfo.getCostcenterID(this.managerID);
/* 77 */         String str4 = recordSet.getString("capitalnum");
/* 78 */         String str5 = "";
/* 79 */         str5 = str1;
/* 80 */         str5 = str5 + c + this.returnDate;
/* 81 */         str5 = str5 + c + this.managerID;
/* 82 */         str5 = str5 + c + this.toDepartmentID;
/* 83 */         str5 = str5 + c + str3;
/* 84 */         str5 = str5 + c + this.fromDepartmentID;
/* 85 */         str5 = str5 + c + str4;
/* 86 */         str5 = str5 + c + this.relateReq;
/* 87 */         str5 = str5 + c + str2;
/* 88 */         recordSet.executeProc("CptUseLogReturn_Insert", str5);
/*    */       }
/*    */     
/* 91 */     } catch (Exception exception) {
/* 92 */       writeLog(exception);
/* 93 */       throw exception;
/*    */     } 
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/cpt/capital/CapitalHandBackComInfo.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */