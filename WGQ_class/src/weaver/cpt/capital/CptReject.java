/*    */ package weaver.cpt.capital;
/*    */ 
/*    */ import java.util.ArrayList;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.Util;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class CptReject
/*    */ {
/*    */   public ArrayList getRejectData(String[] paramArrayOfString) {
/* 32 */     if (paramArrayOfString == null || paramArrayOfString.length < 4) {
/* 33 */       return null;
/*    */     }
/* 35 */     String str1 = Util.null2String(paramArrayOfString[0]);
/* 36 */     String str2 = Util.null2String(paramArrayOfString[1]);
/* 37 */     String str3 = Util.null2String(paramArrayOfString[2]);
/* 38 */     String str4 = Util.null2String(paramArrayOfString[3]);
/*    */     
/* 40 */     String str5 = "";
/* 41 */     String str6 = "";
/* 42 */     boolean bool = true;
/*    */     
/* 44 */     if (!str3.equals("")) {
/* 45 */       if (bool) {
/* 46 */         bool = false;
/* 47 */         str6 = str6 + " WHERE t1.mark LIKE '%" + str3 + "%'";
/*    */       } else {
/* 49 */         str6 = str6 + " AND t1.mark LIKE '%" + str3 + "%'";
/*    */       } 
/*    */     }
/* 52 */     if (!str4.equals("")) {
/* 53 */       if (bool) {
/* 54 */         bool = false;
/* 55 */         str6 = str6 + " WHERE t1.name LIKE '%" + str4 + "%'";
/*    */       } else {
/* 57 */         str6 = str6 + " AND t1.name LIKE '%" + str4 + "%'";
/*    */       } 
/*    */     }
/* 60 */     if (str6.equals("")) {
/* 61 */       str6 = " WHERE 5 = 5";
/*    */     }
/* 63 */     RecordSet recordSet = new RecordSet();
/* 64 */     String str7 = "";
/* 65 */     String str8 = "";
/* 66 */     String str9 = "";
/* 67 */     String str10 = "";
/*    */     
/* 69 */     ArrayList<String[]> arrayList = new ArrayList();
/*    */     
/* 71 */     str5 = "SELECT DISTINCT t1.id, t1.name, t1.mark, t1.capitalnum FROM CptCapital t1, CptShareDetail t2 " + str6 + " AND (t1.id = t2.cptid AND t2.userid = " + str1 + ") AND t2.usertype = " + str2 + " AND t1.isdata = '2' AND t1.stateid <> 5 AND t1.stateid <> -7";
/*    */ 
/*    */ 
/*    */     
/* 75 */     recordSet.executeSql(str5);
/* 76 */     while (recordSet.next()) {
/* 77 */       str7 = Util.null2String(recordSet.getString("id"));
/* 78 */       str8 = Util.null2String(recordSet.getString("mark"));
/* 79 */       str9 = Util.null2String(recordSet.getString("name"));
/* 80 */       str10 = Util.null2String(recordSet.getString("capitalnum"));
/*    */       
/* 82 */       String[] arrayOfString = { str7, str8, str9, str10 };
/* 83 */       arrayList.add(arrayOfString);
/*    */     } 
/*    */     
/* 86 */     return arrayList;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/cpt/capital/CptReject.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */