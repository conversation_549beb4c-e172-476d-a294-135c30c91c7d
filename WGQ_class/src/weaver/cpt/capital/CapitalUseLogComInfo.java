/*     */ package weaver.cpt.capital;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import weaver.cache.CacheBase;
/*     */ import weaver.cache.CacheColumn;
/*     */ import weaver.cache.CacheColumnType;
/*     */ import weaver.cache.PKColumn;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CapitalUseLogComInfo
/*     */   extends CacheBase
/*     */ {
/*  25 */   protected static String TABLE_NAME = "CptUseLog";
/*     */   
/*  27 */   protected static String TABLE_WHERE = null;
/*     */   
/*  29 */   protected static String TABLE_ORDER = "id ASC";
/*     */   
/*     */   @PKColumn(type = CacheColumnType.NUMBER)
/*  32 */   protected static String PK_NAME = "id";
/*     */ 
/*     */   
/*     */   @CacheColumn
/*     */   protected static int cptid;
/*     */ 
/*     */   
/*     */   @CacheColumn
/*     */   protected static int useresourceid;
/*     */ 
/*     */   
/*     */   public boolean next() {
/*  44 */     return super.next();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setTofirstRow() {
/*  51 */     super.setTofirstRow();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getUserResourceid(String paramString) {
/*  62 */     return (String)getValue(useresourceid, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void removeCapitalCache() {
/*  69 */     removeCache();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void addCapitalCache(ArrayList<String> paramArrayList) throws Exception {
/*  78 */     if (paramArrayList == null || paramArrayList.isEmpty()) {
/*     */       return;
/*     */     }
/*  81 */     String str = "";
/*  82 */     for (byte b = 0; b < paramArrayList.size(); b++) {
/*  83 */       str = paramArrayList.get(b);
/*  84 */       addCapitalCache(str);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void addCapitalCache(String paramString) throws Exception {
/*  94 */     addCache(paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void updateCapitalCache(String paramString) throws Exception {
/* 103 */     updateCache(paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void deleteCapitalCache(String paramString) {
/* 111 */     deleteCache(paramString);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/cpt/capital/CapitalUseLogComInfo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */