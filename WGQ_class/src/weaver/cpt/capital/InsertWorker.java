/*    */ package weaver.cpt.capital;
/*    */ 
/*    */ import weaver.conn.RecordSet;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class InsertWorker
/*    */   extends Thread
/*    */ {
/*    */   private String para;
/*    */   
/*    */   public InsertWorker(String paramString) {
/* 16 */     this.para = paramString;
/*    */   }
/*    */   
/*    */   public void run() {
/* 20 */     RecordSet recordSet = new RecordSet();
/* 21 */     recordSet.executeProc("CptStockInDetail_InsertSingle", this.para);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/cpt/capital/InsertWorker.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */