/*     */ package weaver.cpt.capital;
/*     */ 
/*     */ import java.math.BigDecimal;
/*     */ import java.util.Calendar;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CapitalCurPrice
/*     */   extends BaseBean
/*     */ {
/*  18 */   String sptcount = "";
/*  19 */   String startprice = "";
/*  20 */   String capitalnum = "";
/*  21 */   String deprestartdate = "";
/*  22 */   String depreyear = "";
/*  23 */   String deprerate = "";
/*  24 */   Calendar today = Calendar.getInstance();
/*  25 */   String currentdate = Util.add0(this.today.get(1), 4) + "-" + 
/*  26 */     Util.add0(this.today.get(2) + 1, 2) + "-" + 
/*  27 */     Util.add0(this.today.get(5), 2);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getUsedYear() {
/*  35 */     String str1 = "0";
/*  36 */     String str2 = "0";
/*     */     
/*  38 */     int i = calcTotalMonth();
/*  39 */     if (i > 0) {
/*  40 */       str1 = "" + (i / 12);
/*  41 */       str2 = "" + (i % 12);
/*     */     } 
/*     */     
/*  44 */     return str1 + "," + str2;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCurPrice() {
/*  56 */     String str = "";
/*     */     
/*  58 */     BigDecimal bigDecimal1 = new BigDecimal(this.startprice);
/*  59 */     if (bigDecimal1.compareTo(new BigDecimal(0)) < 0) bigDecimal1 = new BigDecimal(0);
/*     */     
/*  61 */     BigDecimal bigDecimal2 = new BigDecimal(this.capitalnum);
/*  62 */     if (bigDecimal2.compareTo(new BigDecimal(0)) < 0) bigDecimal2 = new BigDecimal(0);
/*     */     
/*  64 */     float f1 = Util.getFloatValue(this.depreyear);
/*  65 */     if (f1 < 0.0F) f1 = 0.0F;
/*     */     
/*  67 */     if (this.deprerate.equals("")) {
/*  68 */       this.deprerate = "0";
/*     */     }
/*  70 */     BigDecimal bigDecimal3 = new BigDecimal(this.deprerate);
/*  71 */     if (bigDecimal3.compareTo(new BigDecimal(0)) < 0) bigDecimal3 = new BigDecimal(0); 
/*  72 */     float f2 = Util.getFloatValue(this.deprerate);
/*  73 */     if (f2 < 0.0F) f2 = 0.0F;
/*     */     
/*  75 */     if (this.sptcount.equals("1")) {
/*     */       
/*  77 */       if (Util.getFloatValue(this.deprerate) >= 100.0F || f1 == 0.0F) {
/*  78 */         str = this.startprice;
/*     */       } else {
/*     */         
/*  81 */         int i = calcTotalMonth();
/*     */         
/*  83 */         if (i >= f1 * 12.0F) {
/*  84 */           str = "" + bigDecimal1.multiply(bigDecimal3).divide(new BigDecimal(100));
/*  85 */           str = Util.getPointValue(str);
/*     */         } else {
/*     */           
/*  88 */           float f3 = (100.0F - f2) / 100.0F;
/*     */           
/*  90 */           float f4 = i / f1 * 12.0F;
/*  91 */           str = "" + bigDecimal1.subtract(bigDecimal1.multiply(new BigDecimal(f3)).multiply(new BigDecimal(f4)));
/*  92 */           str = Util.getPointValue(str);
/*     */         } 
/*     */       } 
/*     */     } else {
/*     */       
/*  97 */       str = "" + bigDecimal1.multiply(bigDecimal2).multiply(new BigDecimal(1.0D));
/*     */     } 
/*     */     
/* 100 */     return (new BigDecimal(str)).setScale(2, 4).toString();
/*     */   }
/*     */   
/*     */   private int calcTotalMonth() {
/* 104 */     int i = 0;
/* 105 */     if (!this.deprestartdate.equals("")) {
/* 106 */       i = Util.getIntValue(this.currentdate.substring(5, 7)) - Util.getIntValue(this.deprestartdate.substring(5, 7)) + (Util.getIntValue(this.currentdate.substring(0, 4)) - Util.getIntValue(this.deprestartdate.substring(0, 4))) * 12;
/*     */     }
/* 108 */     return i;
/*     */   }
/*     */   
/*     */   public void setCapitalnum(String paramString) {
/* 112 */     this.capitalnum = paramString;
/*     */   }
/*     */   
/*     */   public void setDeprerate(String paramString) {
/* 116 */     this.deprerate = paramString;
/*     */   }
/*     */   
/*     */   public void setDeprestartdate(String paramString) {
/* 120 */     this.deprestartdate = paramString;
/*     */   }
/*     */   
/*     */   public void setDepreyear(String paramString) {
/* 124 */     this.depreyear = paramString;
/*     */   }
/*     */   
/*     */   public void setSptcount(String paramString) {
/* 128 */     this.sptcount = paramString;
/*     */   }
/*     */   
/*     */   public void setStartprice(String paramString) {
/* 132 */     this.startprice = paramString;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/cpt/capital/CapitalCurPrice.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */