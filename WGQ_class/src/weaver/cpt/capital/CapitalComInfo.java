/*     */ package weaver.cpt.capital;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import weaver.cache.CacheBase;
/*     */ import weaver.cache.CacheColumn;
/*     */ import weaver.cache.CacheColumnType;
/*     */ import weaver.cache.CacheItem;
/*     */ import weaver.cache.CacheMap;
/*     */ import weaver.cache.PKColumn;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CapitalComInfo
/*     */   extends CacheBase
/*     */ {
/*  19 */   protected static String TABLE_NAME = "CptCapital";
/*     */   
/*  21 */   protected static String TABLE_WHERE = null;
/*     */   
/*  23 */   protected static String TABLE_ORDER = "id ASC";
/*     */   
/*     */   @PKColumn(type = CacheColumnType.NUMBER)
/*  26 */   protected static String PK_NAME = "id";
/*     */   
/*     */   @CacheColumn
/*     */   protected static int mark;
/*     */   
/*     */   @CacheColumn
/*     */   protected static int name;
/*     */   
/*     */   @CacheColumn
/*     */   protected static int departmentid;
/*     */   
/*     */   @CacheColumn
/*     */   protected static int resourceid;
/*     */   
/*     */   @CacheColumn
/*     */   protected static int capitaltypeid;
/*     */   @CacheColumn
/*     */   protected static int capitalgroupid;
/*     */   @CacheColumn
/*     */   protected static int stateid;
/*     */   @CacheColumn
/*     */   protected static int isdata;
/*     */   @CacheColumn
/*     */   protected static int sptcount;
/*     */   @CacheColumn
/*     */   protected static int capitalspec;
/*     */   @CacheColumn
/*     */   protected static int capitalnum;
/*     */   
/*     */   public int getCapitalNum() {
/*  56 */     return size();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   protected CacheMap initCache() throws Exception {
/*  66 */     return null;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public CacheItem initCache(String paramString) {
/*  76 */     if (paramString == null || "".equals(paramString.trim())) {
/*  77 */       return null;
/*     */     }
/*  79 */     RecordSet recordSet = new RecordSet();
/*  80 */     String str = "select id,mark,name,departmentid,resourceid,capitaltypeid,capitalgroupid,stateid,isdata,capitalnum,capitalspec,sptcount from cptcapital where id=" + paramString;
/*     */     
/*  82 */     recordSet.execute(str);
/*  83 */     if (recordSet.next()) {
/*  84 */       CacheItem cacheItem = createCacheItem();
/*     */       
/*  86 */       parseResultSetToCacheItem(recordSet, cacheItem);
/*  87 */       modifyCacheItem(paramString, cacheItem);
/*  88 */       return cacheItem;
/*     */     } 
/*  90 */     return null;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean next() {
/*  98 */     return super.next();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setTofirstRow() {
/* 105 */     super.setTofirstRow();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCapitalid() {
/* 113 */     return (String)getRowValue(0);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCapitalname() {
/* 121 */     return (String)getRowValue(name);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getMark() {
/* 129 */     return (String)getRowValue(mark);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCapitalspec() {
/* 137 */     return (String)getRowValue(capitalspec);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getDepartmentid() {
/* 145 */     return (String)getRowValue(departmentid);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getResourceid() {
/* 153 */     return (String)getRowValue(resourceid);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCapitalname(String paramString) {
/* 163 */     return (String)getValue(name, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getMark(String paramString) {
/* 173 */     return (String)getValue(mark, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCapitalspec(String paramString) {
/* 183 */     return (String)getValue(capitalspec, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getDepartmentid(String paramString) {
/* 193 */     return (String)getValue(departmentid, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getResourceid(String paramString) {
/* 203 */     return (String)getValue(resourceid, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCptTypeId(String paramString) {
/* 213 */     return (String)getValue(capitaltypeid, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCptGroupId(String paramString) {
/* 223 */     return (String)getValue(capitalgroupid, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCptStateId(String paramString) {
/* 233 */     return (String)getValue(stateid, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getIsData() {
/* 241 */     return (String)getRowValue(isdata);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getIsData(String paramString) {
/* 250 */     return (String)getValue(isdata, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getSptCount() {
/* 258 */     return (String)getRowValue(sptcount);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getSptCount(String paramString) {
/* 266 */     return (String)getValue(sptcount, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCapitalNum(String paramString) {
/* 272 */     return (String)getValue(capitalnum, paramString);
/*     */   }
/*     */ 
/*     */   
/*     */   public void removeCapitalCache() {
/* 277 */     removeCache();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void addCapitalCache(ArrayList<String> paramArrayList) throws Exception {
/* 286 */     if (paramArrayList == null || paramArrayList.isEmpty()) {
/*     */       return;
/*     */     }
/* 289 */     String str = "";
/* 290 */     for (byte b = 0; b < paramArrayList.size(); b++) {
/* 291 */       str = paramArrayList.get(b);
/* 292 */       addCapitalCache(str);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void addCapitalCache(String paramString) throws Exception {
/* 302 */     addCache(paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void updateCapitalCache(String paramString) throws Exception {
/* 311 */     updateCache(paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void deleteCapitalCache(String paramString) {
/* 319 */     deleteCache(paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getIsSptCount(String paramString1, String paramString2) {
/* 329 */     if (paramString1.equals("1"))
/* 330 */       return SystemEnv.getHtmlLabelName(163, Integer.parseInt(paramString2)); 
/* 331 */     return SystemEnv.getHtmlLabelName(161, Integer.parseInt(paramString2));
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/cpt/capital/CapitalComInfo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */