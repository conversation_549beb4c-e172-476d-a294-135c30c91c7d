/*    */ package weaver.cpt.capital;
/*    */ 
/*    */ import weaver.cache.CacheBase;
/*    */ import weaver.cache.CacheColumn;
/*    */ import weaver.cache.CacheColumnType;
/*    */ import weaver.cache.PKColumn;
/*    */ 
/*    */ public class CapitalRelateWFComInfo
/*    */   extends CacheBase {
/* 10 */   protected static String TABLE_NAME = "CptRelateWorkflow";
/*    */   
/* 12 */   protected static String TABLE_WHERE = null;
/*    */   
/* 14 */   protected static String TABLE_ORDER = "id ASC";
/*    */   
/*    */   @PKColumn(type = CacheColumnType.NUMBER)
/* 17 */   protected static String PK_NAME = "id";
/*    */   
/*    */   @CacheColumn
/*    */   protected static int name;
/*    */ 
/*    */   
/*    */   public int getCapitalRelateWFNum() {
/* 24 */     return size();
/*    */   }
/*    */   
/*    */   public boolean next() {
/* 28 */     return super.next();
/*    */   }
/*    */   
/*    */   public boolean next(String paramString) {
/* 32 */     setTofirstRow();
/* 33 */     return false;
/*    */   }
/*    */   
/*    */   public void setTofirstRow() {
/* 37 */     super.setTofirstRow();
/*    */   }
/*    */   
/*    */   public String getCapitalRelateWFid() {
/* 41 */     return (String)getRowValue(0);
/*    */   }
/*    */   
/*    */   public String getCapitalRelateWFname() {
/* 45 */     return (String)getRowValue(name);
/*    */   }
/*    */ 
/*    */   
/*    */   public String getCapitalRelateWFname(String paramString) {
/* 50 */     return (String)getValue(name, paramString);
/*    */   }
/*    */   
/*    */   public void removeCapitalRelateWFCache() {
/* 54 */     removeCache();
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/cpt/capital/CapitalRelateWFComInfo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */