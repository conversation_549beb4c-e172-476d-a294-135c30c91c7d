/*     */ package weaver.cpt.capital;
/*     */ 
/*     */ import java.util.Calendar;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.cpt.maintenance.Calculate;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CapitalDepre
/*     */   extends BaseBean
/*     */ {
/*  19 */   private Calculate calcu = new Calculate();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private double getRatio(String paramString1, String paramString2) throws Exception {
/*  26 */     String str1 = "";
/*  27 */     String str2 = "";
/*  28 */     String str3 = "";
/*  29 */     String str4 = "";
/*  30 */     String str5 = "";
/*  31 */     double d1 = 0.0D;
/*  32 */     double d2 = 0.0D;
/*  33 */     double d3 = 0.0D;
/*  34 */     double d4 = 0.0D;
/*  35 */     double d5 = 0.0D;
/*  36 */     String str6 = "";
/*  37 */     String str7 = "";
/*  38 */     boolean bool = false;
/*     */     
/*  40 */     RecordSet recordSet = new RecordSet();
/*     */     
/*     */     try {
/*  43 */       recordSet.executeProc("CptCapital_SelectByID", paramString1);
/*  44 */       while (recordSet.next()) {
/*  45 */         paramString1 = recordSet.getString("id");
/*  46 */         str1 = recordSet.getString("depremethod1");
/*     */         
/*  48 */         str3 = recordSet.getString("deprestartdate");
/*  49 */         str4 = recordSet.getString("depreenddate");
/*  50 */         if (str3.compareTo(paramString2) > 0) {
/*  51 */           return 0.0D;
/*     */         }
/*  53 */         if (str4.compareTo(paramString2) > 0 || str4.equals("")) {
/*  54 */           str4 = paramString2;
/*     */         }
/*     */         
/*  57 */         d1 = getTimePeriod(str4, str3);
/*     */       } 
/*     */ 
/*     */       
/*  61 */       recordSet.executeProc("CptDepreMethod1_SelectByID", str1);
/*  62 */       while (recordSet.next()) {
/*  63 */         str7 = recordSet.getString("depretype");
/*  64 */         d4 = recordSet.getDouble("timelimit");
/*  65 */         d2 = recordSet.getDouble("startunit");
/*  66 */         d3 = recordSet.getDouble("endunit");
/*  67 */         str5 = recordSet.getString("deprefunc");
/*     */       } 
/*     */ 
/*     */       
/*  71 */       if (str7.equals("1")) {
/*  72 */         double d6 = getFuncValue("" + d4, str5);
/*  73 */         double d7 = getFuncValue("" + d1, str5);
/*     */ 
/*     */ 
/*     */ 
/*     */         
/*  78 */         return d7 / d6 * (d2 - d3);
/*     */       } 
/*     */ 
/*     */       
/*  82 */       if (str7.equals("2")) {
/*  83 */         recordSet.executeProc("CptDepreMethod2_SByDepreID", str1);
/*  84 */         double d = 0.0D;
/*  85 */         while (recordSet.next()) {
/*  86 */           d5 = recordSet.getDouble("time");
/*  87 */           d = recordSet.getDouble("depreunit");
/*  88 */           if (d1 < d5) {
/*  89 */             if (recordSet.previous()) {
/*  90 */               d = recordSet.getDouble("depreunit");
/*  91 */               return d;
/*     */             } 
/*     */             
/*  94 */             return 0.0D;
/*     */           } 
/*     */         } 
/*     */         
/*  98 */         return d;
/*     */       } 
/*     */       
/* 101 */       return -1.0D;
/*     */     
/*     */     }
/* 104 */     catch (Exception exception) {
/* 105 */       writeLog(exception);
/* 106 */       throw exception;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   private double getRatio(String paramString1, String paramString2, String paramString3) throws Exception {
/* 113 */     return getRatio(paramString1, paramString3) - getRatio(paramString1, paramString2);
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   private double getTimePeriod(String paramString1, String paramString2) throws Exception {
/* 119 */     double d1 = 0.0D;
/* 120 */     double d2 = 0.0D;
/* 121 */     double d3 = 0.0D;
/* 122 */     d1 = Double.parseDouble(paramString1.substring(0, 4)) - Double.parseDouble(paramString2.substring(0, 4));
/* 123 */     d2 = Double.parseDouble(paramString1.substring(5, 7)) - Double.parseDouble(paramString2.substring(5, 7));
/* 124 */     d3 = Double.parseDouble(paramString1.substring(8, 10)) - Double.parseDouble(paramString2.substring(8, 10));
/* 125 */     return d1 * 12.0D + d2 + d3 / 30.0D;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   private double getFuncValue(String paramString1, String paramString2) throws Exception {
/* 131 */     double d = 0.0D;
/* 132 */     StringBuffer stringBuffer = new StringBuffer(paramString2);
/* 133 */     for (byte b = 0; b < stringBuffer.length(); b++) {
/* 134 */       if (stringBuffer.charAt(b) == 't') {
/* 135 */         stringBuffer.replace(b, b + 1, "(" + paramString1 + ")");
/*     */       }
/*     */     } 
/* 138 */     d = this.calcu.calculate(stringBuffer.toString());
/* 139 */     this.calcu.clearAll();
/* 140 */     return d;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public double getCapitalDepreRatio(String paramString) throws Exception {
/* 147 */     double d = -1.0D;
/*     */     try {
/* 149 */       Calendar calendar = Calendar.getInstance();
/* 150 */       String str = Util.add0(calendar.get(1), 4) + "-" + Util.add0(calendar.get(2) + 1, 2) + "-" + Util.add0(calendar.get(5), 2);
/*     */ 
/*     */       
/* 153 */       d = getRatio(paramString, str);
/*     */     }
/* 155 */     catch (Exception exception) {
/* 156 */       d = -1.0D;
/*     */     } 
/* 158 */     return d;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public double getCapitalDepreRatio(String paramString1, String paramString2, String paramString3) throws Exception {
/* 164 */     double d = -1.0D;
/*     */     try {
/* 166 */       d = getRatio(paramString1, paramString2, paramString3);
/*     */     }
/* 168 */     catch (Exception exception) {
/* 169 */       d = -1.0D;
/*     */     } 
/* 171 */     return d;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/cpt/capital/CapitalDepre.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */