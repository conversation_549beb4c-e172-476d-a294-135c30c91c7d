/*    */ package weaver.cpt.capital;
/*    */ 
/*    */ import javax.servlet.http.HttpServletRequest;
/*    */ import javax.servlet.http.HttpServletResponse;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.BaseBean;
/*    */ import weaver.hrm.HrmUserVarify;
/*    */ import weaver.hrm.User;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class VerifyPowerToCapital
/*    */   extends BaseBean
/*    */ {
/*    */   private boolean isinit = true;
/* 17 */   private String TableName = "";
/*    */   private User user;
/*    */   private String login_type;
/* 20 */   private String sqlstr = "";
/*    */   
/*    */   private String userID;
/*    */   
/*    */   public String getTableName(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/* 25 */     if (this.isinit) {
/*    */       
/* 27 */       this.user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/* 28 */       this.login_type = this.user.getLogintype();
/* 29 */       this.sqlstr = "";
/* 30 */       this.userID = "" + this.user.getUID();
/* 31 */       this.TableName = "CptCapital" + this.login_type + "Temp" + this.userID;
/* 32 */       getTableNameFromServer();
/* 33 */       this.isinit = false;
/*    */     } 
/* 35 */     return this.TableName;
/*    */   }
/*    */   
/*    */   public void resetTableName() {
/* 39 */     RecordSet recordSet = new RecordSet();
/*    */     
/* 41 */     this.sqlstr = "if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[" + this.TableName + "]') and OBJECTPROPERTY(id, N'IsView') = 1)\tdrop view [dbo].[" + this.TableName + "]";
/* 42 */     recordSet.execute(this.sqlstr);
/* 43 */     this.isinit = true;
/*    */   }
/*    */   
/*    */   private void getTableNameFromServer() throws Exception {
/* 47 */     RecordSet recordSet = new RecordSet();
/* 48 */     String str1 = "" + this.user.getUserDepartment();
/* 49 */     String str2 = "" + this.user.getSeclevel();
/* 50 */     String str3 = "" + this.user.getUserSubCompany1();
/*    */ 
/*    */     
/*    */     try {
/* 54 */       this.sqlstr = "if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[" + this.TableName + "]') and OBJECTPROPERTY(id, N'IsView') = 1)\tdrop view [dbo].[" + this.TableName + "]";
/* 55 */       recordSet.execute(this.sqlstr);
/*    */       
/* 57 */       if (this.login_type.equals("1"))
/*    */       {
/* 59 */         this.sqlstr = " create view " + this.TableName + " as select t1.id from CptCapital  t1,  CptCapitalShareInfo  t2,  HrmRoleMembers  t3 ";
/*    */         
/* 61 */         this.sqlstr += " where  ((t1.id=t2.relateditemid) and ( (t2.foralluser=1 and t2.seclevel<=" + str2 + ") or ( t2.userid=" + this.userID + " ) or (t2.departmentid=" + str1 + " and t2.seclevel<=" + str2 + ") or (t3.resourceid=" + this.userID + " and t3.roleid=t2.roleid and t3.rolelevel>=t2.rolelevel and ( (t2.rolelevel=0 and t1.departmentid=" + str1 + ") or (t2.rolelevel=1 and " + str3 + " in (select subcompanyid1 from HrmDepartment where id = t1.departmentid))  or (t3.rolelevel=2))))) ";
/*    */         
/* 63 */         this.sqlstr += " UNION ";
/*    */         
/* 65 */         this.sqlstr += " select distinct(t1.id) from CptCapital  t1,  HrmRoleMembers  t3,  HrmResource  t4 ";
/*    */         
/* 67 */         this.sqlstr += " where (t1.resourceid=" + this.userID + "  or  (t4.managerid=" + this.userID + " and t4.id=t1.resourceid)  or \t\t(t3.resourceid=" + this.userID + " and t3.roleid=7 and ( (t3.rolelevel=0 and t1.departmentid=" + str1 + ") or (t3.rolelevel=1 and " + str3 + "in (select subcompanyid1 from HrmDepartment where id = t1.departmentid)) or (t3.rolelevel=2))))";
/*    */         
/* 69 */         recordSet.executeSql(this.sqlstr);
/*    */       }
/*    */     
/* 72 */     } catch (Exception exception) {
/* 73 */       writeLog(exception);
/* 74 */       throw exception;
/*    */     } 
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/cpt/capital/VerifyPowerToCapital.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */