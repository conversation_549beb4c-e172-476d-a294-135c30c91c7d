/*    */ package weaver.cpt.capital;
/*    */ 
/*    */ import weaver.cache.CacheBase;
/*    */ import weaver.cache.CacheColumn;
/*    */ import weaver.cache.CacheColumnType;
/*    */ import weaver.cache.PKColumn;
/*    */ 
/*    */ public class CapitalModifyFieldComInfo
/*    */   extends CacheBase {
/* 10 */   protected static String TABLE_NAME = "CptCapitalModifyField";
/*    */   
/* 12 */   protected static String TABLE_WHERE = null;
/*    */   
/* 14 */   protected static String TABLE_ORDER = "field ASC";
/*    */   
/*    */   @PKColumn(type = CacheColumnType.NUMBER)
/* 17 */   protected static String PK_NAME = "field";
/*    */   
/*    */   @CacheColumn
/*    */   protected static int name;
/*    */ 
/*    */   
/*    */   public int getCapitalModifyFieldNum() {
/* 24 */     return size();
/*    */   }
/*    */   
/*    */   public boolean next() {
/* 28 */     return super.next();
/*    */   }
/*    */   
/*    */   public boolean next(String paramString) {
/* 32 */     setTofirstRow();
/* 33 */     return false;
/*    */   }
/*    */   
/*    */   public void setTofirstRow() {
/* 37 */     super.setTofirstRow();
/*    */   }
/*    */   
/*    */   public String getCapitalModifyFieldid() {
/* 41 */     return (String)getRowValue(0);
/*    */   }
/*    */   
/*    */   public String getCapitalModifyFieldname() {
/* 45 */     return (String)getRowValue(name);
/*    */   }
/*    */ 
/*    */   
/*    */   public String getCapitalModifyFieldname(String paramString) {
/* 50 */     return (String)getValue(name, paramString);
/*    */   }
/*    */   
/*    */   public void removeCapitalModifyFieldCache() {
/* 54 */     removeCache();
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/cpt/capital/CapitalModifyFieldComInfo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */