/*     */ package weaver.cpt.car;
/*     */ 
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CarWorkTimeCalculate
/*     */   extends BaseBean
/*     */ {
/*     */   private String starttime;
/*     */   private String backtime;
/*     */   private String workstarttime;
/*     */   private String workendtime;
/*     */   private float normaltime;
/*     */   private float overtime;
/*     */   private float totaltime;
/*     */   private int weekday;
/*     */   
/*     */   public CarWorkTimeCalculate() {
/*  38 */     resetParameter();
/*     */   }
/*     */   
/*     */   public float getNormaltime() {
/*  42 */     return this.normaltime;
/*     */   }
/*     */   
/*     */   public float getOvertime() {
/*  46 */     return this.overtime;
/*     */   }
/*     */   
/*     */   public float getTotaltime() {
/*  50 */     return this.totaltime;
/*     */   }
/*     */   
/*     */   public void resetParameter() {
/*  54 */     this.starttime = "";
/*  55 */     this.backtime = "";
/*  56 */     this.workstarttime = "";
/*  57 */     this.workendtime = "";
/*  58 */     this.normaltime = 0.0F;
/*  59 */     this.overtime = 0.0F;
/*  60 */     this.totaltime = 0.0F;
/*  61 */     this.weekday = 0;
/*     */   }
/*     */   
/*     */   public void setBacktime(String paramString) {
/*  65 */     this.backtime = paramString;
/*     */   }
/*     */   
/*     */   public void setStarttime(String paramString) {
/*  69 */     this.starttime = paramString;
/*     */   }
/*     */   
/*     */   public void setWeekday(int paramInt) {
/*  73 */     this.weekday = paramInt;
/*     */   }
/*     */   
/*     */   public void setWorkendtime(String paramString) {
/*  77 */     this.workendtime = paramString;
/*     */   }
/*     */   
/*     */   public void setWorkstarttime(String paramString) {
/*  81 */     this.workstarttime = paramString;
/*     */   }
/*     */   
/*     */   public void timeCalculate() {
/*  85 */     String str1 = Util.subTime(this.backtime, this.starttime);
/*  86 */     this.totaltime = timestrTofloat(str1);
/*  87 */     String str2 = "00:00";
/*  88 */     String str3 = "00:00";
/*  89 */     if (this.weekday == 1 || this.weekday == 7 || this.weekday == 8) {
/*  90 */       str2 = str1;
/*  91 */       str3 = "00:00";
/*     */     } else {
/*  93 */       if (this.backtime.compareTo(this.workstarttime) < 0 || this.starttime.compareTo(this.workendtime) > 0) {
/*     */         
/*  95 */         str2 = Util.subTime(this.backtime, this.starttime);
/*  96 */         str3 = "00:00";
/*     */       } 
/*  98 */       if (this.starttime.compareTo(this.workstarttime) < 0 && this.backtime.compareTo(this.workstarttime) > 0 && this.backtime.compareTo(this.workendtime) <= 0) {
/*     */ 
/*     */         
/* 101 */         str2 = Util.subTime(this.workstarttime, this.starttime);
/* 102 */         str3 = Util.subTime(this.backtime, this.workstarttime);
/*     */       } 
/* 104 */       if (this.starttime.compareTo(this.workstarttime) >= 0 && this.backtime.compareTo(this.workendtime) <= 0) {
/*     */         
/* 106 */         str2 = "00:00";
/* 107 */         str3 = Util.subTime(this.backtime, this.starttime);
/*     */       } 
/* 109 */       if (this.starttime.compareTo(this.workstarttime) >= 0 && this.starttime.compareTo(this.workendtime) <= 0 && this.backtime.compareTo(this.workendtime) > 0) {
/*     */ 
/*     */         
/* 112 */         str2 = Util.subTime(this.backtime, this.workendtime);
/* 113 */         str3 = Util.subTime(this.workendtime, this.starttime);
/*     */       } 
/* 115 */       if (this.starttime.compareTo(this.workstarttime) <= 0 && this.backtime.compareTo(this.workendtime) >= 0) {
/*     */         
/* 117 */         str2 = Util.addTime(Util.subTime(this.backtime, this.workendtime), Util.subTime(this.workstarttime, this.starttime));
/*     */         
/* 119 */         str3 = Util.subTime(this.workendtime, this.workstarttime);
/*     */       } 
/*     */     } 
/* 122 */     this.normaltime = timestrTofloat(str3);
/* 123 */     this.overtime = timestrTofloat(str2);
/*     */   }
/*     */   
/*     */   public float timestrTofloat(String paramString) {
/* 127 */     int i = Util.getIntValue(paramString.substring(0, 2));
/* 128 */     int j = Util.getIntValue(paramString.substring(3));
/* 129 */     float f = (j * 100 / 60) / 100.0F;
/* 130 */     return i + f;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/cpt/car/CarWorkTimeCalculate.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */