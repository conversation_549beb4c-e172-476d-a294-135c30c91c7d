/*    */ package weaver.cpt.car;
/*    */ 
/*    */ import java.util.ArrayList;
/*    */ import java.util.List;
/*    */ import weaver.general.BaseBean;
/*    */ import weaver.general.ThreadVarLanguage;
/*    */ import weaver.general.Util;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class UseCarWorkflowSet
/*    */   extends BaseBean
/*    */ {
/*    */   public List<String> getOperate(String paramString1, String paramString2, String paramString3) {
/* 19 */     ArrayList<String> arrayList = new ArrayList();
/* 20 */     paramString2 = Util.null2String(paramString2.trim(), "1");
/* 21 */     if ("".equals(paramString2)) paramString2 = "1"; 
/* 22 */     if ("163".equals(paramString3.trim()) || "".equals(Util.null2String(paramString3))) {
/* 23 */       arrayList.add("0".equals(paramString2) ? "true" : "false");
/* 24 */       arrayList.add("1".equals(paramString2) ? "true" : "false");
/* 25 */       arrayList.add("false");
/* 26 */       arrayList.add("true");
/* 27 */       arrayList.add("false");
/*    */     } else {
/* 29 */       arrayList.add("0".equals(paramString2) ? "true" : "false");
/* 30 */       arrayList.add("1".equals(paramString2) ? "true" : "false");
/* 31 */       arrayList.add("true");
/* 32 */       arrayList.add("true");
/* 33 */       arrayList.add("true");
/*    */     } 
/*    */     
/* 36 */     return arrayList;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public List<String> getOperate(String paramString1, String paramString2) {
/* 46 */     ArrayList<String> arrayList = new ArrayList();
/* 47 */     paramString2 = Util.null2String(paramString2).trim();
/* 48 */     String[] arrayOfString = paramString2.split("\\+");
/* 49 */     String str1 = "";
/* 50 */     String str2 = "";
/* 51 */     if (arrayOfString.length > 0) {
/* 52 */       str1 = Util.null2String(arrayOfString[0]);
/*    */     }
/* 54 */     if (arrayOfString.length > 1) {
/* 55 */       str2 = Util.null2String(arrayOfString[1], "-1");
/*    */     }
/* 57 */     if ("".equals(str1)) str1 = "1"; 
/* 58 */     if ("163".equals(str2.trim()) || "".equals(Util.null2String(str2))) {
/* 59 */       arrayList.add("0".equals(str1) ? "true" : "false");
/* 60 */       arrayList.add("1".equals(str1) ? "true" : "false");
/* 61 */       arrayList.add("false");
/* 62 */       arrayList.add("true");
/* 63 */       arrayList.add("false");
/*    */     } else {
/* 65 */       arrayList.add("0".equals(str1) ? "true" : "false");
/* 66 */       arrayList.add("1".equals(str1) ? "true" : "false");
/* 67 */       arrayList.add("true");
/* 68 */       arrayList.add("true");
/* 69 */       arrayList.add("true");
/*    */     } 
/*    */     
/* 72 */     return arrayList;
/*    */   }
/*    */   
/*    */   public String getUsename(String paramString) {
/* 76 */     if ("1".equals(paramString) || "".equals(Util.null2String(paramString))) {
/* 77 */       return "" + SystemEnv.getHtmlLabelName(26472, ThreadVarLanguage.getLang()) + "";
/*    */     }
/* 79 */     return "" + SystemEnv.getHtmlLabelName(32386, ThreadVarLanguage.getLang()) + "";
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/cpt/car/UseCarWorkflowSet.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */