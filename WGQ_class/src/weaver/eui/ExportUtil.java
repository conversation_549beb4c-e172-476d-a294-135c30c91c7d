/*     */ package weaver.eui;
/*     */ 
/*     */ import com.lowagie.text.Cell;
/*     */ import com.lowagie.text.Document;
/*     */ import com.lowagie.text.Element;
/*     */ import com.lowagie.text.Font;
/*     */ import com.lowagie.text.HeaderFooter;
/*     */ import com.lowagie.text.PageSize;
/*     */ import com.lowagie.text.Paragraph;
/*     */ import com.lowagie.text.Phrase;
/*     */ import com.lowagie.text.Rectangle;
/*     */ import com.lowagie.text.Table;
/*     */ import com.lowagie.text.pdf.BaseFont;
/*     */ import com.lowagie.text.pdf.PdfWriter;
/*     */ import java.awt.Color;
/*     */ import java.io.ByteArrayOutputStream;
/*     */ import org.apache.poi.hssf.usermodel.HSSFCell;
/*     */ import org.apache.poi.hssf.usermodel.HSSFCellStyle;
/*     */ import org.apache.poi.hssf.usermodel.HSSFFont;
/*     */ import org.apache.poi.hssf.usermodel.HSSFRow;
/*     */ import org.apache.poi.hssf.usermodel.HSSFSheet;
/*     */ import org.apache.poi.hssf.usermodel.HSSFWorkbook;
/*     */ import org.apache.poi.hssf.util.HSSFColor;
/*     */ import org.json.JSONArray;
/*     */ import org.json.JSONObject;
/*     */ import weaver.general.ThreadVarLanguage;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ public class ExportUtil
/*     */ {
/*     */   public static HSSFWorkbook exportExcel(JSONArray paramJSONArray, String paramString1, String paramString2, String paramString3) {
/*  32 */     HSSFWorkbook hSSFWorkbook = new HSSFWorkbook();
/*     */     
/*     */     try {
/*  35 */       String[] arrayOfString1 = paramString1.split(",");
/*  36 */       String[] arrayOfString2 = paramString2.split(",");
/*     */       
/*  38 */       HSSFSheet hSSFSheet = hSSFWorkbook.createSheet();
/*     */       
/*  40 */       hSSFWorkbook.setSheetName(0, paramString3);
/*     */       
/*  42 */       HSSFRow hSSFRow = hSSFSheet.createRow(0);
/*     */ 
/*     */       
/*  45 */       HSSFCellStyle hSSFCellStyle = hSSFWorkbook.createCellStyle();
/*     */ 
/*     */       
/*  48 */       HSSFFont hSSFFont = hSSFWorkbook.createFont();
/*     */ 
/*     */       
/*  51 */       hSSFFont.setBold(false);
/*  52 */       hSSFCellStyle.setFont(hSSFFont);
/*     */       
/*  54 */       hSSFCellStyle.setFillBackgroundColor(HSSFColor.HSSFColorPredefined.BLUE.getIndex());
/*  55 */       hSSFCellStyle.setFillForegroundColor(HSSFColor.HSSFColorPredefined.RED.getIndex());
/*     */       
/*     */       byte b;
/*  58 */       for (b = 0; b < arrayOfString1.length; b++) {
/*     */ 
/*     */         
/*  61 */         hSSFSheet.setColumnWidth((short)b, (short)(Integer.parseInt(arrayOfString2[b]) * 256));
/*  62 */         HSSFCell hSSFCell = hSSFRow.createCell((short)b);
/*     */         
/*  64 */         hSSFCell.setCellStyle(hSSFCellStyle);
/*  65 */         hSSFCell.setCellValue(arrayOfString1[b]);
/*     */       } 
/*     */ 
/*     */       
/*  69 */       for (b = 0; b < paramJSONArray.length(); b++) {
/*  70 */         JSONObject jSONObject = (JSONObject)paramJSONArray.get(b);
/*     */         
/*  72 */         hSSFRow = hSSFSheet.createRow(b + 1);
/*  73 */         String[] arrayOfString = (String[])jSONObject.get("cell");
/*  74 */         for (byte b1 = 0; b1 < arrayOfString.length; b1++) {
/*  75 */           HSSFCell hSSFCell = hSSFRow.createCell((short)b1);
/*  76 */           hSSFCell.setCellValue(arrayOfString[b1]);
/*     */         } 
/*     */       } 
/*     */       
/*  80 */       return hSSFWorkbook;
/*     */     }
/*  82 */     catch (Exception exception) {
/*  83 */       exception.printStackTrace();
/*  84 */       return null;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static ByteArrayOutputStream exportPdf(JSONArray paramJSONArray, String paramString1, String paramString2, String paramString3) {
/*     */     try {
/*  93 */       Rectangle rectangle = PageSize.A4;
/*     */ 
/*     */ 
/*     */       
/*  97 */       Document document = new Document(rectangle, 36.0F, 36.0F, 36.0F, 36.0F);
/*     */       
/*  99 */       ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
/*     */       
/* 101 */       PdfWriter pdfWriter = PdfWriter.getInstance(document, byteArrayOutputStream);
/*     */ 
/*     */       
/* 104 */       document.addTitle(new String(SystemEnv.getHtmlLabelName(25496, ThreadVarLanguage.getLang()).getBytes("UTF-8"), "iso-8859-1"));
/*     */       
/* 106 */       document.addAuthor("feng");
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 111 */       HeaderFooter headerFooter1 = new HeaderFooter(new Phrase("create by weaver E-cology"), false);
/*     */       
/* 113 */       document.setHeader(headerFooter1);
/*     */       
/* 115 */       HeaderFooter headerFooter2 = new HeaderFooter(new Phrase("create by weaver E-cology"), false);
/*     */       
/* 117 */       document.setFooter(headerFooter2);
/*     */ 
/*     */ 
/*     */       
/* 121 */       document.open();
/*     */ 
/*     */       
/* 124 */       BaseFont baseFont = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", false);
/*     */       
/* 126 */       Font font = new Font(baseFont, 12.0F, 0);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 139 */       String[] arrayOfString = paramString1.split(",");
/*     */       
/* 141 */       Table table = new Table(arrayOfString.length, paramJSONArray.length() + 2);
/*     */       
/* 143 */       table.setBorder(2);
/* 144 */       table.setBorderColor(new Color(0, 0, 255));
/*     */ 
/*     */       
/* 147 */       Cell cell = new Cell(paramString3);
/*     */       
/* 149 */       cell.setColspan(arrayOfString.length);
/*     */ 
/*     */       
/* 152 */       cell.setHorizontalAlignment(1);
/*     */       
/* 154 */       cell.setVerticalAlignment(5);
/*     */       
/* 156 */       table.addCell(cell);
/*     */       
/*     */       byte b;
/* 159 */       for (b = 0; b < arrayOfString.length; b++) {
/* 160 */         Cell cell1 = new Cell((Element)new Paragraph(arrayOfString[b], font));
/* 161 */         cell1.setHeader(true);
/* 162 */         cell1.setBackgroundColor(new Color(0, 0, 255));
/* 163 */         table.addCell(cell1);
/*     */       } 
/*     */ 
/*     */       
/* 167 */       for (b = 0; b < paramJSONArray.length(); b++) {
/*     */         
/* 169 */         JSONObject jSONObject = (JSONObject)paramJSONArray.get(b);
/*     */         
/* 171 */         String[] arrayOfString1 = (String[])jSONObject.get("cell");
/*     */         
/* 173 */         for (byte b1 = 0; b1 < arrayOfString1.length; b1++) {
/* 174 */           table.addCell(arrayOfString1[b1]);
/*     */         }
/*     */       } 
/* 177 */       document.add((Element)table);
/* 178 */       document.close();
/* 179 */       return byteArrayOutputStream;
/*     */     }
/* 181 */     catch (Exception exception) {
/* 182 */       exception.printStackTrace();
/* 183 */       return null;
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/eui/ExportUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */