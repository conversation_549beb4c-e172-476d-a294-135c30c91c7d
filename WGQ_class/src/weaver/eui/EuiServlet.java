/*     */ package weaver.eui;
/*     */ 
/*     */ import java.io.ByteArrayOutputStream;
/*     */ import java.io.IOException;
/*     */ import java.io.OutputStream;
/*     */ import java.io.PrintWriter;
/*     */ import java.lang.reflect.Constructor;
/*     */ import java.util.HashMap;
/*     */ import javax.servlet.ServletException;
/*     */ import javax.servlet.ServletOutputStream;
/*     */ import javax.servlet.http.HttpServlet;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import org.apache.log4j.Logger;
/*     */ import org.apache.poi.hssf.usermodel.HSSFWorkbook;
/*     */ import org.json.JSONArray;
/*     */ import org.json.JSONObject;
/*     */ import weaver.eui.data.table.AbsTable;
/*     */ import weaver.eui.data.tree.AbsTree;
/*     */ import weaver.eui.data.uploader.AbsUploader;
/*     */ import weaver.general.Util;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class EuiServlet
/*     */   extends HttpServlet
/*     */ {
/*  36 */   private static Logger logger = Logger.getLogger(EuiServlet.class);
/*     */ 
/*     */   
/*     */   public void doGet(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws IOException, ServletException {
/*  40 */     paramHttpServletRequest.setCharacterEncoding("UTF-8");
/*  41 */     String str1 = Util.null2String(paramHttpServletRequest.getParameter("cmp"));
/*  42 */     String str2 = Util.null2String(paramHttpServletRequest.getParameter("operate"));
/*     */     try {
/*  44 */       if ("table".equals(str1)) {
/*  45 */         String str3 = Util.null2String(paramHttpServletRequest.getParameter("classData"));
/*  46 */         String str4 = Util.null2String(paramHttpServletRequest.getParameter("order"));
/*  47 */         String str5 = Util.null2String(paramHttpServletRequest.getParameter("orderby"));
/*  48 */         if ("getCurrentPage".equals(str2)) {
/*  49 */           int i = Util.getIntValue(paramHttpServletRequest.getParameter("pageIndex"), 0);
/*  50 */           int j = Util.getIntValue(paramHttpServletRequest.getParameter("pageSize"), 0);
/*     */           
/*  52 */           JSONObject jSONObject = new JSONObject();
/*     */           try {
/*  54 */             Class<?> clazz = Class.forName(str3);
/*  55 */             Constructor<?> constructor = clazz.getConstructor(new Class[] { HttpServletRequest.class, HttpServletResponse.class });
/*  56 */             AbsTable absTable = (AbsTable)constructor.newInstance(new Object[] { paramHttpServletRequest, paramHttpServletResponse });
/*     */             
/*  58 */             jSONObject.put("totalRecords", absTable.getTotal());
/*  59 */             jSONObject.put("pageIndex", i);
/*  60 */             jSONObject.put("pageSize", j);
/*  61 */             jSONObject.put("rows", absTable.getCurrentPageData(i, j, str4, str5));
/*     */           }
/*  63 */           catch (Exception exception) {
/*  64 */             logger.error(exception);
/*     */           } 
/*  66 */           paramHttpServletResponse.setContentType("application/x-json; charset=UTF-8");
/*  67 */           PrintWriter printWriter = paramHttpServletResponse.getWriter();
/*  68 */           printWriter.print(jSONObject);
/*  69 */         } else if ("exporte".equals(str2)) {
/*  70 */           String str = Util.null2String(paramHttpServletRequest.getParameter("operateState"));
/*  71 */           if ("session".equals(str)) {
/*  72 */             String str6 = Util.getEncrypt("euiTableSession_" + Util.getRandom());
/*  73 */             HashMap<Object, Object> hashMap = new HashMap<Object, Object>();
/*  74 */             hashMap.put("type", Util.null2String(paramHttpServletRequest.getParameter("type")));
/*  75 */             hashMap.put("colNames", Util.null2String(paramHttpServletRequest.getParameter("colNames")));
/*  76 */             hashMap.put("colWidths", Util.null2String(paramHttpServletRequest.getParameter("colWidths")));
/*  77 */             hashMap.put("caption", Util.null2String(paramHttpServletRequest.getParameter("caption")));
/*  78 */             hashMap.put("classData", Util.null2String(paramHttpServletRequest.getParameter("classData")));
/*     */             
/*  80 */             paramHttpServletRequest.getSession().setAttribute(str6, hashMap);
/*  81 */             PrintWriter printWriter = paramHttpServletResponse.getWriter();
/*     */             
/*  83 */             printWriter.print(str6);
/*     */           } else {
/*  85 */             String str6 = Util.null2String(paramHttpServletRequest.getParameter("sessionId"));
/*  86 */             HashMap hashMap = (HashMap)paramHttpServletRequest.getSession().getAttribute(str6);
/*     */             
/*  88 */             String str7 = Util.null2String((String)hashMap.get("type"));
/*  89 */             String str8 = Util.null2String((String)hashMap.get("colNames"));
/*  90 */             String str9 = Util.null2String((String)hashMap.get("colWidths"));
/*  91 */             String str10 = Util.null2String((String)hashMap.get("caption"));
/*  92 */             String str11 = Util.null2String((String)hashMap.get("classData"));
/*     */             try {
/*  94 */               Class<?> clazz = Class.forName(str11);
/*  95 */               Constructor<?> constructor = clazz.getConstructor(new Class[] { HttpServletRequest.class, HttpServletResponse.class });
/*  96 */               AbsTable absTable = (AbsTable)constructor.newInstance(new Object[] { paramHttpServletRequest, paramHttpServletResponse });
/*     */               
/*  98 */               int i = Util.getIntValue(paramHttpServletRequest.getParameter("page"), 1);
/*  99 */               int j = Util.getIntValue(paramHttpServletRequest.getParameter("rows"), 10);
/*     */               
/* 101 */               JSONArray jSONArray = absTable.getCurrentPageData(i, j, str4, str5);
/*     */ 
/*     */               
/* 104 */               if ("pdf".equals(str7)) {
/* 105 */                 ByteArrayOutputStream byteArrayOutputStream = ExportUtil.exportPdf(jSONArray, str8, str9, str10);
/* 106 */                 if (byteArrayOutputStream != null) {
/* 107 */                   paramHttpServletResponse.setContentType("application/x-msdownload;charset=UTF-8");
/* 108 */                   paramHttpServletResponse.setHeader("Content-Disposition", "attachment;filename=test.pdf");
/* 109 */                   paramHttpServletResponse.setContentLength(byteArrayOutputStream.size());
/* 110 */                   ServletOutputStream servletOutputStream = paramHttpServletResponse.getOutputStream();
/* 111 */                   byteArrayOutputStream.writeTo((OutputStream)servletOutputStream);
/* 112 */                   servletOutputStream.flush();
/* 113 */                   servletOutputStream.close();
/*     */                 } else {
/*     */                   
/* 116 */                   PrintWriter printWriter = paramHttpServletResponse.getWriter();
/* 117 */                   printWriter.print("<script>parent.$.euiAlert('Hello Hunk', 'HH')</script>");
/*     */                 
/*     */                 }
/*     */               
/*     */               }
/* 122 */               else if ("excel".equals(str7)) {
/* 123 */                 paramHttpServletResponse.setContentType("application/vnd.ms-excel;charset=utf-8");
/* 124 */                 paramHttpServletResponse.setHeader("Content-Disposition", "attachment;filename=" + new String("系统日志.xls".getBytes(), "iso-8859-1"));
/* 125 */                 ServletOutputStream servletOutputStream = paramHttpServletResponse.getOutputStream();
/* 126 */                 HSSFWorkbook hSSFWorkbook = ExportUtil.exportExcel(jSONArray, str8, str9, str10);
/* 127 */                 hSSFWorkbook.write((OutputStream)servletOutputStream);
/* 128 */                 servletOutputStream.flush();
/* 129 */                 servletOutputStream.close();
/*     */               } 
/* 131 */             } catch (Exception exception) {
/* 132 */               logger.error(exception);
/*     */             } 
/*     */           } 
/*     */         } 
/* 136 */       } else if ("tree".equals(str1)) {
/* 137 */         if ("getTreeData".equals(str2)) {
/* 138 */           String str3 = Util.null2String(paramHttpServletRequest.getParameter("classData"));
/* 139 */           String str4 = Util.null2String(paramHttpServletRequest.getParameter("root"));
/* 140 */           JSONArray jSONArray = new JSONArray();
/*     */           try {
/* 142 */             Class<?> clazz = Class.forName(str3);
/* 143 */             Constructor<?> constructor = clazz.getConstructor(new Class[] { HttpServletRequest.class, HttpServletResponse.class });
/* 144 */             AbsTree absTree = (AbsTree)constructor.newInstance(new Object[] { paramHttpServletRequest, paramHttpServletResponse });
/* 145 */             jSONArray = absTree.getTreeData(str4);
/*     */           }
/* 147 */           catch (Exception exception) {
/* 148 */             logger.error(exception);
/*     */           } 
/* 150 */           paramHttpServletResponse.setContentType("application/x-json; charset=UTF-8");
/* 151 */           PrintWriter printWriter = paramHttpServletResponse.getWriter();
/* 152 */           printWriter.print(jSONArray);
/*     */         } 
/* 154 */       } else if ("uploader".equals(str1) && 
/* 155 */         "uploadfile".equals(str2)) {
/* 156 */         String str = Util.null2String(paramHttpServletRequest.getParameter("classData"));
/*     */         
/* 158 */         int i = -1;
/*     */         try {
/* 160 */           Class<?> clazz = Class.forName(str);
/* 161 */           Constructor<?> constructor = clazz.getConstructor(new Class[] { HttpServletRequest.class, HttpServletResponse.class });
/* 162 */           AbsUploader absUploader = (AbsUploader)constructor.newInstance(new Object[] { paramHttpServletRequest, paramHttpServletResponse });
/* 163 */           i = absUploader.getImageFileId();
/* 164 */         } catch (Exception exception) {
/* 165 */           logger.error(exception);
/*     */         } 
/* 167 */         paramHttpServletResponse.setContentType("application/x-json; charset=UTF-8");
/* 168 */         PrintWriter printWriter = paramHttpServletResponse.getWriter();
/* 169 */         printWriter.print(i);
/*     */       }
/*     */     
/*     */     }
/* 173 */     catch (Exception exception) {
/* 174 */       logger.error(exception);
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   public void doPost(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws IOException, ServletException {
/* 180 */     doGet(paramHttpServletRequest, paramHttpServletResponse);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/eui/EuiServlet.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */