/*     */ package weaver.eui.data.table;
/*     */ 
/*     */ import java.io.StringReader;
/*     */ import java.util.ArrayList;
/*     */ import java.util.List;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import org.jdom.Document;
/*     */ import org.jdom.Element;
/*     */ import org.jdom.input.SAXBuilder;
/*     */ import org.json.JSONArray;
/*     */ import org.json.JSONObject;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.SplitPageParaBean;
/*     */ import weaver.general.SplitPageUtil;
/*     */ import weaver.general.Util;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class SplitePageTable
/*     */   extends AbsTable
/*     */ {
/*     */   public SplitePageTable(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/*  29 */     super(paramHttpServletRequest, paramHttpServletResponse);
/*     */   }
/*     */ 
/*     */   
/*     */   public JSONArray getAllData(String paramString1, String paramString2) throws Exception {
/*  34 */     JSONArray jSONArray = new JSONArray();
/*     */ 
/*     */     
/*  37 */     for (byte b = 0; b < 100; b++) {
/*  38 */       JSONObject jSONObject = new JSONObject();
/*  39 */       jSONObject.put("id", "row");
/*  40 */       jSONObject.put("value", new String[] { "Hunk" + b, "28", "HaHa" });
/*  41 */       jSONArray.put(jSONObject);
/*     */     } 
/*  43 */     return jSONArray;
/*     */   }
/*     */ 
/*     */   
/*     */   public JSONArray getCurrentPageData(int paramInt1, int paramInt2, String paramString1, String paramString2) throws Exception {
/*  48 */     String str1 = getPara("sessionId");
/*  49 */     String str2 = getPara("selectedstrs");
/*  50 */     String str3 = getPara("mode");
/*     */     
/*  52 */     SAXBuilder sAXBuilder = new SAXBuilder();
/*  53 */     Document document = sAXBuilder.build(new StringReader((String)this.req.getSession(true).getAttribute(str1)));
/*     */     
/*  55 */     Element element1 = document.getRootElement();
/*  56 */     Element element2 = element1.getChild("head");
/*  57 */     Element element3 = element1.getChild("sql");
/*  58 */     Element element4 = element1.getChild("operates");
/*     */ 
/*     */     
/*  61 */     Element element5 = element1.getChild("checkboxpopedom");
/*  62 */     String str4 = Util.null2String(element1.getAttributeValue("tabletype"));
/*     */     
/*  64 */     String str5 = Util.toSqlForSplitPage(Util.null2String(element3.getAttributeValue("backfields")));
/*  65 */     String str6 = Util.toSqlForSplitPage(Util.null2String(element3.getAttributeValue("sqlform")));
/*  66 */     String str7 = Util.toSqlForSplitPage(Util.null2String(element3.getAttributeValue("sqlwhere")));
/*  67 */     String str8 = Util.null2String(element3.getAttributeValue("sqlorderby"));
/*  68 */     String str9 = Util.null2String(element3.getAttributeValue("sqlgroupby"));
/*  69 */     String str10 = Util.null2String(element3.getAttributeValue("sqlprimarykey"));
/*  70 */     String str11 = Util.null2String(element3.getAttributeValue("poolname"));
/*     */     
/*  72 */     boolean bool1 = "true".equalsIgnoreCase(element3.getAttributeValue("sqlisdistinct")) ? true : false;
/*  73 */     boolean bool2 = "true".equalsIgnoreCase(element3.getAttributeValue("sqlisprintsql")) ? true : false;
/*  74 */     if ("true".equalsIgnoreCase(str3)) bool2 = true;
/*     */     
/*  76 */     SplitPageParaBean splitPageParaBean = new SplitPageParaBean();
/*  77 */     SplitPageUtil splitPageUtil = new SplitPageUtil();
/*     */     
/*  79 */     splitPageParaBean.setBackFields(str5);
/*  80 */     splitPageParaBean.setSqlFrom(str6);
/*  81 */     splitPageParaBean.setSqlWhere(str7);
/*  82 */     splitPageParaBean.setSqlGroupBy(str9);
/*  83 */     splitPageParaBean.setPrimaryKey(str10);
/*  84 */     splitPageParaBean.setSqlOrderBy(paramString1);
/*  85 */     splitPageParaBean.setSortWay(paramString2.equalsIgnoreCase("DESC") ? 1 : 0);
/*     */     
/*  87 */     splitPageParaBean.setDistinct(bool1);
/*  88 */     splitPageParaBean.setIsPrintExecuteSql(bool2);
/*  89 */     splitPageParaBean.setPoolname(str11);
/*     */     
/*  91 */     splitPageUtil.setSpp(splitPageParaBean);
/*     */ 
/*     */     
/*  94 */     JSONArray jSONArray = new JSONArray();
/*  95 */     List<Element> list = element2.getChildren();
/*  96 */     RecordSet recordSet = splitPageUtil.getCurrentPageRs(paramInt1, paramInt2);
/*  97 */     while (recordSet.next()) {
/*  98 */       JSONObject jSONObject = new JSONObject();
/*  99 */       jSONObject.put("id", recordSet.getString(str10));
/* 100 */       int i = list.size();
/* 101 */       if ("checkbox".equalsIgnoreCase(str4) || "radio".equalsIgnoreCase(str4)) i++; 
/* 102 */       if (element4 != null) i++; 
/* 103 */       String[] arrayOfString = new String[i];
/*     */       
/* 105 */       if ("checkbox".equalsIgnoreCase(str4)) {
/* 106 */         String str12 = Util.null2String(recordSet.getString(str10));
/* 107 */         if (element5 != null) {
/* 108 */           String str13 = element5.getAttributeValue("showmethod");
/* 109 */           String str14 = getRealOtherPara(element5.getAttributeValue("popedompara"), recordSet);
/* 110 */           String str15 = Util.useSpecialTreat(str13, str14);
/* 111 */           if ("true".equalsIgnoreCase(str15)) {
/* 112 */             if (("," + str2 + ",").indexOf("," + str12 + ",") != -1) {
/* 113 */               arrayOfString[0] = "<input type='checkbox' class='chkReturn' checked value='" + str12 + "'>";
/*     */             } else {
/* 115 */               arrayOfString[0] = "<input type='checkbox' class='chkReturn' value='" + str12 + "'>";
/*     */             } 
/*     */           } else {
/* 118 */             arrayOfString[0] = "";
/*     */           }
/*     */         
/* 121 */         } else if (("," + str2 + ",").indexOf("," + str12 + ",") != -1) {
/* 122 */           arrayOfString[0] = "<input type='checkbox' class='chkReturn' checked value='" + str12 + "'>";
/*     */         } else {
/* 124 */           arrayOfString[0] = "<input type='checkbox' class='chkReturn' value='" + str12 + "'>";
/*     */         }
/*     */       
/* 127 */       } else if ("radio".equalsIgnoreCase(str4)) {
/* 128 */         String str12 = Util.null2String(recordSet.getString(str10));
/* 129 */         if (("," + str2 + ",").indexOf("," + str12 + ",") != -1) {
/* 130 */           arrayOfString[0] = "<input type='radio' checked value='" + str12 + "'>";
/*     */         } else {
/* 132 */           arrayOfString[0] = "<input type='radio' value='" + str12 + "'>";
/*     */         } 
/*     */       } 
/*     */       
/* 136 */       for (byte b = 0; b < list.size(); b++) {
/* 137 */         Element element = list.get(b);
/* 138 */         String str12 = element.getAttributeValue("column");
/* 139 */         String str13 = element.getAttributeValue("linkvaluecolumn");
/* 140 */         String str14 = element.getAttributeValue("linkkey");
/* 141 */         String str15 = element.getAttributeValue("target");
/* 142 */         String str16 = element.getAttributeValue("transmethod");
/* 143 */         String str17 = getRealOtherPara(element.getAttributeValue("otherpara"), recordSet);
/* 144 */         if (str13 == null || "".equals(str13.trim()))
/* 145 */           str13 = str12; 
/* 146 */         if (str14 == null || "".equals(str14.trim())) {
/* 147 */           str14 = str12;
/*     */         }
/* 149 */         String str18 = Util.null2String(recordSet.getString(str12));
/* 150 */         String str19 = Util.null2String(recordSet.getString(str13));
/* 151 */         String str20 = "";
/* 152 */         if (str17 == null) {
/* 153 */           str20 = Util.useSpecialTreat(str16, str18);
/*     */         } else {
/* 155 */           str20 = Util.useSpecialTreat(str16, str18, str17);
/*     */         } 
/* 157 */         if ("checkbox".equalsIgnoreCase(str4) || "radio".equalsIgnoreCase(str4)) {
/* 158 */           arrayOfString[b + 1] = str20;
/*     */         } else {
/* 160 */           arrayOfString[b] = str20;
/*     */         } 
/*     */       } 
/*     */ 
/*     */       
/* 165 */       String str = "";
/* 166 */       if (element4 != null) {
/* 167 */         Element element6 = element4.getChild("popedom");
/* 168 */         List<Element> list1 = element4.getChildren("operate");
/*     */         
/* 170 */         Element element7 = new Element("operates");
/*     */         
/* 172 */         if (element6 != null) {
/* 173 */           String str12 = element6.getAttributeValue("transmethod");
/* 174 */           String str13 = element6.getAttributeValue("column");
/* 175 */           String str14 = element6.getAttributeValue("otherpara");
/* 176 */           String str15 = element6.getAttributeValue("otherpara2");
/* 177 */           ArrayList<String> arrayList = new ArrayList();
/* 178 */           if (str13 == null) str13 = str10; 
/* 179 */           if (str14 == null && str15 == null) {
/* 180 */             arrayList = Util.useSpecialTreatArrayList(str12, recordSet.getString(str13));
/* 181 */           } else if (str14 != null && str15 == null) {
/* 182 */             arrayList = Util.useSpecialTreatArrayList(str12, recordSet.getString(str13), getRealOtherPara(str14, recordSet));
/* 183 */           } else if (str14 != null && str15 != null) {
/* 184 */             arrayList = Util.useSpecialTreatArrayList(str12, recordSet.getString(str13), getRealOtherPara(str14, recordSet), getRealOtherPara(str15, recordSet));
/*     */           } 
/*     */           
/* 187 */           for (byte b1 = 0; b1 < list1.size(); b1++) {
/* 188 */             Element element = list1.get(b1);
/* 189 */             int j = Util.getIntValue(element.getAttributeValue("index"));
/* 190 */             String str16 = element.getAttributeValue("linkvaluecolumn");
/* 191 */             if (str16 == null) str16 = str10;
/*     */             
/* 193 */             String str17 = recordSet.getString(str16);
/* 194 */             str = str + getOperateStr(element, ((String)arrayList.get(j)).equalsIgnoreCase("true"), str17) + "&nbsp;";
/*     */           } 
/*     */         } else {
/* 197 */           for (byte b1 = 0; b1 < list1.size(); b1++) {
/* 198 */             Element element = list1.get(b1);
/* 199 */             String str12 = element.getAttributeValue("linkvaluecolumn");
/* 200 */             if (str12 == null) str12 = str10;
/*     */             
/* 202 */             String str13 = recordSet.getString(str12);
/* 203 */             str = str + getOperateStr(element, true, str13) + "&nbsp;";
/*     */           } 
/*     */         } 
/* 206 */         arrayOfString[arrayOfString.length - 1] = str;
/*     */       } 
/*     */       
/* 209 */       jSONObject.put("cell", arrayOfString);
/* 210 */       jSONArray.put(jSONObject);
/*     */     } 
/*     */     
/* 213 */     return jSONArray;
/*     */   }
/*     */   
/*     */   public String getOperateStr(Element paramElement, boolean paramBoolean, String paramString) {
/* 217 */     String str1 = "";
/* 218 */     String str2 = paramElement.getAttributeValue("text");
/* 219 */     String str3 = paramElement.getAttributeValue("href");
/* 220 */     String str4 = paramElement.getAttributeValue("target");
/* 221 */     String str5 = paramElement.getAttributeValue("linkkey");
/*     */     
/* 223 */     if (paramBoolean) {
/* 224 */       if (str3 != null) {
/* 225 */         String str = str3.toLowerCase();
/* 226 */         int i = str.indexOf("javascript:");
/* 227 */         if (i != -1) {
/* 228 */           String str6 = "";
/* 229 */           int j = str3.lastIndexOf("(");
/* 230 */           int k = str3.lastIndexOf(")");
/* 231 */           String str7 = str3.substring(j + 1, k);
/*     */           
/* 233 */           if ("".equals(str7.trim())) {
/* 234 */             str6 = str3.substring(0, k) + "\"" + paramString + "\"" + str3.substring(k);
/*     */           } else {
/* 236 */             str6 = str3.substring(0, k) + ",\"" + paramString + "\"" + str3.substring(k);
/*     */           } 
/*     */           
/* 239 */           str1 = "<a href='" + str6 + "'>" + str2 + "</a>";
/*     */         }
/* 241 */         else if (str4 == "_fullwindow") {
/* 242 */           String str6 = str3 + "?" + str5 + "=" + paramString;
/* 243 */           str1 = "<a href=javascript:this.openFullWindowForXtable('" + str6 + "')>" + str2 + "</a>";
/*     */         } else {
/* 245 */           str1 = "<a href=" + str3 + "?" + str5 + "=" + paramString + " target=" + str4 + ">" + str2 + "</a>";
/*     */         } 
/*     */       } else {
/*     */         
/* 249 */         str1 = str2;
/*     */       } 
/*     */     } else {
/* 252 */       str1 = str2;
/*     */     } 
/*     */     
/* 255 */     return str1;
/*     */   }
/*     */ 
/*     */   
/*     */   public int getTotal() {
/* 260 */     return 100;
/*     */   }
/*     */   
/*     */   private String getRealOtherPara(String paramString, RecordSet paramRecordSet) {
/* 264 */     if (paramString == null) return null; 
/* 265 */     String str = "";
/* 266 */     ArrayList<String> arrayList = Util.TokenizerString(paramString, "+");
/* 267 */     for (byte b = 0; b < arrayList.size(); b++) {
/* 268 */       String str1 = arrayList.get(b);
/* 269 */       String str2 = getRealOtherPara1(str1, paramRecordSet);
/* 270 */       String str3 = "".equals(str2) ? " " : str2;
/* 271 */       str = str + str3 + "+";
/*     */     } 
/*     */     
/* 274 */     str = str.substring(0, str.length() - 1);
/* 275 */     return str;
/*     */   }
/*     */   
/*     */   private String getRealOtherPara1(String paramString, RecordSet paramRecordSet) {
/* 279 */     if (paramString == null) return null; 
/* 280 */     String str = "";
/* 281 */     int i = paramString.toLowerCase().indexOf("column:");
/*     */     
/* 283 */     if (i != -1) {
/* 284 */       String str1 = paramString.substring(i + 7);
/* 285 */       str = Util.null2String(paramRecordSet.getString(str1));
/*     */     } else {
/* 287 */       str = paramString;
/*     */     } 
/* 289 */     return str;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/eui/data/table/SplitePageTable.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */