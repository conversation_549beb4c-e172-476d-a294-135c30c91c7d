/*    */ package weaver.eui.data.table;
/*    */ 
/*    */ import java.net.URLDecoder;
/*    */ import javax.servlet.http.HttpServletRequest;
/*    */ import javax.servlet.http.HttpServletResponse;
/*    */ import org.apache.log4j.Logger;
/*    */ import org.json.JSONArray;
/*    */ import weaver.eui.EuiServlet;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public abstract class AbsTable
/*    */ {
/* 20 */   private static Logger logger = Logger.getLogger(EuiServlet.class);
/* 21 */   public HttpServletRequest req = null;
/* 22 */   public HttpServletResponse res = null;
/*    */   public AbsTable(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/* 24 */     this.req = paramHttpServletRequest;
/* 25 */     this.res = paramHttpServletResponse;
/*    */   }
/*    */   
/*    */   public String getPara(String paramString) throws Exception {
/* 29 */     if (this.req.getParameter(paramString) == null) {
/* 30 */       logger.error(paramString + " is not exsit!");
/* 31 */       return "";
/*    */     } 
/* 33 */     return URLDecoder.decode(this.req.getParameter(paramString), "utf-8");
/*    */   }
/*    */   
/*    */   public abstract int getTotal();
/*    */   
/*    */   public abstract JSONArray getCurrentPageData(int paramInt1, int paramInt2, String paramString1, String paramString2) throws Exception;
/*    */   
/*    */   public abstract JSONArray getAllData(String paramString1, String paramString2) throws Exception;
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/eui/data/table/AbsTable.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */