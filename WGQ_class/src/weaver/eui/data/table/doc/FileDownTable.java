/*    */ package weaver.eui.data.table.doc;
/*    */ 
/*    */ import java.io.File;
/*    */ import java.text.DecimalFormat;
/*    */ import java.text.SimpleDateFormat;
/*    */ import javax.servlet.http.HttpServletRequest;
/*    */ import javax.servlet.http.HttpServletResponse;
/*    */ import org.json.JSONArray;
/*    */ import org.json.JSONObject;
/*    */ import weaver.eui.data.table.AbsTable;
/*    */ import weaver.general.ThreadVarLanguage;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class FileDownTable
/*    */   extends AbsTable
/*    */ {
/*    */   public FileDownTable(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/* 21 */     super(paramHttpServletRequest, paramHttpServletResponse);
/*    */   }
/*    */ 
/*    */   
/*    */   public JSONArray getAllData(String paramString1, String paramString2) throws Exception {
/* 26 */     JSONArray jSONArray = new JSONArray();
/* 27 */     System.out.print("filed:" + this.req.getParameter("filed"));
/* 28 */     for (byte b = 0; b < 100; b++) {
/* 29 */       JSONObject jSONObject = new JSONObject();
/* 30 */       jSONObject.put("id", "row");
/* 31 */       jSONObject.put("value", new String[] { "Hunk" + b, "28", "HaHa" });
/* 32 */       jSONArray.put(jSONObject);
/*    */     } 
/* 34 */     return jSONArray;
/*    */   }
/*    */ 
/*    */   
/*    */   public JSONArray getCurrentPageData(int paramInt1, int paramInt2, String paramString1, String paramString2) throws Exception {
/* 39 */     System.out.print("filed:" + this.req.getParameter("filed"));
/*    */     
/* 41 */     String str1 = getPara("dir");
/*    */     
/* 43 */     String str2 = getPara("ftpServerPath");
/*    */     
/* 45 */     File file = new File(str1);
/* 46 */     File[] arrayOfFile = file.listFiles();
/*    */     
/* 48 */     JSONArray jSONArray = new JSONArray();
/* 49 */     for (byte b = 0; b < paramInt2; b++) {
/* 50 */       if (b < arrayOfFile.length) {
/* 51 */         File file1 = arrayOfFile[b];
/* 52 */         if (file1.isFile()) {
/* 53 */           file1.getName(); file1.length(); file1.lastModified();
/* 54 */           JSONObject jSONObject = new JSONObject();
/* 55 */           jSONObject.put("id", "id" + b);
/* 56 */           DecimalFormat decimalFormat = new DecimalFormat("#######0.00");
/* 57 */           long l = file1.length();
/* 58 */           String str = "";
/* 59 */           if (l > 1048576L) {
/* 60 */             str = decimalFormat.format((new Long(file1.length())).doubleValue() / 1048576.0D) + "M";
/*    */           } else {
/* 62 */             str = decimalFormat.format((new Long(file1.length())).doubleValue() / 1024.0D) + "K";
/*    */           } 
/* 64 */           System.out.println("fileSize:" + str);
/*    */           
/* 66 */           jSONObject.put("cell", new String[] { file1.getName(), str, (new SimpleDateFormat("yyyy-MM-dd")).format(Long.valueOf(file1.lastModified())), "<a href='" + str2 + str1
/* 67 */                 .substring(str1.indexOf("ftp")) + "/" + file1.getName() + "'>" + SystemEnv.getHtmlLabelName(31156, ThreadVarLanguage.getLang()) + "</a>" });
/* 68 */           jSONArray.put(jSONObject);
/*    */         } 
/*    */       } 
/*    */     } 
/* 72 */     return jSONArray;
/*    */   }
/*    */ 
/*    */   
/*    */   public int getTotal() {
/* 77 */     return 1;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/eui/data/table/doc/FileDownTable.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */