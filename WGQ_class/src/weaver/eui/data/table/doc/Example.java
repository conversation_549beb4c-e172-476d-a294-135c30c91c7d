/*    */ package weaver.eui.data.table.doc;
/*    */ 
/*    */ import javax.servlet.http.HttpServletRequest;
/*    */ import javax.servlet.http.HttpServletResponse;
/*    */ import org.json.JSONArray;
/*    */ import org.json.JSONObject;
/*    */ import weaver.eui.data.table.AbsTable;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class Example
/*    */   extends AbsTable
/*    */ {
/*    */   public Example(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/* 18 */     super(paramHttpServletRequest, paramHttpServletResponse);
/*    */   }
/*    */ 
/*    */   
/*    */   public JSONArray getAllData(String paramString1, String paramString2) throws Exception {
/* 23 */     JSONArray jSONArray = new JSONArray();
/*    */ 
/*    */     
/* 26 */     for (byte b = 0; b < 100; b++) {
/* 27 */       JSONObject jSONObject = new JSONObject();
/* 28 */       jSONObject.put("id", "row");
/* 29 */       jSONObject.put("value", new String[] { "Hunk" + b, "28", "HaHa" });
/* 30 */       jSONArray.put(jSONObject);
/*    */     } 
/* 32 */     return jSONArray;
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public JSONArray getCurrentPageData(int paramInt1, int paramInt2, String paramString1, String paramString2) throws Exception {
/* 38 */     JSONArray jSONArray = new JSONArray();
/* 39 */     for (byte b = 0; b < paramInt2; b++) {
/* 40 */       JSONObject jSONObject = new JSONObject();
/* 41 */       jSONObject.put("id", "id" + b);
/* 42 */       jSONObject.put("cell", new String[] { "Hunk" + b, "2007-11-06", "Client", "100", "100", "100", "11" });
/* 43 */       jSONArray.put(jSONObject);
/*    */     } 
/* 45 */     return jSONArray;
/*    */   }
/*    */ 
/*    */   
/*    */   public int getTotal() {
/* 50 */     return 100;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/eui/data/table/doc/Example.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */