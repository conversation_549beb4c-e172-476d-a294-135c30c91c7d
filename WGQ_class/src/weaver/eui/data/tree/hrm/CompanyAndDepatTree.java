/*     */ package weaver.eui.data.tree.hrm;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import org.json.JSONArray;
/*     */ import org.json.JSONException;
/*     */ import org.json.JSONObject;
/*     */ import weaver.eui.data.tree.AbsTree;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.company.CompanyComInfo;
/*     */ import weaver.hrm.company.DepartmentComInfo;
/*     */ import weaver.hrm.company.SubCompanyComInfo;
/*     */ import weaver.systeminfo.systemright.CheckSubCompanyRight;
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CompanyAndDepatTree
/*     */   extends AbsTree
/*     */ {
/*  21 */   CompanyComInfo companyInfo = null;
/*  22 */   SubCompanyComInfo subCompanyInfo = null;
/*  23 */   SubCompanyComInfo subCompanyInfo2 = null;
/*  24 */   DepartmentComInfo departComInfo = null;
/*  25 */   DepartmentComInfo departComInfo2 = null;
/*     */   
/*  27 */   private int[] subcomids = null;
/*  28 */   private int[] subcomids1 = null;
/*     */   private String rightStr;
/*     */   private int userId;
/*     */   
/*     */   public CompanyAndDepatTree(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/*  33 */     super(paramHttpServletRequest, paramHttpServletResponse);
/*     */     try {
/*  35 */       this.companyInfo = new CompanyComInfo();
/*  36 */       this.subCompanyInfo = new SubCompanyComInfo();
/*  37 */       this.subCompanyInfo2 = new SubCompanyComInfo();
/*  38 */       this.departComInfo = new DepartmentComInfo();
/*  39 */       this.departComInfo2 = new DepartmentComInfo();
/*     */ 
/*     */       
/*  42 */       this.rightStr = Util.null2String(this.req.getParameter("rightStr"));
/*  43 */       this.userId = Util.getIntValue(this.req.getParameter("userId"));
/*  44 */     } catch (Exception exception) {
/*  45 */       exception.printStackTrace();
/*     */     } 
/*     */   }
/*     */   
/*     */   public JSONArray getTreeData(String paramString) throws Exception {
/*  50 */     JSONArray jSONArray = new JSONArray();
/*  51 */     if ("source".equals(paramString)) {
/*     */       
/*  53 */       JSONObject jSONObject = new JSONObject("{text:'" + this.companyInfo.getCompanyname("1") + "','id':'0','expanded':true,'classes':'company','href':'/hrm/search/HrmResourceSearchTmp.jsp','target':'contentframe'}");
/*     */ 
/*     */       
/*  56 */       jSONObject.put("children", getSubArray("0"));
/*  57 */       jSONArray.put(jSONObject);
/*     */     } else {
/*     */       
/*  60 */       String[] arrayOfString = Util.TokenizerString2(paramString, "|");
/*     */       
/*  62 */       if ("subcompany".equals(arrayOfString[0])) {
/*  63 */         jSONArray = getSubArray(arrayOfString[1]);
/*  64 */       } else if ("department".equals(arrayOfString[0])) {
/*  65 */         jSONArray = getSubDepartmentArray(arrayOfString[1]);
/*     */       } 
/*     */     } 
/*  68 */     return jSONArray;
/*     */   }
/*     */   public JSONArray getSubArray(String paramString) throws JSONException {
/*  71 */     JSONArray jSONArray = new JSONArray();
/*     */ 
/*     */     
/*  74 */     ArrayList<String[]> arrayList1 = getSubList(paramString);
/*  75 */     for (byte b1 = 0; b1 < arrayList1.size(); b1++) {
/*  76 */       String[] arrayOfString = arrayList1.get(b1);
/*  77 */       JSONObject jSONObject = new JSONObject();
/*  78 */       jSONObject.put("text", arrayOfString[1]);
/*  79 */       jSONObject.put("classes", "subcompany");
/*  80 */       jSONObject.put("id", "subcompany|" + arrayOfString[0]);
/*  81 */       jSONObject.put("href", "/hrm/search/HrmResourceSearchTmp.jsp?subcompany1=" + arrayOfString[0]);
/*  82 */       jSONObject.put("target", "contentframe");
/*  83 */       jSONObject.put("hasChildren", arrayOfString[2].equals("true"));
/*     */ 
/*     */       
/*  86 */       jSONArray.put(jSONObject);
/*     */     } 
/*     */ 
/*     */     
/*  90 */     ArrayList<String[]> arrayList2 = getDepartList(paramString);
/*  91 */     for (byte b2 = 0; b2 < arrayList2.size(); b2++) {
/*  92 */       String[] arrayOfString = arrayList2.get(b2);
/*  93 */       JSONObject jSONObject = new JSONObject();
/*  94 */       jSONObject.put("text", arrayOfString[1]);
/*  95 */       jSONObject.put("id", "department|" + arrayOfString[0]);
/*  96 */       jSONObject.put("classes", "department");
/*  97 */       jSONObject.put("href", "/hrm/search/HrmResourceSearchTmp.jsp?from=hrmorg&department=" + arrayOfString[0]);
/*  98 */       jSONObject.put("target", "contentframe");
/*  99 */       jSONObject.put("hasChildren", arrayOfString[2].equals("true"));
/*     */       
/* 101 */       jSONArray.put(jSONObject);
/*     */     } 
/*     */     
/* 104 */     return jSONArray;
/*     */   }
/*     */   
/*     */   public JSONArray getSubDepartmentArray(String paramString) throws JSONException {
/* 108 */     JSONArray jSONArray = new JSONArray();
/*     */ 
/*     */     
/* 111 */     ArrayList<String[]> arrayList = getSubDepartList(paramString);
/* 112 */     for (byte b = 0; b < arrayList.size(); b++) {
/* 113 */       String[] arrayOfString = arrayList.get(b);
/* 114 */       JSONObject jSONObject = new JSONObject();
/* 115 */       jSONObject.put("text", arrayOfString[1]);
/* 116 */       jSONObject.put("id", "department|" + arrayOfString[0]);
/* 117 */       jSONObject.put("classes", "department");
/* 118 */       jSONObject.put("href", "/hrm/search/HrmResourceSearchTmp.jsp?from=hrmorg&department=" + arrayOfString[0]);
/* 119 */       jSONObject.put("target", "contentframe");
/* 120 */       jSONObject.put("hasChildren", arrayOfString[2].equals("true"));
/*     */       
/* 122 */       jSONArray.put(jSONObject);
/*     */     } 
/*     */     
/* 125 */     return jSONArray;
/*     */   }
/*     */   public ArrayList getSubList(String paramString) {
/* 128 */     ArrayList<String[]> arrayList = new ArrayList();
/* 129 */     CheckSubCompanyRight checkSubCompanyRight = new CheckSubCompanyRight();
/* 130 */     int[] arrayOfInt = checkSubCompanyRight.getSubComPathByUserRightId(this.userId, this.rightStr, 0);
/*     */     
/* 132 */     this.subCompanyInfo.setTofirstRow();
/* 133 */     while (this.subCompanyInfo.next()) {
/* 134 */       String str1 = this.subCompanyInfo.getSupsubcomid();
/* 135 */       if (str1.equals("")) str1 = "0"; 
/* 136 */       if (!str1.equals(paramString))
/*     */         continue; 
/* 138 */       String str2 = this.subCompanyInfo.getSubCompanyid();
/* 139 */       String str3 = this.subCompanyInfo.getSubCompanyname();
/* 140 */       if (!isInArray(str2, arrayOfInt))
/*     */         continue; 
/* 142 */       String[] arrayOfString = { str2, str3, isHavaSubChildren(str2, arrayOfInt) ? "true" : "false" };
/* 143 */       arrayList.add(arrayOfString);
/*     */     } 
/* 145 */     return arrayList;
/*     */   }
/*     */   
/*     */   public boolean isHavaSubChildren(String paramString, int[] paramArrayOfint) {
/* 149 */     boolean bool = false;
/* 150 */     this.subCompanyInfo2.setTofirstRow();
/* 151 */     while (this.subCompanyInfo2.next()) {
/* 152 */       String str = this.subCompanyInfo2.getSupsubcomid();
/* 153 */       if (str.equals(paramString) && isInArray(paramString, paramArrayOfint)) {
/* 154 */         bool = true;
/*     */         
/*     */         break;
/*     */       } 
/*     */     } 
/* 159 */     this.departComInfo2.setTofirstRow();
/* 160 */     while (this.departComInfo2.next()) {
/* 161 */       String str = this.departComInfo2.getSubcompanyid1();
/* 162 */       if (str.equals(paramString)) {
/* 163 */         bool = true;
/*     */         
/*     */         break;
/*     */       } 
/*     */     } 
/* 168 */     return bool;
/*     */   }
/*     */   public boolean isHavaDepartChildren(String paramString) {
/* 171 */     boolean bool = false;
/* 172 */     this.departComInfo2.setTofirstRow();
/* 173 */     while (this.departComInfo2.next()) {
/* 174 */       String str = this.departComInfo2.getDepartmentsupdepid();
/* 175 */       if (paramString.equals(str)) {
/* 176 */         bool = true;
/*     */         break;
/*     */       } 
/*     */     } 
/* 180 */     return bool;
/*     */   }
/*     */   
/*     */   public ArrayList getDepartList(String paramString) {
/* 184 */     ArrayList<String[]> arrayList = new ArrayList();
/* 185 */     this.departComInfo.setTofirstRow();
/* 186 */     while (this.departComInfo.next()) {
/* 187 */       String str1 = this.departComInfo.getSubcompanyid1();
/* 188 */       if (!str1.equals(paramString)) {
/*     */         continue;
/*     */       }
/* 191 */       String str2 = this.departComInfo.getDepartmentid();
/* 192 */       String str3 = this.departComInfo.getDepartmentname();
/*     */ 
/*     */       
/* 195 */       String[] arrayOfString = { str2, str3, isHavaDepartChildren(str2) ? "true" : "false" };
/* 196 */       arrayList.add(arrayOfString);
/*     */     } 
/* 198 */     return arrayList;
/*     */   }
/*     */   public ArrayList getSubDepartList(String paramString) {
/* 201 */     ArrayList<String[]> arrayList = new ArrayList();
/* 202 */     this.departComInfo.setTofirstRow();
/* 203 */     while (this.departComInfo.next()) {
/* 204 */       String str1 = this.departComInfo.getDepartmentsupdepid();
/* 205 */       if (!str1.equals(paramString)) {
/*     */         continue;
/*     */       }
/* 208 */       String str2 = this.departComInfo.getDepartmentid();
/* 209 */       String str3 = this.departComInfo.getDepartmentname();
/*     */ 
/*     */       
/* 212 */       String[] arrayOfString = { str2, str3, isHavaDepartChildren(str2) ? "true" : "false" };
/* 213 */       arrayList.add(arrayOfString);
/*     */     } 
/* 215 */     return arrayList;
/*     */   }
/*     */ 
/*     */   
/*     */   public boolean isInArray(String paramString, int[] paramArrayOfint) {
/* 220 */     boolean bool = false;
/* 221 */     for (byte b = 0; b < paramArrayOfint.length; b++) {
/* 222 */       if (paramString.equals(String.valueOf(paramArrayOfint[b])))
/* 223 */         bool = true; 
/*     */     } 
/* 225 */     return bool;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/eui/data/tree/hrm/CompanyAndDepatTree.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */