/*    */ package weaver.eui.data.tree.doc;
/*    */ 
/*    */ import javax.servlet.http.HttpServletRequest;
/*    */ import javax.servlet.http.HttpServletResponse;
/*    */ import org.json.JSONArray;
/*    */ import weaver.eui.data.tree.AbsTree;
/*    */ import weaver.general.ThreadVarLanguage;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ public class Example
/*    */   extends AbsTree
/*    */ {
/*    */   public Example(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/* 14 */     super(paramHttpServletRequest, paramHttpServletResponse);
/*    */   }
/*    */ 
/*    */   
/*    */   public JSONArray getTreeData(String paramString) throws Exception {
/* 19 */     return new JSONArray("[{text:'" + SystemEnv.getHtmlLabelName(15586, ThreadVarLanguage.getLang()) + "1-N','id':'1','hasChildren': false},{text:'" + 
/* 20 */         SystemEnv.getHtmlLabelName(15586, ThreadVarLanguage.getLang()) + "2-N','id':'1','hasChildren': false},{text:'" + 
/* 21 */         SystemEnv.getHtmlLabelName(15586, ThreadVarLanguage.getLang()) + "3-N','id':'1','hasChildren': false},{text:'" + 
/* 22 */         SystemEnv.getHtmlLabelName(15586, ThreadVarLanguage.getLang()) + "4-N','id':'1','hasChildren': false},{text:'" + 
/* 23 */         SystemEnv.getHtmlLabelName(15586, ThreadVarLanguage.getLang()) + "5-N','id':'1','hasChildren': false},{text:'" + 
/* 24 */         SystemEnv.getHtmlLabelName(15586, ThreadVarLanguage.getLang()) + "6-N','id':'1','hasChildren': true}]");
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/eui/data/tree/doc/Example.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */