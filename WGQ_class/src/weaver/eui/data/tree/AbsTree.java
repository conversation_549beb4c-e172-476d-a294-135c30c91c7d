/*    */ package weaver.eui.data.tree;
/*    */ 
/*    */ import java.net.URLDecoder;
/*    */ import javax.servlet.http.HttpServletRequest;
/*    */ import javax.servlet.http.HttpServletResponse;
/*    */ import org.json.JSONArray;
/*    */ 
/*    */ 
/*    */ public abstract class AbsTree
/*    */ {
/* 11 */   public HttpServletRequest req = null;
/* 12 */   public HttpServletResponse res = null;
/*    */   public AbsTree(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/* 14 */     this.req = paramHttpServletRequest;
/* 15 */     this.res = paramHttpServletResponse;
/*    */   }
/*    */   public String getPara(String paramString) throws Exception {
/* 18 */     return URLDecoder.decode(this.req.getParameter(paramString), "utf-8");
/*    */   }
/*    */   
/*    */   public abstract JSONArray getTreeData(String paramString) throws Exception;
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/eui/data/tree/AbsTree.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */