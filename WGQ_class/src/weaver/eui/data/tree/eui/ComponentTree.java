/*    */ package weaver.eui.data.tree.eui;
/*    */ 
/*    */ import javax.servlet.http.HttpServletRequest;
/*    */ import javax.servlet.http.HttpServletResponse;
/*    */ import org.json.JSONArray;
/*    */ import weaver.eui.data.tree.AbsTree;
/*    */ import weaver.general.ThreadVarLanguage;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ public class ComponentTree
/*    */   extends AbsTree
/*    */ {
/*    */   public ComponentTree(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/* 14 */     super(paramHttpServletRequest, paramHttpServletResponse);
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public JSONArray getTreeData(String paramString) throws Exception {
/* 21 */     String[] arrayOfString = { "base", "black-tie", "blitzer", "cupertino", "dark-hive", "dot-luv", "eggplant", "excite-bike", "flick", "hot-sneaks", "humanity", "le-frog", "mint-choc", "overcast", "pepper-grinder", "redmond", "smoothness", "south-street", "start ", "sunny", "swanky-purse", "trontastic", "ui-darkness", "ui-lightness", "vader" };
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */     
/* 55 */     String str = "[\t{text:'" + SystemEnv.getHtmlLabelName(10003509, ThreadVarLanguage.getLang()) + "',expanded: true,children:[\t\t{text:'" + SystemEnv.getHtmlLabelName(10003510, ThreadVarLanguage.getLang()) + "',href:'readme.jsp',target:'mainFrame',}\t]},\t{text:'" + SystemEnv.getHtmlLabelName(387044, ThreadVarLanguage.getLang()) + "',expanded: true,children:[\t\t{text:'" + SystemEnv.getHtmlLabelName(10003511, ThreadVarLanguage.getLang()) + "',href:'/demos/table/table.jsp',target:'mainFrame'},\t\t{text:'" + SystemEnv.getHtmlLabelName(10003512, ThreadVarLanguage.getLang()) + "',href:'/demos/tree/tree.jsp',target:'mainFrame'},\t\t{text:'" + SystemEnv.getHtmlLabelName(333, ThreadVarLanguage.getLang()) + "',href:'/demos/btn/btn.jsp',target:'mainFrame'},\t\t{text:'" + SystemEnv.getHtmlLabelName(10003513, ThreadVarLanguage.getLang()) + "',href:'/demos/dialog/dialog.jsp',target:'mainFrame'},\t\t{text:'TABS',href:'/demos/tabs/tabs.jsp',target:'mainFrame'},\t\t{text:'" + SystemEnv.getHtmlLabelName(10003514, ThreadVarLanguage.getLang()) + "',href:'/demos/uploader/uploader.jsp',target:'mainFrame'},\t\t{text:'" + SystemEnv.getHtmlLabelName(10003515, ThreadVarLanguage.getLang()) + "',href:'/demos/time/time.jsp',target:'mainFrame'},\t\t{text:'" + SystemEnv.getHtmlLabelName(10003516, ThreadVarLanguage.getLang()) + "',href:'/demos/date/date.jsp',target:'mainFrame'},\t\t{text:'" + SystemEnv.getHtmlLabelName(10003517, ThreadVarLanguage.getLang()) + "',href:'/demos/time/time.jsp',target:'mainFrame'},\t\t{text:'" + SystemEnv.getHtmlLabelName(10003518, ThreadVarLanguage.getLang()) + "',href:'/demos/validate/validate.jsp',target:'mainFrame'},\t\t{text:'" + SystemEnv.getHtmlLabelName(10003519, ThreadVarLanguage.getLang()) + "',href:'/demos/context/context.jsp',target:'mainFrame'},\t\t{text:'" + SystemEnv.getHtmlLabelName(10003520, ThreadVarLanguage.getLang()) + "',href:'/demos/editor/editor.jsp',target:'mainFrame'}\t]},\t{text:'" + SystemEnv.getHtmlLabelName(10003521, ThreadVarLanguage.getLang()) + "',expanded: true,children:[\t\t{text:'" + SystemEnv.getHtmlLabelName(10003522, ThreadVarLanguage.getLang()) + "',href:'/demos/layout/two.jsp',target:'mainFrame'},\t\t{text:'" + SystemEnv.getHtmlLabelName(10003523, ThreadVarLanguage.getLang()) + "',href:'/demos/layout/three.jsp',target:'mainFrame'}\t]},\t{text:'" + SystemEnv.getHtmlLabelName(23140, ThreadVarLanguage.getLang()) + "',expanded: true,children:[\t\t{text:'" + SystemEnv.getHtmlLabelName(10003524, ThreadVarLanguage.getLang()) + "',href:'/demos/template/base.jsp.html',target:'mainFrame'},\t\t{text:'" + SystemEnv.getHtmlLabelName(10003525, ThreadVarLanguage.getLang()) + "',href:'/demos/template/operate.jsp.html',target:'mainFrame'},\t\t{text:'" + SystemEnv.getHtmlLabelName(10003526, ThreadVarLanguage.getLang()) + "',href:'/demos/template/viewform.jsp.html',target:'mainFrame'},\t]},\t{text:'" + SystemEnv.getHtmlLabelName(1014, ThreadVarLanguage.getLang()) + "',expanded: true,children:[";
/*    */ 
/*    */ 
/*    */ 
/*    */     
/* 60 */     str = str + "{text:'" + SystemEnv.getHtmlLabelName(10003527, ThreadVarLanguage.getLang()) + "',href:'StyleChanger.jsp?id=base',target:'mainFrame'},";
/* 61 */     str = str + "{text:'" + SystemEnv.getHtmlLabelName(10003528, ThreadVarLanguage.getLang()) + "',href:'StyleChanger.jsp?id=redmond',target:'mainFrame'},";
/* 62 */     str = str + "{text:'" + SystemEnv.getHtmlLabelName(10003529, ThreadVarLanguage.getLang()) + "',href:'StyleChanger.jsp?id=humanity',target:'mainFrame'},";
/* 63 */     str = str + "\t]},]";
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */     
/* 70 */     return new JSONArray(str);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/eui/data/tree/eui/ComponentTree.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */