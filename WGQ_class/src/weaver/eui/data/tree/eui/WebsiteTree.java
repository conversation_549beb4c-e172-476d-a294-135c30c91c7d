/*    */ package weaver.eui.data.tree.eui;
/*    */ 
/*    */ import javax.servlet.http.HttpServletRequest;
/*    */ import javax.servlet.http.HttpServletResponse;
/*    */ import org.json.JSONArray;
/*    */ import weaver.eui.data.tree.AbsTree;
/*    */ import weaver.general.ThreadVarLanguage;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ public class WebsiteTree
/*    */   extends AbsTree
/*    */ {
/*    */   public WebsiteTree(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/* 14 */     super(paramHttpServletRequest, paramHttpServletResponse);
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public JSONArray getTreeData(String paramString) throws Exception {
/* 20 */     String[] arrayOfString = { "base", "black-tie", "blitzer", "cupertino", "dark-hive", "dot-luv", "eggplant", "excite-bike", "flick", "hot-sneaks", "humanity", "le-frog", "mint-choc", "overcast", "pepper-grinder", "redmond", "smoothness", "south-street", "start ", "sunny", "swanky-purse", "trontastic", "ui-darkness", "ui-lightness", "vader" };
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */     
/* 34 */     String str = "[\t{text:'JAVA" + SystemEnv.getHtmlLabelName(10003550, ThreadVarLanguage.getLang()) + "',expanded: true,children:[\t\t{text:'JavaEye',href:'http://www.javaeye.com/',target:'mainFrame'},       {text:'CSDN',href:'http://www.csdn.net/',target:'mainFrame'}\t]},\t{text:'" + SystemEnv.getHtmlLabelName(10003551, ThreadVarLanguage.getLang()) + "',expanded: true,children:[\t\t{text:'Apache',href:'http://www.apache.org/',target:'mainFrame'},\t\t{text:'resin',href:'http://www.caucho.com/',target:'mainFrame'},\t\t{text:'JQuery',href:'http://jquery.com/',target:'mainFrame'},\t\t{text:'Extjs',href:'http://www.eclipse.org/',target:'mainFrame'},\t\t{text:'Eclipse',href:'http://www.eclipse.org/',target:'mainFrame'},\t\t{text:'Google" + SystemEnv.getHtmlLabelName(590, ThreadVarLanguage.getLang()) + "',href:'http://code.google.com/intl/zh-CN/more/',target:'mainFrame'},\t\t{text:'Java" + SystemEnv.getHtmlLabelName(10003552, ThreadVarLanguage.getLang()) + "',href:'http://java.sun.com/',target:'mainFrame'}\t]}]";
/*    */ 
/*    */     
/* 37 */     return new JSONArray(str);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/eui/data/tree/eui/WebsiteTree.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */