/*    */ package weaver.eui.data.tree.eui;
/*    */ 
/*    */ import java.io.File;
/*    */ import javax.servlet.http.HttpServletRequest;
/*    */ import javax.servlet.http.HttpServletResponse;
/*    */ import org.json.JSONArray;
/*    */ import org.json.JSONObject;
/*    */ import weaver.eui.data.tree.AbsTree;
/*    */ 
/*    */ 
/*    */ 
/*    */ public class HelpTree
/*    */   extends AbsTree
/*    */ {
/*    */   public HelpTree(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/* 16 */     super(paramHttpServletRequest, paramHttpServletResponse);
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public JSONArray getTreeData(String paramString) throws Exception {
/* 23 */     String str1 = ToolTree.class.getClassLoader().getResource("").getPath();
/*    */     
/* 25 */     String str2 = str1.substring(0, str1.lastIndexOf("eui")) + "ftp/resource/api";
/*    */     
/* 27 */     File file = new File(str2);
/*    */     
/* 29 */     File[] arrayOfFile = file.listFiles();
/*    */     
/* 31 */     JSONArray jSONArray = new JSONArray();
/*    */     
/* 33 */     for (File file1 : arrayOfFile) {
/* 34 */       if (file1.isDirectory()) {
/* 35 */         File file2 = new File(str2 + "/" + file1.getName());
/* 36 */         System.out.println("fileName:" + file1.getName());
/* 37 */         JSONObject jSONObject = new JSONObject();
/*    */         
/* 39 */         jSONObject.put("text", file1.getName());
/* 40 */         jSONObject.put("expanded", true);
/*    */         
/* 42 */         File[] arrayOfFile1 = file2.listFiles();
/*    */         
/* 44 */         JSONArray jSONArray1 = new JSONArray();
/* 45 */         for (byte b = 0; b < arrayOfFile1.length; b++) {
/* 46 */           File file3 = arrayOfFile1[b];
/* 47 */           if (file3.isDirectory()) {
/* 48 */             JSONObject jSONObject1 = new JSONObject();
/* 49 */             jSONObject1.put("text", file3.getName());
/*    */             
/* 51 */             jSONObject1.put("href", "/helps.jsp?dirName=" + file1.getName() + "&docName=" + file3.getName());
/* 52 */             jSONObject1.put("target", "mainFrame");
/* 53 */             jSONArray1.put(jSONObject1);
/*    */           } 
/*    */         } 
/* 56 */         if (jSONArray1.length() > 0) {
/* 57 */           jSONObject.put("children", jSONArray1);
/*    */         }
/* 59 */         jSONArray.put(jSONObject);
/*    */       } 
/* 61 */     }  return jSONArray;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/eui/data/tree/eui/HelpTree.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */