/*    */ package weaver.eui.data.tree.eui;
/*    */ 
/*    */ import javax.servlet.http.HttpServletRequest;
/*    */ import javax.servlet.http.HttpServletResponse;
/*    */ import org.json.JSONArray;
/*    */ import weaver.eui.data.tree.AbsTree;
/*    */ import weaver.general.ThreadVarLanguage;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ public class LabelTree
/*    */   extends AbsTree
/*    */ {
/*    */   public LabelTree(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/* 14 */     super(paramHttpServletRequest, paramHttpServletResponse);
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public JSONArray getTreeData(String paramString) throws Exception {
/* 21 */     String[] arrayOfString = { "base", "black-tie", "blitzer", "cupertino", "dark-hive", "dot-luv", "eggplant", "excite-bike", "flick", "hot-sneaks", "humanity", "le-frog", "mint-choc", "overcast", "pepper-grinder", "redmond", "smoothness", "south-street", "start ", "sunny", "swanky-purse", "trontastic", "ui-darkness", "ui-lightness", "vader" };
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */     
/* 66 */     String str = "[\t{text:'" + SystemEnv.getHtmlLabelName(81486, ThreadVarLanguage.getLang()) + "',expanded: true,children:[\t\t{text:'" + SystemEnv.getHtmlLabelName(10003530, ThreadVarLanguage.getLang()) + "',href:'/systeminfo/label/ManageLabel.jsp',target:'mainFrame'},       {text:'" + SystemEnv.getHtmlLabelName(10003531, ThreadVarLanguage.getLang()) + "',href:'/systeminfo/label/AddLabel.jsp',target:'mainFrame'}\t]},\t{text:'" + SystemEnv.getHtmlLabelName(17996, ThreadVarLanguage.getLang()) + "',expanded: true,children:[\t\t{text:'" + SystemEnv.getHtmlLabelName(17996, ThreadVarLanguage.getLang()) + "',href:'/systeminfo/workflowbill/ManageWorkflowbill.jsp',target:'mainFrame'},\t\t{text:'" + SystemEnv.getHtmlLabelName(10003532, ThreadVarLanguage.getLang()) + "',href:'/systeminfo/workflowbill/ManageWorkflowbrowser.jsp',target:'mainFrame'}\t]},\t{text:'" + SystemEnv.getHtmlLabelName(10003533, ThreadVarLanguage.getLang()) + "',expanded: true,children:[\t\t{text:'" + SystemEnv.getHtmlLabelName(17596, ThreadVarLanguage.getLang()) + "',href:'/systeminfo/menuConfig/Maint/ManageLeftMenu.jsp',target:'mainFrame'},\t\t{text:'" + SystemEnv.getHtmlLabelName(17597, ThreadVarLanguage.getLang()) + "',href:'/systeminfo/menuConfig/Maint/ManageMainMenu.jsp',target:'mainFrame'},\t\t{text:'" + SystemEnv.getHtmlLabelName(10003534, ThreadVarLanguage.getLang()) + "',href:'/systeminfo/menuConfig/Maint/ManageSystemModule.jsp',target:'mainFrame'}\t]},\t{text:'" + SystemEnv.getHtmlLabelName(501869, ThreadVarLanguage.getLang()) + "',expanded: true,children:[\t\t{text:'" + SystemEnv.getHtmlLabelName(10003535, ThreadVarLanguage.getLang()) + "',href:'/systeminfo/systemright/SystemRight.jsp',target:'mainFrame'},\t\t{text:'" + SystemEnv.getHtmlLabelName(84070, ThreadVarLanguage.getLang()) + "',href:'/systeminfo/systemright/AddRight.jsp',target:'mainFrame'},\t\t{text:'" + SystemEnv.getHtmlLabelName(10003536, ThreadVarLanguage.getLang()) + "',href:'/systeminfo/systemright/ManageRightDetail.jsp',target:'mainFrame'},\t\t{text:'" + SystemEnv.getHtmlLabelName(10003537, ThreadVarLanguage.getLang()) + "',href:'/systeminfo/systemright/AddRightDetail.jsp',target:'mainFrame'}\t]},\t{text:'" + SystemEnv.getHtmlLabelName(19085, ThreadVarLanguage.getLang()) + "',expanded: true,children:[\t\t{text:'" + SystemEnv.getHtmlLabelName(388297, ThreadVarLanguage.getLang()) + "',href:'/systeminfo/system/SysPopTypeList.jsp',target:'mainFrame'}\t]},\t{text:'" + SystemEnv.getHtmlLabelName(10003538, ThreadVarLanguage.getLang()) + "',expanded: true,children:[\t\t{text:'" + SystemEnv.getHtmlLabelName(10003539, ThreadVarLanguage.getLang()) + "',href:'/systeminfo/systemright/SystemRight.jsp',target:'mainFrame'}\t]},\t{text:'" + SystemEnv.getHtmlLabelName(127229, ThreadVarLanguage.getLang()) + "',expanded: true,children:[\t\t{text:'" + SystemEnv.getHtmlLabelName(10003540, ThreadVarLanguage.getLang()) + "',href:'/systeminfo/note/ManageNote.jsp',target:'mainFrame'},\t\t{text:'" + SystemEnv.getHtmlLabelName(10003541, ThreadVarLanguage.getLang()) + "',href:'/systeminfo/note/AddNote.jsp',target:'mainFrame'}\t]},\t{text:'" + SystemEnv.getHtmlLabelName(25700, ThreadVarLanguage.getLang()) + "',expanded: true,children:[\t\t{text:'" + SystemEnv.getHtmlLabelName(10003542, ThreadVarLanguage.getLang()) + "',href:'/systeminfo/errormsg/ManageErrorMsg.jsp',target:'mainFrame'},\t\t{text:'" + SystemEnv.getHtmlLabelName(10003543, ThreadVarLanguage.getLang()) + "',href:'/systeminfo/errormsg/AddErrorMsg.jsp',target:'mainFrame'}\t]},\t{text:'" + SystemEnv.getHtmlLabelName(10003544, ThreadVarLanguage.getLang()) + "',expanded: true,children:[\t\t{text:'" + SystemEnv.getHtmlLabelName(10003545, ThreadVarLanguage.getLang()) + "',href:'/systeminfo/logitem/ManageLogitem.jsp',target:'mainFrame'},\t\t{text:'" + SystemEnv.getHtmlLabelName(10003546, ThreadVarLanguage.getLang()) + "',href:'/systeminfo/logitem/AddLogitem.jsp',target:'mainFrame'}\t]},\t{text:'" + SystemEnv.getHtmlLabelName(82685, ThreadVarLanguage.getLang()) + "',expanded: true,children:[\t\t{text:'" + SystemEnv.getHtmlLabelName(82685, ThreadVarLanguage.getLang()) + "',href:'/systeminfo/language/managelanguage.jsp',target:'mainFrame'},\t\t{text:'" + SystemEnv.getHtmlLabelName(10003547, ThreadVarLanguage.getLang()) + "',href:'/systeminfo/language/addlanguage.jsp',target:'mainFrame'}\t]},\t{text:'" + SystemEnv.getHtmlLabelName(10003548, ThreadVarLanguage.getLang()) + "',expanded: true,children:[\t\t{text:'" + SystemEnv.getHtmlLabelName(15413, ThreadVarLanguage.getLang()) + "SQL" + SystemEnv.getHtmlLabelName(10003549, ThreadVarLanguage.getLang()) + "',href:'/systeminfo/creatSQL/manageSql.jsp',target:'mainFrame'}\t]}]";
/*    */ 
/*    */     
/* 69 */     return new JSONArray(str);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/eui/data/tree/eui/LabelTree.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */