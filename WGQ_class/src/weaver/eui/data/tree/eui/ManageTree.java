/*    */ package weaver.eui.data.tree.eui;
/*    */ 
/*    */ import javax.servlet.http.HttpServletRequest;
/*    */ import javax.servlet.http.HttpServletResponse;
/*    */ import org.json.JSONArray;
/*    */ import weaver.eui.data.tree.AbsTree;
/*    */ import weaver.general.ThreadVarLanguage;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ public class ManageTree
/*    */   extends AbsTree
/*    */ {
/*    */   public ManageTree(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/* 14 */     super(paramHttpServletRequest, paramHttpServletResponse);
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public JSONArray getTreeData(String paramString) throws Exception {
/* 21 */     String[] arrayOfString = { "base", "black-tie", "blitzer", "cupertino", "dark-hive", "dot-luv", "eggplant", "excite-bike", "flick", "hot-sneaks", "humanity", "le-frog", "mint-choc", "overcast", "pepper-grinder", "redmond", "smoothness", "south-street", "start ", "sunny", "swanky-purse", "trontastic", "ui-darkness", "ui-lightness", "vader" };
/*    */ 
/*    */ 
/*    */ 
/*    */     
/* 26 */     String str = "[\t{text:'" + SystemEnv.getHtmlLabelName(16521, ThreadVarLanguage.getLang()) + "',expanded: true,children:[\t\t{text:'" + SystemEnv.getHtmlLabelName(33198, ThreadVarLanguage.getLang()) + "',href:'/manage/userList.jsp',target:'mainFrame'},       {text:'" + SystemEnv.getHtmlLabelName(383694, ThreadVarLanguage.getLang()) + "',href:'/manage/addUser.jsp',target:'mainFrame'}\t]}]";
/*    */ 
/*    */     
/* 29 */     return new JSONArray(str);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/eui/data/tree/eui/ManageTree.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */