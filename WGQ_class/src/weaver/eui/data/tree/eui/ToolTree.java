/*    */ package weaver.eui.data.tree.eui;
/*    */ 
/*    */ import java.io.File;
/*    */ import javax.servlet.http.HttpServletRequest;
/*    */ import javax.servlet.http.HttpServletResponse;
/*    */ import org.json.JSONArray;
/*    */ import org.json.JSONObject;
/*    */ import weaver.eui.data.tree.AbsTree;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class ToolTree
/*    */   extends AbsTree
/*    */ {
/*    */   public ToolTree(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/* 18 */     super(paramHttpServletRequest, paramHttpServletResponse);
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public JSONArray getTreeData(String paramString) throws Exception {
/* 24 */     String str1 = ToolTree.class.getClassLoader().getResource("").getPath();
/*    */     
/* 26 */     String str2 = getPara("treeType");
/*    */     
/* 28 */     String str3 = str1.substring(0, str1.lastIndexOf("eui")) + "ftp/resource/" + str2;
/*    */     
/* 30 */     return readfile(str3);
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public JSONArray readfile(String paramString) throws Exception {
/* 36 */     File file = new File(paramString);
/* 37 */     JSONArray jSONArray = new JSONArray();
/* 38 */     if (file.isDirectory()) {
/*    */       
/* 40 */       String[] arrayOfString = file.list();
/* 41 */       for (byte b = 0; b < arrayOfString.length; b++) {
/*    */         
/* 43 */         File file1 = new File(paramString + "/" + arrayOfString[b]);
/*    */         
/* 45 */         if (file1.isDirectory()) {
/* 46 */           JSONObject jSONObject = new JSONObject();
/* 47 */           jSONObject.put("text", file1.getName());
/*    */           
/* 49 */           JSONArray jSONArray1 = readfile(paramString + "/" + arrayOfString[b]);
/*    */           
/* 51 */           if (jSONArray1.length() == 0) {
/* 52 */             jSONObject.put("href", "/tools.jsp?dir=" + paramString + "/" + arrayOfString[b]);
/*    */             
/* 54 */             jSONObject.put("target", "mainFrame");
/*    */             
/* 56 */             jSONObject.put("isFolder", true);
/*    */           } 
/* 58 */           jSONObject.put("expanded", true);
/* 59 */           jSONObject.put("children", jSONArray1);
/* 60 */           jSONArray.put(jSONObject);
/*    */         } 
/*    */       } 
/*    */     } 
/*    */     
/* 65 */     return jSONArray;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/eui/data/tree/eui/ToolTree.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */