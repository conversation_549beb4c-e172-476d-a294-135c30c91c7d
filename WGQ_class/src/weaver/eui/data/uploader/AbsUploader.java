/*    */ package weaver.eui.data.uploader;
/*    */ 
/*    */ import javax.servlet.http.HttpServletRequest;
/*    */ import javax.servlet.http.HttpServletResponse;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public abstract class AbsUploader
/*    */ {
/* 12 */   public HttpServletRequest req = null;
/* 13 */   public HttpServletResponse res = null;
/*    */   public AbsUploader(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/* 15 */     this.req = paramHttpServletRequest;
/* 16 */     this.res = paramHttpServletResponse;
/*    */   }
/*    */   
/*    */   public abstract int getImageFileId() throws Exception;
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/eui/data/uploader/AbsUploader.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */