/*    */ package weaver.eui.data.uploader.doc;
/*    */ 
/*    */ import javax.servlet.http.HttpServletRequest;
/*    */ import javax.servlet.http.HttpServletResponse;
/*    */ import weaver.eui.data.uploader.AbsUploader;
/*    */ import weaver.file.FileUpload;
/*    */ import weaver.general.Util;
/*    */ 
/*    */ 
/*    */ public class Example
/*    */   extends AbsUploader
/*    */ {
/*    */   public Example(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/* 14 */     super(paramHttpServletRequest, paramHttpServletResponse);
/*    */   }
/*    */ 
/*    */   
/*    */   public int getImageFileId() throws Exception {
/* 19 */     FileUpload fileUpload = new FileUpload(this.req, "utf-8");
/* 20 */     return Util.getIntValue(fileUpload.uploadFiles("Filedata"));
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/eui/data/uploader/doc/Example.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */