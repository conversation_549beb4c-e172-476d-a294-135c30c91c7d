/*     */ package weaver.eui.tag;
/*     */ 
/*     */ import java.io.IOException;
/*     */ import javax.servlet.jsp.JspException;
/*     */ import javax.servlet.jsp.tagext.TagSupport;
/*     */ import weaver.hrm.User;
/*     */ import weaver.style.SystemStyleSetComInfo;
/*     */ 
/*     */ public class EuiPreload
/*     */   extends TagSupport
/*     */ {
/*  12 */   private String mode = "";
/*  13 */   private String style = "";
/*  14 */   private String lang = "cn";
/*     */   
/*     */   private String type;
/*     */   
/*     */   public int doStartTag() throws JspException {
/*  19 */     this.style = getSystemStyle();
/*     */     
/*  21 */     String str = "";
/*     */     
/*  23 */     if (isHaveType(this.type, "base")) {
/*  24 */       str = str + "<link type='text/css' rel='stylesheet'  href='/eui/common/css/" + this.style + "/eui.base.css'/>\n";
/*  25 */       str = str + "<script language='javascript' type='text/javascript' src='/eui/common/jquery.js'></script>\n";
/*  26 */       str = str + "<script language='javascript' type='text/javascript' src='/eui/common/jquery-ui.js'></script>\n";
/*  27 */       str = str + "<script language='javascript' type='text/javascript' src='/eui/common/jquery.cookie.js'></script>\n";
/*  28 */       str = str + "<script language='javascript' type='text/javascript' src='/eui/common/eui.util.js'></script>\n";
/*     */     } 
/*     */ 
/*     */     
/*  32 */     if ("debug".equalsIgnoreCase(this.mode)) {
/*  33 */       str = str + "<script language='javascript' type='text/javascript' src='/eui/firebug/firebug-lite-compressed.js'></script>\n";
/*  34 */       str = str + "<script language='javascript' type='text/javascript' src='/eui/common/elog.js'></script>\n";
/*  35 */     } else if ("run".equalsIgnoreCase(this.mode)) {
/*  36 */       str = str + "<script language='javascript' type='text/javascript' src='/eui/common/elog2.js'></script>\n";
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/*  41 */     if (isHaveType(this.type, "layout")) {
/*  42 */       str = str + "<link type='text/css' rel='stylesheet'  href='/eui/common/css/" + this.style + "/eui.layout.css'/>\n";
/*  43 */       str = str + "<script language='javascript' type='text/javascript' src='/eui/layout/jquery.layout.min.js'></script>\n";
/*     */     } 
/*     */ 
/*     */     
/*  47 */     if (isHaveType(this.type, "table")) {
/*  48 */       str = str + "<script language='javascript' type='text/javascript' src='/eui/common/jquery-jtemplates.js'></script>\n";
/*  49 */       str = str + "<link type='text/css' rel='stylesheet'  href='/eui/common/css/" + this.style + "/eui.table.css'/>\n";
/*  50 */       str = str + "<script language='javascript' type='text/javascript' src='/eui/common/i18n/" + this.lang + "/table.js'></script>\n";
/*  51 */       str = str + "<script language='javascript' type='text/javascript' src='/eui/table/eui.table.js'></script>\n";
/*     */     } 
/*     */     
/*  54 */     if (isHaveType(this.type, "tree")) {
/*  55 */       str = str + "<link type='text/css' rel='stylesheet'  href='/eui/common/css/" + this.style + "/eui.tree.css'/>\n";
/*  56 */       str = str + "<script language='javascript' type='text/javascript' src='/eui/tree/jquery.treeview.js'></script>\n";
/*  57 */       str = str + "<script language='javascript' type='text/javascript' src='/eui/tree/jquery.treeview.async.js'></script>\n";
/*  58 */       str = str + "<script language='javascript' type='text/javascript' src='/eui/tree/eui.tree.js'></script>\n";
/*     */       
/*  60 */       str = str + "<script language='javascript' type='text/javascript' src='/eui/tree/eui.tree.js'></script>\n";
/*     */     } 
/*  62 */     if (isHaveType(this.type, "dialog")) {
/*  63 */       str = str + "<link type='text/css' rel='stylesheet'  href='/eui/common/css/" + this.style + "/eui.dialog.css'/>\n";
/*  64 */       str = str + "<script language='javascript' type='text/javascript' src='/eui/dialog/eui.dialog.js'></script>\n";
/*     */     } 
/*  66 */     if (isHaveType(this.type, "btn")) {
/*  67 */       str = str + "<link type='text/css' rel='stylesheet'  href='/eui/common/css/" + this.style + "/eui.btn.css'/>\n";
/*  68 */       str = str + "<script language='javascript' type='text/javascript' src='/eui/btn/eui.btn.js'></script>\n";
/*     */     } 
/*  70 */     if (isHaveType(this.type, "tabs")) {
/*  71 */       str = str + "<script language='javascript' type='text/javascript' src='/eui/tabs/TabPanel.js'></script>\n";
/*  72 */       str = str + "<script language='javascript' type='text/javascript' src='/eui/tabs/eui.tabs.js'></script>\n";
/*  73 */       str = str + "<link type='text/css' rel='stylesheet' href='/eui/common/css/" + this.style + "/eui.tabs.css'/>\n";
/*     */     } 
/*     */     
/*  76 */     if (isHaveType(this.type, "date")) {
/*  77 */       str = str + "<script language='javascript' type='text/javascript' src='/eui/date/datetime.js'></script>\n";
/*  78 */       str = str + "<script language='javascript' type='text/javascript' src='/eui/date/WdatePicker.js'></script>\n";
/*  79 */       str = str + "<script language='javascript' type='text/javascript' src='/eui/date/eui.date.js'></script>\n";
/*  80 */       str = str + "<link type='text/css' rel='stylesheet' href='/eui/common/css/" + this.style + "/eui.date.css'/>\n";
/*     */     } 
/*     */     
/*  83 */     if (isHaveType(this.type, "time")) {
/*  84 */       str = str + "<script language='javascript' type='text/javascript' src='/eui/time/selectDateTime.js'></script>\n";
/*  85 */       str = str + "<script language='javascript' type='text/javascript' src='/eui/time/eui.time.js'></script>\n";
/*  86 */       str = str + "<link type='text/css' rel='stylesheet' href='/eui/common/css/" + this.style + "/eui.time.css'/>\n";
/*     */     } 
/*     */ 
/*     */     
/*  90 */     if (isHaveType(this.type, "context")) {
/*  91 */       str = str + "<link type='text/css' rel='stylesheet'  href='/eui/common/css/" + this.style + "/eui.context.css'/>\n";
/*  92 */       str = str + "<script language='javascript' type='text/javascript' src='/eui/context/jquery.contextMenu.js'></script>\n";
/*  93 */       str = str + "<script language='javascript' type='text/javascript' src='/eui/context/eui.context.js'></script>\n";
/*     */     } 
/*     */     
/*  96 */     if (isHaveType(this.type, "coder")) {
/*  97 */       str = str + "<script language='javascript' type='text/javascript' src='/eui/coder/jquery.chili-2.2.js'></script>\n";
/*  98 */       str = str + "<script language='javascript' type='text/javascript' src='/eui/coder/recipes.js'></script>\n";
/*  99 */       str = str + "<script language='javascript' type='text/javascript' src='/eui/coder/eui.coder.js'></script>\n";
/*     */     } 
/*     */ 
/*     */     
/* 103 */     if (isHaveType(this.type, "editor")) {
/*     */       
/* 105 */       str = str + "<script language='javascript' type='text/javascript' src='/eui/editor/fckeditor.js'></script>\n";
/* 106 */       str = str + "<script language='javascript' type='text/javascript' src='/eui/editor/editor.js'></script>\n";
/* 107 */       str = str + "<script language='javascript' type='text/javascript' src='/eui/editor/eui.editor.js'></script>\n";
/* 108 */       str = str + "<script language='javascript' type='text/javascript' >Editor.skinName='office2003';Editor.language='zh-cn';</script>\n";
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 114 */     if (isHaveType(this.type, "validate")) {
/* 115 */       str = str + "<script language='javascript' type='text/javascript' src='/eui/validate/easyui.js'></script>\n";
/* 116 */       str = str + "<script language='javascript' type='text/javascript' src='/eui/validate/eui.validate.js'></script>\n";
/*     */     } 
/*     */     
/* 119 */     if (isHaveType(this.type, "uploader")) {
/* 120 */       str = str + "<link type='text/css' rel='stylesheet' href='/eui/uploader/default.css'/>\n";
/* 121 */       str = str + "<script language='javascript' type='text/javascript' src='/eui/uploader/swfupload.js'></script>\n";
/* 122 */       str = str + "<script language='javascript' type='text/javascript' src='/eui/uploader/swfupload.queue.js'></script>\n";
/* 123 */       str = str + "<script language='javascript' type='text/javascript' src='/eui/uploader/fileprogress.js'></script>\n";
/* 124 */       str = str + "<script language='javascript' type='text/javascript' src='/eui/uploader/handlers.js'></script>\n";
/* 125 */       str = str + "<script language='javascript' type='text/javascript' src='/eui/uploader/eui.uploader.js'></script>\n";
/*     */     } 
/*     */     try {
/* 128 */       this.pageContext.getOut().write(str);
/* 129 */     } catch (IOException iOException) {
/* 130 */       iOException.printStackTrace();
/*     */     } 
/* 132 */     return 0;
/*     */   }
/*     */   public boolean isHaveType(String paramString1, String paramString2) {
/* 135 */     paramString1 = "," + paramString1 + ","; return 
/* 136 */       (paramString1.indexOf("," + paramString2 + ",") != -1);
/*     */   }
/*     */   public String getMode() {
/* 139 */     return this.mode;
/*     */   }
/*     */   public void setMode(String paramString) {
/* 142 */     this.mode = paramString;
/*     */   }
/*     */   public String getStyle() {
/* 145 */     return this.style;
/*     */   }
/*     */   public void setStyle(String paramString) {
/* 148 */     this.style = paramString;
/*     */   }
/*     */   public String getLang() {
/* 151 */     return this.lang;
/*     */   }
/*     */   public void setLang(String paramString) {
/* 154 */     this.lang = paramString;
/*     */   }
/*     */   public String getType() {
/* 157 */     return this.type;
/*     */   }
/*     */   public void setType(String paramString) {
/* 160 */     this.type = paramString;
/*     */   }
/*     */   
/*     */   private String getSystemStyle() {
/*     */     try {
/* 165 */       User user = (User)this.pageContext.getSession().getAttribute("weaver_user@bean");
/* 166 */       SystemStyleSetComInfo systemStyleSetComInfo = new SystemStyleSetComInfo();
/* 167 */       return systemStyleSetComInfo.getUserDefaultStyle(user.getUID(), user.getType());
/* 168 */     } catch (Exception exception) {
/* 169 */       exception.printStackTrace();
/*     */       
/* 171 */       return "gray";
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/eui/tag/EuiPreload.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */