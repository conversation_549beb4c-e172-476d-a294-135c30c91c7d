/*    */ package weaver.aop;
/*    */ 
/*    */ import java.lang.instrument.Instrumentation;
/*    */ 
/*    */ public class WStubPreMain
/*    */ {
/*  7 */   private static Instrumentation instLocal = null;
/*  8 */   private static Redefiner redefiner = null;
/*    */   
/*    */   public static void premain(String agentArgs, Instrumentation inst) {
/* 11 */     instLocal = inst;
/*    */   }
/*    */ 
/*    */   
/*    */   public static void doLoadConfig() {
/* 16 */     if (redefiner == null)
/* 17 */       redefiner = new Redefiner(instLocal); 
/* 18 */     redefiner.loadConfig();
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/aop/WStubPreMain.class
 * Java compiler version: 5 (49.0)
 * JD-Core Version:       1.1.3
 */