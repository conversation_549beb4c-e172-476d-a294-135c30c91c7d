/*    */ package weaver.aop;
/*    */ 
/*    */ import org.objectweb.asm.MethodVisitor;
/*    */ import org.objectweb.asm.commons.AdviceAdapter;
/*    */ 
/*    */ class WAdviceAdapter
/*    */   extends AdviceAdapter
/*    */ {
/*    */   private int access;
/*    */   private String className;
/*    */   private String targetClassName;
/*    */   private String name;
/*    */   private String targetName;
/*    */   private String desc;
/*    */   
/*    */   protected WAdviceAdapter(MethodVisitor mv, int access, String className, String targetClassName, String name, String targetName, String desc, String signature, String[] exceptions) {
/* 17 */     super(mv, access, name, desc);
/* 18 */     this.access = access;
/* 19 */     this.className = className;
/* 20 */     this.targetClassName = targetClassName.replace('.', '/');
/* 21 */     this.name = name;
/* 22 */     this.targetName = targetName;
/* 23 */     this.desc = desc;
/*    */   }
/*    */   
/*    */   protected void onMethodEnter() {
/* 27 */     if (!"<init>".equals(this.name)) {
/*    */       
/* 29 */       loadThis();
/* 30 */       loadArgArray();
/*    */ 
/*    */       
/* 33 */       visitMethodInsn(184, this.targetClassName, String.valueOf(this.targetName) + "_before", "(Ljava/lang/Object;[Ljava/lang/Object;)V");
/*    */     } 
/*    */   }
/*    */   
/*    */   protected void onMethodExit(int opcode) {
/* 38 */     loadThis();
/* 39 */     loadArgArray();
/* 40 */     visitMethodInsn(184, this.targetClassName, String.valueOf(this.targetName) + "_after", "(Ljava/lang/Object;[Ljava/lang/Object;)V");
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/aop/WAdviceAdapter.class
 * Java compiler version: 5 (49.0)
 * JD-Core Version:       1.1.3
 */