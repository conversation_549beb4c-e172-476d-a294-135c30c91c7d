/*    */ package weaver.aop;
/*    */ 
/*    */ import org.objectweb.asm.ClassAdapter;
/*    */ import org.objectweb.asm.ClassVisitor;
/*    */ import org.objectweb.asm.MethodVisitor;
/*    */ 
/*    */ public class WClassAdapter
/*    */   extends ClassAdapter {
/*    */   private String[] methods;
/*    */   private String className;
/*    */   private String targetClassName;
/*    */   
/*    */   public WClassAdapter(ClassVisitor cv, String className, String targetClassName, String[] methods) {
/* 14 */     super(cv);
/* 15 */     this.methods = methods;
/* 16 */     this.className = className;
/* 17 */     this.targetClassName = targetClassName;
/*    */   }
/*    */ 
/*    */   
/*    */   public MethodVisitor visitMethod(int access, String name, String desc, String signature, String[] exceptions) {
/* 22 */     MethodVisitor mv = this.cv.visitMethod(access, name, desc, signature, exceptions);
/* 23 */     if (mv == null || (access & 0x500) > 0) {
/* 24 */       return mv;
/*    */     }
/* 26 */     for (int i = 2; i < this.methods.length; i++) {
/* 27 */       String[] ma = this.methods[i].split("=");
/*    */       
/* 29 */       if ((String.valueOf(name) + desc).equals(ma[0])) {
/* 30 */         return (MethodVisitor)new WAdviceAdapter(mv, access, this.className, this.targetClassName, name, ma[1], desc, signature, exceptions);
/*    */       }
/*    */     } 
/*    */     
/* 34 */     return mv;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/aop/WClassAdapter.class
 * Java compiler version: 5 (49.0)
 * JD-Core Version:       1.1.3
 */