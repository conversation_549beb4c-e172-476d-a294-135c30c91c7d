/*     */ package weaver.aop;
/*     */ 
/*     */ import java.io.BufferedReader;
/*     */ import java.io.File;
/*     */ import java.io.FileReader;
/*     */ import java.io.IOException;
/*     */ import java.lang.instrument.ClassDefinition;
/*     */ import java.lang.instrument.Instrumentation;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Properties;
/*     */ import org.objectweb.asm.ClassReader;
/*     */ import org.objectweb.asm.ClassVisitor;
/*     */ import org.objectweb.asm.ClassWriter;
/*     */ 
/*     */ 
/*     */ public class Redefiner
/*     */ {
/*     */   private final Instrumentation inst;
/*  20 */   private static String wagentconfig = "";
/*  21 */   private static long prevLastModified = 0L;
/*     */ 
/*     */   
/*     */   public Redefiner(Instrumentation paramInstrumentation) {
/*  25 */     this.inst = paramInstrumentation;
/*     */     
/*  27 */     Properties properties = System.getProperties();
/*     */     
/*  29 */     String str = properties.getProperty("resin.home");
/*  30 */     if (!"".equals(str) && str != null) {
/*     */       
/*  32 */       File file = new File(str);
/*  33 */       if (file.exists())
/*  34 */         wagentconfig = file.getAbsolutePath() + File.separatorChar + "conf" + File.separatorChar + "wagent.conf"; 
/*     */     } 
/*  36 */     if ("".equals(wagentconfig))
/*  37 */       wagentconfig = "wagent.conf"; 
/*     */   }
/*     */   
/*     */   public void loadConfig() {
/*  41 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/*  43 */     BufferedReader bufferedReader = null;
/*     */     
/*     */     try {
/*  46 */       File file = new File(wagentconfig);
/*     */       
/*  48 */       long l = file.lastModified();
/*  49 */       if (prevLastModified == l || null == this.inst)
/*     */         return; 
/*  51 */       prevLastModified = l;
/*  52 */       bufferedReader = new BufferedReader(new FileReader(file));
/*  53 */       String str = null;
/*  54 */       ArrayList<String> arrayList = new ArrayList();
/*  55 */       while ((str = bufferedReader.readLine()) != null) {
/*  56 */         arrayList.add(str);
/*     */       }
/*  58 */       String[] arrayOfString = new String[arrayList.size()];
/*  59 */       for (byte b = 0; b < arrayList.size(); b++) {
/*  60 */         str = arrayList.get(b);
/*  61 */         String[] arrayOfString1 = str.split("\\|");
/*  62 */         if (arrayOfString1.length >= 3) {
/*     */           
/*  64 */           String str1 = arrayOfString1[0];
/*  65 */           if (!str1.equals(arrayOfString[b]))
/*     */           
/*  67 */           { arrayOfString[b] = str1;
/*  68 */             String str2 = arrayOfString1[1].split("=")[0];
/*  69 */             String str3 = arrayOfString1[1].split("=")[1];
/*  70 */             if (str1.equals("START"))
/*  71 */             { if (!hashMap.containsKey(str2)) {
/*  72 */                 hashMap.put(str2, getOriginClass(str2));
/*     */               }
/*  74 */               byte[] arrayOfByte = getWrappedClass(str2, str3, arrayOfString1);
/*     */               
/*  76 */               Class<?> clazz = Thread.currentThread().getContextClassLoader().loadClass(str2);
/*  77 */               ClassDefinition[] arrayOfClassDefinition = { new ClassDefinition(clazz, arrayOfByte) };
/*  78 */               this.inst.redefineClasses(arrayOfClassDefinition);
/*  79 */               System.out.println(str2 + " redefine to start....."); }
/*  80 */             else if (str1.equals("STOP"))
/*  81 */             { byte[] arrayOfByte = (byte[])hashMap.get(str2);
/*  82 */               if (arrayOfByte != null)
/*     */               
/*  84 */               { Class<?> clazz = Thread.currentThread().getContextClassLoader().loadClass(str2);
/*  85 */                 ClassDefinition[] arrayOfClassDefinition = { new ClassDefinition(clazz, arrayOfByte) };
/*  86 */                 this.inst.redefineClasses(arrayOfClassDefinition);
/*  87 */                 System.out.println(str2 + " redefine to stop....."); }  }  } 
/*     */         } 
/*     */       } 
/*  90 */     } catch (Exception exception) {
/*  91 */       exception.printStackTrace();
/*     */     } finally {
/*  93 */       if (bufferedReader != null) {
/*     */         try {
/*  95 */           bufferedReader.close();
/*  96 */         } catch (IOException iOException) {}
/*     */       }
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   private static byte[] getWrappedClass(String paramString1, String paramString2, String[] paramArrayOfString) {
/*     */     try {
/* 104 */       String str = paramString1.replace('.', '/') + ".class";
/* 105 */       ClassReader classReader = new ClassReader(Thread.currentThread().getContextClassLoader().getResourceAsStream(str));
/* 106 */       ClassWriter classWriter = new ClassWriter(1);
/* 107 */       WClassAdapter wClassAdapter = new WClassAdapter((ClassVisitor)classWriter, paramString1, paramString2, paramArrayOfString);
/* 108 */       classReader.accept((ClassVisitor)wClassAdapter, 8);
/* 109 */       return classWriter.toByteArray();
/* 110 */     } catch (Exception exception) {
/* 111 */       exception.printStackTrace();
/*     */       
/* 113 */       return null;
/*     */     } 
/*     */   }
/*     */   private static byte[] getOriginClass(String paramString) {
/*     */     try {
/* 118 */       String str = paramString.replace('.', '/') + ".class";
/* 119 */       ClassReader classReader = new ClassReader(Thread.currentThread().getContextClassLoader().getResourceAsStream(str));
/* 120 */       return classReader.b;
/* 121 */     } catch (Exception exception) {
/* 122 */       exception.printStackTrace();
/*     */       
/* 124 */       return null;
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/aop/Redefiner.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */