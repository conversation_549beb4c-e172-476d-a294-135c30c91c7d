/*     */ package weaver.workrelate.util;
/*     */ 
/*     */ import com.engine.common.service.impl.HrmCommonServiceImpl;
/*     */ import com.engine.workrelate.util.TaskUtil;
/*     */ import com.engine.workrelate.util.WorkrelateUtil;
/*     */ import java.util.ArrayList;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.crm.Maint.CustomerInfoComInfo;
/*     */ import weaver.docs.docs.DocComInfo;
/*     */ import weaver.docs.docs.DocImageManager;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.workflow.request.RequestComInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CommonTransUtil
/*     */   extends BaseBean
/*     */ {
/*  24 */   private ResourceComInfo rc = null;
/*  25 */   private CustomerInfoComInfo ci = null;
/*  26 */   private DocComInfo doc = null;
/*  27 */   private RequestComInfo request = null;
/*     */   
/*     */   public CommonTransUtil() {
/*     */     try {
/*  31 */       this.rc = new ResourceComInfo();
/*  32 */       this.ci = new CustomerInfoComInfo();
/*  33 */       this.doc = new DocComInfo();
/*  34 */       this.request = new RequestComInfo();
/*  35 */     } catch (Exception exception) {
/*  36 */       writeLog(exception);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCustomer(String paramString) {
/*  47 */     String str = "";
/*  48 */     if (paramString != null && !"".equals(paramString)) {
/*  49 */       ArrayList<String> arrayList = Util.TokenizerString(paramString, ",");
/*  50 */       for (byte b = 0; b < arrayList.size(); b++) {
/*  51 */         str = str + "<a href=javaScript:openFullWindowHaveBar('/CRM/data/ViewCustomer.jsp?log=n&CustomerID=" + (String)arrayList.get(b) + "')>" + this.ci.getCustomerInfoname(arrayList.get(b)) + "</a> ";
/*     */       }
/*     */     } 
/*  54 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCustomerForMobile(String paramString) {
/*  60 */     String str = "";
/*  61 */     if (paramString != null && !"".equals(paramString)) {
/*  62 */       ArrayList<String> arrayList = Util.TokenizerString(paramString, ",");
/*  63 */       for (byte b = 0; b < arrayList.size(); b++) {
/*  64 */         str = str + "<a href=\"javaScript:toCustomer('" + (String)arrayList.get(b) + "')\">" + this.ci.getCustomerInfoname(arrayList.get(b)) + "</a> ";
/*     */       }
/*     */     } 
/*  67 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getProject(String paramString) {
/*  77 */     String str = "";
/*  78 */     if (paramString.startsWith(",")) paramString = paramString.substring(1); 
/*  79 */     if (paramString.endsWith(",")) paramString = paramString.substring(0, paramString.length() - 1); 
/*  80 */     if (paramString != null && !"".equals(paramString)) {
/*  81 */       RecordSet recordSet = new RecordSet();
/*  82 */       recordSet.executeSql("select id,name from prj_projectinfo  where id in (" + paramString + ")");
/*  83 */       while (recordSet.next()) {
/*  84 */         str = str + "<a href=javaScript:openFullWindowHaveBar('/spa/prj/index.html#/main/prj/projectCard?prjid=" + recordSet.getString(1) + "')>" + recordSet.getString(2) + "</a> ";
/*     */       }
/*     */     } 
/*  87 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public String getProjectForMobile(String paramString) {
/*  93 */     String str = "";
/*  94 */     if (paramString.startsWith(",")) paramString = paramString.substring(1); 
/*  95 */     if (paramString.endsWith(",")) paramString = paramString.substring(0, paramString.length() - 1); 
/*  96 */     if (paramString != null && !"".equals(paramString)) {
/*  97 */       RecordSet recordSet = new RecordSet();
/*  98 */       recordSet.executeSql("select id,name from prj_projectinfo  where id in (" + paramString + ")");
/*  99 */       while (recordSet.next()) {
/* 100 */         str = str + "<a href=\"javaScript:toProject('" + recordSet.getString(1) + "')\">" + recordSet.getString(2) + "</a> ";
/*     */       }
/*     */     } 
/* 103 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getPerson(String paramString) {
/* 113 */     String str = "";
/* 114 */     if (paramString != null && !"".equals(paramString)) {
/* 115 */       ArrayList<String> arrayList = Util.TokenizerString(paramString, ",");
/* 116 */       for (byte b = 0; b < arrayList.size(); b++)
/*     */       {
/* 118 */         str = str + "<a href=\"javaScript:openhrm(" + arrayList.get(b) + ");\" onclick='pointerXY(event);'>" + this.rc.getResourcename(arrayList.get(b)) + "</a> ";
/*     */       }
/*     */     } 
/* 121 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getPerson2(String paramString) {
/* 129 */     String str = "";
/* 130 */     if (paramString != null && !"".equals(paramString)) {
/* 131 */       ArrayList<String> arrayList = Util.TokenizerString(paramString, ",");
/* 132 */       for (byte b = 0; b < arrayList.size(); b++) {
/* 133 */         str = str + this.rc.getResourcename(arrayList.get(b)) + " ";
/*     */       }
/*     */     } 
/* 136 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getHrm(String paramString) {
/* 146 */     String str = "";
/* 147 */     if (paramString != null && !"".equals(paramString)) {
/* 148 */       ArrayList<String> arrayList = Util.TokenizerString(paramString, ",");
/* 149 */       for (byte b = 0; b < arrayList.size(); b++)
/*     */       {
/* 151 */         str = str + "<a href='/hrm/resource/HrmResource.jsp?id=" + arrayList.get(b) + "' target='_blank'>" + this.rc.getResourcename(arrayList.get(b)) + "</a> ";
/*     */       }
/*     */     } 
/* 154 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getDocName(String paramString) {
/* 164 */     String str = "";
/* 165 */     if (paramString != null && !"".equals(paramString)) {
/* 166 */       ArrayList<String> arrayList = Util.TokenizerString(paramString, ",");
/* 167 */       for (byte b = 0; b < arrayList.size(); b++) {
/* 168 */         str = str + "<a href=javaScript:openFullWindowHaveBar('/docs/docs/DocDsp.jsp?id=" + arrayList.get(b) + "') >" + this.doc.getDocname(arrayList.get(b)) + "</a> ";
/*     */       }
/*     */     } 
/* 171 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public String getDocNameForMobile(String paramString) {
/* 177 */     String str = "";
/* 178 */     if (paramString != null && !"".equals(paramString)) {
/* 179 */       ArrayList<String> arrayList = Util.TokenizerString(paramString, ",");
/* 180 */       for (byte b = 0; b < arrayList.size(); b++) {
/* 181 */         str = str + "<a href=\"javaScript:toDocument('" + arrayList.get(b) + "')\">" + this.doc.getDocname(arrayList.get(b)) + "</a> ";
/*     */       }
/*     */     } 
/* 184 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getFileDoc(String paramString1, String paramString2) {
/* 194 */     String str = "";
/* 195 */     if (paramString1 != null && !"".equals(paramString1)) {
/* 196 */       ArrayList<String> arrayList = Util.TokenizerString(paramString1, ",");
/* 197 */       for (byte b = 0; b < arrayList.size(); b++) {
/* 198 */         str = str + "<a href=javaScript:openFullWindowHaveBar('/workrelate/task/util/ViewDoc.jsp?id=" + arrayList.get(b) + "&taskId=" + paramString2 + "') >" + this.doc.getDocname(arrayList.get(b)) + "</a> ";
/*     */       }
/*     */     } 
/* 201 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getRequestName(String paramString) {
/* 211 */     String str = "";
/* 212 */     if (paramString != null && !"".equals(paramString.trim())) {
/* 213 */       ArrayList<String> arrayList = Util.TokenizerString(paramString, ",");
/* 214 */       for (byte b = 0; b < arrayList.size(); b++) {
/* 215 */         str = str + "<a href=javaScript:openFullWindowHaveBar('/workflow/request/ViewRequest.jsp?requestid=" + arrayList.get(b) + "') >" + this.request.getRequestname(arrayList.get(b)) + "</a> ";
/*     */       }
/*     */     } 
/* 218 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public String getRequestNameForMobile(String paramString) {
/* 224 */     String str = "";
/* 225 */     if (paramString != null && !"".equals(paramString.trim())) {
/* 226 */       ArrayList<String> arrayList = Util.TokenizerString(paramString, ",");
/* 227 */       for (byte b = 0; b < arrayList.size(); b++) {
/* 228 */         str = str + "<a href=\"javaScript:toRequest('" + arrayList.get(b) + "')\" >" + this.request.getRequestname(arrayList.get(b)) + "</a> ";
/*     */       }
/*     */     } 
/* 231 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getTaskName(String paramString) {
/* 239 */     String str = "";
/* 240 */     paramString = cutString(paramString, ",", 3);
/* 241 */     if (paramString != null && !"".equals(paramString)) {
/* 242 */       RecordSet recordSet = new RecordSet();
/* 243 */       recordSet.executeSql("select id,name from TM_TaskInfo where id in (" + paramString + ") and ( deleted = 0 OR deleted IS NULL )");
/* 244 */       while (recordSet.next()) {
/* 245 */         str = str + "<a href='javascript:refreshDetail(" + recordSet.getString(1) + ")'>" + recordSet.getString(2) + "</a> ";
/*     */       }
/*     */     } 
/* 248 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getTaskNameForMobile(String paramString) {
/* 256 */     String str = "";
/* 257 */     paramString = cutString(paramString, ",", 3);
/* 258 */     if (paramString != null && !"".equals(paramString)) {
/* 259 */       RecordSet recordSet = new RecordSet();
/* 260 */       recordSet.executeSql("select id,name from TM_TaskInfo where id in (" + paramString + ") and ( deleted = 0 OR deleted IS NULL )");
/* 261 */       while (recordSet.next()) {
/* 262 */         str = str + "<a href='javascript:toTask(" + recordSet.getString(1) + ")'>" + recordSet.getString(2) + "</a> ";
/*     */       }
/*     */     } 
/* 265 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getTaskName2(String paramString) {
/* 273 */     String str = "";
/* 274 */     paramString = cutString(paramString, ",", 3);
/* 275 */     if (paramString != null && !"".equals(paramString)) {
/* 276 */       RecordSet recordSet = new RecordSet();
/* 277 */       recordSet.executeSql("select id,name from TM_TaskInfo where id in (" + paramString + ") and ( deleted = 0 OR deleted IS NULL )");
/* 278 */       while (recordSet.next()) {
/* 279 */         str = str + "<a href='javascript:showTask(" + recordSet.getString(1) + ")'>" + recordSet.getString(2) + "</a> ";
/*     */       }
/*     */     } 
/* 282 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getGoalName(String paramString) {
/* 290 */     String str = "";
/* 291 */     paramString = cutString(paramString, ",", 3);
/* 292 */     if (paramString != null && !"".equals(paramString)) {
/* 293 */       RecordSet recordSet = new RecordSet();
/* 294 */       recordSet.executeSql("select id,name from GM_GoalInfo where id in (" + paramString + ") and ( deleted = 0 OR deleted IS NULL )");
/* 295 */       while (recordSet.next()) {
/* 296 */         str = str + "<a href='javascript:refreshDetail(" + recordSet.getString(1) + ")'>" + recordSet.getString(2) + "</a> ";
/*     */       }
/*     */     } 
/* 299 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getGoalName2(String paramString) {
/* 307 */     String str = "";
/* 308 */     paramString = cutString(paramString, ",", 3);
/* 309 */     if (paramString != null && !"".equals(paramString)) {
/* 310 */       RecordSet recordSet = new RecordSet();
/* 311 */       recordSet.executeSql("select id,name from GM_GoalInfo where id in (" + paramString + ") and ( deleted = 0 OR deleted IS NULL )");
/* 312 */       while (recordSet.next()) {
/* 313 */         str = str + "<a href='javascript:showGoal(" + recordSet.getString(1) + ")'>" + recordSet.getString(2) + "</a> ";
/*     */       }
/*     */     } 
/* 316 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String cutString(String paramString1, String paramString2, int paramInt) {
/* 326 */     paramString1 = Util.null2String(paramString1);
/* 327 */     paramString2 = Util.null2String(paramString2);
/* 328 */     if (paramString1.equals("") || paramString2.equals("")) {
/* 329 */       return paramString1;
/*     */     }
/* 331 */     if ((paramInt == 1 || paramInt == 3) && 
/* 332 */       paramString1.startsWith(paramString2)) {
/* 333 */       paramString1 = paramString1.substring(paramString2.length());
/*     */     }
/*     */     
/* 336 */     if ((paramInt == 2 || paramInt == 3) && 
/* 337 */       paramString1.endsWith(paramString2)) {
/* 338 */       paramString1 = paramString1.substring(0, paramString1.length() - paramString2.length());
/*     */     }
/*     */     
/* 341 */     return paramString1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getDateTime(String paramString1, String paramString2) {
/* 352 */     return paramString1 + " " + paramString2;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getYN(String paramString1, String paramString2) {
/* 362 */     if ("1".equals(paramString1))
/* 363 */       return SystemEnv.getHtmlLabelName(163, Integer.parseInt(paramString2)); 
/* 364 */     if ("0".equals(paramString1)) {
/* 365 */       return SystemEnv.getHtmlLabelName(161, Integer.parseInt(paramString2));
/*     */     }
/* 367 */     return "";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getRight(String paramString, User paramUser) {
/* 377 */     if (paramString == null || "".equals(paramString)) {
/* 378 */       return 0;
/*     */     }
/* 380 */     byte b = 0;
/* 381 */     RecordSet recordSet = new RecordSet();
/*     */ 
/*     */ 
/*     */     
/* 385 */     String str = "select t1.creater,t1.principalid from TM_TaskInfo t1 where (t1.deleted=0 or t1.deleted is null)  and (t1.principalid=" + paramUser.getUID() + " or t1.creater=" + paramUser.getUID() + " or exists (select 1 from TM_TaskPartner tp where tp.taskid=t1.id and tp.partnerid=" + paramUser.getUID() + ") or exists (select 1 from TM_TaskSharer ts where ts.taskid=t1.id and ts.sharerid=" + paramUser.getUID() + ")";
/* 386 */     if ("1".equals(TaskUtil.getBaseSetTask("ismanagerview", "1")))
/*     */     {
/* 388 */       str = str + " or exists (select 1 from HrmResource hrm where (hrm.id=t1.principalid or hrm.id=t1.creater) and hrm.managerstr like '%," + paramUser.getUID() + ",%') or exists (select 1 from HrmResource hrm,TM_TaskPartner tp where tp.taskid=t1.id and hrm.id=tp.partnerid and hrm.managerstr like '%," + paramUser.getUID() + ",%')";
/*     */     }
/* 390 */     str = str + ") and t1.id=" + paramString;
/* 391 */     recordSet.executeSql(str);
/* 392 */     if (recordSet.next()) {
/* 393 */       b = 1;
/* 394 */       if (Util.getIntValue(recordSet.getString("principalid"), 0) == paramUser.getUID() || Util.getIntValue(recordSet.getString("creater"), 0) == paramUser.getUID()) {
/* 395 */         b = 2;
/*     */       }
/*     */     } 
/* 398 */     return b;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getGoalRight(String paramString, User paramUser) {
/* 407 */     if (paramString == null || "".equals(paramString)) {
/* 408 */       return 0;
/*     */     }
/* 410 */     int i = getGoalMaint(paramUser.getUID() + "")[0];
/* 411 */     if (i == 2) return i;
/*     */     
/* 413 */     int j = 0;
/* 414 */     RecordSet recordSet = new RecordSet();
/*     */     
/* 416 */     recordSet.executeSql("select t1.creater,t1.principalid from GM_GoalInfo t1 where (t1.deleted=0 or t1.deleted is null)  and (t1.principalid=" + paramUser
/* 417 */         .getUID() + " or t1.creater=" + paramUser.getUID() + " or exists (select 1 from GM_GoalPartner tp where goalid=t1.id and tp.partnerid=" + paramUser
/* 418 */         .getUID() + ") or exists (select 1 from GM_GoalSharer ts where goalid=t1.id and ts.sharerid=" + paramUser
/* 419 */         .getUID() + ") or exists (select 1 from HrmResource hrm where (hrm.id=t1.principalid or hrm.id=t1.creater) and hrm.managerstr like '%," + paramUser
/* 420 */         .getUID() + ",%') or exists (select 1 from HrmResource hrm,GM_GoalPartner tp where tp.goalid=t1.id and hrm.id=tp.partnerid and hrm.managerstr like '%," + paramUser
/* 421 */         .getUID() + ",%')) and t1.id=" + paramString);
/*     */     
/* 423 */     if (recordSet.next()) {
/* 424 */       j = 1;
/* 425 */       if (Util.getIntValue(recordSet.getString("principalid"), 0) == paramUser.getUID() || Util.getIntValue(recordSet.getString("creater"), 0) == paramUser.getUID()) {
/*     */         
/* 427 */         recordSet.executeSql("select isself from GM_BaseSetting");
/* 428 */         if (recordSet.next()) {
/* 429 */           int k = Util.getIntValue(recordSet.getString("isself"), 0);
/* 430 */           if (k == 1 && getGoalCreate(paramUser.getUID() + "") == 1) j = 2; 
/*     */         } 
/*     */       } 
/*     */     } 
/* 434 */     if (j < i) j = i; 
/* 435 */     return j;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int[] getGoalMaint(String paramString) {
/* 444 */     byte b = 0;
/* 445 */     int i = 0;
/* 446 */     RecordSet recordSet = new RecordSet();
/* 447 */     recordSet.executeSql("select goalmaint,iscgoal,isself from GM_BaseSetting");
/* 448 */     if (recordSet.next()) {
/* 449 */       String str = Util.null2String(recordSet.getString("goalmaint"));
/* 450 */       int j = Util.getIntValue(recordSet.getString("iscgoal"), 0);
/* 451 */       int k = Util.getIntValue(recordSet.getString("isself"), 0);
/* 452 */       if (j == 1) b = 1; 
/* 453 */       if (str.indexOf("," + paramString + ",") > -1) {
/* 454 */         b = 2;
/* 455 */         i = 1;
/* 456 */       } else if (k == 1) {
/* 457 */         i = getGoalCreate(paramString);
/*     */       } 
/*     */     } 
/* 460 */     return new int[] { b, i };
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getGoalCreate(String paramString) {
/* 468 */     RecordSet recordSet = new RecordSet();
/* 469 */     recordSet.executeQuery("SELECT * FROM GM_GOALCREATEAUTH order by id", new Object[0]);
/* 470 */     boolean bool = false;
/* 471 */     String str1 = this.rc.getSubCompanyID(paramString);
/* 472 */     String str2 = this.rc.getDepartmentID(paramString);
/* 473 */     Integer integer = Integer.valueOf(Util.getIntValue(this.rc.getSeclevel(paramString)));
/* 474 */     WorkrelateUtil workrelateUtil = new WorkrelateUtil();
/* 475 */     while (recordSet.next()) {
/* 476 */       String str3 = Util.null2String(recordSet.getString("sharetype"));
/* 477 */       String str4 = Util.null2String(recordSet.getString("objid"));
/* 478 */       String str5 = recordSet.getString("hassub");
/* 479 */       Integer integer1 = Integer.valueOf(recordSet.getInt("fromlevel"));
/* 480 */       Integer integer2 = Integer.valueOf(recordSet.getInt("tolevel"));
/* 481 */       if ("1".equals(str3)) {
/* 482 */         if (transRelateid(str4).contains(transRelateid(paramString)))
/* 483 */           bool = true;  continue;
/*     */       } 
/* 485 */       if ("2".equals(str3)) {
/* 486 */         if (integer.intValue() >= integer1.intValue() && integer.intValue() <= integer2.intValue()) {
/* 487 */           if (!str5.equals("0")) {
/* 488 */             str4 = workrelateUtil.getCpyIds(str4, "3");
/*     */           }
/* 490 */           if (transRelateid(str4).contains(transRelateid(str1)))
/* 491 */             bool = true; 
/*     */         }  continue;
/*     */       } 
/* 494 */       if ("3".equals(str3)) {
/* 495 */         if (integer.intValue() >= integer1.intValue() && integer.intValue() <= integer2.intValue()) {
/* 496 */           if (!str5.equals("0")) {
/* 497 */             str4 = workrelateUtil.getDeptIds(str4, "3");
/*     */           }
/* 499 */           if (transRelateid(str4).contains(transRelateid(str2)))
/* 500 */             bool = true; 
/*     */         }  continue;
/*     */       } 
/* 503 */       if ("4".equals(str3)) {
/* 504 */         if (integer.intValue() >= integer1.intValue() && integer.intValue() <= integer2.intValue())
/* 505 */           bool = true;  continue;
/*     */       } 
/* 507 */       if ("5".equals(str3)) {
/* 508 */         if (integer.intValue() >= integer1.intValue() && integer.intValue() <= integer2.intValue()) {
/* 509 */           String str6 = recordSet.getString("rolelevel");
/* 510 */           HrmCommonServiceImpl hrmCommonServiceImpl = new HrmCommonServiceImpl();
/* 511 */           String str7 = hrmCommonServiceImpl.getRoleMemberIds(str4, str6);
/* 512 */           if (transRelateid(str7).contains(transRelateid(paramString)))
/* 513 */             bool = true; 
/*     */         }  continue;
/*     */       } 
/* 516 */       if ("6".equals(str3)) {
/* 517 */         StringBuilder stringBuilder = new StringBuilder();
/* 518 */         String str6 = recordSet.getString("postlevel");
/* 519 */         String str7 = recordSet.getString("postobjid");
/* 520 */         stringBuilder.append("SELECT * FROM HrmResource where jobtitle IN (").append(str4).append(")");
/* 521 */         if ("1".equals(str6)) {
/* 522 */           stringBuilder.append(" AND subcompanyid1 in(");
/* 523 */           stringBuilder.append(str7);
/* 524 */           stringBuilder.append(")");
/*     */         } 
/* 526 */         if ("2".equals(str6)) {
/* 527 */           stringBuilder.append(" AND departmentid in(");
/* 528 */           stringBuilder.append(str7);
/* 529 */           stringBuilder.append(")");
/*     */         } 
/* 531 */         stringBuilder.append(" AND id=?");
/*     */         
/* 533 */         RecordSet recordSet1 = new RecordSet();
/* 534 */         recordSet1.executeQuery(stringBuilder.toString(), new Object[] { paramString });
/* 535 */         if (recordSet1.next()) {
/* 536 */           bool = true;
/*     */         }
/*     */       } 
/*     */     } 
/* 540 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String transRelateid(Object paramObject) {
/* 550 */     String str = Util.null2String(paramObject);
/* 551 */     if (str.equals(",")) str = ""; 
/* 552 */     if (!str.equals("")) {
/* 553 */       if (!str.startsWith(",")) str = "," + str; 
/* 554 */       if (!str.endsWith(",")) str = str + ","; 
/*     */     } 
/* 556 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getTaskStatus(String paramString1, String paramString2) {
/* 565 */     int i = Integer.parseInt(paramString2);
/* 566 */     if (paramString1.equals("1")) return SystemEnv.getHtmlLabelName(26577, i); 
/* 567 */     if (paramString1.equals("2")) return SystemEnv.getHtmlLabelName(23774, i); 
/* 568 */     if (paramString1.equals("3")) return SystemEnv.getHtmlLabelName(1981, i); 
/* 569 */     return "";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getFileNameForMobile(String paramString) throws Exception {
/* 583 */     String str1 = "";
/* 584 */     String str2 = "";
/* 585 */     String str3 = "";
/* 586 */     int i = 0;
/* 587 */     String str4 = "";
/* 588 */     DocImageManager docImageManager = null;
/* 589 */     if (paramString != null && !"".equals(paramString)) {
/* 590 */       ArrayList<String> arrayList = Util.TokenizerString(paramString, ",");
/* 591 */       for (byte b = 0; b < arrayList.size(); b++) {
/* 592 */         str2 = Util.null2String(arrayList.get(b));
/* 593 */         if (!str2.equals("")) {
/* 594 */           docImageManager = new DocImageManager();
/* 595 */           docImageManager.resetParameter();
/* 596 */           docImageManager.setDocid(Integer.parseInt(arrayList.get(b)));
/* 597 */           docImageManager.selectDocImageInfo();
/* 598 */           docImageManager.next();
/* 599 */           str3 = docImageManager.getImagefileid();
/* 600 */           i = docImageManager.getImageFileSize(Util.getIntValue(str3));
/* 601 */           str4 = docImageManager.getImagefilename();
/* 602 */           str1 = str1 + "<a href=\"javascript:toDownload('" + str3 + "','" + str4 + "')\" docid=\"" + str2 + "\" fileid=\"" + str3 + "\">" + str4 + "</a>";
/*     */         } 
/*     */       } 
/*     */     } 
/* 606 */     return str1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getShareRight(String paramString1, String paramString2, String paramString3) {
/* 616 */     ResourceComInfo resourceComInfo = null;
/*     */     try {
/* 618 */       resourceComInfo = new ResourceComInfo();
/* 619 */     } catch (Exception exception) {
/* 620 */       exception.printStackTrace();
/*     */     } 
/* 622 */     RecordSet recordSet = new RecordSet();
/* 623 */     recordSet.executeQuery("SELECT * FROM GM_WORKRELATESHAREAUTH where workrelatetype = ? and bizid = ?", new Object[] { paramString1, paramString3 });
/* 624 */     boolean bool = false;
/* 625 */     String str1 = resourceComInfo.getSubCompanyID(paramString2);
/* 626 */     String str2 = resourceComInfo.getDepartmentID(paramString2);
/* 627 */     Integer integer = Integer.valueOf(Util.getIntValue(resourceComInfo.getSeclevel(paramString2)));
/* 628 */     WorkrelateUtil workrelateUtil = new WorkrelateUtil();
/* 629 */     while (recordSet.next()) {
/* 630 */       String str3 = Util.null2String(recordSet.getString("sharetype"));
/* 631 */       String str4 = Util.null2String(recordSet.getString("objid"));
/* 632 */       String str5 = recordSet.getString("hassub");
/* 633 */       Integer integer1 = Integer.valueOf(recordSet.getInt("fromlevel"));
/* 634 */       Integer integer2 = Integer.valueOf(recordSet.getInt("tolevel"));
/* 635 */       if ("1".equals(str3)) {
/* 636 */         if (transRelateid(str4).contains(transRelateid(paramString2)))
/* 637 */           bool = true;  continue;
/*     */       } 
/* 639 */       if ("2".equals(str3)) {
/* 640 */         if (integer.intValue() >= integer1.intValue() && integer.intValue() <= integer2.intValue()) {
/* 641 */           if (!str5.equals("0")) {
/* 642 */             str4 = workrelateUtil.getCpyIds(str4, "3");
/*     */           }
/* 644 */           if (transRelateid(str4).contains(transRelateid(str1)))
/* 645 */             bool = true; 
/*     */         }  continue;
/*     */       } 
/* 648 */       if ("3".equals(str3)) {
/* 649 */         if (integer.intValue() >= integer1.intValue() && integer.intValue() <= integer2.intValue()) {
/* 650 */           if (!str5.equals("0")) {
/* 651 */             str4 = workrelateUtil.getDeptIds(str4, "3");
/*     */           }
/* 653 */           if (transRelateid(str4).contains(transRelateid(str2)))
/* 654 */             bool = true; 
/*     */         }  continue;
/*     */       } 
/* 657 */       if ("4".equals(str3)) {
/* 658 */         if (integer.intValue() >= integer1.intValue() && integer.intValue() <= integer2.intValue())
/* 659 */           bool = true;  continue;
/*     */       } 
/* 661 */       if ("5".equals(str3)) {
/* 662 */         if (integer.intValue() >= integer1.intValue() && integer.intValue() <= integer2.intValue()) {
/* 663 */           String str6 = recordSet.getString("rolelevel");
/* 664 */           HrmCommonServiceImpl hrmCommonServiceImpl = new HrmCommonServiceImpl();
/* 665 */           String str7 = hrmCommonServiceImpl.getRoleMemberIds(str4, str6);
/* 666 */           if (transRelateid(str7).contains(transRelateid(paramString2)))
/* 667 */             bool = true; 
/*     */         }  continue;
/*     */       } 
/* 670 */       if ("6".equals(str3)) {
/* 671 */         StringBuilder stringBuilder = new StringBuilder();
/* 672 */         String str6 = recordSet.getString("postlevel");
/* 673 */         String str7 = recordSet.getString("postobjid");
/* 674 */         stringBuilder.append("SELECT * FROM HrmResource where jobtitle IN (").append(str4).append(")");
/* 675 */         if ("1".equals(str6)) {
/* 676 */           stringBuilder.append(" AND subcompanyid1 in(");
/* 677 */           stringBuilder.append(str7);
/* 678 */           stringBuilder.append(")");
/*     */         } 
/* 680 */         if ("2".equals(str6)) {
/* 681 */           stringBuilder.append(" AND departmentid in(");
/* 682 */           stringBuilder.append(str7);
/* 683 */           stringBuilder.append(")");
/*     */         } 
/* 685 */         stringBuilder.append(" AND id=?");
/*     */         
/* 687 */         RecordSet recordSet1 = new RecordSet();
/* 688 */         recordSet1.executeQuery(stringBuilder.toString(), new Object[] { paramString2 });
/* 689 */         if (recordSet1.next()) {
/* 690 */           bool = true;
/*     */         }
/*     */       } 
/*     */     } 
/* 694 */     return bool;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workrelate/util/CommonTransUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */