/*    */ package weaver.workrelate.util;
/*    */ 
/*    */ import java.util.Map;
/*    */ import weaver.mobile.plugin.ecology.service.PushNotificationService;
/*    */ 
/*    */ public class SendMobileMsgThread
/*    */   implements Runnable {
/*    */   private static PushNotificationService pns;
/*    */   private String sendUsers;
/*    */   
/*    */   static {
/*    */     try {
/* 13 */       pns = new PushNotificationService();
/* 14 */     } catch (Exception exception) {
/* 15 */       exception.printStackTrace();
/*    */     } 
/*    */   }
/*    */ 
/*    */   
/*    */   private String msg;
/*    */   
/*    */   private int num;
/*    */   
/*    */   private Map para;
/*    */ 
/*    */   
/*    */   public SendMobileMsgThread(String paramString1, String paramString2, int paramInt, Map paramMap) {
/* 28 */     this.sendUsers = paramString1;
/* 29 */     this.msg = paramString2;
/* 30 */     this.num = paramInt;
/* 31 */     this.para = paramMap;
/*    */   }
/*    */   
/*    */   public void run() {
/* 35 */     pns.push(this.sendUsers, this.msg, this.num, this.para);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workrelate/util/SendMobileMsgThread.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */