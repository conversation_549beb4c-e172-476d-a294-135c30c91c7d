/*     */ package weaver.workrelate.util;
/*     */ 
/*     */ import com.cloudstore.dev.api.bean.MessageBean;
/*     */ import com.cloudstore.dev.api.bean.MessageType;
/*     */ import com.cloudstore.dev.api.util.Util_Message;
/*     */ import com.cloudstore.dev.api.util.Util_Serializer;
/*     */ import com.engine.workrelate.util.SendMsgUtil;
/*     */ import java.util.ArrayList;
/*     */ import java.util.List;
/*     */ import java.util.Set;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class SendMsg
/*     */ {
/*     */   private static ResourceComInfo rc;
/*     */   
/*     */   static {
/*     */     try {
/*  28 */       rc = new ResourceComInfo();
/*  29 */     } catch (Exception exception) {
/*  30 */       exception.printStackTrace();
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static void sendMsg(String paramString1, String paramString2, String paramString3, String paramString4, String paramString5, String paramString6, String paramString7, String paramString8, String paramString9, String paramString10) {
/*     */     try {
/*  46 */       if (!Util.null2String(paramString1).equals("") && !Util.null2String(paramString3).equals("")) {
/*  47 */         RecordSet recordSet1 = new RecordSet();
/*  48 */         RecordSet recordSet2 = new RecordSet();
/*  49 */         recordSet1.executeQuery("select * from TM_TaskInfo where id=? and (deleted = 0 or deleted is null)", new Object[] { paramString1 });
/*  50 */         if (recordSet1.next()) {
/*  51 */           String str1 = "";
/*  52 */           ArrayList<String> arrayList1 = new ArrayList();
/*     */ 
/*     */ 
/*     */           
/*  56 */           ArrayList<String> arrayList2 = new ArrayList();
/*  57 */           String str2 = Util.null2String(recordSet1.getString("name"));
/*  58 */           String str3 = Util.null2String(recordSet1.getString("creater"));
/*  59 */           String str4 = Util.null2String(recordSet1.getString("principalid"));
/*  60 */           String str5 = SystemEnv.getHtmlLabelName(271, Util.getIntValue(rc.getSystemLanguage(str3), 7)) + "：" + rc.getLastname(str3) + "<br/>";
/*  61 */           str5 = str5 + SystemEnv.getHtmlLabelName(1339, Util.getIntValue(rc.getSystemLanguage(str3), 7)) + "：" + recordSet1.getString("createdate") + " " + recordSet1.getString("createtime");
/*  62 */           String str6 = "84046";
/*  63 */           String str7 = Util.null2String(recordSet1.getString("status"));
/*  64 */           if (paramString3.equals("newTask")) {
/*  65 */             str1 = SystemEnv.getHtmlLabelName(84046, Util.getIntValue(rc.getSystemLanguage(str3), 7)) + "【" + str2 + "】";
/*  66 */             arrayList1.add(str4);
/*  67 */           } else if (paramString3.equals("newFb")) {
/*  68 */             str1 = SystemEnv.getHtmlLabelName(1332, Util.getIntValue(rc.getSystemLanguage(str3), 7)) + "【" + str2 + "】" + SystemEnv.getHtmlLabelName(84048, Util.getIntValue(rc.getSystemLanguage(str3), 7));
/*  69 */             arrayList1.add(str3);
/*  70 */             arrayList1.add(str4);
/*  71 */             str6 = "382603";
/*  72 */           } else if (paramString3.equals("newPartners")) {
/*  73 */             str1 = SystemEnv.getHtmlLabelName(84046, Util.getIntValue(rc.getSystemLanguage(str3), 7)) + "【" + str2 + "】";
/*  74 */             if (paramString4 != null && !paramString4.equals("")) {
/*  75 */               ArrayList<String> arrayList = Util.TokenizerString(paramString4, ",");
/*  76 */               String str = "";
/*  77 */               for (byte b = 0; b < arrayList.size(); b++) {
/*  78 */                 str = arrayList.get(b);
/*  79 */                 arrayList1.add(str);
/*     */               } 
/*     */             } 
/*  82 */           } else if (paramString3.equals("newDutyMan") || paramString3.equals("resendTask")) {
/*  83 */             str1 = SystemEnv.getHtmlLabelName(84046, Util.getIntValue(rc.getSystemLanguage(str3), 7)) + "【" + str2 + "】";
/*  84 */             arrayList1.add(str4);
/*  85 */           } else if (paramString3.equals("acceptTask")) {
/*  86 */             str1 = SystemEnv.getHtmlLabelName(84046, Util.getIntValue(rc.getSystemLanguage(str3), 7)) + "【" + str2 + "】";
/*  87 */           } else if (paramString3.equals("refuseTask")) {
/*  88 */             arrayList1.add(str3);
/*  89 */             str6 = "515082";
/*  90 */             str1 = SystemEnv.getHtmlLabelName(515070, Util.getIntValue(rc.getSystemLanguage(str3), 7)) + "【" + str2 + "】";
/*  91 */           } else if (paramString3.equals("submitAudit")) {
/*  92 */             arrayList1.add(paramString6);
/*  93 */             str6 = "16410";
/*  94 */             str1 = SystemEnv.getHtmlLabelName(518618, Util.getIntValue(rc.getSystemLanguage(str3), 7)) + "【" + str2 + "】";
/*  95 */             str7 = "4";
/*  96 */           } else if (paramString3.equals("urgeTask")) {
/*  97 */             if ("".equals(paramString8)) {
/*  98 */               str1 = SystemEnv.getHtmlLabelName(517828, Util.getIntValue(rc.getSystemLanguage(str3), 7)) + "【" + str2 + "】";
/*     */             } else {
/* 100 */               str1 = paramString8 + "【" + str2 + "】";
/*     */             } 
/* 102 */             str6 = "517825";
/* 103 */             if ("1".equals(paramString6)) {
/* 104 */               arrayList1.add(str4);
/*     */             }
/* 106 */             if ("0".equals(paramString7)) {
/* 107 */               str7 = "4";
/*     */             }
/*     */           } 
/* 110 */           if (!paramString3.equals("newPartners") && !paramString3.equals("newDutyMan") && !"4".equals(str7) && !"5".equals(str7)) {
/* 111 */             recordSet2.executeQuery("select partnerid from TM_TaskPartner where taskid=?", new Object[] { paramString1 });
/* 112 */             while (recordSet2.next()) {
/* 113 */               arrayList1.add(recordSet2.getString("partnerid"));
/*     */             }
/*     */           } 
/* 116 */           if (null != arrayList1 && arrayList1.size() > 0) {
/* 117 */             for (byte b = 0; b < arrayList1.size(); b++) {
/* 118 */               String str = arrayList1.get(b);
/* 119 */               if (!"".equals(str) && !str.equals(paramString2) && !arrayList2.contains(str)) {
/* 120 */                 String str8 = rc.getLoginID(str);
/* 121 */                 if (null != str8 && !str8.equals(""))
/*     */                 {
/*     */ 
/*     */                   
/* 125 */                   arrayList2.add(str);
/*     */                 }
/*     */               } 
/*     */             } 
/* 129 */             if (arrayList2 != null && arrayList2.size() > 0) {
/* 130 */               SendMsgUtil.sendMsg(63, arrayList2, str1, str6, str5, "/spa/workrelate/static/index.html#/main/workrelate/task/taskDetail?id=" + paramString1, "/spa/workrelate/static4mobile/index.html#/taskHome/taskDetail/" + paramString1);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */             
/*     */             }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */           
/*     */           }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */         
/*     */         }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/*     */       }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*     */     }
/* 187 */     catch (Exception exception) {
/* 188 */       exception.printStackTrace();
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   public static void sendWFMsg(String paramString1, String paramString2, List paramList) {}
/*     */ 
/*     */   
/*     */   public static void delSortedMessage(String paramString) {
/*     */     try {
/* 198 */       Set set = Util_Message.zrevRange(paramString);
/* 199 */       for (byte[] arrayOfByte : set) {
/*     */         
/* 201 */         MessageBean messageBean = (MessageBean)Util_Serializer.deserialize(arrayOfByte);
/* 202 */         if (messageBean.getMessageType() == MessageType.TASK_ARRIVAL) {
/* 203 */           Util_Message.delSortedMessage(paramString, arrayOfByte);
/*     */         }
/*     */       } 
/* 206 */     } catch (Exception exception) {
/* 207 */       exception.printStackTrace();
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workrelate/util/SendMsg.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */