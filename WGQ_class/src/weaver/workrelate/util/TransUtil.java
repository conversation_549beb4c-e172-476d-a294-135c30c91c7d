/*    */ package weaver.workrelate.util;
/*    */ 
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.docview.CommUtil;
/*    */ import weaver.file.Prop;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.User;
/*    */ 
/*    */ public class TransUtil
/*    */ {
/*    */   public static String getReviewLink(String paramString1, String paramString2, int paramInt1, User paramUser, int paramInt2, String paramString3, String paramString4, String paramString5) {
/* 12 */     return getReviewLink(paramString1, paramString2, paramInt1, "fullwindow", paramUser, paramInt2, paramString3, paramString4, paramString5);
/*    */   }
/*    */   
/*    */   public static String getReviewLink(String paramString1, String paramString2, int paramInt1, String paramString3, User paramUser, int paramInt2, String paramString4, String paramString5, String paramString6) {
/* 16 */     String str = CommUtil.getReviewLink(paramString1, paramString2, paramInt1, paramString3, paramUser);
/* 17 */     return makeurl(str, paramInt2, paramString4, paramString5, paramString6);
/*    */   }
/*    */   
/*    */   public static String getReviewButton(String paramString1, String paramString2, int paramInt1, User paramUser, int paramInt2, String paramString3, String paramString4, String paramString5) {
/* 21 */     return getReviewButton(paramString1, paramString2, paramInt1, "fullwindow", paramUser, paramInt2, paramString3, paramString4, paramString5);
/*    */   }
/*    */   
/*    */   public static String getReviewButton(String paramString1, String paramString2, int paramInt1, String paramString3, User paramUser, int paramInt2, String paramString4, String paramString5, String paramString6) {
/* 25 */     String str = CommUtil.getReviewButton(paramString1, paramString2, paramInt1, paramString3, paramUser);
/* 26 */     return makeurl(str, paramInt2, paramString4, paramString5, paramString6);
/*    */   }
/*    */   
/*    */   public static String makeurl(String paramString1, int paramInt, String paramString2, String paramString3, String paramString4) {
/* 30 */     String str = "";
/* 31 */     if (!paramString1.equals("")) {
/* 32 */       String str1 = "";
/* 33 */       if (paramInt == 1) str1 = "/workrelate/goal/util/ViewDoc.jsp?review=1&id=" + paramString2 + "&taskId=" + paramString3 + "&"; 
/* 34 */       if (paramInt == 2) str1 = "/workrelate/plan/util/ViewDoc.jsp?review=1&id=" + paramString2 + "&planid=" + paramString3 + "&plandetailid=" + paramString4 + "&"; 
/* 35 */       if (paramInt == 3) str1 = "/workrelate/task/util/ViewDoc.jsp?review=1&id=" + paramString2 + "&taskId=" + paramString3 + "&"; 
/* 36 */       if (paramInt == 4) str1 = "/performance/util/ViewDoc.jsp?review=1&id=" + paramString2 + "&scoreid=" + paramString3 + "&"; 
/* 37 */       str = paramString1.replaceAll("/docview/main.jsp[?]", str1);
/*    */     } 
/* 39 */     return str;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public static boolean isgoal() {
/* 46 */     return ismoduls("goal");
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public static boolean isplan() {
/* 53 */     return ismoduls("plan");
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public static boolean istask() {
/* 60 */     return ismoduls("task");
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public static boolean isperformance() {
/* 67 */     return ismoduls("performance");
/*    */   }
/*    */   
/*    */   public static boolean ismoduls(String paramString) {
/* 71 */     return Util.null2String(Prop.getPropValue("workrelate", "is" + paramString)).equals("1") ? true : (
/* 72 */       Util.null2String(Prop.getPropValue("workrelate_" + paramString, "status")).equals("1"));
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public static int hassub(String paramString) {
/* 81 */     RecordSet recordSet = new RecordSet();
/* 82 */     boolean bool = false;
/* 83 */     recordSet.executeSql("select count(id) as amount from hrmresource where " + ("sqlserver".equals(recordSet.getDBType()) ? " loginid<>'' and " : "") + " loginid is not null and (status =0 or status = 1 or status = 2 or status = 3) and managerid=" + paramString);
/* 84 */     if (recordSet.next() && recordSet.getInt(1) > 0) {
/* 85 */       bool = true;
/*    */     }
/* 87 */     return bool;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public static int getsubcount(String paramString) {
/* 95 */     RecordSet recordSet = new RecordSet();
/* 96 */     int i = 0;
/* 97 */     recordSet.executeSql("select count(id) as amount from hrmresource where " + ("sqlserver".equals(recordSet.getDBType()) ? " loginid<>'' and " : "") + " loginid is not null and (status =0 or status = 1 or status = 2 or status = 3) and managerid=" + paramString);
/* 98 */     if (recordSet.next()) i = recordSet.getInt(1); 
/* 99 */     return i;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workrelate/util/TransUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */