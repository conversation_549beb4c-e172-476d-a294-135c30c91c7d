/*    */ package weaver.workrelate.util;
/*    */ 
/*    */ import java.lang.reflect.Method;
/*    */ import java.util.List;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class SendWechatMsgThread
/*    */   implements Runnable
/*    */ {
/*    */   private List<String> userList;
/*    */   private int type;
/*    */   private String content;
/*    */   private String taskId;
/*    */   
/*    */   public SendWechatMsgThread(String paramString1, List<String> paramList, int paramInt, String paramString2) {
/* 19 */     this.taskId = paramString1;
/* 20 */     this.userList = paramList;
/* 21 */     this.type = paramInt;
/* 22 */     this.content = paramString2;
/*    */   }
/*    */   
/*    */   public void run() {
/*    */     try {
/* 27 */       Class<?> clazz = Class.forName("weaver.wxinterface.InterfaceUtil");
/* 28 */       Method method = clazz.getDeclaredMethod("sendMsg", new Class[] { List.class, int.class, String.class, String.class });
/* 29 */       method.invoke(clazz, new Object[] { this.userList, Integer.valueOf(this.type), this.taskId, this.content });
/* 30 */     } catch (Exception exception) {}
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/workrelate/util/SendWechatMsgThread.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */