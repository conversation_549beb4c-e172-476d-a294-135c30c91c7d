/*    */ package weaver.crm.investigate;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ import weaver.cache.CacheBase;
/*    */ import weaver.cache.CacheColumn;
/*    */ import weaver.cache.CacheColumnType;
/*    */ import weaver.cache.PKColumn;
/*    */ import weaver.crm.Maint.CustomerContacterComInfo;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class ContacterComInfo
/*    */   extends CacheBase
/*    */   implements Serializable
/*    */ {
/* 20 */   CustomerContacterComInfo customerContacterComInfo = new CustomerContacterComInfo();
/*    */   
/*    */   private static final long serialVersionUID = 4584648332421442591L;
/*    */   
/* 24 */   protected static String TABLE_NAME = "CRM_CustomerContacter";
/*    */   
/*    */   @PKColumn(type = CacheColumnType.NUMBER)
/* 27 */   protected static String PK_NAME = "id";
/*    */ 
/*    */ 
/*    */   
/*    */   @CacheColumn
/*    */   protected static int id;
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public int getContacterNum() {
/* 38 */     return this.customerContacterComInfo.size();
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getContacterId() {
/* 51 */     return this.customerContacterComInfo.getCustomerContacterid();
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getContacterName() {
/* 59 */     return this.customerContacterComInfo.getCustomerContactername();
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getContacterName(String paramString) {
/* 68 */     return this.customerContacterComInfo.getCustomerContactername(paramString);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/crm/investigate/ContacterComInfo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */