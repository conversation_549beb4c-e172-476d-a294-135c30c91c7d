/*     */ package weaver.crm;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.crm.Maint.ContractComInfo;
/*     */ import weaver.crm.Maint.CustomerInfoComInfo;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.company.DepartmentComInfo;
/*     */ import weaver.hrm.resource.AllManagers;
/*     */ import weaver.hrm.resource.AllSubordinate;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class ContractViewer
/*     */   extends BaseBean
/*     */ {
/*     */   private AllManagers allmanagers;
/*     */   private AllSubordinate allsubordinates;
/*     */   private RecordSet RecordSet;
/*     */   private CustomerInfoComInfo cuscominfo;
/*     */   private ResourceComInfo resourcecominfo;
/*     */   private DepartmentComInfo departmentcominfo;
/*     */   private ContractComInfo contractcominfo;
/*     */   
/*     */   public void setContractShareById(String paramString) throws Exception {
/*  34 */     this.allmanagers = new AllManagers();
/*  35 */     this.RecordSet = new RecordSet();
/*  36 */     this.cuscominfo = new CustomerInfoComInfo();
/*  37 */     this.resourcecominfo = new ResourceComInfo();
/*  38 */     this.departmentcominfo = new DepartmentComInfo();
/*  39 */     this.contractcominfo = new ContractComInfo();
/*     */     
/*  41 */     ArrayList<String> arrayList1 = new ArrayList();
/*  42 */     ArrayList<String> arrayList2 = new ArrayList();
/*  43 */     ArrayList<String> arrayList3 = new ArrayList();
/*  44 */     ArrayList<String> arrayList4 = new ArrayList();
/*     */     
/*  46 */     char c = Util.getSeparator();
/*     */ 
/*     */ 
/*     */     
/*  50 */     String str1 = this.contractcominfo.getContractcrmid(paramString);
/*  51 */     String str2 = this.cuscominfo.getCustomerInfomanager(str1);
/*  52 */     String str3 = "1";
/*  53 */     String str4 = "";
/*  54 */     String str5 = "";
/*     */ 
/*     */     
/*  57 */     arrayList1.add(str2 + "_1");
/*  58 */     arrayList2.add(str2);
/*  59 */     arrayList3.add("1");
/*  60 */     arrayList4.add("1");
/*     */ 
/*     */     
/*  63 */     this.allmanagers.getAll(str2);
/*  64 */     while (this.allmanagers.next()) {
/*  65 */       String str = this.allmanagers.getManagerID();
/*  66 */       if (arrayList1.indexOf(str + "_1") == -1) {
/*  67 */         arrayList1.add(str + "_1");
/*  68 */         arrayList2.add(str);
/*  69 */         arrayList3.add("1");
/*  70 */         arrayList4.add("1");
/*     */       } 
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/*  76 */     str2 = this.contractcominfo.getManagerid(paramString);
/*  77 */     int i = arrayList1.indexOf(str2 + "_1");
/*  78 */     if (i == -1) {
/*  79 */       arrayList1.add(str2 + "_1");
/*  80 */       arrayList2.add(str2);
/*  81 */       arrayList3.add("1");
/*  82 */       arrayList4.add("2");
/*     */     } else {
/*  84 */       arrayList4.set(i, "2");
/*     */     } 
/*     */     
/*  87 */     this.allmanagers.getAll(str2);
/*  88 */     while (this.allmanagers.next()) {
/*  89 */       String str = this.allmanagers.getManagerID();
/*  90 */       i = arrayList1.indexOf(str + "_1");
/*  91 */       if (i == -1) {
/*  92 */         arrayList1.add(str + "_1");
/*  93 */         arrayList2.add(str);
/*  94 */         arrayList3.add("1");
/*  95 */         arrayList4.add("3"); continue;
/*     */       } 
/*  97 */       arrayList4.set(i, "3");
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/* 102 */     str4 = this.resourcecominfo.getDepartmentID(str2);
/* 103 */     str5 = this.departmentcominfo.getSubcompanyid1(str4);
/*     */ 
/*     */     
/* 106 */     String str6 = "";
/*     */ 
/*     */     
/* 109 */     ArrayList<String> arrayList5 = new ArrayList();
/* 110 */     str6 = "select roleid from SystemRightRoles where rightid = 396";
/* 111 */     this.RecordSet.executeSql(str6);
/* 112 */     while (this.RecordSet.next())
/* 113 */       arrayList5.add(this.RecordSet.getString("roleid")); 
/*     */     byte b;
/* 115 */     for (b = 0; b < arrayList5.size(); b++) {
/*     */       
/* 117 */       String str = arrayList5.get(b);
/* 118 */       str6 = "select resourceid from hrmrolemembers  t1, hrmresource  t2  where roleid=" + str + " and t1.resourceid=t2.id and (rolelevel=2 or " + " (rolelevel=0 ";
/*     */ 
/*     */       
/* 121 */       if (!str4.equals("")) {
/* 122 */         str6 = str6 + "and t2.departmentid=" + str4 + "";
/*     */       }
/* 124 */       str6 = str6 + ") or (t1.rolelevel=1 ";
/* 125 */       if (!str5.equals("")) {
/* 126 */         str6 = str6 + "and t2.subcompanyid1=" + str5;
/*     */       }
/* 128 */       str6 = str6 + "))";
/* 129 */       this.RecordSet.executeSql(str6);
/* 130 */       while (this.RecordSet.next()) {
/* 131 */         String str7 = Util.null2String(this.RecordSet.getString(1));
/* 132 */         int j = arrayList1.indexOf(str7 + "_1");
/* 133 */         if (j != -1)
/*     */         {
/* 135 */           if (Util.getIntValue((String)arrayList4.get(j), 0) >= 2)
/*     */             continue; 
/*     */         }
/* 138 */         arrayList1.add(str7 + "_1");
/* 139 */         arrayList2.add(str7);
/* 140 */         arrayList3.add("1");
/* 141 */         arrayList4.add("2");
/*     */       } 
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 148 */     str6 = " select distinct t1.id , t2.sharelevel from HrmResource t1 ,  Contract_ShareInfo  t2  where  ( (t2.foralluser=1 and t2.seclevel<=t1.seclevel)  or ( t2.userid= t1.id ) or (t2.departmentid=t1.departmentid and t2.seclevel<=t1.seclevel) )  and t1.id <> 0 and t2.relateditemid = " + paramString;
/*     */     
/* 150 */     this.RecordSet.executeSql(str6);
/* 151 */     while (this.RecordSet.next()) {
/* 152 */       String str7 = Util.null2String(this.RecordSet.getString(1));
/* 153 */       String str8 = Util.null2String(this.RecordSet.getString(2));
/* 154 */       int j = arrayList1.indexOf(str7 + "_1");
/* 155 */       if (j != -1) {
/* 156 */         if (((String)arrayList4.get(j)).equals("1") && str8.equals("2"))
/* 157 */           arrayList4.set(j, "2"); 
/*     */         continue;
/*     */       } 
/* 160 */       arrayList1.add(str7 + "_1");
/* 161 */       arrayList2.add(str7);
/* 162 */       arrayList3.add("1");
/* 163 */       arrayList4.add(str8);
/*     */     } 
/*     */ 
/*     */     
/* 167 */     str6 = " select distinct t1.id , t2.sharelevel \tfrom HrmResource t1 ,  Contract_ShareInfo  t2,  HrmRoleMembers  t3 \twhere  ( t3.resourceid=t1.id and t3.roleid=t2.roleid and t3.rolelevel>=t2.rolelevel \t\t\t\t\t and(  \t\t\t\t\t\t   (t2.rolelevel=0  and t1.departmentid=" + str4 + ") or  " + "\t\t\t\t\t\t   (t2.rolelevel=1 ";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 173 */     if (!str5.equals("")) {
/* 174 */       str6 = str6 + " and t1.subcompanyid1=" + str5 + "";
/*     */     }
/* 176 */     str6 = str6 + "\t\t\t\t\t     )   or t3.rolelevel=2 )  \t\t\t\t\tand t1.id <> 0  \t\t\t\t\tand t2.relateditemid = " + paramString + " )";
/*     */ 
/*     */ 
/*     */     
/* 180 */     this.RecordSet.executeSql(str6);
/* 181 */     while (this.RecordSet.next()) {
/* 182 */       String str7 = Util.null2String(this.RecordSet.getString(1));
/* 183 */       String str8 = Util.null2String(this.RecordSet.getString(2));
/* 184 */       int j = arrayList1.indexOf(str7 + "_1");
/* 185 */       if (j != -1) {
/* 186 */         if (((String)arrayList4.get(j)).equals("1") && str8.equals("2"))
/* 187 */           arrayList4.set(j, "2"); 
/*     */         continue;
/*     */       } 
/* 190 */       arrayList1.add(str7 + "_1");
/* 191 */       arrayList2.add(str7);
/* 192 */       arrayList3.add("1");
/* 193 */       arrayList4.add(str8);
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/* 198 */     this.RecordSet.executeProc("ContractShareDetail_DById", paramString);
/*     */ 
/*     */ 
/*     */     
/* 202 */     for (b = 0; b < arrayList1.size(); b++) {
/*     */       
/* 204 */       String str = paramString + c + (String)arrayList2.get(b) + c + (String)arrayList3.get(b) + c + (String)arrayList4.get(b);
/*     */       
/* 206 */       this.RecordSet.executeProc("ContractShareDetail_Insert", "" + str);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setContractShareByCrmId(String paramString) throws Exception {
/* 218 */     RecordSet recordSet = new RecordSet();
/* 219 */     recordSet.executeSql("select id from CRM_Contract where crmId = " + paramString);
/* 220 */     while (recordSet.next()) {
/*     */       
/* 222 */       String str = recordSet.getString("id");
/* 223 */       setContractShareById(str);
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/crm/ContractViewer.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */