/*     */ package weaver.crm;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.crm.Maint.CustomerInfoComInfo;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.company.DepartmentComInfo;
/*     */ import weaver.hrm.resource.AllManagers;
/*     */ import weaver.hrm.resource.AllSubordinate;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CrmViewer
/*     */   extends BaseBean
/*     */ {
/*     */   private AllManagers allmanagers;
/*     */   private AllSubordinate allsubordinates;
/*     */   private RecordSet RecordSet;
/*     */   private CustomerInfoComInfo cuscominfo;
/*     */   private ResourceComInfo resourcecominfo;
/*     */   private DepartmentComInfo departmentcominfo;
/*     */   
/*     */   public void setCrmShareByCrm(String paramString) throws Exception {}
/*     */   
/*     */   public void setCrmShareByHrm(String paramString) throws Exception {}
/*     */   
/*     */   public void changeCrmShareByManager(String paramString) throws Exception {
/* 295 */     this.allmanagers = new AllManagers();
/* 296 */     this.RecordSet = new RecordSet();
/*     */     
/* 298 */     ArrayList<String> arrayList1 = new ArrayList();
/* 299 */     ArrayList<String> arrayList2 = new ArrayList();
/*     */     
/* 301 */     char c = Util.getSeparator();
/*     */     
/* 303 */     this.allmanagers.getAll(paramString);
/*     */     
/* 305 */     while (this.allmanagers.next()) {
/* 306 */       String str = this.allmanagers.getManagerID();
/*     */ 
/*     */ 
/*     */       
/* 310 */       this.RecordSet.executeProc("CrmShareDetail_SByResourceId", str);
/* 311 */       while (this.RecordSet.next()) {
/* 312 */         arrayList1.add(Util.null2String(this.RecordSet.getString(1)));
/*     */       }
/*     */ 
/*     */ 
/*     */       
/* 317 */       this.allsubordinates = new AllSubordinate();
/* 318 */       this.allsubordinates.getAll(str);
/*     */       
/* 320 */       while (this.allsubordinates.next()) {
/* 321 */         String str1 = this.allsubordinates.getSubordinateID();
/*     */         
/* 323 */         String str2 = "select id from CRM_CustomerInfo where manager=" + str1;
/*     */         
/* 325 */         this.RecordSet.executeSql(str2);
/*     */         
/* 327 */         while (this.RecordSet.next()) {
/* 328 */           if (arrayList1.indexOf(Util.null2String(this.RecordSet.getString(1))) >= 0)
/* 329 */             continue;  arrayList2.add(Util.null2String(this.RecordSet.getString(1)));
/*     */         } 
/*     */       } 
/*     */ 
/*     */ 
/*     */       
/* 335 */       for (byte b = 0; b < arrayList2.size(); b++) {
/*     */         
/* 337 */         String str1 = (String)arrayList2.get(b) + c + str + c + "1" + c + "3";
/*     */         
/* 339 */         this.RecordSet.executeProc("CrmShareDetail_Insert", "" + str1);
/*     */ 
/*     */         
/* 342 */         str1 = (String)arrayList2.get(b) + c + str;
/* 343 */         this.RecordSet.executeProc("CRM_ShareByHrm_WorkPlan", "" + str1);
/*     */       } 
/*     */ 
/*     */       
/* 347 */       arrayList1.clear();
/* 348 */       arrayList2.clear();
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/crm/CrmViewer.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */