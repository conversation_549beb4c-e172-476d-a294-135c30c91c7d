/*     */ package weaver.crm.Maint;
/*     */ 
/*     */ import weaver.cache.CacheBase;
/*     */ import weaver.cache.CacheColumn;
/*     */ import weaver.cache.CacheColumnType;
/*     */ import weaver.cache.CacheItem;
/*     */ import weaver.cache.CacheMap;
/*     */ import weaver.cache.PKColumn;
/*     */ import weaver.conn.RecordSet;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CustomerContacterComInfo
/*     */   extends CacheBase
/*     */ {
/*  17 */   protected static String TABLE_NAME = "CRM_CustomerContacter";
/*     */   @PKColumn(type = CacheColumnType.NUMBER)
/*  19 */   protected static String PK_NAME = "id";
/*     */   
/*     */   @CacheColumn
/*     */   protected static int id;
/*     */   
/*     */   @CacheColumn
/*     */   protected static int firstname;
/*     */   
/*     */   @CacheColumn
/*     */   protected static int title;
/*     */   
/*     */   @CacheColumn
/*     */   protected static int mobilephone;
/*     */   
/*     */   @CacheColumn
/*     */   protected static int email;
/*     */   
/*     */   protected CacheMap initCache() throws Exception {
/*  37 */     return null;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public CacheItem initCache(String paramString) {
/*  44 */     if (paramString == null || "".equals(paramString.trim())) {
/*  45 */       return null;
/*     */     }
/*  47 */     return super.initCache(paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getCustomerContacterNum() {
/*  55 */     return size();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCustomerContacterid() {
/*  63 */     return (String)getRowValue(id);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCustomerContactername() {
/*  70 */     return (String)getRowValue(firstname);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCustomerContactername(String paramString) {
/*  79 */     return (String)getValue(firstname, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCustomerContactertitle() {
/*  86 */     return (String)getRowValue(title);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCustomerContactertitle(String paramString) {
/*  95 */     return (String)getValue(title, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCustomerContacternameByID(String paramString) {
/* 105 */     String str = "";
/* 106 */     RecordSet recordSet = new RecordSet();
/* 107 */     recordSet.executeSql("SELECT firstname FROM CRM_CustomerContacter where id =" + paramString);
/* 108 */     if (recordSet.next()) {
/* 109 */       str = recordSet.getString("firstname");
/*     */     }
/* 111 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCustomerContactermobilephone() {
/* 118 */     return (String)getRowValue(mobilephone);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCustomerContactermobilephone(String paramString) {
/* 127 */     return (String)getValue(mobilephone, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCustomerContacteremail() {
/* 134 */     return (String)getRowValue(email);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCustomerContacteremail(String paramString) {
/* 143 */     return (String)getValue(email, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void removeCustomerContacterCache() {
/* 152 */     removeCache();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void deleteContacterInfoCache(String paramString) {
/* 161 */     deleteCache(paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean addContacterInfoCache(String paramString) {
/* 170 */     if ("".equals(paramString.trim()))
/* 171 */       return false; 
/* 172 */     synchronized (getClass()) {
/* 173 */       addCache(paramString);
/*     */     } 
/* 175 */     return true;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void updateContacterInfoCache(String paramString) throws Exception {
/* 184 */     updateCache(paramString);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/crm/Maint/CustomerContacterComInfo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */