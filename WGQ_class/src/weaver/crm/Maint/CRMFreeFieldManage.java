/*     */ package weaver.crm.Maint;
/*     */ 
/*     */ import com.weaver.integration.util.IntegratedSapUtil;
/*     */ import java.util.ArrayList;
/*     */ import java.util.List;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.conn.RecordSetTrans;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.GCONST;
/*     */ import weaver.general.StaticObj;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.interfaces.workflow.browser.Browser;
/*     */ import weaver.parseBrowser.SapBrowserComInfo;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.workflow.field.BrowserComInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CRMFreeFieldManage
/*     */   extends BaseBean
/*     */ {
/*  24 */   private RecordSet rs = new RecordSet();
/*  25 */   private String browurlArr = ",2,4,8,16,17,18,37,57,135,152,164,194,1,7,9,58,258,264,265,59,60,61,62,261,63,59,263,161,162,";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getItemFieldTypeSelectForAddMainRow(User paramUser) {
/*  43 */     String str1 = "";
/*     */     
/*  45 */     if (paramUser == null) {
/*  46 */       return str1;
/*     */     }
/*  48 */     BrowserComInfo browserComInfo = new BrowserComInfo();
/*  49 */     String str2 = "1";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  57 */     str1 = str1 + "<div style='float:left;width:100px;'><select class='InputStyle' name='itemFieldType_\" + rowindex + \"'  onChange='onChangItemFieldType(\"  + rowindex +  \")'><option value='1' " + getSelectedForItemFieldType(str2, "1") + ">" + SystemEnv.getHtmlLabelName(688, paramUser.getLanguage()) + "</option><option value='2' " + getSelectedForItemFieldType(str2, "2") + ">" + SystemEnv.getHtmlLabelName(689, paramUser.getLanguage()) + "</option><option value='3' " + getSelectedForItemFieldType(str2, "3") + ">" + SystemEnv.getHtmlLabelName(695, paramUser.getLanguage()) + "</option><option value='4' " + getSelectedForItemFieldType(str2, "4") + ">" + SystemEnv.getHtmlLabelName(691, paramUser.getLanguage()) + "</option><option value='5' " + getSelectedForItemFieldType(str2, "5") + ">" + SystemEnv.getHtmlLabelName(690, paramUser.getLanguage()) + "</option><option value='6' " + getSelectedForItemFieldType(str2, "6") + ">" + SystemEnv.getHtmlLabelName(17616, paramUser.getLanguage()) + "</option></select></div>";
/*     */ 
/*     */     
/*  60 */     String str3 = "style='display:none;padding-left:5px;'";
/*  61 */     String str4 = "style='display:none;padding-left:5px;'";
/*  62 */     String str5 = "style='display:none;padding-left:5px;'";
/*  63 */     String str6 = "style='display:none;padding-left:5px;'";
/*  64 */     String str7 = "style='display:none;padding-left:5px;'";
/*  65 */     String str8 = "style='display:none;padding-left:5px;'";
/*  66 */     String str9 = "style='display:none;padding-left:5px;'";
/*  67 */     String str10 = "style='display:none;padding-left:5px;'";
/*  68 */     String str11 = "style='display:none;padding-left:5px;'";
/*     */     
/*  70 */     if ("1".equals(str2)) {
/*  71 */       str3 = "style='float:left;padding-left:5px;width:100px;'";
/*  72 */     } else if ("2".equals(str2)) {
/*  73 */       str4 = "style='float:left;padding-left:5px;'";
/*  74 */     } else if ("3".equals(str2)) {
/*  75 */       str5 = "style='float:left;padding-left:5px;'";
/*  76 */     } else if (!"4".equals(str2)) {
/*  77 */       if ("5".equals(str2)) {
/*  78 */         str6 = "style='float:left;padding-left:5px;'";
/*  79 */       } else if ("6".equals(str2)) {
/*  80 */         str7 = "style='float:left;padding-left:5px;'";
/*  81 */       } else if ("7".equals(str2)) {
/*  82 */         str9 = "style='float:left;padding-left:5px;'";
/*  83 */         str10 = "style='display:block;padding-left:5px;'";
/*     */       } 
/*  85 */     }  String str12 = IntegratedSapUtil.getIsOpenEcology70Sap();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  91 */     str1 = str1 + "<div id=div1_\" + rowindex + \" " + str3 + " > &nbsp;" + SystemEnv.getHtmlLabelName(63, paramUser.getLanguage()) + "&nbsp;<select class='InputStyle' name='documentType_\" + rowindex + \"' style='width:100px;'  onChange='onChangType(\"  + rowindex +  \")'><option value='1'>" + SystemEnv.getHtmlLabelName(608, paramUser.getLanguage()) + "</option><option value='2'>" + SystemEnv.getHtmlLabelName(696, paramUser.getLanguage()) + "</option><option value='3'>" + SystemEnv.getHtmlLabelName(697, paramUser.getLanguage()) + "</option></select></div>";
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  96 */     str1 = str1 + "<div id='div1_1_\" + rowindex + \"' " + str3 + " >&nbsp;&nbsp;&nbsp;" + SystemEnv.getHtmlLabelName(698, paramUser.getLanguage()) + " <input class='InputStyle' type='text' size=3 maxlength=3 id='itemFieldScale1_\" + rowindex + \"' name='itemFieldScale1_\" + rowindex + \"' onKeyPress='ItemPlusCount_KeyPress()' onblur='checkPlusnumber1(this);checklength(itemFieldScale1_\" + rowindex + \",itemFieldScale1span_\" + rowindex + \");checkcount1(itemFieldScale1_\" + rowindex + \")' style='text-align:right;'><span id=itemFieldScale1span_\" + rowindex + \"><IMG src='/images/BacoError_wev8.gif' align=absMiddle></span></div>";
/*     */ 
/*     */ 
/*     */     
/* 100 */     str1 = str1 + "<div id='div1_3_\" + rowindex + \"' style='display:none;width:80px;'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;" + SystemEnv.getHtmlLabelName(15212, paramUser.getLanguage()) + "&nbsp;<select id='decimaldigits_\" + rowindex + \"' style='width:80px;' name='decimaldigits_\" + rowindex + \"'><option value='1' >1</option><option value='2' selected>2</option><option value='3' >3</option><option value='4' >4</option></select></div>";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 110 */     str1 = str1 + "<div id=div2_\" + rowindex + \" " + str4 + " >" + SystemEnv.getHtmlLabelName(207, paramUser.getLanguage()) + " <input class='InputStyle' type='text' size=4 maxlength=2 value=4 id=textheight_\" + rowindex + \" name='textheight_\" + rowindex + \"' onKeyPress='ItemPlusCount_KeyPress()' onblur='checkPlusnumber1(this);checkcount1(textheight_\" + rowindex + \")' style='text-align:right;'></div>";
/*     */ 
/*     */ 
/*     */     
/* 114 */     str1 = str1 + "<div id=div3_\" + rowindex + \" style='display:none;float:left;padding-left:5px;' > &nbsp;" + SystemEnv.getHtmlLabelName(63, paramUser.getLanguage()) + "<select class='InputStyle' name='broswerType_\" + rowindex + \"' onChange='browserChange(this,\"+rowindex+\")'>";
/*     */     
/* 116 */     while (browserComInfo.next()) {
/* 117 */       if (browserComInfo.getBrowserurl().equals("") || (
/* 118 */         "0".equals(str12) && "224".equals(browserComInfo.getBrowserid())) || "225".equals(browserComInfo.getBrowserid())) {
/*     */         continue;
/*     */       }
/* 121 */       if (-1 != this.browurlArr.indexOf("," + browserComInfo.getBrowserid() + ",")) {
/* 122 */         str1 = str1 + "<option url='" + browserComInfo.getBrowserurl() + "' value='" + browserComInfo.getBrowserid() + "'>" + SystemEnv.getHtmlLabelName(Util.getIntValue(browserComInfo.getBrowserlabelid(), 0), paramUser.getLanguage()) + "</option>";
/*     */       }
/*     */     } 
/* 125 */     str1 = str1 + "</select></div>";
/*     */     
/* 127 */     str1 = str1 + "<div id=div3_0_\" + rowindex + \" " + str5 + " > <span><IMG src='" + GCONST.getContextPath() + "/images/BacoError_wev8.gif' align=absMiddle></span>";
/* 128 */     str1 = str1 + "</div>";
/* 129 */     str1 = str1 + "<div id=div3_1_\" + rowindex + \" " + str5 + " > <select class='InputStyle' name='definebroswerType_\" + rowindex + \"' onChange='div3_0_show(\"+rowindex+\")'>";
/*     */     
/* 131 */     List<String> list = StaticObj.getServiceIds(Browser.class);
/* 132 */     for (byte b1 = 0; b1 < list.size(); b1++) {
/* 133 */       str1 = str1 + "<option value='" + list.get(b1) + "'>" + list.get(b1) + "</option>";
/*     */     }
/* 135 */     str1 = str1 + "</select></div>";
/* 136 */     str1 = str1 + "<div id=div3_4_\" + rowindex + \" " + str5 + " > <select class='InputStyle' name='sapbrowser_\" + rowindex + \"' onChange='div3_4_show(\"+rowindex+\")'>";
/*     */     
/* 138 */     ArrayList<String> arrayList = (new SapBrowserComInfo()).getAllBrowserId();
/* 139 */     for (byte b2 = 0; b2 < arrayList.size(); b2++) {
/* 140 */       str1 = str1 + "<option value='" + arrayList.get(b2) + "'>" + arrayList.get(b2) + "</option>";
/*     */     }
/* 142 */     str1 = str1 + "</select></div>";
/*     */     
/* 144 */     str1 = str1 + "<div id=div3_5_\" + rowindex + \" " + str5 + " > ";
/* 145 */     str1 = str1 + " <button type=button  class='browser' name=newsapbrowser_\" + rowindex + \" id=newsapbrowser_\" + rowindex + \" onclick='OnNewChangeSapBroswerType(\" + rowindex + \")'></button>";
/* 146 */     str1 = str1 + " <span id='showinner_\" + rowindex + \"'></span>";
/* 147 */     str1 = str1 + " <span id='showimg_\" + rowindex + \"'><IMG src='" + GCONST.getContextPath() + "/images/BacoError_wev8.gif' align=absMiddle></span>";
/* 148 */     str1 = str1 + " <input type='hidden' name='showvalue_\" + rowindex + \"' id='showvalue_\" + rowindex + \"'></div>";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 154 */     str1 = str1 + "<div id=div3_2_\" + rowindex + \" " + str5 + " > " + SystemEnv.getHtmlLabelName(19340, paramUser.getLanguage()) + "<select class='InputStyle' name='decentralizationbroswerType_\" + rowindex + \"'><option value='1' selected>" + SystemEnv.getHtmlLabelName(18916, paramUser.getLanguage()) + "</option><option value='2'>" + SystemEnv.getHtmlLabelName(18919, paramUser.getLanguage()) + "</option>";
/* 155 */     str1 = str1 + "</select></div>";
/*     */     
/* 157 */     str1 = str1 + "<div id=div5_\" + rowindex + \" " + str6 + " > <input type=button value=" + SystemEnv.getHtmlLabelName(32714, paramUser.getLanguage()) + " class=e8_btn_top onclick=javascript:maintOption(\" + rowindex +\"); > <select class='InputStyle' notBeauty=true style='max-width:150px' name='selectOption_\" + rowindex + \"' ></select></div>";
/*     */ 
/*     */ 
/*     */     
/* 161 */     str1 = str1 + "<div id=div5_5_\" + rowindex + \" " + str6 + " > </div>";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 167 */     str1 = str1 + "<div id=div6_\" + rowindex + \" " + str7 + " > &nbsp;" + SystemEnv.getHtmlLabelName(63, paramUser.getLanguage()) + "<select class='InputStyle' name='uploadtype_\" + rowindex + \"'  onChange='onuploadtype(this, \"  + rowindex +  \")'><option value='1'>" + SystemEnv.getHtmlLabelName(20798, paramUser.getLanguage()) + "</option><option value='2'>" + SystemEnv.getHtmlLabelName(20001, paramUser.getLanguage()) + "</option></select></div>";
/*     */ 
/*     */ 
/*     */     
/* 171 */     str1 = str1 + "<div id=div6_1_\" + rowindex + \" " + str8 + " > " + SystemEnv.getHtmlLabelName(203, paramUser.getLanguage()) + "<input class=inputstyle type=text name=imgwidth_\" + rowindex + \" size=6 value=100 maxlength=4 onKeyPress='ItemPlusCount_KeyPress()' onBlur='checkPlusnumber1(this)'>" + SystemEnv.getHtmlLabelName(207, paramUser.getLanguage()) + "<input class=inputstyle type=text name=imgheight_\" + rowindex + \" size=6 value=100 maxlength=4 onKeyPress='ItemPlusCount_KeyPress()' onBlur='checkPlusnumber1(this)'></div>";
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 176 */     str1 = str1 + "<div id=div7_\" + rowindex + \" " + str9 + " > &nbsp;" + SystemEnv.getHtmlLabelName(63, paramUser.getLanguage()) + "<select class='InputStyle' name='specialfield_\" + rowindex + \"'  onChange='specialtype(this, \"  + rowindex +  \")'><option value='1'>" + SystemEnv.getHtmlLabelName(21692, paramUser.getLanguage()) + "</option><option value='2'>" + SystemEnv.getHtmlLabelName(21693, paramUser.getLanguage()) + "</option></select></div>";
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 181 */     str1 = str1 + "<div id=div7_1_\" + rowindex + \" " + str10 + " > <table width=100% class='ViewForm' border=0><tr><td width=50%>" + SystemEnv.getHtmlLabelName(606, paramUser.getLanguage()) + "　　<input class=inputstyle type=text name=displayname_\" + rowindex + \" size=25 maxlength=1000></td></tr><tr><td width=100%>" + SystemEnv.getHtmlLabelName(16208, paramUser.getLanguage()) + "　<input class=inputstyle type=text size=25 name=linkaddress_\" + rowindex + \" maxlength=1000><br>" + SystemEnv.getHtmlLabelName(18391, paramUser.getLanguage()) + "</td></tr></table></div>";
/*     */     
/* 183 */     str1 = str1 + "<div id=div7_2_\" + rowindex + \" " + str11 + " > <table width=100% class='ViewForm' border=0><tr><td width=12%>" + SystemEnv.getHtmlLabelName(21693, paramUser.getLanguage()) + "</td><td>　<textarea class='inputstyle' style='width:88%;height:100px' name=descriptivetext_\" + rowindex + \"></textarea></td></tr></table></div>";
/*     */     
/* 185 */     str1 = str1 + "<div id=browspan_\"+ rowindex +\" style='display:none;float:left;padding-left:5px;'></div>\t";
/* 186 */     return str1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String getSelectedForItemFieldType(String paramString1, String paramString2) {
/* 199 */     String str = "";
/*     */     
/* 201 */     if (paramString1 == null || paramString2 == null) {
/* 202 */       return str;
/*     */     }
/*     */     
/* 205 */     if (paramString1.equals(paramString2)) {
/* 206 */       str = "selected";
/*     */     }
/*     */     
/* 209 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public synchronized int getNewIndexId(RecordSetTrans paramRecordSetTrans) {
/* 218 */     int i = -1;
/*     */ 
/*     */ 
/*     */     
/*     */     try {
/* 223 */       paramRecordSetTrans.executeSql("select min(id) as id from HtmlLabelIndex");
/* 224 */       if (paramRecordSetTrans.next()) {
/* 225 */         i = paramRecordSetTrans.getInt("id") - 1;
/* 226 */         if (i > -2) i = -2; 
/*     */       } 
/* 228 */     } catch (Exception exception) {
/* 229 */       i = -1;
/*     */     } 
/* 231 */     return i;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getItemFieldTypeSelectForReviewMainRow(User paramUser, String paramString1, String paramString2, String paramString3, String paramString4, int paramInt, String paramString5, String paramString6, String paramString7, String paramString8, String paramString9) {
/* 242 */     String str = "";
/* 243 */     BrowserComInfo browserComInfo = new BrowserComInfo();
/*     */     
/* 245 */     if (paramUser == null) {
/* 246 */       return str;
/*     */     }
/*     */ 
/*     */     
/* 250 */     str = str + "<input type='hidden' name='itemFieldType_" + paramString1 + "' value='" + paramString2 + "'/>";
/*     */     
/* 252 */     if (paramString2.equals("1")) {
/* 253 */       str = str + "<input type='hidden' name='documentType_" + paramString1 + "' value='" + paramString3 + "'/>";
/* 254 */       str = str + SystemEnv.getHtmlLabelName(688, paramUser.getLanguage());
/* 255 */       if (paramString3.equals("1")) {
/* 256 */         str = str + "<input type='hidden' name='itemFieldScale1_" + paramString1 + "' value='" + paramString4 + "'/>";
/* 257 */         str = str + "&nbsp;" + SystemEnv.getHtmlLabelName(698, paramUser.getLanguage()) + "&nbsp;" + paramString4;
/*     */       } 
/* 259 */       if (paramString3.equals("2")) str = str + "&nbsp;" + SystemEnv.getHtmlLabelName(696, paramUser.getLanguage()); 
/* 260 */       if (paramString3.equals("3")) {
/* 261 */         String str1 = paramString4.split(",")[1];
/*     */         
/* 263 */         str = str + "<input type='hidden' name='decimaldigits_" + paramString1 + "' value='" + str1 + "'/>";
/* 264 */         str = str + "&nbsp;" + SystemEnv.getHtmlLabelName(697, paramUser.getLanguage());
/* 265 */         str = str + "&nbsp;" + SystemEnv.getHtmlLabelName(15212, paramUser.getLanguage()) + "&nbsp;" + str1;
/*     */       }
/*     */     
/*     */     }
/* 269 */     else if (paramString2.equals("2")) {
/* 270 */       str = str + "<input type='hidden' name='textheight_" + paramString1 + "' value='" + paramInt + "'/>";
/* 271 */       str = str + SystemEnv.getHtmlLabelName(689, paramUser.getLanguage());
/* 272 */       str = str + "&nbsp;" + SystemEnv.getHtmlLabelName(207, paramUser.getLanguage()) + "&nbsp;" + paramInt;
/*     */     }
/* 274 */     else if (paramString2.equals("3")) {
/* 275 */       str = str + "<input type='hidden' name='broswerType_" + paramString1 + "' value='" + paramString3 + "'/>";
/* 276 */       str = str + SystemEnv.getHtmlLabelName(695, paramUser.getLanguage());
/* 277 */       str = str + "&nbsp;" + SystemEnv.getHtmlLabelName(Util.getIntValue(browserComInfo.getBrowserlabelid(paramString3 + "")), paramUser.getLanguage());
/* 278 */       if ("161".equals(paramString3) || "162".equals(paramString3)) {
/* 279 */         str = str + "&nbsp;" + getName(paramString9);
/*     */       }
/* 281 */     } else if (paramString2.equals("4")) {
/* 282 */       str = str + SystemEnv.getHtmlLabelName(691, paramUser.getLanguage());
/*     */     }
/* 284 */     else if (paramString2.equals("5")) {
/* 285 */       str = str + SystemEnv.getHtmlLabelName(690, paramUser.getLanguage());
/* 286 */       RecordSet recordSet = new RecordSet();
/* 287 */       recordSet.execute("select seltablename , selcolumname , selkeycolumname from CRM_CustomerDefinField where id = '" + paramString8 + "'  and seltablename is not null and selcolumname is not null and selkeycolumname is not null");
/*     */       
/* 289 */       if (checkField(paramString8))
/* 290 */         str = str + "<input type=\"button\" value=\"" + SystemEnv.getHtmlLabelName(32714, paramUser.getLanguage()) + "\" class=\"e8_btn_top\" style=\"margin-left:6px;\" onclick=\"javascript:changeSelectItemInfo('" + paramString8 + "');\">"; 
/* 291 */       str = str + "&nbsp;<select class='InputStyle' style='max-width:150px' name='selectOption_" + paramString1 + "'>";
/* 292 */       if (recordSet.next()) {
/* 293 */         recordSet.execute("select " + recordSet.getString("selcolumname") + " from " + recordSet
/* 294 */             .getString("seltablename") + " order by " + recordSet.getString("selkeycolumname") + " asc ");
/* 295 */         while (recordSet.next()) {
/* 296 */           str = str + "<option>" + recordSet.getString(1) + "</option>";
/*     */         }
/*     */       } else {
/*     */         
/* 300 */         recordSet.execute("select selectname from crm_selectitem where fieldid =  " + paramString8 + " and isdel = 0 order by fieldorder asc");
/*     */         
/* 302 */         while (recordSet.next()) {
/* 303 */           str = str + "<option>" + recordSet.getString("selectname") + "</option>";
/*     */         }
/*     */       } 
/* 306 */       str = str + "&nbsp;";
/*     */     }
/* 308 */     else if (paramString2.equals("6")) {
/* 309 */       str = str + "<input type='hidden' name='uploadtype_" + paramString1 + "' value='" + paramString3 + "'/>";
/* 310 */       str = str + SystemEnv.getHtmlLabelName(17616, paramUser.getLanguage());
/*     */     } 
/*     */     
/* 313 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getTableGroupInfo(String paramString1, String paramString2, String paramString3, User paramUser, boolean paramBoolean) {
/* 508 */     RecordSet recordSet = new RecordSet();
/* 509 */     if (!paramBoolean) {
/* 510 */       recordSet.execute("select * from CRM_CustomerDefinFieldGroup where id = 4");
/* 511 */       recordSet.next();
/* 512 */       return SystemEnv.getHtmlLabelName(recordSet.getInt("grouplabel"), paramUser.getLanguage());
/*     */     } 
/* 514 */     if (paramString1.equals("CRM_CustomerInfo") && paramString3.equals("4")) {
/* 515 */       recordSet.execute("select * from CRM_CustomerDefinFieldGroup where id = 4");
/* 516 */       recordSet.next();
/* 517 */       return "<input type='hidden' name='groupid_" + paramString2 + "' value=" + paramString3 + ">" + SystemEnv.getHtmlLabelName(recordSet.getInt("grouplabel"), paramUser.getLanguage());
/*     */     } 
/*     */     
/* 520 */     recordSet.execute("select * from CRM_CustomerDefinFieldGroup where usetable = '" + paramString1 + "' and id != 4 order by dsporder asc");
/* 521 */     StringBuffer stringBuffer = new StringBuffer();
/* 522 */     stringBuffer.append("<select name='groupid_" + paramString2 + "' onchange='setChange(" + paramString2 + ")' style='width:100px;'>");
/* 523 */     while (recordSet.next()) {
/* 524 */       String str = recordSet.getString("id").equals(paramString3 + "") ? "selected='selected'" : "";
/* 525 */       stringBuffer.append("<option " + str + " value='" + recordSet.getString("id") + "'>" + SystemEnv.getHtmlLabelName(recordSet.getInt("grouplabel"), paramUser.getLanguage()) + "</option>");
/*     */     } 
/* 527 */     stringBuffer.append("</select>");
/* 528 */     return stringBuffer.toString();
/*     */   }
/*     */ 
/*     */   
/*     */   public String getName(String paramString) {
/* 533 */     RecordSet recordSet = new RecordSet();
/* 534 */     if (paramString != null && !"".equals(paramString)) {
/*     */       
/* 536 */       int i = paramString.indexOf(".");
/* 537 */       if (i > 0) {
/* 538 */         paramString = paramString.substring(i + 1);
/*     */       }
/* 540 */       String str = "select name from datashowset where showname='" + paramString + "'";
/* 541 */       recordSet.executeSql(str);
/* 542 */       if (recordSet.next()) {
/* 543 */         return Util.null2String(recordSet.getString("name"));
/*     */       }
/*     */     } 
/* 546 */     return "";
/*     */   }
/*     */   
/*     */   public boolean checkField(String paramString) {
/* 550 */     RecordSet recordSet = new RecordSet();
/* 551 */     String str1 = ",language,selltypesid,sufactor,defactor,sellstatusid,";
/* 552 */     recordSet.executeSql("select fieldname from CRM_CustomerDefinField where id=" + paramString);
/* 553 */     recordSet.first();
/* 554 */     String str2 = "," + recordSet.getString("fieldname") + ",";
/* 555 */     if (str1.contains(str2))
/* 556 */       return false; 
/* 557 */     return true;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/crm/Maint/CRMFreeFieldManage.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */