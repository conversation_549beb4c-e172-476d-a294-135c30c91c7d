/*    */ package weaver.crm.Maint;
/*    */ 
/*    */ import weaver.cache.CacheBase;
/*    */ import weaver.cache.CacheColumn;
/*    */ import weaver.cache.CacheColumnType;
/*    */ import weaver.cache.PKColumn;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class CustomerDescComInfo
/*    */   extends CacheBase
/*    */ {
/* 20 */   protected static String TABLE_NAME = "CRM_CustomerDesc";
/*    */   @PKColumn(type = CacheColumnType.NUMBER)
/* 22 */   protected static String PK_NAME = "id";
/*    */ 
/*    */   
/*    */   @CacheColumn
/*    */   protected static int id;
/*    */   
/*    */   @CacheColumn
/*    */   protected static int fullname;
/*    */   
/*    */   @CacheColumn
/*    */   protected static int description;
/*    */ 
/*    */   
/*    */   public int getCustomerDescNum() {
/* 36 */     return size();
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getCustomerDescid() {
/* 44 */     return (String)getRowValue(id);
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getCustomerDescname() {
/* 51 */     return (String)getRowValue(fullname);
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getCustomerDescname(String paramString) {
/* 60 */     return (String)getValue(fullname, paramString);
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getCustomerDescdesc() {
/* 67 */     return (String)getRowValue(description);
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getCustomerDescdesc(String paramString) {
/* 76 */     return (String)getValue(description, paramString);
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public void removeCustomerDescCache() {
/* 83 */     removeCache();
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/crm/Maint/CustomerDescComInfo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */