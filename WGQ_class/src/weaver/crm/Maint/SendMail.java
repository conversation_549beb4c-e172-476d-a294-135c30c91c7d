/*     */ package weaver.crm.Maint;
/*     */ 
/*     */ import java.util.Date;
/*     */ import java.util.Hashtable;
/*     */ import java.util.Properties;
/*     */ import javax.activation.DataHandler;
/*     */ import javax.mail.Address;
/*     */ import javax.mail.Message;
/*     */ import javax.mail.Session;
/*     */ import javax.mail.Transport;
/*     */ import javax.mail.internet.InternetAddress;
/*     */ import javax.mail.internet.MimeMessage;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.system.SystemComInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class SendMail
/*     */ {
/*     */   private String subject;
/*     */   private String from;
/*     */   private String selfComment;
/*     */   private int mailid;
/*     */   private int customerid;
/*     */   private String smtpserver;
/*     */   private int sendto;
/*     */   
/*     */   public SendMail() {
/*  41 */     resetParameter();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void resetParameter() {
/*  48 */     this.subject = "";
/*  49 */     this.from = "";
/*  50 */     this.selfComment = "";
/*  51 */     this.mailid = 0;
/*  52 */     this.customerid = 0;
/*  53 */     this.smtpserver = "";
/*  54 */     this.sendto = 0;
/*     */   }
/*     */   
/*     */   public void setMailId(int paramInt) {
/*  58 */     this.mailid = paramInt;
/*     */   }
/*     */   
/*     */   public void setCustomerId(int paramInt) {
/*  62 */     this.customerid = paramInt;
/*     */   }
/*     */   
/*     */   public void setSubject(String paramString) {
/*  66 */     this.subject = paramString;
/*     */   }
/*     */   public void setSelfComment(String paramString) {
/*  69 */     this.selfComment = paramString;
/*     */   }
/*     */   
/*     */   public void setFrom(String paramString) {
/*  73 */     this.from = paramString;
/*     */   }
/*     */   
/*     */   public void setSendTo(int paramInt) {
/*  77 */     this.sendto = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public synchronized void SendHtmlMail2(HttpServletRequest paramHttpServletRequest) throws Exception {
/*  85 */     SystemComInfo systemComInfo = new SystemComInfo();
/*  86 */     this.smtpserver = systemComInfo.getEmailserver();
/*     */     
/*  88 */     Properties properties = new Properties();
/*  89 */     properties.put("mail.smtp.from", this.from);
/*  90 */     properties.put("mail.smtp.host", this.smtpserver);
/*  91 */     properties.put("mail.transport.protocol", "smtp");
/*  92 */     Session session = Session.getInstance(properties, null);
/*     */     
/*  94 */     MimeMessage mimeMessage = new MimeMessage(session);
/*     */     
/*  96 */     String str1 = "";
/*     */     
/*  98 */     RecordSet recordSet1 = new RecordSet();
/*  99 */     RecordSet recordSet2 = new RecordSet();
/* 100 */     RecordSet recordSet3 = new RecordSet();
/*     */     
/* 102 */     recordSet1.executeProc("CRM_CustomerInfo_SelectByID", "" + this.customerid);
/* 103 */     recordSet1.next();
/*     */     
/* 105 */     String str2 = "";
/* 106 */     str2 = this.selfComment;
/*     */     
/* 108 */     int i = 0;
/* 109 */     recordSet3.executeProc("CRM_Find_CustomerContacter", "" + this.customerid);
/* 110 */     if (recordSet3.next()) {
/* 111 */       i = recordSet3.getInt(1);
/*     */     }
/* 113 */     recordSet2.executeProc("CRM_CustomerContacter_SByID", "" + i);
/* 114 */     recordSet2.next();
/*     */     
/* 116 */     String str3 = this.subject;
/* 117 */     str3 = Util.fromScreen2(str3, 7);
/*     */     try {
/* 119 */       if (this.sendto == 1) {
/* 120 */         str1 = Util.null2String(recordSet1.getString("email"));
/*     */         
/* 122 */         if (!str1.equals("")) {
/* 123 */           mimeMessage.setRecipients(Message.RecipientType.TO, (Address[])InternetAddress.parse(str1, true));
/* 124 */           mimeMessage.setDataHandler(new DataHandler(new ByteArrayDataSource(str2, "text/html; charset=UTF-8")));
/* 125 */           mimeMessage.setSubject(str3);
/* 126 */           mimeMessage.setSentDate(new Date());
/* 127 */           Transport.send((Message)mimeMessage);
/*     */         }
/*     */       
/* 130 */       } else if (this.sendto == 2) {
/* 131 */         str1 = Util.null2String(recordSet1.getString("email"));
/* 132 */         if (!str1.equals("")) {
/* 133 */           mimeMessage.setRecipients(Message.RecipientType.TO, (Address[])InternetAddress.parse(str1, true));
/* 134 */           mimeMessage.setDataHandler(new DataHandler(new ByteArrayDataSource(str2, "text/html; charset=UTF-8")));
/* 135 */           mimeMessage.setSubject(str3);
/* 136 */           mimeMessage.setSentDate(new Date());
/* 137 */           Transport.send((Message)mimeMessage);
/*     */         } 
/*     */         
/* 140 */         str1 = Util.null2String(recordSet2.getString("email"));
/* 141 */         if (!str1.equals("")) {
/* 142 */           mimeMessage.setRecipients(Message.RecipientType.TO, (Address[])InternetAddress.parse(str1, true));
/* 143 */           mimeMessage.setDataHandler(new DataHandler(new ByteArrayDataSource(str2, "text/html; charset=UTF-8")));
/* 144 */           mimeMessage.setSubject(str3);
/* 145 */           mimeMessage.setSentDate(new Date());
/* 146 */           Transport.send((Message)mimeMessage);
/*     */         }
/*     */       
/* 149 */       } else if (this.sendto == 3) {
/* 150 */         str1 = Util.null2String(recordSet1.getString("email"));
/* 151 */         mimeMessage.setRecipients(Message.RecipientType.TO, (Address[])InternetAddress.parse(str1, true));
/* 152 */         mimeMessage.setDataHandler(new DataHandler(new ByteArrayDataSource(str2, "text/html; charset=UTF-8")));
/* 153 */         mimeMessage.setSubject(str3);
/* 154 */         mimeMessage.setSentDate(new Date());
/* 155 */         Transport.send((Message)mimeMessage);
/*     */         
/* 157 */         if (recordSet2.next()) {
/* 158 */           str1 = Util.null2String(recordSet2.getString("email"));
/* 159 */           if (!str1.equals("")) {
/* 160 */             mimeMessage.setRecipients(Message.RecipientType.TO, (Address[])InternetAddress.parse(str1, true));
/* 161 */             mimeMessage.setDataHandler(new DataHandler(new ByteArrayDataSource(str2, "text/html; charset=UTF-8")));
/* 162 */             mimeMessage.setSubject(str3);
/* 163 */             mimeMessage.setSentDate(new Date());
/* 164 */             Transport.send((Message)mimeMessage);
/*     */           } 
/*     */         } 
/*     */       } 
/* 168 */     } catch (Exception exception) {}
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public synchronized void SendHtmlMail(HttpServletRequest paramHttpServletRequest) throws Exception {
/* 178 */     SystemComInfo systemComInfo = new SystemComInfo();
/* 179 */     this.smtpserver = systemComInfo.getEmailserver();
/*     */     
/* 181 */     Properties properties = new Properties();
/* 182 */     properties.put("mail.smtp.from", this.from);
/* 183 */     properties.put("mail.smtp.host", this.smtpserver);
/* 184 */     properties.put("mail.transport.protocol", "smtp");
/* 185 */     Session session = Session.getInstance(properties, null);
/*     */     
/* 187 */     MimeMessage mimeMessage = new MimeMessage(session);
/*     */     
/* 189 */     String str1 = "";
/* 190 */     Hashtable<Object, Object> hashtable = new Hashtable<Object, Object>();
/*     */     
/* 192 */     RecordSet recordSet1 = new RecordSet();
/* 193 */     RecordSet recordSet2 = new RecordSet();
/* 194 */     RecordSet recordSet3 = new RecordSet();
/*     */     
/* 196 */     recordSet1.executeProc("CRM_CustomerInfo_SelectByID", "" + this.customerid);
/* 197 */     recordSet1.next();
/*     */     
/* 199 */     String str2 = "select mouldtext from DocMailMould where id = " + this.mailid;
/*     */     
/* 201 */     String str3 = "";
/* 202 */     recordSet3.executeSql(str2);
/* 203 */     if (recordSet3.next()) {
/* 204 */       str3 = Util.toBaseEncoding(recordSet3.getString(1), 7, "1");
/*     */     }
/*     */     
/* 207 */     int i = str3.indexOf("<IMG alt=");
/* 208 */     while (i != -1) {
/* 209 */       i = str3.indexOf("?fileid=", i);
/* 210 */       int k = str3.indexOf("\"", i);
/* 211 */       String str5 = str3.substring(i + 8, k);
/* 212 */       int m = str3.lastIndexOf("\"", i);
/* 213 */       String str6 = paramHttpServletRequest.getServerName();
/* 214 */       String str7 = str3.substring(0, m + 1);
/* 215 */       str7 = str7 + "http://" + str6;
/* 216 */       str7 = str7 + str3.substring(m + 1);
/* 217 */       str3 = str7;
/* 218 */       i = str3.indexOf("<IMG alt=", k);
/*     */     } 
/*     */     
/* 221 */     int j = 0;
/* 222 */     recordSet3.executeProc("CRM_Find_CustomerContacter", "" + this.customerid);
/* 223 */     if (recordSet3.next()) {
/* 224 */       j = recordSet3.getInt(1);
/*     */     }
/* 226 */     recordSet2.executeProc("CRM_CustomerContacter_SByID", "" + j);
/* 227 */     recordSet2.next();
/*     */ 
/*     */     
/* 230 */     str2 = "select * from CRM_CustomizeOption";
/* 231 */     recordSet3.executeSql(str2);
/* 232 */     while (recordSet3.next()) {
/* 233 */       if (recordSet3.getString("tabledesc").equals("2")) {
/* 234 */         hashtable.put("Cont_" + recordSet3.getString("fieldname"), Util.null2String(recordSet2.getString(recordSet3.getString("fieldname")))); continue;
/*     */       } 
/* 236 */       hashtable.put("Cust_" + recordSet3.getString("fieldname"), Util.null2String(recordSet1.getString(recordSet3.getString("fieldname"))));
/*     */     } 
/*     */     
/* 239 */     String str4 = this.subject;
/* 240 */     str4 = Util.fillValuesToString(str4, hashtable);
/* 241 */     str4 = Util.fromScreen2(str4, 7);
/* 242 */     str3 = Util.fillValuesToString(str3, hashtable);
/*     */     
/*     */     try {
/* 245 */       if (this.sendto == 1) {
/* 246 */         str1 = Util.null2String(recordSet1.getString("email"));
/*     */         
/* 248 */         if (!str1.equals("")) {
/* 249 */           mimeMessage.setRecipients(Message.RecipientType.TO, (Address[])InternetAddress.parse(str1, true));
/* 250 */           mimeMessage.setDataHandler(new DataHandler(new ByteArrayDataSource(str3, "text/html; charset=UTF-8")));
/* 251 */           mimeMessage.setSubject(str4);
/* 252 */           mimeMessage.setSentDate(new Date());
/* 253 */           Transport.send((Message)mimeMessage);
/*     */         }
/*     */       
/* 256 */       } else if (this.sendto == 2) {
/* 257 */         str1 = Util.null2String(recordSet1.getString("email"));
/* 258 */         if (!str1.equals("")) {
/* 259 */           mimeMessage.setRecipients(Message.RecipientType.TO, (Address[])InternetAddress.parse(str1, true));
/* 260 */           mimeMessage.setDataHandler(new DataHandler(new ByteArrayDataSource(str3, "text/html; charset=UTF-8")));
/* 261 */           mimeMessage.setSubject(str4);
/* 262 */           mimeMessage.setSentDate(new Date());
/* 263 */           Transport.send((Message)mimeMessage);
/*     */         } 
/*     */         
/* 266 */         str1 = Util.null2String(recordSet2.getString("email"));
/* 267 */         if (!str1.equals("")) {
/* 268 */           mimeMessage.setRecipients(Message.RecipientType.TO, (Address[])InternetAddress.parse(str1, true));
/* 269 */           mimeMessage.setDataHandler(new DataHandler(new ByteArrayDataSource(str3, "text/html; charset=UTF-8")));
/* 270 */           mimeMessage.setSubject(str4);
/* 271 */           mimeMessage.setSentDate(new Date());
/* 272 */           Transport.send((Message)mimeMessage);
/*     */         }
/*     */       
/* 275 */       } else if (this.sendto == 3) {
/* 276 */         str1 = Util.null2String(recordSet1.getString("email"));
/* 277 */         mimeMessage.setRecipients(Message.RecipientType.TO, (Address[])InternetAddress.parse(str1, true));
/* 278 */         mimeMessage.setDataHandler(new DataHandler(new ByteArrayDataSource(str3, "text/html; charset=UTF-8")));
/* 279 */         mimeMessage.setSubject(str4);
/* 280 */         mimeMessage.setSentDate(new Date());
/* 281 */         Transport.send((Message)mimeMessage);
/*     */ 
/*     */         
/* 284 */         if (recordSet2.next()) {
/* 285 */           str1 = Util.null2String(recordSet2.getString("email"));
/* 286 */           if (!str1.equals("")) {
/* 287 */             mimeMessage.setRecipients(Message.RecipientType.TO, (Address[])InternetAddress.parse(str1, true));
/* 288 */             mimeMessage.setDataHandler(new DataHandler(new ByteArrayDataSource(str3, "text/html; charset=UTF-8")));
/* 289 */             mimeMessage.setSubject(str4);
/* 290 */             mimeMessage.setSentDate(new Date());
/* 291 */             Transport.send((Message)mimeMessage);
/*     */           } 
/*     */         } 
/*     */       } 
/* 295 */     } catch (Exception exception) {}
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public synchronized void SendSingleMail(HttpServletRequest paramHttpServletRequest, String paramString1, String paramString2, String paramString3, String paramString4) throws Exception {
/* 310 */     SystemComInfo systemComInfo = new SystemComInfo();
/* 311 */     this.smtpserver = systemComInfo.getEmailserver();
/*     */     
/* 313 */     Properties properties = new Properties();
/* 314 */     properties.put("mail.smtp.from", paramString1);
/* 315 */     properties.put("mail.smtp.host", this.smtpserver);
/* 316 */     properties.put("mail.transport.protocol", "smtp");
/* 317 */     Session session = Session.getInstance(properties, null);
/*     */     
/* 319 */     MimeMessage mimeMessage = new MimeMessage(session);
/*     */     
/* 321 */     String str1 = paramString4;
/* 322 */     String str2 = paramString3;
/*     */     try {
/* 324 */       mimeMessage.setRecipients(Message.RecipientType.TO, (Address[])InternetAddress.parse(paramString2, true));
/* 325 */       mimeMessage.setDataHandler(new DataHandler(new ByteArrayDataSource(str1, "text/html; charset=UTF-8")));
/* 326 */       mimeMessage.setSubject(str2);
/* 327 */       mimeMessage.setSentDate(new Date());
/* 328 */       Transport.send((Message)mimeMessage);
/* 329 */     } catch (Exception exception) {}
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public synchronized void SendSingleMail2(HttpServletRequest paramHttpServletRequest, String paramString1, String paramString2, String paramString3, String paramString4, String paramString5) throws Exception {
/* 344 */     SystemComInfo systemComInfo = new SystemComInfo();
/* 345 */     this.smtpserver = paramString5;
/*     */     
/* 347 */     Properties properties = new Properties();
/* 348 */     properties.put("mail.smtp.from", paramString1);
/* 349 */     properties.put("mail.smtp.host", this.smtpserver);
/* 350 */     properties.put("mail.transport.protocol", "smtp");
/* 351 */     Session session = Session.getInstance(properties, null);
/*     */     
/* 353 */     MimeMessage mimeMessage = new MimeMessage(session);
/*     */     
/* 355 */     String str1 = paramString4;
/* 356 */     String str2 = paramString3;
/*     */     try {
/* 358 */       mimeMessage.setRecipients(Message.RecipientType.TO, (Address[])InternetAddress.parse(paramString2, true));
/* 359 */       mimeMessage.setDataHandler(new DataHandler(new ByteArrayDataSource(str1, "text/html; charset=UTF-8")));
/* 360 */       mimeMessage.setSubject(str2);
/* 361 */       mimeMessage.setSentDate(new Date());
/* 362 */       Transport.send((Message)mimeMessage);
/* 363 */     } catch (Exception exception) {}
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/crm/Maint/SendMail.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */