/*      */ package weaver.crm.Maint;
/*      */ 
/*      */ import java.text.SimpleDateFormat;
/*      */ import java.util.ArrayList;
/*      */ import java.util.List;
/*      */ import weaver.conn.RecordSet;
/*      */ import weaver.crm.SptmForCrmModiRecord;
/*      */ import weaver.crm.customer.CustomerService;
/*      */ import weaver.crm.util.CrmUtil;
/*      */ import weaver.general.BaseBean;
/*      */ import weaver.general.GCONST;
/*      */ import weaver.general.TimeUtil;
/*      */ import weaver.general.Util;
/*      */ import weaver.hrm.HrmUserVarify;
/*      */ import weaver.hrm.User;
/*      */ import weaver.hrm.company.DepartmentComInfo;
/*      */ import weaver.hrm.company.SubCompanyComInfo;
/*      */ import weaver.hrm.job.JobTitlesComInfo;
/*      */ import weaver.hrm.resource.ResourceComInfo;
/*      */ import weaver.hrm.roles.RolesComInfo;
/*      */ import weaver.systeminfo.SystemEnv;
/*      */ import weaver.systeminfo.language.LanguageComInfo;
/*      */ import weaver.task.CommonTransUtil;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ public class CRMTransMethod
/*      */   extends BaseBean
/*      */ {
/*   31 */   private RecordSet rs = new RecordSet();
/*   32 */   private ResourceComInfo resourceinfo = null;
/*   33 */   private DepartmentComInfo departmentinfo = null;
/*   34 */   private RolesComInfo rolesinfo = null;
/*   35 */   private SubCompanyComInfo subCompanyComInfo = null;
/*      */   public CRMTransMethod() {
/*      */     try {
/*   38 */       this.resourceinfo = new ResourceComInfo();
/*   39 */       this.departmentinfo = new DepartmentComInfo();
/*   40 */       this.rolesinfo = new RolesComInfo();
/*   41 */       this.subCompanyComInfo = new SubCompanyComInfo();
/*   42 */     } catch (Exception exception) {
/*   43 */       writeLog(exception);
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getCRMContacterLinkWithTitle(String paramString1, String paramString2) {
/*   56 */     String[] arrayOfString = Util.TokenizerString2(paramString2, "+");
/*   57 */     String str1 = Util.null2String(arrayOfString[0]);
/*   58 */     String str2 = "";
/*   59 */     str2 = "<a href='javascript:void(0)' onclick='javaScript:openDialog(" + str1 + ")'>" + paramString1 + "</a>";
/*   60 */     return str2;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getCRMContacterTitleResultCheckbox(String paramString) {
/*   70 */     String str1 = "true";
/*   71 */     String[] arrayOfString = Util.TokenizerString2(paramString, "+");
/*   72 */     String str2 = arrayOfString[0];
/*   73 */     String str3 = arrayOfString[1];
/*   74 */     User user = getUser(Integer.valueOf(str3).intValue());
/*   75 */     if (HrmUserVarify.checkUserRight("EditContacterTitle:Delete", user)) {
/*      */ 
/*      */       
/*   78 */       this.rs.executeSql("Select title from CRM_CustomerContacter where title=" + str2);
/*   79 */       int i = this.rs.getCounts();
/*   80 */       if (i > 0)
/*   81 */         str1 = "false"; 
/*      */     } else {
/*   83 */       str1 = "false";
/*   84 */     }  return str1;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public List<String> getCRMSearchResultOperation(String paramString1, String paramString2) {
/*   94 */     String str1 = "true";
/*   95 */     String str2 = "true";
/*   96 */     String str3 = "true";
/*   97 */     ArrayList<String> arrayList = new ArrayList();
/*      */     
/*   99 */     User user = getUser(Integer.valueOf(paramString2).intValue());
/*  100 */     if (!HrmUserVarify.checkUserRight("EditContacterTitle:Edit", user))
/*  101 */       str1 = "false"; 
/*  102 */     if (HrmUserVarify.checkUserRight("EditContacterTitle:Delete", user)) {
/*      */ 
/*      */       
/*  105 */       this.rs.executeSql("Select title from CRM_CustomerContacter where title=" + paramString1);
/*  106 */       int i = this.rs.getCounts();
/*  107 */       if (i > 0)
/*  108 */         str2 = "false"; 
/*      */     } else {
/*  110 */       str2 = "false";
/*  111 */     }  if (!HrmUserVarify.checkUserRight("ContacterTitle:Log", user)) {
/*  112 */       str3 = "false";
/*      */     }
/*  114 */     arrayList.add(str1);
/*  115 */     arrayList.add(str2);
/*  116 */     arrayList.add(str3);
/*  117 */     return arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getCRMAddressTypeResultCheckbox(String paramString) {
/*  127 */     String str1 = "true";
/*  128 */     String[] arrayOfString = Util.TokenizerString2(paramString, "+");
/*  129 */     String str2 = arrayOfString[0];
/*  130 */     String str3 = arrayOfString[1];
/*  131 */     String str4 = arrayOfString[2];
/*  132 */     User user = getUser(Integer.valueOf(str4).intValue());
/*  133 */     if (!HrmUserVarify.checkUserRight("EditAddressType:Delete", user)) {
/*  134 */       str1 = "false";
/*      */     } else {
/*  136 */       if (str3.equals("n"))
/*  137 */         str1 = "false"; 
/*  138 */       RecordSet recordSet = new RecordSet();
/*  139 */       recordSet.execute("SELECT typeid FROM CRM_CustomerAddress WHERE typeid ='" + str2 + "'");
/*  140 */       if (recordSet.getCounts() > 0) {
/*  141 */         str1 = "false";
/*      */       }
/*      */     } 
/*  144 */     return str1;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public List<String> getCRMAddressTypeListOperation(String paramString1, String paramString2, String paramString3) {
/*  155 */     String str1 = "true";
/*  156 */     String str2 = "true";
/*  157 */     String str3 = "true";
/*  158 */     String[] arrayOfString = Util.TokenizerString2(paramString3, "+");
/*  159 */     String str4 = arrayOfString[0];
/*      */     
/*  161 */     ArrayList<String> arrayList = new ArrayList();
/*  162 */     User user = getUser(Integer.valueOf(paramString2).intValue());
/*  163 */     if (!HrmUserVarify.checkUserRight("EditAddressType:Edit", user))
/*  164 */       str1 = "false"; 
/*  165 */     if (!HrmUserVarify.checkUserRight("EditAddressType:Delete", user)) {
/*  166 */       str2 = "false";
/*      */     }
/*  168 */     else if (str4.equals("n")) {
/*  169 */       str2 = "false";
/*      */     } 
/*      */     
/*  172 */     if (!HrmUserVarify.checkUserRight("AddressType:Log", user)) {
/*  173 */       str3 = "false";
/*      */     }
/*  175 */     arrayList.add(str1);
/*  176 */     arrayList.add(str2);
/*  177 */     arrayList.add(str3);
/*  178 */     return arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getCRMContactWayResultCheckbox(String paramString) {
/*  188 */     String str1 = "true";
/*  189 */     String[] arrayOfString = Util.TokenizerString2(paramString, "+");
/*  190 */     String str2 = arrayOfString[0];
/*  191 */     String str3 = arrayOfString[1];
/*  192 */     User user = getUser(Integer.valueOf(str3).intValue());
/*  193 */     if (!HrmUserVarify.checkUserRight("EditContactWay:Delete", user)) {
/*  194 */       str1 = "false";
/*      */     }
/*  196 */     RecordSet recordSet = new RecordSet();
/*  197 */     recordSet.execute("SELECT id FROM CRM_CustomerInfo WHERE source ='" + str2 + "'");
/*  198 */     if (recordSet.getCounts() > 0) {
/*  199 */       str1 = "false";
/*      */     }
/*      */     
/*  202 */     return str1;
/*      */   }
/*      */   
/*      */   public String getCRMContactWayResultCheckboxs(String paramString) {
/*  206 */     String str1 = "true";
/*  207 */     String[] arrayOfString = Util.TokenizerString2(paramString, "+");
/*  208 */     String str2 = arrayOfString[0];
/*  209 */     String str3 = arrayOfString[1];
/*  210 */     User user = getUser(Integer.valueOf(str3).intValue());
/*  211 */     if (!HrmUserVarify.checkUserRight("ContactWay:Delete", user)) {
/*  212 */       str1 = "false";
/*      */     }
/*  214 */     if (str2.equals("3") || str2.equals("4")) {
/*  215 */       str1 = "false";
/*      */     }
/*  217 */     RecordSet recordSet = new RecordSet();
/*  218 */     recordSet.execute("SELECT * FROM workplan WHERE contactWay='" + str2 + "'");
/*  219 */     if (recordSet.getCounts() > 0) {
/*  220 */       str1 = "false";
/*      */     }
/*      */     
/*  223 */     return str1;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public List<String> getCRMContactWayListOperation(String paramString1, String paramString2) {
/*  233 */     String str1 = "true";
/*  234 */     String str2 = "true";
/*  235 */     String str3 = "true";
/*      */ 
/*      */     
/*  238 */     ArrayList<String> arrayList = new ArrayList();
/*  239 */     User user = getUser(Integer.valueOf(paramString2).intValue());
/*  240 */     if (!HrmUserVarify.checkUserRight("EditContactWay:Edit", user))
/*  241 */       str1 = "false"; 
/*  242 */     if (!HrmUserVarify.checkUserRight("EditContactWay:Delete", user)) {
/*  243 */       str2 = "false";
/*      */     }
/*      */ 
/*      */ 
/*      */     
/*  248 */     if (!HrmUserVarify.checkUserRight("ContactWay:Log", user)) {
/*  249 */       str3 = "false";
/*      */     }
/*  251 */     arrayList.add(str1);
/*  252 */     arrayList.add(str2);
/*  253 */     arrayList.add(str3);
/*  254 */     return arrayList;
/*      */   }
/*      */   
/*      */   public List<String> getCRMConnectWayListOperation(String paramString1, String paramString2) {
/*  258 */     String str1 = "true";
/*  259 */     String str2 = "true";
/*  260 */     String str3 = "true";
/*      */ 
/*      */     
/*  263 */     ArrayList<String> arrayList = new ArrayList();
/*  264 */     User user = getUser(Integer.valueOf(paramString2).intValue());
/*  265 */     if (!HrmUserVarify.checkUserRight("ContactWay:Edit", user))
/*  266 */       str1 = "false"; 
/*  267 */     if (!HrmUserVarify.checkUserRight("ContactWay:Delete", user)) {
/*  268 */       str2 = "false";
/*      */     }
/*      */ 
/*      */     
/*  272 */     RecordSet recordSet = new RecordSet();
/*  273 */     recordSet.execute("SELECT * FROM workplan WHERE contactWay='" + paramString1 + "'");
/*  274 */     if (recordSet.getCounts() > 0) {
/*  275 */       str2 = "false";
/*      */     }
/*  277 */     if (paramString1.equals("3") || paramString1.equals("4")) {
/*  278 */       str2 = "false";
/*      */     }
/*  280 */     if (!HrmUserVarify.checkUserRight("ContactWay:Log", user)) {
/*  281 */       str3 = "false";
/*      */     }
/*  283 */     arrayList.add(str1);
/*  284 */     arrayList.add(str2);
/*  285 */     arrayList.add(str3);
/*  286 */     return arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getCRMCustomerSizeResultCheckbox(String paramString) {
/*  295 */     String str1 = "true";
/*  296 */     String[] arrayOfString = Util.TokenizerString2(paramString, "+");
/*  297 */     String str2 = arrayOfString[0];
/*  298 */     String str3 = arrayOfString[1];
/*  299 */     User user = getUser(Integer.valueOf(str3).intValue());
/*  300 */     if (!HrmUserVarify.checkUserRight("EditCustomerSize:Delete", user)) {
/*  301 */       str1 = "false";
/*      */     }
/*  303 */     RecordSet recordSet = new RecordSet();
/*  304 */     recordSet.execute("SELECT id FROM CRM_CustomerInfo WHERE size_n ='" + str2 + "'");
/*  305 */     if (recordSet.getCounts() > 0) {
/*  306 */       str1 = "false";
/*      */     }
/*      */ 
/*      */     
/*  310 */     return str1;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public List<String> getCRMCustomerSizeListOperation(String paramString1, String paramString2) {
/*  320 */     String str1 = "true";
/*  321 */     String str2 = "true";
/*  322 */     String str3 = "true";
/*      */ 
/*      */     
/*  325 */     ArrayList<String> arrayList = new ArrayList();
/*  326 */     User user = getUser(Integer.valueOf(paramString2).intValue());
/*  327 */     if (!HrmUserVarify.checkUserRight("EditCustomerSize:Edit", user))
/*  328 */       str1 = "false"; 
/*  329 */     if (!HrmUserVarify.checkUserRight("EditCustomerSize:Delete", user)) {
/*  330 */       str2 = "false";
/*      */     }
/*      */ 
/*      */ 
/*      */     
/*  335 */     if (!HrmUserVarify.checkUserRight("CustomerSize:Log", user)) {
/*  336 */       str3 = "false";
/*      */     }
/*  338 */     arrayList.add(str1);
/*  339 */     arrayList.add(str2);
/*  340 */     arrayList.add(str3);
/*  341 */     return arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getCRMCustomerTypeResultCheckbox(String paramString) {
/*  351 */     String str1 = "true";
/*  352 */     String[] arrayOfString = Util.TokenizerString2(paramString, "+");
/*  353 */     String str2 = arrayOfString[0];
/*  354 */     String str3 = arrayOfString[1];
/*  355 */     if ("n".equals(str3)) {
/*  356 */       str1 = "false";
/*  357 */       return str1;
/*      */     } 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  367 */     RecordSet recordSet = new RecordSet();
/*  368 */     recordSet.execute("SELECT id FROM CRM_CustomerInfo WHERE type ='" + str2 + "'");
/*  369 */     if (recordSet.getCounts() > 0) {
/*  370 */       str1 = "false";
/*      */     }
/*      */     
/*  373 */     return str1;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getCRMCustomerStatusResultCheckbox(String paramString) {
/*  383 */     String str1 = "true";
/*  384 */     String[] arrayOfString = Util.TokenizerString2(paramString, "+");
/*  385 */     String str2 = arrayOfString[0];
/*  386 */     if ("1".equals(str2)) {
/*  387 */       str1 = "false";
/*  388 */       return str1;
/*      */     } 
/*  390 */     RecordSet recordSet = new RecordSet();
/*  391 */     recordSet.execute("SELECT id FROM CRM_CustomerInfo WHERE status ='" + str2 + "'");
/*  392 */     if (recordSet.getCounts() > 0) {
/*  393 */       str1 = "false";
/*      */     }
/*      */     
/*  396 */     return str1;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getCRMCustomerTypeShareResultCheckbox(String paramString) {
/*  406 */     String str1 = "true";
/*  407 */     String[] arrayOfString = Util.TokenizerString2(paramString, "+");
/*  408 */     String str2 = arrayOfString[0];
/*  409 */     String str3 = arrayOfString[1];
/*  410 */     User user = getUser(Integer.valueOf(str3).intValue());
/*  411 */     if (!HrmUserVarify.checkUserRight("EditCustomerType:Edit", user))
/*  412 */       str1 = "false"; 
/*  413 */     return str1;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getCRMCustomShareforType(String paramString1, String paramString2) {
/*  424 */     String str = "";
/*  425 */     User user = getUser(Integer.valueOf(paramString2).intValue());
/*  426 */     if (paramString1.equals("1")) {
/*  427 */       str = SystemEnv.getHtmlLabelName(179, user.getLanguage());
/*  428 */     } else if (paramString1.equals("2")) {
/*  429 */       str = SystemEnv.getHtmlLabelName(124, user.getLanguage());
/*  430 */     } else if (paramString1.equals("3")) {
/*  431 */       str = SystemEnv.getHtmlLabelName(122, user.getLanguage());
/*  432 */     } else if (paramString1.equals("5")) {
/*  433 */       str = SystemEnv.getHtmlLabelName(141, user.getLanguage());
/*  434 */     } else if (paramString1.equals("6")) {
/*  435 */       str = SystemEnv.getHtmlLabelName(6086, user.getLanguage());
/*      */     } else {
/*      */       
/*  438 */       str = SystemEnv.getHtmlLabelName(1340, user.getLanguage());
/*  439 */     }  return str;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getCRMCustomShareforObject(String paramString1, String paramString2) throws Exception {
/*  450 */     String str1 = "";
/*  451 */     String[] arrayOfString = Util.TokenizerString2(paramString2, "+");
/*  452 */     String str2 = arrayOfString[0];
/*  453 */     String str3 = arrayOfString[1];
/*  454 */     String str4 = arrayOfString[2];
/*  455 */     String str5 = arrayOfString[3];
/*  456 */     String str6 = arrayOfString[4];
/*  457 */     String str7 = arrayOfString[5];
/*  458 */     String str8 = arrayOfString[6];
/*  459 */     String str9 = arrayOfString[7];
/*  460 */     if (str2.equals("1")) {
/*  461 */       str1 = this.resourceinfo.getResourcename(str3);
/*  462 */     } else if (str2.equals("2")) {
/*  463 */       str1 = this.departmentinfo.getDepartmentname(str4) + "/" + this.subCompanyComInfo.getSubCompanyname(this.departmentinfo.getSubcompanyid1(str4));
/*  464 */     } else if (str2.equals("3")) {
/*  465 */       str1 = this.rolesinfo.getRolesRemark(str5);
/*  466 */     } else if (str2.equals("4")) {
/*  467 */       str1 = "";
/*  468 */     } else if (str2.equals("5")) {
/*  469 */       str1 = this.subCompanyComInfo.getSubCompanyname(str6);
/*  470 */     } else if (str2.equals("6")) {
/*  471 */       JobTitlesComInfo jobTitlesComInfo = new JobTitlesComInfo();
/*  472 */       DepartmentComInfo departmentComInfo = new DepartmentComInfo();
/*  473 */       SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/*  474 */       str1 = jobTitlesComInfo.getJobTitlesmark("" + str7);
/*  475 */       if ("0".equals(str8)) {
/*  476 */         str1 = str1 + "/" + SystemEnv.getHtmlLabelName(140, 7);
/*  477 */       } else if ("1".equals(str8)) {
/*  478 */         str1 = str1 + "/" + SystemEnv.getHtmlLabelName(19438, 7);
/*  479 */         str1 = str1 + "(" + departmentComInfo.getDepartmentNames(str9) + ")";
/*  480 */       } else if ("2".equals(str8)) {
/*  481 */         str1 = str1 + "/" + SystemEnv.getHtmlLabelName(19437, 7);
/*  482 */         str1 = str1 + "(" + subCompanyComInfo.getSubcompanynames(str9) + ")";
/*      */       } 
/*      */     } else {
/*      */       
/*  486 */       str1 = "";
/*      */     } 
/*  488 */     return str1;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getCRMCustomShareforShare(String paramString1, String paramString2) throws Exception {
/*  499 */     String str = "";
/*  500 */     User user = getUser(Integer.valueOf(paramString2).intValue());
/*  501 */     if (paramString1.equals("1")) {
/*  502 */       str = SystemEnv.getHtmlLabelName(367, user.getLanguage());
/*      */     } else {
/*  504 */       str = SystemEnv.getHtmlLabelName(93, user.getLanguage());
/*  505 */     }  return str;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getCRMCustomShareforSafe(String paramString1, String paramString2) throws Exception {
/*  516 */     String[] arrayOfString = Util.TokenizerString2(paramString2, "+");
/*  517 */     String str1 = arrayOfString[0];
/*  518 */     if (str1.equals("6")) {
/*  519 */       return "";
/*      */     }
/*  521 */     String str2 = arrayOfString[1];
/*  522 */     String str3 = arrayOfString[2];
/*  523 */     String str4 = arrayOfString[3];
/*      */ 
/*      */     
/*  526 */     paramString1 = paramString1 + " - " + (str3.equals("") ? "100" : str3);
/*  527 */     if (str1.equals("1"))
/*  528 */       return ""; 
/*  529 */     if (str1.equals("2"))
/*  530 */       return paramString1; 
/*  531 */     if (str1.equals("5"))
/*  532 */       return paramString1; 
/*  533 */     if (str1.equals("3")) {
/*  534 */       if (str2.equals("0")) {
/*  535 */         return SystemEnv.getHtmlLabelName(124, Util.getIntValue(str4)) + "/" + paramString1;
/*      */       }
/*  537 */       if (str2.equals("1")) {
/*  538 */         return SystemEnv.getHtmlLabelName(141, Util.getIntValue(str4)) + "/" + paramString1;
/*      */       }
/*  540 */       if (str2.equals("2")) {
/*  541 */         return SystemEnv.getHtmlLabelName(140, Util.getIntValue(str4)) + "/" + paramString1;
/*      */       }
/*  543 */       return paramString1;
/*      */     } 
/*  545 */     return paramString1;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public List<String> getCRMCustomerTypeListOperation(String paramString1, String paramString2, String paramString3) {
/*  557 */     String str1 = "true";
/*  558 */     String str2 = "true";
/*  559 */     String str3 = "true";
/*  560 */     String str4 = "true";
/*      */     
/*  562 */     String[] arrayOfString = Util.TokenizerString2(paramString3, "+");
/*  563 */     String str5 = arrayOfString[0];
/*  564 */     String str6 = arrayOfString[1];
/*  565 */     ArrayList<String> arrayList = new ArrayList();
/*  566 */     User user = getUser(Integer.valueOf(paramString2).intValue());
/*  567 */     if (!HrmUserVarify.checkUserRight("EditCustomerType:Edit", user)) {
/*      */       
/*  569 */       str1 = "false";
/*  570 */       str2 = "false";
/*      */     } 
/*  572 */     if (!HrmUserVarify.checkUserRight("EditCustomerType:Delete", user))
/*      */     {
/*  574 */       str3 = "false";
/*      */     }
/*  576 */     if (!HrmUserVarify.checkUserRight("AddCustomerType:add", user))
/*      */     {
/*  578 */       str4 = "false";
/*      */     }
/*  580 */     if ("n".equals(str5))
/*  581 */       str3 = "false"; 
/*  582 */     if ("n".equals(str6)) {
/*      */       
/*  584 */       str1 = "true";
/*  585 */       str2 = "true";
/*      */     } 
/*  587 */     arrayList.add(str1);
/*  588 */     arrayList.add(str2);
/*  589 */     arrayList.add(str3);
/*  590 */     arrayList.add(str4);
/*  591 */     return arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getCRMCustomerDescResultCheckbox(String paramString) {
/*  601 */     String str1 = "true";
/*  602 */     String[] arrayOfString = Util.TokenizerString2(paramString, "+");
/*  603 */     String str2 = arrayOfString[0];
/*  604 */     String str3 = arrayOfString[1];
/*  605 */     User user = getUser(Integer.valueOf(str3).intValue());
/*  606 */     if (!HrmUserVarify.checkUserRight("EditCustomerDesc:Delete", user)) {
/*  607 */       str1 = "false";
/*      */     }
/*  609 */     RecordSet recordSet = new RecordSet();
/*  610 */     recordSet.execute("SELECT id FROM CRM_CustomerInfo WHERE description ='" + str2 + "'");
/*  611 */     if (recordSet.getCounts() > 0) {
/*  612 */       str1 = "false";
/*      */     }
/*      */     
/*  615 */     return str1;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public List<String> getCRMCustomerDescListOperation(String paramString1, String paramString2) {
/*  626 */     String str1 = "true";
/*  627 */     String str2 = "true";
/*  628 */     String str3 = "true";
/*      */ 
/*      */     
/*  631 */     ArrayList<String> arrayList = new ArrayList();
/*  632 */     User user = getUser(Integer.valueOf(paramString2).intValue());
/*  633 */     if (!HrmUserVarify.checkUserRight("EditCustomerDesc:Edit", user))
/*  634 */       str1 = "false"; 
/*  635 */     if (!HrmUserVarify.checkUserRight("EditCustomerDesc:Delete", user)) {
/*  636 */       str2 = "false";
/*      */     }
/*      */ 
/*      */ 
/*      */     
/*  641 */     if (!HrmUserVarify.checkUserRight("CustomerDesc:Log", user)) {
/*  642 */       str3 = "false";
/*      */     }
/*  644 */     arrayList.add(str1);
/*  645 */     arrayList.add(str2);
/*  646 */     arrayList.add(str3);
/*  647 */     return arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public List<String> getCRMCustomerStatusListOperation(String paramString1, String paramString2) {
/*  660 */     String str1 = "true";
/*  661 */     String str2 = "true";
/*  662 */     String str3 = "true";
/*      */ 
/*      */     
/*  665 */     ArrayList<String> arrayList = new ArrayList();
/*  666 */     User user = getUser(Integer.valueOf(paramString2).intValue());
/*  667 */     if (!HrmUserVarify.checkUserRight("EditCustomerStatus:Edit", user)) {
/*      */       
/*  669 */       str1 = "false";
/*  670 */       str2 = "false";
/*      */     } 
/*      */     
/*  673 */     if (!HrmUserVarify.checkUserRight("CustomerStatus:Log", user)) {
/*  674 */       str3 = "false";
/*      */     }
/*  676 */     arrayList.add(str1);
/*  677 */     arrayList.add(str2);
/*  678 */     arrayList.add(str3);
/*  679 */     return arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getCRMEvaluationLevelResultCheckbox(String paramString) {
/*  689 */     String str1 = "true";
/*  690 */     String[] arrayOfString = Util.TokenizerString2(paramString, "+");
/*  691 */     String str2 = arrayOfString[0];
/*  692 */     String str3 = arrayOfString[1];
/*  693 */     User user = getUser(Integer.valueOf(str3).intValue());
/*  694 */     if (!HrmUserVarify.checkUserRight("CRM_EvaluationDelete:Delete", user)) {
/*  695 */       str1 = "false";
/*      */     }
/*  697 */     RecordSet recordSet = new RecordSet();
/*  698 */     recordSet.executeQuery("select 1 from CRM_Evaluation_LevelDetail where levelid=?", new Object[] { str2 });
/*  699 */     if (recordSet.getCounts() > 0) {
/*  700 */       str1 = "false";
/*      */     }
/*      */     
/*  703 */     return str1;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public List<String> getCRMEvaluationLevelListOperation(String paramString1, String paramString2) {
/*  714 */     String str1 = "true";
/*  715 */     String str2 = "true";
/*      */ 
/*      */     
/*  718 */     ArrayList<String> arrayList = new ArrayList();
/*  719 */     User user = getUser(Integer.valueOf(paramString2).intValue());
/*  720 */     if (!HrmUserVarify.checkUserRight("CRM_EvaluationEdit:Edit", user))
/*  721 */       str1 = "false"; 
/*  722 */     if (!HrmUserVarify.checkUserRight("CRM_EvaluationDelete:Delete", user)) {
/*  723 */       str2 = "false";
/*      */     }
/*  725 */     arrayList.add(str1);
/*  726 */     arrayList.add(str2);
/*  727 */     return arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getCRMEvaluationLevelResultCheckbox_l(String paramString) {
/*  736 */     String str1 = "true";
/*  737 */     String[] arrayOfString = Util.TokenizerString2(paramString, "+");
/*  738 */     String str2 = arrayOfString[0];
/*  739 */     String str3 = arrayOfString[1];
/*  740 */     User user = getUser(Integer.valueOf(str3).intValue());
/*  741 */     if (!HrmUserVarify.checkUserRight("CRM_EvaluationDelete:Delete", user))
/*  742 */       str1 = "false"; 
/*  743 */     return str1;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getCRMEvaluationWithProportion(String paramString) {
/*  752 */     return paramString + "%";
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getCRMStatusResultCheckbox(String paramString) {
/*  763 */     String str1 = "true";
/*  764 */     String[] arrayOfString = Util.TokenizerString2(paramString, "+");
/*  765 */     String str2 = arrayOfString[0];
/*  766 */     String str3 = arrayOfString[1];
/*  767 */     User user = getUser(Integer.valueOf(str3).intValue());
/*  768 */     if (!HrmUserVarify.checkUserRight("CrmSalesChance:Maintenance", user)) {
/*  769 */       str1 = "false";
/*      */     }
/*  771 */     RecordSet recordSet = new RecordSet();
/*  772 */     recordSet.execute("SELECT id FROM CRM_SellChance WHERE sellstatusid ='" + str2 + "'");
/*  773 */     if (recordSet.getCounts() > 0) {
/*  774 */       str1 = "false";
/*      */     }
/*      */     
/*  777 */     return str1;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public List<String> getCRMStatusListOperation(String paramString1, String paramString2) {
/*  787 */     String str1 = "true";
/*  788 */     String str2 = "true";
/*      */ 
/*      */     
/*  791 */     ArrayList<String> arrayList = new ArrayList();
/*  792 */     User user = getUser(Integer.valueOf(paramString2).intValue());
/*  793 */     if (!HrmUserVarify.checkUserRight("CrmSalesChance:Maintenance", user)) {
/*      */       
/*  795 */       str1 = "false";
/*  796 */       str2 = "false";
/*      */     } 
/*      */     
/*  799 */     RecordSet recordSet = new RecordSet();
/*  800 */     recordSet.executeQuery("SELECT * from CRM_SellChance where selltypesid ='" + paramString1 + "'", new Object[0]);
/*  801 */     if (recordSet.first()) {
/*  802 */       str2 = "false";
/*      */     }
/*  804 */     arrayList.add(str1);
/*  805 */     arrayList.add(str2);
/*  806 */     return arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getCRMSuccessfactortCheckbox(String paramString) {
/*  815 */     String str1 = "true";
/*  816 */     String[] arrayOfString = Util.TokenizerString2(paramString, "+");
/*  817 */     String str2 = arrayOfString[0];
/*  818 */     String str3 = arrayOfString[1];
/*  819 */     User user = getUser(Integer.valueOf(str3).intValue());
/*  820 */     if (!HrmUserVarify.checkUserRight("CrmSalesChance:Maintenance", user)) {
/*  821 */       str1 = "false";
/*      */     }
/*  823 */     RecordSet recordSet = new RecordSet();
/*  824 */     recordSet.execute("SELECT id FROM CRM_SellChance WHERE sufactor ='" + str2 + "'");
/*  825 */     if (recordSet.getCounts() > 0) {
/*  826 */       str1 = "false";
/*      */     }
/*      */     
/*  829 */     return str1;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getCRMFailfactortCheckbox(String paramString) {
/*  838 */     String str1 = "true";
/*  839 */     String[] arrayOfString = Util.TokenizerString2(paramString, "+");
/*  840 */     String str2 = arrayOfString[0];
/*  841 */     String str3 = arrayOfString[1];
/*  842 */     User user = getUser(Integer.valueOf(str3).intValue());
/*  843 */     if (!HrmUserVarify.checkUserRight("CrmSalesChance:Maintenance", user)) {
/*  844 */       str1 = "false";
/*      */     }
/*  846 */     RecordSet recordSet = new RecordSet();
/*  847 */     recordSet.execute("SELECT id FROM CRM_SellChance WHERE defactor ='" + str2 + "'");
/*  848 */     if (recordSet.getCounts() > 0) {
/*  849 */       str1 = "false";
/*      */     }
/*      */     
/*  852 */     return str1;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getCRMTypesCheckbox(String paramString) {
/*  861 */     String str1 = "true";
/*  862 */     String[] arrayOfString = Util.TokenizerString2(paramString, "+");
/*  863 */     String str2 = arrayOfString[0];
/*  864 */     String str3 = arrayOfString[1];
/*  865 */     User user = getUser(Integer.valueOf(str3).intValue());
/*  866 */     if (!HrmUserVarify.checkUserRight("CrmSalesChance:Maintenance", user)) {
/*  867 */       str1 = "false";
/*      */     }
/*  869 */     RecordSet recordSet = new RecordSet();
/*  870 */     recordSet.execute("SELECT id FROM CRM_SellChance WHERE selltypesid = '" + str2 + "'");
/*  871 */     if (recordSet.getCounts() > 0) {
/*  872 */       str1 = "false";
/*      */     }
/*      */     
/*  875 */     return str1;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getCRMContractTypeResultCheckbox(String paramString) {
/*  884 */     String str1 = "true";
/*  885 */     String[] arrayOfString = Util.TokenizerString2(paramString, "+");
/*  886 */     String str2 = arrayOfString[0];
/*  887 */     String str3 = arrayOfString[1];
/*  888 */     User user = getUser(Integer.valueOf(str3).intValue());
/*  889 */     if (!HrmUserVarify.checkUserRight("CRM_ContractTypeAdd:Add", user)) {
/*  890 */       str1 = "false";
/*      */     }
/*  892 */     RecordSet recordSet = new RecordSet();
/*  893 */     recordSet.execute("SELECT id FROM CRM_Contract WHERE typeId ='" + str2 + "'");
/*  894 */     if (recordSet.getCounts() > 0) {
/*  895 */       str1 = "false";
/*      */     }
/*      */     
/*  898 */     return str1;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public List<String> getContractTypeListOperation(String paramString1, String paramString2) {
/*  909 */     String str1 = "true";
/*  910 */     String str2 = "true";
/*      */ 
/*      */     
/*  913 */     ArrayList<String> arrayList = new ArrayList();
/*  914 */     User user = getUser(Integer.valueOf(paramString2).intValue());
/*  915 */     if (!HrmUserVarify.checkUserRight("CRM_ContractTypeAdd:Add", user)) {
/*      */       
/*  917 */       str1 = "false";
/*  918 */       str2 = "false";
/*      */     } 
/*  920 */     arrayList.add(str1);
/*  921 */     arrayList.add(str2);
/*  922 */     return arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getCreditHighAmount(String paramString1, String paramString2) {
/*  933 */     String str1 = "";
/*  934 */     String str2 = "";
/*  935 */     String str3 = "";
/*  936 */     if (Util.getDoubleValue(Util.null2String(paramString1)) > -1.0D)
/*  937 */       str2 = paramString1; 
/*  938 */     if (Util.getDoubleValue(Util.null2String(paramString2)) > -1.0D)
/*  939 */       str3 = paramString2; 
/*  940 */     str1 = str2 + "--" + str3;
/*  941 */     return str1;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getTradeHighAmount(String paramString1, String paramString2) {
/*  951 */     String str1 = "";
/*  952 */     String str2 = "";
/*  953 */     String str3 = "";
/*  954 */     if (Util.getDoubleValue(Util.null2String(paramString1)) > -1.0D)
/*  955 */       str2 = paramString1; 
/*  956 */     if (Util.getDoubleValue(Util.null2String(paramString2)) > -1.0D)
/*  957 */       str3 = paramString2; 
/*  958 */     str1 = str2 + "--" + str3;
/*      */     
/*  960 */     return str1;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getCRMCreditInfoResultCheckbox(String paramString) {
/*  970 */     String str1 = "true";
/*  971 */     String[] arrayOfString = Util.TokenizerString2(paramString, "+");
/*  972 */     String str2 = arrayOfString[0];
/*  973 */     String str3 = arrayOfString[1];
/*  974 */     User user = getUser(Integer.valueOf(str3).intValue());
/*  975 */     if (!HrmUserVarify.checkUserRight("EditCreditInfo:Delete", user)) {
/*  976 */       str1 = "false";
/*      */     }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  983 */     return str1;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public List<String> getCreditInfoListOperation(String paramString1, String paramString2) {
/*  994 */     String str1 = "true";
/*  995 */     String str2 = "true";
/*      */ 
/*      */     
/*  998 */     ArrayList<String> arrayList = new ArrayList();
/*  999 */     User user = getUser(Integer.valueOf(paramString2).intValue());
/* 1000 */     if (!HrmUserVarify.checkUserRight("EditCreditInfo:Edit", user))
/* 1001 */       str1 = "false"; 
/* 1002 */     if (!HrmUserVarify.checkUserRight("EditCreditInfo:Delete", user)) {
/* 1003 */       str2 = "false";
/*      */     }
/* 1005 */     arrayList.add(str1);
/* 1006 */     arrayList.add(str2);
/* 1007 */     return arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getCRMTradeInfoResultCheckbox(String paramString) {
/* 1017 */     String str1 = "true";
/* 1018 */     String[] arrayOfString = Util.TokenizerString2(paramString, "+");
/* 1019 */     String str2 = arrayOfString[0];
/* 1020 */     String str3 = arrayOfString[1];
/* 1021 */     User user = getUser(Integer.valueOf(str3).intValue());
/* 1022 */     if (!HrmUserVarify.checkUserRight("EditTradeInfo:Delete", user)) {
/* 1023 */       str1 = "false";
/*      */     }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/* 1030 */     return str1;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public List<String> getTradeInfoListOperation(String paramString1, String paramString2) {
/* 1041 */     String str1 = "true";
/* 1042 */     String str2 = "true";
/*      */ 
/*      */     
/* 1045 */     ArrayList<String> arrayList = new ArrayList();
/* 1046 */     User user = getUser(Integer.valueOf(paramString2).intValue());
/* 1047 */     if (!HrmUserVarify.checkUserRight("EditTradeInfo:Edit", user))
/* 1048 */       str1 = "false"; 
/* 1049 */     if (!HrmUserVarify.checkUserRight("EditTradeInfo:Delete", user)) {
/* 1050 */       str2 = "false";
/*      */     }
/* 1052 */     arrayList.add(str1);
/* 1053 */     arrayList.add(str2);
/* 1054 */     return arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getCRMProductResultCheckbox(String paramString) {
/* 1064 */     String str1 = "true";
/* 1065 */     String[] arrayOfString = Util.TokenizerString2(paramString, "+");
/* 1066 */     String str2 = arrayOfString[0];
/* 1067 */     String str3 = arrayOfString[1];
/* 1068 */     User user = getUser(Integer.valueOf(str3).intValue());
/* 1069 */     if (!HrmUserVarify.checkUserRight("CrmProduct:Add", user))
/* 1070 */       str1 = "false"; 
/* 1071 */     return str1;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public List<String> getCRMProductListOperation(String paramString1, String paramString2) {
/* 1082 */     String str1 = "true";
/* 1083 */     String str2 = "true";
/* 1084 */     String str3 = "true";
/*      */     
/* 1086 */     ArrayList<String> arrayList = new ArrayList();
/* 1087 */     User user = getUser(Integer.valueOf(paramString2).intValue());
/* 1088 */     if (!HrmUserVarify.checkUserRight("CrmProduct:Add", user)) {
/*      */       
/* 1090 */       str3 = "false";
/* 1091 */       str2 = "false";
/* 1092 */       str1 = "false";
/*      */     } 
/*      */     
/* 1095 */     arrayList.add(str1);
/* 1096 */     arrayList.add(str2);
/* 1097 */     arrayList.add(str3);
/*      */     
/* 1099 */     return arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getLgcAssortmentResultCheckbox(String paramString) {
/* 1109 */     String str1 = "true";
/* 1110 */     String[] arrayOfString = Util.TokenizerString2(paramString, "+");
/* 1111 */     String str2 = arrayOfString[0];
/* 1112 */     String str3 = arrayOfString[1];
/* 1113 */     User user = getUser(Integer.valueOf(str3).intValue());
/* 1114 */     if (!HrmUserVarify.checkUserRight("CrmProduct:Add", user))
/* 1115 */       str1 = "false"; 
/* 1116 */     RecordSet recordSet1 = new RecordSet();
/* 1117 */     recordSet1.executeSql("select count(t1.id) as s from LgcAssetAssortment t1 where t1.assetcount>0 and id=" + str2);
/* 1118 */     recordSet1.first();
/* 1119 */     int i = recordSet1.getInt(1);
/* 1120 */     RecordSet recordSet2 = new RecordSet();
/* 1121 */     recordSet2.executeSql("select count(t1.id) as t from LgcAssetAssortment t1 where t1.supassortmentid=" + str2);
/* 1122 */     recordSet2.first();
/* 1123 */     int j = recordSet2.getInt(1);
/*      */     
/* 1125 */     if (i > 0 || j > 0)
/*      */     {
/* 1127 */       str1 = "false";
/*      */     }
/* 1129 */     return str1;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public List<String> getLgcAssortmentListOperation(String paramString1, String paramString2) {
/* 1140 */     String str1 = "true";
/* 1141 */     String str2 = "true";
/*      */     
/* 1143 */     ArrayList<String> arrayList = new ArrayList();
/* 1144 */     User user = getUser(Integer.valueOf(paramString2).intValue());
/* 1145 */     if (!HrmUserVarify.checkUserRight("CrmProduct:Add", user)) {
/*      */       
/* 1147 */       str2 = "false";
/* 1148 */       str1 = "false";
/*      */     } 
/* 1150 */     RecordSet recordSet1 = new RecordSet();
/* 1151 */     recordSet1.executeSql("select count(t1.id) as s from LgcAssetAssortment t1 where t1.assetcount>0 and id=" + paramString1);
/* 1152 */     recordSet1.first();
/* 1153 */     int i = recordSet1.getInt(1);
/* 1154 */     RecordSet recordSet2 = new RecordSet();
/* 1155 */     recordSet2.executeSql("select count(t1.id) as t from LgcAssetAssortment t1 where t1.supassortmentid=" + paramString1);
/* 1156 */     recordSet2.first();
/* 1157 */     int j = recordSet2.getInt(1);
/*      */     
/* 1159 */     if (i > 0 || j > 0)
/*      */     {
/* 1161 */       str2 = "false";
/*      */     }
/*      */     
/* 1164 */     arrayList.add(str1);
/* 1165 */     arrayList.add(str2);
/*      */     
/* 1167 */     return arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private User getUser(int paramInt) {
/* 1177 */     User user = new User();
/*      */     try {
/* 1179 */       ResourceComInfo resourceComInfo = new ResourceComInfo();
/* 1180 */       DepartmentComInfo departmentComInfo = new DepartmentComInfo();
/*      */       
/* 1182 */       user.setUid(paramInt);
/* 1183 */       user.setLoginid(resourceComInfo.getLoginID("" + paramInt));
/* 1184 */       user.setFirstname(resourceComInfo.getFirstname("" + paramInt));
/* 1185 */       user.setLastname(resourceComInfo.getLastname("" + paramInt));
/* 1186 */       user.setLogintype("1");
/*      */ 
/*      */ 
/*      */       
/* 1190 */       user.setSex(resourceComInfo.getSexs("" + paramInt));
/* 1191 */       user.setLanguage(7);
/*      */ 
/*      */ 
/*      */       
/* 1195 */       user.setEmail(resourceComInfo.getEmail("" + paramInt));
/*      */       
/* 1197 */       user.setLocationid(resourceComInfo.getLocationid("" + paramInt));
/* 1198 */       user.setResourcetype(resourceComInfo.getResourcetype("" + paramInt));
/*      */ 
/*      */ 
/*      */       
/* 1202 */       user.setJobtitle(resourceComInfo.getJobTitle("" + paramInt));
/*      */ 
/*      */       
/* 1205 */       user.setJoblevel(resourceComInfo.getJoblevel("" + paramInt));
/* 1206 */       user.setSeclevel(resourceComInfo.getSeclevel("" + paramInt));
/* 1207 */       user.setUserDepartment(Util.getIntValue(resourceComInfo.getDepartmentID("" + paramInt), 0));
/* 1208 */       user.setUserSubCompany1(Util.getIntValue(departmentComInfo.getSubcompanyid1(user.getUserDepartment() + ""), 0));
/*      */ 
/*      */ 
/*      */       
/* 1212 */       user.setManagerid(resourceComInfo.getManagerID("" + paramInt));
/* 1213 */       user.setAssistantid(resourceComInfo.getAssistantID("" + paramInt));
/*      */ 
/*      */     
/*      */     }
/* 1217 */     catch (Exception exception) {
/* 1218 */       exception.printStackTrace();
/*      */     } 
/* 1220 */     return user;
/*      */   }
/*      */   public String getMessageContent(String paramString1, String paramString2) {
/* 1223 */     return getMessageContent(paramString1, paramString2, "7");
/*      */   }
/*      */   public String getMessageContent(String paramString1, String paramString2, String paramString3) {
/* 1226 */     String str = "";
/*      */     try {
/* 1228 */       ResourceComInfo resourceComInfo = new ResourceComInfo();
/* 1229 */       CustomerInfoComInfo customerInfoComInfo = new CustomerInfoComInfo();
/* 1230 */       CommonTransUtil commonTransUtil = new CommonTransUtil();
/*      */       
/* 1232 */       ArrayList<String> arrayList = Util.TokenizerString(paramString2, "+");
/* 1233 */       String str1 = arrayList.get(0);
/* 1234 */       String str2 = arrayList.get(1);
/* 1235 */       String str3 = arrayList.get(2);
/* 1236 */       String str4 = arrayList.get(3);
/* 1237 */       String str5 = ((String)arrayList.get(4)).trim();
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/* 1248 */       str = "<div class='feedbackinfo' >" + ((Util.getIntValue(str1) > 0) ? ("<a href='" + GCONST.getContextPath() + "/hrm/resource/HrmResource.jsp?id='" + str1 + "' target='_blank'>" + resourceComInfo.getResourcename(str1) + "</a>") : ("<A href='" + GCONST.getContextPath() + "/CRM/data/ViewCustomer.jsp?CustomerID='" + str1.substring(1) + "'>" + customerInfoComInfo.getCustomerInfoname(str1.substring(1)) + "</a>")) + " " + str2 + " " + str3 + "</div><div class='feedbackrelate'><div>" + str4 + "</div>" + ((!str5.equals("") && !str5.equals("0")) ? ("<div class='relatetitle'>" + SystemEnv.getHtmlLabelName(857, Integer.parseInt(paramString3)) + "：" + commonTransUtil.getDocName(str5) + "</div>") : "") + "</div>";
/*      */     }
/*      */     catch (Exception exception) {
/*      */       
/* 1252 */       exception.printStackTrace();
/*      */     } 
/* 1254 */     return str;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getDateTime(String paramString1, String paramString2) {
/* 1265 */     return paramString1 + " " + paramString2;
/*      */   }
/*      */ 
/*      */   
/*      */   public String getLogSubmiter(String paramString1, String paramString2) {
/* 1270 */     String str = "";
/*      */     try {
/* 1272 */       ResourceComInfo resourceComInfo = new ResourceComInfo();
/* 1273 */       CustomerInfoComInfo customerInfoComInfo = new CustomerInfoComInfo();
/*      */       
/* 1275 */       ArrayList<String> arrayList = Util.TokenizerString(paramString2, "+");
/* 1276 */       String str1 = arrayList.get(0);
/* 1277 */       String str2 = arrayList.get(1);
/*      */       
/* 1279 */       if (!str2.equals("2")) {
/* 1280 */         if (!str1.equals("2")) {
/* 1281 */           str = "<a href='" + GCONST.getContextPath() + "/hrm/resource/HrmResource.jsp?id=" + paramString1 + "' target='_blank'>" + resourceComInfo.getLastname(paramString1) + "</a>";
/*      */         } else {
/* 1283 */           str = "<a href='" + GCONST.getContextPath() + "/CRM/data/ViewCustomer.jsp?CustomerID=" + paramString1 + "'>" + customerInfoComInfo.getCustomerInfoname(paramString1) + "</a>";
/*      */         } 
/* 1285 */       } else if (!str1.equals("2")) {
/* 1286 */         str = resourceComInfo.getLastname(paramString1);
/*      */       } else {
/* 1288 */         str = "<a href='" + GCONST.getContextPath() + "/CRM/data/ViewCustomer.jsp?CustomerID=" + paramString1 + "'>" + customerInfoComInfo.getCustomerInfoname(paramString1) + "</a>";
/*      */       } 
/* 1290 */     } catch (Exception exception) {}
/*      */     
/* 1292 */     return str;
/*      */   }
/*      */   public String getModifyType(String paramString1, String paramString2) {
/* 1295 */     return getModifyType(paramString1, paramString2, "7");
/*      */   }
/*      */   public String getModifyType(String paramString1, String paramString2, String paramString3) {
/*      */     try {
/* 1299 */       CustomerStatusComInfo customerStatusComInfo = new CustomerStatusComInfo();
/* 1300 */       LanguageComInfo languageComInfo = new LanguageComInfo();
/* 1301 */       SptmForCrmModiRecord sptmForCrmModiRecord = new SptmForCrmModiRecord();
/* 1302 */       String str = sptmForCrmModiRecord.getCrmModiInfo(paramString2, paramString1);
/*      */       
/* 1304 */       if (paramString2.equals(SystemEnv.getHtmlLabelName(23247, Integer.parseInt(paramString3)))) str = customerStatusComInfo.getCustomerStatusname(paramString1); 
/* 1305 */       if (paramString2.equals(SystemEnv.getHtmlLabelName(231, Integer.parseInt(paramString3)))) str = languageComInfo.getLanguagename(paramString1); 
/* 1306 */       return str;
/* 1307 */     } catch (Exception exception) {
/* 1308 */       writeLog(exception);
/*      */       
/* 1310 */       return null;
/*      */     } 
/*      */   } public String getLogType(String paramString1, String paramString2) {
/* 1313 */     return getLogType(paramString1, paramString2, "7");
/*      */   }
/*      */   public String getLogType(String paramString1, String paramString2, String paramString3) {
/* 1316 */     ArrayList<String> arrayList = Util.TokenizerString(paramString2, "+");
/* 1317 */     String str1 = arrayList.get(0);
/* 1318 */     String str2 = arrayList.get(1);
/* 1319 */     String str3 = arrayList.get(2);
/* 1320 */     String str4 = arrayList.get(3);
/* 1321 */     String str5 = arrayList.get(4);
/*      */     
/* 1323 */     RecordSet recordSet = new RecordSet();
/* 1324 */     String str6 = "";
/* 1325 */     String str7 = "";
/*      */ 
/*      */     
/*      */     try {
/* 1329 */       if (paramString1.startsWith("v")) {
/* 1330 */         str6 = "<font class='log_txt'>" + SystemEnv.getHtmlLabelName(501696, Integer.parseInt(paramString3));
/* 1331 */       } else if (paramString1.startsWith("r")) {
/* 1332 */         str6 = "<font class='log_txt'>" + SystemEnv.getHtmlLabelName(501697, Integer.parseInt(paramString3));
/* 1333 */       } else if (paramString1.startsWith("n")) {
/* 1334 */         str6 = "<font class='log_txt'>" + SystemEnv.getHtmlLabelName(365, Integer.parseInt(paramString3));
/* 1335 */       } else if (paramString1.startsWith("d")) {
/* 1336 */         str6 = "<font class='log_txt'>" + SystemEnv.getHtmlLabelName(91, Integer.parseInt(paramString3));
/* 1337 */       } else if (paramString1.startsWith("a")) {
/* 1338 */         str6 = "<font class='log_txt'>" + SystemEnv.getHtmlLabelName(23247, Integer.parseInt(paramString3));
/* 1339 */       } else if (paramString1.startsWith("p")) {
/* 1340 */         str6 = "<font class='log_txt'>" + SystemEnv.getHtmlLabelName(582, Integer.parseInt(paramString3));
/* 1341 */       } else if (paramString1.startsWith("m")) {
/* 1342 */         str6 = "<font class='log_txt'>" + SystemEnv.getHtmlLabelName(501169, Integer.parseInt(paramString3));
/* 1343 */       } else if (paramString1.startsWith("u")) {
/* 1344 */         str6 = "<font class='log_txt'>" + SystemEnv.getHtmlLabelName(216, Integer.parseInt(paramString3));
/* 1345 */         String str = str5;
/*      */         try {
/* 1347 */           str7 = SystemEnv.getHtmlLabelName(783, Integer.parseInt(paramString3)) + "(";
/* 1348 */           str = str.substring(str.indexOf(": ") + 1);
/* 1349 */           ArrayList<String> arrayList1 = Util.TokenizerString(str, ",");
/* 1350 */           for (byte b = 0; b < arrayList1.size(); b++) {
/* 1351 */             String str8 = "" + arrayList1.get(b);
/* 1352 */             if (!"1".equals(str8.trim())) {
/* 1353 */               if (b > 50)
/* 1354 */                 break;  recordSet.execute("select id from crm_customerinfo where deleted = '1' and name='" + str8.trim() + "' ");
/* 1355 */               while (recordSet.next()) {
/* 1356 */                 String str9 = recordSet.getString(1);
/* 1357 */                 str7 = str7 + "<a href='" + GCONST.getContextPath() + "/CRM/data/ViewCustomer.jsp?CustomerID=" + str9 + "' target='_news'>" + str8 + "</a> 　";
/*      */               } 
/*      */             } 
/* 1360 */           }  str7 = str7 + ")";
/* 1361 */         } catch (Exception exception) {}
/*      */       } else {
/* 1363 */         str6 = "<font class='log_txt'>";
/*      */       } 
/* 1365 */       if (paramString1.length() > 1) {
/* 1366 */         if (paramString1.substring(1).equals("c")) {
/* 1367 */           str6 = str6 + ": " + SystemEnv.getHtmlLabelName(572, Util.getIntValue(str2));
/* 1368 */         } else if (paramString1.substring(1).equals("a")) {
/* 1369 */           str6 = str6 + ": " + SystemEnv.getHtmlLabelName(110, Util.getIntValue(str2));
/* 1370 */         } else if (paramString1.substring(1).equals("s")) {
/* 1371 */           str6 = str6 + ": " + SystemEnv.getHtmlLabelName(119, Util.getIntValue(str2));
/*      */         } 
/*      */       }
/* 1374 */       str6 = str6 + "</font> ";
/* 1375 */       SptmForCrmModiRecord sptmForCrmModiRecord = new SptmForCrmModiRecord();
/* 1376 */       CustomerStatusComInfo customerStatusComInfo = new CustomerStatusComInfo();
/* 1377 */       LanguageComInfo languageComInfo = new LanguageComInfo();
/* 1378 */       recordSet.executeSql("select fieldname,original,modified from CRM_Modify where customerid = " + str1 + " and modifydate='" + str4 + "' and modifytime='" + str3 + "'  order by modifydate,modifytime desc");
/* 1379 */       while (recordSet.next()) {
/* 1380 */         String str8 = Util.null2String(recordSet.getString("fieldname"));
/* 1381 */         String str9 = recordSet.getString("original");
/* 1382 */         String str10 = recordSet.getString("modified");
/* 1383 */         String str11 = "";
/* 1384 */         String str12 = "";
/*      */         
/* 1386 */         if (!"".equals(str9)) {
/* 1387 */           str11 = sptmForCrmModiRecord.getCrmModiInfo(str8, str9);
/* 1388 */           if (str8.equals(SystemEnv.getHtmlLabelName(23247, Integer.parseInt(paramString3)))) str11 = customerStatusComInfo.getCustomerStatusname(str9); 
/* 1389 */           if (str8.equals(SystemEnv.getHtmlLabelName(231, Integer.parseInt(paramString3)))) str11 = languageComInfo.getLanguagename(str9); 
/*      */         } 
/* 1391 */         if (!"".equals(str10)) {
/* 1392 */           str12 = sptmForCrmModiRecord.getCrmModiInfo(str8, str10);
/* 1393 */           if (str8.equals(SystemEnv.getHtmlLabelName(23247, Integer.parseInt(paramString3)))) str12 = customerStatusComInfo.getCustomerStatusname(str10); 
/* 1394 */           if (str8.equals(SystemEnv.getHtmlLabelName(231, Integer.parseInt(paramString3)))) str12 = languageComInfo.getLanguagename(str10); 
/*      */         } 
/* 1396 */         if (!"".equals(str11) || !"".equals(str12)) {
/* 1397 */           if ("".equals(str11)) str11 = "&nbsp;&nbsp;"; 
/* 1398 */           str7 = str7 + "<font class='log_txt'>" + SystemEnv.getHtmlLabelName(501707, Integer.parseInt(paramString3)) + "</font> <font class='log_field'>" + str8 + "</font> <font class='log_txt'>" + SystemEnv.getHtmlLabelName(623, Integer.parseInt(paramString3)) + "</font> <font class='log_value'>'" + str11 + "'</font> <font class='log_txt'>" + SystemEnv.getHtmlLabelName(501708, Integer.parseInt(paramString3)) + "</font> <font class='log_value'>'" + str12 + "\"</font>"; continue;
/*      */         } 
/* 1400 */         if ("".equals(str9)) str9 = "&nbsp;&nbsp;";
/*      */         
/* 1402 */         str7 = str7 + "<font class='log_txt'>" + SystemEnv.getHtmlLabelName(501707, Integer.parseInt(paramString3)) + "</font> <font class='log_field'>" + str8 + "</font> <font class='log_txt'>" + SystemEnv.getHtmlLabelName(623, Integer.parseInt(paramString3)) + "</font> <font class='log_value'>'" + str9 + "'</font> <font class='log_txt'>" + SystemEnv.getHtmlLabelName(501708, Integer.parseInt(paramString3)) + "</font> <font class='log_value'>'" + str10 + "\"</font>";
/*      */       } 
/*      */       
/* 1405 */       str6 = str6 + str7;
/* 1406 */     } catch (Exception exception) {}
/*      */     
/* 1408 */     return str6;
/*      */   }
/*      */   
/*      */   public String getSellStatus(String paramString) {
/* 1412 */     String str = "";
/* 1413 */     if (Util.null2String(paramString).length() > 0) {
/* 1414 */       RecordSet recordSet = new RecordSet();
/* 1415 */       String str1 = "select fullname from CRM_SellStatus where id =" + paramString;
/* 1416 */       recordSet.executeSql(str1);
/* 1417 */       if (recordSet.next()) {
/* 1418 */         str = recordSet.getString("fullname");
/*      */       }
/*      */     } 
/* 1421 */     return str;
/*      */   }
/*      */ 
/*      */   
/*      */   public String getEndStatus(String paramString1, String paramString2) {
/* 1426 */     if (paramString1.equals("0"))
/* 1427 */       return SystemEnv.getHtmlLabelName(1960, Util.getIntValue(paramString2)); 
/* 1428 */     if (paramString1.equals("1")) {
/* 1429 */       return SystemEnv.getHtmlLabelName(15242, Util.getIntValue(paramString2));
/*      */     }
/* 1431 */     return SystemEnv.getHtmlLabelName(498, Util.getIntValue(paramString2));
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   public String getContractTypeName(String paramString) {
/* 1437 */     String str = "";
/*      */     try {
/* 1439 */       ContractTypeComInfo contractTypeComInfo = new ContractTypeComInfo();
/* 1440 */       str = contractTypeComInfo.getContractTypename(paramString);
/* 1441 */     } catch (Exception exception) {}
/* 1442 */     return str;
/*      */   }
/*      */   
/*      */   public String getContractStatus(String paramString1, String paramString2) {
/* 1446 */     if (paramString1.equals("0"))
/* 1447 */       return SystemEnv.getHtmlLabelName(615, Util.getIntValue(paramString2)); 
/* 1448 */     if (paramString1.equals("-1"))
/* 1449 */       return SystemEnv.getHtmlLabelName(2242, Util.getIntValue(paramString2)); 
/* 1450 */     if (paramString1.equals("1"))
/* 1451 */       return SystemEnv.getHtmlLabelName(1423, Util.getIntValue(paramString2)); 
/* 1452 */     if (paramString1.equals("2")) {
/* 1453 */       return SystemEnv.getHtmlLabelName(6095, Util.getIntValue(paramString2));
/*      */     }
/* 1455 */     return SystemEnv.getHtmlLabelName(555, Util.getIntValue(paramString2));
/*      */   }
/*      */   public String getContactContent(String paramString1, String paramString2) {
/* 1458 */     return getContactContent(paramString1, paramString2, "7");
/*      */   }
/*      */   
/*      */   public String getContactContent(String paramString1, String paramString2, String paramString3) {
/* 1462 */     String str = "";
/*      */     try {
/* 1464 */       ResourceComInfo resourceComInfo = new ResourceComInfo();
/* 1465 */       CustomerInfoComInfo customerInfoComInfo = new CustomerInfoComInfo();
/* 1466 */       CommonTransUtil commonTransUtil = new CommonTransUtil();
/* 1467 */       CustomerService customerService = new CustomerService();
/*      */       
/* 1469 */       ArrayList<String> arrayList = Util.TokenizerString(paramString2, "+");
/* 1470 */       String str1 = arrayList.get(0);
/* 1471 */       String str2 = arrayList.get(1);
/* 1472 */       String str3 = arrayList.get(2);
/* 1473 */       String str4 = arrayList.get(3);
/* 1474 */       String str5 = ((String)arrayList.get(4)).trim();
/* 1475 */       String str6 = ((String)arrayList.get(5)).trim();
/* 1476 */       String str7 = ((String)arrayList.get(6)).trim();
/* 1477 */       String str8 = ((String)arrayList.get(7)).trim();
/* 1478 */       String str9 = ((String)arrayList.get(8)).trim();
/* 1479 */       String str10 = ((String)arrayList.get(9)).trim();
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/* 1493 */       str = "<div class='feedbackshow'><div class='feedbackinfo' ><a href='" + GCONST.getContextPath() + "/hrm/resource/HrmResource.jsp?id='" + str1 + "' target='_blank'>" + resourceComInfo.getResourcename(str1) + "</a> " + str2 + " " + str3 + "</div><div class='feedbackrelate'><div>" + str4 + "</div>" + ((!str5.equals("") && !str5.equals("0")) ? ("<div class='relatetitle'>" + SystemEnv.getHtmlLabelName(783, Integer.parseInt(paramString3)) + "：" + commonTransUtil.getCustomer(str5) + "</div>") : "") + ((!str6.equals("") && !str6.equals("0")) ? ("<div class='relatetitle'>" + SystemEnv.getHtmlLabelName(857, Integer.parseInt(paramString3)) + "：" + commonTransUtil.getDocName(str6) + "</div>") : "") + ((!str7.equals("") && !str7.equals("0")) ? ("<div class='relatetitle'>" + SystemEnv.getHtmlLabelName(22105, Integer.parseInt(paramString3)) + "：" + commonTransUtil.getRequestName(str7) + "</div>") : "") + ((!str8.equals("") && !str8.equals("0")) ? ("<div class='relatetitle'>" + SystemEnv.getHtmlLabelName(782, Integer.parseInt(paramString3)) + "：" + commonTransUtil.getTaskName(str8) + "</div>") : "") + ((!str10.equals("") && !str10.equals("0")) ? ("<div class='relatetitle'>" + SystemEnv.getHtmlLabelName(84372, Integer.parseInt(paramString3)) + "：" + customerService.getSellChanceName(str10) + "</div>") : "") + ((!str9.equals("") && !str9.equals("0")) ? ("<div class='relatetitle'>" + SystemEnv.getHtmlLabelName(84373, Integer.parseInt(paramString3)) + "：" + customerService.getContacterName(str9) + "</div>") : "") + "</div></div>";
/*      */ 
/*      */     
/*      */     }
/* 1497 */     catch (Exception exception) {}
/*      */ 
/*      */     
/* 1500 */     return str;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getBirthdayFormate(String paramString1, String paramString2) {
/* 1510 */     if (paramString1.equals("") || paramString1.length() != 10)
/* 1511 */       return ""; 
/* 1512 */     String str1 = paramString1.substring(5);
/* 1513 */     String str2 = "";
/*      */     
/* 1515 */     String str3 = TimeUtil.getCurrentDateString();
/* 1516 */     String str4 = TimeUtil.dateAdd(str3, 1);
/* 1517 */     String str5 = TimeUtil.dateAdd(str3, 2);
/*      */ 
/*      */     
/* 1520 */     String[] arrayOfString = { SystemEnv.getHtmlLabelName(16106, Util.getIntValue(paramString2)), SystemEnv.getHtmlLabelName(16100, Util.getIntValue(paramString2)), SystemEnv.getHtmlLabelName(16101, Util.getIntValue(paramString2)), SystemEnv.getHtmlLabelName(16102, Util.getIntValue(paramString2)), SystemEnv.getHtmlLabelName(16103, Util.getIntValue(paramString2)), SystemEnv.getHtmlLabelName(16104, Util.getIntValue(paramString2)), SystemEnv.getHtmlLabelName(16105, Util.getIntValue(paramString2)) };
/* 1521 */     SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
/*      */     
/*      */     try {
/* 1524 */       if (str1.equals(str3.substring(5))) {
/* 1525 */         str2 = "<span style='color:red'>[" + SystemEnv.getHtmlLabelName(15537, Integer.parseInt(paramString2)) + "]</span>";
/* 1526 */       } else if (str1.equals(str4.substring(5))) {
/* 1527 */         str2 = "<span style='color:blue'>[" + SystemEnv.getHtmlLabelName(22488, Integer.parseInt(paramString2)) + "]</span>";
/* 1528 */       } else if (str1.equals(str5.substring(5))) {
/* 1529 */         str2 = "<span style='color:blue'>[" + SystemEnv.getHtmlLabelName(22492, Integer.parseInt(paramString2)) + "]</span>";
/*      */       } 
/* 1531 */       if (paramString1.length() == 10) {
/* 1532 */         paramString1 = str3.substring(0, 5) + paramString1.substring(5);
/*      */       }
/*      */       
/* 1535 */       if (simpleDateFormat.parse(paramString1).getDay() == 0 || simpleDateFormat.parse(paramString1).getDay() == 6) {
/* 1536 */         str2 = str2 + "<span style='color:red'>[" + arrayOfString[simpleDateFormat.parse(paramString1).getDay()] + "]</span>";
/*      */       }
/* 1538 */     } catch (Exception exception) {}
/* 1539 */     return str1 + str2;
/*      */   }
/*      */   
/*      */   public String getContacterValue(String paramString1, String paramString2) {
/* 1543 */     String str1 = "";
/* 1544 */     RecordSet recordSet = new RecordSet();
/* 1545 */     String str2 = "";
/* 1546 */     if (recordSet.getDBType().equals("oracle")) {
/* 1547 */       str2 = "select * from (select id,firstname,jobtitle,email,phoneoffice,mobilephone,phonehome,title,customerid from CRM_CustomerContacter where customerid =" + paramString1 + "  ORDER BY main desc,id desc) t3 where rownum=1";
/* 1548 */     } else if (recordSet.getDBType().equals("mysql")) {
/* 1549 */       str2 = "select * from (select id,firstname,jobtitle,email,phoneoffice,mobilephone,phonehome,title,customerid from CRM_CustomerContacter where customerid =" + paramString1 + "  ORDER BY main desc,id desc) t3 limit 0,1";
/*      */     }
/* 1551 */     else if (recordSet.getDBType().equals("postgresql")) {
/* 1552 */       str2 = "select * from (select id,firstname,jobtitle,email,phoneoffice,mobilephone,phonehome,title,customerid from CRM_CustomerContacter where customerid =" + paramString1 + "  ORDER BY main desc,id desc) t3 limit 1 offset 0";
/*      */     } else {
/*      */       
/* 1555 */       str2 = "select TOP 1 id,firstname,jobtitle,email,phoneoffice,mobilephone,phonehome,title,customerid from CRM_CustomerContacter where customerid =" + paramString1 + "  ORDER BY main desc,id desc";
/*      */     } 
/* 1557 */     recordSet.execute(str2);
/* 1558 */     if (recordSet.next()) {
/* 1559 */       str1 = recordSet.getString(paramString2);
/* 1560 */       if (paramString2.equals("firstname")) {
/* 1561 */         String str = recordSet.getString("id");
/* 1562 */         str1 = "<a href='" + GCONST.getContextPath() + "/CRM/data/ViewContacter.jsp?ContacterID=" + str + "' target='_blank'>" + str1 + "</a>";
/*      */       } 
/*      */     } 
/*      */     
/* 1566 */     return str1;
/*      */   }
/*      */   
/*      */   public String getContacterMobilephone(String paramString) {
/* 1570 */     return getContacterValue(paramString, "mobilephone");
/*      */   }
/*      */   
/*      */   public String getContacterEmail(String paramString) {
/* 1574 */     return getContacterValue(paramString, "email");
/*      */   }
/*      */   
/*      */   public String getContacterPhoneoffice(String paramString) {
/* 1578 */     return getContacterValue(paramString, "phoneoffice");
/*      */   }
/*      */   
/*      */   public String getContacterJobtitle(String paramString) {
/* 1582 */     return getContacterValue(paramString, "jobtitle");
/*      */   }
/*      */   
/*      */   public String getContacterFirstname(String paramString) {
/* 1586 */     return getContacterValue(paramString, "firstname");
/*      */   }
/*      */   
/*      */   public String getContacterPhoneHome(String paramString) {
/* 1590 */     return getContacterValue(paramString, "phonehome");
/*      */   }
/*      */   
/*      */   public String getContacterTitle(String paramString) {
/* 1594 */     String str = getContacterValue(paramString, "title");
/* 1595 */     ContacterTitleComInfo contacterTitleComInfo = new ContacterTitleComInfo();
/* 1596 */     return contacterTitleComInfo.getContacterTitlename(str);
/*      */   }
/*      */   
/*      */   public String getCustomerName(String paramString1, String paramString2) {
/* 1600 */     return "<div href='javascript:;' style='width:100%;height:30px;line-height:30px;cursor:pointer;' onclick='viewDetail(" + paramString2 + ",this)' ><a>" + paramString1 + "</a></div>";
/*      */   }
/*      */ 
/*      */   
/*      */   public String getImportant(String paramString1, String paramString2) {
/* 1605 */     String str = "";
/* 1606 */     if ("1".equals(paramString1)) {
/* 1607 */       str = "important";
/*      */     } else {
/* 1609 */       str = "important_no";
/* 1610 */     }  return "<div style='float:center;' class='" + str + "' _important='" + paramString1 + "' _customerid='" + paramString2 + "' onclick='markImportant(this)'></div>";
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getCrmColString(String paramString1, String paramString2, String paramString3, int paramInt, String paramString4, String paramString5, User paramUser, String paramString6, String paramString7) {
/* 1621 */     boolean bool = "1".equals(paramString7);
/* 1622 */     String str = "";
/* 1623 */     switch (paramInt) {
/*      */       case 1:
/* 1625 */         if (paramString2.equals("name")) {
/* 1626 */           str = str + "<col name='" + paramString2 + "' width='20%' text='" + SystemEnv.getHtmlLabelNames(paramString3, paramUser.getLanguage()) + "' column='" + paramString2 + "' href='" + GCONST.getContextPath() + "/CRM/data/ViewCustomer.jsp' linkkey='CustomerID' linkvaluecolumn='id' orderkey='t1." + paramString2 + "' target='_blank'/>";
/*      */         
/*      */         }
/* 1629 */         else if (paramString2.equals("email")) {
/* 1630 */           str = str + "<col name='" + paramString2 + "' width='10%' text='" + SystemEnv.getHtmlLabelNames(paramString3, paramUser.getLanguage()) + "' column='" + paramString2 + "' display = '" + bool + "'/>";
/*      */         }
/*      */         else {
/*      */           
/* 1634 */           if ("4".equals(paramString6))
/* 1635 */             if (paramString2.equals("firstname"))
/* 1636 */             { str = str + "<col name='" + paramString2 + "' width='10%' text='" + SystemEnv.getHtmlLabelNames(paramString3, paramUser.getLanguage()) + "' column='id' transmethod='weaver.crm.Maint.CRMTransMethod.getContacterFirstname' display = '" + bool + "'/>";
/*      */                }
/*      */             
/* 1639 */             else if (paramString2.equals("mobilephone"))
/* 1640 */             { str = str + "<col name='" + paramString2 + "' width='10%' text='" + SystemEnv.getHtmlLabelNames(paramString3, paramUser.getLanguage()) + "' column='id' transmethod='weaver.crm.Maint.CRMTransMethod.getContacterMobilephone' display = '" + bool + "'/>";
/*      */                }
/*      */             
/* 1643 */             else if (paramString2.equals("contactemail"))
/* 1644 */             { str = str + "<col name='" + paramString2 + "' width='10%' text='" + SystemEnv.getHtmlLabelNames(paramString3, paramUser.getLanguage()) + "' column='id' transmethod='weaver.crm.Maint.CRMTransMethod.getContacterEmail' display = '" + bool + "'/>";
/*      */                }
/*      */             
/* 1647 */             else if (paramString2.equals("phoneoffice"))
/* 1648 */             { str = str + "<col name='" + paramString2 + "' width='10%' text='" + SystemEnv.getHtmlLabelNames(paramString3, paramUser.getLanguage()) + "' column='id' transmethod='weaver.crm.Maint.CRMTransMethod.getContacterPhoneoffice' display = '" + bool + "'/>";
/*      */                }
/*      */             
/* 1651 */             else if (paramString2.equals("phonehome"))
/* 1652 */             { str = str + "<col name='" + paramString2 + "' width='10%' text='" + SystemEnv.getHtmlLabelNames(paramString3, paramUser.getLanguage()) + "' column='id' transmethod='weaver.crm.Maint.CRMTransMethod.getContacterPhoneHome' display = '" + bool + "'/>"; }
/*      */             else
/*      */             
/* 1655 */             { if (paramString2.equals("jobtitle")) {
/* 1656 */                 str = str + "<col name='" + paramString2 + "' width='10%' text='" + SystemEnv.getHtmlLabelNames(paramString3, paramUser.getLanguage()) + "' column='id' transmethod='weaver.crm.Maint.CRMTransMethod.getContacterJobtitle' display = '" + bool + "'/>";
/*      */               }
/*      */ 
/*      */ 
/*      */               
/* 1661 */               str = str + "<col name='" + paramString2 + "' width='10%' text='" + SystemEnv.getHtmlLabelNames(paramString3, paramUser.getLanguage()) + "' column='" + paramString2 + "' orderkey='t1." + paramString2 + "' display = '" + bool + "'/>"; }   str = str + "<col name='" + paramString2 + "' width='10%' text='" + SystemEnv.getHtmlLabelNames(paramString3, paramUser.getLanguage()) + "' column='" + paramString2 + "' orderkey='t1." + paramString2 + "' display = '" + bool + "'/>";
/*      */         } 
/*      */       case 2:
/* 1664 */         str = str + "<col name='" + paramString2 + "' width='10%' text='" + SystemEnv.getHtmlLabelNames(paramString3, paramUser.getLanguage()) + "' column='" + paramString2 + "' orderkey='t1." + paramString2 + "' display = '" + bool + "'/>";
/*      */       
/*      */       case 3:
/* 1667 */         if ("4".equals(paramString6) && paramString2.equals("title")) {
/* 1668 */           str = str + "<col name='" + paramString2 + "' width='10%' text='" + SystemEnv.getHtmlLabelNames(paramString3, paramUser.getLanguage()) + "' column='id' transmethod='weaver.crm.Maint.CRMTransMethod.getContacterTitle' display = '" + bool + "'/>";
/*      */ 
/*      */         
/*      */         }
/* 1672 */         else if (paramString2.equals("type")) {
/* 1673 */           str = str + "<col name='" + paramString2 + "' width='10%' text='" + SystemEnv.getHtmlLabelNames(paramString3, paramUser.getLanguage()) + "' column='" + paramString2 + "' transmethod='weaver.crm.Maint.CustomerTypeComInfo.getCustomerTypename' orderkey='t1.type' target='_blank'/>";
/*      */         
/*      */         }
/* 1676 */         else if (paramString2.equals("status")) {
/* 1677 */           str = str + "<col name='" + paramString2 + "' width='10%' text='" + SystemEnv.getHtmlLabelNames(paramString3, paramUser.getLanguage()) + "' column='" + paramString2 + "' transmethod='weaver.crm.Maint.CustomerStatusComInfo.getCustomerStatusname' orderkey='t1.status' target='_blank'/>";
/*      */         
/*      */         }
/* 1680 */         else if (paramString2.equals("manager")) {
/* 1681 */           str = str + "<col name='" + paramString2 + "' width='10%' text='" + SystemEnv.getHtmlLabelNames(paramString3, paramUser.getLanguage()) + "' column='" + paramString2 + "' transmethod='weaver.hrm.resource.ResourceComInfo.getResourcename' href='" + GCONST.getContextPath() + "/hrm/resource/HrmResource.jsp' linkkey='id' orderkey='t1." + paramString2 + "' target='_blank'/>";
/*      */         } else {
/*      */           
/* 1684 */           String str1 = paramString4;
/* 1685 */           if (!"".equals(paramString5)) {
/* 1686 */             str1 = str1 + "+" + paramString5;
/*      */           }
/* 1688 */           str = str + "<col name='" + paramString2 + "' width='10%' text='" + SystemEnv.getHtmlLabelNames(paramString3, paramUser.getLanguage()) + "' column='" + paramString2 + "' otherpara='" + str1 + "' transmethod='weaver.crm.Maint.CRMTransMethod.getBrowserName' display = '" + bool + "'/>";
/*      */         } 
/*      */       case 4:
/* 1691 */         str = str + "<col name='" + paramString2 + "' width='10%' text='" + SystemEnv.getHtmlLabelNames(paramString3, paramUser.getLanguage()) + "' column='" + paramString2 + "' otherpara='" + paramUser.getLanguage() + "' transmethod='weaver.crm.Maint.CRMTransMethod.getCheckedName' display = '" + bool + "'/>";
/*      */       
/*      */       case 5:
/* 1694 */         str = str + "<col name='" + paramString2 + "' width='10%' text='" + SystemEnv.getHtmlLabelNames(paramString3, paramUser.getLanguage()) + "' column='" + paramString2 + "' otherpara='" + paramString1 + "' transmethod='weaver.crm.Maint.CRMTransMethod.getSelectName' display = '" + bool + "'/>";
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*      */       case 6:
/* 1702 */         return str;
/*      */     } 
/*      */     str = str + "<col name='" + paramString2 + "' width='10%' text='" + SystemEnv.getHtmlLabelNames(paramString3, paramUser.getLanguage()) + "' column='" + paramString2 + "' display = '" + bool + "'/>";
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   public String getSelectName(String paramString1, String paramString2) {
/* 1710 */     CrmUtil crmUtil = new CrmUtil();
/*      */     
/* 1712 */     RecordSet recordSet = new RecordSet();
/* 1713 */     recordSet.executeQuery("select fieldname from CRM_CustomerDefinField where id = ?  ", new Object[] { paramString2 });
/* 1714 */     if (recordSet.next()) {
/* 1715 */       if ("portalstatus".equals(recordSet.getString(1).toLowerCase())) {
/* 1716 */         if ("".equals(paramString1)) {
/* 1717 */           paramString1 = "0";
/*      */         }
/* 1719 */       } else if ("seasflag".equals(recordSet.getString(1).toLowerCase())) {
/* 1720 */         if (paramString1.equals("1") || paramString1.equals("2")) {
/* 1721 */           paramString1 = "1";
/*      */         } else {
/* 1723 */           paramString1 = "0";
/*      */         } 
/*      */       } 
/*      */     }
/*      */ 
/*      */     
/* 1729 */     return crmUtil.getFieldvalue(paramString2, paramString1);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getCheckedName(String paramString1, String paramString2) {
/* 1737 */     if ("".equals(paramString1))
/* 1738 */       return SystemEnv.getHtmlLabelName(161, Util.getIntValue(paramString2)); 
/* 1739 */     return "1".equals(paramString1) ? SystemEnv.getHtmlLabelName(163, Util.getIntValue(paramString2)) : SystemEnv.getHtmlLabelName(161, Util.getIntValue(paramString2));
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getBrowserName(String paramString1, String paramString2) {
/* 1747 */     if ("".equals(paramString1))
/* 1748 */       return ""; 
/* 1749 */     String str1 = "";
/* 1750 */     int i = 0;
/* 1751 */     if (paramString2.indexOf("+") < 0) {
/* 1752 */       i = Integer.parseInt(paramString2);
/*      */     } else {
/* 1754 */       String[] arrayOfString = paramString2.split("\\+");
/* 1755 */       i = Integer.parseInt(arrayOfString[0]);
/* 1756 */       str1 = arrayOfString[1];
/*      */     } 
/* 1758 */     CrmUtil crmUtil = new CrmUtil();
/* 1759 */     User user = new User();
/* 1760 */     String str2 = crmUtil.getFieldvalue(user, i, paramString1, str1);
/* 1761 */     if ((i == 161 || i == 162) && 
/* 1762 */       str1.indexOf(".") != -1) {
/* 1763 */       String str3 = str1.substring(str1.indexOf(".") + 1);
/* 1764 */       String str4 = "SELECT detailpageurl FROM mode_browser WHERE showname=?";
/* 1765 */       RecordSet recordSet = new RecordSet();
/* 1766 */       recordSet.executeQuery(str4, new Object[] { str3 });
/* 1767 */       if (recordSet.next()) {
/* 1768 */         String str = recordSet.getString(1) + paramString1;
/* 1769 */         str2 = "<a href=" + str + " target='_blank'>" + str2 + "</a>";
/*      */       } 
/*      */     } 
/*      */     
/* 1773 */     return str2;
/*      */   }
/*      */   
/*      */   public String getIsOpenStr(String paramString1, String paramString2) {
/* 1777 */     String str = "";
/* 1778 */     if ("1".equals(paramString1)) {
/* 1779 */       str = "<input type=checkbox name='chkOpen' interfaceId='" + paramString2 + "' id='chkOpen_" + paramString2 + "' onclick=\"onUse(this)\" checked value='" + paramString2 + "'>";
/*      */     } else {
/* 1781 */       str = "<input type=checkbox name='chkOpen' interfaceId='" + paramString2 + "' id='chkOpen_" + paramString2 + "' onclick=\"onUse(this)\" value='" + paramString2 + "'>";
/*      */     } 
/* 1783 */     return str;
/*      */   }
/*      */   public String getInterfaceType(String paramString) {
/* 1786 */     return getInterfaceType(paramString, "7");
/*      */   }
/*      */   public String getInterfaceType(String paramString1, String paramString2) {
/* 1789 */     return "2".equals(paramString1) ? SystemEnv.getHtmlLabelName(501972, Integer.parseInt(paramString2)) : SystemEnv.getHtmlLabelName(501677, Integer.parseInt(paramString2));
/*      */   }
/*      */   
/*      */   public String getInterfaceGroup(String paramString) {
/* 1793 */     RecordSet recordSet = new RecordSet();
/* 1794 */     String str = "";
/*      */     
/* 1796 */     recordSet.execute("select * from CRM_Business_Group where id=" + paramString);
/* 1797 */     if (recordSet.next()) {
/* 1798 */       str = recordSet.getString("name");
/*      */     }
/* 1800 */     return str;
/*      */   }
/*      */   
/*      */   public String getUperOrLowperValue(String paramString) {
/* 1804 */     String str = "";
/*      */     
/* 1806 */     if (Util.getIntValue(paramString) > -1) {
/* 1807 */       return paramString;
/*      */     }
/* 1809 */     return str;
/*      */   }
/*      */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/crm/Maint/CRMTransMethod.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */