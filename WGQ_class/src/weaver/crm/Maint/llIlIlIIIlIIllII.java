package weaver.crm.Maint;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import javax.activation.DataSource;

class llIlIlIIIlIIllII implements DataSource {
  private byte[] _$1;
  
  private String _$2;
  
  llIlIlIIIlIIllII(String paramString1, String paramString2) {
    try {
      this._$1 = paramString1.getBytes("iso-8859-1");
    } catch (UnsupportedEncodingException unsupportedEncodingException) {}
    this._$2 = paramString2;
  }
  
  public String getContentType() {
    return this._$2;
  }
  
  public InputStream getInputStream() throws IOException {
    if (this._$1 == null)
      throw new IOException("no data"); 
    return new ByteArrayInputStream(this._$1);
  }
  
  public String getName() {
    return "weaver  , <EMAIL>";
  }
  
  public OutputStream getOutputStream() throws IOException {
    throw new IOException("cannot do this");
  }
}


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/crm/Maint/llIlIlIIIlIIllII.class
 * Java compiler version: 1 (45.3)
 * JD-Core Version:       1.1.3
 */