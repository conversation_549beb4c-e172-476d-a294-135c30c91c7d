/*    */ package weaver.crm.Maint;
/*    */ 
/*    */ import weaver.cache.CacheBase;
/*    */ import weaver.cache.CacheColumn;
/*    */ import weaver.cache.CacheColumnType;
/*    */ import weaver.cache.PKColumn;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class ContactWayComInfo
/*    */   extends CacheBase
/*    */ {
/* 20 */   protected static String TABLE_NAME = "CRM_ContactWay";
/*    */   @PKColumn(type = CacheColumnType.NUMBER)
/* 22 */   protected static String PK_NAME = "id";
/*    */ 
/*    */   
/*    */   @CacheColumn
/*    */   protected static int id;
/*    */ 
/*    */   
/*    */   @CacheColumn
/*    */   protected static int fullname;
/*    */   
/*    */   @CacheColumn
/*    */   protected static int description;
/*    */ 
/*    */   
/*    */   public int getContactWayNum() {
/* 37 */     return size();
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getContactWayid() {
/* 45 */     return (String)getRowValue(id);
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getContactWayname() {
/* 52 */     return (String)getRowValue(fullname);
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getContactWayname(String paramString) {
/* 61 */     return (String)getValue(fullname, paramString);
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getContactWaydesc() {
/* 68 */     return (String)getRowValue(description);
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getContactWaydesc(String paramString) {
/* 77 */     return (String)getValue(description, paramString);
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public void removeContactWayCache() {
/* 84 */     removeCache();
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/crm/Maint/ContactWayComInfo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */