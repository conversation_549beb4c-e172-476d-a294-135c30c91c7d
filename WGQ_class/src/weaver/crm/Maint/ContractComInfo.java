/*     */ package weaver.crm.Maint;
/*     */ 
/*     */ import weaver.cache.CacheBase;
/*     */ import weaver.cache.CacheColumn;
/*     */ import weaver.cache.CacheColumnType;
/*     */ import weaver.cache.PKColumn;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class ContractComInfo
/*     */   extends CacheBase
/*     */ {
/*  20 */   protected static String TABLE_NAME = "CRM_Contract";
/*     */   @PKColumn(type = CacheColumnType.NUMBER)
/*  22 */   protected static String PK_NAME = "id";
/*     */ 
/*     */ 
/*     */   
/*     */   @CacheColumn(name = "id")
/*     */   protected static int id;
/*     */ 
/*     */   
/*     */   @CacheColumn(name = "name")
/*     */   protected static int name;
/*     */ 
/*     */   
/*     */   @CacheColumn(name = "crmId")
/*     */   protected static int crmId;
/*     */ 
/*     */   
/*     */   @CacheColumn(name = "manager")
/*     */   protected static int manager;
/*     */ 
/*     */ 
/*     */   
/*     */   public int getContractNum() {
/*  44 */     return size();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getContractid() {
/*  52 */     return (String)getRowValue(id);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getContractname() {
/*  60 */     return (String)getRowValue(name);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getContractname(String paramString) {
/*  69 */     return (String)getValue(name, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getContractcrmid() {
/*  76 */     return (String)getRowValue(crmId);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getContractcrmid(String paramString) {
/*  85 */     return (String)getValue(crmId, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getManagerid() {
/*  92 */     return (String)getRowValue(manager);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getManagerid(String paramString) {
/* 101 */     return (String)getValue(manager, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void removeContractCache() {
/* 109 */     removeCache();
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/crm/Maint/ContractComInfo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */