/*     */ package weaver.crm.Maint;
/*     */ 
/*     */ import weaver.cache.CacheBase;
/*     */ import weaver.cache.CacheColumn;
/*     */ import weaver.cache.CacheColumnType;
/*     */ import weaver.cache.PKColumn;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CustomerContactWayComInfo
/*     */   extends CacheBase
/*     */ {
/*  20 */   protected static String TABLE_NAME = "crm_customercontactway";
/*     */   @PKColumn(type = CacheColumnType.NUMBER)
/*  22 */   protected static String PK_NAME = "id";
/*     */   
/*     */   @CacheColumn
/*     */   protected static int id;
/*     */   
/*     */   @CacheColumn
/*     */   protected static int fullname;
/*     */   
/*     */   @CacheColumn
/*     */   protected static int description;
/*     */   
/*     */   @CacheColumn
/*     */   protected static int orderkey;
/*     */ 
/*     */   
/*     */   public int getContactWayNum() {
/*  38 */     return size();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getContactWayid() {
/*  46 */     return (String)getRowValue(id);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getContactWayname() {
/*  53 */     return (String)getRowValue(fullname);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getContactWayname(String paramString) {
/*  62 */     return (String)getValue(fullname, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getContactWaydesc() {
/*  69 */     return (String)getRowValue(description);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getContactWaydesc(String paramString) {
/*  78 */     return (String)getValue(description, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getContactWayOrder() {
/*  85 */     return (String)getRowValue(orderkey);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getContactWayOrder(String paramString) {
/*  94 */     return (String)getValue(orderkey, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void removeCustomerContactWayCache() {
/* 101 */     removeCache();
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/crm/Maint/CustomerContactWayComInfo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */