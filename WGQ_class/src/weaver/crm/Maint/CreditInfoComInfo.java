/*     */ package weaver.crm.Maint;
/*     */ 
/*     */ import weaver.cache.CacheBase;
/*     */ import weaver.cache.CacheColumn;
/*     */ import weaver.cache.CacheColumnType;
/*     */ import weaver.cache.PKColumn;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CreditInfoComInfo
/*     */   extends CacheBase
/*     */ {
/*  19 */   protected static String TABLE_NAME = "CRM_CreditInfo";
/*     */   @PKColumn(type = CacheColumnType.NUMBER)
/*  21 */   protected static String PK_NAME = "id";
/*     */ 
/*     */   
/*     */   @CacheColumn
/*     */   protected static int id;
/*     */ 
/*     */   
/*     */   @CacheColumn
/*     */   protected static int fullname;
/*     */ 
/*     */   
/*     */   @CacheColumn
/*     */   protected static int creditamount;
/*     */ 
/*     */   
/*     */   @CacheColumn
/*     */   protected static int highamount;
/*     */ 
/*     */ 
/*     */   
/*     */   public int getCreditInfoNum() {
/*  42 */     return size();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCreditInfoid() {
/*  50 */     return (String)getRowValue(id);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCreditInfoname() {
/*  57 */     return (String)getRowValue(fullname);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCreditInfoname(String paramString) {
/*  66 */     return (String)getValue(fullname, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCreditInfodesc() {
/*  73 */     return (String)getRowValue(creditamount);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCreditInfodesc(String paramString) {
/*  82 */     return (String)getValue(creditamount, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCreditInfohighamount() {
/*  89 */     return (String)getRowValue(highamount);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCreditInfohighamount(String paramString) {
/*  98 */     return (String)getValue(highamount, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void removeCreditInfoCache() {
/* 105 */     removeCache();
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/crm/Maint/CreditInfoComInfo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */