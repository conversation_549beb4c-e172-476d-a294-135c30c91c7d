/*    */ package weaver.crm.Maint;
/*    */ 
/*    */ import weaver.cache.CacheBase;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class RegionComInfo
/*    */   extends CacheBase
/*    */ {
/*    */   public String getRegionID() {
/* 13 */     return "";
/*    */   }
/*    */   public String getParentID() {
/* 16 */     return "";
/*    */   }
/*    */   
/*    */   public String getRegionName(String paramString) {
/* 20 */     return "";
/*    */   }
/*    */   
/*    */   public String getChildProvinceCount(String paramString) {
/* 24 */     return "";
/*    */   }
/*    */   public String getChildRegionCount(String paramString) {
/* 27 */     return "";
/*    */   }
/*    */   
/*    */   public String getParentID(String paramString) {
/* 31 */     return "";
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/crm/Maint/RegionComInfo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */