/*    */ package weaver.crm.Maint;
/*    */ 
/*    */ import weaver.cache.CacheBase;
/*    */ import weaver.cache.CacheColumn;
/*    */ import weaver.cache.CacheColumnType;
/*    */ import weaver.cache.PKColumn;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class CustomerSizeComInfo
/*    */   extends CacheBase
/*    */ {
/* 20 */   protected static String TABLE_NAME = "CRM_CustomerSize";
/*    */   @PKColumn(type = CacheColumnType.NUMBER)
/* 22 */   protected static String PK_NAME = "id";
/*    */ 
/*    */   
/*    */   @CacheColumn
/*    */   protected static int id;
/*    */ 
/*    */   
/*    */   @CacheColumn
/*    */   protected static int fullname;
/*    */   
/*    */   @CacheColumn
/*    */   protected static int description;
/*    */ 
/*    */   
/*    */   public int getCustomerSizeNum() {
/* 37 */     return size();
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getCustomerSizeid() {
/* 45 */     return (String)getRowValue(id);
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getCustomerSizename() {
/* 52 */     return (String)getRowValue(fullname);
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getCustomerSizename(String paramString) {
/* 61 */     return (String)getValue(fullname, paramString);
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getCustomerSizedesc() {
/* 68 */     return (String)getRowValue(description);
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getCustomerSizedesc(String paramString) {
/* 77 */     return (String)getValue(description, paramString);
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public void removeCustomerSizeCache() {
/* 84 */     removeCache();
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/crm/Maint/CustomerSizeComInfo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */