/*     */ package weaver.crm.Maint;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ import weaver.cache.CacheBase;
/*     */ import weaver.cache.CacheColumn;
/*     */ import weaver.cache.CacheColumnType;
/*     */ import weaver.cache.CacheItem;
/*     */ import weaver.cache.CacheMap;
/*     */ import weaver.cache.PKColumn;
/*     */ import weaver.conn.RecordSet;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CustomerInfoComInfo
/*     */   extends CacheBase
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 7513768830046427681L;
/*  37 */   protected static String TABLE_NAME = "crm_customerinfo";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  43 */   protected static String TABLE_WHERE = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  50 */   protected static String TABLE_ORDER = null;
/*     */ 
/*     */   
/*     */   @PKColumn(type = CacheColumnType.NUMBER)
/*  54 */   protected static String PK_NAME = "id";
/*     */   
/*     */   @CacheColumn
/*     */   protected static int name;
/*     */   
/*     */   @CacheColumn
/*     */   protected static int engname;
/*     */   
/*     */   @CacheColumn
/*     */   protected static int twname;
/*     */   
/*     */   @CacheColumn
/*     */   protected static int language;
/*     */   
/*     */   @CacheColumn
/*     */   protected static int manager;
/*     */   
/*     */   @CacheColumn
/*     */   protected static int type;
/*     */   
/*     */   @CacheColumn
/*     */   protected static int status;
/*     */   
/*     */   @CacheColumn
/*     */   protected static int seclevel;
/*     */   
/*     */   @CacheColumn
/*     */   protected static int email;
/*     */   
/*     */   @CacheColumn
/*     */   protected static int subcompanyid;
/*     */   @CacheColumn
/*     */   protected static int crmdeptid;
/*     */   @CacheColumn
/*     */   protected static int crmmanagerdeptid;
/*     */   @CacheColumn
/*     */   protected static int crmcode;
/*     */   @CacheColumn
/*     */   protected static int seasFlag;
/*     */   @CacheColumn(isVirtual = true)
/*     */   protected static int virtualColumn1;
/*     */   
/*     */   public CacheItem initCache(String paramString) {
/*  97 */     if (paramString == null || "".equals(paramString.trim())) {
/*  98 */       return null;
/*     */     }
/*     */     
/* 101 */     RecordSet recordSet = new RecordSet();
/* 102 */     String str = "select cc.id,name,engname,twname,language,manager,type,status , seclevel,email,subcompanyid ,crmdeptid ,crmmanagerdeptid,seasFlag FROM CRM_CustomerInfo cc left join customresourceout cso on cc.id =cso.customid where cc.id=" + paramString;
/*     */     
/* 104 */     recordSet.executeSql(str);
/* 105 */     if (recordSet.next()) {
/* 106 */       CacheItem cacheItem = createCacheItem();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 113 */       parseResultSetToCacheItem(recordSet, cacheItem);
/* 114 */       modifyCacheItem(paramString, cacheItem);
/* 115 */       return cacheItem;
/*     */     } 
/* 117 */     return null;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public CacheMap initCache() throws Exception {
/* 139 */     return null;
/*     */   }
/*     */   
/*     */   public int getCustomerInfoNum() {
/* 143 */     return size();
/*     */   }
/*     */ 
/*     */   
/*     */   public void setTofirstRow() {
/* 148 */     super.setTofirstRow();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCustomerInfoid() {
/* 157 */     return (String)getRowValue(0);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCustomerInfoname() {
/* 166 */     return (String)getRowValue(name);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCustomerInfoname(String paramString) {
/* 176 */     return (String)getValue(name, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCustomerInfoEngname() {
/* 185 */     return (String)getRowValue(engname);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCustomerInfoTwname() {
/* 194 */     return (String)getRowValue(twname);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCustomerInfoEngname(String paramString) {
/* 204 */     return (String)getValue(engname, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCustomerInfoTwname(String paramString) {
/* 214 */     return (String)getValue(twname, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCustomerInfoEmail() {
/* 223 */     return (String)getRowValue(email);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCustomerInfoEmail(String paramString) {
/* 233 */     return (String)getValue(email, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCustomerInfoSubcompanyid() {
/* 242 */     return (String)getRowValue(subcompanyid);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCustomerInfoSubcompanyid(String paramString) {
/* 252 */     return (String)getValue(subcompanyid, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCustomerInfoDepartmentid() {
/* 261 */     return (String)getRowValue(crmdeptid);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCustomerInfoDepartmentid(String paramString) {
/* 271 */     return (String)getValue(crmdeptid, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCustomerInfoManagerdepartmentid() {
/* 280 */     return (String)getRowValue(crmmanagerdeptid);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCustomerInfoManagerdepartmentid(String paramString) {
/* 290 */     return (String)getValue(crmmanagerdeptid, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCustomerInfolanguage() {
/* 299 */     return (String)getRowValue(language);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCustomerInfolanguage(String paramString) {
/* 309 */     return (String)getValue(language, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCustomerInfomanager() {
/* 318 */     return (String)getRowValue(manager);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCustomerInfomanager(String paramString) {
/* 328 */     return (String)getValue(manager, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCustomerInfotype() {
/* 337 */     return (String)getRowValue(type);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCustomerInfotype(String paramString) {
/* 347 */     return (String)getValue(type, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCustomerInfostatus() {
/* 356 */     return (String)getRowValue(status);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCustomerInfostatus(String paramString) {
/* 366 */     return (String)getValue(status, paramString);
/*     */   }
/*     */   
/*     */   public String getSeclevel() {
/* 370 */     return (String)getRowValue(seclevel);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getSeclevel(String paramString) {
/* 380 */     return (String)getValue(seclevel, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void removeCustomerInfoCache() {
/* 387 */     removeCache();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean addCustomerInfoCache(String paramString) {
/* 397 */     updateCache(paramString);
/* 398 */     return true;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void updateCustomerInfoCache(String paramString) throws Exception {
/* 408 */     updateCache(paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void deleteCustomerInfoCache(String paramString) {
/* 418 */     deleteCache(paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCrmCode(String paramString) {
/* 428 */     return (String)getValue(crmcode, paramString);
/*     */   }
/*     */   
/*     */   public String getCrmCode() {
/* 432 */     return (String)getRowValue(crmcode);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getSeasFlag(String paramString) {
/* 442 */     return (String)getValue(seasFlag, paramString);
/*     */   }
/*     */   
/*     */   public String getSeasFlag() {
/* 446 */     return (String)getRowValue(seasFlag);
/*     */   }
/*     */ 
/*     */   
/*     */   public String getVirtualColumn1() {
/* 451 */     return (String)getRowValue(virtualColumn1);
/*     */   }
/*     */ 
/*     */   
/*     */   public String getVirtualColumn1(String paramString) {
/* 456 */     return (String)getValue(virtualColumn1, paramString);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/crm/Maint/CustomerInfoComInfo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */