/*    */ package weaver.crm.Maint;
/*    */ 
/*    */ import weaver.cache.CacheBase;
/*    */ import weaver.cache.CacheColumn;
/*    */ import weaver.cache.CacheColumnType;
/*    */ import weaver.cache.PKColumn;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class EvaluationLevelComInfo
/*    */   extends CacheBase
/*    */ {
/* 20 */   protected static String TABLE_NAME = "CRM_Evaluation_Level";
/*    */   @PKColumn(type = CacheColumnType.NUMBER)
/* 22 */   protected static String PK_NAME = "id";
/*    */ 
/*    */   
/*    */   @CacheColumn
/*    */   protected static int id;
/*    */ 
/*    */   
/*    */   @CacheColumn
/*    */   protected static int name;
/*    */   
/*    */   @CacheColumn
/*    */   protected static int levelvalue;
/*    */ 
/*    */   
/*    */   public int getEvaluationLevelNum() {
/* 37 */     return size();
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getEvaluationLevelid() {
/* 45 */     return (String)getRowValue(id);
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getEvaluationLevelname() {
/* 53 */     return (String)getRowValue(name);
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getEvaluationLevelname(String paramString) {
/* 62 */     return (String)getValue(name, paramString);
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getEvaluationLevellevelvalue() {
/* 69 */     return (String)getRowValue(levelvalue);
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getEvaluationLevellevelvalue(String paramString) {
/* 78 */     return (String)getValue(levelvalue, paramString);
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public void removeEvaluationLevelCache() {
/* 85 */     removeCache();
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/crm/Maint/EvaluationLevelComInfo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */