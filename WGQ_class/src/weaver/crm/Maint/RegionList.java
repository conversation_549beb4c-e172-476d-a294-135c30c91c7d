/*     */ package weaver.crm.Maint;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.Hashtable;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class RegionList
/*     */ {
/*     */   private String selectedId;
/*     */   private ArrayList regionSteps;
/*     */   private ArrayList regionIds;
/*     */   private ArrayList names;
/*     */   private ArrayList images;
/*     */   private ArrayList provinceCounts;
/*     */   private ArrayList regionCounts;
/*     */   private ArrayList parentIds;
/*     */   private Hashtable allRegionInfo;
/*     */   private RegionComInfo comInfo;
/*  25 */   private int currentIndex = -1;
/*  26 */   private int recCount = 0;
/*     */ 
/*     */ 
/*     */   
/*     */   public RegionList() {
/*  31 */     this.selectedId = "";
/*  32 */     this.regionSteps = new ArrayList();
/*  33 */     this.regionIds = new ArrayList();
/*  34 */     this.names = new ArrayList();
/*  35 */     this.images = new ArrayList();
/*  36 */     this.regionCounts = new ArrayList();
/*  37 */     this.parentIds = new ArrayList();
/*  38 */     this.provinceCounts = new ArrayList();
/*  39 */     this.allRegionInfo = new Hashtable<Object, Object>();
/*     */     
/*  41 */     this.comInfo = new RegionComInfo();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void initRegionList(String paramString) {
/*  48 */     this.selectedId = paramString;
/*  49 */     while (this.comInfo.next()) {
/*  50 */       String str1 = this.comInfo.getRegionID();
/*  51 */       String str2 = this.comInfo.getParentID();
/*     */       
/*  53 */       ArrayList<String> arrayList = (this.allRegionInfo.get(str2) == null) ? new ArrayList() : (ArrayList)this.allRegionInfo.get(str2);
/*  54 */       arrayList.add(str1);
/*  55 */       this.allRegionInfo.put(str2, arrayList);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setRegionList(String paramString) {
/*  63 */     this.regionSteps.clear();
/*  64 */     this.regionIds.clear();
/*  65 */     this.names.clear();
/*  66 */     this.images.clear();
/*  67 */     this.provinceCounts.clear();
/*  68 */     this.regionCounts.clear();
/*  69 */     this.parentIds.clear();
/*  70 */     this.currentIndex = -1;
/*  71 */     setRegionListInfo(paramString, 1);
/*  72 */     this.recCount = this.regionIds.size();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getRecordCount() {
/*  79 */     return this.recCount;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean next() {
/*  86 */     if (this.currentIndex + 1 < this.recCount) {
/*  87 */       this.currentIndex++;
/*  88 */       return true;
/*     */     } 
/*  90 */     this.currentIndex = -1;
/*  91 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getRegionID() {
/*  99 */     return this.regionIds.get(this.currentIndex);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getRegionName() {
/* 106 */     return ((String)this.names.get(this.currentIndex)).trim();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getRegionStep() {
/* 113 */     return ((String)this.regionSteps.get(this.currentIndex)).trim();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getImage() {
/* 120 */     return ((String)this.images.get(this.currentIndex)).trim();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getProvinceCount() {
/* 127 */     return ((String)this.provinceCounts.get(this.currentIndex)).trim();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getRegionCount() {
/* 134 */     return ((String)this.regionCounts.get(this.currentIndex)).trim();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getParentID() {
/* 141 */     return ((String)this.parentIds.get(this.currentIndex)).trim();
/*     */   }
/*     */   
/*     */   private void setRegionListInfo(String paramString, int paramInt) {
/* 145 */     boolean bool = false;
/*     */     
/* 147 */     if (!paramString.equals("0")) {
/* 148 */       this.regionSteps.add("" + paramInt);
/* 149 */       this.regionIds.add(paramString);
/* 150 */       this.names.add(this.comInfo.getRegionName(paramString));
/* 151 */       this.provinceCounts.add(this.comInfo.getChildProvinceCount(paramString));
/* 152 */       this.regionCounts.add(this.comInfo.getChildRegionCount(paramString));
/* 153 */       this.parentIds.add(this.comInfo.getParentID(paramString));
/* 154 */       if (this.comInfo.getChildRegionCount(paramString).equals("0")) {
/* 155 */         this.images.add("0");
/*     */       }
/* 157 */       else if (this.selectedId.indexOf(paramString + "|") < 0) {
/* 158 */         this.images.add("1");
/*     */       } else {
/* 160 */         this.images.add("2");
/* 161 */         bool = true;
/*     */       } 
/*     */     } 
/*     */ 
/*     */     
/* 166 */     ArrayList<String> arrayList = (ArrayList)this.allRegionInfo.get(paramString);
/*     */     
/* 168 */     if (arrayList != null)
/* 169 */       for (byte b = 0; b < arrayList.size(); b++) {
/* 170 */         String str = arrayList.get(b);
/* 171 */         if (paramString.equals("0")) {
/* 172 */           setRegionListInfo(str, paramInt);
/* 173 */         } else if (bool) {
/* 174 */           setRegionListInfo(str, paramInt + 1);
/*     */         } 
/*     */       }  
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/crm/Maint/RegionList.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */