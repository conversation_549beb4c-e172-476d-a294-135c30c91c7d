/*    */ package weaver.crm.Maint;
/*    */ 
/*    */ import weaver.cache.CacheBase;
/*    */ import weaver.cache.CacheColumn;
/*    */ 
/*    */ 
/*    */ 
/*    */ public class TradeInfoComInfo
/*    */   extends CacheBase
/*    */ {
/*    */   public static String table;
/* 12 */   protected static String TABLE_NAME = "CRM_TradeInfo";
/* 13 */   protected static String PK_NAME = "id";
/*    */ 
/*    */   
/*    */   @CacheColumn
/*    */   protected static int id;
/*    */   
/*    */   @CacheColumn
/*    */   protected static int fullname;
/*    */   
/*    */   @CacheColumn
/*    */   protected static int rangelower;
/*    */   
/*    */   @CacheColumn
/*    */   protected static int rangeupper;
/*    */ 
/*    */   
/*    */   public int getTradeInfoNum() {
/* 30 */     return size();
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getTradeInfoid() {
/* 37 */     return (String)getRowValue(id);
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getTradeInfoname() {
/* 44 */     return (String)getRowValue(fullname);
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getTradeInfoname(String paramString) {
/* 52 */     return (String)getValue(fullname, paramString);
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getTradeInfolowamount() {
/* 59 */     return (String)getRowValue(rangelower);
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getTradeInfolowamount(String paramString) {
/* 67 */     return (String)getValue(rangelower, paramString);
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getTradeInfohighamount() {
/* 74 */     return (String)getRowValue(rangeupper);
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getTradeInfohighamount(String paramString) {
/* 82 */     return (String)getValue(rangeupper, paramString);
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public void removeTradeInfoCache() {
/* 89 */     removeCache();
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/crm/Maint/TradeInfoComInfo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */