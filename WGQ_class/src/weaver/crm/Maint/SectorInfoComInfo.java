/*     */ package weaver.crm.Maint;
/*     */ 
/*     */ import weaver.cache.CacheBase;
/*     */ import weaver.cache.CacheColumn;
/*     */ import weaver.cache.CacheColumnType;
/*     */ import weaver.cache.PKColumn;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class SectorInfoComInfo
/*     */   extends CacheBase
/*     */ {
/*  20 */   protected static String TABLE_NAME = "CRM_SectorInfo";
/*     */   @PKColumn(type = CacheColumnType.NUMBER)
/*  22 */   protected static String PK_NAME = "id";
/*     */ 
/*     */   
/*     */   @CacheColumn
/*     */   protected static int id;
/*     */   
/*     */   @CacheColumn
/*     */   protected static int fullname;
/*     */   
/*     */   @CacheColumn
/*     */   protected static int description;
/*     */   
/*     */   @CacheColumn
/*     */   protected static int parentid;
/*     */ 
/*     */   
/*     */   public int getSectorInfoNum() {
/*  39 */     return size();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getSectorInfoid() {
/*  47 */     return (String)getRowValue(id);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getSectorInfoname() {
/*  54 */     return (String)getRowValue(fullname);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getSectorInfoname(String paramString) {
/*  63 */     return (String)getValue(fullname, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getSectorFullParentid(String paramString) {
/*  73 */     String str1 = "-1";
/*  74 */     String str2 = ",";
/*  75 */     if (!paramString.equals("")) {
/*  76 */       while (!str1.equals("0")) {
/*     */         
/*  78 */         String str3 = getSectorInfoParentid(paramString);
/*  79 */         paramString = str3;
/*  80 */         if (str3.equals("0")) {
/*     */           
/*  82 */           str1 = "0"; continue;
/*     */         } 
/*  84 */         str2 = str2 + str3 + ",";
/*     */       } 
/*  86 */       String[] arrayOfString = str2.split(",");
/*  87 */       String str = ",";
/*  88 */       for (int i = arrayOfString.length - 1; i >= 0; i--) {
/*  89 */         if (!arrayOfString[i].equals(""))
/*  90 */           str = str + arrayOfString[i] + ","; 
/*  91 */       }  str2 = str;
/*     */     } else {
/*     */       
/*  94 */       str2 = "";
/*     */     } 
/*  96 */     return str2;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getSectorFullParentName(String paramString) {
/* 106 */     String[] arrayOfString = getSectorFullParentid(paramString).split(",");
/* 107 */     String str = "";
/* 108 */     for (byte b = 0; b < arrayOfString.length; b++) {
/*     */       
/* 110 */       if (!arrayOfString[b].equals(""))
/*     */       {
/* 112 */         str = str + getSectorInfoname(arrayOfString[b]) + "->";
/*     */       }
/*     */     } 
/* 115 */     if (str.length() > 3)
/* 116 */       str = str.substring(0, str.length() - 2); 
/* 117 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getSectorInfodesc() {
/* 125 */     return (String)getRowValue(description);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getSectorInfodesc(String paramString) {
/* 134 */     return (String)getValue(description, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getSectorInfoParentid() {
/* 141 */     return (String)getRowValue(parentid);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getSectorInfoParentid(String paramString) {
/* 150 */     return (String)getValue(parentid, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void removeSectorInfoCache() {
/* 157 */     removeCache();
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/crm/Maint/SectorInfoComInfo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */