/*     */ package weaver.crm.Maint;
/*     */ 
/*     */ import weaver.cache.CacheBase;
/*     */ import weaver.cache.CacheColumn;
/*     */ import weaver.cache.CacheColumnType;
/*     */ import weaver.cache.PKColumn;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CustomerTypeComInfo
/*     */   extends CacheBase
/*     */ {
/*  20 */   protected static String TABLE_NAME = "CRM_CustomerType";
/*     */   @PKColumn(type = CacheColumnType.NUMBER)
/*  22 */   protected static String PK_NAME = "id";
/*     */ 
/*     */   
/*     */   @CacheColumn
/*     */   protected static int id;
/*     */ 
/*     */   
/*     */   @CacheColumn
/*     */   protected static int fullname;
/*     */ 
/*     */   
/*     */   @CacheColumn
/*     */   protected static int description;
/*     */ 
/*     */   
/*     */   @CacheColumn
/*     */   protected static int workflowid;
/*     */ 
/*     */ 
/*     */   
/*     */   public int getCustomerTypeNum() {
/*  43 */     return size();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCustomerTypeid() {
/*  51 */     return (String)getRowValue(id);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCustomerTypename() {
/*  58 */     return (String)getRowValue(fullname);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCustomerTypename(String paramString) {
/*  67 */     return (String)getValue(fullname, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCustomerTypedesc() {
/*  74 */     return (String)getRowValue(description);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCustomerTypedesc(String paramString) {
/*  83 */     return (String)getValue(description, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getWorkFlowId() {
/*  91 */     return (String)getRowValue(workflowid);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getWorkFlowId(String paramString) {
/* 100 */     return (String)getValue(workflowid, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void removeCustomerTypeCache() {
/* 108 */     removeCache();
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/crm/Maint/CustomerTypeComInfo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */