/*    */ package weaver.crm.Maint;
/*    */ 
/*    */ import weaver.cache.CacheBase;
/*    */ import weaver.cache.CacheColumn;
/*    */ import weaver.cache.CacheColumnType;
/*    */ import weaver.cache.PKColumn;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class ContractTypeComInfo
/*    */   extends CacheBase
/*    */ {
/* 20 */   protected static String TABLE_NAME = "CRM_ContractType";
/*    */   @PKColumn(type = CacheColumnType.NUMBER)
/* 22 */   protected static String PK_NAME = "id";
/*    */ 
/*    */   
/*    */   @CacheColumn
/*    */   protected static int id;
/*    */   
/*    */   @CacheColumn
/*    */   protected static int name;
/*    */   
/*    */   @CacheColumn
/*    */   protected static int contractdesc;
/*    */ 
/*    */   
/*    */   public int getContractTypeNum() {
/* 36 */     return size();
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getContractTypeid() {
/* 45 */     return (String)getRowValue(id);
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getContractTypename() {
/* 53 */     return (String)getRowValue(name);
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getContractTypename(String paramString) {
/* 62 */     return (String)getValue(name, paramString);
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getContractTypedesc() {
/* 69 */     return (String)getRowValue(contractdesc);
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getContractTypedesc(String paramString) {
/* 78 */     return (String)getValue(contractdesc, paramString);
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public void removeContractTypeCache() {
/* 85 */     removeCache();
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/crm/Maint/ContractTypeComInfo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */