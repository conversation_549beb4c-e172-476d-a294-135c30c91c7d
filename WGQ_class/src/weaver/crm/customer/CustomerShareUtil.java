/*     */ package weaver.crm.customer;
/*     */ 
/*     */ import com.api.crm.service.CustomerService;
/*     */ import com.api.workflow.service.RequestAuthenticationService;
/*     */ import com.engine.crm.constant.LogBizTypeEnum;
/*     */ import com.engine.crm.constant.LogSmallTypeEnum;
/*     */ import com.engine.crm.constant.OperateTypeDetailEnum;
/*     */ import com.engine.crm.constant.OperateTypeEnum;
/*     */ import com.engine.crm.entity.CrmLogBean;
/*     */ import com.weaver.formmodel.util.StringHelper;
/*     */ import java.sql.SQLException;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.blog.BlogDao;
/*     */ import weaver.conn.BatchRecordSet;
/*     */ import weaver.conn.ConnStatement;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.cowork.CoworkDAO;
/*     */ import weaver.crm.CrmShareBase;
/*     */ import weaver.formmode.setup.ModeRightInfo;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.UserManager;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CustomerShareUtil
/*     */ {
/*     */   public static String getShareSql(String paramString) {
/*  41 */     CrmShareBase crmShareBase = new CrmShareBase();
/*  42 */     return crmShareBase.getTempTable(paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static void addCustomerShare2(String paramString1, String paramString2, String paramString3, String paramString4, String paramString5, String paramString6, String paramString7, String paramString8, String paramString9, String paramString10, String paramString11) {
/*  72 */     if (paramString2.equals("") || paramString4.equals("")) {
/*     */       return;
/*     */     }
/*     */     
/*  76 */     if (paramString8.equals("0")) {
/*  77 */       paramString2 = customerRightFilter(paramString1, paramString2);
/*     */     }
/*  79 */     if (paramString2.equals(""))
/*     */       return; 
/*  81 */     String[] arrayOfString1 = Util.TokenizerString2(paramString2, ",");
/*  82 */     String[] arrayOfString2 = Util.TokenizerString2(paramString4, ",");
/*     */     
/*  84 */     ArrayList<String> arrayList = new ArrayList();
/*  85 */     char c = Util.getSeparator();
/*  86 */     String str1 = "";
/*     */     
/*  88 */     RecordSet recordSet = new RecordSet();
/*  89 */     String str2 = " and scopeid = '" + paramString11 + "'";
/*  90 */     if (paramString9 != null && !"".equals(paramString9))
/*  91 */       str2 = str2 + " and jobtitleid = '" + paramString9 + "'"; 
/*  92 */     if (paramString10 != null && !"".equals(paramString10)) {
/*  93 */       str2 = str2 + " and joblevel = " + paramString10;
/*     */     }
/*     */     
/*  96 */     if ((paramString3.equals("1") || paramString3.equals("9")) && paramString7
/*  97 */       .equals("1") && !paramString2.equals("")) {
/*  98 */       String str = "delete from CRM_ShareInfo where relateditemid in(" + paramString2 + ") and sharetype=" + paramString3 + " and contents in(" + paramString4 + ") and sharelevel=1 and isdefault is null " + str2;
/*     */ 
/*     */ 
/*     */       
/* 102 */       recordSet.execute(str);
/*     */     } 
/*     */     
/* 105 */     for (String str : arrayOfString1) {
/* 106 */       if (!str.equals(""))
/*     */       {
/* 108 */         for (String str3 : arrayOfString2) {
/* 109 */           if (str3.equals("")) {
/*     */             continue;
/*     */           }
/* 112 */           if ((!paramString3.equals("1") && !paramString3.equals("9")) || 
/* 113 */             !paramString7.equals("1")) {
/*     */ 
/*     */ 
/*     */             
/* 117 */             String str6 = "select id,sharelevel from CRM_ShareInfo where relateditemid=" + str + " and sharetype=" + paramString3 + " and contents=" + str3 + " and seclevel=" + paramString5 + " and seclevelMax=" + paramString6 + " and isdefault is null" + str2;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */             
/* 128 */             recordSet.execute(str6);
/* 129 */             if (recordSet.next()) {
/* 130 */               String str7 = recordSet.getString("id");
/*     */               
/* 132 */               String str8 = recordSet.getString("sharelevel");
/* 133 */               if (Util.getIntValue(paramString7) > 
/* 134 */                 Util.getIntValue(str8)) {
/* 135 */                 str6 = "update CRM_ShareInfo set sharelevel=" + paramString7 + " where id=" + str7;
/*     */                 
/* 137 */                 recordSet.execute(str6);
/*     */               } 
/*     */               
/*     */               continue;
/*     */             } 
/*     */           } 
/* 143 */           str1 = str + c + str3 + c + paramString3 + c + paramString5 + c + paramString6 + c + paramString7 + c + str3 + c + paramString10 + c + paramString11;
/*     */ 
/*     */ 
/*     */           
/* 147 */           RecordSet recordSet1 = new RecordSet();
/* 148 */           String str4 = " seclevel = " + paramString5;
/* 149 */           if (paramString5 == null || "".equals(paramString5))
/* 150 */             str4 = " seclevel is null "; 
/* 151 */           String str5 = "select id from CRM_ShareInfo where relateditemid = " + str + " and contents = '" + str3 + "' and sharetype = " + paramString3 + " and " + str4 + " and seclevelMax = " + paramString6 + " and sharelevel = " + paramString7 + str2;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */           
/* 164 */           recordSet1.execute(str5);
/* 165 */           if (!recordSet1.next())
/* 166 */             arrayList.add(str1); 
/*     */           continue;
/*     */         } 
/*     */       }
/*     */     } 
/* 171 */     (new BatchRecordSet())
/* 172 */       .executeSqlBatch("insert into CRM_ShareInfo(relateditemid,contents,sharetype,seclevel,seclevelMax,sharelevel,jobtitleid,joblevel,scopeid) values (?,?,?,?,?,?,?,?,?)", arrayList);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static void addCustomerShare(String paramString1, String paramString2, String paramString3, String paramString4, String paramString5, String paramString6, String paramString7, String paramString8, String paramString9, String paramString10, String paramString11, String paramString12) {
/* 202 */     if (paramString2.equals("") || paramString4.equals("")) {
/*     */       return;
/*     */     }
/* 205 */     if (paramString2.equals("")) {
/*     */       return;
/*     */     }
/* 208 */     if (paramString12 != null && 
/* 209 */       "".equals(paramString12)) {
/* 210 */       paramString12 = null;
/*     */     }
/*     */     
/* 213 */     String[] arrayOfString1 = Util.TokenizerString2(paramString2, ",");
/* 214 */     String[] arrayOfString2 = Util.TokenizerString2(paramString4, ",");
/*     */     
/* 216 */     CustomerService customerService = new CustomerService();
/* 217 */     char c = Util.getSeparator();
/*     */ 
/*     */     
/* 220 */     ConnStatement connStatement = new ConnStatement();
/* 221 */     String str = "insert into CRM_ShareInfo(relateditemid,contents,sharetype,seclevel,seclevelMax,sharelevel,jobtitleid,joblevel,scopeid,rolelevel) values (?,?,?,?,?,?,?,?,?,?)";
/*     */ 
/*     */     
/*     */     try {
/* 225 */       connStatement.setStatementSql(str);
/* 226 */       for (String str1 : arrayOfString1) {
/* 227 */         if (!str1.equals(""))
/*     */         {
/* 229 */           for (String str2 : arrayOfString2) {
/* 230 */             if (!str2.equals("")) {
/*     */ 
/*     */               
/* 233 */               boolean bool = customerService.isShared(str1, str2, paramString3, paramString7, paramString5, paramString6, paramString12, paramString10, paramString11);
/*     */               
/* 235 */               if (!bool)
/*     */                 try {
/* 237 */                   connStatement.setString(1, str1);
/* 238 */                   connStatement.setString(2, str2);
/* 239 */                   connStatement.setString(3, paramString3);
/* 240 */                   connStatement.setString(4, paramString5);
/* 241 */                   connStatement.setString(5, paramString6);
/* 242 */                   connStatement.setString(6, paramString7);
/* 243 */                   connStatement.setString(7, Util.null2o(paramString9));
/* 244 */                   connStatement.setString(8, Util.null2o(paramString10));
/* 245 */                   connStatement.setString(9, Util.null2o(paramString11));
/* 246 */                   connStatement.setString(10, paramString12);
/* 247 */                   connStatement.executeUpdate();
/* 248 */                 } catch (Exception exception) {
/* 249 */                   String str3 = str1 + c + str2 + c + paramString3 + c + paramString5 + c + paramString6 + c + paramString7 + c + str2 + c + paramString10 + c + paramString11;
/*     */                   
/* 251 */                   (new BaseBean()).writeLog("CustomerShareUtil paras is ==" + str3);
/* 252 */                   (new BaseBean()).writeLog(exception);
/*     */                 }  
/*     */             } 
/*     */           }  } 
/*     */       } 
/* 257 */     } catch (SQLException sQLException) {
/* 258 */       (new BaseBean()).writeLog(sQLException);
/*     */     } finally {
/* 260 */       connStatement.close();
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static void addCustomerShare(String paramString1, String paramString2, String paramString3, String paramString4, String paramString5, String paramString6, String paramString7, String paramString8, String paramString9, String paramString10, String paramString11) {
/* 406 */     addCustomerShare(paramString1, paramString2, paramString3, paramString4, paramString5, paramString6, paramString7, paramString8, paramString9, paramString10, paramString11, "");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static void addCustomerShare(String paramString1, String paramString2, String paramString3, String paramString4, String paramString5, String paramString6, String paramString7) {
/* 552 */     addCustomerShare(paramString1, paramString2, paramString3, paramString4, paramString5, paramString6, paramString7, "0", "", "", "", "", "EM");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static void addCustomerShare(String paramString1, String paramString2, String paramString3, String paramString4, String paramString5, String paramString6, String paramString7, String paramString8) {
/* 559 */     addCustomerShare(paramString1, paramString2, paramString3, paramString4, paramString5, paramString6, paramString7, paramString8, "", "", "", "", "WorkFlow");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static void addCustomerShare(String paramString1, String paramString2, String paramString3, String paramString4, String paramString5, String paramString6, String paramString7, String paramString8, String paramString9, String paramString10, String paramString11, String paramString12, String paramString13) {
/* 593 */     if ("9".equals(paramString3)) {
/*     */       return;
/*     */     }
/* 596 */     String[] arrayOfString = Util.TokenizerString2(paramString2, ",");
/* 597 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 598 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 599 */     CrmLogBean crmLogBean = new CrmLogBean();
/* 600 */     int i = OperateTypeDetailEnum.GENERAL.getOperateTypeDetail();
/* 601 */     if ("EM".equals(paramString13)) {
/* 602 */       i = OperateTypeDetailEnum.EM.getOperateTypeDetail();
/*     */     }
/* 604 */     if ("WorkFlow".equals(paramString13)) {
/* 605 */       i = OperateTypeDetailEnum.WORKFLOW.getOperateTypeDetail();
/*     */     }
/* 607 */     User user = new User(Integer.parseInt(paramString1));
/* 608 */     if ("".equals(paramString7)) {
/* 609 */       paramString7 = "1";
/*     */     }
/* 611 */     String str = "1".equals(paramString7) ? SystemEnv.getHtmlLabelName(367, user.getLanguage()) : SystemEnv.getHtmlLabelName(93, user.getLanguage());
/* 612 */     for (String str1 : arrayOfString) {
/* 613 */       if (!"".equals(paramString3) && !"".equals(paramString7)) {
/*     */         
/* 615 */         hashMap1.put(SystemEnv.getHtmlLabelName(21956, user.getLanguage()), Util.null2String(crmLogBean.getShareType(paramString3)));
/*     */         
/* 617 */         hashMap1.put(SystemEnv.getHtmlLabelName(3005, user.getLanguage()), str);
/*     */       } 
/*     */       
/* 620 */       String str2 = "";
/* 621 */       if (!"".equals(Util.null2String(paramString4))) {
/* 622 */         String[] arrayOfString1 = Util.splitString(paramString4, ",");
/* 623 */         String str3 = "";
/* 624 */         String str4 = "";
/* 625 */         String str5 = "";
/* 626 */         String str6 = "";
/* 627 */         String str7 = "";
/* 628 */         String str8 = "";
/*     */         
/* 630 */         for (String str9 : arrayOfString1) {
/* 631 */           if ("1".equals(paramString3)) {
/* 632 */             str3 = str3 + crmLogBean.getDeleteInfos(paramString3, str9, user) + ",";
/* 633 */             hashMap1.put(SystemEnv.getHtmlLabelName(19117, user.getLanguage()), str3.substring(0, str3.length() - 1));
/*     */           } 
/* 635 */           if ("2".equals(paramString3)) {
/* 636 */             str4 = str4 + crmLogBean.getDeleteInfos(paramString3, str9, user) + ",";
/* 637 */             hashMap1.put(SystemEnv.getHtmlLabelName(19117, user.getLanguage()), str4.substring(0, str4.length() - 1));
/*     */           } 
/* 639 */           if ("3".equals(paramString3)) {
/* 640 */             str5 = str5 + crmLogBean.getDeleteInfos(paramString3, str9, user) + ",";
/* 641 */             hashMap1.put(SystemEnv.getHtmlLabelName(19117, user.getLanguage()), str5.substring(0, str5.length() - 1));
/*     */           } 
/* 643 */           if ("4".equals(paramString3)) {
/* 644 */             str6 = str6 + crmLogBean.getDeleteInfos(paramString3, str9, user) + ",";
/* 645 */             hashMap1.put(SystemEnv.getHtmlLabelName(19117, user.getLanguage()), str6.substring(0, str6.length() - 1));
/*     */           } 
/* 647 */           if ("5".equals(paramString3)) {
/* 648 */             str7 = str7 + crmLogBean.getDeleteInfos(paramString3, str9, user) + ",";
/* 649 */             hashMap1.put(SystemEnv.getHtmlLabelName(19117, user.getLanguage()), str7.substring(0, str7.length() - 1));
/*     */           } 
/* 651 */           if ("6".equals(paramString3)) {
/* 652 */             str8 = str8 + crmLogBean.getDeleteInfos(paramString3, str9, user) + ",";
/* 653 */             hashMap1.put(SystemEnv.getHtmlLabelName(19117, user.getLanguage()), str8.substring(0, str8.length() - 1));
/*     */           } 
/* 655 */           if ("9".equals(paramString3)) {
/* 656 */             str2 = str2 + crmLogBean.getDeleteInfos(paramString3, str9, user) + ",";
/* 657 */             hashMap1.put(SystemEnv.getHtmlLabelName(19117, user.getLanguage()), str2.substring(0, str2.length() - 1));
/*     */           } 
/*     */         } 
/*     */       } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 667 */       hashMap2.putAll(hashMap1);
/* 668 */       hashMap2.remove("param_ip");
/* 669 */       crmLogBean.crmModifyLog(hashMap2, LogBizTypeEnum.CUSTOEMR
/* 670 */           .getLogBizType(), Integer.parseInt(str1), LogSmallTypeEnum.RIGHT
/* 671 */           .getLogSmallType(), Integer.parseInt(str1), OperateTypeEnum.ADD
/* 672 */           .getOperateType(), i, null, hashMap1, "", str1, user);
/*     */ 
/*     */       
/* 675 */       crmLogBean.createLog();
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 681 */     addCustomerShare(paramString1, paramString2, paramString3, paramString4, paramString5, paramString6, paramString7, paramString8, paramString9, paramString10, paramString11, paramString12);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static void addCustomerShare(String paramString1, String paramString2, String paramString3, String paramString4, int paramInt, String paramString5) {
/* 707 */     String[] arrayOfString1 = Util.TokenizerString2(paramString3, ",");
/* 708 */     String[] arrayOfString2 = Util.TokenizerString2(paramString4, ",");
/*     */     
/* 710 */     String str1 = "";
/* 711 */     String str2 = "";
/*     */     
/* 713 */     for (byte b = 0; b < arrayOfString1.length; b++) {
/* 714 */       if (arrayOfString1[b].equals("0")) {
/* 715 */         str1 = str1 + "," + arrayOfString2[b];
/* 716 */       } else if (arrayOfString1[b].equals("1")) {
/* 717 */         str2 = str2 + "," + arrayOfString2[b];
/*     */       } 
/*     */     } 
/* 720 */     str1 = (str1.length() > 0) ? str1.substring(1) : str1;
/* 721 */     str2 = (str2.length() > 0) ? str2.substring(1) : str2;
/*     */ 
/*     */     
/* 724 */     addCustomerShare(paramString1, paramString2, "1", str1, "0", "0", "" + paramInt, paramString5);
/*     */     
/* 726 */     addCustomerShare(paramString1, paramString2, "9", str2, "0", "0", "" + paramInt, paramString5);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static void addCustomerShare(String paramString1, String paramString2, String paramString3, String paramString4, int paramInt) {
/* 741 */     (new BaseBean())
/* 742 */       .writeLog("CustomerShareUtil: paras from workflow is #[userid] == " + paramString1 + "[crmids] == " + paramString2 + "[usertypes] == " + paramString3 + "[resourceids] == " + paramString4 + "[sharelevel] == " + paramInt);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 751 */     addCustomerShare(paramString1, paramString2, paramString3, paramString4, paramInt, "0");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String customerRightFilter(String paramString1, String paramString2) {
/* 766 */     if (paramString2.equals("") || paramString1.equals(paramString2)) {
/* 767 */       return paramString2;
/*     */     }
/* 769 */     CrmShareBase crmShareBase = new CrmShareBase();
/* 770 */     String str1 = crmShareBase.getTempTable(paramString1);
/*     */ 
/*     */     
/* 773 */     String str2 = Util.getSubINClause(paramString2, "relateditemid", "in");
/*     */     
/* 775 */     String str3 = "select relateditemid from " + str1 + "t where " + str2;
/* 776 */     String str4 = "";
/* 777 */     RecordSet recordSet = new RecordSet();
/* 778 */     recordSet.execute(str3);
/* 779 */     while (recordSet.next()) {
/* 780 */       str4 = str4 + "," + recordSet.getString("relateditemid");
/*     */     }
/* 782 */     str4 = (str4.length() > 0) ? str4.substring(1) : str4;
/* 783 */     if (!str4.equals(paramString2))
/*     */     {
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 799 */       (new BaseBean()).writeLog("customerRightFilter--crmids--" + paramString2 + "--result--" + str4 + "--sql--" + str3);
/*     */     }
/* 801 */     return paramString2;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean customerRightCheck(String paramString1, String paramString2, String paramString3, Map<String, String> paramMap) {
/* 820 */     if (paramString3.equals("cowork")) {
/* 821 */       String str = paramMap.get("coworkid");
/* 822 */       CoworkDAO coworkDAO = new CoworkDAO();
/* 823 */       return coworkDAO.haveRightToViewCustomer(paramString1, paramString2, str);
/* 824 */     }  if (paramString3.equals("blog")) {
/* 825 */       BlogDao blogDao = new BlogDao();
/* 826 */       return blogDao.blogAppRightCheck(paramString1, paramMap);
/* 827 */     }  if (paramString3.equals("workflow")) {
/* 828 */       int i = Util.getIntValue(paramMap.get("wfRequestId"));
/* 829 */       RequestAuthenticationService requestAuthenticationService = new RequestAuthenticationService();
/* 830 */       return requestAuthenticationService.verifyCustomRight(Util.getIntValue(paramString1), Util.getIntValue(paramString2), i);
/* 831 */     }  if (!paramString3.equals("doc"))
/*     */     {
/* 833 */       if (paramString3.equals("formmode")) {
/*     */         
/* 835 */         String str = StringHelper.null2String(paramMap
/* 836 */             .get("formmode_authorize"));
/* 837 */         if (str.equals("formmode_authorize")) {
/* 838 */           int i = 0;
/* 839 */           int j = 0;
/* 840 */           int k = 0;
/* 841 */           int m = 0;
/* 842 */           i = Util.getIntValue(paramMap.get("authorizemodeId"), 0);
/* 843 */           j = Util.getIntValue(paramMap
/* 844 */               .get("authorizeformmodebillId"), 0);
/* 845 */           k = Util.getIntValue(paramMap.get("authorizefieldid"), 0);
/* 846 */           m = Util.getIntValue(paramMap
/* 847 */               .get("authorizeformModeReplyid"), 0);
/* 848 */           String str1 = Util.null2String(paramMap
/* 849 */               .get("authorizefMReplyFName"));
/* 850 */           ModeRightInfo modeRightInfo = new ModeRightInfo();
/* 851 */           UserManager userManager = new UserManager();
/* 852 */           User user = userManager.getUserByUserIdAndLoginType(
/* 853 */               Util.getIntValue(paramString1), "1");
/* 854 */           if (StringHelper.isEmpty(user.getLoginid())) {
/* 855 */             user = userManager.getUserByUserIdAndLoginType(
/* 856 */                 Util.getIntValue(paramString1), "2");
/*     */           }
/* 858 */           modeRightInfo.setUser(user);
/* 859 */           Map map = new HashMap<>();
/* 860 */           if (m != 0) {
/* 861 */             Map map1 = modeRightInfo.isFormModeAuthorize(str, i, j, k, 
/*     */                 
/* 863 */                 Util.getIntValue(paramString2), m, str1);
/*     */           } else {
/*     */             
/* 866 */             map = modeRightInfo.isFormModeAuthorize(str, i, j, k, 
/*     */                 
/* 868 */                 Util.getIntValue(paramString2));
/*     */           } 
/* 870 */           return "1".equals(map.get("AuthorizeFlag"));
/*     */         } 
/* 872 */       } else if ("em".equals(paramString3) || "1".equals(paramMap.get("isfromchatshare"))) {
/*     */         
/* 874 */         String str1 = Util.null2String(paramMap.get("resourcetype"));
/* 875 */         String str2 = Util.null2String(paramMap.get("resourceid"));
/* 876 */         String str3 = Util.null2String(paramMap.get("em_auth_userid"));
/* 877 */         RecordSet recordSet = new RecordSet();
/* 878 */         recordSet.executeQuery("select 1 from social_IMChatResourceShare where resourcetype = ? and resourceid = ? and userid =? ", new Object[] { str1, str2, str3 });
/* 879 */         if (recordSet.next()) {
/* 880 */           return true;
/*     */         }
/*     */       } 
/*     */     }
/* 884 */     return false;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/crm/customer/CustomerShareUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */