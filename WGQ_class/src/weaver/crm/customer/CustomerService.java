/*     */ package weaver.crm.customer;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.crm.CrmShareBase;
/*     */ import weaver.crm.investigate.ContacterComInfo;
/*     */ import weaver.general.GCONST;
/*     */ import weaver.general.Util;
/*     */ 
/*     */ public class CustomerService
/*     */ {
/*  15 */   RecordSet recordSet = new RecordSet();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<Map<String, String>> getContacterList(String paramString) {
/*  24 */     if (paramString.equals("")) return new ArrayList<>();
/*     */     
/*  26 */     String str = "SELECT id,firstname,title,phoneoffice,mobilephone,email,jobtitle,department,imcode FROM CRM_CustomerContacter where customerid=" + paramString + " order by id desc";
/*  27 */     return getContacter(str);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map getContacterByid(String paramString) {
/*  34 */     String str = "SELECT id,firstname,title,phoneoffice,mobilephone,email,jobtitle,department,imcode FROM CRM_CustomerContacter where id=" + paramString;
/*  35 */     List<Map<String, String>> list = getContacter(str);
/*     */     
/*  37 */     Map map = new HashMap<>();
/*     */     
/*  39 */     if (list.size() > 0) {
/*  40 */       map = list.get(0);
/*     */     }
/*  42 */     return map;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<Map<String, String>> getContacter(String paramString) {
/*  52 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*     */     
/*  54 */     this.recordSet.execute(paramString);
/*  55 */     while (this.recordSet.next()) {
/*     */       
/*  57 */       HashMap<Object, Object> hashMap = new HashMap<>();
/*     */       
/*  59 */       String str1 = this.recordSet.getString("id");
/*  60 */       String str2 = this.recordSet.getString("firstname");
/*  61 */       String str3 = this.recordSet.getString("title");
/*  62 */       String str4 = this.recordSet.getString("jobtitle");
/*  63 */       String str5 = this.recordSet.getString("department");
/*  64 */       String str6 = this.recordSet.getString("mobilephone");
/*  65 */       String str7 = this.recordSet.getString("phoneoffice");
/*  66 */       String str8 = this.recordSet.getString("email");
/*  67 */       String str9 = this.recordSet.getString("imcode");
/*     */       
/*  69 */       hashMap.put("contacterid", str1);
/*  70 */       hashMap.put("firstname", str2);
/*  71 */       hashMap.put("title", str3);
/*  72 */       hashMap.put("jobtitle", str4);
/*  73 */       hashMap.put("department", str5);
/*  74 */       hashMap.put("mobilephone", str6);
/*  75 */       hashMap.put("phoneoffice", str7);
/*  76 */       hashMap.put("email", str8);
/*  77 */       hashMap.put("imcode", str9);
/*     */       
/*  79 */       arrayList.add(hashMap);
/*     */     } 
/*     */     
/*  82 */     return (List)arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getContacterName(String paramString) {
/*  90 */     String str = "";
/*     */     try {
/*  92 */       ContacterComInfo contacterComInfo = new ContacterComInfo();
/*  93 */       ArrayList<String> arrayList = Util.TokenizerString(paramString, ",");
/*  94 */       for (byte b = 0; b < arrayList.size(); b++) {
/*  95 */         if (!"".equals(arrayList.get(b))) {
/*  96 */           String str1 = " select firstname from  CRM_CustomerContacter where id = " + arrayList.get(b);
/*  97 */           RecordSet recordSet = new RecordSet();
/*  98 */           recordSet.execute(str1);
/*  99 */           String str2 = "";
/* 100 */           if (recordSet.next()) str2 = recordSet.getString("firstname"); 
/* 101 */           str = str + "<a href=javaScript:openFullWindowHaveBar('" + GCONST.getContextPath() + "/CRM/contacter/ContacterView.jsp?ContacterID=" + arrayList.get(b) + "')>" + str2 + "</a> ";
/*     */         } 
/*     */       } 
/* 104 */     } catch (Exception exception) {}
/*     */     
/* 106 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<Map<String, String>> getSellChanceList(String paramString1, String paramString2) {
/* 116 */     if (paramString1.equals("")) return new ArrayList<>();
/*     */     
/* 118 */     CrmShareBase crmShareBase = new CrmShareBase();
/* 119 */     String str1 = crmShareBase.getTempTable("" + paramString2);
/* 120 */     String str2 = "t1.id,t1.subject,t1.predate,t1.preyield,t1.probability,t1.sellstatusid,t1.createdate,t1.createtime,t1.endtatusid,t1.CustomerID,defactor,sufactor ";
/* 121 */     String str3 = " CRM_SellChance  t1," + str1 + " t2,CRM_CustomerInfo t3 ";
/* 122 */     String str4 = " t3.deleted=0 and t3.id= t1.customerid and t1.customerid = t2.relateditemid and customerid=" + paramString1;
/* 123 */     String str5 = "SELECT " + str2 + " from " + str3 + " where " + str4;
/*     */     
/* 125 */     return getSellChance(str5);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List getSellChanceList(String paramString) {
/* 136 */     String str = "select id,subject,predate,preyield,probability,sellstatusid,createdate,createtime,endtatusid,CustomerID,defactor,sufactor from CRM_SellChance where id in(" + paramString + ")";
/*     */     
/* 138 */     return getSellChance(str);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map getSellChanceByid(String paramString) {
/* 149 */     Map map = new HashMap<>();
/* 150 */     if ("".equals(paramString) || "0".equals(paramString)) return map; 
/* 151 */     List<Map> list = getSellChanceList(paramString);
/*     */     
/* 153 */     if (list.size() > 0) {
/* 154 */       map = list.get(0);
/*     */     }
/* 156 */     return map;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getSellChanceName(String paramString) {
/* 164 */     String str = "";
/* 165 */     List<Map> list = getSellChanceList(paramString);
/* 166 */     for (byte b = 0; b < list.size(); b++) {
/* 167 */       Map map = list.get(b);
/* 168 */       str = str + "<a href=javaScript:openFullWindowHaveBar('" + GCONST.getContextPath() + "/CRM/sellchance/ViewSellChanceTab.jsp?id=" + map.get("sellchanceid") + "')>" + map.get("subject") + "</a> ";
/*     */     } 
/* 170 */     return str;
/*     */   }
/*     */ 
/*     */   
/*     */   public List getSellChance(String paramString) {
/* 175 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 176 */     this.recordSet.execute(paramString);
/* 177 */     while (this.recordSet.next()) {
/*     */       
/* 179 */       HashMap<Object, Object> hashMap = new HashMap<>();
/*     */       
/* 181 */       String str1 = this.recordSet.getString("id");
/* 182 */       String str2 = this.recordSet.getString("subject");
/* 183 */       String str3 = this.recordSet.getString("predate");
/* 184 */       String str4 = this.recordSet.getString("preyield");
/* 185 */       String str5 = this.recordSet.getString("probability");
/* 186 */       String str6 = this.recordSet.getString("createdate");
/* 187 */       String str7 = this.recordSet.getString("sellstatusid");
/* 188 */       String str8 = this.recordSet.getString("endtatusid");
/* 189 */       String str9 = this.recordSet.getString("defactor");
/* 190 */       String str10 = this.recordSet.getString("sufactor");
/*     */       
/* 192 */       hashMap.put("sellchanceid", str1);
/* 193 */       hashMap.put("subject", str2);
/* 194 */       hashMap.put("predate", str3);
/* 195 */       hashMap.put("preyield", str4);
/* 196 */       hashMap.put("probability", str5);
/* 197 */       hashMap.put("createdate", str6);
/* 198 */       hashMap.put("sellstatusid", str7);
/* 199 */       hashMap.put("endtatusid", str8);
/* 200 */       hashMap.put("defactor", str9);
/* 201 */       hashMap.put("sufactor", str10);
/*     */       
/* 203 */       arrayList.add(hashMap);
/*     */     } 
/*     */     
/* 206 */     return arrayList;
/*     */   }
/*     */   
/*     */   public String getSubResourceid(String paramString) {
/* 210 */     String str1 = "";
/* 211 */     String str2 = "select id from HrmResource hrm where hrm.managerstr like '%," + paramString + ",%' and status in(0,1,2,3)";
/* 212 */     this.recordSet.execute(str2);
/* 213 */     while (this.recordSet.next()) {
/* 214 */       str1 = str1 + "," + this.recordSet.getString("id");
/*     */     }
/* 216 */     str1 = (str1.length() > 0) ? str1.substring(1) : "";
/*     */     
/* 218 */     return str1;
/*     */   }
/*     */   
/*     */   public void markImportant(String paramString1, String paramString2, String paramString3) {
/* 222 */     String str = "insert into CRM_Attention(resourceid,customerid) values(" + paramString2 + "," + paramString1 + ")";
/* 223 */     if (paramString3.equals("1"))
/* 224 */       str = "delete from CRM_Attention where resourceid=" + paramString2 + " and customerid=" + paramString1; 
/* 225 */     this.recordSet.execute(str);
/*     */   }
/*     */ 
/*     */   
/*     */   public void markAsImportant(String paramString1, String paramString2, String paramString3) {
/* 230 */     String str = "delete from CRM_Attention where resourceid=" + paramString2 + " and customerid in(" + paramString1 + ")";
/* 231 */     this.recordSet.execute(str);
/*     */     
/* 233 */     if (paramString3.equals("1")) {
/* 234 */       String[] arrayOfString = Util.TokenizerString2(paramString1, ",");
/* 235 */       for (byte b = 0; b < arrayOfString.length; b++) {
/* 236 */         str = "insert into CRM_Attention(resourceid,customerid) values(" + paramString2 + "," + arrayOfString[b] + ")";
/* 237 */         this.recordSet.execute(str);
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   public int getBirthdayCount(String paramString, int paramInt) {
/* 244 */     int i = 0;
/*     */     
/* 246 */     CrmShareBase crmShareBase = new CrmShareBase();
/* 247 */     String str1 = crmShareBase.getTempTable(paramString);
/*     */     
/* 249 */     String str2 = "select id as cutomerid,manager,name as customerName from CRM_CustomerInfo t1 left join " + str1 + " t2 on t1.id = t2.relateditemid ";
/* 250 */     String str3 = " where t1.deleted = 0  and t1.id = t2.relateditemid ";
/*     */     
/* 252 */     String str4 = " CRM_CustomerContacter t1,(" + str2 + str3 + ") t2";
/* 253 */     String str5 = " t1.customerid=t2.cutomerid and birthday is not null ";
/*     */     
/* 255 */     if (this.recordSet.getDBType().equals("oracle")) {
/* 256 */       str5 = str5 + " and substr(birthday,6,2) = '" + ((paramInt < 10) ? "0" : "") + paramInt + "' ";
/*     */     } else {
/* 258 */       str5 = str5 + " and substring(birthday,6,2) = '" + ((paramInt < 10) ? "0" : "") + paramInt + "' and birthday <> '' ";
/*     */     } 
/*     */     
/* 261 */     String str6 = "select count(*) as total from " + str4 + " where " + str5;
/*     */     
/* 263 */     this.recordSet.execute(str6);
/* 264 */     if (this.recordSet.next()) {
/* 265 */       i = this.recordSet.getInt("total");
/* 266 */       if (i == -1) i = 0;
/*     */     
/*     */     } 
/* 269 */     return i;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, String> getFieldValues(String paramString1, String paramString2) {
/* 280 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 281 */     String str = "select " + paramString1 + " from CRM_CustomerInfo where id=" + paramString2;
/* 282 */     this.recordSet.execute(str);
/* 283 */     if (this.recordSet.next()) {
/* 284 */       String[] arrayOfString = Util.TokenizerString2(paramString1, ",");
/* 285 */       for (byte b = 0; b < arrayOfString.length; b++) {
/* 286 */         hashMap.put(arrayOfString[b], this.recordSet.getString(arrayOfString[b]));
/*     */       }
/*     */     } 
/*     */     
/* 290 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getFieldValue(String paramString1, String paramString2) {
/* 300 */     String str1 = "";
/* 301 */     String str2 = "select " + paramString1 + " from CRM_CustomerInfo where id=" + paramString2;
/* 302 */     this.recordSet.execute(str2);
/* 303 */     if (this.recordSet.next())
/* 304 */       str1 = this.recordSet.getString(paramString1); 
/* 305 */     return str1;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/crm/customer/CustomerService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */