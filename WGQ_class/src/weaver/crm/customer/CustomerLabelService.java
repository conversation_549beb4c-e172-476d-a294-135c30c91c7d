/*     */ package weaver.crm.customer;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.List;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.file.FileUpload;
/*     */ import weaver.general.TimeUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CustomerLabelService
/*     */ {
/*     */   public boolean addItemLabels(String paramString1, String paramString2, String paramString3) {
/*  23 */     boolean bool = false;
/*     */     
/*  25 */     RecordSet recordSet = new RecordSet();
/*     */     
/*  27 */     if (!"".equals(paramString3)) {
/*  28 */       ArrayList<String> arrayList = Util.TokenizerString(paramString3, ",");
/*     */       
/*  30 */       String str = "delete from CRM_customer_label where customerid=" + paramString1 + " and labelid in(select id from CRM_label where userid=" + paramString2 + ") and labelid not in(" + paramString3 + ")";
/*  31 */       recordSet.execute(str);
/*  32 */       for (byte b = 0; b < arrayList.size(); b++) {
/*  33 */         recordSet.execute("select id from CRM_customer_label where customerid='" + paramString1 + "' and labelid='" + arrayList.get(b) + "'");
/*  34 */         if (!recordSet.next()) {
/*  35 */           bool = recordSet.execute("insert into CRM_customer_label (customerid,labelid) values ('" + paramString1 + "','" + arrayList.get(b) + "')");
/*     */         } else {
/*  37 */           bool = true;
/*     */         } 
/*     */       } 
/*     */     } else {
/*  41 */       recordSet.execute("delete from CRM_customer_label where labelid in (select id from CRM_label where userid=" + paramString2 + " and customerid=" + paramString1 + ")");
/*     */     } 
/*     */     
/*  44 */     return bool;
/*     */   }
/*     */   public boolean cancelItemLabels(String paramString1, String paramString2, String paramString3) {
/*  47 */     boolean bool = false;
/*  48 */     RecordSet recordSet = new RecordSet();
/*     */     
/*  50 */     if (!"".equals(paramString3)) {
/*  51 */       bool = recordSet.execute("delete from CRM_customer_label where customerid = '" + paramString1 + "' " + " and labelid in (" + paramString3 + ")");
/*     */     }
/*     */ 
/*     */     
/*  55 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean createLabel(String paramString, FileUpload paramFileUpload) {
/*  65 */     boolean bool = true;
/*  66 */     RecordSet recordSet = new RecordSet();
/*     */     
/*  68 */     String str1 = paramFileUpload.getParameter("id");
/*  69 */     String str2 = paramFileUpload.getParameter("name");
/*  70 */     String str3 = paramFileUpload.getParameter("labelColor");
/*  71 */     String str4 = paramFileUpload.getParameter("textColor");
/*  72 */     String str5 = paramFileUpload.getParameter("isUsed_" + str1);
/*  73 */     str5 = str5.equals("1") ? "1" : "0";
/*  74 */     int i = 0;
/*     */     
/*  76 */     String str6 = "select max(labelOrder) as maxOrder from CRM_label where userid=" + paramString;
/*  77 */     recordSet.execute(str6);
/*  78 */     if (recordSet.next()) {
/*  79 */       i = recordSet.getInt("maxOrder") + 1;
/*     */     }
/*  81 */     bool = recordSet.execute("insert into CRM_label (userid,name,labelColor,createdate,createtime,isUsed,labelOrder,textColor,labelType) values(" + paramString + ",'" + str2 + "','" + str3 + "','" + TimeUtil.getCurrentDateString() + "','" + TimeUtil.getOnlyCurrentTimeString() + "'," + str5 + "," + i + ",'" + str4 + "','label')");
/*  82 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean isExistLabel(String paramString1, String paramString2) {
/*  92 */     boolean bool = false;
/*     */     
/*  94 */     RecordSet recordSet = new RecordSet();
/*  95 */     recordSet.execute("select id from CRM_label where userid='" + paramString1 + "' and name='" + paramString2 + "'");
/*  96 */     if (recordSet.next()) {
/*  97 */       bool = true;
/*     */     } else {
/*  99 */       bool = false;
/*     */     } 
/* 101 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean updateLabel(FileUpload paramFileUpload) {
/* 113 */     boolean bool = true;
/* 114 */     RecordSet recordSet = new RecordSet();
/* 115 */     String[] arrayOfString1 = paramFileUpload.getParameters("id");
/* 116 */     String[] arrayOfString2 = paramFileUpload.getParameters("labelType");
/* 117 */     String[] arrayOfString3 = paramFileUpload.getParameters("labelColor");
/* 118 */     String[] arrayOfString4 = paramFileUpload.getParameters("textColor");
/* 119 */     if (arrayOfString1 == null) {
/* 120 */       return recordSet.execute("delete from CRM_label");
/*     */     }
/* 122 */     String[] arrayOfString5 = new String[arrayOfString1.length];
/* 123 */     for (byte b1 = 0; b1 < arrayOfString1.length; b1++) {
/* 124 */       String str = paramFileUpload.getParameter("isUsed_" + arrayOfString1[b1]);
/* 125 */       str = str.equals("1") ? "1" : "0";
/* 126 */       arrayOfString5[b1] = str;
/*     */     } 
/* 128 */     String[] arrayOfString6 = paramFileUpload.getParameters("name");
/*     */     
/* 130 */     String[] arrayOfString7 = paramFileUpload.getParameters("labelOrder");
/* 131 */     for (byte b2 = 0; b2 < arrayOfString1.length; b2++) {
/* 132 */       String str1 = Util.null2String(arrayOfString1[b2]);
/* 133 */       String str2 = Util.null2String(arrayOfString6[b2]);
/* 134 */       String str3 = Util.null2String(arrayOfString5[b2]);
/* 135 */       str3 = str3.equals("1") ? "1" : "0";
/* 136 */       String str4 = Util.null2String(arrayOfString7[b2]);
/* 137 */       String str5 = Util.null2String(arrayOfString3[b2]);
/* 138 */       String str6 = Util.null2String(arrayOfString4[b2]);
/*     */       
/* 140 */       bool = recordSet.execute("update CRM_label set name='" + str2 + "',isUsed=" + str3 + ",labelOrder=" + str4 + ",labelColor='" + str5 + "',textColor='" + str6 + "' where id=" + str1);
/*     */     } 
/* 142 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean deleteLabel(String paramString) {
/* 153 */     boolean bool = true;
/*     */     
/* 155 */     RecordSet recordSet = new RecordSet();
/*     */     
/* 157 */     bool = recordSet.execute("delete from CRM_label where id='" + paramString + "'");
/* 158 */     if (bool) {
/* 159 */       bool = recordSet.execute("delete from cowork_item_label where labelid='" + paramString + "'");
/*     */     }
/* 161 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean deleteItemLabel(String paramString) {
/* 170 */     boolean bool = true;
/* 171 */     RecordSet recordSet = new RecordSet();
/* 172 */     bool = recordSet.execute("delete from cowork_item_label where id='" + paramString + "'");
/* 173 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getUserLabels(String paramString, int paramInt) {
/* 183 */     String str = "";
/* 184 */     RecordSet recordSet = new RecordSet();
/*     */     
/* 186 */     recordSet.execute("select id,name,labelColor,textColor from CRM_label where userid=" + paramString + " and labelType='label' order by labelOrder ");
/* 187 */     while (recordSet.next()) {
/* 188 */       str = str + "<div class='row' onmouseover=\"this.className='rowOver'\" onmouseout=\"this.className='row'\"><div class='check' labelid='" + recordSet.getString("id") + "' labelColor='" + recordSet.getString("labelColor") + "' textColor='" + recordSet.getString("textColor") + "'></div><div class='title' onclick='jQuery(this).prev().click()' labelid=\"" + recordSet.getString("id") + "\" labelColor='" + recordSet.getString("labelColor") + "' textColor='" + recordSet.getString("textColor") + "'>" + recordSet.getString("name") + "</div>" + "</div>";
/*     */     }
/*     */ 
/*     */     
/* 192 */     str = str + "<div class=\"row\" style='position:relative'><div id='apply' action=\"applyLabels(this)\" class='operate' onmouseover=\"this.className='operateOver'\" onmouseout=\"this.className='operate'\"  style='float:left;margin-left:20px;'>" + SystemEnv.getHtmlLabelName(25432, paramInt) + "</div>" + "<div id='manage' action=\"openLabelSet()\" class='operate' onmouseover=\"this.className='operateOver'\" onmouseout=\"this.className='operate'\" style='float:right;margin-right:20px;'>" + SystemEnv.getHtmlLabelName(22250, paramInt) + "</div>" + "</div>";
/*     */ 
/*     */ 
/*     */     
/* 196 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getUserLabelsForManage(String paramString, int paramInt) {
/* 207 */     String str = "";
/* 208 */     RecordSet recordSet = new RecordSet();
/*     */     
/* 210 */     recordSet.execute("select id,name,icon from CRM_label where userid='" + paramString + "'");
/* 211 */     str = str + "<table width=250px>";
/* 212 */     str = str + "<tr><td width=200> <span id='create' class='createLabel'>" + SystemEnv.getHtmlLabelName(25431, paramInt) + "</span></td>" + "<td></td>" + "</tr>";
/*     */ 
/*     */ 
/*     */     
/* 216 */     while (recordSet.next()) {
/* 217 */       str = str + "<tr><td width=200> <span class='editLabel'>" + recordSet.getString("name") + "</span></td>" + "<td><div class='check'></div><a href='#' onclick='deleteLabel(this)' >" + SystemEnv.getHtmlLabelName(91, paramInt) + "</a></td>" + "</tr>";
/*     */     }
/*     */ 
/*     */ 
/*     */     
/* 222 */     str = str + "</table>";
/* 223 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getUserLabelsForTab(String paramString) {
/* 232 */     String str = "";
/* 233 */     RecordSet recordSet = new RecordSet();
/* 234 */     recordSet.execute("select id,name,icon from CRM_label where userid='" + paramString + "'");
/* 235 */     while (recordSet.next()) {
/* 236 */       str = str + "<div class='row' onmouseover=\"this.className='rowOver'\" onmouseout=\"this.className='row'\"><div class='title' action=\"loadCoworkItemListByLabel(this)\" labelid=\"" + recordSet.getString("id") + "\">" + recordSet.getString("name") + "</div>" + "</div>";
/*     */     }
/*     */ 
/*     */     
/* 240 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List getLabelList(String paramString1, String paramString2) {
/* 250 */     ArrayList<CustomerLabelVO> arrayList = new ArrayList();
/* 251 */     String str = "select id,name,isUsed,labelType,labelOrder,labelColor,textColor from CRM_label where userid=" + paramString1;
/* 252 */     if (!paramString2.equals("all"))
/* 253 */       str = str + " and labelType=" + paramString2; 
/* 254 */     str = str + " order by labelOrder";
/* 255 */     RecordSet recordSet = new RecordSet();
/* 256 */     boolean bool = recordSet.execute(str);
/*     */     
/* 258 */     while (recordSet.next()) {
/*     */       
/* 260 */       String str1 = recordSet.getString("id");
/* 261 */       String str2 = recordSet.getString("name");
/* 262 */       String str3 = recordSet.getString("isUsed");
/* 263 */       String str4 = recordSet.getString("labelType");
/* 264 */       String str5 = recordSet.getString("labelOrder");
/* 265 */       String str6 = recordSet.getString("labelColor");
/* 266 */       String str7 = recordSet.getString("textColor");
/*     */       
/* 268 */       CustomerLabelVO customerLabelVO = new CustomerLabelVO();
/* 269 */       customerLabelVO.setId(str1);
/* 270 */       customerLabelVO.setName(str2);
/* 271 */       customerLabelVO.setUserid(paramString1);
/* 272 */       customerLabelVO.setIsUsed(str3);
/* 273 */       customerLabelVO.setLabelType(str4);
/* 274 */       customerLabelVO.setLabelOrder(str5);
/* 275 */       customerLabelVO.setLabelColor(str6);
/* 276 */       customerLabelVO.setTextColor(str7);
/*     */       
/* 278 */       arrayList.add(customerLabelVO);
/*     */     } 
/*     */     
/* 281 */     return arrayList;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/crm/customer/CustomerLabelService.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */