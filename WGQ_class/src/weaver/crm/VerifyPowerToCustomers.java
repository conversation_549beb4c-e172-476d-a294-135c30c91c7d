/*    */ package weaver.crm;
/*    */ 
/*    */ import javax.servlet.http.HttpServletRequest;
/*    */ import javax.servlet.http.HttpServletResponse;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.BaseBean;
/*    */ import weaver.hrm.HrmUserVarify;
/*    */ import weaver.hrm.User;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class VerifyPowerToCustomers
/*    */   extends BaseBean
/*    */ {
/*    */   private boolean isinit = true;
/* 19 */   private String TableName = "";
/*    */   private User user;
/* 21 */   private RecordSet rs = new RecordSet();
/*    */   private String login_type;
/* 23 */   private String sqlstr = "";
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   private String userID;
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getTableName(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/* 34 */     if (this.isinit) {
/*    */       
/* 36 */       this.user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/* 37 */       this.login_type = this.user.getLogintype();
/* 38 */       this.sqlstr = "";
/* 39 */       this.userID = "" + this.user.getUID();
/* 40 */       this.TableName = "CRM_CustomerInfo" + this.login_type + "Temp" + this.userID;
/* 41 */       getTableNameFromServer();
/* 42 */       this.isinit = false;
/*    */     } 
/* 44 */     return this.TableName;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public void resetTableName() {
/* 51 */     this.sqlstr = "if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[" + this.TableName + "]') and OBJECTPROPERTY(id, N'IsView') = 1)\tdrop view [dbo].[" + this.TableName + "]";
/* 52 */     this.rs.executeSql(this.sqlstr);
/* 53 */     this.isinit = true;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   private void getTableNameFromServer() throws Exception {
/* 60 */     String str1 = "" + this.user.getUserDepartment();
/* 61 */     String str2 = "" + this.user.getSeclevel();
/* 62 */     String str3 = "" + this.user.getUserSubCompany1();
/*    */ 
/*    */     
/*    */     try {
/* 66 */       this.sqlstr = "if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[" + this.TableName + "]') and OBJECTPROPERTY(id, N'IsView') = 1)\tdrop view [dbo].[" + this.TableName + "]";
/* 67 */       this.rs.executeSql(this.sqlstr);
/*    */       
/* 69 */       if (this.login_type.equals("1")) {
/*    */         
/* 71 */         this.sqlstr = " create view " + this.TableName + " as select t1.id from CRM_CustomerInfo  t1,  CRM_ShareInfo  t2,  HrmRoleMembers  t3 ";
/*    */         
/* 73 */         this.sqlstr += " where  ((t1.id=t2.relateditemid) and ( (t2.foralluser=1 and t2.seclevel<=" + str2 + " and t2.seclevelMax >= " + str2 + ") or ( t2.userid=" + this.userID + " ) or (t2.departmentid=" + str1 + " and t2.seclevel<=" + str2 + " and t2.seclevelMax >= " + str2 + ") or (t3.resourceid=" + this.userID + " and t3.roleid=t2.roleid and t3.rolelevel>=t2.rolelevel and ( (t2.rolelevel=0 and t1.department=" + str1 + ") or (t2.rolelevel=1 and t1.subcompanyid1=" + str3 + ")  or (t3.rolelevel=2) ) ) ) ) ";
/*    */         
/* 75 */         this.sqlstr += " UNION ";
/*    */         
/* 77 */         this.sqlstr += " select distinct(t1.id) from CRM_CustomerInfo  t1,  HrmRoleMembers  t3,  HrmResource  t4 ";
/*    */         
/* 79 */         this.sqlstr += " where (t1.manager=" + this.userID + "  or  (t4.managerid=" + this.userID + " and t4.id=t1.manager)  or \t\t(t3.resourceid=" + this.userID + " and t3.roleid=8 and ( (t3.rolelevel=0 and t1.department=" + str1 + ") or (t3.rolelevel=1 and t1.subcompanyid1=" + str3 + ") or (t3.rolelevel=2))))";
/*    */         
/* 81 */         this.rs.executeSql(this.sqlstr);
/*    */       } 
/*    */       
/* 84 */       if (this.login_type.equals("2"))
/*    */       {
/* 86 */         this.sqlstr = " create view " + this.TableName + " as select id  from CRM_CustomerInfo where agent=" + this.userID;
/*    */         
/* 88 */         this.rs.executeSql(this.sqlstr);
/*    */       }
/*    */     
/* 91 */     } catch (Exception exception) {
/* 92 */       writeLog(exception);
/* 93 */       throw exception;
/*    */     } 
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/crm/VerifyPowerToCustomers.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */