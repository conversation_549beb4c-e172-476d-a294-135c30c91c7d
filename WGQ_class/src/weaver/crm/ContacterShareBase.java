/*     */ package weaver.crm;
/*     */ 
/*     */ import com.engine.common.service.impl.HrmCommonServiceImpl;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.crm.Maint.ContractComInfo;
/*     */ import weaver.crm.Maint.CustomerInfoComInfo;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.companyvirtual.ResourceVirtualComInfo;
/*     */ import weaver.hrm.resource.AllManagers;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class ContacterShareBase
/*     */   extends BaseBean
/*     */ {
/*     */   public void setDefaultShare(String paramString) throws Exception {
/*     */     try {
/*  25 */       RecordSet recordSet = new RecordSet();
/*  26 */       recordSet.executeSql("delete from Contract_ShareInfo where relateditemid = " + paramString + " and isdefault='1'");
/*     */       
/*  28 */       ContractComInfo contractComInfo = new ContractComInfo();
/*  29 */       CustomerInfoComInfo customerInfoComInfo = new CustomerInfoComInfo();
/*  30 */       AllManagers allManagers = new AllManagers();
/*     */       
/*  32 */       String str1 = contractComInfo.getContractcrmid(paramString);
/*  33 */       String str2 = customerInfoComInfo.getCustomerInfomanager(str1);
/*     */ 
/*     */       
/*  36 */       String str3 = contractComInfo.getManagerid(paramString);
/*  37 */       recordSet.executeSql("insert into Contract_ShareInfo (relateditemid,sharetype,seclevel,seclevelMax,rolelevel, sharelevel,userid,departmentid,subcompanyid,roleid,foralluser,isdefault) values (" + paramString + ",1,0,0,0,2," + str3 + ",0,0,0,0,1)");
/*     */ 
/*     */ 
/*     */       
/*  41 */       allManagers.getAll(str3);
/*  42 */       while (allManagers.next()) {
/*  43 */         recordSet.executeSql("insert into Contract_ShareInfo (relateditemid,sharetype,seclevel,seclevelMax,rolelevel, sharelevel,userid,departmentid,subcompanyid,roleid,foralluser,isdefault) values (" + paramString + ",1,0,0,0,3," + allManagers
/*  44 */             .getManagerID() + ",0,0,0,0,1)");
/*     */       }
/*     */       
/*  47 */       if (!str3.equals(str2))
/*     */       {
/*  49 */         recordSet.executeSql("insert into Contract_ShareInfo (relateditemid,sharetype,seclevel,seclevelMax,rolelevel, sharelevel,userid,departmentid,subcompanyid,roleid,foralluser,isdefault) values (" + paramString + ",1,0,0,0,1," + str2 + ",0,0,0,0,1)");
/*     */ 
/*     */ 
/*     */         
/*  53 */         allManagers.getAll(str2);
/*  54 */         while (allManagers.next()) {
/*  55 */           recordSet.executeSql("insert into Contract_ShareInfo (relateditemid,sharetype,seclevel,seclevelMax,rolelevel, sharelevel,userid,departmentid,subcompanyid,roleid,foralluser,isdefault) values (" + paramString + ",1,0,0,0,1," + allManagers
/*  56 */               .getManagerID() + ",0,0,0,0,1)");
/*     */         }
/*     */       }
/*     */     
/*  60 */     } catch (Exception exception) {
/*  61 */       exception.printStackTrace();
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getRightLevelForContacter(String paramString1, String paramString2) {
/*  75 */     int i = 0;
/*     */     try {
/*  77 */       if (paramString1.equals("") || paramString2.equals("")) return i; 
/*  78 */       ResourceComInfo resourceComInfo = new ResourceComInfo();
/*  79 */       RecordSet recordSet = new RecordSet();
/*     */ 
/*     */       
/*  82 */       int j = Util.getIntValue(resourceComInfo.getSeclevel(paramString1), -1);
/*  83 */       int k = Util.getIntValue(resourceComInfo.getDepartmentID(paramString1), -1);
/*  84 */       int m = Util.getIntValue(resourceComInfo.getSubCompanyID(paramString1), -1);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/*  94 */       HrmCommonServiceImpl hrmCommonServiceImpl = new HrmCommonServiceImpl();
/*  95 */       List list = hrmCommonServiceImpl.getRoleInfo(Integer.parseInt(paramString1));
/*     */       
/*  97 */       String str1 = "SELECT DISTINCT roleid,rolelevel FROM HrmRoleMembers WHERE roleid IN ( select roleid from SystemRightRoles where rightid = 396) AND resourceid = " + paramString1 + " order by rolelevel asc";
/*  98 */       recordSet.executeQuery(str1, new Object[0]);
/*  99 */       while (recordSet.next()) {
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */         
/* 109 */         String str = recordSet.getString("roleid");
/* 110 */         int n = recordSet.getInt("rolelevel");
/* 111 */         int i1 = 0;
/* 112 */         RecordSet recordSet1 = new RecordSet();
/* 113 */         recordSet1.executeQuery("select rolelevel from SystemRightRoles where rightid = 396 and roleid =? ", new Object[] { str });
/* 114 */         if (recordSet1.next()) {
/* 115 */           i1 = recordSet1.getInt("rolelevel");
/*     */         }
/* 117 */         boolean bool = (n >= i1) ? true : false;
/* 118 */         if (bool) {
/* 119 */           RecordSet recordSet2 = new RecordSet();
/* 120 */           recordSet2.executeQuery("select departmentid,subcompanyid1 from HrmResource where id in (select manager from CRM_Contract where id = ?)", new Object[] { paramString2 });
/* 121 */           if (recordSet2.next()) {
/* 122 */             int i2 = recordSet2.getInt("departmentid");
/* 123 */             int i3 = recordSet2.getInt("subcompanyid1");
/* 124 */             if (n == 0 && i2 == k) {
/* 125 */               return 2;
/*     */             }
/* 127 */             if (n == 1 && i3 == m) {
/* 128 */               return 2;
/*     */             }
/* 130 */             if (n == 2)
/* 131 */               return 2; 
/*     */             continue;
/*     */           } 
/* 134 */           if (n == 2) {
/* 135 */             return 2;
/*     */           }
/*     */         } 
/*     */       } 
/*     */ 
/*     */       
/* 141 */       String str2 = getRoleSql(paramString1);
/* 142 */       if (str2.equals("")) str2 = " 1=2 "; 
/* 143 */       ResourceVirtualComInfo resourceVirtualComInfo = new ResourceVirtualComInfo();
/* 144 */       String[] arrayOfString1 = Util.TokenizerString2(Util.null2String(resourceVirtualComInfo.getDepartmentids(paramString1)), ",");
/* 145 */       String[] arrayOfString2 = Util.TokenizerString2(Util.null2String(resourceVirtualComInfo.getSubcompanyids(paramString1)), ",");
/* 146 */       String str3 = "or (sharetype=2 and departmentid=" + k + " and seclevel<=" + j + " and seclevelMax >= " + j + " ) ";
/* 147 */       for (String str : arrayOfString1) {
/* 148 */         str3 = str3 + "or (sharetype=2 and departmentid=" + str + " and seclevel<=" + j + " and seclevelMax >= " + j + " ) ";
/*     */       }
/*     */       
/* 151 */       String str4 = "or (sharetype=5 and subcompanyid=" + m + " and seclevel<=" + j + " and seclevelMax >= " + j + " ) ";
/* 152 */       for (String str : arrayOfString2) {
/* 153 */         str4 = str4 + "or (sharetype=5 and subcompanyid=" + str + " and seclevel<=" + j + " and seclevelMax >= " + j + " ) ";
/*     */       }
/* 155 */       str1 = "select max(sharelevel) as sharelevel from Contract_ShareInfo where ( ( sharetype=1 and userid=" + paramString1 + " )" + str3 + str4 + " or ( sharetype=4 and foralluser=1 and seclevel<=" + j + " and seclevelMax >= " + j + "  ) or ( (" + str2 + ")  ) or ( sharetype=5 and subcompanyid=" + m + " and seclevel<=" + j + " and seclevelMax >= " + j + " and foralluser=1 )) and relateditemid=" + paramString2;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 164 */       recordSet.executeSql(str1);
/* 165 */       if (recordSet.next()) {
/* 166 */         i = recordSet.getInt("sharelevel");
/* 167 */         return i;
/*     */       } 
/* 169 */     } catch (Exception exception) {
/* 170 */       writeLog(exception);
/*     */     } 
/* 172 */     return i;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getTempTable(String paramString) {
/* 179 */     String str1 = "";
/* 180 */     String str2 = "";
/*     */     try {
/* 182 */       ResourceComInfo resourceComInfo = new ResourceComInfo();
/* 183 */       RecordSet recordSet = new RecordSet();
/*     */       
/* 185 */       int i = Util.getIntValue(resourceComInfo.getSeclevel(paramString), 0);
/* 186 */       int j = Util.getIntValue(resourceComInfo.getDepartmentID(paramString), -1);
/* 187 */       int k = Util.getIntValue(resourceComInfo.getSubCompanyID(paramString), -1);
/*     */ 
/*     */       
/* 190 */       String str3 = "SELECT DISTINCT rolelevel,roleid FROM HrmRoleMembers WHERE roleid IN (select roleid from SystemRightRoles where rightid = 396) AND resourceid = " + paramString + " order by rolelevel asc";
/*     */       
/* 192 */       recordSet.execute(str3);
/* 193 */       while (recordSet.next()) {
/* 194 */         int m = recordSet.getInt("rolelevel");
/* 195 */         int n = 0;
/* 196 */         RecordSet recordSet1 = new RecordSet();
/* 197 */         recordSet1.executeQuery("select rolelevel from SystemRightRoles where rightid = 396 and roleid = ? ", new Object[] { recordSet.getString("roleid") });
/* 198 */         if (recordSet1.next()) {
/* 199 */           n = recordSet1.getInt("rolelevel");
/*     */         }
/* 201 */         boolean bool = (m >= n) ? true : false;
/* 202 */         if (m == 2 && bool) {
/* 203 */           str2 = " SELECT id as relateditemid from CRM_Contract ";
/*     */         }
/* 205 */         if (m == 0 && bool) {
/* 206 */           str2 = " select id as relateditemid from CRM_Contract where manager in (SELECT id FROM HrmResource WHERE departmentid = " + j + ")";
/*     */         }
/* 208 */         if (m == 1 && bool) {
/* 209 */           str2 = " select id as relateditemid from CRM_Contract where manager in (SELECT id FROM HrmResource WHERE subcompanyid1 = " + k + ")";
/*     */         }
/*     */       } 
/*     */       
/* 213 */       String str4 = getRoleSql(paramString);
/* 214 */       if (str4.equals("")) str4 = " 1=2 "; 
/* 215 */       ResourceVirtualComInfo resourceVirtualComInfo = new ResourceVirtualComInfo();
/* 216 */       String[] arrayOfString1 = Util.TokenizerString2(Util.null2String(resourceVirtualComInfo.getDepartmentids(paramString)), ",");
/* 217 */       String[] arrayOfString2 = Util.TokenizerString2(Util.null2String(resourceVirtualComInfo.getSubcompanyids(paramString)), ",");
/* 218 */       String str5 = "or (sharetype=2 and departmentid=" + j + " and seclevel<=" + i + " and seclevelMax >= " + i + " ) ";
/* 219 */       for (String str : arrayOfString1) {
/* 220 */         str5 = str5 + "or (sharetype=2 and departmentid=" + str + " and seclevel<=" + i + " and seclevelMax >= " + i + " ) ";
/*     */       }
/*     */       
/* 223 */       String str6 = "or (sharetype=5 and subcompanyid=" + k + " and seclevel<=" + i + " and seclevelMax >= " + i + " ) ";
/* 224 */       for (String str : arrayOfString2) {
/* 225 */         str6 = str6 + "or (sharetype=5 and subcompanyid=" + str + " and seclevel<=" + i + " and seclevelMax >= " + i + " ) ";
/*     */       }
/*     */       
/* 228 */       str1 = " select distinct relateditemid from Contract_ShareInfo where  (sharetype=1 and userid=" + paramString + ") " + str5 + str6 + "or ((" + str4 + ") ) or (sharetype=4 and foralluser=1 and seclevel<=" + i + " and seclevelMax >= " + i + ") ";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 236 */       if (str2.equals("")) {
/* 237 */         return "(" + str1 + ")";
/*     */       }
/* 239 */       return "(select distinct relateditemid from (" + str2 + " union all " + str1 + ") tt)";
/*     */     }
/* 241 */     catch (Exception exception) {
/* 242 */       exception.printStackTrace();
/*     */       
/* 244 */       return str1;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getRoleSql(String paramString) throws Exception {
/* 252 */     String str = "";
/* 253 */     ResourceComInfo resourceComInfo = new ResourceComInfo();
/*     */ 
/*     */     
/* 256 */     int i = Util.getIntValue(resourceComInfo.getDepartmentID(paramString), -1);
/* 257 */     int j = Util.getIntValue(resourceComInfo.getSubCompanyID(paramString), -1);
/* 258 */     int k = Util.getIntValue(resourceComInfo.getSeclevel(paramString), 0);
/*     */ 
/*     */     
/* 261 */     HrmCommonServiceImpl hrmCommonServiceImpl = new HrmCommonServiceImpl();
/* 262 */     List<Map> list = hrmCommonServiceImpl.getRoleInfo(Integer.parseInt(paramString));
/* 263 */     for (byte b = 0; b < list.size(); b++) {
/* 264 */       Map map = list.get(b);
/* 265 */       String str1 = (String)map.get("roleid");
/* 266 */       String str2 = (String)map.get("rolelevel");
/*     */ 
/*     */ 
/*     */       
/* 270 */       if (str.equals("")) {
/* 271 */         if (str2.equals("0")) {
/* 272 */           str = str + " (sharetype=3  and  roleid=" + str1 + " and rolelevel<=" + str2 + " and seclevel<=" + k + " and seclevelMax >= " + k + ") ";
/* 273 */         } else if (str2.equals("1")) {
/* 274 */           str = str + " ( sharetype=3  and roleid=" + str1 + " and rolelevel<=" + str2 + " and seclevel<=" + k + " and seclevelMax >= " + k + ") ";
/*     */         } else {
/* 276 */           str = str + " (  sharetype=3  and roleid=" + str1 + " and rolelevel<=" + str2 + " and seclevel<=" + k + " and seclevelMax >= " + k + ") ";
/*     */         } 
/* 278 */       } else if (str2.equals("0")) {
/* 279 */         str = str + " or (sharetype=3  and roleid=" + str1 + " and rolelevel<=" + str2 + " and seclevel<=" + k + " and seclevelMax >= " + k + ") ";
/* 280 */       } else if (str2.equals("1")) {
/* 281 */         str = str + " or (sharetype=3  and roleid=" + str1 + " and rolelevel<=" + str2 + " and seclevel<=" + k + " and seclevelMax >= " + k + ") ";
/*     */       } else {
/* 283 */         str = str + " or (sharetype=3  and roleid=" + str1 + " and rolelevel<=" + str2 + "  and seclevel<=" + k + " and seclevelMax >= " + k + ") ";
/*     */       } 
/* 285 */     }  return str;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/crm/ContacterShareBase.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */