/*     */ package weaver.crm;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.crm.Maint.ContractComInfo;
/*     */ import weaver.crm.Maint.ContractTypeComInfo;
/*     */ import weaver.crm.Maint.CustomerInfoComInfo;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.company.DepartmentComInfo;
/*     */ import weaver.hrm.company.SubCompanyComInfo;
/*     */ import weaver.hrm.job.JobTitlesComInfo;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ import weaver.hrm.roles.RolesComInfo;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CRMTransMethod
/*     */   extends BaseBean
/*     */ {
/*  28 */   private RecordSet rs = new RecordSet();
/*  29 */   private ResourceComInfo rc = null;
/*  30 */   private CustomerInfoComInfo cci = null;
/*  31 */   private ContractTypeComInfo ctci = null;
/*  32 */   private ContractComInfo ci = null;
/*  33 */   private DepartmentComInfo dept = null;
/*  34 */   private RolesComInfo roles = null;
/*  35 */   private SubCompanyComInfo sdept = null;
/*  36 */   private CustomerInfoComInfo customerInfo = null;
/*     */   public CRMTransMethod() {
/*     */     try {
/*  39 */       this.cci = new CustomerInfoComInfo();
/*  40 */       this.rc = new ResourceComInfo();
/*  41 */       this.ctci = new ContractTypeComInfo();
/*  42 */       this.ci = new ContractComInfo();
/*  43 */       this.dept = new DepartmentComInfo();
/*  44 */       this.roles = new RolesComInfo();
/*  45 */       this.sdept = new SubCompanyComInfo();
/*  46 */       this.customerInfo = new CustomerInfoComInfo();
/*  47 */     } catch (Exception exception) {
/*  48 */       writeLog(exception);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getShareTypeName(String paramString1, String paramString2) {
/*  60 */     if (paramString1.equals("1"))
/*  61 */       return SystemEnv.getHtmlLabelName(179, Util.getIntValue(paramString2)); 
/*  62 */     if (paramString1.equals("2"))
/*  63 */       return SystemEnv.getHtmlLabelName(124, Util.getIntValue(paramString2)); 
/*  64 */     if (paramString1.equals("3"))
/*  65 */       return SystemEnv.getHtmlLabelName(122, Util.getIntValue(paramString2)); 
/*  66 */     if (paramString1.equals("5"))
/*  67 */       return SystemEnv.getHtmlLabelName(141, Util.getIntValue(paramString2)); 
/*  68 */     if (paramString1.equals("9"))
/*  69 */       return SystemEnv.getHtmlLabelName(136, Util.getIntValue(paramString2)); 
/*  70 */     if (paramString1.equals("6")) {
/*  71 */       return SystemEnv.getHtmlLabelName(6086, Util.getIntValue(paramString2));
/*     */     }
/*     */     
/*  74 */     return SystemEnv.getHtmlLabelName(1340, Util.getIntValue(paramString2));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getShareValueName(String paramString1, String paramString2) {
/*  86 */     ArrayList<String> arrayList = Util.TokenizerString(paramString2, "+");
/*  87 */     String str1 = arrayList.get(0);
/*  88 */     String str2 = arrayList.get(1);
/*  89 */     String str3 = "0";
/*  90 */     if (arrayList.size() > 5) {
/*  91 */       str3 = arrayList.get(5);
/*     */     }
/*  93 */     if (str1.equals("1"))
/*  94 */       return this.rc.getLastname(paramString1); 
/*  95 */     if (str1.equals("2"))
/*  96 */       return this.dept.getDepartmentname(paramString1) + "/" + this.sdept.getSubCompanyname(this.dept.getSubcompanyid1(paramString1)); 
/*  97 */     if (str1.equals("3")) {
/*  98 */       String str = "";
/*  99 */       if (str3.equals("0")) {
/* 100 */         str = SystemEnv.getHtmlLabelName(124, Util.getIntValue(str2));
/* 101 */       } else if (str3.equals("1")) {
/* 102 */         str = SystemEnv.getHtmlLabelName(141, Util.getIntValue(str2));
/*     */       } else {
/* 104 */         str = SystemEnv.getHtmlLabelName(140, Util.getIntValue(str2));
/*     */       } 
/* 106 */       return this.roles.getRolesRemark(paramString1) + " / " + str;
/*     */     } 
/*     */     
/* 109 */     if (str1.equals("5"))
/* 110 */       return this.sdept.getSubcompanyname(paramString1); 
/* 111 */     if (str1.equals("9"))
/* 112 */       return this.customerInfo.getCustomerInfoname(paramString1); 
/* 113 */     if (str1.equals("6")) {
/* 114 */       String str4 = (arrayList.size() > 3) ? arrayList.get(3).toString().trim() : "";
/* 115 */       String str5 = (arrayList.size() > 4) ? arrayList.get(4).toString().trim() : "";
/* 116 */       JobTitlesComInfo jobTitlesComInfo = null;
/* 117 */       DepartmentComInfo departmentComInfo = null;
/* 118 */       SubCompanyComInfo subCompanyComInfo = null;
/*     */       try {
/* 120 */         jobTitlesComInfo = new JobTitlesComInfo();
/* 121 */         departmentComInfo = new DepartmentComInfo();
/* 122 */         subCompanyComInfo = new SubCompanyComInfo();
/* 123 */       } catch (Exception exception) {}
/*     */       
/* 125 */       String str6 = "";
/* 126 */       if ("0".equals(str4)) {
/* 127 */         str6 = SystemEnv.getHtmlLabelName(140, Util.getIntValue(str2));
/* 128 */       } else if ("2".equals(str4)) {
/* 129 */         str6 = SystemEnv.getHtmlLabelName(19437, 7) + "(" + subCompanyComInfo.getSubcompanynames(str5) + ")";
/* 130 */       } else if ("1".equals(str4)) {
/* 131 */         str6 = SystemEnv.getHtmlLabelName(19438, 7) + "(" + departmentComInfo.getDepartmentNames(str5) + ")";
/*     */       } 
/* 133 */       return jobTitlesComInfo.getJobTitlesmark(paramString1) + " / " + str6;
/*     */     } 
/*     */     
/* 136 */     return "";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getSecLevelName(String paramString1, String paramString2) {
/* 148 */     ArrayList<String> arrayList = Util.TokenizerString(paramString2, "+");
/* 149 */     String str1 = arrayList.get(0);
/* 150 */     String str2 = arrayList.get(1);
/* 151 */     String str3 = arrayList.get(2);
/* 152 */     String str4 = arrayList.get(3).toString().trim();
/*     */     
/* 154 */     paramString1 = paramString1 + " - " + str4;
/* 155 */     if (str1.equals("1"))
/* 156 */       return ""; 
/* 157 */     if (str1.equals("2"))
/* 158 */       return paramString1; 
/* 159 */     if (str1.equals("5"))
/* 160 */       return paramString1; 
/* 161 */     if (str1.equals("3"))
/* 162 */       return paramString1; 
/* 163 */     if (str1.equals("9") || str1.equals("6")) {
/* 164 */       return "";
/*     */     }
/* 166 */     return paramString1;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public String getShareLevelName(String paramString1, String paramString2) {
/* 172 */     return paramString1.equals("1") ? SystemEnv.getHtmlLabelName(367, Util.getIntValue(paramString2)) : SystemEnv.getHtmlLabelName(93, Util.getIntValue(paramString2));
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/crm/CRMTransMethod.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */