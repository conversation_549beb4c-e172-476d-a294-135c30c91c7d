/*    */ package weaver.crm.util;
/*    */ 
/*    */ import java.util.ArrayList;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.crm.Maint.ContactWayComInfo;
/*    */ import weaver.crm.Maint.CustomerInfoComInfo;
/*    */ import weaver.crm.Maint.CustomerStatusComInfo;
/*    */ import weaver.general.BaseBean;
/*    */ import weaver.general.GCONST;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.resource.ResourceComInfo;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ public class TransUtil
/*    */   extends BaseBean {
/* 16 */   private ResourceComInfo rc = null;
/* 17 */   private CustomerInfoComInfo ci = null;
/*    */   
/*    */   public TransUtil() {
/*    */     try {
/* 21 */       this.rc = new ResourceComInfo();
/* 22 */       this.ci = new CustomerInfoComInfo();
/* 23 */     } catch (Exception exception) {
/* 24 */       writeLog(exception);
/*    */     } 
/*    */   }
/*    */   
/*    */   public String getCustomerLink(String paramString1, String paramString2) {
/* 29 */     String str = "";
/* 30 */     if (paramString1 != null && !"".equals(paramString1)) {
/* 31 */       str = "<a href=\"javascript:openFullWindowHaveBar('" + GCONST.getContextPath() + "/CRM/data/ViewCustomer.jsp?log=n&CustomerID=" + paramString1 + "')\">" + paramString2 + "</a>";
/*    */     }
/* 33 */     return str;
/*    */   }
/*    */   
/*    */   public String getPerson(String paramString) {
/* 37 */     String str = "";
/* 38 */     if (paramString != null && !"".equals(paramString)) {
/* 39 */       ArrayList<String> arrayList = Util.TokenizerString(paramString, ",");
/* 40 */       for (byte b = 0; b < arrayList.size(); b++) {
/* 41 */         str = str + "<a href='javaScript:openhrm(" + arrayList.get(b) + ");' onclick='pointerXY(event);'>" + this.rc.getResourcename(arrayList.get(b)) + "</a> ";
/*    */       }
/*    */     } 
/* 44 */     return str;
/*    */   }
/*    */   public String getCreateSource(String paramString1, String paramString2) {
/* 47 */     return getCreateSource(paramString1, paramString2, "7");
/*    */   }
/*    */   public String getCreateSource(String paramString1, String paramString2, String paramString3) {
/* 50 */     ContactWayComInfo contactWayComInfo = null;
/*    */     try {
/* 52 */       contactWayComInfo = new ContactWayComInfo();
/* 53 */     } catch (Exception exception) {
/* 54 */       writeLog(exception);
/*    */     } 
/* 56 */     String str = contactWayComInfo.getContactWayname(paramString2);
/* 57 */     RecordSet recordSet = new RecordSet();
/* 58 */     recordSet.executeSql("select top 1 original,modified from CRM_Modify where (fieldname='source' or fieldname='" + SystemEnv.getHtmlLabelName(645, Integer.parseInt(paramString3)) + "') and customerid=" + paramString1 + " order by modifydate,modifytime");
/* 59 */     if (recordSet.next()) {
/* 60 */       String str1 = Util.null2String(recordSet.getString("original"));
/* 61 */       str = contactWayComInfo.getContactWayname(str1);
/*    */     } 
/* 63 */     return str;
/*    */   }
/*    */ 
/*    */   
/*    */   public String getCustomerStatus(String paramString1, String paramString2, String paramString3) {
/* 68 */     String str = "";
/* 69 */     CustomerStatusComInfo customerStatusComInfo = null;
/*    */     try {
/* 71 */       customerStatusComInfo = new CustomerStatusComInfo();
/* 72 */     } catch (Exception exception) {
/* 73 */       writeLog(exception);
/*    */     } 
/*    */     
/* 76 */     if (paramString2.equals("1")) {
/* 77 */       str = SystemEnv.getHtmlLabelName(501904, Integer.parseInt(paramString3));
/*    */     } else {
/* 79 */       str = customerStatusComInfo.getCustomerStatusname(paramString1);
/*    */     } 
/* 81 */     return str;
/*    */   }
/*    */   
/*    */   public String getCustomerStatus(String paramString1, String paramString2) {
/* 85 */     return getCustomerStatus(paramString1, paramString2, "7");
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/crm/util/TransUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */