/*     */ package weaver.crm.util;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.cpt.capital.CapitalComInfo;
/*     */ import weaver.crm.Maint.CustomerInfoComInfo;
/*     */ import weaver.docs.docs.DocComInfo;
/*     */ import weaver.docs.senddoc.DocReceiveUnitComInfo;
/*     */ import weaver.filter.XssUtil;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.GCONST;
/*     */ import weaver.general.StaticObj;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.city.CityComInfo;
/*     */ import weaver.hrm.company.DepartmentComInfo;
/*     */ import weaver.hrm.company.SubCompanyComInfo;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ import weaver.interfaces.workflow.browser.Browser;
/*     */ import weaver.interfaces.workflow.browser.BrowserBean;
/*     */ import weaver.proj.Maint.ProjectInfoComInfo;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.workflow.field.BrowserComInfo;
/*     */ import weaver.workflow.workflow.WorkflowRequestComInfo;
/*     */ 
/*     */ 
/*     */ public class CrmUtil
/*     */   extends BaseBean
/*     */ {
/*     */   public String getHtmlLableName(CrmFieldComInfo paramCrmFieldComInfo, User paramUser) {
/*  31 */     int i = Util.getIntValue(paramCrmFieldComInfo.getFieldlabel(), 0);
/*  32 */     if (i == 0) {
/*  33 */       return paramCrmFieldComInfo.getFieldlabel() + "";
/*     */     }
/*  35 */     return SystemEnv.getHtmlLabelName(i, paramUser.getLanguage());
/*     */   }
/*     */   
/*     */   public String getHtmlLableName(String paramString, User paramUser) {
/*  39 */     int i = Util.getIntValue(paramString, 0);
/*  40 */     if (i == 0) {
/*  41 */       return paramString + "";
/*     */     }
/*  43 */     return SystemEnv.getHtmlLabelName(i, paramUser.getLanguage());
/*     */   }
/*     */ 
/*     */   
/*     */   public String getHmtlElementInfo(CrmFieldComInfo paramCrmFieldComInfo, String paramString, User paramUser) {
/*  48 */     return getHmtlElementInfo(paramCrmFieldComInfo, paramString, paramUser, "info");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getHmtlElementInfo(CrmFieldComInfo paramCrmFieldComInfo, String paramString1, User paramUser, String paramString2) {
/*  60 */     String str1 = "";
/*  61 */     String str2 = paramCrmFieldComInfo.getFieldname();
/*  62 */     int i = paramCrmFieldComInfo.getFieldhtmltype().intValue();
/*  63 */     String str3 = paramCrmFieldComInfo.getDmlurl();
/*  64 */     String str4 = paramCrmFieldComInfo.getType();
/*  65 */     RecordSet recordSet = new RecordSet();
/*  66 */     if (i == 1) {
/*  67 */       if (paramString2.equals("edit")) {
/*  68 */         return "";
/*     */       }
/*  70 */       return paramString1;
/*     */     } 
/*     */     
/*  73 */     if (i == 2) {
/*  74 */       if (paramString2.equals("edit")) {
/*  75 */         return "";
/*     */       }
/*  77 */       return str1 = paramString1;
/*     */     } 
/*     */     
/*  80 */     if (i == 3) {
/*  81 */       if (str4.equals("2") || str4.equals("19")) {
/*  82 */         if (paramString2.equals("edit")) {
/*  83 */           return "";
/*     */         }
/*  85 */         return paramString1;
/*     */       } 
/*  87 */       if (paramString2.equals("edit")) {
/*  88 */         return "<div class='e8_txt' id='txtdiv_" + paramCrmFieldComInfo.getFieldname() + "'>" + getFieldvalue(paramUser, Util.getIntValue(str4), paramString1, str3) + "</div>";
/*     */       }
/*     */       
/*  91 */       BrowserComInfo browserComInfo = new BrowserComInfo();
/*  92 */       browserComInfo.removeBrowserCache();
/*  93 */       String str = browserComInfo.getLinkurl(str4 + "");
/*  94 */       if (str.indexOf("?") == -1) {
/*  95 */         str = str + "?" + browserComInfo.getBrowserkeycolumname(str4 + "") + "=";
/*     */       }
/*  97 */       if (str.equals("")) {
/*  98 */         return getFieldvalue(paramUser, Util.getIntValue(str4), paramString1, str3);
/*     */       }
/* 100 */       return "<a href='" + GCONST.getContextPath() + str + paramString1 + "' target='_blank'>" + getFieldvalue(paramUser, Util.getIntValue(str4), paramString1, str3) + "</a>";
/*     */     } 
/*     */     
/* 103 */     if (i == 4) {
/* 104 */       if (paramString2.equals("edit")) {
/* 105 */         return "";
/*     */       }
/* 107 */       String str = paramString1.equals("1") ? "checked=checked" : "";
/* 108 */       str1 = "<input type='checkbox'   name=" + str2 + " value='1' " + str + " disabled='disabled'>";
/* 109 */       return str1;
/*     */     } 
/*     */     
/* 112 */     if (i == 5) {
/* 113 */       if (!paramCrmFieldComInfo.getSeltablename().equals("")) {
/* 114 */         recordSet.execute("select " + paramCrmFieldComInfo.getSelcolumname() + " from " + paramCrmFieldComInfo.getSeltablename() + " where " + paramCrmFieldComInfo
/* 115 */             .getSelkeycolumname() + " =  " + paramString1);
/* 116 */         if (recordSet.next()) {
/* 117 */           str1 = recordSet.getString(1);
/*     */         }
/*     */       }
/* 120 */       else if (!"".equals(paramString1)) {
/* 121 */         recordSet.execute("select selectvalue , selectname from crm_selectitem where fieldid = " + paramCrmFieldComInfo.getId() + " and selectvalue = " + paramString1);
/* 122 */         if (recordSet.next()) {
/* 123 */           str1 = recordSet.getString("selectname");
/*     */         }
/*     */       } 
/*     */       
/* 127 */       if (paramString2.equals("edit")) {
/* 128 */         str1 = "<div class='e8_select_txt' id='txtdiv_" + paramCrmFieldComInfo.getFieldname() + "'>" + str1 + "</div>";
/*     */       }
/*     */       
/* 131 */       return str1;
/*     */     } 
/*     */     
/* 134 */     if (i == 6) {
/* 135 */       if (paramString2.equals("edit")) {
/* 136 */         return "";
/*     */       }
/* 138 */       if (!"".equals(paramString1)) {
/* 139 */         recordSet.execute("select imagefileid ,imagefilename from ImageFile  where imagefileid in (" + paramString1 + ")");
/* 140 */         StringBuffer stringBuffer = new StringBuffer();
/* 141 */         while (recordSet.next()) {
/* 142 */           stringBuffer.append("<a href='" + GCONST.getContextPath() + "/weaver/weaver.file.FileDownload?fileid=" + recordSet.getString("imagefileid") + "&download=1' >" + recordSet
/* 143 */               .getString("imagefilename") + "</a>&nbsp;&nbsp;&nbsp;&nbsp;");
/*     */         }
/* 145 */         stringBuffer.append("<a href='" + GCONST.getContextPath() + "/weaver/weaver.file.FileDownload?fieldids=" + paramString1 + ",&download=1&onlydownloadfj=1&labelid=156' >" + 
/* 146 */             SystemEnv.getHtmlLabelName(26654, paramUser.getLanguage()) + "</a>");
/* 147 */         str1 = stringBuffer.toString();
/*     */       } 
/*     */       
/* 150 */       return str1;
/*     */     } 
/*     */     
/* 153 */     return null;
/*     */   }
/*     */   
/*     */   public String getHtmlElementSetting(CrmFieldComInfo paramCrmFieldComInfo, String paramString, User paramUser) {
/* 157 */     return getHtmlElementSetting(paramCrmFieldComInfo, paramString, paramUser, "add");
/*     */   }
/*     */   
/*     */   public String getHtmlElementSetting(CrmFieldComInfo paramCrmFieldComInfo, String paramString1, String paramString2, User paramUser) {
/* 161 */     return getHtmlElementSetting(paramCrmFieldComInfo, paramString1, paramString2, paramUser, "add");
/*     */   }
/*     */   
/*     */   public String getHtmlElementSetting(CrmFieldComInfo paramCrmFieldComInfo, String paramString1, User paramUser, String paramString2) {
/* 165 */     return getHtmlElementSetting(paramCrmFieldComInfo, paramString1, null, paramUser, paramString2);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getHtmlElementSetting(CrmFieldComInfo paramCrmFieldComInfo, String paramString1, String paramString2, User paramUser, String paramString3) {
/* 177 */     RecordSet recordSet = new RecordSet();
/* 178 */     String str1 = "";
/* 179 */     String str2 = paramCrmFieldComInfo.getFieldname();
/* 180 */     String str3 = str2 + "span";
/* 181 */     int i = paramCrmFieldComInfo.getFieldhtmltype().intValue();
/* 182 */     String str4 = paramCrmFieldComInfo.getType();
/* 183 */     int j = Util.getIntValue(paramCrmFieldComInfo.getIsmust(), 0);
/* 184 */     String str5 = "";
/* 185 */     String str6 = "";
/* 186 */     String str7 = "";
/* 187 */     String str8 = "";
/* 188 */     String str9 = paramCrmFieldComInfo.getDmlurl();
/* 189 */     if (j == 1) {
/* 190 */       str5 = "checkinput(\"" + str2 + "\",\"" + str3 + "\")";
/* 191 */       str7 = "<span id='" + str3 + "'><img src='" + GCONST.getContextPath() + "/images/BacoError_wev8.gif' align=absmiddle></span>";
/* 192 */       str8 = paramString1.equals("") ? "" : ("<script>" + str5 + "</script>");
/*     */     } 
/*     */     
/* 195 */     if (paramCrmFieldComInfo.getFieldname().toLowerCase().contains("email")) {
/* 196 */       str6 = "checkinput_email(\"" + str2 + "\",\"" + str3 + "\")";
/*     */     }
/*     */     
/* 199 */     String str10 = "Inputstyle";
/* 200 */     if (paramString3.equals("edit")) {
/* 201 */       str10 = "item_input";
/*     */     }
/* 203 */     if (paramString3.equals("search")) {
/* 204 */       str7 = "";
/* 205 */       str5 = "";
/* 206 */       str6 = "";
/* 207 */       str8 = "";
/* 208 */       j = 0;
/*     */     } 
/*     */     
/* 211 */     if (i == 1) {
/* 212 */       if (str4.equals("1")) {
/* 213 */         String str11 = paramCrmFieldComInfo.getFielddbtype();
/* 214 */         String str12 = str11.substring(str11.indexOf("(") + 1, str11.length() - 1);
/*     */         
/* 216 */         str1 = "<input datatype='text' type='text'  class='" + str10 + "' maxlength='" + str12 + "' id=" + str2 + " name=" + str2 + " onChange='" + str5 + "' onblur='" + str6 + "' value='" + paramString1 + "' isMustInput='" + j + "'>";
/*     */       
/*     */       }
/* 219 */       else if (str4.equals("2")) {
/*     */         
/* 221 */         str1 = "<input  datatype='int' type='text'  class='" + str10 + "' id=" + str2 + " name=" + str2 + " size=10 onKeyPress='ItemCount_KeyPress()' onBlur='checkcount1(this);" + str5 + "' value='" + paramString1 + "'>";
/*     */       
/*     */       }
/* 224 */       else if (str4.equals("3")) {
/* 225 */         String str11 = paramCrmFieldComInfo.getFielddbtype();
/* 226 */         String str12 = str11.substring(str11.indexOf(",") + 1, str11.length() - 1);
/* 227 */         str1 = "<input datatype='float' datalength = '" + str12 + "'  type='text'  class='" + str10 + "' id=" + str2 + " name=" + str2 + " size=10 onBlur='checkFloat(this);" + str5 + "' value='" + paramString1 + "'>";
/*     */       } 
/*     */       
/* 230 */       if (paramString3.equals("search") && str2.equals("preyield")) {
/* 231 */         str1 = "<INPUT type='text' class=InputStyle maxLength=20 style='width:20%' size=12 id='preyield' name='preyield' value=''    onKeyPress='ItemNum_KeyPress()' onBlur='checknumber(\"preyield\");comparenumber()' >-<INPUT type='text' class=InputStyle maxLength=20 style='width:20%' size=12 id='preyield_1' name='preyield_1' value=''  onKeyPress='ItemNum_KeyPress()' onBlur='checknumber(\"preyield_1\");comparenumber()'>";
/*     */       }
/*     */ 
/*     */ 
/*     */       
/* 236 */       if (paramString3.equals("search") && str2.equals("probability")) {
/* 237 */         str1 = "<INPUT type='text' class=InputStyle maxLength=20 style='width:20%' size=12 id='probability' name='probability' value=''    onKeyPress='ItemNum_KeyPress()' onBlur='checknumber(\"probability\");comparenumber()' >-<INPUT type='text' class=InputStyle maxLength=20 style='width:20%' size=12 id='probability_1' name='probability_1' value=''  onKeyPress='ItemNum_KeyPress()' onBlur='checknumber(\"probability_1\");comparenumber()'>";
/*     */       }
/*     */ 
/*     */       
/* 241 */       str1 = str1 + str7 + str8;
/* 242 */       return str1;
/*     */     } 
/*     */     
/* 245 */     if (i == 2) {
/* 246 */       paramString1 = Util.toHtmltextarea(paramString1);
/* 247 */       if (str2.equals("introduction")) {
/* 248 */         str6 = "checklength(this, 2000)";
/* 249 */         str10 = "Inputstyle";
/*     */       } 
/* 251 */       str1 = "<textarea class='" + str10 + "' id=" + str2 + " onblur='" + str6 + "' name=" + str2 + " onChange='" + str5 + "' rows='" + paramCrmFieldComInfo.getTextheight() + "' cols='40' style='width: 90%' class=Inputstyle>" + paramString1 + "</textarea>";
/*     */       
/* 253 */       str1 = str1 + str7 + str8;
/*     */       
/* 255 */       return str1;
/*     */     } 
/*     */     
/* 258 */     if (i == 3) {
/*     */       
/* 260 */       StringBuilder stringBuilder = new StringBuilder();
/*     */       
/* 262 */       if (str4.equals("2") || str4.equals("19")) {
/* 263 */         if (paramString3.equals("search") && str2.equals("predate")) {
/* 264 */           stringBuilder.append("<span>");
/* 265 */           stringBuilder.append("<SELECT  name=\"predate\" id=\"predate\" style=\"width: 100px;\" onchange=\"onChangetype(this)\">");
/* 266 */           stringBuilder.append("<option value=\"\">" + SystemEnv.getHtmlLabelName(332, paramUser.getLanguage()) + "</option>");
/* 267 */           stringBuilder.append("<option value=\"1\">" + SystemEnv.getHtmlLabelName(15537, paramUser.getLanguage()) + "</option>");
/* 268 */           stringBuilder.append("<option value=\"2\">" + SystemEnv.getHtmlLabelName(15539, paramUser.getLanguage()) + "</option>");
/* 269 */           stringBuilder.append("<option value=\"3\">" + SystemEnv.getHtmlLabelName(15541, paramUser.getLanguage()) + "</option>");
/* 270 */           stringBuilder.append("<option value=\"4\">" + SystemEnv.getHtmlLabelName(21904, paramUser.getLanguage()) + "</option>");
/* 271 */           stringBuilder.append("<option value=\"5\">" + SystemEnv.getHtmlLabelName(15384, paramUser.getLanguage()) + "</option>");
/* 272 */           stringBuilder.append("<option value=\"6\">" + SystemEnv.getHtmlLabelName(32530, paramUser.getLanguage()) + "</option>");
/* 273 */           stringBuilder.append("</SELECT>");
/* 274 */           stringBuilder.append("</span>");
/* 275 */           stringBuilder.append("<span id=\"dateTd\" style=\"margin-left: 10px;padding-top: 5px;\">");
/* 276 */           stringBuilder.append("<BUTTON type=\"button\" class=calendar id=SelectDate onclick=getDate(fromdateSpan,fromdate)></BUTTON>&nbsp;");
/* 277 */           stringBuilder.append("<SPAN id=fromdateSpan ></SPAN>");
/* 278 */           stringBuilder.append("<input type=\"hidden\" name=\"fromdate\" value=\"\">");
/* 279 */           stringBuilder.append("-");
/* 280 */           stringBuilder.append("<BUTTON type=\"button\" class=calendar id=SelectDate onclick=getDate(enddateSpan,enddate)></BUTTON>&nbsp;");
/* 281 */           stringBuilder.append("<SPAN id=enddateSpan ></SPAN>");
/* 282 */           stringBuilder.append("<input type=\"hidden\" name=\"enddate\" value=\"\">");
/* 283 */           stringBuilder.append("</span>");
/* 284 */           return stringBuilder.toString();
/*     */         } 
/* 286 */         if (str4.equals("2")) {
/*     */           
/* 288 */           stringBuilder.append("<BUTTON type='button' class='calendar'  onclick='onShowDate1(" + str3 + "," + str2 + "," + j + ")'></BUTTON>");
/* 289 */           stringBuilder.append("<input type='hidden' name=" + str2 + " id=" + str2 + " value='" + paramString1 + "'> ");
/* 290 */           if (j == 1 && "".equals(paramString1)) {
/* 291 */             stringBuilder.append(str7);
/*     */           } else {
/* 293 */             stringBuilder.append("<span id='" + str3 + "'>" + paramString1 + "</span>");
/*     */           } 
/* 295 */           return stringBuilder.toString();
/*     */         } 
/*     */         
/* 298 */         if (str4.equals("19")) {
/*     */ 
/*     */           
/* 301 */           stringBuilder.append("<BUTTON type='button' class=calendar  onclick='onShowTime(" + str3 + "," + str2 + ")'></BUTTON>");
/* 302 */           stringBuilder.append("<input type='hidden' name=" + str2 + " id=" + str2 + " value='" + paramString1 + "'> ");
/* 303 */           if (j == 1 && "".equals(paramString1)) {
/* 304 */             stringBuilder.append(str7);
/*     */           } else {
/*     */             
/* 307 */             stringBuilder.append("<span id='" + str3 + "'>" + paramString1 + "</span>");
/*     */           } 
/*     */ 
/*     */           
/* 311 */           return stringBuilder.toString();
/*     */         } 
/*     */       } else {
/* 314 */         if (str2.equals("city")) {
/*     */           
/*     */           try {
/* 317 */             CityComInfo cityComInfo = new CityComInfo();
/* 318 */             stringBuilder.append("<div areaType=\"" + str2 + "\" areaName=\"" + str2 + "\" areaValue=\"" + paramString1 + "\" areaSpanValue=\"" + cityComInfo.getCityname(paramString1) + "\"  areaMustInput=\"" + ((j == 1) ? "2" : "1") + "\"  areaCallback=\"\" id=\"_areaselect_cityid\" class=\"_areaselect\" ></div>");
/* 319 */             stringBuilder.append("<SCRIPT language=\"javascript\" src=\"" + GCONST.getContextPath() + "/hrm/area/browser/areabrowser_wev8.js\"></script>");
/* 320 */             stringBuilder.append("<LINK href=\"" + GCONST.getContextPath() + "/hrm/area/browser/areabrowser.css\" type=text/css rel=STYLESHEET>");
/* 321 */             stringBuilder.append("<script language=\"javascript\">");
/* 322 */             stringBuilder.append("areromancedivbyid(\"_areaselect_" + str2 + "\")");
/* 323 */             stringBuilder.append("</script>");
/* 324 */           } catch (Exception exception) {}
/*     */           
/* 326 */           return stringBuilder.toString();
/* 327 */         }  if (str2.equals("district")) {
/*     */           
/*     */           try {
/* 330 */             CityComInfo cityComInfo = new CityComInfo();
/* 331 */             stringBuilder.append("<div areaType=\"citytwo\" areaName=\"" + str2 + "\" areaValue=\"" + paramString1 + "\" areaSpanValue=\"" + cityComInfo.getCityname(paramString1) + "\"  areaMustInput=\"" + ((j == 1) ? "2" : "1") + "\"  areaCallback=\"\" id=\"_areaselect_districtid\" class=\"_areaselect\" ></div>");
/* 332 */             stringBuilder.append("<SCRIPT language=\"javascript\" src=\"" + GCONST.getContextPath() + "/hrm/area/browser/areabrowser_wev8.js\"></script>");
/* 333 */             stringBuilder.append("<LINK href=\"" + GCONST.getContextPath() + "/hrm/area/browser/areabrowser.css\" type=text/css rel=STYLESHEET>");
/* 334 */             stringBuilder.append("<script language=\"javascript\">");
/* 335 */             stringBuilder.append("areromancedivbyid(\"_areaselect_" + str2 + "\")");
/* 336 */             stringBuilder.append("</script>");
/* 337 */           } catch (Exception exception) {}
/*     */           
/* 339 */           return stringBuilder.toString();
/* 340 */         }  if (paramUser.getLogintype().equals("2") && str2.equals("manager")) {
/* 341 */           String str = "";
/*     */           try {
/* 343 */             ResourceComInfo resourceComInfo = new ResourceComInfo();
/* 344 */             str = resourceComInfo.getResourcename(paramUser.getManagerid());
/* 345 */           } catch (Exception exception) {
/* 346 */             exception.printStackTrace();
/*     */           } 
/* 348 */           str1 = "<span>" + str + "</span>";
/* 349 */           str1 = str1 + "<input type=hidden name=" + str2 + " _mand='" + j + "' value='" + paramUser.getManagerid() + "'>";
/* 350 */           return str1;
/*     */         } 
/*     */       } 
/* 353 */       BrowserComInfo browserComInfo = new BrowserComInfo();
/* 354 */       String str11 = browserComInfo.getBrowserurl(str4 + "");
/* 355 */       String str12 = browserComInfo.getLinkurl(str4 + "");
/* 356 */       String str13 = GCONST.getContextPath() + "/data.jsp?type=" + str4;
/*     */       
/* 358 */       if ("agent".equals(str2)) {
/* 359 */         str11 = str11 + "?type=(3,4)";
/* 360 */         str13 = str13 + "&whereClause= type in (3,4)";
/*     */       } 
/* 362 */       if (null != paramString2 && !paramString2.equals("")) {
/* 363 */         XssUtil xssUtil = new XssUtil();
/* 364 */         str11 = str11 + "?sqlwhere=" + xssUtil.put(paramString2);
/* 365 */         str13 = str13 + "&whereClause=" + xssUtil.put(paramString2.replace("where", " "));
/*     */       } 
/* 367 */       String str14 = "isSingle:" + (browserComInfo.getBrowserdbtype(str4).toUpperCase().equals("INT") ? "true" : "false") + ",";
/* 368 */       if (str4.equals("161") || str4.equals("162")) {
/* 369 */         if (str9.indexOf(".") != -1) {
/* 370 */           String str16 = str9.substring(str9.indexOf(".") + 1);
/* 371 */           String str17 = "select detailpageurl from datashowset where showname=?";
/* 372 */           recordSet.executeQuery(str17, new Object[] { str16 });
/* 373 */           if (recordSet.next()) {
/* 374 */             str12 = recordSet.getString(1);
/*     */           }
/*     */         } 
/* 377 */         str11 = browserComInfo.getBrowserurl(str4 + "");
/* 378 */         if (str11.endsWith(".jsp")) {
/* 379 */           str11 = str11 + "?type=" + str9 + "|" + paramString1 + "&mouldID=crm&selectedids=";
/*     */         } else {
/* 381 */           str11 = str11 + "&type=" + str9 + "|" + paramString1 + "&mouldID=crm&selectedids=";
/*     */         } 
/* 383 */         if (str9 != null) {
/* 384 */           str13 = str13 + "&fielddbtype=" + str9;
/*     */         }
/* 386 */         if (str4.equals("161")) {
/* 387 */           str14 = "isSingle:true,";
/*     */         } else {
/* 389 */           str14 = "isSingle:false,";
/*     */         } 
/*     */       } 
/*     */       
/* 393 */       str1 = "<button class=Browser  type='button' onclick='onShowBrowser('" + str2 + "','" + str11 + "','" + str12 + "','" + str4 + "','" + j + "') title=" + SystemEnv.getHtmlLabelName(172, paramUser.getLanguage()) + "></button>";
/* 394 */       str1 = str1 + "<input type=hidden name=" + str2 + " _mand='" + j + "'>";
/*     */       
/* 396 */       String str15 = getFieldvalue(paramUser, Util.getIntValue(str4), paramString1, paramCrmFieldComInfo.getDmlurl());
/* 397 */       stringBuilder.append("<span class='browser' id='field" + str2 + "_span_n'></span>")
/* 398 */         .append("<script type='text/javascript'>")
/* 399 */         .append("$('#field" + str2 + "_span_n').e8Browser({")
/* 400 */         .append("name:'" + str2 + "',")
/* 401 */         .append("viewType:'0',")
/* 402 */         .append("browserValue:'" + paramString1 + "',")
/* 403 */         .append("isMustInput:'" + ((j == 1) ? "2" : "1") + "',")
/* 404 */         .append("browserSpanValue:'" + str15 + "',")
/* 405 */         .append("hasInput:'true',")
/* 406 */         .append("linkUrl:'" + str12 + "',")
/* 407 */         .append(str14)
/* 408 */         .append("completeUrl:'" + str13 + "',")
/* 409 */         .append("browserUrl:'" + str11 + "',")
/* 410 */         .append("width:'180px;',")
/* 411 */         .append("hasAdd:false,");
/* 412 */       if (paramString3.equals("edit")) {
/* 413 */         stringBuilder.append("_callback:'callBackSelectUpdate',").append("_callbackParams:'" + paramString1 + "'");
/* 414 */         stringBuilder.append(",afterDelCallback:'callBackSelectDelete'").append(",afterDelParams:'" + paramString1 + "'");
/*     */       } 
/*     */       
/* 417 */       stringBuilder.append("});")
/* 418 */         .append("</script>")
/* 419 */         .append("");
/*     */       
/* 421 */       return stringBuilder.toString();
/*     */     } 
/*     */     
/* 424 */     if (i == 4) {
/* 425 */       String str = paramString1.equals("1") ? "checked=checked" : "";
/* 426 */       if (paramString3.equals("edit")) {
/* 427 */         str1 = "<input type='checkbox'  class=item_checkbox id=" + str2 + "  name=" + str2 + " value='1' " + str + ">";
/*     */       } else {
/* 429 */         str1 = "<input type='checkbox'  id=" + str2 + "  name=" + str2 + " value='1' " + str + ">";
/*     */       } 
/*     */       
/* 432 */       return str1;
/*     */     } 
/*     */     
/* 435 */     if (i == 5) {
/*     */       
/* 437 */       String str = "";
/* 438 */       if (!paramString3.equals("search") && j == 1) {
/* 439 */         str = str + "checkinput('" + str2 + "','" + str3 + "');";
/*     */       }
/*     */       
/* 442 */       if (paramString3.equals("edit")) {
/* 443 */         str = str + "doUpdate(this,1);";
/* 444 */         str = str + "selectchange(this,'" + str2 + "','" + str3 + "'," + j + ");";
/*     */       } 
/* 446 */       if (paramString3.equals("add")) {
/* 447 */         str = str + "selectchange(this,'" + str2 + "','" + str3 + "'," + j + ");";
/*     */       }
/* 449 */       str1 = "<select style='width: 172px' id=" + str2 + " onchange=" + str + "; name=" + str2 + " isMustInput='" + j + "'>";
/* 450 */       if (paramString3.equals("search")) {
/* 451 */         str1 = str1 + "<option value=''>" + SystemEnv.getHtmlLabelName(332, paramUser.getLanguage()) + "</option>";
/*     */       } else {
/*     */         
/* 454 */         str1 = str1 + "<option value=''></option>";
/*     */       } 
/*     */       
/* 457 */       if (!paramCrmFieldComInfo.getSeltablename().equals("")) {
/* 458 */         recordSet.execute("select " + paramCrmFieldComInfo.getSelkeycolumname() + " , " + paramCrmFieldComInfo.getSelcolumname() + " from " + paramCrmFieldComInfo
/* 459 */             .getSeltablename() + " order by " + paramCrmFieldComInfo
/* 460 */             .getSelkeycolumname() + " asc  ");
/* 461 */         while (recordSet.next()) {
/* 462 */           String str11 = recordSet.getString(1).equals(paramString1) ? "selected=selected" : "";
/* 463 */           str1 = str1 + "<option " + str11 + " value=" + recordSet.getString(1) + ">" + recordSet.getString(2) + "</option>";
/*     */         } 
/*     */       } else {
/* 466 */         recordSet.execute("select selectvalue , selectname from crm_selectitem where fieldid = " + paramCrmFieldComInfo.getId() + " and isdel = 0 order by fieldorder asc");
/* 467 */         while (recordSet.next()) {
/* 468 */           String str11 = recordSet.getString("selectvalue").equals(paramString1) ? "selected=selected" : "";
/* 469 */           str1 = str1 + "<option " + str11 + " value=" + recordSet.getString("selectvalue") + ">" + recordSet.getString("selectname") + "</option>";
/*     */         } 
/*     */       } 
/*     */ 
/*     */       
/* 474 */       str1 = str1 + "</select>";
/* 475 */       if (!paramString3.equals("search")) str1 = str1 + str7 + str8; 
/* 476 */       return str1;
/*     */     } 
/*     */     
/* 479 */     if (i == 6) {
/*     */       
/* 481 */       byte b1 = 100;
/* 482 */       byte b2 = 10;
/* 483 */       str1 = str1 + "<div  name='uploadDiv'  usetable='" + paramCrmFieldComInfo.getUsetable() + "' fieldName='" + str2 + "' fieldNameSpan='" + str3 + "' ismust='" + paramCrmFieldComInfo.getIsmust() + "'   maxsize=" + b1 + "  uploadLimit=" + b2 + " checkinputImage = \"" + str7 + "\"></div>";
/*     */       
/* 485 */       if (!"".equals(paramString1)) {
/* 486 */         recordSet.execute("select * from ImageFile  where imagefileid in (" + paramString1 + ")");
/* 487 */         StringBuffer stringBuffer = new StringBuffer();
/*     */         
/* 489 */         while (recordSet.next()) {
/*     */           String str11;
/* 491 */           float f = recordSet.getFloat("fileSize") / 1024.0F;
/*     */           
/* 493 */           if (f == 0.0F) {
/* 494 */             str11 = "1.00 KB";
/* 495 */           } else if (f > 511.0F) {
/* 496 */             str11 = String.format("%.2f", new Object[] { Float.valueOf(f / 1024.0F) }) + " MB";
/*     */           } else {
/* 498 */             str11 = String.format("%.2f", new Object[] { Float.valueOf(f + 1.0F) }) + " KB";
/*     */           } 
/* 500 */           String str12 = recordSet.getString("imagefileid");
/* 501 */           String str13 = recordSet.getString("imagefilename");
/*     */           
/* 503 */           stringBuffer.append("<div class='txtlink showcon txtlink" + str12 + "'  onmouseover='showdel(this)' onmouseout='hidedel(this)'>");
/* 504 */           stringBuffer.append("<div style='float: left;'>");
/* 505 */           stringBuffer.append(str13 + "&nbsp;<a href='" + GCONST.getContextPath() + "/weaver/weaver.file.FileDownload?fileid=" + str12 + "&download=1'>" + SystemEnv.getHtmlLabelName(31156, paramUser.getLanguage()) + "(" + str11 + ")</a>");
/* 506 */           stringBuffer.append("</div>");
/* 507 */           stringBuffer.append("<div class='btn_del' onclick=exeUpdate('" + str2 + "','','attachment','" + str12 + "')></div>");
/* 508 */           stringBuffer.append("<div class='btn_wh'></div>");
/* 509 */           stringBuffer.append("</div>");
/*     */         } 
/* 511 */         str1 = str1 + stringBuffer.toString();
/*     */       } 
/*     */       
/* 514 */       return str1;
/*     */     } 
/*     */     
/* 517 */     return null;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getFieldvalue(String paramString1, String paramString2) {
/* 527 */     String str = "";
/*     */     try {
/* 529 */       RecordSet recordSet = new RecordSet();
/* 530 */       recordSet.execute("select selectname from crm_selectitem where fieldid = '" + paramString1 + "' and selectvalue = '" + paramString2 + "'");
/* 531 */       if (recordSet.next()) {
/* 532 */         return recordSet.getString("selectname");
/*     */       }
/* 534 */     } catch (Exception exception) {
/* 535 */       writeLog(exception);
/*     */     } 
/* 537 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getFieldvalue(User paramUser, int paramInt, String paramString1, String paramString2) {
/* 546 */     String str = "";
/*     */     try {
/* 548 */       RecordSet recordSet = new RecordSet();
/* 549 */       ArrayList<String> arrayList = Util.TokenizerString(paramString1, ",");
/* 550 */       if (paramInt == 1 || paramInt == 17) {
/* 551 */         for (byte b = 0; b < arrayList.size(); b++)
/*     */         {
/* 553 */           str = str + (new ResourceComInfo()).getResourcename(arrayList.get(b)) + ",";
/*     */         }
/*     */       }
/* 556 */       else if (paramInt == 2 || paramInt == 19) {
/*     */         
/* 558 */         str = str + paramString1;
/* 559 */       } else if (paramInt == 4 || paramInt == 57) {
/* 560 */         for (byte b = 0; b < arrayList.size(); b++)
/*     */         {
/* 562 */           str = str + (new DepartmentComInfo()).getDepartmentname(arrayList.get(b)) + ",";
/*     */         }
/*     */       }
/* 565 */       else if (paramInt == 8 || paramInt == 135) {
/* 566 */         for (byte b = 0; b < arrayList.size(); b++)
/*     */         {
/* 568 */           str = str + (new ProjectInfoComInfo()).getProjectInfoname(arrayList.get(b)) + ",";
/*     */         }
/*     */       }
/* 571 */       else if (paramInt == 7 || paramInt == 18) {
/* 572 */         for (byte b = 0; b < arrayList.size(); b++)
/*     */         {
/* 574 */           str = str + (new CustomerInfoComInfo()).getCustomerInfoname(arrayList.get(b)) + ",";
/*     */         }
/*     */       }
/* 577 */       else if (paramInt == 164) {
/* 578 */         for (byte b = 0; b < arrayList.size(); b++)
/*     */         {
/* 580 */           str = str + (new SubCompanyComInfo()).getSubCompanyname(arrayList.get(b)) + ",";
/*     */         }
/*     */       }
/* 583 */       else if (paramInt == 9) {
/* 584 */         for (byte b = 0; b < arrayList.size(); b++)
/*     */         {
/* 586 */           str = str + (new DocComInfo()).getDocname(arrayList.get(b));
/*     */         }
/* 588 */       } else if (paramInt == 37) {
/* 589 */         for (byte b = 0; b < arrayList.size(); b++)
/*     */         {
/* 591 */           str = str + (new DocComInfo()).getDocname(arrayList.get(b)) + ",";
/*     */         }
/* 593 */       } else if (paramInt == 23) {
/* 594 */         for (byte b = 0; b < arrayList.size(); b++)
/*     */         {
/* 596 */           str = str + (new CapitalComInfo()).getCapitalname(arrayList.get(b)) + ",";
/*     */         }
/*     */       }
/* 599 */       else if (paramInt == 16 || paramInt == 152) {
/* 600 */         for (byte b = 0; b < arrayList.size(); b++)
/*     */         {
/* 602 */           str = str + (new WorkflowRequestComInfo()).getRequestName(arrayList.get(b)) + ",";
/*     */         }
/*     */       }
/* 605 */       else if (paramInt == 142) {
/* 606 */         DocReceiveUnitComInfo docReceiveUnitComInfo = new DocReceiveUnitComInfo();
/* 607 */         for (byte b = 0; b < arrayList.size(); b++)
/*     */         {
/* 609 */           str = str + docReceiveUnitComInfo.getReceiveUnitName(arrayList.get(b)) + ",";
/*     */         }
/*     */       }
/* 612 */       else if (paramInt == 226 || paramInt == 227) {
/* 613 */         str = str + paramString1;
/* 614 */       } else if (paramInt == 161 || paramInt == 162) {
/* 615 */         if (Util.null2String(paramString2).length() == 0) return ""; 
/*     */         try {
/* 617 */           Browser browser = (Browser)StaticObj.getServiceByFullname(paramString2, Browser.class);
/* 618 */           for (byte b = 0; b < arrayList.size(); b++) {
/* 619 */             BrowserBean browserBean = browser.searchById(arrayList.get(b));
/* 620 */             String str1 = Util.null2String(browserBean.getName());
/* 621 */             if (str.equals("")) {
/* 622 */               str = str + str1;
/*     */             } else {
/* 624 */               str = str + "," + str1;
/*     */             } 
/*     */           } 
/* 627 */         } catch (Exception exception) {
/*     */           
/* 629 */           writeLog(exception);
/*     */         } 
/*     */       } else {
/*     */         
/* 633 */         String str1 = "";
/* 634 */         String str2 = (new BrowserComInfo()).getBrowsertablename("" + paramInt);
/*     */         
/* 636 */         String str3 = (new BrowserComInfo()).getBrowsercolumname("" + paramInt);
/*     */ 
/*     */         
/* 639 */         String str4 = (new BrowserComInfo()).getBrowserkeycolumname("" + paramInt);
/* 640 */         if (!str3.equals("") && !str2.equals("") && 
/* 641 */           !str4.equals("") && !paramString1.equals("")) {
/*     */           
/* 643 */           str1 = "select " + str3 + " from " + str2 + " where " + str4 + " in(" + paramString1 + ")";
/*     */ 
/*     */           
/* 646 */           recordSet.executeSql(str1);
/* 647 */           while (recordSet.next()) {
/* 648 */             str = str + recordSet.getString(1) + ",";
/*     */           }
/*     */         } 
/*     */       } 
/* 652 */       if (str.endsWith(",")) {
/* 653 */         str = str.substring(0, str.length() - 1);
/*     */       }
/* 655 */     } catch (Exception exception) {
/* 656 */       writeLog(exception);
/*     */     } 
/*     */     
/* 659 */     return str;
/*     */   }
/*     */   
/*     */   public String getFieldIsMustInfo(String paramString) {
/* 663 */     RecordSet recordSet = new RecordSet();
/* 664 */     recordSet.execute("SELECT fieldname FROM CRM_CustomerDefinField WHERE ismust = 1 and usetable = '" + paramString + "' and isopen = 1");
/* 665 */     String str = "";
/* 666 */     while (recordSet.next()) {
/* 667 */       str = str.equals("") ? recordSet.getString("fieldname") : (str + "," + recordSet.getString("fieldname"));
/*     */     }
/* 669 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean getFieldIsOpen(String paramString1, String paramString2) {
/* 680 */     boolean bool = false;
/* 681 */     RecordSet recordSet = new RecordSet();
/* 682 */     recordSet.execute("SELECT fieldname FROM CRM_CustomerDefinField WHERE fieldname='" + paramString2 + "' and usetable = '" + paramString1 + "' and isopen = 1");
/* 683 */     if (recordSet.next()) {
/* 684 */       bool = true;
/*     */     }
/* 686 */     return bool;
/*     */   }
/*     */   
/*     */   public static boolean isInSeas(String paramString) {
/* 690 */     boolean bool = false;
/* 691 */     String str = (new CustomerInfoComInfo()).getSeasFlag(paramString);
/* 692 */     if (str.equals("1") || str.equals("2")) {
/* 693 */       bool = true;
/*     */     }
/* 695 */     return bool;
/*     */   }
/*     */   
/*     */   public static String getCustomerNameCommon(String paramString, User paramUser) {
/* 699 */     CustomerInfoComInfo customerInfoComInfo = new CustomerInfoComInfo();
/* 700 */     String str = customerInfoComInfo.getCustomerInfoname(paramString);
/* 701 */     boolean bool = isInSeas(paramString);
/* 702 */     if (bool) {
/* 703 */       str = str + "(" + SystemEnv.getHtmlLabelNames("532570", paramUser.getLanguage()) + ")";
/*     */     }
/* 705 */     return str;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/crm/util/CrmUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */