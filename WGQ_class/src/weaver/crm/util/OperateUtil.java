/*     */ package weaver.crm.util;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.WorkPlan.WorkPlanLogMan;
/*     */ import weaver.WorkPlan.WorkPlanService;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.crm.CrmShareBase;
/*     */ import weaver.crm.Maint.CustomerInfoComInfo;
/*     */ import weaver.domain.workplan.WorkPlan;
/*     */ import weaver.file.Prop;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.GCONST;
/*     */ import weaver.general.TimeUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.soa.workflow.request.MainTableInfo;
/*     */ import weaver.soa.workflow.request.Property;
/*     */ import weaver.soa.workflow.request.RequestInfo;
/*     */ import weaver.soa.workflow.request.RequestService;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class OperateUtil
/*     */   extends BaseBean
/*     */ {
/*     */   public static int createSellchanceRequest(String paramString1, String paramString2, String paramString3, String paramString4, String paramString5, String paramString6, String paramString7) throws Exception {
/*  45 */     return createRemindRequest("574", paramString1, paramString2, paramString3, paramString4, paramString5, paramString6, paramString7);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static int createRemindRequest(String paramString1, String paramString2, String paramString3, String paramString4, String paramString5, String paramString6, String paramString7) throws Exception {
/*  61 */     return createRemindRequest("518", paramString1, paramString2, paramString3, paramString4, paramString5, paramString6, paramString7);
/*     */   }
/*     */   
/*     */   public static int createRemindRequest(String paramString1, String paramString2, String paramString3, String paramString4, String paramString5, String paramString6, String paramString7, int paramInt) throws Exception {
/*  65 */     return createRemindRequest("518", paramString1, paramString2, paramString3, paramString4, paramString5, paramString6, paramString7, paramInt);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static int createRemindRequest(String paramString1, String paramString2, String paramString3, String paramString4, String paramString5, String paramString6, String paramString7, String paramString8) throws Exception {
/*  81 */     return createRemindRequest(paramString1, paramString2, paramString3, paramString4, paramString5, paramString6, paramString7, paramString8, 0);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static int createRemindRequest(String paramString1, String paramString2, String paramString3, String paramString4, String paramString5, String paramString6, String paramString7, String paramString8, int paramInt) throws Exception {
/*  97 */     return createRemindRequest(paramString1, paramString2, paramString3, paramString4, paramString5, paramString6, paramString7, paramString8, paramInt, "0", "");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static int createRemindRequest(String paramString1, String paramString2, String paramString3, String paramString4, String paramString5, String paramString6, String paramString7, String paramString8, int paramInt, String paramString9, String paramString10) throws Exception {
/* 106 */     if (paramString5.equals(paramString6)) return -1;
/*     */     
/* 108 */     return createRemindRequestBase(paramString1, paramString2, paramString3, paramString4, paramString5, paramString6, paramString7, paramString8, paramInt, paramString9, paramString10);
/*     */   }
/*     */   
/*     */   public static int createRemindRequestBase(String paramString1, String paramString2, String paramString3, String paramString4, String paramString5, String paramString6, String paramString7, String paramString8, int paramInt, String paramString9, String paramString10) throws Exception {
/* 112 */     return createRemindRequestBase(paramString1, paramString2, paramString3, paramString4, paramString5, paramString6, paramString7, paramString8, paramInt, paramString9, paramString10, "7");
/*     */   }
/*     */   public static int createRemindRequestBase(String paramString1, String paramString2, String paramString3, String paramString4, String paramString5, String paramString6, String paramString7, String paramString8, int paramInt, String paramString9, String paramString10, String paramString11) throws Exception {
/* 115 */     if ("".equals(paramString3) || "".equals(paramString6)) {
/* 116 */       return -1;
/*     */     }
/* 118 */     if (0 != paramInt) {
/*     */       
/* 120 */       String str1 = "";
/* 121 */       switch (paramInt) {
/*     */         case 1:
/* 123 */           str1 = Util.null2String(Prop.getPropValue("CRM_notRemindHrms", "hrmids"));
/*     */           break;
/*     */         case 2:
/* 126 */           str1 = Util.null2String(Prop.getPropValue("CRM_notRemindHrms", "hrmids1"));
/*     */           break;
/*     */         case 3:
/* 129 */           str1 = Util.null2String(Prop.getPropValue("CRM_notRemindHrms", "hrmids2"));
/*     */           break;
/*     */       } 
/* 132 */       if (str1.contains("," + paramString6 + ",")) {
/* 133 */         return -1;
/*     */       }
/*     */     } 
/*     */     
/* 137 */     if ("".equals(paramString5)) paramString5 = "1"; 
/* 138 */     CustomerInfoComInfo customerInfoComInfo = new CustomerInfoComInfo();
/* 139 */     String str = customerInfoComInfo.getCustomerInfoname(paramString3);
/*     */     
/* 141 */     if ("".equals(paramString2)) {
/* 142 */       paramString2 = SystemEnv.getHtmlLabelName(501775, Integer.parseInt(paramString11)) + "：" + str;
/*     */     }
/*     */     
/* 145 */     if ("".equals(paramString8))
/*     */     {
/* 147 */       paramString8 = SystemEnv.getHtmlLabelName(501776, Integer.parseInt(paramString11)) + "：<a class='clink' href='" + GCONST.getContextPath() + "/mobile/plugin/crm/manage/CustomerView.jsp?id=" + paramString3 + "&fromreq=1' target='_blank'>" + str + "</a>，" + SystemEnv.getHtmlLabelName(501777, Integer.parseInt(paramString11)) + "！<a class='clink' href='" + GCONST.getContextPath() + "/mobile/plugin/crm/manage/CustomerView.jsp?id=" + paramString3 + "&fromreq=1' target='_blank'>" + SystemEnv.getHtmlLabelName(82279, Integer.parseInt(paramString11)) + "</a>";
/*     */     }
/*     */     
/* 150 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 151 */     hashMap.put("cjr", paramString5);
/* 152 */     hashMap.put("cjrq", TimeUtil.getCurrentDateString());
/* 153 */     hashMap.put("kh", paramString3);
/* 154 */     if (!"".equals(paramString4)) hashMap.put("sj", paramString4); 
/* 155 */     hashMap.put("txyj", paramString8);
/* 156 */     hashMap.put("jsr", paramString6);
/* 157 */     hashMap.put("csr", paramString7);
/* 158 */     hashMap.put("txlx", paramString9);
/* 159 */     hashMap.put("lxfksm", paramString10);
/*     */     
/* 161 */     int i = createRequest(paramString1, paramString2, paramString5, hashMap, "1");
/* 162 */     if (i > 0) {
/* 163 */       RecordSet recordSet = new RecordSet();
/* 164 */       recordSet.executeSql("update workflow_requestbase set crmids='" + paramString3 + "' where requestid=" + i);
/*     */     } 
/* 166 */     return i;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static int createRequest(String paramString1, String paramString2, String paramString3, Map paramMap, String paramString4) {
/* 178 */     return createRequest(paramString1, paramString2, paramString3, paramMap, paramString4, "7");
/*     */   }
/*     */   public static int createRequest(String paramString1, String paramString2, String paramString3, Map paramMap, String paramString4, String paramString5) {
/* 181 */     String str = "";
/*     */     try {
/* 183 */       if ("".equals(paramString1)) {
/* 184 */         return -1;
/*     */       }
/*     */       
/* 187 */       RequestService requestService = new RequestService();
/* 188 */       RequestInfo requestInfo = new RequestInfo();
/*     */       
/* 190 */       requestInfo.setRequestid("");
/* 191 */       requestInfo.setWorkflowid(paramString1);
/* 192 */       requestInfo.setCreatorid(paramString3);
/* 193 */       requestInfo.setDescription(paramString2);
/* 194 */       requestInfo.setRequestlevel("0");
/*     */       
/* 196 */       requestInfo.setIsNextFlow(paramString4);
/* 197 */       requestInfo.set_Remark(SystemEnv.getHtmlLabelName(501780, Integer.parseInt(paramString5)) + "！");
/*     */       
/* 199 */       MainTableInfo mainTableInfo = new MainTableInfo();
/* 200 */       ArrayList<Property> arrayList = new ArrayList();
/* 201 */       Property property = null;
/*     */       
/* 203 */       for (Map.Entry entry : paramMap.entrySet()) {
/*     */         
/* 205 */         property = new Property();
/* 206 */         property.setName((String)((Map.Entry)entry).getKey());
/* 207 */         property.setValue((String)entry.getValue());
/* 208 */         arrayList.add(property);
/*     */       } 
/*     */ 
/*     */       
/* 212 */       Property[] arrayOfProperty = arrayList.<Property>toArray(new Property[arrayList.size()]);
/* 213 */       mainTableInfo.setProperty(arrayOfProperty);
/* 214 */       requestInfo.setMainTableInfo(mainTableInfo);
/*     */       
/* 216 */       str = requestService.createRequest(requestInfo);
/*     */     }
/* 218 */     catch (Exception exception) {
/* 219 */       (new BaseBean()).writeLog(SystemEnv.getHtmlLabelName(501788, Integer.parseInt(paramString5)) + "workflowid:" + paramString1 + "---requestname:" + paramString2 + "！" + exception);
/* 220 */       return 0;
/*     */     } 
/*     */     
/* 223 */     return Util.getIntValue(str, 0);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String addContactRecord(String paramString, User paramUser, Map paramMap) {
/* 234 */     String str = "";
/*     */     try {
/* 236 */       String str1 = paramUser.getUID() + "";
/*     */       
/* 238 */       if (!checkRight(paramString, str1, 1, 1)) return "";
/*     */       
/* 240 */       String str2 = Util.null2String((String)paramMap.get("relatedprj"));
/* 241 */       String str3 = Util.null2String((String)paramMap.get("relatedcus"));
/* 242 */       String str4 = Util.null2String((String)paramMap.get("relatedwf"));
/* 243 */       String str5 = Util.null2String((String)paramMap.get("relateddoc"));
/* 244 */       String str6 = Util.convertInput2DB((String)paramMap.get("ContactInfo"));
/* 245 */       String str7 = Util.null2String((String)paramMap.get("begindate"));
/* 246 */       String str8 = Util.null2String((String)paramMap.get("begintime"));
/* 247 */       String str9 = Util.null2String((String)paramMap.get("enddate"));
/* 248 */       String str10 = Util.null2String((String)paramMap.get("endtime"));
/* 249 */       if (str7.equals("")) str7 = TimeUtil.getCurrentDateString(); 
/* 250 */       if (!str7.equals(TimeUtil.getCurrentDateString())) str8 = "00:00"; 
/* 251 */       if (str8.equals("")) str8 = TimeUtil.getOnlyCurrentTimeString().substring(0, 5);
/*     */       
/* 253 */       if (("," + str3 + ",").indexOf("," + paramString + ",") == -1)
/*     */       {
/* 255 */         str3 = paramString + "," + str3;
/*     */       }
/* 257 */       CustomerInfoComInfo customerInfoComInfo = new CustomerInfoComInfo();
/*     */       
/* 259 */       WorkPlan workPlan = new WorkPlan();
/* 260 */       workPlan.setCreaterId(paramUser.getUID());
/* 261 */       workPlan.setCreateType(Integer.parseInt(paramUser.getLogintype()));
/* 262 */       workPlan.setWorkPlanType(Integer.parseInt("3"));
/* 263 */       workPlan.setWorkPlanName(customerInfoComInfo.getCustomerInfoname(paramString) + "-" + SystemEnv.getHtmlLabelName(6082, paramUser.getLanguage()));
/* 264 */       workPlan.setUrgentLevel("1");
/* 265 */       workPlan.setRemindType("1");
/* 266 */       workPlan.setResourceId(String.valueOf(paramUser.getUID()));
/* 267 */       workPlan.setBeginDate(str7);
/* 268 */       workPlan.setBeginTime(str8);
/* 269 */       workPlan.setEndDate(str9);
/* 270 */       workPlan.setEndTime(str10);
/*     */       
/* 272 */       workPlan.setDescription(str6);
/* 273 */       workPlan.setStatus("2");
/*     */       
/* 275 */       workPlan.setCustomer(str3);
/* 276 */       workPlan.setDocument(str5);
/* 277 */       workPlan.setWorkflow(str4);
/* 278 */       workPlan.setTask(str2);
/*     */       
/* 280 */       WorkPlanService workPlanService = new WorkPlanService();
/*     */       
/* 282 */       workPlanService.insertWorkPlan(workPlan);
/*     */       
/* 284 */       str = workPlan.getWorkPlanID() + "";
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 289 */       String[] arrayOfString = { str, "1", str1, Util.null2String((String)paramMap.get("remoteAddr")) };
/* 290 */       WorkPlanLogMan workPlanLogMan = new WorkPlanLogMan();
/* 291 */       workPlanLogMan.writeViewLog(arrayOfString);
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 296 */       RecordSet recordSet = new RecordSet();
/*     */ 
/*     */       
/* 299 */       String str11 = Util.null2String((String)paramMap.get("relatedfile"));
/* 300 */       str11 = cutString(str11, ",", 3);
/* 301 */       if (!str11.equals("")) {
/* 302 */         recordSet.executeSql("update WorkPlan set relateddoc='," + str11 + ",' where id=" + str);
/*     */       }
/*     */       
/* 305 */       String str12 = Util.null2String((String)paramMap.get("sellchanceid"));
/*     */       
/* 307 */       String str13 = Util.null2String((String)paramMap.get("contacterid"));
/* 308 */       if (!str12.equals("")) {
/* 309 */         recordSet.executeSql("update WorkPlan set sellchanceid=" + str12 + " where id=" + str);
/*     */ 
/*     */         
/* 312 */         recordSet.executeSql("select id from CS_CustomerSellChance where sellchanceid=" + str12);
/* 313 */         if (recordSet.next()) {
/* 314 */           String str14 = Util.null2String(recordSet.getString(1));
/* 315 */           if (!str14.equals("")) {
/* 316 */             str14 = "," + str14 + ",";
/*     */             
/* 318 */             char c = Util.getSeparator();
/* 319 */             StringBuffer stringBuffer = new StringBuffer();
/* 320 */             stringBuffer.append(customerInfoComInfo.getCustomerInfoname(paramString) + "(" + str7 + " " + str8 + ")" + c);
/* 321 */             stringBuffer.append(paramString + c);
/* 322 */             stringBuffer.append(str13 + c);
/* 323 */             stringBuffer.append(paramUser.getUID() + "" + c);
/* 324 */             stringBuffer.append(str7 + c);
/* 325 */             stringBuffer.append(str8 + c);
/* 326 */             stringBuffer.append(str7 + c);
/* 327 */             stringBuffer.append(str8 + c);
/* 328 */             stringBuffer.append("1" + c);
/* 329 */             stringBuffer.append(str6 + c);
/* 330 */             stringBuffer.append("" + c);
/* 331 */             stringBuffer.append("" + c);
/* 332 */             stringBuffer.append(str14 + c);
/* 333 */             stringBuffer.append("" + c);
/* 334 */             stringBuffer.append("");
/*     */             
/* 336 */             recordSet.executeProc("CS_CustomerContactRecord_Insert", stringBuffer.toString());
/* 337 */             if (recordSet.next()) {
/* 338 */               String str15 = Util.null2String(recordSet.getString(1));
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */               
/* 345 */               recordSet.executeProc("CS_ContactRecordContent_Insert", str15 + c + "27");
/*     */               
/* 347 */               String str16 = TimeUtil.getCurrentDateString();
/* 348 */               String str17 = TimeUtil.getOnlyCurrentTimeString();
/* 349 */               stringBuffer = new StringBuffer();
/* 350 */               stringBuffer.append(str15 + c);
/* 351 */               stringBuffer.append(str16 + c);
/* 352 */               stringBuffer.append(str17 + c);
/* 353 */               stringBuffer.append("" + paramUser.getUID() + c);
/* 354 */               stringBuffer.append(paramUser.getLoginip() + c);
/* 355 */               stringBuffer.append("1");
/*     */               
/* 357 */               recordSet.executeProc("CS_CustomerContactRecordLog_Insert", stringBuffer.toString());
/*     */ 
/*     */               
/* 360 */               updateLastDate2(str14);
/*     */             } 
/*     */           } 
/*     */         } 
/*     */       } 
/* 365 */       if (!str13.equals("")) {
/* 366 */         recordSet.executeSql("update WorkPlan set contacterid=" + str13 + " where id=" + str);
/*     */       }
/* 368 */     } catch (Exception exception) {
/* 369 */       writeLog("创建客户联系记录出错！" + exception);
/* 370 */       return "";
/*     */     } 
/* 372 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean checkRight(String paramString1, String paramString2, int paramInt1, int paramInt2) throws Exception {
/* 386 */     CrmShareBase crmShareBase = new CrmShareBase();
/* 387 */     RecordSet recordSet = new RecordSet();
/* 388 */     String str = "";
/* 389 */     if (paramInt1 == 1) {
/* 390 */       str = paramString1;
/* 391 */     } else if (paramInt1 == 2) {
/* 392 */       if (!"".equals(paramString1)) {
/*     */         
/* 394 */         recordSet.executeSql("select t.customerid from CRM_SellChance t where t.id=" + paramString1);
/* 395 */         if (recordSet.next()) str = Util.null2String(recordSet.getString(1));
/*     */       
/*     */       } 
/* 398 */     } else if (!"".equals(paramString1)) {
/*     */       
/* 400 */       recordSet.executeSql("select t.customerid from CRM_CustomerContacter t where t.id=" + paramString1);
/* 401 */       if (recordSet.next()) str = Util.null2String(recordSet.getString(1));
/*     */     
/*     */     } 
/*     */     
/* 405 */     if (!str.equals("")) {
/*     */       
/* 407 */       recordSet.executeProc("CRM_CustomerInfo_SelectByID", str);
/* 408 */       if (!recordSet.next()) {
/* 409 */         return false;
/*     */       }
/* 411 */       int i = crmShareBase.getRightLevelForCRM(paramString2, str);
/* 412 */       if (paramInt2 == 1) {
/*     */         
/* 414 */         if (i < 1) {
/* 415 */           return false;
/*     */         }
/*     */       } else {
/*     */         
/* 419 */         if (i < 2) {
/* 420 */           return false;
/*     */         }
/* 422 */         if (recordSet.getInt("status") == 7 || recordSet.getInt("status") == 8 || recordSet.getInt("status") == 10) {
/* 423 */           return false;
/*     */         }
/*     */       } 
/* 426 */       return true;
/*     */     } 
/* 428 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void updateLastDate2(String paramString) {
/* 436 */     RecordSet recordSet1 = new RecordSet();
/* 437 */     RecordSet recordSet2 = new RecordSet();
/* 438 */     ArrayList<String> arrayList = Util.TokenizerString(paramString, ",");
/* 439 */     String str = "";
/* 440 */     for (byte b = 0; b < arrayList.size(); b++) {
/* 441 */       str = arrayList.get(b);
/* 442 */       if (!"".equals(str)) {
/* 443 */         recordSet2.executeSql("delete from CS_LastSellChanceDate where sellchanceId=" + str);
/* 444 */         recordSet1.executeSql("select top 1 id,startDate,startTime from CS_CustomerContactRecord where sellchanceIds like '%," + str + ",%' order by startDate desc,startTime desc");
/* 445 */         if (recordSet1.next()) {
/* 446 */           recordSet2.executeSql("insert into CS_LastSellChanceDate (sellchanceId,recordId,lastDate,lastTime) values(" + str + "," + recordSet1.getString("id") + ",'" + recordSet1.getString("startDate") + "','" + recordSet1.getString("startTime") + "')");
/*     */         }
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String cutString(String paramString1, String paramString2, int paramInt) {
/* 460 */     paramString1 = Util.null2String(paramString1);
/* 461 */     paramString2 = Util.null2String(paramString2);
/* 462 */     if (paramString1.equals("") || paramString2.equals("")) {
/* 463 */       return paramString1;
/*     */     }
/* 465 */     if ((paramInt == 1 || paramInt == 3) && 
/* 466 */       paramString1.startsWith(paramString2)) {
/* 467 */       paramString1 = paramString1.substring(paramString2.length());
/*     */     }
/*     */     
/* 470 */     if ((paramInt == 2 || paramInt == 3) && 
/* 471 */       paramString1.endsWith(paramString2)) {
/* 472 */       paramString1 = paramString1.substring(0, paramString1.length() - paramString2.length());
/*     */     }
/*     */     
/* 475 */     return paramString1;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/crm/util/OperateUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */