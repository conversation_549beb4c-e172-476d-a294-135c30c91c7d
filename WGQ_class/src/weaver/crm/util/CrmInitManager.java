/*     */ package weaver.crm.util;
/*     */ import com.api.ecme.service.CrmModeInitServer;
/*     */ import com.api.ecme.service.ModuleInitService;
/*     */ import com.engine.crm.constant.CrmRemindWayEnum;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Arrays;
/*     */ import java.util.HashSet;
/*     */ import java.util.UUID;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.conn.RecordSetTrans;
/*     */ import weaver.general.ThreadVarLanguage;
/*     */ import weaver.general.TimeUtil;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ public class CrmInitManager extends BaseBean implements Runnable {
/*     */   public synchronized void run() {
/*  17 */     writeLog(getClass().getName() + " is running start...");
/*     */ 
/*     */     
/*  20 */     boolean bool = "1".equals(getPropValue("module", "crm.status"));
/*  21 */     if (bool) {
/*     */ 
/*     */       
/*  24 */       task1();
/*  25 */       task2();
/*  26 */       task5();
/*  27 */       task6();
/*  28 */       task7();
/*  29 */       task4crmLog();
/*     */     } else {
/*  31 */       task8();
/*     */     } 
/*     */     
/*  34 */     writeLog(getClass().getName() + " is running end...");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void task1() {
/*  41 */     RecordSet recordSet1 = new RecordSet();
/*  42 */     RecordSet recordSet2 = new RecordSet();
/*     */     
/*  44 */     boolean bool = false;
/*  45 */     String str = "";
/*  46 */     recordSet1.executeQuery("select id,fieldhtmltype from CRM_CustomerDefinField where fieldname='projectrole' and usetable='CRM_CustomerContacter'", new Object[0]);
/*  47 */     if (recordSet1.next()) {
/*  48 */       str = recordSet1.getString("id");
/*  49 */       String str1 = recordSet1.getString("fieldhtmltype");
/*  50 */       if (str1.equals("1")) {
/*  51 */         bool = true;
/*     */       }
/*     */     } 
/*  54 */     if (bool && !"".equals(str)) {
/*  55 */       RecordSetTrans recordSetTrans = new RecordSetTrans();
/*  56 */       byte b = 0;
/*     */       try {
/*  58 */         recordSetTrans.setAutoCommit(false);
/*  59 */         ArrayList<String> arrayList = new ArrayList();
/*  60 */         arrayList.add("" + SystemEnv.getHtmlLabelName(124914, ThreadVarLanguage.getLang()) + "");
/*  61 */         arrayList.add("" + SystemEnv.getHtmlLabelName(84246, ThreadVarLanguage.getLang()) + "");
/*  62 */         arrayList.add("" + SystemEnv.getHtmlLabelName(84248, ThreadVarLanguage.getLang()) + "");
/*  63 */         arrayList.add("" + SystemEnv.getHtmlLabelName(84249, ThreadVarLanguage.getLang()) + "");
/*  64 */         arrayList.add("" + SystemEnv.getHtmlLabelName(84251, ThreadVarLanguage.getLang()) + "");
/*  65 */         arrayList.add("" + SystemEnv.getHtmlLabelName(375, ThreadVarLanguage.getLang()) + "");
/*     */         
/*  67 */         for (byte b1 = 0; b1 < arrayList.size(); b1++) {
/*  68 */           String str1 = arrayList.get(b1);
/*  69 */           recordSet2.executeQuery("select fieldid from crm_selectitem where fieldid=? and selectname=?", new Object[] { str, str1 });
/*  70 */           if (!recordSet2.next())
/*     */           {
/*     */             
/*  73 */             recordSetTrans.executeSql("insert into crm_selectitem(fieldid,selectvalue,selectname,fieldorder,isdel) values(" + str + "," + b++ + ",'" + str1 + "'," + b + ",0)");
/*     */           }
/*     */         } 
/*     */         
/*  77 */         recordSet1.executeQuery("select DISTINCT projectrole from CRM_CustomerContacter where projectrole is not null and projectrole<>''", new Object[0]);
/*  78 */         while (recordSet1.next()) {
/*  79 */           String str1 = recordSet1.getString("projectrole");
/*  80 */           recordSet2.executeQuery("select fieldid from crm_selectitem where fieldid=? and selectname=?", new Object[] { str, str1 });
/*  81 */           if (recordSet2.next()) {
/*     */             continue;
/*     */           }
/*  84 */           if (!arrayList.contains(str1)) {
/*  85 */             recordSetTrans.executeSql("insert into crm_selectitem(fieldid,selectvalue,selectname,fieldorder,isdel) values(" + str + "," + b++ + ",'" + str1 + "'," + b + ",0)");
/*     */           }
/*     */         } 
/*     */ 
/*     */         
/*  90 */         if (recordSetTrans.getDBType().equals("sqlserver")) {
/*  91 */           recordSetTrans.executeSql("update CRM_CustomerContacter set projectrole=(select top 1 selectvalue from crm_selectitem where selectname=projectrole and fieldid=" + str + ") where projectrole is not null and projectrole<>''");
/*  92 */         } else if (recordSetTrans.getDBType().equals("mysql")) {
/*  93 */           recordSetTrans.executeSql("update CRM_CustomerContacter set projectrole=(select selectvalue from crm_selectitem where selectname=projectrole and fieldid=" + str + " limit 1) where projectrole is not null and projectrole<>''");
/*     */         }
/*  95 */         else if (recordSetTrans.getDBType().equals("postgresql")) {
/*  96 */           recordSetTrans.executeSql("update CRM_CustomerContacter set projectrole=(select selectvalue from crm_selectitem where selectname=projectrole and fieldid=" + str + " limit 1) where projectrole is not null and projectrole<>''");
/*     */         } else {
/*     */           
/*  99 */           recordSetTrans.executeSql("update CRM_CustomerContacter set projectrole=(select selectvalue from crm_selectitem where selectname=projectrole and fieldid=" + str + " and rownum=1) where projectrole is not null and projectrole<>''");
/*     */         } 
/*     */ 
/*     */         
/* 103 */         if (recordSetTrans.getDBType().equals("sqlserver")) {
/* 104 */           recordSetTrans.executeSql("alter table CRM_CustomerContacter alter column projectrole int");
/* 105 */         } else if (recordSetTrans.getDBType().equals("mysql")) {
/* 106 */           recordSetTrans.executeSql("SELECT 1 FROM information_schema.columns WHERE TABLE_SCHEMA=(select database()) and table_name='CRM_CUSTOMERCONTACTER' AND COLUMN_NAME='PROJECTROLE_BAK_TEMP'");
/* 107 */           boolean bool1 = recordSetTrans.next();
/* 108 */           if (!bool1) {
/* 109 */             recordSetTrans.executeSql("alter table CRM_CustomerContacter add projectrole_bak_temp VARCHAR(100)");
/*     */           }
/* 111 */           recordSetTrans.executeSql("update CRM_CustomerContacter set projectrole_bak_temp=projectrole");
/* 112 */           recordSetTrans.executeSql("update CRM_CustomerContacter set projectrole=null");
/* 113 */           recordSetTrans.executeSql("alter table CRM_CustomerContacter modify column projectrole int");
/* 114 */           recordSetTrans.executeSql("update CRM_CustomerContacter set projectrole=projectrole_bak_temp");
/*     */         
/*     */         }
/* 117 */         else if (recordSetTrans.getDBType().equals("postgresql")) {
/*     */           
/* 119 */           recordSetTrans.executeSql("SELECT 1 FROM information_schema.columns WHERE TABLE_SCHEMA='public' and lower(table_name)=lower('CRM_CUSTOMERCONTACTER') AND lower(COLUMN_NAME)=lower('PROJECTROLE_BAK_TEMP')");
/* 120 */           boolean bool1 = recordSetTrans.next();
/* 121 */           if (!bool1) {
/* 122 */             recordSetTrans.executeSql("alter table CRM_CustomerContacter add projectrole_bak_temp VARCHAR(100)");
/*     */           }
/* 124 */           recordSetTrans.executeSql("update CRM_CustomerContacter set projectrole_bak_temp=projectrole");
/* 125 */           recordSetTrans.executeSql("update CRM_CustomerContacter set projectrole=null");
/* 126 */           recordSetTrans.executeSql("alter table CRM_CustomerContacter alter column projectrole type int");
/* 127 */           recordSetTrans.executeSql("update CRM_CustomerContacter set projectrole=projectrole_bak_temp");
/*     */         } else {
/*     */           
/* 130 */           recordSetTrans.executeSql("select 1 from user_tab_columns t where t.table_name='CRM_CUSTOMERCONTACTER' and t.column_name ='PROJECTROLE_BAK_TEMP'");
/* 131 */           boolean bool1 = recordSetTrans.next();
/* 132 */           if (!bool1) {
/* 133 */             recordSetTrans.executeSql("alter table CRM_CustomerContacter add projectrole_bak_temp VARCHAR2(800)");
/*     */           }
/* 135 */           recordSetTrans.executeSql("update CRM_CustomerContacter set projectrole_bak_temp=projectrole");
/* 136 */           recordSetTrans.executeSql("update CRM_CustomerContacter set projectrole=null");
/*     */           
/* 138 */           if ("jc".equalsIgnoreCase(recordSetTrans.getOrgindbtype())) {
/* 139 */             recordSetTrans.executeSql("alter table CRM_CustomerContacter alter projectrole type integer");
/*     */           } else {
/* 141 */             recordSetTrans.executeSql("alter table CRM_CustomerContacter modify (projectrole integer)");
/*     */           } 
/* 143 */           recordSetTrans.executeSql("update CRM_CustomerContacter set projectrole=projectrole_bak_temp");
/*     */         } 
/*     */ 
/*     */         
/* 147 */         recordSetTrans.executeSql("update CRM_CustomerDefinField set fieldhtmltype=5,fielddbtype='int' where fieldname='projectrole' and usetable in('CRM_CustomerInfo','CRM_CustomerContacter')");
/* 148 */         recordSetTrans.commit();
/* 149 */       } catch (Exception exception) {
/* 150 */         recordSetTrans.rollback();
/* 151 */         writeLog(exception.getMessage());
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void task2() {
/* 160 */     RecordSet recordSet1 = new RecordSet();
/* 161 */     RecordSet recordSet2 = new RecordSet();
/*     */     
/* 163 */     boolean bool = false;
/* 164 */     String str = "";
/* 165 */     recordSet1.executeQuery("select id,fieldhtmltype from CRM_CustomerDefinField where fieldname='attitude' and usetable='CRM_CustomerContacter'", new Object[0]);
/* 166 */     if (recordSet1.next()) {
/* 167 */       str = recordSet1.getString("id");
/* 168 */       String str1 = recordSet1.getString("fieldhtmltype");
/* 169 */       if (str1.equals("1")) {
/* 170 */         bool = true;
/*     */       }
/*     */     } 
/* 173 */     if (bool && !"".equals(str)) {
/* 174 */       RecordSetTrans recordSetTrans = new RecordSetTrans();
/* 175 */       byte b = 0;
/*     */       try {
/* 177 */         recordSetTrans.setAutoCommit(false);
/* 178 */         ArrayList<String> arrayList = new ArrayList();
/* 179 */         arrayList.add("" + SystemEnv.getHtmlLabelName(84256, ThreadVarLanguage.getLang()) + "");
/* 180 */         arrayList.add("" + SystemEnv.getHtmlLabelName(84257, ThreadVarLanguage.getLang()) + "");
/* 181 */         arrayList.add("" + SystemEnv.getHtmlLabelName(84253, ThreadVarLanguage.getLang()) + "");
/* 182 */         arrayList.add("" + SystemEnv.getHtmlLabelName(84254, ThreadVarLanguage.getLang()) + "");
/* 183 */         arrayList.add("" + SystemEnv.getHtmlLabelName(375, ThreadVarLanguage.getLang()) + "");
/*     */         
/* 185 */         for (byte b1 = 0; b1 < arrayList.size(); b1++) {
/* 186 */           String str1 = arrayList.get(b1);
/* 187 */           recordSet2.executeQuery("select fieldid from crm_selectitem where fieldid=? and selectname=?", new Object[] { str, str1 });
/* 188 */           if (!recordSet2.next())
/*     */           {
/*     */             
/* 191 */             recordSetTrans.executeSql("insert into crm_selectitem(fieldid,selectvalue,selectname,fieldorder,isdel) values(" + str + "," + b++ + ",'" + str1 + "'," + b + ",0)");
/*     */           }
/*     */         } 
/* 194 */         recordSet1.executeQuery("select DISTINCT attitude from CRM_CustomerContacter where attitude is not null and attitude<>''", new Object[0]);
/* 195 */         while (recordSet1.next()) {
/* 196 */           String str1 = recordSet1.getString("attitude");
/* 197 */           recordSet2.executeQuery("select fieldid from crm_selectitem where fieldid=? and selectname=?", new Object[] { str, str1 });
/* 198 */           if (recordSet2.next()) {
/*     */             continue;
/*     */           }
/* 201 */           if (!arrayList.contains(str1)) {
/* 202 */             recordSetTrans.executeSql("insert into crm_selectitem(fieldid,selectvalue,selectname,fieldorder,isdel) values(" + str + "," + b++ + ",'" + str1 + "'," + b + ",0)");
/*     */           }
/*     */         } 
/*     */         
/* 206 */         if (recordSetTrans.getDBType().equals("sqlserver")) {
/* 207 */           recordSetTrans.executeSql("update CRM_CustomerContacter set attitude=(select top 1 selectvalue from crm_selectitem where selectname=attitude and fieldid=" + str + ") where attitude is not null and attitude<>''");
/* 208 */         } else if (recordSetTrans.getDBType().equals("mysql")) {
/* 209 */           recordSetTrans.executeSql("update CRM_CustomerContacter set attitude=(select selectvalue from crm_selectitem where selectname=attitude and fieldid=" + str + " limit 1) where attitude is not null and attitude<>''");
/*     */         }
/* 211 */         else if (recordSetTrans.getDBType().equals("postgresql")) {
/* 212 */           recordSetTrans.executeSql("update CRM_CustomerContacter set attitude=(select selectvalue from crm_selectitem where selectname=attitude and fieldid=" + str + " limit 1) where attitude is not null and attitude<>''");
/*     */         } else {
/*     */           
/* 215 */           recordSetTrans.executeSql("update CRM_CustomerContacter set attitude=(select selectvalue from crm_selectitem where selectname=attitude and fieldid=" + str + " and rownum=1) where attitude is not null and attitude<>''");
/*     */         } 
/*     */ 
/*     */         
/* 219 */         if (recordSetTrans.getDBType().equals("sqlserver")) {
/* 220 */           recordSetTrans.executeSql("alter table CRM_CustomerContacter alter column attitude int");
/* 221 */         } else if (recordSetTrans.getDBType().equals("mysql")) {
/* 222 */           recordSetTrans.executeSql("SELECT 1 FROM information_schema.columns WHERE TABLE_SCHEMA=(select database()) and table_name='CRM_CUSTOMERCONTACTER' AND COLUMN_NAME='ATTITUDE_BAK_TEMP'");
/* 223 */           boolean bool1 = recordSetTrans.next();
/* 224 */           if (!bool1) {
/* 225 */             recordSetTrans.executeSql("alter table CRM_CustomerContacter add attitude_bak_temp VARCHAR(100)");
/*     */           }
/* 227 */           recordSetTrans.executeSql("update CRM_CustomerContacter set attitude_bak_temp=attitude");
/* 228 */           recordSetTrans.executeSql("update CRM_CustomerContacter set attitude=null");
/* 229 */           recordSetTrans.executeSql("alter table CRM_CustomerContacter modify column attitude int");
/* 230 */           recordSetTrans.executeSql("update CRM_CustomerContacter set attitude=attitude_bak_temp");
/*     */         }
/* 232 */         else if (recordSetTrans.getDBType().equals("postgresql")) {
/* 233 */           recordSetTrans.executeSql("SELECT 1 FROM information_schema.columns WHERE TABLE_SCHEMA='public' and lower(table_name)=lower('CRM_CUSTOMERCONTACTER') AND lower(COLUMN_NAME)=lower('ATTITUDE_BAK_TEMP')");
/* 234 */           boolean bool1 = recordSetTrans.next();
/* 235 */           if (!bool1) {
/* 236 */             recordSetTrans.executeSql("alter table CRM_CustomerContacter add attitude_bak_temp VARCHAR(100)");
/*     */           }
/* 238 */           recordSetTrans.executeSql("update CRM_CustomerContacter set attitude_bak_temp=attitude");
/* 239 */           recordSetTrans.executeSql("update CRM_CustomerContacter set attitude=null");
/* 240 */           recordSetTrans.executeSql("alter table CRM_CustomerContacter alter column attitude type int");
/* 241 */           recordSetTrans.executeSql("update CRM_CustomerContacter set attitude=attitude_bak_temp");
/*     */         }
/*     */         else {
/*     */           
/* 245 */           recordSetTrans.executeSql("select 1 from user_tab_columns t where t.table_name='CRM_CUSTOMERCONTACTER' and t.column_name ='ATTITUDE_BAK_TEMP'");
/* 246 */           boolean bool1 = recordSetTrans.next();
/* 247 */           if (!bool1) {
/* 248 */             recordSetTrans.executeSql("alter table CRM_CustomerContacter add attitude_bak_temp VARCHAR2(800)");
/*     */           }
/* 250 */           recordSetTrans.executeSql("update CRM_CustomerContacter set attitude_bak_temp=attitude");
/* 251 */           recordSetTrans.executeSql("update CRM_CustomerContacter set attitude=null");
/*     */           
/* 253 */           if ("jc".equalsIgnoreCase(recordSetTrans.getOrgindbtype())) {
/* 254 */             recordSetTrans.executeSql("alter table CRM_CustomerContacter alter attitude type integer");
/*     */           } else {
/* 256 */             recordSetTrans.executeSql("alter table CRM_CustomerContacter modify (attitude integer)");
/*     */           } 
/* 258 */           recordSetTrans.executeSql("update CRM_CustomerContacter set attitude=attitude_bak_temp");
/*     */         } 
/*     */ 
/*     */         
/* 262 */         recordSetTrans.executeSql("update CRM_CustomerDefinField set fieldhtmltype=5,fielddbtype='int' where fieldname='attitude' and usetable in('CRM_CustomerInfo','CRM_CustomerContacter')");
/* 263 */         recordSetTrans.commit();
/* 264 */       } catch (Exception exception) {
/* 265 */         recordSetTrans.rollback();
/* 266 */         writeLog(exception.getMessage());
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void task3() {
/* 275 */     RecordSet recordSet1 = new RecordSet();
/* 276 */     RecordSet recordSet2 = new RecordSet();
/* 277 */     String str = "";
/* 278 */     recordSet1.executeQuery("select id,fieldhtmltype from CRM_CustomerDefinField where fieldname='projectrole' and usetable='CRM_CustomerContacter'", new Object[0]);
/* 279 */     if (recordSet1.next()) {
/* 280 */       str = recordSet1.getString("id");
/* 281 */       String str1 = recordSet1.getString("fieldhtmltype");
/* 282 */       if (str1.equals("5")) {
/* 283 */         recordSet2.executeUpdate("delete from crm_selectitem where fieldid=?", new Object[] { str });
/* 284 */         byte b1 = 0;
/* 285 */         ArrayList<String> arrayList = new ArrayList();
/* 286 */         arrayList.add("" + SystemEnv.getHtmlLabelName(124914, ThreadVarLanguage.getLang()) + "");
/* 287 */         arrayList.add("" + SystemEnv.getHtmlLabelName(84246, ThreadVarLanguage.getLang()) + "");
/* 288 */         arrayList.add("" + SystemEnv.getHtmlLabelName(84248, ThreadVarLanguage.getLang()) + "");
/* 289 */         arrayList.add("" + SystemEnv.getHtmlLabelName(84249, ThreadVarLanguage.getLang()) + "");
/* 290 */         arrayList.add("" + SystemEnv.getHtmlLabelName(84251, ThreadVarLanguage.getLang()) + "");
/* 291 */         arrayList.add("" + SystemEnv.getHtmlLabelName(375, ThreadVarLanguage.getLang()) + "");
/*     */ 
/*     */         
/* 294 */         for (byte b2 = 0; b2 < arrayList.size(); b2++) {
/* 295 */           String str2 = arrayList.get(b2);
/* 296 */           recordSet2.executeSql("insert into crm_selectitem(fieldid,selectvalue,selectname,fieldorder,isdel) values(" + str + "," + b1++ + ",'" + str2 + "'," + b1 + ",0)");
/*     */         } 
/*     */ 
/*     */         
/* 300 */         recordSet1.executeQuery("select DISTINCT projectrole from CRM_CustomerContacter where projectrole is not null and projectrole<>''", new Object[0]);
/* 301 */         while (recordSet1.next()) {
/* 302 */           String str2 = recordSet1.getString("projectrole");
/* 303 */           if (!arrayList.contains(str2)) {
/* 304 */             recordSet2.executeSql("insert into crm_selectitem(fieldid,selectvalue,selectname,fieldorder,isdel) values(" + str + "," + b1++ + ",'" + str2 + "'," + b1 + ",0)");
/*     */           }
/*     */         } 
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void task4() {
/* 315 */     RecordSet recordSet1 = new RecordSet();
/* 316 */     RecordSet recordSet2 = new RecordSet();
/* 317 */     String str = "";
/* 318 */     recordSet1.executeQuery("select id,fieldhtmltype from CRM_CustomerDefinField where fieldname='attitude' and usetable='CRM_CustomerContacter'", new Object[0]);
/* 319 */     if (recordSet1.next()) {
/* 320 */       str = recordSet1.getString("id");
/* 321 */       String str1 = recordSet1.getString("fieldhtmltype");
/* 322 */       if (str1.equals("5")) {
/* 323 */         recordSet2.executeUpdate("delete from crm_selectitem where fieldid=?", new Object[] { str });
/* 324 */         byte b1 = 0;
/* 325 */         ArrayList<String> arrayList = new ArrayList();
/* 326 */         arrayList.add("" + SystemEnv.getHtmlLabelName(84256, ThreadVarLanguage.getLang()) + "");
/* 327 */         arrayList.add("" + SystemEnv.getHtmlLabelName(84257, ThreadVarLanguage.getLang()) + "");
/* 328 */         arrayList.add("" + SystemEnv.getHtmlLabelName(84253, ThreadVarLanguage.getLang()) + "");
/* 329 */         arrayList.add("" + SystemEnv.getHtmlLabelName(84254, ThreadVarLanguage.getLang()) + "");
/* 330 */         arrayList.add("" + SystemEnv.getHtmlLabelName(375, ThreadVarLanguage.getLang()) + "");
/*     */         
/* 332 */         for (byte b2 = 0; b2 < arrayList.size(); b2++) {
/* 333 */           String str2 = arrayList.get(b2);
/* 334 */           recordSet2.executeSql("insert into crm_selectitem(fieldid,selectvalue,selectname,fieldorder,isdel) values(" + str + "," + b1++ + ",'" + str2 + "'," + b1 + ",0)");
/*     */         } 
/*     */         
/* 337 */         recordSet1.executeQuery("select DISTINCT attitude from CRM_CustomerContacter where attitude is not null and attitude<>''", new Object[0]);
/* 338 */         while (recordSet1.next()) {
/* 339 */           String str2 = recordSet1.getString("attitude");
/* 340 */           if (!arrayList.contains(str2)) {
/* 341 */             recordSet2.executeSql("insert into crm_selectitem(fieldid,selectvalue,selectname,fieldorder,isdel) values(" + str + "," + b1++ + ",'" + str2 + "'," + b1 + ",0)");
/*     */           }
/*     */         } 
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void task5() {
/* 352 */     CrmModeInitServer crmModeInitServer = new CrmModeInitServer();
/* 353 */     crmModeInitServer.execute();
/* 354 */     ModuleInitService moduleInitService = new ModuleInitService();
/* 355 */     ArrayList<String> arrayList = new ArrayList();
/* 356 */     arrayList.add("8a7ac4066f21480fbc0ce2ede0051867");
/* 357 */     arrayList.add("a77b554667364184a67b5121869089a2");
/* 358 */     arrayList.add("de5782583e914e90a3c9b5df373a8903");
/* 359 */     arrayList.add("db83fc2e01ea4502939f93c7f72b27f9");
/* 360 */     moduleInitService.initFeaInfoByKey(arrayList, false);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void task6() {
/* 367 */     String str = "SELECT id,DESCRIPTION FROM  WORKPLAN WHERE TYPE_N='3' AND STATUS='2' AND DESCRIPTION LIKE '%&lt;%' AND DESCRIPTION LIKE '%&gt;%'";
/* 368 */     RecordSet recordSet1 = new RecordSet();
/* 369 */     RecordSet recordSet2 = new RecordSet();
/* 370 */     recordSet1.executeQuery(str, new Object[0]);
/* 371 */     while (recordSet1.next()) {
/* 372 */       recordSet2.executeUpdate("UPDATE WORKPLAN SET DESCRIPTION=? WHERE ID=?", new Object[] { recordSet1
/* 373 */             .getString("DESCRIPTION").replace("&lt;", "<").replace("&gt;", ">"), recordSet1
/* 374 */             .getString("id") });
/*     */     } 
/*     */   }
/*     */   private void task7() {
/* 378 */     RecordSet recordSet1 = new RecordSet();
/* 379 */     recordSet1.executeQuery("SELECT * FROM CRM_SEASINFO", new Object[0]);
/* 380 */     RecordSet recordSet2 = new RecordSet();
/* 381 */     while (recordSet1.next()) {
/* 382 */       String str1 = UUID.randomUUID().toString().replaceAll("-", "").toUpperCase();
/* 383 */       String str2 = UUID.randomUUID().toString().replaceAll("-", "").toUpperCase();
/* 384 */       String str3 = recordSet1.getString("id");
/* 385 */       String str4 = recordSet1.getString("remindId");
/* 386 */       String str5 = recordSet1.getString("warnRemindId");
/* 387 */       if (str4.equals("")) {
/* 388 */         recordSet2.executeUpdate("UPDATE CRM_SeasInfo SET remindId=? WHERE ID=?", new Object[] { str2, str3 });
/* 389 */         CrmRemindWayEnum.saveWaysById(str2, new HashSet(Arrays.asList((Object[])"WORKFLOW".split(","))));
/*     */       } 
/*     */       
/* 392 */       if (str5.equals("")) {
/* 393 */         recordSet2.executeUpdate("UPDATE CRM_SeasInfo SET warnRemindId=? WHERE ID=?", new Object[] { str1, str3 });
/* 394 */         CrmRemindWayEnum.saveWaysById(str1, new HashSet(Arrays.asList((Object[])"WORKFLOW".split(","))));
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void task4crmLog() {
/*     */     try {
/* 405 */       writeLog("task4crmLog----start-----time:" + TimeUtil.getCurrentTimeString());
/*     */ 
/*     */       
/* 408 */       RecordSet recordSet = new RecordSet();
/* 409 */       recordSet.executeQuery("select inited from crm_modify_proc_init", new Object[0]);
/* 410 */       recordSet.first();
/* 411 */       boolean bool = (0 == recordSet.getInt("inited")) ? true : false;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 421 */       ArrayList<ArrayList<Integer>> arrayList = new ArrayList();
/*     */ 
/*     */       
/* 424 */       if (bool) {
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */         
/* 441 */         recordSet.executeQuery("select l.id,l.logtype,l.submitdate,l.submittime,l.customerid,m.type from CRM_log l left join CRM_Modify m on m.modifydate=l.submitdate and m.modifytime=l.submittime and m.customerid=l.customerid group by l.id,l.logtype,l.submitdate,l.submittime,l.customerid,m.type", new Object[0]);
/* 442 */         while (recordSet.next()) {
/* 443 */           ArrayList<Integer> arrayList1 = new ArrayList();
/* 444 */           switch (recordSet.getString("logtype")) {
/*     */             case "n":
/*     */             case "b":
/*     */             case "e":
/* 448 */               arrayList1.add(Integer.valueOf(1)); arrayList1.add(Integer.valueOf(1)); arrayList1.add(Integer.valueOf(recordSet.getInt("customerid"))); arrayList1.add(Integer.valueOf(1)); arrayList1.add(Integer.valueOf(recordSet.getInt("customerid"))); arrayList1.add(Integer.valueOf(recordSet.getInt("id")));
/*     */               break;
/*     */             case "d":
/* 451 */               arrayList1.add(Integer.valueOf(3)); arrayList1.add(Integer.valueOf(1)); arrayList1.add(Integer.valueOf(recordSet.getInt("customerid"))); arrayList1.add(Integer.valueOf(1)); arrayList1.add(null); arrayList1.add(Integer.valueOf(recordSet.getInt("id")));
/*     */               break;
/*     */             case "dc":
/* 454 */               arrayList1.add(Integer.valueOf(3)); arrayList1.add(Integer.valueOf(3)); arrayList1.add(Integer.valueOf(recordSet.getInt("customerid"))); arrayList1.add(Integer.valueOf(3)); arrayList1.add(null); arrayList1.add(Integer.valueOf(recordSet.getInt("id")));
/*     */               break;
/*     */             case "u":
/* 457 */               arrayList1.add(Integer.valueOf(4)); arrayList1.add(Integer.valueOf(1)); arrayList1.add(Integer.valueOf(recordSet.getInt("customerid"))); arrayList1.add(Integer.valueOf(1)); arrayList1.add(Integer.valueOf(recordSet.getInt("customerid"))); arrayList1.add(Integer.valueOf(recordSet.getInt("id")));
/*     */               break;
/*     */             case "nc":
/* 460 */               arrayList1.add(Integer.valueOf(1)); arrayList1.add(Integer.valueOf(3)); arrayList1.add(null); arrayList1.add(Integer.valueOf(3)); arrayList1.add(null); arrayList1.add(Integer.valueOf(recordSet.getInt("id")));
/*     */               break;
/*     */             case "ns":
/* 463 */               arrayList1.add(Integer.valueOf(1)); arrayList1.add(Integer.valueOf(1)); arrayList1.add(Integer.valueOf(recordSet.getInt("customerid"))); arrayList1.add(Integer.valueOf(4)); arrayList1.add(Integer.valueOf(recordSet.getInt("customerid"))); arrayList1.add(Integer.valueOf(recordSet.getInt("id")));
/*     */               break;
/*     */             case "ma":
/* 466 */               arrayList1.add(Integer.valueOf(1)); arrayList1.add(Integer.valueOf(1)); arrayList1.add(Integer.valueOf(recordSet.getInt("customerid"))); arrayList1.add(Integer.valueOf(5)); arrayList1.add(Integer.valueOf(recordSet.getInt("customerid"))); arrayList1.add(Integer.valueOf(recordSet.getInt("id")));
/*     */               break;
/*     */             case "si":
/*     */             case "sa":
/*     */             case "se":
/*     */             case "sd":
/* 472 */               arrayList1.add(Integer.valueOf(5)); arrayList1.add(Integer.valueOf(1)); arrayList1.add(Integer.valueOf(recordSet.getInt("customerid"))); arrayList1.add(Integer.valueOf(1)); arrayList1.add(Integer.valueOf(recordSet.getInt("customerid"))); arrayList1.add(Integer.valueOf(recordSet.getInt("id")));
/*     */               break;
/*     */             case "mc":
/* 475 */               arrayList1.add(Integer.valueOf(2)); arrayList1.add(Integer.valueOf(2)); arrayList1.add(Integer.valueOf(recordSet.getInt("customerid"))); arrayList1.add(Integer.valueOf(3)); arrayList1.add((recordSet.getInt("type") > 0) ? Integer.valueOf(recordSet.getInt("type")) : null); arrayList1.add(Integer.valueOf(recordSet.getInt("id")));
/*     */               break;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */             
/*     */             default:
/* 490 */               arrayList1.add(Integer.valueOf(2)); arrayList1.add(Integer.valueOf(1)); arrayList1.add(Integer.valueOf(recordSet.getInt("customerid"))); arrayList1.add(Integer.valueOf(1)); arrayList1.add(Integer.valueOf(recordSet.getInt("customerid"))); arrayList1.add(Integer.valueOf(recordSet.getInt("id")));
/*     */               break;
/*     */           } 
/*     */           
/* 494 */           arrayList.add(arrayList1);
/*     */         } 
/*     */         
/* 497 */         String str = "update CRM_log set operatetype=?,logbiztype=?,logbiztypetargetid=?,logsmalltype=?,logsmalltypetargetid=? where id=?";
/* 498 */         recordSet.executeBatchSql(str, arrayList);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */         
/* 515 */         recordSet.executeUpdate("update crm_modify_proc_init set inited=1", new Object[0]);
/* 516 */         writeLog("task4crmLog----end----size:" + arrayList.size() + "---time:" + TimeUtil.getCurrentTimeString());
/*     */       } 
/* 518 */     } catch (Exception exception) {
/* 519 */       exception.printStackTrace();
/* 520 */       writeLog(exception.getMessage());
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   private void task8() {
/* 526 */     RecordSet recordSet = new RecordSet();
/* 527 */     recordSet.executeUpdate("UPDATE schedulesetting SET runstatus=1 WHERE pointid='CrmTimedRemindJob'", new Object[0]);
/* 528 */     recordSet.executeUpdate("UPDATE schedulesetting SET runstatus=1 WHERE pointid='CrmContactRemindJob'", new Object[0]);
/* 529 */     recordSet.executeUpdate("UPDATE schedulesetting SET runstatus=1 WHERE pointid='CrmSeaJob'", new Object[0]);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/crm/util/CrmInitManager.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */