/*    */ package weaver.crm.util;
/*    */ 
/*    */ import java.io.File;
/*    */ import java.io.FileInputStream;
/*    */ import java.io.IOException;
/*    */ import java.io.InputStream;
/*    */ import javax.servlet.ServletException;
/*    */ import javax.servlet.ServletOutputStream;
/*    */ import javax.servlet.http.HttpServlet;
/*    */ import javax.servlet.http.HttpServletRequest;
/*    */ import javax.servlet.http.HttpServletResponse;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.file.FileSecurityUtil;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.HrmUserVarify;
/*    */ import weaver.hrm.User;
/*    */ 
/*    */ 
/*    */ public class FileDownload
/*    */   extends HttpServlet
/*    */ {
/*    */   private static final long serialVersionUID = 1742928826463371674L;
/*    */   
/*    */   protected void doGet(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws ServletException, IOException {
/* 25 */     User user = HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/* 26 */     if (null == user) {
/*    */       return;
/*    */     }
/*    */     
/* 30 */     String str1 = Util.null2String(paramHttpServletRequest.getSession().getAttribute("crmImportFileId"));
/* 31 */     String str2 = Util.null2String(paramHttpServletRequest.getSession().getAttribute("crmImportFilePath"));
/* 32 */     String str3 = "";
/* 33 */     InputStream inputStream = null;
/* 34 */     ServletOutputStream servletOutputStream = null;
/* 35 */     File file = null;
/*    */     try {
/* 37 */       RecordSet recordSet = new RecordSet();
/* 38 */       recordSet.execute("select filerealpath , imagefilename from ImageFile WHERE imagefileid = '" + str1 + "'");
/* 39 */       if (recordSet.next()) {
/* 40 */         file = new File(recordSet.getString("filerealpath"));
/* 41 */         str3 = recordSet.getString("imagefilename");
/* 42 */         str3 = new String(str3.getBytes("UTF-8"), "ISO8859_1");
/*    */         
/* 44 */         FileSecurityUtil.deleteFile(file);
/*    */       } 
/* 46 */       recordSet.execute("delete from ImageFile WHERE imagefileid = '" + str1 + "'");
/* 47 */       file = new File(str2);
/* 48 */       if (!file.exists()) {
/*    */         return;
/*    */       }
/*    */ 
/*    */       
/* 53 */       inputStream = new FileInputStream(str2);
/* 54 */       paramHttpServletResponse.setHeader("content-disposition", "attachment; filename=" + str3);
/* 55 */       paramHttpServletResponse.setContentType("application/octet-stream");
/*    */       
/* 57 */       servletOutputStream = paramHttpServletResponse.getOutputStream();
/* 58 */       byte[] arrayOfByte = new byte[4096];
/* 59 */       while (-1 != inputStream.read(arrayOfByte)) {
/* 60 */         servletOutputStream.write(arrayOfByte, 0, arrayOfByte.length);
/*    */       }
/* 62 */       servletOutputStream.flush();
/*    */       
/* 64 */       FileSecurityUtil.deleteFile(file);
/* 65 */     } catch (Exception exception) {
/* 66 */       exception.printStackTrace();
/*    */     } finally {
/* 68 */       if (servletOutputStream != null) servletOutputStream.close(); 
/* 69 */       if (inputStream != null) inputStream.close(); 
/* 70 */       FileSecurityUtil.deleteFile(file);
/*    */     } 
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/crm/util/FileDownload.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */