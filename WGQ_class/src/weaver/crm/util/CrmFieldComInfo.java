/*     */ package weaver.crm.util;
/*     */ 
/*     */ import weaver.cache.CacheBase;
/*     */ import weaver.cache.CacheColumn;
/*     */ import weaver.cache.CacheColumnType;
/*     */ import weaver.cache.PKColumn;
/*     */ import weaver.general.Util;
/*     */ 
/*     */ 
/*     */ public class CrmFieldComInfo
/*     */   extends CacheBase
/*     */ {
/*     */   public static String table;
/*  14 */   protected static String TABLE_NAME = "CRM_CustomerDefinField";
/*  15 */   protected static String TABLE_WHERE = "isopen=1";
/*  16 */   protected static String TABLE_ORDER = "usetable asc,groupid asc,dsporder asc";
/*     */   @PKColumn(type = CacheColumnType.NUMBER)
/*  18 */   protected static String PK_NAME = "id";
/*     */   
/*     */   @CacheColumn
/*     */   protected static int id;
/*     */   @CacheColumn
/*     */   protected static int fieldname;
/*     */   @CacheColumn
/*     */   protected static int fieldlabel;
/*     */   @CacheColumn
/*     */   protected static int fielddbtype;
/*     */   @CacheColumn
/*     */   protected static int fieldhtmltype;
/*     */   @CacheColumn
/*     */   protected static int selectid;
/*     */   @CacheColumn
/*     */   protected static int type;
/*     */   @CacheColumn
/*     */   protected static int viewtype;
/*     */   @CacheColumn
/*     */   protected static int textheight;
/*     */   @CacheColumn
/*     */   protected static int imgwidth;
/*     */   @CacheColumn
/*     */   protected static int imgheight;
/*     */   @CacheColumn
/*     */   protected static int dsporder;
/*     */   @CacheColumn
/*     */   protected static int isopen;
/*     */   @CacheColumn
/*     */   protected static int ismust;
/*     */   @CacheColumn
/*     */   protected static int issearch;
/*     */   @CacheColumn
/*     */   protected static int places;
/*     */   @CacheColumn
/*     */   protected static int candel;
/*     */   @CacheColumn
/*     */   protected static int groupid;
/*     */   @CacheColumn
/*     */   protected static int usetable;
/*     */   @CacheColumn
/*     */   protected static int seltablename;
/*     */   @CacheColumn
/*     */   protected static int selcolumname;
/*     */   @CacheColumn
/*     */   protected static int selkeycolumname;
/*     */   @CacheColumn
/*     */   protected static int dmlurl;
/*     */   @CacheColumn
/*     */   protected static int isdisplay;
/*     */   @CacheColumn
/*     */   protected static int isexport;
/*     */   
/*     */   public int getArraySize() {
/*  72 */     return size();
/*     */   }
/*     */   
/*     */   public String getId() {
/*  76 */     return (String)getRowValue(id);
/*     */   }
/*     */   
/*     */   public String getFieldname() {
/*  80 */     return (String)getRowValue(fieldname);
/*     */   }
/*     */   
/*     */   public String getFieldname(String paramString) {
/*  84 */     return (String)getValue(fieldname, paramString);
/*     */   }
/*     */   
/*     */   public String getFieldlabel() {
/*  88 */     return (String)getRowValue(fieldlabel);
/*     */   }
/*     */   
/*     */   public String getFieldlabel(String paramString) {
/*  92 */     return (String)getValue(fieldlabel, paramString);
/*     */   }
/*     */   
/*     */   public String getFielddbtype() {
/*  96 */     return (String)getRowValue(fielddbtype);
/*     */   }
/*     */   
/*     */   public String getFielddbtype(String paramString) {
/* 100 */     return (String)getValue(fielddbtype, paramString);
/*     */   }
/*     */   
/*     */   public Integer getFieldhtmltype() {
/* 104 */     return Integer.valueOf(Util.getIntValue((String)getRowValue(fieldhtmltype)));
/*     */   }
/*     */   
/*     */   public String getFieldhtmltype(String paramString) {
/* 108 */     return (String)getValue(fieldhtmltype, paramString);
/*     */   }
/*     */   
/*     */   public String getSelectid() {
/* 112 */     return (String)getRowValue(selectid);
/*     */   }
/*     */   
/*     */   public String getSelectid(String paramString) {
/* 116 */     return (String)getValue(selectid, paramString);
/*     */   }
/*     */   
/*     */   public String getType() {
/* 120 */     return (String)getRowValue(type);
/*     */   }
/*     */   
/*     */   public String getType(String paramString) {
/* 124 */     return (String)getValue(type, paramString);
/*     */   }
/*     */   
/*     */   public String getViewtype() {
/* 128 */     return (String)getRowValue(viewtype);
/*     */   }
/*     */   
/*     */   public String getViewtype(String paramString) {
/* 132 */     return (String)getValue(viewtype, paramString);
/*     */   }
/*     */   
/*     */   public String getTextheight() {
/* 136 */     return (String)getRowValue(textheight);
/*     */   }
/*     */   
/*     */   public String getTextheight(String paramString) {
/* 140 */     return (String)getValue(textheight, paramString);
/*     */   }
/*     */   
/*     */   public String getImgwidth() {
/* 144 */     return (String)getRowValue(imgwidth);
/*     */   }
/*     */   
/*     */   public String getImgwidth(String paramString) {
/* 148 */     return (String)getValue(imgwidth, paramString);
/*     */   }
/*     */   
/*     */   public String getImgheight() {
/* 152 */     return (String)getRowValue(imgheight);
/*     */   }
/*     */   
/*     */   public String getImgheight(String paramString) {
/* 156 */     return (String)getValue(imgheight, paramString);
/*     */   }
/*     */   
/*     */   public String getDsporder() {
/* 160 */     return (String)getRowValue(dsporder);
/*     */   }
/*     */   
/*     */   public String getDsporder(String paramString) {
/* 164 */     return (String)getValue(dsporder, paramString);
/*     */   }
/*     */   
/*     */   public String getIsopen() {
/* 168 */     return (String)getRowValue(isopen);
/*     */   }
/*     */   
/*     */   public String getIsopen(String paramString) {
/* 172 */     return (String)getValue(isopen, paramString);
/*     */   }
/*     */   
/*     */   public String getIsmust() {
/* 176 */     return (String)getRowValue(ismust);
/*     */   }
/*     */   
/*     */   public String getIsmust(String paramString) {
/* 180 */     return (String)getValue(ismust, paramString);
/*     */   }
/*     */   
/*     */   public String getIssearch() {
/* 184 */     return (String)getRowValue(issearch);
/*     */   }
/*     */   
/*     */   public String getIssearch(String paramString) {
/* 188 */     return (String)getValue(issearch, paramString);
/*     */   }
/*     */   
/*     */   public String getPlace() {
/* 192 */     return (String)getRowValue(places);
/*     */   }
/*     */   
/*     */   public String getPlace(String paramString) {
/* 196 */     return (String)getValue(places, paramString);
/*     */   }
/*     */   
/*     */   public String getCandel() {
/* 200 */     return (String)getRowValue(candel);
/*     */   }
/*     */   
/*     */   public String getCandel(String paramString) {
/* 204 */     return (String)getValue(candel, paramString);
/*     */   }
/*     */   
/*     */   public Integer getGroupid() {
/* 208 */     return Integer.valueOf(Util.getIntValue((String)getRowValue(groupid), 0));
/*     */   }
/*     */   
/*     */   public Integer getGroupid(String paramString) {
/* 212 */     return Integer.valueOf(Util.getIntValue((String)getValue(groupid, paramString), 0));
/*     */   }
/*     */   
/*     */   public String getUsetable() {
/* 216 */     return (String)getRowValue(usetable);
/*     */   }
/*     */   
/*     */   public String getUsetable(String paramString) {
/* 220 */     return (String)getValue(usetable, paramString);
/*     */   }
/*     */   
/*     */   public String getSeltablename() {
/* 224 */     return (String)getRowValue(seltablename);
/*     */   }
/*     */   
/*     */   public String getSeltablename(String paramString) {
/* 228 */     return (String)getValue(seltablename, paramString);
/*     */   }
/*     */   
/*     */   public String getSelcolumname() {
/* 232 */     return (String)getRowValue(selcolumname);
/*     */   }
/*     */   
/*     */   public String getSelcolumname(String paramString) {
/* 236 */     return (String)getValue(selcolumname, paramString);
/*     */   }
/*     */   
/*     */   public String getSelkeycolumname() {
/* 240 */     return (String)getRowValue(selkeycolumname);
/*     */   }
/*     */   
/*     */   public String getSelkeycolumname(String paramString) {
/* 244 */     return (String)getValue(selkeycolumname, paramString);
/*     */   }
/*     */   
/*     */   public String getDmlurl() {
/* 248 */     return (String)getRowValue(dmlurl);
/*     */   }
/*     */   
/*     */   public String getDmlurl(String paramString) {
/* 252 */     return (String)getValue(dmlurl, paramString);
/*     */   }
/*     */   public String getIsdisplay() {
/* 255 */     return (String)getRowValue(isdisplay);
/*     */   }
/*     */   
/*     */   public String getIsdisplay(String paramString) {
/* 259 */     return (String)getValue(isdisplay, paramString);
/*     */   }
/*     */   
/*     */   public String getIsexport() {
/* 263 */     return (String)getRowValue(isexport);
/*     */   }
/*     */   
/*     */   public String getIsexport(String paramString) {
/* 267 */     return (String)getValue(isexport, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public void removeFieldCache() {
/* 273 */     removeCache();
/*     */   }
/*     */   
/*     */   public void removeFieldCache(String paramString) {
/* 277 */     removeCache();
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/crm/util/CrmFieldComInfo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */