/*     */ package weaver.crm;
/*     */ 
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.crm.Maint.CustomerDescComInfo;
/*     */ import weaver.crm.Maint.CustomerInfoComInfo;
/*     */ import weaver.crm.Maint.CustomerSizeComInfo;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.GCONST;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.city.CityComInfo;
/*     */ import weaver.hrm.city.CitytwoComInfo;
/*     */ import weaver.hrm.company.DepartmentComInfo;
/*     */ import weaver.hrm.company.SubCompanyComInfo;
/*     */ import weaver.hrm.province.ProvinceComInfo;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class SptmForCrmModiRecord
/*     */   extends BaseBean
/*     */ {
/*     */   public String getCrmModiInfo(String paramString1, String paramString2) {
/*  29 */     String str = "";
/*     */     
/*     */     try {
/*  32 */       RecordSet recordSet = new RecordSet();
/*  33 */       if (SystemEnv.getHtmlLabelName(377, Util.threadVarLanguage()).equals(paramString1)) {
/*  34 */         recordSet.executeSql("select countryname from HrmCountry where id=" + paramString2);
/*  35 */         if (recordSet.next()) {
/*  36 */           str = recordSet.getString(1);
/*     */         }
/*     */       } 
/*  39 */       if (SystemEnv.getHtmlLabelName(493, Util.threadVarLanguage()).equals(paramString1)) {
/*  40 */         CityComInfo cityComInfo = new CityComInfo();
/*  41 */         str = cityComInfo.getCityname(paramString2);
/*     */       } 
/*  43 */       if (SystemEnv.getHtmlLabelName(643, Util.threadVarLanguage()).equals(paramString1)) {
/*  44 */         ProvinceComInfo provinceComInfo = new ProvinceComInfo();
/*  45 */         str = provinceComInfo.getProvincename(paramString2);
/*     */       } 
/*  47 */       if (SystemEnv.getHtmlLabelName(644, Util.threadVarLanguage()).equals(paramString1)) {
/*  48 */         recordSet.executeSql("select cityname from hrmCityTwo where id=" + paramString2);
/*  49 */         if (recordSet.next()) {
/*  50 */           str = recordSet.getString(1);
/*     */         }
/*     */       } 
/*  53 */       if (SystemEnv.getHtmlLabelName(433, Util.threadVarLanguage()).equals(paramString1)) {
/*  54 */         CustomerDescComInfo customerDescComInfo = new CustomerDescComInfo();
/*  55 */         str = customerDescComInfo.getCustomerDescname(paramString2);
/*     */       } 
/*  57 */       if (SystemEnv.getHtmlLabelName(576, Util.threadVarLanguage()).equals(paramString1)) {
/*  58 */         CustomerSizeComInfo customerSizeComInfo = new CustomerSizeComInfo();
/*  59 */         str = customerSizeComInfo.getCustomerSizename(paramString2);
/*     */       } 
/*  61 */       if (SystemEnv.getHtmlLabelName(63, Util.threadVarLanguage()).equals(paramString1)) {
/*  62 */         recordSet.executeSql("select fullname from CRM_CustomerType where id=" + paramString2);
/*  63 */         if (recordSet.next()) {
/*  64 */           str = recordSet.getString(1);
/*     */         }
/*     */       } 
/*  67 */       if (SystemEnv.getHtmlLabelName(645, Util.threadVarLanguage()).equals(paramString1)) {
/*  68 */         recordSet.executeSql("select fullname from CRM_ContactWay where id=" + paramString2);
/*  69 */         if (recordSet.next()) {
/*  70 */           str = recordSet.getString(1);
/*     */         }
/*     */       } 
/*  73 */       if (SystemEnv.getHtmlLabelName(575, Util.threadVarLanguage()).equals(paramString1)) {
/*  74 */         recordSet.executeSql("select fullname from CRM_SectorInfo where id=" + paramString2);
/*  75 */         if (recordSet.next()) {
/*  76 */           str = recordSet.getString(1);
/*     */         }
/*     */       } 
/*  79 */       if (SystemEnv.getHtmlLabelName(1278, Util.threadVarLanguage()).equals(paramString1)) {
/*  80 */         ResourceComInfo resourceComInfo = new ResourceComInfo();
/*  81 */         str = resourceComInfo.getLastname(paramString2);
/*     */       } 
/*  83 */       if (SystemEnv.getHtmlLabelName(124, Util.threadVarLanguage()).equals(paramString1)) {
/*  84 */         DepartmentComInfo departmentComInfo = new DepartmentComInfo();
/*  85 */         str = departmentComInfo.getDepartmentname(paramString2);
/*     */       } 
/*  87 */       if (SystemEnv.getHtmlLabelName(591, Util.threadVarLanguage()).equals(paramString1)) {
/*  88 */         CustomerInfoComInfo customerInfoComInfo = new CustomerInfoComInfo();
/*  89 */         str = customerInfoComInfo.getCustomerInfoname(paramString2);
/*     */       } 
/*  91 */       if (SystemEnv.getHtmlLabelName(132, Util.threadVarLanguage()).equals(paramString1)) {
/*     */         
/*  93 */         CustomerInfoComInfo customerInfoComInfo = new CustomerInfoComInfo();
/*  94 */         str = customerInfoComInfo.getCustomerInfoname(paramString2);
/*     */       } 
/*  96 */       if (SystemEnv.getHtmlLabelName(81764, Util.threadVarLanguage()).equals(paramString1)) {
/*  97 */         CitytwoComInfo citytwoComInfo = new CitytwoComInfo();
/*  98 */         str = citytwoComInfo.getCityname(paramString2);
/*     */       } 
/* 100 */     } catch (Exception exception) {
/* 101 */       exception.printStackTrace();
/*     */     } 
/* 103 */     return str;
/*     */   }
/*     */   
/*     */   public String getOneSubcompany(String paramString1, String paramString2) {
/* 107 */     String str = "";
/* 108 */     SubCompanyComInfo subCompanyComInfo = null;
/*     */     try {
/* 110 */       subCompanyComInfo = new SubCompanyComInfo();
/* 111 */     } catch (Exception exception) {
/* 112 */       exception.printStackTrace();
/*     */     } 
/* 114 */     if ("0".equals(paramString2)) {
/* 115 */       str = subCompanyComInfo.getSubCompanyname(paramString1);
/*     */     } else {
/* 117 */       str = subCompanyComInfo.getSubCompanyname(paramString2);
/*     */     } 
/* 119 */     return str;
/*     */   }
/*     */   
/*     */   public String getWxCustomername(String paramString1, String paramString2) {
/* 123 */     return "<a href='" + GCONST.getContextPath() + "/CRM/report/CrmLedgerCusView.jsp?hrmid=" + paramString2 + "&status=1' target='_blank'>" + paramString1 + "</a>";
/*     */   }
/*     */ 
/*     */   
/*     */   public String getJcCustomername(String paramString1, String paramString2) {
/* 128 */     return "<a href='" + GCONST.getContextPath() + "/CRM/report/CrmLedgerCusView.jsp?hrmid=" + paramString2 + "&status=2' target='_blank'>" + paramString1 + "</a>";
/*     */   }
/*     */ 
/*     */   
/*     */   public String getQzCustomername(String paramString1, String paramString2) {
/* 133 */     return "<a href='" + GCONST.getContextPath() + "/CRM/report/CrmLedgerCusView.jsp?hrmid=" + paramString2 + "&status=3' target='_blank'>" + paramString1 + "</a>";
/*     */   }
/*     */ 
/*     */   
/*     */   public String getContactlogname(String paramString1, String paramString2) {
/* 138 */     return "<a href='" + GCONST.getContextPath() + "/CRM/report/CrmLedgerCusView.jsp?hrmid=" + paramString2 + "&status=4' target='_blank'>" + paramString1 + "</a>";
/*     */   }
/*     */ 
/*     */   
/*     */   public String getContacteranme(String paramString) {
/* 143 */     RecordSet recordSet = new RecordSet();
/* 144 */     String str = "";
/* 145 */     recordSet.executeSql("select top 1 fullname,id from CRM_CustomerContacter where customerid=" + paramString + " order by main desc,id desc");
/* 146 */     if (recordSet.next()) {
/* 147 */       str = "<a href='" + GCONST.getContextPath() + "/CRM/data/ViewContacter.jsp?ContacterID=" + recordSet.getString("id") + "' target='_blank'>" + recordSet.getString("fullname") + "</a>";
/*     */     }
/* 149 */     return str;
/*     */   }
/*     */   
/*     */   public String getContacterContent(String paramString) {
/* 153 */     RecordSet recordSet = new RecordSet();
/* 154 */     String str = "";
/* 155 */     recordSet.executeSql("select top 1 name,description from WorkPlan where type_n='3' and (','+crmid+',') like ('%,'+cast(" + paramString + " as varchar)+',%') order by id desc");
/* 156 */     if (recordSet.next()) {
/* 157 */       str = SystemEnv.getHtmlLabelName(344, Util.threadVarLanguage()) + ":&nbsp;" + recordSet.getString("name") + ",&nbsp;&nbsp;&nbsp;" + SystemEnv.getHtmlLabelName(345, Util.threadVarLanguage()) + ":&nbsp;" + recordSet.getString("description") + "";
/*     */     }
/* 159 */     return str;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/crm/SptmForCrmModiRecord.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */