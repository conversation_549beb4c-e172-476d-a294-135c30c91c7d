/*    */ package weaver.crm.search;
/*    */ 
/*    */ import java.util.ArrayList;
/*    */ import java.util.List;
/*    */ import weaver.crm.CrmShareBase;
/*    */ import weaver.general.BaseBean;
/*    */ import weaver.hrm.HrmUserVarify;
/*    */ import weaver.hrm.User;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class ContacterSearchTransMethod
/*    */   extends BaseBean
/*    */ {
/*    */   public List getContractOpratePopedom(String paramString1, String paramString2) {
/* 20 */     ArrayList<String> arrayList = new ArrayList();
/*    */     try {
/* 22 */       String[] arrayOfString = paramString2.split("\\+");
/* 23 */       User user = new User();
/* 24 */       user.setUid(Integer.parseInt(arrayOfString[0]));
/* 25 */       user.setLogintype(arrayOfString[1]);
/* 26 */       user.setLoginid(arrayOfString[2]);
/*    */       
/* 28 */       arrayList.add("true");
/* 29 */       if (!arrayOfString[0].trim().equals("")) {
/* 30 */         arrayList.add("true");
/*    */       } else {
/* 32 */         arrayList.add("false");
/*    */       } 
/*    */       
/* 35 */       if (!arrayOfString[1].trim().equals("") && HrmUserVarify.checkUserRight("CreateSMS:View", user)) {
/* 36 */         arrayList.add("true");
/*    */       } else {
/* 38 */         arrayList.add("false");
/*    */       } 
/*    */       
/* 41 */       arrayList.add("true");
/* 42 */       arrayList.add("true");
/* 43 */     } catch (Exception exception) {
/* 44 */       writeLog(exception);
/*    */     } 
/*    */     
/* 47 */     return arrayList;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getCustomerCheckInfo(String paramString) {
/*    */     try {
/* 57 */       String[] arrayOfString = paramString.split("\\+");
/*    */       
/* 59 */       if (arrayOfString[1].equals("7") || arrayOfString[1].equals("8")) {
/* 60 */         return "false";
/*    */       }
/* 62 */       CrmShareBase crmShareBase = new CrmShareBase();
/* 63 */       int i = crmShareBase.getRightLevelForCRM(arrayOfString[2], arrayOfString[0]);
/* 64 */       if (i > 1) {
/* 65 */         return "true";
/*    */       }
/* 67 */     } catch (Exception exception) {
/* 68 */       writeLog(exception);
/*    */     } 
/*    */     
/* 71 */     return "false";
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/crm/search/ContacterSearchTransMethod.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */