/*     */ package weaver.crm.customermap;
/*     */ 
/*     */ import com.alibaba.fastjson.JSONArray;
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import java.io.BufferedReader;
/*     */ import java.io.IOException;
/*     */ import java.io.InputStreamReader;
/*     */ import java.io.PrintWriter;
/*     */ import java.net.URL;
/*     */ import java.net.URLConnection;
/*     */ import java.net.URLEncoder;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.security.util.SecurityMethodUtil;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class AmapUtil
/*     */ {
/*     */   public static Map<String, String> getCoordinateByAddress(String paramString) {
/*  25 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/*  27 */     String str = "";
/*     */     try {
/*  29 */       str = sendPost("https://restapi.amap.com/v3/geocode/geo", "address=" + URLEncoder.encode(paramString, "utf-8") + "&key=8aa2c389011c15e4bb6546f9be74fd74");
/*  30 */       str = SecurityMethodUtil.clearEntity(str);
/*     */ 
/*     */       
/*  33 */       JSONObject jSONObject = JSONObject.parseObject(str);
/*  34 */       if ("1".equals(jSONObject.getString("status")) && "OK".equals(jSONObject.getString("info"))) {
/*  35 */         String[] arrayOfString = JSONArray.parseArray(jSONObject.getString("geocodes")).getJSONObject(0).getString("location").split(",");
/*  36 */         hashMap.put("lng", arrayOfString[0]);
/*  37 */         hashMap.put("lat", arrayOfString[1]);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/*     */       }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*     */     }
/*  50 */     catch (Exception exception) {
/*  51 */       exception.printStackTrace();
/*     */     } 
/*  53 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String sendPost(String paramString1, String paramString2) {
/*  64 */     PrintWriter printWriter = null;
/*  65 */     BufferedReader bufferedReader = null;
/*  66 */     String str = "";
/*     */     try {
/*  68 */       URL uRL = new URL(paramString1);
/*     */       
/*  70 */       URLConnection uRLConnection = uRL.openConnection();
/*     */       
/*  72 */       uRLConnection.setRequestProperty("accept", "*/*");
/*  73 */       uRLConnection.setRequestProperty("connection", "Keep-Alive");
/*  74 */       uRLConnection.setRequestProperty("user-agent", "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
/*     */ 
/*     */       
/*  77 */       uRLConnection.setDoOutput(true);
/*  78 */       uRLConnection.setDoInput(true);
/*     */       
/*  80 */       printWriter = new PrintWriter(uRLConnection.getOutputStream());
/*     */       
/*  82 */       printWriter.print(paramString2);
/*     */       
/*  84 */       printWriter.flush();
/*     */ 
/*     */       
/*  87 */       bufferedReader = new BufferedReader(new InputStreamReader(uRLConnection.getInputStream(), "utf-8"));
/*     */       String str1;
/*  89 */       while ((str1 = bufferedReader.readLine()) != null) {
/*  90 */         str = str + str1;
/*     */       }
/*  92 */     } catch (Exception exception) {
/*  93 */       exception.printStackTrace();
/*     */     } finally {
/*     */ 
/*     */       
/*     */       try {
/*  98 */         if (printWriter != null) {
/*  99 */           printWriter.close();
/*     */         }
/* 101 */         if (bufferedReader != null) {
/* 102 */           bufferedReader.close();
/*     */         }
/*     */       }
/* 105 */       catch (IOException iOException) {
/* 106 */         iOException.printStackTrace();
/*     */       } 
/*     */     } 
/* 109 */     return str;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/crm/customermap/AmapUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */