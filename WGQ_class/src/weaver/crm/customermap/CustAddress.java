/*    */ package weaver.crm.customermap;
/*    */ 
/*    */ import java.io.FileInputStream;
/*    */ import java.io.FileNotFoundException;
/*    */ import java.io.IOException;
/*    */ import java.util.Properties;
/*    */ import weaver.general.GCONST;
/*    */ 
/*    */ public class CustAddress {
/*    */   public static boolean isOpenCustAddress;
/*    */   
/*    */   static {
/* 13 */     Properties properties = new Properties();
/*    */     try {
/* 15 */       String str1 = GCONST.getPropertyPath();
/* 16 */       properties.load(new FileInputStream(str1 + "custaddress.properties"));
/* 17 */       String str2 = properties.getProperty("openCustAddress");
/* 18 */       isOpenCustAddress = "1".equals(str2);
/* 19 */     } catch (FileNotFoundException fileNotFoundException) {
/* 20 */       fileNotFoundException.printStackTrace();
/* 21 */     } catch (IOException iOException) {
/* 22 */       iOException.printStackTrace();
/*    */     } 
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/crm/customermap/CustAddress.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */