/*     */ package weaver.crm.customermap;
/*     */ 
/*     */ import java.io.BufferedReader;
/*     */ import java.io.ByteArrayInputStream;
/*     */ import java.io.IOException;
/*     */ import java.io.InputStreamReader;
/*     */ import java.io.PrintWriter;
/*     */ import java.net.URL;
/*     */ import java.net.URLConnection;
/*     */ import java.net.URLDecoder;
/*     */ import java.net.URLEncoder;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import org.dom4j.Document;
/*     */ import org.dom4j.Node;
/*     */ import org.dom4j.io.SAXReader;
/*     */ import weaver.security.util.SecurityMethodUtil;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class BaiduMapUtil
/*     */ {
/*     */   public static Map<String, String> getCoordinateByAddress(String paramString) {
/*  28 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/*  30 */     String str = "";
/*     */     try {
/*  32 */       str = sendPost("http://api.map.baidu.com/geocoder/v2/?address=" + URLEncoder.encode(paramString, "utf-8"), "ak=rFms0CnkZG3iisWqdNEBp4WT&output=xml");
/*  33 */       str = SecurityMethodUtil.clearEntity(str);
/*     */       
/*  35 */       SAXReader sAXReader = new SAXReader();
/*  36 */       Document document = sAXReader.read(new ByteArrayInputStream(URLDecoder.decode(str, "utf-8").getBytes("utf-8")));
/*  37 */       Node node = document.selectSingleNode("/GeocoderSearchResponse/status");
/*     */       
/*  39 */       if ("0".equals(node.getText())) {
/*     */         
/*  41 */         hashMap.put("lng", document.selectSingleNode("/GeocoderSearchResponse/result/location/lng").getText());
/*  42 */         hashMap.put("lat", document.selectSingleNode("/GeocoderSearchResponse/result/location/lat").getText());
/*     */       } 
/*  44 */     } catch (Exception exception) {
/*  45 */       exception.printStackTrace();
/*     */     } 
/*  47 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String sendPost(String paramString1, String paramString2) {
/*  58 */     PrintWriter printWriter = null;
/*  59 */     BufferedReader bufferedReader = null;
/*  60 */     String str = "";
/*     */     try {
/*  62 */       URL uRL = new URL(paramString1);
/*     */       
/*  64 */       URLConnection uRLConnection = uRL.openConnection();
/*     */       
/*  66 */       uRLConnection.setRequestProperty("accept", "*/*");
/*  67 */       uRLConnection.setRequestProperty("connection", "Keep-Alive");
/*  68 */       uRLConnection.setRequestProperty("user-agent", "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
/*     */ 
/*     */       
/*  71 */       uRLConnection.setDoOutput(true);
/*  72 */       uRLConnection.setDoInput(true);
/*     */       
/*  74 */       printWriter = new PrintWriter(uRLConnection.getOutputStream());
/*     */       
/*  76 */       printWriter.print(paramString2);
/*     */       
/*  78 */       printWriter.flush();
/*     */ 
/*     */       
/*  81 */       bufferedReader = new BufferedReader(new InputStreamReader(uRLConnection.getInputStream(), "utf-8"));
/*     */       String str1;
/*  83 */       while ((str1 = bufferedReader.readLine()) != null) {
/*  84 */         str = str + str1;
/*     */       }
/*  86 */     } catch (Exception exception) {
/*  87 */       exception.printStackTrace();
/*     */     } finally {
/*     */ 
/*     */       
/*     */       try {
/*  92 */         if (printWriter != null) {
/*  93 */           printWriter.close();
/*     */         }
/*  95 */         if (bufferedReader != null) {
/*  96 */           bufferedReader.close();
/*     */         }
/*     */       }
/*  99 */       catch (IOException iOException) {
/* 100 */         iOException.printStackTrace();
/*     */       } 
/*     */     } 
/* 103 */     return str;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/crm/customermap/BaiduMapUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */