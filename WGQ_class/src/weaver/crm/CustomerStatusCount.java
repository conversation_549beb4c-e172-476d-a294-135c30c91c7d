/*     */ package weaver.crm;
/*     */ 
/*     */ import com.engine.crm.util.SellChanceShareUtil;
/*     */ import java.text.SimpleDateFormat;
/*     */ import java.util.Date;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.TimeUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CustomerStatusCount
/*     */   extends BaseBean
/*     */ {
/*     */   public int getContractNumber(String paramString1, String paramString2) {
/*     */     try {
/*  24 */       ContacterShareBase contacterShareBase = new ContacterShareBase();
/*  25 */       String str1 = " CRM_Contract  t1," + contacterShareBase.getTempTable(paramString2) + "  t2 ,CRM_CustomerInfo  t3 ";
/*  26 */       String str2 = " t1.deleted is null and t1.crmId = t3.id and t1.id = t2.relateditemid";
/*  27 */       RecordSet recordSet = new RecordSet();
/*     */       
/*  29 */       if ("expire".equals(paramString1)) {
/*  30 */         if (recordSet.getDBType().equals("oracle")) {
/*  31 */           str2 = str2 + " and t1.status = 2 and t1.isRemind = 0 and TO_DATE(t1.enddate,'yyyy-mm-dd') - sysdate <= t1.remindDay ";
/*     */         
/*     */         }
/*  34 */         else if (recordSet.getDBType().equals("postgresql")) {
/*  35 */           str2 = str2 + " and t1.status = 2 and t1.isRemind = 0 and TO_DATE(t1.enddate,'yyyy-mm-dd') - sysdate <= t1.remindDay ";
/*     */         
/*     */         }
/*  38 */         else if (recordSet.getDBType().equals("mysql")) {
/*  39 */           str2 = str2 + " and t1.status = 2 and t1.isRemind = 0 and datediff(DATE(now()),date_sub(t1.enddate, interval t1.remindDay day)) >= 0 ";
/*     */         } else {
/*     */           
/*  42 */           str2 = str2 + " and t1.status = 2 and t1.isRemind = 0 and datediff(day,DATEADD(day,(0-t1.remindDay),t1.enddate),getDate()) >= 0 ";
/*     */         } 
/*     */       }
/*     */       
/*  46 */       if ("pay".equals(paramString1)) {
/*  47 */         str1 = str1 + " , CRM_ContractPayMethod t4";
/*  48 */         if (recordSet.getDBType().equals("oracle")) {
/*  49 */           str2 = str2 + " and t1.status = 2 and t1.id = t4.contractId   AND t4.isRemind = 0 AND to_number(TO_DATE(t4.payDate,'yyyy-mm-dd')-TO_DATE(to_char(sysdate,'yyyy-mm-dd'),'yyyy-mm-dd')) <= t1.remindDay";
/*     */ 
/*     */         
/*     */         }
/*  53 */         else if (recordSet.getDBType().equals("postgresql")) {
/*  54 */           str2 = str2 + " and t1.status = 2 and t1.id = t4.contractId   AND t4.isRemind = 0 AND to_number(TO_DATE(t4.payDate,'yyyy-mm-dd')-TO_DATE(to_char(sysdate,'yyyy-mm-dd'),'yyyy-mm-dd')) <= t1.remindDay";
/*     */ 
/*     */         
/*     */         }
/*  58 */         else if (recordSet.getDBType().equals("mysql")) {
/*  59 */           str2 = str2 + " and t1.status = 2 and t1.id = t4.contractId AND t4.isRemind = 0 AND datediff(t4.payDate,DATE(now())) <= t1.remindDay ";
/*     */         } else {
/*     */           
/*  62 */           str2 = str2 + " and t1.status = 2 and t1.id = t4.contractId  AND t4.isRemind = 0 AND datediff(day,getDate(),t4.payDate) <= t1.remindDay ";
/*     */         } 
/*     */       } 
/*     */ 
/*     */       
/*  67 */       if ("delivery".equals(paramString1)) {
/*  68 */         str1 = str1 + " , CRM_ContractProduct t5";
/*  69 */         if (recordSet.getDBType().equals("oracle")) {
/*  70 */           str2 = str2 + " and t1.status = 2 and t1.id = t5.contractId   AND t5.isRemind = 0 AND to_number(TO_DATE(t5.planDate,'yyyy-mm-dd')-TO_DATE(to_char(sysdate,'yyyy-mm-dd'),'yyyy-mm-dd')) <= t1.remindDay";
/*     */ 
/*     */         
/*     */         }
/*  74 */         else if (recordSet.getDBType().equals("postgresql")) {
/*  75 */           str2 = str2 + " and t1.status = 2 and t1.id = t5.contractId   AND t5.isRemind = 0 AND to_number(TO_DATE(t5.planDate,'yyyy-mm-dd')-TO_DATE(to_char(sysdate,'yyyy-mm-dd'),'yyyy-mm-dd')) <= t1.remindDay";
/*     */ 
/*     */         
/*     */         }
/*  79 */         else if (recordSet.getDBType().equals("mysql")) {
/*  80 */           str2 = str2 + " and t1.status = 2 and t1.id = t5.contractId AND t5.isRemind = 0 AND datediff(t5.planDate,DATE(now())) <= t1.remindDay ";
/*     */         } else {
/*  82 */           str2 = str2 + " and t1.status = 2 and t1.id = t5.contractId  AND t5.isRemind = 0 AND datediff(day,getDate(),t5.planDate) <= t1.remindDay ";
/*     */         } 
/*     */       } 
/*     */       
/*  86 */       String str3 = "select count(distinct t1.id) from " + str1 + " where " + str2;
/*     */ 
/*     */       
/*  89 */       recordSet.execute(str3);
/*  90 */       recordSet.next();
/*  91 */       return (recordSet.getInt(1) < 0) ? 0 : recordSet.getInt(1);
/*  92 */     } catch (Exception exception) {
/*  93 */       writeLog(exception);
/*     */       
/*  95 */       return 0;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getNewCustomerNumber(String paramString) {
/*     */     try {
/* 106 */       String str = "select count(*) from CRM_CustomerInfo t1 ,CRM_ViewLog2 t2  where (t1.deleted=0 or t1.deleted is null) and t1.id=t2.customerid and t1.manager=" + paramString + " and (t1.seasflag is null or t1.seasflag=3)";
/*     */       
/* 108 */       RecordSet recordSet = new RecordSet();
/* 109 */       recordSet.execute(str);
/* 110 */       recordSet.next();
/* 111 */       return (recordSet.getInt(1) < 0) ? 0 : recordSet.getInt(1);
/* 112 */     } catch (Exception exception) {
/* 113 */       writeLog(exception);
/*     */       
/* 115 */       return 0;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getSellChanceNumber(String paramString) {
/*     */     try {
/* 126 */       String str1 = TimeUtil.getCurrentDateString();
/* 127 */       String str2 = TimeUtil.dateAdd(str1, -30);
/* 128 */       CrmShareBase crmShareBase = new CrmShareBase();
/*     */       
/* 130 */       String str3 = (new SellChanceShareUtil(new User(Util.getIntValue(paramString)))).getTempTable();
/* 131 */       String str4 = "select count(*) from CRM_SellChance  t1 LEFT JOIN CRM_CUSTOMERINFO t2 ON t1.customerid=t2.id ";
/* 132 */       str4 = str4 + " where t2.deleted=0 and  t1.id in " + str3 + " and t1.predate <= '" + str1 + "'and t1.predate>='" + str2 + "'";
/* 133 */       RecordSet recordSet = new RecordSet();
/* 134 */       recordSet.execute(str4);
/* 135 */       recordSet.next();
/* 136 */       return (recordSet.getInt(1) < 0) ? 0 : recordSet.getInt(1);
/* 137 */     } catch (Exception exception) {
/* 138 */       writeLog(exception);
/*     */       
/* 140 */       return 0;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getContactNumber(String paramString) {
/*     */     try {
/* 152 */       RecordSet recordSet = new RecordSet();
/* 153 */       String str = "select count(*) from CRM_CustomerInfo t1, CRM_ContacterLog_Remind t2 ";
/* 154 */       str = str + "where t1.id = t2.customerid AND t1.manager = " + paramString + " AND t1.deleted <> 1 AND t2.isremind = 0 AND ((t2.lastestContactDate is null or t2.lastestContactDate =''";
/*     */ 
/*     */       
/* 157 */       if (recordSet.getDBType().equals("oracle")) {
/*     */         
/* 159 */         str = str + "  and ((trunc(sysdate,'dd') - trunc(to_date(t1.createdate ,'yyyy-mm-dd hh24:mi:ss'),'dd' ))  >= t2.before))";
/* 160 */         str = str + "  or ((trunc(sysdate,'dd') - trunc(to_date(t2.lastestContactDate ,'yyyy-mm-dd hh24:mi:ss'),'dd' ))  >= t2.before)";
/* 161 */       } else if (recordSet.getDBType().equals("mysql")) {
/*     */         
/* 163 */         str = str + "  and TIMESTAMPDIFF(day,t1.createdate ,DATE(now())) >= t2.before)";
/* 164 */         str = str + "  or TIMESTAMPDIFF(day,t2.lastestContactDate ,DATE(now())) >= t2.before";
/*     */       }
/* 166 */       else if (recordSet.getDBType().equals("postgresql")) {
/* 167 */         str = str + "  and datediff(t1.createdate ,now())  >= t2.before)";
/* 168 */         str = str + "  or datediff(t2.lastestContactDate ,now())  >= t2.before";
/*     */       }
/*     */       else {
/*     */         
/* 172 */         str = str + "  and datediff(day , t1.createdate ,getdate())  >= t2.before)";
/* 173 */         str = str + "  or datediff(day , t2.lastestContactDate ,getdate())  >= t2.before";
/*     */       } 
/*     */       
/* 176 */       str = str + ")";
/* 177 */       recordSet.execute(str);
/* 178 */       recordSet.next();
/* 179 */       return (recordSet.getInt(1) < 0) ? 0 : recordSet.getInt(1);
/* 180 */     } catch (Exception exception) {
/* 181 */       writeLog(exception);
/*     */       
/* 183 */       return 0;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getContactNumber(String paramString1, String paramString2) {
/*     */     try {
/* 194 */       RecordSet recordSet = new RecordSet();
/* 195 */       String str1 = (new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")).format(new Date());
/* 196 */       String str2 = "select count(*) from CRM_CustomerInfo t1, CRM_ContacterLog_Remind t2 ";
/* 197 */       str2 = str2 + "where t1.id = t2.customerid AND t1.manager = " + paramString2 + " AND t1.deleted <> 1 AND t2.isremind = 0 AND (t2.lastestContactDate is null or t2.lastestContactDate ='' or t2.lastestContactDate < '" + str1 + "') AND t1.id = " + paramString1;
/*     */ 
/*     */ 
/*     */       
/* 201 */       recordSet.execute(str2);
/* 202 */       recordSet.next();
/* 203 */       return (recordSet.getInt(1) < 0) ? 0 : recordSet.getInt(1);
/* 204 */     } catch (Exception exception) {
/* 205 */       writeLog(exception);
/*     */       
/* 207 */       return 0;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getExchangeInfoCount(String paramString1, String paramString2, int paramInt) {
/*     */     try {
/* 217 */       RecordSet recordSet = new RecordSet();
/* 218 */       String str1 = "select recentId from CRM_Exchange_Info where sortid = '" + paramString1 + "' and type_n = '" + paramString2 + "' and userid = '" + paramInt + "'";
/* 219 */       recordSet.execute(str1);
/* 220 */       int i = 0;
/* 221 */       while (recordSet.next()) {
/* 222 */         i = recordSet.getInt("recentId");
/*     */       }
/*     */       
/* 225 */       String str2 = "select count(*) from Exchange_Info where sortid = '" + paramString1 + "' and type_n = '" + paramString2 + "' and  creater != '" + paramInt + "' and id > '" + i + "'";
/*     */       
/* 227 */       recordSet.execute(str2);
/* 228 */       if (recordSet.next()) {
/* 229 */         return (recordSet.getInt(1) < 0) ? 0 : recordSet.getInt(1);
/*     */       }
/*     */     }
/* 232 */     catch (Exception exception) {
/* 233 */       writeLog(exception);
/*     */     } 
/* 235 */     return 0;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setExchangeInfo(String paramString1, String paramString2, int paramInt) {
/*     */     try {
/* 247 */       String str1 = TimeUtil.getCurrentDateString();
/* 248 */       String str2 = TimeUtil.getOnlyCurrentTimeString();
/*     */       
/* 250 */       RecordSet recordSet = new RecordSet();
/* 251 */       recordSet.execute("select max(id) from Exchange_Info where sortid = '" + paramString1 + "' and type_n = '" + paramString2 + "'");
/* 252 */       recordSet.next();
/* 253 */       int i = recordSet.getInt(1);
/*     */       
/* 255 */       String str3 = "select count(*) from CRM_Exchange_Info where sortid = '" + paramString1 + "' and type_n = '" + paramString2 + "' and userid = '" + paramInt + "'";
/* 256 */       recordSet.execute(str3);
/* 257 */       recordSet.next();
/*     */       
/* 259 */       if (recordSet.getInt(1) == 0) {
/* 260 */         String str = "insert into CRM_Exchange_Info(sortid , type_n, readDate, readTime, recentId, userid) values  ('" + paramString1 + "' , '" + paramString2 + "' , '" + str1 + "' , '" + str2 + "' , " + i + " ,'" + paramInt + "' )";
/*     */         
/* 262 */         recordSet.execute(str);
/*     */       } else {
/*     */         
/* 265 */         String str = "update CRM_Exchange_Info  set readDate = '" + str1 + "' , readTime = '" + str2 + "' , recentId = '" + i + "' where  type_n = '" + paramString2 + "' and sortid = '" + paramString1 + "' and userid = '" + paramInt + "'";
/*     */ 
/*     */         
/* 268 */         recordSet.execute(str);
/*     */       }
/*     */     
/*     */     }
/* 272 */     catch (Exception exception) {
/* 273 */       writeLog(exception);
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/crm/CustomerStatusCount.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */