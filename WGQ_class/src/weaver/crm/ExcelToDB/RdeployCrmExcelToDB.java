/*     */ package weaver.crm.ExcelToDB;
/*     */ 
/*     */ import java.io.File;
/*     */ import java.io.FileInputStream;
/*     */ import java.io.FileOutputStream;
/*     */ import java.net.InetAddress;
/*     */ import java.text.DateFormat;
/*     */ import java.text.SimpleDateFormat;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Date;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import java.util.UUID;
/*     */ import java.util.concurrent.ConcurrentHashMap;
/*     */ import java.util.regex.Matcher;
/*     */ import java.util.regex.Pattern;
/*     */ import java.util.zip.ZipEntry;
/*     */ import java.util.zip.ZipInputStream;
/*     */ import org.apache.poi.hssf.usermodel.HSSFCell;
/*     */ import org.apache.poi.hssf.usermodel.HSSFRow;
/*     */ import org.apache.poi.hssf.usermodel.HSSFSheet;
/*     */ import org.apache.poi.hssf.usermodel.HSSFWorkbook;
/*     */ import org.apache.poi.poifs.filesystem.POIFSFileSystem;
/*     */ import org.apache.poi.ss.usermodel.CellType;
/*     */ import org.json.JSONObject;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.crm.CrmShareBase;
/*     */ import weaver.crm.Maint.CustomerInfoComInfo;
/*     */ import weaver.crm.data.CustomerModifyLog;
/*     */ import weaver.file.FileSecurityUtil;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.GCONST;
/*     */ import weaver.general.TimeUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ public class RdeployCrmExcelToDB extends BaseBean {
/*     */   private User user;
/*  41 */   private int total = 0;
/*  42 */   private int success = 0;
/*  43 */   private int fail = 0;
/*     */   
/*     */   private int isCover;
/*     */   
/*     */   private String Errormsg;
/*     */   
/*     */   private boolean isNoData = false;
/*     */   private boolean isErrTemplate;
/*     */   private boolean isErreData;
/*     */   private boolean isSaveSuccess;
/*  53 */   private static Map<Integer, String> excelCall = new ConcurrentHashMap<>();
/*  54 */   private static Map<String, String> excelIndexCall = new ConcurrentHashMap<>();
/*  55 */   private static Map<String, String> fieldNameMap = new ConcurrentHashMap<>();
/*  56 */   private static Map<String, String> fieldIsCusMap = new ConcurrentHashMap<>();
/*  57 */   private static Map<String, String> mustMap = new ConcurrentHashMap<>();
/*  58 */   private static List<String> mustCallList = new ArrayList<>();
/*     */ 
/*     */   
/*     */   static {
/*  62 */     mustMap.put("name", SystemEnv.getHtmlLabelName(1268, Util.threadVarLanguage()));
/*  63 */     mustMap.put("firstname", SystemEnv.getHtmlLabelNames("572,413", Util.threadVarLanguage()));
/*  64 */     mustMap.put("status", SystemEnv.getHtmlLabelName(602, Util.threadVarLanguage()));
/*     */     
/*  66 */     fieldNameMap.put(SystemEnv.getHtmlLabelName(1268, Util.threadVarLanguage()), "name");
/*  67 */     fieldNameMap.put(SystemEnv.getHtmlLabelName(76, Util.threadVarLanguage()), "website");
/*  68 */     fieldNameMap.put(SystemEnv.getHtmlLabelName(1278, Util.threadVarLanguage()), "manager");
/*  69 */     fieldNameMap.put(SystemEnv.getHtmlLabelName(602, Util.threadVarLanguage()), "status");
/*  70 */     fieldNameMap.put(SystemEnv.getHtmlLabelName(477, Util.threadVarLanguage()), "email");
/*     */     
/*  72 */     fieldIsCusMap.put("name", "1");
/*  73 */     fieldIsCusMap.put("website", "1");
/*  74 */     fieldIsCusMap.put("manager", "1");
/*  75 */     fieldIsCusMap.put("status", "1");
/*  76 */     fieldIsCusMap.put("email", "1");
/*     */     
/*  78 */     fieldNameMap.put(SystemEnv.getHtmlLabelNames("572,413", Util.threadVarLanguage()), "firstname");
/*  79 */     fieldNameMap.put(SystemEnv.getHtmlLabelNames("572,462", Util.threadVarLanguage()), "title");
/*  80 */     fieldNameMap.put(SystemEnv.getHtmlLabelName(640, Util.threadVarLanguage()), "jobtitle");
/*  81 */     fieldNameMap.put(SystemEnv.getHtmlLabelNames("572,477", Util.threadVarLanguage()), "email1");
/*  82 */     fieldNameMap.put(SystemEnv.getHtmlLabelName(661, Util.threadVarLanguage()), "phoneoffice");
/*  83 */     fieldNameMap.put(SystemEnv.getHtmlLabelName(620, Util.threadVarLanguage()), "mobilephone");
/*     */     
/*  85 */     fieldIsCusMap.put("firstname", "2");
/*  86 */     fieldIsCusMap.put("title", "2");
/*  87 */     fieldIsCusMap.put("jobtitle", "2");
/*  88 */     fieldIsCusMap.put("email1", "2");
/*  89 */     fieldIsCusMap.put("phoneoffice", "2");
/*  90 */     fieldIsCusMap.put("mobilephone", "2");
/*     */   }
/*  92 */   private List<String> customerNameList = new ArrayList<>();
/*  93 */   private List<String> errorList = new ArrayList<>();
/*  94 */   private List<Integer> errorRowIdList = new ArrayList<>();
/*  95 */   private List<String> crmSqlList = new ArrayList<>();
/*  96 */   private List<String> contacterSqlList = new ArrayList<>();
/*     */   
/*  98 */   private RecordSet rs = new RecordSet();
/*     */   
/* 100 */   private String creditLevel = "0";
/* 101 */   private String province = "0";
/* 102 */   private String Country = "0";
/* 103 */   private String department = "";
/* 104 */   private String subcompanyid = "";
/* 105 */   private String currentDate = (new SimpleDateFormat("yyyy-MM-dd")).format(new Date());
/* 106 */   private String temId = UUID.randomUUID().toString();
/*     */ 
/*     */ 
/*     */   
/*     */   public void excelToDB(String paramString) {
/* 111 */     ArrayList<String> arrayList = ectract(paramString, GCONST.getRootPath() + "rdeploy/crm/import/temp/" + this.user.getUID() + "/");
/*     */     
/* 113 */     File file1 = new File(arrayList.get(0));
/* 114 */     File file2 = new File((String)arrayList.get(0) + ".xls");
/* 115 */     file1.renameTo(file2);
/*     */     
/* 117 */     paramString = (String)arrayList.get(0) + ".xls";
/* 118 */     JSONObject jSONObject = new JSONObject();
/*     */     
/* 120 */     FileInputStream fileInputStream = null;
/* 121 */     FileOutputStream fileOutputStream = null;
/* 122 */     this.isNoData = false;
/*     */     try {
/* 124 */       fileInputStream = new FileInputStream(paramString);
/* 125 */       POIFSFileSystem pOIFSFileSystem = new POIFSFileSystem(fileInputStream);
/* 126 */       HSSFWorkbook hSSFWorkbook = new HSSFWorkbook(pOIFSFileSystem);
/*     */       
/* 128 */       HSSFSheet hSSFSheet = hSSFWorkbook.getSheetAt(0);
/* 129 */       if (hSSFSheet.getLastRowNum() <= 1) {
/* 130 */         setNoData(true);
/*     */       
/*     */       }
/*     */       else {
/*     */         
/* 135 */         HSSFRow hSSFRow = null;
/* 136 */         ArrayList<String> arrayList1 = new ArrayList();
/*     */         short s;
/* 138 */         for (s = 0; s <= hSSFSheet.getLastRowNum(); s = (short)(s + 1)) {
/* 139 */           boolean bool = true;
/* 140 */           hSSFRow = hSSFSheet.getRow(s); short s1;
/* 141 */           for (s1 = 0; s1 < hSSFRow.getLastCellNum(); s1 = (short)(s1 + 1)) {
/* 142 */             if (null != hSSFRow.getCell(s1) && !getCellValue(hSSFRow.getCell(s1)).trim().equals("")) {
/* 143 */               bool = false;
/*     */               break;
/*     */             } 
/*     */           } 
/* 147 */           if (bool) {
/* 148 */             arrayList1.add(s + "");
/*     */           }
/*     */         } 
/*     */         
/* 152 */         if (arrayList1.size() == hSSFSheet.getLastRowNum()) {
/* 153 */           jSONObject.put("code", "1");
/* 154 */           jSONObject.put("errorMes", SystemEnv.getHtmlLabelName(83781, Util.threadVarLanguage()));
/* 155 */           setNoData(true);
/*     */         
/*     */         }
/*     */         else {
/*     */           
/* 160 */           hSSFRow = hSSFSheet.getRow(1);
/* 161 */           excelCall = new ConcurrentHashMap<>();
/* 162 */           excelIndexCall = new ConcurrentHashMap<>();
/* 163 */           for (s = 0; s < hSSFRow.getLastCellNum(); s = (short)(s + 1)) {
/*     */             
/* 165 */             HSSFCell hSSFCell = hSSFRow.getCell(s);
/* 166 */             String str = hSSFCell.getStringCellValue().trim();
/* 167 */             if (str.indexOf("(") > 0) {
/* 168 */               str = str.substring(0, str.indexOf("(", 0));
/*     */             }
/*     */             
/* 171 */             excelCall.put(Integer.valueOf(Integer.parseInt(s + "")), str);
/* 172 */             excelIndexCall.put(str, str);
/*     */           } 
/* 174 */           s = 1;
/* 175 */           mustCallList = new ArrayList<>();
/* 176 */           if (excelIndexCall.get(mustMap.get("name")) == null || ((String)excelIndexCall.get(mustMap.get("name"))).isEmpty()) {
/* 177 */             jSONObject.put("code", "2");
/* 178 */             jSONObject.put("errorMes", SystemEnv.getHtmlLabelName(525056, Util.threadVarLanguage()) + "【" + (String)mustMap.get("name") + "】" + SystemEnv.getHtmlLabelName(18621, Util.threadVarLanguage()));
/* 179 */             mustCallList.add("1");
/* 180 */             s = 0;
/*     */           } 
/* 182 */           if (excelIndexCall.get(mustMap.get("firstname")) == null || ((String)excelIndexCall.get(mustMap.get("firstname"))).isEmpty()) {
/* 183 */             jSONObject.put("code", "2");
/* 184 */             jSONObject.put("errorMes", SystemEnv.getHtmlLabelName(525056, Util.threadVarLanguage()) + "【" + (String)mustMap.get("firstname") + "】" + SystemEnv.getHtmlLabelName(18621, Util.threadVarLanguage()));
/* 185 */             s = 0;
/* 186 */             mustCallList.add("3");
/*     */           } 
/* 188 */           if (excelIndexCall.get(mustMap.get("status")) == null || ((String)excelIndexCall.get(mustMap.get("status"))).isEmpty()) {
/* 189 */             jSONObject.put("code", "2");
/* 190 */             jSONObject.put("errorMes", SystemEnv.getHtmlLabelName(525056, Util.threadVarLanguage()) + "【" + (String)mustMap.get("status") + "】" + SystemEnv.getHtmlLabelName(18621, Util.threadVarLanguage()));
/* 191 */             s = 0;
/* 192 */             mustCallList.add("2");
/*     */           } 
/* 194 */           if (s != 0) {
/*     */             
/* 196 */             setTotal(hSSFSheet.getLastRowNum() - arrayList1.size() - 1);
/* 197 */             this.rs.executeSql("select c1.id,c1.name from CRM_CustomerInfo c1");
/* 198 */             HashMap<Object, Object> hashMap = new HashMap<>();
/* 199 */             while (this.rs.next()) {
/* 200 */               hashMap.put(this.rs.getString("name"), this.rs.getString("id"));
/*     */             }
/*     */ 
/*     */             
/* 204 */             for (byte b = 2; b < hSSFSheet.getLastRowNum() + 1; b++) {
/* 205 */               if (!arrayList1.contains(b + "")) {
/*     */ 
/*     */                 
/* 208 */                 hSSFRow = hSSFSheet.getRow(b);
/* 209 */                 String str1 = "";
/* 210 */                 String str2 = "";
/* 211 */                 String str3 = "";
/* 212 */                 String str4 = "";
/* 213 */                 boolean bool = checkCellValue(hSSFRow);
/* 214 */                 boolean bool1 = false;
/* 215 */                 String str5 = "-1";
/* 216 */                 if (bool) {
/* 217 */                   this.errorRowIdList.add(Integer.valueOf(b + 1));
/* 218 */                   this.crmSqlList.add("errorSQL");
/* 219 */                   this.contacterSqlList.add("errorSQL");
/*     */                 } else {
/*     */                   short s1;
/* 222 */                   for (s1 = 0; s1 < hSSFRow.getLastCellNum(); s1 = (short)(s1 + 1)) {
/* 223 */                     HSSFCell hSSFCell = hSSFRow.getCell(s1);
/* 224 */                     if (null == hSSFCell) {
/* 225 */                       hSSFCell = hSSFRow.createCell(s1);
/* 226 */                       hSSFCell.setCellValue("");
/*     */                     } 
/*     */                     
/* 229 */                     String str6 = ((String)fieldNameMap.get(excelCall.get(Integer.valueOf(Integer.parseInt(s1 + ""))))).toUpperCase();
/* 230 */                     String str7 = getCellValue(hSSFCell);
/*     */                     
/* 232 */                     if (!this.isErreData) {
/* 233 */                       if (((String)fieldIsCusMap.get(str6.toLowerCase())).equals("1")) {
/* 234 */                         if (str6.toLowerCase().equals("name"))
/*     */                         {
/* 236 */                           if (hashMap.get(str7) != null) {
/*     */                             
/* 238 */                             if (this.isCover == 0) {
/*     */                               continue;
/*     */                             }
/*     */                             
/* 242 */                             if (this.isCover == 1) {
/*     */                               
/* 244 */                               bool1 = true;
/* 245 */                               str5 = (String)hashMap.get(str7);
/*     */                             } 
/*     */                           } 
/*     */                         }
/*     */ 
/*     */ 
/*     */                         
/* 252 */                         if (str6.toLowerCase().equals("status")) {
/* 253 */                           if (str7.isEmpty()) {
/* 254 */                             str7 = "2";
/* 255 */                           } else if (str7.equals(SystemEnv.getHtmlLabelName(17295, Util.threadVarLanguage()))) {
/* 256 */                             str7 = "1";
/* 257 */                           } else if (str7.equals(SystemEnv.getHtmlLabelName(17290, Util.threadVarLanguage()))) {
/* 258 */                             str7 = "2";
/* 259 */                           } else if (str7.equals(SystemEnv.getHtmlLabelName(17291, Util.threadVarLanguage()))) {
/* 260 */                             str7 = "3";
/* 261 */                           } else if (str7.equals(SystemEnv.getHtmlLabelName(17292, Util.threadVarLanguage()))) {
/* 262 */                             str7 = "4";
/* 263 */                           } else if (str7.equals(SystemEnv.getHtmlLabelName(17293, Util.threadVarLanguage()))) {
/* 264 */                             str7 = "5";
/* 265 */                           } else if (str7.equals(SystemEnv.getHtmlLabelName(17294, Util.threadVarLanguage()))) {
/* 266 */                             str7 = "6";
/* 267 */                           } else if (str7.equals(SystemEnv.getHtmlLabelName(525058, Util.threadVarLanguage()))) {
/* 268 */                             str7 = "7";
/* 269 */                           } else if (str7.equals(SystemEnv.getHtmlLabelName(525059, Util.threadVarLanguage()))) {
/* 270 */                             str7 = "8";
/*     */                           } 
/*     */                           
/* 273 */                           str2 = str2 + "'" + str7 + "',";
/*     */                         } else {
/* 275 */                           str2 = str2 + "'" + str7 + "',";
/*     */                         } 
/*     */                         
/* 278 */                         if (bool1) {
/*     */                           
/* 280 */                           if (!str6.toLowerCase().equals("name"))
/*     */                           {
/* 282 */                             str1 = str1 + str6 + " = '" + str7 + "',";
/*     */                           }
/*     */                         }
/*     */                         else {
/*     */                           
/* 287 */                           str1 = str1 + str6 + ",";
/*     */                         } 
/*     */                         continue;
/*     */                       } 
/* 291 */                       if (str6.toLowerCase().equals("title")) {
/* 292 */                         if (str7.isEmpty()) {
/* 293 */                           str7 = "1";
/* 294 */                         } else if (str7.equals(SystemEnv.getHtmlLabelName(26690, Util.threadVarLanguage()))) {
/* 295 */                           str7 = "1";
/* 296 */                         } else if (str7.equals(SystemEnv.getHtmlLabelName(26691, Util.threadVarLanguage()))) {
/* 297 */                           str7 = "2";
/* 298 */                         } else if (str7.equals(SystemEnv.getHtmlLabelName(525060, Util.threadVarLanguage()))) {
/* 299 */                           str7 = "3";
/*     */                         } 
/* 301 */                       } else if (str6.toLowerCase().equals("email1")) {
/* 302 */                         str6 = "email";
/*     */                       } 
/*     */                       
/* 305 */                       if (bool1) {
/*     */                         
/* 307 */                         str3 = str3 + str6 + " = '" + str7 + "',";
/*     */                       }
/*     */                       else {
/*     */                         
/* 311 */                         str3 = str3 + str6 + ",";
/*     */                       } 
/*     */                       
/* 314 */                       str4 = str4 + "'" + str7 + "',";
/*     */                     } 
/*     */                     
/*     */                     continue;
/*     */                   } 
/* 319 */                   if (!this.isErreData) {
/* 320 */                     if (!str1.toLowerCase().contains("manager")) {
/* 321 */                       if (bool1) {
/*     */                         
/* 323 */                         str1 = str1 + "manager = " + this.user.getUID() + ",";
/*     */                       }
/*     */                       else {
/*     */                         
/* 327 */                         str1 = str1 + "manager,";
/*     */                       } 
/* 329 */                       str2 = str2 + this.user.getUID() + ",";
/*     */                     } 
/* 331 */                     if (!str1.toLowerCase().contains("status")) {
/* 332 */                       str1 = str1 + "status,";
/* 333 */                       str2 = str2 + this.user.getUID() + ",";
/*     */                     } 
/* 335 */                     if (!bool1)
/*     */                     {
/* 337 */                       str1 = str1 + "fincode,currency,contractlevel,creditlevel,creditoffset,discount,invoiceacount,deliverytype,paymentterm,paymentway,saleconfirm,typebegin,rating,createdate,province,Country,deleted,department,subcompanyid1";
/*     */                     }
/*     */                     
/* 340 */                     str2 = str2 + "0,0,0,'" + this.creditLevel + "',0,100,0,0,0,0,0,'" + this.currentDate + "',0,'" + this.currentDate + "','" + this.province + "','" + this.Country + "',0,'" + getDepartment() + "','" + getSubcompanyid() + "'";
/* 341 */                     String str = "";
/* 342 */                     if (bool1) {
/*     */                       
/* 344 */                       str1 = str1.substring(0, str1.lastIndexOf(","));
/* 345 */                       str = "update CRM_CustomerInfo set " + str1 + " where id = " + str5;
/*     */                     }
/*     */                     else {
/*     */                       
/* 349 */                       str = "insert into CRM_CustomerInfo(" + str1 + ") values (" + str2 + ")";
/*     */                     } 
/*     */                     
/* 352 */                     this.crmSqlList.add(this.user.getUID() + "|" + str);
/*     */                     
/* 354 */                     if (!str3.equals("")) {
/* 355 */                       str3 = str3.replace("contacteremail", "email");
/* 356 */                       if (!bool1)
/*     */                       {
/* 358 */                         str3 = str3 + "customerid,LANGUAGE,manager,main,picid";
/*     */                       }
/* 360 */                       str4 = str4 + this.temId + "," + this.user.getLanguage() + "," + this.user.getUID() + ",1,0";
/* 361 */                       if (bool1) {
/*     */                         
/* 363 */                         str3 = str3.substring(0, str3.lastIndexOf(","));
/* 364 */                         str = "update CRM_CustomerContacter set " + str3 + " where customerid = " + str5;
/*     */                       }
/*     */                       else {
/*     */                         
/* 368 */                         str = "insert into CRM_CustomerContacter(" + str3 + ") values (" + str4 + ")";
/*     */                       } 
/*     */                       
/* 371 */                       this.contacterSqlList.add(str);
/*     */                     } 
/*     */                   } 
/*     */                 } 
/*     */               } 
/* 376 */             }  saveInfo();
/* 377 */             setSuccess(hSSFSheet.getLastRowNum() - arrayList1.size() - this.errorList.size() - 1);
/* 378 */             setFail(this.errorList.size());
/*     */           } 
/*     */         } 
/*     */       } 
/* 382 */       fileOutputStream = new FileOutputStream(paramString);
/* 383 */       hSSFWorkbook.write(fileOutputStream);
/* 384 */     } catch (Exception exception) {
/* 385 */       this.isSaveSuccess = false;
/* 386 */       exception.printStackTrace();
/* 387 */       writeLog(exception);
/*     */     } finally {
/*     */       try {
/* 390 */         if (null != fileOutputStream) {
/* 391 */           fileOutputStream.close();
/*     */         }
/* 393 */         if (null != fileInputStream) {
/* 394 */           fileInputStream.close();
/*     */         }
/* 396 */         delFolder(GCONST.getRootPath() + "rdeploy/crm/import/temp/" + this.user.getUID());
/* 397 */       } catch (Exception exception) {
/* 398 */         exception.printStackTrace();
/* 399 */         writeLog(exception);
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void saveInfo() throws Exception {
/* 409 */     RecordSet recordSet = new RecordSet();
/*     */     
/* 411 */     CustomerInfoComInfo customerInfoComInfo = new CustomerInfoComInfo();
/* 412 */     CrmShareBase crmShareBase = new CrmShareBase();
/* 413 */     CustomerModifyLog customerModifyLog = new CustomerModifyLog();
/*     */     
/* 415 */     byte b1 = 2;
/* 416 */     InetAddress inetAddress = InetAddress.getLocalHost();
/* 417 */     String str1 = inetAddress.getHostAddress();
/* 418 */     String str2 = TimeUtil.getCurrentDateString();
/* 419 */     String str3 = TimeUtil.getOnlyCurrentTimeString();
/*     */     
/* 421 */     for (byte b2 = 0; b2 < this.crmSqlList.size(); b2++) {
/* 422 */       String str = ((String)this.crmSqlList.get(b2)).toString();
/*     */       
/* 424 */       if (!str.equals("errorSQL")) {
/*     */ 
/*     */ 
/*     */ 
/*     */         
/* 429 */         String str4 = str.substring(str.indexOf("|") + 1);
/* 430 */         String str5 = str.substring(0, str.indexOf("|"));
/* 431 */         boolean bool = recordSet.execute(str4);
/* 432 */         if (bool) {
/*     */           
/* 434 */           if (str4.toLowerCase().startsWith("update")) {
/*     */             
/* 436 */             String str6 = ((String)this.contacterSqlList.get(b2)).toString();
/* 437 */             recordSet.execute(str6);
/*     */           }
/*     */           else {
/*     */             
/* 441 */             recordSet.execute("select max(id) from CRM_CustomerInfo where manager = " + str5);
/* 442 */             String str6 = "";
/* 443 */             if (recordSet.next()) {
/* 444 */               str6 = recordSet.getString(1);
/*     */             }
/* 446 */             if (!str6.isEmpty()) {
/*     */ 
/*     */               
/* 449 */               String str7 = ((String)this.contacterSqlList.get(b2)).toString().replace(this.temId, str6);
/* 450 */               boolean bool1 = recordSet.execute(str7);
/* 451 */               if (!bool1) {
/*     */                 
/* 453 */                 String str9 = "DELETE CRM_CustomerInfo where id = " + str6;
/* 454 */                 recordSet.executeSql(str9);
/* 455 */                 JSONObject jSONObject = new JSONObject();
/* 456 */                 jSONObject.put("code", "5");
/* 457 */                 jSONObject.put("errorCustomerName", this.customerNameList.get(b2));
/* 458 */                 jSONObject.put("rowNum", b2 + 3);
/* 459 */                 jSONObject.put("errorMes", SystemEnv.getHtmlLabelName(525061, Util.threadVarLanguage()));
/* 460 */                 this.errorList.add(jSONObject.toString());
/*     */               } 
/*     */               
/* 463 */               customerInfoComInfo.addCustomerInfoCache(str6);
/* 464 */               crmShareBase.setDefaultShare(str6);
/* 465 */               customerModifyLog.modify(str6 + "", this.user.getUID() + "", this.user.getUID() + "");
/*     */               
/* 467 */               String str8 = "" + str6 + b1 + "n" + b1 + "0" + b1 + "" + b1 + str2 + b1 + str3 + b1 + this.user.getUID() + b1 + "1" + b1 + str1;
/* 468 */               recordSet.executeProc("CRM_Log_Insert", str8);
/*     */             } 
/*     */           } 
/*     */         } else {
/*     */           
/* 473 */           JSONObject jSONObject = new JSONObject();
/* 474 */           jSONObject.put("code", "5");
/* 475 */           jSONObject.put("errorCustomerName", this.customerNameList.get(b2));
/* 476 */           jSONObject.put("rowNum", b2 + 3);
/* 477 */           jSONObject.put("errorMes", SystemEnv.getHtmlLabelName(525061, Util.threadVarLanguage()));
/* 478 */           this.errorList.add(jSONObject.toString());
/*     */         } 
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String getCellValue(HSSFCell paramHSSFCell) throws Exception {
/* 490 */     String str = "";
/* 491 */     switch (paramHSSFCell.getCellType()) {
/*     */       case NUMERIC:
/* 493 */         str = String.valueOf((long)paramHSSFCell.getNumericCellValue());
/*     */         break;
/*     */       case STRING:
/* 496 */         str = paramHSSFCell.getStringCellValue();
/*     */         break;
/*     */       case FORMULA:
/* 499 */         str = DateFormat.getDateInstance().format(paramHSSFCell.getDateCellValue()).toString();
/*     */         break;
/*     */     } 
/*     */ 
/*     */     
/* 504 */     return str.trim();
/*     */   }
/*     */   
/*     */   private boolean checkCellValue(HSSFRow paramHSSFRow) {
/* 508 */     boolean bool = false;
/*     */     try {
/* 510 */       Pattern pattern = Pattern.compile("^([a-zA-Z0-9_\\-\\.]+)@((\\[[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\.)|(([a-zA-Z0-9\\-]+\\.)+))([a-zA-Z]{2,4}|[0-9]{1,3})(\\]?)$");
/* 511 */       JSONObject jSONObject = new JSONObject();
/* 512 */       StringBuffer stringBuffer = new StringBuffer("");
/* 513 */       String str = ""; short s;
/* 514 */       for (s = 0; s < paramHSSFRow.getLastCellNum(); s = (short)(s + 1)) {
/* 515 */         HSSFCell hSSFCell = paramHSSFRow.getCell(s);
/* 516 */         if (null == hSSFCell) {
/* 517 */           hSSFCell = paramHSSFRow.createCell(s);
/* 518 */           hSSFCell.setCellValue("");
/*     */         } 
/*     */         
/* 521 */         String str1 = ((String)fieldNameMap.get(excelCall.get(Integer.valueOf(Integer.parseInt(s + ""))))).toUpperCase();
/* 522 */         String str2 = getCellValue(hSSFCell);
/* 523 */         if (this.customerNameList.contains(str)) {
/*     */           
/* 525 */           jSONObject.put("code", "9");
/* 526 */           jSONObject.put("errorCustomerName", str);
/*     */           
/* 528 */           for (byte b = 0; b < this.customerNameList.size(); b++) {
/*     */             
/* 530 */             if (str.equals(this.customerNameList.get(b))) {
/*     */               
/* 532 */               stringBuffer.append((b + 3) + SystemEnv.getHtmlLabelName(18082, Util.threadVarLanguage()));
/*     */               
/*     */               break;
/*     */             } 
/*     */           } 
/* 537 */           bool = true;
/*     */           break;
/*     */         } 
/* 540 */         if (str1.toLowerCase().equals("name")) {
/* 541 */           if (str2.isEmpty()) {
/* 542 */             stringBuffer.append((String)mustMap.get("name") + SystemEnv.getHtmlLabelName(385284, Util.threadVarLanguage()) + "&nbsp;&nbsp;");
/* 543 */             str = SystemEnv.getHtmlLabelName(525062, Util.threadVarLanguage());
/* 544 */             bool = true;
/*     */           }
/*     */           else {
/*     */             
/* 548 */             str = str2;
/*     */           }
/*     */         
/*     */         }
/* 552 */         else if (str1.toLowerCase().equals("email")) {
/* 553 */           if (!str2.isEmpty()) {
/* 554 */             Matcher matcher = pattern.matcher(str2);
/* 555 */             if (!matcher.matches())
/*     */             {
/* 557 */               stringBuffer.append(SystemEnv.getHtmlLabelName(525063, Util.threadVarLanguage()) + "&nbsp;&nbsp;");
/* 558 */               bool = true;
/*     */             }
/*     */           
/*     */           }
/*     */         
/* 563 */         } else if (str1.toLowerCase().equals("email1")) {
/* 564 */           if (!str2.isEmpty()) {
/* 565 */             Matcher matcher = pattern.matcher(str2);
/* 566 */             if (!matcher.matches())
/*     */             {
/* 568 */               stringBuffer.append(SystemEnv.getHtmlLabelName(525064, Util.threadVarLanguage()) + "&nbsp;&nbsp;");
/* 569 */               bool = true;
/*     */             }
/*     */           
/*     */           } 
/* 573 */         } else if (str1.toLowerCase().equals("mobilephone")) {
/* 574 */           if (!str2.isEmpty()) {
/* 575 */             Pattern pattern1 = Pattern.compile("^((1[0-9][0-9])|(15[^4,\\D])|(18[^1^4,\\D]))\\d{8}");
/* 576 */             Matcher matcher = pattern1.matcher(str2);
/* 577 */             if (!matcher.matches())
/*     */             {
/* 579 */               stringBuffer.append(SystemEnv.getHtmlLabelName(525065, Util.threadVarLanguage()) + "&nbsp;&nbsp;");
/* 580 */               bool = true;
/*     */             }
/*     */           
/*     */           } 
/* 584 */         } else if (str1.toLowerCase().equals("phoneoffice")) {
/* 585 */           if (!str2.isEmpty()) {
/* 586 */             Pattern pattern1 = Pattern.compile("^0\\d{2,3}-\\d{6,8}|0\\d{2,3}-\\d{6,8}$");
/* 587 */             Matcher matcher = pattern1.matcher(str2);
/* 588 */             if (!matcher.matches())
/*     */             {
/* 590 */               stringBuffer.append(SystemEnv.getHtmlLabelName(525066, Util.threadVarLanguage()) + "&nbsp;&nbsp;");
/* 591 */               bool = true;
/*     */             }
/*     */           
/*     */           }
/*     */         
/* 596 */         } else if (str1.toLowerCase().equals("firstname")) {
/* 597 */           if (str2.isEmpty()) {
/* 598 */             stringBuffer.append((String)mustMap.get("firstname") + SystemEnv.getHtmlLabelName(385284, Util.threadVarLanguage()) + "&nbsp;&nbsp;");
/* 599 */             bool = true;
/*     */           } 
/* 601 */         } else if (str1.toLowerCase().equals("status") && 
/* 602 */           str2.isEmpty()) {
/* 603 */           stringBuffer.append((String)mustMap.get("status") + SystemEnv.getHtmlLabelName(385284, Util.threadVarLanguage()) + "&nbsp;&nbsp;");
/* 604 */           bool = true;
/*     */         } 
/*     */       } 
/*     */       
/* 608 */       if (bool) {
/* 609 */         jSONObject.put("code", "4");
/* 610 */         jSONObject.put("errorCustomerName", str);
/* 611 */         jSONObject.put("errorMes", stringBuffer.toString());
/* 612 */         this.errorList.add(jSONObject.toString());
/*     */       } 
/*     */       
/* 615 */       this.customerNameList.add(str);
/*     */       
/* 617 */       return bool;
/* 618 */     } catch (Exception exception) {
/* 619 */       exception.printStackTrace();
/*     */       
/* 621 */       return bool;
/*     */     } 
/*     */   }
/*     */   
/*     */   public static void main(String[] paramArrayOfString) {
/* 626 */     Pattern pattern = Pattern.compile("^((1[0-9][0-9])|(15[^4,\\D])|(18[^1^4,\\D]))\\d{8}");
/* 627 */     Matcher matcher = pattern.matcher("19786198980");
/* 628 */     if (matcher.matches()) {
/*     */       
/* 630 */       System.out.println("sdfsdf");
/*     */     }
/*     */     else {
/*     */       
/* 634 */       System.out.println("111111111111");
/*     */     } 
/*     */   }
/*     */   public static ArrayList ectract(String paramString1, String paramString2) {
/* 638 */     ArrayList<String> arrayList = new ArrayList();
/*     */     
/*     */     try {
/* 641 */       FileInputStream fileInputStream = new FileInputStream(paramString1);
/*     */       
/* 643 */       ZipInputStream zipInputStream = new ZipInputStream(fileInputStream);
/* 644 */       ZipEntry zipEntry = null;
/* 645 */       byte[] arrayOfByte = new byte[256];
/* 646 */       while ((zipEntry = zipInputStream.getNextEntry()) != null) {
/* 647 */         File file1 = new File(paramString2 + zipEntry.getName());
/* 648 */         File file2 = new File(file1.getParentFile().getPath());
/* 649 */         if (zipEntry.isDirectory()) {
/* 650 */           if (!file1.exists())
/* 651 */             file1.mkdirs(); 
/* 652 */           zipInputStream.closeEntry(); continue;
/*     */         } 
/* 654 */         if (!file2.exists())
/* 655 */           file2.mkdirs(); 
/* 656 */         FileOutputStream fileOutputStream = new FileOutputStream(file1);
/*     */         
/* 658 */         arrayList.add(file1.getAbsolutePath()); int i;
/* 659 */         while ((i = zipInputStream.read(arrayOfByte)) != -1)
/* 660 */           fileOutputStream.write(arrayOfByte, 0, i); 
/* 661 */         zipInputStream.closeEntry();
/* 662 */         fileOutputStream.close();
/*     */       } 
/*     */       
/* 665 */       fileInputStream.close();
/* 666 */       zipInputStream.close();
/* 667 */     } catch (Exception exception) {
/* 668 */       System.err.println("Extract error:" + exception.getMessage());
/*     */     } 
/* 670 */     return arrayList;
/*     */   }
/*     */   
/*     */   private void delFolder(String paramString) {
/*     */     try {
/* 675 */       delAllFile(paramString);
/* 676 */       String str = paramString;
/* 677 */       str = str.toString();
/* 678 */       File file = new File(str);
/* 679 */       FileSecurityUtil.deleteFile(file);
/* 680 */     } catch (Exception exception) {
/* 681 */       exception.printStackTrace();
/*     */     } 
/*     */   }
/*     */   
/*     */   private boolean delAllFile(String paramString) {
/* 686 */     boolean bool = false;
/* 687 */     File file1 = new File(paramString);
/* 688 */     if (!file1.exists()) {
/* 689 */       return bool;
/*     */     }
/* 691 */     if (!file1.isDirectory()) {
/* 692 */       return bool;
/*     */     }
/* 694 */     String[] arrayOfString = file1.list();
/* 695 */     File file2 = null;
/* 696 */     for (byte b = 0; b < arrayOfString.length; b++) {
/* 697 */       if (paramString.endsWith(File.separator)) {
/* 698 */         file2 = new File(paramString + arrayOfString[b]);
/*     */       } else {
/* 700 */         file2 = new File(paramString + File.separator + arrayOfString[b]);
/*     */       } 
/* 702 */       if (file2.isFile()) {
/* 703 */         FileSecurityUtil.deleteFile(file2);
/*     */       }
/* 705 */       if (file2.isDirectory()) {
/* 706 */         delAllFile(paramString + "/" + arrayOfString[b]);
/* 707 */         delFolder(paramString + "/" + arrayOfString[b]);
/* 708 */         bool = true;
/*     */       } 
/*     */     } 
/* 711 */     return bool;
/*     */   }
/*     */ 
/*     */   
/*     */   public User getUser() {
/* 716 */     return this.user;
/*     */   }
/*     */   
/*     */   public void setUser(User paramUser) {
/* 720 */     this.user = paramUser;
/*     */   }
/*     */   
/*     */   public int getTotal() {
/* 724 */     return this.total;
/*     */   }
/*     */   
/*     */   public void setTotal(int paramInt) {
/* 728 */     this.total = paramInt;
/*     */   }
/*     */   
/*     */   public int getSuccess() {
/* 732 */     return this.success;
/*     */   }
/*     */   
/*     */   public void setSuccess(int paramInt) {
/* 736 */     this.success = paramInt;
/*     */   }
/*     */   
/*     */   public int getFail() {
/* 740 */     return this.fail;
/*     */   }
/*     */   
/*     */   public void setFail(int paramInt) {
/* 744 */     this.fail = paramInt;
/*     */   }
/*     */   
/*     */   public String getErrormsg() {
/* 748 */     return this.Errormsg;
/*     */   }
/*     */   
/*     */   public void setErrormsg(String paramString) {
/* 752 */     this.Errormsg = paramString;
/*     */   }
/*     */   
/*     */   public String getDepartment() {
/* 756 */     return this.department;
/*     */   }
/*     */   
/*     */   public void setDepartment(String paramString) {
/* 760 */     this.department = paramString;
/*     */   }
/*     */   
/*     */   public String getSubcompanyid() {
/* 764 */     return this.subcompanyid;
/*     */   }
/*     */   
/*     */   public void setSubcompanyid(String paramString) {
/* 768 */     this.subcompanyid = paramString;
/*     */   }
/*     */   
/*     */   public boolean isErrTemplate() {
/* 772 */     return this.isErrTemplate;
/*     */   }
/*     */   
/*     */   public void setErrTemplate(boolean paramBoolean) {
/* 776 */     this.isErrTemplate = paramBoolean;
/*     */   }
/*     */   
/*     */   public boolean isErreData() {
/* 780 */     return this.isErreData;
/*     */   }
/*     */   
/*     */   public void setErreData(boolean paramBoolean) {
/* 784 */     this.isErreData = paramBoolean;
/*     */   }
/*     */   
/*     */   public boolean isSaveSuccess() {
/* 788 */     return this.isSaveSuccess;
/*     */   }
/*     */   
/*     */   public void setSaveSuccess(boolean paramBoolean) {
/* 792 */     this.isSaveSuccess = paramBoolean;
/*     */   }
/*     */   
/*     */   public String getCurrentDate() {
/* 796 */     return this.currentDate;
/*     */   }
/*     */   
/*     */   public void setCurrentDate(String paramString) {
/* 800 */     this.currentDate = paramString;
/*     */   }
/*     */   
/*     */   public int getIsCover() {
/* 804 */     return this.isCover;
/*     */   }
/*     */   
/*     */   public void setIsCover(int paramInt) {
/* 808 */     this.isCover = paramInt;
/*     */   }
/*     */   
/*     */   public List<String> getErrorList() {
/* 812 */     return this.errorList;
/*     */   }
/*     */   
/*     */   public void setErrorList(List<String> paramList) {
/* 816 */     this.errorList = paramList;
/*     */   }
/*     */   
/*     */   public List<Integer> getErrorRowIdList() {
/* 820 */     return this.errorRowIdList;
/*     */   }
/*     */   
/*     */   public void setErrorRowIdList(List<Integer> paramList) {
/* 824 */     this.errorRowIdList = paramList;
/*     */   }
/*     */   
/*     */   public static List<String> getMustCallList() {
/* 828 */     return mustCallList;
/*     */   }
/*     */   
/*     */   public static void setMustCallList(List<String> paramList) {
/* 832 */     mustCallList = paramList;
/*     */   }
/*     */   
/*     */   public boolean isNoData() {
/* 836 */     return this.isNoData;
/*     */   }
/*     */   
/*     */   public void setNoData(boolean paramBoolean) {
/* 840 */     this.isNoData = paramBoolean;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/crm/ExcelToDB/RdeployCrmExcelToDB.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */