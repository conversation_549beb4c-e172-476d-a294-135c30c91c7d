/*    */ package weaver.crm.ExcelToDB;
/*    */ 
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.HrmUserVarify;
/*    */ import weaver.hrm.User;
/*    */ import weaver.hrm.resource.ResourceComInfo;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ public class CrmExcelToDBLog
/*    */ {
/*    */   public String getImportType(String paramString1, String paramString2) {
/* 15 */     String str = "";
/* 16 */     if (paramString1.equals("1")) {
/*    */       
/* 18 */       str = SystemEnv.getHtmlLabelName(611, Util.getIntValue(paramString2));
/* 19 */     } else if (paramString1.equals("2")) {
/* 20 */       str = SystemEnv.getHtmlLabelName(17744, Util.getIntValue(paramString2));
/*    */     } 
/*    */     
/* 23 */     return str;
/*    */   }
/*    */   public String Detail(String paramString1, String paramString2) {
/* 26 */     return "<a href =\"javascript:showDetail1('" + paramString2 + "')\">" + paramString1 + "</a>";
/*    */   }
/*    */ 
/*    */   
/*    */   public String isDeleted(String paramString1, String paramString2) {
/* 31 */     String str = "";
/* 32 */     if (paramString1.equals("1")) {
/* 33 */       str = SystemEnv.getHtmlLabelName(18967, Util.getIntValue(paramString2));
/*    */     }
/* 35 */     return str;
/*    */   }
/*    */   
/*    */   public String getResult(String paramString1, String paramString2) {
/* 39 */     String[] arrayOfString = Util.splitString(paramString2, "+");
/* 40 */     String str1 = arrayOfString[0];
/* 41 */     String str2 = arrayOfString[1];
/* 42 */     String str3 = arrayOfString[2];
/* 43 */     String str4 = "";
/* 44 */     if (str1.equals("1")) {
/* 45 */       String[] arrayOfString1 = paramString1.split(",");
/* 46 */       if ("3".equals(str3)) {
/* 47 */         str4 = SystemEnv.getHtmlLabelName(384061, Util.getIntValue(str2)).replaceAll("\\{rownum}", arrayOfString1[0] + "");
/*    */       } else {
/* 49 */         str4 = SystemEnv.getHtmlLabelName(32935, Util.getIntValue(str2)) + arrayOfString1[0] + SystemEnv.getHtmlLabelName(18256, Util.getIntValue(str2)) + "，" + SystemEnv.getHtmlLabelName(611, Util.getIntValue(str2)) + arrayOfString1[1] + SystemEnv.getHtmlLabelName(18256, Util.getIntValue(str2)) + "，" + SystemEnv.getHtmlLabelName(389290, Util.getIntValue(str2)) + arrayOfString1[2] + SystemEnv.getHtmlLabelName(18256, Util.getIntValue(str2));
/*    */       } 
/* 51 */     } else if (str1.equals("2")) {
/* 52 */       String[] arrayOfString1 = paramString1.split(",");
/* 53 */       str4 = SystemEnv.getHtmlLabelName(32935, Util.getIntValue(str2)) + arrayOfString1[0] + SystemEnv.getHtmlLabelName(18256, Util.getIntValue(str2)) + "，" + SystemEnv.getHtmlLabelName(611, Util.getIntValue(str2)) + arrayOfString1[1] + SystemEnv.getHtmlLabelName(18256, Util.getIntValue(str2)) + "，" + SystemEnv.getHtmlLabelName(17744, Util.getIntValue(str2)) + arrayOfString1[2] + SystemEnv.getHtmlLabelName(18256, Util.getIntValue(str2));
/*    */     } 
/*    */ 
/*    */     
/* 57 */     return str4;
/*    */   }
/*    */   
/*    */   public String logRight(User paramUser) throws Exception {
/* 61 */     ResourceComInfo resourceComInfo = new ResourceComInfo();
/* 62 */     HrmUserVarify hrmUserVarify = new HrmUserVarify();
/* 63 */     RecordSet recordSet = new RecordSet();
/* 64 */     String str1 = Util.null2String(Integer.valueOf(paramUser.getUID()));
/* 65 */     String str2 = "";
/* 66 */     String str3 = "";
/* 67 */     String str4 = "";
/* 68 */     str3 = "select * from HrmRoleMembers where roleid='8' and resourceid='" + str1 + "'";
/* 69 */     recordSet.executeQuery(str3, new Object[0]);
/* 70 */     if (str1.equals("1"))
/* 71 */       return str2; 
/* 72 */     if (recordSet.next())
/* 73 */       return str2; 
/* 74 */     if (HrmUserVarify.checkUserRight("EditCustomer:Delete", paramUser)) {
/* 75 */       return str2;
/*    */     }
/* 77 */     str4 = resourceComInfo.getUnderliningByUserId(str1);
/* 78 */     if (!str4.equals("")) {
/* 79 */       str4 = str4 + "," + str1;
/*    */     } else {
/* 81 */       str4 = str1;
/*    */     } 
/* 83 */     str2 = "and t2.id in (" + str4 + ")";
/*    */     
/* 85 */     return str2;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/crm/ExcelToDB/CrmExcelToDBLog.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */