/*     */ package weaver.crm.data;
/*     */ 
/*     */ import java.sql.Timestamp;
/*     */ import java.util.Date;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CustomerModifyLog
/*     */ {
/*     */   public void modify(int paramInt1, int paramInt2, int paramInt3) {
/*  23 */     if (paramInt2 == paramInt3) {
/*     */       return;
/*     */     }
/*  26 */     Date date = new Date();
/*  27 */     long l = date.getTime();
/*  28 */     Timestamp timestamp = new Timestamp(l);
/*  29 */     String str1 = timestamp.toString().substring(0, 4) + "-" + timestamp.toString().substring(5, 7) + "-" + timestamp.toString().substring(8, 10);
/*  30 */     String str2 = timestamp.toString().substring(11, 13) + ":" + timestamp.toString().substring(14, 16) + ":" + timestamp.toString().substring(17, 19);
/*     */     
/*  32 */     RecordSet recordSet = new RecordSet();
/*  33 */     String str3 = "delete from CRM_ViewLog2 where customerid=" + paramInt1;
/*  34 */     recordSet.executeSql(str3);
/*     */     
/*  36 */     str3 = "insert into CRM_ViewLog2(customerid,oldmanager,newmanager,movedate,movetime) values(" + paramInt1 + "," + paramInt2 + "," + paramInt3 + ",'" + str1 + "','" + str2 + "')";
/*  37 */     recordSet.executeSql(str3);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void modify(String paramString1, String paramString2, String paramString3) {
/*  47 */     modify(Util.getIntValue(paramString1, 0), Util.getIntValue(paramString2, 0), Util.getIntValue(paramString3, 0));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void modify2(int paramInt1, int paramInt2) {
/*  56 */     RecordSet recordSet = new RecordSet();
/*  57 */     String str = "select manager from CRM_CustomerInfo where id=" + paramInt1;
/*  58 */     recordSet.executeSql(str);
/*  59 */     if (recordSet.next()) {
/*  60 */       modify(paramInt1, recordSet.getInt("manager"), paramInt2);
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void modify2(String paramString1, String paramString2) {
/*  69 */     modify2(Util.getIntValue(paramString1, 0), Util.getIntValue(paramString2, 0));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void modifyAll(int paramInt1, int paramInt2) {
/*  78 */     Date date = new Date();
/*  79 */     long l = date.getTime();
/*  80 */     Timestamp timestamp = new Timestamp(l);
/*  81 */     String str1 = timestamp.toString().substring(0, 4) + "-" + timestamp.toString().substring(5, 7) + "-" + timestamp.toString().substring(8, 10);
/*  82 */     String str2 = timestamp.toString().substring(11, 13) + ":" + timestamp.toString().substring(14, 16) + ":" + timestamp.toString().substring(17, 19);
/*     */     
/*  84 */     RecordSet recordSet1 = new RecordSet();
/*  85 */     RecordSet recordSet2 = new RecordSet();
/*     */     
/*  87 */     String str3 = "delete from CRM_ViewLog2 where customerid in (select id from CRM_CustomerInfo where manager=" + paramInt1 + ")";
/*  88 */     recordSet1.executeSql(str3);
/*  89 */     str3 = "select id from CRM_CustomerInfo where manager=" + paramInt1;
/*  90 */     recordSet1.executeSql(str3);
/*  91 */     while (recordSet1.next()) {
/*  92 */       recordSet2.executeSql("insert into CRM_ViewLog2(customerid,oldmanager,newmanager,movedate,movetime) values(" + recordSet1.getInt("id") + "," + paramInt1 + "," + paramInt2 + ",'" + str1 + "','" + str2 + "')");
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void modifyAll(String paramString1, String paramString2) {
/* 104 */     modifyAll(Util.getIntValue(paramString1, 0), Util.getIntValue(paramString2, 0));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void deleteCustomerLog(int paramInt) {
/* 111 */     RecordSet recordSet = new RecordSet();
/* 112 */     String str = "delete from CRM_ViewLog2 where customerid=" + paramInt;
/* 113 */     recordSet.executeSql(str);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void deleteCustomerLog(String paramString) {
/* 121 */     deleteCustomerLog(Util.getIntValue(paramString, 0));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void deleteCustomerLog(int paramInt1, int paramInt2) {
/* 130 */     RecordSet recordSet = new RecordSet();
/* 131 */     String str = "delete from CRM_ViewLog2 where customerid=" + paramInt1 + " and newmanager=" + paramInt2;
/* 132 */     recordSet.executeSql(str);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/crm/data/CustomerModifyLog.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */