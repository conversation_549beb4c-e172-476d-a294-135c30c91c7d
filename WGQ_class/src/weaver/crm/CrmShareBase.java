/*     */ package weaver.crm;
/*     */ 
/*     */ import com.api.crm.service.CustomerService;
/*     */ import com.engine.common.service.HrmCommonService;
/*     */ import com.engine.common.service.impl.HrmCommonServiceImpl;
/*     */ import com.engine.core.exception.ECException;
/*     */ import java.util.Date;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.common.DateUtil;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.crm.data.CustomerModifyLog;
/*     */ import weaver.crm.util.CrmUtil;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.GCONST;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.companyvirtual.ResourceVirtualComInfo;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ import weaver.system.SysRemindWorkflow;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CrmShareBase
/*     */   extends BaseBean
/*     */ {
/*  29 */   public ResourceComInfo ResourceComInfo = null;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   ResourceVirtualComInfo resourceVirtualComInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public HrmCommonService hrmcomm;
/*     */ 
/*     */ 
/*     */   
/*     */   public static final boolean isPro = false;
/*     */ 
/*     */ 
/*     */   
/*     */   String loginType;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void initOldShare() throws Exception {
/*  53 */     RecordSet recordSet = new RecordSet();
/*  54 */     String str = "";
/*  55 */     recordSet.executeSql("select isinitialized from CRMshareinittable");
/*  56 */     if (recordSet.next()) str = Util.null2String(recordSet.getString("isinitialized")); 
/*  57 */     if (str.equals("0")) {
/*  58 */       recordSet.executeSql("select id from crm_customerinfo");
/*  59 */       while (recordSet.next()) {
/*  60 */         String str1 = recordSet.getString("id");
/*  61 */         setDefaultShare(str1);
/*     */       } 
/*     */     } 
/*  64 */     recordSet.executeSql("update CRMshareinittable set isinitialized = '1' ");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getRightLevelForCRM(String paramString1, String paramString2, String paramString3) {
/*  75 */     boolean bool = CrmUtil.isInSeas(paramString2);
/*     */     
/*  77 */     int i = 0;
/*  78 */     if (paramString1.equals("") || paramString2.equals("")) return i;
/*     */     
/*  80 */     String str = " select max(sharelevel) as sharelevel from CRM_ShareInfo where " + getShareSqlWhere(paramString1, "1") + " and relateditemid=" + paramString2;
/*  81 */     RecordSet recordSet = new RecordSet();
/*     */ 
/*     */     
/*  84 */     if (paramString3.equals("1")) {
/*  85 */       str = "select max(sharelevel) as sharelevel from CRM_ShareInfo where relateditemid=" + paramString2 + " and deleted=0 and sharetype=9 and contents=" + paramString1;
/*     */     }
/*  87 */     recordSet.executeSql(str);
/*  88 */     if (recordSet.next()) {
/*  89 */       i = recordSet.getInt("sharelevel");
/*  90 */       if (bool && i > 1) return 1; 
/*  91 */       return i;
/*     */     } 
/*  93 */     return i;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getRightLevelForCRM(String paramString1, String paramString2) {
/* 103 */     return getRightLevelForCRM(paramString1, paramString2, "0");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getAllCrm(String paramString) throws Exception {
/* 112 */     String str1 = "";
/* 113 */     String str2 = "select distinct relateditemid from CRM_ShareInfo " + getShareSqlWhere(paramString, "1");
/* 114 */     RecordSet recordSet = new RecordSet();
/* 115 */     recordSet.executeSql(str2);
/* 116 */     while (recordSet.next()) {
/* 117 */       str1 = str1 + recordSet.getString("relateditemid") + ",";
/*     */     }
/* 119 */     if (!str1.equals("")) str1 = str1.substring(0, str1.length() - 1);
/*     */     
/* 121 */     return str1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean isSystemManager(String paramString) throws Exception {
/* 129 */     boolean bool = false;
/*     */ 
/*     */ 
/*     */     
/* 133 */     if (paramString.equals("1")) {
/* 134 */       return true;
/*     */     }
/* 136 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean isCrmManager(String paramString) throws Exception {
/* 144 */     RecordSet recordSet = new RecordSet();
/* 145 */     recordSet.executeQuery("select id from HrmRoleMembers t where roleid = 8 and rolelevel=2 and " + this.hrmcomm.getHrmRoleMembers_queryConditionSql(Util.getIntValue(paramString), "t"), new Object[0]);
/* 146 */     return recordSet.next();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getRoleSql(String paramString) throws Exception {
/* 154 */     String str = "";
/* 155 */     int i = Util.getIntValue(this.ResourceComInfo.getDepartmentID(paramString), -1);
/* 156 */     int j = Util.getIntValue(this.ResourceComInfo.getSubCompanyID(paramString), -1);
/* 157 */     int k = Util.getIntValue(this.ResourceComInfo.getSeclevel(paramString), 0);
/* 158 */     RecordSet recordSet = new RecordSet();
/* 159 */     recordSet.executeSql("select roleid,rolelevel from HrmRoleMembers where resourceid=" + paramString);
/*     */     
/* 161 */     while (recordSet.next()) {
/* 162 */       String str1 = recordSet.getString("roleid");
/* 163 */       String str2 = recordSet.getString("rolelevel");
/* 164 */       if (str.equals("")) {
/* 165 */         if (str2.equals("0")) {
/* 166 */           str = str + " (sharetype=3  and  contents=" + str1 + " and deptorcomid=" + i + " and rolelevel=" + str2 + " and seclevel<=" + k + " and seclevelMax >= " + k + ") "; continue;
/* 167 */         }  if (str2.equals("1")) {
/* 168 */           str = str + " ( sharetype=3  and contents=" + str1 + " and deptorcomid=" + j + " and rolelevel=" + str2 + " and seclevel<=" + k + " and seclevelMax >= " + k + ") "; continue;
/*     */         } 
/* 170 */         str = str + " (  sharetype=3  and contents=" + str1 + " and rolelevel=" + str2 + " and seclevel<=" + k + " and seclevelMax >= " + k + ") "; continue;
/*     */       } 
/* 172 */       if (str2.equals("0")) {
/* 173 */         str = str + " or (sharetype=3  and contents=" + str1 + " and deptorcomid=" + i + " and rolelevel=" + str2 + " and seclevel<=" + k + " and seclevelMax >= " + k + ") "; continue;
/* 174 */       }  if (str2.equals("1")) {
/* 175 */         str = str + " or (sharetype=3  and contents=" + str1 + " and deptorcomid=" + j + " and rolelevel=" + str2 + " and seclevel<=" + k + " and seclevelMax >= " + k + ") "; continue;
/*     */       } 
/* 177 */       str = str + " or (sharetype=3  and contents=" + str1 + " and rolelevel=" + str2 + "  and seclevel<=" + k + " and seclevelMax >= " + k + ") ";
/*     */     } 
/* 179 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setDefaultShare(String paramString) throws Exception {
/* 187 */     String str = "";
/*     */     
/* 189 */     RecordSet recordSet1 = new RecordSet();
/* 190 */     recordSet1.executeSql("delete from CRM_ShareInfo where isdefault='1' and relateditemid=" + paramString);
/* 191 */     recordSet1.executeSql("select manager from CRM_CustomerInfo where id=" + paramString);
/* 192 */     if (recordSet1.next()) {
/* 193 */       str = Util.null2String(recordSet1.getString("manager"));
/* 194 */       recordSet1.executeSql("insert into CRM_ShareInfo (relateditemid,sharetype,seclevel,seclevelMax,sharelevel,crmid,isdefault,userid,contents) values (" + paramString + ",1,0,0,3,0,1," + str + "," + str + ")");
/*     */     } 
/*     */     
/* 197 */     String[] arrayOfString = Util.TokenizerString2(this.ResourceComInfo.getManagers(str), ","); int i;
/* 198 */     for (i = 0; i < arrayOfString.length; i++) {
/* 199 */       String str1 = arrayOfString[i];
/* 200 */       recordSet1.executeSql("insert into CRM_ShareInfo (relateditemid,sharetype,seclevel,seclevelMax,sharelevel,crmid,isdefault,userid,contents) values (" + paramString + ",1,0,0,3,0,1," + str1 + "," + str1 + ")");
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/* 205 */     i = Util.getIntValue(this.ResourceComInfo.getDepartmentID(str), -1);
/* 206 */     int j = Util.getIntValue(this.ResourceComInfo.getSubCompanyID(str), -1);
/* 207 */     recordSet1.executeSql("insert into CRM_ShareInfo (relateditemid,sharetype,seclevel,seclevelMax,rolelevel,sharelevel,roleid,crmid,isdefault,deptorcomid,contents) values (" + paramString + ",3,0,100,0,4,8,0,1," + i + ",8)");
/* 208 */     recordSet1.executeSql("insert into CRM_ShareInfo (relateditemid,sharetype,seclevel,seclevelMax,rolelevel,sharelevel,roleid,crmid,isdefault,deptorcomid,contents) values (" + paramString + ",3,0,100,1,4,8,0,1," + j + ",8)");
/* 209 */     recordSet1.executeSql("insert into CRM_ShareInfo (relateditemid,sharetype,seclevel,seclevelMax,rolelevel,sharelevel,roleid,crmid,isdefault,contents) values (" + paramString + ",3,0,100,2,4,8,0,1,8)");
/*     */ 
/*     */     
/* 212 */     RecordSet recordSet2 = new RecordSet();
/* 213 */     recordSet1.executeSql("select * from CRM_T_ShareInfo where relateditemid=(select type from CRM_customerinfo where id=" + paramString + ")");
/* 214 */     while (recordSet1.next()) {
/* 215 */       int k = Util.getIntValue(recordSet1.getString("sharetype"), 0);
/* 216 */       int m = Util.getIntValue(recordSet1.getString("seclevel"), 0);
/* 217 */       int n = Util.getIntValue(recordSet1.getString("seclevelMax"), 100);
/* 218 */       int i1 = Util.getIntValue(recordSet1.getString("rolelevel"), 0);
/* 219 */       int i2 = Util.getIntValue(recordSet1.getString("sharelevel"), 0);
/* 220 */       int i3 = Util.getIntValue(recordSet1.getString("userid"), 0);
/* 221 */       int i4 = Util.getIntValue(recordSet1.getString("departmentid"), 0);
/* 222 */       int i5 = Util.getIntValue(recordSet1.getString("roleid"), 0);
/* 223 */       int i6 = Util.getIntValue(recordSet1.getString("foralluser"), 0);
/* 224 */       int i7 = Util.getIntValue(recordSet1.getString("subcompanyid"), 0);
/* 225 */       String str1 = Util.null2String(recordSet1.getString("jobtitleid"), "0");
/* 226 */       int i8 = Util.getIntValue(recordSet1.getString("joblevel"), 0);
/* 227 */       String str2 = Util.null2String(recordSet1.getString("scopeid"), "");
/* 228 */       int i9 = 1;
/* 229 */       int i10 = -1;
/* 230 */       if (k == 1) {
/* 231 */         i9 = i3;
/*     */       }
/* 233 */       else if (k == 2) {
/* 234 */         i9 = i4;
/*     */       }
/* 236 */       else if (k == 3) {
/* 237 */         if (i1 == 1) {
/* 238 */           i10 = j;
/* 239 */         } else if (i1 == 0) {
/* 240 */           i10 = i;
/*     */         } 
/* 242 */         i9 = i5;
/* 243 */       } else if (k == 4) {
/* 244 */         i9 = 1;
/* 245 */       } else if (k == 5) {
/* 246 */         i9 = i7;
/* 247 */       } else if (k == 6) {
/* 248 */         i9 = Integer.parseInt(str1);
/*     */       } 
/* 250 */       recordSet2.executeSql("insert into CRM_ShareInfo (relateditemid,sharetype,seclevel,seclevelMax,rolelevel,sharelevel,userid,departmentid,roleid,foralluser,crmid,contents,deptorcomid,subcompanyid,jobtitleid, joblevel, scopeid) values \t(" + paramString + "," + k + "," + m + "," + n + "," + i1 + "," + i2 + "," + i3 + "," + i4 + "," + i5 + "," + i6 + ",0," + i9 + "," + i10 + "," + i7 + ",'" + str1 + "'," + i8 + ",'" + str2 + "')");
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getTempTable(String paramString) {
/* 260 */     RecordSet recordSet = new RecordSet();
/* 261 */     String str = "";
/*     */     
/* 263 */     if (recordSet.getDBType().equals("oracle")) {
/* 264 */       str = "(select distinct relateditemid from CRM_ShareInfo where " + getShareSqlWhere(paramString, "1") + ")";
/*     */     } else {
/* 266 */       str = "(select distinct relateditemid from " + getShareSqlWhere2(paramString, "1") + ")";
/*     */     } 
/*     */     
/* 269 */     return str;
/*     */   }
/*     */   
/* 272 */   public CrmShareBase() { this.loginType = "0"; try {
/*     */       this.ResourceComInfo = new ResourceComInfo(); this.resourceVirtualComInfo = new ResourceVirtualComInfo(); this.hrmcomm = (HrmCommonService)new HrmCommonServiceImpl();
/* 274 */     } catch (Exception exception) {} } public String getTempTable2(String paramString1, String paramString2) { this.loginType = paramString1;
/* 275 */     RecordSet recordSet = new RecordSet();
/* 276 */     String str = "";
/*     */     
/* 278 */     if (recordSet.getDBType().equals("oracle")) {
/* 279 */       str = "(select distinct relateditemid from CRM_ShareInfo where " + getShareSqlWhere(paramString2, "1") + ")";
/*     */     } else {
/* 281 */       str = "(select distinct relateditemid from " + getShareSqlWhere2(paramString2, "1") + ")";
/*     */     } 
/*     */     
/* 284 */     return str; }
/*     */ 
/*     */ 
/*     */   
/*     */   public String getTempTable3(String paramString) {
/* 289 */     RecordSet recordSet = new RecordSet();
/* 290 */     String str = "";
/*     */     
/* 292 */     if (recordSet.getDBType().equals("oracle")) {
/* 293 */       str = "(select distinct relateditemid from CRM_ShareInfo where " + getShareSqlWhere(paramString, "2") + ")";
/*     */     } else {
/* 295 */       str = "(select distinct relateditemid from " + getShareSqlWhere2(paramString, "2") + ")";
/*     */     } 
/*     */     
/* 298 */     return str;
/*     */   }
/*     */   
/*     */   public String getShareSqlWhere2(String paramString1, String paramString2) {
/* 302 */     String str = "";
/*     */     try {
/* 304 */       int i = Util.getIntValue(this.ResourceComInfo.getSeclevel(paramString1), 0);
/* 305 */       int j = Util.getIntValue(this.ResourceComInfo.getDepartmentID(paramString1), -1);
/* 306 */       int k = Util.getIntValue(this.ResourceComInfo.getSubCompanyID(paramString1), -1);
/*     */ 
/*     */       
/* 309 */       String str1 = this.ResourceComInfo.getJobTitle(paramString1);
/*     */       
/* 311 */       HrmCommonServiceImpl hrmCommonServiceImpl = new HrmCommonServiceImpl();
/* 312 */       List<Map> list = hrmCommonServiceImpl.getRoleInfo(Integer.parseInt(paramString1));
/* 313 */       String str2 = "";
/* 314 */       for (byte b = 0; b < list.size(); b++) {
/* 315 */         Map map = list.get(b);
/* 316 */         String str9 = (String)map.get("roleid");
/* 317 */         String str10 = (String)map.get("rolelevel");
/* 318 */         str2 = str2 + " (contents=" + str9 + " and rolelevel<=" + str10 + " and " + i + ">=seclevel and seclevelMax >= " + i + ")";
/* 319 */         if (b < list.size() - 1) {
/* 320 */           str2 = str2 + " or ";
/*     */         }
/*     */       } 
/*     */       
/* 324 */       String str3 = "";
/* 325 */       if (str2.equals("")) {
/* 326 */         str3 = "1=2";
/*     */       } else {
/* 328 */         str3 = str2;
/*     */       } 
/* 330 */       boolean bool = isSystemManager("" + paramString1);
/*     */       
/* 332 */       if (bool) {
/* 333 */         str = "( select relateditemid from CRM_ShareInfo where deleted =0 ) t ";
/* 334 */         return str;
/*     */       } 
/*     */       
/* 337 */       String str4 = "";
/* 338 */       if (paramString2.equals("2")) {
/* 339 */         str4 = " and sharelevel>1";
/*     */       }
/*     */       
/* 342 */       String[] arrayOfString1 = Util.TokenizerString2(Util.null2String(this.resourceVirtualComInfo.getDepartmentids(paramString1)), ",");
/* 343 */       String[] arrayOfString2 = Util.TokenizerString2(Util.null2String(this.resourceVirtualComInfo.getSubcompanyids(paramString1)), ",");
/*     */       
/* 345 */       String str5 = " select relateditemid from CRM_ShareInfo where deleted =0 and sharetype=2" + str4 + " and contents=" + j + " and " + i + ">=seclevel and seclevelMax >= " + i + "  UNION all ";
/* 346 */       for (String str9 : arrayOfString1) {
/* 347 */         str5 = str5 + " select relateditemid from CRM_ShareInfo where deleted =0 and sharetype=2" + str4 + " and contents=" + str9 + " and " + i + ">=seclevel and seclevelMax >= " + i + " UNION all ";
/*     */       }
/*     */       
/* 350 */       String str6 = " select relateditemid from CRM_ShareInfo where deleted =0 and sharetype=5" + str4 + " and contents=" + k + " and " + i + ">=seclevel and seclevelMax >= " + i + " UNION all ";
/* 351 */       for (String str9 : arrayOfString2) {
/* 352 */         str6 = str6 + " select relateditemid from CRM_ShareInfo where deleted =0 and sharetype=5" + str4 + " and contents=" + str9 + " and " + i + ">=seclevel and seclevelMax >= " + i + " UNION all ";
/*     */       }
/*     */       
/* 355 */       String str7 = " select relateditemid from CRM_ShareInfo where deleted =0 and sharetype=6" + str4 + " and contents = '" + str1 + "' and  joblevel = 0 UNION all   select relateditemid from CRM_ShareInfo where deleted =0 and sharetype=6" + str4 + " and contents = '" + str1 + "' and  joblevel = 1 and scopeid like '%," + j + ",%' UNION all    select relateditemid from CRM_ShareInfo where deleted =0 and sharetype=6" + str4 + " and contents = '" + str1 + "' and  joblevel = 2 and scopeid like '%," + k + ",%'  UNION all  ";
/*     */ 
/*     */       
/* 358 */       String str8 = "";
/* 359 */       if ("2".equals(this.loginType)) {
/* 360 */         str8 = " UNION all  select relateditemid from CRM_ShareInfo where deleted =0 and sharetype=9" + str4 + " and contents=" + paramString1;
/*     */       }
/*     */ 
/*     */       
/* 364 */       str = " ( select relateditemid from CRM_ShareInfo where deleted =0 and sharetype=1" + str4 + " and contents=" + paramString1 + " UNION all " + str5 + str6 + str7 + " select relateditemid from CRM_ShareInfo where deleted =0 and sharetype=3" + str4 + " and (" + str3 + ")  UNION all  select relateditemid from CRM_ShareInfo where deleted =0 and sharetype=4" + str4 + " and contents=1 and " + i + ">=seclevel and seclevelMax >= " + i + " " + str8 + " ) t ";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*     */     }
/* 373 */     catch (Exception exception) {}
/*     */     
/* 375 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getShareSqlWhere(String paramString1, String paramString2) {
/* 384 */     String str = "";
/*     */     try {
/* 386 */       int i = Util.getIntValue(this.ResourceComInfo.getSeclevel(paramString1), 0);
/* 387 */       int j = Util.getIntValue(this.ResourceComInfo.getDepartmentID(paramString1), -1);
/* 388 */       int k = Util.getIntValue(this.ResourceComInfo.getSubCompanyID(paramString1), -1);
/*     */       
/* 390 */       String str1 = this.ResourceComInfo.getJobTitle(paramString1);
/*     */ 
/*     */       
/* 393 */       HrmCommonServiceImpl hrmCommonServiceImpl = new HrmCommonServiceImpl();
/* 394 */       List<Map> list = hrmCommonServiceImpl.getRoleInfo(Integer.parseInt(paramString1));
/* 395 */       String str2 = "";
/* 396 */       for (byte b = 0; b < list.size(); b++) {
/* 397 */         Map map = list.get(b);
/* 398 */         String str10 = (String)map.get("roleid");
/* 399 */         String str11 = (String)map.get("rolelevel");
/* 400 */         str2 = str2 + " (contents=" + str10 + " and rolelevel<=" + str11 + " and " + i + ">=seclevel and seclevelMax >= " + i + ")";
/* 401 */         if (b < list.size() - 1) {
/* 402 */           str2 = str2 + " or ";
/*     */         }
/*     */       } 
/*     */       
/* 406 */       String str3 = "";
/* 407 */       if (str2.equals("")) {
/* 408 */         str3 = "1=2";
/*     */       } else {
/* 410 */         str3 = str2;
/*     */       } 
/* 412 */       boolean bool = isSystemManager("" + paramString1);
/*     */       
/* 414 */       if (bool) {
/* 415 */         str = " deleted =0 ";
/* 416 */         return str;
/*     */       } 
/*     */       
/* 419 */       String str4 = "";
/* 420 */       if (paramString2.equals("2")) {
/* 421 */         str4 = " and sharelevel>1";
/*     */       }
/*     */       
/* 424 */       String[] arrayOfString1 = Util.TokenizerString2(Util.null2String(this.resourceVirtualComInfo.getDepartmentids(paramString1)), ",");
/* 425 */       String[] arrayOfString2 = Util.TokenizerString2(Util.null2String(this.resourceVirtualComInfo.getSubcompanyids(paramString1)), ",");
/*     */       
/* 427 */       String str5 = " or (sharetype=2" + str4 + " and contents=" + j + " and " + i + ">=seclevel and seclevelMax >= " + i + " )";
/* 428 */       for (String str10 : arrayOfString1) {
/* 429 */         str5 = str5 + " or (sharetype=2" + str4 + " and contents=" + str10 + " and " + i + ">=seclevel and seclevelMax >= " + i + ") ";
/*     */       }
/*     */       
/* 432 */       String str6 = " or (sharetype=5" + str4 + " and contents=" + k + " and " + i + ">=seclevel and seclevelMax >= " + i + " ) ";
/* 433 */       for (String str10 : arrayOfString2) {
/* 434 */         str6 = str6 + " or( sharetype=5" + str4 + " and contents=" + str10 + " and " + i + ">=seclevel and seclevelMax >= " + i + ") ";
/*     */       }
/*     */       
/* 437 */       String str7 = " or (sharetype=6" + str4 + " and contents = '" + str1 + "' and  joblevel = 0 )   or (sharetype=6" + str4 + " and contents = '" + str1 + "' and  joblevel = 1 and scopeid like '%," + j + ",%')    or (sharetype=6" + str4 + " and contents = '" + str1 + "' and  joblevel = 2 and scopeid like '%," + k + ",%' )  ";
/*     */ 
/*     */ 
/*     */       
/* 441 */       String str8 = " or (sharetype=9" + str4 + " and contents=" + paramString1 + "))";
/* 442 */       String str9 = ") ";
/*     */       
/* 444 */       str = " deleted =0 and ((sharetype=1" + str4 + " and contents=" + paramString1 + ") " + str5 + str6 + str7 + " or (sharetype=3" + str4 + " and (" + str3 + ") )  or (sharetype=4" + str4 + " and contents=1 and " + i + " >= seclevel and seclevelMax >= " + i + ")";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 451 */       if ("2".equals(this.loginType)) {
/* 452 */         str = str + str8;
/*     */       } else {
/* 454 */         str = str + str9;
/*     */       }
/*     */     
/* 457 */     } catch (Exception exception) {}
/* 458 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setShareForNewManager(String paramString) throws Exception {
/* 467 */     writeLog(getClass().getName(), "setShareForNewManager");
/* 468 */     (new Thread(() -> {
/*     */           RecordSet recordSet1 = new RecordSet();
/*     */           
/*     */           RecordSet recordSet2 = new RecordSet();
/*     */           
/*     */           recordSet2.executeSql("select id from CRM_CustomerInfo where manager = " + paramString);
/*     */           while (recordSet2.next()) {
/*     */             String str1 = recordSet2.getString("id");
/*     */             try {
/*     */               setCRM_WPShare_newCRMManager(str1);
/* 478 */             } catch (Exception exception) {
/*     */               exception.printStackTrace();
/*     */             } 
/*     */           } 
/*     */           
/*     */           String str = "";
/*     */           
/*     */           RecordSet recordSet3 = new RecordSet();
/*     */           
/*     */           if (recordSet3.getDBType().equals("oracle")) {
/*     */             str = "select a.id from CRM_CustomerInfo a,hrmresource b where a.manager=b.id and concat(',',managerstr) like '%," + paramString + ",%'";
/*     */           } else if (recordSet3.getDBType().equals("mysql")) {
/*     */             str = "select a.id from CRM_CustomerInfo a,hrmresource b where a.manager=b.id and concat(',',managerstr) like '%," + paramString + ",%'";
/*     */           } else {
/*     */             str = "select a.id from CRM_CustomerInfo a,hrmresource b where a.manager=b.id and ','+b.managerstr like '%," + paramString + ",%'";
/*     */           } 
/*     */           recordSet3.executeSql(str);
/*     */           while (recordSet3.next()) {
/*     */             String str1 = recordSet3.getString("id");
/*     */             try {
/*     */               setCRM_WPShare_newCRMManager(str1);
/* 499 */             } catch (Exception exception) {
/*     */               
/*     */               exception.printStackTrace();
/*     */             } 
/*     */           } 
/* 504 */         })).start();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setCRM_WPShare_newCRMShare(String paramString1, String paramString2) throws Exception {}
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void addContactShare(String paramString1, String paramString2) {
/* 525 */     if (!paramString1.equals("") && !paramString2.equals("")) {
/*     */       
/* 527 */       RecordSet recordSet1 = new RecordSet();
/* 528 */       RecordSet recordSet2 = new RecordSet();
/* 529 */       String str = "select * from CRM_ShareInfo where id=" + paramString2;
/* 530 */       recordSet1.executeSql(str);
/* 531 */       if (recordSet1.next()) {
/*     */         
/* 533 */         String str1 = recordSet1.getString("sharetype");
/* 534 */         String str2 = recordSet1.getString("contents");
/* 535 */         String str3 = recordSet1.getString("seclevel");
/* 536 */         String str4 = recordSet1.getString("seclevelMax");
/*     */         
/* 538 */         str = "select id from WorkPlan where type_n = '3' and crmid=" + paramString1;
/*     */         
/* 540 */         recordSet1.executeSql(str);
/* 541 */         while (recordSet1.next()) {
/* 542 */           String str5 = recordSet1.getString("id");
/* 543 */           str = "insert into workplansharedetail (workid,usertype,sharelevel,shareType,objId,securityLevel,securityLevelMax) values (" + str5 + ",1,0," + str1 + "," + str2 + "," + str3 + "," + str4 + ")";
/*     */           
/* 545 */           recordSet2.execute(str);
/*     */         } 
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setCRM_WPShare_newContact(String paramString1, String paramString2) throws Exception {}
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setCRM_WPShare_newCRMManager(String paramString) throws Exception {
/* 566 */     if (!paramString.equals("")) {
/*     */       
/* 568 */       RecordSet recordSet = new RecordSet();
/* 569 */       String str = "";
/*     */ 
/*     */       
/* 572 */       recordSet.executeSql("delete from CRM_ShareInfo where isdefault='1' and relateditemid=" + paramString);
/* 573 */       recordSet.executeSql("select manager from CRM_CustomerInfo where id=" + paramString);
/* 574 */       if (recordSet.next()) {
/* 575 */         str = Util.null2String(recordSet.getString("manager"));
/* 576 */         recordSet.executeSql("insert into CRM_ShareInfo (relateditemid,sharetype,seclevel,seclevelMax,sharelevel,crmid,isdefault,userid,contents) values (" + paramString + ",1,0,100,3,0,1," + str + "," + str + ")");
/*     */       } 
/*     */       
/* 579 */       String[] arrayOfString = Util.TokenizerString2(this.ResourceComInfo.getManagers(str), ","); int i;
/* 580 */       for (i = 0; i < arrayOfString.length; i++) {
/* 581 */         String str1 = arrayOfString[i];
/* 582 */         recordSet.executeSql("insert into CRM_ShareInfo (relateditemid,sharetype,seclevel,seclevelMax,sharelevel,crmid,isdefault,userid,contents) values (" + paramString + ",1,0,100,3,0,1," + str1 + "," + str1 + ")");
/*     */       } 
/*     */ 
/*     */ 
/*     */       
/* 587 */       i = Util.getIntValue(this.ResourceComInfo.getDepartmentID(str), -1);
/* 588 */       int j = Util.getIntValue(this.ResourceComInfo.getSubCompanyID(str), -1);
/* 589 */       recordSet.executeSql("insert into CRM_ShareInfo (relateditemid,sharetype,seclevel,seclevelMax,rolelevel,sharelevel,roleid,crmid,isdefault,deptorcomid,contents) values (" + paramString + ",3,0,100,0,4,8,0,1," + i + ",8)");
/* 590 */       recordSet.executeSql("insert into CRM_ShareInfo (relateditemid,sharetype,seclevel,seclevelMax,rolelevel,sharelevel,roleid,crmid,isdefault,deptorcomid,contents) values (" + paramString + ",3,0,100,1,4,8,0,1," + j + ",8)");
/* 591 */       recordSet.executeSql("insert into CRM_ShareInfo (relateditemid,sharetype,seclevel,seclevelMax,rolelevel,sharelevel,roleid,crmid,isdefault,contents) values (" + paramString + ",3,0,100,2,4,8,0,1,8)");
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void resetContactShare(String paramString) throws Exception {}
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void resetStatusShare(String paramString) throws Exception {
/* 621 */     if (!paramString.equals("")) {
/*     */       
/* 623 */       RecordSet recordSet = new RecordSet();
/* 624 */       String str1 = "";
/* 625 */       String str2 = "";
/*     */       
/* 627 */       recordSet.executeSql("delete from CRM_ShareInfo where isdefault='1' and sharetype=3 and roleid=171 and contents=171 and relateditemid=" + paramString);
/* 628 */       recordSet.executeSql("select status from CRM_CustomerInfo where id=" + paramString);
/* 629 */       if (recordSet.next()) {
/* 630 */         str1 = Util.null2String(recordSet.getString("manager"));
/* 631 */         str2 = Util.null2String(recordSet.getString("status"));
/*     */         
/* 633 */         if (str2.equals("4") || str2.equals("5") || str2.equals("6") || str2.equals("8")) {
/* 634 */           int i = Util.getIntValue(this.ResourceComInfo.getDepartmentID(str1), -1);
/* 635 */           int j = Util.getIntValue(this.ResourceComInfo.getSubCompanyID(str1), -1);
/* 636 */           recordSet.executeSql("insert into CRM_ShareInfo (relateditemid,sharetype,seclevel,seclevelMax,rolelevel,sharelevel,roleid,crmid,isdefault,deptorcomid,contents) values (" + paramString + ",3,0,100,0,4,171,0,1," + i + ",171)");
/* 637 */           recordSet.executeSql("insert into CRM_ShareInfo (relateditemid,sharetype,seclevel,seclevelMax,rolelevel,sharelevel,roleid,crmid,isdefault,deptorcomid,contents) values (" + paramString + ",3,0,100,1,4,171,0,1," + j + ",171)");
/* 638 */           recordSet.executeSql("insert into CRM_ShareInfo (relateditemid,sharetype,seclevel,seclevelMax,rolelevel,sharelevel,roleid,crmid,isdefault,contents) values (" + paramString + ",3,0,100,2,4,171,0,1,171)");
/*     */         } 
/*     */       } 
/*     */       
/* 642 */       resetContactShare(paramString);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String[] getCrmCount4Hrm(String paramString1, String paramString2) {
/* 654 */     String[] arrayOfString = new String[2];
/* 655 */     int i = 0;
/*     */     
/*     */     try {
/* 658 */       String str = "select count(*) as total from CRM_CustomerInfo t1 join" + getTempTable(paramString2) + " t2   on t1.id = t2.relateditemid where (t1.deleted = 0 or t1.deleted is null) and (t1.seasflag is null or t1.seasflag=3) and t1.manager = " + paramString1;
/*     */       
/* 660 */       RecordSet recordSet = new RecordSet();
/* 661 */       recordSet.execute(str);
/* 662 */       if (recordSet.next()) {
/* 663 */         i = recordSet.getInt("total");
/* 664 */         i = (i == -1) ? 0 : i;
/*     */       } 
/* 666 */       arrayOfString[0] = GCONST.getContextPath() + "/spa/crm/static/index.html#/main/crm/customer/hrmView?searchHrmId=" + paramString1;
/* 667 */       arrayOfString[1] = "" + i;
/* 668 */     } catch (Exception exception) {}
/* 669 */     return arrayOfString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean checkCrmFileExist(String paramString1, String paramString2, String paramString3) {
/* 681 */     if ("1".equals(paramString3)) {
/* 682 */       RecordSet recordSet = new RecordSet();
/* 683 */       String str = "";
/* 684 */       if (recordSet.getDBType().equals("oracle")) {
/* 685 */         str = "select id from WORKPLAN where type_n = 3 and crmid = " + paramString1 + " and ','||nvl(docid,0)||',' like '%," + paramString2 + ",%'";
/* 686 */       } else if (recordSet.getDBType().equals("mysql")) {
/* 687 */         str = "select id from WORKPLAN where type_n = 3 and crmid = " + paramString1 + " and CONCAT(CONCAT(',',nvl(docid,0)),',') like '%," + paramString2 + ",%'";
/*     */       } else {
/* 689 */         str = "select id from WORKPLAN where type_n = 3 and crmid = " + paramString1 + " and ','+docid+',' like '%," + paramString2 + ",%'";
/*     */       } 
/* 691 */       recordSet.execute(str);
/* 692 */       if (recordSet.next()) {
/* 693 */         return true;
/*     */       }
/*     */     } else {
/*     */       
/* 697 */       return true;
/*     */     } 
/* 699 */     return false;
/*     */   }
/*     */ 
/*     */   
/*     */   public void doSomeThings(String paramString1, String paramString2, String paramString3, User paramUser) {
/* 704 */     (new BaseBean()).writeLog("doSomeThings=======newManager:" + paramString1 + " oldManager:" + paramString2 + " customerId:" + paramString3);
/* 705 */     SysRemindWorkflow sysRemindWorkflow = new SysRemindWorkflow();
/* 706 */     CustomerModifyLog customerModifyLog = new CustomerModifyLog();
/* 707 */     String str = DateUtil.getDate(new Date());
/*     */     
/*     */     try {
/* 710 */       (new RecordSet()).execute("update CRM_CustomerInfo set manager=" + paramString1 + ",seasFlag = 3,approvedate='" + str + "' where id= " + paramString3);
/* 711 */       if (!paramString1.equals(paramString2))
/*     */       {
/* 713 */         resetCustomerShare(paramString3);
/*     */       
/*     */       }
/*     */     }
/* 717 */     catch (Exception exception) {
/* 718 */       throw new ECException(getClass().getName() + "-" + SystemEnv.getHtmlLabelName(384324, paramUser.getLanguage()), exception);
/*     */     } 
/*     */     
/* 721 */     (new RecordSet()).execute("delete from CRM_SeasCustomer where customerid = " + paramString3);
/*     */ 
/*     */     
/* 724 */     customerModifyLog.modify(paramString3, paramString2, paramString1);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void resetCustomerShare(String paramString) throws Exception {
/* 732 */     if (!paramString.equals("")) {
/*     */       
/* 734 */       RecordSet recordSet1 = new RecordSet();
/* 735 */       String str1 = "";
/* 736 */       String str2 = "";
/*     */ 
/*     */       
/* 739 */       recordSet1.execute("delete from CRM_ShareInfo where relateditemid=" + paramString);
/* 740 */       recordSet1.execute("select manager,status from CRM_CustomerInfo where id=" + paramString);
/* 741 */       if (recordSet1.next()) {
/* 742 */         str1 = Util.null2String(recordSet1.getString("manager"));
/* 743 */         str2 = Util.null2String(recordSet1.getString("status"));
/* 744 */         recordSet1.execute("insert into CRM_ShareInfo (relateditemid,sharetype,seclevel,seclevelMax,sharelevel,crmid,isdefault,userid,contents) values (" + paramString + ",1,0,100,3,0,1," + str1 + "," + str1 + ")");
/*     */       } 
/*     */       
/* 747 */       String[] arrayOfString = Util.TokenizerString2(this.ResourceComInfo.getManagers(str1), ","); int i;
/* 748 */       for (i = 0; i < arrayOfString.length; i++) {
/* 749 */         String str = arrayOfString[i];
/* 750 */         recordSet1.execute("insert into CRM_ShareInfo (relateditemid,sharetype,seclevel,seclevelMax,sharelevel,crmid,isdefault,userid,contents) values (" + paramString + ",1,0,100,3,0,1," + str + "," + str + ")");
/*     */       } 
/*     */ 
/*     */ 
/*     */       
/* 755 */       i = Util.getIntValue(this.ResourceComInfo.getDepartmentID(str1), -1);
/* 756 */       int j = Util.getIntValue(this.ResourceComInfo.getSubCompanyID(str1), -1);
/* 757 */       recordSet1.execute("insert into CRM_ShareInfo (relateditemid,sharetype,seclevel,seclevelMax,rolelevel,sharelevel,roleid,crmid,isdefault,deptorcomid,contents) values (" + paramString + ",3,0,100,0,4,8,0,1," + i + ",8)");
/* 758 */       recordSet1.execute("insert into CRM_ShareInfo (relateditemid,sharetype,seclevel,seclevelMax,rolelevel,sharelevel,roleid,crmid,isdefault,deptorcomid,contents) values (" + paramString + ",3,0,100,1,4,8,0,1," + j + ",8)");
/* 759 */       recordSet1.execute("insert into CRM_ShareInfo (relateditemid,sharetype,seclevel,seclevelMax,rolelevel,sharelevel,roleid,crmid,isdefault,contents) values (" + paramString + ",3,0,100,2,4,8,0,1,8)");
/*     */       
/* 761 */       if (CustomerService.isPro)
/*     */       {
/* 763 */         if (str2.equals("4") || str2.equals("5") || str2.equals("6") || str2.equals("8")) {
/* 764 */           recordSet1.executeUpdate("insert into CRM_ShareInfo (relateditemid,sharetype,seclevel,seclevelMax,rolelevel,sharelevel,roleid,crmid,isdefault,deptorcomid,contents) values (" + paramString + ",3,0,100,0,4,171,0,1," + i + ",171)", new Object[0]);
/* 765 */           recordSet1.executeUpdate("insert into CRM_ShareInfo (relateditemid,sharetype,seclevel,seclevelMax,rolelevel,sharelevel,roleid,crmid,isdefault,deptorcomid,contents) values (" + paramString + ",3,0,100,1,4,171,0,1," + j + ",171)", new Object[0]);
/* 766 */           recordSet1.executeUpdate("insert into CRM_ShareInfo (relateditemid,sharetype,seclevel,seclevelMax,rolelevel,sharelevel,roleid,crmid,isdefault,contents) values (" + paramString + ",3,0,100,2,4,171,0,1,171)", new Object[0]);
/*     */         } 
/*     */       }
/*     */ 
/*     */       
/* 771 */       RecordSet recordSet2 = new RecordSet();
/* 772 */       recordSet1.execute("select * from CRM_T_ShareInfo where relateditemid=(select type from CRM_customerinfo where id=" + paramString + ")");
/* 773 */       while (recordSet1.next()) {
/* 774 */         int k = Util.getIntValue(recordSet1.getString("sharetype"), 0);
/* 775 */         int m = Util.getIntValue(recordSet1.getString("seclevel"), 0);
/* 776 */         int n = Util.getIntValue(recordSet1.getString("seclevelMax"), 100);
/* 777 */         int i1 = Util.getIntValue(recordSet1.getString("rolelevel"), 0);
/* 778 */         int i2 = Util.getIntValue(recordSet1.getString("sharelevel"), 0);
/* 779 */         int i3 = Util.getIntValue(recordSet1.getString("userid"), 0);
/* 780 */         int i4 = Util.getIntValue(recordSet1.getString("departmentid"), 0);
/* 781 */         int i5 = Util.getIntValue(recordSet1.getString("roleid"), 0);
/* 782 */         int i6 = Util.getIntValue(recordSet1.getString("foralluser"), 0);
/* 783 */         int i7 = Util.getIntValue(recordSet1.getString("subcompanyid"), 0);
/* 784 */         String str3 = Util.null2String(recordSet1.getString("jobtitleid"), "0");
/* 785 */         int i8 = Util.getIntValue(recordSet1.getString("joblevel"), 0);
/* 786 */         String str4 = Util.null2String(recordSet1.getString("scopeid"), "");
/* 787 */         int i9 = 1;
/* 788 */         int i10 = -1;
/* 789 */         if (k == 1) {
/* 790 */           i9 = i3;
/*     */         }
/* 792 */         else if (k == 2) {
/* 793 */           i9 = i4;
/*     */         }
/* 795 */         else if (k == 3) {
/* 796 */           if (i1 == 1) {
/* 797 */             i10 = j;
/* 798 */           } else if (i1 == 0) {
/* 799 */             i10 = i;
/*     */           } 
/* 801 */           i9 = i5;
/* 802 */         } else if (k == 4) {
/* 803 */           i9 = 1;
/* 804 */         } else if (k == 5) {
/* 805 */           i9 = i7;
/* 806 */         } else if (k == 6) {
/*     */         
/*     */         } 
/* 809 */         recordSet2.executeSql("insert into CRM_ShareInfo (relateditemid,sharetype,seclevel,seclevelMax,rolelevel,sharelevel,userid,departmentid,roleid,foralluser,crmid,contents,deptorcomid,subcompanyid,jobtitleid, joblevel, scopeid) values \t(" + paramString + "," + k + "," + m + "," + n + "," + i1 + "," + i2 + "," + i3 + "," + i4 + "," + i5 + "," + i6 + ",0," + i9 + "," + i10 + "," + i7 + ",'" + str3 + "'," + i8 + ",'" + str4 + "')");
/*     */       } 
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/crm/CrmShareBase.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */