/*     */ package weaver.crm.card;
/*     */ 
/*     */ import java.util.HashSet;
/*     */ import weaver.common.util.string.StringUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.province.ProvinceComInfo;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CardUtil
/*     */ {
/*     */   public static boolean isChinese(String paramString) {
/*  19 */     char[] arrayOfChar = paramString.toCharArray();
/*  20 */     for (byte b = 0; b < arrayOfChar.length; b++) {
/*  21 */       char c = arrayOfChar[b];
/*  22 */       if (isChinese(c)) {
/*  23 */         return true;
/*     */       }
/*     */     } 
/*  26 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static boolean isChinese(char paramChar) {
/*  36 */     Character.UnicodeBlock unicodeBlock = Character.UnicodeBlock.of(paramChar);
/*  37 */     if (unicodeBlock == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS || unicodeBlock == Character.UnicodeBlock.CJK_COMPATIBILITY_IDEOGRAPHS || unicodeBlock == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS_EXTENSION_A || unicodeBlock == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS_EXTENSION_B || unicodeBlock == Character.UnicodeBlock.CJK_SYMBOLS_AND_PUNCTUATION || unicodeBlock == Character.UnicodeBlock.HALFWIDTH_AND_FULLWIDTH_FORMS || unicodeBlock == Character.UnicodeBlock.GENERAL_PUNCTUATION)
/*     */     {
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/*  44 */       return true;
/*     */     }
/*  46 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean isJapanese(String paramString) {
/*  55 */     HashSet<Character.UnicodeBlock> hashSet = new HashSet<Character.UnicodeBlock>()
/*     */       {
/*     */       
/*     */       };
/*     */ 
/*     */     
/*  61 */     for (char c : paramString.toCharArray()) {
/*  62 */       if (hashSet.contains(Character.UnicodeBlock.of(c))) {
/*  63 */         return true;
/*     */       }
/*     */     } 
/*  66 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean isEnglish(String paramString) {
/*  75 */     return paramString.matches(".*[a-zA-Z].*+");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static void main(String[] paramArrayOfString) {
/*  88 */     String str1 = "";
/*  89 */     String str2 = "";
/*  90 */     String str3 = "isEnglish";
/*  91 */     if (StringUtil.isNullOrEmpty(str1) && StringUtil.isNotNullAndEmpty(str2)) {
/*  92 */       str1 = str2;
/*  93 */     } else if (StringUtil.isNullOrEmpty(str1) && StringUtil.isNullOrEmpty(str2) && StringUtil.isNotNullAndEmpty(str3)) {
/*  94 */       str1 = str3;
/*     */     } 
/*  96 */     System.out.println(str1);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getProvinceByCode(String paramString) {
/* 106 */     String str1 = "";
/* 107 */     String str2 = "";
/* 108 */     if ("AH".equals(paramString)) {
/* 109 */       str1 = SystemEnv.getHtmlLabelName(501960, Util.threadVarLanguage());
/* 110 */     } else if ("BJ".equals(paramString)) {
/* 111 */       str1 = SystemEnv.getHtmlLabelName(131471, Util.threadVarLanguage());
/* 112 */     } else if ("CQ".equals(paramString)) {
/* 113 */       str1 = SystemEnv.getHtmlLabelName(501958, Util.threadVarLanguage());
/* 114 */     } else if ("FJ".equals(paramString)) {
/* 115 */       str1 = SystemEnv.getHtmlLabelName(501963, Util.threadVarLanguage());
/* 116 */     } else if ("GD".equals(paramString)) {
/* 117 */       str1 = SystemEnv.getHtmlLabelName(501969, Util.threadVarLanguage());
/* 118 */     } else if ("GS".equals(paramString)) {
/* 119 */       str1 = SystemEnv.getHtmlLabelName(501946, Util.threadVarLanguage());
/* 120 */     } else if ("GX".equals(paramString)) {
/* 121 */       str1 = SystemEnv.getHtmlLabelName(501968, Util.threadVarLanguage());
/* 122 */     } else if ("GZ".equals(paramString)) {
/* 123 */       str1 = SystemEnv.getHtmlLabelName(501967, Util.threadVarLanguage());
/* 124 */     } else if ("HAINAN".equals(paramString)) {
/* 125 */       str1 = SystemEnv.getHtmlLabelName(501940, Util.threadVarLanguage());
/* 126 */     } else if ("HB".equals(paramString)) {
/* 127 */       str1 = SystemEnv.getHtmlLabelName(501953, Util.threadVarLanguage());
/* 128 */     } else if ("HLJ".equals(paramString)) {
/* 129 */       str1 = SystemEnv.getHtmlLabelName(501952, Util.threadVarLanguage());
/* 130 */     } else if ("HN".equals(paramString)) {
/* 131 */       str1 = SystemEnv.getHtmlLabelName(501955, Util.threadVarLanguage());
/* 132 */     } else if ("HUBEI".equals(paramString)) {
/* 133 */       str1 = SystemEnv.getHtmlLabelName(501959, Util.threadVarLanguage());
/* 134 */     } else if ("HUNAN".equals(paramString)) {
/* 135 */       str1 = SystemEnv.getHtmlLabelName(501966, Util.threadVarLanguage());
/* 136 */     } else if ("JL".equals(paramString)) {
/* 137 */       str1 = SystemEnv.getHtmlLabelName(501951, Util.threadVarLanguage());
/* 138 */     } else if ("JS".equals(paramString)) {
/* 139 */       str1 = SystemEnv.getHtmlLabelName(501961, Util.threadVarLanguage());
/* 140 */     } else if ("JX".equals(paramString)) {
/* 141 */       str1 = SystemEnv.getHtmlLabelName(501965, Util.threadVarLanguage());
/* 142 */     } else if ("LN".equals(paramString)) {
/* 143 */       str1 = SystemEnv.getHtmlLabelName(501950, Util.threadVarLanguage());
/* 144 */     } else if ("NMG".equals(paramString)) {
/* 145 */       str1 = SystemEnv.getHtmlLabelName(501947, Util.threadVarLanguage());
/* 146 */     } else if ("NX".equals(paramString)) {
/* 147 */       str1 = SystemEnv.getHtmlLabelName(501948, Util.threadVarLanguage());
/* 148 */     } else if ("QH".equals(paramString)) {
/* 149 */       str1 = SystemEnv.getHtmlLabelName(501945, Util.threadVarLanguage());
/* 150 */     } else if ("SC".equals(paramString)) {
/* 151 */       str1 = SystemEnv.getHtmlLabelName(501957, Util.threadVarLanguage());
/* 152 */     } else if ("SD".equals(paramString)) {
/* 153 */       str1 = SystemEnv.getHtmlLabelName(501954, Util.threadVarLanguage());
/* 154 */     } else if ("SH".equals(paramString)) {
/* 155 */       str1 = SystemEnv.getHtmlLabelName(20940, Util.threadVarLanguage());
/* 156 */     } else if ("SHANXI".equals(paramString)) {
/* 157 */       str1 = SystemEnv.getHtmlLabelName(501956, Util.threadVarLanguage());
/* 158 */     } else if ("SX".equals(paramString)) {
/* 159 */       str1 = SystemEnv.getHtmlLabelName(501949, Util.threadVarLanguage());
/* 160 */     } else if ("TJ".equals(paramString)) {
/* 161 */       str1 = SystemEnv.getHtmlLabelName(501942, Util.threadVarLanguage());
/* 162 */     } else if ("XJ".equals(paramString)) {
/* 163 */       str1 = SystemEnv.getHtmlLabelName(501943, Util.threadVarLanguage());
/* 164 */     } else if ("XZ".equals(paramString)) {
/* 165 */       str1 = SystemEnv.getHtmlLabelName(501944, Util.threadVarLanguage());
/* 166 */     } else if ("YN".equals(paramString)) {
/* 167 */       str1 = SystemEnv.getHtmlLabelName(501941, Util.threadVarLanguage());
/* 168 */     } else if ("ZJ".equals(paramString)) {
/* 169 */       str1 = SystemEnv.getHtmlLabelName(501962, Util.threadVarLanguage());
/*     */     } 
/*     */     
/*     */     try {
/* 173 */       ProvinceComInfo provinceComInfo = new ProvinceComInfo();
/* 174 */       while (provinceComInfo.next()) {
/* 175 */         if (provinceComInfo.getProvincename().equals(str1)) {
/* 176 */           str2 = provinceComInfo.getProvinceid();
/*     */         }
/*     */       } 
/* 179 */     } catch (Exception exception) {
/* 180 */       exception.printStackTrace();
/*     */     } 
/* 182 */     return str2;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/crm/card/CardUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */