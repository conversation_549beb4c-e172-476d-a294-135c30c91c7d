/*     */ package weaver.crm.report;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.List;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.crm.ContacterShareBase;
/*     */ import weaver.crm.Maint.ContractComInfo;
/*     */ import weaver.crm.Maint.ContractTypeComInfo;
/*     */ import weaver.crm.Maint.CustomerInfoComInfo;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.GCONST;
/*     */ import weaver.general.ThreadVarLanguage;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CRMContractTransMethod
/*     */   extends BaseBean
/*     */ {
/*  25 */   private RecordSet rs = new RecordSet();
/*  26 */   private ResourceComInfo rc = null;
/*  27 */   private CustomerInfoComInfo cci = null;
/*  28 */   private ContractTypeComInfo ctci = null;
/*  29 */   private ContractComInfo ci = null;
/*     */   public CRMContractTransMethod() {
/*     */     try {
/*  32 */       this.cci = new CustomerInfoComInfo();
/*  33 */       this.rc = new ResourceComInfo();
/*  34 */       this.ctci = new ContractTypeComInfo();
/*  35 */       this.ci = new ContractComInfo();
/*  36 */     } catch (Exception exception) {
/*  37 */       writeLog(exception);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getResourceName(String paramString) {
/*  46 */     return "<a href='" + GCONST.getContextPath() + "/hrm/resource/HrmResource.jsp?id=" + paramString + "' target='_blank'>" + this.rc.getLastname(paramString) + "</a>";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCRMName(String paramString) {
/*  56 */     return "<a href='" + GCONST.getContextPath() + "/CRM/data/ViewCustomer.jsp?log=n&CustomerID=" + paramString + "' target='_blank'>" + this.cci.getCustomerInfoname(paramString) + "</a>";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCRMNamecont(String paramString1, String paramString2) {
/*  66 */     ArrayList<String> arrayList = Util.TokenizerString(paramString2, "+");
/*  67 */     String str1 = arrayList.get(0);
/*  68 */     String str2 = arrayList.get(1);
/*  69 */     return "<a href='" + GCONST.getContextPath() + "/CRM/sellchance/ViewSellChance.jsp?id=" + paramString1 + "&customerid=" + str1 + "' target='_blank'>" + str2 + "</a>";
/*     */   }
/*     */ 
/*     */   
/*     */   public String getCRMSellSubject(String paramString1, String paramString2) {
/*  74 */     return "<a href='javascript:showInfo(" + paramString1 + ")'>" + paramString2 + "</a>";
/*     */   }
/*     */   
/*     */   public String getContactWay(String paramString1, String paramString2) {
/*  78 */     String str = "" + SystemEnv.getHtmlLabelName(375, ThreadVarLanguage.getLang()) + "";
/*  79 */     RecordSet recordSet = new RecordSet();
/*  80 */     recordSet.executeQuery("SELECT * FROM crm_customercontactway WHERE ID=?", new Object[] { paramString1 });
/*  81 */     if (recordSet.next()) {
/*  82 */       return recordSet.getString("FULLNAME");
/*     */     }
/*  84 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCRMSellStatus(String paramString) {
/*  94 */     String str1 = "";
/*  95 */     String str2 = "";
/*  96 */     if (!paramString.equals("")) {
/*  97 */       String str = "select * from CRM_SellStatus where id =" + paramString;
/*  98 */       this.rs.executeSql(str);
/*  99 */       if (this.rs.next()) {
/* 100 */         str2 = Util.null2String(this.rs.getString("fullname"));
/*     */       }
/*     */     } 
/* 103 */     return str2;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getStatus(String paramString1, String paramString2) {
/* 112 */     String str = "";
/* 113 */     if ("0".equals(paramString1)) {
/* 114 */       str = SystemEnv.getHtmlLabelName(615, Integer.parseInt(paramString2));
/* 115 */     } else if ("-1".equals(paramString1)) {
/* 116 */       str = SystemEnv.getHtmlLabelName(2242, Integer.parseInt(paramString2));
/* 117 */     } else if ("1".equals(paramString1)) {
/* 118 */       str = SystemEnv.getHtmlLabelName(1423, Integer.parseInt(paramString2));
/* 119 */     } else if ("2".equals(paramString1)) {
/* 120 */       str = SystemEnv.getHtmlLabelName(6095, Integer.parseInt(paramString2));
/* 121 */     } else if ("3".equals(paramString1)) {
/* 122 */       str = SystemEnv.getHtmlLabelName(555, Integer.parseInt(paramString2));
/*     */     } 
/* 124 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getPigeonholeStatus(String paramString1, String paramString2) {
/* 132 */     String str = "";
/* 133 */     if ("4".equals(paramString1)) {
/* 134 */       str = SystemEnv.getHtmlLabelName(235, Integer.parseInt(paramString2));
/* 135 */     } else if ("1".equals(paramString1)) {
/* 136 */       str = SystemEnv.getHtmlLabelName(15242, Integer.parseInt(paramString2));
/* 137 */     } else if ("2".equals(paramString1)) {
/* 138 */       str = SystemEnv.getHtmlLabelName(498, Integer.parseInt(paramString2));
/* 139 */     } else if ("0".equals(paramString1)) {
/* 140 */       str = SystemEnv.getHtmlLabelName(1960, Integer.parseInt(paramString2));
/*     */     } 
/* 142 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getTypeName(String paramString) {
/* 150 */     return this.ctci.getContractTypename(paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getContractName(String paramString1, String paramString2) {
/* 160 */     return "<a href='javascript:showInfo(" + paramString1 + ")'>" + this.ci.getContractname(paramString1) + "</a>";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getContactCheckInfo(String paramString) {
/* 170 */     String[] arrayOfString = paramString.split("\\+");
/*     */     
/* 172 */     if (arrayOfString[1].equals("0") && (arrayOfString[0].equals(arrayOfString[2]) || ("," + arrayOfString[3] + ",").indexOf("," + arrayOfString[2] + ",") != -1)) {
/* 173 */       return "true";
/*     */     }
/* 175 */     return "false";
/*     */   }
/*     */   
/*     */   public String getContactName(String paramString1, String paramString2) {
/* 179 */     String[] arrayOfString = paramString2.split("\\+");
/* 180 */     return "<a href='javascript:showInfo(" + arrayOfString[0] + "," + arrayOfString[1] + ")'>" + paramString1 + "</a>";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List getCusOpratePopedom(String paramString1, String paramString2) {
/* 191 */     ArrayList<String> arrayList = new ArrayList();
/* 192 */     String[] arrayOfString = paramString2.split("\\+");
/* 193 */     if (arrayOfString[1].equals("0")) {
/* 194 */       arrayList.add("true");
/*     */     } else {
/* 196 */       arrayList.add("false");
/*     */     } 
/*     */     
/* 199 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List getContractInfo(String paramString) {
/* 209 */     ArrayList<String> arrayList = new ArrayList();
/* 210 */     arrayList.add("true");
/* 211 */     return arrayList;
/*     */   }
/*     */ 
/*     */   
/*     */   public List getContractOpratePopedom(String paramString1, String paramString2) {
/* 216 */     ArrayList<String> arrayList = new ArrayList();
/*     */     
/* 218 */     String str1 = paramString2.substring(0, paramString2.indexOf("+"));
/* 219 */     String str2 = paramString2.substring(paramString2.indexOf("+") + 1);
/* 220 */     if (str2.equals("0")) {
/* 221 */       arrayList.add("true");
/* 222 */       arrayList.add("true");
/*     */     } else {
/* 224 */       arrayList.add("false");
/* 225 */       arrayList.add("false");
/*     */     } 
/*     */     
/* 228 */     if (str2.equals("1")) {
/* 229 */       arrayList.add("true");
/*     */     } else {
/* 231 */       arrayList.add("false");
/*     */     } 
/*     */     
/* 234 */     if (str2.equals("2")) {
/* 235 */       arrayList.add("true");
/*     */     } else {
/* 237 */       arrayList.add("false");
/*     */     } 
/*     */     
/* 240 */     arrayList.add("true");
/* 241 */     arrayList.add("true");
/* 242 */     arrayList.add("true");
/* 243 */     return arrayList;
/*     */   }
/*     */   
/*     */   public String getContractCheckInfo(String paramString) {
/* 247 */     String str1 = paramString;
/* 248 */     String str2 = "false";
/* 249 */     if (str1.equals("0")) {
/* 250 */       str2 = "true";
/*     */     } else {
/* 252 */       str2 = "false";
/*     */     } 
/*     */     
/* 255 */     return str2;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List getContractOpratePopedomCustomer(String paramString1, String paramString2) {
/* 264 */     String[] arrayOfString = paramString2.split("\\+");
/* 265 */     String str1 = arrayOfString[0];
/* 266 */     String str2 = arrayOfString[1];
/*     */     
/* 268 */     ContacterShareBase contacterShareBase = new ContacterShareBase();
/* 269 */     int i = contacterShareBase.getRightLevelForContacter(str2, paramString1);
/*     */     
/* 271 */     ArrayList<String> arrayList = new ArrayList();
/* 272 */     if (str1.equals("0") && i >= 2) {
/* 273 */       arrayList.add("true");
/* 274 */       arrayList.add("true");
/*     */     } else {
/* 276 */       arrayList.add("false");
/* 277 */       arrayList.add("false");
/*     */     } 
/*     */     
/* 280 */     if (str1.equals("1") && i >= 2) {
/* 281 */       arrayList.add("true");
/*     */     } else {
/* 283 */       arrayList.add("false");
/*     */     } 
/*     */     
/* 286 */     if (str1.equals("2") && i >= 2) {
/* 287 */       arrayList.add("true");
/*     */     } else {
/* 289 */       arrayList.add("false");
/*     */     } 
/*     */     
/* 292 */     arrayList.add("true");
/* 293 */     arrayList.add("true");
/* 294 */     if (i >= 2) {
/* 295 */       arrayList.add("true");
/*     */     } else {
/* 297 */       arrayList.add("false");
/*     */     } 
/* 299 */     if (str1.equals("0") && i >= 2) {
/* 300 */       arrayList.add("true");
/*     */     }
/* 302 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCrmFailfactorName(String paramString) {
/* 309 */     String str = "";
/* 310 */     if (Util.null2String(paramString).length() > 0) {
/* 311 */       RecordSet recordSet = new RecordSet();
/* 312 */       String str1 = "select * from CRM_Failfactor where id=" + paramString;
/* 313 */       recordSet.executeSql(str1);
/* 314 */       while (recordSet.next()) {
/* 315 */         str = recordSet.getString("fullname");
/*     */       }
/*     */     } 
/* 318 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCrmSuccessfactorName(String paramString) {
/* 325 */     String str = "";
/* 326 */     if (Util.null2String(paramString).length() > 0) {
/* 327 */       RecordSet recordSet = new RecordSet();
/* 328 */       String str1 = "select * from CRM_Successfactor where id=" + paramString;
/* 329 */       recordSet.executeSql(str1);
/* 330 */       while (recordSet.next()) {
/* 331 */         str = recordSet.getString("fullname");
/*     */       }
/*     */     } 
/* 334 */     return str;
/*     */   }
/*     */   
/*     */   public String getSellChanceName(String paramString1, String paramString2) {
/* 338 */     return "<div href='javascript:;' style='width:100%;height:30px;line-height:30px;cursor:pointer;' onclick='viewDetail(" + paramString2 + ",this)' ><a>" + paramString1 + "</a></div>";
/*     */   }
/*     */ 
/*     */   
/*     */   public String getImportant(String paramString1, String paramString2) {
/* 343 */     String str = "";
/* 344 */     if ("1".equals(paramString1)) {
/* 345 */       str = "important";
/*     */     } else {
/* 347 */       str = "important_no";
/* 348 */     }  return "<div style='float:center;' class='" + str + "' _important='" + paramString1 + "' _sellchanceid='" + paramString2 + "' onclick='markImportant(this)'></div>";
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/crm/report/CRMContractTransMethod.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */