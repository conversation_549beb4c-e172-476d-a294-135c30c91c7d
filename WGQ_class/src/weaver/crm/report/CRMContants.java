/*     */ package weaver.crm.report;
/*     */ 
/*     */ import java.text.DecimalFormat;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.LinkedHashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import java.util.Random;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.GCONST;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.city.CityComInfo;
/*     */ import weaver.hrm.province.ProvinceComInfo;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CRMContants
/*     */   extends BaseBean
/*     */ {
/*  26 */   private Map provs = new HashMap<>();
/*  27 */   private Map colors = new HashMap<>();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private ResourceComInfo rc;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private List colorList;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCode(String paramString) {
/* 108 */     return (String)this.provs.get(paramString);
/*     */   }
/*     */   
/*     */   public String getColor(String paramString) {
/* 112 */     return (String)this.colors.get(paramString);
/*     */   }
/*     */   
/* 115 */   public CRMContants() { this.rc = null;
/* 116 */     this.colorList = new ArrayList(); try {
/*     */       this.rc = new ResourceComInfo();
/*     */     } catch (Exception exception) {
/*     */       writeLog(exception);
/*     */     }  this.provs.put(SystemEnv.getHtmlLabelName(501938, 7), "HKG"); this.provs.put(SystemEnv.getHtmlLabelName(501939, 7), "MAC"); this.provs.put(SystemEnv.getHtmlLabelName(501940, 7), "HAI"); this.provs.put(SystemEnv.getHtmlLabelName(501941, 7), "YUN"); this.provs.put(SystemEnv.getHtmlLabelName(131471, 7), "BEJ"); this.provs.put(SystemEnv.getHtmlLabelName(501942, 7), "TAJ"); this.provs.put(SystemEnv.getHtmlLabelName(501943, 7), "XIN"); this.provs.put(SystemEnv.getHtmlLabelName(501944, 7), "TIB"); this.provs.put(SystemEnv.getHtmlLabelName(501946, 7), "QIH"); this.provs.put(SystemEnv.getHtmlLabelName(501946, 7), "GAN"); this.provs.put(SystemEnv.getHtmlLabelName(501947, 7), "NMG"); this.provs.put(SystemEnv.getHtmlLabelName(501948, 7), "NXA"); this.provs.put(SystemEnv.getHtmlLabelName(501949, 7), "SHX"); this.provs.put(SystemEnv.getHtmlLabelName(501950, 7), "LIA"); this.provs.put(SystemEnv.getHtmlLabelName(501951, 7), "JIL"); this.provs.put(SystemEnv.getHtmlLabelName(501952, 7), "HLJ"); this.provs.put(SystemEnv.getHtmlLabelName(501953, 7), "HEB"); this.provs.put(SystemEnv.getHtmlLabelName(501954, 7), "SHD"); this.provs.put(SystemEnv.getHtmlLabelName(501955, 7), "HEN"); this.provs.put(SystemEnv.getHtmlLabelName(501956, 7), "SHA"); this.provs.put(SystemEnv.getHtmlLabelName(501957, 7), "SCH"); this.provs.put(SystemEnv.getHtmlLabelName(501958, 7), "CHQ"); this.provs.put(SystemEnv.getHtmlLabelName(501959, 7), "HUB"); this.provs.put(SystemEnv.getHtmlLabelName(501960, 7), "ANH"); this.provs.put(SystemEnv.getHtmlLabelName(501961, 7), "JSU"); this.provs.put(SystemEnv.getHtmlLabelName(20940, 7), "SHH"); this.provs.put(SystemEnv.getHtmlLabelName(501962, 7), "ZHJ"); this.provs.put(SystemEnv.getHtmlLabelName(501963, 7), "FUJ"); this.provs.put(SystemEnv.getHtmlLabelName(501964, 7), "TAI"); this.provs.put(SystemEnv.getHtmlLabelName(501965, 7), "JXI"); this.provs.put(SystemEnv.getHtmlLabelName(501966, 7), "HUN"); this.provs.put(SystemEnv.getHtmlLabelName(501967, 7), "GUI"); this.provs.put(SystemEnv.getHtmlLabelName(501968, 7), "GXI"); this.provs.put(SystemEnv.getHtmlLabelName(501969, 7), "GUD"); this.colors.put(SystemEnv.getHtmlLabelName(501938, 7), "#AFD8F8"); this.colors.put(SystemEnv.getHtmlLabelName(501939, 7), "#F6BD0F"); this.colors.put(SystemEnv.getHtmlLabelName(501940, 7), "#8BBA00"); this.colors.put(SystemEnv.getHtmlLabelName(501941, 7), "#FF8E46"); this.colors.put(SystemEnv.getHtmlLabelName(131471, 7), "#008E8E"); this.colors.put(SystemEnv.getHtmlLabelName(501942, 7), "#D64646"); this.colors.put(SystemEnv.getHtmlLabelName(501943, 7), "#CCFFFF"); this.colors.put(SystemEnv.getHtmlLabelName(501944, 7), "#588526"); this.colors.put(SystemEnv.getHtmlLabelName(501945, 7), "#B3AA00"); this.colors.put(SystemEnv.getHtmlLabelName(501946, 7), "#008ED6"); this.colors.put(SystemEnv.getHtmlLabelName(501948, 7), "#9D080D"); this.colors.put(SystemEnv.getHtmlLabelName(501949, 7), "#A186BE"); this.colors.put(SystemEnv.getHtmlLabelName(501950, 7), "#66CC99"); this.colors.put(SystemEnv.getHtmlLabelName(501951, 7), "#66FF66"); this.colors.put(SystemEnv.getHtmlLabelName(501953, 7), "#990033"); this.colors.put(SystemEnv.getHtmlLabelName(501954, 7), "#9933FF"); this.colors.put(SystemEnv.getHtmlLabelName(501955, 7), "#999999"); this.colors.put(SystemEnv.getHtmlLabelName(501956, 7), "#99CC66"); this.colors.put(SystemEnv.getHtmlLabelName(501957, 7), "#CC0000"); this.colors.put(SystemEnv.getHtmlLabelName(501958, 7), "#CC00FF"); this.colors.put(SystemEnv.getHtmlLabelName(501939, 7), "#CC6699"); this.colors.put(SystemEnv.getHtmlLabelName(501960, 7), "#CC9966"); this.colors.put(SystemEnv.getHtmlLabelName(501961, 7), "#E25693"); this.colors.put(SystemEnv.getHtmlLabelName(20940, 7), "#8E468E"); this.colors.put(SystemEnv.getHtmlLabelName(501962, 7), "#FF3399"); this.colors.put(SystemEnv.getHtmlLabelName(501963, 7), "#FF9933"); this.colors.put(SystemEnv.getHtmlLabelName(501964, 7), "#48BEF4"); this.colors.put(SystemEnv.getHtmlLabelName(501965, 7), "#514E95");
/*     */     this.colors.put(SystemEnv.getHtmlLabelName(501966, 7), "#A8C656");
/*     */     this.colors.put(SystemEnv.getHtmlLabelName(501967, 7), "#19AA5F");
/*     */     this.colors.put(SystemEnv.getHtmlLabelName(501968, 7), "#E3563A");
/*     */     this.colors.put(SystemEnv.getHtmlLabelName(501969, 7), "#48BEF4");
/*     */     this.colors.put(SystemEnv.getHtmlLabelName(501947, 7), "#CCCC33");
/* 126 */     this.colors.put(SystemEnv.getHtmlLabelName(501952, 7), "#E9A840"); } public String getPerson(String paramString) { String str = "";
/* 127 */     if (paramString != null && !"".equals(paramString)) {
/* 128 */       ArrayList<String> arrayList = Util.TokenizerString(paramString, ",");
/* 129 */       for (byte b = 0; b < arrayList.size(); b++)
/*     */       {
/*     */ 
/*     */         
/* 133 */         str = str + "<a href='javaScript:openhrm(" + arrayList.get(b) + ");' onclick='pointerXY(event);'>" + this.rc.getResourcename(arrayList.get(b)) + "</a>&nbsp;";
/*     */       }
/*     */     } 
/*     */     
/* 137 */     return str; }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getDateTime(String paramString1, String paramString2) {
/* 148 */     return paramString1 + " " + paramString2;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getProvinceNames(String paramString) throws Exception {
/* 157 */     ProvinceComInfo provinceComInfo = new ProvinceComInfo();
/* 158 */     ArrayList<String> arrayList = Util.TokenizerString(paramString, ",");
/* 159 */     String str = "";
/* 160 */     for (byte b = 0; b < arrayList.size(); b++) {
/* 161 */       str = str + "," + provinceComInfo.getProvincename(arrayList.get(b));
/*     */     }
/* 163 */     if (!str.equals("")) {
/* 164 */       str = str.substring(1);
/*     */     }
/* 166 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCityNames(String paramString) throws Exception {
/* 175 */     CityComInfo cityComInfo = new CityComInfo();
/* 176 */     ArrayList<String> arrayList = Util.TokenizerString(paramString, ",");
/* 177 */     String str = "";
/* 178 */     for (byte b = 0; b < arrayList.size(); b++) {
/* 179 */       str = str + "," + cityComInfo.getCityname(arrayList.get(b));
/*     */     }
/* 181 */     if (!str.equals("")) {
/* 182 */       str = str.substring(1);
/*     */     }
/* 184 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getReportLink(String paramString1, String paramString2) {
/* 193 */     return "<a href=javascript:openOperateWindow('" + GCONST.getContextPath() + "/mapreport/data/ReportView.jsp?reportId=" + paramString1 + "')>" + paramString2 + "</a>";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getReportOperate(String paramString) {
/* 202 */     return getReportOperate(paramString, "7");
/*     */   }
/*     */   public String getReportOperate(String paramString1, String paramString2) {
/* 205 */     return "<a href=javascript:openOperateWindow('" + GCONST.getContextPath() + "/mapreport/data/ReportEdit.jsp?reportId=" + paramString1 + "')>" + SystemEnv.getHtmlLabelName(501169, Integer.parseInt(paramString2)) + "</a>&nbsp;&nbsp;<a href=javascript:openFullWindowHaveBar('" + 
/* 206 */       GCONST.getContextPath() + "/mapreport/data/ReportShow.jsp?reportId=" + paramString1 + "')>" + SystemEnv.getHtmlLabelName(221, Integer.parseInt(paramString2)) + "</a>";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String myRound(String paramString, int paramInt) {
/* 216 */     if (paramString == null || paramString.length() < 1) {
/* 217 */       return "";
/*     */     }
/* 219 */     DecimalFormat decimalFormat = null;
/* 220 */     double d = Double.parseDouble(paramString);
/* 221 */     if (paramInt == 0) {
/* 222 */       decimalFormat = new DecimalFormat("##0");
/*     */     } else {
/* 224 */       StringBuffer stringBuffer = new StringBuffer();
/* 225 */       stringBuffer.append("##0.");
/* 226 */       for (byte b = 0; b < paramInt; b++) {
/* 227 */         stringBuffer.append("0");
/*     */       }
/* 229 */       decimalFormat = new DecimalFormat(stringBuffer.toString());
/*     */     } 
/* 231 */     return decimalFormat.format(d);
/*     */   }
/*     */   public String formatValue(String paramString) {
/* 234 */     if (paramString.indexOf(".") > -1) {
/* 235 */       String str = paramString.substring(paramString.indexOf(".") + 1, paramString.length());
/* 236 */       int i = Integer.parseInt(str);
/* 237 */       if (i > 0) {
/* 238 */         return paramString;
/*     */       }
/* 240 */       return paramString.substring(0, paramString.indexOf("."));
/*     */     } 
/*     */     
/* 243 */     return paramString;
/*     */   }
/*     */   
/*     */   public Map sortMap(Map paramMap) {
/* 247 */     if (paramMap == null) {
/* 248 */       return paramMap;
/*     */     }
/* 250 */     if (paramMap.size() == 0) return paramMap; 
/* 251 */     LinkedHashMap<Object, Object> linkedHashMap = new LinkedHashMap<>();
/* 252 */     int[] arrayOfInt = new int[paramMap.size()];
/* 253 */     String[] arrayOfString = new String[paramMap.size()];
/* 254 */     byte b = 0;
/* 255 */     for (Map.Entry entry : paramMap.entrySet()) {
/* 256 */       String str = (String)((Map.Entry)entry).getKey();
/* 257 */       Object[] arrayOfObject = (Object[])entry.getValue();
/*     */       
/* 259 */       arrayOfString[b] = str;
/* 260 */       arrayOfInt[b] = Integer.parseInt((String)arrayOfObject[0]);
/* 261 */       b++;
/*     */     }  int i;
/* 263 */     for (i = 0; i < arrayOfInt.length; i++) {
/*     */       
/* 265 */       for (byte b1 = 0; b1 < arrayOfInt.length; b1++) {
/*     */ 
/*     */         
/* 268 */         String str = "";
/* 269 */         if (arrayOfInt[i] < arrayOfInt[b1]) {
/*     */           
/* 271 */           int j = arrayOfInt[b1];
/* 272 */           arrayOfInt[b1] = arrayOfInt[i];
/* 273 */           arrayOfInt[i] = j;
/*     */           
/* 275 */           str = arrayOfString[b1];
/* 276 */           arrayOfString[b1] = arrayOfString[i];
/* 277 */           arrayOfString[i] = str;
/*     */         } 
/*     */       } 
/*     */     } 
/* 281 */     for (i = arrayOfString.length - 1; i > -1; i--) {
/* 282 */       linkedHashMap.put(arrayOfString[i], paramMap.get(arrayOfString[i]));
/*     */     }
/*     */     
/* 285 */     return linkedHashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getRandColorCode() {
/* 294 */     Random random = new Random();
/* 295 */     String str1 = Integer.toHexString(random.nextInt(256)).toUpperCase();
/* 296 */     String str2 = Integer.toHexString(random.nextInt(256)).toUpperCase();
/* 297 */     String str3 = Integer.toHexString(random.nextInt(256)).toUpperCase();
/*     */     
/* 299 */     str1 = (str1.length() == 1) ? ("0" + str1) : str1;
/* 300 */     str2 = (str2.length() == 1) ? ("0" + str2) : str2;
/* 301 */     str3 = (str3.length() == 1) ? ("0" + str3) : str3;
/*     */ 
/*     */     
/* 304 */     if (this.colorList.indexOf("#" + str1 + str2 + str3) > -1 || (str1 + str2 + str3).equals("FFFFFF") || (str1 + str2 + str3).equals("000000")) {
/* 305 */       return getRandColorCode();
/*     */     }
/* 307 */     this.colorList.add("#" + str1 + str2 + str3);
/* 308 */     return "#" + str1 + str2 + str3;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/crm/report/CRMContants.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */