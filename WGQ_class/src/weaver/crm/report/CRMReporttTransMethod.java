/*     */ package weaver.crm.report;
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import com.engine.crm.constant.LogSmallTypeEnum;
/*     */ import com.engine.crm.constant.OperateTypeEnum;
/*     */ import java.util.ArrayList;
/*     */ import weaver.crm.Maint.AddressTypeComInfo;
/*     */ import weaver.crm.Maint.CreditInfoComInfo;
/*     */ import weaver.crm.Maint.CustomerContacterComInfo;
/*     */ import weaver.crm.Maint.CustomerInfoComInfo;
/*     */ import weaver.crm.Maint.CustomerStatusComInfo;
/*     */ import weaver.crm.Maint.CustomerTypeComInfo;
/*     */ import weaver.general.GCONST;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ public class CRMReporttTransMethod {
/*     */   public String getCustomerTypeName(String paramString) throws Exception {
/*  19 */     CustomerTypeComInfo customerTypeComInfo = new CustomerTypeComInfo();
/*  20 */     return customerTypeComInfo.getCustomerTypename(paramString);
/*     */   }
/*     */   
/*     */   public String getCustomerStatusName(String paramString) throws Exception {
/*  24 */     CustomerStatusComInfo customerStatusComInfo = new CustomerStatusComInfo();
/*  25 */     return customerStatusComInfo.getCustomerStatusname(paramString);
/*     */   }
/*     */   
/*     */   public String getCustomerDescName(String paramString) throws Exception {
/*  29 */     CustomerDescComInfo customerDescComInfo = new CustomerDescComInfo();
/*  30 */     return customerDescComInfo.getCustomerDescname(paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCustomerInfo(String paramString1, String paramString2) throws Exception {
/*  36 */     if (paramString1.equals("0")) {
/*  37 */       return "0";
/*     */     }
/*  39 */     String str1 = paramString2.substring(0, paramString2.indexOf("+"));
/*  40 */     String str2 = paramString2.substring(paramString2.indexOf("+") + 1);
/*  41 */     return "<a href='javascript:void()' onclick='showDetailInfo(\"" + str1 + "\"," + str2 + ")'>" + paramString1 + "</a>";
/*     */   }
/*     */   
/*     */   public String getSellChanceActiveName(String paramString) {
/*  45 */     if ("0".equals(paramString)) {
/*  46 */       return SystemEnv.getHtmlLabelName(1960, 7);
/*     */     }
/*  48 */     if ("1".equals(paramString)) {
/*  49 */       return SystemEnv.getHtmlLabelName(15242, 7);
/*     */     }
/*  51 */     if ("2".equals(paramString)) {
/*  52 */       return SystemEnv.getHtmlLabelName(498, 7);
/*     */     }
/*     */     
/*  55 */     return "";
/*     */   }
/*     */   
/*     */   public String getSellChanceInfo(String paramString1, String paramString2) throws Exception {
/*  59 */     if (paramString1.equals("0")) {
/*  60 */       return "0";
/*     */     }
/*  62 */     return "<a href='#' onclick='showDetailInfo(\"" + paramString2 + "\")'>" + paramString1 + "</a>";
/*     */   }
/*     */   
/*     */   public List getCrmModifyOpratePopedom(String paramString) {
/*  66 */     ArrayList<String> arrayList = new ArrayList();
/*  67 */     arrayList.add("true");
/*  68 */     return arrayList;
/*     */   }
/*     */ 
/*     */   
/*     */   public String getCrmModifyTime(String paramString1, String paramString2) {
/*  73 */     return paramString1 + "&nbsp&nbsp" + paramString2;
/*     */   }
/*     */   
/*     */   public String getLogType(String paramString1, String paramString2) {
/*  77 */     String[] arrayOfString = Util.splitString(paramString2, "+");
/*  78 */     int i = Integer.parseInt(arrayOfString[0]);
/*  79 */     int j = Integer.parseInt(arrayOfString[1]);
/*  80 */     String str = "";
/*     */ 
/*     */     
/*  83 */     for (OperateTypeEnum operateTypeEnum : OperateTypeEnum.values()) {
/*  84 */       if (Integer.parseInt(paramString1) == operateTypeEnum.getOperateType()) {
/*  85 */         str = str + SystemEnv.getHtmlLabelName(operateTypeEnum.getLabelId(), j);
/*     */       }
/*     */     } 
/*  88 */     for (LogSmallTypeEnum logSmallTypeEnum : LogSmallTypeEnum.values()) {
/*  89 */       if (i == logSmallTypeEnum.getLogSmallType()) {
/*  90 */         str = str + "(" + SystemEnv.getHtmlLabelName(logSmallTypeEnum.getLabelId(), j) + ")";
/*     */       }
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 126 */     return str;
/*     */   }
/*     */   
/*     */   public String getLogType(String paramString) {
/* 130 */     return getLogType(paramString, "7");
/*     */   }
/*     */   
/*     */   public String getSubmiterInfo(String paramString1, String paramString2) throws Exception {
/* 134 */     String str1 = paramString2.substring(0, paramString2.indexOf("+"));
/* 135 */     String str2 = paramString2.substring(paramString2.indexOf("+") + 1);
/* 136 */     if (!"2".equals(str2)) {
/*     */       
/* 138 */       if (!str1.equals("2")) {
/* 139 */         ResourceComInfo resourceComInfo = new ResourceComInfo();
/* 140 */         return "<a href='" + GCONST.getContextPath() + "/hrm/resource/HrmResource.jsp?id=" + paramString1 + "' target='_blank'>" + resourceComInfo.getResourcename(paramString1) + "</a>";
/*     */       } 
/* 142 */       CustomerInfoComInfo customerInfoComInfo1 = new CustomerInfoComInfo();
/* 143 */       return "<a href='" + GCONST.getContextPath() + "/CRM/data/ViewCustomer.jsp?CustomerID=" + paramString1 + "' target='_blank'>" + customerInfoComInfo1.getCustomerInfoname(paramString1) + "</a>";
/*     */     } 
/*     */ 
/*     */     
/* 147 */     if (!str1.equals("2")) {
/* 148 */       ResourceComInfo resourceComInfo = new ResourceComInfo();
/* 149 */       return resourceComInfo.getResourcename(paramString1);
/*     */     } 
/* 151 */     CustomerInfoComInfo customerInfoComInfo = new CustomerInfoComInfo();
/* 152 */     return "<a href='" + GCONST.getContextPath() + "/CRM/data/ViewCustomer.jsp?CustomerID=" + paramString1 + "' target='_blank'>" + customerInfoComInfo.getCustomerInfoname(paramString1) + "</a>";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getManagerInfo(String paramString1, String paramString2) throws Exception {
/* 159 */     ResourceComInfo resourceComInfo = new ResourceComInfo();
/* 160 */     if (!"2".equals(paramString2)) {
/* 161 */       return "<a href='" + GCONST.getContextPath() + "/hrm/resource/HrmResource.jsp?id=" + paramString1 + "' target='_blank'>" + resourceComInfo.getResourcename(paramString1) + "</a>";
/*     */     }
/* 163 */     return resourceComInfo.getResourcename(paramString1);
/*     */   }
/*     */ 
/*     */   
/*     */   public String getViewModifyNewDesc(String paramString1, String paramString2) throws Exception {
/* 168 */     StringBuilder stringBuilder = new StringBuilder(" ");
/*     */     
/* 170 */     JSONObject jSONObject1 = JSON.parseObject(paramString2);
/* 171 */     JSONObject jSONObject2 = JSON.parseObject(paramString1);
/*     */     
/* 173 */     if (jSONObject2 != null && jSONObject1 != null) {
/* 174 */       jSONObject1.forEach((paramString, paramObject) -> paramJSONObject.forEach(()));
/*     */     }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 189 */     if (jSONObject2 != null && jSONObject1 == null) {
/* 190 */       jSONObject2.forEach((paramString, paramObject) -> {
/*     */             paramStringBuilder.append(paramString);
/*     */             
/*     */             paramStringBuilder.append(":");
/*     */             
/*     */             paramStringBuilder.append(paramObject);
/*     */             
/*     */             paramStringBuilder.append("<br/>");
/*     */           });
/*     */     }
/* 200 */     return stringBuilder.toString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getViewModifyDesc(String paramString1, String paramString2) throws Exception {
/* 207 */     String str1 = paramString2.substring(0, paramString2.indexOf("+"));
/* 208 */     String str2 = paramString2.substring(paramString2.indexOf("+") + 1);
/* 209 */     if (paramString1.equals("1")) {
/* 210 */       return SystemEnv.getHtmlLabelName(20323, 7);
/*     */     }
/* 212 */     if (paramString1.equals("2")) {
/* 213 */       CustomerContacterComInfo customerContacterComInfo = new CustomerContacterComInfo();
/* 214 */       return SystemEnv.getHtmlLabelName(572, 7) + "(<a href='" + GCONST.getContextPath() + "/CRM/data/ViewContacter.jsp?ContacterID=" + str1 + "'>" + customerContacterComInfo.getCustomerContactername(str1) + "</a>)";
/*     */     } 
/*     */ 
/*     */     
/* 218 */     if (paramString1.equals("3")) {
/* 219 */       AddressTypeComInfo addressTypeComInfo = new AddressTypeComInfo();
/* 220 */       return SystemEnv.getHtmlLabelName(110, 7) + "<a href='" + GCONST.getContextPath() + "/CRM/data/ViewAddressDetail.jsp?CustomerID=" + str2 + "&TypeID=" + str1 + "'>" + addressTypeComInfo.getAddressTypename(str1) + "</a>)";
/*     */     } 
/*     */ 
/*     */     
/* 224 */     return null;
/*     */   }
/*     */   
/*     */   public String getEvaluationValue(String paramString) {
/* 228 */     double d = Util.getDoubleValue(paramString) / 100.0D;
/* 229 */     return String.format("%.2f", new Object[] { Double.valueOf(d) });
/*     */   }
/*     */   
/*     */   public String getContractName(String paramString1, String paramString2) throws Exception {
/* 233 */     String str1 = paramString2.substring(0, paramString2.indexOf("+"));
/* 234 */     String str2 = paramString2.substring(paramString2.indexOf("+") + 1, paramString2.lastIndexOf("+"));
/* 235 */     String str3 = paramString2.substring(paramString2.lastIndexOf("+") + 1);
/* 236 */     if (str1.trim().equals("") || str2.trim().equals("")) {
/* 237 */       ResourceComInfo resourceComInfo = new ResourceComInfo();
/* 238 */       return "<a href='" + GCONST.getContextPath() + "/hrm/resource/HrmResource.jsp?id=" + str3 + "'><label style='font-weight::bolder !important'>" + resourceComInfo.getResourcename(str3) + "</label></a>";
/*     */     } 
/* 240 */     return "<a href='" + GCONST.getContextPath() + "/CRM/data/ContractView.jsp?CustomerID=" + str1 + "&id=" + str2 + "' target='blank'>" + paramString1 + "</a>";
/*     */   }
/*     */ 
/*     */   
/*     */   public String getContractUnreceive(String paramString1, String paramString2) {
/* 245 */     return String.valueOf(Util.getFloatValue(paramString1) - Util.getFloatValue(paramString2));
/*     */   }
/*     */   
/*     */   public String getReportPrice(String paramString1, String paramString2) {
/* 249 */     String str1 = paramString2.substring(0, paramString2.indexOf("+"));
/* 250 */     String str2 = paramString2.substring(paramString2.indexOf("+") + 1);
/*     */     
/* 252 */     if (str1.equals("first")) {
/* 253 */       if ("1".equals(str2)) {
/* 254 */         return paramString1;
/*     */       }
/* 256 */       return "-";
/*     */     } 
/*     */ 
/*     */     
/* 260 */     if (str1.equals("second")) {
/* 261 */       if ("2".equals(str2)) {
/* 262 */         return paramString1;
/*     */       }
/* 264 */       return "-";
/*     */     } 
/*     */ 
/*     */     
/* 268 */     return null;
/*     */   }
/*     */ 
/*     */   
/*     */   public String getSellAreaResult(String paramString1, String paramString2) {
/* 273 */     if (paramString1.equals("0")) {
/* 274 */       return "0";
/*     */     }
/* 276 */     return "<a href='#' onclick='showDetailInfo(\"" + paramString2 + "\")'>" + paramString1 + "</a>";
/*     */   }
/*     */   
/*     */   public String getCreditInfoName(String paramString1, String paramString2) throws Exception {
/* 280 */     CreditInfoComInfo creditInfoComInfo = new CreditInfoComInfo();
/* 281 */     return Util.toScreen(creditInfoComInfo.getCreditInfoname(paramString1), Util.getIntValue(paramString2)) + ":" + 
/* 282 */       Util.toScreen(creditInfoComInfo.getCreditInfodesc(paramString1), Util.getIntValue(paramString2)) + "--" + 
/* 283 */       Util.toScreen(creditInfoComInfo.getCreditInfohighamount(paramString1), Util.getIntValue(paramString2));
/*     */   }
/*     */ 
/*     */   
/*     */   public String getCreditInfoResult(String paramString1, String paramString2) {
/* 288 */     if (paramString1.equals("0")) {
/* 289 */       return "0";
/*     */     }
/* 291 */     return "<a href='#' onclick='showDetailInfo(\"" + paramString2 + "\")'>" + paramString1 + "</a>";
/*     */   }
/*     */   
/*     */   public String getTradeInfoName(String paramString1, String paramString2) throws Exception {
/* 295 */     String str1 = paramString2.substring(0, paramString2.indexOf("+"));
/* 296 */     String str2 = paramString2.substring(paramString2.indexOf("+") + 1, paramString2.lastIndexOf("+"));
/* 297 */     String str3 = Util.null2String(paramString2.substring(paramString2.lastIndexOf("+") + 1));
/* 298 */     if (!"".equals(str3) && 
/* 299 */       Double.valueOf(str3).doubleValue() < 0.0D) {
/* 300 */       str3 = "";
/*     */     }
/*     */     
/* 303 */     return str1 + ":" + str2 + "&nbsp;&nbsp;--&nbsp;&nbsp;" + str3;
/*     */   }
/*     */   
/*     */   public String getTradeInfoResult(String paramString1, String paramString2) {
/* 307 */     if (paramString1.equals("0")) {
/* 308 */       return "0";
/*     */     }
/* 310 */     return paramString1;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/crm/report/CRMReporttTransMethod.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */