/*     */ package weaver.crm.sellchance;
/*     */ 
/*     */ import com.engine.crm.util.SellChanceShareUtil;
/*     */ import java.util.ArrayList;
/*     */ import java.util.List;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.crm.CrmShareBase;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class SellChangeRoprtTransMethod
/*     */   extends BaseBean
/*     */ {
/*     */   public String getSellCheckInfo(String paramString) {
/*  21 */     String str = "true";
/*     */     try {
/*  23 */       String[] arrayOfString = paramString.split("\\+");
/*     */       
/*  25 */       if (!arrayOfString[0].equals("0")) {
/*  26 */         return "false";
/*     */       }
/*  28 */       User user = User.getUser(Util.getIntValue(arrayOfString[2]), 1);
/*     */       
/*  30 */       SellChanceShareUtil sellChanceShareUtil = new SellChanceShareUtil(user);
/*  31 */       int i = sellChanceShareUtil.getSellChanceShareLevel(Util.getIntValue(arrayOfString[3]));
/*     */ 
/*     */       
/*  34 */       if (i < 2) {
/*  35 */         return "false";
/*     */       }
/*     */       
/*  38 */       RecordSet recordSet = new RecordSet();
/*  39 */       recordSet.executeProc("CRM_CustomerInfo_SelectByID", arrayOfString[1]);
/*  40 */       recordSet.next();
/*     */ 
/*     */       
/*  43 */       if (recordSet.getInt("status") == 7 || recordSet.getInt("status") == 8) {
/*  44 */         return "false";
/*     */       
/*     */       }
/*     */     
/*     */     }
/*  49 */     catch (Exception exception) {
/*  50 */       exception.printStackTrace();
/*  51 */       writeLog(exception);
/*  52 */       return "false";
/*     */     } 
/*     */     
/*  55 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List getSellPopedom(String paramString1, String paramString2) {
/*  63 */     ArrayList<String> arrayList = new ArrayList();
/*     */     try {
/*  65 */       String[] arrayOfString = paramString2.split("\\+");
/*  66 */       boolean bool = true;
/*  67 */       if (!arrayOfString[0].equals("0")) {
/*  68 */         bool = false;
/*     */       }
/*     */       
/*  71 */       CrmShareBase crmShareBase = new CrmShareBase();
/*  72 */       int i = crmShareBase.getRightLevelForCRM("" + arrayOfString[2], arrayOfString[1]);
/*  73 */       if (i < 2) {
/*  74 */         bool = false;
/*     */       }
/*     */       
/*  77 */       RecordSet recordSet = new RecordSet();
/*  78 */       recordSet.executeProc("CRM_CustomerInfo_SelectByID", arrayOfString[1]);
/*  79 */       recordSet.next();
/*     */ 
/*     */       
/*  82 */       if (recordSet.getInt("status") == 7 || recordSet.getInt("status") == 8) {
/*  83 */         bool = false;
/*     */       }
/*     */       
/*  86 */       if (bool) {
/*  87 */         arrayList.add("true");
/*  88 */         arrayList.add("true");
/*  89 */         arrayList.add("true");
/*  90 */         arrayList.add("true");
/*     */       } else {
/*  92 */         arrayList.add("true");
/*  93 */         arrayList.add("false");
/*  94 */         arrayList.add("false");
/*  95 */         arrayList.add("true");
/*     */       } 
/*  97 */     } catch (Exception exception) {
/*  98 */       exception.printStackTrace();
/*  99 */       writeLog(exception);
/*     */     } 
/* 101 */     return arrayList;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/crm/sellchance/SellChangeRoprtTransMethod.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */