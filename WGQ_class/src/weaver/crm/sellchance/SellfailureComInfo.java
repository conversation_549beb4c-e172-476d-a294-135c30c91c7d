/*    */ package weaver.crm.sellchance;
/*    */ 
/*    */ import weaver.cache.CacheBase;
/*    */ import weaver.cache.CacheColumn;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class SellfailureComInfo
/*    */   extends CacheBase
/*    */ {
/*    */   public static String table;
/* 14 */   protected static String TABLE_NAME = "CRM_Failfactor";
/* 15 */   protected static String PK_NAME = "id";
/*    */   
/*    */   @CacheColumn
/*    */   protected static int id;
/*    */   @CacheColumn
/*    */   protected static int fullname;
/*    */   @CacheColumn
/*    */   protected static int description;
/*    */   
/*    */   public int getFaStatusNum() {
/* 25 */     return size();
/*    */   }
/*    */   
/*    */   public String getFaStatusid() {
/* 29 */     return (String)getRowValue(id);
/*    */   }
/*    */   
/*    */   public String getFaStatusname() {
/* 33 */     return (String)getRowValue(fullname);
/*    */   }
/*    */   
/*    */   public String getFaStatusname(String paramString) {
/* 37 */     return (String)getValue(fullname, paramString);
/*    */   }
/*    */   
/*    */   public String getDescription() {
/* 41 */     return (String)getRowValue(description);
/*    */   }
/*    */   
/*    */   public String getDescription(String paramString) {
/* 45 */     return (String)getValue(description, paramString);
/*    */   }
/*    */   public void removeFailureCache() {
/* 48 */     removeCache();
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/crm/sellchance/SellfailureComInfo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */