/*     */ package weaver.crm.sellchance;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.List;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.file.FileUpload;
/*     */ import weaver.general.TimeUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class SellChanceLabelService
/*     */ {
/*     */   public boolean addItemLabels(String paramString1, String paramString2, String paramString3) {
/*  23 */     boolean bool = false;
/*     */     
/*  25 */     RecordSet recordSet = new RecordSet();
/*     */     
/*  27 */     if (!"".equals(paramString3)) {
/*  28 */       bool = recordSet.execute("insert into CRM_Sellchance_label (sellchanceid,userid,labelid) select " + paramString1 + "," + paramString2 + " , id FROM CRM_SellchanceLabel WHERE id IN (" + paramString3 + ") AND id NOT IN " + "(SELECT labelid FROM CRM_Sellchance_label WHERE sellchanceid = " + paramString1 + ")");
/*     */     
/*     */     }
/*     */     else {
/*     */       
/*  33 */       bool = recordSet.execute("delete from CRM_Sellchance_label where sellchanceid=" + paramString1);
/*     */     } 
/*     */     
/*  36 */     return bool;
/*     */   }
/*     */   
/*     */   public boolean cancelItemLabels(String paramString1, String paramString2, String paramString3) {
/*  40 */     boolean bool = false;
/*     */     
/*  42 */     RecordSet recordSet = new RecordSet();
/*     */     
/*  44 */     if (!"".equals(paramString3)) {
/*  45 */       bool = recordSet.execute("delete from CRM_Sellchance_label where sellchanceid = '" + paramString1 + "' " + " and userid = '" + paramString2 + "' and labelid in (" + paramString3 + ")");
/*     */     }
/*     */     
/*  48 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean createLabel(String paramString, FileUpload paramFileUpload) {
/*  58 */     boolean bool = true;
/*  59 */     RecordSet recordSet = new RecordSet();
/*     */     
/*  61 */     String str1 = paramFileUpload.getParameter("id");
/*  62 */     String str2 = paramFileUpload.getParameter("name");
/*  63 */     String str3 = paramFileUpload.getParameter("labelColor");
/*  64 */     String str4 = paramFileUpload.getParameter("textColor");
/*  65 */     String str5 = paramFileUpload.getParameter("isUsed_" + str1);
/*  66 */     str5 = str5.equals("1") ? "1" : "0";
/*  67 */     int i = 0;
/*     */     
/*  69 */     String str6 = "select max(labelOrder) as maxOrder from CRM_SellchanceLabel where userid=" + paramString;
/*  70 */     recordSet.execute(str6);
/*  71 */     if (recordSet.next()) {
/*  72 */       i = recordSet.getInt("maxOrder") + 1;
/*     */     }
/*  74 */     bool = recordSet.execute("insert into CRM_SellchanceLabel (userid,name,labelColor,createdate,createtime,isUsed,labelOrder,textColor,labelType) values(" + paramString + ",'" + str2 + "','" + str3 + "','" + TimeUtil.getCurrentDateString() + "','" + TimeUtil.getOnlyCurrentTimeString() + "'," + str5 + "," + i + ",'" + str4 + "','label')");
/*  75 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean isExistLabel(String paramString1, String paramString2) {
/*  85 */     boolean bool = false;
/*     */     
/*  87 */     RecordSet recordSet = new RecordSet();
/*  88 */     recordSet.execute("select id from CRM_SellchanceLabel where userid='" + paramString1 + "' and name='" + paramString2 + "'");
/*  89 */     if (recordSet.next()) {
/*  90 */       bool = true;
/*     */     } else {
/*  92 */       bool = false;
/*     */     } 
/*  94 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean updateLabel(FileUpload paramFileUpload) {
/* 106 */     boolean bool = true;
/* 107 */     RecordSet recordSet = new RecordSet();
/* 108 */     String[] arrayOfString1 = paramFileUpload.getParameters("id");
/* 109 */     String[] arrayOfString2 = paramFileUpload.getParameters("labelType");
/* 110 */     String[] arrayOfString3 = paramFileUpload.getParameters("labelColor");
/* 111 */     String[] arrayOfString4 = paramFileUpload.getParameters("textColor");
/*     */     
/* 113 */     String[] arrayOfString5 = new String[arrayOfString1.length];
/* 114 */     for (byte b1 = 0; b1 < arrayOfString1.length; b1++) {
/* 115 */       String str = paramFileUpload.getParameter("isUsed_" + arrayOfString1[b1]);
/* 116 */       str = str.equals("1") ? "1" : "0";
/* 117 */       arrayOfString5[b1] = str;
/*     */     } 
/* 119 */     String[] arrayOfString6 = paramFileUpload.getParameters("name");
/*     */     
/* 121 */     String[] arrayOfString7 = paramFileUpload.getParameters("labelOrder");
/* 122 */     for (byte b2 = 0; b2 < arrayOfString1.length; b2++) {
/* 123 */       String str1 = Util.null2String(arrayOfString1[b2]);
/* 124 */       String str2 = Util.null2String(arrayOfString6[b2]);
/* 125 */       String str3 = Util.null2String(arrayOfString5[b2]);
/* 126 */       str3 = str3.equals("1") ? "1" : "0";
/* 127 */       String str4 = Util.null2String(arrayOfString7[b2]);
/* 128 */       String str5 = Util.null2String(arrayOfString3[b2]);
/* 129 */       String str6 = Util.null2String(arrayOfString4[b2]);
/*     */       
/* 131 */       bool = recordSet.execute("update CRM_SellchanceLabel set name='" + str2 + "',isUsed=" + str3 + ",labelOrder=" + str4 + ",labelColor='" + str5 + "',textColor='" + str6 + "' where id=" + str1);
/*     */     } 
/* 133 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean deleteLabel(String paramString) {
/* 144 */     boolean bool = true;
/*     */     
/* 146 */     RecordSet recordSet = new RecordSet();
/*     */     
/* 148 */     bool = recordSet.execute("delete from CRM_SellchanceLabel where id='" + paramString + "'");
/* 149 */     if (bool) {
/* 150 */       bool = recordSet.execute("delete from cowork_item_label where labelid='" + paramString + "'");
/*     */     }
/* 152 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean deleteItemLabel(String paramString) {
/* 161 */     boolean bool = true;
/* 162 */     RecordSet recordSet = new RecordSet();
/* 163 */     bool = recordSet.execute("delete from cowork_item_label where id='" + paramString + "'");
/* 164 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getUserLabels(String paramString, int paramInt) {
/* 174 */     String str = "";
/* 175 */     RecordSet recordSet = new RecordSet();
/*     */     
/* 177 */     recordSet.execute("select id,name,labelColor,textColor from CRM_SellchanceLabel where userid=" + paramString + " and labelType='label' order by labelOrder ");
/* 178 */     while (recordSet.next()) {
/* 179 */       str = str + "<div class='row' onmouseover=\"this.className='rowOver'\" onmouseout=\"this.className='row'\"><div class='check' labelid='" + recordSet.getString("id") + "' labelColor='" + recordSet.getString("labelColor") + "' textColor='" + recordSet.getString("textColor") + "'></div><div class='title' onclick='jQuery(this).prev().click()' labelid=\"" + recordSet.getString("id") + "\" labelColor='" + recordSet.getString("labelColor") + "' textColor='" + recordSet.getString("textColor") + "'>" + recordSet.getString("name") + "</div>" + "</div>";
/*     */     }
/*     */ 
/*     */     
/* 183 */     str = str + "<div class=\"row\" style='position:relative'><div id='apply' action=\"applyLabels(this)\" class='operate' onmouseover=\"this.className='operateOver'\" onmouseout=\"this.className='operate'\"  style='float:left;margin-left:20px;'>" + SystemEnv.getHtmlLabelName(25432, paramInt) + "</div>" + "<div id='manage' action=\"openLabelSet()\" class='operate' onmouseover=\"this.className='operateOver'\" onmouseout=\"this.className='operate'\" style='float:right;margin-right:20px;'>" + SystemEnv.getHtmlLabelName(22250, paramInt) + "</div>" + "</div>";
/*     */ 
/*     */ 
/*     */     
/* 187 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getUserLabelsForManage(String paramString, int paramInt) {
/* 198 */     String str = "";
/* 199 */     RecordSet recordSet = new RecordSet();
/*     */     
/* 201 */     recordSet.execute("select id,name,icon from CRM_SellchanceLabel where userid='" + paramString + "'");
/* 202 */     str = str + "<table width=250px>";
/* 203 */     str = str + "<tr><td width=200> <span id='create' class='createLabel'>" + SystemEnv.getHtmlLabelName(25431, paramInt) + "</span></td>" + "<td></td>" + "</tr>";
/*     */ 
/*     */ 
/*     */     
/* 207 */     while (recordSet.next()) {
/* 208 */       str = str + "<tr><td width=200> <span class='editLabel'>" + recordSet.getString("name") + "</span></td>" + "<td><div class='check'></div><a href='#' onclick='deleteLabel(this)' >" + SystemEnv.getHtmlLabelName(91, paramInt) + "</a></td>" + "</tr>";
/*     */     }
/*     */ 
/*     */ 
/*     */     
/* 213 */     str = str + "</table>";
/* 214 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getUserLabelsForTab(String paramString) {
/* 223 */     String str = "";
/* 224 */     RecordSet recordSet = new RecordSet();
/* 225 */     recordSet.execute("select id,name,icon from CRM_SellchanceLabel where userid='" + paramString + "'");
/* 226 */     while (recordSet.next()) {
/* 227 */       str = str + "<div class='row' onmouseover=\"this.className='rowOver'\" onmouseout=\"this.className='row'\"><div class='title' action=\"loadCoworkItemListByLabel(this)\" labelid=\"" + recordSet.getString("id") + "\">" + recordSet.getString("name") + "</div>" + "</div>";
/*     */     }
/*     */ 
/*     */     
/* 231 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List getLabelList(String paramString1, String paramString2) {
/* 241 */     ArrayList<SellChanceLabelVO> arrayList = new ArrayList();
/* 242 */     String str = "select id,name,isUsed,labelType,labelOrder,labelColor,textColor from CRM_SellchanceLabel where userid=" + paramString1;
/* 243 */     if (!paramString2.equals("all"))
/* 244 */       str = str + " and labelType=" + paramString2; 
/* 245 */     str = str + " order by labelOrder";
/* 246 */     RecordSet recordSet = new RecordSet();
/* 247 */     boolean bool = recordSet.execute(str);
/* 248 */     while (recordSet.next()) {
/*     */       
/* 250 */       String str1 = recordSet.getString("id");
/* 251 */       String str2 = recordSet.getString("name");
/* 252 */       String str3 = recordSet.getString("isUsed");
/* 253 */       String str4 = recordSet.getString("labelType");
/* 254 */       String str5 = recordSet.getString("labelOrder");
/* 255 */       String str6 = recordSet.getString("labelColor");
/* 256 */       String str7 = recordSet.getString("textColor");
/*     */       
/* 258 */       SellChanceLabelVO sellChanceLabelVO = new SellChanceLabelVO();
/* 259 */       sellChanceLabelVO.setId(str1);
/* 260 */       sellChanceLabelVO.setName(str2);
/* 261 */       sellChanceLabelVO.setUserid(paramString1);
/* 262 */       sellChanceLabelVO.setIsUsed(str3);
/* 263 */       sellChanceLabelVO.setLabelType(str4);
/* 264 */       sellChanceLabelVO.setLabelOrder(str5);
/* 265 */       sellChanceLabelVO.setLabelColor(str6);
/* 266 */       sellChanceLabelVO.setTextColor(str7);
/*     */       
/* 268 */       arrayList.add(sellChanceLabelVO);
/*     */     } 
/*     */     
/* 271 */     return arrayList;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/crm/sellchance/SellChanceLabelService.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */