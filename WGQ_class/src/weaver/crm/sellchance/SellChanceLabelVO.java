/*    */ package weaver.crm.sellchance;
/*    */ 
/*    */ public class SellChanceLabelVO {
/*    */   String id;
/*    */   String userid;
/*    */   String name;
/*    */   String labelColor;
/*    */   String createdate;
/*    */   String createtime;
/*    */   String isUsed;
/*    */   String labelOrder;
/*    */   String labelType;
/*    */   String textColor;
/*    */   
/*    */   public String getId() {
/* 16 */     return this.id;
/*    */   }
/*    */   public void setId(String paramString) {
/* 19 */     this.id = paramString;
/*    */   }
/*    */   public String getUserid() {
/* 22 */     return this.userid;
/*    */   }
/*    */   public void setUserid(String paramString) {
/* 25 */     this.userid = paramString;
/*    */   }
/*    */   public String getName() {
/* 28 */     return this.name;
/*    */   }
/*    */   public void setName(String paramString) {
/* 31 */     this.name = paramString;
/*    */   }
/*    */   public String getLabelColor() {
/* 34 */     return this.labelColor;
/*    */   }
/*    */   public void setLabelColor(String paramString) {
/* 37 */     this.labelColor = paramString;
/*    */   }
/*    */   public String getCreatedate() {
/* 40 */     return this.createdate;
/*    */   }
/*    */   public void setCreatedate(String paramString) {
/* 43 */     this.createdate = paramString;
/*    */   }
/*    */   public String getCreatetime() {
/* 46 */     return this.createtime;
/*    */   }
/*    */   public void setCreatetime(String paramString) {
/* 49 */     this.createtime = paramString;
/*    */   }
/*    */   public String getIsUsed() {
/* 52 */     return this.isUsed;
/*    */   }
/*    */   public void setIsUsed(String paramString) {
/* 55 */     this.isUsed = paramString;
/*    */   }
/*    */   public String getLabelOrder() {
/* 58 */     return this.labelOrder;
/*    */   }
/*    */   public void setLabelOrder(String paramString) {
/* 61 */     this.labelOrder = paramString;
/*    */   }
/*    */   public String getLabelType() {
/* 64 */     return this.labelType;
/*    */   }
/*    */   public void setLabelType(String paramString) {
/* 67 */     this.labelType = paramString;
/*    */   }
/*    */   public String getTextColor() {
/* 70 */     return this.textColor;
/*    */   }
/*    */   public void setTextColor(String paramString) {
/* 73 */     this.textColor = paramString;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/crm/sellchance/SellChanceLabelVO.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */