/*    */ package weaver.crm.sellchance;
/*    */ 
/*    */ import weaver.cache.CacheBase;
/*    */ import weaver.cache.CacheColumn;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class SellsuccessComInfo
/*    */   extends CacheBase
/*    */ {
/*    */   public static String table;
/* 14 */   protected static String TABLE_NAME = "CRM_Successfactor";
/* 15 */   protected static String PK_NAME = "id";
/*    */   
/*    */   @CacheColumn
/*    */   protected static int id;
/*    */   @CacheColumn
/*    */   protected static int fullname;
/*    */   @CacheColumn
/*    */   protected static int description;
/*    */   
/*    */   public int getSustatusNum() {
/* 25 */     return size();
/*    */   }
/*    */   
/*    */   public String getSuStatusid() {
/* 29 */     return (String)getRowValue(id);
/*    */   }
/*    */   
/*    */   public String getSuStatusname() {
/* 33 */     return (String)getRowValue(fullname);
/*    */   }
/*    */   
/*    */   public String getSuStatusname(String paramString) {
/* 37 */     return (String)getValue(fullname, paramString);
/*    */   }
/*    */   
/*    */   public String getDescription() {
/* 41 */     return (String)getRowValue(description);
/*    */   }
/*    */   
/*    */   public String getDescription(String paramString) {
/* 45 */     return (String)getValue(description, paramString);
/*    */   }
/*    */   public void removeSuccessCache() {
/* 48 */     removeCache();
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/crm/sellchance/SellsuccessComInfo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */