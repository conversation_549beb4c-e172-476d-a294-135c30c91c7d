/*     */ package weaver.km.util;
/*     */ 
/*     */ import java.text.DecimalFormat;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.docs.docs.DocComInfo;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.GCONST;
/*     */ import weaver.general.ThreadVarLanguage;
/*     */ import weaver.general.TimeUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ import weaver.splitepage.operate.SpopForDoc;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ public class CommonTransUtil extends BaseBean {
/*  19 */   private ResourceComInfo rc = null;
/*  20 */   private DocComInfo doc = null;
/*  21 */   private String isnullstr = "ISNULL";
/*     */   
/*     */   public CommonTransUtil() {
/*  24 */     RecordSet recordSet = new RecordSet();
/*     */     try {
/*  26 */       this.rc = new ResourceComInfo();
/*  27 */       this.doc = new DocComInfo();
/*  28 */       if (recordSet.getDBType().equals("oracle")) this.isnullstr = "NVL"; 
/*  29 */       if (recordSet.getDBType().equals("mysql")) this.isnullstr = "IFNULL"; 
/*  30 */       if (recordSet.getDBType().equals("postgresql")) this.isnullstr = "coalesce"; 
/*     */     } catch (Exception exception) {
/*  32 */       writeLog(exception);
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   public String getPerson(String paramString) {
/*  38 */     String str = "";
/*  39 */     if (paramString != null && !"".equals(paramString)) {
/*  40 */       ArrayList<String> arrayList = Util.TokenizerString(paramString, ",");
/*  41 */       for (byte b = 0; b < arrayList.size(); b++)
/*     */       {
/*     */ 
/*     */         
/*  45 */         str = str + "<a href='javaScript:openhrm(" + arrayList.get(b) + ");' onclick='pointerXY(event);'>" + this.rc.getResourcename(arrayList.get(b)) + "</a>&nbsp;";
/*     */       }
/*     */     } 
/*     */     
/*  49 */     return str;
/*     */   }
/*     */   public String getPerson2(String paramString) {
/*  52 */     String str = "";
/*  53 */     if (paramString != null && !"".equals(paramString)) {
/*  54 */       ArrayList<String> arrayList = Util.TokenizerString(paramString, ",");
/*  55 */       for (byte b = 0; b < arrayList.size(); b++)
/*     */       {
/*     */ 
/*     */         
/*  59 */         str = str + "<a href='" + GCONST.getContextPath() + "/hrm/resource/HrmResource.jsp?id=" + arrayList.get(b) + "' target='_blank'>" + this.rc.getResourcename(arrayList.get(b)) + "</a>&nbsp;";
/*     */       }
/*     */     } 
/*  62 */     return str;
/*     */   }
/*     */ 
/*     */   
/*     */   public String getDateTime(String paramString1, String paramString2) {
/*  67 */     return paramString1 + " " + paramString2;
/*     */   }
/*     */ 
/*     */   
/*     */   public String getDocName(String paramString) {
/*  72 */     String str = "";
/*  73 */     if (paramString != null && !"".equals(paramString)) {
/*  74 */       ArrayList<String> arrayList = Util.TokenizerString(paramString, ",");
/*  75 */       for (byte b = 0; b < arrayList.size(); b++) {
/*  76 */         str = str + "<a href=javaScript:openFullWindowHaveBar('" + GCONST.getContextPath() + "/docs/docs/DocDsp.jsp?id=" + arrayList.get(b) + "')>" + this.doc.getDocname(arrayList.get(b)) + "</a>&nbsp";
/*     */       }
/*     */     } 
/*  79 */     return str;
/*     */   }
/*     */ 
/*     */   
/*     */   public String getDocStateLinkNew(String paramString1, String paramString2) throws Exception {
/*  84 */     StringBuilder stringBuilder = new StringBuilder();
/*  85 */     if (null == paramString1 || "".equals(paramString1)) {
/*  86 */       return stringBuilder.toString();
/*     */     }
/*     */     
/*  89 */     String str1 = "";
/*  90 */     String str2 = "";
/*  91 */     RecordSet recordSet = new RecordSet();
/*     */     
/*  93 */     String[] arrayOfString = Util.TokenizerString2(paramString2, "_");
/*  94 */     String str3 = Util.null2String(arrayOfString[0]);
/*  95 */     String str4 = Util.null2String(arrayOfString[1]);
/*  96 */     String str5 = Util.null2String(arrayOfString[2]);
/*  97 */     String str6 = Util.null2String(arrayOfString[3]);
/*  98 */     String str7 = Util.null2String(arrayOfString[4]);
/*  99 */     String str8 = Util.null2String(arrayOfString[5]);
/* 100 */     String str9 = Util.null2String(arrayOfString[6]);
/* 101 */     int i = Util.getIntValue(arrayOfString[7], 7);
/*     */     
/* 103 */     SpopForDoc spopForDoc = new SpopForDoc();
/* 104 */     ArrayList<String> arrayList = Util.TokenizerString(paramString1, ",");
/* 105 */     String str10 = "";
/* 106 */     for (byte b = 0; b < arrayList.size(); b++) {
/* 107 */       str2 = arrayList.get(b);
/* 108 */       str10 = this.doc.getDocCreaterid(str2);
/* 109 */       if (str10.equals(str4)) {
/* 110 */         stringBuilder.append("<a href=javaScript:openFullWindowHaveBar('" + GCONST.getContextPath() + "/docs/docs/DocDsp.jsp?id=").append(str2).append("')>").append(this.doc.getDocname(str2)).append("</a>&nbsp;");
/*     */       }
/*     */       else {
/*     */         
/* 114 */         ArrayList<String> arrayList1 = spopForDoc.getDocOpratePopedom(str2, paramString2);
/* 115 */         if (((String)arrayList1.get(0)).equals("true")) {
/* 116 */           stringBuilder.append("<a href=javaScript:openFullWindowHaveBar('" + GCONST.getContextPath() + "/docs/docs/DocDsp.jsp?id=").append(str2).append("')>").append(this.doc.getDocname(str2)).append("</a>");
/* 117 */           String str = this.doc.getDocLastModDateTime(str2);
/*     */           
/* 119 */           recordSet.executeSql("select operatedate,operatetime from DocDetailLog where operatetype=0 and operateuserid=" + str4 + " and docid = " + str2 + " order by operatedate desc,operatetime desc");
/* 120 */           if (recordSet.next()) {
/* 121 */             String str11 = Util.null2String(recordSet.getString("operatedate"));
/* 122 */             String str12 = Util.null2String(recordSet.getString("operatetime"));
/* 123 */             if (TimeUtil.timeInterval(str11 + " " + str12, str) > 0L)
/* 124 */               stringBuilder.append("&nbsp;<img src='" + GCONST.getContextPath() + "/knowledgeMap/images/state2.png' border=0 style='margin-bottom: -2px'>"); 
/*     */           } else {
/* 126 */             stringBuilder.append("&nbsp;<img src='" + GCONST.getContextPath() + "/knowledgeMap/images/state1.png' border=0 style='margin-bottom: -2px'>");
/*     */           } 
/*     */         } else {
/* 129 */           stringBuilder.append(this.doc.getDocname(str2));
/*     */           
/* 131 */           String str = "";
/* 132 */           recordSet.executeSql("select orderable from DocSecCategory where id = (select seccategory from DocDetail where id = " + str2 + ")");
/* 133 */           if (recordSet.next()) str = recordSet.getString(1); 
/* 134 */           if ("1".equals(str)) {
/* 135 */             stringBuilder.append("&nbsp;<img src='" + GCONST.getContextPath() + "/knowledgeMap/images/state3.png' border=0 style='margin-bottom: -2px' style='cursor: pointer;' title='").append(SystemEnv.getHtmlLabelName(18668, i)).append("' onclick=openFullWindowForXtable('/docs/docsubscribe/DocSubscribeAdd.jsp?subscribeDocId=").append(str2).append("') >");
/* 136 */             str1 = str1 + "," + str2;
/*     */           } else {
/* 138 */             stringBuilder.append("&nbsp;<img src='" + GCONST.getContextPath() + "/knowledgeMap/images/state4.png' border=0 style='margin-bottom: -2px' >");
/*     */           } 
/*     */         } 
/*     */         
/* 142 */         stringBuilder.append("&nbsp;");
/*     */       } 
/*     */     } 
/* 145 */     if ("".equals(str9) && !"".equals(str1)) {
/* 146 */       str1 = str1.substring(1);
/* 147 */       stringBuilder.append("_").append(str1);
/*     */     } 
/*     */     
/* 150 */     return stringBuilder.toString();
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, String> getDocStateLinkNew2(String paramString1, String paramString2) throws Exception {
/* 155 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 156 */     if (null == paramString1 || "".equals(paramString1)) {
/* 157 */       return null;
/*     */     }
/* 159 */     StringBuilder stringBuilder = new StringBuilder();
/* 160 */     String str1 = "";
/* 161 */     String str2 = paramString1;
/* 162 */     RecordSet recordSet = new RecordSet();
/*     */     
/* 164 */     String[] arrayOfString = Util.TokenizerString2(paramString2, "_");
/* 165 */     String str3 = Util.null2String(arrayOfString[0]);
/* 166 */     String str4 = Util.null2String(arrayOfString[1]);
/* 167 */     String str5 = Util.null2String(arrayOfString[2]);
/* 168 */     String str6 = Util.null2String(arrayOfString[3]);
/* 169 */     String str7 = Util.null2String(arrayOfString[4]);
/* 170 */     String str8 = Util.null2String(arrayOfString[5]);
/* 171 */     String str9 = Util.null2String(arrayOfString[6]);
/* 172 */     int i = Util.getIntValue(arrayOfString[7], 7);
/*     */     
/* 174 */     SpopForDoc spopForDoc = new SpopForDoc();
/* 175 */     String str10 = "";
/* 176 */     str10 = this.doc.getDocCreaterid(str2);
/* 177 */     if (str10.equals(str4)) {
/* 178 */       stringBuilder.append("<a href=javaScript:openFullWindowHaveBar('" + GCONST.getContextPath() + "/docs/docs/DocDsp.jsp?id=").append(str2).append("')>").append(this.doc.getDocname(str2)).append("</a>&nbsp;");
/*     */     } else {
/* 180 */       ArrayList<String> arrayList = spopForDoc.getDocOpratePopedom(str2, paramString2);
/* 181 */       if (((String)arrayList.get(0)).equals("true")) {
/* 182 */         stringBuilder.append("<a href=javaScript:openFullWindowHaveBar('" + GCONST.getContextPath() + "/docs/docs/DocDsp.jsp?id=").append(str2).append("')>").append(this.doc.getDocname(str2)).append("</a>");
/* 183 */         String str = this.doc.getDocLastModDateTime(str2);
/*     */         
/* 185 */         recordSet.executeSql("select operatedate,operatetime from DocDetailLog where operatetype=0 and operateuserid=" + str4 + " and docid = " + str2 + " order by operatedate desc,operatetime desc");
/* 186 */         if (recordSet.next()) {
/* 187 */           String str11 = Util.null2String(recordSet.getString("operatedate"));
/* 188 */           String str12 = Util.null2String(recordSet.getString("operatetime"));
/* 189 */           if (TimeUtil.timeInterval(str11 + " " + str12, str) > 0L)
/* 190 */             stringBuilder.append("&nbsp;<img src='" + GCONST.getContextPath() + "/knowledgeMap/images/state2.png' border=0 style='margin-bottom: -2px'>"); 
/*     */         } else {
/* 192 */           stringBuilder.append("&nbsp;<img src='" + GCONST.getContextPath() + "/knowledgeMap/images/state1.png' border=0 style='margin-bottom: -2px'>");
/*     */         } 
/*     */       } else {
/* 195 */         stringBuilder.append(this.doc.getDocname(str2));
/*     */         
/* 197 */         String str = "";
/* 198 */         recordSet.executeSql("select orderable from DocSecCategory where id = (select seccategory from DocDetail where id = " + str2 + ")");
/* 199 */         if (recordSet.next()) str = recordSet.getString(1); 
/* 200 */         if ("1".equals(str)) {
/* 201 */           stringBuilder.append("&nbsp;<img src='" + GCONST.getContextPath() + "/knowledgeMap/images/state3.png' border=0 style='margin-bottom: -2px' style='cursor: pointer;' title='").append(SystemEnv.getHtmlLabelName(18668, i)).append("' onclick=openFullWindowForXtable('/docs/docsubscribe/DocSubscribeAdd.jsp?subscribeDocId=").append(str2).append("') >");
/* 202 */           str1 = str1 + "," + str2;
/*     */         } else {
/* 204 */           stringBuilder.append("&nbsp;<img src='" + GCONST.getContextPath() + "/knowledgeMap/images/state4.png' border=0 style='margin-bottom: -2px' >");
/*     */         } 
/*     */       } 
/* 207 */       stringBuilder.append("&nbsp;");
/*     */     } 
/* 209 */     hashMap.put("docNames", stringBuilder.toString());
/* 210 */     if ("".equals(str9) && !"".equals(str1)) {
/* 211 */       str1 = str1.substring(1);
/* 212 */       hashMap.put("subscribeIds", str1);
/*     */     } 
/* 214 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public String getDocStateLink(String paramString1, String paramString2) throws Exception {
/* 220 */     String str1 = "";
/* 221 */     String str2 = "";
/* 222 */     String str3 = "";
/* 223 */     RecordSet recordSet = new RecordSet();
/* 224 */     if (paramString1 != null && !"".equals(paramString1)) {
/* 225 */       String[] arrayOfString = Util.TokenizerString2(paramString2, "_");
/* 226 */       String str4 = Util.null2String(arrayOfString[0]);
/* 227 */       String str5 = Util.null2String(arrayOfString[1]);
/* 228 */       String str6 = Util.null2String(arrayOfString[2]);
/* 229 */       String str7 = Util.null2String(arrayOfString[3]);
/* 230 */       String str8 = Util.null2String(arrayOfString[4]);
/* 231 */       String str9 = Util.null2String(arrayOfString[5]);
/* 232 */       String str10 = Util.null2String(arrayOfString[6]);
/*     */       
/* 234 */       SpopForDoc spopForDoc = new SpopForDoc();
/* 235 */       ArrayList<String> arrayList = Util.TokenizerString(paramString1, ",");
/* 236 */       String str11 = "";
/* 237 */       for (byte b = 0; b < arrayList.size(); b++) {
/* 238 */         str3 = arrayList.get(b);
/*     */         
/* 240 */         String str = "select doccreaterid from DocDetail where id = " + str3;
/* 241 */         recordSet.executeSql(str);
/* 242 */         if (recordSet.next()) {
/* 243 */           str11 = Util.null2String(recordSet.getString("doccreaterid"));
/*     */           
/* 245 */           if (!str11.equals(str5)) {
/*     */             
/* 247 */             ArrayList<String> arrayList1 = spopForDoc.getDocOpratePopedom(str3, paramString2);
/* 248 */             if (((String)arrayList1.get(0)).equals("true")) {
/* 249 */               str1 = str1 + "<a href=javaScript:openFullWindowHaveBar('" + GCONST.getContextPath() + "/docs/docs/DocDsp.jsp?id=" + str3 + "')>" + this.doc.getDocname(str3) + "</a>";
/* 250 */               if (this.doc.getIsNewDoc(str3, str7, str5)) {
/* 251 */                 str1 = str1 + "&nbsp;<img src='" + GCONST.getContextPath() + "/knowledgeMap/images/state1.png' border=0 style='margin-bottom: -2px'>";
/*     */               } else {
/* 253 */                 recordSet.executeSql("select doclastmoddate,doclastmodtime from DocDetail where id = " + str3);
/* 254 */                 if (recordSet.next()) {
/* 255 */                   String str12 = Util.null2String(recordSet.getString("doclastmoddate"));
/* 256 */                   String str13 = Util.null2String(recordSet.getString("doclastmodtime"));
/* 257 */                   recordSet.executeSql("select operatedate,operatetime from DocDetailLog where operatetype=0 and operateuserid=" + str5 + " and docid = " + str3 + " order by operatedate desc,operatetime desc");
/*     */                   
/* 259 */                   String str14 = Util.null2String(recordSet.getString("operatedate"));
/* 260 */                   String str15 = Util.null2String(recordSet.getString("operatetime"));
/* 261 */                   if (recordSet.next() && TimeUtil.timeInterval(str14 + " " + str15, str12 + " " + str13) > 0L) {
/* 262 */                     str1 = str1 + "&nbsp;<img src='" + GCONST.getContextPath() + "/knowledgeMap/images/state2.png' border=0 style='margin-bottom: -2px'>";
/*     */                   }
/*     */                 } 
/*     */               } 
/*     */             } else {
/*     */               
/* 268 */               str1 = str1 + this.doc.getDocname(str3);
/*     */               
/* 270 */               String str12 = "";
/* 271 */               recordSet.executeSql("select orderable from DocSecCategory where id = (select seccategory from DocDetail where id = " + str3 + ")");
/* 272 */               if (recordSet.next()) str12 = recordSet.getString(1); 
/* 273 */               if ("1".equals(str12)) {
/* 274 */                 str1 = str1 + "&nbsp;<img src='" + GCONST.getContextPath() + "/knowledgeMap/images/state3.png' border=0 style='margin-bottom: -2px' style='cursor: pointer;' title='" + SystemEnv.getHtmlLabelName(18668, ThreadVarLanguage.getLang()) + "' onclick=openFullWindowForXtable('/docs/docsubscribe/DocSubscribeAdd.jsp?subscribeDocId=" + str3 + "') >";
/* 275 */                 str2 = str2 + "," + str3;
/*     */               } else {
/* 277 */                 str1 = str1 + "&nbsp;<img src='" + GCONST.getContextPath() + "/knowledgeMap/images/state4.png' border=0 style='margin-bottom: -2px' >";
/*     */               } 
/*     */             } 
/*     */           } else {
/* 281 */             str1 = str1 + "<a href=javaScript:openFullWindowHaveBar('" + GCONST.getContextPath() + "/docs/docs/DocDsp.jsp?id=" + str3 + "')>" + this.doc.getDocname(str3) + "</a>";
/*     */           } 
/* 283 */           str1 = str1 + "&nbsp;";
/*     */         } 
/*     */       } 
/* 286 */       if (str10.equals("1") && 
/* 287 */         !str2.equals("")) {
/* 288 */         str2 = str2.substring(1);
/* 289 */         str1 = str1 + "_" + str2;
/*     */       } 
/*     */     } 
/*     */     
/* 293 */     return str1;
/*     */   }
/*     */ 
/*     */   
/*     */   public String getKnowledgeLink(String paramString1, String paramString2) {
/* 298 */     return "<a href=javascript:openOperateWindow('" + GCONST.getContextPath() + "/knowledgeMap/base/KnowledgeView.jsp?knowledgeId=" + paramString1 + "')>" + paramString2 + "</a>";
/*     */   }
/*     */ 
/*     */   
/*     */   public String getKnowledgeOperate(String paramString1, String paramString2) {
/* 303 */     String[] arrayOfString1 = Util.TokenizerString2(paramString1, "+");
/* 304 */     String str1 = Util.null2String(arrayOfString1[0]);
/* 305 */     String str2 = Util.null2String(arrayOfString1[1]);
/*     */     
/* 307 */     String[] arrayOfString2 = Util.TokenizerString2(paramString2, "+");
/* 308 */     String str3 = Util.null2String(arrayOfString2[0]);
/* 309 */     String str4 = Util.null2String(arrayOfString2[1]);
/*     */     
/* 311 */     if ("1".equals(str4)) {
/* 312 */       return "<a href=javascript:openOperateWindow('" + GCONST.getContextPath() + "/knowledgeMap/base/KnowledgeEdit.jsp?knowledgeId=" + str1 + "')>" + SystemEnv.getHtmlLabelName(93, ThreadVarLanguage.getLang()) + "</a>&nbsp;&nbsp;<a href=javascript:doDelete('" + str1 + "')>" + 
/* 313 */         SystemEnv.getHtmlLabelName(130663, ThreadVarLanguage.getLang()) + "</a>&nbsp;&nbsp;<a href=javascript:openOperateWindow('" + 
/* 314 */         GCONST.getContextPath() + "/knowledgeMap/base/KnowledgeShow.jsp?knowledgeId=" + str1 + "')>" + SystemEnv.getHtmlLabelName(126095, ThreadVarLanguage.getLang()) + "</a>";
/*     */     }
/*     */     
/* 317 */     return "<a href=javascript:openOperateWindow('" + GCONST.getContextPath() + "/knowledgeMap/base/KnowledgeShow.jsp?knowledgeId=" + str1 + "')>" + SystemEnv.getHtmlLabelName(126095, ThreadVarLanguage.getLang()) + "</a>";
/*     */   }
/*     */ 
/*     */   
/*     */   public String getMapLink(String paramString1, String paramString2) {
/* 322 */     return "<a href=javascript:openOperateWindow('" + GCONST.getContextPath() + "/knowledgeMap/base/MapView.jsp?mapId=" + paramString1 + "')>" + paramString2 + "</a>";
/*     */   }
/*     */ 
/*     */   
/*     */   public String getMapOperate(String paramString1, String paramString2) {
/* 327 */     String[] arrayOfString1 = Util.TokenizerString2(paramString1, "+");
/* 328 */     String str1 = Util.null2String(arrayOfString1[0]);
/* 329 */     String str2 = Util.null2String(arrayOfString1[1]);
/*     */     
/* 331 */     String[] arrayOfString2 = Util.TokenizerString2(paramString2, "+");
/* 332 */     String str3 = Util.null2String(arrayOfString2[0]);
/* 333 */     String str4 = Util.null2String(arrayOfString2[1]);
/* 334 */     String str5 = Util.null2String(arrayOfString2[2]);
/* 335 */     String str6 = "";
/* 336 */     if ("1".equals(str4)) {
/*     */ 
/*     */       
/* 339 */       str6 = "<a href=javascript:openOperateWindow('" + GCONST.getContextPath() + "/knowledgeMap/base/MapEdit.jsp?mapId=" + str1 + "')>" + SystemEnv.getHtmlLabelName(93, ThreadVarLanguage.getLang()) + "</a>&nbsp;&nbsp;<a href=javascript:doDelete('" + str1 + "')>" + SystemEnv.getHtmlLabelName(130663, ThreadVarLanguage.getLang()) + "</a>&nbsp;&nbsp;<a href=javascript:openFullWindowHaveBar('" + GCONST.getContextPath() + "/knowledgeMap/data/MainInfo.jsp?mapId=" + str1 + "')>" + SystemEnv.getHtmlLabelName(10003717, ThreadVarLanguage.getLang()) + "</a>";
/*     */     }
/*     */     else {
/*     */       
/* 343 */       str6 = "<a href=javascript:openFullWindowHaveBar('" + GCONST.getContextPath() + "/knowledgeMap/data/MainInfo.jsp?mapId=" + str1 + "')>" + SystemEnv.getHtmlLabelName(10003717, ThreadVarLanguage.getLang()) + "</a>";
/*     */     } 
/*     */     
/* 346 */     if ("1".equals(str5)) {
/* 347 */       str6 = str6 + "&nbsp;&nbsp;<a href=javascript:doAddMenu('" + GCONST.getContextPath() + "/knowledgeMap/data/MainInfo.jsp?mapId=" + str1 + "')>" + SystemEnv.getHtmlLabelName(84130, ThreadVarLanguage.getLang()) + "</a>";
/*     */     }
/* 349 */     str6 = str6 + "&nbsp;&nbsp;<a href=javascript:showUrl('" + GCONST.getContextPath() + "/knowledgeMap/data/MainInfo.jsp?mapId=" + str1 + "')>URL</a>";
/* 350 */     return str6;
/*     */   }
/*     */ 
/*     */   
/*     */   public String getKnowledgeStatus(String paramString1, String paramString2, String paramString3) {
/* 355 */     RecordSet recordSet = new RecordSet();
/* 356 */     String str1 = "0";
/* 357 */     String str2 = "select " + this.isnullstr + "(SUM(t2.rate),0.00) as rate  from KT_KnowledgeHrm t1,KT_KnowledgeDetail t2 where t1.detailId=t2.id and t1.konwledgeId=t2.knowledgeId and t1.deleted=0 and t2.deleted=0 and t1.hrmId=" + paramString1 + " and t1.konwledgeId=" + paramString2 + " and t1.mapid=" + paramString3;
/*     */ 
/*     */ 
/*     */     
/* 361 */     recordSet.executeSql(str2);
/* 362 */     if (recordSet.next()) {
/* 363 */       str1 = myRound(Util.getDoubleValue(recordSet.getString(1), 0.0D) + "", 0);
/*     */     }
/* 365 */     recordSet.writeLog("================228行数据=" + str1);
/* 366 */     recordSet.writeLog("================229行数据=" + str2);
/* 367 */     return str1;
/*     */   }
/*     */ 
/*     */   
/*     */   public String getThemeStatus(String paramString1, String paramString2, String paramString3) {
/* 372 */     RecordSet recordSet = new RecordSet();
/* 373 */     String str1 = "0";
/* 374 */     String str2 = "select sum(" + this.isnullstr + "((a.rate*b.rate)/100,0)) as rate from KT_ThemeKnowledge a  left join  (select " + this.isnullstr + "(SUM(t2.rate),0.00) as rate,t1.konwledgeId  from KT_KnowledgeHrm t1,KT_KnowledgeDetail t2 where t1.detailId=t2.id and t1.konwledgeId=t2.knowledgeId and t1.deleted=0 and t2.deleted=0 and t1.hrmId=" + paramString1 + " and t1.mapId=" + paramString3 + " group by t1.konwledgeId) b on a.kId=b.konwledgeId where a.deleted=0 and a.tId=" + paramString2;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 382 */     recordSet.executeSql(str2);
/* 383 */     if (recordSet.next()) {
/* 384 */       str1 = myRound(Util.getDoubleValue(recordSet.getString(1), 0.0D) + "", 0);
/*     */     }
/* 386 */     return str1;
/*     */   }
/*     */ 
/*     */   
/*     */   public String getMapStatus(String paramString1, String paramString2) {
/* 391 */     RecordSet recordSet = new RecordSet();
/* 392 */     String str1 = "0";
/* 393 */     String str2 = "select sum(" + this.isnullstr + "(t.rate*(a.rate*b.rate)/10000,0)) as rate from KT_MapDetail t join KT_ThemeKnowledge a on t.id=a.tId join (select " + this.isnullstr + "(SUM(t2.rate),0.00) as rate,t1.konwledgeId  from KT_KnowledgeHrm t1,KT_KnowledgeDetail t2  where t1.detailId=t2.id and t1.konwledgeId=t2.knowledgeId and t1.deleted=0 and t2.deleted=0 and t1.hrmId=" + paramString1 + " and t1.mapId=" + paramString2 + " group by t1.konwledgeId) b  on a.kId=b.konwledgeId where t.deleted=0 and a.deleted=0  and t.mapId=" + paramString2;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 403 */     recordSet.executeSql(str2);
/* 404 */     recordSet.writeLog("=============MapStatussql=" + str2);
/* 405 */     if (recordSet.next()) {
/* 406 */       str1 = myRound(Util.getDoubleValue(recordSet.getString(1), 0.0D) + "", 0);
/*     */     }
/* 408 */     return str1;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map getTAllKStatus(String paramString1, String paramString2, String paramString3) {
/* 413 */     RecordSet recordSet = new RecordSet();
/* 414 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 415 */     String str = "select a.tId,a.kId," + this.isnullstr + "(b.rate,0) as rate from KT_ThemeKnowledge a  left join  (select " + this.isnullstr + "(SUM(t2.rate),0.00) as rate,t1.konwledgeId  from KT_KnowledgeHrm t1,KT_KnowledgeDetail t2 where t1.detailId=t2.id and t1.konwledgeId=t2.knowledgeId and t1.deleted=0 and t2.deleted=0 and t1.mapId= " + paramString3 + " and t1.hrmId=" + paramString1 + " group by t1.konwledgeId) b on a.kId=b.konwledgeId where a.deleted=0 and a.tId=" + paramString2 + " order by a.id ";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 425 */     recordSet.executeSql(str);
/* 426 */     recordSet.writeLog("===getTAllKStatus sql:" + str);
/* 427 */     while (recordSet.next()) {
/* 428 */       hashMap.put(recordSet.getString("kId"), myRound(Util.getDoubleValue(recordSet.getString("rate") + "", 0.0D) + "", 0));
/*     */     }
/* 430 */     return hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map getMAllTStatus(String paramString1, String paramString2) {
/* 435 */     RecordSet recordSet = new RecordSet();
/* 436 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 437 */     String str = "select t.mapId,t.id as tId,sum(" + this.isnullstr + "((a.rate*b.rate)/100,0)) as rate from KT_MapDetail t join KT_ThemeKnowledge a on t.id=a.tId join (select " + this.isnullstr + "(SUM(t2.rate),0.00) as rate,t1.konwledgeId  from KT_KnowledgeHrm t1,KT_KnowledgeDetail t2  where t1.detailId=t2.id and t1.konwledgeId=t2.knowledgeId and t1.deleted=0 and t2.deleted=0 and t1.hrmId=" + paramString1 + " and t1.mapId=" + paramString2 + " group by t1.konwledgeId) b  on a.kId=b.konwledgeId where t.deleted=0 and a.deleted=0  and t.mapId=" + paramString2 + " group by t.id,t.mapId,t.sort order by t.sort";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 449 */     recordSet.executeSql(str);
/* 450 */     while (recordSet.next()) {
/* 451 */       hashMap.put(recordSet.getString("tId"), myRound(Util.getDoubleValue(recordSet.getString("rate") + "", 0.0D) + "", 0));
/*     */     }
/* 453 */     return hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public String myRound(String paramString, int paramInt) {
/* 458 */     if (paramString == null || paramString.length() < 1) {
/* 459 */       return "";
/*     */     }
/* 461 */     DecimalFormat decimalFormat = null;
/* 462 */     double d = Double.parseDouble(paramString);
/* 463 */     if (paramInt == 0) {
/* 464 */       decimalFormat = new DecimalFormat("##0");
/*     */     } else {
/* 466 */       StringBuffer stringBuffer = new StringBuffer();
/* 467 */       stringBuffer.append("##0.");
/* 468 */       for (byte b = 0; b < paramInt; b++) {
/* 469 */         stringBuffer.append("0");
/*     */       }
/* 471 */       decimalFormat = new DecimalFormat(stringBuffer.toString());
/*     */     } 
/* 473 */     return decimalFormat.format(d);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/km/util/CommonTransUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */