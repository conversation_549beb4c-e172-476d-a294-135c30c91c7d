/*     */ package wgqzc.workflow;
/*     */ 
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.formmode.setup.ModeRightInfo;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.interfaces.workflow.action.Action;
/*     */ import weaver.soa.workflow.request.Property;
/*     */ import weaver.soa.workflow.request.RequestInfo;
/*     */ import weaver.workflow.request.RequestManager;
/*     */ import weaver.workflow.workflow.WorkflowComInfo;
/*     */ 
/*     */ public class ProcMeetingAction
/*     */   extends BaseBean
/*     */   implements Action {
/*     */   public String execute(RequestInfo request) {
/*  19 */     String retStr = "1";
/*  20 */     RequestManager rm = request.getRequestManager();
/*  21 */     writeLog(getClass().getName() + " -- ActionDemo xxxx ");
/*     */     try {
/*  23 */       WorkflowComInfo workflowComInfo = new WorkflowComInfo();
/*  24 */       int requestid = Util.getIntValue(request.getRequestid());
/*  25 */       int cjr = Util.getIntValue(request.getCreatorid());
/*  26 */       String requestname = Util.null2String(request.getRequestManager().getRequestname());
/*  27 */       int workflowid = Util.getIntValue(request.getWorkflowid());
/*  28 */       int formid = Util.getIntValue(workflowComInfo.getFormId("" + workflowid));
/*  29 */       String maintablename = "formtable_main_" + Math.abs(formid);
/*  30 */       int wfstatus = request.getRequestManager().getCreater();
/*  31 */       writeLog("requestid = " + requestid);
/*  32 */       writeLog("workflowid = " + workflowid);
/*  33 */       writeLog("formid = " + formid);
/*  34 */       String sql = "";
/*  35 */       RecordSet rs = new RecordSet();
/*     */ 
/*     */       
/*  38 */       String bt = "";
/*  39 */       String sqr = "";
/*  40 */       String bzsm = "";
/*  41 */       String sqlx = "";
/*  42 */       String cx = "";
/*  43 */       String mj = "";
/*  44 */       String ssbm = "";
/*  45 */       String zsjbm = "";
/*  46 */       String gxdx = "";
/*     */ 
/*     */       
/*  49 */       String sqlxString = "";
/*  50 */       String cxString = "";
/*  51 */       String mjString = "";
/*     */       
/*  53 */       String xgfj = "";
/*  54 */       String xgxwjdwd = "";
/*  55 */       String fujian = "";
/*     */       
/*  57 */       Property[] properties = request.getMainTableInfo().getProperty();
/*  58 */       for (int i = 0; i < properties.length; i++) {
/*  59 */         String field = properties[i].getName();
/*  60 */         if ("bt".equalsIgnoreCase(field)) {
/*  61 */           bt = Util.null2String(properties[i].getValue());
/*     */         }
/*  63 */         if ("sqr".equalsIgnoreCase(field)) {
/*  64 */           sqr = Util.null2String(properties[i].getValue());
/*     */         }
/*  66 */         if ("bzsm".equalsIgnoreCase(field)) {
/*  67 */           bzsm = Util.null2String(properties[i].getValue());
/*     */         }
/*  69 */         if ("sqlx".equalsIgnoreCase(field)) {
/*  70 */           sqlx = Util.null2String(properties[i].getValue());
/*  71 */           if ("0".equals(sqlx)) {
/*  72 */             sqlxString = "专业室输出审批";
/*  73 */           } else if ("1".equals(sqlx)) {
/*  74 */             sqlxString = "集中输出审批";
/*  75 */           } else if ("2".equals(sqlx)) {
/*  76 */             sqlxString = "切割指令审批";
/*     */           } 
/*     */         } 
/*  79 */         if ("cx".equalsIgnoreCase(field)) {
/*  80 */           cx = Util.null2String(properties[i].getValue());
/*  81 */           if ("0".equals(cx)) {
/*  82 */             cxString = "民船";
/*  83 */           } else if ("1".equals(cx)) {
/*  84 */             cxString = "邮轮";
/*  85 */           } else if ("2".equals(cx)) {
/*  86 */             cxString = "海工";
/*     */           } 
/*     */         } 
/*  89 */         if ("mj".equalsIgnoreCase(field)) {
/*  90 */           mj = Util.null2String(properties[i].getValue());
/*  91 */           if ("0".equals(mj)) {
/*  92 */             mjString = "核心商密";
/*  93 */           } else if ("1".equals(mj)) {
/*  94 */             mjString = "普通商密";
/*  95 */           } else if ("2".equals(mj)) {
/*  96 */             mjString = "内部资料";
/*     */           } 
/*     */         } 
/*  99 */         if ("ssbm".equalsIgnoreCase(field)) {
/* 100 */           ssbm = Util.null2String(properties[i].getValue());
/*     */         }
/* 102 */         if ("zsjbm".equalsIgnoreCase(field)) {
/* 103 */           zsjbm = Util.null2String(properties[i].getValue());
/*     */         }
/* 105 */         if ("gxdx".equalsIgnoreCase(field)) {
/* 106 */           gxdx = Util.null2String(properties[i].getValue());
/*     */         }
/* 108 */         if ("xgfj".equalsIgnoreCase(field)) {
/* 109 */           xgfj = Util.null2String(properties[i].getValue());
/*     */         }
/* 111 */         if ("xgxwjdwd".equalsIgnoreCase(field)) {
/* 112 */           xgxwjdwd = Util.null2String(properties[i].getValue());
/*     */         }
/*     */         
/* 115 */         writeLog("name===========>" + field);
/*     */       } 
/* 117 */       String cynr = "【申请类型】" + sqlxString + " <br/>【船型】" + cxString + " <br/>【密级】" + mjString + " <br/>【用途说明】" + bzsm + "<br/>";
/*     */       
/* 119 */       String formmodeid = "501";
/* 120 */       String fjxz = "2";
/* 121 */       String cyxx = "4,5";
/* 122 */       sql = "insert into uf_sws_nbcy(requestid,formmodeid,modedatacreater,modedatacreatertype,modedatacreatedate,modedatacreatetime,modedatastatus,cybt,fqr,fqsj,cynr,fjxz,cyxx,ycsj,bm,zsjbm) values(" + requestid + "," + formmodeid + "," + sqr + ",0,to_char(sysdate,'yyyy-mm-dd'),to_char(sysdate,'hh24:mi:ss'),0,'【文件共享】" + requestname + "','" + sqr + "',to_char(sysdate,'yyyy-mm-dd'),'" + cynr + "','" + fjxz + "','" + cyxx + "',to_char(sysdate,'hh24:mi'),'" + ssbm + "','" + zsjbm + "')";
/*     */ 
/*     */ 
/*     */       
/* 126 */       writeLog("into uf_sws_nbcy ------ " + sql);
/* 127 */       rs.execute(sql);
/*     */       
/* 129 */       sql = "select id from uf_sws_nbcy where requestid=" + requestid;
/* 130 */       writeLog("select sql======" + sql);
/* 131 */       rs.execute(sql);
/* 132 */       String id = "";
/* 133 */       if (rs.next()) {
/* 134 */         id = rs.getString("id");
/*     */       }
/*     */       
/* 137 */       if (!"".equals(xgfj)) {
/* 138 */         fujian = xgfj;
/*     */       } else {
/* 140 */         fujian = xgxwjdwd;
/*     */       } 
/* 142 */       if (!"".equals(fujian)) {
/* 143 */         String detailsql = "insert into uf_sws_nbcy_dt1(mainid,fjmc,fjscr,fjscsj) values(" + id + ",'" + fujian + "','" + sqr + "',to_char(sysdate,'yyyy-mm-dd hh24:mi'))";
/* 144 */         rs.execute(detailsql);
/* 145 */         writeLog("insert uf_sws_nbcy_dt1=====" + detailsql);
/*     */       } 
/*     */       
/* 148 */       String[] hrmmembersArray = gxdx.split(",");
/* 149 */       for (int j = 0; j < hrmmembersArray.length; j++) {
/* 150 */         Map<String, String> map = queryPeopInfo(hrmmembersArray[j]);
/* 151 */         String detail3sql = "insert into uf_sws_nbcy_dt3(mainid,cydx,rypx,rygh,zt) values(" + id + ",'" + hrmmembersArray[j] + "','" + (String)map.get("dsporder") + "','" + (String)map.get("workcode") + "','0')";
/* 152 */         rs.execute(detail3sql);
/* 153 */         writeLog("success insert uf_sws_nbcy and uf_sws_nbcy_dt1");
/*     */       } 
/*     */ 
/*     */       
/* 157 */       String billsql = "select id,modedatacreater,formmodeid from uf_sws_nbcy where requestid='" + requestid + "'";
/* 158 */       rs.execute(billsql);
/* 159 */       while (rs.next()) {
/* 160 */         int billid = Util.getIntValue(rs.getString("id"));
/* 161 */         int modedatacreater = Util.getIntValue(rs.getString("modedatacreater"));
/* 162 */         int modeid = Util.getIntValue(rs.getString("formmodeid"));
/* 163 */         ModeRightInfo ModeRightInfo = new ModeRightInfo();
/* 164 */         ModeRightInfo.rebuildModeDataShareByEdit(modedatacreater, modeid, billid);
/*     */       } 
/* 166 */       writeLog("success insert into uf_sws_nbcy_dt3 ");
/* 167 */     } catch (Exception ex) {
/*     */       
/* 169 */       if (rm != null) {
/*     */         
/* 171 */         rm.setMessagecontent("插入会议建模数据失败, 请联系系统管理员处理.");
/* 172 */         retStr = "0";
/*     */       } 
/*     */       
/* 175 */       writeLog(ex);
/*     */     } 
/*     */     
/* 178 */     return retStr;
/*     */   }
/*     */   public static Map<String, String> queryPeopInfo(String peopid) {
/* 181 */     RecordSet rs = new RecordSet();
/* 182 */     String sql = "select dsporder,workcode,lastname from hrmresource where id=" + peopid;
/* 183 */     rs.execute(sql);
/* 184 */     Map<String, String> map = new HashMap<>();
/* 185 */     if (rs.next()) {
/* 186 */       map.put("dsporder", rs.getString("dsporder"));
/* 187 */       map.put("workcode", rs.getString("workcode"));
/* 188 */       map.put("lastname", rs.getString("lastname"));
/*     */     } 
/* 190 */     return map;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/wgqzc/workflow/ProcMeetingAction.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */