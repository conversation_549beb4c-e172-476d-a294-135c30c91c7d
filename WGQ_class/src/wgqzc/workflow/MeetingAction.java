/*     */ package wgqzc.workflow;
/*     */ 
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.formmode.setup.ModeRightInfo;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.interfaces.workflow.action.Action;
/*     */ import weaver.soa.workflow.request.Property;
/*     */ import weaver.soa.workflow.request.RequestInfo;
/*     */ import weaver.workflow.request.RequestManager;
/*     */ import weaver.workflow.workflow.WorkflowComInfo;
/*     */ 
/*     */ public class MeetingAction
/*     */   extends BaseBean
/*     */   implements Action {
/*     */   public String execute(RequestInfo request) {
/*  19 */     String retStr = "1";
/*  20 */     RequestManager rm = request.getRequestManager();
/*  21 */     writeLog(getClass().getName() + " -- ActionDemo xxxx ");
/*     */     try {
/*  23 */       WorkflowComInfo workflowComInfo = new WorkflowComInfo();
/*  24 */       int requestid = Util.getIntValue(request.getRequestid());
/*  25 */       int cjr = Util.getIntValue(request.getCreatorid());
/*  26 */       String requestname = Util.null2String(request.getRequestManager().getRequestname());
/*  27 */       int workflowid = Util.getIntValue(request.getWorkflowid());
/*  28 */       int formid = Util.getIntValue(workflowComInfo.getFormId("" + workflowid));
/*  29 */       String maintablename = "formtable_main_" + Math.abs(formid);
/*  30 */       int wfstatus = request.getRequestManager().getCreater();
/*  31 */       writeLog("requestid = " + requestid);
/*  32 */       writeLog("workflowid = " + workflowid);
/*  33 */       writeLog("formid = " + formid);
/*  34 */       String sql = "";
/*  35 */       RecordSet rs = new RecordSet();
/*     */ 
/*     */       
/*  38 */       String name = "";
/*  39 */       String caller = "";
/*  40 */       String address = "";
/*  41 */       String desc_n = "";
/*  42 */       String contacter = "";
/*  43 */       String begindate = "";
/*  44 */       String begintime = "";
/*  45 */       String enddate = "";
/*  46 */       String endtime = "";
/*  47 */       String ssbm = "";
/*  48 */       String zsjbm = "";
/*  49 */       String accessorys = "";
/*  50 */       String hrmmembers = "";
/*  51 */       String sqr = "";
/*     */ 
/*     */       
/*  54 */       Property[] properties = request.getMainTableInfo().getProperty();
/*  55 */       for (int i = 0; i < properties.length; i++) {
/*  56 */         String field = properties[i].getName();
/*  57 */         if ("name".equalsIgnoreCase(field)) {
/*  58 */           name = Util.null2String(properties[i].getValue());
/*     */         }
/*  60 */         if ("caller".equalsIgnoreCase(field)) {
/*  61 */           caller = Util.null2String(properties[i].getValue());
/*     */         }
/*  63 */         if ("address".equalsIgnoreCase(field)) {
/*  64 */           address = Util.null2String(properties[i].getValue());
/*     */         }
/*  66 */         if ("desc_n".equalsIgnoreCase(field)) {
/*  67 */           desc_n = Util.null2String(properties[i].getValue());
/*     */         }
/*  69 */         if ("contacter".equalsIgnoreCase(field)) {
/*  70 */           contacter = Util.null2String(properties[i].getValue());
/*     */         }
/*  72 */         if ("begindate".equalsIgnoreCase(field)) {
/*  73 */           begindate = Util.null2String(properties[i].getValue());
/*     */         }
/*  75 */         if ("begintime".equalsIgnoreCase(field)) {
/*  76 */           begintime = Util.null2String(properties[i].getValue());
/*     */         }
/*  78 */         if ("enddate".equalsIgnoreCase(field)) {
/*  79 */           enddate = Util.null2String(properties[i].getValue());
/*     */         }
/*  81 */         if ("endtime".equalsIgnoreCase(field)) {
/*  82 */           endtime = Util.null2String(properties[i].getValue());
/*     */         }
/*  84 */         if ("ssbm".equalsIgnoreCase(field)) {
/*  85 */           ssbm = Util.null2String(properties[i].getValue());
/*     */         }
/*  87 */         if ("zsjbm".equalsIgnoreCase(field)) {
/*  88 */           zsjbm = Util.null2String(properties[i].getValue());
/*     */         }
/*  90 */         if ("accessorys".equalsIgnoreCase(field)) {
/*  91 */           accessorys = Util.null2String(properties[i].getValue());
/*     */         }
/*  93 */         if ("hrmmembers".equalsIgnoreCase(field)) {
/*  94 */           hrmmembers = Util.null2String(properties[i].getValue());
/*     */         }
/*  96 */         if ("sqr".equalsIgnoreCase(field)) {
/*  97 */           sqr = Util.null2String(properties[i].getValue());
/*     */         }
/*  99 */         writeLog("name===========>" + field);
/*     */       } 
/* 101 */       String meetingName = queryMeetingAddById(address);
/* 102 */       String peopName = getPeoplebyid(hrmmembers);
/* 103 */       contacter = getPeoplebyid(contacter);
/* 104 */       String cynr = "【会议名称】" + name + "<br/>" + "【会议时间】" + begindate + " " + begintime + " 至 " + enddate + " " + endtime + "<br/>" + "【会议地点】" + meetingName + "<br/>" + "【主持人】" + contacter + "<br/>" + "【参会人员】" + peopName + "<br/>" + "【会议安排】" + desc_n + "<br/>";
/*     */       
/* 106 */       String formmodeid = "501";
/* 107 */       String fjxz = "2";
/* 108 */       String cyxx = "4,5";
/* 109 */       sql = "insert into uf_sws_nbcy(requestid,formmodeid,modedatacreater,modedatacreatertype,modedatacreatedate,modedatacreatetime,modedatastatus,cybt,fqr,fqsj,cynr,fjxz,cyxx,ycsj,bm,zsjbm) values(" + requestid + "," + formmodeid + "," + sqr + ",0,to_char(sysdate,'yyyy-mm-dd'),to_char(sysdate,'hh24:mi:ss'),0,'【会议通知】" + name + "','" + caller + "',to_char(sysdate,'yyyy-mm-dd'),'" + cynr + "','" + fjxz + "','" + cyxx + "',to_char(sysdate,'hh24:mi'),'" + ssbm + "','" + zsjbm + "')";
/*     */ 
/*     */ 
/*     */       
/* 113 */       writeLog("into uf_sws_nbcy ------ " + sql);
/* 114 */       rs.execute(sql);
/*     */       
/* 116 */       sql = "select id from uf_sws_nbcy where requestid=" + requestid;
/* 117 */       writeLog("select sql======" + sql);
/* 118 */       rs.execute(sql);
/* 119 */       String id = "";
/* 120 */       if (rs.next()) {
/* 121 */         id = rs.getString("id");
/*     */       }
/*     */       
/* 124 */       if (!"".equals(accessorys)) {
/* 125 */         String detailsql = "insert into uf_sws_nbcy_dt1(mainid,fjmc,fjscr,fjscsj) values(" + id + ",'" + accessorys + "','" + caller + "',to_char(sysdate,'yyyy-mm-dd hh24:mi'))";
/* 126 */         rs.execute(detailsql);
/* 127 */         writeLog("insert uf_sws_nbcy_dt1=====" + detailsql);
/*     */       } 
/*     */       
/* 130 */       String[] hrmmembersArray = hrmmembers.split(",");
/* 131 */       for (int j = 0; j < hrmmembersArray.length; j++) {
/* 132 */         Map<String, String> map = queryPeopInfo(hrmmembersArray[j]);
/* 133 */         String detail3sql = "insert into uf_sws_nbcy_dt3(mainid,cydx,rypx,rygh,zt) values(" + id + ",'" + hrmmembersArray[j] + "','" + (String)map.get("dsporder") + "','" + (String)map.get("workcode") + "','0')";
/* 134 */         rs.execute(detail3sql);
/* 135 */         writeLog("success insert uf_sws_nbcy and uf_sws_nbcy_dt1");
/*     */       } 
/*     */ 
/*     */       
/* 139 */       String billsql = "select id,modedatacreater,formmodeid from uf_sws_nbcy where requestid='" + requestid + "'";
/* 140 */       rs.execute(billsql);
/* 141 */       while (rs.next()) {
/* 142 */         int billid = Util.getIntValue(rs.getString("id"));
/* 143 */         int modedatacreater = Util.getIntValue(rs.getString("modedatacreater"));
/* 144 */         int modeid = Util.getIntValue(rs.getString("formmodeid"));
/* 145 */         ModeRightInfo ModeRightInfo = new ModeRightInfo();
/* 146 */         ModeRightInfo.rebuildModeDataShareByEdit(modedatacreater, modeid, billid);
/*     */       } 
/* 148 */       writeLog("success insert into uf_sws_nbcy_dt3 ");
/* 149 */     } catch (Exception ex) {
/*     */       
/* 151 */       if (rm != null) {
/*     */         
/* 153 */         rm.setMessagecontent("插入会议建模数据失败, 请联系系统管理员处理.");
/* 154 */         retStr = "0";
/*     */       } 
/*     */       
/* 157 */       writeLog(ex);
/*     */     } 
/*     */     
/* 160 */     return retStr;
/*     */   }
/*     */   public static String queryMeetingAddById(String meetingid) {
/* 163 */     String sql = "select name from  meetingroom where id='" + meetingid + "'";
/* 164 */     RecordSet recordSet = new RecordSet();
/* 165 */     recordSet.execute(sql);
/* 166 */     String name = "";
/* 167 */     if (recordSet.next()) {
/* 168 */       name = recordSet.getString("name");
/*     */     }
/* 170 */     return name;
/*     */   }
/*     */   public static Map<String, String> queryPeopInfo(String peopid) {
/* 173 */     RecordSet rs = new RecordSet();
/* 174 */     String sql = "select dsporder,workcode,lastname from hrmresource where id=" + peopid;
/* 175 */     rs.execute(sql);
/* 176 */     Map<String, String> map = new HashMap<>();
/* 177 */     if (rs.next()) {
/* 178 */       map.put("dsporder", rs.getString("dsporder"));
/* 179 */       map.put("workcode", rs.getString("workcode"));
/* 180 */       map.put("lastname", rs.getString("lastname"));
/*     */     } 
/* 182 */     return map;
/*     */   }
/*     */   public static String getPeoplebyid(String id) {
/* 185 */     RecordSet rs = new RecordSet();
/* 186 */     String sql = "select lastname from hrmresource where id in (" + id + ") order by dsporder asc,workcode asc";
/* 187 */     rs.execute(sql);
/* 188 */     String lastname = "";
/* 189 */     while (rs.next()) {
/* 190 */       lastname = lastname + rs.getString("lastname") + ",";
/*     */     }
/* 192 */     if (!"".equals(lastname)) {
/* 193 */       lastname = lastname.substring(0, lastname.length() - 1);
/*     */     }
/* 195 */     return lastname;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/wgqzc/workflow/MeetingAction.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */