/*      */ package Menu;
/*      */ 
/*      */ import java.applet.Applet;
/*      */ import java.applet.AudioClip;
/*      */ import java.awt.Color;
/*      */ import java.awt.Component;
/*      */ import java.awt.Container;
/*      */ import java.awt.Event;
/*      */ import java.awt.Font;
/*      */ import java.awt.Frame;
/*      */ import java.awt.Graphics;
/*      */ import java.awt.Image;
/*      */ import java.awt.MediaTracker;
/*      */ import java.awt.Menu;
/*      */ import java.awt.MenuItem;
/*      */ import java.awt.PopupMenu;
/*      */ import java.awt.Rectangle;
/*      */ import java.awt.SystemColor;
/*      */ import java.io.BufferedReader;
/*      */ import java.io.InputStreamReader;
/*      */ import java.net.URL;
/*      */ import java.util.StringTokenizer;
/*      */ import netscape.javascript.JSObject;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ public class apXPDropDown
/*      */   extends Applet
/*      */   implements Runnable
/*      */ {
/*      */   Thread III;
/*      */   int II1;
/*      */   int IIl;
/*      */   Image I1I;
/*      */   Image[] I11;
/*      */   Graphics IlI;
/*      */   int Il1;
/*      */   int Ill;
/*      */   int lII;
/*      */   int lI1;
/*      */   int lIl;
/*      */   int l1I;
/*      */   int I1;
/*      */   int lIl1I;
/*      */   int lIlI1;
/*      */   int lIlIl;
/*      */   String[][] l1l;
/*      */   String[] llI;
/*      */   String[] ll1;
/*      */   String[] lll;
/*      */   String l1II;
/*      */   String l1I1;
/*      */   String llllI;
/*      */   String lI1ll;
/*      */   URL[] l1Il;
/*      */   URL[] l11I;
/*      */   int[] l111;
/*      */   int[] l11l;
/*      */   int[] l1lI;
/*      */   int[] lll11;
/*      */   int[][] l1l1;
/*      */   int[] lll1I;
/*      */   int[] llll1;
/*      */   int l1ll;
/*      */   int lIII;
/*      */   int lllIl;
/*      */   int lll1l;
/*   88 */   int[][] lIllI = new int[][] { { 20, 17, 17, 17, 17, 17, 17 }, { 20, 30, 31, 31, 31, 30, 30 }, { 30, 34, 34, 34, 32, 31, 30 }, { 34, 33, 31, 31, 30, 29, 28 }, { 34, 31, 29, 29, -1, -1, -1 }, { 32, 29, 27, -1, -1, -1, -1 }, { 31, 28, 26, -1, -1, -1, -1 }, { 30, 27, -1, -1, -1, -1, -1 }, { 30, 26, -1, -1, -1, -1, -1 } }; String lI11l; URL lI1lI; int lI1l1; int[] lIIll; int[] lIlII; int llIIl; PopupMenu[] lI11; MenuItem[] lI1l; AudioClip lIlI; AudioClip lIl1; Frame lIll; boolean llII; boolean lIIlI; boolean llIl; boolean ll1I; boolean ll11; boolean ll1l; boolean lllI;
/*      */   boolean lll1;
/*      */   boolean llll;
/*      */   int I1II;
/*      */   int lIIl1;
/*      */   int I1Il;
/*      */   int I11I;
/*      */   int lllII;
/*      */   int lllI1;
/*      */   int I111;
/*      */   int I11l;
/*      */   int I1lI;
/*      */   int I1l1;
/*      */   int I1ll;
/*      */   String IIII;
/*      */   String III1;
/*      */   String IIIl;
/*      */   String II1I;
/*      */   int II11;
/*      */   int II1l;
/*      */   int IIlI;
/*  109 */   int[][] lIll1 = new int[][] { { 17, 16, 15, 24, -1 }, { 29, 28, 27, 20, 16 }, { -1, -1, 28, 22, -1 }, { -1, 28, 27, -1, -1 } };
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*  120 */   int[][] lIl1l = new int[][] { { -1, -1, 25, -1, -1 }, { -1, -1, 24, 21, -1 }, { 25, 24, 22, 20, -1 }, { -1, 21, 20, 11, 18 }, { -1, -1, -1, 18, -1 } };
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*  133 */   int[][] lIl11 = new int[][] { { -1, 28, -1, -1 }, { -1, 27, 25, -1 }, { 18, 11, 24, 23 }, { -1, 18, -1, -1 } };
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   int I;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public apXPDropDown() {
/*  148 */     this.lIl1I = 3;
/*  149 */     this.lIlI1 = 2;
/*  150 */     this.lIlIl = 2;
/*  151 */     this.llllI = "Loading...";
/*  152 */     this.lI1ll = "";
/*  153 */     this.l1ll = -1;
/*  154 */     this.lllIl = -2;
/*  155 */     this.lll1l = -2;
/*  156 */     this.lI11l = "_";
/*  157 */     this.lI1l1 = -1;
/*  158 */     this.llIIl = 20;
/*  159 */     this.llII = true;
/*  160 */     this.lIIlI = true;
/*  161 */     this.llIl = false;
/*  162 */     this.ll1I = false;
/*  163 */     this.ll11 = true;
/*  164 */     this.ll1l = true;
/*  165 */     this.lllI = false;
/*  166 */     this.lll1 = true;
/*  167 */     this.llll = false;
/*  168 */     this.I1II = 2383580;
/*  169 */     this.lIIl1 = 2383580;
/*  170 */     this.I1Il = 16777215;
/*  171 */     this.I11I = 16777215;
/*  172 */     this.lllII = -1;
/*  173 */     this.lllI1 = -1;
/*  174 */     this.I1ll = 0;
/*  175 */     this.IIII = "";
/*  176 */     this.III1 = "left";
/*  177 */     this.IIIl = "link";
/*  178 */     this.II1I = ",";
/*  179 */     this.II11 = 11;
/*  180 */     this.II1l = 0;
/*  181 */     this.IIlI = 0;
/*  182 */     this.I = -1;
/*      */   }
/*      */ 
/*      */   
/*      */   String I(String paramString) {
/*  187 */     char[] arrayOfChar = paramString.toCharArray();
/*  188 */     for (byte b = 0; b < arrayOfChar.length; b++)
/*      */     {
/*  190 */       arrayOfChar[b] = (char)(arrayOfChar[b] - b % 3 - 1);
/*      */     }
/*      */ 
/*      */     
/*  194 */     return new String("");
/*      */   }
/*      */ 
/*      */   
/*      */   public void II(String paramString) {
/*  199 */     if ("0123456789".indexOf(paramString.substring(11, 12)) != -1)
/*      */     {
/*  201 */       paramString = getParameter(paramString);
/*      */     }
/*  203 */     if (paramString != null) {
/*      */       
/*  205 */       if (paramString.startsWith("javascript:"))
/*      */       {
/*  207 */         paramString = paramString.substring(11);
/*      */       }
/*      */       
/*      */       try {
/*  211 */         JSObject.getWindow(this).eval(paramString);
/*      */ 
/*      */       
/*      */       }
/*  215 */       catch (Throwable throwable) {
/*      */         try {
/*  217 */           JSObject jSObject = JSObject.getWindow(this);
/*  218 */           String str = "";
/*  219 */           StringTokenizer stringTokenizer = new StringTokenizer(paramString, "()");
/*  220 */           paramString = stringTokenizer.nextToken().trim();
/*  221 */           if (stringTokenizer.hasMoreTokens())
/*      */           {
/*  223 */             str = stringTokenizer.nextToken();
/*      */           }
/*  225 */           stringTokenizer = new StringTokenizer(str.trim(), "'");
/*  226 */           String[] arrayOfString = new String[16];
/*  227 */           byte b = 0;
/*  228 */           arrayOfString[0] = "";
/*  229 */           while (stringTokenizer.hasMoreTokens()) {
/*      */             
/*  231 */             arrayOfString[b] = stringTokenizer.nextToken().trim();
/*  232 */             if (arrayOfString[b].equals(",")) {
/*      */               
/*  234 */               arrayOfString[b] = null;
/*      */               continue;
/*      */             } 
/*  237 */             b++;
/*      */           } 
/*      */           
/*  240 */           JSObject.getWindow(this).call(paramString, (Object[])arrayOfString);
/*      */         }
/*  242 */         catch (Exception exception) {}
/*      */       } 
/*      */     } 
/*      */   }
/*      */ 
/*      */   
/*      */   public void IIl1() {
/*  249 */     this.lII = 0;
/*  250 */     this.Ill = 0;
/*  251 */     this.Il1 = 0;
/*  252 */     this.l1II = getParameter("Copyright");
/*      */ 
/*      */     
/*  255 */     this.llll = true;
/*      */     
/*  257 */     this.I1II = lII1l("backColor", 16, this.I1II);
/*  258 */     if (this.I1II == -999)
/*      */     {
/*  260 */       this.I1II = SystemColor.control.getRGB();
/*      */     }
/*  262 */     this.lIIl1 = this.I1II;
/*  263 */     this.I1Il = lII1l("fontColor", 16, this.I1Il);
/*  264 */     if (this.I1Il == -999)
/*      */     {
/*  266 */       this.I1Il = SystemColor.controlText.getRGB();
/*      */     }
/*  268 */     this.I11I = this.I1Il;
/*  269 */     this.l1II = getParameter("loadingString");
/*  270 */     if (this.l1II != null)
/*      */     {
/*  272 */       this.llllI = this.l1II;
/*      */     }
/*  274 */     this.II1 = (size()).width;
/*  275 */     this.IIl = (size()).height;
/*  276 */     this.II1 = lII1l("Width", 10, this.II1);
/*  277 */     this.IIl = lII1l("Height", 10, this.IIl);
/*      */     Container container;
/*  279 */     for (container = this; container != null && !(container instanceof Frame); container = ((Component)container).getParent());
/*  280 */     this.lIll = (Frame)container;
/*  281 */     if (this.lIll != null)
/*      */     {
/*  283 */       this.lIll.setCursor(3);
/*      */     }
/*  285 */     this.I1I = createImage(this.II1, this.IIl);
/*  286 */     this.IlI = this.I1I.getGraphics();
/*  287 */     this.lIIll = new int[this.llIIl * 2 + 1];
/*  288 */     this.lIIll = lII11(this.I1II);
/*  289 */     this.I11l = this.lIIll[0];
/*  290 */     this.I111 = this.lIIll[30];
/*  291 */     lIII1();
/*  292 */     this.IlI.setFont(new Font("Arial", 0, 11));
/*  293 */     this.IlI.setColor(new Color(this.I1Il));
/*  294 */     this.IlI.drawString(this.llllI, 10, 15);
/*  295 */     IlI1();
/*  296 */     this.I1ll = lII1l("buttonType", 10, this.I1ll);
/*  297 */     this.lllIl = lII1l("pressedItem", 10, this.lllIl) - 1;
/*  298 */     this.lIIl1 = lII1l("buttonColor", 16, this.lIIl1);
/*  299 */     if (this.lIIl1 == -999)
/*      */     {
/*  301 */       this.lIIl1 = SystemColor.textHighlight.getRGB();
/*      */     }
/*  303 */     this.lIlII = new int[this.llIIl * 2 + 1];
/*  304 */     this.lIlII = lII11(this.lIIl1);
/*  305 */     this.I1l1 = this.lIlII[0];
/*  306 */     this.I1lI = this.lIlII[30];
/*  307 */     this.I11I = lII1l("fontHighColor", 16, this.I11I);
/*  308 */     if (this.I11I == -999)
/*      */     {
/*  310 */       this.I11I = SystemColor.textHighlightText.getRGB();
/*      */     }
/*  312 */     this.lllII = lII1l("shadowColor", 16, this.lllII);
/*  313 */     this.lllI1 = lII1l("shadowHighColor", 16, this.lllI1);
/*  314 */     this.l1II = getParameter("isHorizontal");
/*  315 */     if (this.l1II != null && this.l1II.equalsIgnoreCase("false"))
/*      */     {
/*  317 */       this.llII = false;
/*      */     }
/*  319 */     this.l1II = getParameter("3DBackground");
/*  320 */     if (this.l1II != null && this.l1II.equalsIgnoreCase("false")) {
/*      */       
/*  322 */       this.lIIlI = false;
/*  323 */       this.lIl1I = 0;
/*  324 */       this.lIlI1 = 0;
/*  325 */       this.lIlIl = 0;
/*      */     } 
/*  327 */     this.l1II = getParameter("solidArrows");
/*  328 */     if (this.l1II != null && this.l1II.equalsIgnoreCase("true"))
/*      */     {
/*  330 */       this.ll1I = true;
/*      */     }
/*  332 */     this.l1II = getParameter("popupOver");
/*  333 */     if (this.l1II != null && this.l1II.equalsIgnoreCase("true"))
/*      */     {
/*  335 */       this.llIl = true;
/*      */     }
/*  337 */     this.l1II = getParameter("showArrows");
/*  338 */     if (this.l1II != null && this.l1II.equalsIgnoreCase("false"))
/*      */     {
/*  340 */       this.ll11 = false;
/*      */     }
/*  342 */     this.l1II = getParameter("systemSubFont");
/*  343 */     if (this.l1II != null && this.l1II.equalsIgnoreCase("false"))
/*      */     {
/*  345 */       this.ll1l = false;
/*      */     }
/*  347 */     this.l1II = getParameter("alignText");
/*  348 */     if (this.l1II != null)
/*      */     {
/*  350 */       this.III1 = this.l1II;
/*      */     }
/*  352 */     this.l1II = getParameter("status");
/*  353 */     if (this.l1II != null)
/*      */     {
/*  355 */       this.IIIl = this.l1II;
/*      */     }
/*  357 */     this.l1II = getParameter("statusString");
/*  358 */     if (this.l1II != null)
/*      */     {
/*  360 */       this.l1I1 = this.l1II;
/*      */     }
/*      */     
/*      */     try {
/*  364 */       this.l1II = getParameter("overSound");
/*  365 */       if (this.l1II != null)
/*      */       {
/*  367 */         this.lIlI = getAudioClip(getDocumentBase(), this.l1II);
/*      */       }
/*  369 */       this.l1II = getParameter("clickSound");
/*  370 */       if (this.l1II != null)
/*      */       {
/*  372 */         this.lIl1 = getAudioClip(getDocumentBase(), this.l1II);
/*      */       }
/*      */     }
/*  375 */     catch (Exception exception) {}
/*  376 */     this.l1II = getParameter("font");
/*  377 */     if (this.l1II != null) {
/*      */       
/*  379 */       StringTokenizer stringTokenizer1 = new StringTokenizer(this.l1II, ",");
/*  380 */       this.IIII = stringTokenizer1.nextToken();
/*  381 */       this.II11 = Integer.parseInt(stringTokenizer1.nextToken());
/*  382 */       this.II1l = Integer.parseInt(stringTokenizer1.nextToken());
/*      */     } 
/*  384 */     this.IlI.setFont(new Font(this.IIII, this.II1l, this.II11));
/*  385 */     this.l1II = getParameter("delimiter");
/*  386 */     if (this.l1II != null)
/*      */     {
/*  388 */       this.II1I = this.l1II;
/*      */     }
/*  390 */     this.l1II = getParameter("encoding");
/*  391 */     if (this.l1II != null)
/*      */     {
/*  393 */       this.lI1ll = this.l1II;
/*      */     }
/*  395 */     this.l1II = getParameter("menuItemsFile");
/*  396 */     if (this.l1II != null) {
/*      */ 
/*      */       
/*      */       try {
/*  400 */         URL uRL = new URL(getDocumentBase(), this.l1II);
/*  401 */         this.l1II = "";
/*  402 */         BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(uRL.openStream()));
/*      */         String str1;
/*  404 */         while ((str1 = bufferedReader.readLine()) != null)
/*      */         {
/*  406 */           this.l1II += str1;
/*      */         }
/*      */       }
/*  409 */       catch (Exception exception) {}
/*      */     } else {
/*      */       
/*  412 */       this.l1II = getParameter("menuItems");
/*      */     } 
/*  414 */     this.l1II = this.l1II.substring(0, this.l1II.lastIndexOf("}"));
/*  415 */     StringTokenizer stringTokenizer = new StringTokenizer(this.l1II, "}");
/*  416 */     this.lII = stringTokenizer.countTokens();
/*  417 */     for (stringTokenizer = new StringTokenizer(this.l1II, "}"); stringTokenizer.hasMoreTokens(); ) {
/*      */       
/*  419 */       String str1 = stringTokenizer.nextToken();
/*  420 */       str1 = str1.substring(str1.indexOf("{") + 1).trim();
/*  421 */       if (str1.startsWith("|"))
/*      */       {
/*  423 */         this.Il1++;
/*      */       }
/*      */     } 
/*      */     
/*  427 */     stringTokenizer = new StringTokenizer(this.l1II, "}");
/*  428 */     this.Ill = this.lII;
/*  429 */     this.lII = this.Ill - this.Il1;
/*  430 */     this.l111 = new int[this.lII];
/*  431 */     this.l11l = new int[this.lII];
/*  432 */     this.l1lI = new int[this.lII];
/*  433 */     this.lll11 = new int[this.lII];
/*  434 */     this.l1l1 = new int[this.lII][10];
/*  435 */     this.lll1I = new int[this.lII];
/*  436 */     this.llll1 = new int[this.lII];
/*  437 */     this.l1l = new String[this.lII][10];
/*  438 */     this.l1Il = new URL[this.lII];
/*  439 */     this.llI = new String[this.lII];
/*  440 */     this.lI11 = new PopupMenu[this.lII];
/*  441 */     this.lI1l = new MenuItem[this.Il1];
/*  442 */     this.ll1 = new String[this.Il1];
/*  443 */     this.l11I = new URL[this.Il1];
/*  444 */     this.lll = new String[this.Il1];
/*  445 */     this.I11 = new Image[this.lII];
/*  446 */     this.lIl = 0;
/*  447 */     this.l1I = 0;
/*  448 */     byte b1 = 0;
/*  449 */     int i = 0;
/*  450 */     String str = "";
/*  451 */     while (stringTokenizer.hasMoreTokens()) {
/*      */       
/*  453 */       this.l1II = stringTokenizer.nextToken();
/*  454 */       this.l1II = this.l1II.substring(this.l1II.indexOf("{") + 1);
/*  455 */       StringTokenizer stringTokenizer1 = new StringTokenizer(this.l1II, this.II1I);
/*  456 */       String str1 = "";
/*  457 */       String str2 = "";
/*  458 */       int k = this.l1II.indexOf("javascript:");
/*  459 */       int m = this.l1II.lastIndexOf(")") + 1;
/*  460 */       if (k != -1 && m > k) {
/*      */         
/*  462 */         str1 = this.l1II.substring(k, m);
/*  463 */         k = this.l1II.lastIndexOf(this.II1I) + 1;
/*  464 */         m = this.l1II.lastIndexOf(".") + 4;
/*  465 */         if (m > k)
/*      */         {
/*  467 */           str2 = this.l1II.substring(k, m);
/*      */         }
/*      */       } 
/*  470 */       String str3 = stringTokenizer1.nextToken();
/*  471 */       if (this.lI1ll != "") {
/*      */         
/*      */         try {
/*      */           
/*  475 */           str3 = new String(str3.getBytes(), this.lI1ll);
/*      */         }
/*  477 */         catch (Exception exception) {}
/*      */       }
/*  479 */       if (!this.l1II.trim().startsWith("|")) {
/*      */         
/*  481 */         this.l1l[this.l1I][0] = str3;
/*  482 */         StringTokenizer stringTokenizer2 = new StringTokenizer(this.l1l[this.l1I][0], "\\");
/*      */         byte b;
/*  484 */         for (b = 0; stringTokenizer2.hasMoreTokens(); b++)
/*      */         {
/*  486 */           this.l1l[this.l1I][b] = stringTokenizer2.nextToken();
/*      */         }
/*      */         
/*  489 */         this.llll1[this.l1I] = b;
/*  490 */         if (b > 1)
/*      */         {
/*  492 */           i += b - 1;
/*      */         }
/*  494 */         if (this.l1l[this.l1I][0].equals("_"))
/*      */         {
/*  496 */           this.l1l[this.l1I][0] = "";
/*      */         }
/*  498 */         if (this.l1l[this.l1I][0].trim().equals("-")) {
/*      */           
/*  500 */           b1++;
/*  501 */           this.l1I++;
/*      */           continue;
/*      */         } 
/*  504 */         if (!stringTokenizer1.hasMoreTokens()) {
/*      */           
/*  506 */           this.l1I++;
/*      */           continue;
/*      */         } 
/*  509 */         if (stringTokenizer1.countTokens() == 1) {
/*      */           
/*  511 */           this.I11[this.l1I] = IlII(stringTokenizer1.nextToken());
/*      */         } else {
/*      */           
/*  514 */           this.l1II = stringTokenizer1.nextToken().trim();
/*  515 */           if (!this.l1II.startsWith("javascript:")) {
/*      */ 
/*      */             
/*      */             try {
/*  519 */               this.l1Il[this.l1I] = new URL(getDocumentBase(), this.l1II);
/*      */             }
/*  521 */             catch (Exception exception) {}
/*  522 */             this.llI[this.l1I] = stringTokenizer1.nextToken();
/*      */           }
/*  524 */           else if (str1 == "") {
/*      */             
/*  526 */             this.llI[this.l1I] = this.l1II;
/*  527 */             this.l1II = stringTokenizer1.nextToken();
/*      */           } else {
/*      */             
/*  530 */             this.llI[this.l1I] = str1;
/*  531 */             if (str2 != "")
/*      */             {
/*  533 */               this.I11[this.l1I] = IlII(str2);
/*      */             }
/*  535 */             this.l1I++;
/*      */             continue;
/*      */           } 
/*  538 */           if (stringTokenizer1.hasMoreTokens())
/*      */           {
/*  540 */             this.I11[this.l1I] = IlII(stringTokenizer1.nextToken());
/*      */           }
/*      */         } 
/*  543 */         this.l1I++;
/*      */         continue;
/*      */       } 
/*  546 */       this.llI[this.l1I - 1] = "__menu" + (this.l1I - 1) + "_";
/*  547 */       this.ll1[this.lIl] = str3;
/*  548 */       if (stringTokenizer1.hasMoreTokens()) {
/*      */         
/*  550 */         this.l1II = stringTokenizer1.nextToken().trim();
/*  551 */         if (!this.l1II.startsWith("javascript:")) {
/*      */ 
/*      */           
/*      */           try {
/*  555 */             this.l11I[this.lIl] = new URL(getDocumentBase(), this.l1II);
/*      */           }
/*  557 */           catch (Exception exception) {}
/*  558 */           this.lll[this.lIl] = stringTokenizer1.nextToken();
/*      */         }
/*  560 */         else if (str1 == "") {
/*      */           
/*  562 */           this.lll[this.lIl] = this.l1II;
/*      */         } else {
/*      */           
/*  565 */           this.lll[this.lIl] = str1;
/*      */         } 
/*      */       } 
/*  568 */       this.lll[this.lIl] = this.lll[this.lIl] + this.llI[this.l1I - 1];
/*  569 */       this.lIl++;
/*      */     } 
/*      */     
/*  572 */     for (byte b2 = 0; b2 < this.lII; b2++) {
/*      */       
/*  574 */       if (this.llI[b2] != null && this.llI[b2].startsWith("__menu")) {
/*      */         
/*  576 */         this.lI11[b2] = new PopupMenu();
/*  577 */         Illl(this.lI11[b2], b2, 1);
/*  578 */         add(this.lI11[b2]);
/*      */       } 
/*      */     } 
/*      */     
/*  582 */     if (!this.llII) {
/*      */       
/*  584 */       this.lI1 = (this.IIl - b1 * 8 - this.lIl1I + this.lIlI1 - (this.II11 + 1) * i) / (this.lII - b1);
/*      */     } else {
/*      */       
/*  587 */       this.lI1 = this.IIl - this.lIl1I + this.lIlI1;
/*      */     } 
/*  589 */     this.lIl = 0;
/*  590 */     int j = 0;
/*  591 */     if (this.llII) {
/*      */       
/*  593 */       for (byte b3 = 0; b3 < this.lII; b3++) {
/*      */         
/*  595 */         if (!this.l1l[b3][0].trim().equals("-")) {
/*      */           
/*  597 */           this.lll1I[b3] = this.lI1 / 2 - (this.II11 + 1) * (this.llll1[b3] - 1) / 2 + this.II11 / 2 - 1;
/*  598 */           this.l1lI[b3] = 0;
/*  599 */           for (byte b = 0; b < this.llll1[b3]; b++) {
/*      */             
/*  601 */             if (this.l1lI[b3] < this.IlI.getFontMetrics().stringWidth(this.l1l[b3][b]))
/*      */             {
/*  603 */               this.l1lI[b3] = this.IlI.getFontMetrics().stringWidth(this.l1l[b3][b]);
/*      */             }
/*      */           } 
/*      */           
/*  607 */           if (this.llI[b3].startsWith("__menu") && this.ll11) {
/*      */             
/*  609 */             j = 12;
/*  610 */             this.l1lI[b3] = this.l1lI[b3] + j;
/*      */           } 
/*  612 */           if (this.I11[b3] != null)
/*      */           {
/*  614 */             this.l1lI[b3] = this.l1lI[b3] + this.I11[b3].getWidth(this) + 3;
/*      */           }
/*  616 */           this.lIl += this.l1lI[b3];
/*      */         } 
/*      */       } 
/*      */       
/*  620 */       this.l111[0] = this.lIlIl;
/*  621 */       for (byte b4 = 0; b4 < this.lII; b4++) {
/*      */         
/*  623 */         j = 0;
/*  624 */         if (this.llI[b4] != null && this.llI[b4].startsWith("__menu") && this.ll11)
/*      */         {
/*  626 */           j = this.II11;
/*      */         }
/*  628 */         this.lll11[b4] = this.lI1;
/*  629 */         int k = (this.II1 - 10 * b1 - this.lIl) / (this.lII - b1) / 2;
/*  630 */         if (this.l1l[b4][0].trim().equals("-")) {
/*      */           
/*  632 */           this.l1lI[b4] = 10;
/*      */         } else {
/*      */           
/*  635 */           this.l1lI[b4] = this.l1lI[b4] + k * 2;
/*      */         } 
/*  637 */         if (b4 > 0)
/*      */         {
/*  639 */           this.l111[b4] = this.l111[b4 - 1] + this.l1lI[b4 - 1];
/*      */         }
/*  641 */         this.l11l[b4] = this.lIl1I;
/*  642 */         int m = 0;
/*  643 */         if (this.I11[b4] != null)
/*      */         {
/*  645 */           m = this.I11[b4].getWidth(this);
/*      */         }
/*  647 */         for (byte b = 0; b < this.llll1[b4]; b++) {
/*      */           
/*  649 */           this.l1l1[b4][b] = (this.l1lI[b4] - this.IlI.getFontMetrics().stringWidth(this.l1l[b4][b]) + m - j + 3) / 2;
/*  650 */           if (this.III1.equalsIgnoreCase("right"))
/*      */           {
/*  652 */             this.l1l1[b4][b] = this.l1l1[b4][b] * 2 - m - this.II11 + 3;
/*      */           }
/*  654 */           if (this.III1.equalsIgnoreCase("left"))
/*      */           {
/*  656 */             this.l1l1[b4][b] = this.II11 / 2 + m + 3;
/*      */           }
/*      */         } 
/*      */       } 
/*      */ 
/*      */       
/*  662 */       this.l1lI[this.lII - 1] = this.II1 - this.l111[this.lII - 1] - this.lIlIl;
/*  663 */       IlI1();
/*      */     } else {
/*      */       
/*  666 */       this.lIl = this.lIl1I;
/*  667 */       for (byte b = 0; b < this.lII; b++) {
/*      */         
/*  669 */         this.l111[b] = this.lIlIl;
/*  670 */         this.l1lI[b] = this.II1 - this.lIlIl * 2;
/*  671 */         if (this.l1l[b][0].trim().equals("-")) {
/*      */           
/*  673 */           this.l11l[b] = this.lIl;
/*  674 */           this.lIl += 10;
/*      */         } else {
/*      */           
/*  677 */           if (this.llI[b].startsWith("__menu") && this.ll11)
/*      */           {
/*  679 */             j = this.II11;
/*      */           }
/*  681 */           this.lll1I[b] = this.lI1 / 2 + this.II11 / 2 - 1;
/*  682 */           this.l11l[b] = this.lIl;
/*  683 */           this.lll11[b] = this.lI1 + (this.llll1[b] - 1) * (this.II11 + 1);
/*  684 */           this.lIl += this.lll11[b];
/*  685 */           for (byte b3 = 0; b3 < this.llll1[b]; b3++) {
/*      */             
/*  687 */             this.l1l1[b][b3] = this.II11 / 2 + this.IIlI + 3;
/*  688 */             if (this.III1.equalsIgnoreCase("right"))
/*      */             {
/*  690 */               this.l1l1[b][b3] = this.II1 - this.IlI.getFontMetrics().stringWidth(this.l1l[b][b3]) - this.II11 - j;
/*      */             }
/*  692 */             if (this.III1.equalsIgnoreCase("center"))
/*      */             {
/*  694 */               this.l1l1[b][b3] = (this.II1 - this.IlI.getFontMetrics().stringWidth(this.l1l[b][b3])) / 2;
/*      */             }
/*      */           } 
/*      */         } 
/*      */       } 
/*      */     } 
/*      */ 
/*      */     
/*  702 */     System.gc();
/*      */   }
/*      */ 
/*      */   
/*      */   int IIll(int paramInt) {
/*  707 */     if (paramInt > 255)
/*      */     {
/*  709 */       return 255;
/*      */     }
/*  711 */     if (paramInt < 0)
/*      */     {
/*  713 */       return 0;
/*      */     }
/*      */     
/*  716 */     return paramInt;
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   public void Il1I(int paramInt1, int paramInt2, int paramInt3) {
/*  722 */     if (!this.llI[paramInt1].startsWith("__menu") || !this.ll11) {
/*      */       return;
/*      */     }
/*      */     
/*  726 */     int i = this.I111;
/*  727 */     int j = this.I11l;
/*  728 */     int k = this.I1Il;
/*  729 */     if (paramInt3 != 0) {
/*      */       
/*  731 */       int m = this.I1lI;
/*  732 */       int n = this.I1l1;
/*  733 */       k = this.I11I;
/*      */     } 
/*  735 */     boolean bool = false;
/*  736 */     if (this.llII) {
/*      */       
/*  738 */       int m = 0;
/*  739 */       byte b1 = 0;
/*  740 */       for (byte b2 = 0; b2 < this.llll1[paramInt1]; b2++) {
/*      */         
/*  742 */         if (m < this.IlI.getFontMetrics().stringWidth(this.l1l[paramInt1][b2])) {
/*      */           
/*  744 */           m = this.IlI.getFontMetrics().stringWidth(this.l1l[paramInt1][b2]);
/*  745 */           b1 = b2;
/*      */         } 
/*      */       } 
/*      */       
/*  749 */       int n = this.l11l[paramInt1] + this.lll11[paramInt1] / 2 - 3 + paramInt2;
/*  750 */       int i1 = this.l111[paramInt1] + this.l1lI[paramInt1] - 12;
/*  751 */       if (this.III1.equalsIgnoreCase("center")) {
/*      */         
/*  753 */         i1 = this.l111[paramInt1] + this.l1lI[paramInt1] - this.l1l1[paramInt1][b1] - 6;
/*  754 */         if (this.I11[paramInt1] != null)
/*      */         {
/*  756 */           i1 += this.I11[paramInt1].getWidth(this);
/*      */         }
/*  758 */         if (this.l1l[paramInt1][0] == "")
/*      */         {
/*  760 */           i1 = this.l111[paramInt1] + this.l1l1[paramInt1][b1];
/*      */         }
/*      */       } 
/*  763 */       if (this.ll1I) {
/*      */         
/*  765 */         this.IlI.setColor(new Color(k));
/*  766 */         for (byte b = 0; b < 4; b++)
/*      */         {
/*  768 */           this.IlI.drawLine(i1 + b, n + 2 + b, i1 + 6 - b, n + 2 + b);
/*      */         }
/*      */       }
/*      */       else {
/*      */         
/*  773 */         this.IlI.setColor(new Color(k));
/*  774 */         for (byte b = 0; b < 2; b++)
/*      */         {
/*  776 */           this.IlI.drawLine(i1 + 2, n + b * 4, i1 + 4, n + 2 + b * 4);
/*  777 */           this.IlI.drawLine(i1 + 4, n + 2 + b * 4, i1 + 6, n + b * 4);
/*  778 */           this.IlI.drawLine(i1 + 2, n + 1 + b * 4, i1 + 4, n + 3 + b * 4);
/*  779 */           this.IlI.drawLine(i1 + 4, n + 3 + b * 4, i1 + 6, n + 1 + b * 4);
/*      */         }
/*      */       
/*      */       } 
/*      */     } else {
/*      */       
/*  785 */       int m = this.l11l[paramInt1] + this.lll11[paramInt1] / 2 - 3 + paramInt2;
/*  786 */       if (this.ll1I) {
/*      */         
/*  788 */         this.IlI.setColor(new Color(k));
/*  789 */         for (byte b = 0; b < 4; b++)
/*      */         {
/*  791 */           this.IlI.drawLine(this.l111[paramInt1] + this.l1lI[paramInt1] - 12 + b, m + b, this.l111[paramInt1] + this.l1lI[paramInt1] - 12 + b, m + 6 - b);
/*      */         }
/*      */       }
/*      */       else {
/*      */         
/*  796 */         this.IlI.setColor(new Color(k));
/*  797 */         for (byte b = 0; b < 2; b++) {
/*      */           
/*  799 */           this.IlI.drawLine(this.l111[paramInt1] + this.l1lI[paramInt1] - 12 + b * 4, m, this.l111[paramInt1] + this.l1lI[paramInt1] - 10 + b * 4, m + 2);
/*  800 */           this.IlI.drawLine(this.l111[paramInt1] + this.l1lI[paramInt1] - 12 + b * 4, m + 4, this.l111[paramInt1] + this.l1lI[paramInt1] - 10 + b * 4, m + 2);
/*  801 */           this.IlI.drawLine(this.l111[paramInt1] + this.l1lI[paramInt1] - 13 + b * 4, m, this.l111[paramInt1] + this.l1lI[paramInt1] - 11 + b * 4, m + 2);
/*  802 */           this.IlI.drawLine(this.l111[paramInt1] + this.l1lI[paramInt1] - 13 + b * 4, m + 4, this.l111[paramInt1] + this.l1lI[paramInt1] - 11 + b * 4, m + 2);
/*      */         } 
/*      */       } 
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   public void Il1l(int paramInt1, int paramInt2) {
/*  811 */     if (this.I11[paramInt1] == null) {
/*      */       return;
/*      */     }
/*      */     
/*  815 */     if (this.llII) {
/*      */       
/*  817 */       int i = 0;
/*  818 */       byte b1 = 0;
/*  819 */       for (byte b2 = 0; b2 < this.llll1[paramInt1]; b2++) {
/*      */         
/*  821 */         if (i < this.IlI.getFontMetrics().stringWidth(this.l1l[paramInt1][b2])) {
/*      */           
/*  823 */           i = this.IlI.getFontMetrics().stringWidth(this.l1l[paramInt1][b2]);
/*  824 */           b1 = b2;
/*      */         } 
/*      */       } 
/*      */       
/*  828 */       this.IlI.drawImage(this.I11[paramInt1], this.l111[paramInt1] + this.l1l1[paramInt1][b1] - this.I11[paramInt1].getWidth(this) - 3, this.l11l[paramInt1] + (this.lll11[paramInt1] - this.I11[paramInt1].getHeight(this)) / 2 + paramInt2, this);
/*      */     } else {
/*      */       
/*  831 */       this.IlI.drawImage(this.I11[paramInt1], this.l111[paramInt1] + 3, this.l11l[paramInt1] + (this.lll11[paramInt1] - this.I11[paramInt1].getHeight(this)) / 2 + paramInt2, this);
/*      */     } 
/*      */   }
/*      */ 
/*      */   
/*      */   public synchronized void IlI1() {
/*  837 */     if (this.I1I == null) {
/*      */       return;
/*      */     }
/*      */     
/*  841 */     if (this.lllI)
/*      */     {
/*  843 */       if (this.llll) {
/*      */         
/*  845 */         lIII1();
/*  846 */         for (byte b = 0; b < this.lII; b++)
/*      */         {
/*  848 */           IllI(b);
/*      */         }
/*      */       }
/*      */       else {
/*      */         
/*  853 */         this.IlI.setColor(new Color(this.I1Il));
/*  854 */         this.IlI.drawString("Incorrect Copyright", 10, 15);
/*      */       } 
/*      */     }
/*  857 */     getGraphics().drawImage(this.I1I, 0, 0, this);
/*      */   }
/*      */ 
/*      */   
/*      */   public Image IlII(String paramString) {
/*  862 */     Image image = null;
/*  863 */     MediaTracker mediaTracker = new MediaTracker(this);
/*      */     
/*      */     try {
/*  866 */       image = getImage(getDocumentBase(), paramString);
/*  867 */       mediaTracker.addImage(image, 0);
/*  868 */       mediaTracker.waitForID(0);
/*      */     }
/*      */     catch (Exception exception) {
/*      */       
/*  872 */       return null;
/*      */     } 
/*  874 */     if (mediaTracker.isErrorID(0))
/*      */     {
/*  876 */       return null;
/*      */     }
/*      */     
/*  879 */     this.IIlI = (image.getWidth(this) <= this.IIlI) ? this.IIlI : image.getWidth(this);
/*  880 */     return image;
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   public void IlIl(int paramInt) {
/*  886 */     byte b1 = 2;
/*  887 */     byte b2 = 6;
/*  888 */     byte b3 = 6;
/*  889 */     if (this.llII) {
/*      */       
/*  891 */       for (byte b = 0; b < (this.IIl - 5) / b3; b++) {
/*      */         
/*  893 */         this.IlI.setColor(new Color(this.I111));
/*  894 */         this.IlI.drawRect(this.l111[paramInt] + b1 + 1, b2 + 1 + b * b3, 1, 1);
/*  895 */         this.IlI.setColor(new Color(this.I11l));
/*  896 */         this.IlI.drawRect(this.l111[paramInt] + b1, b2 + b * b3, 1, 1);
/*  897 */         if (this.IIl - b2 + 4 + b * b3 > 5)
/*      */         {
/*  899 */           this.IlI.setColor(new Color(this.I111));
/*  900 */           this.IlI.drawRect(this.l111[paramInt] + b1 + 4, b2 + 4 + b * b3, 1, 1);
/*  901 */           this.IlI.setColor(new Color(this.I11l));
/*  902 */           this.IlI.drawRect(this.l111[paramInt] + b1 + 3, b2 + 3 + b * b3, 1, 1);
/*      */         }
/*      */       
/*      */       } 
/*      */     } else {
/*      */       
/*  908 */       for (byte b = 0; b < (this.l1lI[paramInt] - 3) / b3; b++) {
/*      */         
/*  910 */         this.IlI.setColor(new Color(this.I111));
/*  911 */         this.IlI.drawRect(this.l111[paramInt] + b1 + 1 + b * b3, this.l11l[paramInt] + b1 + 1, 1, 1);
/*  912 */         this.IlI.drawRect(this.l111[paramInt] + b1 + 4 + b * b3, this.l11l[paramInt] + b1 + 4, 1, 1);
/*  913 */         this.IlI.setColor(new Color(this.I11l));
/*  914 */         this.IlI.drawRect(this.l111[paramInt] + b1 + b * b3, this.l11l[paramInt] + b1, 1, 1);
/*  915 */         this.IlI.drawRect(this.l111[paramInt] + b1 + b * b3 + 3, this.l11l[paramInt] + b1 + 3, 1, 1);
/*      */       } 
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   public int Ill1(int paramInt1, int paramInt2) {
/*  923 */     for (byte b = 0; b < this.lII; b++) {
/*      */       
/*  925 */       if (!this.l1l[b][0].trim().equals("-")) {
/*      */         
/*  927 */         Rectangle rectangle = new Rectangle(this.l111[b], this.l11l[b], this.l1lI[b], this.lll11[b]);
/*  928 */         if (rectangle.inside(paramInt1, paramInt2))
/*      */         {
/*  930 */           return b;
/*      */         }
/*      */       } 
/*      */     } 
/*      */     
/*  935 */     return -1;
/*      */   }
/*      */ 
/*      */   
/*      */   public void IllI(int paramInt) {
/*  940 */     if (this.l1l[paramInt][0].trim().equals("-")) {
/*      */       
/*  942 */       IlIl(paramInt);
/*      */       return;
/*      */     } 
/*  945 */     boolean bool = false;
/*  946 */     int i = 0;
/*  947 */     int j = this.I1II;
/*  948 */     int k = this.I1Il;
/*  949 */     int m = this.lllII;
/*  950 */     byte b1 = 0;
/*  951 */     if (this.l1ll == paramInt) {
/*      */       
/*  953 */       bool = true;
/*  954 */       i = this.lIII;
/*  955 */       k = this.I11I;
/*  956 */       m = this.lllI1;
/*  957 */       b1 = 6;
/*      */     } 
/*  959 */     if (this.lllIl == paramInt) {
/*      */       
/*  961 */       bool = true;
/*  962 */       i = 1;
/*  963 */       k = this.I11I;
/*  964 */       m = this.lllI1;
/*  965 */       if (this.lll1l != paramInt)
/*      */       {
/*  967 */         i = 0;
/*      */       }
/*      */     } 
/*  970 */     if (i == 0 && this.lllIl != paramInt) {
/*      */       
/*  972 */       if (this.I1ll == 0 || this.I1ll == 2 || b1 != 0)
/*      */       {
/*  974 */         if (this.I1ll == 2 || this.I1ll == 3) {
/*      */           
/*  976 */           this.IlI.setColor(new Color(this.lIlII[17]));
/*  977 */           this.IlI.fillRect(this.l111[paramInt] + 1, this.l11l[paramInt] + 2, this.l1lI[paramInt] - 2, this.lll11[paramInt] - 3);
/*  978 */           if (b1 == 0 || this.I1ll == 3) {
/*      */             
/*  980 */             int[] arrayOfInt = { 10, 37, 39, 36, 34, 32, 30, 28, 26, 23, 21, 19, 18 };
/*      */ 
/*      */ 
/*      */             
/*  984 */             lII1I(this.l111[paramInt] + 1, this.l11l[paramInt], this.l1lI[paramInt] - 3, arrayOfInt, this.lIlII, 0);
/*      */           } else {
/*      */             
/*  987 */             int[] arrayOfInt1 = { 18, 19, 21, 22, 24, 26, 28, 30, 32, 34, 36, 38, 39, 39, 39 };
/*      */ 
/*      */ 
/*      */             
/*  991 */             lII1I(this.l111[paramInt] + 2, this.l11l[paramInt] + this.lll11[paramInt] - 15, this.l1lI[paramInt] - 5, arrayOfInt1, this.lIlII, 0);
/*  992 */             int[] arrayOfInt2 = { 10, 37 };
/*      */ 
/*      */             
/*  995 */             lII1I(this.l111[paramInt] + 1, this.l11l[paramInt], this.l1lI[paramInt] - 3, arrayOfInt2, this.lIlII, 0);
/*      */           } 
/*  997 */           this.IlI.setColor(new Color(this.lIlII[10]));
/*  998 */           this.IlI.drawLine(this.l111[paramInt], this.l11l[paramInt] + 1, this.l111[paramInt], this.l11l[paramInt] + this.lll11[paramInt] - 2);
/*  999 */           this.IlI.setColor(new Color(this.lIlII[32]));
/* 1000 */           this.IlI.drawLine(this.l111[paramInt] + 1, this.l11l[paramInt] + 1, this.l111[paramInt] + 1, this.l11l[paramInt] + this.lll11[paramInt] - 3);
/* 1001 */           this.IlI.setColor(new Color(this.lIlII[15]));
/* 1002 */           this.IlI.drawLine(this.l111[paramInt] + 1, this.l11l[paramInt] + 1, this.l111[paramInt] + 1, this.l11l[paramInt] + 1);
/* 1003 */           this.IlI.setColor(new Color(this.lIlII[15]));
/* 1004 */           this.IlI.drawLine(this.l111[paramInt] + this.l1lI[paramInt] - 2, this.l11l[paramInt] + 1, this.l111[paramInt] + this.l1lI[paramInt] - 2, this.l11l[paramInt] + 1);
/* 1005 */           this.IlI.setColor(new Color(this.lIlII[32]));
/* 1006 */           this.IlI.drawLine(this.l111[paramInt] + this.l1lI[paramInt] - 2, this.l11l[paramInt] + 2, this.l111[paramInt] + this.l1lI[paramInt] - 2, this.l11l[paramInt] + this.lll11[paramInt] - 3);
/* 1007 */           this.IlI.setColor(new Color(this.lIlII[4]));
/* 1008 */           this.IlI.drawLine(this.l111[paramInt] + this.l1lI[paramInt] - 1, this.l11l[paramInt] + 1, this.l111[paramInt] + this.l1lI[paramInt] - 1, this.l11l[paramInt] + this.lll11[paramInt] - 2);
/* 1009 */           this.IlI.setColor(new Color(this.lIlII[2]));
/* 1010 */           this.IlI.drawLine(this.l111[paramInt] + 1, this.l11l[paramInt] + this.lll11[paramInt] - 1, this.l111[paramInt] + this.l1lI[paramInt] - 2, this.l11l[paramInt] + this.lll11[paramInt] - 1);
/*      */         } else {
/*      */           
/* 1013 */           this.IlI.setColor(new Color(this.lIlII[26 + b1]));
/* 1014 */           this.IlI.fillRect(this.l111[paramInt] + 2, this.l11l[paramInt] + 2, this.l1lI[paramInt] - 4, this.lll11[paramInt] - 2);
/* 1015 */           int[] arrayOfInt1 = { 17, 30, 29, 28, 27, 26, 26 };
/*      */ 
/*      */           
/* 1018 */           lII1I(this.l111[paramInt] + 2, this.l11l[paramInt], this.l1lI[paramInt] - 5, arrayOfInt1, this.lIlII, b1);
/* 1019 */           int[] arrayOfInt2 = { 22, 6 };
/*      */ 
/*      */           
/* 1022 */           lII1I(this.l111[paramInt] + 2, this.l11l[paramInt] + this.lll11[paramInt] - 2, this.l1lI[paramInt] - 5, arrayOfInt2, this.lIlII, b1);
/* 1023 */           this.IlI.setColor(new Color(this.lIlII[17 + b1]));
/* 1024 */           this.IlI.drawLine(this.l111[paramInt], this.l11l[paramInt] + 1, this.l111[paramInt], this.l11l[paramInt] + this.lll11[paramInt] - 3);
/* 1025 */           this.IlI.setColor(new Color(this.lIlII[29 + b1]));
/* 1026 */           this.IlI.drawLine(this.l111[paramInt] + 1, this.l11l[paramInt] + 2, this.l111[paramInt] + 1, this.l11l[paramInt] + this.lll11[paramInt] - 3);
/* 1027 */           lIIIl(this.l111[paramInt] + 1, this.l11l[paramInt], this.lIllI, this.lIlII, b1);
/* 1028 */           this.IlI.setColor(new Color(this.lIlII[22 + b1]));
/* 1029 */           this.IlI.drawLine(this.l111[paramInt] + this.l1lI[paramInt] - 2, this.l11l[paramInt] + 1, this.l111[paramInt] + this.l1lI[paramInt] - 2, this.l11l[paramInt] + this.lll11[paramInt] - 2);
/* 1030 */           this.IlI.setColor(new Color(this.lIlII[6 + b1]));
/* 1031 */           this.IlI.drawLine(this.l111[paramInt] + this.l1lI[paramInt] - 1, this.l11l[paramInt] + 2, this.l111[paramInt] + this.l1lI[paramInt] - 1, this.l11l[paramInt] + this.lll11[paramInt] - 3);
/* 1032 */           lIIIl(this.l111[paramInt] + this.l1lI[paramInt] - 5, this.l11l[paramInt], this.lIll1, this.lIlII, b1);
/* 1033 */           lIIIl(this.l111[paramInt] + this.l1lI[paramInt] - 5, this.l11l[paramInt] + this.lll11[paramInt] - 5, this.lIl1l, this.lIlII, b1);
/* 1034 */           lIIIl(this.l111[paramInt], this.l11l[paramInt] + this.lll11[paramInt] - 4, this.lIl11, this.lIlII, b1);
/*      */         } 
/*      */       }
/*      */     } else {
/*      */       
/* 1039 */       if (b1 == 6)
/*      */       {
/* 1041 */         b1 = 9;
/*      */       }
/* 1043 */       this.IlI.setColor(new Color(this.lIlII[14 + b1]));
/* 1044 */       this.IlI.fillRect(this.l111[paramInt] + 1, this.l11l[paramInt] + 2, this.l1lI[paramInt] - 3, this.lll11[paramInt] - 2);
/* 1045 */       int[] arrayOfInt1 = { 4, 9, 13 };
/*      */ 
/*      */       
/* 1048 */       lII1I(this.l111[paramInt] + 1, this.l11l[paramInt] + 1, this.l1lI[paramInt] - 3, arrayOfInt1, this.lIlII, b1);
/* 1049 */       int[] arrayOfInt2 = { 11, 6 };
/*      */ 
/*      */       
/* 1052 */       lII1I(this.l111[paramInt] + 1, this.l11l[paramInt] + this.lll11[paramInt] - 2, this.l1lI[paramInt] - 4, arrayOfInt2, this.lIlII, b1);
/* 1053 */       this.IlI.setColor(new Color(this.lIlII[6 + b1]));
/* 1054 */       this.IlI.drawLine(this.l111[paramInt], this.l11l[paramInt] + 2, this.l111[paramInt], this.l11l[paramInt] + this.lll11[paramInt] - 2);
/* 1055 */       this.IlI.setColor(new Color(this.lIlII[11 + b1]));
/* 1056 */       this.IlI.drawLine(this.l111[paramInt] + this.l1lI[paramInt] - 2, this.l11l[paramInt] + 3, this.l111[paramInt] + this.l1lI[paramInt] - 2, this.l11l[paramInt] + this.lll11[paramInt] - 2);
/* 1057 */       this.IlI.setColor(new Color(this.lIlII[4 + b1]));
/* 1058 */       this.IlI.drawLine(this.l111[paramInt] + this.l1lI[paramInt] - 1, this.l11l[paramInt] + 2, this.l111[paramInt] + this.l1lI[paramInt] - 1, this.l11l[paramInt] + this.lll11[paramInt] - 3);
/*      */     } 
/* 1060 */     int n = 0;
/* 1061 */     int i1 = this.I1lI;
/* 1062 */     int i2 = this.I1l1;
/* 1063 */     if (this.I1ll == 1 || this.I1ll == 3) {
/*      */       
/* 1065 */       i1 = this.I111;
/* 1066 */       i2 = this.I11l;
/*      */     } 
/* 1068 */     for (byte b2 = 0; b2 < this.llll1[paramInt]; b2++) {
/*      */       
/* 1070 */       if (m != -1) {
/*      */         
/* 1072 */         this.IlI.setColor(new Color(m));
/* 1073 */         this.IlI.drawString(this.l1l[paramInt][b2], this.l111[paramInt] + this.l1l1[paramInt][b2] + 1, this.l11l[paramInt] + this.lll1I[paramInt] + 1 + n);
/*      */       } 
/* 1075 */       if (this.llI[paramInt].equals("_")) {
/*      */         
/* 1077 */         this.IlI.setColor(new Color(i1));
/* 1078 */         this.IlI.drawString(this.l1l[paramInt][b2], this.l111[paramInt] + this.l1l1[paramInt][b2] + 1, this.l11l[paramInt] + this.lll1I[paramInt] + 1 + n);
/*      */       } 
/* 1080 */       this.IlI.setColor(new Color(k));
/* 1081 */       if (this.llI[paramInt].equals("_"))
/*      */       {
/* 1083 */         this.IlI.setColor(new Color(i2));
/*      */       }
/* 1085 */       this.IlI.drawString(this.l1l[paramInt][b2], this.l111[paramInt] + this.l1l1[paramInt][b2], this.l11l[paramInt] + this.lll1I[paramInt] + i + n);
/* 1086 */       n += this.II11 + 1;
/*      */     } 
/*      */     
/* 1089 */     Il1l(paramInt, i);
/* 1090 */     Il1I(paramInt, i, bool);
/* 1091 */     if (this.I == -1 && this.l1ll != -1)
/*      */     {
/* 1093 */       this.I = this.l1ll;
/*      */     }
/* 1095 */     if (this.I > -1)
/*      */     {
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/* 1102 */       if (this.I != this.l1ll)
/*      */       {
/* 1104 */         this.I = -2;
/*      */       }
/*      */     }
/*      */   }
/*      */ 
/*      */   
/*      */   private void Illl(Menu paramMenu, int paramInt1, int paramInt2) {
/* 1111 */     String str1 = "";
/* 1112 */     String str2 = "__menu" + paramInt1 + "_";
/* 1113 */     for (byte b = 0; b < this.Il1; b++) {
/*      */       
/* 1115 */       if (this.lll[b].indexOf(str2) != -1) {
/*      */ 
/*      */ 
/*      */         
/* 1119 */         byte b1 = 0;
/* 1120 */         byte b2 = 0;
/* 1121 */         for (StringTokenizer stringTokenizer = new StringTokenizer(this.ll1[b], "|", true); stringTokenizer.nextToken().equals("|");)
/*      */         {
/* 1123 */           b1++;
/*      */         }
/*      */         
/* 1126 */         if (paramInt2 > b1) {
/*      */           return;
/*      */         }
/*      */         
/* 1130 */         this.lll[b] = this.lll[b].substring(0, this.lll[b].indexOf(str2));
/* 1131 */         String str = this.ll1[b].substring(b1);
/* 1132 */         if (this.l11I[b] != null || this.lll[b].startsWith("javascript:")) {
/*      */           
/* 1134 */           this.lI1l[b] = new MenuItem(str);
/* 1135 */           if (this.lll[b].equals("_"))
/*      */           {
/* 1137 */             this.lI1l[b].enable(false);
/*      */           }
/* 1139 */           paramMenu.add(this.lI1l[b]);
/* 1140 */           if (!this.ll1l)
/*      */           {
/* 1142 */             paramMenu.setFont(new Font(this.IIII, this.II1l, this.II11));
/* 1143 */             this.lI1l[b].setFont(new Font(this.IIII, this.II1l, this.II11));
/*      */           }
/*      */         
/* 1146 */         } else if (str.startsWith("-")) {
/*      */           
/* 1148 */           paramMenu.addSeparator();
/*      */         } else {
/*      */           
/* 1151 */           Menu menu = new Menu(str);
/* 1152 */           if (!this.ll1l)
/*      */           {
/* 1154 */             menu.setFont(new Font(this.IIII, this.II1l, this.II11));
/*      */           }
/* 1156 */           Illl(menu, paramInt1, paramInt2 + 1);
/* 1157 */           paramMenu.add(menu);
/*      */           b++;
/*      */         } 
/* 1160 */         if (b + 1 < this.Il1 && this.lll[b + 1].indexOf("__menu" + paramInt1 + "_") != -1)
/*      */         {
/* 1162 */           for (StringTokenizer stringTokenizer1 = new StringTokenizer(this.ll1[b + 1], "|", true); stringTokenizer1.nextToken().equals("|");)
/*      */           {
/* 1164 */             b2++;
/*      */           }
/*      */         }
/*      */         
/* 1168 */         if (b2 < b1) {
/*      */           return;
/*      */         }
/*      */       } 
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   public void changeItem(String paramString1, String paramString2, String paramString3, String paramString4) {
/* 1178 */     StringTokenizer stringTokenizer = new StringTokenizer(paramString1, "_");
/* 1179 */     int i = stringTokenizer.countTokens();
/* 1180 */     byte b1 = 0;
/* 1181 */     int j = Integer.parseInt(stringTokenizer.nextToken()) - 1;
/* 1182 */     if (!stringTokenizer.hasMoreTokens()) {
/*      */       
/* 1184 */       if (j >= this.lII || this.l1l[j][0].trim().equals("-")) {
/*      */         return;
/*      */       }
/*      */       
/* 1188 */       StringTokenizer stringTokenizer1 = new StringTokenizer(paramString2, "\\");
/*      */       byte b;
/* 1190 */       for (b = 0; stringTokenizer1.hasMoreTokens(); b++)
/*      */       {
/* 1192 */         this.l1l[j][b] = stringTokenizer1.nextToken();
/*      */       }
/*      */       
/* 1195 */       this.llll1[j] = b;
/* 1196 */       if (!this.llI[j].startsWith("__menu")) {
/*      */         
/* 1198 */         if (!paramString4.equals(""))
/*      */         {
/* 1200 */           this.llI[j] = paramString4;
/*      */         }
/* 1202 */         if (!paramString3.equals(""))
/*      */         {
/* 1204 */           if (!paramString3.startsWith("javascript:")) {
/*      */ 
/*      */             
/*      */             try {
/* 1208 */               this.l1Il[j] = new URL(getDocumentBase(), paramString3);
/*      */             }
/* 1210 */             catch (Exception exception) {}
/*      */           } else {
/*      */             
/* 1213 */             this.llI[j] = paramString3;
/*      */           } 
/*      */         }
/*      */       } 
/*      */       return;
/*      */     } 
/* 1219 */     int k = Integer.parseInt(stringTokenizer.nextToken()) - 1;
/* 1220 */     b1 = 2;
/* 1221 */     MenuItem menuItem = null;
/* 1222 */     Menu menu = null;
/* 1223 */     if (this.lI11[j].getItem(k) instanceof Menu) {
/*      */       
/* 1225 */       menu = (Menu)this.lI11[j].getItem(k);
/* 1226 */       if (b1 == i) {
/*      */         
/* 1228 */         menu.setLabel(paramString2);
/*      */         
/*      */         return;
/*      */       } 
/*      */     } else {
/* 1233 */       menuItem = this.lI11[j].getItem(k);
/*      */     } 
/* 1235 */     while (stringTokenizer.hasMoreTokens()) {
/*      */       
/* 1237 */       int m = Integer.parseInt(stringTokenizer.nextToken()) - 1;
/* 1238 */       b1++;
/* 1239 */       if (menu.getItem(m) instanceof Menu) {
/*      */         
/* 1241 */         menu = (Menu)menu.getItem(m);
/* 1242 */         if (b1 == i) {
/*      */           
/* 1244 */           menu.setLabel(paramString2);
/*      */           return;
/*      */         } 
/*      */         continue;
/*      */       } 
/* 1249 */       if (b1 == i)
/*      */       {
/* 1251 */         menuItem = menu.getItem(m);
/*      */       }
/*      */       break;
/*      */     } 
/* 1255 */     menuItem.setLabel(paramString2);
/* 1256 */     for (byte b2 = 0; b2 < this.Il1; b2++) {
/*      */       
/* 1258 */       if (this.lI1l[b2] != null && this.lI1l[b2].equals(menuItem)) {
/*      */         
/* 1260 */         if (!paramString4.equals(""))
/*      */         {
/* 1262 */           this.lll[b2] = paramString4;
/*      */         }
/* 1264 */         if (!paramString3.equals(""))
/*      */         {
/* 1266 */           if (!paramString3.startsWith("javascript:")) {
/*      */ 
/*      */             
/*      */             try {
/* 1270 */               this.l11I[b2] = new URL(getDocumentBase(), paramString3);
/*      */             }
/* 1272 */             catch (Exception exception) {}
/*      */           } else {
/*      */             
/* 1275 */             this.lll[b2] = paramString3;
/*      */           } 
/*      */         }
/* 1278 */         if (this.lll[b2].equals("_")) {
/*      */           
/* 1280 */           this.lI1l[b2].enable(false);
/*      */         } else {
/*      */           
/* 1283 */           this.lI1l[b2].enable(true);
/*      */         } 
/*      */       } 
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean handleEvent(Event paramEvent) {
/* 1292 */     if (!this.lllI)
/*      */     {
/* 1294 */       return true;
/*      */     }
/* 1296 */     if (paramEvent.id == 1004 || paramEvent.id == 1005)
/*      */     {
/* 1298 */       return true;
/*      */     }
/* 1300 */     int i = Ill1(paramEvent.x, paramEvent.y);
/* 1301 */     if (i == -1 || this.llI[i].equals("_"))
/*      */     {
/* 1303 */       i = -1;
/*      */     }
/*      */     
/*      */     try {
/* 1307 */       if (paramEvent.id == 1001) {
/*      */         
/* 1309 */         if (this.lll1l > -1) {
/*      */           
/* 1311 */           this.lllIl = this.lll1l;
/* 1312 */           this.lll1l = -2;
/*      */         } 
/* 1314 */         for (byte b = 0; b < this.Il1; b++) {
/*      */           
/* 1316 */           if (this.lI1l[b] != null && this.lI1l[b].equals(paramEvent.target) && !this.lll[b].equals("_")) {
/*      */             
/* 1318 */             this.lI1lI = this.l11I[b];
/* 1319 */             this.lI11l = this.lll[b];
/*      */           } 
/*      */         } 
/*      */       } 
/*      */       
/* 1324 */       if (i == -1 || paramEvent.id == 505) {
/*      */         
/* 1326 */         this.l1ll = -1;
/* 1327 */         if (this.lll1l > -1 && (!this.llI[this.lll1l].startsWith("__menu") || this.lllIl == this.lll1l)) {
/*      */           
/* 1329 */           this.lllIl = this.lll1l;
/* 1330 */           this.lll1l = -2;
/*      */         } 
/* 1332 */         if (this.lIll != null)
/*      */         {
/* 1334 */           this.lIll.setCursor(0);
/*      */         }
/* 1336 */         IlI1();
/* 1337 */         return true;
/*      */       } 
/* 1339 */       if (paramEvent.id == 503) {
/*      */         
/* 1341 */         if (!this.lll1)
/*      */         {
/* 1343 */           return true;
/*      */         }
/* 1345 */         this.lll1 = false;
/* 1346 */         if (i != this.l1ll) {
/*      */           
/* 1348 */           if (this.lll1l > -1 && (!this.llI[this.lll1l].startsWith("__menu") || this.lllIl == this.lll1l)) {
/*      */             
/* 1350 */             this.lllIl = this.lll1l;
/* 1351 */             this.lll1l = -2;
/*      */           } 
/* 1353 */           if (this.lIll != null)
/*      */           {
/* 1355 */             this.lIll.setCursor(12);
/*      */           }
/* 1357 */           this.lIII = 0;
/* 1358 */           this.l1ll = i;
/* 1359 */           IlI1();
/* 1360 */           if (this.lIlI != null)
/*      */           {
/* 1362 */             this.lIlI.play();
/*      */           }
/* 1364 */           if (this.l1I1 == null) {
/*      */             
/* 1366 */             if (this.IIIl.equalsIgnoreCase("link") && !this.llI[i].startsWith("__menu") && this.l1Il[i] != null) {
/*      */               
/* 1368 */               showStatus(this.l1Il[i].toString());
/*      */             } else {
/*      */               
/* 1371 */               showStatus(this.l1l[i][0].trim());
/*      */             } 
/*      */           } else {
/*      */             
/* 1375 */             showStatus(this.l1I1);
/*      */           } 
/* 1377 */           if (this.llIl && this.llI[i].startsWith("__menu")) {
/*      */             
/* 1379 */             paramEvent.id = 501;
/* 1380 */             this.lll1 = true;
/*      */           } 
/*      */         } 
/*      */       } 
/* 1384 */       if (paramEvent.id == 501)
/*      */       {
/* 1386 */         if (!this.lll1)
/*      */         {
/* 1388 */           return true;
/*      */         }
/* 1390 */         this.lll1 = false;
/* 1391 */         this.l1ll = i;
/* 1392 */         this.lIII = 1;
/* 1393 */         if (this.lllIl > -2) {
/*      */           
/* 1395 */           this.lll1l = i;
/* 1396 */           if (!this.llI[i].startsWith("__menu"))
/*      */           {
/* 1398 */             this.lllIl = -1;
/*      */           }
/*      */         } 
/* 1401 */         IlI1();
/* 1402 */         this.lI1lI = this.l1Il[i];
/* 1403 */         this.lI11l = this.llI[i];
/* 1404 */         this.lI1l1 = i;
/* 1405 */         if (this.l1I1 == null)
/*      */         {
/* 1407 */           showStatus(this.l1l[i][0].trim());
/*      */         }
/*      */       }
/*      */     
/* 1411 */     } catch (Throwable throwable) {}
/* 1412 */     this.lll1 = true;
/* 1413 */     return true;
/*      */   }
/*      */ 
/*      */   
/*      */   int[] lII11(int paramInt) {
/* 1418 */     int[] arrayOfInt = new int[this.llIIl * 2 + 1];
/* 1419 */     int i = (paramInt & 0xFF0000) >> 16;
/* 1420 */     int j = (paramInt & 0xFF00) >> 8;
/* 1421 */     int k = paramInt & 0xFF;
/* 1422 */     int m = i / 3 / this.llIIl;
/* 1423 */     int n = j / 3 / this.llIIl;
/* 1424 */     int i1 = k / 3 / this.llIIl;
/* 1425 */     int i2 = (255 - i) / 3 * 2 / this.llIIl;
/* 1426 */     int i3 = (255 - j) / 3 * 2 / this.llIIl;
/* 1427 */     int i4 = (255 - k) / 3 * 2 / this.llIIl;
/* 1428 */     for (byte b1 = 0; b1 < this.llIIl + 1; b1++)
/*      */     {
/* 1430 */       arrayOfInt[this.llIIl - b1] = (IIll(i - m * b1) << 16) + (IIll(j - n * b1) << 8) + IIll(k - i1 * b1);
/*      */     }
/*      */     
/* 1433 */     for (byte b2 = 0; b2 < this.llIIl + 1; b2++)
/*      */     {
/* 1435 */       arrayOfInt[this.llIIl + b2] = (IIll(i + i2 * b2) << 16) + (IIll(j + i3 * b2) << 8) + IIll(k + i4 * b2);
/*      */     }
/*      */     
/* 1438 */     return arrayOfInt;
/*      */   }
/*      */ 
/*      */   
/*      */   public void lII1I(int paramInt1, int paramInt2, int paramInt3, int[] paramArrayOfint1, int[] paramArrayOfint2, int paramInt4) {
/* 1443 */     for (byte b = 0; b < paramArrayOfint1.length; b++) {
/*      */       
/* 1445 */       int i = paramArrayOfint1[b] + paramInt4;
/* 1446 */       if (i >= paramArrayOfint2.length)
/*      */       {
/* 1448 */         i = paramArrayOfint2.length - 1;
/*      */       }
/* 1450 */       if (i > paramInt4 - 1) {
/*      */         
/* 1452 */         this.IlI.setColor(new Color(paramArrayOfint2[i]));
/* 1453 */         this.IlI.drawLine(paramInt1, paramInt2 + b, paramInt1 + paramInt3, paramInt2 + b);
/*      */       } 
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   int lII1l(String paramString, int paramInt1, int paramInt2) {
/* 1461 */     paramString = getParameter(paramString);
/* 1462 */     if (paramString != null)
/*      */     {
/* 1464 */       if (paramString.equalsIgnoreCase("system")) {
/*      */         
/* 1466 */         paramInt2 = -999;
/*      */       } else {
/*      */         
/* 1469 */         if (paramString.startsWith("#"))
/*      */         {
/* 1471 */           paramString = paramString.substring(1);
/*      */         }
/* 1473 */         paramInt2 = Integer.parseInt(paramString, paramInt1);
/*      */       } 
/*      */     }
/* 1476 */     return paramInt2;
/*      */   }
/*      */ 
/*      */   
/*      */   public void lIII1() {
/* 1481 */     this.IlI.setColor(new Color(this.I1II));
/* 1482 */     this.IlI.fillRect(0, 0, this.II1, this.IIl);
/* 1483 */     if (this.lIIlI) {
/*      */       
/* 1485 */       int[] arrayOfInt1 = { 17, 28, 30, 26, 22, 18, 18, 17, 17, 17, 17, 18, 19, 19 };
/*      */ 
/*      */ 
/*      */       
/* 1489 */       lII1I(0, 0, this.II1, arrayOfInt1, this.lIIll, 0);
/* 1490 */       int[] arrayOfInt2 = { 19, 18, 17, 11, 4 };
/*      */ 
/*      */       
/* 1493 */       lII1I(0, this.IIl - 5, this.II1, arrayOfInt2, this.lIIll, 0);
/* 1494 */       this.IlI.setColor(new Color(this.lIIll[17]));
/* 1495 */       this.IlI.drawLine(0, 0, 0, this.IIl);
/* 1496 */       this.IlI.setColor(new Color(this.lIIll[28]));
/* 1497 */       this.IlI.drawLine(1, 3, 1, this.IIl - 2);
/* 1498 */       this.IlI.setColor(new Color(this.lIIll[22]));
/* 1499 */       this.IlI.drawLine(2, 5, 2, this.IIl - 3);
/* 1500 */       this.IlI.setColor(new Color(this.lIIll[0]));
/* 1501 */       this.IlI.drawLine(this.II1 - 1, 1, this.II1 - 1, this.IIl - 2);
/*      */     } 
/*      */   }
/*      */ 
/*      */   
/*      */   public void lIIIl(int paramInt1, int paramInt2, int[][] paramArrayOfint, int[] paramArrayOfint1, int paramInt3) {
/* 1507 */     for (byte b = 0; b < paramArrayOfint.length; b++) {
/*      */       
/* 1509 */       for (byte b1 = 0; b1 < (paramArrayOfint[b]).length; b1++) {
/*      */         
/* 1511 */         int i = paramArrayOfint[b][b1] + paramInt3;
/* 1512 */         if (i >= paramArrayOfint1.length)
/*      */         {
/* 1514 */           i = paramArrayOfint1.length - 1;
/*      */         }
/* 1516 */         if (i > paramInt3 - 1) {
/*      */           
/* 1518 */           this.IlI.setColor(new Color(paramArrayOfint1[i]));
/* 1519 */           this.IlI.drawLine(paramInt1 + b1, paramInt2 + b, paramInt1 + b1, paramInt2 + b);
/*      */         } 
/*      */       } 
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void paint(Graphics paramGraphics) {
/* 1529 */     IlI1();
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   public void run() {
/*      */     while (true) {
/* 1536 */       if (!this.lllI) {
/*      */         
/*      */         try {
/*      */           
/* 1540 */           IIl1();
/* 1541 */           this.lllI = true;
/* 1542 */           if (this.lIll != null)
/*      */           {
/* 1544 */             this.lIll.setCursor(0);
/*      */           }
/* 1546 */           IlI1();
/*      */         }
/* 1548 */         catch (Exception exception) {}
/*      */       }
/*      */       
/*      */       try {
/* 1552 */         if (!this.lI11l.equals("_")) {
/*      */           
/* 1554 */           IlI1();
/* 1555 */           if (this.lIl1 != null)
/*      */           {
/* 1557 */             this.lIl1.play();
/*      */           }
/* 1559 */           if (this.lI11l.startsWith("__menu")) {
/*      */             
/* 1561 */             this.lI11l = "_";
/* 1562 */             if (this.llII) {
/*      */               
/* 1564 */               this.lI11[this.lI1l1].show(this, this.l111[this.lI1l1], this.l11l[this.lI1l1] + this.lll11[this.lI1l1]);
/*      */             } else {
/*      */               
/* 1567 */               this.lI11[this.lI1l1].show(this, this.l111[this.lI1l1] + this.l1lI[this.lI1l1], this.l11l[this.lI1l1]);
/*      */             } 
/*      */           } else {
/*      */             
/* 1571 */             if (!this.lI11l.startsWith("javascript:")) {
/*      */               
/* 1573 */               if (this.lI1lI != null)
/*      */               {
/* 1575 */                 getAppletContext().showDocument(this.lI1lI, this.lI11l);
/*      */               }
/*      */             } else {
/*      */               
/* 1579 */               II(this.lI11l);
/*      */             } 
/* 1581 */             this.lI11l = "_";
/*      */           } 
/* 1583 */           IlI1();
/*      */           continue;
/*      */         } 
/* 1586 */         Thread.sleep(200L);
/* 1587 */         IlI1();
/*      */       
/*      */       }
/* 1590 */       catch (Exception exception) {}
/*      */     } 
/*      */   }
/*      */ 
/*      */   
/*      */   public void setPressedItem(int paramInt) {
/* 1596 */     this.lllIl = paramInt - 1;
/*      */   }
/*      */ 
/*      */   
/*      */   public void start() {
/* 1601 */     if (this.III == null) {
/*      */       
/* 1603 */       this.III = new Thread(this);
/* 1604 */       this.III.start();
/*      */     } 
/*      */   }
/*      */ 
/*      */   
/*      */   public void stop() {
/* 1610 */     if (this.III != null) {
/*      */       
/* 1612 */       this.III.stop();
/* 1613 */       this.III = null;
/*      */     } 
/*      */   }
/*      */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/Menu/apXPDropDown.class
 * Java compiler version: 1 (45.3)
 * JD-Core Version:       1.1.3
 */