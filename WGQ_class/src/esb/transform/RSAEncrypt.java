/*    */ package esb.transform;
/*    */ 
/*    */ import java.util.Map;
/*    */ import weaver.general.Util;
/*    */ import weaver.rsa.security.RSA;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class RSAEncrypt
/*    */ {
/*    */   private Map allParams;
/*    */   
/*    */   public String execute(Map<String, String> paramMap) {
/* 36 */     String str1 = paramMap.get("spk");
/* 37 */     String str2 = paramMap.get("source");
/* 38 */     String str3 = Util.null2String(paramMap.get("charset"), "utf-8");
/*    */     
/* 40 */     RSA rSA = new RSA();
/* 41 */     return rSA.encrypt(null, str2, null, str3, str1, false);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/esb/transform/RSAEncrypt.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */