/*    */ package esb.transform;
/*    */ 
/*    */ import com.alibaba.fastjson.JSONArray;
/*    */ import java.util.Arrays;
/*    */ import java.util.List;
/*    */ import java.util.Map;
/*    */ import java.util.stream.Collectors;
/*    */ import org.apache.commons.lang.StringUtils;
/*    */ import weaver.general.Util;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class String2Ary
/*    */ {
/*    */   private Map allParams;
/*    */   
/*    */   public String execute(Map<String, String> paramMap) {
/* 39 */     String str1 = Util.null2String(paramMap.get("source"));
/* 40 */     String str2 = escapeExprSpecialWord(Util.null2String(paramMap.get("split")));
/* 41 */     String str3 = Util.null2String(paramMap.get("filterEmpty"));
/*    */ 
/*    */     
/* 44 */     List<String> list = Arrays.asList(str1.split(str2, -1));
/*    */     
/* 46 */     if (StringUtils.isNotBlank(str3) && "1".equalsIgnoreCase(str3))
/*    */     {
/*    */       
/* 49 */       list = (List<String>)list.stream().filter(paramString -> StringUtils.isNotBlank(paramString)).collect(Collectors.toList());
/*    */     }
/*    */     
/* 52 */     JSONArray jSONArray = new JSONArray();
/* 53 */     jSONArray.addAll(list);
/*    */     
/* 55 */     return jSONArray.toJSONString();
/*    */   }
/*    */ 
/*    */   
/*    */   public static String escapeExprSpecialWord(String paramString) {
/* 60 */     if (StringUtils.isNotBlank(paramString)) {
/* 61 */       String[] arrayOfString = { "\\", "$", "(", ")", "*", "+", ".", "[", "]", "?", "^", "{", "}", "|" };
/* 62 */       for (String str : arrayOfString) {
/* 63 */         if (paramString.contains(str)) {
/* 64 */           paramString = paramString.replace(str, "\\" + str);
/*    */         }
/*    */       } 
/*    */     } 
/* 68 */     return paramString;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/esb/transform/String2Ary.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */