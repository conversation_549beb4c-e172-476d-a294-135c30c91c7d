/*     */ package dk.itst.oiosaml.sp;
/*     */ 
/*     */ import dk.itst.oiosaml.configuration.SAMLConfigurationFactory;
/*     */ import dk.itst.oiosaml.error.InvalidCertificateException;
/*     */ import dk.itst.oiosaml.error.WrappedException;
/*     */ import dk.itst.oiosaml.security.CredentialRepository;
/*     */ import dk.itst.oiosaml.sp.metadata.IdpMetadata;
/*     */ import dk.itst.oiosaml.sp.metadata.SPMetadata;
/*     */ import dk.itst.oiosaml.sp.model.OIOAssertion;
/*     */ import dk.itst.oiosaml.sp.model.OIOAttributeQuery;
/*     */ import dk.itst.oiosaml.sp.service.util.HttpSOAPClient;
/*     */ import dk.itst.oiosaml.sp.service.util.SOAPClient;
/*     */ import dk.itst.oiosaml.sp.util.AttributeUtil;
/*     */ import java.io.IOException;
/*     */ import java.security.KeyStoreException;
/*     */ import java.security.NoSuchAlgorithmException;
/*     */ import java.security.cert.CertificateException;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Collection;
/*     */ import org.opensaml.saml2.core.Attribute;
/*     */ import org.opensaml.saml2.core.AttributeStatement;
/*     */ import org.opensaml.xml.security.credential.Credential;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class UserAttributeQuery
/*     */ {
/*  54 */   private static final CredentialRepository credentialRepository = new CredentialRepository();
/*     */   
/*     */   private final String username;
/*     */   private final String password;
/*     */   private final SOAPClient client;
/*     */   private final Credential credential;
/*     */   private final boolean ignoreCertPath;
/*     */   private final boolean requireEncryption;
/*     */   private final IdpMetadata.Metadata idpMetadata;
/*     */   private final String spEntityId;
/*     */   
/*     */   public UserAttributeQuery() throws WrappedException, NoSuchAlgorithmException, CertificateException, IllegalStateException, KeyStoreException, IOException {
/*  66 */     this(SAMLConfigurationFactory.getConfiguration().getSystemConfiguration()
/*  67 */         .getString("oiosaml-sp.resolve.username", null), SAMLConfigurationFactory.getConfiguration()
/*  68 */         .getSystemConfiguration().getString("oiosaml-sp.resolve.password", null));
/*     */   }
/*     */ 
/*     */   
/*     */   public UserAttributeQuery(String paramString1, String paramString2) throws WrappedException, NoSuchAlgorithmException, CertificateException, IllegalStateException, KeyStoreException, IOException {
/*  73 */     this((UserAssertionHolder.get() != null) ? UserAssertionHolder.get().getIssuer() : null, paramString1, paramString2);
/*     */   }
/*     */ 
/*     */   
/*     */   public UserAttributeQuery(String paramString1, String paramString2, String paramString3) throws WrappedException, NoSuchAlgorithmException, CertificateException, IllegalStateException, KeyStoreException, IOException {
/*  78 */     this(IdpMetadata.getInstance().getMetadata(paramString1), paramString2, paramString3, (SOAPClient)new HttpSOAPClient(), (Credential)credentialRepository
/*  79 */         .getCredential(
/*  80 */           SAMLConfigurationFactory.getConfiguration().getKeystore(), 
/*  81 */           SAMLConfigurationFactory.getConfiguration().getSystemConfiguration()
/*  82 */           .getString("oiosaml-sp.certificate.password")), 
/*  83 */         SAMLConfigurationFactory.getConfiguration().getSystemConfiguration().getBoolean("oiosaml-sp.resolve.ignorecert", false), 
/*  84 */         SAMLConfigurationFactory.getConfiguration().getSystemConfiguration()
/*  85 */         .getBoolean("oiosaml-sp.encryption.force", true), SPMetadata.getInstance().getEntityID());
/*     */   }
/*     */ 
/*     */   
/*     */   public UserAttributeQuery(IdpMetadata.Metadata paramMetadata, String paramString1, String paramString2, SOAPClient paramSOAPClient, Credential paramCredential, boolean paramBoolean1, boolean paramBoolean2, String paramString3) {
/*  90 */     this.spEntityId = paramString3;
/*  91 */     if (paramMetadata == null)
/*  92 */       throw new IllegalArgumentException("IdP Metadata cannot be null"); 
/*  93 */     this.idpMetadata = paramMetadata;
/*  94 */     this.username = paramString1;
/*  95 */     this.password = paramString2;
/*  96 */     this.client = paramSOAPClient;
/*  97 */     this.credential = paramCredential;
/*  98 */     this.ignoreCertPath = paramBoolean1;
/*  99 */     this.requireEncryption = paramBoolean2;
/*     */   }
/*     */ 
/*     */   
/*     */   public Collection<UserAttribute> query(String paramString, NameIDFormat paramNameIDFormat, String... paramVarArgs) throws InvalidCertificateException, IOException {
/* 104 */     UserAttribute[] arrayOfUserAttribute = new UserAttribute[paramVarArgs.length];
/* 105 */     for (byte b = 0; b < paramVarArgs.length; b++) {
/* 106 */       arrayOfUserAttribute[b] = UserAttribute.create(paramVarArgs[b], null);
/*     */     }
/* 108 */     return query(paramString, paramNameIDFormat, arrayOfUserAttribute);
/*     */   }
/*     */ 
/*     */   
/*     */   public Collection<UserAttribute> query(String paramString, NameIDFormat paramNameIDFormat, UserAttribute... paramVarArgs) throws InvalidCertificateException, IOException {
/* 113 */     OIOAttributeQuery oIOAttributeQuery = OIOAttributeQuery.newQuery(this.idpMetadata
/* 114 */         .getAttributeQueryServiceLocation("urn:oasis:names:tc:SAML:2.0:bindings:SOAP"), paramString, paramNameIDFormat, this.spEntityId);
/*     */     
/* 116 */     for (UserAttribute userAttribute : paramVarArgs) {
/* 117 */       oIOAttributeQuery.addAttribute(userAttribute.getName(), userAttribute.getFormat());
/*     */     }
/* 119 */     OIOAssertion oIOAssertion = oIOAttributeQuery.executeQuery(this.client, this.credential, this.username, this.password, this.ignoreCertPath, this.idpMetadata
/* 120 */         .getValidCertificates(), !this.requireEncryption);
/* 121 */     ArrayList<UserAttribute> arrayList = new ArrayList();
/* 122 */     for (AttributeStatement attributeStatement : oIOAssertion.getAssertion().getAttributeStatements()) {
/* 123 */       for (Attribute attribute : attributeStatement.getAttributes()) {
/* 124 */         arrayList.add(new UserAttribute(attribute.getName(), attribute.getFriendlyName(), 
/* 125 */               AttributeUtil.extractAttributeValueValues(attribute), attribute.getNameFormat()));
/*     */       }
/*     */     } 
/* 128 */     return arrayList;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/dk/itst/oiosaml/sp/UserAttributeQuery.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */