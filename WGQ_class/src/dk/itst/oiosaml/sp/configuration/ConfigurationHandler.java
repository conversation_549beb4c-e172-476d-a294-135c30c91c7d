/*     */ package dk.itst.oiosaml.sp.configuration;
/*     */ 
/*     */ import dk.itst.oiosaml.common.SAMLUtil;
/*     */ import dk.itst.oiosaml.configuration.FileConfiguration;
/*     */ import dk.itst.oiosaml.configuration.SAMLConfigurationFactory;
/*     */ import dk.itst.oiosaml.error.Layer;
/*     */ import dk.itst.oiosaml.error.WrappedException;
/*     */ import dk.itst.oiosaml.logging.Logger;
/*     */ import dk.itst.oiosaml.logging.LoggerFactory;
/*     */ import dk.itst.oiosaml.security.CredentialRepository;
/*     */ import dk.itst.oiosaml.security.SecurityHelper;
/*     */ import dk.itst.oiosaml.sp.service.RequestContext;
/*     */ import dk.itst.oiosaml.sp.service.SAMLHandler;
/*     */ import java.io.ByteArrayInputStream;
/*     */ import java.io.ByteArrayOutputStream;
/*     */ import java.io.File;
/*     */ import java.io.FileInputStream;
/*     */ import java.io.FileNotFoundException;
/*     */ import java.io.FileOutputStream;
/*     */ import java.io.IOException;
/*     */ import java.io.StringWriter;
/*     */ import java.security.KeyPair;
/*     */ import java.security.KeyStore;
/*     */ import java.security.cert.Certificate;
/*     */ import java.security.cert.X509Certificate;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import java.util.zip.ZipEntry;
/*     */ import java.util.zip.ZipInputStream;
/*     */ import java.util.zip.ZipOutputStream;
/*     */ import javax.servlet.ServletException;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import org.apache.commons.fileupload.FileItem;
/*     */ import org.apache.commons.fileupload.FileItemFactory;
/*     */ import org.apache.commons.fileupload.FileUploadException;
/*     */ import org.apache.commons.fileupload.disk.DiskFileItemFactory;
/*     */ import org.apache.commons.fileupload.servlet.ServletFileUpload;
/*     */ import org.apache.commons.io.FileUtils;
/*     */ import org.apache.commons.io.IOUtils;
/*     */ import org.apache.velocity.VelocityContext;
/*     */ import org.apache.velocity.app.VelocityEngine;
/*     */ import org.apache.velocity.context.Context;
/*     */ import org.opensaml.saml2.metadata.AttributeConsumingService;
/*     */ import org.opensaml.saml2.metadata.ContactPerson;
/*     */ import org.opensaml.saml2.metadata.ContactPersonTypeEnumeration;
/*     */ import org.opensaml.saml2.metadata.EntityDescriptor;
/*     */ import org.opensaml.saml2.metadata.KeyDescriptor;
/*     */ import org.opensaml.saml2.metadata.NameIDFormat;
/*     */ import org.opensaml.saml2.metadata.SPSSODescriptor;
/*     */ import org.opensaml.xml.Configuration;
/*     */ import org.opensaml.xml.XMLObject;
/*     */ import org.opensaml.xml.security.SecurityException;
/*     */ import org.opensaml.xml.security.SecurityHelper;
/*     */ import org.opensaml.xml.security.credential.Credential;
/*     */ import org.opensaml.xml.security.credential.UsageType;
/*     */ import org.opensaml.xml.security.keyinfo.KeyInfoGenerator;
/*     */ import org.opensaml.xml.security.x509.BasicX509Credential;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class ConfigurationHandler
/*     */   implements SAMLHandler
/*     */ {
/*     */   public static final String SESSION_CONFIGURATION = "CONFIGURATION";
/* 110 */   private static final Logger log = LoggerFactory.getLogger(ConfigurationHandler.class);
/*     */   protected final VelocityEngine engine;
/*     */   
/*     */   public ConfigurationHandler() {
/* 114 */     this.engine = new VelocityEngine();
/* 115 */     this.engine.setProperty("resource.loader", "classpath");
/* 116 */     this.engine.setProperty("classpath.resource.loader.class", "org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader");
/*     */     try {
/* 118 */       this.engine.init();
/*     */     }
/* 120 */     catch (Exception exception) {
/* 121 */       log.error("Unable to initialize Velocity", exception);
/* 122 */       throw new WrappedException(Layer.BUSINESS, exception);
/*     */     } 
/*     */   }
/*     */   
/*     */   public void handleGet(RequestContext paramRequestContext) throws ServletException, IOException {
/* 127 */     HttpServletRequest httpServletRequest = paramRequestContext.getRequest();
/* 128 */     HttpServletResponse httpServletResponse = paramRequestContext.getResponse();
/* 129 */     if (httpServletRequest.getParameter("download") != null) {
/* 130 */       byte[] arrayOfByte = (byte[])httpServletRequest.getSession().getAttribute("CONFIGURATION");
/* 131 */       if (arrayOfByte == null) {
/* 132 */         httpServletResponse.sendError(404, "No configuration available for download");
/*     */         return;
/*     */       } 
/* 135 */       httpServletResponse.setContentType("application/octet-stream");
/* 136 */       httpServletResponse.setContentLength(arrayOfByte.length);
/* 137 */       httpServletResponse.addHeader("Content-disposition", "attachment; filename=oiosaml.java-config.zip");
/* 138 */       httpServletResponse.getOutputStream().write(arrayOfByte);
/*     */       return;
/*     */     } 
/* 141 */     if (!checkConfiguration(httpServletResponse)) {
/*     */       return;
/*     */     }
/* 144 */     Map<String, Object> map = getStandardParameters(httpServletRequest);
/*     */     
/* 146 */     String str = renderTemplate("configure.vm", map, true);
/* 147 */     sendResponse(httpServletResponse, str);
/*     */   }
/*     */   public void handlePost(RequestContext paramRequestContext) throws ServletException, IOException {
/*     */     BasicX509Credential basicX509Credential;
/* 151 */     HttpServletRequest httpServletRequest = paramRequestContext.getRequest();
/* 152 */     HttpServletResponse httpServletResponse = paramRequestContext.getResponse();
/*     */     
/* 154 */     if (!checkConfiguration(httpServletResponse)) {
/*     */       return;
/*     */     }
/* 157 */     List<?> list = extractParameterList(httpServletRequest);
/*     */     
/* 159 */     String str1 = extractParameter("organisationName", list);
/* 160 */     String str2 = extractParameter("organisationUrl", list);
/* 161 */     String str3 = extractParameter("email", list);
/* 162 */     String str4 = extractParameter("entityId", list);
/* 163 */     String str5 = extractParameter("keystorePassword", list);
/* 164 */     byte[] arrayOfByte1 = extractFile("metadata", list).get();
/* 165 */     FileItem fileItem = extractFile("keystore", list);
/* 166 */     byte[] arrayOfByte2 = null;
/* 167 */     if (fileItem != null) {
/* 168 */       arrayOfByte2 = fileItem.get();
/*     */     }
/* 170 */     if (!checkNotNull(new Object[] { str1, str2, str3, str5, arrayOfByte1, str4 }) || arrayOfByte1.length == 0 || (arrayOfByte2 == null && !Boolean.valueOf(extractParameter("createkeystore", list)).booleanValue())) {
/* 171 */       Map<String, Object> map = getStandardParameters(httpServletRequest);
/* 172 */       map.put("error", "All fields must be filled.");
/* 173 */       map.put("organisationName", str1);
/* 174 */       map.put("organisationUrl", str2);
/* 175 */       map.put("email", str3);
/* 176 */       map.put("keystorePassword", str5);
/* 177 */       map.put("entityId", str4);
/* 178 */       log.info("Parameters not correct: " + map);
/* 179 */       log.info("Metadata: " + new String(arrayOfByte1));
/*     */       
/* 181 */       String str = renderTemplate("configure.vm", map, true);
/* 182 */       sendResponse(httpServletResponse, str);
/*     */       
/*     */       return;
/*     */     } 
/* 186 */     Credential credential = paramRequestContext.getCredential();
/* 187 */     if (arrayOfByte2 != null && arrayOfByte2.length > 0) {
/* 188 */       ByteArrayInputStream byteArrayInputStream = null;
/*     */       try {
/* 190 */         byteArrayInputStream = new ByteArrayInputStream(arrayOfByte2);
/*     */         
/* 192 */         KeyStore keyStore = KeyStore.getInstance("JKS");
/* 193 */         keyStore.load(byteArrayInputStream, str5.toCharArray());
/* 194 */         basicX509Credential = CredentialRepository.createCredential(keyStore, str5);
/*     */       }
/* 196 */       catch (Exception exception) {
/* 197 */         log.info("Keystore is not of type JKS. Trying type PKCS12");
/*     */         try {
/* 199 */           KeyStore keyStore = KeyStore.getInstance("PKCS12");
/* 200 */           byteArrayInputStream.reset();
/* 201 */           keyStore.load(byteArrayInputStream, str5.toCharArray());
/* 202 */           basicX509Credential = CredentialRepository.createCredential(keyStore, str5);
/*     */         }
/* 204 */         catch (Exception exception1) {
/* 205 */           log.error("Unable to use/load keystore", exception1);
/* 206 */           throw new RuntimeException("Unable to use/load keystore", exception1);
/*     */         } 
/*     */       } finally {
/*     */         
/* 210 */         if (byteArrayInputStream != null) {
/* 211 */           byteArrayInputStream.close();
/*     */         }
/*     */       } 
/* 214 */     } else if (Boolean.valueOf(extractParameter("createkeystore", list)).booleanValue()) {
/*     */       try {
/* 216 */         BasicX509Credential basicX509Credential1 = new BasicX509Credential();
/* 217 */         KeyPair keyPair = SecurityHelper.generateKeyPairFromURI("http://www.w3.org/2001/04/xmlenc#rsa-1_5", 1024);
/* 218 */         basicX509Credential1.setPrivateKey(keyPair.getPrivate());
/* 219 */         basicX509Credential1.setPublicKey(keyPair.getPublic());
/* 220 */         basicX509Credential = basicX509Credential1;
/*     */         
/* 222 */         KeyStore keyStore = KeyStore.getInstance("JKS");
/* 223 */         keyStore.load(null, null);
/* 224 */         X509Certificate x509Certificate = SecurityHelper.generateCertificate((Credential)basicX509Credential, getEntityId(httpServletRequest));
/* 225 */         basicX509Credential1.setEntityCertificate(x509Certificate);
/*     */         
/* 227 */         keyStore.setKeyEntry("oiosaml", basicX509Credential.getPrivateKey(), str5.toCharArray(), new Certificate[] { x509Certificate });
/* 228 */         ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
/* 229 */         keyStore.store(byteArrayOutputStream, str5.toCharArray());
/*     */         
/* 231 */         arrayOfByte2 = byteArrayOutputStream.toByteArray();
/* 232 */         byteArrayOutputStream.close();
/*     */       }
/* 234 */       catch (Exception exception) {
/* 235 */         log.error("Unable to generate credential", exception);
/* 236 */         throw new RuntimeException("Unable to generate credential", exception);
/*     */       } 
/*     */     } 
/*     */     
/* 240 */     EntityDescriptor entityDescriptor = generateSPDescriptor(getBaseUrl(httpServletRequest), str4, (Credential)basicX509Credential, str1, str2, str3, Boolean.valueOf(extractParameter("enableArtifact", list)).booleanValue(), Boolean.valueOf(extractParameter("enablePost", list)).booleanValue(), 
/* 241 */         Boolean.valueOf(extractParameter("enableSoap", list)).booleanValue(), Boolean.valueOf(extractParameter("enablePostSLO", list)).booleanValue(), Boolean.valueOf(extractParameter("supportOCESAttributeProfile", list)).booleanValue());
/* 242 */     File file = generateZipFile(httpServletRequest.getContextPath(), str5, arrayOfByte1, arrayOfByte2, entityDescriptor);
/*     */     
/* 244 */     byte[] arrayOfByte3 = saveConfigurationInSession(httpServletRequest, file);
/* 245 */     boolean bool = writeConfiguration(getHome(), arrayOfByte3);
/*     */     
/* 247 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 248 */     hashMap.put("home", getHome());
/* 249 */     hashMap.put("written", Boolean.valueOf(bool));
/* 250 */     sendResponse(httpServletResponse, renderTemplate("done.vm", (Map)hashMap, true));
/*     */   }
/*     */   
/*     */   public boolean writeConfiguration(String paramString, byte[] paramArrayOfbyte) {
/* 254 */     File file = new File(paramString);
/* 255 */     if (!file.isDirectory() || !file.canWrite()) {
/* 256 */       return false;
/*     */     }
/* 258 */     boolean bool = true;
/*     */     try {
/* 260 */       ZipInputStream zipInputStream = new ZipInputStream(new ByteArrayInputStream(paramArrayOfbyte));
/* 261 */       ZipEntry zipEntry = null;
/* 262 */       while ((zipEntry = zipInputStream.getNextEntry()) != null) {
/* 263 */         File file1 = new File(file, zipEntry.getName());
/* 264 */         FileUtils.forceMkdir(file1.getParentFile());
/*     */         
/* 266 */         FileOutputStream fileOutputStream = new FileOutputStream(file1);
/* 267 */         IOUtils.copy(zipInputStream, fileOutputStream);
/* 268 */         fileOutputStream.close();
/* 269 */         zipInputStream.closeEntry();
/*     */       } 
/* 271 */       zipInputStream.close();
/*     */     }
/* 273 */     catch (IOException iOException) {
/* 274 */       log.error("Unable to write configuration files to " + file, iOException);
/* 275 */       bool = false;
/*     */     } 
/* 277 */     return bool;
/*     */   }
/*     */   
/*     */   private static byte[] saveConfigurationInSession(HttpServletRequest paramHttpServletRequest, File paramFile) throws IOException, FileNotFoundException {
/* 281 */     byte[] arrayOfByte = IOUtils.toByteArray(new FileInputStream(paramFile));
/* 282 */     paramHttpServletRequest.getSession().setAttribute("CONFIGURATION", arrayOfByte);
/* 283 */     return arrayOfByte;
/*     */   }
/*     */ 
/*     */   
/*     */   protected File generateZipFile(final String contextPath, final String password, byte[] paramArrayOfbyte1, byte[] paramArrayOfbyte2, EntityDescriptor paramEntityDescriptor) throws IOException {
/* 288 */     File file = File.createTempFile("oiosaml-", ".zip");
/* 289 */     ZipOutputStream zipOutputStream = new ZipOutputStream(new FileOutputStream(file));
/* 290 */     zipOutputStream.putNextEntry(new ZipEntry("oiosaml-sp.properties"));
/* 291 */     zipOutputStream.write(renderTemplate("defaultproperties.vm", new HashMap<String, Object>() {  }, false)
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */         
/* 298 */         .getBytes());
/* 299 */     zipOutputStream.closeEntry();
/*     */     
/* 301 */     zipOutputStream.putNextEntry(new ZipEntry("metadata/SP/SPMetadata.xml"));
/* 302 */     zipOutputStream.write(SAMLUtil.getSAMLObjectAsPrettyPrintXML((XMLObject)paramEntityDescriptor).getBytes());
/* 303 */     zipOutputStream.closeEntry();
/*     */     
/* 305 */     zipOutputStream.putNextEntry(new ZipEntry("metadata/IdP/IdPMetadata.xml"));
/* 306 */     zipOutputStream.write(paramArrayOfbyte1);
/* 307 */     zipOutputStream.closeEntry();
/*     */     
/* 309 */     zipOutputStream.putNextEntry(new ZipEntry("certificate/keystore"));
/* 310 */     zipOutputStream.write(paramArrayOfbyte2);
/* 311 */     zipOutputStream.closeEntry();
/*     */     
/* 313 */     zipOutputStream.putNextEntry(new ZipEntry("oiosaml-sp.log4j.xml"));
/* 314 */     IOUtils.copy(getClass().getResourceAsStream("/oiosaml-sp.log4j.xml"), zipOutputStream);
/* 315 */     zipOutputStream.closeEntry();
/*     */     
/* 317 */     zipOutputStream.close();
/* 318 */     return file;
/*     */   }
/*     */   
/*     */   protected EntityDescriptor generateSPDescriptor(String paramString1, String paramString2, Credential paramCredential, String paramString3, String paramString4, String paramString5, boolean paramBoolean1, boolean paramBoolean2, boolean paramBoolean3, boolean paramBoolean4, boolean paramBoolean5) {
/* 322 */     EntityDescriptor entityDescriptor = (EntityDescriptor)SAMLUtil.buildXMLObject(EntityDescriptor.class);
/* 323 */     entityDescriptor.setEntityID(paramString2);
/*     */     
/* 325 */     SPSSODescriptor sPSSODescriptor = (SPSSODescriptor)SAMLUtil.buildXMLObject(SPSSODescriptor.class);
/* 326 */     sPSSODescriptor.setAuthnRequestsSigned(Boolean.valueOf(true));
/* 327 */     sPSSODescriptor.setWantAssertionsSigned(Boolean.valueOf(true));
/*     */     
/* 329 */     ContactPerson contactPerson = (ContactPerson)SAMLUtil.buildXMLObject(ContactPerson.class);
/* 330 */     contactPerson.getEmailAddresses().add(SAMLUtil.createEmail(paramString5));
/* 331 */     contactPerson.setCompany(SAMLUtil.createCompany(paramString3));
/* 332 */     contactPerson.setType(ContactPersonTypeEnumeration.TECHNICAL);
/*     */     
/* 334 */     entityDescriptor.getContactPersons().add(contactPerson);
/* 335 */     entityDescriptor.setOrganization(SAMLUtil.createOrganization(paramString3, paramString3, paramString4));
/*     */     
/* 337 */     KeyDescriptor keyDescriptor1 = (KeyDescriptor)SAMLUtil.buildXMLObject(KeyDescriptor.class);
/* 338 */     keyDescriptor1.setUse(UsageType.SIGNING);
/* 339 */     KeyDescriptor keyDescriptor2 = (KeyDescriptor)SAMLUtil.buildXMLObject(KeyDescriptor.class);
/* 340 */     keyDescriptor2.setUse(UsageType.ENCRYPTION);
/*     */     
/*     */     try {
/* 343 */       KeyInfoGenerator keyInfoGenerator = SecurityHelper.getKeyInfoGenerator(paramCredential, Configuration.getGlobalSecurityConfiguration(), null);
/* 344 */       keyDescriptor1.setKeyInfo(keyInfoGenerator.generate(paramCredential));
/* 345 */       keyDescriptor2.setKeyInfo(keyInfoGenerator.generate(paramCredential));
/*     */     }
/* 347 */     catch (SecurityException securityException) {
/* 348 */       throw new WrappedException(Layer.BUSINESS, securityException);
/*     */     } 
/* 350 */     sPSSODescriptor.getKeyDescriptors().add(keyDescriptor1);
/* 351 */     sPSSODescriptor.getKeyDescriptors().add(keyDescriptor2);
/*     */     
/* 353 */     sPSSODescriptor.addSupportedProtocol("urn:oasis:names:tc:SAML:2.0:protocol");
/* 354 */     sPSSODescriptor.getAssertionConsumerServices().add(SAMLUtil.createAssertionConsumerService(paramString1 + "/SAMLAssertionConsumer", "urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST", 0, true));
/* 355 */     if (paramBoolean1) {
/* 356 */       sPSSODescriptor.getAssertionConsumerServices().add(SAMLUtil.createAssertionConsumerService(paramString1 + "/SAMLAssertionConsumer", "urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Artifact", 1, false));
/*     */     }
/* 358 */     if (paramBoolean2) {
/* 359 */       sPSSODescriptor.getAssertionConsumerServices().add(SAMLUtil.createAssertionConsumerService(paramString1 + "/SAMLAssertionConsumer", "urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Redirect", 2, false));
/*     */     }
/*     */     
/* 362 */     sPSSODescriptor.getSingleLogoutServices().add(SAMLUtil.createSingleLogoutService(paramString1 + "/LogoutServiceHTTPRedirect", paramString1 + "/LogoutServiceHTTPRedirectResponse", "urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Redirect"));
/*     */     
/* 364 */     if (paramBoolean3) {
/* 365 */       sPSSODescriptor.getSingleLogoutServices().add(SAMLUtil.createSingleLogoutService(paramString1 + "/LogoutServiceSOAP", null, "urn:oasis:names:tc:SAML:2.0:bindings:SOAP"));
/*     */     }
/*     */     
/* 368 */     if (paramBoolean4) {
/* 369 */       sPSSODescriptor.getSingleLogoutServices().add(SAMLUtil.createSingleLogoutService(paramString1 + "/LogoutServiceHTTPPost", paramString1 + "/LogoutServiceHTTPRedirectResponse", "urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST"));
/*     */     }
/*     */     
/* 372 */     NameIDFormat nameIDFormat = SAMLUtil.createNameIDFormat("urn:oasis:names:tc:SAML:1.1:nameid-format:X509SubjectName");
/* 373 */     List<NameIDFormat> list = sPSSODescriptor.getNameIDFormats();
/* 374 */     list.add(nameIDFormat);
/*     */     
/* 376 */     if (paramBoolean1) {
/* 377 */       sPSSODescriptor.getArtifactResolutionServices().add(SAMLUtil.createArtifactResolutionService(paramString1 + "/SAMLAssertionConsumer"));
/*     */     }
/*     */     
/* 380 */     if (paramBoolean5) {
/* 381 */       addAttributeConsumerService(sPSSODescriptor, paramString2);
/*     */     }
/*     */     
/* 384 */     entityDescriptor.getRoleDescriptors().add(sPSSODescriptor);
/* 385 */     return entityDescriptor;
/*     */   }
/*     */   
/*     */   private static void addAttributeConsumerService(SPSSODescriptor paramSPSSODescriptor, String paramString) {
/* 389 */     AttributeConsumingService attributeConsumingService = SAMLUtil.createAttributeConsumingService(paramString);
/*     */     
/* 391 */     String[] arrayOfString1 = { "urn:oid:*******", "urn:oid:*******", "urn:oid:0.9.2342.********.100.1.1", "urn:oid:0.9.2342.********.100.1.3", "dk:gov:saml:attribute:AssuranceLevel", "dk:gov:saml:attribute:SpecVer", "urn:oid:*******", "dk:gov:saml:attribute:IsYouthCert", "urn:oid:*********" };
/*     */ 
/*     */     
/* 394 */     String[] arrayOfString2 = { "dk:gov:saml:attribute:UniqueAccountKey", "dk:gov:saml:attribute:CvrNumberIdentifier", "urn:oid:********", "urn:oid:********", "urn:oid:********", "urn:oid:********", "urn:oid:********", "urn:oid:*******.4.1.1466.***********", "dk:gov:saml:attribute:PidNumberIdentifier", "dk:gov:saml:attribute:CprNumberIdentifier", "dk:gov:saml:attribute:RidNumberIdentifier", "dk:gov:saml:attribute:Privileges_intermediate", "dk:gov:saml:attribute:UserAdministratorIndicator" };
/*     */ 
/*     */     
/* 397 */     for (String str : arrayOfString1) {
/* 398 */       attributeConsumingService.getRequestAttributes().add(SAMLUtil.createRequestedAttribute(str, "urn:oasis:names:tc:SAML:2.0:attrname-format:basic", true));
/*     */     }
/* 400 */     for (String str : arrayOfString2) {
/* 401 */       attributeConsumingService.getRequestAttributes().add(SAMLUtil.createRequestedAttribute(str, "urn:oasis:names:tc:SAML:2.0:attrname-format:basic", false));
/*     */     }
/*     */     
/* 404 */     paramSPSSODescriptor.getAttributeConsumingServices().add(attributeConsumingService);
/*     */   }
/*     */   
/*     */   private static List<?> extractParameterList(HttpServletRequest paramHttpServletRequest) {
/*     */     List<?> list;
/*     */     try {
/* 410 */       DiskFileItemFactory diskFileItemFactory = new DiskFileItemFactory();
/* 411 */       list = (new ServletFileUpload((FileItemFactory)diskFileItemFactory)).parseRequest(paramHttpServletRequest);
/*     */     }
/* 413 */     catch (FileUploadException fileUploadException) {
/* 414 */       log.error("Unable to parse uploaded files", (Throwable)fileUploadException);
/* 415 */       throw new RuntimeException("Unable to parse uploaded files", fileUploadException);
/*     */     } 
/* 417 */     return list;
/*     */   }
/*     */ 
/*     */   
/*     */   private boolean checkConfiguration(HttpServletResponse paramHttpServletResponse) throws IOException {
/* 422 */     if (isConfigured()) {
/* 423 */       sendResponse(paramHttpServletResponse, renderTemplate("alreadyConfigured.vm", new HashMap<String, Object>() {  }, true));
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 428 */       return false;
/*     */     } 
/* 430 */     return true;
/*     */   }
/*     */   
/*     */   private static FileItem extractFile(String paramString, List<?> paramList) {
/* 434 */     for (FileItem fileItem : paramList) {
/*     */       
/* 436 */       if (!fileItem.isFormField() && fileItem.getFieldName().equals(paramString)) {
/* 437 */         return fileItem;
/*     */       }
/*     */     } 
/* 440 */     return null;
/*     */   }
/*     */   
/*     */   private static String extractParameter(String paramString, List<?> paramList) {
/* 444 */     for (FileItem fileItem : paramList) {
/*     */       
/* 446 */       if (fileItem.isFormField() && fileItem.getFieldName().equals(paramString)) {
/* 447 */         return "".equals(fileItem.getString()) ? null : fileItem.getString();
/*     */       }
/*     */     } 
/* 450 */     return null;
/*     */   }
/*     */   
/*     */   private static void sendResponse(HttpServletResponse paramHttpServletResponse, String paramString) throws IOException {
/* 454 */     paramHttpServletResponse.setContentType("text/html");
/* 455 */     paramHttpServletResponse.setCharacterEncoding("UTF-8");
/* 456 */     paramHttpServletResponse.getWriter().write(paramString);
/*     */   }
/*     */   
/*     */   protected String getBaseUrl(HttpServletRequest paramHttpServletRequest) {
/* 460 */     String str = paramHttpServletRequest.getRequestURL().toString();
/* 461 */     int i = str.lastIndexOf(paramHttpServletRequest.getServletPath());
/*     */     
/* 463 */     return str.substring(0, i + paramHttpServletRequest.getServletPath().length());
/*     */   }
/*     */   
/*     */   protected boolean isHomeAvailable() {
/* 467 */     String str = getHome();
/* 468 */     if (str == null) {
/* 469 */       return false;
/*     */     }
/* 471 */     if ((new File(str)).isDirectory()) {
/* 472 */       return true;
/*     */     }
/*     */     
/* 475 */     return false;
/*     */   }
/*     */   
/*     */   protected boolean isConfigured() {
/* 479 */     String str = getHome();
/* 480 */     if (str == null) {
/* 481 */       return false;
/*     */     }
/* 483 */     File file = new File(str);
/* 484 */     String[] arrayOfString = file.list();
/* 485 */     if (arrayOfString != null && arrayOfString.length > 0) {
/* 486 */       return true;
/*     */     }
/*     */     
/* 489 */     return false;
/*     */   }
/*     */   
/*     */   protected String renderTemplate(String paramString, Map<String, Object> paramMap, boolean paramBoolean) {
/* 493 */     VelocityContext velocityContext = new VelocityContext();
/* 494 */     for (Map.Entry<String, Object> entry : paramMap.entrySet()) {
/* 495 */       velocityContext.put((String)entry.getKey(), entry.getValue());
/*     */     }
/*     */     
/* 498 */     StringWriter stringWriter = new StringWriter();
/*     */     
/*     */     try {
/* 501 */       if (paramBoolean) {
/* 502 */         this.engine.mergeTemplate("head.vm", "UTF-8", (Context)velocityContext, stringWriter);
/*     */       }
/* 504 */       this.engine.mergeTemplate(paramString, "UTF-8", (Context)velocityContext, stringWriter);
/* 505 */       if (paramBoolean) {
/* 506 */         this.engine.mergeTemplate("foot.vm", "UTF-8", (Context)velocityContext, stringWriter);
/*     */       }
/*     */     }
/* 509 */     catch (Exception exception) {
/* 510 */       log.error("Unable to merge templates", exception);
/*     */     } 
/* 512 */     return stringWriter.toString();
/*     */   }
/*     */   
/*     */   private static String getHome() {
/* 516 */     String str = ((FileConfiguration)SAMLConfigurationFactory.getConfiguration()).getHomeDir();
/* 517 */     File file = new File(str);
/* 518 */     if (!file.exists())
/* 519 */       file.mkdir(); 
/* 520 */     return str;
/*     */   }
/*     */   
/*     */   private static String getEntityId(HttpServletRequest paramHttpServletRequest) {
/* 524 */     return paramHttpServletRequest.getScheme() + "://saml." + paramHttpServletRequest.getServerName();
/*     */   }
/*     */   
/*     */   private static boolean checkNotNull(Object... paramVarArgs) {
/* 528 */     for (Object object : paramVarArgs) {
/* 529 */       if (object == null) {
/* 530 */         return false;
/*     */       }
/*     */     } 
/* 533 */     return true;
/*     */   }
/*     */   
/*     */   protected Map<String, Object> getStandardParameters(HttpServletRequest paramHttpServletRequest) {
/* 537 */     String str = getBaseUrl(paramHttpServletRequest);
/* 538 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 539 */     hashMap.put("artifactResponseUrl", str + "/SAMLAssertionConsumer");
/* 540 */     hashMap.put("postResponseUrl", str + "/SAMLAssertionConsumer");
/* 541 */     hashMap.put("logoutUrl", str + "/SAMLAssertionConsumer");
/* 542 */     hashMap.put("logoutResponseUrl", str + "/LogoutServiceHTTPRedirectResponse");
/* 543 */     hashMap.put("logoutRequestUrl", str + "/LogoutServiceHTTPRedirect");
/* 544 */     hashMap.put("logoutSoapRequestUrl", str + "/LogoutServiceSOAP");
/* 545 */     hashMap.put("logoutPostRequestUrl", str + "/LogoutServiceHTTPPost");
/* 546 */     hashMap.put("home", getHome());
/* 547 */     hashMap.put("entityId", getEntityId(paramHttpServletRequest));
/* 548 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/dk/itst/oiosaml/sp/configuration/ConfigurationHandler.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */