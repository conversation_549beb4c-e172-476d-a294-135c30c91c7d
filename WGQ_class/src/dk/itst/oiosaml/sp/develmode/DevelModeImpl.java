/*     */ package dk.itst.oiosaml.sp.develmode;
/*     */ 
/*     */ import dk.itst.oiosaml.common.SAMLUtil;
/*     */ import dk.itst.oiosaml.logging.Logger;
/*     */ import dk.itst.oiosaml.logging.LoggerFactory;
/*     */ import dk.itst.oiosaml.sp.UserAssertion;
/*     */ import dk.itst.oiosaml.sp.UserAssertionHolder;
/*     */ import dk.itst.oiosaml.sp.UserAssertionImpl;
/*     */ import dk.itst.oiosaml.sp.model.OIOAssertion;
/*     */ import dk.itst.oiosaml.sp.service.SAMLHttpServletRequest;
/*     */ import dk.itst.oiosaml.sp.service.util.HTTPUtils;
/*     */ import dk.itst.oiosaml.sp.util.AttributeUtil;
/*     */ import java.io.IOException;
/*     */ import java.net.URLEncoder;
/*     */ import java.util.Arrays;
/*     */ import java.util.HashMap;
/*     */ import java.util.Iterator;
/*     */ import java.util.Map;
/*     */ import javax.servlet.FilterChain;
/*     */ import javax.servlet.ServletException;
/*     */ import javax.servlet.ServletRequest;
/*     */ import javax.servlet.ServletResponse;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import org.apache.commons.configuration.Configuration;
/*     */ import org.apache.velocity.VelocityContext;
/*     */ import org.apache.velocity.context.Context;
/*     */ import org.joda.time.DateTime;
/*     */ import org.opensaml.saml2.core.Assertion;
/*     */ import org.opensaml.saml2.core.Attribute;
/*     */ import org.opensaml.saml2.core.AttributeStatement;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class DevelModeImpl
/*     */   implements DevelMode
/*     */ {
/*  59 */   private static final Logger log = LoggerFactory.getLogger(DevelModeImpl.class);
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void doFilter(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse, FilterChain paramFilterChain, Configuration paramConfiguration) throws IOException, ServletException {
/*  65 */     if (paramHttpServletRequest.getServletPath().equals(paramConfiguration.getProperty("oiosaml-sp.servlet"))) {
/*  66 */       log.debug("Develmode: Request to SAML servlet, access granted");
/*  67 */       paramFilterChain.doFilter((ServletRequest)paramHttpServletRequest, (ServletResponse)paramHttpServletResponse);
/*     */       
/*     */       return;
/*     */     } 
/*  71 */     UserAssertionHolder.set(null);
/*  72 */     UserAssertion userAssertion = (UserAssertion)paramHttpServletRequest.getSession().getAttribute("dk.itst.oiosaml.userassertion");
/*  73 */     if (userAssertion == null) {
/*  74 */       String[] arrayOfString = paramConfiguration.getStringArray("oiosaml-sp.develmode.users");
/*  75 */       if (arrayOfString == null || arrayOfString.length == 0) {
/*  76 */         log.error("No users defined in properties. Set oiosaml-sp.develmode.users");
/*  77 */         paramHttpServletResponse.setStatus(500);
/*  78 */         HTTPUtils.sendCacheHeaders(paramHttpServletResponse);
/*  79 */         render("nousers.vm", paramHttpServletResponse, new HashMap<>());
/*     */         
/*     */         return;
/*     */       } 
/*  83 */       if (arrayOfString.length == 1) {
/*  84 */         userAssertion = selectUser(arrayOfString[0], paramConfiguration);
/*     */       } else {
/*     */         
/*  87 */         String str = paramHttpServletRequest.getParameter("__oiosaml_devel");
/*  88 */         if (str == null || !Arrays.<String>asList(arrayOfString).contains(str)) {
/*  89 */           HTTPUtils.sendCacheHeaders(paramHttpServletResponse);
/*     */           
/*  91 */           HashMap<Object, Object> hashMap = new HashMap<>();
/*  92 */           hashMap.put("users", arrayOfString);
/*  93 */           hashMap.put("params", buildParameterString(paramHttpServletRequest.getParameterMap()));
/*  94 */           render("users.vm", paramHttpServletResponse, (Map)hashMap);
/*     */           
/*     */           return;
/*     */         } 
/*  98 */         HTTPUtils.sendCacheHeaders(paramHttpServletResponse);
/*  99 */         userAssertion = selectUser(str, paramConfiguration);
/* 100 */         paramHttpServletRequest.getSession().setAttribute("dk.itst.oiosaml.userassertion", userAssertion);
/* 101 */         paramHttpServletResponse.sendRedirect(paramHttpServletRequest.getRequestURI() + "?" + buildParameterString(paramHttpServletRequest.getParameterMap()));
/*     */       } 
/*     */     } 
/*     */     
/* 105 */     if (userAssertion != null) {
/* 106 */       paramHttpServletRequest.getSession().setAttribute("dk.itst.oiosaml.userassertion", userAssertion);
/* 107 */       UserAssertionHolder.set(userAssertion);
/*     */       
/* 109 */       SAMLHttpServletRequest sAMLHttpServletRequest = new SAMLHttpServletRequest(paramHttpServletRequest, userAssertion, "");
/* 110 */       paramFilterChain.doFilter((ServletRequest)sAMLHttpServletRequest, (ServletResponse)paramHttpServletResponse);
/*     */       
/*     */       return;
/*     */     } 
/* 114 */     log.error("No assertion found");
/* 115 */     paramHttpServletResponse.sendError(500);
/*     */   }
/*     */   
/*     */   private static String buildParameterString(Map<String, String[]> paramMap) {
/* 119 */     StringBuilder stringBuilder = new StringBuilder();
/* 120 */     String str = "";
/*     */     try {
/* 122 */       for (Map.Entry<String, String> entry : paramMap.entrySet()) {
/* 123 */         if ("__oiosaml_devel".equals(entry.getKey()))
/*     */           continue; 
/* 125 */         for (String str1 : (String[])entry.getValue()) {
/* 126 */           stringBuilder.append(str);
/* 127 */           str = "&";
/* 128 */           stringBuilder.append((String)entry.getKey()).append("=").append(URLEncoder.encode(str1, "UTF-8"));
/*     */         }
/*     */       
/*     */       } 
/* 132 */     } catch (Exception exception) {
/* 133 */       throw new RuntimeException(exception);
/*     */     } 
/* 135 */     return stringBuilder.toString();
/*     */   }
/*     */   
/*     */   private static UserAssertion selectUser(String paramString, Configuration paramConfiguration) {
/* 139 */     Map<String, String[]> map = getAttributes(paramString, paramConfiguration);
/*     */     
/* 141 */     Assertion assertion = (Assertion)SAMLUtil.buildXMLObject(Assertion.class);
/* 142 */     assertion.setSubject(SAMLUtil.createSubject(paramString, "urn:test", (new DateTime()).plusHours(1)));
/*     */     
/* 144 */     AttributeStatement attributeStatement = (AttributeStatement)SAMLUtil.buildXMLObject(AttributeStatement.class);
/* 145 */     assertion.getAttributeStatements().add(attributeStatement);
/*     */     
/* 147 */     for (Map.Entry<String, String> entry : map.entrySet()) {
/* 148 */       Attribute attribute = AttributeUtil.createAttribute((String)entry.getKey(), (String)entry.getKey(), "");
/* 149 */       for (String str : (String[])entry.getValue()) {
/* 150 */         attribute.getAttributeValues().add(AttributeUtil.createAttributeValue(str));
/*     */       }
/* 152 */       attributeStatement.getAttributes().add(attribute);
/*     */     } 
/*     */     
/* 155 */     return (UserAssertion)new UserAssertionImpl(new OIOAssertion(assertion));
/*     */   }
/*     */   
/*     */   private static Map<String, String[]> getAttributes(String paramString, Configuration paramConfiguration) {
/* 159 */     String str = "oiosaml-sp.develmode." + paramString + ".";
/*     */     
/* 161 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 162 */     Iterator<String> iterator = paramConfiguration.getKeys();
/* 163 */     while (iterator.hasNext()) {
/* 164 */       String str1 = iterator.next();
/* 165 */       if (str1.startsWith(str)) {
/* 166 */         String str2 = str1.substring(str.length());
/* 167 */         String[] arrayOfString = paramConfiguration.getStringArray(str1);
/* 168 */         hashMap.put(str2, arrayOfString);
/*     */       } 
/*     */     } 
/* 171 */     return (Map)hashMap;
/*     */   }
/*     */   
/*     */   private static void render(String paramString, HttpServletResponse paramHttpServletResponse, Map<String, ?> paramMap) {
/* 175 */     VelocityContext velocityContext = new VelocityContext();
/* 176 */     for (Map.Entry<String, ?> entry : paramMap.entrySet()) {
/* 177 */       velocityContext.put((String)entry.getKey(), entry.getValue());
/*     */     }
/*     */     
/*     */     try {
/* 181 */       HTTPUtils.getEngine().mergeTemplate(paramString, "UTF-8", (Context)velocityContext, paramHttpServletResponse.getWriter());
/*     */     }
/* 183 */     catch (Exception exception) {
/* 184 */       throw new RuntimeException(exception);
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/dk/itst/oiosaml/sp/develmode/DevelModeImpl.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */