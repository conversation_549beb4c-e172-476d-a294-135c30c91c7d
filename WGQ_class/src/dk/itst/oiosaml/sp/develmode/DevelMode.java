package dk.itst.oiosaml.sp.develmode;

import java.io.IOException;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.commons.configuration.Configuration;

public interface DevelMode {
  void doFilter(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse, FilterChain paramFilterChain, Configuration paramConfiguration) throws IOException, ServletException;
}


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/dk/itst/oiosaml/sp/develmode/DevelMode.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */