/*    */ package dk.itst.oiosaml.sp.model;
/*    */ 
/*    */ import javax.servlet.http.HttpServletRequest;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class RelayState
/*    */ {
/*    */   private final String relayState;
/*    */   
/*    */   public RelayState(String paramString) {
/* 34 */     this.relayState = paramString;
/*    */   }
/*    */   
/*    */   public static RelayState fromRequest(HttpServletRequest paramHttpServletRequest) {
/* 38 */     String str = paramHttpServletRequest.getParameter("RelayState");
/* 39 */     return new RelayState(str);
/*    */   }
/*    */   
/*    */   public String toString() {
/* 43 */     return "RelayState: " + this.relayState;
/*    */   }
/*    */   
/*    */   public String getRelayState() {
/* 47 */     return this.relayState;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/dk/itst/oiosaml/sp/model/RelayState.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */