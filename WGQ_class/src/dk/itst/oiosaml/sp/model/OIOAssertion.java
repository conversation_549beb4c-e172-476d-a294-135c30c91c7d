/*     */ package dk.itst.oiosaml.sp.model;
/*     */ 
/*     */ import dk.itst.oiosaml.logging.Logger;
/*     */ import dk.itst.oiosaml.logging.LoggerFactory;
/*     */ import dk.itst.oiosaml.sp.model.validation.AssertionValidator;
/*     */ import dk.itst.oiosaml.sp.model.validation.ValidationException;
/*     */ import dk.itst.oiosaml.sp.util.AttributeUtil;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Collection;
/*     */ import org.joda.time.DateTime;
/*     */ import org.opensaml.common.SAMLVersion;
/*     */ import org.opensaml.saml2.core.Assertion;
/*     */ import org.opensaml.saml2.core.Attribute;
/*     */ import org.opensaml.saml2.core.AttributeStatement;
/*     */ import org.opensaml.saml2.core.Audience;
/*     */ import org.opensaml.saml2.core.AudienceRestriction;
/*     */ import org.opensaml.saml2.core.AuthnContext;
/*     */ import org.opensaml.saml2.core.AuthnContextClassRef;
/*     */ import org.opensaml.saml2.core.AuthnStatement;
/*     */ import org.opensaml.saml2.core.Response;
/*     */ import org.opensaml.saml2.core.SubjectConfirmation;
/*     */ import org.opensaml.saml2.core.SubjectConfirmationData;
/*     */ import org.opensaml.xml.XMLObject;
/*     */ import org.opensaml.xml.validation.ValidationException;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class OIOAssertion
/*     */   extends OIOSamlObject
/*     */ {
/*  52 */   private static final Logger log = LoggerFactory.getLogger(OIOAssertion.class);
/*     */   
/*     */   private final Assertion assertion;
/*     */   
/*     */   public OIOAssertion(Assertion paramAssertion) {
/*  57 */     super((XMLObject)paramAssertion);
/*  58 */     this.assertion = paramAssertion;
/*     */   }
/*     */   
/*     */   public static OIOAssertion fromResponse(Response paramResponse) {
/*  62 */     if (paramResponse.getAssertions().isEmpty()) {
/*  63 */       throw new RuntimeException("Didn't get an assertion in ArtifactResponse");
/*     */     }
/*  65 */     Assertion assertion = paramResponse.getAssertions().get(0);
/*  66 */     return new OIOAssertion(assertion);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getSubjectNameIDValue() {
/*  76 */     String str = null;
/*  77 */     if (this.assertion.getSubject() != null && this.assertion
/*  78 */       .getSubject().getNameID() != null) {
/*  79 */       str = this.assertion.getSubject().getNameID().getValue();
/*     */     }
/*  81 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean checkRecipient(String paramString) {
/*  95 */     if (paramString == null) return false; 
/*  96 */     if (this.assertion.getSubject() == null) return false; 
/*  97 */     if (this.assertion.getSubject().getSubjectConfirmations() == null) return false;
/*     */ 
/*     */     
/* 100 */     for (SubjectConfirmation subjectConfirmation : this.assertion.getSubject().getSubjectConfirmations()) {
/* 101 */       if (!"urn:oasis:names:tc:SAML:2.0:cm:bearer".equals(subjectConfirmation.getMethod()))
/*     */         continue; 
/* 103 */       SubjectConfirmationData subjectConfirmationData = subjectConfirmation.getSubjectConfirmationData();
/* 104 */       if (subjectConfirmationData == null)
/*     */         continue; 
/* 106 */       if (paramString.equals(subjectConfirmationData.getRecipient())) {
/* 107 */         return true;
/*     */       }
/*     */     } 
/* 110 */     return false;
/*     */   }
/*     */   
/*     */   public DateTime getConfirmationTime() {
/* 114 */     if (this.assertion.getSubject() == null) return null; 
/* 115 */     if (this.assertion.getSubject().getSubjectConfirmations() == null || this.assertion
/* 116 */       .getSubject().getSubjectConfirmations().isEmpty()) return null;
/*     */     
/* 118 */     for (SubjectConfirmation subjectConfirmation : this.assertion.getSubject().getSubjectConfirmations()) {
/* 119 */       SubjectConfirmationData subjectConfirmationData = subjectConfirmation.getSubjectConfirmationData();
/*     */       
/* 121 */       if (subjectConfirmationData != null && subjectConfirmationData.getNotOnOrAfter() != null) {
/* 122 */         return subjectConfirmationData.getNotOnOrAfter();
/*     */       }
/*     */     } 
/* 125 */     return null;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getSessionIndex() {
/* 136 */     String str = null;
/* 137 */     if (this.assertion != null && this.assertion.getAuthnStatements() != null && 
/* 138 */       this.assertion.getAuthnStatements().size() > 0) {
/*     */       
/* 140 */       AuthnStatement authnStatement = this.assertion.getAuthnStatements().get(0);
/* 141 */       str = authnStatement.getSessionIndex();
/*     */     } 
/*     */     
/* 144 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean hasSessionExpired() {
/* 155 */     boolean bool = false;
/* 156 */     if (this.assertion != null && this.assertion.getAuthnStatements() != null && 
/* 157 */       this.assertion.getAuthnStatements().size() > 0) {
/*     */       
/* 159 */       AuthnStatement authnStatement = this.assertion.getAuthnStatements().get(0);
/* 160 */       if (authnStatement.getSessionNotOnOrAfter() != null) {
/* 161 */         bool = authnStatement.getSessionNotOnOrAfter().isBeforeNow();
/*     */       } else {
/* 163 */         bool = false;
/*     */       } 
/*     */     } 
/*     */     
/* 167 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getAuthnContextClassRef() {
/* 178 */     String str = null;
/* 179 */     if (this.assertion.getAuthnStatements() != null && 
/* 180 */       this.assertion.getAuthnStatements().size() > 0) {
/*     */       
/* 182 */       AuthnStatement authnStatement = this.assertion.getAuthnStatements().get(0);
/* 183 */       AuthnContext authnContext = authnStatement.getAuthnContext();
/* 184 */       if (authnContext != null) {
/* 185 */         AuthnContextClassRef authnContextClassRef = authnContext.getAuthnContextClassRef();
/* 186 */         if (authnContextClassRef != null) {
/* 187 */           str = authnContextClassRef.getAuthnContextClassRef();
/*     */         }
/*     */       } 
/*     */     } 
/*     */     
/* 192 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void validateAssertion(AssertionValidator paramAssertionValidator, String paramString1, String paramString2) throws ValidationException {
/*     */     try {
/* 203 */       this.assertion.validate(false);
/* 204 */     } catch (ValidationException validationException) {
/* 205 */       throw new ValidationException(validationException);
/*     */     } 
/*     */     
/* 208 */     if (!SAMLVersion.VERSION_20.equals(this.assertion.getVersion())) {
/* 209 */       throw new ValidationException("The assertion must be version 2.0. Was " + this.assertion.getVersion());
/*     */     }
/*     */     
/* 212 */     if (this.assertion.getID() == null) {
/* 213 */       throw new ValidationException("The assertion must contain a ID");
/*     */     }
/*     */     
/* 216 */     log.debug("Using validator: " + paramAssertionValidator);
/* 217 */     if (paramAssertionValidator != null) {
/* 218 */       paramAssertionValidator.validate(this, paramString1, paramString2);
/*     */     }
/*     */   }
/*     */   
/*     */   public Assertion getAssertion() {
/* 223 */     return this.assertion;
/*     */   }
/*     */   
/*     */   public int getAssuranceLevel() {
/* 227 */     for (AttributeStatement attributeStatement : this.assertion.getAttributeStatements()) {
/* 228 */       for (Attribute attribute : attributeStatement.getAttributes()) {
/* 229 */         if ("dk:gov:saml:attribute:AssuranceLevel".equals(attribute.getName())) {
/* 230 */           String str = AttributeUtil.extractAttributeValueValue(attribute);
/* 231 */           return (new AssuranceLevel(str)).getValue();
/*     */         } 
/*     */       } 
/*     */     } 
/* 235 */     return 0;
/*     */   }
/*     */   
/*     */   public String getID() {
/* 239 */     return this.assertion.getID();
/*     */   }
/*     */   
/*     */   public boolean isHolderOfKey() {
/* 243 */     if (this.assertion.getSubject() == null) return false; 
/* 244 */     if (this.assertion.getSubject().getSubjectConfirmations().isEmpty()) return false;
/*     */     
/* 246 */     return "urn:oasis:names:tc:SAML:2.0:cm:holder-of-key".equals(((SubjectConfirmation)this.assertion.getSubject().getSubjectConfirmations().get(0)).getMethod());
/*     */   }
/*     */   
/*     */   public Collection<String> getAudience() {
/* 250 */     ArrayList<String> arrayList = new ArrayList();
/*     */     
/* 252 */     if (this.assertion.getConditions() == null) return arrayList;
/*     */     
/* 254 */     for (AudienceRestriction audienceRestriction : this.assertion.getConditions().getAudienceRestrictions()) {
/* 255 */       for (Audience audience : audienceRestriction.getAudiences()) {
/* 256 */         arrayList.add(audience.getAudienceURI());
/*     */       }
/*     */     } 
/*     */     
/* 260 */     return arrayList;
/*     */   }
/*     */   
/*     */   public DateTime getConditionTime() {
/* 264 */     if (this.assertion.getConditions() == null) return null;
/*     */     
/* 266 */     return this.assertion.getConditions().getNotOnOrAfter();
/*     */   }
/*     */ 
/*     */   
/*     */   public String getIssuer() {
/* 271 */     return this.assertion.getIssuer().getValue();
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/dk/itst/oiosaml/sp/model/OIOAssertion.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */