/*     */ package dk.itst.oiosaml.sp.model;
/*     */ 
/*     */ import dk.itst.oiosaml.common.SAMLUtil;
/*     */ import dk.itst.oiosaml.error.Layer;
/*     */ import dk.itst.oiosaml.error.WrappedException;
/*     */ import dk.itst.oiosaml.logging.Logger;
/*     */ import dk.itst.oiosaml.logging.LoggerFactory;
/*     */ import dk.itst.oiosaml.sp.model.validation.ValidationException;
/*     */ import dk.itst.oiosaml.sp.service.util.Utils;
/*     */ import java.io.ByteArrayOutputStream;
/*     */ import java.io.IOException;
/*     */ import java.security.PublicKey;
/*     */ import java.util.Collection;
/*     */ import java.util.Collections;
/*     */ import java.util.List;
/*     */ import java.util.zip.Deflater;
/*     */ import java.util.zip.DeflaterOutputStream;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import org.joda.time.DateTime;
/*     */ import org.joda.time.DateTimeZone;
/*     */ import org.opensaml.common.SAMLObject;
/*     */ import org.opensaml.common.SAMLVersion;
/*     */ import org.opensaml.common.binding.BasicSAMLMessageContext;
/*     */ import org.opensaml.saml2.binding.decoding.HTTPPostDecoder;
/*     */ import org.opensaml.saml2.binding.decoding.HTTPRedirectDeflateDecoder;
/*     */ import org.opensaml.saml2.binding.encoding.HTTPRedirectDeflateEncoder;
/*     */ import org.opensaml.saml2.core.LogoutResponse;
/*     */ import org.opensaml.saml2.core.StatusResponseType;
/*     */ import org.opensaml.util.URLBuilder;
/*     */ import org.opensaml.ws.message.MessageContext;
/*     */ import org.opensaml.ws.message.decoder.MessageDecodingException;
/*     */ import org.opensaml.ws.message.encoder.MessageEncodingException;
/*     */ import org.opensaml.ws.transport.InTransport;
/*     */ import org.opensaml.ws.transport.http.HttpServletRequestAdapter;
/*     */ import org.opensaml.xml.XMLObject;
/*     */ import org.opensaml.xml.security.SecurityConfiguration;
/*     */ import org.opensaml.xml.security.SecurityException;
/*     */ import org.opensaml.xml.security.credential.Credential;
/*     */ import org.opensaml.xml.util.Base64;
/*     */ import org.opensaml.xml.util.Pair;
/*     */ import org.opensaml.xml.util.XMLHelper;
/*     */ import org.opensaml.xml.validation.ValidationException;
/*     */ import weaver.integration.logging.Logger;
/*     */ import weaver.integration.logging.LoggerFactory;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class OIOLogoutResponse
/*     */   extends OIOAbstractResponse
/*     */ {
/*  68 */   private static final Logger log = LoggerFactory.getLogger(OIOLogoutResponse.class);
/*     */   
/*     */   private final LogoutResponse response;
/*     */   
/*     */   public OIOLogoutResponse(LogoutResponse paramLogoutResponse) {
/*  73 */     super((StatusResponseType)paramLogoutResponse);
/*  74 */     this.response = paramLogoutResponse;
/*     */   }
/*     */   
/*     */   public static OIOLogoutResponse fromRequest(OIOLogoutRequest paramOIOLogoutRequest, String paramString1, String paramString2, String paramString3, String paramString4) {
/*  78 */     LogoutResponse logoutResponse = (LogoutResponse)SAMLUtil.buildXMLObject(LogoutResponse.class);
/*     */     
/*  80 */     logoutResponse.setID(Utils.generateUUID());
/*  81 */     logoutResponse.setIssueInstant(new DateTime(DateTimeZone.UTC));
/*  82 */     logoutResponse.setVersion(SAMLVersion.VERSION_20);
/*  83 */     logoutResponse.setStatus(SAMLUtil.createStatus((paramString1 != null) ? paramString1 : "urn:oasis:names:tc:SAML:2.0:status:Success"));
/*     */     
/*  85 */     if (paramOIOLogoutRequest != null) {
/*  86 */       logoutResponse.setInResponseTo(paramOIOLogoutRequest.getID());
/*     */     }
/*  88 */     logoutResponse.setIssuer(SAMLUtil.createIssuer(paramString3));
/*  89 */     logoutResponse.setDestination(paramString4);
/*  90 */     if (paramString2 != null) {
/*  91 */       logoutResponse.setConsent(paramString2);
/*     */     }
/*  93 */     if (paramString1 != null && !"urn:oasis:names:tc:SAML:2.0:status:Success".equals(paramString1)) {
/*  94 */       log.error("Invalid <LogoutRequest>: " + paramString2);
/*     */     }
/*     */     try {
/*  97 */       if (log.isDebugEnabled()) log.debug("Validate the logoutResponse..."); 
/*  98 */       logoutResponse.validate(true);
/*  99 */       if (log.isDebugEnabled()) log.debug("...OK"); 
/* 100 */     } catch (ValidationException validationException) {
/* 101 */       throw new WrappedException(Layer.CLIENT, validationException);
/*     */     } 
/*     */     
/* 104 */     return new OIOLogoutResponse(logoutResponse);
/*     */   }
/*     */   
/*     */   public static OIOLogoutResponse fromPostRequest(HttpServletRequest paramHttpServletRequest) {
/* 108 */     BasicSAMLMessageContext basicSAMLMessageContext = new BasicSAMLMessageContext();
/* 109 */     basicSAMLMessageContext.setInboundMessageTransport((InTransport)new HttpServletRequestAdapter(paramHttpServletRequest));
/*     */     
/*     */     try {
/* 112 */       HTTPPostDecoder hTTPPostDecoder = new HTTPPostDecoder();
/* 113 */       hTTPPostDecoder.decode((MessageContext)basicSAMLMessageContext);
/* 114 */     } catch (MessageDecodingException messageDecodingException) {
/* 115 */       throw new WrappedException(Layer.CLIENT, messageDecodingException);
/* 116 */     } catch (SecurityException securityException) {
/* 117 */       throw new WrappedException(Layer.CLIENT, securityException);
/*     */     } 
/*     */     
/* 120 */     LogoutResponse logoutResponse = (LogoutResponse)basicSAMLMessageContext.getInboundSAMLMessage();
/* 121 */     OIOLogoutResponse oIOLogoutResponse = new OIOLogoutResponse(logoutResponse);
/* 122 */     if (log.isDebugEnabled()) {
/* 123 */       log.debug("Received response: " + oIOLogoutResponse.toXML());
/*     */     }
/*     */     
/* 126 */     return oIOLogoutResponse;
/*     */   }
/*     */   
/*     */   public static OIOLogoutResponse fromHttpRedirect(HttpServletRequest paramHttpServletRequest) {
/* 130 */     BasicSAMLMessageContext basicSAMLMessageContext = new BasicSAMLMessageContext();
/* 131 */     basicSAMLMessageContext.setInboundMessageTransport((InTransport)new HttpServletRequestAdapter(paramHttpServletRequest));
/*     */     
/*     */     try {
/* 134 */       HTTPRedirectDeflateDecoder hTTPRedirectDeflateDecoder = new HTTPRedirectDeflateDecoder();
/*     */ 
/*     */       
/* 137 */       hTTPRedirectDeflateDecoder.decode((MessageContext)basicSAMLMessageContext);
/* 138 */     } catch (MessageDecodingException messageDecodingException) {
/* 139 */       logger.error("===============MessageDecodingException error occured!" + messageDecodingException.getMessage());
/* 140 */       messageDecodingException.printStackTrace();
/*     */     }
/* 142 */     catch (SecurityException securityException) {
/* 143 */       logger.error("===============SecurityException error occured!" + securityException.getMessage());
/* 144 */       securityException.printStackTrace();
/*     */     } 
/*     */ 
/*     */     
/* 148 */     LogoutResponse logoutResponse = (LogoutResponse)basicSAMLMessageContext.getInboundSAMLMessage();
/*     */     
/* 150 */     OIOLogoutResponse oIOLogoutResponse = new OIOLogoutResponse(logoutResponse);
/* 151 */     if (log.isDebugEnabled()) {
/* 152 */       log.debug("Received response: " + oIOLogoutResponse.toXML());
/*     */     }
/*     */     
/* 155 */     return oIOLogoutResponse;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getRedirectURL(Credential paramCredential, String paramString) {
/* 165 */     Encoder encoder = new Encoder();
/*     */ 
/*     */     
/* 168 */     if (log.isDebugEnabled()) {
/* 169 */       log.debug("Setting RelayState..:" + paramString);
/*     */     }
/*     */     try {
/* 172 */       return buildRedirectURL(encoder.deflateAndBase64Encode((SAMLObject)this.response), paramString, paramCredential);
/* 173 */     } catch (MessageEncodingException messageEncodingException) {
/* 174 */       throw new WrappedException(Layer.CLIENT, messageEncodingException);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String buildRedirectURL(String paramString1, String paramString2, Credential paramCredential) throws MessageEncodingException {
/* 184 */     if (log.isDebugEnabled()) {
/* 185 */       log.debug("Building URL to redirect client to: " + this.response.getDestination());
/*     */     }
/* 187 */     URLBuilder uRLBuilder = new URLBuilder(this.response.getDestination());
/*     */     
/* 189 */     List<Pair> list = uRLBuilder.getQueryParams();
/* 190 */     list.clear();
/* 191 */     list.add(new Pair("SAMLResponse", paramString1));
/*     */ 
/*     */ 
/*     */     
/* 195 */     if (paramString2 != null) {
/* 196 */       list.add(new Pair("RelayState", paramString2));
/*     */     }
/*     */     
/* 199 */     Encoder encoder = new Encoder();
/* 200 */     if (paramCredential != null) {
/* 201 */       list.add(new Pair("SigAlg", encoder.getSignatureAlgorithmURI(paramCredential, null)));
/* 202 */       String str = uRLBuilder.buildQueryString();
/*     */       
/* 204 */       list.add(new Pair("Signature", encoder
/* 205 */             .generateSignature(paramCredential, encoder.getSignatureAlgorithmURI(paramCredential, null), str)));
/*     */     } 
/* 207 */     return uRLBuilder.buildURL();
/*     */   }
/*     */   
/*     */   public void validate(String paramString1, String paramString2) throws ValidationException {
/*     */     try {
/* 212 */       this.response.validate(true);
/* 213 */     } catch (ValidationException validationException) {
/* 214 */       log.error("Unable to validate message", (Throwable)validationException);
/* 215 */       throw new ValidationException(validationException);
/*     */     } 
/* 217 */     validateResponse(paramString1, paramString2, false);
/*     */   }
/*     */   
/*     */   public void validate(String paramString1, String paramString2, String paramString3, String paramString4, PublicKey paramPublicKey) {
/* 221 */     validate(paramString1, paramString2, paramString3, paramString4, Collections.singletonList(paramPublicKey));
/*     */   }
/*     */   
/*     */   public void validate(String paramString1, String paramString2, String paramString3, String paramString4, Collection<PublicKey> paramCollection) {
/* 225 */     validate(paramString1, paramString2);
/*     */     
/* 227 */     boolean bool = false;
/* 228 */     for (PublicKey publicKey : paramCollection) {
/* 229 */       if (Utils.verifySignature(paramString3, paramString4, "SAMLResponse", publicKey)) {
/* 230 */         bool = true;
/*     */       }
/*     */     } 
/* 233 */     if (!bool)
/* 234 */       throw new ValidationException("Invalid signature"); 
/* 235 */     if (log.isDebugEnabled()) {
/* 236 */       log.debug("...signature OK");
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void validate(String paramString1, String paramString2, Collection<PublicKey> paramCollection) {
/* 244 */     validate(paramString1, paramString2);
/*     */     
/* 246 */     boolean bool = false;
/* 247 */     for (PublicKey publicKey : paramCollection) {
/* 248 */       if (verifySignature(publicKey)) {
/* 249 */         bool = true;
/*     */       }
/*     */     } 
/*     */     
/* 253 */     if (!bool) {
/* 254 */       throw new ValidationException("Invalid signature");
/*     */     }
/* 256 */     if (log.isDebugEnabled()) {
/* 257 */       log.debug("...signature OK");
/*     */     }
/*     */   }
/*     */   
/*     */   protected static class Encoder
/*     */     extends HTTPRedirectDeflateEncoder
/*     */   {
/*     */     public String deflateAndBase64Encode(SAMLObject param1SAMLObject) throws MessageEncodingException {
/* 265 */       String str = XMLHelper.nodeToString(marshallMessage((XMLObject)param1SAMLObject));
/*     */       
/* 267 */       ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
/* 268 */       Deflater deflater = new Deflater(8, true);
/* 269 */       DeflaterOutputStream deflaterOutputStream = new DeflaterOutputStream(byteArrayOutputStream, deflater);
/*     */       try {
/* 271 */         deflaterOutputStream.write(str.getBytes("UTF-8"));
/* 272 */         deflaterOutputStream.finish();
/* 273 */       } catch (IOException iOException) {
/* 274 */         throw new RuntimeException("Unable to deflate message", iOException);
/*     */       } 
/*     */       
/* 277 */       return Base64.encodeBytes(byteArrayOutputStream.toByteArray(), 8);
/*     */     }
/*     */ 
/*     */ 
/*     */     
/*     */     protected String generateSignature(Credential param1Credential, String param1String1, String param1String2) throws MessageEncodingException {
/* 283 */       return super.generateSignature(param1Credential, param1String1, param1String2);
/*     */     }
/*     */ 
/*     */ 
/*     */     
/*     */     protected String getSignatureAlgorithmURI(Credential param1Credential, SecurityConfiguration param1SecurityConfiguration) throws MessageEncodingException {
/* 289 */       return super.getSignatureAlgorithmURI(param1Credential, param1SecurityConfiguration);
/*     */     }
/*     */   }
/*     */ 
/*     */   
/* 294 */   private static Logger logger = LoggerFactory.getLogger(OIOLogoutResponse.class);
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/dk/itst/oiosaml/sp/model/OIOLogoutResponse.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */