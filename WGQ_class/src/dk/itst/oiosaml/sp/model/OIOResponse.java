/*     */ package dk.itst.oiosaml.sp.model;
/*     */ 
/*     */ import dk.itst.oiosaml.helper.DeveloperHelper;
/*     */ import dk.itst.oiosaml.logging.Logger;
/*     */ import dk.itst.oiosaml.logging.LoggerFactory;
/*     */ import dk.itst.oiosaml.sp.model.validation.ValidationException;
/*     */ import dk.itst.oiosaml.sp.service.session.SessionHandler;
/*     */ import java.security.cert.Certificate;
/*     */ import java.util.Collection;
/*     */ import java.util.Collections;
/*     */ import org.opensaml.saml2.core.Assertion;
/*     */ import org.opensaml.saml2.core.Issuer;
/*     */ import org.opensaml.saml2.core.Response;
/*     */ import org.opensaml.saml2.core.StatusResponseType;
/*     */ import org.opensaml.xml.security.credential.Credential;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class OIOResponse
/*     */   extends OIOAbstractResponse
/*     */ {
/*  49 */   private static final Logger log = LoggerFactory.getLogger(OIOResponse.class);
/*     */   
/*     */   private final Response response;
/*     */   
/*     */   private OIOAssertion assertion;
/*     */   
/*     */   public OIOResponse(Response paramResponse) {
/*  56 */     super((StatusResponseType)paramResponse);
/*     */     
/*  58 */     this.response = paramResponse;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getOriginatingIdpEntityId(SessionHandler paramSessionHandler) {
/*  71 */     if (this.response.getInResponseTo() == null) {
/*  72 */       Issuer issuer = null;
/*  73 */       if (!this.response.getAssertions().isEmpty()) {
/*  74 */         issuer = ((Assertion)this.response.getAssertions().get(0)).getIssuer();
/*     */       }
/*  76 */       if (issuer == null) {
/*  77 */         issuer = this.response.getIssuer();
/*     */       }
/*     */       
/*  80 */       if (issuer == null) {
/*  81 */         throw new ValidationException("SAML Response does not contain a issuer, this is required for unsolicited Responses");
/*     */       }
/*  83 */       return issuer.getValue();
/*     */     } 
/*     */     
/*  86 */     return paramSessionHandler.removeEntityIdForRequest(this.response.getInResponseTo());
/*     */   }
/*     */   
/*     */   public void validateResponse(String paramString, Certificate paramCertificate, boolean paramBoolean) throws ValidationException {
/*  90 */     validateResponse(paramString, Collections.singletonList(paramCertificate), paramBoolean);
/*     */   }
/*     */   
/*     */   public void validateResponse(String paramString, Collection<? extends Certificate> paramCollection, boolean paramBoolean) throws ValidationException {
/*  94 */     validateResponse((String)null, paramString, paramBoolean);
/*     */     
/*  96 */     if (this.response.getAssertions().isEmpty() && !isPassive()) {
/*  97 */       throw new ValidationException("Response must contain an Assertion. If the Response contains an encrypted Assertion, decrypt it before calling validate.");
/*     */     }
/*     */     
/* 100 */     if (!hasSignature() && isPassive()) {
/*     */       return;
/*     */     }
/*     */     
/* 104 */     if (hasSignature() || isPassive()) {
/* 105 */       boolean bool = false;
/* 106 */       for (Certificate certificate : paramCollection) {
/* 107 */         if (verifySignature(certificate.getPublicKey())) {
/* 108 */           bool = true;
/*     */         }
/*     */       } 
/* 111 */       if (!bool) {
/* 112 */         throw new ValidationException("The response is not signed correctly");
/*     */       
/*     */       }
/*     */     }
/* 116 */     else if (!this.response.getAssertions().isEmpty()) {
/* 117 */       boolean bool = false;
/*     */       
/* 119 */       if (paramCollection.size() == 0) {
/* 120 */         DeveloperHelper.log("It is not possible to validate the signature on the assertion, because there are no valid certificates to check the signature against. This might be because revocation checking has failed on the IdP certificates");
/*     */       }
/*     */       
/* 123 */       for (Certificate certificate : paramCollection) {
/* 124 */         if (getAssertion().verifySignature(certificate.getPublicKey())) {
/* 125 */           bool = true;
/*     */         }
/*     */       } 
/*     */       
/* 129 */       if (!bool) {
/* 130 */         throw new ValidationException("The assertion is not signed correctly");
/*     */       }
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public OIOAssertion getAssertion() {
/* 140 */     if (this.assertion != null) {
/* 141 */       if (log.isDebugEnabled())
/* 142 */         log.debug("Found encrypted assertion, returning decrypted"); 
/* 143 */       return this.assertion;
/*     */     } 
/* 145 */     return OIOAssertion.fromResponse(this.response);
/*     */   }
/*     */   
/*     */   public void decryptAssertion(Credential paramCredential, boolean paramBoolean) {
/* 149 */     if (this.response.getEncryptedAssertions().size() > 0) {
/* 150 */       OIOEncryptedAssertion oIOEncryptedAssertion = new OIOEncryptedAssertion(this.response.getEncryptedAssertions().get(0));
/* 151 */       this.assertion = oIOEncryptedAssertion.decryptAssertion(paramCredential);
/* 152 */       this.response.getAssertions().add(this.assertion.getAssertion());
/*     */     
/*     */     }
/* 155 */     else if (!paramBoolean && !this.response.getAssertions().isEmpty()) {
/* 156 */       throw new ValidationException("Assertion is not encrypted");
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   public Response getResponse() {
/* 162 */     return this.response;
/*     */   }
/*     */   
/*     */   public boolean isPassive() {
/* 166 */     if (this.response.getStatus() == null)
/* 167 */       return false; 
/* 168 */     if (this.response.getStatus().getStatusCode() == null)
/* 169 */       return false; 
/* 170 */     if (this.response.getStatus().getStatusCode().getStatusCode() == null)
/* 171 */       return false; 
/* 172 */     return "urn:oasis:names:tc:SAML:2.0:status:NoPassive".equals(this.response.getStatus().getStatusCode().getStatusCode().getValue());
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/dk/itst/oiosaml/sp/model/OIOResponse.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */