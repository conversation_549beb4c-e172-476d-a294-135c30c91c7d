/*    */ package dk.itst.oiosaml.sp.model;
/*    */ 
/*    */ import javax.xml.namespace.QName;
/*    */ import org.opensaml.xml.XMLObject;
/*    */ import org.opensaml.xml.schema.XSAny;
/*    */ import org.opensaml.xml.schema.impl.XSAnyBuilder;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class AssuranceLevel
/*    */   implements BRSSAMLExtensionObject
/*    */ {
/*    */   public static final String VERSION = "$Id: AssuranceLevel.java 2829 2008-05-13 12:11:31Z jre $";
/*    */   public static final String DEFAULT_ELEMENT_LOCAL_NAME = "AssuranceLevel";
/* 48 */   public static final QName DEFAULT_ELEMENT_NAME = new QName("http://www.eogs.dk/2007/07/brs", "AssuranceLevel", "brs");
/*    */   
/*    */   public static final int PASSWORD_ASSURANCE_LEVEL = 2;
/*    */   
/*    */   public static final int CERTIFICATE_ASSURANCE_LEVEL = 3;
/*    */   
/*    */   public static final int DEFAULT_ASSURANCE_LEVEL = 2;
/*    */   
/*    */   private int value;
/*    */ 
/*    */   
/*    */   public AssuranceLevel(String paramString) {
/*    */     try {
/* 61 */       this.value = Integer.parseInt(paramString);
/* 62 */     } catch (NumberFormatException numberFormatException) {
/* 63 */       this.value = 2;
/*    */     } 
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public int getValue() {
/* 72 */     return this.value;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public void setValue(int paramInt) {
/* 80 */     this.value = paramInt;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public XMLObject getXMLObject() {
/* 87 */     XSAnyBuilder xSAnyBuilder = new XSAnyBuilder();
/* 88 */     XSAny xSAny = xSAnyBuilder.buildObject("http://www.eogs.dk/2007/07/brs", "AssuranceLevel", "brs");
/* 89 */     xSAny.setTextContent(String.valueOf(this.value));
/* 90 */     return (XMLObject)xSAny;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/dk/itst/oiosaml/sp/model/AssuranceLevel.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */