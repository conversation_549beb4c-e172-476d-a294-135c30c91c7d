/*    */ package dk.itst.oiosaml.sp.model;
/*    */ 
/*    */ import dk.itst.oiosaml.common.SAMLUtil;
/*    */ import dk.itst.oiosaml.helper.DeveloperHelper;
/*    */ import dk.itst.oiosaml.logging.Logger;
/*    */ import dk.itst.oiosaml.logging.LoggerFactory;
/*    */ import dk.itst.oiosaml.sp.model.validation.ValidationException;
/*    */ import org.opensaml.saml2.core.Assertion;
/*    */ import org.opensaml.saml2.core.EncryptedAssertion;
/*    */ import org.opensaml.saml2.encryption.Decrypter;
/*    */ import org.opensaml.saml2.encryption.EncryptedElementTypeEncryptedKeyResolver;
/*    */ import org.opensaml.xml.encryption.ChainingEncryptedKeyResolver;
/*    */ import org.opensaml.xml.encryption.DecryptionException;
/*    */ import org.opensaml.xml.encryption.EncryptedKeyResolver;
/*    */ import org.opensaml.xml.encryption.InlineEncryptedKeyResolver;
/*    */ import org.opensaml.xml.encryption.SimpleRetrievalMethodEncryptedKeyResolver;
/*    */ import org.opensaml.xml.security.credential.Credential;
/*    */ import org.opensaml.xml.security.keyinfo.KeyInfoCredentialResolver;
/*    */ import org.opensaml.xml.security.keyinfo.StaticKeyInfoCredentialResolver;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class OIOEncryptedAssertion
/*    */ {
/* 44 */   private static final Logger log = LoggerFactory.getLogger(OIOEncryptedAssertion.class);
/*    */   
/*    */   private final EncryptedAssertion encrypted;
/*    */   
/*    */   public OIOEncryptedAssertion(EncryptedAssertion paramEncryptedAssertion) {
/* 49 */     this.encrypted = paramEncryptedAssertion;
/* 50 */     if (paramEncryptedAssertion.getEncryptedData().getType() == null) {
/* 51 */       paramEncryptedAssertion.getEncryptedData().setType("http://www.w3.org/2001/04/xmlenc#Element");
/*    */     }
/*    */   }
/*    */   
/*    */   public OIOAssertion decryptAssertion(Credential paramCredential) {
/* 56 */     StaticKeyInfoCredentialResolver staticKeyInfoCredentialResolver = new StaticKeyInfoCredentialResolver(paramCredential);
/*    */     
/* 58 */     ChainingEncryptedKeyResolver chainingEncryptedKeyResolver = new ChainingEncryptedKeyResolver();
/* 59 */     chainingEncryptedKeyResolver.getResolverChain().add(new InlineEncryptedKeyResolver());
/* 60 */     chainingEncryptedKeyResolver.getResolverChain().add(new EncryptedElementTypeEncryptedKeyResolver());
/* 61 */     chainingEncryptedKeyResolver.getResolverChain().add(new SimpleRetrievalMethodEncryptedKeyResolver());
/*    */     
/*    */     try {
/* 64 */       if (log.isDebugEnabled()) log.debug("Assertion encrypted: " + this.encrypted);
/*    */       
/* 66 */       Decrypter decrypter = new Decrypter(null, (KeyInfoCredentialResolver)staticKeyInfoCredentialResolver, (EncryptedKeyResolver)chainingEncryptedKeyResolver);
/*    */ 
/*    */ 
/*    */       
/* 70 */       Assertion assertion = decrypter.decrypt(this.encrypted);
/* 71 */       OIOAssertion oIOAssertion = new OIOAssertion(assertion);
/* 72 */       assertion = (Assertion)SAMLUtil.unmarshallElementFromString(oIOAssertion.toXML());
/* 73 */       if (log.isDebugEnabled()) log.debug("Decrypted assertion: " + oIOAssertion.toXML());
/*    */       
/* 75 */       return new OIOAssertion(assertion);
/* 76 */     } catch (DecryptionException decryptionException) {
/* 77 */       DeveloperHelper.log("Unable to decrypt assertion - this might be caused by using Oracle Java without installing the \"Java Cryptography Extension (JCE) Unlimited Strength Jurisdiction Policy Files\".");
/* 78 */       throw new ValidationException(decryptionException);
/*    */     } 
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/dk/itst/oiosaml/sp/model/OIOEncryptedAssertion.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */