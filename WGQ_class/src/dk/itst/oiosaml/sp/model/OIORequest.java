/*     */ package dk.itst.oiosaml.sp.model;
/*     */ 
/*     */ import java.io.ByteArrayOutputStream;
/*     */ import java.io.IOException;
/*     */ import java.security.PublicKey;
/*     */ import java.util.Collection;
/*     */ import java.util.Collections;
/*     */ import java.util.List;
/*     */ import java.util.zip.Deflater;
/*     */ import java.util.zip.DeflaterOutputStream;
/*     */ import org.opensaml.common.SAMLObject;
/*     */ import org.opensaml.common.binding.BasicSAMLMessageContext;
/*     */ import org.opensaml.common.binding.SAMLMessageContext;
/*     */ import org.opensaml.saml2.binding.encoding.HTTPRedirectDeflateEncoder;
/*     */ import org.opensaml.saml2.core.RequestAbstractType;
/*     */ import org.opensaml.ws.message.encoder.MessageEncodingException;
/*     */ import org.opensaml.xml.XMLObject;
/*     */ import org.opensaml.xml.security.credential.Credential;
/*     */ import org.opensaml.xml.util.Base64;
/*     */ import org.opensaml.xml.util.XMLHelper;
/*     */ import org.opensaml.xml.validation.ValidationException;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public abstract class OIORequest
/*     */   extends OIOSamlObject
/*     */ {
/*     */   private final RequestAbstractType request;
/*     */   
/*     */   public OIORequest(RequestAbstractType paramRequestAbstractType) {
/*  57 */     super((XMLObject)paramRequestAbstractType);
/*  58 */     this.request = paramRequestAbstractType;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean isDestinationOK(String paramString) {
/*  69 */     if (this.request.getDestination() == null) return true;
/*     */     
/*  71 */     return (this.request.getDestination() != null && this.request.getDestination().equals(paramString));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean isIssuerOK(String paramString) {
/*  78 */     return (this.request.getIssuer() != null && this.request
/*  79 */       .getIssuer().getValue() != null && this.request
/*  80 */       .getIssuer().getValue().equals(paramString));
/*     */   }
/*     */ 
/*     */   
/*     */   protected final void validateRequest(String paramString1, String paramString2, PublicKey paramPublicKey, List<String> paramList) {
/*  85 */     validateRequest(paramString1, paramString2, Collections.singletonList(paramPublicKey), paramList);
/*     */   }
/*     */   
/*     */   protected final void validateRequest(String paramString1, String paramString2, Collection<PublicKey> paramCollection, List<String> paramList) {
/*     */     try {
/*  90 */       this.request.validate(true);
/*  91 */     } catch (ValidationException validationException) {
/*  92 */       paramList.add(validationException.getMessage());
/*     */     } 
/*  94 */     if (!isDestinationOK(paramString2)) {
/*  95 */       paramList.add("Wrong destination. Expected " + paramString2 + " but was " + this.request.getDestination());
/*     */     }
/*  97 */     if (!isIssuerOK(paramString1)) {
/*  98 */       paramList.add("Wring issuer. Expected " + paramString1 + " but was " + this.request.getIssuer());
/*     */     }
/* 100 */     if (hasSignature()) {
/* 101 */       boolean bool = false;
/* 102 */       for (PublicKey publicKey : paramCollection) {
/* 103 */         if (verifySignature(publicKey)) {
/* 104 */           bool = true;
/*     */         }
/*     */       } 
/* 107 */       if (!bool) {
/* 108 */         paramList.add("Invalid signature in SAMLObject");
/*     */       }
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getID() {
/* 117 */     return this.request.getID();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getIssuer() {
/* 125 */     return (this.request.getIssuer() != null) ? this.request.getIssuer().getValue() : null;
/*     */   }
/*     */   
/*     */   public String getDestination() {
/* 129 */     return this.request.getDestination();
/*     */   }
/*     */   
/*     */   protected class Encoder extends HTTPRedirectDeflateEncoder {
/*     */     public String buildRedirectURL(Credential param1Credential, String param1String) throws MessageEncodingException {
/* 134 */       BasicSAMLMessageContext basicSAMLMessageContext = new BasicSAMLMessageContext();
/*     */       
/* 136 */       basicSAMLMessageContext.setOutboundSAMLMessage((SAMLObject)OIORequest.this.request);
/* 137 */       basicSAMLMessageContext.setRelayState(param1String);
/*     */ 
/*     */       
/* 140 */       basicSAMLMessageContext.setOutboundSAMLMessageSigningCredential(param1Credential);
/*     */       
/* 142 */       String str1 = XMLHelper.nodeToString(marshallMessage((XMLObject)OIORequest.this.request));
/*     */       
/* 144 */       ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
/* 145 */       Deflater deflater = new Deflater(8, true);
/* 146 */       DeflaterOutputStream deflaterOutputStream = new DeflaterOutputStream(byteArrayOutputStream, deflater);
/*     */       try {
/* 148 */         deflaterOutputStream.write(str1.getBytes("UTF-8"));
/* 149 */         deflaterOutputStream.finish();
/* 150 */       } catch (IOException iOException) {
/* 151 */         throw new RuntimeException("Unable to deflate message", iOException);
/*     */       } 
/*     */       
/* 154 */       String str2 = Base64.encodeBytes(byteArrayOutputStream.toByteArray(), 8);
/* 155 */       return buildRedirectURL((SAMLMessageContext)basicSAMLMessageContext, OIORequest.this.request.getDestination(), str2);
/*     */     }
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/dk/itst/oiosaml/sp/model/OIORequest.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */