/*     */ package dk.itst.oiosaml.sp.model;
/*     */ 
/*     */ import dk.itst.oiosaml.common.OIOSAMLConstants;
/*     */ import dk.itst.oiosaml.common.SAMLUtil;
/*     */ import dk.itst.oiosaml.error.Layer;
/*     */ import dk.itst.oiosaml.error.WrappedException;
/*     */ import dk.itst.oiosaml.logging.Logger;
/*     */ import dk.itst.oiosaml.logging.LoggerFactory;
/*     */ import dk.itst.oiosaml.sp.service.session.SessionHandler;
/*     */ import dk.itst.oiosaml.sp.service.util.Utils;
/*     */ import dk.itst.oiosaml.sp.util.LogoutRequestValidationException;
/*     */ import java.security.PublicKey;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Collection;
/*     */ import java.util.Collections;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpSession;
/*     */ import org.joda.time.DateTime;
/*     */ import org.joda.time.DateTimeZone;
/*     */ import org.opensaml.common.binding.BasicSAMLMessageContext;
/*     */ import org.opensaml.saml2.binding.decoding.HTTPPostDecoder;
/*     */ import org.opensaml.saml2.binding.decoding.HTTPRedirectDeflateDecoder;
/*     */ import org.opensaml.saml2.core.LogoutRequest;
/*     */ import org.opensaml.saml2.core.NameID;
/*     */ import org.opensaml.saml2.core.RequestAbstractType;
/*     */ import org.opensaml.saml2.core.SessionIndex;
/*     */ import org.opensaml.saml2.core.impl.LogoutRequestBuilder;
/*     */ import org.opensaml.saml2.core.impl.SessionIndexBuilder;
/*     */ import org.opensaml.ws.message.MessageContext;
/*     */ import org.opensaml.ws.message.decoder.MessageDecodingException;
/*     */ import org.opensaml.ws.message.encoder.MessageEncodingException;
/*     */ import org.opensaml.ws.transport.InTransport;
/*     */ import org.opensaml.ws.transport.http.HttpServletRequestAdapter;
/*     */ import org.opensaml.xml.security.SecurityException;
/*     */ import org.opensaml.xml.security.credential.Credential;
/*     */ import org.opensaml.xml.validation.ValidationException;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class OIOLogoutRequest
/*     */   extends OIORequest
/*     */ {
/*  65 */   private static final Logger log = LoggerFactory.getLogger(OIOLogoutRequest.class);
/*     */   
/*     */   private final LogoutRequest request;
/*     */   
/*     */   public OIOLogoutRequest(LogoutRequest paramLogoutRequest) {
/*  70 */     super((RequestAbstractType)paramLogoutRequest);
/*  71 */     this.request = paramLogoutRequest;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static OIOLogoutRequest fromRedirectRequest(HttpServletRequest paramHttpServletRequest) {
/*  81 */     BasicSAMLMessageContext<LogoutRequest, ?, ?> basicSAMLMessageContext = getMessageContextFromRequest(paramHttpServletRequest);
/*     */     
/*  83 */     HTTPRedirectDeflateDecoder hTTPRedirectDeflateDecoder = new HTTPRedirectDeflateDecoder();
/*     */     
/*     */     try {
/*  86 */       hTTPRedirectDeflateDecoder.decode((MessageContext)basicSAMLMessageContext);
/*  87 */     } catch (MessageDecodingException messageDecodingException) {
/*  88 */       throw new WrappedException(Layer.CLIENT, messageDecodingException);
/*  89 */     } catch (SecurityException securityException) {
/*  90 */       throw new WrappedException(Layer.CLIENT, securityException);
/*     */     } 
/*     */     
/*  93 */     return new OIOLogoutRequest((LogoutRequest)basicSAMLMessageContext.getInboundSAMLMessage());
/*     */   }
/*     */   
/*     */   public static OIOLogoutRequest fromPostRequest(HttpServletRequest paramHttpServletRequest) {
/*  97 */     BasicSAMLMessageContext<LogoutRequest, ?, ?> basicSAMLMessageContext = getMessageContextFromRequest(paramHttpServletRequest);
/*     */     
/*  99 */     HTTPPostDecoder hTTPPostDecoder = new HTTPPostDecoder();
/*     */     
/*     */     try {
/* 102 */       hTTPPostDecoder.decode((MessageContext)basicSAMLMessageContext);
/* 103 */     } catch (MessageDecodingException messageDecodingException) {
/* 104 */       throw new WrappedException(Layer.CLIENT, messageDecodingException);
/* 105 */     } catch (SecurityException securityException) {
/* 106 */       throw new WrappedException(Layer.CLIENT, securityException);
/*     */     } 
/*     */     
/* 109 */     return new OIOLogoutRequest((LogoutRequest)basicSAMLMessageContext.getInboundSAMLMessage());
/*     */   }
/*     */ 
/*     */   
/*     */   private static BasicSAMLMessageContext<LogoutRequest, ?, ?> getMessageContextFromRequest(HttpServletRequest paramHttpServletRequest) {
/* 114 */     BasicSAMLMessageContext<LogoutRequest, ?, ?> basicSAMLMessageContext = new BasicSAMLMessageContext();
/* 115 */     basicSAMLMessageContext.setInboundMessageTransport((InTransport)new HttpServletRequestAdapter(paramHttpServletRequest));
/* 116 */     return basicSAMLMessageContext;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getSessionIndex() {
/* 127 */     String str = null;
/* 128 */     if (this.request.getSessionIndexes() != null && this.request.getSessionIndexes().size() > 0) {
/* 129 */       SessionIndex sessionIndex = this.request.getSessionIndexes().get(0);
/*     */       
/* 131 */       str = sessionIndex.getSessionIndex();
/*     */     } 
/* 133 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean isSessionIndexOK(String paramString) {
/* 142 */     String str = getSessionIndex();
/* 143 */     return (str != null && str.equals(paramString));
/*     */   }
/*     */   
/*     */   public void validateRequest(String paramString1, String paramString2, PublicKey paramPublicKey, String paramString3, String paramString4) throws LogoutRequestValidationException {
/* 147 */     validateRequest(paramString1, paramString2, Collections.singletonList(paramPublicKey), paramString3, paramString4);
/*     */   }
/*     */   
/*     */   public void validateRequest(String paramString1, String paramString2, Collection<PublicKey> paramCollection, String paramString3, String paramString4) throws LogoutRequestValidationException {
/* 151 */     ArrayList<String> arrayList = new ArrayList();
/* 152 */     validateRequest(paramString4, paramString3, paramCollection, arrayList);
/*     */     
/* 154 */     if (paramString1 != null) {
/* 155 */       boolean bool = false;
/* 156 */       for (PublicKey publicKey : paramCollection) {
/* 157 */         if (Utils.verifySignature(paramString1, paramString2, "SAMLRequest", publicKey)) {
/* 158 */           bool = true;
/*     */         }
/*     */       } 
/* 161 */       if (!bool) {
/* 162 */         arrayList.add("Invalid signature");
/*     */       }
/*     */     } 
/*     */     
/* 166 */     if (this.request.getNotOnOrAfter() != null && !this.request.getNotOnOrAfter().isAfterNow()) {
/* 167 */       arrayList.add("LogoutRequest is expired. NotOnOrAfter; " + this.request.getNotOnOrAfter());
/*     */     }
/* 169 */     if (!arrayList.isEmpty()) {
/* 170 */       throw new LogoutRequestValidationException(arrayList);
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static OIOLogoutRequest buildLogoutRequest(HttpSession paramHttpSession, String paramString1, String paramString2, SessionHandler paramSessionHandler) {
/* 183 */     LogoutRequest logoutRequest = (new LogoutRequestBuilder()).buildObject();
/*     */     
/* 185 */     logoutRequest.setID(Utils.generateUUID());
/* 186 */     logoutRequest.setIssueInstant(new DateTime(DateTimeZone.UTC));
/* 187 */     logoutRequest.addNamespace(OIOSAMLConstants.SAML20_NAMESPACE);
/* 188 */     logoutRequest.setDestination(paramString1);
/* 189 */     logoutRequest.setReason("urn:oasis:names:tc:SAML:2.0:logout:user");
/* 190 */     logoutRequest.setIssuer(SAMLUtil.createIssuer(paramString2));
/*     */     
/* 192 */     OIOAssertion oIOAssertion = paramSessionHandler.getAssertion(paramHttpSession.getId());
/* 193 */     if (oIOAssertion != null) {
/* 194 */       NameID nameID = SAMLUtil.createNameID(oIOAssertion.getSubjectNameIDValue());
/* 195 */       nameID.setFormat(oIOAssertion.getAssertion().getSubject().getNameID().getFormat());
/* 196 */       logoutRequest.setNameID(nameID);
/* 197 */       SessionIndex sessionIndex = (new SessionIndexBuilder()).buildObject();
/* 198 */       logoutRequest.getSessionIndexes().add(sessionIndex);
/* 199 */       sessionIndex.setSessionIndex(oIOAssertion.getSessionIndex());
/*     */     } 
/*     */     
/*     */     try {
/* 203 */       if (log.isDebugEnabled()) {
/* 204 */         log.debug("Validate the logoutRequest...");
/*     */       }
/* 206 */       logoutRequest.validate(true);
/* 207 */       if (log.isDebugEnabled()) {
/* 208 */         log.debug("...OK");
/*     */       }
/* 210 */     } catch (ValidationException validationException) {
/* 211 */       throw new WrappedException(Layer.CLIENT, validationException);
/*     */     } 
/*     */     
/* 214 */     return new OIOLogoutRequest(logoutRequest);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getRedirectRequestURL(Credential paramCredential) {
/* 227 */     OIORequest.Encoder encoder = new OIORequest.Encoder(this);
/*     */     
/*     */     try {
/* 230 */       return encoder.buildRedirectURL(paramCredential, null);
/* 231 */     } catch (MessageEncodingException messageEncodingException) {
/* 232 */       throw new WrappedException(Layer.CLIENT, messageEncodingException);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setReason(String paramString) {
/* 240 */     this.request.setReason(paramString);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/dk/itst/oiosaml/sp/model/OIOLogoutRequest.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */