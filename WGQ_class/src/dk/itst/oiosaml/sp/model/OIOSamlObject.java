/*     */ package dk.itst.oiosaml.sp.model;
/*     */ 
/*     */ import dk.itst.oiosaml.common.SAMLUtil;
/*     */ import dk.itst.oiosaml.error.Layer;
/*     */ import dk.itst.oiosaml.error.WrappedException;
/*     */ import dk.itst.oiosaml.logging.Logger;
/*     */ import dk.itst.oiosaml.logging.LoggerFactory;
/*     */ import java.security.PublicKey;
/*     */ import org.opensaml.Configuration;
/*     */ import org.opensaml.common.SignableSAMLObject;
/*     */ import org.opensaml.security.SAMLSignatureProfileValidator;
/*     */ import org.opensaml.ws.soap.soap11.Body;
/*     */ import org.opensaml.ws.soap.soap11.Envelope;
/*     */ import org.opensaml.xml.ElementExtensibleXMLObject;
/*     */ import org.opensaml.xml.Namespace;
/*     */ import org.opensaml.xml.XMLObject;
/*     */ import org.opensaml.xml.io.Marshaller;
/*     */ import org.opensaml.xml.io.MarshallingException;
/*     */ import org.opensaml.xml.security.SecurityException;
/*     */ import org.opensaml.xml.security.SecurityHelper;
/*     */ import org.opensaml.xml.security.credential.Credential;
/*     */ import org.opensaml.xml.security.x509.BasicX509Credential;
/*     */ import org.opensaml.xml.signature.Signature;
/*     */ import org.opensaml.xml.signature.SignatureException;
/*     */ import org.opensaml.xml.signature.SignatureValidator;
/*     */ import org.opensaml.xml.signature.Signer;
/*     */ import org.opensaml.xml.util.Base64;
/*     */ import org.opensaml.xml.util.XMLHelper;
/*     */ import org.opensaml.xml.validation.ValidationException;
/*     */ import org.w3c.dom.Element;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class OIOSamlObject
/*     */ {
/*  68 */   private static final Logger log = LoggerFactory.getLogger(OIOSamlObject.class);
/*     */   
/*     */   private final XMLObject obj;
/*     */   
/*     */   public OIOSamlObject(XMLObject paramXMLObject) {
/*  73 */     if (paramXMLObject == null) throw new IllegalArgumentException("Object cannot be null");
/*     */     
/*  75 */     this.obj = paramXMLObject;
/*     */   }
/*     */ 
/*     */   
/*     */   public String toString() {
/*  80 */     return "Object: " + this.obj;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String toXML() {
/*  87 */     Element element = SAMLUtil.marshallObject(this.obj);
/*  88 */     return XMLHelper.nodeToString(element);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void sign(Credential paramCredential) {
/* 101 */     Signature signature = (Signature)SAMLUtil.buildXMLObject(Signature.class);
/* 102 */     if (!(this.obj instanceof SignableSAMLObject)) {
/* 103 */       throw new IllegalStateException("Object of type " + this.obj.getClass() + " is not signable");
/*     */     }
/*     */     
/* 106 */     this.obj.addNamespace(new Namespace("http://www.w3.org/2000/09/xmldsig#", "ds"));
/*     */     
/* 108 */     signature.setSigningCredential(paramCredential);
/*     */     try {
/* 110 */       SecurityHelper.prepareSignatureParams(signature, paramCredential, null, null);
/* 111 */     } catch (SecurityException securityException) {
/* 112 */       throw new WrappedException(Layer.BUSINESS, securityException);
/*     */     } 
/*     */     
/* 115 */     ((SignableSAMLObject)this.obj).setSignature(signature);
/*     */     
/*     */     try {
/* 118 */       Marshaller marshaller = Configuration.getMarshallerFactory().getMarshaller(this.obj);
/* 119 */       if (marshaller == null) {
/* 120 */         throw new RuntimeException("No marshaller registered for " + this.obj
/* 121 */             .getElementQName() + ", unable to marshall in preperation for signing");
/*     */       }
/* 123 */       marshaller.marshall(this.obj);
/*     */       
/* 125 */       Signer.signObject(signature);
/* 126 */     } catch (MarshallingException marshallingException) {
/* 127 */       log.error("Unable to marshall protocol message in preparation for signing", (Throwable)marshallingException);
/* 128 */       throw new WrappedException(Layer.BUSINESS, marshallingException);
/* 129 */     } catch (SignatureException signatureException) {
/* 130 */       log.error("Unable to sign protocol message", (Throwable)signatureException);
/* 131 */       throw new WrappedException(Layer.BUSINESS, signatureException);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String toBase64() {
/* 141 */     Element element = SAMLUtil.marshallObject(this.obj);
/* 142 */     String str = XMLHelper.nodeToString(element);
/* 143 */     return Base64.encodeBytes(str.getBytes(), 8);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean hasSignature() {
/* 151 */     if (!(this.obj instanceof SignableSAMLObject)) return false; 
/* 152 */     return (((SignableSAMLObject)this.obj).getSignature() != null);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean verifySignature(PublicKey paramPublicKey) {
/* 162 */     if (paramPublicKey == null) {
/* 163 */       throw new IllegalArgumentException("Certificate cannot be null");
/*     */     }
/* 165 */     Signature signature = null;
/* 166 */     if (this.obj instanceof SignableSAMLObject) {
/* 167 */       SignableSAMLObject signableSAMLObject = (SignableSAMLObject)this.obj;
/*     */       
/* 169 */       signature = signableSAMLObject.getSignature();
/* 170 */     } else if (this.obj instanceof ElementExtensibleXMLObject) {
/* 171 */       signature = (Signature)SAMLUtil.getFirstElement((ElementExtensibleXMLObject)this.obj, Signature.class);
/*     */     } 
/*     */     
/* 174 */     if (signature == null) {
/* 175 */       log.warn("No signature present in object " + this.obj);
/* 176 */       return false;
/*     */     } 
/*     */ 
/*     */     
/* 180 */     SAMLSignatureProfileValidator sAMLSignatureProfileValidator = new SAMLSignatureProfileValidator();
/*     */     try {
/* 182 */       sAMLSignatureProfileValidator.validate(signature);
/*     */     }
/* 184 */     catch (Exception exception) {
/* 185 */       log.warn("The signature does not meet the requirements indicated by the SAML profile of the XML signature", exception);
/* 186 */       return false;
/*     */     } 
/*     */ 
/*     */     
/* 190 */     BasicX509Credential basicX509Credential = new BasicX509Credential();
/* 191 */     basicX509Credential.setPublicKey(paramPublicKey);
/* 192 */     SignatureValidator signatureValidator = new SignatureValidator((Credential)basicX509Credential);
/*     */     try {
/* 194 */       signatureValidator.validate(signature);
/* 195 */       return true;
/* 196 */     } catch (ValidationException validationException) {
/* 197 */       log.warn("The signature does not match the signature of the login site", (Throwable)validationException);
/* 198 */       return false;
/*     */     } 
/*     */   }
/*     */   
/*     */   public String toSoapEnvelope() {
/* 203 */     Body body = (Body)SAMLUtil.buildXMLObject(Body.class);
/* 204 */     body.getUnknownXMLObjects().add(this.obj);
/*     */ 
/*     */     
/* 207 */     Envelope envelope = (Envelope)SAMLUtil.buildXMLObject(Envelope.class);
/* 208 */     envelope.setBody(body);
/* 209 */     Marshaller marshaller = Configuration.getMarshallerFactory().getMarshaller((XMLObject)envelope);
/*     */     try {
/* 211 */       Element element = marshaller.marshall((XMLObject)envelope);
/* 212 */       return XMLHelper.nodeToString(element);
/* 213 */     } catch (MarshallingException marshallingException) {
/* 214 */       throw new WrappedException(Layer.CLIENT, marshallingException);
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/dk/itst/oiosaml/sp/model/OIOSamlObject.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */