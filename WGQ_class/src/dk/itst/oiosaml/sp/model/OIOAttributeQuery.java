/*     */ package dk.itst.oiosaml.sp.model;
/*     */ 
/*     */ import dk.itst.oiosaml.common.SAMLUtil;
/*     */ import dk.itst.oiosaml.logging.Audit;
/*     */ import dk.itst.oiosaml.logging.Logger;
/*     */ import dk.itst.oiosaml.logging.LoggerFactory;
/*     */ import dk.itst.oiosaml.logging.Operation;
/*     */ import dk.itst.oiosaml.sp.NameIDFormat;
/*     */ import dk.itst.oiosaml.sp.model.validation.ValidationException;
/*     */ import dk.itst.oiosaml.sp.service.util.SOAPClient;
/*     */ import dk.itst.oiosaml.sp.service.util.Utils;
/*     */ import java.io.IOException;
/*     */ import java.security.cert.Certificate;
/*     */ import java.util.Collection;
/*     */ import java.util.Collections;
/*     */ import org.joda.time.DateTime;
/*     */ import org.opensaml.common.SAMLVersion;
/*     */ import org.opensaml.saml2.core.Attribute;
/*     */ import org.opensaml.saml2.core.AttributeQuery;
/*     */ import org.opensaml.saml2.core.RequestAbstractType;
/*     */ import org.opensaml.saml2.core.Response;
/*     */ import org.opensaml.saml2.core.Subject;
/*     */ import org.opensaml.xml.XMLObject;
/*     */ import org.opensaml.xml.security.credential.Credential;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class OIOAttributeQuery
/*     */   extends OIORequest
/*     */ {
/*  51 */   private static final Logger log = LoggerFactory.getLogger(OIOAttributeQuery.class);
/*     */   
/*     */   private final AttributeQuery request;
/*     */   
/*     */   public OIOAttributeQuery(AttributeQuery paramAttributeQuery) {
/*  56 */     super((RequestAbstractType)paramAttributeQuery);
/*  57 */     this.request = paramAttributeQuery;
/*     */   }
/*     */ 
/*     */   
/*     */   public static OIOAttributeQuery newQuery(String paramString1, String paramString2, NameIDFormat paramNameIDFormat, String paramString3) {
/*  62 */     AttributeQuery attributeQuery = (AttributeQuery)SAMLUtil.buildXMLObject(AttributeQuery.class);
/*  63 */     attributeQuery.setVersion(SAMLVersion.VERSION_20);
/*     */     
/*  65 */     Subject subject = SAMLUtil.createSubject(paramString2, paramString1, (new DateTime()).plusMinutes(5));
/*  66 */     subject.getSubjectConfirmations().clear();
/*  67 */     subject.getNameID().setFormat(paramNameIDFormat.getFormat());
/*     */     
/*  69 */     attributeQuery.setSubject(subject);
/*     */     
/*  71 */     attributeQuery.setDestination(paramString1);
/*  72 */     attributeQuery.setIssueInstant(new DateTime());
/*  73 */     attributeQuery.setID(Utils.generateUUID());
/*  74 */     attributeQuery.setIssuer(SAMLUtil.createIssuer(paramString3));
/*  75 */     attributeQuery.setConsent("urn:oasis:names:tc:SAML:2.0:consent:current-implicit");
/*     */     
/*  77 */     return new OIOAttributeQuery(attributeQuery);
/*     */   }
/*     */   
/*     */   public void addAttribute(String paramString1, String paramString2) {
/*  81 */     Attribute attribute = (Attribute)SAMLUtil.buildXMLObject(Attribute.class);
/*  82 */     attribute.setName(paramString1);
/*  83 */     attribute.setNameFormat(paramString2);
/*  84 */     this.request.getAttributes().add(attribute);
/*     */   }
/*     */   
/*     */   public OIOAssertion executeQuery(SOAPClient paramSOAPClient, Credential paramCredential, String paramString1, String paramString2, boolean paramBoolean1, Certificate paramCertificate, boolean paramBoolean2) throws IOException {
/*  88 */     return executeQuery(paramSOAPClient, paramCredential, paramString1, paramString2, paramBoolean1, Collections.singletonList(paramCertificate), paramBoolean2);
/*     */   }
/*     */   
/*     */   public OIOAssertion executeQuery(SOAPClient paramSOAPClient, Credential paramCredential, String paramString1, String paramString2, boolean paramBoolean1, Collection<? extends Certificate> paramCollection, boolean paramBoolean2) throws IOException {
/*     */     try {
/*  93 */       sign(paramCredential);
/*  94 */       Audit.log(Operation.ATTRIBUTEQUERY, true, getID(), toXML());
/*     */       
/*  96 */       XMLObject xMLObject = paramSOAPClient.wsCall(this, getDestination(), paramString1, paramString2, paramBoolean1);
/*  97 */       if (!(xMLObject instanceof Response)) throw new IllegalStateException("Received wrong type from IdP (expected Response): " + xMLObject);
/*     */       
/*  99 */       OIOResponse oIOResponse = new OIOResponse((Response)xMLObject);
/* 100 */       if (log.isDebugEnabled()) log.debug("Received attribute query response: " + oIOResponse.toXML());
/*     */       
/* 102 */       Audit.log(Operation.ATTRIBUTEQUERY, false, getID(), oIOResponse.toXML());
/*     */       
/* 104 */       oIOResponse.decryptAssertion(paramCredential, paramBoolean2);
/* 105 */       oIOResponse.validateResponse((String)null, paramCollection, false);
/*     */       
/* 107 */       return oIOResponse.getAssertion();
/* 108 */     } catch (ValidationException validationException) {
/* 109 */       Audit.logError(Operation.ATTRIBUTEQUERY, false, getID(), (Throwable)validationException);
/* 110 */       throw validationException;
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/dk/itst/oiosaml/sp/model/OIOAttributeQuery.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */