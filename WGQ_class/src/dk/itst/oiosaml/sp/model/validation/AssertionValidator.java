package dk.itst.oiosaml.sp.model.validation;

import dk.itst.oiosaml.sp.model.OIOAssertion;

public interface AssertionValidator {
  void validate(OIOAssertion paramOIOAssertion, String paramString1, String paramString2) throws ValidationException;
}


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/dk/itst/oiosaml/sp/model/validation/AssertionValidator.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */