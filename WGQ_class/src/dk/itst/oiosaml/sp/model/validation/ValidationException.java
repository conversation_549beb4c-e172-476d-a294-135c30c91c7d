/*    */ package dk.itst.oiosaml.sp.model.validation;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class ValidationException
/*    */   extends RuntimeException
/*    */ {
/*    */   private static final long serialVersionUID = -5914214959146816794L;
/*    */   
/*    */   public ValidationException(String paramString) {
/* 31 */     super(paramString);
/*    */   }
/*    */   
/*    */   public ValidationException(Exception paramException) {
/* 35 */     super(paramException);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/dk/itst/oiosaml/sp/model/validation/ValidationException.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */