/*    */ package dk.itst.oiosaml.sp.model.validation;
/*    */ 
/*    */ import dk.itst.oiosaml.sp.model.OIOAssertion;
/*    */ import org.joda.time.DateTime;
/*    */ import org.opensaml.saml2.core.Assertion;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class BasicAssertionValidator
/*    */   implements AssertionValidator
/*    */ {
/*    */   public void validate(OIOAssertion paramOIOAssertion, String paramString1, String paramString2) throws ValidationException {
/* 34 */     Assertion assertion = paramOIOAssertion.getAssertion();
/*    */ 
/*    */     
/* 37 */     if (assertion.getIssueInstant() == null) {
/* 38 */       throw new ValidationException("The assertion must contain a IssueInstant");
/*    */     }
/*    */ 
/*    */     
/* 42 */     if (assertion.getIssuer() == null || assertion
/* 43 */       .getIssuer().getValue() == null) {
/* 44 */       throw new ValidationException("The assertion must contain an Issuer");
/*    */     }
/*    */ 
/*    */     
/* 48 */     if (paramOIOAssertion.getSubjectNameIDValue() == null) {
/* 49 */       throw new ValidationException("The assertion must contain a Subject/NameID");
/*    */     }
/*    */ 
/*    */     
/* 53 */     if (!paramOIOAssertion.getAudience().contains(paramString1)) {
/* 54 */       throw new ValidationException("The assertion must contain the service provider " + paramString1 + " within the Audience list: " + paramOIOAssertion.getAudience());
/*    */     }
/*    */     
/* 57 */     DateTime dateTime = paramOIOAssertion.getConditionTime();
/* 58 */     if (dateTime == null || !dateTime.isAfterNow())
/* 59 */       throw new ValidationException("Condition NotOnOrAfter is after now: " + dateTime); 
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/dk/itst/oiosaml/sp/model/validation/BasicAssertionValidator.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */