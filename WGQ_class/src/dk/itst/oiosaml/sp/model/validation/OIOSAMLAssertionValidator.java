/*    */ package dk.itst.oiosaml.sp.model.validation;
/*    */ 
/*    */ import dk.itst.oiosaml.logging.Logger;
/*    */ import dk.itst.oiosaml.logging.LoggerFactory;
/*    */ import dk.itst.oiosaml.sp.model.OIOAssertion;
/*    */ import org.joda.time.DateTime;
/*    */ import org.opensaml.saml2.core.Assertion;
/*    */ import org.opensaml.saml2.core.AuthnContext;
/*    */ import org.opensaml.saml2.core.AuthnContextClassRef;
/*    */ import org.opensaml.saml2.core.AuthnStatement;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class OIOSAMLAssertionValidator
/*    */   extends BasicAssertionValidator
/*    */ {
/* 39 */   private static final Logger log = LoggerFactory.getLogger(OIOSAMLAssertionValidator.class);
/*    */   
/*    */   public void validate(OIOAssertion paramOIOAssertion, String paramString1, String paramString2) throws ValidationException {
/* 42 */     super.validate(paramOIOAssertion, paramString1, paramString2);
/*    */     
/* 44 */     Assertion assertion = paramOIOAssertion.getAssertion();
/*    */     
/* 46 */     DateTime dateTime = paramOIOAssertion.getConfirmationTime();
/* 47 */     if (dateTime == null || !dateTime.isAfterNow()) {
/* 48 */       throw new ValidationException("Subject Confirmation Data is expired: " + dateTime + " before " + new DateTime());
/*    */     }
/*    */ 
/*    */     
/* 52 */     if (assertion.getAuthnStatements().size() != 1) {
/* 53 */       throw new ValidationException("The assertion must contain exactly one AuthnStatement. Was " + assertion.getAuthnStatements().size());
/*    */     }
/*    */     
/* 56 */     int i = paramOIOAssertion.getAssuranceLevel();
/* 57 */     String str = null;
/* 58 */     AuthnStatement authnStatement = assertion.getAuthnStatements().get(0);
/* 59 */     AuthnContext authnContext = authnStatement.getAuthnContext();
/* 60 */     if (authnContext != null) {
/* 61 */       AuthnContextClassRef authnContextClassRef = authnContext.getAuthnContextClassRef();
/* 62 */       if (authnContextClassRef != null) {
/* 63 */         str = authnContextClassRef.getAuthnContextClassRef();
/*    */       }
/*    */     } 
/* 66 */     if (i == 2 && 
/* 67 */       !"urn:oasis:names:tc:SAML:2.0:ac:classes:Password".equals(str)) {
/* 68 */       log.warn("The assuranceLevel attribute " + i + "  in the assertion does not correspond with the value of AuthnStatement/AuthnContext/AuthnContextClassRef: " + str);
/* 69 */     } else if (i == 3 && 
/* 70 */       !"urn:oasis:names:tc:SAML:2.0:ac:classes:X509".equals(str)) {
/* 71 */       log.warn("The assuranceLevel attribute " + i + "  in the assertion does not correspond with the value of AuthnStatement/AuthnContext/AuthnContextClassRef: " + str);
/*    */     } 
/*    */ 
/*    */     
/* 75 */     if (paramOIOAssertion.getSessionIndex() == null) {
/* 76 */       throw new ValidationException("The assertion must contain a AuthnStatement@SessionIndex");
/*    */     }
/*    */     
/* 79 */     if (assertion.getAttributeStatements().size() != 1) {
/* 80 */       throw new ValidationException("The assertion must contain exactly one AttributeStatement. Contains " + assertion.getAttributeStatements().size());
/*    */     }
/*    */     
/* 83 */     if (assertion.getAuthzDecisionStatements().size() != 0) {
/* 84 */       throw new ValidationException("The assertion must not contain a AuthzDecisionStatement. Contains " + assertion.getAuthzDecisionStatements().size());
/*    */     }
/*    */ 
/*    */     
/* 88 */     if (!paramOIOAssertion.checkRecipient(paramString2)) {
/* 89 */       throw new ValidationException("The assertion must contain the recipient " + paramString2);
/*    */     }
/*    */ 
/*    */     
/* 93 */     if (authnStatement.getSessionNotOnOrAfter() != null && 
/* 94 */       !authnStatement.getSessionNotOnOrAfter().isAfterNow())
/* 95 */       throw new ValidationException("The assertion must have a AuthnStatement@SessionNotOnOrAfter and it must not have expired. SessionNotOnOrAfter: " + authnStatement.getSessionNotOnOrAfter()); 
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/dk/itst/oiosaml/sp/model/validation/OIOSAMLAssertionValidator.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */