/*     */ package dk.itst.oiosaml.sp.model;
/*     */ 
/*     */ import dk.itst.oiosaml.common.SAMLUtil;
/*     */ import dk.itst.oiosaml.error.Layer;
/*     */ import dk.itst.oiosaml.error.WrappedException;
/*     */ import dk.itst.oiosaml.logging.Logger;
/*     */ import dk.itst.oiosaml.logging.LoggerFactory;
/*     */ import dk.itst.oiosaml.sp.NameIDFormat;
/*     */ import dk.itst.oiosaml.sp.service.session.SessionHandler;
/*     */ import dk.itst.oiosaml.sp.service.util.Utils;
/*     */ import org.joda.time.DateTime;
/*     */ import org.joda.time.DateTimeZone;
/*     */ import org.opensaml.saml2.core.AuthnRequest;
/*     */ import org.opensaml.saml2.core.NameIDPolicy;
/*     */ import org.opensaml.saml2.core.RequestAbstractType;
/*     */ import org.opensaml.ws.message.encoder.MessageEncodingException;
/*     */ import org.opensaml.xml.security.credential.Credential;
/*     */ import org.opensaml.xml.validation.ValidationException;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class OIOAuthnRequest
/*     */   extends OIORequest
/*     */ {
/*  44 */   private static final Logger log = LoggerFactory.getLogger(OIOAuthnRequest.class);
/*     */   
/*     */   private final AuthnRequest request;
/*     */   
/*     */   private final String relayState;
/*     */   
/*     */   public OIOAuthnRequest(AuthnRequest paramAuthnRequest, String paramString) {
/*  51 */     super((RequestAbstractType)paramAuthnRequest);
/*  52 */     this.request = paramAuthnRequest;
/*  53 */     this.relayState = paramString;
/*     */   }
/*     */   
/*     */   public static OIOAuthnRequest buildAuthnRequest(String paramString1, String paramString2, String paramString3, SessionHandler paramSessionHandler, String paramString4, String paramString5) {
/*  57 */     AuthnRequest authnRequest = (AuthnRequest)SAMLUtil.buildXMLObject(AuthnRequest.class);
/*     */     
/*  59 */     authnRequest.setIssuer(SAMLUtil.createIssuer(paramString2));
/*  60 */     authnRequest.setID(Utils.generateUUID());
/*  61 */     authnRequest.setForceAuthn(Boolean.FALSE);
/*  62 */     authnRequest.setIssueInstant(new DateTime(DateTimeZone.UTC));
/*  63 */     authnRequest.setProtocolBinding(paramString3);
/*  64 */     authnRequest.setDestination(paramString1);
/*  65 */     authnRequest.setAssertionConsumerServiceURL(paramString5);
/*     */     
/*     */     try {
/*  68 */       if (log.isDebugEnabled())
/*  69 */         log.debug("Validate the authnRequest..."); 
/*  70 */       authnRequest.validate(true);
/*  71 */       if (log.isDebugEnabled())
/*  72 */         log.debug("...OK"); 
/*  73 */     } catch (ValidationException validationException) {
/*  74 */       throw new WrappedException(Layer.CLIENT, validationException);
/*     */     } 
/*  76 */     return new OIOAuthnRequest(authnRequest, paramString4);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getRedirectURL(Credential paramCredential) {
/*  85 */     OIORequest.Encoder encoder = new OIORequest.Encoder(this);
/*     */     try {
/*  87 */       return encoder.buildRedirectURL(paramCredential, getRelayState());
/*  88 */     } catch (MessageEncodingException messageEncodingException) {
/*  89 */       throw new WrappedException(Layer.CLIENT, messageEncodingException);
/*     */     } 
/*     */   }
/*     */   
/*     */   public void setNameIDPolicy(String paramString, boolean paramBoolean) {
/*  94 */     if (paramString == null || paramString.trim().equals(""))
/*     */       return; 
/*  96 */     NameIDFormat nameIDFormat = NameIDFormat.valueOf(paramString.toUpperCase());
/*  97 */     NameIDPolicy nameIDPolicy = (NameIDPolicy)SAMLUtil.buildXMLObject(NameIDPolicy.class);
/*  98 */     nameIDPolicy.setAllowCreate(Boolean.valueOf(paramBoolean));
/*  99 */     nameIDPolicy.setFormat(nameIDFormat.getFormat());
/* 100 */     nameIDPolicy.setSPNameQualifier(this.request.getIssuer().getValue());
/*     */     
/* 102 */     this.request.setNameIDPolicy(nameIDPolicy);
/*     */   }
/*     */   
/*     */   public String getRelayState() {
/* 106 */     return this.relayState;
/*     */   }
/*     */   
/*     */   public boolean isForceAuthn() {
/* 110 */     return (this.request.isForceAuthn() != null && this.request.isForceAuthn().booleanValue());
/*     */   }
/*     */   
/*     */   public void setForceAuthn(boolean paramBoolean) {
/* 114 */     this.request.setForceAuthn(Boolean.valueOf(paramBoolean));
/*     */   }
/*     */   
/*     */   public void setPasive(boolean paramBoolean) {
/* 118 */     this.request.setIsPassive(Boolean.valueOf(paramBoolean));
/*     */   }
/*     */   
/*     */   public boolean isPassive() {
/* 122 */     return (this.request.isPassive() != null && this.request.isPassive().booleanValue());
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/dk/itst/oiosaml/sp/model/OIOAuthnRequest.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */