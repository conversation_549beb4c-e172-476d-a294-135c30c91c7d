/*    */ package dk.itst.oiosaml.sp;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public final class UserAssertionHolder
/*    */ {
/* 35 */   private static final ThreadLocal<UserAssertion> assertion = new ThreadLocal<>();
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public static UserAssertion get() {
/* 41 */     return assertion.get();
/*    */   }
/*    */   
/*    */   public static void set(UserAssertion paramUserAssertion) {
/* 45 */     assertion.set(paramUserAssertion);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/dk/itst/oiosaml/sp/UserAssertionHolder.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */