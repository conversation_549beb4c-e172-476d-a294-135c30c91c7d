/*     */ package dk.itst.oiosaml.sp;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ import java.util.ArrayList;
/*     */ import java.util.List;
/*     */ import org.opensaml.xml.util.Base64;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class UserAttribute
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 7213348395041737852L;
/*     */   private final String name;
/*     */   private final String friendlyName;
/*     */   private final List<String> values;
/*     */   private final String format;
/*     */   
/*     */   public UserAttribute(String paramString1, String paramString2, List<String> paramList, String paramString3) {
/*  43 */     this.name = paramString1;
/*  44 */     this.friendlyName = paramString2;
/*  45 */     this.values = paramList;
/*  46 */     this.format = paramString3;
/*     */   }
/*     */   
/*     */   public String getName() {
/*  50 */     return this.name;
/*     */   }
/*     */   
/*     */   public String getFriendlyName() {
/*  54 */     return this.friendlyName;
/*     */   }
/*     */   
/*     */   public List<String> getValues() {
/*  58 */     if (this.values == null) {
/*  59 */       return new ArrayList<>();
/*     */     }
/*  61 */     return this.values;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getValue() {
/*  69 */     if (this.values == null || this.values.size() == 0) {
/*  70 */       return null;
/*     */     }
/*  72 */     return this.values.get(0);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<byte[]> getBase64Values() {
/*  80 */     ArrayList<byte[]> arrayList = new ArrayList();
/*  81 */     if (this.values == null) {
/*  82 */       return (List)new ArrayList<>();
/*     */     }
/*  84 */     for (String str : this.values) {
/*  85 */       arrayList.add(Base64.decode(str));
/*     */     }
/*  87 */     return (List<byte[]>)arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public byte[] getBase64Value() {
/*  95 */     if (this.values == null || this.values.size() == 0) {
/*  96 */       return null;
/*     */     }
/*  98 */     return Base64.decode(this.values.get(0));
/*     */   }
/*     */ 
/*     */   
/*     */   public String getFormat() {
/* 103 */     return this.format;
/*     */   }
/*     */   
/*     */   public static UserAttribute create(String paramString1, String paramString2) {
/* 107 */     if (paramString2 != null && paramString2.trim().equals("")) {
/* 108 */       paramString2 = null;
/*     */     }
/* 110 */     return new UserAttribute(paramString1, null, null, paramString2);
/*     */   }
/*     */ 
/*     */   
/*     */   public String toString() {
/* 115 */     return this.name + " (" + this.friendlyName + "): " + this.values;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/dk/itst/oiosaml/sp/UserAttribute.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */