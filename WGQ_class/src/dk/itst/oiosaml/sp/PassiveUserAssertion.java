/*     */ package dk.itst.oiosaml.sp;
/*     */ 
/*     */ import java.security.cert.X509Certificate;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Collection;
/*     */ import java.util.Collections;
/*     */ import java.util.Date;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class PassiveUserAssertion
/*     */   implements UserAssertion
/*     */ {
/*     */   private final String userId;
/*     */   
/*     */   public PassiveUserAssertion(String paramString) {
/*  37 */     this.userId = paramString;
/*     */   }
/*     */   
/*     */   public Collection<UserAttribute> getAllAttributes() {
/*  41 */     return Collections.unmodifiableCollection(new ArrayList<>());
/*     */   }
/*     */   
/*     */   public String getAssertionId() {
/*  45 */     return null;
/*     */   }
/*     */   
/*     */   public int getAssuranceLevel() {
/*  49 */     return 0;
/*     */   }
/*     */   
/*     */   public UserAttribute getAttribute(String paramString) {
/*  53 */     return null;
/*     */   }
/*     */   
/*     */   public String getCPRNumber() {
/*  57 */     return null;
/*     */   }
/*     */   
/*     */   public String getCVRNumberIdentifier() {
/*  61 */     return null;
/*     */   }
/*     */   
/*     */   public String getCertificateSerialNumber() {
/*  65 */     return null;
/*     */   }
/*     */   
/*     */   public String getCommonName() {
/*  69 */     return null;
/*     */   }
/*     */   
/*     */   public Date getIssueTime() {
/*  73 */     return new Date();
/*     */   }
/*     */   
/*     */   public String getIssuer() {
/*  77 */     return null;
/*     */   }
/*     */   
/*     */   public String getMail() {
/*  81 */     return null;
/*     */   }
/*     */   
/*     */   public NameIDFormat getNameIDFormat() {
/*  85 */     return NameIDFormat.UNSPECIFIED;
/*     */   }
/*     */   
/*     */   public String getOrganizationName() {
/*  89 */     return null;
/*     */   }
/*     */   
/*     */   public String getOrganizationUnit() {
/*  93 */     return null;
/*     */   }
/*     */   
/*     */   public String getPIDNumber() {
/*  97 */     return null;
/*     */   }
/*     */   
/*     */   public String getPostalAddress() {
/* 101 */     return null;
/*     */   }
/*     */   
/*     */   public String getPseudonym() {
/* 105 */     return null;
/*     */   }
/*     */   
/*     */   public String getRIDNumber() {
/* 109 */     return null;
/*     */   }
/*     */   
/*     */   public Date getSessionExpireTime() {
/* 113 */     return null;
/*     */   }
/*     */   
/*     */   public String getSpecificationVersion() {
/* 117 */     return null;
/*     */   }
/*     */   
/*     */   public String getSubject() {
/* 121 */     return this.userId;
/*     */   }
/*     */   
/*     */   public String getSurname() {
/* 125 */     return null;
/*     */   }
/*     */   
/*     */   public String getTitle() {
/* 129 */     return null;
/*     */   }
/*     */   
/*     */   public String getUniqueAccountKey() {
/* 133 */     return null;
/*     */   }
/*     */   
/*     */   public X509Certificate getUserCertificate() {
/* 137 */     return null;
/*     */   }
/*     */   
/*     */   public String getUserId() {
/* 141 */     return this.userId;
/*     */   }
/*     */   
/*     */   public String getXML() {
/* 145 */     return "";
/*     */   }
/*     */   
/*     */   public boolean isAuthenticated() {
/* 149 */     return false;
/*     */   }
/*     */   
/*     */   public boolean isOCESProfileCompliant() {
/* 153 */     return false;
/*     */   }
/*     */   
/*     */   public boolean isOIOSAMLCompliant() {
/* 157 */     return false;
/*     */   }
/*     */   
/*     */   public boolean isPersistentPseudonymProfileCompliant() {
/* 161 */     return false;
/*     */   }
/*     */   
/*     */   public boolean isSigned() {
/* 165 */     return false;
/*     */   }
/*     */   
/*     */   public Boolean isYouthCertificate() {
/* 169 */     return null;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/dk/itst/oiosaml/sp/PassiveUserAssertion.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */