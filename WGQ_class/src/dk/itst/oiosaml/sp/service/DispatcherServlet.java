/*     */ package dk.itst.oiosaml.sp.service;
/*     */ 
/*     */ import dk.itst.oiosaml.configuration.SAMLConfigurationFactory;
/*     */ import dk.itst.oiosaml.error.WrappedException;
/*     */ import dk.itst.oiosaml.logging.Audit;
/*     */ import dk.itst.oiosaml.logging.Logger;
/*     */ import dk.itst.oiosaml.logging.LoggerFactory;
/*     */ import dk.itst.oiosaml.security.CredentialRepository;
/*     */ import dk.itst.oiosaml.sp.bindings.BindingHandlerFactory;
/*     */ import dk.itst.oiosaml.sp.bindings.DefaultBindingHandlerFactory;
/*     */ import dk.itst.oiosaml.sp.configuration.ConfigurationHandler;
/*     */ import dk.itst.oiosaml.sp.metadata.IdpMetadata;
/*     */ import dk.itst.oiosaml.sp.metadata.SPMetadata;
/*     */ import dk.itst.oiosaml.sp.service.session.SessionHandler;
/*     */ import dk.itst.oiosaml.sp.service.session.SessionHandlerFactory;
/*     */ import dk.itst.oiosaml.sp.service.util.Utils;
/*     */ import java.io.IOException;
/*     */ import java.io.PrintWriter;
/*     */ import java.security.KeyStoreException;
/*     */ import java.security.NoSuchAlgorithmException;
/*     */ import java.security.cert.CertificateException;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import javax.servlet.ServletConfig;
/*     */ import javax.servlet.ServletContext;
/*     */ import javax.servlet.ServletException;
/*     */ import javax.servlet.ServletRequest;
/*     */ import javax.servlet.ServletResponse;
/*     */ import javax.servlet.http.HttpServlet;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import org.apache.commons.configuration.Configuration;
/*     */ import org.apache.velocity.VelocityContext;
/*     */ import org.apache.velocity.app.VelocityEngine;
/*     */ import org.apache.velocity.context.Context;
/*     */ import org.opensaml.xml.security.credential.Credential;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class DispatcherServlet
/*     */   extends HttpServlet
/*     */ {
/*     */   private static final long serialVersionUID = 45789427728055436L;
/*  76 */   private static final Logger log = LoggerFactory.getLogger(DispatcherServlet.class);
/*     */   
/*     */   private transient IdpMetadata idpMetadata;
/*     */   
/*     */   private transient SPMetadata spMetadata;
/*     */   private Configuration configuration;
/*     */   private Credential credential;
/*  83 */   private final Map<String, SAMLHandler> handlers = new HashMap<>();
/*     */   
/*     */   private boolean initialized = false;
/*     */   
/*     */   private transient VelocityEngine engine;
/*     */   
/*     */   private BindingHandlerFactory bindingHandlerFactory;
/*     */   private SessionHandlerFactory sessionHandlerFactory;
/*     */   private ServletContext servletContext;
/*     */   
/*     */   public final void init(ServletConfig paramServletConfig) throws ServletException {
/*  94 */     setHandler((SAMLHandler)new ConfigurationHandler(), "configure");
/*     */     
/*  96 */     this.servletContext = paramServletConfig.getServletContext();
/*     */     
/*     */     try {
/*  99 */       initServlet();
/* 100 */     } catch (Exception exception) {
/* 101 */       exception.printStackTrace();
/*     */     } 
/*     */     
/* 104 */     this.engine = new VelocityEngine();
/* 105 */     this.engine.setProperty("resource.loader", "classpath");
/* 106 */     this.engine.setProperty("classpath.resource.loader.class", "org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader");
/*     */     
/*     */     try {
/* 109 */       this.engine.init();
/* 110 */     } catch (Exception exception) {
/* 111 */       throw new RuntimeException(exception);
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   private void initServlet() throws WrappedException, NoSuchAlgorithmException, CertificateException, KeyStoreException, IOException {
/*     */     try {
/* 118 */       if (!this.initialized) {
/* 119 */         setConfiguration(SAMLConfigurationFactory.getConfiguration().getSystemConfiguration());
/*     */         
/* 121 */         this.handlers.putAll(Utils.getHandlers(this.configuration, this.servletContext));
/* 122 */         if (log.isDebugEnabled()) {
/* 123 */           log.debug("Found handlers: " + this.handlers);
/*     */         }
/* 125 */         setHandler(new IndexHandler(), "");
/* 126 */         this.sessionHandlerFactory = SessionHandlerFactory.Factory.newInstance(this.configuration);
/* 127 */         this.sessionHandlerFactory.getHandler().resetReplayProtection(this.configuration
/* 128 */             .getInt("common.saml2.loggedinhandler.numusedassertionids"));
/*     */         
/* 130 */         if (this.configuration.getBoolean("oiosaml-sp.develmode", false)) {
/* 131 */           log.warn("Running in devel mode");
/*     */           return;
/*     */         } 
/* 134 */         setBindingHandler((BindingHandlerFactory)new DefaultBindingHandlerFactory());
/* 135 */         setIdPMetadata(IdpMetadata.getInstance());
/* 136 */         setSPMetadata(SPMetadata.getInstance());
/* 137 */         setCredential((Credential)(new CredentialRepository()).getCredential(SAMLConfigurationFactory.getConfiguration()
/* 138 */               .getKeystore(), this.configuration.getString("oiosaml-sp.certificate.password")));
/*     */         
/* 140 */         this.initialized = true;
/*     */       } 
/* 142 */     } catch (IllegalStateException illegalStateException) {
/*     */       try {
/* 144 */         this.handlers.putAll(Utils.getHandlers(SAMLConfigurationFactory.getConfiguration().getCommonConfiguration(), this.servletContext));
/*     */       }
/* 146 */       catch (IOException iOException) {
/* 147 */         log.error("Unable to load config", illegalStateException);
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   protected final void doPut(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws ServletException, IOException {
/* 154 */     doPost(paramHttpServletRequest, paramHttpServletResponse);
/*     */   }
/*     */ 
/*     */   
/*     */   protected final void doDelete(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws ServletException, IOException {
/* 159 */     doGet(paramHttpServletRequest, paramHttpServletResponse);
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   protected final void doGet(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws ServletException, IOException {
/*     */     try {
/* 166 */       initServlet();
/* 167 */     } catch (Exception exception) {
/* 168 */       exception.printStackTrace();
/*     */     } 
/*     */     
/* 171 */     String str = paramHttpServletRequest.getRequestURI().substring(paramHttpServletRequest.getRequestURI().lastIndexOf("/") + 1);
/* 172 */     Audit.init(paramHttpServletRequest);
/*     */ 
/*     */ 
/*     */     
/* 176 */     if (this.handlers.containsKey(str)) {
/*     */       try {
/* 178 */         SAMLHandler sAMLHandler = this.handlers.get(str);
/* 179 */         SessionHandler sessionHandler = (this.sessionHandlerFactory != null) ? this.sessionHandlerFactory.getHandler() : null;
/*     */         
/* 181 */         RequestContext requestContext = new RequestContext(paramHttpServletRequest, paramHttpServletResponse, this.idpMetadata, this.spMetadata, this.credential, this.configuration, sessionHandler, this.bindingHandlerFactory);
/*     */         
/* 183 */         sAMLHandler.handleGet(requestContext);
/* 184 */       } catch (Exception exception) {
/* 185 */         Audit.logError(str, false, exception);
/* 186 */         handleError(paramHttpServletRequest, paramHttpServletResponse, exception);
/*     */       } 
/*     */     } else {
/* 189 */       throw new UnsupportedOperationException(str + ", allowed: " + this.handlers.keySet());
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   protected void doPost(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws ServletException, IOException {
/*     */     try {
/* 196 */       initServlet();
/* 197 */     } catch (Exception exception) {
/* 198 */       exception.printStackTrace();
/*     */     } 
/*     */     
/* 201 */     String str = paramHttpServletRequest.getRequestURI().substring(paramHttpServletRequest.getRequestURI().lastIndexOf("/") + 1);
/* 202 */     Audit.init(paramHttpServletRequest);
/*     */ 
/*     */ 
/*     */     
/* 206 */     if (this.handlers.containsKey(str)) {
/*     */       try {
/* 208 */         SAMLHandler sAMLHandler = this.handlers.get(str);
/* 209 */         SessionHandler sessionHandler = (this.sessionHandlerFactory != null) ? this.sessionHandlerFactory.getHandler() : null;
/*     */         
/* 211 */         RequestContext requestContext = new RequestContext(paramHttpServletRequest, paramHttpServletResponse, this.idpMetadata, this.spMetadata, this.credential, this.configuration, sessionHandler, this.bindingHandlerFactory);
/*     */         
/* 213 */         sAMLHandler.handlePost(requestContext);
/* 214 */       } catch (Exception exception) {
/* 215 */         Audit.logError(str, false, exception);
/* 216 */         handleError(paramHttpServletRequest, paramHttpServletResponse, exception);
/*     */       } 
/*     */     } else {
/* 219 */       throw new UnsupportedOperationException(str);
/*     */     } 
/*     */   }
/*     */   
/*     */   public void setInitialized(boolean paramBoolean) {
/* 224 */     this.initialized = paramBoolean;
/*     */   }
/*     */   
/*     */   public boolean isInitialized() {
/* 228 */     return this.initialized;
/*     */   }
/*     */   
/*     */   public final void setCredential(Credential paramCredential) {
/* 232 */     this.credential = paramCredential;
/*     */   }
/*     */   
/*     */   public final void setConfiguration(Configuration paramConfiguration) {
/* 236 */     this.configuration = paramConfiguration;
/*     */   }
/*     */   
/*     */   public final void setSPMetadata(SPMetadata paramSPMetadata) {
/* 240 */     this.spMetadata = paramSPMetadata;
/*     */   }
/*     */   
/*     */   public final void setIdPMetadata(IdpMetadata paramIdpMetadata) {
/* 244 */     this.idpMetadata = paramIdpMetadata;
/*     */   }
/*     */   
/*     */   public void setHandler(SAMLHandler paramSAMLHandler, String paramString) {
/* 248 */     this.handlers.put(paramString, paramSAMLHandler);
/*     */   }
/*     */   
/*     */   public void setBindingHandler(BindingHandlerFactory paramBindingHandlerFactory) {
/* 252 */     this.bindingHandlerFactory = paramBindingHandlerFactory;
/*     */   }
/*     */   
/*     */   public void setSessionHandlerFactory(SessionHandlerFactory paramSessionHandlerFactory) {
/* 256 */     this.sessionHandlerFactory = paramSessionHandlerFactory;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void handleError(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse, Exception paramException) throws ServletException, IOException {
/* 267 */     String str1 = "Unable to validate SAML message!";
/*     */     
/* 269 */     log.error("Unable to validate Response", paramException);
/*     */     
/* 271 */     String str2 = null;
/* 272 */     if (this.configuration != null) {
/* 273 */       str2 = this.configuration.getString("oiosaml-sp.errors", null);
/*     */     }
/*     */     
/* 276 */     if (str2 != null) {
/* 277 */       if (this.configuration.getBoolean("oiosaml-sp.showerror", false)) {
/* 278 */         paramHttpServletRequest.setAttribute("error", paramException.getMessage());
/* 279 */         paramHttpServletRequest.setAttribute("exception", paramException);
/*     */       } else {
/* 281 */         paramHttpServletRequest.setAttribute("error", str1);
/* 282 */         paramHttpServletRequest.setAttribute("exception", null);
/*     */       } 
/* 284 */       paramHttpServletRequest.getRequestDispatcher(str2).forward((ServletRequest)paramHttpServletRequest, (ServletResponse)paramHttpServletResponse);
/*     */     } else {
/* 286 */       VelocityContext velocityContext = new VelocityContext();
/*     */       
/* 288 */       if (this.configuration.getBoolean("oiosaml-sp.showerror", false)) {
/* 289 */         velocityContext.put("error", paramException.getMessage());
/* 290 */         velocityContext.put("exception", paramException);
/*     */       } else {
/* 292 */         velocityContext.put("error", str1);
/* 293 */         velocityContext.put("exception", null);
/*     */       } 
/*     */       
/* 296 */       paramHttpServletResponse.setContentType("text/html");
/* 297 */       paramHttpServletResponse.setStatus(500);
/*     */       
/*     */       try {
/* 300 */         this.engine.mergeTemplate("error.vm", "UTF-8", (Context)velocityContext, paramHttpServletResponse.getWriter());
/* 301 */       } catch (Exception exception) {
/* 302 */         log.error("Unable to render error template", exception);
/* 303 */         throw new ServletException(exception);
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public void destroy() {
/* 311 */     if (this.sessionHandlerFactory != null) {
/* 312 */       this.sessionHandlerFactory.close();
/*     */     }
/* 314 */     SessionHandlerFactory.Factory.close();
/*     */   }
/*     */   
/*     */   private class IndexHandler implements SAMLHandler {
/*     */     public void handleGet(RequestContext param1RequestContext) throws ServletException, IOException {
/* 319 */       PrintWriter printWriter = param1RequestContext.getResponse().getWriter();
/*     */       
/* 321 */       printWriter.println("<html><head><title>SAML Endppoints</title></head><body><h1>SAML Endpoints</h1>");
/* 322 */       printWriter.println("<ul>");
/* 323 */       for (Map.Entry entry : DispatcherServlet.this.handlers.entrySet()) {
/* 324 */         printWriter.println("<li><a href=\"");
/* 325 */         printWriter.print((String)entry.getKey());
/* 326 */         printWriter.print("\">");
/* 327 */         printWriter.print((String)entry.getKey());
/* 328 */         printWriter.print("</a>: ");
/* 329 */         printWriter.print(entry.getValue());
/* 330 */         printWriter.println("</li>");
/*     */       } 
/* 332 */       printWriter.println("</ul>");
/* 333 */       printWriter.println("</body></html>");
/*     */     }
/*     */     
/*     */     private IndexHandler() {}
/*     */     
/*     */     public void handlePost(RequestContext param1RequestContext) throws ServletException, IOException {}
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/dk/itst/oiosaml/sp/service/DispatcherServlet.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */