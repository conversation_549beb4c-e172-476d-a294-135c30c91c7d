/*     */ package dk.itst.oiosaml.sp.service;
/*     */ 
/*     */ import dk.itst.oiosaml.common.SAMLUtil;
/*     */ import dk.itst.oiosaml.error.Layer;
/*     */ import dk.itst.oiosaml.error.WrappedException;
/*     */ import dk.itst.oiosaml.logging.Audit;
/*     */ import dk.itst.oiosaml.logging.Logger;
/*     */ import dk.itst.oiosaml.logging.LoggerFactory;
/*     */ import dk.itst.oiosaml.logging.Operation;
/*     */ import dk.itst.oiosaml.sp.metadata.IdpMetadata;
/*     */ import dk.itst.oiosaml.sp.model.OIOAssertion;
/*     */ import dk.itst.oiosaml.sp.model.OIOLogoutRequest;
/*     */ import dk.itst.oiosaml.sp.model.OIOLogoutResponse;
/*     */ import dk.itst.oiosaml.sp.util.LogoutRequestValidationException;
/*     */ import java.io.IOException;
/*     */ import java.io.InputStream;
/*     */ import java.io.PrintWriter;
/*     */ import java.io.UnsupportedEncodingException;
/*     */ import javax.servlet.ServletException;
/*     */ import javax.servlet.ServletInputStream;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import org.apache.commons.io.IOUtils;
/*     */ import org.opensaml.saml2.core.LogoutRequest;
/*     */ import org.opensaml.ws.soap.soap11.Body;
/*     */ import org.opensaml.ws.soap.soap11.Envelope;
/*     */ import org.opensaml.xml.XMLObject;
/*     */ import org.opensaml.xml.security.credential.Credential;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class LogoutServiceSOAPHandler
/*     */   implements SAMLHandler
/*     */ {
/*  63 */   private static final Logger log = LoggerFactory.getLogger(LogoutServiceSOAPHandler.class);
/*     */   
/*     */   private static OIOLogoutRequest extractRequest(HttpServletRequest paramHttpServletRequest) throws IOException {
/*  66 */     ServletInputStream servletInputStream = paramHttpServletRequest.getInputStream();
/*     */ 
/*     */     
/*  69 */     String str = IOUtils.toString((InputStream)servletInputStream, "UTF-8");
/*  70 */     XMLObject xMLObject = SAMLUtil.unmarshallElementFromString(str);
/*     */     
/*  72 */     if (log.isDebugEnabled()) log.debug("Request..:" + str);
/*     */     
/*  74 */     if (xMLObject != null && xMLObject instanceof Envelope) {
/*  75 */       Envelope envelope = (Envelope)xMLObject;
/*  76 */       Body body = envelope.getBody();
/*  77 */       xMLObject = body.getUnknownXMLObjects().get(0);
/*  78 */       if (xMLObject != null && xMLObject instanceof LogoutRequest) {
/*  79 */         LogoutRequest logoutRequest = (LogoutRequest)xMLObject;
/*  80 */         return new OIOLogoutRequest(logoutRequest);
/*     */       } 
/*     */     } 
/*  83 */     throw new RuntimeException("SOAP request did not contain a LogoutRequest on the body");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void handlePost(RequestContext paramRequestContext) throws ServletException, IOException {
/*  90 */     String str1 = "urn:oasis:names:tc:SAML:2.0:status:Success";
/*  91 */     String str2 = null;
/*     */     
/*  93 */     OIOLogoutRequest oIOLogoutRequest = extractRequest(paramRequestContext.getRequest());
/*  94 */     Audit.log(Operation.LOGOUT_SOAP, false, oIOLogoutRequest.getID(), oIOLogoutRequest.toXML());
/*     */     
/*     */     try {
/*  97 */       String str3 = oIOLogoutRequest.getSessionIndex();
/*  98 */       String str4 = paramRequestContext.getSessionHandler().getRelatedSessionId(str3);
/*     */       
/* 100 */       OIOAssertion oIOAssertion = paramRequestContext.getSessionHandler().getAssertion(str4);
/* 101 */       String str5 = null;
/* 102 */       if (oIOAssertion != null) {
/* 103 */         str5 = oIOAssertion.getIssuer();
/*     */       }
/* 105 */       if (str5 == null) {
/* 106 */         log.warn("LogoutRequest received over SOAP for unknown user");
/* 107 */         str1 = "urn:oasis:names:tc:SAML:2.0:status:NoSupportedIDP";
/*     */       } else {
/*     */         try {
/* 110 */           IdpMetadata.Metadata metadata = paramRequestContext.getIdpMetadata().getMetadata(str5);
/*     */           
/* 112 */           oIOLogoutRequest.validateRequest(null, null, metadata.getPublicKeys(), paramRequestContext.getSpMetadata().getSingleLogoutServiceSOAPLocation(), metadata.getEntityID());
/* 113 */           paramRequestContext.getSessionHandler().logOut(str4);
/*     */           
/* 115 */           Audit.log(Operation.LOGOUT, oIOAssertion.getSubjectNameIDValue());
/* 116 */         } catch (LogoutRequestValidationException logoutRequestValidationException) {
/* 117 */           str2 = logoutRequestValidationException.getMessage();
/* 118 */           str1 = "urn:oasis:names:tc:SAML:2.0:status:AuthnFailed";
/*     */         } 
/*     */       } 
/* 121 */     } catch (Throwable throwable) {
/* 122 */       str1 = "urn:oasis:names:tc:SAML:2.0:status:AuthnFailed";
/* 123 */       str2 = (throwable instanceof WrappedException) ? throwable.getCause().getMessage() : throwable.getMessage();
/* 124 */       Audit.logError(Operation.LOGOUT_SOAP, false, oIOLogoutRequest.getID(), throwable);
/*     */     } 
/*     */     
/* 127 */     if (log.isDebugEnabled()) log.debug("Logout status: " + str1 + ", message: " + str2);
/*     */     
/* 129 */     OIOLogoutResponse oIOLogoutResponse = OIOLogoutResponse.fromRequest(oIOLogoutRequest, str1, str2, paramRequestContext.getSpMetadata().getEntityID(), null);
/* 130 */     returnResponse(paramRequestContext.getResponse(), oIOLogoutResponse, paramRequestContext.getCredential());
/* 131 */     Audit.log(Operation.LOGOUT_SOAP, true, oIOLogoutRequest.getID(), oIOLogoutResponse.toXML());
/*     */   }
/*     */   
/*     */   public void handleGet(RequestContext paramRequestContext) throws IOException {
/* 135 */     String str = paramRequestContext.getRequest().getParameter("wsdl");
/* 136 */     HttpServletResponse httpServletResponse = paramRequestContext.getResponse();
/* 137 */     if (str != null) {
/*     */       try {
/* 139 */         if (log.isDebugEnabled())
/* 140 */           log.debug("Returning wsdl..."); 
/* 141 */         PrintWriter printWriter = httpServletResponse.getWriter();
/* 142 */         httpServletResponse.setContentType("text/xml");
/* 143 */         httpServletResponse.setCharacterEncoding("UTF-8");
/* 144 */         InputStream inputStream = LogoutServiceSOAPHandler.class.getResourceAsStream("/SAML2LogoutService.wsdl");
/* 145 */         IOUtils.copy(inputStream, printWriter);
/*     */         
/* 147 */         inputStream.close();
/* 148 */         printWriter.flush();
/*     */         return;
/* 150 */       } catch (IOException iOException) {
/* 151 */         throw new WrappedException(Layer.CLIENT, iOException);
/*     */       } 
/*     */     }
/*     */     
/* 155 */     httpServletResponse.sendError(412, "No argument wsdl on get request. Use POST for SOAP requests.");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static void returnResponse(HttpServletResponse paramHttpServletResponse, OIOLogoutResponse paramOIOLogoutResponse, Credential paramCredential) {
/*     */     byte[] arrayOfByte;
/* 168 */     paramOIOLogoutResponse.sign(paramCredential);
/*     */ 
/*     */     
/* 171 */     String str = paramOIOLogoutResponse.toSoapEnvelope();
/*     */     
/* 173 */     if (log.isDebugEnabled()) {
/* 174 */       log.debug("Response..: " + str);
/*     */     }
/*     */     
/*     */     try {
/* 178 */       arrayOfByte = str.getBytes("UTF-8");
/* 179 */     } catch (UnsupportedEncodingException unsupportedEncodingException) {
/* 180 */       throw new WrappedException(Layer.CLIENT, unsupportedEncodingException);
/*     */     } 
/* 182 */     paramHttpServletResponse.setContentLength(arrayOfByte.length);
/* 183 */     paramHttpServletResponse.setCharacterEncoding("UTF-8");
/* 184 */     paramHttpServletResponse.setContentType("text/xml");
/* 185 */     paramHttpServletResponse.setStatus(200);
/*     */     try {
/* 187 */       paramHttpServletResponse.getOutputStream().write(arrayOfByte);
/* 188 */     } catch (IOException iOException) {
/* 189 */       throw new WrappedException(Layer.CLIENT, iOException);
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/dk/itst/oiosaml/sp/service/LogoutServiceSOAPHandler.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */