/*     */ package dk.itst.oiosaml.sp.service;
/*     */ 
/*     */ import dk.itst.oiosaml.logging.Audit;
/*     */ import dk.itst.oiosaml.logging.Logger;
/*     */ import dk.itst.oiosaml.logging.LoggerFactory;
/*     */ import dk.itst.oiosaml.logging.Operation;
/*     */ import dk.itst.oiosaml.sp.AuthenticationHandler;
/*     */ import dk.itst.oiosaml.sp.LogoutAuthenticationHandler;
/*     */ import dk.itst.oiosaml.sp.metadata.IdpMetadata;
/*     */ import dk.itst.oiosaml.sp.model.OIOAssertion;
/*     */ import dk.itst.oiosaml.sp.model.OIOLogoutRequest;
/*     */ import dk.itst.oiosaml.sp.model.OIOLogoutResponse;
/*     */ import dk.itst.oiosaml.sp.service.util.Utils;
/*     */ import dk.itst.oiosaml.sp.util.LogoutRequestValidationException;
/*     */ import java.io.IOException;
/*     */ import javax.servlet.ServletException;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpSession;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class LogoutServiceHTTPRedirectHandler
/*     */   implements SAMLHandler
/*     */ {
/*     */   private static final long serialVersionUID = -6035256219067030678L;
/*     */   public static final String VERSION = "$Id: LogoutServiceHTTPRedirectHandler.java 2890 2008-05-16 16:18:56Z jre $";
/*  59 */   private static final Logger log = LoggerFactory.getLogger(LogoutServiceHTTPRedirectHandler.class);
/*     */   
/*     */   public void handleGet(RequestContext paramRequestContext) throws ServletException, IOException {
/*  62 */     HttpServletRequest httpServletRequest = paramRequestContext.getRequest();
/*  63 */     HttpSession httpSession = paramRequestContext.getSession();
/*     */     
/*  65 */     String str1 = httpServletRequest.getParameter("SAMLRequest");
/*  66 */     String str2 = httpServletRequest.getParameter("RelayState");
/*  67 */     String str3 = httpServletRequest.getParameter("SigAlg");
/*  68 */     String str4 = httpServletRequest.getParameter("Signature");
/*     */     
/*  70 */     if (log.isDebugEnabled()) {
/*  71 */       log.debug("samlRequest...:" + str1);
/*  72 */       log.debug("relayState....:" + str2);
/*  73 */       log.debug("sigAlg........:" + str3);
/*  74 */       log.debug("signature.....:" + str4);
/*     */     } 
/*     */     
/*  77 */     OIOLogoutRequest oIOLogoutRequest = OIOLogoutRequest.fromRedirectRequest(httpServletRequest);
/*  78 */     if (log.isDebugEnabled()) {
/*  79 */       log.debug("Got InboundSAMLMessage..:" + oIOLogoutRequest.toXML());
/*     */     }
/*  81 */     Audit.log(Operation.LOGOUTREQUEST, false, oIOLogoutRequest.getID(), oIOLogoutRequest.toXML());
/*     */     
/*  83 */     String str5 = "urn:oasis:names:tc:SAML:2.0:status:Success";
/*  84 */     String str6 = null;
/*     */     
/*  86 */     OIOAssertion oIOAssertion = paramRequestContext.getSessionHandler().getAssertion(httpSession.getId());
/*  87 */     String str7 = null;
/*  88 */     if (oIOAssertion != null) {
/*  89 */       str7 = oIOAssertion.getIssuer();
/*     */     }
/*  91 */     if (str7 == null) {
/*  92 */       log.warn("LogoutRequest received but user is not logged in");
/*  93 */       str7 = oIOLogoutRequest.getIssuer();
/*     */     } 
/*  95 */     if (str7 == null) {
/*  96 */       throw new RuntimeException("User is not logged in, and there is no Issuer in the LogoutRequest. Unable to continue.");
/*     */     }
/*  98 */     IdpMetadata.Metadata metadata = paramRequestContext.getIdpMetadata().getMetadata(str7);
/*     */     
/*     */     try {
/* 101 */       oIOLogoutRequest.validateRequest(str4, httpServletRequest.getQueryString(), metadata.getPublicKeys(), paramRequestContext.getSpMetadata().getSingleLogoutServiceHTTPRedirectLocation(), metadata.getEntityID());
/*     */ 
/*     */       
/* 104 */       if (oIOAssertion != null) {
/* 105 */         log.info("Logging user out via SLO HTTP Redirect: " + oIOAssertion.getSubjectNameIDValue());
/*     */       } else {
/*     */         
/* 108 */         log.info("Logging user out via SLO HTTP Redirect without active session");
/*     */       } 
/* 110 */       paramRequestContext.getSessionHandler().logOut(httpSession);
/* 111 */       invokeAuthenticationHandler(paramRequestContext);
/*     */     }
/* 113 */     catch (LogoutRequestValidationException logoutRequestValidationException) {
/* 114 */       str6 = logoutRequestValidationException.getMessage();
/* 115 */       str5 = "urn:oasis:names:tc:SAML:2.0:status:AuthnFailed";
/*     */     } 
/*     */     
/* 118 */     if (log.isDebugEnabled()) {
/* 119 */       log.debug("Logout status: " + str5 + ", message: " + str6);
/*     */     }
/*     */ 
/*     */     
/* 123 */     OIOLogoutResponse oIOLogoutResponse = OIOLogoutResponse.fromRequest(oIOLogoutRequest, str5, str6, paramRequestContext.getSpMetadata().getEntityID(), metadata.getSingleLogoutServiceResponseLocation());
/* 124 */     String str8 = oIOLogoutResponse.getRedirectURL(paramRequestContext.getCredential(), str2);
/*     */     
/* 126 */     Audit.log(Operation.LOGOUTRESPONSE, true, oIOLogoutResponse.getID(), oIOLogoutResponse.toXML());
/*     */     
/* 128 */     if (log.isDebugEnabled())
/* 129 */       log.debug("sendRedirect to..:" + str8); 
/* 130 */     paramRequestContext.getResponse().sendRedirect(str8);
/*     */   }
/*     */   
/*     */   public void handlePost(RequestContext paramRequestContext) throws ServletException, IOException {
/* 134 */     HttpServletRequest httpServletRequest = paramRequestContext.getRequest();
/* 135 */     HttpSession httpSession = paramRequestContext.getSession();
/*     */     
/* 137 */     String str1 = httpServletRequest.getParameter("SAMLRequest");
/* 138 */     String str2 = httpServletRequest.getParameter("RelayState");
/* 139 */     String str3 = httpServletRequest.getParameter("SigAlg");
/* 140 */     String str4 = httpServletRequest.getParameter("Signature");
/*     */     
/* 142 */     if (log.isDebugEnabled()) {
/* 143 */       log.debug("samlRequest...:" + str1);
/* 144 */       log.debug("relayState....:" + str2);
/* 145 */       log.debug("sigAlg........:" + str3);
/* 146 */       log.debug("signature.....:" + str4);
/*     */     } 
/*     */     
/* 149 */     OIOLogoutRequest oIOLogoutRequest = OIOLogoutRequest.fromPostRequest(httpServletRequest);
/* 150 */     if (log.isDebugEnabled()) {
/* 151 */       log.debug("Got InboundSAMLMessage..:" + oIOLogoutRequest.toXML());
/*     */     }
/*     */     
/* 154 */     Audit.log(Operation.LOGOUTREQUEST, false, oIOLogoutRequest.getID(), oIOLogoutRequest.toXML());
/*     */     
/* 156 */     String str5 = "urn:oasis:names:tc:SAML:2.0:status:Success";
/* 157 */     String str6 = null;
/*     */     
/* 159 */     OIOAssertion oIOAssertion = paramRequestContext.getSessionHandler().getAssertion(httpSession.getId());
/* 160 */     String str7 = null;
/* 161 */     if (oIOAssertion != null) {
/* 162 */       str7 = oIOAssertion.getIssuer();
/*     */     }
/* 164 */     if (str7 == null) {
/* 165 */       log.warn("LogoutRequest received but user is not logged in");
/* 166 */       str7 = oIOLogoutRequest.getIssuer();
/*     */     } 
/* 168 */     if (str7 == null) {
/* 169 */       throw new RuntimeException("User is not logged in, and there is no Issuer in the LogoutRequest. Unable to continue.");
/*     */     }
/*     */     
/* 172 */     IdpMetadata.Metadata metadata = paramRequestContext.getIdpMetadata().getMetadata(str7);
/*     */     
/*     */     try {
/* 175 */       oIOLogoutRequest.validateRequest(str4, httpServletRequest.getQueryString(), metadata.getPublicKeys(), paramRequestContext.getSpMetadata().getSingleLogoutServiceHTTPPostLocation(), metadata.getEntityID());
/*     */ 
/*     */       
/* 178 */       if (oIOAssertion != null) {
/* 179 */         log.info("Logging user out via SLO HTTP POST: " + oIOAssertion.getSubjectNameIDValue());
/*     */       } else {
/*     */         
/* 182 */         log.info("Logging user out via SLO HTTP POST without active session");
/*     */       } 
/* 184 */       paramRequestContext.getSessionHandler().logOut(httpSession);
/* 185 */       invokeAuthenticationHandler(paramRequestContext);
/*     */     }
/* 187 */     catch (LogoutRequestValidationException logoutRequestValidationException) {
/* 188 */       str6 = logoutRequestValidationException.getMessage();
/* 189 */       str5 = "urn:oasis:names:tc:SAML:2.0:status:AuthnFailed";
/*     */     } 
/*     */     
/* 192 */     if (log.isDebugEnabled()) {
/* 193 */       log.debug("Logout status: " + str5 + ", message: " + str6);
/*     */     }
/*     */ 
/*     */ 
/*     */     
/* 198 */     OIOLogoutResponse oIOLogoutResponse = OIOLogoutResponse.fromRequest(oIOLogoutRequest, str5, str6, paramRequestContext.getSpMetadata().getEntityID(), metadata.getSingleLogoutServiceResponseLocation());
/* 199 */     String str8 = oIOLogoutResponse.getRedirectURL(paramRequestContext.getCredential(), str2);
/*     */     
/* 201 */     Audit.log(Operation.LOGOUTRESPONSE, true, oIOLogoutResponse.getID(), oIOLogoutResponse.toXML());
/*     */     
/* 203 */     if (log.isDebugEnabled())
/* 204 */       log.debug("sendRedirect to..:" + str8); 
/* 205 */     paramRequestContext.getResponse().sendRedirect(str8);
/*     */   }
/*     */   
/*     */   private static void invokeAuthenticationHandler(RequestContext paramRequestContext) {
/* 209 */     String str = paramRequestContext.getConfiguration().getString("oiosaml-sp.authenticationhandler", null);
/* 210 */     if (str != null) {
/* 211 */       log.debug("Authentication handler: " + str);
/*     */       
/* 213 */       AuthenticationHandler authenticationHandler = (AuthenticationHandler)Utils.newInstance(paramRequestContext.getConfiguration(), "oiosaml-sp.authenticationhandler");
/* 214 */       if (authenticationHandler instanceof LogoutAuthenticationHandler) {
/* 215 */         ((LogoutAuthenticationHandler)authenticationHandler).userLoggedOut(paramRequestContext.getRequest(), paramRequestContext.getResponse());
/*     */       }
/*     */     } else {
/*     */       
/* 219 */       log.debug("No authentication handler configured");
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/dk/itst/oiosaml/sp/service/LogoutServiceHTTPRedirectHandler.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */