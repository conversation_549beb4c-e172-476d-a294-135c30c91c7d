/*     */ package dk.itst.oiosaml.sp.service;
/*     */ 
/*     */ import dk.itst.oiosaml.logging.Audit;
/*     */ import dk.itst.oiosaml.logging.Logger;
/*     */ import dk.itst.oiosaml.logging.LoggerFactory;
/*     */ import dk.itst.oiosaml.logging.Operation;
/*     */ import dk.itst.oiosaml.sp.metadata.IdpMetadata;
/*     */ import dk.itst.oiosaml.sp.model.OIOLogoutResponse;
/*     */ import java.io.IOException;
/*     */ import javax.servlet.ServletException;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpSession;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class LogoutHTTPResponseHandler
/*     */   implements SAMLHandler
/*     */ {
/*     */   private static final long serialVersionUID = 2487601130738744767L;
/*  49 */   private static final Logger log = LoggerFactory.getLogger(LogoutHTTPResponseHandler.class);
/*     */ 
/*     */   
/*     */   public static final String VERSION = "$Id: LogoutHTTPResponseHandler.java 2950 2008-05-28 08:22:34Z jre $";
/*     */ 
/*     */   
/*     */   public void handleGet(RequestContext paramRequestContext) throws ServletException, IOException {
/*  56 */     HttpServletRequest httpServletRequest = paramRequestContext.getRequest();
/*  57 */     HttpSession httpSession = paramRequestContext.getSession();
/*  58 */     if (log.isDebugEnabled()) {
/*  59 */       log.debug("Calling URL.:" + httpServletRequest.getRequestURI() + "?" + httpServletRequest
/*  60 */           .getQueryString());
/*     */       
/*  62 */       log.debug("samlResponse...:" + httpServletRequest.getParameter("SAMLResponse"));
/*  63 */       log.debug("relayState....:" + httpServletRequest.getParameter("RelayState"));
/*  64 */       log.debug("sigAlg........:" + httpServletRequest.getParameter("SigAlg"));
/*  65 */       log.debug("signature.....:" + httpServletRequest.getParameter("Signature"));
/*     */     } 
/*     */     
/*  68 */     OIOLogoutResponse oIOLogoutResponse = OIOLogoutResponse.fromHttpRedirect(httpServletRequest);
/*     */     
/*  70 */     Audit.log(Operation.LOGOUTREQUEST, false, oIOLogoutResponse.getInResponseTo(), oIOLogoutResponse.toXML());
/*     */     
/*  72 */     String str1 = paramRequestContext.getSessionHandler().removeEntityIdForRequest(oIOLogoutResponse.getInResponseTo());
/*  73 */     IdpMetadata.Metadata metadata = paramRequestContext.getIdpMetadata().getMetadata(str1);
/*  74 */     oIOLogoutResponse.validate(null, paramRequestContext.getSpMetadata().getSingleLogoutServiceHTTPRedirectResponseLocation(), httpServletRequest.getParameter("Signature"), httpServletRequest.getQueryString(), metadata.getPublicKeys());
/*     */     
/*  76 */     paramRequestContext.getSessionHandler().logOut(httpSession);
/*     */     
/*  78 */     Audit.log(Operation.LOGOUT, null);
/*     */     
/*  80 */     String str2 = paramRequestContext.getConfiguration().getString("oiosaml-sp.uri.home");
/*  81 */     if (log.isDebugEnabled()) {
/*  82 */       log.debug("sendRedirect to..:" + str2);
/*     */     }
/*  84 */     if (str2 == null) str2 = httpServletRequest.getContextPath();
/*     */ 
/*     */     
/*  87 */     paramRequestContext.getResponse().sendRedirect(str2);
/*     */   }
/*     */   
/*     */   public void handlePost(RequestContext paramRequestContext) throws ServletException, IOException {
/*  91 */     HttpServletRequest httpServletRequest = paramRequestContext.getRequest();
/*  92 */     HttpSession httpSession = paramRequestContext.getSession();
/*     */     
/*  94 */     if (log.isDebugEnabled()) {
/*  95 */       log.debug("samlResponse...:" + httpServletRequest.getParameter("SAMLResponse"));
/*     */     }
/*     */     
/*  98 */     OIOLogoutResponse oIOLogoutResponse = OIOLogoutResponse.fromPostRequest(httpServletRequest);
/*     */     
/* 100 */     Audit.log(Operation.LOGOUTREQUEST, false, oIOLogoutResponse.getInResponseTo(), oIOLogoutResponse.toXML());
/*     */     
/* 102 */     String str1 = paramRequestContext.getSessionHandler().removeEntityIdForRequest(oIOLogoutResponse.getInResponseTo());
/* 103 */     IdpMetadata.Metadata metadata = paramRequestContext.getIdpMetadata().getMetadata(str1);
/*     */     
/* 105 */     oIOLogoutResponse.validate(null, paramRequestContext.getSpMetadata().getSingleLogoutServiceHTTPPostResponseLocation(), metadata.getPublicKeys());
/*     */     
/* 107 */     paramRequestContext.getSessionHandler().logOut(httpSession);
/*     */     
/* 109 */     Audit.log(Operation.LOGOUT, null);
/*     */     
/* 111 */     String str2 = paramRequestContext.getConfiguration().getString("oiosaml-sp.uri.home");
/* 112 */     if (log.isDebugEnabled()) {
/* 113 */       log.debug("sendRedirect to..:" + str2);
/*     */     }
/* 115 */     if (str2 == null) str2 = httpServletRequest.getContextPath();
/*     */ 
/*     */     
/* 118 */     paramRequestContext.getResponse().sendRedirect(str2);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/dk/itst/oiosaml/sp/service/LogoutHTTPResponseHandler.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */