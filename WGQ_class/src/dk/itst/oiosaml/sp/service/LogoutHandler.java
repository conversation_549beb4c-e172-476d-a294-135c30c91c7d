/*    */ package dk.itst.oiosaml.sp.service;
/*    */ 
/*    */ import dk.itst.oiosaml.logging.Audit;
/*    */ import dk.itst.oiosaml.logging.Logger;
/*    */ import dk.itst.oiosaml.logging.LoggerFactory;
/*    */ import dk.itst.oiosaml.logging.Operation;
/*    */ import dk.itst.oiosaml.sp.AuthenticationHandler;
/*    */ import dk.itst.oiosaml.sp.LogoutAuthenticationHandler;
/*    */ import dk.itst.oiosaml.sp.metadata.IdpMetadata;
/*    */ import dk.itst.oiosaml.sp.model.OIOAssertion;
/*    */ import dk.itst.oiosaml.sp.model.OIOLogoutRequest;
/*    */ import dk.itst.oiosaml.sp.service.util.Utils;
/*    */ import java.io.IOException;
/*    */ import javax.servlet.ServletException;
/*    */ import javax.servlet.http.HttpSession;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class LogoutHandler
/*    */   implements SAMLHandler
/*    */ {
/*    */   private static final long serialVersionUID = 3843822219113371749L;
/*    */   public static final String VERSION = "$Id: LogoutHandler.java 2950 2008-05-28 08:22:34Z jre $";
/* 48 */   private static final Logger log = LoggerFactory.getLogger(LogoutHandler.class);
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public void handleGet(RequestContext paramRequestContext) throws ServletException, IOException {
/* 55 */     HttpSession httpSession = paramRequestContext.getSession();
/*    */ 
/*    */     
/* 58 */     if (!paramRequestContext.getSessionHandler().isLoggedIn(httpSession.getId())) {
/* 59 */       paramRequestContext.getSessionHandler().logOut(httpSession);
/* 60 */       String str = paramRequestContext.getConfiguration().getString("oiosaml-sp.uri.home", paramRequestContext.getRequest().getContextPath());
/* 61 */       paramRequestContext.getResponse().sendRedirect(str);
/*    */       
/*    */       return;
/*    */     } 
/* 65 */     OIOAssertion oIOAssertion = paramRequestContext.getSessionHandler().getAssertion(httpSession.getId());
/* 66 */     String str1 = oIOAssertion.getAssertion().getIssuer().getValue();
/* 67 */     IdpMetadata.Metadata metadata = paramRequestContext.getIdpMetadata().getMetadata(str1);
/*    */     
/* 69 */     OIOLogoutRequest oIOLogoutRequest = OIOLogoutRequest.buildLogoutRequest(httpSession, metadata.getSingleLogoutServiceLocation(), paramRequestContext.getSpMetadata().getEntityID(), paramRequestContext.getSessionHandler());
/* 70 */     String str2 = oIOLogoutRequest.getRedirectRequestURL(paramRequestContext.getCredential());
/*    */     
/* 72 */     Audit.log(Operation.LOGOUTREQUEST, true, oIOLogoutRequest.getID(), oIOLogoutRequest.toXML());
/*    */     
/* 74 */     paramRequestContext.getSessionHandler().registerRequest(oIOLogoutRequest.getID(), metadata.getEntityID());
/* 75 */     paramRequestContext.getSessionHandler().logOut(httpSession);
/*    */     
/* 77 */     invokeAuthenticationHandler(paramRequestContext);
/*    */     
/* 79 */     if (log.isDebugEnabled()) log.debug("Redirect to..:" + str2); 
/* 80 */     Audit.log(Operation.LOGOUT, oIOAssertion.getSubjectNameIDValue());
/*    */     
/* 82 */     paramRequestContext.getResponse().sendRedirect(str2);
/*    */   }
/*    */   
/*    */   public void handlePost(RequestContext paramRequestContext) throws ServletException, IOException {
/* 86 */     throw new UnsupportedOperationException();
/*    */   }
/*    */   
/*    */   private static void invokeAuthenticationHandler(RequestContext paramRequestContext) {
/* 90 */     String str = paramRequestContext.getConfiguration().getString("oiosaml-sp.authenticationhandler", null);
/* 91 */     if (str != null) {
/* 92 */       log.debug("Authentication handler: " + str);
/*    */       
/* 94 */       AuthenticationHandler authenticationHandler = (AuthenticationHandler)Utils.newInstance(paramRequestContext.getConfiguration(), "oiosaml-sp.authenticationhandler");
/* 95 */       if (authenticationHandler instanceof LogoutAuthenticationHandler) {
/* 96 */         ((LogoutAuthenticationHandler)authenticationHandler).userLoggedOut(paramRequestContext.getRequest(), paramRequestContext.getResponse());
/*    */       }
/*    */     } else {
/* 99 */       log.debug("No authentication handler configured");
/*    */     } 
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/dk/itst/oiosaml/sp/service/LogoutHandler.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */