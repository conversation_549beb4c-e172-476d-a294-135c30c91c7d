/*    */ package dk.itst.oiosaml.sp.service;
/*    */ 
/*    */ import java.io.IOException;
/*    */ import javax.servlet.ServletException;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class MetadataHandler
/*    */   implements SAMLHandler
/*    */ {
/*    */   public void handleGet(RequestContext paramRequestContext) throws ServletException, IOException {
/* 33 */     if (paramRequestContext.getRequest().getParameter("raw") != null) {
/* 34 */       paramRequestContext.getResponse().setContentType("text/plain");
/*    */     } else {
/* 36 */       paramRequestContext.getResponse().setCharacterEncoding("utf-8");
/*    */     } 
/*    */     
/* 39 */     boolean bool = (paramRequestContext.getRequest().getParameter("unsigned") == null) ? true : false;
/* 40 */     String str = paramRequestContext.getSpMetadata().getMetadata(paramRequestContext.getCredential(), bool);
/*    */     
/* 42 */     paramRequestContext.getResponse().getWriter().print(str);
/*    */   }
/*    */   
/*    */   public void handlePost(RequestContext paramRequestContext) throws ServletException, IOException {
/* 46 */     throw new UnsupportedOperationException("POST not allowed");
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/dk/itst/oiosaml/sp/service/MetadataHandler.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */