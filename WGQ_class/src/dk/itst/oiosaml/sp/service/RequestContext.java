/*    */ package dk.itst.oiosaml.sp.service;
/*    */ 
/*    */ import dk.itst.oiosaml.sp.bindings.BindingHandlerFactory;
/*    */ import dk.itst.oiosaml.sp.metadata.IdpMetadata;
/*    */ import dk.itst.oiosaml.sp.metadata.SPMetadata;
/*    */ import dk.itst.oiosaml.sp.service.session.SessionHandler;
/*    */ import javax.servlet.http.HttpServletRequest;
/*    */ import javax.servlet.http.HttpServletResponse;
/*    */ import javax.servlet.http.HttpSession;
/*    */ import org.apache.commons.configuration.Configuration;
/*    */ import org.opensaml.xml.security.credential.Credential;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class RequestContext
/*    */ {
/*    */   private final HttpServletRequest request;
/*    */   private final HttpServletResponse response;
/*    */   private final IdpMetadata idpMetadata;
/*    */   private final SPMetadata spMetadata;
/*    */   private final Credential credential;
/*    */   private final Configuration configuration;
/*    */   private final SessionHandler sessionHandler;
/*    */   private final BindingHandlerFactory bindingHandlerFactory;
/*    */   
/*    */   public RequestContext(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse, IdpMetadata paramIdpMetadata, SPMetadata paramSPMetadata, Credential paramCredential, Configuration paramConfiguration, SessionHandler paramSessionHandler, BindingHandlerFactory paramBindingHandlerFactory) {
/* 49 */     this.request = paramHttpServletRequest;
/* 50 */     this.response = paramHttpServletResponse;
/* 51 */     this.idpMetadata = paramIdpMetadata;
/* 52 */     this.spMetadata = paramSPMetadata;
/* 53 */     this.credential = paramCredential;
/* 54 */     this.configuration = paramConfiguration;
/* 55 */     this.sessionHandler = paramSessionHandler;
/* 56 */     this.bindingHandlerFactory = paramBindingHandlerFactory;
/*    */   }
/*    */   
/*    */   public HttpServletRequest getRequest() {
/* 60 */     return this.request;
/*    */   }
/*    */   
/*    */   public HttpServletResponse getResponse() {
/* 64 */     return this.response;
/*    */   }
/*    */   
/*    */   public IdpMetadata getIdpMetadata() {
/* 68 */     return this.idpMetadata;
/*    */   }
/*    */   
/*    */   public SPMetadata getSpMetadata() {
/* 72 */     return this.spMetadata;
/*    */   }
/*    */   
/*    */   public Credential getCredential() {
/* 76 */     return this.credential;
/*    */   }
/*    */   
/*    */   public Configuration getConfiguration() {
/* 80 */     return this.configuration;
/*    */   }
/*    */   
/*    */   public HttpSession getSession() {
/* 84 */     return this.request.getSession();
/*    */   }
/*    */   
/*    */   public SessionHandler getSessionHandler() {
/* 88 */     return this.sessionHandler;
/*    */   }
/*    */   
/*    */   public BindingHandlerFactory getBindingHandlerFactory() {
/* 92 */     return this.bindingHandlerFactory;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/dk/itst/oiosaml/sp/service/RequestContext.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */