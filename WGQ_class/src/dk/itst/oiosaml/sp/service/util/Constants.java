package dk.itst.oiosaml.sp.service.util;

public interface Constants {
  public static final String SESSION_USER_ASSERTION = "dk.itst.oiosaml.userassertion";
  
  public static final String QUERY_STRING_FORCE_AUTHN = "forceAuthn";
  
  public static final String PROP_HOME = "oiosaml-sp.uri.home";
  
  public static final String PROP_CERTIFICATE_LOCATION = "oiosaml-sp.certificate.location";
  
  public static final String PROP_CERTIFICATE_PASSWORD = "oiosaml-sp.certificate.password";
  
  public static final String PROP_IGNORE_CERTPATH = "oiosaml-sp.resolve.ignorecert";
  
  public static final String PROP_RESOLVE_USERNAME = "oiosaml-sp.resolve.username";
  
  public static final String PROP_RESOLVE_PASSWORD = "oiosaml-sp.resolve.password";
  
  public static final String PROP_ASSURANCE_LEVEL = "oiosaml-sp.assurancelevel";
  
  public static final String PROP_HTTP_PROXY_HOST = "oiosaml-sp.http.proxy.host";
  
  public static final String PROP_HTTP_PROXY_PORT = "oiosaml-sp.http.proxy.port";
  
  public static final String PROP_CIRCUIT_BREAKER_ATTEMPTS_BEFORE_OPENING = "oiosaml-sp.cb.attempts.before.opening";
  
  public static final String PROP_CIRCUIT_BREAKER_ATTEMPTS_WITHIN_IN_SECONDS = "oiosaml-sp.cb.attempts.within.in.seconds";
  
  public static final String PROP_CIRCUIT_BREAKER_RESET_TIME_IN_SECONDS = "oiosaml-sp.cb.reset.time.in.seconds";
  
  public static final String PROP_CIRCUIT_BREAKER_DELAY_BETWEEN_ATTEMPTS_IN_SECONDS = "oiosaml-sp.cb.delay.between.attempts.in.seconds";
  
  public static final String PROP_CERTIFICATES_REMAIN_VALID_PERIOD_IN_SECONDS = "oiosaml-sp.remain.valid.period.in.seconds";
  
  public static final String PROP_CRL = "oiosaml-sp.crl.";
  
  public static final String PROP_CRL_CHECK_PERIOD = "oiosaml-sp.crl.period";
  
  public static final String PROP_CRL_TRUSTSTORE = "oiosaml-sp.crl.truststore";
  
  public static final String PROP_CRL_TRUSTSTORE_PASSWORD = "oiosaml-sp.crl.truststore.password";
  
  public static final String PROP_OCSP_CA = "oiosaml-sp.ocsp.ca";
  
  public static final String PROP_OCSP_RESPONDER = "oiosaml-sp.ocsp.responder";
  
  public static final String PROP_REQUIRE_ENCRYPTION = "oiosaml-sp.encryption.force";
  
  public static final String PROP_NUM_TRACKED_ASSERTIONIDS = "common.saml2.loggedinhandler.numusedassertionids";
  
  public static final String PROP_VALIDATOR = "oiosaml-sp.assertion.validator";
  
  public static final String PROP_NAMEID_POLICY = "oiosaml-sp.nameid.policy";
  
  public static final String PROP_NAMEID_POLICY_ALLOW_CREATE = "oiosaml-sp.nameid.allowcreate";
  
  public static final String PROP_ERROR_SERVLET = "oiosaml-sp.errors";
  
  public static final String PROP_SESSION_HANDLER_FACTORY = "oiosaml-sp.sessionhandler.factory";
  
  public static final String PROP_PASSIVE = "oiosaml-sp.passive";
  
  public static final String PROP_PASSIVE_USER_ID = "oiosaml-sp.passive.user";
  
  public static final String PROP_FORCE_AUTHN_URLS = "oiosaml-sp.authn.force";
  
  public static final String PROP_SAML_SERVLET = "oiosaml-sp.servlet";
  
  public static final String PROP_AUTHENTICATION_HANDLER = "oiosaml-sp.authenticationhandler";
  
  public static final String PROP_SUPPORTED_BINDINGS = "oiosaml-sp.bindings";
  
  public static final String PROP_REPOST_SERVLET = "oiosaml-sp.repost";
  
  public static final String SERVICE_AUTHN_REQUEST = "<AuthnRequest>";
  
  public static final String SERVICE_LOGOUT_REQUEST = "<LogoutRequest>";
  
  public static final String SERVICE_LOGOUT_RESPONSE = "<LogoutResponse>";
  
  public static final String SERVICE_ARTIFACT_RESOLVE = "<ArtifactResolve>";
  
  public static final String SAML_RELAYSTATE = "RelayState";
  
  public static final String SAML_SAMLREQUEST = "SAMLRequest";
  
  public static final String SAML_SAMLRESPONSE = "SAMLResponse";
  
  public static final String SAML_SIGALG = "SigAlg";
  
  public static final String SAML_SIGNATURE = "Signature";
  
  public static final String SAML_SAMLART = "SAMLart";
  
  public static final String SHA1_WITH_RSA = "SHA1withRSA";
  
  public static final String INIT_OIOSAML_HOME = "oiosaml-j.home";
  
  public static final String INIT_OIOSAML_NAME = "oiosaml-j.name";
  
  public static final String INIT_OIOSAML_FILE = "oiosaml-j.file";
  
  public static final String DISCOVERY_LOCATION = "oiosaml-sp.discovery";
  
  public static final String PROP_DISCOVERY_DEFAULT_IDP = "oiosaml-sp.discovery.default";
  
  public static final String PROP_DISCOVERY_PROMPT = "oiosaml-sp.discovery.prompt";
  
  public static final String PROP_DISCOVERY_PROMPT_SERVLET = "oiosaml-sp.discovery.prompt.servlet";
  
  public static final String DISCOVERY_ATTRIBUTE = "_saml_idp";
  
  public static final String PROP_LOG_FILE_NAME = "oiosaml-sp.log";
  
  public static final String PROP_PROTOCOL = "oiosaml-sp.protocol";
  
  public static final String ATTRIBUTE_ERROR = "error";
  
  public static final String ATTRIBUTE_EXCEPTION = "exception";
  
  public static final String PROP_DEVEL_MODE = "oiosaml-sp.develmode";
  
  public static final String PROP_SHOW_ERROR = "oiosaml-sp.showerror";
  
  public static final String SP_METADATA_FILE = "common.saml2.metadata.sp.filename";
  
  public static final String SP_METADATA_DIRECTORY = "common.saml2.metadata.sp.directory";
  
  public static final String IDP_METADATA_FILE = "common.saml2.metadata.idp.filename";
  
  public static final String IDP_METADATA_DIRECTORY = "common.saml2.metadata.idp.directory";
  
  public static final String SIGNATURE_ALGORITHM = "oiosaml-sp.signature.algorithm";
  
  public static final String SELF_SIGNED_CERT_SUPPORT = "oiosaml-sp.selfsignedcertificates";
  
  public static final String DISABLE_OCES_TEST_CRL_CHECK = "oiosaml-cp.crl.disable-in-oces-test";
}


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/dk/itst/oiosaml/sp/service/util/Constants.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */