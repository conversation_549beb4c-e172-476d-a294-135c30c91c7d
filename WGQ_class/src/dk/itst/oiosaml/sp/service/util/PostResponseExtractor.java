/*    */ package dk.itst.oiosaml.sp.service.util;
/*    */ 
/*    */ import dk.itst.oiosaml.common.SAMLUtil;
/*    */ import dk.itst.oiosaml.error.Layer;
/*    */ import dk.itst.oiosaml.error.WrappedException;
/*    */ import dk.itst.oiosaml.logging.Logger;
/*    */ import dk.itst.oiosaml.logging.LoggerFactory;
/*    */ import dk.itst.oiosaml.sp.model.OIOResponse;
/*    */ import java.io.UnsupportedEncodingException;
/*    */ import javax.servlet.http.HttpServletRequest;
/*    */ import org.opensaml.saml2.core.Response;
/*    */ import org.opensaml.xml.XMLObject;
/*    */ import org.opensaml.xml.util.Base64;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class PostResponseExtractor
/*    */ {
/* 48 */   private static final Logger log = LoggerFactory.getLogger(PostResponseExtractor.class);
/*    */   
/*    */   public OIOResponse extract(HttpServletRequest paramHttpServletRequest) {
/* 51 */     String str = paramHttpServletRequest.getParameter("SAMLResponse");
/* 52 */     if (str == null) {
/* 53 */       throw new IllegalStateException("SAMLResponse parameter cannot be null");
/*    */     }
/* 55 */     if (log.isDebugEnabled()) log.debug("SAMLResponse: " + str);
/*    */     
/*    */     try {
/* 58 */       String str1 = new String(Base64.decode(str), "UTF-8");
/* 59 */       XMLObject xMLObject = SAMLUtil.unmarshallElementFromString(str1);
/* 60 */       if (!(xMLObject instanceof Response)) {
/* 61 */         throw new IllegalArgumentException("SAMLResponse must be of type Response. Was " + xMLObject);
/*    */       }
/* 63 */       return new OIOResponse((Response)xMLObject);
/* 64 */     } catch (UnsupportedEncodingException unsupportedEncodingException) {
/* 65 */       throw new WrappedException(Layer.BUSINESS, unsupportedEncodingException);
/*    */     } 
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/dk/itst/oiosaml/sp/service/util/PostResponseExtractor.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */