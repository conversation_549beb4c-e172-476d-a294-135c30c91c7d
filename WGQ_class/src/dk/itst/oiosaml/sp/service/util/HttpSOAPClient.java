/*     */ package dk.itst.oiosaml.sp.service.util;
/*     */ 
/*     */ import dk.itst.oiosaml.common.SAMLUtil;
/*     */ import dk.itst.oiosaml.common.SOAPException;
/*     */ import dk.itst.oiosaml.logging.Logger;
/*     */ import dk.itst.oiosaml.logging.LoggerFactory;
/*     */ import dk.itst.oiosaml.sp.model.OIOSamlObject;
/*     */ import java.io.IOException;
/*     */ import java.io.InputStream;
/*     */ import java.io.OutputStream;
/*     */ import java.net.HttpURLConnection;
/*     */ import java.net.URI;
/*     */ import java.net.URISyntaxException;
/*     */ import javax.net.ssl.HostnameVerifier;
/*     */ import javax.net.ssl.HttpsURLConnection;
/*     */ import javax.net.ssl.SSLSession;
/*     */ import org.apache.commons.io.IOUtils;
/*     */ import org.opensaml.ws.soap.soap11.Envelope;
/*     */ import org.opensaml.ws.soap.soap11.Fault;
/*     */ import org.opensaml.xml.ElementExtensibleXMLObject;
/*     */ import org.opensaml.xml.XMLObject;
/*     */ import org.opensaml.xml.util.Base64;
/*     */ import org.opensaml.xml.util.XMLHelper;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HttpSOAPClient
/*     */   implements SOAPClient
/*     */ {
/*     */   private static final String START_SOAP_ENVELOPE = "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\"><soapenv:Header/><soapenv:Body>";
/*     */   private static final String END_SOAP_ENVELOPE = "</soapenv:Body></soapenv:Envelope>";
/*  54 */   private static final Logger log = LoggerFactory.getLogger(HttpSOAPClient.class);
/*     */   
/*     */   public XMLObject wsCall(OIOSamlObject paramOIOSamlObject, String paramString1, String paramString2, String paramString3, boolean paramBoolean) throws IOException {
/*  57 */     return wsCall(paramString1, paramString2, paramString3, paramBoolean, paramOIOSamlObject.toSoapEnvelope(), "http://www.oasis-open.org/committees/security").getBody().getUnknownXMLObjects().get(0);
/*     */   }
/*     */   
/*     */   public Envelope wsCall(XMLObject paramXMLObject, String paramString1, String paramString2, String paramString3, boolean paramBoolean) throws IOException {
/*  61 */     String str = XMLHelper.nodeToString(SAMLUtil.marshallObject(paramXMLObject));
/*  62 */     str = "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\"><soapenv:Header/><soapenv:Body>" + str.substring(str.indexOf("?>") + 2) + "</soapenv:Body></soapenv:Envelope>";
/*  63 */     return wsCall(paramString1, paramString2, paramString3, paramBoolean, str, "http://www.oasis-open.org/committees/security");
/*     */   }
/*     */   
/*     */   public Envelope wsCall(String paramString1, String paramString2, String paramString3, boolean paramBoolean, String paramString4, String paramString5) throws IOException, SOAPException {
/*     */     URI uRI;
/*     */     try {
/*  69 */       uRI = new URI(paramString1);
/*  70 */     } catch (URISyntaxException uRISyntaxException) {
/*  71 */       throw new IOException("Invalid uri for artifact resolve: " + paramString1);
/*     */     } 
/*  73 */     if (log.isDebugEnabled()) log.debug("serviceLocation..:" + uRI); 
/*  74 */     if (log.isDebugEnabled()) log.debug("SOAP Request: " + paramString4);
/*     */     
/*  76 */     HttpURLConnection httpURLConnection = (HttpURLConnection)uRI.toURL().openConnection();
/*  77 */     if (httpURLConnection instanceof HttpsURLConnection) {
/*  78 */       HttpsURLConnection httpsURLConnection = (HttpsURLConnection)httpURLConnection;
/*     */       
/*  80 */       if (paramBoolean) {
/*  81 */         httpsURLConnection.setSSLSocketFactory(new DummySSLSocketFactory());
/*  82 */         httpsURLConnection.setHostnameVerifier(new HostnameVerifier() {
/*     */               public boolean verify(String param1String, SSLSession param1SSLSession) {
/*  84 */                 return true;
/*     */               }
/*     */             });
/*     */       } 
/*     */     } 
/*  89 */     httpURLConnection.setAllowUserInteraction(false);
/*  90 */     httpURLConnection.setDoInput(true);
/*  91 */     httpURLConnection.setDoOutput(true);
/*  92 */     httpURLConnection.setFixedLengthStreamingMode((paramString4.getBytes("UTF-8")).length);
/*  93 */     httpURLConnection.setRequestMethod("POST");
/*  94 */     httpURLConnection.setReadTimeout(20000);
/*  95 */     httpURLConnection.setConnectTimeout(30000);
/*     */     
/*  97 */     addContentTypeHeader(paramString4, httpURLConnection);
/*  98 */     httpURLConnection.addRequestProperty("SOAPAction", "\"" + ((paramString5 == null) ? "" : paramString5) + "\"");
/*     */     
/* 100 */     if (paramString2 != null && paramString3 != null) {
/* 101 */       httpURLConnection.addRequestProperty("Authorization", "Basic " + Base64.encodeBytes((paramString2 + ":" + paramString3).getBytes(), 8));
/*     */     }
/* 103 */     OutputStream outputStream = httpURLConnection.getOutputStream();
/* 104 */     IOUtils.write(paramString4, outputStream, "UTF-8");
/* 105 */     outputStream.flush();
/* 106 */     outputStream.close();
/*     */     
/* 108 */     if (httpURLConnection.getResponseCode() == 200) {
/* 109 */       InputStream inputStream1 = httpURLConnection.getInputStream();
/* 110 */       String str1 = IOUtils.toString(inputStream1, "UTF-8");
/* 111 */       inputStream1.close();
/*     */       
/* 113 */       if (log.isDebugEnabled()) log.debug("Server SOAP response: " + str1); 
/* 114 */       XMLObject xMLObject = SAMLUtil.unmarshallElementFromString(str1);
/*     */       
/* 116 */       Envelope envelope = (Envelope)xMLObject;
/* 117 */       if (SAMLUtil.getFirstElement((ElementExtensibleXMLObject)envelope.getBody(), Fault.class) != null) {
/* 118 */         log.warn("Result has soap11:Fault, but server returned 200 OK. Treating as error, please fix the server");
/* 119 */         throw new SOAPException(httpURLConnection.getResponseCode(), str1);
/*     */       } 
/* 121 */       return envelope;
/*     */     } 
/* 123 */     log.debug("Response code: " + httpURLConnection.getResponseCode());
/*     */     
/* 125 */     InputStream inputStream = httpURLConnection.getErrorStream();
/* 126 */     String str = IOUtils.toString(inputStream, "UTF-8");
/* 127 */     inputStream.close();
/*     */     
/* 129 */     if (log.isDebugEnabled()) log.debug("Server SOAP fault: " + str);
/*     */     
/* 131 */     throw new SOAPException(httpURLConnection.getResponseCode(), str);
/*     */   }
/*     */   
/*     */   private static void addContentTypeHeader(String paramString, HttpURLConnection paramHttpURLConnection) {
/* 135 */     String str = Utils.getSoapVersion(paramString);
/* 136 */     if ("http://schemas.xmlsoap.org/soap/envelope/".equals(str)) {
/* 137 */       paramHttpURLConnection.addRequestProperty("Content-Type", "text/xml; charset=utf-8");
/* 138 */     } else if ("http://www.w3.org/2003/05/soap-envelope".equals(str)) {
/* 139 */       paramHttpURLConnection.addRequestProperty("Content-Type", "application/soap+xml; charset=utf-8");
/*     */     } else {
/* 141 */       throw new UnsupportedOperationException("SOAP version " + str + " not supported");
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/dk/itst/oiosaml/sp/service/util/HttpSOAPClient.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */