/*     */ package dk.itst.oiosaml.sp.service.util;
/*     */ 
/*     */ import dk.itst.oiosaml.error.Layer;
/*     */ import dk.itst.oiosaml.error.WrappedException;
/*     */ import dk.itst.oiosaml.logging.Logger;
/*     */ import dk.itst.oiosaml.logging.LoggerFactory;
/*     */ import dk.itst.oiosaml.sp.service.SAMLHandler;
/*     */ import java.lang.reflect.Constructor;
/*     */ import java.security.InvalidKeyException;
/*     */ import java.security.NoSuchAlgorithmException;
/*     */ import java.security.PublicKey;
/*     */ import java.security.Signature;
/*     */ import java.security.SignatureException;
/*     */ import java.util.HashMap;
/*     */ import java.util.Iterator;
/*     */ import java.util.Map;
/*     */ import java.util.UUID;
/*     */ import javax.servlet.ServletContext;
/*     */ import org.apache.commons.configuration.Configuration;
/*     */ import org.opensaml.xml.util.Base64;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public final class Utils
/*     */ {
/*     */   public static final String VERSION = "$Id: Utils.java 3197 2008-07-25 07:47:33Z jre $";
/*  58 */   private static final Logger log = LoggerFactory.getLogger(Utils.class);
/*  59 */   private static final String[] SOAP_VERSIONS = new String[] { "http://schemas.xmlsoap.org/soap/envelope/", "http://www.w3.org/2003/05/soap-envelope" };
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String makeXML(String paramString) {
/*  67 */     String str = paramString;
/*  68 */     if (str != null && !"".equals(str)) {
/*  69 */       str = str.replaceAll("><", ">\n<");
/*  70 */       str = str.replaceAll("<", "&lt;");
/*  71 */       str = str.replaceAll(">", "&gt;");
/*  72 */       str = str.replaceAll("\n", "<br />");
/*     */     } 
/*  74 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String htmlEntityEncode(String paramString) {
/*  81 */     StringBuilder stringBuilder = new StringBuilder();
/*  82 */     byte b1 = (paramString == null) ? -1 : paramString.length();
/*  83 */     for (byte b2 = 0; b2 < b1; b2++) {
/*  84 */       char c = paramString.charAt(b2);
/*  85 */       if ((c >= 'a' && c <= 'z') || (c >= 'A' && c <= 'Z') || (c >= '0' && c <= '9')) {
/*  86 */         stringBuilder.append(c);
/*     */       } else {
/*  88 */         stringBuilder.append("&#" + c + ";");
/*     */       } 
/*     */     } 
/*  91 */     return stringBuilder.toString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean verifySignature(String paramString1, String paramString2, String paramString3, PublicKey paramPublicKey) {
/* 100 */     if (log.isDebugEnabled())
/* 101 */       log.debug("signature..:" + paramString1); 
/* 102 */     if (paramString1 == null) {
/* 103 */       return false;
/*     */     }
/*     */     
/* 106 */     byte[] arrayOfByte = Base64.decode(paramString1);
/*     */     
/* 108 */     String str = parseSignedQueryString(paramString2, paramString3);
/*     */ 
/*     */     
/* 111 */     if (log.isDebugEnabled()) {
/* 112 */       log.debug("data.......:" + str);
/*     */     }
/* 114 */     if (log.isDebugEnabled()) {
/* 115 */       log.debug("Verifying Signature...");
/*     */     }
/* 117 */     return verifySignature(str.getBytes(), paramPublicKey, arrayOfByte);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean verifySignature(byte[] paramArrayOfbyte1, PublicKey paramPublicKey, byte[] paramArrayOfbyte2) {
/* 135 */     if (log.isDebugEnabled())
/* 136 */       log.debug("data...:" + new String(paramArrayOfbyte1)); 
/* 137 */     if (log.isDebugEnabled())
/* 138 */       log.debug("sig....:" + new String(paramArrayOfbyte2)); 
/* 139 */     if (log.isDebugEnabled()) {
/* 140 */       log.debug("key....:" + paramPublicKey.toString());
/*     */     }
/*     */     
/*     */     try {
/* 144 */       Signature signature = Signature.getInstance("SHA1withRSA");
/* 145 */       signature.initVerify(paramPublicKey);
/* 146 */       signature.update(paramArrayOfbyte1);
/* 147 */       return signature.verify(paramArrayOfbyte2);
/*     */     }
/* 149 */     catch (SignatureException signatureException) {
/*     */       
/* 151 */       Signature signature = Signature.getInstance("SHA256withRSA");
/* 152 */       signature.initVerify(paramPublicKey);
/* 153 */       signature.update(paramArrayOfbyte1);
/* 154 */       return signature.verify(paramArrayOfbyte2);
/*     */     }
/* 156 */     catch (InvalidKeyException invalidKeyException) {
/* 157 */       throw new WrappedException(Layer.CLIENT, invalidKeyException);
/* 158 */     } catch (NoSuchAlgorithmException noSuchAlgorithmException) {
/* 159 */       throw new WrappedException(Layer.CLIENT, noSuchAlgorithmException);
/* 160 */     } catch (SignatureException signatureException) {
/* 161 */       log.error("Failed to verify signature", signatureException);
/* 162 */       return false;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String beautifyAndHtmlXML(String paramString1, String paramString2) {
/* 170 */     return makeXML(beautifyXML(paramString1, paramString2));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String beautifyXML(String paramString1, String paramString2) {
/* 177 */     String str1 = "";
/* 178 */     if (paramString2 != null) {
/* 179 */       str1 = ".:split:.";
/*     */     }
/* 181 */     if (paramString1 == null || "".equals(paramString1)) {
/* 182 */       return paramString1;
/*     */     }
/* 184 */     StringBuffer stringBuffer = new StringBuffer();
/*     */ 
/*     */     
/* 187 */     String[] arrayOfString = paramString1.split("<");
/* 188 */     for (byte b1 = 1; b1 < arrayOfString.length; b1++) {
/* 189 */       arrayOfString[b1] = "<" + arrayOfString[b1].trim();
/* 190 */       if (arrayOfString[b1].endsWith("/>")) {
/* 191 */         stringBuffer.append(arrayOfString[b1]).append(str1);
/* 192 */       } else if (arrayOfString[b1].startsWith("</")) {
/* 193 */         stringBuffer.append(arrayOfString[b1]).append(str1);
/* 194 */       } else if (arrayOfString[b1].endsWith(">")) {
/* 195 */         stringBuffer.append(arrayOfString[b1]).append(str1);
/*     */       } else {
/* 197 */         stringBuffer.append(arrayOfString[b1]);
/*     */       } 
/*     */     } 
/*     */ 
/*     */     
/* 202 */     if (paramString2 == null) {
/* 203 */       return stringBuffer.toString().trim();
/*     */     }
/* 205 */     StringBuilder stringBuilder = new StringBuilder();
/* 206 */     String str2 = "";
/* 207 */     arrayOfString = stringBuffer.toString().split(str1);
/* 208 */     for (byte b2 = 0; b2 < arrayOfString.length; b2++) {
/* 209 */       if (arrayOfString[b2].startsWith("</")) {
/* 210 */         str2 = str2.substring(paramString2.length());
/*     */       }
/* 212 */       stringBuilder.append(str2).append(arrayOfString[b2]).append("\n");
/*     */       
/* 214 */       if (!arrayOfString[b2].startsWith("<!") && !arrayOfString[b2].startsWith("<?") && arrayOfString[b2]
/* 215 */         .indexOf("</") == -1 && arrayOfString[b2]
/* 216 */         .indexOf("/>") == -1)
/* 217 */         str2 = str2 + paramString2; 
/*     */     } 
/* 219 */     return stringBuilder.toString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String generateUUID() {
/* 226 */     return "_" + UUID.randomUUID().toString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getSoapVersion(String paramString) {
/* 236 */     for (byte b = 0; b < SOAP_VERSIONS.length; b++) {
/* 237 */       int i = paramString.indexOf(SOAP_VERSIONS[b]);
/* 238 */       if (i > -1) {
/* 239 */         String str = getPrefix(paramString, i);
/* 240 */         int j = paramString.lastIndexOf('<', i);
/*     */         
/* 242 */         if (str == null) {
/* 243 */           str = "<";
/*     */         } else {
/* 245 */           str = "<" + str + ":";
/*     */         } 
/* 247 */         if (paramString.lastIndexOf(str + "Envelope", i) >= j) {
/* 248 */           return SOAP_VERSIONS[b];
/*     */         }
/*     */       } 
/*     */     } 
/* 252 */     return null;
/*     */   }
/*     */   
/*     */   private static String getPrefix(String paramString, int paramInt) {
/* 256 */     if (paramInt > -1) {
/* 257 */       String str = paramString.substring(paramString.lastIndexOf(' ', paramInt) + 1, paramInt);
/* 258 */       if (str.startsWith("xmlns:")) {
/* 259 */         str = str.substring(6, str.lastIndexOf('=')).trim();
/*     */       } else {
/* 261 */         str = null;
/*     */       } 
/* 263 */       return str;
/*     */     } 
/* 265 */     return null;
/*     */   }
/*     */   
/*     */   public static Object newInstance(Configuration paramConfiguration, String paramString) {
/* 269 */     String str = paramConfiguration.getString(paramString);
/* 270 */     if (str == null) {
/* 271 */       throw new IllegalArgumentException("Property " + paramString + " has not been set");
/*     */     }
/*     */     
/*     */     try {
/* 275 */       Class<?> clazz = Class.forName(str);
/*     */       
/* 277 */       return clazz.newInstance();
/* 278 */     } catch (Exception exception) {
/* 279 */       throw new RuntimeException("Unable to create instance of " + str, exception);
/*     */     } 
/*     */   }
/*     */   
/*     */   public static Map<String, SAMLHandler> getHandlers(Configuration paramConfiguration, ServletContext paramServletContext) {
/* 284 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/* 286 */     for (Iterator<String> iterator = paramConfiguration.getKeys(); iterator.hasNext(); ) {
/* 287 */       String str = iterator.next();
/* 288 */       if (!str.startsWith("oiosaml-sp.protocol.endpoints."))
/* 289 */         continue;  log.debug("Checking " + str);
/*     */       try {
/*     */         SAMLHandler sAMLHandler;
/* 292 */         Class<?> clazz = Class.forName(paramConfiguration.getString(str));
/*     */         
/*     */         try {
/* 295 */           Constructor<?> constructor = clazz.getConstructor(new Class[] { Configuration.class });
/* 296 */           sAMLHandler = (SAMLHandler)constructor.newInstance(new Object[] { paramConfiguration });
/* 297 */         } catch (NoSuchMethodException noSuchMethodException) {
/*     */           try {
/* 299 */             Constructor<?> constructor = clazz.getConstructor(new Class[] { ServletContext.class });
/* 300 */             sAMLHandler = (SAMLHandler)constructor.newInstance(new Object[] { paramServletContext });
/* 301 */           } catch (NoSuchMethodException noSuchMethodException1) {
/* 302 */             sAMLHandler = (SAMLHandler)clazz.newInstance();
/*     */           } 
/*     */         } 
/*     */         
/* 306 */         hashMap.put(str.substring(str.lastIndexOf('.') + 1), sAMLHandler);
/*     */       }
/* 308 */       catch (Exception exception) {
/* 309 */         log.error("Unable to instantiate " + str + ": " + paramConfiguration.getString(str), exception);
/* 310 */         throw new RuntimeException(exception);
/*     */       } 
/*     */     } 
/*     */ 
/*     */     
/* 315 */     return (Map)hashMap;
/*     */   }
/*     */   
/*     */   public static String parseSignedQueryString(String paramString1, String paramString2) {
/* 319 */     StringBuilder stringBuilder = new StringBuilder();
/*     */     
/* 321 */     String str1 = getParameter(paramString2, paramString1);
/* 322 */     String str2 = getParameter("RelayState", paramString1);
/* 323 */     String str3 = getParameter("SigAlg", paramString1);
/*     */ 
/*     */     
/* 326 */     stringBuilder.append(paramString2);
/* 327 */     stringBuilder.append("=");
/* 328 */     stringBuilder.append(str1);
/* 329 */     if (str2 != null) {
/* 330 */       stringBuilder.append("&");
/* 331 */       stringBuilder.append("RelayState");
/* 332 */       stringBuilder.append("=");
/* 333 */       stringBuilder.append(str2);
/*     */     } 
/* 335 */     stringBuilder.append("&");
/* 336 */     stringBuilder.append("SigAlg");
/* 337 */     stringBuilder.append("=");
/* 338 */     stringBuilder.append(str3);
/*     */     
/* 340 */     return stringBuilder.toString();
/*     */   }
/*     */ 
/*     */   
/*     */   public static String getParameter(String paramString1, String paramString2) {
/* 345 */     int i = paramString2.indexOf('?') + 1;
/* 346 */     String[] arrayOfString = paramString2.substring(i).split("&");
/* 347 */     for (String str1 : arrayOfString) {
/* 348 */       int j = str1.indexOf('=');
/* 349 */       String str2 = str1.substring(0, j);
/* 350 */       if (paramString1.equals(str2)) {
/* 351 */         return str1.substring(j + 1);
/*     */       }
/*     */     } 
/* 354 */     return null;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/dk/itst/oiosaml/sp/service/util/Utils.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */