/*     */ package dk.itst.oiosaml.sp.service.util;
/*     */ 
/*     */ import dk.itst.oiosaml.logging.Logger;
/*     */ import dk.itst.oiosaml.logging.LoggerFactory;
/*     */ import dk.itst.oiosaml.sp.service.RequestContext;
/*     */ import dk.itst.oiosaml.sp.service.session.Request;
/*     */ import java.io.IOException;
/*     */ import java.io.PrintWriter;
/*     */ import java.net.URLDecoder;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import javax.servlet.ServletException;
/*     */ import javax.servlet.ServletRequest;
/*     */ import javax.servlet.ServletResponse;
/*     */ import javax.servlet.http.Cookie;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import org.apache.commons.lang3.StringUtils;
/*     */ import org.apache.velocity.VelocityContext;
/*     */ import org.apache.velocity.app.VelocityEngine;
/*     */ import org.apache.velocity.context.Context;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HTTPUtils
/*     */ {
/*  54 */   private static final Logger log = LoggerFactory.getLogger(HTTPUtils.class);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static void sendMetaRedirect(HttpServletResponse paramHttpServletResponse, String paramString1, String paramString2, boolean paramBoolean) throws IOException {
/*  67 */     paramHttpServletResponse.setContentType("text/html");
/*  68 */     sendCacheHeaders(paramHttpServletResponse);
/*     */     
/*  70 */     PrintWriter printWriter = paramHttpServletResponse.getWriter();
/*  71 */     printWriter.write("<html><head>");
/*  72 */     printWriter.write("<meta http-equiv=\"refresh\" content=\"0;url=");
/*  73 */     printWriter.write(paramString1);
/*  74 */     if (paramString2 != null) {
/*  75 */       if (paramString1.contains("?")) {
/*  76 */         printWriter.write("&");
/*     */       } else {
/*  78 */         printWriter.write("?");
/*     */       } 
/*  80 */       printWriter.write(paramString2);
/*     */     } 
/*  82 */     printWriter.write("\">");
/*  83 */     printWriter.write("</head><body>");
/*  84 */     if (paramBoolean) {
/*  85 */       printWriter.write("<script type=\"text/javascript\">document.cookie = 'oiosaml-fragment=' + escape(location.hash) + '; path=/';</script>");
/*     */     }
/*  87 */     printWriter.write("</body></html>");
/*     */   }
/*     */ 
/*     */   
/*     */   public static void sendCacheHeaders(HttpServletResponse paramHttpServletResponse) {
/*  92 */     paramHttpServletResponse.addHeader("Pragma", "no-cache");
/*  93 */     paramHttpServletResponse.addDateHeader("Expires", -1L);
/*  94 */     paramHttpServletResponse.addHeader("Cache-Control", "no-cache");
/*  95 */     paramHttpServletResponse.addHeader("Cache-Control", "no-store");
/*     */   }
/*     */   
/*     */   public static String getFragmentCookie(HttpServletRequest paramHttpServletRequest) {
/*  99 */     Cookie[] arrayOfCookie = paramHttpServletRequest.getCookies();
/* 100 */     if (arrayOfCookie == null) return null;
/*     */     
/* 102 */     for (Cookie cookie : arrayOfCookie) {
/* 103 */       if ("oiosaml-fragment".equals(cookie.getName())) {
/* 104 */         return cookie.getValue();
/*     */       }
/*     */     } 
/* 107 */     return null;
/*     */   }
/*     */   
/*     */   public static void removeFragmentCookie(HttpServletResponse paramHttpServletResponse) {
/* 111 */     Cookie cookie = new Cookie("oiosaml-fragment", "");
/* 112 */     cookie.setPath("/");
/* 113 */     cookie.setMaxAge(0);
/* 114 */     paramHttpServletResponse.addCookie(cookie);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static void sendResponse(Request paramRequest, RequestContext paramRequestContext) throws IOException, ServletException {
/* 131 */     sendCacheHeaders(paramRequestContext.getResponse());
/*     */     
/* 133 */     String str1 = paramRequestContext.getConfiguration().getString("oiosaml-sp.uri.home");
/* 134 */     String str2 = paramRequestContext.getConfiguration().getString("oiosaml-sp.request.ctxpath");
/* 135 */     boolean bool = StringUtils.isNotBlank(str2);
/* 136 */     if (paramRequest == null) {
/* 137 */       log.debug("No request saved in RelayState, redrecting to default url: " + str1);
/* 138 */       paramRequestContext.getResponse().sendRedirect(str1);
/*     */       
/*     */       return;
/*     */     } 
/* 142 */     String str3 = paramRequest.getRequestURI();
/* 143 */     if ("GET".equals(paramRequest.getMethod())) {
/* 144 */       str3 = (bool ? str2 : "") + str3.replaceAll("/[/]*", "/");
/* 145 */       StringBuilder stringBuilder = new StringBuilder(str3);
/* 146 */       if (paramRequest.getQueryString() != null) {
/* 147 */         stringBuilder.append("?");
/* 148 */         stringBuilder.append(paramRequest.getQueryString());
/*     */       } 
/* 150 */       String str = getFragmentCookie(paramRequestContext.getRequest());
/* 151 */       if (str != null) {
/* 152 */         removeFragmentCookie(paramRequestContext.getResponse());
/* 153 */         stringBuilder.append(URLDecoder.decode(str, "utf-8"));
/*     */       } 
/*     */       
/* 156 */       if (log.isDebugEnabled()) log.debug("Saved GET request, redirecting to " + stringBuilder); 
/* 157 */       paramRequestContext.getResponse().sendRedirect(stringBuilder.toString());
/*     */     } else {
/* 159 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 160 */       for (Map.Entry entry : paramRequest.getParameters().entrySet()) {
/* 161 */         ArrayList<String> arrayList = new ArrayList();
/*     */         
/* 163 */         for (String str4 : (String[])entry.getValue()) {
/* 164 */           arrayList.add(Utils.htmlEntityEncode(str4));
/*     */         }
/* 166 */         hashMap.put(Utils.htmlEntityEncode((String)entry.getKey()), arrayList.toArray(new String[0]));
/*     */       } 
/* 168 */       paramRequest = new Request(str3, paramRequest.getQueryString(), paramRequest.getMethod(), hashMap);
/*     */       
/* 170 */       String str = paramRequestContext.getConfiguration().getString("oiosaml-sp.repost", null);
/* 171 */       if (str != null) {
/* 172 */         if (log.isDebugEnabled()) log.debug("POST Request with custom servlet at " + str + " for action " + str3); 
/* 173 */         paramRequestContext.getRequest().setAttribute("request", paramRequest);
/* 174 */         paramRequestContext.getRequest().setAttribute("home", str1);
/* 175 */         paramRequestContext.getRequest().getRequestDispatcher(str).forward((ServletRequest)paramRequestContext.getRequest(), (ServletResponse)paramRequestContext.getResponse());
/*     */       } else {
/* 177 */         if (log.isDebugEnabled()) log.debug("Saved POST request with default servlet for action " + str3);
/*     */         
/* 179 */         VelocityContext velocityContext = new VelocityContext();
/* 180 */         velocityContext.put("request", paramRequest);
/* 181 */         velocityContext.put("home", str1);
/*     */         
/* 183 */         paramRequestContext.getResponse().setContentType("text/html");
/*     */         
/*     */         try {
/* 186 */           getEngine().mergeTemplate("repost.vm", "UTF-8", (Context)velocityContext, paramRequestContext.getResponse().getWriter());
/* 187 */         } catch (Exception exception) {
/* 188 */           log.error("Unable to render error template", exception);
/* 189 */           throw new ServletException(exception);
/*     */         } 
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static VelocityEngine getEngine() {
/* 198 */     VelocityEngine velocityEngine = new VelocityEngine();
/* 199 */     velocityEngine.setProperty("resource.loader", "classpath");
/* 200 */     velocityEngine.setProperty("classpath.resource.loader.class", "org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader");
/*     */     try {
/* 202 */       velocityEngine.init();
/* 203 */     } catch (Exception exception) {
/* 204 */       throw new RuntimeException(exception);
/*     */     } 
/* 206 */     return velocityEngine;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/dk/itst/oiosaml/sp/service/util/HTTPUtils.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */