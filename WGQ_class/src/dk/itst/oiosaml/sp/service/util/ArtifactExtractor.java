/*     */ package dk.itst.oiosaml.sp.service.util;
/*     */ 
/*     */ import dk.itst.oiosaml.common.OIOSAMLConstants;
/*     */ import dk.itst.oiosaml.common.SAMLUtil;
/*     */ import dk.itst.oiosaml.error.Layer;
/*     */ import dk.itst.oiosaml.error.WrappedException;
/*     */ import dk.itst.oiosaml.logging.Audit;
/*     */ import dk.itst.oiosaml.logging.Logger;
/*     */ import dk.itst.oiosaml.logging.LoggerFactory;
/*     */ import dk.itst.oiosaml.logging.Operation;
/*     */ import dk.itst.oiosaml.sp.metadata.IdpMetadata;
/*     */ import dk.itst.oiosaml.sp.model.OIOResponse;
/*     */ import dk.itst.oiosaml.sp.util.BRSArtifact;
/*     */ import java.io.IOException;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import org.joda.time.DateTime;
/*     */ import org.joda.time.DateTimeZone;
/*     */ import org.opensaml.common.binding.BindingException;
/*     */ import org.opensaml.saml2.core.ArtifactResolve;
/*     */ import org.opensaml.saml2.core.ArtifactResponse;
/*     */ import org.opensaml.saml2.core.Response;
/*     */ import org.opensaml.ws.soap.soap11.Envelope;
/*     */ import org.opensaml.xml.XMLObject;
/*     */ import org.opensaml.xml.util.XMLHelper;
/*     */ import org.opensaml.xml.validation.ValidationException;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class ArtifactExtractor
/*     */ {
/*  55 */   private static final Logger log = LoggerFactory.getLogger(ArtifactExtractor.class);
/*     */   private final String spEntityID;
/*     */   private String resolveUsername;
/*     */   private String resolvePassword;
/*     */   private final IdpMetadata idpMetadata;
/*     */   private final SOAPClient client;
/*     */   private final boolean ignoreCertPath;
/*     */   
/*     */   public ArtifactExtractor(IdpMetadata paramIdpMetadata, String paramString1, SOAPClient paramSOAPClient, String paramString2, String paramString3, boolean paramBoolean) {
/*  64 */     this.idpMetadata = paramIdpMetadata;
/*  65 */     this.spEntityID = paramString1;
/*  66 */     this.client = paramSOAPClient;
/*  67 */     this.resolveUsername = paramString2;
/*  68 */     this.resolvePassword = paramString3;
/*  69 */     this.ignoreCertPath = paramBoolean;
/*     */   }
/*     */   
/*     */   public OIOResponse extract(HttpServletRequest paramHttpServletRequest) throws IOException {
/*     */     BRSArtifact bRSArtifact;
/*  74 */     String str1 = paramHttpServletRequest.getParameter("SAMLart");
/*  75 */     if (log.isDebugEnabled()) log.debug("Got SAMLart..:" + str1);
/*     */     
/*  77 */     if (str1 == null) {
/*  78 */       throw new IllegalArgumentException(" Parameter 'SAMLart' is null...");
/*     */     }
/*     */ 
/*     */     
/*  82 */     int i = 0;
/*     */     
/*     */     try {
/*  85 */       bRSArtifact = new BRSArtifact(str1, (String[])this.idpMetadata.getEntityIDs().toArray((Object[])new String[0]));
/*  86 */       i = bRSArtifact.getEndpointIndex();
/*  87 */       if (log.isDebugEnabled()) log.debug("Got endpointIndex..:" + i); 
/*  88 */     } catch (BindingException bindingException) {
/*  89 */       throw new WrappedException(Layer.BUSINESS, bindingException);
/*  90 */     } catch (NullPointerException nullPointerException) {
/*  91 */       throw new IllegalArgumentException(str1, nullPointerException);
/*     */     } 
/*  93 */     String str2 = this.idpMetadata.getMetadata(bRSArtifact.getEntityId()).getArtifactResolutionServiceLocation("urn:oasis:names:tc:SAML:2.0:bindings:SOAP");
/*     */ 
/*     */     
/*  96 */     String str3 = Utils.generateUUID();
/*  97 */     ArtifactResolve artifactResolve = buildArtifactResolve(str1, str3, str2);
/*     */     
/*  99 */     Audit.log(Operation.ARTIFACTRESOLVE, true, artifactResolve.getID(), XMLHelper.nodeToString(SAMLUtil.marshallObject((XMLObject)artifactResolve)));
/*     */     
/* 101 */     Envelope envelope = this.client.wsCall((XMLObject)artifactResolve, str2, this.resolveUsername, this.resolvePassword, this.ignoreCertPath);
/* 102 */     ArtifactResponse artifactResponse = envelope.getBody().getUnknownXMLObjects().get(0);
/*     */     try {
/* 104 */       artifactResponse.validate(false);
/* 105 */     } catch (ValidationException validationException) {
/* 106 */       throw new WrappedException(Layer.CLIENT, validationException);
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/* 111 */     if (!str3.equals(artifactResponse.getInResponseTo())) {
/* 112 */       RuntimeException runtimeException = new RuntimeException("Received different id than I sent: Expected " + str3 + ". Was " + artifactResponse.getInResponseTo());
/* 113 */       Audit.logError(Operation.ARTIFACTRESOLVE, false, artifactResolve.getID(), runtimeException);
/* 114 */       throw runtimeException;
/*     */     } 
/*     */ 
/*     */     
/* 118 */     String str4 = artifactResponse.getStatus().getStatusCode().getValue();
/* 119 */     if (!"urn:oasis:names:tc:SAML:2.0:status:Success".equals(str4)) {
/* 120 */       RuntimeException runtimeException = new RuntimeException("Got ArtifactResponse:StatusCode " + str4 + " should be " + "urn:oasis:names:tc:SAML:2.0:status:Success");
/* 121 */       Audit.logError(Operation.ARTIFACTRESOLVE, false, artifactResolve.getID(), runtimeException);
/* 122 */       throw runtimeException;
/*     */     } 
/* 124 */     OIOResponse oIOResponse = new OIOResponse((Response)artifactResponse.getMessage());
/* 125 */     Audit.log(Operation.ARTIFACTRESOLVE, false, artifactResolve.getID(), oIOResponse.toXML());
/* 126 */     return oIOResponse;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private ArtifactResolve buildArtifactResolve(String paramString1, String paramString2, String paramString3) {
/* 140 */     if (log.isDebugEnabled()) {
/* 141 */       log.debug("buildArtifactResolve...");
/*     */     }
/* 143 */     ArtifactResolve artifactResolve = (ArtifactResolve)SAMLUtil.buildXMLObject(ArtifactResolve.class);
/* 144 */     artifactResolve.addNamespace(OIOSAMLConstants.SAML20_NAMESPACE);
/* 145 */     artifactResolve.setIssuer(SAMLUtil.createIssuer(this.spEntityID));
/* 146 */     artifactResolve.setID(paramString2);
/* 147 */     artifactResolve.setIssueInstant(new DateTime(DateTimeZone.UTC));
/* 148 */     artifactResolve.setArtifact(SAMLUtil.createArtifact(paramString1));
/* 149 */     artifactResolve.setDestination(paramString3);
/*     */ 
/*     */     
/*     */     try {
/* 153 */       artifactResolve.validate(true);
/* 154 */     } catch (ValidationException validationException) {
/* 155 */       throw new WrappedException(Layer.CLIENT, validationException);
/*     */     } 
/* 157 */     return artifactResolve;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/dk/itst/oiosaml/sp/service/util/ArtifactExtractor.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */