/*    */ package dk.itst.oiosaml.sp.service.util;
/*    */ 
/*    */ import java.security.cert.X509Certificate;
/*    */ import javax.net.ssl.X509TrustManager;
/*    */ 
/*    */ 
/*    */ public class DummyTrustManager
/*    */   implements X509TrustManager
/*    */ {
/*    */   public void checkClientTrusted(X509Certificate[] paramArrayOfX509Certificate, String paramString) {}
/*    */   
/*    */   public void checkServerTrusted(X509Certificate[] paramArrayOfX509Certificate, String paramString) {}
/*    */   
/*    */   public X509Certificate[] getAcceptedIssuers() {
/* 15 */     return new X509Certificate[0];
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/dk/itst/oiosaml/sp/service/util/DummyTrustManager.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */