package dk.itst.oiosaml.sp.service.util;

import dk.itst.oiosaml.common.SOAPException;
import dk.itst.oiosaml.sp.model.OIOSamlObject;
import java.io.IOException;
import org.opensaml.ws.soap.soap11.Envelope;
import org.opensaml.xml.XMLObject;

public interface SOAPClient {
  Envelope wsCall(XMLObject paramXMLObject, String paramString1, String paramString2, String paramString3, boolean paramBoolean) throws IOException;
  
  XMLObject wsCall(OIOSamlObject paramOIOSamlObject, String paramString1, String paramString2, String paramString3, boolean paramBoolean) throws IOException;
  
  Envelope wsCall(String paramString1, String paramString2, String paramString3, boolean paramBoolean, String paramString4, String paramString5) throws IOException, SOAPException;
}


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/dk/itst/oiosaml/sp/service/util/SOAPClient.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */