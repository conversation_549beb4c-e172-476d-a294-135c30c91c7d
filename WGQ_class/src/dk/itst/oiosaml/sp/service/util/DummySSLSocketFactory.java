/*    */ package dk.itst.oiosaml.sp.service.util;
/*    */ 
/*    */ import java.io.IOException;
/*    */ import java.net.InetAddress;
/*    */ import java.net.Socket;
/*    */ import javax.net.SocketFactory;
/*    */ import javax.net.ssl.SSLContext;
/*    */ import javax.net.ssl.SSLSocketFactory;
/*    */ import javax.net.ssl.TrustManager;
/*    */ 
/*    */ public class DummySSLSocketFactory
/*    */   extends SSLSocketFactory {
/*    */   private SSLSocketFactory factory;
/*    */   
/*    */   public DummySSLSocketFactory() {
/*    */     try {
/* 17 */       SSLContext sSLContext = SSLContext.getInstance("TLS");
/* 18 */       sSLContext.init(null, new TrustManager[] { new DummyTrustManager() }, null);
/*    */ 
/*    */       
/* 21 */       this.factory = sSLContext.getSocketFactory();
/* 22 */     } catch (Exception exception) {}
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public static SocketFactory getDefault() {
/* 28 */     return new DummySSLSocketFactory();
/*    */   }
/*    */ 
/*    */   
/*    */   public Socket createSocket(Socket paramSocket, String paramString, int paramInt, boolean paramBoolean) throws IOException {
/* 33 */     return this.factory.createSocket(paramSocket, paramString, paramInt, paramBoolean);
/*    */   }
/*    */ 
/*    */   
/*    */   public Socket createSocket(InetAddress paramInetAddress1, int paramInt1, InetAddress paramInetAddress2, int paramInt2) throws IOException {
/* 38 */     return this.factory.createSocket(paramInetAddress1, paramInt1, paramInetAddress2, paramInt2);
/*    */   }
/*    */ 
/*    */   
/*    */   public Socket createSocket(InetAddress paramInetAddress, int paramInt) throws IOException {
/* 43 */     return this.factory.createSocket(paramInetAddress, paramInt);
/*    */   }
/*    */ 
/*    */   
/*    */   public Socket createSocket(String paramString, int paramInt1, InetAddress paramInetAddress, int paramInt2) throws IOException {
/* 48 */     return this.factory.createSocket(paramString, paramInt1, paramInetAddress, paramInt2);
/*    */   }
/*    */   
/*    */   public Socket createSocket(String paramString, int paramInt) throws IOException {
/* 52 */     return this.factory.createSocket(paramString, paramInt);
/*    */   }
/*    */   
/*    */   public String[] getDefaultCipherSuites() {
/* 56 */     return this.factory.getDefaultCipherSuites();
/*    */   }
/*    */   public Socket createSocket() throws IOException {
/* 59 */     return this.factory.createSocket();
/*    */   }
/*    */   public String[] getSupportedCipherSuites() {
/* 62 */     return this.factory.getSupportedCipherSuites();
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/dk/itst/oiosaml/sp/service/util/DummySSLSocketFactory.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */