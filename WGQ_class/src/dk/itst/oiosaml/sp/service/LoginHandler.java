/*     */ package dk.itst.oiosaml.sp.service;
/*     */ 
/*     */ import dk.itst.oiosaml.common.SAMLUtil;
/*     */ import dk.itst.oiosaml.logging.Audit;
/*     */ import dk.itst.oiosaml.logging.Logger;
/*     */ import dk.itst.oiosaml.logging.LoggerFactory;
/*     */ import dk.itst.oiosaml.logging.Operation;
/*     */ import dk.itst.oiosaml.sp.UserAssertion;
/*     */ import dk.itst.oiosaml.sp.UserAssertionHolder;
/*     */ import dk.itst.oiosaml.sp.bindings.BindingHandler;
/*     */ import dk.itst.oiosaml.sp.metadata.IdpMetadata;
/*     */ import dk.itst.oiosaml.sp.model.OIOAuthnRequest;
/*     */ import dk.itst.oiosaml.sp.service.util.HTTPUtils;
/*     */ import java.io.IOException;
/*     */ import java.net.URLEncoder;
/*     */ import java.util.Arrays;
/*     */ import java.util.HashMap;
/*     */ import javax.servlet.ServletException;
/*     */ import javax.servlet.ServletRequest;
/*     */ import javax.servlet.ServletResponse;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import javax.servlet.http.HttpSession;
/*     */ import org.apache.commons.configuration.Configuration;
/*     */ import org.apache.velocity.VelocityContext;
/*     */ import org.apache.velocity.context.Context;
/*     */ import org.opensaml.saml2.metadata.Endpoint;
/*     */ import org.opensaml.xml.util.Base64;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class LoginHandler
/*     */   implements SAMLHandler
/*     */ {
/*  57 */   private static final Logger log = LoggerFactory.getLogger(LoginHandler.class);
/*     */   public void handleGet(RequestContext paramRequestContext) throws ServletException, IOException {
/*     */     IdpMetadata.Metadata metadata;
/*  60 */     if (log.isDebugEnabled()) log.debug("Go to login...");
/*     */     
/*  62 */     IdpMetadata idpMetadata = paramRequestContext.getIdpMetadata();
/*  63 */     Configuration configuration = paramRequestContext.getConfiguration();
/*  64 */     HttpServletRequest httpServletRequest = paramRequestContext.getRequest();
/*  65 */     HttpServletResponse httpServletResponse = paramRequestContext.getResponse();
/*     */ 
/*     */     
/*  68 */     if (idpMetadata.enableDiscovery()) {
/*  69 */       log.debug("Discovery profile is active");
/*  70 */       String str1 = httpServletRequest.getParameter("_saml_idp");
/*  71 */       if (str1 == null) {
/*  72 */         String str2 = configuration.getString("oiosaml-sp.discovery");
/*  73 */         log.debug("No _saml_idp discovery value found, redirecting to discovery service at " + str2);
/*  74 */         String str3 = httpServletRequest.getRequestURL().toString();
/*  75 */         if (httpServletRequest.getQueryString() != null) {
/*  76 */           str3 = str3 + "?" + httpServletRequest.getQueryString();
/*     */         }
/*  78 */         Audit.log(Operation.DISCOVER, true, "", str2);
/*  79 */         HTTPUtils.sendMetaRedirect(httpServletResponse, str2, "r=" + URLEncoder.encode(str3, "UTF-8"), true); return;
/*     */       } 
/*  81 */       if ("".equals(str1)) {
/*  82 */         String str2 = configuration.getString("oiosaml-sp.discovery.default", null);
/*  83 */         if (str2 != null) {
/*  84 */           log.debug("No IdP discovered, using default IdP from configuration: " + str2);
/*  85 */           metadata = idpMetadata.getMetadata(str2);
/*     */         } else {
/*  87 */           if (configuration.getBoolean("oiosaml-sp.discovery.prompt", false)) {
/*  88 */             String str3 = httpServletRequest.getRequestURL().toString();
/*  89 */             String str4 = httpServletRequest.getParameter("RelayState");
/*  90 */             if (str4 != null) {
/*  91 */               str3 = str3 + "?RelayState=" + str4;
/*     */             }
/*  93 */             promptIdp(paramRequestContext, str3);
/*     */             
/*     */             return;
/*     */           } 
/*  97 */           log.debug("No IdP discovered, using first from metadata");
/*  98 */           metadata = idpMetadata.getFirstMetadata();
/*     */         } 
/*     */       } else {
/* 101 */         String[] arrayOfString = SAMLUtil.decodeDiscoveryValue(str1);
/* 102 */         Audit.log(Operation.DISCOVER, false, "", Arrays.<String>asList(arrayOfString).toString());
/* 103 */         metadata = idpMetadata.findSupportedEntity(arrayOfString);
/* 104 */         if (metadata != null) {
/* 105 */           log.debug("Discovered idp " + metadata.getEntityID());
/*     */         } else {
/* 107 */           log.debug("No supported IdP discovered, using first from metadata");
/* 108 */           metadata = idpMetadata.getFirstMetadata();
/*     */         } 
/*     */       } 
/*     */     } else {
/* 112 */       metadata = idpMetadata.getFirstMetadata();
/*     */     } 
/* 114 */     Audit.log(Operation.DISCOVER, metadata.getEntityID());
/*     */     
/* 116 */     Endpoint endpoint = metadata.findLoginEndpoint(configuration.getStringArray("oiosaml-sp.bindings"));
/* 117 */     if (endpoint == null) {
/* 118 */       String str1 = "Could not find a valid IdP signon location. Supported bindings: " + configuration.getString("oiosaml-sp.bindings") + ", available: " + metadata.getSingleSignonServices();
/* 119 */       log.error(str1);
/* 120 */       throw new RuntimeException(str1);
/*     */     } 
/* 122 */     log.debug("Signing on at " + endpoint.getLocation());
/*     */     
/* 124 */     BindingHandler bindingHandler = paramRequestContext.getBindingHandlerFactory().getBindingHandler(endpoint.getBinding());
/* 125 */     log.info("Using idp " + metadata.getEntityID() + " at " + endpoint.getLocation() + " with binding " + endpoint.getBinding());
/*     */     
/* 127 */     HttpSession httpSession = paramRequestContext.getSession();
/* 128 */     UserAssertion userAssertion = (UserAssertion)httpSession.getAttribute("dk.itst.oiosaml.userassertion");
/* 129 */     httpSession.removeAttribute("dk.itst.oiosaml.userassertion");
/* 130 */     UserAssertionHolder.set(null);
/*     */     
/* 132 */     String str = paramRequestContext.getRequest().getParameter("RelayState");
/* 133 */     OIOAuthnRequest oIOAuthnRequest = OIOAuthnRequest.buildAuthnRequest(endpoint.getLocation(), paramRequestContext.getSpMetadata().getEntityID(), paramRequestContext.getSpMetadata().getDefaultAssertionConsumerService().getBinding(), paramRequestContext.getSessionHandler(), str, paramRequestContext.getSpMetadata().getDefaultAssertionConsumerService().getLocation());
/* 134 */     oIOAuthnRequest.setNameIDPolicy(configuration.getString("oiosaml-sp.nameid.policy", null), configuration.getBoolean("oiosaml-sp.nameid.allowcreate", false));
/* 135 */     oIOAuthnRequest.setForceAuthn(isForceAuthnEnabled(httpServletRequest, configuration));
/*     */     
/* 137 */     if (userAssertion == null) {
/* 138 */       oIOAuthnRequest.setPasive(configuration.getBoolean("oiosaml-sp.passive", false));
/*     */     }
/* 140 */     Audit.log(Operation.AUTHNREQUEST_SEND, true, oIOAuthnRequest.getID(), oIOAuthnRequest.toXML());
/*     */     
/* 142 */     paramRequestContext.getSessionHandler().registerRequest(oIOAuthnRequest.getID(), metadata.getEntityID());
/* 143 */     bindingHandler.handle(httpServletRequest, httpServletResponse, paramRequestContext.getCredential(), oIOAuthnRequest);
/*     */   }
/*     */   
/*     */   public void handlePost(RequestContext paramRequestContext) throws ServletException, IOException {
/* 147 */     handleGet(paramRequestContext);
/*     */   }
/*     */   
/*     */   private static boolean isForceAuthnEnabled(HttpServletRequest paramHttpServletRequest, Configuration paramConfiguration) {
/* 151 */     String[] arrayOfString = paramConfiguration.getStringArray("oiosaml-sp.authn.force");
/* 152 */     if (arrayOfString == null) return false;
/*     */     
/* 154 */     String str = paramHttpServletRequest.getPathInfo();
/* 155 */     if (str == null) {
/* 156 */       str = "/";
/*     */     }
/* 158 */     if (log.isDebugEnabled()) log.debug("ForceAuthn urls: " + Arrays.toString((Object[])arrayOfString) + "; path: " + str);
/*     */ 
/*     */     
/* 161 */     for (String str1 : arrayOfString) {
/* 162 */       if (str.matches(str1.trim())) {
/* 163 */         if (log.isDebugEnabled()) log.debug("Requested url " + str + " is in forceauthn list " + Arrays.toString((Object[])arrayOfString)); 
/* 164 */         return true;
/*     */       } 
/*     */     } 
/*     */ 
/*     */     
/* 169 */     if (paramHttpServletRequest.getParameterMap().containsKey("forceAuthn")) {
/* 170 */       String str1 = paramHttpServletRequest.getParameter("forceAuthn");
/* 171 */       return str1.toLowerCase().equals("true");
/*     */     } 
/*     */     
/* 174 */     return false;
/*     */   }
/*     */   
/*     */   private static void promptIdp(RequestContext paramRequestContext, String paramString) throws ServletException, IOException {
/* 178 */     log.debug("Prompting user for IdP");
/*     */     
/* 180 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 181 */     for (String str1 : paramRequestContext.getIdpMetadata().getEntityIDs()) {
/* 182 */       StringBuilder stringBuilder = new StringBuilder(paramString);
/* 183 */       if (paramString.indexOf('?') > -1) {
/* 184 */         stringBuilder.append("&");
/*     */       } else {
/* 186 */         stringBuilder.append("?");
/*     */       } 
/* 188 */       stringBuilder.append("_saml_idp").append("=");
/* 189 */       stringBuilder.append(Base64.encodeBytes(str1.getBytes(), 8));
/* 190 */       hashMap.put(str1, stringBuilder.toString());
/*     */     } 
/*     */     
/* 193 */     String str = paramRequestContext.getConfiguration().getString("oiosaml-sp.discovery.prompt.servlet", null);
/* 194 */     if (str != null) {
/* 195 */       HttpServletRequest httpServletRequest = paramRequestContext.getRequest();
/* 196 */       httpServletRequest.setAttribute("entityIds", hashMap);
/* 197 */       httpServletRequest.getRequestDispatcher(str).forward((ServletRequest)httpServletRequest, (ServletResponse)paramRequestContext.getResponse());
/*     */     } else {
/* 199 */       VelocityContext velocityContext = new VelocityContext();
/* 200 */       velocityContext.put("entityIds", hashMap);
/*     */       
/*     */       try {
/* 203 */         HTTPUtils.getEngine().mergeTemplate("idp.vm", "UTF-8", (Context)velocityContext, paramRequestContext.getResponse().getWriter());
/* 204 */       } catch (Exception exception) {
/* 205 */         log.error("Unable to render IdP list", exception);
/* 206 */         throw new ServletException(exception);
/*     */       } 
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/dk/itst/oiosaml/sp/service/LoginHandler.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */