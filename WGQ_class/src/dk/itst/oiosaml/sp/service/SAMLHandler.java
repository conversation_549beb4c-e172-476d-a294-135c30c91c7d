package dk.itst.oiosaml.sp.service;

import java.io.IOException;
import javax.servlet.ServletException;

public interface SAMLHandler {
  void handleGet(RequestContext paramRequestContext) throws ServletException, IOException;
  
  void handlePost(RequestContext paramRequestContext) throws ServletException, IOException;
}


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/dk/itst/oiosaml/sp/service/SAMLHandler.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */