/*     */ package dk.itst.oiosaml.sp.service;
/*     */ 
/*     */ import dk.itst.oiosaml.common.SAMLUtil;
/*     */ import dk.itst.oiosaml.logging.Audit;
/*     */ import dk.itst.oiosaml.logging.Logger;
/*     */ import dk.itst.oiosaml.logging.LoggerFactory;
/*     */ import dk.itst.oiosaml.logging.Operation;
/*     */ import dk.itst.oiosaml.sp.AuthenticationHandler;
/*     */ import dk.itst.oiosaml.sp.PassiveUserAssertion;
/*     */ import dk.itst.oiosaml.sp.UserAssertion;
/*     */ import dk.itst.oiosaml.sp.UserAssertionImpl;
/*     */ import dk.itst.oiosaml.sp.metadata.IdpMetadata;
/*     */ import dk.itst.oiosaml.sp.model.OIOAssertion;
/*     */ import dk.itst.oiosaml.sp.model.OIOResponse;
/*     */ import dk.itst.oiosaml.sp.model.RelayState;
/*     */ import dk.itst.oiosaml.sp.model.validation.AssertionValidator;
/*     */ import dk.itst.oiosaml.sp.service.util.ArtifactExtractor;
/*     */ import dk.itst.oiosaml.sp.service.util.HTTPUtils;
/*     */ import dk.itst.oiosaml.sp.service.util.HttpSOAPClient;
/*     */ import dk.itst.oiosaml.sp.service.util.PostResponseExtractor;
/*     */ import dk.itst.oiosaml.sp.service.util.SOAPClient;
/*     */ import dk.itst.oiosaml.sp.service.util.Utils;
/*     */ import java.io.IOException;
/*     */ import javax.servlet.ServletException;
/*     */ import javax.servlet.http.HttpSession;
/*     */ import org.apache.commons.configuration.Configuration;
/*     */ import org.opensaml.saml2.core.Assertion;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class SAMLAssertionConsumerHandler
/*     */   implements SAMLHandler
/*     */ {
/*     */   private static final long serialVersionUID = -8417816228519917989L;
/*     */   public static final String VERSION = "$Id: SAMLAssertionConsumerHandler.java 2910 2008-05-21 13:07:31Z jre $";
/*  80 */   private static final Logger log = LoggerFactory.getLogger(SAMLAssertionConsumerHandler.class);
/*     */   private SOAPClient client;
/*     */   private final AssertionValidator validator;
/*     */   
/*     */   public SAMLAssertionConsumerHandler(Configuration paramConfiguration) {
/*  85 */     this.validator = (AssertionValidator)Utils.newInstance(paramConfiguration, "oiosaml-sp.assertion.validator");
/*  86 */     setSoapClient((SOAPClient)new HttpSOAPClient());
/*     */   }
/*     */   
/*     */   public void setSoapClient(SOAPClient paramSOAPClient) {
/*  90 */     this.client = paramSOAPClient;
/*     */   }
/*     */   
/*     */   public void handlePost(RequestContext paramRequestContext) throws IOException, ServletException {
/*  94 */     PostResponseExtractor postResponseExtractor = new PostResponseExtractor();
/*  95 */     handleSAMLResponse(paramRequestContext, postResponseExtractor.extract(paramRequestContext.getRequest()));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void handleGet(RequestContext paramRequestContext) throws IOException, ServletException {
/* 102 */     if (paramRequestContext.getRequest().getParameter("SAMLResponse") != null) {
/* 103 */       handlePost(paramRequestContext);
/*     */     }
/*     */     else {
/*     */       
/* 107 */       ArtifactExtractor artifactExtractor = new ArtifactExtractor(paramRequestContext.getIdpMetadata(), paramRequestContext.getSpMetadata().getEntityID(), this.client, paramRequestContext.getConfiguration().getString("oiosaml-sp.resolve.username"), paramRequestContext.getConfiguration().getString("oiosaml-sp.resolve.password"), paramRequestContext.getConfiguration().getBoolean("oiosaml-sp.resolve.ignorecert", false));
/* 108 */       handleSAMLResponse(paramRequestContext, artifactExtractor.extract(paramRequestContext.getRequest()));
/*     */     } 
/*     */   }
/*     */   
/*     */   private void handleSAMLResponse(RequestContext paramRequestContext, OIOResponse paramOIOResponse) throws IOException, ServletException {
/* 113 */     Audit.log(Operation.AUTHNREQUEST_SEND, false, paramOIOResponse.getInResponseTo(), paramOIOResponse.toXML());
/*     */     
/* 115 */     HttpSession httpSession = paramRequestContext.getSession();
/*     */     
/* 117 */     if (log.isDebugEnabled()) {
/* 118 */       log.debug("Calling URL.:" + paramRequestContext.getRequest().getRequestURI() + "?" + paramRequestContext.getRequest().getQueryString());
/* 119 */       log.debug("SessionId..:" + httpSession.getId());
/*     */     } 
/*     */     
/* 122 */     RelayState relayState = RelayState.fromRequest(paramRequestContext.getRequest());
/* 123 */     if (log.isDebugEnabled()) {
/* 124 */       log.debug("Got relayState..:" + relayState);
/*     */     }
/* 126 */     String str = paramOIOResponse.getOriginatingIdpEntityId(paramRequestContext.getSessionHandler());
/* 127 */     if (log.isDebugEnabled()) {
/* 128 */       log.debug("Received SAML Response from " + str + ": " + paramOIOResponse.toXML());
/*     */     }
/* 130 */     boolean bool = paramRequestContext.getConfiguration().getBoolean("oiosaml-sp.passive", false);
/* 131 */     IdpMetadata.Metadata metadata = paramRequestContext.getIdpMetadata().getMetadata(str);
/* 132 */     paramOIOResponse.decryptAssertion(paramRequestContext.getCredential(), !paramRequestContext.getConfiguration().getBoolean("oiosaml-sp.encryption.force", false));
/*     */ 
/*     */     
/* 135 */     if (bool && paramOIOResponse.isPassive()) {
/* 136 */       log.debug("Received passive response, setting passive userassertion");
/* 137 */       Assertion assertion = (Assertion)SAMLUtil.buildXMLObject(Assertion.class);
/* 138 */       assertion.setID("" + System.currentTimeMillis());
/* 139 */       paramRequestContext.getSessionHandler().setAssertion(httpSession.getId(), new OIOAssertion(assertion));
/* 140 */       PassiveUserAssertion passiveUserAssertion = new PassiveUserAssertion(paramRequestContext.getConfiguration().getString("oiosaml-sp.passive.user"));
/* 141 */       httpSession.setAttribute("dk.itst.oiosaml.userassertion", passiveUserAssertion);
/*     */       
/* 143 */       Audit.log(Operation.LOGIN, passiveUserAssertion.getSubject());
/*     */     } else {
/*     */       
/* 146 */       OIOAssertion oIOAssertion = paramOIOResponse.getAssertion();
/*     */       
/* 148 */       oIOAssertion.validateAssertion(this.validator, paramRequestContext.getSpMetadata().getEntityID(), paramRequestContext.getSpMetadata().getAssertionConsumerServiceLocation(0));
/*     */       
/* 150 */       UserAssertionImpl userAssertionImpl = new UserAssertionImpl(oIOAssertion);
/* 151 */       if (!invokeAuthenticationHandler(paramRequestContext, (UserAssertion)userAssertionImpl)) {
/* 152 */         Audit.logError(Operation.LOGIN, false, paramOIOResponse.getInResponseTo(), "Authentication handler stopped authentication");
/* 153 */         log.error("Authentication handler stopped authentication");
/*     */         return;
/*     */       } 
/* 156 */       Audit.setAssertionId(oIOAssertion.getID());
/* 157 */       Audit.log(Operation.LOGIN, oIOAssertion.getSubjectNameIDValue() + "/" + oIOAssertion.getAssuranceLevel() + " via " + oIOAssertion.getIssuer());
/* 158 */       Audit.log(Operation.LOGIN_SESSION, Integer.toString(httpSession.getMaxInactiveInterval()));
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 163 */       Assertion assertion = oIOAssertion.getAssertion();
/* 164 */       assertion.releaseChildrenDOM(true);
/* 165 */       assertion.releaseDOM();
/* 166 */       assertion.detach();
/*     */       
/* 168 */       paramRequestContext.getSessionHandler().setAssertion(httpSession.getId(), oIOAssertion);
/* 169 */       httpSession.setAttribute("dk.itst.oiosaml.userassertion", userAssertionImpl);
/*     */     } 
/*     */     
/* 172 */     if (relayState.getRelayState() != null) {
/* 173 */       HTTPUtils.sendResponse(paramRequestContext.getSessionHandler().getRequest(relayState.getRelayState()), paramRequestContext);
/*     */     } else {
/*     */       
/* 176 */       HTTPUtils.sendResponse(null, paramRequestContext);
/*     */     } 
/*     */   }
/*     */   
/*     */   private static boolean invokeAuthenticationHandler(RequestContext paramRequestContext, UserAssertion paramUserAssertion) {
/* 181 */     String str = paramRequestContext.getConfiguration().getString("oiosaml-sp.authenticationhandler", null);
/* 182 */     if (str != null) {
/* 183 */       log.debug("Authentication handler: " + str);
/*     */       
/* 185 */       AuthenticationHandler authenticationHandler = (AuthenticationHandler)Utils.newInstance(paramRequestContext.getConfiguration(), "oiosaml-sp.authenticationhandler");
/* 186 */       return authenticationHandler.userAuthenticated(paramUserAssertion, paramRequestContext.getRequest(), paramRequestContext.getResponse());
/*     */     } 
/*     */     
/* 189 */     log.debug("No authentication handler configured");
/* 190 */     return true;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/dk/itst/oiosaml/sp/service/SAMLAssertionConsumerHandler.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */