/*    */ package dk.itst.oiosaml.sp.service;
/*    */ 
/*    */ import java.io.IOException;
/*    */ import java.io.PrintWriter;
/*    */ import java.security.cert.CertificateEncodingException;
/*    */ import javax.servlet.ServletException;
/*    */ import javax.servlet.http.HttpServletResponse;
/*    */ import org.opensaml.xml.security.x509.X509Credential;
/*    */ import org.opensaml.xml.util.Base64;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class CertificateHandler
/*    */   implements SAMLHandler
/*    */ {
/*    */   public void handleGet(RequestContext paramRequestContext) throws ServletException, IOException {
/* 39 */     X509Credential x509Credential = (X509Credential)paramRequestContext.getCredential();
/*    */     try {
/* 41 */       String str = Base64.encodeBytes(x509Credential.getEntityCertificate().getEncoded());
/*    */       
/* 43 */       HttpServletResponse httpServletResponse = paramRequestContext.getResponse();
/* 44 */       httpServletResponse.setContentType("text/plain");
/* 45 */       PrintWriter printWriter = httpServletResponse.getWriter();
/* 46 */       printWriter.println("-----BEGIN CERTIFICATE-----");
/* 47 */       printWriter.println(str);
/* 48 */       printWriter.println("-----END CERTIFICATE-----");
/* 49 */       printWriter.close();
/* 50 */     } catch (CertificateEncodingException certificateEncodingException) {
/* 51 */       throw new ServletException(certificateEncodingException);
/*    */     } 
/*    */   }
/*    */   
/*    */   public void handlePost(RequestContext paramRequestContext) throws ServletException, IOException {
/* 56 */     throw new UnsupportedOperationException();
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/dk/itst/oiosaml/sp/service/CertificateHandler.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */