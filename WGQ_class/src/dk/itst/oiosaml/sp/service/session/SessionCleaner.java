/*    */ package dk.itst.oiosaml.sp.service.session;
/*    */ 
/*    */ import dk.itst.oiosaml.logging.Logger;
/*    */ import dk.itst.oiosaml.logging.LoggerFactory;
/*    */ import java.util.Timer;
/*    */ import java.util.TimerTask;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class SessionCleaner
/*    */ {
/* 33 */   private static final Logger log = LoggerFactory.getLogger(SessionCleaner.class);
/*    */   
/* 35 */   private static Timer cleanupTimer = null;
/*    */   
/*    */   public static void startCleaner(final SessionHandler handler, int paramInt1, int paramInt2) {
/* 38 */     log.info("Starting session cleaner");
/*    */     
/* 40 */     if (cleanupTimer != null) {
/* 41 */       cleanupTimer.cancel();
/*    */     }
/*    */     
/* 44 */     cleanupTimer = new Timer("Session Cleanup");
/* 45 */     final long sessionCleanupDelay = paramInt1 * 1000L;
/* 46 */     final long requestIdsCleanupDelay = paramInt2 * 1000L;
/*    */     
/* 48 */     cleanupTimer.schedule(new TimerTask() {
/*    */           public void run() {
/* 50 */             SessionCleaner.log.debug("Cleaning sessions older than " + sessionCleanupDelay + " and request ids older than " + requestIdsCleanupDelay);
/*    */             
/* 52 */             handler.cleanup(requestIdsCleanupDelay, sessionCleanupDelay);
/*    */           }
/*    */         }l1, l1);
/*    */   }
/*    */   
/*    */   public static void stopCleaner() {
/* 58 */     if (cleanupTimer != null) {
/* 59 */       cleanupTimer.cancel();
/* 60 */       cleanupTimer = null;
/*    */     } 
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/dk/itst/oiosaml/sp/service/session/SessionCleaner.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */