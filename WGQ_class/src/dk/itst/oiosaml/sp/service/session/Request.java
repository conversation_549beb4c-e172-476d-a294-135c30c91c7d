/*    */ package dk.itst.oiosaml.sp.service.session;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import javax.servlet.http.HttpServletRequest;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class Request
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 8582710873277995206L;
/*    */   private final String requestURI;
/*    */   private final String queryString;
/*    */   private final String method;
/*    */   private final Map<String, String[]> parameters;
/*    */   
/*    */   public Request(String paramString1, String paramString2, String paramString3, Map<String, String[]> paramMap) {
/* 42 */     this.requestURI = paramString1;
/*    */ 
/*    */     
/* 45 */     this.queryString = (paramString2 == null) ? null : paramString2.replaceAll("forceAuthn=.*?($|[&;])", "");
/*    */     
/* 47 */     this.method = paramString3;
/*    */ 
/*    */     
/* 50 */     if (paramMap != null)
/* 51 */       paramMap.remove("forceAuthn"); 
/* 52 */     this.parameters = paramMap;
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public static Request fromHttpRequest(HttpServletRequest paramHttpServletRequest) {
/* 58 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 59 */     hashMap.putAll(paramHttpServletRequest.getParameterMap());
/* 60 */     return new Request(paramHttpServletRequest.getRequestURI(), paramHttpServletRequest.getQueryString(), paramHttpServletRequest.getMethod(), (Map)hashMap);
/*    */   }
/*    */ 
/*    */   
/*    */   public String getMethod() {
/* 65 */     return this.method;
/*    */   }
/*    */   
/*    */   public Map<String, String[]> getParameters() {
/* 69 */     return this.parameters;
/*    */   }
/*    */   
/*    */   public String getQueryString() {
/* 73 */     return this.queryString;
/*    */   }
/*    */   
/*    */   public String getRequestURI() {
/* 77 */     return this.requestURI;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/dk/itst/oiosaml/sp/service/session/Request.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */