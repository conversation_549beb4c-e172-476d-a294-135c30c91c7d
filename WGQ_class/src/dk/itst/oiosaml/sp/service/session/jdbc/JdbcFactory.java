/*     */ package dk.itst.oiosaml.sp.service.session.jdbc;
/*     */ 
/*     */ import dk.itst.oiosaml.sp.service.session.SessionHandler;
/*     */ import dk.itst.oiosaml.sp.service.session.SessionHandlerFactory;
/*     */ import java.io.PrintWriter;
/*     */ import java.sql.Connection;
/*     */ import java.sql.DriverManager;
/*     */ import java.sql.SQLException;
/*     */ import java.sql.SQLFeatureNotSupportedException;
/*     */ import java.util.logging.Logger;
/*     */ import javax.sql.DataSource;
/*     */ import org.apache.commons.configuration.Configuration;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class JdbcFactory
/*     */   implements SessionHandlerFactory
/*     */ {
/*     */   private String url;
/*     */   private String username;
/*     */   private String password;
/*     */   private String driver;
/*     */   
/*     */   public void close() {}
/*     */   
/*     */   public void configure(Configuration paramConfiguration) {
/*  67 */     this.url = paramConfiguration.getString("oiosaml-sp.sessionhandler.jdbc.url");
/*  68 */     this.username = paramConfiguration.getString("oiosaml-sp.sessionhandler.jdbc.username");
/*  69 */     this.password = paramConfiguration.getString("oiosaml-sp.sessionhandler.jdbc.password");
/*  70 */     this.driver = paramConfiguration.getString("oiosaml-sp.sessionhandler.jdbc.driver");
/*     */     
/*     */     try {
/*  73 */       Class.forName(this.driver);
/*  74 */     } catch (ClassNotFoundException classNotFoundException) {
/*  75 */       throw new RuntimeException("Unable to load driver " + this.driver, classNotFoundException);
/*     */     } 
/*     */   }
/*     */   
/*     */   public SessionHandler getHandler() {
/*  80 */     return new JdbcSessionHandler(new DS());
/*     */   }
/*     */   
/*     */   private class DS implements DataSource {
/*     */     public Connection getConnection() throws SQLException {
/*  85 */       return DriverManager.getConnection(JdbcFactory.this.url, JdbcFactory.this.username, JdbcFactory.this.password);
/*     */     }
/*     */ 
/*     */     
/*     */     public Connection getConnection(String param1String1, String param1String2) throws SQLException {
/*  90 */       throw new UnsupportedOperationException();
/*     */     }
/*     */     private DS() {}
/*     */     public PrintWriter getLogWriter() throws SQLException {
/*  94 */       throw new UnsupportedOperationException();
/*     */     }
/*     */     
/*     */     public int getLoginTimeout() throws SQLException {
/*  98 */       throw new UnsupportedOperationException();
/*     */     }
/*     */     
/*     */     public void setLogWriter(PrintWriter param1PrintWriter) throws SQLException {
/* 102 */       throw new UnsupportedOperationException();
/*     */     }
/*     */     
/*     */     public void setLoginTimeout(int param1Int) throws SQLException {
/* 106 */       throw new UnsupportedOperationException();
/*     */     }
/*     */     
/*     */     public boolean isWrapperFor(Class<?> param1Class) throws SQLException {
/* 110 */       throw new UnsupportedOperationException();
/*     */     }
/*     */     
/*     */     public <T> T unwrap(Class<T> param1Class) throws SQLException {
/* 114 */       throw new UnsupportedOperationException();
/*     */     }
/*     */ 
/*     */     
/*     */     public Logger getParentLogger() throws SQLFeatureNotSupportedException {
/* 119 */       return null;
/*     */     }
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/dk/itst/oiosaml/sp/service/session/jdbc/JdbcFactory.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */