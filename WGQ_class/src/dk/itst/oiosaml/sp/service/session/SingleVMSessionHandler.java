/*     */ package dk.itst.oiosaml.sp.service.session;
/*     */ 
/*     */ import dk.itst.oiosaml.logging.Audit;
/*     */ import dk.itst.oiosaml.logging.Logger;
/*     */ import dk.itst.oiosaml.logging.LoggerFactory;
/*     */ import dk.itst.oiosaml.logging.Operation;
/*     */ import dk.itst.oiosaml.sp.model.OIOAssertion;
/*     */ import dk.itst.oiosaml.sp.service.util.Utils;
/*     */ import java.util.Map;
/*     */ import java.util.concurrent.ConcurrentHashMap;
/*     */ import javax.servlet.http.HttpSession;
/*     */ import org.apache.commons.collections.map.LRUMap;
/*     */ import org.opensaml.saml2.core.Issuer;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class SingleVMSessionHandler
/*     */   implements SessionHandler
/*     */ {
/*  50 */   private static final Logger log = LoggerFactory.getLogger(SingleVMSessionHandler.class);
/*     */   
/*  52 */   private final Map<String, TimeOutWrapper<OIOAssertion>> sessionMap = new ConcurrentHashMap<>();
/*  53 */   private final Map<String, TimeOutWrapper<String>> sessionIndexMap = new ConcurrentHashMap<>();
/*  54 */   private final Map<String, TimeOutWrapper<String>> requestIds = new ConcurrentHashMap<>();
/*  55 */   private final Map<String, TimeOutWrapper<Request>> requests = new ConcurrentHashMap<>();
/*  56 */   private Map<String, String> usedAssertionIds = (Map<String, String>)new LRUMap(10000);
/*     */   
/*     */   public synchronized void setAssertion(String paramString, OIOAssertion paramOIOAssertion) throws IllegalArgumentException {
/*  59 */     Issuer issuer = paramOIOAssertion.getAssertion().getIssuer();
/*  60 */     String str1 = ((issuer != null) ? issuer.getValue() : "unknown") + ":" + paramOIOAssertion.getAssertion().getID();
/*  61 */     if (this.usedAssertionIds.containsKey(str1)) {
/*  62 */       throw new IllegalArgumentException("Assertion ID begin replayed: " + str1);
/*     */     }
/*  64 */     this.usedAssertionIds.put(str1, paramOIOAssertion.getAssertion().getID());
/*  65 */     this.sessionMap.put(paramString, new TimeOutWrapper<>(paramOIOAssertion));
/*     */     
/*  67 */     String str2 = paramOIOAssertion.getSessionIndex();
/*  68 */     if (str2 != null) {
/*     */       
/*  70 */       this.sessionIndexMap.remove(str2);
/*     */ 
/*     */       
/*  73 */       this.sessionIndexMap.put(str2, new TimeOutWrapper<>(paramString));
/*     */     } 
/*     */   }
/*     */   
/*     */   public boolean isLoggedIn(String paramString) {
/*  78 */     OIOAssertion oIOAssertion = getAssertion(paramString);
/*  79 */     return (oIOAssertion != null && !oIOAssertion.hasSessionExpired());
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public void logOut(HttpSession paramHttpSession) {
/*  85 */     removeAssertion(paramHttpSession.getId());
/*  86 */     paramHttpSession.removeAttribute("dk.itst.oiosaml.userassertion");
/*     */   }
/*     */   
/*     */   private void removeAssertion(String paramString) {
/*  90 */     TimeOutWrapper<OIOAssertion> timeOutWrapper = this.sessionMap.remove(paramString);
/*  91 */     if (timeOutWrapper != null) {
/*  92 */       OIOAssertion oIOAssertion = timeOutWrapper.getObject();
/*     */       
/*  94 */       if (oIOAssertion != null) {
/*  95 */         String str = oIOAssertion.getSessionIndex();
/*  96 */         if (str != null) {
/*  97 */           this.sessionIndexMap.remove(str);
/*     */         }
/*     */       } 
/*     */     } 
/*     */   }
/*     */   
/*     */   public void logOut(String paramString) {
/* 104 */     removeAssertion(paramString);
/*     */   }
/*     */   
/*     */   public synchronized OIOAssertion getAssertion(String paramString) {
/* 108 */     if (paramString == null) {
/* 109 */       return null;
/*     */     }
/* 111 */     if (!this.sessionMap.containsKey(paramString)) {
/* 112 */       return null;
/*     */     }
/* 114 */     TimeOutWrapper<OIOAssertion> timeOutWrapper = this.sessionMap.get(paramString);
/* 115 */     timeOutWrapper.setAccesstime();
/* 116 */     return timeOutWrapper.getObject();
/*     */   }
/*     */   
/*     */   public String getRelatedSessionId(String paramString) {
/* 120 */     return ((TimeOutWrapper<String>)this.sessionIndexMap.get(paramString)).getObject();
/*     */   }
/*     */   
/*     */   public Object clone() throws CloneNotSupportedException {
/* 124 */     throw new CloneNotSupportedException();
/*     */   }
/*     */   
/*     */   public void registerRequest(String paramString1, String paramString2) {
/* 128 */     if (log.isDebugEnabled()) log.debug("Registered id " + paramString1 + " for " + paramString2 + "(size: " + this.requestIds.size() + ")");
/*     */ 
/*     */     
/* 131 */     this.requestIds.put(paramString1, new TimeOutWrapper<>(paramString2));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String removeEntityIdForRequest(String paramString) {
/* 140 */     if (log.isDebugEnabled()) log.debug("Removing id " + paramString);
/*     */     
/* 142 */     TimeOutWrapper<String> timeOutWrapper = this.requestIds.remove(paramString);
/* 143 */     if (timeOutWrapper == null) {
/* 144 */       throw new IllegalArgumentException("Request id " + paramString + " is unknown");
/*     */     }
/* 146 */     if (log.isDebugEnabled()) log.debug("Entity for request " + paramString + ": " + (String)timeOutWrapper.getObject()); 
/* 147 */     return timeOutWrapper.getObject();
/*     */   }
/*     */ 
/*     */   
/*     */   public void cleanup(long paramLong1, long paramLong2) {
/* 152 */     cleanup(this.sessionMap, paramLong2, "Session ");
/* 153 */     cleanup(this.requestIds, paramLong1, "Request ");
/* 154 */     cleanup(this.sessionIndexMap, paramLong2, "SessionIndex ");
/* 155 */     cleanup(this.requests, paramLong2, "Request ");
/*     */   }
/*     */   
/*     */   private <E, T> void cleanup(Map<E, TimeOutWrapper<T>> paramMap, long paramLong, String paramString) {
/* 159 */     if (log.isDebugEnabled()) log.debug(hashCode() + " Running cleanup timer on " + paramMap); 
/* 160 */     for (E e : paramMap.keySet()) {
/* 161 */       TimeOutWrapper<OIOAssertion> timeOutWrapper = (TimeOutWrapper)paramMap.get(e);
/* 162 */       if (timeOutWrapper.isExpired(paramLong)) {
/* 163 */         log.debug("Expiring " + timeOutWrapper);
/* 164 */         if (timeOutWrapper.getObject() instanceof OIOAssertion) {
/* 165 */           OIOAssertion oIOAssertion = timeOutWrapper.getObject();
/* 166 */           Audit.logSystem(null, oIOAssertion.getID(), Operation.TIMEOUT, oIOAssertion.getSubjectNameIDValue());
/*     */         } 
/* 168 */         paramMap.remove(e);
/*     */       } 
/*     */     } 
/*     */   }
/*     */   
/*     */   public void resetReplayProtection(int paramInt) {
/* 174 */     this.usedAssertionIds = (Map<String, String>)new LRUMap(paramInt);
/*     */   }
/*     */   
/*     */   public String saveRequest(Request paramRequest) {
/* 178 */     String str = Utils.generateUUID();
/* 179 */     this.requests.put(str, new TimeOutWrapper<>(paramRequest));
/* 180 */     return str;
/*     */   }
/*     */   
/*     */   public Request getRequest(String paramString) throws IllegalArgumentException {
/* 184 */     TimeOutWrapper<Request> timeOutWrapper = this.requests.remove(paramString);
/* 185 */     if (timeOutWrapper == null) {
/* 186 */       throw new IllegalArgumentException("No request for state " + paramString);
/*     */     }
/* 188 */     return timeOutWrapper.getObject();
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/dk/itst/oiosaml/sp/service/session/SingleVMSessionHandler.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */