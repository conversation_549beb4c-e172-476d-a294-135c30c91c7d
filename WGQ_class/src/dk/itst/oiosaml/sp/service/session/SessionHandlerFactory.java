/*    */ package dk.itst.oiosaml.sp.service.session;
/*    */ 
/*    */ import dk.itst.oiosaml.logging.Logger;
/*    */ import dk.itst.oiosaml.logging.LoggerFactory;
/*    */ import dk.itst.oiosaml.sp.service.util.Utils;
/*    */ import org.apache.commons.configuration.Configuration;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public interface SessionHandlerFactory
/*    */ {
/*    */   SessionHandler getHandler();
/*    */   
/*    */   void close();
/*    */   
/*    */   void configure(Configuration paramConfiguration);
/*    */   
/*    */   public static class Factory
/*    */   {
/* 59 */     private static final Logger log = LoggerFactory.getLogger(SessionHandlerFactory.class);
/*    */     
/*    */     private static SessionHandlerFactory instance;
/*    */     
/*    */     public static synchronized SessionHandlerFactory newInstance(Configuration param1Configuration) {
/* 64 */       if (log.isDebugEnabled()) log.debug("Creating new handler factory: " + instance + ", config: " + param1Configuration);
/*    */       
/* 66 */       if (instance != null) return instance;
/*    */       
/* 68 */       if (param1Configuration == null) return null;
/*    */       
/* 70 */       String str = param1Configuration.getString("oiosaml-sp.sessionhandler.factory");
/* 71 */       if (log.isDebugEnabled()) log.debug("Using session handler factory class: " + str);
/*    */       
/* 73 */       SessionHandlerFactory sessionHandlerFactory = (SessionHandlerFactory)Utils.newInstance(param1Configuration, "oiosaml-sp.sessionhandler.factory");
/* 74 */       sessionHandlerFactory.configure(param1Configuration);
/*    */       
/* 76 */       instance = sessionHandlerFactory;
/*    */       
/* 78 */       return sessionHandlerFactory;
/*    */     }
/*    */     
/*    */     public static void close() {
/* 82 */       instance = null;
/*    */     }
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/dk/itst/oiosaml/sp/service/session/SessionHandlerFactory.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */