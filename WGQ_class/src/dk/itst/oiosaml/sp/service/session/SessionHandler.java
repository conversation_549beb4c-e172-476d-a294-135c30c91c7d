package dk.itst.oiosaml.sp.service.session;

import dk.itst.oiosaml.sp.model.OIOAssertion;
import javax.servlet.http.HttpSession;

public interface SessionHandler {
  void setAssertion(String paramString, OIOAssertion paramOIOAssertion) throws IllegalArgumentException;
  
  boolean isLoggedIn(String paramString);
  
  void logOut(HttpSession paramHttpSession);
  
  void logOut(String paramString);
  
  OIOAssertion getAssertion(String paramString);
  
  String getRelatedSessionId(String paramString);
  
  void registerRequest(String paramString1, String paramString2);
  
  String removeEntityIdForRequest(String paramString) throws IllegalArgumentException;
  
  void cleanup(long paramLong1, long paramLong2);
  
  void resetReplayProtection(int paramInt);
  
  String saveRequest(Request paramRequest);
  
  Request getRequest(String paramString) throws IllegalArgumentException;
}


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/dk/itst/oiosaml/sp/service/session/SessionHandler.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */