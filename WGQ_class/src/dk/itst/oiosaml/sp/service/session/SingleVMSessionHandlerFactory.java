/*    */ package dk.itst.oiosaml.sp.service.session;
/*    */ 
/*    */ import dk.itst.oiosaml.logging.Logger;
/*    */ import dk.itst.oiosaml.logging.LoggerFactory;
/*    */ import org.apache.commons.configuration.Configuration;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class SingleVMSessionHandlerFactory
/*    */   implements SessionHandlerFactory
/*    */ {
/* 31 */   private static final Logger log = LoggerFactory.getLogger(SingleVMSessionHandlerFactory.class);
/*    */   
/*    */   private SingleVMSessionHandler instance;
/*    */   
/*    */   public void close() {
/* 36 */     log.debug("Closing factory with instance " + this.instance);
/* 37 */     this.instance = null;
/*    */   }
/*    */   
/*    */   public void configure(Configuration paramConfiguration) {
/* 41 */     this.instance = new SingleVMSessionHandler();
/*    */   }
/*    */   
/*    */   public SessionHandler getHandler() {
/* 45 */     if (this.instance == null) throw new IllegalStateException("Instance is null, please call configure before getHandler"); 
/* 46 */     return this.instance;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/dk/itst/oiosaml/sp/service/session/SingleVMSessionHandlerFactory.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */