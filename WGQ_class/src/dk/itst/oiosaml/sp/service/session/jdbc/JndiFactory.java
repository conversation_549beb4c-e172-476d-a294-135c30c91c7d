/*    */ package dk.itst.oiosaml.sp.service.session.jdbc;
/*    */ 
/*    */ import dk.itst.oiosaml.sp.service.session.SessionHandler;
/*    */ import dk.itst.oiosaml.sp.service.session.SessionHandlerFactory;
/*    */ import javax.naming.InitialContext;
/*    */ import javax.naming.NamingException;
/*    */ import javax.sql.DataSource;
/*    */ import org.apache.commons.configuration.Configuration;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class JndiFactory
/*    */   implements SessionHandlerFactory
/*    */ {
/*    */   private String name;
/*    */   
/*    */   public void close() {}
/*    */   
/*    */   public void configure(Configuration paramConfiguration) {
/* 53 */     this.name = paramConfiguration.getString("oiosaml-sp.sessionhandler.jndi");
/*    */   }
/*    */   
/*    */   public SessionHandler getHandler() {
/*    */     try {
/* 58 */       InitialContext initialContext = new InitialContext();
/* 59 */       DataSource dataSource = (DataSource)initialContext.lookup(this.name);
/*    */       
/* 61 */       return new JdbcSessionHandler(dataSource);
/* 62 */     } catch (NamingException namingException) {
/* 63 */       throw new RuntimeException(namingException);
/*    */     } 
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/dk/itst/oiosaml/sp/service/session/jdbc/JndiFactory.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */