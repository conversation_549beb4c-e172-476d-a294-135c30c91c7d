/*     */ package dk.itst.oiosaml.sp.service.session.jdbc;
/*     */ 
/*     */ import dk.itst.oiosaml.common.SAMLUtil;
/*     */ import dk.itst.oiosaml.logging.Logger;
/*     */ import dk.itst.oiosaml.logging.LoggerFactory;
/*     */ import dk.itst.oiosaml.sp.model.OIOAssertion;
/*     */ import dk.itst.oiosaml.sp.service.session.Request;
/*     */ import dk.itst.oiosaml.sp.service.session.SessionHandler;
/*     */ import dk.itst.oiosaml.sp.service.util.Utils;
/*     */ import java.io.ByteArrayInputStream;
/*     */ import java.io.ByteArrayOutputStream;
/*     */ import java.io.IOException;
/*     */ import java.io.ObjectInputStream;
/*     */ import java.io.ObjectOutputStream;
/*     */ import java.sql.Connection;
/*     */ import java.sql.PreparedStatement;
/*     */ import java.sql.ResultSet;
/*     */ import java.sql.SQLException;
/*     */ import java.sql.Timestamp;
/*     */ import java.util.Date;
/*     */ import javax.servlet.http.HttpSession;
/*     */ import javax.sql.DataSource;
/*     */ import org.opensaml.saml2.core.Assertion;
/*     */ import org.opensaml.xml.util.Base64;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class JdbcSessionHandler
/*     */   implements SessionHandler
/*     */ {
/*  54 */   private static final Logger log = LoggerFactory.getLogger(JdbcSessionHandler.class);
/*     */   private final DataSource ds;
/*  56 */   private static int uniqueId = 0;
/*  57 */   private static int counter = 0;
/*     */   
/*     */   public JdbcSessionHandler(DataSource paramDataSource) {
/*  60 */     this.ds = paramDataSource;
/*     */   }
/*     */   
/*     */   private Connection getConnection() {
/*     */     try {
/*  65 */       Connection connection = this.ds.getConnection();
/*  66 */       connection.setAutoCommit(true);
/*  67 */       return connection;
/*     */     }
/*  69 */     catch (SQLException sQLException) {
/*  70 */       throw new RuntimeException(sQLException);
/*     */     } 
/*     */   }
/*     */   
/*     */   private static void closeConnection(Connection paramConnection) {
/*  75 */     if (paramConnection == null)
/*     */       return; 
/*     */     try {
/*  78 */       paramConnection.close();
/*     */     }
/*  80 */     catch (SQLException sQLException) {
/*  81 */       log.error("Unable to close connection", sQLException);
/*     */     } 
/*     */   }
/*     */   
/*     */   public void cleanup(long paramLong1, long paramLong2) {
/*  86 */     Connection connection = getConnection();
/*  87 */     String[] arrayOfString = { "assertions", "requests", "requestdata" };
/*     */     
/*     */     try {
/*  90 */       for (String str : arrayOfString) {
/*  91 */         PreparedStatement preparedStatement = connection.prepareStatement("DELETE FROM " + str + " WHERE timestamp < ?");
/*  92 */         preparedStatement.setTimestamp(1, new Timestamp((new Date()).getTime() - paramLong2));
/*  93 */         preparedStatement.executeUpdate();
/*     */       }
/*     */     
/*  96 */     } catch (SQLException sQLException) {
/*  97 */       throw new RuntimeException(sQLException);
/*     */     } finally {
/*     */       
/* 100 */       closeConnection(connection);
/*     */     } 
/*     */   }
/*     */   
/*     */   public OIOAssertion getAssertion(String paramString) {
/* 105 */     Connection connection = getConnection();
/*     */     try {
/* 107 */       PreparedStatement preparedStatement = connection.prepareStatement("SELECT assertion FROM assertions WHERE id = ?");
/* 108 */       preparedStatement.setString(1, paramString);
/* 109 */       ResultSet resultSet = preparedStatement.executeQuery();
/* 110 */       if (resultSet.next()) {
/* 111 */         OIOAssertion oIOAssertion = new OIOAssertion((Assertion)SAMLUtil.unmarshallElementFromString(resultSet.getString("assertion")));
/* 112 */         updateTimestamp(paramString, connection);
/*     */         
/* 114 */         return oIOAssertion;
/*     */       } 
/*     */       
/* 117 */       return null;
/*     */     }
/* 119 */     catch (SQLException sQLException) {
/* 120 */       throw new RuntimeException(sQLException);
/*     */     } finally {
/*     */       
/* 123 */       closeConnection(connection);
/*     */     } 
/*     */   }
/*     */   
/*     */   private static void updateTimestamp(String paramString, Connection paramConnection) throws SQLException {
/* 128 */     PreparedStatement preparedStatement = paramConnection.prepareStatement("UPDATE assertions SET timestamp = ? WHERE id = ?");
/* 129 */     preparedStatement.setTimestamp(1, new Timestamp((new Date()).getTime()));
/* 130 */     preparedStatement.setString(2, paramString);
/* 131 */     preparedStatement.executeUpdate();
/* 132 */     preparedStatement.close();
/*     */   }
/*     */   
/*     */   public String getRelatedSessionId(String paramString) {
/* 136 */     Connection connection = getConnection();
/*     */     try {
/* 138 */       PreparedStatement preparedStatement = connection.prepareStatement("SELECT id FROM assertions WHERE sessionindex = ?");
/* 139 */       ResultSet resultSet = preparedStatement.executeQuery();
/* 140 */       if (resultSet.next()) {
/* 141 */         return resultSet.getString("id");
/*     */       }
/*     */       
/* 144 */       return null;
/*     */     }
/* 146 */     catch (SQLException sQLException) {
/* 147 */       throw new RuntimeException(sQLException);
/*     */     } finally {
/*     */       
/* 150 */       closeConnection(connection);
/*     */     } 
/*     */   }
/*     */   
/*     */   public Request getRequest(String paramString) throws IllegalArgumentException {
/* 155 */     Connection connection = getConnection();
/*     */     try {
/* 157 */       PreparedStatement preparedStatement = connection.prepareStatement("SELECT data FROM requestdata WHERE id = ?");
/* 158 */       preparedStatement.setString(1, paramString);
/* 159 */       ResultSet resultSet = preparedStatement.executeQuery();
/* 160 */       if (resultSet.next()) {
/* 161 */         ObjectInputStream objectInputStream = new ObjectInputStream(new ByteArrayInputStream(Base64.decode(resultSet.getString("data"))));
/* 162 */         Request request = (Request)objectInputStream.readObject();
/*     */         
/* 164 */         preparedStatement = connection.prepareStatement("DELETE FROM requestdata where id = ?");
/* 165 */         preparedStatement.setString(1, paramString);
/* 166 */         preparedStatement.executeUpdate();
/*     */         
/* 168 */         return request;
/*     */       } 
/*     */       
/* 171 */       throw new IllegalArgumentException("No state with " + paramString + " registered");
/*     */     }
/* 173 */     catch (Exception exception) {
/* 174 */       throw new RuntimeException(exception);
/*     */     } finally {
/*     */       
/* 177 */       closeConnection(connection);
/*     */     } 
/*     */   }
/*     */   
/*     */   public boolean isLoggedIn(String paramString) {
/* 182 */     OIOAssertion oIOAssertion = getAssertion(paramString);
/* 183 */     return (oIOAssertion != null && !oIOAssertion.hasSessionExpired());
/*     */   }
/*     */   
/*     */   public void logOut(HttpSession paramHttpSession) {
/* 187 */     paramHttpSession.removeAttribute("dk.itst.oiosaml.userassertion");
/* 188 */     logOut(paramHttpSession.getId());
/*     */   }
/*     */   
/*     */   public void logOut(String paramString) {
/* 192 */     Connection connection = getConnection();
/*     */     try {
/* 194 */       PreparedStatement preparedStatement = connection.prepareStatement("DELETE FROM assertions WHERE id = ?");
/* 195 */       preparedStatement.setString(1, paramString);
/* 196 */       preparedStatement.executeUpdate();
/* 197 */       preparedStatement.close();
/*     */     }
/* 199 */     catch (SQLException sQLException) {
/* 200 */       throw new RuntimeException(sQLException);
/*     */     } finally {
/*     */       
/* 203 */       closeConnection(connection);
/*     */     } 
/*     */   }
/*     */   
/*     */   public void registerRequest(String paramString1, String paramString2) {
/* 208 */     Connection connection = getConnection();
/*     */     try {
/* 210 */       PreparedStatement preparedStatement = connection.prepareStatement("INSERT INTO requests (id, receiver, timestamp) VALUES (?, ?, ?)");
/* 211 */       preparedStatement.setString(1, paramString1);
/* 212 */       preparedStatement.setString(2, paramString2);
/* 213 */       preparedStatement.setTimestamp(3, new Timestamp((new Date()).getTime()));
/* 214 */       preparedStatement.executeUpdate();
/* 215 */       preparedStatement.close();
/*     */     }
/* 217 */     catch (SQLException sQLException) {
/* 218 */       throw new RuntimeException(sQLException);
/*     */     } finally {
/*     */       
/* 221 */       closeConnection(connection);
/*     */     } 
/*     */   }
/*     */   
/*     */   public String removeEntityIdForRequest(String paramString) {
/* 226 */     Connection connection = getConnection();
/*     */     try {
/* 228 */       PreparedStatement preparedStatement = connection.prepareStatement("SELECT receiver FROM requests WHERE id = ?");
/* 229 */       preparedStatement.setString(1, paramString);
/* 230 */       ResultSet resultSet = preparedStatement.executeQuery();
/* 231 */       if (resultSet.next()) {
/* 232 */         return resultSet.getString("receiver");
/*     */       }
/*     */       
/* 235 */       throw new IllegalArgumentException("Request with id " + paramString + " is unknown");
/*     */     }
/* 237 */     catch (SQLException sQLException) {
/* 238 */       throw new RuntimeException(sQLException);
/*     */     } finally {
/*     */       
/* 241 */       closeConnection(connection);
/*     */     } 
/*     */   }
/*     */   
/*     */   public void resetReplayProtection(int paramInt) {
/* 246 */     Connection connection = getConnection();
/*     */     try {
/* 248 */       PreparedStatement preparedStatement = connection.prepareStatement("DELETE FROM assertions");
/* 249 */       preparedStatement.executeUpdate();
/*     */     }
/* 251 */     catch (SQLException sQLException) {
/* 252 */       throw new RuntimeException(sQLException);
/*     */     } finally {
/*     */       
/* 255 */       closeConnection(connection);
/*     */     } 
/*     */   }
/*     */   
/*     */   public String saveRequest(Request paramRequest) {
/* 260 */     Connection connection = getConnection();
/*     */     try {
/* 262 */       String str1 = Utils.generateUUID();
/* 263 */       ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
/* 264 */       ObjectOutputStream objectOutputStream = new ObjectOutputStream(byteArrayOutputStream);
/* 265 */       objectOutputStream.writeObject(paramRequest);
/* 266 */       objectOutputStream.close();
/*     */       
/* 268 */       String str2 = Base64.encodeBytes(byteArrayOutputStream.toByteArray());
/*     */       
/* 270 */       PreparedStatement preparedStatement = connection.prepareStatement("INSERT INTO requestdata (id, data, timestamp) VALUES (?, ?, ?)");
/* 271 */       preparedStatement.setString(1, str1);
/* 272 */       preparedStatement.setString(2, str2);
/* 273 */       preparedStatement.setTimestamp(3, new Timestamp((new Date()).getTime()));
/* 274 */       preparedStatement.executeUpdate();
/*     */       
/* 276 */       return str1;
/*     */     }
/* 278 */     catch (SQLException sQLException) {
/* 279 */       throw new RuntimeException(sQLException);
/*     */     }
/* 281 */     catch (IOException iOException) {
/* 282 */       throw new RuntimeException(iOException);
/*     */     } finally {
/*     */       
/* 285 */       closeConnection(connection);
/*     */     } 
/*     */   }
/*     */   
/*     */   public void setAssertion(String paramString, OIOAssertion paramOIOAssertion) throws IllegalArgumentException {
/* 290 */     Connection connection = getConnection();
/*     */     try {
/* 292 */       PreparedStatement preparedStatement = connection.prepareStatement("SELECT 1 FROM assertions WHERE assertionid = ? OR sessionindex = ?");
/* 293 */       preparedStatement.setString(1, paramOIOAssertion.getID());
/* 294 */       preparedStatement.setString(2, paramOIOAssertion.getSessionIndex());
/* 295 */       ResultSet resultSet = preparedStatement.executeQuery();
/* 296 */       if (resultSet.next()) {
/* 297 */         throw new IllegalArgumentException("Assertion with id " + paramOIOAssertion.getID() + " and sessionidx " + paramOIOAssertion.getSessionIndex() + " is already registered");
/*     */       }
/* 299 */       preparedStatement.close();
/*     */       
/* 301 */       preparedStatement = connection.prepareStatement("DELETE FROM assertions WHERE id = ? OR sessionindex = ?");
/* 302 */       preparedStatement.setString(1, paramString);
/* 303 */       preparedStatement.setString(2, paramOIOAssertion.getSessionIndex());
/* 304 */       if (preparedStatement.executeUpdate() > 0) {
/* 305 */         log.debug("Overwriting existing session info for session " + paramString);
/*     */       }
/* 307 */       preparedStatement.close();
/*     */       
/* 309 */       preparedStatement = connection.prepareStatement("INSERT INTO assertions (id, assertion, assertionid, sessionindex, timestamp) VALUES (?, ?, ?, ?, ?)");
/* 310 */       preparedStatement.setString(1, paramString);
/* 311 */       preparedStatement.setString(2, paramOIOAssertion.toXML());
/* 312 */       preparedStatement.setString(3, paramOIOAssertion.getID());
/*     */       
/* 314 */       String str = paramOIOAssertion.getSessionIndex();
/* 315 */       if (str == null) {
/* 316 */         str = getNextPassiveSessionIndex();
/*     */       }
/* 318 */       preparedStatement.setString(4, str);
/*     */       
/* 320 */       preparedStatement.setTimestamp(5, new Timestamp((new Date()).getTime()));
/* 321 */       preparedStatement.execute();
/* 322 */       preparedStatement.close();
/*     */     }
/* 324 */     catch (SQLException sQLException) {
/* 325 */       throw new RuntimeException(sQLException);
/*     */     } finally {
/*     */       
/* 328 */       closeConnection(connection);
/*     */     } 
/*     */   }
/*     */   
/*     */   private String getNextPassiveSessionIndex() {
/* 333 */     String str = null;
/*     */     
/* 335 */     synchronized (this) {
/* 336 */       Connection connection = null;
/*     */       
/*     */       try {
/* 339 */         connection = getConnection();
/*     */         
/* 341 */         for (byte b = 0; b < 10; b++) {
/* 342 */           counter++;
/*     */           
/* 344 */           String str1 = "Passive:" + uniqueId + "x" + counter;
/*     */ 
/*     */           
/* 347 */           PreparedStatement preparedStatement = connection.prepareStatement("SELECT 1 FROM assertions WHERE sessionindex = ?");
/* 348 */           preparedStatement.setString(1, str1);
/* 349 */           ResultSet resultSet = preparedStatement.executeQuery();
/*     */           
/* 351 */           if (!resultSet.next()) {
/* 352 */             str = str1;
/* 353 */             preparedStatement.close();
/*     */             break;
/*     */           } 
/* 356 */           preparedStatement.close();
/*     */         }
/*     */       
/* 359 */       } catch (Exception exception) {
/* 360 */         throw new RuntimeException(exception);
/*     */       } finally {
/*     */         
/* 363 */         closeConnection(connection);
/*     */       } 
/*     */     } 
/*     */     
/* 367 */     if (str == null) {
/* 368 */       throw new RuntimeException("Failed to aquire a unique sessionIndex for passive-login!");
/*     */     }
/*     */     
/* 371 */     return str;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/dk/itst/oiosaml/sp/service/session/jdbc/JdbcSessionHandler.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */