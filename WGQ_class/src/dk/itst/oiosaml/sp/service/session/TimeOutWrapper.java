/*    */ package dk.itst.oiosaml.sp.service.session;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class TimeOutWrapper<T>
/*    */ {
/*    */   private final T object;
/*    */   private long accesstime;
/*    */   
/*    */   public TimeOutWrapper(T paramT) {
/* 31 */     this.object = paramT;
/* 32 */     this.accesstime = System.currentTimeMillis();
/*    */   }
/*    */   
/*    */   public T getObject() {
/* 36 */     return this.object;
/*    */   }
/*    */   
/*    */   public boolean isExpired(long paramLong) {
/* 40 */     return (System.currentTimeMillis() > this.accesstime + paramLong);
/*    */   }
/*    */   
/*    */   public void setAccesstime() {
/* 44 */     this.accesstime = System.currentTimeMillis();
/*    */   }
/*    */ 
/*    */   
/*    */   public String toString() {
/* 49 */     return "TO: " + this.accesstime + ", obj: " + this.object;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/dk/itst/oiosaml/sp/service/session/TimeOutWrapper.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */