/*    */ package dk.itst.oiosaml.sp.service.session;
/*    */ 
/*    */ import dk.itst.oiosaml.logging.Audit;
/*    */ import dk.itst.oiosaml.logging.Logger;
/*    */ import dk.itst.oiosaml.logging.LoggerFactory;
/*    */ import dk.itst.oiosaml.logging.Operation;
/*    */ import dk.itst.oiosaml.sp.model.OIOAssertion;
/*    */ import javax.servlet.http.HttpSessionEvent;
/*    */ import javax.servlet.http.HttpSessionListener;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class SessionDestroyListener
/*    */   implements HttpSessionListener
/*    */ {
/* 41 */   private static final Logger logger = LoggerFactory.getLogger(SessionDestroyListener.class);
/*    */   
/*    */   public void sessionCreated(HttpSessionEvent paramHttpSessionEvent) {
/* 44 */     logger.debug("Session: " + paramHttpSessionEvent);
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public void sessionDestroyed(HttpSessionEvent paramHttpSessionEvent) {
/* 51 */     SessionHandlerFactory sessionHandlerFactory = SessionHandlerFactory.Factory.newInstance(null);
/* 52 */     if (sessionHandlerFactory == null) {
/* 53 */       logger.warn("No SessionHandler configured, skipping session destroy");
/*    */       return;
/*    */     } 
/* 56 */     if (paramHttpSessionEvent.getSession() == null)
/*    */       return; 
/* 58 */     SessionHandler sessionHandler = sessionHandlerFactory.getHandler();
/* 59 */     boolean bool = sessionHandler.isLoggedIn(paramHttpSessionEvent.getSession().getId());
/* 60 */     logger.debug("User logged in: " + bool);
/* 61 */     if (bool) {
/* 62 */       OIOAssertion oIOAssertion = sessionHandler.getAssertion(paramHttpSessionEvent.getSession().getId());
/* 63 */       Audit.logSystem(paramHttpSessionEvent.getSession().getId(), oIOAssertion.getID(), Operation.TIMEOUT, oIOAssertion.getSubjectNameIDValue());
/*    */       
/* 65 */       sessionHandler.logOut(paramHttpSessionEvent.getSession().getId());
/*    */     } else {
/* 67 */       logger.debug("Session destroyed without saml assertion");
/*    */     } 
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/dk/itst/oiosaml/sp/service/session/SessionDestroyListener.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */