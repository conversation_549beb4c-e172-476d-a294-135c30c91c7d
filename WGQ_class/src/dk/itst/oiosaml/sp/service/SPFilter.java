/*     */ package dk.itst.oiosaml.sp.service;
/*     */ 
/*     */ import dk.itst.oiosaml.configuration.SAMLConfiguration;
/*     */ import dk.itst.oiosaml.configuration.SAMLConfigurationFactory;
/*     */ import dk.itst.oiosaml.error.Layer;
/*     */ import dk.itst.oiosaml.error.WrappedException;
/*     */ import dk.itst.oiosaml.logging.Audit;
/*     */ import dk.itst.oiosaml.logging.Logger;
/*     */ import dk.itst.oiosaml.logging.LoggerFactory;
/*     */ import dk.itst.oiosaml.logging.Operation;
/*     */ import dk.itst.oiosaml.sp.UserAssertion;
/*     */ import dk.itst.oiosaml.sp.UserAssertionHolder;
/*     */ import dk.itst.oiosaml.sp.develmode.DevelMode;
/*     */ import dk.itst.oiosaml.sp.develmode.DevelModeImpl;
/*     */ import dk.itst.oiosaml.sp.metadata.CRLChecker;
/*     */ import dk.itst.oiosaml.sp.metadata.IdpMetadata;
/*     */ import dk.itst.oiosaml.sp.metadata.SPMetadata;
/*     */ import dk.itst.oiosaml.sp.service.session.Request;
/*     */ import dk.itst.oiosaml.sp.service.session.SessionCleaner;
/*     */ import dk.itst.oiosaml.sp.service.session.SessionHandler;
/*     */ import dk.itst.oiosaml.sp.service.session.SessionHandlerFactory;
/*     */ import java.io.IOException;
/*     */ import java.util.concurrent.atomic.AtomicBoolean;
/*     */ import javax.servlet.Filter;
/*     */ import javax.servlet.FilterChain;
/*     */ import javax.servlet.FilterConfig;
/*     */ import javax.servlet.RequestDispatcher;
/*     */ import javax.servlet.ServletException;
/*     */ import javax.servlet.ServletRequest;
/*     */ import javax.servlet.ServletResponse;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import javax.servlet.http.HttpSession;
/*     */ import org.apache.commons.configuration.Configuration;
/*     */ import org.opensaml.Configuration;
/*     */ import org.opensaml.DefaultBootstrap;
/*     */ import org.opensaml.xml.ConfigurationException;
/*     */ import org.opensaml.xml.security.BasicSecurityConfiguration;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class SPFilter
/*     */   implements Filter
/*     */ {
/*  98 */   private static final Logger log = LoggerFactory.getLogger(SPFilter.class);
/*  99 */   private CRLChecker crlChecker = new CRLChecker();
/*     */   private boolean filterInitialized;
/*     */   private SAMLConfiguration conf;
/*     */   private String hostname;
/*     */   private SessionHandlerFactory sessionHandlerFactory;
/* 104 */   private AtomicBoolean cleanerRunning = new AtomicBoolean(false);
/*     */   
/*     */   private DevelMode develMode;
/*     */ 
/*     */   
/*     */   static {
/*     */     try {
/* 111 */       DefaultBootstrap.bootstrap();
/* 112 */     } catch (ConfigurationException configurationException) {
/* 113 */       throw new WrappedException(Layer.DATAACCESS, configurationException);
/*     */     } 
/*     */   }
/*     */   
/*     */   public void destroy() {
/* 118 */     SessionCleaner.stopCleaner();
/* 119 */     this.crlChecker.stopChecker();
/* 120 */     if (this.sessionHandlerFactory != null) {
/* 121 */       this.sessionHandlerFactory.close();
/*     */     }
/* 123 */     SessionHandlerFactory.Factory.close();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void doFilter(ServletRequest paramServletRequest, ServletResponse paramServletResponse, FilterChain paramFilterChain) throws IOException, ServletException {
/* 138 */     String str1 = (String)((HttpServletRequest)paramServletRequest).getSession(true).getAttribute("weaver_login_type");
/* 139 */     if (str1 != null && "OALogin".equals(str1)) {
/* 140 */       paramFilterChain.doFilter(paramServletRequest, paramServletResponse);
/*     */       
/*     */       return;
/*     */     } 
/* 144 */     String str2 = paramServletRequest.getParameter("logintype");
/* 145 */     if (str2 != null && "2".equals(str2)) {
/* 146 */       ((HttpServletRequest)paramServletRequest).getSession(true).setAttribute("weaver_login_type", "OALogin");
/* 147 */       paramFilterChain.doFilter(paramServletRequest, paramServletResponse);
/*     */       return;
/*     */     } 
/* 150 */     if (log.isDebugEnabled())
/* 151 */       log.debug("OIOSAML-J SP Filter invoked"); 
/* 152 */     if (!(paramServletRequest instanceof HttpServletRequest)) {
/* 153 */       throw new RuntimeException("Not supported operation...");
/*     */     }
/* 155 */     HttpServletRequest httpServletRequest = (HttpServletRequest)paramServletRequest;
/* 156 */     Audit.init(httpServletRequest);
/* 157 */     if (!isFilterInitialized()) {
/*     */       try {
/* 159 */         Configuration configuration = SAMLConfigurationFactory.getConfiguration().getSystemConfiguration();
/* 160 */         setRuntimeConfiguration(configuration);
/* 161 */       } catch (IllegalStateException illegalStateException) {
/* 162 */         paramServletRequest.getRequestDispatcher("/saml/configure").forward(paramServletRequest, paramServletResponse);
/*     */         return;
/*     */       } 
/*     */     }
/* 166 */     if (this.conf.getSystemConfiguration().getBoolean("oiosaml-sp.develmode", false)) {
/* 167 */       log.warn("Running in debug mode, skipping regular filter");
/* 168 */       this.develMode.doFilter(httpServletRequest, (HttpServletResponse)paramServletResponse, paramFilterChain, this.conf.getSystemConfiguration());
/*     */       return;
/*     */     } 
/* 171 */     if (this.cleanerRunning.compareAndSet(false, true)) {
/* 172 */       SessionCleaner.startCleaner(this.sessionHandlerFactory.getHandler(), ((HttpServletRequest)paramServletRequest).getSession().getMaxInactiveInterval(), 30);
/*     */     }
/* 174 */     SessionHandler sessionHandler = this.sessionHandlerFactory.getHandler();
/* 175 */     if (httpServletRequest.getServletPath().equals(this.conf.getSystemConfiguration().getProperty("oiosaml-sp.servlet"))) {
/* 176 */       log.debug("Request to SAML servlet, access granted");
/* 177 */       paramFilterChain.doFilter((ServletRequest)new SAMLHttpServletRequest(httpServletRequest, this.hostname, null), paramServletResponse);
/*     */       return;
/*     */     } 
/* 180 */     HttpSession httpSession = httpServletRequest.getSession();
/* 181 */     if (log.isDebugEnabled()) {
/* 182 */       log.debug("sessionId....:" + httpSession.getId());
/*     */     }
/* 184 */     Boolean bool = Boolean.valueOf(false);
/* 185 */     if (paramServletRequest.getParameterMap().containsKey("forceAuthn")) {
/*     */       
/* 187 */       String str = paramServletRequest.getParameter("forceAuthn");
/* 188 */       bool = Boolean.valueOf(str.toLowerCase().equals("true"));
/*     */     } 
/*     */ 
/*     */     
/* 192 */     if (sessionHandler.isLoggedIn(httpSession.getId()) && httpSession.getAttribute("dk.itst.oiosaml.userassertion") != null && !bool.booleanValue()) {
/* 193 */       int i = sessionHandler.getAssertion(httpSession.getId()).getAssuranceLevel();
/* 194 */       int j = this.conf.getSystemConfiguration().getInt("oiosaml-sp.assurancelevel");
/* 195 */       if (i > 0 && i < j) {
/* 196 */         sessionHandler.logOut(httpSession);
/* 197 */         log.warn("Assurance level too low: " + i + ", required: " + j);
/* 198 */         throw new RuntimeException("Assurance level too low: " + i + ", required: " + j);
/*     */       } 
/* 200 */       UserAssertion userAssertion = (UserAssertion)httpSession.getAttribute("dk.itst.oiosaml.userassertion");
/* 201 */       if (log.isDebugEnabled())
/* 202 */         log.debug("Everything is ok... Assertion: " + userAssertion); 
/* 203 */       Audit.log(Operation.ACCESS, httpServletRequest.getRequestURI());
/*     */       try {
/* 205 */         UserAssertionHolder.set(userAssertion);
/* 206 */         SAMLHttpServletRequest sAMLHttpServletRequest = new SAMLHttpServletRequest(httpServletRequest, userAssertion, this.hostname);
/* 207 */         paramFilterChain.doFilter((ServletRequest)sAMLHttpServletRequest, paramServletResponse);
/*     */         return;
/*     */       } finally {
/* 210 */         UserAssertionHolder.set(null);
/*     */       } 
/*     */     } 
/*     */     
/* 214 */     User user = (User)httpServletRequest.getSession(true).getAttribute("weaver_user@bean");
/* 215 */     boolean bool1 = false;
/*     */     
/* 217 */     String str3 = httpServletRequest.getServletPath();
/*     */     
/* 219 */     if (str3.indexOf("/login/loginSaml.jsp") < 0) {
/* 220 */       String str = Util.null2String(this.conf.getSystemConfiguration().getString("special-pages"));
/* 221 */       String[] arrayOfString = str.replace("\r", "").replace("\n", "").replaceAll(" ", "").replace("\t", "").split("-");
/* 222 */       for (byte b = 0; b < arrayOfString.length; b++) {
/* 223 */         if (str3.indexOf(arrayOfString[b]) == 0) {
/* 224 */           bool1 = true;
/*     */           
/*     */           break;
/*     */         } 
/*     */       } 
/* 229 */       if (bool1 || user != null) {
/* 230 */         log.error("需要排除地址：" + str3);
/* 231 */         paramFilterChain.doFilter(paramServletRequest, paramServletResponse);
/*     */         
/*     */         return;
/*     */       } 
/*     */     } 
/* 236 */     httpSession.removeAttribute("dk.itst.oiosaml.userassertion");
/* 237 */     UserAssertionHolder.set(null);
/* 238 */     saveRequestAndGotoLogin((HttpServletResponse)paramServletResponse, httpServletRequest);
/*     */   }
/*     */ 
/*     */   
/*     */   protected void saveRequestAndGotoLogin(HttpServletResponse paramHttpServletResponse, HttpServletRequest paramHttpServletRequest) throws ServletException, IOException {
/* 243 */     SessionHandler sessionHandler = this.sessionHandlerFactory.getHandler();
/* 244 */     String str1 = sessionHandler.saveRequest(Request.fromHttpRequest(paramHttpServletRequest));
/* 245 */     String str2 = this.conf.getSystemConfiguration().getString("oiosaml-sp.protocol", "saml20");
/* 246 */     String str3 = this.conf.getSystemConfiguration().getString("oiosaml-sp.servlet", "/saml");
/* 247 */     String str4 = this.conf.getSystemConfiguration().getString("oiosaml-sp.protocol." + str2);
/* 248 */     if (str4 == null) {
/* 249 */       throw new RuntimeException("No protocol url configured for oiosaml-sp.protocol." + str2);
/*     */     }
/* 251 */     str3 = str3 + str4;
/* 252 */     if (log.isDebugEnabled())
/* 253 */       log.debug("Redirecting to " + str2 + " login handler at " + str3); 
/* 254 */     RequestDispatcher requestDispatcher = paramHttpServletRequest.getRequestDispatcher(str3);
/* 255 */     requestDispatcher.forward((ServletRequest)new SAMLHttpServletRequest(paramHttpServletRequest, this.hostname, str1), (ServletResponse)paramHttpServletResponse);
/*     */   }
/*     */   
/*     */   public void init(FilterConfig paramFilterConfig) throws ServletException {
/* 259 */     this.conf = SAMLConfigurationFactory.getConfiguration();
/*     */     
/* 261 */     if (this.conf.isConfigured()) {
/*     */       try {
/* 263 */         Configuration configuration = SAMLConfigurationFactory.getConfiguration().getSystemConfiguration();
/* 264 */         if (configuration.getBoolean("oiosaml-sp.develmode", false)) {
/* 265 */           this.develMode = (DevelMode)new DevelModeImpl();
/* 266 */           setConfiguration(configuration);
/* 267 */           setFilterInitialized(true);
/*     */           return;
/*     */         } 
/* 270 */         setRuntimeConfiguration(configuration);
/* 271 */         setFilterInitialized(true);
/*     */         return;
/* 273 */       } catch (IllegalStateException illegalStateException) {
/* 274 */         log.error("Unable to configure", illegalStateException);
/*     */       } 
/*     */     }
/* 277 */     setFilterInitialized(false);
/*     */   }
/*     */   
/*     */   private void setRuntimeConfiguration(Configuration paramConfiguration) {
/* 281 */     restartCRLChecker(paramConfiguration);
/* 282 */     setFilterInitialized(true);
/* 283 */     setConfiguration(paramConfiguration);
/* 284 */     if (!IdpMetadata.getInstance().enableDiscovery()) {
/* 285 */       log.info("Discovery profile disabled, only one metadata file found");
/*     */     }
/* 287 */     else if (paramConfiguration.getString("oiosaml-sp.discovery") == null) {
/* 288 */       throw new IllegalStateException("Discovery location cannot be null when discovery profile is active");
/*     */     } 
/*     */     
/* 291 */     setHostname();
/* 292 */     this.sessionHandlerFactory = SessionHandlerFactory.Factory.newInstance(paramConfiguration);
/* 293 */     this.sessionHandlerFactory.getHandler().resetReplayProtection(paramConfiguration.getInt("common.saml2.loggedinhandler.numusedassertionids"));
/*     */     
/* 295 */     BasicSecurityConfiguration basicSecurityConfiguration = (BasicSecurityConfiguration)Configuration.getGlobalSecurityConfiguration();
/* 296 */     basicSecurityConfiguration.registerSignatureAlgorithmURI("RSA", paramConfiguration.getString("oiosaml-sp.signature.algorithm", "http://www.w3.org/2001/04/xmldsig-more#rsa-sha256"));
/*     */     
/* 298 */     log.info("Home url: " + paramConfiguration.getString("oiosaml-sp.uri.home"));
/* 299 */     log.info("Assurance level: " + paramConfiguration.getInt("oiosaml-sp.assurancelevel"));
/* 300 */     log.info("SP entity ID: " + SPMetadata.getInstance().getEntityID());
/* 301 */     log.info("Base hostname: " + this.hostname);
/*     */   }
/*     */   
/*     */   private void setHostname() {
/* 305 */     String str = SPMetadata.getInstance().getDefaultAssertionConsumerService().getLocation();
/* 306 */     setHostname(str.substring(0, str.indexOf('/', 8)));
/*     */   }
/*     */   
/*     */   private void restartCRLChecker(Configuration paramConfiguration) {
/* 310 */     this.crlChecker.stopChecker();
/* 311 */     int i = paramConfiguration.getInt("oiosaml-sp.crl.period", 600);
/* 312 */     if (i > 0) {
/* 313 */       this.crlChecker.startChecker(i, IdpMetadata.getInstance(), paramConfiguration);
/*     */     } else {
/*     */       
/* 316 */       log.warn("Revocation check of IdP certificates has been disabled. All certificates including self signed certificates will be considered valid.");
/* 317 */       this.crlChecker.setAllCertificatesValid(IdpMetadata.getInstance());
/*     */     } 
/*     */   }
/*     */   
/*     */   public void setHostname(String paramString) {
/* 322 */     this.hostname = paramString;
/*     */   }
/*     */   
/*     */   public void setFilterInitialized(boolean paramBoolean) {
/* 326 */     this.filterInitialized = paramBoolean;
/*     */   }
/*     */   
/*     */   public boolean isFilterInitialized() {
/* 330 */     return this.filterInitialized;
/*     */   }
/*     */   
/*     */   public void setConfiguration(Configuration paramConfiguration) {
/* 334 */     SAMLConfigurationFactory.getConfiguration().setConfiguration(paramConfiguration);
/* 335 */     this.conf = SAMLConfigurationFactory.getConfiguration();
/*     */   }
/*     */   
/*     */   public void setSessionHandlerFactory(SessionHandlerFactory paramSessionHandlerFactory) {
/* 339 */     this.sessionHandlerFactory = paramSessionHandlerFactory;
/*     */   }
/*     */   
/*     */   public void setDevelMode(DevelMode paramDevelMode) {
/* 343 */     this.develMode = paramDevelMode;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/dk/itst/oiosaml/sp/service/SPFilter.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */