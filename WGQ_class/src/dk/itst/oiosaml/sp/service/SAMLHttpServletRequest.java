/*    */ package dk.itst.oiosaml.sp.service;
/*    */ 
/*    */ import dk.itst.oiosaml.sp.OIOPrincipal;
/*    */ import dk.itst.oiosaml.sp.UserAssertion;
/*    */ import java.security.Principal;
/*    */ import javax.servlet.http.HttpServletRequest;
/*    */ import javax.servlet.http.HttpServletRequestWrapper;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class SAMLHttpServletRequest
/*    */   extends HttpServletRequestWrapper
/*    */ {
/*    */   private final UserAssertion assertion;
/*    */   private final String hostname;
/*    */   private String relayState;
/*    */   
/*    */   public SAMLHttpServletRequest(HttpServletRequest paramHttpServletRequest, UserAssertion paramUserAssertion, String paramString) {
/* 42 */     super(paramHttpServletRequest);
/* 43 */     this.assertion = paramUserAssertion;
/* 44 */     this.hostname = paramString;
/*    */   }
/*    */   
/*    */   public SAMLHttpServletRequest(HttpServletRequest paramHttpServletRequest, String paramString1, String paramString2) {
/* 48 */     this(paramHttpServletRequest, (UserAssertion)null, paramString1);
/* 49 */     this.relayState = paramString2;
/*    */   }
/*    */ 
/*    */   
/*    */   public String getRemoteUser() {
/* 54 */     if (this.assertion != null) {
/* 55 */       return this.assertion.getSubject();
/*    */     }
/*    */     
/* 58 */     return super.getRemoteUser();
/*    */   }
/*    */ 
/*    */   
/*    */   public Principal getUserPrincipal() {
/* 63 */     if (this.assertion != null) {
/* 64 */       return (Principal)new OIOPrincipal(this.assertion);
/*    */     }
/*    */     
/* 67 */     return super.getUserPrincipal();
/*    */   }
/*    */ 
/*    */   
/*    */   public StringBuffer getRequestURL() {
/* 72 */     String str1 = super.getRequestURL().toString();
/*    */     
/* 74 */     String str2 = this.hostname + str1.substring(str1.indexOf('/', 8));
/* 75 */     return new StringBuffer(str2);
/*    */   }
/*    */ 
/*    */   
/*    */   public String getParameter(String paramString) {
/* 80 */     if ("RelayState".equals(paramString) && this.relayState != null) {
/* 81 */       return this.relayState;
/*    */     }
/* 83 */     return super.getParameter(paramString);
/*    */   }
/*    */ 
/*    */   
/*    */   public String getQueryString() {
/* 88 */     if (this.relayState == null) return super.getQueryString();
/*    */     
/* 90 */     String str = super.getQueryString();
/* 91 */     if (str == null) {
/* 92 */       str = "";
/*    */     }
/* 94 */     return str + "&" + "RelayState" + "=" + this.relayState;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/dk/itst/oiosaml/sp/service/SAMLHttpServletRequest.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */