/*     */ package dk.itst.oiosaml.sp;
/*     */ 
/*     */ import dk.itst.oiosaml.security.SecurityHelper;
/*     */ import dk.itst.oiosaml.sp.model.OIOAssertion;
/*     */ import dk.itst.oiosaml.sp.util.AttributeUtil;
/*     */ import java.io.Serializable;
/*     */ import java.security.cert.CertificateException;
/*     */ import java.security.cert.X509Certificate;
/*     */ import java.util.Collection;
/*     */ import java.util.Collections;
/*     */ import java.util.Date;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import org.joda.time.DateTime;
/*     */ import org.opensaml.saml2.core.Attribute;
/*     */ import org.opensaml.saml2.core.AttributeStatement;
/*     */ import org.opensaml.saml2.core.AuthnStatement;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class UserAssertionImpl
/*     */   implements UserAssertion, Serializable
/*     */ {
/*     */   private static final long serialVersionUID = -1756335950388129831L;
/*  46 */   private Map<String, UserAttribute> attributes = new HashMap<>();
/*     */   private Date issueTime;
/*     */   private String issuer;
/*     */   private Date sessionExpireTime;
/*     */   private NameIDFormat nameIDFormat;
/*     */   private String nameID;
/*     */   private boolean signed;
/*     */   private String xml;
/*     */   private String id;
/*     */   
/*     */   public UserAssertionImpl(OIOAssertion paramOIOAssertion) {
/*  57 */     for (AttributeStatement attributeStatement : paramOIOAssertion.getAssertion().getAttributeStatements()) {
/*  58 */       for (Attribute attribute : attributeStatement.getAttributes()) {
/*  59 */         if (this.attributes.containsKey(attribute.getName())) {
/*  60 */           UserAttribute userAttribute = this.attributes.get(attribute.getName());
/*  61 */           List list = AttributeUtil.extractAttributeValueValues(attribute);
/*  62 */           for (String str : list) {
/*  63 */             userAttribute.getValues().add(str);
/*     */           }
/*     */           continue;
/*     */         } 
/*  67 */         this.attributes.put(attribute.getName(), new UserAttribute(attribute.getName(), attribute.getFriendlyName(), AttributeUtil.extractAttributeValueValues(attribute), attribute.getNameFormat()));
/*     */       } 
/*     */     } 
/*     */     
/*  71 */     this.id = paramOIOAssertion.getID();
/*     */     
/*  73 */     if (paramOIOAssertion.getAssertion().getIssueInstant() != null) {
/*  74 */       this.issueTime = paramOIOAssertion.getAssertion().getIssueInstant().toDate();
/*     */     }
/*  76 */     if (paramOIOAssertion.getAssertion().getIssuer() != null) {
/*  77 */       this.issuer = paramOIOAssertion.getAssertion().getIssuer().getValue();
/*     */     }
/*  79 */     if (!paramOIOAssertion.getAssertion().getAuthnStatements().isEmpty()) {
/*  80 */       DateTime dateTime = ((AuthnStatement)paramOIOAssertion.getAssertion().getAuthnStatements().get(0)).getSessionNotOnOrAfter();
/*  81 */       if (dateTime != null) {
/*  82 */         this.sessionExpireTime = dateTime.toDate();
/*     */       }
/*     */     } 
/*  85 */     if (paramOIOAssertion.getAssertion().getSubject() != null) {
/*  86 */       this.nameIDFormat = NameIDFormat.getNameID(paramOIOAssertion.getAssertion().getSubject().getNameID().getFormat());
/*  87 */       this.nameID = paramOIOAssertion.getAssertion().getSubject().getNameID().getValue();
/*     */     } 
/*  89 */     this.signed = (paramOIOAssertion.getAssertion().getSignature() != null);
/*     */     try {
/*  91 */       this.xml = paramOIOAssertion.toXML();
/*  92 */     } catch (Exception exception) {}
/*     */   }
/*     */   
/*     */   public Collection<UserAttribute> getAllAttributes() {
/*  96 */     return Collections.unmodifiableCollection(this.attributes.values());
/*     */   }
/*     */   
/*     */   public int getAssuranceLevel() {
/* 100 */     String str = getAttributeValue("dk:gov:saml:attribute:AssuranceLevel");
/* 101 */     if (str == null)
/* 102 */       return 0; 
/* 103 */     if ("test".equals(str)) {
/* 104 */       return -1;
/*     */     }
/* 106 */     return Integer.valueOf(str).intValue();
/*     */   }
/*     */ 
/*     */   
/*     */   public UserAttribute getAttribute(String paramString) {
/* 111 */     return this.attributes.get(paramString);
/*     */   }
/*     */   
/*     */   public String getCVRNumberIdentifier() {
/* 115 */     return getAttributeValue("dk:gov:saml:attribute:CvrNumberIdentifier");
/*     */   }
/*     */   
/*     */   public String getCertificateSerialNumber() {
/* 119 */     return getAttributeValue("urn:oid:2.5.4.5");
/*     */   }
/*     */   
/*     */   public String getCommonName() {
/* 123 */     return getAttributeValue("urn:oid:2.5.4.3");
/*     */   }
/*     */   
/*     */   public Date getIssueTime() {
/* 127 */     return this.issueTime;
/*     */   }
/*     */   
/*     */   public String getIssuer() {
/* 131 */     return this.issuer;
/*     */   }
/*     */   
/*     */   public String getMail() {
/* 135 */     return getAttributeValue("urn:oid:0.9.2342.********.100.1.3");
/*     */   }
/*     */   
/*     */   public NameIDFormat getNameIDFormat() {
/* 139 */     return this.nameIDFormat;
/*     */   }
/*     */   
/*     */   public String getOrganizationName() {
/* 143 */     return getAttributeValue("urn:oid:********");
/*     */   }
/*     */   
/*     */   public String getOrganizationUnit() {
/* 147 */     return getAttributeValue("urn:oid:********");
/*     */   }
/*     */   
/*     */   public String getPostalAddress() {
/* 151 */     return getAttributeValue("urn:oid:********");
/*     */   }
/*     */   
/*     */   public Date getSessionExpireTime() {
/* 155 */     return this.sessionExpireTime;
/*     */   }
/*     */   
/*     */   public String getSpecificationVersion() {
/* 159 */     return getAttributeValue("dk:gov:saml:attribute:SpecVer");
/*     */   }
/*     */   
/*     */   public String getSubject() {
/* 163 */     return this.nameID;
/*     */   }
/*     */   
/*     */   public String getSurname() {
/* 167 */     return getAttributeValue("urn:oid:*******");
/*     */   }
/*     */   
/*     */   public String getTitle() {
/* 171 */     return getAttributeValue("urn:oid:********");
/*     */   }
/*     */   
/*     */   public String getUniqueAccountKey() {
/* 175 */     return getAttributeValue("dk:gov:saml:attribute:UniqueAccountKey");
/*     */   }
/*     */   
/*     */   public String getUserId() {
/* 179 */     return getAttributeValue("urn:oid:0.9.2342.********.100.1.1");
/*     */   }
/*     */   
/*     */   public String getXML() {
/* 183 */     return this.xml;
/*     */   }
/*     */   
/*     */   public boolean isSigned() {
/* 187 */     return this.signed;
/*     */   }
/*     */   
/*     */   private String getAttributeValue(String paramString) {
/* 191 */     UserAttribute userAttribute = this.attributes.get(paramString);
/* 192 */     if (userAttribute != null) {
/* 193 */       List<String> list = userAttribute.getValues();
/* 194 */       if (list.size() > 0) {
/* 195 */         return list.get(0);
/*     */       }
/*     */     } 
/* 198 */     return null;
/*     */   }
/*     */   
/*     */   public String getCPRNumber() {
/* 202 */     return getAttributeValue("dk:gov:saml:attribute:CprNumberIdentifier");
/*     */   }
/*     */   
/*     */   public String getRIDNumber() {
/* 206 */     return getAttributeValue("dk:gov:saml:attribute:RidNumberIdentifier");
/*     */   }
/*     */   
/*     */   public String getPIDNumber() {
/* 210 */     return getAttributeValue("dk:gov:saml:attribute:PidNumberIdentifier");
/*     */   }
/*     */   
/*     */   public String getPseudonym() {
/* 214 */     return getAttributeValue("urn:oid:2.5.4.65");
/*     */   }
/*     */   
/*     */   public X509Certificate getUserCertificate() {
/* 218 */     String str = getAttributeValue("urn:oid:1.3.6.1.4.1.1466.115.121.1.8");
/* 219 */     if (str == null) return null;
/*     */     
/*     */     try {
/* 222 */       return SecurityHelper.buildJavaX509Cert(str);
/* 223 */     } catch (CertificateException certificateException) {
/* 224 */       throw new RuntimeException(certificateException);
/*     */     } 
/*     */   }
/*     */   
/*     */   public boolean isOCESProfileCompliant() {
/* 229 */     boolean bool = isOIOSAMLCompliant(); try {
/*     */       boolean bool1;
/* 231 */       bool &= NameIDFormat.X509SUBJECT.equals(getNameIDFormat());
/* 232 */       int i = bool & ((getCertificateSerialNumber() != null) ? 1 : 0);
/* 233 */       i &= (isYouthCertificate() != null) ? 1 : 0;
/* 234 */       i &= ((getPIDNumber() != null) ? 1 : 0) ^ ((getRIDNumber() != null) ? 1 : 0);
/*     */       
/* 236 */       if (getPIDNumber() != null) {
/* 237 */         bool1 = i & ("PID:" + getPIDNumber()).equals(getUserId());
/* 238 */       } else if (getRIDNumber() != null) {
/* 239 */         int j = bool1 & ((getCVRNumberIdentifier() != null) ? 1 : 0);
/* 240 */         bool1 = j & ("CVR:" + getCVRNumberIdentifier() + "-RID:" + getRIDNumber()).equals(getUserId());
/*     */       } 
/* 242 */       return bool1;
/* 243 */     } catch (RuntimeException runtimeException) {
/* 244 */       return false;
/*     */     } 
/*     */   }
/*     */   
/*     */   public boolean isOIOSAMLCompliant() {
/* 249 */     boolean bool = true;
/* 250 */     bool &= "DK-SAML-2.0".equals(getSpecificationVersion());
/* 251 */     int i = bool & ((getAssuranceLevel() > 0) ? 1 : 0);
/* 252 */     i &= (getSurname() != null) ? 1 : 0;
/* 253 */     i &= (getCommonName() != null) ? 1 : 0;
/* 254 */     i &= (getUserId() != null) ? 1 : 0;
/* 255 */     i &= (getMail() != null) ? 1 : 0;
/* 256 */     return i;
/*     */   }
/*     */   
/*     */   public boolean isPersistentPseudonymProfileCompliant() {
/* 260 */     boolean bool2 = true;
/* 261 */     bool2 &= "DK-SAML-2.0".equals(getSpecificationVersion());
/* 262 */     int j = bool2 & ((getAssuranceLevel() > 0) ? 1 : 0);
/* 263 */     j &= (getUserId() == null) ? 1 : 0;
/* 264 */     boolean bool1 = j & NameIDFormat.PERSISTENT.equals(getNameIDFormat());
/* 265 */     int i = bool1 & ((getPIDNumber() == null) ? 1 : 0);
/* 266 */     i &= (getRIDNumber() == null) ? 1 : 0;
/* 267 */     i &= (getCertificateSerialNumber() == null) ? 1 : 0;
/* 268 */     i &= (getMail() == null) ? 1 : 0;
/* 269 */     i &= (getSurname() == null) ? 1 : 0;
/* 270 */     i &= (getCommonName() == null) ? 1 : 0;
/*     */     
/* 272 */     return i;
/*     */   }
/*     */   
/*     */   public Boolean isYouthCertificate() {
/* 276 */     String str = getAttributeValue("dk:gov:saml:attribute:IsYouthCert");
/* 277 */     if (str == null) return null;
/*     */     
/* 279 */     return Boolean.valueOf(str);
/*     */   }
/*     */   
/*     */   public String getAssertionId() {
/* 283 */     return this.id;
/*     */   }
/*     */   
/*     */   public boolean isAuthenticated() {
/* 287 */     return true;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/dk/itst/oiosaml/sp/UserAssertionImpl.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */