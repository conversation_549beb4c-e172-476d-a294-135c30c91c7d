package dk.itst.oiosaml.sp;

import java.security.cert.X509Certificate;
import java.util.Collection;
import java.util.Date;

public interface UserAssertion {
  boolean isOIOSAMLCompliant();
  
  boolean isOCESProfileCompliant();
  
  boolean isPersistentPseudonymProfileCompliant();
  
  String getXML();
  
  Date getSessionExpireTime();
  
  Date getIssueTime();
  
  String getIssuer();
  
  boolean isSigned();
  
  String getSubject();
  
  NameIDFormat getNameIDFormat();
  
  String getSurname();
  
  String getCommonName();
  
  String getUserId();
  
  String getMail();
  
  int getAssuranceLevel();
  
  String getSpecificationVersion();
  
  String getUniqueAccountKey();
  
  String getCVRNumberIdentifier();
  
  UserAttribute getAttribute(String paramString);
  
  Collection<UserAttribute> getAllAttributes();
  
  String getCertificateSerialNumber();
  
  String getOrganizationName();
  
  String getOrganizationUnit();
  
  String getTitle();
  
  String getPostalAddress();
  
  String getPseudonym();
  
  Boolean isYouthCertificate();
  
  X509Certificate getUserCertificate();
  
  String getPIDNumber();
  
  String getCPRNumber();
  
  String getRIDNumber();
  
  String getAssertionId();
  
  boolean isAuthenticated();
}


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/dk/itst/oiosaml/sp/UserAssertion.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */