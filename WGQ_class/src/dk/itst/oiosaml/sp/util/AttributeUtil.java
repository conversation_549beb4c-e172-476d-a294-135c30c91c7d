/*     */ package dk.itst.oiosaml.sp.util;
/*     */ 
/*     */ import dk.itst.oiosaml.common.OIOSAMLConstants;
/*     */ import dk.itst.oiosaml.common.SAMLUtil;
/*     */ import java.util.ArrayList;
/*     */ import java.util.List;
/*     */ import javax.xml.namespace.QName;
/*     */ import org.opensaml.saml2.core.Attribute;
/*     */ import org.opensaml.saml2.core.impl.AttributeBuilder;
/*     */ import org.opensaml.xml.Namespace;
/*     */ import org.opensaml.xml.XMLObject;
/*     */ import org.opensaml.xml.parse.BasicParserPool;
/*     */ import org.opensaml.xml.schema.XSAny;
/*     */ import org.opensaml.xml.schema.XSString;
/*     */ import org.opensaml.xml.schema.impl.XSAnyBuilder;
/*     */ import org.opensaml.xml.util.XMLHelper;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class AttributeUtil
/*     */   implements OIOSAMLConstants
/*     */ {
/*     */   public static final String VERSION = "$Id: AttributeUtil.java 2950 2008-05-28 08:22:34Z jre $";
/*  57 */   protected static BasicParserPool parser = new BasicParserPool();
/*     */ 
/*     */   
/*  60 */   public static final QName XSI_TYPE_ATTRIBUTE_NAME = new QName("http://www.w3.org/2001/XMLSchema-instance", "type", "xsi");
/*     */ 
/*     */ 
/*     */   
/*     */   public static final String XS_STRING = "xs:string";
/*     */ 
/*     */ 
/*     */   
/*     */   public static Attribute createAttribute(String paramString1, String paramString2, String paramString3) {
/*  69 */     Attribute attribute = (new AttributeBuilder()).buildObject();
/*  70 */     attribute.setName(paramString1);
/*  71 */     attribute.setFriendlyName(paramString2);
/*  72 */     attribute.setNameFormat(paramString3);
/*  73 */     return attribute;
/*     */   }
/*     */   
/*     */   private static XSAny createAttributeValue() {
/*  77 */     XSAnyBuilder xSAnyBuilder = new XSAnyBuilder();
/*  78 */     return xSAnyBuilder.buildObject("urn:oasis:names:tc:SAML:2.0:assertion", "AttributeValue", "saml2");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static XSAny createAttributeValue(String paramString1, String paramString2) {
/*  86 */     XSAny xSAny = createAttributeValue();
/*  87 */     xSAny.setTextContent(String.valueOf(paramString1));
/*  88 */     xSAny.getUnknownAttributes().put(XSI_TYPE_ATTRIBUTE_NAME, paramString2);
/*     */     
/*  90 */     xSAny.addNamespace(new Namespace("http://www.w3.org/2001/XMLSchema-instance", "xsi"));
/*  91 */     return xSAny;
/*     */   }
/*     */   
/*     */   public static XSAny createAttributeValue(String paramString) {
/*  95 */     return createAttributeValue(paramString, "xs:string");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static Attribute createSurname(String paramString) {
/* 107 */     Attribute attribute = createAttribute("urn:oid:2.5.4.4", "surName", "urn:oasis:names:tc:SAML:2.0:attrname-format:basic");
/*     */     
/* 109 */     if (paramString != null) {
/* 110 */       attribute.getAttributeValues().add(createAttributeValue(paramString));
/*     */     }
/* 112 */     return attribute;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static Attribute createCommonName(String paramString) {
/* 124 */     Attribute attribute = createAttribute("urn:oid:2.5.4.3", "CommonName", "urn:oasis:names:tc:SAML:2.0:attrname-format:basic");
/*     */     
/* 126 */     if (paramString != null) {
/* 127 */       attribute.getAttributeValues().add(createAttributeValue(paramString));
/*     */     }
/* 129 */     return attribute;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static Attribute createUid(String paramString) {
/* 141 */     Attribute attribute = createAttribute("urn:oid:0.9.2342.19200300.100.1.1", "uid", "urn:oasis:names:tc:SAML:2.0:attrname-format:basic");
/*     */     
/* 143 */     if (paramString != null) {
/* 144 */       attribute.getAttributeValues().add(createAttributeValue(paramString));
/*     */     }
/* 146 */     return attribute;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static Attribute createMail(String paramString) {
/* 158 */     Attribute attribute = createAttribute("urn:oid:0.9.2342.19200300.100.1.3", "mail", "urn:oasis:names:tc:SAML:2.0:attrname-format:basic");
/*     */     
/* 160 */     if (paramString != null) {
/* 161 */       attribute.getAttributeValues().add(createAttributeValue(paramString));
/*     */     }
/* 163 */     return attribute;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static Attribute createCVRNumberIdentifier(String paramString) {
/* 175 */     Attribute attribute = createAttribute("dk:gov:saml:attribute:CvrNumberIdentifier", "CVRnumberIdentifier", "urn:oasis:names:tc:SAML:2.0:attrname-format:basic");
/*     */ 
/*     */ 
/*     */     
/* 179 */     if (paramString != null) {
/* 180 */       attribute.getAttributeValues().add(createAttributeValue(paramString));
/*     */     }
/* 182 */     return attribute;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static Attribute createSerialNumber(String paramString) {
/* 194 */     Attribute attribute = createAttribute("urn:oid:2.5.4.5", "serialNumber", "urn:oasis:names:tc:SAML:2.0:attrname-format:basic");
/*     */ 
/*     */     
/* 197 */     if (paramString != null) {
/* 198 */       attribute.getAttributeValues().add(createAttributeValue(paramString));
/*     */     }
/* 200 */     return attribute;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static Attribute createPidNumberIdentifier(String paramString) {
/* 212 */     Attribute attribute = createAttribute("dk:gov:saml:attribute:PidNumberIdentifier", "PidNumberIdentifier", "urn:oasis:names:tc:SAML:2.0:attrname-format:basic");
/*     */ 
/*     */ 
/*     */     
/* 216 */     if (paramString != null) {
/* 217 */       attribute.getAttributeValues().add(createAttributeValue(paramString));
/*     */     }
/* 219 */     return attribute;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static Attribute createRidNumberIdentifier(String paramString) {
/* 231 */     Attribute attribute = createAttribute("dk:gov:saml:attribute:RidNumberIdentifier", "RidNumberIdentifier", "urn:oasis:names:tc:SAML:2.0:attrname-format:basic");
/*     */ 
/*     */ 
/*     */     
/* 235 */     if (paramString != null) {
/* 236 */       attribute.getAttributeValues().add(createAttributeValue(paramString));
/*     */     }
/* 238 */     return attribute;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static Attribute createUserCertificate(String paramString) {
/* 250 */     Attribute attribute = createAttribute("urn:oid:1.3.6.1.4.1.1466.115.121.1.8", "userCertificate", "urn:oasis:names:tc:SAML:2.0:attrname-format:basic");
/*     */ 
/*     */     
/* 253 */     if (paramString != null) {
/* 254 */       attribute.getAttributeValues().add(createAttributeValue(paramString));
/*     */     }
/* 256 */     return attribute;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static Attribute createAssuranceLevel(int paramInt) {
/* 268 */     Attribute attribute = createAttribute("dk:gov:saml:attribute:AssuranceLevel", "AssuranceLevel", "urn:oasis:names:tc:SAML:2.0:attrname-format:basic");
/*     */ 
/*     */     
/* 271 */     if (paramInt != 0) {
/* 272 */       attribute.getAttributeValues().add(
/* 273 */           createAttributeValue(String.valueOf(paramInt)));
/*     */     }
/* 275 */     return attribute;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String extractAttributeValueValue(Attribute paramAttribute) {
/* 286 */     for (byte b = 0; b < paramAttribute.getAttributeValues().size(); b++) {
/* 287 */       if (paramAttribute.getAttributeValues().get(b) instanceof XSString) {
/* 288 */         XSString xSString = paramAttribute.getAttributeValues().get(b);
/* 289 */         if ("AttributeValue".equals(xSString.getElementQName().getLocalPart()) && "urn:oasis:names:tc:SAML:2.0:assertion"
/* 290 */           .equals(xSString.getElementQName().getNamespaceURI())) {
/* 291 */           return xSString.getValue();
/*     */         }
/*     */       } else {
/* 294 */         XSAny xSAny = paramAttribute.getAttributeValues().get(b);
/* 295 */         if ("AttributeValue".equals(xSAny.getElementQName().getLocalPart()) && "urn:oasis:names:tc:SAML:2.0:assertion"
/* 296 */           .equals(xSAny.getElementQName().getNamespaceURI())) {
/* 297 */           if (xSAny.getUnknownXMLObjects().size() > 0) {
/* 298 */             StringBuilder stringBuilder = new StringBuilder();
/* 299 */             for (XMLObject xMLObject : xSAny.getUnknownXMLObjects()) {
/* 300 */               stringBuilder.append(XMLHelper.nodeToString(SAMLUtil.marshallObject(xMLObject)));
/*     */             }
/* 302 */             return stringBuilder.toString();
/*     */           } 
/* 304 */           return xSAny.getTextContent();
/*     */         } 
/*     */       } 
/*     */     } 
/* 308 */     return null;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static List<String> extractAttributeValueValues(Attribute paramAttribute) {
/* 318 */     ArrayList<String> arrayList = new ArrayList();
/* 319 */     for (byte b = 0; b < paramAttribute.getAttributeValues().size(); b++) {
/* 320 */       if (paramAttribute.getAttributeValues().get(b) instanceof XSString) {
/* 321 */         XSString xSString = paramAttribute.getAttributeValues().get(b);
/* 322 */         if ("AttributeValue".equals(xSString.getElementQName().getLocalPart()) && "urn:oasis:names:tc:SAML:2.0:assertion"
/* 323 */           .equals(xSString.getElementQName().getNamespaceURI())) {
/* 324 */           arrayList.add(xSString.getValue());
/*     */         }
/*     */       } else {
/* 327 */         XSAny xSAny = paramAttribute.getAttributeValues().get(b);
/* 328 */         if ("AttributeValue".equals(xSAny.getElementQName().getLocalPart()) && "urn:oasis:names:tc:SAML:2.0:assertion"
/* 329 */           .equals(xSAny.getElementQName().getNamespaceURI())) {
/* 330 */           if (xSAny.getUnknownXMLObjects().size() > 0) {
/* 331 */             StringBuilder stringBuilder = new StringBuilder();
/* 332 */             for (XMLObject xMLObject : xSAny.getUnknownXMLObjects()) {
/* 333 */               stringBuilder.append(XMLHelper.nodeToString(SAMLUtil.marshallObject(xMLObject)));
/*     */             }
/* 335 */             arrayList.add(stringBuilder.toString());
/*     */           } 
/* 337 */           arrayList.add(xSAny.getTextContent());
/*     */         } 
/*     */       } 
/*     */     } 
/* 341 */     return arrayList;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/dk/itst/oiosaml/sp/util/AttributeUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */