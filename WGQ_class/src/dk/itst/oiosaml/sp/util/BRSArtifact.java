/*     */ package dk.itst.oiosaml.sp.util;
/*     */ 
/*     */ import dk.itst.oiosaml.error.Layer;
/*     */ import dk.itst.oiosaml.error.WrappedException;
/*     */ import java.io.UnsupportedEncodingException;
/*     */ import java.security.MessageDigest;
/*     */ import java.security.NoSuchAlgorithmException;
/*     */ import java.security.SecureRandom;
/*     */ import java.util.Arrays;
/*     */ import org.opensaml.common.binding.BindingException;
/*     */ import org.opensaml.saml2.binding.artifact.AbstractSAML2Artifact;
/*     */ import org.opensaml.saml2.binding.artifact.SAML2ArtifactBuilderFactory;
/*     */ import org.opensaml.saml2.binding.artifact.SAML2ArtifactType0004;
/*     */ import org.opensaml.xml.util.Base64;
/*     */ import org.opensaml.xml.util.DatatypeHelper;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class BRSArtifact
/*     */ {
/*     */   public static final String VERSION = "$Id: BRSArtifact.java 2829 2008-05-13 12:11:31Z jre $";
/*  50 */   private static SAML2ArtifactBuilderFactory artifactFactory = new SAML2ArtifactBuilderFactory();
/*     */   
/*  52 */   private SAML2ArtifactType0004 samlArtifact = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private final String entityId;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public BRSArtifact(String paramString, String... paramVarArgs) throws BindingException {
/*  67 */     this.samlArtifact = decodeArtifact(paramString);
/*  68 */     this.entityId = validate(paramVarArgs);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static SAML2ArtifactType0004 decodeArtifact(String paramString) throws BindingException {
/*  78 */     byte[] arrayOfByte = Base64.decode(paramString);
/*  79 */     AbstractSAML2Artifact abstractSAML2Artifact = artifactFactory.buildArtifact(arrayOfByte);
/*  80 */     if (abstractSAML2Artifact instanceof SAML2ArtifactType0004) {
/*  81 */       return (SAML2ArtifactType0004)abstractSAML2Artifact;
/*     */     }
/*     */     
/*  84 */     throw new BindingException("The artifact is not of the expected type: SAML2ArtifactType004");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String validate(String... paramVarArgs) throws BindingException {
/*  96 */     byte[] arrayOfByte = this.samlArtifact.getSourceID();
/*     */     try {
/*  98 */       for (String str : paramVarArgs) {
/*  99 */         MessageDigest messageDigest = MessageDigest.getInstance("SHA-1");
/* 100 */         byte[] arrayOfByte1 = messageDigest.digest(str.getBytes("UTF-8"));
/*     */         
/* 102 */         if (Arrays.equals(arrayOfByte1, arrayOfByte))
/*     */         {
/* 104 */           return str;
/*     */         }
/*     */       } 
/*     */       
/* 108 */       throw new BindingException("The sourceID:" + new String(arrayOfByte) + " does not match the expected sourceId");
/* 109 */     } catch (NoSuchAlgorithmException noSuchAlgorithmException) {
/* 110 */       throw new WrappedException(Layer.DATAACCESS, noSuchAlgorithmException);
/* 111 */     } catch (UnsupportedEncodingException unsupportedEncodingException) {
/* 112 */       throw new WrappedException(Layer.DATAACCESS, unsupportedEncodingException);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getEndpointIndex() {
/* 123 */     byte[] arrayOfByte = this.samlArtifact.getEndpointIndex();
/*     */     
/* 125 */     int i = 0;
/* 126 */     for (byte b = 0; b < arrayOfByte.length; b++) {
/* 127 */       int j = (arrayOfByte.length - 1 - b) * 8;
/* 128 */       i += (arrayOfByte[b] & 0xFF) << j;
/*     */     } 
/* 130 */     return i;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static SAML2ArtifactType0004 buildArtifact(int paramInt, String paramString) {
/*     */     try {
/* 141 */       byte[] arrayOfByte1 = DatatypeHelper.intToByteArray(paramInt);
/* 142 */       byte[] arrayOfByte2 = new byte[2];
/* 143 */       arrayOfByte2[0] = arrayOfByte1[2];
/* 144 */       arrayOfByte2[1] = arrayOfByte1[3];
/*     */       
/* 146 */       MessageDigest messageDigest = MessageDigest.getInstance("SHA-1");
/* 147 */       byte[] arrayOfByte3 = messageDigest.digest(paramString.getBytes());
/*     */       
/* 149 */       SecureRandom secureRandom = SecureRandom.getInstance("SHA1PRNG");
/*     */       
/* 151 */       byte[] arrayOfByte4 = new byte[20];
/* 152 */       secureRandom.nextBytes(arrayOfByte4);
/*     */       
/* 154 */       return new BRSArtifactType0004(arrayOfByte2, arrayOfByte3, arrayOfByte4);
/* 155 */     } catch (NoSuchAlgorithmException noSuchAlgorithmException) {
/* 156 */       throw new InternalError("JVM does not support required cryptography algorithms: SHA-1/SHA1PRNG.");
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getEntityId() {
/* 164 */     return this.entityId;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/dk/itst/oiosaml/sp/util/BRSArtifact.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */