/*    */ package dk.itst.oiosaml.sp.util;
/*    */ 
/*    */ import java.util.Collections;
/*    */ import java.util.List;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class LogoutRequestValidationException
/*    */   extends Exception
/*    */ {
/*    */   private static final long serialVersionUID = -1726459502073878835L;
/*    */   private final List<String> errors;
/*    */   
/*    */   public LogoutRequestValidationException(List<String> paramList) {
/* 35 */     super("Validation errors: " + paramList);
/* 36 */     this.errors = paramList;
/*    */   }
/*    */   
/*    */   public List<String> getErrors() {
/* 40 */     return Collections.unmodifiableList(this.errors);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/dk/itst/oiosaml/sp/util/LogoutRequestValidationException.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */