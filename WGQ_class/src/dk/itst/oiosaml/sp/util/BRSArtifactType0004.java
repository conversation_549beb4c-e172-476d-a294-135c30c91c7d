/*    */ package dk.itst.oiosaml.sp.util;
/*    */ 
/*    */ import org.opensaml.saml2.binding.artifact.SAML2ArtifactType0004;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class BRSArtifactType0004
/*    */   extends SAML2ArtifactType0004
/*    */ {
/*    */   public static final String VERSION = "$Id: BRSArtifactType0004.java 2829 2008-05-13 12:11:31Z jre $";
/*    */   
/*    */   public BRSArtifactType0004(byte[] paramArrayOfbyte1, byte[] paramArrayOfbyte2, byte[] paramArrayOfbyte3) {
/* 43 */     super(paramArrayOfbyte1, paramArrayOfbyte2, paramArrayOfbyte3);
/*    */     
/* 45 */     setTypeCode(SAML2ArtifactType0004.TYPE_CODE);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/dk/itst/oiosaml/sp/util/BRSArtifactType0004.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */