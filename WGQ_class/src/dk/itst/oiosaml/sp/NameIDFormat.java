/*    */ package dk.itst.oiosaml.sp;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public enum NameIDFormat
/*    */ {
/* 33 */   UNSPECIFIED("urn:oasis:names:tc:SAML:1.1:nameid-format:unspecified"),
/* 34 */   EMAIL("urn:oasis:names:tc:SAML:1.1:nameid-format:emailAddress"),
/* 35 */   X509SUBJECT("urn:oasis:names:tc:SAML:1.1:nameid-format:X509SubjectName"),
/* 36 */   WINDOWS_DOMAIN("urn:oasis:names:tc:SAML:1.1:nameid-format:WindowsDomainQualifiedName"),
/* 37 */   KERBEROS_PRINCIPAL("urn:oasis:names:tc:SAML:2.0:nameid-format:kerberos"),
/* 38 */   ENTITY("urn:oasis:names:tc:SAML:2.0:nameid-format:entity"),
/* 39 */   PERSISTENT("urn:oasis:names:tc:SAML:2.0:nameid-format:persistent"),
/* 40 */   TRANSIENT("urn:oasis:names:tc:SAML:2.0:nameid-format:transient");
/*    */   
/*    */   private final String format;
/*    */   
/*    */   NameIDFormat(String paramString1) {
/* 45 */     this.format = paramString1;
/*    */   }
/*    */ 
/*    */   
/*    */   public String toString() {
/* 50 */     return super.toString() + ": " + this.format;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public static NameIDFormat getNameID(String paramString) {
/* 60 */     for (NameIDFormat nameIDFormat : values()) {
/* 61 */       if (nameIDFormat.format.equals(paramString)) {
/* 62 */         return nameIDFormat;
/*    */       }
/*    */     } 
/* 65 */     throw new IllegalArgumentException("Format " + paramString + " unknown");
/*    */   }
/*    */   
/*    */   public String getFormat() {
/* 69 */     return this.format;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/dk/itst/oiosaml/sp/NameIDFormat.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */