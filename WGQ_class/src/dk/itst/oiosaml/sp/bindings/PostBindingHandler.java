/*     */ package dk.itst.oiosaml.sp.bindings;
/*     */ 
/*     */ import dk.itst.oiosaml.configuration.SAMLConfigurationFactory;
/*     */ import dk.itst.oiosaml.logging.Audit;
/*     */ import dk.itst.oiosaml.logging.Logger;
/*     */ import dk.itst.oiosaml.logging.LoggerFactory;
/*     */ import dk.itst.oiosaml.logging.Operation;
/*     */ import dk.itst.oiosaml.sp.model.OIOAuthnRequest;
/*     */ import java.io.IOException;
/*     */ import javax.servlet.RequestDispatcher;
/*     */ import javax.servlet.ServletException;
/*     */ import javax.servlet.ServletRequest;
/*     */ import javax.servlet.ServletResponse;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import org.opensaml.xml.security.credential.Credential;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class PostBindingHandler
/*     */   implements BindingHandler
/*     */ {
/*  68 */   private static final Logger log = LoggerFactory.getLogger(PostBindingHandler.class);
/*     */   
/*     */   private String dispatchPath;
/*     */   
/*     */   public PostBindingHandler() {
/*  73 */     this.dispatchPath = SAMLConfigurationFactory.getConfiguration().getSystemConfiguration().getString("POSTDispatchPath", null);
/*     */   }
/*     */   
/*     */   public PostBindingHandler(String paramString) {
/*  77 */     this.dispatchPath = paramString;
/*     */   }
/*     */   
/*     */   public String getBindingURI() {
/*  81 */     return "urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST";
/*     */   }
/*     */   
/*     */   public void handle(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse, Credential paramCredential, OIOAuthnRequest paramOIOAuthnRequest) throws IOException, ServletException {
/*  85 */     paramOIOAuthnRequest.sign(paramCredential);
/*  86 */     String str = paramOIOAuthnRequest.toBase64();
/*     */     
/*  88 */     paramHttpServletRequest.setAttribute("action", paramOIOAuthnRequest.getDestination());
/*  89 */     if (paramOIOAuthnRequest.getRelayState() != null) {
/*  90 */       paramHttpServletRequest.setAttribute("RelayState", paramOIOAuthnRequest.getRelayState());
/*     */     }
/*  92 */     paramHttpServletRequest.setAttribute("SAMLRequest", str);
/*  93 */     RequestDispatcher requestDispatcher = paramHttpServletRequest.getRequestDispatcher(this.dispatchPath);
/*  94 */     if (requestDispatcher == null) {
/*  95 */       log.error("No request dispatcher found for path: " + this.dispatchPath);
/*  96 */       throw new RuntimeException("No request dispatcher found for path: " + this.dispatchPath);
/*     */     } 
/*  98 */     log.debug("Dispatching request to: " + this.dispatchPath);
/*     */     
/* 100 */     Audit.log(Operation.AUTHNREQUEST_POST, true, paramOIOAuthnRequest.getID(), str);
/*     */     
/* 102 */     requestDispatcher.forward((ServletRequest)paramHttpServletRequest, (ServletResponse)paramHttpServletResponse);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/dk/itst/oiosaml/sp/bindings/PostBindingHandler.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */