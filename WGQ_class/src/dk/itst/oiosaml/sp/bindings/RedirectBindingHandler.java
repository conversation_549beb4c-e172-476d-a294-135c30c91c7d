/*    */ package dk.itst.oiosaml.sp.bindings;
/*    */ 
/*    */ import dk.itst.oiosaml.logging.Audit;
/*    */ import dk.itst.oiosaml.logging.Logger;
/*    */ import dk.itst.oiosaml.logging.LoggerFactory;
/*    */ import dk.itst.oiosaml.logging.Operation;
/*    */ import dk.itst.oiosaml.sp.model.OIOAuthnRequest;
/*    */ import dk.itst.oiosaml.sp.service.util.HTTPUtils;
/*    */ import java.io.IOException;
/*    */ import javax.servlet.ServletException;
/*    */ import javax.servlet.http.HttpServletRequest;
/*    */ import javax.servlet.http.HttpServletResponse;
/*    */ import org.opensaml.xml.security.credential.Credential;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class RedirectBindingHandler
/*    */   implements BindingHandler
/*    */ {
/* 43 */   private static final Logger log = LoggerFactory.getLogger(RedirectBindingHandler.class);
/*    */ 
/*    */   
/*    */   public String getBindingURI() {
/* 47 */     return "urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Redirect";
/*    */   }
/*    */   
/*    */   public void handle(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse, Credential paramCredential, OIOAuthnRequest paramOIOAuthnRequest) throws IOException, ServletException {
/* 51 */     String str = paramOIOAuthnRequest.getRedirectURL(paramCredential);
/* 52 */     log.debug("Issuing redirect to " + str);
/*    */     
/* 54 */     Audit.log(Operation.AUTHNREQUEST_REDIRECT, true, paramOIOAuthnRequest.getID(), str);
/* 55 */     HTTPUtils.sendMetaRedirect(paramHttpServletResponse, str, null, (HTTPUtils.getFragmentCookie(paramHttpServletRequest) == null));
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/dk/itst/oiosaml/sp/bindings/RedirectBindingHandler.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */