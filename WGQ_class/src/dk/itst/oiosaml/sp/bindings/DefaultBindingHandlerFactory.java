/*    */ package dk.itst.oiosaml.sp.bindings;
/*    */ 
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class DefaultBindingHandlerFactory
/*    */   implements BindingHandlerFactory
/*    */ {
/* 32 */   private static final Map<String, BindingHandler> handlers = new HashMap<String, BindingHandler>()
/*    */     {
/*    */       private static final long serialVersionUID = 469249093583103484L;
/*    */     };
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public BindingHandler getBindingHandler(String paramString) throws IllegalArgumentException {
/* 42 */     BindingHandler bindingHandler = handlers.get(paramString);
/* 43 */     if (bindingHandler == null) {
/* 44 */       throw new IllegalArgumentException(paramString);
/*    */     }
/*    */     
/* 47 */     return bindingHandler;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/dk/itst/oiosaml/sp/bindings/DefaultBindingHandlerFactory.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */