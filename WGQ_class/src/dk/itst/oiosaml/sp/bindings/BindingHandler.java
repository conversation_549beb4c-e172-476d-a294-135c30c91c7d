package dk.itst.oiosaml.sp.bindings;

import dk.itst.oiosaml.sp.model.OIOAuthnRequest;
import java.io.IOException;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.opensaml.xml.security.credential.Credential;

public interface BindingHandler {
  String getBindingURI();
  
  void handle(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse, Credential paramCredential, OIOAuthnRequest paramOIOAuthnRequest) throws IOException, ServletException;
}


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/dk/itst/oiosaml/sp/bindings/BindingHandler.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */