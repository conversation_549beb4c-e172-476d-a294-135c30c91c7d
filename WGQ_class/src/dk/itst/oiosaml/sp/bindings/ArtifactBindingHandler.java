/*    */ package dk.itst.oiosaml.sp.bindings;
/*    */ 
/*    */ import dk.itst.oiosaml.logging.Audit;
/*    */ import dk.itst.oiosaml.logging.Logger;
/*    */ import dk.itst.oiosaml.logging.LoggerFactory;
/*    */ import dk.itst.oiosaml.logging.Operation;
/*    */ import dk.itst.oiosaml.sp.model.OIOAuthnRequest;
/*    */ import java.io.IOException;
/*    */ import javax.servlet.ServletException;
/*    */ import javax.servlet.http.HttpServletRequest;
/*    */ import javax.servlet.http.HttpServletResponse;
/*    */ import org.opensaml.xml.security.credential.Credential;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class ArtifactBindingHandler
/*    */   implements BindingHandler
/*    */ {
/* 50 */   private static final Logger log = LoggerFactory.getLogger(ArtifactBindingHandler.class);
/*    */   public static final String VERSION = "$Id: ClientSSOEngine.java 2546 2008-04-11 13:29:25Z jre $";
/*    */   
/*    */   public String getBindingURI() {
/* 54 */     return "urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Artifact";
/*    */   }
/*    */   
/*    */   public void handle(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse, Credential paramCredential, OIOAuthnRequest paramOIOAuthnRequest) throws IOException, ServletException {
/* 58 */     String str = paramOIOAuthnRequest.getRedirectURL(paramCredential);
/*    */     
/* 60 */     if (log.isDebugEnabled())
/* 61 */       log.debug("redirectURL...:" + str); 
/* 62 */     Audit.log(Operation.AUTHNREQUEST_REDIRECT_ARTIFACT, true, paramOIOAuthnRequest.getID(), str);
/*    */     
/* 64 */     paramHttpServletResponse.sendRedirect(str);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/dk/itst/oiosaml/sp/bindings/ArtifactBindingHandler.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */