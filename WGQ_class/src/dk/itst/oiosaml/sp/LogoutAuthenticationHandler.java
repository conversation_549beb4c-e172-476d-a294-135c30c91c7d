package dk.itst.oiosaml.sp;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

public interface LogoutAuthenticationHandler extends AuthenticationHandler {
  void userLoggedOut(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse);
}


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/dk/itst/oiosaml/sp/LogoutAuthenticationHandler.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */