/*    */ package dk.itst.oiosaml.sp;
/*    */ 
/*    */ import java.security.Principal;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class OIOPrincipal
/*    */   implements Principal
/*    */ {
/*    */   private final UserAssertion assertion;
/*    */   
/*    */   public OIOPrincipal(UserAssertion paramUserAssertion) {
/* 33 */     this.assertion = paramUserAssertion;
/*    */   }
/*    */   
/*    */   public String getName() {
/* 37 */     return this.assertion.getSubject();
/*    */   }
/*    */   
/*    */   public UserAssertion getAssertion() {
/* 41 */     return this.assertion;
/*    */   }
/*    */   
/*    */   public String toString() {
/* 45 */     return "SAML Assertion subject: " + this.assertion.getSubject() + " - assurance level : " + this.assertion.getAssuranceLevel();
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/dk/itst/oiosaml/sp/OIOPrincipal.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */