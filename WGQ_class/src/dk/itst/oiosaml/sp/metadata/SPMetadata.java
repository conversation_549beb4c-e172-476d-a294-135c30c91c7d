/*     */ package dk.itst.oiosaml.sp.metadata;
/*     */ 
/*     */ import dk.itst.oiosaml.common.SAMLUtil;
/*     */ import dk.itst.oiosaml.configuration.SAMLConfiguration;
/*     */ import dk.itst.oiosaml.configuration.SAMLConfigurationFactory;
/*     */ import dk.itst.oiosaml.sp.model.OIOSamlObject;
/*     */ import java.security.cert.CertificateEncodingException;
/*     */ import org.opensaml.saml2.metadata.AssertionConsumerService;
/*     */ import org.opensaml.saml2.metadata.EntityDescriptor;
/*     */ import org.opensaml.saml2.metadata.KeyDescriptor;
/*     */ import org.opensaml.saml2.metadata.RoleDescriptor;
/*     */ import org.opensaml.saml2.metadata.SPSSODescriptor;
/*     */ import org.opensaml.saml2.metadata.SingleLogoutService;
/*     */ import org.opensaml.xml.XMLObject;
/*     */ import org.opensaml.xml.security.credential.Credential;
/*     */ import org.opensaml.xml.security.x509.X509Credential;
/*     */ import org.opensaml.xml.signature.X509Certificate;
/*     */ import org.opensaml.xml.signature.X509Data;
/*     */ import org.opensaml.xml.util.Base64;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class SPMetadata
/*     */ {
/*     */   public static final String VERSION = "$Id: SPMetadata.java 2950 2008-05-28 08:22:34Z jre $";
/*     */   private EntityDescriptor entityDescriptor;
/*     */   private SPSSODescriptor spSSODescriptor;
/*     */   private static SPMetadata instance;
/*     */   
/*     */   public SPMetadata(EntityDescriptor paramEntityDescriptor, String paramString) {
/*  67 */     this.entityDescriptor = paramEntityDescriptor;
/*  68 */     this.spSSODescriptor = paramEntityDescriptor.getSPSSODescriptor(paramString);
/*     */   }
/*     */   
/*     */   public static SPMetadata getInstance() {
/*  72 */     if (instance == null) {
/*  73 */       SAMLConfiguration sAMLConfiguration = SAMLConfigurationFactory.getConfiguration();
/*  74 */       instance = new SPMetadata((EntityDescriptor)sAMLConfiguration.getSPMetaData(), sAMLConfiguration.getSystemConfiguration().getString("oiosaml-sp.protocol"));
/*     */     } 
/*  76 */     return instance;
/*     */   }
/*     */   
/*     */   public static void setMetadata(SPMetadata paramSPMetadata) {
/*  80 */     instance = paramSPMetadata;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getEntityID() {
/*  88 */     return this.entityDescriptor.getEntityID();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public AssertionConsumerService getDefaultAssertionConsumerService() {
/*  96 */     AssertionConsumerService assertionConsumerService = this.spSSODescriptor.getDefaultAssertionConsumerService();
/*  97 */     if (assertionConsumerService != null)
/*  98 */       return assertionConsumerService; 
/*  99 */     if (this.spSSODescriptor.getAssertionConsumerServices().isEmpty())
/* 100 */       throw new IllegalStateException("No AssertionConsumerServices defined in SP metadata"); 
/* 101 */     return this.spSSODescriptor.getAssertionConsumerServices().get(0);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getAssertionConsumerServiceLocation(int paramInt) {
/* 111 */     if (this.spSSODescriptor.getAssertionConsumerServices().size() > paramInt) {
/* 112 */       AssertionConsumerService assertionConsumerService = this.spSSODescriptor.getAssertionConsumerServices().get(paramInt);
/* 113 */       return assertionConsumerService.getLocation();
/*     */     } 
/* 115 */     return null;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getSingleLogoutServiceHTTPRedirectLocation() {
/* 124 */     for (SingleLogoutService singleLogoutService : this.spSSODescriptor.getSingleLogoutServices()) {
/* 125 */       if ("urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Redirect".equals(singleLogoutService.getBinding())) {
/* 126 */         return singleLogoutService.getLocation();
/*     */       }
/*     */     } 
/* 129 */     return null;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getSingleLogoutServiceHTTPRedirectResponseLocation() {
/* 138 */     for (SingleLogoutService singleLogoutService : this.spSSODescriptor.getSingleLogoutServices()) {
/* 139 */       if ("urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Redirect".equals(singleLogoutService.getBinding())) {
/* 140 */         return singleLogoutService.getResponseLocation();
/*     */       }
/*     */     } 
/* 143 */     return null;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getSingleLogoutServiceSOAPLocation() {
/* 152 */     for (SingleLogoutService singleLogoutService : this.spSSODescriptor.getSingleLogoutServices()) {
/* 153 */       if ("urn:oasis:names:tc:SAML:2.0:bindings:SOAP".equals(singleLogoutService.getBinding())) {
/* 154 */         return singleLogoutService.getLocation();
/*     */       }
/*     */     } 
/* 157 */     return null;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getSingleLogoutServiceHTTPPostLocation() {
/* 166 */     for (SingleLogoutService singleLogoutService : this.spSSODescriptor.getSingleLogoutServices()) {
/* 167 */       if ("urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST".equals(singleLogoutService.getBinding())) {
/* 168 */         return singleLogoutService.getLocation();
/*     */       }
/*     */     } 
/* 171 */     return null;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getSingleLogoutServiceHTTPPostResponseLocation() {
/* 180 */     for (SingleLogoutService singleLogoutService : this.spSSODescriptor.getSingleLogoutServices()) {
/* 181 */       if ("urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST".equals(singleLogoutService.getBinding())) {
/* 182 */         return singleLogoutService.getResponseLocation();
/*     */       }
/*     */     } 
/* 185 */     return null;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getMetadata(Credential paramCredential, boolean paramBoolean) {
/* 200 */     X509Credential x509Credential = (X509Credential)paramCredential;
/* 201 */     EntityDescriptor entityDescriptor = (EntityDescriptor)SAMLUtil.clone((XMLObject)this.entityDescriptor);
/* 202 */     for (RoleDescriptor roleDescriptor : entityDescriptor.getRoleDescriptors()) {
/* 203 */       for (KeyDescriptor keyDescriptor : roleDescriptor.getKeyDescriptors()) {
/* 204 */         for (X509Data x509Data : keyDescriptor.getKeyInfo().getX509Datas()) {
/* 205 */           for (X509Certificate x509Certificate : x509Data.getX509Certificates()) {
/*     */             try {
/* 207 */               x509Certificate.setValue(Base64.encodeBytes(x509Credential.getEntityCertificate().getEncoded()));
/* 208 */             } catch (CertificateEncodingException certificateEncodingException) {
/* 209 */               throw new RuntimeException(certificateEncodingException);
/*     */             } 
/*     */           } 
/*     */         } 
/*     */       } 
/*     */     } 
/* 215 */     OIOSamlObject oIOSamlObject = new OIOSamlObject((XMLObject)entityDescriptor);
/* 216 */     if (paramBoolean) {
/* 217 */       oIOSamlObject.sign(paramCredential);
/*     */     }
/* 219 */     return oIOSamlObject.toXML();
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/dk/itst/oiosaml/sp/metadata/SPMetadata.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */