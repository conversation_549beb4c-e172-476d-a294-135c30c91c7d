/*     */ package dk.itst.oiosaml.sp.metadata;
/*     */ 
/*     */ import dk.itst.oiosaml.configuration.SAMLConfiguration;
/*     */ import dk.itst.oiosaml.configuration.SAMLConfigurationFactory;
/*     */ import dk.itst.oiosaml.error.Layer;
/*     */ import dk.itst.oiosaml.error.WrappedException;
/*     */ import dk.itst.oiosaml.logging.Logger;
/*     */ import dk.itst.oiosaml.logging.LoggerFactory;
/*     */ import dk.itst.oiosaml.security.SecurityHelper;
/*     */ import java.security.PublicKey;
/*     */ import java.security.cert.CertificateException;
/*     */ import java.security.cert.X509Certificate;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Arrays;
/*     */ import java.util.Collection;
/*     */ import java.util.Date;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import org.opensaml.saml2.core.Attribute;
/*     */ import org.opensaml.saml2.metadata.ArtifactResolutionService;
/*     */ import org.opensaml.saml2.metadata.AttributeAuthorityDescriptor;
/*     */ import org.opensaml.saml2.metadata.AttributeService;
/*     */ import org.opensaml.saml2.metadata.Endpoint;
/*     */ import org.opensaml.saml2.metadata.EntityDescriptor;
/*     */ import org.opensaml.saml2.metadata.IDPSSODescriptor;
/*     */ import org.opensaml.saml2.metadata.KeyDescriptor;
/*     */ import org.opensaml.saml2.metadata.SingleLogoutService;
/*     */ import org.opensaml.saml2.metadata.SingleSignOnService;
/*     */ import org.opensaml.xml.security.credential.UsageType;
/*     */ import org.opensaml.xml.signature.X509Certificate;
/*     */ import org.opensaml.xml.signature.X509Data;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class IdpMetadata
/*     */ {
/*     */   public static final String VERSION = "$Id: IdpMetadata.java 2964 2008-06-02 11:34:06Z jre $";
/*     */   public static final String METADATA_DIRECTORY = "common.saml2.metadata.idp.directory";
/*     */   private static IdpMetadata instance;
/*  76 */   private static final Logger log = LoggerFactory.getLogger(IdpMetadata.class);
/*     */   
/*  78 */   private final Map<String, Metadata> metadata = new HashMap<>();
/*     */   
/*     */   public IdpMetadata(String paramString, EntityDescriptor... paramVarArgs) {
/*  81 */     for (EntityDescriptor entityDescriptor : paramVarArgs) {
/*  82 */       if (this.metadata.containsKey(entityDescriptor.getEntityID())) {
/*  83 */         ((Metadata)this.metadata.get(entityDescriptor.getEntityID())).addCertificates((new Metadata(entityDescriptor, paramString)).getCertificates());
/*     */       } else {
/*  85 */         this.metadata.put(entityDescriptor.getEntityID(), new Metadata(entityDescriptor, paramString));
/*     */       } 
/*     */     } 
/*     */   }
/*     */   
/*     */   public static IdpMetadata getInstance() {
/*  91 */     if (instance == null) {
/*  92 */       SAMLConfiguration sAMLConfiguration = SAMLConfigurationFactory.getConfiguration();
/*  93 */       String str = sAMLConfiguration.getSystemConfiguration().getString("oiosaml-sp.protocol");
/*  94 */       List list = sAMLConfiguration.getListOfIdpMetadata();
/*  95 */       instance = new IdpMetadata(str, (EntityDescriptor[])list.toArray((Object[])new EntityDescriptor[list.size()]));
/*     */     } 
/*  97 */     return instance;
/*     */   }
/*     */   
/*     */   public static void setMetadata(IdpMetadata paramIdpMetadata) {
/* 101 */     instance = paramIdpMetadata;
/*     */   }
/*     */   
/*     */   public Metadata getMetadata(String paramString) {
/* 105 */     Metadata metadata = this.metadata.get(paramString);
/* 106 */     if (metadata == null) {
/* 107 */       throw new IllegalArgumentException("No metadata found for " + paramString);
/*     */     }
/* 109 */     return metadata;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean enableDiscovery() {
/* 118 */     return (this.metadata.size() > 1);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Metadata getFirstMetadata() {
/* 128 */     return getMetadata(getEntityIDs().iterator().next());
/*     */   }
/*     */   
/*     */   public Collection<String> getEntityIDs() {
/* 132 */     return this.metadata.keySet();
/*     */   }
/*     */ 
/*     */   
/*     */   public Metadata findSupportedEntity(String... paramVarArgs) {
/* 137 */     for (String str : paramVarArgs) {
/* 138 */       Metadata metadata = this.metadata.get(str);
/* 139 */       if (metadata != null) {
/* 140 */         return metadata;
/*     */       }
/*     */     } 
/* 143 */     log.debug("No supported idp found in " + Arrays.toString((Object[])paramVarArgs) + ". Supported ids: " + this.metadata.keySet());
/* 144 */     return null;
/*     */   }
/*     */   
/*     */   public static class Metadata {
/*     */     private EntityDescriptor entityDescriptor;
/*     */     private IDPSSODescriptor idpSSODescriptor;
/* 150 */     private Collection<X509Certificate> certificates = new ArrayList<>();
/* 151 */     private Map<X509Certificate, Date> validCertificates = new HashMap<>();
/*     */     
/*     */     private Metadata(EntityDescriptor param1EntityDescriptor, String param1String) {
/* 154 */       this.entityDescriptor = param1EntityDescriptor;
/* 155 */       this.idpSSODescriptor = param1EntityDescriptor.getIDPSSODescriptor(param1String);
/*     */       try {
/* 157 */         X509Certificate x509Certificate = SecurityHelper.buildJavaX509Cert(getCertificateNode().getValue());
/* 158 */         this.certificates.add(x509Certificate);
/* 159 */       } catch (CertificateException certificateException) {
/* 160 */         throw new WrappedException(Layer.BUSINESS, certificateException);
/*     */       } 
/*     */     }
/*     */     
/*     */     public void addCertificates(Collection<X509Certificate> param1Collection) {
/* 165 */       this.certificates.addAll(param1Collection);
/*     */     }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*     */     public String getEntityID() {
/* 173 */       return this.entityDescriptor.getEntityID();
/*     */     }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*     */     public String getArtifactResolutionServiceLocation(String param1String) throws IllegalArgumentException {
/* 181 */       for (ArtifactResolutionService artifactResolutionService : this.idpSSODescriptor.getArtifactResolutionServices()) {
/* 182 */         if ("urn:oasis:names:tc:SAML:2.0:bindings:SOAP".equals(artifactResolutionService.getBinding())) {
/* 183 */           return artifactResolutionService.getLocation();
/*     */         }
/*     */       } 
/* 186 */       throw new IllegalArgumentException("No artifact resolution service for binding " + param1String);
/*     */     }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*     */     public String getSingleSignonServiceLocation(String param1String) throws IllegalArgumentException {
/* 196 */       for (SingleSignOnService singleSignOnService : this.idpSSODescriptor.getSingleSignOnServices()) {
/* 197 */         if (singleSignOnService.getBinding().equals(param1String)) {
/* 198 */           return singleSignOnService.getLocation();
/*     */         }
/*     */       } 
/* 201 */       throw new IllegalArgumentException("Binding " + param1String + " not found");
/*     */     }
/*     */     
/*     */     public String getAttributeQueryServiceLocation(String param1String) throws IllegalArgumentException {
/* 205 */       AttributeAuthorityDescriptor attributeAuthorityDescriptor = this.entityDescriptor.getAttributeAuthorityDescriptor("urn:oasis:names:tc:SAML:2.0:protocol");
/* 206 */       if (attributeAuthorityDescriptor == null) throw new IllegalArgumentException("Metadata does not contain a AttributeAuthorityDescriptor"); 
/* 207 */       for (AttributeService attributeService : attributeAuthorityDescriptor.getAttributeServices()) {
/* 208 */         if (param1String.equals(attributeService.getBinding())) {
/* 209 */           return attributeService.getLocation();
/*     */         }
/*     */       } 
/* 212 */       throw new IllegalArgumentException("Binding " + param1String + " not found in AttributeServices");
/*     */     }
/*     */     
/*     */     public List<SingleSignOnService> getSingleSignonServices() {
/* 216 */       return this.idpSSODescriptor.getSingleSignOnServices();
/*     */     }
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*     */     public String getSingleLogoutServiceLocation() {
/* 223 */       String str = null;
/* 224 */       if (this.idpSSODescriptor.getSingleLogoutServices().size() > 0) {
/* 225 */         SingleLogoutService singleLogoutService = this.idpSSODescriptor.getSingleLogoutServices().get(0);
/* 226 */         str = singleLogoutService.getLocation();
/*     */       } 
/* 228 */       return str;
/*     */     }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*     */     public String getSingleLogoutServiceResponseLocation() {
/* 236 */       if (this.idpSSODescriptor.getSingleLogoutServices().size() > 0) {
/* 237 */         List list = this.idpSSODescriptor.getSingleLogoutServices();
/*     */ 
/*     */         
/* 240 */         SingleLogoutService singleLogoutService = this.idpSSODescriptor.getSingleLogoutServices().get(0);
/* 241 */         for (SingleLogoutService singleLogoutService1 : list) {
/* 242 */           if (singleLogoutService1.getBinding().equals("urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST")) {
/* 243 */             singleLogoutService = singleLogoutService1;
/*     */             
/*     */             break;
/*     */           } 
/*     */         } 
/* 248 */         String str = singleLogoutService.getResponseLocation();
/* 249 */         if (str == null) {
/* 250 */           str = singleLogoutService.getLocation();
/*     */         }
/* 252 */         return str;
/*     */       } 
/* 254 */       return null;
/*     */     }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*     */     private X509Certificate getCertificateNode() {
/* 264 */       if (this.idpSSODescriptor != null && this.idpSSODescriptor.getKeyDescriptors().size() > 0) {
/* 265 */         KeyDescriptor keyDescriptor = null;
/* 266 */         for (KeyDescriptor keyDescriptor1 : this.idpSSODescriptor.getKeyDescriptors()) {
/* 267 */           if (keyDescriptor1.getUse().equals(UsageType.SIGNING)) {
/* 268 */             keyDescriptor = keyDescriptor1;
/*     */           }
/*     */         } 
/*     */         
/* 272 */         if (keyDescriptor.getKeyInfo().getX509Datas().size() > 0) {
/* 273 */           X509Data x509Data = keyDescriptor.getKeyInfo().getX509Datas().get(0);
/* 274 */           if (x509Data.getX509Certificates().size() > 0) {
/* 275 */             return x509Data.getX509Certificates().get(0);
/*     */           }
/*     */         } 
/*     */       } 
/* 279 */       throw new IllegalStateException("IdP Metadata does not contain a certificate: " + getEntityID());
/*     */     }
/*     */     
/*     */     Collection<X509Certificate> getAllCertificates() {
/* 283 */       return this.certificates;
/*     */     }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*     */     public Collection<X509Certificate> getCertificates() {
/* 292 */       ArrayList<X509Certificate> arrayList = new ArrayList();
/* 293 */       for (X509Certificate x509Certificate : this.certificates) {
/* 294 */         if (x509Certificate.getNotAfter().after(new Date())) {
/* 295 */           arrayList.add(x509Certificate); continue;
/*     */         } 
/* 297 */         IdpMetadata.log.debug("Local Metadata certificateValidated for " + getEntityID() + " expired at " + x509Certificate.getNotAfter() + ", current: " + new Date());
/*     */       } 
/*     */       
/* 300 */       return arrayList;
/*     */     }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*     */     public Collection<X509Certificate> getValidCertificates() {
/* 309 */       ArrayList<X509Certificate> arrayList = new ArrayList();
/* 310 */       for (X509Certificate x509Certificate : this.validCertificates.keySet()) {
/* 311 */         if (x509Certificate.getNotAfter().after(new Date())) {
/* 312 */           arrayList.add(x509Certificate); continue;
/*     */         } 
/* 314 */         IdpMetadata.log.debug("Local Metadata certificateValidated for " + getEntityID() + " expired at " + x509Certificate.getNotAfter() + ", current: " + new Date());
/*     */       } 
/*     */       
/* 317 */       return arrayList;
/*     */     }
/*     */     
/*     */     public void setCertificateValid(X509Certificate param1X509Certificate, boolean param1Boolean) {
/* 321 */       if (param1Boolean) {
/*     */         
/* 323 */         this.validCertificates.put(param1X509Certificate, new Date());
/*     */       } else {
/* 325 */         this.validCertificates.remove(param1X509Certificate);
/*     */       } 
/*     */     }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*     */     public Date getLastTimeForCertificationValidation(X509Certificate param1X509Certificate) {
/* 335 */       return this.validCertificates.get(param1X509Certificate);
/*     */     }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*     */     public Endpoint findLoginEndpoint(String[] param1ArrayOfString) {
/* 344 */       if (param1ArrayOfString == null) throw new IllegalArgumentException("bindings cannot be null");
/*     */       
/* 346 */       for (String str : param1ArrayOfString) {
/* 347 */         for (SingleSignOnService singleSignOnService : this.idpSSODescriptor.getSingleSignOnServices()) {
/* 348 */           if (singleSignOnService.getBinding().equalsIgnoreCase(str)) {
/* 349 */             return (Endpoint)singleSignOnService;
/*     */           }
/*     */         } 
/*     */       } 
/* 353 */       throw new IllegalArgumentException("No SingleSignonService found for " + Arrays.toString(param1ArrayOfString));
/*     */     }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*     */     public String getAttributeNameFormat(String param1String1, String param1String2) {
/* 363 */       for (Attribute attribute : this.idpSSODescriptor.getAttributes()) {
/* 364 */         if (param1String1.equals(attribute.getName())) {
/* 365 */           return attribute.getNameFormat();
/*     */         }
/*     */       } 
/* 368 */       return param1String2;
/*     */     }
/*     */ 
/*     */     
/*     */     public Collection<PublicKey> getPublicKeys() {
/* 373 */       ArrayList<PublicKey> arrayList = new ArrayList();
/* 374 */       for (X509Certificate x509Certificate : getCertificates()) {
/* 375 */         arrayList.add(x509Certificate.getPublicKey());
/*     */       }
/* 377 */       return arrayList;
/*     */     }
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/dk/itst/oiosaml/sp/metadata/IdpMetadata.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */