/*    */ package dk.itst.oiosaml.helper;
/*    */ 
/*    */ import dk.itst.oiosaml.logging.Logger;
/*    */ import dk.itst.oiosaml.logging.LoggerFactory;
/*    */ import java.util.ArrayList;
/*    */ import java.util.List;
/*    */ 
/*    */ public class DeveloperHelper
/*    */ {
/* 10 */   private static final Logger log = LoggerFactory.getLogger(DeveloperHelper.class);
/*    */   
/*    */   public static void log(String paramString) {
/* 13 */     List<String> list = convertToLines(paramString);
/* 14 */     StringBuilder stringBuilder = new StringBuilder();
/*    */     
/* 16 */     stringBuilder.append("\n\n *****************************************************************************\n");
/* 17 */     stringBuilder.append(" *   DEVELOPER HINT   - the message below might explain the error            *\n");
/* 18 */     stringBuilder.append(" *****************************************************************************\n");
/*    */     
/* 20 */     for (String str : list) {
/* 21 */       stringBuilder.append(" * " + str + " *\n");
/*    */     }
/*    */     
/* 24 */     stringBuilder.append(" *****************************************************************************\n\n");
/*    */     
/* 26 */     log.info(stringBuilder.toString());
/*    */   }
/*    */   
/*    */   private static List<String> convertToLines(String paramString) {
/* 30 */     ArrayList<String> arrayList = new ArrayList();
/* 31 */     int i = 999999999;
/* 32 */     while (i > 0) {
/* 33 */       if (paramString.length() <= 73) {
/* 34 */         StringBuilder stringBuilder1 = new StringBuilder(paramString);
/* 35 */         while (stringBuilder1.length() < 73) {
/* 36 */           stringBuilder1.append(" ");
/*    */         }
/*    */         
/* 39 */         arrayList.add(stringBuilder1.toString());
/*    */         
/*    */         break;
/*    */       } 
/*    */       
/* 44 */       byte b = 73;
/* 45 */       for (; b >= 0 && 
/* 46 */         paramString.charAt(b) != ' '; b--);
/*    */ 
/*    */ 
/*    */ 
/*    */       
/* 51 */       if (b == 0) {
/* 52 */         b = 73;
/*    */       }
/*    */       
/* 55 */       StringBuilder stringBuilder = new StringBuilder(paramString.substring(0, b));
/* 56 */       while (stringBuilder.length() < 73) {
/* 57 */         stringBuilder.append(" ");
/*    */       }
/*    */       
/* 60 */       arrayList.add(stringBuilder.toString());
/*    */ 
/*    */       
/* 63 */       while (paramString.length() > b && paramString.charAt(b) == ' ') {
/* 64 */         b++;
/*    */       }
/*    */       
/* 67 */       paramString = paramString.substring(b);
/* 68 */       i--;
/*    */     } 
/*    */     
/* 71 */     return arrayList;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/dk/itst/oiosaml/helper/DeveloperHelper.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */