/*     */ package dk.itst.oiosaml.security;
/*     */ 
/*     */ import java.io.ByteArrayInputStream;
/*     */ import java.math.BigInteger;
/*     */ import java.security.KeyException;
/*     */ import java.security.KeyFactory;
/*     */ import java.security.KeyPair;
/*     */ import java.security.KeyPairGenerator;
/*     */ import java.security.NoSuchAlgorithmException;
/*     */ import java.security.NoSuchProviderException;
/*     */ import java.security.Provider;
/*     */ import java.security.PublicKey;
/*     */ import java.security.Security;
/*     */ import java.security.cert.CRLException;
/*     */ import java.security.cert.CertificateException;
/*     */ import java.security.cert.CertificateFactory;
/*     */ import java.security.cert.X509CRL;
/*     */ import java.security.cert.X509Certificate;
/*     */ import java.security.spec.InvalidKeySpecException;
/*     */ import java.security.spec.KeySpec;
/*     */ import java.util.Date;
/*     */ import org.apache.xml.security.algorithms.JCEMapper;
/*     */ import org.opensaml.xml.security.credential.Credential;
/*     */ import org.opensaml.xml.util.Base64;
/*     */ import org.saml_bouncycastle.asn1.ASN1Encodable;
/*     */ import org.saml_bouncycastle.asn1.ASN1InputStream;
/*     */ import org.saml_bouncycastle.asn1.ASN1Sequence;
/*     */ import org.saml_bouncycastle.asn1.x500.X500Name;
/*     */ import org.saml_bouncycastle.asn1.x509.SubjectPublicKeyInfo;
/*     */ import org.saml_bouncycastle.asn1.x509.X509Extension;
/*     */ import org.saml_bouncycastle.cert.X509CertificateHolder;
/*     */ import org.saml_bouncycastle.cert.X509v3CertificateBuilder;
/*     */ import org.saml_bouncycastle.cert.jcajce.JcaX509CertificateConverter;
/*     */ import org.saml_bouncycastle.cert.jcajce.JcaX509ExtensionUtils;
/*     */ import org.saml_bouncycastle.jce.provider.BouncyCastleProvider;
/*     */ import org.saml_bouncycastle.operator.ContentSigner;
/*     */ import org.saml_bouncycastle.operator.jcajce.JcaContentSignerBuilder;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class SecurityHelper
/*     */ {
/*     */   public static final String VERSION = "$Id: SecurityHelper.java 2836 2008-05-14 06:22:24Z jre $";
/*     */   
/*     */   static {
/*  65 */     Security.addProvider((Provider)new BouncyCastleProvider());
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static X509Certificate buildJavaX509Cert(String paramString) throws CertificateException {
/*  80 */     CertificateFactory certificateFactory = null;
/*  81 */     certificateFactory = CertificateFactory.getInstance("X.509");
/*     */     
/*  83 */     ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(Base64.decode(paramString));
/*  84 */     X509Certificate x509Certificate = null;
/*  85 */     x509Certificate = (X509Certificate)certificateFactory.generateCertificate(byteArrayInputStream);
/*  86 */     return x509Certificate;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static X509CRL buildJavaX509CRL(String paramString) throws CertificateException, CRLException {
/*  99 */     CertificateFactory certificateFactory = null;
/* 100 */     certificateFactory = CertificateFactory.getInstance("X.509");
/*     */     
/* 102 */     ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(Base64.decode(paramString));
/* 103 */     X509CRL x509CRL = null;
/* 104 */     x509CRL = (X509CRL)certificateFactory.generateCRL(byteArrayInputStream);
/*     */     
/* 106 */     return x509CRL;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static PublicKey buildKey(KeySpec paramKeySpec, String paramString) throws KeyException {
/*     */     try {
/* 122 */       KeyFactory keyFactory = KeyFactory.getInstance(paramString);
/* 123 */       return keyFactory.generatePublic(paramKeySpec);
/* 124 */     } catch (NoSuchAlgorithmException noSuchAlgorithmException) {
/* 125 */       throw new KeyException(paramString + "algorithm is not supported by the JCE:" + noSuchAlgorithmException.getMessage());
/* 126 */     } catch (InvalidKeySpecException invalidKeySpecException) {
/* 127 */       throw new KeyException("Invalid key information:" + invalidKeySpecException.getMessage());
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static KeyPair generateKeyPairFromURI(String paramString, int paramInt) throws NoSuchAlgorithmException, NoSuchProviderException {
/* 142 */     String str = JCEMapper.getJCEKeyAlgorithmFromURI(paramString);
/* 143 */     return generateKeyPair(str, paramInt, null);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static KeyPair generateKeyPair(String paramString1, int paramInt, String paramString2) throws NoSuchAlgorithmException, NoSuchProviderException {
/* 158 */     KeyPair keyPair = null;
/* 159 */     KeyPairGenerator keyPairGenerator = null;
/* 160 */     if (paramString2 != null) {
/* 161 */       keyPairGenerator = KeyPairGenerator.getInstance(paramString1, paramString2);
/*     */     } else {
/* 163 */       keyPairGenerator = KeyPairGenerator.getInstance(paramString1);
/*     */     } 
/* 165 */     keyPairGenerator.initialize(paramInt);
/* 166 */     keyPair = keyPairGenerator.generateKeyPair();
/* 167 */     return keyPair;
/*     */   }
/*     */   
/*     */   public static X509Certificate generateCertificate(Credential paramCredential, String paramString) throws Exception {
/* 171 */     X500Name x500Name1 = new X500Name("o=keymanager, ou=oiosaml-sp");
/* 172 */     BigInteger bigInteger = BigInteger.valueOf(System.currentTimeMillis());
/* 173 */     Date date1 = new Date();
/* 174 */     Date date2 = new Date(System.currentTimeMillis() + 315360000000L);
/* 175 */     X500Name x500Name2 = new X500Name("cn=" + paramString + ", ou=oiosaml-sp");
/*     */     
/* 177 */     ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(paramCredential.getPublicKey().getEncoded());
/* 178 */     ASN1InputStream aSN1InputStream = new ASN1InputStream(byteArrayInputStream);
/* 179 */     SubjectPublicKeyInfo subjectPublicKeyInfo = new SubjectPublicKeyInfo((ASN1Sequence)aSN1InputStream.readObject());
/* 180 */     aSN1InputStream.close();
/*     */     
/* 182 */     X509v3CertificateBuilder x509v3CertificateBuilder = new X509v3CertificateBuilder(x500Name1, bigInteger, date1, date2, x500Name2, subjectPublicKeyInfo);
/*     */     
/* 184 */     x509v3CertificateBuilder.addExtension(X509Extension.subjectKeyIdentifier, false, (ASN1Encodable)(new JcaX509ExtensionUtils()).createSubjectKeyIdentifier(paramCredential.getPublicKey()));
/* 185 */     x509v3CertificateBuilder.addExtension(X509Extension.authorityKeyIdentifier, false, (ASN1Encodable)(new JcaX509ExtensionUtils()).createAuthorityKeyIdentifier(paramCredential.getPublicKey()));
/*     */     
/* 187 */     ContentSigner contentSigner = (new JcaContentSignerBuilder("SHA1withRSA")).setProvider("BC").build(paramCredential.getPrivateKey());
/* 188 */     X509CertificateHolder x509CertificateHolder = x509v3CertificateBuilder.build(contentSigner);
/*     */     
/* 190 */     return (new JcaX509CertificateConverter()).setProvider("BC").getCertificate(x509CertificateHolder);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/dk/itst/oiosaml/security/SecurityHelper.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */