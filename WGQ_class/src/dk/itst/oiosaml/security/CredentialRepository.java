/*     */ package dk.itst.oiosaml.security;
/*     */ 
/*     */ import dk.itst.oiosaml.error.Layer;
/*     */ import dk.itst.oiosaml.error.WrappedException;
/*     */ import dk.itst.oiosaml.logging.Logger;
/*     */ import dk.itst.oiosaml.logging.LoggerFactory;
/*     */ import java.security.GeneralSecurityException;
/*     */ import java.security.KeyStore;
/*     */ import java.security.PrivateKey;
/*     */ import java.security.PublicKey;
/*     */ import java.security.cert.X509Certificate;
/*     */ import java.util.Collection;
/*     */ import java.util.Enumeration;
/*     */ import java.util.Map;
/*     */ import java.util.concurrent.ConcurrentHashMap;
/*     */ import org.opensaml.xml.security.x509.BasicX509Credential;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CredentialRepository
/*     */ {
/*  58 */   private static final Logger log = LoggerFactory.getLogger(CredentialRepository.class);
/*     */   
/*  60 */   private final Map<Key, BasicX509Credential> credentials = new ConcurrentHashMap<>();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public BasicX509Credential getCredential(KeyStore paramKeyStore, String paramString1, String paramString2) {
/*  74 */     Key key = new Key(paramString2, paramString1);
/*  75 */     BasicX509Credential basicX509Credential = this.credentials.get(key);
/*  76 */     if (basicX509Credential == null) {
/*  77 */       basicX509Credential = createCredential(paramKeyStore, paramString1);
/*  78 */       this.credentials.put(key, basicX509Credential);
/*     */     } 
/*     */     
/*  81 */     return basicX509Credential;
/*     */   }
/*     */   
/*     */   public BasicX509Credential getCredential(KeyStore paramKeyStore, String paramString) {
/*  85 */     return getCredential(paramKeyStore, paramString, "oiosaml-sp.certificate.location");
/*     */   }
/*     */   
/*     */   public Collection<BasicX509Credential> getCredentials() {
/*  89 */     return this.credentials.values();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public X509Certificate getCertificate(KeyStore paramKeyStore, String paramString1, String paramString2, String paramString3) {
/* 100 */     BasicX509Credential basicX509Credential = null;
/* 101 */     if (paramString3 != null) {
/* 102 */       Key key = new Key(paramString3, paramString1, paramString2);
/* 103 */       basicX509Credential = this.credentials.get(key);
/*     */     } 
/* 105 */     if (basicX509Credential == null) {
/*     */       
/*     */       try {
/* 108 */         if (paramString2 == null) {
/* 109 */           Enumeration<String> enumeration = paramKeyStore.aliases();
/* 110 */           while (enumeration.hasMoreElements()) {
/* 111 */             String str = enumeration.nextElement();
/* 112 */             log.debug("Trying " + str);
/* 113 */             if (paramKeyStore.isCertificateEntry(str)) {
/* 114 */               X509Certificate x509Certificate = (X509Certificate)paramKeyStore.getCertificate(str);
/* 115 */               basicX509Credential = new BasicX509Credential();
/* 116 */               basicX509Credential.setEntityCertificate(x509Certificate);
/* 117 */               if (paramString3 != null) {
/* 118 */                 this.credentials.put(new Key(paramString3, paramString1, str), basicX509Credential);
/*     */               }
/* 120 */               paramString2 = str;
/*     */             } 
/*     */           } 
/*     */         } 
/* 124 */         log.debug("Getting certificate from alias " + paramString2);
/* 125 */         if (paramString3 != null) {
/* 126 */           basicX509Credential = this.credentials.get(new Key(paramString3, paramString1, paramString2));
/*     */         }
/* 128 */         if (basicX509Credential == null) {
/* 129 */           throw new NullPointerException("Unable to find certificate for " + paramString2);
/*     */         }
/* 131 */       } catch (GeneralSecurityException generalSecurityException) {
/* 132 */         throw new WrappedException(Layer.CLIENT, generalSecurityException);
/*     */       } 
/*     */     }
/* 135 */     return basicX509Credential.getEntityCertificate();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static BasicX509Credential createCredential(KeyStore paramKeyStore, String paramString) {
/* 148 */     BasicX509Credential basicX509Credential = new BasicX509Credential();
/*     */     try {
/* 150 */       Enumeration<String> enumeration = paramKeyStore.aliases();
/* 151 */       while (enumeration.hasMoreElements()) {
/* 152 */         String str = enumeration.nextElement();
/*     */         
/* 154 */         if (paramKeyStore.isKeyEntry(str)) {
/* 155 */           PrivateKey privateKey = (PrivateKey)paramKeyStore.getKey(str, paramString.toCharArray());
/* 156 */           basicX509Credential.setPrivateKey(privateKey);
/* 157 */           basicX509Credential.setEntityCertificate((X509Certificate)paramKeyStore.getCertificate(str));
/* 158 */           PublicKey publicKey = paramKeyStore.getCertificate(str).getPublicKey();
/* 159 */           if (log.isDebugEnabled())
/* 160 */             log.debug("publicKey..:" + publicKey + ", privateKey: " + privateKey); 
/* 161 */           basicX509Credential.setPublicKey(publicKey);
/*     */         } 
/*     */       } 
/* 164 */     } catch (GeneralSecurityException generalSecurityException) {
/* 165 */       throw new WrappedException(Layer.CLIENT, generalSecurityException);
/*     */     } 
/*     */     
/* 168 */     return basicX509Credential;
/*     */   }
/*     */   
/*     */   private static class Key {
/*     */     private final String location;
/*     */     private final String password;
/*     */     private final String alias;
/*     */     
/*     */     public Key(String param1String1, String param1String2) {
/* 177 */       this.location = param1String1;
/* 178 */       this.password = param1String2;
/* 179 */       this.alias = null;
/*     */     }
/*     */     
/*     */     public Key(String param1String1, String param1String2, String param1String3) {
/* 183 */       this.location = param1String1;
/* 184 */       this.password = param1String2;
/* 185 */       this.alias = param1String3;
/*     */     }
/*     */ 
/*     */ 
/*     */     
/*     */     public int hashCode() {
/* 191 */       int i = 1;
/* 192 */       i = 31 * i + ((this.alias == null) ? 0 : this.alias.hashCode());
/* 193 */       i = 31 * i + ((this.location == null) ? 0 : this.location.hashCode());
/* 194 */       i = 31 * i + ((this.password == null) ? 0 : this.password.hashCode());
/* 195 */       return i;
/*     */     }
/*     */ 
/*     */     
/*     */     public boolean equals(Object param1Object) {
/* 200 */       if (this == param1Object) return true; 
/* 201 */       if (param1Object == null) return false; 
/* 202 */       if (getClass() != param1Object.getClass()) return false; 
/* 203 */       Key key = (Key)param1Object;
/* 204 */       if (this.alias == null)
/* 205 */       { if (key.alias != null) return false;  }
/* 206 */       else if (!this.alias.equals(key.alias)) { return false; }
/* 207 */        if (this.location == null)
/* 208 */       { if (key.location != null) return false;  }
/* 209 */       else if (!this.location.equals(key.location)) { return false; }
/* 210 */        if (this.password == null)
/* 211 */       { if (key.password != null) return false;  }
/* 212 */       else if (!this.password.equals(key.password)) { return false; }
/*     */       
/* 214 */       return true;
/*     */     }
/*     */   }
/*     */   
/*     */   public X509Certificate getCertificate(KeyStore paramKeyStore, String paramString1, String paramString2) {
/* 219 */     return getCertificate(paramKeyStore, paramString1, paramString2, "oiosaml-sp.certificate.location");
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/dk/itst/oiosaml/security/CredentialRepository.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */