/*    */ package dk.itst.oiosaml.common;
/*    */ 
/*    */ import java.io.IOException;
/*    */ import java.util.List;
/*    */ import org.opensaml.ws.soap.soap11.Envelope;
/*    */ import org.opensaml.ws.soap.soap11.Fault;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class SOAPException
/*    */   extends IOException
/*    */ {
/*    */   private static final long serialVersionUID = 6684189535343316988L;
/*    */   private final String response;
/*    */   private Envelope envelope;
/*    */   
/*    */   public SOAPException(int paramInt, String paramString) {
/* 46 */     super("Server returned error response: " + paramInt);
/* 47 */     this.response = paramString;
/*    */     
/*    */     try {
/* 50 */       this.envelope = (Envelope)SAMLUtil.unmarshallElementFromString(paramString);
/* 51 */     } catch (Exception exception) {}
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public Envelope getEnvelope() {
/* 59 */     return this.envelope;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getResponse() {
/* 66 */     return this.response;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public Fault getFault() {
/* 74 */     if (this.envelope == null) return null; 
/* 75 */     List<Fault> list = this.envelope.getBody().getUnknownXMLObjects(Fault.DEFAULT_ELEMENT_NAME);
/* 76 */     if (list.isEmpty()) return null;
/*    */     
/* 78 */     return list.get(0);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/dk/itst/oiosaml/common/SOAPException.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */