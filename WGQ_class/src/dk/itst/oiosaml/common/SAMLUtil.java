/*     */ package dk.itst.oiosaml.common;
/*     */ 
/*     */ import dk.itst.oiosaml.error.Layer;
/*     */ import dk.itst.oiosaml.error.WrappedException;
/*     */ import dk.itst.oiosaml.logging.Logger;
/*     */ import dk.itst.oiosaml.logging.LoggerFactory;
/*     */ import java.io.BufferedReader;
/*     */ import java.io.ByteArrayInputStream;
/*     */ import java.io.File;
/*     */ import java.io.FileReader;
/*     */ import java.io.IOException;
/*     */ import java.io.InputStream;
/*     */ import java.lang.reflect.Field;
/*     */ import java.security.InvalidParameterException;
/*     */ import java.util.Map;
/*     */ import java.util.concurrent.ConcurrentHashMap;
/*     */ import javax.xml.namespace.QName;
/*     */ import javax.xml.parsers.DocumentBuilder;
/*     */ import javax.xml.parsers.DocumentBuilderFactory;
/*     */ import javax.xml.parsers.ParserConfigurationException;
/*     */ import org.joda.time.DateTime;
/*     */ import org.opensaml.Configuration;
/*     */ import org.opensaml.saml2.core.Artifact;
/*     */ import org.opensaml.saml2.core.Audience;
/*     */ import org.opensaml.saml2.core.AudienceRestriction;
/*     */ import org.opensaml.saml2.core.AuthnContext;
/*     */ import org.opensaml.saml2.core.AuthnContextClassRef;
/*     */ import org.opensaml.saml2.core.Conditions;
/*     */ import org.opensaml.saml2.core.Issuer;
/*     */ import org.opensaml.saml2.core.NameID;
/*     */ import org.opensaml.saml2.core.SessionIndex;
/*     */ import org.opensaml.saml2.core.Status;
/*     */ import org.opensaml.saml2.core.StatusCode;
/*     */ import org.opensaml.saml2.core.Subject;
/*     */ import org.opensaml.saml2.core.SubjectConfirmation;
/*     */ import org.opensaml.saml2.core.SubjectConfirmationData;
/*     */ import org.opensaml.saml2.metadata.ArtifactResolutionService;
/*     */ import org.opensaml.saml2.metadata.AssertionConsumerService;
/*     */ import org.opensaml.saml2.metadata.AttributeConsumingService;
/*     */ import org.opensaml.saml2.metadata.Company;
/*     */ import org.opensaml.saml2.metadata.EmailAddress;
/*     */ import org.opensaml.saml2.metadata.LocalizedString;
/*     */ import org.opensaml.saml2.metadata.NameIDFormat;
/*     */ import org.opensaml.saml2.metadata.Organization;
/*     */ import org.opensaml.saml2.metadata.OrganizationDisplayName;
/*     */ import org.opensaml.saml2.metadata.OrganizationName;
/*     */ import org.opensaml.saml2.metadata.OrganizationURL;
/*     */ import org.opensaml.saml2.metadata.RequestedAttribute;
/*     */ import org.opensaml.saml2.metadata.ServiceName;
/*     */ import org.opensaml.saml2.metadata.SingleLogoutService;
/*     */ import org.opensaml.xml.ElementExtensibleXMLObject;
/*     */ import org.opensaml.xml.XMLObject;
/*     */ import org.opensaml.xml.XMLObjectBuilder;
/*     */ import org.opensaml.xml.io.Marshaller;
/*     */ import org.opensaml.xml.io.MarshallingException;
/*     */ import org.opensaml.xml.io.Unmarshaller;
/*     */ import org.opensaml.xml.io.UnmarshallingException;
/*     */ import org.opensaml.xml.signature.KeyInfo;
/*     */ import org.opensaml.xml.signature.KeyName;
/*     */ import org.opensaml.xml.signature.Signature;
/*     */ import org.opensaml.xml.util.Base64;
/*     */ import org.opensaml.xml.util.XMLHelper;
/*     */ import org.w3c.dom.Document;
/*     */ import org.w3c.dom.Element;
/*     */ import org.xml.sax.SAXException;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class SAMLUtil
/*     */ {
/*     */   public static final String VERSION = "$Id: BRSUtil.java 2910 2008-05-21 13:07:31Z jre $";
/* 105 */   private static final Logger log = LoggerFactory.getLogger(SAMLUtil.class);
/*     */   
/*     */   public static final String OIOSAML_HOME = "oiosaml.home";
/*     */   public static final String OIOSAML_DEFAULT_CONFIGURATION_FILE = "oiosaml-sp.properties";
/* 109 */   private static final Map<Class<?>, QName> elementCache = new ConcurrentHashMap<>();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static <T extends XMLObject> T buildXMLObject(Class<T> paramClass) {
/*     */     try {
/* 121 */       QName qName = getElementQName(paramClass);
/* 122 */       XMLObjectBuilder xMLObjectBuilder = Configuration.getBuilderFactory().getBuilder(qName);
/* 123 */       if (xMLObjectBuilder == null) {
/* 124 */         throw new InvalidParameterException("No builder exists for object: " + qName.getLocalPart());
/*     */       }
/* 126 */       return (T)xMLObjectBuilder.buildObject(qName.getNamespaceURI(), qName.getLocalPart(), qName.getPrefix());
/* 127 */     } catch (SecurityException securityException) {
/* 128 */       throw new RuntimeException(securityException);
/*     */     } 
/*     */   }
/*     */   
/*     */   private static <T> QName getElementQName(Class<T> paramClass) {
/* 133 */     if (elementCache.containsKey(paramClass)) return elementCache.get(paramClass);
/*     */     
/*     */     try {
/*     */       Field field;
/*     */       try {
/* 138 */         field = paramClass.getDeclaredField("DEFAULT_ELEMENT_NAME");
/* 139 */       } catch (NoSuchFieldException noSuchFieldException) {
/* 140 */         field = paramClass.getDeclaredField("ELEMENT_NAME");
/*     */       } 
/*     */       
/* 143 */       QName qName = (QName)field.get(null);
/* 144 */       elementCache.put(paramClass, qName);
/* 145 */       return qName;
/* 146 */     } catch (NoSuchFieldException noSuchFieldException) {
/* 147 */       throw new RuntimeException(noSuchFieldException);
/* 148 */     } catch (IllegalAccessException illegalAccessException) {
/* 149 */       throw new RuntimeException(illegalAccessException);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static Issuer createIssuer(String paramString) {
/* 161 */     if (paramString == null) return null;
/*     */     
/* 163 */     Issuer issuer = buildXMLObject(Issuer.class);
/* 164 */     issuer.setValue(paramString);
/* 165 */     return issuer;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static NameID createNameID(String paramString) {
/* 176 */     NameID nameID = buildXMLObject(NameID.class);
/* 177 */     nameID.setValue(paramString);
/* 178 */     nameID.setFormat("urn:oasis:names:tc:SAML:2.0:nameid-format:persistent");
/* 179 */     return nameID;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static SessionIndex createSessionIndex(String paramString) {
/* 190 */     SessionIndex sessionIndex = buildXMLObject(SessionIndex.class);
/* 191 */     sessionIndex.setSessionIndex(paramString);
/* 192 */     return sessionIndex;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static Subject createSubject(String paramString1, String paramString2, DateTime paramDateTime) {
/* 206 */     Subject subject = buildXMLObject(Subject.class);
/* 207 */     subject.setNameID(createNameID(paramString1));
/* 208 */     SubjectConfirmation subjectConfirmation = buildXMLObject(SubjectConfirmation.class);
/* 209 */     subjectConfirmation.setMethod("urn:oasis:names:tc:SAML:2.0:cm:bearer");
/* 210 */     SubjectConfirmationData subjectConfirmationData = buildXMLObject(SubjectConfirmationData.class);
/* 211 */     subjectConfirmationData.setRecipient(paramString2);
/* 212 */     subjectConfirmationData.setNotOnOrAfter(paramDateTime);
/* 213 */     subjectConfirmation.setSubjectConfirmationData(subjectConfirmationData);
/* 214 */     subject.getSubjectConfirmations().add(subjectConfirmation);
/* 215 */     return subject;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static AuthnContext createAuthnContext(String paramString) {
/* 227 */     AuthnContext authnContext = buildXMLObject(AuthnContext.class);
/* 228 */     AuthnContextClassRef authnContextClassRef = buildXMLObject(AuthnContextClassRef.class);
/* 229 */     authnContextClassRef.setAuthnContextClassRef(paramString);
/* 230 */     authnContext.setAuthnContextClassRef(authnContextClassRef);
/* 231 */     return authnContext;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static Conditions createAudienceCondition(String paramString) {
/* 242 */     Audience audience = buildXMLObject(Audience.class);
/* 243 */     audience.setAudienceURI(paramString);
/* 244 */     AudienceRestriction audienceRestriction = buildXMLObject(AudienceRestriction.class);
/* 245 */     audienceRestriction.getAudiences().add(audience);
/* 246 */     Conditions conditions = buildXMLObject(Conditions.class);
/* 247 */     conditions.getAudienceRestrictions().add(audienceRestriction);
/* 248 */     return conditions;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static Artifact createArtifact(String paramString) {
/* 259 */     Artifact artifact = buildXMLObject(Artifact.class);
/* 260 */     artifact.setArtifact(paramString);
/* 261 */     return artifact;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static Status createStatus(String paramString) {
/* 272 */     Status status = buildXMLObject(Status.class);
/* 273 */     status.setStatusCode(buildXMLObject(StatusCode.class));
/* 274 */     status.getStatusCode().setValue(paramString);
/* 275 */     return status;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static Signature createSignature(String paramString) {
/* 286 */     Signature signature = buildXMLObject(Signature.class);
/* 287 */     signature.setKeyInfo(buildXMLObject(KeyInfo.class));
/* 288 */     KeyName keyName = buildXMLObject(KeyName.class);
/* 289 */     keyName.setValue(paramString);
/* 290 */     signature.getKeyInfo().getKeyNames().add(keyName);
/* 291 */     return signature;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static EmailAddress createEmail(String paramString) {
/* 298 */     EmailAddress emailAddress = buildXMLObject(EmailAddress.class);
/* 299 */     emailAddress.setAddress(paramString);
/* 300 */     return emailAddress;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static Organization createOrganization(String paramString1, String paramString2, String paramString3) {
/* 307 */     OrganizationDisplayName organizationDisplayName = buildXMLObject(OrganizationDisplayName.class);
/* 308 */     organizationDisplayName.setName(new LocalizedString(paramString2, "en"));
/* 309 */     Organization organization = buildXMLObject(Organization.class);
/* 310 */     organization.getDisplayNames().add(organizationDisplayName);
/*     */     
/* 312 */     OrganizationName organizationName = buildXMLObject(OrganizationName.class);
/* 313 */     organizationName.setName(new LocalizedString(paramString1, "en"));
/* 314 */     organization.getOrganizationNames().add(organizationName);
/*     */     
/* 316 */     OrganizationURL organizationURL = buildXMLObject(OrganizationURL.class);
/* 317 */     organizationURL.setURL(new LocalizedString(paramString3, "en"));
/* 318 */     organization.getURLs().add(organizationURL);
/* 319 */     return organization;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static XMLObject unmarshallElement(InputStream paramInputStream) {
/*     */     try {
/* 331 */       Element element = loadElement(paramInputStream);
/*     */       
/* 333 */       Unmarshaller unmarshaller = Configuration.getUnmarshallerFactory().getUnmarshaller(element);
/* 334 */       if (unmarshaller == null) {
/* 335 */         log.error("Unable to retrieve unmarshaller by DOM Element");
/* 336 */         throw new IllegalArgumentException("No unmarshaller for " + element);
/*     */       } 
/*     */       
/* 339 */       return unmarshaller.unmarshall(element);
/* 340 */     } catch (UnmarshallingException unmarshallingException) {
/* 341 */       log.error("Unmarshalling failed when parsing element file " + paramInputStream, (Throwable)unmarshallingException);
/*     */ 
/*     */       
/* 344 */       return null;
/*     */     } 
/*     */   }
/*     */   public static XMLObject unmarshallElement(Element paramElement) {
/* 348 */     Unmarshaller unmarshaller = Configuration.getUnmarshallerFactory().getUnmarshaller(paramElement);
/* 349 */     if (unmarshaller == null) {
/* 350 */       log.error("Unable to retrieve unmarshaller by DOM Element " + paramElement);
/* 351 */       throw new IllegalArgumentException("No unmarshaller for " + paramElement);
/*     */     } 
/*     */     try {
/* 354 */       return unmarshaller.unmarshall(paramElement);
/* 355 */     } catch (UnmarshallingException unmarshallingException) {
/* 356 */       log.error("Unmarshalling failed when parsing element file " + paramElement, (Throwable)unmarshallingException);
/* 357 */       return null;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static Element loadElement(InputStream paramInputStream) {
/*     */     try {
/* 370 */       DocumentBuilderFactory documentBuilderFactory = getDocumentBuilderFactory();
/*     */       
/* 372 */       DocumentBuilder documentBuilder = documentBuilderFactory.newDocumentBuilder();
/*     */       
/* 374 */       Document document = documentBuilder.parse(paramInputStream);
/* 375 */       return document.getDocumentElement();
/*     */     
/*     */     }
/* 378 */     catch (ParserConfigurationException parserConfigurationException) {
/* 379 */       log.error("Unable to parse element file " + paramInputStream, parserConfigurationException);
/* 380 */     } catch (SAXException sAXException) {
/* 381 */       log.error("Unable to parse element file " + paramInputStream, sAXException);
/* 382 */     } catch (IOException iOException) {
/* 383 */       log.error("Unable to parse element file " + paramInputStream, iOException);
/*     */     } 
/* 385 */     return null;
/*     */   }
/*     */ 
/*     */   
/*     */   private static DocumentBuilderFactory getDocumentBuilderFactory() throws ParserConfigurationException {
/* 390 */     System.setProperty("javax.xml.parsers.DocumentBuilderFactory", "com.sun.org.apache.xerces.internal.jaxp.DocumentBuilderFactoryImpl");
/* 391 */     DocumentBuilderFactory documentBuilderFactory = DocumentBuilderFactory.newInstance();
/* 392 */     documentBuilderFactory.setNamespaceAware(true);
/*     */ 
/*     */     
/* 395 */     String str = "http://xml.org/sax/features/external-general-entities";
/* 396 */     documentBuilderFactory.setFeature(str, false);
/* 397 */     return documentBuilderFactory;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static XMLObject unmarshallElementFromString(String paramString) {
/*     */     try {
/* 409 */       Element element = loadElementFromString(paramString);
/*     */       
/* 411 */       Unmarshaller unmarshaller = Configuration.getUnmarshallerFactory().getUnmarshaller(element);
/* 412 */       if (unmarshaller == null) {
/* 413 */         log.error("Unable to retrieve unmarshaller by DOM Element");
/* 414 */         throw new IllegalArgumentException("No unmarshaller for " + paramString);
/*     */       } 
/*     */       
/* 417 */       return unmarshaller.unmarshall(element);
/* 418 */     } catch (UnmarshallingException unmarshallingException) {
/* 419 */       log.error("Unmarshalling failed when parsing element string " + paramString, (Throwable)unmarshallingException);
/* 420 */       throw new WrappedException(Layer.DATAACCESS, unmarshallingException);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static Element loadElementFromString(String paramString) {
/*     */     try {
/* 433 */       DocumentBuilderFactory documentBuilderFactory = getDocumentBuilderFactory();
/* 434 */       documentBuilderFactory.setNamespaceAware(true);
/*     */       
/* 436 */       DocumentBuilder documentBuilder = documentBuilderFactory.newDocumentBuilder();
/*     */       
/* 438 */       Document document = documentBuilder.parse(new ByteArrayInputStream(paramString.getBytes("UTF-8")));
/* 439 */       return document.getDocumentElement();
/*     */     
/*     */     }
/* 442 */     catch (ParserConfigurationException parserConfigurationException) {
/* 443 */       log.error("Unable to parse element string " + paramString, parserConfigurationException);
/* 444 */       throw new WrappedException(Layer.DATAACCESS, parserConfigurationException);
/* 445 */     } catch (SAXException sAXException) {
/* 446 */       log.error("Ue, nable to parse element string " + paramString, sAXException);
/* 447 */       throw new WrappedException(Layer.DATAACCESS, sAXException);
/* 448 */     } catch (IOException iOException) {
/* 449 */       log.error("Unable to parse element string " + paramString, iOException);
/* 450 */       throw new WrappedException(Layer.DATAACCESS, iOException);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static XMLObject unmarshallElementFromFile(String paramString) {
/* 463 */     File file = new File(paramString);
/* 464 */     if (!file.isFile() || !file.canRead()) {
/* 465 */       log.error("Can't find or read file " + paramString);
/* 466 */       throw new RuntimeException("Cannot find file " + paramString);
/*     */     } 
/*     */     
/*     */     try {
/* 470 */       Element element = loadElementFromFile(paramString);
/*     */ 
/*     */       
/* 473 */       Unmarshaller unmarshaller = Configuration.getUnmarshallerFactory().getUnmarshaller(element);
/* 474 */       if (unmarshaller == null) {
/* 475 */         log.error("Unable to retrieve unmarshaller by DOM Element for {" + element.getNamespaceURI() + "}" + element.getLocalName());
/* 476 */         throw new IllegalArgumentException("No unmarshaller for element {" + element.getNamespaceURI() + "}" + element.getLocalName() + " from file " + paramString);
/*     */       } 
/*     */       
/* 479 */       return unmarshaller.unmarshall(element);
/* 480 */     } catch (UnmarshallingException unmarshallingException) {
/* 481 */       throw new WrappedException(Layer.DATAACCESS, unmarshallingException);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static Element loadElementFromFile(String paramString) {
/*     */     try {
/* 495 */       StringBuffer stringBuffer = new StringBuffer(2048);
/* 496 */       BufferedReader bufferedReader = new BufferedReader(new FileReader(paramString));
/*     */       
/* 498 */       char[] arrayOfChar = new char[1024];
/* 499 */       int i = 0;
/* 500 */       while ((i = bufferedReader.read(arrayOfChar)) != -1) {
/* 501 */         String str = String.valueOf(arrayOfChar, 0, i);
/* 502 */         stringBuffer.append(str);
/* 503 */         arrayOfChar = new char[1024];
/*     */       } 
/* 505 */       bufferedReader.close();
/* 506 */       if (log.isDebugEnabled()) log.debug(stringBuffer.toString()); 
/* 507 */       return loadElementFromString(stringBuffer.toString());
/* 508 */     } catch (IOException iOException) {
/* 509 */       throw new WrappedException(Layer.DATAACCESS, iOException);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getSAMLObjectAsPrettyPrintXML(XMLObject paramXMLObject) {
/* 521 */     if (paramXMLObject == null) {
/* 522 */       throw new IllegalArgumentException("Object cannot be null");
/*     */     }
/* 524 */     Element element = marshallObject(paramXMLObject);
/*     */     
/* 526 */     return XMLHelper.prettyPrintXML(element);
/*     */   }
/*     */   
/*     */   public static Element marshallObject(XMLObject paramXMLObject) {
/* 530 */     if (paramXMLObject.getDOM() == null) {
/* 531 */       Marshaller marshaller = Configuration.getMarshallerFactory().getMarshaller(paramXMLObject);
/* 532 */       if (marshaller == null) {
/* 533 */         throw new IllegalArgumentException("No unmarshaller for " + paramXMLObject);
/*     */       }
/*     */       try {
/* 536 */         return marshaller.marshall(paramXMLObject);
/* 537 */       } catch (MarshallingException marshallingException) {
/* 538 */         throw new WrappedException(Layer.CLIENT, marshallingException);
/*     */       } 
/*     */     } 
/*     */     
/* 542 */     return paramXMLObject.getDOM();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static AssertionConsumerService createAssertionConsumerService(String paramString1, String paramString2, int paramInt, boolean paramBoolean) {
/* 550 */     AssertionConsumerService assertionConsumerService = buildXMLObject(AssertionConsumerService.class);
/* 551 */     assertionConsumerService.setBinding(paramString2);
/* 552 */     assertionConsumerService.setIndex(Integer.valueOf(paramInt));
/* 553 */     assertionConsumerService.setLocation(paramString1);
/* 554 */     assertionConsumerService.setIsDefault(Boolean.valueOf(paramBoolean));
/* 555 */     return assertionConsumerService;
/*     */   }
/*     */   
/*     */   public static SingleLogoutService createSingleLogoutService(String paramString1, String paramString2, String paramString3) {
/* 559 */     SingleLogoutService singleLogoutService = buildXMLObject(SingleLogoutService.class);
/* 560 */     singleLogoutService.setBinding(paramString3);
/* 561 */     singleLogoutService.setLocation(paramString1);
/* 562 */     singleLogoutService.setResponseLocation(paramString2);
/* 563 */     return singleLogoutService;
/*     */   }
/*     */   
/*     */   public static ArtifactResolutionService createArtifactResolutionService(String paramString) {
/* 567 */     ArtifactResolutionService artifactResolutionService = buildXMLObject(ArtifactResolutionService.class);
/* 568 */     artifactResolutionService.setBinding("urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Artifact");
/* 569 */     artifactResolutionService.setIndex(Integer.valueOf(0));
/* 570 */     artifactResolutionService.setIsDefault(Boolean.valueOf(true));
/* 571 */     artifactResolutionService.setLocation(paramString);
/* 572 */     return artifactResolutionService;
/*     */   }
/*     */   
/*     */   public static RequestedAttribute createRequestedAttribute(String paramString1, String paramString2, boolean paramBoolean) {
/* 576 */     RequestedAttribute requestedAttribute = buildXMLObject(RequestedAttribute.class);
/* 577 */     requestedAttribute.setIsRequired(Boolean.valueOf(paramBoolean));
/* 578 */     requestedAttribute.setName(paramString1);
/* 579 */     requestedAttribute.setNameFormat(paramString2);
/*     */     
/* 581 */     return requestedAttribute;
/*     */   }
/*     */   
/*     */   public static NameIDFormat createNameIDFormat(String paramString) {
/* 585 */     NameIDFormat nameIDFormat = buildXMLObject(NameIDFormat.class);
/* 586 */     nameIDFormat.setFormat(paramString);
/* 587 */     return nameIDFormat;
/*     */   }
/*     */   
/*     */   public static AttributeConsumingService createAttributeConsumingService(String paramString) {
/* 591 */     AttributeConsumingService attributeConsumingService = buildXMLObject(AttributeConsumingService.class);
/* 592 */     ServiceName serviceName = buildXMLObject(ServiceName.class);
/* 593 */     serviceName.setName(new LocalizedString(paramString, "en"));
/* 594 */     attributeConsumingService.getNames().add(serviceName);
/*     */     
/* 596 */     attributeConsumingService.setIndex(0);
/* 597 */     attributeConsumingService.setIsDefault(Boolean.valueOf(true));
/*     */     
/* 599 */     return attributeConsumingService;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String[] decodeDiscoveryValue(String paramString) {
/* 609 */     if (paramString == null) {
/* 610 */       return new String[0];
/*     */     }
/* 612 */     String[] arrayOfString = paramString.split(" ");
/* 613 */     for (byte b = 0; b < arrayOfString.length; b++) {
/* 614 */       arrayOfString[b] = new String(Base64.decode(arrayOfString[b]));
/*     */     }
/* 616 */     return arrayOfString;
/*     */   }
/*     */   
/*     */   public static Company createCompany(String paramString) {
/* 620 */     Company company = buildXMLObject(Company.class);
/* 621 */     company.setName(paramString);
/* 622 */     return company;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static <T extends XMLObject> T getFirstElement(ElementExtensibleXMLObject paramElementExtensibleXMLObject, Class<T> paramClass) {
/* 633 */     if (paramElementExtensibleXMLObject == null) return null;
/*     */     
/* 635 */     for (XMLObject xMLObject : paramElementExtensibleXMLObject.getUnknownXMLObjects()) {
/* 636 */       if (paramClass.isInstance(xMLObject)) {
/* 637 */         return (T)xMLObject;
/*     */       }
/*     */     } 
/* 640 */     return null;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static <T extends XMLObject> T clone(T paramT) {
/* 648 */     return (T)unmarshallElementFromString(XMLHelper.nodeToString(marshallObject((XMLObject)paramT)));
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/dk/itst/oiosaml/common/SAMLUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */