/*    */ package dk.itst.oiosaml.error;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public abstract class UnrecoverableException
/*    */   extends RuntimeException
/*    */ {
/*    */   private static final long serialVersionUID = 8184459972073800325L;
/*    */   public static final String VERSION = "$Id: UnrecoverableException.java 2847 2008-05-14 13:37:36Z rolf $";
/*    */   private ErrorCodes.ErrorCode errorCode;
/*    */   private Layer layer;
/*    */   
/*    */   public UnrecoverableException(ErrorCodes.ErrorCode paramErrorCode, Layer paramLayer, String paramString, Throwable paramThrowable) {
/* 45 */     super(paramString, paramThrowable);
/* 46 */     this.errorCode = paramErrorCode;
/* 47 */     this.layer = paramLayer;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public UnrecoverableException(ErrorCodes.ErrorCode paramErrorCode, Layer paramLayer, String paramString) {
/* 54 */     this(paramErrorCode, paramLayer, paramString, null);
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public ErrorCodes.ErrorCode getErrorCode() {
/* 62 */     return this.errorCode;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public Layer getLayer() {
/* 70 */     return this.layer;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/dk/itst/oiosaml/error/UnrecoverableException.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */