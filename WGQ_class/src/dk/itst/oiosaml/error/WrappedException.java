/*    */ package dk.itst.oiosaml.error;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class WrappedException
/*    */   extends UnrecoverableException
/*    */ {
/*    */   public static final String VERSION = "$Id: WrappedException.java 2847 2008-05-14 13:37:36Z rolf $";
/*    */   private static final long serialVersionUID = -4215933609877540662L;
/*    */   
/*    */   public WrappedException(Layer paramLayer, Throwable paramThrowable) {
/* 42 */     super(ErrorCodes.WRAPPED_EXCEPTION, paramLayer, "Wrapped", paramThrowable);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/dk/itst/oiosaml/error/WrappedException.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */