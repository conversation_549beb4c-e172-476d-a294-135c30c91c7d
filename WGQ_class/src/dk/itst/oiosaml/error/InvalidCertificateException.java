/*    */ package dk.itst.oiosaml.error;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class InvalidCertificateException
/*    */   extends RuntimeException
/*    */ {
/*    */   private static final long serialVersionUID = 3702630477555505824L;
/*    */   
/*    */   public InvalidCertificateException(String paramString) {
/* 30 */     super(paramString);
/*    */   }
/*    */   
/*    */   public InvalidCertificateException(String paramString, Exception paramException) {
/* 34 */     super(paramString, paramException);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/dk/itst/oiosaml/error/InvalidCertificateException.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */