/*    */ package dk.itst.oiosaml.error;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class Layer
/*    */ {
/*    */   public static final String VERSION = "$Id: Layer.java 2829 2008-05-13 12:11:31Z jre $";
/* 34 */   public static final Layer CLIENT = new Layer("CLIENT");
/* 35 */   public static final Layer BUSINESS = new Layer("BUSINESS");
/* 36 */   public static final Layer DATAACCESS = new Layer("RESOURCE");
/* 37 */   public static final Layer UNDEFINED = new Layer("UNDEFINED");
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   private String value;
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   private Layer(String paramString) {
/* 48 */     this.value = paramString;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getValue() {
/* 56 */     return this.value;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String toString() {
/* 63 */     return this.value;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public boolean equals(Layer paramLayer) {
/* 70 */     return this.value.equals(paramLayer.value);
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public int hashCode() {
/* 77 */     return getValue().hashCode();
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public boolean equals(Object paramObject) {
/* 84 */     if (!(paramObject instanceof Layer)) {
/* 85 */       return false;
/*    */     }
/*    */     
/* 88 */     return (getValue() != null && getValue().equals(((Layer)paramObject).getValue()));
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/dk/itst/oiosaml/error/Layer.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */