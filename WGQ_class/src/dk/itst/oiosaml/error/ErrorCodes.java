/*    */ package dk.itst.oiosaml.error;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class ErrorCodes
/*    */ {
/*    */   public static final String VERSION = "$Id: ErrorCodes.java 2829 2008-05-13 12:11:31Z jre $";
/* 33 */   public static final ErrorCode RUNTIME_EXCEPTION = new ErrorCode("RUNTIME_EXCEPTION");
/* 34 */   public static final ErrorCode REMOTE_EXCEPTION = new ErrorCode("REMOTE_EXCEPTION");
/* 35 */   public static final ErrorCode WRAPPED_EXCEPTION = new ErrorCode("WRAPPED_EXCEPTION");
/* 36 */   public static final ErrorCode SERIALIZED_EXCEPTION = new ErrorCode("SERIALIZED_EXCEPTION");
/* 37 */   public static final ErrorCode OBJECT_NOT_FOUND = new ErrorCode("OBJECT_NOT_FOUND");
/* 38 */   public static final ErrorCode STARTUP_EXCEPTION = new ErrorCode("STARTUP_EXCEPTION");
/*    */ 
/*    */ 
/*    */   
/*    */   public static class ErrorCode
/*    */   {
/*    */     private String value;
/*    */ 
/*    */     
/*    */     public ErrorCode(String param1String) {
/* 48 */       this.value = param1String;
/*    */     }
/*    */ 
/*    */ 
/*    */ 
/*    */     
/*    */     public String getValue() {
/* 55 */       return this.value;
/*    */     }
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/dk/itst/oiosaml/error/ErrorCodes.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */