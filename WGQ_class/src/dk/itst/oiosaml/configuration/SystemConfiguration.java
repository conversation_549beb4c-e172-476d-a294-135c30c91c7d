/*    */ package dk.itst.oiosaml.configuration;
/*    */ 
/*    */ import javax.naming.Context;
/*    */ import javax.naming.InitialContext;
/*    */ import javax.naming.NamingException;
/*    */ import weaver.integration.cache.SAMLConfigCache;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class SystemConfiguration
/*    */ {
/*    */   public static String getHomeDir() {
/*    */     try {
/* 48 */       Context context = (Context)(new InitialContext()).lookup("java:comp/env");
/* 49 */       return (String)context.lookup("oiosaml-j.home");
/* 50 */     } catch (NamingException namingException) {
/* 51 */       SAMLConfigCache sAMLConfigCache = new SAMLConfigCache();
/* 52 */       if (sAMLConfigCache.next()) {
/* 53 */         return sAMLConfigCache.getCfg_dir();
/*    */       }
/* 55 */       return null;
/*    */     } 
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public static String getApplicationName() {
/*    */     try {
/* 66 */       Context context = (Context)(new InitialContext()).lookup("java:comp/env");
/* 67 */       return (String)context.lookup("oiosaml-j.name");
/* 68 */     } catch (NamingException namingException) {
/* 69 */       return null;
/*    */     } 
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public static String getFullPathToConfigurationFile() {
/*    */     try {
/* 80 */       Context context = (Context)(new InitialContext()).lookup("java:comp/env");
/* 81 */       return (String)context.lookup("oiosaml-j.file");
/* 82 */     } catch (NamingException namingException) {
/* 83 */       return null;
/*    */     } 
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/dk/itst/oiosaml/configuration/SystemConfiguration.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */