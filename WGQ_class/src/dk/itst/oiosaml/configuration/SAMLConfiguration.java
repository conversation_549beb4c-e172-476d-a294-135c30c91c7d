package dk.itst.oiosaml.configuration;

import dk.itst.oiosaml.error.WrappedException;
import java.io.IOException;
import java.security.KeyStore;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.CertificateException;
import java.util.List;
import java.util.Map;
import org.apache.commons.configuration.Configuration;
import org.opensaml.xml.XMLObject;

public interface SAMLConfiguration {
  boolean isConfigured();
  
  Configuration getSystemConfiguration();
  
  KeyStore getKeystore() throws WrappedException, NoSuchAlgorithmException, CertificateException, IllegalStateException, IOException, KeyStoreException;
  
  List<XMLObject> getListOfIdpMetadata();
  
  XMLObject getSPMetaData();
  
  Configuration getCommonConfiguration() throws IOException;
  
  void setConfiguration(Configuration paramConfiguration);
  
  void setInitConfiguration(Map<String, String> paramMap);
}


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/dk/itst/oiosaml/configuration/SAMLConfiguration.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */