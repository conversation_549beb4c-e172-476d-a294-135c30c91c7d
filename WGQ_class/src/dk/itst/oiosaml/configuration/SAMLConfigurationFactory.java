/*    */ package dk.itst.oiosaml.configuration;
/*    */ 
/*    */ import dk.itst.oiosaml.logging.Logger;
/*    */ import dk.itst.oiosaml.logging.LoggerFactory;
/*    */ import java.util.Iterator;
/*    */ import java.util.ServiceLoader;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class SAMLConfigurationFactory
/*    */ {
/* 39 */   private static final Logger log = LoggerFactory.getLogger(SAMLConfigurationFactory.class);
/*    */   private static SAMLConfiguration configuration;
/*    */   
/*    */   public static SAMLConfiguration getConfiguration() {
/* 43 */     if (configuration == null) {
/* 44 */       ServiceLoader<SAMLConfiguration> serviceLoader = ServiceLoader.load(SAMLConfiguration.class);
/* 45 */       for (Iterator<SAMLConfiguration> iterator = serviceLoader.iterator(); iterator.hasNext(); ) {
/* 46 */         configuration = iterator.next();
/* 47 */         if (iterator.hasNext()) {
/* 48 */           log.error("Appears to be more than one configuration implementation. Please check META-INF/services for occurencies. Choosing the implementation: " + configuration.getClass().getName());
/*    */           
/*    */           break;
/*    */         } 
/*    */       } 
/*    */     } 
/* 54 */     return configuration;
/*    */   }
/*    */   
/*    */   public static void reset() {
/* 58 */     configuration = null;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/dk/itst/oiosaml/configuration/SAMLConfigurationFactory.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */