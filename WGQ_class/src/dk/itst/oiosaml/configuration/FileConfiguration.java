/*     */ package dk.itst.oiosaml.configuration;
/*     */ 
/*     */ import dk.itst.oiosaml.common.SAMLUtil;
/*     */ import dk.itst.oiosaml.error.Layer;
/*     */ import dk.itst.oiosaml.error.WrappedException;
/*     */ import dk.itst.oiosaml.helper.DeveloperHelper;
/*     */ import dk.itst.oiosaml.logging.Logger;
/*     */ import dk.itst.oiosaml.logging.LoggerFactory;
/*     */ import java.io.BufferedInputStream;
/*     */ import java.io.File;
/*     */ import java.io.FileInputStream;
/*     */ import java.io.FileNotFoundException;
/*     */ import java.io.FilenameFilter;
/*     */ import java.io.IOException;
/*     */ import java.io.InputStream;
/*     */ import java.net.URL;
/*     */ import java.security.KeyStore;
/*     */ import java.security.KeyStoreException;
/*     */ import java.security.NoSuchAlgorithmException;
/*     */ import java.security.cert.CertificateException;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Enumeration;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import org.apache.commons.configuration.CompositeConfiguration;
/*     */ import org.apache.commons.configuration.Configuration;
/*     */ import org.apache.commons.configuration.ConfigurationException;
/*     */ import org.apache.commons.configuration.PropertiesConfiguration;
/*     */ import org.opensaml.saml2.metadata.EntitiesDescriptor;
/*     */ import org.opensaml.saml2.metadata.EntityDescriptor;
/*     */ import org.opensaml.xml.XMLObject;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class FileConfiguration
/*     */   implements SAMLConfiguration
/*     */ {
/*  74 */   private static final Logger log = LoggerFactory.getLogger(FileConfiguration.class);
/*     */ 
/*     */   
/*     */   private String homeDir;
/*     */   
/*     */   private String configurationFileName;
/*     */   
/*     */   private Configuration systemConfiguration;
/*     */ 
/*     */   
/*     */   public FileConfiguration() {
/*     */     try {
/*  86 */       String str1 = SystemConfiguration.getApplicationName();
/*  87 */       if (str1 != null) {
/*  88 */         log.info("oiosaml-j.name set to " + str1 + " in web.xml");
/*     */       } else {
/*  90 */         log.info("oiosaml-j.name was not defined in web.xml.");
/*     */       } 
/*     */       
/*  93 */       String str2 = SystemConfiguration.getHomeDir();
/*  94 */       if (str2 != null) {
/*  95 */         log.info("oiosaml-j.home set to " + str2 + " in web.xml");
/*     */       } else {
/*  97 */         log.info("oiosaml-j.home was not defined in web.xml.");
/*     */       } 
/*     */       
/* 100 */       String str3 = SystemConfiguration.getFullPathToConfigurationFile();
/* 101 */       if (str3 != null) {
/* 102 */         log.info("oiosaml-j.file set to " + str3 + " in web.xml");
/*     */       } else {
/* 104 */         log.info("oiosaml-j.file was not defined in web.xml.");
/*     */       } 
/* 106 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 107 */       if (str3 != null) {
/* 108 */         hashMap.put("oiosaml-j.file", str3);
/*     */       } else {
/*     */         
/* 111 */         if (str2 == null) {
/* 112 */           str2 = System.getProperty("oiosaml.home");
/* 113 */           log.info("oiosaml-j.home not set in web.xml. Setting it to oiosaml.home Java system property with value: " + str2);
/*     */         } 
/* 115 */         if (str2 == null) {
/* 116 */           str2 = System.getProperty("user.home") + File.separator + ".oiosaml";
/* 117 */           log.info("oiosaml-j.home not set in Java system property. Setting it to default path: " + str2);
/*     */         } 
/*     */         
/* 120 */         hashMap.put("oiosaml-j.home", str2);
/* 121 */         hashMap.put("oiosaml-j.name", str1);
/*     */       } 
/*     */       
/* 124 */       setInitConfiguration((Map)hashMap);
/* 125 */     } catch (Exception exception) {
/* 126 */       exception.printStackTrace();
/* 127 */       log.info("there's something wrong with the class FileConfiguration, plz check the error");
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Configuration getSystemConfiguration() throws IllegalStateException {
/* 140 */     if (this.systemConfiguration != null) {
/* 141 */       return this.systemConfiguration;
/*     */     }
/*     */     
/* 144 */     if (this.homeDir == null || !isConfigured()) {
/* 145 */       throw new IllegalStateException("System not configured");
/*     */     }
/*     */     
/* 148 */     CompositeConfiguration compositeConfiguration = new CompositeConfiguration();
/* 149 */     compositeConfiguration.setProperty("oiosaml.home", this.homeDir);
/*     */     
/*     */     try {
/* 152 */       compositeConfiguration.addConfiguration((Configuration)new PropertiesConfiguration(new File(this.homeDir, this.configurationFileName)));
/* 153 */       compositeConfiguration.addConfiguration(getCommonConfiguration());
/*     */       
/* 155 */       this.systemConfiguration = (Configuration)compositeConfiguration;
/* 156 */       return this.systemConfiguration;
/* 157 */     } catch (ConfigurationException configurationException) {
/* 158 */       log.error("Cannot load the configuration file", (Throwable)configurationException);
/* 159 */       throw new WrappedException(Layer.DATAACCESS, configurationException);
/* 160 */     } catch (IOException iOException) {
/* 161 */       log.error("Unable to load oiosaml-common.propeties from classpath", iOException);
/* 162 */       throw new WrappedException(Layer.DATAACCESS, iOException);
/*     */     } 
/*     */   }
/*     */   
/*     */   public Configuration getCommonConfiguration() throws IOException {
/* 167 */     CompositeConfiguration compositeConfiguration = new CompositeConfiguration();
/* 168 */     Enumeration<URL> enumeration = SAMLConfiguration.class.getClassLoader().getResources("oiosaml-common.properties");
/* 169 */     while (enumeration.hasMoreElements()) {
/* 170 */       URL uRL = enumeration.nextElement();
/* 171 */       log.debug("Loading config from " + uRL);
/*     */       try {
/* 173 */         compositeConfiguration.addConfiguration((Configuration)new PropertiesConfiguration(uRL));
/* 174 */       } catch (ConfigurationException configurationException) {
/* 175 */         log.error("Cannot load the configuration file", (Throwable)configurationException);
/* 176 */         throw new WrappedException(Layer.DATAACCESS, configurationException);
/*     */       } 
/*     */     } 
/*     */     
/* 180 */     return (Configuration)compositeConfiguration;
/*     */   }
/*     */   
/*     */   public boolean isConfigured() {
/* 184 */     if (this.homeDir == null) {
/* 185 */       return false;
/*     */     }
/* 187 */     log.info("Config filename: " + this.homeDir + this.configurationFileName);
/* 188 */     File file = new File(this.homeDir + this.configurationFileName);
/*     */     
/* 190 */     log.info("Looking in : " + file.getAbsolutePath());
/* 191 */     return file.exists();
/*     */   }
/*     */   
/*     */   public KeyStore getKeystore() throws WrappedException {
/* 195 */     KeyStore keyStore = null;
/*     */     
/* 197 */     File file = new File(getSystemConfiguration().getString("oiosaml-sp.certificate.location"));
/*     */ 
/*     */     
/* 200 */     if (!file.exists()) {
/* 201 */       file = new File(this.homeDir + getSystemConfiguration().getString("oiosaml-sp.certificate.location"));
/*     */     }
/*     */     
/*     */     try {
/* 205 */       FileInputStream fileInputStream = new FileInputStream(file);
/* 206 */       BufferedInputStream bufferedInputStream = new BufferedInputStream(fileInputStream);
/* 207 */       bufferedInputStream.mark(1048576);
/*     */       try {
/* 209 */         keyStore = loadStore(bufferedInputStream, getSystemConfiguration().getString("oiosaml-sp.certificate.password"), "PKCS12");
/*     */       }
/* 211 */       catch (IOException iOException) {
/* 212 */         log.debug("Keystore is not of type 'PCKS12' Trying type 'JKS'.");
/*     */         try {
/* 214 */           bufferedInputStream.reset();
/* 215 */           keyStore = loadStore(bufferedInputStream, 
/* 216 */               getSystemConfiguration().getString("oiosaml-sp.certificate.password"), "JKS");
/* 217 */         } catch (IOException iOException1) {
/* 218 */           DeveloperHelper.log("It is not possible to access the configured keystore. Please check that the configured path and password are correct.");
/*     */           
/* 220 */           log.error("Unable to find keystore file. Looking for: " + file.getAbsolutePath());
/* 221 */           throw new WrappedException(Layer.DATAACCESS, iOException1);
/* 222 */         } catch (Exception exception) {
/* 223 */           log.error("Exception occured while processing keystore: " + file.getAbsolutePath());
/* 224 */           throw new WrappedException(Layer.DATAACCESS, exception);
/*     */         } 
/* 226 */       } catch (Exception exception) {
/* 227 */         log.error("Exception occured while processing keystore: " + file.getAbsolutePath());
/* 228 */         throw new WrappedException(Layer.DATAACCESS, exception);
/*     */       }
/*     */     
/* 231 */     } catch (FileNotFoundException fileNotFoundException) {
/* 232 */       log.error("Unable to find keystore file. Looking for: " + file.getAbsolutePath());
/* 233 */       throw new WrappedException(Layer.DATAACCESS, fileNotFoundException);
/*     */     } 
/* 235 */     return keyStore;
/*     */   }
/*     */ 
/*     */   
/*     */   private static KeyStore loadStore(InputStream paramInputStream, String paramString1, String paramString2) throws KeyStoreException, NoSuchAlgorithmException, CertificateException, IOException {
/* 240 */     KeyStore keyStore = KeyStore.getInstance(paramString2);
/* 241 */     char[] arrayOfChar = paramString1.toCharArray();
/* 242 */     keyStore.load(paramInputStream, arrayOfChar);
/* 243 */     paramInputStream.close();
/* 244 */     return keyStore;
/*     */   }
/*     */   
/*     */   public XMLObject getSPMetaData() throws WrappedException {
/* 248 */     String str1 = getSystemConfiguration().getString("common.saml2.metadata.sp.filename");
/* 249 */     String str2 = this.homeDir + getSystemConfiguration().getString("common.saml2.metadata.sp.directory");
/* 250 */     String str3 = str2 + "/" + str1;
/*     */     
/* 252 */     XMLObject xMLObject = null;
/*     */     try {
/* 254 */       xMLObject = SAMLUtil.unmarshallElementFromFile(str3);
/* 255 */     } catch (Exception exception) {
/* 256 */       log.error("Unable to find SP metadata file. Tries to look for: " + str3);
/* 257 */       throw new WrappedException(Layer.DATAACCESS, exception);
/*     */     } 
/* 259 */     return xMLObject;
/*     */   }
/*     */   
/*     */   public List<XMLObject> getListOfIdpMetadata() throws WrappedException {
/* 263 */     ArrayList<XMLObject> arrayList = new ArrayList();
/* 264 */     String str = getSystemConfiguration().getString("oiosaml-sp.protocol");
/* 265 */     if (getSystemConfiguration().getString("common.saml2.metadata.idp.filename") != null) {
/*     */       
/* 267 */       String str1 = this.homeDir + getSystemConfiguration().getString("common.saml2.metadata.idp.directory") + "/" + getSystemConfiguration().getString("common.saml2.metadata.idp.filename");
/* 268 */       File file = new File(str1);
/* 269 */       log.info("Loading " + str + " metadata from " + file);
/*     */       try {
/* 271 */         XMLObject xMLObject = SAMLUtil.unmarshallElementFromFile(file.getAbsolutePath());
/* 272 */         if (xMLObject instanceof EntityDescriptor) {
/* 273 */           arrayList.add(xMLObject);
/* 274 */         } else if (xMLObject instanceof EntitiesDescriptor) {
/* 275 */           EntitiesDescriptor entitiesDescriptor = (EntitiesDescriptor)xMLObject;
/* 276 */           arrayList.addAll(entitiesDescriptor.getEntityDescriptors());
/*     */         } else {
/* 278 */           throw new RuntimeException("Metadata file " + file + " does not contain an EntityDescriptor. Found " + xMLObject
/* 279 */               .getElementQName() + ", expected " + EntityDescriptor.ELEMENT_QNAME);
/*     */         } 
/* 281 */       } catch (RuntimeException runtimeException) {
/* 282 */         log.error("Unable to load metadata from " + file + ". File must contain valid XML and have EntityDescriptor as top tag", runtimeException);
/*     */         
/* 284 */         throw runtimeException;
/*     */       } 
/*     */     } else {
/* 287 */       String str1 = this.homeDir + getSystemConfiguration().getString("common.saml2.metadata.idp.directory");
/* 288 */       File file = new File(str1);
/* 289 */       File[] arrayOfFile = file.listFiles(new FilenameFilter() {
/*     */             public boolean accept(File param1File, String param1String) {
/* 291 */               return param1String.toLowerCase().endsWith(".xml");
/*     */             }
/*     */           });
/* 294 */       if (arrayOfFile != null) {
/* 295 */         for (File file1 : arrayOfFile) {
/* 296 */           log.info("Loading " + str + " metadata from " + file1);
/*     */           try {
/* 298 */             XMLObject xMLObject = SAMLUtil.unmarshallElementFromFile(file1.getAbsolutePath());
/* 299 */             if (xMLObject instanceof EntityDescriptor) {
/* 300 */               arrayList.add(xMLObject);
/* 301 */             } else if (xMLObject instanceof EntitiesDescriptor) {
/* 302 */               EntitiesDescriptor entitiesDescriptor = (EntitiesDescriptor)xMLObject;
/* 303 */               arrayList.addAll(entitiesDescriptor.getEntityDescriptors());
/*     */             } else {
/* 305 */               throw new RuntimeException("Metadata file " + file1 + " does not contain an EntityDescriptor. Found " + xMLObject
/* 306 */                   .getElementQName() + ", expected " + EntityDescriptor.ELEMENT_QNAME);
/*     */             }
/*     */           
/* 309 */           } catch (RuntimeException runtimeException) {
/* 310 */             log.error("Unable to load metadata from " + file1 + ". File must contain valid XML and have EntityDescriptor as top tag", runtimeException);
/*     */             
/* 312 */             throw runtimeException;
/*     */           } 
/*     */         } 
/*     */       }
/*     */     } 
/* 317 */     if (arrayList.isEmpty()) {
/* 318 */       throw new IllegalStateException("No IdP descriptors found in ! At least one file is required.");
/*     */     }
/* 320 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setInitConfiguration(Map<String, String> paramMap) {
/* 330 */     this.systemConfiguration = null;
/* 331 */     if (paramMap != null) {
/* 332 */       if (paramMap.containsKey("oiosaml-j.file")) {
/* 333 */         String str = paramMap.get("oiosaml-j.file");
/* 334 */         if (str != null) {
/* 335 */           int i = str.lastIndexOf(File.separator) + 1;
/* 336 */           this.configurationFileName = str.substring(i, str.length());
/* 337 */           this.homeDir = str.substring(0, i);
/*     */         } 
/* 339 */       } else if (paramMap.containsKey("oiosaml-j.home")) {
/* 340 */         String str1 = paramMap.get("oiosaml-j.home");
/* 341 */         if (str1 != null) {
/* 342 */           this.homeDir = str1;
/* 343 */           this.configurationFileName = "oiosaml-sp.properties";
/*     */         } 
/*     */ 
/*     */         
/* 347 */         String str2 = paramMap.get("oiosaml-j.name");
/* 348 */         if (str2 != null && !str2.trim().isEmpty()) {
/*     */           
/* 350 */           if (this.homeDir.endsWith(File.separator))
/* 351 */             this.homeDir = this.homeDir.substring(0, this.homeDir.length() - 1); 
/* 352 */           this.homeDir += "-" + str2;
/*     */         } 
/*     */ 
/*     */         
/* 356 */         if (!this.homeDir.endsWith(File.separator)) {
/* 357 */           this.homeDir += File.separator;
/*     */         }
/*     */       } else {
/* 360 */         this.homeDir = null;
/* 361 */         this.configurationFileName = null;
/*     */       } 
/*     */ 
/*     */       
/* 365 */       log.info("Path to configuration folder set to: " + this.homeDir);
/* 366 */       log.info("Configuration file name set to: " + this.configurationFileName);
/*     */     } 
/*     */   }
/*     */   
/*     */   public void setConfiguration(Configuration paramConfiguration) {
/* 371 */     this.systemConfiguration = paramConfiguration;
/*     */   }
/*     */   
/*     */   public String getHomeDir() {
/* 375 */     return this.homeDir;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/dk/itst/oiosaml/configuration/FileConfiguration.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */