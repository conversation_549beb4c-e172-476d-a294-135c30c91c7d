/*     */ package dk.itst.oiosaml.logging;
/*     */ 
/*     */ import dk.itst.oiosaml.configuration.SAMLConfigurationFactory;
/*     */ import dk.itst.oiosaml.error.Layer;
/*     */ import dk.itst.oiosaml.error.WrappedException;
/*     */ import java.io.BufferedReader;
/*     */ import java.io.ByteArrayInputStream;
/*     */ import java.io.File;
/*     */ import java.io.FileNotFoundException;
/*     */ import java.io.FileReader;
/*     */ import java.io.IOException;
/*     */ import org.apache.commons.configuration.Configuration;
/*     */ import org.apache.log4j.LogManager;
/*     */ import org.apache.log4j.Logger;
/*     */ import org.apache.log4j.xml.DOMConfigurator;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class Log4JLogger
/*     */   implements Logger
/*     */ {
/*     */   private static boolean initialized = false;
/*     */   private static boolean initializationOngoing = false;
/*  41 */   private static Object lock = new Object();
/*     */   
/*     */   private Logger log;
/*     */   
/*     */   public boolean isDebugEnabled() {
/*  46 */     return this.log.isDebugEnabled();
/*     */   }
/*     */   
/*     */   public void debug(Object paramObject) {
/*  50 */     this.log.debug(paramObject);
/*     */   }
/*     */   
/*     */   public void debug(Object paramObject, Throwable paramThrowable) {
/*  54 */     this.log.debug(paramObject, paramThrowable);
/*     */   }
/*     */ 
/*     */   
/*     */   public boolean isInfoEnabled() {
/*  59 */     return this.log.isInfoEnabled();
/*     */   }
/*     */ 
/*     */   
/*     */   public void info(Object paramObject) {
/*  64 */     this.log.info(paramObject);
/*     */   }
/*     */ 
/*     */   
/*     */   public void info(Object paramObject, Throwable paramThrowable) {
/*  69 */     this.log.info(paramObject, paramThrowable);
/*     */   }
/*     */ 
/*     */   
/*     */   public void warn(Object paramObject) {
/*  74 */     this.log.warn(paramObject);
/*     */   }
/*     */ 
/*     */   
/*     */   public void warn(Object paramObject, Throwable paramThrowable) {
/*  79 */     this.log.warn(paramObject, paramThrowable);
/*     */   }
/*     */ 
/*     */   
/*     */   public void error(Object paramObject) {
/*  84 */     this.log.error(paramObject);
/*     */   }
/*     */ 
/*     */   
/*     */   public void error(Object paramObject, Throwable paramThrowable) {
/*  89 */     this.log.error(paramObject, paramThrowable);
/*     */   }
/*     */   
/*     */   public void init(String paramString) {
/*  93 */     this.log = Logger.getLogger(paramString);
/*     */     
/*  95 */     synchronized (lock) {
/*  96 */       if (!initialized && !initializationOngoing) {
/*     */         Configuration configuration; String str3;
/*  98 */         initializationOngoing = true;
/*     */ 
/*     */         
/*     */         try {
/* 102 */           configuration = SAMLConfigurationFactory.getConfiguration().getSystemConfiguration();
/*     */         
/*     */         }
/* 105 */         catch (IllegalStateException illegalStateException) {
/* 106 */           error("Unable to retrieve configuration", illegalStateException);
/* 107 */           initializationOngoing = false;
/* 108 */           initialized = true;
/*     */           
/*     */           return;
/*     */         } 
/* 112 */         String str1 = configuration.getString("oiosaml.home");
/* 113 */         String str2 = str1 + configuration.getString("oiosaml-sp.log");
/*     */ 
/*     */         
/* 116 */         StringBuilder stringBuilder = new StringBuilder();
/*     */         
/*     */         try {
/* 119 */           BufferedReader bufferedReader = new BufferedReader(new FileReader(str2));
/*     */           
/*     */           int i;
/* 122 */           while ((i = bufferedReader.read()) != -1) {
/* 123 */             stringBuilder.append((char)i);
/*     */           }
/*     */           
/* 126 */           bufferedReader.close();
/*     */           
/* 128 */           if (str1.endsWith(File.separator)) {
/* 129 */             str1 = str1.substring(0, str1.length() - 1);
/*     */           }
/* 131 */           str3 = stringBuilder.toString().replaceAll("\\$\\{oiosaml.home\\}", str1.replace("\\", "/"));
/*     */         }
/* 133 */         catch (FileNotFoundException fileNotFoundException) {
/* 134 */           this.log.error("Unable to find log file. Tries to look for: " + str2);
/* 135 */           throw new WrappedException(Layer.DATAACCESS, fileNotFoundException);
/* 136 */         } catch (IOException iOException) {
/* 137 */           this.log.error("Unable to process log file.");
/* 138 */           throw new WrappedException(Layer.DATAACCESS, iOException);
/*     */         } 
/*     */         
/* 141 */         ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(str3.getBytes());
/*     */         
/*     */         try {
/* 144 */           (new DOMConfigurator()).doConfigure(byteArrayInputStream, LogManager.getLoggerRepository());
/*     */         } finally {
/* 146 */           if (byteArrayInputStream != null) {
/*     */             try {
/* 148 */               byteArrayInputStream.close();
/* 149 */             } catch (IOException iOException) {
/* 150 */               throw new WrappedException(Layer.UNDEFINED, iOException);
/*     */             } 
/*     */           }
/*     */         } 
/* 154 */         initializationOngoing = false;
/* 155 */         initialized = true;
/*     */       } 
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/dk/itst/oiosaml/logging/Log4JLogger.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */