/*    */ package dk.itst.oiosaml.logging;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public enum Operation
/*    */ {
/* 28 */   AUTHNREQUEST_SEND,
/* 29 */   AUTHNREQUEST_REDIRECT_ARTIFACT,
/* 30 */   AUTHNREQUEST_REDIRECT,
/* 31 */   AUTHNREQUEST_POST,
/*    */   
/* 33 */   ATTRIBUTEQUERY, LOGOUTREQUEST, LOGOUT, LOGOUTRESPONSE, ARTIFACTRESOLVE, LOGIN, LOGOUT_SOAP, ACCESS, DISCOVER, TIMEOUT, CRLCHECK, LOGIN_SESSION, OCSPCHECK;
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/dk/itst/oiosaml/logging/Operation.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */