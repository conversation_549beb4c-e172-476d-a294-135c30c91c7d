/*     */ package dk.itst.oiosaml.logging;
/*     */ 
/*     */ import dk.itst.oiosaml.sp.UserAssertion;
/*     */ import java.text.MessageFormat;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class Audit
/*     */ {
/*  35 */   private static Logger log = LoggerFactory.getLogger("OIOSAML_AUDIT_LOGGER");
/*  36 */   private static final ThreadLocal<MessageFormat> format = new ThreadLocal<MessageFormat>()
/*     */     {
/*     */       protected MessageFormat initialValue()
/*     */       {
/*  40 */         return new MessageFormat("{0} {1,choice,-1#---|0#-->|1#'<'--} {2} {3} ''{4}'' ''{5}'' ''{6}''");
/*     */       }
/*     */     };
/*  43 */   private static final ThreadLocal<String> remoteAddress = new ThreadLocal<>();
/*  44 */   private static final ThreadLocal<String> session = new ThreadLocal<>();
/*  45 */   private static final ThreadLocal<String> assertionId = new ThreadLocal<String>() {
/*     */       protected String initialValue() {
/*  47 */         return "";
/*     */       }
/*     */     };
/*     */   
/*     */   public static void log(Operation paramOperation, String paramString) {
/*  52 */     logEntry(paramOperation.name(), null, "", paramString);
/*     */   }
/*     */   
/*     */   public static void log(Operation paramOperation, boolean paramBoolean, String paramString1, String paramString2) {
/*  56 */     if (paramString1 != null && !"".equals(paramString1)) {
/*  57 */       logEntry(paramOperation.name(), Boolean.valueOf(paramBoolean), paramString2, "RequestID " + paramString1);
/*     */     } else {
/*  59 */       logEntry(paramOperation.name(), Boolean.valueOf(paramBoolean), paramString2, "");
/*     */     } 
/*     */   }
/*     */   
/*     */   private static void logEntry(String paramString1, Boolean paramBoolean, String paramString2, String paramString3) {
/*  64 */     int i = getDirection(paramBoolean);
/*  65 */     if (paramString3 == null)
/*  66 */       paramString3 = ""; 
/*  67 */     String str = ((MessageFormat)format.get()).format(new Object[] { paramString1, Integer.valueOf(i), remoteAddress.get(), session.get(), assertionId.get(), paramString2, paramString3.replace('\n', ' ') });
/*  68 */     log.info(str);
/*     */   }
/*     */   
/*     */   public static void logSystem(String paramString1, String paramString2, Operation paramOperation, String paramString3) {
/*  72 */     int i = getDirection(null);
/*  73 */     if (paramString3 == null) {
/*  74 */       paramString3 = "";
/*     */     }
/*  76 */     String str = ((MessageFormat)format.get()).format(new Object[] { paramOperation, Integer.valueOf(i), "127.0.0.1", paramString1, paramString2, "", paramString3.replace('\n', ' ') });
/*  77 */     log.info(str);
/*     */   }
/*     */   
/*     */   public static void logError(Operation paramOperation, boolean paramBoolean, String paramString, Throwable paramThrowable) {
/*  81 */     logError(paramOperation.name(), Boolean.valueOf(paramBoolean), paramThrowable.getMessage(), paramThrowable);
/*     */   }
/*     */   
/*     */   public static void logError(String paramString, boolean paramBoolean, Exception paramException) {
/*  85 */     logError("Dispatch:" + paramString, Boolean.valueOf(paramBoolean), paramException.getMessage(), paramException);
/*     */   }
/*     */   
/*     */   public static void logError(Operation paramOperation, boolean paramBoolean, String paramString1, String paramString2) {
/*  89 */     logError(paramOperation.name(), Boolean.valueOf(paramBoolean), paramString2, (Throwable)null);
/*     */   }
/*     */   
/*     */   private static void logError(String paramString1, Boolean paramBoolean, String paramString2, Throwable paramThrowable) {
/*  93 */     String str = ((MessageFormat)format.get()).format(new Object[] { paramString1, Integer.valueOf(getDirection(paramBoolean)), remoteAddress.get(), session.get(), assertionId.get(), "", paramString2 });
/*  94 */     log.error(str, paramThrowable);
/*     */   }
/*     */   
/*     */   private static int getDirection(Boolean paramBoolean) {
/*     */     boolean bool;
/*  99 */     if (paramBoolean == null) {
/* 100 */       bool = true;
/* 101 */     } else if (paramBoolean.booleanValue()) {
/* 102 */       bool = false;
/*     */     } else {
/* 104 */       bool = true;
/*     */     } 
/* 106 */     return bool;
/*     */   }
/*     */   
/*     */   public static void init(HttpServletRequest paramHttpServletRequest) {
/* 110 */     log.info("Session created at: " + paramHttpServletRequest.getSession().getCreationTime() + ", timeout after " + paramHttpServletRequest.getSession().getMaxInactiveInterval() + " seconds");
/* 111 */     remoteAddress.set(paramHttpServletRequest.getRemoteAddr());
/* 112 */     session.set(paramHttpServletRequest.getSession().getId());
/* 113 */     UserAssertion userAssertion = (UserAssertion)paramHttpServletRequest.getSession().getAttribute("dk.itst.oiosaml.userassertion");
/* 114 */     if (userAssertion != null) {
/* 115 */       assertionId.set(userAssertion.getAssertionId());
/*     */     } else {
/* 117 */       assertionId.set("");
/*     */     } 
/*     */   }
/*     */   
/*     */   public static void setAssertionId(String paramString) {
/* 122 */     assertionId.set(paramString);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/dk/itst/oiosaml/logging/Audit.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */