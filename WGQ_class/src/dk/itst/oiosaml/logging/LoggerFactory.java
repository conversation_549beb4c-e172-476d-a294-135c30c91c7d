/*    */ package dk.itst.oiosaml.logging;
/*    */ 
/*    */ import java.util.Iterator;
/*    */ import java.util.ServiceLoader;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class LoggerFactory
/*    */ {
/* 28 */   private static final Logger log = getLogger(LoggerFactory.class);
/*    */   
/*    */   public static Logger getLogger(String paramString) {
/* 31 */     Logger logger = null;
/* 32 */     ServiceLoader<Logger> serviceLoader = ServiceLoader.load(Logger.class);
/* 33 */     for (Iterator<Logger> iterator = serviceLoader.iterator(); iterator.hasNext(); ) {
/* 34 */       logger = iterator.next();
/* 35 */       if (iterator.hasNext()) {
/* 36 */         log.error("Appears to be more than one logger implementation. Please check META-INF/services for occurencies. Choosing the implementation: " + logger.getClass().getName());
/*    */         break;
/*    */       } 
/*    */     } 
/* 40 */     logger.init(paramString);
/* 41 */     return logger;
/*    */   }
/*    */   
/*    */   public static Logger getLogger(Class<?> paramClass) {
/* 45 */     return getLogger(paramClass.getCanonicalName());
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/dk/itst/oiosaml/logging/LoggerFactory.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */